package com.holderzone.saas.store.weixin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import com.google.common.util.concurrent.MoreExecutors;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.holder.saas.weixin.entry.dto.WxMemberSessionDTO;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.saas.store.dto.WxStoreMerchantDineInItemDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.order.OrderWechatDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.common.SingleListDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CancelOrderReqDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.dto.organization.BrandStoreDetailDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.store.table.TableDTO;
import com.holderzone.saas.store.dto.weixin.*;
import com.holderzone.saas.store.dto.weixin.deal.PayBackOrderRecordDTO;
import com.holderzone.saas.store.dto.weixin.deal.UnMemberMessageDTO;
import com.holderzone.saas.store.dto.weixin.deal.WxStoreUserOrderItemDTO;
import com.holderzone.saas.store.dto.weixin.req.WxBrandUserOrderReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.*;
import com.holderzone.saas.store.weixin.config.WeChatConfig;
import com.holderzone.saas.store.weixin.entity.domain.WxOrderRecordDO;
import com.holderzone.saas.store.weixin.entity.domain.WxQrCodeInfoDO;
import com.holderzone.saas.store.weixin.entity.domain.WxStoreMerchantOrderDO;
import com.holderzone.saas.store.weixin.entity.domain.WxUserRecordDO;
import com.holderzone.saas.store.weixin.entity.dto.WxMemberConsumeMsgDTO;
import com.holderzone.saas.store.weixin.entity.enums.WxTemplateTypeEnum;
import com.holderzone.saas.store.weixin.entity.query.WxOrderRecordQuery;
import com.holderzone.saas.store.weixin.entity.query.WxStorePendingOrdersQuery;
import com.holderzone.saas.store.weixin.mapper.WxOrderRecordMapper;
import com.holderzone.saas.store.weixin.mapper.WxStoreMerchantOrderMapper;
import com.holderzone.saas.store.weixin.mapstruct.WxStoreMerchantOrderMapstruct;
import com.holderzone.saas.store.weixin.mapstruct.WxStoreUserOrderMapper;
import com.holderzone.saas.store.weixin.service.*;
import com.holderzone.saas.store.weixin.service.OrderItemService;
import com.holderzone.saas.store.weixin.service.rpc.WxStoreDineInOrderClientService;
import com.holderzone.saas.store.weixin.service.rpc.WxStoreTableClientService;
import com.holderzone.saas.store.weixin.service.rpc.WxStoreWeChatOrderClientService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class WxOrderRecordServiceImplTest {

    @Mock
    private RedisUtils mockRedisUtils;
    @Mock
    private WxUserRecordService mockWxUserRecordService;
    @Mock
    private WxOrderRecordMapper mockWxOrderRecordMapper;
    @Mock
    private WxStoreWeChatOrderClientService mockWxStoreWeChatOrderClientService;
    @Mock
    private WxStoreUserOrderMapper mockWxStoreUserOrderMapper;
    @Mock
    private WxStoreMerchantOrderService mockWxStoreMerchantOrderService;
    @Mock
    private WxStoreMerchantDineInItemService mockWxStoreMerchantDineInItemService;
    @Mock
    private WxStoreTableClientService mockWxStoreTableClientService;
    @Mock
    private WxStoreSessionDetailsService mockWxStoreSessionDetailsService;
    @Mock
    private WxStoreDineInOrderClientService mockWxStoreDineInOrderClientService;
    @Mock
    private OrderItemService mockOrderItemService;
    @Mock
    private WxOpenMessageService mockWxOpenMessageService;
    @Mock
    private WxStoreMerchantOrderMapper mockWxStoreMerchantOrderMapper;
    @Mock
    private WxStoreMerchantOrderMapstruct mockWxStoreMerchantOrderMapstruct;
    @Mock
    private WeChatConfig mockWeChatConfig;

    @InjectMocks
    private WxOrderRecordServiceImpl wxOrderRecordServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        ReflectionTestUtils.setField(wxOrderRecordServiceImplUnderTest, "orderRecordExecutor",
                MoreExecutors.directExecutor());
    }

    @Test
    public void testUpdate() {
        // Setup
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("orderGuid");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setOrderNo("orderNo");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setStateName("orderStateName");
        dineinOrderDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setItemName("itemName");
        dineInItemDTO.setParentItemGuid(0L);
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        final WxStoreAccountDTO wxStoreAccountDTO = new WxStoreAccountDTO(dineinOrderDetailRespDTO,
                WxStoreAdvanceConsumerReqDTO.builder()
                        .tradeOrderGuid("tradeOrderGuid")
                        .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                                .openId("openId")
                                .nickName("nickName")
                                .headImgUrl("headImgUrl")
                                .enterpriseGuid("enterpriseGuid")
                                .enterpriseName("enterpriseName")
                                .storeGuid("storeGuid")
                                .storeName("name")
                                .diningTableGuid("diningTableGuid")
                                .tableCode("code")
                                .areaGuid("areaGuid")
                                .areaName("areaName")
                                .brandName("brandName")
                                .brandGuid("brandGuid")
                                .brandLogo("brandLogo")
                                .build())
                        .build());

        // Configure WxUserRecordService.getWxuserRecord(...).
        final WxUserRecordDO wxUserRecordDO = new WxUserRecordDO(0L, "guid", "openId", "nickName", "headImgUrl", 0,
                "country", "province", "city", "phone", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid", 0);
        when(mockWxUserRecordService.getWxuserRecord(WxStoreConsumerDTO.builder()
                .openId("openId")
                .nickName("nickName")
                .headImgUrl("headImgUrl")
                .enterpriseGuid("enterpriseGuid")
                .enterpriseName("enterpriseName")
                .storeGuid("storeGuid")
                .storeName("name")
                .diningTableGuid("diningTableGuid")
                .tableCode("code")
                .areaGuid("areaGuid")
                .areaName("areaName")
                .brandName("brandName")
                .brandGuid("brandGuid")
                .brandLogo("brandLogo")
                .build())).thenReturn(wxUserRecordDO);

        when(mockRedisUtils.generatdDTOGuid(WxOrderRecordDO.class)).thenReturn("e19e50b7-9e82-41f9-b8da-7274e1a1cc20");

        // Run the test
        final boolean result = wxOrderRecordServiceImplUnderTest.update(wxStoreAccountDTO);

        // Verify the results
        assertFalse(result);
        verify(mockWxUserRecordService).saveOrUpdateUserInfo(WxStoreConsumerDTO.builder()
                .openId("openId")
                .nickName("nickName")
                .headImgUrl("headImgUrl")
                .enterpriseGuid("enterpriseGuid")
                .enterpriseName("enterpriseName")
                .storeGuid("storeGuid")
                .storeName("name")
                .diningTableGuid("diningTableGuid")
                .tableCode("code")
                .areaGuid("areaGuid")
                .areaName("areaName")
                .brandName("brandName")
                .brandGuid("brandGuid")
                .brandLogo("brandLogo")
                .build());
    }

    @Test
    public void testUpdateTable() {
        wxOrderRecordServiceImplUnderTest.updateTable(WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("tradeOrderGuid")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("name")
                        .diningTableGuid("diningTableGuid")
                        .tableCode("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .brandLogo("brandLogo")
                        .build())
                .build());
    }

    @Test
    public void testGetWxOrderRecordByCondition() {
        // Setup
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("tradeOrderGuid")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("name")
                        .diningTableGuid("diningTableGuid")
                        .tableCode("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .brandLogo("brandLogo")
                        .build())
                .build();
        final List<WxOrderRecordDO> expectedResult = Arrays.asList(
                new WxOrderRecordDO(0L, "e19e50b7-9e82-41f9-b8da-7274e1a1cc20", "orderGuid", "merchantGuid", "guid",
                        "brandGuid", "brandName", "brandLogo", "storeGuid", "storeName", "areaGuid", "areaName",
                        "diningTableGuid", "diningTableCode", 0, 0, "orderStateName", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName", "orderHolderNo",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0));

        // Configure WxUserRecordService.getWxuserRecord(...).
        final WxUserRecordDO wxUserRecordDO = new WxUserRecordDO(0L, "guid", "openId", "nickName", "headImgUrl", 0,
                "country", "province", "city", "phone", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid", 0);
        when(mockWxUserRecordService.getWxuserRecord(WxStoreConsumerDTO.builder()
                .openId("openId")
                .nickName("nickName")
                .headImgUrl("headImgUrl")
                .enterpriseGuid("enterpriseGuid")
                .enterpriseName("enterpriseName")
                .storeGuid("storeGuid")
                .storeName("name")
                .diningTableGuid("diningTableGuid")
                .tableCode("code")
                .areaGuid("areaGuid")
                .areaName("areaName")
                .brandName("brandName")
                .brandGuid("brandGuid")
                .brandLogo("brandLogo")
                .build())).thenReturn(wxUserRecordDO);

        // Configure WxOrderRecordMapper.selectList(...).
        final List<WxOrderRecordDO> wxOrderRecordDOS = Arrays.asList(
                new WxOrderRecordDO(0L, "e19e50b7-9e82-41f9-b8da-7274e1a1cc20", "orderGuid", "merchantGuid", "guid",
                        "brandGuid", "brandName", "brandLogo", "storeGuid", "storeName", "areaGuid", "areaName",
                        "diningTableGuid", "diningTableCode", 0, 0, "orderStateName", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName", "orderHolderNo",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0));
        when(mockWxOrderRecordMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(wxOrderRecordDOS);

        // Run the test
        final List<WxOrderRecordDO> result = wxOrderRecordServiceImplUnderTest.getWxOrderRecordByCondition(
                wxStoreAdvanceConsumerReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetWxOrderRecordByCondition_WxOrderRecordMapperReturnsNoItems() {
        // Setup
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("tradeOrderGuid")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("name")
                        .diningTableGuid("diningTableGuid")
                        .tableCode("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .brandLogo("brandLogo")
                        .build())
                .build();

        // Configure WxUserRecordService.getWxuserRecord(...).
        final WxUserRecordDO wxUserRecordDO = new WxUserRecordDO(0L, "guid", "openId", "nickName", "headImgUrl", 0,
                "country", "province", "city", "phone", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid", 0);
        when(mockWxUserRecordService.getWxuserRecord(WxStoreConsumerDTO.builder()
                .openId("openId")
                .nickName("nickName")
                .headImgUrl("headImgUrl")
                .enterpriseGuid("enterpriseGuid")
                .enterpriseName("enterpriseName")
                .storeGuid("storeGuid")
                .storeName("name")
                .diningTableGuid("diningTableGuid")
                .tableCode("code")
                .areaGuid("areaGuid")
                .areaName("areaName")
                .brandName("brandName")
                .brandGuid("brandGuid")
                .brandLogo("brandLogo")
                .build())).thenReturn(wxUserRecordDO);

        when(mockWxOrderRecordMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<WxOrderRecordDO> result = wxOrderRecordServiceImplUnderTest.getWxOrderRecordByCondition(
                wxStoreAdvanceConsumerReqDTO);

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testDelWxOrderRecord() {
        // Setup
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("tradeOrderGuid")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("name")
                        .diningTableGuid("diningTableGuid")
                        .tableCode("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .brandLogo("brandLogo")
                        .build())
                .build();

        // Configure WxUserRecordService.getWxuserRecord(...).
        final WxUserRecordDO wxUserRecordDO = new WxUserRecordDO(0L, "guid", "openId", "nickName", "headImgUrl", 0,
                "country", "province", "city", "phone", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid", 0);
        when(mockWxUserRecordService.getWxuserRecord(WxStoreConsumerDTO.builder()
                .openId("openId")
                .nickName("nickName")
                .headImgUrl("headImgUrl")
                .enterpriseGuid("enterpriseGuid")
                .enterpriseName("enterpriseName")
                .storeGuid("storeGuid")
                .storeName("name")
                .diningTableGuid("diningTableGuid")
                .tableCode("code")
                .areaGuid("areaGuid")
                .areaName("areaName")
                .brandName("brandName")
                .brandGuid("brandGuid")
                .brandLogo("brandLogo")
                .build())).thenReturn(wxUserRecordDO);

        // Run the test
        wxOrderRecordServiceImplUnderTest.delWxOrderRecord(wxStoreAdvanceConsumerReqDTO);

        // Verify the results
    }

    @Test
    public void testGetOrderRecord() {
        // Setup
        final WxOrderRecordDTO expectedResult = WxOrderRecordDTO.builder().build();

        // Run the test
        final WxOrderRecordDTO result = wxOrderRecordServiceImplUnderTest.getOrderRecord("orderGuid");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetWxStoreUserOrder() {
        // Setup
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("tradeOrderGuid")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("name")
                        .diningTableGuid("diningTableGuid")
                        .tableCode("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .brandLogo("brandLogo")
                        .build())
                .build();
        final WxStoreUserOrderDTO expectedResult = new WxStoreUserOrderDTO();
        final WxStoreDineinOrderDetailsRespDTO wxStoreDineinOrderDetailsRespDTO = new WxStoreDineinOrderDetailsRespDTO();
        wxStoreDineinOrderDetailsRespDTO.setLogUrl("brandLogo");
        wxStoreDineinOrderDetailsRespDTO.setStoreName("name");
        wxStoreDineinOrderDetailsRespDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreDineinOrderDetailsRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setOrders(Arrays.asList(wxStoreDineinOrderDetailsRespDTO));

        // Configure WxUserRecordService.getWxuserRecord(...).
        final WxUserRecordDO wxUserRecordDO = new WxUserRecordDO(0L, "guid", "openId", "nickName", "headImgUrl", 0,
                "country", "province", "city", "phone", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid", 0);
        when(mockWxUserRecordService.getWxuserRecord(WxStoreConsumerDTO.builder()
                .openId("openId")
                .nickName("nickName")
                .headImgUrl("headImgUrl")
                .enterpriseGuid("enterpriseGuid")
                .enterpriseName("enterpriseName")
                .storeGuid("storeGuid")
                .storeName("name")
                .diningTableGuid("diningTableGuid")
                .tableCode("code")
                .areaGuid("areaGuid")
                .areaName("areaName")
                .brandName("brandName")
                .brandGuid("brandGuid")
                .brandLogo("brandLogo")
                .build())).thenReturn(wxUserRecordDO);

        // Configure WxStoreWeChatOrderClientService.getOrderDetails(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("orderGuid");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setOrderNo("orderNo");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setStateName("orderStateName");
        dineinOrderDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setItemName("itemName");
        dineInItemDTO.setParentItemGuid(0L);
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        final List<DineinOrderDetailRespDTO> dineinOrderDetailRespDTOS = Arrays.asList(dineinOrderDetailRespDTO);
        final SingleListDTO singleListDTO = new SingleListDTO();
        singleListDTO.setDeviceType(0);
        singleListDTO.setDeviceId("openId");
        singleListDTO.setEnterpriseGuid("enterpriseGuid");
        singleListDTO.setStoreGuid("storeGuid");
        singleListDTO.setUserGuid("openId");
        singleListDTO.setUserName("nickName");
        singleListDTO.setList(Arrays.asList("value"));
        when(mockWxStoreWeChatOrderClientService.getOrderDetails(singleListDTO)).thenReturn(dineinOrderDetailRespDTOS);

        // Configure WxStoreDineInOrderClientService.cancelOrder(...).
        final CancelOrderReqDTO cancelOrderReqDTO = new CancelOrderReqDTO();
        cancelOrderReqDTO.setDeviceType(0);
        cancelOrderReqDTO.setDeviceId("openId");
        cancelOrderReqDTO.setEnterpriseGuid("enterpriseGuid");
        cancelOrderReqDTO.setStoreGuid("storeGuid");
        cancelOrderReqDTO.setUserGuid("openId");
        cancelOrderReqDTO.setUserName("nickName");
        cancelOrderReqDTO.setOrderGuid("orderGuid");
        cancelOrderReqDTO.setReason("过期做废");
        cancelOrderReqDTO.setTable(false);
        cancelOrderReqDTO.setFastFood(false);
        when(mockWxStoreDineInOrderClientService.cancelOrder(cancelOrderReqDTO)).thenReturn(false);

        when(mockWxStoreTableClientService.getOrderGuid("diningTableGuid")).thenReturn("data");

        // Configure WxStoreDineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO1 = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO1.setGuid("orderGuid");
        dineinOrderDetailRespDTO1.setTradeMode(0);
        dineinOrderDetailRespDTO1.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setOrderNo("orderNo");
        dineinOrderDetailRespDTO1.setState(0);
        dineinOrderDetailRespDTO1.setStateName("orderStateName");
        dineinOrderDetailRespDTO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO1.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setItemName("itemName");
        dineInItemDTO1.setParentItemGuid(0L);
        dineinOrderDetailRespDTO1.setDineInItemDTOS(Arrays.asList(dineInItemDTO1));
        when(mockWxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder()
                .data("data")
                .datas(Arrays.asList("value"))
                .build())).thenReturn(dineinOrderDetailRespDTO1);

        when(mockWxStoreSessionDetailsService.getMerchantBatchGuid("diningTableGuid")).thenReturn("orderGuid");

        // Configure WxStoreMerchantOrderService.getPendingOrders(...).
        final WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO.setGuid("guid");
        wxStoreMerchantOrderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setTotalPrice(new BigDecimal("0.00"));
        wxStoreMerchantOrderDTO.setOpenId("openId");
        final List<WxStoreMerchantOrderDTO> wxStoreMerchantOrderDTOS = Arrays.asList(wxStoreMerchantOrderDTO);
        when(mockWxStoreMerchantOrderService.getPendingOrders(WxStorePendingOrdersQuery.builder()
                .storeGuid("storeGuid")
                .diningTableGuid("diningTableGuid")
                .orderStates(Arrays.asList(0))
                .tradeMode(0)
                .orderGuid("orderGuid")
                .build())).thenReturn(wxStoreMerchantOrderDTOS);

        // Configure WxStoreMerchantDineInItemService.getWxStoreMerchantDineInItem(...).
        final WxStoreMerchantDineInItemDTO wxStoreMerchantDineInItemDTO = new WxStoreMerchantDineInItemDTO();
        wxStoreMerchantDineInItemDTO.setGuid("dc1f512f-7921-4cc8-b56d-501245d8bca2");
        final DineInItemDTO dineInItemDTO2 = new DineInItemDTO();
        dineInItemDTO2.setGuid("ae455df7-daeb-4c0c-9243-22ed0a9492a1");
        dineInItemDTO2.setItemName("itemName");
        dineInItemDTO2.setParentItemGuid(0L);
        wxStoreMerchantDineInItemDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO2));
        when(mockWxStoreMerchantDineInItemService.getWxStoreMerchantDineInItem("guid"))
                .thenReturn(wxStoreMerchantDineInItemDTO);

        // Configure WxStoreUserOrderMapper.getWxStoreUserOrder(...).
        final WxStoreUserOrderDTO wxStoreUserOrderDTO = new WxStoreUserOrderDTO();
        final WxStoreDineinOrderDetailsRespDTO wxStoreDineinOrderDetailsRespDTO1 = new WxStoreDineinOrderDetailsRespDTO();
        wxStoreDineinOrderDetailsRespDTO1.setLogUrl("brandLogo");
        wxStoreDineinOrderDetailsRespDTO1.setStoreName("name");
        wxStoreDineinOrderDetailsRespDTO1.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreDineinOrderDetailsRespDTO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreUserOrderDTO.setOrders(Arrays.asList(wxStoreDineinOrderDetailsRespDTO1));
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO2 = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO2.setGuid("orderGuid");
        dineinOrderDetailRespDTO2.setTradeMode(0);
        dineinOrderDetailRespDTO2.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO2.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO2.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO2.setOrderNo("orderNo");
        dineinOrderDetailRespDTO2.setState(0);
        dineinOrderDetailRespDTO2.setStateName("orderStateName");
        dineinOrderDetailRespDTO2.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO2.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final DineInItemDTO dineInItemDTO3 = new DineInItemDTO();
        dineInItemDTO3.setItemName("itemName");
        dineInItemDTO3.setParentItemGuid(0L);
        dineinOrderDetailRespDTO2.setDineInItemDTOS(Arrays.asList(dineInItemDTO3));
        final List<DineinOrderDetailRespDTO> orderDetailLis = Arrays.asList(dineinOrderDetailRespDTO2);
        when(mockWxStoreUserOrderMapper.getWxStoreUserOrder(WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("tradeOrderGuid")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("name")
                        .diningTableGuid("diningTableGuid")
                        .tableCode("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .brandLogo("brandLogo")
                        .build())
                .build(), orderDetailLis)).thenReturn(wxStoreUserOrderDTO);

        // Run the test
        final WxStoreUserOrderDTO result = wxOrderRecordServiceImplUnderTest.getWxStoreUserOrder(
                wxStoreAdvanceConsumerReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetWxStoreUserOrder_WxStoreWeChatOrderClientServiceReturnsNoItems() {
        // Setup
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("tradeOrderGuid")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("name")
                        .diningTableGuid("diningTableGuid")
                        .tableCode("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .brandLogo("brandLogo")
                        .build())
                .build();
        final WxStoreUserOrderDTO expectedResult = new WxStoreUserOrderDTO();
        final WxStoreDineinOrderDetailsRespDTO wxStoreDineinOrderDetailsRespDTO = new WxStoreDineinOrderDetailsRespDTO();
        wxStoreDineinOrderDetailsRespDTO.setLogUrl("brandLogo");
        wxStoreDineinOrderDetailsRespDTO.setStoreName("name");
        wxStoreDineinOrderDetailsRespDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreDineinOrderDetailsRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setOrders(Arrays.asList(wxStoreDineinOrderDetailsRespDTO));

        // Configure WxUserRecordService.getWxuserRecord(...).
        final WxUserRecordDO wxUserRecordDO = new WxUserRecordDO(0L, "guid", "openId", "nickName", "headImgUrl", 0,
                "country", "province", "city", "phone", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid", 0);
        when(mockWxUserRecordService.getWxuserRecord(WxStoreConsumerDTO.builder()
                .openId("openId")
                .nickName("nickName")
                .headImgUrl("headImgUrl")
                .enterpriseGuid("enterpriseGuid")
                .enterpriseName("enterpriseName")
                .storeGuid("storeGuid")
                .storeName("name")
                .diningTableGuid("diningTableGuid")
                .tableCode("code")
                .areaGuid("areaGuid")
                .areaName("areaName")
                .brandName("brandName")
                .brandGuid("brandGuid")
                .brandLogo("brandLogo")
                .build())).thenReturn(wxUserRecordDO);

        // Configure WxStoreWeChatOrderClientService.getOrderDetails(...).
        final SingleListDTO singleListDTO = new SingleListDTO();
        singleListDTO.setDeviceType(0);
        singleListDTO.setDeviceId("openId");
        singleListDTO.setEnterpriseGuid("enterpriseGuid");
        singleListDTO.setStoreGuid("storeGuid");
        singleListDTO.setUserGuid("openId");
        singleListDTO.setUserName("nickName");
        singleListDTO.setList(Arrays.asList("value"));
        when(mockWxStoreWeChatOrderClientService.getOrderDetails(singleListDTO)).thenReturn(Collections.emptyList());

        // Configure WxStoreDineInOrderClientService.cancelOrder(...).
        final CancelOrderReqDTO cancelOrderReqDTO = new CancelOrderReqDTO();
        cancelOrderReqDTO.setDeviceType(0);
        cancelOrderReqDTO.setDeviceId("openId");
        cancelOrderReqDTO.setEnterpriseGuid("enterpriseGuid");
        cancelOrderReqDTO.setStoreGuid("storeGuid");
        cancelOrderReqDTO.setUserGuid("openId");
        cancelOrderReqDTO.setUserName("nickName");
        cancelOrderReqDTO.setOrderGuid("orderGuid");
        cancelOrderReqDTO.setReason("过期做废");
        cancelOrderReqDTO.setTable(false);
        cancelOrderReqDTO.setFastFood(false);
        when(mockWxStoreDineInOrderClientService.cancelOrder(cancelOrderReqDTO)).thenReturn(false);

        when(mockWxStoreTableClientService.getOrderGuid("diningTableGuid")).thenReturn("data");

        // Configure WxStoreDineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("orderGuid");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setOrderNo("orderNo");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setStateName("orderStateName");
        dineinOrderDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setItemName("itemName");
        dineInItemDTO.setParentItemGuid(0L);
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        when(mockWxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder()
                .data("data")
                .datas(Arrays.asList("value"))
                .build())).thenReturn(dineinOrderDetailRespDTO);

        when(mockWxStoreSessionDetailsService.getMerchantBatchGuid("diningTableGuid")).thenReturn("orderGuid");

        // Configure WxStoreMerchantOrderService.getPendingOrders(...).
        final WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO.setGuid("guid");
        wxStoreMerchantOrderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setTotalPrice(new BigDecimal("0.00"));
        wxStoreMerchantOrderDTO.setOpenId("openId");
        final List<WxStoreMerchantOrderDTO> wxStoreMerchantOrderDTOS = Arrays.asList(wxStoreMerchantOrderDTO);
        when(mockWxStoreMerchantOrderService.getPendingOrders(WxStorePendingOrdersQuery.builder()
                .storeGuid("storeGuid")
                .diningTableGuid("diningTableGuid")
                .orderStates(Arrays.asList(0))
                .tradeMode(0)
                .orderGuid("orderGuid")
                .build())).thenReturn(wxStoreMerchantOrderDTOS);

        // Configure WxStoreMerchantDineInItemService.getWxStoreMerchantDineInItem(...).
        final WxStoreMerchantDineInItemDTO wxStoreMerchantDineInItemDTO = new WxStoreMerchantDineInItemDTO();
        wxStoreMerchantDineInItemDTO.setGuid("dc1f512f-7921-4cc8-b56d-501245d8bca2");
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setGuid("ae455df7-daeb-4c0c-9243-22ed0a9492a1");
        dineInItemDTO1.setItemName("itemName");
        dineInItemDTO1.setParentItemGuid(0L);
        wxStoreMerchantDineInItemDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO1));
        when(mockWxStoreMerchantDineInItemService.getWxStoreMerchantDineInItem("guid"))
                .thenReturn(wxStoreMerchantDineInItemDTO);

        // Configure WxStoreUserOrderMapper.getWxStoreUserOrder(...).
        final WxStoreUserOrderDTO wxStoreUserOrderDTO = new WxStoreUserOrderDTO();
        final WxStoreDineinOrderDetailsRespDTO wxStoreDineinOrderDetailsRespDTO1 = new WxStoreDineinOrderDetailsRespDTO();
        wxStoreDineinOrderDetailsRespDTO1.setLogUrl("brandLogo");
        wxStoreDineinOrderDetailsRespDTO1.setStoreName("name");
        wxStoreDineinOrderDetailsRespDTO1.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreDineinOrderDetailsRespDTO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreUserOrderDTO.setOrders(Arrays.asList(wxStoreDineinOrderDetailsRespDTO1));
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO1 = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO1.setGuid("orderGuid");
        dineinOrderDetailRespDTO1.setTradeMode(0);
        dineinOrderDetailRespDTO1.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setOrderNo("orderNo");
        dineinOrderDetailRespDTO1.setState(0);
        dineinOrderDetailRespDTO1.setStateName("orderStateName");
        dineinOrderDetailRespDTO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO1.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final DineInItemDTO dineInItemDTO2 = new DineInItemDTO();
        dineInItemDTO2.setItemName("itemName");
        dineInItemDTO2.setParentItemGuid(0L);
        dineinOrderDetailRespDTO1.setDineInItemDTOS(Arrays.asList(dineInItemDTO2));
        final List<DineinOrderDetailRespDTO> orderDetailLis = Arrays.asList(dineinOrderDetailRespDTO1);
        when(mockWxStoreUserOrderMapper.getWxStoreUserOrder(WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("tradeOrderGuid")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("name")
                        .diningTableGuid("diningTableGuid")
                        .tableCode("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .brandLogo("brandLogo")
                        .build())
                .build(), orderDetailLis)).thenReturn(wxStoreUserOrderDTO);

        // Run the test
        final WxStoreUserOrderDTO result = wxOrderRecordServiceImplUnderTest.getWxStoreUserOrder(
                wxStoreAdvanceConsumerReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetWxStoreUserOrder_WxStoreMerchantOrderServiceReturnsNoItems() {
        // Setup
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("tradeOrderGuid")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("name")
                        .diningTableGuid("diningTableGuid")
                        .tableCode("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .brandLogo("brandLogo")
                        .build())
                .build();
        final WxStoreUserOrderDTO expectedResult = new WxStoreUserOrderDTO();
        final WxStoreDineinOrderDetailsRespDTO wxStoreDineinOrderDetailsRespDTO = new WxStoreDineinOrderDetailsRespDTO();
        wxStoreDineinOrderDetailsRespDTO.setLogUrl("brandLogo");
        wxStoreDineinOrderDetailsRespDTO.setStoreName("name");
        wxStoreDineinOrderDetailsRespDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreDineinOrderDetailsRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setOrders(Arrays.asList(wxStoreDineinOrderDetailsRespDTO));

        // Configure WxUserRecordService.getWxuserRecord(...).
        final WxUserRecordDO wxUserRecordDO = new WxUserRecordDO(0L, "guid", "openId", "nickName", "headImgUrl", 0,
                "country", "province", "city", "phone", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid", 0);
        when(mockWxUserRecordService.getWxuserRecord(WxStoreConsumerDTO.builder()
                .openId("openId")
                .nickName("nickName")
                .headImgUrl("headImgUrl")
                .enterpriseGuid("enterpriseGuid")
                .enterpriseName("enterpriseName")
                .storeGuid("storeGuid")
                .storeName("name")
                .diningTableGuid("diningTableGuid")
                .tableCode("code")
                .areaGuid("areaGuid")
                .areaName("areaName")
                .brandName("brandName")
                .brandGuid("brandGuid")
                .brandLogo("brandLogo")
                .build())).thenReturn(wxUserRecordDO);

        // Configure WxStoreWeChatOrderClientService.getOrderDetails(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("orderGuid");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setOrderNo("orderNo");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setStateName("orderStateName");
        dineinOrderDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setItemName("itemName");
        dineInItemDTO.setParentItemGuid(0L);
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        final List<DineinOrderDetailRespDTO> dineinOrderDetailRespDTOS = Arrays.asList(dineinOrderDetailRespDTO);
        final SingleListDTO singleListDTO = new SingleListDTO();
        singleListDTO.setDeviceType(0);
        singleListDTO.setDeviceId("openId");
        singleListDTO.setEnterpriseGuid("enterpriseGuid");
        singleListDTO.setStoreGuid("storeGuid");
        singleListDTO.setUserGuid("openId");
        singleListDTO.setUserName("nickName");
        singleListDTO.setList(Arrays.asList("value"));
        when(mockWxStoreWeChatOrderClientService.getOrderDetails(singleListDTO)).thenReturn(dineinOrderDetailRespDTOS);

        // Configure WxStoreDineInOrderClientService.cancelOrder(...).
        final CancelOrderReqDTO cancelOrderReqDTO = new CancelOrderReqDTO();
        cancelOrderReqDTO.setDeviceType(0);
        cancelOrderReqDTO.setDeviceId("openId");
        cancelOrderReqDTO.setEnterpriseGuid("enterpriseGuid");
        cancelOrderReqDTO.setStoreGuid("storeGuid");
        cancelOrderReqDTO.setUserGuid("openId");
        cancelOrderReqDTO.setUserName("nickName");
        cancelOrderReqDTO.setOrderGuid("orderGuid");
        cancelOrderReqDTO.setReason("过期做废");
        cancelOrderReqDTO.setTable(false);
        cancelOrderReqDTO.setFastFood(false);
        when(mockWxStoreDineInOrderClientService.cancelOrder(cancelOrderReqDTO)).thenReturn(false);

        when(mockWxStoreTableClientService.getOrderGuid("diningTableGuid")).thenReturn("data");

        // Configure WxStoreDineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO1 = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO1.setGuid("orderGuid");
        dineinOrderDetailRespDTO1.setTradeMode(0);
        dineinOrderDetailRespDTO1.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setOrderNo("orderNo");
        dineinOrderDetailRespDTO1.setState(0);
        dineinOrderDetailRespDTO1.setStateName("orderStateName");
        dineinOrderDetailRespDTO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO1.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setItemName("itemName");
        dineInItemDTO1.setParentItemGuid(0L);
        dineinOrderDetailRespDTO1.setDineInItemDTOS(Arrays.asList(dineInItemDTO1));
        when(mockWxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder()
                .data("data")
                .datas(Arrays.asList("value"))
                .build())).thenReturn(dineinOrderDetailRespDTO1);

        when(mockWxStoreSessionDetailsService.getMerchantBatchGuid("diningTableGuid")).thenReturn("orderGuid");
        when(mockWxStoreMerchantOrderService.getPendingOrders(WxStorePendingOrdersQuery.builder()
                .storeGuid("storeGuid")
                .diningTableGuid("diningTableGuid")
                .orderStates(Arrays.asList(0))
                .tradeMode(0)
                .orderGuid("orderGuid")
                .build())).thenReturn(Collections.emptyList());

        // Configure WxStoreUserOrderMapper.getWxStoreUserOrder(...).
        final WxStoreUserOrderDTO wxStoreUserOrderDTO = new WxStoreUserOrderDTO();
        final WxStoreDineinOrderDetailsRespDTO wxStoreDineinOrderDetailsRespDTO1 = new WxStoreDineinOrderDetailsRespDTO();
        wxStoreDineinOrderDetailsRespDTO1.setLogUrl("brandLogo");
        wxStoreDineinOrderDetailsRespDTO1.setStoreName("name");
        wxStoreDineinOrderDetailsRespDTO1.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreDineinOrderDetailsRespDTO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreUserOrderDTO.setOrders(Arrays.asList(wxStoreDineinOrderDetailsRespDTO1));
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO2 = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO2.setGuid("orderGuid");
        dineinOrderDetailRespDTO2.setTradeMode(0);
        dineinOrderDetailRespDTO2.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO2.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO2.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO2.setOrderNo("orderNo");
        dineinOrderDetailRespDTO2.setState(0);
        dineinOrderDetailRespDTO2.setStateName("orderStateName");
        dineinOrderDetailRespDTO2.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO2.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final DineInItemDTO dineInItemDTO2 = new DineInItemDTO();
        dineInItemDTO2.setItemName("itemName");
        dineInItemDTO2.setParentItemGuid(0L);
        dineinOrderDetailRespDTO2.setDineInItemDTOS(Arrays.asList(dineInItemDTO2));
        final List<DineinOrderDetailRespDTO> orderDetailLis = Arrays.asList(dineinOrderDetailRespDTO2);
        when(mockWxStoreUserOrderMapper.getWxStoreUserOrder(WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("tradeOrderGuid")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("name")
                        .diningTableGuid("diningTableGuid")
                        .tableCode("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .brandLogo("brandLogo")
                        .build())
                .build(), orderDetailLis)).thenReturn(wxStoreUserOrderDTO);

        // Run the test
        final WxStoreUserOrderDTO result = wxOrderRecordServiceImplUnderTest.getWxStoreUserOrder(
                wxStoreAdvanceConsumerReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetOne() {
        // Setup
        final WxOrderRecordQuery query = WxOrderRecordQuery.builder()
                .orderGuid("orderGuid")
                .build();
        final WxOrderRecordDTO expectedResult = WxOrderRecordDTO.builder().build();

        // Run the test
        final WxOrderRecordDTO result = wxOrderRecordServiceImplUnderTest.getOne(query);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetWxBrandUserOrder() {
        // Setup
        final WxBrandUserOrderReqDTO wxBrandUserOrderReqDTO = WxBrandUserOrderReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("name")
                        .diningTableGuid("diningTableGuid")
                        .tableCode("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .brandLogo("brandLogo")
                        .build())
                .orderState(0)
                .build();
        final WxBrandUserOrderDTO expectedResult = new WxBrandUserOrderDTO(
                Arrays.asList(new WxBrandUserOrderTitleGroup(0, "已取消", 0)), Arrays.asList(
                new WxBrandUserOrderItemDTO("e19e50b7-9e82-41f9-b8da-7274e1a1cc20", "brandLogo",
                        WxStoreConsumerDTO.builder()
                                .openId("openId")
                                .nickName("nickName")
                                .headImgUrl("headImgUrl")
                                .enterpriseGuid("enterpriseGuid")
                                .enterpriseName("enterpriseName")
                                .storeGuid("storeGuid")
                                .storeName("name")
                                .diningTableGuid("diningTableGuid")
                                .tableCode("code")
                                .areaGuid("areaGuid")
                                .areaName("areaName")
                                .brandName("brandName")
                                .brandGuid("brandGuid")
                                .brandLogo("brandLogo")
                                .build(), 0, new BigDecimal("0.00"), 0, LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        Arrays.asList("value"))));

        // Configure WxStoreSessionDetailsService.getBrandDetail(...).
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("3c3fab7f-efa8-4395-a76e-418bfc8e2864");
        brandDTO.setUuid("39c0c0e0-c769-41a7-9177-596f4a7e170e");
        brandDTO.setName("brandName");
        brandDTO.setDescription("description");
        brandDTO.setLogoUrl("brandLogo");
        when(mockWxStoreSessionDetailsService.getBrandDetail("brandGuid")).thenReturn(brandDTO);

        // Configure WxUserRecordService.getWxuserRecord(...).
        final WxUserRecordDO wxUserRecordDO = new WxUserRecordDO(0L, "guid", "openId", "nickName", "headImgUrl", 0,
                "country", "province", "city", "phone", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid", 0);
        when(mockWxUserRecordService.getWxuserRecord(WxStoreConsumerDTO.builder()
                .openId("openId")
                .nickName("nickName")
                .headImgUrl("headImgUrl")
                .enterpriseGuid("enterpriseGuid")
                .enterpriseName("enterpriseName")
                .storeGuid("storeGuid")
                .storeName("name")
                .diningTableGuid("diningTableGuid")
                .tableCode("code")
                .areaGuid("areaGuid")
                .areaName("areaName")
                .brandName("brandName")
                .brandGuid("brandGuid")
                .brandLogo("brandLogo")
                .build())).thenReturn(wxUserRecordDO);

        when(mockOrderItemService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));

        // Configure WxStoreWeChatOrderClientService.getOrderDetailList(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("orderGuid");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setOrderNo("orderNo");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setStateName("orderStateName");
        dineinOrderDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setItemName("itemName");
        dineInItemDTO.setParentItemGuid(0L);
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        final List<DineinOrderDetailRespDTO> dineinOrderDetailRespDTOS = Arrays.asList(dineinOrderDetailRespDTO);
        final SingleListDTO singleListDTO = new SingleListDTO();
        singleListDTO.setDeviceType(0);
        singleListDTO.setDeviceId("openId");
        singleListDTO.setEnterpriseGuid("enterpriseGuid");
        singleListDTO.setStoreGuid("storeGuid");
        singleListDTO.setUserGuid("openId");
        singleListDTO.setUserName("nickName");
        singleListDTO.setList(Arrays.asList("value"));
        when(mockWxStoreWeChatOrderClientService.getOrderDetailList(singleListDTO))
                .thenReturn(dineinOrderDetailRespDTOS);

        // Configure WxStoreSessionDetailsService.getTableDetail(...).
        final TableDTO tableDTO = TableDTO.builder()
                .storeGuid("storeGuid")
                .areaGuid("areaGuid")
                .areaName("areaName")
                .code("code")
                .build();
        when(mockWxStoreSessionDetailsService.getTableDetail("diningTableGuid")).thenReturn(tableDTO);

        // Configure WxStoreSessionDetailsService.getStoreDetail(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("cc8df81a-65d2-4f67-ae41-19806b7ff93c");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        when(mockWxStoreSessionDetailsService.getStoreDetail("storeGuid")).thenReturn(storeDTO);

        // Run the test
        final WxBrandUserOrderDTO result = wxOrderRecordServiceImplUnderTest.getWxBrandUserOrder(
                wxBrandUserOrderReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetWxBrandUserOrder_WxStoreWeChatOrderClientServiceReturnsNoItems() {
        // Setup
        final WxBrandUserOrderReqDTO wxBrandUserOrderReqDTO = WxBrandUserOrderReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("name")
                        .diningTableGuid("diningTableGuid")
                        .tableCode("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .brandLogo("brandLogo")
                        .build())
                .orderState(0)
                .build();
        final WxBrandUserOrderDTO expectedResult = new WxBrandUserOrderDTO(
                Arrays.asList(new WxBrandUserOrderTitleGroup(0, "已取消", 0)), Arrays.asList(
                new WxBrandUserOrderItemDTO("e19e50b7-9e82-41f9-b8da-7274e1a1cc20", "brandLogo",
                        WxStoreConsumerDTO.builder()
                                .openId("openId")
                                .nickName("nickName")
                                .headImgUrl("headImgUrl")
                                .enterpriseGuid("enterpriseGuid")
                                .enterpriseName("enterpriseName")
                                .storeGuid("storeGuid")
                                .storeName("name")
                                .diningTableGuid("diningTableGuid")
                                .tableCode("code")
                                .areaGuid("areaGuid")
                                .areaName("areaName")
                                .brandName("brandName")
                                .brandGuid("brandGuid")
                                .brandLogo("brandLogo")
                                .build(), 0, new BigDecimal("0.00"), 0, LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        Arrays.asList("value"))));

        // Configure WxStoreSessionDetailsService.getBrandDetail(...).
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("3c3fab7f-efa8-4395-a76e-418bfc8e2864");
        brandDTO.setUuid("39c0c0e0-c769-41a7-9177-596f4a7e170e");
        brandDTO.setName("brandName");
        brandDTO.setDescription("description");
        brandDTO.setLogoUrl("brandLogo");
        when(mockWxStoreSessionDetailsService.getBrandDetail("brandGuid")).thenReturn(brandDTO);

        // Configure WxUserRecordService.getWxuserRecord(...).
        final WxUserRecordDO wxUserRecordDO = new WxUserRecordDO(0L, "guid", "openId", "nickName", "headImgUrl", 0,
                "country", "province", "city", "phone", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid", 0);
        when(mockWxUserRecordService.getWxuserRecord(WxStoreConsumerDTO.builder()
                .openId("openId")
                .nickName("nickName")
                .headImgUrl("headImgUrl")
                .enterpriseGuid("enterpriseGuid")
                .enterpriseName("enterpriseName")
                .storeGuid("storeGuid")
                .storeName("name")
                .diningTableGuid("diningTableGuid")
                .tableCode("code")
                .areaGuid("areaGuid")
                .areaName("areaName")
                .brandName("brandName")
                .brandGuid("brandGuid")
                .brandLogo("brandLogo")
                .build())).thenReturn(wxUserRecordDO);

        when(mockOrderItemService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));

        // Configure WxStoreWeChatOrderClientService.getOrderDetailList(...).
        final SingleListDTO singleListDTO = new SingleListDTO();
        singleListDTO.setDeviceType(0);
        singleListDTO.setDeviceId("openId");
        singleListDTO.setEnterpriseGuid("enterpriseGuid");
        singleListDTO.setStoreGuid("storeGuid");
        singleListDTO.setUserGuid("openId");
        singleListDTO.setUserName("nickName");
        singleListDTO.setList(Arrays.asList("value"));
        when(mockWxStoreWeChatOrderClientService.getOrderDetailList(singleListDTO)).thenReturn(Collections.emptyList());

        // Configure WxStoreSessionDetailsService.getTableDetail(...).
        final TableDTO tableDTO = TableDTO.builder()
                .storeGuid("storeGuid")
                .areaGuid("areaGuid")
                .areaName("areaName")
                .code("code")
                .build();
        when(mockWxStoreSessionDetailsService.getTableDetail("diningTableGuid")).thenReturn(tableDTO);

        // Configure WxStoreSessionDetailsService.getStoreDetail(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("cc8df81a-65d2-4f67-ae41-19806b7ff93c");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        when(mockWxStoreSessionDetailsService.getStoreDetail("storeGuid")).thenReturn(storeDTO);

        // Run the test
        final WxBrandUserOrderDTO result = wxOrderRecordServiceImplUnderTest.getWxBrandUserOrder(
                wxBrandUserOrderReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testCancelFastOrder1() {
        // Setup
        // Run the test
        wxOrderRecordServiceImplUnderTest.cancelFastOrder("orderGuid");

        // Verify the results
    }

    @Test
    public void testCancelFastOrder2() {
        // Setup
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("orderGuid");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setOrderNo("orderNo");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setStateName("orderStateName");
        dineinOrderDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setItemName("itemName");
        dineInItemDTO.setParentItemGuid(0L);
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));

        final WxStoreConsumerDTO wxStoreConsumerDTO = WxStoreConsumerDTO.builder()
                .openId("openId")
                .nickName("nickName")
                .headImgUrl("headImgUrl")
                .enterpriseGuid("enterpriseGuid")
                .enterpriseName("enterpriseName")
                .storeGuid("storeGuid")
                .storeName("name")
                .diningTableGuid("diningTableGuid")
                .tableCode("code")
                .areaGuid("areaGuid")
                .areaName("areaName")
                .brandName("brandName")
                .brandGuid("brandGuid")
                .brandLogo("brandLogo")
                .build();

        // Configure WxStoreDineInOrderClientService.cancelOrder(...).
        final CancelOrderReqDTO cancelOrderReqDTO = new CancelOrderReqDTO();
        cancelOrderReqDTO.setDeviceType(0);
        cancelOrderReqDTO.setDeviceId("openId");
        cancelOrderReqDTO.setEnterpriseGuid("enterpriseGuid");
        cancelOrderReqDTO.setStoreGuid("storeGuid");
        cancelOrderReqDTO.setUserGuid("openId");
        cancelOrderReqDTO.setUserName("nickName");
        cancelOrderReqDTO.setOrderGuid("orderGuid");
        cancelOrderReqDTO.setReason("过期做废");
        cancelOrderReqDTO.setTable(false);
        cancelOrderReqDTO.setFastFood(false);
        when(mockWxStoreDineInOrderClientService.cancelOrder(cancelOrderReqDTO)).thenReturn(false);

        // Run the test
        final boolean result = wxOrderRecordServiceImplUnderTest.cancelFastOrder(dineinOrderDetailRespDTO,
                wxStoreConsumerDTO);

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testCancelFastOrder2_WxStoreDineInOrderClientServiceReturnsTrue() {
        // Setup
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("orderGuid");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setOrderNo("orderNo");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setStateName("orderStateName");
        dineinOrderDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setItemName("itemName");
        dineInItemDTO.setParentItemGuid(0L);
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));

        final WxStoreConsumerDTO wxStoreConsumerDTO = WxStoreConsumerDTO.builder()
                .openId("openId")
                .nickName("nickName")
                .headImgUrl("headImgUrl")
                .enterpriseGuid("enterpriseGuid")
                .enterpriseName("enterpriseName")
                .storeGuid("storeGuid")
                .storeName("name")
                .diningTableGuid("diningTableGuid")
                .tableCode("code")
                .areaGuid("areaGuid")
                .areaName("areaName")
                .brandName("brandName")
                .brandGuid("brandGuid")
                .brandLogo("brandLogo")
                .build();

        // Configure WxStoreDineInOrderClientService.cancelOrder(...).
        final CancelOrderReqDTO cancelOrderReqDTO = new CancelOrderReqDTO();
        cancelOrderReqDTO.setDeviceType(0);
        cancelOrderReqDTO.setDeviceId("openId");
        cancelOrderReqDTO.setEnterpriseGuid("enterpriseGuid");
        cancelOrderReqDTO.setStoreGuid("storeGuid");
        cancelOrderReqDTO.setUserGuid("openId");
        cancelOrderReqDTO.setUserName("nickName");
        cancelOrderReqDTO.setOrderGuid("orderGuid");
        cancelOrderReqDTO.setReason("过期做废");
        cancelOrderReqDTO.setTable(false);
        cancelOrderReqDTO.setFastFood(false);
        when(mockWxStoreDineInOrderClientService.cancelOrder(cancelOrderReqDTO)).thenReturn(true);

        // Run the test
        final boolean result = wxOrderRecordServiceImplUnderTest.cancelFastOrder(dineinOrderDetailRespDTO,
                wxStoreConsumerDTO);

        // Verify the results
        assertTrue(result);
    }

    @Test
    public void testReplaceOrderID() {
        // Setup
        final Map<String, String> map = new HashMap<>();

        // Run the test
        final Boolean result = wxOrderRecordServiceImplUnderTest.replaceOrderID(map);

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testGetOldOrderList() {
        // Setup
        final List<WxOrderRecordDO> expectedResult = Arrays.asList(
                new WxOrderRecordDO(0L, "e19e50b7-9e82-41f9-b8da-7274e1a1cc20", "orderGuid", "merchantGuid", "guid",
                        "brandGuid", "brandName", "brandLogo", "storeGuid", "storeName", "areaGuid", "areaName",
                        "diningTableGuid", "diningTableCode", 0, 0, "orderStateName", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName", "orderHolderNo",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0));

        // Run the test
        final List<WxOrderRecordDO> result = wxOrderRecordServiceImplUnderTest.getOldOrderList(
                new HashSet<>(Arrays.asList("value")));

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testBatchSave() {
        // Setup
        final List<WxOrderRecordDO> wxOrderRecordDOS = Arrays.asList(
                new WxOrderRecordDO(0L, "e19e50b7-9e82-41f9-b8da-7274e1a1cc20", "orderGuid", "merchantGuid", "guid",
                        "brandGuid", "brandName", "brandLogo", "storeGuid", "storeName", "areaGuid", "areaName",
                        "diningTableGuid", "diningTableCode", 0, 0, "orderStateName", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName", "orderHolderNo",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0));

        // Run the test
        final Boolean result = wxOrderRecordServiceImplUnderTest.batchSave(wxOrderRecordDOS);

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testGetOutStandingOrders() {
        // Setup
        final List<WxOrderRecordDO> expectedResult = Arrays.asList(
                new WxOrderRecordDO(0L, "e19e50b7-9e82-41f9-b8da-7274e1a1cc20", "orderGuid", "merchantGuid", "guid",
                        "brandGuid", "brandName", "brandLogo", "storeGuid", "storeName", "areaGuid", "areaName",
                        "diningTableGuid", "diningTableCode", 0, 0, "orderStateName", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName", "orderHolderNo",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0));

        // Run the test
        final List<WxOrderRecordDO> result = wxOrderRecordServiceImplUnderTest.getOutStandingOrders("tableGuid");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testUserStoreList() {
        // Setup
        final List<WxStoreUserOrderItemDTO> expectedResult = Arrays.asList(WxStoreUserOrderItemDTO.builder()
                .guid("e19e50b7-9e82-41f9-b8da-7274e1a1cc20")
                .storeName("storeName")
                .brandName("brandName")
                .logUrl("brandLogo")
                .tradeMode(0)
                .actuallyPayFee(new BigDecimal("0.00"))
                .useDisCount(0)
                .disCountFee(new BigDecimal("0.00"))
                .originalFee(new BigDecimal("0.00"))
                .state(0)
                .checkoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .itemNames(Arrays.asList("value"))
                .build());

        // Configure WxUserRecordService.getOneByOpenId(...).
        final WxUserRecordDO wxUserRecordDO = new WxUserRecordDO(0L, "guid", "openId", "nickName", "headImgUrl", 0,
                "country", "province", "city", "phone", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid", 0);
        when(mockWxUserRecordService.getOneByOpenId("openId")).thenReturn(wxUserRecordDO);

        // Configure WxStoreSessionDetailsService.getStoreBrandDetail(...).
        final BrandStoreDetailDTO brandStoreDetailDTO = new BrandStoreDetailDTO();
        brandStoreDetailDTO.setStoreGuid("storeGuid");
        brandStoreDetailDTO.setBrandGuid("brandGuid");
        brandStoreDetailDTO.setStoreName("storeName");
        brandStoreDetailDTO.setBrandName("brandName");
        brandStoreDetailDTO.setBrandLogoUrl("brandLogo");
        when(mockWxStoreSessionDetailsService.getStoreBrandDetail("storeGuid", "brandGuid"))
                .thenReturn(brandStoreDetailDTO);

        // Configure WxStoreWeChatOrderClientService.getOrderDetailList(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("orderGuid");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setOrderNo("orderNo");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setStateName("orderStateName");
        dineinOrderDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setItemName("itemName");
        dineInItemDTO.setParentItemGuid(0L);
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        final List<DineinOrderDetailRespDTO> dineinOrderDetailRespDTOS = Arrays.asList(dineinOrderDetailRespDTO);
        final SingleListDTO singleListDTO = new SingleListDTO();
        singleListDTO.setDeviceType(0);
        singleListDTO.setDeviceId("openId");
        singleListDTO.setEnterpriseGuid("enterpriseGuid");
        singleListDTO.setStoreGuid("storeGuid");
        singleListDTO.setUserGuid("openId");
        singleListDTO.setUserName("nickName");
        singleListDTO.setList(Arrays.asList("value"));
        when(mockWxStoreWeChatOrderClientService.getOrderDetailList(singleListDTO))
                .thenReturn(dineinOrderDetailRespDTOS);

        when(mockWxStoreTableClientService.getOrderGuid("diningTableGuid")).thenReturn("result");
        when(mockOrderItemService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));

        // Run the test
        final List<WxStoreUserOrderItemDTO> result = wxOrderRecordServiceImplUnderTest.userStoreList();

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testUserStoreList_WxUserRecordServiceReturnsNull() {
        // Setup
        final List<WxStoreUserOrderItemDTO> expectedResult = Arrays.asList(WxStoreUserOrderItemDTO.builder()
                .guid("e19e50b7-9e82-41f9-b8da-7274e1a1cc20")
                .storeName("storeName")
                .brandName("brandName")
                .logUrl("brandLogo")
                .tradeMode(0)
                .actuallyPayFee(new BigDecimal("0.00"))
                .useDisCount(0)
                .disCountFee(new BigDecimal("0.00"))
                .originalFee(new BigDecimal("0.00"))
                .state(0)
                .checkoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .itemNames(Arrays.asList("value"))
                .build());
        when(mockWxUserRecordService.getOneByOpenId("openId")).thenReturn(null);

        // Run the test
        final List<WxStoreUserOrderItemDTO> result = wxOrderRecordServiceImplUnderTest.userStoreList();

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testUserStoreList_WxStoreWeChatOrderClientServiceReturnsNoItems() {
        // Setup
        // Configure WxUserRecordService.getOneByOpenId(...).
        final WxUserRecordDO wxUserRecordDO = new WxUserRecordDO(0L, "guid", "openId", "nickName", "headImgUrl", 0,
                "country", "province", "city", "phone", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid", 0);
        when(mockWxUserRecordService.getOneByOpenId("openId")).thenReturn(wxUserRecordDO);

        // Configure WxStoreSessionDetailsService.getStoreBrandDetail(...).
        final BrandStoreDetailDTO brandStoreDetailDTO = new BrandStoreDetailDTO();
        brandStoreDetailDTO.setStoreGuid("storeGuid");
        brandStoreDetailDTO.setBrandGuid("brandGuid");
        brandStoreDetailDTO.setStoreName("storeName");
        brandStoreDetailDTO.setBrandName("brandName");
        brandStoreDetailDTO.setBrandLogoUrl("brandLogo");
        when(mockWxStoreSessionDetailsService.getStoreBrandDetail("storeGuid", "brandGuid"))
                .thenReturn(brandStoreDetailDTO);

        // Configure WxStoreWeChatOrderClientService.getOrderDetailList(...).
        final SingleListDTO singleListDTO = new SingleListDTO();
        singleListDTO.setDeviceType(0);
        singleListDTO.setDeviceId("openId");
        singleListDTO.setEnterpriseGuid("enterpriseGuid");
        singleListDTO.setStoreGuid("storeGuid");
        singleListDTO.setUserGuid("openId");
        singleListDTO.setUserName("nickName");
        singleListDTO.setList(Arrays.asList("value"));
        when(mockWxStoreWeChatOrderClientService.getOrderDetailList(singleListDTO)).thenReturn(Collections.emptyList());

        when(mockWxStoreTableClientService.getOrderGuid("diningTableGuid")).thenReturn("result");
        when(mockOrderItemService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));

        // Run the test
        final List<WxStoreUserOrderItemDTO> result = wxOrderRecordServiceImplUnderTest.userStoreList();

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testGetUnFinishStoreUserOrderList() {
        // Setup
        final List<WxStoreUserOrderItemDTO> expectedResult = Arrays.asList(WxStoreUserOrderItemDTO.builder()
                .guid("e19e50b7-9e82-41f9-b8da-7274e1a1cc20")
                .storeName("storeName")
                .brandName("brandName")
                .logUrl("brandLogo")
                .tradeMode(0)
                .actuallyPayFee(new BigDecimal("0.00"))
                .useDisCount(0)
                .disCountFee(new BigDecimal("0.00"))
                .originalFee(new BigDecimal("0.00"))
                .state(0)
                .checkoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .itemNames(Arrays.asList("value"))
                .build());

        // Configure WxUserRecordService.getOneByOpenId(...).
        final WxUserRecordDO wxUserRecordDO = new WxUserRecordDO(0L, "guid", "openId", "nickName", "headImgUrl", 0,
                "country", "province", "city", "phone", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid", 0);
        when(mockWxUserRecordService.getOneByOpenId("openId")).thenReturn(wxUserRecordDO);

        // Configure WxStoreSessionDetailsService.getStoreBrandDetail(...).
        final BrandStoreDetailDTO brandStoreDetailDTO = new BrandStoreDetailDTO();
        brandStoreDetailDTO.setStoreGuid("storeGuid");
        brandStoreDetailDTO.setBrandGuid("brandGuid");
        brandStoreDetailDTO.setStoreName("storeName");
        brandStoreDetailDTO.setBrandName("brandName");
        brandStoreDetailDTO.setBrandLogoUrl("brandLogo");
        when(mockWxStoreSessionDetailsService.getStoreBrandDetail("storeGuid", "brandGuid"))
                .thenReturn(brandStoreDetailDTO);

        // Configure WxStoreWeChatOrderClientService.getOrderDetailList(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("orderGuid");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setOrderNo("orderNo");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setStateName("orderStateName");
        dineinOrderDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setItemName("itemName");
        dineInItemDTO.setParentItemGuid(0L);
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        final List<DineinOrderDetailRespDTO> dineinOrderDetailRespDTOS = Arrays.asList(dineinOrderDetailRespDTO);
        final SingleListDTO singleListDTO = new SingleListDTO();
        singleListDTO.setDeviceType(0);
        singleListDTO.setDeviceId("openId");
        singleListDTO.setEnterpriseGuid("enterpriseGuid");
        singleListDTO.setStoreGuid("storeGuid");
        singleListDTO.setUserGuid("openId");
        singleListDTO.setUserName("nickName");
        singleListDTO.setList(Arrays.asList("value"));
        when(mockWxStoreWeChatOrderClientService.getOrderDetailList(singleListDTO))
                .thenReturn(dineinOrderDetailRespDTOS);

        when(mockWxStoreTableClientService.getOrderGuid("diningTableGuid")).thenReturn("result");
        when(mockOrderItemService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));

        // Run the test
        final List<WxStoreUserOrderItemDTO> result = wxOrderRecordServiceImplUnderTest.getUnFinishStoreUserOrderList();

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetUnFinishStoreUserOrderList_WxUserRecordServiceReturnsNull() {
        // Setup
        final List<WxStoreUserOrderItemDTO> expectedResult = Arrays.asList(WxStoreUserOrderItemDTO.builder()
                .guid("e19e50b7-9e82-41f9-b8da-7274e1a1cc20")
                .storeName("storeName")
                .brandName("brandName")
                .logUrl("brandLogo")
                .tradeMode(0)
                .actuallyPayFee(new BigDecimal("0.00"))
                .useDisCount(0)
                .disCountFee(new BigDecimal("0.00"))
                .originalFee(new BigDecimal("0.00"))
                .state(0)
                .checkoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .itemNames(Arrays.asList("value"))
                .build());
        when(mockWxUserRecordService.getOneByOpenId("openId")).thenReturn(null);

        // Run the test
        final List<WxStoreUserOrderItemDTO> result = wxOrderRecordServiceImplUnderTest.getUnFinishStoreUserOrderList();

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetUnFinishStoreUserOrderList_WxStoreWeChatOrderClientServiceReturnsNoItems() {
        // Setup
        // Configure WxUserRecordService.getOneByOpenId(...).
        final WxUserRecordDO wxUserRecordDO = new WxUserRecordDO(0L, "guid", "openId", "nickName", "headImgUrl", 0,
                "country", "province", "city", "phone", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid", 0);
        when(mockWxUserRecordService.getOneByOpenId("openId")).thenReturn(wxUserRecordDO);

        // Configure WxStoreSessionDetailsService.getStoreBrandDetail(...).
        final BrandStoreDetailDTO brandStoreDetailDTO = new BrandStoreDetailDTO();
        brandStoreDetailDTO.setStoreGuid("storeGuid");
        brandStoreDetailDTO.setBrandGuid("brandGuid");
        brandStoreDetailDTO.setStoreName("storeName");
        brandStoreDetailDTO.setBrandName("brandName");
        brandStoreDetailDTO.setBrandLogoUrl("brandLogo");
        when(mockWxStoreSessionDetailsService.getStoreBrandDetail("storeGuid", "brandGuid"))
                .thenReturn(brandStoreDetailDTO);

        // Configure WxStoreWeChatOrderClientService.getOrderDetailList(...).
        final SingleListDTO singleListDTO = new SingleListDTO();
        singleListDTO.setDeviceType(0);
        singleListDTO.setDeviceId("openId");
        singleListDTO.setEnterpriseGuid("enterpriseGuid");
        singleListDTO.setStoreGuid("storeGuid");
        singleListDTO.setUserGuid("openId");
        singleListDTO.setUserName("nickName");
        singleListDTO.setList(Arrays.asList("value"));
        when(mockWxStoreWeChatOrderClientService.getOrderDetailList(singleListDTO)).thenReturn(Collections.emptyList());

        when(mockWxStoreTableClientService.getOrderGuid("diningTableGuid")).thenReturn("result");
        when(mockOrderItemService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));

        // Run the test
        final List<WxStoreUserOrderItemDTO> result = wxOrderRecordServiceImplUnderTest.getUnFinishStoreUserOrderList();

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testGetMerchantOrderGuid() {
        assertEquals("result", wxOrderRecordServiceImplUnderTest.getMerchantOrderGuid("orderRecordGuid"));
    }

    @Test
    public void testGetByOrderGuid() {
        // Setup
        final WxOrderRecordDO expectedResult = new WxOrderRecordDO(0L, "e19e50b7-9e82-41f9-b8da-7274e1a1cc20",
                "orderGuid", "merchantGuid", "guid", "brandGuid", "brandName", "brandLogo", "storeGuid", "storeName",
                "areaGuid", "areaName", "diningTableGuid", "diningTableCode", 0, 0, "orderStateName",
                new BigDecimal("0.00"), new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName",
                "orderHolderNo", LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0);

        // Run the test
        final WxOrderRecordDO result = wxOrderRecordServiceImplUnderTest.getByOrderGuid("orderGuid");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testUserStorePage() {
        // Setup
        // Configure WxUserRecordService.getOneByOpenId(...).
        final WxUserRecordDO wxUserRecordDO = new WxUserRecordDO(0L, "guid", "openId", "nickName", "headImgUrl", 0,
                "country", "province", "city", "phone", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid", 0);
        when(mockWxUserRecordService.getOneByOpenId("openId")).thenReturn(wxUserRecordDO);

        // Run the test
        final IPage<WxStoreUserOrderItemDTO> result = wxOrderRecordServiceImplUnderTest.userStorePage(0, 0);

        // Verify the results
    }

    @Test
    public void testUserStorePage_WxUserRecordServiceReturnsNull() {
        // Setup
        when(mockWxUserRecordService.getOneByOpenId("openId")).thenReturn(null);

        // Run the test
        final IPage<WxStoreUserOrderItemDTO> result = wxOrderRecordServiceImplUnderTest.userStorePage(0, 0);

        // Verify the results
    }

    @Test
    public void testDealFastOrderTimeOut() {
        // Setup
        // Configure WxUserRecordService.getById(...).
        final WxUserRecordDO wxUserRecordDO = new WxUserRecordDO(0L, "guid", "openId", "nickName", "headImgUrl", 0,
                "country", "province", "city", "phone", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid", 0);
        when(mockWxUserRecordService.getById("guid")).thenReturn(wxUserRecordDO);

        // Configure WxStoreWeChatOrderClientService.getOrder(...).
        final OrderWechatDTO orderWechatDTO = new OrderWechatDTO();
        orderWechatDTO.setCheckoutStaffGuid("checkoutStaffGuid");
        orderWechatDTO.setCheckoutStaffName("checkoutStaffName");
        orderWechatDTO.setOrderGuid("orderGuid");
        orderWechatDTO.setState(0);
        orderWechatDTO.setDeviceType(0);
        when(mockWxStoreWeChatOrderClientService.getOrder("orderGuid")).thenReturn(orderWechatDTO);

        // Configure WxStoreDineInOrderClientService.cancelOrder(...).
        final CancelOrderReqDTO cancelOrderReqDTO = new CancelOrderReqDTO();
        cancelOrderReqDTO.setDeviceType(0);
        cancelOrderReqDTO.setDeviceId("openId");
        cancelOrderReqDTO.setEnterpriseGuid("enterpriseGuid");
        cancelOrderReqDTO.setStoreGuid("storeGuid");
        cancelOrderReqDTO.setUserGuid("openId");
        cancelOrderReqDTO.setUserName("nickName");
        cancelOrderReqDTO.setOrderGuid("orderGuid");
        cancelOrderReqDTO.setReason("过期做废");
        cancelOrderReqDTO.setTable(false);
        cancelOrderReqDTO.setFastFood(false);
        when(mockWxStoreDineInOrderClientService.cancelOrder(cancelOrderReqDTO)).thenReturn(false);

        // Run the test
        final Boolean result = wxOrderRecordServiceImplUnderTest.dealFastOrderTimeOut(
                "40fbd1ac-f5f3-4f1c-a2a5-4bf1d77edd8a");

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testDealFastOrderTimeOut_WxUserRecordServiceReturnsNull() {
        // Setup
        when(mockWxUserRecordService.getById("guid")).thenReturn(null);

        // Run the test
        final Boolean result = wxOrderRecordServiceImplUnderTest.dealFastOrderTimeOut(
                "40fbd1ac-f5f3-4f1c-a2a5-4bf1d77edd8a");

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testDealFastOrderTimeOut_WxStoreWeChatOrderClientServiceReturnsNull() {
        // Setup
        // Configure WxUserRecordService.getById(...).
        final WxUserRecordDO wxUserRecordDO = new WxUserRecordDO(0L, "guid", "openId", "nickName", "headImgUrl", 0,
                "country", "province", "city", "phone", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid", 0);
        when(mockWxUserRecordService.getById("guid")).thenReturn(wxUserRecordDO);

        when(mockWxStoreWeChatOrderClientService.getOrder("orderGuid")).thenReturn(null);

        // Run the test
        final Boolean result = wxOrderRecordServiceImplUnderTest.dealFastOrderTimeOut(
                "40fbd1ac-f5f3-4f1c-a2a5-4bf1d77edd8a");

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testDealFastOrderTimeOut_WxStoreDineInOrderClientServiceReturnsTrue() {
        // Setup
        // Configure WxUserRecordService.getById(...).
        final WxUserRecordDO wxUserRecordDO = new WxUserRecordDO(0L, "guid", "openId", "nickName", "headImgUrl", 0,
                "country", "province", "city", "phone", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid", 0);
        when(mockWxUserRecordService.getById("guid")).thenReturn(wxUserRecordDO);

        // Configure WxStoreWeChatOrderClientService.getOrder(...).
        final OrderWechatDTO orderWechatDTO = new OrderWechatDTO();
        orderWechatDTO.setCheckoutStaffGuid("checkoutStaffGuid");
        orderWechatDTO.setCheckoutStaffName("checkoutStaffName");
        orderWechatDTO.setOrderGuid("orderGuid");
        orderWechatDTO.setState(0);
        orderWechatDTO.setDeviceType(0);
        when(mockWxStoreWeChatOrderClientService.getOrder("orderGuid")).thenReturn(orderWechatDTO);

        // Configure WxStoreDineInOrderClientService.cancelOrder(...).
        final CancelOrderReqDTO cancelOrderReqDTO = new CancelOrderReqDTO();
        cancelOrderReqDTO.setDeviceType(0);
        cancelOrderReqDTO.setDeviceId("openId");
        cancelOrderReqDTO.setEnterpriseGuid("enterpriseGuid");
        cancelOrderReqDTO.setStoreGuid("storeGuid");
        cancelOrderReqDTO.setUserGuid("openId");
        cancelOrderReqDTO.setUserName("nickName");
        cancelOrderReqDTO.setOrderGuid("orderGuid");
        cancelOrderReqDTO.setReason("过期做废");
        cancelOrderReqDTO.setTable(false);
        cancelOrderReqDTO.setFastFood(false);
        when(mockWxStoreDineInOrderClientService.cancelOrder(cancelOrderReqDTO)).thenReturn(true);

        when(mockWxStoreMerchantOrderService.update(any(LambdaUpdateWrapper.class))).thenReturn(false);

        // Run the test
        final Boolean result = wxOrderRecordServiceImplUnderTest.dealFastOrderTimeOut(
                "40fbd1ac-f5f3-4f1c-a2a5-4bf1d77edd8a");

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testDealFastOrderTimeOut_WxStoreMerchantOrderServiceReturnsTrue() {
        // Setup
        // Configure WxUserRecordService.getById(...).
        final WxUserRecordDO wxUserRecordDO = new WxUserRecordDO(0L, "guid", "openId", "nickName", "headImgUrl", 0,
                "country", "province", "city", "phone", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid", 0);
        when(mockWxUserRecordService.getById("guid")).thenReturn(wxUserRecordDO);

        // Configure WxStoreWeChatOrderClientService.getOrder(...).
        final OrderWechatDTO orderWechatDTO = new OrderWechatDTO();
        orderWechatDTO.setCheckoutStaffGuid("checkoutStaffGuid");
        orderWechatDTO.setCheckoutStaffName("checkoutStaffName");
        orderWechatDTO.setOrderGuid("orderGuid");
        orderWechatDTO.setState(0);
        orderWechatDTO.setDeviceType(0);
        when(mockWxStoreWeChatOrderClientService.getOrder("orderGuid")).thenReturn(orderWechatDTO);

        // Configure WxStoreDineInOrderClientService.cancelOrder(...).
        final CancelOrderReqDTO cancelOrderReqDTO = new CancelOrderReqDTO();
        cancelOrderReqDTO.setDeviceType(0);
        cancelOrderReqDTO.setDeviceId("openId");
        cancelOrderReqDTO.setEnterpriseGuid("enterpriseGuid");
        cancelOrderReqDTO.setStoreGuid("storeGuid");
        cancelOrderReqDTO.setUserGuid("openId");
        cancelOrderReqDTO.setUserName("nickName");
        cancelOrderReqDTO.setOrderGuid("orderGuid");
        cancelOrderReqDTO.setReason("过期做废");
        cancelOrderReqDTO.setTable(false);
        cancelOrderReqDTO.setFastFood(false);
        when(mockWxStoreDineInOrderClientService.cancelOrder(cancelOrderReqDTO)).thenReturn(true);

        when(mockWxStoreMerchantOrderService.update(any(LambdaUpdateWrapper.class))).thenReturn(true);

        // Run the test
        final Boolean result = wxOrderRecordServiceImplUnderTest.dealFastOrderTimeOut(
                "40fbd1ac-f5f3-4f1c-a2a5-4bf1d77edd8a");

        // Verify the results
        assertTrue(result);
    }

    @Test
    public void testInitialRecord1() {
        // Setup
        final WxQrCodeInfoDO wxQrCodeInfoDO = new WxQrCodeInfoDO(0L, "e7775879-a69e-4b65-8438-d81abbea7e06",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "storeGuid", "storeName",
                "areaGuid", "areaName", "diningTableGuid", "tableName", "brandGuid", 0, "appId");
        when(mockRedisUtils.generatdDTOGuid(WxOrderRecordDO.class)).thenReturn("e19e50b7-9e82-41f9-b8da-7274e1a1cc20");
        when(mockRedisUtils.setNx("key", "e19e50b7-9e82-41f9-b8da-7274e1a1cc20", 60L)).thenReturn(false);
        when(mockRedisUtils.get("key")).thenReturn("result");

        // Run the test
        wxOrderRecordServiceImplUnderTest.initialRecord(wxQrCodeInfoDO);

        // Verify the results
    }

    @Test
    public void testInitialRecord1_RedisUtilsSetNxReturnsTrue() {
        // Setup
        final WxQrCodeInfoDO wxQrCodeInfoDO = new WxQrCodeInfoDO(0L, "e7775879-a69e-4b65-8438-d81abbea7e06",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "storeGuid", "storeName",
                "areaGuid", "areaName", "diningTableGuid", "tableName", "brandGuid", 0, "appId");
        when(mockRedisUtils.generatdDTOGuid(WxOrderRecordDO.class)).thenReturn("e19e50b7-9e82-41f9-b8da-7274e1a1cc20");
        when(mockRedisUtils.setNx("key", "e19e50b7-9e82-41f9-b8da-7274e1a1cc20", 60L)).thenReturn(true);

        // Configure WxStoreDineInOrderClientService.getOrder(...).
        final OrderWechatDTO orderWechatDTO = new OrderWechatDTO();
        orderWechatDTO.setCheckoutStaffGuid("checkoutStaffGuid");
        orderWechatDTO.setCheckoutStaffName("checkoutStaffName");
        orderWechatDTO.setOrderGuid("orderGuid");
        orderWechatDTO.setState(0);
        orderWechatDTO.setDeviceType(0);
        when(mockWxStoreDineInOrderClientService.getOrder("orderGuid")).thenReturn(orderWechatDTO);

        when(mockRedisUtils.delete("key")).thenReturn(false);

        // Run the test
        wxOrderRecordServiceImplUnderTest.initialRecord(wxQrCodeInfoDO);

        // Verify the results
    }

    @Test
    public void testInitialRecord1_WxStoreDineInOrderClientServiceReturnsNull() {
        // Setup
        final WxQrCodeInfoDO wxQrCodeInfoDO = new WxQrCodeInfoDO(0L, "e7775879-a69e-4b65-8438-d81abbea7e06",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "storeGuid", "storeName",
                "areaGuid", "areaName", "diningTableGuid", "tableName", "brandGuid", 0, "appId");
        when(mockRedisUtils.generatdDTOGuid(WxOrderRecordDO.class)).thenReturn("e19e50b7-9e82-41f9-b8da-7274e1a1cc20");
        when(mockRedisUtils.setNx("key", "e19e50b7-9e82-41f9-b8da-7274e1a1cc20", 60L)).thenReturn(true);
        when(mockWxStoreDineInOrderClientService.getOrder("orderGuid")).thenReturn(null);
        when(mockRedisUtils.delete("key")).thenReturn(false);

        // Run the test
        wxOrderRecordServiceImplUnderTest.initialRecord(wxQrCodeInfoDO);

        // Verify the results
    }

    @Test
    public void testInitialRecord2() {
        // Setup
        final WxMemberSessionDTO wxMemberSessionDTO = WxMemberSessionDTO.builder()
                .brandGuid("brandGuid")
                .brandName("brandName")
                .logUrl("brandLogo")
                .storeGuid("storeGuid")
                .storeName("storeName")
                .diningTableGuid("diningTableGuid")
                .diningTableCode("diningTableCode")
                .areaGuid("areaGuid")
                .areaName("areaName")
                .build();
        when(mockRedisUtils.generatdDTOGuid(WxOrderRecordDO.class)).thenReturn("e19e50b7-9e82-41f9-b8da-7274e1a1cc20");
        when(mockRedisUtils.setNx("key", "e19e50b7-9e82-41f9-b8da-7274e1a1cc20", 60L)).thenReturn(false);
        when(mockRedisUtils.get("key")).thenReturn("result");

        // Run the test
        final String result = wxOrderRecordServiceImplUnderTest.initialRecord(wxMemberSessionDTO);

        // Verify the results
        assertEquals("e19e50b7-9e82-41f9-b8da-7274e1a1cc20", result);
    }

    @Test
    public void testInitialRecord2_RedisUtilsSetNxReturnsTrue() {
        // Setup
        final WxMemberSessionDTO wxMemberSessionDTO = WxMemberSessionDTO.builder()
                .brandGuid("brandGuid")
                .brandName("brandName")
                .logUrl("brandLogo")
                .storeGuid("storeGuid")
                .storeName("storeName")
                .diningTableGuid("diningTableGuid")
                .diningTableCode("diningTableCode")
                .areaGuid("areaGuid")
                .areaName("areaName")
                .build();
        when(mockRedisUtils.generatdDTOGuid(WxOrderRecordDO.class)).thenReturn("e19e50b7-9e82-41f9-b8da-7274e1a1cc20");
        when(mockRedisUtils.setNx("key", "e19e50b7-9e82-41f9-b8da-7274e1a1cc20", 60L)).thenReturn(true);

        // Configure WxStoreDineInOrderClientService.getOrder(...).
        final OrderWechatDTO orderWechatDTO = new OrderWechatDTO();
        orderWechatDTO.setCheckoutStaffGuid("checkoutStaffGuid");
        orderWechatDTO.setCheckoutStaffName("checkoutStaffName");
        orderWechatDTO.setOrderGuid("orderGuid");
        orderWechatDTO.setState(0);
        orderWechatDTO.setDeviceType(0);
        when(mockWxStoreDineInOrderClientService.getOrder("orderGuid")).thenReturn(orderWechatDTO);

        when(mockRedisUtils.delete("key")).thenReturn(false);

        // Run the test
        final String result = wxOrderRecordServiceImplUnderTest.initialRecord(wxMemberSessionDTO);

        // Verify the results
        assertEquals("e19e50b7-9e82-41f9-b8da-7274e1a1cc20", result);
    }

    @Test
    public void testInitialRecord2_WxStoreDineInOrderClientServiceReturnsNull() {
        // Setup
        final WxMemberSessionDTO wxMemberSessionDTO = WxMemberSessionDTO.builder()
                .brandGuid("brandGuid")
                .brandName("brandName")
                .logUrl("brandLogo")
                .storeGuid("storeGuid")
                .storeName("storeName")
                .diningTableGuid("diningTableGuid")
                .diningTableCode("diningTableCode")
                .areaGuid("areaGuid")
                .areaName("areaName")
                .build();
        when(mockRedisUtils.generatdDTOGuid(WxOrderRecordDO.class)).thenReturn("e19e50b7-9e82-41f9-b8da-7274e1a1cc20");
        when(mockRedisUtils.setNx("key", "e19e50b7-9e82-41f9-b8da-7274e1a1cc20", 60L)).thenReturn(true);
        when(mockWxStoreDineInOrderClientService.getOrder("orderGuid")).thenReturn(null);
        when(mockRedisUtils.delete("key")).thenReturn(false);

        // Run the test
        final String result = wxOrderRecordServiceImplUnderTest.initialRecord(wxMemberSessionDTO);

        // Verify the results
        assertEquals("e19e50b7-9e82-41f9-b8da-7274e1a1cc20", result);
    }

    @Test
    public void testPayBackOrder() {
        // Setup
        final PayBackOrderRecordDTO payBackOrderRecordDTO = new PayBackOrderRecordDTO(
                "0440de4f-507d-417c-b842-620928dcb048", "orderGuid", "merchantGuid", "guid", "brandGuid", "brandName",
                "logUrl", "storeGuid", "storeName", "areaGuid", "areaName", "tableGuid", "tableCode", 0, 0,
                "orderStateName", new BigDecimal("0.00"), new BigDecimal("0.00"), false, "memberInfoCardGuid",
                "volumeCode", "itemName", LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Configure WxUserRecordService.getOneByOpenId(...).
        final WxUserRecordDO wxUserRecordDO = new WxUserRecordDO(0L, "guid", "openId", "nickName", "headImgUrl", 0,
                "country", "province", "city", "phone", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid", 0);
        when(mockWxUserRecordService.getOneByOpenId("openId")).thenReturn(wxUserRecordDO);

        when(mockRedisUtils.get("key")).thenReturn("result");

        // Run the test
        wxOrderRecordServiceImplUnderTest.payBackOrder(payBackOrderRecordDTO);

        // Verify the results
        // Confirm WxStoreMerchantOrderService.pushOrderMsg(...).
        final WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO.setGuid("guid");
        wxStoreMerchantOrderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setTotalPrice(new BigDecimal("0.00"));
        wxStoreMerchantOrderDTO.setOpenId("openId");
        verify(mockWxStoreMerchantOrderService).pushOrderMsg(wxStoreMerchantOrderDTO);
    }

    @Test(expected = BusinessException.class)
    public void testPayBackOrder_WxUserRecordServiceReturnsNull() {
        // Setup
        final PayBackOrderRecordDTO payBackOrderRecordDTO = new PayBackOrderRecordDTO(
                "0440de4f-507d-417c-b842-620928dcb048", "orderGuid", "merchantGuid", "guid", "brandGuid", "brandName",
                "logUrl", "storeGuid", "storeName", "areaGuid", "areaName", "tableGuid", "tableCode", 0, 0,
                "orderStateName", new BigDecimal("0.00"), new BigDecimal("0.00"), false, "memberInfoCardGuid",
                "volumeCode", "itemName", LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockWxUserRecordService.getOneByOpenId("openId")).thenReturn(null);

        // Run the test
        wxOrderRecordServiceImplUnderTest.payBackOrder(payBackOrderRecordDTO);
    }

    @Test
    public void testPayBackOrder_RedisUtilsReturnsNull() {
        // Setup
        final PayBackOrderRecordDTO payBackOrderRecordDTO = new PayBackOrderRecordDTO(
                "0440de4f-507d-417c-b842-620928dcb048", "orderGuid", "merchantGuid", "guid", "brandGuid", "brandName",
                "logUrl", "storeGuid", "storeName", "areaGuid", "areaName", "tableGuid", "tableCode", 0, 0,
                "orderStateName", new BigDecimal("0.00"), new BigDecimal("0.00"), false, "memberInfoCardGuid",
                "volumeCode", "itemName", LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Configure WxUserRecordService.getOneByOpenId(...).
        final WxUserRecordDO wxUserRecordDO = new WxUserRecordDO(0L, "guid", "openId", "nickName", "headImgUrl", 0,
                "country", "province", "city", "phone", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid", 0);
        when(mockWxUserRecordService.getOneByOpenId("openId")).thenReturn(wxUserRecordDO);

        when(mockRedisUtils.get("key")).thenReturn(null);

        // Run the test
        wxOrderRecordServiceImplUnderTest.payBackOrder(payBackOrderRecordDTO);

        // Verify the results
    }

    @Test
    public void testSendUnMemberMessage1() {
        // Setup
        final DineinOrderDetailRespDTO orderDetails = new DineinOrderDetailRespDTO();
        orderDetails.setGuid("orderGuid");
        orderDetails.setTradeMode(0);
        orderDetails.setOrderFee(new BigDecimal("0.00"));
        orderDetails.setActuallyPayFee(new BigDecimal("0.00"));
        orderDetails.setDiscountFee(new BigDecimal("0.00"));
        orderDetails.setOrderNo("orderNo");
        orderDetails.setState(0);
        orderDetails.setStateName("orderStateName");
        orderDetails.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDetails.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setItemName("itemName");
        dineInItemDTO.setParentItemGuid(0L);
        orderDetails.setDineInItemDTOS(Arrays.asList(dineInItemDTO));

        final WxOrderRecordDO wxOrderRecordDO = new WxOrderRecordDO(0L, "e19e50b7-9e82-41f9-b8da-7274e1a1cc20",
                "orderGuid", "merchantGuid", "guid", "brandGuid", "brandName", "brandLogo", "storeGuid", "storeName",
                "areaGuid", "areaName", "diningTableGuid", "diningTableCode", 0, 0, "orderStateName",
                new BigDecimal("0.00"), new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName",
                "orderHolderNo", LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0);
        when(mockWeChatConfig.getMEMBER_ORDER_PAGE()).thenReturn("result");

        // Run the test
        wxOrderRecordServiceImplUnderTest.sendUnMemberMessage(orderDetails, wxOrderRecordDO, "openId");

        // Verify the results
        verify(mockWxOpenMessageService).sendMemberMsgNew(WxMemberConsumeMsgDTO.builder()
                .openId("openId")
                .brandGuid("brandGuid")
                .storeGuid("storeGuid")
                .orderGuid("e19e50b7-9e82-41f9-b8da-7274e1a1cc20")
                .first("first")
                .remark("remark")
                .url("url")
                .wxTemplateTypeEnum(WxTemplateTypeEnum.QUEUE_TEMPLATE)
                .build());
    }

    @Test
    public void testSendUnMemberMessage2() {
        // Setup
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("orderGuid");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setOrderNo("orderNo");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setStateName("orderStateName");
        dineinOrderDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setItemName("itemName");
        dineInItemDTO.setParentItemGuid(0L);
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        final UnMemberMessageDTO unMemberMessageDTO = new UnMemberMessageDTO(dineinOrderDetailRespDTO,
                "orderRecordGuid", "openId");
        when(mockWeChatConfig.getMEMBER_ORDER_PAGE()).thenReturn("result");

        // Run the test
        wxOrderRecordServiceImplUnderTest.sendUnMemberMessage(unMemberMessageDTO);

        // Verify the results
        verify(mockWxOpenMessageService).sendMemberMsgNew(WxMemberConsumeMsgDTO.builder()
                .openId("openId")
                .brandGuid("brandGuid")
                .storeGuid("storeGuid")
                .orderGuid("e19e50b7-9e82-41f9-b8da-7274e1a1cc20")
                .first("first")
                .remark("remark")
                .url("url")
                .wxTemplateTypeEnum(WxTemplateTypeEnum.QUEUE_TEMPLATE)
                .build());
    }

    @Test
    public void testGetMerchantOrderPhone() {
        // Setup
        final WxStoreMerchantOrderDTO expectedResult = new WxStoreMerchantOrderDTO();
        expectedResult.setGuid("guid");
        expectedResult.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setTotalPrice(new BigDecimal("0.00"));
        expectedResult.setOpenId("openId");

        // Configure WxOrderRecordMapper.selectOne(...).
        final WxOrderRecordDO wxOrderRecordDO = new WxOrderRecordDO(0L, "e19e50b7-9e82-41f9-b8da-7274e1a1cc20",
                "orderGuid", "merchantGuid", "guid", "brandGuid", "brandName", "brandLogo", "storeGuid", "storeName",
                "areaGuid", "areaName", "diningTableGuid", "diningTableCode", 0, 0, "orderStateName",
                new BigDecimal("0.00"), new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName",
                "orderHolderNo", LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0);
        when(mockWxOrderRecordMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(wxOrderRecordDO);

        // Configure WxStoreMerchantOrderMapper.selectOne(...).
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO.setId(0L);
        wxStoreMerchantOrderDO.setGuid("963027fe-0d4f-4378-91d7-5b54e7af4771");
        wxStoreMerchantOrderDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDO.setOrderState(0);
        wxStoreMerchantOrderDO.setOrderRecordGuid("orderRecordGuid");
        when(mockWxStoreMerchantOrderMapper.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(wxStoreMerchantOrderDO);

        // Configure WxStoreMerchantOrderMapstruct.getWxStoreMerchantOrder(...).
        final WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO.setGuid("guid");
        wxStoreMerchantOrderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setTotalPrice(new BigDecimal("0.00"));
        wxStoreMerchantOrderDTO.setOpenId("openId");
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO1 = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO1.setId(0L);
        wxStoreMerchantOrderDO1.setGuid("963027fe-0d4f-4378-91d7-5b54e7af4771");
        wxStoreMerchantOrderDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDO1.setOrderState(0);
        wxStoreMerchantOrderDO1.setOrderRecordGuid("orderRecordGuid");
        when(mockWxStoreMerchantOrderMapstruct.getWxStoreMerchantOrder(wxStoreMerchantOrderDO1))
                .thenReturn(wxStoreMerchantOrderDTO);

        // Run the test
        final WxStoreMerchantOrderDTO result = wxOrderRecordServiceImplUnderTest.getMerchantOrderPhone("orderGuid");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetByOrderHolderNo() {
        // Setup
        final WxOrderRecordDO expectedResult = new WxOrderRecordDO(0L, "e19e50b7-9e82-41f9-b8da-7274e1a1cc20",
                "orderGuid", "merchantGuid", "guid", "brandGuid", "brandName", "brandLogo", "storeGuid", "storeName",
                "areaGuid", "areaName", "diningTableGuid", "diningTableCode", 0, 0, "orderStateName",
                new BigDecimal("0.00"), new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName",
                "orderHolderNo", LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0);

        // Run the test
        final WxOrderRecordDO result = wxOrderRecordServiceImplUnderTest.getByOrderHolderNo("orderHolderNo");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testListByOrderGuid() {
        // Setup
        final SingleDataDTO query = SingleDataDTO.builder()
                .data("data")
                .datas(Arrays.asList("value"))
                .build();
        final WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO.setGuid("guid");
        wxStoreMerchantOrderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setTotalPrice(new BigDecimal("0.00"));
        wxStoreMerchantOrderDTO.setOpenId("openId");
        final List<WxStoreMerchantOrderDTO> expectedResult = Arrays.asList(wxStoreMerchantOrderDTO);

        // Configure WxOrderRecordMapper.selectList(...).
        final List<WxOrderRecordDO> wxOrderRecordDOS = Arrays.asList(
                new WxOrderRecordDO(0L, "e19e50b7-9e82-41f9-b8da-7274e1a1cc20", "orderGuid", "merchantGuid", "guid",
                        "brandGuid", "brandName", "brandLogo", "storeGuid", "storeName", "areaGuid", "areaName",
                        "diningTableGuid", "diningTableCode", 0, 0, "orderStateName", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName", "orderHolderNo",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0));
        when(mockWxOrderRecordMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(wxOrderRecordDOS);

        // Configure WxStoreMerchantOrderMapper.selectList(...).
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO.setId(0L);
        wxStoreMerchantOrderDO.setGuid("963027fe-0d4f-4378-91d7-5b54e7af4771");
        wxStoreMerchantOrderDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDO.setOrderState(0);
        wxStoreMerchantOrderDO.setOrderRecordGuid("orderRecordGuid");
        final List<WxStoreMerchantOrderDO> wxStoreMerchantOrderDOS = Arrays.asList(wxStoreMerchantOrderDO);
        when(mockWxStoreMerchantOrderMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(wxStoreMerchantOrderDOS);

        // Configure WxStoreMerchantOrderMapstruct.getWxStoreMerchantOrder(...).
        final WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO1 = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO1.setGuid("guid");
        wxStoreMerchantOrderDTO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO1.setTotalPrice(new BigDecimal("0.00"));
        wxStoreMerchantOrderDTO1.setOpenId("openId");
        final List<WxStoreMerchantOrderDTO> wxStoreMerchantOrderDTOS = Arrays.asList(wxStoreMerchantOrderDTO1);
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO1 = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO1.setId(0L);
        wxStoreMerchantOrderDO1.setGuid("963027fe-0d4f-4378-91d7-5b54e7af4771");
        wxStoreMerchantOrderDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDO1.setOrderState(0);
        wxStoreMerchantOrderDO1.setOrderRecordGuid("orderRecordGuid");
        final List<WxStoreMerchantOrderDO> wxStoreMerchantOrderDOS1 = Arrays.asList(wxStoreMerchantOrderDO1);
        when(mockWxStoreMerchantOrderMapstruct.getWxStoreMerchantOrder(wxStoreMerchantOrderDOS1))
                .thenReturn(wxStoreMerchantOrderDTOS);

        // Run the test
        final List<WxStoreMerchantOrderDTO> result = wxOrderRecordServiceImplUnderTest.listByOrderGuid(query);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testListByOrderGuid_WxOrderRecordMapperReturnsNoItems() {
        // Setup
        final SingleDataDTO query = SingleDataDTO.builder()
                .data("data")
                .datas(Arrays.asList("value"))
                .build();
        when(mockWxOrderRecordMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<WxStoreMerchantOrderDTO> result = wxOrderRecordServiceImplUnderTest.listByOrderGuid(query);

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testListByOrderGuid_WxStoreMerchantOrderMapperReturnsNoItems() {
        // Setup
        final SingleDataDTO query = SingleDataDTO.builder()
                .data("data")
                .datas(Arrays.asList("value"))
                .build();

        // Configure WxOrderRecordMapper.selectList(...).
        final List<WxOrderRecordDO> wxOrderRecordDOS = Arrays.asList(
                new WxOrderRecordDO(0L, "e19e50b7-9e82-41f9-b8da-7274e1a1cc20", "orderGuid", "merchantGuid", "guid",
                        "brandGuid", "brandName", "brandLogo", "storeGuid", "storeName", "areaGuid", "areaName",
                        "diningTableGuid", "diningTableCode", 0, 0, "orderStateName", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName", "orderHolderNo",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0));
        when(mockWxOrderRecordMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(wxOrderRecordDOS);

        when(mockWxStoreMerchantOrderMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<WxStoreMerchantOrderDTO> result = wxOrderRecordServiceImplUnderTest.listByOrderGuid(query);

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testListByOrderGuid_WxStoreMerchantOrderMapstructReturnsNoItems() {
        // Setup
        final SingleDataDTO query = SingleDataDTO.builder()
                .data("data")
                .datas(Arrays.asList("value"))
                .build();

        // Configure WxOrderRecordMapper.selectList(...).
        final List<WxOrderRecordDO> wxOrderRecordDOS = Arrays.asList(
                new WxOrderRecordDO(0L, "e19e50b7-9e82-41f9-b8da-7274e1a1cc20", "orderGuid", "merchantGuid", "guid",
                        "brandGuid", "brandName", "brandLogo", "storeGuid", "storeName", "areaGuid", "areaName",
                        "diningTableGuid", "diningTableCode", 0, 0, "orderStateName", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName", "orderHolderNo",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0));
        when(mockWxOrderRecordMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(wxOrderRecordDOS);

        // Configure WxStoreMerchantOrderMapper.selectList(...).
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO.setId(0L);
        wxStoreMerchantOrderDO.setGuid("963027fe-0d4f-4378-91d7-5b54e7af4771");
        wxStoreMerchantOrderDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDO.setOrderState(0);
        wxStoreMerchantOrderDO.setOrderRecordGuid("orderRecordGuid");
        final List<WxStoreMerchantOrderDO> wxStoreMerchantOrderDOS = Arrays.asList(wxStoreMerchantOrderDO);
        when(mockWxStoreMerchantOrderMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(wxStoreMerchantOrderDOS);

        // Configure WxStoreMerchantOrderMapstruct.getWxStoreMerchantOrder(...).
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO1 = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO1.setId(0L);
        wxStoreMerchantOrderDO1.setGuid("963027fe-0d4f-4378-91d7-5b54e7af4771");
        wxStoreMerchantOrderDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDO1.setOrderState(0);
        wxStoreMerchantOrderDO1.setOrderRecordGuid("orderRecordGuid");
        final List<WxStoreMerchantOrderDO> wxStoreMerchantOrderDOS1 = Arrays.asList(wxStoreMerchantOrderDO1);
        when(mockWxStoreMerchantOrderMapstruct.getWxStoreMerchantOrder(wxStoreMerchantOrderDOS1))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<WxStoreMerchantOrderDTO> result = wxOrderRecordServiceImplUnderTest.listByOrderGuid(query);

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }
}
