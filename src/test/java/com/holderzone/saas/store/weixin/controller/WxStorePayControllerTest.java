package com.holderzone.saas.store.weixin.controller;

import com.holderzone.saas.store.dto.common.UserInfoDTO;
import com.holderzone.saas.store.dto.pay.AggPayPollingRespDTO;
import com.holderzone.saas.store.dto.pay.SaasNotifyDTO;
import com.holderzone.saas.store.dto.weixin.WxPrepayRespDTO;
import com.holderzone.saas.store.dto.weixin.WxZeroPayReqDTO;
import com.holderzone.saas.store.dto.weixin.deal.WeChatH5PayReqDTO;
import com.holderzone.saas.store.dto.weixin.member.*;
import com.holderzone.saas.store.dto.weixin.req.WxH5PayReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxMemberModeNotifyReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxMemberTradeNotifyReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxPrepayReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxPayRespDTO;
import com.holderzone.saas.store.weixin.entity.dto.WxPayCallbackDTO;
import com.holderzone.saas.store.weixin.event.NotifyMessageQueue;
import com.holderzone.saas.store.weixin.service.WxStorePayService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(WxStorePayController.class)
public class WxStorePayControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private WxStorePayService mockWxStorePayService;
    @MockBean
    private NotifyMessageQueue mockNotifyMessageQueue;

    @Test
    public void testWeChatPublic() throws Exception {
        // Setup
        when(mockWxStorePayService.weChatPublic(WxH5PayReqDTO.builder().build()))
                .thenReturn(WxPayRespDTO.builder().build());

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_pay/wechat/public")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testWeChatPublic_WxStorePayServiceReturnsFailure() throws Exception {
        // Setup
        when(mockWxStorePayService.weChatPublic(WxH5PayReqDTO.builder().build()))
                .thenReturn(WxPayRespDTO.changeFailed());

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_pay/wechat/public")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testWeChatPublic_WxStorePayServiceThrowsUnsupportedEncodingException() throws Exception {
        // Setup
        when(mockWxStorePayService.weChatPublic(WxH5PayReqDTO.builder().build()))
                .thenThrow(UnsupportedEncodingException.class);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_pay/wechat/public")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().is5xxServerError())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testWeChatPay() throws Exception {
        // Setup
        when(mockWxStorePayService.weChatPay(
                new WeChatH5PayReqDTO("orderGuid", "outNotifyUrl", 0, new BigDecimal("0.00"), false, "appId",
                        "clientIp", false, "",""))).thenReturn(WxPayRespDTO.builder().build());

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_pay/wechat_pay")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testWeChatPay_WxStorePayServiceReturnsFailure() throws Exception {
        // Setup
        when(mockWxStorePayService.weChatPay(
                new WeChatH5PayReqDTO("orderGuid", "outNotifyUrl", 0, new BigDecimal("0.00"), false, "appId",
                        "clientIp", false,"",""))).thenReturn(WxPayRespDTO.changeFailed());

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_pay/wechat_pay")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testWxResultOperation() throws Exception {
        // Setup
        // Configure WxStorePayService.wxResultOperation(...).
        final SaasNotifyDTO saasNotifyDTO = new SaasNotifyDTO();
        final AggPayPollingRespDTO aggPayPollingRespDTO = new AggPayPollingRespDTO();
        aggPayPollingRespDTO.setCode("code");
        aggPayPollingRespDTO.setMsg("msg");
        aggPayPollingRespDTO.setAmount(new BigDecimal("0.00"));
        aggPayPollingRespDTO.setSubject("subject");
        saasNotifyDTO.setAggPayPollingRespDTO(aggPayPollingRespDTO);
        final WxPayCallbackDTO wxStoreCallbackNotifyDTO = new WxPayCallbackDTO("storeGuid", "openId", saasNotifyDTO);
        when(mockWxStorePayService.wxResultOperation(wxStoreCallbackNotifyDTO)).thenReturn("result");

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_pay/result_operation")
                        .param("openId", "openId")
                        .param("storeGuid", "storeGuid")
                        .param("allianceId", "allianceId")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testAggPayResultOperation() throws Exception {
        // Setup
        // Configure WxStorePayService.wxResultOperation(...).
        final SaasNotifyDTO saasNotifyDTO = new SaasNotifyDTO();
        final AggPayPollingRespDTO aggPayPollingRespDTO = new AggPayPollingRespDTO();
        aggPayPollingRespDTO.setCode("code");
        aggPayPollingRespDTO.setMsg("msg");
        aggPayPollingRespDTO.setAmount(new BigDecimal("0.00"));
        aggPayPollingRespDTO.setSubject("subject");
        saasNotifyDTO.setAggPayPollingRespDTO(aggPayPollingRespDTO);
        final WxPayCallbackDTO wxStoreCallbackNotifyDTO = new WxPayCallbackDTO("storeGuid", "openId", saasNotifyDTO);
        when(mockWxStorePayService.wxResultOperation(wxStoreCallbackNotifyDTO)).thenReturn("result");

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_pay/result_agg_pay")
                        .param("openId", "openId")
                        .param("storeGuid", "storeGuid")
                        .param("allianceId", "allianceId")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testMemberPay() throws Exception {
        // Setup
        when(mockWxStorePayService.memberPay(WxMemberPayDTO.builder().build()))
                .thenReturn(WxStorePayResultDTO.builder().build());

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_pay/member_pay")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testMemberPay_WxStorePayServiceThrowsException() throws Exception {
        // Setup
        when(mockWxStorePayService.memberPay(WxMemberPayDTO.builder().build())).thenThrow(Exception.class);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_pay/member_pay")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().is5xxServerError())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testSendWeixinNotifyMessage() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_pay/sendWeixinNotifyMessage")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
        verify(mockNotifyMessageQueue).add(WxMemberModeNotifyReqDTO.builder()
                .remainingTime(0L)
                .userInfoDTO(UserInfoDTO.builder()
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .allianceId("allianceId")
                        .source("source")
                        .build())
                .wxMemberTradeNotifyReqDTO(WxMemberTradeNotifyReqDTO.builder().build())
                .build());
    }

    @Test
    public void testGetAllPayWay() throws Exception {
        // Setup
        when(mockWxStorePayService.getAllPayWay(WxStorePayReqDTO.builder().build()))
                .thenReturn(WxPayWayRespDTO.builder().build());

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_pay/pay_way")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testPrepay() throws Exception {
        // Setup
        when(mockWxStorePayService.prepay(WxPrepayReqDTO.builder().build()))
                .thenReturn(WxPrepayRespDTO.builder().build());

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_pay/prepay")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testPrepay_WxStorePayServiceReturnsFailure() throws Exception {
        // Setup
        when(mockWxStorePayService.prepay(WxPrepayReqDTO.builder().build())).thenReturn(WxPrepayRespDTO.changeFailed());

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_pay/prepay")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testValidateOrder() throws Exception {
        // Setup
        when(mockWxStorePayService.validateOrder(WxStorePayReqDTO.builder().build()))
                .thenReturn(WxPrepayRespDTO.builder().build());

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_pay/validate_order")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testValidateOrder_WxStorePayServiceReturnsFailure() throws Exception {
        // Setup
        when(mockWxStorePayService.validateOrder(WxStorePayReqDTO.builder().build()))
                .thenReturn(WxPrepayRespDTO.changeFailed());

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_pay/validate_order")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testMemberConfirm() throws Exception {
        // Setup
        when(mockWxStorePayService.memberConfirm(WxPrepayConfirmReqDTO.builder().build()))
                .thenReturn(WxPrepayConfirmRespDTO.builder().build());

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_pay/prepay_confirm")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testZeroPay() throws Exception {
        // Setup
        when(mockWxStorePayService.zeroPay(WxZeroPayReqDTO.builder().build()))
                .thenReturn(WxStorePayResultDTO.builder().build());

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_pay/zero_pay")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }
}
