package com.holderzone.saas.store.weixin.service.impl;

import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.weixin.common.BusinessName;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.weixin.common.WxStoreInfoDTO;
import com.holderzone.saas.store.dto.weixin.req.WxOrderConfigUpdateBatchReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStorePageReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStoreReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxOrderConfigDTO;
import com.holderzone.saas.store.weixin.entity.domain.WxOrderConfigDO;
import com.holderzone.saas.store.weixin.mapstruct.WxStoreConfigMapstruct;
import com.holderzone.saas.store.weixin.service.WxConfigOverviewService;
import com.holderzone.saas.store.weixin.service.WxOrganizationService;
import com.holderzone.saas.store.weixin.service.rpc.OrganizationClientService;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static org.junit.Assert.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class WxStoreOrderConfigServiceImplTest {

    @Mock
    private WxStoreConfigMapstruct mockWxStoreConfigMapstruct;
    @Mock
    private OrganizationClientService mockOrganizationClientService;
    @Mock
    private WxOrganizationService mockWxOrganizationService;
    @Mock
    private WxConfigOverviewService mockWxConfigOverviewService;
    @Mock
    private RedisUtils mockRedisUtils;

    private WxStoreOrderConfigServiceImpl wxStoreOrderConfigServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        wxStoreOrderConfigServiceImplUnderTest = new WxStoreOrderConfigServiceImpl();
        wxStoreOrderConfigServiceImplUnderTest.wxStoreConfigMapstruct = mockWxStoreConfigMapstruct;
        wxStoreOrderConfigServiceImplUnderTest.organizationClientService = mockOrganizationClientService;
        wxStoreOrderConfigServiceImplUnderTest.wxOrganizationService = mockWxOrganizationService;
        wxStoreOrderConfigServiceImplUnderTest.wxConfigOverviewService = mockWxConfigOverviewService;
        wxStoreOrderConfigServiceImplUnderTest.redisUtils = mockRedisUtils;
    }

    @Test
    public void testPageOrderConfig() {
        // Setup
        final WxStorePageReqDTO wxStorePageReqDTO = WxStorePageReqDTO.builder().build();

        // Configure WxOrganizationService.getStoreConfig(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("1cd66a2f-a7a7-4cbe-b747-f4863e1fc43f");
        storeDTO.setName("name");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setName("name");
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        final StoreDTO storeDTO1 = new StoreDTO();
        storeDTO1.setGuid("1cd66a2f-a7a7-4cbe-b747-f4863e1fc43f");
        storeDTO1.setName("name");
        storeDTO1.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        final BrandDTO brandDTO1 = new BrandDTO();
        brandDTO1.setName("name");
        storeDTO1.setBrandDTOList(Arrays.asList(brandDTO1));
        final Triple<Page<StoreDTO>, List<StoreDTO>, List<String>> pageListListTriple = Triple.of(
                new Page<>(0L, 0L, Arrays.asList(storeDTO)), Arrays.asList(storeDTO1), Arrays.asList("value"));
        when(mockWxOrganizationService.getStoreConfig(WxStorePageReqDTO.builder().build()))
                .thenReturn(pageListListTriple);

        // Configure WxStoreConfigMapstruct.orderConfigDO2OrderConfigRespDTO(...).
        final WxOrderConfigDTO wxOrderConfigDTO = new WxOrderConfigDTO();
        wxOrderConfigDTO.setStoreGuid("storeGuid");
        wxOrderConfigDTO.setStoreName("name");
        wxOrderConfigDTO.setIsOpened(false);
        wxOrderConfigDTO.setBrandNameList(Arrays.asList("value"));
        wxOrderConfigDTO.setTagNames("tagNames");
        wxOrderConfigDTO.setAutoConfirm(0);
        wxOrderConfigDTO.setAutoConfirmTime(0L);
        wxOrderConfigDTO.setConfirmPromptTime(0L);
        wxOrderConfigDTO.setIsOnlyState(false);
        wxOrderConfigDTO.setPadBackgroundUrl("padBackgroundUrl");
        final WxOrderConfigDO wxOrderConfigDO = new WxOrderConfigDO();
        wxOrderConfigDO.setId(0L);
        wxOrderConfigDO.setGuid("a835db38-696a-4252-827c-50e08079adb1");
        wxOrderConfigDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxOrderConfigDO.setStoreGuid("storeGuid");
        wxOrderConfigDO.setPadBackgroundUrl("padBackgroundUrl");
        when(mockWxStoreConfigMapstruct.orderConfigDO2OrderConfigRespDTO(wxOrderConfigDO)).thenReturn(wxOrderConfigDTO);

        // Run the test
        final Page<WxOrderConfigDTO> result = wxStoreOrderConfigServiceImplUnderTest.pageOrderConfig(wxStorePageReqDTO);

        // Verify the results
    }

    @Test
    public void testSaveStoreConfig() {
        // Setup
        final WxStoreReqDTO wxStoreReqDTO = WxStoreReqDTO.builder()
                .guid("99983b84-590d-4710-9a74-477b36586bc7")
                .storeGuid("storeGuid")
                .build();
        when(mockRedisUtils.generateGuid("redisKey")).thenReturn("a835db38-696a-4252-827c-50e08079adb1");
        when(mockRedisUtils.keyGenerate(BusinessName.WX_STORE_CONFIG)).thenReturn("key");

        // Configure WxStoreConfigMapstruct.orderConfigDO2OrderConfigRespDTO(...).
        final WxOrderConfigDTO wxOrderConfigDTO = new WxOrderConfigDTO();
        wxOrderConfigDTO.setStoreGuid("storeGuid");
        wxOrderConfigDTO.setStoreName("name");
        wxOrderConfigDTO.setIsOpened(false);
        wxOrderConfigDTO.setBrandNameList(Arrays.asList("value"));
        wxOrderConfigDTO.setTagNames("tagNames");
        wxOrderConfigDTO.setAutoConfirm(0);
        wxOrderConfigDTO.setAutoConfirmTime(0L);
        wxOrderConfigDTO.setConfirmPromptTime(0L);
        wxOrderConfigDTO.setIsOnlyState(false);
        wxOrderConfigDTO.setPadBackgroundUrl("padBackgroundUrl");
        final WxOrderConfigDO wxOrderConfigDO = new WxOrderConfigDO();
        wxOrderConfigDO.setId(0L);
        wxOrderConfigDO.setGuid("a835db38-696a-4252-827c-50e08079adb1");
        wxOrderConfigDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxOrderConfigDO.setStoreGuid("storeGuid");
        wxOrderConfigDO.setPadBackgroundUrl("padBackgroundUrl");
        when(mockWxStoreConfigMapstruct.orderConfigDO2OrderConfigRespDTO(wxOrderConfigDO)).thenReturn(wxOrderConfigDTO);

        // Run the test
        final Boolean result = wxStoreOrderConfigServiceImplUnderTest.saveStoreConfig(wxStoreReqDTO);

        // Verify the results
        assertTrue(result);

        // Confirm RedisUtils.setEx(...).
        final WxOrderConfigDTO value = new WxOrderConfigDTO();
        value.setStoreGuid("storeGuid");
        value.setStoreName("name");
        value.setIsOpened(false);
        value.setBrandNameList(Arrays.asList("value"));
        value.setTagNames("tagNames");
        value.setAutoConfirm(0);
        value.setAutoConfirmTime(0L);
        value.setConfirmPromptTime(0L);
        value.setIsOnlyState(false);
        value.setPadBackgroundUrl("padBackgroundUrl");
        verify(mockRedisUtils).setEx("key", value, 24L, TimeUnit.HOURS);
    }

    @Test
    public void testGetDetailConfig() {
        // Setup
        final WxStoreReqDTO wxStoreReqDTO = WxStoreReqDTO.builder()
                .guid("99983b84-590d-4710-9a74-477b36586bc7")
                .storeGuid("storeGuid")
                .build();
        final WxOrderConfigDTO expectedResult = new WxOrderConfigDTO();
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setStoreName("name");
        expectedResult.setIsOpened(false);
        expectedResult.setBrandNameList(Arrays.asList("value"));
        expectedResult.setTagNames("tagNames");
        expectedResult.setAutoConfirm(0);
        expectedResult.setAutoConfirmTime(0L);
        expectedResult.setConfirmPromptTime(0L);
        expectedResult.setIsOnlyState(false);
        expectedResult.setPadBackgroundUrl("padBackgroundUrl");

        // Configure WxStoreConfigMapstruct.orderConfigDO2OrderConfigRespDTO(...).
        final WxOrderConfigDTO wxOrderConfigDTO = new WxOrderConfigDTO();
        wxOrderConfigDTO.setStoreGuid("storeGuid");
        wxOrderConfigDTO.setStoreName("name");
        wxOrderConfigDTO.setIsOpened(false);
        wxOrderConfigDTO.setBrandNameList(Arrays.asList("value"));
        wxOrderConfigDTO.setTagNames("tagNames");
        wxOrderConfigDTO.setAutoConfirm(0);
        wxOrderConfigDTO.setAutoConfirmTime(0L);
        wxOrderConfigDTO.setConfirmPromptTime(0L);
        wxOrderConfigDTO.setIsOnlyState(false);
        wxOrderConfigDTO.setPadBackgroundUrl("padBackgroundUrl");
        final WxOrderConfigDO wxOrderConfigDO = new WxOrderConfigDO();
        wxOrderConfigDO.setId(0L);
        wxOrderConfigDO.setGuid("a835db38-696a-4252-827c-50e08079adb1");
        wxOrderConfigDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxOrderConfigDO.setStoreGuid("storeGuid");
        wxOrderConfigDO.setPadBackgroundUrl("padBackgroundUrl");
        when(mockWxStoreConfigMapstruct.orderConfigDO2OrderConfigRespDTO(wxOrderConfigDO)).thenReturn(wxOrderConfigDTO);

        when(mockRedisUtils.keyGenerate(BusinessName.WX_STORE_CONFIG)).thenReturn("key");
        when(mockRedisUtils.get("key")).thenReturn("result");

        // Run the test
        final WxOrderConfigDTO result = wxStoreOrderConfigServiceImplUnderTest.getDetailConfig(wxStoreReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);

        // Confirm RedisUtils.setEx(...).
        final WxOrderConfigDTO value = new WxOrderConfigDTO();
        value.setStoreGuid("storeGuid");
        value.setStoreName("name");
        value.setIsOpened(false);
        value.setBrandNameList(Arrays.asList("value"));
        value.setTagNames("tagNames");
        value.setAutoConfirm(0);
        value.setAutoConfirmTime(0L);
        value.setConfirmPromptTime(0L);
        value.setIsOnlyState(false);
        value.setPadBackgroundUrl("padBackgroundUrl");
        verify(mockRedisUtils).setEx("key", value, 24L, TimeUnit.HOURS);
    }

    @Test
    public void testGetPadBackgroundUrl() {
        // Setup
        when(mockRedisUtils.keyGenerate(BusinessName.WX_STORE_CONFIG)).thenReturn("key");
        when(mockRedisUtils.get("key")).thenReturn("result");

        // Run the test
        final String result = wxStoreOrderConfigServiceImplUnderTest.getPadBackgroundUrl("storeGuid");

        // Verify the results
        assertEquals("padBackgroundUrl", result);
    }

    @Test
    public void testUpdateStoreConfig() {
        // Setup
        final WxOrderConfigDTO wxOrderConfigDTO = new WxOrderConfigDTO();
        wxOrderConfigDTO.setStoreGuid("storeGuid");
        wxOrderConfigDTO.setStoreName("name");
        wxOrderConfigDTO.setIsOpened(false);
        wxOrderConfigDTO.setBrandNameList(Arrays.asList("value"));
        wxOrderConfigDTO.setTagNames("tagNames");
        wxOrderConfigDTO.setAutoConfirm(0);
        wxOrderConfigDTO.setAutoConfirmTime(0L);
        wxOrderConfigDTO.setConfirmPromptTime(0L);
        wxOrderConfigDTO.setIsOnlyState(false);
        wxOrderConfigDTO.setPadBackgroundUrl("padBackgroundUrl");

        // Configure WxStoreConfigMapstruct.updateDTO2WxStoreOrderConfigDO(...).
        final WxOrderConfigDO wxOrderConfigDO = new WxOrderConfigDO();
        wxOrderConfigDO.setId(0L);
        wxOrderConfigDO.setGuid("a835db38-696a-4252-827c-50e08079adb1");
        wxOrderConfigDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxOrderConfigDO.setStoreGuid("storeGuid");
        wxOrderConfigDO.setPadBackgroundUrl("padBackgroundUrl");
        final WxOrderConfigDTO wxOrderConfigDTO1 = new WxOrderConfigDTO();
        wxOrderConfigDTO1.setStoreGuid("storeGuid");
        wxOrderConfigDTO1.setStoreName("name");
        wxOrderConfigDTO1.setIsOpened(false);
        wxOrderConfigDTO1.setBrandNameList(Arrays.asList("value"));
        wxOrderConfigDTO1.setTagNames("tagNames");
        wxOrderConfigDTO1.setAutoConfirm(0);
        wxOrderConfigDTO1.setAutoConfirmTime(0L);
        wxOrderConfigDTO1.setConfirmPromptTime(0L);
        wxOrderConfigDTO1.setIsOnlyState(false);
        wxOrderConfigDTO1.setPadBackgroundUrl("padBackgroundUrl");
        when(mockWxStoreConfigMapstruct.updateDTO2WxStoreOrderConfigDO(wxOrderConfigDTO1)).thenReturn(wxOrderConfigDO);

        // Configure WxStoreConfigMapstruct.orderConfigDO2OrderConfigRespDTO(...).
        final WxOrderConfigDTO wxOrderConfigDTO2 = new WxOrderConfigDTO();
        wxOrderConfigDTO2.setStoreGuid("storeGuid");
        wxOrderConfigDTO2.setStoreName("name");
        wxOrderConfigDTO2.setIsOpened(false);
        wxOrderConfigDTO2.setBrandNameList(Arrays.asList("value"));
        wxOrderConfigDTO2.setTagNames("tagNames");
        wxOrderConfigDTO2.setAutoConfirm(0);
        wxOrderConfigDTO2.setAutoConfirmTime(0L);
        wxOrderConfigDTO2.setConfirmPromptTime(0L);
        wxOrderConfigDTO2.setIsOnlyState(false);
        wxOrderConfigDTO2.setPadBackgroundUrl("padBackgroundUrl");
        final WxOrderConfigDO wxOrderConfigDO1 = new WxOrderConfigDO();
        wxOrderConfigDO1.setId(0L);
        wxOrderConfigDO1.setGuid("a835db38-696a-4252-827c-50e08079adb1");
        wxOrderConfigDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxOrderConfigDO1.setStoreGuid("storeGuid");
        wxOrderConfigDO1.setPadBackgroundUrl("padBackgroundUrl");
        when(mockWxStoreConfigMapstruct.orderConfigDO2OrderConfigRespDTO(wxOrderConfigDO1))
                .thenReturn(wxOrderConfigDTO2);

        when(mockRedisUtils.keyGenerate(BusinessName.WX_STORE_CONFIG)).thenReturn("key");

        // Run the test
        final Boolean result = wxStoreOrderConfigServiceImplUnderTest.updateStoreConfig(wxOrderConfigDTO);

        // Verify the results
        assertFalse(result);
        verify(mockWxConfigOverviewService).couldEdit(Arrays.asList("value"), 0);

        // Confirm RedisUtils.setEx(...).
        final WxOrderConfigDTO value = new WxOrderConfigDTO();
        value.setStoreGuid("storeGuid");
        value.setStoreName("name");
        value.setIsOpened(false);
        value.setBrandNameList(Arrays.asList("value"));
        value.setTagNames("tagNames");
        value.setAutoConfirm(0);
        value.setAutoConfirmTime(0L);
        value.setConfirmPromptTime(0L);
        value.setIsOnlyState(false);
        value.setPadBackgroundUrl("padBackgroundUrl");
        verify(mockRedisUtils).setEx("key", value, 24L, TimeUnit.HOURS);
    }

    @Test
    public void testUpdateBatchStoreConfig() {
        // Setup
        final WxOrderConfigUpdateBatchReqDTO wxOrderConfigUpdateBatchReqDTO = new WxOrderConfigUpdateBatchReqDTO();
        final WxOrderConfigDTO wxOrderConfigDTO = new WxOrderConfigDTO();
        wxOrderConfigDTO.setStoreGuid("storeGuid");
        wxOrderConfigDTO.setStoreName("name");
        wxOrderConfigDTO.setIsOpened(false);
        wxOrderConfigDTO.setBrandNameList(Arrays.asList("value"));
        wxOrderConfigDTO.setTagNames("tagNames");
        wxOrderConfigDTO.setAutoConfirm(0);
        wxOrderConfigDTO.setAutoConfirmTime(0L);
        wxOrderConfigDTO.setConfirmPromptTime(0L);
        wxOrderConfigDTO.setIsOnlyState(false);
        wxOrderConfigDTO.setPadBackgroundUrl("padBackgroundUrl");
        wxOrderConfigUpdateBatchReqDTO.setWxOrderConfigDTO(wxOrderConfigDTO);
        wxOrderConfigUpdateBatchReqDTO.setStoreGuidList(Arrays.asList("value"));

        // Configure WxStoreConfigMapstruct.updateDTO2WxStoreOrderConfigDO(...).
        final WxOrderConfigDO wxOrderConfigDO = new WxOrderConfigDO();
        wxOrderConfigDO.setId(0L);
        wxOrderConfigDO.setGuid("a835db38-696a-4252-827c-50e08079adb1");
        wxOrderConfigDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxOrderConfigDO.setStoreGuid("storeGuid");
        wxOrderConfigDO.setPadBackgroundUrl("padBackgroundUrl");
        final WxOrderConfigDTO wxOrderConfigDTO1 = new WxOrderConfigDTO();
        wxOrderConfigDTO1.setStoreGuid("storeGuid");
        wxOrderConfigDTO1.setStoreName("name");
        wxOrderConfigDTO1.setIsOpened(false);
        wxOrderConfigDTO1.setBrandNameList(Arrays.asList("value"));
        wxOrderConfigDTO1.setTagNames("tagNames");
        wxOrderConfigDTO1.setAutoConfirm(0);
        wxOrderConfigDTO1.setAutoConfirmTime(0L);
        wxOrderConfigDTO1.setConfirmPromptTime(0L);
        wxOrderConfigDTO1.setIsOnlyState(false);
        wxOrderConfigDTO1.setPadBackgroundUrl("padBackgroundUrl");
        when(mockWxStoreConfigMapstruct.updateDTO2WxStoreOrderConfigDO(wxOrderConfigDTO1)).thenReturn(wxOrderConfigDO);

        // Configure WxStoreConfigMapstruct.orderConfigDO2OrderConfigRespDTO(...).
        final WxOrderConfigDTO wxOrderConfigDTO2 = new WxOrderConfigDTO();
        wxOrderConfigDTO2.setStoreGuid("storeGuid");
        wxOrderConfigDTO2.setStoreName("name");
        wxOrderConfigDTO2.setIsOpened(false);
        wxOrderConfigDTO2.setBrandNameList(Arrays.asList("value"));
        wxOrderConfigDTO2.setTagNames("tagNames");
        wxOrderConfigDTO2.setAutoConfirm(0);
        wxOrderConfigDTO2.setAutoConfirmTime(0L);
        wxOrderConfigDTO2.setConfirmPromptTime(0L);
        wxOrderConfigDTO2.setIsOnlyState(false);
        wxOrderConfigDTO2.setPadBackgroundUrl("padBackgroundUrl");
        final WxOrderConfigDO wxOrderConfigDO1 = new WxOrderConfigDO();
        wxOrderConfigDO1.setId(0L);
        wxOrderConfigDO1.setGuid("a835db38-696a-4252-827c-50e08079adb1");
        wxOrderConfigDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxOrderConfigDO1.setStoreGuid("storeGuid");
        wxOrderConfigDO1.setPadBackgroundUrl("padBackgroundUrl");
        when(mockWxStoreConfigMapstruct.orderConfigDO2OrderConfigRespDTO(wxOrderConfigDO1))
                .thenReturn(wxOrderConfigDTO2);

        when(mockRedisUtils.keyGenerate(BusinessName.WX_STORE_CONFIG)).thenReturn("result");

        // Run the test
        final Boolean result = wxStoreOrderConfigServiceImplUnderTest.updateBatchStoreConfig(
                wxOrderConfigUpdateBatchReqDTO);

        // Verify the results
        assertFalse(result);
        verify(mockRedisUtils).setParamListEx(Arrays.asList(Pair.of("left", "right")), 24L, TimeUnit.HOURS);
    }

    @Test
    public void testMapOrderConfigDO() {
        // Setup
        final Map<String, WxOrderConfigDO> expectedResult = new HashMap<>();

        // Run the test
        final Map<String, WxOrderConfigDO> result = wxStoreOrderConfigServiceImplUnderTest.mapOrderConfigDO(
                Arrays.asList("value"));

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetStoreConfig() {
        // Setup
        final WxOrderConfigDTO expectedResult = new WxOrderConfigDTO();
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setStoreName("name");
        expectedResult.setIsOpened(false);
        expectedResult.setBrandNameList(Arrays.asList("value"));
        expectedResult.setTagNames("tagNames");
        expectedResult.setAutoConfirm(0);
        expectedResult.setAutoConfirmTime(0L);
        expectedResult.setConfirmPromptTime(0L);
        expectedResult.setIsOnlyState(false);
        expectedResult.setPadBackgroundUrl("padBackgroundUrl");

        // Configure WxStoreConfigMapstruct.orderConfigDO2OrderConfigRespDTO(...).
        final WxOrderConfigDTO wxOrderConfigDTO = new WxOrderConfigDTO();
        wxOrderConfigDTO.setStoreGuid("storeGuid");
        wxOrderConfigDTO.setStoreName("name");
        wxOrderConfigDTO.setIsOpened(false);
        wxOrderConfigDTO.setBrandNameList(Arrays.asList("value"));
        wxOrderConfigDTO.setTagNames("tagNames");
        wxOrderConfigDTO.setAutoConfirm(0);
        wxOrderConfigDTO.setAutoConfirmTime(0L);
        wxOrderConfigDTO.setConfirmPromptTime(0L);
        wxOrderConfigDTO.setIsOnlyState(false);
        wxOrderConfigDTO.setPadBackgroundUrl("padBackgroundUrl");
        final WxOrderConfigDO wxOrderConfigDO = new WxOrderConfigDO();
        wxOrderConfigDO.setId(0L);
        wxOrderConfigDO.setGuid("a835db38-696a-4252-827c-50e08079adb1");
        wxOrderConfigDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxOrderConfigDO.setStoreGuid("storeGuid");
        wxOrderConfigDO.setPadBackgroundUrl("padBackgroundUrl");
        when(mockWxStoreConfigMapstruct.orderConfigDO2OrderConfigRespDTO(wxOrderConfigDO)).thenReturn(wxOrderConfigDTO);

        when(mockRedisUtils.keyGenerate(BusinessName.WX_STORE_CONFIG)).thenReturn("key");
        when(mockRedisUtils.get("key")).thenReturn("result");

        // Configure WxConfigOverviewService.isStoreOpen(...).
        final Pair<WxStoreInfoDTO, Boolean> wxStoreInfoDTOBooleanPair = Pair.of(
                new WxStoreInfoDTO("storeGuid", "name", "tel", "address", false, 0, new BigDecimal("0.00"),
                        new BigDecimal("0.00"), Arrays.asList("value")), false);
        when(mockWxConfigOverviewService.isStoreOpen(0, "storeGuid")).thenReturn(wxStoreInfoDTOBooleanPair);

        // Run the test
        final WxOrderConfigDTO result = wxStoreOrderConfigServiceImplUnderTest.getStoreConfig("storeGuid");

        // Verify the results
        assertEquals(expectedResult, result);

        // Confirm RedisUtils.setEx(...).
        final WxOrderConfigDTO value = new WxOrderConfigDTO();
        value.setStoreGuid("storeGuid");
        value.setStoreName("name");
        value.setIsOpened(false);
        value.setBrandNameList(Arrays.asList("value"));
        value.setTagNames("tagNames");
        value.setAutoConfirm(0);
        value.setAutoConfirmTime(0L);
        value.setConfirmPromptTime(0L);
        value.setIsOnlyState(false);
        value.setPadBackgroundUrl("padBackgroundUrl");
        verify(mockRedisUtils).setEx("key", value, 24L, TimeUnit.HOURS);
    }
}
