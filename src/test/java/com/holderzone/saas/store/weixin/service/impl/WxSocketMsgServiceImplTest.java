package com.holderzone.saas.store.weixin.service.impl;

import com.holderzone.framework.rocketmq.common.DefaultRocketMqProducer;
import com.holderzone.saas.store.dto.weixin.WxSocketDistributionDTO;
import org.apache.rocketmq.common.message.Message;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;

@RunWith(MockitoJUnitRunner.class)
public class WxSocketMsgServiceImplTest {

    @Mock
    private DefaultRocketMqProducer mockDefaultRocketMqProducer;

    @InjectMocks
    private WxSocketMsgServiceImpl wxSocketMsgServiceImplUnderTest;

    @Test
    public void testDistribute() {
        // Setup
        final WxSocketDistributionDTO wxSocketDistributionDTO = WxSocketDistributionDTO.builder().build();

        // Run the test
        wxSocketMsgServiceImplUnderTest.distribute(wxSocketDistributionDTO);

        // Verify the results
        verify(mockDefaultRocketMqProducer).sendMessage(any(Message.class));
    }
}
