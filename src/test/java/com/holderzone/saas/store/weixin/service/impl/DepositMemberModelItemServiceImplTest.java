package com.holderzone.saas.store.weixin.service.impl;

import com.holderzone.saas.store.dto.weixin.WxMemberOverviewModelDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import org.junit.Before;
import org.junit.Test;

import static org.junit.Assert.assertEquals;

public class DepositMemberModelItemServiceImplTest {

    private DepositMemberModelItemServiceImpl depositMemberModelItemServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        depositMemberModelItemServiceImplUnderTest = new DepositMemberModelItemServiceImpl();
    }

    @Test
    public void testGetWxMemberOverviewModel() {
        // Setup
        final WxStoreConsumerDTO wxStoreConsumerDTO = WxStoreConsumerDTO.builder().build();
        final WxMemberOverviewModelDTO expectedResult = WxMemberOverviewModelDTO.builder()
                .modelId(0)
                .modelName("我的寄存")
                .modelCount(0)
                .build();

        // Run the test
        final WxMemberOverviewModelDTO result = depositMemberModelItemServiceImplUnderTest.getWxMemberOverviewModel(
                wxStoreConsumerDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }
}
