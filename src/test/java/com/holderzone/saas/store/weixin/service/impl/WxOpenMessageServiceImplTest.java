package com.holderzone.saas.store.weixin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.framework.response.Result;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.queue.HolderQueueDTO;
import com.holderzone.saas.store.dto.queue.HolderQueueItemDetailDTO;
import com.holderzone.saas.store.dto.queue.HolderQueueQueueRecordDTO;
import com.holderzone.saas.store.dto.queue.ItemGuidDTO;
import com.holderzone.saas.store.dto.trade.OrderDTO;
import com.holderzone.saas.store.dto.weixin.MultiMemberDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import com.holderzone.saas.store.dto.weixin.menu.WxMenuUrlDTO;
import com.holderzone.saas.store.dto.weixin.open.WxMessageHandleReqDTO;
import com.holderzone.saas.store.dto.weixin.open.WxOrderItemReqDTO;
import com.holderzone.saas.store.dto.weixin.open.WxOrderReqDTO;
import com.holderzone.saas.store.dto.weixin.open.WxSendMessageReqDTO;
import com.holderzone.saas.store.dto.weixin.req.*;
import com.holderzone.saas.store.dto.weixin.resp.WxConfigRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxSubjectRespDTO;
import com.holderzone.saas.store.weixin.config.WeChatCardMessageConfig;
import com.holderzone.saas.store.weixin.config.WeChatConfig;
import com.holderzone.saas.store.weixin.config.WxAccountConfig;
import com.holderzone.saas.store.weixin.config.WxThirdOpenConfig;
import com.holderzone.saas.store.weixin.entity.domain.WxMpTemplateDO;
import com.holderzone.saas.store.weixin.entity.domain.WxQrCodeInfoDO;
import com.holderzone.saas.store.weixin.entity.domain.WxStoreAuthorizerInfoDO;
import com.holderzone.saas.store.weixin.entity.domain.WxStoreMerchantOrderDO;
import com.holderzone.saas.store.weixin.entity.dto.WxMemberConsumeMsgDTO;
import com.holderzone.saas.store.weixin.entity.enums.WxTemplateTypeEnum;
import com.holderzone.saas.store.weixin.manager.WeixinSdkManager;
import com.holderzone.saas.store.weixin.mapper.WxStoreMerchantOrderMapper;
import com.holderzone.saas.store.weixin.mapstruct.WxStoreOpenMapstruct;
import com.holderzone.saas.store.weixin.service.WxMpTemplateService;
import com.holderzone.saas.store.weixin.service.WxQrCodeInfoService;
import com.holderzone.saas.store.weixin.service.WxSaasMpService;
import com.holderzone.saas.store.weixin.service.WxStoreAuthorizerInfoService;
import com.holderzone.saas.store.weixin.service.rpc.EnterpriseClientService;
import com.holderzone.saas.store.weixin.service.rpc.OrganizationClientService;
import com.holderzone.saas.store.weixin.service.rpc.QueueClientService;
import com.holderzone.saas.store.weixin.service.rpc.TradeClientService;
import com.holderzone.saas.store.weixin.utils.DynamicHelper;
import com.holderzone.saas.store.weixin.utils.WxConsumerParsUtil;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.bean.result.WxMpUser;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateData;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateMessage;
import me.chanjar.weixin.open.api.WxOpenComponentService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.redisson.api.RedissonClient;

import java.time.LocalDateTime;
import java.util.*;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class WxOpenMessageServiceImplTest {

    @Mock
    private WxThirdOpenConfig mockWxThirdOpenConfig;
    @Mock
    private WxQrCodeInfoService mockWxQrCodeInfoService;
    @Mock
    private WxSaasMpService mockWxSaasMpService;
    @Mock
    private WxOpenComponentService mockWxOpenComponentService;
    @Mock
    private WxStoreAuthorizerInfoService mockWxStoreAuthorizerInfoService;
    @Mock
    private DynamicHelper mockDynamicHelper;
    @Mock
    private WxConsumerParsUtil mockWxConsumerParsUtil;
    @Mock
    private RedisUtils mockRedisUtils;
    @Mock
    private RedissonClient mockRedissonClient;
    @Mock
    private QueueClientService mockQueueClientService;
    @Mock
    private OrganizationClientService mockOrganizationClientService;
    @Mock
    private WxStoreOpenMapstruct mockWxStoreOpenMapstruct;
    @Mock
    private WxMpTemplateService mockWxMpTemplateService;
    @Mock
    private WeChatConfig mockWeChatConfig;
    @Mock
    private WxAccountConfig mockWxAccountConfig;
    @Mock
    private EnterpriseClientService mockEnterpriseClientService;
    @Mock
    private WeixinSdkManager mockWeixinSdkManager;
    @Mock
    private WxStoreMerchantOrderMapper mockWxStoreMerchantOrderMapper;
    @Mock
    private TradeClientService mockTradeClientService;

    @InjectMocks
    private WxOpenMessageServiceImpl wxOpenMessageServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        wxOpenMessageServiceImplUnderTest.wxThirdOpenConfig = mockWxThirdOpenConfig;
        wxOpenMessageServiceImplUnderTest.wxQrCodeInfoService = mockWxQrCodeInfoService;
        wxOpenMessageServiceImplUnderTest.wxSaasMpService = mockWxSaasMpService;
        wxOpenMessageServiceImplUnderTest.wxStoreAuthorizerInfoService = mockWxStoreAuthorizerInfoService;
        wxOpenMessageServiceImplUnderTest.dynamicHelper = mockDynamicHelper;
        wxOpenMessageServiceImplUnderTest.wxConsumerParsUtil = mockWxConsumerParsUtil;
        wxOpenMessageServiceImplUnderTest.redisUtils = mockRedisUtils;
        wxOpenMessageServiceImplUnderTest.redissonClient = mockRedissonClient;
        wxOpenMessageServiceImplUnderTest.queueClientService = mockQueueClientService;
        wxOpenMessageServiceImplUnderTest.organizationClientService = mockOrganizationClientService;
        wxOpenMessageServiceImplUnderTest.wxStoreOpenMapstruct = mockWxStoreOpenMapstruct;
        wxOpenMessageServiceImplUnderTest.wxMpTemplateService = mockWxMpTemplateService;
        wxOpenMessageServiceImplUnderTest.weChatConfig = mockWeChatConfig;
        wxOpenMessageServiceImplUnderTest.wxAccountConfig = mockWxAccountConfig;
        wxOpenMessageServiceImplUnderTest.enterpriseClientService = mockEnterpriseClientService;
        wxOpenMessageServiceImplUnderTest.weixinSdkManager = mockWeixinSdkManager;
    }

    @Test
    public void testGetWxMpXmlMessage() throws Exception {
        // Setup
        final WxCommonReqDTO wxCommonReqDTO = new WxCommonReqDTO();
        wxCommonReqDTO.setSignature("signature");
        wxCommonReqDTO.setTimestamp("timestamp");
        wxCommonReqDTO.setNonce("nonce");
        wxCommonReqDTO.setEncrypt_type("encrypt_type");
        wxCommonReqDTO.setMsg_signature("msg_signature");
        final WxMessageHandleReqDTO wxMessageHandleReqDTO = new WxMessageHandleReqDTO("body", "appId", wxCommonReqDTO);
        when(mockWxThirdOpenConfig.getWxOpenComponentService()).thenReturn(null);
        when(mockWxThirdOpenConfig.getWxOpenConfigStorage()).thenReturn(null);
        when(mockWeChatConfig.getAppId()).thenReturn("result");
        when(mockWeChatConfig.getKeyWordsMap()).thenReturn(new HashMap<>());
        when(mockRedissonClient.getBucket("s")).thenReturn(null);

        // Configure WeChatConfig.getCardMessageConfig(...).
        final WeChatCardMessageConfig weChatCardMessageConfig = new WeChatCardMessageConfig();
        weChatCardMessageConfig.setEnterpriseGuid("enterpriseGuid");
        weChatCardMessageConfig.setStoreGuid("storeGuid");
        weChatCardMessageConfig.setMiniAppid("miniAppid");
        weChatCardMessageConfig.setPagepath("pagepath");
        weChatCardMessageConfig.setThumbMediaId("thumbMediaId");
        weChatCardMessageConfig.setTitle("title");
        when(mockWeChatConfig.getCardMessageConfig("enterpriseGuid")).thenReturn(weChatCardMessageConfig);

        // Configure WxStoreAuthorizerInfoService.getByAppId(...).
        final WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = new WxStoreAuthorizerInfoDO(0L,
                "9bf5fc12-0ba3-4bae-976e-7622e4fe39e0", "brandGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId", "authorizerAccessToken", 0L, "authorizerRefreshToken",
                "funcInfo", "nickName", "headImg", 0, 0, "userName", "principalName", "alias", "qrcodeUrl", "signature",
                "unBandUserGuid", "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0,
                "templateMsgId", false, "operSubjectGuid");
        when(mockWxStoreAuthorizerInfoService.getByAppId("appId")).thenReturn(wxStoreAuthorizerInfoDO);

        when(mockWxSaasMpService.getWxMpService(
                new WxStoreAuthorizerInfoDO(0L, "9bf5fc12-0ba3-4bae-976e-7622e4fe39e0", "brandGuid",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId",
                        "authorizerAccessToken", 0L, "authorizerRefreshToken", "funcInfo", "nickName", "headImg", 0, 0,
                        "userName", "principalName", "alias", "qrcodeUrl", "signature", "unBandUserGuid",
                        "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, "templateMsgId",
                        false, "operSubjectGuid"))).thenReturn(null);
        when(mockWeChatConfig.getBaseJumpUrl()).thenReturn("result");
        when(mockWeChatConfig.getSendBackMessage()).thenReturn("result");

        // Configure WxQrCodeInfoService.getCacheWxQrCodeInfoByGuid(...).
        final WxQrCodeInfoDO wxQrCodeInfoDO = new WxQrCodeInfoDO(0L, "47198c13-8ba7-4f75-a706-660dc13bccd6",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "storeGuid", "storeName",
                "areaGuid", "areaName", "tableGuid", "tableName", "brandGuid", 0, "appId");
        when(mockWxQrCodeInfoService.getCacheWxQrCodeInfoByGuid("guid")).thenReturn(wxQrCodeInfoDO);

        when(mockRedisUtils.get("key")).thenReturn("result");
        when(mockWeChatConfig.getQUEUE_DETAIL_PAGE()).thenReturn("result");

        // Configure QueueClientService.obtain(...).
        final HolderQueueDTO holderQueueDTO = new HolderQueueDTO();
        holderQueueDTO.setGuid("8fdc4686-4326-43e0-b995-75f1c902c84e");
        holderQueueDTO.setStoreGuid("storeGuid");
        holderQueueDTO.setBrandName("brandName");
        holderQueueDTO.setLogoUrl("logoUrl");
        holderQueueDTO.setIsEnableRecovery(false);
        final HolderQueueItemDetailDTO holderQueueItemDetailDTO = new HolderQueueItemDetailDTO();
        holderQueueItemDetailDTO.setStoreName("value");
        holderQueueItemDetailDTO.setStoreGuid("storeGuid");
        holderQueueItemDetailDTO.setQueueGuid("queueGuid");
        holderQueueItemDetailDTO.setStatus((byte) 0b0);
        holderQueueItemDetailDTO.setBefore(0);
        final HolderQueueQueueRecordDTO holderQueueQueueRecordDTO = new HolderQueueQueueRecordDTO(holderQueueDTO,
                holderQueueItemDetailDTO);
        final ItemGuidDTO dto = new ItemGuidDTO();
        dto.setStoreGuid("storeGuid");
        dto.setStoreName("value");
        dto.setItemGuid("queueGuid");
        dto.setBrandGuid("brandGuid");
        dto.setEnterpriseGuid("enterpriseGuid");
        when(mockQueueClientService.obtain(dto)).thenReturn(holderQueueQueueRecordDTO);

        // Configure OrganizationClientService.queryStoreByGuid(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("ba4dec25-8f87-4e2b-a29d-e504b7562d5a");
        storeDTO.setCode("code");
        storeDTO.setName("value");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        when(mockOrganizationClientService.queryStoreByGuid("storeGuid")).thenReturn(storeDTO);

        // Configure WxMpTemplateService.getOne(...).
        final WxMpTemplateDO wxMpTemplateDO = WxMpTemplateDO.builder()
                .id(0L)
                .guid("ada0a2e8-e23f-4dd4-ba33-b6eccc0be0f5")
                .shortId("shortId")
                .templateId("templateId")
                .appId("appId")
                .BrandGuid("brandGuid")
                .type(0)
                .build();
        when(mockWxMpTemplateService.getOne(any(LambdaQueryWrapper.class))).thenReturn(wxMpTemplateDO);

        // Run the test
        final String result = wxOpenMessageServiceImplUnderTest.getWxMpXmlMessage(wxMessageHandleReqDTO);

        // Verify the results
        assertEquals("success", result);
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
    }

    @Test(expected = WxErrorException.class)
    public void testGetWxMpXmlMessage_WxSaasMpServiceThrowsWxErrorException() throws Exception {
        // Setup
        final WxCommonReqDTO wxCommonReqDTO = new WxCommonReqDTO();
        wxCommonReqDTO.setSignature("signature");
        wxCommonReqDTO.setTimestamp("timestamp");
        wxCommonReqDTO.setNonce("nonce");
        wxCommonReqDTO.setEncrypt_type("encrypt_type");
        wxCommonReqDTO.setMsg_signature("msg_signature");
        final WxMessageHandleReqDTO wxMessageHandleReqDTO = new WxMessageHandleReqDTO("body", "appId", wxCommonReqDTO);
        when(mockWxThirdOpenConfig.getWxOpenComponentService()).thenReturn(null);
        when(mockWxThirdOpenConfig.getWxOpenConfigStorage()).thenReturn(null);
        when(mockWeChatConfig.getAppId()).thenReturn("result");
        when(mockWeChatConfig.getKeyWordsMap()).thenReturn(new HashMap<>());

        // Configure WeChatConfig.getCardMessageConfig(...).
        final WeChatCardMessageConfig weChatCardMessageConfig = new WeChatCardMessageConfig();
        weChatCardMessageConfig.setEnterpriseGuid("enterpriseGuid");
        weChatCardMessageConfig.setStoreGuid("storeGuid");
        weChatCardMessageConfig.setMiniAppid("miniAppid");
        weChatCardMessageConfig.setPagepath("pagepath");
        weChatCardMessageConfig.setThumbMediaId("thumbMediaId");
        weChatCardMessageConfig.setTitle("title");
        when(mockWeChatConfig.getCardMessageConfig("enterpriseGuid")).thenReturn(weChatCardMessageConfig);

        // Configure WxStoreAuthorizerInfoService.getByAppId(...).
        final WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = new WxStoreAuthorizerInfoDO(0L,
                "9bf5fc12-0ba3-4bae-976e-7622e4fe39e0", "brandGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId", "authorizerAccessToken", 0L, "authorizerRefreshToken",
                "funcInfo", "nickName", "headImg", 0, 0, "userName", "principalName", "alias", "qrcodeUrl", "signature",
                "unBandUserGuid", "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0,
                "templateMsgId", false, "operSubjectGuid");
        when(mockWxStoreAuthorizerInfoService.getByAppId("appId")).thenReturn(wxStoreAuthorizerInfoDO);

        when(mockWxSaasMpService.getWxMpService(
                new WxStoreAuthorizerInfoDO(0L, "9bf5fc12-0ba3-4bae-976e-7622e4fe39e0", "brandGuid",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId",
                        "authorizerAccessToken", 0L, "authorizerRefreshToken", "funcInfo", "nickName", "headImg", 0, 0,
                        "userName", "principalName", "alias", "qrcodeUrl", "signature", "unBandUserGuid",
                        "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, "templateMsgId",
                        false, "operSubjectGuid"))).thenThrow(WxErrorException.class);

        // Run the test
        wxOpenMessageServiceImplUnderTest.getWxMpXmlMessage(wxMessageHandleReqDTO);
    }

    @Test
    public void testGetWxMpXmlMessage_WxMpTemplateServiceReturnsNull() throws Exception {
        // Setup
        final WxCommonReqDTO wxCommonReqDTO = new WxCommonReqDTO();
        wxCommonReqDTO.setSignature("signature");
        wxCommonReqDTO.setTimestamp("timestamp");
        wxCommonReqDTO.setNonce("nonce");
        wxCommonReqDTO.setEncrypt_type("encrypt_type");
        wxCommonReqDTO.setMsg_signature("msg_signature");
        final WxMessageHandleReqDTO wxMessageHandleReqDTO = new WxMessageHandleReqDTO("body", "appId", wxCommonReqDTO);
        when(mockWxThirdOpenConfig.getWxOpenComponentService()).thenReturn(null);
        when(mockWxThirdOpenConfig.getWxOpenConfigStorage()).thenReturn(null);
        when(mockWeChatConfig.getAppId()).thenReturn("result");
        when(mockWeChatConfig.getKeyWordsMap()).thenReturn(new HashMap<>());

        // Configure WeChatConfig.getCardMessageConfig(...).
        final WeChatCardMessageConfig weChatCardMessageConfig = new WeChatCardMessageConfig();
        weChatCardMessageConfig.setEnterpriseGuid("enterpriseGuid");
        weChatCardMessageConfig.setStoreGuid("storeGuid");
        weChatCardMessageConfig.setMiniAppid("miniAppid");
        weChatCardMessageConfig.setPagepath("pagepath");
        weChatCardMessageConfig.setThumbMediaId("thumbMediaId");
        weChatCardMessageConfig.setTitle("title");
        when(mockWeChatConfig.getCardMessageConfig("enterpriseGuid")).thenReturn(weChatCardMessageConfig);

        // Configure WxStoreAuthorizerInfoService.getByAppId(...).
        final WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = new WxStoreAuthorizerInfoDO(0L,
                "9bf5fc12-0ba3-4bae-976e-7622e4fe39e0", "brandGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId", "authorizerAccessToken", 0L, "authorizerRefreshToken",
                "funcInfo", "nickName", "headImg", 0, 0, "userName", "principalName", "alias", "qrcodeUrl", "signature",
                "unBandUserGuid", "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0,
                "templateMsgId", false, "operSubjectGuid");
        when(mockWxStoreAuthorizerInfoService.getByAppId("appId")).thenReturn(wxStoreAuthorizerInfoDO);

        when(mockWxSaasMpService.getWxMpService(
                new WxStoreAuthorizerInfoDO(0L, "9bf5fc12-0ba3-4bae-976e-7622e4fe39e0", "brandGuid",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId",
                        "authorizerAccessToken", 0L, "authorizerRefreshToken", "funcInfo", "nickName", "headImg", 0, 0,
                        "userName", "principalName", "alias", "qrcodeUrl", "signature", "unBandUserGuid",
                        "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, "templateMsgId",
                        false, "operSubjectGuid"))).thenReturn(null);
        when(mockRedisUtils.get("key")).thenReturn("result");
        when(mockWeChatConfig.getQUEUE_DETAIL_PAGE()).thenReturn("result");

        // Configure QueueClientService.obtain(...).
        final HolderQueueDTO holderQueueDTO = new HolderQueueDTO();
        holderQueueDTO.setGuid("8fdc4686-4326-43e0-b995-75f1c902c84e");
        holderQueueDTO.setStoreGuid("storeGuid");
        holderQueueDTO.setBrandName("brandName");
        holderQueueDTO.setLogoUrl("logoUrl");
        holderQueueDTO.setIsEnableRecovery(false);
        final HolderQueueItemDetailDTO holderQueueItemDetailDTO = new HolderQueueItemDetailDTO();
        holderQueueItemDetailDTO.setStoreName("value");
        holderQueueItemDetailDTO.setStoreGuid("storeGuid");
        holderQueueItemDetailDTO.setQueueGuid("queueGuid");
        holderQueueItemDetailDTO.setStatus((byte) 0b0);
        holderQueueItemDetailDTO.setBefore(0);
        final HolderQueueQueueRecordDTO holderQueueQueueRecordDTO = new HolderQueueQueueRecordDTO(holderQueueDTO,
                holderQueueItemDetailDTO);
        final ItemGuidDTO dto = new ItemGuidDTO();
        dto.setStoreGuid("storeGuid");
        dto.setStoreName("value");
        dto.setItemGuid("queueGuid");
        dto.setBrandGuid("brandGuid");
        dto.setEnterpriseGuid("enterpriseGuid");
        when(mockQueueClientService.obtain(dto)).thenReturn(holderQueueQueueRecordDTO);

        // Configure OrganizationClientService.queryStoreByGuid(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("ba4dec25-8f87-4e2b-a29d-e504b7562d5a");
        storeDTO.setCode("code");
        storeDTO.setName("value");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        when(mockOrganizationClientService.queryStoreByGuid("storeGuid")).thenReturn(storeDTO);

        when(mockWxMpTemplateService.getOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Run the test
        final String result = wxOpenMessageServiceImplUnderTest.getWxMpXmlMessage(wxMessageHandleReqDTO);

        // Verify the results
        assertEquals("success", result);
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
    }

    @Test
    public void testGetUserInfo() throws Exception {
        // Setup
        final WxAuthorizeReqDTO wxAuthorizeReqDTO = new WxAuthorizeReqDTO();
        wxAuthorizeReqDTO.setCode("code");
        wxAuthorizeReqDTO.setState("state");
        wxAuthorizeReqDTO.setEventKey("eventKey");
        wxAuthorizeReqDTO.setAppId("appId");
        wxAuthorizeReqDTO.setMenuType("menuType");

        when(mockWxConsumerParsUtil.changeDateSourceByEventKey("eventKey")).thenReturn("eventKey");

        // Configure WxStoreAuthorizerInfoService.getByAppId(...).
        final WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = new WxStoreAuthorizerInfoDO(0L,
                "9bf5fc12-0ba3-4bae-976e-7622e4fe39e0", "brandGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId", "authorizerAccessToken", 0L, "authorizerRefreshToken",
                "funcInfo", "nickName", "headImg", 0, 0, "userName", "principalName", "alias", "qrcodeUrl", "signature",
                "unBandUserGuid", "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0,
                "templateMsgId", false, "operSubjectGuid");
        when(mockWxStoreAuthorizerInfoService.getByAppId("appId")).thenReturn(wxStoreAuthorizerInfoDO);

        when(mockWeChatConfig.getOrderPageUrl()).thenReturn("result");
        when(mockWxSaasMpService.getWxMpService(
                new WxStoreAuthorizerInfoDO(0L, "9bf5fc12-0ba3-4bae-976e-7622e4fe39e0", "brandGuid",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId",
                        "authorizerAccessToken", 0L, "authorizerRefreshToken", "funcInfo", "nickName", "headImg", 0, 0,
                        "userName", "principalName", "alias", "qrcodeUrl", "signature", "unBandUserGuid",
                        "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, "templateMsgId",
                        false, "operSubjectGuid"))).thenReturn(null);
        when(mockWxThirdOpenConfig.getWxOpenComponentService()).thenReturn(null);

        // Configure WxConsumerParsUtil.parse2Consumer(...).
        final WxMpUser wxMpUser = new WxMpUser();
        wxMpUser.setSubscribe(false);
        wxMpUser.setOpenId("openId");
        wxMpUser.setNickname("nickname");
        wxMpUser.setSexDesc("sexDesc");
        wxMpUser.setSex(0);
        when(mockWxConsumerParsUtil.parse2Consumer("eventKey", wxMpUser,
                new WxStoreAuthorizerInfoDO(0L, "9bf5fc12-0ba3-4bae-976e-7622e4fe39e0", "brandGuid",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId",
                        "authorizerAccessToken", 0L, "authorizerRefreshToken", "funcInfo", "nickName", "headImg", 0, 0,
                        "userName", "principalName", "alias", "qrcodeUrl", "signature", "unBandUserGuid",
                        "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, "templateMsgId",
                        false, "operSubjectGuid"))).thenReturn("result");

        // Run the test
        final String result = wxOpenMessageServiceImplUnderTest.getUserInfo(wxAuthorizeReqDTO);

        // Verify the results
        assertEquals("result", result);
    }

    @Test(expected = WxErrorException.class)
    public void testGetUserInfo_WxSaasMpServiceThrowsWxErrorException() throws Exception {
        // Setup
        final WxAuthorizeReqDTO wxAuthorizeReqDTO = new WxAuthorizeReqDTO();
        wxAuthorizeReqDTO.setCode("code");
        wxAuthorizeReqDTO.setState("state");
        wxAuthorizeReqDTO.setEventKey("eventKey");
        wxAuthorizeReqDTO.setAppId("appId");
        wxAuthorizeReqDTO.setMenuType("menuType");

        when(mockWxConsumerParsUtil.changeDateSourceByEventKey("eventKey")).thenReturn("eventKey");

        // Configure WxStoreAuthorizerInfoService.getByAppId(...).
        final WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = new WxStoreAuthorizerInfoDO(0L,
                "9bf5fc12-0ba3-4bae-976e-7622e4fe39e0", "brandGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId", "authorizerAccessToken", 0L, "authorizerRefreshToken",
                "funcInfo", "nickName", "headImg", 0, 0, "userName", "principalName", "alias", "qrcodeUrl", "signature",
                "unBandUserGuid", "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0,
                "templateMsgId", false, "operSubjectGuid");
        when(mockWxStoreAuthorizerInfoService.getByAppId("appId")).thenReturn(wxStoreAuthorizerInfoDO);

        when(mockWxSaasMpService.getWxMpService(
                new WxStoreAuthorizerInfoDO(0L, "9bf5fc12-0ba3-4bae-976e-7622e4fe39e0", "brandGuid",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId",
                        "authorizerAccessToken", 0L, "authorizerRefreshToken", "funcInfo", "nickName", "headImg", 0, 0,
                        "userName", "principalName", "alias", "qrcodeUrl", "signature", "unBandUserGuid",
                        "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, "templateMsgId",
                        false, "operSubjectGuid"))).thenThrow(WxErrorException.class);

        // Run the test
        wxOpenMessageServiceImplUnderTest.getUserInfo(wxAuthorizeReqDTO);
    }

    @Test
    public void testCreateQueueMsgTemp() throws Exception {
        // Setup
        final TempMsgCreateDTO tempMsgCreateDTO = new TempMsgCreateDTO("appId");

        // Configure WxStoreAuthorizerInfoService.getByAppId(...).
        final WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = new WxStoreAuthorizerInfoDO(0L,
                "9bf5fc12-0ba3-4bae-976e-7622e4fe39e0", "brandGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId", "authorizerAccessToken", 0L, "authorizerRefreshToken",
                "funcInfo", "nickName", "headImg", 0, 0, "userName", "principalName", "alias", "qrcodeUrl", "signature",
                "unBandUserGuid", "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0,
                "templateMsgId", false, "operSubjectGuid");
        when(mockWxStoreAuthorizerInfoService.getByAppId("appId")).thenReturn(wxStoreAuthorizerInfoDO);

        when(mockWxSaasMpService.getWxMpService(
                new WxStoreAuthorizerInfoDO(0L, "9bf5fc12-0ba3-4bae-976e-7622e4fe39e0", "brandGuid",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId",
                        "authorizerAccessToken", 0L, "authorizerRefreshToken", "funcInfo", "nickName", "headImg", 0, 0,
                        "userName", "principalName", "alias", "qrcodeUrl", "signature", "unBandUserGuid",
                        "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, "templateMsgId",
                        false, "operSubjectGuid"))).thenReturn(null);

        // Run the test
        wxOpenMessageServiceImplUnderTest.createQueueMsgTemp(tempMsgCreateDTO);

        // Verify the results
        verify(mockWxStoreAuthorizerInfoService).updateById(
                new WxStoreAuthorizerInfoDO(0L, "9bf5fc12-0ba3-4bae-976e-7622e4fe39e0", "brandGuid",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId",
                        "authorizerAccessToken", 0L, "authorizerRefreshToken", "funcInfo", "nickName", "headImg", 0, 0,
                        "userName", "principalName", "alias", "qrcodeUrl", "signature", "unBandUserGuid",
                        "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, "templateMsgId",
                        false, "operSubjectGuid"));
    }

    @Test(expected = WxErrorException.class)
    public void testCreateQueueMsgTemp_WxSaasMpServiceThrowsWxErrorException() throws Exception {
        // Setup
        final TempMsgCreateDTO tempMsgCreateDTO = new TempMsgCreateDTO("appId");

        // Configure WxStoreAuthorizerInfoService.getByAppId(...).
        final WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = new WxStoreAuthorizerInfoDO(0L,
                "9bf5fc12-0ba3-4bae-976e-7622e4fe39e0", "brandGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId", "authorizerAccessToken", 0L, "authorizerRefreshToken",
                "funcInfo", "nickName", "headImg", 0, 0, "userName", "principalName", "alias", "qrcodeUrl", "signature",
                "unBandUserGuid", "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0,
                "templateMsgId", false, "operSubjectGuid");
        when(mockWxStoreAuthorizerInfoService.getByAppId("appId")).thenReturn(wxStoreAuthorizerInfoDO);

        when(mockWxSaasMpService.getWxMpService(
                new WxStoreAuthorizerInfoDO(0L, "9bf5fc12-0ba3-4bae-976e-7622e4fe39e0", "brandGuid",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId",
                        "authorizerAccessToken", 0L, "authorizerRefreshToken", "funcInfo", "nickName", "headImg", 0, 0,
                        "userName", "principalName", "alias", "qrcodeUrl", "signature", "unBandUserGuid",
                        "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, "templateMsgId",
                        false, "operSubjectGuid"))).thenThrow(WxErrorException.class);

        // Run the test
        wxOpenMessageServiceImplUnderTest.createQueueMsgTemp(tempMsgCreateDTO);
    }

    @Test
    public void testSendQueueMsg() throws Exception {
        // Setup
        final WxStoreConsumerDTO wxStoreConsumerDTO = WxStoreConsumerDTO.builder()
                .openId("openId")
                .enterpriseGuid("enterpriseGuid")
                .brandGuid("brandGuid")
                .build();
        final HolderQueueDTO holderQueueDTO = new HolderQueueDTO();
        holderQueueDTO.setGuid("8fdc4686-4326-43e0-b995-75f1c902c84e");
        holderQueueDTO.setStoreGuid("storeGuid");
        holderQueueDTO.setBrandName("brandName");
        holderQueueDTO.setLogoUrl("logoUrl");
        holderQueueDTO.setIsEnableRecovery(false);
        final HolderQueueItemDetailDTO holderQueueItemDetailDTO = new HolderQueueItemDetailDTO();
        holderQueueItemDetailDTO.setStoreName("value");
        holderQueueItemDetailDTO.setStoreGuid("storeGuid");
        holderQueueItemDetailDTO.setQueueGuid("queueGuid");
        holderQueueItemDetailDTO.setStatus((byte) 0b0);
        holderQueueItemDetailDTO.setBefore(0);
        final HolderQueueQueueRecordDTO obtain = new HolderQueueQueueRecordDTO(holderQueueDTO,
                holderQueueItemDetailDTO);
        final WxPortalReqDTO wxPortalReqDTO = WxPortalReqDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .appId("appId")
                .brandGuid("brandGuid")
                .msgKey("msgKey")
                .url("url")
                .reqType(0)
                .build();

        // Configure WxStoreAuthorizerInfoService.getById(...).
        final WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = new WxStoreAuthorizerInfoDO(0L,
                "9bf5fc12-0ba3-4bae-976e-7622e4fe39e0", "brandGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId", "authorizerAccessToken", 0L, "authorizerRefreshToken",
                "funcInfo", "nickName", "headImg", 0, 0, "userName", "principalName", "alias", "qrcodeUrl", "signature",
                "unBandUserGuid", "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0,
                "templateMsgId", false, "operSubjectGuid");
        when(mockWxStoreAuthorizerInfoService.getById("brandGuid")).thenReturn(wxStoreAuthorizerInfoDO);

        when(mockWxSaasMpService.getWxMpService(
                new WxStoreAuthorizerInfoDO(0L, "9bf5fc12-0ba3-4bae-976e-7622e4fe39e0", "brandGuid",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId",
                        "authorizerAccessToken", 0L, "authorizerRefreshToken", "funcInfo", "nickName", "headImg", 0, 0,
                        "userName", "principalName", "alias", "qrcodeUrl", "signature", "unBandUserGuid",
                        "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, "templateMsgId",
                        false, "operSubjectGuid"))).thenReturn(null);

        // Configure OrganizationClientService.queryStoreByGuid(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("ba4dec25-8f87-4e2b-a29d-e504b7562d5a");
        storeDTO.setCode("code");
        storeDTO.setName("value");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        when(mockOrganizationClientService.queryStoreByGuid("storeGuid")).thenReturn(storeDTO);

        // Configure WxMpTemplateService.getOne(...).
        final WxMpTemplateDO wxMpTemplateDO = WxMpTemplateDO.builder()
                .id(0L)
                .guid("ada0a2e8-e23f-4dd4-ba33-b6eccc0be0f5")
                .shortId("shortId")
                .templateId("templateId")
                .appId("appId")
                .BrandGuid("brandGuid")
                .type(0)
                .build();
        when(mockWxMpTemplateService.getOne(any(LambdaQueryWrapper.class))).thenReturn(wxMpTemplateDO);

        when(mockWeChatConfig.getQUEUE_DETAIL_PAGE()).thenReturn("result");

        // Run the test
        wxOpenMessageServiceImplUnderTest.sendQueueMsg(wxStoreConsumerDTO, obtain, wxPortalReqDTO);

        // Verify the results
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
    }

    @Test(expected = WxErrorException.class)
    public void testSendQueueMsg_WxSaasMpServiceThrowsWxErrorException() throws Exception {
        // Setup
        final WxStoreConsumerDTO wxStoreConsumerDTO = WxStoreConsumerDTO.builder()
                .openId("openId")
                .enterpriseGuid("enterpriseGuid")
                .brandGuid("brandGuid")
                .build();
        final HolderQueueDTO holderQueueDTO = new HolderQueueDTO();
        holderQueueDTO.setGuid("8fdc4686-4326-43e0-b995-75f1c902c84e");
        holderQueueDTO.setStoreGuid("storeGuid");
        holderQueueDTO.setBrandName("brandName");
        holderQueueDTO.setLogoUrl("logoUrl");
        holderQueueDTO.setIsEnableRecovery(false);
        final HolderQueueItemDetailDTO holderQueueItemDetailDTO = new HolderQueueItemDetailDTO();
        holderQueueItemDetailDTO.setStoreName("value");
        holderQueueItemDetailDTO.setStoreGuid("storeGuid");
        holderQueueItemDetailDTO.setQueueGuid("queueGuid");
        holderQueueItemDetailDTO.setStatus((byte) 0b0);
        holderQueueItemDetailDTO.setBefore(0);
        final HolderQueueQueueRecordDTO obtain = new HolderQueueQueueRecordDTO(holderQueueDTO,
                holderQueueItemDetailDTO);
        final WxPortalReqDTO wxPortalReqDTO = WxPortalReqDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .appId("appId")
                .brandGuid("brandGuid")
                .msgKey("msgKey")
                .url("url")
                .reqType(0)
                .build();

        // Configure WxStoreAuthorizerInfoService.getById(...).
        final WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = new WxStoreAuthorizerInfoDO(0L,
                "9bf5fc12-0ba3-4bae-976e-7622e4fe39e0", "brandGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId", "authorizerAccessToken", 0L, "authorizerRefreshToken",
                "funcInfo", "nickName", "headImg", 0, 0, "userName", "principalName", "alias", "qrcodeUrl", "signature",
                "unBandUserGuid", "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0,
                "templateMsgId", false, "operSubjectGuid");
        when(mockWxStoreAuthorizerInfoService.getById("brandGuid")).thenReturn(wxStoreAuthorizerInfoDO);

        when(mockWxSaasMpService.getWxMpService(
                new WxStoreAuthorizerInfoDO(0L, "9bf5fc12-0ba3-4bae-976e-7622e4fe39e0", "brandGuid",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId",
                        "authorizerAccessToken", 0L, "authorizerRefreshToken", "funcInfo", "nickName", "headImg", 0, 0,
                        "userName", "principalName", "alias", "qrcodeUrl", "signature", "unBandUserGuid",
                        "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, "templateMsgId",
                        false, "operSubjectGuid"))).thenThrow(WxErrorException.class);

        // Run the test
        wxOpenMessageServiceImplUnderTest.sendQueueMsg(wxStoreConsumerDTO, obtain, wxPortalReqDTO);
    }

    @Test
    public void testSendQueueMsg_WxMpTemplateServiceReturnsNull() throws Exception {
        // Setup
        final WxStoreConsumerDTO wxStoreConsumerDTO = WxStoreConsumerDTO.builder()
                .openId("openId")
                .enterpriseGuid("enterpriseGuid")
                .brandGuid("brandGuid")
                .build();
        final HolderQueueDTO holderQueueDTO = new HolderQueueDTO();
        holderQueueDTO.setGuid("8fdc4686-4326-43e0-b995-75f1c902c84e");
        holderQueueDTO.setStoreGuid("storeGuid");
        holderQueueDTO.setBrandName("brandName");
        holderQueueDTO.setLogoUrl("logoUrl");
        holderQueueDTO.setIsEnableRecovery(false);
        final HolderQueueItemDetailDTO holderQueueItemDetailDTO = new HolderQueueItemDetailDTO();
        holderQueueItemDetailDTO.setStoreName("value");
        holderQueueItemDetailDTO.setStoreGuid("storeGuid");
        holderQueueItemDetailDTO.setQueueGuid("queueGuid");
        holderQueueItemDetailDTO.setStatus((byte) 0b0);
        holderQueueItemDetailDTO.setBefore(0);
        final HolderQueueQueueRecordDTO obtain = new HolderQueueQueueRecordDTO(holderQueueDTO,
                holderQueueItemDetailDTO);
        final WxPortalReqDTO wxPortalReqDTO = WxPortalReqDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .appId("appId")
                .brandGuid("brandGuid")
                .msgKey("msgKey")
                .url("url")
                .reqType(0)
                .build();

        // Configure WxStoreAuthorizerInfoService.getById(...).
        final WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = new WxStoreAuthorizerInfoDO(0L,
                "9bf5fc12-0ba3-4bae-976e-7622e4fe39e0", "brandGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId", "authorizerAccessToken", 0L, "authorizerRefreshToken",
                "funcInfo", "nickName", "headImg", 0, 0, "userName", "principalName", "alias", "qrcodeUrl", "signature",
                "unBandUserGuid", "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0,
                "templateMsgId", false, "operSubjectGuid");
        when(mockWxStoreAuthorizerInfoService.getById("brandGuid")).thenReturn(wxStoreAuthorizerInfoDO);

        when(mockWxSaasMpService.getWxMpService(
                new WxStoreAuthorizerInfoDO(0L, "9bf5fc12-0ba3-4bae-976e-7622e4fe39e0", "brandGuid",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId",
                        "authorizerAccessToken", 0L, "authorizerRefreshToken", "funcInfo", "nickName", "headImg", 0, 0,
                        "userName", "principalName", "alias", "qrcodeUrl", "signature", "unBandUserGuid",
                        "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, "templateMsgId",
                        false, "operSubjectGuid"))).thenReturn(null);

        // Configure OrganizationClientService.queryStoreByGuid(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("ba4dec25-8f87-4e2b-a29d-e504b7562d5a");
        storeDTO.setCode("code");
        storeDTO.setName("value");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        when(mockOrganizationClientService.queryStoreByGuid("storeGuid")).thenReturn(storeDTO);

        when(mockWxMpTemplateService.getOne(any(LambdaQueryWrapper.class))).thenReturn(null);
        when(mockWeChatConfig.getQUEUE_DETAIL_PAGE()).thenReturn("result");

        // Run the test
        wxOpenMessageServiceImplUnderTest.sendQueueMsg(wxStoreConsumerDTO, obtain, wxPortalReqDTO);

        // Verify the results
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
    }

    @Test
    public void testShopList() {
        // Setup
        final WxPortalReqDTO wxPortalReqDTO = WxPortalReqDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .appId("appId")
                .brandGuid("brandGuid")
                .msgKey("msgKey")
                .url("url")
                .reqType(0)
                .build();

        // Configure WxStoreAuthorizerInfoService.getById(...).
        final WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = new WxStoreAuthorizerInfoDO(0L,
                "9bf5fc12-0ba3-4bae-976e-7622e4fe39e0", "brandGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId", "authorizerAccessToken", 0L, "authorizerRefreshToken",
                "funcInfo", "nickName", "headImg", 0, 0, "userName", "principalName", "alias", "qrcodeUrl", "signature",
                "unBandUserGuid", "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0,
                "templateMsgId", false, "operSubjectGuid");
        when(mockWxStoreAuthorizerInfoService.getById("brandGuid")).thenReturn(wxStoreAuthorizerInfoDO);

        when(mockWeChatConfig.getBaseJumpUrl()).thenReturn("result");
        when(mockWxThirdOpenConfig.getWxOpenComponentService()).thenReturn(null);

        // Run the test
        final String result = wxOpenMessageServiceImplUnderTest.shopList(wxPortalReqDTO);

        // Verify the results
        assertEquals("result", result);
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
    }

    @Test
    public void testGetBossAuthorizeUrl() {
        // Setup
        final WxMenuUrlDTO wxMenuUrlDTO = new WxMenuUrlDTO();
        wxMenuUrlDTO.setEnterpriseGuid("enterpriseGuid");
        wxMenuUrlDTO.setBrandGuid("brandGuid");
        wxMenuUrlDTO.setMenuType("menuType");
        wxMenuUrlDTO.setToken("token");
        wxMenuUrlDTO.setMsgKey("msgKey");

        when(mockWeChatConfig.getBossAuthorizeUrl()).thenReturn("result");
        when(mockWeixinSdkManager.buildWxMpService(4)).thenReturn(null);
        when(mockWxThirdOpenConfig.getWxOpenComponentService()).thenReturn(null);
        when(mockWxAccountConfig.getAppId()).thenReturn("result");

        // Run the test
        final String result = wxOpenMessageServiceImplUnderTest.getBossAuthorizeUrl(wxMenuUrlDTO);

        // Verify the results
        assertEquals("result", result);
    }

    @Test
    public void testGetBossRedirectUrl() {
        // Setup
        final WxAuthorizeReqDTO wxAuthorizeReqDTO = new WxAuthorizeReqDTO();
        wxAuthorizeReqDTO.setCode("code");
        wxAuthorizeReqDTO.setState("state");
        wxAuthorizeReqDTO.setEventKey("eventKey");
        wxAuthorizeReqDTO.setAppId("appId");
        wxAuthorizeReqDTO.setMenuType("menuType");

        when(mockWeixinSdkManager.buildWxMpService(4)).thenReturn(null);
        when(mockRedissonClient.getBucket("s")).thenReturn(null);
        when(mockWeChatConfig.getBiBossRedirectUrl()).thenReturn("result");

        // Run the test
        final String result = wxOpenMessageServiceImplUnderTest.getBossRedirectUrl(wxAuthorizeReqDTO);

        // Verify the results
        assertEquals("result", result);
    }

    @Test
    public void testSaveBossAuthToken() {
        // Setup
        final WxMenuUrlDTO wxMenuUrlDTO = new WxMenuUrlDTO();
        wxMenuUrlDTO.setEnterpriseGuid("enterpriseGuid");
        wxMenuUrlDTO.setBrandGuid("brandGuid");
        wxMenuUrlDTO.setMenuType("menuType");
        wxMenuUrlDTO.setToken("token");
        wxMenuUrlDTO.setMsgKey("msgKey");

        when(mockRedissonClient.getBucket("s")).thenReturn(null);

        // Run the test
        wxOpenMessageServiceImplUnderTest.saveBossAuthToken(wxMenuUrlDTO);

        // Verify the results
    }

    @Test
    public void testCleanBossAuthToken() {
        // Setup
        final WxMenuUrlDTO wxMenuUrlDTO = new WxMenuUrlDTO();
        wxMenuUrlDTO.setEnterpriseGuid("enterpriseGuid");
        wxMenuUrlDTO.setBrandGuid("brandGuid");
        wxMenuUrlDTO.setMenuType("menuType");
        wxMenuUrlDTO.setToken("token");
        wxMenuUrlDTO.setMsgKey("msgKey");

        when(mockRedissonClient.getBucket("s")).thenReturn(null);

        // Run the test
        wxOpenMessageServiceImplUnderTest.cleanBossAuthToken(wxMenuUrlDTO);

        // Verify the results
    }

    @Test
    public void testRemoveBossAuthToken() {
        // Setup
        when(mockRedissonClient.getBucket("s")).thenReturn(null);

        // Run the test
        wxOpenMessageServiceImplUnderTest.removeBossAuthToken("openId");

        // Verify the results
    }

    @Test
    public void testGenerateDTO() throws Exception {
        // Setup
        final WxPortalReqDTO wxPortalReqDTO = WxPortalReqDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .appId("appId")
                .brandGuid("brandGuid")
                .msgKey("msgKey")
                .url("url")
                .reqType(0)
                .build();
        final WxCommonReqDTO wxCommonReqDTO = new WxCommonReqDTO();
        wxCommonReqDTO.setSignature("signature");
        wxCommonReqDTO.setTimestamp("timestamp");
        wxCommonReqDTO.setNonce("nonce");
        wxCommonReqDTO.setEncrypt_type("encrypt_type");
        wxCommonReqDTO.setMsg_signature("msg_signature");
        final WxConfigRespDTO expectedResult = new WxConfigRespDTO(wxCommonReqDTO, "name", "appId", "enterpriseGuid");

        // Configure WxStoreAuthorizerInfoService.getByAppId(...).
        final WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = new WxStoreAuthorizerInfoDO(0L,
                "9bf5fc12-0ba3-4bae-976e-7622e4fe39e0", "brandGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId", "authorizerAccessToken", 0L, "authorizerRefreshToken",
                "funcInfo", "nickName", "headImg", 0, 0, "userName", "principalName", "alias", "qrcodeUrl", "signature",
                "unBandUserGuid", "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0,
                "templateMsgId", false, "operSubjectGuid");
        when(mockWxStoreAuthorizerInfoService.getByAppId("appId")).thenReturn(wxStoreAuthorizerInfoDO);

        when(mockWxThirdOpenConfig.getWxOpenConfigStorage()).thenReturn(null);
        when(mockWxSaasMpService.getWxMpService(
                new WxStoreAuthorizerInfoDO(0L, "9bf5fc12-0ba3-4bae-976e-7622e4fe39e0", "brandGuid",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId",
                        "authorizerAccessToken", 0L, "authorizerRefreshToken", "funcInfo", "nickName", "headImg", 0, 0,
                        "userName", "principalName", "alias", "qrcodeUrl", "signature", "unBandUserGuid",
                        "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, "templateMsgId",
                        false, "operSubjectGuid"))).thenReturn(null);

        // Configure OrganizationClientService.queryBrandByGuid(...).
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("e4af49d0-c481-4dd7-90ab-19cdb914e47c");
        brandDTO.setUuid("e8240044-a86e-42f7-a6ad-15c20d91b0d7");
        brandDTO.setName("name");
        brandDTO.setDescription("description");
        brandDTO.setLogoUrl("logoUrl");
        when(mockOrganizationClientService.queryBrandByGuid("brandGuid")).thenReturn(brandDTO);

        // Run the test
        final WxConfigRespDTO result = wxOpenMessageServiceImplUnderTest.generateDTO(wxPortalReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
    }

    @Test(expected = WxErrorException.class)
    public void testGenerateDTO_WxSaasMpServiceThrowsWxErrorException() throws Exception {
        // Setup
        final WxPortalReqDTO wxPortalReqDTO = WxPortalReqDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .appId("appId")
                .brandGuid("brandGuid")
                .msgKey("msgKey")
                .url("url")
                .reqType(0)
                .build();

        // Configure WxStoreAuthorizerInfoService.getByAppId(...).
        final WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = new WxStoreAuthorizerInfoDO(0L,
                "9bf5fc12-0ba3-4bae-976e-7622e4fe39e0", "brandGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId", "authorizerAccessToken", 0L, "authorizerRefreshToken",
                "funcInfo", "nickName", "headImg", 0, 0, "userName", "principalName", "alias", "qrcodeUrl", "signature",
                "unBandUserGuid", "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0,
                "templateMsgId", false, "operSubjectGuid");
        when(mockWxStoreAuthorizerInfoService.getByAppId("appId")).thenReturn(wxStoreAuthorizerInfoDO);

        when(mockWxThirdOpenConfig.getWxOpenConfigStorage()).thenReturn(null);
        when(mockWxSaasMpService.getWxMpService(
                new WxStoreAuthorizerInfoDO(0L, "9bf5fc12-0ba3-4bae-976e-7622e4fe39e0", "brandGuid",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId",
                        "authorizerAccessToken", 0L, "authorizerRefreshToken", "funcInfo", "nickName", "headImg", 0, 0,
                        "userName", "principalName", "alias", "qrcodeUrl", "signature", "unBandUserGuid",
                        "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, "templateMsgId",
                        false, "operSubjectGuid"))).thenThrow(WxErrorException.class);

        // Run the test
        wxOpenMessageServiceImplUnderTest.generateDTO(wxPortalReqDTO);
    }

    @Test
    public void testMemberLogin() {
        // Setup
        final WxPortalReqDTO wxPortalReqDTO = WxPortalReqDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .appId("appId")
                .brandGuid("brandGuid")
                .msgKey("msgKey")
                .url("url")
                .reqType(0)
                .build();

        // Configure WxStoreAuthorizerInfoService.getById(...).
        final WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = new WxStoreAuthorizerInfoDO(0L,
                "9bf5fc12-0ba3-4bae-976e-7622e4fe39e0", "brandGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId", "authorizerAccessToken", 0L, "authorizerRefreshToken",
                "funcInfo", "nickName", "headImg", 0, 0, "userName", "principalName", "alias", "qrcodeUrl", "signature",
                "unBandUserGuid", "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0,
                "templateMsgId", false, "operSubjectGuid");
        when(mockWxStoreAuthorizerInfoService.getById("brandGuid")).thenReturn(wxStoreAuthorizerInfoDO);

        when(mockWeChatConfig.getBaseJumpUrl()).thenReturn("result");
        when(mockWxThirdOpenConfig.getWxOpenComponentService()).thenReturn(null);

        // Run the test
        final String result = wxOpenMessageServiceImplUnderTest.memberLogin(wxPortalReqDTO);

        // Verify the results
        assertEquals("result", result);
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
    }

    @Test
    public void testNewMemberLogin() {
        // Setup
        final WxPortalReqDTO wxPortalReqDTO = WxPortalReqDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .appId("appId")
                .brandGuid("brandGuid")
                .msgKey("msgKey")
                .url("url")
                .reqType(0)
                .build();

        // Configure WxStoreAuthorizerInfoService.getById(...).
        final WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = new WxStoreAuthorizerInfoDO(0L,
                "9bf5fc12-0ba3-4bae-976e-7622e4fe39e0", "brandGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId", "authorizerAccessToken", 0L, "authorizerRefreshToken",
                "funcInfo", "nickName", "headImg", 0, 0, "userName", "principalName", "alias", "qrcodeUrl", "signature",
                "unBandUserGuid", "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0,
                "templateMsgId", false, "operSubjectGuid");
        when(mockWxStoreAuthorizerInfoService.getById("brandGuid")).thenReturn(wxStoreAuthorizerInfoDO);

        when(mockWeChatConfig.getBaseJumpUrl()).thenReturn("result");
        when(mockWxThirdOpenConfig.getWxOpenComponentService()).thenReturn(null);

        // Run the test
        final String result = wxOpenMessageServiceImplUnderTest.newMemberLogin(wxPortalReqDTO);

        // Verify the results
        assertEquals("result", result);
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
    }

    @Test
    public void testSendMemberMsg1() throws Exception {
        // Setup
        final WxMemberConsumeMsgDTO wxMemberConsumeMsgDTO = WxMemberConsumeMsgDTO.builder()
                .openId("openId")
                .brandGuid("brandGuid")
                .storeGuid("storeGuid")
                .orderGuid("orderGuid")
                .first("value")
                .remark("value")
                .url("queueDetailUrl")
                .wxTemplateTypeEnum(WxTemplateTypeEnum.QUEUE_TEMPLATE)
                .args(new String[]{"args"})
                .keywordValues(Arrays.asList("value"))
                .build();

        // Configure WxStoreAuthorizerInfoService.getById(...).
        final WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = new WxStoreAuthorizerInfoDO(0L,
                "9bf5fc12-0ba3-4bae-976e-7622e4fe39e0", "brandGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId", "authorizerAccessToken", 0L, "authorizerRefreshToken",
                "funcInfo", "nickName", "headImg", 0, 0, "userName", "principalName", "alias", "qrcodeUrl", "signature",
                "unBandUserGuid", "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0,
                "templateMsgId", false, "operSubjectGuid");
        when(mockWxStoreAuthorizerInfoService.getById("brandGuid")).thenReturn(wxStoreAuthorizerInfoDO);

        // Configure WxMpTemplateService.getOne(...).
        final WxMpTemplateDO wxMpTemplateDO = WxMpTemplateDO.builder()
                .id(0L)
                .guid("ada0a2e8-e23f-4dd4-ba33-b6eccc0be0f5")
                .shortId("shortId")
                .templateId("templateId")
                .appId("appId")
                .BrandGuid("brandGuid")
                .type(0)
                .build();
        when(mockWxMpTemplateService.getOne(any(LambdaQueryWrapper.class))).thenReturn(wxMpTemplateDO);

        when(mockWeChatConfig.getMEMBER_ORDER_PAGE()).thenReturn("result");
        when(mockWxSaasMpService.getWxMpService(
                new WxStoreAuthorizerInfoDO(0L, "9bf5fc12-0ba3-4bae-976e-7622e4fe39e0", "brandGuid",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId",
                        "authorizerAccessToken", 0L, "authorizerRefreshToken", "funcInfo", "nickName", "headImg", 0, 0,
                        "userName", "principalName", "alias", "qrcodeUrl", "signature", "unBandUserGuid",
                        "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, "templateMsgId",
                        false, "operSubjectGuid"))).thenReturn(null);

        // Run the test
        wxOpenMessageServiceImplUnderTest.sendMemberMsg(wxMemberConsumeMsgDTO);

        // Verify the results
    }

    @Test
    public void testSendMemberMsg1_WxMpTemplateServiceReturnsNull() {
        // Setup
        final WxMemberConsumeMsgDTO wxMemberConsumeMsgDTO = WxMemberConsumeMsgDTO.builder()
                .openId("openId")
                .brandGuid("brandGuid")
                .storeGuid("storeGuid")
                .orderGuid("orderGuid")
                .first("value")
                .remark("value")
                .url("queueDetailUrl")
                .wxTemplateTypeEnum(WxTemplateTypeEnum.QUEUE_TEMPLATE)
                .args(new String[]{"args"})
                .keywordValues(Arrays.asList("value"))
                .build();

        // Configure WxStoreAuthorizerInfoService.getById(...).
        final WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = new WxStoreAuthorizerInfoDO(0L,
                "9bf5fc12-0ba3-4bae-976e-7622e4fe39e0", "brandGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId", "authorizerAccessToken", 0L, "authorizerRefreshToken",
                "funcInfo", "nickName", "headImg", 0, 0, "userName", "principalName", "alias", "qrcodeUrl", "signature",
                "unBandUserGuid", "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0,
                "templateMsgId", false, "operSubjectGuid");
        when(mockWxStoreAuthorizerInfoService.getById("brandGuid")).thenReturn(wxStoreAuthorizerInfoDO);

        when(mockWxMpTemplateService.getOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Run the test
        wxOpenMessageServiceImplUnderTest.sendMemberMsg(wxMemberConsumeMsgDTO);

        // Verify the results
    }

    @Test
    public void testSendMemberMsg1_WxSaasMpServiceThrowsWxErrorException() throws Exception {
        // Setup
        final WxMemberConsumeMsgDTO wxMemberConsumeMsgDTO = WxMemberConsumeMsgDTO.builder()
                .openId("openId")
                .brandGuid("brandGuid")
                .storeGuid("storeGuid")
                .orderGuid("orderGuid")
                .first("value")
                .remark("value")
                .url("queueDetailUrl")
                .wxTemplateTypeEnum(WxTemplateTypeEnum.QUEUE_TEMPLATE)
                .args(new String[]{"args"})
                .keywordValues(Arrays.asList("value"))
                .build();

        // Configure WxStoreAuthorizerInfoService.getById(...).
        final WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = new WxStoreAuthorizerInfoDO(0L,
                "9bf5fc12-0ba3-4bae-976e-7622e4fe39e0", "brandGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId", "authorizerAccessToken", 0L, "authorizerRefreshToken",
                "funcInfo", "nickName", "headImg", 0, 0, "userName", "principalName", "alias", "qrcodeUrl", "signature",
                "unBandUserGuid", "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0,
                "templateMsgId", false, "operSubjectGuid");
        when(mockWxStoreAuthorizerInfoService.getById("brandGuid")).thenReturn(wxStoreAuthorizerInfoDO);

        // Configure WxMpTemplateService.getOne(...).
        final WxMpTemplateDO wxMpTemplateDO = WxMpTemplateDO.builder()
                .id(0L)
                .guid("ada0a2e8-e23f-4dd4-ba33-b6eccc0be0f5")
                .shortId("shortId")
                .templateId("templateId")
                .appId("appId")
                .BrandGuid("brandGuid")
                .type(0)
                .build();
        when(mockWxMpTemplateService.getOne(any(LambdaQueryWrapper.class))).thenReturn(wxMpTemplateDO);

        when(mockWeChatConfig.getMEMBER_ORDER_PAGE()).thenReturn("result");
        when(mockWxSaasMpService.getWxMpService(
                new WxStoreAuthorizerInfoDO(0L, "9bf5fc12-0ba3-4bae-976e-7622e4fe39e0", "brandGuid",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId",
                        "authorizerAccessToken", 0L, "authorizerRefreshToken", "funcInfo", "nickName", "headImg", 0, 0,
                        "userName", "principalName", "alias", "qrcodeUrl", "signature", "unBandUserGuid",
                        "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, "templateMsgId",
                        false, "operSubjectGuid"))).thenThrow(WxErrorException.class);

        // Run the test
        wxOpenMessageServiceImplUnderTest.sendMemberMsg(wxMemberConsumeMsgDTO);

        // Verify the results
    }

    @Test
    public void testSendMemberMsgNew() throws Exception {
        // Setup
        final WxMemberConsumeMsgDTO wxMemberConsumeMsgDTO = WxMemberConsumeMsgDTO.builder()
                .openId("openId")
                .brandGuid("brandGuid")
                .storeGuid("storeGuid")
                .orderGuid("orderGuid")
                .first("value")
                .remark("value")
                .url("queueDetailUrl")
                .wxTemplateTypeEnum(WxTemplateTypeEnum.QUEUE_TEMPLATE)
                .args(new String[]{"args"})
                .keywordValues(Arrays.asList("value"))
                .build();

        // Configure WxStoreAuthorizerInfoService.getById(...).
        final WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = new WxStoreAuthorizerInfoDO(0L,
                "9bf5fc12-0ba3-4bae-976e-7622e4fe39e0", "brandGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId", "authorizerAccessToken", 0L, "authorizerRefreshToken",
                "funcInfo", "nickName", "headImg", 0, 0, "userName", "principalName", "alias", "qrcodeUrl", "signature",
                "unBandUserGuid", "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0,
                "templateMsgId", false, "operSubjectGuid");
        when(mockWxStoreAuthorizerInfoService.getById("brandGuid")).thenReturn(wxStoreAuthorizerInfoDO);

        // Configure WxMpTemplateService.list(...).
        final List<WxMpTemplateDO> wxMpTemplateDOS = Arrays.asList(WxMpTemplateDO.builder()
                .id(0L)
                .guid("ada0a2e8-e23f-4dd4-ba33-b6eccc0be0f5")
                .shortId("shortId")
                .templateId("templateId")
                .appId("appId")
                .BrandGuid("brandGuid")
                .type(0)
                .build());
        when(mockWxMpTemplateService.list(any(LambdaQueryWrapper.class))).thenReturn(wxMpTemplateDOS);

        when(mockWxSaasMpService.getWxMpService(
                new WxStoreAuthorizerInfoDO(0L, "9bf5fc12-0ba3-4bae-976e-7622e4fe39e0", "brandGuid",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId",
                        "authorizerAccessToken", 0L, "authorizerRefreshToken", "funcInfo", "nickName", "headImg", 0, 0,
                        "userName", "principalName", "alias", "qrcodeUrl", "signature", "unBandUserGuid",
                        "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, "templateMsgId",
                        false, "operSubjectGuid"))).thenReturn(null);
        when(mockWxMpTemplateService.save(WxMpTemplateDO.builder()
                .id(0L)
                .guid("ada0a2e8-e23f-4dd4-ba33-b6eccc0be0f5")
                .shortId("shortId")
                .templateId("templateId")
                .appId("appId")
                .BrandGuid("brandGuid")
                .type(0)
                .build())).thenReturn(false);

        // Run the test
        final Result<String> result = wxOpenMessageServiceImplUnderTest.sendMemberMsgNew(wxMemberConsumeMsgDTO);

        // Verify the results
    }

    @Test
    public void testSendMemberMsgNew_WxMpTemplateServiceListReturnsNoItems() throws Exception {
        // Setup
        final WxMemberConsumeMsgDTO wxMemberConsumeMsgDTO = WxMemberConsumeMsgDTO.builder()
                .openId("openId")
                .brandGuid("brandGuid")
                .storeGuid("storeGuid")
                .orderGuid("orderGuid")
                .first("value")
                .remark("value")
                .url("queueDetailUrl")
                .wxTemplateTypeEnum(WxTemplateTypeEnum.QUEUE_TEMPLATE)
                .args(new String[]{"args"})
                .keywordValues(Arrays.asList("value"))
                .build();

        // Configure WxStoreAuthorizerInfoService.getById(...).
        final WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = new WxStoreAuthorizerInfoDO(0L,
                "9bf5fc12-0ba3-4bae-976e-7622e4fe39e0", "brandGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId", "authorizerAccessToken", 0L, "authorizerRefreshToken",
                "funcInfo", "nickName", "headImg", 0, 0, "userName", "principalName", "alias", "qrcodeUrl", "signature",
                "unBandUserGuid", "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0,
                "templateMsgId", false, "operSubjectGuid");
        when(mockWxStoreAuthorizerInfoService.getById("brandGuid")).thenReturn(wxStoreAuthorizerInfoDO);

        when(mockWxMpTemplateService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());
        when(mockWxSaasMpService.getWxMpService(
                new WxStoreAuthorizerInfoDO(0L, "9bf5fc12-0ba3-4bae-976e-7622e4fe39e0", "brandGuid",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId",
                        "authorizerAccessToken", 0L, "authorizerRefreshToken", "funcInfo", "nickName", "headImg", 0, 0,
                        "userName", "principalName", "alias", "qrcodeUrl", "signature", "unBandUserGuid",
                        "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, "templateMsgId",
                        false, "operSubjectGuid"))).thenReturn(null);
        when(mockWxMpTemplateService.save(WxMpTemplateDO.builder()
                .id(0L)
                .guid("ada0a2e8-e23f-4dd4-ba33-b6eccc0be0f5")
                .shortId("shortId")
                .templateId("templateId")
                .appId("appId")
                .BrandGuid("brandGuid")
                .type(0)
                .build())).thenReturn(false);

        // Run the test
        final Result<String> result = wxOpenMessageServiceImplUnderTest.sendMemberMsgNew(wxMemberConsumeMsgDTO);

        // Verify the results
    }

    @Test
    public void testSendMemberMsgNew_WxSaasMpServiceThrowsWxErrorException() throws Exception {
        // Setup
        final WxMemberConsumeMsgDTO wxMemberConsumeMsgDTO = WxMemberConsumeMsgDTO.builder()
                .openId("openId")
                .brandGuid("brandGuid")
                .storeGuid("storeGuid")
                .orderGuid("orderGuid")
                .first("value")
                .remark("value")
                .url("queueDetailUrl")
                .wxTemplateTypeEnum(WxTemplateTypeEnum.QUEUE_TEMPLATE)
                .args(new String[]{"args"})
                .keywordValues(Arrays.asList("value"))
                .build();

        // Configure WxStoreAuthorizerInfoService.getById(...).
        final WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = new WxStoreAuthorizerInfoDO(0L,
                "9bf5fc12-0ba3-4bae-976e-7622e4fe39e0", "brandGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId", "authorizerAccessToken", 0L, "authorizerRefreshToken",
                "funcInfo", "nickName", "headImg", 0, 0, "userName", "principalName", "alias", "qrcodeUrl", "signature",
                "unBandUserGuid", "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0,
                "templateMsgId", false, "operSubjectGuid");
        when(mockWxStoreAuthorizerInfoService.getById("brandGuid")).thenReturn(wxStoreAuthorizerInfoDO);

        // Configure WxMpTemplateService.list(...).
        final List<WxMpTemplateDO> wxMpTemplateDOS = Arrays.asList(WxMpTemplateDO.builder()
                .id(0L)
                .guid("ada0a2e8-e23f-4dd4-ba33-b6eccc0be0f5")
                .shortId("shortId")
                .templateId("templateId")
                .appId("appId")
                .BrandGuid("brandGuid")
                .type(0)
                .build());
        when(mockWxMpTemplateService.list(any(LambdaQueryWrapper.class))).thenReturn(wxMpTemplateDOS);

        when(mockWxSaasMpService.getWxMpService(
                new WxStoreAuthorizerInfoDO(0L, "9bf5fc12-0ba3-4bae-976e-7622e4fe39e0", "brandGuid",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId",
                        "authorizerAccessToken", 0L, "authorizerRefreshToken", "funcInfo", "nickName", "headImg", 0, 0,
                        "userName", "principalName", "alias", "qrcodeUrl", "signature", "unBandUserGuid",
                        "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, "templateMsgId",
                        false, "operSubjectGuid"))).thenThrow(WxErrorException.class);

        // Run the test
        final Result<String> result = wxOpenMessageServiceImplUnderTest.sendMemberMsgNew(wxMemberConsumeMsgDTO);

        // Verify the results
    }

    @Test
    public void testGetWxSubject() {
        // Setup
        final WxSubjectReqDTO wxSubjectReqDTO = WxSubjectReqDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .appId("appId")
                .build();
        final WxSubjectRespDTO expectedResult = new WxSubjectRespDTO(false, "operSubjectGuid", false);

        // Configure WxStoreAuthorizerInfoService.getByAppId(...).
        final WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = new WxStoreAuthorizerInfoDO(0L,
                "9bf5fc12-0ba3-4bae-976e-7622e4fe39e0", "brandGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId", "authorizerAccessToken", 0L, "authorizerRefreshToken",
                "funcInfo", "nickName", "headImg", 0, 0, "userName", "principalName", "alias", "qrcodeUrl", "signature",
                "unBandUserGuid", "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0,
                "templateMsgId", false, "operSubjectGuid");
        when(mockWxStoreAuthorizerInfoService.getByAppId("appId")).thenReturn(wxStoreAuthorizerInfoDO);

        // Configure EnterpriseClientService.list(...).
        final MultiMemberDTO multiMemberDTO = new MultiMemberDTO();
        multiMemberDTO.setId(0L);
        multiMemberDTO.setEnterpriseGuid("enterpriseGuid");
        multiMemberDTO.setMultiMemberGuid("multiMemberGuid");
        multiMemberDTO.setMultiMemberName("multiMemberName");
        multiMemberDTO.setEnabled(false);
        final List<MultiMemberDTO> multiMemberDTOS = Arrays.asList(multiMemberDTO);
        when(mockEnterpriseClientService.list(new HashSet<>(Arrays.asList("value")))).thenReturn(multiMemberDTOS);

        // Run the test
        final WxSubjectRespDTO result = wxOpenMessageServiceImplUnderTest.getWxSubject(wxSubjectReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetWxSubject_EnterpriseClientServiceReturnsNoItems() {
        // Setup
        final WxSubjectReqDTO wxSubjectReqDTO = WxSubjectReqDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .appId("appId")
                .build();
        final WxSubjectRespDTO expectedResult = new WxSubjectRespDTO(false, "operSubjectGuid", false);

        // Configure WxStoreAuthorizerInfoService.getByAppId(...).
        final WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = new WxStoreAuthorizerInfoDO(0L,
                "9bf5fc12-0ba3-4bae-976e-7622e4fe39e0", "brandGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId", "authorizerAccessToken", 0L, "authorizerRefreshToken",
                "funcInfo", "nickName", "headImg", 0, 0, "userName", "principalName", "alias", "qrcodeUrl", "signature",
                "unBandUserGuid", "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0,
                "templateMsgId", false, "operSubjectGuid");
        when(mockWxStoreAuthorizerInfoService.getByAppId("appId")).thenReturn(wxStoreAuthorizerInfoDO);

        when(mockEnterpriseClientService.list(new HashSet<>(Arrays.asList("value"))))
                .thenReturn(Collections.emptyList());

        // Run the test
        final WxSubjectRespDTO result = wxOpenMessageServiceImplUnderTest.getWxSubject(wxSubjectReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testSendCallMessage() throws Exception {
        // Setup
        final WxSendMessageReqDTO sendMessageReqDTO = new WxSendMessageReqDTO();
        final WxOrderReqDTO wxOrderReqDTO = new WxOrderReqDTO();
        wxOrderReqDTO.setOrderGuid("orderGuid");
        final WxOrderItemReqDTO wxOrderItemReqDTO = new WxOrderItemReqDTO();
        wxOrderItemReqDTO.setItemName("itemName");
        wxOrderItemReqDTO.setNumber(0);
        wxOrderReqDTO.setOrderItemList(Arrays.asList(wxOrderItemReqDTO));
        sendMessageReqDTO.setOrderList(Arrays.asList(wxOrderReqDTO));

        // Configure TradeClientService.listByOrderGuid(...).
        final List<OrderDTO> orderDTOS = Arrays.asList(OrderDTO.builder()
                .guid("333cde67-7280-484d-9aad-d2bc3e8860fa")
                .mark("value")
                .storeGuid("storeGuid")
                .storeName("value")
                .build());
        when(mockTradeClientService.listByOrderGuid(SingleDataDTO.builder()
                .datas(Arrays.asList("value"))
                .build())).thenReturn(orderDTOS);

        // Configure OrganizationClientService.queryStoreByGuid(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("ba4dec25-8f87-4e2b-a29d-e504b7562d5a");
        storeDTO.setCode("code");
        storeDTO.setName("value");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        when(mockOrganizationClientService.queryStoreByGuid("storeGuid")).thenReturn(storeDTO);

        // Configure WxStoreAuthorizerInfoService.getById(...).
        final WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = new WxStoreAuthorizerInfoDO(0L,
                "9bf5fc12-0ba3-4bae-976e-7622e4fe39e0", "brandGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId", "authorizerAccessToken", 0L, "authorizerRefreshToken",
                "funcInfo", "nickName", "headImg", 0, 0, "userName", "principalName", "alias", "qrcodeUrl", "signature",
                "unBandUserGuid", "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0,
                "templateMsgId", false, "operSubjectGuid");
        when(mockWxStoreAuthorizerInfoService.getById("belongBrandGuid")).thenReturn(wxStoreAuthorizerInfoDO);

        // Configure WxMpTemplateService.list(...).
        final List<WxMpTemplateDO> wxMpTemplateDOS = Arrays.asList(WxMpTemplateDO.builder()
                .id(0L)
                .guid("ada0a2e8-e23f-4dd4-ba33-b6eccc0be0f5")
                .shortId("shortId")
                .templateId("templateId")
                .appId("appId")
                .BrandGuid("brandGuid")
                .type(0)
                .build());
        when(mockWxMpTemplateService.list(any(LambdaQueryWrapper.class))).thenReturn(wxMpTemplateDOS);

        // Configure WxStoreMerchantOrderMapper.selectList(...).
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO.setId(0L);
        wxStoreMerchantOrderDO.setGuid("3fa2965e-b3f0-4559-80ea-ef7113d9d737");
        wxStoreMerchantOrderDO.setOpenId("openId");
        wxStoreMerchantOrderDO.setOrderGuid("orderGuid");
        wxStoreMerchantOrderDO.setOrderRecordGuid("orderRecordGuid");
        final List<WxStoreMerchantOrderDO> wxStoreMerchantOrderDOS = Arrays.asList(wxStoreMerchantOrderDO);
        when(mockWxStoreMerchantOrderMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(wxStoreMerchantOrderDOS);

        when(mockRedisUtils.get("key")).thenReturn("openId");
        when(mockWeChatConfig.getMEMBER_ORDER_PAGE()).thenReturn("result");
        when(mockWxSaasMpService.getWxMpService(
                new WxStoreAuthorizerInfoDO(0L, "9bf5fc12-0ba3-4bae-976e-7622e4fe39e0", "brandGuid",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId",
                        "authorizerAccessToken", 0L, "authorizerRefreshToken", "funcInfo", "nickName", "headImg", 0, 0,
                        "userName", "principalName", "alias", "qrcodeUrl", "signature", "unBandUserGuid",
                        "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, "templateMsgId",
                        false, "operSubjectGuid"))).thenReturn(null);

        // Run the test
        wxOpenMessageServiceImplUnderTest.sendCallMessage(sendMessageReqDTO);

        // Verify the results
    }

    @Test
    public void testSendCallMessage_TradeClientServiceReturnsNoItems() {
        // Setup
        final WxSendMessageReqDTO sendMessageReqDTO = new WxSendMessageReqDTO();
        final WxOrderReqDTO wxOrderReqDTO = new WxOrderReqDTO();
        wxOrderReqDTO.setOrderGuid("orderGuid");
        final WxOrderItemReqDTO wxOrderItemReqDTO = new WxOrderItemReqDTO();
        wxOrderItemReqDTO.setItemName("itemName");
        wxOrderItemReqDTO.setNumber(0);
        wxOrderReqDTO.setOrderItemList(Arrays.asList(wxOrderItemReqDTO));
        sendMessageReqDTO.setOrderList(Arrays.asList(wxOrderReqDTO));

        when(mockTradeClientService.listByOrderGuid(SingleDataDTO.builder()
                .datas(Arrays.asList("value"))
                .build())).thenReturn(Collections.emptyList());

        // Run the test
        wxOpenMessageServiceImplUnderTest.sendCallMessage(sendMessageReqDTO);

        // Verify the results
    }

    @Test
    public void testSendCallMessage_WxMpTemplateServiceReturnsNoItems() throws Exception {
        // Setup
        final WxSendMessageReqDTO sendMessageReqDTO = new WxSendMessageReqDTO();
        final WxOrderReqDTO wxOrderReqDTO = new WxOrderReqDTO();
        wxOrderReqDTO.setOrderGuid("orderGuid");
        final WxOrderItemReqDTO wxOrderItemReqDTO = new WxOrderItemReqDTO();
        wxOrderItemReqDTO.setItemName("itemName");
        wxOrderItemReqDTO.setNumber(0);
        wxOrderReqDTO.setOrderItemList(Arrays.asList(wxOrderItemReqDTO));
        sendMessageReqDTO.setOrderList(Arrays.asList(wxOrderReqDTO));

        // Configure TradeClientService.listByOrderGuid(...).
        final List<OrderDTO> orderDTOS = Arrays.asList(OrderDTO.builder()
                .guid("333cde67-7280-484d-9aad-d2bc3e8860fa")
                .mark("value")
                .storeGuid("storeGuid")
                .storeName("value")
                .build());
        when(mockTradeClientService.listByOrderGuid(SingleDataDTO.builder()
                .datas(Arrays.asList("value"))
                .build())).thenReturn(orderDTOS);

        // Configure OrganizationClientService.queryStoreByGuid(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("ba4dec25-8f87-4e2b-a29d-e504b7562d5a");
        storeDTO.setCode("code");
        storeDTO.setName("value");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        when(mockOrganizationClientService.queryStoreByGuid("storeGuid")).thenReturn(storeDTO);

        // Configure WxStoreAuthorizerInfoService.getById(...).
        final WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = new WxStoreAuthorizerInfoDO(0L,
                "9bf5fc12-0ba3-4bae-976e-7622e4fe39e0", "brandGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId", "authorizerAccessToken", 0L, "authorizerRefreshToken",
                "funcInfo", "nickName", "headImg", 0, 0, "userName", "principalName", "alias", "qrcodeUrl", "signature",
                "unBandUserGuid", "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0,
                "templateMsgId", false, "operSubjectGuid");
        when(mockWxStoreAuthorizerInfoService.getById("belongBrandGuid")).thenReturn(wxStoreAuthorizerInfoDO);

        when(mockWxMpTemplateService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure WxStoreMerchantOrderMapper.selectList(...).
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO.setId(0L);
        wxStoreMerchantOrderDO.setGuid("3fa2965e-b3f0-4559-80ea-ef7113d9d737");
        wxStoreMerchantOrderDO.setOpenId("openId");
        wxStoreMerchantOrderDO.setOrderGuid("orderGuid");
        wxStoreMerchantOrderDO.setOrderRecordGuid("orderRecordGuid");
        final List<WxStoreMerchantOrderDO> wxStoreMerchantOrderDOS = Arrays.asList(wxStoreMerchantOrderDO);
        when(mockWxStoreMerchantOrderMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(wxStoreMerchantOrderDOS);

        when(mockRedisUtils.get("key")).thenReturn("openId");
        when(mockWeChatConfig.getMEMBER_ORDER_PAGE()).thenReturn("result");
        when(mockWxSaasMpService.getWxMpService(
                new WxStoreAuthorizerInfoDO(0L, "9bf5fc12-0ba3-4bae-976e-7622e4fe39e0", "brandGuid",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId",
                        "authorizerAccessToken", 0L, "authorizerRefreshToken", "funcInfo", "nickName", "headImg", 0, 0,
                        "userName", "principalName", "alias", "qrcodeUrl", "signature", "unBandUserGuid",
                        "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, "templateMsgId",
                        false, "operSubjectGuid"))).thenReturn(null);

        // Run the test
        wxOpenMessageServiceImplUnderTest.sendCallMessage(sendMessageReqDTO);

        // Verify the results
    }

    @Test
    public void testSendCallMessage_WxStoreMerchantOrderMapperReturnsNoItems() {
        // Setup
        final WxSendMessageReqDTO sendMessageReqDTO = new WxSendMessageReqDTO();
        final WxOrderReqDTO wxOrderReqDTO = new WxOrderReqDTO();
        wxOrderReqDTO.setOrderGuid("orderGuid");
        final WxOrderItemReqDTO wxOrderItemReqDTO = new WxOrderItemReqDTO();
        wxOrderItemReqDTO.setItemName("itemName");
        wxOrderItemReqDTO.setNumber(0);
        wxOrderReqDTO.setOrderItemList(Arrays.asList(wxOrderItemReqDTO));
        sendMessageReqDTO.setOrderList(Arrays.asList(wxOrderReqDTO));

        // Configure TradeClientService.listByOrderGuid(...).
        final List<OrderDTO> orderDTOS = Arrays.asList(OrderDTO.builder()
                .guid("333cde67-7280-484d-9aad-d2bc3e8860fa")
                .mark("value")
                .storeGuid("storeGuid")
                .storeName("value")
                .build());
        when(mockTradeClientService.listByOrderGuid(SingleDataDTO.builder()
                .datas(Arrays.asList("value"))
                .build())).thenReturn(orderDTOS);

        // Configure OrganizationClientService.queryStoreByGuid(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("ba4dec25-8f87-4e2b-a29d-e504b7562d5a");
        storeDTO.setCode("code");
        storeDTO.setName("value");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        when(mockOrganizationClientService.queryStoreByGuid("storeGuid")).thenReturn(storeDTO);

        // Configure WxStoreAuthorizerInfoService.getById(...).
        final WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = new WxStoreAuthorizerInfoDO(0L,
                "9bf5fc12-0ba3-4bae-976e-7622e4fe39e0", "brandGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId", "authorizerAccessToken", 0L, "authorizerRefreshToken",
                "funcInfo", "nickName", "headImg", 0, 0, "userName", "principalName", "alias", "qrcodeUrl", "signature",
                "unBandUserGuid", "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0,
                "templateMsgId", false, "operSubjectGuid");
        when(mockWxStoreAuthorizerInfoService.getById("belongBrandGuid")).thenReturn(wxStoreAuthorizerInfoDO);

        // Configure WxMpTemplateService.list(...).
        final List<WxMpTemplateDO> wxMpTemplateDOS = Arrays.asList(WxMpTemplateDO.builder()
                .id(0L)
                .guid("ada0a2e8-e23f-4dd4-ba33-b6eccc0be0f5")
                .shortId("shortId")
                .templateId("templateId")
                .appId("appId")
                .BrandGuid("brandGuid")
                .type(0)
                .build());
        when(mockWxMpTemplateService.list(any(LambdaQueryWrapper.class))).thenReturn(wxMpTemplateDOS);

        when(mockWxStoreMerchantOrderMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        wxOpenMessageServiceImplUnderTest.sendCallMessage(sendMessageReqDTO);

        // Verify the results
    }

    @Test
    public void testSendCallMessage_WxSaasMpServiceThrowsWxErrorException() throws Exception {
        // Setup
        final WxSendMessageReqDTO sendMessageReqDTO = new WxSendMessageReqDTO();
        final WxOrderReqDTO wxOrderReqDTO = new WxOrderReqDTO();
        wxOrderReqDTO.setOrderGuid("orderGuid");
        final WxOrderItemReqDTO wxOrderItemReqDTO = new WxOrderItemReqDTO();
        wxOrderItemReqDTO.setItemName("itemName");
        wxOrderItemReqDTO.setNumber(0);
        wxOrderReqDTO.setOrderItemList(Arrays.asList(wxOrderItemReqDTO));
        sendMessageReqDTO.setOrderList(Arrays.asList(wxOrderReqDTO));

        // Configure TradeClientService.listByOrderGuid(...).
        final List<OrderDTO> orderDTOS = Arrays.asList(OrderDTO.builder()
                .guid("333cde67-7280-484d-9aad-d2bc3e8860fa")
                .mark("value")
                .storeGuid("storeGuid")
                .storeName("value")
                .build());
        when(mockTradeClientService.listByOrderGuid(SingleDataDTO.builder()
                .datas(Arrays.asList("value"))
                .build())).thenReturn(orderDTOS);

        // Configure OrganizationClientService.queryStoreByGuid(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("ba4dec25-8f87-4e2b-a29d-e504b7562d5a");
        storeDTO.setCode("code");
        storeDTO.setName("value");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        when(mockOrganizationClientService.queryStoreByGuid("storeGuid")).thenReturn(storeDTO);

        // Configure WxStoreAuthorizerInfoService.getById(...).
        final WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = new WxStoreAuthorizerInfoDO(0L,
                "9bf5fc12-0ba3-4bae-976e-7622e4fe39e0", "brandGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId", "authorizerAccessToken", 0L, "authorizerRefreshToken",
                "funcInfo", "nickName", "headImg", 0, 0, "userName", "principalName", "alias", "qrcodeUrl", "signature",
                "unBandUserGuid", "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0,
                "templateMsgId", false, "operSubjectGuid");
        when(mockWxStoreAuthorizerInfoService.getById("belongBrandGuid")).thenReturn(wxStoreAuthorizerInfoDO);

        // Configure WxMpTemplateService.list(...).
        final List<WxMpTemplateDO> wxMpTemplateDOS = Arrays.asList(WxMpTemplateDO.builder()
                .id(0L)
                .guid("ada0a2e8-e23f-4dd4-ba33-b6eccc0be0f5")
                .shortId("shortId")
                .templateId("templateId")
                .appId("appId")
                .BrandGuid("brandGuid")
                .type(0)
                .build());
        when(mockWxMpTemplateService.list(any(LambdaQueryWrapper.class))).thenReturn(wxMpTemplateDOS);

        // Configure WxStoreMerchantOrderMapper.selectList(...).
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO.setId(0L);
        wxStoreMerchantOrderDO.setGuid("3fa2965e-b3f0-4559-80ea-ef7113d9d737");
        wxStoreMerchantOrderDO.setOpenId("openId");
        wxStoreMerchantOrderDO.setOrderGuid("orderGuid");
        wxStoreMerchantOrderDO.setOrderRecordGuid("orderRecordGuid");
        final List<WxStoreMerchantOrderDO> wxStoreMerchantOrderDOS = Arrays.asList(wxStoreMerchantOrderDO);
        when(mockWxStoreMerchantOrderMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(wxStoreMerchantOrderDOS);

        when(mockRedisUtils.get("key")).thenReturn("openId");
        when(mockWeChatConfig.getMEMBER_ORDER_PAGE()).thenReturn("result");
        when(mockWxSaasMpService.getWxMpService(
                new WxStoreAuthorizerInfoDO(0L, "9bf5fc12-0ba3-4bae-976e-7622e4fe39e0", "brandGuid",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId",
                        "authorizerAccessToken", 0L, "authorizerRefreshToken", "funcInfo", "nickName", "headImg", 0, 0,
                        "userName", "principalName", "alias", "qrcodeUrl", "signature", "unBandUserGuid",
                        "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, "templateMsgId",
                        false, "operSubjectGuid"))).thenThrow(WxErrorException.class);

        // Run the test
        wxOpenMessageServiceImplUnderTest.sendCallMessage(sendMessageReqDTO);

        // Verify the results
    }

    @Test
    public void testSendMemberMsg3() throws Exception {
        // Setup
        final WxMpTemplateMessage wxMpTemplateMessage = new WxMpTemplateMessage("toUser", "templateId", "url",
                new WxMpTemplateMessage.MiniProgram("appid", "pagePath", false),
                Arrays.asList(new WxMpTemplateData("name", "value", "color")));

        // Configure WxStoreAuthorizerInfoService.getById(...).
        final WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = new WxStoreAuthorizerInfoDO(0L,
                "9bf5fc12-0ba3-4bae-976e-7622e4fe39e0", "brandGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId", "authorizerAccessToken", 0L, "authorizerRefreshToken",
                "funcInfo", "nickName", "headImg", 0, 0, "userName", "principalName", "alias", "qrcodeUrl", "signature",
                "unBandUserGuid", "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0,
                "templateMsgId", false, "operSubjectGuid");
        when(mockWxStoreAuthorizerInfoService.getById("brandGuid")).thenReturn(wxStoreAuthorizerInfoDO);

        // Configure WxMpTemplateService.list(...).
        final List<WxMpTemplateDO> wxMpTemplateDOS = Arrays.asList(WxMpTemplateDO.builder()
                .id(0L)
                .guid("ada0a2e8-e23f-4dd4-ba33-b6eccc0be0f5")
                .shortId("shortId")
                .templateId("templateId")
                .appId("appId")
                .BrandGuid("brandGuid")
                .type(0)
                .build());
        when(mockWxMpTemplateService.list(any(LambdaQueryWrapper.class))).thenReturn(wxMpTemplateDOS);

        when(mockWxSaasMpService.getWxMpService(
                new WxStoreAuthorizerInfoDO(0L, "9bf5fc12-0ba3-4bae-976e-7622e4fe39e0", "brandGuid",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId",
                        "authorizerAccessToken", 0L, "authorizerRefreshToken", "funcInfo", "nickName", "headImg", 0, 0,
                        "userName", "principalName", "alias", "qrcodeUrl", "signature", "unBandUserGuid",
                        "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, "templateMsgId",
                        false, "operSubjectGuid"))).thenReturn(null);
        when(mockWxMpTemplateService.save(WxMpTemplateDO.builder()
                .id(0L)
                .guid("ada0a2e8-e23f-4dd4-ba33-b6eccc0be0f5")
                .shortId("shortId")
                .templateId("templateId")
                .appId("appId")
                .BrandGuid("brandGuid")
                .type(0)
                .build())).thenReturn(false);

        // Run the test
        final Result<String> result = wxOpenMessageServiceImplUnderTest.sendMemberMsg("brandGuid", 0,
                wxMpTemplateMessage);

        // Verify the results
    }

    @Test
    public void testSendMemberMsg3_WxMpTemplateServiceListReturnsNoItems() throws Exception {
        // Setup
        final WxMpTemplateMessage wxMpTemplateMessage = new WxMpTemplateMessage("toUser", "templateId", "url",
                new WxMpTemplateMessage.MiniProgram("appid", "pagePath", false),
                Arrays.asList(new WxMpTemplateData("name", "value", "color")));

        // Configure WxStoreAuthorizerInfoService.getById(...).
        final WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = new WxStoreAuthorizerInfoDO(0L,
                "9bf5fc12-0ba3-4bae-976e-7622e4fe39e0", "brandGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId", "authorizerAccessToken", 0L, "authorizerRefreshToken",
                "funcInfo", "nickName", "headImg", 0, 0, "userName", "principalName", "alias", "qrcodeUrl", "signature",
                "unBandUserGuid", "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0,
                "templateMsgId", false, "operSubjectGuid");
        when(mockWxStoreAuthorizerInfoService.getById("brandGuid")).thenReturn(wxStoreAuthorizerInfoDO);

        when(mockWxMpTemplateService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());
        when(mockWxSaasMpService.getWxMpService(
                new WxStoreAuthorizerInfoDO(0L, "9bf5fc12-0ba3-4bae-976e-7622e4fe39e0", "brandGuid",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId",
                        "authorizerAccessToken", 0L, "authorizerRefreshToken", "funcInfo", "nickName", "headImg", 0, 0,
                        "userName", "principalName", "alias", "qrcodeUrl", "signature", "unBandUserGuid",
                        "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, "templateMsgId",
                        false, "operSubjectGuid"))).thenReturn(null);
        when(mockWxMpTemplateService.save(WxMpTemplateDO.builder()
                .id(0L)
                .guid("ada0a2e8-e23f-4dd4-ba33-b6eccc0be0f5")
                .shortId("shortId")
                .templateId("templateId")
                .appId("appId")
                .BrandGuid("brandGuid")
                .type(0)
                .build())).thenReturn(false);

        // Run the test
        final Result<String> result = wxOpenMessageServiceImplUnderTest.sendMemberMsg("brandGuid", 0,
                wxMpTemplateMessage);

        // Verify the results
    }

    @Test
    public void testSendMemberMsg3_WxSaasMpServiceThrowsWxErrorException() throws Exception {
        // Setup
        final WxMpTemplateMessage wxMpTemplateMessage = new WxMpTemplateMessage("toUser", "templateId", "url",
                new WxMpTemplateMessage.MiniProgram("appid", "pagePath", false),
                Arrays.asList(new WxMpTemplateData("name", "value", "color")));

        // Configure WxStoreAuthorizerInfoService.getById(...).
        final WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = new WxStoreAuthorizerInfoDO(0L,
                "9bf5fc12-0ba3-4bae-976e-7622e4fe39e0", "brandGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId", "authorizerAccessToken", 0L, "authorizerRefreshToken",
                "funcInfo", "nickName", "headImg", 0, 0, "userName", "principalName", "alias", "qrcodeUrl", "signature",
                "unBandUserGuid", "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0,
                "templateMsgId", false, "operSubjectGuid");
        when(mockWxStoreAuthorizerInfoService.getById("brandGuid")).thenReturn(wxStoreAuthorizerInfoDO);

        // Configure WxMpTemplateService.list(...).
        final List<WxMpTemplateDO> wxMpTemplateDOS = Arrays.asList(WxMpTemplateDO.builder()
                .id(0L)
                .guid("ada0a2e8-e23f-4dd4-ba33-b6eccc0be0f5")
                .shortId("shortId")
                .templateId("templateId")
                .appId("appId")
                .BrandGuid("brandGuid")
                .type(0)
                .build());
        when(mockWxMpTemplateService.list(any(LambdaQueryWrapper.class))).thenReturn(wxMpTemplateDOS);

        when(mockWxSaasMpService.getWxMpService(
                new WxStoreAuthorizerInfoDO(0L, "9bf5fc12-0ba3-4bae-976e-7622e4fe39e0", "brandGuid",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId",
                        "authorizerAccessToken", 0L, "authorizerRefreshToken", "funcInfo", "nickName", "headImg", 0, 0,
                        "userName", "principalName", "alias", "qrcodeUrl", "signature", "unBandUserGuid",
                        "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, "templateMsgId",
                        false, "operSubjectGuid"))).thenThrow(WxErrorException.class);

        // Run the test
        final Result<String> result = wxOpenMessageServiceImplUnderTest.sendMemberMsg("brandGuid", 0,
                wxMpTemplateMessage);

        // Verify the results
    }

    @Test
    public void testSendCard() {
        // Setup
        final WeChatCardMessageConfig cardMessageConfig = new WeChatCardMessageConfig();
        cardMessageConfig.setEnterpriseGuid("enterpriseGuid");
        cardMessageConfig.setStoreGuid("storeGuid");
        cardMessageConfig.setMiniAppid("miniAppid");
        cardMessageConfig.setPagepath("pagepath");
        cardMessageConfig.setThumbMediaId("thumbMediaId");
        cardMessageConfig.setTitle("title");

        // Run the test
        final boolean result = wxOpenMessageServiceImplUnderTest.sendCard("openid", "authorizerAccessToken",
                "pagepathParameters", "title", cardMessageConfig);

        // Verify the results
        assertFalse(result);
    }
}
