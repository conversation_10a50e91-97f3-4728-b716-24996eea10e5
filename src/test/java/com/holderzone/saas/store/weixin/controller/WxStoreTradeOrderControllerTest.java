package com.holderzone.saas.store.weixin.controller;

import com.holderzone.holder.saas.member.terminal.dto.member.request.RequestQueryStoreAndMemberAndCard;
import com.holderzone.holder.saas.member.terminal.dto.member.response.ResponseMemberAndCardInfoDTO;
import com.holderzone.holder.saas.member.terminal.dto.member.response.ResponseMemberInfoDTO;
import com.holderzone.holder.saas.member.wechat.dto.member.ResponseMemberInfoVolumeDetails;
import com.holderzone.saas.store.dto.order.request.dinein.CreateDineInOrderReqDTO;
import com.holderzone.saas.store.dto.weixin.SubmitReturnDTO;
import com.holderzone.saas.store.dto.weixin.WebSocketMessageDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreAdvanceConsumerReqDTO;
import com.holderzone.saas.store.dto.weixin.member.*;
import com.holderzone.saas.store.dto.weixin.req.WxPaidOrderDetailsReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreTradeOrderDetailsRespDTO;
import com.holderzone.saas.store.weixin.service.WxStoreOrderPayService;
import com.holderzone.saas.store.weixin.service.WxStoreTradeOrderService;
import com.holderzone.saas.store.weixin.service.rpc.member.MemberTerminalClientService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDateTime;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(WxStoreTradeOrderController.class)
public class WxStoreTradeOrderControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private WxStoreTradeOrderService mockWxStoreTradeOrderService;
    @MockBean
    private WxStoreOrderPayService mockWxStoreOrderPayService;
    @MockBean
    private MemberTerminalClientService mockMemberTerminalClientService;

    @Test
    public void testSubmitOrder() throws Exception {
        // Setup
        when(mockWxStoreTradeOrderService.submitOrder(WxStoreAdvanceConsumerReqDTO.builder().build()))
                .thenReturn(SubmitReturnDTO.builder().build());

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_order_provide/submit")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testTableOrderDetails() throws Exception {
        // Setup
        // Configure WxStoreTradeOrderService.getTableOrderDetails(...).
        final WebSocketMessageDTO webSocketMessageDTO = new WebSocketMessageDTO<>(0, 0, "content", 0, "errorMsg");
        when(mockWxStoreTradeOrderService.getTableOrderDetails(
                WxStoreAdvanceConsumerReqDTO.builder().build())).thenReturn(webSocketMessageDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_order_provide/table_details")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testOrderPay() throws Exception {
        // Setup
        // Configure WxStoreOrderPayService.orderPay(...).
        final WxStoreTradeOrderDetailsRespDTO wxStoreTradeOrderDetailsRespDTO = WxStoreTradeOrderDetailsRespDTO.builder().build();
        when(mockWxStoreOrderPayService.orderPay(WxStoreAdvanceConsumerReqDTO.builder().build()))
                .thenReturn(wxStoreTradeOrderDetailsRespDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_order_provide/order_pay")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testUpdateRemark() throws Exception {
        // Setup
        // Configure WxStoreTradeOrderService.updateRemark(...).
        final CreateDineInOrderReqDTO createDineInOrderReqDTO = new CreateDineInOrderReqDTO();
        createDineInOrderReqDTO.setGuid("ee473c5d-eb02-437c-ba90-08fb42a1362d");
        createDineInOrderReqDTO.setUserWxPublicOpenId("userWxPublicOpenId");
        createDineInOrderReqDTO.setRemark("remark");
        createDineInOrderReqDTO.setDiningTableGuid("diningTableGuid");
        createDineInOrderReqDTO.setDiningTableName("diningTableName");
        when(mockWxStoreTradeOrderService.updateRemark(createDineInOrderReqDTO)).thenReturn(false);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_order_provide/update_remark")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testUpdateRemark_WxStoreTradeOrderServiceReturnsTrue() throws Exception {
        // Setup
        // Configure WxStoreTradeOrderService.updateRemark(...).
        final CreateDineInOrderReqDTO createDineInOrderReqDTO = new CreateDineInOrderReqDTO();
        createDineInOrderReqDTO.setGuid("ee473c5d-eb02-437c-ba90-08fb42a1362d");
        createDineInOrderReqDTO.setUserWxPublicOpenId("userWxPublicOpenId");
        createDineInOrderReqDTO.setRemark("remark");
        createDineInOrderReqDTO.setDiningTableGuid("diningTableGuid");
        createDineInOrderReqDTO.setDiningTableName("diningTableName");
        when(mockWxStoreTradeOrderService.updateRemark(createDineInOrderReqDTO)).thenReturn(true);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_order_provide/update_remark")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testUpdateGuestCount() throws Exception {
        // Setup
        // Configure WxStoreTradeOrderService.updateGuestCount(...).
        final CreateDineInOrderReqDTO createDineInOrderReqDTO = new CreateDineInOrderReqDTO();
        createDineInOrderReqDTO.setGuid("ee473c5d-eb02-437c-ba90-08fb42a1362d");
        createDineInOrderReqDTO.setUserWxPublicOpenId("userWxPublicOpenId");
        createDineInOrderReqDTO.setRemark("remark");
        createDineInOrderReqDTO.setDiningTableGuid("diningTableGuid");
        createDineInOrderReqDTO.setDiningTableName("diningTableName");
        when(mockWxStoreTradeOrderService.updateGuestCount(createDineInOrderReqDTO)).thenReturn(false);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_order_provide/update_guest_count")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testUpdateGuestCount_WxStoreTradeOrderServiceReturnsTrue() throws Exception {
        // Setup
        // Configure WxStoreTradeOrderService.updateGuestCount(...).
        final CreateDineInOrderReqDTO createDineInOrderReqDTO = new CreateDineInOrderReqDTO();
        createDineInOrderReqDTO.setGuid("ee473c5d-eb02-437c-ba90-08fb42a1362d");
        createDineInOrderReqDTO.setUserWxPublicOpenId("userWxPublicOpenId");
        createDineInOrderReqDTO.setRemark("remark");
        createDineInOrderReqDTO.setDiningTableGuid("diningTableGuid");
        createDineInOrderReqDTO.setDiningTableName("diningTableName");
        when(mockWxStoreTradeOrderService.updateGuestCount(createDineInOrderReqDTO)).thenReturn(true);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_order_provide/update_guest_count")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testReleaseTableLock() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_order_provide/release_table_lock")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
        verify(mockWxStoreTradeOrderService).releaseTableLock(WxStoreAdvanceConsumerReqDTO.builder().build());
    }

    @Test
    public void testValidateCardAndVolume() throws Exception {
        // Setup
        when(mockWxStoreTradeOrderService.validateCardAndVolume(
                CardAndVolumeDiscountReqDTO.builder().build())).thenReturn(CardAndVolumeDTO.builder().build());

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_order_provide/validate_card")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testCardList() throws Exception {
        // Setup
        when(mockWxStoreTradeOrderService.cardList(WxMemberCardListReqDTO.builder().build()))
                .thenReturn(WxMemberCardRespDTO.builder().build());

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_order_provide/member_card_list")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testVolumeCodeList() throws Exception {
        // Setup
        when(mockWxStoreTradeOrderService.volumeCodeList(WxVolumeCodeReqDTO.builder().build()))
                .thenReturn(WxVolumeCodeRespDTO.builder().build());

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_order_provide/member_volume_list")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testVolumeCodeDetails() throws Exception {
        // Setup
        // Configure WxStoreTradeOrderService.volumeCodeDetails(...).
        final ResponseMemberInfoVolumeDetails responseMemberInfoVolumeDetails = new ResponseMemberInfoVolumeDetails();
        responseMemberInfoVolumeDetails.setMemberVolumeGuid("memberVolumeGuid");
        responseMemberInfoVolumeDetails.setVolumeInfoGuid("volumeInfoGuid");
        responseMemberInfoVolumeDetails.setVolumeInfoName("volumeInfoName");
        responseMemberInfoVolumeDetails.setVolumeSimpleDesc("volumeSimpleDesc");
        responseMemberInfoVolumeDetails.setVolumeStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockWxStoreTradeOrderService.volumeCodeDetails(WxVolumeCodeDetailsReqDTO.builder().build()))
                .thenReturn(responseMemberInfoVolumeDetails);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_order_provide/volume_details")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testOrderDetails() throws Exception {
        // Setup
        // Configure WxStoreTradeOrderService.orderDetails(...).
        final WebSocketMessageDTO webSocketMessageDTO = new WebSocketMessageDTO<>(0, 0, "content", 0, "errorMsg");
        when(mockWxStoreTradeOrderService.orderDetails(WxPaidOrderDetailsReqDTO.builder().build()))
                .thenReturn(webSocketMessageDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_order_provide/paid_order_details")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testExceptionTest() throws Exception {
        // Setup
        // Configure MemberTerminalClientService.getMemberInfoAndCard(...).
        final ResponseMemberAndCardInfoDTO responseMemberAndCardInfoDTO = new ResponseMemberAndCardInfoDTO();
        final ResponseMemberInfoDTO memberInfoDTO = new ResponseMemberInfoDTO();
        memberInfoDTO.setMemberQRCode("memberQRCode");
        memberInfoDTO.setMemberInfoGuid("memberInfoGuid");
        memberInfoDTO.setPhoneNum("phoneNum");
        memberInfoDTO.setNickName("nickName");
        responseMemberAndCardInfoDTO.setMemberInfoDTO(memberInfoDTO);
        final RequestQueryStoreAndMemberAndCard requestQueryStoreAndMemberAndCard = new RequestQueryStoreAndMemberAndCard();
        requestQueryStoreAndMemberAndCard.setPhoneNumOrCardNum("openId");
        requestQueryStoreAndMemberAndCard.setStoreGuid("storeGuid");
        requestQueryStoreAndMemberAndCard.setEnterpriseGuid("enterpriseGuid");
        requestQueryStoreAndMemberAndCard.setIsCurrentStoreCard(0);
        requestQueryStoreAndMemberAndCard.setCheckExpireGuid("checkExpireGuid");
        when(mockMemberTerminalClientService.getMemberInfoAndCard(requestQueryStoreAndMemberAndCard))
                .thenReturn(responseMemberAndCardInfoDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_order_provide/exception")
                        .param("enterpriseGuid", "enterpriseGuid")
                        .param("storeGuid", "storeGuid")
                        .param("openId", "openId")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("", true));
    }
}
