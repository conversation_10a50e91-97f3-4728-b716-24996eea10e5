package com.holderzone.saas.store.weixin.service.impl;

import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.weixin.entity.domain.WxQrCodeInfoDO;
import com.holderzone.saas.store.weixin.entity.query.WxQrCodeUrlQuery;
import com.holderzone.saas.store.weixin.service.WxStoreAuthorizerInfoService;
import com.holderzone.saas.store.weixin.service.rpc.OrganizationClientService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class WxQrCodeInfoServiceImplTest {

    @Mock
    private RedisUtils mockRedisUtils;
    @Mock
    private OrganizationClientService mockOrganizationClientService;
    @Mock
    private WxStoreAuthorizerInfoService mockWxStoreAuthorizerInfoService;

    private WxQrCodeInfoServiceImpl wxQrCodeInfoServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        wxQrCodeInfoServiceImplUnderTest = new WxQrCodeInfoServiceImpl();
        wxQrCodeInfoServiceImplUnderTest.redisUtils = mockRedisUtils;
        wxQrCodeInfoServiceImplUnderTest.organizationClientService = mockOrganizationClientService;
        wxQrCodeInfoServiceImplUnderTest.wxStoreAuthorizerInfoService = mockWxStoreAuthorizerInfoService;
    }

    @Test
    public void testGetSceneStr() {
        // Setup
        final WxQrCodeUrlQuery wxQrCodeUrlQuery = new WxQrCodeUrlQuery("enterpriseGuid", "storeGuid", "storeName",
                "areaGuid", "areaName", "tableGuid", "tableName", 0, "brandGuid", "appId", false, false,
                "myselfOpenId");
        when(mockRedisUtils.generateGuid("holder-saas-store-weixin:createScene"))
                .thenReturn("ee72f94e-93bc-42bb-a661-60491576449e");

        // Run the test
        final String result = wxQrCodeInfoServiceImplUnderTest.getSceneStr(wxQrCodeUrlQuery);

        // Verify the results
        assertEquals("result", result);
    }

    @Test
    public void testGetWxQrCodeInfoByGuid() {
        // Setup
        final WxQrCodeInfoDO expectedResult = new WxQrCodeInfoDO(0L, "ee72f94e-93bc-42bb-a661-60491576449e",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "storeGuid", "name",
                "areaGuid", "areaName", "tableGuid", "tableName", "brandGuid", 0, "appId");

        // Configure OrganizationClientService.queryStoreByGuid(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("3807cbd0-1042-4eff-8d43-69927ff59869");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        when(mockOrganizationClientService.queryStoreByGuid("storeGuid")).thenReturn(storeDTO);

        // Run the test
        final WxQrCodeInfoDO result = wxQrCodeInfoServiceImplUnderTest.getWxQrCodeInfoByGuid(
                "ac664cd9-ad24-471f-b9c6-0f0d64001f5e");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetCacheWxQrCodeInfoByGuid() {
        // Setup
        final WxQrCodeInfoDO expectedResult = new WxQrCodeInfoDO(0L, "ee72f94e-93bc-42bb-a661-60491576449e",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "storeGuid", "name",
                "areaGuid", "areaName", "tableGuid", "tableName", "brandGuid", 0, "appId");
        when(mockRedisUtils.get("key")).thenReturn("result");

        // Run the test
        final WxQrCodeInfoDO result = wxQrCodeInfoServiceImplUnderTest.getCacheWxQrCodeInfoByGuid(
                "f4d2517b-8ff4-4a26-8aa3-9d6693fd4a72");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetCacheWxQrCodeInfoByGuid_RedisUtilsGetReturnsNull() {
        // Setup
        final WxQrCodeInfoDO expectedResult = new WxQrCodeInfoDO(0L, "ee72f94e-93bc-42bb-a661-60491576449e",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "storeGuid", "name",
                "areaGuid", "areaName", "tableGuid", "tableName", "brandGuid", 0, "appId");
        when(mockRedisUtils.get("key")).thenReturn(null);

        // Configure OrganizationClientService.queryStoreByGuid(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("3807cbd0-1042-4eff-8d43-69927ff59869");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        when(mockOrganizationClientService.queryStoreByGuid("storeGuid")).thenReturn(storeDTO);

        // Run the test
        final WxQrCodeInfoDO result = wxQrCodeInfoServiceImplUnderTest.getCacheWxQrCodeInfoByGuid(
                "f4d2517b-8ff4-4a26-8aa3-9d6693fd4a72");

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockRedisUtils).setEx("key",
                new WxQrCodeInfoDO(0L, "ee72f94e-93bc-42bb-a661-60491576449e", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), "storeGuid", "name", "areaGuid", "areaName", "tableGuid",
                        "tableName", "brandGuid", 0, "appId"), 2L, TimeUnit.HOURS);
    }
}
