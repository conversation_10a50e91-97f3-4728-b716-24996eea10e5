package com.holderzone.saas.store.weixin.service.impl;

import com.google.common.util.concurrent.MoreExecutors;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.member.wechat.dto.card.RequestCardOwnedPage;
import com.holderzone.holder.saas.member.wechat.dto.member.*;
import com.holderzone.holder.saas.weixin.entry.dto.WxMemberSessionDTO;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import com.holderzone.saas.store.dto.weixin.deal.UserMemberCardCacheDTO;
import com.holderzone.saas.store.dto.weixin.deal.UserMemberSessionDTO;
import com.holderzone.saas.store.weixin.config.ResponseModel;
import com.holderzone.saas.store.weixin.entity.domain.WxUserRecordDO;
import com.holderzone.saas.store.weixin.mapper.WxUserRecordMapper;
import com.holderzone.saas.store.weixin.service.rpc.member.HsaBaseClientService;
import com.holderzone.saas.store.weixin.utils.DynamicHelper;
import com.holderzone.saas.store.weixin.utils.UserMemberSessionUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;

import static org.junit.Assert.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class WxUserRecordServiceImplTest {

    @Mock
    private RedisUtils mockRedisUtils;
    @Mock
    private DynamicHelper mockDynamicHelper;
    @Mock
    private UserMemberSessionUtils mockUserMemberSessionUtils;
    @Mock
    private HsaBaseClientService mockHsaBaseClientService;
    @Mock
    private WxUserRecordMapper mockWxUserRecordMapper;

    private WxUserRecordServiceImpl wxUserRecordServiceImplUnderTest;

    @Before
    public void setUp() {
        wxUserRecordServiceImplUnderTest = new WxUserRecordServiceImpl(mockRedisUtils, mockDynamicHelper,
                MoreExecutors.newDirectExecutorService(), mockUserMemberSessionUtils, mockHsaBaseClientService,
                mockWxUserRecordMapper);
    }

    @Test
    public void testUpdateUserLoginByOpenId() {
        // Setup
        final WxMemberSessionDTO memberSessionDTO = WxMemberSessionDTO.builder()
                .operSubjectGuid("operSubjectGuid")
                .enterpriseGuid("enterpriseGuid")
                .brandGuid("brandGuid")
                .storeGuid("storeGuid")
                .build();
        when(mockRedisUtils.generateGuid("WxUserRecord")).thenReturn("3226b8c9-c5a6-4b03-a26e-2196b8f7aeb8");

        // Run the test
        final Boolean result = wxUserRecordServiceImplUnderTest.updateUserLoginByOpenId("enterpriseGuid", "openId",
                false, memberSessionDTO);

        // Verify the results
        assertTrue(result);
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
    }

    @Test
    public void testUpdateUserLogin() {
        // Setup
        final WxStoreConsumerDTO wxStoreConsumerDTO = WxStoreConsumerDTO.builder()
                .openId("openId")
                .nickName("nickName")
                .headImgUrl("headImgUrl")
                .sex(0)
                .country("country")
                .province("province")
                .city("city")
                .enterpriseGuid("enterpriseGuid")
                .userGuid("3226b8c9-c5a6-4b03-a26e-2196b8f7aeb8")
                .isLogin(false)
                .phoneNum("phone")
                .operSubjectGuid("operSubjectGuid")
                .build();

        // Run the test
        final Boolean result = wxUserRecordServiceImplUnderTest.updateUserLogin(wxStoreConsumerDTO);

        // Verify the results
        assertTrue(result);
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
    }

    @Test
    public void testSaveOrUpdateUserInfo() {
        // Setup
        final WxStoreConsumerDTO wxStoreConsumerDTO = WxStoreConsumerDTO.builder()
                .openId("openId")
                .nickName("nickName")
                .headImgUrl("headImgUrl")
                .sex(0)
                .country("country")
                .province("province")
                .city("city")
                .enterpriseGuid("enterpriseGuid")
                .userGuid("3226b8c9-c5a6-4b03-a26e-2196b8f7aeb8")
                .isLogin(false)
                .phoneNum("phone")
                .operSubjectGuid("operSubjectGuid")
                .build();
        when(mockRedisUtils.setNx("key", "1", 30L)).thenReturn(false);

        // Run the test
        final WxUserRecordDO result = wxUserRecordServiceImplUnderTest.saveOrUpdateUserInfo(wxStoreConsumerDTO);

        // Verify the results
        assertNull(result);
        verify(mockRedisUtils).delete("saveOrUpdateUserInfo:");
    }

    @Test
    public void testSaveOrUpdateUserInfo_RedisUtilsSetNxReturnsTrue() {
        // Setup
        final WxStoreConsumerDTO wxStoreConsumerDTO = WxStoreConsumerDTO.builder()
                .openId("openId")
                .nickName("nickName")
                .headImgUrl("headImgUrl")
                .sex(0)
                .country("country")
                .province("province")
                .city("city")
                .enterpriseGuid("enterpriseGuid")
                .userGuid("3226b8c9-c5a6-4b03-a26e-2196b8f7aeb8")
                .isLogin(false)
                .phoneNum("phone")
                .operSubjectGuid("operSubjectGuid")
                .build();
        final WxUserRecordDO expectedResult = new WxUserRecordDO(0L, "3226b8c9-c5a6-4b03-a26e-2196b8f7aeb8", "openId",
                "nickName", "headImgUrl", 0, "country", "province", "city", "phone",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid",
                0);
        when(mockRedisUtils.setNx("key", "1", 30L)).thenReturn(true);
        when(mockRedisUtils.generateGuid("redisKey")).thenReturn("3226b8c9-c5a6-4b03-a26e-2196b8f7aeb8");

        // Run the test
        final WxUserRecordDO result = wxUserRecordServiceImplUnderTest.saveOrUpdateUserInfo(wxStoreConsumerDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockWxUserRecordMapper).deleteUserRecord("openId");
        verify(mockRedisUtils).delete("saveOrUpdateUserInfo:");
    }

    @Test
    public void testGetWxuserRecord() {
        // Setup
        final WxStoreConsumerDTO wxStoreConsumerDTO = WxStoreConsumerDTO.builder()
                .openId("openId")
                .nickName("nickName")
                .headImgUrl("headImgUrl")
                .sex(0)
                .country("country")
                .province("province")
                .city("city")
                .enterpriseGuid("enterpriseGuid")
                .userGuid("3226b8c9-c5a6-4b03-a26e-2196b8f7aeb8")
                .isLogin(false)
                .phoneNum("phone")
                .operSubjectGuid("operSubjectGuid")
                .build();
        final WxUserRecordDO expectedResult = new WxUserRecordDO(0L, "3226b8c9-c5a6-4b03-a26e-2196b8f7aeb8", "openId",
                "nickName", "headImgUrl", 0, "country", "province", "city", "phone",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid",
                0);

        // Run the test
        final WxUserRecordDO result = wxUserRecordServiceImplUnderTest.getWxuserRecord(wxStoreConsumerDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetOneByOpenId() {
        // Setup
        final WxUserRecordDO expectedResult = new WxUserRecordDO(0L, "3226b8c9-c5a6-4b03-a26e-2196b8f7aeb8", "openId",
                "nickName", "headImgUrl", 0, "country", "province", "city", "phone",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid",
                0);

        // Run the test
        final WxUserRecordDO result = wxUserRecordServiceImplUnderTest.getOneByOpenId("openId");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testBindPhoneNum() {
        assertFalse(wxUserRecordServiceImplUnderTest.bindPhoneNum("phoneNum"));
    }

    @Test
    public void testObtainByPhone() {
        // Setup
        final WxUserRecordDO expectedResult = new WxUserRecordDO(0L, "3226b8c9-c5a6-4b03-a26e-2196b8f7aeb8", "openId",
                "nickName", "headImgUrl", 0, "country", "province", "city", "phone",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid",
                0);

        // Run the test
        final WxUserRecordDO result = wxUserRecordServiceImplUnderTest.obtainByPhone("phone");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testUpdateUserInfo() {
        // Setup
        final WxUserRecordDO expectedResult = new WxUserRecordDO(0L, "3226b8c9-c5a6-4b03-a26e-2196b8f7aeb8", "openId",
                "nickName", "headImgUrl", 0, "country", "province", "city", "phone",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid",
                0);

        // Run the test
        final WxUserRecordDO result = wxUserRecordServiceImplUnderTest.updateUserInfo("openId");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testAsyncMemberInfo() {
        // Setup
        final WxMemberSessionDTO wxMemberSessionDTO = WxMemberSessionDTO.builder()
                .operSubjectGuid("operSubjectGuid")
                .enterpriseGuid("enterpriseGuid")
                .brandGuid("brandGuid")
                .storeGuid("storeGuid")
                .build();

        // Configure UserMemberSessionUtils.getUserMemberSession(...).
        final UserMemberSessionDTO userMemberSessionDTO = new UserMemberSessionDTO("storeGuid", "openId", "userGuid",
                false, "nickName", "memberInfoGuid", "cardGuid", "memberInfoCardGuid", "systemManagementGuid", 0, false,
                "volumeCode", false, new BigDecimal("0.00"), new BigDecimal("0.00"), "operSubjectGuid");
        when(mockUserMemberSessionUtils.getUserMemberSession("openId")).thenReturn(userMemberSessionDTO);

        // Configure HsaBaseClientService.getMemberCardByPage(...).
        final ResponseMemberCardListOwned responseMemberCardListOwned = new ResponseMemberCardListOwned();
        responseMemberCardListOwned.setCardQRCode("cardQRCode");
        responseMemberCardListOwned.setCardGuid("cardGuid");
        responseMemberCardListOwned.setMemberInfoCardGuid("memberInfoCardGuid");
        responseMemberCardListOwned.setSystemManagementGuid("systemManagementGuid");
        responseMemberCardListOwned.setEnterpriseMemberInfoSystemGuid("enterpriseMemberInfoSystemGuid");
        final ResponseModel<Page<ResponseMemberCardListOwned>> pageResponseModel = new ResponseModel<>(
                new Page<>(0L, 0L, Arrays.asList(responseMemberCardListOwned)));
        final RequestCardOwnedPage memberCardsOwnedPageReqDTO = new RequestCardOwnedPage();
        memberCardsOwnedPageReqDTO.setPage(0);
        memberCardsOwnedPageReqDTO.setPageSize(0);
        memberCardsOwnedPageReqDTO.setOpenId("openId");
        memberCardsOwnedPageReqDTO.setEnterpriseGuid("enterpriseGuid");
        memberCardsOwnedPageReqDTO.setBrandGuid("brandGuid");
        memberCardsOwnedPageReqDTO.setStoreGuid("storeGuid");
        when(mockHsaBaseClientService.getMemberCardByPage(memberCardsOwnedPageReqDTO)).thenReturn(pageResponseModel);

        // Configure HsaBaseClientService.getMemberInfo(...).
        final ResponseMemberInfo responseMemberInfo = new ResponseMemberInfo();
        responseMemberInfo.setQrcode("qrcode");
        responseMemberInfo.setMemberInfoGuid("memberInfoGuid");
        responseMemberInfo.setOperSubjectGuid("operSubjectGuid");
        responseMemberInfo.setEnterpriseGuid("enterpriseGuid");
        responseMemberInfo.setPhoneNum("phoneNum");
        final ResponseModel<ResponseMemberInfo> responseMemberInfoResponseModel = new ResponseModel<>(
                responseMemberInfo);
        final RequestQueryMemberInfo requestQueryMemberInfo = new RequestQueryMemberInfo();
        requestQueryMemberInfo.setMemberInfoGuid("memberInfoGuid");
        requestQueryMemberInfo.setUnionId("unionId");
        requestQueryMemberInfo.setOpenId("openId");
        requestQueryMemberInfo.setPhoneNum("phoneNum");
        requestQueryMemberInfo.setOperSubjectGuid("operSubjectGuid");
        when(mockHsaBaseClientService.getMemberInfo(requestQueryMemberInfo))
                .thenReturn(responseMemberInfoResponseModel);

        // Configure HsaBaseClientService.getMemberVolume(...).
        final ResponseMemberInfoVolume responseMemberInfoVolume = new ResponseMemberInfoVolume();
        responseMemberInfoVolume.setMayUseVolumeNum(0);
        responseMemberInfoVolume.setNotUseVolumeNum(0);
        final MemberInfoVolume memberInfoVolume = new MemberInfoVolume();
        memberInfoVolume.setMemberVolumeGuid("memberVolumeGuid");
        memberInfoVolume.setVolumeCode("volumeCode");
        responseMemberInfoVolume.setMemberVolumeList(Arrays.asList(memberInfoVolume));
        final ResponseModel<ResponseMemberInfoVolume> responseMemberInfoVolumeResponseModel = new ResponseModel<>(
                responseMemberInfoVolume);
        final RequestMemberInfoVolumeQuery memberInfoVolumeQueryReqDTO = new RequestMemberInfoVolumeQuery();
        memberInfoVolumeQueryReqDTO.setMemberInfoGuid("memberInfoGuid");
        memberInfoVolumeQueryReqDTO.setVolumeType(0);
        memberInfoVolumeQueryReqDTO.setStoreGuid("storeGuid");
        memberInfoVolumeQueryReqDTO.setBrandGuid("brandGuid");
        memberInfoVolumeQueryReqDTO.setMayUseVolume(0);
        when(mockHsaBaseClientService.getMemberVolume(memberInfoVolumeQueryReqDTO))
                .thenReturn(responseMemberInfoVolumeResponseModel);

        // Run the test
        wxUserRecordServiceImplUnderTest.asyncMemberInfo(wxMemberSessionDTO, "openId");

        // Verify the results
        verify(mockUserMemberSessionUtils).addCardList("storeGuid", "openId", Arrays.asList(
                new UserMemberCardCacheDTO("cardQRCode", "cardGuid", "memberInfoCardGuid", "systemManagementGuid",
                        "enterpriseMemberInfoSystemGuid", "cardName", "cardLogo", "cardIcon", "cardColour", 0,
                        "cardStartDate", "cardEndDate", 0, "systemManagementCardNum", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), "cardIntegral", "cardLevelGuid", "cardLevelName", 0, "levelIcon", 0, 0,
                        false)));
        verify(mockUserMemberSessionUtils).addUserMemberSession(
                new UserMemberSessionDTO("storeGuid", "openId", "userGuid", false, "nickName", "memberInfoGuid",
                        "cardGuid", "memberInfoCardGuid", "systemManagementGuid", 0, false, "volumeCode", false,
                        new BigDecimal("0.00"), new BigDecimal("0.00"), "operSubjectGuid"));
    }
}
