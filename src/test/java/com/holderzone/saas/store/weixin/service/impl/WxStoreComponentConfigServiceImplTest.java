package com.holderzone.saas.store.weixin.service.impl;

import com.holderzone.saas.store.dto.weixin.req.WxCommonReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxComponentConfigDTO;
import com.holderzone.saas.store.weixin.config.WxThirdOpenConfig;
import com.holderzone.saas.store.weixin.service.rpc.WxComponentClientService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class WxStoreComponentConfigServiceImplTest {

    @Mock
    private WxThirdOpenConfig mockWxThirdOpenConfig;
    @Mock
    private WxComponentClientService mockWxComponentClientService;

    private WxStoreComponentConfigServiceImpl wxStoreComponentConfigServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        wxStoreComponentConfigServiceImplUnderTest = new WxStoreComponentConfigServiceImpl();
        wxStoreComponentConfigServiceImplUnderTest.wxThirdOpenConfig = mockWxThirdOpenConfig;
        wxStoreComponentConfigServiceImplUnderTest.wxComponentClientService = mockWxComponentClientService;
    }

    @Test
    public void testReceiveTicket() {
        // Setup
        final WxCommonReqDTO wxCommonReqDTO = new WxCommonReqDTO();
        wxCommonReqDTO.setSignature("signature");
        wxCommonReqDTO.setTimestamp("timestamp");
        wxCommonReqDTO.setNonce("nonce");
        wxCommonReqDTO.setEncrypt_type("encrypt_type");
        wxCommonReqDTO.setMsg_signature("msg_signature");
        wxCommonReqDTO.setXml("xml");

        when(mockWxThirdOpenConfig.getWxOpenComponentService()).thenReturn(null);
        when(mockWxThirdOpenConfig.getWxOpenConfigStorage()).thenReturn(null);

        // Configure WxComponentClientService.findWxConfig(...).
        final WxComponentConfigDTO wxComponentConfigDTO = new WxComponentConfigDTO();
        wxComponentConfigDTO.setComponentAccessToken("componentAccessToken");
        wxComponentConfigDTO.setComponentAppId("appId");
        wxComponentConfigDTO.setComponentExpiresTime("componentExpiresTime");
        wxComponentConfigDTO.setComponentVerifyTicket("componentVerifyTicket");
        wxComponentConfigDTO.setGuid("2837963c-c8ab-45f1-9464-3c0eb03fb6a8");
        when(mockWxComponentClientService.findWxConfig("appId")).thenReturn(wxComponentConfigDTO);

        // Run the test
        final String result = wxStoreComponentConfigServiceImplUnderTest.receiveTicket(wxCommonReqDTO);

        // Verify the results
        assertEquals("success", result);

        // Confirm WxComponentClientService.addWxConfig(...).
        final WxComponentConfigDTO wxComponentConfigDTO1 = new WxComponentConfigDTO();
        wxComponentConfigDTO1.setComponentAccessToken("componentAccessToken");
        wxComponentConfigDTO1.setComponentAppId("appId");
        wxComponentConfigDTO1.setComponentExpiresTime("componentExpiresTime");
        wxComponentConfigDTO1.setComponentVerifyTicket("componentVerifyTicket");
        wxComponentConfigDTO1.setGuid("2837963c-c8ab-45f1-9464-3c0eb03fb6a8");
        verify(mockWxComponentClientService).addWxConfig(wxComponentConfigDTO1);

        // Confirm WxComponentClientService.updateWxConfig(...).
        final WxComponentConfigDTO wxComponentConfigDTO2 = new WxComponentConfigDTO();
        wxComponentConfigDTO2.setComponentAccessToken("componentAccessToken");
        wxComponentConfigDTO2.setComponentAppId("appId");
        wxComponentConfigDTO2.setComponentExpiresTime("componentExpiresTime");
        wxComponentConfigDTO2.setComponentVerifyTicket("componentVerifyTicket");
        wxComponentConfigDTO2.setGuid("2837963c-c8ab-45f1-9464-3c0eb03fb6a8");
        verify(mockWxComponentClientService).updateWxConfig(wxComponentConfigDTO2);
    }

    @Test
    public void testSetVerifyTicket() {
        // Setup
        final WxComponentConfigDTO wxComponentConfigDTO = new WxComponentConfigDTO();
        wxComponentConfigDTO.setComponentAccessToken("componentAccessToken");
        wxComponentConfigDTO.setComponentAppId("appId");
        wxComponentConfigDTO.setComponentExpiresTime("componentExpiresTime");
        wxComponentConfigDTO.setComponentVerifyTicket("componentVerifyTicket");
        wxComponentConfigDTO.setGuid("2837963c-c8ab-45f1-9464-3c0eb03fb6a8");

        when(mockWxThirdOpenConfig.getWxOpenConfigStorage()).thenReturn(null);

        // Configure WxComponentClientService.findWxConfig(...).
        final WxComponentConfigDTO wxComponentConfigDTO1 = new WxComponentConfigDTO();
        wxComponentConfigDTO1.setComponentAccessToken("componentAccessToken");
        wxComponentConfigDTO1.setComponentAppId("appId");
        wxComponentConfigDTO1.setComponentExpiresTime("componentExpiresTime");
        wxComponentConfigDTO1.setComponentVerifyTicket("componentVerifyTicket");
        wxComponentConfigDTO1.setGuid("2837963c-c8ab-45f1-9464-3c0eb03fb6a8");
        when(mockWxComponentClientService.findWxConfig("appId")).thenReturn(wxComponentConfigDTO1);

        // Run the test
        wxStoreComponentConfigServiceImplUnderTest.setVerifyTicket(wxComponentConfigDTO);

        // Verify the results
    }

    @Test
    public void testGetAccessToken() throws Exception {
        // Setup
        when(mockWxThirdOpenConfig.getWxOpenConfigStorage()).thenReturn(null);

        // Configure WxComponentClientService.findWxConfig(...).
        final WxComponentConfigDTO wxComponentConfigDTO = new WxComponentConfigDTO();
        wxComponentConfigDTO.setComponentAccessToken("componentAccessToken");
        wxComponentConfigDTO.setComponentAppId("appId");
        wxComponentConfigDTO.setComponentExpiresTime("componentExpiresTime");
        wxComponentConfigDTO.setComponentVerifyTicket("componentVerifyTicket");
        wxComponentConfigDTO.setGuid("2837963c-c8ab-45f1-9464-3c0eb03fb6a8");
        when(mockWxComponentClientService.findWxConfig("componentAppId")).thenReturn(wxComponentConfigDTO);

        when(mockWxThirdOpenConfig.getWxOpenComponentService()).thenReturn(null);

        // Run the test
        final String result = wxStoreComponentConfigServiceImplUnderTest.getAccessToken();

        // Verify the results
        assertEquals("componentAccessToken", result);

        // Confirm WxComponentClientService.updateWxConfig(...).
        final WxComponentConfigDTO wxComponentConfigDTO1 = new WxComponentConfigDTO();
        wxComponentConfigDTO1.setComponentAccessToken("componentAccessToken");
        wxComponentConfigDTO1.setComponentAppId("appId");
        wxComponentConfigDTO1.setComponentExpiresTime("componentExpiresTime");
        wxComponentConfigDTO1.setComponentVerifyTicket("componentVerifyTicket");
        wxComponentConfigDTO1.setGuid("2837963c-c8ab-45f1-9464-3c0eb03fb6a8");
        verify(mockWxComponentClientService).updateWxConfig(wxComponentConfigDTO1);
    }
}
