package com.holderzone.saas.store.weixin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import com.google.common.util.concurrent.MoreExecutors;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.holder.saas.member.wechat.dto.member.RequestQueryMemberInfo;
import com.holderzone.holder.saas.member.wechat.dto.member.ResponseMemberInfo;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.holder.saas.weixin.utils.WebsocketUtils;
import com.holderzone.saas.store.dto.WxStoreMerchantDineInItemDTO;
import com.holderzone.saas.store.dto.message.BusinessMessageDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.common.ItemAttrDTO;
import com.holderzone.saas.store.dto.order.common.PackageSubgroupDTO;
import com.holderzone.saas.store.dto.order.common.SubDineInItemDTO;
import com.holderzone.saas.store.dto.order.request.bill.BillCalculateReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CancelOrderReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CreateDineInOrderReqDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.order.response.item.EstimateItemRespDTO;
import com.holderzone.saas.store.dto.organization.PadOrderTypeReqDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.store.table.TableDTO;
import com.holderzone.saas.store.dto.table.OpenTableDTO;
import com.holderzone.saas.store.dto.table.TableInfoDTO;
import com.holderzone.saas.store.dto.table.TableOrderCombineDTO;
import com.holderzone.saas.store.dto.table.TurnTableDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceDTO;
import com.holderzone.saas.store.dto.trade.OrderDTO;
import com.holderzone.saas.store.dto.trade.resp.PadOrderRespDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreMerchantOperationDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreMerchantOrderDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreMerchantOrderReqDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreTableCombineDTO;
import com.holderzone.saas.store.dto.weixin.deal.UserMemberSessionDTO;
import com.holderzone.saas.store.dto.weixin.deal.WechatOrderInfoDTO;
import com.holderzone.saas.store.dto.weixin.req.WxOperateReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreMerchantOrderRespDTO;
import com.holderzone.saas.store.weixin.config.ResponseModel;
import com.holderzone.saas.store.weixin.entity.domain.WxOrderItemDO;
import com.holderzone.saas.store.weixin.entity.domain.WxOrderRecordDO;
import com.holderzone.saas.store.weixin.entity.domain.WxStoreMerchantOrderDO;
import com.holderzone.saas.store.weixin.entity.domain.WxUserRecordDO;
import com.holderzone.saas.store.weixin.entity.query.WxStorePendingOrdersQuery;
import com.holderzone.saas.store.weixin.helper.WebsocketMessageHelper;
import com.holderzone.saas.store.weixin.mapper.WxOrderItemMapper;
import com.holderzone.saas.store.weixin.mapper.WxStoreMerchantOrderMapper;
import com.holderzone.saas.store.weixin.mapstruct.WxStoreMerchantOrderMapstruct;
import com.holderzone.saas.store.weixin.service.*;
import com.holderzone.saas.store.weixin.service.OrderItemService;
import com.holderzone.saas.store.weixin.service.rpc.*;
import com.holderzone.saas.store.weixin.service.rpc.member.HsaBaseClientService;
import com.holderzone.saas.store.weixin.service.rpc.member.MemberClientService;
import com.holderzone.saas.store.weixin.utils.UserMemberSessionUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class WxStoreMerchantOrderImplTest {

    @Mock
    private WxStoreMerchantOrderMapstruct mockWxStoreMerchantOrderMapstruct;
    @Mock
    private RedisUtils mockRedisUtils;
    @Mock
    private OrganizationClientService mockOrganizationClientService;
    @Mock
    private BizMsgFeignClient mockBizMsgFeignClient;
    @Mock
    private TradeClientService mockTradeClientService;
    @Mock
    private WxStoreTableClientService mockTableClientService;
    @Mock
    private WxStoreTableClientService mockWxStoreTableClientService;
    @Mock
    private EnterpriseClientService mockEnterpriseClientService;
    @Mock
    private WxStoreDineInOrderClientService mockWxStoreDineInOrderClientService;
    @Mock
    private WxStoreMenuDetailsService mockWxStoreMenuDetailsService;
    @Mock
    private WxStoreMerchantDineInItemService mockWxStoreMerchantDineInItemService;
    @Mock
    private WxStoreOrderConfigService mockWxStoreOrderConfigService;
    @Mock
    private WxStoreSessionDetailsService mockWxStoreSessionDetailsService;
    @Mock
    private WxStoreMerchantOrderMapper mockWxStoreMerchantOrderMapper;
    @Mock
    private OrderItemService mockOrderItemService;
    @Mock
    private WxOrderItemMapper mockWxOrderItemMapper;
    @Mock
    private WxOrderRecordService mockWxOrderRecordService;
    @Mock
    private WxUserRecordService mockWxUserRecordService;
    @Mock
    private WebsocketUtils mockWebsocketUtils;
    @Mock
    private WebsocketMessageHelper mockWebsocketMessageHelper;
    @Mock
    private UserMemberSessionUtils mockUserMemberSessionUtils;
    @Mock
    private WxStoreDineInBillClientService mockWxStoreDineInBillClientService;
    @Mock
    private MemberClientService mockMemberClientService;
    @Mock
    private HsaBaseClientService mockHsaBaseClientService;

    private WxStoreMerchantOrderImpl wxStoreMerchantOrderImplUnderTest;

    @Before
    public void setUp() throws Exception {
        wxStoreMerchantOrderImplUnderTest = new WxStoreMerchantOrderImpl(mockWxStoreMerchantOrderMapstruct,
                mockRedisUtils, mockOrganizationClientService, mockBizMsgFeignClient, mockTradeClientService,
                mockTableClientService);
        ReflectionTestUtils.setField(wxStoreMerchantOrderImplUnderTest, "wxStoreDineInOrderClientService",
                mockWxStoreDineInOrderClientService);
        ReflectionTestUtils.setField(wxStoreMerchantOrderImplUnderTest, "wxStoreMenuDetailsService",
                mockWxStoreMenuDetailsService);
        ReflectionTestUtils.setField(wxStoreMerchantOrderImplUnderTest, "wxStoreMerchantDineInItemService",
                mockWxStoreMerchantDineInItemService);
        ReflectionTestUtils.setField(wxStoreMerchantOrderImplUnderTest, "wxStoreOrderConfigService",
                mockWxStoreOrderConfigService);
        ReflectionTestUtils.setField(wxStoreMerchantOrderImplUnderTest, "wxStoreSessionDetailsService",
                mockWxStoreSessionDetailsService);
        ReflectionTestUtils.setField(wxStoreMerchantOrderImplUnderTest, "wxStoreMerchantOrderMapper",
                mockWxStoreMerchantOrderMapper);
        ReflectionTestUtils.setField(wxStoreMerchantOrderImplUnderTest, "orderItemService", mockOrderItemService);
        ReflectionTestUtils.setField(wxStoreMerchantOrderImplUnderTest, "wxOrderItemMapper", mockWxOrderItemMapper);
        ReflectionTestUtils.setField(wxStoreMerchantOrderImplUnderTest, "wxOrderRecordService",
                mockWxOrderRecordService);
        ReflectionTestUtils.setField(wxStoreMerchantOrderImplUnderTest, "wxUserRecordService", mockWxUserRecordService);
        ReflectionTestUtils.setField(wxStoreMerchantOrderImplUnderTest, "websocketUtils", mockWebsocketUtils);
        ReflectionTestUtils.setField(wxStoreMerchantOrderImplUnderTest, "websocketMessageHelper",
                mockWebsocketMessageHelper);
        ReflectionTestUtils.setField(wxStoreMerchantOrderImplUnderTest, "userMemberSessionUtils",
                mockUserMemberSessionUtils);
        ReflectionTestUtils.setField(wxStoreMerchantOrderImplUnderTest, "wxStoreDineInBillClientService",
                mockWxStoreDineInBillClientService);
        ReflectionTestUtils.setField(wxStoreMerchantOrderImplUnderTest, "executorService",
                MoreExecutors.newDirectExecutorService());
        wxStoreMerchantOrderImplUnderTest.wxStoreTableClientService = mockWxStoreTableClientService;
        wxStoreMerchantOrderImplUnderTest.enterpriseClientService = mockEnterpriseClientService;
        wxStoreMerchantOrderImplUnderTest.memberClientService = mockMemberClientService;
        wxStoreMerchantOrderImplUnderTest.hsaBaseClientService = mockHsaBaseClientService;
    }

    @Test
    public void testGetWxStoreMerchantOrderResp() {
        // Setup
        final WxStoreMerchantOrderReqDTO wxStoreMerchantOrderReqDTO = new WxStoreMerchantOrderReqDTO();
        wxStoreMerchantOrderReqDTO.setGuid("guid");
        wxStoreMerchantOrderReqDTO.setOrderState(0);
        wxStoreMerchantOrderReqDTO.setTradeMode(0);
        wxStoreMerchantOrderReqDTO.setLastGuid("lastGuid");
        wxStoreMerchantOrderReqDTO.setCount(0);

        final WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        wxStoreMerchantOrderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setOpenId("openId");
        wxStoreMerchantOrderDTO.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDTO.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDTO.setOrderState(0);
        wxStoreMerchantOrderDTO.setTradeMode(0);
        wxStoreMerchantOrderDTO.setTradeModeName("desc");
        wxStoreMerchantOrderDTO.setMsgSource(0);
        wxStoreMerchantOrderDTO.setMsgSourceName("desc");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setUserWxPublicOpenId("openId");
        dineInItemDTO.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO.setItemGuid("itemGuid");
        dineInItemDTO.setItemType(0);
        dineInItemDTO.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO.setItemTypeName("itemTypeName");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO = new SubDineInItemDTO();
        subDineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO));
        dineInItemDTO.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO));
        final ItemAttrDTO itemAttrDTO = new ItemAttrDTO();
        itemAttrDTO.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO.setNum(0);
        dineInItemDTO.setItemAttrDTOS(Arrays.asList(itemAttrDTO));
        wxStoreMerchantOrderDTO.setDineInItemDTOList(Arrays.asList(dineInItemDTO));
        wxStoreMerchantOrderDTO.setOrderNo("guid");
        wxStoreMerchantOrderDTO.setOperationSource(0);
        wxStoreMerchantOrderDTO.setOperationSourceName("desc");
        wxStoreMerchantOrderDTO.setOrderSource(0);
        final WxStoreMerchantOrderRespDTO expectedResult = new WxStoreMerchantOrderRespDTO(
                Arrays.asList(wxStoreMerchantOrderDTO));

        // Configure TradeClientService.listPadOrder(...).
        final PadOrderRespDTO padOrderRespDTO = new PadOrderRespDTO();
        padOrderRespDTO.setGuid(0L);
        padOrderRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        padOrderRespDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        padOrderRespDTO.setOrderGuid("e4c510fe-d776-4a77-bbe5-2067155947b9");
        padOrderRespDTO.setActualGuestsNo(0);
        padOrderRespDTO.setAreaName("areaName");
        padOrderRespDTO.setDiningTableGuid("diningTableGuid");
        padOrderRespDTO.setTableCode("tableCode");
        padOrderRespDTO.setOrderState(0);
        padOrderRespDTO.setDenialReason("denialReason");
        padOrderRespDTO.setStoreGuid("storeGuid");
        padOrderRespDTO.setRemark("remark");
        padOrderRespDTO.setOperationGuid("openId");
        padOrderRespDTO.setOperationName("nickName");
        padOrderRespDTO.setCombineOrderGuid("combineOrderGuid");
        padOrderRespDTO.setOrderSource(0);
        final List<PadOrderRespDTO> padOrderRespDTOS = Arrays.asList(padOrderRespDTO);
        final WxStoreMerchantOrderReqDTO request = new WxStoreMerchantOrderReqDTO();
        request.setGuid("guid");
        request.setOrderState(0);
        request.setTradeMode(0);
        request.setLastGuid("lastGuid");
        request.setCount(0);
        when(mockTradeClientService.listPadOrder(request)).thenReturn(padOrderRespDTOS);

        // Configure WxStoreMerchantOrderMapstruct.padOrderDTOList2wxOrderDTOList(...).
        final WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO1 = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO1.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        wxStoreMerchantOrderDTO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO1.setOpenId("openId");
        wxStoreMerchantOrderDTO1.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDTO1.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDTO1.setOrderState(0);
        wxStoreMerchantOrderDTO1.setTradeMode(0);
        wxStoreMerchantOrderDTO1.setTradeModeName("desc");
        wxStoreMerchantOrderDTO1.setMsgSource(0);
        wxStoreMerchantOrderDTO1.setMsgSourceName("desc");
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setUserWxPublicOpenId("openId");
        dineInItemDTO1.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO1.setItemGuid("itemGuid");
        dineInItemDTO1.setItemType(0);
        dineInItemDTO1.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO1.setItemTypeName("itemTypeName");
        dineInItemDTO1.setPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO1.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO1 = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO1 = new SubDineInItemDTO();
        subDineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO1.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO1.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO1));
        dineInItemDTO1.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO1));
        final ItemAttrDTO itemAttrDTO1 = new ItemAttrDTO();
        itemAttrDTO1.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO1.setNum(0);
        dineInItemDTO1.setItemAttrDTOS(Arrays.asList(itemAttrDTO1));
        wxStoreMerchantOrderDTO1.setDineInItemDTOList(Arrays.asList(dineInItemDTO1));
        wxStoreMerchantOrderDTO1.setOrderNo("guid");
        wxStoreMerchantOrderDTO1.setOperationSource(0);
        wxStoreMerchantOrderDTO1.setOperationSourceName("desc");
        wxStoreMerchantOrderDTO1.setOrderSource(0);
        final List<WxStoreMerchantOrderDTO> wxStoreMerchantOrderDTOS = Arrays.asList(wxStoreMerchantOrderDTO1);
        final PadOrderRespDTO padOrderRespDTO1 = new PadOrderRespDTO();
        padOrderRespDTO1.setGuid(0L);
        padOrderRespDTO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        padOrderRespDTO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        padOrderRespDTO1.setOrderGuid("e4c510fe-d776-4a77-bbe5-2067155947b9");
        padOrderRespDTO1.setActualGuestsNo(0);
        padOrderRespDTO1.setAreaName("areaName");
        padOrderRespDTO1.setDiningTableGuid("diningTableGuid");
        padOrderRespDTO1.setTableCode("tableCode");
        padOrderRespDTO1.setOrderState(0);
        padOrderRespDTO1.setDenialReason("denialReason");
        padOrderRespDTO1.setStoreGuid("storeGuid");
        padOrderRespDTO1.setRemark("remark");
        padOrderRespDTO1.setOperationGuid("openId");
        padOrderRespDTO1.setOperationName("nickName");
        padOrderRespDTO1.setCombineOrderGuid("combineOrderGuid");
        padOrderRespDTO1.setOrderSource(0);
        final List<PadOrderRespDTO> padOrderRespDTOList = Arrays.asList(padOrderRespDTO1);
        when(mockWxStoreMerchantOrderMapstruct.padOrderDTOList2wxOrderDTOList(padOrderRespDTOList))
                .thenReturn(wxStoreMerchantOrderDTOS);

        // Configure WxStoreMerchantOrderMapper.queryWeChatOrderList(...).
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        wxStoreMerchantOrderDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDO.setTotalPrice(new BigDecimal("0.00"));
        wxStoreMerchantOrderDO.setOpenId("openId");
        wxStoreMerchantOrderDO.setNickName("nickName");
        wxStoreMerchantOrderDO.setActualGuestsNo(0);
        wxStoreMerchantOrderDO.setTradeMode(0);
        wxStoreMerchantOrderDO.setOrderState(0);
        wxStoreMerchantOrderDO.setDenialReason("denialReason");
        wxStoreMerchantOrderDO.setOrderGuid("guid");
        wxStoreMerchantOrderDO.setRemark("remark");
        wxStoreMerchantOrderDO.setAreaGuid("areaGuid");
        wxStoreMerchantOrderDO.setAreaName("areaName");
        wxStoreMerchantOrderDO.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDO.setStoreName("name");
        wxStoreMerchantOrderDO.setBrandName("brandName");
        wxStoreMerchantOrderDO.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDO.setTableCode("tableCode");
        wxStoreMerchantOrderDO.setOperationGuid("openId");
        wxStoreMerchantOrderDO.setOperationSource(0);
        wxStoreMerchantOrderDO.setOperationName("nickName");
        wxStoreMerchantOrderDO.setOrderRecordGuid("58d0dfce-a97a-4608-b8a7-f7f4f68b843b");
        wxStoreMerchantOrderDO.setCombine("mainOrderGuid");
        wxStoreMerchantOrderDO.setOrderSource(0);
        final List<WxStoreMerchantOrderDO> wxStoreMerchantOrderDOS = Arrays.asList(wxStoreMerchantOrderDO);
        final WxStoreMerchantOrderReqDTO wxStoreMerchantOrderReqDTO1 = new WxStoreMerchantOrderReqDTO();
        wxStoreMerchantOrderReqDTO1.setGuid("guid");
        wxStoreMerchantOrderReqDTO1.setOrderState(0);
        wxStoreMerchantOrderReqDTO1.setTradeMode(0);
        wxStoreMerchantOrderReqDTO1.setLastGuid("lastGuid");
        wxStoreMerchantOrderReqDTO1.setCount(0);
        when(mockWxStoreMerchantOrderMapper.queryWeChatOrderList(wxStoreMerchantOrderReqDTO1, "storeGuid",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0))).thenReturn(wxStoreMerchantOrderDOS);

        // Configure WxStoreMerchantOrderMapstruct.getWxStoreMerchantOrder(...).
        final WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO2 = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO2.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        wxStoreMerchantOrderDTO2.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO2.setOpenId("openId");
        wxStoreMerchantOrderDTO2.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDTO2.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDTO2.setOrderState(0);
        wxStoreMerchantOrderDTO2.setTradeMode(0);
        wxStoreMerchantOrderDTO2.setTradeModeName("desc");
        wxStoreMerchantOrderDTO2.setMsgSource(0);
        wxStoreMerchantOrderDTO2.setMsgSourceName("desc");
        final DineInItemDTO dineInItemDTO2 = new DineInItemDTO();
        dineInItemDTO2.setUserWxPublicOpenId("openId");
        dineInItemDTO2.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO2.setItemGuid("itemGuid");
        dineInItemDTO2.setItemType(0);
        dineInItemDTO2.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO2.setItemTypeName("itemTypeName");
        dineInItemDTO2.setPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO2.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO2 = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO2 = new SubDineInItemDTO();
        subDineInItemDTO2.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO2.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO2.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO2));
        dineInItemDTO2.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO2));
        final ItemAttrDTO itemAttrDTO2 = new ItemAttrDTO();
        itemAttrDTO2.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO2.setNum(0);
        dineInItemDTO2.setItemAttrDTOS(Arrays.asList(itemAttrDTO2));
        wxStoreMerchantOrderDTO2.setDineInItemDTOList(Arrays.asList(dineInItemDTO2));
        wxStoreMerchantOrderDTO2.setOrderNo("guid");
        wxStoreMerchantOrderDTO2.setOperationSource(0);
        wxStoreMerchantOrderDTO2.setOperationSourceName("desc");
        wxStoreMerchantOrderDTO2.setOrderSource(0);
        final List<WxStoreMerchantOrderDTO> wxStoreMerchantOrderDTOS1 = Arrays.asList(wxStoreMerchantOrderDTO2);
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO1 = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO1.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        wxStoreMerchantOrderDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDO1.setTotalPrice(new BigDecimal("0.00"));
        wxStoreMerchantOrderDO1.setOpenId("openId");
        wxStoreMerchantOrderDO1.setNickName("nickName");
        wxStoreMerchantOrderDO1.setActualGuestsNo(0);
        wxStoreMerchantOrderDO1.setTradeMode(0);
        wxStoreMerchantOrderDO1.setOrderState(0);
        wxStoreMerchantOrderDO1.setDenialReason("denialReason");
        wxStoreMerchantOrderDO1.setOrderGuid("guid");
        wxStoreMerchantOrderDO1.setRemark("remark");
        wxStoreMerchantOrderDO1.setAreaGuid("areaGuid");
        wxStoreMerchantOrderDO1.setAreaName("areaName");
        wxStoreMerchantOrderDO1.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDO1.setStoreName("name");
        wxStoreMerchantOrderDO1.setBrandName("brandName");
        wxStoreMerchantOrderDO1.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDO1.setTableCode("tableCode");
        wxStoreMerchantOrderDO1.setOperationGuid("openId");
        wxStoreMerchantOrderDO1.setOperationSource(0);
        wxStoreMerchantOrderDO1.setOperationName("nickName");
        wxStoreMerchantOrderDO1.setOrderRecordGuid("58d0dfce-a97a-4608-b8a7-f7f4f68b843b");
        wxStoreMerchantOrderDO1.setCombine("mainOrderGuid");
        wxStoreMerchantOrderDO1.setOrderSource(0);
        final List<WxStoreMerchantOrderDO> wxStoreMerchantOrderDOS1 = Arrays.asList(wxStoreMerchantOrderDO1);
        when(mockWxStoreMerchantOrderMapstruct.getWxStoreMerchantOrder(wxStoreMerchantOrderDOS1))
                .thenReturn(wxStoreMerchantOrderDTOS1);

        // Run the test
        final WxStoreMerchantOrderRespDTO result = wxStoreMerchantOrderImplUnderTest.getWxStoreMerchantOrderResp(
                wxStoreMerchantOrderReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetWxStoreMerchantOrderResp_TradeClientServiceReturnsNoItems() {
        // Setup
        final WxStoreMerchantOrderReqDTO wxStoreMerchantOrderReqDTO = new WxStoreMerchantOrderReqDTO();
        wxStoreMerchantOrderReqDTO.setGuid("guid");
        wxStoreMerchantOrderReqDTO.setOrderState(0);
        wxStoreMerchantOrderReqDTO.setTradeMode(0);
        wxStoreMerchantOrderReqDTO.setLastGuid("lastGuid");
        wxStoreMerchantOrderReqDTO.setCount(0);

        final WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        wxStoreMerchantOrderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setOpenId("openId");
        wxStoreMerchantOrderDTO.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDTO.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDTO.setOrderState(0);
        wxStoreMerchantOrderDTO.setTradeMode(0);
        wxStoreMerchantOrderDTO.setTradeModeName("desc");
        wxStoreMerchantOrderDTO.setMsgSource(0);
        wxStoreMerchantOrderDTO.setMsgSourceName("desc");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setUserWxPublicOpenId("openId");
        dineInItemDTO.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO.setItemGuid("itemGuid");
        dineInItemDTO.setItemType(0);
        dineInItemDTO.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO.setItemTypeName("itemTypeName");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO = new SubDineInItemDTO();
        subDineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO));
        dineInItemDTO.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO));
        final ItemAttrDTO itemAttrDTO = new ItemAttrDTO();
        itemAttrDTO.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO.setNum(0);
        dineInItemDTO.setItemAttrDTOS(Arrays.asList(itemAttrDTO));
        wxStoreMerchantOrderDTO.setDineInItemDTOList(Arrays.asList(dineInItemDTO));
        wxStoreMerchantOrderDTO.setOrderNo("guid");
        wxStoreMerchantOrderDTO.setOperationSource(0);
        wxStoreMerchantOrderDTO.setOperationSourceName("desc");
        wxStoreMerchantOrderDTO.setOrderSource(0);
        final WxStoreMerchantOrderRespDTO expectedResult = new WxStoreMerchantOrderRespDTO(
                Arrays.asList(wxStoreMerchantOrderDTO));

        // Configure TradeClientService.listPadOrder(...).
        final WxStoreMerchantOrderReqDTO request = new WxStoreMerchantOrderReqDTO();
        request.setGuid("guid");
        request.setOrderState(0);
        request.setTradeMode(0);
        request.setLastGuid("lastGuid");
        request.setCount(0);
        when(mockTradeClientService.listPadOrder(request)).thenReturn(Collections.emptyList());

        // Configure WxStoreMerchantOrderMapstruct.padOrderDTOList2wxOrderDTOList(...).
        final WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO1 = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO1.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        wxStoreMerchantOrderDTO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO1.setOpenId("openId");
        wxStoreMerchantOrderDTO1.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDTO1.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDTO1.setOrderState(0);
        wxStoreMerchantOrderDTO1.setTradeMode(0);
        wxStoreMerchantOrderDTO1.setTradeModeName("desc");
        wxStoreMerchantOrderDTO1.setMsgSource(0);
        wxStoreMerchantOrderDTO1.setMsgSourceName("desc");
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setUserWxPublicOpenId("openId");
        dineInItemDTO1.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO1.setItemGuid("itemGuid");
        dineInItemDTO1.setItemType(0);
        dineInItemDTO1.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO1.setItemTypeName("itemTypeName");
        dineInItemDTO1.setPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO1.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO1 = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO1 = new SubDineInItemDTO();
        subDineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO1.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO1.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO1));
        dineInItemDTO1.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO1));
        final ItemAttrDTO itemAttrDTO1 = new ItemAttrDTO();
        itemAttrDTO1.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO1.setNum(0);
        dineInItemDTO1.setItemAttrDTOS(Arrays.asList(itemAttrDTO1));
        wxStoreMerchantOrderDTO1.setDineInItemDTOList(Arrays.asList(dineInItemDTO1));
        wxStoreMerchantOrderDTO1.setOrderNo("guid");
        wxStoreMerchantOrderDTO1.setOperationSource(0);
        wxStoreMerchantOrderDTO1.setOperationSourceName("desc");
        wxStoreMerchantOrderDTO1.setOrderSource(0);
        final List<WxStoreMerchantOrderDTO> wxStoreMerchantOrderDTOS = Arrays.asList(wxStoreMerchantOrderDTO1);
        final PadOrderRespDTO padOrderRespDTO = new PadOrderRespDTO();
        padOrderRespDTO.setGuid(0L);
        padOrderRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        padOrderRespDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        padOrderRespDTO.setOrderGuid("e4c510fe-d776-4a77-bbe5-2067155947b9");
        padOrderRespDTO.setActualGuestsNo(0);
        padOrderRespDTO.setAreaName("areaName");
        padOrderRespDTO.setDiningTableGuid("diningTableGuid");
        padOrderRespDTO.setTableCode("tableCode");
        padOrderRespDTO.setOrderState(0);
        padOrderRespDTO.setDenialReason("denialReason");
        padOrderRespDTO.setStoreGuid("storeGuid");
        padOrderRespDTO.setRemark("remark");
        padOrderRespDTO.setOperationGuid("openId");
        padOrderRespDTO.setOperationName("nickName");
        padOrderRespDTO.setCombineOrderGuid("combineOrderGuid");
        padOrderRespDTO.setOrderSource(0);
        final List<PadOrderRespDTO> padOrderRespDTOList = Arrays.asList(padOrderRespDTO);
        when(mockWxStoreMerchantOrderMapstruct.padOrderDTOList2wxOrderDTOList(padOrderRespDTOList))
                .thenReturn(wxStoreMerchantOrderDTOS);

        // Configure WxStoreMerchantOrderMapper.queryWeChatOrderList(...).
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        wxStoreMerchantOrderDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDO.setTotalPrice(new BigDecimal("0.00"));
        wxStoreMerchantOrderDO.setOpenId("openId");
        wxStoreMerchantOrderDO.setNickName("nickName");
        wxStoreMerchantOrderDO.setActualGuestsNo(0);
        wxStoreMerchantOrderDO.setTradeMode(0);
        wxStoreMerchantOrderDO.setOrderState(0);
        wxStoreMerchantOrderDO.setDenialReason("denialReason");
        wxStoreMerchantOrderDO.setOrderGuid("guid");
        wxStoreMerchantOrderDO.setRemark("remark");
        wxStoreMerchantOrderDO.setAreaGuid("areaGuid");
        wxStoreMerchantOrderDO.setAreaName("areaName");
        wxStoreMerchantOrderDO.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDO.setStoreName("name");
        wxStoreMerchantOrderDO.setBrandName("brandName");
        wxStoreMerchantOrderDO.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDO.setTableCode("tableCode");
        wxStoreMerchantOrderDO.setOperationGuid("openId");
        wxStoreMerchantOrderDO.setOperationSource(0);
        wxStoreMerchantOrderDO.setOperationName("nickName");
        wxStoreMerchantOrderDO.setOrderRecordGuid("58d0dfce-a97a-4608-b8a7-f7f4f68b843b");
        wxStoreMerchantOrderDO.setCombine("mainOrderGuid");
        wxStoreMerchantOrderDO.setOrderSource(0);
        final List<WxStoreMerchantOrderDO> wxStoreMerchantOrderDOS = Arrays.asList(wxStoreMerchantOrderDO);
        final WxStoreMerchantOrderReqDTO wxStoreMerchantOrderReqDTO1 = new WxStoreMerchantOrderReqDTO();
        wxStoreMerchantOrderReqDTO1.setGuid("guid");
        wxStoreMerchantOrderReqDTO1.setOrderState(0);
        wxStoreMerchantOrderReqDTO1.setTradeMode(0);
        wxStoreMerchantOrderReqDTO1.setLastGuid("lastGuid");
        wxStoreMerchantOrderReqDTO1.setCount(0);
        when(mockWxStoreMerchantOrderMapper.queryWeChatOrderList(wxStoreMerchantOrderReqDTO1, "storeGuid",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0))).thenReturn(wxStoreMerchantOrderDOS);

        // Configure WxStoreMerchantOrderMapstruct.getWxStoreMerchantOrder(...).
        final WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO2 = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO2.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        wxStoreMerchantOrderDTO2.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO2.setOpenId("openId");
        wxStoreMerchantOrderDTO2.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDTO2.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDTO2.setOrderState(0);
        wxStoreMerchantOrderDTO2.setTradeMode(0);
        wxStoreMerchantOrderDTO2.setTradeModeName("desc");
        wxStoreMerchantOrderDTO2.setMsgSource(0);
        wxStoreMerchantOrderDTO2.setMsgSourceName("desc");
        final DineInItemDTO dineInItemDTO2 = new DineInItemDTO();
        dineInItemDTO2.setUserWxPublicOpenId("openId");
        dineInItemDTO2.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO2.setItemGuid("itemGuid");
        dineInItemDTO2.setItemType(0);
        dineInItemDTO2.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO2.setItemTypeName("itemTypeName");
        dineInItemDTO2.setPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO2.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO2 = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO2 = new SubDineInItemDTO();
        subDineInItemDTO2.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO2.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO2.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO2));
        dineInItemDTO2.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO2));
        final ItemAttrDTO itemAttrDTO2 = new ItemAttrDTO();
        itemAttrDTO2.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO2.setNum(0);
        dineInItemDTO2.setItemAttrDTOS(Arrays.asList(itemAttrDTO2));
        wxStoreMerchantOrderDTO2.setDineInItemDTOList(Arrays.asList(dineInItemDTO2));
        wxStoreMerchantOrderDTO2.setOrderNo("guid");
        wxStoreMerchantOrderDTO2.setOperationSource(0);
        wxStoreMerchantOrderDTO2.setOperationSourceName("desc");
        wxStoreMerchantOrderDTO2.setOrderSource(0);
        final List<WxStoreMerchantOrderDTO> wxStoreMerchantOrderDTOS1 = Arrays.asList(wxStoreMerchantOrderDTO2);
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO1 = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO1.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        wxStoreMerchantOrderDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDO1.setTotalPrice(new BigDecimal("0.00"));
        wxStoreMerchantOrderDO1.setOpenId("openId");
        wxStoreMerchantOrderDO1.setNickName("nickName");
        wxStoreMerchantOrderDO1.setActualGuestsNo(0);
        wxStoreMerchantOrderDO1.setTradeMode(0);
        wxStoreMerchantOrderDO1.setOrderState(0);
        wxStoreMerchantOrderDO1.setDenialReason("denialReason");
        wxStoreMerchantOrderDO1.setOrderGuid("guid");
        wxStoreMerchantOrderDO1.setRemark("remark");
        wxStoreMerchantOrderDO1.setAreaGuid("areaGuid");
        wxStoreMerchantOrderDO1.setAreaName("areaName");
        wxStoreMerchantOrderDO1.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDO1.setStoreName("name");
        wxStoreMerchantOrderDO1.setBrandName("brandName");
        wxStoreMerchantOrderDO1.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDO1.setTableCode("tableCode");
        wxStoreMerchantOrderDO1.setOperationGuid("openId");
        wxStoreMerchantOrderDO1.setOperationSource(0);
        wxStoreMerchantOrderDO1.setOperationName("nickName");
        wxStoreMerchantOrderDO1.setOrderRecordGuid("58d0dfce-a97a-4608-b8a7-f7f4f68b843b");
        wxStoreMerchantOrderDO1.setCombine("mainOrderGuid");
        wxStoreMerchantOrderDO1.setOrderSource(0);
        final List<WxStoreMerchantOrderDO> wxStoreMerchantOrderDOS1 = Arrays.asList(wxStoreMerchantOrderDO1);
        when(mockWxStoreMerchantOrderMapstruct.getWxStoreMerchantOrder(wxStoreMerchantOrderDOS1))
                .thenReturn(wxStoreMerchantOrderDTOS1);

        // Run the test
        final WxStoreMerchantOrderRespDTO result = wxStoreMerchantOrderImplUnderTest.getWxStoreMerchantOrderResp(
                wxStoreMerchantOrderReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetWxStoreMerchantOrderResp_WxStoreMerchantOrderMapstructPadOrderDTOList2wxOrderDTOListReturnsNoItems() {
        // Setup
        final WxStoreMerchantOrderReqDTO wxStoreMerchantOrderReqDTO = new WxStoreMerchantOrderReqDTO();
        wxStoreMerchantOrderReqDTO.setGuid("guid");
        wxStoreMerchantOrderReqDTO.setOrderState(0);
        wxStoreMerchantOrderReqDTO.setTradeMode(0);
        wxStoreMerchantOrderReqDTO.setLastGuid("lastGuid");
        wxStoreMerchantOrderReqDTO.setCount(0);

        final WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        wxStoreMerchantOrderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setOpenId("openId");
        wxStoreMerchantOrderDTO.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDTO.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDTO.setOrderState(0);
        wxStoreMerchantOrderDTO.setTradeMode(0);
        wxStoreMerchantOrderDTO.setTradeModeName("desc");
        wxStoreMerchantOrderDTO.setMsgSource(0);
        wxStoreMerchantOrderDTO.setMsgSourceName("desc");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setUserWxPublicOpenId("openId");
        dineInItemDTO.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO.setItemGuid("itemGuid");
        dineInItemDTO.setItemType(0);
        dineInItemDTO.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO.setItemTypeName("itemTypeName");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO = new SubDineInItemDTO();
        subDineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO));
        dineInItemDTO.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO));
        final ItemAttrDTO itemAttrDTO = new ItemAttrDTO();
        itemAttrDTO.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO.setNum(0);
        dineInItemDTO.setItemAttrDTOS(Arrays.asList(itemAttrDTO));
        wxStoreMerchantOrderDTO.setDineInItemDTOList(Arrays.asList(dineInItemDTO));
        wxStoreMerchantOrderDTO.setOrderNo("guid");
        wxStoreMerchantOrderDTO.setOperationSource(0);
        wxStoreMerchantOrderDTO.setOperationSourceName("desc");
        wxStoreMerchantOrderDTO.setOrderSource(0);
        final WxStoreMerchantOrderRespDTO expectedResult = new WxStoreMerchantOrderRespDTO(
                Arrays.asList(wxStoreMerchantOrderDTO));

        // Configure TradeClientService.listPadOrder(...).
        final PadOrderRespDTO padOrderRespDTO = new PadOrderRespDTO();
        padOrderRespDTO.setGuid(0L);
        padOrderRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        padOrderRespDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        padOrderRespDTO.setOrderGuid("e4c510fe-d776-4a77-bbe5-2067155947b9");
        padOrderRespDTO.setActualGuestsNo(0);
        padOrderRespDTO.setAreaName("areaName");
        padOrderRespDTO.setDiningTableGuid("diningTableGuid");
        padOrderRespDTO.setTableCode("tableCode");
        padOrderRespDTO.setOrderState(0);
        padOrderRespDTO.setDenialReason("denialReason");
        padOrderRespDTO.setStoreGuid("storeGuid");
        padOrderRespDTO.setRemark("remark");
        padOrderRespDTO.setOperationGuid("openId");
        padOrderRespDTO.setOperationName("nickName");
        padOrderRespDTO.setCombineOrderGuid("combineOrderGuid");
        padOrderRespDTO.setOrderSource(0);
        final List<PadOrderRespDTO> padOrderRespDTOS = Arrays.asList(padOrderRespDTO);
        final WxStoreMerchantOrderReqDTO request = new WxStoreMerchantOrderReqDTO();
        request.setGuid("guid");
        request.setOrderState(0);
        request.setTradeMode(0);
        request.setLastGuid("lastGuid");
        request.setCount(0);
        when(mockTradeClientService.listPadOrder(request)).thenReturn(padOrderRespDTOS);

        // Configure WxStoreMerchantOrderMapstruct.padOrderDTOList2wxOrderDTOList(...).
        final PadOrderRespDTO padOrderRespDTO1 = new PadOrderRespDTO();
        padOrderRespDTO1.setGuid(0L);
        padOrderRespDTO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        padOrderRespDTO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        padOrderRespDTO1.setOrderGuid("e4c510fe-d776-4a77-bbe5-2067155947b9");
        padOrderRespDTO1.setActualGuestsNo(0);
        padOrderRespDTO1.setAreaName("areaName");
        padOrderRespDTO1.setDiningTableGuid("diningTableGuid");
        padOrderRespDTO1.setTableCode("tableCode");
        padOrderRespDTO1.setOrderState(0);
        padOrderRespDTO1.setDenialReason("denialReason");
        padOrderRespDTO1.setStoreGuid("storeGuid");
        padOrderRespDTO1.setRemark("remark");
        padOrderRespDTO1.setOperationGuid("openId");
        padOrderRespDTO1.setOperationName("nickName");
        padOrderRespDTO1.setCombineOrderGuid("combineOrderGuid");
        padOrderRespDTO1.setOrderSource(0);
        final List<PadOrderRespDTO> padOrderRespDTOList = Arrays.asList(padOrderRespDTO1);
        when(mockWxStoreMerchantOrderMapstruct.padOrderDTOList2wxOrderDTOList(padOrderRespDTOList))
                .thenReturn(Collections.emptyList());

        // Configure WxStoreMerchantOrderMapper.queryWeChatOrderList(...).
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        wxStoreMerchantOrderDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDO.setTotalPrice(new BigDecimal("0.00"));
        wxStoreMerchantOrderDO.setOpenId("openId");
        wxStoreMerchantOrderDO.setNickName("nickName");
        wxStoreMerchantOrderDO.setActualGuestsNo(0);
        wxStoreMerchantOrderDO.setTradeMode(0);
        wxStoreMerchantOrderDO.setOrderState(0);
        wxStoreMerchantOrderDO.setDenialReason("denialReason");
        wxStoreMerchantOrderDO.setOrderGuid("guid");
        wxStoreMerchantOrderDO.setRemark("remark");
        wxStoreMerchantOrderDO.setAreaGuid("areaGuid");
        wxStoreMerchantOrderDO.setAreaName("areaName");
        wxStoreMerchantOrderDO.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDO.setStoreName("name");
        wxStoreMerchantOrderDO.setBrandName("brandName");
        wxStoreMerchantOrderDO.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDO.setTableCode("tableCode");
        wxStoreMerchantOrderDO.setOperationGuid("openId");
        wxStoreMerchantOrderDO.setOperationSource(0);
        wxStoreMerchantOrderDO.setOperationName("nickName");
        wxStoreMerchantOrderDO.setOrderRecordGuid("58d0dfce-a97a-4608-b8a7-f7f4f68b843b");
        wxStoreMerchantOrderDO.setCombine("mainOrderGuid");
        wxStoreMerchantOrderDO.setOrderSource(0);
        final List<WxStoreMerchantOrderDO> wxStoreMerchantOrderDOS = Arrays.asList(wxStoreMerchantOrderDO);
        final WxStoreMerchantOrderReqDTO wxStoreMerchantOrderReqDTO1 = new WxStoreMerchantOrderReqDTO();
        wxStoreMerchantOrderReqDTO1.setGuid("guid");
        wxStoreMerchantOrderReqDTO1.setOrderState(0);
        wxStoreMerchantOrderReqDTO1.setTradeMode(0);
        wxStoreMerchantOrderReqDTO1.setLastGuid("lastGuid");
        wxStoreMerchantOrderReqDTO1.setCount(0);
        when(mockWxStoreMerchantOrderMapper.queryWeChatOrderList(wxStoreMerchantOrderReqDTO1, "storeGuid",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0))).thenReturn(wxStoreMerchantOrderDOS);

        // Configure WxStoreMerchantOrderMapstruct.getWxStoreMerchantOrder(...).
        final WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO1 = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO1.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        wxStoreMerchantOrderDTO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO1.setOpenId("openId");
        wxStoreMerchantOrderDTO1.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDTO1.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDTO1.setOrderState(0);
        wxStoreMerchantOrderDTO1.setTradeMode(0);
        wxStoreMerchantOrderDTO1.setTradeModeName("desc");
        wxStoreMerchantOrderDTO1.setMsgSource(0);
        wxStoreMerchantOrderDTO1.setMsgSourceName("desc");
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setUserWxPublicOpenId("openId");
        dineInItemDTO1.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO1.setItemGuid("itemGuid");
        dineInItemDTO1.setItemType(0);
        dineInItemDTO1.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO1.setItemTypeName("itemTypeName");
        dineInItemDTO1.setPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO1.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO1 = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO1 = new SubDineInItemDTO();
        subDineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO1.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO1.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO1));
        dineInItemDTO1.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO1));
        final ItemAttrDTO itemAttrDTO1 = new ItemAttrDTO();
        itemAttrDTO1.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO1.setNum(0);
        dineInItemDTO1.setItemAttrDTOS(Arrays.asList(itemAttrDTO1));
        wxStoreMerchantOrderDTO1.setDineInItemDTOList(Arrays.asList(dineInItemDTO1));
        wxStoreMerchantOrderDTO1.setOrderNo("guid");
        wxStoreMerchantOrderDTO1.setOperationSource(0);
        wxStoreMerchantOrderDTO1.setOperationSourceName("desc");
        wxStoreMerchantOrderDTO1.setOrderSource(0);
        final List<WxStoreMerchantOrderDTO> wxStoreMerchantOrderDTOS = Arrays.asList(wxStoreMerchantOrderDTO1);
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO1 = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO1.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        wxStoreMerchantOrderDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDO1.setTotalPrice(new BigDecimal("0.00"));
        wxStoreMerchantOrderDO1.setOpenId("openId");
        wxStoreMerchantOrderDO1.setNickName("nickName");
        wxStoreMerchantOrderDO1.setActualGuestsNo(0);
        wxStoreMerchantOrderDO1.setTradeMode(0);
        wxStoreMerchantOrderDO1.setOrderState(0);
        wxStoreMerchantOrderDO1.setDenialReason("denialReason");
        wxStoreMerchantOrderDO1.setOrderGuid("guid");
        wxStoreMerchantOrderDO1.setRemark("remark");
        wxStoreMerchantOrderDO1.setAreaGuid("areaGuid");
        wxStoreMerchantOrderDO1.setAreaName("areaName");
        wxStoreMerchantOrderDO1.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDO1.setStoreName("name");
        wxStoreMerchantOrderDO1.setBrandName("brandName");
        wxStoreMerchantOrderDO1.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDO1.setTableCode("tableCode");
        wxStoreMerchantOrderDO1.setOperationGuid("openId");
        wxStoreMerchantOrderDO1.setOperationSource(0);
        wxStoreMerchantOrderDO1.setOperationName("nickName");
        wxStoreMerchantOrderDO1.setOrderRecordGuid("58d0dfce-a97a-4608-b8a7-f7f4f68b843b");
        wxStoreMerchantOrderDO1.setCombine("mainOrderGuid");
        wxStoreMerchantOrderDO1.setOrderSource(0);
        final List<WxStoreMerchantOrderDO> wxStoreMerchantOrderDOS1 = Arrays.asList(wxStoreMerchantOrderDO1);
        when(mockWxStoreMerchantOrderMapstruct.getWxStoreMerchantOrder(wxStoreMerchantOrderDOS1))
                .thenReturn(wxStoreMerchantOrderDTOS);

        // Run the test
        final WxStoreMerchantOrderRespDTO result = wxStoreMerchantOrderImplUnderTest.getWxStoreMerchantOrderResp(
                wxStoreMerchantOrderReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetWxStoreMerchantOrderResp_WxStoreMerchantOrderMapperReturnsNoItems() {
        // Setup
        final WxStoreMerchantOrderReqDTO wxStoreMerchantOrderReqDTO = new WxStoreMerchantOrderReqDTO();
        wxStoreMerchantOrderReqDTO.setGuid("guid");
        wxStoreMerchantOrderReqDTO.setOrderState(0);
        wxStoreMerchantOrderReqDTO.setTradeMode(0);
        wxStoreMerchantOrderReqDTO.setLastGuid("lastGuid");
        wxStoreMerchantOrderReqDTO.setCount(0);

        final WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        wxStoreMerchantOrderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setOpenId("openId");
        wxStoreMerchantOrderDTO.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDTO.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDTO.setOrderState(0);
        wxStoreMerchantOrderDTO.setTradeMode(0);
        wxStoreMerchantOrderDTO.setTradeModeName("desc");
        wxStoreMerchantOrderDTO.setMsgSource(0);
        wxStoreMerchantOrderDTO.setMsgSourceName("desc");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setUserWxPublicOpenId("openId");
        dineInItemDTO.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO.setItemGuid("itemGuid");
        dineInItemDTO.setItemType(0);
        dineInItemDTO.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO.setItemTypeName("itemTypeName");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO = new SubDineInItemDTO();
        subDineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO));
        dineInItemDTO.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO));
        final ItemAttrDTO itemAttrDTO = new ItemAttrDTO();
        itemAttrDTO.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO.setNum(0);
        dineInItemDTO.setItemAttrDTOS(Arrays.asList(itemAttrDTO));
        wxStoreMerchantOrderDTO.setDineInItemDTOList(Arrays.asList(dineInItemDTO));
        wxStoreMerchantOrderDTO.setOrderNo("guid");
        wxStoreMerchantOrderDTO.setOperationSource(0);
        wxStoreMerchantOrderDTO.setOperationSourceName("desc");
        wxStoreMerchantOrderDTO.setOrderSource(0);
        final WxStoreMerchantOrderRespDTO expectedResult = new WxStoreMerchantOrderRespDTO(
                Arrays.asList(wxStoreMerchantOrderDTO));

        // Configure TradeClientService.listPadOrder(...).
        final PadOrderRespDTO padOrderRespDTO = new PadOrderRespDTO();
        padOrderRespDTO.setGuid(0L);
        padOrderRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        padOrderRespDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        padOrderRespDTO.setOrderGuid("e4c510fe-d776-4a77-bbe5-2067155947b9");
        padOrderRespDTO.setActualGuestsNo(0);
        padOrderRespDTO.setAreaName("areaName");
        padOrderRespDTO.setDiningTableGuid("diningTableGuid");
        padOrderRespDTO.setTableCode("tableCode");
        padOrderRespDTO.setOrderState(0);
        padOrderRespDTO.setDenialReason("denialReason");
        padOrderRespDTO.setStoreGuid("storeGuid");
        padOrderRespDTO.setRemark("remark");
        padOrderRespDTO.setOperationGuid("openId");
        padOrderRespDTO.setOperationName("nickName");
        padOrderRespDTO.setCombineOrderGuid("combineOrderGuid");
        padOrderRespDTO.setOrderSource(0);
        final List<PadOrderRespDTO> padOrderRespDTOS = Arrays.asList(padOrderRespDTO);
        final WxStoreMerchantOrderReqDTO request = new WxStoreMerchantOrderReqDTO();
        request.setGuid("guid");
        request.setOrderState(0);
        request.setTradeMode(0);
        request.setLastGuid("lastGuid");
        request.setCount(0);
        when(mockTradeClientService.listPadOrder(request)).thenReturn(padOrderRespDTOS);

        // Configure WxStoreMerchantOrderMapstruct.padOrderDTOList2wxOrderDTOList(...).
        final WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO1 = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO1.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        wxStoreMerchantOrderDTO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO1.setOpenId("openId");
        wxStoreMerchantOrderDTO1.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDTO1.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDTO1.setOrderState(0);
        wxStoreMerchantOrderDTO1.setTradeMode(0);
        wxStoreMerchantOrderDTO1.setTradeModeName("desc");
        wxStoreMerchantOrderDTO1.setMsgSource(0);
        wxStoreMerchantOrderDTO1.setMsgSourceName("desc");
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setUserWxPublicOpenId("openId");
        dineInItemDTO1.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO1.setItemGuid("itemGuid");
        dineInItemDTO1.setItemType(0);
        dineInItemDTO1.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO1.setItemTypeName("itemTypeName");
        dineInItemDTO1.setPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO1.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO1 = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO1 = new SubDineInItemDTO();
        subDineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO1.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO1.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO1));
        dineInItemDTO1.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO1));
        final ItemAttrDTO itemAttrDTO1 = new ItemAttrDTO();
        itemAttrDTO1.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO1.setNum(0);
        dineInItemDTO1.setItemAttrDTOS(Arrays.asList(itemAttrDTO1));
        wxStoreMerchantOrderDTO1.setDineInItemDTOList(Arrays.asList(dineInItemDTO1));
        wxStoreMerchantOrderDTO1.setOrderNo("guid");
        wxStoreMerchantOrderDTO1.setOperationSource(0);
        wxStoreMerchantOrderDTO1.setOperationSourceName("desc");
        wxStoreMerchantOrderDTO1.setOrderSource(0);
        final List<WxStoreMerchantOrderDTO> wxStoreMerchantOrderDTOS = Arrays.asList(wxStoreMerchantOrderDTO1);
        final PadOrderRespDTO padOrderRespDTO1 = new PadOrderRespDTO();
        padOrderRespDTO1.setGuid(0L);
        padOrderRespDTO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        padOrderRespDTO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        padOrderRespDTO1.setOrderGuid("e4c510fe-d776-4a77-bbe5-2067155947b9");
        padOrderRespDTO1.setActualGuestsNo(0);
        padOrderRespDTO1.setAreaName("areaName");
        padOrderRespDTO1.setDiningTableGuid("diningTableGuid");
        padOrderRespDTO1.setTableCode("tableCode");
        padOrderRespDTO1.setOrderState(0);
        padOrderRespDTO1.setDenialReason("denialReason");
        padOrderRespDTO1.setStoreGuid("storeGuid");
        padOrderRespDTO1.setRemark("remark");
        padOrderRespDTO1.setOperationGuid("openId");
        padOrderRespDTO1.setOperationName("nickName");
        padOrderRespDTO1.setCombineOrderGuid("combineOrderGuid");
        padOrderRespDTO1.setOrderSource(0);
        final List<PadOrderRespDTO> padOrderRespDTOList = Arrays.asList(padOrderRespDTO1);
        when(mockWxStoreMerchantOrderMapstruct.padOrderDTOList2wxOrderDTOList(padOrderRespDTOList))
                .thenReturn(wxStoreMerchantOrderDTOS);

        // Configure WxStoreMerchantOrderMapper.queryWeChatOrderList(...).
        final WxStoreMerchantOrderReqDTO wxStoreMerchantOrderReqDTO1 = new WxStoreMerchantOrderReqDTO();
        wxStoreMerchantOrderReqDTO1.setGuid("guid");
        wxStoreMerchantOrderReqDTO1.setOrderState(0);
        wxStoreMerchantOrderReqDTO1.setTradeMode(0);
        wxStoreMerchantOrderReqDTO1.setLastGuid("lastGuid");
        wxStoreMerchantOrderReqDTO1.setCount(0);
        when(mockWxStoreMerchantOrderMapper.queryWeChatOrderList(wxStoreMerchantOrderReqDTO1, "storeGuid",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0))).thenReturn(Collections.emptyList());

        // Configure WxStoreMerchantOrderMapstruct.getWxStoreMerchantOrder(...).
        final WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO2 = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO2.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        wxStoreMerchantOrderDTO2.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO2.setOpenId("openId");
        wxStoreMerchantOrderDTO2.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDTO2.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDTO2.setOrderState(0);
        wxStoreMerchantOrderDTO2.setTradeMode(0);
        wxStoreMerchantOrderDTO2.setTradeModeName("desc");
        wxStoreMerchantOrderDTO2.setMsgSource(0);
        wxStoreMerchantOrderDTO2.setMsgSourceName("desc");
        final DineInItemDTO dineInItemDTO2 = new DineInItemDTO();
        dineInItemDTO2.setUserWxPublicOpenId("openId");
        dineInItemDTO2.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO2.setItemGuid("itemGuid");
        dineInItemDTO2.setItemType(0);
        dineInItemDTO2.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO2.setItemTypeName("itemTypeName");
        dineInItemDTO2.setPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO2.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO2 = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO2 = new SubDineInItemDTO();
        subDineInItemDTO2.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO2.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO2.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO2));
        dineInItemDTO2.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO2));
        final ItemAttrDTO itemAttrDTO2 = new ItemAttrDTO();
        itemAttrDTO2.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO2.setNum(0);
        dineInItemDTO2.setItemAttrDTOS(Arrays.asList(itemAttrDTO2));
        wxStoreMerchantOrderDTO2.setDineInItemDTOList(Arrays.asList(dineInItemDTO2));
        wxStoreMerchantOrderDTO2.setOrderNo("guid");
        wxStoreMerchantOrderDTO2.setOperationSource(0);
        wxStoreMerchantOrderDTO2.setOperationSourceName("desc");
        wxStoreMerchantOrderDTO2.setOrderSource(0);
        final List<WxStoreMerchantOrderDTO> wxStoreMerchantOrderDTOS1 = Arrays.asList(wxStoreMerchantOrderDTO2);
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        wxStoreMerchantOrderDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDO.setTotalPrice(new BigDecimal("0.00"));
        wxStoreMerchantOrderDO.setOpenId("openId");
        wxStoreMerchantOrderDO.setNickName("nickName");
        wxStoreMerchantOrderDO.setActualGuestsNo(0);
        wxStoreMerchantOrderDO.setTradeMode(0);
        wxStoreMerchantOrderDO.setOrderState(0);
        wxStoreMerchantOrderDO.setDenialReason("denialReason");
        wxStoreMerchantOrderDO.setOrderGuid("guid");
        wxStoreMerchantOrderDO.setRemark("remark");
        wxStoreMerchantOrderDO.setAreaGuid("areaGuid");
        wxStoreMerchantOrderDO.setAreaName("areaName");
        wxStoreMerchantOrderDO.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDO.setStoreName("name");
        wxStoreMerchantOrderDO.setBrandName("brandName");
        wxStoreMerchantOrderDO.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDO.setTableCode("tableCode");
        wxStoreMerchantOrderDO.setOperationGuid("openId");
        wxStoreMerchantOrderDO.setOperationSource(0);
        wxStoreMerchantOrderDO.setOperationName("nickName");
        wxStoreMerchantOrderDO.setOrderRecordGuid("58d0dfce-a97a-4608-b8a7-f7f4f68b843b");
        wxStoreMerchantOrderDO.setCombine("mainOrderGuid");
        wxStoreMerchantOrderDO.setOrderSource(0);
        final List<WxStoreMerchantOrderDO> wxStoreMerchantOrderDOS = Arrays.asList(wxStoreMerchantOrderDO);
        when(mockWxStoreMerchantOrderMapstruct.getWxStoreMerchantOrder(wxStoreMerchantOrderDOS))
                .thenReturn(wxStoreMerchantOrderDTOS1);

        // Run the test
        final WxStoreMerchantOrderRespDTO result = wxStoreMerchantOrderImplUnderTest.getWxStoreMerchantOrderResp(
                wxStoreMerchantOrderReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetWxStoreMerchantOrderResp_WxStoreMerchantOrderMapstructGetWxStoreMerchantOrderReturnsNoItems() {
        // Setup
        final WxStoreMerchantOrderReqDTO wxStoreMerchantOrderReqDTO = new WxStoreMerchantOrderReqDTO();
        wxStoreMerchantOrderReqDTO.setGuid("guid");
        wxStoreMerchantOrderReqDTO.setOrderState(0);
        wxStoreMerchantOrderReqDTO.setTradeMode(0);
        wxStoreMerchantOrderReqDTO.setLastGuid("lastGuid");
        wxStoreMerchantOrderReqDTO.setCount(0);

        final WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        wxStoreMerchantOrderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setOpenId("openId");
        wxStoreMerchantOrderDTO.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDTO.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDTO.setOrderState(0);
        wxStoreMerchantOrderDTO.setTradeMode(0);
        wxStoreMerchantOrderDTO.setTradeModeName("desc");
        wxStoreMerchantOrderDTO.setMsgSource(0);
        wxStoreMerchantOrderDTO.setMsgSourceName("desc");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setUserWxPublicOpenId("openId");
        dineInItemDTO.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO.setItemGuid("itemGuid");
        dineInItemDTO.setItemType(0);
        dineInItemDTO.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO.setItemTypeName("itemTypeName");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO = new SubDineInItemDTO();
        subDineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO));
        dineInItemDTO.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO));
        final ItemAttrDTO itemAttrDTO = new ItemAttrDTO();
        itemAttrDTO.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO.setNum(0);
        dineInItemDTO.setItemAttrDTOS(Arrays.asList(itemAttrDTO));
        wxStoreMerchantOrderDTO.setDineInItemDTOList(Arrays.asList(dineInItemDTO));
        wxStoreMerchantOrderDTO.setOrderNo("guid");
        wxStoreMerchantOrderDTO.setOperationSource(0);
        wxStoreMerchantOrderDTO.setOperationSourceName("desc");
        wxStoreMerchantOrderDTO.setOrderSource(0);
        final WxStoreMerchantOrderRespDTO expectedResult = new WxStoreMerchantOrderRespDTO(
                Arrays.asList(wxStoreMerchantOrderDTO));

        // Configure TradeClientService.listPadOrder(...).
        final PadOrderRespDTO padOrderRespDTO = new PadOrderRespDTO();
        padOrderRespDTO.setGuid(0L);
        padOrderRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        padOrderRespDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        padOrderRespDTO.setOrderGuid("e4c510fe-d776-4a77-bbe5-2067155947b9");
        padOrderRespDTO.setActualGuestsNo(0);
        padOrderRespDTO.setAreaName("areaName");
        padOrderRespDTO.setDiningTableGuid("diningTableGuid");
        padOrderRespDTO.setTableCode("tableCode");
        padOrderRespDTO.setOrderState(0);
        padOrderRespDTO.setDenialReason("denialReason");
        padOrderRespDTO.setStoreGuid("storeGuid");
        padOrderRespDTO.setRemark("remark");
        padOrderRespDTO.setOperationGuid("openId");
        padOrderRespDTO.setOperationName("nickName");
        padOrderRespDTO.setCombineOrderGuid("combineOrderGuid");
        padOrderRespDTO.setOrderSource(0);
        final List<PadOrderRespDTO> padOrderRespDTOS = Arrays.asList(padOrderRespDTO);
        final WxStoreMerchantOrderReqDTO request = new WxStoreMerchantOrderReqDTO();
        request.setGuid("guid");
        request.setOrderState(0);
        request.setTradeMode(0);
        request.setLastGuid("lastGuid");
        request.setCount(0);
        when(mockTradeClientService.listPadOrder(request)).thenReturn(padOrderRespDTOS);

        // Configure WxStoreMerchantOrderMapstruct.padOrderDTOList2wxOrderDTOList(...).
        final WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO1 = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO1.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        wxStoreMerchantOrderDTO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO1.setOpenId("openId");
        wxStoreMerchantOrderDTO1.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDTO1.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDTO1.setOrderState(0);
        wxStoreMerchantOrderDTO1.setTradeMode(0);
        wxStoreMerchantOrderDTO1.setTradeModeName("desc");
        wxStoreMerchantOrderDTO1.setMsgSource(0);
        wxStoreMerchantOrderDTO1.setMsgSourceName("desc");
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setUserWxPublicOpenId("openId");
        dineInItemDTO1.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO1.setItemGuid("itemGuid");
        dineInItemDTO1.setItemType(0);
        dineInItemDTO1.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO1.setItemTypeName("itemTypeName");
        dineInItemDTO1.setPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO1.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO1 = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO1 = new SubDineInItemDTO();
        subDineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO1.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO1.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO1));
        dineInItemDTO1.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO1));
        final ItemAttrDTO itemAttrDTO1 = new ItemAttrDTO();
        itemAttrDTO1.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO1.setNum(0);
        dineInItemDTO1.setItemAttrDTOS(Arrays.asList(itemAttrDTO1));
        wxStoreMerchantOrderDTO1.setDineInItemDTOList(Arrays.asList(dineInItemDTO1));
        wxStoreMerchantOrderDTO1.setOrderNo("guid");
        wxStoreMerchantOrderDTO1.setOperationSource(0);
        wxStoreMerchantOrderDTO1.setOperationSourceName("desc");
        wxStoreMerchantOrderDTO1.setOrderSource(0);
        final List<WxStoreMerchantOrderDTO> wxStoreMerchantOrderDTOS = Arrays.asList(wxStoreMerchantOrderDTO1);
        final PadOrderRespDTO padOrderRespDTO1 = new PadOrderRespDTO();
        padOrderRespDTO1.setGuid(0L);
        padOrderRespDTO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        padOrderRespDTO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        padOrderRespDTO1.setOrderGuid("e4c510fe-d776-4a77-bbe5-2067155947b9");
        padOrderRespDTO1.setActualGuestsNo(0);
        padOrderRespDTO1.setAreaName("areaName");
        padOrderRespDTO1.setDiningTableGuid("diningTableGuid");
        padOrderRespDTO1.setTableCode("tableCode");
        padOrderRespDTO1.setOrderState(0);
        padOrderRespDTO1.setDenialReason("denialReason");
        padOrderRespDTO1.setStoreGuid("storeGuid");
        padOrderRespDTO1.setRemark("remark");
        padOrderRespDTO1.setOperationGuid("openId");
        padOrderRespDTO1.setOperationName("nickName");
        padOrderRespDTO1.setCombineOrderGuid("combineOrderGuid");
        padOrderRespDTO1.setOrderSource(0);
        final List<PadOrderRespDTO> padOrderRespDTOList = Arrays.asList(padOrderRespDTO1);
        when(mockWxStoreMerchantOrderMapstruct.padOrderDTOList2wxOrderDTOList(padOrderRespDTOList))
                .thenReturn(wxStoreMerchantOrderDTOS);

        // Configure WxStoreMerchantOrderMapper.queryWeChatOrderList(...).
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        wxStoreMerchantOrderDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDO.setTotalPrice(new BigDecimal("0.00"));
        wxStoreMerchantOrderDO.setOpenId("openId");
        wxStoreMerchantOrderDO.setNickName("nickName");
        wxStoreMerchantOrderDO.setActualGuestsNo(0);
        wxStoreMerchantOrderDO.setTradeMode(0);
        wxStoreMerchantOrderDO.setOrderState(0);
        wxStoreMerchantOrderDO.setDenialReason("denialReason");
        wxStoreMerchantOrderDO.setOrderGuid("guid");
        wxStoreMerchantOrderDO.setRemark("remark");
        wxStoreMerchantOrderDO.setAreaGuid("areaGuid");
        wxStoreMerchantOrderDO.setAreaName("areaName");
        wxStoreMerchantOrderDO.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDO.setStoreName("name");
        wxStoreMerchantOrderDO.setBrandName("brandName");
        wxStoreMerchantOrderDO.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDO.setTableCode("tableCode");
        wxStoreMerchantOrderDO.setOperationGuid("openId");
        wxStoreMerchantOrderDO.setOperationSource(0);
        wxStoreMerchantOrderDO.setOperationName("nickName");
        wxStoreMerchantOrderDO.setOrderRecordGuid("58d0dfce-a97a-4608-b8a7-f7f4f68b843b");
        wxStoreMerchantOrderDO.setCombine("mainOrderGuid");
        wxStoreMerchantOrderDO.setOrderSource(0);
        final List<WxStoreMerchantOrderDO> wxStoreMerchantOrderDOS = Arrays.asList(wxStoreMerchantOrderDO);
        final WxStoreMerchantOrderReqDTO wxStoreMerchantOrderReqDTO1 = new WxStoreMerchantOrderReqDTO();
        wxStoreMerchantOrderReqDTO1.setGuid("guid");
        wxStoreMerchantOrderReqDTO1.setOrderState(0);
        wxStoreMerchantOrderReqDTO1.setTradeMode(0);
        wxStoreMerchantOrderReqDTO1.setLastGuid("lastGuid");
        wxStoreMerchantOrderReqDTO1.setCount(0);
        when(mockWxStoreMerchantOrderMapper.queryWeChatOrderList(wxStoreMerchantOrderReqDTO1, "storeGuid",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0))).thenReturn(wxStoreMerchantOrderDOS);

        // Configure WxStoreMerchantOrderMapstruct.getWxStoreMerchantOrder(...).
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO1 = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO1.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        wxStoreMerchantOrderDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDO1.setTotalPrice(new BigDecimal("0.00"));
        wxStoreMerchantOrderDO1.setOpenId("openId");
        wxStoreMerchantOrderDO1.setNickName("nickName");
        wxStoreMerchantOrderDO1.setActualGuestsNo(0);
        wxStoreMerchantOrderDO1.setTradeMode(0);
        wxStoreMerchantOrderDO1.setOrderState(0);
        wxStoreMerchantOrderDO1.setDenialReason("denialReason");
        wxStoreMerchantOrderDO1.setOrderGuid("guid");
        wxStoreMerchantOrderDO1.setRemark("remark");
        wxStoreMerchantOrderDO1.setAreaGuid("areaGuid");
        wxStoreMerchantOrderDO1.setAreaName("areaName");
        wxStoreMerchantOrderDO1.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDO1.setStoreName("name");
        wxStoreMerchantOrderDO1.setBrandName("brandName");
        wxStoreMerchantOrderDO1.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDO1.setTableCode("tableCode");
        wxStoreMerchantOrderDO1.setOperationGuid("openId");
        wxStoreMerchantOrderDO1.setOperationSource(0);
        wxStoreMerchantOrderDO1.setOperationName("nickName");
        wxStoreMerchantOrderDO1.setOrderRecordGuid("58d0dfce-a97a-4608-b8a7-f7f4f68b843b");
        wxStoreMerchantOrderDO1.setCombine("mainOrderGuid");
        wxStoreMerchantOrderDO1.setOrderSource(0);
        final List<WxStoreMerchantOrderDO> wxStoreMerchantOrderDOS1 = Arrays.asList(wxStoreMerchantOrderDO1);
        when(mockWxStoreMerchantOrderMapstruct.getWxStoreMerchantOrder(wxStoreMerchantOrderDOS1))
                .thenReturn(Collections.emptyList());

        // Run the test
        final WxStoreMerchantOrderRespDTO result = wxStoreMerchantOrderImplUnderTest.getWxStoreMerchantOrderResp(
                wxStoreMerchantOrderReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetDetailPend() {
        // Setup
        final WxStoreMerchantOrderReqDTO wxStoreMerchantOrderReqDTO = new WxStoreMerchantOrderReqDTO();
        wxStoreMerchantOrderReqDTO.setGuid("guid");
        wxStoreMerchantOrderReqDTO.setOrderState(0);
        wxStoreMerchantOrderReqDTO.setTradeMode(0);
        wxStoreMerchantOrderReqDTO.setLastGuid("lastGuid");
        wxStoreMerchantOrderReqDTO.setCount(0);

        final WxStoreMerchantOrderDTO expectedResult = new WxStoreMerchantOrderDTO();
        expectedResult.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        expectedResult.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setOpenId("openId");
        expectedResult.setDiningTableGuid("diningTableGuid");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setOrderState(0);
        expectedResult.setTradeMode(0);
        expectedResult.setTradeModeName("desc");
        expectedResult.setMsgSource(0);
        expectedResult.setMsgSourceName("desc");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setUserWxPublicOpenId("openId");
        dineInItemDTO.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO.setItemGuid("itemGuid");
        dineInItemDTO.setItemType(0);
        dineInItemDTO.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO.setItemTypeName("itemTypeName");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO = new SubDineInItemDTO();
        subDineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO));
        dineInItemDTO.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO));
        final ItemAttrDTO itemAttrDTO = new ItemAttrDTO();
        itemAttrDTO.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO.setNum(0);
        dineInItemDTO.setItemAttrDTOS(Arrays.asList(itemAttrDTO));
        expectedResult.setDineInItemDTOList(Arrays.asList(dineInItemDTO));
        expectedResult.setOrderNo("guid");
        expectedResult.setOperationSource(0);
        expectedResult.setOperationSourceName("desc");
        expectedResult.setOrderSource(0);

        // Configure WxStoreMerchantOrderMapstruct.getWxStoreMerchantOrder(...).
        final WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        wxStoreMerchantOrderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setOpenId("openId");
        wxStoreMerchantOrderDTO.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDTO.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDTO.setOrderState(0);
        wxStoreMerchantOrderDTO.setTradeMode(0);
        wxStoreMerchantOrderDTO.setTradeModeName("desc");
        wxStoreMerchantOrderDTO.setMsgSource(0);
        wxStoreMerchantOrderDTO.setMsgSourceName("desc");
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setUserWxPublicOpenId("openId");
        dineInItemDTO1.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO1.setItemGuid("itemGuid");
        dineInItemDTO1.setItemType(0);
        dineInItemDTO1.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO1.setItemTypeName("itemTypeName");
        dineInItemDTO1.setPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO1.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO1 = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO1 = new SubDineInItemDTO();
        subDineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO1.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO1.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO1));
        dineInItemDTO1.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO1));
        final ItemAttrDTO itemAttrDTO1 = new ItemAttrDTO();
        itemAttrDTO1.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO1.setNum(0);
        dineInItemDTO1.setItemAttrDTOS(Arrays.asList(itemAttrDTO1));
        wxStoreMerchantOrderDTO.setDineInItemDTOList(Arrays.asList(dineInItemDTO1));
        wxStoreMerchantOrderDTO.setOrderNo("guid");
        wxStoreMerchantOrderDTO.setOperationSource(0);
        wxStoreMerchantOrderDTO.setOperationSourceName("desc");
        wxStoreMerchantOrderDTO.setOrderSource(0);
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        wxStoreMerchantOrderDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDO.setTotalPrice(new BigDecimal("0.00"));
        wxStoreMerchantOrderDO.setOpenId("openId");
        wxStoreMerchantOrderDO.setNickName("nickName");
        wxStoreMerchantOrderDO.setActualGuestsNo(0);
        wxStoreMerchantOrderDO.setTradeMode(0);
        wxStoreMerchantOrderDO.setOrderState(0);
        wxStoreMerchantOrderDO.setDenialReason("denialReason");
        wxStoreMerchantOrderDO.setOrderGuid("guid");
        wxStoreMerchantOrderDO.setRemark("remark");
        wxStoreMerchantOrderDO.setAreaGuid("areaGuid");
        wxStoreMerchantOrderDO.setAreaName("areaName");
        wxStoreMerchantOrderDO.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDO.setStoreName("name");
        wxStoreMerchantOrderDO.setBrandName("brandName");
        wxStoreMerchantOrderDO.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDO.setTableCode("tableCode");
        wxStoreMerchantOrderDO.setOperationGuid("openId");
        wxStoreMerchantOrderDO.setOperationSource(0);
        wxStoreMerchantOrderDO.setOperationName("nickName");
        wxStoreMerchantOrderDO.setOrderRecordGuid("58d0dfce-a97a-4608-b8a7-f7f4f68b843b");
        wxStoreMerchantOrderDO.setCombine("mainOrderGuid");
        wxStoreMerchantOrderDO.setOrderSource(0);
        when(mockWxStoreMerchantOrderMapstruct.getWxStoreMerchantOrder(wxStoreMerchantOrderDO))
                .thenReturn(wxStoreMerchantOrderDTO);

        // Configure OrderItemService.getItemListByMerchantGuid(...).
        final DineInItemDTO dineInItemDTO2 = new DineInItemDTO();
        dineInItemDTO2.setUserWxPublicOpenId("openId");
        dineInItemDTO2.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO2.setItemGuid("itemGuid");
        dineInItemDTO2.setItemType(0);
        dineInItemDTO2.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO2.setItemTypeName("itemTypeName");
        dineInItemDTO2.setPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO2.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO2 = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO2 = new SubDineInItemDTO();
        subDineInItemDTO2.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO2.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO2.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO2));
        dineInItemDTO2.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO2));
        final ItemAttrDTO itemAttrDTO2 = new ItemAttrDTO();
        itemAttrDTO2.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO2.setNum(0);
        dineInItemDTO2.setItemAttrDTOS(Arrays.asList(itemAttrDTO2));
        final List<DineInItemDTO> dineInItemDTOS = Arrays.asList(dineInItemDTO2);
        when(mockOrderItemService.getItemListByMerchantGuid("guid")).thenReturn(dineInItemDTOS);

        // Configure TradeClientService.getPadOrderByGuid(...).
        final PadOrderRespDTO padOrderRespDTO = new PadOrderRespDTO();
        padOrderRespDTO.setGuid(0L);
        padOrderRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        padOrderRespDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        padOrderRespDTO.setOrderGuid("e4c510fe-d776-4a77-bbe5-2067155947b9");
        padOrderRespDTO.setActualGuestsNo(0);
        padOrderRespDTO.setAreaName("areaName");
        padOrderRespDTO.setDiningTableGuid("diningTableGuid");
        padOrderRespDTO.setTableCode("tableCode");
        padOrderRespDTO.setOrderState(0);
        padOrderRespDTO.setDenialReason("denialReason");
        padOrderRespDTO.setStoreGuid("storeGuid");
        padOrderRespDTO.setRemark("remark");
        padOrderRespDTO.setOperationGuid("openId");
        padOrderRespDTO.setOperationName("nickName");
        padOrderRespDTO.setCombineOrderGuid("combineOrderGuid");
        padOrderRespDTO.setOrderSource(0);
        when(mockTradeClientService.getPadOrderByGuid("guid")).thenReturn(padOrderRespDTO);

        // Configure WxStoreMerchantOrderMapstruct.padOrderDTO2wxOrderDTO(...).
        final WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO1 = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO1.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        wxStoreMerchantOrderDTO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO1.setOpenId("openId");
        wxStoreMerchantOrderDTO1.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDTO1.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDTO1.setOrderState(0);
        wxStoreMerchantOrderDTO1.setTradeMode(0);
        wxStoreMerchantOrderDTO1.setTradeModeName("desc");
        wxStoreMerchantOrderDTO1.setMsgSource(0);
        wxStoreMerchantOrderDTO1.setMsgSourceName("desc");
        final DineInItemDTO dineInItemDTO3 = new DineInItemDTO();
        dineInItemDTO3.setUserWxPublicOpenId("openId");
        dineInItemDTO3.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO3.setItemGuid("itemGuid");
        dineInItemDTO3.setItemType(0);
        dineInItemDTO3.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO3.setItemTypeName("itemTypeName");
        dineInItemDTO3.setPrice(new BigDecimal("0.00"));
        dineInItemDTO3.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO3.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO3.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO3 = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO3 = new SubDineInItemDTO();
        subDineInItemDTO3.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO3.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO3.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO3));
        dineInItemDTO3.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO3));
        final ItemAttrDTO itemAttrDTO3 = new ItemAttrDTO();
        itemAttrDTO3.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO3.setNum(0);
        dineInItemDTO3.setItemAttrDTOS(Arrays.asList(itemAttrDTO3));
        wxStoreMerchantOrderDTO1.setDineInItemDTOList(Arrays.asList(dineInItemDTO3));
        wxStoreMerchantOrderDTO1.setOrderNo("guid");
        wxStoreMerchantOrderDTO1.setOperationSource(0);
        wxStoreMerchantOrderDTO1.setOperationSourceName("desc");
        wxStoreMerchantOrderDTO1.setOrderSource(0);
        final PadOrderRespDTO padOrderRespDTO1 = new PadOrderRespDTO();
        padOrderRespDTO1.setGuid(0L);
        padOrderRespDTO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        padOrderRespDTO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        padOrderRespDTO1.setOrderGuid("e4c510fe-d776-4a77-bbe5-2067155947b9");
        padOrderRespDTO1.setActualGuestsNo(0);
        padOrderRespDTO1.setAreaName("areaName");
        padOrderRespDTO1.setDiningTableGuid("diningTableGuid");
        padOrderRespDTO1.setTableCode("tableCode");
        padOrderRespDTO1.setOrderState(0);
        padOrderRespDTO1.setDenialReason("denialReason");
        padOrderRespDTO1.setStoreGuid("storeGuid");
        padOrderRespDTO1.setRemark("remark");
        padOrderRespDTO1.setOperationGuid("openId");
        padOrderRespDTO1.setOperationName("nickName");
        padOrderRespDTO1.setCombineOrderGuid("combineOrderGuid");
        padOrderRespDTO1.setOrderSource(0);
        when(mockWxStoreMerchantOrderMapstruct.padOrderDTO2wxOrderDTO(padOrderRespDTO1))
                .thenReturn(wxStoreMerchantOrderDTO1);

        // Configure TradeClientService.findByOrderGuid(...).
        final OrderDTO orderDTO = OrderDTO.builder()
                .guid("guid")
                .orderNo("guid")
                .deviceType(0)
                .orderFee(new BigDecimal("0.00"))
                .state(0)
                .build();
        when(mockTradeClientService.findByOrderGuid("e4c510fe-d776-4a77-bbe5-2067155947b9")).thenReturn(orderDTO);

        // Configure TradeClientService.queryItemByPadGuid(...).
        final DineInItemDTO dineInItemDTO4 = new DineInItemDTO();
        dineInItemDTO4.setUserWxPublicOpenId("openId");
        dineInItemDTO4.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO4.setItemGuid("itemGuid");
        dineInItemDTO4.setItemType(0);
        dineInItemDTO4.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO4.setItemTypeName("itemTypeName");
        dineInItemDTO4.setPrice(new BigDecimal("0.00"));
        dineInItemDTO4.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO4.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO4.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO4 = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO4 = new SubDineInItemDTO();
        subDineInItemDTO4.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO4.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO4.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO4));
        dineInItemDTO4.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO4));
        final ItemAttrDTO itemAttrDTO4 = new ItemAttrDTO();
        itemAttrDTO4.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO4.setNum(0);
        dineInItemDTO4.setItemAttrDTOS(Arrays.asList(itemAttrDTO4));
        final List<DineInItemDTO> dineInItemDTOS1 = Arrays.asList(dineInItemDTO4);
        when(mockTradeClientService.queryItemByPadGuid("guid")).thenReturn(dineInItemDTOS1);

        // Run the test
        final WxStoreMerchantOrderDTO result = wxStoreMerchantOrderImplUnderTest.getDetailPend(
                wxStoreMerchantOrderReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test(expected = BusinessException.class)
    public void testGetDetailPend_OrderItemServiceReturnsNoItems() {
        // Setup
        final WxStoreMerchantOrderReqDTO wxStoreMerchantOrderReqDTO = new WxStoreMerchantOrderReqDTO();
        wxStoreMerchantOrderReqDTO.setGuid("guid");
        wxStoreMerchantOrderReqDTO.setOrderState(0);
        wxStoreMerchantOrderReqDTO.setTradeMode(0);
        wxStoreMerchantOrderReqDTO.setLastGuid("lastGuid");
        wxStoreMerchantOrderReqDTO.setCount(0);

        // Configure WxStoreMerchantOrderMapstruct.getWxStoreMerchantOrder(...).
        final WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        wxStoreMerchantOrderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setOpenId("openId");
        wxStoreMerchantOrderDTO.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDTO.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDTO.setOrderState(0);
        wxStoreMerchantOrderDTO.setTradeMode(0);
        wxStoreMerchantOrderDTO.setTradeModeName("desc");
        wxStoreMerchantOrderDTO.setMsgSource(0);
        wxStoreMerchantOrderDTO.setMsgSourceName("desc");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setUserWxPublicOpenId("openId");
        dineInItemDTO.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO.setItemGuid("itemGuid");
        dineInItemDTO.setItemType(0);
        dineInItemDTO.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO.setItemTypeName("itemTypeName");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO = new SubDineInItemDTO();
        subDineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO));
        dineInItemDTO.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO));
        final ItemAttrDTO itemAttrDTO = new ItemAttrDTO();
        itemAttrDTO.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO.setNum(0);
        dineInItemDTO.setItemAttrDTOS(Arrays.asList(itemAttrDTO));
        wxStoreMerchantOrderDTO.setDineInItemDTOList(Arrays.asList(dineInItemDTO));
        wxStoreMerchantOrderDTO.setOrderNo("guid");
        wxStoreMerchantOrderDTO.setOperationSource(0);
        wxStoreMerchantOrderDTO.setOperationSourceName("desc");
        wxStoreMerchantOrderDTO.setOrderSource(0);
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        wxStoreMerchantOrderDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDO.setTotalPrice(new BigDecimal("0.00"));
        wxStoreMerchantOrderDO.setOpenId("openId");
        wxStoreMerchantOrderDO.setNickName("nickName");
        wxStoreMerchantOrderDO.setActualGuestsNo(0);
        wxStoreMerchantOrderDO.setTradeMode(0);
        wxStoreMerchantOrderDO.setOrderState(0);
        wxStoreMerchantOrderDO.setDenialReason("denialReason");
        wxStoreMerchantOrderDO.setOrderGuid("guid");
        wxStoreMerchantOrderDO.setRemark("remark");
        wxStoreMerchantOrderDO.setAreaGuid("areaGuid");
        wxStoreMerchantOrderDO.setAreaName("areaName");
        wxStoreMerchantOrderDO.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDO.setStoreName("name");
        wxStoreMerchantOrderDO.setBrandName("brandName");
        wxStoreMerchantOrderDO.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDO.setTableCode("tableCode");
        wxStoreMerchantOrderDO.setOperationGuid("openId");
        wxStoreMerchantOrderDO.setOperationSource(0);
        wxStoreMerchantOrderDO.setOperationName("nickName");
        wxStoreMerchantOrderDO.setOrderRecordGuid("58d0dfce-a97a-4608-b8a7-f7f4f68b843b");
        wxStoreMerchantOrderDO.setCombine("mainOrderGuid");
        wxStoreMerchantOrderDO.setOrderSource(0);
        when(mockWxStoreMerchantOrderMapstruct.getWxStoreMerchantOrder(wxStoreMerchantOrderDO))
                .thenReturn(wxStoreMerchantOrderDTO);

        when(mockOrderItemService.getItemListByMerchantGuid("guid")).thenReturn(Collections.emptyList());

        // Run the test
        wxStoreMerchantOrderImplUnderTest.getDetailPend(wxStoreMerchantOrderReqDTO);
    }

    @Test(expected = BusinessException.class)
    public void testGetDetailPend_TradeClientServiceQueryItemByPadGuidReturnsNoItems() {
        // Setup
        final WxStoreMerchantOrderReqDTO wxStoreMerchantOrderReqDTO = new WxStoreMerchantOrderReqDTO();
        wxStoreMerchantOrderReqDTO.setGuid("guid");
        wxStoreMerchantOrderReqDTO.setOrderState(0);
        wxStoreMerchantOrderReqDTO.setTradeMode(0);
        wxStoreMerchantOrderReqDTO.setLastGuid("lastGuid");
        wxStoreMerchantOrderReqDTO.setCount(0);

        // Configure TradeClientService.getPadOrderByGuid(...).
        final PadOrderRespDTO padOrderRespDTO = new PadOrderRespDTO();
        padOrderRespDTO.setGuid(0L);
        padOrderRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        padOrderRespDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        padOrderRespDTO.setOrderGuid("e4c510fe-d776-4a77-bbe5-2067155947b9");
        padOrderRespDTO.setActualGuestsNo(0);
        padOrderRespDTO.setAreaName("areaName");
        padOrderRespDTO.setDiningTableGuid("diningTableGuid");
        padOrderRespDTO.setTableCode("tableCode");
        padOrderRespDTO.setOrderState(0);
        padOrderRespDTO.setDenialReason("denialReason");
        padOrderRespDTO.setStoreGuid("storeGuid");
        padOrderRespDTO.setRemark("remark");
        padOrderRespDTO.setOperationGuid("openId");
        padOrderRespDTO.setOperationName("nickName");
        padOrderRespDTO.setCombineOrderGuid("combineOrderGuid");
        padOrderRespDTO.setOrderSource(0);
        when(mockTradeClientService.getPadOrderByGuid("guid")).thenReturn(padOrderRespDTO);

        // Configure WxStoreMerchantOrderMapstruct.padOrderDTO2wxOrderDTO(...).
        final WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        wxStoreMerchantOrderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setOpenId("openId");
        wxStoreMerchantOrderDTO.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDTO.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDTO.setOrderState(0);
        wxStoreMerchantOrderDTO.setTradeMode(0);
        wxStoreMerchantOrderDTO.setTradeModeName("desc");
        wxStoreMerchantOrderDTO.setMsgSource(0);
        wxStoreMerchantOrderDTO.setMsgSourceName("desc");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setUserWxPublicOpenId("openId");
        dineInItemDTO.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO.setItemGuid("itemGuid");
        dineInItemDTO.setItemType(0);
        dineInItemDTO.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO.setItemTypeName("itemTypeName");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO = new SubDineInItemDTO();
        subDineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO));
        dineInItemDTO.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO));
        final ItemAttrDTO itemAttrDTO = new ItemAttrDTO();
        itemAttrDTO.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO.setNum(0);
        dineInItemDTO.setItemAttrDTOS(Arrays.asList(itemAttrDTO));
        wxStoreMerchantOrderDTO.setDineInItemDTOList(Arrays.asList(dineInItemDTO));
        wxStoreMerchantOrderDTO.setOrderNo("guid");
        wxStoreMerchantOrderDTO.setOperationSource(0);
        wxStoreMerchantOrderDTO.setOperationSourceName("desc");
        wxStoreMerchantOrderDTO.setOrderSource(0);
        final PadOrderRespDTO padOrderRespDTO1 = new PadOrderRespDTO();
        padOrderRespDTO1.setGuid(0L);
        padOrderRespDTO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        padOrderRespDTO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        padOrderRespDTO1.setOrderGuid("e4c510fe-d776-4a77-bbe5-2067155947b9");
        padOrderRespDTO1.setActualGuestsNo(0);
        padOrderRespDTO1.setAreaName("areaName");
        padOrderRespDTO1.setDiningTableGuid("diningTableGuid");
        padOrderRespDTO1.setTableCode("tableCode");
        padOrderRespDTO1.setOrderState(0);
        padOrderRespDTO1.setDenialReason("denialReason");
        padOrderRespDTO1.setStoreGuid("storeGuid");
        padOrderRespDTO1.setRemark("remark");
        padOrderRespDTO1.setOperationGuid("openId");
        padOrderRespDTO1.setOperationName("nickName");
        padOrderRespDTO1.setCombineOrderGuid("combineOrderGuid");
        padOrderRespDTO1.setOrderSource(0);
        when(mockWxStoreMerchantOrderMapstruct.padOrderDTO2wxOrderDTO(padOrderRespDTO1))
                .thenReturn(wxStoreMerchantOrderDTO);

        // Configure TradeClientService.findByOrderGuid(...).
        final OrderDTO orderDTO = OrderDTO.builder()
                .guid("guid")
                .orderNo("guid")
                .deviceType(0)
                .orderFee(new BigDecimal("0.00"))
                .state(0)
                .build();
        when(mockTradeClientService.findByOrderGuid("e4c510fe-d776-4a77-bbe5-2067155947b9")).thenReturn(orderDTO);

        when(mockTradeClientService.queryItemByPadGuid("guid")).thenReturn(Collections.emptyList());

        // Run the test
        wxStoreMerchantOrderImplUnderTest.getDetailPend(wxStoreMerchantOrderReqDTO);
    }

    @Test
    public void testGetAttrTotalPrice() {
        // Setup
        final ItemAttrDTO itemAttrDTO = new ItemAttrDTO();
        itemAttrDTO.setGuid("655912f3-3fbc-4d98-a672-8f821170fe2e");
        itemAttrDTO.setAttrGuid("attrGuid");
        itemAttrDTO.setAttrName("attrName");
        itemAttrDTO.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO.setNum(0);
        final List<ItemAttrDTO> itemAttrDTOList = Arrays.asList(itemAttrDTO);

        // Run the test
        final BigDecimal result = wxStoreMerchantOrderImplUnderTest.getAttrTotalPrice(itemAttrDTOList);

        // Verify the results
        assertEquals(new BigDecimal("0.00"), result);
    }

    @Test
    public void testSubAttrFee() {
        // Setup
        final SubDineInItemDTO subDineInItemDTO = new SubDineInItemDTO();
        subDineInItemDTO.setGuid("711499ae-e272-47dc-a15f-2c920c58ee2e");
        subDineInItemDTO.setOriginalOrderItemGuid(0L);
        subDineInItemDTO.setItemGuid("itemGuid");
        subDineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO.setAddPrice(new BigDecimal("0.00"));
        final List<SubDineInItemDTO> subDineInItemDTOList = Arrays.asList(subDineInItemDTO);

        // Run the test
        final BigDecimal result = wxStoreMerchantOrderImplUnderTest.subAttrFee(subDineInItemDTOList);

        // Verify the results
        assertEquals(new BigDecimal("0.00"), result);
    }

    @Test
    public void testOperationMerchantOrder() {
        // Setup
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setUserWxPublicOpenId("openId");
        dineInItemDTO.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO.setItemGuid("itemGuid");
        dineInItemDTO.setItemType(0);
        dineInItemDTO.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO.setItemTypeName("itemTypeName");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO = new SubDineInItemDTO();
        subDineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO));
        dineInItemDTO.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO));
        final ItemAttrDTO itemAttrDTO = new ItemAttrDTO();
        itemAttrDTO.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO.setNum(0);
        dineInItemDTO.setItemAttrDTOS(Arrays.asList(itemAttrDTO));
        final WxOperateReqDTO wxOperateReqDTO = new WxOperateReqDTO("guid", 0, "denialReason", "tableGuid", "tableName",
                0, "areaName", "remark", Arrays.asList(dineInItemDTO));
        final WxStoreMerchantOperationDTO expectedResult = WxStoreMerchantOperationDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .storeGuid("storeGuid")
                .diningTableGuid("diningTableGuid")
                .tableCode("tableCode")
                .areaName("areaName")
                .storeName("storeName")
                .brandName("brandName")
                .estimateItemRespDTO(new EstimateItemRespDTO())
                .errorMsg("errorMsg")
                .build();

        // Configure TradeClientService.getPadOrderByGuid(...).
        final PadOrderRespDTO padOrderRespDTO = new PadOrderRespDTO();
        padOrderRespDTO.setGuid(0L);
        padOrderRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        padOrderRespDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        padOrderRespDTO.setOrderGuid("e4c510fe-d776-4a77-bbe5-2067155947b9");
        padOrderRespDTO.setActualGuestsNo(0);
        padOrderRespDTO.setAreaName("areaName");
        padOrderRespDTO.setDiningTableGuid("diningTableGuid");
        padOrderRespDTO.setTableCode("tableCode");
        padOrderRespDTO.setOrderState(0);
        padOrderRespDTO.setDenialReason("denialReason");
        padOrderRespDTO.setStoreGuid("storeGuid");
        padOrderRespDTO.setRemark("remark");
        padOrderRespDTO.setOperationGuid("openId");
        padOrderRespDTO.setOperationName("nickName");
        padOrderRespDTO.setCombineOrderGuid("combineOrderGuid");
        padOrderRespDTO.setOrderSource(0);
        when(mockTradeClientService.getPadOrderByGuid("guid")).thenReturn(padOrderRespDTO);

        // Configure WxStoreDineInOrderClientService.updateRemark(...).
        final CreateDineInOrderReqDTO createDineInOrderReqDTO = new CreateDineInOrderReqDTO();
        createDineInOrderReqDTO.setDeviceType(0);
        createDineInOrderReqDTO.setDeviceId("openId");
        createDineInOrderReqDTO.setEnterpriseGuid("enterpriseGuid");
        createDineInOrderReqDTO.setEnterpriseName("enterpriseName");
        createDineInOrderReqDTO.setStoreGuid("storeGuid");
        createDineInOrderReqDTO.setStoreName("name");
        createDineInOrderReqDTO.setUserGuid("openId");
        createDineInOrderReqDTO.setUserName("nickName");
        createDineInOrderReqDTO.setGuid("e4c510fe-d776-4a77-bbe5-2067155947b9");
        createDineInOrderReqDTO.setUserWxPublicOpenId("openId");
        createDineInOrderReqDTO.setRemark("remark");
        createDineInOrderReqDTO.setDiningTableGuid("diningTableGuid");
        createDineInOrderReqDTO.setDiningTableName("tableCode");
        createDineInOrderReqDTO.setAreaName("areaName");
        createDineInOrderReqDTO.setGuestCount(0);
        createDineInOrderReqDTO.setPrint(0);
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setUserWxPublicOpenId("openId");
        dineInItemDTO1.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO1.setItemGuid("itemGuid");
        dineInItemDTO1.setItemType(0);
        dineInItemDTO1.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO1.setItemTypeName("itemTypeName");
        dineInItemDTO1.setPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO1.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO1 = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO1 = new SubDineInItemDTO();
        subDineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO1.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO1.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO1));
        dineInItemDTO1.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO1));
        final ItemAttrDTO itemAttrDTO1 = new ItemAttrDTO();
        itemAttrDTO1.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO1.setNum(0);
        dineInItemDTO1.setItemAttrDTOS(Arrays.asList(itemAttrDTO1));
        createDineInOrderReqDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO1));
        createDineInOrderReqDTO.setEstimateType(0);
        when(mockWxStoreDineInOrderClientService.updateRemark(createDineInOrderReqDTO)).thenReturn(false);

        // Configure TradeClientService.getPadOrderAddItemInfoByRedis(...).
        final CreateDineInOrderReqDTO createDineInOrderReqDTO1 = new CreateDineInOrderReqDTO();
        createDineInOrderReqDTO1.setDeviceType(0);
        createDineInOrderReqDTO1.setDeviceId("openId");
        createDineInOrderReqDTO1.setEnterpriseGuid("enterpriseGuid");
        createDineInOrderReqDTO1.setEnterpriseName("enterpriseName");
        createDineInOrderReqDTO1.setStoreGuid("storeGuid");
        createDineInOrderReqDTO1.setStoreName("name");
        createDineInOrderReqDTO1.setUserGuid("openId");
        createDineInOrderReqDTO1.setUserName("nickName");
        createDineInOrderReqDTO1.setGuid("e4c510fe-d776-4a77-bbe5-2067155947b9");
        createDineInOrderReqDTO1.setUserWxPublicOpenId("openId");
        createDineInOrderReqDTO1.setRemark("remark");
        createDineInOrderReqDTO1.setDiningTableGuid("diningTableGuid");
        createDineInOrderReqDTO1.setDiningTableName("tableCode");
        createDineInOrderReqDTO1.setAreaName("areaName");
        createDineInOrderReqDTO1.setGuestCount(0);
        createDineInOrderReqDTO1.setPrint(0);
        final DineInItemDTO dineInItemDTO2 = new DineInItemDTO();
        dineInItemDTO2.setUserWxPublicOpenId("openId");
        dineInItemDTO2.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO2.setItemGuid("itemGuid");
        dineInItemDTO2.setItemType(0);
        dineInItemDTO2.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO2.setItemTypeName("itemTypeName");
        dineInItemDTO2.setPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO2.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO2 = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO2 = new SubDineInItemDTO();
        subDineInItemDTO2.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO2.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO2.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO2));
        dineInItemDTO2.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO2));
        final ItemAttrDTO itemAttrDTO2 = new ItemAttrDTO();
        itemAttrDTO2.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO2.setNum(0);
        dineInItemDTO2.setItemAttrDTOS(Arrays.asList(itemAttrDTO2));
        createDineInOrderReqDTO1.setDineInItemDTOS(Arrays.asList(dineInItemDTO2));
        createDineInOrderReqDTO1.setEstimateType(0);
        when(mockTradeClientService.getPadOrderAddItemInfoByRedis("key")).thenReturn(createDineInOrderReqDTO1);

        // Configure WxStoreDineInOrderClientService.batchAddItems(...).
        final EstimateItemRespDTO estimateItemRespDTO = new EstimateItemRespDTO();
        estimateItemRespDTO.setResult(false);
        estimateItemRespDTO.setEstimate(false);
        estimateItemRespDTO.setErrorMsg("errorMsg");
        estimateItemRespDTO.setEstimateInfo("estimateInfo");
        estimateItemRespDTO.setEstimateSkuGuids(Arrays.asList("value"));
        final CreateDineInOrderReqDTO createDineInOrderReqDTO2 = new CreateDineInOrderReqDTO();
        createDineInOrderReqDTO2.setDeviceType(0);
        createDineInOrderReqDTO2.setDeviceId("openId");
        createDineInOrderReqDTO2.setEnterpriseGuid("enterpriseGuid");
        createDineInOrderReqDTO2.setEnterpriseName("enterpriseName");
        createDineInOrderReqDTO2.setStoreGuid("storeGuid");
        createDineInOrderReqDTO2.setStoreName("name");
        createDineInOrderReqDTO2.setUserGuid("openId");
        createDineInOrderReqDTO2.setUserName("nickName");
        createDineInOrderReqDTO2.setGuid("e4c510fe-d776-4a77-bbe5-2067155947b9");
        createDineInOrderReqDTO2.setUserWxPublicOpenId("openId");
        createDineInOrderReqDTO2.setRemark("remark");
        createDineInOrderReqDTO2.setDiningTableGuid("diningTableGuid");
        createDineInOrderReqDTO2.setDiningTableName("tableCode");
        createDineInOrderReqDTO2.setAreaName("areaName");
        createDineInOrderReqDTO2.setGuestCount(0);
        createDineInOrderReqDTO2.setPrint(0);
        final DineInItemDTO dineInItemDTO3 = new DineInItemDTO();
        dineInItemDTO3.setUserWxPublicOpenId("openId");
        dineInItemDTO3.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO3.setItemGuid("itemGuid");
        dineInItemDTO3.setItemType(0);
        dineInItemDTO3.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO3.setItemTypeName("itemTypeName");
        dineInItemDTO3.setPrice(new BigDecimal("0.00"));
        dineInItemDTO3.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO3.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO3.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO3 = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO3 = new SubDineInItemDTO();
        subDineInItemDTO3.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO3.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO3.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO3));
        dineInItemDTO3.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO3));
        final ItemAttrDTO itemAttrDTO3 = new ItemAttrDTO();
        itemAttrDTO3.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO3.setNum(0);
        dineInItemDTO3.setItemAttrDTOS(Arrays.asList(itemAttrDTO3));
        createDineInOrderReqDTO2.setDineInItemDTOS(Arrays.asList(dineInItemDTO3));
        createDineInOrderReqDTO2.setEstimateType(0);
        when(mockWxStoreDineInOrderClientService.batchAddItems(createDineInOrderReqDTO2))
                .thenReturn(estimateItemRespDTO);

        when(mockRedisUtils.generatdDTOGuid(BusinessMessageDTO.class)).thenReturn("messageGuid");

        // Configure OrganizationClientService.queryStoreByGuid(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("13b15130-a1a8-4a4f-8eeb-d1af12c961a2");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        when(mockOrganizationClientService.queryStoreByGuid("storeGuid")).thenReturn(storeDTO);

        // Configure WxStoreTableClientService.getTableByGuid(...).
        final TableDTO tableDTO = TableDTO.builder()
                .orderGuid("orderGuid")
                .build();
        when(mockTableClientService.getTableByGuid("diningTableGuid")).thenReturn(tableDTO);

        // Configure OrganizationClientService.queryDeviceByStoreTable(...).
        final StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO("enterpriseGuid", "storeNo", "storeGuid", "storeName",
                "deviceNo", "deviceGuid", false, false, 0, "deviceName", 0, "createUserGuid", "createUserName",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "tableGuid", 0);
        when(mockOrganizationClientService.queryDeviceByStoreTable(any(PadOrderTypeReqDTO.class)))
                .thenReturn(storeDeviceDTO);

        // Configure TradeClientService.listPadOrderByCombineOrderGuid(...).
        final PadOrderRespDTO padOrderRespDTO1 = new PadOrderRespDTO();
        padOrderRespDTO1.setGuid(0L);
        padOrderRespDTO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        padOrderRespDTO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        padOrderRespDTO1.setOrderGuid("e4c510fe-d776-4a77-bbe5-2067155947b9");
        padOrderRespDTO1.setActualGuestsNo(0);
        padOrderRespDTO1.setAreaName("areaName");
        padOrderRespDTO1.setDiningTableGuid("diningTableGuid");
        padOrderRespDTO1.setTableCode("tableCode");
        padOrderRespDTO1.setOrderState(0);
        padOrderRespDTO1.setDenialReason("denialReason");
        padOrderRespDTO1.setStoreGuid("storeGuid");
        padOrderRespDTO1.setRemark("remark");
        padOrderRespDTO1.setOperationGuid("openId");
        padOrderRespDTO1.setOperationName("nickName");
        padOrderRespDTO1.setCombineOrderGuid("combineOrderGuid");
        padOrderRespDTO1.setOrderSource(0);
        final List<PadOrderRespDTO> padOrderRespDTOS = Arrays.asList(padOrderRespDTO1);
        when(mockTradeClientService.listPadOrderByCombineOrderGuid("combineOrderGuid")).thenReturn(padOrderRespDTOS);

        // Configure OrganizationClientService.listDeviceByStoreTable(...).
        final List<StoreDeviceDTO> storeDeviceDTOS = Arrays.asList(
                new StoreDeviceDTO("enterpriseGuid", "storeNo", "storeGuid", "storeName", "deviceNo", "deviceGuid",
                        false, false, 0, "deviceName", 0, "createUserGuid", "createUserName",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), "tableGuid", 0));
        when(mockOrganizationClientService.listDeviceByStoreTable(any(PadOrderTypeReqDTO.class)))
                .thenReturn(storeDeviceDTOS);

        // Run the test
        final WxStoreMerchantOperationDTO result = wxStoreMerchantOrderImplUnderTest.operationMerchantOrder(
                wxOperateReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);

        // Confirm TradeClientService.updateById(...).
        final PadOrderRespDTO padOrderRespDTO2 = new PadOrderRespDTO();
        padOrderRespDTO2.setGuid(0L);
        padOrderRespDTO2.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        padOrderRespDTO2.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        padOrderRespDTO2.setOrderGuid("e4c510fe-d776-4a77-bbe5-2067155947b9");
        padOrderRespDTO2.setActualGuestsNo(0);
        padOrderRespDTO2.setAreaName("areaName");
        padOrderRespDTO2.setDiningTableGuid("diningTableGuid");
        padOrderRespDTO2.setTableCode("tableCode");
        padOrderRespDTO2.setOrderState(0);
        padOrderRespDTO2.setDenialReason("denialReason");
        padOrderRespDTO2.setStoreGuid("storeGuid");
        padOrderRespDTO2.setRemark("remark");
        padOrderRespDTO2.setOperationGuid("openId");
        padOrderRespDTO2.setOperationName("nickName");
        padOrderRespDTO2.setCombineOrderGuid("combineOrderGuid");
        padOrderRespDTO2.setOrderSource(0);
        verify(mockTradeClientService).updateById(padOrderRespDTO2);
        verify(mockBizMsgFeignClient).msg(BusinessMessageDTO.builder()
                .messageGuid("messageGuid")
                .subject("name")
                .content("content")
                .messageType(0)
                .detailMessageType(0)
                .platform("desc")
                .storeGuid("storeGuid")
                .storeName("name")
                .state("state")
                .createTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .pushTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .messageTypeStr("messageTypeStr")
                .build());
    }

    @Test(expected = BusinessException.class)
    public void testOperationMerchantOrder_TradeClientServiceListPadOrderByCombineOrderGuidReturnsNoItems() {
        // Setup
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setUserWxPublicOpenId("openId");
        dineInItemDTO.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO.setItemGuid("itemGuid");
        dineInItemDTO.setItemType(0);
        dineInItemDTO.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO.setItemTypeName("itemTypeName");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO = new SubDineInItemDTO();
        subDineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO));
        dineInItemDTO.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO));
        final ItemAttrDTO itemAttrDTO = new ItemAttrDTO();
        itemAttrDTO.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO.setNum(0);
        dineInItemDTO.setItemAttrDTOS(Arrays.asList(itemAttrDTO));
        final WxOperateReqDTO wxOperateReqDTO = new WxOperateReqDTO("guid", 0, "denialReason", "tableGuid", "tableName",
                0, "areaName", "remark", Arrays.asList(dineInItemDTO));

        // Configure TradeClientService.getPadOrderByGuid(...).
        final PadOrderRespDTO padOrderRespDTO = new PadOrderRespDTO();
        padOrderRespDTO.setGuid(0L);
        padOrderRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        padOrderRespDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        padOrderRespDTO.setOrderGuid("e4c510fe-d776-4a77-bbe5-2067155947b9");
        padOrderRespDTO.setActualGuestsNo(0);
        padOrderRespDTO.setAreaName("areaName");
        padOrderRespDTO.setDiningTableGuid("diningTableGuid");
        padOrderRespDTO.setTableCode("tableCode");
        padOrderRespDTO.setOrderState(0);
        padOrderRespDTO.setDenialReason("denialReason");
        padOrderRespDTO.setStoreGuid("storeGuid");
        padOrderRespDTO.setRemark("remark");
        padOrderRespDTO.setOperationGuid("openId");
        padOrderRespDTO.setOperationName("nickName");
        padOrderRespDTO.setCombineOrderGuid("combineOrderGuid");
        padOrderRespDTO.setOrderSource(0);
        when(mockTradeClientService.getPadOrderByGuid("guid")).thenReturn(padOrderRespDTO);

        // Configure WxStoreDineInOrderClientService.updateRemark(...).
        final CreateDineInOrderReqDTO createDineInOrderReqDTO = new CreateDineInOrderReqDTO();
        createDineInOrderReqDTO.setDeviceType(0);
        createDineInOrderReqDTO.setDeviceId("openId");
        createDineInOrderReqDTO.setEnterpriseGuid("enterpriseGuid");
        createDineInOrderReqDTO.setEnterpriseName("enterpriseName");
        createDineInOrderReqDTO.setStoreGuid("storeGuid");
        createDineInOrderReqDTO.setStoreName("name");
        createDineInOrderReqDTO.setUserGuid("openId");
        createDineInOrderReqDTO.setUserName("nickName");
        createDineInOrderReqDTO.setGuid("e4c510fe-d776-4a77-bbe5-2067155947b9");
        createDineInOrderReqDTO.setUserWxPublicOpenId("openId");
        createDineInOrderReqDTO.setRemark("remark");
        createDineInOrderReqDTO.setDiningTableGuid("diningTableGuid");
        createDineInOrderReqDTO.setDiningTableName("tableCode");
        createDineInOrderReqDTO.setAreaName("areaName");
        createDineInOrderReqDTO.setGuestCount(0);
        createDineInOrderReqDTO.setPrint(0);
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setUserWxPublicOpenId("openId");
        dineInItemDTO1.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO1.setItemGuid("itemGuid");
        dineInItemDTO1.setItemType(0);
        dineInItemDTO1.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO1.setItemTypeName("itemTypeName");
        dineInItemDTO1.setPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO1.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO1 = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO1 = new SubDineInItemDTO();
        subDineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO1.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO1.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO1));
        dineInItemDTO1.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO1));
        final ItemAttrDTO itemAttrDTO1 = new ItemAttrDTO();
        itemAttrDTO1.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO1.setNum(0);
        dineInItemDTO1.setItemAttrDTOS(Arrays.asList(itemAttrDTO1));
        createDineInOrderReqDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO1));
        createDineInOrderReqDTO.setEstimateType(0);
        when(mockWxStoreDineInOrderClientService.updateRemark(createDineInOrderReqDTO)).thenReturn(false);

        // Configure TradeClientService.getPadOrderAddItemInfoByRedis(...).
        final CreateDineInOrderReqDTO createDineInOrderReqDTO1 = new CreateDineInOrderReqDTO();
        createDineInOrderReqDTO1.setDeviceType(0);
        createDineInOrderReqDTO1.setDeviceId("openId");
        createDineInOrderReqDTO1.setEnterpriseGuid("enterpriseGuid");
        createDineInOrderReqDTO1.setEnterpriseName("enterpriseName");
        createDineInOrderReqDTO1.setStoreGuid("storeGuid");
        createDineInOrderReqDTO1.setStoreName("name");
        createDineInOrderReqDTO1.setUserGuid("openId");
        createDineInOrderReqDTO1.setUserName("nickName");
        createDineInOrderReqDTO1.setGuid("e4c510fe-d776-4a77-bbe5-2067155947b9");
        createDineInOrderReqDTO1.setUserWxPublicOpenId("openId");
        createDineInOrderReqDTO1.setRemark("remark");
        createDineInOrderReqDTO1.setDiningTableGuid("diningTableGuid");
        createDineInOrderReqDTO1.setDiningTableName("tableCode");
        createDineInOrderReqDTO1.setAreaName("areaName");
        createDineInOrderReqDTO1.setGuestCount(0);
        createDineInOrderReqDTO1.setPrint(0);
        final DineInItemDTO dineInItemDTO2 = new DineInItemDTO();
        dineInItemDTO2.setUserWxPublicOpenId("openId");
        dineInItemDTO2.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO2.setItemGuid("itemGuid");
        dineInItemDTO2.setItemType(0);
        dineInItemDTO2.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO2.setItemTypeName("itemTypeName");
        dineInItemDTO2.setPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO2.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO2 = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO2 = new SubDineInItemDTO();
        subDineInItemDTO2.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO2.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO2.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO2));
        dineInItemDTO2.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO2));
        final ItemAttrDTO itemAttrDTO2 = new ItemAttrDTO();
        itemAttrDTO2.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO2.setNum(0);
        dineInItemDTO2.setItemAttrDTOS(Arrays.asList(itemAttrDTO2));
        createDineInOrderReqDTO1.setDineInItemDTOS(Arrays.asList(dineInItemDTO2));
        createDineInOrderReqDTO1.setEstimateType(0);
        when(mockTradeClientService.getPadOrderAddItemInfoByRedis("key")).thenReturn(createDineInOrderReqDTO1);

        // Configure WxStoreDineInOrderClientService.batchAddItems(...).
        final EstimateItemRespDTO estimateItemRespDTO = new EstimateItemRespDTO();
        estimateItemRespDTO.setResult(false);
        estimateItemRespDTO.setEstimate(false);
        estimateItemRespDTO.setErrorMsg("errorMsg");
        estimateItemRespDTO.setEstimateInfo("estimateInfo");
        estimateItemRespDTO.setEstimateSkuGuids(Arrays.asList("value"));
        final CreateDineInOrderReqDTO createDineInOrderReqDTO2 = new CreateDineInOrderReqDTO();
        createDineInOrderReqDTO2.setDeviceType(0);
        createDineInOrderReqDTO2.setDeviceId("openId");
        createDineInOrderReqDTO2.setEnterpriseGuid("enterpriseGuid");
        createDineInOrderReqDTO2.setEnterpriseName("enterpriseName");
        createDineInOrderReqDTO2.setStoreGuid("storeGuid");
        createDineInOrderReqDTO2.setStoreName("name");
        createDineInOrderReqDTO2.setUserGuid("openId");
        createDineInOrderReqDTO2.setUserName("nickName");
        createDineInOrderReqDTO2.setGuid("e4c510fe-d776-4a77-bbe5-2067155947b9");
        createDineInOrderReqDTO2.setUserWxPublicOpenId("openId");
        createDineInOrderReqDTO2.setRemark("remark");
        createDineInOrderReqDTO2.setDiningTableGuid("diningTableGuid");
        createDineInOrderReqDTO2.setDiningTableName("tableCode");
        createDineInOrderReqDTO2.setAreaName("areaName");
        createDineInOrderReqDTO2.setGuestCount(0);
        createDineInOrderReqDTO2.setPrint(0);
        final DineInItemDTO dineInItemDTO3 = new DineInItemDTO();
        dineInItemDTO3.setUserWxPublicOpenId("openId");
        dineInItemDTO3.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO3.setItemGuid("itemGuid");
        dineInItemDTO3.setItemType(0);
        dineInItemDTO3.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO3.setItemTypeName("itemTypeName");
        dineInItemDTO3.setPrice(new BigDecimal("0.00"));
        dineInItemDTO3.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO3.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO3.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO3 = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO3 = new SubDineInItemDTO();
        subDineInItemDTO3.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO3.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO3.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO3));
        dineInItemDTO3.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO3));
        final ItemAttrDTO itemAttrDTO3 = new ItemAttrDTO();
        itemAttrDTO3.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO3.setNum(0);
        dineInItemDTO3.setItemAttrDTOS(Arrays.asList(itemAttrDTO3));
        createDineInOrderReqDTO2.setDineInItemDTOS(Arrays.asList(dineInItemDTO3));
        createDineInOrderReqDTO2.setEstimateType(0);
        when(mockWxStoreDineInOrderClientService.batchAddItems(createDineInOrderReqDTO2))
                .thenReturn(estimateItemRespDTO);

        when(mockRedisUtils.generatdDTOGuid(BusinessMessageDTO.class)).thenReturn("messageGuid");

        // Configure OrganizationClientService.queryStoreByGuid(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("13b15130-a1a8-4a4f-8eeb-d1af12c961a2");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        when(mockOrganizationClientService.queryStoreByGuid("storeGuid")).thenReturn(storeDTO);

        // Configure WxStoreTableClientService.getTableByGuid(...).
        final TableDTO tableDTO = TableDTO.builder()
                .orderGuid("orderGuid")
                .build();
        when(mockTableClientService.getTableByGuid("diningTableGuid")).thenReturn(tableDTO);

        when(mockTradeClientService.listPadOrderByCombineOrderGuid("combineOrderGuid"))
                .thenReturn(Collections.emptyList());

        // Run the test
        wxStoreMerchantOrderImplUnderTest.operationMerchantOrder(wxOperateReqDTO);
    }

    @Test
    public void testOperationMerchantOrder_OrganizationClientServiceListDeviceByStoreTableReturnsNoItems() {
        // Setup
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setUserWxPublicOpenId("openId");
        dineInItemDTO.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO.setItemGuid("itemGuid");
        dineInItemDTO.setItemType(0);
        dineInItemDTO.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO.setItemTypeName("itemTypeName");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO = new SubDineInItemDTO();
        subDineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO));
        dineInItemDTO.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO));
        final ItemAttrDTO itemAttrDTO = new ItemAttrDTO();
        itemAttrDTO.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO.setNum(0);
        dineInItemDTO.setItemAttrDTOS(Arrays.asList(itemAttrDTO));
        final WxOperateReqDTO wxOperateReqDTO = new WxOperateReqDTO("guid", 0, "denialReason", "tableGuid", "tableName",
                0, "areaName", "remark", Arrays.asList(dineInItemDTO));
        final WxStoreMerchantOperationDTO expectedResult = WxStoreMerchantOperationDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .storeGuid("storeGuid")
                .diningTableGuid("diningTableGuid")
                .tableCode("tableCode")
                .areaName("areaName")
                .storeName("storeName")
                .brandName("brandName")
                .estimateItemRespDTO(new EstimateItemRespDTO())
                .errorMsg("errorMsg")
                .build();

        // Configure TradeClientService.getPadOrderByGuid(...).
        final PadOrderRespDTO padOrderRespDTO = new PadOrderRespDTO();
        padOrderRespDTO.setGuid(0L);
        padOrderRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        padOrderRespDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        padOrderRespDTO.setOrderGuid("e4c510fe-d776-4a77-bbe5-2067155947b9");
        padOrderRespDTO.setActualGuestsNo(0);
        padOrderRespDTO.setAreaName("areaName");
        padOrderRespDTO.setDiningTableGuid("diningTableGuid");
        padOrderRespDTO.setTableCode("tableCode");
        padOrderRespDTO.setOrderState(0);
        padOrderRespDTO.setDenialReason("denialReason");
        padOrderRespDTO.setStoreGuid("storeGuid");
        padOrderRespDTO.setRemark("remark");
        padOrderRespDTO.setOperationGuid("openId");
        padOrderRespDTO.setOperationName("nickName");
        padOrderRespDTO.setCombineOrderGuid("combineOrderGuid");
        padOrderRespDTO.setOrderSource(0);
        when(mockTradeClientService.getPadOrderByGuid("guid")).thenReturn(padOrderRespDTO);

        // Configure WxStoreDineInOrderClientService.updateRemark(...).
        final CreateDineInOrderReqDTO createDineInOrderReqDTO = new CreateDineInOrderReqDTO();
        createDineInOrderReqDTO.setDeviceType(0);
        createDineInOrderReqDTO.setDeviceId("openId");
        createDineInOrderReqDTO.setEnterpriseGuid("enterpriseGuid");
        createDineInOrderReqDTO.setEnterpriseName("enterpriseName");
        createDineInOrderReqDTO.setStoreGuid("storeGuid");
        createDineInOrderReqDTO.setStoreName("name");
        createDineInOrderReqDTO.setUserGuid("openId");
        createDineInOrderReqDTO.setUserName("nickName");
        createDineInOrderReqDTO.setGuid("e4c510fe-d776-4a77-bbe5-2067155947b9");
        createDineInOrderReqDTO.setUserWxPublicOpenId("openId");
        createDineInOrderReqDTO.setRemark("remark");
        createDineInOrderReqDTO.setDiningTableGuid("diningTableGuid");
        createDineInOrderReqDTO.setDiningTableName("tableCode");
        createDineInOrderReqDTO.setAreaName("areaName");
        createDineInOrderReqDTO.setGuestCount(0);
        createDineInOrderReqDTO.setPrint(0);
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setUserWxPublicOpenId("openId");
        dineInItemDTO1.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO1.setItemGuid("itemGuid");
        dineInItemDTO1.setItemType(0);
        dineInItemDTO1.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO1.setItemTypeName("itemTypeName");
        dineInItemDTO1.setPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO1.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO1 = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO1 = new SubDineInItemDTO();
        subDineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO1.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO1.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO1));
        dineInItemDTO1.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO1));
        final ItemAttrDTO itemAttrDTO1 = new ItemAttrDTO();
        itemAttrDTO1.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO1.setNum(0);
        dineInItemDTO1.setItemAttrDTOS(Arrays.asList(itemAttrDTO1));
        createDineInOrderReqDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO1));
        createDineInOrderReqDTO.setEstimateType(0);
        when(mockWxStoreDineInOrderClientService.updateRemark(createDineInOrderReqDTO)).thenReturn(false);

        // Configure TradeClientService.getPadOrderAddItemInfoByRedis(...).
        final CreateDineInOrderReqDTO createDineInOrderReqDTO1 = new CreateDineInOrderReqDTO();
        createDineInOrderReqDTO1.setDeviceType(0);
        createDineInOrderReqDTO1.setDeviceId("openId");
        createDineInOrderReqDTO1.setEnterpriseGuid("enterpriseGuid");
        createDineInOrderReqDTO1.setEnterpriseName("enterpriseName");
        createDineInOrderReqDTO1.setStoreGuid("storeGuid");
        createDineInOrderReqDTO1.setStoreName("name");
        createDineInOrderReqDTO1.setUserGuid("openId");
        createDineInOrderReqDTO1.setUserName("nickName");
        createDineInOrderReqDTO1.setGuid("e4c510fe-d776-4a77-bbe5-2067155947b9");
        createDineInOrderReqDTO1.setUserWxPublicOpenId("openId");
        createDineInOrderReqDTO1.setRemark("remark");
        createDineInOrderReqDTO1.setDiningTableGuid("diningTableGuid");
        createDineInOrderReqDTO1.setDiningTableName("tableCode");
        createDineInOrderReqDTO1.setAreaName("areaName");
        createDineInOrderReqDTO1.setGuestCount(0);
        createDineInOrderReqDTO1.setPrint(0);
        final DineInItemDTO dineInItemDTO2 = new DineInItemDTO();
        dineInItemDTO2.setUserWxPublicOpenId("openId");
        dineInItemDTO2.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO2.setItemGuid("itemGuid");
        dineInItemDTO2.setItemType(0);
        dineInItemDTO2.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO2.setItemTypeName("itemTypeName");
        dineInItemDTO2.setPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO2.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO2 = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO2 = new SubDineInItemDTO();
        subDineInItemDTO2.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO2.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO2.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO2));
        dineInItemDTO2.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO2));
        final ItemAttrDTO itemAttrDTO2 = new ItemAttrDTO();
        itemAttrDTO2.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO2.setNum(0);
        dineInItemDTO2.setItemAttrDTOS(Arrays.asList(itemAttrDTO2));
        createDineInOrderReqDTO1.setDineInItemDTOS(Arrays.asList(dineInItemDTO2));
        createDineInOrderReqDTO1.setEstimateType(0);
        when(mockTradeClientService.getPadOrderAddItemInfoByRedis("key")).thenReturn(createDineInOrderReqDTO1);

        // Configure WxStoreDineInOrderClientService.batchAddItems(...).
        final EstimateItemRespDTO estimateItemRespDTO = new EstimateItemRespDTO();
        estimateItemRespDTO.setResult(false);
        estimateItemRespDTO.setEstimate(false);
        estimateItemRespDTO.setErrorMsg("errorMsg");
        estimateItemRespDTO.setEstimateInfo("estimateInfo");
        estimateItemRespDTO.setEstimateSkuGuids(Arrays.asList("value"));
        final CreateDineInOrderReqDTO createDineInOrderReqDTO2 = new CreateDineInOrderReqDTO();
        createDineInOrderReqDTO2.setDeviceType(0);
        createDineInOrderReqDTO2.setDeviceId("openId");
        createDineInOrderReqDTO2.setEnterpriseGuid("enterpriseGuid");
        createDineInOrderReqDTO2.setEnterpriseName("enterpriseName");
        createDineInOrderReqDTO2.setStoreGuid("storeGuid");
        createDineInOrderReqDTO2.setStoreName("name");
        createDineInOrderReqDTO2.setUserGuid("openId");
        createDineInOrderReqDTO2.setUserName("nickName");
        createDineInOrderReqDTO2.setGuid("e4c510fe-d776-4a77-bbe5-2067155947b9");
        createDineInOrderReqDTO2.setUserWxPublicOpenId("openId");
        createDineInOrderReqDTO2.setRemark("remark");
        createDineInOrderReqDTO2.setDiningTableGuid("diningTableGuid");
        createDineInOrderReqDTO2.setDiningTableName("tableCode");
        createDineInOrderReqDTO2.setAreaName("areaName");
        createDineInOrderReqDTO2.setGuestCount(0);
        createDineInOrderReqDTO2.setPrint(0);
        final DineInItemDTO dineInItemDTO3 = new DineInItemDTO();
        dineInItemDTO3.setUserWxPublicOpenId("openId");
        dineInItemDTO3.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO3.setItemGuid("itemGuid");
        dineInItemDTO3.setItemType(0);
        dineInItemDTO3.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO3.setItemTypeName("itemTypeName");
        dineInItemDTO3.setPrice(new BigDecimal("0.00"));
        dineInItemDTO3.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO3.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO3.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO3 = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO3 = new SubDineInItemDTO();
        subDineInItemDTO3.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO3.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO3.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO3));
        dineInItemDTO3.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO3));
        final ItemAttrDTO itemAttrDTO3 = new ItemAttrDTO();
        itemAttrDTO3.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO3.setNum(0);
        dineInItemDTO3.setItemAttrDTOS(Arrays.asList(itemAttrDTO3));
        createDineInOrderReqDTO2.setDineInItemDTOS(Arrays.asList(dineInItemDTO3));
        createDineInOrderReqDTO2.setEstimateType(0);
        when(mockWxStoreDineInOrderClientService.batchAddItems(createDineInOrderReqDTO2))
                .thenReturn(estimateItemRespDTO);

        when(mockRedisUtils.generatdDTOGuid(BusinessMessageDTO.class)).thenReturn("messageGuid");

        // Configure OrganizationClientService.queryStoreByGuid(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("13b15130-a1a8-4a4f-8eeb-d1af12c961a2");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        when(mockOrganizationClientService.queryStoreByGuid("storeGuid")).thenReturn(storeDTO);

        // Configure WxStoreTableClientService.getTableByGuid(...).
        final TableDTO tableDTO = TableDTO.builder()
                .orderGuid("orderGuid")
                .build();
        when(mockTableClientService.getTableByGuid("diningTableGuid")).thenReturn(tableDTO);

        // Configure TradeClientService.listPadOrderByCombineOrderGuid(...).
        final PadOrderRespDTO padOrderRespDTO1 = new PadOrderRespDTO();
        padOrderRespDTO1.setGuid(0L);
        padOrderRespDTO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        padOrderRespDTO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        padOrderRespDTO1.setOrderGuid("e4c510fe-d776-4a77-bbe5-2067155947b9");
        padOrderRespDTO1.setActualGuestsNo(0);
        padOrderRespDTO1.setAreaName("areaName");
        padOrderRespDTO1.setDiningTableGuid("diningTableGuid");
        padOrderRespDTO1.setTableCode("tableCode");
        padOrderRespDTO1.setOrderState(0);
        padOrderRespDTO1.setDenialReason("denialReason");
        padOrderRespDTO1.setStoreGuid("storeGuid");
        padOrderRespDTO1.setRemark("remark");
        padOrderRespDTO1.setOperationGuid("openId");
        padOrderRespDTO1.setOperationName("nickName");
        padOrderRespDTO1.setCombineOrderGuid("combineOrderGuid");
        padOrderRespDTO1.setOrderSource(0);
        final List<PadOrderRespDTO> padOrderRespDTOS = Arrays.asList(padOrderRespDTO1);
        when(mockTradeClientService.listPadOrderByCombineOrderGuid("combineOrderGuid")).thenReturn(padOrderRespDTOS);

        when(mockOrganizationClientService.listDeviceByStoreTable(any(PadOrderTypeReqDTO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final WxStoreMerchantOperationDTO result = wxStoreMerchantOrderImplUnderTest.operationMerchantOrder(
                wxOperateReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);

        // Confirm TradeClientService.updateById(...).
        final PadOrderRespDTO padOrderRespDTO2 = new PadOrderRespDTO();
        padOrderRespDTO2.setGuid(0L);
        padOrderRespDTO2.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        padOrderRespDTO2.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        padOrderRespDTO2.setOrderGuid("e4c510fe-d776-4a77-bbe5-2067155947b9");
        padOrderRespDTO2.setActualGuestsNo(0);
        padOrderRespDTO2.setAreaName("areaName");
        padOrderRespDTO2.setDiningTableGuid("diningTableGuid");
        padOrderRespDTO2.setTableCode("tableCode");
        padOrderRespDTO2.setOrderState(0);
        padOrderRespDTO2.setDenialReason("denialReason");
        padOrderRespDTO2.setStoreGuid("storeGuid");
        padOrderRespDTO2.setRemark("remark");
        padOrderRespDTO2.setOperationGuid("openId");
        padOrderRespDTO2.setOperationName("nickName");
        padOrderRespDTO2.setCombineOrderGuid("combineOrderGuid");
        padOrderRespDTO2.setOrderSource(0);
        verify(mockTradeClientService).updateById(padOrderRespDTO2);
        verify(mockBizMsgFeignClient).msg(BusinessMessageDTO.builder()
                .messageGuid("messageGuid")
                .subject("name")
                .content("content")
                .messageType(0)
                .detailMessageType(0)
                .platform("desc")
                .storeGuid("storeGuid")
                .storeName("name")
                .state("state")
                .createTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .pushTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .messageTypeStr("messageTypeStr")
                .build());
    }

    @Test
    public void testOperationMerchantOrder_OrderItemServiceGetItemListByMerchantGuidReturnsNoItems() {
        // Setup
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setUserWxPublicOpenId("openId");
        dineInItemDTO.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO.setItemGuid("itemGuid");
        dineInItemDTO.setItemType(0);
        dineInItemDTO.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO.setItemTypeName("itemTypeName");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO = new SubDineInItemDTO();
        subDineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO));
        dineInItemDTO.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO));
        final ItemAttrDTO itemAttrDTO = new ItemAttrDTO();
        itemAttrDTO.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO.setNum(0);
        dineInItemDTO.setItemAttrDTOS(Arrays.asList(itemAttrDTO));
        final WxOperateReqDTO wxOperateReqDTO = new WxOperateReqDTO("guid", 0, "denialReason", "tableGuid", "tableName",
                0, "areaName", "remark", Arrays.asList(dineInItemDTO));
        final WxStoreMerchantOperationDTO expectedResult = WxStoreMerchantOperationDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .storeGuid("storeGuid")
                .diningTableGuid("diningTableGuid")
                .tableCode("tableCode")
                .areaName("areaName")
                .storeName("storeName")
                .brandName("brandName")
                .estimateItemRespDTO(new EstimateItemRespDTO())
                .errorMsg("errorMsg")
                .build();

        // Configure WxStoreDineInOrderClientService.updateRemark(...).
        final CreateDineInOrderReqDTO createDineInOrderReqDTO = new CreateDineInOrderReqDTO();
        createDineInOrderReqDTO.setDeviceType(0);
        createDineInOrderReqDTO.setDeviceId("openId");
        createDineInOrderReqDTO.setEnterpriseGuid("enterpriseGuid");
        createDineInOrderReqDTO.setEnterpriseName("enterpriseName");
        createDineInOrderReqDTO.setStoreGuid("storeGuid");
        createDineInOrderReqDTO.setStoreName("name");
        createDineInOrderReqDTO.setUserGuid("openId");
        createDineInOrderReqDTO.setUserName("nickName");
        createDineInOrderReqDTO.setGuid("e4c510fe-d776-4a77-bbe5-2067155947b9");
        createDineInOrderReqDTO.setUserWxPublicOpenId("openId");
        createDineInOrderReqDTO.setRemark("remark");
        createDineInOrderReqDTO.setDiningTableGuid("diningTableGuid");
        createDineInOrderReqDTO.setDiningTableName("tableCode");
        createDineInOrderReqDTO.setAreaName("areaName");
        createDineInOrderReqDTO.setGuestCount(0);
        createDineInOrderReqDTO.setPrint(0);
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setUserWxPublicOpenId("openId");
        dineInItemDTO1.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO1.setItemGuid("itemGuid");
        dineInItemDTO1.setItemType(0);
        dineInItemDTO1.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO1.setItemTypeName("itemTypeName");
        dineInItemDTO1.setPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO1.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO1 = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO1 = new SubDineInItemDTO();
        subDineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO1.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO1.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO1));
        dineInItemDTO1.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO1));
        final ItemAttrDTO itemAttrDTO1 = new ItemAttrDTO();
        itemAttrDTO1.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO1.setNum(0);
        dineInItemDTO1.setItemAttrDTOS(Arrays.asList(itemAttrDTO1));
        createDineInOrderReqDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO1));
        createDineInOrderReqDTO.setEstimateType(0);
        when(mockWxStoreDineInOrderClientService.updateRemark(createDineInOrderReqDTO)).thenReturn(false);

        // Configure WxStoreTableClientService.tryOpen(...).
        final OpenTableDTO openTableDTO = new OpenTableDTO();
        openTableDTO.setDeviceType(0);
        openTableDTO.setDeviceId("openId");
        openTableDTO.setEnterpriseGuid("enterpriseGuid");
        openTableDTO.setEnterpriseName("enterpriseName");
        openTableDTO.setStoreGuid("storeGuid");
        openTableDTO.setStoreName("name");
        openTableDTO.setUserGuid("openId");
        openTableDTO.setUserName("nickName");
        openTableDTO.setTableGuid("diningTableGuid");
        openTableDTO.setTableCode("tableCode");
        openTableDTO.setAreaName("areaName");
        openTableDTO.setActualGuestsNo(0);
        when(mockWxStoreTableClientService.tryOpen(openTableDTO)).thenReturn("e4c510fe-d776-4a77-bbe5-2067155947b9");

        when(mockOrderItemService.getItemListByMerchantGuid("guid")).thenReturn(Collections.emptyList());

        // Run the test
        final WxStoreMerchantOperationDTO result = wxStoreMerchantOrderImplUnderTest.operationMerchantOrder(
                wxOperateReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockOrderItemService).setTableStaffInfo("e4c510fe-d776-4a77-bbe5-2067155947b9", "diningTableGuid");
    }

    @Test
    public void testOperationMerchantOrder_WebsocketUtilsReturnsNoItems() {
        // Setup
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setUserWxPublicOpenId("openId");
        dineInItemDTO.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO.setItemGuid("itemGuid");
        dineInItemDTO.setItemType(0);
        dineInItemDTO.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO.setItemTypeName("itemTypeName");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO = new SubDineInItemDTO();
        subDineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO));
        dineInItemDTO.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO));
        final ItemAttrDTO itemAttrDTO = new ItemAttrDTO();
        itemAttrDTO.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO.setNum(0);
        dineInItemDTO.setItemAttrDTOS(Arrays.asList(itemAttrDTO));
        final WxOperateReqDTO wxOperateReqDTO = new WxOperateReqDTO("guid", 0, "denialReason", "tableGuid", "tableName",
                0, "areaName", "remark", Arrays.asList(dineInItemDTO));
        final WxStoreMerchantOperationDTO expectedResult = WxStoreMerchantOperationDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .storeGuid("storeGuid")
                .diningTableGuid("diningTableGuid")
                .tableCode("tableCode")
                .areaName("areaName")
                .storeName("storeName")
                .brandName("brandName")
                .estimateItemRespDTO(new EstimateItemRespDTO())
                .errorMsg("errorMsg")
                .build();

        // Configure WxStoreDineInOrderClientService.updateRemark(...).
        final CreateDineInOrderReqDTO createDineInOrderReqDTO = new CreateDineInOrderReqDTO();
        createDineInOrderReqDTO.setDeviceType(0);
        createDineInOrderReqDTO.setDeviceId("openId");
        createDineInOrderReqDTO.setEnterpriseGuid("enterpriseGuid");
        createDineInOrderReqDTO.setEnterpriseName("enterpriseName");
        createDineInOrderReqDTO.setStoreGuid("storeGuid");
        createDineInOrderReqDTO.setStoreName("name");
        createDineInOrderReqDTO.setUserGuid("openId");
        createDineInOrderReqDTO.setUserName("nickName");
        createDineInOrderReqDTO.setGuid("e4c510fe-d776-4a77-bbe5-2067155947b9");
        createDineInOrderReqDTO.setUserWxPublicOpenId("openId");
        createDineInOrderReqDTO.setRemark("remark");
        createDineInOrderReqDTO.setDiningTableGuid("diningTableGuid");
        createDineInOrderReqDTO.setDiningTableName("tableCode");
        createDineInOrderReqDTO.setAreaName("areaName");
        createDineInOrderReqDTO.setGuestCount(0);
        createDineInOrderReqDTO.setPrint(0);
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setUserWxPublicOpenId("openId");
        dineInItemDTO1.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO1.setItemGuid("itemGuid");
        dineInItemDTO1.setItemType(0);
        dineInItemDTO1.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO1.setItemTypeName("itemTypeName");
        dineInItemDTO1.setPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO1.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO1 = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO1 = new SubDineInItemDTO();
        subDineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO1.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO1.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO1));
        dineInItemDTO1.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO1));
        final ItemAttrDTO itemAttrDTO1 = new ItemAttrDTO();
        itemAttrDTO1.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO1.setNum(0);
        dineInItemDTO1.setItemAttrDTOS(Arrays.asList(itemAttrDTO1));
        createDineInOrderReqDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO1));
        createDineInOrderReqDTO.setEstimateType(0);
        when(mockWxStoreDineInOrderClientService.updateRemark(createDineInOrderReqDTO)).thenReturn(false);

        // Configure WxStoreDineInOrderClientService.batchAddItems(...).
        final EstimateItemRespDTO estimateItemRespDTO = new EstimateItemRespDTO();
        estimateItemRespDTO.setResult(false);
        estimateItemRespDTO.setEstimate(false);
        estimateItemRespDTO.setErrorMsg("errorMsg");
        estimateItemRespDTO.setEstimateInfo("estimateInfo");
        estimateItemRespDTO.setEstimateSkuGuids(Arrays.asList("value"));
        final CreateDineInOrderReqDTO createDineInOrderReqDTO1 = new CreateDineInOrderReqDTO();
        createDineInOrderReqDTO1.setDeviceType(0);
        createDineInOrderReqDTO1.setDeviceId("openId");
        createDineInOrderReqDTO1.setEnterpriseGuid("enterpriseGuid");
        createDineInOrderReqDTO1.setEnterpriseName("enterpriseName");
        createDineInOrderReqDTO1.setStoreGuid("storeGuid");
        createDineInOrderReqDTO1.setStoreName("name");
        createDineInOrderReqDTO1.setUserGuid("openId");
        createDineInOrderReqDTO1.setUserName("nickName");
        createDineInOrderReqDTO1.setGuid("e4c510fe-d776-4a77-bbe5-2067155947b9");
        createDineInOrderReqDTO1.setUserWxPublicOpenId("openId");
        createDineInOrderReqDTO1.setRemark("remark");
        createDineInOrderReqDTO1.setDiningTableGuid("diningTableGuid");
        createDineInOrderReqDTO1.setDiningTableName("tableCode");
        createDineInOrderReqDTO1.setAreaName("areaName");
        createDineInOrderReqDTO1.setGuestCount(0);
        createDineInOrderReqDTO1.setPrint(0);
        final DineInItemDTO dineInItemDTO2 = new DineInItemDTO();
        dineInItemDTO2.setUserWxPublicOpenId("openId");
        dineInItemDTO2.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO2.setItemGuid("itemGuid");
        dineInItemDTO2.setItemType(0);
        dineInItemDTO2.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO2.setItemTypeName("itemTypeName");
        dineInItemDTO2.setPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO2.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO2 = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO2 = new SubDineInItemDTO();
        subDineInItemDTO2.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO2.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO2.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO2));
        dineInItemDTO2.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO2));
        final ItemAttrDTO itemAttrDTO2 = new ItemAttrDTO();
        itemAttrDTO2.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO2.setNum(0);
        dineInItemDTO2.setItemAttrDTOS(Arrays.asList(itemAttrDTO2));
        createDineInOrderReqDTO1.setDineInItemDTOS(Arrays.asList(dineInItemDTO2));
        createDineInOrderReqDTO1.setEstimateType(0);
        when(mockWxStoreDineInOrderClientService.batchAddItems(createDineInOrderReqDTO1))
                .thenReturn(estimateItemRespDTO);

        when(mockRedisUtils.generatdDTOGuid(BusinessMessageDTO.class)).thenReturn("messageGuid");

        // Configure WxStoreTableClientService.tryOpen(...).
        final OpenTableDTO openTableDTO = new OpenTableDTO();
        openTableDTO.setDeviceType(0);
        openTableDTO.setDeviceId("openId");
        openTableDTO.setEnterpriseGuid("enterpriseGuid");
        openTableDTO.setEnterpriseName("enterpriseName");
        openTableDTO.setStoreGuid("storeGuid");
        openTableDTO.setStoreName("name");
        openTableDTO.setUserGuid("openId");
        openTableDTO.setUserName("nickName");
        openTableDTO.setTableGuid("diningTableGuid");
        openTableDTO.setTableCode("tableCode");
        openTableDTO.setAreaName("areaName");
        openTableDTO.setActualGuestsNo(0);
        when(mockWxStoreTableClientService.tryOpen(openTableDTO)).thenReturn("e4c510fe-d776-4a77-bbe5-2067155947b9");

        // Configure OrderItemService.getItemListByMerchantGuid(...).
        final DineInItemDTO dineInItemDTO3 = new DineInItemDTO();
        dineInItemDTO3.setUserWxPublicOpenId("openId");
        dineInItemDTO3.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO3.setItemGuid("itemGuid");
        dineInItemDTO3.setItemType(0);
        dineInItemDTO3.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO3.setItemTypeName("itemTypeName");
        dineInItemDTO3.setPrice(new BigDecimal("0.00"));
        dineInItemDTO3.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO3.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO3.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO3 = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO3 = new SubDineInItemDTO();
        subDineInItemDTO3.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO3.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO3.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO3));
        dineInItemDTO3.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO3));
        final ItemAttrDTO itemAttrDTO3 = new ItemAttrDTO();
        itemAttrDTO3.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO3.setNum(0);
        dineInItemDTO3.setItemAttrDTOS(Arrays.asList(itemAttrDTO3));
        final List<DineInItemDTO> dineInItemDTOS = Arrays.asList(dineInItemDTO3);
        when(mockOrderItemService.getItemListByMerchantGuid("guid")).thenReturn(dineInItemDTOS);

        // Configure WxStoreMerchantOrderMapstruct.getWxStoreMerchantOrder(...).
        final WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        wxStoreMerchantOrderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setOpenId("openId");
        wxStoreMerchantOrderDTO.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDTO.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDTO.setOrderState(0);
        wxStoreMerchantOrderDTO.setTradeMode(0);
        wxStoreMerchantOrderDTO.setTradeModeName("desc");
        wxStoreMerchantOrderDTO.setMsgSource(0);
        wxStoreMerchantOrderDTO.setMsgSourceName("desc");
        final DineInItemDTO dineInItemDTO4 = new DineInItemDTO();
        dineInItemDTO4.setUserWxPublicOpenId("openId");
        dineInItemDTO4.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO4.setItemGuid("itemGuid");
        dineInItemDTO4.setItemType(0);
        dineInItemDTO4.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO4.setItemTypeName("itemTypeName");
        dineInItemDTO4.setPrice(new BigDecimal("0.00"));
        dineInItemDTO4.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO4.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO4.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO4 = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO4 = new SubDineInItemDTO();
        subDineInItemDTO4.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO4.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO4.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO4));
        dineInItemDTO4.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO4));
        final ItemAttrDTO itemAttrDTO4 = new ItemAttrDTO();
        itemAttrDTO4.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO4.setNum(0);
        dineInItemDTO4.setItemAttrDTOS(Arrays.asList(itemAttrDTO4));
        wxStoreMerchantOrderDTO.setDineInItemDTOList(Arrays.asList(dineInItemDTO4));
        wxStoreMerchantOrderDTO.setOrderNo("guid");
        wxStoreMerchantOrderDTO.setOperationSource(0);
        wxStoreMerchantOrderDTO.setOperationSourceName("desc");
        wxStoreMerchantOrderDTO.setOrderSource(0);
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        wxStoreMerchantOrderDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDO.setTotalPrice(new BigDecimal("0.00"));
        wxStoreMerchantOrderDO.setOpenId("openId");
        wxStoreMerchantOrderDO.setNickName("nickName");
        wxStoreMerchantOrderDO.setActualGuestsNo(0);
        wxStoreMerchantOrderDO.setTradeMode(0);
        wxStoreMerchantOrderDO.setOrderState(0);
        wxStoreMerchantOrderDO.setDenialReason("denialReason");
        wxStoreMerchantOrderDO.setOrderGuid("guid");
        wxStoreMerchantOrderDO.setRemark("remark");
        wxStoreMerchantOrderDO.setAreaGuid("areaGuid");
        wxStoreMerchantOrderDO.setAreaName("areaName");
        wxStoreMerchantOrderDO.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDO.setStoreName("name");
        wxStoreMerchantOrderDO.setBrandName("brandName");
        wxStoreMerchantOrderDO.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDO.setTableCode("tableCode");
        wxStoreMerchantOrderDO.setOperationGuid("openId");
        wxStoreMerchantOrderDO.setOperationSource(0);
        wxStoreMerchantOrderDO.setOperationName("nickName");
        wxStoreMerchantOrderDO.setOrderRecordGuid("58d0dfce-a97a-4608-b8a7-f7f4f68b843b");
        wxStoreMerchantOrderDO.setCombine("mainOrderGuid");
        wxStoreMerchantOrderDO.setOrderSource(0);
        when(mockWxStoreMerchantOrderMapstruct.getWxStoreMerchantOrder(wxStoreMerchantOrderDO))
                .thenReturn(wxStoreMerchantOrderDTO);

        // Configure WxStoreSessionDetailsService.getStoreDetail(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("13b15130-a1a8-4a4f-8eeb-d1af12c961a2");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        when(mockWxStoreSessionDetailsService.getStoreDetail("storeGuid")).thenReturn(storeDTO);

        when(mockWebsocketUtils.getOpenIdsByTableGuid("diningTableGuid")).thenReturn(Collections.emptySet());

        // Configure WxOrderRecordService.getById(...).
        final WxOrderRecordDO wxOrderRecordDO = new WxOrderRecordDO(0L, "58d0dfce-a97a-4608-b8a7-f7f4f68b843b",
                "orderGuid", "merchantGuid", "guid", "brandGuid", "brandName", "logUrl", "storeGuid", "storeName",
                "areaGuid", "areaName", "tableGuid", "tableCode", 0, 0, "orderStateName", new BigDecimal("0.00"),
                new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName", "orderHolderNo",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0);
        when(mockWxOrderRecordService.getById("58d0dfce-a97a-4608-b8a7-f7f4f68b843b")).thenReturn(wxOrderRecordDO);

        // Configure WxUserRecordService.getOne(...).
        final WxUserRecordDO wxUserRecordDO = new WxUserRecordDO(0L, "guid", "openId", "nickName", "headImgUrl", 0,
                "country", "province", "city", "phone", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid", 0);
        when(mockWxUserRecordService.getOne(any(LambdaQueryWrapper.class), eq(false))).thenReturn(wxUserRecordDO);

        when(mockOrderItemService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));
        when(mockWxOrderRecordService.updateById(
                new WxOrderRecordDO(0L, "58d0dfce-a97a-4608-b8a7-f7f4f68b843b", "orderGuid", "merchantGuid", "guid",
                        "brandGuid", "brandName", "logUrl", "storeGuid", "storeName", "areaGuid", "areaName",
                        "tableGuid", "tableCode", 0, 0, "orderStateName", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName", "orderHolderNo",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0)))
                .thenReturn(false);

        // Configure WxStoreTableClientService.closeTable(...).
        final CancelOrderReqDTO cancelOrderReqDTO = new CancelOrderReqDTO();
        cancelOrderReqDTO.setDeviceType(0);
        cancelOrderReqDTO.setDeviceId("openId");
        cancelOrderReqDTO.setEnterpriseGuid("enterpriseGuid");
        cancelOrderReqDTO.setEnterpriseName("enterpriseName");
        cancelOrderReqDTO.setStoreGuid("storeGuid");
        cancelOrderReqDTO.setStoreName("name");
        cancelOrderReqDTO.setUserGuid("openId");
        cancelOrderReqDTO.setUserName("nickName");
        cancelOrderReqDTO.setTableGuid("tableGuid");
        cancelOrderReqDTO.setOrderGuid("guid");
        when(mockTableClientService.closeTable(cancelOrderReqDTO)).thenReturn(false);

        when(mockWxStoreMerchantOrderMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(0);
        when(mockWxStoreTableClientService.getOrderGuid("diningTableGuid")).thenReturn("orderGuid");

        // Configure UserMemberSessionUtils.getUserMemberSession(...).
        final UserMemberSessionDTO userMemberSessionDTO = new UserMemberSessionDTO("storeGuid", "openId", "userGuid",
                false, "nickName", "memberInfoGuid", "cardGuid", "memberInfoCardGuid", "systemManagementGuid", 0, false,
                "volumeCode", false, new BigDecimal("0.00"), new BigDecimal("0.00"), "operSubjectGuid");
        when(mockUserMemberSessionUtils.getUserMemberSession("openId")).thenReturn(userMemberSessionDTO);

        // Configure HsaBaseClientService.getMemberInfo(...).
        final ResponseMemberInfo responseMemberInfo = new ResponseMemberInfo();
        responseMemberInfo.setQrcode("qrcode");
        responseMemberInfo.setMemberInfoGuid("memberInfoGuid");
        responseMemberInfo.setOperSubjectGuid("operSubjectGuid");
        responseMemberInfo.setEnterpriseGuid("enterpriseGuid");
        responseMemberInfo.setPhoneNum("openId");
        final ResponseModel<ResponseMemberInfo> responseMemberInfoResponseModel = new ResponseModel<>(
                responseMemberInfo);
        final RequestQueryMemberInfo requestQueryMemberInfo = new RequestQueryMemberInfo();
        requestQueryMemberInfo.setMemberInfoGuid("memberInfoGuid");
        requestQueryMemberInfo.setUnionId("unionId");
        requestQueryMemberInfo.setOpenId("openId");
        requestQueryMemberInfo.setPhoneNum("phoneNum");
        requestQueryMemberInfo.setOperSubjectGuid("operSubjectGuid");
        when(mockHsaBaseClientService.getMemberInfo(requestQueryMemberInfo))
                .thenReturn(responseMemberInfoResponseModel);

        when(mockRedisUtils.setNx("key", "1", 18000L)).thenReturn(false);

        // Configure WxStoreMerchantOrderMapper.selectOne(...).
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO1 = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO1.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        wxStoreMerchantOrderDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDO1.setTotalPrice(new BigDecimal("0.00"));
        wxStoreMerchantOrderDO1.setOpenId("openId");
        wxStoreMerchantOrderDO1.setNickName("nickName");
        wxStoreMerchantOrderDO1.setActualGuestsNo(0);
        wxStoreMerchantOrderDO1.setTradeMode(0);
        wxStoreMerchantOrderDO1.setOrderState(0);
        wxStoreMerchantOrderDO1.setDenialReason("denialReason");
        wxStoreMerchantOrderDO1.setOrderGuid("guid");
        wxStoreMerchantOrderDO1.setRemark("remark");
        wxStoreMerchantOrderDO1.setAreaGuid("areaGuid");
        wxStoreMerchantOrderDO1.setAreaName("areaName");
        wxStoreMerchantOrderDO1.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDO1.setStoreName("name");
        wxStoreMerchantOrderDO1.setBrandName("brandName");
        wxStoreMerchantOrderDO1.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDO1.setTableCode("tableCode");
        wxStoreMerchantOrderDO1.setOperationGuid("openId");
        wxStoreMerchantOrderDO1.setOperationSource(0);
        wxStoreMerchantOrderDO1.setOperationName("nickName");
        wxStoreMerchantOrderDO1.setOrderRecordGuid("58d0dfce-a97a-4608-b8a7-f7f4f68b843b");
        wxStoreMerchantOrderDO1.setCombine("mainOrderGuid");
        wxStoreMerchantOrderDO1.setOrderSource(0);
        when(mockWxStoreMerchantOrderMapper.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(wxStoreMerchantOrderDO1);

        // Configure WxStoreMerchantOrderMapper.updateById(...).
        final WxStoreMerchantOrderDO entity = new WxStoreMerchantOrderDO();
        entity.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        entity.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setTotalPrice(new BigDecimal("0.00"));
        entity.setOpenId("openId");
        entity.setNickName("nickName");
        entity.setActualGuestsNo(0);
        entity.setTradeMode(0);
        entity.setOrderState(0);
        entity.setDenialReason("denialReason");
        entity.setOrderGuid("guid");
        entity.setRemark("remark");
        entity.setAreaGuid("areaGuid");
        entity.setAreaName("areaName");
        entity.setStoreGuid("storeGuid");
        entity.setStoreName("name");
        entity.setBrandName("brandName");
        entity.setDiningTableGuid("diningTableGuid");
        entity.setTableCode("tableCode");
        entity.setOperationGuid("openId");
        entity.setOperationSource(0);
        entity.setOperationName("nickName");
        entity.setOrderRecordGuid("58d0dfce-a97a-4608-b8a7-f7f4f68b843b");
        entity.setCombine("mainOrderGuid");
        entity.setOrderSource(0);
        when(mockWxStoreMerchantOrderMapper.updateById(entity)).thenReturn(0);

        // Configure WxOrderRecordService.getOne(...).
        final WxOrderRecordDO wxOrderRecordDO1 = new WxOrderRecordDO(0L, "58d0dfce-a97a-4608-b8a7-f7f4f68b843b",
                "orderGuid", "merchantGuid", "guid", "brandGuid", "brandName", "logUrl", "storeGuid", "storeName",
                "areaGuid", "areaName", "tableGuid", "tableCode", 0, 0, "orderStateName", new BigDecimal("0.00"),
                new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName", "orderHolderNo",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0);
        when(mockWxOrderRecordService.getOne(any(LambdaQueryWrapper.class))).thenReturn(wxOrderRecordDO1);

        // Configure OrderItemService.getOne(...).
        final WxOrderItemDO wxOrderItemDO = new WxOrderItemDO();
        wxOrderItemDO.setMerchantGuid("merchantGuid");
        wxOrderItemDO.setOrderRecordGuid("orderRecordGuid");
        wxOrderItemDO.setItemGuid("itemGuid");
        wxOrderItemDO.setItemName("itemName");
        wxOrderItemDO.setMemberPrice(new BigDecimal("0.00"));
        wxOrderItemDO.setOriginalPrice(new BigDecimal("0.00"));
        wxOrderItemDO.setRemark("remark");
        when(mockOrderItemService.getOne(any(LambdaQueryWrapper.class))).thenReturn(wxOrderItemDO);

        // Run the test
        final WxStoreMerchantOperationDTO result = wxStoreMerchantOrderImplUnderTest.operationMerchantOrder(
                wxOperateReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockBizMsgFeignClient).msg(BusinessMessageDTO.builder()
                .messageGuid("messageGuid")
                .subject("name")
                .content("content")
                .messageType(0)
                .detailMessageType(0)
                .platform("desc")
                .storeGuid("storeGuid")
                .storeName("name")
                .state("state")
                .createTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .pushTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .messageTypeStr("messageTypeStr")
                .build());
        verify(mockOrderItemService).setTableStaffInfo("e4c510fe-d776-4a77-bbe5-2067155947b9", "diningTableGuid");
        verify(mockRedisUtils).delete("key");
        verify(mockWxStoreDineInOrderClientService).updateOrderRemarkById("orderGuid", "remark");

        // Confirm WxOrderItemMapper.updateById(...).
        final WxOrderItemDO entity1 = new WxOrderItemDO();
        entity1.setMerchantGuid("merchantGuid");
        entity1.setOrderRecordGuid("orderRecordGuid");
        entity1.setItemGuid("itemGuid");
        entity1.setItemName("itemName");
        entity1.setMemberPrice(new BigDecimal("0.00"));
        entity1.setOriginalPrice(new BigDecimal("0.00"));
        entity1.setRemark("remark");
        verify(mockWxOrderItemMapper).updateById(entity1);
        verify(mockWxStoreDineInOrderClientService).updateOrderItemRemarkById("orderGuid", "itemGuid", "remark");
    }

    @Test
    public void testOperationMerchantOrder_WxOrderRecordServiceGetByIdReturnsNull() {
        // Setup
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setUserWxPublicOpenId("openId");
        dineInItemDTO.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO.setItemGuid("itemGuid");
        dineInItemDTO.setItemType(0);
        dineInItemDTO.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO.setItemTypeName("itemTypeName");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO = new SubDineInItemDTO();
        subDineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO));
        dineInItemDTO.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO));
        final ItemAttrDTO itemAttrDTO = new ItemAttrDTO();
        itemAttrDTO.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO.setNum(0);
        dineInItemDTO.setItemAttrDTOS(Arrays.asList(itemAttrDTO));
        final WxOperateReqDTO wxOperateReqDTO = new WxOperateReqDTO("guid", 0, "denialReason", "tableGuid", "tableName",
                0, "areaName", "remark", Arrays.asList(dineInItemDTO));
        final WxStoreMerchantOperationDTO expectedResult = WxStoreMerchantOperationDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .storeGuid("storeGuid")
                .diningTableGuid("diningTableGuid")
                .tableCode("tableCode")
                .areaName("areaName")
                .storeName("storeName")
                .brandName("brandName")
                .estimateItemRespDTO(new EstimateItemRespDTO())
                .errorMsg("errorMsg")
                .build();

        // Configure WxStoreDineInOrderClientService.updateRemark(...).
        final CreateDineInOrderReqDTO createDineInOrderReqDTO = new CreateDineInOrderReqDTO();
        createDineInOrderReqDTO.setDeviceType(0);
        createDineInOrderReqDTO.setDeviceId("openId");
        createDineInOrderReqDTO.setEnterpriseGuid("enterpriseGuid");
        createDineInOrderReqDTO.setEnterpriseName("enterpriseName");
        createDineInOrderReqDTO.setStoreGuid("storeGuid");
        createDineInOrderReqDTO.setStoreName("name");
        createDineInOrderReqDTO.setUserGuid("openId");
        createDineInOrderReqDTO.setUserName("nickName");
        createDineInOrderReqDTO.setGuid("e4c510fe-d776-4a77-bbe5-2067155947b9");
        createDineInOrderReqDTO.setUserWxPublicOpenId("openId");
        createDineInOrderReqDTO.setRemark("remark");
        createDineInOrderReqDTO.setDiningTableGuid("diningTableGuid");
        createDineInOrderReqDTO.setDiningTableName("tableCode");
        createDineInOrderReqDTO.setAreaName("areaName");
        createDineInOrderReqDTO.setGuestCount(0);
        createDineInOrderReqDTO.setPrint(0);
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setUserWxPublicOpenId("openId");
        dineInItemDTO1.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO1.setItemGuid("itemGuid");
        dineInItemDTO1.setItemType(0);
        dineInItemDTO1.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO1.setItemTypeName("itemTypeName");
        dineInItemDTO1.setPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO1.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO1 = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO1 = new SubDineInItemDTO();
        subDineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO1.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO1.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO1));
        dineInItemDTO1.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO1));
        final ItemAttrDTO itemAttrDTO1 = new ItemAttrDTO();
        itemAttrDTO1.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO1.setNum(0);
        dineInItemDTO1.setItemAttrDTOS(Arrays.asList(itemAttrDTO1));
        createDineInOrderReqDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO1));
        createDineInOrderReqDTO.setEstimateType(0);
        when(mockWxStoreDineInOrderClientService.updateRemark(createDineInOrderReqDTO)).thenReturn(false);

        // Configure WxStoreDineInOrderClientService.batchAddItems(...).
        final EstimateItemRespDTO estimateItemRespDTO = new EstimateItemRespDTO();
        estimateItemRespDTO.setResult(false);
        estimateItemRespDTO.setEstimate(false);
        estimateItemRespDTO.setErrorMsg("errorMsg");
        estimateItemRespDTO.setEstimateInfo("estimateInfo");
        estimateItemRespDTO.setEstimateSkuGuids(Arrays.asList("value"));
        final CreateDineInOrderReqDTO createDineInOrderReqDTO1 = new CreateDineInOrderReqDTO();
        createDineInOrderReqDTO1.setDeviceType(0);
        createDineInOrderReqDTO1.setDeviceId("openId");
        createDineInOrderReqDTO1.setEnterpriseGuid("enterpriseGuid");
        createDineInOrderReqDTO1.setEnterpriseName("enterpriseName");
        createDineInOrderReqDTO1.setStoreGuid("storeGuid");
        createDineInOrderReqDTO1.setStoreName("name");
        createDineInOrderReqDTO1.setUserGuid("openId");
        createDineInOrderReqDTO1.setUserName("nickName");
        createDineInOrderReqDTO1.setGuid("e4c510fe-d776-4a77-bbe5-2067155947b9");
        createDineInOrderReqDTO1.setUserWxPublicOpenId("openId");
        createDineInOrderReqDTO1.setRemark("remark");
        createDineInOrderReqDTO1.setDiningTableGuid("diningTableGuid");
        createDineInOrderReqDTO1.setDiningTableName("tableCode");
        createDineInOrderReqDTO1.setAreaName("areaName");
        createDineInOrderReqDTO1.setGuestCount(0);
        createDineInOrderReqDTO1.setPrint(0);
        final DineInItemDTO dineInItemDTO2 = new DineInItemDTO();
        dineInItemDTO2.setUserWxPublicOpenId("openId");
        dineInItemDTO2.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO2.setItemGuid("itemGuid");
        dineInItemDTO2.setItemType(0);
        dineInItemDTO2.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO2.setItemTypeName("itemTypeName");
        dineInItemDTO2.setPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO2.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO2 = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO2 = new SubDineInItemDTO();
        subDineInItemDTO2.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO2.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO2.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO2));
        dineInItemDTO2.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO2));
        final ItemAttrDTO itemAttrDTO2 = new ItemAttrDTO();
        itemAttrDTO2.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO2.setNum(0);
        dineInItemDTO2.setItemAttrDTOS(Arrays.asList(itemAttrDTO2));
        createDineInOrderReqDTO1.setDineInItemDTOS(Arrays.asList(dineInItemDTO2));
        createDineInOrderReqDTO1.setEstimateType(0);
        when(mockWxStoreDineInOrderClientService.batchAddItems(createDineInOrderReqDTO1))
                .thenReturn(estimateItemRespDTO);

        when(mockRedisUtils.generatdDTOGuid(BusinessMessageDTO.class)).thenReturn("messageGuid");

        // Configure WxStoreTableClientService.tryOpen(...).
        final OpenTableDTO openTableDTO = new OpenTableDTO();
        openTableDTO.setDeviceType(0);
        openTableDTO.setDeviceId("openId");
        openTableDTO.setEnterpriseGuid("enterpriseGuid");
        openTableDTO.setEnterpriseName("enterpriseName");
        openTableDTO.setStoreGuid("storeGuid");
        openTableDTO.setStoreName("name");
        openTableDTO.setUserGuid("openId");
        openTableDTO.setUserName("nickName");
        openTableDTO.setTableGuid("diningTableGuid");
        openTableDTO.setTableCode("tableCode");
        openTableDTO.setAreaName("areaName");
        openTableDTO.setActualGuestsNo(0);
        when(mockWxStoreTableClientService.tryOpen(openTableDTO)).thenReturn("e4c510fe-d776-4a77-bbe5-2067155947b9");

        // Configure OrderItemService.getItemListByMerchantGuid(...).
        final DineInItemDTO dineInItemDTO3 = new DineInItemDTO();
        dineInItemDTO3.setUserWxPublicOpenId("openId");
        dineInItemDTO3.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO3.setItemGuid("itemGuid");
        dineInItemDTO3.setItemType(0);
        dineInItemDTO3.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO3.setItemTypeName("itemTypeName");
        dineInItemDTO3.setPrice(new BigDecimal("0.00"));
        dineInItemDTO3.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO3.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO3.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO3 = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO3 = new SubDineInItemDTO();
        subDineInItemDTO3.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO3.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO3.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO3));
        dineInItemDTO3.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO3));
        final ItemAttrDTO itemAttrDTO3 = new ItemAttrDTO();
        itemAttrDTO3.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO3.setNum(0);
        dineInItemDTO3.setItemAttrDTOS(Arrays.asList(itemAttrDTO3));
        final List<DineInItemDTO> dineInItemDTOS = Arrays.asList(dineInItemDTO3);
        when(mockOrderItemService.getItemListByMerchantGuid("guid")).thenReturn(dineInItemDTOS);

        // Configure WxStoreMerchantOrderMapstruct.getWxStoreMerchantOrder(...).
        final WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        wxStoreMerchantOrderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setOpenId("openId");
        wxStoreMerchantOrderDTO.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDTO.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDTO.setOrderState(0);
        wxStoreMerchantOrderDTO.setTradeMode(0);
        wxStoreMerchantOrderDTO.setTradeModeName("desc");
        wxStoreMerchantOrderDTO.setMsgSource(0);
        wxStoreMerchantOrderDTO.setMsgSourceName("desc");
        final DineInItemDTO dineInItemDTO4 = new DineInItemDTO();
        dineInItemDTO4.setUserWxPublicOpenId("openId");
        dineInItemDTO4.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO4.setItemGuid("itemGuid");
        dineInItemDTO4.setItemType(0);
        dineInItemDTO4.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO4.setItemTypeName("itemTypeName");
        dineInItemDTO4.setPrice(new BigDecimal("0.00"));
        dineInItemDTO4.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO4.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO4.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO4 = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO4 = new SubDineInItemDTO();
        subDineInItemDTO4.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO4.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO4.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO4));
        dineInItemDTO4.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO4));
        final ItemAttrDTO itemAttrDTO4 = new ItemAttrDTO();
        itemAttrDTO4.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO4.setNum(0);
        dineInItemDTO4.setItemAttrDTOS(Arrays.asList(itemAttrDTO4));
        wxStoreMerchantOrderDTO.setDineInItemDTOList(Arrays.asList(dineInItemDTO4));
        wxStoreMerchantOrderDTO.setOrderNo("guid");
        wxStoreMerchantOrderDTO.setOperationSource(0);
        wxStoreMerchantOrderDTO.setOperationSourceName("desc");
        wxStoreMerchantOrderDTO.setOrderSource(0);
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        wxStoreMerchantOrderDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDO.setTotalPrice(new BigDecimal("0.00"));
        wxStoreMerchantOrderDO.setOpenId("openId");
        wxStoreMerchantOrderDO.setNickName("nickName");
        wxStoreMerchantOrderDO.setActualGuestsNo(0);
        wxStoreMerchantOrderDO.setTradeMode(0);
        wxStoreMerchantOrderDO.setOrderState(0);
        wxStoreMerchantOrderDO.setDenialReason("denialReason");
        wxStoreMerchantOrderDO.setOrderGuid("guid");
        wxStoreMerchantOrderDO.setRemark("remark");
        wxStoreMerchantOrderDO.setAreaGuid("areaGuid");
        wxStoreMerchantOrderDO.setAreaName("areaName");
        wxStoreMerchantOrderDO.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDO.setStoreName("name");
        wxStoreMerchantOrderDO.setBrandName("brandName");
        wxStoreMerchantOrderDO.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDO.setTableCode("tableCode");
        wxStoreMerchantOrderDO.setOperationGuid("openId");
        wxStoreMerchantOrderDO.setOperationSource(0);
        wxStoreMerchantOrderDO.setOperationName("nickName");
        wxStoreMerchantOrderDO.setOrderRecordGuid("58d0dfce-a97a-4608-b8a7-f7f4f68b843b");
        wxStoreMerchantOrderDO.setCombine("mainOrderGuid");
        wxStoreMerchantOrderDO.setOrderSource(0);
        when(mockWxStoreMerchantOrderMapstruct.getWxStoreMerchantOrder(wxStoreMerchantOrderDO))
                .thenReturn(wxStoreMerchantOrderDTO);

        // Configure WxStoreSessionDetailsService.getStoreDetail(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("13b15130-a1a8-4a4f-8eeb-d1af12c961a2");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        when(mockWxStoreSessionDetailsService.getStoreDetail("storeGuid")).thenReturn(storeDTO);

        when(mockWebsocketUtils.getOpenIdsByTableGuid("diningTableGuid"))
                .thenReturn(new HashSet<>(Arrays.asList("value")));
        when(mockWxOrderRecordService.getById("58d0dfce-a97a-4608-b8a7-f7f4f68b843b")).thenReturn(null);

        // Run the test
        final WxStoreMerchantOperationDTO result = wxStoreMerchantOrderImplUnderTest.operationMerchantOrder(
                wxOperateReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockBizMsgFeignClient).msg(BusinessMessageDTO.builder()
                .messageGuid("messageGuid")
                .subject("name")
                .content("content")
                .messageType(0)
                .detailMessageType(0)
                .platform("desc")
                .storeGuid("storeGuid")
                .storeName("name")
                .state("state")
                .createTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .pushTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .messageTypeStr("messageTypeStr")
                .build());
        verify(mockOrderItemService).setTableStaffInfo("e4c510fe-d776-4a77-bbe5-2067155947b9", "diningTableGuid");
        verify(mockWebsocketMessageHelper).sendOrderEmqMessage("diningTableGuid", Arrays.asList("value"));
        verify(mockRedisUtils).delete("key");
    }

    @Test
    public void testOperationMerchantOrder_UserMemberSessionUtilsReturnsNull() {
        // Setup
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setUserWxPublicOpenId("openId");
        dineInItemDTO.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO.setItemGuid("itemGuid");
        dineInItemDTO.setItemType(0);
        dineInItemDTO.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO.setItemTypeName("itemTypeName");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO = new SubDineInItemDTO();
        subDineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO));
        dineInItemDTO.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO));
        final ItemAttrDTO itemAttrDTO = new ItemAttrDTO();
        itemAttrDTO.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO.setNum(0);
        dineInItemDTO.setItemAttrDTOS(Arrays.asList(itemAttrDTO));
        final WxOperateReqDTO wxOperateReqDTO = new WxOperateReqDTO("guid", 0, "denialReason", "tableGuid", "tableName",
                0, "areaName", "remark", Arrays.asList(dineInItemDTO));
        final WxStoreMerchantOperationDTO expectedResult = WxStoreMerchantOperationDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .storeGuid("storeGuid")
                .diningTableGuid("diningTableGuid")
                .tableCode("tableCode")
                .areaName("areaName")
                .storeName("storeName")
                .brandName("brandName")
                .estimateItemRespDTO(new EstimateItemRespDTO())
                .errorMsg("errorMsg")
                .build();

        // Configure WxStoreDineInOrderClientService.updateRemark(...).
        final CreateDineInOrderReqDTO createDineInOrderReqDTO = new CreateDineInOrderReqDTO();
        createDineInOrderReqDTO.setDeviceType(0);
        createDineInOrderReqDTO.setDeviceId("openId");
        createDineInOrderReqDTO.setEnterpriseGuid("enterpriseGuid");
        createDineInOrderReqDTO.setEnterpriseName("enterpriseName");
        createDineInOrderReqDTO.setStoreGuid("storeGuid");
        createDineInOrderReqDTO.setStoreName("name");
        createDineInOrderReqDTO.setUserGuid("openId");
        createDineInOrderReqDTO.setUserName("nickName");
        createDineInOrderReqDTO.setGuid("e4c510fe-d776-4a77-bbe5-2067155947b9");
        createDineInOrderReqDTO.setUserWxPublicOpenId("openId");
        createDineInOrderReqDTO.setRemark("remark");
        createDineInOrderReqDTO.setDiningTableGuid("diningTableGuid");
        createDineInOrderReqDTO.setDiningTableName("tableCode");
        createDineInOrderReqDTO.setAreaName("areaName");
        createDineInOrderReqDTO.setGuestCount(0);
        createDineInOrderReqDTO.setPrint(0);
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setUserWxPublicOpenId("openId");
        dineInItemDTO1.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO1.setItemGuid("itemGuid");
        dineInItemDTO1.setItemType(0);
        dineInItemDTO1.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO1.setItemTypeName("itemTypeName");
        dineInItemDTO1.setPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO1.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO1 = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO1 = new SubDineInItemDTO();
        subDineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO1.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO1.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO1));
        dineInItemDTO1.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO1));
        final ItemAttrDTO itemAttrDTO1 = new ItemAttrDTO();
        itemAttrDTO1.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO1.setNum(0);
        dineInItemDTO1.setItemAttrDTOS(Arrays.asList(itemAttrDTO1));
        createDineInOrderReqDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO1));
        createDineInOrderReqDTO.setEstimateType(0);
        when(mockWxStoreDineInOrderClientService.updateRemark(createDineInOrderReqDTO)).thenReturn(false);

        // Configure WxStoreDineInOrderClientService.batchAddItems(...).
        final EstimateItemRespDTO estimateItemRespDTO = new EstimateItemRespDTO();
        estimateItemRespDTO.setResult(false);
        estimateItemRespDTO.setEstimate(false);
        estimateItemRespDTO.setErrorMsg("errorMsg");
        estimateItemRespDTO.setEstimateInfo("estimateInfo");
        estimateItemRespDTO.setEstimateSkuGuids(Arrays.asList("value"));
        final CreateDineInOrderReqDTO createDineInOrderReqDTO1 = new CreateDineInOrderReqDTO();
        createDineInOrderReqDTO1.setDeviceType(0);
        createDineInOrderReqDTO1.setDeviceId("openId");
        createDineInOrderReqDTO1.setEnterpriseGuid("enterpriseGuid");
        createDineInOrderReqDTO1.setEnterpriseName("enterpriseName");
        createDineInOrderReqDTO1.setStoreGuid("storeGuid");
        createDineInOrderReqDTO1.setStoreName("name");
        createDineInOrderReqDTO1.setUserGuid("openId");
        createDineInOrderReqDTO1.setUserName("nickName");
        createDineInOrderReqDTO1.setGuid("e4c510fe-d776-4a77-bbe5-2067155947b9");
        createDineInOrderReqDTO1.setUserWxPublicOpenId("openId");
        createDineInOrderReqDTO1.setRemark("remark");
        createDineInOrderReqDTO1.setDiningTableGuid("diningTableGuid");
        createDineInOrderReqDTO1.setDiningTableName("tableCode");
        createDineInOrderReqDTO1.setAreaName("areaName");
        createDineInOrderReqDTO1.setGuestCount(0);
        createDineInOrderReqDTO1.setPrint(0);
        final DineInItemDTO dineInItemDTO2 = new DineInItemDTO();
        dineInItemDTO2.setUserWxPublicOpenId("openId");
        dineInItemDTO2.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO2.setItemGuid("itemGuid");
        dineInItemDTO2.setItemType(0);
        dineInItemDTO2.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO2.setItemTypeName("itemTypeName");
        dineInItemDTO2.setPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO2.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO2 = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO2 = new SubDineInItemDTO();
        subDineInItemDTO2.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO2.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO2.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO2));
        dineInItemDTO2.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO2));
        final ItemAttrDTO itemAttrDTO2 = new ItemAttrDTO();
        itemAttrDTO2.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO2.setNum(0);
        dineInItemDTO2.setItemAttrDTOS(Arrays.asList(itemAttrDTO2));
        createDineInOrderReqDTO1.setDineInItemDTOS(Arrays.asList(dineInItemDTO2));
        createDineInOrderReqDTO1.setEstimateType(0);
        when(mockWxStoreDineInOrderClientService.batchAddItems(createDineInOrderReqDTO1))
                .thenReturn(estimateItemRespDTO);

        when(mockRedisUtils.generatdDTOGuid(BusinessMessageDTO.class)).thenReturn("messageGuid");

        // Configure WxStoreTableClientService.tryOpen(...).
        final OpenTableDTO openTableDTO = new OpenTableDTO();
        openTableDTO.setDeviceType(0);
        openTableDTO.setDeviceId("openId");
        openTableDTO.setEnterpriseGuid("enterpriseGuid");
        openTableDTO.setEnterpriseName("enterpriseName");
        openTableDTO.setStoreGuid("storeGuid");
        openTableDTO.setStoreName("name");
        openTableDTO.setUserGuid("openId");
        openTableDTO.setUserName("nickName");
        openTableDTO.setTableGuid("diningTableGuid");
        openTableDTO.setTableCode("tableCode");
        openTableDTO.setAreaName("areaName");
        openTableDTO.setActualGuestsNo(0);
        when(mockWxStoreTableClientService.tryOpen(openTableDTO)).thenReturn("e4c510fe-d776-4a77-bbe5-2067155947b9");

        // Configure OrderItemService.getItemListByMerchantGuid(...).
        final DineInItemDTO dineInItemDTO3 = new DineInItemDTO();
        dineInItemDTO3.setUserWxPublicOpenId("openId");
        dineInItemDTO3.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO3.setItemGuid("itemGuid");
        dineInItemDTO3.setItemType(0);
        dineInItemDTO3.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO3.setItemTypeName("itemTypeName");
        dineInItemDTO3.setPrice(new BigDecimal("0.00"));
        dineInItemDTO3.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO3.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO3.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO3 = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO3 = new SubDineInItemDTO();
        subDineInItemDTO3.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO3.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO3.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO3));
        dineInItemDTO3.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO3));
        final ItemAttrDTO itemAttrDTO3 = new ItemAttrDTO();
        itemAttrDTO3.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO3.setNum(0);
        dineInItemDTO3.setItemAttrDTOS(Arrays.asList(itemAttrDTO3));
        final List<DineInItemDTO> dineInItemDTOS = Arrays.asList(dineInItemDTO3);
        when(mockOrderItemService.getItemListByMerchantGuid("guid")).thenReturn(dineInItemDTOS);

        // Configure WxStoreMerchantOrderMapstruct.getWxStoreMerchantOrder(...).
        final WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        wxStoreMerchantOrderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setOpenId("openId");
        wxStoreMerchantOrderDTO.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDTO.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDTO.setOrderState(0);
        wxStoreMerchantOrderDTO.setTradeMode(0);
        wxStoreMerchantOrderDTO.setTradeModeName("desc");
        wxStoreMerchantOrderDTO.setMsgSource(0);
        wxStoreMerchantOrderDTO.setMsgSourceName("desc");
        final DineInItemDTO dineInItemDTO4 = new DineInItemDTO();
        dineInItemDTO4.setUserWxPublicOpenId("openId");
        dineInItemDTO4.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO4.setItemGuid("itemGuid");
        dineInItemDTO4.setItemType(0);
        dineInItemDTO4.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO4.setItemTypeName("itemTypeName");
        dineInItemDTO4.setPrice(new BigDecimal("0.00"));
        dineInItemDTO4.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO4.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO4.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO4 = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO4 = new SubDineInItemDTO();
        subDineInItemDTO4.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO4.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO4.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO4));
        dineInItemDTO4.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO4));
        final ItemAttrDTO itemAttrDTO4 = new ItemAttrDTO();
        itemAttrDTO4.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO4.setNum(0);
        dineInItemDTO4.setItemAttrDTOS(Arrays.asList(itemAttrDTO4));
        wxStoreMerchantOrderDTO.setDineInItemDTOList(Arrays.asList(dineInItemDTO4));
        wxStoreMerchantOrderDTO.setOrderNo("guid");
        wxStoreMerchantOrderDTO.setOperationSource(0);
        wxStoreMerchantOrderDTO.setOperationSourceName("desc");
        wxStoreMerchantOrderDTO.setOrderSource(0);
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        wxStoreMerchantOrderDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDO.setTotalPrice(new BigDecimal("0.00"));
        wxStoreMerchantOrderDO.setOpenId("openId");
        wxStoreMerchantOrderDO.setNickName("nickName");
        wxStoreMerchantOrderDO.setActualGuestsNo(0);
        wxStoreMerchantOrderDO.setTradeMode(0);
        wxStoreMerchantOrderDO.setOrderState(0);
        wxStoreMerchantOrderDO.setDenialReason("denialReason");
        wxStoreMerchantOrderDO.setOrderGuid("guid");
        wxStoreMerchantOrderDO.setRemark("remark");
        wxStoreMerchantOrderDO.setAreaGuid("areaGuid");
        wxStoreMerchantOrderDO.setAreaName("areaName");
        wxStoreMerchantOrderDO.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDO.setStoreName("name");
        wxStoreMerchantOrderDO.setBrandName("brandName");
        wxStoreMerchantOrderDO.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDO.setTableCode("tableCode");
        wxStoreMerchantOrderDO.setOperationGuid("openId");
        wxStoreMerchantOrderDO.setOperationSource(0);
        wxStoreMerchantOrderDO.setOperationName("nickName");
        wxStoreMerchantOrderDO.setOrderRecordGuid("58d0dfce-a97a-4608-b8a7-f7f4f68b843b");
        wxStoreMerchantOrderDO.setCombine("mainOrderGuid");
        wxStoreMerchantOrderDO.setOrderSource(0);
        when(mockWxStoreMerchantOrderMapstruct.getWxStoreMerchantOrder(wxStoreMerchantOrderDO))
                .thenReturn(wxStoreMerchantOrderDTO);

        // Configure WxStoreSessionDetailsService.getStoreDetail(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("13b15130-a1a8-4a4f-8eeb-d1af12c961a2");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        when(mockWxStoreSessionDetailsService.getStoreDetail("storeGuid")).thenReturn(storeDTO);

        when(mockWebsocketUtils.getOpenIdsByTableGuid("diningTableGuid"))
                .thenReturn(new HashSet<>(Arrays.asList("value")));
        when(mockWxOrderRecordService.updateById(
                new WxOrderRecordDO(0L, "58d0dfce-a97a-4608-b8a7-f7f4f68b843b", "orderGuid", "merchantGuid", "guid",
                        "brandGuid", "brandName", "logUrl", "storeGuid", "storeName", "areaGuid", "areaName",
                        "tableGuid", "tableCode", 0, 0, "orderStateName", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName", "orderHolderNo",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0)))
                .thenReturn(false);
        when(mockWxStoreMerchantOrderMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(0);
        when(mockWxStoreTableClientService.getOrderGuid("diningTableGuid")).thenReturn("orderGuid");
        when(mockUserMemberSessionUtils.getUserMemberSession("openId")).thenReturn(null);

        // Configure WxStoreMerchantOrderMapper.selectOne(...).
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO1 = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO1.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        wxStoreMerchantOrderDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDO1.setTotalPrice(new BigDecimal("0.00"));
        wxStoreMerchantOrderDO1.setOpenId("openId");
        wxStoreMerchantOrderDO1.setNickName("nickName");
        wxStoreMerchantOrderDO1.setActualGuestsNo(0);
        wxStoreMerchantOrderDO1.setTradeMode(0);
        wxStoreMerchantOrderDO1.setOrderState(0);
        wxStoreMerchantOrderDO1.setDenialReason("denialReason");
        wxStoreMerchantOrderDO1.setOrderGuid("guid");
        wxStoreMerchantOrderDO1.setRemark("remark");
        wxStoreMerchantOrderDO1.setAreaGuid("areaGuid");
        wxStoreMerchantOrderDO1.setAreaName("areaName");
        wxStoreMerchantOrderDO1.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDO1.setStoreName("name");
        wxStoreMerchantOrderDO1.setBrandName("brandName");
        wxStoreMerchantOrderDO1.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDO1.setTableCode("tableCode");
        wxStoreMerchantOrderDO1.setOperationGuid("openId");
        wxStoreMerchantOrderDO1.setOperationSource(0);
        wxStoreMerchantOrderDO1.setOperationName("nickName");
        wxStoreMerchantOrderDO1.setOrderRecordGuid("58d0dfce-a97a-4608-b8a7-f7f4f68b843b");
        wxStoreMerchantOrderDO1.setCombine("mainOrderGuid");
        wxStoreMerchantOrderDO1.setOrderSource(0);
        when(mockWxStoreMerchantOrderMapper.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(wxStoreMerchantOrderDO1);

        // Configure WxStoreMerchantOrderMapper.updateById(...).
        final WxStoreMerchantOrderDO entity = new WxStoreMerchantOrderDO();
        entity.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        entity.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setTotalPrice(new BigDecimal("0.00"));
        entity.setOpenId("openId");
        entity.setNickName("nickName");
        entity.setActualGuestsNo(0);
        entity.setTradeMode(0);
        entity.setOrderState(0);
        entity.setDenialReason("denialReason");
        entity.setOrderGuid("guid");
        entity.setRemark("remark");
        entity.setAreaGuid("areaGuid");
        entity.setAreaName("areaName");
        entity.setStoreGuid("storeGuid");
        entity.setStoreName("name");
        entity.setBrandName("brandName");
        entity.setDiningTableGuid("diningTableGuid");
        entity.setTableCode("tableCode");
        entity.setOperationGuid("openId");
        entity.setOperationSource(0);
        entity.setOperationName("nickName");
        entity.setOrderRecordGuid("58d0dfce-a97a-4608-b8a7-f7f4f68b843b");
        entity.setCombine("mainOrderGuid");
        entity.setOrderSource(0);
        when(mockWxStoreMerchantOrderMapper.updateById(entity)).thenReturn(0);

        // Configure WxOrderRecordService.getOne(...).
        final WxOrderRecordDO wxOrderRecordDO = new WxOrderRecordDO(0L, "58d0dfce-a97a-4608-b8a7-f7f4f68b843b",
                "orderGuid", "merchantGuid", "guid", "brandGuid", "brandName", "logUrl", "storeGuid", "storeName",
                "areaGuid", "areaName", "tableGuid", "tableCode", 0, 0, "orderStateName", new BigDecimal("0.00"),
                new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName", "orderHolderNo",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0);
        when(mockWxOrderRecordService.getOne(any(LambdaQueryWrapper.class))).thenReturn(wxOrderRecordDO);

        // Configure OrderItemService.getOne(...).
        final WxOrderItemDO wxOrderItemDO = new WxOrderItemDO();
        wxOrderItemDO.setMerchantGuid("merchantGuid");
        wxOrderItemDO.setOrderRecordGuid("orderRecordGuid");
        wxOrderItemDO.setItemGuid("itemGuid");
        wxOrderItemDO.setItemName("itemName");
        wxOrderItemDO.setMemberPrice(new BigDecimal("0.00"));
        wxOrderItemDO.setOriginalPrice(new BigDecimal("0.00"));
        wxOrderItemDO.setRemark("remark");
        when(mockOrderItemService.getOne(any(LambdaQueryWrapper.class))).thenReturn(wxOrderItemDO);

        // Run the test
        final WxStoreMerchantOperationDTO result = wxStoreMerchantOrderImplUnderTest.operationMerchantOrder(
                wxOperateReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockBizMsgFeignClient).msg(BusinessMessageDTO.builder()
                .messageGuid("messageGuid")
                .subject("name")
                .content("content")
                .messageType(0)
                .detailMessageType(0)
                .platform("desc")
                .storeGuid("storeGuid")
                .storeName("name")
                .state("state")
                .createTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .pushTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .messageTypeStr("messageTypeStr")
                .build());
        verify(mockOrderItemService).setTableStaffInfo("e4c510fe-d776-4a77-bbe5-2067155947b9", "diningTableGuid");
        verify(mockWebsocketMessageHelper).sendOrderEmqMessage("diningTableGuid", Arrays.asList("value"));
        verify(mockWxStoreDineInOrderClientService).updateOrderRemarkById("orderGuid", "remark");

        // Confirm WxOrderItemMapper.updateById(...).
        final WxOrderItemDO entity1 = new WxOrderItemDO();
        entity1.setMerchantGuid("merchantGuid");
        entity1.setOrderRecordGuid("orderRecordGuid");
        entity1.setItemGuid("itemGuid");
        entity1.setItemName("itemName");
        entity1.setMemberPrice(new BigDecimal("0.00"));
        entity1.setOriginalPrice(new BigDecimal("0.00"));
        entity1.setRemark("remark");
        verify(mockWxOrderItemMapper).updateById(entity1);
        verify(mockWxStoreDineInOrderClientService).updateOrderItemRemarkById("orderGuid", "itemGuid", "remark");
    }

    @Test
    public void testOperationMerchantOrder_RedisUtilsSetNxReturnsTrue() {
        // Setup
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setUserWxPublicOpenId("openId");
        dineInItemDTO.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO.setItemGuid("itemGuid");
        dineInItemDTO.setItemType(0);
        dineInItemDTO.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO.setItemTypeName("itemTypeName");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO = new SubDineInItemDTO();
        subDineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO));
        dineInItemDTO.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO));
        final ItemAttrDTO itemAttrDTO = new ItemAttrDTO();
        itemAttrDTO.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO.setNum(0);
        dineInItemDTO.setItemAttrDTOS(Arrays.asList(itemAttrDTO));
        final WxOperateReqDTO wxOperateReqDTO = new WxOperateReqDTO("guid", 0, "denialReason", "tableGuid", "tableName",
                0, "areaName", "remark", Arrays.asList(dineInItemDTO));
        final WxStoreMerchantOperationDTO expectedResult = WxStoreMerchantOperationDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .storeGuid("storeGuid")
                .diningTableGuid("diningTableGuid")
                .tableCode("tableCode")
                .areaName("areaName")
                .storeName("storeName")
                .brandName("brandName")
                .estimateItemRespDTO(new EstimateItemRespDTO())
                .errorMsg("errorMsg")
                .build();

        // Configure WxStoreDineInOrderClientService.updateRemark(...).
        final CreateDineInOrderReqDTO createDineInOrderReqDTO = new CreateDineInOrderReqDTO();
        createDineInOrderReqDTO.setDeviceType(0);
        createDineInOrderReqDTO.setDeviceId("openId");
        createDineInOrderReqDTO.setEnterpriseGuid("enterpriseGuid");
        createDineInOrderReqDTO.setEnterpriseName("enterpriseName");
        createDineInOrderReqDTO.setStoreGuid("storeGuid");
        createDineInOrderReqDTO.setStoreName("name");
        createDineInOrderReqDTO.setUserGuid("openId");
        createDineInOrderReqDTO.setUserName("nickName");
        createDineInOrderReqDTO.setGuid("e4c510fe-d776-4a77-bbe5-2067155947b9");
        createDineInOrderReqDTO.setUserWxPublicOpenId("openId");
        createDineInOrderReqDTO.setRemark("remark");
        createDineInOrderReqDTO.setDiningTableGuid("diningTableGuid");
        createDineInOrderReqDTO.setDiningTableName("tableCode");
        createDineInOrderReqDTO.setAreaName("areaName");
        createDineInOrderReqDTO.setGuestCount(0);
        createDineInOrderReqDTO.setPrint(0);
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setUserWxPublicOpenId("openId");
        dineInItemDTO1.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO1.setItemGuid("itemGuid");
        dineInItemDTO1.setItemType(0);
        dineInItemDTO1.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO1.setItemTypeName("itemTypeName");
        dineInItemDTO1.setPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO1.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO1 = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO1 = new SubDineInItemDTO();
        subDineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO1.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO1.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO1));
        dineInItemDTO1.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO1));
        final ItemAttrDTO itemAttrDTO1 = new ItemAttrDTO();
        itemAttrDTO1.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO1.setNum(0);
        dineInItemDTO1.setItemAttrDTOS(Arrays.asList(itemAttrDTO1));
        createDineInOrderReqDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO1));
        createDineInOrderReqDTO.setEstimateType(0);
        when(mockWxStoreDineInOrderClientService.updateRemark(createDineInOrderReqDTO)).thenReturn(false);

        // Configure WxStoreDineInOrderClientService.batchAddItems(...).
        final EstimateItemRespDTO estimateItemRespDTO = new EstimateItemRespDTO();
        estimateItemRespDTO.setResult(false);
        estimateItemRespDTO.setEstimate(false);
        estimateItemRespDTO.setErrorMsg("errorMsg");
        estimateItemRespDTO.setEstimateInfo("estimateInfo");
        estimateItemRespDTO.setEstimateSkuGuids(Arrays.asList("value"));
        final CreateDineInOrderReqDTO createDineInOrderReqDTO1 = new CreateDineInOrderReqDTO();
        createDineInOrderReqDTO1.setDeviceType(0);
        createDineInOrderReqDTO1.setDeviceId("openId");
        createDineInOrderReqDTO1.setEnterpriseGuid("enterpriseGuid");
        createDineInOrderReqDTO1.setEnterpriseName("enterpriseName");
        createDineInOrderReqDTO1.setStoreGuid("storeGuid");
        createDineInOrderReqDTO1.setStoreName("name");
        createDineInOrderReqDTO1.setUserGuid("openId");
        createDineInOrderReqDTO1.setUserName("nickName");
        createDineInOrderReqDTO1.setGuid("e4c510fe-d776-4a77-bbe5-2067155947b9");
        createDineInOrderReqDTO1.setUserWxPublicOpenId("openId");
        createDineInOrderReqDTO1.setRemark("remark");
        createDineInOrderReqDTO1.setDiningTableGuid("diningTableGuid");
        createDineInOrderReqDTO1.setDiningTableName("tableCode");
        createDineInOrderReqDTO1.setAreaName("areaName");
        createDineInOrderReqDTO1.setGuestCount(0);
        createDineInOrderReqDTO1.setPrint(0);
        final DineInItemDTO dineInItemDTO2 = new DineInItemDTO();
        dineInItemDTO2.setUserWxPublicOpenId("openId");
        dineInItemDTO2.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO2.setItemGuid("itemGuid");
        dineInItemDTO2.setItemType(0);
        dineInItemDTO2.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO2.setItemTypeName("itemTypeName");
        dineInItemDTO2.setPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO2.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO2 = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO2 = new SubDineInItemDTO();
        subDineInItemDTO2.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO2.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO2.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO2));
        dineInItemDTO2.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO2));
        final ItemAttrDTO itemAttrDTO2 = new ItemAttrDTO();
        itemAttrDTO2.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO2.setNum(0);
        dineInItemDTO2.setItemAttrDTOS(Arrays.asList(itemAttrDTO2));
        createDineInOrderReqDTO1.setDineInItemDTOS(Arrays.asList(dineInItemDTO2));
        createDineInOrderReqDTO1.setEstimateType(0);
        when(mockWxStoreDineInOrderClientService.batchAddItems(createDineInOrderReqDTO1))
                .thenReturn(estimateItemRespDTO);

        when(mockRedisUtils.generatdDTOGuid(BusinessMessageDTO.class)).thenReturn("messageGuid");

        // Configure WxStoreTableClientService.tryOpen(...).
        final OpenTableDTO openTableDTO = new OpenTableDTO();
        openTableDTO.setDeviceType(0);
        openTableDTO.setDeviceId("openId");
        openTableDTO.setEnterpriseGuid("enterpriseGuid");
        openTableDTO.setEnterpriseName("enterpriseName");
        openTableDTO.setStoreGuid("storeGuid");
        openTableDTO.setStoreName("name");
        openTableDTO.setUserGuid("openId");
        openTableDTO.setUserName("nickName");
        openTableDTO.setTableGuid("diningTableGuid");
        openTableDTO.setTableCode("tableCode");
        openTableDTO.setAreaName("areaName");
        openTableDTO.setActualGuestsNo(0);
        when(mockWxStoreTableClientService.tryOpen(openTableDTO)).thenReturn("e4c510fe-d776-4a77-bbe5-2067155947b9");

        // Configure OrderItemService.getItemListByMerchantGuid(...).
        final DineInItemDTO dineInItemDTO3 = new DineInItemDTO();
        dineInItemDTO3.setUserWxPublicOpenId("openId");
        dineInItemDTO3.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO3.setItemGuid("itemGuid");
        dineInItemDTO3.setItemType(0);
        dineInItemDTO3.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO3.setItemTypeName("itemTypeName");
        dineInItemDTO3.setPrice(new BigDecimal("0.00"));
        dineInItemDTO3.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO3.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO3.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO3 = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO3 = new SubDineInItemDTO();
        subDineInItemDTO3.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO3.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO3.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO3));
        dineInItemDTO3.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO3));
        final ItemAttrDTO itemAttrDTO3 = new ItemAttrDTO();
        itemAttrDTO3.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO3.setNum(0);
        dineInItemDTO3.setItemAttrDTOS(Arrays.asList(itemAttrDTO3));
        final List<DineInItemDTO> dineInItemDTOS = Arrays.asList(dineInItemDTO3);
        when(mockOrderItemService.getItemListByMerchantGuid("guid")).thenReturn(dineInItemDTOS);

        // Configure WxStoreMerchantOrderMapstruct.getWxStoreMerchantOrder(...).
        final WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        wxStoreMerchantOrderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setOpenId("openId");
        wxStoreMerchantOrderDTO.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDTO.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDTO.setOrderState(0);
        wxStoreMerchantOrderDTO.setTradeMode(0);
        wxStoreMerchantOrderDTO.setTradeModeName("desc");
        wxStoreMerchantOrderDTO.setMsgSource(0);
        wxStoreMerchantOrderDTO.setMsgSourceName("desc");
        final DineInItemDTO dineInItemDTO4 = new DineInItemDTO();
        dineInItemDTO4.setUserWxPublicOpenId("openId");
        dineInItemDTO4.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO4.setItemGuid("itemGuid");
        dineInItemDTO4.setItemType(0);
        dineInItemDTO4.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO4.setItemTypeName("itemTypeName");
        dineInItemDTO4.setPrice(new BigDecimal("0.00"));
        dineInItemDTO4.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO4.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO4.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO4 = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO4 = new SubDineInItemDTO();
        subDineInItemDTO4.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO4.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO4.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO4));
        dineInItemDTO4.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO4));
        final ItemAttrDTO itemAttrDTO4 = new ItemAttrDTO();
        itemAttrDTO4.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO4.setNum(0);
        dineInItemDTO4.setItemAttrDTOS(Arrays.asList(itemAttrDTO4));
        wxStoreMerchantOrderDTO.setDineInItemDTOList(Arrays.asList(dineInItemDTO4));
        wxStoreMerchantOrderDTO.setOrderNo("guid");
        wxStoreMerchantOrderDTO.setOperationSource(0);
        wxStoreMerchantOrderDTO.setOperationSourceName("desc");
        wxStoreMerchantOrderDTO.setOrderSource(0);
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        wxStoreMerchantOrderDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDO.setTotalPrice(new BigDecimal("0.00"));
        wxStoreMerchantOrderDO.setOpenId("openId");
        wxStoreMerchantOrderDO.setNickName("nickName");
        wxStoreMerchantOrderDO.setActualGuestsNo(0);
        wxStoreMerchantOrderDO.setTradeMode(0);
        wxStoreMerchantOrderDO.setOrderState(0);
        wxStoreMerchantOrderDO.setDenialReason("denialReason");
        wxStoreMerchantOrderDO.setOrderGuid("guid");
        wxStoreMerchantOrderDO.setRemark("remark");
        wxStoreMerchantOrderDO.setAreaGuid("areaGuid");
        wxStoreMerchantOrderDO.setAreaName("areaName");
        wxStoreMerchantOrderDO.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDO.setStoreName("name");
        wxStoreMerchantOrderDO.setBrandName("brandName");
        wxStoreMerchantOrderDO.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDO.setTableCode("tableCode");
        wxStoreMerchantOrderDO.setOperationGuid("openId");
        wxStoreMerchantOrderDO.setOperationSource(0);
        wxStoreMerchantOrderDO.setOperationName("nickName");
        wxStoreMerchantOrderDO.setOrderRecordGuid("58d0dfce-a97a-4608-b8a7-f7f4f68b843b");
        wxStoreMerchantOrderDO.setCombine("mainOrderGuid");
        wxStoreMerchantOrderDO.setOrderSource(0);
        when(mockWxStoreMerchantOrderMapstruct.getWxStoreMerchantOrder(wxStoreMerchantOrderDO))
                .thenReturn(wxStoreMerchantOrderDTO);

        // Configure WxStoreSessionDetailsService.getStoreDetail(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("13b15130-a1a8-4a4f-8eeb-d1af12c961a2");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        when(mockWxStoreSessionDetailsService.getStoreDetail("storeGuid")).thenReturn(storeDTO);

        when(mockWebsocketUtils.getOpenIdsByTableGuid("diningTableGuid"))
                .thenReturn(new HashSet<>(Arrays.asList("value")));
        when(mockWxOrderRecordService.updateById(
                new WxOrderRecordDO(0L, "58d0dfce-a97a-4608-b8a7-f7f4f68b843b", "orderGuid", "merchantGuid", "guid",
                        "brandGuid", "brandName", "logUrl", "storeGuid", "storeName", "areaGuid", "areaName",
                        "tableGuid", "tableCode", 0, 0, "orderStateName", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName", "orderHolderNo",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0)))
                .thenReturn(false);
        when(mockWxStoreMerchantOrderMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(0);
        when(mockWxStoreTableClientService.getOrderGuid("diningTableGuid")).thenReturn("orderGuid");

        // Configure UserMemberSessionUtils.getUserMemberSession(...).
        final UserMemberSessionDTO userMemberSessionDTO = new UserMemberSessionDTO("storeGuid", "openId", "userGuid",
                false, "nickName", "memberInfoGuid", "cardGuid", "memberInfoCardGuid", "systemManagementGuid", 0, false,
                "volumeCode", false, new BigDecimal("0.00"), new BigDecimal("0.00"), "operSubjectGuid");
        when(mockUserMemberSessionUtils.getUserMemberSession("openId")).thenReturn(userMemberSessionDTO);

        // Configure HsaBaseClientService.getMemberInfo(...).
        final ResponseMemberInfo responseMemberInfo = new ResponseMemberInfo();
        responseMemberInfo.setQrcode("qrcode");
        responseMemberInfo.setMemberInfoGuid("memberInfoGuid");
        responseMemberInfo.setOperSubjectGuid("operSubjectGuid");
        responseMemberInfo.setEnterpriseGuid("enterpriseGuid");
        responseMemberInfo.setPhoneNum("openId");
        final ResponseModel<ResponseMemberInfo> responseMemberInfoResponseModel = new ResponseModel<>(
                responseMemberInfo);
        final RequestQueryMemberInfo requestQueryMemberInfo = new RequestQueryMemberInfo();
        requestQueryMemberInfo.setMemberInfoGuid("memberInfoGuid");
        requestQueryMemberInfo.setUnionId("unionId");
        requestQueryMemberInfo.setOpenId("openId");
        requestQueryMemberInfo.setPhoneNum("phoneNum");
        requestQueryMemberInfo.setOperSubjectGuid("operSubjectGuid");
        when(mockHsaBaseClientService.getMemberInfo(requestQueryMemberInfo))
                .thenReturn(responseMemberInfoResponseModel);

        when(mockRedisUtils.setNx("key", "1", 18000L)).thenReturn(true);

        // Configure WxStoreDineInBillClientService.calculate(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        final DineInItemDTO dineInItemDTO5 = new DineInItemDTO();
        dineInItemDTO5.setUserWxPublicOpenId("openId");
        dineInItemDTO5.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO5.setItemGuid("itemGuid");
        dineInItemDTO5.setItemType(0);
        dineInItemDTO5.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO5.setItemTypeName("itemTypeName");
        dineInItemDTO5.setPrice(new BigDecimal("0.00"));
        dineInItemDTO5.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO5.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO5.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO5 = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO5 = new SubDineInItemDTO();
        subDineInItemDTO5.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO5.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO5.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO5));
        dineInItemDTO5.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO5));
        final ItemAttrDTO itemAttrDTO5 = new ItemAttrDTO();
        itemAttrDTO5.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO5.setNum(0);
        dineInItemDTO5.setItemAttrDTOS(Arrays.asList(itemAttrDTO5));
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO5));
        final BillCalculateReqDTO billCalculateReqDTO = new BillCalculateReqDTO();
        billCalculateReqDTO.setDeviceType(0);
        billCalculateReqDTO.setDeviceId("openId");
        billCalculateReqDTO.setEnterpriseGuid("enterpriseGuid");
        billCalculateReqDTO.setEnterpriseName("enterpriseName");
        billCalculateReqDTO.setStoreGuid("storeGuid");
        billCalculateReqDTO.setStoreName("name");
        billCalculateReqDTO.setUserGuid("openId");
        billCalculateReqDTO.setUserName("nickName");
        billCalculateReqDTO.setOrderGuid("orderGuid");
        billCalculateReqDTO.setMemberLogin(0);
        billCalculateReqDTO.setMemberIntegral(0);
        billCalculateReqDTO.setMemberInfoCardGuid("memberInfoCardGuid");
        billCalculateReqDTO.setMemberPhone("openId");
        when(mockWxStoreDineInBillClientService.calculate(billCalculateReqDTO)).thenReturn(dineinOrderDetailRespDTO);

        // Configure WxStoreMerchantOrderMapper.selectOne(...).
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO1 = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO1.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        wxStoreMerchantOrderDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDO1.setTotalPrice(new BigDecimal("0.00"));
        wxStoreMerchantOrderDO1.setOpenId("openId");
        wxStoreMerchantOrderDO1.setNickName("nickName");
        wxStoreMerchantOrderDO1.setActualGuestsNo(0);
        wxStoreMerchantOrderDO1.setTradeMode(0);
        wxStoreMerchantOrderDO1.setOrderState(0);
        wxStoreMerchantOrderDO1.setDenialReason("denialReason");
        wxStoreMerchantOrderDO1.setOrderGuid("guid");
        wxStoreMerchantOrderDO1.setRemark("remark");
        wxStoreMerchantOrderDO1.setAreaGuid("areaGuid");
        wxStoreMerchantOrderDO1.setAreaName("areaName");
        wxStoreMerchantOrderDO1.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDO1.setStoreName("name");
        wxStoreMerchantOrderDO1.setBrandName("brandName");
        wxStoreMerchantOrderDO1.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDO1.setTableCode("tableCode");
        wxStoreMerchantOrderDO1.setOperationGuid("openId");
        wxStoreMerchantOrderDO1.setOperationSource(0);
        wxStoreMerchantOrderDO1.setOperationName("nickName");
        wxStoreMerchantOrderDO1.setOrderRecordGuid("58d0dfce-a97a-4608-b8a7-f7f4f68b843b");
        wxStoreMerchantOrderDO1.setCombine("mainOrderGuid");
        wxStoreMerchantOrderDO1.setOrderSource(0);
        when(mockWxStoreMerchantOrderMapper.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(wxStoreMerchantOrderDO1);

        // Configure WxStoreMerchantOrderMapper.updateById(...).
        final WxStoreMerchantOrderDO entity = new WxStoreMerchantOrderDO();
        entity.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        entity.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setTotalPrice(new BigDecimal("0.00"));
        entity.setOpenId("openId");
        entity.setNickName("nickName");
        entity.setActualGuestsNo(0);
        entity.setTradeMode(0);
        entity.setOrderState(0);
        entity.setDenialReason("denialReason");
        entity.setOrderGuid("guid");
        entity.setRemark("remark");
        entity.setAreaGuid("areaGuid");
        entity.setAreaName("areaName");
        entity.setStoreGuid("storeGuid");
        entity.setStoreName("name");
        entity.setBrandName("brandName");
        entity.setDiningTableGuid("diningTableGuid");
        entity.setTableCode("tableCode");
        entity.setOperationGuid("openId");
        entity.setOperationSource(0);
        entity.setOperationName("nickName");
        entity.setOrderRecordGuid("58d0dfce-a97a-4608-b8a7-f7f4f68b843b");
        entity.setCombine("mainOrderGuid");
        entity.setOrderSource(0);
        when(mockWxStoreMerchantOrderMapper.updateById(entity)).thenReturn(0);

        // Configure WxOrderRecordService.getOne(...).
        final WxOrderRecordDO wxOrderRecordDO = new WxOrderRecordDO(0L, "58d0dfce-a97a-4608-b8a7-f7f4f68b843b",
                "orderGuid", "merchantGuid", "guid", "brandGuid", "brandName", "logUrl", "storeGuid", "storeName",
                "areaGuid", "areaName", "tableGuid", "tableCode", 0, 0, "orderStateName", new BigDecimal("0.00"),
                new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName", "orderHolderNo",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0);
        when(mockWxOrderRecordService.getOne(any(LambdaQueryWrapper.class))).thenReturn(wxOrderRecordDO);

        // Configure OrderItemService.getOne(...).
        final WxOrderItemDO wxOrderItemDO = new WxOrderItemDO();
        wxOrderItemDO.setMerchantGuid("merchantGuid");
        wxOrderItemDO.setOrderRecordGuid("orderRecordGuid");
        wxOrderItemDO.setItemGuid("itemGuid");
        wxOrderItemDO.setItemName("itemName");
        wxOrderItemDO.setMemberPrice(new BigDecimal("0.00"));
        wxOrderItemDO.setOriginalPrice(new BigDecimal("0.00"));
        wxOrderItemDO.setRemark("remark");
        when(mockOrderItemService.getOne(any(LambdaQueryWrapper.class))).thenReturn(wxOrderItemDO);

        // Run the test
        final WxStoreMerchantOperationDTO result = wxStoreMerchantOrderImplUnderTest.operationMerchantOrder(
                wxOperateReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockBizMsgFeignClient).msg(BusinessMessageDTO.builder()
                .messageGuid("messageGuid")
                .subject("name")
                .content("content")
                .messageType(0)
                .detailMessageType(0)
                .platform("desc")
                .storeGuid("storeGuid")
                .storeName("name")
                .state("state")
                .createTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .pushTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .messageTypeStr("messageTypeStr")
                .build());
        verify(mockOrderItemService).setTableStaffInfo("e4c510fe-d776-4a77-bbe5-2067155947b9", "diningTableGuid");
        verify(mockWebsocketMessageHelper).sendOrderEmqMessage("diningTableGuid", Arrays.asList("value"));
        verify(mockWxStoreDineInOrderClientService).updateOrderRemarkById("orderGuid", "remark");

        // Confirm WxOrderItemMapper.updateById(...).
        final WxOrderItemDO entity1 = new WxOrderItemDO();
        entity1.setMerchantGuid("merchantGuid");
        entity1.setOrderRecordGuid("orderRecordGuid");
        entity1.setItemGuid("itemGuid");
        entity1.setItemName("itemName");
        entity1.setMemberPrice(new BigDecimal("0.00"));
        entity1.setOriginalPrice(new BigDecimal("0.00"));
        entity1.setRemark("remark");
        verify(mockWxOrderItemMapper).updateById(entity1);
        verify(mockWxStoreDineInOrderClientService).updateOrderItemRemarkById("orderGuid", "itemGuid", "remark");
    }

    @Test
    public void testPushAutoMsg() {
        // Setup
        final WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        wxStoreMerchantOrderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setOpenId("openId");
        wxStoreMerchantOrderDTO.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDTO.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDTO.setOrderState(0);
        wxStoreMerchantOrderDTO.setTradeMode(0);
        wxStoreMerchantOrderDTO.setTradeModeName("desc");
        wxStoreMerchantOrderDTO.setMsgSource(0);
        wxStoreMerchantOrderDTO.setMsgSourceName("desc");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setUserWxPublicOpenId("openId");
        dineInItemDTO.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO.setItemGuid("itemGuid");
        dineInItemDTO.setItemType(0);
        dineInItemDTO.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO.setItemTypeName("itemTypeName");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO = new SubDineInItemDTO();
        subDineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO));
        dineInItemDTO.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO));
        final ItemAttrDTO itemAttrDTO = new ItemAttrDTO();
        itemAttrDTO.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO.setNum(0);
        dineInItemDTO.setItemAttrDTOS(Arrays.asList(itemAttrDTO));
        wxStoreMerchantOrderDTO.setDineInItemDTOList(Arrays.asList(dineInItemDTO));
        wxStoreMerchantOrderDTO.setOrderNo("guid");
        wxStoreMerchantOrderDTO.setOperationSource(0);
        wxStoreMerchantOrderDTO.setOperationSourceName("desc");
        wxStoreMerchantOrderDTO.setOrderSource(0);

        final StoreDeviceDTO masterDevice = new StoreDeviceDTO("enterpriseGuid", "storeNo", "storeGuid", "storeName",
                "deviceNo", "deviceGuid", false, false, 0, "deviceName", 0, "createUserGuid", "createUserName",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "tableGuid", 0);
        when(mockRedisUtils.generatdDTOGuid(BusinessMessageDTO.class)).thenReturn("messageGuid");

        // Configure WxStoreSessionDetailsService.getStoreDetail(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("13b15130-a1a8-4a4f-8eeb-d1af12c961a2");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        when(mockWxStoreSessionDetailsService.getStoreDetail("storeGuid")).thenReturn(storeDTO);

        // Run the test
        wxStoreMerchantOrderImplUnderTest.pushAutoMsg(wxStoreMerchantOrderDTO, masterDevice, 0);

        // Verify the results
        verify(mockBizMsgFeignClient).msg(BusinessMessageDTO.builder()
                .messageGuid("messageGuid")
                .subject("name")
                .content("content")
                .messageType(0)
                .detailMessageType(0)
                .platform("desc")
                .storeGuid("storeGuid")
                .storeName("name")
                .state("state")
                .createTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .pushTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .messageTypeStr("messageTypeStr")
                .build());
    }

    @Test
    public void testPushMsg2() {
        // Setup
        final WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        wxStoreMerchantOrderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setOpenId("openId");
        wxStoreMerchantOrderDTO.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDTO.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDTO.setOrderState(0);
        wxStoreMerchantOrderDTO.setTradeMode(0);
        wxStoreMerchantOrderDTO.setTradeModeName("desc");
        wxStoreMerchantOrderDTO.setMsgSource(0);
        wxStoreMerchantOrderDTO.setMsgSourceName("desc");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setUserWxPublicOpenId("openId");
        dineInItemDTO.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO.setItemGuid("itemGuid");
        dineInItemDTO.setItemType(0);
        dineInItemDTO.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO.setItemTypeName("itemTypeName");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO = new SubDineInItemDTO();
        subDineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO));
        dineInItemDTO.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO));
        final ItemAttrDTO itemAttrDTO = new ItemAttrDTO();
        itemAttrDTO.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO.setNum(0);
        dineInItemDTO.setItemAttrDTOS(Arrays.asList(itemAttrDTO));
        wxStoreMerchantOrderDTO.setDineInItemDTOList(Arrays.asList(dineInItemDTO));
        wxStoreMerchantOrderDTO.setOrderNo("guid");
        wxStoreMerchantOrderDTO.setOperationSource(0);
        wxStoreMerchantOrderDTO.setOperationSourceName("desc");
        wxStoreMerchantOrderDTO.setOrderSource(0);

        when(mockRedisUtils.generatdDTOGuid(BusinessMessageDTO.class)).thenReturn("messageGuid");

        // Configure WxStoreSessionDetailsService.getStoreDetail(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("13b15130-a1a8-4a4f-8eeb-d1af12c961a2");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        when(mockWxStoreSessionDetailsService.getStoreDetail("storeGuid")).thenReturn(storeDTO);

        // Run the test
        wxStoreMerchantOrderImplUnderTest.pushMsg(wxStoreMerchantOrderDTO);

        // Verify the results
        verify(mockBizMsgFeignClient).msg(BusinessMessageDTO.builder()
                .messageGuid("messageGuid")
                .subject("name")
                .content("content")
                .messageType(0)
                .detailMessageType(0)
                .platform("desc")
                .storeGuid("storeGuid")
                .storeName("name")
                .state("state")
                .createTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .pushTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .messageTypeStr("messageTypeStr")
                .build());
    }

    @Test
    public void testUpdateMerchantOrder() {
        // Setup
        final WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        wxStoreMerchantOrderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setOpenId("openId");
        wxStoreMerchantOrderDTO.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDTO.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDTO.setOrderState(0);
        wxStoreMerchantOrderDTO.setTradeMode(0);
        wxStoreMerchantOrderDTO.setTradeModeName("desc");
        wxStoreMerchantOrderDTO.setMsgSource(0);
        wxStoreMerchantOrderDTO.setMsgSourceName("desc");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setUserWxPublicOpenId("openId");
        dineInItemDTO.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO.setItemGuid("itemGuid");
        dineInItemDTO.setItemType(0);
        dineInItemDTO.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO.setItemTypeName("itemTypeName");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO = new SubDineInItemDTO();
        subDineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO));
        dineInItemDTO.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO));
        final ItemAttrDTO itemAttrDTO = new ItemAttrDTO();
        itemAttrDTO.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO.setNum(0);
        dineInItemDTO.setItemAttrDTOS(Arrays.asList(itemAttrDTO));
        wxStoreMerchantOrderDTO.setDineInItemDTOList(Arrays.asList(dineInItemDTO));
        wxStoreMerchantOrderDTO.setOrderNo("guid");
        wxStoreMerchantOrderDTO.setOperationSource(0);
        wxStoreMerchantOrderDTO.setOperationSourceName("desc");
        wxStoreMerchantOrderDTO.setOrderSource(0);

        // Configure WxStoreMerchantOrderMapstruct.getWxStoreMerchantOrder(...).
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        wxStoreMerchantOrderDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDO.setTotalPrice(new BigDecimal("0.00"));
        wxStoreMerchantOrderDO.setOpenId("openId");
        wxStoreMerchantOrderDO.setNickName("nickName");
        wxStoreMerchantOrderDO.setActualGuestsNo(0);
        wxStoreMerchantOrderDO.setTradeMode(0);
        wxStoreMerchantOrderDO.setOrderState(0);
        wxStoreMerchantOrderDO.setDenialReason("denialReason");
        wxStoreMerchantOrderDO.setOrderGuid("guid");
        wxStoreMerchantOrderDO.setRemark("remark");
        wxStoreMerchantOrderDO.setAreaGuid("areaGuid");
        wxStoreMerchantOrderDO.setAreaName("areaName");
        wxStoreMerchantOrderDO.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDO.setStoreName("name");
        wxStoreMerchantOrderDO.setBrandName("brandName");
        wxStoreMerchantOrderDO.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDO.setTableCode("tableCode");
        wxStoreMerchantOrderDO.setOperationGuid("openId");
        wxStoreMerchantOrderDO.setOperationSource(0);
        wxStoreMerchantOrderDO.setOperationName("nickName");
        wxStoreMerchantOrderDO.setOrderRecordGuid("58d0dfce-a97a-4608-b8a7-f7f4f68b843b");
        wxStoreMerchantOrderDO.setCombine("mainOrderGuid");
        wxStoreMerchantOrderDO.setOrderSource(0);
        final WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO1 = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO1.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        wxStoreMerchantOrderDTO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO1.setOpenId("openId");
        wxStoreMerchantOrderDTO1.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDTO1.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDTO1.setOrderState(0);
        wxStoreMerchantOrderDTO1.setTradeMode(0);
        wxStoreMerchantOrderDTO1.setTradeModeName("desc");
        wxStoreMerchantOrderDTO1.setMsgSource(0);
        wxStoreMerchantOrderDTO1.setMsgSourceName("desc");
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setUserWxPublicOpenId("openId");
        dineInItemDTO1.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO1.setItemGuid("itemGuid");
        dineInItemDTO1.setItemType(0);
        dineInItemDTO1.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO1.setItemTypeName("itemTypeName");
        dineInItemDTO1.setPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO1.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO1 = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO1 = new SubDineInItemDTO();
        subDineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO1.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO1.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO1));
        dineInItemDTO1.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO1));
        final ItemAttrDTO itemAttrDTO1 = new ItemAttrDTO();
        itemAttrDTO1.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO1.setNum(0);
        dineInItemDTO1.setItemAttrDTOS(Arrays.asList(itemAttrDTO1));
        wxStoreMerchantOrderDTO1.setDineInItemDTOList(Arrays.asList(dineInItemDTO1));
        wxStoreMerchantOrderDTO1.setOrderNo("guid");
        wxStoreMerchantOrderDTO1.setOperationSource(0);
        wxStoreMerchantOrderDTO1.setOperationSourceName("desc");
        wxStoreMerchantOrderDTO1.setOrderSource(0);
        when(mockWxStoreMerchantOrderMapstruct.getWxStoreMerchantOrder(wxStoreMerchantOrderDTO1))
                .thenReturn(wxStoreMerchantOrderDO);

        when(mockRedisUtils.generatdDTOGuid(WxStoreMerchantOrderDTO.class))
                .thenReturn("aca475ce-291d-445a-8665-84db3c769401");

        // Configure WxStoreSessionDetailsService.getStoreDetail(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("13b15130-a1a8-4a4f-8eeb-d1af12c961a2");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        when(mockWxStoreSessionDetailsService.getStoreDetail("storeGuid")).thenReturn(storeDTO);

        // Run the test
        wxStoreMerchantOrderImplUnderTest.updateMerchantOrder(wxStoreMerchantOrderDTO);

        // Verify the results
        verify(mockBizMsgFeignClient).msg(BusinessMessageDTO.builder()
                .messageGuid("messageGuid")
                .subject("name")
                .content("content")
                .messageType(0)
                .detailMessageType(0)
                .platform("desc")
                .storeGuid("storeGuid")
                .storeName("name")
                .state("state")
                .createTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .pushTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .messageTypeStr("messageTypeStr")
                .build());
    }

    @Test
    public void testUpdateFastOrder() {
        // Setup
        final WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        wxStoreMerchantOrderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setOpenId("openId");
        wxStoreMerchantOrderDTO.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDTO.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDTO.setOrderState(0);
        wxStoreMerchantOrderDTO.setTradeMode(0);
        wxStoreMerchantOrderDTO.setTradeModeName("desc");
        wxStoreMerchantOrderDTO.setMsgSource(0);
        wxStoreMerchantOrderDTO.setMsgSourceName("desc");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setUserWxPublicOpenId("openId");
        dineInItemDTO.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO.setItemGuid("itemGuid");
        dineInItemDTO.setItemType(0);
        dineInItemDTO.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO.setItemTypeName("itemTypeName");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO = new SubDineInItemDTO();
        subDineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO));
        dineInItemDTO.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO));
        final ItemAttrDTO itemAttrDTO = new ItemAttrDTO();
        itemAttrDTO.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO.setNum(0);
        dineInItemDTO.setItemAttrDTOS(Arrays.asList(itemAttrDTO));
        wxStoreMerchantOrderDTO.setDineInItemDTOList(Arrays.asList(dineInItemDTO));
        wxStoreMerchantOrderDTO.setOrderNo("guid");
        wxStoreMerchantOrderDTO.setOperationSource(0);
        wxStoreMerchantOrderDTO.setOperationSourceName("desc");
        wxStoreMerchantOrderDTO.setOrderSource(0);

        // Configure WxStoreMerchantOrderMapstruct.getWxStoreMerchantOrder(...).
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        wxStoreMerchantOrderDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDO.setTotalPrice(new BigDecimal("0.00"));
        wxStoreMerchantOrderDO.setOpenId("openId");
        wxStoreMerchantOrderDO.setNickName("nickName");
        wxStoreMerchantOrderDO.setActualGuestsNo(0);
        wxStoreMerchantOrderDO.setTradeMode(0);
        wxStoreMerchantOrderDO.setOrderState(0);
        wxStoreMerchantOrderDO.setDenialReason("denialReason");
        wxStoreMerchantOrderDO.setOrderGuid("guid");
        wxStoreMerchantOrderDO.setRemark("remark");
        wxStoreMerchantOrderDO.setAreaGuid("areaGuid");
        wxStoreMerchantOrderDO.setAreaName("areaName");
        wxStoreMerchantOrderDO.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDO.setStoreName("name");
        wxStoreMerchantOrderDO.setBrandName("brandName");
        wxStoreMerchantOrderDO.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDO.setTableCode("tableCode");
        wxStoreMerchantOrderDO.setOperationGuid("openId");
        wxStoreMerchantOrderDO.setOperationSource(0);
        wxStoreMerchantOrderDO.setOperationName("nickName");
        wxStoreMerchantOrderDO.setOrderRecordGuid("58d0dfce-a97a-4608-b8a7-f7f4f68b843b");
        wxStoreMerchantOrderDO.setCombine("mainOrderGuid");
        wxStoreMerchantOrderDO.setOrderSource(0);
        final WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO1 = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO1.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        wxStoreMerchantOrderDTO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO1.setOpenId("openId");
        wxStoreMerchantOrderDTO1.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDTO1.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDTO1.setOrderState(0);
        wxStoreMerchantOrderDTO1.setTradeMode(0);
        wxStoreMerchantOrderDTO1.setTradeModeName("desc");
        wxStoreMerchantOrderDTO1.setMsgSource(0);
        wxStoreMerchantOrderDTO1.setMsgSourceName("desc");
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setUserWxPublicOpenId("openId");
        dineInItemDTO1.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO1.setItemGuid("itemGuid");
        dineInItemDTO1.setItemType(0);
        dineInItemDTO1.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO1.setItemTypeName("itemTypeName");
        dineInItemDTO1.setPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO1.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO1 = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO1 = new SubDineInItemDTO();
        subDineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO1.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO1.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO1));
        dineInItemDTO1.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO1));
        final ItemAttrDTO itemAttrDTO1 = new ItemAttrDTO();
        itemAttrDTO1.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO1.setNum(0);
        dineInItemDTO1.setItemAttrDTOS(Arrays.asList(itemAttrDTO1));
        wxStoreMerchantOrderDTO1.setDineInItemDTOList(Arrays.asList(dineInItemDTO1));
        wxStoreMerchantOrderDTO1.setOrderNo("guid");
        wxStoreMerchantOrderDTO1.setOperationSource(0);
        wxStoreMerchantOrderDTO1.setOperationSourceName("desc");
        wxStoreMerchantOrderDTO1.setOrderSource(0);
        when(mockWxStoreMerchantOrderMapstruct.getWxStoreMerchantOrder(wxStoreMerchantOrderDTO1))
                .thenReturn(wxStoreMerchantOrderDO);

        when(mockRedisUtils.generatdDTOGuid(WxStoreMerchantOrderDTO.class))
                .thenReturn("aca475ce-291d-445a-8665-84db3c769401");

        // Run the test
        wxStoreMerchantOrderImplUnderTest.updateFastOrder(wxStoreMerchantOrderDTO);

        // Verify the results
        // Confirm RedisUtils.set(...).
        final WxStoreMerchantOrderDTO value = new WxStoreMerchantOrderDTO();
        value.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        value.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        value.setOpenId("openId");
        value.setDiningTableGuid("diningTableGuid");
        value.setStoreGuid("storeGuid");
        value.setOrderState(0);
        value.setTradeMode(0);
        value.setTradeModeName("desc");
        value.setMsgSource(0);
        value.setMsgSourceName("desc");
        final DineInItemDTO dineInItemDTO2 = new DineInItemDTO();
        dineInItemDTO2.setUserWxPublicOpenId("openId");
        dineInItemDTO2.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO2.setItemGuid("itemGuid");
        dineInItemDTO2.setItemType(0);
        dineInItemDTO2.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO2.setItemTypeName("itemTypeName");
        dineInItemDTO2.setPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO2.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO2 = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO2 = new SubDineInItemDTO();
        subDineInItemDTO2.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO2.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO2.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO2));
        dineInItemDTO2.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO2));
        final ItemAttrDTO itemAttrDTO2 = new ItemAttrDTO();
        itemAttrDTO2.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO2.setNum(0);
        dineInItemDTO2.setItemAttrDTOS(Arrays.asList(itemAttrDTO2));
        value.setDineInItemDTOList(Arrays.asList(dineInItemDTO2));
        value.setOrderNo("guid");
        value.setOperationSource(0);
        value.setOperationSourceName("desc");
        value.setOrderSource(0);
        verify(mockRedisUtils).set("key", value);
    }

    @Test
    public void testDelMerchantOrder() {
        wxStoreMerchantOrderImplUnderTest.delMerchantOrder(new WxStoreMerchantOrderDTO());
    }

    @Test
    public void testGetPendingOrders() {
        // Setup
        final WxStorePendingOrdersQuery query = WxStorePendingOrdersQuery.builder()
                .diningTableGuid("diningTableGuid")
                .orderStates(Arrays.asList(0))
                .tradeMode(0)
                .orderGuid("orderGuid")
                .combine("combine")
                .build();
        final WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        wxStoreMerchantOrderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setOpenId("openId");
        wxStoreMerchantOrderDTO.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDTO.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDTO.setOrderState(0);
        wxStoreMerchantOrderDTO.setTradeMode(0);
        wxStoreMerchantOrderDTO.setTradeModeName("desc");
        wxStoreMerchantOrderDTO.setMsgSource(0);
        wxStoreMerchantOrderDTO.setMsgSourceName("desc");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setUserWxPublicOpenId("openId");
        dineInItemDTO.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO.setItemGuid("itemGuid");
        dineInItemDTO.setItemType(0);
        dineInItemDTO.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO.setItemTypeName("itemTypeName");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO = new SubDineInItemDTO();
        subDineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO));
        dineInItemDTO.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO));
        final ItemAttrDTO itemAttrDTO = new ItemAttrDTO();
        itemAttrDTO.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO.setNum(0);
        dineInItemDTO.setItemAttrDTOS(Arrays.asList(itemAttrDTO));
        wxStoreMerchantOrderDTO.setDineInItemDTOList(Arrays.asList(dineInItemDTO));
        wxStoreMerchantOrderDTO.setOrderNo("guid");
        wxStoreMerchantOrderDTO.setOperationSource(0);
        wxStoreMerchantOrderDTO.setOperationSourceName("desc");
        wxStoreMerchantOrderDTO.setOrderSource(0);
        final List<WxStoreMerchantOrderDTO> expectedResult = Arrays.asList(wxStoreMerchantOrderDTO);

        // Configure WxStoreMerchantOrderMapstruct.getWxStoreMerchantOrder(...).
        final WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO1 = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO1.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        wxStoreMerchantOrderDTO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO1.setOpenId("openId");
        wxStoreMerchantOrderDTO1.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDTO1.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDTO1.setOrderState(0);
        wxStoreMerchantOrderDTO1.setTradeMode(0);
        wxStoreMerchantOrderDTO1.setTradeModeName("desc");
        wxStoreMerchantOrderDTO1.setMsgSource(0);
        wxStoreMerchantOrderDTO1.setMsgSourceName("desc");
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setUserWxPublicOpenId("openId");
        dineInItemDTO1.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO1.setItemGuid("itemGuid");
        dineInItemDTO1.setItemType(0);
        dineInItemDTO1.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO1.setItemTypeName("itemTypeName");
        dineInItemDTO1.setPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO1.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO1 = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO1 = new SubDineInItemDTO();
        subDineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO1.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO1.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO1));
        dineInItemDTO1.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO1));
        final ItemAttrDTO itemAttrDTO1 = new ItemAttrDTO();
        itemAttrDTO1.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO1.setNum(0);
        dineInItemDTO1.setItemAttrDTOS(Arrays.asList(itemAttrDTO1));
        wxStoreMerchantOrderDTO1.setDineInItemDTOList(Arrays.asList(dineInItemDTO1));
        wxStoreMerchantOrderDTO1.setOrderNo("guid");
        wxStoreMerchantOrderDTO1.setOperationSource(0);
        wxStoreMerchantOrderDTO1.setOperationSourceName("desc");
        wxStoreMerchantOrderDTO1.setOrderSource(0);
        final List<WxStoreMerchantOrderDTO> wxStoreMerchantOrderDTOS = Arrays.asList(wxStoreMerchantOrderDTO1);
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        wxStoreMerchantOrderDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDO.setTotalPrice(new BigDecimal("0.00"));
        wxStoreMerchantOrderDO.setOpenId("openId");
        wxStoreMerchantOrderDO.setNickName("nickName");
        wxStoreMerchantOrderDO.setActualGuestsNo(0);
        wxStoreMerchantOrderDO.setTradeMode(0);
        wxStoreMerchantOrderDO.setOrderState(0);
        wxStoreMerchantOrderDO.setDenialReason("denialReason");
        wxStoreMerchantOrderDO.setOrderGuid("guid");
        wxStoreMerchantOrderDO.setRemark("remark");
        wxStoreMerchantOrderDO.setAreaGuid("areaGuid");
        wxStoreMerchantOrderDO.setAreaName("areaName");
        wxStoreMerchantOrderDO.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDO.setStoreName("name");
        wxStoreMerchantOrderDO.setBrandName("brandName");
        wxStoreMerchantOrderDO.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDO.setTableCode("tableCode");
        wxStoreMerchantOrderDO.setOperationGuid("openId");
        wxStoreMerchantOrderDO.setOperationSource(0);
        wxStoreMerchantOrderDO.setOperationName("nickName");
        wxStoreMerchantOrderDO.setOrderRecordGuid("58d0dfce-a97a-4608-b8a7-f7f4f68b843b");
        wxStoreMerchantOrderDO.setCombine("mainOrderGuid");
        wxStoreMerchantOrderDO.setOrderSource(0);
        final List<WxStoreMerchantOrderDO> wxStoreMerchantOrderDOS = Arrays.asList(wxStoreMerchantOrderDO);
        when(mockWxStoreMerchantOrderMapstruct.getWxStoreMerchantOrder(wxStoreMerchantOrderDOS))
                .thenReturn(wxStoreMerchantOrderDTOS);

        // Run the test
        final List<WxStoreMerchantOrderDTO> result = wxStoreMerchantOrderImplUnderTest.getPendingOrders(query);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetPendingOrders_WxStoreMerchantOrderMapstructReturnsNoItems() {
        // Setup
        final WxStorePendingOrdersQuery query = WxStorePendingOrdersQuery.builder()
                .diningTableGuid("diningTableGuid")
                .orderStates(Arrays.asList(0))
                .tradeMode(0)
                .orderGuid("orderGuid")
                .combine("combine")
                .build();

        // Configure WxStoreMerchantOrderMapstruct.getWxStoreMerchantOrder(...).
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        wxStoreMerchantOrderDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDO.setTotalPrice(new BigDecimal("0.00"));
        wxStoreMerchantOrderDO.setOpenId("openId");
        wxStoreMerchantOrderDO.setNickName("nickName");
        wxStoreMerchantOrderDO.setActualGuestsNo(0);
        wxStoreMerchantOrderDO.setTradeMode(0);
        wxStoreMerchantOrderDO.setOrderState(0);
        wxStoreMerchantOrderDO.setDenialReason("denialReason");
        wxStoreMerchantOrderDO.setOrderGuid("guid");
        wxStoreMerchantOrderDO.setRemark("remark");
        wxStoreMerchantOrderDO.setAreaGuid("areaGuid");
        wxStoreMerchantOrderDO.setAreaName("areaName");
        wxStoreMerchantOrderDO.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDO.setStoreName("name");
        wxStoreMerchantOrderDO.setBrandName("brandName");
        wxStoreMerchantOrderDO.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDO.setTableCode("tableCode");
        wxStoreMerchantOrderDO.setOperationGuid("openId");
        wxStoreMerchantOrderDO.setOperationSource(0);
        wxStoreMerchantOrderDO.setOperationName("nickName");
        wxStoreMerchantOrderDO.setOrderRecordGuid("58d0dfce-a97a-4608-b8a7-f7f4f68b843b");
        wxStoreMerchantOrderDO.setCombine("mainOrderGuid");
        wxStoreMerchantOrderDO.setOrderSource(0);
        final List<WxStoreMerchantOrderDO> wxStoreMerchantOrderDOS = Arrays.asList(wxStoreMerchantOrderDO);
        when(mockWxStoreMerchantOrderMapstruct.getWxStoreMerchantOrder(wxStoreMerchantOrderDOS))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<WxStoreMerchantOrderDTO> result = wxStoreMerchantOrderImplUnderTest.getPendingOrders(query);

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testPushOrderMsg() {
        // Setup
        final WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        wxStoreMerchantOrderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setOpenId("openId");
        wxStoreMerchantOrderDTO.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDTO.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDTO.setOrderState(0);
        wxStoreMerchantOrderDTO.setTradeMode(0);
        wxStoreMerchantOrderDTO.setTradeModeName("desc");
        wxStoreMerchantOrderDTO.setMsgSource(0);
        wxStoreMerchantOrderDTO.setMsgSourceName("desc");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setUserWxPublicOpenId("openId");
        dineInItemDTO.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO.setItemGuid("itemGuid");
        dineInItemDTO.setItemType(0);
        dineInItemDTO.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO.setItemTypeName("itemTypeName");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO = new SubDineInItemDTO();
        subDineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO));
        dineInItemDTO.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO));
        final ItemAttrDTO itemAttrDTO = new ItemAttrDTO();
        itemAttrDTO.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO.setNum(0);
        dineInItemDTO.setItemAttrDTOS(Arrays.asList(itemAttrDTO));
        wxStoreMerchantOrderDTO.setDineInItemDTOList(Arrays.asList(dineInItemDTO));
        wxStoreMerchantOrderDTO.setOrderNo("guid");
        wxStoreMerchantOrderDTO.setOperationSource(0);
        wxStoreMerchantOrderDTO.setOperationSourceName("desc");
        wxStoreMerchantOrderDTO.setOrderSource(0);

        final BusinessMessageDTO expectedResult = BusinessMessageDTO.builder()
                .messageGuid("messageGuid")
                .subject("name")
                .content("content")
                .messageType(0)
                .detailMessageType(0)
                .platform("desc")
                .storeGuid("storeGuid")
                .storeName("name")
                .state("state")
                .createTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .pushTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .messageTypeStr("messageTypeStr")
                .build();
        when(mockRedisUtils.generatdDTOGuid(BusinessMessageDTO.class)).thenReturn("messageGuid");

        // Configure WxStoreSessionDetailsService.getStoreDetail(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("13b15130-a1a8-4a4f-8eeb-d1af12c961a2");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        when(mockWxStoreSessionDetailsService.getStoreDetail("storeGuid")).thenReturn(storeDTO);

        // Run the test
        final BusinessMessageDTO result = wxStoreMerchantOrderImplUnderTest.pushOrderMsg(wxStoreMerchantOrderDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockBizMsgFeignClient).msg(BusinessMessageDTO.builder()
                .messageGuid("messageGuid")
                .subject("name")
                .content("content")
                .messageType(0)
                .detailMessageType(0)
                .platform("desc")
                .storeGuid("storeGuid")
                .storeName("name")
                .state("state")
                .createTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .pushTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .messageTypeStr("messageTypeStr")
                .build());
    }

    @Test
    public void testPushFastMsg() {
        // Setup
        final WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        wxStoreMerchantOrderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setOpenId("openId");
        wxStoreMerchantOrderDTO.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDTO.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDTO.setOrderState(0);
        wxStoreMerchantOrderDTO.setTradeMode(0);
        wxStoreMerchantOrderDTO.setTradeModeName("desc");
        wxStoreMerchantOrderDTO.setMsgSource(0);
        wxStoreMerchantOrderDTO.setMsgSourceName("desc");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setUserWxPublicOpenId("openId");
        dineInItemDTO.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO.setItemGuid("itemGuid");
        dineInItemDTO.setItemType(0);
        dineInItemDTO.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO.setItemTypeName("itemTypeName");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO = new SubDineInItemDTO();
        subDineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO));
        dineInItemDTO.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO));
        final ItemAttrDTO itemAttrDTO = new ItemAttrDTO();
        itemAttrDTO.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO.setNum(0);
        dineInItemDTO.setItemAttrDTOS(Arrays.asList(itemAttrDTO));
        wxStoreMerchantOrderDTO.setDineInItemDTOList(Arrays.asList(dineInItemDTO));
        wxStoreMerchantOrderDTO.setOrderNo("guid");
        wxStoreMerchantOrderDTO.setOperationSource(0);
        wxStoreMerchantOrderDTO.setOperationSourceName("desc");
        wxStoreMerchantOrderDTO.setOrderSource(0);

        when(mockRedisUtils.generatdDTOGuid(BusinessMessageDTO.class)).thenReturn("messageGuid");

        // Configure WxStoreSessionDetailsService.getStoreDetail(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("13b15130-a1a8-4a4f-8eeb-d1af12c961a2");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        when(mockWxStoreSessionDetailsService.getStoreDetail("storeGuid")).thenReturn(storeDTO);

        // Run the test
        wxStoreMerchantOrderImplUnderTest.pushFastMsg(wxStoreMerchantOrderDTO);

        // Verify the results
        verify(mockBizMsgFeignClient).msg(BusinessMessageDTO.builder()
                .messageGuid("messageGuid")
                .subject("name")
                .content("content")
                .messageType(0)
                .detailMessageType(0)
                .platform("desc")
                .storeGuid("storeGuid")
                .storeName("name")
                .state("state")
                .createTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .pushTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .messageTypeStr("messageTypeStr")
                .build());
    }

    @Test
    public void testCombine() {
        assertFalse(wxStoreMerchantOrderImplUnderTest.combine(Arrays.asList("value"), "mainOrderGuid"));
    }

    @Test
    public void testSeparate() {
        // Setup
        final TableInfoDTO tableInfoDTO = new TableInfoDTO();
        tableInfoDTO.setDeviceType(0);
        tableInfoDTO.setDeviceId("openId");
        tableInfoDTO.setEnterpriseGuid("enterpriseGuid");
        tableInfoDTO.setEnterpriseName("enterpriseName");
        tableInfoDTO.setStoreGuid("storeGuid");
        tableInfoDTO.setStoreName("name");
        tableInfoDTO.setUserGuid("openId");
        tableInfoDTO.setUserName("nickName");
        tableInfoDTO.setTableGuid("tableGuid");
        tableInfoDTO.setOrderGuid("guid");
        final TableOrderCombineDTO tableOrderCombineDTO = new TableOrderCombineDTO(Arrays.asList(tableInfoDTO),
                "mainOrderGuid", "mainTableGuid", true);
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        wxStoreMerchantOrderDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDO.setTotalPrice(new BigDecimal("0.00"));
        wxStoreMerchantOrderDO.setOpenId("openId");
        wxStoreMerchantOrderDO.setNickName("nickName");
        wxStoreMerchantOrderDO.setActualGuestsNo(0);
        wxStoreMerchantOrderDO.setTradeMode(0);
        wxStoreMerchantOrderDO.setOrderState(0);
        wxStoreMerchantOrderDO.setDenialReason("denialReason");
        wxStoreMerchantOrderDO.setOrderGuid("guid");
        wxStoreMerchantOrderDO.setRemark("remark");
        wxStoreMerchantOrderDO.setAreaGuid("areaGuid");
        wxStoreMerchantOrderDO.setAreaName("areaName");
        wxStoreMerchantOrderDO.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDO.setStoreName("name");
        wxStoreMerchantOrderDO.setBrandName("brandName");
        wxStoreMerchantOrderDO.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDO.setTableCode("tableCode");
        wxStoreMerchantOrderDO.setOperationGuid("openId");
        wxStoreMerchantOrderDO.setOperationSource(0);
        wxStoreMerchantOrderDO.setOperationName("nickName");
        wxStoreMerchantOrderDO.setOrderRecordGuid("58d0dfce-a97a-4608-b8a7-f7f4f68b843b");
        wxStoreMerchantOrderDO.setCombine("mainOrderGuid");
        wxStoreMerchantOrderDO.setOrderSource(0);
        final List<WxStoreMerchantOrderDO> expectedResult = Arrays.asList(wxStoreMerchantOrderDO);

        // Run the test
        final List<WxStoreMerchantOrderDO> result = wxStoreMerchantOrderImplUnderTest.separate(tableOrderCombineDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testTurn() {
        // Setup
        final TurnTableDTO turnTableDTO = new TurnTableDTO();
        turnTableDTO.setDeviceType(0);
        turnTableDTO.setDeviceId("openId");
        turnTableDTO.setEnterpriseGuid("enterpriseGuid");
        turnTableDTO.setEnterpriseName("enterpriseName");
        turnTableDTO.setStoreGuid("storeGuid");
        turnTableDTO.setStoreName("name");
        turnTableDTO.setUserGuid("openId");
        turnTableDTO.setUserName("nickName");
        turnTableDTO.setOriginTableGuid("tableGuid");
        turnTableDTO.setNewTableAreaName("areaName");
        turnTableDTO.setNewTableAreaGuid("areaGuid");
        turnTableDTO.setNewTableGuid("diningTableGuid");
        turnTableDTO.setNewTableCode("tableCode");

        when(mockWxStoreSessionDetailsService.getMerchantBatchGuid("diningTableGuid")).thenReturn("result");

        // Configure WxStoreTableClientService.tableList(...).
        final List<WxStoreTableCombineDTO> wxStoreTableCombineDTOS = Arrays.asList(WxStoreTableCombineDTO.builder()
                .mainOrderGuid("mainOrderGuid")
                .build());
        when(mockWxStoreTableClientService.tableList("tableGuid")).thenReturn(wxStoreTableCombineDTOS);

        // Run the test
        wxStoreMerchantOrderImplUnderTest.turn(turnTableDTO);

        // Verify the results
    }

    @Test
    public void testTurn_WxStoreTableClientServiceReturnsNoItems() {
        // Setup
        final TurnTableDTO turnTableDTO = new TurnTableDTO();
        turnTableDTO.setDeviceType(0);
        turnTableDTO.setDeviceId("openId");
        turnTableDTO.setEnterpriseGuid("enterpriseGuid");
        turnTableDTO.setEnterpriseName("enterpriseName");
        turnTableDTO.setStoreGuid("storeGuid");
        turnTableDTO.setStoreName("name");
        turnTableDTO.setUserGuid("openId");
        turnTableDTO.setUserName("nickName");
        turnTableDTO.setOriginTableGuid("tableGuid");
        turnTableDTO.setNewTableAreaName("areaName");
        turnTableDTO.setNewTableAreaGuid("areaGuid");
        turnTableDTO.setNewTableGuid("diningTableGuid");
        turnTableDTO.setNewTableCode("tableCode");

        when(mockWxStoreSessionDetailsService.getMerchantBatchGuid("diningTableGuid")).thenReturn("result");
        when(mockWxStoreTableClientService.tableList("tableGuid")).thenReturn(Collections.emptyList());

        // Run the test
        wxStoreMerchantOrderImplUnderTest.turn(turnTableDTO);

        // Verify the results
    }

    @Test
    public void testUpdateOrderState() {
        // Setup
        when(mockWxStoreSessionDetailsService.getMerchantBatchGuid("tableGuid")).thenReturn("result");

        // Run the test
        wxStoreMerchantOrderImplUnderTest.updateOrderState("tableGuid", 0);

        // Verify the results
    }

    @Test
    public void testGetOneByGuid() {
        // Setup
        final WxStoreMerchantOrderDO expectedResult = new WxStoreMerchantOrderDO();
        expectedResult.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        expectedResult.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setTotalPrice(new BigDecimal("0.00"));
        expectedResult.setOpenId("openId");
        expectedResult.setNickName("nickName");
        expectedResult.setActualGuestsNo(0);
        expectedResult.setTradeMode(0);
        expectedResult.setOrderState(0);
        expectedResult.setDenialReason("denialReason");
        expectedResult.setOrderGuid("guid");
        expectedResult.setRemark("remark");
        expectedResult.setAreaGuid("areaGuid");
        expectedResult.setAreaName("areaName");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setStoreName("name");
        expectedResult.setBrandName("brandName");
        expectedResult.setDiningTableGuid("diningTableGuid");
        expectedResult.setTableCode("tableCode");
        expectedResult.setOperationGuid("openId");
        expectedResult.setOperationSource(0);
        expectedResult.setOperationName("nickName");
        expectedResult.setOrderRecordGuid("58d0dfce-a97a-4608-b8a7-f7f4f68b843b");
        expectedResult.setCombine("mainOrderGuid");
        expectedResult.setOrderSource(0);

        // Run the test
        final WxStoreMerchantOrderDO result = wxStoreMerchantOrderImplUnderTest.getOneByGuid("merchantGuid");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testTableListByMerchantGuidOrOrderGuid() {
        // Setup
        final WxStorePendingOrdersQuery query = WxStorePendingOrdersQuery.builder()
                .diningTableGuid("diningTableGuid")
                .orderStates(Arrays.asList(0))
                .tradeMode(0)
                .orderGuid("orderGuid")
                .combine("combine")
                .build();
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        wxStoreMerchantOrderDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDO.setTotalPrice(new BigDecimal("0.00"));
        wxStoreMerchantOrderDO.setOpenId("openId");
        wxStoreMerchantOrderDO.setNickName("nickName");
        wxStoreMerchantOrderDO.setActualGuestsNo(0);
        wxStoreMerchantOrderDO.setTradeMode(0);
        wxStoreMerchantOrderDO.setOrderState(0);
        wxStoreMerchantOrderDO.setDenialReason("denialReason");
        wxStoreMerchantOrderDO.setOrderGuid("guid");
        wxStoreMerchantOrderDO.setRemark("remark");
        wxStoreMerchantOrderDO.setAreaGuid("areaGuid");
        wxStoreMerchantOrderDO.setAreaName("areaName");
        wxStoreMerchantOrderDO.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDO.setStoreName("name");
        wxStoreMerchantOrderDO.setBrandName("brandName");
        wxStoreMerchantOrderDO.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDO.setTableCode("tableCode");
        wxStoreMerchantOrderDO.setOperationGuid("openId");
        wxStoreMerchantOrderDO.setOperationSource(0);
        wxStoreMerchantOrderDO.setOperationName("nickName");
        wxStoreMerchantOrderDO.setOrderRecordGuid("58d0dfce-a97a-4608-b8a7-f7f4f68b843b");
        wxStoreMerchantOrderDO.setCombine("mainOrderGuid");
        wxStoreMerchantOrderDO.setOrderSource(0);
        final List<WxStoreMerchantOrderDO> expectedResult = Arrays.asList(wxStoreMerchantOrderDO);

        // Run the test
        final List<WxStoreMerchantOrderDO> result = wxStoreMerchantOrderImplUnderTest.tableListByMerchantGuidOrOrderGuid(
                query);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testCurrentTimePendingOrderList() {
        // Setup
        final WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        wxStoreMerchantOrderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setOpenId("openId");
        wxStoreMerchantOrderDTO.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDTO.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDTO.setOrderState(0);
        wxStoreMerchantOrderDTO.setTradeMode(0);
        wxStoreMerchantOrderDTO.setTradeModeName("desc");
        wxStoreMerchantOrderDTO.setMsgSource(0);
        wxStoreMerchantOrderDTO.setMsgSourceName("desc");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setUserWxPublicOpenId("openId");
        dineInItemDTO.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO.setItemGuid("itemGuid");
        dineInItemDTO.setItemType(0);
        dineInItemDTO.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO.setItemTypeName("itemTypeName");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO = new SubDineInItemDTO();
        subDineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO));
        dineInItemDTO.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO));
        final ItemAttrDTO itemAttrDTO = new ItemAttrDTO();
        itemAttrDTO.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO.setNum(0);
        dineInItemDTO.setItemAttrDTOS(Arrays.asList(itemAttrDTO));
        wxStoreMerchantOrderDTO.setDineInItemDTOList(Arrays.asList(dineInItemDTO));
        wxStoreMerchantOrderDTO.setOrderNo("guid");
        wxStoreMerchantOrderDTO.setOperationSource(0);
        wxStoreMerchantOrderDTO.setOperationSourceName("desc");
        wxStoreMerchantOrderDTO.setOrderSource(0);
        final List<WxStoreMerchantOrderDTO> expectedResult = Arrays.asList(wxStoreMerchantOrderDTO);

        // Configure WxStoreMerchantOrderMapstruct.getWxStoreMerchantOrder(...).
        final WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO1 = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO1.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        wxStoreMerchantOrderDTO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO1.setOpenId("openId");
        wxStoreMerchantOrderDTO1.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDTO1.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDTO1.setOrderState(0);
        wxStoreMerchantOrderDTO1.setTradeMode(0);
        wxStoreMerchantOrderDTO1.setTradeModeName("desc");
        wxStoreMerchantOrderDTO1.setMsgSource(0);
        wxStoreMerchantOrderDTO1.setMsgSourceName("desc");
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setUserWxPublicOpenId("openId");
        dineInItemDTO1.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO1.setItemGuid("itemGuid");
        dineInItemDTO1.setItemType(0);
        dineInItemDTO1.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO1.setItemTypeName("itemTypeName");
        dineInItemDTO1.setPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO1.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO1 = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO1 = new SubDineInItemDTO();
        subDineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO1.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO1.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO1));
        dineInItemDTO1.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO1));
        final ItemAttrDTO itemAttrDTO1 = new ItemAttrDTO();
        itemAttrDTO1.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO1.setNum(0);
        dineInItemDTO1.setItemAttrDTOS(Arrays.asList(itemAttrDTO1));
        wxStoreMerchantOrderDTO1.setDineInItemDTOList(Arrays.asList(dineInItemDTO1));
        wxStoreMerchantOrderDTO1.setOrderNo("guid");
        wxStoreMerchantOrderDTO1.setOperationSource(0);
        wxStoreMerchantOrderDTO1.setOperationSourceName("desc");
        wxStoreMerchantOrderDTO1.setOrderSource(0);
        final List<WxStoreMerchantOrderDTO> wxStoreMerchantOrderDTOS = Arrays.asList(wxStoreMerchantOrderDTO1);
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        wxStoreMerchantOrderDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDO.setTotalPrice(new BigDecimal("0.00"));
        wxStoreMerchantOrderDO.setOpenId("openId");
        wxStoreMerchantOrderDO.setNickName("nickName");
        wxStoreMerchantOrderDO.setActualGuestsNo(0);
        wxStoreMerchantOrderDO.setTradeMode(0);
        wxStoreMerchantOrderDO.setOrderState(0);
        wxStoreMerchantOrderDO.setDenialReason("denialReason");
        wxStoreMerchantOrderDO.setOrderGuid("guid");
        wxStoreMerchantOrderDO.setRemark("remark");
        wxStoreMerchantOrderDO.setAreaGuid("areaGuid");
        wxStoreMerchantOrderDO.setAreaName("areaName");
        wxStoreMerchantOrderDO.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDO.setStoreName("name");
        wxStoreMerchantOrderDO.setBrandName("brandName");
        wxStoreMerchantOrderDO.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDO.setTableCode("tableCode");
        wxStoreMerchantOrderDO.setOperationGuid("openId");
        wxStoreMerchantOrderDO.setOperationSource(0);
        wxStoreMerchantOrderDO.setOperationName("nickName");
        wxStoreMerchantOrderDO.setOrderRecordGuid("58d0dfce-a97a-4608-b8a7-f7f4f68b843b");
        wxStoreMerchantOrderDO.setCombine("mainOrderGuid");
        wxStoreMerchantOrderDO.setOrderSource(0);
        final List<WxStoreMerchantOrderDO> wxStoreMerchantOrderDOS = Arrays.asList(wxStoreMerchantOrderDO);
        when(mockWxStoreMerchantOrderMapstruct.getWxStoreMerchantOrder(wxStoreMerchantOrderDOS))
                .thenReturn(wxStoreMerchantOrderDTOS);

        // Run the test
        final List<WxStoreMerchantOrderDTO> result = wxStoreMerchantOrderImplUnderTest.currentTimePendingOrderList(
                "openId");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testCurrentTimePendingOrderList_WxStoreMerchantOrderMapstructReturnsNoItems() {
        // Setup
        // Configure WxStoreMerchantOrderMapstruct.getWxStoreMerchantOrder(...).
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        wxStoreMerchantOrderDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDO.setTotalPrice(new BigDecimal("0.00"));
        wxStoreMerchantOrderDO.setOpenId("openId");
        wxStoreMerchantOrderDO.setNickName("nickName");
        wxStoreMerchantOrderDO.setActualGuestsNo(0);
        wxStoreMerchantOrderDO.setTradeMode(0);
        wxStoreMerchantOrderDO.setOrderState(0);
        wxStoreMerchantOrderDO.setDenialReason("denialReason");
        wxStoreMerchantOrderDO.setOrderGuid("guid");
        wxStoreMerchantOrderDO.setRemark("remark");
        wxStoreMerchantOrderDO.setAreaGuid("areaGuid");
        wxStoreMerchantOrderDO.setAreaName("areaName");
        wxStoreMerchantOrderDO.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDO.setStoreName("name");
        wxStoreMerchantOrderDO.setBrandName("brandName");
        wxStoreMerchantOrderDO.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDO.setTableCode("tableCode");
        wxStoreMerchantOrderDO.setOperationGuid("openId");
        wxStoreMerchantOrderDO.setOperationSource(0);
        wxStoreMerchantOrderDO.setOperationName("nickName");
        wxStoreMerchantOrderDO.setOrderRecordGuid("58d0dfce-a97a-4608-b8a7-f7f4f68b843b");
        wxStoreMerchantOrderDO.setCombine("mainOrderGuid");
        wxStoreMerchantOrderDO.setOrderSource(0);
        final List<WxStoreMerchantOrderDO> wxStoreMerchantOrderDOS = Arrays.asList(wxStoreMerchantOrderDO);
        when(mockWxStoreMerchantOrderMapstruct.getWxStoreMerchantOrder(wxStoreMerchantOrderDOS))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<WxStoreMerchantOrderDTO> result = wxStoreMerchantOrderImplUnderTest.currentTimePendingOrderList(
                "openId");

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testUpdateOutDateOrder() {
        // Setup
        // Configure WxStoreMerchantDineInItemService.getWxStoreMerchantDineInItem(...).
        final WxStoreMerchantDineInItemDTO wxStoreMerchantDineInItemDTO = new WxStoreMerchantDineInItemDTO();
        wxStoreMerchantDineInItemDTO.setGuid("67caa390-0069-4051-b6a8-600aaf0a8501");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setGuid("f21bea36-c6bf-4fce-98fd-1a06b61b8ae0");
        dineInItemDTO.setOrderGuid("orderGuid");
        dineInItemDTO.setOriginalOrderItemGuid(0L);
        wxStoreMerchantDineInItemDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        when(mockWxStoreMerchantDineInItemService.getWxStoreMerchantDineInItem(
                "aca475ce-291d-445a-8665-84db3c769401")).thenReturn(wxStoreMerchantDineInItemDTO);

        // Run the test
        wxStoreMerchantOrderImplUnderTest.updateOutDateOrder("openId");

        // Verify the results
    }

    @Test
    public void testUpdateOutDateOrder_WxStoreMerchantDineInItemServiceReturnsNull() {
        // Setup
        when(mockWxStoreMerchantDineInItemService.getWxStoreMerchantDineInItem(
                "aca475ce-291d-445a-8665-84db3c769401")).thenReturn(null);

        // Run the test
        wxStoreMerchantOrderImplUnderTest.updateOutDateOrder("openId");

        // Verify the results
    }

    @Test
    public void testSelectByOrderRecordGuid() {
        // Setup
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        wxStoreMerchantOrderDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDO.setTotalPrice(new BigDecimal("0.00"));
        wxStoreMerchantOrderDO.setOpenId("openId");
        wxStoreMerchantOrderDO.setNickName("nickName");
        wxStoreMerchantOrderDO.setActualGuestsNo(0);
        wxStoreMerchantOrderDO.setTradeMode(0);
        wxStoreMerchantOrderDO.setOrderState(0);
        wxStoreMerchantOrderDO.setDenialReason("denialReason");
        wxStoreMerchantOrderDO.setOrderGuid("guid");
        wxStoreMerchantOrderDO.setRemark("remark");
        wxStoreMerchantOrderDO.setAreaGuid("areaGuid");
        wxStoreMerchantOrderDO.setAreaName("areaName");
        wxStoreMerchantOrderDO.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDO.setStoreName("name");
        wxStoreMerchantOrderDO.setBrandName("brandName");
        wxStoreMerchantOrderDO.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDO.setTableCode("tableCode");
        wxStoreMerchantOrderDO.setOperationGuid("openId");
        wxStoreMerchantOrderDO.setOperationSource(0);
        wxStoreMerchantOrderDO.setOperationName("nickName");
        wxStoreMerchantOrderDO.setOrderRecordGuid("58d0dfce-a97a-4608-b8a7-f7f4f68b843b");
        wxStoreMerchantOrderDO.setCombine("mainOrderGuid");
        wxStoreMerchantOrderDO.setOrderSource(0);
        final List<WxStoreMerchantOrderDO> expectedResult = Arrays.asList(wxStoreMerchantOrderDO);

        // Configure WxStoreMerchantOrderMapper.selectByOrderRecordGuid(...).
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO1 = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO1.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        wxStoreMerchantOrderDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDO1.setTotalPrice(new BigDecimal("0.00"));
        wxStoreMerchantOrderDO1.setOpenId("openId");
        wxStoreMerchantOrderDO1.setNickName("nickName");
        wxStoreMerchantOrderDO1.setActualGuestsNo(0);
        wxStoreMerchantOrderDO1.setTradeMode(0);
        wxStoreMerchantOrderDO1.setOrderState(0);
        wxStoreMerchantOrderDO1.setDenialReason("denialReason");
        wxStoreMerchantOrderDO1.setOrderGuid("guid");
        wxStoreMerchantOrderDO1.setRemark("remark");
        wxStoreMerchantOrderDO1.setAreaGuid("areaGuid");
        wxStoreMerchantOrderDO1.setAreaName("areaName");
        wxStoreMerchantOrderDO1.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDO1.setStoreName("name");
        wxStoreMerchantOrderDO1.setBrandName("brandName");
        wxStoreMerchantOrderDO1.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDO1.setTableCode("tableCode");
        wxStoreMerchantOrderDO1.setOperationGuid("openId");
        wxStoreMerchantOrderDO1.setOperationSource(0);
        wxStoreMerchantOrderDO1.setOperationName("nickName");
        wxStoreMerchantOrderDO1.setOrderRecordGuid("58d0dfce-a97a-4608-b8a7-f7f4f68b843b");
        wxStoreMerchantOrderDO1.setCombine("mainOrderGuid");
        wxStoreMerchantOrderDO1.setOrderSource(0);
        final List<WxStoreMerchantOrderDO> wxStoreMerchantOrderDOS = Arrays.asList(wxStoreMerchantOrderDO1);
        when(mockWxStoreMerchantOrderMapper.selectByOrderRecordGuid("orderRecordGuid",
                Arrays.asList("value"))).thenReturn(wxStoreMerchantOrderDOS);

        // Run the test
        final List<WxStoreMerchantOrderDO> result = wxStoreMerchantOrderImplUnderTest.selectByOrderRecordGuid(
                "orderRecordGuid", Arrays.asList("value"));

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testSelectByOrderRecordGuid_WxStoreMerchantOrderMapperReturnsNoItems() {
        // Setup
        when(mockWxStoreMerchantOrderMapper.selectByOrderRecordGuid("orderRecordGuid",
                Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        final List<WxStoreMerchantOrderDO> result = wxStoreMerchantOrderImplUnderTest.selectByOrderRecordGuid(
                "orderRecordGuid", Arrays.asList("value"));

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testCountByStatus() {
        // Setup
        when(mockWxStoreMerchantOrderMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(0);

        // Run the test
        final int result = wxStoreMerchantOrderImplUnderTest.countByStatus("orderRecordGuid", 0, 0);

        // Verify the results
        assertEquals(0, result);
    }

    @Test
    public void testDealUnFinishedOrders() {
        // Setup
        // Run the test
        wxStoreMerchantOrderImplUnderTest.dealUnFinishedOrders("orderGuid");

        // Verify the results
    }

    @Test
    public void testFirstSubmit() {
        // Setup
        final WxStoreMerchantOrderDO expectedResult = new WxStoreMerchantOrderDO();
        expectedResult.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        expectedResult.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setTotalPrice(new BigDecimal("0.00"));
        expectedResult.setOpenId("openId");
        expectedResult.setNickName("nickName");
        expectedResult.setActualGuestsNo(0);
        expectedResult.setTradeMode(0);
        expectedResult.setOrderState(0);
        expectedResult.setDenialReason("denialReason");
        expectedResult.setOrderGuid("guid");
        expectedResult.setRemark("remark");
        expectedResult.setAreaGuid("areaGuid");
        expectedResult.setAreaName("areaName");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setStoreName("name");
        expectedResult.setBrandName("brandName");
        expectedResult.setDiningTableGuid("diningTableGuid");
        expectedResult.setTableCode("tableCode");
        expectedResult.setOperationGuid("openId");
        expectedResult.setOperationSource(0);
        expectedResult.setOperationName("nickName");
        expectedResult.setOrderRecordGuid("58d0dfce-a97a-4608-b8a7-f7f4f68b843b");
        expectedResult.setCombine("mainOrderGuid");
        expectedResult.setOrderSource(0);

        // Run the test
        final WxStoreMerchantOrderDO result = wxStoreMerchantOrderImplUnderTest.firstSubmit("orderRecordGuid");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testUpdateRemarkById() {
        // Setup
        // Configure WxStoreMerchantOrderMapper.selectOne(...).
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        wxStoreMerchantOrderDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDO.setTotalPrice(new BigDecimal("0.00"));
        wxStoreMerchantOrderDO.setOpenId("openId");
        wxStoreMerchantOrderDO.setNickName("nickName");
        wxStoreMerchantOrderDO.setActualGuestsNo(0);
        wxStoreMerchantOrderDO.setTradeMode(0);
        wxStoreMerchantOrderDO.setOrderState(0);
        wxStoreMerchantOrderDO.setDenialReason("denialReason");
        wxStoreMerchantOrderDO.setOrderGuid("guid");
        wxStoreMerchantOrderDO.setRemark("remark");
        wxStoreMerchantOrderDO.setAreaGuid("areaGuid");
        wxStoreMerchantOrderDO.setAreaName("areaName");
        wxStoreMerchantOrderDO.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDO.setStoreName("name");
        wxStoreMerchantOrderDO.setBrandName("brandName");
        wxStoreMerchantOrderDO.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDO.setTableCode("tableCode");
        wxStoreMerchantOrderDO.setOperationGuid("openId");
        wxStoreMerchantOrderDO.setOperationSource(0);
        wxStoreMerchantOrderDO.setOperationName("nickName");
        wxStoreMerchantOrderDO.setOrderRecordGuid("58d0dfce-a97a-4608-b8a7-f7f4f68b843b");
        wxStoreMerchantOrderDO.setCombine("mainOrderGuid");
        wxStoreMerchantOrderDO.setOrderSource(0);
        when(mockWxStoreMerchantOrderMapper.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(wxStoreMerchantOrderDO);

        // Configure WxStoreMerchantOrderMapper.updateById(...).
        final WxStoreMerchantOrderDO entity = new WxStoreMerchantOrderDO();
        entity.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        entity.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setTotalPrice(new BigDecimal("0.00"));
        entity.setOpenId("openId");
        entity.setNickName("nickName");
        entity.setActualGuestsNo(0);
        entity.setTradeMode(0);
        entity.setOrderState(0);
        entity.setDenialReason("denialReason");
        entity.setOrderGuid("guid");
        entity.setRemark("remark");
        entity.setAreaGuid("areaGuid");
        entity.setAreaName("areaName");
        entity.setStoreGuid("storeGuid");
        entity.setStoreName("name");
        entity.setBrandName("brandName");
        entity.setDiningTableGuid("diningTableGuid");
        entity.setTableCode("tableCode");
        entity.setOperationGuid("openId");
        entity.setOperationSource(0);
        entity.setOperationName("nickName");
        entity.setOrderRecordGuid("58d0dfce-a97a-4608-b8a7-f7f4f68b843b");
        entity.setCombine("mainOrderGuid");
        entity.setOrderSource(0);
        when(mockWxStoreMerchantOrderMapper.updateById(entity)).thenReturn(0);

        // Configure WxOrderRecordService.getOne(...).
        final WxOrderRecordDO wxOrderRecordDO = new WxOrderRecordDO(0L, "58d0dfce-a97a-4608-b8a7-f7f4f68b843b",
                "orderGuid", "merchantGuid", "guid", "brandGuid", "brandName", "logUrl", "storeGuid", "storeName",
                "areaGuid", "areaName", "tableGuid", "tableCode", 0, 0, "orderStateName", new BigDecimal("0.00"),
                new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName", "orderHolderNo",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0);
        when(mockWxOrderRecordService.getOne(any(LambdaQueryWrapper.class))).thenReturn(wxOrderRecordDO);

        // Run the test
        wxStoreMerchantOrderImplUnderTest.updateRemarkById("205a7499-9628-40ec-aee0-491c42b91f13", "remark");

        // Verify the results
        verify(mockWxStoreDineInOrderClientService).updateOrderRemarkById("orderGuid", "remark");
    }

    @Test
    public void testUpdateItemRemarkById() {
        // Setup
        // Configure OrderItemService.getOne(...).
        final WxOrderItemDO wxOrderItemDO = new WxOrderItemDO();
        wxOrderItemDO.setMerchantGuid("merchantGuid");
        wxOrderItemDO.setOrderRecordGuid("orderRecordGuid");
        wxOrderItemDO.setItemGuid("itemGuid");
        wxOrderItemDO.setItemName("itemName");
        wxOrderItemDO.setMemberPrice(new BigDecimal("0.00"));
        wxOrderItemDO.setOriginalPrice(new BigDecimal("0.00"));
        wxOrderItemDO.setRemark("remark");
        when(mockOrderItemService.getOne(any(LambdaQueryWrapper.class))).thenReturn(wxOrderItemDO);

        // Configure WxOrderRecordService.getOne(...).
        final WxOrderRecordDO wxOrderRecordDO = new WxOrderRecordDO(0L, "58d0dfce-a97a-4608-b8a7-f7f4f68b843b",
                "orderGuid", "merchantGuid", "guid", "brandGuid", "brandName", "logUrl", "storeGuid", "storeName",
                "areaGuid", "areaName", "tableGuid", "tableCode", 0, 0, "orderStateName", new BigDecimal("0.00"),
                new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName", "orderHolderNo",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0);
        when(mockWxOrderRecordService.getOne(any(LambdaQueryWrapper.class))).thenReturn(wxOrderRecordDO);

        // Run the test
        wxStoreMerchantOrderImplUnderTest.updateItemRemarkById("orderGuid", "itemGuid", "remark");

        // Verify the results
        // Confirm WxOrderItemMapper.updateById(...).
        final WxOrderItemDO entity = new WxOrderItemDO();
        entity.setMerchantGuid("merchantGuid");
        entity.setOrderRecordGuid("orderRecordGuid");
        entity.setItemGuid("itemGuid");
        entity.setItemName("itemName");
        entity.setMemberPrice(new BigDecimal("0.00"));
        entity.setOriginalPrice(new BigDecimal("0.00"));
        entity.setRemark("remark");
        verify(mockWxOrderItemMapper).updateById(entity);
        verify(mockWxStoreDineInOrderClientService).updateOrderItemRemarkById("orderGuid", "itemGuid", "remark");
    }

    @Test
    public void testGetDetailByOrderRecordGuid() {
        // Setup
        final WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        wxStoreMerchantOrderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setOpenId("openId");
        wxStoreMerchantOrderDTO.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDTO.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDTO.setOrderState(0);
        wxStoreMerchantOrderDTO.setTradeMode(0);
        wxStoreMerchantOrderDTO.setTradeModeName("desc");
        wxStoreMerchantOrderDTO.setMsgSource(0);
        wxStoreMerchantOrderDTO.setMsgSourceName("desc");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setUserWxPublicOpenId("openId");
        dineInItemDTO.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO.setItemGuid("itemGuid");
        dineInItemDTO.setItemType(0);
        dineInItemDTO.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO.setItemTypeName("itemTypeName");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO = new SubDineInItemDTO();
        subDineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO));
        dineInItemDTO.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO));
        final ItemAttrDTO itemAttrDTO = new ItemAttrDTO();
        itemAttrDTO.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO.setNum(0);
        dineInItemDTO.setItemAttrDTOS(Arrays.asList(itemAttrDTO));
        wxStoreMerchantOrderDTO.setDineInItemDTOList(Arrays.asList(dineInItemDTO));
        wxStoreMerchantOrderDTO.setOrderNo("guid");
        wxStoreMerchantOrderDTO.setOperationSource(0);
        wxStoreMerchantOrderDTO.setOperationSourceName("desc");
        wxStoreMerchantOrderDTO.setOrderSource(0);
        final List<WxStoreMerchantOrderDTO> expectedResult = Arrays.asList(wxStoreMerchantOrderDTO);

        // Configure WxStoreMerchantOrderMapstruct.getWxStoreMerchantOrder(...).
        final WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO1 = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO1.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        wxStoreMerchantOrderDTO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO1.setOpenId("openId");
        wxStoreMerchantOrderDTO1.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDTO1.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDTO1.setOrderState(0);
        wxStoreMerchantOrderDTO1.setTradeMode(0);
        wxStoreMerchantOrderDTO1.setTradeModeName("desc");
        wxStoreMerchantOrderDTO1.setMsgSource(0);
        wxStoreMerchantOrderDTO1.setMsgSourceName("desc");
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setUserWxPublicOpenId("openId");
        dineInItemDTO1.setWxBatch("aca475ce-291d-445a-8665-84db3c769401");
        dineInItemDTO1.setItemGuid("itemGuid");
        dineInItemDTO1.setItemType(0);
        dineInItemDTO1.setItemTypeGuid("itemTypeGuid");
        dineInItemDTO1.setItemTypeName("itemTypeName");
        dineInItemDTO1.setPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO1.setRemark("remark");
        final PackageSubgroupDTO packageSubgroupDTO1 = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO1 = new SubDineInItemDTO();
        subDineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO1.setAddPrice(new BigDecimal("0.00"));
        packageSubgroupDTO1.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO1));
        dineInItemDTO1.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO1));
        final ItemAttrDTO itemAttrDTO1 = new ItemAttrDTO();
        itemAttrDTO1.setAttrPrice(new BigDecimal("0.00"));
        itemAttrDTO1.setNum(0);
        dineInItemDTO1.setItemAttrDTOS(Arrays.asList(itemAttrDTO1));
        wxStoreMerchantOrderDTO1.setDineInItemDTOList(Arrays.asList(dineInItemDTO1));
        wxStoreMerchantOrderDTO1.setOrderNo("guid");
        wxStoreMerchantOrderDTO1.setOperationSource(0);
        wxStoreMerchantOrderDTO1.setOperationSourceName("desc");
        wxStoreMerchantOrderDTO1.setOrderSource(0);
        final List<WxStoreMerchantOrderDTO> wxStoreMerchantOrderDTOS = Arrays.asList(wxStoreMerchantOrderDTO1);
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        wxStoreMerchantOrderDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDO.setTotalPrice(new BigDecimal("0.00"));
        wxStoreMerchantOrderDO.setOpenId("openId");
        wxStoreMerchantOrderDO.setNickName("nickName");
        wxStoreMerchantOrderDO.setActualGuestsNo(0);
        wxStoreMerchantOrderDO.setTradeMode(0);
        wxStoreMerchantOrderDO.setOrderState(0);
        wxStoreMerchantOrderDO.setDenialReason("denialReason");
        wxStoreMerchantOrderDO.setOrderGuid("guid");
        wxStoreMerchantOrderDO.setRemark("remark");
        wxStoreMerchantOrderDO.setAreaGuid("areaGuid");
        wxStoreMerchantOrderDO.setAreaName("areaName");
        wxStoreMerchantOrderDO.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDO.setStoreName("name");
        wxStoreMerchantOrderDO.setBrandName("brandName");
        wxStoreMerchantOrderDO.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDO.setTableCode("tableCode");
        wxStoreMerchantOrderDO.setOperationGuid("openId");
        wxStoreMerchantOrderDO.setOperationSource(0);
        wxStoreMerchantOrderDO.setOperationName("nickName");
        wxStoreMerchantOrderDO.setOrderRecordGuid("58d0dfce-a97a-4608-b8a7-f7f4f68b843b");
        wxStoreMerchantOrderDO.setCombine("mainOrderGuid");
        wxStoreMerchantOrderDO.setOrderSource(0);
        final List<WxStoreMerchantOrderDO> wxStoreMerchantOrderDOS = Arrays.asList(wxStoreMerchantOrderDO);
        when(mockWxStoreMerchantOrderMapstruct.getWxStoreMerchantOrder(wxStoreMerchantOrderDOS))
                .thenReturn(wxStoreMerchantOrderDTOS);

        // Run the test
        final List<WxStoreMerchantOrderDTO> result = wxStoreMerchantOrderImplUnderTest.getDetailByOrderRecordGuid(
                "orderRecordGuid");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetDetailByOrderRecordGuid_WxStoreMerchantOrderMapstructReturnsNoItems() {
        // Setup
        // Configure WxStoreMerchantOrderMapstruct.getWxStoreMerchantOrder(...).
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO.setGuid("aca475ce-291d-445a-8665-84db3c769401");
        wxStoreMerchantOrderDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDO.setTotalPrice(new BigDecimal("0.00"));
        wxStoreMerchantOrderDO.setOpenId("openId");
        wxStoreMerchantOrderDO.setNickName("nickName");
        wxStoreMerchantOrderDO.setActualGuestsNo(0);
        wxStoreMerchantOrderDO.setTradeMode(0);
        wxStoreMerchantOrderDO.setOrderState(0);
        wxStoreMerchantOrderDO.setDenialReason("denialReason");
        wxStoreMerchantOrderDO.setOrderGuid("guid");
        wxStoreMerchantOrderDO.setRemark("remark");
        wxStoreMerchantOrderDO.setAreaGuid("areaGuid");
        wxStoreMerchantOrderDO.setAreaName("areaName");
        wxStoreMerchantOrderDO.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDO.setStoreName("name");
        wxStoreMerchantOrderDO.setBrandName("brandName");
        wxStoreMerchantOrderDO.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDO.setTableCode("tableCode");
        wxStoreMerchantOrderDO.setOperationGuid("openId");
        wxStoreMerchantOrderDO.setOperationSource(0);
        wxStoreMerchantOrderDO.setOperationName("nickName");
        wxStoreMerchantOrderDO.setOrderRecordGuid("58d0dfce-a97a-4608-b8a7-f7f4f68b843b");
        wxStoreMerchantOrderDO.setCombine("mainOrderGuid");
        wxStoreMerchantOrderDO.setOrderSource(0);
        final List<WxStoreMerchantOrderDO> wxStoreMerchantOrderDOS = Arrays.asList(wxStoreMerchantOrderDO);
        when(mockWxStoreMerchantOrderMapstruct.getWxStoreMerchantOrder(wxStoreMerchantOrderDOS))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<WxStoreMerchantOrderDTO> result = wxStoreMerchantOrderImplUnderTest.getDetailByOrderRecordGuid(
                "orderRecordGuid");

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testGetWechatOrderFeeByOrderGuid() {
        assertEquals(new BigDecimal("0.00"),
                wxStoreMerchantOrderImplUnderTest.getWechatOrderFeeByOrderGuid("orderGuid"));
    }

    @Test
    public void testGetWechatOrderInfoByOrderGuid() {
        // Setup
        final WechatOrderInfoDTO expectedResult = new WechatOrderInfoDTO();
        expectedResult.setWechatOrderFee(new BigDecimal("0.00"));
        expectedResult.setOrderState(0);
        expectedResult.setOrderSource(0);
        expectedResult.setOrderGuid("guid");

        // Configure TradeClientService.findByOrderGuid(...).
        final OrderDTO orderDTO = OrderDTO.builder()
                .guid("guid")
                .orderNo("guid")
                .deviceType(0)
                .orderFee(new BigDecimal("0.00"))
                .state(0)
                .build();
        when(mockTradeClientService.findByOrderGuid("orderGuid")).thenReturn(orderDTO);

        // Run the test
        final WechatOrderInfoDTO result = wxStoreMerchantOrderImplUnderTest.getWechatOrderInfoByOrderGuid("orderGuid");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetWechatOrderInfoByGuid() {
        // Setup
        final WechatOrderInfoDTO expectedResult = new WechatOrderInfoDTO();
        expectedResult.setWechatOrderFee(new BigDecimal("0.00"));
        expectedResult.setOrderState(0);
        expectedResult.setOrderSource(0);
        expectedResult.setOrderGuid("guid");

        // Configure TradeClientService.findByOrderGuid(...).
        final OrderDTO orderDTO = OrderDTO.builder()
                .guid("guid")
                .orderNo("guid")
                .deviceType(0)
                .orderFee(new BigDecimal("0.00"))
                .state(0)
                .build();
        when(mockTradeClientService.findByOrderGuid("guid")).thenReturn(orderDTO);

        // Configure WxOrderRecordService.getOne(...).
        final WxOrderRecordDO wxOrderRecordDO = new WxOrderRecordDO(0L, "58d0dfce-a97a-4608-b8a7-f7f4f68b843b",
                "orderGuid", "merchantGuid", "guid", "brandGuid", "brandName", "logUrl", "storeGuid", "storeName",
                "areaGuid", "areaName", "tableGuid", "tableCode", 0, 0, "orderStateName", new BigDecimal("0.00"),
                new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName", "orderHolderNo",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0);
        when(mockWxOrderRecordService.getOne(any(LambdaQueryWrapper.class))).thenReturn(wxOrderRecordDO);

        // Run the test
        final WechatOrderInfoDTO result = wxStoreMerchantOrderImplUnderTest.getWechatOrderInfoByGuid(
                "f4ada807-7502-488f-a090-ce7451ce36b5");

        // Verify the results
        assertEquals(expectedResult, result);
    }
}
