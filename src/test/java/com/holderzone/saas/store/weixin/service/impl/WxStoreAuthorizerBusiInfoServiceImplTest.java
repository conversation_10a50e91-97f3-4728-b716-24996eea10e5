package com.holderzone.saas.store.weixin.service.impl;

import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.assertFalse;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class WxStoreAuthorizerBusiInfoServiceImplTest {

    @Mock
    private RedisUtils mockRedisUtils;

    @InjectMocks
    private WxStoreAuthorizerBusiInfoServiceImpl wxStoreAuthorizerBusiInfoServiceImplUnderTest;

    @Test
    public void testSaveOrUpdateStoreAuthorizeBusiInfo() {
        // Setup
        final Map<String, Integer> businessInfo = new HashMap<>();
        when(mockRedisUtils.generateGuid("redisKey")).thenReturn("7b09acf8-d644-4817-bdfe-9ccf28176d8f");

        // Run the test
        final boolean result = wxStoreAuthorizerBusiInfoServiceImplUnderTest.saveOrUpdateStoreAuthorizeBusiInfo(
                "authorizerAppId", businessInfo);

        // Verify the results
        assertFalse(result);
    }
}
