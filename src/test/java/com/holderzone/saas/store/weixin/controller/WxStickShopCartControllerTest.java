package com.holderzone.saas.store.weixin.controller;

import com.holderzone.saas.store.dto.weixin.WxStickShopCartDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStickShopCartRemoveDTO;
import com.holderzone.saas.store.weixin.service.WxStickShopCartService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(WxStickShopCartController.class)
public class WxStickShopCartControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private WxStickShopCartService mockWxStickShopCartService;

    @Test
    public void testListShopCart() throws Exception {
        // Setup
        // Configure WxStickShopCartService.listShopCart(...).
        final List<WxStickShopCartDTO> wxStickShopCartDTOS = Arrays.asList(
                new WxStickShopCartDTO("modelGuid", "modelName", "previewImg", new BigDecimal("0.00")));
        when(mockWxStickShopCartService.listShopCart()).thenReturn(wxStickShopCartDTOS);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_stick_shop_cart/list_shop_cart")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testListShopCart_WxStickShopCartServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockWxStickShopCartService.listShopCart()).thenReturn(Collections.emptyList());

        // Run the test and verify the results
        mockMvc.perform(post("/wx_stick_shop_cart/list_shop_cart")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("[]", true));
    }

    @Test
    public void testAddModels() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(post("/wx_stick_shop_cart/add_models")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
        verify(mockWxStickShopCartService).addModels(
                Arrays.asList(new WxStickShopCartDTO("modelGuid", "modelName", "previewImg", new BigDecimal("0.00"))));
    }

    @Test
    public void testRemoveModels() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(post("/wx_stick_shop_cart/remove_models")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
        verify(mockWxStickShopCartService).removeModels(new WxStickShopCartRemoveDTO(Arrays.asList("value"), 0));
    }
}
