package com.holderzone.saas.store.weixin.controller;

import com.holderzone.saas.store.dto.weixin.WxMemberCenterCardRespDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import com.holderzone.saas.store.weixin.service.WxMemberCenterCardService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(WxMemberCenterCardController.class)
public class WxMemberCenterCardControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private WxMemberCenterCardService mockWxMemberCenterCardService;

    @Test
    public void testCardList() throws Exception {
        // Setup
        // Configure WxMemberCenterCardService.cardList(...).
        final WxMemberCenterCardRespDTO wxMemberCenterCardRespDTO = WxMemberCenterCardRespDTO.builder().build();
        when(mockWxMemberCenterCardService.cardList(WxStoreConsumerDTO.builder().build()))
                .thenReturn(wxMemberCenterCardRespDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_member_card/card_list")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }
}
