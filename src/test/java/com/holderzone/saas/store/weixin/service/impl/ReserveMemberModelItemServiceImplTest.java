package com.holderzone.saas.store.weixin.service.impl;

import com.holderzone.saas.store.dto.weixin.WxMemberOverviewModelDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import com.holderzone.saas.store.weixin.service.WxStoreSessionDetailsService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertEquals;

@RunWith(MockitoJUnitRunner.class)
public class ReserveMemberModelItemServiceImplTest {

    @Mock
    private WxStoreSessionDetailsService mockWxStoreSessionDetailsService;

    @InjectMocks
    private ReserveMemberModelItemServiceImpl reserveMemberModelItemServiceImplUnderTest;

    @Test
    public void testGetWxMemberOverviewModel() {
        // Setup
        final WxStoreConsumerDTO wxStoreConsumerDTO = WxStoreConsumerDTO.builder().build();
        final WxMemberOverviewModelDTO expectedResult = WxMemberOverviewModelDTO.builder()
                .modelId(0)
                .modelName("我的预订")
                .modelCount(0)
                .build();

        // Run the test
        final WxMemberOverviewModelDTO result = reserveMemberModelItemServiceImplUnderTest.getWxMemberOverviewModel(
                wxStoreConsumerDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }
}
