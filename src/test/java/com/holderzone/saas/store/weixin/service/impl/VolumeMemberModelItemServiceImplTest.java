package com.holderzone.saas.store.weixin.service.impl;

import com.holderzone.saas.store.dto.weixin.WxMemberOverviewModelDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import com.holderzone.saas.store.weixin.service.WxStoreSessionDetailsService;
import com.holderzone.saas.store.weixin.service.rpc.member.MemberClientService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class VolumeMemberModelItemServiceImplTest {

    @Mock
    private WxStoreSessionDetailsService mockWxStoreSessionDetailsService;
    @Mock
    private MemberClientService mockMemberClientService;

    @InjectMocks
    private VolumeMemberModelItemServiceImpl volumeMemberModelItemServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        volumeMemberModelItemServiceImplUnderTest.memberClientService = mockMemberClientService;
    }

    @Test
    public void testGetWxMemberOverviewModel() {
        // Setup
        final WxStoreConsumerDTO wxStoreConsumerDTO = WxStoreConsumerDTO.builder()
                .openId("openId")
                .enterpriseGuid("enterpriseGuid")
                .brandGuid("brandGuid")
                .build();
        final WxMemberOverviewModelDTO expectedResult = WxMemberOverviewModelDTO.builder()
                .modelId(0)
                .modelName("我的券")
                .modelCount(0)
                .build();
        when(mockWxStoreSessionDetailsService.getMemberValidVolumeNumber("enterpriseGuid", "brandGuid",
                "openId")).thenReturn(0);

        // Run the test
        final WxMemberOverviewModelDTO result = volumeMemberModelItemServiceImplUnderTest.getWxMemberOverviewModel(
                wxStoreConsumerDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetWxMemberOverviewModel_WxStoreSessionDetailsServiceReturnsNull() {
        // Setup
        final WxStoreConsumerDTO wxStoreConsumerDTO = WxStoreConsumerDTO.builder()
                .openId("openId")
                .enterpriseGuid("enterpriseGuid")
                .brandGuid("brandGuid")
                .build();
        final WxMemberOverviewModelDTO expectedResult = WxMemberOverviewModelDTO.builder()
                .modelId(0)
                .modelName("我的券")
                .modelCount(0)
                .build();
        when(mockWxStoreSessionDetailsService.getMemberValidVolumeNumber("enterpriseGuid", "brandGuid",
                "openId")).thenReturn(null);

        // Run the test
        final WxMemberOverviewModelDTO result = volumeMemberModelItemServiceImplUnderTest.getWxMemberOverviewModel(
                wxStoreConsumerDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }
}
