package com.holderzone.saas.store.weixin.service.impl;

import com.google.common.util.concurrent.MoreExecutors;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.holder.saas.member.terminal.dto.member.response.ResponseMemberAndCardInfoDTO;
import com.holderzone.holder.saas.member.terminal.dto.member.response.ResponseMemberCard;
import com.holderzone.holder.saas.member.terminal.dto.member.response.ResponseMemberInfoDTO;
import com.holderzone.holder.saas.member.terminal.dto.order.ResponseIntegralOffset;
import com.holderzone.holder.saas.member.wechat.dto.activitie.MemberConsumptionGiftDTO;
import com.holderzone.holder.saas.member.wechat.dto.label.RequestManualLabel;
import com.holderzone.holder.saas.member.wechat.dto.member.RequestQueryMemberInfo;
import com.holderzone.holder.saas.member.wechat.dto.member.ResponseMemberInfo;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.resource.common.dto.enterprise.EnterpriseDTO;
import com.holderzone.saas.store.dto.business.manage.HandoverPayDetailDTO;
import com.holderzone.saas.store.dto.business.manage.HandoverRecordDTO;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.item.resp.ItemEstimateForAndroidRespDTO;
import com.holderzone.saas.store.dto.item.resp.estimate.EstimateResultRespDTO;
import com.holderzone.saas.store.dto.order.OrderLockDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.request.bill.BillPayReqDTO;
import com.holderzone.saas.store.dto.order.request.face.WeChatPayLockReqDTO;
import com.holderzone.saas.store.dto.order.request.face.WeChatPayReqDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.order.response.item.EstimateItemRespDTO;
import com.holderzone.saas.store.dto.pay.*;
import com.holderzone.saas.store.dto.terminal.StoreDeviceDTO;
import com.holderzone.saas.store.dto.trade.BaseInfo;
import com.holderzone.saas.store.dto.weixin.*;
import com.holderzone.saas.store.dto.weixin.deal.UserMemberSessionDTO;
import com.holderzone.saas.store.dto.weixin.deal.WeChatH5PayReqDTO;
import com.holderzone.saas.store.dto.weixin.member.*;
import com.holderzone.saas.store.dto.weixin.req.WxH5PayReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxPrepayReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStoreReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxOrderConfigDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxPayRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreItemRespDTO;
import com.holderzone.saas.store.weixin.config.ResponseModel;
import com.holderzone.saas.store.weixin.config.WeChatConfig;
import com.holderzone.saas.store.weixin.entity.domain.WxOrderRecordDO;
import com.holderzone.saas.store.weixin.entity.domain.WxUserRecordDO;
import com.holderzone.saas.store.weixin.entity.dto.WxMemberConsumeMsgDTO;
import com.holderzone.saas.store.weixin.entity.dto.WxPayCallbackDTO;
import com.holderzone.saas.store.weixin.entity.enums.WxTemplateTypeEnum;
import com.holderzone.saas.store.weixin.event.NotifyMessageQueue;
import com.holderzone.saas.store.weixin.execption.CorrectResult;
import com.holderzone.saas.store.weixin.helper.WebsocketMessageHelper;
import com.holderzone.saas.store.weixin.service.*;
import com.holderzone.saas.store.weixin.service.rpc.*;
import com.holderzone.saas.store.weixin.service.rpc.member.HsaBaseClientService;
import com.holderzone.saas.store.weixin.utils.DynamicHelper;
import com.holderzone.saas.store.weixin.utils.UserMemberSessionUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class WxStorePayServiceImplTest {

    @Mock
    private WxStoreOrderConfigService mockWxStoreOrderConfigService;
    @Mock
    private DynamicHelper mockDynamicHelper;
    @Mock
    private WxStorePayClientService mockWxStorePayClientService;
    @Mock
    private RedisUtils mockRedisUtils;
    @Mock
    private WxStoreMenuDetailsService mockWxStoreMenuDetailsService;
    @Mock
    private WxStoreDineInOrderClientService mockWxStoreDineInOrderClientService;
    @Mock
    private WxStoreMerchantOrderService mockWxStoreMerchantOrderService;
    @Mock
    private WxOrderRecordService mockWxOrderRecordService;
    @Mock
    private WxStoreSessionDetailsService mockWxStoreSessionDetailsService;
    @Mock
    private WxStoreDineInBillClientService mockWxStoreDineInBillClientService;
    @Mock
    private WxStoreTableClientService mockWxStoreTableClientService;
    @Mock
    private WxStoreEstimateClientService mockWxStoreEstimateClientService;
    @Mock
    private NotifyMessageQueue mockNotifyMessageQueue;
    @Mock
    private WxStoreAdvanceOrderService mockWxStoreAdvanceOrderService;
    @Mock
    private UserMemberSessionUtils mockUserMemberSessionUtils;
    @Mock
    private WxUserRecordService mockWxUserRecordService;
    @Mock
    private WebsocketMessageHelper mockWebsocketMessageHelper;
    @Mock
    private WxOpenMessageService mockWxOpenMessageService;
    @Mock
    private OrganizationClientService mockOrganizationClientService;
    @Mock
    private BusinessClientService mockBusinessClientService;
    @Mock
    private HsaBaseClientService mockHsaBaseClientService;
    @Mock
    private WeChatConfig mockWeChatConfig;
    @Mock
    private WxStoreAuthorizerInfoService mockWxStoreAuthorizerInfoService;
    @Mock
    private WxStoreTradeOrderService mockWxStoreTradeOrderService;
    @Mock
    private MemberMarketingClientService mockMemberMarketingClientService;
    @Mock
    private TcdOrderService mockTcdOrderService;

    @InjectMocks
    private WxStorePayServiceImpl wxStorePayServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        ReflectionTestUtils.setField(wxStorePayServiceImplUnderTest, "executorService",
                MoreExecutors.newDirectExecutorService());
        ReflectionTestUtils.setField(wxStorePayServiceImplUnderTest, "mpAppId", "appId");
        wxStorePayServiceImplUnderTest.wxStoreOrderConfigService = mockWxStoreOrderConfigService;
        wxStorePayServiceImplUnderTest.dynamicHelper = mockDynamicHelper;
        wxStorePayServiceImplUnderTest.weChatConfig = mockWeChatConfig;
    }

    @Test
    public void testWeChatPublic() {
        // Setup
        final WxH5PayReqDTO wxH5PayReqDTO = WxH5PayReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("createName")
                        .headImgUrl("headImgUrl")
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("diningTableGuid")
                        .brandGuid("brandGuid")
                        .build())
                .outNotifyUrl("outNotifyUrl")
                .orderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed")
                .build();
        final WxPayRespDTO expectedResult = WxPayRespDTO.builder()
                .payUrl("payUrl")
                .couldPay(0)
                .errorMsg("errorMsg")
                .result(AggPayRespDTO.builder().build())
                .build();
        when(mockWxStoreMenuDetailsService.judgeOrderType("storeGuid")).thenReturn(false);
        when(mockRedisUtils.generateGuid("hstTransactionRecord")).thenReturn("62fd3dbb-b68c-45a1-95a9-87508447c165");

        // Configure WxStoreSessionDetailsService.getPrepay(...).
        final WxPrepayReqDTO wxPrepayReqDTO = WxPrepayReqDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .brandGuid("brandGuid")
                .storeGuid("storeGuid")
                .tableGuid("diningTableGuid")
                .openId("openId")
                .payAmount(new BigDecimal("0.00"))
                .memberCardGuid("cardGuid")
                .cardGuid("cardGuid")
                .memberInfoCardGuid("memberInfoCardGuid")
                .memberIntegral(0)
                .volumeCode("volumeCode")
                .orderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed")
                .mode(0)
                .version(0)
                .build();
        when(mockWxStoreSessionDetailsService.getPrepay("storeGuid", "openId")).thenReturn(wxPrepayReqDTO);

        when(mockWxStoreSessionDetailsService.getOrderGuid("storeGuid", "diningTableGuid", "openId"))
                .thenReturn("orderGuid");

        // Configure WxStoreSessionDetailsService.calculateDetails2(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        dineinOrderDetailRespDTO.setStoreGuid("storeGuid");
        dineinOrderDetailRespDTO.setStoreName("storeName");
        dineinOrderDetailRespDTO.setOriginalOrderGuid(0L);
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setDeviceType(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setAppendFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setChangeFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        discountFeeDetailDTO.setRule("rule");
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setOrderNo("body");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setStateName("已支付");
        dineinOrderDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setMemberGuid("memberGuid");
        dineinOrderDetailRespDTO.setMemberPhone("memberPhone");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        final ResponseIntegralOffset integralOffsetResultRespDTO = new ResponseIntegralOffset();
        integralOffsetResultRespDTO.setUseIntegral(0);
        integralOffsetResultRespDTO.setDeductionMoney(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setIntegralOffsetResultRespDTO(integralOffsetResultRespDTO);
        dineinOrderDetailRespDTO.setTip("errorMsg");
        dineinOrderDetailRespDTO.setVersion(0);
        final CorrectResult<DineinOrderDetailRespDTO> dineinOrderDetailRespDTOCorrectResult = new CorrectResult<>(
                "errorMsg", 0, dineinOrderDetailRespDTO);
        when(mockWxStoreSessionDetailsService.calculateDetails2("orderGuid", "openId", "memberInfoCardGuid", 0,
                "volumeCode")).thenReturn(dineinOrderDetailRespDTOCorrectResult);

        when(mockWeChatConfig.getPayAppId()).thenReturn("appId");
        when(mockWeChatConfig.getMchntName()).thenReturn("mchntName");
        when(mockWeChatConfig.getAppSecret()).thenReturn("appSecret");

        // Configure WxStoreSessionDetailsService.getEnterpriseDetail(...).
        final EnterpriseDTO enterpriseDTO = new EnterpriseDTO();
        enterpriseDTO.setId(0L);
        enterpriseDTO.setUid("uid");
        enterpriseDTO.setEnterpriseGuid("enterpriseGuid");
        enterpriseDTO.setName("enterpriseName");
        enterpriseDTO.setDbServer(0);
        when(mockWxStoreSessionDetailsService.getEnterpriseDetail()).thenReturn(enterpriseDTO);

        // Configure WxStoreOrderConfigService.getDetailConfig(...).
        final WxOrderConfigDTO wxOrderConfigDTO = new WxOrderConfigDTO();
        wxOrderConfigDTO.setGuid("200534fd-70b3-4856-ac55-94e0a06310f4");
        wxOrderConfigDTO.setIsOrderOpen(0);
        wxOrderConfigDTO.setOrderModel(0);
        wxOrderConfigDTO.setTakingModel(0);
        wxOrderConfigDTO.setIsOnlinePayed(0);
        when(mockWxStoreOrderConfigService.getDetailConfig(WxStoreReqDTO.builder()
                .storeGuid("storeGuid")
                .build())).thenReturn(wxOrderConfigDTO);

        when(mockWxStorePayClientService.weChatPublic(new SaasAggWeChatPublicAccountPayDTO(
                new AggWeChatPublicAccountPayDTO(new BigDecimal("0.00"), "6e64ec83-01fe-4f5b-8759-73c1b66062ed",
                        "62fd3dbb-b68c-45a1-95a9-87508447c165", "6e64ec83-01fe-4f5b-8759-73c1b66062ed",
                        "6e64ec83-01fe-4f5b-8759-73c1b66062ed", "outNotifyUrl", "storeName", "enterpriseName", "appId",
                        "mchntName", "appSecret", "outNotifyUrl", "attachData"), "format"))).thenReturn("payUrl");
        when(mockRedisUtils.get("key")).thenReturn("result");

        // Configure WxStoreAdvanceOrderService.checkEstimate(...).
        final WxStoreAdvanceEstimateDTO wxStoreAdvanceEstimateDTO = WxStoreAdvanceEstimateDTO.builder()
                .estimateResult(false)
                .wxStoreEstimateItemList(Arrays.asList(WxStoreEstimateItem.builder().build()))
                .build();
        final WxStoreAdvanceOrderDTO wxStoreAdvanceOrderDTO = new WxStoreAdvanceOrderDTO();
        wxStoreAdvanceOrderDTO.setWxStoreAdvanceConsumerReqDTO(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("createName")
                        .headImgUrl("headImgUrl")
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("diningTableGuid")
                        .brandGuid("brandGuid")
                        .build())
                .build());
        final WxStoreItemRespDTO wxStoreItemRespDTO = new WxStoreItemRespDTO();
        wxStoreItemRespDTO.setItemGuid("itemGuid");
        wxStoreItemRespDTO.setTypeGuid("typeGuid");
        wxStoreItemRespDTO.setTypeName("typeName");
        wxStoreAdvanceOrderDTO.setItemList(Arrays.asList(wxStoreItemRespDTO));
        when(mockWxStoreAdvanceOrderService.checkEstimate(wxStoreAdvanceOrderDTO))
                .thenReturn(wxStoreAdvanceEstimateDTO);

        // Run the test
        final WxPayRespDTO result = wxStorePayServiceImplUnderTest.weChatPublic(wxH5PayReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockWxStoreSessionDetailsService).savePaidUser("storeGuid", "openId", WxH5PayReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("createName")
                        .headImgUrl("headImgUrl")
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("diningTableGuid")
                        .brandGuid("brandGuid")
                        .build())
                .outNotifyUrl("outNotifyUrl")
                .orderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed")
                .build());

        // Confirm WxStoreSessionDetailsService.savePayCallBack(...).
        final WeChatPayReqDTO weChatPayReqDTO = new WeChatPayReqDTO();
        weChatPayReqDTO.setDeviceType(0);
        weChatPayReqDTO.setDeviceId("openId");
        weChatPayReqDTO.setEnterpriseGuid("enterpriseGuid");
        weChatPayReqDTO.setStoreGuid("storeGuid");
        weChatPayReqDTO.setStoreName("storeName");
        weChatPayReqDTO.setUserGuid("openId");
        weChatPayReqDTO.setUserName("createName");
        weChatPayReqDTO.setOrderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        weChatPayReqDTO.setPayGuid("62fd3dbb-b68c-45a1-95a9-87508447c165");
        weChatPayReqDTO.setPayPowerId("payPowerId");
        weChatPayReqDTO.setSuccess(false);
        weChatPayReqDTO.setAmount(new BigDecimal("0.00"));
        weChatPayReqDTO.setOrderFee(new BigDecimal("0.00"));
        weChatPayReqDTO.setActuallyPayFee(new BigDecimal("0.00"));
        weChatPayReqDTO.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO1.setDiscountType(0);
        discountFeeDetailDTO1.setDiscountFee(new BigDecimal("0.00"));
        discountFeeDetailDTO1.setRule("rule");
        weChatPayReqDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        weChatPayReqDTO.setMemberGuid("memberGuid");
        verify(mockWxStoreSessionDetailsService).savePayCallBack("6e64ec83-01fe-4f5b-8759-73c1b66062ed",
                weChatPayReqDTO);

        // Confirm WxStoreDineInOrderClientService.lockorder(...).
        final OrderLockDTO orderLockDto = new OrderLockDTO();
        orderLockDto.setDeviceType(0);
        orderLockDto.setDeviceId("openId");
        orderLockDto.setEnterpriseGuid("enterpriseGuid");
        orderLockDto.setStoreGuid("storeGuid");
        orderLockDto.setStoreName("storeName");
        orderLockDto.setUserGuid("openId");
        orderLockDto.setUserName("createName");
        orderLockDto.setOrderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        verify(mockWxStoreDineInOrderClientService).lockorder(orderLockDto);
    }

    @Test
    public void testWeChatPublic_WxStoreMenuDetailsServiceReturnsTrue() {
        // Setup
        final WxH5PayReqDTO wxH5PayReqDTO = WxH5PayReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("createName")
                        .headImgUrl("headImgUrl")
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("diningTableGuid")
                        .brandGuid("brandGuid")
                        .build())
                .outNotifyUrl("outNotifyUrl")
                .orderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed")
                .build();
        final WxPayRespDTO expectedResult = WxPayRespDTO.builder()
                .payUrl("payUrl")
                .couldPay(0)
                .errorMsg("errorMsg")
                .result(AggPayRespDTO.builder().build())
                .build();
        when(mockWxStoreMenuDetailsService.judgeOrderType("storeGuid")).thenReturn(true);
        when(mockRedisUtils.generateGuid("hstTransactionRecord")).thenReturn("62fd3dbb-b68c-45a1-95a9-87508447c165");

        // Configure WxStoreSessionDetailsService.getPrepay(...).
        final WxPrepayReqDTO wxPrepayReqDTO = WxPrepayReqDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .brandGuid("brandGuid")
                .storeGuid("storeGuid")
                .tableGuid("diningTableGuid")
                .openId("openId")
                .payAmount(new BigDecimal("0.00"))
                .memberCardGuid("cardGuid")
                .cardGuid("cardGuid")
                .memberInfoCardGuid("memberInfoCardGuid")
                .memberIntegral(0)
                .volumeCode("volumeCode")
                .orderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed")
                .mode(0)
                .version(0)
                .build();
        when(mockWxStoreSessionDetailsService.getPrepay("storeGuid", "openId")).thenReturn(wxPrepayReqDTO);

        when(mockWxStoreSessionDetailsService.getOrderGuid("storeGuid", "diningTableGuid", "openId"))
                .thenReturn("orderGuid");

        // Configure WxStoreSessionDetailsService.calculateDetails2(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        dineinOrderDetailRespDTO.setStoreGuid("storeGuid");
        dineinOrderDetailRespDTO.setStoreName("storeName");
        dineinOrderDetailRespDTO.setOriginalOrderGuid(0L);
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setDeviceType(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setAppendFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setChangeFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        discountFeeDetailDTO.setRule("rule");
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setOrderNo("body");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setStateName("已支付");
        dineinOrderDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setMemberGuid("memberGuid");
        dineinOrderDetailRespDTO.setMemberPhone("memberPhone");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        final ResponseIntegralOffset integralOffsetResultRespDTO = new ResponseIntegralOffset();
        integralOffsetResultRespDTO.setUseIntegral(0);
        integralOffsetResultRespDTO.setDeductionMoney(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setIntegralOffsetResultRespDTO(integralOffsetResultRespDTO);
        dineinOrderDetailRespDTO.setTip("errorMsg");
        dineinOrderDetailRespDTO.setVersion(0);
        final CorrectResult<DineinOrderDetailRespDTO> dineinOrderDetailRespDTOCorrectResult = new CorrectResult<>(
                "errorMsg", 0, dineinOrderDetailRespDTO);
        when(mockWxStoreSessionDetailsService.calculateDetails2("orderGuid", "openId", "memberInfoCardGuid", 0,
                "volumeCode")).thenReturn(dineinOrderDetailRespDTOCorrectResult);

        // Configure WxStoreDineInOrderClientService.prepay(...).
        final WeChatPayLockReqDTO WeChatPayLockReqDTO = new WeChatPayLockReqDTO();
        WeChatPayLockReqDTO.setOrderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        WeChatPayLockReqDTO.setVersion(0);
        when(mockWxStoreDineInOrderClientService.prepay(WeChatPayLockReqDTO)).thenReturn(false);

        // Run the test
        final WxPayRespDTO result = wxStorePayServiceImplUnderTest.weChatPublic(wxH5PayReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testWeChatPublic_WxStoreSessionDetailsServiceCalculateDetails2ReturnsNoItem() {
        // Setup
        final WxH5PayReqDTO wxH5PayReqDTO = WxH5PayReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("createName")
                        .headImgUrl("headImgUrl")
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("diningTableGuid")
                        .brandGuid("brandGuid")
                        .build())
                .outNotifyUrl("outNotifyUrl")
                .orderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed")
                .build();
        final WxPayRespDTO expectedResult = WxPayRespDTO.builder()
                .payUrl("payUrl")
                .couldPay(0)
                .errorMsg("errorMsg")
                .result(AggPayRespDTO.builder().build())
                .build();
        when(mockWxStoreMenuDetailsService.judgeOrderType("storeGuid")).thenReturn(true);
        when(mockRedisUtils.generateGuid("hstTransactionRecord")).thenReturn("62fd3dbb-b68c-45a1-95a9-87508447c165");

        // Configure WxStoreSessionDetailsService.getPrepay(...).
        final WxPrepayReqDTO wxPrepayReqDTO = WxPrepayReqDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .brandGuid("brandGuid")
                .storeGuid("storeGuid")
                .tableGuid("diningTableGuid")
                .openId("openId")
                .payAmount(new BigDecimal("0.00"))
                .memberCardGuid("cardGuid")
                .cardGuid("cardGuid")
                .memberInfoCardGuid("memberInfoCardGuid")
                .memberIntegral(0)
                .volumeCode("volumeCode")
                .orderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed")
                .mode(0)
                .version(0)
                .build();
        when(mockWxStoreSessionDetailsService.getPrepay("storeGuid", "openId")).thenReturn(wxPrepayReqDTO);

        when(mockWxStoreSessionDetailsService.getOrderGuid("storeGuid", "diningTableGuid", "openId"))
                .thenReturn("orderGuid");
        when(mockWxStoreSessionDetailsService.calculateDetails2("orderGuid", "openId", "memberInfoCardGuid", 0,
                "volumeCode")).thenReturn(CorrectResult.changeFailed());

        // Configure WxStoreDineInOrderClientService.prepay(...).
        final WeChatPayLockReqDTO WeChatPayLockReqDTO = new WeChatPayLockReqDTO();
        WeChatPayLockReqDTO.setOrderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        WeChatPayLockReqDTO.setVersion(0);
        when(mockWxStoreDineInOrderClientService.prepay(WeChatPayLockReqDTO)).thenReturn(false);

        // Run the test
        final WxPayRespDTO result = wxStorePayServiceImplUnderTest.weChatPublic(wxH5PayReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testWeChatPublic_WxStoreSessionDetailsServiceCalculateDetails2ReturnsFailure() {
        // Setup
        final WxH5PayReqDTO wxH5PayReqDTO = WxH5PayReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("createName")
                        .headImgUrl("headImgUrl")
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("diningTableGuid")
                        .brandGuid("brandGuid")
                        .build())
                .outNotifyUrl("outNotifyUrl")
                .orderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed")
                .build();
        final WxPayRespDTO expectedResult = WxPayRespDTO.builder()
                .payUrl("payUrl")
                .couldPay(0)
                .errorMsg("errorMsg")
                .result(AggPayRespDTO.builder().build())
                .build();
        when(mockWxStoreMenuDetailsService.judgeOrderType("storeGuid")).thenReturn(true);
        when(mockRedisUtils.generateGuid("hstTransactionRecord")).thenReturn("62fd3dbb-b68c-45a1-95a9-87508447c165");

        // Configure WxStoreSessionDetailsService.getPrepay(...).
        final WxPrepayReqDTO wxPrepayReqDTO = WxPrepayReqDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .brandGuid("brandGuid")
                .storeGuid("storeGuid")
                .tableGuid("diningTableGuid")
                .openId("openId")
                .payAmount(new BigDecimal("0.00"))
                .memberCardGuid("cardGuid")
                .cardGuid("cardGuid")
                .memberInfoCardGuid("memberInfoCardGuid")
                .memberIntegral(0)
                .volumeCode("volumeCode")
                .orderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed")
                .mode(0)
                .version(0)
                .build();
        when(mockWxStoreSessionDetailsService.getPrepay("storeGuid", "openId")).thenReturn(wxPrepayReqDTO);

        when(mockWxStoreSessionDetailsService.getOrderGuid("storeGuid", "diningTableGuid", "openId"))
                .thenReturn("orderGuid");

        // Configure WxStoreSessionDetailsService.calculateDetails2(...).
        final CorrectResult<DineinOrderDetailRespDTO> dineinOrderDetailRespDTOCorrectResult = CorrectResult.changeFailed();
        when(mockWxStoreSessionDetailsService.calculateDetails2("orderGuid", "openId", "memberInfoCardGuid", 0,
                "volumeCode")).thenReturn(dineinOrderDetailRespDTOCorrectResult);

        // Configure WxStoreDineInOrderClientService.prepay(...).
        final WeChatPayLockReqDTO WeChatPayLockReqDTO = new WeChatPayLockReqDTO();
        WeChatPayLockReqDTO.setOrderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        WeChatPayLockReqDTO.setVersion(0);
        when(mockWxStoreDineInOrderClientService.prepay(WeChatPayLockReqDTO)).thenReturn(false);

        // Run the test
        final WxPayRespDTO result = wxStorePayServiceImplUnderTest.weChatPublic(wxH5PayReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testWeChatPublic_WxStoreDineInOrderClientServicePrepayReturnsTrue() {
        // Setup
        final WxH5PayReqDTO wxH5PayReqDTO = WxH5PayReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("createName")
                        .headImgUrl("headImgUrl")
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("diningTableGuid")
                        .brandGuid("brandGuid")
                        .build())
                .outNotifyUrl("outNotifyUrl")
                .orderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed")
                .build();
        final WxPayRespDTO expectedResult = WxPayRespDTO.builder()
                .payUrl("payUrl")
                .couldPay(0)
                .errorMsg("errorMsg")
                .result(AggPayRespDTO.builder().build())
                .build();
        when(mockWxStoreMenuDetailsService.judgeOrderType("storeGuid")).thenReturn(true);
        when(mockRedisUtils.generateGuid("hstTransactionRecord")).thenReturn("62fd3dbb-b68c-45a1-95a9-87508447c165");

        // Configure WxStoreSessionDetailsService.getPrepay(...).
        final WxPrepayReqDTO wxPrepayReqDTO = WxPrepayReqDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .brandGuid("brandGuid")
                .storeGuid("storeGuid")
                .tableGuid("diningTableGuid")
                .openId("openId")
                .payAmount(new BigDecimal("0.00"))
                .memberCardGuid("cardGuid")
                .cardGuid("cardGuid")
                .memberInfoCardGuid("memberInfoCardGuid")
                .memberIntegral(0)
                .volumeCode("volumeCode")
                .orderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed")
                .mode(0)
                .version(0)
                .build();
        when(mockWxStoreSessionDetailsService.getPrepay("storeGuid", "openId")).thenReturn(wxPrepayReqDTO);

        when(mockWxStoreSessionDetailsService.getOrderGuid("storeGuid", "diningTableGuid", "openId"))
                .thenReturn("orderGuid");

        // Configure WxStoreSessionDetailsService.calculateDetails2(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        dineinOrderDetailRespDTO.setStoreGuid("storeGuid");
        dineinOrderDetailRespDTO.setStoreName("storeName");
        dineinOrderDetailRespDTO.setOriginalOrderGuid(0L);
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setDeviceType(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setAppendFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setChangeFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        discountFeeDetailDTO.setRule("rule");
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setOrderNo("body");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setStateName("已支付");
        dineinOrderDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setMemberGuid("memberGuid");
        dineinOrderDetailRespDTO.setMemberPhone("memberPhone");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        final ResponseIntegralOffset integralOffsetResultRespDTO = new ResponseIntegralOffset();
        integralOffsetResultRespDTO.setUseIntegral(0);
        integralOffsetResultRespDTO.setDeductionMoney(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setIntegralOffsetResultRespDTO(integralOffsetResultRespDTO);
        dineinOrderDetailRespDTO.setTip("errorMsg");
        dineinOrderDetailRespDTO.setVersion(0);
        final CorrectResult<DineinOrderDetailRespDTO> dineinOrderDetailRespDTOCorrectResult = new CorrectResult<>(
                "errorMsg", 0, dineinOrderDetailRespDTO);
        when(mockWxStoreSessionDetailsService.calculateDetails2("orderGuid", "openId", "memberInfoCardGuid", 0,
                "volumeCode")).thenReturn(dineinOrderDetailRespDTOCorrectResult);

        // Configure WxStoreDineInOrderClientService.prepay(...).
        final WeChatPayLockReqDTO WeChatPayLockReqDTO = new WeChatPayLockReqDTO();
        WeChatPayLockReqDTO.setOrderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        WeChatPayLockReqDTO.setVersion(0);
        when(mockWxStoreDineInOrderClientService.prepay(WeChatPayLockReqDTO)).thenReturn(true);

        when(mockWeChatConfig.getPayAppId()).thenReturn("appId");
        when(mockWeChatConfig.getMchntName()).thenReturn("mchntName");
        when(mockWeChatConfig.getAppSecret()).thenReturn("appSecret");

        // Configure WxStoreSessionDetailsService.getEnterpriseDetail(...).
        final EnterpriseDTO enterpriseDTO = new EnterpriseDTO();
        enterpriseDTO.setId(0L);
        enterpriseDTO.setUid("uid");
        enterpriseDTO.setEnterpriseGuid("enterpriseGuid");
        enterpriseDTO.setName("enterpriseName");
        enterpriseDTO.setDbServer(0);
        when(mockWxStoreSessionDetailsService.getEnterpriseDetail()).thenReturn(enterpriseDTO);

        // Configure WxStoreOrderConfigService.getDetailConfig(...).
        final WxOrderConfigDTO wxOrderConfigDTO = new WxOrderConfigDTO();
        wxOrderConfigDTO.setGuid("200534fd-70b3-4856-ac55-94e0a06310f4");
        wxOrderConfigDTO.setIsOrderOpen(0);
        wxOrderConfigDTO.setOrderModel(0);
        wxOrderConfigDTO.setTakingModel(0);
        wxOrderConfigDTO.setIsOnlinePayed(0);
        when(mockWxStoreOrderConfigService.getDetailConfig(WxStoreReqDTO.builder()
                .storeGuid("storeGuid")
                .build())).thenReturn(wxOrderConfigDTO);

        when(mockWxStorePayClientService.weChatPublic(new SaasAggWeChatPublicAccountPayDTO(
                new AggWeChatPublicAccountPayDTO(new BigDecimal("0.00"), "6e64ec83-01fe-4f5b-8759-73c1b66062ed",
                        "62fd3dbb-b68c-45a1-95a9-87508447c165", "6e64ec83-01fe-4f5b-8759-73c1b66062ed",
                        "6e64ec83-01fe-4f5b-8759-73c1b66062ed", "outNotifyUrl", "storeName", "enterpriseName", "appId",
                        "mchntName", "appSecret", "outNotifyUrl", "attachData"), "format"))).thenReturn("payUrl");

        // Run the test
        final WxPayRespDTO result = wxStorePayServiceImplUnderTest.weChatPublic(wxH5PayReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockWxStoreSessionDetailsService).savePaidUser("storeGuid", "openId", WxH5PayReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("createName")
                        .headImgUrl("headImgUrl")
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("diningTableGuid")
                        .brandGuid("brandGuid")
                        .build())
                .outNotifyUrl("outNotifyUrl")
                .orderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed")
                .build());
        verify(mockWxStoreSessionDetailsService).saveTablePaidUser(WxStoreConsumerDTO.builder()
                .openId("openId")
                .nickName("createName")
                .headImgUrl("headImgUrl")
                .enterpriseGuid("enterpriseGuid")
                .enterpriseName("enterpriseName")
                .storeGuid("storeGuid")
                .storeName("storeName")
                .diningTableGuid("diningTableGuid")
                .brandGuid("brandGuid")
                .build());

        // Confirm WxStoreSessionDetailsService.savePayCallBack(...).
        final WeChatPayReqDTO weChatPayReqDTO = new WeChatPayReqDTO();
        weChatPayReqDTO.setDeviceType(0);
        weChatPayReqDTO.setDeviceId("openId");
        weChatPayReqDTO.setEnterpriseGuid("enterpriseGuid");
        weChatPayReqDTO.setStoreGuid("storeGuid");
        weChatPayReqDTO.setStoreName("storeName");
        weChatPayReqDTO.setUserGuid("openId");
        weChatPayReqDTO.setUserName("createName");
        weChatPayReqDTO.setOrderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        weChatPayReqDTO.setPayGuid("62fd3dbb-b68c-45a1-95a9-87508447c165");
        weChatPayReqDTO.setPayPowerId("payPowerId");
        weChatPayReqDTO.setSuccess(false);
        weChatPayReqDTO.setAmount(new BigDecimal("0.00"));
        weChatPayReqDTO.setOrderFee(new BigDecimal("0.00"));
        weChatPayReqDTO.setActuallyPayFee(new BigDecimal("0.00"));
        weChatPayReqDTO.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO1.setDiscountType(0);
        discountFeeDetailDTO1.setDiscountFee(new BigDecimal("0.00"));
        discountFeeDetailDTO1.setRule("rule");
        weChatPayReqDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        weChatPayReqDTO.setMemberGuid("memberGuid");
        verify(mockWxStoreSessionDetailsService).savePayCallBack("6e64ec83-01fe-4f5b-8759-73c1b66062ed",
                weChatPayReqDTO);

        // Confirm WxStoreDineInOrderClientService.lockorder(...).
        final OrderLockDTO orderLockDto = new OrderLockDTO();
        orderLockDto.setDeviceType(0);
        orderLockDto.setDeviceId("openId");
        orderLockDto.setEnterpriseGuid("enterpriseGuid");
        orderLockDto.setStoreGuid("storeGuid");
        orderLockDto.setStoreName("storeName");
        orderLockDto.setUserGuid("openId");
        orderLockDto.setUserName("createName");
        orderLockDto.setOrderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        verify(mockWxStoreDineInOrderClientService).lockorder(orderLockDto);
    }

    @Test
    public void testWxResultOperation() {
        // Setup
        final SaasNotifyDTO saasNotifyDTO = new SaasNotifyDTO();
        final AggPayPollingRespDTO aggPayPollingRespDTO = new AggPayPollingRespDTO();
        aggPayPollingRespDTO.setAmount(new BigDecimal("0.00"));
        aggPayPollingRespDTO.setPayPowerId("payPowerId");
        aggPayPollingRespDTO.setPaySt("paySt");
        aggPayPollingRespDTO.setOrderGUID("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        aggPayPollingRespDTO.setPayGUID("62fd3dbb-b68c-45a1-95a9-87508447c165");
        aggPayPollingRespDTO.setBankOrderNo("orderHolderNo");
        saasNotifyDTO.setAggPayPollingRespDTO(aggPayPollingRespDTO);
        final BaseInfo baseInfo = new BaseInfo();
        baseInfo.setDeviceType(0);
        baseInfo.setDeviceId("openId");
        baseInfo.setEnterpriseGuid("enterpriseGuid");
        baseInfo.setStoreGuid("storeGuid");
        baseInfo.setStoreName("storeName");
        baseInfo.setUserGuid("openId");
        baseInfo.setUserName("createName");
        saasNotifyDTO.setBaseInfo(baseInfo);
        final WxPayCallbackDTO wxStoreCallbackNotifyDTO = new WxPayCallbackDTO("storeGuid", "openId", saasNotifyDTO);

        // Configure UserMemberSessionUtils.getUserMemberSession(...).
        final UserMemberSessionDTO userMemberSessionDTO = new UserMemberSessionDTO("storeGuid", "openId", "guid", false,
                "nickName", "memberInfoGuid", "cardGuid", "memberInfoCardGuid", "systemManagementGuid", 0, false,
                "volumeCode", false, new BigDecimal("0.00"), new BigDecimal("0.00"), "operSubjectGuid");
        when(mockUserMemberSessionUtils.getUserMemberSession("openId")).thenReturn(userMemberSessionDTO);

        // Configure WxStoreSessionDetailsService.getPayCallBack(...).
        final WeChatPayReqDTO weChatPayReqDTO = new WeChatPayReqDTO();
        weChatPayReqDTO.setDeviceType(0);
        weChatPayReqDTO.setDeviceId("openId");
        weChatPayReqDTO.setEnterpriseGuid("enterpriseGuid");
        weChatPayReqDTO.setStoreGuid("storeGuid");
        weChatPayReqDTO.setStoreName("storeName");
        weChatPayReqDTO.setUserGuid("openId");
        weChatPayReqDTO.setUserName("createName");
        weChatPayReqDTO.setOrderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        weChatPayReqDTO.setPayGuid("62fd3dbb-b68c-45a1-95a9-87508447c165");
        weChatPayReqDTO.setPayPowerId("payPowerId");
        weChatPayReqDTO.setSuccess(false);
        weChatPayReqDTO.setAmount(new BigDecimal("0.00"));
        weChatPayReqDTO.setOrderFee(new BigDecimal("0.00"));
        weChatPayReqDTO.setActuallyPayFee(new BigDecimal("0.00"));
        weChatPayReqDTO.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        discountFeeDetailDTO.setRule("rule");
        weChatPayReqDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        weChatPayReqDTO.setMemberGuid("memberGuid");
        when(mockWxStoreSessionDetailsService.getPayCallBack("6e64ec83-01fe-4f5b-8759-73c1b66062ed"))
                .thenReturn(weChatPayReqDTO);

        // Configure WxStoreDineInOrderClientService.pay(...).
        final WeChatPayReqDTO weChatPayReqDTO1 = new WeChatPayReqDTO();
        weChatPayReqDTO1.setDeviceType(0);
        weChatPayReqDTO1.setDeviceId("openId");
        weChatPayReqDTO1.setEnterpriseGuid("enterpriseGuid");
        weChatPayReqDTO1.setStoreGuid("storeGuid");
        weChatPayReqDTO1.setStoreName("storeName");
        weChatPayReqDTO1.setUserGuid("openId");
        weChatPayReqDTO1.setUserName("createName");
        weChatPayReqDTO1.setOrderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        weChatPayReqDTO1.setPayGuid("62fd3dbb-b68c-45a1-95a9-87508447c165");
        weChatPayReqDTO1.setPayPowerId("payPowerId");
        weChatPayReqDTO1.setSuccess(false);
        weChatPayReqDTO1.setAmount(new BigDecimal("0.00"));
        weChatPayReqDTO1.setOrderFee(new BigDecimal("0.00"));
        weChatPayReqDTO1.setActuallyPayFee(new BigDecimal("0.00"));
        weChatPayReqDTO1.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO1.setDiscountType(0);
        discountFeeDetailDTO1.setDiscountFee(new BigDecimal("0.00"));
        discountFeeDetailDTO1.setRule("rule");
        weChatPayReqDTO1.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        weChatPayReqDTO1.setMemberGuid("memberGuid");
        when(mockWxStoreDineInOrderClientService.pay(weChatPayReqDTO1)).thenReturn(false);

        // Run the test
        final String result = wxStorePayServiceImplUnderTest.wxResultOperation(wxStoreCallbackNotifyDTO);

        // Verify the results
        assertEquals("success", result);
    }

    @Test
    public void testWxResultOperation_WxStoreSessionDetailsServiceGetPayCallBackReturnsNull() {
        // Setup
        final SaasNotifyDTO saasNotifyDTO = new SaasNotifyDTO();
        final AggPayPollingRespDTO aggPayPollingRespDTO = new AggPayPollingRespDTO();
        aggPayPollingRespDTO.setAmount(new BigDecimal("0.00"));
        aggPayPollingRespDTO.setPayPowerId("payPowerId");
        aggPayPollingRespDTO.setPaySt("paySt");
        aggPayPollingRespDTO.setOrderGUID("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        aggPayPollingRespDTO.setPayGUID("62fd3dbb-b68c-45a1-95a9-87508447c165");
        aggPayPollingRespDTO.setBankOrderNo("orderHolderNo");
        saasNotifyDTO.setAggPayPollingRespDTO(aggPayPollingRespDTO);
        final BaseInfo baseInfo = new BaseInfo();
        baseInfo.setDeviceType(0);
        baseInfo.setDeviceId("openId");
        baseInfo.setEnterpriseGuid("enterpriseGuid");
        baseInfo.setStoreGuid("storeGuid");
        baseInfo.setStoreName("storeName");
        baseInfo.setUserGuid("openId");
        baseInfo.setUserName("createName");
        saasNotifyDTO.setBaseInfo(baseInfo);
        final WxPayCallbackDTO wxStoreCallbackNotifyDTO = new WxPayCallbackDTO("storeGuid", "openId", saasNotifyDTO);

        // Configure UserMemberSessionUtils.getUserMemberSession(...).
        final UserMemberSessionDTO userMemberSessionDTO = new UserMemberSessionDTO("storeGuid", "openId", "guid", false,
                "nickName", "memberInfoGuid", "cardGuid", "memberInfoCardGuid", "systemManagementGuid", 0, false,
                "volumeCode", false, new BigDecimal("0.00"), new BigDecimal("0.00"), "operSubjectGuid");
        when(mockUserMemberSessionUtils.getUserMemberSession("openId")).thenReturn(userMemberSessionDTO);

        when(mockWxStoreSessionDetailsService.getPayCallBack("6e64ec83-01fe-4f5b-8759-73c1b66062ed")).thenReturn(null);

        // Run the test
        final String result = wxStorePayServiceImplUnderTest.wxResultOperation(wxStoreCallbackNotifyDTO);

        // Verify the results
        assertEquals("success", result);
    }

    @Test
    public void testWxResultOperation_WxStoreDineInOrderClientServicePayReturnsTrue() {
        // Setup
        final SaasNotifyDTO saasNotifyDTO = new SaasNotifyDTO();
        final AggPayPollingRespDTO aggPayPollingRespDTO = new AggPayPollingRespDTO();
        aggPayPollingRespDTO.setAmount(new BigDecimal("0.00"));
        aggPayPollingRespDTO.setPayPowerId("payPowerId");
        aggPayPollingRespDTO.setPaySt("paySt");
        aggPayPollingRespDTO.setOrderGUID("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        aggPayPollingRespDTO.setPayGUID("62fd3dbb-b68c-45a1-95a9-87508447c165");
        aggPayPollingRespDTO.setBankOrderNo("orderHolderNo");
        saasNotifyDTO.setAggPayPollingRespDTO(aggPayPollingRespDTO);
        final BaseInfo baseInfo = new BaseInfo();
        baseInfo.setDeviceType(0);
        baseInfo.setDeviceId("openId");
        baseInfo.setEnterpriseGuid("enterpriseGuid");
        baseInfo.setStoreGuid("storeGuid");
        baseInfo.setStoreName("storeName");
        baseInfo.setUserGuid("openId");
        baseInfo.setUserName("createName");
        saasNotifyDTO.setBaseInfo(baseInfo);
        final WxPayCallbackDTO wxStoreCallbackNotifyDTO = new WxPayCallbackDTO("storeGuid", "openId", saasNotifyDTO);

        // Configure UserMemberSessionUtils.getUserMemberSession(...).
        final UserMemberSessionDTO userMemberSessionDTO = new UserMemberSessionDTO("storeGuid", "openId", "guid", false,
                "nickName", "memberInfoGuid", "cardGuid", "memberInfoCardGuid", "systemManagementGuid", 0, false,
                "volumeCode", false, new BigDecimal("0.00"), new BigDecimal("0.00"), "operSubjectGuid");
        when(mockUserMemberSessionUtils.getUserMemberSession("openId")).thenReturn(userMemberSessionDTO);

        // Configure WxStoreSessionDetailsService.getPayCallBack(...).
        final WeChatPayReqDTO weChatPayReqDTO = new WeChatPayReqDTO();
        weChatPayReqDTO.setDeviceType(0);
        weChatPayReqDTO.setDeviceId("openId");
        weChatPayReqDTO.setEnterpriseGuid("enterpriseGuid");
        weChatPayReqDTO.setStoreGuid("storeGuid");
        weChatPayReqDTO.setStoreName("storeName");
        weChatPayReqDTO.setUserGuid("openId");
        weChatPayReqDTO.setUserName("createName");
        weChatPayReqDTO.setOrderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        weChatPayReqDTO.setPayGuid("62fd3dbb-b68c-45a1-95a9-87508447c165");
        weChatPayReqDTO.setPayPowerId("payPowerId");
        weChatPayReqDTO.setSuccess(false);
        weChatPayReqDTO.setAmount(new BigDecimal("0.00"));
        weChatPayReqDTO.setOrderFee(new BigDecimal("0.00"));
        weChatPayReqDTO.setActuallyPayFee(new BigDecimal("0.00"));
        weChatPayReqDTO.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        discountFeeDetailDTO.setRule("rule");
        weChatPayReqDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        weChatPayReqDTO.setMemberGuid("memberGuid");
        when(mockWxStoreSessionDetailsService.getPayCallBack("6e64ec83-01fe-4f5b-8759-73c1b66062ed"))
                .thenReturn(weChatPayReqDTO);

        // Configure WxStoreDineInOrderClientService.pay(...).
        final WeChatPayReqDTO weChatPayReqDTO1 = new WeChatPayReqDTO();
        weChatPayReqDTO1.setDeviceType(0);
        weChatPayReqDTO1.setDeviceId("openId");
        weChatPayReqDTO1.setEnterpriseGuid("enterpriseGuid");
        weChatPayReqDTO1.setStoreGuid("storeGuid");
        weChatPayReqDTO1.setStoreName("storeName");
        weChatPayReqDTO1.setUserGuid("openId");
        weChatPayReqDTO1.setUserName("createName");
        weChatPayReqDTO1.setOrderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        weChatPayReqDTO1.setPayGuid("62fd3dbb-b68c-45a1-95a9-87508447c165");
        weChatPayReqDTO1.setPayPowerId("payPowerId");
        weChatPayReqDTO1.setSuccess(false);
        weChatPayReqDTO1.setAmount(new BigDecimal("0.00"));
        weChatPayReqDTO1.setOrderFee(new BigDecimal("0.00"));
        weChatPayReqDTO1.setActuallyPayFee(new BigDecimal("0.00"));
        weChatPayReqDTO1.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO1.setDiscountType(0);
        discountFeeDetailDTO1.setDiscountFee(new BigDecimal("0.00"));
        discountFeeDetailDTO1.setRule("rule");
        weChatPayReqDTO1.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        weChatPayReqDTO1.setMemberGuid("memberGuid");
        when(mockWxStoreDineInOrderClientService.pay(weChatPayReqDTO1)).thenReturn(true);

        // Configure WxOrderRecordService.getByOrderGuid(...).
        final WxOrderRecordDO wxOrderRecordDO = new WxOrderRecordDO(0L, "2fd7df1e-4c6a-4209-95f7-d828f00d5ff0",
                "6e64ec83-01fe-4f5b-8759-73c1b66062ed", "merchantGuid", "guid", "brandGuid", "brandName", "logUrl",
                "storeGuid", "storeName", "areaGuid", "areaName", "tableGuid", "tableCode", 0, 0, "orderStateName",
                new BigDecimal("0.00"), new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName",
                "orderHolderNo", LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0);
        when(mockWxOrderRecordService.getByOrderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed"))
                .thenReturn(wxOrderRecordDO);

        // Configure WxUserRecordService.getOneByOpenId(...).
        final WxUserRecordDO wxUserRecordDO = new WxUserRecordDO(0L, "guid", "openId", "nickName", "headImgUrl", 0,
                "country", "province", "city", "phone", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid", 0);
        when(mockWxUserRecordService.getOneByOpenId("openId")).thenReturn(wxUserRecordDO);

        // Configure WxStoreDineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        dineinOrderDetailRespDTO.setStoreGuid("storeGuid");
        dineinOrderDetailRespDTO.setStoreName("storeName");
        dineinOrderDetailRespDTO.setOriginalOrderGuid(0L);
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setDeviceType(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setAppendFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setChangeFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO2 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO2.setDiscountType(0);
        discountFeeDetailDTO2.setDiscountFee(new BigDecimal("0.00"));
        discountFeeDetailDTO2.setRule("rule");
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO2));
        dineinOrderDetailRespDTO.setOrderNo("body");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setStateName("已支付");
        dineinOrderDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setMemberGuid("memberGuid");
        dineinOrderDetailRespDTO.setMemberPhone("memberPhone");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        final ResponseIntegralOffset integralOffsetResultRespDTO = new ResponseIntegralOffset();
        integralOffsetResultRespDTO.setUseIntegral(0);
        integralOffsetResultRespDTO.setDeductionMoney(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setIntegralOffsetResultRespDTO(integralOffsetResultRespDTO);
        dineinOrderDetailRespDTO.setTip("errorMsg");
        dineinOrderDetailRespDTO.setVersion(0);
        when(mockWxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder()
                .data("6e64ec83-01fe-4f5b-8759-73c1b66062ed")
                .build())).thenReturn(dineinOrderDetailRespDTO);

        when(mockWeChatConfig.getMEMBER_ORDER_PAGE()).thenReturn("result");
        when(mockRedisUtils.hKeyList("key")).thenReturn(new HashSet<>(Arrays.asList("value")));
        when(mockRedisUtils.get("key")).thenReturn("result");

        // Configure WxStoreEstimateClientService.verifyDineInItemEstimate(...).
        final EstimateResultRespDTO estimateResultRespDTO = EstimateResultRespDTO.builder()
                .success(false)
                .build();
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setGuid("77883994-00fe-4d96-983a-882ed1d82107");
        dineInItemDTO1.setOrderGuid("orderGuid");
        dineInItemDTO1.setOriginalOrderItemGuid(0L);
        dineInItemDTO1.setAdjustNumber(new BigDecimal("0.00"));
        dineInItemDTO1.setRollbackCount(new BigDecimal("0.00"));
        final List<DineInItemDTO> request = Arrays.asList(dineInItemDTO1);
        when(mockWxStoreEstimateClientService.verifyDineInItemEstimate(request)).thenReturn(estimateResultRespDTO);

        // Configure HsaBaseClientService.getMemberInfo(...).
        final ResponseMemberInfo responseMemberInfo = new ResponseMemberInfo();
        responseMemberInfo.setQrcode("qrcode");
        responseMemberInfo.setNickName("nickName");
        responseMemberInfo.setUserName("userName");
        responseMemberInfo.setOperationMemberInfoCardLevelGuid("memberInfoGradeGuid");
        responseMemberInfo.setLabelSettingGuid(Arrays.asList("value"));
        final ResponseModel<ResponseMemberInfo> responseMemberInfoResponseModel = new ResponseModel<>(
                responseMemberInfo);
        final RequestQueryMemberInfo requestQueryMemberInfo = new RequestQueryMemberInfo();
        requestQueryMemberInfo.setMemberInfoGuid("memberGuid");
        requestQueryMemberInfo.setUnionId("unionId");
        requestQueryMemberInfo.setOpenId("openId");
        requestQueryMemberInfo.setPhoneNum("phoneNum");
        requestQueryMemberInfo.setOperSubjectGuid("operSubjectGuid");
        when(mockHsaBaseClientService.getMemberInfo(requestQueryMemberInfo))
                .thenReturn(responseMemberInfoResponseModel);

        // Run the test
        final String result = wxStorePayServiceImplUnderTest.wxResultOperation(wxStoreCallbackNotifyDTO);

        // Verify the results
        assertEquals("success", result);

        // Confirm WxStoreDineInOrderClientService.unLockOrder(...).
        final OrderLockDTO orderLockDto = new OrderLockDTO();
        orderLockDto.setDeviceType(0);
        orderLockDto.setDeviceId("openId");
        orderLockDto.setEnterpriseGuid("enterpriseGuid");
        orderLockDto.setStoreGuid("storeGuid");
        orderLockDto.setStoreName("storeName");
        orderLockDto.setUserGuid("openId");
        orderLockDto.setUserName("createName");
        orderLockDto.setOrderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        verify(mockWxStoreDineInOrderClientService).unLockOrder(orderLockDto);
        verify(mockWxStoreSessionDetailsService).delPayCallBack("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        verify(mockWxOrderRecordService).saveOrUpdate(
                new WxOrderRecordDO(0L, "2fd7df1e-4c6a-4209-95f7-d828f00d5ff0", "6e64ec83-01fe-4f5b-8759-73c1b66062ed",
                        "merchantGuid", "guid", "brandGuid", "brandName", "logUrl", "storeGuid", "storeName",
                        "areaGuid", "areaName", "tableGuid", "tableCode", 0, 0, "orderStateName",
                        new BigDecimal("0.00"), new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode",
                        "itemName", "orderHolderNo", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0));
        verify(mockWxOpenMessageService).sendMemberMsgNew(WxMemberConsumeMsgDTO.builder()
                .openId("openId")
                .brandGuid("brandGuid")
                .storeGuid("storeGuid")
                .first("first")
                .remark("remark")
                .url("url")
                .wxTemplateTypeEnum(WxTemplateTypeEnum.QUEUE_TEMPLATE)
                .build());
        verify(mockWebsocketMessageHelper).sendOrderEmqMessage("tableGuid", new HashSet<>(Arrays.asList("value")));
        verify(mockRedisUtils).delete("key");
        verify(mockWxStoreMerchantOrderService).dealUnFinishedOrders("6e64ec83-01fe-4f5b-8759-73c1b66062ed");

        // Confirm WxStoreMerchantOrderService.pushFastMsg(...).
        final WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO.setGuid("0b550432-c989-4b0f-b462-d30b2855c69f");
        wxStoreMerchantOrderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setTotalPrice(new BigDecimal("0.00"));
        wxStoreMerchantOrderDTO.setOpenId("openId");
        verify(mockWxStoreMerchantOrderService).pushFastMsg(wxStoreMerchantOrderDTO);

        // Confirm MemberMarketingClientService.dealConsumptionGift(...).
        final MemberConsumptionGiftDTO memberConsumptionGiftQO = new MemberConsumptionGiftDTO();
        memberConsumptionGiftQO.setOperSubjectGuid("operSubjectGuid");
        memberConsumptionGiftQO.setEnterpriseGuid("enterpriseGuid");
        memberConsumptionGiftQO.setMemberInfoGuid("memberGuid");
        memberConsumptionGiftQO.setOrderAmount(new BigDecimal("0.00"));
        memberConsumptionGiftQO.setOrderNumber("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        memberConsumptionGiftQO.setRechargeStatus(0);
        memberConsumptionGiftQO.setMemberName("memberName");
        memberConsumptionGiftQO.setMemberPhone("memberPhone");
        memberConsumptionGiftQO.setMemberLabelGuid(Arrays.asList("value"));
        memberConsumptionGiftQO.setMemberInfoGradeGuid("memberInfoGradeGuid");
        memberConsumptionGiftQO.setStoreGuid("storeGuid");
        memberConsumptionGiftQO.setStoreName("storeName");
        memberConsumptionGiftQO.setChangeSource("微信");
        memberConsumptionGiftQO.setOrderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        memberConsumptionGiftQO.setActivitySceneType(0);
        verify(mockMemberMarketingClientService).dealConsumptionGift(memberConsumptionGiftQO);
        verify(mockUserMemberSessionUtils).delAllUserSession(new HashSet<>(Arrays.asList("value")));
        verify(mockUserMemberSessionUtils).delTableCardList("storeGuid", new HashSet<>(Arrays.asList("value")));
        verify(mockWxStoreTradeOrderService).removeFastGuestCount("tableGuid", "openId");
        verify(mockWxStoreTradeOrderService).transformSurchargeCache("tableGuid",
                "6e64ec83-01fe-4f5b-8759-73c1b66062ed");

        // Confirm HsaBaseClientService.batchAddManualLabel(...).
        final RequestManualLabel req = new RequestManualLabel();
        req.setMemberInfoGuidArray("memberGuid");
        req.setLabelSettingGuid("labelSettingGuid");
        verify(mockHsaBaseClientService).batchAddManualLabel(req);
    }

    @Test(expected = BusinessException.class)
    public void testWxResultOperation_WxUserRecordServiceReturnsNull() {
        // Setup
        final SaasNotifyDTO saasNotifyDTO = new SaasNotifyDTO();
        final AggPayPollingRespDTO aggPayPollingRespDTO = new AggPayPollingRespDTO();
        aggPayPollingRespDTO.setAmount(new BigDecimal("0.00"));
        aggPayPollingRespDTO.setPayPowerId("payPowerId");
        aggPayPollingRespDTO.setPaySt("paySt");
        aggPayPollingRespDTO.setOrderGUID("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        aggPayPollingRespDTO.setPayGUID("62fd3dbb-b68c-45a1-95a9-87508447c165");
        aggPayPollingRespDTO.setBankOrderNo("orderHolderNo");
        saasNotifyDTO.setAggPayPollingRespDTO(aggPayPollingRespDTO);
        final BaseInfo baseInfo = new BaseInfo();
        baseInfo.setDeviceType(0);
        baseInfo.setDeviceId("openId");
        baseInfo.setEnterpriseGuid("enterpriseGuid");
        baseInfo.setStoreGuid("storeGuid");
        baseInfo.setStoreName("storeName");
        baseInfo.setUserGuid("openId");
        baseInfo.setUserName("createName");
        saasNotifyDTO.setBaseInfo(baseInfo);
        final WxPayCallbackDTO wxStoreCallbackNotifyDTO = new WxPayCallbackDTO("storeGuid", "openId", saasNotifyDTO);

        // Configure UserMemberSessionUtils.getUserMemberSession(...).
        final UserMemberSessionDTO userMemberSessionDTO = new UserMemberSessionDTO("storeGuid", "openId", "guid", false,
                "nickName", "memberInfoGuid", "cardGuid", "memberInfoCardGuid", "systemManagementGuid", 0, false,
                "volumeCode", false, new BigDecimal("0.00"), new BigDecimal("0.00"), "operSubjectGuid");
        when(mockUserMemberSessionUtils.getUserMemberSession("openId")).thenReturn(userMemberSessionDTO);

        // Configure WxStoreSessionDetailsService.getPayCallBack(...).
        final WeChatPayReqDTO weChatPayReqDTO = new WeChatPayReqDTO();
        weChatPayReqDTO.setDeviceType(0);
        weChatPayReqDTO.setDeviceId("openId");
        weChatPayReqDTO.setEnterpriseGuid("enterpriseGuid");
        weChatPayReqDTO.setStoreGuid("storeGuid");
        weChatPayReqDTO.setStoreName("storeName");
        weChatPayReqDTO.setUserGuid("openId");
        weChatPayReqDTO.setUserName("createName");
        weChatPayReqDTO.setOrderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        weChatPayReqDTO.setPayGuid("62fd3dbb-b68c-45a1-95a9-87508447c165");
        weChatPayReqDTO.setPayPowerId("payPowerId");
        weChatPayReqDTO.setSuccess(false);
        weChatPayReqDTO.setAmount(new BigDecimal("0.00"));
        weChatPayReqDTO.setOrderFee(new BigDecimal("0.00"));
        weChatPayReqDTO.setActuallyPayFee(new BigDecimal("0.00"));
        weChatPayReqDTO.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        discountFeeDetailDTO.setRule("rule");
        weChatPayReqDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        weChatPayReqDTO.setMemberGuid("memberGuid");
        when(mockWxStoreSessionDetailsService.getPayCallBack("6e64ec83-01fe-4f5b-8759-73c1b66062ed"))
                .thenReturn(weChatPayReqDTO);

        // Configure WxStoreDineInOrderClientService.pay(...).
        final WeChatPayReqDTO weChatPayReqDTO1 = new WeChatPayReqDTO();
        weChatPayReqDTO1.setDeviceType(0);
        weChatPayReqDTO1.setDeviceId("openId");
        weChatPayReqDTO1.setEnterpriseGuid("enterpriseGuid");
        weChatPayReqDTO1.setStoreGuid("storeGuid");
        weChatPayReqDTO1.setStoreName("storeName");
        weChatPayReqDTO1.setUserGuid("openId");
        weChatPayReqDTO1.setUserName("createName");
        weChatPayReqDTO1.setOrderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        weChatPayReqDTO1.setPayGuid("62fd3dbb-b68c-45a1-95a9-87508447c165");
        weChatPayReqDTO1.setPayPowerId("payPowerId");
        weChatPayReqDTO1.setSuccess(false);
        weChatPayReqDTO1.setAmount(new BigDecimal("0.00"));
        weChatPayReqDTO1.setOrderFee(new BigDecimal("0.00"));
        weChatPayReqDTO1.setActuallyPayFee(new BigDecimal("0.00"));
        weChatPayReqDTO1.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO1.setDiscountType(0);
        discountFeeDetailDTO1.setDiscountFee(new BigDecimal("0.00"));
        discountFeeDetailDTO1.setRule("rule");
        weChatPayReqDTO1.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        weChatPayReqDTO1.setMemberGuid("memberGuid");
        when(mockWxStoreDineInOrderClientService.pay(weChatPayReqDTO1)).thenReturn(true);

        // Configure WxOrderRecordService.getByOrderGuid(...).
        final WxOrderRecordDO wxOrderRecordDO = new WxOrderRecordDO(0L, "2fd7df1e-4c6a-4209-95f7-d828f00d5ff0",
                "6e64ec83-01fe-4f5b-8759-73c1b66062ed", "merchantGuid", "guid", "brandGuid", "brandName", "logUrl",
                "storeGuid", "storeName", "areaGuid", "areaName", "tableGuid", "tableCode", 0, 0, "orderStateName",
                new BigDecimal("0.00"), new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName",
                "orderHolderNo", LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0);
        when(mockWxOrderRecordService.getByOrderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed"))
                .thenReturn(wxOrderRecordDO);

        when(mockWxUserRecordService.getOneByOpenId("openId")).thenReturn(null);

        // Run the test
        wxStorePayServiceImplUnderTest.wxResultOperation(wxStoreCallbackNotifyDTO);
    }

    @Test
    public void testWxResultOperation_WxStoreDineInOrderClientServiceGetOrderDetailReturnsNull() {
        // Setup
        final SaasNotifyDTO saasNotifyDTO = new SaasNotifyDTO();
        final AggPayPollingRespDTO aggPayPollingRespDTO = new AggPayPollingRespDTO();
        aggPayPollingRespDTO.setAmount(new BigDecimal("0.00"));
        aggPayPollingRespDTO.setPayPowerId("payPowerId");
        aggPayPollingRespDTO.setPaySt("paySt");
        aggPayPollingRespDTO.setOrderGUID("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        aggPayPollingRespDTO.setPayGUID("62fd3dbb-b68c-45a1-95a9-87508447c165");
        aggPayPollingRespDTO.setBankOrderNo("orderHolderNo");
        saasNotifyDTO.setAggPayPollingRespDTO(aggPayPollingRespDTO);
        final BaseInfo baseInfo = new BaseInfo();
        baseInfo.setDeviceType(0);
        baseInfo.setDeviceId("openId");
        baseInfo.setEnterpriseGuid("enterpriseGuid");
        baseInfo.setStoreGuid("storeGuid");
        baseInfo.setStoreName("storeName");
        baseInfo.setUserGuid("openId");
        baseInfo.setUserName("createName");
        saasNotifyDTO.setBaseInfo(baseInfo);
        final WxPayCallbackDTO wxStoreCallbackNotifyDTO = new WxPayCallbackDTO("storeGuid", "openId", saasNotifyDTO);

        // Configure UserMemberSessionUtils.getUserMemberSession(...).
        final UserMemberSessionDTO userMemberSessionDTO = new UserMemberSessionDTO("storeGuid", "openId", "guid", false,
                "nickName", "memberInfoGuid", "cardGuid", "memberInfoCardGuid", "systemManagementGuid", 0, false,
                "volumeCode", false, new BigDecimal("0.00"), new BigDecimal("0.00"), "operSubjectGuid");
        when(mockUserMemberSessionUtils.getUserMemberSession("openId")).thenReturn(userMemberSessionDTO);

        // Configure WxStoreSessionDetailsService.getPayCallBack(...).
        final WeChatPayReqDTO weChatPayReqDTO = new WeChatPayReqDTO();
        weChatPayReqDTO.setDeviceType(0);
        weChatPayReqDTO.setDeviceId("openId");
        weChatPayReqDTO.setEnterpriseGuid("enterpriseGuid");
        weChatPayReqDTO.setStoreGuid("storeGuid");
        weChatPayReqDTO.setStoreName("storeName");
        weChatPayReqDTO.setUserGuid("openId");
        weChatPayReqDTO.setUserName("createName");
        weChatPayReqDTO.setOrderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        weChatPayReqDTO.setPayGuid("62fd3dbb-b68c-45a1-95a9-87508447c165");
        weChatPayReqDTO.setPayPowerId("payPowerId");
        weChatPayReqDTO.setSuccess(false);
        weChatPayReqDTO.setAmount(new BigDecimal("0.00"));
        weChatPayReqDTO.setOrderFee(new BigDecimal("0.00"));
        weChatPayReqDTO.setActuallyPayFee(new BigDecimal("0.00"));
        weChatPayReqDTO.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        discountFeeDetailDTO.setRule("rule");
        weChatPayReqDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        weChatPayReqDTO.setMemberGuid("memberGuid");
        when(mockWxStoreSessionDetailsService.getPayCallBack("6e64ec83-01fe-4f5b-8759-73c1b66062ed"))
                .thenReturn(weChatPayReqDTO);

        // Configure WxStoreDineInOrderClientService.pay(...).
        final WeChatPayReqDTO weChatPayReqDTO1 = new WeChatPayReqDTO();
        weChatPayReqDTO1.setDeviceType(0);
        weChatPayReqDTO1.setDeviceId("openId");
        weChatPayReqDTO1.setEnterpriseGuid("enterpriseGuid");
        weChatPayReqDTO1.setStoreGuid("storeGuid");
        weChatPayReqDTO1.setStoreName("storeName");
        weChatPayReqDTO1.setUserGuid("openId");
        weChatPayReqDTO1.setUserName("createName");
        weChatPayReqDTO1.setOrderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        weChatPayReqDTO1.setPayGuid("62fd3dbb-b68c-45a1-95a9-87508447c165");
        weChatPayReqDTO1.setPayPowerId("payPowerId");
        weChatPayReqDTO1.setSuccess(false);
        weChatPayReqDTO1.setAmount(new BigDecimal("0.00"));
        weChatPayReqDTO1.setOrderFee(new BigDecimal("0.00"));
        weChatPayReqDTO1.setActuallyPayFee(new BigDecimal("0.00"));
        weChatPayReqDTO1.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO1.setDiscountType(0);
        discountFeeDetailDTO1.setDiscountFee(new BigDecimal("0.00"));
        discountFeeDetailDTO1.setRule("rule");
        weChatPayReqDTO1.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        weChatPayReqDTO1.setMemberGuid("memberGuid");
        when(mockWxStoreDineInOrderClientService.pay(weChatPayReqDTO1)).thenReturn(true);

        // Configure WxOrderRecordService.getByOrderGuid(...).
        final WxOrderRecordDO wxOrderRecordDO = new WxOrderRecordDO(0L, "2fd7df1e-4c6a-4209-95f7-d828f00d5ff0",
                "6e64ec83-01fe-4f5b-8759-73c1b66062ed", "merchantGuid", "guid", "brandGuid", "brandName", "logUrl",
                "storeGuid", "storeName", "areaGuid", "areaName", "tableGuid", "tableCode", 0, 0, "orderStateName",
                new BigDecimal("0.00"), new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName",
                "orderHolderNo", LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0);
        when(mockWxOrderRecordService.getByOrderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed"))
                .thenReturn(wxOrderRecordDO);

        // Configure WxUserRecordService.getOneByOpenId(...).
        final WxUserRecordDO wxUserRecordDO = new WxUserRecordDO(0L, "guid", "openId", "nickName", "headImgUrl", 0,
                "country", "province", "city", "phone", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid", 0);
        when(mockWxUserRecordService.getOneByOpenId("openId")).thenReturn(wxUserRecordDO);

        when(mockWxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder()
                .data("6e64ec83-01fe-4f5b-8759-73c1b66062ed")
                .build())).thenReturn(null);
        when(mockWeChatConfig.getMEMBER_ORDER_PAGE()).thenReturn("result");
        when(mockRedisUtils.hKeyList("key")).thenReturn(new HashSet<>(Arrays.asList("value")));
        when(mockRedisUtils.get("key")).thenReturn("result");

        // Configure WxStoreEstimateClientService.verifyDineInItemEstimate(...).
        final EstimateResultRespDTO estimateResultRespDTO = EstimateResultRespDTO.builder()
                .success(false)
                .build();
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setGuid("77883994-00fe-4d96-983a-882ed1d82107");
        dineInItemDTO.setOrderGuid("orderGuid");
        dineInItemDTO.setOriginalOrderItemGuid(0L);
        dineInItemDTO.setAdjustNumber(new BigDecimal("0.00"));
        dineInItemDTO.setRollbackCount(new BigDecimal("0.00"));
        final List<DineInItemDTO> request = Arrays.asList(dineInItemDTO);
        when(mockWxStoreEstimateClientService.verifyDineInItemEstimate(request)).thenReturn(estimateResultRespDTO);

        // Configure HsaBaseClientService.getMemberInfo(...).
        final ResponseMemberInfo responseMemberInfo = new ResponseMemberInfo();
        responseMemberInfo.setQrcode("qrcode");
        responseMemberInfo.setNickName("nickName");
        responseMemberInfo.setUserName("userName");
        responseMemberInfo.setOperationMemberInfoCardLevelGuid("memberInfoGradeGuid");
        responseMemberInfo.setLabelSettingGuid(Arrays.asList("value"));
        final ResponseModel<ResponseMemberInfo> responseMemberInfoResponseModel = new ResponseModel<>(
                responseMemberInfo);
        final RequestQueryMemberInfo requestQueryMemberInfo = new RequestQueryMemberInfo();
        requestQueryMemberInfo.setMemberInfoGuid("memberGuid");
        requestQueryMemberInfo.setUnionId("unionId");
        requestQueryMemberInfo.setOpenId("openId");
        requestQueryMemberInfo.setPhoneNum("phoneNum");
        requestQueryMemberInfo.setOperSubjectGuid("operSubjectGuid");
        when(mockHsaBaseClientService.getMemberInfo(requestQueryMemberInfo))
                .thenReturn(responseMemberInfoResponseModel);

        // Run the test
        final String result = wxStorePayServiceImplUnderTest.wxResultOperation(wxStoreCallbackNotifyDTO);

        // Verify the results
        assertEquals("success", result);

        // Confirm WxStoreDineInOrderClientService.unLockOrder(...).
        final OrderLockDTO orderLockDto = new OrderLockDTO();
        orderLockDto.setDeviceType(0);
        orderLockDto.setDeviceId("openId");
        orderLockDto.setEnterpriseGuid("enterpriseGuid");
        orderLockDto.setStoreGuid("storeGuid");
        orderLockDto.setStoreName("storeName");
        orderLockDto.setUserGuid("openId");
        orderLockDto.setUserName("createName");
        orderLockDto.setOrderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        verify(mockWxStoreDineInOrderClientService).unLockOrder(orderLockDto);
        verify(mockWxStoreSessionDetailsService).delPayCallBack("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        verify(mockWxOrderRecordService).saveOrUpdate(
                new WxOrderRecordDO(0L, "2fd7df1e-4c6a-4209-95f7-d828f00d5ff0", "6e64ec83-01fe-4f5b-8759-73c1b66062ed",
                        "merchantGuid", "guid", "brandGuid", "brandName", "logUrl", "storeGuid", "storeName",
                        "areaGuid", "areaName", "tableGuid", "tableCode", 0, 0, "orderStateName",
                        new BigDecimal("0.00"), new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode",
                        "itemName", "orderHolderNo", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0));
        verify(mockWxOpenMessageService).sendMemberMsgNew(WxMemberConsumeMsgDTO.builder()
                .openId("openId")
                .brandGuid("brandGuid")
                .storeGuid("storeGuid")
                .first("first")
                .remark("remark")
                .url("url")
                .wxTemplateTypeEnum(WxTemplateTypeEnum.QUEUE_TEMPLATE)
                .build());
        verify(mockWebsocketMessageHelper).sendOrderEmqMessage("tableGuid", new HashSet<>(Arrays.asList("value")));
        verify(mockRedisUtils).delete("key");
        verify(mockWxStoreMerchantOrderService).dealUnFinishedOrders("6e64ec83-01fe-4f5b-8759-73c1b66062ed");

        // Confirm WxStoreMerchantOrderService.pushFastMsg(...).
        final WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO.setGuid("0b550432-c989-4b0f-b462-d30b2855c69f");
        wxStoreMerchantOrderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setTotalPrice(new BigDecimal("0.00"));
        wxStoreMerchantOrderDTO.setOpenId("openId");
        verify(mockWxStoreMerchantOrderService).pushFastMsg(wxStoreMerchantOrderDTO);

        // Confirm MemberMarketingClientService.dealConsumptionGift(...).
        final MemberConsumptionGiftDTO memberConsumptionGiftQO = new MemberConsumptionGiftDTO();
        memberConsumptionGiftQO.setOperSubjectGuid("operSubjectGuid");
        memberConsumptionGiftQO.setEnterpriseGuid("enterpriseGuid");
        memberConsumptionGiftQO.setMemberInfoGuid("memberGuid");
        memberConsumptionGiftQO.setOrderAmount(new BigDecimal("0.00"));
        memberConsumptionGiftQO.setOrderNumber("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        memberConsumptionGiftQO.setRechargeStatus(0);
        memberConsumptionGiftQO.setMemberName("memberName");
        memberConsumptionGiftQO.setMemberPhone("memberPhone");
        memberConsumptionGiftQO.setMemberLabelGuid(Arrays.asList("value"));
        memberConsumptionGiftQO.setMemberInfoGradeGuid("memberInfoGradeGuid");
        memberConsumptionGiftQO.setStoreGuid("storeGuid");
        memberConsumptionGiftQO.setStoreName("storeName");
        memberConsumptionGiftQO.setChangeSource("微信");
        memberConsumptionGiftQO.setOrderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        memberConsumptionGiftQO.setActivitySceneType(0);
        verify(mockMemberMarketingClientService).dealConsumptionGift(memberConsumptionGiftQO);
        verify(mockUserMemberSessionUtils).delAllUserSession(new HashSet<>(Arrays.asList("value")));
        verify(mockUserMemberSessionUtils).delTableCardList("storeGuid", new HashSet<>(Arrays.asList("value")));
        verify(mockWxStoreTradeOrderService).removeFastGuestCount("tableGuid", "openId");
        verify(mockWxStoreTradeOrderService).transformSurchargeCache("tableGuid",
                "6e64ec83-01fe-4f5b-8759-73c1b66062ed");

        // Confirm HsaBaseClientService.batchAddManualLabel(...).
        final RequestManualLabel req = new RequestManualLabel();
        req.setMemberInfoGuidArray("memberGuid");
        req.setLabelSettingGuid("labelSettingGuid");
        verify(mockHsaBaseClientService).batchAddManualLabel(req);
    }

    @Test
    public void testWxResultOperation_RedisUtilsHKeyListReturnsNoItems() {
        // Setup
        final SaasNotifyDTO saasNotifyDTO = new SaasNotifyDTO();
        final AggPayPollingRespDTO aggPayPollingRespDTO = new AggPayPollingRespDTO();
        aggPayPollingRespDTO.setAmount(new BigDecimal("0.00"));
        aggPayPollingRespDTO.setPayPowerId("payPowerId");
        aggPayPollingRespDTO.setPaySt("paySt");
        aggPayPollingRespDTO.setOrderGUID("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        aggPayPollingRespDTO.setPayGUID("62fd3dbb-b68c-45a1-95a9-87508447c165");
        aggPayPollingRespDTO.setBankOrderNo("orderHolderNo");
        saasNotifyDTO.setAggPayPollingRespDTO(aggPayPollingRespDTO);
        final BaseInfo baseInfo = new BaseInfo();
        baseInfo.setDeviceType(0);
        baseInfo.setDeviceId("openId");
        baseInfo.setEnterpriseGuid("enterpriseGuid");
        baseInfo.setStoreGuid("storeGuid");
        baseInfo.setStoreName("storeName");
        baseInfo.setUserGuid("openId");
        baseInfo.setUserName("createName");
        saasNotifyDTO.setBaseInfo(baseInfo);
        final WxPayCallbackDTO wxStoreCallbackNotifyDTO = new WxPayCallbackDTO("storeGuid", "openId", saasNotifyDTO);

        // Configure UserMemberSessionUtils.getUserMemberSession(...).
        final UserMemberSessionDTO userMemberSessionDTO = new UserMemberSessionDTO("storeGuid", "openId", "guid", false,
                "nickName", "memberInfoGuid", "cardGuid", "memberInfoCardGuid", "systemManagementGuid", 0, false,
                "volumeCode", false, new BigDecimal("0.00"), new BigDecimal("0.00"), "operSubjectGuid");
        when(mockUserMemberSessionUtils.getUserMemberSession("openId")).thenReturn(userMemberSessionDTO);

        // Configure WxStoreSessionDetailsService.getPayCallBack(...).
        final WeChatPayReqDTO weChatPayReqDTO = new WeChatPayReqDTO();
        weChatPayReqDTO.setDeviceType(0);
        weChatPayReqDTO.setDeviceId("openId");
        weChatPayReqDTO.setEnterpriseGuid("enterpriseGuid");
        weChatPayReqDTO.setStoreGuid("storeGuid");
        weChatPayReqDTO.setStoreName("storeName");
        weChatPayReqDTO.setUserGuid("openId");
        weChatPayReqDTO.setUserName("createName");
        weChatPayReqDTO.setOrderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        weChatPayReqDTO.setPayGuid("62fd3dbb-b68c-45a1-95a9-87508447c165");
        weChatPayReqDTO.setPayPowerId("payPowerId");
        weChatPayReqDTO.setSuccess(false);
        weChatPayReqDTO.setAmount(new BigDecimal("0.00"));
        weChatPayReqDTO.setOrderFee(new BigDecimal("0.00"));
        weChatPayReqDTO.setActuallyPayFee(new BigDecimal("0.00"));
        weChatPayReqDTO.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        discountFeeDetailDTO.setRule("rule");
        weChatPayReqDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        weChatPayReqDTO.setMemberGuid("memberGuid");
        when(mockWxStoreSessionDetailsService.getPayCallBack("6e64ec83-01fe-4f5b-8759-73c1b66062ed"))
                .thenReturn(weChatPayReqDTO);

        // Configure WxStoreDineInOrderClientService.pay(...).
        final WeChatPayReqDTO weChatPayReqDTO1 = new WeChatPayReqDTO();
        weChatPayReqDTO1.setDeviceType(0);
        weChatPayReqDTO1.setDeviceId("openId");
        weChatPayReqDTO1.setEnterpriseGuid("enterpriseGuid");
        weChatPayReqDTO1.setStoreGuid("storeGuid");
        weChatPayReqDTO1.setStoreName("storeName");
        weChatPayReqDTO1.setUserGuid("openId");
        weChatPayReqDTO1.setUserName("createName");
        weChatPayReqDTO1.setOrderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        weChatPayReqDTO1.setPayGuid("62fd3dbb-b68c-45a1-95a9-87508447c165");
        weChatPayReqDTO1.setPayPowerId("payPowerId");
        weChatPayReqDTO1.setSuccess(false);
        weChatPayReqDTO1.setAmount(new BigDecimal("0.00"));
        weChatPayReqDTO1.setOrderFee(new BigDecimal("0.00"));
        weChatPayReqDTO1.setActuallyPayFee(new BigDecimal("0.00"));
        weChatPayReqDTO1.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO1.setDiscountType(0);
        discountFeeDetailDTO1.setDiscountFee(new BigDecimal("0.00"));
        discountFeeDetailDTO1.setRule("rule");
        weChatPayReqDTO1.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        weChatPayReqDTO1.setMemberGuid("memberGuid");
        when(mockWxStoreDineInOrderClientService.pay(weChatPayReqDTO1)).thenReturn(true);

        // Configure WxOrderRecordService.getByOrderGuid(...).
        final WxOrderRecordDO wxOrderRecordDO = new WxOrderRecordDO(0L, "2fd7df1e-4c6a-4209-95f7-d828f00d5ff0",
                "6e64ec83-01fe-4f5b-8759-73c1b66062ed", "merchantGuid", "guid", "brandGuid", "brandName", "logUrl",
                "storeGuid", "storeName", "areaGuid", "areaName", "tableGuid", "tableCode", 0, 0, "orderStateName",
                new BigDecimal("0.00"), new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName",
                "orderHolderNo", LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0);
        when(mockWxOrderRecordService.getByOrderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed"))
                .thenReturn(wxOrderRecordDO);

        // Configure WxUserRecordService.getOneByOpenId(...).
        final WxUserRecordDO wxUserRecordDO = new WxUserRecordDO(0L, "guid", "openId", "nickName", "headImgUrl", 0,
                "country", "province", "city", "phone", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid", 0);
        when(mockWxUserRecordService.getOneByOpenId("openId")).thenReturn(wxUserRecordDO);

        // Configure WxStoreDineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        dineinOrderDetailRespDTO.setStoreGuid("storeGuid");
        dineinOrderDetailRespDTO.setStoreName("storeName");
        dineinOrderDetailRespDTO.setOriginalOrderGuid(0L);
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setDeviceType(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setAppendFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setChangeFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO2 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO2.setDiscountType(0);
        discountFeeDetailDTO2.setDiscountFee(new BigDecimal("0.00"));
        discountFeeDetailDTO2.setRule("rule");
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO2));
        dineinOrderDetailRespDTO.setOrderNo("body");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setStateName("已支付");
        dineinOrderDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setMemberGuid("memberGuid");
        dineinOrderDetailRespDTO.setMemberPhone("memberPhone");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        final ResponseIntegralOffset integralOffsetResultRespDTO = new ResponseIntegralOffset();
        integralOffsetResultRespDTO.setUseIntegral(0);
        integralOffsetResultRespDTO.setDeductionMoney(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setIntegralOffsetResultRespDTO(integralOffsetResultRespDTO);
        dineinOrderDetailRespDTO.setTip("errorMsg");
        dineinOrderDetailRespDTO.setVersion(0);
        when(mockWxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder()
                .data("6e64ec83-01fe-4f5b-8759-73c1b66062ed")
                .build())).thenReturn(dineinOrderDetailRespDTO);

        when(mockWeChatConfig.getMEMBER_ORDER_PAGE()).thenReturn("result");
        when(mockRedisUtils.hKeyList("key")).thenReturn(Collections.emptySet());

        // Configure HsaBaseClientService.getMemberInfo(...).
        final ResponseMemberInfo responseMemberInfo = new ResponseMemberInfo();
        responseMemberInfo.setQrcode("qrcode");
        responseMemberInfo.setNickName("nickName");
        responseMemberInfo.setUserName("userName");
        responseMemberInfo.setOperationMemberInfoCardLevelGuid("memberInfoGradeGuid");
        responseMemberInfo.setLabelSettingGuid(Arrays.asList("value"));
        final ResponseModel<ResponseMemberInfo> responseMemberInfoResponseModel = new ResponseModel<>(
                responseMemberInfo);
        final RequestQueryMemberInfo requestQueryMemberInfo = new RequestQueryMemberInfo();
        requestQueryMemberInfo.setMemberInfoGuid("memberGuid");
        requestQueryMemberInfo.setUnionId("unionId");
        requestQueryMemberInfo.setOpenId("openId");
        requestQueryMemberInfo.setPhoneNum("phoneNum");
        requestQueryMemberInfo.setOperSubjectGuid("operSubjectGuid");
        when(mockHsaBaseClientService.getMemberInfo(requestQueryMemberInfo))
                .thenReturn(responseMemberInfoResponseModel);

        // Run the test
        final String result = wxStorePayServiceImplUnderTest.wxResultOperation(wxStoreCallbackNotifyDTO);

        // Verify the results
        assertEquals("success", result);

        // Confirm WxStoreDineInOrderClientService.unLockOrder(...).
        final OrderLockDTO orderLockDto = new OrderLockDTO();
        orderLockDto.setDeviceType(0);
        orderLockDto.setDeviceId("openId");
        orderLockDto.setEnterpriseGuid("enterpriseGuid");
        orderLockDto.setStoreGuid("storeGuid");
        orderLockDto.setStoreName("storeName");
        orderLockDto.setUserGuid("openId");
        orderLockDto.setUserName("createName");
        orderLockDto.setOrderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        verify(mockWxStoreDineInOrderClientService).unLockOrder(orderLockDto);
        verify(mockWxStoreSessionDetailsService).delPayCallBack("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        verify(mockWxOrderRecordService).saveOrUpdate(
                new WxOrderRecordDO(0L, "2fd7df1e-4c6a-4209-95f7-d828f00d5ff0", "6e64ec83-01fe-4f5b-8759-73c1b66062ed",
                        "merchantGuid", "guid", "brandGuid", "brandName", "logUrl", "storeGuid", "storeName",
                        "areaGuid", "areaName", "tableGuid", "tableCode", 0, 0, "orderStateName",
                        new BigDecimal("0.00"), new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode",
                        "itemName", "orderHolderNo", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0));
        verify(mockWxOpenMessageService).sendMemberMsgNew(WxMemberConsumeMsgDTO.builder()
                .openId("openId")
                .brandGuid("brandGuid")
                .storeGuid("storeGuid")
                .first("first")
                .remark("remark")
                .url("url")
                .wxTemplateTypeEnum(WxTemplateTypeEnum.QUEUE_TEMPLATE)
                .build());
        verify(mockWebsocketMessageHelper).sendOrderEmqMessage("tableGuid", new HashSet<>(Arrays.asList("value")));
        verify(mockRedisUtils).delete("key");
        verify(mockWxStoreMerchantOrderService).dealUnFinishedOrders("6e64ec83-01fe-4f5b-8759-73c1b66062ed");

        // Confirm MemberMarketingClientService.dealConsumptionGift(...).
        final MemberConsumptionGiftDTO memberConsumptionGiftQO = new MemberConsumptionGiftDTO();
        memberConsumptionGiftQO.setOperSubjectGuid("operSubjectGuid");
        memberConsumptionGiftQO.setEnterpriseGuid("enterpriseGuid");
        memberConsumptionGiftQO.setMemberInfoGuid("memberGuid");
        memberConsumptionGiftQO.setOrderAmount(new BigDecimal("0.00"));
        memberConsumptionGiftQO.setOrderNumber("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        memberConsumptionGiftQO.setRechargeStatus(0);
        memberConsumptionGiftQO.setMemberName("memberName");
        memberConsumptionGiftQO.setMemberPhone("memberPhone");
        memberConsumptionGiftQO.setMemberLabelGuid(Arrays.asList("value"));
        memberConsumptionGiftQO.setMemberInfoGradeGuid("memberInfoGradeGuid");
        memberConsumptionGiftQO.setStoreGuid("storeGuid");
        memberConsumptionGiftQO.setStoreName("storeName");
        memberConsumptionGiftQO.setChangeSource("微信");
        memberConsumptionGiftQO.setOrderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        memberConsumptionGiftQO.setActivitySceneType(0);
        verify(mockMemberMarketingClientService).dealConsumptionGift(memberConsumptionGiftQO);
        verify(mockUserMemberSessionUtils).delAllUserSession(new HashSet<>(Arrays.asList("value")));
        verify(mockUserMemberSessionUtils).delTableCardList("storeGuid", new HashSet<>(Arrays.asList("value")));
        verify(mockWxStoreTradeOrderService).removeFastGuestCount("tableGuid", "openId");
        verify(mockWxStoreTradeOrderService).transformSurchargeCache("tableGuid",
                "6e64ec83-01fe-4f5b-8759-73c1b66062ed");

        // Confirm HsaBaseClientService.batchAddManualLabel(...).
        final RequestManualLabel req = new RequestManualLabel();
        req.setMemberInfoGuidArray("memberGuid");
        req.setLabelSettingGuid("labelSettingGuid");
        verify(mockHsaBaseClientService).batchAddManualLabel(req);
    }

    @Test
    public void testWxResultOperation_RedisUtilsGetReturnsNull() {
        // Setup
        final SaasNotifyDTO saasNotifyDTO = new SaasNotifyDTO();
        final AggPayPollingRespDTO aggPayPollingRespDTO = new AggPayPollingRespDTO();
        aggPayPollingRespDTO.setAmount(new BigDecimal("0.00"));
        aggPayPollingRespDTO.setPayPowerId("payPowerId");
        aggPayPollingRespDTO.setPaySt("paySt");
        aggPayPollingRespDTO.setOrderGUID("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        aggPayPollingRespDTO.setPayGUID("62fd3dbb-b68c-45a1-95a9-87508447c165");
        aggPayPollingRespDTO.setBankOrderNo("orderHolderNo");
        saasNotifyDTO.setAggPayPollingRespDTO(aggPayPollingRespDTO);
        final BaseInfo baseInfo = new BaseInfo();
        baseInfo.setDeviceType(0);
        baseInfo.setDeviceId("openId");
        baseInfo.setEnterpriseGuid("enterpriseGuid");
        baseInfo.setStoreGuid("storeGuid");
        baseInfo.setStoreName("storeName");
        baseInfo.setUserGuid("openId");
        baseInfo.setUserName("createName");
        saasNotifyDTO.setBaseInfo(baseInfo);
        final WxPayCallbackDTO wxStoreCallbackNotifyDTO = new WxPayCallbackDTO("storeGuid", "openId", saasNotifyDTO);

        // Configure UserMemberSessionUtils.getUserMemberSession(...).
        final UserMemberSessionDTO userMemberSessionDTO = new UserMemberSessionDTO("storeGuid", "openId", "guid", false,
                "nickName", "memberInfoGuid", "cardGuid", "memberInfoCardGuid", "systemManagementGuid", 0, false,
                "volumeCode", false, new BigDecimal("0.00"), new BigDecimal("0.00"), "operSubjectGuid");
        when(mockUserMemberSessionUtils.getUserMemberSession("openId")).thenReturn(userMemberSessionDTO);

        // Configure WxStoreSessionDetailsService.getPayCallBack(...).
        final WeChatPayReqDTO weChatPayReqDTO = new WeChatPayReqDTO();
        weChatPayReqDTO.setDeviceType(0);
        weChatPayReqDTO.setDeviceId("openId");
        weChatPayReqDTO.setEnterpriseGuid("enterpriseGuid");
        weChatPayReqDTO.setStoreGuid("storeGuid");
        weChatPayReqDTO.setStoreName("storeName");
        weChatPayReqDTO.setUserGuid("openId");
        weChatPayReqDTO.setUserName("createName");
        weChatPayReqDTO.setOrderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        weChatPayReqDTO.setPayGuid("62fd3dbb-b68c-45a1-95a9-87508447c165");
        weChatPayReqDTO.setPayPowerId("payPowerId");
        weChatPayReqDTO.setSuccess(false);
        weChatPayReqDTO.setAmount(new BigDecimal("0.00"));
        weChatPayReqDTO.setOrderFee(new BigDecimal("0.00"));
        weChatPayReqDTO.setActuallyPayFee(new BigDecimal("0.00"));
        weChatPayReqDTO.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        discountFeeDetailDTO.setRule("rule");
        weChatPayReqDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        weChatPayReqDTO.setMemberGuid("memberGuid");
        when(mockWxStoreSessionDetailsService.getPayCallBack("6e64ec83-01fe-4f5b-8759-73c1b66062ed"))
                .thenReturn(weChatPayReqDTO);

        // Configure WxStoreDineInOrderClientService.pay(...).
        final WeChatPayReqDTO weChatPayReqDTO1 = new WeChatPayReqDTO();
        weChatPayReqDTO1.setDeviceType(0);
        weChatPayReqDTO1.setDeviceId("openId");
        weChatPayReqDTO1.setEnterpriseGuid("enterpriseGuid");
        weChatPayReqDTO1.setStoreGuid("storeGuid");
        weChatPayReqDTO1.setStoreName("storeName");
        weChatPayReqDTO1.setUserGuid("openId");
        weChatPayReqDTO1.setUserName("createName");
        weChatPayReqDTO1.setOrderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        weChatPayReqDTO1.setPayGuid("62fd3dbb-b68c-45a1-95a9-87508447c165");
        weChatPayReqDTO1.setPayPowerId("payPowerId");
        weChatPayReqDTO1.setSuccess(false);
        weChatPayReqDTO1.setAmount(new BigDecimal("0.00"));
        weChatPayReqDTO1.setOrderFee(new BigDecimal("0.00"));
        weChatPayReqDTO1.setActuallyPayFee(new BigDecimal("0.00"));
        weChatPayReqDTO1.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO1.setDiscountType(0);
        discountFeeDetailDTO1.setDiscountFee(new BigDecimal("0.00"));
        discountFeeDetailDTO1.setRule("rule");
        weChatPayReqDTO1.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        weChatPayReqDTO1.setMemberGuid("memberGuid");
        when(mockWxStoreDineInOrderClientService.pay(weChatPayReqDTO1)).thenReturn(true);

        // Configure WxOrderRecordService.getByOrderGuid(...).
        final WxOrderRecordDO wxOrderRecordDO = new WxOrderRecordDO(0L, "2fd7df1e-4c6a-4209-95f7-d828f00d5ff0",
                "6e64ec83-01fe-4f5b-8759-73c1b66062ed", "merchantGuid", "guid", "brandGuid", "brandName", "logUrl",
                "storeGuid", "storeName", "areaGuid", "areaName", "tableGuid", "tableCode", 0, 0, "orderStateName",
                new BigDecimal("0.00"), new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName",
                "orderHolderNo", LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0);
        when(mockWxOrderRecordService.getByOrderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed"))
                .thenReturn(wxOrderRecordDO);

        // Configure WxUserRecordService.getOneByOpenId(...).
        final WxUserRecordDO wxUserRecordDO = new WxUserRecordDO(0L, "guid", "openId", "nickName", "headImgUrl", 0,
                "country", "province", "city", "phone", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid", 0);
        when(mockWxUserRecordService.getOneByOpenId("openId")).thenReturn(wxUserRecordDO);

        // Configure WxStoreDineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        dineinOrderDetailRespDTO.setStoreGuid("storeGuid");
        dineinOrderDetailRespDTO.setStoreName("storeName");
        dineinOrderDetailRespDTO.setOriginalOrderGuid(0L);
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setDeviceType(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setAppendFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setChangeFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO2 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO2.setDiscountType(0);
        discountFeeDetailDTO2.setDiscountFee(new BigDecimal("0.00"));
        discountFeeDetailDTO2.setRule("rule");
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO2));
        dineinOrderDetailRespDTO.setOrderNo("body");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setStateName("已支付");
        dineinOrderDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setMemberGuid("memberGuid");
        dineinOrderDetailRespDTO.setMemberPhone("memberPhone");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        final ResponseIntegralOffset integralOffsetResultRespDTO = new ResponseIntegralOffset();
        integralOffsetResultRespDTO.setUseIntegral(0);
        integralOffsetResultRespDTO.setDeductionMoney(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setIntegralOffsetResultRespDTO(integralOffsetResultRespDTO);
        dineinOrderDetailRespDTO.setTip("errorMsg");
        dineinOrderDetailRespDTO.setVersion(0);
        when(mockWxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder()
                .data("6e64ec83-01fe-4f5b-8759-73c1b66062ed")
                .build())).thenReturn(dineinOrderDetailRespDTO);

        when(mockWeChatConfig.getMEMBER_ORDER_PAGE()).thenReturn("result");
        when(mockRedisUtils.get("key")).thenReturn(null);
        when(mockRedisUtils.hKeyList("key")).thenReturn(new HashSet<>(Arrays.asList("value")));

        // Configure WxStoreEstimateClientService.verifyDineInItemEstimate(...).
        final EstimateResultRespDTO estimateResultRespDTO = EstimateResultRespDTO.builder()
                .success(false)
                .build();
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setGuid("77883994-00fe-4d96-983a-882ed1d82107");
        dineInItemDTO1.setOrderGuid("orderGuid");
        dineInItemDTO1.setOriginalOrderItemGuid(0L);
        dineInItemDTO1.setAdjustNumber(new BigDecimal("0.00"));
        dineInItemDTO1.setRollbackCount(new BigDecimal("0.00"));
        final List<DineInItemDTO> request = Arrays.asList(dineInItemDTO1);
        when(mockWxStoreEstimateClientService.verifyDineInItemEstimate(request)).thenReturn(estimateResultRespDTO);

        // Configure HsaBaseClientService.getMemberInfo(...).
        final ResponseMemberInfo responseMemberInfo = new ResponseMemberInfo();
        responseMemberInfo.setQrcode("qrcode");
        responseMemberInfo.setNickName("nickName");
        responseMemberInfo.setUserName("userName");
        responseMemberInfo.setOperationMemberInfoCardLevelGuid("memberInfoGradeGuid");
        responseMemberInfo.setLabelSettingGuid(Arrays.asList("value"));
        final ResponseModel<ResponseMemberInfo> responseMemberInfoResponseModel = new ResponseModel<>(
                responseMemberInfo);
        final RequestQueryMemberInfo requestQueryMemberInfo = new RequestQueryMemberInfo();
        requestQueryMemberInfo.setMemberInfoGuid("memberGuid");
        requestQueryMemberInfo.setUnionId("unionId");
        requestQueryMemberInfo.setOpenId("openId");
        requestQueryMemberInfo.setPhoneNum("phoneNum");
        requestQueryMemberInfo.setOperSubjectGuid("operSubjectGuid");
        when(mockHsaBaseClientService.getMemberInfo(requestQueryMemberInfo))
                .thenReturn(responseMemberInfoResponseModel);

        // Run the test
        final String result = wxStorePayServiceImplUnderTest.wxResultOperation(wxStoreCallbackNotifyDTO);

        // Verify the results
        assertEquals("success", result);

        // Confirm WxStoreDineInOrderClientService.unLockOrder(...).
        final OrderLockDTO orderLockDto = new OrderLockDTO();
        orderLockDto.setDeviceType(0);
        orderLockDto.setDeviceId("openId");
        orderLockDto.setEnterpriseGuid("enterpriseGuid");
        orderLockDto.setStoreGuid("storeGuid");
        orderLockDto.setStoreName("storeName");
        orderLockDto.setUserGuid("openId");
        orderLockDto.setUserName("createName");
        orderLockDto.setOrderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        verify(mockWxStoreDineInOrderClientService).unLockOrder(orderLockDto);
        verify(mockWxStoreSessionDetailsService).delPayCallBack("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        verify(mockWxOrderRecordService).saveOrUpdate(
                new WxOrderRecordDO(0L, "2fd7df1e-4c6a-4209-95f7-d828f00d5ff0", "6e64ec83-01fe-4f5b-8759-73c1b66062ed",
                        "merchantGuid", "guid", "brandGuid", "brandName", "logUrl", "storeGuid", "storeName",
                        "areaGuid", "areaName", "tableGuid", "tableCode", 0, 0, "orderStateName",
                        new BigDecimal("0.00"), new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode",
                        "itemName", "orderHolderNo", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0));
        verify(mockWxOpenMessageService).sendMemberMsgNew(WxMemberConsumeMsgDTO.builder()
                .openId("openId")
                .brandGuid("brandGuid")
                .storeGuid("storeGuid")
                .first("first")
                .remark("remark")
                .url("url")
                .wxTemplateTypeEnum(WxTemplateTypeEnum.QUEUE_TEMPLATE)
                .build());
        verify(mockRedisUtils).delete("key");

        // Confirm MemberMarketingClientService.dealConsumptionGift(...).
        final MemberConsumptionGiftDTO memberConsumptionGiftQO = new MemberConsumptionGiftDTO();
        memberConsumptionGiftQO.setOperSubjectGuid("operSubjectGuid");
        memberConsumptionGiftQO.setEnterpriseGuid("enterpriseGuid");
        memberConsumptionGiftQO.setMemberInfoGuid("memberGuid");
        memberConsumptionGiftQO.setOrderAmount(new BigDecimal("0.00"));
        memberConsumptionGiftQO.setOrderNumber("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        memberConsumptionGiftQO.setRechargeStatus(0);
        memberConsumptionGiftQO.setMemberName("memberName");
        memberConsumptionGiftQO.setMemberPhone("memberPhone");
        memberConsumptionGiftQO.setMemberLabelGuid(Arrays.asList("value"));
        memberConsumptionGiftQO.setMemberInfoGradeGuid("memberInfoGradeGuid");
        memberConsumptionGiftQO.setStoreGuid("storeGuid");
        memberConsumptionGiftQO.setStoreName("storeName");
        memberConsumptionGiftQO.setChangeSource("微信");
        memberConsumptionGiftQO.setOrderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        memberConsumptionGiftQO.setActivitySceneType(0);
        verify(mockMemberMarketingClientService).dealConsumptionGift(memberConsumptionGiftQO);
        verify(mockUserMemberSessionUtils).delAllUserSession(new HashSet<>(Arrays.asList("value")));
        verify(mockUserMemberSessionUtils).delTableCardList("storeGuid", new HashSet<>(Arrays.asList("value")));
        verify(mockWxStoreTradeOrderService).removeFastGuestCount("tableGuid", "openId");
        verify(mockWxStoreTradeOrderService).transformSurchargeCache("tableGuid",
                "6e64ec83-01fe-4f5b-8759-73c1b66062ed");

        // Confirm HsaBaseClientService.batchAddManualLabel(...).
        final RequestManualLabel req = new RequestManualLabel();
        req.setMemberInfoGuidArray("memberGuid");
        req.setLabelSettingGuid("labelSettingGuid");
        verify(mockHsaBaseClientService).batchAddManualLabel(req);
    }

    @Test
    public void testAsync() {
        // Setup
        final SaasNotifyDTO saasNotifyDTO = new SaasNotifyDTO();
        final AggPayPollingRespDTO aggPayPollingRespDTO = new AggPayPollingRespDTO();
        aggPayPollingRespDTO.setAmount(new BigDecimal("0.00"));
        aggPayPollingRespDTO.setPayPowerId("payPowerId");
        aggPayPollingRespDTO.setPaySt("paySt");
        aggPayPollingRespDTO.setOrderGUID("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        aggPayPollingRespDTO.setPayGUID("62fd3dbb-b68c-45a1-95a9-87508447c165");
        aggPayPollingRespDTO.setBankOrderNo("orderHolderNo");
        saasNotifyDTO.setAggPayPollingRespDTO(aggPayPollingRespDTO);
        final BaseInfo baseInfo = new BaseInfo();
        baseInfo.setDeviceType(0);
        baseInfo.setDeviceId("openId");
        baseInfo.setEnterpriseGuid("enterpriseGuid");
        baseInfo.setStoreGuid("storeGuid");
        baseInfo.setStoreName("storeName");
        baseInfo.setUserGuid("openId");
        baseInfo.setUserName("createName");
        saasNotifyDTO.setBaseInfo(baseInfo);
        final WxPayCallbackDTO wxStoreCallbackNotifyDTO = new WxPayCallbackDTO("storeGuid", "openId", saasNotifyDTO);
        final WxOrderRecordDO wxOrderRecordDO = new WxOrderRecordDO(0L, "2fd7df1e-4c6a-4209-95f7-d828f00d5ff0",
                "6e64ec83-01fe-4f5b-8759-73c1b66062ed", "merchantGuid", "guid", "brandGuid", "brandName", "logUrl",
                "storeGuid", "storeName", "areaGuid", "areaName", "tableGuid", "tableCode", 0, 0, "orderStateName",
                new BigDecimal("0.00"), new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName",
                "orderHolderNo", LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0);
        final UserMemberSessionDTO userMemberSession = new UserMemberSessionDTO("storeGuid", "openId", "guid", false,
                "nickName", "memberInfoGuid", "cardGuid", "memberInfoCardGuid", "systemManagementGuid", 0, false,
                "volumeCode", false, new BigDecimal("0.00"), new BigDecimal("0.00"), "operSubjectGuid");

        // Configure WxStoreDineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        dineinOrderDetailRespDTO.setStoreGuid("storeGuid");
        dineinOrderDetailRespDTO.setStoreName("storeName");
        dineinOrderDetailRespDTO.setOriginalOrderGuid(0L);
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setDeviceType(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setAppendFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setChangeFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        discountFeeDetailDTO.setRule("rule");
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setOrderNo("body");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setStateName("已支付");
        dineinOrderDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setMemberGuid("memberGuid");
        dineinOrderDetailRespDTO.setMemberPhone("memberPhone");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        final ResponseIntegralOffset integralOffsetResultRespDTO = new ResponseIntegralOffset();
        integralOffsetResultRespDTO.setUseIntegral(0);
        integralOffsetResultRespDTO.setDeductionMoney(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setIntegralOffsetResultRespDTO(integralOffsetResultRespDTO);
        dineinOrderDetailRespDTO.setTip("errorMsg");
        dineinOrderDetailRespDTO.setVersion(0);
        when(mockWxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder()
                .data("6e64ec83-01fe-4f5b-8759-73c1b66062ed")
                .build())).thenReturn(dineinOrderDetailRespDTO);

        when(mockWeChatConfig.getMEMBER_ORDER_PAGE()).thenReturn("result");
        when(mockRedisUtils.hKeyList("key")).thenReturn(new HashSet<>(Arrays.asList("value")));
        when(mockRedisUtils.get("key")).thenReturn("result");

        // Configure WxStoreEstimateClientService.verifyDineInItemEstimate(...).
        final EstimateResultRespDTO estimateResultRespDTO = EstimateResultRespDTO.builder()
                .success(false)
                .build();
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setGuid("77883994-00fe-4d96-983a-882ed1d82107");
        dineInItemDTO1.setOrderGuid("orderGuid");
        dineInItemDTO1.setOriginalOrderItemGuid(0L);
        dineInItemDTO1.setAdjustNumber(new BigDecimal("0.00"));
        dineInItemDTO1.setRollbackCount(new BigDecimal("0.00"));
        final List<DineInItemDTO> request = Arrays.asList(dineInItemDTO1);
        when(mockWxStoreEstimateClientService.verifyDineInItemEstimate(request)).thenReturn(estimateResultRespDTO);

        // Configure HsaBaseClientService.getMemberInfo(...).
        final ResponseMemberInfo responseMemberInfo = new ResponseMemberInfo();
        responseMemberInfo.setQrcode("qrcode");
        responseMemberInfo.setNickName("nickName");
        responseMemberInfo.setUserName("userName");
        responseMemberInfo.setOperationMemberInfoCardLevelGuid("memberInfoGradeGuid");
        responseMemberInfo.setLabelSettingGuid(Arrays.asList("value"));
        final ResponseModel<ResponseMemberInfo> responseMemberInfoResponseModel = new ResponseModel<>(
                responseMemberInfo);
        final RequestQueryMemberInfo requestQueryMemberInfo = new RequestQueryMemberInfo();
        requestQueryMemberInfo.setMemberInfoGuid("memberGuid");
        requestQueryMemberInfo.setUnionId("unionId");
        requestQueryMemberInfo.setOpenId("openId");
        requestQueryMemberInfo.setPhoneNum("phoneNum");
        requestQueryMemberInfo.setOperSubjectGuid("operSubjectGuid");
        when(mockHsaBaseClientService.getMemberInfo(requestQueryMemberInfo))
                .thenReturn(responseMemberInfoResponseModel);

        // Run the test
        wxStorePayServiceImplUnderTest.async(wxStoreCallbackNotifyDTO, wxOrderRecordDO, "openId", userMemberSession);

        // Verify the results
        verify(mockWxOpenMessageService).sendMemberMsgNew(WxMemberConsumeMsgDTO.builder()
                .openId("openId")
                .brandGuid("brandGuid")
                .storeGuid("storeGuid")
                .first("first")
                .remark("remark")
                .url("url")
                .wxTemplateTypeEnum(WxTemplateTypeEnum.QUEUE_TEMPLATE)
                .build());
        verify(mockWebsocketMessageHelper).sendOrderEmqMessage("tableGuid", new HashSet<>(Arrays.asList("value")));
        verify(mockRedisUtils).delete("key");
        verify(mockWxStoreMerchantOrderService).dealUnFinishedOrders("6e64ec83-01fe-4f5b-8759-73c1b66062ed");

        // Confirm WxStoreMerchantOrderService.pushFastMsg(...).
        final WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO.setGuid("0b550432-c989-4b0f-b462-d30b2855c69f");
        wxStoreMerchantOrderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setTotalPrice(new BigDecimal("0.00"));
        wxStoreMerchantOrderDTO.setOpenId("openId");
        verify(mockWxStoreMerchantOrderService).pushFastMsg(wxStoreMerchantOrderDTO);

        // Confirm MemberMarketingClientService.dealConsumptionGift(...).
        final MemberConsumptionGiftDTO memberConsumptionGiftQO = new MemberConsumptionGiftDTO();
        memberConsumptionGiftQO.setOperSubjectGuid("operSubjectGuid");
        memberConsumptionGiftQO.setEnterpriseGuid("enterpriseGuid");
        memberConsumptionGiftQO.setMemberInfoGuid("memberGuid");
        memberConsumptionGiftQO.setOrderAmount(new BigDecimal("0.00"));
        memberConsumptionGiftQO.setOrderNumber("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        memberConsumptionGiftQO.setRechargeStatus(0);
        memberConsumptionGiftQO.setMemberName("memberName");
        memberConsumptionGiftQO.setMemberPhone("memberPhone");
        memberConsumptionGiftQO.setMemberLabelGuid(Arrays.asList("value"));
        memberConsumptionGiftQO.setMemberInfoGradeGuid("memberInfoGradeGuid");
        memberConsumptionGiftQO.setStoreGuid("storeGuid");
        memberConsumptionGiftQO.setStoreName("storeName");
        memberConsumptionGiftQO.setChangeSource("微信");
        memberConsumptionGiftQO.setOrderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        memberConsumptionGiftQO.setActivitySceneType(0);
        verify(mockMemberMarketingClientService).dealConsumptionGift(memberConsumptionGiftQO);
    }

    @Test
    public void testAsync_WxStoreDineInOrderClientServiceReturnsNull() {
        // Setup
        final SaasNotifyDTO saasNotifyDTO = new SaasNotifyDTO();
        final AggPayPollingRespDTO aggPayPollingRespDTO = new AggPayPollingRespDTO();
        aggPayPollingRespDTO.setAmount(new BigDecimal("0.00"));
        aggPayPollingRespDTO.setPayPowerId("payPowerId");
        aggPayPollingRespDTO.setPaySt("paySt");
        aggPayPollingRespDTO.setOrderGUID("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        aggPayPollingRespDTO.setPayGUID("62fd3dbb-b68c-45a1-95a9-87508447c165");
        aggPayPollingRespDTO.setBankOrderNo("orderHolderNo");
        saasNotifyDTO.setAggPayPollingRespDTO(aggPayPollingRespDTO);
        final BaseInfo baseInfo = new BaseInfo();
        baseInfo.setDeviceType(0);
        baseInfo.setDeviceId("openId");
        baseInfo.setEnterpriseGuid("enterpriseGuid");
        baseInfo.setStoreGuid("storeGuid");
        baseInfo.setStoreName("storeName");
        baseInfo.setUserGuid("openId");
        baseInfo.setUserName("createName");
        saasNotifyDTO.setBaseInfo(baseInfo);
        final WxPayCallbackDTO wxStoreCallbackNotifyDTO = new WxPayCallbackDTO("storeGuid", "openId", saasNotifyDTO);
        final WxOrderRecordDO wxOrderRecordDO = new WxOrderRecordDO(0L, "2fd7df1e-4c6a-4209-95f7-d828f00d5ff0",
                "6e64ec83-01fe-4f5b-8759-73c1b66062ed", "merchantGuid", "guid", "brandGuid", "brandName", "logUrl",
                "storeGuid", "storeName", "areaGuid", "areaName", "tableGuid", "tableCode", 0, 0, "orderStateName",
                new BigDecimal("0.00"), new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName",
                "orderHolderNo", LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0);
        final UserMemberSessionDTO userMemberSession = new UserMemberSessionDTO("storeGuid", "openId", "guid", false,
                "nickName", "memberInfoGuid", "cardGuid", "memberInfoCardGuid", "systemManagementGuid", 0, false,
                "volumeCode", false, new BigDecimal("0.00"), new BigDecimal("0.00"), "operSubjectGuid");
        when(mockWxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder()
                .data("6e64ec83-01fe-4f5b-8759-73c1b66062ed")
                .build())).thenReturn(null);
        when(mockWeChatConfig.getMEMBER_ORDER_PAGE()).thenReturn("result");
        when(mockRedisUtils.hKeyList("key")).thenReturn(new HashSet<>(Arrays.asList("value")));
        when(mockRedisUtils.get("key")).thenReturn("result");

        // Configure WxStoreEstimateClientService.verifyDineInItemEstimate(...).
        final EstimateResultRespDTO estimateResultRespDTO = EstimateResultRespDTO.builder()
                .success(false)
                .build();
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setGuid("77883994-00fe-4d96-983a-882ed1d82107");
        dineInItemDTO.setOrderGuid("orderGuid");
        dineInItemDTO.setOriginalOrderItemGuid(0L);
        dineInItemDTO.setAdjustNumber(new BigDecimal("0.00"));
        dineInItemDTO.setRollbackCount(new BigDecimal("0.00"));
        final List<DineInItemDTO> request = Arrays.asList(dineInItemDTO);
        when(mockWxStoreEstimateClientService.verifyDineInItemEstimate(request)).thenReturn(estimateResultRespDTO);

        // Configure HsaBaseClientService.getMemberInfo(...).
        final ResponseMemberInfo responseMemberInfo = new ResponseMemberInfo();
        responseMemberInfo.setQrcode("qrcode");
        responseMemberInfo.setNickName("nickName");
        responseMemberInfo.setUserName("userName");
        responseMemberInfo.setOperationMemberInfoCardLevelGuid("memberInfoGradeGuid");
        responseMemberInfo.setLabelSettingGuid(Arrays.asList("value"));
        final ResponseModel<ResponseMemberInfo> responseMemberInfoResponseModel = new ResponseModel<>(
                responseMemberInfo);
        final RequestQueryMemberInfo requestQueryMemberInfo = new RequestQueryMemberInfo();
        requestQueryMemberInfo.setMemberInfoGuid("memberGuid");
        requestQueryMemberInfo.setUnionId("unionId");
        requestQueryMemberInfo.setOpenId("openId");
        requestQueryMemberInfo.setPhoneNum("phoneNum");
        requestQueryMemberInfo.setOperSubjectGuid("operSubjectGuid");
        when(mockHsaBaseClientService.getMemberInfo(requestQueryMemberInfo))
                .thenReturn(responseMemberInfoResponseModel);

        // Run the test
        wxStorePayServiceImplUnderTest.async(wxStoreCallbackNotifyDTO, wxOrderRecordDO, "openId", userMemberSession);

        // Verify the results
        verify(mockWxOpenMessageService).sendMemberMsgNew(WxMemberConsumeMsgDTO.builder()
                .openId("openId")
                .brandGuid("brandGuid")
                .storeGuid("storeGuid")
                .first("first")
                .remark("remark")
                .url("url")
                .wxTemplateTypeEnum(WxTemplateTypeEnum.QUEUE_TEMPLATE)
                .build());
        verify(mockWebsocketMessageHelper).sendOrderEmqMessage("tableGuid", new HashSet<>(Arrays.asList("value")));
        verify(mockRedisUtils).delete("key");
        verify(mockWxStoreMerchantOrderService).dealUnFinishedOrders("6e64ec83-01fe-4f5b-8759-73c1b66062ed");

        // Confirm WxStoreMerchantOrderService.pushFastMsg(...).
        final WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO.setGuid("0b550432-c989-4b0f-b462-d30b2855c69f");
        wxStoreMerchantOrderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setTotalPrice(new BigDecimal("0.00"));
        wxStoreMerchantOrderDTO.setOpenId("openId");
        verify(mockWxStoreMerchantOrderService).pushFastMsg(wxStoreMerchantOrderDTO);

        // Confirm MemberMarketingClientService.dealConsumptionGift(...).
        final MemberConsumptionGiftDTO memberConsumptionGiftQO = new MemberConsumptionGiftDTO();
        memberConsumptionGiftQO.setOperSubjectGuid("operSubjectGuid");
        memberConsumptionGiftQO.setEnterpriseGuid("enterpriseGuid");
        memberConsumptionGiftQO.setMemberInfoGuid("memberGuid");
        memberConsumptionGiftQO.setOrderAmount(new BigDecimal("0.00"));
        memberConsumptionGiftQO.setOrderNumber("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        memberConsumptionGiftQO.setRechargeStatus(0);
        memberConsumptionGiftQO.setMemberName("memberName");
        memberConsumptionGiftQO.setMemberPhone("memberPhone");
        memberConsumptionGiftQO.setMemberLabelGuid(Arrays.asList("value"));
        memberConsumptionGiftQO.setMemberInfoGradeGuid("memberInfoGradeGuid");
        memberConsumptionGiftQO.setStoreGuid("storeGuid");
        memberConsumptionGiftQO.setStoreName("storeName");
        memberConsumptionGiftQO.setChangeSource("微信");
        memberConsumptionGiftQO.setOrderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        memberConsumptionGiftQO.setActivitySceneType(0);
        verify(mockMemberMarketingClientService).dealConsumptionGift(memberConsumptionGiftQO);
    }

    @Test
    public void testAsync_RedisUtilsHKeyListReturnsNoItems() {
        // Setup
        final SaasNotifyDTO saasNotifyDTO = new SaasNotifyDTO();
        final AggPayPollingRespDTO aggPayPollingRespDTO = new AggPayPollingRespDTO();
        aggPayPollingRespDTO.setAmount(new BigDecimal("0.00"));
        aggPayPollingRespDTO.setPayPowerId("payPowerId");
        aggPayPollingRespDTO.setPaySt("paySt");
        aggPayPollingRespDTO.setOrderGUID("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        aggPayPollingRespDTO.setPayGUID("62fd3dbb-b68c-45a1-95a9-87508447c165");
        aggPayPollingRespDTO.setBankOrderNo("orderHolderNo");
        saasNotifyDTO.setAggPayPollingRespDTO(aggPayPollingRespDTO);
        final BaseInfo baseInfo = new BaseInfo();
        baseInfo.setDeviceType(0);
        baseInfo.setDeviceId("openId");
        baseInfo.setEnterpriseGuid("enterpriseGuid");
        baseInfo.setStoreGuid("storeGuid");
        baseInfo.setStoreName("storeName");
        baseInfo.setUserGuid("openId");
        baseInfo.setUserName("createName");
        saasNotifyDTO.setBaseInfo(baseInfo);
        final WxPayCallbackDTO wxStoreCallbackNotifyDTO = new WxPayCallbackDTO("storeGuid", "openId", saasNotifyDTO);
        final WxOrderRecordDO wxOrderRecordDO = new WxOrderRecordDO(0L, "2fd7df1e-4c6a-4209-95f7-d828f00d5ff0",
                "6e64ec83-01fe-4f5b-8759-73c1b66062ed", "merchantGuid", "guid", "brandGuid", "brandName", "logUrl",
                "storeGuid", "storeName", "areaGuid", "areaName", "tableGuid", "tableCode", 0, 0, "orderStateName",
                new BigDecimal("0.00"), new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName",
                "orderHolderNo", LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0);
        final UserMemberSessionDTO userMemberSession = new UserMemberSessionDTO("storeGuid", "openId", "guid", false,
                "nickName", "memberInfoGuid", "cardGuid", "memberInfoCardGuid", "systemManagementGuid", 0, false,
                "volumeCode", false, new BigDecimal("0.00"), new BigDecimal("0.00"), "operSubjectGuid");

        // Configure WxStoreDineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        dineinOrderDetailRespDTO.setStoreGuid("storeGuid");
        dineinOrderDetailRespDTO.setStoreName("storeName");
        dineinOrderDetailRespDTO.setOriginalOrderGuid(0L);
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setDeviceType(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setAppendFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setChangeFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        discountFeeDetailDTO.setRule("rule");
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setOrderNo("body");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setStateName("已支付");
        dineinOrderDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setMemberGuid("memberGuid");
        dineinOrderDetailRespDTO.setMemberPhone("memberPhone");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        final ResponseIntegralOffset integralOffsetResultRespDTO = new ResponseIntegralOffset();
        integralOffsetResultRespDTO.setUseIntegral(0);
        integralOffsetResultRespDTO.setDeductionMoney(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setIntegralOffsetResultRespDTO(integralOffsetResultRespDTO);
        dineinOrderDetailRespDTO.setTip("errorMsg");
        dineinOrderDetailRespDTO.setVersion(0);
        when(mockWxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder()
                .data("6e64ec83-01fe-4f5b-8759-73c1b66062ed")
                .build())).thenReturn(dineinOrderDetailRespDTO);

        when(mockWeChatConfig.getMEMBER_ORDER_PAGE()).thenReturn("result");
        when(mockRedisUtils.hKeyList("key")).thenReturn(Collections.emptySet());

        // Configure HsaBaseClientService.getMemberInfo(...).
        final ResponseMemberInfo responseMemberInfo = new ResponseMemberInfo();
        responseMemberInfo.setQrcode("qrcode");
        responseMemberInfo.setNickName("nickName");
        responseMemberInfo.setUserName("userName");
        responseMemberInfo.setOperationMemberInfoCardLevelGuid("memberInfoGradeGuid");
        responseMemberInfo.setLabelSettingGuid(Arrays.asList("value"));
        final ResponseModel<ResponseMemberInfo> responseMemberInfoResponseModel = new ResponseModel<>(
                responseMemberInfo);
        final RequestQueryMemberInfo requestQueryMemberInfo = new RequestQueryMemberInfo();
        requestQueryMemberInfo.setMemberInfoGuid("memberGuid");
        requestQueryMemberInfo.setUnionId("unionId");
        requestQueryMemberInfo.setOpenId("openId");
        requestQueryMemberInfo.setPhoneNum("phoneNum");
        requestQueryMemberInfo.setOperSubjectGuid("operSubjectGuid");
        when(mockHsaBaseClientService.getMemberInfo(requestQueryMemberInfo))
                .thenReturn(responseMemberInfoResponseModel);

        // Run the test
        wxStorePayServiceImplUnderTest.async(wxStoreCallbackNotifyDTO, wxOrderRecordDO, "openId", userMemberSession);

        // Verify the results
        verify(mockWxOpenMessageService).sendMemberMsgNew(WxMemberConsumeMsgDTO.builder()
                .openId("openId")
                .brandGuid("brandGuid")
                .storeGuid("storeGuid")
                .first("first")
                .remark("remark")
                .url("url")
                .wxTemplateTypeEnum(WxTemplateTypeEnum.QUEUE_TEMPLATE)
                .build());
        verify(mockWebsocketMessageHelper).sendOrderEmqMessage("tableGuid", new HashSet<>(Arrays.asList("value")));
        verify(mockRedisUtils).delete("key");
        verify(mockWxStoreMerchantOrderService).dealUnFinishedOrders("6e64ec83-01fe-4f5b-8759-73c1b66062ed");

        // Confirm MemberMarketingClientService.dealConsumptionGift(...).
        final MemberConsumptionGiftDTO memberConsumptionGiftQO = new MemberConsumptionGiftDTO();
        memberConsumptionGiftQO.setOperSubjectGuid("operSubjectGuid");
        memberConsumptionGiftQO.setEnterpriseGuid("enterpriseGuid");
        memberConsumptionGiftQO.setMemberInfoGuid("memberGuid");
        memberConsumptionGiftQO.setOrderAmount(new BigDecimal("0.00"));
        memberConsumptionGiftQO.setOrderNumber("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        memberConsumptionGiftQO.setRechargeStatus(0);
        memberConsumptionGiftQO.setMemberName("memberName");
        memberConsumptionGiftQO.setMemberPhone("memberPhone");
        memberConsumptionGiftQO.setMemberLabelGuid(Arrays.asList("value"));
        memberConsumptionGiftQO.setMemberInfoGradeGuid("memberInfoGradeGuid");
        memberConsumptionGiftQO.setStoreGuid("storeGuid");
        memberConsumptionGiftQO.setStoreName("storeName");
        memberConsumptionGiftQO.setChangeSource("微信");
        memberConsumptionGiftQO.setOrderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        memberConsumptionGiftQO.setActivitySceneType(0);
        verify(mockMemberMarketingClientService).dealConsumptionGift(memberConsumptionGiftQO);
    }

    @Test
    public void testAsync_RedisUtilsGetReturnsNull() {
        // Setup
        final SaasNotifyDTO saasNotifyDTO = new SaasNotifyDTO();
        final AggPayPollingRespDTO aggPayPollingRespDTO = new AggPayPollingRespDTO();
        aggPayPollingRespDTO.setAmount(new BigDecimal("0.00"));
        aggPayPollingRespDTO.setPayPowerId("payPowerId");
        aggPayPollingRespDTO.setPaySt("paySt");
        aggPayPollingRespDTO.setOrderGUID("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        aggPayPollingRespDTO.setPayGUID("62fd3dbb-b68c-45a1-95a9-87508447c165");
        aggPayPollingRespDTO.setBankOrderNo("orderHolderNo");
        saasNotifyDTO.setAggPayPollingRespDTO(aggPayPollingRespDTO);
        final BaseInfo baseInfo = new BaseInfo();
        baseInfo.setDeviceType(0);
        baseInfo.setDeviceId("openId");
        baseInfo.setEnterpriseGuid("enterpriseGuid");
        baseInfo.setStoreGuid("storeGuid");
        baseInfo.setStoreName("storeName");
        baseInfo.setUserGuid("openId");
        baseInfo.setUserName("createName");
        saasNotifyDTO.setBaseInfo(baseInfo);
        final WxPayCallbackDTO wxStoreCallbackNotifyDTO = new WxPayCallbackDTO("storeGuid", "openId", saasNotifyDTO);
        final WxOrderRecordDO wxOrderRecordDO = new WxOrderRecordDO(0L, "2fd7df1e-4c6a-4209-95f7-d828f00d5ff0",
                "6e64ec83-01fe-4f5b-8759-73c1b66062ed", "merchantGuid", "guid", "brandGuid", "brandName", "logUrl",
                "storeGuid", "storeName", "areaGuid", "areaName", "tableGuid", "tableCode", 0, 0, "orderStateName",
                new BigDecimal("0.00"), new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName",
                "orderHolderNo", LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0);
        final UserMemberSessionDTO userMemberSession = new UserMemberSessionDTO("storeGuid", "openId", "guid", false,
                "nickName", "memberInfoGuid", "cardGuid", "memberInfoCardGuid", "systemManagementGuid", 0, false,
                "volumeCode", false, new BigDecimal("0.00"), new BigDecimal("0.00"), "operSubjectGuid");

        // Configure WxStoreDineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        dineinOrderDetailRespDTO.setStoreGuid("storeGuid");
        dineinOrderDetailRespDTO.setStoreName("storeName");
        dineinOrderDetailRespDTO.setOriginalOrderGuid(0L);
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setDeviceType(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setAppendFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setChangeFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        discountFeeDetailDTO.setRule("rule");
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setOrderNo("body");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setStateName("已支付");
        dineinOrderDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setMemberGuid("memberGuid");
        dineinOrderDetailRespDTO.setMemberPhone("memberPhone");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        final ResponseIntegralOffset integralOffsetResultRespDTO = new ResponseIntegralOffset();
        integralOffsetResultRespDTO.setUseIntegral(0);
        integralOffsetResultRespDTO.setDeductionMoney(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setIntegralOffsetResultRespDTO(integralOffsetResultRespDTO);
        dineinOrderDetailRespDTO.setTip("errorMsg");
        dineinOrderDetailRespDTO.setVersion(0);
        when(mockWxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder()
                .data("6e64ec83-01fe-4f5b-8759-73c1b66062ed")
                .build())).thenReturn(dineinOrderDetailRespDTO);

        when(mockWeChatConfig.getMEMBER_ORDER_PAGE()).thenReturn("result");
        when(mockRedisUtils.get("key")).thenReturn(null);

        // Configure WxStoreEstimateClientService.verifyDineInItemEstimate(...).
        final EstimateResultRespDTO estimateResultRespDTO = EstimateResultRespDTO.builder()
                .success(false)
                .build();
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setGuid("77883994-00fe-4d96-983a-882ed1d82107");
        dineInItemDTO1.setOrderGuid("orderGuid");
        dineInItemDTO1.setOriginalOrderItemGuid(0L);
        dineInItemDTO1.setAdjustNumber(new BigDecimal("0.00"));
        dineInItemDTO1.setRollbackCount(new BigDecimal("0.00"));
        final List<DineInItemDTO> request = Arrays.asList(dineInItemDTO1);
        when(mockWxStoreEstimateClientService.verifyDineInItemEstimate(request)).thenReturn(estimateResultRespDTO);

        // Configure HsaBaseClientService.getMemberInfo(...).
        final ResponseMemberInfo responseMemberInfo = new ResponseMemberInfo();
        responseMemberInfo.setQrcode("qrcode");
        responseMemberInfo.setNickName("nickName");
        responseMemberInfo.setUserName("userName");
        responseMemberInfo.setOperationMemberInfoCardLevelGuid("memberInfoGradeGuid");
        responseMemberInfo.setLabelSettingGuid(Arrays.asList("value"));
        final ResponseModel<ResponseMemberInfo> responseMemberInfoResponseModel = new ResponseModel<>(
                responseMemberInfo);
        final RequestQueryMemberInfo requestQueryMemberInfo = new RequestQueryMemberInfo();
        requestQueryMemberInfo.setMemberInfoGuid("memberGuid");
        requestQueryMemberInfo.setUnionId("unionId");
        requestQueryMemberInfo.setOpenId("openId");
        requestQueryMemberInfo.setPhoneNum("phoneNum");
        requestQueryMemberInfo.setOperSubjectGuid("operSubjectGuid");
        when(mockHsaBaseClientService.getMemberInfo(requestQueryMemberInfo))
                .thenReturn(responseMemberInfoResponseModel);

        // Run the test
        wxStorePayServiceImplUnderTest.async(wxStoreCallbackNotifyDTO, wxOrderRecordDO, "openId", userMemberSession);

        // Verify the results
        verify(mockWxOpenMessageService).sendMemberMsgNew(WxMemberConsumeMsgDTO.builder()
                .openId("openId")
                .brandGuid("brandGuid")
                .storeGuid("storeGuid")
                .first("first")
                .remark("remark")
                .url("url")
                .wxTemplateTypeEnum(WxTemplateTypeEnum.QUEUE_TEMPLATE)
                .build());

        // Confirm MemberMarketingClientService.dealConsumptionGift(...).
        final MemberConsumptionGiftDTO memberConsumptionGiftQO = new MemberConsumptionGiftDTO();
        memberConsumptionGiftQO.setOperSubjectGuid("operSubjectGuid");
        memberConsumptionGiftQO.setEnterpriseGuid("enterpriseGuid");
        memberConsumptionGiftQO.setMemberInfoGuid("memberGuid");
        memberConsumptionGiftQO.setOrderAmount(new BigDecimal("0.00"));
        memberConsumptionGiftQO.setOrderNumber("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        memberConsumptionGiftQO.setRechargeStatus(0);
        memberConsumptionGiftQO.setMemberName("memberName");
        memberConsumptionGiftQO.setMemberPhone("memberPhone");
        memberConsumptionGiftQO.setMemberLabelGuid(Arrays.asList("value"));
        memberConsumptionGiftQO.setMemberInfoGradeGuid("memberInfoGradeGuid");
        memberConsumptionGiftQO.setStoreGuid("storeGuid");
        memberConsumptionGiftQO.setStoreName("storeName");
        memberConsumptionGiftQO.setChangeSource("微信");
        memberConsumptionGiftQO.setOrderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        memberConsumptionGiftQO.setActivitySceneType(0);
        verify(mockMemberMarketingClientService).dealConsumptionGift(memberConsumptionGiftQO);
    }

    @Test
    public void testMemberPay() throws Exception {
        // Setup
        final WxMemberPayDTO wxMemberPayDTO = WxMemberPayDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .storeGuid("storeGuid")
                .tableGuid("diningTableGuid")
                .openId("openId")
                .nickName("createName")
                .headImgUrl("headImgUrl")
                .memberPassWord("memberPassWord")
                .storeName("storeName")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("createName")
                        .headImgUrl("headImgUrl")
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("diningTableGuid")
                        .brandGuid("brandGuid")
                        .build())
                .build();
        final WxStorePayResultDTO expectedResult = WxStorePayResultDTO.builder()
                .result(0)
                .errorMsg("errorMsg")
                .build();

        // Configure WxStoreSessionDetailsService.getPrepay(...).
        final WxPrepayReqDTO wxPrepayReqDTO = WxPrepayReqDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .brandGuid("brandGuid")
                .storeGuid("storeGuid")
                .tableGuid("diningTableGuid")
                .openId("openId")
                .payAmount(new BigDecimal("0.00"))
                .memberCardGuid("cardGuid")
                .cardGuid("cardGuid")
                .memberInfoCardGuid("memberInfoCardGuid")
                .memberIntegral(0)
                .volumeCode("volumeCode")
                .orderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed")
                .mode(0)
                .version(0)
                .build();
        when(mockWxStoreSessionDetailsService.getPrepay("storeGuid", "openId")).thenReturn(wxPrepayReqDTO);

        when(mockWeChatConfig.getMemberPaykey()).thenReturn("result");

        // Configure WxStoreSessionDetailsService.calculateDetails2(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        dineinOrderDetailRespDTO.setStoreGuid("storeGuid");
        dineinOrderDetailRespDTO.setStoreName("storeName");
        dineinOrderDetailRespDTO.setOriginalOrderGuid(0L);
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setDeviceType(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setAppendFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setChangeFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        discountFeeDetailDTO.setRule("rule");
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setOrderNo("body");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setStateName("已支付");
        dineinOrderDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setMemberGuid("memberGuid");
        dineinOrderDetailRespDTO.setMemberPhone("memberPhone");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        final ResponseIntegralOffset integralOffsetResultRespDTO = new ResponseIntegralOffset();
        integralOffsetResultRespDTO.setUseIntegral(0);
        integralOffsetResultRespDTO.setDeductionMoney(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setIntegralOffsetResultRespDTO(integralOffsetResultRespDTO);
        dineinOrderDetailRespDTO.setTip("errorMsg");
        dineinOrderDetailRespDTO.setVersion(0);
        final CorrectResult<DineinOrderDetailRespDTO> dineinOrderDetailRespDTOCorrectResult = new CorrectResult<>(
                "errorMsg", 0, dineinOrderDetailRespDTO);
        when(mockWxStoreSessionDetailsService.calculateDetails2("6e64ec83-01fe-4f5b-8759-73c1b66062ed", "openId",
                "memberInfoCardGuid", 0, "volumeCode")).thenReturn(dineinOrderDetailRespDTOCorrectResult);

        // Configure WxStoreDineInBillClientService.pay(...).
        final EstimateItemRespDTO estimateItemRespDTO = new EstimateItemRespDTO();
        estimateItemRespDTO.setResult(false);
        estimateItemRespDTO.setEstimate(false);
        estimateItemRespDTO.setErrorMsg("errorMsg");
        estimateItemRespDTO.setEstimateInfo("estimateInfo");
        estimateItemRespDTO.setEstimateSkuGuids(Arrays.asList("value"));
        final BillPayReqDTO billPayReqDTO = new BillPayReqDTO();
        billPayReqDTO.setDeviceType(0);
        billPayReqDTO.setDeviceId("openId");
        billPayReqDTO.setEnterpriseGuid("enterpriseGuid");
        billPayReqDTO.setStoreGuid("storeGuid");
        billPayReqDTO.setStoreName("storeName");
        billPayReqDTO.setUserGuid("openId");
        billPayReqDTO.setUserName("createName");
        billPayReqDTO.setOrderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        billPayReqDTO.setOrderFee(new BigDecimal("0.00"));
        billPayReqDTO.setAppendFee(new BigDecimal("0.00"));
        billPayReqDTO.setActuallyPayFee(new BigDecimal("0.00"));
        billPayReqDTO.setDiscountFee(new BigDecimal("0.00"));
        billPayReqDTO.setChangeFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO1.setDiscountType(0);
        discountFeeDetailDTO1.setDiscountFee(new BigDecimal("0.00"));
        discountFeeDetailDTO1.setRule("rule");
        billPayReqDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        final BillPayReqDTO.Payment payment = new BillPayReqDTO.Payment();
        payment.setAmount(new BigDecimal("0.00"));
        payment.setPaymentType(0);
        payment.setPaymentTypeName("会员卡支付");
        billPayReqDTO.setPayments(Arrays.asList(payment));
        billPayReqDTO.setFastFood(false);
        billPayReqDTO.setMemberPassWord("memberPassWord");
        billPayReqDTO.setMemberInfoCardGuid("memberInfoCardGuid");
        billPayReqDTO.setUseIntegral(0);
        billPayReqDTO.setIntegralDiscountMoney(new BigDecimal("0.00"));
        billPayReqDTO.setVersion(0);
        billPayReqDTO.setOpenId("openId");
        when(mockWxStoreDineInBillClientService.pay(billPayReqDTO)).thenReturn(estimateItemRespDTO);

        when(mockRedisUtils.get("key")).thenReturn("result");

        // Run the test
        final WxStorePayResultDTO result = wxStorePayServiceImplUnderTest.memberPay(wxMemberPayDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockWxStoreSessionDetailsService).delPrepay("storeGuid", "openId");
        verify(mockWxStoreSessionDetailsService).saveOrderState("diningTableGuid", 0);
        verify(mockWxStoreMerchantOrderService).updateOrderState("diningTableGuid", 4);
        verify(mockWxStoreSessionDetailsService).saveTablePaidUser(WxStoreConsumerDTO.builder()
                .openId("openId")
                .nickName("createName")
                .headImgUrl("headImgUrl")
                .enterpriseGuid("enterpriseGuid")
                .enterpriseName("enterpriseName")
                .storeGuid("storeGuid")
                .storeName("storeName")
                .diningTableGuid("diningTableGuid")
                .brandGuid("brandGuid")
                .build());

        // Confirm WxStoreMerchantOrderService.pushFastMsg(...).
        final WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO.setGuid("0b550432-c989-4b0f-b462-d30b2855c69f");
        wxStoreMerchantOrderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setTotalPrice(new BigDecimal("0.00"));
        wxStoreMerchantOrderDTO.setOpenId("openId");
        verify(mockWxStoreMerchantOrderService).pushFastMsg(wxStoreMerchantOrderDTO);

        // Confirm WxOrderRecordService.update(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO1 = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO1.setGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        dineinOrderDetailRespDTO1.setStoreGuid("storeGuid");
        dineinOrderDetailRespDTO1.setStoreName("storeName");
        dineinOrderDetailRespDTO1.setOriginalOrderGuid(0L);
        dineinOrderDetailRespDTO1.setTradeMode(0);
        dineinOrderDetailRespDTO1.setDeviceType(0);
        dineinOrderDetailRespDTO1.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setAppendFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setChangeFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO2 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO2.setDiscountType(0);
        discountFeeDetailDTO2.setDiscountFee(new BigDecimal("0.00"));
        discountFeeDetailDTO2.setRule("rule");
        dineinOrderDetailRespDTO1.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO2));
        dineinOrderDetailRespDTO1.setOrderNo("body");
        dineinOrderDetailRespDTO1.setState(0);
        dineinOrderDetailRespDTO1.setStateName("已支付");
        dineinOrderDetailRespDTO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO1.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO1.setMemberGuid("memberGuid");
        dineinOrderDetailRespDTO1.setMemberPhone("memberPhone");
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineinOrderDetailRespDTO1.setDineInItemDTOS(Arrays.asList(dineInItemDTO1));
        final ResponseIntegralOffset integralOffsetResultRespDTO1 = new ResponseIntegralOffset();
        integralOffsetResultRespDTO1.setUseIntegral(0);
        integralOffsetResultRespDTO1.setDeductionMoney(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setIntegralOffsetResultRespDTO(integralOffsetResultRespDTO1);
        dineinOrderDetailRespDTO1.setTip("errorMsg");
        dineinOrderDetailRespDTO1.setVersion(0);
        final WxStoreAccountDTO wxStoreAccountDTO = new WxStoreAccountDTO(dineinOrderDetailRespDTO1,
                WxStoreAdvanceConsumerReqDTO.builder()
                        .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                                .openId("openId")
                                .nickName("createName")
                                .headImgUrl("headImgUrl")
                                .enterpriseGuid("enterpriseGuid")
                                .enterpriseName("enterpriseName")
                                .storeGuid("storeGuid")
                                .storeName("storeName")
                                .diningTableGuid("diningTableGuid")
                                .brandGuid("brandGuid")
                                .build())
                        .build());
        verify(mockWxOrderRecordService).update(wxStoreAccountDTO);
    }

    @Test
    public void testMemberPay_WxStoreSessionDetailsServiceCalculateDetails2ReturnsNoItem() throws Exception {
        // Setup
        final WxMemberPayDTO wxMemberPayDTO = WxMemberPayDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .storeGuid("storeGuid")
                .tableGuid("diningTableGuid")
                .openId("openId")
                .nickName("createName")
                .headImgUrl("headImgUrl")
                .memberPassWord("memberPassWord")
                .storeName("storeName")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("createName")
                        .headImgUrl("headImgUrl")
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("diningTableGuid")
                        .brandGuid("brandGuid")
                        .build())
                .build();
        final WxStorePayResultDTO expectedResult = WxStorePayResultDTO.builder()
                .result(0)
                .errorMsg("errorMsg")
                .build();

        // Configure WxStoreSessionDetailsService.getPrepay(...).
        final WxPrepayReqDTO wxPrepayReqDTO = WxPrepayReqDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .brandGuid("brandGuid")
                .storeGuid("storeGuid")
                .tableGuid("diningTableGuid")
                .openId("openId")
                .payAmount(new BigDecimal("0.00"))
                .memberCardGuid("cardGuid")
                .cardGuid("cardGuid")
                .memberInfoCardGuid("memberInfoCardGuid")
                .memberIntegral(0)
                .volumeCode("volumeCode")
                .orderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed")
                .mode(0)
                .version(0)
                .build();
        when(mockWxStoreSessionDetailsService.getPrepay("storeGuid", "openId")).thenReturn(wxPrepayReqDTO);

        when(mockWeChatConfig.getMemberPaykey()).thenReturn("result");
        when(mockWxStoreSessionDetailsService.calculateDetails2("6e64ec83-01fe-4f5b-8759-73c1b66062ed", "openId",
                "memberInfoCardGuid", 0, "volumeCode")).thenReturn(CorrectResult.changeFailed());

        // Configure WxStoreDineInBillClientService.pay(...).
        final EstimateItemRespDTO estimateItemRespDTO = new EstimateItemRespDTO();
        estimateItemRespDTO.setResult(false);
        estimateItemRespDTO.setEstimate(false);
        estimateItemRespDTO.setErrorMsg("errorMsg");
        estimateItemRespDTO.setEstimateInfo("estimateInfo");
        estimateItemRespDTO.setEstimateSkuGuids(Arrays.asList("value"));
        final BillPayReqDTO billPayReqDTO = new BillPayReqDTO();
        billPayReqDTO.setDeviceType(0);
        billPayReqDTO.setDeviceId("openId");
        billPayReqDTO.setEnterpriseGuid("enterpriseGuid");
        billPayReqDTO.setStoreGuid("storeGuid");
        billPayReqDTO.setStoreName("storeName");
        billPayReqDTO.setUserGuid("openId");
        billPayReqDTO.setUserName("createName");
        billPayReqDTO.setOrderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        billPayReqDTO.setOrderFee(new BigDecimal("0.00"));
        billPayReqDTO.setAppendFee(new BigDecimal("0.00"));
        billPayReqDTO.setActuallyPayFee(new BigDecimal("0.00"));
        billPayReqDTO.setDiscountFee(new BigDecimal("0.00"));
        billPayReqDTO.setChangeFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        discountFeeDetailDTO.setRule("rule");
        billPayReqDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        final BillPayReqDTO.Payment payment = new BillPayReqDTO.Payment();
        payment.setAmount(new BigDecimal("0.00"));
        payment.setPaymentType(0);
        payment.setPaymentTypeName("会员卡支付");
        billPayReqDTO.setPayments(Arrays.asList(payment));
        billPayReqDTO.setFastFood(false);
        billPayReqDTO.setMemberPassWord("memberPassWord");
        billPayReqDTO.setMemberInfoCardGuid("memberInfoCardGuid");
        billPayReqDTO.setUseIntegral(0);
        billPayReqDTO.setIntegralDiscountMoney(new BigDecimal("0.00"));
        billPayReqDTO.setVersion(0);
        billPayReqDTO.setOpenId("openId");
        when(mockWxStoreDineInBillClientService.pay(billPayReqDTO)).thenReturn(estimateItemRespDTO);

        when(mockRedisUtils.get("key")).thenReturn("result");

        // Run the test
        final WxStorePayResultDTO result = wxStorePayServiceImplUnderTest.memberPay(wxMemberPayDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockWxStoreSessionDetailsService).delPrepay("storeGuid", "openId");
        verify(mockWxStoreSessionDetailsService).saveOrderState("diningTableGuid", 0);
        verify(mockWxStoreMerchantOrderService).updateOrderState("diningTableGuid", 4);
        verify(mockWxStoreSessionDetailsService).saveTablePaidUser(WxStoreConsumerDTO.builder()
                .openId("openId")
                .nickName("createName")
                .headImgUrl("headImgUrl")
                .enterpriseGuid("enterpriseGuid")
                .enterpriseName("enterpriseName")
                .storeGuid("storeGuid")
                .storeName("storeName")
                .diningTableGuid("diningTableGuid")
                .brandGuid("brandGuid")
                .build());

        // Confirm WxStoreMerchantOrderService.pushFastMsg(...).
        final WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO.setGuid("0b550432-c989-4b0f-b462-d30b2855c69f");
        wxStoreMerchantOrderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setTotalPrice(new BigDecimal("0.00"));
        wxStoreMerchantOrderDTO.setOpenId("openId");
        verify(mockWxStoreMerchantOrderService).pushFastMsg(wxStoreMerchantOrderDTO);

        // Confirm WxOrderRecordService.update(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        dineinOrderDetailRespDTO.setStoreGuid("storeGuid");
        dineinOrderDetailRespDTO.setStoreName("storeName");
        dineinOrderDetailRespDTO.setOriginalOrderGuid(0L);
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setDeviceType(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setAppendFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setChangeFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO1.setDiscountType(0);
        discountFeeDetailDTO1.setDiscountFee(new BigDecimal("0.00"));
        discountFeeDetailDTO1.setRule("rule");
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        dineinOrderDetailRespDTO.setOrderNo("body");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setStateName("已支付");
        dineinOrderDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setMemberGuid("memberGuid");
        dineinOrderDetailRespDTO.setMemberPhone("memberPhone");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        final ResponseIntegralOffset integralOffsetResultRespDTO = new ResponseIntegralOffset();
        integralOffsetResultRespDTO.setUseIntegral(0);
        integralOffsetResultRespDTO.setDeductionMoney(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setIntegralOffsetResultRespDTO(integralOffsetResultRespDTO);
        dineinOrderDetailRespDTO.setTip("errorMsg");
        dineinOrderDetailRespDTO.setVersion(0);
        final WxStoreAccountDTO wxStoreAccountDTO = new WxStoreAccountDTO(dineinOrderDetailRespDTO,
                WxStoreAdvanceConsumerReqDTO.builder()
                        .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                                .openId("openId")
                                .nickName("createName")
                                .headImgUrl("headImgUrl")
                                .enterpriseGuid("enterpriseGuid")
                                .enterpriseName("enterpriseName")
                                .storeGuid("storeGuid")
                                .storeName("storeName")
                                .diningTableGuid("diningTableGuid")
                                .brandGuid("brandGuid")
                                .build())
                        .build());
        verify(mockWxOrderRecordService).update(wxStoreAccountDTO);
    }

    @Test
    public void testMemberPay_WxStoreSessionDetailsServiceCalculateDetails2ReturnsFailure() throws Exception {
        // Setup
        final WxMemberPayDTO wxMemberPayDTO = WxMemberPayDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .storeGuid("storeGuid")
                .tableGuid("diningTableGuid")
                .openId("openId")
                .nickName("createName")
                .headImgUrl("headImgUrl")
                .memberPassWord("memberPassWord")
                .storeName("storeName")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("createName")
                        .headImgUrl("headImgUrl")
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("diningTableGuid")
                        .brandGuid("brandGuid")
                        .build())
                .build();
        final WxStorePayResultDTO expectedResult = WxStorePayResultDTO.builder()
                .result(0)
                .errorMsg("errorMsg")
                .build();

        // Configure WxStoreSessionDetailsService.getPrepay(...).
        final WxPrepayReqDTO wxPrepayReqDTO = WxPrepayReqDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .brandGuid("brandGuid")
                .storeGuid("storeGuid")
                .tableGuid("diningTableGuid")
                .openId("openId")
                .payAmount(new BigDecimal("0.00"))
                .memberCardGuid("cardGuid")
                .cardGuid("cardGuid")
                .memberInfoCardGuid("memberInfoCardGuid")
                .memberIntegral(0)
                .volumeCode("volumeCode")
                .orderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed")
                .mode(0)
                .version(0)
                .build();
        when(mockWxStoreSessionDetailsService.getPrepay("storeGuid", "openId")).thenReturn(wxPrepayReqDTO);

        when(mockWeChatConfig.getMemberPaykey()).thenReturn("result");

        // Configure WxStoreSessionDetailsService.calculateDetails2(...).
        final CorrectResult<DineinOrderDetailRespDTO> dineinOrderDetailRespDTOCorrectResult = CorrectResult.changeFailed();
        when(mockWxStoreSessionDetailsService.calculateDetails2("6e64ec83-01fe-4f5b-8759-73c1b66062ed", "openId",
                "memberInfoCardGuid", 0, "volumeCode")).thenReturn(dineinOrderDetailRespDTOCorrectResult);

        // Configure WxStoreDineInBillClientService.pay(...).
        final EstimateItemRespDTO estimateItemRespDTO = new EstimateItemRespDTO();
        estimateItemRespDTO.setResult(false);
        estimateItemRespDTO.setEstimate(false);
        estimateItemRespDTO.setErrorMsg("errorMsg");
        estimateItemRespDTO.setEstimateInfo("estimateInfo");
        estimateItemRespDTO.setEstimateSkuGuids(Arrays.asList("value"));
        final BillPayReqDTO billPayReqDTO = new BillPayReqDTO();
        billPayReqDTO.setDeviceType(0);
        billPayReqDTO.setDeviceId("openId");
        billPayReqDTO.setEnterpriseGuid("enterpriseGuid");
        billPayReqDTO.setStoreGuid("storeGuid");
        billPayReqDTO.setStoreName("storeName");
        billPayReqDTO.setUserGuid("openId");
        billPayReqDTO.setUserName("createName");
        billPayReqDTO.setOrderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        billPayReqDTO.setOrderFee(new BigDecimal("0.00"));
        billPayReqDTO.setAppendFee(new BigDecimal("0.00"));
        billPayReqDTO.setActuallyPayFee(new BigDecimal("0.00"));
        billPayReqDTO.setDiscountFee(new BigDecimal("0.00"));
        billPayReqDTO.setChangeFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        discountFeeDetailDTO.setRule("rule");
        billPayReqDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        final BillPayReqDTO.Payment payment = new BillPayReqDTO.Payment();
        payment.setAmount(new BigDecimal("0.00"));
        payment.setPaymentType(0);
        payment.setPaymentTypeName("会员卡支付");
        billPayReqDTO.setPayments(Arrays.asList(payment));
        billPayReqDTO.setFastFood(false);
        billPayReqDTO.setMemberPassWord("memberPassWord");
        billPayReqDTO.setMemberInfoCardGuid("memberInfoCardGuid");
        billPayReqDTO.setUseIntegral(0);
        billPayReqDTO.setIntegralDiscountMoney(new BigDecimal("0.00"));
        billPayReqDTO.setVersion(0);
        billPayReqDTO.setOpenId("openId");
        when(mockWxStoreDineInBillClientService.pay(billPayReqDTO)).thenReturn(estimateItemRespDTO);

        when(mockRedisUtils.get("key")).thenReturn("result");

        // Run the test
        final WxStorePayResultDTO result = wxStorePayServiceImplUnderTest.memberPay(wxMemberPayDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockWxStoreSessionDetailsService).delPrepay("storeGuid", "openId");
        verify(mockWxStoreSessionDetailsService).saveOrderState("diningTableGuid", 0);
        verify(mockWxStoreMerchantOrderService).updateOrderState("diningTableGuid", 4);
        verify(mockWxStoreSessionDetailsService).saveTablePaidUser(WxStoreConsumerDTO.builder()
                .openId("openId")
                .nickName("createName")
                .headImgUrl("headImgUrl")
                .enterpriseGuid("enterpriseGuid")
                .enterpriseName("enterpriseName")
                .storeGuid("storeGuid")
                .storeName("storeName")
                .diningTableGuid("diningTableGuid")
                .brandGuid("brandGuid")
                .build());

        // Confirm WxStoreMerchantOrderService.pushFastMsg(...).
        final WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO.setGuid("0b550432-c989-4b0f-b462-d30b2855c69f");
        wxStoreMerchantOrderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setTotalPrice(new BigDecimal("0.00"));
        wxStoreMerchantOrderDTO.setOpenId("openId");
        verify(mockWxStoreMerchantOrderService).pushFastMsg(wxStoreMerchantOrderDTO);

        // Confirm WxOrderRecordService.update(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        dineinOrderDetailRespDTO.setStoreGuid("storeGuid");
        dineinOrderDetailRespDTO.setStoreName("storeName");
        dineinOrderDetailRespDTO.setOriginalOrderGuid(0L);
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setDeviceType(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setAppendFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setChangeFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO1.setDiscountType(0);
        discountFeeDetailDTO1.setDiscountFee(new BigDecimal("0.00"));
        discountFeeDetailDTO1.setRule("rule");
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        dineinOrderDetailRespDTO.setOrderNo("body");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setStateName("已支付");
        dineinOrderDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setMemberGuid("memberGuid");
        dineinOrderDetailRespDTO.setMemberPhone("memberPhone");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        final ResponseIntegralOffset integralOffsetResultRespDTO = new ResponseIntegralOffset();
        integralOffsetResultRespDTO.setUseIntegral(0);
        integralOffsetResultRespDTO.setDeductionMoney(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setIntegralOffsetResultRespDTO(integralOffsetResultRespDTO);
        dineinOrderDetailRespDTO.setTip("errorMsg");
        dineinOrderDetailRespDTO.setVersion(0);
        final WxStoreAccountDTO wxStoreAccountDTO = new WxStoreAccountDTO(dineinOrderDetailRespDTO,
                WxStoreAdvanceConsumerReqDTO.builder()
                        .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                                .openId("openId")
                                .nickName("createName")
                                .headImgUrl("headImgUrl")
                                .enterpriseGuid("enterpriseGuid")
                                .enterpriseName("enterpriseName")
                                .storeGuid("storeGuid")
                                .storeName("storeName")
                                .diningTableGuid("diningTableGuid")
                                .brandGuid("brandGuid")
                                .build())
                        .build());
        verify(mockWxOrderRecordService).update(wxStoreAccountDTO);
    }

    @Test
    public void testGetAllPayWay() {
        // Setup
        final WxStorePayReqDTO wxStorePayReqDTO = WxStorePayReqDTO.builder()
                .storeGuid("storeGuid")
                .tableGuid("tableGuid")
                .openId("openId")
                .build();
        final WxPayWayRespDTO expectedResult = WxPayWayRespDTO.builder()
                .cardName("cardName")
                .balance(new BigDecimal("0.00"))
                .enable(0)
                .payAmount(new BigDecimal("0.00"))
                .whetherCard(0)
                .build();

        // Configure WxStoreSessionDetailsService.getPrepay(...).
        final WxPrepayReqDTO wxPrepayReqDTO = WxPrepayReqDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .brandGuid("brandGuid")
                .storeGuid("storeGuid")
                .tableGuid("diningTableGuid")
                .openId("openId")
                .payAmount(new BigDecimal("0.00"))
                .memberCardGuid("cardGuid")
                .cardGuid("cardGuid")
                .memberInfoCardGuid("memberInfoCardGuid")
                .memberIntegral(0)
                .volumeCode("volumeCode")
                .orderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed")
                .mode(0)
                .version(0)
                .build();
        when(mockWxStoreSessionDetailsService.getPrepay("storeGuid", "openId")).thenReturn(wxPrepayReqDTO);

        // Configure WxStoreSessionDetailsService.getMemberInfoAndCardList(...).
        final ResponseMemberAndCardInfoDTO responseMemberAndCardInfoDTO = new ResponseMemberAndCardInfoDTO();
        final ResponseMemberInfoDTO memberInfoDTO = new ResponseMemberInfoDTO();
        responseMemberAndCardInfoDTO.setMemberInfoDTO(memberInfoDTO);
        final ResponseMemberCard responseMemberCard = new ResponseMemberCard();
        responseMemberCard.setMemberInfoCardGuid("memberInfoCardGuid");
        responseMemberCard.setCardName("cardName");
        responseMemberCard.setCardMoney(new BigDecimal("0.00"));
        responseMemberAndCardInfoDTO.setMemberCardListRespDTOs(Arrays.asList(responseMemberCard));
        when(mockWxStoreSessionDetailsService.getMemberInfoAndCardList("enterpriseGuid", "storeGuid",
                "openId")).thenReturn(responseMemberAndCardInfoDTO);

        // Run the test
        final WxPayWayRespDTO result = wxStorePayServiceImplUnderTest.getAllPayWay(wxStorePayReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testPrepay() {
        // Setup
        final WxPrepayReqDTO wxPrepayReqDTO = WxPrepayReqDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .brandGuid("brandGuid")
                .storeGuid("storeGuid")
                .tableGuid("diningTableGuid")
                .openId("openId")
                .payAmount(new BigDecimal("0.00"))
                .memberCardGuid("cardGuid")
                .cardGuid("cardGuid")
                .memberInfoCardGuid("memberInfoCardGuid")
                .memberIntegral(0)
                .volumeCode("volumeCode")
                .orderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed")
                .mode(0)
                .version(0)
                .build();
        final WxPrepayRespDTO expectedResult = WxPrepayRespDTO.builder()
                .result(0)
                .errorMsg("errorMsg")
                .build();

        // Configure WxStoreSessionDetailsService.getPrepay(...).
        final WxPrepayReqDTO wxPrepayReqDTO1 = WxPrepayReqDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .brandGuid("brandGuid")
                .storeGuid("storeGuid")
                .tableGuid("diningTableGuid")
                .openId("openId")
                .payAmount(new BigDecimal("0.00"))
                .memberCardGuid("cardGuid")
                .cardGuid("cardGuid")
                .memberInfoCardGuid("memberInfoCardGuid")
                .memberIntegral(0)
                .volumeCode("volumeCode")
                .orderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed")
                .mode(0)
                .version(0)
                .build();
        when(mockWxStoreSessionDetailsService.getPrepay("storeGuid", "openId")).thenReturn(wxPrepayReqDTO1);

        // Configure WxStoreDineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        dineinOrderDetailRespDTO.setStoreGuid("storeGuid");
        dineinOrderDetailRespDTO.setStoreName("storeName");
        dineinOrderDetailRespDTO.setOriginalOrderGuid(0L);
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setDeviceType(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setAppendFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setChangeFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        discountFeeDetailDTO.setRule("rule");
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setOrderNo("body");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setStateName("已支付");
        dineinOrderDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setMemberGuid("memberGuid");
        dineinOrderDetailRespDTO.setMemberPhone("memberPhone");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        final ResponseIntegralOffset integralOffsetResultRespDTO = new ResponseIntegralOffset();
        integralOffsetResultRespDTO.setUseIntegral(0);
        integralOffsetResultRespDTO.setDeductionMoney(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setIntegralOffsetResultRespDTO(integralOffsetResultRespDTO);
        dineinOrderDetailRespDTO.setTip("errorMsg");
        dineinOrderDetailRespDTO.setVersion(0);
        when(mockWxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder()
                .data("6e64ec83-01fe-4f5b-8759-73c1b66062ed")
                .build())).thenReturn(dineinOrderDetailRespDTO);

        // Configure WxStoreSessionDetailsService.calculateDetails(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO1 = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO1.setGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        dineinOrderDetailRespDTO1.setStoreGuid("storeGuid");
        dineinOrderDetailRespDTO1.setStoreName("storeName");
        dineinOrderDetailRespDTO1.setOriginalOrderGuid(0L);
        dineinOrderDetailRespDTO1.setTradeMode(0);
        dineinOrderDetailRespDTO1.setDeviceType(0);
        dineinOrderDetailRespDTO1.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setAppendFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setChangeFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO1.setDiscountType(0);
        discountFeeDetailDTO1.setDiscountFee(new BigDecimal("0.00"));
        discountFeeDetailDTO1.setRule("rule");
        dineinOrderDetailRespDTO1.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        dineinOrderDetailRespDTO1.setOrderNo("body");
        dineinOrderDetailRespDTO1.setState(0);
        dineinOrderDetailRespDTO1.setStateName("已支付");
        dineinOrderDetailRespDTO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO1.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO1.setMemberGuid("memberGuid");
        dineinOrderDetailRespDTO1.setMemberPhone("memberPhone");
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineinOrderDetailRespDTO1.setDineInItemDTOS(Arrays.asList(dineInItemDTO1));
        final ResponseIntegralOffset integralOffsetResultRespDTO1 = new ResponseIntegralOffset();
        integralOffsetResultRespDTO1.setUseIntegral(0);
        integralOffsetResultRespDTO1.setDeductionMoney(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setIntegralOffsetResultRespDTO(integralOffsetResultRespDTO1);
        dineinOrderDetailRespDTO1.setTip("errorMsg");
        dineinOrderDetailRespDTO1.setVersion(0);
        final CorrectResult<DineinOrderDetailRespDTO> dineinOrderDetailRespDTOCorrectResult = new CorrectResult<>(
                "errorMsg", 0, dineinOrderDetailRespDTO1);
        when(mockWxStoreSessionDetailsService.calculateDetails("6e64ec83-01fe-4f5b-8759-73c1b66062ed", "openId",
                "memberInfoCardGuid", 0, "volumeCode")).thenReturn(dineinOrderDetailRespDTOCorrectResult);

        // Run the test
        final WxPrepayRespDTO result = wxStorePayServiceImplUnderTest.prepay(wxPrepayReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockWxStoreSessionDetailsService).savePrepay(WxPrepayReqDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .brandGuid("brandGuid")
                .storeGuid("storeGuid")
                .tableGuid("diningTableGuid")
                .openId("openId")
                .payAmount(new BigDecimal("0.00"))
                .memberCardGuid("cardGuid")
                .cardGuid("cardGuid")
                .memberInfoCardGuid("memberInfoCardGuid")
                .memberIntegral(0)
                .volumeCode("volumeCode")
                .orderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed")
                .mode(0)
                .version(0)
                .build());
    }

    @Test
    public void testPrepay_WxStoreSessionDetailsServiceCalculateDetailsReturnsNoItem() {
        // Setup
        final WxPrepayReqDTO wxPrepayReqDTO = WxPrepayReqDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .brandGuid("brandGuid")
                .storeGuid("storeGuid")
                .tableGuid("diningTableGuid")
                .openId("openId")
                .payAmount(new BigDecimal("0.00"))
                .memberCardGuid("cardGuid")
                .cardGuid("cardGuid")
                .memberInfoCardGuid("memberInfoCardGuid")
                .memberIntegral(0)
                .volumeCode("volumeCode")
                .orderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed")
                .mode(0)
                .version(0)
                .build();
        final WxPrepayRespDTO expectedResult = WxPrepayRespDTO.builder()
                .result(0)
                .errorMsg("errorMsg")
                .build();

        // Configure WxStoreSessionDetailsService.getPrepay(...).
        final WxPrepayReqDTO wxPrepayReqDTO1 = WxPrepayReqDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .brandGuid("brandGuid")
                .storeGuid("storeGuid")
                .tableGuid("diningTableGuid")
                .openId("openId")
                .payAmount(new BigDecimal("0.00"))
                .memberCardGuid("cardGuid")
                .cardGuid("cardGuid")
                .memberInfoCardGuid("memberInfoCardGuid")
                .memberIntegral(0)
                .volumeCode("volumeCode")
                .orderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed")
                .mode(0)
                .version(0)
                .build();
        when(mockWxStoreSessionDetailsService.getPrepay("storeGuid", "openId")).thenReturn(wxPrepayReqDTO1);

        // Configure WxStoreDineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        dineinOrderDetailRespDTO.setStoreGuid("storeGuid");
        dineinOrderDetailRespDTO.setStoreName("storeName");
        dineinOrderDetailRespDTO.setOriginalOrderGuid(0L);
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setDeviceType(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setAppendFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setChangeFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        discountFeeDetailDTO.setRule("rule");
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setOrderNo("body");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setStateName("已支付");
        dineinOrderDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setMemberGuid("memberGuid");
        dineinOrderDetailRespDTO.setMemberPhone("memberPhone");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        final ResponseIntegralOffset integralOffsetResultRespDTO = new ResponseIntegralOffset();
        integralOffsetResultRespDTO.setUseIntegral(0);
        integralOffsetResultRespDTO.setDeductionMoney(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setIntegralOffsetResultRespDTO(integralOffsetResultRespDTO);
        dineinOrderDetailRespDTO.setTip("errorMsg");
        dineinOrderDetailRespDTO.setVersion(0);
        when(mockWxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder()
                .data("6e64ec83-01fe-4f5b-8759-73c1b66062ed")
                .build())).thenReturn(dineinOrderDetailRespDTO);

        when(mockWxStoreSessionDetailsService.calculateDetails("6e64ec83-01fe-4f5b-8759-73c1b66062ed", "openId",
                "memberInfoCardGuid", 0, "volumeCode")).thenReturn(CorrectResult.changeFailed());

        // Run the test
        final WxPrepayRespDTO result = wxStorePayServiceImplUnderTest.prepay(wxPrepayReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockWxStoreSessionDetailsService).savePrepay(WxPrepayReqDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .brandGuid("brandGuid")
                .storeGuid("storeGuid")
                .tableGuid("diningTableGuid")
                .openId("openId")
                .payAmount(new BigDecimal("0.00"))
                .memberCardGuid("cardGuid")
                .cardGuid("cardGuid")
                .memberInfoCardGuid("memberInfoCardGuid")
                .memberIntegral(0)
                .volumeCode("volumeCode")
                .orderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed")
                .mode(0)
                .version(0)
                .build());
    }

    @Test
    public void testPrepay_WxStoreSessionDetailsServiceCalculateDetailsReturnsFailure() {
        // Setup
        final WxPrepayReqDTO wxPrepayReqDTO = WxPrepayReqDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .brandGuid("brandGuid")
                .storeGuid("storeGuid")
                .tableGuid("diningTableGuid")
                .openId("openId")
                .payAmount(new BigDecimal("0.00"))
                .memberCardGuid("cardGuid")
                .cardGuid("cardGuid")
                .memberInfoCardGuid("memberInfoCardGuid")
                .memberIntegral(0)
                .volumeCode("volumeCode")
                .orderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed")
                .mode(0)
                .version(0)
                .build();
        final WxPrepayRespDTO expectedResult = WxPrepayRespDTO.builder()
                .result(0)
                .errorMsg("errorMsg")
                .build();

        // Configure WxStoreSessionDetailsService.getPrepay(...).
        final WxPrepayReqDTO wxPrepayReqDTO1 = WxPrepayReqDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .brandGuid("brandGuid")
                .storeGuid("storeGuid")
                .tableGuid("diningTableGuid")
                .openId("openId")
                .payAmount(new BigDecimal("0.00"))
                .memberCardGuid("cardGuid")
                .cardGuid("cardGuid")
                .memberInfoCardGuid("memberInfoCardGuid")
                .memberIntegral(0)
                .volumeCode("volumeCode")
                .orderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed")
                .mode(0)
                .version(0)
                .build();
        when(mockWxStoreSessionDetailsService.getPrepay("storeGuid", "openId")).thenReturn(wxPrepayReqDTO1);

        // Configure WxStoreDineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        dineinOrderDetailRespDTO.setStoreGuid("storeGuid");
        dineinOrderDetailRespDTO.setStoreName("storeName");
        dineinOrderDetailRespDTO.setOriginalOrderGuid(0L);
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setDeviceType(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setAppendFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setChangeFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        discountFeeDetailDTO.setRule("rule");
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setOrderNo("body");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setStateName("已支付");
        dineinOrderDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setMemberGuid("memberGuid");
        dineinOrderDetailRespDTO.setMemberPhone("memberPhone");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        final ResponseIntegralOffset integralOffsetResultRespDTO = new ResponseIntegralOffset();
        integralOffsetResultRespDTO.setUseIntegral(0);
        integralOffsetResultRespDTO.setDeductionMoney(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setIntegralOffsetResultRespDTO(integralOffsetResultRespDTO);
        dineinOrderDetailRespDTO.setTip("errorMsg");
        dineinOrderDetailRespDTO.setVersion(0);
        when(mockWxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder()
                .data("6e64ec83-01fe-4f5b-8759-73c1b66062ed")
                .build())).thenReturn(dineinOrderDetailRespDTO);

        // Configure WxStoreSessionDetailsService.calculateDetails(...).
        final CorrectResult<DineinOrderDetailRespDTO> dineinOrderDetailRespDTOCorrectResult = CorrectResult.changeFailed();
        when(mockWxStoreSessionDetailsService.calculateDetails("6e64ec83-01fe-4f5b-8759-73c1b66062ed", "openId",
                "memberInfoCardGuid", 0, "volumeCode")).thenReturn(dineinOrderDetailRespDTOCorrectResult);

        // Run the test
        final WxPrepayRespDTO result = wxStorePayServiceImplUnderTest.prepay(wxPrepayReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockWxStoreSessionDetailsService).savePrepay(WxPrepayReqDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .brandGuid("brandGuid")
                .storeGuid("storeGuid")
                .tableGuid("diningTableGuid")
                .openId("openId")
                .payAmount(new BigDecimal("0.00"))
                .memberCardGuid("cardGuid")
                .cardGuid("cardGuid")
                .memberInfoCardGuid("memberInfoCardGuid")
                .memberIntegral(0)
                .volumeCode("volumeCode")
                .orderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed")
                .mode(0)
                .version(0)
                .build());
    }

    @Test
    public void testValidateOrder() {
        // Setup
        final WxStorePayReqDTO wxStorePayReqDTO = WxStorePayReqDTO.builder()
                .storeGuid("storeGuid")
                .tableGuid("tableGuid")
                .openId("openId")
                .build();
        final WxPrepayRespDTO expectedResult = WxPrepayRespDTO.builder()
                .result(0)
                .errorMsg("errorMsg")
                .build();
        when(mockWxStoreMenuDetailsService.judgeOrderType("storeGuid")).thenReturn(false);
        when(mockWxStoreSessionDetailsService.getFastOrderGuid("storeGuid", "openId"))
                .thenReturn("6e64ec83-01fe-4f5b-8759-73c1b66062ed");

        // Configure WxStoreDineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        dineinOrderDetailRespDTO.setStoreGuid("storeGuid");
        dineinOrderDetailRespDTO.setStoreName("storeName");
        dineinOrderDetailRespDTO.setOriginalOrderGuid(0L);
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setDeviceType(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setAppendFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setChangeFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        discountFeeDetailDTO.setRule("rule");
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setOrderNo("body");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setStateName("已支付");
        dineinOrderDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setMemberGuid("memberGuid");
        dineinOrderDetailRespDTO.setMemberPhone("memberPhone");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        final ResponseIntegralOffset integralOffsetResultRespDTO = new ResponseIntegralOffset();
        integralOffsetResultRespDTO.setUseIntegral(0);
        integralOffsetResultRespDTO.setDeductionMoney(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setIntegralOffsetResultRespDTO(integralOffsetResultRespDTO);
        dineinOrderDetailRespDTO.setTip("errorMsg");
        dineinOrderDetailRespDTO.setVersion(0);
        when(mockWxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder()
                .data("6e64ec83-01fe-4f5b-8759-73c1b66062ed")
                .build())).thenReturn(dineinOrderDetailRespDTO);

        // Run the test
        final WxPrepayRespDTO result = wxStorePayServiceImplUnderTest.validateOrder(wxStorePayReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testValidateOrder_WxStoreMenuDetailsServiceReturnsTrue() {
        // Setup
        final WxStorePayReqDTO wxStorePayReqDTO = WxStorePayReqDTO.builder()
                .storeGuid("storeGuid")
                .tableGuid("tableGuid")
                .openId("openId")
                .build();
        final WxPrepayRespDTO expectedResult = WxPrepayRespDTO.builder()
                .result(0)
                .errorMsg("errorMsg")
                .build();
        when(mockWxStoreMenuDetailsService.judgeOrderType("storeGuid")).thenReturn(true);
        when(mockWxStoreTableClientService.getOrderGuid("tableGuid"))
                .thenReturn("6e64ec83-01fe-4f5b-8759-73c1b66062ed");

        // Configure WxStoreDineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        dineinOrderDetailRespDTO.setStoreGuid("storeGuid");
        dineinOrderDetailRespDTO.setStoreName("storeName");
        dineinOrderDetailRespDTO.setOriginalOrderGuid(0L);
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setDeviceType(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setAppendFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setChangeFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        discountFeeDetailDTO.setRule("rule");
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setOrderNo("body");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setStateName("已支付");
        dineinOrderDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setMemberGuid("memberGuid");
        dineinOrderDetailRespDTO.setMemberPhone("memberPhone");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        final ResponseIntegralOffset integralOffsetResultRespDTO = new ResponseIntegralOffset();
        integralOffsetResultRespDTO.setUseIntegral(0);
        integralOffsetResultRespDTO.setDeductionMoney(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setIntegralOffsetResultRespDTO(integralOffsetResultRespDTO);
        dineinOrderDetailRespDTO.setTip("errorMsg");
        dineinOrderDetailRespDTO.setVersion(0);
        when(mockWxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder()
                .data("6e64ec83-01fe-4f5b-8759-73c1b66062ed")
                .build())).thenReturn(dineinOrderDetailRespDTO);

        // Run the test
        final WxPrepayRespDTO result = wxStorePayServiceImplUnderTest.validateOrder(wxStorePayReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testMemberConfirm() {
        // Setup
        final WxPrepayConfirmReqDTO wxPrepayConfirmReqDTO = WxPrepayConfirmReqDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .brandGuid("brandGuid")
                .storeGuid("storeGuid")
                .tableGuid("diningTableGuid")
                .openId("openId")
                .cardGuid("cardGuid")
                .memberInfoCardGuid("memberInfoCardGuid")
                .memberIntegral(0)
                .volumeCode("volumeCode")
                .build();
        final WxPrepayConfirmRespDTO expectedResult = WxPrepayConfirmRespDTO.builder()
                .errorMsg("errorMsg")
                .result(0)
                .build();
        when(mockWxStoreSessionDetailsService.getOrderGuid("storeGuid", "diningTableGuid", "openId"))
                .thenReturn("orderGuid");

        // Configure WxStoreSessionDetailsService.calculateDetails(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        dineinOrderDetailRespDTO.setStoreGuid("storeGuid");
        dineinOrderDetailRespDTO.setStoreName("storeName");
        dineinOrderDetailRespDTO.setOriginalOrderGuid(0L);
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setDeviceType(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setAppendFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setChangeFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        discountFeeDetailDTO.setRule("rule");
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setOrderNo("body");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setStateName("已支付");
        dineinOrderDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setMemberGuid("memberGuid");
        dineinOrderDetailRespDTO.setMemberPhone("memberPhone");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        final ResponseIntegralOffset integralOffsetResultRespDTO = new ResponseIntegralOffset();
        integralOffsetResultRespDTO.setUseIntegral(0);
        integralOffsetResultRespDTO.setDeductionMoney(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setIntegralOffsetResultRespDTO(integralOffsetResultRespDTO);
        dineinOrderDetailRespDTO.setTip("errorMsg");
        dineinOrderDetailRespDTO.setVersion(0);
        final CorrectResult<DineinOrderDetailRespDTO> dineinOrderDetailRespDTOCorrectResult = new CorrectResult<>(
                "errorMsg", 0, dineinOrderDetailRespDTO);
        when(mockWxStoreSessionDetailsService.calculateDetails("orderGuid", "openId", "memberInfoCardGuid", 0,
                "volumeCode")).thenReturn(dineinOrderDetailRespDTOCorrectResult);

        // Configure WxStoreSessionDetailsService.getPrepay(...).
        final WxPrepayReqDTO wxPrepayReqDTO = WxPrepayReqDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .brandGuid("brandGuid")
                .storeGuid("storeGuid")
                .tableGuid("diningTableGuid")
                .openId("openId")
                .payAmount(new BigDecimal("0.00"))
                .memberCardGuid("cardGuid")
                .cardGuid("cardGuid")
                .memberInfoCardGuid("memberInfoCardGuid")
                .memberIntegral(0)
                .volumeCode("volumeCode")
                .orderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed")
                .mode(0)
                .version(0)
                .build();
        when(mockWxStoreSessionDetailsService.getPrepay("storeGuid", "openId")).thenReturn(wxPrepayReqDTO);

        // Run the test
        final WxPrepayConfirmRespDTO result = wxStorePayServiceImplUnderTest.memberConfirm(wxPrepayConfirmReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockWxStoreSessionDetailsService).delPrepay("storeGuid", "openId");
        verify(mockWxStoreSessionDetailsService).savePrepay(WxPrepayReqDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .brandGuid("brandGuid")
                .storeGuid("storeGuid")
                .tableGuid("diningTableGuid")
                .openId("openId")
                .payAmount(new BigDecimal("0.00"))
                .memberCardGuid("cardGuid")
                .cardGuid("cardGuid")
                .memberInfoCardGuid("memberInfoCardGuid")
                .memberIntegral(0)
                .volumeCode("volumeCode")
                .orderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed")
                .mode(0)
                .version(0)
                .build());
    }

    @Test
    public void testMemberConfirm_WxStoreSessionDetailsServiceCalculateDetailsReturnsNoItem() {
        // Setup
        final WxPrepayConfirmReqDTO wxPrepayConfirmReqDTO = WxPrepayConfirmReqDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .brandGuid("brandGuid")
                .storeGuid("storeGuid")
                .tableGuid("diningTableGuid")
                .openId("openId")
                .cardGuid("cardGuid")
                .memberInfoCardGuid("memberInfoCardGuid")
                .memberIntegral(0)
                .volumeCode("volumeCode")
                .build();
        final WxPrepayConfirmRespDTO expectedResult = WxPrepayConfirmRespDTO.builder()
                .errorMsg("errorMsg")
                .result(0)
                .build();
        when(mockWxStoreSessionDetailsService.getOrderGuid("storeGuid", "diningTableGuid", "openId"))
                .thenReturn("orderGuid");
        when(mockWxStoreSessionDetailsService.calculateDetails("orderGuid", "openId", "memberInfoCardGuid", 0,
                "volumeCode")).thenReturn(CorrectResult.changeFailed());

        // Configure WxStoreSessionDetailsService.getPrepay(...).
        final WxPrepayReqDTO wxPrepayReqDTO = WxPrepayReqDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .brandGuid("brandGuid")
                .storeGuid("storeGuid")
                .tableGuid("diningTableGuid")
                .openId("openId")
                .payAmount(new BigDecimal("0.00"))
                .memberCardGuid("cardGuid")
                .cardGuid("cardGuid")
                .memberInfoCardGuid("memberInfoCardGuid")
                .memberIntegral(0)
                .volumeCode("volumeCode")
                .orderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed")
                .mode(0)
                .version(0)
                .build();
        when(mockWxStoreSessionDetailsService.getPrepay("storeGuid", "openId")).thenReturn(wxPrepayReqDTO);

        // Run the test
        final WxPrepayConfirmRespDTO result = wxStorePayServiceImplUnderTest.memberConfirm(wxPrepayConfirmReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockWxStoreSessionDetailsService).delPrepay("storeGuid", "openId");
        verify(mockWxStoreSessionDetailsService).savePrepay(WxPrepayReqDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .brandGuid("brandGuid")
                .storeGuid("storeGuid")
                .tableGuid("diningTableGuid")
                .openId("openId")
                .payAmount(new BigDecimal("0.00"))
                .memberCardGuid("cardGuid")
                .cardGuid("cardGuid")
                .memberInfoCardGuid("memberInfoCardGuid")
                .memberIntegral(0)
                .volumeCode("volumeCode")
                .orderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed")
                .mode(0)
                .version(0)
                .build());
    }

    @Test
    public void testMemberConfirm_WxStoreSessionDetailsServiceCalculateDetailsReturnsFailure() {
        // Setup
        final WxPrepayConfirmReqDTO wxPrepayConfirmReqDTO = WxPrepayConfirmReqDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .brandGuid("brandGuid")
                .storeGuid("storeGuid")
                .tableGuid("diningTableGuid")
                .openId("openId")
                .cardGuid("cardGuid")
                .memberInfoCardGuid("memberInfoCardGuid")
                .memberIntegral(0)
                .volumeCode("volumeCode")
                .build();
        final WxPrepayConfirmRespDTO expectedResult = WxPrepayConfirmRespDTO.builder()
                .errorMsg("errorMsg")
                .result(0)
                .build();
        when(mockWxStoreSessionDetailsService.getOrderGuid("storeGuid", "diningTableGuid", "openId"))
                .thenReturn("orderGuid");

        // Configure WxStoreSessionDetailsService.calculateDetails(...).
        final CorrectResult<DineinOrderDetailRespDTO> dineinOrderDetailRespDTOCorrectResult = CorrectResult.changeFailed();
        when(mockWxStoreSessionDetailsService.calculateDetails("orderGuid", "openId", "memberInfoCardGuid", 0,
                "volumeCode")).thenReturn(dineinOrderDetailRespDTOCorrectResult);

        // Configure WxStoreSessionDetailsService.getPrepay(...).
        final WxPrepayReqDTO wxPrepayReqDTO = WxPrepayReqDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .brandGuid("brandGuid")
                .storeGuid("storeGuid")
                .tableGuid("diningTableGuid")
                .openId("openId")
                .payAmount(new BigDecimal("0.00"))
                .memberCardGuid("cardGuid")
                .cardGuid("cardGuid")
                .memberInfoCardGuid("memberInfoCardGuid")
                .memberIntegral(0)
                .volumeCode("volumeCode")
                .orderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed")
                .mode(0)
                .version(0)
                .build();
        when(mockWxStoreSessionDetailsService.getPrepay("storeGuid", "openId")).thenReturn(wxPrepayReqDTO);

        // Run the test
        final WxPrepayConfirmRespDTO result = wxStorePayServiceImplUnderTest.memberConfirm(wxPrepayConfirmReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockWxStoreSessionDetailsService).delPrepay("storeGuid", "openId");
        verify(mockWxStoreSessionDetailsService).savePrepay(WxPrepayReqDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .brandGuid("brandGuid")
                .storeGuid("storeGuid")
                .tableGuid("diningTableGuid")
                .openId("openId")
                .payAmount(new BigDecimal("0.00"))
                .memberCardGuid("cardGuid")
                .cardGuid("cardGuid")
                .memberInfoCardGuid("memberInfoCardGuid")
                .memberIntegral(0)
                .volumeCode("volumeCode")
                .orderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed")
                .mode(0)
                .version(0)
                .build());
    }

    @Test
    public void testZeroPay() {
        // Setup
        final WxZeroPayReqDTO wxZeroPayReqDTO = WxZeroPayReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("createName")
                        .headImgUrl("headImgUrl")
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("diningTableGuid")
                        .brandGuid("brandGuid")
                        .build())
                .build();
        final WxStorePayResultDTO expectedResult = WxStorePayResultDTO.builder()
                .result(0)
                .errorMsg("errorMsg")
                .build();

        // Configure WxStoreSessionDetailsService.getPrepay(...).
        final WxPrepayReqDTO wxPrepayReqDTO = WxPrepayReqDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .brandGuid("brandGuid")
                .storeGuid("storeGuid")
                .tableGuid("diningTableGuid")
                .openId("openId")
                .payAmount(new BigDecimal("0.00"))
                .memberCardGuid("cardGuid")
                .cardGuid("cardGuid")
                .memberInfoCardGuid("memberInfoCardGuid")
                .memberIntegral(0)
                .volumeCode("volumeCode")
                .orderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed")
                .mode(0)
                .version(0)
                .build();
        when(mockWxStoreSessionDetailsService.getPrepay("storeGuid", "openId")).thenReturn(wxPrepayReqDTO);

        // Configure WxStoreSessionDetailsService.calculateDetails2(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        dineinOrderDetailRespDTO.setStoreGuid("storeGuid");
        dineinOrderDetailRespDTO.setStoreName("storeName");
        dineinOrderDetailRespDTO.setOriginalOrderGuid(0L);
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setDeviceType(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setAppendFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setChangeFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        discountFeeDetailDTO.setRule("rule");
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setOrderNo("body");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setStateName("已支付");
        dineinOrderDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setMemberGuid("memberGuid");
        dineinOrderDetailRespDTO.setMemberPhone("memberPhone");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        final ResponseIntegralOffset integralOffsetResultRespDTO = new ResponseIntegralOffset();
        integralOffsetResultRespDTO.setUseIntegral(0);
        integralOffsetResultRespDTO.setDeductionMoney(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setIntegralOffsetResultRespDTO(integralOffsetResultRespDTO);
        dineinOrderDetailRespDTO.setTip("errorMsg");
        dineinOrderDetailRespDTO.setVersion(0);
        final CorrectResult<DineinOrderDetailRespDTO> dineinOrderDetailRespDTOCorrectResult = new CorrectResult<>(
                "errorMsg", 0, dineinOrderDetailRespDTO);
        when(mockWxStoreSessionDetailsService.calculateDetails2("6e64ec83-01fe-4f5b-8759-73c1b66062ed", "openId",
                "memberInfoCardGuid", 0, "volumeCode")).thenReturn(dineinOrderDetailRespDTOCorrectResult);

        // Configure WxStoreDineInBillClientService.pay(...).
        final EstimateItemRespDTO estimateItemRespDTO = new EstimateItemRespDTO();
        estimateItemRespDTO.setResult(false);
        estimateItemRespDTO.setEstimate(false);
        estimateItemRespDTO.setErrorMsg("errorMsg");
        estimateItemRespDTO.setEstimateInfo("estimateInfo");
        estimateItemRespDTO.setEstimateSkuGuids(Arrays.asList("value"));
        final BillPayReqDTO billPayReqDTO = new BillPayReqDTO();
        billPayReqDTO.setDeviceType(0);
        billPayReqDTO.setDeviceId("openId");
        billPayReqDTO.setEnterpriseGuid("enterpriseGuid");
        billPayReqDTO.setStoreGuid("storeGuid");
        billPayReqDTO.setStoreName("storeName");
        billPayReqDTO.setUserGuid("openId");
        billPayReqDTO.setUserName("createName");
        billPayReqDTO.setOrderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        billPayReqDTO.setOrderFee(new BigDecimal("0.00"));
        billPayReqDTO.setAppendFee(new BigDecimal("0.00"));
        billPayReqDTO.setActuallyPayFee(new BigDecimal("0.00"));
        billPayReqDTO.setDiscountFee(new BigDecimal("0.00"));
        billPayReqDTO.setChangeFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO1.setDiscountType(0);
        discountFeeDetailDTO1.setDiscountFee(new BigDecimal("0.00"));
        discountFeeDetailDTO1.setRule("rule");
        billPayReqDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        final BillPayReqDTO.Payment payment = new BillPayReqDTO.Payment();
        payment.setAmount(new BigDecimal("0.00"));
        payment.setPaymentType(0);
        payment.setPaymentTypeName("会员卡支付");
        billPayReqDTO.setPayments(Arrays.asList(payment));
        billPayReqDTO.setFastFood(false);
        billPayReqDTO.setMemberPassWord("memberPassWord");
        billPayReqDTO.setMemberInfoCardGuid("memberInfoCardGuid");
        billPayReqDTO.setUseIntegral(0);
        billPayReqDTO.setIntegralDiscountMoney(new BigDecimal("0.00"));
        billPayReqDTO.setVersion(0);
        billPayReqDTO.setOpenId("openId");
        when(mockWxStoreDineInBillClientService.pay(billPayReqDTO)).thenReturn(estimateItemRespDTO);

        when(mockRedisUtils.get("key")).thenReturn("result");

        // Run the test
        final WxStorePayResultDTO result = wxStorePayServiceImplUnderTest.zeroPay(wxZeroPayReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockWxStoreSessionDetailsService).saveOrderState("diningTableGuid", 0);
        verify(mockWxStoreMerchantOrderService).updateOrderState("diningTableGuid", 4);
        verify(mockWxStoreSessionDetailsService).saveTablePaidUser(WxStoreConsumerDTO.builder()
                .openId("openId")
                .nickName("createName")
                .headImgUrl("headImgUrl")
                .enterpriseGuid("enterpriseGuid")
                .enterpriseName("enterpriseName")
                .storeGuid("storeGuid")
                .storeName("storeName")
                .diningTableGuid("diningTableGuid")
                .brandGuid("brandGuid")
                .build());

        // Confirm WxStoreMerchantOrderService.pushFastMsg(...).
        final WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO.setGuid("0b550432-c989-4b0f-b462-d30b2855c69f");
        wxStoreMerchantOrderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setTotalPrice(new BigDecimal("0.00"));
        wxStoreMerchantOrderDTO.setOpenId("openId");
        verify(mockWxStoreMerchantOrderService).pushFastMsg(wxStoreMerchantOrderDTO);

        // Confirm WxOrderRecordService.update(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO1 = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO1.setGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        dineinOrderDetailRespDTO1.setStoreGuid("storeGuid");
        dineinOrderDetailRespDTO1.setStoreName("storeName");
        dineinOrderDetailRespDTO1.setOriginalOrderGuid(0L);
        dineinOrderDetailRespDTO1.setTradeMode(0);
        dineinOrderDetailRespDTO1.setDeviceType(0);
        dineinOrderDetailRespDTO1.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setAppendFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setChangeFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO2 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO2.setDiscountType(0);
        discountFeeDetailDTO2.setDiscountFee(new BigDecimal("0.00"));
        discountFeeDetailDTO2.setRule("rule");
        dineinOrderDetailRespDTO1.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO2));
        dineinOrderDetailRespDTO1.setOrderNo("body");
        dineinOrderDetailRespDTO1.setState(0);
        dineinOrderDetailRespDTO1.setStateName("已支付");
        dineinOrderDetailRespDTO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO1.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO1.setMemberGuid("memberGuid");
        dineinOrderDetailRespDTO1.setMemberPhone("memberPhone");
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineinOrderDetailRespDTO1.setDineInItemDTOS(Arrays.asList(dineInItemDTO1));
        final ResponseIntegralOffset integralOffsetResultRespDTO1 = new ResponseIntegralOffset();
        integralOffsetResultRespDTO1.setUseIntegral(0);
        integralOffsetResultRespDTO1.setDeductionMoney(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setIntegralOffsetResultRespDTO(integralOffsetResultRespDTO1);
        dineinOrderDetailRespDTO1.setTip("errorMsg");
        dineinOrderDetailRespDTO1.setVersion(0);
        final WxStoreAccountDTO wxStoreAccountDTO = new WxStoreAccountDTO(dineinOrderDetailRespDTO1,
                WxStoreAdvanceConsumerReqDTO.builder()
                        .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                                .openId("openId")
                                .nickName("createName")
                                .headImgUrl("headImgUrl")
                                .enterpriseGuid("enterpriseGuid")
                                .enterpriseName("enterpriseName")
                                .storeGuid("storeGuid")
                                .storeName("storeName")
                                .diningTableGuid("diningTableGuid")
                                .brandGuid("brandGuid")
                                .build())
                        .build());
        verify(mockWxOrderRecordService).update(wxStoreAccountDTO);
        verify(mockWxStoreSessionDetailsService).delPrepay("storeGuid", "openId");
    }

    @Test
    public void testZeroPay_WxStoreSessionDetailsServiceCalculateDetails2ReturnsNoItem() {
        // Setup
        final WxZeroPayReqDTO wxZeroPayReqDTO = WxZeroPayReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("createName")
                        .headImgUrl("headImgUrl")
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("diningTableGuid")
                        .brandGuid("brandGuid")
                        .build())
                .build();
        final WxStorePayResultDTO expectedResult = WxStorePayResultDTO.builder()
                .result(0)
                .errorMsg("errorMsg")
                .build();

        // Configure WxStoreSessionDetailsService.getPrepay(...).
        final WxPrepayReqDTO wxPrepayReqDTO = WxPrepayReqDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .brandGuid("brandGuid")
                .storeGuid("storeGuid")
                .tableGuid("diningTableGuid")
                .openId("openId")
                .payAmount(new BigDecimal("0.00"))
                .memberCardGuid("cardGuid")
                .cardGuid("cardGuid")
                .memberInfoCardGuid("memberInfoCardGuid")
                .memberIntegral(0)
                .volumeCode("volumeCode")
                .orderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed")
                .mode(0)
                .version(0)
                .build();
        when(mockWxStoreSessionDetailsService.getPrepay("storeGuid", "openId")).thenReturn(wxPrepayReqDTO);

        when(mockWxStoreSessionDetailsService.calculateDetails2("6e64ec83-01fe-4f5b-8759-73c1b66062ed", "openId",
                "memberInfoCardGuid", 0, "volumeCode")).thenReturn(CorrectResult.changeFailed());

        // Configure WxStoreDineInBillClientService.pay(...).
        final EstimateItemRespDTO estimateItemRespDTO = new EstimateItemRespDTO();
        estimateItemRespDTO.setResult(false);
        estimateItemRespDTO.setEstimate(false);
        estimateItemRespDTO.setErrorMsg("errorMsg");
        estimateItemRespDTO.setEstimateInfo("estimateInfo");
        estimateItemRespDTO.setEstimateSkuGuids(Arrays.asList("value"));
        final BillPayReqDTO billPayReqDTO = new BillPayReqDTO();
        billPayReqDTO.setDeviceType(0);
        billPayReqDTO.setDeviceId("openId");
        billPayReqDTO.setEnterpriseGuid("enterpriseGuid");
        billPayReqDTO.setStoreGuid("storeGuid");
        billPayReqDTO.setStoreName("storeName");
        billPayReqDTO.setUserGuid("openId");
        billPayReqDTO.setUserName("createName");
        billPayReqDTO.setOrderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        billPayReqDTO.setOrderFee(new BigDecimal("0.00"));
        billPayReqDTO.setAppendFee(new BigDecimal("0.00"));
        billPayReqDTO.setActuallyPayFee(new BigDecimal("0.00"));
        billPayReqDTO.setDiscountFee(new BigDecimal("0.00"));
        billPayReqDTO.setChangeFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        discountFeeDetailDTO.setRule("rule");
        billPayReqDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        final BillPayReqDTO.Payment payment = new BillPayReqDTO.Payment();
        payment.setAmount(new BigDecimal("0.00"));
        payment.setPaymentType(0);
        payment.setPaymentTypeName("会员卡支付");
        billPayReqDTO.setPayments(Arrays.asList(payment));
        billPayReqDTO.setFastFood(false);
        billPayReqDTO.setMemberPassWord("memberPassWord");
        billPayReqDTO.setMemberInfoCardGuid("memberInfoCardGuid");
        billPayReqDTO.setUseIntegral(0);
        billPayReqDTO.setIntegralDiscountMoney(new BigDecimal("0.00"));
        billPayReqDTO.setVersion(0);
        billPayReqDTO.setOpenId("openId");
        when(mockWxStoreDineInBillClientService.pay(billPayReqDTO)).thenReturn(estimateItemRespDTO);

        when(mockRedisUtils.get("key")).thenReturn("result");

        // Run the test
        final WxStorePayResultDTO result = wxStorePayServiceImplUnderTest.zeroPay(wxZeroPayReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockWxStoreSessionDetailsService).saveOrderState("diningTableGuid", 0);
        verify(mockWxStoreMerchantOrderService).updateOrderState("diningTableGuid", 4);
        verify(mockWxStoreSessionDetailsService).saveTablePaidUser(WxStoreConsumerDTO.builder()
                .openId("openId")
                .nickName("createName")
                .headImgUrl("headImgUrl")
                .enterpriseGuid("enterpriseGuid")
                .enterpriseName("enterpriseName")
                .storeGuid("storeGuid")
                .storeName("storeName")
                .diningTableGuid("diningTableGuid")
                .brandGuid("brandGuid")
                .build());

        // Confirm WxStoreMerchantOrderService.pushFastMsg(...).
        final WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO.setGuid("0b550432-c989-4b0f-b462-d30b2855c69f");
        wxStoreMerchantOrderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setTotalPrice(new BigDecimal("0.00"));
        wxStoreMerchantOrderDTO.setOpenId("openId");
        verify(mockWxStoreMerchantOrderService).pushFastMsg(wxStoreMerchantOrderDTO);

        // Confirm WxOrderRecordService.update(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        dineinOrderDetailRespDTO.setStoreGuid("storeGuid");
        dineinOrderDetailRespDTO.setStoreName("storeName");
        dineinOrderDetailRespDTO.setOriginalOrderGuid(0L);
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setDeviceType(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setAppendFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setChangeFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO1.setDiscountType(0);
        discountFeeDetailDTO1.setDiscountFee(new BigDecimal("0.00"));
        discountFeeDetailDTO1.setRule("rule");
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        dineinOrderDetailRespDTO.setOrderNo("body");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setStateName("已支付");
        dineinOrderDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setMemberGuid("memberGuid");
        dineinOrderDetailRespDTO.setMemberPhone("memberPhone");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        final ResponseIntegralOffset integralOffsetResultRespDTO = new ResponseIntegralOffset();
        integralOffsetResultRespDTO.setUseIntegral(0);
        integralOffsetResultRespDTO.setDeductionMoney(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setIntegralOffsetResultRespDTO(integralOffsetResultRespDTO);
        dineinOrderDetailRespDTO.setTip("errorMsg");
        dineinOrderDetailRespDTO.setVersion(0);
        final WxStoreAccountDTO wxStoreAccountDTO = new WxStoreAccountDTO(dineinOrderDetailRespDTO,
                WxStoreAdvanceConsumerReqDTO.builder()
                        .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                                .openId("openId")
                                .nickName("createName")
                                .headImgUrl("headImgUrl")
                                .enterpriseGuid("enterpriseGuid")
                                .enterpriseName("enterpriseName")
                                .storeGuid("storeGuid")
                                .storeName("storeName")
                                .diningTableGuid("diningTableGuid")
                                .brandGuid("brandGuid")
                                .build())
                        .build());
        verify(mockWxOrderRecordService).update(wxStoreAccountDTO);
        verify(mockWxStoreSessionDetailsService).delPrepay("storeGuid", "openId");
    }

    @Test
    public void testZeroPay_WxStoreSessionDetailsServiceCalculateDetails2ReturnsFailure() {
        // Setup
        final WxZeroPayReqDTO wxZeroPayReqDTO = WxZeroPayReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("createName")
                        .headImgUrl("headImgUrl")
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("diningTableGuid")
                        .brandGuid("brandGuid")
                        .build())
                .build();
        final WxStorePayResultDTO expectedResult = WxStorePayResultDTO.builder()
                .result(0)
                .errorMsg("errorMsg")
                .build();

        // Configure WxStoreSessionDetailsService.getPrepay(...).
        final WxPrepayReqDTO wxPrepayReqDTO = WxPrepayReqDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .brandGuid("brandGuid")
                .storeGuid("storeGuid")
                .tableGuid("diningTableGuid")
                .openId("openId")
                .payAmount(new BigDecimal("0.00"))
                .memberCardGuid("cardGuid")
                .cardGuid("cardGuid")
                .memberInfoCardGuid("memberInfoCardGuid")
                .memberIntegral(0)
                .volumeCode("volumeCode")
                .orderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed")
                .mode(0)
                .version(0)
                .build();
        when(mockWxStoreSessionDetailsService.getPrepay("storeGuid", "openId")).thenReturn(wxPrepayReqDTO);

        // Configure WxStoreSessionDetailsService.calculateDetails2(...).
        final CorrectResult<DineinOrderDetailRespDTO> dineinOrderDetailRespDTOCorrectResult = CorrectResult.changeFailed();
        when(mockWxStoreSessionDetailsService.calculateDetails2("6e64ec83-01fe-4f5b-8759-73c1b66062ed", "openId",
                "memberInfoCardGuid", 0, "volumeCode")).thenReturn(dineinOrderDetailRespDTOCorrectResult);

        // Configure WxStoreDineInBillClientService.pay(...).
        final EstimateItemRespDTO estimateItemRespDTO = new EstimateItemRespDTO();
        estimateItemRespDTO.setResult(false);
        estimateItemRespDTO.setEstimate(false);
        estimateItemRespDTO.setErrorMsg("errorMsg");
        estimateItemRespDTO.setEstimateInfo("estimateInfo");
        estimateItemRespDTO.setEstimateSkuGuids(Arrays.asList("value"));
        final BillPayReqDTO billPayReqDTO = new BillPayReqDTO();
        billPayReqDTO.setDeviceType(0);
        billPayReqDTO.setDeviceId("openId");
        billPayReqDTO.setEnterpriseGuid("enterpriseGuid");
        billPayReqDTO.setStoreGuid("storeGuid");
        billPayReqDTO.setStoreName("storeName");
        billPayReqDTO.setUserGuid("openId");
        billPayReqDTO.setUserName("createName");
        billPayReqDTO.setOrderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        billPayReqDTO.setOrderFee(new BigDecimal("0.00"));
        billPayReqDTO.setAppendFee(new BigDecimal("0.00"));
        billPayReqDTO.setActuallyPayFee(new BigDecimal("0.00"));
        billPayReqDTO.setDiscountFee(new BigDecimal("0.00"));
        billPayReqDTO.setChangeFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        discountFeeDetailDTO.setRule("rule");
        billPayReqDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        final BillPayReqDTO.Payment payment = new BillPayReqDTO.Payment();
        payment.setAmount(new BigDecimal("0.00"));
        payment.setPaymentType(0);
        payment.setPaymentTypeName("会员卡支付");
        billPayReqDTO.setPayments(Arrays.asList(payment));
        billPayReqDTO.setFastFood(false);
        billPayReqDTO.setMemberPassWord("memberPassWord");
        billPayReqDTO.setMemberInfoCardGuid("memberInfoCardGuid");
        billPayReqDTO.setUseIntegral(0);
        billPayReqDTO.setIntegralDiscountMoney(new BigDecimal("0.00"));
        billPayReqDTO.setVersion(0);
        billPayReqDTO.setOpenId("openId");
        when(mockWxStoreDineInBillClientService.pay(billPayReqDTO)).thenReturn(estimateItemRespDTO);

        when(mockRedisUtils.get("key")).thenReturn("result");

        // Run the test
        final WxStorePayResultDTO result = wxStorePayServiceImplUnderTest.zeroPay(wxZeroPayReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockWxStoreSessionDetailsService).saveOrderState("diningTableGuid", 0);
        verify(mockWxStoreMerchantOrderService).updateOrderState("diningTableGuid", 4);
        verify(mockWxStoreSessionDetailsService).saveTablePaidUser(WxStoreConsumerDTO.builder()
                .openId("openId")
                .nickName("createName")
                .headImgUrl("headImgUrl")
                .enterpriseGuid("enterpriseGuid")
                .enterpriseName("enterpriseName")
                .storeGuid("storeGuid")
                .storeName("storeName")
                .diningTableGuid("diningTableGuid")
                .brandGuid("brandGuid")
                .build());

        // Confirm WxStoreMerchantOrderService.pushFastMsg(...).
        final WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO.setGuid("0b550432-c989-4b0f-b462-d30b2855c69f");
        wxStoreMerchantOrderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setTotalPrice(new BigDecimal("0.00"));
        wxStoreMerchantOrderDTO.setOpenId("openId");
        verify(mockWxStoreMerchantOrderService).pushFastMsg(wxStoreMerchantOrderDTO);

        // Confirm WxOrderRecordService.update(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        dineinOrderDetailRespDTO.setStoreGuid("storeGuid");
        dineinOrderDetailRespDTO.setStoreName("storeName");
        dineinOrderDetailRespDTO.setOriginalOrderGuid(0L);
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setDeviceType(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setAppendFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setChangeFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO1.setDiscountType(0);
        discountFeeDetailDTO1.setDiscountFee(new BigDecimal("0.00"));
        discountFeeDetailDTO1.setRule("rule");
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        dineinOrderDetailRespDTO.setOrderNo("body");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setStateName("已支付");
        dineinOrderDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setMemberGuid("memberGuid");
        dineinOrderDetailRespDTO.setMemberPhone("memberPhone");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        final ResponseIntegralOffset integralOffsetResultRespDTO = new ResponseIntegralOffset();
        integralOffsetResultRespDTO.setUseIntegral(0);
        integralOffsetResultRespDTO.setDeductionMoney(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setIntegralOffsetResultRespDTO(integralOffsetResultRespDTO);
        dineinOrderDetailRespDTO.setTip("errorMsg");
        dineinOrderDetailRespDTO.setVersion(0);
        final WxStoreAccountDTO wxStoreAccountDTO = new WxStoreAccountDTO(dineinOrderDetailRespDTO,
                WxStoreAdvanceConsumerReqDTO.builder()
                        .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                                .openId("openId")
                                .nickName("createName")
                                .headImgUrl("headImgUrl")
                                .enterpriseGuid("enterpriseGuid")
                                .enterpriseName("enterpriseName")
                                .storeGuid("storeGuid")
                                .storeName("storeName")
                                .diningTableGuid("diningTableGuid")
                                .brandGuid("brandGuid")
                                .build())
                        .build());
        verify(mockWxOrderRecordService).update(wxStoreAccountDTO);
        verify(mockWxStoreSessionDetailsService).delPrepay("storeGuid", "openId");
    }

    @Test
    public void testWeChatPay() {
        // Setup
        final WeChatH5PayReqDTO weChatH5PayReqDTO = new WeChatH5PayReqDTO("orderGuid", "outNotifyUrl", 0,
                new BigDecimal("0.00"), false, "appId", "clientIp", false, "", "");
        final WxPayRespDTO expectedResult = WxPayRespDTO.builder()
                .payUrl("payUrl")
                .couldPay(0)
                .errorMsg("errorMsg")
                .result(AggPayRespDTO.builder().build())
                .build();

        // Configure WxOrderRecordService.getById(...).
        final WxOrderRecordDO wxOrderRecordDO = new WxOrderRecordDO(0L, "2fd7df1e-4c6a-4209-95f7-d828f00d5ff0",
                "6e64ec83-01fe-4f5b-8759-73c1b66062ed", "merchantGuid", "guid", "brandGuid", "brandName", "logUrl",
                "storeGuid", "storeName", "areaGuid", "areaName", "tableGuid", "tableCode", 0, 0, "orderStateName",
                new BigDecimal("0.00"), new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName",
                "orderHolderNo", LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0);
        when(mockWxOrderRecordService.getById("orderGuid")).thenReturn(wxOrderRecordDO);

        // Configure UserMemberSessionUtils.getUserMemberSession(...).
        final UserMemberSessionDTO userMemberSessionDTO = new UserMemberSessionDTO("storeGuid", "openId", "guid", false,
                "nickName", "memberInfoGuid", "cardGuid", "memberInfoCardGuid", "systemManagementGuid", 0, false,
                "volumeCode", false, new BigDecimal("0.00"), new BigDecimal("0.00"), "operSubjectGuid");
        when(mockUserMemberSessionUtils.getUserMemberSession("openId")).thenReturn(userMemberSessionDTO);

        // Configure WxStoreDineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        dineinOrderDetailRespDTO.setStoreGuid("storeGuid");
        dineinOrderDetailRespDTO.setStoreName("storeName");
        dineinOrderDetailRespDTO.setOriginalOrderGuid(0L);
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setDeviceType(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setAppendFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setChangeFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        discountFeeDetailDTO.setRule("rule");
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setOrderNo("body");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setStateName("已支付");
        dineinOrderDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setMemberGuid("memberGuid");
        dineinOrderDetailRespDTO.setMemberPhone("memberPhone");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        final ResponseIntegralOffset integralOffsetResultRespDTO = new ResponseIntegralOffset();
        integralOffsetResultRespDTO.setUseIntegral(0);
        integralOffsetResultRespDTO.setDeductionMoney(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setIntegralOffsetResultRespDTO(integralOffsetResultRespDTO);
        dineinOrderDetailRespDTO.setTip("errorMsg");
        dineinOrderDetailRespDTO.setVersion(0);
        when(mockWxStoreDineInOrderClientService.getOrderDetail("6e64ec83-01fe-4f5b-8759-73c1b66062ed"))
                .thenReturn(dineinOrderDetailRespDTO);

        when(mockRedisUtils.get("key")).thenReturn("result");

        // Configure WxStoreEstimateClientService.queryEstimateForSyn(...).
        final List<ItemEstimateForAndroidRespDTO> itemEstimateForAndroidRespDTOS = Arrays.asList(
                ItemEstimateForAndroidRespDTO.builder()
                        .skuGuid("skuGuid")
                        .isSoldOut(0)
                        .residueQuantity(new BigDecimal("0.00"))
                        .build());
        final BaseDTO baseDTO = new BaseDTO();
        baseDTO.setDeviceType(0);
        baseDTO.setDeviceId("openId");
        baseDTO.setEnterpriseGuid("enterpriseGuid");
        baseDTO.setStoreGuid("storeGuid");
        baseDTO.setStoreName("storeName");
        baseDTO.setUserGuid("openId");
        baseDTO.setUserName("createName");
        when(mockWxStoreEstimateClientService.queryEstimateForSyn(baseDTO)).thenReturn(itemEstimateForAndroidRespDTOS);

        // Configure WxStoreOrderConfigService.getDetailConfig(...).
        final WxOrderConfigDTO wxOrderConfigDTO = new WxOrderConfigDTO();
        wxOrderConfigDTO.setGuid("200534fd-70b3-4856-ac55-94e0a06310f4");
        wxOrderConfigDTO.setIsOrderOpen(0);
        wxOrderConfigDTO.setOrderModel(0);
        wxOrderConfigDTO.setTakingModel(0);
        wxOrderConfigDTO.setIsOnlinePayed(0);
        when(mockWxStoreOrderConfigService.getDetailConfig(WxStoreReqDTO.builder()
                .storeGuid("storeGuid")
                .build())).thenReturn(wxOrderConfigDTO);

        when(mockRedisUtils.generateGuid("hstTransactionRecord")).thenReturn("62fd3dbb-b68c-45a1-95a9-87508447c165");

        // Configure WxStoreSessionDetailsService.getEnterpriseDetail(...).
        final EnterpriseDTO enterpriseDTO = new EnterpriseDTO();
        enterpriseDTO.setId(0L);
        enterpriseDTO.setUid("uid");
        enterpriseDTO.setEnterpriseGuid("enterpriseGuid");
        enterpriseDTO.setName("enterpriseName");
        enterpriseDTO.setDbServer(0);
        when(mockWxStoreSessionDetailsService.getEnterpriseDetail()).thenReturn(enterpriseDTO);

        when(mockWxStorePayClientService.pay(new SaasAggPayDTO(AggPayPreTradingReqDTO.builder()
                .amount(new BigDecimal("0.00"))
                .orderGUID("6e64ec83-01fe-4f5b-8759-73c1b66062ed")
                .payGUID("62fd3dbb-b68c-45a1-95a9-87508447c165")
                .goodsName("body")
                .body("body")
                .payPowerId("id")
                .terminalId("62fd3dbb-b68c-45a1-95a9-87508447c165")
                .subAppId("appId")
                .subOpenId("openId")
                .enterpriseName("enterpriseName")
                .storeName("storeName")
                .clientIp("clientIp")
                .build(), false, false, false, "saasCallBackUrl", "callBackUrl"))).thenReturn(AggPayRespDTO.builder().build());

        // Configure BusinessClientService.queryOnDutyStaffs(...).
        final List<HandoverRecordDTO> handoverRecordDTOS = Arrays.asList(
                new HandoverRecordDTO("storeGuid", "storeName", "terminalId", "handoverRecordGuid", "openId",
                        "createName", new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"),
                        "confirmUserGuid", "confirmUserName", 0, 0, 0, 0, new BigDecimal("0.00"),
                        new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0,
                        new BigDecimal("0.00"), Arrays.asList(
                        new HandoverPayDetailDTO("handoverRecordGuid", "terminalId", "payType", "payTypeName",
                                new BigDecimal("0.00"), LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                                LocalDateTime.of(2020, 1, 1, 0, 0, 0))), new BigDecimal("0.00"),
                        new BigDecimal("0.00")));
        when(mockBusinessClientService.queryOnDutyStaffs("storeGuid")).thenReturn(handoverRecordDTOS);

        // Configure OrganizationClientService.findMasterDevice(...).
        final StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO("enterpriseGuid", "storeNo", "storeGuid", "storeName",
                "deviceNo", "deviceGuid", false, false, 0, "deviceName", 0, "createUserGuid", "createUserName",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "tableGuid", 0);
        when(mockOrganizationClientService.findMasterDevice("storeGuid")).thenReturn(storeDeviceDTO);

        // Run the test
        final WxPayRespDTO result = wxStorePayServiceImplUnderTest.weChatPay(weChatH5PayReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);

        // Confirm WxStoreSessionDetailsService.savePayCallBack(...).
        final WeChatPayReqDTO weChatPayReqDTO = new WeChatPayReqDTO();
        weChatPayReqDTO.setDeviceType(0);
        weChatPayReqDTO.setDeviceId("openId");
        weChatPayReqDTO.setEnterpriseGuid("enterpriseGuid");
        weChatPayReqDTO.setStoreGuid("storeGuid");
        weChatPayReqDTO.setStoreName("storeName");
        weChatPayReqDTO.setUserGuid("openId");
        weChatPayReqDTO.setUserName("createName");
        weChatPayReqDTO.setOrderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        weChatPayReqDTO.setPayGuid("62fd3dbb-b68c-45a1-95a9-87508447c165");
        weChatPayReqDTO.setPayPowerId("payPowerId");
        weChatPayReqDTO.setSuccess(false);
        weChatPayReqDTO.setAmount(new BigDecimal("0.00"));
        weChatPayReqDTO.setOrderFee(new BigDecimal("0.00"));
        weChatPayReqDTO.setActuallyPayFee(new BigDecimal("0.00"));
        weChatPayReqDTO.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO1.setDiscountType(0);
        discountFeeDetailDTO1.setDiscountFee(new BigDecimal("0.00"));
        discountFeeDetailDTO1.setRule("rule");
        weChatPayReqDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        weChatPayReqDTO.setMemberGuid("memberGuid");
        verify(mockWxStoreSessionDetailsService).savePayCallBack("6e64ec83-01fe-4f5b-8759-73c1b66062ed",
                weChatPayReqDTO);

        // Confirm WxStoreDineInOrderClientService.lockorder(...).
        final OrderLockDTO orderLockDto = new OrderLockDTO();
        orderLockDto.setDeviceType(0);
        orderLockDto.setDeviceId("openId");
        orderLockDto.setEnterpriseGuid("enterpriseGuid");
        orderLockDto.setStoreGuid("storeGuid");
        orderLockDto.setStoreName("storeName");
        orderLockDto.setUserGuid("openId");
        orderLockDto.setUserName("createName");
        orderLockDto.setOrderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        verify(mockWxStoreDineInOrderClientService).lockorder(orderLockDto);
        verify(mockRedisUtils).delete("key");
        verify(mockUserMemberSessionUtils).addUserMemberSession(
                new UserMemberSessionDTO("storeGuid", "openId", "guid", false, "nickName", "memberInfoGuid", "cardGuid",
                        "memberInfoCardGuid", "systemManagementGuid", 0, false, "volumeCode", false,
                        new BigDecimal("0.00"), new BigDecimal("0.00"), "operSubjectGuid"));
    }

    @Test
    public void testWeChatPay_WxOrderRecordServiceReturnsNull() {
        // Setup
        final WeChatH5PayReqDTO weChatH5PayReqDTO = new WeChatH5PayReqDTO("orderGuid", "outNotifyUrl", 0,
                new BigDecimal("0.00"), false, "appId", "clientIp", false, "","");
        final WxPayRespDTO expectedResult = WxPayRespDTO.builder()
                .payUrl("payUrl")
                .couldPay(0)
                .errorMsg("errorMsg")
                .result(AggPayRespDTO.builder().build())
                .build();
        when(mockWxOrderRecordService.getById("orderGuid")).thenReturn(null);

        // Run the test
        final WxPayRespDTO result = wxStorePayServiceImplUnderTest.weChatPay(weChatH5PayReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testWeChatPay_WxStoreEstimateClientServiceReturnsNoItems() {
        // Setup
        final WeChatH5PayReqDTO weChatH5PayReqDTO = new WeChatH5PayReqDTO("orderGuid", "outNotifyUrl", 0,
                new BigDecimal("0.00"), false, "appId", "clientIp", false, "", "");
        final WxPayRespDTO expectedResult = WxPayRespDTO.builder()
                .payUrl("payUrl")
                .couldPay(0)
                .errorMsg("errorMsg")
                .result(AggPayRespDTO.builder().build())
                .build();

        // Configure WxOrderRecordService.getById(...).
        final WxOrderRecordDO wxOrderRecordDO = new WxOrderRecordDO(0L, "2fd7df1e-4c6a-4209-95f7-d828f00d5ff0",
                "6e64ec83-01fe-4f5b-8759-73c1b66062ed", "merchantGuid", "guid", "brandGuid", "brandName", "logUrl",
                "storeGuid", "storeName", "areaGuid", "areaName", "tableGuid", "tableCode", 0, 0, "orderStateName",
                new BigDecimal("0.00"), new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName",
                "orderHolderNo", LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0);
        when(mockWxOrderRecordService.getById("orderGuid")).thenReturn(wxOrderRecordDO);

        // Configure UserMemberSessionUtils.getUserMemberSession(...).
        final UserMemberSessionDTO userMemberSessionDTO = new UserMemberSessionDTO("storeGuid", "openId", "guid", false,
                "nickName", "memberInfoGuid", "cardGuid", "memberInfoCardGuid", "systemManagementGuid", 0, false,
                "volumeCode", false, new BigDecimal("0.00"), new BigDecimal("0.00"), "operSubjectGuid");
        when(mockUserMemberSessionUtils.getUserMemberSession("openId")).thenReturn(userMemberSessionDTO);

        // Configure WxStoreDineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        dineinOrderDetailRespDTO.setStoreGuid("storeGuid");
        dineinOrderDetailRespDTO.setStoreName("storeName");
        dineinOrderDetailRespDTO.setOriginalOrderGuid(0L);
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setDeviceType(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setAppendFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setChangeFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        discountFeeDetailDTO.setRule("rule");
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setOrderNo("body");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setStateName("已支付");
        dineinOrderDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setMemberGuid("memberGuid");
        dineinOrderDetailRespDTO.setMemberPhone("memberPhone");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        final ResponseIntegralOffset integralOffsetResultRespDTO = new ResponseIntegralOffset();
        integralOffsetResultRespDTO.setUseIntegral(0);
        integralOffsetResultRespDTO.setDeductionMoney(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setIntegralOffsetResultRespDTO(integralOffsetResultRespDTO);
        dineinOrderDetailRespDTO.setTip("errorMsg");
        dineinOrderDetailRespDTO.setVersion(0);
        when(mockWxStoreDineInOrderClientService.getOrderDetail("6e64ec83-01fe-4f5b-8759-73c1b66062ed"))
                .thenReturn(dineinOrderDetailRespDTO);

        when(mockRedisUtils.get("key")).thenReturn("result");

        // Configure WxStoreEstimateClientService.queryEstimateForSyn(...).
        final BaseDTO baseDTO = new BaseDTO();
        baseDTO.setDeviceType(0);
        baseDTO.setDeviceId("openId");
        baseDTO.setEnterpriseGuid("enterpriseGuid");
        baseDTO.setStoreGuid("storeGuid");
        baseDTO.setStoreName("storeName");
        baseDTO.setUserGuid("openId");
        baseDTO.setUserName("createName");
        when(mockWxStoreEstimateClientService.queryEstimateForSyn(baseDTO)).thenReturn(Collections.emptyList());

        // Configure WxStoreOrderConfigService.getDetailConfig(...).
        final WxOrderConfigDTO wxOrderConfigDTO = new WxOrderConfigDTO();
        wxOrderConfigDTO.setGuid("200534fd-70b3-4856-ac55-94e0a06310f4");
        wxOrderConfigDTO.setIsOrderOpen(0);
        wxOrderConfigDTO.setOrderModel(0);
        wxOrderConfigDTO.setTakingModel(0);
        wxOrderConfigDTO.setIsOnlinePayed(0);
        when(mockWxStoreOrderConfigService.getDetailConfig(WxStoreReqDTO.builder()
                .storeGuid("storeGuid")
                .build())).thenReturn(wxOrderConfigDTO);

        when(mockRedisUtils.generateGuid("hstTransactionRecord")).thenReturn("62fd3dbb-b68c-45a1-95a9-87508447c165");

        // Configure WxStoreSessionDetailsService.getEnterpriseDetail(...).
        final EnterpriseDTO enterpriseDTO = new EnterpriseDTO();
        enterpriseDTO.setId(0L);
        enterpriseDTO.setUid("uid");
        enterpriseDTO.setEnterpriseGuid("enterpriseGuid");
        enterpriseDTO.setName("enterpriseName");
        enterpriseDTO.setDbServer(0);
        when(mockWxStoreSessionDetailsService.getEnterpriseDetail()).thenReturn(enterpriseDTO);

        when(mockWxStorePayClientService.pay(new SaasAggPayDTO(AggPayPreTradingReqDTO.builder()
                .amount(new BigDecimal("0.00"))
                .orderGUID("6e64ec83-01fe-4f5b-8759-73c1b66062ed")
                .payGUID("62fd3dbb-b68c-45a1-95a9-87508447c165")
                .goodsName("body")
                .body("body")
                .payPowerId("id")
                .terminalId("62fd3dbb-b68c-45a1-95a9-87508447c165")
                .subAppId("appId")
                .subOpenId("openId")
                .enterpriseName("enterpriseName")
                .storeName("storeName")
                .clientIp("clientIp")
                .build(), false, false, false, "saasCallBackUrl", "callBackUrl"))).thenReturn(AggPayRespDTO.builder().build());

        // Configure BusinessClientService.queryOnDutyStaffs(...).
        final List<HandoverRecordDTO> handoverRecordDTOS = Arrays.asList(
                new HandoverRecordDTO("storeGuid", "storeName", "terminalId", "handoverRecordGuid", "openId",
                        "createName", new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"),
                        "confirmUserGuid", "confirmUserName", 0, 0, 0, 0, new BigDecimal("0.00"),
                        new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0,
                        new BigDecimal("0.00"), Arrays.asList(
                        new HandoverPayDetailDTO("handoverRecordGuid", "terminalId", "payType", "payTypeName",
                                new BigDecimal("0.00"), LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                                LocalDateTime.of(2020, 1, 1, 0, 0, 0))), new BigDecimal("0.00"),
                        new BigDecimal("0.00")));
        when(mockBusinessClientService.queryOnDutyStaffs("storeGuid")).thenReturn(handoverRecordDTOS);

        // Configure OrganizationClientService.findMasterDevice(...).
        final StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO("enterpriseGuid", "storeNo", "storeGuid", "storeName",
                "deviceNo", "deviceGuid", false, false, 0, "deviceName", 0, "createUserGuid", "createUserName",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "tableGuid", 0);
        when(mockOrganizationClientService.findMasterDevice("storeGuid")).thenReturn(storeDeviceDTO);

        // Run the test
        final WxPayRespDTO result = wxStorePayServiceImplUnderTest.weChatPay(weChatH5PayReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);

        // Confirm WxStoreSessionDetailsService.savePayCallBack(...).
        final WeChatPayReqDTO weChatPayReqDTO = new WeChatPayReqDTO();
        weChatPayReqDTO.setDeviceType(0);
        weChatPayReqDTO.setDeviceId("openId");
        weChatPayReqDTO.setEnterpriseGuid("enterpriseGuid");
        weChatPayReqDTO.setStoreGuid("storeGuid");
        weChatPayReqDTO.setStoreName("storeName");
        weChatPayReqDTO.setUserGuid("openId");
        weChatPayReqDTO.setUserName("createName");
        weChatPayReqDTO.setOrderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        weChatPayReqDTO.setPayGuid("62fd3dbb-b68c-45a1-95a9-87508447c165");
        weChatPayReqDTO.setPayPowerId("payPowerId");
        weChatPayReqDTO.setSuccess(false);
        weChatPayReqDTO.setAmount(new BigDecimal("0.00"));
        weChatPayReqDTO.setOrderFee(new BigDecimal("0.00"));
        weChatPayReqDTO.setActuallyPayFee(new BigDecimal("0.00"));
        weChatPayReqDTO.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO1.setDiscountType(0);
        discountFeeDetailDTO1.setDiscountFee(new BigDecimal("0.00"));
        discountFeeDetailDTO1.setRule("rule");
        weChatPayReqDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        weChatPayReqDTO.setMemberGuid("memberGuid");
        verify(mockWxStoreSessionDetailsService).savePayCallBack("6e64ec83-01fe-4f5b-8759-73c1b66062ed",
                weChatPayReqDTO);

        // Confirm WxStoreDineInOrderClientService.lockorder(...).
        final OrderLockDTO orderLockDto = new OrderLockDTO();
        orderLockDto.setDeviceType(0);
        orderLockDto.setDeviceId("openId");
        orderLockDto.setEnterpriseGuid("enterpriseGuid");
        orderLockDto.setStoreGuid("storeGuid");
        orderLockDto.setStoreName("storeName");
        orderLockDto.setUserGuid("openId");
        orderLockDto.setUserName("createName");
        orderLockDto.setOrderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        verify(mockWxStoreDineInOrderClientService).lockorder(orderLockDto);
        verify(mockRedisUtils).delete("key");
        verify(mockUserMemberSessionUtils).addUserMemberSession(
                new UserMemberSessionDTO("storeGuid", "openId", "guid", false, "nickName", "memberInfoGuid", "cardGuid",
                        "memberInfoCardGuid", "systemManagementGuid", 0, false, "volumeCode", false,
                        new BigDecimal("0.00"), new BigDecimal("0.00"), "operSubjectGuid"));
    }

    @Test
    public void testWeChatPay_WxStorePayClientServiceReturnsError() {
        // Setup
        final WeChatH5PayReqDTO weChatH5PayReqDTO = new WeChatH5PayReqDTO("orderGuid", "outNotifyUrl", 0,
                new BigDecimal("0.00"), false, "appId", "clientIp", false, "", "");
        final WxPayRespDTO expectedResult = WxPayRespDTO.builder()
                .payUrl("payUrl")
                .couldPay(0)
                .errorMsg("errorMsg")
                .result(AggPayRespDTO.builder().build())
                .build();

        // Configure WxOrderRecordService.getById(...).
        final WxOrderRecordDO wxOrderRecordDO = new WxOrderRecordDO(0L, "2fd7df1e-4c6a-4209-95f7-d828f00d5ff0",
                "6e64ec83-01fe-4f5b-8759-73c1b66062ed", "merchantGuid", "guid", "brandGuid", "brandName", "logUrl",
                "storeGuid", "storeName", "areaGuid", "areaName", "tableGuid", "tableCode", 0, 0, "orderStateName",
                new BigDecimal("0.00"), new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName",
                "orderHolderNo", LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0);
        when(mockWxOrderRecordService.getById("orderGuid")).thenReturn(wxOrderRecordDO);

        // Configure UserMemberSessionUtils.getUserMemberSession(...).
        final UserMemberSessionDTO userMemberSessionDTO = new UserMemberSessionDTO("storeGuid", "openId", "guid", false,
                "nickName", "memberInfoGuid", "cardGuid", "memberInfoCardGuid", "systemManagementGuid", 0, false,
                "volumeCode", false, new BigDecimal("0.00"), new BigDecimal("0.00"), "operSubjectGuid");
        when(mockUserMemberSessionUtils.getUserMemberSession("openId")).thenReturn(userMemberSessionDTO);

        // Configure WxStoreDineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        dineinOrderDetailRespDTO.setStoreGuid("storeGuid");
        dineinOrderDetailRespDTO.setStoreName("storeName");
        dineinOrderDetailRespDTO.setOriginalOrderGuid(0L);
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setDeviceType(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setAppendFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setChangeFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        discountFeeDetailDTO.setRule("rule");
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setOrderNo("body");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setStateName("已支付");
        dineinOrderDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setMemberGuid("memberGuid");
        dineinOrderDetailRespDTO.setMemberPhone("memberPhone");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        final ResponseIntegralOffset integralOffsetResultRespDTO = new ResponseIntegralOffset();
        integralOffsetResultRespDTO.setUseIntegral(0);
        integralOffsetResultRespDTO.setDeductionMoney(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setIntegralOffsetResultRespDTO(integralOffsetResultRespDTO);
        dineinOrderDetailRespDTO.setTip("errorMsg");
        dineinOrderDetailRespDTO.setVersion(0);
        when(mockWxStoreDineInOrderClientService.getOrderDetail("6e64ec83-01fe-4f5b-8759-73c1b66062ed"))
                .thenReturn(dineinOrderDetailRespDTO);

        when(mockRedisUtils.get("key")).thenReturn("result");

        // Configure WxStoreEstimateClientService.queryEstimateForSyn(...).
        final List<ItemEstimateForAndroidRespDTO> itemEstimateForAndroidRespDTOS = Arrays.asList(
                ItemEstimateForAndroidRespDTO.builder()
                        .skuGuid("skuGuid")
                        .isSoldOut(0)
                        .residueQuantity(new BigDecimal("0.00"))
                        .build());
        final BaseDTO baseDTO = new BaseDTO();
        baseDTO.setDeviceType(0);
        baseDTO.setDeviceId("openId");
        baseDTO.setEnterpriseGuid("enterpriseGuid");
        baseDTO.setStoreGuid("storeGuid");
        baseDTO.setStoreName("storeName");
        baseDTO.setUserGuid("openId");
        baseDTO.setUserName("createName");
        when(mockWxStoreEstimateClientService.queryEstimateForSyn(baseDTO)).thenReturn(itemEstimateForAndroidRespDTOS);

        // Configure WxStoreOrderConfigService.getDetailConfig(...).
        final WxOrderConfigDTO wxOrderConfigDTO = new WxOrderConfigDTO();
        wxOrderConfigDTO.setGuid("200534fd-70b3-4856-ac55-94e0a06310f4");
        wxOrderConfigDTO.setIsOrderOpen(0);
        wxOrderConfigDTO.setOrderModel(0);
        wxOrderConfigDTO.setTakingModel(0);
        wxOrderConfigDTO.setIsOnlinePayed(0);
        when(mockWxStoreOrderConfigService.getDetailConfig(WxStoreReqDTO.builder()
                .storeGuid("storeGuid")
                .build())).thenReturn(wxOrderConfigDTO);

        when(mockRedisUtils.generateGuid("hstTransactionRecord")).thenReturn("62fd3dbb-b68c-45a1-95a9-87508447c165");

        // Configure WxStoreSessionDetailsService.getEnterpriseDetail(...).
        final EnterpriseDTO enterpriseDTO = new EnterpriseDTO();
        enterpriseDTO.setId(0L);
        enterpriseDTO.setUid("uid");
        enterpriseDTO.setEnterpriseGuid("enterpriseGuid");
        enterpriseDTO.setName("enterpriseName");
        enterpriseDTO.setDbServer(0);
        when(mockWxStoreSessionDetailsService.getEnterpriseDetail()).thenReturn(enterpriseDTO);

        when(mockWxStorePayClientService.pay(new SaasAggPayDTO(AggPayPreTradingReqDTO.builder()
                .amount(new BigDecimal("0.00"))
                .orderGUID("6e64ec83-01fe-4f5b-8759-73c1b66062ed")
                .payGUID("62fd3dbb-b68c-45a1-95a9-87508447c165")
                .goodsName("body")
                .body("body")
                .payPowerId("id")
                .terminalId("62fd3dbb-b68c-45a1-95a9-87508447c165")
                .subAppId("appId")
                .subOpenId("openId")
                .enterpriseName("enterpriseName")
                .storeName("storeName")
                .clientIp("clientIp")
                .build(), false, false, false, "saasCallBackUrl", "callBackUrl")))
                .thenReturn(AggPayRespDTO.errorPaymentResp("code", "msg"));

        // Configure BusinessClientService.queryOnDutyStaffs(...).
        final List<HandoverRecordDTO> handoverRecordDTOS = Arrays.asList(
                new HandoverRecordDTO("storeGuid", "storeName", "terminalId", "handoverRecordGuid", "openId",
                        "createName", new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"),
                        "confirmUserGuid", "confirmUserName", 0, 0, 0, 0, new BigDecimal("0.00"),
                        new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0,
                        new BigDecimal("0.00"), Arrays.asList(
                        new HandoverPayDetailDTO("handoverRecordGuid", "terminalId", "payType", "payTypeName",
                                new BigDecimal("0.00"), LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                                LocalDateTime.of(2020, 1, 1, 0, 0, 0))), new BigDecimal("0.00"),
                        new BigDecimal("0.00")));
        when(mockBusinessClientService.queryOnDutyStaffs("storeGuid")).thenReturn(handoverRecordDTOS);

        // Configure OrganizationClientService.findMasterDevice(...).
        final StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO("enterpriseGuid", "storeNo", "storeGuid", "storeName",
                "deviceNo", "deviceGuid", false, false, 0, "deviceName", 0, "createUserGuid", "createUserName",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "tableGuid", 0);
        when(mockOrganizationClientService.findMasterDevice("storeGuid")).thenReturn(storeDeviceDTO);

        // Run the test
        final WxPayRespDTO result = wxStorePayServiceImplUnderTest.weChatPay(weChatH5PayReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);

        // Confirm WxStoreSessionDetailsService.savePayCallBack(...).
        final WeChatPayReqDTO weChatPayReqDTO = new WeChatPayReqDTO();
        weChatPayReqDTO.setDeviceType(0);
        weChatPayReqDTO.setDeviceId("openId");
        weChatPayReqDTO.setEnterpriseGuid("enterpriseGuid");
        weChatPayReqDTO.setStoreGuid("storeGuid");
        weChatPayReqDTO.setStoreName("storeName");
        weChatPayReqDTO.setUserGuid("openId");
        weChatPayReqDTO.setUserName("createName");
        weChatPayReqDTO.setOrderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        weChatPayReqDTO.setPayGuid("62fd3dbb-b68c-45a1-95a9-87508447c165");
        weChatPayReqDTO.setPayPowerId("payPowerId");
        weChatPayReqDTO.setSuccess(false);
        weChatPayReqDTO.setAmount(new BigDecimal("0.00"));
        weChatPayReqDTO.setOrderFee(new BigDecimal("0.00"));
        weChatPayReqDTO.setActuallyPayFee(new BigDecimal("0.00"));
        weChatPayReqDTO.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO1.setDiscountType(0);
        discountFeeDetailDTO1.setDiscountFee(new BigDecimal("0.00"));
        discountFeeDetailDTO1.setRule("rule");
        weChatPayReqDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        weChatPayReqDTO.setMemberGuid("memberGuid");
        verify(mockWxStoreSessionDetailsService).savePayCallBack("6e64ec83-01fe-4f5b-8759-73c1b66062ed",
                weChatPayReqDTO);

        // Confirm WxStoreDineInOrderClientService.lockorder(...).
        final OrderLockDTO orderLockDto = new OrderLockDTO();
        orderLockDto.setDeviceType(0);
        orderLockDto.setDeviceId("openId");
        orderLockDto.setEnterpriseGuid("enterpriseGuid");
        orderLockDto.setStoreGuid("storeGuid");
        orderLockDto.setStoreName("storeName");
        orderLockDto.setUserGuid("openId");
        orderLockDto.setUserName("createName");
        orderLockDto.setOrderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        verify(mockWxStoreDineInOrderClientService).lockorder(orderLockDto);
        verify(mockRedisUtils).delete("key");
        verify(mockUserMemberSessionUtils).addUserMemberSession(
                new UserMemberSessionDTO("storeGuid", "openId", "guid", false, "nickName", "memberInfoGuid", "cardGuid",
                        "memberInfoCardGuid", "systemManagementGuid", 0, false, "volumeCode", false,
                        new BigDecimal("0.00"), new BigDecimal("0.00"), "operSubjectGuid"));
    }

    @Test
    public void testWeChatPay_BusinessClientServiceReturnsNoItems() {
        // Setup
        final WeChatH5PayReqDTO weChatH5PayReqDTO = new WeChatH5PayReqDTO("orderGuid", "outNotifyUrl", 0,
                new BigDecimal("0.00"), false, "appId", "clientIp", false,"","");
        final WxPayRespDTO expectedResult = WxPayRespDTO.builder()
                .payUrl("payUrl")
                .couldPay(0)
                .errorMsg("errorMsg")
                .result(AggPayRespDTO.builder().build())
                .build();

        // Configure WxOrderRecordService.getById(...).
        final WxOrderRecordDO wxOrderRecordDO = new WxOrderRecordDO(0L, "2fd7df1e-4c6a-4209-95f7-d828f00d5ff0",
                "6e64ec83-01fe-4f5b-8759-73c1b66062ed", "merchantGuid", "guid", "brandGuid", "brandName", "logUrl",
                "storeGuid", "storeName", "areaGuid", "areaName", "tableGuid", "tableCode", 0, 0, "orderStateName",
                new BigDecimal("0.00"), new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName",
                "orderHolderNo", LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0);
        when(mockWxOrderRecordService.getById("orderGuid")).thenReturn(wxOrderRecordDO);

        // Configure UserMemberSessionUtils.getUserMemberSession(...).
        final UserMemberSessionDTO userMemberSessionDTO = new UserMemberSessionDTO("storeGuid", "openId", "guid", false,
                "nickName", "memberInfoGuid", "cardGuid", "memberInfoCardGuid", "systemManagementGuid", 0, false,
                "volumeCode", false, new BigDecimal("0.00"), new BigDecimal("0.00"), "operSubjectGuid");
        when(mockUserMemberSessionUtils.getUserMemberSession("openId")).thenReturn(userMemberSessionDTO);

        // Configure WxStoreDineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        dineinOrderDetailRespDTO.setStoreGuid("storeGuid");
        dineinOrderDetailRespDTO.setStoreName("storeName");
        dineinOrderDetailRespDTO.setOriginalOrderGuid(0L);
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setDeviceType(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setAppendFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setChangeFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        discountFeeDetailDTO.setRule("rule");
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setOrderNo("body");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setStateName("已支付");
        dineinOrderDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setMemberGuid("memberGuid");
        dineinOrderDetailRespDTO.setMemberPhone("memberPhone");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        final ResponseIntegralOffset integralOffsetResultRespDTO = new ResponseIntegralOffset();
        integralOffsetResultRespDTO.setUseIntegral(0);
        integralOffsetResultRespDTO.setDeductionMoney(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setIntegralOffsetResultRespDTO(integralOffsetResultRespDTO);
        dineinOrderDetailRespDTO.setTip("errorMsg");
        dineinOrderDetailRespDTO.setVersion(0);
        when(mockWxStoreDineInOrderClientService.getOrderDetail("6e64ec83-01fe-4f5b-8759-73c1b66062ed"))
                .thenReturn(dineinOrderDetailRespDTO);

        when(mockRedisUtils.get("key")).thenReturn("result");

        // Configure WxStoreEstimateClientService.queryEstimateForSyn(...).
        final List<ItemEstimateForAndroidRespDTO> itemEstimateForAndroidRespDTOS = Arrays.asList(
                ItemEstimateForAndroidRespDTO.builder()
                        .skuGuid("skuGuid")
                        .isSoldOut(0)
                        .residueQuantity(new BigDecimal("0.00"))
                        .build());
        final BaseDTO baseDTO = new BaseDTO();
        baseDTO.setDeviceType(0);
        baseDTO.setDeviceId("openId");
        baseDTO.setEnterpriseGuid("enterpriseGuid");
        baseDTO.setStoreGuid("storeGuid");
        baseDTO.setStoreName("storeName");
        baseDTO.setUserGuid("openId");
        baseDTO.setUserName("createName");
        when(mockWxStoreEstimateClientService.queryEstimateForSyn(baseDTO)).thenReturn(itemEstimateForAndroidRespDTOS);

        // Configure WxStoreOrderConfigService.getDetailConfig(...).
        final WxOrderConfigDTO wxOrderConfigDTO = new WxOrderConfigDTO();
        wxOrderConfigDTO.setGuid("200534fd-70b3-4856-ac55-94e0a06310f4");
        wxOrderConfigDTO.setIsOrderOpen(0);
        wxOrderConfigDTO.setOrderModel(0);
        wxOrderConfigDTO.setTakingModel(0);
        wxOrderConfigDTO.setIsOnlinePayed(0);
        when(mockWxStoreOrderConfigService.getDetailConfig(WxStoreReqDTO.builder()
                .storeGuid("storeGuid")
                .build())).thenReturn(wxOrderConfigDTO);

        when(mockRedisUtils.generateGuid("hstTransactionRecord")).thenReturn("62fd3dbb-b68c-45a1-95a9-87508447c165");

        // Configure WxStoreSessionDetailsService.getEnterpriseDetail(...).
        final EnterpriseDTO enterpriseDTO = new EnterpriseDTO();
        enterpriseDTO.setId(0L);
        enterpriseDTO.setUid("uid");
        enterpriseDTO.setEnterpriseGuid("enterpriseGuid");
        enterpriseDTO.setName("enterpriseName");
        enterpriseDTO.setDbServer(0);
        when(mockWxStoreSessionDetailsService.getEnterpriseDetail()).thenReturn(enterpriseDTO);

        when(mockWxStorePayClientService.pay(new SaasAggPayDTO(AggPayPreTradingReqDTO.builder()
                .amount(new BigDecimal("0.00"))
                .orderGUID("6e64ec83-01fe-4f5b-8759-73c1b66062ed")
                .payGUID("62fd3dbb-b68c-45a1-95a9-87508447c165")
                .goodsName("body")
                .body("body")
                .payPowerId("id")
                .terminalId("62fd3dbb-b68c-45a1-95a9-87508447c165")
                .subAppId("appId")
                .subOpenId("openId")
                .enterpriseName("enterpriseName")
                .storeName("storeName")
                .clientIp("clientIp")
                .build(), false, false, false, "saasCallBackUrl", "callBackUrl"))).thenReturn(AggPayRespDTO.builder().build());
        when(mockBusinessClientService.queryOnDutyStaffs("storeGuid")).thenReturn(Collections.emptyList());

        // Run the test
        final WxPayRespDTO result = wxStorePayServiceImplUnderTest.weChatPay(weChatH5PayReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);

        // Confirm WxStoreSessionDetailsService.savePayCallBack(...).
        final WeChatPayReqDTO weChatPayReqDTO = new WeChatPayReqDTO();
        weChatPayReqDTO.setDeviceType(0);
        weChatPayReqDTO.setDeviceId("openId");
        weChatPayReqDTO.setEnterpriseGuid("enterpriseGuid");
        weChatPayReqDTO.setStoreGuid("storeGuid");
        weChatPayReqDTO.setStoreName("storeName");
        weChatPayReqDTO.setUserGuid("openId");
        weChatPayReqDTO.setUserName("createName");
        weChatPayReqDTO.setOrderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        weChatPayReqDTO.setPayGuid("62fd3dbb-b68c-45a1-95a9-87508447c165");
        weChatPayReqDTO.setPayPowerId("payPowerId");
        weChatPayReqDTO.setSuccess(false);
        weChatPayReqDTO.setAmount(new BigDecimal("0.00"));
        weChatPayReqDTO.setOrderFee(new BigDecimal("0.00"));
        weChatPayReqDTO.setActuallyPayFee(new BigDecimal("0.00"));
        weChatPayReqDTO.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO1.setDiscountType(0);
        discountFeeDetailDTO1.setDiscountFee(new BigDecimal("0.00"));
        discountFeeDetailDTO1.setRule("rule");
        weChatPayReqDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        weChatPayReqDTO.setMemberGuid("memberGuid");
        verify(mockWxStoreSessionDetailsService).savePayCallBack("6e64ec83-01fe-4f5b-8759-73c1b66062ed",
                weChatPayReqDTO);

        // Confirm WxStoreDineInOrderClientService.lockorder(...).
        final OrderLockDTO orderLockDto = new OrderLockDTO();
        orderLockDto.setDeviceType(0);
        orderLockDto.setDeviceId("openId");
        orderLockDto.setEnterpriseGuid("enterpriseGuid");
        orderLockDto.setStoreGuid("storeGuid");
        orderLockDto.setStoreName("storeName");
        orderLockDto.setUserGuid("openId");
        orderLockDto.setUserName("createName");
        orderLockDto.setOrderGuid("6e64ec83-01fe-4f5b-8759-73c1b66062ed");
        verify(mockWxStoreDineInOrderClientService).lockorder(orderLockDto);
        verify(mockRedisUtils).delete("key");
        verify(mockUserMemberSessionUtils).addUserMemberSession(
                new UserMemberSessionDTO("storeGuid", "openId", "guid", false, "nickName", "memberInfoGuid", "cardGuid",
                        "memberInfoCardGuid", "systemManagementGuid", 0, false, "volumeCode", false,
                        new BigDecimal("0.00"), new BigDecimal("0.00"), "operSubjectGuid"));
    }
}
