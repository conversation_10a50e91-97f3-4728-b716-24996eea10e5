package com.holderzone.saas.store.weixin.service.impl;

import com.google.common.collect.Lists;
import com.holderzone.holder.saas.weixin.common.BusinessName;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.item.resp.*;
import com.holderzone.saas.store.dto.weixin.*;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreAttrGroupRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreAttrRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreItemRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreSkuRespDTO;
import com.holderzone.saas.store.weixin.service.WxSocketMsgService;
import com.holderzone.saas.store.weixin.service.WxStoreMenuDetailsService;
import com.holderzone.saas.store.weixin.service.WxStoreSessionDetailsService;
import com.holderzone.saas.store.weixin.service.rpc.WxStoreEstimateClientService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static org.junit.Assert.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class WxStoreAdvanceOrderServiceImplTest {

    @Mock
    private RedisUtils mockRedisUtils;
    @Mock
    private WxStoreMenuDetailsService mockWxStoreMenuDetailsService;
    @Mock
    private WxStoreSessionDetailsService mockWxStoreSessionDetailsService;
    @Mock
    private WxSocketMsgService mockWxSocketMsgService;
    @Mock
    private WxStoreEstimateClientService mockWxStoreEstimateClientService;

    private WxStoreAdvanceOrderServiceImpl wxStoreAdvanceOrderServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        wxStoreAdvanceOrderServiceImplUnderTest = new WxStoreAdvanceOrderServiceImpl(mockRedisUtils,
                mockWxStoreMenuDetailsService, mockWxStoreSessionDetailsService, mockWxSocketMsgService);
        ReflectionTestUtils.setField(wxStoreAdvanceOrderServiceImplUnderTest, "wxStoreEstimateClientService",
                mockWxStoreEstimateClientService);
    }

    @Test
    public void testCreateAdvanceOrder() {
        // Setup
        final WxStoreAdvanceOrderDTO wxStoreAdvanceOrderDTO = new WxStoreAdvanceOrderDTO();
        wxStoreAdvanceOrderDTO.setWxStoreAdvanceConsumerReqDTO(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build());
        final WxStoreItemRespDTO wxStoreItemRespDTO = new WxStoreItemRespDTO();
        wxStoreItemRespDTO.setItemGuid("itemGuid");
        wxStoreItemRespDTO.setItemType(0);
        wxStoreItemRespDTO.setName("itemName");
        wxStoreItemRespDTO.setIsFixPkg(0);
        final WxStoreSkuRespDTO wxStoreSkuRespDTO = new WxStoreSkuRespDTO();
        wxStoreSkuRespDTO.setSkuGuid("skuGuid");
        wxStoreSkuRespDTO.setName("name");
        wxStoreSkuRespDTO.setSalePrice(new BigDecimal("0.00"));
        wxStoreSkuRespDTO.setUserck(0);
        wxStoreItemRespDTO.setSkuList(Arrays.asList(wxStoreSkuRespDTO));
        final SubgroupSynRespDTO subgroupSynRespDTO = new SubgroupSynRespDTO();
        subgroupSynRespDTO.setPickNum(0);
        final SubItemSkuSynRespDTO subItemSkuSynRespDTO = new SubItemSkuSynRespDTO();
        subItemSkuSynRespDTO.setSkuGuid("skuGuid");
        subItemSkuSynRespDTO.setSkuName("name");
        subItemSkuSynRespDTO.setItemType(0);
        subItemSkuSynRespDTO.setItemNum(new BigDecimal("0.00"));
        subItemSkuSynRespDTO.setAddPrice(new BigDecimal("0.00"));
        subItemSkuSynRespDTO.setDefaultNum(0);
        final AttrGroupSynRespDTO attrGroupSynRespDTO = new AttrGroupSynRespDTO();
        attrGroupSynRespDTO.setIsRequired(0);
        attrGroupSynRespDTO.setWithDefault(0);
        final AttrSynRespDTO attrSynRespDTO = new AttrSynRespDTO();
        attrSynRespDTO.setPrice(new BigDecimal("0.00"));
        attrSynRespDTO.setIsDefault(0);
        attrGroupSynRespDTO.setAttrList(Arrays.asList(attrSynRespDTO));
        subItemSkuSynRespDTO.setAttrGroupList(Arrays.asList(attrGroupSynRespDTO));
        subgroupSynRespDTO.setSubItemSkuList(Arrays.asList(subItemSkuSynRespDTO));
        wxStoreItemRespDTO.setSubgroupList(Arrays.asList(subgroupSynRespDTO));
        final WxStoreAttrGroupRespDTO wxStoreAttrGroupRespDTO = new WxStoreAttrGroupRespDTO();
        final WxStoreAttrRespDTO wxStoreAttrRespDTO = new WxStoreAttrRespDTO();
        wxStoreAttrRespDTO.setPrice(new BigDecimal("0.00"));
        wxStoreAttrRespDTO.setUserck(0);
        wxStoreAttrGroupRespDTO.setAttrList(Arrays.asList(wxStoreAttrRespDTO));
        wxStoreItemRespDTO.setAttrGroupList(Arrays.asList(wxStoreAttrGroupRespDTO));
        wxStoreItemRespDTO.setCurrentCount(new BigDecimal("0.00"));
        wxStoreItemRespDTO.setItemPrice(new BigDecimal("0.00"));
        wxStoreAdvanceOrderDTO.setItemList(Arrays.asList(wxStoreItemRespDTO));

        final WxStoreAdvanceEstimateDTO expectedResult = WxStoreAdvanceEstimateDTO.builder()
                .estimateResult(false)
                .wxStoreEstimateItemList(Arrays.asList(WxStoreEstimateItem.builder()
                        .skuGuid("skuGuid")
                        .skuName("name")
                        .itemName("itemName")
                        .itemGuid("itemGuid")
                        .build()))
                .build();

        // Configure WxStoreEstimateClientService.queryEstimateForSyn(...).
        final List<ItemEstimateForAndroidRespDTO> itemEstimateForAndroidRespDTOS = Arrays.asList(
                ItemEstimateForAndroidRespDTO.builder()
                        .skuGuid("skuGuid")
                        .isSoldOut(0)
                        .build());
        final BaseDTO baseDTO = new BaseDTO();
        baseDTO.setDeviceType(0);
        baseDTO.setDeviceId("deviceId");
        baseDTO.setEnterpriseGuid("enterpriseGuid");
        baseDTO.setEnterpriseName("enterpriseName");
        baseDTO.setStoreGuid("storeGuid");
        when(mockWxStoreEstimateClientService.queryEstimateForSyn(baseDTO)).thenReturn(itemEstimateForAndroidRespDTOS);

        when(mockWxStoreMenuDetailsService.judgeOrderType(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build())).thenReturn(false);
        when(mockRedisUtils.keyGenerate(BusinessName.ADVANCE_ORDER)).thenReturn("key");
        when(mockRedisUtils.hGet("key", "nopenId")).thenReturn("result");

        // Run the test
        final WxStoreAdvanceEstimateDTO result = wxStoreAdvanceOrderServiceImplUnderTest.createAdvanceOrder(
                wxStoreAdvanceOrderDTO);

        // Verify the results
        assertEquals(expectedResult, result);

        // Confirm RedisUtils.hPut(...).
        final WxStoreAdvanceOrderDTO value = new WxStoreAdvanceOrderDTO();
        value.setWxStoreAdvanceConsumerReqDTO(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build());
        final WxStoreItemRespDTO wxStoreItemRespDTO1 = new WxStoreItemRespDTO();
        wxStoreItemRespDTO1.setItemGuid("itemGuid");
        wxStoreItemRespDTO1.setItemType(0);
        wxStoreItemRespDTO1.setName("itemName");
        wxStoreItemRespDTO1.setIsFixPkg(0);
        final WxStoreSkuRespDTO wxStoreSkuRespDTO1 = new WxStoreSkuRespDTO();
        wxStoreSkuRespDTO1.setSkuGuid("skuGuid");
        wxStoreSkuRespDTO1.setName("name");
        wxStoreSkuRespDTO1.setSalePrice(new BigDecimal("0.00"));
        wxStoreSkuRespDTO1.setUserck(0);
        wxStoreItemRespDTO1.setSkuList(Arrays.asList(wxStoreSkuRespDTO1));
        final SubgroupSynRespDTO subgroupSynRespDTO1 = new SubgroupSynRespDTO();
        subgroupSynRespDTO1.setPickNum(0);
        final SubItemSkuSynRespDTO subItemSkuSynRespDTO1 = new SubItemSkuSynRespDTO();
        subItemSkuSynRespDTO1.setSkuGuid("skuGuid");
        subItemSkuSynRespDTO1.setSkuName("name");
        subItemSkuSynRespDTO1.setItemType(0);
        subItemSkuSynRespDTO1.setItemNum(new BigDecimal("0.00"));
        subItemSkuSynRespDTO1.setAddPrice(new BigDecimal("0.00"));
        subItemSkuSynRespDTO1.setDefaultNum(0);
        final AttrGroupSynRespDTO attrGroupSynRespDTO1 = new AttrGroupSynRespDTO();
        attrGroupSynRespDTO1.setIsRequired(0);
        attrGroupSynRespDTO1.setWithDefault(0);
        final AttrSynRespDTO attrSynRespDTO1 = new AttrSynRespDTO();
        attrSynRespDTO1.setPrice(new BigDecimal("0.00"));
        attrSynRespDTO1.setIsDefault(0);
        attrGroupSynRespDTO1.setAttrList(Arrays.asList(attrSynRespDTO1));
        subItemSkuSynRespDTO1.setAttrGroupList(Arrays.asList(attrGroupSynRespDTO1));
        subgroupSynRespDTO1.setSubItemSkuList(Arrays.asList(subItemSkuSynRespDTO1));
        wxStoreItemRespDTO1.setSubgroupList(Arrays.asList(subgroupSynRespDTO1));
        final WxStoreAttrGroupRespDTO wxStoreAttrGroupRespDTO1 = new WxStoreAttrGroupRespDTO();
        final WxStoreAttrRespDTO wxStoreAttrRespDTO1 = new WxStoreAttrRespDTO();
        wxStoreAttrRespDTO1.setPrice(new BigDecimal("0.00"));
        wxStoreAttrRespDTO1.setUserck(0);
        wxStoreAttrGroupRespDTO1.setAttrList(Arrays.asList(wxStoreAttrRespDTO1));
        wxStoreItemRespDTO1.setAttrGroupList(Arrays.asList(wxStoreAttrGroupRespDTO1));
        wxStoreItemRespDTO1.setCurrentCount(new BigDecimal("0.00"));
        wxStoreItemRespDTO1.setItemPrice(new BigDecimal("0.00"));
        value.setItemList(Arrays.asList(wxStoreItemRespDTO1));
        verify(mockRedisUtils).hPut("key", "nopenId", value);
        verify(mockWxSocketMsgService).distribute(WxSocketDistributionDTO.builder()
                .distribution(0)
                .tableGuid("diningTableGuid")
                .openId("nopenId")
                .content("content")
                .build());

        // Confirm RedisUtils.set(...).
        final WxStoreAdvanceOrderDTO value1 = new WxStoreAdvanceOrderDTO();
        value1.setWxStoreAdvanceConsumerReqDTO(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build());
        final WxStoreItemRespDTO wxStoreItemRespDTO2 = new WxStoreItemRespDTO();
        wxStoreItemRespDTO2.setItemGuid("itemGuid");
        wxStoreItemRespDTO2.setItemType(0);
        wxStoreItemRespDTO2.setName("itemName");
        wxStoreItemRespDTO2.setIsFixPkg(0);
        final WxStoreSkuRespDTO wxStoreSkuRespDTO2 = new WxStoreSkuRespDTO();
        wxStoreSkuRespDTO2.setSkuGuid("skuGuid");
        wxStoreSkuRespDTO2.setName("name");
        wxStoreSkuRespDTO2.setSalePrice(new BigDecimal("0.00"));
        wxStoreSkuRespDTO2.setUserck(0);
        wxStoreItemRespDTO2.setSkuList(Arrays.asList(wxStoreSkuRespDTO2));
        final SubgroupSynRespDTO subgroupSynRespDTO2 = new SubgroupSynRespDTO();
        subgroupSynRespDTO2.setPickNum(0);
        final SubItemSkuSynRespDTO subItemSkuSynRespDTO2 = new SubItemSkuSynRespDTO();
        subItemSkuSynRespDTO2.setSkuGuid("skuGuid");
        subItemSkuSynRespDTO2.setSkuName("name");
        subItemSkuSynRespDTO2.setItemType(0);
        subItemSkuSynRespDTO2.setItemNum(new BigDecimal("0.00"));
        subItemSkuSynRespDTO2.setAddPrice(new BigDecimal("0.00"));
        subItemSkuSynRespDTO2.setDefaultNum(0);
        final AttrGroupSynRespDTO attrGroupSynRespDTO2 = new AttrGroupSynRespDTO();
        attrGroupSynRespDTO2.setIsRequired(0);
        attrGroupSynRespDTO2.setWithDefault(0);
        final AttrSynRespDTO attrSynRespDTO2 = new AttrSynRespDTO();
        attrSynRespDTO2.setPrice(new BigDecimal("0.00"));
        attrSynRespDTO2.setIsDefault(0);
        attrGroupSynRespDTO2.setAttrList(Arrays.asList(attrSynRespDTO2));
        subItemSkuSynRespDTO2.setAttrGroupList(Arrays.asList(attrGroupSynRespDTO2));
        subgroupSynRespDTO2.setSubItemSkuList(Arrays.asList(subItemSkuSynRespDTO2));
        wxStoreItemRespDTO2.setSubgroupList(Arrays.asList(subgroupSynRespDTO2));
        final WxStoreAttrGroupRespDTO wxStoreAttrGroupRespDTO2 = new WxStoreAttrGroupRespDTO();
        final WxStoreAttrRespDTO wxStoreAttrRespDTO2 = new WxStoreAttrRespDTO();
        wxStoreAttrRespDTO2.setPrice(new BigDecimal("0.00"));
        wxStoreAttrRespDTO2.setUserck(0);
        wxStoreAttrGroupRespDTO2.setAttrList(Arrays.asList(wxStoreAttrRespDTO2));
        wxStoreItemRespDTO2.setAttrGroupList(Arrays.asList(wxStoreAttrGroupRespDTO2));
        wxStoreItemRespDTO2.setCurrentCount(new BigDecimal("0.00"));
        wxStoreItemRespDTO2.setItemPrice(new BigDecimal("0.00"));
        value1.setItemList(Arrays.asList(wxStoreItemRespDTO2));
        verify(mockRedisUtils).set("key", value1);
        verify(mockRedisUtils).expire("key", 2L, TimeUnit.HOURS);
    }

    @Test
    public void testCreateAdvanceOrder_WxStoreEstimateClientServiceReturnsNoItems() {
        // Setup
        final WxStoreAdvanceOrderDTO wxStoreAdvanceOrderDTO = new WxStoreAdvanceOrderDTO();
        wxStoreAdvanceOrderDTO.setWxStoreAdvanceConsumerReqDTO(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build());
        final WxStoreItemRespDTO wxStoreItemRespDTO = new WxStoreItemRespDTO();
        wxStoreItemRespDTO.setItemGuid("itemGuid");
        wxStoreItemRespDTO.setItemType(0);
        wxStoreItemRespDTO.setName("itemName");
        wxStoreItemRespDTO.setIsFixPkg(0);
        final WxStoreSkuRespDTO wxStoreSkuRespDTO = new WxStoreSkuRespDTO();
        wxStoreSkuRespDTO.setSkuGuid("skuGuid");
        wxStoreSkuRespDTO.setName("name");
        wxStoreSkuRespDTO.setSalePrice(new BigDecimal("0.00"));
        wxStoreSkuRespDTO.setUserck(0);
        wxStoreItemRespDTO.setSkuList(Arrays.asList(wxStoreSkuRespDTO));
        final SubgroupSynRespDTO subgroupSynRespDTO = new SubgroupSynRespDTO();
        subgroupSynRespDTO.setPickNum(0);
        final SubItemSkuSynRespDTO subItemSkuSynRespDTO = new SubItemSkuSynRespDTO();
        subItemSkuSynRespDTO.setSkuGuid("skuGuid");
        subItemSkuSynRespDTO.setSkuName("name");
        subItemSkuSynRespDTO.setItemType(0);
        subItemSkuSynRespDTO.setItemNum(new BigDecimal("0.00"));
        subItemSkuSynRespDTO.setAddPrice(new BigDecimal("0.00"));
        subItemSkuSynRespDTO.setDefaultNum(0);
        final AttrGroupSynRespDTO attrGroupSynRespDTO = new AttrGroupSynRespDTO();
        attrGroupSynRespDTO.setIsRequired(0);
        attrGroupSynRespDTO.setWithDefault(0);
        final AttrSynRespDTO attrSynRespDTO = new AttrSynRespDTO();
        attrSynRespDTO.setPrice(new BigDecimal("0.00"));
        attrSynRespDTO.setIsDefault(0);
        attrGroupSynRespDTO.setAttrList(Arrays.asList(attrSynRespDTO));
        subItemSkuSynRespDTO.setAttrGroupList(Arrays.asList(attrGroupSynRespDTO));
        subgroupSynRespDTO.setSubItemSkuList(Arrays.asList(subItemSkuSynRespDTO));
        wxStoreItemRespDTO.setSubgroupList(Arrays.asList(subgroupSynRespDTO));
        final WxStoreAttrGroupRespDTO wxStoreAttrGroupRespDTO = new WxStoreAttrGroupRespDTO();
        final WxStoreAttrRespDTO wxStoreAttrRespDTO = new WxStoreAttrRespDTO();
        wxStoreAttrRespDTO.setPrice(new BigDecimal("0.00"));
        wxStoreAttrRespDTO.setUserck(0);
        wxStoreAttrGroupRespDTO.setAttrList(Arrays.asList(wxStoreAttrRespDTO));
        wxStoreItemRespDTO.setAttrGroupList(Arrays.asList(wxStoreAttrGroupRespDTO));
        wxStoreItemRespDTO.setCurrentCount(new BigDecimal("0.00"));
        wxStoreItemRespDTO.setItemPrice(new BigDecimal("0.00"));
        wxStoreAdvanceOrderDTO.setItemList(Arrays.asList(wxStoreItemRespDTO));

        final WxStoreAdvanceEstimateDTO expectedResult = WxStoreAdvanceEstimateDTO.builder()
                .estimateResult(false)
                .wxStoreEstimateItemList(Arrays.asList(WxStoreEstimateItem.builder()
                        .skuGuid("skuGuid")
                        .skuName("name")
                        .itemName("itemName")
                        .itemGuid("itemGuid")
                        .build()))
                .build();

        // Configure WxStoreEstimateClientService.queryEstimateForSyn(...).
        final BaseDTO baseDTO = new BaseDTO();
        baseDTO.setDeviceType(0);
        baseDTO.setDeviceId("deviceId");
        baseDTO.setEnterpriseGuid("enterpriseGuid");
        baseDTO.setEnterpriseName("enterpriseName");
        baseDTO.setStoreGuid("storeGuid");
        when(mockWxStoreEstimateClientService.queryEstimateForSyn(baseDTO)).thenReturn(Collections.emptyList());

        when(mockWxStoreMenuDetailsService.judgeOrderType(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build())).thenReturn(false);
        when(mockRedisUtils.keyGenerate(BusinessName.ADVANCE_ORDER)).thenReturn("key");
        when(mockRedisUtils.hGet("key", "nopenId")).thenReturn("result");

        // Run the test
        final WxStoreAdvanceEstimateDTO result = wxStoreAdvanceOrderServiceImplUnderTest.createAdvanceOrder(
                wxStoreAdvanceOrderDTO);

        // Verify the results
        assertEquals(expectedResult, result);

        // Confirm RedisUtils.hPut(...).
        final WxStoreAdvanceOrderDTO value = new WxStoreAdvanceOrderDTO();
        value.setWxStoreAdvanceConsumerReqDTO(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build());
        final WxStoreItemRespDTO wxStoreItemRespDTO1 = new WxStoreItemRespDTO();
        wxStoreItemRespDTO1.setItemGuid("itemGuid");
        wxStoreItemRespDTO1.setItemType(0);
        wxStoreItemRespDTO1.setName("itemName");
        wxStoreItemRespDTO1.setIsFixPkg(0);
        final WxStoreSkuRespDTO wxStoreSkuRespDTO1 = new WxStoreSkuRespDTO();
        wxStoreSkuRespDTO1.setSkuGuid("skuGuid");
        wxStoreSkuRespDTO1.setName("name");
        wxStoreSkuRespDTO1.setSalePrice(new BigDecimal("0.00"));
        wxStoreSkuRespDTO1.setUserck(0);
        wxStoreItemRespDTO1.setSkuList(Arrays.asList(wxStoreSkuRespDTO1));
        final SubgroupSynRespDTO subgroupSynRespDTO1 = new SubgroupSynRespDTO();
        subgroupSynRespDTO1.setPickNum(0);
        final SubItemSkuSynRespDTO subItemSkuSynRespDTO1 = new SubItemSkuSynRespDTO();
        subItemSkuSynRespDTO1.setSkuGuid("skuGuid");
        subItemSkuSynRespDTO1.setSkuName("name");
        subItemSkuSynRespDTO1.setItemType(0);
        subItemSkuSynRespDTO1.setItemNum(new BigDecimal("0.00"));
        subItemSkuSynRespDTO1.setAddPrice(new BigDecimal("0.00"));
        subItemSkuSynRespDTO1.setDefaultNum(0);
        final AttrGroupSynRespDTO attrGroupSynRespDTO1 = new AttrGroupSynRespDTO();
        attrGroupSynRespDTO1.setIsRequired(0);
        attrGroupSynRespDTO1.setWithDefault(0);
        final AttrSynRespDTO attrSynRespDTO1 = new AttrSynRespDTO();
        attrSynRespDTO1.setPrice(new BigDecimal("0.00"));
        attrSynRespDTO1.setIsDefault(0);
        attrGroupSynRespDTO1.setAttrList(Arrays.asList(attrSynRespDTO1));
        subItemSkuSynRespDTO1.setAttrGroupList(Arrays.asList(attrGroupSynRespDTO1));
        subgroupSynRespDTO1.setSubItemSkuList(Arrays.asList(subItemSkuSynRespDTO1));
        wxStoreItemRespDTO1.setSubgroupList(Arrays.asList(subgroupSynRespDTO1));
        final WxStoreAttrGroupRespDTO wxStoreAttrGroupRespDTO1 = new WxStoreAttrGroupRespDTO();
        final WxStoreAttrRespDTO wxStoreAttrRespDTO1 = new WxStoreAttrRespDTO();
        wxStoreAttrRespDTO1.setPrice(new BigDecimal("0.00"));
        wxStoreAttrRespDTO1.setUserck(0);
        wxStoreAttrGroupRespDTO1.setAttrList(Arrays.asList(wxStoreAttrRespDTO1));
        wxStoreItemRespDTO1.setAttrGroupList(Arrays.asList(wxStoreAttrGroupRespDTO1));
        wxStoreItemRespDTO1.setCurrentCount(new BigDecimal("0.00"));
        wxStoreItemRespDTO1.setItemPrice(new BigDecimal("0.00"));
        value.setItemList(Arrays.asList(wxStoreItemRespDTO1));
        verify(mockRedisUtils).hPut("key", "nopenId", value);
        verify(mockWxSocketMsgService).distribute(WxSocketDistributionDTO.builder()
                .distribution(0)
                .tableGuid("diningTableGuid")
                .openId("nopenId")
                .content("content")
                .build());

        // Confirm RedisUtils.set(...).
        final WxStoreAdvanceOrderDTO value1 = new WxStoreAdvanceOrderDTO();
        value1.setWxStoreAdvanceConsumerReqDTO(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build());
        final WxStoreItemRespDTO wxStoreItemRespDTO2 = new WxStoreItemRespDTO();
        wxStoreItemRespDTO2.setItemGuid("itemGuid");
        wxStoreItemRespDTO2.setItemType(0);
        wxStoreItemRespDTO2.setName("itemName");
        wxStoreItemRespDTO2.setIsFixPkg(0);
        final WxStoreSkuRespDTO wxStoreSkuRespDTO2 = new WxStoreSkuRespDTO();
        wxStoreSkuRespDTO2.setSkuGuid("skuGuid");
        wxStoreSkuRespDTO2.setName("name");
        wxStoreSkuRespDTO2.setSalePrice(new BigDecimal("0.00"));
        wxStoreSkuRespDTO2.setUserck(0);
        wxStoreItemRespDTO2.setSkuList(Arrays.asList(wxStoreSkuRespDTO2));
        final SubgroupSynRespDTO subgroupSynRespDTO2 = new SubgroupSynRespDTO();
        subgroupSynRespDTO2.setPickNum(0);
        final SubItemSkuSynRespDTO subItemSkuSynRespDTO2 = new SubItemSkuSynRespDTO();
        subItemSkuSynRespDTO2.setSkuGuid("skuGuid");
        subItemSkuSynRespDTO2.setSkuName("name");
        subItemSkuSynRespDTO2.setItemType(0);
        subItemSkuSynRespDTO2.setItemNum(new BigDecimal("0.00"));
        subItemSkuSynRespDTO2.setAddPrice(new BigDecimal("0.00"));
        subItemSkuSynRespDTO2.setDefaultNum(0);
        final AttrGroupSynRespDTO attrGroupSynRespDTO2 = new AttrGroupSynRespDTO();
        attrGroupSynRespDTO2.setIsRequired(0);
        attrGroupSynRespDTO2.setWithDefault(0);
        final AttrSynRespDTO attrSynRespDTO2 = new AttrSynRespDTO();
        attrSynRespDTO2.setPrice(new BigDecimal("0.00"));
        attrSynRespDTO2.setIsDefault(0);
        attrGroupSynRespDTO2.setAttrList(Arrays.asList(attrSynRespDTO2));
        subItemSkuSynRespDTO2.setAttrGroupList(Arrays.asList(attrGroupSynRespDTO2));
        subgroupSynRespDTO2.setSubItemSkuList(Arrays.asList(subItemSkuSynRespDTO2));
        wxStoreItemRespDTO2.setSubgroupList(Arrays.asList(subgroupSynRespDTO2));
        final WxStoreAttrGroupRespDTO wxStoreAttrGroupRespDTO2 = new WxStoreAttrGroupRespDTO();
        final WxStoreAttrRespDTO wxStoreAttrRespDTO2 = new WxStoreAttrRespDTO();
        wxStoreAttrRespDTO2.setPrice(new BigDecimal("0.00"));
        wxStoreAttrRespDTO2.setUserck(0);
        wxStoreAttrGroupRespDTO2.setAttrList(Arrays.asList(wxStoreAttrRespDTO2));
        wxStoreItemRespDTO2.setAttrGroupList(Arrays.asList(wxStoreAttrGroupRespDTO2));
        wxStoreItemRespDTO2.setCurrentCount(new BigDecimal("0.00"));
        wxStoreItemRespDTO2.setItemPrice(new BigDecimal("0.00"));
        value1.setItemList(Arrays.asList(wxStoreItemRespDTO2));
        verify(mockRedisUtils).set("key", value1);
        verify(mockRedisUtils).expire("key", 2L, TimeUnit.HOURS);
    }

    @Test
    public void testCreateAdvanceOrder_WxStoreMenuDetailsServiceReturnsTrue() {
        // Setup
        final WxStoreAdvanceOrderDTO wxStoreAdvanceOrderDTO = new WxStoreAdvanceOrderDTO();
        wxStoreAdvanceOrderDTO.setWxStoreAdvanceConsumerReqDTO(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build());
        final WxStoreItemRespDTO wxStoreItemRespDTO = new WxStoreItemRespDTO();
        wxStoreItemRespDTO.setItemGuid("itemGuid");
        wxStoreItemRespDTO.setItemType(0);
        wxStoreItemRespDTO.setName("itemName");
        wxStoreItemRespDTO.setIsFixPkg(0);
        final WxStoreSkuRespDTO wxStoreSkuRespDTO = new WxStoreSkuRespDTO();
        wxStoreSkuRespDTO.setSkuGuid("skuGuid");
        wxStoreSkuRespDTO.setName("name");
        wxStoreSkuRespDTO.setSalePrice(new BigDecimal("0.00"));
        wxStoreSkuRespDTO.setUserck(0);
        wxStoreItemRespDTO.setSkuList(Arrays.asList(wxStoreSkuRespDTO));
        final SubgroupSynRespDTO subgroupSynRespDTO = new SubgroupSynRespDTO();
        subgroupSynRespDTO.setPickNum(0);
        final SubItemSkuSynRespDTO subItemSkuSynRespDTO = new SubItemSkuSynRespDTO();
        subItemSkuSynRespDTO.setSkuGuid("skuGuid");
        subItemSkuSynRespDTO.setSkuName("name");
        subItemSkuSynRespDTO.setItemType(0);
        subItemSkuSynRespDTO.setItemNum(new BigDecimal("0.00"));
        subItemSkuSynRespDTO.setAddPrice(new BigDecimal("0.00"));
        subItemSkuSynRespDTO.setDefaultNum(0);
        final AttrGroupSynRespDTO attrGroupSynRespDTO = new AttrGroupSynRespDTO();
        attrGroupSynRespDTO.setIsRequired(0);
        attrGroupSynRespDTO.setWithDefault(0);
        final AttrSynRespDTO attrSynRespDTO = new AttrSynRespDTO();
        attrSynRespDTO.setPrice(new BigDecimal("0.00"));
        attrSynRespDTO.setIsDefault(0);
        attrGroupSynRespDTO.setAttrList(Arrays.asList(attrSynRespDTO));
        subItemSkuSynRespDTO.setAttrGroupList(Arrays.asList(attrGroupSynRespDTO));
        subgroupSynRespDTO.setSubItemSkuList(Arrays.asList(subItemSkuSynRespDTO));
        wxStoreItemRespDTO.setSubgroupList(Arrays.asList(subgroupSynRespDTO));
        final WxStoreAttrGroupRespDTO wxStoreAttrGroupRespDTO = new WxStoreAttrGroupRespDTO();
        final WxStoreAttrRespDTO wxStoreAttrRespDTO = new WxStoreAttrRespDTO();
        wxStoreAttrRespDTO.setPrice(new BigDecimal("0.00"));
        wxStoreAttrRespDTO.setUserck(0);
        wxStoreAttrGroupRespDTO.setAttrList(Arrays.asList(wxStoreAttrRespDTO));
        wxStoreItemRespDTO.setAttrGroupList(Arrays.asList(wxStoreAttrGroupRespDTO));
        wxStoreItemRespDTO.setCurrentCount(new BigDecimal("0.00"));
        wxStoreItemRespDTO.setItemPrice(new BigDecimal("0.00"));
        wxStoreAdvanceOrderDTO.setItemList(Arrays.asList(wxStoreItemRespDTO));

        final WxStoreAdvanceEstimateDTO expectedResult = WxStoreAdvanceEstimateDTO.builder()
                .estimateResult(false)
                .wxStoreEstimateItemList(Arrays.asList(WxStoreEstimateItem.builder()
                        .skuGuid("skuGuid")
                        .skuName("name")
                        .itemName("itemName")
                        .itemGuid("itemGuid")
                        .build()))
                .build();

        // Configure WxStoreEstimateClientService.queryEstimateForSyn(...).
        final List<ItemEstimateForAndroidRespDTO> itemEstimateForAndroidRespDTOS = Arrays.asList(
                ItemEstimateForAndroidRespDTO.builder()
                        .skuGuid("skuGuid")
                        .isSoldOut(0)
                        .build());
        final BaseDTO baseDTO = new BaseDTO();
        baseDTO.setDeviceType(0);
        baseDTO.setDeviceId("deviceId");
        baseDTO.setEnterpriseGuid("enterpriseGuid");
        baseDTO.setEnterpriseName("enterpriseName");
        baseDTO.setStoreGuid("storeGuid");
        when(mockWxStoreEstimateClientService.queryEstimateForSyn(baseDTO)).thenReturn(itemEstimateForAndroidRespDTOS);

        when(mockWxStoreMenuDetailsService.judgeOrderType(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build())).thenReturn(true);
        when(mockRedisUtils.keyGenerate(BusinessName.ADVANCE_ORDER)).thenReturn("key");
        when(mockRedisUtils.hGet("key", "nopenId")).thenReturn("result");

        // Run the test
        final WxStoreAdvanceEstimateDTO result = wxStoreAdvanceOrderServiceImplUnderTest.createAdvanceOrder(
                wxStoreAdvanceOrderDTO);

        // Verify the results
        assertEquals(expectedResult, result);

        // Confirm RedisUtils.hPut(...).
        final WxStoreAdvanceOrderDTO value = new WxStoreAdvanceOrderDTO();
        value.setWxStoreAdvanceConsumerReqDTO(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build());
        final WxStoreItemRespDTO wxStoreItemRespDTO1 = new WxStoreItemRespDTO();
        wxStoreItemRespDTO1.setItemGuid("itemGuid");
        wxStoreItemRespDTO1.setItemType(0);
        wxStoreItemRespDTO1.setName("itemName");
        wxStoreItemRespDTO1.setIsFixPkg(0);
        final WxStoreSkuRespDTO wxStoreSkuRespDTO1 = new WxStoreSkuRespDTO();
        wxStoreSkuRespDTO1.setSkuGuid("skuGuid");
        wxStoreSkuRespDTO1.setName("name");
        wxStoreSkuRespDTO1.setSalePrice(new BigDecimal("0.00"));
        wxStoreSkuRespDTO1.setUserck(0);
        wxStoreItemRespDTO1.setSkuList(Arrays.asList(wxStoreSkuRespDTO1));
        final SubgroupSynRespDTO subgroupSynRespDTO1 = new SubgroupSynRespDTO();
        subgroupSynRespDTO1.setPickNum(0);
        final SubItemSkuSynRespDTO subItemSkuSynRespDTO1 = new SubItemSkuSynRespDTO();
        subItemSkuSynRespDTO1.setSkuGuid("skuGuid");
        subItemSkuSynRespDTO1.setSkuName("name");
        subItemSkuSynRespDTO1.setItemType(0);
        subItemSkuSynRespDTO1.setItemNum(new BigDecimal("0.00"));
        subItemSkuSynRespDTO1.setAddPrice(new BigDecimal("0.00"));
        subItemSkuSynRespDTO1.setDefaultNum(0);
        final AttrGroupSynRespDTO attrGroupSynRespDTO1 = new AttrGroupSynRespDTO();
        attrGroupSynRespDTO1.setIsRequired(0);
        attrGroupSynRespDTO1.setWithDefault(0);
        final AttrSynRespDTO attrSynRespDTO1 = new AttrSynRespDTO();
        attrSynRespDTO1.setPrice(new BigDecimal("0.00"));
        attrSynRespDTO1.setIsDefault(0);
        attrGroupSynRespDTO1.setAttrList(Arrays.asList(attrSynRespDTO1));
        subItemSkuSynRespDTO1.setAttrGroupList(Arrays.asList(attrGroupSynRespDTO1));
        subgroupSynRespDTO1.setSubItemSkuList(Arrays.asList(subItemSkuSynRespDTO1));
        wxStoreItemRespDTO1.setSubgroupList(Arrays.asList(subgroupSynRespDTO1));
        final WxStoreAttrGroupRespDTO wxStoreAttrGroupRespDTO1 = new WxStoreAttrGroupRespDTO();
        final WxStoreAttrRespDTO wxStoreAttrRespDTO1 = new WxStoreAttrRespDTO();
        wxStoreAttrRespDTO1.setPrice(new BigDecimal("0.00"));
        wxStoreAttrRespDTO1.setUserck(0);
        wxStoreAttrGroupRespDTO1.setAttrList(Arrays.asList(wxStoreAttrRespDTO1));
        wxStoreItemRespDTO1.setAttrGroupList(Arrays.asList(wxStoreAttrGroupRespDTO1));
        wxStoreItemRespDTO1.setCurrentCount(new BigDecimal("0.00"));
        wxStoreItemRespDTO1.setItemPrice(new BigDecimal("0.00"));
        value.setItemList(Arrays.asList(wxStoreItemRespDTO1));
        verify(mockRedisUtils).hPut("key", "nopenId", value);
        verify(mockWxSocketMsgService).distribute(WxSocketDistributionDTO.builder()
                .distribution(0)
                .tableGuid("diningTableGuid")
                .openId("nopenId")
                .content("content")
                .build());

        // Confirm RedisUtils.set(...).
        final WxStoreAdvanceOrderDTO value1 = new WxStoreAdvanceOrderDTO();
        value1.setWxStoreAdvanceConsumerReqDTO(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build());
        final WxStoreItemRespDTO wxStoreItemRespDTO2 = new WxStoreItemRespDTO();
        wxStoreItemRespDTO2.setItemGuid("itemGuid");
        wxStoreItemRespDTO2.setItemType(0);
        wxStoreItemRespDTO2.setName("itemName");
        wxStoreItemRespDTO2.setIsFixPkg(0);
        final WxStoreSkuRespDTO wxStoreSkuRespDTO2 = new WxStoreSkuRespDTO();
        wxStoreSkuRespDTO2.setSkuGuid("skuGuid");
        wxStoreSkuRespDTO2.setName("name");
        wxStoreSkuRespDTO2.setSalePrice(new BigDecimal("0.00"));
        wxStoreSkuRespDTO2.setUserck(0);
        wxStoreItemRespDTO2.setSkuList(Arrays.asList(wxStoreSkuRespDTO2));
        final SubgroupSynRespDTO subgroupSynRespDTO2 = new SubgroupSynRespDTO();
        subgroupSynRespDTO2.setPickNum(0);
        final SubItemSkuSynRespDTO subItemSkuSynRespDTO2 = new SubItemSkuSynRespDTO();
        subItemSkuSynRespDTO2.setSkuGuid("skuGuid");
        subItemSkuSynRespDTO2.setSkuName("name");
        subItemSkuSynRespDTO2.setItemType(0);
        subItemSkuSynRespDTO2.setItemNum(new BigDecimal("0.00"));
        subItemSkuSynRespDTO2.setAddPrice(new BigDecimal("0.00"));
        subItemSkuSynRespDTO2.setDefaultNum(0);
        final AttrGroupSynRespDTO attrGroupSynRespDTO2 = new AttrGroupSynRespDTO();
        attrGroupSynRespDTO2.setIsRequired(0);
        attrGroupSynRespDTO2.setWithDefault(0);
        final AttrSynRespDTO attrSynRespDTO2 = new AttrSynRespDTO();
        attrSynRespDTO2.setPrice(new BigDecimal("0.00"));
        attrSynRespDTO2.setIsDefault(0);
        attrGroupSynRespDTO2.setAttrList(Arrays.asList(attrSynRespDTO2));
        subItemSkuSynRespDTO2.setAttrGroupList(Arrays.asList(attrGroupSynRespDTO2));
        subgroupSynRespDTO2.setSubItemSkuList(Arrays.asList(subItemSkuSynRespDTO2));
        wxStoreItemRespDTO2.setSubgroupList(Arrays.asList(subgroupSynRespDTO2));
        final WxStoreAttrGroupRespDTO wxStoreAttrGroupRespDTO2 = new WxStoreAttrGroupRespDTO();
        final WxStoreAttrRespDTO wxStoreAttrRespDTO2 = new WxStoreAttrRespDTO();
        wxStoreAttrRespDTO2.setPrice(new BigDecimal("0.00"));
        wxStoreAttrRespDTO2.setUserck(0);
        wxStoreAttrGroupRespDTO2.setAttrList(Arrays.asList(wxStoreAttrRespDTO2));
        wxStoreItemRespDTO2.setAttrGroupList(Arrays.asList(wxStoreAttrGroupRespDTO2));
        wxStoreItemRespDTO2.setCurrentCount(new BigDecimal("0.00"));
        wxStoreItemRespDTO2.setItemPrice(new BigDecimal("0.00"));
        value1.setItemList(Arrays.asList(wxStoreItemRespDTO2));
        verify(mockRedisUtils).set("key", value1);
        verify(mockRedisUtils).expire("key", 2L, TimeUnit.HOURS);
    }

    @Test
    public void testCheckEstimate() {
        // Setup
        final WxStoreAdvanceOrderDTO wxStoreAdvanceOrderDTO = new WxStoreAdvanceOrderDTO();
        wxStoreAdvanceOrderDTO.setWxStoreAdvanceConsumerReqDTO(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build());
        final WxStoreItemRespDTO wxStoreItemRespDTO = new WxStoreItemRespDTO();
        wxStoreItemRespDTO.setItemGuid("itemGuid");
        wxStoreItemRespDTO.setItemType(0);
        wxStoreItemRespDTO.setName("itemName");
        wxStoreItemRespDTO.setIsFixPkg(0);
        final WxStoreSkuRespDTO wxStoreSkuRespDTO = new WxStoreSkuRespDTO();
        wxStoreSkuRespDTO.setSkuGuid("skuGuid");
        wxStoreSkuRespDTO.setName("name");
        wxStoreSkuRespDTO.setSalePrice(new BigDecimal("0.00"));
        wxStoreSkuRespDTO.setUserck(0);
        wxStoreItemRespDTO.setSkuList(Arrays.asList(wxStoreSkuRespDTO));
        final SubgroupSynRespDTO subgroupSynRespDTO = new SubgroupSynRespDTO();
        subgroupSynRespDTO.setPickNum(0);
        final SubItemSkuSynRespDTO subItemSkuSynRespDTO = new SubItemSkuSynRespDTO();
        subItemSkuSynRespDTO.setSkuGuid("skuGuid");
        subItemSkuSynRespDTO.setSkuName("name");
        subItemSkuSynRespDTO.setItemType(0);
        subItemSkuSynRespDTO.setItemNum(new BigDecimal("0.00"));
        subItemSkuSynRespDTO.setAddPrice(new BigDecimal("0.00"));
        subItemSkuSynRespDTO.setDefaultNum(0);
        final AttrGroupSynRespDTO attrGroupSynRespDTO = new AttrGroupSynRespDTO();
        attrGroupSynRespDTO.setIsRequired(0);
        attrGroupSynRespDTO.setWithDefault(0);
        final AttrSynRespDTO attrSynRespDTO = new AttrSynRespDTO();
        attrSynRespDTO.setPrice(new BigDecimal("0.00"));
        attrSynRespDTO.setIsDefault(0);
        attrGroupSynRespDTO.setAttrList(Arrays.asList(attrSynRespDTO));
        subItemSkuSynRespDTO.setAttrGroupList(Arrays.asList(attrGroupSynRespDTO));
        subgroupSynRespDTO.setSubItemSkuList(Arrays.asList(subItemSkuSynRespDTO));
        wxStoreItemRespDTO.setSubgroupList(Arrays.asList(subgroupSynRespDTO));
        final WxStoreAttrGroupRespDTO wxStoreAttrGroupRespDTO = new WxStoreAttrGroupRespDTO();
        final WxStoreAttrRespDTO wxStoreAttrRespDTO = new WxStoreAttrRespDTO();
        wxStoreAttrRespDTO.setPrice(new BigDecimal("0.00"));
        wxStoreAttrRespDTO.setUserck(0);
        wxStoreAttrGroupRespDTO.setAttrList(Arrays.asList(wxStoreAttrRespDTO));
        wxStoreItemRespDTO.setAttrGroupList(Arrays.asList(wxStoreAttrGroupRespDTO));
        wxStoreItemRespDTO.setCurrentCount(new BigDecimal("0.00"));
        wxStoreItemRespDTO.setItemPrice(new BigDecimal("0.00"));
        wxStoreAdvanceOrderDTO.setItemList(Arrays.asList(wxStoreItemRespDTO));

        final WxStoreAdvanceEstimateDTO expectedResult = WxStoreAdvanceEstimateDTO.builder()
                .estimateResult(false)
                .wxStoreEstimateItemList(Arrays.asList(WxStoreEstimateItem.builder()
                        .skuGuid("skuGuid")
                        .skuName("name")
                        .itemName("itemName")
                        .itemGuid("itemGuid")
                        .build()))
                .build();

        // Configure WxStoreEstimateClientService.queryEstimateForSyn(...).
        final List<ItemEstimateForAndroidRespDTO> itemEstimateForAndroidRespDTOS = Arrays.asList(
                ItemEstimateForAndroidRespDTO.builder()
                        .skuGuid("skuGuid")
                        .isSoldOut(0)
                        .build());
        final BaseDTO baseDTO = new BaseDTO();
        baseDTO.setDeviceType(0);
        baseDTO.setDeviceId("deviceId");
        baseDTO.setEnterpriseGuid("enterpriseGuid");
        baseDTO.setEnterpriseName("enterpriseName");
        baseDTO.setStoreGuid("storeGuid");
        when(mockWxStoreEstimateClientService.queryEstimateForSyn(baseDTO)).thenReturn(itemEstimateForAndroidRespDTOS);

        // Run the test
        final WxStoreAdvanceEstimateDTO result = wxStoreAdvanceOrderServiceImplUnderTest.checkEstimate(
                wxStoreAdvanceOrderDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testCheckEstimate_WxStoreEstimateClientServiceReturnsNoItems() {
        // Setup
        final WxStoreAdvanceOrderDTO wxStoreAdvanceOrderDTO = new WxStoreAdvanceOrderDTO();
        wxStoreAdvanceOrderDTO.setWxStoreAdvanceConsumerReqDTO(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build());
        final WxStoreItemRespDTO wxStoreItemRespDTO = new WxStoreItemRespDTO();
        wxStoreItemRespDTO.setItemGuid("itemGuid");
        wxStoreItemRespDTO.setItemType(0);
        wxStoreItemRespDTO.setName("itemName");
        wxStoreItemRespDTO.setIsFixPkg(0);
        final WxStoreSkuRespDTO wxStoreSkuRespDTO = new WxStoreSkuRespDTO();
        wxStoreSkuRespDTO.setSkuGuid("skuGuid");
        wxStoreSkuRespDTO.setName("name");
        wxStoreSkuRespDTO.setSalePrice(new BigDecimal("0.00"));
        wxStoreSkuRespDTO.setUserck(0);
        wxStoreItemRespDTO.setSkuList(Arrays.asList(wxStoreSkuRespDTO));
        final SubgroupSynRespDTO subgroupSynRespDTO = new SubgroupSynRespDTO();
        subgroupSynRespDTO.setPickNum(0);
        final SubItemSkuSynRespDTO subItemSkuSynRespDTO = new SubItemSkuSynRespDTO();
        subItemSkuSynRespDTO.setSkuGuid("skuGuid");
        subItemSkuSynRespDTO.setSkuName("name");
        subItemSkuSynRespDTO.setItemType(0);
        subItemSkuSynRespDTO.setItemNum(new BigDecimal("0.00"));
        subItemSkuSynRespDTO.setAddPrice(new BigDecimal("0.00"));
        subItemSkuSynRespDTO.setDefaultNum(0);
        final AttrGroupSynRespDTO attrGroupSynRespDTO = new AttrGroupSynRespDTO();
        attrGroupSynRespDTO.setIsRequired(0);
        attrGroupSynRespDTO.setWithDefault(0);
        final AttrSynRespDTO attrSynRespDTO = new AttrSynRespDTO();
        attrSynRespDTO.setPrice(new BigDecimal("0.00"));
        attrSynRespDTO.setIsDefault(0);
        attrGroupSynRespDTO.setAttrList(Arrays.asList(attrSynRespDTO));
        subItemSkuSynRespDTO.setAttrGroupList(Arrays.asList(attrGroupSynRespDTO));
        subgroupSynRespDTO.setSubItemSkuList(Arrays.asList(subItemSkuSynRespDTO));
        wxStoreItemRespDTO.setSubgroupList(Arrays.asList(subgroupSynRespDTO));
        final WxStoreAttrGroupRespDTO wxStoreAttrGroupRespDTO = new WxStoreAttrGroupRespDTO();
        final WxStoreAttrRespDTO wxStoreAttrRespDTO = new WxStoreAttrRespDTO();
        wxStoreAttrRespDTO.setPrice(new BigDecimal("0.00"));
        wxStoreAttrRespDTO.setUserck(0);
        wxStoreAttrGroupRespDTO.setAttrList(Arrays.asList(wxStoreAttrRespDTO));
        wxStoreItemRespDTO.setAttrGroupList(Arrays.asList(wxStoreAttrGroupRespDTO));
        wxStoreItemRespDTO.setCurrentCount(new BigDecimal("0.00"));
        wxStoreItemRespDTO.setItemPrice(new BigDecimal("0.00"));
        wxStoreAdvanceOrderDTO.setItemList(Arrays.asList(wxStoreItemRespDTO));

        final WxStoreAdvanceEstimateDTO expectedResult = WxStoreAdvanceEstimateDTO.builder()
                .estimateResult(false)
                .wxStoreEstimateItemList(Arrays.asList(WxStoreEstimateItem.builder()
                        .skuGuid("skuGuid")
                        .skuName("name")
                        .itemName("itemName")
                        .itemGuid("itemGuid")
                        .build()))
                .build();

        // Configure WxStoreEstimateClientService.queryEstimateForSyn(...).
        final BaseDTO baseDTO = new BaseDTO();
        baseDTO.setDeviceType(0);
        baseDTO.setDeviceId("deviceId");
        baseDTO.setEnterpriseGuid("enterpriseGuid");
        baseDTO.setEnterpriseName("enterpriseName");
        baseDTO.setStoreGuid("storeGuid");
        when(mockWxStoreEstimateClientService.queryEstimateForSyn(baseDTO)).thenReturn(Collections.emptyList());

        // Run the test
        final WxStoreAdvanceEstimateDTO result = wxStoreAdvanceOrderServiceImplUnderTest.checkEstimate(
                wxStoreAdvanceOrderDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testDelAdvanceOrder() {
        // Setup
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build();
        when(mockWxStoreMenuDetailsService.judgeOrderType(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build())).thenReturn(false);
        when(mockRedisUtils.keyGenerate(BusinessName.ADVANCE_ORDER)).thenReturn("key");
        when(mockRedisUtils.delete("key")).thenReturn(false);

        // Run the test
        final Boolean result = wxStoreAdvanceOrderServiceImplUnderTest.delAdvanceOrder(wxStoreAdvanceConsumerReqDTO);

        // Verify the results
        assertFalse(result);
        verify(mockRedisUtils).hDelete("key", "nopenId");
        verify(mockWxSocketMsgService).distribute(WxSocketDistributionDTO.builder()
                .distribution(0)
                .tableGuid("diningTableGuid")
                .openId("nopenId")
                .content("content")
                .build());
    }

    @Test
    public void testDelAdvanceOrder_WxStoreMenuDetailsServiceReturnsTrue() {
        // Setup
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build();
        when(mockWxStoreMenuDetailsService.judgeOrderType(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build())).thenReturn(true);
        when(mockRedisUtils.keyGenerate(BusinessName.ADVANCE_ORDER)).thenReturn("key");
        when(mockRedisUtils.delete("key")).thenReturn(false);

        // Run the test
        final Boolean result = wxStoreAdvanceOrderServiceImplUnderTest.delAdvanceOrder(wxStoreAdvanceConsumerReqDTO);

        // Verify the results
        assertFalse(result);
        verify(mockRedisUtils).hDelete("key", "nopenId");
        verify(mockWxSocketMsgService).distribute(WxSocketDistributionDTO.builder()
                .distribution(0)
                .tableGuid("diningTableGuid")
                .openId("nopenId")
                .content("content")
                .build());
    }

    @Test
    public void testDelAdvanceOrder_RedisUtilsDeleteReturnsTrue() {
        // Setup
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build();
        when(mockWxStoreMenuDetailsService.judgeOrderType(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build())).thenReturn(false);
        when(mockRedisUtils.keyGenerate(BusinessName.ADVANCE_ORDER)).thenReturn("key");
        when(mockRedisUtils.delete("key")).thenReturn(true);

        // Run the test
        final Boolean result = wxStoreAdvanceOrderServiceImplUnderTest.delAdvanceOrder(wxStoreAdvanceConsumerReqDTO);

        // Verify the results
        assertTrue(result);
    }

    @Test
    public void testDelAdvanceTableOrder() {
        // Setup
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build();
        when(mockWxStoreMenuDetailsService.judgeOrderType(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build())).thenReturn(false);
        when(mockRedisUtils.keyGenerate(BusinessName.ADVANCE_ORDER)).thenReturn("key");
        when(mockRedisUtils.delete("key")).thenReturn(false);

        // Run the test
        final Boolean result = wxStoreAdvanceOrderServiceImplUnderTest.delAdvanceTableOrder(
                wxStoreAdvanceConsumerReqDTO);

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testDelAdvanceTableOrder_WxStoreMenuDetailsServiceReturnsTrue() {
        // Setup
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build();
        when(mockWxStoreMenuDetailsService.judgeOrderType(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build())).thenReturn(true);
        when(mockRedisUtils.keyGenerate(BusinessName.ADVANCE_ORDER)).thenReturn("key");
        when(mockRedisUtils.delete("key")).thenReturn(false);

        // Run the test
        final Boolean result = wxStoreAdvanceOrderServiceImplUnderTest.delAdvanceTableOrder(
                wxStoreAdvanceConsumerReqDTO);

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testDelAdvanceTableOrder_RedisUtilsDeleteReturnsTrue() {
        // Setup
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build();
        when(mockWxStoreMenuDetailsService.judgeOrderType(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build())).thenReturn(false);
        when(mockRedisUtils.keyGenerate(BusinessName.ADVANCE_ORDER)).thenReturn("key");
        when(mockRedisUtils.delete("key")).thenReturn(true);

        // Run the test
        final Boolean result = wxStoreAdvanceOrderServiceImplUnderTest.delAdvanceTableOrder(
                wxStoreAdvanceConsumerReqDTO);

        // Verify the results
        assertTrue(result);
    }

    @Test
    public void testGetPersonWxStoreAdvanceOrder() {
        // Setup
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build();
        final WxStoreAdvanceOrderPriceDTO expectedResult = new WxStoreAdvanceOrderPriceDTO();
        expectedResult.setTradeOrderGuid("orderGuid");
        final WxStoreAdvanceOrderDTO wxStoreAdvanceOrderDTO = new WxStoreAdvanceOrderDTO();
        wxStoreAdvanceOrderDTO.setWxStoreAdvanceConsumerReqDTO(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build());
        final WxStoreItemRespDTO wxStoreItemRespDTO = new WxStoreItemRespDTO();
        wxStoreItemRespDTO.setItemGuid("itemGuid");
        wxStoreItemRespDTO.setItemType(0);
        wxStoreItemRespDTO.setName("itemName");
        wxStoreItemRespDTO.setIsFixPkg(0);
        final WxStoreSkuRespDTO wxStoreSkuRespDTO = new WxStoreSkuRespDTO();
        wxStoreSkuRespDTO.setSkuGuid("skuGuid");
        wxStoreSkuRespDTO.setName("name");
        wxStoreSkuRespDTO.setSalePrice(new BigDecimal("0.00"));
        wxStoreSkuRespDTO.setUserck(0);
        wxStoreItemRespDTO.setSkuList(Arrays.asList(wxStoreSkuRespDTO));
        final SubgroupSynRespDTO subgroupSynRespDTO = new SubgroupSynRespDTO();
        subgroupSynRespDTO.setPickNum(0);
        final SubItemSkuSynRespDTO subItemSkuSynRespDTO = new SubItemSkuSynRespDTO();
        subItemSkuSynRespDTO.setSkuGuid("skuGuid");
        subItemSkuSynRespDTO.setSkuName("name");
        subItemSkuSynRespDTO.setItemType(0);
        subItemSkuSynRespDTO.setItemNum(new BigDecimal("0.00"));
        subItemSkuSynRespDTO.setAddPrice(new BigDecimal("0.00"));
        subItemSkuSynRespDTO.setDefaultNum(0);
        final AttrGroupSynRespDTO attrGroupSynRespDTO = new AttrGroupSynRespDTO();
        attrGroupSynRespDTO.setIsRequired(0);
        attrGroupSynRespDTO.setWithDefault(0);
        final AttrSynRespDTO attrSynRespDTO = new AttrSynRespDTO();
        attrSynRespDTO.setPrice(new BigDecimal("0.00"));
        attrSynRespDTO.setIsDefault(0);
        attrGroupSynRespDTO.setAttrList(Arrays.asList(attrSynRespDTO));
        subItemSkuSynRespDTO.setAttrGroupList(Arrays.asList(attrGroupSynRespDTO));
        subgroupSynRespDTO.setSubItemSkuList(Arrays.asList(subItemSkuSynRespDTO));
        wxStoreItemRespDTO.setSubgroupList(Arrays.asList(subgroupSynRespDTO));
        final WxStoreAttrGroupRespDTO wxStoreAttrGroupRespDTO = new WxStoreAttrGroupRespDTO();
        final WxStoreAttrRespDTO wxStoreAttrRespDTO = new WxStoreAttrRespDTO();
        wxStoreAttrRespDTO.setPrice(new BigDecimal("0.00"));
        wxStoreAttrRespDTO.setUserck(0);
        wxStoreAttrGroupRespDTO.setAttrList(Arrays.asList(wxStoreAttrRespDTO));
        wxStoreItemRespDTO.setAttrGroupList(Arrays.asList(wxStoreAttrGroupRespDTO));
        wxStoreItemRespDTO.setCurrentCount(new BigDecimal("0.00"));
        wxStoreItemRespDTO.setItemPrice(new BigDecimal("0.00"));
        wxStoreAdvanceOrderDTO.setItemList(Arrays.asList(wxStoreItemRespDTO));
        expectedResult.setWxStoreAdvanceOrderDTOS(Arrays.asList(wxStoreAdvanceOrderDTO));
        expectedResult.setTotalPrice(new BigDecimal("0.00"));
        expectedResult.setTotalRemark("totalRemark");

        when(mockWxStoreMenuDetailsService.judgeOrderType(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build())).thenReturn(false);
        when(mockRedisUtils.keyGenerate(BusinessName.ADVANCE_ORDER)).thenReturn("key");
        when(mockRedisUtils.hGet("key", "nopenId")).thenReturn("result");
        when(mockRedisUtils.get("key")).thenReturn("result");

        // Run the test
        final WxStoreAdvanceOrderPriceDTO result = wxStoreAdvanceOrderServiceImplUnderTest.getPersonWxStoreAdvanceOrder(
                wxStoreAdvanceConsumerReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetPersonWxStoreAdvanceOrder_WxStoreMenuDetailsServiceReturnsTrue() {
        // Setup
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build();
        final WxStoreAdvanceOrderPriceDTO expectedResult = new WxStoreAdvanceOrderPriceDTO();
        expectedResult.setTradeOrderGuid("orderGuid");
        final WxStoreAdvanceOrderDTO wxStoreAdvanceOrderDTO = new WxStoreAdvanceOrderDTO();
        wxStoreAdvanceOrderDTO.setWxStoreAdvanceConsumerReqDTO(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build());
        final WxStoreItemRespDTO wxStoreItemRespDTO = new WxStoreItemRespDTO();
        wxStoreItemRespDTO.setItemGuid("itemGuid");
        wxStoreItemRespDTO.setItemType(0);
        wxStoreItemRespDTO.setName("itemName");
        wxStoreItemRespDTO.setIsFixPkg(0);
        final WxStoreSkuRespDTO wxStoreSkuRespDTO = new WxStoreSkuRespDTO();
        wxStoreSkuRespDTO.setSkuGuid("skuGuid");
        wxStoreSkuRespDTO.setName("name");
        wxStoreSkuRespDTO.setSalePrice(new BigDecimal("0.00"));
        wxStoreSkuRespDTO.setUserck(0);
        wxStoreItemRespDTO.setSkuList(Arrays.asList(wxStoreSkuRespDTO));
        final SubgroupSynRespDTO subgroupSynRespDTO = new SubgroupSynRespDTO();
        subgroupSynRespDTO.setPickNum(0);
        final SubItemSkuSynRespDTO subItemSkuSynRespDTO = new SubItemSkuSynRespDTO();
        subItemSkuSynRespDTO.setSkuGuid("skuGuid");
        subItemSkuSynRespDTO.setSkuName("name");
        subItemSkuSynRespDTO.setItemType(0);
        subItemSkuSynRespDTO.setItemNum(new BigDecimal("0.00"));
        subItemSkuSynRespDTO.setAddPrice(new BigDecimal("0.00"));
        subItemSkuSynRespDTO.setDefaultNum(0);
        final AttrGroupSynRespDTO attrGroupSynRespDTO = new AttrGroupSynRespDTO();
        attrGroupSynRespDTO.setIsRequired(0);
        attrGroupSynRespDTO.setWithDefault(0);
        final AttrSynRespDTO attrSynRespDTO = new AttrSynRespDTO();
        attrSynRespDTO.setPrice(new BigDecimal("0.00"));
        attrSynRespDTO.setIsDefault(0);
        attrGroupSynRespDTO.setAttrList(Arrays.asList(attrSynRespDTO));
        subItemSkuSynRespDTO.setAttrGroupList(Arrays.asList(attrGroupSynRespDTO));
        subgroupSynRespDTO.setSubItemSkuList(Arrays.asList(subItemSkuSynRespDTO));
        wxStoreItemRespDTO.setSubgroupList(Arrays.asList(subgroupSynRespDTO));
        final WxStoreAttrGroupRespDTO wxStoreAttrGroupRespDTO = new WxStoreAttrGroupRespDTO();
        final WxStoreAttrRespDTO wxStoreAttrRespDTO = new WxStoreAttrRespDTO();
        wxStoreAttrRespDTO.setPrice(new BigDecimal("0.00"));
        wxStoreAttrRespDTO.setUserck(0);
        wxStoreAttrGroupRespDTO.setAttrList(Arrays.asList(wxStoreAttrRespDTO));
        wxStoreItemRespDTO.setAttrGroupList(Arrays.asList(wxStoreAttrGroupRespDTO));
        wxStoreItemRespDTO.setCurrentCount(new BigDecimal("0.00"));
        wxStoreItemRespDTO.setItemPrice(new BigDecimal("0.00"));
        wxStoreAdvanceOrderDTO.setItemList(Arrays.asList(wxStoreItemRespDTO));
        expectedResult.setWxStoreAdvanceOrderDTOS(Arrays.asList(wxStoreAdvanceOrderDTO));
        expectedResult.setTotalPrice(new BigDecimal("0.00"));
        expectedResult.setTotalRemark("totalRemark");

        when(mockWxStoreMenuDetailsService.judgeOrderType(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build())).thenReturn(true);
        when(mockRedisUtils.keyGenerate(BusinessName.ADVANCE_ORDER)).thenReturn("key");
        when(mockRedisUtils.hGet("key", "nopenId")).thenReturn("result");
        when(mockRedisUtils.get("key")).thenReturn("result");

        // Run the test
        final WxStoreAdvanceOrderPriceDTO result = wxStoreAdvanceOrderServiceImplUnderTest.getPersonWxStoreAdvanceOrder(
                wxStoreAdvanceConsumerReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetTableWxStoreAdvanceOrder() {
        // Setup
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build();
        final WxStoreAdvanceOrderPriceDTO expectedResult = new WxStoreAdvanceOrderPriceDTO();
        expectedResult.setTradeOrderGuid("orderGuid");
        final WxStoreAdvanceOrderDTO wxStoreAdvanceOrderDTO = new WxStoreAdvanceOrderDTO();
        wxStoreAdvanceOrderDTO.setWxStoreAdvanceConsumerReqDTO(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build());
        final WxStoreItemRespDTO wxStoreItemRespDTO = new WxStoreItemRespDTO();
        wxStoreItemRespDTO.setItemGuid("itemGuid");
        wxStoreItemRespDTO.setItemType(0);
        wxStoreItemRespDTO.setName("itemName");
        wxStoreItemRespDTO.setIsFixPkg(0);
        final WxStoreSkuRespDTO wxStoreSkuRespDTO = new WxStoreSkuRespDTO();
        wxStoreSkuRespDTO.setSkuGuid("skuGuid");
        wxStoreSkuRespDTO.setName("name");
        wxStoreSkuRespDTO.setSalePrice(new BigDecimal("0.00"));
        wxStoreSkuRespDTO.setUserck(0);
        wxStoreItemRespDTO.setSkuList(Arrays.asList(wxStoreSkuRespDTO));
        final SubgroupSynRespDTO subgroupSynRespDTO = new SubgroupSynRespDTO();
        subgroupSynRespDTO.setPickNum(0);
        final SubItemSkuSynRespDTO subItemSkuSynRespDTO = new SubItemSkuSynRespDTO();
        subItemSkuSynRespDTO.setSkuGuid("skuGuid");
        subItemSkuSynRespDTO.setSkuName("name");
        subItemSkuSynRespDTO.setItemType(0);
        subItemSkuSynRespDTO.setItemNum(new BigDecimal("0.00"));
        subItemSkuSynRespDTO.setAddPrice(new BigDecimal("0.00"));
        subItemSkuSynRespDTO.setDefaultNum(0);
        final AttrGroupSynRespDTO attrGroupSynRespDTO = new AttrGroupSynRespDTO();
        attrGroupSynRespDTO.setIsRequired(0);
        attrGroupSynRespDTO.setWithDefault(0);
        final AttrSynRespDTO attrSynRespDTO = new AttrSynRespDTO();
        attrSynRespDTO.setPrice(new BigDecimal("0.00"));
        attrSynRespDTO.setIsDefault(0);
        attrGroupSynRespDTO.setAttrList(Arrays.asList(attrSynRespDTO));
        subItemSkuSynRespDTO.setAttrGroupList(Arrays.asList(attrGroupSynRespDTO));
        subgroupSynRespDTO.setSubItemSkuList(Arrays.asList(subItemSkuSynRespDTO));
        wxStoreItemRespDTO.setSubgroupList(Arrays.asList(subgroupSynRespDTO));
        final WxStoreAttrGroupRespDTO wxStoreAttrGroupRespDTO = new WxStoreAttrGroupRespDTO();
        final WxStoreAttrRespDTO wxStoreAttrRespDTO = new WxStoreAttrRespDTO();
        wxStoreAttrRespDTO.setPrice(new BigDecimal("0.00"));
        wxStoreAttrRespDTO.setUserck(0);
        wxStoreAttrGroupRespDTO.setAttrList(Arrays.asList(wxStoreAttrRespDTO));
        wxStoreItemRespDTO.setAttrGroupList(Arrays.asList(wxStoreAttrGroupRespDTO));
        wxStoreItemRespDTO.setCurrentCount(new BigDecimal("0.00"));
        wxStoreItemRespDTO.setItemPrice(new BigDecimal("0.00"));
        wxStoreAdvanceOrderDTO.setItemList(Arrays.asList(wxStoreItemRespDTO));
        expectedResult.setWxStoreAdvanceOrderDTOS(Arrays.asList(wxStoreAdvanceOrderDTO));
        expectedResult.setTotalPrice(new BigDecimal("0.00"));
        expectedResult.setTotalRemark("totalRemark");

        when(mockWxStoreMenuDetailsService.judgeOrderType(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build())).thenReturn(false);
        when(mockRedisUtils.keyGenerate(BusinessName.ADVANCE_ORDER)).thenReturn("key");

        // Configure RedisUtils.hValues(...).
        final WxStoreAdvanceOrderDTO wxStoreAdvanceOrderDTO1 = new WxStoreAdvanceOrderDTO();
        wxStoreAdvanceOrderDTO1.setWxStoreAdvanceConsumerReqDTO(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build());
        final WxStoreItemRespDTO wxStoreItemRespDTO1 = new WxStoreItemRespDTO();
        wxStoreItemRespDTO1.setItemGuid("itemGuid");
        wxStoreItemRespDTO1.setItemType(0);
        wxStoreItemRespDTO1.setName("itemName");
        wxStoreItemRespDTO1.setIsFixPkg(0);
        final WxStoreSkuRespDTO wxStoreSkuRespDTO1 = new WxStoreSkuRespDTO();
        wxStoreSkuRespDTO1.setSkuGuid("skuGuid");
        wxStoreSkuRespDTO1.setName("name");
        wxStoreSkuRespDTO1.setSalePrice(new BigDecimal("0.00"));
        wxStoreSkuRespDTO1.setUserck(0);
        wxStoreItemRespDTO1.setSkuList(Arrays.asList(wxStoreSkuRespDTO1));
        final SubgroupSynRespDTO subgroupSynRespDTO1 = new SubgroupSynRespDTO();
        subgroupSynRespDTO1.setPickNum(0);
        final SubItemSkuSynRespDTO subItemSkuSynRespDTO1 = new SubItemSkuSynRespDTO();
        subItemSkuSynRespDTO1.setSkuGuid("skuGuid");
        subItemSkuSynRespDTO1.setSkuName("name");
        subItemSkuSynRespDTO1.setItemType(0);
        subItemSkuSynRespDTO1.setItemNum(new BigDecimal("0.00"));
        subItemSkuSynRespDTO1.setAddPrice(new BigDecimal("0.00"));
        subItemSkuSynRespDTO1.setDefaultNum(0);
        final AttrGroupSynRespDTO attrGroupSynRespDTO1 = new AttrGroupSynRespDTO();
        attrGroupSynRespDTO1.setIsRequired(0);
        attrGroupSynRespDTO1.setWithDefault(0);
        final AttrSynRespDTO attrSynRespDTO1 = new AttrSynRespDTO();
        attrSynRespDTO1.setPrice(new BigDecimal("0.00"));
        attrSynRespDTO1.setIsDefault(0);
        attrGroupSynRespDTO1.setAttrList(Arrays.asList(attrSynRespDTO1));
        subItemSkuSynRespDTO1.setAttrGroupList(Arrays.asList(attrGroupSynRespDTO1));
        subgroupSynRespDTO1.setSubItemSkuList(Arrays.asList(subItemSkuSynRespDTO1));
        wxStoreItemRespDTO1.setSubgroupList(Arrays.asList(subgroupSynRespDTO1));
        final WxStoreAttrGroupRespDTO wxStoreAttrGroupRespDTO1 = new WxStoreAttrGroupRespDTO();
        final WxStoreAttrRespDTO wxStoreAttrRespDTO1 = new WxStoreAttrRespDTO();
        wxStoreAttrRespDTO1.setPrice(new BigDecimal("0.00"));
        wxStoreAttrRespDTO1.setUserck(0);
        wxStoreAttrGroupRespDTO1.setAttrList(Arrays.asList(wxStoreAttrRespDTO1));
        wxStoreItemRespDTO1.setAttrGroupList(Arrays.asList(wxStoreAttrGroupRespDTO1));
        wxStoreItemRespDTO1.setCurrentCount(new BigDecimal("0.00"));
        wxStoreItemRespDTO1.setItemPrice(new BigDecimal("0.00"));
        wxStoreAdvanceOrderDTO1.setItemList(Arrays.asList(wxStoreItemRespDTO1));
        when(mockRedisUtils.hValues("key")).thenReturn(Lists.newArrayList(wxStoreAdvanceOrderDTO1));

        when(mockRedisUtils.get("key")).thenReturn("result");
        when(mockWxStoreSessionDetailsService.getDinnerGuestsCount("diningTableGuid")).thenReturn(0);

        // Run the test
        final WxStoreAdvanceOrderPriceDTO result = wxStoreAdvanceOrderServiceImplUnderTest.getTableWxStoreAdvanceOrder(
                wxStoreAdvanceConsumerReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetTableWxStoreAdvanceOrder_WxStoreMenuDetailsServiceReturnsTrue() {
        // Setup
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build();
        final WxStoreAdvanceOrderPriceDTO expectedResult = new WxStoreAdvanceOrderPriceDTO();
        expectedResult.setTradeOrderGuid("orderGuid");
        final WxStoreAdvanceOrderDTO wxStoreAdvanceOrderDTO = new WxStoreAdvanceOrderDTO();
        wxStoreAdvanceOrderDTO.setWxStoreAdvanceConsumerReqDTO(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build());
        final WxStoreItemRespDTO wxStoreItemRespDTO = new WxStoreItemRespDTO();
        wxStoreItemRespDTO.setItemGuid("itemGuid");
        wxStoreItemRespDTO.setItemType(0);
        wxStoreItemRespDTO.setName("itemName");
        wxStoreItemRespDTO.setIsFixPkg(0);
        final WxStoreSkuRespDTO wxStoreSkuRespDTO = new WxStoreSkuRespDTO();
        wxStoreSkuRespDTO.setSkuGuid("skuGuid");
        wxStoreSkuRespDTO.setName("name");
        wxStoreSkuRespDTO.setSalePrice(new BigDecimal("0.00"));
        wxStoreSkuRespDTO.setUserck(0);
        wxStoreItemRespDTO.setSkuList(Arrays.asList(wxStoreSkuRespDTO));
        final SubgroupSynRespDTO subgroupSynRespDTO = new SubgroupSynRespDTO();
        subgroupSynRespDTO.setPickNum(0);
        final SubItemSkuSynRespDTO subItemSkuSynRespDTO = new SubItemSkuSynRespDTO();
        subItemSkuSynRespDTO.setSkuGuid("skuGuid");
        subItemSkuSynRespDTO.setSkuName("name");
        subItemSkuSynRespDTO.setItemType(0);
        subItemSkuSynRespDTO.setItemNum(new BigDecimal("0.00"));
        subItemSkuSynRespDTO.setAddPrice(new BigDecimal("0.00"));
        subItemSkuSynRespDTO.setDefaultNum(0);
        final AttrGroupSynRespDTO attrGroupSynRespDTO = new AttrGroupSynRespDTO();
        attrGroupSynRespDTO.setIsRequired(0);
        attrGroupSynRespDTO.setWithDefault(0);
        final AttrSynRespDTO attrSynRespDTO = new AttrSynRespDTO();
        attrSynRespDTO.setPrice(new BigDecimal("0.00"));
        attrSynRespDTO.setIsDefault(0);
        attrGroupSynRespDTO.setAttrList(Arrays.asList(attrSynRespDTO));
        subItemSkuSynRespDTO.setAttrGroupList(Arrays.asList(attrGroupSynRespDTO));
        subgroupSynRespDTO.setSubItemSkuList(Arrays.asList(subItemSkuSynRespDTO));
        wxStoreItemRespDTO.setSubgroupList(Arrays.asList(subgroupSynRespDTO));
        final WxStoreAttrGroupRespDTO wxStoreAttrGroupRespDTO = new WxStoreAttrGroupRespDTO();
        final WxStoreAttrRespDTO wxStoreAttrRespDTO = new WxStoreAttrRespDTO();
        wxStoreAttrRespDTO.setPrice(new BigDecimal("0.00"));
        wxStoreAttrRespDTO.setUserck(0);
        wxStoreAttrGroupRespDTO.setAttrList(Arrays.asList(wxStoreAttrRespDTO));
        wxStoreItemRespDTO.setAttrGroupList(Arrays.asList(wxStoreAttrGroupRespDTO));
        wxStoreItemRespDTO.setCurrentCount(new BigDecimal("0.00"));
        wxStoreItemRespDTO.setItemPrice(new BigDecimal("0.00"));
        wxStoreAdvanceOrderDTO.setItemList(Arrays.asList(wxStoreItemRespDTO));
        expectedResult.setWxStoreAdvanceOrderDTOS(Arrays.asList(wxStoreAdvanceOrderDTO));
        expectedResult.setTotalPrice(new BigDecimal("0.00"));
        expectedResult.setTotalRemark("totalRemark");

        when(mockWxStoreMenuDetailsService.judgeOrderType(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build())).thenReturn(true);
        when(mockRedisUtils.keyGenerate(BusinessName.ADVANCE_ORDER)).thenReturn("key");

        // Configure RedisUtils.hValues(...).
        final WxStoreAdvanceOrderDTO wxStoreAdvanceOrderDTO1 = new WxStoreAdvanceOrderDTO();
        wxStoreAdvanceOrderDTO1.setWxStoreAdvanceConsumerReqDTO(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build());
        final WxStoreItemRespDTO wxStoreItemRespDTO1 = new WxStoreItemRespDTO();
        wxStoreItemRespDTO1.setItemGuid("itemGuid");
        wxStoreItemRespDTO1.setItemType(0);
        wxStoreItemRespDTO1.setName("itemName");
        wxStoreItemRespDTO1.setIsFixPkg(0);
        final WxStoreSkuRespDTO wxStoreSkuRespDTO1 = new WxStoreSkuRespDTO();
        wxStoreSkuRespDTO1.setSkuGuid("skuGuid");
        wxStoreSkuRespDTO1.setName("name");
        wxStoreSkuRespDTO1.setSalePrice(new BigDecimal("0.00"));
        wxStoreSkuRespDTO1.setUserck(0);
        wxStoreItemRespDTO1.setSkuList(Arrays.asList(wxStoreSkuRespDTO1));
        final SubgroupSynRespDTO subgroupSynRespDTO1 = new SubgroupSynRespDTO();
        subgroupSynRespDTO1.setPickNum(0);
        final SubItemSkuSynRespDTO subItemSkuSynRespDTO1 = new SubItemSkuSynRespDTO();
        subItemSkuSynRespDTO1.setSkuGuid("skuGuid");
        subItemSkuSynRespDTO1.setSkuName("name");
        subItemSkuSynRespDTO1.setItemType(0);
        subItemSkuSynRespDTO1.setItemNum(new BigDecimal("0.00"));
        subItemSkuSynRespDTO1.setAddPrice(new BigDecimal("0.00"));
        subItemSkuSynRespDTO1.setDefaultNum(0);
        final AttrGroupSynRespDTO attrGroupSynRespDTO1 = new AttrGroupSynRespDTO();
        attrGroupSynRespDTO1.setIsRequired(0);
        attrGroupSynRespDTO1.setWithDefault(0);
        final AttrSynRespDTO attrSynRespDTO1 = new AttrSynRespDTO();
        attrSynRespDTO1.setPrice(new BigDecimal("0.00"));
        attrSynRespDTO1.setIsDefault(0);
        attrGroupSynRespDTO1.setAttrList(Arrays.asList(attrSynRespDTO1));
        subItemSkuSynRespDTO1.setAttrGroupList(Arrays.asList(attrGroupSynRespDTO1));
        subgroupSynRespDTO1.setSubItemSkuList(Arrays.asList(subItemSkuSynRespDTO1));
        wxStoreItemRespDTO1.setSubgroupList(Arrays.asList(subgroupSynRespDTO1));
        final WxStoreAttrGroupRespDTO wxStoreAttrGroupRespDTO1 = new WxStoreAttrGroupRespDTO();
        final WxStoreAttrRespDTO wxStoreAttrRespDTO1 = new WxStoreAttrRespDTO();
        wxStoreAttrRespDTO1.setPrice(new BigDecimal("0.00"));
        wxStoreAttrRespDTO1.setUserck(0);
        wxStoreAttrGroupRespDTO1.setAttrList(Arrays.asList(wxStoreAttrRespDTO1));
        wxStoreItemRespDTO1.setAttrGroupList(Arrays.asList(wxStoreAttrGroupRespDTO1));
        wxStoreItemRespDTO1.setCurrentCount(new BigDecimal("0.00"));
        wxStoreItemRespDTO1.setItemPrice(new BigDecimal("0.00"));
        wxStoreAdvanceOrderDTO1.setItemList(Arrays.asList(wxStoreItemRespDTO1));
        when(mockRedisUtils.hValues("key")).thenReturn(Lists.newArrayList(wxStoreAdvanceOrderDTO1));

        when(mockRedisUtils.get("key")).thenReturn("result");
        when(mockWxStoreSessionDetailsService.getDinnerGuestsCount("diningTableGuid")).thenReturn(0);

        // Run the test
        final WxStoreAdvanceOrderPriceDTO result = wxStoreAdvanceOrderServiceImplUnderTest.getTableWxStoreAdvanceOrder(
                wxStoreAdvanceConsumerReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetTableWxStoreAdvanceOrder_RedisUtilsHValuesReturnsNoItems() {
        // Setup
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build();
        final WxStoreAdvanceOrderPriceDTO expectedResult = new WxStoreAdvanceOrderPriceDTO();
        expectedResult.setTradeOrderGuid("orderGuid");
        final WxStoreAdvanceOrderDTO wxStoreAdvanceOrderDTO = new WxStoreAdvanceOrderDTO();
        wxStoreAdvanceOrderDTO.setWxStoreAdvanceConsumerReqDTO(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build());
        final WxStoreItemRespDTO wxStoreItemRespDTO = new WxStoreItemRespDTO();
        wxStoreItemRespDTO.setItemGuid("itemGuid");
        wxStoreItemRespDTO.setItemType(0);
        wxStoreItemRespDTO.setName("itemName");
        wxStoreItemRespDTO.setIsFixPkg(0);
        final WxStoreSkuRespDTO wxStoreSkuRespDTO = new WxStoreSkuRespDTO();
        wxStoreSkuRespDTO.setSkuGuid("skuGuid");
        wxStoreSkuRespDTO.setName("name");
        wxStoreSkuRespDTO.setSalePrice(new BigDecimal("0.00"));
        wxStoreSkuRespDTO.setUserck(0);
        wxStoreItemRespDTO.setSkuList(Arrays.asList(wxStoreSkuRespDTO));
        final SubgroupSynRespDTO subgroupSynRespDTO = new SubgroupSynRespDTO();
        subgroupSynRespDTO.setPickNum(0);
        final SubItemSkuSynRespDTO subItemSkuSynRespDTO = new SubItemSkuSynRespDTO();
        subItemSkuSynRespDTO.setSkuGuid("skuGuid");
        subItemSkuSynRespDTO.setSkuName("name");
        subItemSkuSynRespDTO.setItemType(0);
        subItemSkuSynRespDTO.setItemNum(new BigDecimal("0.00"));
        subItemSkuSynRespDTO.setAddPrice(new BigDecimal("0.00"));
        subItemSkuSynRespDTO.setDefaultNum(0);
        final AttrGroupSynRespDTO attrGroupSynRespDTO = new AttrGroupSynRespDTO();
        attrGroupSynRespDTO.setIsRequired(0);
        attrGroupSynRespDTO.setWithDefault(0);
        final AttrSynRespDTO attrSynRespDTO = new AttrSynRespDTO();
        attrSynRespDTO.setPrice(new BigDecimal("0.00"));
        attrSynRespDTO.setIsDefault(0);
        attrGroupSynRespDTO.setAttrList(Arrays.asList(attrSynRespDTO));
        subItemSkuSynRespDTO.setAttrGroupList(Arrays.asList(attrGroupSynRespDTO));
        subgroupSynRespDTO.setSubItemSkuList(Arrays.asList(subItemSkuSynRespDTO));
        wxStoreItemRespDTO.setSubgroupList(Arrays.asList(subgroupSynRespDTO));
        final WxStoreAttrGroupRespDTO wxStoreAttrGroupRespDTO = new WxStoreAttrGroupRespDTO();
        final WxStoreAttrRespDTO wxStoreAttrRespDTO = new WxStoreAttrRespDTO();
        wxStoreAttrRespDTO.setPrice(new BigDecimal("0.00"));
        wxStoreAttrRespDTO.setUserck(0);
        wxStoreAttrGroupRespDTO.setAttrList(Arrays.asList(wxStoreAttrRespDTO));
        wxStoreItemRespDTO.setAttrGroupList(Arrays.asList(wxStoreAttrGroupRespDTO));
        wxStoreItemRespDTO.setCurrentCount(new BigDecimal("0.00"));
        wxStoreItemRespDTO.setItemPrice(new BigDecimal("0.00"));
        wxStoreAdvanceOrderDTO.setItemList(Arrays.asList(wxStoreItemRespDTO));
        expectedResult.setWxStoreAdvanceOrderDTOS(Arrays.asList(wxStoreAdvanceOrderDTO));
        expectedResult.setTotalPrice(new BigDecimal("0.00"));
        expectedResult.setTotalRemark("totalRemark");

        when(mockWxStoreMenuDetailsService.judgeOrderType(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build())).thenReturn(false);
        when(mockRedisUtils.keyGenerate(BusinessName.ADVANCE_ORDER)).thenReturn("key");
        when(mockRedisUtils.hValues("key")).thenReturn(Collections.emptyList());
        when(mockRedisUtils.get("key")).thenReturn("result");
        when(mockWxStoreSessionDetailsService.getDinnerGuestsCount("diningTableGuid")).thenReturn(0);

        // Run the test
        final WxStoreAdvanceOrderPriceDTO result = wxStoreAdvanceOrderServiceImplUnderTest.getTableWxStoreAdvanceOrder(
                wxStoreAdvanceConsumerReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetTableWxStoreAdvanceOrders() {
        // Setup
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build();
        final WxStoreAdvanceOrderDTO wxStoreAdvanceOrderDTO = new WxStoreAdvanceOrderDTO();
        wxStoreAdvanceOrderDTO.setWxStoreAdvanceConsumerReqDTO(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build());
        final WxStoreItemRespDTO wxStoreItemRespDTO = new WxStoreItemRespDTO();
        wxStoreItemRespDTO.setItemGuid("itemGuid");
        wxStoreItemRespDTO.setItemType(0);
        wxStoreItemRespDTO.setName("itemName");
        wxStoreItemRespDTO.setIsFixPkg(0);
        final WxStoreSkuRespDTO wxStoreSkuRespDTO = new WxStoreSkuRespDTO();
        wxStoreSkuRespDTO.setSkuGuid("skuGuid");
        wxStoreSkuRespDTO.setName("name");
        wxStoreSkuRespDTO.setSalePrice(new BigDecimal("0.00"));
        wxStoreSkuRespDTO.setUserck(0);
        wxStoreItemRespDTO.setSkuList(Arrays.asList(wxStoreSkuRespDTO));
        final SubgroupSynRespDTO subgroupSynRespDTO = new SubgroupSynRespDTO();
        subgroupSynRespDTO.setPickNum(0);
        final SubItemSkuSynRespDTO subItemSkuSynRespDTO = new SubItemSkuSynRespDTO();
        subItemSkuSynRespDTO.setSkuGuid("skuGuid");
        subItemSkuSynRespDTO.setSkuName("name");
        subItemSkuSynRespDTO.setItemType(0);
        subItemSkuSynRespDTO.setItemNum(new BigDecimal("0.00"));
        subItemSkuSynRespDTO.setAddPrice(new BigDecimal("0.00"));
        subItemSkuSynRespDTO.setDefaultNum(0);
        final AttrGroupSynRespDTO attrGroupSynRespDTO = new AttrGroupSynRespDTO();
        attrGroupSynRespDTO.setIsRequired(0);
        attrGroupSynRespDTO.setWithDefault(0);
        final AttrSynRespDTO attrSynRespDTO = new AttrSynRespDTO();
        attrSynRespDTO.setPrice(new BigDecimal("0.00"));
        attrSynRespDTO.setIsDefault(0);
        attrGroupSynRespDTO.setAttrList(Arrays.asList(attrSynRespDTO));
        subItemSkuSynRespDTO.setAttrGroupList(Arrays.asList(attrGroupSynRespDTO));
        subgroupSynRespDTO.setSubItemSkuList(Arrays.asList(subItemSkuSynRespDTO));
        wxStoreItemRespDTO.setSubgroupList(Arrays.asList(subgroupSynRespDTO));
        final WxStoreAttrGroupRespDTO wxStoreAttrGroupRespDTO = new WxStoreAttrGroupRespDTO();
        final WxStoreAttrRespDTO wxStoreAttrRespDTO = new WxStoreAttrRespDTO();
        wxStoreAttrRespDTO.setPrice(new BigDecimal("0.00"));
        wxStoreAttrRespDTO.setUserck(0);
        wxStoreAttrGroupRespDTO.setAttrList(Arrays.asList(wxStoreAttrRespDTO));
        wxStoreItemRespDTO.setAttrGroupList(Arrays.asList(wxStoreAttrGroupRespDTO));
        wxStoreItemRespDTO.setCurrentCount(new BigDecimal("0.00"));
        wxStoreItemRespDTO.setItemPrice(new BigDecimal("0.00"));
        wxStoreAdvanceOrderDTO.setItemList(Arrays.asList(wxStoreItemRespDTO));
        final List<WxStoreAdvanceOrderDTO> expectedResult = Arrays.asList(wxStoreAdvanceOrderDTO);
        when(mockWxStoreMenuDetailsService.judgeOrderType(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build())).thenReturn(false);
        when(mockRedisUtils.keyGenerate(BusinessName.ADVANCE_ORDER)).thenReturn("key");

        // Configure RedisUtils.hValues(...).
        final WxStoreAdvanceOrderDTO wxStoreAdvanceOrderDTO1 = new WxStoreAdvanceOrderDTO();
        wxStoreAdvanceOrderDTO1.setWxStoreAdvanceConsumerReqDTO(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build());
        final WxStoreItemRespDTO wxStoreItemRespDTO1 = new WxStoreItemRespDTO();
        wxStoreItemRespDTO1.setItemGuid("itemGuid");
        wxStoreItemRespDTO1.setItemType(0);
        wxStoreItemRespDTO1.setName("itemName");
        wxStoreItemRespDTO1.setIsFixPkg(0);
        final WxStoreSkuRespDTO wxStoreSkuRespDTO1 = new WxStoreSkuRespDTO();
        wxStoreSkuRespDTO1.setSkuGuid("skuGuid");
        wxStoreSkuRespDTO1.setName("name");
        wxStoreSkuRespDTO1.setSalePrice(new BigDecimal("0.00"));
        wxStoreSkuRespDTO1.setUserck(0);
        wxStoreItemRespDTO1.setSkuList(Arrays.asList(wxStoreSkuRespDTO1));
        final SubgroupSynRespDTO subgroupSynRespDTO1 = new SubgroupSynRespDTO();
        subgroupSynRespDTO1.setPickNum(0);
        final SubItemSkuSynRespDTO subItemSkuSynRespDTO1 = new SubItemSkuSynRespDTO();
        subItemSkuSynRespDTO1.setSkuGuid("skuGuid");
        subItemSkuSynRespDTO1.setSkuName("name");
        subItemSkuSynRespDTO1.setItemType(0);
        subItemSkuSynRespDTO1.setItemNum(new BigDecimal("0.00"));
        subItemSkuSynRespDTO1.setAddPrice(new BigDecimal("0.00"));
        subItemSkuSynRespDTO1.setDefaultNum(0);
        final AttrGroupSynRespDTO attrGroupSynRespDTO1 = new AttrGroupSynRespDTO();
        attrGroupSynRespDTO1.setIsRequired(0);
        attrGroupSynRespDTO1.setWithDefault(0);
        final AttrSynRespDTO attrSynRespDTO1 = new AttrSynRespDTO();
        attrSynRespDTO1.setPrice(new BigDecimal("0.00"));
        attrSynRespDTO1.setIsDefault(0);
        attrGroupSynRespDTO1.setAttrList(Arrays.asList(attrSynRespDTO1));
        subItemSkuSynRespDTO1.setAttrGroupList(Arrays.asList(attrGroupSynRespDTO1));
        subgroupSynRespDTO1.setSubItemSkuList(Arrays.asList(subItemSkuSynRespDTO1));
        wxStoreItemRespDTO1.setSubgroupList(Arrays.asList(subgroupSynRespDTO1));
        final WxStoreAttrGroupRespDTO wxStoreAttrGroupRespDTO1 = new WxStoreAttrGroupRespDTO();
        final WxStoreAttrRespDTO wxStoreAttrRespDTO1 = new WxStoreAttrRespDTO();
        wxStoreAttrRespDTO1.setPrice(new BigDecimal("0.00"));
        wxStoreAttrRespDTO1.setUserck(0);
        wxStoreAttrGroupRespDTO1.setAttrList(Arrays.asList(wxStoreAttrRespDTO1));
        wxStoreItemRespDTO1.setAttrGroupList(Arrays.asList(wxStoreAttrGroupRespDTO1));
        wxStoreItemRespDTO1.setCurrentCount(new BigDecimal("0.00"));
        wxStoreItemRespDTO1.setItemPrice(new BigDecimal("0.00"));
        wxStoreAdvanceOrderDTO1.setItemList(Arrays.asList(wxStoreItemRespDTO1));
        final List<WxStoreAdvanceOrderDTO> wxStoreAdvanceOrderDTOS = Arrays.asList();
        when(mockRedisUtils.hValues("key")).thenReturn(Lists.newArrayList(wxStoreAdvanceOrderDTO1));

        when(mockRedisUtils.get("key")).thenReturn("result");

        // Run the test
        final List<WxStoreAdvanceOrderDTO> result = wxStoreAdvanceOrderServiceImplUnderTest.getTableWxStoreAdvanceOrders(
                wxStoreAdvanceConsumerReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetTableWxStoreAdvanceOrders_WxStoreMenuDetailsServiceReturnsTrue() {
        // Setup
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build();
        final WxStoreAdvanceOrderDTO wxStoreAdvanceOrderDTO = new WxStoreAdvanceOrderDTO();
        wxStoreAdvanceOrderDTO.setWxStoreAdvanceConsumerReqDTO(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build());
        final WxStoreItemRespDTO wxStoreItemRespDTO = new WxStoreItemRespDTO();
        wxStoreItemRespDTO.setItemGuid("itemGuid");
        wxStoreItemRespDTO.setItemType(0);
        wxStoreItemRespDTO.setName("itemName");
        wxStoreItemRespDTO.setIsFixPkg(0);
        final WxStoreSkuRespDTO wxStoreSkuRespDTO = new WxStoreSkuRespDTO();
        wxStoreSkuRespDTO.setSkuGuid("skuGuid");
        wxStoreSkuRespDTO.setName("name");
        wxStoreSkuRespDTO.setSalePrice(new BigDecimal("0.00"));
        wxStoreSkuRespDTO.setUserck(0);
        wxStoreItemRespDTO.setSkuList(Arrays.asList(wxStoreSkuRespDTO));
        final SubgroupSynRespDTO subgroupSynRespDTO = new SubgroupSynRespDTO();
        subgroupSynRespDTO.setPickNum(0);
        final SubItemSkuSynRespDTO subItemSkuSynRespDTO = new SubItemSkuSynRespDTO();
        subItemSkuSynRespDTO.setSkuGuid("skuGuid");
        subItemSkuSynRespDTO.setSkuName("name");
        subItemSkuSynRespDTO.setItemType(0);
        subItemSkuSynRespDTO.setItemNum(new BigDecimal("0.00"));
        subItemSkuSynRespDTO.setAddPrice(new BigDecimal("0.00"));
        subItemSkuSynRespDTO.setDefaultNum(0);
        final AttrGroupSynRespDTO attrGroupSynRespDTO = new AttrGroupSynRespDTO();
        attrGroupSynRespDTO.setIsRequired(0);
        attrGroupSynRespDTO.setWithDefault(0);
        final AttrSynRespDTO attrSynRespDTO = new AttrSynRespDTO();
        attrSynRespDTO.setPrice(new BigDecimal("0.00"));
        attrSynRespDTO.setIsDefault(0);
        attrGroupSynRespDTO.setAttrList(Arrays.asList(attrSynRespDTO));
        subItemSkuSynRespDTO.setAttrGroupList(Arrays.asList(attrGroupSynRespDTO));
        subgroupSynRespDTO.setSubItemSkuList(Arrays.asList(subItemSkuSynRespDTO));
        wxStoreItemRespDTO.setSubgroupList(Arrays.asList(subgroupSynRespDTO));
        final WxStoreAttrGroupRespDTO wxStoreAttrGroupRespDTO = new WxStoreAttrGroupRespDTO();
        final WxStoreAttrRespDTO wxStoreAttrRespDTO = new WxStoreAttrRespDTO();
        wxStoreAttrRespDTO.setPrice(new BigDecimal("0.00"));
        wxStoreAttrRespDTO.setUserck(0);
        wxStoreAttrGroupRespDTO.setAttrList(Arrays.asList(wxStoreAttrRespDTO));
        wxStoreItemRespDTO.setAttrGroupList(Arrays.asList(wxStoreAttrGroupRespDTO));
        wxStoreItemRespDTO.setCurrentCount(new BigDecimal("0.00"));
        wxStoreItemRespDTO.setItemPrice(new BigDecimal("0.00"));
        wxStoreAdvanceOrderDTO.setItemList(Arrays.asList(wxStoreItemRespDTO));
        final List<WxStoreAdvanceOrderDTO> expectedResult = Arrays.asList(wxStoreAdvanceOrderDTO);
        when(mockWxStoreMenuDetailsService.judgeOrderType(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build())).thenReturn(true);
        when(mockRedisUtils.keyGenerate(BusinessName.ADVANCE_ORDER)).thenReturn("key");

        // Configure RedisUtils.hValues(...).
        final WxStoreAdvanceOrderDTO wxStoreAdvanceOrderDTO1 = new WxStoreAdvanceOrderDTO();
        wxStoreAdvanceOrderDTO1.setWxStoreAdvanceConsumerReqDTO(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build());
        final WxStoreItemRespDTO wxStoreItemRespDTO1 = new WxStoreItemRespDTO();
        wxStoreItemRespDTO1.setItemGuid("itemGuid");
        wxStoreItemRespDTO1.setItemType(0);
        wxStoreItemRespDTO1.setName("itemName");
        wxStoreItemRespDTO1.setIsFixPkg(0);
        final WxStoreSkuRespDTO wxStoreSkuRespDTO1 = new WxStoreSkuRespDTO();
        wxStoreSkuRespDTO1.setSkuGuid("skuGuid");
        wxStoreSkuRespDTO1.setName("name");
        wxStoreSkuRespDTO1.setSalePrice(new BigDecimal("0.00"));
        wxStoreSkuRespDTO1.setUserck(0);
        wxStoreItemRespDTO1.setSkuList(Arrays.asList(wxStoreSkuRespDTO1));
        final SubgroupSynRespDTO subgroupSynRespDTO1 = new SubgroupSynRespDTO();
        subgroupSynRespDTO1.setPickNum(0);
        final SubItemSkuSynRespDTO subItemSkuSynRespDTO1 = new SubItemSkuSynRespDTO();
        subItemSkuSynRespDTO1.setSkuGuid("skuGuid");
        subItemSkuSynRespDTO1.setSkuName("name");
        subItemSkuSynRespDTO1.setItemType(0);
        subItemSkuSynRespDTO1.setItemNum(new BigDecimal("0.00"));
        subItemSkuSynRespDTO1.setAddPrice(new BigDecimal("0.00"));
        subItemSkuSynRespDTO1.setDefaultNum(0);
        final AttrGroupSynRespDTO attrGroupSynRespDTO1 = new AttrGroupSynRespDTO();
        attrGroupSynRespDTO1.setIsRequired(0);
        attrGroupSynRespDTO1.setWithDefault(0);
        final AttrSynRespDTO attrSynRespDTO1 = new AttrSynRespDTO();
        attrSynRespDTO1.setPrice(new BigDecimal("0.00"));
        attrSynRespDTO1.setIsDefault(0);
        attrGroupSynRespDTO1.setAttrList(Arrays.asList(attrSynRespDTO1));
        subItemSkuSynRespDTO1.setAttrGroupList(Arrays.asList(attrGroupSynRespDTO1));
        subgroupSynRespDTO1.setSubItemSkuList(Arrays.asList(subItemSkuSynRespDTO1));
        wxStoreItemRespDTO1.setSubgroupList(Arrays.asList(subgroupSynRespDTO1));
        final WxStoreAttrGroupRespDTO wxStoreAttrGroupRespDTO1 = new WxStoreAttrGroupRespDTO();
        final WxStoreAttrRespDTO wxStoreAttrRespDTO1 = new WxStoreAttrRespDTO();
        wxStoreAttrRespDTO1.setPrice(new BigDecimal("0.00"));
        wxStoreAttrRespDTO1.setUserck(0);
        wxStoreAttrGroupRespDTO1.setAttrList(Arrays.asList(wxStoreAttrRespDTO1));
        wxStoreItemRespDTO1.setAttrGroupList(Arrays.asList(wxStoreAttrGroupRespDTO1));
        wxStoreItemRespDTO1.setCurrentCount(new BigDecimal("0.00"));
        wxStoreItemRespDTO1.setItemPrice(new BigDecimal("0.00"));
        wxStoreAdvanceOrderDTO1.setItemList(Arrays.asList(wxStoreItemRespDTO1));
        when(mockRedisUtils.hValues("key")).thenReturn(Lists.newArrayList(wxStoreAdvanceOrderDTO1));

        when(mockRedisUtils.get("key")).thenReturn("result");

        // Run the test
        final List<WxStoreAdvanceOrderDTO> result = wxStoreAdvanceOrderServiceImplUnderTest.getTableWxStoreAdvanceOrders(
                wxStoreAdvanceConsumerReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetTableWxStoreAdvanceOrders_RedisUtilsHValuesReturnsNoItems() {
        // Setup
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build();
        when(mockWxStoreMenuDetailsService.judgeOrderType(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build())).thenReturn(false);
        when(mockRedisUtils.keyGenerate(BusinessName.ADVANCE_ORDER)).thenReturn("key");
        when(mockRedisUtils.hValues("key")).thenReturn(Collections.emptyList());

        // Run the test
        final List<WxStoreAdvanceOrderDTO> result = wxStoreAdvanceOrderServiceImplUnderTest.getTableWxStoreAdvanceOrders(
                wxStoreAdvanceConsumerReqDTO);

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testGetSingleWxStoreAdvanceOrder() {
        // Setup
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build();
        final WxStoreAdvanceOrderDTO expectedResult = new WxStoreAdvanceOrderDTO();
        expectedResult.setWxStoreAdvanceConsumerReqDTO(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build());
        final WxStoreItemRespDTO wxStoreItemRespDTO = new WxStoreItemRespDTO();
        wxStoreItemRespDTO.setItemGuid("itemGuid");
        wxStoreItemRespDTO.setItemType(0);
        wxStoreItemRespDTO.setName("itemName");
        wxStoreItemRespDTO.setIsFixPkg(0);
        final WxStoreSkuRespDTO wxStoreSkuRespDTO = new WxStoreSkuRespDTO();
        wxStoreSkuRespDTO.setSkuGuid("skuGuid");
        wxStoreSkuRespDTO.setName("name");
        wxStoreSkuRespDTO.setSalePrice(new BigDecimal("0.00"));
        wxStoreSkuRespDTO.setUserck(0);
        wxStoreItemRespDTO.setSkuList(Arrays.asList(wxStoreSkuRespDTO));
        final SubgroupSynRespDTO subgroupSynRespDTO = new SubgroupSynRespDTO();
        subgroupSynRespDTO.setPickNum(0);
        final SubItemSkuSynRespDTO subItemSkuSynRespDTO = new SubItemSkuSynRespDTO();
        subItemSkuSynRespDTO.setSkuGuid("skuGuid");
        subItemSkuSynRespDTO.setSkuName("name");
        subItemSkuSynRespDTO.setItemType(0);
        subItemSkuSynRespDTO.setItemNum(new BigDecimal("0.00"));
        subItemSkuSynRespDTO.setAddPrice(new BigDecimal("0.00"));
        subItemSkuSynRespDTO.setDefaultNum(0);
        final AttrGroupSynRespDTO attrGroupSynRespDTO = new AttrGroupSynRespDTO();
        attrGroupSynRespDTO.setIsRequired(0);
        attrGroupSynRespDTO.setWithDefault(0);
        final AttrSynRespDTO attrSynRespDTO = new AttrSynRespDTO();
        attrSynRespDTO.setPrice(new BigDecimal("0.00"));
        attrSynRespDTO.setIsDefault(0);
        attrGroupSynRespDTO.setAttrList(Arrays.asList(attrSynRespDTO));
        subItemSkuSynRespDTO.setAttrGroupList(Arrays.asList(attrGroupSynRespDTO));
        subgroupSynRespDTO.setSubItemSkuList(Arrays.asList(subItemSkuSynRespDTO));
        wxStoreItemRespDTO.setSubgroupList(Arrays.asList(subgroupSynRespDTO));
        final WxStoreAttrGroupRespDTO wxStoreAttrGroupRespDTO = new WxStoreAttrGroupRespDTO();
        final WxStoreAttrRespDTO wxStoreAttrRespDTO = new WxStoreAttrRespDTO();
        wxStoreAttrRespDTO.setPrice(new BigDecimal("0.00"));
        wxStoreAttrRespDTO.setUserck(0);
        wxStoreAttrGroupRespDTO.setAttrList(Arrays.asList(wxStoreAttrRespDTO));
        wxStoreItemRespDTO.setAttrGroupList(Arrays.asList(wxStoreAttrGroupRespDTO));
        wxStoreItemRespDTO.setCurrentCount(new BigDecimal("0.00"));
        wxStoreItemRespDTO.setItemPrice(new BigDecimal("0.00"));
        expectedResult.setItemList(Arrays.asList(wxStoreItemRespDTO));

        when(mockWxStoreMenuDetailsService.judgeOrderType(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build())).thenReturn(false);
        when(mockRedisUtils.keyGenerate(BusinessName.ADVANCE_ORDER)).thenReturn("key");

        // Configure RedisUtils.hValues(...).
        final WxStoreAdvanceOrderDTO wxStoreAdvanceOrderDTO = new WxStoreAdvanceOrderDTO();
        wxStoreAdvanceOrderDTO.setWxStoreAdvanceConsumerReqDTO(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build());
        final WxStoreItemRespDTO wxStoreItemRespDTO1 = new WxStoreItemRespDTO();
        wxStoreItemRespDTO1.setItemGuid("itemGuid");
        wxStoreItemRespDTO1.setItemType(0);
        wxStoreItemRespDTO1.setName("itemName");
        wxStoreItemRespDTO1.setIsFixPkg(0);
        final WxStoreSkuRespDTO wxStoreSkuRespDTO1 = new WxStoreSkuRespDTO();
        wxStoreSkuRespDTO1.setSkuGuid("skuGuid");
        wxStoreSkuRespDTO1.setName("name");
        wxStoreSkuRespDTO1.setSalePrice(new BigDecimal("0.00"));
        wxStoreSkuRespDTO1.setUserck(0);
        wxStoreItemRespDTO1.setSkuList(Arrays.asList(wxStoreSkuRespDTO1));
        final SubgroupSynRespDTO subgroupSynRespDTO1 = new SubgroupSynRespDTO();
        subgroupSynRespDTO1.setPickNum(0);
        final SubItemSkuSynRespDTO subItemSkuSynRespDTO1 = new SubItemSkuSynRespDTO();
        subItemSkuSynRespDTO1.setSkuGuid("skuGuid");
        subItemSkuSynRespDTO1.setSkuName("name");
        subItemSkuSynRespDTO1.setItemType(0);
        subItemSkuSynRespDTO1.setItemNum(new BigDecimal("0.00"));
        subItemSkuSynRespDTO1.setAddPrice(new BigDecimal("0.00"));
        subItemSkuSynRespDTO1.setDefaultNum(0);
        final AttrGroupSynRespDTO attrGroupSynRespDTO1 = new AttrGroupSynRespDTO();
        attrGroupSynRespDTO1.setIsRequired(0);
        attrGroupSynRespDTO1.setWithDefault(0);
        final AttrSynRespDTO attrSynRespDTO1 = new AttrSynRespDTO();
        attrSynRespDTO1.setPrice(new BigDecimal("0.00"));
        attrSynRespDTO1.setIsDefault(0);
        attrGroupSynRespDTO1.setAttrList(Arrays.asList(attrSynRespDTO1));
        subItemSkuSynRespDTO1.setAttrGroupList(Arrays.asList(attrGroupSynRespDTO1));
        subgroupSynRespDTO1.setSubItemSkuList(Arrays.asList(subItemSkuSynRespDTO1));
        wxStoreItemRespDTO1.setSubgroupList(Arrays.asList(subgroupSynRespDTO1));
        final WxStoreAttrGroupRespDTO wxStoreAttrGroupRespDTO1 = new WxStoreAttrGroupRespDTO();
        final WxStoreAttrRespDTO wxStoreAttrRespDTO1 = new WxStoreAttrRespDTO();
        wxStoreAttrRespDTO1.setPrice(new BigDecimal("0.00"));
        wxStoreAttrRespDTO1.setUserck(0);
        wxStoreAttrGroupRespDTO1.setAttrList(Arrays.asList(wxStoreAttrRespDTO1));
        wxStoreItemRespDTO1.setAttrGroupList(Arrays.asList(wxStoreAttrGroupRespDTO1));
        wxStoreItemRespDTO1.setCurrentCount(new BigDecimal("0.00"));
        wxStoreItemRespDTO1.setItemPrice(new BigDecimal("0.00"));
        wxStoreAdvanceOrderDTO.setItemList(Arrays.asList(wxStoreItemRespDTO1));
        when(mockRedisUtils.hValues("key")).thenReturn(Lists.newArrayList(wxStoreAdvanceOrderDTO));

        when(mockRedisUtils.get("key")).thenReturn("result");

        // Run the test
        final WxStoreAdvanceOrderDTO result = wxStoreAdvanceOrderServiceImplUnderTest.getSingleWxStoreAdvanceOrder(
                wxStoreAdvanceConsumerReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetSingleWxStoreAdvanceOrder_WxStoreMenuDetailsServiceReturnsTrue() {
        // Setup
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build();
        final WxStoreAdvanceOrderDTO expectedResult = new WxStoreAdvanceOrderDTO();
        expectedResult.setWxStoreAdvanceConsumerReqDTO(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build());
        final WxStoreItemRespDTO wxStoreItemRespDTO = new WxStoreItemRespDTO();
        wxStoreItemRespDTO.setItemGuid("itemGuid");
        wxStoreItemRespDTO.setItemType(0);
        wxStoreItemRespDTO.setName("itemName");
        wxStoreItemRespDTO.setIsFixPkg(0);
        final WxStoreSkuRespDTO wxStoreSkuRespDTO = new WxStoreSkuRespDTO();
        wxStoreSkuRespDTO.setSkuGuid("skuGuid");
        wxStoreSkuRespDTO.setName("name");
        wxStoreSkuRespDTO.setSalePrice(new BigDecimal("0.00"));
        wxStoreSkuRespDTO.setUserck(0);
        wxStoreItemRespDTO.setSkuList(Arrays.asList(wxStoreSkuRespDTO));
        final SubgroupSynRespDTO subgroupSynRespDTO = new SubgroupSynRespDTO();
        subgroupSynRespDTO.setPickNum(0);
        final SubItemSkuSynRespDTO subItemSkuSynRespDTO = new SubItemSkuSynRespDTO();
        subItemSkuSynRespDTO.setSkuGuid("skuGuid");
        subItemSkuSynRespDTO.setSkuName("name");
        subItemSkuSynRespDTO.setItemType(0);
        subItemSkuSynRespDTO.setItemNum(new BigDecimal("0.00"));
        subItemSkuSynRespDTO.setAddPrice(new BigDecimal("0.00"));
        subItemSkuSynRespDTO.setDefaultNum(0);
        final AttrGroupSynRespDTO attrGroupSynRespDTO = new AttrGroupSynRespDTO();
        attrGroupSynRespDTO.setIsRequired(0);
        attrGroupSynRespDTO.setWithDefault(0);
        final AttrSynRespDTO attrSynRespDTO = new AttrSynRespDTO();
        attrSynRespDTO.setPrice(new BigDecimal("0.00"));
        attrSynRespDTO.setIsDefault(0);
        attrGroupSynRespDTO.setAttrList(Arrays.asList(attrSynRespDTO));
        subItemSkuSynRespDTO.setAttrGroupList(Arrays.asList(attrGroupSynRespDTO));
        subgroupSynRespDTO.setSubItemSkuList(Arrays.asList(subItemSkuSynRespDTO));
        wxStoreItemRespDTO.setSubgroupList(Arrays.asList(subgroupSynRespDTO));
        final WxStoreAttrGroupRespDTO wxStoreAttrGroupRespDTO = new WxStoreAttrGroupRespDTO();
        final WxStoreAttrRespDTO wxStoreAttrRespDTO = new WxStoreAttrRespDTO();
        wxStoreAttrRespDTO.setPrice(new BigDecimal("0.00"));
        wxStoreAttrRespDTO.setUserck(0);
        wxStoreAttrGroupRespDTO.setAttrList(Arrays.asList(wxStoreAttrRespDTO));
        wxStoreItemRespDTO.setAttrGroupList(Arrays.asList(wxStoreAttrGroupRespDTO));
        wxStoreItemRespDTO.setCurrentCount(new BigDecimal("0.00"));
        wxStoreItemRespDTO.setItemPrice(new BigDecimal("0.00"));
        expectedResult.setItemList(Arrays.asList(wxStoreItemRespDTO));

        when(mockWxStoreMenuDetailsService.judgeOrderType(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build())).thenReturn(true);
        when(mockRedisUtils.keyGenerate(BusinessName.ADVANCE_ORDER)).thenReturn("key");

        // Configure RedisUtils.hValues(...).
        final WxStoreAdvanceOrderDTO wxStoreAdvanceOrderDTO = new WxStoreAdvanceOrderDTO();
        wxStoreAdvanceOrderDTO.setWxStoreAdvanceConsumerReqDTO(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build());
        final WxStoreItemRespDTO wxStoreItemRespDTO1 = new WxStoreItemRespDTO();
        wxStoreItemRespDTO1.setItemGuid("itemGuid");
        wxStoreItemRespDTO1.setItemType(0);
        wxStoreItemRespDTO1.setName("itemName");
        wxStoreItemRespDTO1.setIsFixPkg(0);
        final WxStoreSkuRespDTO wxStoreSkuRespDTO1 = new WxStoreSkuRespDTO();
        wxStoreSkuRespDTO1.setSkuGuid("skuGuid");
        wxStoreSkuRespDTO1.setName("name");
        wxStoreSkuRespDTO1.setSalePrice(new BigDecimal("0.00"));
        wxStoreSkuRespDTO1.setUserck(0);
        wxStoreItemRespDTO1.setSkuList(Arrays.asList(wxStoreSkuRespDTO1));
        final SubgroupSynRespDTO subgroupSynRespDTO1 = new SubgroupSynRespDTO();
        subgroupSynRespDTO1.setPickNum(0);
        final SubItemSkuSynRespDTO subItemSkuSynRespDTO1 = new SubItemSkuSynRespDTO();
        subItemSkuSynRespDTO1.setSkuGuid("skuGuid");
        subItemSkuSynRespDTO1.setSkuName("name");
        subItemSkuSynRespDTO1.setItemType(0);
        subItemSkuSynRespDTO1.setItemNum(new BigDecimal("0.00"));
        subItemSkuSynRespDTO1.setAddPrice(new BigDecimal("0.00"));
        subItemSkuSynRespDTO1.setDefaultNum(0);
        final AttrGroupSynRespDTO attrGroupSynRespDTO1 = new AttrGroupSynRespDTO();
        attrGroupSynRespDTO1.setIsRequired(0);
        attrGroupSynRespDTO1.setWithDefault(0);
        final AttrSynRespDTO attrSynRespDTO1 = new AttrSynRespDTO();
        attrSynRespDTO1.setPrice(new BigDecimal("0.00"));
        attrSynRespDTO1.setIsDefault(0);
        attrGroupSynRespDTO1.setAttrList(Arrays.asList(attrSynRespDTO1));
        subItemSkuSynRespDTO1.setAttrGroupList(Arrays.asList(attrGroupSynRespDTO1));
        subgroupSynRespDTO1.setSubItemSkuList(Arrays.asList(subItemSkuSynRespDTO1));
        wxStoreItemRespDTO1.setSubgroupList(Arrays.asList(subgroupSynRespDTO1));
        final WxStoreAttrGroupRespDTO wxStoreAttrGroupRespDTO1 = new WxStoreAttrGroupRespDTO();
        final WxStoreAttrRespDTO wxStoreAttrRespDTO1 = new WxStoreAttrRespDTO();
        wxStoreAttrRespDTO1.setPrice(new BigDecimal("0.00"));
        wxStoreAttrRespDTO1.setUserck(0);
        wxStoreAttrGroupRespDTO1.setAttrList(Arrays.asList(wxStoreAttrRespDTO1));
        wxStoreItemRespDTO1.setAttrGroupList(Arrays.asList(wxStoreAttrGroupRespDTO1));
        wxStoreItemRespDTO1.setCurrentCount(new BigDecimal("0.00"));
        wxStoreItemRespDTO1.setItemPrice(new BigDecimal("0.00"));
        wxStoreAdvanceOrderDTO.setItemList(Arrays.asList(wxStoreItemRespDTO1));
        when(mockRedisUtils.hValues("key")).thenReturn(Lists.newArrayList(wxStoreAdvanceOrderDTO));

        when(mockRedisUtils.get("key")).thenReturn("result");

        // Run the test
        final WxStoreAdvanceOrderDTO result = wxStoreAdvanceOrderServiceImplUnderTest.getSingleWxStoreAdvanceOrder(
                wxStoreAdvanceConsumerReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetSingleWxStoreAdvanceOrder_RedisUtilsHValuesReturnsNoItems() {
        // Setup
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build();
        final WxStoreAdvanceOrderDTO expectedResult = new WxStoreAdvanceOrderDTO();
        expectedResult.setWxStoreAdvanceConsumerReqDTO(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build());
        final WxStoreItemRespDTO wxStoreItemRespDTO = new WxStoreItemRespDTO();
        wxStoreItemRespDTO.setItemGuid("itemGuid");
        wxStoreItemRespDTO.setItemType(0);
        wxStoreItemRespDTO.setName("itemName");
        wxStoreItemRespDTO.setIsFixPkg(0);
        final WxStoreSkuRespDTO wxStoreSkuRespDTO = new WxStoreSkuRespDTO();
        wxStoreSkuRespDTO.setSkuGuid("skuGuid");
        wxStoreSkuRespDTO.setName("name");
        wxStoreSkuRespDTO.setSalePrice(new BigDecimal("0.00"));
        wxStoreSkuRespDTO.setUserck(0);
        wxStoreItemRespDTO.setSkuList(Arrays.asList(wxStoreSkuRespDTO));
        final SubgroupSynRespDTO subgroupSynRespDTO = new SubgroupSynRespDTO();
        subgroupSynRespDTO.setPickNum(0);
        final SubItemSkuSynRespDTO subItemSkuSynRespDTO = new SubItemSkuSynRespDTO();
        subItemSkuSynRespDTO.setSkuGuid("skuGuid");
        subItemSkuSynRespDTO.setSkuName("name");
        subItemSkuSynRespDTO.setItemType(0);
        subItemSkuSynRespDTO.setItemNum(new BigDecimal("0.00"));
        subItemSkuSynRespDTO.setAddPrice(new BigDecimal("0.00"));
        subItemSkuSynRespDTO.setDefaultNum(0);
        final AttrGroupSynRespDTO attrGroupSynRespDTO = new AttrGroupSynRespDTO();
        attrGroupSynRespDTO.setIsRequired(0);
        attrGroupSynRespDTO.setWithDefault(0);
        final AttrSynRespDTO attrSynRespDTO = new AttrSynRespDTO();
        attrSynRespDTO.setPrice(new BigDecimal("0.00"));
        attrSynRespDTO.setIsDefault(0);
        attrGroupSynRespDTO.setAttrList(Arrays.asList(attrSynRespDTO));
        subItemSkuSynRespDTO.setAttrGroupList(Arrays.asList(attrGroupSynRespDTO));
        subgroupSynRespDTO.setSubItemSkuList(Arrays.asList(subItemSkuSynRespDTO));
        wxStoreItemRespDTO.setSubgroupList(Arrays.asList(subgroupSynRespDTO));
        final WxStoreAttrGroupRespDTO wxStoreAttrGroupRespDTO = new WxStoreAttrGroupRespDTO();
        final WxStoreAttrRespDTO wxStoreAttrRespDTO = new WxStoreAttrRespDTO();
        wxStoreAttrRespDTO.setPrice(new BigDecimal("0.00"));
        wxStoreAttrRespDTO.setUserck(0);
        wxStoreAttrGroupRespDTO.setAttrList(Arrays.asList(wxStoreAttrRespDTO));
        wxStoreItemRespDTO.setAttrGroupList(Arrays.asList(wxStoreAttrGroupRespDTO));
        wxStoreItemRespDTO.setCurrentCount(new BigDecimal("0.00"));
        wxStoreItemRespDTO.setItemPrice(new BigDecimal("0.00"));
        expectedResult.setItemList(Arrays.asList(wxStoreItemRespDTO));

        when(mockWxStoreMenuDetailsService.judgeOrderType(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build())).thenReturn(false);
        when(mockRedisUtils.keyGenerate(BusinessName.ADVANCE_ORDER)).thenReturn("key");
        when(mockRedisUtils.hValues("key")).thenReturn(Collections.emptyList());

        // Run the test
        final WxStoreAdvanceOrderDTO result = wxStoreAdvanceOrderServiceImplUnderTest.getSingleWxStoreAdvanceOrder(
                wxStoreAdvanceConsumerReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetTotalPrice1() {
        // Setup
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build();
        when(mockWxStoreMenuDetailsService.judgeOrderType(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build())).thenReturn(false);
        when(mockRedisUtils.keyGenerate(BusinessName.ADVANCE_ORDER)).thenReturn("key");

        // Configure RedisUtils.hValues(...).
        final WxStoreAdvanceOrderDTO wxStoreAdvanceOrderDTO = new WxStoreAdvanceOrderDTO();
        wxStoreAdvanceOrderDTO.setWxStoreAdvanceConsumerReqDTO(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build());
        final WxStoreItemRespDTO wxStoreItemRespDTO = new WxStoreItemRespDTO();
        wxStoreItemRespDTO.setItemGuid("itemGuid");
        wxStoreItemRespDTO.setItemType(0);
        wxStoreItemRespDTO.setName("itemName");
        wxStoreItemRespDTO.setIsFixPkg(0);
        final WxStoreSkuRespDTO wxStoreSkuRespDTO = new WxStoreSkuRespDTO();
        wxStoreSkuRespDTO.setSkuGuid("skuGuid");
        wxStoreSkuRespDTO.setName("name");
        wxStoreSkuRespDTO.setSalePrice(new BigDecimal("0.00"));
        wxStoreSkuRespDTO.setUserck(0);
        wxStoreItemRespDTO.setSkuList(Arrays.asList(wxStoreSkuRespDTO));
        final SubgroupSynRespDTO subgroupSynRespDTO = new SubgroupSynRespDTO();
        subgroupSynRespDTO.setPickNum(0);
        final SubItemSkuSynRespDTO subItemSkuSynRespDTO = new SubItemSkuSynRespDTO();
        subItemSkuSynRespDTO.setSkuGuid("skuGuid");
        subItemSkuSynRespDTO.setSkuName("name");
        subItemSkuSynRespDTO.setItemType(0);
        subItemSkuSynRespDTO.setItemNum(new BigDecimal("0.00"));
        subItemSkuSynRespDTO.setAddPrice(new BigDecimal("0.00"));
        subItemSkuSynRespDTO.setDefaultNum(0);
        final AttrGroupSynRespDTO attrGroupSynRespDTO = new AttrGroupSynRespDTO();
        attrGroupSynRespDTO.setIsRequired(0);
        attrGroupSynRespDTO.setWithDefault(0);
        final AttrSynRespDTO attrSynRespDTO = new AttrSynRespDTO();
        attrSynRespDTO.setPrice(new BigDecimal("0.00"));
        attrSynRespDTO.setIsDefault(0);
        attrGroupSynRespDTO.setAttrList(Arrays.asList(attrSynRespDTO));
        subItemSkuSynRespDTO.setAttrGroupList(Arrays.asList(attrGroupSynRespDTO));
        subgroupSynRespDTO.setSubItemSkuList(Arrays.asList(subItemSkuSynRespDTO));
        wxStoreItemRespDTO.setSubgroupList(Arrays.asList(subgroupSynRespDTO));
        final WxStoreAttrGroupRespDTO wxStoreAttrGroupRespDTO = new WxStoreAttrGroupRespDTO();
        final WxStoreAttrRespDTO wxStoreAttrRespDTO = new WxStoreAttrRespDTO();
        wxStoreAttrRespDTO.setPrice(new BigDecimal("0.00"));
        wxStoreAttrRespDTO.setUserck(0);
        wxStoreAttrGroupRespDTO.setAttrList(Arrays.asList(wxStoreAttrRespDTO));
        wxStoreItemRespDTO.setAttrGroupList(Arrays.asList(wxStoreAttrGroupRespDTO));
        wxStoreItemRespDTO.setCurrentCount(new BigDecimal("0.00"));
        wxStoreItemRespDTO.setItemPrice(new BigDecimal("0.00"));
        wxStoreAdvanceOrderDTO.setItemList(Arrays.asList(wxStoreItemRespDTO));
        when(mockRedisUtils.hValues("key")).thenReturn(Lists.newArrayList(wxStoreAdvanceOrderDTO));

        when(mockRedisUtils.get("key")).thenReturn("result");

        // Run the test
        final BigDecimal result = wxStoreAdvanceOrderServiceImplUnderTest.getTotalPrice(wxStoreAdvanceConsumerReqDTO);

        // Verify the results
        assertEquals(new BigDecimal("0.00"), result);
    }

    @Test
    public void testGetTotalPrice1_WxStoreMenuDetailsServiceReturnsTrue() {
        // Setup
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build();
        when(mockWxStoreMenuDetailsService.judgeOrderType(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build())).thenReturn(true);
        when(mockRedisUtils.keyGenerate(BusinessName.ADVANCE_ORDER)).thenReturn("key");

        // Configure RedisUtils.hValues(...).
        final WxStoreAdvanceOrderDTO wxStoreAdvanceOrderDTO = new WxStoreAdvanceOrderDTO();
        wxStoreAdvanceOrderDTO.setWxStoreAdvanceConsumerReqDTO(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build());
        final WxStoreItemRespDTO wxStoreItemRespDTO = new WxStoreItemRespDTO();
        wxStoreItemRespDTO.setItemGuid("itemGuid");
        wxStoreItemRespDTO.setItemType(0);
        wxStoreItemRespDTO.setName("itemName");
        wxStoreItemRespDTO.setIsFixPkg(0);
        final WxStoreSkuRespDTO wxStoreSkuRespDTO = new WxStoreSkuRespDTO();
        wxStoreSkuRespDTO.setSkuGuid("skuGuid");
        wxStoreSkuRespDTO.setName("name");
        wxStoreSkuRespDTO.setSalePrice(new BigDecimal("0.00"));
        wxStoreSkuRespDTO.setUserck(0);
        wxStoreItemRespDTO.setSkuList(Arrays.asList(wxStoreSkuRespDTO));
        final SubgroupSynRespDTO subgroupSynRespDTO = new SubgroupSynRespDTO();
        subgroupSynRespDTO.setPickNum(0);
        final SubItemSkuSynRespDTO subItemSkuSynRespDTO = new SubItemSkuSynRespDTO();
        subItemSkuSynRespDTO.setSkuGuid("skuGuid");
        subItemSkuSynRespDTO.setSkuName("name");
        subItemSkuSynRespDTO.setItemType(0);
        subItemSkuSynRespDTO.setItemNum(new BigDecimal("0.00"));
        subItemSkuSynRespDTO.setAddPrice(new BigDecimal("0.00"));
        subItemSkuSynRespDTO.setDefaultNum(0);
        final AttrGroupSynRespDTO attrGroupSynRespDTO = new AttrGroupSynRespDTO();
        attrGroupSynRespDTO.setIsRequired(0);
        attrGroupSynRespDTO.setWithDefault(0);
        final AttrSynRespDTO attrSynRespDTO = new AttrSynRespDTO();
        attrSynRespDTO.setPrice(new BigDecimal("0.00"));
        attrSynRespDTO.setIsDefault(0);
        attrGroupSynRespDTO.setAttrList(Arrays.asList(attrSynRespDTO));
        subItemSkuSynRespDTO.setAttrGroupList(Arrays.asList(attrGroupSynRespDTO));
        subgroupSynRespDTO.setSubItemSkuList(Arrays.asList(subItemSkuSynRespDTO));
        wxStoreItemRespDTO.setSubgroupList(Arrays.asList(subgroupSynRespDTO));
        final WxStoreAttrGroupRespDTO wxStoreAttrGroupRespDTO = new WxStoreAttrGroupRespDTO();
        final WxStoreAttrRespDTO wxStoreAttrRespDTO = new WxStoreAttrRespDTO();
        wxStoreAttrRespDTO.setPrice(new BigDecimal("0.00"));
        wxStoreAttrRespDTO.setUserck(0);
        wxStoreAttrGroupRespDTO.setAttrList(Arrays.asList(wxStoreAttrRespDTO));
        wxStoreItemRespDTO.setAttrGroupList(Arrays.asList(wxStoreAttrGroupRespDTO));
        wxStoreItemRespDTO.setCurrentCount(new BigDecimal("0.00"));
        wxStoreItemRespDTO.setItemPrice(new BigDecimal("0.00"));
        wxStoreAdvanceOrderDTO.setItemList(Arrays.asList(wxStoreItemRespDTO));
        when(mockRedisUtils.hValues("key")).thenReturn(Lists.newArrayList(wxStoreAdvanceOrderDTO));

        when(mockRedisUtils.get("key")).thenReturn("result");

        // Run the test
        final BigDecimal result = wxStoreAdvanceOrderServiceImplUnderTest.getTotalPrice(wxStoreAdvanceConsumerReqDTO);

        // Verify the results
        assertEquals(new BigDecimal("0.00"), result);
    }

    @Test
    public void testGetTotalPrice1_RedisUtilsHValuesReturnsNoItems() {
        // Setup
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build();
        when(mockWxStoreMenuDetailsService.judgeOrderType(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build())).thenReturn(false);
        when(mockRedisUtils.keyGenerate(BusinessName.ADVANCE_ORDER)).thenReturn("key");
        when(mockRedisUtils.hValues("key")).thenReturn(Collections.emptyList());

        // Run the test
        final BigDecimal result = wxStoreAdvanceOrderServiceImplUnderTest.getTotalPrice(wxStoreAdvanceConsumerReqDTO);

        // Verify the results
        assertEquals(new BigDecimal("0.00"), result);
    }

    @Test
    public void testGetTotalPrice2() {
        // Setup
        final WxStoreAdvanceOrderDTO wxStoreAdvanceOrderDTO = new WxStoreAdvanceOrderDTO();
        wxStoreAdvanceOrderDTO.setWxStoreAdvanceConsumerReqDTO(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build());
        final WxStoreItemRespDTO wxStoreItemRespDTO = new WxStoreItemRespDTO();
        wxStoreItemRespDTO.setItemGuid("itemGuid");
        wxStoreItemRespDTO.setItemType(0);
        wxStoreItemRespDTO.setName("itemName");
        wxStoreItemRespDTO.setIsFixPkg(0);
        final WxStoreSkuRespDTO wxStoreSkuRespDTO = new WxStoreSkuRespDTO();
        wxStoreSkuRespDTO.setSkuGuid("skuGuid");
        wxStoreSkuRespDTO.setName("name");
        wxStoreSkuRespDTO.setSalePrice(new BigDecimal("0.00"));
        wxStoreSkuRespDTO.setUserck(0);
        wxStoreItemRespDTO.setSkuList(Arrays.asList(wxStoreSkuRespDTO));
        final SubgroupSynRespDTO subgroupSynRespDTO = new SubgroupSynRespDTO();
        subgroupSynRespDTO.setPickNum(0);
        final SubItemSkuSynRespDTO subItemSkuSynRespDTO = new SubItemSkuSynRespDTO();
        subItemSkuSynRespDTO.setSkuGuid("skuGuid");
        subItemSkuSynRespDTO.setSkuName("name");
        subItemSkuSynRespDTO.setItemType(0);
        subItemSkuSynRespDTO.setItemNum(new BigDecimal("0.00"));
        subItemSkuSynRespDTO.setAddPrice(new BigDecimal("0.00"));
        subItemSkuSynRespDTO.setDefaultNum(0);
        final AttrGroupSynRespDTO attrGroupSynRespDTO = new AttrGroupSynRespDTO();
        attrGroupSynRespDTO.setIsRequired(0);
        attrGroupSynRespDTO.setWithDefault(0);
        final AttrSynRespDTO attrSynRespDTO = new AttrSynRespDTO();
        attrSynRespDTO.setPrice(new BigDecimal("0.00"));
        attrSynRespDTO.setIsDefault(0);
        attrGroupSynRespDTO.setAttrList(Arrays.asList(attrSynRespDTO));
        subItemSkuSynRespDTO.setAttrGroupList(Arrays.asList(attrGroupSynRespDTO));
        subgroupSynRespDTO.setSubItemSkuList(Arrays.asList(subItemSkuSynRespDTO));
        wxStoreItemRespDTO.setSubgroupList(Arrays.asList(subgroupSynRespDTO));
        final WxStoreAttrGroupRespDTO wxStoreAttrGroupRespDTO = new WxStoreAttrGroupRespDTO();
        final WxStoreAttrRespDTO wxStoreAttrRespDTO = new WxStoreAttrRespDTO();
        wxStoreAttrRespDTO.setPrice(new BigDecimal("0.00"));
        wxStoreAttrRespDTO.setUserck(0);
        wxStoreAttrGroupRespDTO.setAttrList(Arrays.asList(wxStoreAttrRespDTO));
        wxStoreItemRespDTO.setAttrGroupList(Arrays.asList(wxStoreAttrGroupRespDTO));
        wxStoreItemRespDTO.setCurrentCount(new BigDecimal("0.00"));
        wxStoreItemRespDTO.setItemPrice(new BigDecimal("0.00"));
        wxStoreAdvanceOrderDTO.setItemList(Arrays.asList(wxStoreItemRespDTO));
        final List<WxStoreAdvanceOrderDTO> wxStoreAdvanceOrderDTOS = Arrays.asList(wxStoreAdvanceOrderDTO);

        // Run the test
        final BigDecimal result = wxStoreAdvanceOrderServiceImplUnderTest.getTotalPrice(wxStoreAdvanceOrderDTOS);

        // Verify the results
        assertEquals(new BigDecimal("0.00"), result);
    }

    @Test
    public void testGetAttrTotalPrice() {
        // Setup
        final WxStoreItemRespDTO wxStoreItemRespDTO = new WxStoreItemRespDTO();
        wxStoreItemRespDTO.setItemGuid("itemGuid");
        wxStoreItemRespDTO.setItemType(0);
        wxStoreItemRespDTO.setName("itemName");
        wxStoreItemRespDTO.setIsFixPkg(0);
        final WxStoreSkuRespDTO wxStoreSkuRespDTO = new WxStoreSkuRespDTO();
        wxStoreSkuRespDTO.setSkuGuid("skuGuid");
        wxStoreSkuRespDTO.setName("name");
        wxStoreSkuRespDTO.setSalePrice(new BigDecimal("0.00"));
        wxStoreSkuRespDTO.setUserck(0);
        wxStoreItemRespDTO.setSkuList(Arrays.asList(wxStoreSkuRespDTO));
        final SubgroupSynRespDTO subgroupSynRespDTO = new SubgroupSynRespDTO();
        subgroupSynRespDTO.setPickNum(0);
        final SubItemSkuSynRespDTO subItemSkuSynRespDTO = new SubItemSkuSynRespDTO();
        subItemSkuSynRespDTO.setSkuGuid("skuGuid");
        subItemSkuSynRespDTO.setSkuName("name");
        subItemSkuSynRespDTO.setItemType(0);
        subItemSkuSynRespDTO.setItemNum(new BigDecimal("0.00"));
        subItemSkuSynRespDTO.setAddPrice(new BigDecimal("0.00"));
        subItemSkuSynRespDTO.setDefaultNum(0);
        final AttrGroupSynRespDTO attrGroupSynRespDTO = new AttrGroupSynRespDTO();
        attrGroupSynRespDTO.setIsRequired(0);
        attrGroupSynRespDTO.setWithDefault(0);
        final AttrSynRespDTO attrSynRespDTO = new AttrSynRespDTO();
        attrSynRespDTO.setPrice(new BigDecimal("0.00"));
        attrSynRespDTO.setIsDefault(0);
        attrGroupSynRespDTO.setAttrList(Arrays.asList(attrSynRespDTO));
        subItemSkuSynRespDTO.setAttrGroupList(Arrays.asList(attrGroupSynRespDTO));
        subgroupSynRespDTO.setSubItemSkuList(Arrays.asList(subItemSkuSynRespDTO));
        wxStoreItemRespDTO.setSubgroupList(Arrays.asList(subgroupSynRespDTO));
        final WxStoreAttrGroupRespDTO wxStoreAttrGroupRespDTO = new WxStoreAttrGroupRespDTO();
        final WxStoreAttrRespDTO wxStoreAttrRespDTO = new WxStoreAttrRespDTO();
        wxStoreAttrRespDTO.setPrice(new BigDecimal("0.00"));
        wxStoreAttrRespDTO.setUserck(0);
        wxStoreAttrGroupRespDTO.setAttrList(Arrays.asList(wxStoreAttrRespDTO));
        wxStoreItemRespDTO.setAttrGroupList(Arrays.asList(wxStoreAttrGroupRespDTO));
        wxStoreItemRespDTO.setCurrentCount(new BigDecimal("0.00"));
        wxStoreItemRespDTO.setItemPrice(new BigDecimal("0.00"));

        // Run the test
        final BigDecimal result = wxStoreAdvanceOrderServiceImplUnderTest.getAttrTotalPrice(wxStoreItemRespDTO);

        // Verify the results
        assertEquals(new BigDecimal("0.00"), result);
    }

    @Test
    public void testGetAdvanceOrderRemark() {
        // Setup
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build();
        when(mockWxStoreMenuDetailsService.judgeOrderType(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build())).thenReturn(false);
        when(mockRedisUtils.keyGenerate(BusinessName.ADVANCE_REMARK)).thenReturn("key");
        when(mockRedisUtils.get("key")).thenReturn("result");

        // Run the test
        final String result = wxStoreAdvanceOrderServiceImplUnderTest.getAdvanceOrderRemark(
                wxStoreAdvanceConsumerReqDTO);

        // Verify the results
        assertEquals("result", result);
    }

    @Test
    public void testGetAdvanceOrderRemark_WxStoreMenuDetailsServiceReturnsTrue() {
        // Setup
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build();
        when(mockWxStoreMenuDetailsService.judgeOrderType(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build())).thenReturn(true);
        when(mockRedisUtils.keyGenerate(BusinessName.ADVANCE_REMARK)).thenReturn("key");
        when(mockRedisUtils.get("key")).thenReturn("result");

        // Run the test
        final String result = wxStoreAdvanceOrderServiceImplUnderTest.getAdvanceOrderRemark(
                wxStoreAdvanceConsumerReqDTO);

        // Verify the results
        assertEquals("result", result);
    }

    @Test
    public void testGetAdvanceOrderRemark_RedisUtilsGetReturnsNull() {
        // Setup
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build();
        when(mockWxStoreMenuDetailsService.judgeOrderType(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build())).thenReturn(false);
        when(mockRedisUtils.keyGenerate(BusinessName.ADVANCE_REMARK)).thenReturn("key");
        when(mockRedisUtils.get("key")).thenReturn(null);

        // Run the test
        final String result = wxStoreAdvanceOrderServiceImplUnderTest.getAdvanceOrderRemark(
                wxStoreAdvanceConsumerReqDTO);

        // Verify the results
        assertNull(result);
    }

    @Test
    public void testUpdateAdvanceOrderRemark() {
        // Setup
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build();
        when(mockWxStoreMenuDetailsService.judgeOrderType(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build())).thenReturn(false);
        when(mockRedisUtils.keyGenerate(BusinessName.ADVANCE_REMARK)).thenReturn("key");

        // Run the test
        wxStoreAdvanceOrderServiceImplUnderTest.updateAdvanceOrderRemark(wxStoreAdvanceConsumerReqDTO);

        // Verify the results
        verify(mockRedisUtils).set("key", "orderRemark");
    }

    @Test
    public void testUpdateAdvanceOrderRemark_WxStoreMenuDetailsServiceReturnsTrue() {
        // Setup
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build();
        when(mockWxStoreMenuDetailsService.judgeOrderType(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build())).thenReturn(true);
        when(mockRedisUtils.keyGenerate(BusinessName.ADVANCE_REMARK)).thenReturn("key");

        // Run the test
        wxStoreAdvanceOrderServiceImplUnderTest.updateAdvanceOrderRemark(wxStoreAdvanceConsumerReqDTO);

        // Verify the results
        verify(mockRedisUtils).set("key", "orderRemark");
    }

    @Test
    public void testDelAdvanceOrderRemark() {
        // Setup
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build();
        when(mockWxStoreMenuDetailsService.judgeOrderType(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build())).thenReturn(false);
        when(mockRedisUtils.keyGenerate(BusinessName.ADVANCE_REMARK)).thenReturn("key");

        // Run the test
        wxStoreAdvanceOrderServiceImplUnderTest.delAdvanceOrderRemark(wxStoreAdvanceConsumerReqDTO);

        // Verify the results
        verify(mockRedisUtils).delete("key");
    }

    @Test
    public void testDelAdvanceOrderRemark_WxStoreMenuDetailsServiceReturnsTrue() {
        // Setup
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build();
        when(mockWxStoreMenuDetailsService.judgeOrderType(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("nopenId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .orderRemark("orderRemark")
                .nopenId("nopenId")
                .build())).thenReturn(true);
        when(mockRedisUtils.keyGenerate(BusinessName.ADVANCE_REMARK)).thenReturn("key");

        // Run the test
        wxStoreAdvanceOrderServiceImplUnderTest.delAdvanceOrderRemark(wxStoreAdvanceConsumerReqDTO);

        // Verify the results
        verify(mockRedisUtils).delete("key");
    }
}
