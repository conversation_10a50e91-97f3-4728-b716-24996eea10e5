package com.holderzone.saas.store.weixin.service;

import cn.hutool.poi.excel.ExcelReader;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.weixin.WxStickDownloadTableDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStickDownloadReqDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.BasicResponseHandler;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.Test;
import org.springframework.beans.BeanUtils;

import java.io.*;
import java.text.DecimalFormat;
import java.util.*;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Yu Ren
 * @date 2020/7/6 15:29
 * @description 手动下载企业所有桌贴工具
 */
//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = HolderSaasStoreWeixinApplication.class)
//@FixMethodOrder(MethodSorters.NAME_ASCENDING)
@Slf4j
public class TableStickersManualDownload {
    private static Logger logger = Logger.getLogger(ExcelReader.class.getName()); // 日志打印类

    /**
     * todo token
     */
//    private static final String TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJlbnRlcnByaXNlR3VpZCI6Ijg4N2YyMTgxLWViMDYtNGQ3Ny1iOTE0LTdjMzdjODg0OTUyYyIsInN0b3JlTm8iOiIiLCJkZXZpY2VHdWlkIjoiIiwidXNlckd1aWQiOiI3MmRiNzNlZi1lN2Y1LTQxMzUtODM4NS04MjEzNjAzOGQ1NGYiLCJpYXQiOjE2OTcwMjA4MzQsImlzcyI6IkhvbGRlci5jb20ifQ==.XFRh77+9Dwgla2RL77+977+9eD5Y77+9Lgjvv70E77+9WzDvv73vv73vv71Rbu+/ve+/ve+/vVA=";
//    private static final String TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJlbnRlcnByaXNlR3VpZCI6Ijg4N2YyMTgxLWViMDYtNGQ3Ny1iOTE0LTdjMzdjODg0OTUyYyIsInN0b3JlTm8iOiIiLCJkZXZpY2VHdWlkIjoiIiwidXNlckd1aWQiOiI3MmRiNzNlZi1lN2Y1LTQxMzUtODM4NS04MjEzNjAzOGQ1NGYiLCJpYXQiOjE3MDUyMDM2MTcsImlzcyI6IkhvbGRlci5jb20ifQ==.XwVFNznvv73vv70uYO+/ve+/vWvvv73vv711K07vv73vv71RUy0PF07vv70x77+9Fe+/vc6p";
    private static final String TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJlbnRlcnByaXNlR3VpZCI6Ijg4N2YyMTgxLWViMDYtNGQ3Ny1iOTE0LTdjMzdjODg0OTUyYyIsInN0b3JlTm8iOiIiLCJkZXZpY2VHdWlkIjoiIiwidXNlckd1aWQiOiI3MmRiNzNlZi1lN2Y1LTQxMzUtODM4NS04MjEzNjAzOGQ1NGYiLCJpYXQiOjE3MTQ5ODM2MTIsImlzcyI6IkhvbGRlci5jb20ifQ==.1arvv73vv71cYu+/ve+/vQ0me++/ve+/vRVrFO+/vRjvv73vv71Uf++/vUUX77+977+9Xe+/ve+/vQ==";
    // 何师：11d2895a-ad2e-4283-ba69-69d98bd1d0a5
    //红色：6694170967561732096 黄色：6694170998201122816
    private static final String ENTERPRISE_GUID = "887f2181-eb06-4d77-b914-7c37c884952c";
    private static final String LOGIN_TYPE = "0";
    private static final String SOURCE = "0"; //todo 来源
    private static final String TERMINAL_CODE = "100";

    private static final String XLS = "xls";
    private static final String XLSX = "xlsx";

    /**
     * 设定Excel文件所在路径
     *
     * excel colums:
     *      区域guid	区域名字	桌贴guid	桌贴名字	门店id	门店名称
     * sql:
     *      select area_guid,area_name,guid as table_guid,table_code as table_name,store_guid,store_name from hst_table_basic where store_name like 'He%'and deleted = 0
     */
    private final static String INPUT_EXCEL = "D:\\holder\\files\\导表数据\\何师桌台\\何师桌台20240506.xlsx";

    //输出
    private final static String OUTPUT_EXCEL = "D:\\holder\\files\\导表数据\\何师桌台\\何师桌贴带20240506\\";

    public static void main(String[] args) throws IOException, InterruptedException {
        download();
    }

    @Test
    public static void download() throws IOException, InterruptedException {
        // 读取Excel文件内容
        List<WxStickDownloadTableNewDTO> readResult = readExcel(INPUT_EXCEL);
        if (CollectionUtils.isEmpty(readResult)) {
            throw new BusinessException("Excel无数据！");
        }
        Map<String, List<WxStickDownloadTableNewDTO>> collect =
                readResult.stream().collect(Collectors.groupingBy(WxStickDownloadTableNewDTO::getAreaGuid));
        Map<String, String> resultMap = new HashMap<>();
        String areaCode = "";
        Set<String> areaGuids = collect.keySet();
        StringBuilder errorMessage = new StringBuilder();
        for (String areaGuid : areaGuids) {
            WxStickDownloadReqDTO wxStickDownloadReqDTO = new WxStickDownloadReqDTO();
            List<WxStickDownloadTableNewDTO> wxStickDownloadTableNewDTOS = collect.get(areaGuid);
            List<WxStickDownloadTableDTO> wxStickDownloadTableDTOList = Lists.newLinkedList();
            wxStickDownloadTableNewDTOS.stream().forEach(e -> {
                WxStickDownloadTableDTO wxStickDownloadTableDTO = new WxStickDownloadTableDTO();
                BeanUtils.copyProperties(e, wxStickDownloadTableDTO);
                wxStickDownloadTableDTOList.add(wxStickDownloadTableDTO);
            });
            wxStickDownloadReqDTO.setTableList(wxStickDownloadTableDTOList);
            wxStickDownloadReqDTO.setBrandGuid("11d2895a-ad2e-4283-ba69-69d98bd1d0a5");//todo 品牌
            wxStickDownloadReqDTO.setQrCodeType(3);//赚餐v二维码
            wxStickDownloadReqDTO.setStickGuid("0");//todo 桌贴模版
            wxStickDownloadReqDTO.setStoreGuid(wxStickDownloadTableNewDTOS.get(0).getStoreGuid());
            wxStickDownloadReqDTO.setStoreName(wxStickDownloadTableNewDTOS.get(0).getStoreName());
            areaCode = wxStickDownloadTableNewDTOS.get(0).getAreaName();
            //请求
            String url = "https://mch.holderzone.com/gateway/merchant/wx_table_stick/create_stick_zip";
            HttpClient httpClient = HttpClientBuilder.create().build();
            StringEntity stringEntity = new StringEntity(JacksonUtils.writeValueAsString(wxStickDownloadReqDTO), "utf" +
                    "-8");
            stringEntity.setContentType("application/json");
            HttpPost httpPost = new HttpPost(url);
            httpPost.setEntity(stringEntity);
            httpPost.addHeader("enterpriseGuid", ENTERPRISE_GUID);
            httpPost.addHeader("logintype", LOGIN_TYPE);
            httpPost.addHeader("menuguid", "1903121557593120029");//todo menuid
            httpPost.addHeader("source", SOURCE);
            httpPost.addHeader("terminalcode", TERMINAL_CODE);
            httpPost.addHeader("token", TOKEN);
            ResponseHandler<String> responseHandler = new BasicResponseHandler();
            String execute = httpClient.execute(httpPost, responseHandler);
            log.info("httppostresult：{}", execute);
            resultMap.put(execute, wxStickDownloadReqDTO.getStoreName());
            Thread.sleep(30000);
            downloadNewZip(wxStickDownloadReqDTO.getStoreName() + "-" + areaCode,
                    JSON.parseObject(execute).getString("tdata"),errorMessage);
        }
        //错误文件
        BufferedWriter out = new BufferedWriter(new FileWriter(OUTPUT_EXCEL + "error.txt"));
        out.write(errorMessage.toString());
        out.close();
        log.info("所有key的总和{}", JacksonUtils.writeValueAsString(resultMap));
    }

    private static void downloadNewZip(String fileName, String key,StringBuilder errorMessage) throws IOException, InterruptedException {
        String url = "https://mch.holderzone.com/gateway/merchant/wx_table_stick/download_stick_zip";
        HttpClient httpClient = HttpClientBuilder.create().build();
        WxStickDownloadReqDTO wxStickDownloadReqDTO = new WxStickDownloadReqDTO();
        wxStickDownloadReqDTO.setDownloadKey(key);
        StringEntity stringEntity = new StringEntity(JacksonUtils.writeValueAsString(wxStickDownloadReqDTO), "utf-8");
        stringEntity.setContentType("application/json");
        HttpPost httpPost = new HttpPost(url);
        httpPost.setEntity(stringEntity);
        httpPost.addHeader("enterpriseGuid", ENTERPRISE_GUID);
        httpPost.addHeader("logintype", LOGIN_TYPE);
        httpPost.addHeader("menuguid", "1903121557593120029");
        httpPost.addHeader("source", SOURCE);
        httpPost.addHeader("terminalcode", TERMINAL_CODE);
        httpPost.addHeader("token", TOKEN);
        HttpResponse execute = httpClient.execute(httpPost);


        InputStream content = execute.getEntity().getContent();
        log.info("httppostresult：{}", execute);
        //        ByteArrayOutputStream output = new ByteArrayOutputStream();
        //        byte[] buffer = new byte[1024*4];
        //        int n = 0;
        //        while (-1 != (n = content.read(buffer))) {
        //            output.write(buffer, 0, n);
        //        }
        File file = new File(OUTPUT_EXCEL + fileName.replace("*","x") + ".zip");
        try {
            FileUtils.copyInputStreamToFile(content, file);
        } catch (Exception e) {
            log.info("失败的门店桌贴{}", fileName);
            errorMessage.append("\n" + fileName);
        }
        //        return byteToFile(output.toByteArray(),"F:\\读取桌台\\"+fileName+".zip");
    }

    //    public static Boolean byteToFile(byte[] contents, String filePath) throws IOException {
    //        Boolean result = true;
    //        BufferedInputStream bis = null;
    //        FileOutputStream fos = null;
    //        BufferedOutputStream output = null;
    //        try {
    //            ByteArrayInputStream byteInputStream = new ByteArrayInputStream(contents);
    //            bis = new BufferedInputStream(byteInputStream);
    //            File file = new File(filePath);
    //            // 获取文件的父路径字符串
    //            File path = file.getParentFile();
    //            if (!path.exists()) {
    //                log.info("文件夹不存在，创建。path={}", path);
    //                boolean isCreated = path.mkdirs();
    //                if (!isCreated) {
    //                    log.error("创建文件夹失败，path={}", path);
    //                }
    //            }
    //            fos = new FileOutputStream(file);
    //            // 实例化OutputString 对象
    //            output = new BufferedOutputStream(fos);
    //            byte[] buffer = new byte[1024];
    //            int length = bis.read(buffer);
    //            while (length != -1) {
    //                output.write(buffer, 0, length);
    //                length = bis.read(buffer);
    //            }
    //            output.flush();
    //        } catch (Exception e) {
    //            log.error("输出文件流时抛异常，filePath={}", filePath, e);
    //        } finally {
    ////            try {
    //                assert bis != null;
    //                bis.close();
    //                assert fos != null;
    //                fos.close();
    //                assert output != null;
    //                output.close();
    //                result = false;
    ////            } catch (IOException e0) {
    ////                log.error("文件处理失败，filePath={}", filePath, e0);
    ////            }
    //        }
    //        return result;
    //    }

    /**
     * 根据文件后缀名类型获取对应的工作簿对象
     *
     * @param inputStream 读取文件的输入流
     * @param fileType    文件后缀名类型（xls或xlsx）
     * @return 包含文件数据的工作簿对象
     * @throws IOException
     */
    public static Workbook getWorkbook(InputStream inputStream, String fileType) throws IOException {
        Workbook workbook = null;
        if (fileType.equalsIgnoreCase(XLS)) {
            workbook = new HSSFWorkbook(inputStream);
        } else if (fileType.equalsIgnoreCase(XLSX)) {
            workbook = new XSSFWorkbook(inputStream);
        }
        return workbook;
    }

    /**
     * 读取Excel文件内容
     *
     * @param fileName 要读取的Excel文件所在路径
     * @return 读取结果列表，读取失败时返回null
     */
    public static List<WxStickDownloadTableNewDTO> readExcel(String fileName) {

        Workbook workbook = null;
        FileInputStream inputStream = null;

        try {
            // 获取Excel后缀名
            String fileType = fileName.substring(fileName.lastIndexOf(".") + 1, fileName.length());
            // 获取Excel文件
            File excelFile = new File(fileName);
            if (!excelFile.exists()) {
                log.info("指定的Excel文件不存在！");
                return null;
            }

            // 获取Excel工作簿
            inputStream = new FileInputStream(excelFile);
            workbook = getWorkbook(inputStream, fileType);

            // 读取excel中的数据
            List<WxStickDownloadTableNewDTO> resultDataList = parseExcel(workbook);

            return resultDataList;
        } catch (Exception e) {
            log.error("解析Excel失败，文件名：" + fileName + " 错误信息：" + e.getMessage());
            return null;
        } finally {
            try {
                if (null != workbook) {
                    workbook.close();
                }
                if (null != inputStream) {
                    inputStream.close();
                }
            } catch (Exception e) {
                log.error("关闭数据流出错！错误信息：" + e.getMessage());
                return null;
            }
        }
    }

    /**
     * 解析Excel数据
     *
     * @param workbook Excel工作簿对象
     * @return 解析结果
     */
    private static List<WxStickDownloadTableNewDTO> parseExcel(Workbook workbook) {
        List<WxStickDownloadTableNewDTO> resultDataList = new ArrayList<>();
        // 解析sheet
        for (int sheetNum = 0; sheetNum < workbook.getNumberOfSheets(); sheetNum++) {
            Sheet sheet = workbook.getSheetAt(sheetNum);

            // 校验sheet是否合法
            if (sheet == null) {
                continue;
            }

            // 获取第一行数据
            int firstRowNum = sheet.getFirstRowNum();
            Row firstRow = sheet.getRow(firstRowNum);
            if (null == firstRow) {
                log.info("解析Excel失败，在第一行没有读取到任何数据！");
            }

            // 解析每一行的数据，构造数据对象
            int rowStart = firstRowNum + 1;
            int rowEnd = sheet.getPhysicalNumberOfRows();
            for (int rowNum = rowStart; rowNum < rowEnd; rowNum++) {
                Row row = sheet.getRow(rowNum);

                if (null == row) {
                    continue;
                }

                WxStickDownloadTableNewDTO resultData = convertRowToData(row);
                resultDataList.add(resultData);
            }
        }

        return resultDataList;
    }

    /**
     * 将单元格内容转换为字符串
     *
     * @param cell
     * @return
     */
    private static String convertCellValueToString(Cell cell) {
        if (cell == null) {
            return null;
        }
        String returnValue = null;
        switch (cell.getCellType()) {
            case NUMERIC:   //数字
                Double doubleValue = cell.getNumericCellValue();

                // 格式化科学计数法，取一位整数
                DecimalFormat df = new DecimalFormat("0");
                returnValue = df.format(doubleValue);
                break;
            case STRING:    //字符串
                returnValue = cell.getStringCellValue();
                break;
            case BOOLEAN:   //布尔
                Boolean booleanValue = cell.getBooleanCellValue();
                returnValue = booleanValue.toString();
                break;
            case BLANK:     // 空值
                break;
            case FORMULA:   // 公式
                returnValue = cell.getCellFormula();
                break;
            case ERROR:     // 故障
                break;
            default:
                break;
        }
        return returnValue;
    }

    /**
     * 提取每一行中需要的数据，构造成为一个结果数据对象
     * <p>
     * 当该行中有单元格的数据为空或不合法时，忽略该行的数据
     *
     * @param row 行数据
     * @return 解析后的行数据对象，行数据错误时返回null
     */
    private static WxStickDownloadTableNewDTO convertRowToData(Row row) {
        WxStickDownloadTableNewDTO resultData = new WxStickDownloadTableNewDTO();

        Cell cell;
        int cellNum = 0;
        // 获取区域id
        cell = row.getCell(cellNum++);
        String name = convertCellValueToString(cell);
        resultData.setAreaGuid(name);
        // 获取区域名称
        cell = row.getCell(cellNum++);
        String ageStr = convertCellValueToString(cell);
        if (null == ageStr || "".equals(ageStr)) {
            // 年龄为空
            resultData.setAreaName(null);
        } else {
            resultData.setAreaName(ageStr);
        }
        // 获取桌台guid
        cell = row.getCell(cellNum++);
        String location = convertCellValueToString(cell);
        resultData.setTableGuid(location);
        // 获取桌台名称
        cell = row.getCell(cellNum++);
        String job = convertCellValueToString(cell);
        resultData.setTableName(job);
        // 获取门店id
        cell = row.getCell(cellNum++);
        String storeGuid = convertCellValueToString(cell);
        resultData.setStoreGuid(storeGuid);
        // 获取门店名称
        cell = row.getCell(cellNum++);
        String storeName = convertCellValueToString(cell);
        resultData.setStoreName(storeName);
        return resultData;
    }
}
