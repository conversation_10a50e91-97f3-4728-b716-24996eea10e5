package com.holderzone.saas.store.weixin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.saas.store.dto.map.GeoCodeDTO;
import com.holderzone.saas.store.dto.map.GeoCodeRespDTO;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.queue.StoreGuidDTO;
import com.holderzone.saas.store.dto.table.TableBasicQueryDTO;
import com.holderzone.saas.store.dto.table.TableOrderDTO;
import com.holderzone.saas.store.dto.weixin.*;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreCityListRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreStatusRespDTO;
import com.holderzone.saas.store.weixin.entity.domain.WxQueueConfigDO;
import com.holderzone.saas.store.weixin.service.WxConfigOverviewService;
import com.holderzone.saas.store.weixin.service.WxQueueConfigService;
import com.holderzone.saas.store.weixin.service.rpc.OrganizationClientService;
import com.holderzone.saas.store.weixin.service.rpc.QueueClientService;
import com.holderzone.saas.store.weixin.service.rpc.WxStoreTableClientService;
import com.holderzone.saas.store.weixin.utils.DynamicHelper;
import com.holderzone.saas.store.weixin.utils.MapUtil;
import javafx.util.Pair;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.time.LocalTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class WxStoreListServiceImplTest {

    @Mock
    private OrganizationClientService mockOrganizationClientService;
    @Mock
    private DynamicHelper mockDynamicHelper;
    @Mock
    private WxQueueConfigService mockWxQueueConfigService;
    @Mock
    private WxConfigOverviewService mockWxConfigOverviewService;
    @Mock
    private QueueClientService mockQueueClientService;
    @Mock
    private WxStoreTableClientService mockWxStoreTableClientService;
    @Mock
    private MapUtil mockMapUtil;

    private WxStoreListServiceImpl wxStoreListServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        wxStoreListServiceImplUnderTest = new WxStoreListServiceImpl();
        wxStoreListServiceImplUnderTest.organizationClientService = mockOrganizationClientService;
        wxStoreListServiceImplUnderTest.dynamicHelper = mockDynamicHelper;
        wxStoreListServiceImplUnderTest.wxQueueConfigService = mockWxQueueConfigService;
        wxStoreListServiceImplUnderTest.wxConfigOverviewService = mockWxConfigOverviewService;
        wxStoreListServiceImplUnderTest.queueClientService = mockQueueClientService;
        wxStoreListServiceImplUnderTest.wxStoreTableClientService = mockWxStoreTableClientService;
        wxStoreListServiceImplUnderTest.mapUtil = mockMapUtil;
    }

    @Test
    public void testListStoreConfig() {
        // Setup
        final WxPositionDTO wxPositionDTO = new WxPositionDTO(new BigDecimal("0.00"), new BigDecimal("0.00"),
                "cityCode", "cityName");
        final WxStoreListDTO expectedResult = new WxStoreListDTO();
        expectedResult.setEnterpriseGuid("enterpriseGuid");
        expectedResult.setBrandGuid("belongBrandGuid");
        expectedResult.setBrandName("brandName");
        expectedResult.setBrandLogoUrl("brandLogoUrl");
        final WxStoreConfigDTO wxStoreConfigDTO = new WxStoreConfigDTO();
        wxStoreConfigDTO.setStoreGuid("guid");
        wxStoreConfigDTO.setStoreName("name");
        wxStoreConfigDTO.setTel("contactTel");
        wxStoreConfigDTO.setAddress("address");
        wxStoreConfigDTO.setIsOpened(false);
        wxStoreConfigDTO.setDistant(0);
        wxStoreConfigDTO.setLongitude(new BigDecimal("0.00"));
        wxStoreConfigDTO.setLatitude(new BigDecimal("0.00"));
        final WxStoreQueueConfigDTO wxStoreQueueConfigDTO = new WxStoreQueueConfigDTO();
        wxStoreQueueConfigDTO.setShowQueueButton(false);
        wxStoreQueueConfigDTO.setCouldQueue(false);
        wxStoreQueueConfigDTO.setNeedQueue(false);
        wxStoreQueueConfigDTO.setMaxDistant(0);
        wxStoreQueueConfigDTO.setDistantConstraint(false);
        wxStoreConfigDTO.setWxStoreQueueConfigDTO(wxStoreQueueConfigDTO);
        expectedResult.setStoreConfigDTOS(Arrays.asList(wxStoreConfigDTO));
        expectedResult.setStoreCount(0);

        // Configure OrganizationClientService.queryBrandByGuid(...).
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("cb936710-e9e4-4867-8af0-2e8d387bb1cb");
        brandDTO.setUuid("786a5e3c-bdc7-4910-a396-08c6463816f6");
        brandDTO.setName("brandName");
        brandDTO.setDescription("description");
        brandDTO.setLogoUrl("brandLogoUrl");
        when(mockOrganizationClientService.queryBrandByGuid("belongBrandGuid")).thenReturn(brandDTO);

        // Configure OrganizationClientService.queryStoreByCityAndBrand(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setProvinceCode("provinceCode");
        storeDTO.setProvinceName("provinceName");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setCountyName("countyName");
        storeDTO.setAddressDetail("address");
        storeDTO.setLongitude("key");
        storeDTO.setLatitude("key");
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO);
        when(mockOrganizationClientService.queryStoreByCityAndBrand(any(StoreDTO.class))).thenReturn(storeDTOS);

        // Configure WxConfigOverviewService.listByStoreGuidList(...).
        final WxStoreStatusRespDTO wxStoreStatusRespDTO = new WxStoreStatusRespDTO();
        wxStoreStatusRespDTO.setGuid("c3962094-7841-42da-93b3-cdce202a3931");
        wxStoreStatusRespDTO.setStoreGuid("storeGuid");
        wxStoreStatusRespDTO.setStoreName("storeName");
        wxStoreStatusRespDTO.setIsOpened(0);
        wxStoreStatusRespDTO.setQueueUpStatus(0);
        final List<WxStoreStatusRespDTO> wxStoreStatusRespDTOS = Arrays.asList(wxStoreStatusRespDTO);
        when(mockWxConfigOverviewService.listByStoreGuidList(Arrays.asList("value"))).thenReturn(wxStoreStatusRespDTOS);

        // Configure WxQueueConfigService.list(...).
        final WxQueueConfigDO wxQueueConfigDO = new WxQueueConfigDO();
        wxQueueConfigDO.setId(0L);
        wxQueueConfigDO.setStoreGuid("storeGuid");
        wxQueueConfigDO.setIsQueueOpen(0);
        wxQueueConfigDO.setDistant(0);
        wxQueueConfigDO.setDistantConstraint(0);
        final List<WxQueueConfigDO> wxQueueConfigDOS = Arrays.asList(wxQueueConfigDO);
        when(mockWxQueueConfigService.list(any(LambdaQueryWrapper.class))).thenReturn(wxQueueConfigDOS);

        // Configure MapUtil.geoLocation(...).
        final GeoCodeRespDTO geoCodeRespDTO = new GeoCodeRespDTO();
        final GeoCodeDTO geoCodeDTO = new GeoCodeDTO();
        geoCodeDTO.setCity("city");
        geoCodeDTO.setDistrict("district");
        geoCodeDTO.setNumber("number");
        geoCodeDTO.setLocation("location");
        geoCodeRespDTO.setGeoCodeDTOList(Arrays.asList(geoCodeDTO));
        when(mockMapUtil.geoLocation("address")).thenReturn(geoCodeRespDTO);

        // Configure MapUtil.splitLocation(...).
        final Pair<BigDecimal, BigDecimal> bigDecimalBigDecimalPair = new Pair<>(new BigDecimal("0.00"),
                new BigDecimal("0.00"));
        when(mockMapUtil.splitLocation("location")).thenReturn(bigDecimalBigDecimalPair);

        when(mockMapUtil.distant(new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"),
                new BigDecimal("0.00"))).thenReturn(0);
        when(mockQueueClientService.allEmpty(new StoreGuidDTO("guid"))).thenReturn(false);

        // Configure WxStoreTableClientService.listByAndroid(...).
        final TableOrderDTO tableOrderDTO = new TableOrderDTO();
        tableOrderDTO.setTableGuid("tableGuid");
        tableOrderDTO.setAreaName("areaName");
        tableOrderDTO.setAreaGuid("areaGuid");
        tableOrderDTO.setOrderAmount(new BigDecimal("0.00"));
        tableOrderDTO.setStatus(0);
        final List<TableOrderDTO> tableOrderDTOS = Arrays.asList(tableOrderDTO);
        final TableBasicQueryDTO tableBasicQueryDTO = new TableBasicQueryDTO();
        tableBasicQueryDTO.setStoreGuid("guid");
        tableBasicQueryDTO.setAreaGuid("areaGuid");
        tableBasicQueryDTO.setTableGuidList(Arrays.asList("value"));
        when(mockWxStoreTableClientService.listByAndroid(tableBasicQueryDTO)).thenReturn(tableOrderDTOS);

        // Run the test
        final WxStoreListDTO result = wxStoreListServiceImplUnderTest.listStoreConfig(wxPositionDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
    }

    @Test
    public void testListStoreConfig_OrganizationClientServiceQueryStoreByCityAndBrandReturnsNoItems() {
        // Setup
        final WxPositionDTO wxPositionDTO = new WxPositionDTO(new BigDecimal("0.00"), new BigDecimal("0.00"),
                "cityCode", "cityName");
        final WxStoreListDTO expectedResult = new WxStoreListDTO();
        expectedResult.setEnterpriseGuid("enterpriseGuid");
        expectedResult.setBrandGuid("belongBrandGuid");
        expectedResult.setBrandName("brandName");
        expectedResult.setBrandLogoUrl("brandLogoUrl");
        final WxStoreConfigDTO wxStoreConfigDTO = new WxStoreConfigDTO();
        wxStoreConfigDTO.setStoreGuid("guid");
        wxStoreConfigDTO.setStoreName("name");
        wxStoreConfigDTO.setTel("contactTel");
        wxStoreConfigDTO.setAddress("address");
        wxStoreConfigDTO.setIsOpened(false);
        wxStoreConfigDTO.setDistant(0);
        wxStoreConfigDTO.setLongitude(new BigDecimal("0.00"));
        wxStoreConfigDTO.setLatitude(new BigDecimal("0.00"));
        final WxStoreQueueConfigDTO wxStoreQueueConfigDTO = new WxStoreQueueConfigDTO();
        wxStoreQueueConfigDTO.setShowQueueButton(false);
        wxStoreQueueConfigDTO.setCouldQueue(false);
        wxStoreQueueConfigDTO.setNeedQueue(false);
        wxStoreQueueConfigDTO.setMaxDistant(0);
        wxStoreQueueConfigDTO.setDistantConstraint(false);
        wxStoreConfigDTO.setWxStoreQueueConfigDTO(wxStoreQueueConfigDTO);
        expectedResult.setStoreConfigDTOS(Arrays.asList(wxStoreConfigDTO));
        expectedResult.setStoreCount(0);

        // Configure OrganizationClientService.queryBrandByGuid(...).
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("cb936710-e9e4-4867-8af0-2e8d387bb1cb");
        brandDTO.setUuid("786a5e3c-bdc7-4910-a396-08c6463816f6");
        brandDTO.setName("brandName");
        brandDTO.setDescription("description");
        brandDTO.setLogoUrl("brandLogoUrl");
        when(mockOrganizationClientService.queryBrandByGuid("belongBrandGuid")).thenReturn(brandDTO);

        when(mockOrganizationClientService.queryStoreByCityAndBrand(any(StoreDTO.class)))
                .thenReturn(Collections.emptyList());

        // Configure WxConfigOverviewService.listByStoreGuidList(...).
        final WxStoreStatusRespDTO wxStoreStatusRespDTO = new WxStoreStatusRespDTO();
        wxStoreStatusRespDTO.setGuid("c3962094-7841-42da-93b3-cdce202a3931");
        wxStoreStatusRespDTO.setStoreGuid("storeGuid");
        wxStoreStatusRespDTO.setStoreName("storeName");
        wxStoreStatusRespDTO.setIsOpened(0);
        wxStoreStatusRespDTO.setQueueUpStatus(0);
        final List<WxStoreStatusRespDTO> wxStoreStatusRespDTOS = Arrays.asList(wxStoreStatusRespDTO);
        when(mockWxConfigOverviewService.listByStoreGuidList(Arrays.asList("value"))).thenReturn(wxStoreStatusRespDTOS);

        // Configure WxQueueConfigService.list(...).
        final WxQueueConfigDO wxQueueConfigDO = new WxQueueConfigDO();
        wxQueueConfigDO.setId(0L);
        wxQueueConfigDO.setStoreGuid("storeGuid");
        wxQueueConfigDO.setIsQueueOpen(0);
        wxQueueConfigDO.setDistant(0);
        wxQueueConfigDO.setDistantConstraint(0);
        final List<WxQueueConfigDO> wxQueueConfigDOS = Arrays.asList(wxQueueConfigDO);
        when(mockWxQueueConfigService.list(any(LambdaQueryWrapper.class))).thenReturn(wxQueueConfigDOS);

        // Configure MapUtil.geoLocation(...).
        final GeoCodeRespDTO geoCodeRespDTO = new GeoCodeRespDTO();
        final GeoCodeDTO geoCodeDTO = new GeoCodeDTO();
        geoCodeDTO.setCity("city");
        geoCodeDTO.setDistrict("district");
        geoCodeDTO.setNumber("number");
        geoCodeDTO.setLocation("location");
        geoCodeRespDTO.setGeoCodeDTOList(Arrays.asList(geoCodeDTO));
        when(mockMapUtil.geoLocation("address")).thenReturn(geoCodeRespDTO);

        // Configure MapUtil.splitLocation(...).
        final Pair<BigDecimal, BigDecimal> bigDecimalBigDecimalPair = new Pair<>(new BigDecimal("0.00"),
                new BigDecimal("0.00"));
        when(mockMapUtil.splitLocation("location")).thenReturn(bigDecimalBigDecimalPair);

        when(mockMapUtil.distant(new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"),
                new BigDecimal("0.00"))).thenReturn(0);
        when(mockQueueClientService.allEmpty(new StoreGuidDTO("guid"))).thenReturn(false);

        // Configure WxStoreTableClientService.listByAndroid(...).
        final TableOrderDTO tableOrderDTO = new TableOrderDTO();
        tableOrderDTO.setTableGuid("tableGuid");
        tableOrderDTO.setAreaName("areaName");
        tableOrderDTO.setAreaGuid("areaGuid");
        tableOrderDTO.setOrderAmount(new BigDecimal("0.00"));
        tableOrderDTO.setStatus(0);
        final List<TableOrderDTO> tableOrderDTOS = Arrays.asList(tableOrderDTO);
        final TableBasicQueryDTO tableBasicQueryDTO = new TableBasicQueryDTO();
        tableBasicQueryDTO.setStoreGuid("guid");
        tableBasicQueryDTO.setAreaGuid("areaGuid");
        tableBasicQueryDTO.setTableGuidList(Arrays.asList("value"));
        when(mockWxStoreTableClientService.listByAndroid(tableBasicQueryDTO)).thenReturn(tableOrderDTOS);

        // Run the test
        final WxStoreListDTO result = wxStoreListServiceImplUnderTest.listStoreConfig(wxPositionDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
    }

    @Test
    public void testListStoreConfig_WxConfigOverviewServiceReturnsNoItems() {
        // Setup
        final WxPositionDTO wxPositionDTO = new WxPositionDTO(new BigDecimal("0.00"), new BigDecimal("0.00"),
                "cityCode", "cityName");
        final WxStoreListDTO expectedResult = new WxStoreListDTO();
        expectedResult.setEnterpriseGuid("enterpriseGuid");
        expectedResult.setBrandGuid("belongBrandGuid");
        expectedResult.setBrandName("brandName");
        expectedResult.setBrandLogoUrl("brandLogoUrl");
        final WxStoreConfigDTO wxStoreConfigDTO = new WxStoreConfigDTO();
        wxStoreConfigDTO.setStoreGuid("guid");
        wxStoreConfigDTO.setStoreName("name");
        wxStoreConfigDTO.setTel("contactTel");
        wxStoreConfigDTO.setAddress("address");
        wxStoreConfigDTO.setIsOpened(false);
        wxStoreConfigDTO.setDistant(0);
        wxStoreConfigDTO.setLongitude(new BigDecimal("0.00"));
        wxStoreConfigDTO.setLatitude(new BigDecimal("0.00"));
        final WxStoreQueueConfigDTO wxStoreQueueConfigDTO = new WxStoreQueueConfigDTO();
        wxStoreQueueConfigDTO.setShowQueueButton(false);
        wxStoreQueueConfigDTO.setCouldQueue(false);
        wxStoreQueueConfigDTO.setNeedQueue(false);
        wxStoreQueueConfigDTO.setMaxDistant(0);
        wxStoreQueueConfigDTO.setDistantConstraint(false);
        wxStoreConfigDTO.setWxStoreQueueConfigDTO(wxStoreQueueConfigDTO);
        expectedResult.setStoreConfigDTOS(Arrays.asList(wxStoreConfigDTO));
        expectedResult.setStoreCount(0);

        // Configure OrganizationClientService.queryBrandByGuid(...).
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("cb936710-e9e4-4867-8af0-2e8d387bb1cb");
        brandDTO.setUuid("786a5e3c-bdc7-4910-a396-08c6463816f6");
        brandDTO.setName("brandName");
        brandDTO.setDescription("description");
        brandDTO.setLogoUrl("brandLogoUrl");
        when(mockOrganizationClientService.queryBrandByGuid("belongBrandGuid")).thenReturn(brandDTO);

        // Configure OrganizationClientService.queryStoreByCityAndBrand(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setProvinceCode("provinceCode");
        storeDTO.setProvinceName("provinceName");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setCountyName("countyName");
        storeDTO.setAddressDetail("address");
        storeDTO.setLongitude("key");
        storeDTO.setLatitude("key");
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO);
        when(mockOrganizationClientService.queryStoreByCityAndBrand(any(StoreDTO.class))).thenReturn(storeDTOS);

        when(mockWxConfigOverviewService.listByStoreGuidList(Arrays.asList("value")))
                .thenReturn(Collections.emptyList());

        // Configure WxQueueConfigService.list(...).
        final WxQueueConfigDO wxQueueConfigDO = new WxQueueConfigDO();
        wxQueueConfigDO.setId(0L);
        wxQueueConfigDO.setStoreGuid("storeGuid");
        wxQueueConfigDO.setIsQueueOpen(0);
        wxQueueConfigDO.setDistant(0);
        wxQueueConfigDO.setDistantConstraint(0);
        final List<WxQueueConfigDO> wxQueueConfigDOS = Arrays.asList(wxQueueConfigDO);
        when(mockWxQueueConfigService.list(any(LambdaQueryWrapper.class))).thenReturn(wxQueueConfigDOS);

        // Configure MapUtil.geoLocation(...).
        final GeoCodeRespDTO geoCodeRespDTO = new GeoCodeRespDTO();
        final GeoCodeDTO geoCodeDTO = new GeoCodeDTO();
        geoCodeDTO.setCity("city");
        geoCodeDTO.setDistrict("district");
        geoCodeDTO.setNumber("number");
        geoCodeDTO.setLocation("location");
        geoCodeRespDTO.setGeoCodeDTOList(Arrays.asList(geoCodeDTO));
        when(mockMapUtil.geoLocation("address")).thenReturn(geoCodeRespDTO);

        // Configure MapUtil.splitLocation(...).
        final Pair<BigDecimal, BigDecimal> bigDecimalBigDecimalPair = new Pair<>(new BigDecimal("0.00"),
                new BigDecimal("0.00"));
        when(mockMapUtil.splitLocation("location")).thenReturn(bigDecimalBigDecimalPair);

        when(mockMapUtil.distant(new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"),
                new BigDecimal("0.00"))).thenReturn(0);
        when(mockQueueClientService.allEmpty(new StoreGuidDTO("guid"))).thenReturn(false);

        // Configure WxStoreTableClientService.listByAndroid(...).
        final TableOrderDTO tableOrderDTO = new TableOrderDTO();
        tableOrderDTO.setTableGuid("tableGuid");
        tableOrderDTO.setAreaName("areaName");
        tableOrderDTO.setAreaGuid("areaGuid");
        tableOrderDTO.setOrderAmount(new BigDecimal("0.00"));
        tableOrderDTO.setStatus(0);
        final List<TableOrderDTO> tableOrderDTOS = Arrays.asList(tableOrderDTO);
        final TableBasicQueryDTO tableBasicQueryDTO = new TableBasicQueryDTO();
        tableBasicQueryDTO.setStoreGuid("guid");
        tableBasicQueryDTO.setAreaGuid("areaGuid");
        tableBasicQueryDTO.setTableGuidList(Arrays.asList("value"));
        when(mockWxStoreTableClientService.listByAndroid(tableBasicQueryDTO)).thenReturn(tableOrderDTOS);

        // Run the test
        final WxStoreListDTO result = wxStoreListServiceImplUnderTest.listStoreConfig(wxPositionDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
    }

    @Test
    public void testListStoreConfig_WxQueueConfigServiceReturnsNoItems() {
        // Setup
        final WxPositionDTO wxPositionDTO = new WxPositionDTO(new BigDecimal("0.00"), new BigDecimal("0.00"),
                "cityCode", "cityName");
        final WxStoreListDTO expectedResult = new WxStoreListDTO();
        expectedResult.setEnterpriseGuid("enterpriseGuid");
        expectedResult.setBrandGuid("belongBrandGuid");
        expectedResult.setBrandName("brandName");
        expectedResult.setBrandLogoUrl("brandLogoUrl");
        final WxStoreConfigDTO wxStoreConfigDTO = new WxStoreConfigDTO();
        wxStoreConfigDTO.setStoreGuid("guid");
        wxStoreConfigDTO.setStoreName("name");
        wxStoreConfigDTO.setTel("contactTel");
        wxStoreConfigDTO.setAddress("address");
        wxStoreConfigDTO.setIsOpened(false);
        wxStoreConfigDTO.setDistant(0);
        wxStoreConfigDTO.setLongitude(new BigDecimal("0.00"));
        wxStoreConfigDTO.setLatitude(new BigDecimal("0.00"));
        final WxStoreQueueConfigDTO wxStoreQueueConfigDTO = new WxStoreQueueConfigDTO();
        wxStoreQueueConfigDTO.setShowQueueButton(false);
        wxStoreQueueConfigDTO.setCouldQueue(false);
        wxStoreQueueConfigDTO.setNeedQueue(false);
        wxStoreQueueConfigDTO.setMaxDistant(0);
        wxStoreQueueConfigDTO.setDistantConstraint(false);
        wxStoreConfigDTO.setWxStoreQueueConfigDTO(wxStoreQueueConfigDTO);
        expectedResult.setStoreConfigDTOS(Arrays.asList(wxStoreConfigDTO));
        expectedResult.setStoreCount(0);

        // Configure OrganizationClientService.queryBrandByGuid(...).
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("cb936710-e9e4-4867-8af0-2e8d387bb1cb");
        brandDTO.setUuid("786a5e3c-bdc7-4910-a396-08c6463816f6");
        brandDTO.setName("brandName");
        brandDTO.setDescription("description");
        brandDTO.setLogoUrl("brandLogoUrl");
        when(mockOrganizationClientService.queryBrandByGuid("belongBrandGuid")).thenReturn(brandDTO);

        // Configure OrganizationClientService.queryStoreByCityAndBrand(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setProvinceCode("provinceCode");
        storeDTO.setProvinceName("provinceName");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setCountyName("countyName");
        storeDTO.setAddressDetail("address");
        storeDTO.setLongitude("key");
        storeDTO.setLatitude("key");
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO);
        when(mockOrganizationClientService.queryStoreByCityAndBrand(any(StoreDTO.class))).thenReturn(storeDTOS);

        // Configure WxConfigOverviewService.listByStoreGuidList(...).
        final WxStoreStatusRespDTO wxStoreStatusRespDTO = new WxStoreStatusRespDTO();
        wxStoreStatusRespDTO.setGuid("c3962094-7841-42da-93b3-cdce202a3931");
        wxStoreStatusRespDTO.setStoreGuid("storeGuid");
        wxStoreStatusRespDTO.setStoreName("storeName");
        wxStoreStatusRespDTO.setIsOpened(0);
        wxStoreStatusRespDTO.setQueueUpStatus(0);
        final List<WxStoreStatusRespDTO> wxStoreStatusRespDTOS = Arrays.asList(wxStoreStatusRespDTO);
        when(mockWxConfigOverviewService.listByStoreGuidList(Arrays.asList("value"))).thenReturn(wxStoreStatusRespDTOS);

        when(mockWxQueueConfigService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure MapUtil.geoLocation(...).
        final GeoCodeRespDTO geoCodeRespDTO = new GeoCodeRespDTO();
        final GeoCodeDTO geoCodeDTO = new GeoCodeDTO();
        geoCodeDTO.setCity("city");
        geoCodeDTO.setDistrict("district");
        geoCodeDTO.setNumber("number");
        geoCodeDTO.setLocation("location");
        geoCodeRespDTO.setGeoCodeDTOList(Arrays.asList(geoCodeDTO));
        when(mockMapUtil.geoLocation("address")).thenReturn(geoCodeRespDTO);

        // Configure MapUtil.splitLocation(...).
        final Pair<BigDecimal, BigDecimal> bigDecimalBigDecimalPair = new Pair<>(new BigDecimal("0.00"),
                new BigDecimal("0.00"));
        when(mockMapUtil.splitLocation("location")).thenReturn(bigDecimalBigDecimalPair);

        when(mockMapUtil.distant(new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"),
                new BigDecimal("0.00"))).thenReturn(0);
        when(mockQueueClientService.allEmpty(new StoreGuidDTO("guid"))).thenReturn(false);

        // Configure WxStoreTableClientService.listByAndroid(...).
        final TableOrderDTO tableOrderDTO = new TableOrderDTO();
        tableOrderDTO.setTableGuid("tableGuid");
        tableOrderDTO.setAreaName("areaName");
        tableOrderDTO.setAreaGuid("areaGuid");
        tableOrderDTO.setOrderAmount(new BigDecimal("0.00"));
        tableOrderDTO.setStatus(0);
        final List<TableOrderDTO> tableOrderDTOS = Arrays.asList(tableOrderDTO);
        final TableBasicQueryDTO tableBasicQueryDTO = new TableBasicQueryDTO();
        tableBasicQueryDTO.setStoreGuid("guid");
        tableBasicQueryDTO.setAreaGuid("areaGuid");
        tableBasicQueryDTO.setTableGuidList(Arrays.asList("value"));
        when(mockWxStoreTableClientService.listByAndroid(tableBasicQueryDTO)).thenReturn(tableOrderDTOS);

        // Run the test
        final WxStoreListDTO result = wxStoreListServiceImplUnderTest.listStoreConfig(wxPositionDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
    }

    @Test
    public void testListStoreConfig_WxStoreTableClientServiceReturnsNoItems() {
        // Setup
        final WxPositionDTO wxPositionDTO = new WxPositionDTO(new BigDecimal("0.00"), new BigDecimal("0.00"),
                "cityCode", "cityName");
        final WxStoreListDTO expectedResult = new WxStoreListDTO();
        expectedResult.setEnterpriseGuid("enterpriseGuid");
        expectedResult.setBrandGuid("belongBrandGuid");
        expectedResult.setBrandName("brandName");
        expectedResult.setBrandLogoUrl("brandLogoUrl");
        final WxStoreConfigDTO wxStoreConfigDTO = new WxStoreConfigDTO();
        wxStoreConfigDTO.setStoreGuid("guid");
        wxStoreConfigDTO.setStoreName("name");
        wxStoreConfigDTO.setTel("contactTel");
        wxStoreConfigDTO.setAddress("address");
        wxStoreConfigDTO.setIsOpened(false);
        wxStoreConfigDTO.setDistant(0);
        wxStoreConfigDTO.setLongitude(new BigDecimal("0.00"));
        wxStoreConfigDTO.setLatitude(new BigDecimal("0.00"));
        final WxStoreQueueConfigDTO wxStoreQueueConfigDTO = new WxStoreQueueConfigDTO();
        wxStoreQueueConfigDTO.setShowQueueButton(false);
        wxStoreQueueConfigDTO.setCouldQueue(false);
        wxStoreQueueConfigDTO.setNeedQueue(false);
        wxStoreQueueConfigDTO.setMaxDistant(0);
        wxStoreQueueConfigDTO.setDistantConstraint(false);
        wxStoreConfigDTO.setWxStoreQueueConfigDTO(wxStoreQueueConfigDTO);
        expectedResult.setStoreConfigDTOS(Arrays.asList(wxStoreConfigDTO));
        expectedResult.setStoreCount(0);

        // Configure OrganizationClientService.queryBrandByGuid(...).
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("cb936710-e9e4-4867-8af0-2e8d387bb1cb");
        brandDTO.setUuid("786a5e3c-bdc7-4910-a396-08c6463816f6");
        brandDTO.setName("brandName");
        brandDTO.setDescription("description");
        brandDTO.setLogoUrl("brandLogoUrl");
        when(mockOrganizationClientService.queryBrandByGuid("belongBrandGuid")).thenReturn(brandDTO);

        // Configure OrganizationClientService.queryStoreByCityAndBrand(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setProvinceCode("provinceCode");
        storeDTO.setProvinceName("provinceName");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setCountyName("countyName");
        storeDTO.setAddressDetail("address");
        storeDTO.setLongitude("key");
        storeDTO.setLatitude("key");
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO);
        when(mockOrganizationClientService.queryStoreByCityAndBrand(any(StoreDTO.class))).thenReturn(storeDTOS);

        // Configure WxConfigOverviewService.listByStoreGuidList(...).
        final WxStoreStatusRespDTO wxStoreStatusRespDTO = new WxStoreStatusRespDTO();
        wxStoreStatusRespDTO.setGuid("c3962094-7841-42da-93b3-cdce202a3931");
        wxStoreStatusRespDTO.setStoreGuid("storeGuid");
        wxStoreStatusRespDTO.setStoreName("storeName");
        wxStoreStatusRespDTO.setIsOpened(0);
        wxStoreStatusRespDTO.setQueueUpStatus(0);
        final List<WxStoreStatusRespDTO> wxStoreStatusRespDTOS = Arrays.asList(wxStoreStatusRespDTO);
        when(mockWxConfigOverviewService.listByStoreGuidList(Arrays.asList("value"))).thenReturn(wxStoreStatusRespDTOS);

        // Configure WxQueueConfigService.list(...).
        final WxQueueConfigDO wxQueueConfigDO = new WxQueueConfigDO();
        wxQueueConfigDO.setId(0L);
        wxQueueConfigDO.setStoreGuid("storeGuid");
        wxQueueConfigDO.setIsQueueOpen(0);
        wxQueueConfigDO.setDistant(0);
        wxQueueConfigDO.setDistantConstraint(0);
        final List<WxQueueConfigDO> wxQueueConfigDOS = Arrays.asList(wxQueueConfigDO);
        when(mockWxQueueConfigService.list(any(LambdaQueryWrapper.class))).thenReturn(wxQueueConfigDOS);

        // Configure MapUtil.geoLocation(...).
        final GeoCodeRespDTO geoCodeRespDTO = new GeoCodeRespDTO();
        final GeoCodeDTO geoCodeDTO = new GeoCodeDTO();
        geoCodeDTO.setCity("city");
        geoCodeDTO.setDistrict("district");
        geoCodeDTO.setNumber("number");
        geoCodeDTO.setLocation("location");
        geoCodeRespDTO.setGeoCodeDTOList(Arrays.asList(geoCodeDTO));
        when(mockMapUtil.geoLocation("address")).thenReturn(geoCodeRespDTO);

        // Configure MapUtil.splitLocation(...).
        final Pair<BigDecimal, BigDecimal> bigDecimalBigDecimalPair = new Pair<>(new BigDecimal("0.00"),
                new BigDecimal("0.00"));
        when(mockMapUtil.splitLocation("location")).thenReturn(bigDecimalBigDecimalPair);

        when(mockMapUtil.distant(new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"),
                new BigDecimal("0.00"))).thenReturn(0);
        when(mockQueueClientService.allEmpty(new StoreGuidDTO("guid"))).thenReturn(false);

        // Configure WxStoreTableClientService.listByAndroid(...).
        final TableBasicQueryDTO tableBasicQueryDTO = new TableBasicQueryDTO();
        tableBasicQueryDTO.setStoreGuid("guid");
        tableBasicQueryDTO.setAreaGuid("areaGuid");
        tableBasicQueryDTO.setTableGuidList(Arrays.asList("value"));
        when(mockWxStoreTableClientService.listByAndroid(tableBasicQueryDTO)).thenReturn(Collections.emptyList());

        // Run the test
        final WxStoreListDTO result = wxStoreListServiceImplUnderTest.listStoreConfig(wxPositionDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
    }

    @Test
    public void testListStoreCity() {
        // Setup
        final WxPositionDTO wxPositionDTO = new WxPositionDTO(new BigDecimal("0.00"), new BigDecimal("0.00"),
                "cityCode", "cityName");
        final WxStoreCityListRespDTO expectedResult = new WxStoreCityListRespDTO();
        final WxStoreCityDTO wxStoreCityDTO = new WxStoreCityDTO();
        wxStoreCityDTO.setCityName("cityName");
        wxStoreCityDTO.setCode("code");
        wxStoreCityDTO.setCount(0);
        expectedResult.setCityList(Arrays.asList(wxStoreCityDTO));
        final WxStoreCityDTO wxStoreCityDTO1 = new WxStoreCityDTO();
        wxStoreCityDTO1.setCityName("cityName");
        wxStoreCityDTO1.setCode("code");
        wxStoreCityDTO1.setCount(0);
        expectedResult.setOtherCities(Arrays.asList(wxStoreCityDTO1));
        expectedResult.setTotalCount(0);

        // Configure OrganizationClientService.queryStoreByCityAndBrand(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setProvinceCode("provinceCode");
        storeDTO.setProvinceName("provinceName");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setCountyName("countyName");
        storeDTO.setAddressDetail("address");
        storeDTO.setLongitude("key");
        storeDTO.setLatitude("key");
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO);
        when(mockOrganizationClientService.queryStoreByCityAndBrand(any(StoreDTO.class))).thenReturn(storeDTOS);

        // Run the test
        final WxStoreCityListRespDTO result = wxStoreListServiceImplUnderTest.listStoreCity(wxPositionDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
    }

    @Test
    public void testListStoreCity_OrganizationClientServiceReturnsNoItems() {
        // Setup
        final WxPositionDTO wxPositionDTO = new WxPositionDTO(new BigDecimal("0.00"), new BigDecimal("0.00"),
                "cityCode", "cityName");
        final WxStoreCityListRespDTO expectedResult = new WxStoreCityListRespDTO();
        final WxStoreCityDTO wxStoreCityDTO = new WxStoreCityDTO();
        wxStoreCityDTO.setCityName("cityName");
        wxStoreCityDTO.setCode("code");
        wxStoreCityDTO.setCount(0);
        expectedResult.setCityList(Arrays.asList(wxStoreCityDTO));
        final WxStoreCityDTO wxStoreCityDTO1 = new WxStoreCityDTO();
        wxStoreCityDTO1.setCityName("cityName");
        wxStoreCityDTO1.setCode("code");
        wxStoreCityDTO1.setCount(0);
        expectedResult.setOtherCities(Arrays.asList(wxStoreCityDTO1));
        expectedResult.setTotalCount(0);

        when(mockOrganizationClientService.queryStoreByCityAndBrand(any(StoreDTO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final WxStoreCityListRespDTO result = wxStoreListServiceImplUnderTest.listStoreCity(wxPositionDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
    }
}
