package com.holderzone.saas.store.business.service.impl;

import com.holderzone.saas.store.business.config.RedisIDGenerator;
import com.holderzone.saas.store.business.mapper.ReasonMapper;
import com.holderzone.saas.store.dto.business.reason.ReasonCopyReqDTO;
import com.holderzone.saas.store.dto.business.reason.ReasonDTO;
import com.holderzone.saas.store.dto.business.reason.ReasonTypeDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ReasonServiceImplTest {

    @Mock
    private ReasonMapper mockReasonMapper;
    @Mock
    private RedisIDGenerator mockRedisIDGenerator;

    private ReasonServiceImpl reasonServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        reasonServiceImplUnderTest = new ReasonServiceImpl(mockReasonMapper, mockRedisIDGenerator);
    }

    @Test
    public void testFindReason() {
        // Setup
        final ReasonDTO reasonDTO = new ReasonDTO("reasonGuid", "storeGuid", 0, "reasonType", "reason");
        final List<ReasonDTO> expectedResult = Arrays.asList(
                new ReasonDTO("reasonGuid", "storeGuid", 0, "reasonType", "reason"));

        // Configure ReasonMapper.findReason(...).
        final List<ReasonDTO> reasonDTOS = Arrays.asList(
                new ReasonDTO("reasonGuid", "storeGuid", 0, "reasonType", "reason"));
        when(mockReasonMapper.findReason(
                new ReasonDTO("reasonGuid", "storeGuid", 0, "reasonType", "reason"))).thenReturn(reasonDTOS);

        // Run the test
        final List<ReasonDTO> result = reasonServiceImplUnderTest.findReason(reasonDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testFindReason_ReasonMapperReturnsNoItems() {
        // Setup
        final ReasonDTO reasonDTO = new ReasonDTO("reasonGuid", "storeGuid", 0, "reasonType", "reason");
        when(mockReasonMapper.findReason(
                new ReasonDTO("reasonGuid", "storeGuid", 0, "reasonType", "reason")))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<ReasonDTO> result = reasonServiceImplUnderTest.findReason(reasonDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testInsertReason() {
        // Setup
        final ReasonDTO reasonDTO = new ReasonDTO("reasonGuid", "storeGuid", 0, "reasonType", "reason");
        when(mockRedisIDGenerator.getSingle("hsb_reason_reason")).thenReturn(0L);

        // Run the test
        reasonServiceImplUnderTest.insertReason(reasonDTO);

        // Verify the results
    }

    @Test
    public void testDeleteReason() {
        // Setup
        final ReasonDTO reasonDTO = new ReasonDTO("reasonGuid", "storeGuid", 0, "reasonType", "reason");

        // Run the test
        reasonServiceImplUnderTest.deleteReason(reasonDTO);

        // Verify the results
    }

    @Test
    public void testUpdateReason() {
        // Setup
        final ReasonDTO reasonDTO = new ReasonDTO("reasonGuid", "storeGuid", 0, "reasonType", "reason");

        // Run the test
        reasonServiceImplUnderTest.updateReason(reasonDTO);

        // Verify the results
    }

    @Test
    public void testUpdateCount() {
        // Setup
        // Run the test
        reasonServiceImplUnderTest.updateCount(Arrays.asList("value"));

        // Verify the results
        verify(mockReasonMapper).updateCount(Arrays.asList("value"));
    }

    @Test
    public void testFindReasonType() {
        // Setup
        final List<ReasonTypeDTO> expectedResult = Arrays.asList(new ReasonTypeDTO("reasonTypeGuid", "reasonType", 0));

        // Run the test
        final List<ReasonTypeDTO> result = reasonServiceImplUnderTest.findReasonType();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testFindReasonTypeSuperMarket() {
        // Setup
        final List<ReasonTypeDTO> expectedResult = Arrays.asList(new ReasonTypeDTO("reasonTypeGuid", "reasonType", 0));

        // Run the test
        final List<ReasonTypeDTO> result = reasonServiceImplUnderTest.findReasonTypeSuperMarket();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCopyReason() {
        // Setup
        final ReasonCopyReqDTO copyReqDTO = new ReasonCopyReqDTO();
        final ReasonDTO reasonDTO = new ReasonDTO();
        reasonDTO.setReasonGuid("reasonGuid");
        reasonDTO.setStoreGuid("storeGuid");
        reasonDTO.setReasonTypeCode(0);
        reasonDTO.setReasonType("reasonType");
        reasonDTO.setReason("reason");
        copyReqDTO.setReasonDTOList(Arrays.asList(reasonDTO));
        copyReqDTO.setStoreGuidList(Arrays.asList("value"));

        when(mockRedisIDGenerator.getSingle("hsb_reason_reason")).thenReturn(0L);

        // Run the test
        final Boolean result = reasonServiceImplUnderTest.copyReason(copyReqDTO);

        // Verify the results
        assertThat(result).isFalse();
    }
}
