package com.holderzone.saas.store.business.queue.utils;

import com.holderzone.saas.store.business.queue.helper.DynamicHelper;
import org.junit.Test;

import java.util.Arrays;
import java.util.Collections;

import static org.assertj.core.api.Assertions.assertThat;

public class QueuedUtilsTest {

    @Test
    public void testNextTypeGuid() {
        assertThat(QueuedUtils.nextTypeGuid()).isEqualTo("result");
    }

    @Test
    public void testNextConfigGuid1() {
        assertThat(QueuedUtils.nextConfigGuid()).isEqualTo("result");
    }

    @Test
    public void testNextConfigGuid2() {
        assertThat(QueuedUtils.nextConfigGuid(0)).isEqualTo(Arrays.asList("value"));
        assertThat(QueuedUtils.nextConfigGuid(0)).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testNextRecordGuid() {
        assertThat(QueuedUtils.nextRecordGuid()).isEqualTo("result");
    }

    @Test
    public void testDynamicHelper() {
        // Setup
        // Run the test
        final DynamicHelper result = QueuedUtils.dynamicHelper();

        // Verify the results
    }
}
