package com.holderzone.saas.store.business.service.impl;

import com.holderzone.saas.store.business.entity.domain.EstimateDishDO;
import com.holderzone.saas.store.business.entity.domain.EstimateRecordDO;
import com.holderzone.saas.store.business.entity.dto.*;
import com.holderzone.saas.store.business.mapper.EstimateMapper;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class EstimateServiceImplTest {

    @Mock
    private EstimateMapper mockEstimateMapper;

    private EstimateServiceImpl estimateServiceImplUnderTest;

    @Before
    public void setUp() {
        estimateServiceImplUnderTest = new EstimateServiceImpl(mockEstimateMapper);
    }

    @Test
    public void testCreateOrReplace() {
        // Setup
        final EstimateRecordCreateDTO estimateRecordCreateDTO = new EstimateRecordCreateDTO();
        estimateRecordCreateDTO.setStoreGuid("storeGuid");
        estimateRecordCreateDTO.setCreateUserGuid("createUserGuid");
        estimateRecordCreateDTO.setCreateUserName("createUserName");
        estimateRecordCreateDTO.setBusinessDay(LocalDate.of(2020, 1, 1));
        final EstimateDishCreateDTO estimateDishCreateDTO = new EstimateDishCreateDTO();
        estimateRecordCreateDTO.setEstimateDishes(Arrays.asList(estimateDishCreateDTO));

        // Configure EstimateMapper.findByBusinessDay(...).
        final EstimateRecordDO estimateRecordDO = new EstimateRecordDO();
        estimateRecordDO.setStoreGuid("storeGuid");
        estimateRecordDO.setEstimateRecordGuid("estimateRecordGuid");
        estimateRecordDO.setCreateUserGuid("createUserGuid");
        estimateRecordDO.setCreateUserName("createUserName");
        estimateRecordDO.setBusinessDay(LocalDate.of(2020, 1, 1));
        final EstimateRecordDO estimateRecordDO1 = new EstimateRecordDO();
        estimateRecordDO1.setStoreGuid("storeGuid");
        estimateRecordDO1.setEstimateRecordGuid("estimateRecordGuid");
        estimateRecordDO1.setCreateUserGuid("createUserGuid");
        estimateRecordDO1.setCreateUserName("createUserName");
        estimateRecordDO1.setBusinessDay(LocalDate.of(2020, 1, 1));
        when(mockEstimateMapper.findByBusinessDay(estimateRecordDO1)).thenReturn(estimateRecordDO);

        // Run the test
        estimateServiceImplUnderTest.createOrReplace(estimateRecordCreateDTO);

        // Verify the results
        verify(mockEstimateMapper).batchDeleteDishes("estimateRecordGuid");
        verify(mockEstimateMapper).batchInsertDishes(Arrays.asList(
                new EstimateDishDO("estimateRecordGuid", 0L, new BigDecimal("0.00"), new BigDecimal("0.00"), 0)));
    }

    @Test
    public void testCreateOrReplace_EstimateMapperFindByBusinessDayReturnsNull() {
        // Setup
        final EstimateRecordCreateDTO estimateRecordCreateDTO = new EstimateRecordCreateDTO();
        estimateRecordCreateDTO.setStoreGuid("storeGuid");
        estimateRecordCreateDTO.setCreateUserGuid("createUserGuid");
        estimateRecordCreateDTO.setCreateUserName("createUserName");
        estimateRecordCreateDTO.setBusinessDay(LocalDate.of(2020, 1, 1));
        final EstimateDishCreateDTO estimateDishCreateDTO = new EstimateDishCreateDTO();
        estimateRecordCreateDTO.setEstimateDishes(Arrays.asList(estimateDishCreateDTO));

        // Configure EstimateMapper.findByBusinessDay(...).
        final EstimateRecordDO estimateRecordDO = new EstimateRecordDO();
        estimateRecordDO.setStoreGuid("storeGuid");
        estimateRecordDO.setEstimateRecordGuid("estimateRecordGuid");
        estimateRecordDO.setCreateUserGuid("createUserGuid");
        estimateRecordDO.setCreateUserName("createUserName");
        estimateRecordDO.setBusinessDay(LocalDate.of(2020, 1, 1));
        when(mockEstimateMapper.findByBusinessDay(estimateRecordDO)).thenReturn(null);

        // Run the test
        estimateServiceImplUnderTest.createOrReplace(estimateRecordCreateDTO);

        // Verify the results
        // Confirm EstimateMapper.insertRecord(...).
        final EstimateRecordDO estimateRecordDO1 = new EstimateRecordDO();
        estimateRecordDO1.setStoreGuid("storeGuid");
        estimateRecordDO1.setEstimateRecordGuid("estimateRecordGuid");
        estimateRecordDO1.setCreateUserGuid("createUserGuid");
        estimateRecordDO1.setCreateUserName("createUserName");
        estimateRecordDO1.setBusinessDay(LocalDate.of(2020, 1, 1));
        verify(mockEstimateMapper).insertRecord(estimateRecordDO1);
        verify(mockEstimateMapper).batchInsertDishes(Arrays.asList(
                new EstimateDishDO("estimateRecordGuid", 0L, new BigDecimal("0.00"), new BigDecimal("0.00"), 0)));
    }

    @Test
    public void testGetByBusinessDay() {
        // Setup
        final EstimateRecordQuerryDTO estimateRecordQuerryDTO = new EstimateRecordQuerryDTO("storeGuid",
                LocalDate.of(2020, 1, 1));
        final EstimateRecordDTO expectedResult = new EstimateRecordDTO();
        final EstimateDishDTO estimateDishDTO = new EstimateDishDTO();
        estimateDishDTO.setEstimateRecordGuid("estimateRecordGuid");
        estimateDishDTO.setDishId(0L);
        estimateDishDTO.setEstimateCount(new BigDecimal("0.00"));
        estimateDishDTO.setWarningCount(new BigDecimal("0.00"));
        expectedResult.dishes(Arrays.asList(estimateDishDTO));

        // Configure EstimateMapper.findByBusinessDay(...).
        final EstimateRecordDO estimateRecordDO = new EstimateRecordDO();
        estimateRecordDO.setStoreGuid("storeGuid");
        estimateRecordDO.setEstimateRecordGuid("estimateRecordGuid");
        estimateRecordDO.setCreateUserGuid("createUserGuid");
        estimateRecordDO.setCreateUserName("createUserName");
        estimateRecordDO.setBusinessDay(LocalDate.of(2020, 1, 1));
        final EstimateRecordDO estimateRecordDO1 = new EstimateRecordDO();
        estimateRecordDO1.setStoreGuid("storeGuid");
        estimateRecordDO1.setEstimateRecordGuid("estimateRecordGuid");
        estimateRecordDO1.setCreateUserGuid("createUserGuid");
        estimateRecordDO1.setCreateUserName("createUserName");
        estimateRecordDO1.setBusinessDay(LocalDate.of(2020, 1, 1));
        when(mockEstimateMapper.findByBusinessDay(estimateRecordDO1)).thenReturn(estimateRecordDO);

        // Configure EstimateMapper.queryDishesByRecordGuid(...).
        final List<EstimateDishDO> estimateDishDOS = Arrays.asList(
                new EstimateDishDO("estimateRecordGuid", 0L, new BigDecimal("0.00"), new BigDecimal("0.00"), 0));
        when(mockEstimateMapper.queryDishesByRecordGuid("estimateRecordGuid")).thenReturn(estimateDishDOS);

        // Run the test
        final EstimateRecordDTO result = estimateServiceImplUnderTest.getByBusinessDay(estimateRecordQuerryDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetByBusinessDay_EstimateMapperFindByBusinessDayReturnsNull() {
        // Setup
        final EstimateRecordQuerryDTO estimateRecordQuerryDTO = new EstimateRecordQuerryDTO("storeGuid",
                LocalDate.of(2020, 1, 1));
        final EstimateRecordDTO expectedResult = new EstimateRecordDTO();
        final EstimateDishDTO estimateDishDTO = new EstimateDishDTO();
        estimateDishDTO.setEstimateRecordGuid("estimateRecordGuid");
        estimateDishDTO.setDishId(0L);
        estimateDishDTO.setEstimateCount(new BigDecimal("0.00"));
        estimateDishDTO.setWarningCount(new BigDecimal("0.00"));
        expectedResult.dishes(Arrays.asList(estimateDishDTO));

        // Configure EstimateMapper.findByBusinessDay(...).
        final EstimateRecordDO estimateRecordDO = new EstimateRecordDO();
        estimateRecordDO.setStoreGuid("storeGuid");
        estimateRecordDO.setEstimateRecordGuid("estimateRecordGuid");
        estimateRecordDO.setCreateUserGuid("createUserGuid");
        estimateRecordDO.setCreateUserName("createUserName");
        estimateRecordDO.setBusinessDay(LocalDate.of(2020, 1, 1));
        when(mockEstimateMapper.findByBusinessDay(estimateRecordDO)).thenReturn(null);

        // Run the test
        final EstimateRecordDTO result = estimateServiceImplUnderTest.getByBusinessDay(estimateRecordQuerryDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetByBusinessDay_EstimateMapperQueryDishesByRecordGuidReturnsNoItems() {
        // Setup
        final EstimateRecordQuerryDTO estimateRecordQuerryDTO = new EstimateRecordQuerryDTO("storeGuid",
                LocalDate.of(2020, 1, 1));
        final EstimateRecordDTO expectedResult = new EstimateRecordDTO();
        final EstimateDishDTO estimateDishDTO = new EstimateDishDTO();
        estimateDishDTO.setEstimateRecordGuid("estimateRecordGuid");
        estimateDishDTO.setDishId(0L);
        estimateDishDTO.setEstimateCount(new BigDecimal("0.00"));
        estimateDishDTO.setWarningCount(new BigDecimal("0.00"));
        expectedResult.dishes(Arrays.asList(estimateDishDTO));

        // Configure EstimateMapper.findByBusinessDay(...).
        final EstimateRecordDO estimateRecordDO = new EstimateRecordDO();
        estimateRecordDO.setStoreGuid("storeGuid");
        estimateRecordDO.setEstimateRecordGuid("estimateRecordGuid");
        estimateRecordDO.setCreateUserGuid("createUserGuid");
        estimateRecordDO.setCreateUserName("createUserName");
        estimateRecordDO.setBusinessDay(LocalDate.of(2020, 1, 1));
        final EstimateRecordDO estimateRecordDO1 = new EstimateRecordDO();
        estimateRecordDO1.setStoreGuid("storeGuid");
        estimateRecordDO1.setEstimateRecordGuid("estimateRecordGuid");
        estimateRecordDO1.setCreateUserGuid("createUserGuid");
        estimateRecordDO1.setCreateUserName("createUserName");
        estimateRecordDO1.setBusinessDay(LocalDate.of(2020, 1, 1));
        when(mockEstimateMapper.findByBusinessDay(estimateRecordDO1)).thenReturn(estimateRecordDO);

        when(mockEstimateMapper.queryDishesByRecordGuid("estimateRecordGuid")).thenReturn(Collections.emptyList());

        // Run the test
        final EstimateRecordDTO result = estimateServiceImplUnderTest.getByBusinessDay(estimateRecordQuerryDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
