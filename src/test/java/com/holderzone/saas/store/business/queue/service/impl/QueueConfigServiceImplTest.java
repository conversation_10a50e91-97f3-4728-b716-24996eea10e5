package com.holderzone.saas.store.business.queue.service.impl;

import com.holderzone.saas.store.business.queue.domain.HolderQueueConfigDO;
import com.holderzone.saas.store.business.queue.mapstruct.QueueConfigMapStruct;
import com.holderzone.saas.store.business.queue.service.remote.ConfigClient;
import com.holderzone.saas.store.dto.config.req.ConfigReqDTO;
import com.holderzone.saas.store.dto.config.resp.ConfigRespDTO;
import com.holderzone.saas.store.dto.queue.StoreConfigDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class QueueConfigServiceImplTest {

    @Mock
    private QueueConfigMapStruct mockMapStruct;
    @Mock
    private ConfigClient mockConfigClient;

    @InjectMocks
    private QueueConfigServiceImpl queueConfigServiceImplUnderTest;

    @Test
    public void testConfig() {
        // Setup
        final StoreConfigDTO dto = new StoreConfigDTO();
        dto.setGuid("1b17767a-7951-49b8-aaa6-8fd773e88f62");
        dto.setStoreGuid("storeGuid");
        dto.setIsEnableEat(false);
        dto.setIsEnableRecovery(false);
        dto.setRecoveryNum("recoveryNum");

        final StoreConfigDTO expectedResult = new StoreConfigDTO();
        expectedResult.setGuid("1b17767a-7951-49b8-aaa6-8fd773e88f62");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setIsEnableEat(false);
        expectedResult.setIsEnableRecovery(false);
        expectedResult.setRecoveryNum("recoveryNum");

        // Configure QueueConfigMapStruct.toDo(...).
        final HolderQueueConfigDO holderQueueConfigDO = new HolderQueueConfigDO();
        holderQueueConfigDO.setGuid("1b17767a-7951-49b8-aaa6-8fd773e88f62");
        holderQueueConfigDO.setStoreGuid("storeGuid");
        holderQueueConfigDO.setIsEnableAutoReset(false);
        holderQueueConfigDO.setAutoResetTiming(LocalTime.of(0, 0, 0));
        holderQueueConfigDO.setIsDeleted(false);
        final StoreConfigDTO configDO = new StoreConfigDTO();
        configDO.setGuid("1b17767a-7951-49b8-aaa6-8fd773e88f62");
        configDO.setStoreGuid("storeGuid");
        configDO.setIsEnableEat(false);
        configDO.setIsEnableRecovery(false);
        configDO.setRecoveryNum("recoveryNum");
        when(mockMapStruct.toDo(configDO)).thenReturn(holderQueueConfigDO);

        // Configure ConfigClient.getConfig(...).
        final ConfigRespDTO configRespDTO = new ConfigRespDTO();
        configRespDTO.setGuid("dfb7dc8c-f5be-48d5-9ed8-f9add8e9c440");
        configRespDTO.setEnterpriseGuid("enterpriseGuid");
        configRespDTO.setStoreGuid("storeGuid");
        configRespDTO.setDicCode(0);
        configRespDTO.setDicName("desc");
        configRespDTO.setDictValue("dictValue");
        configRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        configRespDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        configRespDTO.setIsEnable(0);
        final List<ConfigRespDTO> configRespDTOS = Arrays.asList(configRespDTO);
        final ConfigReqDTO configReqDTO = new ConfigReqDTO();
        configReqDTO.setGuid("b196c0e5-8ece-4ea4-b698-d1d993b94aab");
        configReqDTO.setEnterpriseGuid("enterpriseGuid");
        configReqDTO.setStoreGuid("storeGuid");
        configReqDTO.setDicCode(0);
        configReqDTO.setDicName("dicName");
        when(mockConfigClient.getConfig(configReqDTO)).thenReturn(configRespDTOS);

        // Run the test
        final StoreConfigDTO result = queueConfigServiceImplUnderTest.config(dto);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);

        // Confirm ConfigClient.saveConfig(...).
        final ConfigReqDTO request = new ConfigReqDTO();
        request.setGuid("b196c0e5-8ece-4ea4-b698-d1d993b94aab");
        request.setEnterpriseGuid("enterpriseGuid");
        request.setStoreGuid("storeGuid");
        request.setDicCode(0);
        request.setDicName("dicName");
        verify(mockConfigClient).saveConfig(request);
    }

    @Test
    public void testConfig_ConfigClientGetConfigReturnsNull() {
        // Setup
        final StoreConfigDTO dto = new StoreConfigDTO();
        dto.setGuid("1b17767a-7951-49b8-aaa6-8fd773e88f62");
        dto.setStoreGuid("storeGuid");
        dto.setIsEnableEat(false);
        dto.setIsEnableRecovery(false);
        dto.setRecoveryNum("recoveryNum");

        final StoreConfigDTO expectedResult = new StoreConfigDTO();
        expectedResult.setGuid("1b17767a-7951-49b8-aaa6-8fd773e88f62");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setIsEnableEat(false);
        expectedResult.setIsEnableRecovery(false);
        expectedResult.setRecoveryNum("recoveryNum");

        // Configure QueueConfigMapStruct.toDo(...).
        final HolderQueueConfigDO holderQueueConfigDO = new HolderQueueConfigDO();
        holderQueueConfigDO.setGuid("1b17767a-7951-49b8-aaa6-8fd773e88f62");
        holderQueueConfigDO.setStoreGuid("storeGuid");
        holderQueueConfigDO.setIsEnableAutoReset(false);
        holderQueueConfigDO.setAutoResetTiming(LocalTime.of(0, 0, 0));
        holderQueueConfigDO.setIsDeleted(false);
        final StoreConfigDTO configDO = new StoreConfigDTO();
        configDO.setGuid("1b17767a-7951-49b8-aaa6-8fd773e88f62");
        configDO.setStoreGuid("storeGuid");
        configDO.setIsEnableEat(false);
        configDO.setIsEnableRecovery(false);
        configDO.setRecoveryNum("recoveryNum");
        when(mockMapStruct.toDo(configDO)).thenReturn(holderQueueConfigDO);

        // Configure ConfigClient.getConfig(...).
        final ConfigReqDTO configReqDTO = new ConfigReqDTO();
        configReqDTO.setGuid("b196c0e5-8ece-4ea4-b698-d1d993b94aab");
        configReqDTO.setEnterpriseGuid("enterpriseGuid");
        configReqDTO.setStoreGuid("storeGuid");
        configReqDTO.setDicCode(0);
        configReqDTO.setDicName("dicName");
        when(mockConfigClient.getConfig(configReqDTO)).thenReturn(null);

        // Run the test
        final StoreConfigDTO result = queueConfigServiceImplUnderTest.config(dto);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);

        // Confirm ConfigClient.saveConfig(...).
        final ConfigReqDTO request = new ConfigReqDTO();
        request.setGuid("b196c0e5-8ece-4ea4-b698-d1d993b94aab");
        request.setEnterpriseGuid("enterpriseGuid");
        request.setStoreGuid("storeGuid");
        request.setDicCode(0);
        request.setDicName("dicName");
        verify(mockConfigClient).saveConfig(request);
    }

    @Test
    public void testConfig_ConfigClientGetConfigReturnsNoItems() {
        // Setup
        final StoreConfigDTO dto = new StoreConfigDTO();
        dto.setGuid("1b17767a-7951-49b8-aaa6-8fd773e88f62");
        dto.setStoreGuid("storeGuid");
        dto.setIsEnableEat(false);
        dto.setIsEnableRecovery(false);
        dto.setRecoveryNum("recoveryNum");

        final StoreConfigDTO expectedResult = new StoreConfigDTO();
        expectedResult.setGuid("1b17767a-7951-49b8-aaa6-8fd773e88f62");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setIsEnableEat(false);
        expectedResult.setIsEnableRecovery(false);
        expectedResult.setRecoveryNum("recoveryNum");

        // Configure QueueConfigMapStruct.toDo(...).
        final HolderQueueConfigDO holderQueueConfigDO = new HolderQueueConfigDO();
        holderQueueConfigDO.setGuid("1b17767a-7951-49b8-aaa6-8fd773e88f62");
        holderQueueConfigDO.setStoreGuid("storeGuid");
        holderQueueConfigDO.setIsEnableAutoReset(false);
        holderQueueConfigDO.setAutoResetTiming(LocalTime.of(0, 0, 0));
        holderQueueConfigDO.setIsDeleted(false);
        final StoreConfigDTO configDO = new StoreConfigDTO();
        configDO.setGuid("1b17767a-7951-49b8-aaa6-8fd773e88f62");
        configDO.setStoreGuid("storeGuid");
        configDO.setIsEnableEat(false);
        configDO.setIsEnableRecovery(false);
        configDO.setRecoveryNum("recoveryNum");
        when(mockMapStruct.toDo(configDO)).thenReturn(holderQueueConfigDO);

        // Configure ConfigClient.getConfig(...).
        final ConfigReqDTO configReqDTO = new ConfigReqDTO();
        configReqDTO.setGuid("b196c0e5-8ece-4ea4-b698-d1d993b94aab");
        configReqDTO.setEnterpriseGuid("enterpriseGuid");
        configReqDTO.setStoreGuid("storeGuid");
        configReqDTO.setDicCode(0);
        configReqDTO.setDicName("dicName");
        when(mockConfigClient.getConfig(configReqDTO)).thenReturn(Collections.emptyList());

        // Run the test
        final StoreConfigDTO result = queueConfigServiceImplUnderTest.config(dto);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);

        // Confirm ConfigClient.saveConfig(...).
        final ConfigReqDTO request = new ConfigReqDTO();
        request.setGuid("b196c0e5-8ece-4ea4-b698-d1d993b94aab");
        request.setEnterpriseGuid("enterpriseGuid");
        request.setStoreGuid("storeGuid");
        request.setDicCode(0);
        request.setDicName("dicName");
        verify(mockConfigClient).saveConfig(request);
    }

    @Test
    public void testObtain() {
        // Setup
        final StoreConfigDTO expectedResult = new StoreConfigDTO();
        expectedResult.setGuid("1b17767a-7951-49b8-aaa6-8fd773e88f62");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setIsEnableEat(false);
        expectedResult.setIsEnableRecovery(false);
        expectedResult.setRecoveryNum("recoveryNum");

        // Configure QueueConfigMapStruct.toDo(...).
        final HolderQueueConfigDO holderQueueConfigDO = new HolderQueueConfigDO();
        holderQueueConfigDO.setGuid("1b17767a-7951-49b8-aaa6-8fd773e88f62");
        holderQueueConfigDO.setStoreGuid("storeGuid");
        holderQueueConfigDO.setIsEnableAutoReset(false);
        holderQueueConfigDO.setAutoResetTiming(LocalTime.of(0, 0, 0));
        holderQueueConfigDO.setIsDeleted(false);
        final StoreConfigDTO configDO = new StoreConfigDTO();
        configDO.setGuid("1b17767a-7951-49b8-aaa6-8fd773e88f62");
        configDO.setStoreGuid("storeGuid");
        configDO.setIsEnableEat(false);
        configDO.setIsEnableRecovery(false);
        configDO.setRecoveryNum("recoveryNum");
        when(mockMapStruct.toDo(configDO)).thenReturn(holderQueueConfigDO);

        // Configure ConfigClient.getConfig(...).
        final ConfigRespDTO configRespDTO = new ConfigRespDTO();
        configRespDTO.setGuid("dfb7dc8c-f5be-48d5-9ed8-f9add8e9c440");
        configRespDTO.setEnterpriseGuid("enterpriseGuid");
        configRespDTO.setStoreGuid("storeGuid");
        configRespDTO.setDicCode(0);
        configRespDTO.setDicName("desc");
        configRespDTO.setDictValue("dictValue");
        configRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        configRespDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        configRespDTO.setIsEnable(0);
        final List<ConfigRespDTO> configRespDTOS = Arrays.asList(configRespDTO);
        final ConfigReqDTO configReqDTO = new ConfigReqDTO();
        configReqDTO.setGuid("b196c0e5-8ece-4ea4-b698-d1d993b94aab");
        configReqDTO.setEnterpriseGuid("enterpriseGuid");
        configReqDTO.setStoreGuid("storeGuid");
        configReqDTO.setDicCode(0);
        configReqDTO.setDicName("dicName");
        when(mockConfigClient.getConfig(configReqDTO)).thenReturn(configRespDTOS);

        // Configure QueueConfigMapStruct.toDto(...).
        final StoreConfigDTO storeConfigDTO = new StoreConfigDTO();
        storeConfigDTO.setGuid("1b17767a-7951-49b8-aaa6-8fd773e88f62");
        storeConfigDTO.setStoreGuid("storeGuid");
        storeConfigDTO.setIsEnableEat(false);
        storeConfigDTO.setIsEnableRecovery(false);
        storeConfigDTO.setRecoveryNum("recoveryNum");
        final HolderQueueConfigDO configDO1 = new HolderQueueConfigDO();
        configDO1.setGuid("1b17767a-7951-49b8-aaa6-8fd773e88f62");
        configDO1.setStoreGuid("storeGuid");
        configDO1.setIsEnableAutoReset(false);
        configDO1.setAutoResetTiming(LocalTime.of(0, 0, 0));
        configDO1.setIsDeleted(false);
        when(mockMapStruct.toDto(configDO1)).thenReturn(storeConfigDTO);

        // Run the test
        final StoreConfigDTO result = queueConfigServiceImplUnderTest.obtain("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);

        // Confirm ConfigClient.saveConfig(...).
        final ConfigReqDTO request = new ConfigReqDTO();
        request.setGuid("b196c0e5-8ece-4ea4-b698-d1d993b94aab");
        request.setEnterpriseGuid("enterpriseGuid");
        request.setStoreGuid("storeGuid");
        request.setDicCode(0);
        request.setDicName("dicName");
        verify(mockConfigClient).saveConfig(request);
    }

    @Test
    public void testObtain_ConfigClientGetConfigReturnsNull() {
        // Setup
        final StoreConfigDTO expectedResult = new StoreConfigDTO();
        expectedResult.setGuid("1b17767a-7951-49b8-aaa6-8fd773e88f62");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setIsEnableEat(false);
        expectedResult.setIsEnableRecovery(false);
        expectedResult.setRecoveryNum("recoveryNum");

        // Configure QueueConfigMapStruct.toDo(...).
        final HolderQueueConfigDO holderQueueConfigDO = new HolderQueueConfigDO();
        holderQueueConfigDO.setGuid("1b17767a-7951-49b8-aaa6-8fd773e88f62");
        holderQueueConfigDO.setStoreGuid("storeGuid");
        holderQueueConfigDO.setIsEnableAutoReset(false);
        holderQueueConfigDO.setAutoResetTiming(LocalTime.of(0, 0, 0));
        holderQueueConfigDO.setIsDeleted(false);
        final StoreConfigDTO configDO = new StoreConfigDTO();
        configDO.setGuid("1b17767a-7951-49b8-aaa6-8fd773e88f62");
        configDO.setStoreGuid("storeGuid");
        configDO.setIsEnableEat(false);
        configDO.setIsEnableRecovery(false);
        configDO.setRecoveryNum("recoveryNum");
        when(mockMapStruct.toDo(configDO)).thenReturn(holderQueueConfigDO);

        // Configure ConfigClient.getConfig(...).
        final ConfigReqDTO configReqDTO = new ConfigReqDTO();
        configReqDTO.setGuid("b196c0e5-8ece-4ea4-b698-d1d993b94aab");
        configReqDTO.setEnterpriseGuid("enterpriseGuid");
        configReqDTO.setStoreGuid("storeGuid");
        configReqDTO.setDicCode(0);
        configReqDTO.setDicName("dicName");
        when(mockConfigClient.getConfig(configReqDTO)).thenReturn(null);

        // Run the test
        final StoreConfigDTO result = queueConfigServiceImplUnderTest.obtain("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);

        // Confirm ConfigClient.saveConfig(...).
        final ConfigReqDTO request = new ConfigReqDTO();
        request.setGuid("b196c0e5-8ece-4ea4-b698-d1d993b94aab");
        request.setEnterpriseGuid("enterpriseGuid");
        request.setStoreGuid("storeGuid");
        request.setDicCode(0);
        request.setDicName("dicName");
        verify(mockConfigClient).saveConfig(request);
    }

    @Test
    public void testObtain_ConfigClientGetConfigReturnsNoItems() {
        // Setup
        final StoreConfigDTO expectedResult = new StoreConfigDTO();
        expectedResult.setGuid("1b17767a-7951-49b8-aaa6-8fd773e88f62");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setIsEnableEat(false);
        expectedResult.setIsEnableRecovery(false);
        expectedResult.setRecoveryNum("recoveryNum");

        // Configure QueueConfigMapStruct.toDo(...).
        final HolderQueueConfigDO holderQueueConfigDO = new HolderQueueConfigDO();
        holderQueueConfigDO.setGuid("1b17767a-7951-49b8-aaa6-8fd773e88f62");
        holderQueueConfigDO.setStoreGuid("storeGuid");
        holderQueueConfigDO.setIsEnableAutoReset(false);
        holderQueueConfigDO.setAutoResetTiming(LocalTime.of(0, 0, 0));
        holderQueueConfigDO.setIsDeleted(false);
        final StoreConfigDTO configDO = new StoreConfigDTO();
        configDO.setGuid("1b17767a-7951-49b8-aaa6-8fd773e88f62");
        configDO.setStoreGuid("storeGuid");
        configDO.setIsEnableEat(false);
        configDO.setIsEnableRecovery(false);
        configDO.setRecoveryNum("recoveryNum");
        when(mockMapStruct.toDo(configDO)).thenReturn(holderQueueConfigDO);

        // Configure ConfigClient.getConfig(...).
        final ConfigReqDTO configReqDTO = new ConfigReqDTO();
        configReqDTO.setGuid("b196c0e5-8ece-4ea4-b698-d1d993b94aab");
        configReqDTO.setEnterpriseGuid("enterpriseGuid");
        configReqDTO.setStoreGuid("storeGuid");
        configReqDTO.setDicCode(0);
        configReqDTO.setDicName("dicName");
        when(mockConfigClient.getConfig(configReqDTO)).thenReturn(Collections.emptyList());

        // Run the test
        final StoreConfigDTO result = queueConfigServiceImplUnderTest.obtain("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);

        // Confirm ConfigClient.saveConfig(...).
        final ConfigReqDTO request = new ConfigReqDTO();
        request.setGuid("b196c0e5-8ece-4ea4-b698-d1d993b94aab");
        request.setEnterpriseGuid("enterpriseGuid");
        request.setStoreGuid("storeGuid");
        request.setDicCode(0);
        request.setDicName("dicName");
        verify(mockConfigClient).saveConfig(request);
    }

    @Test
    public void testDoSave() {
        // Setup
        final HolderQueueConfigDO holderQueueConfigDO = new HolderQueueConfigDO();
        holderQueueConfigDO.setGuid("1b17767a-7951-49b8-aaa6-8fd773e88f62");
        holderQueueConfigDO.setStoreGuid("storeGuid");
        holderQueueConfigDO.setIsEnableAutoReset(false);
        holderQueueConfigDO.setAutoResetTiming(LocalTime.of(0, 0, 0));
        holderQueueConfigDO.setIsDeleted(false);

        // Configure ConfigClient.getConfig(...).
        final ConfigRespDTO configRespDTO = new ConfigRespDTO();
        configRespDTO.setGuid("dfb7dc8c-f5be-48d5-9ed8-f9add8e9c440");
        configRespDTO.setEnterpriseGuid("enterpriseGuid");
        configRespDTO.setStoreGuid("storeGuid");
        configRespDTO.setDicCode(0);
        configRespDTO.setDicName("desc");
        configRespDTO.setDictValue("dictValue");
        configRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        configRespDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        configRespDTO.setIsEnable(0);
        final List<ConfigRespDTO> configRespDTOS = Arrays.asList(configRespDTO);
        final ConfigReqDTO configReqDTO = new ConfigReqDTO();
        configReqDTO.setGuid("b196c0e5-8ece-4ea4-b698-d1d993b94aab");
        configReqDTO.setEnterpriseGuid("enterpriseGuid");
        configReqDTO.setStoreGuid("storeGuid");
        configReqDTO.setDicCode(0);
        configReqDTO.setDicName("dicName");
        when(mockConfigClient.getConfig(configReqDTO)).thenReturn(configRespDTOS);

        // Run the test
        queueConfigServiceImplUnderTest.doSave(holderQueueConfigDO, 0);

        // Verify the results
        // Confirm ConfigClient.saveConfig(...).
        final ConfigReqDTO request = new ConfigReqDTO();
        request.setGuid("b196c0e5-8ece-4ea4-b698-d1d993b94aab");
        request.setEnterpriseGuid("enterpriseGuid");
        request.setStoreGuid("storeGuid");
        request.setDicCode(0);
        request.setDicName("dicName");
        verify(mockConfigClient).saveConfig(request);
    }

    @Test
    public void testDoSave_ConfigClientGetConfigReturnsNull() {
        // Setup
        final HolderQueueConfigDO holderQueueConfigDO = new HolderQueueConfigDO();
        holderQueueConfigDO.setGuid("1b17767a-7951-49b8-aaa6-8fd773e88f62");
        holderQueueConfigDO.setStoreGuid("storeGuid");
        holderQueueConfigDO.setIsEnableAutoReset(false);
        holderQueueConfigDO.setAutoResetTiming(LocalTime.of(0, 0, 0));
        holderQueueConfigDO.setIsDeleted(false);

        // Configure ConfigClient.getConfig(...).
        final ConfigReqDTO configReqDTO = new ConfigReqDTO();
        configReqDTO.setGuid("b196c0e5-8ece-4ea4-b698-d1d993b94aab");
        configReqDTO.setEnterpriseGuid("enterpriseGuid");
        configReqDTO.setStoreGuid("storeGuid");
        configReqDTO.setDicCode(0);
        configReqDTO.setDicName("dicName");
        when(mockConfigClient.getConfig(configReqDTO)).thenReturn(null);

        // Run the test
        queueConfigServiceImplUnderTest.doSave(holderQueueConfigDO, 0);

        // Verify the results
        // Confirm ConfigClient.saveConfig(...).
        final ConfigReqDTO request = new ConfigReqDTO();
        request.setGuid("b196c0e5-8ece-4ea4-b698-d1d993b94aab");
        request.setEnterpriseGuid("enterpriseGuid");
        request.setStoreGuid("storeGuid");
        request.setDicCode(0);
        request.setDicName("dicName");
        verify(mockConfigClient).saveConfig(request);
    }

    @Test
    public void testDoSave_ConfigClientGetConfigReturnsNoItems() {
        // Setup
        final HolderQueueConfigDO holderQueueConfigDO = new HolderQueueConfigDO();
        holderQueueConfigDO.setGuid("1b17767a-7951-49b8-aaa6-8fd773e88f62");
        holderQueueConfigDO.setStoreGuid("storeGuid");
        holderQueueConfigDO.setIsEnableAutoReset(false);
        holderQueueConfigDO.setAutoResetTiming(LocalTime.of(0, 0, 0));
        holderQueueConfigDO.setIsDeleted(false);

        // Configure ConfigClient.getConfig(...).
        final ConfigReqDTO configReqDTO = new ConfigReqDTO();
        configReqDTO.setGuid("b196c0e5-8ece-4ea4-b698-d1d993b94aab");
        configReqDTO.setEnterpriseGuid("enterpriseGuid");
        configReqDTO.setStoreGuid("storeGuid");
        configReqDTO.setDicCode(0);
        configReqDTO.setDicName("dicName");
        when(mockConfigClient.getConfig(configReqDTO)).thenReturn(Collections.emptyList());

        // Run the test
        queueConfigServiceImplUnderTest.doSave(holderQueueConfigDO, 0);

        // Verify the results
        // Confirm ConfigClient.saveConfig(...).
        final ConfigReqDTO request = new ConfigReqDTO();
        request.setGuid("b196c0e5-8ece-4ea4-b698-d1d993b94aab");
        request.setEnterpriseGuid("enterpriseGuid");
        request.setStoreGuid("storeGuid");
        request.setDicCode(0);
        request.setDicName("dicName");
        verify(mockConfigClient).saveConfig(request);
    }
}
