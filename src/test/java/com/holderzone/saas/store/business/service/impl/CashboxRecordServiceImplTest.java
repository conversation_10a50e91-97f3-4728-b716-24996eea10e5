package com.holderzone.saas.store.business.service.impl;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.business.entity.domain.CashboxRecordDO;
import com.holderzone.saas.store.business.mapper.CashboxRecordMapper;
import com.holderzone.saas.store.business.service.HandoverRecordService;
import com.holderzone.saas.store.dto.business.manage.*;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class CashboxRecordServiceImplTest {

    @Mock
    private CashboxRecordMapper mockCashboxRecordMapper;
    @Mock
    private HandoverRecordService mockHandoverRecordService;

    private CashboxRecordServiceImpl cashboxRecordServiceImplUnderTest;

    @Before
    public void setUp() {
        cashboxRecordServiceImplUnderTest = new CashboxRecordServiceImpl(mockCashboxRecordMapper,
                mockHandoverRecordService);
    }

    @Test
    public void testCreate() {
        // Setup
        final CashboxRecordCreateDTO cashboxRecordCreateDTO = new CashboxRecordCreateDTO();
        cashboxRecordCreateDTO.setStoreGuid("storeGuid");
        cashboxRecordCreateDTO.setUserGuid("userGuid");
        cashboxRecordCreateDTO.setUserName("userName");
        cashboxRecordCreateDTO.setOperationType(0);
        cashboxRecordCreateDTO.setTerminalId("deviceId");

        // Configure HandoverRecordService.queryByPage(...).
        final HandoverRecordDTO handoverRecordDTO = new HandoverRecordDTO();
        handoverRecordDTO.setStoreGuid("storeGuid");
        handoverRecordDTO.setStoreName("storeName");
        handoverRecordDTO.setTerminalId("terminalId");
        handoverRecordDTO.setHandoverRecordGuid("handoverRecordGuid");
        handoverRecordDTO.setCreateGuid("createGuid");
        final Page<HandoverRecordDTO> handoverRecordDTOPage = new Page<>(0L, 0L, Arrays.asList(handoverRecordDTO));
        when(mockHandoverRecordService.queryByPage(
                new HandoverRecordQueryAllDTO("terminalId", 0, LocalDateTime.of(2020, 1, 1, 0, 0, 0))))
                .thenReturn(handoverRecordDTOPage);

        // Configure CashboxRecordMapper.insert(...).
        final CashboxRecordDO cashboxRecordDO = new CashboxRecordDO();
        cashboxRecordDO.setStoreGuid("storeGuid");
        cashboxRecordDO.setTerminalId("terminalId");
        cashboxRecordDO.setHandoverRecordGuid("handoverRecordGuid");
        cashboxRecordDO.setCashboxRecordGuid("cashboxRecordGuid");
        cashboxRecordDO.setUserGuid("userGuid");
        when(mockCashboxRecordMapper.insert(cashboxRecordDO)).thenReturn(0);

        // Run the test
        cashboxRecordServiceImplUnderTest.create(cashboxRecordCreateDTO);

        // Verify the results
    }

    @Test
    public void testQuery() {
        // Setup
        final CashboxRecordQueryDTO cashboxRecordQueryDTO = new CashboxRecordQueryDTO("cashboxRecordGuid");
        final CashboxRecordDTO expectedResult = new CashboxRecordDTO();
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setHandoverRecordGuid("handoverRecordGuid");
        expectedResult.setCashboxRecordGuid("cashboxRecordGuid");
        expectedResult.setUserGuid("userGuid");
        expectedResult.setUserName("userName");

        // Configure CashboxRecordMapper.query(...).
        final CashboxRecordDO cashboxRecordDO = new CashboxRecordDO();
        cashboxRecordDO.setStoreGuid("storeGuid");
        cashboxRecordDO.setTerminalId("terminalId");
        cashboxRecordDO.setHandoverRecordGuid("handoverRecordGuid");
        cashboxRecordDO.setCashboxRecordGuid("cashboxRecordGuid");
        cashboxRecordDO.setUserGuid("userGuid");
        final CashboxRecordDO cashboxRecordDO1 = new CashboxRecordDO();
        cashboxRecordDO1.setStoreGuid("storeGuid");
        cashboxRecordDO1.setTerminalId("terminalId");
        cashboxRecordDO1.setHandoverRecordGuid("handoverRecordGuid");
        cashboxRecordDO1.setCashboxRecordGuid("cashboxRecordGuid");
        cashboxRecordDO1.setUserGuid("userGuid");
        when(mockCashboxRecordMapper.query(cashboxRecordDO1)).thenReturn(cashboxRecordDO);

        // Run the test
        final CashboxRecordDTO result = cashboxRecordServiceImplUnderTest.query(cashboxRecordQueryDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQuery_CashboxRecordMapperReturnsNull() {
        // Setup
        final CashboxRecordQueryDTO cashboxRecordQueryDTO = new CashboxRecordQueryDTO("cashboxRecordGuid");

        // Configure CashboxRecordMapper.query(...).
        final CashboxRecordDO cashboxRecordDO = new CashboxRecordDO();
        cashboxRecordDO.setStoreGuid("storeGuid");
        cashboxRecordDO.setTerminalId("terminalId");
        cashboxRecordDO.setHandoverRecordGuid("handoverRecordGuid");
        cashboxRecordDO.setCashboxRecordGuid("cashboxRecordGuid");
        cashboxRecordDO.setUserGuid("userGuid");
        when(mockCashboxRecordMapper.query(cashboxRecordDO)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> cashboxRecordServiceImplUnderTest.query(cashboxRecordQueryDTO))
                .isInstanceOf(BusinessException.class);
    }

    @Test
    public void testList() {
        // Setup
        final CashboxRecordListDTO cashboxRecordListDTO = new CashboxRecordListDTO("storeGuid", "userGuid", 0);
        final CashboxRecordDTO cashboxRecordDTO = new CashboxRecordDTO();
        cashboxRecordDTO.setStoreGuid("storeGuid");
        cashboxRecordDTO.setHandoverRecordGuid("handoverRecordGuid");
        cashboxRecordDTO.setCashboxRecordGuid("cashboxRecordGuid");
        cashboxRecordDTO.setUserGuid("userGuid");
        cashboxRecordDTO.setUserName("userName");
        final List<CashboxRecordDTO> expectedResult = Arrays.asList(cashboxRecordDTO);

        // Configure CashboxRecordMapper.queryAll(...).
        final CashboxRecordDO cashboxRecordDO = new CashboxRecordDO();
        cashboxRecordDO.setStoreGuid("storeGuid");
        cashboxRecordDO.setTerminalId("terminalId");
        cashboxRecordDO.setHandoverRecordGuid("handoverRecordGuid");
        cashboxRecordDO.setCashboxRecordGuid("cashboxRecordGuid");
        cashboxRecordDO.setUserGuid("userGuid");
        final List<CashboxRecordDO> cashboxRecordDOS = Arrays.asList(cashboxRecordDO);
        final CashboxRecordDO cashboxRecordDO1 = new CashboxRecordDO();
        cashboxRecordDO1.setStoreGuid("storeGuid");
        cashboxRecordDO1.setTerminalId("terminalId");
        cashboxRecordDO1.setHandoverRecordGuid("handoverRecordGuid");
        cashboxRecordDO1.setCashboxRecordGuid("cashboxRecordGuid");
        cashboxRecordDO1.setUserGuid("userGuid");
        when(mockCashboxRecordMapper.queryAll(cashboxRecordDO1)).thenReturn(cashboxRecordDOS);

        // Run the test
        final List<CashboxRecordDTO> result = cashboxRecordServiceImplUnderTest.list(cashboxRecordListDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testList_CashboxRecordMapperReturnsNoItems() {
        // Setup
        final CashboxRecordListDTO cashboxRecordListDTO = new CashboxRecordListDTO("storeGuid", "userGuid", 0);

        // Configure CashboxRecordMapper.queryAll(...).
        final CashboxRecordDO cashboxRecordDO = new CashboxRecordDO();
        cashboxRecordDO.setStoreGuid("storeGuid");
        cashboxRecordDO.setTerminalId("terminalId");
        cashboxRecordDO.setHandoverRecordGuid("handoverRecordGuid");
        cashboxRecordDO.setCashboxRecordGuid("cashboxRecordGuid");
        cashboxRecordDO.setUserGuid("userGuid");
        when(mockCashboxRecordMapper.queryAll(cashboxRecordDO)).thenReturn(Collections.emptyList());

        // Run the test
        final List<CashboxRecordDTO> result = cashboxRecordServiceImplUnderTest.list(cashboxRecordListDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }
}
