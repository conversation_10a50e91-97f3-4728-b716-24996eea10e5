package com.holderzone.saas.store.business.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.business.config.RedisIDGenerator;
import com.holderzone.saas.store.business.entity.domain.PaymentTypeDO;
import com.holderzone.saas.store.business.exception.IllegalParameterException;
import com.holderzone.saas.store.business.mapper.PaymentTypeMapper;
import com.holderzone.saas.store.business.service.RedisService;
import com.holderzone.saas.store.business.service.client.EnterpriseClientService;
import com.holderzone.saas.store.business.service.client.JHPayClientService;
import com.holderzone.saas.store.business.utils.PageAdapter;
import com.holderzone.saas.store.dto.journaling.req.PaySerialStatisticsReqDTO;
import com.holderzone.saas.store.dto.trade.*;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.RedisTemplate;

import java.time.LocalDate;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PaymentTypeServiceImplTest {

    @Mock
    private PaymentTypeMapper mockPaymentTypeMapper;
    @Mock
    private RedisService mockRedisService;
    @Mock
    private EnterpriseClientService mockEnterpriseClientService;
    @Mock
    private JHPayClientService mockJhPayClientService;
    @Mock
    private RedisIDGenerator mockRedisIDGenerator;

    @Mock
    private RedisTemplate mockRedisTemplate;

    private PaymentTypeServiceImpl paymentTypeServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        paymentTypeServiceImplUnderTest = new PaymentTypeServiceImpl(mockPaymentTypeMapper, mockRedisService,
                mockEnterpriseClientService, mockJhPayClientService, mockRedisIDGenerator, mockRedisTemplate);
    }

    @Test
    public void testConfig() {
        // Setup
        final JHConfigDTO jhConfigDTO = new JHConfigDTO();
        jhConfigDTO.setAppId("appId");
        jhConfigDTO.setAppSecretKey("appSecret");
        jhConfigDTO.setStoreGuid("storeGuid");

        // Configure JHPayClientService.check(...).
        final MchntValidateDTO mchntValidateDTO = new MchntValidateDTO();
        mchntValidateDTO.setAppId("appId");
        mchntValidateDTO.setAppSecretKey("appSecret");
        when(mockJhPayClientService.check(mchntValidateDTO)).thenReturn(false);

        // Run the test
        final String result = paymentTypeServiceImplUnderTest.config(jhConfigDTO);

        // Verify the results
        assertThat(result).isEqualTo("failure");
    }

    @Test
    public void testConfig_JHPayClientServiceReturnsTrue() {
        // Setup
        final JHConfigDTO jhConfigDTO = new JHConfigDTO();
        jhConfigDTO.setAppId("appId");
        jhConfigDTO.setAppSecretKey("appSecret");
        jhConfigDTO.setStoreGuid("storeGuid");

        // Configure JHPayClientService.check(...).
        final MchntValidateDTO mchntValidateDTO = new MchntValidateDTO();
        mchntValidateDTO.setAppId("appId");
        mchntValidateDTO.setAppSecretKey("appSecret");
        when(mockJhPayClientService.check(mchntValidateDTO)).thenReturn(true);

        // Configure EnterpriseClientService.notify(...).
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setStoreGuid("storeGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setAccountName("accountName");
        paymentInfoDTO.setIsDefaultAccount(0);
        paymentInfoDTO.setDiversionRules("diversionRules");
        paymentInfoDTO.setDiversionRule(Arrays.asList(0));
        when(mockEnterpriseClientService.notify(paymentInfoDTO)).thenReturn(false);

        // Configure EnterpriseClientService.update(...).
        final PaymentInfoDTO paymentInfoDTO1 = new PaymentInfoDTO();
        paymentInfoDTO1.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO1.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO1.setStoreGuid("storeGuid");
        paymentInfoDTO1.setAppId("appId");
        paymentInfoDTO1.setAppSecret("appSecret");
        paymentInfoDTO1.setAccountName("accountName");
        paymentInfoDTO1.setIsDefaultAccount(0);
        paymentInfoDTO1.setDiversionRules("diversionRules");
        paymentInfoDTO1.setDiversionRule(Arrays.asList(0));
        when(mockEnterpriseClientService.update(paymentInfoDTO1)).thenReturn(false);

        // Run the test
        final String result = paymentTypeServiceImplUnderTest.config(jhConfigDTO);

        // Verify the results
        assertThat(result).isEqualTo("failure");
    }

    @Test
    public void testConfig_EnterpriseClientServiceNotifyReturnsNull() {
        // Setup
        final JHConfigDTO jhConfigDTO = new JHConfigDTO();
        jhConfigDTO.setAppId("appId");
        jhConfigDTO.setAppSecretKey("appSecret");
        jhConfigDTO.setStoreGuid("storeGuid");

        // Configure JHPayClientService.check(...).
        final MchntValidateDTO mchntValidateDTO = new MchntValidateDTO();
        mchntValidateDTO.setAppId("appId");
        mchntValidateDTO.setAppSecretKey("appSecret");
        when(mockJhPayClientService.check(mchntValidateDTO)).thenReturn(true);

        // Configure EnterpriseClientService.notify(...).
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setStoreGuid("storeGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setAccountName("accountName");
        paymentInfoDTO.setIsDefaultAccount(0);
        paymentInfoDTO.setDiversionRules("diversionRules");
        paymentInfoDTO.setDiversionRule(Arrays.asList(0));
        when(mockEnterpriseClientService.notify(paymentInfoDTO)).thenReturn(null);

        // Configure EnterpriseClientService.update(...).
        final PaymentInfoDTO paymentInfoDTO1 = new PaymentInfoDTO();
        paymentInfoDTO1.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO1.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO1.setStoreGuid("storeGuid");
        paymentInfoDTO1.setAppId("appId");
        paymentInfoDTO1.setAppSecret("appSecret");
        paymentInfoDTO1.setAccountName("accountName");
        paymentInfoDTO1.setIsDefaultAccount(0);
        paymentInfoDTO1.setDiversionRules("diversionRules");
        paymentInfoDTO1.setDiversionRule(Arrays.asList(0));
        when(mockEnterpriseClientService.update(paymentInfoDTO1)).thenReturn(false);

        // Run the test
        final String result = paymentTypeServiceImplUnderTest.config(jhConfigDTO);

        // Verify the results
        assertThat(result).isEqualTo("failure");
    }

    @Test
    public void testConfig_EnterpriseClientServiceNotifyReturnsTrue() {
        // Setup
        final JHConfigDTO jhConfigDTO = new JHConfigDTO();
        jhConfigDTO.setAppId("appId");
        jhConfigDTO.setAppSecretKey("appSecret");
        jhConfigDTO.setStoreGuid("storeGuid");

        // Configure JHPayClientService.check(...).
        final MchntValidateDTO mchntValidateDTO = new MchntValidateDTO();
        mchntValidateDTO.setAppId("appId");
        mchntValidateDTO.setAppSecretKey("appSecret");
        when(mockJhPayClientService.check(mchntValidateDTO)).thenReturn(true);

        // Configure EnterpriseClientService.notify(...).
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setStoreGuid("storeGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setAccountName("accountName");
        paymentInfoDTO.setIsDefaultAccount(0);
        paymentInfoDTO.setDiversionRules("diversionRules");
        paymentInfoDTO.setDiversionRule(Arrays.asList(0));
        when(mockEnterpriseClientService.notify(paymentInfoDTO)).thenReturn(true);

        // Run the test
        final String result = paymentTypeServiceImplUnderTest.config(jhConfigDTO);

        // Verify the results
        assertThat(result).isEqualTo("failure");

        // Confirm RedisService.putJHPayInfo(...).
        final PaymentInfoDTO paymentInfoDTO1 = new PaymentInfoDTO();
        paymentInfoDTO1.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO1.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO1.setStoreGuid("storeGuid");
        paymentInfoDTO1.setAppId("appId");
        paymentInfoDTO1.setAppSecret("appSecret");
        paymentInfoDTO1.setAccountName("accountName");
        paymentInfoDTO1.setIsDefaultAccount(0);
        paymentInfoDTO1.setDiversionRules("diversionRules");
        paymentInfoDTO1.setDiversionRule(Arrays.asList(0));
        verify(mockRedisService).putJHPayInfo(paymentInfoDTO1);
    }

    @Test
    public void testUnbind() {
        // Setup
        final JHReqDTO jhReqDTO = new JHReqDTO();
        jhReqDTO.setAppId("appId");
        jhReqDTO.setAppSecretKey("appSecret");
        jhReqDTO.setStoreGuid("storeGuid");
        jhReqDTO.setPaymentTypeGuid("paymentTypeGuid");

        // Configure EnterpriseClientService.unbind(...).
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setStoreGuid("storeGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setAccountName("accountName");
        paymentInfoDTO.setIsDefaultAccount(0);
        paymentInfoDTO.setDiversionRules("diversionRules");
        paymentInfoDTO.setDiversionRule(Arrays.asList(0));
        when(mockEnterpriseClientService.unbind(paymentInfoDTO)).thenReturn(false);

        // Run the test
        final String result = paymentTypeServiceImplUnderTest.unbind(jhReqDTO);

        // Verify the results
        assertThat(result).isEqualTo("failure");
    }

    @Test
    public void testUnbind_EnterpriseClientServiceReturnsTrue() {
        // Setup
        final JHReqDTO jhReqDTO = new JHReqDTO();
        jhReqDTO.setAppId("appId");
        jhReqDTO.setAppSecretKey("appSecret");
        jhReqDTO.setStoreGuid("storeGuid");
        jhReqDTO.setPaymentTypeGuid("paymentTypeGuid");

        // Configure EnterpriseClientService.unbind(...).
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setStoreGuid("storeGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setAccountName("accountName");
        paymentInfoDTO.setIsDefaultAccount(0);
        paymentInfoDTO.setDiversionRules("diversionRules");
        paymentInfoDTO.setDiversionRule(Arrays.asList(0));
        when(mockEnterpriseClientService.unbind(paymentInfoDTO)).thenReturn(true);

        // Run the test
        final String result = paymentTypeServiceImplUnderTest.unbind(jhReqDTO);

        // Verify the results
        assertThat(result).isEqualTo("failure");
        verify(mockRedisService).deleteJHPayInfo("storeGuid");
        verify(mockPaymentTypeMapper).updateShunt("paymentTypeGuid", 0, "storeGuid");
    }

    @Test
    public void testGetMultiStorePayWay() {
        // Setup
        final PaySerialStatisticsReqDTO paySerialStatisticsReqDTO = new PaySerialStatisticsReqDTO();
        paySerialStatisticsReqDTO.setStartDate(LocalDate.of(2020, 1, 1));
        paySerialStatisticsReqDTO.setEndDate(LocalDate.of(2020, 1, 1));
        paySerialStatisticsReqDTO.setStoreGuid("storeGuid");

        // Configure PaymentTypeMapper.getMultiStorePayWay(...).
        final PaySerialStatisticsReqDTO paySerialStatisticsReqDTO1 = new PaySerialStatisticsReqDTO();
        paySerialStatisticsReqDTO1.setStartDate(LocalDate.of(2020, 1, 1));
        paySerialStatisticsReqDTO1.setEndDate(LocalDate.of(2020, 1, 1));
        paySerialStatisticsReqDTO1.setStoreGuid("storeGuid");
        when(mockPaymentTypeMapper.getMultiStorePayWay(any(PageAdapter.class),
                eq(paySerialStatisticsReqDTO1))).thenReturn(null);

        // Run the test
        final Page<PaymentTypeDTO> result = paymentTypeServiceImplUnderTest.getMultiStorePayWay(
                paySerialStatisticsReqDTO);

        // Verify the results
    }

    @Test
    public void testInitPaymentType() {
        // Setup
        // Configure PaymentTypeMapper.getAll(...).
        final PaymentTypeDTO paymentTypeDTO = new PaymentTypeDTO();
        paymentTypeDTO.setStoreGuid("storeGuid");
        paymentTypeDTO.setStoreName("storeName");
        paymentTypeDTO.setPaymentTypeGuid("paymentTypeGuid");
        paymentTypeDTO.setPaymentTypeName("name");
        paymentTypeDTO.setPaymentType(0);
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setStoreGuid("storeGuid");
        storeDTO.setStoreName("storeName");
        paymentTypeDTO.setStoreDTOS(Arrays.asList(storeDTO));
        paymentTypeDTO.setSorting(0);
        paymentTypeDTO.setAppId("appId");
        paymentTypeDTO.setAppSecretKey("appSecret");
        paymentTypeDTO.setSource(0);
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setStoreGuid("storeGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setAccountName("accountName");
        paymentInfoDTO.setIsDefaultAccount(0);
        paymentInfoDTO.setDiversionRules("diversionRules");
        paymentInfoDTO.setDiversionRule(Arrays.asList(0));
        paymentTypeDTO.setJhPayInfoList(Arrays.asList(paymentInfoDTO));
        final List<PaymentTypeDTO> paymentTypeDTOS = Arrays.asList(paymentTypeDTO);
        when(mockPaymentTypeMapper.getAll("storeGuid", 1)).thenReturn(paymentTypeDTOS);

        when(mockRedisIDGenerator.getSingle("hst_payment_type")).thenReturn(0L);

        // Run the test
        final Boolean result = paymentTypeServiceImplUnderTest.initPaymentType("storeGuid");

        // Verify the results
        assertThat(result).isFalse();
        verify(mockPaymentTypeMapper).addAll(Arrays.asList(PaymentTypeDO.builder()
                .paymentTypeGuid("parentPaymentTypeGuid")
                .paymentTypeName("name")
                .paymentType(0)
                .state(0)
                .storeGuid("storeGuid")
                .storeName("storeName")
                .sorting(0)
                .source(0)
                .paymentMode(0)
                .parentPaymentTypeGuid("parentPaymentTypeGuid")
                .paymentShunt(0)
                .build()));
    }

    @Test
    public void testInitPaymentType_PaymentTypeMapperGetAllReturnsNoItems() {
        // Setup
        when(mockPaymentTypeMapper.getAll("storeGuid", 1)).thenReturn(Collections.emptyList());

        // Run the test
        final Boolean result = paymentTypeServiceImplUnderTest.initPaymentType("storeGuid");

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testGetPaymentTypeList() {
        // Setup
        final PaymentTypeEnumDTO paymentTypeEnumDTO = new PaymentTypeEnumDTO();
        paymentTypeEnumDTO.setPaymentTypeName("name");
        paymentTypeEnumDTO.setPaymentType(0);
        final List<PaymentTypeEnumDTO> expectedResult = Arrays.asList(paymentTypeEnumDTO);

        // Run the test
        final List<PaymentTypeEnumDTO> result = paymentTypeServiceImplUnderTest.getPaymentTypeList();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetOne() {
        // Setup
        final PaymentTypeDTO paymentTypeDTO = new PaymentTypeDTO();
        paymentTypeDTO.setStoreGuid("storeGuid");
        paymentTypeDTO.setStoreName("storeName");
        paymentTypeDTO.setPaymentTypeGuid("paymentTypeGuid");
        paymentTypeDTO.setPaymentTypeName("name");
        paymentTypeDTO.setPaymentType(0);
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setStoreGuid("storeGuid");
        storeDTO.setStoreName("storeName");
        paymentTypeDTO.setStoreDTOS(Arrays.asList(storeDTO));
        paymentTypeDTO.setSorting(0);
        paymentTypeDTO.setAppId("appId");
        paymentTypeDTO.setAppSecretKey("appSecret");
        paymentTypeDTO.setSource(0);
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setStoreGuid("storeGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setAccountName("accountName");
        paymentInfoDTO.setIsDefaultAccount(0);
        paymentInfoDTO.setDiversionRules("diversionRules");
        paymentInfoDTO.setDiversionRule(Arrays.asList(0));
        paymentTypeDTO.setJhPayInfoList(Arrays.asList(paymentInfoDTO));

        final PaymentTypeDTO expectedResult = new PaymentTypeDTO();
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setStoreName("storeName");
        expectedResult.setPaymentTypeGuid("paymentTypeGuid");
        expectedResult.setPaymentTypeName("name");
        expectedResult.setPaymentType(0);
        final StoreDTO storeDTO1 = new StoreDTO();
        storeDTO1.setStoreGuid("storeGuid");
        storeDTO1.setStoreName("storeName");
        expectedResult.setStoreDTOS(Arrays.asList(storeDTO1));
        expectedResult.setSorting(0);
        expectedResult.setAppId("appId");
        expectedResult.setAppSecretKey("appSecret");
        expectedResult.setSource(0);
        final PaymentInfoDTO paymentInfoDTO1 = new PaymentInfoDTO();
        paymentInfoDTO1.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO1.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO1.setStoreGuid("storeGuid");
        paymentInfoDTO1.setAppId("appId");
        paymentInfoDTO1.setAppSecret("appSecret");
        paymentInfoDTO1.setAccountName("accountName");
        paymentInfoDTO1.setIsDefaultAccount(0);
        paymentInfoDTO1.setDiversionRules("diversionRules");
        paymentInfoDTO1.setDiversionRule(Arrays.asList(0));
        expectedResult.setJhPayInfoList(Arrays.asList(paymentInfoDTO1));

        // Configure PaymentTypeMapper.getOne(...).
        final PaymentTypeDTO paymentTypeDTO1 = new PaymentTypeDTO();
        paymentTypeDTO1.setStoreGuid("storeGuid");
        paymentTypeDTO1.setStoreName("storeName");
        paymentTypeDTO1.setPaymentTypeGuid("paymentTypeGuid");
        paymentTypeDTO1.setPaymentTypeName("name");
        paymentTypeDTO1.setPaymentType(0);
        final StoreDTO storeDTO2 = new StoreDTO();
        storeDTO2.setStoreGuid("storeGuid");
        storeDTO2.setStoreName("storeName");
        paymentTypeDTO1.setStoreDTOS(Arrays.asList(storeDTO2));
        paymentTypeDTO1.setSorting(0);
        paymentTypeDTO1.setAppId("appId");
        paymentTypeDTO1.setAppSecretKey("appSecret");
        paymentTypeDTO1.setSource(0);
        final PaymentInfoDTO paymentInfoDTO2 = new PaymentInfoDTO();
        paymentInfoDTO2.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO2.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO2.setStoreGuid("storeGuid");
        paymentInfoDTO2.setAppId("appId");
        paymentInfoDTO2.setAppSecret("appSecret");
        paymentInfoDTO2.setAccountName("accountName");
        paymentInfoDTO2.setIsDefaultAccount(0);
        paymentInfoDTO2.setDiversionRules("diversionRules");
        paymentInfoDTO2.setDiversionRule(Arrays.asList(0));
        paymentTypeDTO1.setJhPayInfoList(Arrays.asList(paymentInfoDTO2));
        final PaymentTypeDTO paymentTypeDTO2 = new PaymentTypeDTO();
        paymentTypeDTO2.setStoreGuid("storeGuid");
        paymentTypeDTO2.setStoreName("storeName");
        paymentTypeDTO2.setPaymentTypeGuid("paymentTypeGuid");
        paymentTypeDTO2.setPaymentTypeName("name");
        paymentTypeDTO2.setPaymentType(0);
        final StoreDTO storeDTO3 = new StoreDTO();
        storeDTO3.setStoreGuid("storeGuid");
        storeDTO3.setStoreName("storeName");
        paymentTypeDTO2.setStoreDTOS(Arrays.asList(storeDTO3));
        paymentTypeDTO2.setSorting(0);
        paymentTypeDTO2.setAppId("appId");
        paymentTypeDTO2.setAppSecretKey("appSecret");
        paymentTypeDTO2.setSource(0);
        final PaymentInfoDTO paymentInfoDTO3 = new PaymentInfoDTO();
        paymentInfoDTO3.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO3.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO3.setStoreGuid("storeGuid");
        paymentInfoDTO3.setAppId("appId");
        paymentInfoDTO3.setAppSecret("appSecret");
        paymentInfoDTO3.setAccountName("accountName");
        paymentInfoDTO3.setIsDefaultAccount(0);
        paymentInfoDTO3.setDiversionRules("diversionRules");
        paymentInfoDTO3.setDiversionRule(Arrays.asList(0));
        paymentTypeDTO2.setJhPayInfoList(Arrays.asList(paymentInfoDTO3));
        when(mockPaymentTypeMapper.getOne(paymentTypeDTO2)).thenReturn(paymentTypeDTO1);

        // Configure EnterpriseClientService.getJHInfo(...).
        final PaymentInfoDTO paymentInfoDTO4 = new PaymentInfoDTO();
        paymentInfoDTO4.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO4.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO4.setStoreGuid("storeGuid");
        paymentInfoDTO4.setAppId("appId");
        paymentInfoDTO4.setAppSecret("appSecret");
        paymentInfoDTO4.setAccountName("accountName");
        paymentInfoDTO4.setIsDefaultAccount(0);
        paymentInfoDTO4.setDiversionRules("diversionRules");
        paymentInfoDTO4.setDiversionRule(Arrays.asList(0));
        when(mockEnterpriseClientService.getJHInfo("enterpriseGuid", "storeGuid")).thenReturn(paymentInfoDTO4);

        // Run the test
        final PaymentTypeDTO result = paymentTypeServiceImplUnderTest.getOne(paymentTypeDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetOne_EnterpriseClientServiceReturnsNull() {
        // Setup
        final PaymentTypeDTO paymentTypeDTO = new PaymentTypeDTO();
        paymentTypeDTO.setStoreGuid("storeGuid");
        paymentTypeDTO.setStoreName("storeName");
        paymentTypeDTO.setPaymentTypeGuid("paymentTypeGuid");
        paymentTypeDTO.setPaymentTypeName("name");
        paymentTypeDTO.setPaymentType(0);
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setStoreGuid("storeGuid");
        storeDTO.setStoreName("storeName");
        paymentTypeDTO.setStoreDTOS(Arrays.asList(storeDTO));
        paymentTypeDTO.setSorting(0);
        paymentTypeDTO.setAppId("appId");
        paymentTypeDTO.setAppSecretKey("appSecret");
        paymentTypeDTO.setSource(0);
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setStoreGuid("storeGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setAccountName("accountName");
        paymentInfoDTO.setIsDefaultAccount(0);
        paymentInfoDTO.setDiversionRules("diversionRules");
        paymentInfoDTO.setDiversionRule(Arrays.asList(0));
        paymentTypeDTO.setJhPayInfoList(Arrays.asList(paymentInfoDTO));

        final PaymentTypeDTO expectedResult = new PaymentTypeDTO();
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setStoreName("storeName");
        expectedResult.setPaymentTypeGuid("paymentTypeGuid");
        expectedResult.setPaymentTypeName("name");
        expectedResult.setPaymentType(0);
        final StoreDTO storeDTO1 = new StoreDTO();
        storeDTO1.setStoreGuid("storeGuid");
        storeDTO1.setStoreName("storeName");
        expectedResult.setStoreDTOS(Arrays.asList(storeDTO1));
        expectedResult.setSorting(0);
        expectedResult.setAppId("appId");
        expectedResult.setAppSecretKey("appSecret");
        expectedResult.setSource(0);
        final PaymentInfoDTO paymentInfoDTO1 = new PaymentInfoDTO();
        paymentInfoDTO1.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO1.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO1.setStoreGuid("storeGuid");
        paymentInfoDTO1.setAppId("appId");
        paymentInfoDTO1.setAppSecret("appSecret");
        paymentInfoDTO1.setAccountName("accountName");
        paymentInfoDTO1.setIsDefaultAccount(0);
        paymentInfoDTO1.setDiversionRules("diversionRules");
        paymentInfoDTO1.setDiversionRule(Arrays.asList(0));
        expectedResult.setJhPayInfoList(Arrays.asList(paymentInfoDTO1));

        // Configure PaymentTypeMapper.getOne(...).
        final PaymentTypeDTO paymentTypeDTO1 = new PaymentTypeDTO();
        paymentTypeDTO1.setStoreGuid("storeGuid");
        paymentTypeDTO1.setStoreName("storeName");
        paymentTypeDTO1.setPaymentTypeGuid("paymentTypeGuid");
        paymentTypeDTO1.setPaymentTypeName("name");
        paymentTypeDTO1.setPaymentType(0);
        final StoreDTO storeDTO2 = new StoreDTO();
        storeDTO2.setStoreGuid("storeGuid");
        storeDTO2.setStoreName("storeName");
        paymentTypeDTO1.setStoreDTOS(Arrays.asList(storeDTO2));
        paymentTypeDTO1.setSorting(0);
        paymentTypeDTO1.setAppId("appId");
        paymentTypeDTO1.setAppSecretKey("appSecret");
        paymentTypeDTO1.setSource(0);
        final PaymentInfoDTO paymentInfoDTO2 = new PaymentInfoDTO();
        paymentInfoDTO2.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO2.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO2.setStoreGuid("storeGuid");
        paymentInfoDTO2.setAppId("appId");
        paymentInfoDTO2.setAppSecret("appSecret");
        paymentInfoDTO2.setAccountName("accountName");
        paymentInfoDTO2.setIsDefaultAccount(0);
        paymentInfoDTO2.setDiversionRules("diversionRules");
        paymentInfoDTO2.setDiversionRule(Arrays.asList(0));
        paymentTypeDTO1.setJhPayInfoList(Arrays.asList(paymentInfoDTO2));
        final PaymentTypeDTO paymentTypeDTO2 = new PaymentTypeDTO();
        paymentTypeDTO2.setStoreGuid("storeGuid");
        paymentTypeDTO2.setStoreName("storeName");
        paymentTypeDTO2.setPaymentTypeGuid("paymentTypeGuid");
        paymentTypeDTO2.setPaymentTypeName("name");
        paymentTypeDTO2.setPaymentType(0);
        final StoreDTO storeDTO3 = new StoreDTO();
        storeDTO3.setStoreGuid("storeGuid");
        storeDTO3.setStoreName("storeName");
        paymentTypeDTO2.setStoreDTOS(Arrays.asList(storeDTO3));
        paymentTypeDTO2.setSorting(0);
        paymentTypeDTO2.setAppId("appId");
        paymentTypeDTO2.setAppSecretKey("appSecret");
        paymentTypeDTO2.setSource(0);
        final PaymentInfoDTO paymentInfoDTO3 = new PaymentInfoDTO();
        paymentInfoDTO3.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO3.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO3.setStoreGuid("storeGuid");
        paymentInfoDTO3.setAppId("appId");
        paymentInfoDTO3.setAppSecret("appSecret");
        paymentInfoDTO3.setAccountName("accountName");
        paymentInfoDTO3.setIsDefaultAccount(0);
        paymentInfoDTO3.setDiversionRules("diversionRules");
        paymentInfoDTO3.setDiversionRule(Arrays.asList(0));
        paymentTypeDTO2.setJhPayInfoList(Arrays.asList(paymentInfoDTO3));
        when(mockPaymentTypeMapper.getOne(paymentTypeDTO2)).thenReturn(paymentTypeDTO1);

        when(mockEnterpriseClientService.getJHInfo("enterpriseGuid", "storeGuid")).thenReturn(null);

        // Run the test
        final PaymentTypeDTO result = paymentTypeServiceImplUnderTest.getOne(paymentTypeDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetAllTypeName() {
        // Setup
        final PaymentTypeDTO paymentTypeDTO = new PaymentTypeDTO();
        paymentTypeDTO.setStoreGuid("storeGuid");
        paymentTypeDTO.setStoreName("storeName");
        paymentTypeDTO.setPaymentTypeGuid("paymentTypeGuid");
        paymentTypeDTO.setPaymentTypeName("name");
        paymentTypeDTO.setPaymentType(0);
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setStoreGuid("storeGuid");
        storeDTO.setStoreName("storeName");
        paymentTypeDTO.setStoreDTOS(Arrays.asList(storeDTO));
        paymentTypeDTO.setSorting(0);
        paymentTypeDTO.setAppId("appId");
        paymentTypeDTO.setAppSecretKey("appSecret");
        paymentTypeDTO.setSource(0);
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setStoreGuid("storeGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setAccountName("accountName");
        paymentInfoDTO.setIsDefaultAccount(0);
        paymentInfoDTO.setDiversionRules("diversionRules");
        paymentInfoDTO.setDiversionRule(Arrays.asList(0));
        paymentTypeDTO.setJhPayInfoList(Arrays.asList(paymentInfoDTO));
        final List<PaymentTypeDTO> expectedResult = Arrays.asList(paymentTypeDTO);

        // Configure PaymentTypeMapper.getAll(...).
        final PaymentTypeDTO paymentTypeDTO1 = new PaymentTypeDTO();
        paymentTypeDTO1.setStoreGuid("storeGuid");
        paymentTypeDTO1.setStoreName("storeName");
        paymentTypeDTO1.setPaymentTypeGuid("paymentTypeGuid");
        paymentTypeDTO1.setPaymentTypeName("name");
        paymentTypeDTO1.setPaymentType(0);
        final StoreDTO storeDTO1 = new StoreDTO();
        storeDTO1.setStoreGuid("storeGuid");
        storeDTO1.setStoreName("storeName");
        paymentTypeDTO1.setStoreDTOS(Arrays.asList(storeDTO1));
        paymentTypeDTO1.setSorting(0);
        paymentTypeDTO1.setAppId("appId");
        paymentTypeDTO1.setAppSecretKey("appSecret");
        paymentTypeDTO1.setSource(0);
        final PaymentInfoDTO paymentInfoDTO1 = new PaymentInfoDTO();
        paymentInfoDTO1.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO1.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO1.setStoreGuid("storeGuid");
        paymentInfoDTO1.setAppId("appId");
        paymentInfoDTO1.setAppSecret("appSecret");
        paymentInfoDTO1.setAccountName("accountName");
        paymentInfoDTO1.setIsDefaultAccount(0);
        paymentInfoDTO1.setDiversionRules("diversionRules");
        paymentInfoDTO1.setDiversionRule(Arrays.asList(0));
        paymentTypeDTO1.setJhPayInfoList(Arrays.asList(paymentInfoDTO1));
        final List<PaymentTypeDTO> paymentTypeDTOS = Arrays.asList(paymentTypeDTO1);
        when(mockPaymentTypeMapper.getAll("storeGuid", 0)).thenReturn(paymentTypeDTOS);

        // Run the test
        final List<PaymentTypeDTO> result = paymentTypeServiceImplUnderTest.getAllTypeName("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetAllTypeName_PaymentTypeMapperReturnsNoItems() {
        // Setup
        when(mockPaymentTypeMapper.getAll("storeGuid", 0)).thenReturn(Collections.emptyList());

        // Run the test
        final List<PaymentTypeDTO> result = paymentTypeServiceImplUnderTest.getAllTypeName("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testGetAllStoreNames() {
        // Setup
        when(mockPaymentTypeMapper.getAllStorePayNames(Arrays.asList("value")))
                .thenReturn(new HashSet<>(Arrays.asList("value")));

        // Run the test
        final Set<String> result = paymentTypeServiceImplUnderTest.getAllStoreNames(Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(new HashSet<>(Arrays.asList("value")));
    }

    @Test
    public void testGetAllStoreNames_PaymentTypeMapperReturnsNoItems() {
        // Setup
        when(mockPaymentTypeMapper.getAllStorePayNames(Arrays.asList("value"))).thenReturn(Collections.emptySet());

        // Run the test
        final Set<String> result = paymentTypeServiceImplUnderTest.getAllStoreNames(Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptySet());
    }

    @Test
    public void testInit_ThrowsBusinessException() {
        // Setup
        when(mockPaymentTypeMapper.queryDefauktPayTypeExist("storeGuid")).thenReturn(0);

        // Run the test
        assertThatThrownBy(
                () -> paymentTypeServiceImplUnderTest.init("storeGuid", "storeName", "mchntTypeCode"))
                .isInstanceOf(BusinessException.class);
    }

    @Test
    public void testInit_PaymentTypeMapperQueryDefauktPayTypeExistReturnsNull() {
        // Setup
        when(mockPaymentTypeMapper.queryDefauktPayTypeExist("storeGuid")).thenReturn(null);
        when(mockRedisIDGenerator.getBatchIds(0L, "hst_payment_type")).thenReturn(Arrays.asList(0L));
        when(mockRedisIDGenerator.getSingle("hst_payment_type")).thenReturn(0L);

        // Run the test
        final String result = paymentTypeServiceImplUnderTest.init("storeGuid", "storeName", "mchntTypeCode");

        // Verify the results
        assertThat(result).isEqualTo("result");
        verify(mockPaymentTypeMapper).addAll(Arrays.asList(PaymentTypeDO.builder()
                .paymentTypeGuid("parentPaymentTypeGuid")
                .paymentTypeName("name")
                .paymentType(0)
                .state(0)
                .storeGuid("storeGuid")
                .storeName("storeName")
                .sorting(0)
                .source(0)
                .paymentMode(0)
                .parentPaymentTypeGuid("parentPaymentTypeGuid")
                .paymentShunt(0)
                .build()));
    }

    @Test
    public void testInit_RedisIDGeneratorGetBatchIdsReturnsNoItems() {
        // Setup
        when(mockPaymentTypeMapper.queryDefauktPayTypeExist("storeGuid")).thenReturn(null);
        when(mockRedisIDGenerator.getBatchIds(0L, "hst_payment_type")).thenReturn(Collections.emptyList());
        when(mockRedisIDGenerator.getSingle("hst_payment_type")).thenReturn(0L);

        // Run the test
        final String result = paymentTypeServiceImplUnderTest.init("storeGuid", "storeName", "mchntTypeCode");

        // Verify the results
        assertThat(result).isEqualTo("result");
        verify(mockPaymentTypeMapper).addAll(Arrays.asList(PaymentTypeDO.builder()
                .paymentTypeGuid("parentPaymentTypeGuid")
                .paymentTypeName("name")
                .paymentType(0)
                .state(0)
                .storeGuid("storeGuid")
                .storeName("storeName")
                .sorting(0)
                .source(0)
                .paymentMode(0)
                .parentPaymentTypeGuid("parentPaymentTypeGuid")
                .paymentShunt(0)
                .build()));
    }

    @Test
    public void testAdd() {
        // Setup
        final PaymentTypeDTO paymentTypeDTO = new PaymentTypeDTO();
        paymentTypeDTO.setStoreGuid("storeGuid");
        paymentTypeDTO.setStoreName("storeName");
        paymentTypeDTO.setPaymentTypeGuid("paymentTypeGuid");
        paymentTypeDTO.setPaymentTypeName("name");
        paymentTypeDTO.setPaymentType(0);
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setStoreGuid("storeGuid");
        storeDTO.setStoreName("storeName");
        paymentTypeDTO.setStoreDTOS(Arrays.asList(storeDTO));
        paymentTypeDTO.setSorting(0);
        paymentTypeDTO.setAppId("appId");
        paymentTypeDTO.setAppSecretKey("appSecret");
        paymentTypeDTO.setSource(0);
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setStoreGuid("storeGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setAccountName("accountName");
        paymentInfoDTO.setIsDefaultAccount(0);
        paymentInfoDTO.setDiversionRules("diversionRules");
        paymentInfoDTO.setDiversionRule(Arrays.asList(0));
        paymentTypeDTO.setJhPayInfoList(Arrays.asList(paymentInfoDTO));

        PaymentTypeDO paymentTypeDO = new PaymentTypeDO();
        paymentTypeDO.setSorting(0);
        paymentTypeDO.setPaymentType(0);

        when(mockPaymentTypeMapper.countByStoreGuidInAndName(Arrays.asList("value"), "name", "guid")).thenReturn(0);
        when(mockRedisIDGenerator.getBatchIds(0L, "hst_payment_type")).thenReturn(Arrays.asList(0L));
        when(mockPaymentTypeMapper.getMaxSortingAndMaxPaymentType("storeGuid")).thenReturn(paymentTypeDO);

        // Run the test
        paymentTypeServiceImplUnderTest.add(paymentTypeDTO);

        // Verify the results
        verify(mockRedisService).deletePaymentType("storeGuid");
    }

    @Test
    public void testAdd_RedisIDGeneratorReturnsNoItems() {
        // Setup
        final PaymentTypeDTO paymentTypeDTO = new PaymentTypeDTO();
        paymentTypeDTO.setStoreGuid("storeGuid");
        paymentTypeDTO.setStoreName("storeName");
        paymentTypeDTO.setPaymentTypeGuid("paymentTypeGuid");
        paymentTypeDTO.setPaymentTypeName("name");
        paymentTypeDTO.setPaymentType(0);
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setStoreGuid("storeGuid");
        storeDTO.setStoreName("storeName");
        paymentTypeDTO.setStoreDTOS(Arrays.asList(storeDTO));
        paymentTypeDTO.setSorting(0);
        paymentTypeDTO.setAppId("appId");
        paymentTypeDTO.setAppSecretKey("appSecret");
        paymentTypeDTO.setSource(0);
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setStoreGuid("storeGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setAccountName("accountName");
        paymentInfoDTO.setIsDefaultAccount(0);
        paymentInfoDTO.setDiversionRules("diversionRules");
        paymentInfoDTO.setDiversionRule(Arrays.asList(0));
        paymentTypeDTO.setJhPayInfoList(Arrays.asList(paymentInfoDTO));

        PaymentTypeDO paymentTypeDO = new PaymentTypeDO();
        paymentTypeDO.setSorting(0);
        paymentTypeDO.setPaymentType(0);

        when(mockPaymentTypeMapper.countByStoreGuidInAndName(Arrays.asList("value"), "name", "guid")).thenReturn(0);
        when(mockRedisIDGenerator.getBatchIds(0L, "hst_payment_type")).thenReturn(Collections.emptyList());
        when(mockPaymentTypeMapper.getMaxSortingAndMaxPaymentType("storeGuid")).thenReturn(paymentTypeDO);

        // Run the test
        paymentTypeServiceImplUnderTest.add(paymentTypeDTO);

        // Verify the results
        verify(mockRedisService).deletePaymentType("storeGuid");
    }

    @Test
    public void testUpdate() {
        // Setup
        final PaymentTypeDTO paymentTypeDTO = new PaymentTypeDTO();
        paymentTypeDTO.setStoreGuid("storeGuid");
        paymentTypeDTO.setStoreName("storeName");
        paymentTypeDTO.setPaymentTypeGuid("paymentTypeGuid");
        paymentTypeDTO.setPaymentTypeName("name");
        paymentTypeDTO.setPaymentType(0);
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setStoreGuid("storeGuid");
        storeDTO.setStoreName("storeName");
        paymentTypeDTO.setStoreDTOS(Arrays.asList(storeDTO));
        paymentTypeDTO.setSorting(0);
        paymentTypeDTO.setAppId("appId");
        paymentTypeDTO.setAppSecretKey("appSecret");
        paymentTypeDTO.setSource(0);
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setStoreGuid("storeGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setAccountName("accountName");
        paymentInfoDTO.setIsDefaultAccount(0);
        paymentInfoDTO.setDiversionRules("diversionRules");
        paymentInfoDTO.setDiversionRule(Arrays.asList(0));
        paymentTypeDTO.setJhPayInfoList(Arrays.asList(paymentInfoDTO));

        when(mockPaymentTypeMapper.countByStoreGuidInAndName(Arrays.asList("value"), "name",
                "paymentTypeGuid")).thenReturn(0);

        // Configure EnterpriseClientService.update(...).
        final PaymentInfoDTO paymentInfoDTO1 = new PaymentInfoDTO();
        paymentInfoDTO1.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO1.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO1.setStoreGuid("storeGuid");
        paymentInfoDTO1.setAppId("appId");
        paymentInfoDTO1.setAppSecret("appSecret");
        paymentInfoDTO1.setAccountName("accountName");
        paymentInfoDTO1.setIsDefaultAccount(0);
        paymentInfoDTO1.setDiversionRules("diversionRules");
        paymentInfoDTO1.setDiversionRule(Arrays.asList(0));
        when(mockEnterpriseClientService.update(paymentInfoDTO1)).thenReturn(false);

        // Run the test
        paymentTypeServiceImplUnderTest.update(paymentTypeDTO);

        // Verify the results
        verify(mockPaymentTypeMapper).update(PaymentTypeDO.builder()
                .paymentTypeGuid("parentPaymentTypeGuid")
                .paymentTypeName("name")
                .paymentType(0)
                .state(0)
                .storeGuid("storeGuid")
                .storeName("storeName")
                .sorting(0)
                .source(0)
                .paymentMode(0)
                .parentPaymentTypeGuid("parentPaymentTypeGuid")
                .paymentShunt(0)
                .build());
        verify(mockRedisService).deletePaymentType("storeGuid");
    }

    @Test
    public void testUpdate_EnterpriseClientServiceReturnsNull() {
        // Setup
        final PaymentTypeDTO paymentTypeDTO = new PaymentTypeDTO();
        paymentTypeDTO.setStoreGuid("storeGuid");
        paymentTypeDTO.setStoreName("storeName");
        paymentTypeDTO.setPaymentTypeGuid("paymentTypeGuid");
        paymentTypeDTO.setPaymentTypeName("name");
        paymentTypeDTO.setPaymentType(0);
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setStoreGuid("storeGuid");
        storeDTO.setStoreName("storeName");
        paymentTypeDTO.setStoreDTOS(Arrays.asList(storeDTO));
        paymentTypeDTO.setSorting(0);
        paymentTypeDTO.setAppId("appId");
        paymentTypeDTO.setAppSecretKey("appSecret");
        paymentTypeDTO.setSource(0);
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setStoreGuid("storeGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setAccountName("accountName");
        paymentInfoDTO.setIsDefaultAccount(0);
        paymentInfoDTO.setDiversionRules("diversionRules");
        paymentInfoDTO.setDiversionRule(Arrays.asList(0));
        paymentTypeDTO.setJhPayInfoList(Arrays.asList(paymentInfoDTO));

        when(mockPaymentTypeMapper.countByStoreGuidInAndName(Arrays.asList("value"), "name",
                "paymentTypeGuid")).thenReturn(0);

        // Configure EnterpriseClientService.update(...).
        final PaymentInfoDTO paymentInfoDTO1 = new PaymentInfoDTO();
        paymentInfoDTO1.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO1.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO1.setStoreGuid("storeGuid");
        paymentInfoDTO1.setAppId("appId");
        paymentInfoDTO1.setAppSecret("appSecret");
        paymentInfoDTO1.setAccountName("accountName");
        paymentInfoDTO1.setIsDefaultAccount(0);
        paymentInfoDTO1.setDiversionRules("diversionRules");
        paymentInfoDTO1.setDiversionRule(Arrays.asList(0));
        when(mockEnterpriseClientService.update(paymentInfoDTO1)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> paymentTypeServiceImplUnderTest.update(paymentTypeDTO))
                .isInstanceOf(IllegalParameterException.class);
    }

    @Test
    public void testUpdate_EnterpriseClientServiceReturnsTrue() {
        // Setup
        final PaymentTypeDTO paymentTypeDTO = new PaymentTypeDTO();
        paymentTypeDTO.setStoreGuid("storeGuid");
        paymentTypeDTO.setStoreName("storeName");
        paymentTypeDTO.setPaymentTypeGuid("paymentTypeGuid");
        paymentTypeDTO.setPaymentTypeName("name");
        paymentTypeDTO.setPaymentType(0);
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setStoreGuid("storeGuid");
        storeDTO.setStoreName("storeName");
        paymentTypeDTO.setStoreDTOS(Arrays.asList(storeDTO));
        paymentTypeDTO.setSorting(0);
        paymentTypeDTO.setAppId("appId");
        paymentTypeDTO.setAppSecretKey("appSecret");
        paymentTypeDTO.setSource(0);
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setStoreGuid("storeGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setAccountName("accountName");
        paymentInfoDTO.setIsDefaultAccount(0);
        paymentInfoDTO.setDiversionRules("diversionRules");
        paymentInfoDTO.setDiversionRule(Arrays.asList(0));
        paymentTypeDTO.setJhPayInfoList(Arrays.asList(paymentInfoDTO));

        when(mockPaymentTypeMapper.countByStoreGuidInAndName(Arrays.asList("value"), "name",
                "paymentTypeGuid")).thenReturn(0);

        // Configure EnterpriseClientService.update(...).
        final PaymentInfoDTO paymentInfoDTO1 = new PaymentInfoDTO();
        paymentInfoDTO1.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO1.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO1.setStoreGuid("storeGuid");
        paymentInfoDTO1.setAppId("appId");
        paymentInfoDTO1.setAppSecret("appSecret");
        paymentInfoDTO1.setAccountName("accountName");
        paymentInfoDTO1.setIsDefaultAccount(0);
        paymentInfoDTO1.setDiversionRules("diversionRules");
        paymentInfoDTO1.setDiversionRule(Arrays.asList(0));
        when(mockEnterpriseClientService.update(paymentInfoDTO1)).thenReturn(true);

        // Run the test
        assertThatThrownBy(() -> paymentTypeServiceImplUnderTest.update(paymentTypeDTO))
                .isInstanceOf(IllegalParameterException.class);
    }

    @Test
    public void testDelete() {
        // Setup
        // Run the test
        paymentTypeServiceImplUnderTest.delete("storeGuid", "paymentTypeGuid");

        // Verify the results
        verify(mockPaymentTypeMapper).delete("storeGuid", "paymentTypeGuid");
        verify(mockRedisService).deletePaymentType("storeGuid");
    }

    @Test
    public void testGetAllByAndroid() {
        // Setup
        final PaymentTypeQueryDTO paymentTypeQueryDTO = new PaymentTypeQueryDTO();
        paymentTypeQueryDTO.setStoreGuid("storeGuid");
        paymentTypeQueryDTO.setSource(0);

        final PaymentTypeDTO paymentTypeDTO = new PaymentTypeDTO();
        paymentTypeDTO.setStoreGuid("storeGuid");
        paymentTypeDTO.setStoreName("storeName");
        paymentTypeDTO.setPaymentTypeGuid("paymentTypeGuid");
        paymentTypeDTO.setPaymentTypeName("name");
        paymentTypeDTO.setPaymentType(0);
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setStoreGuid("storeGuid");
        storeDTO.setStoreName("storeName");
        paymentTypeDTO.setStoreDTOS(Arrays.asList(storeDTO));
        paymentTypeDTO.setSorting(0);
        paymentTypeDTO.setAppId("appId");
        paymentTypeDTO.setAppSecretKey("appSecret");
        paymentTypeDTO.setSource(0);
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setStoreGuid("storeGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setAccountName("accountName");
        paymentInfoDTO.setIsDefaultAccount(0);
        paymentInfoDTO.setDiversionRules("diversionRules");
        paymentInfoDTO.setDiversionRule(Arrays.asList(0));
        paymentTypeDTO.setJhPayInfoList(Arrays.asList(paymentInfoDTO));
        final List<PaymentTypeDTO> expectedResult = Arrays.asList(paymentTypeDTO);

        // Configure RedisService.getPaymentType(...).
        final PaymentTypeDTO paymentTypeDTO1 = new PaymentTypeDTO();
        paymentTypeDTO1.setStoreGuid("storeGuid");
        paymentTypeDTO1.setStoreName("storeName");
        paymentTypeDTO1.setPaymentTypeGuid("paymentTypeGuid");
        paymentTypeDTO1.setPaymentTypeName("name");
        paymentTypeDTO1.setPaymentType(0);
        final StoreDTO storeDTO1 = new StoreDTO();
        storeDTO1.setStoreGuid("storeGuid");
        storeDTO1.setStoreName("storeName");
        paymentTypeDTO1.setStoreDTOS(Arrays.asList(storeDTO1));
        paymentTypeDTO1.setSorting(0);
        paymentTypeDTO1.setAppId("appId");
        paymentTypeDTO1.setAppSecretKey("appSecret");
        paymentTypeDTO1.setSource(0);
        final PaymentInfoDTO paymentInfoDTO1 = new PaymentInfoDTO();
        paymentInfoDTO1.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO1.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO1.setStoreGuid("storeGuid");
        paymentInfoDTO1.setAppId("appId");
        paymentInfoDTO1.setAppSecret("appSecret");
        paymentInfoDTO1.setAccountName("accountName");
        paymentInfoDTO1.setIsDefaultAccount(0);
        paymentInfoDTO1.setDiversionRules("diversionRules");
        paymentInfoDTO1.setDiversionRule(Arrays.asList(0));
        paymentTypeDTO1.setJhPayInfoList(Arrays.asList(paymentInfoDTO1));
        final List<PaymentTypeDTO> paymentTypeDTOS = Arrays.asList(paymentTypeDTO1);
        when(mockRedisService.getPaymentType("storeGuid")).thenReturn(paymentTypeDTOS);

        // Run the test
        final List<PaymentTypeDTO> result = paymentTypeServiceImplUnderTest.getAllByAndroid(paymentTypeQueryDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetAllByAndroid_RedisServiceGetPaymentTypeReturnsNull() {
        // Setup
        final PaymentTypeQueryDTO paymentTypeQueryDTO = new PaymentTypeQueryDTO();
        paymentTypeQueryDTO.setStoreGuid("storeGuid");
        paymentTypeQueryDTO.setSource(0);

        final PaymentTypeDTO paymentTypeDTO = new PaymentTypeDTO();
        paymentTypeDTO.setStoreGuid("storeGuid");
        paymentTypeDTO.setStoreName("storeName");
        paymentTypeDTO.setPaymentTypeGuid("paymentTypeGuid");
        paymentTypeDTO.setPaymentTypeName("name");
        paymentTypeDTO.setPaymentType(0);
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setStoreGuid("storeGuid");
        storeDTO.setStoreName("storeName");
        paymentTypeDTO.setStoreDTOS(Arrays.asList(storeDTO));
        paymentTypeDTO.setSorting(0);
        paymentTypeDTO.setAppId("appId");
        paymentTypeDTO.setAppSecretKey("appSecret");
        paymentTypeDTO.setSource(0);
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setStoreGuid("storeGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setAccountName("accountName");
        paymentInfoDTO.setIsDefaultAccount(0);
        paymentInfoDTO.setDiversionRules("diversionRules");
        paymentInfoDTO.setDiversionRule(Arrays.asList(0));
        paymentTypeDTO.setJhPayInfoList(Arrays.asList(paymentInfoDTO));
        final List<PaymentTypeDTO> expectedResult = Arrays.asList(paymentTypeDTO);
        when(mockRedisService.getPaymentType("storeGuid")).thenReturn(null);

        // Configure PaymentTypeMapper.getAll(...).
        final PaymentTypeDTO paymentTypeDTO1 = new PaymentTypeDTO();
        paymentTypeDTO1.setStoreGuid("storeGuid");
        paymentTypeDTO1.setStoreName("storeName");
        paymentTypeDTO1.setPaymentTypeGuid("paymentTypeGuid");
        paymentTypeDTO1.setPaymentTypeName("name");
        paymentTypeDTO1.setPaymentType(0);
        final StoreDTO storeDTO1 = new StoreDTO();
        storeDTO1.setStoreGuid("storeGuid");
        storeDTO1.setStoreName("storeName");
        paymentTypeDTO1.setStoreDTOS(Arrays.asList(storeDTO1));
        paymentTypeDTO1.setSorting(0);
        paymentTypeDTO1.setAppId("appId");
        paymentTypeDTO1.setAppSecretKey("appSecret");
        paymentTypeDTO1.setSource(0);
        final PaymentInfoDTO paymentInfoDTO1 = new PaymentInfoDTO();
        paymentInfoDTO1.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO1.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO1.setStoreGuid("storeGuid");
        paymentInfoDTO1.setAppId("appId");
        paymentInfoDTO1.setAppSecret("appSecret");
        paymentInfoDTO1.setAccountName("accountName");
        paymentInfoDTO1.setIsDefaultAccount(0);
        paymentInfoDTO1.setDiversionRules("diversionRules");
        paymentInfoDTO1.setDiversionRule(Arrays.asList(0));
        paymentTypeDTO1.setJhPayInfoList(Arrays.asList(paymentInfoDTO1));
        final List<PaymentTypeDTO> paymentTypeDTOS = Arrays.asList(paymentTypeDTO1);
        when(mockPaymentTypeMapper.getAll("storeGuid", 0)).thenReturn(paymentTypeDTOS);

        // Run the test
        final List<PaymentTypeDTO> result = paymentTypeServiceImplUnderTest.getAllByAndroid(paymentTypeQueryDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);

        // Confirm RedisService.putPaymentType(...).
        final PaymentTypeDTO paymentTypeDTO2 = new PaymentTypeDTO();
        paymentTypeDTO2.setStoreGuid("storeGuid");
        paymentTypeDTO2.setStoreName("storeName");
        paymentTypeDTO2.setPaymentTypeGuid("paymentTypeGuid");
        paymentTypeDTO2.setPaymentTypeName("name");
        paymentTypeDTO2.setPaymentType(0);
        final StoreDTO storeDTO2 = new StoreDTO();
        storeDTO2.setStoreGuid("storeGuid");
        storeDTO2.setStoreName("storeName");
        paymentTypeDTO2.setStoreDTOS(Arrays.asList(storeDTO2));
        paymentTypeDTO2.setSorting(0);
        paymentTypeDTO2.setAppId("appId");
        paymentTypeDTO2.setAppSecretKey("appSecret");
        paymentTypeDTO2.setSource(0);
        final PaymentInfoDTO paymentInfoDTO2 = new PaymentInfoDTO();
        paymentInfoDTO2.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO2.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO2.setStoreGuid("storeGuid");
        paymentInfoDTO2.setAppId("appId");
        paymentInfoDTO2.setAppSecret("appSecret");
        paymentInfoDTO2.setAccountName("accountName");
        paymentInfoDTO2.setIsDefaultAccount(0);
        paymentInfoDTO2.setDiversionRules("diversionRules");
        paymentInfoDTO2.setDiversionRule(Arrays.asList(0));
        paymentTypeDTO2.setJhPayInfoList(Arrays.asList(paymentInfoDTO2));
        final List<PaymentTypeDTO> all = Arrays.asList(paymentTypeDTO2);
        verify(mockRedisService).putPaymentType("storeGuid", all);
    }

    @Test
    public void testGetAllByAndroid_RedisServiceGetPaymentTypeReturnsNoItems() {
        // Setup
        final PaymentTypeQueryDTO paymentTypeQueryDTO = new PaymentTypeQueryDTO();
        paymentTypeQueryDTO.setStoreGuid("storeGuid");
        paymentTypeQueryDTO.setSource(0);

        when(mockRedisService.getPaymentType("storeGuid")).thenReturn(Collections.emptyList());

        // Run the test
        final List<PaymentTypeDTO> result = paymentTypeServiceImplUnderTest.getAllByAndroid(paymentTypeQueryDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testGetAllByAndroid_PaymentTypeMapperReturnsNoItems() {
        // Setup
        final PaymentTypeQueryDTO paymentTypeQueryDTO = new PaymentTypeQueryDTO();
        paymentTypeQueryDTO.setStoreGuid("storeGuid");
        paymentTypeQueryDTO.setSource(0);

        when(mockRedisService.getPaymentType("storeGuid")).thenReturn(null);
        when(mockPaymentTypeMapper.getAll("storeGuid", 0)).thenReturn(Collections.emptyList());

        // Run the test
        final List<PaymentTypeDTO> result = paymentTypeServiceImplUnderTest.getAllByAndroid(paymentTypeQueryDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());

        // Confirm RedisService.putPaymentType(...).
        final PaymentTypeDTO paymentTypeDTO = new PaymentTypeDTO();
        paymentTypeDTO.setStoreGuid("storeGuid");
        paymentTypeDTO.setStoreName("storeName");
        paymentTypeDTO.setPaymentTypeGuid("paymentTypeGuid");
        paymentTypeDTO.setPaymentTypeName("name");
        paymentTypeDTO.setPaymentType(0);
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setStoreGuid("storeGuid");
        storeDTO.setStoreName("storeName");
        paymentTypeDTO.setStoreDTOS(Arrays.asList(storeDTO));
        paymentTypeDTO.setSorting(0);
        paymentTypeDTO.setAppId("appId");
        paymentTypeDTO.setAppSecretKey("appSecret");
        paymentTypeDTO.setSource(0);
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setStoreGuid("storeGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setAccountName("accountName");
        paymentInfoDTO.setIsDefaultAccount(0);
        paymentInfoDTO.setDiversionRules("diversionRules");
        paymentInfoDTO.setDiversionRule(Arrays.asList(0));
        paymentTypeDTO.setJhPayInfoList(Arrays.asList(paymentInfoDTO));
        final List<PaymentTypeDTO> all = Arrays.asList(paymentTypeDTO);
        verify(mockRedisService).putPaymentType("storeGuid", all);
    }

    @Test
    public void testGetAll() {
        // Setup
        final PaymentTypeQueryDTO paymentTypeQueryDTO = new PaymentTypeQueryDTO();
        paymentTypeQueryDTO.setStoreGuid("storeGuid");
        paymentTypeQueryDTO.setSource(0);

        final PaymentTypeDTO paymentTypeDTO = new PaymentTypeDTO();
        paymentTypeDTO.setStoreGuid("storeGuid");
        paymentTypeDTO.setStoreName("storeName");
        paymentTypeDTO.setPaymentTypeGuid("paymentTypeGuid");
        paymentTypeDTO.setPaymentTypeName("name");
        paymentTypeDTO.setPaymentType(0);
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setStoreGuid("storeGuid");
        storeDTO.setStoreName("storeName");
        paymentTypeDTO.setStoreDTOS(Arrays.asList(storeDTO));
        paymentTypeDTO.setSorting(0);
        paymentTypeDTO.setAppId("appId");
        paymentTypeDTO.setAppSecretKey("appSecret");
        paymentTypeDTO.setSource(0);
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setStoreGuid("storeGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setAccountName("accountName");
        paymentInfoDTO.setIsDefaultAccount(0);
        paymentInfoDTO.setDiversionRules("diversionRules");
        paymentInfoDTO.setDiversionRule(Arrays.asList(0));
        paymentTypeDTO.setJhPayInfoList(Arrays.asList(paymentInfoDTO));
        final List<PaymentTypeDTO> expectedResult = Arrays.asList(paymentTypeDTO);

        // Configure PaymentTypeMapper.getAll(...).
        final PaymentTypeDTO paymentTypeDTO1 = new PaymentTypeDTO();
        paymentTypeDTO1.setStoreGuid("storeGuid");
        paymentTypeDTO1.setStoreName("storeName");
        paymentTypeDTO1.setPaymentTypeGuid("paymentTypeGuid");
        paymentTypeDTO1.setPaymentTypeName("name");
        paymentTypeDTO1.setPaymentType(0);
        final StoreDTO storeDTO1 = new StoreDTO();
        storeDTO1.setStoreGuid("storeGuid");
        storeDTO1.setStoreName("storeName");
        paymentTypeDTO1.setStoreDTOS(Arrays.asList(storeDTO1));
        paymentTypeDTO1.setSorting(0);
        paymentTypeDTO1.setAppId("appId");
        paymentTypeDTO1.setAppSecretKey("appSecret");
        paymentTypeDTO1.setSource(0);
        final PaymentInfoDTO paymentInfoDTO1 = new PaymentInfoDTO();
        paymentInfoDTO1.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO1.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO1.setStoreGuid("storeGuid");
        paymentInfoDTO1.setAppId("appId");
        paymentInfoDTO1.setAppSecret("appSecret");
        paymentInfoDTO1.setAccountName("accountName");
        paymentInfoDTO1.setIsDefaultAccount(0);
        paymentInfoDTO1.setDiversionRules("diversionRules");
        paymentInfoDTO1.setDiversionRule(Arrays.asList(0));
        paymentTypeDTO1.setJhPayInfoList(Arrays.asList(paymentInfoDTO1));
        final List<PaymentTypeDTO> paymentTypeDTOS = Arrays.asList(paymentTypeDTO1);
        when(mockPaymentTypeMapper.getAll("storeGuid", 0)).thenReturn(paymentTypeDTOS);

        // Configure RedisService.getJHPayInfoList(...).
        final PaymentInfoDTO paymentInfoDTO2 = new PaymentInfoDTO();
        paymentInfoDTO2.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO2.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO2.setStoreGuid("storeGuid");
        paymentInfoDTO2.setAppId("appId");
        paymentInfoDTO2.setAppSecret("appSecret");
        paymentInfoDTO2.setAccountName("accountName");
        paymentInfoDTO2.setIsDefaultAccount(0);
        paymentInfoDTO2.setDiversionRules("diversionRules");
        paymentInfoDTO2.setDiversionRule(Arrays.asList(0));
        final List<PaymentInfoDTO> paymentInfoDTOS = Arrays.asList(paymentInfoDTO2);
        when(mockRedisService.getJHPayInfoList("storeGuid")).thenReturn(paymentInfoDTOS);

        // Run the test
        final List<PaymentTypeDTO> result = paymentTypeServiceImplUnderTest.getAll(paymentTypeQueryDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetAll_PaymentTypeMapperReturnsNoItems() {
        // Setup
        final PaymentTypeQueryDTO paymentTypeQueryDTO = new PaymentTypeQueryDTO();
        paymentTypeQueryDTO.setStoreGuid("storeGuid");
        paymentTypeQueryDTO.setSource(0);

        when(mockPaymentTypeMapper.getAll("storeGuid", 0)).thenReturn(Collections.emptyList());

        // Configure RedisService.getJHPayInfoList(...).
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setStoreGuid("storeGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setAccountName("accountName");
        paymentInfoDTO.setIsDefaultAccount(0);
        paymentInfoDTO.setDiversionRules("diversionRules");
        paymentInfoDTO.setDiversionRule(Arrays.asList(0));
        final List<PaymentInfoDTO> paymentInfoDTOS = Arrays.asList(paymentInfoDTO);
        when(mockRedisService.getJHPayInfoList("storeGuid")).thenReturn(paymentInfoDTOS);

        // Run the test
        final List<PaymentTypeDTO> result = paymentTypeServiceImplUnderTest.getAll(paymentTypeQueryDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testGetAll_RedisServiceGetJHPayInfoListReturnsNoItems() {
        // Setup
        final PaymentTypeQueryDTO paymentTypeQueryDTO = new PaymentTypeQueryDTO();
        paymentTypeQueryDTO.setStoreGuid("storeGuid");
        paymentTypeQueryDTO.setSource(0);

        final PaymentTypeDTO paymentTypeDTO = new PaymentTypeDTO();
        paymentTypeDTO.setStoreGuid("storeGuid");
        paymentTypeDTO.setStoreName("storeName");
        paymentTypeDTO.setPaymentTypeGuid("paymentTypeGuid");
        paymentTypeDTO.setPaymentTypeName("name");
        paymentTypeDTO.setPaymentType(0);
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setStoreGuid("storeGuid");
        storeDTO.setStoreName("storeName");
        paymentTypeDTO.setStoreDTOS(Arrays.asList(storeDTO));
        paymentTypeDTO.setSorting(0);
        paymentTypeDTO.setAppId("appId");
        paymentTypeDTO.setAppSecretKey("appSecret");
        paymentTypeDTO.setSource(0);
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setStoreGuid("storeGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setAccountName("accountName");
        paymentInfoDTO.setIsDefaultAccount(0);
        paymentInfoDTO.setDiversionRules("diversionRules");
        paymentInfoDTO.setDiversionRule(Arrays.asList(0));
        paymentTypeDTO.setJhPayInfoList(Arrays.asList(paymentInfoDTO));
        final List<PaymentTypeDTO> expectedResult = Arrays.asList(paymentTypeDTO);

        // Configure PaymentTypeMapper.getAll(...).
        final PaymentTypeDTO paymentTypeDTO1 = new PaymentTypeDTO();
        paymentTypeDTO1.setStoreGuid("storeGuid");
        paymentTypeDTO1.setStoreName("storeName");
        paymentTypeDTO1.setPaymentTypeGuid("paymentTypeGuid");
        paymentTypeDTO1.setPaymentTypeName("name");
        paymentTypeDTO1.setPaymentType(0);
        final StoreDTO storeDTO1 = new StoreDTO();
        storeDTO1.setStoreGuid("storeGuid");
        storeDTO1.setStoreName("storeName");
        paymentTypeDTO1.setStoreDTOS(Arrays.asList(storeDTO1));
        paymentTypeDTO1.setSorting(0);
        paymentTypeDTO1.setAppId("appId");
        paymentTypeDTO1.setAppSecretKey("appSecret");
        paymentTypeDTO1.setSource(0);
        final PaymentInfoDTO paymentInfoDTO1 = new PaymentInfoDTO();
        paymentInfoDTO1.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO1.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO1.setStoreGuid("storeGuid");
        paymentInfoDTO1.setAppId("appId");
        paymentInfoDTO1.setAppSecret("appSecret");
        paymentInfoDTO1.setAccountName("accountName");
        paymentInfoDTO1.setIsDefaultAccount(0);
        paymentInfoDTO1.setDiversionRules("diversionRules");
        paymentInfoDTO1.setDiversionRule(Arrays.asList(0));
        paymentTypeDTO1.setJhPayInfoList(Arrays.asList(paymentInfoDTO1));
        final List<PaymentTypeDTO> paymentTypeDTOS = Arrays.asList(paymentTypeDTO1);
        when(mockPaymentTypeMapper.getAll("storeGuid", 0)).thenReturn(paymentTypeDTOS);

        when(mockRedisService.getJHPayInfoList("storeGuid")).thenReturn(Collections.emptyList());

        // Configure EnterpriseClientService.listPaymentInfo(...).
        final PaymentInfoDTO paymentInfoDTO2 = new PaymentInfoDTO();
        paymentInfoDTO2.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO2.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO2.setStoreGuid("storeGuid");
        paymentInfoDTO2.setAppId("appId");
        paymentInfoDTO2.setAppSecret("appSecret");
        paymentInfoDTO2.setAccountName("accountName");
        paymentInfoDTO2.setIsDefaultAccount(0);
        paymentInfoDTO2.setDiversionRules("diversionRules");
        paymentInfoDTO2.setDiversionRule(Arrays.asList(0));
        final List<PaymentInfoDTO> paymentInfoDTOS = Arrays.asList(paymentInfoDTO2);
        when(mockEnterpriseClientService.listPaymentInfo("enterpriseGuid", "storeGuid")).thenReturn(paymentInfoDTOS);

        // Run the test
        final List<PaymentTypeDTO> result = paymentTypeServiceImplUnderTest.getAll(paymentTypeQueryDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetAll_EnterpriseClientServiceReturnsNoItems() {
        // Setup
        final PaymentTypeQueryDTO paymentTypeQueryDTO = new PaymentTypeQueryDTO();
        paymentTypeQueryDTO.setStoreGuid("storeGuid");
        paymentTypeQueryDTO.setSource(0);

        final PaymentTypeDTO paymentTypeDTO = new PaymentTypeDTO();
        paymentTypeDTO.setStoreGuid("storeGuid");
        paymentTypeDTO.setStoreName("storeName");
        paymentTypeDTO.setPaymentTypeGuid("paymentTypeGuid");
        paymentTypeDTO.setPaymentTypeName("name");
        paymentTypeDTO.setPaymentType(0);
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setStoreGuid("storeGuid");
        storeDTO.setStoreName("storeName");
        paymentTypeDTO.setStoreDTOS(Arrays.asList(storeDTO));
        paymentTypeDTO.setSorting(0);
        paymentTypeDTO.setAppId("appId");
        paymentTypeDTO.setAppSecretKey("appSecret");
        paymentTypeDTO.setSource(0);
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setStoreGuid("storeGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setAccountName("accountName");
        paymentInfoDTO.setIsDefaultAccount(0);
        paymentInfoDTO.setDiversionRules("diversionRules");
        paymentInfoDTO.setDiversionRule(Arrays.asList(0));
        paymentTypeDTO.setJhPayInfoList(Arrays.asList(paymentInfoDTO));
        final List<PaymentTypeDTO> expectedResult = Arrays.asList(paymentTypeDTO);

        // Configure PaymentTypeMapper.getAll(...).
        final PaymentTypeDTO paymentTypeDTO1 = new PaymentTypeDTO();
        paymentTypeDTO1.setStoreGuid("storeGuid");
        paymentTypeDTO1.setStoreName("storeName");
        paymentTypeDTO1.setPaymentTypeGuid("paymentTypeGuid");
        paymentTypeDTO1.setPaymentTypeName("name");
        paymentTypeDTO1.setPaymentType(0);
        final StoreDTO storeDTO1 = new StoreDTO();
        storeDTO1.setStoreGuid("storeGuid");
        storeDTO1.setStoreName("storeName");
        paymentTypeDTO1.setStoreDTOS(Arrays.asList(storeDTO1));
        paymentTypeDTO1.setSorting(0);
        paymentTypeDTO1.setAppId("appId");
        paymentTypeDTO1.setAppSecretKey("appSecret");
        paymentTypeDTO1.setSource(0);
        final PaymentInfoDTO paymentInfoDTO1 = new PaymentInfoDTO();
        paymentInfoDTO1.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO1.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO1.setStoreGuid("storeGuid");
        paymentInfoDTO1.setAppId("appId");
        paymentInfoDTO1.setAppSecret("appSecret");
        paymentInfoDTO1.setAccountName("accountName");
        paymentInfoDTO1.setIsDefaultAccount(0);
        paymentInfoDTO1.setDiversionRules("diversionRules");
        paymentInfoDTO1.setDiversionRule(Arrays.asList(0));
        paymentTypeDTO1.setJhPayInfoList(Arrays.asList(paymentInfoDTO1));
        final List<PaymentTypeDTO> paymentTypeDTOS = Arrays.asList(paymentTypeDTO1);
        when(mockPaymentTypeMapper.getAll("storeGuid", 0)).thenReturn(paymentTypeDTOS);

        when(mockRedisService.getJHPayInfoList("storeGuid")).thenReturn(Collections.emptyList());
        when(mockEnterpriseClientService.listPaymentInfo("enterpriseGuid", "storeGuid"))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<PaymentTypeDTO> result = paymentTypeServiceImplUnderTest.getAll(paymentTypeQueryDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetAllByStoreGuidList() {
        // Setup
        final PaymentTypeQueryDTO paymentTypeQueryDTO = new PaymentTypeQueryDTO();
        paymentTypeQueryDTO.setStoreGuid("storeGuid");
        paymentTypeQueryDTO.setSource(0);
        final PaymentTypeBatchQureyDTO paymentTypeBatchQureyDTO = new PaymentTypeBatchQureyDTO(
                Arrays.asList(paymentTypeQueryDTO));
        final PaymentTypeDTO paymentTypeDTO = new PaymentTypeDTO();
        paymentTypeDTO.setStoreGuid("storeGuid");
        paymentTypeDTO.setStoreName("storeName");
        paymentTypeDTO.setPaymentTypeGuid("paymentTypeGuid");
        paymentTypeDTO.setPaymentTypeName("name");
        paymentTypeDTO.setPaymentType(0);
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setStoreGuid("storeGuid");
        storeDTO.setStoreName("storeName");
        paymentTypeDTO.setStoreDTOS(Arrays.asList(storeDTO));
        paymentTypeDTO.setSorting(0);
        paymentTypeDTO.setAppId("appId");
        paymentTypeDTO.setAppSecretKey("appSecret");
        paymentTypeDTO.setSource(0);
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setStoreGuid("storeGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setAccountName("accountName");
        paymentInfoDTO.setIsDefaultAccount(0);
        paymentInfoDTO.setDiversionRules("diversionRules");
        paymentInfoDTO.setDiversionRule(Arrays.asList(0));
        paymentTypeDTO.setJhPayInfoList(Arrays.asList(paymentInfoDTO));
        final List<PaymentTypeBatchDTO> expectedResult = Arrays.asList(
                new PaymentTypeBatchDTO("storeGuid", Arrays.asList(paymentTypeDTO)));

        // Configure PaymentTypeMapper.getAll(...).
        final PaymentTypeDTO paymentTypeDTO1 = new PaymentTypeDTO();
        paymentTypeDTO1.setStoreGuid("storeGuid");
        paymentTypeDTO1.setStoreName("storeName");
        paymentTypeDTO1.setPaymentTypeGuid("paymentTypeGuid");
        paymentTypeDTO1.setPaymentTypeName("name");
        paymentTypeDTO1.setPaymentType(0);
        final StoreDTO storeDTO1 = new StoreDTO();
        storeDTO1.setStoreGuid("storeGuid");
        storeDTO1.setStoreName("storeName");
        paymentTypeDTO1.setStoreDTOS(Arrays.asList(storeDTO1));
        paymentTypeDTO1.setSorting(0);
        paymentTypeDTO1.setAppId("appId");
        paymentTypeDTO1.setAppSecretKey("appSecret");
        paymentTypeDTO1.setSource(0);
        final PaymentInfoDTO paymentInfoDTO1 = new PaymentInfoDTO();
        paymentInfoDTO1.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO1.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO1.setStoreGuid("storeGuid");
        paymentInfoDTO1.setAppId("appId");
        paymentInfoDTO1.setAppSecret("appSecret");
        paymentInfoDTO1.setAccountName("accountName");
        paymentInfoDTO1.setIsDefaultAccount(0);
        paymentInfoDTO1.setDiversionRules("diversionRules");
        paymentInfoDTO1.setDiversionRule(Arrays.asList(0));
        paymentTypeDTO1.setJhPayInfoList(Arrays.asList(paymentInfoDTO1));
        final List<PaymentTypeDTO> paymentTypeDTOS = Arrays.asList(paymentTypeDTO1);
        when(mockPaymentTypeMapper.getAll("storeGuid", 0)).thenReturn(paymentTypeDTOS);

        // Configure RedisService.getJHPayInfoList(...).
        final PaymentInfoDTO paymentInfoDTO2 = new PaymentInfoDTO();
        paymentInfoDTO2.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO2.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO2.setStoreGuid("storeGuid");
        paymentInfoDTO2.setAppId("appId");
        paymentInfoDTO2.setAppSecret("appSecret");
        paymentInfoDTO2.setAccountName("accountName");
        paymentInfoDTO2.setIsDefaultAccount(0);
        paymentInfoDTO2.setDiversionRules("diversionRules");
        paymentInfoDTO2.setDiversionRule(Arrays.asList(0));
        final List<PaymentInfoDTO> paymentInfoDTOS = Arrays.asList(paymentInfoDTO2);
        when(mockRedisService.getJHPayInfoList("storeGuid")).thenReturn(paymentInfoDTOS);

        // Run the test
        final List<PaymentTypeBatchDTO> result = paymentTypeServiceImplUnderTest.getAllByStoreGuidList(
                paymentTypeBatchQureyDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetAllByStoreGuidList_PaymentTypeMapperReturnsNoItems() {
        // Setup
        final PaymentTypeQueryDTO paymentTypeQueryDTO = new PaymentTypeQueryDTO();
        paymentTypeQueryDTO.setStoreGuid("storeGuid");
        paymentTypeQueryDTO.setSource(0);
        final PaymentTypeBatchQureyDTO paymentTypeBatchQureyDTO = new PaymentTypeBatchQureyDTO(
                Arrays.asList(paymentTypeQueryDTO));
        final PaymentTypeDTO paymentTypeDTO = new PaymentTypeDTO();
        paymentTypeDTO.setStoreGuid("storeGuid");
        paymentTypeDTO.setStoreName("storeName");
        paymentTypeDTO.setPaymentTypeGuid("paymentTypeGuid");
        paymentTypeDTO.setPaymentTypeName("name");
        paymentTypeDTO.setPaymentType(0);
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setStoreGuid("storeGuid");
        storeDTO.setStoreName("storeName");
        paymentTypeDTO.setStoreDTOS(Arrays.asList(storeDTO));
        paymentTypeDTO.setSorting(0);
        paymentTypeDTO.setAppId("appId");
        paymentTypeDTO.setAppSecretKey("appSecret");
        paymentTypeDTO.setSource(0);
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setStoreGuid("storeGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setAccountName("accountName");
        paymentInfoDTO.setIsDefaultAccount(0);
        paymentInfoDTO.setDiversionRules("diversionRules");
        paymentInfoDTO.setDiversionRule(Arrays.asList(0));
        paymentTypeDTO.setJhPayInfoList(Arrays.asList(paymentInfoDTO));
        final List<PaymentTypeBatchDTO> expectedResult = Arrays.asList(
                new PaymentTypeBatchDTO("storeGuid", Arrays.asList(paymentTypeDTO)));
        when(mockPaymentTypeMapper.getAll("storeGuid", 0)).thenReturn(Collections.emptyList());

        // Configure RedisService.getJHPayInfoList(...).
        final PaymentInfoDTO paymentInfoDTO1 = new PaymentInfoDTO();
        paymentInfoDTO1.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO1.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO1.setStoreGuid("storeGuid");
        paymentInfoDTO1.setAppId("appId");
        paymentInfoDTO1.setAppSecret("appSecret");
        paymentInfoDTO1.setAccountName("accountName");
        paymentInfoDTO1.setIsDefaultAccount(0);
        paymentInfoDTO1.setDiversionRules("diversionRules");
        paymentInfoDTO1.setDiversionRule(Arrays.asList(0));
        final List<PaymentInfoDTO> paymentInfoDTOS = Arrays.asList(paymentInfoDTO1);
        when(mockRedisService.getJHPayInfoList("storeGuid")).thenReturn(paymentInfoDTOS);

        // Run the test
        final List<PaymentTypeBatchDTO> result = paymentTypeServiceImplUnderTest.getAllByStoreGuidList(
                paymentTypeBatchQureyDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetAllByStoreGuidList_RedisServiceGetJHPayInfoListReturnsNoItems() {
        // Setup
        final PaymentTypeQueryDTO paymentTypeQueryDTO = new PaymentTypeQueryDTO();
        paymentTypeQueryDTO.setStoreGuid("storeGuid");
        paymentTypeQueryDTO.setSource(0);
        final PaymentTypeBatchQureyDTO paymentTypeBatchQureyDTO = new PaymentTypeBatchQureyDTO(
                Arrays.asList(paymentTypeQueryDTO));
        final PaymentTypeDTO paymentTypeDTO = new PaymentTypeDTO();
        paymentTypeDTO.setStoreGuid("storeGuid");
        paymentTypeDTO.setStoreName("storeName");
        paymentTypeDTO.setPaymentTypeGuid("paymentTypeGuid");
        paymentTypeDTO.setPaymentTypeName("name");
        paymentTypeDTO.setPaymentType(0);
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setStoreGuid("storeGuid");
        storeDTO.setStoreName("storeName");
        paymentTypeDTO.setStoreDTOS(Arrays.asList(storeDTO));
        paymentTypeDTO.setSorting(0);
        paymentTypeDTO.setAppId("appId");
        paymentTypeDTO.setAppSecretKey("appSecret");
        paymentTypeDTO.setSource(0);
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setStoreGuid("storeGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setAccountName("accountName");
        paymentInfoDTO.setIsDefaultAccount(0);
        paymentInfoDTO.setDiversionRules("diversionRules");
        paymentInfoDTO.setDiversionRule(Arrays.asList(0));
        paymentTypeDTO.setJhPayInfoList(Arrays.asList(paymentInfoDTO));
        final List<PaymentTypeBatchDTO> expectedResult = Arrays.asList(
                new PaymentTypeBatchDTO("storeGuid", Arrays.asList(paymentTypeDTO)));

        // Configure PaymentTypeMapper.getAll(...).
        final PaymentTypeDTO paymentTypeDTO1 = new PaymentTypeDTO();
        paymentTypeDTO1.setStoreGuid("storeGuid");
        paymentTypeDTO1.setStoreName("storeName");
        paymentTypeDTO1.setPaymentTypeGuid("paymentTypeGuid");
        paymentTypeDTO1.setPaymentTypeName("name");
        paymentTypeDTO1.setPaymentType(0);
        final StoreDTO storeDTO1 = new StoreDTO();
        storeDTO1.setStoreGuid("storeGuid");
        storeDTO1.setStoreName("storeName");
        paymentTypeDTO1.setStoreDTOS(Arrays.asList(storeDTO1));
        paymentTypeDTO1.setSorting(0);
        paymentTypeDTO1.setAppId("appId");
        paymentTypeDTO1.setAppSecretKey("appSecret");
        paymentTypeDTO1.setSource(0);
        final PaymentInfoDTO paymentInfoDTO1 = new PaymentInfoDTO();
        paymentInfoDTO1.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO1.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO1.setStoreGuid("storeGuid");
        paymentInfoDTO1.setAppId("appId");
        paymentInfoDTO1.setAppSecret("appSecret");
        paymentInfoDTO1.setAccountName("accountName");
        paymentInfoDTO1.setIsDefaultAccount(0);
        paymentInfoDTO1.setDiversionRules("diversionRules");
        paymentInfoDTO1.setDiversionRule(Arrays.asList(0));
        paymentTypeDTO1.setJhPayInfoList(Arrays.asList(paymentInfoDTO1));
        final List<PaymentTypeDTO> paymentTypeDTOS = Arrays.asList(paymentTypeDTO1);
        when(mockPaymentTypeMapper.getAll("storeGuid", 0)).thenReturn(paymentTypeDTOS);

        when(mockRedisService.getJHPayInfoList("storeGuid")).thenReturn(Collections.emptyList());

        // Configure EnterpriseClientService.listPaymentInfo(...).
        final PaymentInfoDTO paymentInfoDTO2 = new PaymentInfoDTO();
        paymentInfoDTO2.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO2.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO2.setStoreGuid("storeGuid");
        paymentInfoDTO2.setAppId("appId");
        paymentInfoDTO2.setAppSecret("appSecret");
        paymentInfoDTO2.setAccountName("accountName");
        paymentInfoDTO2.setIsDefaultAccount(0);
        paymentInfoDTO2.setDiversionRules("diversionRules");
        paymentInfoDTO2.setDiversionRule(Arrays.asList(0));
        final List<PaymentInfoDTO> paymentInfoDTOS = Arrays.asList(paymentInfoDTO2);
        when(mockEnterpriseClientService.listPaymentInfo("enterpriseGuid", "storeGuid")).thenReturn(paymentInfoDTOS);

        // Run the test
        final List<PaymentTypeBatchDTO> result = paymentTypeServiceImplUnderTest.getAllByStoreGuidList(
                paymentTypeBatchQureyDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetAllByStoreGuidList_EnterpriseClientServiceReturnsNoItems() {
        // Setup
        final PaymentTypeQueryDTO paymentTypeQueryDTO = new PaymentTypeQueryDTO();
        paymentTypeQueryDTO.setStoreGuid("storeGuid");
        paymentTypeQueryDTO.setSource(0);
        final PaymentTypeBatchQureyDTO paymentTypeBatchQureyDTO = new PaymentTypeBatchQureyDTO(
                Arrays.asList(paymentTypeQueryDTO));
        final PaymentTypeDTO paymentTypeDTO = new PaymentTypeDTO();
        paymentTypeDTO.setStoreGuid("storeGuid");
        paymentTypeDTO.setStoreName("storeName");
        paymentTypeDTO.setPaymentTypeGuid("paymentTypeGuid");
        paymentTypeDTO.setPaymentTypeName("name");
        paymentTypeDTO.setPaymentType(0);
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setStoreGuid("storeGuid");
        storeDTO.setStoreName("storeName");
        paymentTypeDTO.setStoreDTOS(Arrays.asList(storeDTO));
        paymentTypeDTO.setSorting(0);
        paymentTypeDTO.setAppId("appId");
        paymentTypeDTO.setAppSecretKey("appSecret");
        paymentTypeDTO.setSource(0);
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setStoreGuid("storeGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setAccountName("accountName");
        paymentInfoDTO.setIsDefaultAccount(0);
        paymentInfoDTO.setDiversionRules("diversionRules");
        paymentInfoDTO.setDiversionRule(Arrays.asList(0));
        paymentTypeDTO.setJhPayInfoList(Arrays.asList(paymentInfoDTO));
        final List<PaymentTypeBatchDTO> expectedResult = Arrays.asList(
                new PaymentTypeBatchDTO("storeGuid", Arrays.asList(paymentTypeDTO)));

        // Configure PaymentTypeMapper.getAll(...).
        final PaymentTypeDTO paymentTypeDTO1 = new PaymentTypeDTO();
        paymentTypeDTO1.setStoreGuid("storeGuid");
        paymentTypeDTO1.setStoreName("storeName");
        paymentTypeDTO1.setPaymentTypeGuid("paymentTypeGuid");
        paymentTypeDTO1.setPaymentTypeName("name");
        paymentTypeDTO1.setPaymentType(0);
        final StoreDTO storeDTO1 = new StoreDTO();
        storeDTO1.setStoreGuid("storeGuid");
        storeDTO1.setStoreName("storeName");
        paymentTypeDTO1.setStoreDTOS(Arrays.asList(storeDTO1));
        paymentTypeDTO1.setSorting(0);
        paymentTypeDTO1.setAppId("appId");
        paymentTypeDTO1.setAppSecretKey("appSecret");
        paymentTypeDTO1.setSource(0);
        final PaymentInfoDTO paymentInfoDTO1 = new PaymentInfoDTO();
        paymentInfoDTO1.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO1.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO1.setStoreGuid("storeGuid");
        paymentInfoDTO1.setAppId("appId");
        paymentInfoDTO1.setAppSecret("appSecret");
        paymentInfoDTO1.setAccountName("accountName");
        paymentInfoDTO1.setIsDefaultAccount(0);
        paymentInfoDTO1.setDiversionRules("diversionRules");
        paymentInfoDTO1.setDiversionRule(Arrays.asList(0));
        paymentTypeDTO1.setJhPayInfoList(Arrays.asList(paymentInfoDTO1));
        final List<PaymentTypeDTO> paymentTypeDTOS = Arrays.asList(paymentTypeDTO1);
        when(mockPaymentTypeMapper.getAll("storeGuid", 0)).thenReturn(paymentTypeDTOS);

        when(mockRedisService.getJHPayInfoList("storeGuid")).thenReturn(Collections.emptyList());
        when(mockEnterpriseClientService.listPaymentInfo("enterpriseGuid", "storeGuid"))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<PaymentTypeBatchDTO> result = paymentTypeServiceImplUnderTest.getAllByStoreGuidList(
                paymentTypeBatchQureyDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testSort() {
        // Setup
        final PaymentTypeDTO paymentTypeDTO = new PaymentTypeDTO();
        paymentTypeDTO.setStoreGuid("storeGuid");
        paymentTypeDTO.setStoreName("storeName");
        paymentTypeDTO.setPaymentTypeGuid("paymentTypeGuid");
        paymentTypeDTO.setPaymentTypeName("name");
        paymentTypeDTO.setPaymentType(0);
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setStoreGuid("storeGuid");
        storeDTO.setStoreName("storeName");
        paymentTypeDTO.setStoreDTOS(Arrays.asList(storeDTO));
        paymentTypeDTO.setSorting(0);
        paymentTypeDTO.setAppId("appId");
        paymentTypeDTO.setAppSecretKey("appSecret");
        paymentTypeDTO.setSource(0);
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setStoreGuid("storeGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setAccountName("accountName");
        paymentInfoDTO.setIsDefaultAccount(0);
        paymentInfoDTO.setDiversionRules("diversionRules");
        paymentInfoDTO.setDiversionRule(Arrays.asList(0));
        paymentTypeDTO.setJhPayInfoList(Arrays.asList(paymentInfoDTO));
        final List<PaymentTypeDTO> paymentTypeDTOS = Arrays.asList(paymentTypeDTO);

        // Run the test
        final String result = paymentTypeServiceImplUnderTest.sort(paymentTypeDTOS);

        // Verify the results
        assertThat(result).isEqualTo("success");

        // Confirm PaymentTypeMapper.updateAllSort(...).
        final PaymentTypeDTO paymentTypeDTO1 = new PaymentTypeDTO();
        paymentTypeDTO1.setStoreGuid("storeGuid");
        paymentTypeDTO1.setStoreName("storeName");
        paymentTypeDTO1.setPaymentTypeGuid("paymentTypeGuid");
        paymentTypeDTO1.setPaymentTypeName("name");
        paymentTypeDTO1.setPaymentType(0);
        final StoreDTO storeDTO1 = new StoreDTO();
        storeDTO1.setStoreGuid("storeGuid");
        storeDTO1.setStoreName("storeName");
        paymentTypeDTO1.setStoreDTOS(Arrays.asList(storeDTO1));
        paymentTypeDTO1.setSorting(0);
        paymentTypeDTO1.setAppId("appId");
        paymentTypeDTO1.setAppSecretKey("appSecret");
        paymentTypeDTO1.setSource(0);
        final PaymentInfoDTO paymentInfoDTO1 = new PaymentInfoDTO();
        paymentInfoDTO1.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO1.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO1.setStoreGuid("storeGuid");
        paymentInfoDTO1.setAppId("appId");
        paymentInfoDTO1.setAppSecret("appSecret");
        paymentInfoDTO1.setAccountName("accountName");
        paymentInfoDTO1.setIsDefaultAccount(0);
        paymentInfoDTO1.setDiversionRules("diversionRules");
        paymentInfoDTO1.setDiversionRule(Arrays.asList(0));
        paymentTypeDTO1.setJhPayInfoList(Arrays.asList(paymentInfoDTO1));
        final List<PaymentTypeDTO> paymentTypeDTOS1 = Arrays.asList(paymentTypeDTO1);
        verify(mockPaymentTypeMapper).updateAllSort(paymentTypeDTOS1);
        verify(mockRedisService).deletePaymentType(new HashSet<>(Arrays.asList("value")));
    }

    @Test
    public void testEditPaymentTypeMode() {
        // Setup
        final PaymentTypeModeDTO modeDTO = new PaymentTypeModeDTO();
        modeDTO.setPaymentTypeGuid("paymentTypeGuid");
        modeDTO.setStoreGuid("storeGuid");
        modeDTO.setPaymentMode(0);

        // Configure PaymentTypeMapper.getOne(...).
        final PaymentTypeDTO paymentTypeDTO = new PaymentTypeDTO();
        paymentTypeDTO.setStoreGuid("storeGuid");
        paymentTypeDTO.setStoreName("storeName");
        paymentTypeDTO.setPaymentTypeGuid("paymentTypeGuid");
        paymentTypeDTO.setPaymentTypeName("name");
        paymentTypeDTO.setPaymentType(0);
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setStoreGuid("storeGuid");
        storeDTO.setStoreName("storeName");
        paymentTypeDTO.setStoreDTOS(Arrays.asList(storeDTO));
        paymentTypeDTO.setSorting(0);
        paymentTypeDTO.setAppId("appId");
        paymentTypeDTO.setAppSecretKey("appSecret");
        paymentTypeDTO.setSource(0);
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setStoreGuid("storeGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setAccountName("accountName");
        paymentInfoDTO.setIsDefaultAccount(0);
        paymentInfoDTO.setDiversionRules("diversionRules");
        paymentInfoDTO.setDiversionRule(Arrays.asList(0));
        paymentTypeDTO.setJhPayInfoList(Arrays.asList(paymentInfoDTO));
        final PaymentTypeDTO paymentTypeDTO1 = new PaymentTypeDTO();
        paymentTypeDTO1.setStoreGuid("storeGuid");
        paymentTypeDTO1.setStoreName("storeName");
        paymentTypeDTO1.setPaymentTypeGuid("paymentTypeGuid");
        paymentTypeDTO1.setPaymentTypeName("name");
        paymentTypeDTO1.setPaymentType(0);
        final StoreDTO storeDTO1 = new StoreDTO();
        storeDTO1.setStoreGuid("storeGuid");
        storeDTO1.setStoreName("storeName");
        paymentTypeDTO1.setStoreDTOS(Arrays.asList(storeDTO1));
        paymentTypeDTO1.setSorting(0);
        paymentTypeDTO1.setAppId("appId");
        paymentTypeDTO1.setAppSecretKey("appSecret");
        paymentTypeDTO1.setSource(0);
        final PaymentInfoDTO paymentInfoDTO1 = new PaymentInfoDTO();
        paymentInfoDTO1.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO1.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO1.setStoreGuid("storeGuid");
        paymentInfoDTO1.setAppId("appId");
        paymentInfoDTO1.setAppSecret("appSecret");
        paymentInfoDTO1.setAccountName("accountName");
        paymentInfoDTO1.setIsDefaultAccount(0);
        paymentInfoDTO1.setDiversionRules("diversionRules");
        paymentInfoDTO1.setDiversionRule(Arrays.asList(0));
        paymentTypeDTO1.setJhPayInfoList(Arrays.asList(paymentInfoDTO1));
        when(mockPaymentTypeMapper.getOne(paymentTypeDTO1)).thenReturn(paymentTypeDTO);

        // Run the test
        final Boolean result = paymentTypeServiceImplUnderTest.editPaymentTypeMode(modeDTO);

        // Verify the results
        assertThat(result).isTrue();
        verify(mockPaymentTypeMapper).updatePaymentTypeMode(modeDTO);
        verify(mockRedisService).deletePaymentType("storeGuid");
    }

    @Test
    public void testEditPaymentTypeMode_PaymentTypeMapperGetOneReturnsNull() {
        // Setup
        final PaymentTypeModeDTO modeDTO = new PaymentTypeModeDTO();
        modeDTO.setPaymentTypeGuid("paymentTypeGuid");
        modeDTO.setStoreGuid("storeGuid");
        modeDTO.setPaymentMode(0);

        // Configure PaymentTypeMapper.getOne(...).
        final PaymentTypeDTO paymentTypeDTO = new PaymentTypeDTO();
        paymentTypeDTO.setStoreGuid("storeGuid");
        paymentTypeDTO.setStoreName("storeName");
        paymentTypeDTO.setPaymentTypeGuid("paymentTypeGuid");
        paymentTypeDTO.setPaymentTypeName("name");
        paymentTypeDTO.setPaymentType(0);
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setStoreGuid("storeGuid");
        storeDTO.setStoreName("storeName");
        paymentTypeDTO.setStoreDTOS(Arrays.asList(storeDTO));
        paymentTypeDTO.setSorting(0);
        paymentTypeDTO.setAppId("appId");
        paymentTypeDTO.setAppSecretKey("appSecret");
        paymentTypeDTO.setSource(0);
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setStoreGuid("storeGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setAccountName("accountName");
        paymentInfoDTO.setIsDefaultAccount(0);
        paymentInfoDTO.setDiversionRules("diversionRules");
        paymentInfoDTO.setDiversionRule(Arrays.asList(0));
        paymentTypeDTO.setJhPayInfoList(Arrays.asList(paymentInfoDTO));
        when(mockPaymentTypeMapper.getOne(paymentTypeDTO)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> paymentTypeServiceImplUnderTest.editPaymentTypeMode(modeDTO))
                .isInstanceOf(BusinessException.class);
    }

    @Test
    public void testPaymentAccountSave() {
        // Setup
        final PaymentAccountReqDTO request = new PaymentAccountReqDTO();
        request.setPaymentTypeGuid("paymentTypeGuid");
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setStoreGuid("storeGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setAccountName("accountName");
        paymentInfoDTO.setIsDefaultAccount(0);
        paymentInfoDTO.setDiversionRules("diversionRules");
        paymentInfoDTO.setDiversionRule(Arrays.asList(0));
        request.setJhPayInfoList(Arrays.asList(paymentInfoDTO));
        request.setPaymentShunt(0);
        request.setStoreGuid("storeGuid");
        request.setToDeleteList(Arrays.asList("value"));

        // Configure JHPayClientService.check(...).
        final MchntValidateDTO mchntValidateDTO = new MchntValidateDTO();
        mchntValidateDTO.setAppId("appId");
        mchntValidateDTO.setAppSecretKey("appSecret");
        when(mockJhPayClientService.check(mchntValidateDTO)).thenReturn(false);

        // Configure EnterpriseClientService.existSameAccountName(...).
        final PaymentInfoDTO paymentInfoDTO1 = new PaymentInfoDTO();
        paymentInfoDTO1.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO1.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO1.setStoreGuid("storeGuid");
        paymentInfoDTO1.setAppId("appId");
        paymentInfoDTO1.setAppSecret("appSecret");
        paymentInfoDTO1.setAccountName("accountName");
        paymentInfoDTO1.setIsDefaultAccount(0);
        paymentInfoDTO1.setDiversionRules("diversionRules");
        paymentInfoDTO1.setDiversionRule(Arrays.asList(0));
        final List<PaymentInfoDTO> paymentInfoDTOS = Arrays.asList(paymentInfoDTO1);
        when(mockEnterpriseClientService.existSameAccountName("enterpriseGuid", "storeGuid", "accountName"))
                .thenReturn(paymentInfoDTOS);

        // Configure PaymentTypeMapper.selectOne(...).
        final PaymentTypeDO paymentTypeDO = PaymentTypeDO.builder()
                .paymentTypeGuid("parentPaymentTypeGuid")
                .paymentTypeName("name")
                .paymentType(0)
                .state(0)
                .storeGuid("storeGuid")
                .storeName("storeName")
                .sorting(0)
                .source(0)
                .paymentMode(0)
                .parentPaymentTypeGuid("parentPaymentTypeGuid")
                .paymentShunt(0)
                .build();
        when(mockPaymentTypeMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(paymentTypeDO);

        // Configure EnterpriseClientService.listPaymentInfo(...).
        final PaymentInfoDTO paymentInfoDTO2 = new PaymentInfoDTO();
        paymentInfoDTO2.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO2.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO2.setStoreGuid("storeGuid");
        paymentInfoDTO2.setAppId("appId");
        paymentInfoDTO2.setAppSecret("appSecret");
        paymentInfoDTO2.setAccountName("accountName");
        paymentInfoDTO2.setIsDefaultAccount(0);
        paymentInfoDTO2.setDiversionRules("diversionRules");
        paymentInfoDTO2.setDiversionRule(Arrays.asList(0));
        final List<PaymentInfoDTO> paymentInfoDTOS1 = Arrays.asList(paymentInfoDTO2);
        when(mockEnterpriseClientService.listPaymentInfo("enterpriseGuid", "storeGuid")).thenReturn(paymentInfoDTOS1);

        // Run the test
        final Boolean result = paymentTypeServiceImplUnderTest.paymentAccountSave(request);

        // Verify the results
        assertThat(result).isFalse();
        verify(mockEnterpriseClientService).deletePayInfoByGuidList(Arrays.asList("value"));

        // Confirm EnterpriseClientService.batchSaveOrUpdatePaymentInfoList(...).
        final PaymentInfoDTO paymentInfoDTO3 = new PaymentInfoDTO();
        paymentInfoDTO3.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO3.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO3.setStoreGuid("storeGuid");
        paymentInfoDTO3.setAppId("appId");
        paymentInfoDTO3.setAppSecret("appSecret");
        paymentInfoDTO3.setAccountName("accountName");
        paymentInfoDTO3.setIsDefaultAccount(0);
        paymentInfoDTO3.setDiversionRules("diversionRules");
        paymentInfoDTO3.setDiversionRule(Arrays.asList(0));
        final List<PaymentInfoDTO> paymentInfoDTOList = Arrays.asList(paymentInfoDTO3);
        verify(mockEnterpriseClientService).batchSaveOrUpdatePaymentInfoList(paymentInfoDTOList);

        // Confirm RedisService.putJHPayInfoList(...).
        final PaymentInfoDTO paymentInfoDTO4 = new PaymentInfoDTO();
        paymentInfoDTO4.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO4.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO4.setStoreGuid("storeGuid");
        paymentInfoDTO4.setAppId("appId");
        paymentInfoDTO4.setAppSecret("appSecret");
        paymentInfoDTO4.setAccountName("accountName");
        paymentInfoDTO4.setIsDefaultAccount(0);
        paymentInfoDTO4.setDiversionRules("diversionRules");
        paymentInfoDTO4.setDiversionRule(Arrays.asList(0));
        final List<PaymentInfoDTO> paymentInfoDTOList1 = Arrays.asList(paymentInfoDTO4);
        verify(mockRedisService).putJHPayInfoList(paymentInfoDTOList1, "storeGuid");
    }

    @Test
    public void testPaymentAccountSave_EnterpriseClientServiceExistSameAccountNameReturnsNoItems() {
        // Setup
        final PaymentAccountReqDTO request = new PaymentAccountReqDTO();
        request.setPaymentTypeGuid("paymentTypeGuid");
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setStoreGuid("storeGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setAccountName("accountName");
        paymentInfoDTO.setIsDefaultAccount(0);
        paymentInfoDTO.setDiversionRules("diversionRules");
        paymentInfoDTO.setDiversionRule(Arrays.asList(0));
        request.setJhPayInfoList(Arrays.asList(paymentInfoDTO));
        request.setPaymentShunt(0);
        request.setStoreGuid("storeGuid");
        request.setToDeleteList(Arrays.asList("value"));

        // Configure JHPayClientService.check(...).
        final MchntValidateDTO mchntValidateDTO = new MchntValidateDTO();
        mchntValidateDTO.setAppId("appId");
        mchntValidateDTO.setAppSecretKey("appSecret");
        when(mockJhPayClientService.check(mchntValidateDTO)).thenReturn(false);

        when(mockEnterpriseClientService.existSameAccountName("enterpriseGuid", "storeGuid", "accountName"))
                .thenReturn(Collections.emptyList());

        // Configure PaymentTypeMapper.selectOne(...).
        final PaymentTypeDO paymentTypeDO = PaymentTypeDO.builder()
                .paymentTypeGuid("parentPaymentTypeGuid")
                .paymentTypeName("name")
                .paymentType(0)
                .state(0)
                .storeGuid("storeGuid")
                .storeName("storeName")
                .sorting(0)
                .source(0)
                .paymentMode(0)
                .parentPaymentTypeGuid("parentPaymentTypeGuid")
                .paymentShunt(0)
                .build();
        when(mockPaymentTypeMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(paymentTypeDO);

        // Configure EnterpriseClientService.listPaymentInfo(...).
        final PaymentInfoDTO paymentInfoDTO1 = new PaymentInfoDTO();
        paymentInfoDTO1.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO1.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO1.setStoreGuid("storeGuid");
        paymentInfoDTO1.setAppId("appId");
        paymentInfoDTO1.setAppSecret("appSecret");
        paymentInfoDTO1.setAccountName("accountName");
        paymentInfoDTO1.setIsDefaultAccount(0);
        paymentInfoDTO1.setDiversionRules("diversionRules");
        paymentInfoDTO1.setDiversionRule(Arrays.asList(0));
        final List<PaymentInfoDTO> paymentInfoDTOS = Arrays.asList(paymentInfoDTO1);
        when(mockEnterpriseClientService.listPaymentInfo("enterpriseGuid", "storeGuid")).thenReturn(paymentInfoDTOS);

        // Run the test
        final Boolean result = paymentTypeServiceImplUnderTest.paymentAccountSave(request);

        // Verify the results
        assertThat(result).isFalse();
        verify(mockEnterpriseClientService).deletePayInfoByGuidList(Arrays.asList("value"));

        // Confirm EnterpriseClientService.batchSaveOrUpdatePaymentInfoList(...).
        final PaymentInfoDTO paymentInfoDTO2 = new PaymentInfoDTO();
        paymentInfoDTO2.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO2.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO2.setStoreGuid("storeGuid");
        paymentInfoDTO2.setAppId("appId");
        paymentInfoDTO2.setAppSecret("appSecret");
        paymentInfoDTO2.setAccountName("accountName");
        paymentInfoDTO2.setIsDefaultAccount(0);
        paymentInfoDTO2.setDiversionRules("diversionRules");
        paymentInfoDTO2.setDiversionRule(Arrays.asList(0));
        final List<PaymentInfoDTO> paymentInfoDTOList = Arrays.asList(paymentInfoDTO2);
        verify(mockEnterpriseClientService).batchSaveOrUpdatePaymentInfoList(paymentInfoDTOList);

        // Confirm RedisService.putJHPayInfoList(...).
        final PaymentInfoDTO paymentInfoDTO3 = new PaymentInfoDTO();
        paymentInfoDTO3.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO3.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO3.setStoreGuid("storeGuid");
        paymentInfoDTO3.setAppId("appId");
        paymentInfoDTO3.setAppSecret("appSecret");
        paymentInfoDTO3.setAccountName("accountName");
        paymentInfoDTO3.setIsDefaultAccount(0);
        paymentInfoDTO3.setDiversionRules("diversionRules");
        paymentInfoDTO3.setDiversionRule(Arrays.asList(0));
        final List<PaymentInfoDTO> paymentInfoDTOList1 = Arrays.asList(paymentInfoDTO3);
        verify(mockRedisService).putJHPayInfoList(paymentInfoDTOList1, "storeGuid");
    }

    @Test
    public void testPaymentAccountSave_EnterpriseClientServiceListPaymentInfoReturnsNoItems() {
        // Setup
        final PaymentAccountReqDTO request = new PaymentAccountReqDTO();
        request.setPaymentTypeGuid("paymentTypeGuid");
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setStoreGuid("storeGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setAccountName("accountName");
        paymentInfoDTO.setIsDefaultAccount(0);
        paymentInfoDTO.setDiversionRules("diversionRules");
        paymentInfoDTO.setDiversionRule(Arrays.asList(0));
        request.setJhPayInfoList(Arrays.asList(paymentInfoDTO));
        request.setPaymentShunt(0);
        request.setStoreGuid("storeGuid");
        request.setToDeleteList(Arrays.asList("value"));

        // Configure JHPayClientService.check(...).
        final MchntValidateDTO mchntValidateDTO = new MchntValidateDTO();
        mchntValidateDTO.setAppId("appId");
        mchntValidateDTO.setAppSecretKey("appSecret");
        when(mockJhPayClientService.check(mchntValidateDTO)).thenReturn(false);

        // Configure EnterpriseClientService.existSameAccountName(...).
        final PaymentInfoDTO paymentInfoDTO1 = new PaymentInfoDTO();
        paymentInfoDTO1.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO1.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO1.setStoreGuid("storeGuid");
        paymentInfoDTO1.setAppId("appId");
        paymentInfoDTO1.setAppSecret("appSecret");
        paymentInfoDTO1.setAccountName("accountName");
        paymentInfoDTO1.setIsDefaultAccount(0);
        paymentInfoDTO1.setDiversionRules("diversionRules");
        paymentInfoDTO1.setDiversionRule(Arrays.asList(0));
        final List<PaymentInfoDTO> paymentInfoDTOS = Arrays.asList(paymentInfoDTO1);
        when(mockEnterpriseClientService.existSameAccountName("enterpriseGuid", "storeGuid", "accountName"))
                .thenReturn(paymentInfoDTOS);

        // Configure PaymentTypeMapper.selectOne(...).
        final PaymentTypeDO paymentTypeDO = PaymentTypeDO.builder()
                .paymentTypeGuid("parentPaymentTypeGuid")
                .paymentTypeName("name")
                .paymentType(0)
                .state(0)
                .storeGuid("storeGuid")
                .storeName("storeName")
                .sorting(0)
                .source(0)
                .paymentMode(0)
                .parentPaymentTypeGuid("parentPaymentTypeGuid")
                .paymentShunt(0)
                .build();
        when(mockPaymentTypeMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(paymentTypeDO);

        when(mockEnterpriseClientService.listPaymentInfo("enterpriseGuid", "storeGuid"))
                .thenReturn(Collections.emptyList());

        // Run the test
        final Boolean result = paymentTypeServiceImplUnderTest.paymentAccountSave(request);

        // Verify the results
        assertThat(result).isFalse();
        verify(mockEnterpriseClientService).deletePayInfoByGuidList(Arrays.asList("value"));

        // Confirm EnterpriseClientService.batchSaveOrUpdatePaymentInfoList(...).
        final PaymentInfoDTO paymentInfoDTO2 = new PaymentInfoDTO();
        paymentInfoDTO2.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO2.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO2.setStoreGuid("storeGuid");
        paymentInfoDTO2.setAppId("appId");
        paymentInfoDTO2.setAppSecret("appSecret");
        paymentInfoDTO2.setAccountName("accountName");
        paymentInfoDTO2.setIsDefaultAccount(0);
        paymentInfoDTO2.setDiversionRules("diversionRules");
        paymentInfoDTO2.setDiversionRule(Arrays.asList(0));
        final List<PaymentInfoDTO> paymentInfoDTOList = Arrays.asList(paymentInfoDTO2);
        verify(mockEnterpriseClientService).batchSaveOrUpdatePaymentInfoList(paymentInfoDTOList);

        // Confirm RedisService.putJHPayInfoList(...).
        final PaymentInfoDTO paymentInfoDTO3 = new PaymentInfoDTO();
        paymentInfoDTO3.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO3.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO3.setStoreGuid("storeGuid");
        paymentInfoDTO3.setAppId("appId");
        paymentInfoDTO3.setAppSecret("appSecret");
        paymentInfoDTO3.setAccountName("accountName");
        paymentInfoDTO3.setIsDefaultAccount(0);
        paymentInfoDTO3.setDiversionRules("diversionRules");
        paymentInfoDTO3.setDiversionRule(Arrays.asList(0));
        final List<PaymentInfoDTO> paymentInfoDTOList1 = Arrays.asList(paymentInfoDTO3);
        verify(mockRedisService).putJHPayInfoList(paymentInfoDTOList1, "storeGuid");
    }

    @Test
    public void testGetJhPaymentTypeInfo() {
        // Setup
        final PaymentTypeDTO expectedResult = new PaymentTypeDTO();
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setStoreName("storeName");
        expectedResult.setPaymentTypeGuid("paymentTypeGuid");
        expectedResult.setPaymentTypeName("name");
        expectedResult.setPaymentType(0);
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setStoreGuid("storeGuid");
        storeDTO.setStoreName("storeName");
        expectedResult.setStoreDTOS(Arrays.asList(storeDTO));
        expectedResult.setSorting(0);
        expectedResult.setAppId("appId");
        expectedResult.setAppSecretKey("appSecret");
        expectedResult.setSource(0);
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setStoreGuid("storeGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setAccountName("accountName");
        paymentInfoDTO.setIsDefaultAccount(0);
        paymentInfoDTO.setDiversionRules("diversionRules");
        paymentInfoDTO.setDiversionRule(Arrays.asList(0));
        expectedResult.setJhPayInfoList(Arrays.asList(paymentInfoDTO));

        // Configure PaymentTypeMapper.getPaymentTypeInfo(...).
        final PaymentTypeDTO paymentTypeDTO = new PaymentTypeDTO();
        paymentTypeDTO.setStoreGuid("storeGuid");
        paymentTypeDTO.setStoreName("storeName");
        paymentTypeDTO.setPaymentTypeGuid("paymentTypeGuid");
        paymentTypeDTO.setPaymentTypeName("name");
        paymentTypeDTO.setPaymentType(0);
        final StoreDTO storeDTO1 = new StoreDTO();
        storeDTO1.setStoreGuid("storeGuid");
        storeDTO1.setStoreName("storeName");
        paymentTypeDTO.setStoreDTOS(Arrays.asList(storeDTO1));
        paymentTypeDTO.setSorting(0);
        paymentTypeDTO.setAppId("appId");
        paymentTypeDTO.setAppSecretKey("appSecret");
        paymentTypeDTO.setSource(0);
        final PaymentInfoDTO paymentInfoDTO1 = new PaymentInfoDTO();
        paymentInfoDTO1.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO1.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO1.setStoreGuid("storeGuid");
        paymentInfoDTO1.setAppId("appId");
        paymentInfoDTO1.setAppSecret("appSecret");
        paymentInfoDTO1.setAccountName("accountName");
        paymentInfoDTO1.setIsDefaultAccount(0);
        paymentInfoDTO1.setDiversionRules("diversionRules");
        paymentInfoDTO1.setDiversionRule(Arrays.asList(0));
        paymentTypeDTO.setJhPayInfoList(Arrays.asList(paymentInfoDTO1));
        when(mockPaymentTypeMapper.getPaymentTypeInfo("storeGuid", 0)).thenReturn(paymentTypeDTO);

        // Configure RedisService.getJHPayInfoList(...).
        final PaymentInfoDTO paymentInfoDTO2 = new PaymentInfoDTO();
        paymentInfoDTO2.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO2.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO2.setStoreGuid("storeGuid");
        paymentInfoDTO2.setAppId("appId");
        paymentInfoDTO2.setAppSecret("appSecret");
        paymentInfoDTO2.setAccountName("accountName");
        paymentInfoDTO2.setIsDefaultAccount(0);
        paymentInfoDTO2.setDiversionRules("diversionRules");
        paymentInfoDTO2.setDiversionRule(Arrays.asList(0));
        final List<PaymentInfoDTO> paymentInfoDTOS = Arrays.asList(paymentInfoDTO2);
        when(mockRedisService.getJHPayInfoList("storeGuid")).thenReturn(paymentInfoDTOS);

        // Run the test
        final PaymentTypeDTO result = paymentTypeServiceImplUnderTest.getJhPaymentTypeInfo("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetJhPaymentTypeInfo_RedisServiceGetJHPayInfoListReturnsNoItems() {
        // Setup
        final PaymentTypeDTO expectedResult = new PaymentTypeDTO();
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setStoreName("storeName");
        expectedResult.setPaymentTypeGuid("paymentTypeGuid");
        expectedResult.setPaymentTypeName("name");
        expectedResult.setPaymentType(0);
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setStoreGuid("storeGuid");
        storeDTO.setStoreName("storeName");
        expectedResult.setStoreDTOS(Arrays.asList(storeDTO));
        expectedResult.setSorting(0);
        expectedResult.setAppId("appId");
        expectedResult.setAppSecretKey("appSecret");
        expectedResult.setSource(0);
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setStoreGuid("storeGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setAccountName("accountName");
        paymentInfoDTO.setIsDefaultAccount(0);
        paymentInfoDTO.setDiversionRules("diversionRules");
        paymentInfoDTO.setDiversionRule(Arrays.asList(0));
        expectedResult.setJhPayInfoList(Arrays.asList(paymentInfoDTO));

        // Configure PaymentTypeMapper.getPaymentTypeInfo(...).
        final PaymentTypeDTO paymentTypeDTO = new PaymentTypeDTO();
        paymentTypeDTO.setStoreGuid("storeGuid");
        paymentTypeDTO.setStoreName("storeName");
        paymentTypeDTO.setPaymentTypeGuid("paymentTypeGuid");
        paymentTypeDTO.setPaymentTypeName("name");
        paymentTypeDTO.setPaymentType(0);
        final StoreDTO storeDTO1 = new StoreDTO();
        storeDTO1.setStoreGuid("storeGuid");
        storeDTO1.setStoreName("storeName");
        paymentTypeDTO.setStoreDTOS(Arrays.asList(storeDTO1));
        paymentTypeDTO.setSorting(0);
        paymentTypeDTO.setAppId("appId");
        paymentTypeDTO.setAppSecretKey("appSecret");
        paymentTypeDTO.setSource(0);
        final PaymentInfoDTO paymentInfoDTO1 = new PaymentInfoDTO();
        paymentInfoDTO1.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO1.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO1.setStoreGuid("storeGuid");
        paymentInfoDTO1.setAppId("appId");
        paymentInfoDTO1.setAppSecret("appSecret");
        paymentInfoDTO1.setAccountName("accountName");
        paymentInfoDTO1.setIsDefaultAccount(0);
        paymentInfoDTO1.setDiversionRules("diversionRules");
        paymentInfoDTO1.setDiversionRule(Arrays.asList(0));
        paymentTypeDTO.setJhPayInfoList(Arrays.asList(paymentInfoDTO1));
        when(mockPaymentTypeMapper.getPaymentTypeInfo("storeGuid", 0)).thenReturn(paymentTypeDTO);

        when(mockRedisService.getJHPayInfoList("storeGuid")).thenReturn(Collections.emptyList());

        // Configure EnterpriseClientService.listPaymentInfo(...).
        final PaymentInfoDTO paymentInfoDTO2 = new PaymentInfoDTO();
        paymentInfoDTO2.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO2.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO2.setStoreGuid("storeGuid");
        paymentInfoDTO2.setAppId("appId");
        paymentInfoDTO2.setAppSecret("appSecret");
        paymentInfoDTO2.setAccountName("accountName");
        paymentInfoDTO2.setIsDefaultAccount(0);
        paymentInfoDTO2.setDiversionRules("diversionRules");
        paymentInfoDTO2.setDiversionRule(Arrays.asList(0));
        final List<PaymentInfoDTO> paymentInfoDTOS = Arrays.asList(paymentInfoDTO2);
        when(mockEnterpriseClientService.listPaymentInfo("enterpriseGuid", "storeGuid")).thenReturn(paymentInfoDTOS);

        // Run the test
        final PaymentTypeDTO result = paymentTypeServiceImplUnderTest.getJhPaymentTypeInfo("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetJhPaymentTypeInfo_EnterpriseClientServiceReturnsNoItems() {
        // Setup
        final PaymentTypeDTO expectedResult = new PaymentTypeDTO();
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setStoreName("storeName");
        expectedResult.setPaymentTypeGuid("paymentTypeGuid");
        expectedResult.setPaymentTypeName("name");
        expectedResult.setPaymentType(0);
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setStoreGuid("storeGuid");
        storeDTO.setStoreName("storeName");
        expectedResult.setStoreDTOS(Arrays.asList(storeDTO));
        expectedResult.setSorting(0);
        expectedResult.setAppId("appId");
        expectedResult.setAppSecretKey("appSecret");
        expectedResult.setSource(0);
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setStoreGuid("storeGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setAccountName("accountName");
        paymentInfoDTO.setIsDefaultAccount(0);
        paymentInfoDTO.setDiversionRules("diversionRules");
        paymentInfoDTO.setDiversionRule(Arrays.asList(0));
        expectedResult.setJhPayInfoList(Arrays.asList(paymentInfoDTO));

        // Configure PaymentTypeMapper.getPaymentTypeInfo(...).
        final PaymentTypeDTO paymentTypeDTO = new PaymentTypeDTO();
        paymentTypeDTO.setStoreGuid("storeGuid");
        paymentTypeDTO.setStoreName("storeName");
        paymentTypeDTO.setPaymentTypeGuid("paymentTypeGuid");
        paymentTypeDTO.setPaymentTypeName("name");
        paymentTypeDTO.setPaymentType(0);
        final StoreDTO storeDTO1 = new StoreDTO();
        storeDTO1.setStoreGuid("storeGuid");
        storeDTO1.setStoreName("storeName");
        paymentTypeDTO.setStoreDTOS(Arrays.asList(storeDTO1));
        paymentTypeDTO.setSorting(0);
        paymentTypeDTO.setAppId("appId");
        paymentTypeDTO.setAppSecretKey("appSecret");
        paymentTypeDTO.setSource(0);
        final PaymentInfoDTO paymentInfoDTO1 = new PaymentInfoDTO();
        paymentInfoDTO1.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO1.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO1.setStoreGuid("storeGuid");
        paymentInfoDTO1.setAppId("appId");
        paymentInfoDTO1.setAppSecret("appSecret");
        paymentInfoDTO1.setAccountName("accountName");
        paymentInfoDTO1.setIsDefaultAccount(0);
        paymentInfoDTO1.setDiversionRules("diversionRules");
        paymentInfoDTO1.setDiversionRule(Arrays.asList(0));
        paymentTypeDTO.setJhPayInfoList(Arrays.asList(paymentInfoDTO1));
        when(mockPaymentTypeMapper.getPaymentTypeInfo("storeGuid", 0)).thenReturn(paymentTypeDTO);

        when(mockRedisService.getJHPayInfoList("storeGuid")).thenReturn(Collections.emptyList());
        when(mockEnterpriseClientService.listPaymentInfo("enterpriseGuid", "storeGuid"))
                .thenReturn(Collections.emptyList());

        // Run the test
        final PaymentTypeDTO result = paymentTypeServiceImplUnderTest.getJhPaymentTypeInfo("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
