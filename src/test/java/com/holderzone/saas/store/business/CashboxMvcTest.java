package com.holderzone.saas.store.business;

import com.alibaba.fastjson.JSON;
import com.holderzone.saas.store.dto.business.manage.CashboxRecordCreateDTO;
import com.holderzone.saas.store.dto.business.manage.CashboxRecordListDTO;
import com.holderzone.saas.store.dto.business.manage.CashboxRecordQueryDTO;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.math.BigDecimal;

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className CashboxMvcTest
 * @date 18-9-5 上午10:38
 * @description 钱箱服务相关接口测试
 * @program holder-saas-store-business
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class CashboxMvcTest {
    @Autowired
    private WebApplicationContext webApplicationContext;

    private MockMvc mockMvc;

    private String storeGuid;

    @Before
    public void setup() {
        this.mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        storeGuid = "8b9ed76b-4c9c-443b-8d0e-8a2d2c234511";
    }

    @Test
    @Ignore
    public void testCreate() throws Exception {
        CashboxRecordCreateDTO dto = new CashboxRecordCreateDTO();
        dto.setStoreGuid(storeGuid);
        dto.setUserGuid("123456");
        dto.setUserName("system");
        dto.setMoney(new BigDecimal(100L));
        dto.setOperationType(0);
        dto.setRemark("测试钱箱存入");
        dto.setTerminalId("123456");
        String result = mockMvc.perform(MockMvcRequestBuilders.post("/cashboxRecord/create")
                                                              .accept(MediaType.APPLICATION_JSON_UTF8)
                                                              .contentType(MediaType.APPLICATION_JSON_UTF8)
                                                              .content(JSON.toJSONString(dto)))
                               .andExpect(status().isOk())
                               .andDo(MockMvcResultHandlers.print())
                               .andReturn()
                               .getResponse().getContentAsString();
    }

    @Test
//    @Ignore
    public void testQuery() throws Exception {
        CashboxRecordQueryDTO dto = new CashboxRecordQueryDTO();
        dto.setCashboxRecordGuid("9602b6b5-33f4-4896-9978-9b2a0fe96049");
        String result = mockMvc.perform(MockMvcRequestBuilders.post("/cashboxRecord/queryByAndroid")
                                                              .accept(MediaType.APPLICATION_JSON_UTF8)
                                                              .contentType(MediaType.APPLICATION_JSON_UTF8)
                                                              .content(JSON.toJSONString(dto)))
                               .andExpect(status().isOk())
                               .andDo(MockMvcResultHandlers.print())
                               .andReturn()
                               .getResponse().getContentAsString();
    }

    @Test
    @Ignore
    public void testQueryAll() throws Exception {
        CashboxRecordListDTO dto = new CashboxRecordListDTO();
        dto.setStoreGuid("8b9ed76b-4c9c-443b-8d0e-8a2d2c234511");
        String result = mockMvc.perform(MockMvcRequestBuilders.post("/cashboxRecord/list")
                                                              .accept(MediaType.APPLICATION_JSON_UTF8)
                                                              .contentType(MediaType.APPLICATION_JSON_UTF8)
                                                              .content(JSON.toJSONString(dto)))
                               .andExpect(status().isOk())
                               .andDo(MockMvcResultHandlers.print())
                               .andReturn()
                               .getResponse().getContentAsString();
    }
}
