package com.holderzone.saas.store.business.service.impl;

import com.holderzone.saas.store.business.entity.domain.StoreConfigDO;
import com.holderzone.saas.store.business.mapper.StoreConfigMapper;
import com.holderzone.saas.store.business.service.RedisService;
import com.holderzone.saas.store.business.service.client.OrgFeignClient;
import com.holderzone.saas.store.business.service.client.TradingClient;
import com.holderzone.saas.store.dto.business.manage.StoreConfigCreateDTO;
import com.holderzone.saas.store.dto.business.manage.StoreConfigDTO;
import com.holderzone.saas.store.dto.business.manage.StoreConfigQueryDTO;
import com.holderzone.saas.store.dto.business.manage.StoreConfigUpdateDTO;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class StoreConfigServiceImplTest {

    @Mock
    private StoreConfigMapper mockStoreConfigMapper;
    @Mock
    private TradingClient mockTradingClient;
    @Mock
    private OrgFeignClient mockOrgFeignClient;
    @Mock
    private RedisService redisService;

    private StoreConfigServiceImpl storeConfigServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        storeConfigServiceImplUnderTest = new StoreConfigServiceImpl(mockStoreConfigMapper, mockTradingClient,
                mockOrgFeignClient, redisService);
    }

    @Test
    public void testCreate() {
        // Setup
        final StoreConfigCreateDTO storeConfigCreateDTO = new StoreConfigCreateDTO();
        storeConfigCreateDTO.setStoreGuid("storeGuid");
        storeConfigCreateDTO.setStoreName("storeName");
        storeConfigCreateDTO.setEnablePadPwd(0);
        storeConfigCreateDTO.setEnableMarkDish(0);
        storeConfigCreateDTO.setEnableMemPrice(0);

        // Configure StoreConfigMapper.insert(...).
        final StoreConfigDO storeConfigDO = new StoreConfigDO();
        storeConfigDO.setStoreGuid("storeGuid");
        storeConfigDO.setStoreName("storeName");
        storeConfigDO.setEnablePadPwd(0);
        storeConfigDO.setBusinessStartTime(LocalTime.of(0, 0, 0));
        storeConfigDO.setBusinessEndTime(LocalTime.of(0, 0, 0));
        when(mockStoreConfigMapper.insert(storeConfigDO)).thenReturn(0);

        // Run the test
        storeConfigServiceImplUnderTest.create(storeConfigCreateDTO);

        // Verify the results
    }

    @Test
    public void testQuery() throws Exception {
        // Setup
        final StoreConfigQueryDTO storeConfigQueryDTO = new StoreConfigQueryDTO("storeGuid","123");
        final StoreConfigDTO expectedResult = new StoreConfigDTO();
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setStoreName("storeName");
        expectedResult.setEnablePadPwd(0);
        expectedResult.setEnableMarkDish(0);
        expectedResult.setEnableMemPrice(0);

        // Configure StoreConfigMapper.query(...).
        final StoreConfigDO storeConfigDO = new StoreConfigDO();
        storeConfigDO.setStoreGuid("storeGuid");
        storeConfigDO.setStoreName("storeName");
        storeConfigDO.setEnablePadPwd(0);
        storeConfigDO.setBusinessStartTime(LocalTime.of(0, 0, 0));
        storeConfigDO.setBusinessEndTime(LocalTime.of(0, 0, 0));
        final StoreConfigDO storeConfigDO1 = new StoreConfigDO();
        storeConfigDO1.setStoreGuid("storeGuid");
        storeConfigDO1.setStoreName("storeName");
        storeConfigDO1.setEnablePadPwd(0);
        storeConfigDO1.setBusinessStartTime(LocalTime.of(0, 0, 0));
        storeConfigDO1.setBusinessEndTime(LocalTime.of(0, 0, 0));
        when(mockStoreConfigMapper.query(storeConfigDO1)).thenReturn(storeConfigDO);

        // Run the test
        final StoreConfigDTO result = storeConfigServiceImplUnderTest.query(storeConfigQueryDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQuery_StoreConfigMapperReturnsNull() {
        // Setup
        final StoreConfigQueryDTO storeConfigQueryDTO = new StoreConfigQueryDTO("storeGuid","123");

        // Configure StoreConfigMapper.query(...).
        final StoreConfigDO storeConfigDO = new StoreConfigDO();
        storeConfigDO.setStoreGuid("storeGuid");
        storeConfigDO.setStoreName("storeName");
        storeConfigDO.setEnablePadPwd(0);
        storeConfigDO.setBusinessStartTime(LocalTime.of(0, 0, 0));
        storeConfigDO.setBusinessEndTime(LocalTime.of(0, 0, 0));
        when(mockStoreConfigMapper.query(storeConfigDO)).thenReturn(null);

        // Run the test
        final StoreConfigDTO result = storeConfigServiceImplUnderTest.query(storeConfigQueryDTO);

        // Verify the results
        assertThat(result).isNull();
    }

    @Test
    public void testUpdate() {
        // Setup
        final StoreConfigUpdateDTO storeConfigUpdateDTO = new StoreConfigUpdateDTO();
        storeConfigUpdateDTO.setStoreGuid("storeGuid");
        storeConfigUpdateDTO.setEnablePadPwd(0);
        storeConfigUpdateDTO.setEnableMarkDish(0);
        storeConfigUpdateDTO.setEnableMemPrice(0);
        storeConfigUpdateDTO.setEnableHandover(0);

        // Configure StoreConfigMapper.update(...).
        final StoreConfigDO storeConfigDO = new StoreConfigDO();
        storeConfigDO.setStoreGuid("storeGuid");
        storeConfigDO.setStoreName("storeName");
        storeConfigDO.setEnablePadPwd(0);
        storeConfigDO.setBusinessStartTime(LocalTime.of(0, 0, 0));
        storeConfigDO.setBusinessEndTime(LocalTime.of(0, 0, 0));
        when(mockStoreConfigMapper.update(storeConfigDO)).thenReturn(0L);

        // Run the test
        storeConfigServiceImplUnderTest.update(storeConfigUpdateDTO);

        // Verify the results
        verify(mockTradingClient).removeStoreAutoMark("storeGuid");
    }

    @Test
    public void testList() {
        // Setup
        final StoreConfigDTO storeConfigDTO = new StoreConfigDTO();
        storeConfigDTO.setStoreGuid("storeGuid");
        storeConfigDTO.setStoreName("storeName");
        storeConfigDTO.setEnablePadPwd(0);
        storeConfigDTO.setEnableMarkDish(0);
        storeConfigDTO.setEnableMemPrice(0);
        final List<StoreConfigDTO> expectedResult = Arrays.asList(storeConfigDTO);

        // Configure StoreConfigMapper.queryAll(...).
        final StoreConfigDO storeConfigDO = new StoreConfigDO();
        storeConfigDO.setStoreGuid("storeGuid");
        storeConfigDO.setStoreName("storeName");
        storeConfigDO.setEnablePadPwd(0);
        storeConfigDO.setBusinessStartTime(LocalTime.of(0, 0, 0));
        storeConfigDO.setBusinessEndTime(LocalTime.of(0, 0, 0));
        final List<StoreConfigDO> storeConfigDOS = Arrays.asList(storeConfigDO);
        when(mockStoreConfigMapper.queryAll()).thenReturn(storeConfigDOS);

        // Run the test
        final List<StoreConfigDTO> result = storeConfigServiceImplUnderTest.list();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testList_StoreConfigMapperReturnsNoItems() {
        // Setup
        when(mockStoreConfigMapper.queryAll()).thenReturn(Collections.emptyList());

        // Run the test
        final List<StoreConfigDTO> result = storeConfigServiceImplUnderTest.list();

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testQueryByIdList() {
        // Setup
        final StoreConfigDTO storeConfigDTO = new StoreConfigDTO();
        storeConfigDTO.setStoreGuid("storeGuid");
        storeConfigDTO.setStoreName("storeName");
        storeConfigDTO.setEnablePadPwd(0);
        storeConfigDTO.setEnableMarkDish(0);
        storeConfigDTO.setEnableMemPrice(0);
        final List<StoreConfigDTO> expectedResult = Arrays.asList(storeConfigDTO);

        // Configure StoreConfigMapper.queryList(...).
        final StoreConfigDO storeConfigDO = new StoreConfigDO();
        storeConfigDO.setStoreGuid("storeGuid");
        storeConfigDO.setStoreName("storeName");
        storeConfigDO.setEnablePadPwd(0);
        storeConfigDO.setBusinessStartTime(LocalTime.of(0, 0, 0));
        storeConfigDO.setBusinessEndTime(LocalTime.of(0, 0, 0));
        final List<StoreConfigDO> storeConfigDOS = Arrays.asList(storeConfigDO);
        when(mockStoreConfigMapper.queryList(Arrays.asList("value"))).thenReturn(storeConfigDOS);

        // Run the test
        final List<StoreConfigDTO> result = storeConfigServiceImplUnderTest.queryByIdList(Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryByIdList_StoreConfigMapperReturnsNull() {
        // Setup
        when(mockStoreConfigMapper.queryList(Arrays.asList("value"))).thenReturn(null);

        // Run the test
        final List<StoreConfigDTO> result = storeConfigServiceImplUnderTest.queryByIdList(Arrays.asList("value"));

        // Verify the results
        assertThat(result).isNull();
    }

    @Test
    public void testQueryByIdList_StoreConfigMapperReturnsNoItems() {
        // Setup
        when(mockStoreConfigMapper.queryList(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        final List<StoreConfigDTO> result = storeConfigServiceImplUnderTest.queryByIdList(Arrays.asList("value"));

        // Verify the results
        assertThat(result).isNull();
    }

    @Test
    public void testInit() {
        // Setup
        final BaseDTO baseDTO = new BaseDTO();
        baseDTO.setDeviceType(0);
        baseDTO.setDeviceId("deviceId");
        baseDTO.setEnterpriseGuid("enterpriseGuid");
        baseDTO.setEnterpriseName("enterpriseName");
        baseDTO.setStoreGuid("storeGuid");

        // Configure OrgFeignClient.queryStoreByGuid(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("2a5c365a-5c78-4c38-99ce-529cd8852ac1");
        storeDTO.setCode("code");
        storeDTO.setName("storeName");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        when(mockOrgFeignClient.queryStoreByGuid("storeGuid")).thenReturn(storeDTO);

        // Run the test
        storeConfigServiceImplUnderTest.init(baseDTO);

        // Verify the results
        // Confirm StoreConfigMapper.insert(...).
        final StoreConfigDO storeConfigDO = new StoreConfigDO();
        storeConfigDO.setStoreGuid("storeGuid");
        storeConfigDO.setStoreName("storeName");
        storeConfigDO.setEnablePadPwd(0);
        storeConfigDO.setBusinessStartTime(LocalTime.of(0, 0, 0));
        storeConfigDO.setBusinessEndTime(LocalTime.of(0, 0, 0));
        verify(mockStoreConfigMapper).insert(storeConfigDO);
    }

    @Test
    public void testUpdateFinishFood() {
        // Setup
        final StoreConfigUpdateDTO storeConfigUpdateDTO = new StoreConfigUpdateDTO();
        storeConfigUpdateDTO.setStoreGuid("storeGuid");
        storeConfigUpdateDTO.setEnablePadPwd(0);
        storeConfigUpdateDTO.setEnableMarkDish(0);
        storeConfigUpdateDTO.setEnableMemPrice(0);
        storeConfigUpdateDTO.setEnableHandover(0);

        // Configure StoreConfigMapper.update(...).
        final StoreConfigDO storeConfigDO = new StoreConfigDO();
        storeConfigDO.setStoreGuid("storeGuid");
        storeConfigDO.setStoreName("storeName");
        storeConfigDO.setEnablePadPwd(0);
        storeConfigDO.setBusinessStartTime(LocalTime.of(0, 0, 0));
        storeConfigDO.setBusinessEndTime(LocalTime.of(0, 0, 0));
        when(mockStoreConfigMapper.update(storeConfigDO)).thenReturn(0L);

        // Run the test
        storeConfigServiceImplUnderTest.updateFinishFood(storeConfigUpdateDTO);

        // Verify the results
    }
}
