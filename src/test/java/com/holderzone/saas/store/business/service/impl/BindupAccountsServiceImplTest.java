package com.holderzone.saas.store.business.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.saas.store.business.entity.domain.BindupAccountsDo;
import com.holderzone.saas.store.business.mapper.BindupAccountsMapper;
import com.holderzone.saas.store.business.service.client.*;
import com.holderzone.saas.store.dto.config.req.ConfigReqDTO;
import com.holderzone.saas.store.dto.config.resp.ConfigRespDTO;
import com.holderzone.saas.store.dto.message.BusinessMessageDTO;
import com.holderzone.saas.store.dto.organization.BusinessDateReqDTO;
import com.holderzone.saas.store.dto.organization.StoreBindUpAccountsDTO;
import com.holderzone.saas.store.dto.table.TableBasicQueryDTO;
import com.holderzone.saas.store.dto.table.TableOrderDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class BindupAccountsServiceImplTest {

    @Mock
    private BindupAccountsMapper mockBindupAccountsMapper;
    @Mock
    private OrgFeignClient mockOrgFeignClient;
    @Mock
    private MessageService mockMessageService;
    @Mock
    private ConfigFeignService mockConfigFeignService;
    @Mock
    private TradingClient mockTradingClient;
    @Mock
    private TableRpcService mockTableRpcService;

    @InjectMocks
    private BindupAccountsServiceImpl bindupAccountsServiceImplUnderTest;

    @Test
    public void testQueryBindUpAccountsList() {
        // Setup
        final BindupAccountsDo bindupAccountsDo = new BindupAccountsDo();
        bindupAccountsDo.setBindupAccounts(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        bindupAccountsDo.setStoreGuid("storeGuid");
        bindupAccountsDo.setUserGuid("userGuid");
        bindupAccountsDo.setUserName("userName");
        bindupAccountsDo.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<BindupAccountsDo> expectedResult = Arrays.asList(bindupAccountsDo);

        // Configure BindupAccountsMapper.selectList(...).
        final BindupAccountsDo bindupAccountsDo1 = new BindupAccountsDo();
        bindupAccountsDo1.setBindupAccounts(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        bindupAccountsDo1.setStoreGuid("storeGuid");
        bindupAccountsDo1.setUserGuid("userGuid");
        bindupAccountsDo1.setUserName("userName");
        bindupAccountsDo1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<BindupAccountsDo> bindupAccountsDos = Arrays.asList(bindupAccountsDo1);
        when(mockBindupAccountsMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(bindupAccountsDos);

        // Run the test
        final List<BindupAccountsDo> result = bindupAccountsServiceImplUnderTest.queryBindUpAccountsList("storeGuid",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryBindUpAccountsList_BindupAccountsMapperReturnsNoItems() {
        // Setup
        when(mockBindupAccountsMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<BindupAccountsDo> result = bindupAccountsServiceImplUnderTest.queryBindUpAccountsList("storeGuid",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testQueryBindUpAccountsLast() {
        // Setup
        final BindupAccountsDo expectedResult = new BindupAccountsDo();
        expectedResult.setBindupAccounts(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setUserGuid("userGuid");
        expectedResult.setUserName("userName");
        expectedResult.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Configure BindupAccountsMapper.queryBindUpAccountsLast(...).
        final BindupAccountsDo bindupAccountsDo = new BindupAccountsDo();
        bindupAccountsDo.setBindupAccounts(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        bindupAccountsDo.setStoreGuid("storeGuid");
        bindupAccountsDo.setUserGuid("userGuid");
        bindupAccountsDo.setUserName("userName");
        bindupAccountsDo.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockBindupAccountsMapper.queryBindUpAccountsLast("storeGuid")).thenReturn(bindupAccountsDo);

        // Run the test
        final BindupAccountsDo result = bindupAccountsServiceImplUnderTest.queryBindUpAccountsLast("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryBindUpAccountsLast_BindupAccountsMapperReturnsNull() {
        // Setup
        when(mockBindupAccountsMapper.queryBindUpAccountsLast("storeGuid")).thenReturn(null);

        // Run the test
        final BindupAccountsDo result = bindupAccountsServiceImplUnderTest.queryBindUpAccountsLast("storeGuid");

        // Verify the results
        assertThat(result).isNull();
    }

    @Test
    public void testSaveBindUpAccounts() {
        // Setup
        final BindupAccountsDo expectedResult = new BindupAccountsDo();
        expectedResult.setBindupAccounts(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setUserGuid("userGuid");
        expectedResult.setUserName("userName");
        expectedResult.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Configure OrgFeignClient.queryBusinessDay(...).
        final BusinessDateReqDTO businessDateReqDTO = new BusinessDateReqDTO();
        businessDateReqDTO.setQueryDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        businessDateReqDTO.setStoreGuidList(Arrays.asList("value"));
        businessDateReqDTO.setStoreGuid("storeGuid");
        when(mockOrgFeignClient.queryBusinessDay(businessDateReqDTO)).thenReturn(LocalDate.of(2020, 1, 1));

        // Run the test
        final BindupAccountsDo result = bindupAccountsServiceImplUnderTest.saveBindUpAccounts("storeGuid", "userGuid",
                "userName", LocalDate.of(2020, 1, 1));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);

        // Confirm OrgFeignClient.batchUpdateBuAccounts(...).
        final StoreBindUpAccountsDTO st = new StoreBindUpAccountsDTO();
        st.setStoreList(Arrays.asList("value"));
        st.setIsBuAccounts(0);
        st.setIsShowCash(0);
        st.setCanOpenTable(0);
        st.setIsMultiHandover(0);
        verify(mockOrgFeignClient).batchUpdateBuAccounts(st);

        // Confirm MessageService.msg(...).
        final BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setSubject("name");
        businessMessageDTO.setContent("0");
        businessMessageDTO.setMessageType(0);
        businessMessageDTO.setDetailMessageType(0);
        businessMessageDTO.setPlatform("2");
        businessMessageDTO.setStoreGuid("storeGuid");
        verify(mockMessageService).msg(businessMessageDTO);
    }

    @Test
    public void testSendMqForCanOpenTable() {
        // Setup
        // Run the test
        bindupAccountsServiceImplUnderTest.sendMqForCanOpenTable("storeGuid");

        // Verify the results
        // Confirm MessageService.msg(...).
        final BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setSubject("name");
        businessMessageDTO.setContent("0");
        businessMessageDTO.setMessageType(0);
        businessMessageDTO.setDetailMessageType(0);
        businessMessageDTO.setPlatform("2");
        businessMessageDTO.setStoreGuid("storeGuid");
        verify(mockMessageService).msg(businessMessageDTO);
    }

    @Test
    public void testSendMqBindupAccounts() {
        // Setup
        // Configure ConfigFeignService.getConfig(...).
        final ConfigRespDTO configRespDTO = new ConfigRespDTO();
        configRespDTO.setGuid("298f0937-9d60-4370-8849-38e93e346bab");
        configRespDTO.setEnterpriseGuid("enterpriseGuid");
        configRespDTO.setStoreGuid("storeGuid");
        configRespDTO.setDicCode(0);
        configRespDTO.setDicName("dicName");
        final List<ConfigRespDTO> configRespDTOS = Arrays.asList(configRespDTO);
        final ConfigReqDTO request = new ConfigReqDTO();
        request.setGuid("63eb3f3c-0599-438f-8708-79bfc67f8d1f");
        request.setEnterpriseGuid("enterpriseGuid");
        request.setDicCode(0);
        request.setDictValue("dictValue");
        request.setIsEnable(0);
        when(mockConfigFeignService.getConfig(request)).thenReturn(configRespDTOS);

        when(mockTradingClient.queryOrderNumForStoreGuid("storeGuid")).thenReturn(0);

        // Configure BindupAccountsMapper.queryBindUpAccountsLast(...).
        final BindupAccountsDo bindupAccountsDo = new BindupAccountsDo();
        bindupAccountsDo.setBindupAccounts(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        bindupAccountsDo.setStoreGuid("storeGuid");
        bindupAccountsDo.setUserGuid("userGuid");
        bindupAccountsDo.setUserName("userName");
        bindupAccountsDo.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockBindupAccountsMapper.queryBindUpAccountsLast("storeGuid")).thenReturn(bindupAccountsDo);

        // Configure OrgFeignClient.queryBusinessDay(...).
        final BusinessDateReqDTO businessDateReqDTO = new BusinessDateReqDTO();
        businessDateReqDTO.setQueryDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        businessDateReqDTO.setStoreGuidList(Arrays.asList("value"));
        businessDateReqDTO.setStoreGuid("storeGuid");
        when(mockOrgFeignClient.queryBusinessDay(businessDateReqDTO)).thenReturn(LocalDate.of(2020, 1, 1));

        // Configure TableRpcService.listByAndroid(...).
        final TableOrderDTO tableOrderDTO = new TableOrderDTO();
        tableOrderDTO.setTableGuid("tableGuid");
        tableOrderDTO.setAreaGuid("areaGuid");
        tableOrderDTO.setOrderAmount(new BigDecimal("0.00"));
        tableOrderDTO.setTableCode("tableCode");
        tableOrderDTO.setStatus(0);
        final List<TableOrderDTO> tableOrderDTOS = Arrays.asList(tableOrderDTO);
        final TableBasicQueryDTO tableBasicQueryDTO = new TableBasicQueryDTO();
        tableBasicQueryDTO.setStoreGuid("storeGuid");
        tableBasicQueryDTO.setAreaGuid("areaGuid");
        tableBasicQueryDTO.setTableGuidList(Arrays.asList("value"));
        when(mockTableRpcService.listByAndroid(tableBasicQueryDTO)).thenReturn(tableOrderDTOS);

        // Run the test
        bindupAccountsServiceImplUnderTest.sendMqBindupAccounts();

        // Verify the results
        // Confirm MessageService.msg(...).
        final BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setSubject("name");
        businessMessageDTO.setContent("0");
        businessMessageDTO.setMessageType(0);
        businessMessageDTO.setDetailMessageType(0);
        businessMessageDTO.setPlatform("2");
        businessMessageDTO.setStoreGuid("storeGuid");
        verify(mockMessageService).msg(businessMessageDTO);

        // Confirm OrgFeignClient.batchUpdateBuAccounts(...).
        final StoreBindUpAccountsDTO st = new StoreBindUpAccountsDTO();
        st.setStoreList(Arrays.asList("value"));
        st.setIsBuAccounts(0);
        st.setIsShowCash(0);
        st.setCanOpenTable(0);
        st.setIsMultiHandover(0);
        verify(mockOrgFeignClient).batchUpdateBuAccounts(st);
    }

    @Test
    public void testSendMqBindupAccounts_ConfigFeignServiceReturnsNoItems() {
        // Setup
        // Configure ConfigFeignService.getConfig(...).
        final ConfigReqDTO request = new ConfigReqDTO();
        request.setGuid("63eb3f3c-0599-438f-8708-79bfc67f8d1f");
        request.setEnterpriseGuid("enterpriseGuid");
        request.setDicCode(0);
        request.setDictValue("dictValue");
        request.setIsEnable(0);
        when(mockConfigFeignService.getConfig(request)).thenReturn(Collections.emptyList());

        // Run the test
        bindupAccountsServiceImplUnderTest.sendMqBindupAccounts();

        // Verify the results
    }

    @Test
    public void testSendMqBindupAccounts_BindupAccountsMapperReturnsNull() {
        // Setup
        // Configure ConfigFeignService.getConfig(...).
        final ConfigRespDTO configRespDTO = new ConfigRespDTO();
        configRespDTO.setGuid("298f0937-9d60-4370-8849-38e93e346bab");
        configRespDTO.setEnterpriseGuid("enterpriseGuid");
        configRespDTO.setStoreGuid("storeGuid");
        configRespDTO.setDicCode(0);
        configRespDTO.setDicName("dicName");
        final List<ConfigRespDTO> configRespDTOS = Arrays.asList(configRespDTO);
        final ConfigReqDTO request = new ConfigReqDTO();
        request.setGuid("63eb3f3c-0599-438f-8708-79bfc67f8d1f");
        request.setEnterpriseGuid("enterpriseGuid");
        request.setDicCode(0);
        request.setDictValue("dictValue");
        request.setIsEnable(0);
        when(mockConfigFeignService.getConfig(request)).thenReturn(configRespDTOS);

        when(mockTradingClient.queryOrderNumForStoreGuid("storeGuid")).thenReturn(0);
        when(mockBindupAccountsMapper.queryBindUpAccountsLast("storeGuid")).thenReturn(null);

        // Configure OrgFeignClient.queryBusinessDay(...).
        final BusinessDateReqDTO businessDateReqDTO = new BusinessDateReqDTO();
        businessDateReqDTO.setQueryDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        businessDateReqDTO.setStoreGuidList(Arrays.asList("value"));
        businessDateReqDTO.setStoreGuid("storeGuid");
        when(mockOrgFeignClient.queryBusinessDay(businessDateReqDTO)).thenReturn(LocalDate.of(2020, 1, 1));

        // Run the test
        bindupAccountsServiceImplUnderTest.sendMqBindupAccounts();

        // Verify the results
        // Confirm MessageService.msg(...).
        final BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setSubject("name");
        businessMessageDTO.setContent("0");
        businessMessageDTO.setMessageType(0);
        businessMessageDTO.setDetailMessageType(0);
        businessMessageDTO.setPlatform("2");
        businessMessageDTO.setStoreGuid("storeGuid");
        verify(mockMessageService).msg(businessMessageDTO);

        // Confirm OrgFeignClient.batchUpdateBuAccounts(...).
        final StoreBindUpAccountsDTO st = new StoreBindUpAccountsDTO();
        st.setStoreList(Arrays.asList("value"));
        st.setIsBuAccounts(0);
        st.setIsShowCash(0);
        st.setCanOpenTable(0);
        st.setIsMultiHandover(0);
        verify(mockOrgFeignClient).batchUpdateBuAccounts(st);
    }

    @Test
    public void testSendMqBindupAccounts_TableRpcServiceReturnsNoItems() {
        // Setup
        // Configure ConfigFeignService.getConfig(...).
        final ConfigRespDTO configRespDTO = new ConfigRespDTO();
        configRespDTO.setGuid("298f0937-9d60-4370-8849-38e93e346bab");
        configRespDTO.setEnterpriseGuid("enterpriseGuid");
        configRespDTO.setStoreGuid("storeGuid");
        configRespDTO.setDicCode(0);
        configRespDTO.setDicName("dicName");
        final List<ConfigRespDTO> configRespDTOS = Arrays.asList(configRespDTO);
        final ConfigReqDTO request = new ConfigReqDTO();
        request.setGuid("63eb3f3c-0599-438f-8708-79bfc67f8d1f");
        request.setEnterpriseGuid("enterpriseGuid");
        request.setDicCode(0);
        request.setDictValue("dictValue");
        request.setIsEnable(0);
        when(mockConfigFeignService.getConfig(request)).thenReturn(configRespDTOS);

        when(mockTradingClient.queryOrderNumForStoreGuid("storeGuid")).thenReturn(0);

        // Configure BindupAccountsMapper.queryBindUpAccountsLast(...).
        final BindupAccountsDo bindupAccountsDo = new BindupAccountsDo();
        bindupAccountsDo.setBindupAccounts(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        bindupAccountsDo.setStoreGuid("storeGuid");
        bindupAccountsDo.setUserGuid("userGuid");
        bindupAccountsDo.setUserName("userName");
        bindupAccountsDo.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockBindupAccountsMapper.queryBindUpAccountsLast("storeGuid")).thenReturn(bindupAccountsDo);

        // Configure OrgFeignClient.queryBusinessDay(...).
        final BusinessDateReqDTO businessDateReqDTO = new BusinessDateReqDTO();
        businessDateReqDTO.setQueryDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        businessDateReqDTO.setStoreGuidList(Arrays.asList("value"));
        businessDateReqDTO.setStoreGuid("storeGuid");
        when(mockOrgFeignClient.queryBusinessDay(businessDateReqDTO)).thenReturn(LocalDate.of(2020, 1, 1));

        // Configure TableRpcService.listByAndroid(...).
        final TableBasicQueryDTO tableBasicQueryDTO = new TableBasicQueryDTO();
        tableBasicQueryDTO.setStoreGuid("storeGuid");
        tableBasicQueryDTO.setAreaGuid("areaGuid");
        tableBasicQueryDTO.setTableGuidList(Arrays.asList("value"));
        when(mockTableRpcService.listByAndroid(tableBasicQueryDTO)).thenReturn(Collections.emptyList());

        // Run the test
        bindupAccountsServiceImplUnderTest.sendMqBindupAccounts();

        // Verify the results
        // Confirm OrgFeignClient.batchUpdateBuAccounts(...).
        final StoreBindUpAccountsDTO st = new StoreBindUpAccountsDTO();
        st.setStoreList(Arrays.asList("value"));
        st.setIsBuAccounts(0);
        st.setIsShowCash(0);
        st.setCanOpenTable(0);
        st.setIsMultiHandover(0);
        verify(mockOrgFeignClient).batchUpdateBuAccounts(st);
    }

    @Test
    public void testSendMessageBindUpAccounts() {
        // Setup
        // Configure OrgFeignClient.queryBusinessDay(...).
        final BusinessDateReqDTO businessDateReqDTO = new BusinessDateReqDTO();
        businessDateReqDTO.setQueryDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        businessDateReqDTO.setStoreGuidList(Arrays.asList("value"));
        businessDateReqDTO.setStoreGuid("storeGuid");
        when(mockOrgFeignClient.queryBusinessDay(businessDateReqDTO)).thenReturn(LocalDate.of(2020, 1, 1));

        // Run the test
        bindupAccountsServiceImplUnderTest.sendMessageBindUpAccounts("storeGuid");

        // Verify the results
        // Confirm MessageService.msg(...).
        final BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setSubject("name");
        businessMessageDTO.setContent("0");
        businessMessageDTO.setMessageType(0);
        businessMessageDTO.setDetailMessageType(0);
        businessMessageDTO.setPlatform("2");
        businessMessageDTO.setStoreGuid("storeGuid");
        verify(mockMessageService).msg(businessMessageDTO);
    }

    @Test
    public void testSendMessageBindUpAccountsStatus() {
        // Setup
        // Run the test
        bindupAccountsServiceImplUnderTest.sendMessageBindUpAccountsStatus("storeGuid", "enterpriseGuid");

        // Verify the results
        // Confirm MessageService.msg(...).
        final BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setSubject("name");
        businessMessageDTO.setContent("0");
        businessMessageDTO.setMessageType(0);
        businessMessageDTO.setDetailMessageType(0);
        businessMessageDTO.setPlatform("2");
        businessMessageDTO.setStoreGuid("storeGuid");
        verify(mockMessageService).msg(businessMessageDTO);
    }

    @Test
    public void testCurrentTimeDay() {
        // Setup
        // Configure OrgFeignClient.queryBusinessDay(...).
        final BusinessDateReqDTO businessDateReqDTO = new BusinessDateReqDTO();
        businessDateReqDTO.setQueryDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        businessDateReqDTO.setStoreGuidList(Arrays.asList("value"));
        businessDateReqDTO.setStoreGuid("storeGuid");
        when(mockOrgFeignClient.queryBusinessDay(businessDateReqDTO)).thenReturn(LocalDate.of(2020, 1, 1));

        // Run the test
        final LocalDate result = bindupAccountsServiceImplUnderTest.currentTimeDay("storeGuid",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Verify the results
        assertThat(result).isEqualTo(LocalDate.of(2020, 1, 1));
    }
}
