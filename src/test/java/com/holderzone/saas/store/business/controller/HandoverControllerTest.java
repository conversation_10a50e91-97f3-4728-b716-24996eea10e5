package com.holderzone.saas.store.business.controller;

import com.alibaba.fastjson.JSON;
import com.holderzone.saas.store.business.HolderSaasStoreManageApplication;
import com.holderzone.saas.store.business.exception.FileIllegalException;
import com.holderzone.saas.store.business.util.JsonFileUtil;
import com.holderzone.saas.store.dto.exception.TestException;
import com.holderzone.saas.store.dto.trade.req.RecordThirdActivityInfoReqDTO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.io.UnsupportedEncodingException;

import static com.holderzone.saas.store.dto.common.CommonConstant.USER_INFO;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@Slf4j
@WebAppConfiguration
@ContextConfiguration
@AutoConfigureMockMvc
@RunWith(SpringRunner.class)
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
@SpringBootTest(classes = HolderSaasStoreManageApplication.class)
public class HandoverControllerTest {

    private static final String USERINFO = "{\"operSubjectGuid\": \"2010121440477930009\",\"enterpriseGuid\":" +
            " \"2009281531195930006\",\"enterpriseName\": \"赵氏企业\",\"enterpriseNo\": \"********\",\"storeGuid\":" +
            " \"2106221850429620006\",\"storeName\": \"交子大道测试门店\",\"storeNo\": \"5796807\",\"userGuid\": " +
            "\"6787561298847596545\",\"account\": \"196504\",\"tel\": \"***********\",\"name\": \"靓亮仔\"}\n";

    private static final String HANDOVER = "/handover";

    private static final String RESPONSE = "response:";

    @Autowired
    private WebApplicationContext wac;

    private MockMvc mockMvc;

    @Before
    public void setupMockMvc() {
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();
    }

    @Test
    public void create() throws UnsupportedEncodingException {
        RecordThirdActivityInfoReqDTO reqCreateDTO = JSON.parseObject(JsonFileUtil.read("handover/create.json"),
                RecordThirdActivityInfoReqDTO.class);
        String jsonCreateString = JSON.toJSONString(reqCreateDTO);
        MvcResult mvcCreateResult = null;
        try {
            mvcCreateResult = mockMvc.perform(post(HANDOVER + "/create")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(jsonCreateString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new TestException(e.getMessage());
        }
        String contentAsString = mvcCreateResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    @Test
    public void confirm() throws UnsupportedEncodingException {
        RecordThirdActivityInfoReqDTO reqConfirmDTO = JSON.parseObject(JsonFileUtil.read("handover/confirm.json"),
                RecordThirdActivityInfoReqDTO.class);
        String jsonConfirmString = JSON.toJSONString(reqConfirmDTO);
        MvcResult mvcConfirmResult = null;
        try {
            mvcConfirmResult = mockMvc.perform(post(HANDOVER + "/confirm")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(jsonConfirmString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new TestException(e.getMessage());
        }
        String contentAsString = mvcConfirmResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    @Test
    public void confirmNew() throws UnsupportedEncodingException {
        RecordThirdActivityInfoReqDTO reqCconfirmNewDTO = JSON.parseObject(JsonFileUtil.read("handover/confirmNew.json"),
                RecordThirdActivityInfoReqDTO.class);
        String jsonCconfirmNewString = JSON.toJSONString(reqCconfirmNewDTO);
        MvcResult mvcCconfirmNewResult = null;
        try {
            mvcCconfirmNewResult = mockMvc.perform(post(HANDOVER + "/confirmNew")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(jsonCconfirmNewString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new TestException(e.getMessage());
        }
        String contentAsString = mvcCconfirmNewResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    @Test
    public void settle() throws UnsupportedEncodingException {
        RecordThirdActivityInfoReqDTO reqSettleDTO = JSON.parseObject(JsonFileUtil.read("handover/settle.json"),
                RecordThirdActivityInfoReqDTO.class);
        String jsonSettleString = JSON.toJSONString(reqSettleDTO);
        MvcResult mvcSettleResult = null;
        try {
            mvcSettleResult = mockMvc.perform(post(HANDOVER + "/settle")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(jsonSettleString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new TestException(e.getMessage());
        }
        String contentAsString = mvcSettleResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    @Test
    public void settleNew() throws UnsupportedEncodingException {
        RecordThirdActivityInfoReqDTO reqDTO = JSON.parseObject(JsonFileUtil.read("handover/settleNew.json"),
                RecordThirdActivityInfoReqDTO.class);
        String jsonString = JSON.toJSONString(reqDTO);
        MvcResult mvcResult = null;
        try {
            mvcResult = mockMvc.perform(post(HANDOVER + "/settleNew")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(jsonString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new FileIllegalException(e.getMessage());
        }
        String contentAsString = mvcResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    @Test
    public void query() throws UnsupportedEncodingException {
        RecordThirdActivityInfoReqDTO reqQueryDTO = JSON.parseObject(JsonFileUtil.read("handover/query.json"),
                RecordThirdActivityInfoReqDTO.class);
        String jsonQueryString = JSON.toJSONString(reqQueryDTO);
        MvcResult mvcQueryResult = null;
        try {
            mvcQueryResult = mockMvc.perform(post(HANDOVER + "/query")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(jsonQueryString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new TestException(e.getMessage());
        }
        String contentAsString = mvcQueryResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    @Test
    public void queryNew() throws UnsupportedEncodingException {
        RecordThirdActivityInfoReqDTO reqQueryNewDTO = JSON.parseObject(JsonFileUtil.read("handover/queryNew.json"),
                RecordThirdActivityInfoReqDTO.class);
        String jsonQueryNewString = JSON.toJSONString(reqQueryNewDTO);
        MvcResult mvcQueryNewResult = null;
        try {
            mvcQueryNewResult = mockMvc.perform(post(HANDOVER + "/queryNew")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(jsonQueryNewString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new TestException(e.getMessage());
        }
        String contentAsString = mvcQueryNewResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    @Test
    public void queryByPage() throws UnsupportedEncodingException {
        RecordThirdActivityInfoReqDTO reqQueryByPageDTO = JSON.parseObject(JsonFileUtil.read("handover/queryByPage.json"),
                RecordThirdActivityInfoReqDTO.class);
        String jsonQueryByPageString = JSON.toJSONString(reqQueryByPageDTO);
        MvcResult mvcQueryByPageResult = null;
        try {
            mvcQueryByPageResult = mockMvc.perform(post(HANDOVER + "/queryByPage")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(jsonQueryByPageString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new TestException(e.getMessage());
        }
        String contentAsString = mvcQueryByPageResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

}