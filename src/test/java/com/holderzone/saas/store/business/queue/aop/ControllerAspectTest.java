package com.holderzone.saas.store.business.queue.aop;

import org.aspectj.lang.JoinPoint;
import org.junit.Before;
import org.junit.Test;

public class ControllerAspectTest {

    private ControllerAspect controllerAspectUnderTest;

    @Before
    public void setUp() {
        controllerAspectUnderTest = new ControllerAspect();
    }

    @Test
    public void testPointCut() {
        controllerAspectUnderTest.pointCut();
    }

    @Test
    public void testDoBefore() {
        // Setup
        final JoinPoint joinPoint = null;

        // Run the test
        controllerAspectUnderTest.doBefore(joinPoint);

        // Verify the results
    }
}
