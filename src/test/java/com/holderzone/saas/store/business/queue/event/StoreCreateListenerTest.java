package com.holderzone.saas.store.business.queue.event;

import com.holderzone.saas.store.business.queue.service.QueueConfigService;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.queue.StoreConfigDTO;
import org.apache.rocketmq.common.message.MessageExt;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.net.InetSocketAddress;

import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.verify;

@RunWith(MockitoJUnitRunner.class)
public class StoreCreateListenerTest {

    @Mock
    private QueueConfigService mockQueueConfigService;

    private StoreCreateListener storeCreateListenerUnderTest;

    @Before
    public void setUp() throws Exception {
        storeCreateListenerUnderTest = new StoreCreateListener(mockQueueConfigService);
    }

    @Test
    public void testConsumeMsg() {
        // Setup
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("eede9424-472d-4366-b5fe-14e77f8b47f6");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setParentIds("parentIds");

        final MessageExt messageExt = new MessageExt();
        messageExt.setQueueId(0);
        messageExt.setBornTimestamp(0L);
        messageExt.setBornHost(new InetSocketAddress("localhost", 80));
        messageExt.setStoreTimestamp(0L);
        messageExt.setStoreHost(new InetSocketAddress("localhost", 80));

        // Run the test
        final boolean result = storeCreateListenerUnderTest.consumeMsg(storeDTO, messageExt);

        // Verify the results
        assertTrue(result);

        // Confirm QueueConfigService.config(...).
        final StoreConfigDTO dto = new StoreConfigDTO();
        dto.setGuid("24cc4382-dc66-4e16-87e5-04e0db96be23");
        dto.setStoreGuid("storeGuid");
        dto.setIsEnableEat(false);
        dto.setIsEnableRecovery(false);
        dto.setRecoveryNum("recoveryNum");
        verify(mockQueueConfigService).config(dto);
    }
}
