package com.holderzone.saas.store.business.queue.config;

import org.junit.Before;
import org.junit.Test;
import springfox.documentation.spring.web.plugins.Docket;

public class SwaggerConfigTest {

    private SwaggerConfig swaggerConfigUnderTest;

    @Before
    public void setUp() throws Exception {
        swaggerConfigUnderTest = new SwaggerConfig();
    }

    @Test
    public void testTestApi() {
        // Setup
        // Run the test
        final Docket result = swaggerConfigUnderTest.testApi();

        // Verify the results
    }
}
