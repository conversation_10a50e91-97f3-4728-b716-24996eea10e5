package com.holderzone.saas.store.business.service.impl;

import com.holderzone.saas.store.business.entity.domain.SurchargeAreaDO;
import com.holderzone.saas.store.business.mapstruct.SurchargeMapstruct;
import com.holderzone.saas.store.business.service.RedisService;
import com.holderzone.saas.store.business.service.client.TableRpcService;
import com.holderzone.saas.store.dto.business.manage.SurchargeAreaDTO;
import com.holderzone.saas.store.dto.business.manage.SurchargeCreateDTO;
import com.holderzone.saas.store.dto.business.manage.SurchargeUpdateDTO;
import com.holderzone.saas.store.dto.table.AreaDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class SurchargeAreaServiceImplTest {

    @Mock
    private RedisService mockRedisService;
    @Mock
    private TableRpcService mockTableRpcService;
    @Mock
    private SurchargeMapstruct mockSurchargeMapstruct;

    private SurchargeAreaServiceImpl surchargeAreaServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        surchargeAreaServiceImplUnderTest = new SurchargeAreaServiceImpl(mockRedisService, mockTableRpcService,
                mockSurchargeMapstruct);
    }

    @Test
    public void testBindSurchargeArea() {
        // Setup
        final SurchargeCreateDTO surchargeCreateDTO = new SurchargeCreateDTO();
        surchargeCreateDTO.setSurchargeGuid("surchargeGuid");
        surchargeCreateDTO.setStoreGuid("storeGuid");
        surchargeCreateDTO.setName("name");
        surchargeCreateDTO.setAmount(new BigDecimal("0.00"));
        surchargeCreateDTO.setAreaGuidList(Arrays.asList("value"));

        when(mockRedisService.nextBatchSurchargeAreaGuid(0L)).thenReturn(Arrays.asList("value"));

        // Run the test
        surchargeAreaServiceImplUnderTest.bindSurchargeArea(surchargeCreateDTO);

        // Verify the results
    }

    @Test
    public void testBindSurchargeArea_RedisServiceReturnsNoItems() {
        // Setup
        final SurchargeCreateDTO surchargeCreateDTO = new SurchargeCreateDTO();
        surchargeCreateDTO.setSurchargeGuid("surchargeGuid");
        surchargeCreateDTO.setStoreGuid("storeGuid");
        surchargeCreateDTO.setName("name");
        surchargeCreateDTO.setAmount(new BigDecimal("0.00"));
        surchargeCreateDTO.setAreaGuidList(Arrays.asList("value"));

        when(mockRedisService.nextBatchSurchargeAreaGuid(0L)).thenReturn(Collections.emptyList());

        // Run the test
        surchargeAreaServiceImplUnderTest.bindSurchargeArea(surchargeCreateDTO);

        // Verify the results
    }

    @Test
    public void testFindSurchargeArea() {
        // Setup
        final List<SurchargeAreaDTO> expectedResult = Arrays.asList(
                new SurchargeAreaDTO("5092f4bc-c13d-46a7-a5a1-0590136990f2", "areaName", false));

        // Configure TableRpcService.queryArea(...).
        final AreaDTO areaDTO = new AreaDTO();
        areaDTO.setGuid("88660a59-cf8e-4da7-ab7e-80846dfa6201");
        areaDTO.setStoreGuid("storeGuid");
        areaDTO.setStoreName("storeName");
        areaDTO.setAreaName("areaName");
        areaDTO.setSort(0);
        final List<AreaDTO> areaDTOS = Arrays.asList(areaDTO);
        when(mockTableRpcService.queryArea("storeGuid")).thenReturn(areaDTOS);

        // Run the test
        final List<SurchargeAreaDTO> result = surchargeAreaServiceImplUnderTest.findSurchargeArea("surchargeGuid",
                "storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testFindSurchargeArea_TableRpcServiceReturnsNoItems() {
        // Setup
        when(mockTableRpcService.queryArea("storeGuid")).thenReturn(Collections.emptyList());

        // Run the test
        final List<SurchargeAreaDTO> result = surchargeAreaServiceImplUnderTest.findSurchargeArea("surchargeGuid",
                "storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testFindAreaSurcharge1() {
        // Setup
        final SurchargeAreaDO surchargeAreaDO = new SurchargeAreaDO();
        surchargeAreaDO.setId(0L);
        surchargeAreaDO.setGuid("7d849831-e5b4-451a-9145-3240539acfaa");
        surchargeAreaDO.setSurchargeGuid("surchargeGuid");
        surchargeAreaDO.setAreaGuid("areaGuid");
        surchargeAreaDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<SurchargeAreaDO> expectedResult = Arrays.asList(surchargeAreaDO);

        // Run the test
        final List<SurchargeAreaDO> result = surchargeAreaServiceImplUnderTest.findAreaSurcharge(
                Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testFindAreaSurcharge2() {
        // Setup
        final SurchargeAreaDO surchargeAreaDO = new SurchargeAreaDO();
        surchargeAreaDO.setId(0L);
        surchargeAreaDO.setGuid("7d849831-e5b4-451a-9145-3240539acfaa");
        surchargeAreaDO.setSurchargeGuid("surchargeGuid");
        surchargeAreaDO.setAreaGuid("areaGuid");
        surchargeAreaDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<SurchargeAreaDO> expectedResult = Arrays.asList(surchargeAreaDO);

        // Run the test
        final List<SurchargeAreaDO> result = surchargeAreaServiceImplUnderTest.findAreaSurcharge(Arrays.asList("value"),
                "areaGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testFindAreaSurchargeBySurchargeGuids() {
        // Setup
        final SurchargeAreaDO surchargeAreaDO = new SurchargeAreaDO();
        surchargeAreaDO.setId(0L);
        surchargeAreaDO.setGuid("7d849831-e5b4-451a-9145-3240539acfaa");
        surchargeAreaDO.setSurchargeGuid("surchargeGuid");
        surchargeAreaDO.setAreaGuid("areaGuid");
        surchargeAreaDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<SurchargeAreaDO> expectedResult = Arrays.asList(surchargeAreaDO);

        // Run the test
        final List<SurchargeAreaDO> result = surchargeAreaServiceImplUnderTest.findAreaSurchargeBySurchargeGuids(
                Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testRebindSurchargeArea() {
        // Setup
        final SurchargeUpdateDTO surchargeUpdateDTO = new SurchargeUpdateDTO();
        surchargeUpdateDTO.setSurchargeGuid("surchargeGuid");
        surchargeUpdateDTO.setStoreGuid("storeGuid");
        surchargeUpdateDTO.setName("name");
        surchargeUpdateDTO.setAmount(new BigDecimal("0.00"));
        surchargeUpdateDTO.setAreaGuidList(Arrays.asList("value"));

        when(mockRedisService.nextBatchSurchargeAreaGuid(0L)).thenReturn(Arrays.asList("value"));

        // Run the test
        surchargeAreaServiceImplUnderTest.rebindSurchargeArea(surchargeUpdateDTO);

        // Verify the results
    }

    @Test
    public void testRebindSurchargeArea_RedisServiceReturnsNoItems() {
        // Setup
        final SurchargeUpdateDTO surchargeUpdateDTO = new SurchargeUpdateDTO();
        surchargeUpdateDTO.setSurchargeGuid("surchargeGuid");
        surchargeUpdateDTO.setStoreGuid("storeGuid");
        surchargeUpdateDTO.setName("name");
        surchargeUpdateDTO.setAmount(new BigDecimal("0.00"));
        surchargeUpdateDTO.setAreaGuidList(Arrays.asList("value"));

        when(mockRedisService.nextBatchSurchargeAreaGuid(0L)).thenReturn(Collections.emptyList());

        // Run the test
        surchargeAreaServiceImplUnderTest.rebindSurchargeArea(surchargeUpdateDTO);

        // Verify the results
    }

    @Test
    public void testRemoveSurchargeArea1() {
        // Setup
        // Run the test
        surchargeAreaServiceImplUnderTest.removeSurchargeArea("surchargeGuid");

        // Verify the results
    }

    @Test
    public void testRemoveSurchargeArea2() {
        // Setup
        // Run the test
        surchargeAreaServiceImplUnderTest.removeSurchargeArea(Arrays.asList("value"));

        // Verify the results
    }
}
