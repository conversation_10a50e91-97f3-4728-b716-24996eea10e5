package com.holderzone.saas.store.business.service.impl;

import com.holderzone.saas.store.business.entity.domain.MemberOperateRecordDO;
import com.holderzone.saas.store.business.mapper.MemberOperateRecordMapper;
import com.holderzone.saas.store.business.service.RedisService;
import com.holderzone.saas.store.dto.trade.req.record.MemberOperateRecordReqDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class MemberOperateRecordServiceImplTest {

    @Mock
    private MemberOperateRecordMapper mockMemberOperateRecordMapper;
    @Mock
    private RedisService mockRedisService;

    private MemberOperateRecordServiceImpl memberOperateRecordServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        memberOperateRecordServiceImplUnderTest = new MemberOperateRecordServiceImpl(mockMemberOperateRecordMapper,
                mockRedisService);
    }

    @Test
    public void testSaveRecord() {
        // Setup
        final MemberOperateRecordReqDTO reqDTO = new MemberOperateRecordReqDTO();
        reqDTO.setDeviceType(0);
        reqDTO.setLoginType(0);
        reqDTO.setModuleType(0);
        reqDTO.setTradeMode(0);
        reqDTO.setOperatorGuid("operatorGuid");
        reqDTO.setPhoneNum("cardNum");
        reqDTO.setOperatorName("operatorName");
        reqDTO.setOrderGuid("orderGuid");
        reqDTO.setStoreGuid("storeGuid");
        reqDTO.setStoreName("storeName");

        when(mockRedisService.nextMemberOperateRecordGuid()).thenReturn("result");

        // Run the test
        memberOperateRecordServiceImplUnderTest.saveRecord(reqDTO);

        // Verify the results
        // Confirm MemberOperateRecordMapper.insert(...).
        final MemberOperateRecordDO entity = new MemberOperateRecordDO();
        entity.setGuid("27dce979-cfc8-4ba2-a745-24f471a452d7");
        entity.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setIsDelete(0);
        entity.setDeviceType(0);
        entity.setModuleType(0);
        entity.setLoginType(0);
        entity.setTradeMode(0);
        entity.setOperateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setOperatorGuid("operatorGuid");
        entity.setOperatorName("operatorName");
        entity.setPhoneNum("cardNum");
        entity.setCardNum("cardNum");
        entity.setMemberOpenid("cardNum");
        entity.setOrderGuid("orderGuid");
        entity.setStoreGuid("storeGuid");
        entity.setStoreName("storeName");
        verify(mockMemberOperateRecordMapper).insert(entity);
    }
}
