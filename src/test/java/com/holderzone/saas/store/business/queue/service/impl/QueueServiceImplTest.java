package com.holderzone.saas.store.business.queue.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.saas.store.business.queue.domain.HolderQueueConfigDO;
import com.holderzone.saas.store.business.queue.domain.HolderQueueDO;
import com.holderzone.saas.store.business.queue.domain.HolderQueueItemDO;
import com.holderzone.saas.store.business.queue.domain.HolderQueueTableDO;
import com.holderzone.saas.store.business.queue.mapper.QueueConfigMapper;
import com.holderzone.saas.store.business.queue.mapper.QueueItemMapper;
import com.holderzone.saas.store.business.queue.mapper.QueueTableMapper;
import com.holderzone.saas.store.business.queue.mapstruct.QueueItemMapStruct;
import com.holderzone.saas.store.business.queue.mapstruct.QueueMapStruct;
import com.holderzone.saas.store.business.queue.service.QueueConfigService;
import com.holderzone.saas.store.business.queue.service.QueueItemService;
import com.holderzone.saas.store.business.queue.service.remote.BusinessMsgClient;
import com.holderzone.saas.store.business.queue.service.remote.OrganizationClientService;
import com.holderzone.saas.store.business.queue.service.remote.TableClientService;
import com.holderzone.saas.store.dto.message.BusinessMessageDTO;
import com.holderzone.saas.store.dto.queue.*;
import com.holderzone.saas.store.dto.table.TableBasicDTO;
import com.holderzone.saas.store.dto.table.TableBasicQueryDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.transaction.TransactionException;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

import java.time.LocalDateTime;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class QueueServiceImplTest {

    @Mock
    private QueueMapStruct mockQueueMapStruct;
    @Mock
    private QueueItemMapStruct mockQueueItemMapStruct;
    @Mock
    private QueueItemMapper mockQueueItemMapper;
    @Mock
    private QueueItemService mockQueueItemService;
    @Mock
    private TransactionTemplate mockTransactionTemplate;
    @Mock
    private QueueConfigService mockQueueConfigService;
    @Mock
    private QueueTableMapper mockQueueTableMapper;
    @Mock
    private BusinessMsgClient mockBusinessMsgClient;
    @Mock
    private QueueConfigMapper mockConfigMapper;
    @Mock
    private QueueTableServiceImpl mockTableService;
    @Mock
    private TableClientService mockTableClientService;
    @Mock
    private OrganizationClientService mockOrganizationClientService;

    private QueueServiceImpl queueServiceImplUnderTest;

    @Before
    public void setUp() {
        queueServiceImplUnderTest = new QueueServiceImpl(mockQueueMapStruct, mockQueueItemMapStruct,
                mockQueueItemMapper, mockQueueItemService);
        ReflectionTestUtils.setField(queueServiceImplUnderTest, "queueConfigService", mockQueueConfigService);
        ReflectionTestUtils.setField(queueServiceImplUnderTest, "queueTableMapper", mockQueueTableMapper);
        ReflectionTestUtils.setField(queueServiceImplUnderTest, "businessMsgClient", mockBusinessMsgClient);
        ReflectionTestUtils.setField(queueServiceImplUnderTest, "configMapper", mockConfigMapper);
        ReflectionTestUtils.setField(queueServiceImplUnderTest, "tableService", mockTableService);
        ReflectionTestUtils.setField(queueServiceImplUnderTest, "tableClientService", mockTableClientService);
        ReflectionTestUtils.setField(queueServiceImplUnderTest, "organizationClientService",
                mockOrganizationClientService);
        queueServiceImplUnderTest.transactionTemplate = mockTransactionTemplate;
    }

    @Test
    public void testDoinTransaction() {
        // Setup
        when(mockTransactionTemplate.execute(any(TransactionCallback.class))).thenReturn("result");

        // Run the test
        final String result = queueServiceImplUnderTest.doinTransaction(() -> "value");

        // Verify the results
        assertThat(result).isEqualTo("result");
    }

    @Test
    public void testDoinTransaction_TransactionTemplateReturnsNull() {
        // Setup
        when(mockTransactionTemplate.execute(any(TransactionCallback.class))).thenReturn(null);

        // Run the test
        final String result = queueServiceImplUnderTest.doinTransaction(() -> "value");

        // Verify the results
        assertThat(result).isNull();
    }

    @Test
    public void testDoinTransaction_TransactionTemplateThrowsTransactionException() {
        // Setup
        when(mockTransactionTemplate.execute(any(TransactionCallback.class))).thenThrow(TransactionException.class);

        // Run the test
        assertThatThrownBy(() -> queueServiceImplUnderTest.doinTransaction(() -> "value"))
                .isInstanceOf(TransactionException.class);
    }

    @Test
    public void testSave() {
        // Setup
        final HolderQueueDTO dto = new HolderQueueDTO();
        dto.setGuid("ca8ba3ce-7933-477d-aa89-2c7fc969bb50");
        dto.setStoreGuid("storeGuid");
        dto.setName("name");
        dto.setCode("code");
        dto.setMin((byte) 0b0);
        dto.setMax((byte) 0b0);
        dto.setSize(0);

        when(mockTransactionTemplate.execute(any(TransactionCallback.class))).thenReturn("result");

        // Configure QueueMapStruct.toDo(...).
        final HolderQueueDO holderQueueDO = new HolderQueueDO();
        holderQueueDO.setGuid("ca8ba3ce-7933-477d-aa89-2c7fc969bb50");
        holderQueueDO.setStoreGuid("storeGuid");
        holderQueueDO.setName("name");
        holderQueueDO.setCode("code");
        holderQueueDO.setMin((byte) 0b0);
        holderQueueDO.setMax((byte) 0b0);
        holderQueueDO.setIsEnable(false);
        holderQueueDO.setIsDeleted(false);
        holderQueueDO.setCreateStaffGuid("createStaffGuid");
        holderQueueDO.setModifiedStaffGuid("createStaffGuid");
        holderQueueDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        holderQueueDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HolderQueueDTO dto1 = new HolderQueueDTO();
        dto1.setGuid("ca8ba3ce-7933-477d-aa89-2c7fc969bb50");
        dto1.setStoreGuid("storeGuid");
        dto1.setName("name");
        dto1.setCode("code");
        dto1.setMin((byte) 0b0);
        dto1.setMax((byte) 0b0);
        dto1.setSize(0);
        when(mockQueueMapStruct.toDo(dto1)).thenReturn(holderQueueDO);

        // Configure BusinessMsgClient.sendMsg(...).
        final BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setMessageGuid("messageGuid");
        businessMessageDTO.setSubject("subject");
        businessMessageDTO.setContent("content");
        businessMessageDTO.setMessageType(0);
        businessMessageDTO.setDetailMessageType(0);
        when(mockBusinessMsgClient.sendMsg(businessMessageDTO)).thenReturn("result");

        // Run the test
        final String result = queueServiceImplUnderTest.save(dto);

        // Verify the results
        assertThat(result).isEqualTo("ca8ba3ce-7933-477d-aa89-2c7fc969bb50");
    }

    @Test
    public void testSave_TransactionTemplateReturnsNull() {
        // Setup
        final HolderQueueDTO dto = new HolderQueueDTO();
        dto.setGuid("ca8ba3ce-7933-477d-aa89-2c7fc969bb50");
        dto.setStoreGuid("storeGuid");
        dto.setName("name");
        dto.setCode("code");
        dto.setMin((byte) 0b0);
        dto.setMax((byte) 0b0);
        dto.setSize(0);

        when(mockTransactionTemplate.execute(any(TransactionCallback.class))).thenReturn(null);

        // Configure QueueMapStruct.toDo(...).
        final HolderQueueDO holderQueueDO = new HolderQueueDO();
        holderQueueDO.setGuid("ca8ba3ce-7933-477d-aa89-2c7fc969bb50");
        holderQueueDO.setStoreGuid("storeGuid");
        holderQueueDO.setName("name");
        holderQueueDO.setCode("code");
        holderQueueDO.setMin((byte) 0b0);
        holderQueueDO.setMax((byte) 0b0);
        holderQueueDO.setIsEnable(false);
        holderQueueDO.setIsDeleted(false);
        holderQueueDO.setCreateStaffGuid("createStaffGuid");
        holderQueueDO.setModifiedStaffGuid("createStaffGuid");
        holderQueueDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        holderQueueDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HolderQueueDTO dto1 = new HolderQueueDTO();
        dto1.setGuid("ca8ba3ce-7933-477d-aa89-2c7fc969bb50");
        dto1.setStoreGuid("storeGuid");
        dto1.setName("name");
        dto1.setCode("code");
        dto1.setMin((byte) 0b0);
        dto1.setMax((byte) 0b0);
        dto1.setSize(0);
        when(mockQueueMapStruct.toDo(dto1)).thenReturn(holderQueueDO);

        // Configure BusinessMsgClient.sendMsg(...).
        final BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setMessageGuid("messageGuid");
        businessMessageDTO.setSubject("subject");
        businessMessageDTO.setContent("content");
        businessMessageDTO.setMessageType(0);
        businessMessageDTO.setDetailMessageType(0);
        when(mockBusinessMsgClient.sendMsg(businessMessageDTO)).thenReturn("result");

        // Run the test
        final String result = queueServiceImplUnderTest.save(dto);

        // Verify the results
        assertThat(result).isEqualTo("ca8ba3ce-7933-477d-aa89-2c7fc969bb50");
    }

    @Test
    public void testSave_TransactionTemplateThrowsTransactionException() {
        // Setup
        final HolderQueueDTO dto = new HolderQueueDTO();
        dto.setGuid("ca8ba3ce-7933-477d-aa89-2c7fc969bb50");
        dto.setStoreGuid("storeGuid");
        dto.setName("name");
        dto.setCode("code");
        dto.setMin((byte) 0b0);
        dto.setMax((byte) 0b0);
        dto.setSize(0);

        when(mockTransactionTemplate.execute(any(TransactionCallback.class))).thenThrow(TransactionException.class);

        // Run the test
        assertThatThrownBy(() -> queueServiceImplUnderTest.save(dto)).isInstanceOf(Exception.class);
    }

    @Test
    public void testEnable1() {
        assertThat(queueServiceImplUnderTest.enable("e1952a40-762d-438c-b34a-3f0fc9739d35")).isFalse();
    }

    @Test
    public void testDisable() {
        assertThat(queueServiceImplUnderTest.disable("80351057-3dcc-47ac-8cc4-fb9891195f3d")).isFalse();
    }

    @Test
    public void testDelete() {
        // Setup
        when(mockTransactionTemplate.execute(any(TransactionCallback.class))).thenReturn("result");
        when(mockQueueItemMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(0);

        // Configure BusinessMsgClient.sendMsg(...).
        final BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setMessageGuid("messageGuid");
        businessMessageDTO.setSubject("subject");
        businessMessageDTO.setContent("content");
        businessMessageDTO.setMessageType(0);
        businessMessageDTO.setDetailMessageType(0);
        when(mockBusinessMsgClient.sendMsg(businessMessageDTO)).thenReturn("result");

        // Run the test
        final Boolean result = queueServiceImplUnderTest.delete("e03a58a6-5dca-438c-b8f5-54d8d326730d");

        // Verify the results
        assertThat(result).isTrue();

        // Confirm QueueItemService.clean(...).
        final HolderQueueDO holderQueueDO = new HolderQueueDO();
        holderQueueDO.setGuid("ca8ba3ce-7933-477d-aa89-2c7fc969bb50");
        holderQueueDO.setStoreGuid("storeGuid");
        holderQueueDO.setName("name");
        holderQueueDO.setCode("code");
        holderQueueDO.setMin((byte) 0b0);
        holderQueueDO.setMax((byte) 0b0);
        holderQueueDO.setIsEnable(false);
        holderQueueDO.setIsDeleted(false);
        holderQueueDO.setCreateStaffGuid("createStaffGuid");
        holderQueueDO.setModifiedStaffGuid("createStaffGuid");
        holderQueueDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        holderQueueDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HolderQueueDO> list = Arrays.asList(holderQueueDO);
        verify(mockQueueItemService).clean(list);
        verify(mockQueueTableMapper).delete(any(LambdaQueryWrapper.class));
    }

    @Test
    public void testDelete_TransactionTemplateReturnsNull() {
        // Setup
        when(mockTransactionTemplate.execute(any(TransactionCallback.class))).thenReturn(null);
        when(mockQueueItemMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(0);

        // Configure BusinessMsgClient.sendMsg(...).
        final BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setMessageGuid("messageGuid");
        businessMessageDTO.setSubject("subject");
        businessMessageDTO.setContent("content");
        businessMessageDTO.setMessageType(0);
        businessMessageDTO.setDetailMessageType(0);
        when(mockBusinessMsgClient.sendMsg(businessMessageDTO)).thenReturn("result");

        // Run the test
        final Boolean result = queueServiceImplUnderTest.delete("e03a58a6-5dca-438c-b8f5-54d8d326730d");

        // Verify the results
        assertThat(result).isTrue();

        // Confirm QueueItemService.clean(...).
        final HolderQueueDO holderQueueDO = new HolderQueueDO();
        holderQueueDO.setGuid("ca8ba3ce-7933-477d-aa89-2c7fc969bb50");
        holderQueueDO.setStoreGuid("storeGuid");
        holderQueueDO.setName("name");
        holderQueueDO.setCode("code");
        holderQueueDO.setMin((byte) 0b0);
        holderQueueDO.setMax((byte) 0b0);
        holderQueueDO.setIsEnable(false);
        holderQueueDO.setIsDeleted(false);
        holderQueueDO.setCreateStaffGuid("createStaffGuid");
        holderQueueDO.setModifiedStaffGuid("createStaffGuid");
        holderQueueDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        holderQueueDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HolderQueueDO> list = Arrays.asList(holderQueueDO);
        verify(mockQueueItemService).clean(list);
        verify(mockQueueTableMapper).delete(any(LambdaQueryWrapper.class));
    }

    @Test
    public void testDelete_TransactionTemplateThrowsTransactionException() {
        // Setup
        when(mockTransactionTemplate.execute(any(TransactionCallback.class))).thenThrow(TransactionException.class);

        // Run the test
        assertThatThrownBy(() -> queueServiceImplUnderTest.delete("e03a58a6-5dca-438c-b8f5-54d8d326730d"))
                .isInstanceOf(TransactionException.class);
    }

    @Test
    public void testFetchOne() {
        // Setup
        final HolderQueueDTO expectedResult = new HolderQueueDTO();
        expectedResult.setGuid("ca8ba3ce-7933-477d-aa89-2c7fc969bb50");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setName("name");
        expectedResult.setCode("code");
        expectedResult.setMin((byte) 0b0);
        expectedResult.setMax((byte) 0b0);
        expectedResult.setSize(0);

        // Configure QueueMapStruct.toDto(...).
        final HolderQueueDTO holderQueueDTO = new HolderQueueDTO();
        holderQueueDTO.setGuid("ca8ba3ce-7933-477d-aa89-2c7fc969bb50");
        holderQueueDTO.setStoreGuid("storeGuid");
        holderQueueDTO.setName("name");
        holderQueueDTO.setCode("code");
        holderQueueDTO.setMin((byte) 0b0);
        holderQueueDTO.setMax((byte) 0b0);
        holderQueueDTO.setSize(0);
        final HolderQueueDO d = new HolderQueueDO();
        d.setGuid("ca8ba3ce-7933-477d-aa89-2c7fc969bb50");
        d.setStoreGuid("storeGuid");
        d.setName("name");
        d.setCode("code");
        d.setMin((byte) 0b0);
        d.setMax((byte) 0b0);
        d.setIsEnable(false);
        d.setIsDeleted(false);
        d.setCreateStaffGuid("createStaffGuid");
        d.setModifiedStaffGuid("createStaffGuid");
        d.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockQueueMapStruct.toDto(d)).thenReturn(holderQueueDTO);

        // Run the test
        final HolderQueueDTO result = queueServiceImplUnderTest.fetchOne("queueGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testRemove() {
        // Setup
        // Run the test
        final Boolean result = queueServiceImplUnderTest.remove(Arrays.asList("value"));

        // Verify the results
        assertThat(result).isTrue();
        verify(mockQueueItemMapper).delete(any(LambdaQueryWrapper.class));
    }

    @Test
    public void testRemoveAll() {
        // Setup
        // Configure QueueConfigService.obtain(...).
        final StoreConfigDTO storeConfigDTO = new StoreConfigDTO();
        storeConfigDTO.setGuid("882afd62-1451-4f00-bc6a-c147e0cb09a9");
        storeConfigDTO.setStoreGuid("storeGuid");
        storeConfigDTO.setIsEnableEat(false);
        storeConfigDTO.setIsEnableRecovery(false);
        storeConfigDTO.setIsEnableManualReset(false);
        when(mockQueueConfigService.obtain("storeGuid")).thenReturn(storeConfigDTO);

        // Configure BusinessMsgClient.sendMsg(...).
        final BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setMessageGuid("messageGuid");
        businessMessageDTO.setSubject("subject");
        businessMessageDTO.setContent("content");
        businessMessageDTO.setMessageType(0);
        businessMessageDTO.setDetailMessageType(0);
        when(mockBusinessMsgClient.sendMsg(businessMessageDTO)).thenReturn("result");

        // Run the test
        final Boolean result = queueServiceImplUnderTest.removeAll();

        // Verify the results
        assertThat(result).isTrue();
        verify(mockQueueItemMapper).delete(any(LambdaQueryWrapper.class));

        // Confirm QueueItemService.clean(...).
        final HolderQueueDO holderQueueDO = new HolderQueueDO();
        holderQueueDO.setGuid("ca8ba3ce-7933-477d-aa89-2c7fc969bb50");
        holderQueueDO.setStoreGuid("storeGuid");
        holderQueueDO.setName("name");
        holderQueueDO.setCode("code");
        holderQueueDO.setMin((byte) 0b0);
        holderQueueDO.setMax((byte) 0b0);
        holderQueueDO.setIsEnable(false);
        holderQueueDO.setIsDeleted(false);
        holderQueueDO.setCreateStaffGuid("createStaffGuid");
        holderQueueDO.setModifiedStaffGuid("createStaffGuid");
        holderQueueDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        holderQueueDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HolderQueueDO> list = Arrays.asList(holderQueueDO);
        verify(mockQueueItemService).clean(list);
    }

    @Test
    public void testRemoveAll_QueueConfigServiceReturnsNull() {
        // Setup
        when(mockQueueConfigService.obtain("storeGuid")).thenReturn(null);

        // Configure BusinessMsgClient.sendMsg(...).
        final BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setMessageGuid("messageGuid");
        businessMessageDTO.setSubject("subject");
        businessMessageDTO.setContent("content");
        businessMessageDTO.setMessageType(0);
        businessMessageDTO.setDetailMessageType(0);
        when(mockBusinessMsgClient.sendMsg(businessMessageDTO)).thenReturn("result");

        // Run the test
        final Boolean result = queueServiceImplUnderTest.removeAll();

        // Verify the results
        assertThat(result).isTrue();
        verify(mockQueueItemMapper).delete(any(LambdaQueryWrapper.class));

        // Confirm QueueItemService.clean(...).
        final HolderQueueDO holderQueueDO = new HolderQueueDO();
        holderQueueDO.setGuid("ca8ba3ce-7933-477d-aa89-2c7fc969bb50");
        holderQueueDO.setStoreGuid("storeGuid");
        holderQueueDO.setName("name");
        holderQueueDO.setCode("code");
        holderQueueDO.setMin((byte) 0b0);
        holderQueueDO.setMax((byte) 0b0);
        holderQueueDO.setIsEnable(false);
        holderQueueDO.setIsDeleted(false);
        holderQueueDO.setCreateStaffGuid("createStaffGuid");
        holderQueueDO.setModifiedStaffGuid("createStaffGuid");
        holderQueueDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        holderQueueDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HolderQueueDO> list = Arrays.asList(holderQueueDO);
        verify(mockQueueItemService).clean(list);
    }

    @Test
    public void testClean() {
        // Setup
        // Configure BusinessMsgClient.sendMsg(...).
        final BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setMessageGuid("messageGuid");
        businessMessageDTO.setSubject("subject");
        businessMessageDTO.setContent("content");
        businessMessageDTO.setMessageType(0);
        businessMessageDTO.setDetailMessageType(0);
        when(mockBusinessMsgClient.sendMsg(businessMessageDTO)).thenReturn("result");

        // Run the test
        queueServiceImplUnderTest.clean("storeGuid");

        // Verify the results
        verify(mockQueueItemMapper).delete(any(LambdaQueryWrapper.class));

        // Confirm QueueItemService.clean(...).
        final HolderQueueDO holderQueueDO = new HolderQueueDO();
        holderQueueDO.setGuid("ca8ba3ce-7933-477d-aa89-2c7fc969bb50");
        holderQueueDO.setStoreGuid("storeGuid");
        holderQueueDO.setName("name");
        holderQueueDO.setCode("code");
        holderQueueDO.setMin((byte) 0b0);
        holderQueueDO.setMax((byte) 0b0);
        holderQueueDO.setIsEnable(false);
        holderQueueDO.setIsDeleted(false);
        holderQueueDO.setCreateStaffGuid("createStaffGuid");
        holderQueueDO.setModifiedStaffGuid("createStaffGuid");
        holderQueueDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        holderQueueDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HolderQueueDO> list = Arrays.asList(holderQueueDO);
        verify(mockQueueItemService).clean(list);
    }

    @Test
    public void testAvailableCode() {
        // Setup
        // Run the test
        final List<String> result = queueServiceImplUnderTest.availableCode();

        // Verify the results
        assertThat(result).isEqualTo(Arrays.asList("value"));
    }

    @Test
    public void testSendMessage() {
        // Setup
        // Configure BusinessMsgClient.sendMsg(...).
        final BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setMessageGuid("messageGuid");
        businessMessageDTO.setSubject("subject");
        businessMessageDTO.setContent("content");
        businessMessageDTO.setMessageType(0);
        businessMessageDTO.setDetailMessageType(0);
        when(mockBusinessMsgClient.sendMsg(businessMessageDTO)).thenReturn("result");

        // Run the test
        queueServiceImplUnderTest.sendMessage("storeGuid", "storeName");

        // Verify the results
    }

    @Test
    public void testAll() {
        // Setup
        final HolderQueueDetailDTO holderQueueDetailDTO = new HolderQueueDetailDTO();
        holderQueueDetailDTO.setGuid("ca8ba3ce-7933-477d-aa89-2c7fc969bb50");
        holderQueueDetailDTO.setStoreGuid("storeGuid");
        holderQueueDetailDTO.setName("name");
        holderQueueDetailDTO.setCode("code");
        holderQueueDetailDTO.setMin((byte) 0b0);
        holderQueueDetailDTO.setMax((byte) 0b0);
        holderQueueDetailDTO.setSize(0);
        final HolderQueueItemDetailDTO holderQueueItemDetailDTO = new HolderQueueItemDetailDTO();
        holderQueueItemDetailDTO.setQueueGuid("queueGuid");
        holderQueueItemDetailDTO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        holderQueueDetailDTO.setItems(Arrays.asList(holderQueueItemDetailDTO));
        final StoreQueueDTO expectedResult = new StoreQueueDTO(Arrays.asList(holderQueueDetailDTO), false, false,
                false);

        // Configure QueueConfigMapper.selectOne(...).
        final HolderQueueConfigDO holderQueueConfigDO = new HolderQueueConfigDO();
        holderQueueConfigDO.setId(0L);
        holderQueueConfigDO.setStoreGuid("storeGuid");
        holderQueueConfigDO.setIsEnableEat(false);
        holderQueueConfigDO.setIsEnableRecovery(false);
        holderQueueConfigDO.setIsEnableManualReset(false);
        when(mockConfigMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(holderQueueConfigDO);

        when(mockTransactionTemplate.execute(any(TransactionCallback.class))).thenReturn("result");

        // Configure QueueMapStruct.toDo(...).
        final HolderQueueDO holderQueueDO = new HolderQueueDO();
        holderQueueDO.setGuid("ca8ba3ce-7933-477d-aa89-2c7fc969bb50");
        holderQueueDO.setStoreGuid("storeGuid");
        holderQueueDO.setName("name");
        holderQueueDO.setCode("code");
        holderQueueDO.setMin((byte) 0b0);
        holderQueueDO.setMax((byte) 0b0);
        holderQueueDO.setIsEnable(false);
        holderQueueDO.setIsDeleted(false);
        holderQueueDO.setCreateStaffGuid("createStaffGuid");
        holderQueueDO.setModifiedStaffGuid("createStaffGuid");
        holderQueueDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        holderQueueDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HolderQueueDTO dto = new HolderQueueDTO();
        dto.setGuid("ca8ba3ce-7933-477d-aa89-2c7fc969bb50");
        dto.setStoreGuid("storeGuid");
        dto.setName("name");
        dto.setCode("code");
        dto.setMin((byte) 0b0);
        dto.setMax((byte) 0b0);
        dto.setSize(0);
        when(mockQueueMapStruct.toDo(dto)).thenReturn(holderQueueDO);

        // Configure BusinessMsgClient.sendMsg(...).
        final BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setMessageGuid("messageGuid");
        businessMessageDTO.setSubject("subject");
        businessMessageDTO.setContent("content");
        businessMessageDTO.setMessageType(0);
        businessMessageDTO.setDetailMessageType(0);
        when(mockBusinessMsgClient.sendMsg(businessMessageDTO)).thenReturn("result");

        // Configure QueueItemMapper.selectList(...).
        final HolderQueueItemDO holderQueueItemDO = new HolderQueueItemDO();
        holderQueueItemDO.setStoreGuid("storeGuid");
        holderQueueItemDO.setQueueGuid("queueGuid");
        holderQueueItemDO.setStatus((byte) 0b0);
        holderQueueItemDO.setSort(0.0f);
        holderQueueItemDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HolderQueueItemDO> holderQueueItemDOS = Arrays.asList(holderQueueItemDO);
        when(mockQueueItemMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(holderQueueItemDOS);

        // Configure QueueItemMapStruct.toDetailDto(...).
        final HolderQueueItemDetailDTO holderQueueItemDetailDTO1 = new HolderQueueItemDetailDTO();
        holderQueueItemDetailDTO1.setQueueGuid("queueGuid");
        holderQueueItemDetailDTO1.setQueueCode("queueCode");
        holderQueueItemDetailDTO1.setSort(0);
        holderQueueItemDetailDTO1.setCode("code");
        holderQueueItemDetailDTO1.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HolderQueueItemDO d = new HolderQueueItemDO();
        d.setStoreGuid("storeGuid");
        d.setQueueGuid("queueGuid");
        d.setStatus((byte) 0b0);
        d.setSort(0.0f);
        d.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockQueueItemMapStruct.toDetailDto(d)).thenReturn(holderQueueItemDetailDTO1);

        // Configure QueueMapStruct.toDetailDto(...).
        final HolderQueueDetailDTO holderQueueDetailDTO1 = new HolderQueueDetailDTO();
        holderQueueDetailDTO1.setGuid("ca8ba3ce-7933-477d-aa89-2c7fc969bb50");
        holderQueueDetailDTO1.setStoreGuid("storeGuid");
        holderQueueDetailDTO1.setName("name");
        holderQueueDetailDTO1.setCode("code");
        holderQueueDetailDTO1.setMin((byte) 0b0);
        holderQueueDetailDTO1.setMax((byte) 0b0);
        holderQueueDetailDTO1.setSize(0);
        final HolderQueueItemDetailDTO holderQueueItemDetailDTO2 = new HolderQueueItemDetailDTO();
        holderQueueItemDetailDTO2.setQueueGuid("queueGuid");
        holderQueueItemDetailDTO2.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        holderQueueDetailDTO1.setItems(Arrays.asList(holderQueueItemDetailDTO2));
        final HolderQueueDO d1 = new HolderQueueDO();
        d1.setGuid("ca8ba3ce-7933-477d-aa89-2c7fc969bb50");
        d1.setStoreGuid("storeGuid");
        d1.setName("name");
        d1.setCode("code");
        d1.setMin((byte) 0b0);
        d1.setMax((byte) 0b0);
        d1.setIsEnable(false);
        d1.setIsDeleted(false);
        d1.setCreateStaffGuid("createStaffGuid");
        d1.setModifiedStaffGuid("createStaffGuid");
        d1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockQueueMapStruct.toDetailDto(d1)).thenReturn(holderQueueDetailDTO1);

        // Run the test
        final StoreQueueDTO result = queueServiceImplUnderTest.all();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testAll_QueueConfigMapperReturnsNull() {
        // Setup
        final HolderQueueDetailDTO holderQueueDetailDTO = new HolderQueueDetailDTO();
        holderQueueDetailDTO.setGuid("ca8ba3ce-7933-477d-aa89-2c7fc969bb50");
        holderQueueDetailDTO.setStoreGuid("storeGuid");
        holderQueueDetailDTO.setName("name");
        holderQueueDetailDTO.setCode("code");
        holderQueueDetailDTO.setMin((byte) 0b0);
        holderQueueDetailDTO.setMax((byte) 0b0);
        holderQueueDetailDTO.setSize(0);
        final HolderQueueItemDetailDTO holderQueueItemDetailDTO = new HolderQueueItemDetailDTO();
        holderQueueItemDetailDTO.setQueueGuid("queueGuid");
        holderQueueItemDetailDTO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        holderQueueDetailDTO.setItems(Arrays.asList(holderQueueItemDetailDTO));
        final StoreQueueDTO expectedResult = new StoreQueueDTO(Arrays.asList(holderQueueDetailDTO), false, false,
                false);
        when(mockConfigMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);
        when(mockTransactionTemplate.execute(any(TransactionCallback.class))).thenReturn("result");

        // Configure QueueMapStruct.toDo(...).
        final HolderQueueDO holderQueueDO = new HolderQueueDO();
        holderQueueDO.setGuid("ca8ba3ce-7933-477d-aa89-2c7fc969bb50");
        holderQueueDO.setStoreGuid("storeGuid");
        holderQueueDO.setName("name");
        holderQueueDO.setCode("code");
        holderQueueDO.setMin((byte) 0b0);
        holderQueueDO.setMax((byte) 0b0);
        holderQueueDO.setIsEnable(false);
        holderQueueDO.setIsDeleted(false);
        holderQueueDO.setCreateStaffGuid("createStaffGuid");
        holderQueueDO.setModifiedStaffGuid("createStaffGuid");
        holderQueueDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        holderQueueDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HolderQueueDTO dto = new HolderQueueDTO();
        dto.setGuid("ca8ba3ce-7933-477d-aa89-2c7fc969bb50");
        dto.setStoreGuid("storeGuid");
        dto.setName("name");
        dto.setCode("code");
        dto.setMin((byte) 0b0);
        dto.setMax((byte) 0b0);
        dto.setSize(0);
        when(mockQueueMapStruct.toDo(dto)).thenReturn(holderQueueDO);

        // Configure BusinessMsgClient.sendMsg(...).
        final BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setMessageGuid("messageGuid");
        businessMessageDTO.setSubject("subject");
        businessMessageDTO.setContent("content");
        businessMessageDTO.setMessageType(0);
        businessMessageDTO.setDetailMessageType(0);
        when(mockBusinessMsgClient.sendMsg(businessMessageDTO)).thenReturn("result");

        // Configure QueueItemMapper.selectList(...).
        final HolderQueueItemDO holderQueueItemDO = new HolderQueueItemDO();
        holderQueueItemDO.setStoreGuid("storeGuid");
        holderQueueItemDO.setQueueGuid("queueGuid");
        holderQueueItemDO.setStatus((byte) 0b0);
        holderQueueItemDO.setSort(0.0f);
        holderQueueItemDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HolderQueueItemDO> holderQueueItemDOS = Arrays.asList(holderQueueItemDO);
        when(mockQueueItemMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(holderQueueItemDOS);

        // Configure QueueItemMapStruct.toDetailDto(...).
        final HolderQueueItemDetailDTO holderQueueItemDetailDTO1 = new HolderQueueItemDetailDTO();
        holderQueueItemDetailDTO1.setQueueGuid("queueGuid");
        holderQueueItemDetailDTO1.setQueueCode("queueCode");
        holderQueueItemDetailDTO1.setSort(0);
        holderQueueItemDetailDTO1.setCode("code");
        holderQueueItemDetailDTO1.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HolderQueueItemDO d = new HolderQueueItemDO();
        d.setStoreGuid("storeGuid");
        d.setQueueGuid("queueGuid");
        d.setStatus((byte) 0b0);
        d.setSort(0.0f);
        d.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockQueueItemMapStruct.toDetailDto(d)).thenReturn(holderQueueItemDetailDTO1);

        // Configure QueueMapStruct.toDetailDto(...).
        final HolderQueueDetailDTO holderQueueDetailDTO1 = new HolderQueueDetailDTO();
        holderQueueDetailDTO1.setGuid("ca8ba3ce-7933-477d-aa89-2c7fc969bb50");
        holderQueueDetailDTO1.setStoreGuid("storeGuid");
        holderQueueDetailDTO1.setName("name");
        holderQueueDetailDTO1.setCode("code");
        holderQueueDetailDTO1.setMin((byte) 0b0);
        holderQueueDetailDTO1.setMax((byte) 0b0);
        holderQueueDetailDTO1.setSize(0);
        final HolderQueueItemDetailDTO holderQueueItemDetailDTO2 = new HolderQueueItemDetailDTO();
        holderQueueItemDetailDTO2.setQueueGuid("queueGuid");
        holderQueueItemDetailDTO2.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        holderQueueDetailDTO1.setItems(Arrays.asList(holderQueueItemDetailDTO2));
        final HolderQueueDO d1 = new HolderQueueDO();
        d1.setGuid("ca8ba3ce-7933-477d-aa89-2c7fc969bb50");
        d1.setStoreGuid("storeGuid");
        d1.setName("name");
        d1.setCode("code");
        d1.setMin((byte) 0b0);
        d1.setMax((byte) 0b0);
        d1.setIsEnable(false);
        d1.setIsDeleted(false);
        d1.setCreateStaffGuid("createStaffGuid");
        d1.setModifiedStaffGuid("createStaffGuid");
        d1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockQueueMapStruct.toDetailDto(d1)).thenReturn(holderQueueDetailDTO1);

        // Run the test
        final StoreQueueDTO result = queueServiceImplUnderTest.all();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testAll_TransactionTemplateReturnsNull() {
        // Setup
        final HolderQueueDetailDTO holderQueueDetailDTO = new HolderQueueDetailDTO();
        holderQueueDetailDTO.setGuid("ca8ba3ce-7933-477d-aa89-2c7fc969bb50");
        holderQueueDetailDTO.setStoreGuid("storeGuid");
        holderQueueDetailDTO.setName("name");
        holderQueueDetailDTO.setCode("code");
        holderQueueDetailDTO.setMin((byte) 0b0);
        holderQueueDetailDTO.setMax((byte) 0b0);
        holderQueueDetailDTO.setSize(0);
        final HolderQueueItemDetailDTO holderQueueItemDetailDTO = new HolderQueueItemDetailDTO();
        holderQueueItemDetailDTO.setQueueGuid("queueGuid");
        holderQueueItemDetailDTO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        holderQueueDetailDTO.setItems(Arrays.asList(holderQueueItemDetailDTO));
        final StoreQueueDTO expectedResult = new StoreQueueDTO(Arrays.asList(holderQueueDetailDTO), false, false,
                false);

        // Configure QueueConfigMapper.selectOne(...).
        final HolderQueueConfigDO holderQueueConfigDO = new HolderQueueConfigDO();
        holderQueueConfigDO.setId(0L);
        holderQueueConfigDO.setStoreGuid("storeGuid");
        holderQueueConfigDO.setIsEnableEat(false);
        holderQueueConfigDO.setIsEnableRecovery(false);
        holderQueueConfigDO.setIsEnableManualReset(false);
        when(mockConfigMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(holderQueueConfigDO);

        when(mockTransactionTemplate.execute(any(TransactionCallback.class))).thenReturn(null);

        // Configure QueueMapStruct.toDo(...).
        final HolderQueueDO holderQueueDO = new HolderQueueDO();
        holderQueueDO.setGuid("ca8ba3ce-7933-477d-aa89-2c7fc969bb50");
        holderQueueDO.setStoreGuid("storeGuid");
        holderQueueDO.setName("name");
        holderQueueDO.setCode("code");
        holderQueueDO.setMin((byte) 0b0);
        holderQueueDO.setMax((byte) 0b0);
        holderQueueDO.setIsEnable(false);
        holderQueueDO.setIsDeleted(false);
        holderQueueDO.setCreateStaffGuid("createStaffGuid");
        holderQueueDO.setModifiedStaffGuid("createStaffGuid");
        holderQueueDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        holderQueueDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HolderQueueDTO dto = new HolderQueueDTO();
        dto.setGuid("ca8ba3ce-7933-477d-aa89-2c7fc969bb50");
        dto.setStoreGuid("storeGuid");
        dto.setName("name");
        dto.setCode("code");
        dto.setMin((byte) 0b0);
        dto.setMax((byte) 0b0);
        dto.setSize(0);
        when(mockQueueMapStruct.toDo(dto)).thenReturn(holderQueueDO);

        // Configure BusinessMsgClient.sendMsg(...).
        final BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setMessageGuid("messageGuid");
        businessMessageDTO.setSubject("subject");
        businessMessageDTO.setContent("content");
        businessMessageDTO.setMessageType(0);
        businessMessageDTO.setDetailMessageType(0);
        when(mockBusinessMsgClient.sendMsg(businessMessageDTO)).thenReturn("result");

        // Configure QueueItemMapper.selectList(...).
        final HolderQueueItemDO holderQueueItemDO = new HolderQueueItemDO();
        holderQueueItemDO.setStoreGuid("storeGuid");
        holderQueueItemDO.setQueueGuid("queueGuid");
        holderQueueItemDO.setStatus((byte) 0b0);
        holderQueueItemDO.setSort(0.0f);
        holderQueueItemDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HolderQueueItemDO> holderQueueItemDOS = Arrays.asList(holderQueueItemDO);
        when(mockQueueItemMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(holderQueueItemDOS);

        // Configure QueueItemMapStruct.toDetailDto(...).
        final HolderQueueItemDetailDTO holderQueueItemDetailDTO1 = new HolderQueueItemDetailDTO();
        holderQueueItemDetailDTO1.setQueueGuid("queueGuid");
        holderQueueItemDetailDTO1.setQueueCode("queueCode");
        holderQueueItemDetailDTO1.setSort(0);
        holderQueueItemDetailDTO1.setCode("code");
        holderQueueItemDetailDTO1.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HolderQueueItemDO d = new HolderQueueItemDO();
        d.setStoreGuid("storeGuid");
        d.setQueueGuid("queueGuid");
        d.setStatus((byte) 0b0);
        d.setSort(0.0f);
        d.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockQueueItemMapStruct.toDetailDto(d)).thenReturn(holderQueueItemDetailDTO1);

        // Configure QueueMapStruct.toDetailDto(...).
        final HolderQueueDetailDTO holderQueueDetailDTO1 = new HolderQueueDetailDTO();
        holderQueueDetailDTO1.setGuid("ca8ba3ce-7933-477d-aa89-2c7fc969bb50");
        holderQueueDetailDTO1.setStoreGuid("storeGuid");
        holderQueueDetailDTO1.setName("name");
        holderQueueDetailDTO1.setCode("code");
        holderQueueDetailDTO1.setMin((byte) 0b0);
        holderQueueDetailDTO1.setMax((byte) 0b0);
        holderQueueDetailDTO1.setSize(0);
        final HolderQueueItemDetailDTO holderQueueItemDetailDTO2 = new HolderQueueItemDetailDTO();
        holderQueueItemDetailDTO2.setQueueGuid("queueGuid");
        holderQueueItemDetailDTO2.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        holderQueueDetailDTO1.setItems(Arrays.asList(holderQueueItemDetailDTO2));
        final HolderQueueDO d1 = new HolderQueueDO();
        d1.setGuid("ca8ba3ce-7933-477d-aa89-2c7fc969bb50");
        d1.setStoreGuid("storeGuid");
        d1.setName("name");
        d1.setCode("code");
        d1.setMin((byte) 0b0);
        d1.setMax((byte) 0b0);
        d1.setIsEnable(false);
        d1.setIsDeleted(false);
        d1.setCreateStaffGuid("createStaffGuid");
        d1.setModifiedStaffGuid("createStaffGuid");
        d1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockQueueMapStruct.toDetailDto(d1)).thenReturn(holderQueueDetailDTO1);

        // Run the test
        final StoreQueueDTO result = queueServiceImplUnderTest.all();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testAll_TransactionTemplateThrowsTransactionException() {
        // Setup
        // Configure QueueConfigMapper.selectOne(...).
        final HolderQueueConfigDO holderQueueConfigDO = new HolderQueueConfigDO();
        holderQueueConfigDO.setId(0L);
        holderQueueConfigDO.setStoreGuid("storeGuid");
        holderQueueConfigDO.setIsEnableEat(false);
        holderQueueConfigDO.setIsEnableRecovery(false);
        holderQueueConfigDO.setIsEnableManualReset(false);
        when(mockConfigMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(holderQueueConfigDO);

        when(mockTransactionTemplate.execute(any(TransactionCallback.class))).thenThrow(TransactionException.class);

        // Run the test
        assertThatThrownBy(() -> queueServiceImplUnderTest.all()).isInstanceOf(Exception.class);
    }

    @Test
    public void testAll_QueueItemMapperReturnsNoItems() {
        // Setup
        final HolderQueueDetailDTO holderQueueDetailDTO = new HolderQueueDetailDTO();
        holderQueueDetailDTO.setGuid("ca8ba3ce-7933-477d-aa89-2c7fc969bb50");
        holderQueueDetailDTO.setStoreGuid("storeGuid");
        holderQueueDetailDTO.setName("name");
        holderQueueDetailDTO.setCode("code");
        holderQueueDetailDTO.setMin((byte) 0b0);
        holderQueueDetailDTO.setMax((byte) 0b0);
        holderQueueDetailDTO.setSize(0);
        final HolderQueueItemDetailDTO holderQueueItemDetailDTO = new HolderQueueItemDetailDTO();
        holderQueueItemDetailDTO.setQueueGuid("queueGuid");
        holderQueueItemDetailDTO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        holderQueueDetailDTO.setItems(Arrays.asList(holderQueueItemDetailDTO));
        final StoreQueueDTO expectedResult = new StoreQueueDTO(Arrays.asList(holderQueueDetailDTO), false, false,
                false);

        // Configure QueueConfigMapper.selectOne(...).
        final HolderQueueConfigDO holderQueueConfigDO = new HolderQueueConfigDO();
        holderQueueConfigDO.setId(0L);
        holderQueueConfigDO.setStoreGuid("storeGuid");
        holderQueueConfigDO.setIsEnableEat(false);
        holderQueueConfigDO.setIsEnableRecovery(false);
        holderQueueConfigDO.setIsEnableManualReset(false);
        when(mockConfigMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(holderQueueConfigDO);

        when(mockQueueItemMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure QueueMapStruct.toDetailDto(...).
        final HolderQueueDetailDTO holderQueueDetailDTO1 = new HolderQueueDetailDTO();
        holderQueueDetailDTO1.setGuid("ca8ba3ce-7933-477d-aa89-2c7fc969bb50");
        holderQueueDetailDTO1.setStoreGuid("storeGuid");
        holderQueueDetailDTO1.setName("name");
        holderQueueDetailDTO1.setCode("code");
        holderQueueDetailDTO1.setMin((byte) 0b0);
        holderQueueDetailDTO1.setMax((byte) 0b0);
        holderQueueDetailDTO1.setSize(0);
        final HolderQueueItemDetailDTO holderQueueItemDetailDTO1 = new HolderQueueItemDetailDTO();
        holderQueueItemDetailDTO1.setQueueGuid("queueGuid");
        holderQueueItemDetailDTO1.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        holderQueueDetailDTO1.setItems(Arrays.asList(holderQueueItemDetailDTO1));
        final HolderQueueDO d = new HolderQueueDO();
        d.setGuid("ca8ba3ce-7933-477d-aa89-2c7fc969bb50");
        d.setStoreGuid("storeGuid");
        d.setName("name");
        d.setCode("code");
        d.setMin((byte) 0b0);
        d.setMax((byte) 0b0);
        d.setIsEnable(false);
        d.setIsDeleted(false);
        d.setCreateStaffGuid("createStaffGuid");
        d.setModifiedStaffGuid("createStaffGuid");
        d.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockQueueMapStruct.toDetailDto(d)).thenReturn(holderQueueDetailDTO1);

        // Run the test
        final StoreQueueDTO result = queueServiceImplUnderTest.all();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testSaveTables() {
        // Setup
        final QueueTableDTO dto = new QueueTableDTO();
        dto.setQueueGuid("queueGuid");
        final TableDTO tableDTO = new TableDTO();
        tableDTO.setTableGuid("tableGuid");
        tableDTO.setAreaGuid("areaGuid");
        tableDTO.setAreaName("areaName");
        dto.setTables(Arrays.asList(tableDTO));

        final QueueTableDTO expectedResult = new QueueTableDTO();
        expectedResult.setQueueGuid("queueGuid");
        final TableDTO tableDTO1 = new TableDTO();
        tableDTO1.setTableGuid("tableGuid");
        tableDTO1.setAreaGuid("areaGuid");
        tableDTO1.setAreaName("areaName");
        expectedResult.setTables(Arrays.asList(tableDTO1));

        when(mockTransactionTemplate.execute(any(TransactionCallback.class))).thenReturn("result");

        // Configure QueueMapStruct.toDo(...).
        final HolderQueueTableDO holderQueueTableDO = new HolderQueueTableDO();
        holderQueueTableDO.setId(0L);
        holderQueueTableDO.setGuid("50b61f68-d05b-4dd4-a835-0ca01a59d172");
        holderQueueTableDO.setQueueGuid("queueGuid");
        holderQueueTableDO.setTableGuid("tableGuid");
        holderQueueTableDO.setTableName("tableName");
        final TableDTO tableDO = new TableDTO();
        tableDO.setTableGuid("tableGuid");
        tableDO.setTableName("tableName");
        tableDO.setAreaGuid("areaGuid");
        tableDO.setAreaName("areaName");
        tableDO.setSeats(0);
        when(mockQueueMapStruct.toDo(tableDO, "queueGuid")).thenReturn(holderQueueTableDO);

        // Configure QueueTableMapper.selectList(...).
        final HolderQueueTableDO holderQueueTableDO1 = new HolderQueueTableDO();
        holderQueueTableDO1.setId(0L);
        holderQueueTableDO1.setGuid("50b61f68-d05b-4dd4-a835-0ca01a59d172");
        holderQueueTableDO1.setQueueGuid("queueGuid");
        holderQueueTableDO1.setTableGuid("tableGuid");
        holderQueueTableDO1.setTableName("tableName");
        final List<HolderQueueTableDO> holderQueueTableDOS = Arrays.asList(holderQueueTableDO1);
        when(mockQueueTableMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(holderQueueTableDOS);

        when(mockQueueTableMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(0);

        // Configure BusinessMsgClient.sendMsg(...).
        final BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setMessageGuid("messageGuid");
        businessMessageDTO.setSubject("subject");
        businessMessageDTO.setContent("content");
        businessMessageDTO.setMessageType(0);
        businessMessageDTO.setDetailMessageType(0);
        when(mockBusinessMsgClient.sendMsg(businessMessageDTO)).thenReturn("result");

        // Run the test
        final QueueTableDTO result = queueServiceImplUnderTest.saveTables(dto);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);

        // Confirm QueueTableServiceImpl.saveBatch(...).
        final HolderQueueTableDO holderQueueTableDO2 = new HolderQueueTableDO();
        holderQueueTableDO2.setId(0L);
        holderQueueTableDO2.setGuid("50b61f68-d05b-4dd4-a835-0ca01a59d172");
        holderQueueTableDO2.setQueueGuid("queueGuid");
        holderQueueTableDO2.setTableGuid("tableGuid");
        holderQueueTableDO2.setTableName("tableName");
        final Collection<HolderQueueTableDO> entityList = Arrays.asList(holderQueueTableDO2);
        verify(mockTableService).saveBatch(entityList);
        verify(mockQueueTableMapper).delete(any(LambdaQueryWrapper.class));
    }

    @Test
    public void testSaveTables_TransactionTemplateReturnsNull() {
        // Setup
        final QueueTableDTO dto = new QueueTableDTO();
        dto.setQueueGuid("queueGuid");
        final TableDTO tableDTO = new TableDTO();
        tableDTO.setTableGuid("tableGuid");
        tableDTO.setAreaGuid("areaGuid");
        tableDTO.setAreaName("areaName");
        dto.setTables(Arrays.asList(tableDTO));

        when(mockTransactionTemplate.execute(any(TransactionCallback.class))).thenReturn(null);

        // Configure QueueMapStruct.toDo(...).
        final HolderQueueTableDO holderQueueTableDO = new HolderQueueTableDO();
        holderQueueTableDO.setId(0L);
        holderQueueTableDO.setGuid("50b61f68-d05b-4dd4-a835-0ca01a59d172");
        holderQueueTableDO.setQueueGuid("queueGuid");
        holderQueueTableDO.setTableGuid("tableGuid");
        holderQueueTableDO.setTableName("tableName");
        final TableDTO tableDO = new TableDTO();
        tableDO.setTableGuid("tableGuid");
        tableDO.setTableName("tableName");
        tableDO.setAreaGuid("areaGuid");
        tableDO.setAreaName("areaName");
        tableDO.setSeats(0);
        when(mockQueueMapStruct.toDo(tableDO, "queueGuid")).thenReturn(holderQueueTableDO);

        // Configure QueueTableMapper.selectList(...).
        final HolderQueueTableDO holderQueueTableDO1 = new HolderQueueTableDO();
        holderQueueTableDO1.setId(0L);
        holderQueueTableDO1.setGuid("50b61f68-d05b-4dd4-a835-0ca01a59d172");
        holderQueueTableDO1.setQueueGuid("queueGuid");
        holderQueueTableDO1.setTableGuid("tableGuid");
        holderQueueTableDO1.setTableName("tableName");
        final List<HolderQueueTableDO> holderQueueTableDOS = Arrays.asList(holderQueueTableDO1);
        when(mockQueueTableMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(holderQueueTableDOS);

        when(mockQueueTableMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(0);

        // Configure BusinessMsgClient.sendMsg(...).
        final BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setMessageGuid("messageGuid");
        businessMessageDTO.setSubject("subject");
        businessMessageDTO.setContent("content");
        businessMessageDTO.setMessageType(0);
        businessMessageDTO.setDetailMessageType(0);
        when(mockBusinessMsgClient.sendMsg(businessMessageDTO)).thenReturn("result");

        // Run the test
        final QueueTableDTO result = queueServiceImplUnderTest.saveTables(dto);

        // Verify the results
        assertThat(result).isNull();

        // Confirm QueueTableServiceImpl.saveBatch(...).
        final HolderQueueTableDO holderQueueTableDO2 = new HolderQueueTableDO();
        holderQueueTableDO2.setId(0L);
        holderQueueTableDO2.setGuid("50b61f68-d05b-4dd4-a835-0ca01a59d172");
        holderQueueTableDO2.setQueueGuid("queueGuid");
        holderQueueTableDO2.setTableGuid("tableGuid");
        holderQueueTableDO2.setTableName("tableName");
        final Collection<HolderQueueTableDO> entityList = Arrays.asList(holderQueueTableDO2);
        verify(mockTableService).saveBatch(entityList);
        verify(mockQueueTableMapper).delete(any(LambdaQueryWrapper.class));
    }

    @Test
    public void testSaveTables_TransactionTemplateThrowsTransactionException() {
        // Setup
        final QueueTableDTO dto = new QueueTableDTO();
        dto.setQueueGuid("queueGuid");
        final TableDTO tableDTO = new TableDTO();
        tableDTO.setTableGuid("tableGuid");
        tableDTO.setAreaGuid("areaGuid");
        tableDTO.setAreaName("areaName");
        dto.setTables(Arrays.asList(tableDTO));

        when(mockTransactionTemplate.execute(any(TransactionCallback.class))).thenThrow(TransactionException.class);

        // Run the test
        assertThatThrownBy(() -> queueServiceImplUnderTest.saveTables(dto)).isInstanceOf(Exception.class);
    }

    @Test
    public void testSaveTables_QueueTableMapperSelectListReturnsNoItems() {
        // Setup
        final QueueTableDTO dto = new QueueTableDTO();
        dto.setQueueGuid("queueGuid");
        final TableDTO tableDTO = new TableDTO();
        tableDTO.setTableGuid("tableGuid");
        tableDTO.setAreaGuid("areaGuid");
        tableDTO.setAreaName("areaName");
        dto.setTables(Arrays.asList(tableDTO));

        final QueueTableDTO expectedResult = new QueueTableDTO();
        expectedResult.setQueueGuid("queueGuid");
        final TableDTO tableDTO1 = new TableDTO();
        tableDTO1.setTableGuid("tableGuid");
        tableDTO1.setAreaGuid("areaGuid");
        tableDTO1.setAreaName("areaName");
        expectedResult.setTables(Arrays.asList(tableDTO1));

        when(mockTransactionTemplate.execute(any(TransactionCallback.class))).thenReturn("result");

        // Configure QueueMapStruct.toDo(...).
        final HolderQueueTableDO holderQueueTableDO = new HolderQueueTableDO();
        holderQueueTableDO.setId(0L);
        holderQueueTableDO.setGuid("50b61f68-d05b-4dd4-a835-0ca01a59d172");
        holderQueueTableDO.setQueueGuid("queueGuid");
        holderQueueTableDO.setTableGuid("tableGuid");
        holderQueueTableDO.setTableName("tableName");
        final TableDTO tableDO = new TableDTO();
        tableDO.setTableGuid("tableGuid");
        tableDO.setTableName("tableName");
        tableDO.setAreaGuid("areaGuid");
        tableDO.setAreaName("areaName");
        tableDO.setSeats(0);
        when(mockQueueMapStruct.toDo(tableDO, "queueGuid")).thenReturn(holderQueueTableDO);

        when(mockQueueTableMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());
        when(mockQueueTableMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(0);

        // Configure BusinessMsgClient.sendMsg(...).
        final BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setMessageGuid("messageGuid");
        businessMessageDTO.setSubject("subject");
        businessMessageDTO.setContent("content");
        businessMessageDTO.setMessageType(0);
        businessMessageDTO.setDetailMessageType(0);
        when(mockBusinessMsgClient.sendMsg(businessMessageDTO)).thenReturn("result");

        // Run the test
        final QueueTableDTO result = queueServiceImplUnderTest.saveTables(dto);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);

        // Confirm QueueTableServiceImpl.saveBatch(...).
        final HolderQueueTableDO holderQueueTableDO1 = new HolderQueueTableDO();
        holderQueueTableDO1.setId(0L);
        holderQueueTableDO1.setGuid("50b61f68-d05b-4dd4-a835-0ca01a59d172");
        holderQueueTableDO1.setQueueGuid("queueGuid");
        holderQueueTableDO1.setTableGuid("tableGuid");
        holderQueueTableDO1.setTableName("tableName");
        final Collection<HolderQueueTableDO> entityList = Arrays.asList(holderQueueTableDO1);
        verify(mockTableService).saveBatch(entityList);
    }

    @Test
    public void testAllEmpty() {
        // Setup
        when(mockQueueItemMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(0);

        // Run the test
        final Boolean result = queueServiceImplUnderTest.allEmpty("storeGuid");

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testListByStore() {
        // Setup
        final HolderQueueDTO holderQueueDTO = new HolderQueueDTO();
        holderQueueDTO.setGuid("ca8ba3ce-7933-477d-aa89-2c7fc969bb50");
        holderQueueDTO.setStoreGuid("storeGuid");
        holderQueueDTO.setName("name");
        holderQueueDTO.setCode("code");
        holderQueueDTO.setMin((byte) 0b0);
        holderQueueDTO.setMax((byte) 0b0);
        holderQueueDTO.setSize(0);
        final List<HolderQueueDTO> expectedResult = Arrays.asList(holderQueueDTO);

        // Configure QueueMapStruct.toDto(...).
        final HolderQueueDTO holderQueueDTO1 = new HolderQueueDTO();
        holderQueueDTO1.setGuid("ca8ba3ce-7933-477d-aa89-2c7fc969bb50");
        holderQueueDTO1.setStoreGuid("storeGuid");
        holderQueueDTO1.setName("name");
        holderQueueDTO1.setCode("code");
        holderQueueDTO1.setMin((byte) 0b0);
        holderQueueDTO1.setMax((byte) 0b0);
        holderQueueDTO1.setSize(0);
        final HolderQueueDO d = new HolderQueueDO();
        d.setGuid("ca8ba3ce-7933-477d-aa89-2c7fc969bb50");
        d.setStoreGuid("storeGuid");
        d.setName("name");
        d.setCode("code");
        d.setMin((byte) 0b0);
        d.setMax((byte) 0b0);
        d.setIsEnable(false);
        d.setIsDeleted(false);
        d.setCreateStaffGuid("createStaffGuid");
        d.setModifiedStaffGuid("createStaffGuid");
        d.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockQueueMapStruct.toDto(d)).thenReturn(holderQueueDTO1);

        // Run the test
        final List<HolderQueueDTO> result = queueServiceImplUnderTest.listByStore("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQuery() {
        // Setup
        final QueueDetailDTO queueDetailDTO = new QueueDetailDTO();
        queueDetailDTO.setMin((byte) 0b0);
        queueDetailDTO.setIndex(0);
        final TableDTO tableDTO = new TableDTO();
        tableDTO.setTableGuid("tableGuid");
        tableDTO.setAreaGuid("areaGuid");
        tableDTO.setAreaName("areaName");
        queueDetailDTO.setTables(Arrays.asList(tableDTO));
        final List<QueueDetailDTO> expectedResult = Arrays.asList(queueDetailDTO);
        when(mockTransactionTemplate.execute(any(TransactionCallback.class))).thenReturn("result");

        // Configure QueueMapStruct.toDo(...).
        final HolderQueueDO holderQueueDO = new HolderQueueDO();
        holderQueueDO.setGuid("ca8ba3ce-7933-477d-aa89-2c7fc969bb50");
        holderQueueDO.setStoreGuid("storeGuid");
        holderQueueDO.setName("name");
        holderQueueDO.setCode("code");
        holderQueueDO.setMin((byte) 0b0);
        holderQueueDO.setMax((byte) 0b0);
        holderQueueDO.setIsEnable(false);
        holderQueueDO.setIsDeleted(false);
        holderQueueDO.setCreateStaffGuid("createStaffGuid");
        holderQueueDO.setModifiedStaffGuid("createStaffGuid");
        holderQueueDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        holderQueueDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HolderQueueDTO dto = new HolderQueueDTO();
        dto.setGuid("ca8ba3ce-7933-477d-aa89-2c7fc969bb50");
        dto.setStoreGuid("storeGuid");
        dto.setName("name");
        dto.setCode("code");
        dto.setMin((byte) 0b0);
        dto.setMax((byte) 0b0);
        dto.setSize(0);
        when(mockQueueMapStruct.toDo(dto)).thenReturn(holderQueueDO);

        // Configure BusinessMsgClient.sendMsg(...).
        final BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setMessageGuid("messageGuid");
        businessMessageDTO.setSubject("subject");
        businessMessageDTO.setContent("content");
        businessMessageDTO.setMessageType(0);
        businessMessageDTO.setDetailMessageType(0);
        when(mockBusinessMsgClient.sendMsg(businessMessageDTO)).thenReturn("result");

        // Run the test
        final List<QueueDetailDTO> result = queueServiceImplUnderTest.query("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQuery_TransactionTemplateReturnsNull() {
        // Setup
        final QueueDetailDTO queueDetailDTO = new QueueDetailDTO();
        queueDetailDTO.setMin((byte) 0b0);
        queueDetailDTO.setIndex(0);
        final TableDTO tableDTO = new TableDTO();
        tableDTO.setTableGuid("tableGuid");
        tableDTO.setAreaGuid("areaGuid");
        tableDTO.setAreaName("areaName");
        queueDetailDTO.setTables(Arrays.asList(tableDTO));
        final List<QueueDetailDTO> expectedResult = Arrays.asList(queueDetailDTO);
        when(mockTransactionTemplate.execute(any(TransactionCallback.class))).thenReturn(null);

        // Configure QueueMapStruct.toDo(...).
        final HolderQueueDO holderQueueDO = new HolderQueueDO();
        holderQueueDO.setGuid("ca8ba3ce-7933-477d-aa89-2c7fc969bb50");
        holderQueueDO.setStoreGuid("storeGuid");
        holderQueueDO.setName("name");
        holderQueueDO.setCode("code");
        holderQueueDO.setMin((byte) 0b0);
        holderQueueDO.setMax((byte) 0b0);
        holderQueueDO.setIsEnable(false);
        holderQueueDO.setIsDeleted(false);
        holderQueueDO.setCreateStaffGuid("createStaffGuid");
        holderQueueDO.setModifiedStaffGuid("createStaffGuid");
        holderQueueDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        holderQueueDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HolderQueueDTO dto = new HolderQueueDTO();
        dto.setGuid("ca8ba3ce-7933-477d-aa89-2c7fc969bb50");
        dto.setStoreGuid("storeGuid");
        dto.setName("name");
        dto.setCode("code");
        dto.setMin((byte) 0b0);
        dto.setMax((byte) 0b0);
        dto.setSize(0);
        when(mockQueueMapStruct.toDo(dto)).thenReturn(holderQueueDO);

        // Configure BusinessMsgClient.sendMsg(...).
        final BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setMessageGuid("messageGuid");
        businessMessageDTO.setSubject("subject");
        businessMessageDTO.setContent("content");
        businessMessageDTO.setMessageType(0);
        businessMessageDTO.setDetailMessageType(0);
        when(mockBusinessMsgClient.sendMsg(businessMessageDTO)).thenReturn("result");

        // Run the test
        final List<QueueDetailDTO> result = queueServiceImplUnderTest.query("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQuery_TransactionTemplateThrowsTransactionException() {
        // Setup
        when(mockTransactionTemplate.execute(any(TransactionCallback.class))).thenThrow(TransactionException.class);

        // Run the test
        assertThatThrownBy(() -> queueServiceImplUnderTest.query("storeGuid")).isInstanceOf(Exception.class);
    }

    @Test
    public void testQuery_QueueTableMapperReturnsNoItems() {
        // Setup
        final QueueDetailDTO queueDetailDTO = new QueueDetailDTO();
        queueDetailDTO.setMin((byte) 0b0);
        queueDetailDTO.setIndex(0);
        final TableDTO tableDTO = new TableDTO();
        tableDTO.setTableGuid("tableGuid");
        tableDTO.setAreaGuid("areaGuid");
        tableDTO.setAreaName("areaName");
        queueDetailDTO.setTables(Arrays.asList(tableDTO));
        final List<QueueDetailDTO> expectedResult = Arrays.asList(queueDetailDTO);

        // Configure QueueMapStruct.toTableDto(...).
        final QueueDetailDTO queueDetailDTO1 = new QueueDetailDTO();
        queueDetailDTO1.setMin((byte) 0b0);
        queueDetailDTO1.setIndex(0);
        final TableDTO tableDTO1 = new TableDTO();
        tableDTO1.setTableGuid("tableGuid");
        tableDTO1.setAreaGuid("areaGuid");
        tableDTO1.setAreaName("areaName");
        queueDetailDTO1.setTables(Arrays.asList(tableDTO1));
        final HolderQueueDO d = new HolderQueueDO();
        d.setGuid("ca8ba3ce-7933-477d-aa89-2c7fc969bb50");
        d.setStoreGuid("storeGuid");
        d.setName("name");
        d.setCode("code");
        d.setMin((byte) 0b0);
        d.setMax((byte) 0b0);
        d.setIsEnable(false);
        d.setIsDeleted(false);
        d.setCreateStaffGuid("createStaffGuid");
        d.setModifiedStaffGuid("createStaffGuid");
        d.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockQueueMapStruct.toTableDto(d)).thenReturn(queueDetailDTO1);

        when(mockQueueTableMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure TableClientService.listByWeb(...).
        final TableBasicDTO tableBasicDTO = new TableBasicDTO();
        tableBasicDTO.setGuid("bd2aef79-6529-4a41-bc41-a835de2aee58");
        tableBasicDTO.setStoreGuid("storeGuid");
        tableBasicDTO.setStoreName("storeName");
        tableBasicDTO.setAreaGuid("areaGuid");
        tableBasicDTO.setSort(0);
        final List<TableBasicDTO> tableBasicDTOS = Arrays.asList(tableBasicDTO);
        final TableBasicQueryDTO tableBasicQueryDTO = new TableBasicQueryDTO();
        tableBasicQueryDTO.setStoreGuid("storeGuid");
        tableBasicQueryDTO.setAreaGuid("areaGuid");
        tableBasicQueryDTO.setTableGuidList(Arrays.asList("value"));
        when(mockTableClientService.listByWeb(tableBasicQueryDTO)).thenReturn(tableBasicDTOS);

        // Configure QueueMapStruct.toDto(...).
        final TreeTableDTO treeTableDTO = new TreeTableDTO();
        treeTableDTO.setTableGuid("tableGuid");
        treeTableDTO.setAreaGuid("areaGuid");
        treeTableDTO.setAreaName("areaName");
        treeTableDTO.setChildren(Arrays.asList(new TreeTableDTO()));
        treeTableDTO.setQueueGuid("queueGuid");
        final TableBasicDTO tableBasicDTO1 = new TableBasicDTO();
        tableBasicDTO1.setGuid("bd2aef79-6529-4a41-bc41-a835de2aee58");
        tableBasicDTO1.setStoreGuid("storeGuid");
        tableBasicDTO1.setStoreName("storeName");
        tableBasicDTO1.setAreaGuid("areaGuid");
        tableBasicDTO1.setSort(0);
        when(mockQueueMapStruct.toDto(tableBasicDTO1)).thenReturn(treeTableDTO);

        // Run the test
        final List<QueueDetailDTO> result = queueServiceImplUnderTest.query("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQuery_TableClientServiceReturnsNoItems() {
        // Setup
        // Configure QueueMapStruct.toTableDto(...).
        final QueueDetailDTO queueDetailDTO = new QueueDetailDTO();
        queueDetailDTO.setMin((byte) 0b0);
        queueDetailDTO.setIndex(0);
        final TableDTO tableDTO = new TableDTO();
        tableDTO.setTableGuid("tableGuid");
        tableDTO.setAreaGuid("areaGuid");
        tableDTO.setAreaName("areaName");
        queueDetailDTO.setTables(Arrays.asList(tableDTO));
        final HolderQueueDO d = new HolderQueueDO();
        d.setGuid("ca8ba3ce-7933-477d-aa89-2c7fc969bb50");
        d.setStoreGuid("storeGuid");
        d.setName("name");
        d.setCode("code");
        d.setMin((byte) 0b0);
        d.setMax((byte) 0b0);
        d.setIsEnable(false);
        d.setIsDeleted(false);
        d.setCreateStaffGuid("createStaffGuid");
        d.setModifiedStaffGuid("createStaffGuid");
        d.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockQueueMapStruct.toTableDto(d)).thenReturn(queueDetailDTO);

        // Configure QueueTableMapper.selectList(...).
        final HolderQueueTableDO holderQueueTableDO = new HolderQueueTableDO();
        holderQueueTableDO.setId(0L);
        holderQueueTableDO.setGuid("50b61f68-d05b-4dd4-a835-0ca01a59d172");
        holderQueueTableDO.setQueueGuid("queueGuid");
        holderQueueTableDO.setTableGuid("tableGuid");
        holderQueueTableDO.setTableName("tableName");
        final List<HolderQueueTableDO> holderQueueTableDOS = Arrays.asList(holderQueueTableDO);
        when(mockQueueTableMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(holderQueueTableDOS);

        // Configure TableClientService.listByWeb(...).
        final TableBasicQueryDTO tableBasicQueryDTO = new TableBasicQueryDTO();
        tableBasicQueryDTO.setStoreGuid("storeGuid");
        tableBasicQueryDTO.setAreaGuid("areaGuid");
        tableBasicQueryDTO.setTableGuidList(Arrays.asList("value"));
        when(mockTableClientService.listByWeb(tableBasicQueryDTO)).thenReturn(Collections.emptyList());

        // Configure QueueMapStruct.toDto(...).
        final TreeTableDTO treeTableDTO = new TreeTableDTO();
        treeTableDTO.setTableGuid("tableGuid");
        treeTableDTO.setAreaGuid("areaGuid");
        treeTableDTO.setAreaName("areaName");
        treeTableDTO.setChildren(Arrays.asList(new TreeTableDTO()));
        treeTableDTO.setQueueGuid("queueGuid");
        final TableBasicDTO tableBasicDTO = new TableBasicDTO();
        tableBasicDTO.setGuid("bd2aef79-6529-4a41-bc41-a835de2aee58");
        tableBasicDTO.setStoreGuid("storeGuid");
        tableBasicDTO.setStoreName("storeName");
        tableBasicDTO.setAreaGuid("areaGuid");
        tableBasicDTO.setSort(0);
        when(mockQueueMapStruct.toDto(tableBasicDTO)).thenReturn(treeTableDTO);

        // Run the test
        final List<QueueDetailDTO> result = queueServiceImplUnderTest.query("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testObtain() {
        // Setup
        final QueueTableDTO expectedResult = new QueueTableDTO();
        expectedResult.setQueueGuid("queueGuid");
        final TableDTO tableDTO = new TableDTO();
        tableDTO.setTableGuid("tableGuid");
        tableDTO.setAreaGuid("areaGuid");
        tableDTO.setAreaName("areaName");
        expectedResult.setTables(Arrays.asList(tableDTO));

        // Configure QueueTableMapper.selectList(...).
        final HolderQueueTableDO holderQueueTableDO = new HolderQueueTableDO();
        holderQueueTableDO.setId(0L);
        holderQueueTableDO.setGuid("50b61f68-d05b-4dd4-a835-0ca01a59d172");
        holderQueueTableDO.setQueueGuid("queueGuid");
        holderQueueTableDO.setTableGuid("tableGuid");
        holderQueueTableDO.setTableName("tableName");
        final List<HolderQueueTableDO> holderQueueTableDOS = Arrays.asList(holderQueueTableDO);
        when(mockQueueTableMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(holderQueueTableDOS);

        // Configure QueueMapStruct.toDto(...).
        final TreeTableDTO treeTableDTO = new TreeTableDTO();
        treeTableDTO.setTableGuid("tableGuid");
        treeTableDTO.setAreaGuid("areaGuid");
        treeTableDTO.setAreaName("areaName");
        treeTableDTO.setChildren(Arrays.asList(new TreeTableDTO()));
        treeTableDTO.setQueueGuid("queueGuid");
        final ArrayList<TreeTableDTO> treeTableDTOS = new ArrayList<>(Arrays.asList(treeTableDTO));
        final HolderQueueTableDO holderQueueTableDO1 = new HolderQueueTableDO();
        holderQueueTableDO1.setId(0L);
        holderQueueTableDO1.setGuid("50b61f68-d05b-4dd4-a835-0ca01a59d172");
        holderQueueTableDO1.setQueueGuid("queueGuid");
        holderQueueTableDO1.setTableGuid("tableGuid");
        holderQueueTableDO1.setTableName("tableName");
        final Collection<HolderQueueTableDO> tableDO = Arrays.asList(holderQueueTableDO1);
        when(mockQueueMapStruct.toDto(tableDO)).thenReturn(treeTableDTOS);

        // Run the test
        final QueueTableDTO result = queueServiceImplUnderTest.obtain("queueGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testObtain_QueueTableMapperReturnsNoItems() {
        // Setup
        final QueueTableDTO expectedResult = new QueueTableDTO();
        expectedResult.setQueueGuid("queueGuid");
        final TableDTO tableDTO = new TableDTO();
        tableDTO.setTableGuid("tableGuid");
        tableDTO.setAreaGuid("areaGuid");
        tableDTO.setAreaName("areaName");
        expectedResult.setTables(Arrays.asList(tableDTO));

        when(mockQueueTableMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure QueueMapStruct.toDto(...).
        final TreeTableDTO treeTableDTO = new TreeTableDTO();
        treeTableDTO.setTableGuid("tableGuid");
        treeTableDTO.setAreaGuid("areaGuid");
        treeTableDTO.setAreaName("areaName");
        treeTableDTO.setChildren(Arrays.asList(new TreeTableDTO()));
        treeTableDTO.setQueueGuid("queueGuid");
        final ArrayList<TreeTableDTO> treeTableDTOS = new ArrayList<>(Arrays.asList(treeTableDTO));
        final HolderQueueTableDO holderQueueTableDO = new HolderQueueTableDO();
        holderQueueTableDO.setId(0L);
        holderQueueTableDO.setGuid("50b61f68-d05b-4dd4-a835-0ca01a59d172");
        holderQueueTableDO.setQueueGuid("queueGuid");
        holderQueueTableDO.setTableGuid("tableGuid");
        holderQueueTableDO.setTableName("tableName");
        final Collection<HolderQueueTableDO> tableDO = Arrays.asList(holderQueueTableDO);
        when(mockQueueMapStruct.toDto(tableDO)).thenReturn(treeTableDTOS);

        // Run the test
        final QueueTableDTO result = queueServiceImplUnderTest.obtain("queueGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testObtain_QueueMapStructReturnsNoItems() {
        // Setup
        final QueueTableDTO expectedResult = new QueueTableDTO();
        expectedResult.setQueueGuid("queueGuid");
        final TableDTO tableDTO = new TableDTO();
        tableDTO.setTableGuid("tableGuid");
        tableDTO.setAreaGuid("areaGuid");
        tableDTO.setAreaName("areaName");
        expectedResult.setTables(Arrays.asList(tableDTO));

        // Configure QueueTableMapper.selectList(...).
        final HolderQueueTableDO holderQueueTableDO = new HolderQueueTableDO();
        holderQueueTableDO.setId(0L);
        holderQueueTableDO.setGuid("50b61f68-d05b-4dd4-a835-0ca01a59d172");
        holderQueueTableDO.setQueueGuid("queueGuid");
        holderQueueTableDO.setTableGuid("tableGuid");
        holderQueueTableDO.setTableName("tableName");
        final List<HolderQueueTableDO> holderQueueTableDOS = Arrays.asList(holderQueueTableDO);
        when(mockQueueTableMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(holderQueueTableDOS);

        // Configure QueueMapStruct.toDto(...).
        final HolderQueueTableDO holderQueueTableDO1 = new HolderQueueTableDO();
        holderQueueTableDO1.setId(0L);
        holderQueueTableDO1.setGuid("50b61f68-d05b-4dd4-a835-0ca01a59d172");
        holderQueueTableDO1.setQueueGuid("queueGuid");
        holderQueueTableDO1.setTableGuid("tableGuid");
        holderQueueTableDO1.setTableName("tableName");
        final Collection<HolderQueueTableDO> tableDO = Arrays.asList(holderQueueTableDO1);
        when(mockQueueMapStruct.toDto(tableDO)).thenReturn(new ArrayList<>());

        // Run the test
        final QueueTableDTO result = queueServiceImplUnderTest.obtain("queueGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryByStore() {
        // Setup
        final QueueTableDTO queueTableDTO = new QueueTableDTO();
        queueTableDTO.setQueueGuid("queueGuid");
        final TableDTO tableDTO = new TableDTO();
        tableDTO.setTableGuid("tableGuid");
        tableDTO.setAreaGuid("areaGuid");
        tableDTO.setAreaName("areaName");
        queueTableDTO.setTables(Arrays.asList(tableDTO));
        final List<QueueTableDTO> expectedResult = Arrays.asList(queueTableDTO);

        // Configure QueueTableMapper.selectList(...).
        final HolderQueueTableDO holderQueueTableDO = new HolderQueueTableDO();
        holderQueueTableDO.setId(0L);
        holderQueueTableDO.setGuid("50b61f68-d05b-4dd4-a835-0ca01a59d172");
        holderQueueTableDO.setQueueGuid("queueGuid");
        holderQueueTableDO.setTableGuid("tableGuid");
        holderQueueTableDO.setTableName("tableName");
        final List<HolderQueueTableDO> holderQueueTableDOS = Arrays.asList(holderQueueTableDO);
        when(mockQueueTableMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(holderQueueTableDOS);

        // Configure QueueMapStruct.toDto(...).
        final TreeTableDTO treeTableDTO = new TreeTableDTO();
        treeTableDTO.setTableGuid("tableGuid");
        treeTableDTO.setAreaGuid("areaGuid");
        treeTableDTO.setAreaName("areaName");
        treeTableDTO.setChildren(Arrays.asList(new TreeTableDTO()));
        treeTableDTO.setQueueGuid("queueGuid");
        final ArrayList<TreeTableDTO> treeTableDTOS = new ArrayList<>(Arrays.asList(treeTableDTO));
        final HolderQueueTableDO holderQueueTableDO1 = new HolderQueueTableDO();
        holderQueueTableDO1.setId(0L);
        holderQueueTableDO1.setGuid("50b61f68-d05b-4dd4-a835-0ca01a59d172");
        holderQueueTableDO1.setQueueGuid("queueGuid");
        holderQueueTableDO1.setTableGuid("tableGuid");
        holderQueueTableDO1.setTableName("tableName");
        final Collection<HolderQueueTableDO> tableDO = Arrays.asList(holderQueueTableDO1);
        when(mockQueueMapStruct.toDto(tableDO)).thenReturn(treeTableDTOS);

        // Run the test
        final List<QueueTableDTO> result = queueServiceImplUnderTest.queryByStore("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryByStore_QueueTableMapperReturnsNoItems() {
        // Setup
        when(mockQueueTableMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<QueueTableDTO> result = queueServiceImplUnderTest.queryByStore("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testQueryByStore_QueueMapStructReturnsNoItems() {
        // Setup
        final QueueTableDTO queueTableDTO = new QueueTableDTO();
        queueTableDTO.setQueueGuid("queueGuid");
        final TableDTO tableDTO = new TableDTO();
        tableDTO.setTableGuid("tableGuid");
        tableDTO.setAreaGuid("areaGuid");
        tableDTO.setAreaName("areaName");
        queueTableDTO.setTables(Arrays.asList(tableDTO));
        final List<QueueTableDTO> expectedResult = Arrays.asList(queueTableDTO);

        // Configure QueueTableMapper.selectList(...).
        final HolderQueueTableDO holderQueueTableDO = new HolderQueueTableDO();
        holderQueueTableDO.setId(0L);
        holderQueueTableDO.setGuid("50b61f68-d05b-4dd4-a835-0ca01a59d172");
        holderQueueTableDO.setQueueGuid("queueGuid");
        holderQueueTableDO.setTableGuid("tableGuid");
        holderQueueTableDO.setTableName("tableName");
        final List<HolderQueueTableDO> holderQueueTableDOS = Arrays.asList(holderQueueTableDO);
        when(mockQueueTableMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(holderQueueTableDOS);

        // Configure QueueMapStruct.toDto(...).
        final HolderQueueTableDO holderQueueTableDO1 = new HolderQueueTableDO();
        holderQueueTableDO1.setId(0L);
        holderQueueTableDO1.setGuid("50b61f68-d05b-4dd4-a835-0ca01a59d172");
        holderQueueTableDO1.setQueueGuid("queueGuid");
        holderQueueTableDO1.setTableGuid("tableGuid");
        holderQueueTableDO1.setTableName("tableName");
        final Collection<HolderQueueTableDO> tableDO = Arrays.asList(holderQueueTableDO1);
        when(mockQueueMapStruct.toDto(tableDO)).thenReturn(new ArrayList<>());

        // Run the test
        final List<QueueTableDTO> result = queueServiceImplUnderTest.queryByStore("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testAllTables() {
        // Setup
        final TreeTableDTO treeTableDTO = new TreeTableDTO();
        treeTableDTO.setTableGuid("tableGuid");
        treeTableDTO.setAreaGuid("areaGuid");
        treeTableDTO.setAreaName("areaName");
        treeTableDTO.setChildren(Arrays.asList(new TreeTableDTO()));
        treeTableDTO.setQueueGuid("queueGuid");
        final List<TreeTableDTO> expectedResult = Arrays.asList(treeTableDTO);

        // Configure TableClientService.listByWeb(...).
        final TableBasicDTO tableBasicDTO = new TableBasicDTO();
        tableBasicDTO.setGuid("bd2aef79-6529-4a41-bc41-a835de2aee58");
        tableBasicDTO.setStoreGuid("storeGuid");
        tableBasicDTO.setStoreName("storeName");
        tableBasicDTO.setAreaGuid("areaGuid");
        tableBasicDTO.setSort(0);
        final List<TableBasicDTO> tableBasicDTOS = Arrays.asList(tableBasicDTO);
        final TableBasicQueryDTO tableBasicQueryDTO = new TableBasicQueryDTO();
        tableBasicQueryDTO.setStoreGuid("storeGuid");
        tableBasicQueryDTO.setAreaGuid("areaGuid");
        tableBasicQueryDTO.setTableGuidList(Arrays.asList("value"));
        when(mockTableClientService.listByWeb(tableBasicQueryDTO)).thenReturn(tableBasicDTOS);

        // Configure QueueMapStruct.toDto(...).
        final TreeTableDTO treeTableDTO1 = new TreeTableDTO();
        treeTableDTO1.setTableGuid("tableGuid");
        treeTableDTO1.setAreaGuid("areaGuid");
        treeTableDTO1.setAreaName("areaName");
        treeTableDTO1.setChildren(Arrays.asList(new TreeTableDTO()));
        treeTableDTO1.setQueueGuid("queueGuid");
        final TableBasicDTO tableBasicDTO1 = new TableBasicDTO();
        tableBasicDTO1.setGuid("bd2aef79-6529-4a41-bc41-a835de2aee58");
        tableBasicDTO1.setStoreGuid("storeGuid");
        tableBasicDTO1.setStoreName("storeName");
        tableBasicDTO1.setAreaGuid("areaGuid");
        tableBasicDTO1.setSort(0);
        when(mockQueueMapStruct.toDto(tableBasicDTO1)).thenReturn(treeTableDTO1);

        // Configure QueueTableMapper.selectList(...).
        final HolderQueueTableDO holderQueueTableDO = new HolderQueueTableDO();
        holderQueueTableDO.setId(0L);
        holderQueueTableDO.setGuid("50b61f68-d05b-4dd4-a835-0ca01a59d172");
        holderQueueTableDO.setQueueGuid("queueGuid");
        holderQueueTableDO.setTableGuid("tableGuid");
        holderQueueTableDO.setTableName("tableName");
        final List<HolderQueueTableDO> holderQueueTableDOS = Arrays.asList(holderQueueTableDO);
        when(mockQueueTableMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(holderQueueTableDOS);

        // Run the test
        final List<TreeTableDTO> result = queueServiceImplUnderTest.allTables("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testAllTables_TableClientServiceReturnsNoItems() {
        // Setup
        // Configure TableClientService.listByWeb(...).
        final TableBasicQueryDTO tableBasicQueryDTO = new TableBasicQueryDTO();
        tableBasicQueryDTO.setStoreGuid("storeGuid");
        tableBasicQueryDTO.setAreaGuid("areaGuid");
        tableBasicQueryDTO.setTableGuidList(Arrays.asList("value"));
        when(mockTableClientService.listByWeb(tableBasicQueryDTO)).thenReturn(Collections.emptyList());

        // Configure QueueMapStruct.toDto(...).
        final TreeTableDTO treeTableDTO = new TreeTableDTO();
        treeTableDTO.setTableGuid("tableGuid");
        treeTableDTO.setAreaGuid("areaGuid");
        treeTableDTO.setAreaName("areaName");
        treeTableDTO.setChildren(Arrays.asList(new TreeTableDTO()));
        treeTableDTO.setQueueGuid("queueGuid");
        final TableBasicDTO tableBasicDTO = new TableBasicDTO();
        tableBasicDTO.setGuid("bd2aef79-6529-4a41-bc41-a835de2aee58");
        tableBasicDTO.setStoreGuid("storeGuid");
        tableBasicDTO.setStoreName("storeName");
        tableBasicDTO.setAreaGuid("areaGuid");
        tableBasicDTO.setSort(0);
        when(mockQueueMapStruct.toDto(tableBasicDTO)).thenReturn(treeTableDTO);

        // Configure QueueTableMapper.selectList(...).
        final HolderQueueTableDO holderQueueTableDO = new HolderQueueTableDO();
        holderQueueTableDO.setId(0L);
        holderQueueTableDO.setGuid("50b61f68-d05b-4dd4-a835-0ca01a59d172");
        holderQueueTableDO.setQueueGuid("queueGuid");
        holderQueueTableDO.setTableGuid("tableGuid");
        holderQueueTableDO.setTableName("tableName");
        final List<HolderQueueTableDO> holderQueueTableDOS = Arrays.asList(holderQueueTableDO);
        when(mockQueueTableMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(holderQueueTableDOS);

        // Run the test
        final List<TreeTableDTO> result = queueServiceImplUnderTest.allTables("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testAllTables_QueueTableMapperReturnsNoItems() {
        // Setup
        final TreeTableDTO treeTableDTO = new TreeTableDTO();
        treeTableDTO.setTableGuid("tableGuid");
        treeTableDTO.setAreaGuid("areaGuid");
        treeTableDTO.setAreaName("areaName");
        treeTableDTO.setChildren(Arrays.asList(new TreeTableDTO()));
        treeTableDTO.setQueueGuid("queueGuid");
        final List<TreeTableDTO> expectedResult = Arrays.asList(treeTableDTO);

        // Configure TableClientService.listByWeb(...).
        final TableBasicDTO tableBasicDTO = new TableBasicDTO();
        tableBasicDTO.setGuid("bd2aef79-6529-4a41-bc41-a835de2aee58");
        tableBasicDTO.setStoreGuid("storeGuid");
        tableBasicDTO.setStoreName("storeName");
        tableBasicDTO.setAreaGuid("areaGuid");
        tableBasicDTO.setSort(0);
        final List<TableBasicDTO> tableBasicDTOS = Arrays.asList(tableBasicDTO);
        final TableBasicQueryDTO tableBasicQueryDTO = new TableBasicQueryDTO();
        tableBasicQueryDTO.setStoreGuid("storeGuid");
        tableBasicQueryDTO.setAreaGuid("areaGuid");
        tableBasicQueryDTO.setTableGuidList(Arrays.asList("value"));
        when(mockTableClientService.listByWeb(tableBasicQueryDTO)).thenReturn(tableBasicDTOS);

        // Configure QueueMapStruct.toDto(...).
        final TreeTableDTO treeTableDTO1 = new TreeTableDTO();
        treeTableDTO1.setTableGuid("tableGuid");
        treeTableDTO1.setAreaGuid("areaGuid");
        treeTableDTO1.setAreaName("areaName");
        treeTableDTO1.setChildren(Arrays.asList(new TreeTableDTO()));
        treeTableDTO1.setQueueGuid("queueGuid");
        final TableBasicDTO tableBasicDTO1 = new TableBasicDTO();
        tableBasicDTO1.setGuid("bd2aef79-6529-4a41-bc41-a835de2aee58");
        tableBasicDTO1.setStoreGuid("storeGuid");
        tableBasicDTO1.setStoreName("storeName");
        tableBasicDTO1.setAreaGuid("areaGuid");
        tableBasicDTO1.setSort(0);
        when(mockQueueMapStruct.toDto(tableBasicDTO1)).thenReturn(treeTableDTO1);

        when(mockQueueTableMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<TreeTableDTO> result = queueServiceImplUnderTest.allTables("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
