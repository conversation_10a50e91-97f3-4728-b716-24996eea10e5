package com.holderzone.saas.store.business.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.business.entity.domain.SurchargeAreaDO;
import com.holderzone.saas.store.business.entity.domain.SurchargeDO;
import com.holderzone.saas.store.business.mapstruct.SurchargeMapstruct;
import com.holderzone.saas.store.business.service.RedisService;
import com.holderzone.saas.store.business.service.SurchargeAreaService;
import com.holderzone.saas.store.business.service.client.MessageService;
import com.holderzone.saas.store.business.service.client.TableRpcService;
import com.holderzone.saas.store.dto.business.manage.*;
import com.holderzone.saas.store.dto.business.manage.sync.*;
import com.holderzone.saas.store.dto.message.BusinessMessageDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class SurchargeServiceImplTest {

    @Mock
    private SurchargeAreaService mockSurchargeAreaService;
    @Mock
    private RedisService mockRedisService;
    @Mock
    private SurchargeMapstruct mockSurchargeMapstruct;
    @Mock
    private MessageService mockMessageService;
    @Mock
    private TableRpcService mockTableRpcService;

    private SurchargeServiceImpl surchargeServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        surchargeServiceImplUnderTest = new SurchargeServiceImpl(mockSurchargeAreaService, mockRedisService,
                mockSurchargeMapstruct, mockMessageService, mockTableRpcService);
    }

    @Test
    public void testAddSurcharge() {
        // Setup
        final SurchargeCreateDTO surchargeCreateDTO = new SurchargeCreateDTO();
        surchargeCreateDTO.setSurchargeGuid("surchargeGuid");
        surchargeCreateDTO.setStoreGuid("storeGuid");
        surchargeCreateDTO.setName("name");
        surchargeCreateDTO.setTradeModes(Arrays.asList("value"));
        surchargeCreateDTO.setAreaGuidList(Arrays.asList("value"));

        when(mockRedisService.nextSurchargeGuid()).thenReturn("surchargeGuid");

        // Configure SurchargeMapstruct.fromUpdate(...).
        final SurchargeDO surchargeDO = new SurchargeDO();
        surchargeDO.setSurchargeGuid("surchargeGuid");
        surchargeDO.setStoreGuid("storeGuid");
        surchargeDO.setName("name");
        surchargeDO.setAmount(new BigDecimal("0.00"));
        surchargeDO.setType(0);
        surchargeDO.setTradeMode("tradeMode");
        surchargeDO.setEffectiveTime(0);
        surchargeDO.setIsEnable(false);
        final SurchargeCreateDTO surchargeCreateDTO1 = new SurchargeCreateDTO();
        surchargeCreateDTO1.setSurchargeGuid("surchargeGuid");
        surchargeCreateDTO1.setStoreGuid("storeGuid");
        surchargeCreateDTO1.setName("name");
        surchargeCreateDTO1.setTradeModes(Arrays.asList("value"));
        surchargeCreateDTO1.setAreaGuidList(Arrays.asList("value"));
        when(mockSurchargeMapstruct.fromUpdate(surchargeCreateDTO1)).thenReturn(surchargeDO);

        // Run the test
        final String result = surchargeServiceImplUnderTest.addSurcharge(surchargeCreateDTO);

        // Verify the results
        assertThat(result).isEqualTo("surchargeGuid");

        // Confirm SurchargeAreaService.bindSurchargeArea(...).
        final SurchargeCreateDTO surchargeCreateDTO2 = new SurchargeCreateDTO();
        surchargeCreateDTO2.setSurchargeGuid("surchargeGuid");
        surchargeCreateDTO2.setStoreGuid("storeGuid");
        surchargeCreateDTO2.setName("name");
        surchargeCreateDTO2.setTradeModes(Arrays.asList("value"));
        surchargeCreateDTO2.setAreaGuidList(Arrays.asList("value"));
        verify(mockSurchargeAreaService).bindSurchargeArea(surchargeCreateDTO2);
    }

    @Test
    public void testQuerySurcharge() {
        // Setup
        final SurchargeQueryDTO surchargeQueryDTO = new SurchargeQueryDTO("surchargeGuid");
        final SurchargeDTO expectedResult = new SurchargeDTO();
        expectedResult.setStoreGuid("storeGuid");
        final SurchargeAreaDTO surchargeAreaDTO = new SurchargeAreaDTO();
        expectedResult.setAreaList(Arrays.asList(surchargeAreaDTO));
        expectedResult.setTradeMode("tradeMode");
        expectedResult.setTradeModes(Arrays.asList("value"));
        expectedResult.setEffectiveTime(0);

        // Configure SurchargeMapstruct.toRespDTO(...).
        final SurchargeDTO surchargeDTO = new SurchargeDTO();
        surchargeDTO.setStoreGuid("storeGuid");
        final SurchargeAreaDTO surchargeAreaDTO1 = new SurchargeAreaDTO();
        surchargeDTO.setAreaList(Arrays.asList(surchargeAreaDTO1));
        surchargeDTO.setTradeMode("tradeMode");
        surchargeDTO.setTradeModes(Arrays.asList("value"));
        surchargeDTO.setEffectiveTime(0);
        final SurchargeDO surchargeDO = new SurchargeDO();
        surchargeDO.setSurchargeGuid("surchargeGuid");
        surchargeDO.setStoreGuid("storeGuid");
        surchargeDO.setName("name");
        surchargeDO.setAmount(new BigDecimal("0.00"));
        surchargeDO.setType(0);
        surchargeDO.setTradeMode("tradeMode");
        surchargeDO.setEffectiveTime(0);
        surchargeDO.setIsEnable(false);
        when(mockSurchargeMapstruct.toRespDTO(surchargeDO)).thenReturn(surchargeDTO);

        // Configure SurchargeAreaService.findSurchargeArea(...).
        final List<SurchargeAreaDTO> surchargeAreaDTOS = Arrays.asList(
                new SurchargeAreaDTO("3d3294fa-ddad-4f14-a7fd-3fef735cd738", "areaName", false));
        when(mockSurchargeAreaService.findSurchargeArea("surchargeGuid", "storeGuid")).thenReturn(surchargeAreaDTOS);

        // Run the test
        final SurchargeDTO result = surchargeServiceImplUnderTest.querySurcharge(surchargeQueryDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQuerySurcharge_SurchargeAreaServiceReturnsNoItems() {
        // Setup
        final SurchargeQueryDTO surchargeQueryDTO = new SurchargeQueryDTO("surchargeGuid");
        final SurchargeDTO expectedResult = new SurchargeDTO();
        expectedResult.setStoreGuid("storeGuid");
        final SurchargeAreaDTO surchargeAreaDTO = new SurchargeAreaDTO();
        expectedResult.setAreaList(Arrays.asList(surchargeAreaDTO));
        expectedResult.setTradeMode("tradeMode");
        expectedResult.setTradeModes(Arrays.asList("value"));
        expectedResult.setEffectiveTime(0);

        // Configure SurchargeMapstruct.toRespDTO(...).
        final SurchargeDTO surchargeDTO = new SurchargeDTO();
        surchargeDTO.setStoreGuid("storeGuid");
        final SurchargeAreaDTO surchargeAreaDTO1 = new SurchargeAreaDTO();
        surchargeDTO.setAreaList(Arrays.asList(surchargeAreaDTO1));
        surchargeDTO.setTradeMode("tradeMode");
        surchargeDTO.setTradeModes(Arrays.asList("value"));
        surchargeDTO.setEffectiveTime(0);
        final SurchargeDO surchargeDO = new SurchargeDO();
        surchargeDO.setSurchargeGuid("surchargeGuid");
        surchargeDO.setStoreGuid("storeGuid");
        surchargeDO.setName("name");
        surchargeDO.setAmount(new BigDecimal("0.00"));
        surchargeDO.setType(0);
        surchargeDO.setTradeMode("tradeMode");
        surchargeDO.setEffectiveTime(0);
        surchargeDO.setIsEnable(false);
        when(mockSurchargeMapstruct.toRespDTO(surchargeDO)).thenReturn(surchargeDTO);

        when(mockSurchargeAreaService.findSurchargeArea("surchargeGuid", "storeGuid"))
                .thenReturn(Collections.emptyList());

        // Run the test
        final SurchargeDTO result = surchargeServiceImplUnderTest.querySurcharge(surchargeQueryDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testUpdateSurcharge() {
        // Setup
        final SurchargeUpdateDTO surchargeUpdateDTO = new SurchargeUpdateDTO();
        surchargeUpdateDTO.setSurchargeGuid("surchargeGuid");
        surchargeUpdateDTO.setStoreGuid("storeGuid");
        surchargeUpdateDTO.setName("name");
        surchargeUpdateDTO.setTradeModes(Arrays.asList("value"));
        surchargeUpdateDTO.setAreaGuidList(Arrays.asList("value"));

        // Configure SurchargeMapstruct.fromUpdate(...).
        final SurchargeDO surchargeDO = new SurchargeDO();
        surchargeDO.setSurchargeGuid("surchargeGuid");
        surchargeDO.setStoreGuid("storeGuid");
        surchargeDO.setName("name");
        surchargeDO.setAmount(new BigDecimal("0.00"));
        surchargeDO.setType(0);
        surchargeDO.setTradeMode("tradeMode");
        surchargeDO.setEffectiveTime(0);
        surchargeDO.setIsEnable(false);
        final SurchargeUpdateDTO surchargeUpdateDTO1 = new SurchargeUpdateDTO();
        surchargeUpdateDTO1.setSurchargeGuid("surchargeGuid");
        surchargeUpdateDTO1.setStoreGuid("storeGuid");
        surchargeUpdateDTO1.setName("name");
        surchargeUpdateDTO1.setTradeModes(Arrays.asList("value"));
        surchargeUpdateDTO1.setAreaGuidList(Arrays.asList("value"));
        when(mockSurchargeMapstruct.fromUpdate(surchargeUpdateDTO1)).thenReturn(surchargeDO);

        // Run the test
        surchargeServiceImplUnderTest.updateSurcharge(surchargeUpdateDTO);

        // Verify the results
        // Confirm SurchargeAreaService.rebindSurchargeArea(...).
        final SurchargeUpdateDTO surchargeUpdateDTO2 = new SurchargeUpdateDTO();
        surchargeUpdateDTO2.setSurchargeGuid("surchargeGuid");
        surchargeUpdateDTO2.setStoreGuid("storeGuid");
        surchargeUpdateDTO2.setName("name");
        surchargeUpdateDTO2.setTradeModes(Arrays.asList("value"));
        surchargeUpdateDTO2.setAreaGuidList(Arrays.asList("value"));
        verify(mockSurchargeAreaService).rebindSurchargeArea(surchargeUpdateDTO2);

        // Confirm MessageService.msg(...).
        final BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setSubject("name");
        businessMessageDTO.setContent("附加费修改信息");
        businessMessageDTO.setMessageType(0);
        businessMessageDTO.setDetailMessageType(0);
        businessMessageDTO.setPlatform("2");
        businessMessageDTO.setStoreGuid("storeGuid");
        verify(mockMessageService).msg(businessMessageDTO);
    }

    @Test
    public void testEnableSurcharge() {
        // Setup
        final SurchargeEnableDTO surchargeEnableDTO = new SurchargeEnableDTO("surchargeGuid", false);

        // Run the test
        surchargeServiceImplUnderTest.enableSurcharge(surchargeEnableDTO);

        // Verify the results
    }

    @Test
    public void testDeleteSurcharge() {
        // Setup
        final SurchargeDeleteDTO surchargeDeleteDTO = new SurchargeDeleteDTO("surchargeGuid");

        // Run the test
        surchargeServiceImplUnderTest.deleteSurcharge(surchargeDeleteDTO);

        // Verify the results
        verify(mockSurchargeAreaService).removeSurchargeArea("surchargeGuid");
    }

    @Test
    public void testBatchEnableSurcharge() {
        // Setup
        final SurchargeBatchEnableDTO surchargeBatchEnableDTO = new SurchargeBatchEnableDTO(Arrays.asList("value"),
                false);

        // Run the test
        surchargeServiceImplUnderTest.batchEnableSurcharge(surchargeBatchEnableDTO);

        // Verify the results
    }

    @Test
    public void testBatchDeleteSurcharge() {
        // Setup
        final SurchargeBatchDeleteDTO surchargeBatchDeleteDTO = new SurchargeBatchDeleteDTO(Arrays.asList("value"));

        // Run the test
        surchargeServiceImplUnderTest.batchDeleteSurcharge(surchargeBatchDeleteDTO);

        // Verify the results
        verify(mockSurchargeAreaService).removeSurchargeArea(Arrays.asList("value"));
    }

    @Test
    public void testListByType() {
        // Setup
        final SurchargeListDTO surchargeListDTO = new SurchargeListDTO("storeGuid", 0);

        // Configure SurchargeMapstruct.toRespDTO(...).
        final SurchargeDTO surchargeDTO = new SurchargeDTO();
        surchargeDTO.setStoreGuid("storeGuid");
        final SurchargeAreaDTO surchargeAreaDTO = new SurchargeAreaDTO();
        surchargeDTO.setAreaList(Arrays.asList(surchargeAreaDTO));
        surchargeDTO.setTradeMode("tradeMode");
        surchargeDTO.setTradeModes(Arrays.asList("value"));
        surchargeDTO.setEffectiveTime(0);
        final List<SurchargeDTO> surchargeDTOS = Arrays.asList(surchargeDTO);
        final SurchargeDO surchargeDO = new SurchargeDO();
        surchargeDO.setSurchargeGuid("surchargeGuid");
        surchargeDO.setStoreGuid("storeGuid");
        surchargeDO.setName("name");
        surchargeDO.setAmount(new BigDecimal("0.00"));
        surchargeDO.setType(0);
        surchargeDO.setTradeMode("tradeMode");
        surchargeDO.setEffectiveTime(0);
        surchargeDO.setIsEnable(false);
        final List<SurchargeDO> surchargeDOS = Arrays.asList(surchargeDO);
        when(mockSurchargeMapstruct.toRespDTO(surchargeDOS)).thenReturn(surchargeDTOS);

        // Run the test
        final Page<SurchargeDTO> result = surchargeServiceImplUnderTest.listByType(surchargeListDTO);

        // Verify the results
    }

    @Test
    public void testListByType_SurchargeMapstructReturnsNoItems() {
        // Setup
        final SurchargeListDTO surchargeListDTO = new SurchargeListDTO("storeGuid", 0);

        // Configure SurchargeMapstruct.toRespDTO(...).
        final SurchargeDO surchargeDO = new SurchargeDO();
        surchargeDO.setSurchargeGuid("surchargeGuid");
        surchargeDO.setStoreGuid("storeGuid");
        surchargeDO.setName("name");
        surchargeDO.setAmount(new BigDecimal("0.00"));
        surchargeDO.setType(0);
        surchargeDO.setTradeMode("tradeMode");
        surchargeDO.setEffectiveTime(0);
        surchargeDO.setIsEnable(false);
        final List<SurchargeDO> surchargeDOS = Arrays.asList(surchargeDO);
        when(mockSurchargeMapstruct.toRespDTO(surchargeDOS)).thenReturn(Collections.emptyList());

        // Run the test
        final Page<SurchargeDTO> result = surchargeServiceImplUnderTest.listByType(surchargeListDTO);

        // Verify the results
    }

    @Test
    public void testListByAreaGuid() {
        // Setup
        final Map<String, List<SurchargeLinkDTO>> expectedResult = new HashMap<>();

        // Configure SurchargeAreaService.findAreaSurcharge(...).
        final SurchargeAreaDO surchargeAreaDO = new SurchargeAreaDO();
        surchargeAreaDO.setId(0L);
        surchargeAreaDO.setGuid("4787ca52-34dd-4648-a62b-98338ff0b2f5");
        surchargeAreaDO.setSurchargeGuid("surchargeGuid");
        surchargeAreaDO.setAreaGuid("areaGuid");
        surchargeAreaDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<SurchargeAreaDO> surchargeAreaDOS = Arrays.asList(surchargeAreaDO);
        when(mockSurchargeAreaService.findAreaSurcharge(Arrays.asList("value"))).thenReturn(surchargeAreaDOS);

        // Run the test
        final Map<String, List<SurchargeLinkDTO>> result = surchargeServiceImplUnderTest.listByAreaGuid(
                Arrays.asList("value"), 0);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testListByAreaGuid_SurchargeAreaServiceReturnsNoItems() {
        // Setup
        final Map<String, List<SurchargeLinkDTO>> expectedResult = new HashMap<>();
        when(mockSurchargeAreaService.findAreaSurcharge(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        final Map<String, List<SurchargeLinkDTO>> result = surchargeServiceImplUnderTest.listByAreaGuid(
                Arrays.asList("value"), 0);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testSync() {
        // Setup
        final SurchargeSyncDTO surchargeDTO = new SurchargeSyncDTO("storeGuid");
        final SurchargeAggDTO expectedResult = new SurchargeAggDTO();
        final SurchargeRawDTO surchargeRawDTO = new SurchargeRawDTO();
        surchargeRawDTO.setId(0L);
        surchargeRawDTO.setSurchargeGuid("surchargeGuid");
        surchargeRawDTO.setStoreGuid("storeGuid");
        surchargeRawDTO.setName("name");
        expectedResult.setSurchargeList(Arrays.asList(surchargeRawDTO));

        // Configure SurchargeAreaService.list(...).
        final SurchargeAreaDO surchargeAreaDO = new SurchargeAreaDO();
        surchargeAreaDO.setId(0L);
        surchargeAreaDO.setGuid("4787ca52-34dd-4648-a62b-98338ff0b2f5");
        surchargeAreaDO.setSurchargeGuid("surchargeGuid");
        surchargeAreaDO.setAreaGuid("areaGuid");
        surchargeAreaDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<SurchargeAreaDO> surchargeAreaDOS = Arrays.asList(surchargeAreaDO);
        when(mockSurchargeAreaService.list(any(LambdaQueryWrapper.class))).thenReturn(surchargeAreaDOS);

        // Configure SurchargeMapstruct.toRaw(...).
        final SurchargeRawDTO surchargeRawDTO1 = new SurchargeRawDTO();
        surchargeRawDTO1.setId(0L);
        surchargeRawDTO1.setSurchargeGuid("surchargeGuid");
        surchargeRawDTO1.setStoreGuid("storeGuid");
        surchargeRawDTO1.setName("name");
        surchargeRawDTO1.setAmount(new BigDecimal("0.00"));
        final List<SurchargeRawDTO> surchargeRawDTOS = Arrays.asList(surchargeRawDTO1);
        final SurchargeDO surchargeDO1 = new SurchargeDO();
        surchargeDO1.setSurchargeGuid("surchargeGuid");
        surchargeDO1.setStoreGuid("storeGuid");
        surchargeDO1.setName("name");
        surchargeDO1.setAmount(new BigDecimal("0.00"));
        surchargeDO1.setType(0);
        surchargeDO1.setTradeMode("tradeMode");
        surchargeDO1.setEffectiveTime(0);
        surchargeDO1.setIsEnable(false);
        final List<SurchargeDO> surchargeDO = Arrays.asList(surchargeDO1);
        when(mockSurchargeMapstruct.toRaw(surchargeDO)).thenReturn(surchargeRawDTOS);

        // Configure SurchargeMapstruct.toAreaRaw(...).
        final SurchargeAreaRawDTO surchargeAreaRawDTO = new SurchargeAreaRawDTO();
        surchargeAreaRawDTO.setId(0L);
        surchargeAreaRawDTO.setGuid("87bf24de-9b8e-4d9c-9159-54b9b4c09740");
        surchargeAreaRawDTO.setSurchargeGuid("surchargeGuid");
        surchargeAreaRawDTO.setAreaGuid("areaGuid");
        surchargeAreaRawDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<SurchargeAreaRawDTO> surchargeAreaRawDTOS = Arrays.asList(surchargeAreaRawDTO);
        final SurchargeAreaDO surchargeAreaDO2 = new SurchargeAreaDO();
        surchargeAreaDO2.setId(0L);
        surchargeAreaDO2.setGuid("4787ca52-34dd-4648-a62b-98338ff0b2f5");
        surchargeAreaDO2.setSurchargeGuid("surchargeGuid");
        surchargeAreaDO2.setAreaGuid("areaGuid");
        surchargeAreaDO2.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<SurchargeAreaDO> surchargeAreaDO1 = Arrays.asList(surchargeAreaDO2);
        when(mockSurchargeMapstruct.toAreaRaw(surchargeAreaDO1)).thenReturn(surchargeAreaRawDTOS);

        // Run the test
        final SurchargeAggDTO result = surchargeServiceImplUnderTest.sync(surchargeDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testSync_SurchargeAreaServiceReturnsNoItems() {
        // Setup
        final SurchargeSyncDTO surchargeDTO = new SurchargeSyncDTO("storeGuid");
        final SurchargeAggDTO expectedResult = new SurchargeAggDTO();
        final SurchargeRawDTO surchargeRawDTO = new SurchargeRawDTO();
        surchargeRawDTO.setId(0L);
        surchargeRawDTO.setSurchargeGuid("surchargeGuid");
        surchargeRawDTO.setStoreGuid("storeGuid");
        surchargeRawDTO.setName("name");
        expectedResult.setSurchargeList(Arrays.asList(surchargeRawDTO));

        when(mockSurchargeAreaService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure SurchargeMapstruct.toRaw(...).
        final SurchargeRawDTO surchargeRawDTO1 = new SurchargeRawDTO();
        surchargeRawDTO1.setId(0L);
        surchargeRawDTO1.setSurchargeGuid("surchargeGuid");
        surchargeRawDTO1.setStoreGuid("storeGuid");
        surchargeRawDTO1.setName("name");
        surchargeRawDTO1.setAmount(new BigDecimal("0.00"));
        final List<SurchargeRawDTO> surchargeRawDTOS = Arrays.asList(surchargeRawDTO1);
        final SurchargeDO surchargeDO1 = new SurchargeDO();
        surchargeDO1.setSurchargeGuid("surchargeGuid");
        surchargeDO1.setStoreGuid("storeGuid");
        surchargeDO1.setName("name");
        surchargeDO1.setAmount(new BigDecimal("0.00"));
        surchargeDO1.setType(0);
        surchargeDO1.setTradeMode("tradeMode");
        surchargeDO1.setEffectiveTime(0);
        surchargeDO1.setIsEnable(false);
        final List<SurchargeDO> surchargeDO = Arrays.asList(surchargeDO1);
        when(mockSurchargeMapstruct.toRaw(surchargeDO)).thenReturn(surchargeRawDTOS);

        // Configure SurchargeMapstruct.toAreaRaw(...).
        final SurchargeAreaRawDTO surchargeAreaRawDTO = new SurchargeAreaRawDTO();
        surchargeAreaRawDTO.setId(0L);
        surchargeAreaRawDTO.setGuid("87bf24de-9b8e-4d9c-9159-54b9b4c09740");
        surchargeAreaRawDTO.setSurchargeGuid("surchargeGuid");
        surchargeAreaRawDTO.setAreaGuid("areaGuid");
        surchargeAreaRawDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<SurchargeAreaRawDTO> surchargeAreaRawDTOS = Arrays.asList(surchargeAreaRawDTO);
        final SurchargeAreaDO surchargeAreaDO1 = new SurchargeAreaDO();
        surchargeAreaDO1.setId(0L);
        surchargeAreaDO1.setGuid("4787ca52-34dd-4648-a62b-98338ff0b2f5");
        surchargeAreaDO1.setSurchargeGuid("surchargeGuid");
        surchargeAreaDO1.setAreaGuid("areaGuid");
        surchargeAreaDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<SurchargeAreaDO> surchargeAreaDO = Arrays.asList(surchargeAreaDO1);
        when(mockSurchargeMapstruct.toAreaRaw(surchargeAreaDO)).thenReturn(surchargeAreaRawDTOS);

        // Run the test
        final SurchargeAggDTO result = surchargeServiceImplUnderTest.sync(surchargeDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testSync_SurchargeMapstructToRawReturnsNoItems() {
        // Setup
        final SurchargeSyncDTO surchargeDTO = new SurchargeSyncDTO("storeGuid");
        final SurchargeAggDTO expectedResult = new SurchargeAggDTO();
        final SurchargeRawDTO surchargeRawDTO = new SurchargeRawDTO();
        surchargeRawDTO.setId(0L);
        surchargeRawDTO.setSurchargeGuid("surchargeGuid");
        surchargeRawDTO.setStoreGuid("storeGuid");
        surchargeRawDTO.setName("name");
        expectedResult.setSurchargeList(Arrays.asList(surchargeRawDTO));

        // Configure SurchargeAreaService.list(...).
        final SurchargeAreaDO surchargeAreaDO = new SurchargeAreaDO();
        surchargeAreaDO.setId(0L);
        surchargeAreaDO.setGuid("4787ca52-34dd-4648-a62b-98338ff0b2f5");
        surchargeAreaDO.setSurchargeGuid("surchargeGuid");
        surchargeAreaDO.setAreaGuid("areaGuid");
        surchargeAreaDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<SurchargeAreaDO> surchargeAreaDOS = Arrays.asList(surchargeAreaDO);
        when(mockSurchargeAreaService.list(any(LambdaQueryWrapper.class))).thenReturn(surchargeAreaDOS);

        // Configure SurchargeMapstruct.toRaw(...).
        final SurchargeDO surchargeDO1 = new SurchargeDO();
        surchargeDO1.setSurchargeGuid("surchargeGuid");
        surchargeDO1.setStoreGuid("storeGuid");
        surchargeDO1.setName("name");
        surchargeDO1.setAmount(new BigDecimal("0.00"));
        surchargeDO1.setType(0);
        surchargeDO1.setTradeMode("tradeMode");
        surchargeDO1.setEffectiveTime(0);
        surchargeDO1.setIsEnable(false);
        final List<SurchargeDO> surchargeDO = Arrays.asList(surchargeDO1);
        when(mockSurchargeMapstruct.toRaw(surchargeDO)).thenReturn(Collections.emptyList());

        // Configure SurchargeMapstruct.toAreaRaw(...).
        final SurchargeAreaRawDTO surchargeAreaRawDTO = new SurchargeAreaRawDTO();
        surchargeAreaRawDTO.setId(0L);
        surchargeAreaRawDTO.setGuid("87bf24de-9b8e-4d9c-9159-54b9b4c09740");
        surchargeAreaRawDTO.setSurchargeGuid("surchargeGuid");
        surchargeAreaRawDTO.setAreaGuid("areaGuid");
        surchargeAreaRawDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<SurchargeAreaRawDTO> surchargeAreaRawDTOS = Arrays.asList(surchargeAreaRawDTO);
        final SurchargeAreaDO surchargeAreaDO2 = new SurchargeAreaDO();
        surchargeAreaDO2.setId(0L);
        surchargeAreaDO2.setGuid("4787ca52-34dd-4648-a62b-98338ff0b2f5");
        surchargeAreaDO2.setSurchargeGuid("surchargeGuid");
        surchargeAreaDO2.setAreaGuid("areaGuid");
        surchargeAreaDO2.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<SurchargeAreaDO> surchargeAreaDO1 = Arrays.asList(surchargeAreaDO2);
        when(mockSurchargeMapstruct.toAreaRaw(surchargeAreaDO1)).thenReturn(surchargeAreaRawDTOS);

        // Run the test
        final SurchargeAggDTO result = surchargeServiceImplUnderTest.sync(surchargeDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testSync_SurchargeMapstructToAreaRawReturnsNoItems() {
        // Setup
        final SurchargeSyncDTO surchargeDTO = new SurchargeSyncDTO("storeGuid");
        final SurchargeAggDTO expectedResult = new SurchargeAggDTO();
        final SurchargeRawDTO surchargeRawDTO = new SurchargeRawDTO();
        surchargeRawDTO.setId(0L);
        surchargeRawDTO.setSurchargeGuid("surchargeGuid");
        surchargeRawDTO.setStoreGuid("storeGuid");
        surchargeRawDTO.setName("name");
        expectedResult.setSurchargeList(Arrays.asList(surchargeRawDTO));

        // Configure SurchargeAreaService.list(...).
        final SurchargeAreaDO surchargeAreaDO = new SurchargeAreaDO();
        surchargeAreaDO.setId(0L);
        surchargeAreaDO.setGuid("4787ca52-34dd-4648-a62b-98338ff0b2f5");
        surchargeAreaDO.setSurchargeGuid("surchargeGuid");
        surchargeAreaDO.setAreaGuid("areaGuid");
        surchargeAreaDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<SurchargeAreaDO> surchargeAreaDOS = Arrays.asList(surchargeAreaDO);
        when(mockSurchargeAreaService.list(any(LambdaQueryWrapper.class))).thenReturn(surchargeAreaDOS);

        // Configure SurchargeMapstruct.toRaw(...).
        final SurchargeRawDTO surchargeRawDTO1 = new SurchargeRawDTO();
        surchargeRawDTO1.setId(0L);
        surchargeRawDTO1.setSurchargeGuid("surchargeGuid");
        surchargeRawDTO1.setStoreGuid("storeGuid");
        surchargeRawDTO1.setName("name");
        surchargeRawDTO1.setAmount(new BigDecimal("0.00"));
        final List<SurchargeRawDTO> surchargeRawDTOS = Arrays.asList(surchargeRawDTO1);
        final SurchargeDO surchargeDO1 = new SurchargeDO();
        surchargeDO1.setSurchargeGuid("surchargeGuid");
        surchargeDO1.setStoreGuid("storeGuid");
        surchargeDO1.setName("name");
        surchargeDO1.setAmount(new BigDecimal("0.00"));
        surchargeDO1.setType(0);
        surchargeDO1.setTradeMode("tradeMode");
        surchargeDO1.setEffectiveTime(0);
        surchargeDO1.setIsEnable(false);
        final List<SurchargeDO> surchargeDO = Arrays.asList(surchargeDO1);
        when(mockSurchargeMapstruct.toRaw(surchargeDO)).thenReturn(surchargeRawDTOS);

        // Configure SurchargeMapstruct.toAreaRaw(...).
        final SurchargeAreaDO surchargeAreaDO2 = new SurchargeAreaDO();
        surchargeAreaDO2.setId(0L);
        surchargeAreaDO2.setGuid("4787ca52-34dd-4648-a62b-98338ff0b2f5");
        surchargeAreaDO2.setSurchargeGuid("surchargeGuid");
        surchargeAreaDO2.setAreaGuid("areaGuid");
        surchargeAreaDO2.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<SurchargeAreaDO> surchargeAreaDO1 = Arrays.asList(surchargeAreaDO2);
        when(mockSurchargeMapstruct.toAreaRaw(surchargeAreaDO1)).thenReturn(Collections.emptyList());

        // Run the test
        final SurchargeAggDTO result = surchargeServiceImplUnderTest.sync(surchargeDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCalculateSurcharge() {
        // Setup
        final SurchargeCalculateDTO surchargeDTO = new SurchargeCalculateDTO();
        surchargeDTO.setGuestCount(0);
        surchargeDTO.setTableGuid("tableGuid");
        surchargeDTO.setAreaGuid("areaGuid");
        surchargeDTO.setEnterpriseGuid("enterpriseGuid");
        surchargeDTO.setTradeMode(0);

        final SurchargeLinkDTO surchargeLinkDTO = new SurchargeLinkDTO();
        surchargeLinkDTO.setSurchargeGuid("surchargeGuid");
        surchargeLinkDTO.setAreaGuid("areaGuid");
        surchargeLinkDTO.setName("name");
        surchargeLinkDTO.setAmount(new BigDecimal("0.00"));
        surchargeLinkDTO.setType(0);
        surchargeLinkDTO.setTradeMode("tradeMode");
        surchargeLinkDTO.setEffectiveTime(0);
        final List<SurchargeLinkDTO> expectedResult = Arrays.asList(surchargeLinkDTO);
        when(mockTableRpcService.getAreaGuidByTableGuid("tableGuid")).thenReturn("result");

        // Configure SurchargeAreaService.findAreaSurcharge(...).
        final SurchargeAreaDO surchargeAreaDO = new SurchargeAreaDO();
        surchargeAreaDO.setId(0L);
        surchargeAreaDO.setGuid("4787ca52-34dd-4648-a62b-98338ff0b2f5");
        surchargeAreaDO.setSurchargeGuid("surchargeGuid");
        surchargeAreaDO.setAreaGuid("areaGuid");
        surchargeAreaDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<SurchargeAreaDO> surchargeAreaDOS = Arrays.asList(surchargeAreaDO);
        when(mockSurchargeAreaService.findAreaSurcharge(Arrays.asList("value"))).thenReturn(surchargeAreaDOS);

        // Run the test
        final List<SurchargeLinkDTO> result = surchargeServiceImplUnderTest.calculateSurcharge(surchargeDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCalculateSurcharge_SurchargeAreaServiceReturnsNoItems() {
        // Setup
        final SurchargeCalculateDTO surchargeDTO = new SurchargeCalculateDTO();
        surchargeDTO.setGuestCount(0);
        surchargeDTO.setTableGuid("tableGuid");
        surchargeDTO.setAreaGuid("areaGuid");
        surchargeDTO.setEnterpriseGuid("enterpriseGuid");
        surchargeDTO.setTradeMode(0);

        when(mockTableRpcService.getAreaGuidByTableGuid("tableGuid")).thenReturn("result");
        when(mockSurchargeAreaService.findAreaSurcharge(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        final List<SurchargeLinkDTO> result = surchargeServiceImplUnderTest.calculateSurcharge(surchargeDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testListByCondition() {
        // Setup
        final SurchargeConditionQuery query = new SurchargeConditionQuery();
        query.setStoreGuid("storeGuid");
        query.setType(0);
        query.setTradeMode(0);
        query.setAreaGuid("areaGuid");

        final SurchargeLinkDTO surchargeLinkDTO = new SurchargeLinkDTO();
        surchargeLinkDTO.setSurchargeGuid("surchargeGuid");
        surchargeLinkDTO.setAreaGuid("areaGuid");
        surchargeLinkDTO.setName("name");
        surchargeLinkDTO.setAmount(new BigDecimal("0.00"));
        surchargeLinkDTO.setType(0);
        surchargeLinkDTO.setTradeMode("tradeMode");
        surchargeLinkDTO.setEffectiveTime(0);
        final List<SurchargeLinkDTO> expectedResult = Arrays.asList(surchargeLinkDTO);

        // Configure SurchargeAreaService.findAreaSurchargeBySurchargeGuids(...).
        final SurchargeAreaDO surchargeAreaDO = new SurchargeAreaDO();
        surchargeAreaDO.setId(0L);
        surchargeAreaDO.setGuid("4787ca52-34dd-4648-a62b-98338ff0b2f5");
        surchargeAreaDO.setSurchargeGuid("surchargeGuid");
        surchargeAreaDO.setAreaGuid("areaGuid");
        surchargeAreaDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<SurchargeAreaDO> surchargeAreaDOS = Arrays.asList(surchargeAreaDO);
        when(mockSurchargeAreaService.findAreaSurchargeBySurchargeGuids(Arrays.asList("value")))
                .thenReturn(surchargeAreaDOS);

        // Run the test
        final List<SurchargeLinkDTO> result = surchargeServiceImplUnderTest.listByCondition(query);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testListByCondition_SurchargeAreaServiceReturnsNoItems() {
        // Setup
        final SurchargeConditionQuery query = new SurchargeConditionQuery();
        query.setStoreGuid("storeGuid");
        query.setType(0);
        query.setTradeMode(0);
        query.setAreaGuid("areaGuid");

        when(mockSurchargeAreaService.findAreaSurchargeBySurchargeGuids(Arrays.asList("value")))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<SurchargeLinkDTO> result = surchargeServiceImplUnderTest.listByCondition(query);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }
}
