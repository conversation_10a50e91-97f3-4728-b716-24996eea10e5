package com.holderzone.saas.store.business.queue.service.impl;

import com.holderzone.saas.store.business.queue.domain.HolderQueueDO;
import com.holderzone.saas.store.business.queue.helper.DynamicHelper;
import com.holderzone.saas.store.business.queue.mapper.QueueMapper;
import com.holderzone.saas.store.business.queue.mapstruct.QueueItemMapStruct;
import com.holderzone.saas.store.business.queue.mapstruct.QueueMapStruct;
import com.holderzone.saas.store.business.queue.service.QueueConfigService;
import com.holderzone.saas.store.business.queue.service.remote.*;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class RedisOrderQueueItemServiceTest {

    @Mock
    private QueueMapper mockQueueMapper;
    @Mock
    private QueueItemMapStruct mockQueueItemMapStruct;
    @Mock
    private BusinessMsgClient mockBusinessMsgClient;
    @Mock
    private PrintClient mockPrintClient;
    @Mock
    private OrganizationClientService mockOrganizationClientService;
    @Mock
    private TableClientService mockTableClientService;
    @Mock
    private WxClient mockWxClient;
    @Mock
    private QueueConfigService mockQueueConfigService;
    @Mock
    private QueueMapStruct mockQueueMapStruct;
    @Mock
    private RedisTemplate mockRedisTemplate;
    @Mock
    private DynamicHelper mockDynamicHelper;

    private RedisOrderQueueItemService redisOrderQueueItemServiceUnderTest;

    @Before
    public void setUp() {
        redisOrderQueueItemServiceUnderTest = new RedisOrderQueueItemService(mockQueueMapper, mockQueueItemMapStruct,
                mockBusinessMsgClient, mockPrintClient, mockOrganizationClientService, mockTableClientService,
                mockWxClient, mockQueueConfigService, mockQueueMapStruct, mockRedisTemplate, mockDynamicHelper);
    }

    @Test
    public void testOrder() {
        // Setup
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        final Integer result = redisOrderQueueItemServiceUnderTest.order("queueGuid");

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    public void testClean() {
        // Setup
        final HolderQueueDO holderQueueDO = new HolderQueueDO();
        holderQueueDO.setGuid("5d797353-7e70-48a0-a26d-30aa20a618e0");
        holderQueueDO.setStoreGuid("storeGuid");
        holderQueueDO.setCode("code");
        holderQueueDO.setMin((byte) 0b0);
        holderQueueDO.setMax((byte) 0b0);
        holderQueueDO.setIsEnable(false);
        final List<HolderQueueDO> list = Arrays.asList(holderQueueDO);

        // Run the test
        redisOrderQueueItemServiceUnderTest.clean(list);

        // Verify the results
        verify(mockRedisTemplate).delete(Arrays.asList("value"));
    }
}
