package com.holderzone.saas.store.business;

import com.alibaba.fastjson.JSON;
import com.holderzone.saas.store.dto.business.manage.StoreConfigCreateDTO;
import com.holderzone.saas.store.dto.business.manage.StoreConfigDTO;
import com.holderzone.saas.store.dto.business.manage.StoreConfigUpdateDTO;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.time.LocalTime;

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className StoreConfigMvcTest
 * @date 18-9-4 下午2:14
 * @description 门店配置相关接口测试
 * @program holder-saas-store-business
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class StoreConfigMvcTest {
    @Autowired
    private WebApplicationContext webApplicationContext;

    private MockMvc mockMvc;

    private String storeGuid;

    @Before
    public void setup() {
        this.mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        storeGuid = "8b9ed76b-4c9c-443b-8d0e-8a2d2c234511";
    }

    @Test
    public void testCreate() throws Exception {
        StoreConfigCreateDTO dto = new StoreConfigCreateDTO();
        dto.setStoreGuid(storeGuid);
        dto.setStoreName("测试门店4");
        dto.setBusinessStartTime(LocalTime.parse("01:00"));
        dto.setBusinessEndTime(LocalTime.parse("23:59"));
        String result = mockMvc.perform(MockMvcRequestBuilders.post("/storeConfig/create")
                                                              .accept(MediaType.APPLICATION_JSON_UTF8)
                                                              .contentType(MediaType.APPLICATION_JSON_UTF8)
                                                              .content(JSON.toJSONString(dto)))
                               .andExpect(status().isOk())
                               .andDo(MockMvcResultHandlers.print())
                               .andReturn()
                               .getResponse().getContentAsString();
    }

    @Test
    @Ignore
    public void testQuery() throws Exception {
        StoreConfigDTO dto = new StoreConfigDTO();
        dto.setStoreGuid(storeGuid);
        String result = mockMvc.perform(MockMvcRequestBuilders.post("/storeConfig/queryByAndroid")
                                                              .accept(MediaType.APPLICATION_JSON_UTF8)
                                                              .contentType(MediaType.APPLICATION_JSON_UTF8)
                                                              .content(JSON.toJSONString(dto)))
                               .andExpect(status().isOk())
                               .andDo(MockMvcResultHandlers.print())
                               .andReturn()
                               .getResponse().getContentAsString();
    }

    @Test
    @Ignore
    public void testUpdate() throws Exception {
        StoreConfigUpdateDTO dto = new StoreConfigUpdateDTO();
        dto.setStoreGuid(storeGuid);
        dto.setBusinessEndTime(LocalTime.parse("20:20:10"));
        String result = mockMvc.perform(MockMvcRequestBuilders.post("/storeConfig/update")
                                                              .accept(MediaType.APPLICATION_JSON_UTF8)
                                                              .contentType(MediaType.APPLICATION_JSON_UTF8)
                                                              .content(JSON.toJSONString(dto)))
                               .andExpect(status().isOk())
                               .andDo(MockMvcResultHandlers.print())
                               .andReturn()
                               .getResponse().getContentAsString();
    }

    @Test
    @Ignore
    public void testQueryAll() throws Exception {
        String result = mockMvc.perform(MockMvcRequestBuilders.get("/storeConfig/list")
                                                              .accept(MediaType.APPLICATION_JSON_UTF8)
                                                              .contentType(MediaType.APPLICATION_JSON_UTF8))
                               .andExpect(status().isOk())
                               .andDo(MockMvcResultHandlers.print())
                               .andReturn()
                               .getResponse().getContentAsString();
    }
}
