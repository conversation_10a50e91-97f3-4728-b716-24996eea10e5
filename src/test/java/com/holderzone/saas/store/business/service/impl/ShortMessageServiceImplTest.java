package com.holderzone.saas.store.business.service.impl;

import com.holderzone.saas.store.business.service.client.MarketClient;
import com.holderzone.saas.store.business.service.client.SmsEnterpriseClientService;
import com.holderzone.saas.store.business.service.client.SmsRechargeClient;
import com.holderzone.saas.store.dto.business.manage.*;
import com.holderzone.saas.store.dto.trade.ShortMsgConfigDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ShortMessageServiceImplTest {

    @Mock
    private SmsRechargeClient mockSmsRechargeClient;
    @Mock
    private MarketClient mockMarketClient;
    @Mock
    private SmsEnterpriseClientService mockSmsEnterpriseClientService;

    private ShortMessageServiceImpl shortMessageServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        shortMessageServiceImplUnderTest = new ShortMessageServiceImpl(mockSmsRechargeClient, mockMarketClient,
                mockSmsEnterpriseClientService);
    }

    @Test
    public void testGetAll() {
        // Setup
        final ChargeDTO chargeDTO = new ChargeDTO();
        chargeDTO.setChargeGuid("chargeGuid");
        chargeDTO.setProductGuid("productGuid");
        chargeDTO.setChargeType("chargeType");
        chargeDTO.setUnitPrice(new BigDecimal("0.00"));
        chargeDTO.setUnit(0);
        final List<ChargeDTO> expectedResult = Arrays.asList(chargeDTO);

        // Configure SmsRechargeClient.getChargeListForSMS(...).
        final ChargeDTO chargeDTO1 = new ChargeDTO();
        chargeDTO1.setChargeGuid("chargeGuid");
        chargeDTO1.setProductGuid("productGuid");
        chargeDTO1.setChargeType("chargeType");
        chargeDTO1.setUnitPrice(new BigDecimal("0.00"));
        chargeDTO1.setUnit(0);
        final List<ChargeDTO> chargeDTOS = Arrays.asList(chargeDTO1);
        when(mockSmsRechargeClient.getChargeListForSMS()).thenReturn(chargeDTOS);

        // Run the test
        final List<ChargeDTO> result = shortMessageServiceImplUnderTest.getAll();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetAll_SmsRechargeClientReturnsNoItems() {
        // Setup
        when(mockSmsRechargeClient.getChargeListForSMS()).thenReturn(Collections.emptyList());

        // Run the test
        final List<ChargeDTO> result = shortMessageServiceImplUnderTest.getAll();

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testCharge() {
        // Setup
        final ProductOrderDTO productOrderDTO = new ProductOrderDTO();
        productOrderDTO.setEnterpriseGuid("enterpriseGuid");
        productOrderDTO.setProductGuid("productGuid");
        productOrderDTO.setStaffAccount("staffAccount");
        productOrderDTO.setStaffGuid("staffGuid");
        productOrderDTO.setStaffName("staffName");

        final ShortMsgRespDTO expectedResult = new ShortMsgRespDTO();
        expectedResult.setCode("code");
        expectedResult.setMsg("msg");
        expectedResult.setPayGuid("payGuid");
        expectedResult.setPaymentGuid("paymentGuid");
        expectedResult.setCodeUrl("codeUrl");
        expectedResult.setPaySt("10");

        // Configure MarketClient.charge(...).
        final ProductJHPayRespDTO productJHPayRespDTO = new ProductJHPayRespDTO();
        productJHPayRespDTO.setCode("code");
        productJHPayRespDTO.setMsg("msg");
        productJHPayRespDTO.setResult("result");
        productJHPayRespDTO.setAttachData("attachData");
        final ProductOrderDTO productOrderDTO1 = new ProductOrderDTO();
        productOrderDTO1.setEnterpriseGuid("enterpriseGuid");
        productOrderDTO1.setProductGuid("productGuid");
        productOrderDTO1.setStaffAccount("staffAccount");
        productOrderDTO1.setStaffGuid("staffGuid");
        productOrderDTO1.setStaffName("staffName");
        when(mockMarketClient.charge(productOrderDTO1)).thenReturn(productJHPayRespDTO);

        // Configure MarketClient.polling(...).
        final ShortMsgPollingRespDTO shortMsgPollingRespDTO = new ShortMsgPollingRespDTO();
        shortMsgPollingRespDTO.setCode("code");
        shortMsgPollingRespDTO.setMsg("msg");
        shortMsgPollingRespDTO.setBody("body");
        shortMsgPollingRespDTO.setPaySt("10");
        shortMsgPollingRespDTO.setCodeUrl("codeUrl");
        final ShortMsgRespDTO shortMsgRespDTO = new ShortMsgRespDTO();
        shortMsgRespDTO.setCode("code");
        shortMsgRespDTO.setMsg("msg");
        shortMsgRespDTO.setPayGuid("payGuid");
        shortMsgRespDTO.setPaymentGuid("paymentGuid");
        shortMsgRespDTO.setCodeUrl("codeUrl");
        shortMsgRespDTO.setPaySt("10");
        when(mockMarketClient.polling(shortMsgRespDTO)).thenReturn(shortMsgPollingRespDTO);

        // Run the test
        final ShortMsgRespDTO result = shortMessageServiceImplUnderTest.charge(productOrderDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCharge_MarketClientChargeReturnsNull() {
        // Setup
        final ProductOrderDTO productOrderDTO = new ProductOrderDTO();
        productOrderDTO.setEnterpriseGuid("enterpriseGuid");
        productOrderDTO.setProductGuid("productGuid");
        productOrderDTO.setStaffAccount("staffAccount");
        productOrderDTO.setStaffGuid("staffGuid");
        productOrderDTO.setStaffName("staffName");

        final ShortMsgRespDTO expectedResult = new ShortMsgRespDTO();
        expectedResult.setCode("code");
        expectedResult.setMsg("msg");
        expectedResult.setPayGuid("payGuid");
        expectedResult.setPaymentGuid("paymentGuid");
        expectedResult.setCodeUrl("codeUrl");
        expectedResult.setPaySt("10");

        // Configure MarketClient.charge(...).
        final ProductOrderDTO productOrderDTO1 = new ProductOrderDTO();
        productOrderDTO1.setEnterpriseGuid("enterpriseGuid");
        productOrderDTO1.setProductGuid("productGuid");
        productOrderDTO1.setStaffAccount("staffAccount");
        productOrderDTO1.setStaffGuid("staffGuid");
        productOrderDTO1.setStaffName("staffName");
        when(mockMarketClient.charge(productOrderDTO1)).thenReturn(null);

        // Run the test
        final ShortMsgRespDTO result = shortMessageServiceImplUnderTest.charge(productOrderDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCharge_MarketClientPollingReturnsNull() {
        // Setup
        final ProductOrderDTO productOrderDTO = new ProductOrderDTO();
        productOrderDTO.setEnterpriseGuid("enterpriseGuid");
        productOrderDTO.setProductGuid("productGuid");
        productOrderDTO.setStaffAccount("staffAccount");
        productOrderDTO.setStaffGuid("staffGuid");
        productOrderDTO.setStaffName("staffName");

        final ShortMsgRespDTO expectedResult = new ShortMsgRespDTO();
        expectedResult.setCode("code");
        expectedResult.setMsg("msg");
        expectedResult.setPayGuid("payGuid");
        expectedResult.setPaymentGuid("paymentGuid");
        expectedResult.setCodeUrl("codeUrl");
        expectedResult.setPaySt("10");

        // Configure MarketClient.charge(...).
        final ProductJHPayRespDTO productJHPayRespDTO = new ProductJHPayRespDTO();
        productJHPayRespDTO.setCode("code");
        productJHPayRespDTO.setMsg("msg");
        productJHPayRespDTO.setResult("result");
        productJHPayRespDTO.setAttachData("attachData");
        final ProductOrderDTO productOrderDTO1 = new ProductOrderDTO();
        productOrderDTO1.setEnterpriseGuid("enterpriseGuid");
        productOrderDTO1.setProductGuid("productGuid");
        productOrderDTO1.setStaffAccount("staffAccount");
        productOrderDTO1.setStaffGuid("staffGuid");
        productOrderDTO1.setStaffName("staffName");
        when(mockMarketClient.charge(productOrderDTO1)).thenReturn(productJHPayRespDTO);

        // Configure MarketClient.polling(...).
        final ShortMsgRespDTO shortMsgRespDTO = new ShortMsgRespDTO();
        shortMsgRespDTO.setCode("code");
        shortMsgRespDTO.setMsg("msg");
        shortMsgRespDTO.setPayGuid("payGuid");
        shortMsgRespDTO.setPaymentGuid("paymentGuid");
        shortMsgRespDTO.setCodeUrl("codeUrl");
        shortMsgRespDTO.setPaySt("10");
        when(mockMarketClient.polling(shortMsgRespDTO)).thenReturn(null);

        // Run the test
        final ShortMsgRespDTO result = shortMessageServiceImplUnderTest.charge(productOrderDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testPolling() {
        // Setup
        final ShortMsgRespDTO shortMsgRespDTO = new ShortMsgRespDTO();
        shortMsgRespDTO.setCode("code");
        shortMsgRespDTO.setMsg("msg");
        shortMsgRespDTO.setPayGuid("payGuid");
        shortMsgRespDTO.setPaymentGuid("paymentGuid");
        shortMsgRespDTO.setCodeUrl("codeUrl");
        shortMsgRespDTO.setPaySt("10");

        final ShortMsgRespDTO expectedResult = new ShortMsgRespDTO();
        expectedResult.setCode("code");
        expectedResult.setMsg("msg");
        expectedResult.setPayGuid("payGuid");
        expectedResult.setPaymentGuid("paymentGuid");
        expectedResult.setCodeUrl("codeUrl");
        expectedResult.setPaySt("10");

        // Configure MarketClient.polling(...).
        final ShortMsgPollingRespDTO shortMsgPollingRespDTO = new ShortMsgPollingRespDTO();
        shortMsgPollingRespDTO.setCode("code");
        shortMsgPollingRespDTO.setMsg("msg");
        shortMsgPollingRespDTO.setBody("body");
        shortMsgPollingRespDTO.setPaySt("10");
        shortMsgPollingRespDTO.setCodeUrl("codeUrl");
        final ShortMsgRespDTO shortMsgRespDTO1 = new ShortMsgRespDTO();
        shortMsgRespDTO1.setCode("code");
        shortMsgRespDTO1.setMsg("msg");
        shortMsgRespDTO1.setPayGuid("payGuid");
        shortMsgRespDTO1.setPaymentGuid("paymentGuid");
        shortMsgRespDTO1.setCodeUrl("codeUrl");
        shortMsgRespDTO1.setPaySt("10");
        when(mockMarketClient.polling(shortMsgRespDTO1)).thenReturn(shortMsgPollingRespDTO);

        // Run the test
        final ShortMsgRespDTO result = shortMessageServiceImplUnderTest.polling(shortMsgRespDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testPolling_MarketClientReturnsNull() {
        // Setup
        final ShortMsgRespDTO shortMsgRespDTO = new ShortMsgRespDTO();
        shortMsgRespDTO.setCode("code");
        shortMsgRespDTO.setMsg("msg");
        shortMsgRespDTO.setPayGuid("payGuid");
        shortMsgRespDTO.setPaymentGuid("paymentGuid");
        shortMsgRespDTO.setCodeUrl("codeUrl");
        shortMsgRespDTO.setPaySt("10");

        // Configure MarketClient.polling(...).
        final ShortMsgRespDTO shortMsgRespDTO1 = new ShortMsgRespDTO();
        shortMsgRespDTO1.setCode("code");
        shortMsgRespDTO1.setMsg("msg");
        shortMsgRespDTO1.setPayGuid("payGuid");
        shortMsgRespDTO1.setPaymentGuid("paymentGuid");
        shortMsgRespDTO1.setCodeUrl("codeUrl");
        shortMsgRespDTO1.setPaySt("10");
        when(mockMarketClient.polling(shortMsgRespDTO1)).thenReturn(null);

        // Run the test
        final ShortMsgRespDTO result = shortMessageServiceImplUnderTest.polling(shortMsgRespDTO);

        // Verify the results
        assertThat(result).isNull();
    }

    @Test
    public void testQuery() throws Exception {
        // Setup
        final ShortMsgConfigDTO expectedResult = new ShortMsgConfigDTO();
        expectedResult.setAppreciateGuid("appreciateGuid");
        expectedResult.setAfterConsume(0);
        expectedResult.setAfterCharge(0);
        expectedResult.setEnterpriseGuid("enterpriseGuid");
        expectedResult.setResidueCount(0);

        // Configure SmsEnterpriseClientService.query(...).
        final ShortMsgConfigDTO shortMsgConfigDTO = new ShortMsgConfigDTO();
        shortMsgConfigDTO.setAppreciateGuid("appreciateGuid");
        shortMsgConfigDTO.setAfterConsume(0);
        shortMsgConfigDTO.setAfterCharge(0);
        shortMsgConfigDTO.setEnterpriseGuid("enterpriseGuid");
        shortMsgConfigDTO.setResidueCount(0);
        when(mockSmsEnterpriseClientService.query("enterpriseGuid")).thenReturn(shortMsgConfigDTO);

        // Run the test
        final ShortMsgConfigDTO result = shortMessageServiceImplUnderTest.query();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testUpdateMessageConfig() {
        // Setup
        final ShortMsgConfigDTO shortMsgConfigDTO = new ShortMsgConfigDTO();
        shortMsgConfigDTO.setAppreciateGuid("appreciateGuid");
        shortMsgConfigDTO.setAfterConsume(0);
        shortMsgConfigDTO.setAfterCharge(0);
        shortMsgConfigDTO.setEnterpriseGuid("enterpriseGuid");
        shortMsgConfigDTO.setResidueCount(0);

        // Configure SmsEnterpriseClientService.update(...).
        final ShortMsgConfigDTO shortMsgConfigDTO1 = new ShortMsgConfigDTO();
        shortMsgConfigDTO1.setAppreciateGuid("appreciateGuid");
        shortMsgConfigDTO1.setAfterConsume(0);
        shortMsgConfigDTO1.setAfterCharge(0);
        shortMsgConfigDTO1.setEnterpriseGuid("enterpriseGuid");
        shortMsgConfigDTO1.setResidueCount(0);
        when(mockSmsEnterpriseClientService.update(shortMsgConfigDTO1)).thenReturn(false);

        // Run the test
        final boolean result = shortMessageServiceImplUnderTest.updateMessageConfig(shortMsgConfigDTO);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testUpdateMessageConfig_SmsEnterpriseClientServiceReturnsTrue() {
        // Setup
        final ShortMsgConfigDTO shortMsgConfigDTO = new ShortMsgConfigDTO();
        shortMsgConfigDTO.setAppreciateGuid("appreciateGuid");
        shortMsgConfigDTO.setAfterConsume(0);
        shortMsgConfigDTO.setAfterCharge(0);
        shortMsgConfigDTO.setEnterpriseGuid("enterpriseGuid");
        shortMsgConfigDTO.setResidueCount(0);

        // Configure SmsEnterpriseClientService.update(...).
        final ShortMsgConfigDTO shortMsgConfigDTO1 = new ShortMsgConfigDTO();
        shortMsgConfigDTO1.setAppreciateGuid("appreciateGuid");
        shortMsgConfigDTO1.setAfterConsume(0);
        shortMsgConfigDTO1.setAfterCharge(0);
        shortMsgConfigDTO1.setEnterpriseGuid("enterpriseGuid");
        shortMsgConfigDTO1.setResidueCount(0);
        when(mockSmsEnterpriseClientService.update(shortMsgConfigDTO1)).thenReturn(true);

        // Run the test
        final boolean result = shortMessageServiceImplUnderTest.updateMessageConfig(shortMsgConfigDTO);

        // Verify the results
        assertThat(result).isTrue();
    }
}
