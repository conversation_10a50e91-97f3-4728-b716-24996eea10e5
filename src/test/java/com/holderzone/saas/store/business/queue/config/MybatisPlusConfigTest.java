package com.holderzone.saas.store.business.queue.config;

import com.baomidou.mybatisplus.core.injector.ISqlInjector;
import com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor;
import org.junit.Before;
import org.junit.Test;

import static org.junit.Assert.assertEquals;

public class MybatisPlusConfigTest {

    private MybatisPlusConfig mybatisPlusConfigUnderTest;

    @Before
    public void setUp() {
        mybatisPlusConfigUnderTest = new MybatisPlusConfig();
    }

    @Test
    public void testPaginationInterceptor() {
        // Setup
        final PaginationInterceptor expectedResult = new PaginationInterceptor();

        // Run the test
        final PaginationInterceptor result = mybatisPlusConfigUnderTest.paginationInterceptor();

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testSqlInjector() {
        // Setup
        // Run the test
        final ISqlInjector result = mybatisPlusConfigUnderTest.sqlInjector();

        // Verify the results
    }
}
