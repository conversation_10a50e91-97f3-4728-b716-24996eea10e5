package com.holderzone.saas.store.business.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.saas.store.business.entity.domain.ScreenPicTimeDO;
import com.holderzone.saas.store.business.entity.domain.ScreenPictureDO;
import com.holderzone.saas.store.business.mapper.ScreenPictureMapper;
import com.holderzone.saas.store.business.service.ScreenPicTimeService;
import com.holderzone.saas.store.business.service.client.FileUploadClient;
import com.holderzone.saas.store.dto.business.manage.*;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ScreenPictureServiceImplTest {

    @Mock
    private FileUploadClient mockFileUploadClient;
    @Mock
    private ScreenPictureMapper mockScreenPictureMapper;
    @Mock
    private ScreenPicTimeService mockScreenPicTimeService;

    private ScreenPictureServiceImpl screenPictureServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        screenPictureServiceImplUnderTest = new ScreenPictureServiceImpl(mockFileUploadClient, mockScreenPictureMapper,
                mockScreenPicTimeService);
    }

    @Test
    public void testGetConfig() {
        // Setup
        final ScreenPicConfigReqDTO screenPicConfigReqDTO = new ScreenPicConfigReqDTO();
        screenPicConfigReqDTO.setStoreGuid("storeGuid");
        screenPicConfigReqDTO.setDeviceType(0);
        screenPicConfigReqDTO.setPicType(0);

        final ScreenPictureConfigDTO expectedResult = new ScreenPictureConfigDTO();
        final ScreenPicDTO screenPicDTO = new ScreenPicDTO();
        screenPicDTO.setOssUrl("ossUrl");
        screenPicDTO.setPxType(0);
        screenPicDTO.setName("name");
        expectedResult.setScreenPicDTOList(Arrays.asList(screenPicDTO));
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setStoreName("storeName");
        expectedResult.setDeviceType(0);
        expectedResult.setPicType(0);
        expectedResult.setChangeMills(0);
        expectedResult.setPicTimeGuid("picTimeGuid");

        // Configure ScreenPicTimeService.getOne(...).
        final ScreenPicTimeDO screenPicTimeDO = ScreenPicTimeDO.builder()
                .picTimeGuid("picTimeGuid")
                .changeMills(0)
                .picType(0)
                .storeGuid("storeGuid")
                .storeName("storeName")
                .deviceType(0)
                .build();
        when(mockScreenPicTimeService.getOne(any(LambdaQueryWrapper.class))).thenReturn(screenPicTimeDO);

        // Run the test
        final ScreenPictureConfigDTO result = screenPictureServiceImplUnderTest.getConfig(screenPicConfigReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetConfig_ScreenPicTimeServiceReturnsNull() {
        // Setup
        final ScreenPicConfigReqDTO screenPicConfigReqDTO = new ScreenPicConfigReqDTO();
        screenPicConfigReqDTO.setStoreGuid("storeGuid");
        screenPicConfigReqDTO.setDeviceType(0);
        screenPicConfigReqDTO.setPicType(0);

        final ScreenPictureConfigDTO expectedResult = new ScreenPictureConfigDTO();
        final ScreenPicDTO screenPicDTO = new ScreenPicDTO();
        screenPicDTO.setOssUrl("ossUrl");
        screenPicDTO.setPxType(0);
        screenPicDTO.setName("name");
        expectedResult.setScreenPicDTOList(Arrays.asList(screenPicDTO));
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setStoreName("storeName");
        expectedResult.setDeviceType(0);
        expectedResult.setPicType(0);
        expectedResult.setChangeMills(0);
        expectedResult.setPicTimeGuid("picTimeGuid");

        when(mockScreenPicTimeService.getOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Run the test
        final ScreenPictureConfigDTO result = screenPictureServiceImplUnderTest.getConfig(screenPicConfigReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testSaveConfig() {
        // Setup
        final ScreenPictureConfigDTO screenPictureConfigDTO = new ScreenPictureConfigDTO();
        final ScreenPicDTO screenPicDTO = new ScreenPicDTO();
        screenPicDTO.setOssUrl("ossUrl");
        screenPicDTO.setPxType(0);
        screenPicDTO.setName("name");
        screenPictureConfigDTO.setScreenPicDTOList(Arrays.asList(screenPicDTO));
        screenPictureConfigDTO.setStoreGuid("storeGuid");
        screenPictureConfigDTO.setStoreName("storeName");
        screenPictureConfigDTO.setDeviceType(0);
        screenPictureConfigDTO.setPicType(0);
        screenPictureConfigDTO.setChangeMills(0);
        screenPictureConfigDTO.setPicTimeGuid("picTimeGuid");

        when(mockScreenPicTimeService.saveOrUpdate(ScreenPicTimeDO.builder()
                .picTimeGuid("picTimeGuid")
                .changeMills(0)
                .picType(0)
                .storeGuid("storeGuid")
                .storeName("storeName")
                .deviceType(0)
                .build())).thenReturn(false);

        // Run the test
        final String result = screenPictureServiceImplUnderTest.saveConfig(screenPictureConfigDTO);

        // Verify the results
        assertThat(result).isEqualTo("failure");
        verify(mockFileUploadClient).delete("ossUrl");
    }

    @Test
    public void testSave() {
        // Setup
        final ScreenPictureDTO screenPictureDTO = new ScreenPictureDTO();
        screenPictureDTO.setScreenPictureGuid("screenPictureGuid");
        screenPictureDTO.setChangeMills(0);
        screenPictureDTO.setOssUrl("ossUrl");
        screenPictureDTO.setSelectStoreGuid("storeGuid");
        screenPictureDTO.setStoreName("storeName");
        final PictureInfoDTO pictureInfoDTO = new PictureInfoDTO();
        pictureInfoDTO.setPxType(0);
        pictureInfoDTO.setPicType(0);
        screenPictureDTO.setPictureInfoDTO(pictureInfoDTO);

        when(mockScreenPictureMapper.countPics("storeGuid", 0)).thenReturn(0);

        // Configure ScreenPictureMapper.queryOneByGuid(...).
        final ScreenPictureDO screenPictureDO = ScreenPictureDO.builder()
                .screenPictureGuid("screenPictureGuid")
                .name("name")
                .changeMills(0)
                .ossUrl("ossUrl")
                .storeGuid("storeGuid")
                .storeName("storeName")
                .weight(0)
                .height(0)
                .pxType(0)
                .picType(0)
                .asFirst(false)
                .createTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .deviceType(0)
                .build();
        when(mockScreenPictureMapper.queryOneByGuid("screenPictureGuid")).thenReturn(screenPictureDO);

        // Run the test
        final String result = screenPictureServiceImplUnderTest.save(screenPictureDTO);

        // Verify the results
        assertThat(result).isEqualTo("failure");
        verify(mockFileUploadClient).delete("ossUrl");
        verify(mockScreenPictureMapper).insertOrUpdate(ScreenPictureDO.builder()
                .screenPictureGuid("screenPictureGuid")
                .name("name")
                .changeMills(0)
                .ossUrl("ossUrl")
                .storeGuid("storeGuid")
                .storeName("storeName")
                .weight(0)
                .height(0)
                .pxType(0)
                .picType(0)
                .asFirst(false)
                .createTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .deviceType(0)
                .build());
    }

    @Test
    public void testSetTime() {
        // Setup
        final ScreenPicTimeDTO screenPicTimeDTO = new ScreenPicTimeDTO();
        screenPicTimeDTO.setPicTimeGuid("picTimeGuid");
        screenPicTimeDTO.setChangeMills(0);
        screenPicTimeDTO.setPicType(0);
        screenPicTimeDTO.setSelectStoreGuid("selectStoreGuid");

        // Run the test
        final String result = screenPictureServiceImplUnderTest.setTime(screenPicTimeDTO);

        // Verify the results
        assertThat(result).isEqualTo("success");
        verify(mockScreenPictureMapper).insertPicTime(ScreenPicTimeDO.builder()
                .picTimeGuid("picTimeGuid")
                .changeMills(0)
                .picType(0)
                .storeGuid("storeGuid")
                .storeName("storeName")
                .deviceType(0)
                .build());
        verify(mockScreenPictureMapper).updatePicTime(ScreenPicTimeDO.builder()
                .picTimeGuid("picTimeGuid")
                .changeMills(0)
                .picType(0)
                .storeGuid("storeGuid")
                .storeName("storeName")
                .deviceType(0)
                .build());
    }

    @Test
    public void testQueryByWeb() {
        // Setup
        final ScreenPicQuery screenPicQuery = new ScreenPicQuery();
        screenPicQuery.setPicType(0);
        screenPicQuery.setPxType(0);
        screenPicQuery.setSelectStoreGuid("selectStoreGuid");

        final ScreenAppRespDTO screenAppRespDTO = new ScreenAppRespDTO();
        screenAppRespDTO.setOssUrl("ossUrl");
        screenAppRespDTO.setChangeMils(0);
        screenAppRespDTO.setScreenPictureGuid("screenPictureGuid");
        screenAppRespDTO.setHeight(0);
        screenAppRespDTO.setWeight(0);
        final List<ScreenAppRespDTO> expectedResult = Arrays.asList(screenAppRespDTO);

        // Configure ScreenPictureMapper.queryByWeb(...).
        final List<ScreenPictureDO> screenPictureDOS = Arrays.asList(ScreenPictureDO.builder()
                .screenPictureGuid("screenPictureGuid")
                .name("name")
                .changeMills(0)
                .ossUrl("ossUrl")
                .storeGuid("storeGuid")
                .storeName("storeName")
                .weight(0)
                .height(0)
                .pxType(0)
                .picType(0)
                .asFirst(false)
                .createTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .deviceType(0)
                .build());
        final ScreenPicQuery screenPicQuery1 = new ScreenPicQuery();
        screenPicQuery1.setPicType(0);
        screenPicQuery1.setPxType(0);
        screenPicQuery1.setSelectStoreGuid("selectStoreGuid");
        when(mockScreenPictureMapper.queryByWeb(screenPicQuery1)).thenReturn(screenPictureDOS);

        // Run the test
        final List<ScreenAppRespDTO> result = screenPictureServiceImplUnderTest.queryByWeb(screenPicQuery);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryByWeb_ScreenPictureMapperReturnsNoItems() {
        // Setup
        final ScreenPicQuery screenPicQuery = new ScreenPicQuery();
        screenPicQuery.setPicType(0);
        screenPicQuery.setPxType(0);
        screenPicQuery.setSelectStoreGuid("selectStoreGuid");

        // Configure ScreenPictureMapper.queryByWeb(...).
        final ScreenPicQuery screenPicQuery1 = new ScreenPicQuery();
        screenPicQuery1.setPicType(0);
        screenPicQuery1.setPxType(0);
        screenPicQuery1.setSelectStoreGuid("selectStoreGuid");
        when(mockScreenPictureMapper.queryByWeb(screenPicQuery1)).thenReturn(Collections.emptyList());

        // Run the test
        final List<ScreenAppRespDTO> result = screenPictureServiceImplUnderTest.queryByWeb(screenPicQuery);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testGetTime() {
        // Setup
        final ScreenPicTimeDTO screenPicTimeDTO = new ScreenPicTimeDTO();
        screenPicTimeDTO.setPicTimeGuid("picTimeGuid");
        screenPicTimeDTO.setChangeMills(0);
        screenPicTimeDTO.setPicType(0);
        screenPicTimeDTO.setSelectStoreGuid("selectStoreGuid");

        final ScreenPicTimeDTO screenPicTimeDTO1 = new ScreenPicTimeDTO();
        screenPicTimeDTO1.setPicTimeGuid("picTimeGuid");
        screenPicTimeDTO1.setChangeMills(0);
        screenPicTimeDTO1.setPicType(0);
        screenPicTimeDTO1.setSelectStoreGuid("selectStoreGuid");
        final List<ScreenPicTimeDTO> expectedResult = Arrays.asList(screenPicTimeDTO1);

        // Configure ScreenPictureMapper.queryPicTime(...).
        final List<ScreenPicTimeDO> screenPicTimeDOS = Arrays.asList(ScreenPicTimeDO.builder()
                .picTimeGuid("picTimeGuid")
                .changeMills(0)
                .picType(0)
                .storeGuid("storeGuid")
                .storeName("storeName")
                .deviceType(0)
                .build());
        final ScreenPicTimeDTO screenPicTimeDTO2 = new ScreenPicTimeDTO();
        screenPicTimeDTO2.setPicTimeGuid("picTimeGuid");
        screenPicTimeDTO2.setChangeMills(0);
        screenPicTimeDTO2.setPicType(0);
        screenPicTimeDTO2.setSelectStoreGuid("selectStoreGuid");
        when(mockScreenPictureMapper.queryPicTime(screenPicTimeDTO2)).thenReturn(screenPicTimeDOS);

        // Run the test
        final List<ScreenPicTimeDTO> result = screenPictureServiceImplUnderTest.getTime(screenPicTimeDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetTime_ScreenPictureMapperReturnsNoItems() {
        // Setup
        final ScreenPicTimeDTO screenPicTimeDTO = new ScreenPicTimeDTO();
        screenPicTimeDTO.setPicTimeGuid("picTimeGuid");
        screenPicTimeDTO.setChangeMills(0);
        screenPicTimeDTO.setPicType(0);
        screenPicTimeDTO.setSelectStoreGuid("selectStoreGuid");

        // Configure ScreenPictureMapper.queryPicTime(...).
        final ScreenPicTimeDTO screenPicTimeDTO1 = new ScreenPicTimeDTO();
        screenPicTimeDTO1.setPicTimeGuid("picTimeGuid");
        screenPicTimeDTO1.setChangeMills(0);
        screenPicTimeDTO1.setPicType(0);
        screenPicTimeDTO1.setSelectStoreGuid("selectStoreGuid");
        when(mockScreenPictureMapper.queryPicTime(screenPicTimeDTO1)).thenReturn(Collections.emptyList());

        // Run the test
        final List<ScreenPicTimeDTO> result = screenPictureServiceImplUnderTest.getTime(screenPicTimeDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testQueryByAndroid() {
        // Setup
        final ScreenAppRespDTO screenAppRespDTO = new ScreenAppRespDTO();
        screenAppRespDTO.setOssUrl("ossUrl");
        screenAppRespDTO.setChangeMils(0);
        screenAppRespDTO.setScreenPictureGuid("screenPictureGuid");
        screenAppRespDTO.setHeight(0);
        screenAppRespDTO.setWeight(0);
        final List<ScreenAppRespDTO> expectedResult = Arrays.asList(screenAppRespDTO);

        // Run the test
        final List<ScreenAppRespDTO> result = screenPictureServiceImplUnderTest.queryByAndroid("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testDelete() {
        // Setup
        when(mockScreenPictureMapper.getOssUrl("screenPictureGuid")).thenReturn("fileUrl");

        // Run the test
        final String result = screenPictureServiceImplUnderTest.delete("screenPictureGuid");

        // Verify the results
        assertThat(result).isEqualTo("success");
        verify(mockScreenPictureMapper).delete("screenPictureGuid");
        verify(mockFileUploadClient).delete("fileUrl");
    }

    @Test
    public void testCompressPicForScale() {
        assertThat(screenPictureServiceImplUnderTest.compressPicForScale("content".getBytes(), 0L))
                .isEqualTo("content".getBytes());
    }
}
