package com.holderzone.saas.store.business.queue.controller;

import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.business.queue.service.QueueItemService;
import com.holderzone.saas.store.dto.queue.*;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(QueueItemController.class)
public class QueueItemControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private QueueItemService mockQueueItemService;

    @Test
    public void testInQueue() throws Exception {
        // Setup
        // Configure QueueItemService.inQueue(...).
        final HolderQueueItemDetailDTO holderQueueItemDetailDTO = new HolderQueueItemDetailDTO();
        holderQueueItemDetailDTO.setQueueGuid("queueGuid");
        holderQueueItemDetailDTO.setQueueCode("queueCode");
        holderQueueItemDetailDTO.setSort(0);
        holderQueueItemDetailDTO.setCode("code");
        holderQueueItemDetailDTO.setStatus((byte) 0b0);
        final HolderQueueItemDTO dto = new HolderQueueItemDTO();
        dto.setGuid("8f01a5cd-9e71-4dc5-95e9-0539f0728941");
        dto.setStoreGuid("storeGuid");
        dto.setContact("contact");
        dto.setPhone("phone");
        dto.setGender(false);
        when(mockQueueItemService.inQueue(dto)).thenReturn(holderQueueItemDetailDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/queue/item/inQueue")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testCall() throws Exception {
        // Setup
        // Configure QueueItemService.call(...).
        final HolderQueueItemDetailDTO holderQueueItemDetailDTO = new HolderQueueItemDetailDTO();
        holderQueueItemDetailDTO.setQueueGuid("queueGuid");
        holderQueueItemDetailDTO.setQueueCode("queueCode");
        holderQueueItemDetailDTO.setSort(0);
        holderQueueItemDetailDTO.setCode("code");
        holderQueueItemDetailDTO.setStatus((byte) 0b0);
        final ItemGuidDTO dto = new ItemGuidDTO();
        dto.setItemGuid("itemGuid");
        dto.setBrandGuid("brandGuid");
        dto.setEnterpriseGuid("enterpriseGuid");
        when(mockQueueItemService.call(dto)).thenReturn(holderQueueItemDetailDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/queue/item/call")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testPass() throws Exception {
        // Setup
        // Configure QueueItemService.pass(...).
        final HolderQueueItemDetailDTO holderQueueItemDetailDTO = new HolderQueueItemDetailDTO();
        holderQueueItemDetailDTO.setQueueGuid("queueGuid");
        holderQueueItemDetailDTO.setQueueCode("queueCode");
        holderQueueItemDetailDTO.setSort(0);
        holderQueueItemDetailDTO.setCode("code");
        holderQueueItemDetailDTO.setStatus((byte) 0b0);
        final ItemGuidDTO dto = new ItemGuidDTO();
        dto.setItemGuid("itemGuid");
        dto.setBrandGuid("brandGuid");
        dto.setEnterpriseGuid("enterpriseGuid");
        when(mockQueueItemService.pass(dto)).thenReturn(holderQueueItemDetailDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/queue/item/pass")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testConfirm() throws Exception {
        // Setup
        // Configure QueueItemService.confirm(...).
        final HolderQueueItemDetailDTO holderQueueItemDetailDTO = new HolderQueueItemDetailDTO();
        holderQueueItemDetailDTO.setQueueGuid("queueGuid");
        holderQueueItemDetailDTO.setQueueCode("queueCode");
        holderQueueItemDetailDTO.setSort(0);
        holderQueueItemDetailDTO.setCode("code");
        holderQueueItemDetailDTO.setStatus((byte) 0b0);
        final ItemGuidDTO dto = new ItemGuidDTO();
        dto.setItemGuid("itemGuid");
        dto.setBrandGuid("brandGuid");
        dto.setEnterpriseGuid("enterpriseGuid");
        when(mockQueueItemService.confirm(dto)).thenReturn(holderQueueItemDetailDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/queue/item/confirm")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testConfirmAndTable() throws Exception {
        // Setup
        // Configure QueueItemService.confirmAndTable(...).
        final TableQueueItemDetailDTO tableQueueItemDetailDTO = new TableQueueItemDetailDTO();
        tableQueueItemDetailDTO.setOrderNo("orderNo");
        final TableConfirmDTO dto = new TableConfirmDTO();
        dto.setGuid("edfadca8-775b-4a2e-a992-8c8d1965f2c1");
        dto.setRemark("remark");
        dto.setDiningTableGuid("diningTableGuid");
        dto.setDiningTableName("diningTableName");
        dto.setAreaGuid("areaGuid");
        when(mockQueueItemService.confirmAndTable(dto)).thenReturn(tableQueueItemDetailDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/queue/item/confirmAndTable")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testRecover() throws Exception {
        // Setup
        // Configure QueueItemService.recover(...).
        final HolderQueueItemDetailDTO holderQueueItemDetailDTO = new HolderQueueItemDetailDTO();
        holderQueueItemDetailDTO.setQueueGuid("queueGuid");
        holderQueueItemDetailDTO.setQueueCode("queueCode");
        holderQueueItemDetailDTO.setSort(0);
        holderQueueItemDetailDTO.setCode("code");
        holderQueueItemDetailDTO.setStatus((byte) 0b0);
        final ItemGuidDTO dto = new ItemGuidDTO();
        dto.setItemGuid("itemGuid");
        dto.setBrandGuid("brandGuid");
        dto.setEnterpriseGuid("enterpriseGuid");
        when(mockQueueItemService.recover(dto)).thenReturn(holderQueueItemDetailDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/queue/item/recover")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testObtain() throws Exception {
        // Setup
        // Configure QueueItemService.obtain(...).
        final HolderQueueDTO holderQueueDTO = new HolderQueueDTO();
        holderQueueDTO.setGuid("0f0f8f37-c884-4712-9406-6f26661eb915");
        holderQueueDTO.setStoreGuid("storeGuid");
        holderQueueDTO.setBrandName("brandName");
        holderQueueDTO.setLogoUrl("logoUrl");
        holderQueueDTO.setIsEnableRecovery(false);
        final HolderQueueItemDetailDTO holderQueueItemDetailDTO = new HolderQueueItemDetailDTO();
        holderQueueItemDetailDTO.setQueueGuid("queueGuid");
        holderQueueItemDetailDTO.setQueueCode("queueCode");
        holderQueueItemDetailDTO.setSort(0);
        holderQueueItemDetailDTO.setCode("code");
        holderQueueItemDetailDTO.setStatus((byte) 0b0);
        final HolderQueueQueueRecordDTO holderQueueQueueRecordDTO = new HolderQueueQueueRecordDTO(holderQueueDTO,
                holderQueueItemDetailDTO);
        final ItemGuidDTO dto = new ItemGuidDTO();
        dto.setItemGuid("itemGuid");
        dto.setBrandGuid("brandGuid");
        dto.setEnterpriseGuid("enterpriseGuid");
        when(mockQueueItemService.obtain(dto)).thenReturn(holderQueueQueueRecordDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/queue/item/obtain")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testRecords() throws Exception {
        // Setup
        // Configure QueueItemService.listQueueItemDetails(...).
        final HolderQueueItemDetailDTO holderQueueItemDetailDTO = new HolderQueueItemDetailDTO();
        holderQueueItemDetailDTO.setQueueGuid("queueGuid");
        holderQueueItemDetailDTO.setQueueCode("queueCode");
        holderQueueItemDetailDTO.setSort(0);
        holderQueueItemDetailDTO.setCode("code");
        holderQueueItemDetailDTO.setStatus((byte) 0b0);
        final List<HolderQueueItemDetailDTO> holderQueueItemDetailDTOS = Arrays.asList(holderQueueItemDetailDTO);
        when(mockQueueItemService.listQueueItemDetails()).thenReturn(holderQueueItemDetailDTOS);

        // Run the test and verify the results
        mockMvc.perform(post("/queue/records")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testRecords_QueueItemServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockQueueItemService.listQueueItemDetails()).thenReturn(Collections.emptyList());

        // Run the test and verify the results
        mockMvc.perform(post("/queue/records")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("[]", true));
    }

    @Test
    public void testPage() throws Exception {
        // Setup
        // Configure QueueItemService.page(...).
        final HolderQueueItemDetailDTO holderQueueItemDetailDTO = new HolderQueueItemDetailDTO();
        holderQueueItemDetailDTO.setQueueGuid("queueGuid");
        holderQueueItemDetailDTO.setQueueCode("queueCode");
        holderQueueItemDetailDTO.setSort(0);
        holderQueueItemDetailDTO.setCode("code");
        holderQueueItemDetailDTO.setStatus((byte) 0b0);
        final Page<HolderQueueItemDetailDTO> holderQueueItemDetailDTOPage = new Page<>(0L, 0L,
                Arrays.asList(holderQueueItemDetailDTO));
        when(mockQueueItemService.page(any(Page.class))).thenReturn(holderQueueItemDetailDTOPage);

        // Run the test and verify the results
        mockMvc.perform(post("/queue/records/page")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testQueryByUser() throws Exception {
        // Setup
        // Configure QueueItemService.queryByUser(...).
        final WxQueueListDTO wxQueueListDTO = new WxQueueListDTO();
        wxQueueListDTO.setGuid("85900cce-861b-406a-816b-162a11eea716");
        wxQueueListDTO.setUserGuid("userGuid");
        wxQueueListDTO.setStoreGuid("storeGuid");
        wxQueueListDTO.setStoreName("storeName");
        wxQueueListDTO.setBrandName("brandName");
        final List<WxQueueListDTO> wxQueueListDTOS = Arrays.asList(wxQueueListDTO);
        when(mockQueueItemService.queryByUser(
                new QueueWechatDTO("brandGuid", "userGuid", (byte) 0b0, "enterpriseGuid"))).thenReturn(wxQueueListDTOS);

        // Run the test and verify the results
        mockMvc.perform(post("/queue/queryByUser")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testQueryByUser_QueueItemServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockQueueItemService.queryByUser(
                new QueueWechatDTO("brandGuid", "userGuid", (byte) 0b0, "enterpriseGuid")))
                .thenReturn(Collections.emptyList());

        // Run the test and verify the results
        mockMvc.perform(post("/queue/queryByUser")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("[]", true));
    }

    @Test
    public void testCancel() throws Exception {
        // Setup
        when(mockQueueItemService.cancel("queueGuid", "enterpriseGuid")).thenReturn(false);

        // Run the test and verify the results
        mockMvc.perform(get("/queue/cancel")
                        .param("queueGuid", "queueGuid")
                        .param("enterpriseGuid", "enterpriseGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testCancel_QueueItemServiceReturnsTrue() throws Exception {
        // Setup
        when(mockQueueItemService.cancel("queueGuid", "enterpriseGuid")).thenReturn(true);

        // Run the test and verify the results
        mockMvc.perform(get("/queue/cancel")
                        .param("queueGuid", "queueGuid")
                        .param("enterpriseGuid", "enterpriseGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGetQueueDetails() throws Exception {
        // Setup
        // Configure QueueItemService.getQueueDetail(...).
        final HolderQueueDTO holderQueueDTO = new HolderQueueDTO();
        holderQueueDTO.setGuid("0f0f8f37-c884-4712-9406-6f26661eb915");
        holderQueueDTO.setStoreGuid("storeGuid");
        holderQueueDTO.setBrandName("brandName");
        holderQueueDTO.setLogoUrl("logoUrl");
        holderQueueDTO.setIsEnableRecovery(false);
        final HolderQueueItemDetailDTO holderQueueItemDetailDTO = new HolderQueueItemDetailDTO();
        holderQueueItemDetailDTO.setQueueGuid("queueGuid");
        holderQueueItemDetailDTO.setQueueCode("queueCode");
        holderQueueItemDetailDTO.setSort(0);
        holderQueueItemDetailDTO.setCode("code");
        holderQueueItemDetailDTO.setStatus((byte) 0b0);
        final HolderQueueQueueRecordDTO holderQueueQueueRecordDTO = new HolderQueueQueueRecordDTO(holderQueueDTO,
                holderQueueItemDetailDTO);
        final ItemGuidDTO dto = new ItemGuidDTO();
        dto.setItemGuid("itemGuid");
        dto.setBrandGuid("brandGuid");
        dto.setEnterpriseGuid("enterpriseGuid");
        when(mockQueueItemService.getQueueDetail(dto)).thenReturn(holderQueueQueueRecordDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/queue/getQueueDetail")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }
}
