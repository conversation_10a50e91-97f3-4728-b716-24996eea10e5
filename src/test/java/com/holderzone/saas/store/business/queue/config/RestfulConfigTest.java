package com.holderzone.saas.store.business.queue.config;

import org.junit.Before;
import org.junit.Test;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

public class RestfulConfigTest {

    private RestfulConfig restfulConfigUnderTest;

    @Before
    public void setUp() throws Exception {
        restfulConfigUnderTest = new RestfulConfig();
    }

    @Test
    public void testRestTemplate() {
        // Setup
        // Run the test
        final RestTemplate result = restfulConfigUnderTest.restTemplate();

        // Verify the results
    }

    @Test
    public void testSimpleClientHttpRequestFactory() {
        // Setup
        // Run the test
        final ClientHttpRequestFactory result = restfulConfigUnderTest.simpleClientHttpRequestFactory();

        // Verify the results
    }
}
