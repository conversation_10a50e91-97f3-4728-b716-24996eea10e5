package com.holderzone.saas.store.business.queue.controller;

import com.holderzone.saas.store.business.queue.service.QueueService;
import com.holderzone.saas.store.dto.queue.*;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(QueueController.class)
public class QueueControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private QueueService mockQueueService;

    @Test
    public void testSave() throws Exception {
        // Setup
        // Configure QueueService.save(...).
        final HolderQueueDTO dto = new HolderQueueDTO();
        dto.setGuid("269fe427-acb9-4717-a74a-d0e12c843a91");
        dto.setStoreGuid("storeGuid");
        dto.setBrandName("brandName");
        dto.setLogoUrl("logoUrl");
        dto.setIsEnableRecovery(false);
        when(mockQueueService.save(dto)).thenReturn("result");

        // Run the test and verify the results
        mockMvc.perform(post("/queue/save")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testEnable() throws Exception {
        // Setup
        when(mockQueueService.enable("ad8a3e69-c8ee-49b9-a843-5efda548b5e7")).thenReturn(false);

        // Run the test and verify the results
        mockMvc.perform(post("/queue/enable")
                        .param("guid", "ad8a3e69-c8ee-49b9-a843-5efda548b5e7")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testEnable_QueueServiceReturnsTrue() throws Exception {
        // Setup
        when(mockQueueService.enable("ad8a3e69-c8ee-49b9-a843-5efda548b5e7")).thenReturn(true);

        // Run the test and verify the results
        mockMvc.perform(post("/queue/enable")
                        .param("guid", "ad8a3e69-c8ee-49b9-a843-5efda548b5e7")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testDelete() throws Exception {
        // Setup
        when(mockQueueService.delete("c3e3273e-442b-4a16-bf0c-5336c9f88fcf")).thenReturn(false);

        // Run the test and verify the results
        mockMvc.perform(delete("/queue/delete")
                        .param("guid", "c3e3273e-442b-4a16-bf0c-5336c9f88fcf")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testDelete_QueueServiceReturnsTrue() throws Exception {
        // Setup
        when(mockQueueService.delete("c3e3273e-442b-4a16-bf0c-5336c9f88fcf")).thenReturn(true);

        // Run the test and verify the results
        mockMvc.perform(delete("/queue/delete")
                        .param("guid", "c3e3273e-442b-4a16-bf0c-5336c9f88fcf")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testCodes() throws Exception {
        // Setup
        when(mockQueueService.availableCode()).thenReturn(Arrays.asList("value"));

        // Run the test and verify the results
        mockMvc.perform(post("/queue/codes")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testCodes_QueueServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockQueueService.availableCode()).thenReturn(Collections.emptyList());

        // Run the test and verify the results
        mockMvc.perform(post("/queue/codes")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("[]", true));
    }

    @Test
    public void testList() throws Exception {
        // Setup
        // Configure QueueService.all(...).
        final HolderQueueDetailDTO holderQueueDetailDTO = new HolderQueueDetailDTO();
        final HolderQueueItemDetailDTO holderQueueItemDetailDTO = new HolderQueueItemDetailDTO();
        holderQueueItemDetailDTO.setQueueGuid("queueGuid");
        holderQueueItemDetailDTO.setQueueCode("queueCode");
        holderQueueItemDetailDTO.setSort(0);
        holderQueueItemDetailDTO.setCode("code");
        holderQueueDetailDTO.setItems(Arrays.asList(holderQueueItemDetailDTO));
        final StoreQueueDTO storeQueueDTO = new StoreQueueDTO(Arrays.asList(holderQueueDetailDTO), false, false, false);
        when(mockQueueService.all()).thenReturn(storeQueueDTO);

        // Run the test and verify the results
        mockMvc.perform(get("/queue/list")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testQuery() throws Exception {
        // Setup
        // Configure QueueService.query(...).
        final QueueDetailDTO queueDetailDTO = new QueueDetailDTO();
        queueDetailDTO.setGuid("baab69df-3ccc-46e4-88d5-e1e260411f08");
        queueDetailDTO.setStoreGuid("storeGuid");
        queueDetailDTO.setStoreName("storeName");
        queueDetailDTO.setName("name");
        queueDetailDTO.setCode("code");
        final List<QueueDetailDTO> queueDetailDTOS = Arrays.asList(queueDetailDTO);
        when(mockQueueService.query("storeGuid")).thenReturn(queueDetailDTOS);

        // Run the test and verify the results
        mockMvc.perform(get("/queue/query")
                        .param("storeGuid", "storeGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testQuery_QueueServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockQueueService.query("storeGuid")).thenReturn(Collections.emptyList());

        // Run the test and verify the results
        mockMvc.perform(get("/queue/query")
                        .param("storeGuid", "storeGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("[]", true));
    }

    @Test
    public void testListByStore() throws Exception {
        // Setup
        // Configure QueueService.listByStore(...).
        final HolderQueueDTO holderQueueDTO = new HolderQueueDTO();
        holderQueueDTO.setGuid("269fe427-acb9-4717-a74a-d0e12c843a91");
        holderQueueDTO.setStoreGuid("storeGuid");
        holderQueueDTO.setBrandName("brandName");
        holderQueueDTO.setLogoUrl("logoUrl");
        holderQueueDTO.setIsEnableRecovery(false);
        final List<HolderQueueDTO> holderQueueDTOS = Arrays.asList(holderQueueDTO);
        when(mockQueueService.listByStore("storeGuid")).thenReturn(holderQueueDTOS);

        // Run the test and verify the results
        mockMvc.perform(post("/queue/listByStore")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testListByStore_QueueServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockQueueService.listByStore("storeGuid")).thenReturn(Collections.emptyList());

        // Run the test and verify the results
        mockMvc.perform(post("/queue/listByStore")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("[]", true));
    }

    @Test
    public void testAllEmpty() throws Exception {
        // Setup
        when(mockQueueService.allEmpty("storeGuid")).thenReturn(false);

        // Run the test and verify the results
        mockMvc.perform(post("/queue/allEmpty")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testAllEmpty_QueueServiceReturnsTrue() throws Exception {
        // Setup
        when(mockQueueService.allEmpty("storeGuid")).thenReturn(true);

        // Run the test and verify the results
        mockMvc.perform(post("/queue/allEmpty")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testFetchOne() throws Exception {
        // Setup
        // Configure QueueService.fetchOne(...).
        final HolderQueueDTO holderQueueDTO = new HolderQueueDTO();
        holderQueueDTO.setGuid("269fe427-acb9-4717-a74a-d0e12c843a91");
        holderQueueDTO.setStoreGuid("storeGuid");
        holderQueueDTO.setBrandName("brandName");
        holderQueueDTO.setLogoUrl("logoUrl");
        holderQueueDTO.setIsEnableRecovery(false);
        when(mockQueueService.fetchOne("queueGuid")).thenReturn(holderQueueDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/queue/obtain")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testRemove() throws Exception {
        // Setup
        when(mockQueueService.remove(Arrays.asList("value"))).thenReturn(false);

        // Run the test and verify the results
        mockMvc.perform(post("/queue/remove")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testRemove_QueueServiceReturnsTrue() throws Exception {
        // Setup
        when(mockQueueService.remove(Arrays.asList("value"))).thenReturn(true);

        // Run the test and verify the results
        mockMvc.perform(post("/queue/remove")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testRemoveAll() throws Exception {
        // Setup
        when(mockQueueService.removeAll()).thenReturn(false);

        // Run the test and verify the results
        mockMvc.perform(post("/queue/removeAll")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testRemoveAll_QueueServiceReturnsTrue() throws Exception {
        // Setup
        when(mockQueueService.removeAll()).thenReturn(true);

        // Run the test and verify the results
        mockMvc.perform(post("/queue/removeAll")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testAutoClean() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(post("/queue/clean/auto")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
        verify(mockQueueService).clean("storeGuid");
    }

    @Test
    public void testSaveTable() throws Exception {
        // Setup
        // Configure QueueService.saveTables(...).
        final QueueTableDTO queueTableDTO = new QueueTableDTO();
        queueTableDTO.setQueueGuid("queueGuid");
        final TableDTO tableDTO = new TableDTO();
        tableDTO.setTableGuid("tableGuid");
        tableDTO.setTableName("tableName");
        tableDTO.setAreaGuid("areaGuid");
        queueTableDTO.setTables(Arrays.asList(tableDTO));
        final QueueTableDTO dto = new QueueTableDTO();
        dto.setQueueGuid("queueGuid");
        final TableDTO tableDTO1 = new TableDTO();
        tableDTO1.setTableGuid("tableGuid");
        tableDTO1.setTableName("tableName");
        tableDTO1.setAreaGuid("areaGuid");
        dto.setTables(Arrays.asList(tableDTO1));
        when(mockQueueService.saveTables(dto)).thenReturn(queueTableDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/queue/table/save")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testAllTables() throws Exception {
        // Setup
        // Configure QueueService.allTables(...).
        final TreeTableDTO treeTableDTO = new TreeTableDTO();
        treeTableDTO.setChildren(Arrays.asList(new TreeTableDTO()));
        treeTableDTO.setQueueGuid("queueGuid");
        final List<TreeTableDTO> treeTableDTOS = Arrays.asList(treeTableDTO);
        when(mockQueueService.allTables("storeGuid")).thenReturn(treeTableDTOS);

        // Run the test and verify the results
        mockMvc.perform(get("/queue/table/all")
                        .param("storeGuid", "storeGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testAllTables_QueueServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockQueueService.allTables("storeGuid")).thenReturn(Collections.emptyList());

        // Run the test and verify the results
        mockMvc.perform(get("/queue/table/all")
                        .param("storeGuid", "storeGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("[]", true));
    }

    @Test
    public void testObtain1() throws Exception {
        // Setup
        // Configure QueueService.obtain(...).
        final QueueTableDTO queueTableDTO = new QueueTableDTO();
        queueTableDTO.setQueueGuid("queueGuid");
        final TableDTO tableDTO = new TableDTO();
        tableDTO.setTableGuid("tableGuid");
        tableDTO.setTableName("tableName");
        tableDTO.setAreaGuid("areaGuid");
        queueTableDTO.setTables(Arrays.asList(tableDTO));
        when(mockQueueService.obtain("queueGuid")).thenReturn(queueTableDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/queue/table/obtain")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testObtain2() throws Exception {
        // Setup
        // Configure QueueService.queryByStore(...).
        final QueueTableDTO queueTableDTO = new QueueTableDTO();
        queueTableDTO.setQueueGuid("queueGuid");
        final TableDTO tableDTO = new TableDTO();
        tableDTO.setTableGuid("tableGuid");
        tableDTO.setTableName("tableName");
        tableDTO.setAreaGuid("areaGuid");
        queueTableDTO.setTables(Arrays.asList(tableDTO));
        final List<QueueTableDTO> queueTableDTOS = Arrays.asList(queueTableDTO);
        when(mockQueueService.queryByStore("storeGuid")).thenReturn(queueTableDTOS);

        // Run the test and verify the results
        mockMvc.perform(post("/queue/table")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testObtain2_QueueServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockQueueService.queryByStore("storeGuid")).thenReturn(Collections.emptyList());

        // Run the test and verify the results
        mockMvc.perform(post("/queue/table")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("[]", true));
    }
}
