package com.holderzone.saas.store.business.service.impl;

import com.holderzone.saas.store.business.config.RedisIDGenerator;
import com.holderzone.saas.store.business.entity.domain.SystemDiscountDO;
import com.holderzone.saas.store.business.mapper.SystemDiscountMapper;
import com.holderzone.saas.store.business.service.RedisService;
import com.holderzone.saas.store.dto.trade.StoreDTO;
import com.holderzone.saas.store.dto.trade.SystemDiscountDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class SystemDiscountServiceImplTest {

    @Mock
    private SystemDiscountMapper mockSystemDiscountMapper;
    @Mock
    private RedisService mockRedisService;
    @Mock
    private RedisIDGenerator mockRedisIDGenerator;

    private SystemDiscountServiceImpl systemDiscountServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        systemDiscountServiceImplUnderTest = new SystemDiscountServiceImpl(mockSystemDiscountMapper, mockRedisService,
                mockRedisIDGenerator);
    }

    @Test
    public void testAddSystemDiscount() {
        // Setup
        final SystemDiscountDTO systemDiscountDTO = new SystemDiscountDTO();
        systemDiscountDTO.setStoreGuid("storeGuid");
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setStoreGuid("storeGuid");
        storeDTO.setStoreName("storeName");
        systemDiscountDTO.setStoreDTOS(Arrays.asList(storeDTO));
        systemDiscountDTO.setSystemDiscountGuid("systemDiscountGuid");
        systemDiscountDTO.setDiscountFee(new BigDecimal("0.00"));
        systemDiscountDTO.setRoundType(0);

        when(mockRedisIDGenerator.getBatchIds(0L, "hst_sys_discount")).thenReturn(Arrays.asList(0L));
        when(mockSystemDiscountMapper.countSameFee(new BigDecimal("0.00"), "storeGuid")).thenReturn(0);

        // Run the test
        systemDiscountServiceImplUnderTest.addSystemDiscount(systemDiscountDTO);

        // Verify the results
        // Confirm SystemDiscountMapper.insertAll(...).
        final SystemDiscountDO systemDiscountDO = new SystemDiscountDO();
        systemDiscountDO.setStoreGuid("storeGuid");
        systemDiscountDO.setStoreName("storeName");
        systemDiscountDO.setSystemDiscountGuid("systemDiscountGuid");
        systemDiscountDO.setDiscountFee(new BigDecimal("0.00"));
        systemDiscountDO.setRoundType(0);
        final List<SystemDiscountDO> systemDiscountDOS = Arrays.asList(systemDiscountDO);
        verify(mockSystemDiscountMapper).insertAll(systemDiscountDOS);
        verify(mockRedisService).removeSysDiscount("storeGuid");
    }

    @Test
    public void testAddSystemDiscount_RedisIDGeneratorReturnsNoItems() {
        // Setup
        final SystemDiscountDTO systemDiscountDTO = new SystemDiscountDTO();
        systemDiscountDTO.setStoreGuid("storeGuid");
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setStoreGuid("storeGuid");
        storeDTO.setStoreName("storeName");
        systemDiscountDTO.setStoreDTOS(Arrays.asList(storeDTO));
        systemDiscountDTO.setSystemDiscountGuid("systemDiscountGuid");
        systemDiscountDTO.setDiscountFee(new BigDecimal("0.00"));
        systemDiscountDTO.setRoundType(0);

        when(mockRedisIDGenerator.getBatchIds(0L, "hst_sys_discount")).thenReturn(Collections.emptyList());
        when(mockSystemDiscountMapper.countSameFee(new BigDecimal("0.00"), "storeGuid")).thenReturn(0);

        // Run the test
        systemDiscountServiceImplUnderTest.addSystemDiscount(systemDiscountDTO);

        // Verify the results
        // Confirm SystemDiscountMapper.insertAll(...).
        final SystemDiscountDO systemDiscountDO = new SystemDiscountDO();
        systemDiscountDO.setStoreGuid("storeGuid");
        systemDiscountDO.setStoreName("storeName");
        systemDiscountDO.setSystemDiscountGuid("systemDiscountGuid");
        systemDiscountDO.setDiscountFee(new BigDecimal("0.00"));
        systemDiscountDO.setRoundType(0);
        final List<SystemDiscountDO> systemDiscountDOS = Arrays.asList(systemDiscountDO);
        verify(mockSystemDiscountMapper).insertAll(systemDiscountDOS);
        verify(mockRedisService).removeSysDiscount("storeGuid");
    }

    @Test
    public void testQueryAllSystemDis() {
        // Setup
        final SystemDiscountDTO systemDiscountDTO = new SystemDiscountDTO();
        systemDiscountDTO.setStoreGuid("storeGuid");
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setStoreGuid("storeGuid");
        storeDTO.setStoreName("storeName");
        systemDiscountDTO.setStoreDTOS(Arrays.asList(storeDTO));
        systemDiscountDTO.setSystemDiscountGuid("systemDiscountGuid");
        systemDiscountDTO.setDiscountFee(new BigDecimal("0.00"));
        systemDiscountDTO.setRoundType(0);
        final List<SystemDiscountDTO> expectedResult = Arrays.asList(systemDiscountDTO);

        // Configure SystemDiscountMapper.getAllSystemDiscount(...).
        final SystemDiscountDTO systemDiscountDTO1 = new SystemDiscountDTO();
        systemDiscountDTO1.setStoreGuid("storeGuid");
        final StoreDTO storeDTO1 = new StoreDTO();
        storeDTO1.setStoreGuid("storeGuid");
        storeDTO1.setStoreName("storeName");
        systemDiscountDTO1.setStoreDTOS(Arrays.asList(storeDTO1));
        systemDiscountDTO1.setSystemDiscountGuid("systemDiscountGuid");
        systemDiscountDTO1.setDiscountFee(new BigDecimal("0.00"));
        systemDiscountDTO1.setRoundType(0);
        final List<SystemDiscountDTO> systemDiscountDTOS = Arrays.asList(systemDiscountDTO1);
        when(mockSystemDiscountMapper.getAllSystemDiscount("storeGuid")).thenReturn(systemDiscountDTOS);

        // Run the test
        final List<SystemDiscountDTO> result = systemDiscountServiceImplUnderTest.queryAllSystemDis("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryAllSystemDis_SystemDiscountMapperReturnsNoItems() {
        // Setup
        when(mockSystemDiscountMapper.getAllSystemDiscount("storeGuid")).thenReturn(Collections.emptyList());

        // Run the test
        final List<SystemDiscountDTO> result = systemDiscountServiceImplUnderTest.queryAllSystemDis("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testQueryAllSystemDOS() {
        // Setup
        final SystemDiscountDO systemDiscountDO = new SystemDiscountDO();
        systemDiscountDO.setStoreGuid("storeGuid");
        systemDiscountDO.setStoreName("storeName");
        systemDiscountDO.setSystemDiscountGuid("systemDiscountGuid");
        systemDiscountDO.setDiscountFee(new BigDecimal("0.00"));
        systemDiscountDO.setRoundType(0);
        final List<SystemDiscountDO> expectedResult = Arrays.asList(systemDiscountDO);

        // Configure SystemDiscountMapper.getAllSystemDOS(...).
        final SystemDiscountDO systemDiscountDO1 = new SystemDiscountDO();
        systemDiscountDO1.setStoreGuid("storeGuid");
        systemDiscountDO1.setStoreName("storeName");
        systemDiscountDO1.setSystemDiscountGuid("systemDiscountGuid");
        systemDiscountDO1.setDiscountFee(new BigDecimal("0.00"));
        systemDiscountDO1.setRoundType(0);
        final List<SystemDiscountDO> systemDiscountDOS = Arrays.asList(systemDiscountDO1);
        when(mockSystemDiscountMapper.getAllSystemDOS("billGuid")).thenReturn(systemDiscountDOS);

        // Run the test
        final List<SystemDiscountDO> result = systemDiscountServiceImplUnderTest.queryAllSystemDOS("billGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryAllSystemDOS_SystemDiscountMapperReturnsNoItems() {
        // Setup
        when(mockSystemDiscountMapper.getAllSystemDOS("billGuid")).thenReturn(Collections.emptyList());

        // Run the test
        final List<SystemDiscountDO> result = systemDiscountServiceImplUnderTest.queryAllSystemDOS("billGuid");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testUpdate() {
        // Setup
        final SystemDiscountDTO systemDiscountDTO = new SystemDiscountDTO();
        systemDiscountDTO.setStoreGuid("storeGuid");
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setStoreGuid("storeGuid");
        storeDTO.setStoreName("storeName");
        systemDiscountDTO.setStoreDTOS(Arrays.asList(storeDTO));
        systemDiscountDTO.setSystemDiscountGuid("systemDiscountGuid");
        systemDiscountDTO.setDiscountFee(new BigDecimal("0.00"));
        systemDiscountDTO.setRoundType(0);

        when(mockSystemDiscountMapper.countSameFee(new BigDecimal("0.00"), "storeGuid")).thenReturn(0);
        when(mockSystemDiscountMapper.getDiscountFee("systemDiscountGuid")).thenReturn(new BigDecimal("0.00"));

        // Run the test
        systemDiscountServiceImplUnderTest.update(systemDiscountDTO);

        // Verify the results
        // Confirm SystemDiscountMapper.update(...).
        final SystemDiscountDO systemDiscountDO = new SystemDiscountDO();
        systemDiscountDO.setStoreGuid("storeGuid");
        systemDiscountDO.setStoreName("storeName");
        systemDiscountDO.setSystemDiscountGuid("systemDiscountGuid");
        systemDiscountDO.setDiscountFee(new BigDecimal("0.00"));
        systemDiscountDO.setRoundType(0);
        verify(mockSystemDiscountMapper).update(systemDiscountDO);
        verify(mockRedisService).removeSysDiscount("storeGuid");
    }

    @Test
    public void testGetByStoreGuid() {
        // Setup
        final SystemDiscountDTO expectedResult = new SystemDiscountDTO();
        expectedResult.setStoreGuid("storeGuid");
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setStoreGuid("storeGuid");
        storeDTO.setStoreName("storeName");
        expectedResult.setStoreDTOS(Arrays.asList(storeDTO));
        expectedResult.setSystemDiscountGuid("systemDiscountGuid");
        expectedResult.setDiscountFee(new BigDecimal("0.00"));
        expectedResult.setRoundType(0);

        // Configure SystemDiscountMapper.getByStoreGuid(...).
        final SystemDiscountDO systemDiscountDO = new SystemDiscountDO();
        systemDiscountDO.setStoreGuid("storeGuid");
        systemDiscountDO.setStoreName("storeName");
        systemDiscountDO.setSystemDiscountGuid("systemDiscountGuid");
        systemDiscountDO.setDiscountFee(new BigDecimal("0.00"));
        systemDiscountDO.setRoundType(0);
        when(mockSystemDiscountMapper.getByStoreGuid("storeGuid")).thenReturn(systemDiscountDO);

        // Run the test
        final SystemDiscountDTO result = systemDiscountServiceImplUnderTest.getByStoreGuid("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testSaveOrUpdate() {
        // Setup
        final SystemDiscountDTO systemDiscountDTO = new SystemDiscountDTO();
        systemDiscountDTO.setStoreGuid("storeGuid");
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setStoreGuid("storeGuid");
        storeDTO.setStoreName("storeName");
        systemDiscountDTO.setStoreDTOS(Arrays.asList(storeDTO));
        systemDiscountDTO.setSystemDiscountGuid("systemDiscountGuid");
        systemDiscountDTO.setDiscountFee(new BigDecimal("0.00"));
        systemDiscountDTO.setRoundType(0);

        final SystemDiscountDTO expectedResult = new SystemDiscountDTO();
        expectedResult.setStoreGuid("storeGuid");
        final StoreDTO storeDTO1 = new StoreDTO();
        storeDTO1.setStoreGuid("storeGuid");
        storeDTO1.setStoreName("storeName");
        expectedResult.setStoreDTOS(Arrays.asList(storeDTO1));
        expectedResult.setSystemDiscountGuid("systemDiscountGuid");
        expectedResult.setDiscountFee(new BigDecimal("0.00"));
        expectedResult.setRoundType(0);

        when(mockRedisIDGenerator.getBatchIds(1L, "hst_sys_discount")).thenReturn(Arrays.asList(0L));

        // Configure SystemDiscountMapper.getById(...).
        final SystemDiscountDO systemDiscountDO = new SystemDiscountDO();
        systemDiscountDO.setStoreGuid("storeGuid");
        systemDiscountDO.setStoreName("storeName");
        systemDiscountDO.setSystemDiscountGuid("systemDiscountGuid");
        systemDiscountDO.setDiscountFee(new BigDecimal("0.00"));
        systemDiscountDO.setRoundType(0);
        when(mockSystemDiscountMapper.getById("systemDiscountGuid")).thenReturn(systemDiscountDO);

        // Run the test
        final SystemDiscountDTO result = systemDiscountServiceImplUnderTest.saveOrUpdate(systemDiscountDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockSystemDiscountMapper).delete("storeGuid", "systemDiscountGuid");

        // Confirm SystemDiscountMapper.update(...).
        final SystemDiscountDO systemDiscountDO1 = new SystemDiscountDO();
        systemDiscountDO1.setStoreGuid("storeGuid");
        systemDiscountDO1.setStoreName("storeName");
        systemDiscountDO1.setSystemDiscountGuid("systemDiscountGuid");
        systemDiscountDO1.setDiscountFee(new BigDecimal("0.00"));
        systemDiscountDO1.setRoundType(0);
        verify(mockSystemDiscountMapper).update(systemDiscountDO1);
    }

    @Test
    public void testSaveOrUpdate_RedisIDGeneratorReturnsNoItems() {
        // Setup
        final SystemDiscountDTO systemDiscountDTO = new SystemDiscountDTO();
        systemDiscountDTO.setStoreGuid("storeGuid");
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setStoreGuid("storeGuid");
        storeDTO.setStoreName("storeName");
        systemDiscountDTO.setStoreDTOS(Arrays.asList(storeDTO));
        systemDiscountDTO.setSystemDiscountGuid("systemDiscountGuid");
        systemDiscountDTO.setDiscountFee(new BigDecimal("0.00"));
        systemDiscountDTO.setRoundType(0);

        final SystemDiscountDTO expectedResult = new SystemDiscountDTO();
        expectedResult.setStoreGuid("storeGuid");
        final StoreDTO storeDTO1 = new StoreDTO();
        storeDTO1.setStoreGuid("storeGuid");
        storeDTO1.setStoreName("storeName");
        expectedResult.setStoreDTOS(Arrays.asList(storeDTO1));
        expectedResult.setSystemDiscountGuid("systemDiscountGuid");
        expectedResult.setDiscountFee(new BigDecimal("0.00"));
        expectedResult.setRoundType(0);

        when(mockRedisIDGenerator.getBatchIds(1L, "hst_sys_discount")).thenReturn(Collections.emptyList());

        // Configure SystemDiscountMapper.getById(...).
        final SystemDiscountDO systemDiscountDO = new SystemDiscountDO();
        systemDiscountDO.setStoreGuid("storeGuid");
        systemDiscountDO.setStoreName("storeName");
        systemDiscountDO.setSystemDiscountGuid("systemDiscountGuid");
        systemDiscountDO.setDiscountFee(new BigDecimal("0.00"));
        systemDiscountDO.setRoundType(0);
        when(mockSystemDiscountMapper.getById("systemDiscountGuid")).thenReturn(systemDiscountDO);

        // Run the test
        final SystemDiscountDTO result = systemDiscountServiceImplUnderTest.saveOrUpdate(systemDiscountDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockSystemDiscountMapper).delete("storeGuid", "systemDiscountGuid");

        // Confirm SystemDiscountMapper.update(...).
        final SystemDiscountDO systemDiscountDO1 = new SystemDiscountDO();
        systemDiscountDO1.setStoreGuid("storeGuid");
        systemDiscountDO1.setStoreName("storeName");
        systemDiscountDO1.setSystemDiscountGuid("systemDiscountGuid");
        systemDiscountDO1.setDiscountFee(new BigDecimal("0.00"));
        systemDiscountDO1.setRoundType(0);
        verify(mockSystemDiscountMapper).update(systemDiscountDO1);
    }

    @Test
    public void testSaveOrUpdate_SystemDiscountMapperGetByIdReturnsNull() {
        // Setup
        final SystemDiscountDTO systemDiscountDTO = new SystemDiscountDTO();
        systemDiscountDTO.setStoreGuid("storeGuid");
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setStoreGuid("storeGuid");
        storeDTO.setStoreName("storeName");
        systemDiscountDTO.setStoreDTOS(Arrays.asList(storeDTO));
        systemDiscountDTO.setSystemDiscountGuid("systemDiscountGuid");
        systemDiscountDTO.setDiscountFee(new BigDecimal("0.00"));
        systemDiscountDTO.setRoundType(0);

        final SystemDiscountDTO expectedResult = new SystemDiscountDTO();
        expectedResult.setStoreGuid("storeGuid");
        final StoreDTO storeDTO1 = new StoreDTO();
        storeDTO1.setStoreGuid("storeGuid");
        storeDTO1.setStoreName("storeName");
        expectedResult.setStoreDTOS(Arrays.asList(storeDTO1));
        expectedResult.setSystemDiscountGuid("systemDiscountGuid");
        expectedResult.setDiscountFee(new BigDecimal("0.00"));
        expectedResult.setRoundType(0);

        when(mockRedisIDGenerator.getBatchIds(1L, "hst_sys_discount")).thenReturn(Arrays.asList(0L));
        when(mockSystemDiscountMapper.getById("systemDiscountGuid")).thenReturn(null);

        // Run the test
        final SystemDiscountDTO result = systemDiscountServiceImplUnderTest.saveOrUpdate(systemDiscountDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);

        // Confirm SystemDiscountMapper.insertSystemDiscout(...).
        final SystemDiscountDO systemDiscountDO = new SystemDiscountDO();
        systemDiscountDO.setStoreGuid("storeGuid");
        systemDiscountDO.setStoreName("storeName");
        systemDiscountDO.setSystemDiscountGuid("systemDiscountGuid");
        systemDiscountDO.setDiscountFee(new BigDecimal("0.00"));
        systemDiscountDO.setRoundType(0);
        verify(mockSystemDiscountMapper).insertSystemDiscout(systemDiscountDO);
    }

    @Test
    public void testDelete() {
        // Setup
        // Run the test
        systemDiscountServiceImplUnderTest.delete("storeGuid", "systemDiscountGuid");

        // Verify the results
        verify(mockSystemDiscountMapper).delete("storeGuid", "systemDiscountGuid");
        verify(mockRedisService).removeSysDiscount("storeGuid");
    }
}
