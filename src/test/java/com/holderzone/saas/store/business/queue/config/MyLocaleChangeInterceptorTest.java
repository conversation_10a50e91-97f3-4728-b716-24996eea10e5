package com.holderzone.saas.store.business.queue.config;

import org.junit.Before;
import org.junit.Test;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

import javax.servlet.ServletException;

import static org.junit.Assert.assertFalse;

public class MyLocaleChangeInterceptorTest {

    private MyLocaleChangeInterceptor myLocaleChangeInterceptorUnderTest;

    @Before
    public void setUp() {
        myLocaleChangeInterceptorUnderTest = new MyLocaleChangeInterceptor();
    }

    @Test
    public void testPreHandle() throws Exception {
        // Setup
        final MockHttpServletRequest request = new MockHttpServletRequest();
        final MockHttpServletResponse response = new MockHttpServletResponse();

        // Run the test
        final boolean result = myLocaleChangeInterceptorUnderTest.preHandle(request, response, "handler");

        // Verify the results
        assertFalse(result);
    }

    @Test(expected = ServletException.class)
    public void testPreHandle_ThrowsServletException() throws Exception {
        // Setup
        final MockHttpServletRequest request = new MockHttpServletRequest();
        final MockHttpServletResponse response = new MockHttpServletResponse();

        // Run the test
        myLocaleChangeInterceptorUnderTest.preHandle(request, response, "handler");
    }

    @Test(expected = Exception.class)
    public void testPreHandle_ThrowsException() throws Exception {
        // Setup
        final MockHttpServletRequest request = new MockHttpServletRequest();
        final MockHttpServletResponse response = new MockHttpServletResponse();

        // Run the test
        myLocaleChangeInterceptorUnderTest.preHandle(request, response, "handler");
    }
}
