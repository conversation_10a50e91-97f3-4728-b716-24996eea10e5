package com.holderzone.saas.store.business;

import com.alibaba.fastjson.JSON;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.business.entity.domain.HandoverPayDetailDO;
import com.holderzone.saas.store.business.entity.domain.HandoverRecordDO;
import com.holderzone.saas.store.business.mapper.HandoverPayDetailMapper;
import com.holderzone.saas.store.business.mapper.HandoverRecordMapper;
import com.holderzone.saas.store.business.service.client.TradingClient;
import com.holderzone.saas.store.business.utils.DynamicHelper;
import com.holderzone.saas.store.dto.business.manage.*;
import com.holderzone.saas.store.dto.trade.PaymentTypeDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className HandoverMvcTest
 * @date 18-9-4 上午10:01
 * @description 交接班接口测试
 * @program holder-saas-store-business
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class HandoverMvcTest {

    @Autowired
    private WebApplicationContext webApplicationContext;
    @Autowired
    private HandoverRecordMapper handoverRecordMapper;
    @Autowired
    private DynamicHelper dynamicHelper;

    @Autowired
    private HandoverPayDetailMapper handoverPayDetailMapper;

    @Autowired
    private TradingClient tradingClient;

    private MockMvc mockMvc;

    private String storeGuid;

    @Before
    public void setup() {
        this.mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        storeGuid = "8b9ed76b-4c9c-443b-8d0e-8a2d2c234511";
    }

    @Test
    // @Ignore
    public void testCreate() throws Exception {
        HandoverRecordCreateDTO dto = new HandoverRecordCreateDTO();
        dto.setStoreGuid(storeGuid);
        dto.setStoreName("测试门店4");
        dto.setTerminalId("123456");
        dto.setCreateUserGuid("123456");
        dto.setCreateUserName("system");
        String result = mockMvc.perform(MockMvcRequestBuilders.post("/handover/create")
                                                              .accept(MediaType.APPLICATION_JSON_UTF8)
                                                              .contentType(MediaType.APPLICATION_JSON_UTF8)
                                                              .content(JSON.toJSONString(dto)))
                               .andExpect(status().isOk())
                               .andDo(MockMvcResultHandlers.print())
                               .andReturn()
                               .getResponse().getContentAsString();
    }

    @Test
    // @Ignore
    public void testConfirm() throws Exception {
        HandoverRecordConfirmDTO dto = new HandoverRecordConfirmDTO();
        // dto.setPaymentCount(2);
        // dto.setPaymentMoney(new BigDecimal(100.1));
        String result = mockMvc.perform(MockMvcRequestBuilders.post("/handover/confirm")
                                                              .accept(MediaType.APPLICATION_JSON_UTF8)
                                                              .contentType(MediaType.APPLICATION_JSON_UTF8)
                                                              .content(JSON.toJSONString(dto)))
                               .andExpect(status().isOk())
                               .andDo(MockMvcResultHandlers.print())
                               .andReturn()
                               .getResponse().getContentAsString();
    }

    @Test
    // @Ignore
    public void testQuery() throws Exception {
        HandoverRecordDTO dto = new HandoverRecordDTO();
        dto.setHandoverRecordGuid("90b8b2d4-43c6-4298-a1ed-9da7d922a640");
        String result = mockMvc.perform(MockMvcRequestBuilders.post("/handover/queryByAndroid")
                                                              .accept(MediaType.APPLICATION_JSON_UTF8)
                                                              .contentType(MediaType.APPLICATION_JSON_UTF8)
                                                              .content(JSON.toJSONString(dto)))
                               .andExpect(status().isOk())
                               .andDo(MockMvcResultHandlers.print())
                               .andReturn()
                               .getResponse().getContentAsString();
    }

    @Test
    // @Ignore
    public void testQueryByPage() throws Exception {
        HandoverRecordQueryAllDTO dto = new HandoverRecordQueryAllDTO();
        dto.setCurrentPage(1);
        dto.setPageSize(10);
        dto.setStoreGuid(storeGuid);
        dto.setDeviceId("123456");
        String result = mockMvc.perform(MockMvcRequestBuilders.post("/handover/queryByPage")
                                                              .accept(MediaType.APPLICATION_JSON_UTF8)
                                                              .contentType(MediaType.APPLICATION_JSON_UTF8)
                                                              .content(JSON.toJSONString(dto)))
                               .andExpect(status().isOk())
                               .andDo(MockMvcResultHandlers.print())
                               .andReturn()
                               .getResponse().getContentAsString();
    }

    @Test
    // @Ignore
    public void testIsConfirm() throws Exception {
        HandoverRecordIsAllConfirmedDTO dto = new HandoverRecordIsAllConfirmedDTO();
        dto.setStoreGuid(storeGuid);
        String result = mockMvc.perform(MockMvcRequestBuilders.post("/handover/isAllConfirmed")
                                                              .accept(MediaType.APPLICATION_JSON_UTF8)
                                                              .contentType(MediaType.APPLICATION_JSON_UTF8)
                                                              .content(JSON.toJSONString(dto)))
                               .andExpect(status().isOk())
                               .andDo(MockMvcResultHandlers.print())
                               .andReturn()
                               .getResponse().getContentAsString();
    }

    @Test
    public void testBatchInsert() {
        List<PaymentTypeDTO> list = tradingClient.getAllTypeName("testStoreGuid");

        HashMap<Integer, BigDecimal> map = new HashMap<>();
        map.put(1, new BigDecimal("100.00"));
        map.put(2, new BigDecimal("200.10"));
        map.put(3, new BigDecimal("300.01"));
        List<HandoverPayDetailDO> handoverPayDetailDOList = new ArrayList<>(map.size());
        map.forEach(
                (k, v) -> {
                    HandoverPayDetailDO payDetailDO = new HandoverPayDetailDO();
                    payDetailDO.setHandoverRecordGuid("123456")
                               .setTerminalId("123456")
                               .setPayType(k.toString())
                               .setPayMoney(v);
                    if(list != null && !list.isEmpty()) {
                        list.stream().filter(p -> Objects.equals(p.getPaymentType(), k))
                                          .findFirst().ifPresent((p) -> payDetailDO.setPayTypeName(p.getPaymentTypeName()));
                        handoverPayDetailDOList.add(payDetailDO);
                    }
                });

        int result = handoverPayDetailMapper.batchInsert(handoverPayDetailDOList);
        if (result != map.size()) {
            throw new BusinessException("出错了，回滚");
        }
    }

    @Test
    public void testSelectPayDetail() {
        List<HandoverPayDetailDO> list = handoverPayDetailMapper.selectPayDetailById("123456");
    }

    @Test
    public void testHandover() {
        dynamicHelper.changeDatasource("6473487755495150593");
        HandoverRecordDO handoverRecordDO = handoverRecordMapper.queryByStoreGuidAndUserGuid("6473493364208513025", "6473713098674930690");
        System.out.println(handoverRecordDO);
    }
}
