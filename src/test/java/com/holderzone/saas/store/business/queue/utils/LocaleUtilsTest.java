package com.holderzone.saas.store.business.queue.utils;

import org.junit.Test;

import static org.assertj.core.api.Assertions.assertThat;

public class LocaleUtilsTest {

    @Test
    public void testGetMessage1() {
        assertThat(LocaleUtils.getMessage("msgKey")).isEqualTo("result");
    }

    @Test
    public void testGetMessage2() {
        assertThat(LocaleUtils.getMessage("message", "message")).isEqualTo("message");
    }
}
