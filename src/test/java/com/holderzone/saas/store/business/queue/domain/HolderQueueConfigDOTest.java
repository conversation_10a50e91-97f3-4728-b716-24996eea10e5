package com.holderzone.saas.store.business.queue.domain;

import org.junit.Test;

import java.time.LocalDateTime;
import java.time.LocalTime;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;

public class HolderQueueConfigDOTest {

    @Test
    public void testDefaultConfig() {
        // Run the test
        final HolderQueueConfigDO result = HolderQueueConfigDO.defaultConfig();
        assertEquals(Long.valueOf(0L), result.getId());
        assertEquals("guid", result.getGuid());
        assertEquals("storeGuid", result.getStoreGuid());
        assertFalse(result.getIsEnableEat());
        assertFalse(result.getIsEnableRecovery());
        assertEquals(Integer.valueOf(0), result.getRecoveryNum());
        assertFalse(result.getIsEnableManualReset());
        assertFalse(result.getIsEnableAutoReset());
        assertEquals(LocalTime.of(0, 0, 0), result.getAutoResetTiming());
        assertFalse(result.getIsDeleted());
        assertEquals("createStaffGuid", result.getCreateStaffGuid());
        assertEquals("modifiedStaffGuid", result.getModifiedStaffGuid());
        assertEquals(LocalDateTime.of(2020, 1, 1, 0, 0, 0), result.getGmtCreate());
        assertEquals(LocalDateTime.of(2020, 1, 1, 0, 0, 0), result.getGmtModified());
        assertFalse(result.equals("o"));
        assertEquals(0, result.hashCode());
        assertEquals("result", result.toString());
    }
}
