package com.holderzone.saas.store.business.queue.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.business.queue.domain.HolderQueueDO;
import com.holderzone.saas.store.business.queue.domain.HolderQueueItemDO;
import com.holderzone.saas.store.business.queue.helper.DynamicHelper;
import com.holderzone.saas.store.business.queue.mapper.QueueMapper;
import com.holderzone.saas.store.business.queue.mapstruct.QueueItemMapStruct;
import com.holderzone.saas.store.business.queue.mapstruct.QueueMapStruct;
import com.holderzone.saas.store.business.queue.service.QueueConfigService;
import com.holderzone.saas.store.business.queue.service.remote.*;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.message.BusinessMessageDTO;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.print.content.PrintDTO;
import com.holderzone.saas.store.dto.queue.*;
import com.holderzone.saas.store.dto.table.OpenTableDTO;
import com.holderzone.saas.store.dto.weixin.req.WxQueueInfoReqDTO;
import com.holderzone.saas.store.enums.print.PrintSourceEnum;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.transaction.TransactionException;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class QueueItemServiceImplTest {

    @Mock
    private QueueMapper mockQueueMapper;
    @Mock
    private QueueItemMapStruct mockQueueItemMapStruct;
    @Mock
    private BusinessMsgClient mockBusinessMsgClient;
    @Mock
    private PrintClient mockPrintClient;
    @Mock
    private OrganizationClientService mockOrganizationClientService;
    @Mock
    private TableClientService mockTableClientService;
    @Mock
    private WxClient mockWxClient;
    @Mock
    private QueueConfigService mockQueueConfigService;
    @Mock
    private QueueMapStruct mockQueueMapStruct;
    @Mock
    private DynamicHelper mockDynamicHelper;
    @Mock
    private TransactionTemplate mockTransactionTemplate;

    private QueueItemServiceImpl queueItemServiceImplUnderTest;

    @Before
    public void setUp() {
        queueItemServiceImplUnderTest = new QueueItemServiceImpl(mockQueueMapper, mockQueueItemMapStruct,
                mockBusinessMsgClient, mockPrintClient, mockOrganizationClientService, mockTableClientService,
                mockWxClient, mockQueueConfigService, mockQueueMapStruct, mockDynamicHelper);
        queueItemServiceImplUnderTest.transactionTemplate = mockTransactionTemplate;
    }

    @Test
    public void testMain() {
        // Setup
        // Run the test
        QueueItemServiceImpl.main(new String[]{"args"});

        // Verify the results
    }

    @Test
    public void testCurrentBusinessStart1() {
        // Setup
        // Configure OrganizationClientService.queryStoreByGuid(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("13bab148-d971-47a4-b422-f966bc3fc49a");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        when(mockOrganizationClientService.queryStoreByGuid("storeGuid")).thenReturn(storeDTO);

        // Run the test
        final LocalDateTime result = queueItemServiceImplUnderTest.currentBusinessStart("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
    }

    @Test
    public void testInQueue() {
        // Setup
        final HolderQueueItemDTO dto = new HolderQueueItemDTO();
        dto.setDeviceType(0);
        dto.setDeviceId("deviceId");
        dto.setStoreName("storeName");
        dto.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        dto.setStoreGuid("storeGuid");
        dto.setPhone("phone");
        dto.setPeopleNumber((byte) 0b0);

        final HolderQueueItemDetailDTO expectedResult = new HolderQueueItemDetailDTO();
        expectedResult.setDeviceType(0);
        expectedResult.setDeviceId("deviceId");
        expectedResult.setStoreName("storeName");
        expectedResult.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setPhone("phone");
        expectedResult.setPeopleNumber((byte) 0b0);
        expectedResult.setQueueGuid("queueGuid");
        expectedResult.setSort(0);
        expectedResult.setBefore(0);
        expectedResult.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Configure QueueMapper.selectList(...).
        final HolderQueueDO holderQueueDO = new HolderQueueDO();
        holderQueueDO.setGuid("98dbaed2-ad56-4263-9a2a-85c05a345eea");
        holderQueueDO.setStoreGuid("storeGuid");
        holderQueueDO.setCode("code");
        holderQueueDO.setMin((byte) 0b0);
        holderQueueDO.setMax((byte) 0b0);
        holderQueueDO.setIsEnable(false);
        final List<HolderQueueDO> queueDOList = Arrays.asList(holderQueueDO);
        when(mockQueueMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(queueDOList);

        when(mockTransactionTemplate.execute(any(TransactionCallback.class))).thenReturn("result");

        // Configure QueueItemMapStruct.toDetailDto(...).
        final HolderQueueItemDetailDTO holderQueueItemDetailDTO = new HolderQueueItemDetailDTO();
        holderQueueItemDetailDTO.setDeviceType(0);
        holderQueueItemDetailDTO.setDeviceId("deviceId");
        holderQueueItemDetailDTO.setStoreName("storeName");
        holderQueueItemDetailDTO.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        holderQueueItemDetailDTO.setStoreGuid("storeGuid");
        holderQueueItemDetailDTO.setPhone("phone");
        holderQueueItemDetailDTO.setPeopleNumber((byte) 0b0);
        holderQueueItemDetailDTO.setQueueGuid("queueGuid");
        holderQueueItemDetailDTO.setSort(0);
        holderQueueItemDetailDTO.setBefore(0);
        holderQueueItemDetailDTO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HolderQueueItemDO d = new HolderQueueItemDO();
        d.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        d.setStoreGuid("storeGuid");
        d.setQueueGuid("98dbaed2-ad56-4263-9a2a-85c05a345eea");
        d.setStatus((byte) 0b0);
        d.setPhone("phone");
        d.setPeopleNumber((byte) 0b0);
        d.setSort(0.0f);
        d.setQueueCode("code");
        d.setCode("code");
        d.setCallTimes(0);
        d.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setBusinessDay(LocalDate.of(2020, 1, 1));
        d.setIsEnable(false);
        d.setIsDeleted(false);
        d.setCreateStaffGuid("createStaffGuid");
        d.setModifiedStaffGuid("modifiedStaffGuid");
        d.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setDeviceType(0);
        d.setDeviceId("deviceId");
        d.setTableName("diningTableName");
        d.setTableGuid("diningTableGuid");
        d.setAreaName("areaName");
        when(mockQueueItemMapStruct.toDetailDto(d)).thenReturn(holderQueueItemDetailDTO);

        // Configure QueueItemMapStruct.toDo(...).
        final HolderQueueItemDO holderQueueItemDO = new HolderQueueItemDO();
        holderQueueItemDO.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        holderQueueItemDO.setStoreGuid("storeGuid");
        holderQueueItemDO.setQueueGuid("98dbaed2-ad56-4263-9a2a-85c05a345eea");
        holderQueueItemDO.setStatus((byte) 0b0);
        holderQueueItemDO.setPhone("phone");
        holderQueueItemDO.setPeopleNumber((byte) 0b0);
        holderQueueItemDO.setSort(0.0f);
        holderQueueItemDO.setQueueCode("code");
        holderQueueItemDO.setCode("code");
        holderQueueItemDO.setCallTimes(0);
        holderQueueItemDO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        holderQueueItemDO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        holderQueueItemDO.setBusinessDay(LocalDate.of(2020, 1, 1));
        holderQueueItemDO.setIsEnable(false);
        holderQueueItemDO.setIsDeleted(false);
        holderQueueItemDO.setCreateStaffGuid("createStaffGuid");
        holderQueueItemDO.setModifiedStaffGuid("modifiedStaffGuid");
        holderQueueItemDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        holderQueueItemDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        holderQueueItemDO.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        holderQueueItemDO.setDeviceType(0);
        holderQueueItemDO.setDeviceId("deviceId");
        holderQueueItemDO.setTableName("diningTableName");
        holderQueueItemDO.setTableGuid("diningTableGuid");
        holderQueueItemDO.setAreaName("areaName");
        final HolderQueueItemDTO dto1 = new HolderQueueItemDTO();
        dto1.setDeviceType(0);
        dto1.setDeviceId("deviceId");
        dto1.setStoreName("storeName");
        dto1.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        dto1.setStoreGuid("storeGuid");
        dto1.setPhone("phone");
        dto1.setPeopleNumber((byte) 0b0);
        when(mockQueueItemMapStruct.toDo(dto1)).thenReturn(holderQueueItemDO);

        // Configure BusinessMsgClient.sendMsg(...).
        final BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setMessageGuid("messageGuid");
        businessMessageDTO.setSubject("subject");
        businessMessageDTO.setContent("content");
        businessMessageDTO.setMessageType(0);
        businessMessageDTO.setDetailMessageType(0);
        when(mockBusinessMsgClient.sendMsg(businessMessageDTO)).thenReturn("result");

        // Configure OrganizationClientService.queryStoreByGuid(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("13bab148-d971-47a4-b422-f966bc3fc49a");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        when(mockOrganizationClientService.queryStoreByGuid("storeGuid")).thenReturn(storeDTO);

        when(mockWxClient.getQueueQrCode(new WxQueueInfoReqDTO("queueGuid"))).thenReturn("queueUrl");

        // Run the test
        final HolderQueueItemDetailDTO result = queueItemServiceImplUnderTest.inQueue(dto);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);

        // Confirm PrintClient.print(...).
        final PrintDTO printDTO = new PrintDTO();
        printDTO.setInvoiceType(0);
        printDTO.setStoreGuid("storeGuid");
        printDTO.setPrintUid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        printDTO.setOperatorStaffGuid("operatorStaffGuid");
        printDTO.setOperatorStaffName("operatorStaffName");
        printDTO.setCreateTime(0L);
        printDTO.setDeviceId("deviceId");
        printDTO.setPrintSourceEnum(PrintSourceEnum.AIO);
        verify(mockPrintClient).print(printDTO);
    }

    @Test
    public void testInQueue_QueueMapperReturnsNoItems() {
        // Setup
        final HolderQueueItemDTO dto = new HolderQueueItemDTO();
        dto.setDeviceType(0);
        dto.setDeviceId("deviceId");
        dto.setStoreName("storeName");
        dto.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        dto.setStoreGuid("storeGuid");
        dto.setPhone("phone");
        dto.setPeopleNumber((byte) 0b0);

        final HolderQueueItemDetailDTO expectedResult = new HolderQueueItemDetailDTO();
        expectedResult.setDeviceType(0);
        expectedResult.setDeviceId("deviceId");
        expectedResult.setStoreName("storeName");
        expectedResult.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setPhone("phone");
        expectedResult.setPeopleNumber((byte) 0b0);
        expectedResult.setQueueGuid("queueGuid");
        expectedResult.setSort(0);
        expectedResult.setBefore(0);
        expectedResult.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        when(mockQueueMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());
        when(mockTransactionTemplate.execute(any(TransactionCallback.class))).thenReturn("result");

        // Configure QueueItemMapStruct.toDetailDto(...).
        final HolderQueueItemDetailDTO holderQueueItemDetailDTO = new HolderQueueItemDetailDTO();
        holderQueueItemDetailDTO.setDeviceType(0);
        holderQueueItemDetailDTO.setDeviceId("deviceId");
        holderQueueItemDetailDTO.setStoreName("storeName");
        holderQueueItemDetailDTO.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        holderQueueItemDetailDTO.setStoreGuid("storeGuid");
        holderQueueItemDetailDTO.setPhone("phone");
        holderQueueItemDetailDTO.setPeopleNumber((byte) 0b0);
        holderQueueItemDetailDTO.setQueueGuid("queueGuid");
        holderQueueItemDetailDTO.setSort(0);
        holderQueueItemDetailDTO.setBefore(0);
        holderQueueItemDetailDTO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HolderQueueItemDO d = new HolderQueueItemDO();
        d.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        d.setStoreGuid("storeGuid");
        d.setQueueGuid("98dbaed2-ad56-4263-9a2a-85c05a345eea");
        d.setStatus((byte) 0b0);
        d.setPhone("phone");
        d.setPeopleNumber((byte) 0b0);
        d.setSort(0.0f);
        d.setQueueCode("code");
        d.setCode("code");
        d.setCallTimes(0);
        d.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setBusinessDay(LocalDate.of(2020, 1, 1));
        d.setIsEnable(false);
        d.setIsDeleted(false);
        d.setCreateStaffGuid("createStaffGuid");
        d.setModifiedStaffGuid("modifiedStaffGuid");
        d.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setDeviceType(0);
        d.setDeviceId("deviceId");
        d.setTableName("diningTableName");
        d.setTableGuid("diningTableGuid");
        d.setAreaName("areaName");
        when(mockQueueItemMapStruct.toDetailDto(d)).thenReturn(holderQueueItemDetailDTO);

        // Configure QueueItemMapStruct.toDo(...).
        final HolderQueueItemDO holderQueueItemDO = new HolderQueueItemDO();
        holderQueueItemDO.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        holderQueueItemDO.setStoreGuid("storeGuid");
        holderQueueItemDO.setQueueGuid("98dbaed2-ad56-4263-9a2a-85c05a345eea");
        holderQueueItemDO.setStatus((byte) 0b0);
        holderQueueItemDO.setPhone("phone");
        holderQueueItemDO.setPeopleNumber((byte) 0b0);
        holderQueueItemDO.setSort(0.0f);
        holderQueueItemDO.setQueueCode("code");
        holderQueueItemDO.setCode("code");
        holderQueueItemDO.setCallTimes(0);
        holderQueueItemDO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        holderQueueItemDO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        holderQueueItemDO.setBusinessDay(LocalDate.of(2020, 1, 1));
        holderQueueItemDO.setIsEnable(false);
        holderQueueItemDO.setIsDeleted(false);
        holderQueueItemDO.setCreateStaffGuid("createStaffGuid");
        holderQueueItemDO.setModifiedStaffGuid("modifiedStaffGuid");
        holderQueueItemDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        holderQueueItemDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        holderQueueItemDO.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        holderQueueItemDO.setDeviceType(0);
        holderQueueItemDO.setDeviceId("deviceId");
        holderQueueItemDO.setTableName("diningTableName");
        holderQueueItemDO.setTableGuid("diningTableGuid");
        holderQueueItemDO.setAreaName("areaName");
        final HolderQueueItemDTO dto1 = new HolderQueueItemDTO();
        dto1.setDeviceType(0);
        dto1.setDeviceId("deviceId");
        dto1.setStoreName("storeName");
        dto1.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        dto1.setStoreGuid("storeGuid");
        dto1.setPhone("phone");
        dto1.setPeopleNumber((byte) 0b0);
        when(mockQueueItemMapStruct.toDo(dto1)).thenReturn(holderQueueItemDO);

        // Configure BusinessMsgClient.sendMsg(...).
        final BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setMessageGuid("messageGuid");
        businessMessageDTO.setSubject("subject");
        businessMessageDTO.setContent("content");
        businessMessageDTO.setMessageType(0);
        businessMessageDTO.setDetailMessageType(0);
        when(mockBusinessMsgClient.sendMsg(businessMessageDTO)).thenReturn("result");

        // Configure OrganizationClientService.queryStoreByGuid(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("13bab148-d971-47a4-b422-f966bc3fc49a");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        when(mockOrganizationClientService.queryStoreByGuid("storeGuid")).thenReturn(storeDTO);

        when(mockWxClient.getQueueQrCode(new WxQueueInfoReqDTO("queueGuid"))).thenReturn("queueUrl");

        // Run the test
        final HolderQueueItemDetailDTO result = queueItemServiceImplUnderTest.inQueue(dto);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);

        // Confirm PrintClient.print(...).
        final PrintDTO printDTO = new PrintDTO();
        printDTO.setInvoiceType(0);
        printDTO.setStoreGuid("storeGuid");
        printDTO.setPrintUid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        printDTO.setOperatorStaffGuid("operatorStaffGuid");
        printDTO.setOperatorStaffName("operatorStaffName");
        printDTO.setCreateTime(0L);
        printDTO.setDeviceId("deviceId");
        printDTO.setPrintSourceEnum(PrintSourceEnum.AIO);
        verify(mockPrintClient).print(printDTO);
    }

    @Test
    public void testInQueue_TransactionTemplateReturnsNull() {
        // Setup
        final HolderQueueItemDTO dto = new HolderQueueItemDTO();
        dto.setDeviceType(0);
        dto.setDeviceId("deviceId");
        dto.setStoreName("storeName");
        dto.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        dto.setStoreGuid("storeGuid");
        dto.setPhone("phone");
        dto.setPeopleNumber((byte) 0b0);

        // Configure QueueMapper.selectList(...).
        final HolderQueueDO holderQueueDO = new HolderQueueDO();
        holderQueueDO.setGuid("98dbaed2-ad56-4263-9a2a-85c05a345eea");
        holderQueueDO.setStoreGuid("storeGuid");
        holderQueueDO.setCode("code");
        holderQueueDO.setMin((byte) 0b0);
        holderQueueDO.setMax((byte) 0b0);
        holderQueueDO.setIsEnable(false);
        final List<HolderQueueDO> queueDOList = Arrays.asList(holderQueueDO);
        when(mockQueueMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(queueDOList);

        when(mockTransactionTemplate.execute(any(TransactionCallback.class))).thenReturn(null);

        // Configure QueueItemMapStruct.toDetailDto(...).
        final HolderQueueItemDetailDTO holderQueueItemDetailDTO = new HolderQueueItemDetailDTO();
        holderQueueItemDetailDTO.setDeviceType(0);
        holderQueueItemDetailDTO.setDeviceId("deviceId");
        holderQueueItemDetailDTO.setStoreName("storeName");
        holderQueueItemDetailDTO.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        holderQueueItemDetailDTO.setStoreGuid("storeGuid");
        holderQueueItemDetailDTO.setPhone("phone");
        holderQueueItemDetailDTO.setPeopleNumber((byte) 0b0);
        holderQueueItemDetailDTO.setQueueGuid("queueGuid");
        holderQueueItemDetailDTO.setSort(0);
        holderQueueItemDetailDTO.setBefore(0);
        holderQueueItemDetailDTO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HolderQueueItemDO d = new HolderQueueItemDO();
        d.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        d.setStoreGuid("storeGuid");
        d.setQueueGuid("98dbaed2-ad56-4263-9a2a-85c05a345eea");
        d.setStatus((byte) 0b0);
        d.setPhone("phone");
        d.setPeopleNumber((byte) 0b0);
        d.setSort(0.0f);
        d.setQueueCode("code");
        d.setCode("code");
        d.setCallTimes(0);
        d.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setBusinessDay(LocalDate.of(2020, 1, 1));
        d.setIsEnable(false);
        d.setIsDeleted(false);
        d.setCreateStaffGuid("createStaffGuid");
        d.setModifiedStaffGuid("modifiedStaffGuid");
        d.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setDeviceType(0);
        d.setDeviceId("deviceId");
        d.setTableName("diningTableName");
        d.setTableGuid("diningTableGuid");
        d.setAreaName("areaName");
        when(mockQueueItemMapStruct.toDetailDto(d)).thenReturn(holderQueueItemDetailDTO);

        // Configure QueueItemMapStruct.toDo(...).
        final HolderQueueItemDO holderQueueItemDO = new HolderQueueItemDO();
        holderQueueItemDO.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        holderQueueItemDO.setStoreGuid("storeGuid");
        holderQueueItemDO.setQueueGuid("98dbaed2-ad56-4263-9a2a-85c05a345eea");
        holderQueueItemDO.setStatus((byte) 0b0);
        holderQueueItemDO.setPhone("phone");
        holderQueueItemDO.setPeopleNumber((byte) 0b0);
        holderQueueItemDO.setSort(0.0f);
        holderQueueItemDO.setQueueCode("code");
        holderQueueItemDO.setCode("code");
        holderQueueItemDO.setCallTimes(0);
        holderQueueItemDO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        holderQueueItemDO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        holderQueueItemDO.setBusinessDay(LocalDate.of(2020, 1, 1));
        holderQueueItemDO.setIsEnable(false);
        holderQueueItemDO.setIsDeleted(false);
        holderQueueItemDO.setCreateStaffGuid("createStaffGuid");
        holderQueueItemDO.setModifiedStaffGuid("modifiedStaffGuid");
        holderQueueItemDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        holderQueueItemDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        holderQueueItemDO.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        holderQueueItemDO.setDeviceType(0);
        holderQueueItemDO.setDeviceId("deviceId");
        holderQueueItemDO.setTableName("diningTableName");
        holderQueueItemDO.setTableGuid("diningTableGuid");
        holderQueueItemDO.setAreaName("areaName");
        final HolderQueueItemDTO dto1 = new HolderQueueItemDTO();
        dto1.setDeviceType(0);
        dto1.setDeviceId("deviceId");
        dto1.setStoreName("storeName");
        dto1.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        dto1.setStoreGuid("storeGuid");
        dto1.setPhone("phone");
        dto1.setPeopleNumber((byte) 0b0);
        when(mockQueueItemMapStruct.toDo(dto1)).thenReturn(holderQueueItemDO);

        // Configure BusinessMsgClient.sendMsg(...).
        final BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setMessageGuid("messageGuid");
        businessMessageDTO.setSubject("subject");
        businessMessageDTO.setContent("content");
        businessMessageDTO.setMessageType(0);
        businessMessageDTO.setDetailMessageType(0);
        when(mockBusinessMsgClient.sendMsg(businessMessageDTO)).thenReturn("result");

        // Configure OrganizationClientService.queryStoreByGuid(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("13bab148-d971-47a4-b422-f966bc3fc49a");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        when(mockOrganizationClientService.queryStoreByGuid("storeGuid")).thenReturn(storeDTO);

        when(mockWxClient.getQueueQrCode(new WxQueueInfoReqDTO("queueGuid"))).thenReturn("queueUrl");

        // Run the test
        final HolderQueueItemDetailDTO result = queueItemServiceImplUnderTest.inQueue(dto);

        // Verify the results
        assertThat(result).isNull();

        // Confirm PrintClient.print(...).
        final PrintDTO printDTO = new PrintDTO();
        printDTO.setInvoiceType(0);
        printDTO.setStoreGuid("storeGuid");
        printDTO.setPrintUid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        printDTO.setOperatorStaffGuid("operatorStaffGuid");
        printDTO.setOperatorStaffName("operatorStaffName");
        printDTO.setCreateTime(0L);
        printDTO.setDeviceId("deviceId");
        printDTO.setPrintSourceEnum(PrintSourceEnum.AIO);
        verify(mockPrintClient).print(printDTO);
    }

    @Test
    public void testInQueue_TransactionTemplateThrowsTransactionException() {
        // Setup
        final HolderQueueItemDTO dto = new HolderQueueItemDTO();
        dto.setDeviceType(0);
        dto.setDeviceId("deviceId");
        dto.setStoreName("storeName");
        dto.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        dto.setStoreGuid("storeGuid");
        dto.setPhone("phone");
        dto.setPeopleNumber((byte) 0b0);

        // Configure QueueMapper.selectList(...).
        final HolderQueueDO holderQueueDO = new HolderQueueDO();
        holderQueueDO.setGuid("98dbaed2-ad56-4263-9a2a-85c05a345eea");
        holderQueueDO.setStoreGuid("storeGuid");
        holderQueueDO.setCode("code");
        holderQueueDO.setMin((byte) 0b0);
        holderQueueDO.setMax((byte) 0b0);
        holderQueueDO.setIsEnable(false);
        final List<HolderQueueDO> queueDOList = Arrays.asList(holderQueueDO);
        when(mockQueueMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(queueDOList);

        when(mockTransactionTemplate.execute(any(TransactionCallback.class))).thenThrow(TransactionException.class);

        // Run the test
        assertThatThrownBy(() -> queueItemServiceImplUnderTest.inQueue(dto)).isInstanceOf(BusinessException.class);
    }

    @Test
    public void testDoinTransaction() {
        // Setup
        when(mockTransactionTemplate.execute(any(TransactionCallback.class))).thenReturn("result");

        // Run the test
        final String result = queueItemServiceImplUnderTest.doinTransaction(() -> "value");

        // Verify the results
        assertThat(result).isEqualTo("result");
    }

    @Test
    public void testDoinTransaction_TransactionTemplateReturnsNull() {
        // Setup
        when(mockTransactionTemplate.execute(any(TransactionCallback.class))).thenReturn(null);

        // Run the test
        final String result = queueItemServiceImplUnderTest.doinTransaction(() -> "value");

        // Verify the results
        assertThat(result).isNull();
    }

    @Test
    public void testDoinTransaction_TransactionTemplateThrowsTransactionException() {
        // Setup
        when(mockTransactionTemplate.execute(any(TransactionCallback.class))).thenThrow(TransactionException.class);

        // Run the test
        assertThatThrownBy(() -> queueItemServiceImplUnderTest.doinTransaction(() -> "value"))
                .isInstanceOf(TransactionException.class);
    }

    @Test
    public void testSendMessage() {
        // Setup
        final HolderQueueItemDetailDTO result1 = new HolderQueueItemDetailDTO();
        result1.setDeviceType(0);
        result1.setDeviceId("deviceId");
        result1.setStoreName("storeName");
        result1.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        result1.setStoreGuid("storeGuid");
        result1.setPhone("phone");
        result1.setPeopleNumber((byte) 0b0);
        result1.setQueueGuid("queueGuid");
        result1.setSort(0);
        result1.setBefore(0);
        result1.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        final BaseDTO dto = new BaseDTO();
        dto.setDeviceType(0);
        dto.setDeviceId("deviceId");
        dto.setEnterpriseGuid("enterpriseGuid");
        dto.setStoreGuid("storeGuid");
        dto.setStoreName("storeName");

        // Configure BusinessMsgClient.sendMsg(...).
        final BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setMessageGuid("messageGuid");
        businessMessageDTO.setSubject("subject");
        businessMessageDTO.setContent("content");
        businessMessageDTO.setMessageType(0);
        businessMessageDTO.setDetailMessageType(0);
        when(mockBusinessMsgClient.sendMsg(businessMessageDTO)).thenReturn("result");

        // Run the test
        queueItemServiceImplUnderTest.sendMessage(result1, dto);

        // Verify the results
    }

    @Test
    public void testPrint() {
        // Setup
        final HolderQueueItemDetailDTO result1 = new HolderQueueItemDetailDTO();
        result1.setDeviceType(0);
        result1.setDeviceId("deviceId");
        result1.setStoreName("storeName");
        result1.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        result1.setStoreGuid("storeGuid");
        result1.setPhone("phone");
        result1.setPeopleNumber((byte) 0b0);
        result1.setQueueGuid("queueGuid");
        result1.setSort(0);
        result1.setBefore(0);
        result1.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        final BaseDTO dto = new BaseDTO();
        dto.setDeviceType(0);
        dto.setDeviceId("deviceId");
        dto.setEnterpriseGuid("enterpriseGuid");
        dto.setStoreGuid("storeGuid");
        dto.setStoreName("storeName");

        // Configure OrganizationClientService.queryStoreByGuid(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("13bab148-d971-47a4-b422-f966bc3fc49a");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        when(mockOrganizationClientService.queryStoreByGuid("storeGuid")).thenReturn(storeDTO);

        when(mockWxClient.getQueueQrCode(new WxQueueInfoReqDTO("queueGuid"))).thenReturn("queueUrl");

        // Run the test
        queueItemServiceImplUnderTest.print(result1, dto);

        // Verify the results
        // Confirm PrintClient.print(...).
        final PrintDTO printDTO = new PrintDTO();
        printDTO.setInvoiceType(0);
        printDTO.setStoreGuid("storeGuid");
        printDTO.setPrintUid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        printDTO.setOperatorStaffGuid("operatorStaffGuid");
        printDTO.setOperatorStaffName("operatorStaffName");
        printDTO.setCreateTime(0L);
        printDTO.setDeviceId("deviceId");
        printDTO.setPrintSourceEnum(PrintSourceEnum.AIO);
        verify(mockPrintClient).print(printDTO);
    }

    @Test
    public void testBuildQrCode() {
        // Setup
        final HolderQueueItemDetailDTO result1 = new HolderQueueItemDetailDTO();
        result1.setDeviceType(0);
        result1.setDeviceId("deviceId");
        result1.setStoreName("storeName");
        result1.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        result1.setStoreGuid("storeGuid");
        result1.setPhone("phone");
        result1.setPeopleNumber((byte) 0b0);
        result1.setQueueGuid("queueGuid");
        result1.setSort(0);
        result1.setBefore(0);
        result1.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        final BaseDTO dto = new BaseDTO();
        dto.setDeviceType(0);
        dto.setDeviceId("deviceId");
        dto.setEnterpriseGuid("enterpriseGuid");
        dto.setStoreGuid("storeGuid");
        dto.setStoreName("storeName");

        when(mockWxClient.getQueueQrCode(new WxQueueInfoReqDTO("queueGuid"))).thenReturn("queueUrl");

        // Run the test
        final String result = queueItemServiceImplUnderTest.buildQrCode(result1, dto);

        // Verify the results
        assertThat(result).isEqualTo("queueUrl");
    }

    @Test
    public void testCall() {
        // Setup
        final ItemGuidDTO dto = new ItemGuidDTO();
        dto.setDeviceType(0);
        dto.setDeviceId("deviceId");
        dto.setStoreGuid("storeGuid");
        dto.setStoreName("storeName");
        dto.setItemGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        dto.setBrandGuid("brandGuid");
        dto.setEnterpriseGuid("enterpriseGuid");

        final HolderQueueItemDetailDTO expectedResult = new HolderQueueItemDetailDTO();
        expectedResult.setDeviceType(0);
        expectedResult.setDeviceId("deviceId");
        expectedResult.setStoreName("storeName");
        expectedResult.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setPhone("phone");
        expectedResult.setPeopleNumber((byte) 0b0);
        expectedResult.setQueueGuid("queueGuid");
        expectedResult.setSort(0);
        expectedResult.setBefore(0);
        expectedResult.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        when(mockTransactionTemplate.execute(any(TransactionCallback.class))).thenReturn("result");

        // Configure QueueItemMapStruct.toDetailDto(...).
        final HolderQueueItemDetailDTO holderQueueItemDetailDTO = new HolderQueueItemDetailDTO();
        holderQueueItemDetailDTO.setDeviceType(0);
        holderQueueItemDetailDTO.setDeviceId("deviceId");
        holderQueueItemDetailDTO.setStoreName("storeName");
        holderQueueItemDetailDTO.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        holderQueueItemDetailDTO.setStoreGuid("storeGuid");
        holderQueueItemDetailDTO.setPhone("phone");
        holderQueueItemDetailDTO.setPeopleNumber((byte) 0b0);
        holderQueueItemDetailDTO.setQueueGuid("queueGuid");
        holderQueueItemDetailDTO.setSort(0);
        holderQueueItemDetailDTO.setBefore(0);
        holderQueueItemDetailDTO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HolderQueueItemDO d = new HolderQueueItemDO();
        d.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        d.setStoreGuid("storeGuid");
        d.setQueueGuid("98dbaed2-ad56-4263-9a2a-85c05a345eea");
        d.setStatus((byte) 0b0);
        d.setPhone("phone");
        d.setPeopleNumber((byte) 0b0);
        d.setSort(0.0f);
        d.setQueueCode("code");
        d.setCode("code");
        d.setCallTimes(0);
        d.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setBusinessDay(LocalDate.of(2020, 1, 1));
        d.setIsEnable(false);
        d.setIsDeleted(false);
        d.setCreateStaffGuid("createStaffGuid");
        d.setModifiedStaffGuid("modifiedStaffGuid");
        d.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setDeviceType(0);
        d.setDeviceId("deviceId");
        d.setTableName("diningTableName");
        d.setTableGuid("diningTableGuid");
        d.setAreaName("areaName");
        when(mockQueueItemMapStruct.toDetailDto(d)).thenReturn(holderQueueItemDetailDTO);

        // Configure BusinessMsgClient.sendMsg(...).
        final BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setMessageGuid("messageGuid");
        businessMessageDTO.setSubject("subject");
        businessMessageDTO.setContent("content");
        businessMessageDTO.setMessageType(0);
        businessMessageDTO.setDetailMessageType(0);
        when(mockBusinessMsgClient.sendMsg(businessMessageDTO)).thenReturn("result");

        // Run the test
        final HolderQueueItemDetailDTO result = queueItemServiceImplUnderTest.call(dto);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);

        // Confirm WxClient.callUpNotify(...).
        final ItemGuidDTO itemGuidDTO1 = new ItemGuidDTO();
        itemGuidDTO1.setDeviceType(0);
        itemGuidDTO1.setDeviceId("deviceId");
        itemGuidDTO1.setStoreGuid("storeGuid");
        itemGuidDTO1.setStoreName("storeName");
        itemGuidDTO1.setItemGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        itemGuidDTO1.setBrandGuid("brandGuid");
        itemGuidDTO1.setEnterpriseGuid("enterpriseGuid");
        final List<ItemGuidDTO> itemGuidDTO = Arrays.asList(itemGuidDTO1);
        verify(mockWxClient).callUpNotify(itemGuidDTO);
    }

    @Test
    public void testCall_TransactionTemplateReturnsNull() {
        // Setup
        final ItemGuidDTO dto = new ItemGuidDTO();
        dto.setDeviceType(0);
        dto.setDeviceId("deviceId");
        dto.setStoreGuid("storeGuid");
        dto.setStoreName("storeName");
        dto.setItemGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        dto.setBrandGuid("brandGuid");
        dto.setEnterpriseGuid("enterpriseGuid");

        when(mockTransactionTemplate.execute(any(TransactionCallback.class))).thenReturn(null);

        // Configure QueueItemMapStruct.toDetailDto(...).
        final HolderQueueItemDetailDTO holderQueueItemDetailDTO = new HolderQueueItemDetailDTO();
        holderQueueItemDetailDTO.setDeviceType(0);
        holderQueueItemDetailDTO.setDeviceId("deviceId");
        holderQueueItemDetailDTO.setStoreName("storeName");
        holderQueueItemDetailDTO.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        holderQueueItemDetailDTO.setStoreGuid("storeGuid");
        holderQueueItemDetailDTO.setPhone("phone");
        holderQueueItemDetailDTO.setPeopleNumber((byte) 0b0);
        holderQueueItemDetailDTO.setQueueGuid("queueGuid");
        holderQueueItemDetailDTO.setSort(0);
        holderQueueItemDetailDTO.setBefore(0);
        holderQueueItemDetailDTO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HolderQueueItemDO d = new HolderQueueItemDO();
        d.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        d.setStoreGuid("storeGuid");
        d.setQueueGuid("98dbaed2-ad56-4263-9a2a-85c05a345eea");
        d.setStatus((byte) 0b0);
        d.setPhone("phone");
        d.setPeopleNumber((byte) 0b0);
        d.setSort(0.0f);
        d.setQueueCode("code");
        d.setCode("code");
        d.setCallTimes(0);
        d.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setBusinessDay(LocalDate.of(2020, 1, 1));
        d.setIsEnable(false);
        d.setIsDeleted(false);
        d.setCreateStaffGuid("createStaffGuid");
        d.setModifiedStaffGuid("modifiedStaffGuid");
        d.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setDeviceType(0);
        d.setDeviceId("deviceId");
        d.setTableName("diningTableName");
        d.setTableGuid("diningTableGuid");
        d.setAreaName("areaName");
        when(mockQueueItemMapStruct.toDetailDto(d)).thenReturn(holderQueueItemDetailDTO);

        // Configure BusinessMsgClient.sendMsg(...).
        final BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setMessageGuid("messageGuid");
        businessMessageDTO.setSubject("subject");
        businessMessageDTO.setContent("content");
        businessMessageDTO.setMessageType(0);
        businessMessageDTO.setDetailMessageType(0);
        when(mockBusinessMsgClient.sendMsg(businessMessageDTO)).thenReturn("result");

        // Run the test
        final HolderQueueItemDetailDTO result = queueItemServiceImplUnderTest.call(dto);

        // Verify the results
        assertThat(result).isNull();

        // Confirm WxClient.callUpNotify(...).
        final ItemGuidDTO itemGuidDTO1 = new ItemGuidDTO();
        itemGuidDTO1.setDeviceType(0);
        itemGuidDTO1.setDeviceId("deviceId");
        itemGuidDTO1.setStoreGuid("storeGuid");
        itemGuidDTO1.setStoreName("storeName");
        itemGuidDTO1.setItemGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        itemGuidDTO1.setBrandGuid("brandGuid");
        itemGuidDTO1.setEnterpriseGuid("enterpriseGuid");
        final List<ItemGuidDTO> itemGuidDTO = Arrays.asList(itemGuidDTO1);
        verify(mockWxClient).callUpNotify(itemGuidDTO);
    }

    @Test
    public void testCall_TransactionTemplateThrowsTransactionException() {
        // Setup
        final ItemGuidDTO dto = new ItemGuidDTO();
        dto.setDeviceType(0);
        dto.setDeviceId("deviceId");
        dto.setStoreGuid("storeGuid");
        dto.setStoreName("storeName");
        dto.setItemGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        dto.setBrandGuid("brandGuid");
        dto.setEnterpriseGuid("enterpriseGuid");

        when(mockTransactionTemplate.execute(any(TransactionCallback.class))).thenThrow(TransactionException.class);

        // Run the test
        assertThatThrownBy(() -> queueItemServiceImplUnderTest.call(dto)).isInstanceOf(TransactionException.class);
    }

    @Test
    public void testOrder() {
        // Setup
        // Run the test
        final Integer result = queueItemServiceImplUnderTest.order("98dbaed2-ad56-4263-9a2a-85c05a345eea");

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    public void testPreCount() {
        // Setup
        // Run the test
        final Integer result = queueItemServiceImplUnderTest.preCount("98dbaed2-ad56-4263-9a2a-85c05a345eea", 0);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    public void testPass() {
        // Setup
        final ItemGuidDTO dto = new ItemGuidDTO();
        dto.setDeviceType(0);
        dto.setDeviceId("deviceId");
        dto.setStoreGuid("storeGuid");
        dto.setStoreName("storeName");
        dto.setItemGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        dto.setBrandGuid("brandGuid");
        dto.setEnterpriseGuid("enterpriseGuid");

        final HolderQueueItemDetailDTO expectedResult = new HolderQueueItemDetailDTO();
        expectedResult.setDeviceType(0);
        expectedResult.setDeviceId("deviceId");
        expectedResult.setStoreName("storeName");
        expectedResult.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setPhone("phone");
        expectedResult.setPeopleNumber((byte) 0b0);
        expectedResult.setQueueGuid("queueGuid");
        expectedResult.setSort(0);
        expectedResult.setBefore(0);
        expectedResult.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        when(mockTransactionTemplate.execute(any(TransactionCallback.class))).thenReturn("result");

        // Configure QueueItemMapStruct.toDetailDto(...).
        final HolderQueueItemDetailDTO holderQueueItemDetailDTO = new HolderQueueItemDetailDTO();
        holderQueueItemDetailDTO.setDeviceType(0);
        holderQueueItemDetailDTO.setDeviceId("deviceId");
        holderQueueItemDetailDTO.setStoreName("storeName");
        holderQueueItemDetailDTO.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        holderQueueItemDetailDTO.setStoreGuid("storeGuid");
        holderQueueItemDetailDTO.setPhone("phone");
        holderQueueItemDetailDTO.setPeopleNumber((byte) 0b0);
        holderQueueItemDetailDTO.setQueueGuid("queueGuid");
        holderQueueItemDetailDTO.setSort(0);
        holderQueueItemDetailDTO.setBefore(0);
        holderQueueItemDetailDTO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HolderQueueItemDO d = new HolderQueueItemDO();
        d.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        d.setStoreGuid("storeGuid");
        d.setQueueGuid("98dbaed2-ad56-4263-9a2a-85c05a345eea");
        d.setStatus((byte) 0b0);
        d.setPhone("phone");
        d.setPeopleNumber((byte) 0b0);
        d.setSort(0.0f);
        d.setQueueCode("code");
        d.setCode("code");
        d.setCallTimes(0);
        d.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setBusinessDay(LocalDate.of(2020, 1, 1));
        d.setIsEnable(false);
        d.setIsDeleted(false);
        d.setCreateStaffGuid("createStaffGuid");
        d.setModifiedStaffGuid("modifiedStaffGuid");
        d.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setDeviceType(0);
        d.setDeviceId("deviceId");
        d.setTableName("diningTableName");
        d.setTableGuid("diningTableGuid");
        d.setAreaName("areaName");
        when(mockQueueItemMapStruct.toDetailDto(d)).thenReturn(holderQueueItemDetailDTO);

        // Configure BusinessMsgClient.sendMsg(...).
        final BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setMessageGuid("messageGuid");
        businessMessageDTO.setSubject("subject");
        businessMessageDTO.setContent("content");
        businessMessageDTO.setMessageType(0);
        businessMessageDTO.setDetailMessageType(0);
        when(mockBusinessMsgClient.sendMsg(businessMessageDTO)).thenReturn("result");

        // Run the test
        final HolderQueueItemDetailDTO result = queueItemServiceImplUnderTest.pass(dto);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);

        // Confirm WxClient.callUpNotify(...).
        final ItemGuidDTO itemGuidDTO1 = new ItemGuidDTO();
        itemGuidDTO1.setDeviceType(0);
        itemGuidDTO1.setDeviceId("deviceId");
        itemGuidDTO1.setStoreGuid("storeGuid");
        itemGuidDTO1.setStoreName("storeName");
        itemGuidDTO1.setItemGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        itemGuidDTO1.setBrandGuid("brandGuid");
        itemGuidDTO1.setEnterpriseGuid("enterpriseGuid");
        final List<ItemGuidDTO> itemGuidDTO = Arrays.asList(itemGuidDTO1);
        verify(mockWxClient).callUpNotify(itemGuidDTO);
    }

    @Test
    public void testPass_TransactionTemplateReturnsNull() {
        // Setup
        final ItemGuidDTO dto = new ItemGuidDTO();
        dto.setDeviceType(0);
        dto.setDeviceId("deviceId");
        dto.setStoreGuid("storeGuid");
        dto.setStoreName("storeName");
        dto.setItemGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        dto.setBrandGuid("brandGuid");
        dto.setEnterpriseGuid("enterpriseGuid");

        when(mockTransactionTemplate.execute(any(TransactionCallback.class))).thenReturn(null);

        // Configure QueueItemMapStruct.toDetailDto(...).
        final HolderQueueItemDetailDTO holderQueueItemDetailDTO = new HolderQueueItemDetailDTO();
        holderQueueItemDetailDTO.setDeviceType(0);
        holderQueueItemDetailDTO.setDeviceId("deviceId");
        holderQueueItemDetailDTO.setStoreName("storeName");
        holderQueueItemDetailDTO.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        holderQueueItemDetailDTO.setStoreGuid("storeGuid");
        holderQueueItemDetailDTO.setPhone("phone");
        holderQueueItemDetailDTO.setPeopleNumber((byte) 0b0);
        holderQueueItemDetailDTO.setQueueGuid("queueGuid");
        holderQueueItemDetailDTO.setSort(0);
        holderQueueItemDetailDTO.setBefore(0);
        holderQueueItemDetailDTO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HolderQueueItemDO d = new HolderQueueItemDO();
        d.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        d.setStoreGuid("storeGuid");
        d.setQueueGuid("98dbaed2-ad56-4263-9a2a-85c05a345eea");
        d.setStatus((byte) 0b0);
        d.setPhone("phone");
        d.setPeopleNumber((byte) 0b0);
        d.setSort(0.0f);
        d.setQueueCode("code");
        d.setCode("code");
        d.setCallTimes(0);
        d.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setBusinessDay(LocalDate.of(2020, 1, 1));
        d.setIsEnable(false);
        d.setIsDeleted(false);
        d.setCreateStaffGuid("createStaffGuid");
        d.setModifiedStaffGuid("modifiedStaffGuid");
        d.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setDeviceType(0);
        d.setDeviceId("deviceId");
        d.setTableName("diningTableName");
        d.setTableGuid("diningTableGuid");
        d.setAreaName("areaName");
        when(mockQueueItemMapStruct.toDetailDto(d)).thenReturn(holderQueueItemDetailDTO);

        // Configure BusinessMsgClient.sendMsg(...).
        final BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setMessageGuid("messageGuid");
        businessMessageDTO.setSubject("subject");
        businessMessageDTO.setContent("content");
        businessMessageDTO.setMessageType(0);
        businessMessageDTO.setDetailMessageType(0);
        when(mockBusinessMsgClient.sendMsg(businessMessageDTO)).thenReturn("result");

        // Run the test
        final HolderQueueItemDetailDTO result = queueItemServiceImplUnderTest.pass(dto);

        // Verify the results
        assertThat(result).isNull();

        // Confirm WxClient.callUpNotify(...).
        final ItemGuidDTO itemGuidDTO1 = new ItemGuidDTO();
        itemGuidDTO1.setDeviceType(0);
        itemGuidDTO1.setDeviceId("deviceId");
        itemGuidDTO1.setStoreGuid("storeGuid");
        itemGuidDTO1.setStoreName("storeName");
        itemGuidDTO1.setItemGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        itemGuidDTO1.setBrandGuid("brandGuid");
        itemGuidDTO1.setEnterpriseGuid("enterpriseGuid");
        final List<ItemGuidDTO> itemGuidDTO = Arrays.asList(itemGuidDTO1);
        verify(mockWxClient).callUpNotify(itemGuidDTO);
    }

    @Test
    public void testPass_TransactionTemplateThrowsTransactionException() {
        // Setup
        final ItemGuidDTO dto = new ItemGuidDTO();
        dto.setDeviceType(0);
        dto.setDeviceId("deviceId");
        dto.setStoreGuid("storeGuid");
        dto.setStoreName("storeName");
        dto.setItemGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        dto.setBrandGuid("brandGuid");
        dto.setEnterpriseGuid("enterpriseGuid");

        when(mockTransactionTemplate.execute(any(TransactionCallback.class))).thenThrow(TransactionException.class);

        // Run the test
        assertThatThrownBy(() -> queueItemServiceImplUnderTest.pass(dto)).isInstanceOf(TransactionException.class);
    }

    @Test
    public void testRecover() {
        // Setup
        final ItemGuidDTO dto = new ItemGuidDTO();
        dto.setDeviceType(0);
        dto.setDeviceId("deviceId");
        dto.setStoreGuid("storeGuid");
        dto.setStoreName("storeName");
        dto.setItemGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        dto.setBrandGuid("brandGuid");
        dto.setEnterpriseGuid("enterpriseGuid");

        final HolderQueueItemDetailDTO expectedResult = new HolderQueueItemDetailDTO();
        expectedResult.setDeviceType(0);
        expectedResult.setDeviceId("deviceId");
        expectedResult.setStoreName("storeName");
        expectedResult.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setPhone("phone");
        expectedResult.setPeopleNumber((byte) 0b0);
        expectedResult.setQueueGuid("queueGuid");
        expectedResult.setSort(0);
        expectedResult.setBefore(0);
        expectedResult.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        when(mockTransactionTemplate.execute(any(TransactionCallback.class))).thenReturn("result");

        // Configure QueueConfigService.obtain(...).
        final StoreConfigDTO storeConfigDTO = new StoreConfigDTO();
        storeConfigDTO.setGuid("f4eeaaad-3623-4267-9c62-8fb13c0e6eea");
        storeConfigDTO.setStoreGuid("storeGuid");
        storeConfigDTO.setIsEnableEat(false);
        storeConfigDTO.setIsEnableRecovery(false);
        storeConfigDTO.setRecoveryNum("recoveryNum");
        when(mockQueueConfigService.obtain("storeGuid")).thenReturn(storeConfigDTO);

        // Configure QueueItemMapStruct.toDetailDto(...).
        final HolderQueueItemDetailDTO holderQueueItemDetailDTO = new HolderQueueItemDetailDTO();
        holderQueueItemDetailDTO.setDeviceType(0);
        holderQueueItemDetailDTO.setDeviceId("deviceId");
        holderQueueItemDetailDTO.setStoreName("storeName");
        holderQueueItemDetailDTO.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        holderQueueItemDetailDTO.setStoreGuid("storeGuid");
        holderQueueItemDetailDTO.setPhone("phone");
        holderQueueItemDetailDTO.setPeopleNumber((byte) 0b0);
        holderQueueItemDetailDTO.setQueueGuid("queueGuid");
        holderQueueItemDetailDTO.setSort(0);
        holderQueueItemDetailDTO.setBefore(0);
        holderQueueItemDetailDTO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HolderQueueItemDO d = new HolderQueueItemDO();
        d.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        d.setStoreGuid("storeGuid");
        d.setQueueGuid("98dbaed2-ad56-4263-9a2a-85c05a345eea");
        d.setStatus((byte) 0b0);
        d.setPhone("phone");
        d.setPeopleNumber((byte) 0b0);
        d.setSort(0.0f);
        d.setQueueCode("code");
        d.setCode("code");
        d.setCallTimes(0);
        d.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setBusinessDay(LocalDate.of(2020, 1, 1));
        d.setIsEnable(false);
        d.setIsDeleted(false);
        d.setCreateStaffGuid("createStaffGuid");
        d.setModifiedStaffGuid("modifiedStaffGuid");
        d.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setDeviceType(0);
        d.setDeviceId("deviceId");
        d.setTableName("diningTableName");
        d.setTableGuid("diningTableGuid");
        d.setAreaName("areaName");
        when(mockQueueItemMapStruct.toDetailDto(d)).thenReturn(holderQueueItemDetailDTO);

        // Configure BusinessMsgClient.sendMsg(...).
        final BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setMessageGuid("messageGuid");
        businessMessageDTO.setSubject("subject");
        businessMessageDTO.setContent("content");
        businessMessageDTO.setMessageType(0);
        businessMessageDTO.setDetailMessageType(0);
        when(mockBusinessMsgClient.sendMsg(businessMessageDTO)).thenReturn("result");

        // Run the test
        final HolderQueueItemDetailDTO result = queueItemServiceImplUnderTest.recover(dto);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testRecover_TransactionTemplateReturnsNull() {
        // Setup
        final ItemGuidDTO dto = new ItemGuidDTO();
        dto.setDeviceType(0);
        dto.setDeviceId("deviceId");
        dto.setStoreGuid("storeGuid");
        dto.setStoreName("storeName");
        dto.setItemGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        dto.setBrandGuid("brandGuid");
        dto.setEnterpriseGuid("enterpriseGuid");

        when(mockTransactionTemplate.execute(any(TransactionCallback.class))).thenReturn(null);

        // Configure QueueConfigService.obtain(...).
        final StoreConfigDTO storeConfigDTO = new StoreConfigDTO();
        storeConfigDTO.setGuid("f4eeaaad-3623-4267-9c62-8fb13c0e6eea");
        storeConfigDTO.setStoreGuid("storeGuid");
        storeConfigDTO.setIsEnableEat(false);
        storeConfigDTO.setIsEnableRecovery(false);
        storeConfigDTO.setRecoveryNum("recoveryNum");
        when(mockQueueConfigService.obtain("storeGuid")).thenReturn(storeConfigDTO);

        // Configure QueueItemMapStruct.toDetailDto(...).
        final HolderQueueItemDetailDTO holderQueueItemDetailDTO = new HolderQueueItemDetailDTO();
        holderQueueItemDetailDTO.setDeviceType(0);
        holderQueueItemDetailDTO.setDeviceId("deviceId");
        holderQueueItemDetailDTO.setStoreName("storeName");
        holderQueueItemDetailDTO.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        holderQueueItemDetailDTO.setStoreGuid("storeGuid");
        holderQueueItemDetailDTO.setPhone("phone");
        holderQueueItemDetailDTO.setPeopleNumber((byte) 0b0);
        holderQueueItemDetailDTO.setQueueGuid("queueGuid");
        holderQueueItemDetailDTO.setSort(0);
        holderQueueItemDetailDTO.setBefore(0);
        holderQueueItemDetailDTO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HolderQueueItemDO d = new HolderQueueItemDO();
        d.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        d.setStoreGuid("storeGuid");
        d.setQueueGuid("98dbaed2-ad56-4263-9a2a-85c05a345eea");
        d.setStatus((byte) 0b0);
        d.setPhone("phone");
        d.setPeopleNumber((byte) 0b0);
        d.setSort(0.0f);
        d.setQueueCode("code");
        d.setCode("code");
        d.setCallTimes(0);
        d.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setBusinessDay(LocalDate.of(2020, 1, 1));
        d.setIsEnable(false);
        d.setIsDeleted(false);
        d.setCreateStaffGuid("createStaffGuid");
        d.setModifiedStaffGuid("modifiedStaffGuid");
        d.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setDeviceType(0);
        d.setDeviceId("deviceId");
        d.setTableName("diningTableName");
        d.setTableGuid("diningTableGuid");
        d.setAreaName("areaName");
        when(mockQueueItemMapStruct.toDetailDto(d)).thenReturn(holderQueueItemDetailDTO);

        // Configure BusinessMsgClient.sendMsg(...).
        final BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setMessageGuid("messageGuid");
        businessMessageDTO.setSubject("subject");
        businessMessageDTO.setContent("content");
        businessMessageDTO.setMessageType(0);
        businessMessageDTO.setDetailMessageType(0);
        when(mockBusinessMsgClient.sendMsg(businessMessageDTO)).thenReturn("result");

        // Run the test
        final HolderQueueItemDetailDTO result = queueItemServiceImplUnderTest.recover(dto);

        // Verify the results
        assertThat(result).isNull();
    }

    @Test
    public void testRecover_TransactionTemplateThrowsTransactionException() {
        // Setup
        final ItemGuidDTO dto = new ItemGuidDTO();
        dto.setDeviceType(0);
        dto.setDeviceId("deviceId");
        dto.setStoreGuid("storeGuid");
        dto.setStoreName("storeName");
        dto.setItemGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        dto.setBrandGuid("brandGuid");
        dto.setEnterpriseGuid("enterpriseGuid");

        when(mockTransactionTemplate.execute(any(TransactionCallback.class))).thenThrow(TransactionException.class);

        // Run the test
        assertThatThrownBy(() -> queueItemServiceImplUnderTest.recover(dto)).isInstanceOf(TransactionException.class);
    }

    @Test
    public void testRecover_QueueConfigServiceReturnsNull() {
        // Setup
        final ItemGuidDTO dto = new ItemGuidDTO();
        dto.setDeviceType(0);
        dto.setDeviceId("deviceId");
        dto.setStoreGuid("storeGuid");
        dto.setStoreName("storeName");
        dto.setItemGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        dto.setBrandGuid("brandGuid");
        dto.setEnterpriseGuid("enterpriseGuid");

        final HolderQueueItemDetailDTO expectedResult = new HolderQueueItemDetailDTO();
        expectedResult.setDeviceType(0);
        expectedResult.setDeviceId("deviceId");
        expectedResult.setStoreName("storeName");
        expectedResult.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setPhone("phone");
        expectedResult.setPeopleNumber((byte) 0b0);
        expectedResult.setQueueGuid("queueGuid");
        expectedResult.setSort(0);
        expectedResult.setBefore(0);
        expectedResult.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        when(mockTransactionTemplate.execute(any(TransactionCallback.class))).thenReturn("result");
        when(mockQueueConfigService.obtain("storeGuid")).thenReturn(null);

        // Configure QueueItemMapStruct.toDetailDto(...).
        final HolderQueueItemDetailDTO holderQueueItemDetailDTO = new HolderQueueItemDetailDTO();
        holderQueueItemDetailDTO.setDeviceType(0);
        holderQueueItemDetailDTO.setDeviceId("deviceId");
        holderQueueItemDetailDTO.setStoreName("storeName");
        holderQueueItemDetailDTO.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        holderQueueItemDetailDTO.setStoreGuid("storeGuid");
        holderQueueItemDetailDTO.setPhone("phone");
        holderQueueItemDetailDTO.setPeopleNumber((byte) 0b0);
        holderQueueItemDetailDTO.setQueueGuid("queueGuid");
        holderQueueItemDetailDTO.setSort(0);
        holderQueueItemDetailDTO.setBefore(0);
        holderQueueItemDetailDTO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HolderQueueItemDO d = new HolderQueueItemDO();
        d.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        d.setStoreGuid("storeGuid");
        d.setQueueGuid("98dbaed2-ad56-4263-9a2a-85c05a345eea");
        d.setStatus((byte) 0b0);
        d.setPhone("phone");
        d.setPeopleNumber((byte) 0b0);
        d.setSort(0.0f);
        d.setQueueCode("code");
        d.setCode("code");
        d.setCallTimes(0);
        d.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setBusinessDay(LocalDate.of(2020, 1, 1));
        d.setIsEnable(false);
        d.setIsDeleted(false);
        d.setCreateStaffGuid("createStaffGuid");
        d.setModifiedStaffGuid("modifiedStaffGuid");
        d.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setDeviceType(0);
        d.setDeviceId("deviceId");
        d.setTableName("diningTableName");
        d.setTableGuid("diningTableGuid");
        d.setAreaName("areaName");
        when(mockQueueItemMapStruct.toDetailDto(d)).thenReturn(holderQueueItemDetailDTO);

        // Configure BusinessMsgClient.sendMsg(...).
        final BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setMessageGuid("messageGuid");
        businessMessageDTO.setSubject("subject");
        businessMessageDTO.setContent("content");
        businessMessageDTO.setMessageType(0);
        businessMessageDTO.setDetailMessageType(0);
        when(mockBusinessMsgClient.sendMsg(businessMessageDTO)).thenReturn("result");

        // Run the test
        final HolderQueueItemDetailDTO result = queueItemServiceImplUnderTest.recover(dto);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testObtain() {
        // Setup
        final ItemGuidDTO dto = new ItemGuidDTO();
        dto.setDeviceType(0);
        dto.setDeviceId("deviceId");
        dto.setStoreGuid("storeGuid");
        dto.setStoreName("storeName");
        dto.setItemGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        dto.setBrandGuid("brandGuid");
        dto.setEnterpriseGuid("enterpriseGuid");

        final HolderQueueDTO holderQueueDTO = new HolderQueueDTO();
        holderQueueDTO.setGuid("8b195f90-2912-45e7-9b98-171c4b87236e");
        holderQueueDTO.setStoreGuid("storeGuid");
        holderQueueDTO.setBrandName("brandName");
        holderQueueDTO.setLogoUrl("logoUrl");
        holderQueueDTO.setStoreName("name");
        final HolderQueueItemDetailDTO holderQueueItemDetailDTO = new HolderQueueItemDetailDTO();
        holderQueueItemDetailDTO.setDeviceType(0);
        holderQueueItemDetailDTO.setDeviceId("deviceId");
        holderQueueItemDetailDTO.setStoreName("storeName");
        holderQueueItemDetailDTO.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        holderQueueItemDetailDTO.setStoreGuid("storeGuid");
        holderQueueItemDetailDTO.setPhone("phone");
        holderQueueItemDetailDTO.setPeopleNumber((byte) 0b0);
        holderQueueItemDetailDTO.setQueueGuid("queueGuid");
        holderQueueItemDetailDTO.setSort(0);
        holderQueueItemDetailDTO.setBefore(0);
        holderQueueItemDetailDTO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HolderQueueQueueRecordDTO expectedResult = new HolderQueueQueueRecordDTO(holderQueueDTO,
                holderQueueItemDetailDTO);

        // Configure QueueMapper.selectOne(...).
        final HolderQueueDO holderQueueDO = new HolderQueueDO();
        holderQueueDO.setGuid("98dbaed2-ad56-4263-9a2a-85c05a345eea");
        holderQueueDO.setStoreGuid("storeGuid");
        holderQueueDO.setCode("code");
        holderQueueDO.setMin((byte) 0b0);
        holderQueueDO.setMax((byte) 0b0);
        holderQueueDO.setIsEnable(false);
        when(mockQueueMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(holderQueueDO);

        // Configure QueueItemMapStruct.toDetailDto(...).
        final HolderQueueItemDetailDTO holderQueueItemDetailDTO1 = new HolderQueueItemDetailDTO();
        holderQueueItemDetailDTO1.setDeviceType(0);
        holderQueueItemDetailDTO1.setDeviceId("deviceId");
        holderQueueItemDetailDTO1.setStoreName("storeName");
        holderQueueItemDetailDTO1.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        holderQueueItemDetailDTO1.setStoreGuid("storeGuid");
        holderQueueItemDetailDTO1.setPhone("phone");
        holderQueueItemDetailDTO1.setPeopleNumber((byte) 0b0);
        holderQueueItemDetailDTO1.setQueueGuid("queueGuid");
        holderQueueItemDetailDTO1.setSort(0);
        holderQueueItemDetailDTO1.setBefore(0);
        holderQueueItemDetailDTO1.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HolderQueueItemDO d = new HolderQueueItemDO();
        d.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        d.setStoreGuid("storeGuid");
        d.setQueueGuid("98dbaed2-ad56-4263-9a2a-85c05a345eea");
        d.setStatus((byte) 0b0);
        d.setPhone("phone");
        d.setPeopleNumber((byte) 0b0);
        d.setSort(0.0f);
        d.setQueueCode("code");
        d.setCode("code");
        d.setCallTimes(0);
        d.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setBusinessDay(LocalDate.of(2020, 1, 1));
        d.setIsEnable(false);
        d.setIsDeleted(false);
        d.setCreateStaffGuid("createStaffGuid");
        d.setModifiedStaffGuid("modifiedStaffGuid");
        d.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setDeviceType(0);
        d.setDeviceId("deviceId");
        d.setTableName("diningTableName");
        d.setTableGuid("diningTableGuid");
        d.setAreaName("areaName");
        when(mockQueueItemMapStruct.toDetailDto(d)).thenReturn(holderQueueItemDetailDTO1);

        // Configure QueueMapStruct.toDetailDto(...).
        final HolderQueueDetailDTO holderQueueDetailDTO = new HolderQueueDetailDTO();
        holderQueueDetailDTO.setBrandName("brandName");
        holderQueueDetailDTO.setLogoUrl("logoUrl");
        holderQueueDetailDTO.setStoreName("name");
        final HolderQueueItemDetailDTO holderQueueItemDetailDTO2 = new HolderQueueItemDetailDTO();
        holderQueueItemDetailDTO2.setQueueGuid("queueGuid");
        holderQueueDetailDTO.setItems(Arrays.asList(holderQueueItemDetailDTO2));
        final HolderQueueDO d1 = new HolderQueueDO();
        d1.setGuid("98dbaed2-ad56-4263-9a2a-85c05a345eea");
        d1.setStoreGuid("storeGuid");
        d1.setCode("code");
        d1.setMin((byte) 0b0);
        d1.setMax((byte) 0b0);
        d1.setIsEnable(false);
        when(mockQueueMapStruct.toDetailDto(d1)).thenReturn(holderQueueDetailDTO);

        // Run the test
        final HolderQueueQueueRecordDTO result = queueItemServiceImplUnderTest.obtain(dto);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testConfirm() {
        // Setup
        final ItemGuidDTO dto = new ItemGuidDTO();
        dto.setDeviceType(0);
        dto.setDeviceId("deviceId");
        dto.setStoreGuid("storeGuid");
        dto.setStoreName("storeName");
        dto.setItemGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        dto.setBrandGuid("brandGuid");
        dto.setEnterpriseGuid("enterpriseGuid");

        final HolderQueueItemDetailDTO expectedResult = new HolderQueueItemDetailDTO();
        expectedResult.setDeviceType(0);
        expectedResult.setDeviceId("deviceId");
        expectedResult.setStoreName("storeName");
        expectedResult.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setPhone("phone");
        expectedResult.setPeopleNumber((byte) 0b0);
        expectedResult.setQueueGuid("queueGuid");
        expectedResult.setSort(0);
        expectedResult.setBefore(0);
        expectedResult.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Configure QueueConfigService.obtain(...).
        final StoreConfigDTO storeConfigDTO = new StoreConfigDTO();
        storeConfigDTO.setGuid("f4eeaaad-3623-4267-9c62-8fb13c0e6eea");
        storeConfigDTO.setStoreGuid("storeGuid");
        storeConfigDTO.setIsEnableEat(false);
        storeConfigDTO.setIsEnableRecovery(false);
        storeConfigDTO.setRecoveryNum("recoveryNum");
        when(mockQueueConfigService.obtain("storeGuid")).thenReturn(storeConfigDTO);

        when(mockTransactionTemplate.execute(any(TransactionCallback.class))).thenReturn("result");

        // Configure QueueItemMapStruct.toDetailDto(...).
        final HolderQueueItemDetailDTO holderQueueItemDetailDTO = new HolderQueueItemDetailDTO();
        holderQueueItemDetailDTO.setDeviceType(0);
        holderQueueItemDetailDTO.setDeviceId("deviceId");
        holderQueueItemDetailDTO.setStoreName("storeName");
        holderQueueItemDetailDTO.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        holderQueueItemDetailDTO.setStoreGuid("storeGuid");
        holderQueueItemDetailDTO.setPhone("phone");
        holderQueueItemDetailDTO.setPeopleNumber((byte) 0b0);
        holderQueueItemDetailDTO.setQueueGuid("queueGuid");
        holderQueueItemDetailDTO.setSort(0);
        holderQueueItemDetailDTO.setBefore(0);
        holderQueueItemDetailDTO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HolderQueueItemDO d = new HolderQueueItemDO();
        d.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        d.setStoreGuid("storeGuid");
        d.setQueueGuid("98dbaed2-ad56-4263-9a2a-85c05a345eea");
        d.setStatus((byte) 0b0);
        d.setPhone("phone");
        d.setPeopleNumber((byte) 0b0);
        d.setSort(0.0f);
        d.setQueueCode("code");
        d.setCode("code");
        d.setCallTimes(0);
        d.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setBusinessDay(LocalDate.of(2020, 1, 1));
        d.setIsEnable(false);
        d.setIsDeleted(false);
        d.setCreateStaffGuid("createStaffGuid");
        d.setModifiedStaffGuid("modifiedStaffGuid");
        d.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setDeviceType(0);
        d.setDeviceId("deviceId");
        d.setTableName("diningTableName");
        d.setTableGuid("diningTableGuid");
        d.setAreaName("areaName");
        when(mockQueueItemMapStruct.toDetailDto(d)).thenReturn(holderQueueItemDetailDTO);

        // Configure BusinessMsgClient.sendMsg(...).
        final BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setMessageGuid("messageGuid");
        businessMessageDTO.setSubject("subject");
        businessMessageDTO.setContent("content");
        businessMessageDTO.setMessageType(0);
        businessMessageDTO.setDetailMessageType(0);
        when(mockBusinessMsgClient.sendMsg(businessMessageDTO)).thenReturn("result");

        // Run the test
        final HolderQueueItemDetailDTO result = queueItemServiceImplUnderTest.confirm(dto);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);

        // Confirm WxClient.callUpNotify(...).
        final ItemGuidDTO itemGuidDTO1 = new ItemGuidDTO();
        itemGuidDTO1.setDeviceType(0);
        itemGuidDTO1.setDeviceId("deviceId");
        itemGuidDTO1.setStoreGuid("storeGuid");
        itemGuidDTO1.setStoreName("storeName");
        itemGuidDTO1.setItemGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        itemGuidDTO1.setBrandGuid("brandGuid");
        itemGuidDTO1.setEnterpriseGuid("enterpriseGuid");
        final List<ItemGuidDTO> itemGuidDTO = Arrays.asList(itemGuidDTO1);
        verify(mockWxClient).callUpNotify(itemGuidDTO);
    }

    @Test
    public void testConfirm_QueueConfigServiceReturnsNull() {
        // Setup
        final ItemGuidDTO dto = new ItemGuidDTO();
        dto.setDeviceType(0);
        dto.setDeviceId("deviceId");
        dto.setStoreGuid("storeGuid");
        dto.setStoreName("storeName");
        dto.setItemGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        dto.setBrandGuid("brandGuid");
        dto.setEnterpriseGuid("enterpriseGuid");

        final HolderQueueItemDetailDTO expectedResult = new HolderQueueItemDetailDTO();
        expectedResult.setDeviceType(0);
        expectedResult.setDeviceId("deviceId");
        expectedResult.setStoreName("storeName");
        expectedResult.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setPhone("phone");
        expectedResult.setPeopleNumber((byte) 0b0);
        expectedResult.setQueueGuid("queueGuid");
        expectedResult.setSort(0);
        expectedResult.setBefore(0);
        expectedResult.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        when(mockQueueConfigService.obtain("storeGuid")).thenReturn(null);
        when(mockTransactionTemplate.execute(any(TransactionCallback.class))).thenReturn("result");

        // Configure QueueItemMapStruct.toDetailDto(...).
        final HolderQueueItemDetailDTO holderQueueItemDetailDTO = new HolderQueueItemDetailDTO();
        holderQueueItemDetailDTO.setDeviceType(0);
        holderQueueItemDetailDTO.setDeviceId("deviceId");
        holderQueueItemDetailDTO.setStoreName("storeName");
        holderQueueItemDetailDTO.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        holderQueueItemDetailDTO.setStoreGuid("storeGuid");
        holderQueueItemDetailDTO.setPhone("phone");
        holderQueueItemDetailDTO.setPeopleNumber((byte) 0b0);
        holderQueueItemDetailDTO.setQueueGuid("queueGuid");
        holderQueueItemDetailDTO.setSort(0);
        holderQueueItemDetailDTO.setBefore(0);
        holderQueueItemDetailDTO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HolderQueueItemDO d = new HolderQueueItemDO();
        d.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        d.setStoreGuid("storeGuid");
        d.setQueueGuid("98dbaed2-ad56-4263-9a2a-85c05a345eea");
        d.setStatus((byte) 0b0);
        d.setPhone("phone");
        d.setPeopleNumber((byte) 0b0);
        d.setSort(0.0f);
        d.setQueueCode("code");
        d.setCode("code");
        d.setCallTimes(0);
        d.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setBusinessDay(LocalDate.of(2020, 1, 1));
        d.setIsEnable(false);
        d.setIsDeleted(false);
        d.setCreateStaffGuid("createStaffGuid");
        d.setModifiedStaffGuid("modifiedStaffGuid");
        d.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setDeviceType(0);
        d.setDeviceId("deviceId");
        d.setTableName("diningTableName");
        d.setTableGuid("diningTableGuid");
        d.setAreaName("areaName");
        when(mockQueueItemMapStruct.toDetailDto(d)).thenReturn(holderQueueItemDetailDTO);

        // Configure BusinessMsgClient.sendMsg(...).
        final BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setMessageGuid("messageGuid");
        businessMessageDTO.setSubject("subject");
        businessMessageDTO.setContent("content");
        businessMessageDTO.setMessageType(0);
        businessMessageDTO.setDetailMessageType(0);
        when(mockBusinessMsgClient.sendMsg(businessMessageDTO)).thenReturn("result");

        // Run the test
        final HolderQueueItemDetailDTO result = queueItemServiceImplUnderTest.confirm(dto);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);

        // Confirm WxClient.callUpNotify(...).
        final ItemGuidDTO itemGuidDTO1 = new ItemGuidDTO();
        itemGuidDTO1.setDeviceType(0);
        itemGuidDTO1.setDeviceId("deviceId");
        itemGuidDTO1.setStoreGuid("storeGuid");
        itemGuidDTO1.setStoreName("storeName");
        itemGuidDTO1.setItemGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        itemGuidDTO1.setBrandGuid("brandGuid");
        itemGuidDTO1.setEnterpriseGuid("enterpriseGuid");
        final List<ItemGuidDTO> itemGuidDTO = Arrays.asList(itemGuidDTO1);
        verify(mockWxClient).callUpNotify(itemGuidDTO);
    }

    @Test
    public void testConfirm_TransactionTemplateReturnsNull() {
        // Setup
        final ItemGuidDTO dto = new ItemGuidDTO();
        dto.setDeviceType(0);
        dto.setDeviceId("deviceId");
        dto.setStoreGuid("storeGuid");
        dto.setStoreName("storeName");
        dto.setItemGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        dto.setBrandGuid("brandGuid");
        dto.setEnterpriseGuid("enterpriseGuid");

        // Configure QueueConfigService.obtain(...).
        final StoreConfigDTO storeConfigDTO = new StoreConfigDTO();
        storeConfigDTO.setGuid("f4eeaaad-3623-4267-9c62-8fb13c0e6eea");
        storeConfigDTO.setStoreGuid("storeGuid");
        storeConfigDTO.setIsEnableEat(false);
        storeConfigDTO.setIsEnableRecovery(false);
        storeConfigDTO.setRecoveryNum("recoveryNum");
        when(mockQueueConfigService.obtain("storeGuid")).thenReturn(storeConfigDTO);

        when(mockTransactionTemplate.execute(any(TransactionCallback.class))).thenReturn(null);

        // Configure QueueItemMapStruct.toDetailDto(...).
        final HolderQueueItemDetailDTO holderQueueItemDetailDTO = new HolderQueueItemDetailDTO();
        holderQueueItemDetailDTO.setDeviceType(0);
        holderQueueItemDetailDTO.setDeviceId("deviceId");
        holderQueueItemDetailDTO.setStoreName("storeName");
        holderQueueItemDetailDTO.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        holderQueueItemDetailDTO.setStoreGuid("storeGuid");
        holderQueueItemDetailDTO.setPhone("phone");
        holderQueueItemDetailDTO.setPeopleNumber((byte) 0b0);
        holderQueueItemDetailDTO.setQueueGuid("queueGuid");
        holderQueueItemDetailDTO.setSort(0);
        holderQueueItemDetailDTO.setBefore(0);
        holderQueueItemDetailDTO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HolderQueueItemDO d = new HolderQueueItemDO();
        d.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        d.setStoreGuid("storeGuid");
        d.setQueueGuid("98dbaed2-ad56-4263-9a2a-85c05a345eea");
        d.setStatus((byte) 0b0);
        d.setPhone("phone");
        d.setPeopleNumber((byte) 0b0);
        d.setSort(0.0f);
        d.setQueueCode("code");
        d.setCode("code");
        d.setCallTimes(0);
        d.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setBusinessDay(LocalDate.of(2020, 1, 1));
        d.setIsEnable(false);
        d.setIsDeleted(false);
        d.setCreateStaffGuid("createStaffGuid");
        d.setModifiedStaffGuid("modifiedStaffGuid");
        d.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setDeviceType(0);
        d.setDeviceId("deviceId");
        d.setTableName("diningTableName");
        d.setTableGuid("diningTableGuid");
        d.setAreaName("areaName");
        when(mockQueueItemMapStruct.toDetailDto(d)).thenReturn(holderQueueItemDetailDTO);

        // Configure BusinessMsgClient.sendMsg(...).
        final BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setMessageGuid("messageGuid");
        businessMessageDTO.setSubject("subject");
        businessMessageDTO.setContent("content");
        businessMessageDTO.setMessageType(0);
        businessMessageDTO.setDetailMessageType(0);
        when(mockBusinessMsgClient.sendMsg(businessMessageDTO)).thenReturn("result");

        // Run the test
        final HolderQueueItemDetailDTO result = queueItemServiceImplUnderTest.confirm(dto);

        // Verify the results
        assertThat(result).isNull();

        // Confirm WxClient.callUpNotify(...).
        final ItemGuidDTO itemGuidDTO1 = new ItemGuidDTO();
        itemGuidDTO1.setDeviceType(0);
        itemGuidDTO1.setDeviceId("deviceId");
        itemGuidDTO1.setStoreGuid("storeGuid");
        itemGuidDTO1.setStoreName("storeName");
        itemGuidDTO1.setItemGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        itemGuidDTO1.setBrandGuid("brandGuid");
        itemGuidDTO1.setEnterpriseGuid("enterpriseGuid");
        final List<ItemGuidDTO> itemGuidDTO = Arrays.asList(itemGuidDTO1);
        verify(mockWxClient).callUpNotify(itemGuidDTO);
    }

    @Test
    public void testConfirm_TransactionTemplateThrowsTransactionException() {
        // Setup
        final ItemGuidDTO dto = new ItemGuidDTO();
        dto.setDeviceType(0);
        dto.setDeviceId("deviceId");
        dto.setStoreGuid("storeGuid");
        dto.setStoreName("storeName");
        dto.setItemGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        dto.setBrandGuid("brandGuid");
        dto.setEnterpriseGuid("enterpriseGuid");

        // Configure QueueConfigService.obtain(...).
        final StoreConfigDTO storeConfigDTO = new StoreConfigDTO();
        storeConfigDTO.setGuid("f4eeaaad-3623-4267-9c62-8fb13c0e6eea");
        storeConfigDTO.setStoreGuid("storeGuid");
        storeConfigDTO.setIsEnableEat(false);
        storeConfigDTO.setIsEnableRecovery(false);
        storeConfigDTO.setRecoveryNum("recoveryNum");
        when(mockQueueConfigService.obtain("storeGuid")).thenReturn(storeConfigDTO);

        when(mockTransactionTemplate.execute(any(TransactionCallback.class))).thenThrow(TransactionException.class);

        // Run the test
        assertThatThrownBy(() -> queueItemServiceImplUnderTest.confirm(dto)).isInstanceOf(TransactionException.class);
    }

    @Test
    public void testConfirmAndTable() {
        // Setup
        final TableConfirmDTO dto = new TableConfirmDTO();
        dto.setDeviceType(0);
        dto.setDeviceId("deviceId");
        dto.setStoreGuid("storeGuid");
        dto.setStoreName("storeName");
        dto.setItemGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        dto.setBrandGuid("brandGuid");
        dto.setEnterpriseGuid("enterpriseGuid");
        dto.setDiningTableGuid("diningTableGuid");
        dto.setDiningTableName("diningTableName");
        dto.setAreaGuid("areaGuid");
        dto.setAreaName("areaName");

        final TableQueueItemDetailDTO expectedResult = new TableQueueItemDetailDTO();
        expectedResult.setDeviceType(0);
        expectedResult.setDeviceId("deviceId");
        expectedResult.setStoreName("storeName");
        expectedResult.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setPhone("phone");
        expectedResult.setPeopleNumber((byte) 0b0);
        expectedResult.setQueueGuid("queueGuid");
        expectedResult.setSort(0);
        expectedResult.setBefore(0);
        expectedResult.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setOrderNo("orderNo");

        when(mockTransactionTemplate.execute(any(TransactionCallback.class))).thenReturn("result");

        // Configure QueueConfigService.obtain(...).
        final StoreConfigDTO storeConfigDTO = new StoreConfigDTO();
        storeConfigDTO.setGuid("f4eeaaad-3623-4267-9c62-8fb13c0e6eea");
        storeConfigDTO.setStoreGuid("storeGuid");
        storeConfigDTO.setIsEnableEat(false);
        storeConfigDTO.setIsEnableRecovery(false);
        storeConfigDTO.setRecoveryNum("recoveryNum");
        when(mockQueueConfigService.obtain("storeGuid")).thenReturn(storeConfigDTO);

        // Configure QueueItemMapStruct.toDetailDto(...).
        final HolderQueueItemDetailDTO holderQueueItemDetailDTO = new HolderQueueItemDetailDTO();
        holderQueueItemDetailDTO.setDeviceType(0);
        holderQueueItemDetailDTO.setDeviceId("deviceId");
        holderQueueItemDetailDTO.setStoreName("storeName");
        holderQueueItemDetailDTO.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        holderQueueItemDetailDTO.setStoreGuid("storeGuid");
        holderQueueItemDetailDTO.setPhone("phone");
        holderQueueItemDetailDTO.setPeopleNumber((byte) 0b0);
        holderQueueItemDetailDTO.setQueueGuid("queueGuid");
        holderQueueItemDetailDTO.setSort(0);
        holderQueueItemDetailDTO.setBefore(0);
        holderQueueItemDetailDTO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HolderQueueItemDO d = new HolderQueueItemDO();
        d.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        d.setStoreGuid("storeGuid");
        d.setQueueGuid("98dbaed2-ad56-4263-9a2a-85c05a345eea");
        d.setStatus((byte) 0b0);
        d.setPhone("phone");
        d.setPeopleNumber((byte) 0b0);
        d.setSort(0.0f);
        d.setQueueCode("code");
        d.setCode("code");
        d.setCallTimes(0);
        d.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setBusinessDay(LocalDate.of(2020, 1, 1));
        d.setIsEnable(false);
        d.setIsDeleted(false);
        d.setCreateStaffGuid("createStaffGuid");
        d.setModifiedStaffGuid("modifiedStaffGuid");
        d.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setDeviceType(0);
        d.setDeviceId("deviceId");
        d.setTableName("diningTableName");
        d.setTableGuid("diningTableGuid");
        d.setAreaName("areaName");
        when(mockQueueItemMapStruct.toDetailDto(d)).thenReturn(holderQueueItemDetailDTO);

        // Configure BusinessMsgClient.sendMsg(...).
        final BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setMessageGuid("messageGuid");
        businessMessageDTO.setSubject("subject");
        businessMessageDTO.setContent("content");
        businessMessageDTO.setMessageType(0);
        businessMessageDTO.setDetailMessageType(0);
        when(mockBusinessMsgClient.sendMsg(businessMessageDTO)).thenReturn("result");

        // Configure QueueItemMapStruct.toTradeDTO(...).
        final OpenTableDTO openTableDTO = new OpenTableDTO();
        openTableDTO.setDeviceType(0);
        openTableDTO.setDeviceId("deviceId");
        openTableDTO.setStoreGuid("storeGuid");
        openTableDTO.setStoreName("storeName");
        openTableDTO.setTableGuid("diningTableGuid");
        openTableDTO.setTableCode("diningTableName");
        openTableDTO.setAreaName("areaName");
        openTableDTO.setActualGuestsNo(0);
        openTableDTO.setAreaGuid("areaGuid");
        final TableConfirmDTO dto1 = new TableConfirmDTO();
        dto1.setDeviceType(0);
        dto1.setDeviceId("deviceId");
        dto1.setStoreGuid("storeGuid");
        dto1.setStoreName("storeName");
        dto1.setItemGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        dto1.setBrandGuid("brandGuid");
        dto1.setEnterpriseGuid("enterpriseGuid");
        dto1.setDiningTableGuid("diningTableGuid");
        dto1.setDiningTableName("diningTableName");
        dto1.setAreaGuid("areaGuid");
        dto1.setAreaName("areaName");
        when(mockQueueItemMapStruct.toTradeDTO(dto1)).thenReturn(openTableDTO);

        // Configure TableClientService.openTable(...).
        final OpenTableDTO openTableDTO1 = new OpenTableDTO();
        openTableDTO1.setDeviceType(0);
        openTableDTO1.setDeviceId("deviceId");
        openTableDTO1.setStoreGuid("storeGuid");
        openTableDTO1.setStoreName("storeName");
        openTableDTO1.setTableGuid("diningTableGuid");
        openTableDTO1.setTableCode("diningTableName");
        openTableDTO1.setAreaName("areaName");
        openTableDTO1.setActualGuestsNo(0);
        openTableDTO1.setAreaGuid("areaGuid");
        when(mockTableClientService.openTable(openTableDTO1)).thenReturn("orderNo");

        // Configure QueueItemMapStruct.toTableDTO(...).
        final TableQueueItemDetailDTO tableQueueItemDetailDTO = new TableQueueItemDetailDTO();
        tableQueueItemDetailDTO.setDeviceType(0);
        tableQueueItemDetailDTO.setDeviceId("deviceId");
        tableQueueItemDetailDTO.setStoreName("storeName");
        tableQueueItemDetailDTO.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        tableQueueItemDetailDTO.setStoreGuid("storeGuid");
        tableQueueItemDetailDTO.setPhone("phone");
        tableQueueItemDetailDTO.setPeopleNumber((byte) 0b0);
        tableQueueItemDetailDTO.setQueueGuid("queueGuid");
        tableQueueItemDetailDTO.setSort(0);
        tableQueueItemDetailDTO.setBefore(0);
        tableQueueItemDetailDTO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tableQueueItemDetailDTO.setOrderNo("orderNo");
        final HolderQueueItemDetailDTO dto2 = new HolderQueueItemDetailDTO();
        dto2.setDeviceType(0);
        dto2.setDeviceId("deviceId");
        dto2.setStoreName("storeName");
        dto2.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        dto2.setStoreGuid("storeGuid");
        dto2.setPhone("phone");
        dto2.setPeopleNumber((byte) 0b0);
        dto2.setQueueGuid("queueGuid");
        dto2.setSort(0);
        dto2.setBefore(0);
        dto2.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockQueueItemMapStruct.toTableDTO(dto2)).thenReturn(tableQueueItemDetailDTO);

        // Run the test
        final TableQueueItemDetailDTO result = queueItemServiceImplUnderTest.confirmAndTable(dto);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);

        // Confirm WxClient.callUpNotify(...).
        final ItemGuidDTO itemGuidDTO1 = new ItemGuidDTO();
        itemGuidDTO1.setDeviceType(0);
        itemGuidDTO1.setDeviceId("deviceId");
        itemGuidDTO1.setStoreGuid("storeGuid");
        itemGuidDTO1.setStoreName("storeName");
        itemGuidDTO1.setItemGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        itemGuidDTO1.setBrandGuid("brandGuid");
        itemGuidDTO1.setEnterpriseGuid("enterpriseGuid");
        final List<ItemGuidDTO> itemGuidDTO = Arrays.asList(itemGuidDTO1);
        verify(mockWxClient).callUpNotify(itemGuidDTO);
    }

    @Test
    public void testConfirmAndTable_TransactionTemplateReturnsNull() {
        // Setup
        final TableConfirmDTO dto = new TableConfirmDTO();
        dto.setDeviceType(0);
        dto.setDeviceId("deviceId");
        dto.setStoreGuid("storeGuid");
        dto.setStoreName("storeName");
        dto.setItemGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        dto.setBrandGuid("brandGuid");
        dto.setEnterpriseGuid("enterpriseGuid");
        dto.setDiningTableGuid("diningTableGuid");
        dto.setDiningTableName("diningTableName");
        dto.setAreaGuid("areaGuid");
        dto.setAreaName("areaName");

        when(mockTransactionTemplate.execute(any(TransactionCallback.class))).thenReturn(null);

        // Configure QueueConfigService.obtain(...).
        final StoreConfigDTO storeConfigDTO = new StoreConfigDTO();
        storeConfigDTO.setGuid("f4eeaaad-3623-4267-9c62-8fb13c0e6eea");
        storeConfigDTO.setStoreGuid("storeGuid");
        storeConfigDTO.setIsEnableEat(false);
        storeConfigDTO.setIsEnableRecovery(false);
        storeConfigDTO.setRecoveryNum("recoveryNum");
        when(mockQueueConfigService.obtain("storeGuid")).thenReturn(storeConfigDTO);

        // Configure QueueItemMapStruct.toDetailDto(...).
        final HolderQueueItemDetailDTO holderQueueItemDetailDTO = new HolderQueueItemDetailDTO();
        holderQueueItemDetailDTO.setDeviceType(0);
        holderQueueItemDetailDTO.setDeviceId("deviceId");
        holderQueueItemDetailDTO.setStoreName("storeName");
        holderQueueItemDetailDTO.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        holderQueueItemDetailDTO.setStoreGuid("storeGuid");
        holderQueueItemDetailDTO.setPhone("phone");
        holderQueueItemDetailDTO.setPeopleNumber((byte) 0b0);
        holderQueueItemDetailDTO.setQueueGuid("queueGuid");
        holderQueueItemDetailDTO.setSort(0);
        holderQueueItemDetailDTO.setBefore(0);
        holderQueueItemDetailDTO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HolderQueueItemDO d = new HolderQueueItemDO();
        d.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        d.setStoreGuid("storeGuid");
        d.setQueueGuid("98dbaed2-ad56-4263-9a2a-85c05a345eea");
        d.setStatus((byte) 0b0);
        d.setPhone("phone");
        d.setPeopleNumber((byte) 0b0);
        d.setSort(0.0f);
        d.setQueueCode("code");
        d.setCode("code");
        d.setCallTimes(0);
        d.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setBusinessDay(LocalDate.of(2020, 1, 1));
        d.setIsEnable(false);
        d.setIsDeleted(false);
        d.setCreateStaffGuid("createStaffGuid");
        d.setModifiedStaffGuid("modifiedStaffGuid");
        d.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setDeviceType(0);
        d.setDeviceId("deviceId");
        d.setTableName("diningTableName");
        d.setTableGuid("diningTableGuid");
        d.setAreaName("areaName");
        when(mockQueueItemMapStruct.toDetailDto(d)).thenReturn(holderQueueItemDetailDTO);

        // Configure BusinessMsgClient.sendMsg(...).
        final BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setMessageGuid("messageGuid");
        businessMessageDTO.setSubject("subject");
        businessMessageDTO.setContent("content");
        businessMessageDTO.setMessageType(0);
        businessMessageDTO.setDetailMessageType(0);
        when(mockBusinessMsgClient.sendMsg(businessMessageDTO)).thenReturn("result");

        // Configure QueueItemMapStruct.toTradeDTO(...).
        final OpenTableDTO openTableDTO = new OpenTableDTO();
        openTableDTO.setDeviceType(0);
        openTableDTO.setDeviceId("deviceId");
        openTableDTO.setStoreGuid("storeGuid");
        openTableDTO.setStoreName("storeName");
        openTableDTO.setTableGuid("diningTableGuid");
        openTableDTO.setTableCode("diningTableName");
        openTableDTO.setAreaName("areaName");
        openTableDTO.setActualGuestsNo(0);
        openTableDTO.setAreaGuid("areaGuid");
        final TableConfirmDTO dto1 = new TableConfirmDTO();
        dto1.setDeviceType(0);
        dto1.setDeviceId("deviceId");
        dto1.setStoreGuid("storeGuid");
        dto1.setStoreName("storeName");
        dto1.setItemGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        dto1.setBrandGuid("brandGuid");
        dto1.setEnterpriseGuid("enterpriseGuid");
        dto1.setDiningTableGuid("diningTableGuid");
        dto1.setDiningTableName("diningTableName");
        dto1.setAreaGuid("areaGuid");
        dto1.setAreaName("areaName");
        when(mockQueueItemMapStruct.toTradeDTO(dto1)).thenReturn(openTableDTO);

        // Configure TableClientService.openTable(...).
        final OpenTableDTO openTableDTO1 = new OpenTableDTO();
        openTableDTO1.setDeviceType(0);
        openTableDTO1.setDeviceId("deviceId");
        openTableDTO1.setStoreGuid("storeGuid");
        openTableDTO1.setStoreName("storeName");
        openTableDTO1.setTableGuid("diningTableGuid");
        openTableDTO1.setTableCode("diningTableName");
        openTableDTO1.setAreaName("areaName");
        openTableDTO1.setActualGuestsNo(0);
        openTableDTO1.setAreaGuid("areaGuid");
        when(mockTableClientService.openTable(openTableDTO1)).thenReturn("orderNo");

        // Configure QueueItemMapStruct.toTableDTO(...).
        final TableQueueItemDetailDTO tableQueueItemDetailDTO = new TableQueueItemDetailDTO();
        tableQueueItemDetailDTO.setDeviceType(0);
        tableQueueItemDetailDTO.setDeviceId("deviceId");
        tableQueueItemDetailDTO.setStoreName("storeName");
        tableQueueItemDetailDTO.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        tableQueueItemDetailDTO.setStoreGuid("storeGuid");
        tableQueueItemDetailDTO.setPhone("phone");
        tableQueueItemDetailDTO.setPeopleNumber((byte) 0b0);
        tableQueueItemDetailDTO.setQueueGuid("queueGuid");
        tableQueueItemDetailDTO.setSort(0);
        tableQueueItemDetailDTO.setBefore(0);
        tableQueueItemDetailDTO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tableQueueItemDetailDTO.setOrderNo("orderNo");
        final HolderQueueItemDetailDTO dto2 = new HolderQueueItemDetailDTO();
        dto2.setDeviceType(0);
        dto2.setDeviceId("deviceId");
        dto2.setStoreName("storeName");
        dto2.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        dto2.setStoreGuid("storeGuid");
        dto2.setPhone("phone");
        dto2.setPeopleNumber((byte) 0b0);
        dto2.setQueueGuid("queueGuid");
        dto2.setSort(0);
        dto2.setBefore(0);
        dto2.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockQueueItemMapStruct.toTableDTO(dto2)).thenReturn(tableQueueItemDetailDTO);

        // Run the test
        final TableQueueItemDetailDTO result = queueItemServiceImplUnderTest.confirmAndTable(dto);

        // Verify the results
        assertThat(result).isNull();

        // Confirm WxClient.callUpNotify(...).
        final ItemGuidDTO itemGuidDTO1 = new ItemGuidDTO();
        itemGuidDTO1.setDeviceType(0);
        itemGuidDTO1.setDeviceId("deviceId");
        itemGuidDTO1.setStoreGuid("storeGuid");
        itemGuidDTO1.setStoreName("storeName");
        itemGuidDTO1.setItemGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        itemGuidDTO1.setBrandGuid("brandGuid");
        itemGuidDTO1.setEnterpriseGuid("enterpriseGuid");
        final List<ItemGuidDTO> itemGuidDTO = Arrays.asList(itemGuidDTO1);
        verify(mockWxClient).callUpNotify(itemGuidDTO);
    }

    @Test
    public void testConfirmAndTable_TransactionTemplateThrowsTransactionException() {
        // Setup
        final TableConfirmDTO dto = new TableConfirmDTO();
        dto.setDeviceType(0);
        dto.setDeviceId("deviceId");
        dto.setStoreGuid("storeGuid");
        dto.setStoreName("storeName");
        dto.setItemGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        dto.setBrandGuid("brandGuid");
        dto.setEnterpriseGuid("enterpriseGuid");
        dto.setDiningTableGuid("diningTableGuid");
        dto.setDiningTableName("diningTableName");
        dto.setAreaGuid("areaGuid");
        dto.setAreaName("areaName");

        when(mockTransactionTemplate.execute(any(TransactionCallback.class))).thenThrow(TransactionException.class);

        // Run the test
        assertThatThrownBy(() -> queueItemServiceImplUnderTest.confirmAndTable(dto))
                .isInstanceOf(TransactionException.class);
    }

    @Test
    public void testConfirmAndTable_QueueConfigServiceReturnsNull() {
        // Setup
        final TableConfirmDTO dto = new TableConfirmDTO();
        dto.setDeviceType(0);
        dto.setDeviceId("deviceId");
        dto.setStoreGuid("storeGuid");
        dto.setStoreName("storeName");
        dto.setItemGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        dto.setBrandGuid("brandGuid");
        dto.setEnterpriseGuid("enterpriseGuid");
        dto.setDiningTableGuid("diningTableGuid");
        dto.setDiningTableName("diningTableName");
        dto.setAreaGuid("areaGuid");
        dto.setAreaName("areaName");

        final TableQueueItemDetailDTO expectedResult = new TableQueueItemDetailDTO();
        expectedResult.setDeviceType(0);
        expectedResult.setDeviceId("deviceId");
        expectedResult.setStoreName("storeName");
        expectedResult.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setPhone("phone");
        expectedResult.setPeopleNumber((byte) 0b0);
        expectedResult.setQueueGuid("queueGuid");
        expectedResult.setSort(0);
        expectedResult.setBefore(0);
        expectedResult.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setOrderNo("orderNo");

        when(mockTransactionTemplate.execute(any(TransactionCallback.class))).thenReturn("result");
        when(mockQueueConfigService.obtain("storeGuid")).thenReturn(null);

        // Configure QueueItemMapStruct.toDetailDto(...).
        final HolderQueueItemDetailDTO holderQueueItemDetailDTO = new HolderQueueItemDetailDTO();
        holderQueueItemDetailDTO.setDeviceType(0);
        holderQueueItemDetailDTO.setDeviceId("deviceId");
        holderQueueItemDetailDTO.setStoreName("storeName");
        holderQueueItemDetailDTO.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        holderQueueItemDetailDTO.setStoreGuid("storeGuid");
        holderQueueItemDetailDTO.setPhone("phone");
        holderQueueItemDetailDTO.setPeopleNumber((byte) 0b0);
        holderQueueItemDetailDTO.setQueueGuid("queueGuid");
        holderQueueItemDetailDTO.setSort(0);
        holderQueueItemDetailDTO.setBefore(0);
        holderQueueItemDetailDTO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HolderQueueItemDO d = new HolderQueueItemDO();
        d.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        d.setStoreGuid("storeGuid");
        d.setQueueGuid("98dbaed2-ad56-4263-9a2a-85c05a345eea");
        d.setStatus((byte) 0b0);
        d.setPhone("phone");
        d.setPeopleNumber((byte) 0b0);
        d.setSort(0.0f);
        d.setQueueCode("code");
        d.setCode("code");
        d.setCallTimes(0);
        d.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setBusinessDay(LocalDate.of(2020, 1, 1));
        d.setIsEnable(false);
        d.setIsDeleted(false);
        d.setCreateStaffGuid("createStaffGuid");
        d.setModifiedStaffGuid("modifiedStaffGuid");
        d.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setDeviceType(0);
        d.setDeviceId("deviceId");
        d.setTableName("diningTableName");
        d.setTableGuid("diningTableGuid");
        d.setAreaName("areaName");
        when(mockQueueItemMapStruct.toDetailDto(d)).thenReturn(holderQueueItemDetailDTO);

        // Configure BusinessMsgClient.sendMsg(...).
        final BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setMessageGuid("messageGuid");
        businessMessageDTO.setSubject("subject");
        businessMessageDTO.setContent("content");
        businessMessageDTO.setMessageType(0);
        businessMessageDTO.setDetailMessageType(0);
        when(mockBusinessMsgClient.sendMsg(businessMessageDTO)).thenReturn("result");

        // Configure QueueItemMapStruct.toTradeDTO(...).
        final OpenTableDTO openTableDTO = new OpenTableDTO();
        openTableDTO.setDeviceType(0);
        openTableDTO.setDeviceId("deviceId");
        openTableDTO.setStoreGuid("storeGuid");
        openTableDTO.setStoreName("storeName");
        openTableDTO.setTableGuid("diningTableGuid");
        openTableDTO.setTableCode("diningTableName");
        openTableDTO.setAreaName("areaName");
        openTableDTO.setActualGuestsNo(0);
        openTableDTO.setAreaGuid("areaGuid");
        final TableConfirmDTO dto1 = new TableConfirmDTO();
        dto1.setDeviceType(0);
        dto1.setDeviceId("deviceId");
        dto1.setStoreGuid("storeGuid");
        dto1.setStoreName("storeName");
        dto1.setItemGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        dto1.setBrandGuid("brandGuid");
        dto1.setEnterpriseGuid("enterpriseGuid");
        dto1.setDiningTableGuid("diningTableGuid");
        dto1.setDiningTableName("diningTableName");
        dto1.setAreaGuid("areaGuid");
        dto1.setAreaName("areaName");
        when(mockQueueItemMapStruct.toTradeDTO(dto1)).thenReturn(openTableDTO);

        // Configure TableClientService.openTable(...).
        final OpenTableDTO openTableDTO1 = new OpenTableDTO();
        openTableDTO1.setDeviceType(0);
        openTableDTO1.setDeviceId("deviceId");
        openTableDTO1.setStoreGuid("storeGuid");
        openTableDTO1.setStoreName("storeName");
        openTableDTO1.setTableGuid("diningTableGuid");
        openTableDTO1.setTableCode("diningTableName");
        openTableDTO1.setAreaName("areaName");
        openTableDTO1.setActualGuestsNo(0);
        openTableDTO1.setAreaGuid("areaGuid");
        when(mockTableClientService.openTable(openTableDTO1)).thenReturn("orderNo");

        // Configure QueueItemMapStruct.toTableDTO(...).
        final TableQueueItemDetailDTO tableQueueItemDetailDTO = new TableQueueItemDetailDTO();
        tableQueueItemDetailDTO.setDeviceType(0);
        tableQueueItemDetailDTO.setDeviceId("deviceId");
        tableQueueItemDetailDTO.setStoreName("storeName");
        tableQueueItemDetailDTO.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        tableQueueItemDetailDTO.setStoreGuid("storeGuid");
        tableQueueItemDetailDTO.setPhone("phone");
        tableQueueItemDetailDTO.setPeopleNumber((byte) 0b0);
        tableQueueItemDetailDTO.setQueueGuid("queueGuid");
        tableQueueItemDetailDTO.setSort(0);
        tableQueueItemDetailDTO.setBefore(0);
        tableQueueItemDetailDTO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tableQueueItemDetailDTO.setOrderNo("orderNo");
        final HolderQueueItemDetailDTO dto2 = new HolderQueueItemDetailDTO();
        dto2.setDeviceType(0);
        dto2.setDeviceId("deviceId");
        dto2.setStoreName("storeName");
        dto2.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        dto2.setStoreGuid("storeGuid");
        dto2.setPhone("phone");
        dto2.setPeopleNumber((byte) 0b0);
        dto2.setQueueGuid("queueGuid");
        dto2.setSort(0);
        dto2.setBefore(0);
        dto2.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockQueueItemMapStruct.toTableDTO(dto2)).thenReturn(tableQueueItemDetailDTO);

        // Run the test
        final TableQueueItemDetailDTO result = queueItemServiceImplUnderTest.confirmAndTable(dto);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);

        // Confirm WxClient.callUpNotify(...).
        final ItemGuidDTO itemGuidDTO1 = new ItemGuidDTO();
        itemGuidDTO1.setDeviceType(0);
        itemGuidDTO1.setDeviceId("deviceId");
        itemGuidDTO1.setStoreGuid("storeGuid");
        itemGuidDTO1.setStoreName("storeName");
        itemGuidDTO1.setItemGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        itemGuidDTO1.setBrandGuid("brandGuid");
        itemGuidDTO1.setEnterpriseGuid("enterpriseGuid");
        final List<ItemGuidDTO> itemGuidDTO = Arrays.asList(itemGuidDTO1);
        verify(mockWxClient).callUpNotify(itemGuidDTO);
    }

    @Test
    public void testListQueueItemDetails() {
        // Setup
        final HolderQueueItemDetailDTO holderQueueItemDetailDTO = new HolderQueueItemDetailDTO();
        holderQueueItemDetailDTO.setDeviceType(0);
        holderQueueItemDetailDTO.setDeviceId("deviceId");
        holderQueueItemDetailDTO.setStoreName("storeName");
        holderQueueItemDetailDTO.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        holderQueueItemDetailDTO.setStoreGuid("storeGuid");
        holderQueueItemDetailDTO.setPhone("phone");
        holderQueueItemDetailDTO.setPeopleNumber((byte) 0b0);
        holderQueueItemDetailDTO.setQueueGuid("queueGuid");
        holderQueueItemDetailDTO.setSort(0);
        holderQueueItemDetailDTO.setBefore(0);
        holderQueueItemDetailDTO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HolderQueueItemDetailDTO> expectedResult = Arrays.asList(holderQueueItemDetailDTO);

        // Configure QueueItemMapStruct.toDetailDto(...).
        final HolderQueueItemDetailDTO holderQueueItemDetailDTO1 = new HolderQueueItemDetailDTO();
        holderQueueItemDetailDTO1.setDeviceType(0);
        holderQueueItemDetailDTO1.setDeviceId("deviceId");
        holderQueueItemDetailDTO1.setStoreName("storeName");
        holderQueueItemDetailDTO1.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        holderQueueItemDetailDTO1.setStoreGuid("storeGuid");
        holderQueueItemDetailDTO1.setPhone("phone");
        holderQueueItemDetailDTO1.setPeopleNumber((byte) 0b0);
        holderQueueItemDetailDTO1.setQueueGuid("queueGuid");
        holderQueueItemDetailDTO1.setSort(0);
        holderQueueItemDetailDTO1.setBefore(0);
        holderQueueItemDetailDTO1.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HolderQueueItemDO d = new HolderQueueItemDO();
        d.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        d.setStoreGuid("storeGuid");
        d.setQueueGuid("98dbaed2-ad56-4263-9a2a-85c05a345eea");
        d.setStatus((byte) 0b0);
        d.setPhone("phone");
        d.setPeopleNumber((byte) 0b0);
        d.setSort(0.0f);
        d.setQueueCode("code");
        d.setCode("code");
        d.setCallTimes(0);
        d.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setBusinessDay(LocalDate.of(2020, 1, 1));
        d.setIsEnable(false);
        d.setIsDeleted(false);
        d.setCreateStaffGuid("createStaffGuid");
        d.setModifiedStaffGuid("modifiedStaffGuid");
        d.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setDeviceType(0);
        d.setDeviceId("deviceId");
        d.setTableName("diningTableName");
        d.setTableGuid("diningTableGuid");
        d.setAreaName("areaName");
        when(mockQueueItemMapStruct.toDetailDto(d)).thenReturn(holderQueueItemDetailDTO1);

        // Run the test
        final List<HolderQueueItemDetailDTO> result = queueItemServiceImplUnderTest.listQueueItemDetails();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testPage() {
        // Setup
        final Page page = new Page<>(0L, 0L, Arrays.asList());

        // Configure QueueItemMapStruct.toDetailDto(...).
        final HolderQueueItemDetailDTO holderQueueItemDetailDTO = new HolderQueueItemDetailDTO();
        holderQueueItemDetailDTO.setDeviceType(0);
        holderQueueItemDetailDTO.setDeviceId("deviceId");
        holderQueueItemDetailDTO.setStoreName("storeName");
        holderQueueItemDetailDTO.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        holderQueueItemDetailDTO.setStoreGuid("storeGuid");
        holderQueueItemDetailDTO.setPhone("phone");
        holderQueueItemDetailDTO.setPeopleNumber((byte) 0b0);
        holderQueueItemDetailDTO.setQueueGuid("queueGuid");
        holderQueueItemDetailDTO.setSort(0);
        holderQueueItemDetailDTO.setBefore(0);
        holderQueueItemDetailDTO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HolderQueueItemDO d = new HolderQueueItemDO();
        d.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        d.setStoreGuid("storeGuid");
        d.setQueueGuid("98dbaed2-ad56-4263-9a2a-85c05a345eea");
        d.setStatus((byte) 0b0);
        d.setPhone("phone");
        d.setPeopleNumber((byte) 0b0);
        d.setSort(0.0f);
        d.setQueueCode("code");
        d.setCode("code");
        d.setCallTimes(0);
        d.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setBusinessDay(LocalDate.of(2020, 1, 1));
        d.setIsEnable(false);
        d.setIsDeleted(false);
        d.setCreateStaffGuid("createStaffGuid");
        d.setModifiedStaffGuid("modifiedStaffGuid");
        d.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setDeviceType(0);
        d.setDeviceId("deviceId");
        d.setTableName("diningTableName");
        d.setTableGuid("diningTableGuid");
        d.setAreaName("areaName");
        when(mockQueueItemMapStruct.toDetailDto(d)).thenReturn(holderQueueItemDetailDTO);

        // Run the test
        final Page<HolderQueueItemDetailDTO> result = queueItemServiceImplUnderTest.page(page);

        // Verify the results
    }

    @Test
    public void testCancel() {
        // Setup
        // Run the test
        final Boolean result = queueItemServiceImplUnderTest.cancel("9914228c-8e16-433e-80c4-f78f40257139",
                "enterpriseGuid");

        // Verify the results
        assertThat(result).isTrue();
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
    }

    @Test
    public void testQueryByUser() {
        // Setup
        final QueueWechatDTO queueWechatDTO = new QueueWechatDTO("brandGuid", "userGuid", (byte) 0b0, "enterpriseGuid");
        final WxQueueListDTO wxQueueListDTO = new WxQueueListDTO();
        wxQueueListDTO.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        wxQueueListDTO.setUserGuid("deviceId");
        wxQueueListDTO.setStoreGuid("storeGuid");
        wxQueueListDTO.setStoreName("storeName");
        wxQueueListDTO.setBrandName("brandName");
        wxQueueListDTO.setBrandGuid("brandGuid");
        wxQueueListDTO.setDate(LocalDate.of(2020, 1, 1));
        wxQueueListDTO.setStatus((byte) 0b0);
        wxQueueListDTO.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<WxQueueListDTO> expectedResult = Arrays.asList(wxQueueListDTO);

        // Configure OrganizationClientService.queryBrandByGuid(...).
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("2fef8c8e-c6ac-48b2-bd04-e3e2e675864b");
        brandDTO.setUuid("e1b7e798-7254-49cc-8abb-2331c77396b9");
        brandDTO.setName("brandName");
        brandDTO.setDescription("description");
        brandDTO.setLogoUrl("logoUrl");
        when(mockOrganizationClientService.queryBrandByGuid("brandGuid")).thenReturn(brandDTO);

        // Configure OrganizationClientService.queryStoreByBrand(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("13bab148-d971-47a4-b422-f966bc3fc49a");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO);
        when(mockOrganizationClientService.queryStoreByBrand(Arrays.asList("value"))).thenReturn(storeDTOS);

        // Run the test
        final List<WxQueueListDTO> result = queueItemServiceImplUnderTest.queryByUser(queueWechatDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
    }

    @Test
    public void testQueryByUser_OrganizationClientServiceQueryBrandByGuidReturnsNull() {
        // Setup
        final QueueWechatDTO queueWechatDTO = new QueueWechatDTO("brandGuid", "userGuid", (byte) 0b0, "enterpriseGuid");
        when(mockOrganizationClientService.queryBrandByGuid("brandGuid")).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> queueItemServiceImplUnderTest.queryByUser(queueWechatDTO))
                .isInstanceOf(BusinessException.class);
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
    }

    @Test
    public void testQueryByUser_OrganizationClientServiceQueryStoreByBrandReturnsNoItems() {
        // Setup
        final QueueWechatDTO queueWechatDTO = new QueueWechatDTO("brandGuid", "userGuid", (byte) 0b0, "enterpriseGuid");

        // Configure OrganizationClientService.queryBrandByGuid(...).
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("2fef8c8e-c6ac-48b2-bd04-e3e2e675864b");
        brandDTO.setUuid("e1b7e798-7254-49cc-8abb-2331c77396b9");
        brandDTO.setName("brandName");
        brandDTO.setDescription("description");
        brandDTO.setLogoUrl("logoUrl");
        when(mockOrganizationClientService.queryBrandByGuid("brandGuid")).thenReturn(brandDTO);

        when(mockOrganizationClientService.queryStoreByBrand(Arrays.asList("value")))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<WxQueueListDTO> result = queueItemServiceImplUnderTest.queryByUser(queueWechatDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
    }

    @Test
    public void testGetQueueDetail() {
        // Setup
        final ItemGuidDTO itemGuidDTO = new ItemGuidDTO();
        itemGuidDTO.setDeviceType(0);
        itemGuidDTO.setDeviceId("deviceId");
        itemGuidDTO.setStoreGuid("storeGuid");
        itemGuidDTO.setStoreName("storeName");
        itemGuidDTO.setItemGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        itemGuidDTO.setBrandGuid("brandGuid");
        itemGuidDTO.setEnterpriseGuid("enterpriseGuid");

        final HolderQueueDTO holderQueueDTO = new HolderQueueDTO();
        holderQueueDTO.setGuid("8b195f90-2912-45e7-9b98-171c4b87236e");
        holderQueueDTO.setStoreGuid("storeGuid");
        holderQueueDTO.setBrandName("brandName");
        holderQueueDTO.setLogoUrl("logoUrl");
        holderQueueDTO.setStoreName("name");
        final HolderQueueItemDetailDTO holderQueueItemDetailDTO = new HolderQueueItemDetailDTO();
        holderQueueItemDetailDTO.setDeviceType(0);
        holderQueueItemDetailDTO.setDeviceId("deviceId");
        holderQueueItemDetailDTO.setStoreName("storeName");
        holderQueueItemDetailDTO.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        holderQueueItemDetailDTO.setStoreGuid("storeGuid");
        holderQueueItemDetailDTO.setPhone("phone");
        holderQueueItemDetailDTO.setPeopleNumber((byte) 0b0);
        holderQueueItemDetailDTO.setQueueGuid("queueGuid");
        holderQueueItemDetailDTO.setSort(0);
        holderQueueItemDetailDTO.setBefore(0);
        holderQueueItemDetailDTO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HolderQueueQueueRecordDTO expectedResult = new HolderQueueQueueRecordDTO(holderQueueDTO,
                holderQueueItemDetailDTO);

        // Configure QueueMapper.selectOne(...).
        final HolderQueueDO holderQueueDO = new HolderQueueDO();
        holderQueueDO.setGuid("98dbaed2-ad56-4263-9a2a-85c05a345eea");
        holderQueueDO.setStoreGuid("storeGuid");
        holderQueueDO.setCode("code");
        holderQueueDO.setMin((byte) 0b0);
        holderQueueDO.setMax((byte) 0b0);
        holderQueueDO.setIsEnable(false);
        when(mockQueueMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(holderQueueDO);

        // Configure QueueItemMapStruct.toDetailDto(...).
        final HolderQueueItemDetailDTO holderQueueItemDetailDTO1 = new HolderQueueItemDetailDTO();
        holderQueueItemDetailDTO1.setDeviceType(0);
        holderQueueItemDetailDTO1.setDeviceId("deviceId");
        holderQueueItemDetailDTO1.setStoreName("storeName");
        holderQueueItemDetailDTO1.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        holderQueueItemDetailDTO1.setStoreGuid("storeGuid");
        holderQueueItemDetailDTO1.setPhone("phone");
        holderQueueItemDetailDTO1.setPeopleNumber((byte) 0b0);
        holderQueueItemDetailDTO1.setQueueGuid("queueGuid");
        holderQueueItemDetailDTO1.setSort(0);
        holderQueueItemDetailDTO1.setBefore(0);
        holderQueueItemDetailDTO1.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HolderQueueItemDO d = new HolderQueueItemDO();
        d.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        d.setStoreGuid("storeGuid");
        d.setQueueGuid("98dbaed2-ad56-4263-9a2a-85c05a345eea");
        d.setStatus((byte) 0b0);
        d.setPhone("phone");
        d.setPeopleNumber((byte) 0b0);
        d.setSort(0.0f);
        d.setQueueCode("code");
        d.setCode("code");
        d.setCallTimes(0);
        d.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setBusinessDay(LocalDate.of(2020, 1, 1));
        d.setIsEnable(false);
        d.setIsDeleted(false);
        d.setCreateStaffGuid("createStaffGuid");
        d.setModifiedStaffGuid("modifiedStaffGuid");
        d.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setDeviceType(0);
        d.setDeviceId("deviceId");
        d.setTableName("diningTableName");
        d.setTableGuid("diningTableGuid");
        d.setAreaName("areaName");
        when(mockQueueItemMapStruct.toDetailDto(d)).thenReturn(holderQueueItemDetailDTO1);

        // Configure QueueMapStruct.toDetailDto(...).
        final HolderQueueDetailDTO holderQueueDetailDTO = new HolderQueueDetailDTO();
        holderQueueDetailDTO.setBrandName("brandName");
        holderQueueDetailDTO.setLogoUrl("logoUrl");
        holderQueueDetailDTO.setStoreName("name");
        final HolderQueueItemDetailDTO holderQueueItemDetailDTO2 = new HolderQueueItemDetailDTO();
        holderQueueItemDetailDTO2.setQueueGuid("queueGuid");
        holderQueueDetailDTO.setItems(Arrays.asList(holderQueueItemDetailDTO2));
        final HolderQueueDO d1 = new HolderQueueDO();
        d1.setGuid("98dbaed2-ad56-4263-9a2a-85c05a345eea");
        d1.setStoreGuid("storeGuid");
        d1.setCode("code");
        d1.setMin((byte) 0b0);
        d1.setMax((byte) 0b0);
        d1.setIsEnable(false);
        when(mockQueueMapStruct.toDetailDto(d1)).thenReturn(holderQueueDetailDTO);

        // Configure OrganizationClientService.queryBrandByGuid(...).
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("2fef8c8e-c6ac-48b2-bd04-e3e2e675864b");
        brandDTO.setUuid("e1b7e798-7254-49cc-8abb-2331c77396b9");
        brandDTO.setName("brandName");
        brandDTO.setDescription("description");
        brandDTO.setLogoUrl("logoUrl");
        when(mockOrganizationClientService.queryBrandByGuid("brandGuid")).thenReturn(brandDTO);

        // Configure OrganizationClientService.queryStoreByGuid(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("13bab148-d971-47a4-b422-f966bc3fc49a");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        when(mockOrganizationClientService.queryStoreByGuid("storeGuid")).thenReturn(storeDTO);

        // Run the test
        final HolderQueueQueueRecordDTO result = queueItemServiceImplUnderTest.getQueueDetail(itemGuidDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
    }

    @Test
    public void testGetQueueDetail_OrganizationClientServiceQueryBrandByGuidReturnsNull() {
        // Setup
        final ItemGuidDTO itemGuidDTO = new ItemGuidDTO();
        itemGuidDTO.setDeviceType(0);
        itemGuidDTO.setDeviceId("deviceId");
        itemGuidDTO.setStoreGuid("storeGuid");
        itemGuidDTO.setStoreName("storeName");
        itemGuidDTO.setItemGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        itemGuidDTO.setBrandGuid("brandGuid");
        itemGuidDTO.setEnterpriseGuid("enterpriseGuid");

        // Configure QueueMapper.selectOne(...).
        final HolderQueueDO holderQueueDO = new HolderQueueDO();
        holderQueueDO.setGuid("98dbaed2-ad56-4263-9a2a-85c05a345eea");
        holderQueueDO.setStoreGuid("storeGuid");
        holderQueueDO.setCode("code");
        holderQueueDO.setMin((byte) 0b0);
        holderQueueDO.setMax((byte) 0b0);
        holderQueueDO.setIsEnable(false);
        when(mockQueueMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(holderQueueDO);

        // Configure QueueItemMapStruct.toDetailDto(...).
        final HolderQueueItemDetailDTO holderQueueItemDetailDTO = new HolderQueueItemDetailDTO();
        holderQueueItemDetailDTO.setDeviceType(0);
        holderQueueItemDetailDTO.setDeviceId("deviceId");
        holderQueueItemDetailDTO.setStoreName("storeName");
        holderQueueItemDetailDTO.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        holderQueueItemDetailDTO.setStoreGuid("storeGuid");
        holderQueueItemDetailDTO.setPhone("phone");
        holderQueueItemDetailDTO.setPeopleNumber((byte) 0b0);
        holderQueueItemDetailDTO.setQueueGuid("queueGuid");
        holderQueueItemDetailDTO.setSort(0);
        holderQueueItemDetailDTO.setBefore(0);
        holderQueueItemDetailDTO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final HolderQueueItemDO d = new HolderQueueItemDO();
        d.setGuid("e649fa35-abdc-412c-b6fa-52be419bcab1");
        d.setStoreGuid("storeGuid");
        d.setQueueGuid("98dbaed2-ad56-4263-9a2a-85c05a345eea");
        d.setStatus((byte) 0b0);
        d.setPhone("phone");
        d.setPeopleNumber((byte) 0b0);
        d.setSort(0.0f);
        d.setQueueCode("code");
        d.setCode("code");
        d.setCallTimes(0);
        d.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setBusinessDay(LocalDate.of(2020, 1, 1));
        d.setIsEnable(false);
        d.setIsDeleted(false);
        d.setCreateStaffGuid("createStaffGuid");
        d.setModifiedStaffGuid("modifiedStaffGuid");
        d.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        d.setDeviceType(0);
        d.setDeviceId("deviceId");
        d.setTableName("diningTableName");
        d.setTableGuid("diningTableGuid");
        d.setAreaName("areaName");
        when(mockQueueItemMapStruct.toDetailDto(d)).thenReturn(holderQueueItemDetailDTO);

        // Configure QueueMapStruct.toDetailDto(...).
        final HolderQueueDetailDTO holderQueueDetailDTO = new HolderQueueDetailDTO();
        holderQueueDetailDTO.setBrandName("brandName");
        holderQueueDetailDTO.setLogoUrl("logoUrl");
        holderQueueDetailDTO.setStoreName("name");
        final HolderQueueItemDetailDTO holderQueueItemDetailDTO1 = new HolderQueueItemDetailDTO();
        holderQueueItemDetailDTO1.setQueueGuid("queueGuid");
        holderQueueDetailDTO.setItems(Arrays.asList(holderQueueItemDetailDTO1));
        final HolderQueueDO d1 = new HolderQueueDO();
        d1.setGuid("98dbaed2-ad56-4263-9a2a-85c05a345eea");
        d1.setStoreGuid("storeGuid");
        d1.setCode("code");
        d1.setMin((byte) 0b0);
        d1.setMax((byte) 0b0);
        d1.setIsEnable(false);
        when(mockQueueMapStruct.toDetailDto(d1)).thenReturn(holderQueueDetailDTO);

        when(mockOrganizationClientService.queryBrandByGuid("brandGuid")).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> queueItemServiceImplUnderTest.getQueueDetail(itemGuidDTO))
                .isInstanceOf(BusinessException.class);
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
    }
}
