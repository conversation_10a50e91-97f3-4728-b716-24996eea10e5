package com.holderzone.saas.store.business.queue;

import com.holderzone.saas.store.business.queue.domain.HolderQueueDO;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.saas.store.business.queue.mapper.QueueMapper;
import com.holderzone.saas.store.business.queue.service.QueueService;
import com.holderzone.saas.store.dto.queue.HolderQueueDTO;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @className QueueTypeMvcTest
 * @date 2018/07/28 下午3:04
 * @description //TODO
 * @program holder-saas-store-business-center
 */
public class QueueTypeMvcTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    private MockMvc mockMvc;

    private String storeGuid;

    private String queuedTypeGuid;
    @InjectMocks
    private QueueMapper queueMapper;
    @Autowired
    private QueueService queueService;

    private List<HolderQueueDO> doList = new ArrayList();
    ArgumentCaptor<HolderQueueDO> personCaptor = ArgumentCaptor.forClass(HolderQueueDO.class);

    @Before
    public void setup() {
        this.mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        when(queueMapper.insert(any())).thenReturn(1);
    }

    @Test
    public void testSave() {
        UserContextUtils.put("{\"enterpriseGuid\":\"6506431195651982337\",\"account\":100009,\"userGuid\":\"6514308814446002177\"}");
        HolderQueueDTO holderQueueDTO = new HolderQueueDTO();
        holderQueueDTO.setMin((byte) 1);
        holderQueueDTO.setMax((byte) 8);

        queueService.save(holderQueueDTO);
        verify(queueMapper).insert(personCaptor.capture());
        HolderQueueDO d = personCaptor.getValue();
        Assert.assertTrue("caonima", d.getIsEnable());
        Assert.assertTrue("caonima", d.getMax() != 8);
        Assert.assertTrue("caonima", d.getMin() == 1);
    }
}