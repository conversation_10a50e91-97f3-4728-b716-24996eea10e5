package com.holderzone.saas.store.business;

import com.alibaba.fastjson.JSON;
import com.holderzone.saas.store.dto.business.manage.*;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static com.holderzone.saas.store.dto.common.CommonConstant.USER_INFO;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReserveRecordMvcTest
 * @date 2018/08/02 上午10:03
 * @description //TODO
 * @program holder-saas-store-business
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class AdditionalFeeMvcTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    private MockMvc mockMvc;

    private String storeGuid;

    private String additionalFeeGuid;

    private static final String encode = "%7B%22enterpriseGuid%22%3A%226506431195651982337%22%2C%22enterpriseName%22" +
            "%3A%22%E4%BC%81%E4%B8%9A0227%22%2C%22enterpriseNo%22%3A%**********%22%2C%22storeGuid%22%3A" +
            "%226506453252643487745%22%2C%22storeName%22%3A%22%E9%97%A8%E5%BA%970227_3%22%2C%22storeNo%22%3A" +
            "%*********%22%2C%22userGuid%22%3A%226507063794701697025%22%2C%22account%22%3A%********%22%2C%22tel%22%3A" +
            "%*************%22%2C%22name%22%3A%22wg%22%7D";

    @Before
    public void setup() {
        this.mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        storeGuid = "fa10a446-1869-4da7-b322-3c012470b5c1";
        additionalFeeGuid = "fb3c7fef-c972-47d8-85ea-b07548cb18cb";
    }

    @Test
    public void testCreate() throws Exception {
        List<String> areaList = new ArrayList<>();
        areaList.add("6506433582473412609");
        areaList.add("6506433601305837569");
        areaList.add("6506455625080242177");

        SurchargeCreateDTO surchargeCreateDTO = new SurchargeCreateDTO();
        surchargeCreateDTO.setAmount(new BigDecimal(12));
        surchargeCreateDTO.setAreaGuidList(areaList);
        surchargeCreateDTO.setStoreGuid("6506453252643487745");
        surchargeCreateDTO.setName("服务费11");
        surchargeCreateDTO.setType(1);
        String json = JSON.toJSONString(surchargeCreateDTO);
        MvcResult mvcResult = mockMvc.perform(post("/additional_fee/create")
                .header(USER_INFO, encode)
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON)
                .content(json))
                .andExpect(status().isOk())
                .andDo(print())
                .andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }

    @Test
    public void testSingle() throws Exception {
        SurchargeQueryDTO surchargeQueryDTO = new SurchargeQueryDTO();
        surchargeQueryDTO.setSurchargeGuid("6530673041087660033");
        String json = JSON.toJSONString(surchargeQueryDTO);
        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders.post("/additional_fee/single")
                .header(USER_INFO, encode)
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON)
                .content(json))
                .andExpect(status().isOk())
                .andDo(print())
                .andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }

    @Test
    public void testDisable() throws Exception {
        SurchargeEnableDTO surchargeEnableDTO = new SurchargeEnableDTO();
        surchargeEnableDTO.setIsEnable(false);
        surchargeEnableDTO.setSurchargeGuid("6530673041087660033");

        String json = JSON.toJSONString(surchargeEnableDTO);
        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders.post("/additional_fee/enable")
                .header(USER_INFO, encode)
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON)
                .content(json))
                .andExpect(status().isOk())
                .andDo(print())
                .andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);


    }


    @Test
    public void testEnable() throws Exception {
        SurchargeEnableDTO surchargeEnableDTO = new SurchargeEnableDTO();
        surchargeEnableDTO.setIsEnable(true);
        surchargeEnableDTO.setSurchargeGuid("6530673041087660033");

        String json = JSON.toJSONString(surchargeEnableDTO);
        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders.post("/additional_fee/enable")
                .header(USER_INFO, encode)
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON)
                .content(json))
                .andExpect(status().isOk())
                .andDo(print())
                .andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);

    }

    @Test
    public void testUpdate() throws Exception {
        SurchargeUpdateDTO surchargeUpdateDTO = new SurchargeUpdateDTO();
        surchargeUpdateDTO.setSurchargeGuid("6530673041087660033");
        surchargeUpdateDTO.setAmount(new BigDecimal(15));
        surchargeUpdateDTO.setType(0);
        surchargeUpdateDTO.setName("大大费");
        String json = JSON.toJSONString(surchargeUpdateDTO);
        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders.post("/additional_fee/update")
                .header(USER_INFO, encode)
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON)
                .content(json))
                .andExpect(status().isOk())
                .andDo(print())
                .andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }


    @Test
    public void testDelete() throws Exception {
        SurchargeDeleteDTO surchargeDeleteDTO = new SurchargeDeleteDTO();
        surchargeDeleteDTO.setSurchargeGuid("6530673041087660033");
        String json = JSON.toJSONString(surchargeDeleteDTO);
        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders.post("/additional_fee/delete")
                .header(USER_INFO, encode)
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON)
                .content(json))
                .andExpect(status().isOk())
                .andDo(print())
                .andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }


    @Test
    public void testListByFeeType() throws Exception {
        SurchargeListDTO surchargeListDTO = new SurchargeListDTO();
        surchargeListDTO.setStoreGuid("6506453252643487745");
        surchargeListDTO.setCurrentPage(2);
        surchargeListDTO.setPageSize(10);
        //按桌
        //additionalFeeListDTO.setType(1);

        //按人
        //additionalFeeListDTO.setType(0);

        //查询全部

        String json = JSON.toJSONString(surchargeListDTO);
        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders.post("/additional_fee/list_by_fee_type")
                .header(USER_INFO, encode)
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON)
                .content(json))
                .andExpect(status().isOk())
                .andDo(print())
                .andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }

    @Test
    public void testListByAreaGuids() throws Exception {
        List<String> areaList = new ArrayList<>();
        areaList.add("6506455625080242177");
        areaList.add("6506433582473412609");
        String json = JSON.toJSONString(areaList);
        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders.post("/additional_fee/list_by_area_guid")
                .header(USER_INFO, encode)
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON)
                .content(json))
                .andExpect(status().isOk())
                .andDo(print())
                .andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }


    @Test
    public void testBatchSort() throws Exception {
        String requestBody = "[\n" +
                "  {\n" +
                "    \"guid\": \"" + additionalFeeGuid + "\",\n" +
                "    \"sort\": 999\n" +
                "  }\n" +
                "]";
        mockMvc.perform(MockMvcRequestBuilders.post("/additional_fee/batchSort")
                .accept(MediaType.APPLICATION_JSON_UTF8)
                .contentType(MediaType.APPLICATION_JSON_UTF8)
                .content(requestBody))
                .andExpect(status().isOk());
    }

    @Test
    public void testSingleUnDeleted() throws Exception {
        String requestBody = "{\n" +
                "  \"guid\": \"" + additionalFeeGuid + "\"\n" +
                "}";
        mockMvc.perform(MockMvcRequestBuilders.post("/additional_fee/single")
                .accept(MediaType.APPLICATION_JSON_UTF8)
                .contentType(MediaType.APPLICATION_JSON_UTF8)
                .content(requestBody))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON_UTF8))
                .andExpect(jsonPath("$.sort").value(999))
                .andExpect(jsonPath("$.deleted").value(0));
    }

    @Test
    public void testSingleDeleted() throws Exception {
        String requestBody = "{\n" +
                "  \"guid\": \"" + additionalFeeGuid + "\"\n" +
                "}";
        mockMvc.perform(MockMvcRequestBuilders.post("/additional_fee/single")
                .accept(MediaType.APPLICATION_JSON_UTF8)
                .contentType(MediaType.APPLICATION_JSON_UTF8)
                .content(requestBody))
                .andExpect(status().is4xxClientError());
    }
}
