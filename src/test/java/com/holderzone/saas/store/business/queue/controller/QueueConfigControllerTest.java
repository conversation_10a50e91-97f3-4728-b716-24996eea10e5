package com.holderzone.saas.store.business.queue.controller;

import com.holderzone.saas.store.business.queue.service.QueueConfigService;
import com.holderzone.saas.store.dto.queue.StoreConfigDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(QueueConfigController.class)
public class QueueConfigControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private QueueConfigService mockConfigService;

    @Test
    public void testConfig() throws Exception {
        // Setup
        // Configure QueueConfigService.config(...).
        final StoreConfigDTO storeConfigDTO = new StoreConfigDTO();
        storeConfigDTO.setGuid("3afc8f98-98e6-466d-a979-64a0fa7730a8");
        storeConfigDTO.setStoreGuid("storeGuid");
        storeConfigDTO.setIsEnableEat(false);
        storeConfigDTO.setIsEnableRecovery(false);
        storeConfigDTO.setRecoveryNum("recoveryNum");
        final StoreConfigDTO dto = new StoreConfigDTO();
        dto.setGuid("3afc8f98-98e6-466d-a979-64a0fa7730a8");
        dto.setStoreGuid("storeGuid");
        dto.setIsEnableEat(false);
        dto.setIsEnableRecovery(false);
        dto.setRecoveryNum("recoveryNum");
        when(mockConfigService.config(dto)).thenReturn(storeConfigDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/queue/config")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testQuery() throws Exception {
        // Setup
        // Configure QueueConfigService.obtain(...).
        final StoreConfigDTO storeConfigDTO = new StoreConfigDTO();
        storeConfigDTO.setGuid("3afc8f98-98e6-466d-a979-64a0fa7730a8");
        storeConfigDTO.setStoreGuid("storeGuid");
        storeConfigDTO.setIsEnableEat(false);
        storeConfigDTO.setIsEnableRecovery(false);
        storeConfigDTO.setRecoveryNum("recoveryNum");
        when(mockConfigService.obtain("storeGuid")).thenReturn(storeConfigDTO);

        // Run the test and verify the results
        mockMvc.perform(get("/queue/config")
                        .param("storeGuid", "storeGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }
}
