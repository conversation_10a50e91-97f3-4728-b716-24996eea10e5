package com.holderzone.saas.store.business.controller;

import com.alibaba.fastjson.JSON;
import com.holderzone.saas.store.business.HolderSaasStoreManageApplication;
import com.holderzone.saas.store.dto.business.brand.BrandConfigQueryDTO;
import com.holderzone.saas.store.dto.business.brand.BrandConfigSaveOrUpdateDTO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * 品牌配置Controller测试类
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = HolderSaasStoreManageApplication.class)
public class BrandConfigControllerTest {

    private static final String BRAND_CONFIG = "/brand_config";
    private static final String USER_INFO = "userInfo";
    private static final String USERINFO = "{\"userGuid\":\"test\",\"userName\":\"test\"}";
    private static final String RESPONSE = "响应结果：";

    @Autowired
    private WebApplicationContext webApplicationContext;

    private MockMvc mockMvc;

    @Before
    public void setUp() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
    }

    @Test
    public void testSaveOrUpdateBrandConfig() throws Exception {
        BrandConfigSaveOrUpdateDTO saveOrUpdateDTO = new BrandConfigSaveOrUpdateDTO();
        saveOrUpdateDTO.setBrandGuid("test-brand-guid");
        saveOrUpdateDTO.setRecoveryTimeLimit(24);
        saveOrUpdateDTO.setRecoveryTimeLimitUnit(1);
        saveOrUpdateDTO.setRefundTimeLimit(7);
        saveOrUpdateDTO.setRefundTimeLimitUnit(1);

        String jsonString = JSON.toJSONString(saveOrUpdateDTO);
        MvcResult mvcResult = mockMvc.perform(post(BRAND_CONFIG + "/save_or_update")
                        .header(USER_INFO, USERINFO)
                        .accept(MediaType.APPLICATION_JSON_VALUE)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(jsonString))
                .andExpect(status().isOk())
                .andDo(print())
                .andReturn();

        String contentAsString = mvcResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    @Test
    public void testSaveOrUpdateBrandConfigWithNullValues() throws Exception {
        BrandConfigSaveOrUpdateDTO saveOrUpdateDTO = new BrandConfigSaveOrUpdateDTO();
        saveOrUpdateDTO.setBrandGuid("test-brand-guid-null");
        // 不设置时效限制字段，测试null值处理
        // saveOrUpdateDTO.setRecoveryTimeLimit(null);
        // saveOrUpdateDTO.setRecoveryTimeLimitUnit(null);
        // saveOrUpdateDTO.setRefundTimeLimit(null);
        // saveOrUpdateDTO.setRefundTimeLimitUnit(null);

        String jsonString = JSON.toJSONString(saveOrUpdateDTO);
        MvcResult mvcResult = mockMvc.perform(post(BRAND_CONFIG + "/save_or_update")
                        .header(USER_INFO, USERINFO)
                        .accept(MediaType.APPLICATION_JSON_VALUE)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(jsonString))
                .andExpect(status().isOk())
                .andDo(print())
                .andReturn();

        String contentAsString = mvcResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    @Test
    public void testGetBrandConfigById() throws Exception {
        MvcResult mvcResult = mockMvc.perform(get(BRAND_CONFIG + "/get/1")
                        .header(USER_INFO, USERINFO)
                        .accept(MediaType.APPLICATION_JSON_VALUE))
                .andExpect(status().isOk())
                .andDo(print())
                .andReturn();

        String contentAsString = mvcResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    @Test
    public void testListBrandConfigByPage() throws Exception {
        BrandConfigQueryDTO queryDTO = new BrandConfigQueryDTO();
        queryDTO.setCurrentPage(1);
        queryDTO.setPageSize(10);

        String jsonString = JSON.toJSONString(queryDTO);
        MvcResult mvcResult = mockMvc.perform(post(BRAND_CONFIG + "/list_by_page")
                        .header(USER_INFO, USERINFO)
                        .accept(MediaType.APPLICATION_JSON_VALUE)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(jsonString))
                .andExpect(status().isOk())
                .andDo(print())
                .andReturn();

        String contentAsString = mvcResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }
}
