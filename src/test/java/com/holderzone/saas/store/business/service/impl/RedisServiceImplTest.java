package com.holderzone.saas.store.business.service.impl;

import com.holderzone.saas.store.dto.trade.PaymentInfoDTO;
import com.holderzone.saas.store.dto.trade.PaymentTypeDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class RedisServiceImplTest {

    @Mock
    private RedisTemplate mockRedisTemplate;

    @InjectMocks
    private RedisServiceImpl redisServiceImplUnderTest;

    @Test
    public void testPutPaymentType() {
        // Setup
        final PaymentTypeDTO paymentTypeDTO = new PaymentTypeDTO();
        paymentTypeDTO.setStoreGuid("storeGuid");
        paymentTypeDTO.setStoreName("storeName");
        paymentTypeDTO.setPaymentTypeGuid("paymentTypeGuid");
        paymentTypeDTO.setPaymentTypeName("paymentTypeName");
        paymentTypeDTO.setPaymentType(0);
        final List<PaymentTypeDTO> all = Arrays.asList(paymentTypeDTO);
        when(mockRedisTemplate.opsForList()).thenReturn(null);

        // Run the test
        redisServiceImplUnderTest.putPaymentType("storeGuid", all);

        // Verify the results
        verify(mockRedisTemplate).expire("key", 2L, TimeUnit.HOURS);
    }

    @Test
    public void testGetPaymentType() {
        // Setup
        final PaymentTypeDTO paymentTypeDTO = new PaymentTypeDTO();
        paymentTypeDTO.setStoreGuid("storeGuid");
        paymentTypeDTO.setStoreName("storeName");
        paymentTypeDTO.setPaymentTypeGuid("paymentTypeGuid");
        paymentTypeDTO.setPaymentTypeName("paymentTypeName");
        paymentTypeDTO.setPaymentType(0);
        final List<PaymentTypeDTO> expectedResult = Arrays.asList(paymentTypeDTO);
        when(mockRedisTemplate.opsForList()).thenReturn(null);

        // Run the test
        final List<PaymentTypeDTO> result = redisServiceImplUnderTest.getPaymentType("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testDeletePaymentType1() {
        // Setup
        // Run the test
        redisServiceImplUnderTest.deletePaymentType("storeGuid");

        // Verify the results
        verify(mockRedisTemplate).delete("key");
    }

    @Test
    public void testDeletePaymentType2() {
        // Setup
        // Run the test
        redisServiceImplUnderTest.deletePaymentType(new HashSet<>(Arrays.asList("value")));

        // Verify the results
        verify(mockRedisTemplate).delete(Arrays.asList("value"));
    }

    @Test
    public void testRemoveSysDiscount() {
        // Setup
        // Run the test
        redisServiceImplUnderTest.removeSysDiscount("storeGuid");

        // Verify the results
        verify(mockRedisTemplate).delete("key");
    }

    @Test
    public void testPutJHPayInfo() {
        // Setup
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setStoreGuid("storeGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");

        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        redisServiceImplUnderTest.putJHPayInfo(paymentInfoDTO);

        // Verify the results
    }

    @Test
    public void testPutJHPayInfoList() {
        // Setup
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setStoreGuid("storeGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        final List<PaymentInfoDTO> paymentInfoDTOList = Arrays.asList(paymentInfoDTO);
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        redisServiceImplUnderTest.putJHPayInfoList(paymentInfoDTOList, "storeGuid");

        // Verify the results
    }

    @Test
    public void testPutJHPayInfoString() {
        // Setup
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        redisServiceImplUnderTest.putJHPayInfoString("payStr", "storeGuid");

        // Verify the results
    }

    @Test
    public void testGetJHPayInfo() {
        // Setup
        final PaymentInfoDTO expectedResult = new PaymentInfoDTO();
        expectedResult.setPaymentInfoGuid("paymentInfoGuid");
        expectedResult.setEnterpriseGuid("enterpriseGuid");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setAppId("appId");
        expectedResult.setAppSecret("appSecret");

        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        final PaymentInfoDTO result = redisServiceImplUnderTest.getJHPayInfo("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetJHPayInfoList() {
        // Setup
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setStoreGuid("storeGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        final List<PaymentInfoDTO> expectedResult = Arrays.asList(paymentInfoDTO);
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        final List<PaymentInfoDTO> result = redisServiceImplUnderTest.getJHPayInfoList("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testDeleteJHPayInfo() {
        // Setup
        // Run the test
        redisServiceImplUnderTest.deleteJHPayInfo("storeGuid");

        // Verify the results
        verify(mockRedisTemplate).delete("key");
    }

    @Test
    public void testRawId() {
        assertThat(redisServiceImplUnderTest.rawId("tag")).isEqualTo(0L);
    }

    @Test
    public void testSingleGuid() {
        assertThat(redisServiceImplUnderTest.singleGuid("tableName")).isEqualTo("result");
    }

    @Test
    public void testNextBatchId() {
        assertThat(redisServiceImplUnderTest.nextBatchId("tag", 0L)).isEqualTo(Arrays.asList("value"));
        assertThat(redisServiceImplUnderTest.nextBatchId("tag", 0L)).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testNextSurchargeGuid() {
        assertThat(redisServiceImplUnderTest.nextSurchargeGuid()).isEqualTo("result");
    }

    @Test
    public void testNextSurchargeAreaGuid() {
        assertThat(redisServiceImplUnderTest.nextSurchargeAreaGuid()).isEqualTo("result");
    }

    @Test
    public void testNextBatchSurchargeAreaGuid() {
        assertThat(redisServiceImplUnderTest.nextBatchSurchargeAreaGuid(0L)).isEqualTo(Arrays.asList("value"));
        assertThat(redisServiceImplUnderTest.nextBatchSurchargeAreaGuid(0L)).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testNextMemberOperateRecordGuid() {
        assertThat(redisServiceImplUnderTest.nextMemberOperateRecordGuid()).isEqualTo("result");
    }
}
