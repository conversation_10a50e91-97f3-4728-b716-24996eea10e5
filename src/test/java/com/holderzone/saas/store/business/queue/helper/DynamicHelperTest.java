package com.holderzone.saas.store.business.queue.helper;

import org.junit.Before;
import org.junit.Test;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.Assert.assertEquals;

public class DynamicHelperTest {

    private DynamicHelper dynamicHelperUnderTest;

    @Before
    public void setUp() throws Exception {
        dynamicHelperUnderTest = new DynamicHelper();
        ReflectionTestUtils.setField(dynamicHelperUnderTest, "openDynamicDatasource", false);
    }

    @Test
    public void testChangeDatasource() {
        // Setup
        // Run the test
        dynamicHelperUnderTest.changeDatasource("enterpriseGuid");

        // Verify the results
    }

    @Test
    public void testChangeRedis() {
        // Setup
        // Run the test
        dynamicHelperUnderTest.changeRedis("enterpriseGuid");

        // Verify the results
    }

    @Test
    public void testRemoveThreadLocalDatabaseInfo() {
        // Setup
        // Run the test
        dynamicHelperUnderTest.removeThreadLocalDatabaseInfo();

        // Verify the results
    }

    @Test
    public void testRemoveThreadLocalRedisInfo() {
        // Setup
        // Run the test
        dynamicHelperUnderTest.removeThreadLocalRedisInfo();

        // Verify the results
    }

    @Test
    public void testGenerateGuid() {
        assertEquals("result", dynamicHelperUnderTest.generateGuid("redisKey"));
    }
}
