package com.holderzone.saas.store.business.controller;

import com.alibaba.fastjson.JSON;
import com.holderzone.saas.store.business.HolderSaasStoreManageApplication;
import com.holderzone.saas.store.business.util.JsonFileUtil;
import com.holderzone.saas.store.dto.business.manage.sync.SurchargeCalculateDTO;
import com.holderzone.saas.store.dto.exception.GroupBuyException;
import com.holderzone.saas.store.dto.trade.req.RecordThirdActivityInfoReqDTO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.io.UnsupportedEncodingException;

import static com.holderzone.saas.store.dto.common.CommonConstant.USER_INFO;
import static org.junit.Assert.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * <AUTHOR>
 * @date 2023/10/18
 * @description
 */
@Slf4j
@WebAppConfiguration
@ContextConfiguration
@AutoConfigureMockMvc
@RunWith(SpringRunner.class)
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
@SpringBootTest(classes = HolderSaasStoreManageApplication.class)
public class MemberOperateRecordControllerTest {

    private static final String MEMBER_OPERATE_RECORD = "/member_operate_record";

    private static final String RESPONSE = "response:";

    private static final String USERINFO = "{\"operSubjectGuid\": \"2008141005594470007\",\"enterpriseGuid\":" +
            " \"6506431195651982337\",\"enterpriseName\": \"企业0227\",\"enterpriseNo\": \"********\",\"storeGuid\":" +
            " \"6619160595813892096\",\"storeName\": \"听雨阁\",\"storeNo\": \"6148139\",\"userGuid\": " +
            "\"6653489337230950401\",\"account\": \"200003\",\"tel\": \"***********\",\"name\": \"赵亮\"}\n";

    private MockMvc mockMvc;

    @Autowired
    private WebApplicationContext wac;

    @Before
    public void setUp() {
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();
    }

    @Test
    public void saveRecord() throws UnsupportedEncodingException {
        RecordThirdActivityInfoReqDTO reqSaveRecordDTO = JSON.parseObject(JsonFileUtil.read("record/saveRecord.json"),
                RecordThirdActivityInfoReqDTO.class);
        String jsonSaveRecord = JSON.toJSONString(reqSaveRecordDTO);
        MvcResult mvcSaveRecordResult = null;
        try {
            mvcSaveRecordResult = mockMvc.perform(post(MEMBER_OPERATE_RECORD + "/save_record")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(jsonSaveRecord))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new GroupBuyException(e.getMessage());
        }
        String contentAsString = mvcSaveRecordResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

}