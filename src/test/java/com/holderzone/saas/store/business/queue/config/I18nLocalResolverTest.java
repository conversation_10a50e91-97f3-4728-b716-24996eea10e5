package com.holderzone.saas.store.business.queue.config;

import org.junit.Before;
import org.junit.Test;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

import java.util.Locale;

import static org.junit.Assert.assertEquals;

public class I18nLocalResolverTest {

    private I18nLocalResolver i18nLocalResolverUnderTest;

    @Before
    public void setUp() {
        i18nLocalResolverUnderTest = new I18nLocalResolver();
    }

    @Test
    public void testResolveLocale() {
        // Setup
        final MockHttpServletRequest request = new MockHttpServletRequest();

        // Run the test
        final Locale result = i18nLocalResolverUnderTest.resolveLocale(request);

        // Verify the results
        assertEquals(Locale.US, result);
    }

    @Test
    public void testSetLocale() {
        // Setup
        final MockHttpServletRequest request = new MockHttpServletRequest();
        final MockHttpServletResponse response = new MockHttpServletResponse();

        // Run the test
        i18nLocalResolverUnderTest.setLocale(request, response, Locale.US);

        // Verify the results
    }

    @Test(expected = UnsupportedOperationException.class)
    public void testSetLocale_ThrowsUnsupportedOperationException() {
        // Setup
        final MockHttpServletRequest request = new MockHttpServletRequest();
        final MockHttpServletResponse response = new MockHttpServletResponse();

        // Run the test
        i18nLocalResolverUnderTest.setLocale(request, response, Locale.US);
    }
}
