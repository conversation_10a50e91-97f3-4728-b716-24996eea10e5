package com.holderzone.saas.store.business.service.impl;

import com.holderzone.saas.store.business.service.client.SmsRechargeClient;
import com.holderzone.saas.store.dto.business.manage.ChargeDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class SmsRechargeServiceImplTest {

    @Mock
    private SmsRechargeClient mockSmsRechargeClient;

    @InjectMocks
    private SmsRechargeServiceImpl smsRechargeServiceImplUnderTest;

    @Test
    public void testFindAllProduct() {
        // Setup
        final ChargeDTO chargeDTO = new ChargeDTO();
        chargeDTO.setChargeGuid("chargeGuid");
        chargeDTO.setProductGuid("productGuid");
        chargeDTO.setChargeType("chargeType");
        chargeDTO.setUnitPrice(new BigDecimal("0.00"));
        chargeDTO.setUnit(0);
        final List<ChargeDTO> expectedResult = Arrays.asList(chargeDTO);

        // Configure SmsRechargeClient.getChargeListForSMS(...).
        final ChargeDTO chargeDTO1 = new ChargeDTO();
        chargeDTO1.setChargeGuid("chargeGuid");
        chargeDTO1.setProductGuid("productGuid");
        chargeDTO1.setChargeType("chargeType");
        chargeDTO1.setUnitPrice(new BigDecimal("0.00"));
        chargeDTO1.setUnit(0);
        final List<ChargeDTO> chargeDTOS = Arrays.asList(chargeDTO1);
        when(mockSmsRechargeClient.getChargeListForSMS()).thenReturn(chargeDTOS);

        // Run the test
        final List<ChargeDTO> result = smsRechargeServiceImplUnderTest.findAllProduct();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testFindAllProduct_SmsRechargeClientReturnsNoItems() {
        // Setup
        when(mockSmsRechargeClient.getChargeListForSMS()).thenReturn(Collections.emptyList());

        // Run the test
        final List<ChargeDTO> result = smsRechargeServiceImplUnderTest.findAllProduct();

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }
}
