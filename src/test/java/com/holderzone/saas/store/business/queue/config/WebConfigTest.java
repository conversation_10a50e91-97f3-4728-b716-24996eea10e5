package com.holderzone.saas.store.business.queue.config;

import org.junit.Before;
import org.junit.Test;
import org.springframework.web.servlet.LocaleResolver;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;

public class WebConfigTest {

    private WebConfig webConfigUnderTest;

    @Before
    public void setUp() throws Exception {
        webConfigUnderTest = new WebConfig();
    }

    @Test
    public void testLocaleResolver() {
        // Setup
        // Run the test
        final LocaleResolver result = webConfigUnderTest.localeResolver();

        // Verify the results
    }

    @Test
    public void testAddInterceptors() {
        // Setup
        final InterceptorRegistry registry = new InterceptorRegistry();

        // Run the test
        webConfigUnderTest.addInterceptors(registry);

        // Verify the results
    }
}
