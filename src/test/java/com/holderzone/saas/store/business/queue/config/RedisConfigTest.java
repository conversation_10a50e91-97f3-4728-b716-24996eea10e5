package com.holderzone.saas.store.business.queue.config;

import org.junit.Before;
import org.junit.Test;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;

public class RedisConfigTest {

    private RedisConfig redisConfigUnderTest;

    @Before
    public void setUp() throws Exception {
        redisConfigUnderTest = new RedisConfig();
    }

    @Test
    public void testRedisCacheManager() {
        // Setup
        final RedisConnectionFactory connectionFactory = null;

        // Run the test
        final RedisCacheManager result = redisConfigUnderTest.redisCacheManager(connectionFactory);

        // Verify the results
    }

    @Test
    public void testRedisCacheConfiguration() {
        // Setup
        // Run the test
        final RedisCacheConfiguration result = redisConfigUnderTest.redisCacheConfiguration();

        // Verify the results
    }

    @Test
    public void testStringRedisTemplate() {
        // Setup
        final RedisConnectionFactory factory = null;

        // Run the test
        final StringRedisTemplate result = redisConfigUnderTest.stringRedisTemplate(factory);

        // Verify the results
    }

    @Test
    public void testRedisTemplate() {
        // Setup
        final RedisConnectionFactory factory = null;

        // Run the test
        final RedisTemplate result = redisConfigUnderTest.redisTemplate(factory);

        // Verify the results
    }
}
