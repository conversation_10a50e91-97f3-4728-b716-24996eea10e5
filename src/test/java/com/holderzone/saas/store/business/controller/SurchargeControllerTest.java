package com.holderzone.saas.store.business.controller;

import com.alibaba.fastjson.JSON;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.business.HolderSaasStoreManageApplication;
import com.holderzone.saas.store.business.util.JsonFileUtil;
import com.holderzone.saas.store.dto.business.manage.*;
import com.holderzone.saas.store.dto.business.manage.sync.SurchargeCalculateDTO;
import com.holderzone.saas.store.dto.exception.GroupBuyException;
import com.holderzone.saas.store.dto.exception.TestException;
import com.holderzone.saas.store.dto.trade.req.RecordThirdActivityInfoReqDTO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.List;

import static com.holderzone.saas.store.dto.common.CommonConstant.USER_INFO;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@Slf4j
@WebAppConfiguration
@ContextConfiguration
@AutoConfigureMockMvc
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
@SpringBootTest(classes = HolderSaasStoreManageApplication.class)
@RunWith(SpringRunner.class)
public class SurchargeControllerTest {

    private static final String SURCHARGE = "/surcharge";

    private static final String RESPONSE = "response:";

    private static final String USERINFO = "{\"operSubjectGuid\": \"2008141005594470007\",\"enterpriseGuid\":" +
            " \"6506431195651982337\",\"enterpriseName\": \"企业0227\",\"enterpriseNo\": \"********\",\"storeGuid\":" +
            " \"6619160595813892096\",\"storeName\": \"听雨阁\",\"storeNo\": \"6148139\",\"userGuid\": " +
            "\"6653489337230950401\",\"account\": \"200003\",\"tel\": \"***********\",\"name\": \"赵亮\"}\n";

    @Autowired
    private WebApplicationContext wac;

    private MockMvc mockMvc;

    public static String SURCHARGE_GUID = "6506433870517239818";

    @Before
    public void setUp() {
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();
    }

    @Test
    public void calculateSurcharge() throws Exception {
        SurchargeCalculateDTO query = new SurchargeCalculateDTO();
        query.setGuestCount(3);
        query.setTableGuid("6506433870517239818");
        query.setEnterpriseGuid("6506431195651982337");
        String jsonString = JSON.toJSONString(query);
        MvcResult mvcResult = mockMvc.perform(post(SURCHARGE + "/calculate_surcharge")
                .header(USER_INFO, USERINFO)
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(jsonString))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println(RESPONSE + contentAsString);
    }

    @Test
    public void listByAreaGuid() throws UnsupportedEncodingException {
        List<String> areaList = new ArrayList<>();
        areaList.add("6978614792504213505");
        String jsonListByAreaGuid = JSON.toJSONString(areaList);
        MvcResult mvcListByAreaGuidResult = null;
        try {
            mvcListByAreaGuidResult = mockMvc.perform(post(SURCHARGE + "/list_by_area_guid")
                    .header(USER_INFO, USERINFO)
                    .accept(MediaType.APPLICATION_JSON_VALUE)
                    .contentType(MediaType.APPLICATION_JSON).content(jsonListByAreaGuid))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new GroupBuyException(e.getMessage());
        }
        String contentAsString = mvcListByAreaGuidResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    @Test
    public void listByCondition() throws UnsupportedEncodingException {
        RecordThirdActivityInfoReqDTO reqListByConditionDTO = JSON.parseObject(JsonFileUtil.read("surcharge/listByCondition.json"),
                RecordThirdActivityInfoReqDTO.class);
        String jsonListByCondition = JSON.toJSONString(reqListByConditionDTO);
        MvcResult mvcListByConditionResult = null;
        try {
            mvcListByConditionResult = mockMvc.perform(post(SURCHARGE + "/list_by_condition")
                    .header(USER_INFO, USERINFO)
                    .accept(MediaType.APPLICATION_JSON_VALUE)
                    .contentType(MediaType.APPLICATION_JSON).content(jsonListByCondition))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new GroupBuyException(e.getMessage());
        }
        String contentAsString = mvcListByConditionResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    /**
     * 新增附加费
     * 场景设置
     * 时效
     */
    @Test
    public void create() throws UnsupportedEncodingException {
        SurchargeCreateDTO surchargeCreateDTO = JSON.parseObject(JsonFileUtil.read("surcharge/create.json"),
                SurchargeCreateDTO.class);
        String surchargeCreateJsonStr = JSON.toJSONString(surchargeCreateDTO);
        MvcResult mvcSurchargeCreateResult = null;
        try {
            mvcSurchargeCreateResult = mockMvc.perform(post(SURCHARGE + "/create")
                    .header(USER_INFO, USERINFO)
                    .accept(MediaType.APPLICATION_JSON_VALUE)
                    .contentType(MediaType.APPLICATION_JSON).content(surchargeCreateJsonStr))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new TestException(e.getMessage());
        }
        String content = mvcSurchargeCreateResult.getResponse().getContentAsString();
        log.info(RESPONSE + content);
    }

    /**
     * 编辑附加费
     */
    @Test
    public void update() throws UnsupportedEncodingException {
        SurchargeUpdateDTO surchargeUpdateDTO = JSON.parseObject(JsonFileUtil.read("surcharge/update.json"),
                SurchargeUpdateDTO.class);
        String surchargeUpdateJsonStr = JSON.toJSONString(surchargeUpdateDTO);
        MvcResult mvcSurchargeUpdateResult = null;
        try {
            mvcSurchargeUpdateResult = mockMvc.perform(post(SURCHARGE + "/update")
                    .header(USER_INFO, USERINFO)
                    .accept(MediaType.APPLICATION_JSON_VALUE)
                    .contentType(MediaType.APPLICATION_JSON).content(surchargeUpdateJsonStr))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new TestException(e.getMessage());
        }
        String content = mvcSurchargeUpdateResult.getResponse().getContentAsString();
        log.info(RESPONSE + content);
    }


    /**
     * 查询附加费详情
     */
    @Test
    public void query() throws UnsupportedEncodingException {
        SurchargeQueryDTO queryDTO = new SurchargeQueryDTO();
        queryDTO.setSurchargeGuid(SURCHARGE_GUID);
        String surchargeQueryJsonStr = JSON.toJSONString(queryDTO);
        MvcResult mvcSurchargeQueryResult = null;
        try {
            mvcSurchargeQueryResult = mockMvc.perform(post(SURCHARGE + "/query")
                    .header(USER_INFO, USERINFO)
                    .accept(MediaType.APPLICATION_JSON_VALUE)
                    .contentType(MediaType.APPLICATION_JSON).content(surchargeQueryJsonStr))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new TestException(e.getMessage());
        }
        String content = mvcSurchargeQueryResult.getResponse().getContentAsString();
        log.info(RESPONSE + content);
        SurchargeDTO surchargeDTO = JacksonUtils.toObject(SurchargeDTO.class, content);
        log.info("附加费详情返回:{}", JacksonUtils.writeValueAsString(surchargeDTO));
    }


    /**
     * 启用附加费
     */
    @Test
    public void enable() throws UnsupportedEncodingException {
        SurchargeEnableDTO enableDTO = new SurchargeEnableDTO();
        enableDTO.setSurchargeGuid(SURCHARGE_GUID);
        enableDTO.setIsEnable(true);
        String surchargeEnableJsonStr = JSON.toJSONString(enableDTO);
        MvcResult mvcSurchargeEnableResult = null;
        try {
            mvcSurchargeEnableResult = mockMvc.perform(post(SURCHARGE + "/enable")
                    .header(USER_INFO, USERINFO)
                    .accept(MediaType.APPLICATION_JSON_VALUE)
                    .contentType(MediaType.APPLICATION_JSON).content(surchargeEnableJsonStr))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new TestException(e.getMessage());
        }
        String content = mvcSurchargeEnableResult.getResponse().getContentAsString();
        log.info(RESPONSE + content);
    }

    /**
     * 禁用附加费
     */
    @Test
    public void disable() throws UnsupportedEncodingException {
        SurchargeEnableDTO disableDTO = new SurchargeEnableDTO();
        disableDTO.setSurchargeGuid(SURCHARGE_GUID);
        disableDTO.setIsEnable(false);
        String surchargeDisableJsonStr = JSON.toJSONString(disableDTO);
        MvcResult mvcSurchargeDisableResult = null;
        try {
            mvcSurchargeDisableResult = mockMvc.perform(post(SURCHARGE + "/enable")
                    .header(USER_INFO, USERINFO)
                    .accept(MediaType.APPLICATION_JSON_VALUE)
                    .contentType(MediaType.APPLICATION_JSON).content(surchargeDisableJsonStr))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new TestException(e.getMessage());
        }
        String content = mvcSurchargeDisableResult.getResponse().getContentAsString();
        log.info(RESPONSE + content);
    }


    /**
     * 批量启用附加费
     */
    @Test
    public void batchEnable() throws UnsupportedEncodingException {
        SurchargeBatchEnableDTO surchargeBatchEnableDTO = JSON.parseObject(JsonFileUtil.read("surcharge/batch_enable.json"),
                SurchargeBatchEnableDTO.class);
        String surchargeBatchEnableJsonStr = JSON.toJSONString(surchargeBatchEnableDTO);
        MvcResult mvcSurchargeBatchEnableResult = null;
        try {
            mvcSurchargeBatchEnableResult = mockMvc.perform(post(SURCHARGE + "/batch_enable")
                    .header(USER_INFO, USERINFO)
                    .accept(MediaType.APPLICATION_JSON_VALUE)
                    .contentType(MediaType.APPLICATION_JSON).content(surchargeBatchEnableJsonStr))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new TestException(e.getMessage());
        }
        String content = mvcSurchargeBatchEnableResult.getResponse().getContentAsString();
        log.info(RESPONSE + content);
    }

    /**
     * 批量启用附加费
     */
    @Test
    public void batchDisable() throws UnsupportedEncodingException {
        SurchargeBatchEnableDTO surchargeBatchDisableDTO = JSON.parseObject(JsonFileUtil.read("surcharge/batch_disable.json"),
                SurchargeBatchEnableDTO.class);
        String surchargeBatchDisableJsonStr = JSON.toJSONString(surchargeBatchDisableDTO);
        MvcResult mvcSurchargeBatchDisableResult = null;
        try {
            mvcSurchargeBatchDisableResult = mockMvc.perform(post(SURCHARGE + "/batch_enable")
                    .header(USER_INFO, USERINFO)
                    .accept(MediaType.APPLICATION_JSON_VALUE)
                    .contentType(MediaType.APPLICATION_JSON).content(surchargeBatchDisableJsonStr))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new TestException(e.getMessage());
        }
        String content = mvcSurchargeBatchDisableResult.getResponse().getContentAsString();
        log.info(RESPONSE + content);
    }

    /**
     * 批量删除附加费
     */
    @Test
    public void batchDelete() throws UnsupportedEncodingException {
        SurchargeBatchDeleteDTO surchargeBatchDeleteDTO = JSON.parseObject(JsonFileUtil.read("surcharge/batch_delete.json"),
                SurchargeBatchDeleteDTO.class);
        String surchargeBatchDeleteJsonStr = JSON.toJSONString(surchargeBatchDeleteDTO);
        MvcResult mvcSurchargeBatchDeleteResult = null;
        try {
            mvcSurchargeBatchDeleteResult = mockMvc.perform(post(SURCHARGE + "/batch_delete")
                    .header(USER_INFO, USERINFO)
                    .accept(MediaType.APPLICATION_JSON_VALUE)
                    .contentType(MediaType.APPLICATION_JSON).content(surchargeBatchDeleteJsonStr))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new TestException(e.getMessage());
        }
        String content = mvcSurchargeBatchDeleteResult.getResponse().getContentAsString();
        log.info(RESPONSE + content);
    }
}