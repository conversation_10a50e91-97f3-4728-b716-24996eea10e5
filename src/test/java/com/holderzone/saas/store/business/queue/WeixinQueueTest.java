package com.holderzone.saas.store.business.queue;

import com.alibaba.fastjson.JSON;
import com.holderzone.saas.store.dto.business.reason.ReasonDTO;
import com.holderzone.saas.store.dto.queue.QueueWechatDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import static com.holderzone.saas.store.dto.common.CommonConstant.USER_INFO;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReserveRecordMvcTest
 * @date 2018/08/02 上午10:03
 * @description //TODO
 * @program holder-saas-store-business
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class WeixinQueueTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    private MockMvc mockMvc;

    private String storeGuid;

    private String additionalFeeGuid;

    private static final String encode = "%7B%22enterpriseGuid%22%3A%226506431195651982337%22%2C%22enterpriseName%22" +
            "%3A%22%E4%BC%81%E4%B8%9A0227%22%2C%22enterpriseNo%22%3A%**********%22%2C%22storeGuid%22%3A" +
            "%226506453252643487745%22%2C%22storeName%22%3A%22%E9%97%A8%E5%BA%970227_3%22%2C%22storeNo%22%3A" +
            "%*********%22%2C%22userGuid%22%3A%226507063794701697025%22%2C%22account%22%3A%********%22%2C%22tel%22%3A" +
            "%*************%22%2C%22name%22%3A%22wg%22%7D";

    @Before
    public void setup() {
        this.mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
//        storeGuid = "fa10a446-1869-4da7-b322-3c012470b5c1";
//        additionalFeeGuid = "fb3c7fef-c972-47d8-85ea-b07548cb18cb";
    }

    @Test
    public void testFindQueue() throws Exception {
        QueueWechatDTO queueWechatDTO = new QueueWechatDTO();
        queueWechatDTO.setBrandGuid("6506433399404625921");
        queueWechatDTO.setUserGuid("6523761222599311361");
        String json = JSON.toJSONString(queueWechatDTO);
        MvcResult mvcResult = mockMvc.perform(post("/queue/queryByUser")
                .header(USER_INFO, encode)
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON)
                .content(json))
                .andExpect(status().isOk())
                .andDo(print())
                .andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);


    }

    @Test
    public void testUpdateReason() throws Exception {

        ReasonDTO reasonDTO = new ReasonDTO();
        reasonDTO.setReasonGuid("6568035530548707328");
        reasonDTO.setReason("味道还是可以的!!!");
        String json = JSON.toJSONString(reasonDTO);
        MvcResult mvcResult = mockMvc.perform(post("/reason/updateReason")
                .header(USER_INFO, encode)
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON)
                .content(json))
                .andExpect(status().isOk())
                .andDo(print())
                .andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);


    }

    @Test
    public void testDeleteReason() throws Exception {

        ReasonDTO reasonDTO = new ReasonDTO();
        reasonDTO.setReasonGuid("6568033341793107968");
        String json = JSON.toJSONString(reasonDTO);
        MvcResult mvcResult = mockMvc.perform(post("/reason/deleteReason")
                .header(USER_INFO, encode)
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON)
                .content(json))
                .andExpect(status().isOk())
                .andDo(print())
                .andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }

    @Test
    public void testInsertReason() throws Exception {

        ReasonDTO reasonDTO = new ReasonDTO();
        reasonDTO.setReasonTypeCode(1);
        reasonDTO.setStoreGuid("6515841002941448193");
        String s = "";
        for (int i = 0; i < 3; i++) {
            s = s + "表";
        }
        reasonDTO.setReason(s);
        String json = JSON.toJSONString(reasonDTO);
        MvcResult mvcResult = mockMvc.perform(post("/reason/insertReason")
                .header(USER_INFO, encode)
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON)
                .content(json))
                .andExpect(status().isOk())
                .andDo(print())
                .andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }

    @Test
    public void testFindReasonType() throws Exception {

        String json = JSON.toJSONString("");
        MvcResult mvcResult = mockMvc.perform(post("/reason/findReasonType")
                .header(USER_INFO, encode)
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON)
                .content(json))
                .andExpect(status().isOk())
                .andDo(print())
                .andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }
}