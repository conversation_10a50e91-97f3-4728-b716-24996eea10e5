package com.holderzone.saas.store.organization.controller;

import com.holderzone.resource.common.dto.holder.organization.HolderOrganizationResultDTO;
import com.holderzone.saas.store.dto.organization.OrgGeneralDTO;
import com.holderzone.saas.store.dto.organization.OrganizationDTO;
import com.holderzone.saas.store.organization.service.OrganizationService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

@RunWith(SpringRunner.class)
@WebMvcTest(OrganizationController.class)
public class OrganizationControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private OrganizationService mockOrganizationService;

    @Test
    public void testCreateOrganization() throws Exception {
        // Setup
        // Configure OrganizationService.createOrganization(...).
        final OrganizationDTO organizationDTO = new OrganizationDTO();
        organizationDTO.setGuid("24d5d6fe-b7ca-4c37-a202-b4afcaf3ccbc");
        organizationDTO.setUuid("1b932347-5168-4e2e-bad0-28f4cc692e04");
        organizationDTO.setName("name");
        organizationDTO.setContactName("contactName");
        organizationDTO.setContactTel("contactTel");
        when(mockOrganizationService.createOrganization(any(OrganizationDTO.class))).thenReturn(organizationDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/com/holderzone/saas/store/organization/create")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testCreateBatchOrganization() throws Exception {
        // Setup
        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/com/holderzone/saas/store/organization/batch/create")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());

        // Confirm OrganizationService.createBatchOrganization(...).
        final HolderOrganizationResultDTO holderOrganizationResultDTO = new HolderOrganizationResultDTO();
        holderOrganizationResultDTO.setId(0L);
        holderOrganizationResultDTO.setParentId(0L);
        holderOrganizationResultDTO.setParentName("parentName");
        holderOrganizationResultDTO.setParentIds("parentIds");
        holderOrganizationResultDTO.setType(0);
        final List<HolderOrganizationResultDTO> holderOrganizationResultList = Arrays.asList(
                holderOrganizationResultDTO);
        verify(mockOrganizationService).createBatchOrganization(holderOrganizationResultList);
    }

    @Test
    public void testUpdateOrganization() throws Exception {
        // Setup
        when(mockOrganizationService.updateOrganization(any(OrganizationDTO.class))).thenReturn(false);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/com/holderzone/saas/store/organization/update")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testUpdateOrganization_OrganizationServiceReturnsTrue() throws Exception {
        // Setup
        when(mockOrganizationService.updateOrganization(any(OrganizationDTO.class))).thenReturn(true);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/com/holderzone/saas/store/organization/update")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testDeleteOrganization() throws Exception {
        // Setup
        when(mockOrganizationService.deleteOrganization("organizationGuid")).thenReturn(false);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/com/holderzone/saas/store/organization/delete")
                        .param("organizationGuid", "organizationGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testDeleteOrganization_OrganizationServiceReturnsTrue() throws Exception {
        // Setup
        when(mockOrganizationService.deleteOrganization("organizationGuid")).thenReturn(true);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/com/holderzone/saas/store/organization/delete")
                        .param("organizationGuid", "organizationGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testQueryExistOrganizationOrStore() throws Exception {
        // Setup
        when(mockOrganizationService.queryExistOrganizationOrStore("organizationGuid")).thenReturn(false);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/com/holderzone/saas/store/organization/query_exist_organization_or_store")
                        .param("organizationGuid", "organizationGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testQueryExistOrganizationOrStore_OrganizationServiceReturnsTrue() throws Exception {
        // Setup
        when(mockOrganizationService.queryExistOrganizationOrStore("organizationGuid")).thenReturn(true);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/com/holderzone/saas/store/organization/query_exist_organization_or_store")
                        .param("organizationGuid", "organizationGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testQueryExistAccount() throws Exception {
        // Setup
        when(mockOrganizationService.queryExistAccount("organizationGuid")).thenReturn(false);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/com/holderzone/saas/store/organization/query_exist_account")
                        .param("organizationGuid", "organizationGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testQueryExistAccount_OrganizationServiceReturnsTrue() throws Exception {
        // Setup
        when(mockOrganizationService.queryExistAccount("organizationGuid")).thenReturn(true);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/com/holderzone/saas/store/organization/query_exist_account")
                        .param("organizationGuid", "organizationGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testGetOptionalOrganization() throws Exception {
        // Setup
        // Configure OrganizationService.getOptionalOrganization(...).
        final OrganizationDTO organizationDTO = new OrganizationDTO();
        organizationDTO.setGuid("24d5d6fe-b7ca-4c37-a202-b4afcaf3ccbc");
        organizationDTO.setUuid("1b932347-5168-4e2e-bad0-28f4cc692e04");
        organizationDTO.setName("name");
        organizationDTO.setContactName("contactName");
        organizationDTO.setContactTel("contactTel");
        final List<OrganizationDTO> organizationDTOS = Arrays.asList(organizationDTO);
        when(mockOrganizationService.getOptionalOrganization("organizationGuid")).thenReturn(organizationDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/com/holderzone/saas/store/organization/get_optional_organization")
                        .param("organizationGuid", "organizationGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testGetOptionalOrganization_OrganizationServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockOrganizationService.getOptionalOrganization("organizationGuid")).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/com/holderzone/saas/store/organization/get_optional_organization")
                        .param("organizationGuid", "organizationGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("[]", response.getContentAsString());
    }

    @Test
    public void testQueryEnterpriseAndOrganization() throws Exception {
        // Setup
        // Configure OrganizationService.queryEnterpriseAndOrganization(...).
        final OrganizationDTO organizationDTO = new OrganizationDTO();
        organizationDTO.setGuid("24d5d6fe-b7ca-4c37-a202-b4afcaf3ccbc");
        organizationDTO.setUuid("1b932347-5168-4e2e-bad0-28f4cc692e04");
        organizationDTO.setName("name");
        organizationDTO.setContactName("contactName");
        organizationDTO.setContactTel("contactTel");
        final List<OrganizationDTO> organizationDTOS = Arrays.asList(organizationDTO);
        when(mockOrganizationService.queryEnterpriseAndOrganization()).thenReturn(organizationDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/com/holderzone/saas/store/organization/query_enterprise_and_organization")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testQueryEnterpriseAndOrganization_OrganizationServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockOrganizationService.queryEnterpriseAndOrganization()).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/com/holderzone/saas/store/organization/query_enterprise_and_organization")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("[]", response.getContentAsString());
    }

    @Test
    public void testQueryOrganizationList() throws Exception {
        // Setup
        // Configure OrganizationService.queryOrganizationList(...).
        final OrganizationDTO organizationDTO = new OrganizationDTO();
        organizationDTO.setGuid("24d5d6fe-b7ca-4c37-a202-b4afcaf3ccbc");
        organizationDTO.setUuid("1b932347-5168-4e2e-bad0-28f4cc692e04");
        organizationDTO.setName("name");
        organizationDTO.setContactName("contactName");
        organizationDTO.setContactTel("contactTel");
        final List<OrganizationDTO> organizationDTOS = Arrays.asList(organizationDTO);
        when(mockOrganizationService.queryOrganizationList()).thenReturn(organizationDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/com/holderzone/saas/store/organization/query_organization/list")
                        .param("enterpriseGuid", "enterpriseGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testQueryOrganizationList_OrganizationServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockOrganizationService.queryOrganizationList()).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/com/holderzone/saas/store/organization/query_organization/list")
                        .param("enterpriseGuid", "enterpriseGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("[]", response.getContentAsString());
    }

    @Test
    public void testQueryAllOrganization() throws Exception {
        // Setup
        // Configure OrganizationService.queryAllOrganization(...).
        final List<OrgGeneralDTO> orgGeneralDTOS = Arrays.asList(
                new OrgGeneralDTO("73bd316e-fbca-475d-8f18-14850ef0acb8", "name", 0, false, Arrays.asList()));
        when(mockOrganizationService.queryAllOrganization()).thenReturn(orgGeneralDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/com/holderzone/saas/store/organization/query_all_organization")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testQueryAllOrganization_OrganizationServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockOrganizationService.queryAllOrganization()).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/com/holderzone/saas/store/organization/query_all_organization")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("[]", response.getContentAsString());
    }

    @Test
    public void testIsExistOrganization() throws Exception {
        // Setup
        when(mockOrganizationService.isExistOrganization()).thenReturn(false);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/com/holderzone/saas/store/organization/is_exist_organization")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testIsExistOrganization_OrganizationServiceReturnsTrue() throws Exception {
        // Setup
        when(mockOrganizationService.isExistOrganization()).thenReturn(true);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/com/holderzone/saas/store/organization/is_exist_organization")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testQueryErpOrgStore() throws Exception {
        // Setup
        // Configure OrganizationService.queryErpAndOrgAndStore(...).
        final List<OrgGeneralDTO> orgGeneralDTOS = Arrays.asList(
                new OrgGeneralDTO("73bd316e-fbca-475d-8f18-14850ef0acb8", "name", 0, false, Arrays.asList()));
        when(mockOrganizationService.queryErpAndOrgAndStore(0, 0)).thenReturn(orgGeneralDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/com/holderzone/saas/store/organization/query_erp_org_store")
                        .param("queryErp", "0")
                        .param("queryStore", "0")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testQueryErpOrgStore_OrganizationServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockOrganizationService.queryErpAndOrgAndStore(0, 0)).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/com/holderzone/saas/store/organization/query_erp_org_store")
                        .param("queryErp", "0")
                        .param("queryStore", "0")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("[]", response.getContentAsString());
    }

    @Test
    public void testQueryOrgByChildIdList() throws Exception {
        // Setup
        // Configure OrganizationService.queryOrgByChildIdList(...).
        final List<OrgGeneralDTO> orgGeneralDTOS = Arrays.asList(
                new OrgGeneralDTO("73bd316e-fbca-475d-8f18-14850ef0acb8", "name", 0, false, Arrays.asList()));
        when(mockOrganizationService.queryOrgByChildIdList(Arrays.asList("value"))).thenReturn(orgGeneralDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/com/holderzone/saas/store/organization/query_org_by_child")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testQueryOrgByChildIdList_OrganizationServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockOrganizationService.queryOrgByChildIdList(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/com/holderzone/saas/store/organization/query_org_by_child")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("[]", response.getContentAsString());
    }

    @Test
    public void testQueryOrgParentList() throws Exception {
        // Setup
        when(mockOrganizationService.queryOrgParentList(Arrays.asList("value"))).thenReturn(new HashMap<>());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/com/holderzone/saas/store/organization/query_org_parent_list")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testQueryOrgByIdList() throws Exception {
        // Setup
        // Configure OrganizationService.queryOrgByIdList(...).
        final OrganizationDTO organizationDTO = new OrganizationDTO();
        organizationDTO.setGuid("24d5d6fe-b7ca-4c37-a202-b4afcaf3ccbc");
        organizationDTO.setUuid("1b932347-5168-4e2e-bad0-28f4cc692e04");
        organizationDTO.setName("name");
        organizationDTO.setContactName("contactName");
        organizationDTO.setContactTel("contactTel");
        final List<OrganizationDTO> organizationDTOS = Arrays.asList(organizationDTO);
        when(mockOrganizationService.queryOrgByIdList(Arrays.asList("value"))).thenReturn(organizationDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/com/holderzone/saas/store/organization/query_org_by_idlist")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testQueryOrgByIdList_OrganizationServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockOrganizationService.queryOrgByIdList(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/com/holderzone/saas/store/organization/query_org_by_idlist")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("[]", response.getContentAsString());
    }

    @Test
    public void testQueryAllChildOrg() throws Exception {
        // Setup
        when(mockOrganizationService.queryAllChildOrg(Arrays.asList("value"))).thenReturn(Arrays.asList("value"));

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/com/holderzone/saas/store/organization/query_all_child_org")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testQueryAllChildOrg_OrganizationServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockOrganizationService.queryAllChildOrg(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/com/holderzone/saas/store/organization/query_all_child_org")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("[]", response.getContentAsString());
    }
}
