package com.holderzone.saas.store.organization.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.resource.common.dto.holder.organization.HolderOrganizationResultDTO;
import com.holderzone.saas.store.dto.organization.OrgGeneralDTO;
import com.holderzone.saas.store.dto.organization.OrganizationDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.organization.domain.OrganizationDO;
import com.holderzone.saas.store.organization.mapper.OrganizationMapper;
import com.holderzone.saas.store.organization.mapstruct.OrganizationMapstruct;
import com.holderzone.saas.store.organization.mapstruct.StoreMapstruct;
import com.holderzone.saas.store.organization.service.DistributedIdService;
import com.holderzone.saas.store.organization.service.StoreService;
import com.holderzone.saas.store.organization.service.impl.OrganizationServiceImpl;
import com.holderzone.saas.store.organization.service.remote.UserClient;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class OrganizationServiceImplTest {

    @Mock
    private OrganizationMapstruct mockOrganizationMapstruct;
    @Mock
    private OrganizationMapper mockOrganizationMapper;
    @Mock
    private DistributedIdService mockDistributedIdService;
    @Mock
    private StoreService mockStoreService;
    @Mock
    private StoreMapstruct mockStoreMapstruct;
    @Mock
    private UserClient mockUserClient;
    @Mock
    private ThreadPoolTaskExecutor mockTaskExecutor;

    private OrganizationServiceImpl organizationServiceImplUnderTest;

    @Before
    public void setUp() {
        organizationServiceImplUnderTest = new OrganizationServiceImpl(mockOrganizationMapstruct,
                mockOrganizationMapper, mockDistributedIdService, mockStoreService, mockStoreMapstruct, mockUserClient);
        ReflectionTestUtils.setField(organizationServiceImplUnderTest, "taskExecutor", mockTaskExecutor);
    }

    @Test
    public void testCreateOrganization() {
        // Setup
        final OrganizationDTO organizationDTO = new OrganizationDTO();
        organizationDTO.setGuid("********-74ff-4f0e-ad5e-96d415ca6c7e");
        organizationDTO.setName("中国");
        organizationDTO.setParentIds("enterpriseGuid");
        organizationDTO.setCreateUserGuid("企业初始化");
        organizationDTO.setModifiedUserGuid("企业初始化");
        organizationDTO.setChildOrganizationDTOList(Arrays.asList(new OrganizationDTO()));
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("df79f94d-8cf0-4aca-862b-cd1e80be2145");
        storeDTO.setName("我的门店");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setIsEnable(false);
        storeDTO.setCreateUserGuid("企业初始化");
        storeDTO.setModifiedUserGuid("企业初始化");
        organizationDTO.setStoreList(Arrays.asList(storeDTO));

        when(mockOrganizationMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(0);
        when(mockDistributedIdService.nextOrganizationGuid()).thenReturn("df79f94d-8cf0-4aca-862b-cd1e80be2145");

        // Configure OrganizationMapstruct.organizationDTO2DO(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("df79f94d-8cf0-4aca-862b-cd1e80be2145");
        organizationDO.setType(0);
        organizationDO.setName("我的门店");
        organizationDO.setParentIds("parentIds");
        organizationDO.setCreateUserGuid("云端同步");
        organizationDO.setModifiedUserGuid("云端同步");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        when(mockOrganizationMapstruct.organizationDTO2DO(any(OrganizationDTO.class))).thenReturn(organizationDO);

        // Configure StoreMapstruct.storeDTO2DTO(...).
        final OrganizationDO organizationDO1 = new OrganizationDO();
        organizationDO1.setGuid("df79f94d-8cf0-4aca-862b-cd1e80be2145");
        organizationDO1.setType(0);
        organizationDO1.setName("我的门店");
        organizationDO1.setParentIds("parentIds");
        organizationDO1.setCreateUserGuid("云端同步");
        organizationDO1.setModifiedUserGuid("云端同步");
        organizationDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setIsBuAccounts(0);
        organizationDO1.setIsShowCash(0);
        when(mockStoreMapstruct.storeDTO2DTO(any(StoreDTO.class))).thenReturn(organizationDO1);

        // Configure OrganizationMapstruct.organizationDO2DTO(...).
        final OrganizationDTO organizationDTO1 = new OrganizationDTO();
        organizationDTO1.setGuid("********-74ff-4f0e-ad5e-96d415ca6c7e");
        organizationDTO1.setName("中国");
        organizationDTO1.setParentIds("enterpriseGuid");
        organizationDTO1.setCreateUserGuid("企业初始化");
        organizationDTO1.setModifiedUserGuid("企业初始化");
        organizationDTO1.setChildOrganizationDTOList(Arrays.asList(new OrganizationDTO()));
        final StoreDTO storeDTO1 = new StoreDTO();
        storeDTO1.setGuid("df79f94d-8cf0-4aca-862b-cd1e80be2145");
        storeDTO1.setName("我的门店");
        storeDTO1.setParentIds("parentIds");
        storeDTO1.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO1.setIsEnable(false);
        storeDTO1.setCreateUserGuid("企业初始化");
        storeDTO1.setModifiedUserGuid("企业初始化");
        organizationDTO1.setStoreList(Arrays.asList(storeDTO1));
        final OrganizationDO organizationDO2 = new OrganizationDO();
        organizationDO2.setGuid("df79f94d-8cf0-4aca-862b-cd1e80be2145");
        organizationDO2.setType(0);
        organizationDO2.setName("我的门店");
        organizationDO2.setParentIds("parentIds");
        organizationDO2.setCreateUserGuid("云端同步");
        organizationDO2.setModifiedUserGuid("云端同步");
        organizationDO2.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO2.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO2.setIsBuAccounts(0);
        organizationDO2.setIsShowCash(0);
        when(mockOrganizationMapstruct.organizationDO2DTO(organizationDO2)).thenReturn(organizationDTO1);

        // Run the test
        final OrganizationDTO result = organizationServiceImplUnderTest.createOrganization(organizationDTO);

        // Verify the results
    }

    @Test(expected = BusinessException.class)
    public void testCreateOrganization_OrganizationMapperReturnsNull() {
        // Setup
        final OrganizationDTO organizationDTO = new OrganizationDTO();
        organizationDTO.setGuid("********-74ff-4f0e-ad5e-96d415ca6c7e");
        organizationDTO.setName("中国");
        organizationDTO.setParentIds("enterpriseGuid");
        organizationDTO.setCreateUserGuid("企业初始化");
        organizationDTO.setModifiedUserGuid("企业初始化");
        organizationDTO.setChildOrganizationDTOList(Arrays.asList(new OrganizationDTO()));
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("df79f94d-8cf0-4aca-862b-cd1e80be2145");
        storeDTO.setName("我的门店");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setIsEnable(false);
        storeDTO.setCreateUserGuid("企业初始化");
        storeDTO.setModifiedUserGuid("企业初始化");
        organizationDTO.setStoreList(Arrays.asList(storeDTO));

        when(mockOrganizationMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Run the test
        organizationServiceImplUnderTest.createOrganization(organizationDTO);
    }

    @Test
    public void testCreateBatchOrganization() {
        // Setup
        final HolderOrganizationResultDTO holderOrganizationResultDTO = new HolderOrganizationResultDTO();
        holderOrganizationResultDTO.setId(0L);
        holderOrganizationResultDTO.setParentId(0L);
        holderOrganizationResultDTO.setParentName("parentName");
        holderOrganizationResultDTO.setParentIds("parentIds");
        holderOrganizationResultDTO.setType(0);
        final List<HolderOrganizationResultDTO> holderOrganizationResultList = Arrays.asList(
                holderOrganizationResultDTO);

        // Configure OrganizationMapstruct.holderOrganizationDTOList2DTOList(...).
        final OrganizationDTO organizationDTO = new OrganizationDTO();
        organizationDTO.setGuid("********-74ff-4f0e-ad5e-96d415ca6c7e");
        organizationDTO.setName("中国");
        organizationDTO.setParentIds("enterpriseGuid");
        organizationDTO.setCreateUserGuid("企业初始化");
        organizationDTO.setModifiedUserGuid("企业初始化");
        organizationDTO.setChildOrganizationDTOList(Arrays.asList(new OrganizationDTO()));
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("df79f94d-8cf0-4aca-862b-cd1e80be2145");
        storeDTO.setName("我的门店");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setIsEnable(false);
        storeDTO.setCreateUserGuid("企业初始化");
        storeDTO.setModifiedUserGuid("企业初始化");
        organizationDTO.setStoreList(Arrays.asList(storeDTO));
        final List<OrganizationDTO> organizationDTOS = Arrays.asList(organizationDTO);
        final HolderOrganizationResultDTO holderOrganizationResultDTO1 = new HolderOrganizationResultDTO();
        holderOrganizationResultDTO1.setId(0L);
        holderOrganizationResultDTO1.setParentId(0L);
        holderOrganizationResultDTO1.setParentName("parentName");
        holderOrganizationResultDTO1.setParentIds("parentIds");
        holderOrganizationResultDTO1.setType(0);
        final List<HolderOrganizationResultDTO> holderOrganizationResultDTOList = Arrays.asList(
                holderOrganizationResultDTO1);
        when(mockOrganizationMapstruct.holderOrganizationDTOList2DTOList(holderOrganizationResultDTOList))
                .thenReturn(organizationDTOS);

        // Configure OrganizationMapstruct.organizationDTO2DO(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("df79f94d-8cf0-4aca-862b-cd1e80be2145");
        organizationDO.setType(0);
        organizationDO.setName("我的门店");
        organizationDO.setParentIds("parentIds");
        organizationDO.setCreateUserGuid("云端同步");
        organizationDO.setModifiedUserGuid("云端同步");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        when(mockOrganizationMapstruct.organizationDTO2DO(any(OrganizationDTO.class))).thenReturn(organizationDO);

        doAnswer(invocation -> {
            ((Runnable) invocation.getArguments()[0]).run();
            return null;
        }).when(mockTaskExecutor).execute(any(Runnable.class));

        // Run the test
        organizationServiceImplUnderTest.createBatchOrganization(holderOrganizationResultList);

        // Verify the results
        verify(mockTaskExecutor).execute(any(Runnable.class));
        verify(mockUserClient).syncHolderUser(Arrays.asList("value"));
    }

    @Test
    public void testCreateBatchOrganization_OrganizationMapstructHolderOrganizationDTOList2DTOListReturnsNoItems() {
        // Setup
        final HolderOrganizationResultDTO holderOrganizationResultDTO = new HolderOrganizationResultDTO();
        holderOrganizationResultDTO.setId(0L);
        holderOrganizationResultDTO.setParentId(0L);
        holderOrganizationResultDTO.setParentName("parentName");
        holderOrganizationResultDTO.setParentIds("parentIds");
        holderOrganizationResultDTO.setType(0);
        final List<HolderOrganizationResultDTO> holderOrganizationResultList = Arrays.asList(
                holderOrganizationResultDTO);

        // Configure OrganizationMapstruct.holderOrganizationDTOList2DTOList(...).
        final HolderOrganizationResultDTO holderOrganizationResultDTO1 = new HolderOrganizationResultDTO();
        holderOrganizationResultDTO1.setId(0L);
        holderOrganizationResultDTO1.setParentId(0L);
        holderOrganizationResultDTO1.setParentName("parentName");
        holderOrganizationResultDTO1.setParentIds("parentIds");
        holderOrganizationResultDTO1.setType(0);
        final List<HolderOrganizationResultDTO> holderOrganizationResultDTOList = Arrays.asList(
                holderOrganizationResultDTO1);
        when(mockOrganizationMapstruct.holderOrganizationDTOList2DTOList(holderOrganizationResultDTOList))
                .thenReturn(Collections.emptyList());

        doAnswer(invocation -> {
            ((Runnable) invocation.getArguments()[0]).run();
            return null;
        }).when(mockTaskExecutor).execute(any(Runnable.class));

        // Run the test
        organizationServiceImplUnderTest.createBatchOrganization(holderOrganizationResultList);

        // Verify the results
        verify(mockTaskExecutor).execute(any(Runnable.class));
        verify(mockUserClient).syncHolderUser(Arrays.asList("value"));
    }

    @Test
    public void testUpdateDemotedType() {
        // Setup
        // Run the test
        organizationServiceImplUnderTest.updateDemotedType();

        // Verify the results
    }

    @Test
    public void testRemoveAllOrganization() {
        // Setup
        // Run the test
        organizationServiceImplUnderTest.removeAllOrganization();

        // Verify the results
    }

    @Test
    public void testUpdateOrganization() {
        // Setup
        final OrganizationDTO organizationDTO = new OrganizationDTO();
        organizationDTO.setGuid("********-74ff-4f0e-ad5e-96d415ca6c7e");
        organizationDTO.setName("中国");
        organizationDTO.setParentIds("enterpriseGuid");
        organizationDTO.setCreateUserGuid("企业初始化");
        organizationDTO.setModifiedUserGuid("企业初始化");
        organizationDTO.setChildOrganizationDTOList(Arrays.asList(new OrganizationDTO()));
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("df79f94d-8cf0-4aca-862b-cd1e80be2145");
        storeDTO.setName("我的门店");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setIsEnable(false);
        storeDTO.setCreateUserGuid("企业初始化");
        storeDTO.setModifiedUserGuid("企业初始化");
        organizationDTO.setStoreList(Arrays.asList(storeDTO));

        when(mockOrganizationMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(0);

        // Configure OrganizationMapper.selectList(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("df79f94d-8cf0-4aca-862b-cd1e80be2145");
        organizationDO.setType(0);
        organizationDO.setName("我的门店");
        organizationDO.setParentIds("parentIds");
        organizationDO.setCreateUserGuid("云端同步");
        organizationDO.setModifiedUserGuid("云端同步");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOS = Arrays.asList(organizationDO);
        when(mockOrganizationMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(organizationDOS);

        // Configure StoreMapstruct.storeDTO2DTO(...).
        final OrganizationDO organizationDO1 = new OrganizationDO();
        organizationDO1.setGuid("df79f94d-8cf0-4aca-862b-cd1e80be2145");
        organizationDO1.setType(0);
        organizationDO1.setName("我的门店");
        organizationDO1.setParentIds("parentIds");
        organizationDO1.setCreateUserGuid("云端同步");
        organizationDO1.setModifiedUserGuid("云端同步");
        organizationDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setIsBuAccounts(0);
        organizationDO1.setIsShowCash(0);
        when(mockStoreMapstruct.storeDTO2DTO(any(StoreDTO.class))).thenReturn(organizationDO1);

        // Configure OrganizationMapstruct.organizationDTO2DO(...).
        final OrganizationDO organizationDO2 = new OrganizationDO();
        organizationDO2.setGuid("df79f94d-8cf0-4aca-862b-cd1e80be2145");
        organizationDO2.setType(0);
        organizationDO2.setName("我的门店");
        organizationDO2.setParentIds("parentIds");
        organizationDO2.setCreateUserGuid("云端同步");
        organizationDO2.setModifiedUserGuid("云端同步");
        organizationDO2.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO2.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO2.setIsBuAccounts(0);
        organizationDO2.setIsShowCash(0);
        when(mockOrganizationMapstruct.organizationDTO2DO(any(OrganizationDTO.class))).thenReturn(organizationDO2);

        // Run the test
        final boolean result = organizationServiceImplUnderTest.updateOrganization(organizationDTO);

        // Verify the results
        assertFalse(result);
    }

    @Test(expected = BusinessException.class)
    public void testUpdateOrganization_OrganizationMapperSelectCountReturnsNull() {
        // Setup
        final OrganizationDTO organizationDTO = new OrganizationDTO();
        organizationDTO.setGuid("********-74ff-4f0e-ad5e-96d415ca6c7e");
        organizationDTO.setName("中国");
        organizationDTO.setParentIds("enterpriseGuid");
        organizationDTO.setCreateUserGuid("企业初始化");
        organizationDTO.setModifiedUserGuid("企业初始化");
        organizationDTO.setChildOrganizationDTOList(Arrays.asList(new OrganizationDTO()));
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("df79f94d-8cf0-4aca-862b-cd1e80be2145");
        storeDTO.setName("我的门店");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setIsEnable(false);
        storeDTO.setCreateUserGuid("企业初始化");
        storeDTO.setModifiedUserGuid("企业初始化");
        organizationDTO.setStoreList(Arrays.asList(storeDTO));

        when(mockOrganizationMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Run the test
        organizationServiceImplUnderTest.updateOrganization(organizationDTO);
    }

    @Test
    public void testUpdateOrganization_OrganizationMapperSelectListReturnsNoItems() {
        // Setup
        final OrganizationDTO organizationDTO = new OrganizationDTO();
        organizationDTO.setGuid("********-74ff-4f0e-ad5e-96d415ca6c7e");
        organizationDTO.setName("中国");
        organizationDTO.setParentIds("enterpriseGuid");
        organizationDTO.setCreateUserGuid("企业初始化");
        organizationDTO.setModifiedUserGuid("企业初始化");
        organizationDTO.setChildOrganizationDTOList(Arrays.asList(new OrganizationDTO()));
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("df79f94d-8cf0-4aca-862b-cd1e80be2145");
        storeDTO.setName("我的门店");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setIsEnable(false);
        storeDTO.setCreateUserGuid("企业初始化");
        storeDTO.setModifiedUserGuid("企业初始化");
        organizationDTO.setStoreList(Arrays.asList(storeDTO));

        when(mockOrganizationMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(0);
        when(mockOrganizationMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure StoreMapstruct.storeDTO2DTO(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("df79f94d-8cf0-4aca-862b-cd1e80be2145");
        organizationDO.setType(0);
        organizationDO.setName("我的门店");
        organizationDO.setParentIds("parentIds");
        organizationDO.setCreateUserGuid("云端同步");
        organizationDO.setModifiedUserGuid("云端同步");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        when(mockStoreMapstruct.storeDTO2DTO(any(StoreDTO.class))).thenReturn(organizationDO);

        // Configure OrganizationMapstruct.organizationDTO2DO(...).
        final OrganizationDO organizationDO1 = new OrganizationDO();
        organizationDO1.setGuid("df79f94d-8cf0-4aca-862b-cd1e80be2145");
        organizationDO1.setType(0);
        organizationDO1.setName("我的门店");
        organizationDO1.setParentIds("parentIds");
        organizationDO1.setCreateUserGuid("云端同步");
        organizationDO1.setModifiedUserGuid("云端同步");
        organizationDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setIsBuAccounts(0);
        organizationDO1.setIsShowCash(0);
        when(mockOrganizationMapstruct.organizationDTO2DO(any(OrganizationDTO.class))).thenReturn(organizationDO1);

        // Run the test
        final boolean result = organizationServiceImplUnderTest.updateOrganization(organizationDTO);

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testGetOptionalOrganization() {
        // Setup
        // Configure OrganizationMapstruct.organizationDOList2DTOList(...).
        final OrganizationDTO organizationDTO = new OrganizationDTO();
        organizationDTO.setGuid("********-74ff-4f0e-ad5e-96d415ca6c7e");
        organizationDTO.setName("中国");
        organizationDTO.setParentIds("enterpriseGuid");
        organizationDTO.setCreateUserGuid("企业初始化");
        organizationDTO.setModifiedUserGuid("企业初始化");
        organizationDTO.setChildOrganizationDTOList(Arrays.asList(new OrganizationDTO()));
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("df79f94d-8cf0-4aca-862b-cd1e80be2145");
        storeDTO.setName("我的门店");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setIsEnable(false);
        storeDTO.setCreateUserGuid("企业初始化");
        storeDTO.setModifiedUserGuid("企业初始化");
        organizationDTO.setStoreList(Arrays.asList(storeDTO));
        final List<OrganizationDTO> organizationDTOS = Arrays.asList(organizationDTO);
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("df79f94d-8cf0-4aca-862b-cd1e80be2145");
        organizationDO.setType(0);
        organizationDO.setName("我的门店");
        organizationDO.setParentIds("parentIds");
        organizationDO.setCreateUserGuid("云端同步");
        organizationDO.setModifiedUserGuid("云端同步");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOList = Arrays.asList(organizationDO);
        when(mockOrganizationMapstruct.organizationDOList2DTOList(organizationDOList)).thenReturn(organizationDTOS);

        // Run the test
        final List<OrganizationDTO> result = organizationServiceImplUnderTest.getOptionalOrganization(
                "organizationGuid");

        // Verify the results
    }

    @Test
    public void testGetOptionalOrganization_OrganizationMapstructReturnsNoItems() {
        // Setup
        // Configure OrganizationMapstruct.organizationDOList2DTOList(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("df79f94d-8cf0-4aca-862b-cd1e80be2145");
        organizationDO.setType(0);
        organizationDO.setName("我的门店");
        organizationDO.setParentIds("parentIds");
        organizationDO.setCreateUserGuid("云端同步");
        organizationDO.setModifiedUserGuid("云端同步");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOList = Arrays.asList(organizationDO);
        when(mockOrganizationMapstruct.organizationDOList2DTOList(organizationDOList))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<OrganizationDTO> result = organizationServiceImplUnderTest.getOptionalOrganization(
                "organizationGuid");

        // Verify the results
    }

    @Test
    public void testQueryOrganizationList() {
        // Setup
        // Configure OrganizationMapstruct.organizationDOList2DTOList(...).
        final OrganizationDTO organizationDTO = new OrganizationDTO();
        organizationDTO.setGuid("********-74ff-4f0e-ad5e-96d415ca6c7e");
        organizationDTO.setName("中国");
        organizationDTO.setParentIds("enterpriseGuid");
        organizationDTO.setCreateUserGuid("企业初始化");
        organizationDTO.setModifiedUserGuid("企业初始化");
        organizationDTO.setChildOrganizationDTOList(Arrays.asList(new OrganizationDTO()));
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("df79f94d-8cf0-4aca-862b-cd1e80be2145");
        storeDTO.setName("我的门店");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setIsEnable(false);
        storeDTO.setCreateUserGuid("企业初始化");
        storeDTO.setModifiedUserGuid("企业初始化");
        organizationDTO.setStoreList(Arrays.asList(storeDTO));
        final List<OrganizationDTO> organizationDTOS = Arrays.asList(organizationDTO);
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("df79f94d-8cf0-4aca-862b-cd1e80be2145");
        organizationDO.setType(0);
        organizationDO.setName("我的门店");
        organizationDO.setParentIds("parentIds");
        organizationDO.setCreateUserGuid("云端同步");
        organizationDO.setModifiedUserGuid("云端同步");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOList = Arrays.asList(organizationDO);
        when(mockOrganizationMapstruct.organizationDOList2DTOList(organizationDOList)).thenReturn(organizationDTOS);

        // Run the test
        final List<OrganizationDTO> result = organizationServiceImplUnderTest.queryOrganizationList();

        // Verify the results
    }

    @Test
    public void testQueryOrganizationList_OrganizationMapstructReturnsNoItems() {
        // Setup
        // Configure OrganizationMapstruct.organizationDOList2DTOList(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("df79f94d-8cf0-4aca-862b-cd1e80be2145");
        organizationDO.setType(0);
        organizationDO.setName("我的门店");
        organizationDO.setParentIds("parentIds");
        organizationDO.setCreateUserGuid("云端同步");
        organizationDO.setModifiedUserGuid("云端同步");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOList = Arrays.asList(organizationDO);
        when(mockOrganizationMapstruct.organizationDOList2DTOList(organizationDOList))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<OrganizationDTO> result = organizationServiceImplUnderTest.queryOrganizationList();

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testQueryEnterpriseAndOrganization() {
        // Setup
        // Configure OrganizationMapstruct.organizationDOList2DTOList(...).
        final OrganizationDTO organizationDTO = new OrganizationDTO();
        organizationDTO.setGuid("********-74ff-4f0e-ad5e-96d415ca6c7e");
        organizationDTO.setName("中国");
        organizationDTO.setParentIds("enterpriseGuid");
        organizationDTO.setCreateUserGuid("企业初始化");
        organizationDTO.setModifiedUserGuid("企业初始化");
        organizationDTO.setChildOrganizationDTOList(Arrays.asList(new OrganizationDTO()));
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("df79f94d-8cf0-4aca-862b-cd1e80be2145");
        storeDTO.setName("我的门店");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setIsEnable(false);
        storeDTO.setCreateUserGuid("企业初始化");
        storeDTO.setModifiedUserGuid("企业初始化");
        organizationDTO.setStoreList(Arrays.asList(storeDTO));
        final List<OrganizationDTO> organizationDTOS = Arrays.asList(organizationDTO);
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("df79f94d-8cf0-4aca-862b-cd1e80be2145");
        organizationDO.setType(0);
        organizationDO.setName("我的门店");
        organizationDO.setParentIds("parentIds");
        organizationDO.setCreateUserGuid("云端同步");
        organizationDO.setModifiedUserGuid("云端同步");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOList = Arrays.asList(organizationDO);
        when(mockOrganizationMapstruct.organizationDOList2DTOList(organizationDOList)).thenReturn(organizationDTOS);

        // Run the test
        final List<OrganizationDTO> result = organizationServiceImplUnderTest.queryEnterpriseAndOrganization();

        // Verify the results
    }

    @Test
    public void testQueryEnterpriseAndOrganization_OrganizationMapstructReturnsNoItems() {
        // Setup
        // Configure OrganizationMapstruct.organizationDOList2DTOList(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("df79f94d-8cf0-4aca-862b-cd1e80be2145");
        organizationDO.setType(0);
        organizationDO.setName("我的门店");
        organizationDO.setParentIds("parentIds");
        organizationDO.setCreateUserGuid("云端同步");
        organizationDO.setModifiedUserGuid("云端同步");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOList = Arrays.asList(organizationDO);
        when(mockOrganizationMapstruct.organizationDOList2DTOList(organizationDOList))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<OrganizationDTO> result = organizationServiceImplUnderTest.queryEnterpriseAndOrganization();

        // Verify the results
    }

    @Test(expected = BusinessException.class)
    public void testDeleteOrganization_ThrowsBusinessException() {
        // Setup
        // Configure OrganizationMapper.selectList(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("df79f94d-8cf0-4aca-862b-cd1e80be2145");
        organizationDO.setType(0);
        organizationDO.setName("我的门店");
        organizationDO.setParentIds("parentIds");
        organizationDO.setCreateUserGuid("云端同步");
        organizationDO.setModifiedUserGuid("云端同步");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOS = Arrays.asList(organizationDO);
        when(mockOrganizationMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(organizationDOS);

        // Run the test
        organizationServiceImplUnderTest.deleteOrganization("organizationGuid");
    }

    @Test
    public void testDeleteOrganization_OrganizationMapperReturnsNoItems() {
        // Setup
        when(mockOrganizationMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final boolean result = organizationServiceImplUnderTest.deleteOrganization("organizationGuid");

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testQueryExistOrganizationOrStore() {
        // Setup
        when(mockOrganizationMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(0);

        // Run the test
        final boolean result = organizationServiceImplUnderTest.queryExistOrganizationOrStore("organizationGuid");

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testQueryExistOrganizationOrStore_OrganizationMapperReturnsNull() {
        // Setup
        when(mockOrganizationMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Run the test
        final boolean result = organizationServiceImplUnderTest.queryExistOrganizationOrStore("organizationGuid");

        // Verify the results
        assertTrue(result);
    }

    @Test
    public void testQueryExistAccount() {
        assertFalse(organizationServiceImplUnderTest.queryExistAccount("organizationGuid"));
    }

    @Test
    public void testIsExistOrganization() {
        // Setup
        // Run the test
        final boolean result = organizationServiceImplUnderTest.isExistOrganization();

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testQueryAllOrganization() {
        // Setup
        // Configure OrganizationMapper.selectList(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("df79f94d-8cf0-4aca-862b-cd1e80be2145");
        organizationDO.setType(0);
        organizationDO.setName("我的门店");
        organizationDO.setParentIds("parentIds");
        organizationDO.setCreateUserGuid("云端同步");
        organizationDO.setModifiedUserGuid("云端同步");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOS = Arrays.asList(organizationDO);
        when(mockOrganizationMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(organizationDOS);

        // Run the test
        final List<OrgGeneralDTO> result = organizationServiceImplUnderTest.queryAllOrganization();

        // Verify the results
    }

    @Test
    public void testQueryAllOrganization_OrganizationMapperReturnsNoItems() {
        // Setup
        when(mockOrganizationMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<OrgGeneralDTO> result = organizationServiceImplUnderTest.queryAllOrganization();

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testQueryErpAndOrgAndStore() {
        // Setup
        // Configure OrganizationMapper.selectList(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("df79f94d-8cf0-4aca-862b-cd1e80be2145");
        organizationDO.setType(0);
        organizationDO.setName("我的门店");
        organizationDO.setParentIds("parentIds");
        organizationDO.setCreateUserGuid("云端同步");
        organizationDO.setModifiedUserGuid("云端同步");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOS = Arrays.asList(organizationDO);
        when(mockOrganizationMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(organizationDOS);

        // Run the test
        final List<OrgGeneralDTO> result = organizationServiceImplUnderTest.queryErpAndOrgAndStore(0, 0);

        // Verify the results
    }

    @Test
    public void testQueryErpAndOrgAndStore_OrganizationMapperReturnsNoItems() {
        // Setup
        when(mockOrganizationMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<OrgGeneralDTO> result = organizationServiceImplUnderTest.queryErpAndOrgAndStore(0, 0);

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testQueryOrgByChildIdList() {
        // Setup
        // Configure OrganizationMapper.selectList(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("df79f94d-8cf0-4aca-862b-cd1e80be2145");
        organizationDO.setType(0);
        organizationDO.setName("我的门店");
        organizationDO.setParentIds("parentIds");
        organizationDO.setCreateUserGuid("云端同步");
        organizationDO.setModifiedUserGuid("云端同步");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOS = Arrays.asList(organizationDO);
        when(mockOrganizationMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(organizationDOS);

        // Run the test
        final List<OrgGeneralDTO> result = organizationServiceImplUnderTest.queryOrgByChildIdList(
                Arrays.asList("value"));

        // Verify the results
    }

    @Test
    public void testQueryOrgByChildIdList_OrganizationMapperReturnsNoItems() {
        // Setup
        when(mockOrganizationMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<OrgGeneralDTO> result = organizationServiceImplUnderTest.queryOrgByChildIdList(
                Arrays.asList("value"));

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testQueryOrgParentList() {
        // Setup
        // Configure OrganizationMapper.selectList(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("df79f94d-8cf0-4aca-862b-cd1e80be2145");
        organizationDO.setType(0);
        organizationDO.setName("我的门店");
        organizationDO.setParentIds("parentIds");
        organizationDO.setCreateUserGuid("云端同步");
        organizationDO.setModifiedUserGuid("云端同步");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOS = Arrays.asList(organizationDO);
        when(mockOrganizationMapper.selectList(any(Wrapper.class))).thenReturn(organizationDOS);

        // Configure OrganizationMapstruct.organizationDOList2DTOList(...).
        final OrganizationDTO organizationDTO = new OrganizationDTO();
        organizationDTO.setGuid("********-74ff-4f0e-ad5e-96d415ca6c7e");
        organizationDTO.setName("中国");
        organizationDTO.setParentIds("enterpriseGuid");
        organizationDTO.setCreateUserGuid("企业初始化");
        organizationDTO.setModifiedUserGuid("企业初始化");
        organizationDTO.setChildOrganizationDTOList(Arrays.asList(new OrganizationDTO()));
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("df79f94d-8cf0-4aca-862b-cd1e80be2145");
        storeDTO.setName("我的门店");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setIsEnable(false);
        storeDTO.setCreateUserGuid("企业初始化");
        storeDTO.setModifiedUserGuid("企业初始化");
        organizationDTO.setStoreList(Arrays.asList(storeDTO));
        final List<OrganizationDTO> organizationDTOS = Arrays.asList(organizationDTO);
        final OrganizationDO organizationDO1 = new OrganizationDO();
        organizationDO1.setGuid("df79f94d-8cf0-4aca-862b-cd1e80be2145");
        organizationDO1.setType(0);
        organizationDO1.setName("我的门店");
        organizationDO1.setParentIds("parentIds");
        organizationDO1.setCreateUserGuid("云端同步");
        organizationDO1.setModifiedUserGuid("云端同步");
        organizationDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setIsBuAccounts(0);
        organizationDO1.setIsShowCash(0);
        final List<OrganizationDO> organizationDOList = Arrays.asList(organizationDO1);
        when(mockOrganizationMapstruct.organizationDOList2DTOList(organizationDOList)).thenReturn(organizationDTOS);

        // Run the test
        final Map<String, List<OrganizationDTO>> result = organizationServiceImplUnderTest.queryOrgParentList(
                Arrays.asList("value"));

        // Verify the results
    }

    @Test
    public void testQueryOrgParentList_OrganizationMapperReturnsNoItems() {
        // Setup
        when(mockOrganizationMapper.selectList(any(Wrapper.class))).thenReturn(Collections.emptyList());

        // Configure OrganizationMapstruct.organizationDOList2DTOList(...).
        final OrganizationDTO organizationDTO = new OrganizationDTO();
        organizationDTO.setGuid("********-74ff-4f0e-ad5e-96d415ca6c7e");
        organizationDTO.setName("中国");
        organizationDTO.setParentIds("enterpriseGuid");
        organizationDTO.setCreateUserGuid("企业初始化");
        organizationDTO.setModifiedUserGuid("企业初始化");
        organizationDTO.setChildOrganizationDTOList(Arrays.asList(new OrganizationDTO()));
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("df79f94d-8cf0-4aca-862b-cd1e80be2145");
        storeDTO.setName("我的门店");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setIsEnable(false);
        storeDTO.setCreateUserGuid("企业初始化");
        storeDTO.setModifiedUserGuid("企业初始化");
        organizationDTO.setStoreList(Arrays.asList(storeDTO));
        final List<OrganizationDTO> organizationDTOS = Arrays.asList(organizationDTO);
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("df79f94d-8cf0-4aca-862b-cd1e80be2145");
        organizationDO.setType(0);
        organizationDO.setName("我的门店");
        organizationDO.setParentIds("parentIds");
        organizationDO.setCreateUserGuid("云端同步");
        organizationDO.setModifiedUserGuid("云端同步");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOList = Arrays.asList(organizationDO);
        when(mockOrganizationMapstruct.organizationDOList2DTOList(organizationDOList)).thenReturn(organizationDTOS);

        // Run the test
        final Map<String, List<OrganizationDTO>> result = organizationServiceImplUnderTest.queryOrgParentList(
                Arrays.asList("value"));

        // Verify the results
    }

    @Test
    public void testQueryOrgParentList_OrganizationMapstructReturnsNoItems() {
        // Setup
        // Configure OrganizationMapper.selectList(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("df79f94d-8cf0-4aca-862b-cd1e80be2145");
        organizationDO.setType(0);
        organizationDO.setName("我的门店");
        organizationDO.setParentIds("parentIds");
        organizationDO.setCreateUserGuid("云端同步");
        organizationDO.setModifiedUserGuid("云端同步");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOS = Arrays.asList(organizationDO);
        when(mockOrganizationMapper.selectList(any(Wrapper.class))).thenReturn(organizationDOS);

        // Configure OrganizationMapstruct.organizationDOList2DTOList(...).
        final OrganizationDO organizationDO1 = new OrganizationDO();
        organizationDO1.setGuid("df79f94d-8cf0-4aca-862b-cd1e80be2145");
        organizationDO1.setType(0);
        organizationDO1.setName("我的门店");
        organizationDO1.setParentIds("parentIds");
        organizationDO1.setCreateUserGuid("云端同步");
        organizationDO1.setModifiedUserGuid("云端同步");
        organizationDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setIsBuAccounts(0);
        organizationDO1.setIsShowCash(0);
        final List<OrganizationDO> organizationDOList = Arrays.asList(organizationDO1);
        when(mockOrganizationMapstruct.organizationDOList2DTOList(organizationDOList))
                .thenReturn(Collections.emptyList());

        // Run the test
        final Map<String, List<OrganizationDTO>> result = organizationServiceImplUnderTest.queryOrgParentList(
                Arrays.asList("value"));

        // Verify the results
    }

    @Test
    public void testQueryOrgByIdList() {
        // Setup
        // Configure OrganizationMapper.selectList(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("df79f94d-8cf0-4aca-862b-cd1e80be2145");
        organizationDO.setType(0);
        organizationDO.setName("我的门店");
        organizationDO.setParentIds("parentIds");
        organizationDO.setCreateUserGuid("云端同步");
        organizationDO.setModifiedUserGuid("云端同步");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOS = Arrays.asList(organizationDO);
        when(mockOrganizationMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(organizationDOS);

        // Configure OrganizationMapstruct.organizationDOList2DTOList(...).
        final OrganizationDTO organizationDTO = new OrganizationDTO();
        organizationDTO.setGuid("********-74ff-4f0e-ad5e-96d415ca6c7e");
        organizationDTO.setName("中国");
        organizationDTO.setParentIds("enterpriseGuid");
        organizationDTO.setCreateUserGuid("企业初始化");
        organizationDTO.setModifiedUserGuid("企业初始化");
        organizationDTO.setChildOrganizationDTOList(Arrays.asList(new OrganizationDTO()));
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("df79f94d-8cf0-4aca-862b-cd1e80be2145");
        storeDTO.setName("我的门店");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setIsEnable(false);
        storeDTO.setCreateUserGuid("企业初始化");
        storeDTO.setModifiedUserGuid("企业初始化");
        organizationDTO.setStoreList(Arrays.asList(storeDTO));
        final List<OrganizationDTO> organizationDTOS = Arrays.asList(organizationDTO);
        final OrganizationDO organizationDO1 = new OrganizationDO();
        organizationDO1.setGuid("df79f94d-8cf0-4aca-862b-cd1e80be2145");
        organizationDO1.setType(0);
        organizationDO1.setName("我的门店");
        organizationDO1.setParentIds("parentIds");
        organizationDO1.setCreateUserGuid("云端同步");
        organizationDO1.setModifiedUserGuid("云端同步");
        organizationDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setIsBuAccounts(0);
        organizationDO1.setIsShowCash(0);
        final List<OrganizationDO> organizationDOList = Arrays.asList(organizationDO1);
        when(mockOrganizationMapstruct.organizationDOList2DTOList(organizationDOList)).thenReturn(organizationDTOS);

        // Run the test
        final List<OrganizationDTO> result = organizationServiceImplUnderTest.queryOrgByIdList(Arrays.asList("value"));

        // Verify the results
    }

    @Test
    public void testQueryOrgByIdList_OrganizationMapperReturnsNoItems() {
        // Setup
        when(mockOrganizationMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure OrganizationMapstruct.organizationDOList2DTOList(...).
        final OrganizationDTO organizationDTO = new OrganizationDTO();
        organizationDTO.setGuid("********-74ff-4f0e-ad5e-96d415ca6c7e");
        organizationDTO.setName("中国");
        organizationDTO.setParentIds("enterpriseGuid");
        organizationDTO.setCreateUserGuid("企业初始化");
        organizationDTO.setModifiedUserGuid("企业初始化");
        organizationDTO.setChildOrganizationDTOList(Arrays.asList(new OrganizationDTO()));
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("df79f94d-8cf0-4aca-862b-cd1e80be2145");
        storeDTO.setName("我的门店");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setIsEnable(false);
        storeDTO.setCreateUserGuid("企业初始化");
        storeDTO.setModifiedUserGuid("企业初始化");
        organizationDTO.setStoreList(Arrays.asList(storeDTO));
        final List<OrganizationDTO> organizationDTOS = Arrays.asList(organizationDTO);
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("df79f94d-8cf0-4aca-862b-cd1e80be2145");
        organizationDO.setType(0);
        organizationDO.setName("我的门店");
        organizationDO.setParentIds("parentIds");
        organizationDO.setCreateUserGuid("云端同步");
        organizationDO.setModifiedUserGuid("云端同步");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOList = Arrays.asList(organizationDO);
        when(mockOrganizationMapstruct.organizationDOList2DTOList(organizationDOList)).thenReturn(organizationDTOS);

        // Run the test
        final List<OrganizationDTO> result = organizationServiceImplUnderTest.queryOrgByIdList(Arrays.asList("value"));

        // Verify the results
    }

    @Test
    public void testQueryOrgByIdList_OrganizationMapstructReturnsNoItems() {
        // Setup
        // Configure OrganizationMapper.selectList(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("df79f94d-8cf0-4aca-862b-cd1e80be2145");
        organizationDO.setType(0);
        organizationDO.setName("我的门店");
        organizationDO.setParentIds("parentIds");
        organizationDO.setCreateUserGuid("云端同步");
        organizationDO.setModifiedUserGuid("云端同步");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOS = Arrays.asList(organizationDO);
        when(mockOrganizationMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(organizationDOS);

        // Configure OrganizationMapstruct.organizationDOList2DTOList(...).
        final OrganizationDO organizationDO1 = new OrganizationDO();
        organizationDO1.setGuid("df79f94d-8cf0-4aca-862b-cd1e80be2145");
        organizationDO1.setType(0);
        organizationDO1.setName("我的门店");
        organizationDO1.setParentIds("parentIds");
        organizationDO1.setCreateUserGuid("云端同步");
        organizationDO1.setModifiedUserGuid("云端同步");
        organizationDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setIsBuAccounts(0);
        organizationDO1.setIsShowCash(0);
        final List<OrganizationDO> organizationDOList = Arrays.asList(organizationDO1);
        when(mockOrganizationMapstruct.organizationDOList2DTOList(organizationDOList))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<OrganizationDTO> result = organizationServiceImplUnderTest.queryOrgByIdList(Arrays.asList("value"));

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testQueryAllChildOrg() {
        // Setup
        when(mockOrganizationMapper.queryAllChildOrg(Arrays.asList("value"))).thenReturn(Arrays.asList("value"));

        // Run the test
        final List<String> result = organizationServiceImplUnderTest.queryAllChildOrg(Arrays.asList("value"));

        // Verify the results
        assertEquals(Arrays.asList("value"), result);
    }

    @Test
    public void testQueryAllChildOrg_OrganizationMapperReturnsNoItems() {
        // Setup
        when(mockOrganizationMapper.queryAllChildOrg(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        final List<String> result = organizationServiceImplUnderTest.queryAllChildOrg(Arrays.asList("value"));

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testInitEnterprise() {
        // Setup
        when(mockOrganizationMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(0);
        when(mockDistributedIdService.nextOrganizationGuid()).thenReturn("df79f94d-8cf0-4aca-862b-cd1e80be2145");

        // Configure OrganizationMapstruct.organizationDTO2DO(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("df79f94d-8cf0-4aca-862b-cd1e80be2145");
        organizationDO.setType(0);
        organizationDO.setName("我的门店");
        organizationDO.setParentIds("parentIds");
        organizationDO.setCreateUserGuid("云端同步");
        organizationDO.setModifiedUserGuid("云端同步");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        when(mockOrganizationMapstruct.organizationDTO2DO(any(OrganizationDTO.class))).thenReturn(organizationDO);

        // Configure StoreMapstruct.storeDTO2DTO(...).
        final OrganizationDO organizationDO1 = new OrganizationDO();
        organizationDO1.setGuid("df79f94d-8cf0-4aca-862b-cd1e80be2145");
        organizationDO1.setType(0);
        organizationDO1.setName("我的门店");
        organizationDO1.setParentIds("parentIds");
        organizationDO1.setCreateUserGuid("云端同步");
        organizationDO1.setModifiedUserGuid("云端同步");
        organizationDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setIsBuAccounts(0);
        organizationDO1.setIsShowCash(0);
        when(mockStoreMapstruct.storeDTO2DTO(any(StoreDTO.class))).thenReturn(organizationDO1);

        // Configure OrganizationMapstruct.organizationDO2DTO(...).
        final OrganizationDTO organizationDTO = new OrganizationDTO();
        organizationDTO.setGuid("********-74ff-4f0e-ad5e-96d415ca6c7e");
        organizationDTO.setName("中国");
        organizationDTO.setParentIds("enterpriseGuid");
        organizationDTO.setCreateUserGuid("企业初始化");
        organizationDTO.setModifiedUserGuid("企业初始化");
        organizationDTO.setChildOrganizationDTOList(Arrays.asList(new OrganizationDTO()));
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("df79f94d-8cf0-4aca-862b-cd1e80be2145");
        storeDTO.setName("我的门店");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setIsEnable(false);
        storeDTO.setCreateUserGuid("企业初始化");
        storeDTO.setModifiedUserGuid("企业初始化");
        organizationDTO.setStoreList(Arrays.asList(storeDTO));
        final OrganizationDO organizationDO2 = new OrganizationDO();
        organizationDO2.setGuid("df79f94d-8cf0-4aca-862b-cd1e80be2145");
        organizationDO2.setType(0);
        organizationDO2.setName("我的门店");
        organizationDO2.setParentIds("parentIds");
        organizationDO2.setCreateUserGuid("云端同步");
        organizationDO2.setModifiedUserGuid("云端同步");
        organizationDO2.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO2.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO2.setIsBuAccounts(0);
        organizationDO2.setIsShowCash(0);
        when(mockOrganizationMapstruct.organizationDO2DTO(organizationDO2)).thenReturn(organizationDTO);

        when(mockStoreService.createStore(any(StoreDTO.class))).thenReturn(false);

        // Run the test
        final boolean result = organizationServiceImplUnderTest.initEnterprise("enterpriseGuid");

        // Verify the results
        assertFalse(result);
    }

    @Test(expected = BusinessException.class)
    public void testInitEnterprise_OrganizationMapperReturnsNull() {
        // Setup
        when(mockOrganizationMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Run the test
        organizationServiceImplUnderTest.initEnterprise("enterpriseGuid");
    }

    @Test
    public void testInitEnterprise_StoreServiceReturnsTrue() {
        // Setup
        when(mockOrganizationMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(0);
        when(mockDistributedIdService.nextOrganizationGuid()).thenReturn("df79f94d-8cf0-4aca-862b-cd1e80be2145");

        // Configure OrganizationMapstruct.organizationDTO2DO(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("df79f94d-8cf0-4aca-862b-cd1e80be2145");
        organizationDO.setType(0);
        organizationDO.setName("我的门店");
        organizationDO.setParentIds("parentIds");
        organizationDO.setCreateUserGuid("云端同步");
        organizationDO.setModifiedUserGuid("云端同步");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        when(mockOrganizationMapstruct.organizationDTO2DO(any(OrganizationDTO.class))).thenReturn(organizationDO);

        // Configure StoreMapstruct.storeDTO2DTO(...).
        final OrganizationDO organizationDO1 = new OrganizationDO();
        organizationDO1.setGuid("df79f94d-8cf0-4aca-862b-cd1e80be2145");
        organizationDO1.setType(0);
        organizationDO1.setName("我的门店");
        organizationDO1.setParentIds("parentIds");
        organizationDO1.setCreateUserGuid("云端同步");
        organizationDO1.setModifiedUserGuid("云端同步");
        organizationDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setIsBuAccounts(0);
        organizationDO1.setIsShowCash(0);
        when(mockStoreMapstruct.storeDTO2DTO(any(StoreDTO.class))).thenReturn(organizationDO1);

        // Configure OrganizationMapstruct.organizationDO2DTO(...).
        final OrganizationDTO organizationDTO = new OrganizationDTO();
        organizationDTO.setGuid("********-74ff-4f0e-ad5e-96d415ca6c7e");
        organizationDTO.setName("中国");
        organizationDTO.setParentIds("enterpriseGuid");
        organizationDTO.setCreateUserGuid("企业初始化");
        organizationDTO.setModifiedUserGuid("企业初始化");
        organizationDTO.setChildOrganizationDTOList(Arrays.asList(new OrganizationDTO()));
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("df79f94d-8cf0-4aca-862b-cd1e80be2145");
        storeDTO.setName("我的门店");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setIsEnable(false);
        storeDTO.setCreateUserGuid("企业初始化");
        storeDTO.setModifiedUserGuid("企业初始化");
        organizationDTO.setStoreList(Arrays.asList(storeDTO));
        final OrganizationDO organizationDO2 = new OrganizationDO();
        organizationDO2.setGuid("df79f94d-8cf0-4aca-862b-cd1e80be2145");
        organizationDO2.setType(0);
        organizationDO2.setName("我的门店");
        organizationDO2.setParentIds("parentIds");
        organizationDO2.setCreateUserGuid("云端同步");
        organizationDO2.setModifiedUserGuid("云端同步");
        organizationDO2.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO2.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO2.setIsBuAccounts(0);
        organizationDO2.setIsShowCash(0);
        when(mockOrganizationMapstruct.organizationDO2DTO(organizationDO2)).thenReturn(organizationDTO);

        when(mockStoreService.createStore(any(StoreDTO.class))).thenReturn(true);

        // Run the test
        final boolean result = organizationServiceImplUnderTest.initEnterprise("enterpriseGuid");

        // Verify the results
        assertTrue(result);
    }

    @Test
    public void testQueryAllOrgGuids() {
        assertEquals(Arrays.asList("value"), organizationServiceImplUnderTest.queryAllOrgGuids());
    }
}
