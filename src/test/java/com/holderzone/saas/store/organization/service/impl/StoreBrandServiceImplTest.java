package com.holderzone.saas.store.organization.service.impl;

import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.organization.service.BrandService;
import com.holderzone.saas.store.organization.service.DistributedIdService;
import com.holderzone.saas.store.organization.service.impl.StoreBrandServiceImpl;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class StoreBrandServiceImplTest {

    @Mock
    private BrandService mockBrandService;
    @Mock
    private DistributedIdService mockDistributedIdService;

    private StoreBrandServiceImpl storeBrandServiceImplUnderTest;

    @Before
    public void setUp() {
        storeBrandServiceImplUnderTest = new StoreBrandServiceImpl(mockBrandService, mockDistributedIdService);
    }

    @Test
    public void testCreateStoreBrand() {
        // Setup
        when(mockBrandService.queryDefaultBrand()).thenReturn(new BrandDTO());
        when(mockDistributedIdService.nextStoreBrandGuid()).thenReturn("4e341411-b28b-44b5-b868-8782c5a82aec");

        // Run the test
        final String result = storeBrandServiceImplUnderTest.createStoreBrand("storeGuid", "brandGuid",
                "createUserGuid", "modifiedUserGuid");

        // Verify the results
        assertEquals("brandGuid", result);
    }
}
