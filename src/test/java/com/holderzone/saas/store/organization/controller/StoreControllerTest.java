package com.holderzone.saas.store.organization.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.order.BusinessDayDTO;
import com.holderzone.saas.store.dto.organization.*;
import com.holderzone.saas.store.dto.store.store.BindupAccountsSaveDTO;
import com.holderzone.saas.store.dto.store.table.TableStatusDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxOrderConfigDTO;
import com.holderzone.saas.store.organization.controller.StoreController;
import com.holderzone.saas.store.organization.service.StoreService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

@RunWith(SpringRunner.class)
@WebMvcTest(StoreController.class)
public class StoreControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private StoreService mockStoreService;

    @Test
    public void testCreateStore() throws Exception {
        // Setup
        when(mockStoreService.createStore(any(StoreDTO.class))).thenReturn(false);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/store/create")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testCreateStore_StoreServiceReturnsTrue() throws Exception {
        // Setup
        when(mockStoreService.createStore(any(StoreDTO.class))).thenReturn(true);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/store/create")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testCreateStoreByMdm() throws Exception {
        // Setup
        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/store/create_by_mdm")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
        verify(mockStoreService).createStoreByMdm(any(StoreDTO.class));
    }

    @Test
    public void testUpdateStore() throws Exception {
        // Setup
        when(mockStoreService.updateStore(any(StoreDTO.class), eq(false))).thenReturn(false);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/store/update")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testUpdateStore_StoreServiceReturnsTrue() throws Exception {
        // Setup
        when(mockStoreService.updateStore(any(StoreDTO.class), eq(false))).thenReturn(true);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/store/update")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testItemUploadUpdate() throws Exception {
        // Setup
        // Configure StoreService.itemUploadUpdate(...).
        final ItemUploadUpdateReq req = new ItemUploadUpdateReq();
        req.setStoreGuid("storeGuid");
        req.setIsItemUpload(0);
        when(mockStoreService.itemUploadUpdate(req)).thenReturn(false);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/store/itemUploadUpdate")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testItemUploadUpdate_StoreServiceReturnsTrue() throws Exception {
        // Setup
        // Configure StoreService.itemUploadUpdate(...).
        final ItemUploadUpdateReq req = new ItemUploadUpdateReq();
        req.setStoreGuid("storeGuid");
        req.setIsItemUpload(0);
        when(mockStoreService.itemUploadUpdate(req)).thenReturn(true);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/store/itemUploadUpdate")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testUpdateStoreByMdm() throws Exception {
        // Setup
        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/store/update_by_mdm")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
        verify(mockStoreService).updateStoreByMdm(any(StoreDTO.class));
    }

    @Test
    public void testUpdateStoreByAccountAndShowCard() throws Exception {
        // Setup
        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/store/update_by_accountcard")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());

        // Confirm StoreService.updateBuAccounts(...).
        final BindupAccountsSaveDTO bindupAccountsSaveDTO = new BindupAccountsSaveDTO();
        bindupAccountsSaveDTO.setIsBuAccounts(0);
        bindupAccountsSaveDTO.setIsShowCash(0);
        bindupAccountsSaveDTO.setIsMultiHandover(0);
        bindupAccountsSaveDTO.setBrandGuid("brandGuid");
        verify(mockStoreService).updateBuAccounts(bindupAccountsSaveDTO);
    }

    @Test
    public void testEnableStore() throws Exception {
        // Setup
        when(mockStoreService.enableStore("storeGuid")).thenReturn(false);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/store/enable")
                        .param("storeGuid", "storeGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testEnableStore_StoreServiceReturnsTrue() throws Exception {
        // Setup
        when(mockStoreService.enableStore("storeGuid")).thenReturn(true);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/store/enable")
                        .param("storeGuid", "storeGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testDeleteStore() throws Exception {
        // Setup
        when(mockStoreService.deleteStore("storeGuid")).thenReturn(false);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/store/delete")
                        .param("storeGuid", "storeGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testDeleteStore_StoreServiceReturnsTrue() throws Exception {
        // Setup
        when(mockStoreService.deleteStore("storeGuid")).thenReturn(true);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/store/delete")
                        .param("storeGuid", "storeGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testQueryByCondition() throws Exception {
        // Setup
        // Configure StoreService.queryByCondition(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("e8cef7c3-1c41-4443-9cc9-0863a176efe1");
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setCanOpenTable(0);
        storeDTO.setIsMultiHandover(0);
        final Page<StoreDTO> storeDTOPage = new Page<>(0L, 0L, Arrays.asList(storeDTO));
        final QueryStoreDTO queryStoreDTO = new QueryStoreDTO();
        queryStoreDTO.setIsEnable(0);
        queryStoreDTO.setStoreName("storeName");
        when(mockStoreService.queryByCondition(queryStoreDTO)).thenReturn(storeDTOPage);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/store/query_by_condition")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testListStoreByCondition() throws Exception {
        // Setup
        // Configure StoreService.listStoreByCondition(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("e8cef7c3-1c41-4443-9cc9-0863a176efe1");
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setCanOpenTable(0);
        storeDTO.setIsMultiHandover(0);
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO);
        final QueryStoreDTO queryStoreDTO = new QueryStoreDTO();
        queryStoreDTO.setIsEnable(0);
        queryStoreDTO.setStoreName("storeName");
        when(mockStoreService.listStoreByCondition(queryStoreDTO)).thenReturn(storeDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/store/list_store_by_condition")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testListStoreByCondition_StoreServiceReturnsNoItems() throws Exception {
        // Setup
        // Configure StoreService.listStoreByCondition(...).
        final QueryStoreDTO queryStoreDTO = new QueryStoreDTO();
        queryStoreDTO.setIsEnable(0);
        queryStoreDTO.setStoreName("storeName");
        when(mockStoreService.listStoreByCondition(queryStoreDTO)).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/store/list_store_by_condition")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("[]", response.getContentAsString());
    }

    @Test
    public void testQueryByConditionNoPage() throws Exception {
        // Setup
        // Configure StoreService.queryByConditionNoPage(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("e8cef7c3-1c41-4443-9cc9-0863a176efe1");
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setCanOpenTable(0);
        storeDTO.setIsMultiHandover(0);
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO);
        final StoreParserDTO storeParserDTO = new StoreParserDTO();
        storeParserDTO.setAllStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setRegionCodeList(Arrays.asList("value"));
        storeParserDTO.setBrandGuidList(Arrays.asList("value"));
        storeParserDTO.setOrganizationGuidList(Arrays.asList("value"));
        when(mockStoreService.queryByConditionNoPage(storeParserDTO)).thenReturn(storeDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/store/query_by_condition_no_page")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testQueryByConditionNoPage_StoreServiceReturnsNoItems() throws Exception {
        // Setup
        // Configure StoreService.queryByConditionNoPage(...).
        final StoreParserDTO storeParserDTO = new StoreParserDTO();
        storeParserDTO.setAllStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setRegionCodeList(Arrays.asList("value"));
        storeParserDTO.setBrandGuidList(Arrays.asList("value"));
        storeParserDTO.setOrganizationGuidList(Arrays.asList("value"));
        when(mockStoreService.queryByConditionNoPage(storeParserDTO)).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/store/query_by_condition_no_page")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("[]", response.getContentAsString());
    }

    @Test
    public void testParseByCondition() throws Exception {
        // Setup
        // Configure StoreService.parseByCondition(...).
        final StoreParserDTO storeParserDTO = new StoreParserDTO();
        storeParserDTO.setAllStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setRegionCodeList(Arrays.asList("value"));
        storeParserDTO.setBrandGuidList(Arrays.asList("value"));
        storeParserDTO.setOrganizationGuidList(Arrays.asList("value"));
        when(mockStoreService.parseByCondition(storeParserDTO)).thenReturn(Arrays.asList("value"));

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/store/parse_by_condition")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testParseByCondition_StoreServiceReturnsNoItems() throws Exception {
        // Setup
        // Configure StoreService.parseByCondition(...).
        final StoreParserDTO storeParserDTO = new StoreParserDTO();
        storeParserDTO.setAllStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setRegionCodeList(Arrays.asList("value"));
        storeParserDTO.setBrandGuidList(Arrays.asList("value"));
        storeParserDTO.setOrganizationGuidList(Arrays.asList("value"));
        when(mockStoreService.parseByCondition(storeParserDTO)).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/store/parse_by_condition")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("[]", response.getContentAsString());
    }

    @Test
    public void testParseByConditionNotUnion() throws Exception {
        // Setup
        // Configure StoreService.parseByConditionNotUnion(...).
        final StoreParserDTO storeParserDTO = new StoreParserDTO();
        storeParserDTO.setAllStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setRegionCodeList(Arrays.asList("value"));
        storeParserDTO.setBrandGuidList(Arrays.asList("value"));
        storeParserDTO.setOrganizationGuidList(Arrays.asList("value"));
        when(mockStoreService.parseByConditionNotUnion(storeParserDTO)).thenReturn(Arrays.asList("value"));

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/store/parse_by_condition_not_union")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testParseByConditionNotUnion_StoreServiceReturnsNoItems() throws Exception {
        // Setup
        // Configure StoreService.parseByConditionNotUnion(...).
        final StoreParserDTO storeParserDTO = new StoreParserDTO();
        storeParserDTO.setAllStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setRegionCodeList(Arrays.asList("value"));
        storeParserDTO.setBrandGuidList(Arrays.asList("value"));
        storeParserDTO.setOrganizationGuidList(Arrays.asList("value"));
        when(mockStoreService.parseByConditionNotUnion(storeParserDTO)).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/store/parse_by_condition_not_union")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("[]", response.getContentAsString());
    }

    @Test
    public void testQueryStoreByGuid() throws Exception {
        // Setup
        // Configure StoreService.queryStoreByGuid(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("e8cef7c3-1c41-4443-9cc9-0863a176efe1");
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setCanOpenTable(0);
        storeDTO.setIsMultiHandover(0);
        when(mockStoreService.queryStoreByGuid("storeGuid")).thenReturn(storeDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/store/query_store_by_guid")
                        .param("storeGuid", "storeGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testQueryStoreBaseByGuid() throws Exception {
        // Setup
        // Configure StoreService.queryStoreBaseByGuid(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("e8cef7c3-1c41-4443-9cc9-0863a176efe1");
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setCanOpenTable(0);
        storeDTO.setIsMultiHandover(0);
        when(mockStoreService.queryStoreBaseByGuid("storeGuid")).thenReturn(storeDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/store/query_store_base_by_guid")
                        .param("storeGuid", "storeGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testQueryStoreBrandDetail() throws Exception {
        // Setup
        // Configure StoreService.queryStoreBrandDetail(...).
        final BrandStoreDetailDTO brandStoreDetailDTO = new BrandStoreDetailDTO();
        brandStoreDetailDTO.setStoreGuid("storeGuid");
        brandStoreDetailDTO.setBrandGuid("brandGuid");
        brandStoreDetailDTO.setStoreName("storeName");
        brandStoreDetailDTO.setBrandName("brandName");
        brandStoreDetailDTO.setBrandLogoUrl("brandLogoUrl");
        when(mockStoreService.queryStoreBrandDetail("storeGuid", "brandGuid")).thenReturn(brandStoreDetailDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/store/query_store_brand_by_guid")
                        .param("storeGuid", "storeGuid")
                        .param("brandGuid", "brandGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testQueryStoreBizByGuid() throws Exception {
        // Setup
        // Configure StoreService.queryStoreBizByGuid(...).
        final StoreBizDTO storeBizDTO = new StoreBizDTO();
        storeBizDTO.setStoreGuid("storeGuid");
        storeBizDTO.setAddressDetail("addressDetail");
        storeBizDTO.setContactTel("contactTel");
        storeBizDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeBizDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        when(mockStoreService.queryStoreBizByGuid("storeGuid")).thenReturn(storeBizDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/store/query_store_biz_by_guid")
                        .param("storeGuid", "storeGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testQueryStoreByCode() throws Exception {
        // Setup
        // Configure StoreService.queryStoreByCode(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("e8cef7c3-1c41-4443-9cc9-0863a176efe1");
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setCanOpenTable(0);
        storeDTO.setIsMultiHandover(0);
        when(mockStoreService.queryStoreByCode("storeCode")).thenReturn(storeDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/store/query_store_by_code")
                        .param("storeCode", "storeCode")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testQueryStoreByRegionList() throws Exception {
        // Setup
        // Configure StoreService.queryStoreByRegionList(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("e8cef7c3-1c41-4443-9cc9-0863a176efe1");
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setCanOpenTable(0);
        storeDTO.setIsMultiHandover(0);
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO);
        when(mockStoreService.queryStoreByRegionList(
                Arrays.asList(new RegionDTO("adcode", "name", false, Arrays.asList())))).thenReturn(storeDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/store/query_store_by_regionlist")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testQueryStoreByRegionList_StoreServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockStoreService.queryStoreByRegionList(
                Arrays.asList(new RegionDTO("adcode", "name", false, Arrays.asList()))))
                .thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/store/query_store_by_regionlist")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("[]", response.getContentAsString());
    }

    @Test
    public void testQueryStoreByBrandList() throws Exception {
        // Setup
        // Configure StoreService.queryStoreByBrandList(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("e8cef7c3-1c41-4443-9cc9-0863a176efe1");
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setCanOpenTable(0);
        storeDTO.setIsMultiHandover(0);
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO);
        when(mockStoreService.queryStoreByBrandList(Arrays.asList("value"))).thenReturn(storeDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/store/query_store_by_brandlist")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testQueryStoreByBrandList_StoreServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockStoreService.queryStoreByBrandList(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/store/query_store_by_brandlist")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("[]", response.getContentAsString());
    }

    @Test
    public void testQueryDeleteCondition() throws Exception {
        // Setup
        when(mockStoreService.queryDeleteCondition("storeGuid")).thenReturn(false);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/store/query_delete_condition")
                        .param("storeGuid", "storeGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testQueryDeleteCondition_StoreServiceReturnsTrue() throws Exception {
        // Setup
        when(mockStoreService.queryDeleteCondition("storeGuid")).thenReturn(true);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/store/query_delete_condition")
                        .param("storeGuid", "storeGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testQueryBrandByStoreGuid() throws Exception {
        // Setup
        // Configure StoreService.queryBrandByStoreGuid(...).
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("dcbcb51a-ed19-411d-9064-93963eb1b034");
        brandDTO.setUuid("ca0061b2-3370-4bff-8812-fecd972e1d05");
        brandDTO.setName("name");
        brandDTO.setDescription("description");
        brandDTO.setLogoUrl("logoUrl");
        when(mockStoreService.queryBrandByStoreGuid("storeGuid")).thenReturn(brandDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/store/query_brand_by_storeguid")
                        .param("storeGuid", "storeGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testQueryBrandListByStoreGuidList() throws Exception {
        // Setup
        // Configure StoreService.queryBrandListByStoreGuidList(...).
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("dcbcb51a-ed19-411d-9064-93963eb1b034");
        brandDTO.setUuid("ca0061b2-3370-4bff-8812-fecd972e1d05");
        brandDTO.setName("name");
        brandDTO.setDescription("description");
        brandDTO.setLogoUrl("logoUrl");
        final List<BrandDTO> brandDTOS = Arrays.asList(brandDTO);
        when(mockStoreService.queryBrandListByStoreGuidList(Arrays.asList("value"))).thenReturn(brandDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/store/query_brand_by_store_guid_list")
                        .param("storeGuidList", "storeGuidList")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testQueryBrandListByStoreGuidList_StoreServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockStoreService.queryBrandListByStoreGuidList(Arrays.asList("value")))
                .thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/store/query_brand_by_store_guid_list")
                        .param("storeGuidList", "storeGuidList")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("[]", response.getContentAsString());
    }

    @Test
    public void testQueryBrandByStoreGuidForMember() throws Exception {
        // Setup
        // Configure StoreService.queryBrandByStoreGuidForMember(...).
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("dcbcb51a-ed19-411d-9064-93963eb1b034");
        brandDTO.setUuid("ca0061b2-3370-4bff-8812-fecd972e1d05");
        brandDTO.setName("name");
        brandDTO.setDescription("description");
        brandDTO.setLogoUrl("logoUrl");
        when(mockStoreService.queryBrandByStoreGuidForMember("storeGuid")).thenReturn(brandDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/store/query_brand_by_storeguid_for_member")
                        .param("storeGuid", "storeGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testQueryStoreByIdList1() throws Exception {
        // Setup
        // Configure StoreService.queryStoreByIdList(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("e8cef7c3-1c41-4443-9cc9-0863a176efe1");
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setCanOpenTable(0);
        storeDTO.setIsMultiHandover(0);
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO);
        when(mockStoreService.queryStoreByIdList(Arrays.asList("value"))).thenReturn(storeDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/store/query_store_by_idlist")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testQueryStoreByIdList1_StoreServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockStoreService.queryStoreByIdList(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/store/query_store_by_idlist")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("[]", response.getContentAsString());
    }

    @Test
    public void testQueryStoreByIdList2() throws Exception {
        // Setup
        // Configure StoreService.queryStoreByIdListAndBrandId(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("e8cef7c3-1c41-4443-9cc9-0863a176efe1");
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setCanOpenTable(0);
        storeDTO.setIsMultiHandover(0);
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO);
        when(mockStoreService.queryStoreByIdListAndBrandId(
                new SingleDataDTO("data", Arrays.asList("value")))).thenReturn(storeDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/store/query_store_by_idlist_and_brand")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testQueryStoreByIdList2_StoreServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockStoreService.queryStoreByIdListAndBrandId(
                new SingleDataDTO("data", Arrays.asList("value")))).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/store/query_store_by_idlist_and_brand")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("[]", response.getContentAsString());
    }

    @Test
    public void testQueryStoreDetailByIdList() throws Exception {
        // Setup
        // Configure StoreService.queryStoreDetailByIdList(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("e8cef7c3-1c41-4443-9cc9-0863a176efe1");
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setCanOpenTable(0);
        storeDTO.setIsMultiHandover(0);
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO);
        when(mockStoreService.queryStoreDetailByIdList(Arrays.asList("value"))).thenReturn(storeDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/store/query_store_detail_by_idlist")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testQueryStoreDetailByIdList_StoreServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockStoreService.queryStoreDetailByIdList(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/store/query_store_detail_by_idlist")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("[]", response.getContentAsString());
    }

    @Test
    public void testQueryStoreAndBrandByIdList() throws Exception {
        // Setup
        // Configure StoreService.queryStoreAndBrandByIdList(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("e8cef7c3-1c41-4443-9cc9-0863a176efe1");
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setCanOpenTable(0);
        storeDTO.setIsMultiHandover(0);
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO);
        when(mockStoreService.queryStoreAndBrandByIdList(Arrays.asList("value"))).thenReturn(storeDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/store/query_store_and_brand_by_id_list")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testQueryStoreAndBrandByIdList_StoreServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockStoreService.queryStoreAndBrandByIdList(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/store/query_store_and_brand_by_id_list")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("[]", response.getContentAsString());
    }

    @Test
    public void testQueryAllStore() throws Exception {
        // Setup
        // Configure StoreService.queryAllStore(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("e8cef7c3-1c41-4443-9cc9-0863a176efe1");
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setCanOpenTable(0);
        storeDTO.setIsMultiHandover(0);
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO);
        when(mockStoreService.queryAllStore()).thenReturn(storeDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/store/query_all_store")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testQueryAllStore_StoreServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockStoreService.queryAllStore()).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/store/query_all_store")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("[]", response.getContentAsString());
    }

    @Test
    public void testQueryAllStoreGuid() throws Exception {
        // Setup
        when(mockStoreService.queryAllStoreGuid()).thenReturn(Arrays.asList("value"));

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/store/query_all_store_guid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testQueryAllStoreGuid_StoreServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockStoreService.queryAllStoreGuid()).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/store/query_all_store_guid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("[]", response.getContentAsString());
    }

    @Test
    public void testList() throws Exception {
        // Setup
        // Configure StoreService.list(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("e8cef7c3-1c41-4443-9cc9-0863a176efe1");
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setCanOpenTable(0);
        storeDTO.setIsMultiHandover(0);
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO);
        final StoreListReq dto = new StoreListReq();
        dto.setStoreGuidList(Arrays.asList("value"));
        dto.setRegionCodeList(Arrays.asList("value"));
        dto.setBrandGuidList(Arrays.asList("value"));
        when(mockStoreService.list(dto)).thenReturn(storeDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/store/list")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testList_StoreServiceReturnsNoItems() throws Exception {
        // Setup
        // Configure StoreService.list(...).
        final StoreListReq dto = new StoreListReq();
        dto.setStoreGuidList(Arrays.asList("value"));
        dto.setRegionCodeList(Arrays.asList("value"));
        dto.setBrandGuidList(Arrays.asList("value"));
        when(mockStoreService.list(dto)).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/store/list")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("[]", response.getContentAsString());
    }

    @Test
    public void testQueryStoreByCondition() throws Exception {
        // Setup
        // Configure StoreService.queryStoreByCondition(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("e8cef7c3-1c41-4443-9cc9-0863a176efe1");
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setCanOpenTable(0);
        storeDTO.setIsMultiHandover(0);
        final Page<StoreDTO> storeDTOPage = new Page<>(0L, 0L, Arrays.asList(storeDTO));
        final QueryStoreDTO queryStoreDTO = new QueryStoreDTO();
        queryStoreDTO.setIsEnable(0);
        queryStoreDTO.setStoreName("storeName");
        when(mockStoreService.queryStoreByCondition(queryStoreDTO)).thenReturn(storeDTOPage);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/store/query_store_by_condition")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testQueryStoreByCityAndBrand() throws Exception {
        // Setup
        // Configure StoreService.queryStoreByCityAndBrand(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("e8cef7c3-1c41-4443-9cc9-0863a176efe1");
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setCanOpenTable(0);
        storeDTO.setIsMultiHandover(0);
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO);
        when(mockStoreService.queryStoreByCityAndBrand(any(StoreDTO.class))).thenReturn(storeDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/store/query_store_by_city_and_brand")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testQueryStoreByCityAndBrand_StoreServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockStoreService.queryStoreByCityAndBrand(any(StoreDTO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/store/query_store_by_city_and_brand")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("[]", response.getContentAsString());
    }

    @Test
    public void testQueryBusinessDay() throws Exception {
        // Setup
        // Configure StoreService.queryBusinessDay(...).
        final BusinessDateReqDTO businessDateReqDTO = new BusinessDateReqDTO();
        businessDateReqDTO.setQueryDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        businessDateReqDTO.setStoreGuidList(Arrays.asList("value"));
        businessDateReqDTO.setStoreGuid("storeGuid");
        when(mockStoreService.queryBusinessDay(businessDateReqDTO)).thenReturn(LocalDate.of(2020, 1, 1));

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/store/query_business_day")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testQueryBusinessDayInfo() throws Exception {
        // Setup
        // Configure StoreService.queryBusinessDayInfo(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("e8cef7c3-1c41-4443-9cc9-0863a176efe1");
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setCanOpenTable(0);
        storeDTO.setIsMultiHandover(0);
        final BusinessDateReqDTO businessDateReqDTO = new BusinessDateReqDTO();
        businessDateReqDTO.setQueryDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        businessDateReqDTO.setStoreGuidList(Arrays.asList("value"));
        businessDateReqDTO.setStoreGuid("storeGuid");
        when(mockStoreService.queryBusinessDayInfo(businessDateReqDTO)).thenReturn(storeDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/store/query_business_day_info")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testGetAliveStoreCount() throws Exception {
        // Setup
        when(mockStoreService.count(any(LambdaQueryWrapper.class))).thenReturn(0);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/store/get_alive_store_count")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testQueryBusinessDate() throws Exception {
        // Setup
        // Configure StoreService.queryBusinessDate(...).
        final StoreBusinessDateDTO storeBusinessDateDTO = new StoreBusinessDateDTO(
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockStoreService.queryBusinessDate("storeGuid")).thenReturn(storeBusinessDateDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/store/query_business_date")
                        .param("storeGuid", "storeGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testQueryStoreByNameList() throws Exception {
        // Setup
        // Configure StoreService.queryStoreByNameList(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("e8cef7c3-1c41-4443-9cc9-0863a176efe1");
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setCanOpenTable(0);
        storeDTO.setIsMultiHandover(0);
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO);
        when(mockStoreService.queryStoreByNameList(Arrays.asList("value"))).thenReturn(storeDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/store/query_store_by_name_list")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testQueryStoreByNameList_StoreServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockStoreService.queryStoreByNameList(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/store/query_store_by_name_list")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("[]", response.getContentAsString());
    }

    @Test
    public void testGetPadStartOrderInfo() throws Exception {
        // Setup
        // Configure StoreService.getPadStartOrderInfo(...).
        final WxOrderConfigDTO wxOrderConfigDTO = new WxOrderConfigDTO();
        wxOrderConfigDTO.setGuid("d9c04fdf-62aa-4d2f-8246-8dea91734769");
        wxOrderConfigDTO.setIsOrderOpen(0);
        wxOrderConfigDTO.setOrderModel(0);
        wxOrderConfigDTO.setTakingModel(0);
        wxOrderConfigDTO.setIsOnlinePayed(0);
        final PadStartOrderRespDTO padStartOrderRespDTO = new PadStartOrderRespDTO("storeGuid", "storeName",
                "brandLogoUrl", wxOrderConfigDTO, false);
        when(mockStoreService.getPadStartOrderInfo("storeGuid")).thenReturn(padStartOrderRespDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/store/get_pad_start_order_info")
                        .param("storeGuid", "storeGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testBatchUpdateBuAccounts() throws Exception {
        // Setup
        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/store/bindupAccountupdate")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
        verify(mockStoreService).batchUpdateBuAccounts(eq(Arrays.asList("value")), any(StoreDTO.class));
    }

    @Test
    public void testAutoBindupAccounts() throws Exception {
        // Setup
        when(mockStoreService.checkBindUpAccount("storeGuid", "userGuid", "userName", Arrays.asList(
                new TableStatusDTO(LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, LocalDate.of(2020, 1, 1)))))
                .thenReturn(new HashMap<>());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/store/auto/storeTable")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testQueryStoreBusinessDay() throws Exception {
        // Setup
        // Configure StoreService.queryStoreBusinessDay(...).
        final BusinessDayDTO businessDayDTO = new BusinessDayDTO();
        businessDayDTO.setGuid("19855c62-ab5f-4f82-9dfb-7855bf207c57");
        businessDayDTO.setStoreGuid("storeGuid");
        businessDayDTO.setEnterpriseGuid("enterpriseGuid");
        businessDayDTO.setOrderNo("orderNo");
        businessDayDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        final List<BusinessDayDTO> businessDayDTOS = Arrays.asList(businessDayDTO);
        final BusinessDayDTO businessDayDTO1 = new BusinessDayDTO();
        businessDayDTO1.setGuid("19855c62-ab5f-4f82-9dfb-7855bf207c57");
        businessDayDTO1.setStoreGuid("storeGuid");
        businessDayDTO1.setEnterpriseGuid("enterpriseGuid");
        businessDayDTO1.setOrderNo("orderNo");
        businessDayDTO1.setBusinessStart(LocalTime.of(0, 0, 0));
        final List<BusinessDayDTO> businessDayDTOList = Arrays.asList(businessDayDTO1);
        when(mockStoreService.queryStoreBusinessDay(businessDayDTOList)).thenReturn(businessDayDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/store/query_store_business_day")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testQueryStoreBusinessDay_StoreServiceReturnsNoItems() throws Exception {
        // Setup
        // Configure StoreService.queryStoreBusinessDay(...).
        final BusinessDayDTO businessDayDTO = new BusinessDayDTO();
        businessDayDTO.setGuid("19855c62-ab5f-4f82-9dfb-7855bf207c57");
        businessDayDTO.setStoreGuid("storeGuid");
        businessDayDTO.setEnterpriseGuid("enterpriseGuid");
        businessDayDTO.setOrderNo("orderNo");
        businessDayDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        final List<BusinessDayDTO> businessDayDTOList = Arrays.asList(businessDayDTO);
        when(mockStoreService.queryStoreBusinessDay(businessDayDTOList)).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/store/query_store_business_day")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("[]", response.getContentAsString());
    }
}
