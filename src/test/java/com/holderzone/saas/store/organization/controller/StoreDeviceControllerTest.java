package com.holderzone.saas.store.organization.controller;

import com.holderzone.saas.store.dto.organization.PadOrderTypeReqDTO;
import com.holderzone.saas.store.dto.organization.PadOrderTypeRespDTO;
import com.holderzone.saas.store.dto.table.PadAreaDTO;
import com.holderzone.saas.store.dto.table.TableInfoDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceQueryDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceSortDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceUnbindDTO;
import com.holderzone.saas.store.organization.controller.StoreDeviceController;
import com.holderzone.saas.store.organization.service.StoreDeviceService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

@RunWith(SpringRunner.class)
@WebMvcTest(StoreDeviceController.class)
public class StoreDeviceControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private StoreDeviceService mockStoreDeviceService;

    @Test
    public void testCreate() throws Exception {
        // Setup
        // Configure StoreDeviceService.create(...).
        final StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO();
        storeDeviceDTO.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO.setStoreNo("storeNo");
        storeDeviceDTO.setStoreGuid("storeGuid");
        storeDeviceDTO.setStoreName("storeName");
        storeDeviceDTO.setDeviceNo("deviceNo");
        when(mockStoreDeviceService.create(storeDeviceDTO)).thenReturn(false);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/device/bind")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testCreate_StoreDeviceServiceReturnsTrue() throws Exception {
        // Setup
        // Configure StoreDeviceService.create(...).
        final StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO();
        storeDeviceDTO.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO.setStoreNo("storeNo");
        storeDeviceDTO.setStoreGuid("storeGuid");
        storeDeviceDTO.setStoreName("storeName");
        storeDeviceDTO.setDeviceNo("deviceNo");
        when(mockStoreDeviceService.create(storeDeviceDTO)).thenReturn(true);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/device/bind")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testFindStoreDevice() throws Exception {
        // Setup
        // Configure StoreDeviceService.findStoreDevice(...).
        final StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO();
        storeDeviceDTO.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO.setStoreNo("storeNo");
        storeDeviceDTO.setStoreGuid("storeGuid");
        storeDeviceDTO.setStoreName("storeName");
        storeDeviceDTO.setDeviceNo("deviceNo");
        final List<StoreDeviceDTO> storeDeviceDTOS = Arrays.asList(storeDeviceDTO);
        when(mockStoreDeviceService.findStoreDevice(new StoreDeviceQueryDTO("storeGuid", 0)))
                .thenReturn(storeDeviceDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/device/find_store_device")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testFindStoreDevice_StoreDeviceServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockStoreDeviceService.findStoreDevice(new StoreDeviceQueryDTO("storeGuid", 0)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/device/find_store_device")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("[]", response.getContentAsString());
    }

    @Test
    public void testSort() throws Exception {
        // Setup
        when(mockStoreDeviceService.sort(
                Arrays.asList(new StoreDeviceSortDTO("storeGuid", "deviceNo", "deviceGuid", 0, 0)))).thenReturn(false);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/device/sort")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testSort_StoreDeviceServiceReturnsTrue() throws Exception {
        // Setup
        when(mockStoreDeviceService.sort(
                Arrays.asList(new StoreDeviceSortDTO("storeGuid", "deviceNo", "deviceGuid", 0, 0)))).thenReturn(true);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/device/sort")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testUnbind() throws Exception {
        // Setup
        when(mockStoreDeviceService.unbind(any(StoreDeviceUnbindDTO.class))).thenReturn(false);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/device/unbind")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testUnbind_StoreDeviceServiceReturnsTrue() throws Exception {
        // Setup
        when(mockStoreDeviceService.unbind(any(StoreDeviceUnbindDTO.class))).thenReturn(true);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/device/unbind")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testSetMasterDevice() throws Exception {
        // Setup
        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(
                        post("/device/set_master/{storeGuid}/{deviceGuid}", "storeGuid", "deviceGuid")
                                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
        verify(mockStoreDeviceService).setMasterDevice("storeGuid", "deviceGuid");
    }

    @Test
    public void testGetMasterDeviceByStoreGuid1() throws Exception {
        // Setup
        // Configure StoreDeviceService.getMasterDeviceByStoreGuid(...).
        final StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO();
        storeDeviceDTO.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO.setStoreNo("storeNo");
        storeDeviceDTO.setStoreGuid("storeGuid");
        storeDeviceDTO.setStoreName("storeName");
        storeDeviceDTO.setDeviceNo("deviceNo");
        when(mockStoreDeviceService.getMasterDeviceByStoreGuid("storeGuid")).thenReturn(storeDeviceDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(
                        get("/device/get_master_device_by_storeguid/{storeGuid}", "storeGuid")
                                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testGetMasterDeviceByStoreGuid2() throws Exception {
        // Setup
        // Configure StoreDeviceService.getMasterDeviceByStoreGuid(...).
        final StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO();
        storeDeviceDTO.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO.setStoreNo("storeNo");
        storeDeviceDTO.setStoreGuid("storeGuid");
        storeDeviceDTO.setStoreName("storeName");
        storeDeviceDTO.setDeviceNo("deviceNo");
        when(mockStoreDeviceService.getMasterDeviceByStoreGuid("storeGuid")).thenReturn(storeDeviceDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(
                        get("/device/get_master_device/{enterpriseGuid}/{storeGuid}", "enterpriseGuid", "storeGuid")
                                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testFindDeviceStatus() throws Exception {
        // Setup
        // Configure StoreDeviceService.findDeviceStatus(...).
        final StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO();
        storeDeviceDTO.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO.setStoreNo("storeNo");
        storeDeviceDTO.setStoreGuid("storeGuid");
        storeDeviceDTO.setStoreName("storeName");
        storeDeviceDTO.setDeviceNo("deviceNo");
        when(mockStoreDeviceService.findDeviceStatus("deviceNo")).thenReturn(storeDeviceDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(
                        get("/device/find_device_status/{deviceNo}", "deviceNo")
                                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testQueryPadOrderType() throws Exception {
        // Setup
        // Configure StoreDeviceService.queryPadOrderType(...).
        final PadOrderTypeRespDTO padOrderTypeRespDTO = new PadOrderTypeRespDTO();
        padOrderTypeRespDTO.setPadOrderType(0);
        final PadAreaDTO padAreaDTO = new PadAreaDTO();
        padAreaDTO.setAreaGuid("areaGuid");
        padAreaDTO.setAreaName("areaName");
        final TableInfoDTO tableInfoDTO = new TableInfoDTO();
        padAreaDTO.setTableIDTOList(Arrays.asList(tableInfoDTO));
        padOrderTypeRespDTO.setPadAreaDTOList(Arrays.asList(padAreaDTO));
        when(mockStoreDeviceService.queryPadOrderType(any(PadOrderTypeReqDTO.class))).thenReturn(padOrderTypeRespDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/device/query_pad_order_type")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testQueryBindingTableInfo() throws Exception {
        // Setup
        // Configure StoreDeviceService.queryBindingTableInfo(...).
        final TableInfoDTO tableInfoDTO = new TableInfoDTO();
        tableInfoDTO.setTableName("tableName");
        tableInfoDTO.setAreaGuid("areaGuid");
        tableInfoDTO.setAreaName("areaName");
        tableInfoDTO.setSort(0);
        tableInfoDTO.setIsBind(false);
        when(mockStoreDeviceService.queryBindingTableInfo(any(PadOrderTypeReqDTO.class))).thenReturn(tableInfoDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/device/query_binding_table_info")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testQueryUnBindingTableInfo() throws Exception {
        // Setup
        // Configure StoreDeviceService.queryUnBindingTableInfo(...).
        final TableInfoDTO tableInfoDTO = new TableInfoDTO();
        tableInfoDTO.setTableName("tableName");
        tableInfoDTO.setAreaGuid("areaGuid");
        tableInfoDTO.setAreaName("areaName");
        tableInfoDTO.setSort(0);
        tableInfoDTO.setIsBind(false);
        final List<PadAreaDTO> padAreaDTOS = Arrays.asList(
                new PadAreaDTO("areaGuid", "areaName", Arrays.asList(tableInfoDTO)));
        when(mockStoreDeviceService.queryUnBindingTableInfo(any(PadOrderTypeReqDTO.class))).thenReturn(padAreaDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/device/query_un_binding_table_info")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testQueryUnBindingTableInfo_StoreDeviceServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockStoreDeviceService.queryUnBindingTableInfo(any(PadOrderTypeReqDTO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/device/query_un_binding_table_info")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("[]", response.getContentAsString());
    }

    @Test
    public void testInitializePadOrderTypeSet() throws Exception {
        // Setup
        when(mockStoreDeviceService.initializePadOrderTypeSet(any(PadOrderTypeReqDTO.class))).thenReturn(false);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/device/initialize_pad_order_type_set")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testInitializePadOrderTypeSet_StoreDeviceServiceReturnsTrue() throws Exception {
        // Setup
        when(mockStoreDeviceService.initializePadOrderTypeSet(any(PadOrderTypeReqDTO.class))).thenReturn(true);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/device/initialize_pad_order_type_set")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testPadOrderTypeSet() throws Exception {
        // Setup
        when(mockStoreDeviceService.padOrderTypeSet(any(PadOrderTypeReqDTO.class))).thenReturn(false);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/device/pad_order_type_set")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testPadOrderTypeSet_StoreDeviceServiceReturnsTrue() throws Exception {
        // Setup
        when(mockStoreDeviceService.padOrderTypeSet(any(PadOrderTypeReqDTO.class))).thenReturn(true);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/device/pad_order_type_set")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testQueryDeviceByStoreTable() throws Exception {
        // Setup
        // Configure StoreDeviceService.queryDeviceByStoreTable(...).
        final StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO();
        storeDeviceDTO.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO.setStoreNo("storeNo");
        storeDeviceDTO.setStoreGuid("storeGuid");
        storeDeviceDTO.setStoreName("storeName");
        storeDeviceDTO.setDeviceNo("deviceNo");
        when(mockStoreDeviceService.queryDeviceByStoreTable(any(PadOrderTypeReqDTO.class))).thenReturn(storeDeviceDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/device/query_device_by_store_table")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testListDeviceByStoreTable() throws Exception {
        // Setup
        // Configure StoreDeviceService.listDeviceByStoreTable(...).
        final StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO();
        storeDeviceDTO.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO.setStoreNo("storeNo");
        storeDeviceDTO.setStoreGuid("storeGuid");
        storeDeviceDTO.setStoreName("storeName");
        storeDeviceDTO.setDeviceNo("deviceNo");
        final List<StoreDeviceDTO> storeDeviceDTOS = Arrays.asList(storeDeviceDTO);
        when(mockStoreDeviceService.listDeviceByStoreTable(any(PadOrderTypeReqDTO.class))).thenReturn(storeDeviceDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/device/list_device_by_store_table")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testListDeviceByStoreTable_StoreDeviceServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockStoreDeviceService.listDeviceByStoreTable(any(PadOrderTypeReqDTO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/device/list_device_by_store_table")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("[]", response.getContentAsString());
    }

    @Test
    public void testListAllDeviceByStoreTable() throws Exception {
        // Setup
        // Configure StoreDeviceService.listAllDeviceByStoreTable(...).
        final StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO();
        storeDeviceDTO.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO.setStoreNo("storeNo");
        storeDeviceDTO.setStoreGuid("storeGuid");
        storeDeviceDTO.setStoreName("storeName");
        storeDeviceDTO.setDeviceNo("deviceNo");
        final List<StoreDeviceDTO> storeDeviceDTOS = Arrays.asList(storeDeviceDTO);
        when(mockStoreDeviceService.listAllDeviceByStoreTable(any(PadOrderTypeReqDTO.class)))
                .thenReturn(storeDeviceDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/device/list_all_device_by_store_table")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testListAllDeviceByStoreTable_StoreDeviceServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockStoreDeviceService.listAllDeviceByStoreTable(any(PadOrderTypeReqDTO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/device/list_all_device_by_store_table")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("[]", response.getContentAsString());
    }

    @Test
    public void testQueryDeviceByDeviceId() throws Exception {
        // Setup
        // Configure StoreDeviceService.queryDeviceByDeviceId(...).
        final StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO();
        storeDeviceDTO.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO.setStoreNo("storeNo");
        storeDeviceDTO.setStoreGuid("storeGuid");
        storeDeviceDTO.setStoreName("storeName");
        storeDeviceDTO.setDeviceNo("deviceNo");
        when(mockStoreDeviceService.queryDeviceByDeviceId("deviceId")).thenReturn(storeDeviceDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/device/query_device_by_deviceId")
                        .param("deviceId", "deviceId")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }
}
