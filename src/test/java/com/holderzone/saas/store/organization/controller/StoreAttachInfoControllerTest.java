package com.holderzone.saas.store.organization.controller;

import com.holderzone.saas.store.dto.organization.StoreAttachInfoDTO;
import com.holderzone.saas.store.organization.controller.StoreAttachInfoController;
import com.holderzone.saas.store.organization.service.StoreAttachInfoService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

@RunWith(SpringRunner.class)
@WebMvcTest(StoreAttachInfoController.class)
public class StoreAttachInfoControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private StoreAttachInfoService mockStoreAttachInfoService;

    @Test
    public void testInfo() throws Exception {
        // Setup
        // Configure StoreAttachInfoService.info(...).
        final StoreAttachInfoDTO storeAttachInfoDTO = new StoreAttachInfoDTO();
        storeAttachInfoDTO.setStoreGuid("storeGuid");
        storeAttachInfoDTO.setUatAppId("uatAppId");
        storeAttachInfoDTO.setUatSecret("uatSecret");
        when(mockStoreAttachInfoService.info("storeGuid")).thenReturn(storeAttachInfoDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/store-attach-info/info")
                        .param("storeGuid", "storeGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testSave() throws Exception {
        // Setup
        // Configure StoreAttachInfoService.save(...).
        final StoreAttachInfoDTO dto = new StoreAttachInfoDTO();
        dto.setStoreGuid("storeGuid");
        dto.setUatAppId("uatAppId");
        dto.setUatSecret("uatSecret");
        when(mockStoreAttachInfoService.save(dto)).thenReturn(false);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/store-attach-info/save")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testSave_StoreAttachInfoServiceReturnsTrue() throws Exception {
        // Setup
        // Configure StoreAttachInfoService.save(...).
        final StoreAttachInfoDTO dto = new StoreAttachInfoDTO();
        dto.setStoreGuid("storeGuid");
        dto.setUatAppId("uatAppId");
        dto.setUatSecret("uatSecret");
        when(mockStoreAttachInfoService.save(dto)).thenReturn(true);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/store-attach-info/save")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }
}
