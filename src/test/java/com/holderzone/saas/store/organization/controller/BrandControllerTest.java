package com.holderzone.saas.store.organization.controller;

import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.dto.organization.QueryBrandDTO;
import com.holderzone.saas.store.organization.controller.BrandController;
import com.holderzone.saas.store.organization.service.BrandService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

@RunWith(SpringRunner.class)
@WebMvcTest(BrandController.class)
public class BrandControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private BrandService mockBrandService;

    @Test
    public void testCreateBrand() throws Exception {
        // Setup
        // Configure BrandService.createBrand(...).
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("3ce858a7-7423-4abf-a903-033b3f3ffc5d");
        brandDTO.setUuid("a1ee6f3c-a5e2-47a2-83d7-e45e6b0b48f2");
        brandDTO.setName("name");
        brandDTO.setDescription("description");
        brandDTO.setLogoUrl("logoUrl");
        when(mockBrandService.createBrand(any(BrandDTO.class))).thenReturn(brandDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/brand/create")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testUpdateBrand() throws Exception {
        // Setup
        when(mockBrandService.updateBrand(any(BrandDTO.class))).thenReturn(false);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/brand/update")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testUpdateBrand_BrandServiceReturnsTrue() throws Exception {
        // Setup
        when(mockBrandService.updateBrand(any(BrandDTO.class))).thenReturn(true);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/brand/update")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testDeleteBrand() throws Exception {
        // Setup
        when(mockBrandService.deleteBrand("brandGuid")).thenReturn(false);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/brand/delete")
                        .param("brandGuid", "brandGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testDeleteBrand_BrandServiceReturnsTrue() throws Exception {
        // Setup
        when(mockBrandService.deleteBrand("brandGuid")).thenReturn(true);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/brand/delete")
                        .param("brandGuid", "brandGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testQueryBrandByGuid() throws Exception {
        // Setup
        // Configure BrandService.queryBrandByGuid(...).
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("3ce858a7-7423-4abf-a903-033b3f3ffc5d");
        brandDTO.setUuid("a1ee6f3c-a5e2-47a2-83d7-e45e6b0b48f2");
        brandDTO.setName("name");
        brandDTO.setDescription("description");
        brandDTO.setLogoUrl("logoUrl");
        when(mockBrandService.queryBrandByGuid("brandGuid")).thenReturn(brandDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/brand/query_brand_by_guid")
                        .param("brandGuid", "brandGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testQueryBrandByIdList() throws Exception {
        // Setup
        // Configure BrandService.queryBrandByIdList(...).
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("3ce858a7-7423-4abf-a903-033b3f3ffc5d");
        brandDTO.setUuid("a1ee6f3c-a5e2-47a2-83d7-e45e6b0b48f2");
        brandDTO.setName("name");
        brandDTO.setDescription("description");
        brandDTO.setLogoUrl("logoUrl");
        final List<BrandDTO> brandDTOS = Arrays.asList(brandDTO);
        when(mockBrandService.queryBrandByIdList(Arrays.asList("value"))).thenReturn(brandDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/brand/query_brand_by_idlist")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testQueryBrandByIdList_BrandServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockBrandService.queryBrandByIdList(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/brand/query_brand_by_idlist")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("[]", response.getContentAsString());
    }

    @Test
    public void testQueryList() throws Exception {
        // Setup
        // Configure BrandService.queryAllList(...).
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("3ce858a7-7423-4abf-a903-033b3f3ffc5d");
        brandDTO.setUuid("a1ee6f3c-a5e2-47a2-83d7-e45e6b0b48f2");
        brandDTO.setName("name");
        brandDTO.setDescription("description");
        brandDTO.setLogoUrl("logoUrl");
        final List<BrandDTO> brandDTOS = Arrays.asList(brandDTO);
        when(mockBrandService.queryAllList(new QueryBrandDTO("brandGuid", "brandName"))).thenReturn(brandDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/brand/query_list")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testQueryList_BrandServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockBrandService.queryAllList(new QueryBrandDTO("brandGuid", "brandName")))
                .thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/brand/query_list")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("[]", response.getContentAsString());
    }

    @Test
    public void testQueryExistStoreAccount() throws Exception {
        // Setup
        when(mockBrandService.queryExistStoreAccount("brandGuid")).thenReturn(false);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(
                        post("/brand/query_exist_store_account/{brandGuid}", "brandGuid")
                                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testQueryExistStoreAccount_BrandServiceReturnsTrue() throws Exception {
        // Setup
        when(mockBrandService.queryExistStoreAccount("brandGuid")).thenReturn(true);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(
                        post("/brand/query_exist_store_account/{brandGuid}", "brandGuid")
                                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testQueryStoreGuidListByBrandGui() throws Exception {
        // Setup
        when(mockBrandService.queryStoreGuidListByBrandGuid("brandGuid")).thenReturn(Arrays.asList("value"));

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/brand/query_store_guid_list_by_brand_guid")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testQueryStoreGuidListByBrandGui_BrandServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockBrandService.queryStoreGuidListByBrandGuid("brandGuid")).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/brand/query_store_guid_list_by_brand_guid")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("[]", response.getContentAsString());
    }

    @Test
    public void testUpdateSalesModel() throws Exception {
        // Setup
        when(mockBrandService.updateSalesModel(any(BrandDTO.class))).thenReturn(false);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/brand/update_sales_model")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testUpdateSalesModel_BrandServiceReturnsTrue() throws Exception {
        // Setup
        when(mockBrandService.updateSalesModel(any(BrandDTO.class))).thenReturn(true);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/brand/update_sales_model")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }
}
