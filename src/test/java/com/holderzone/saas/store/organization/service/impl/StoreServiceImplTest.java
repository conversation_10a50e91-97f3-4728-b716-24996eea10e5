package com.holderzone.saas.store.organization.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.config.req.ConfigReqDTO;
import com.holderzone.saas.store.dto.config.req.ConfigReqQueryDTO;
import com.holderzone.saas.store.dto.config.resp.ConfigRespDTO;
import com.holderzone.saas.store.dto.item.resp.PricePlanBingStoreRespDTO;
import com.holderzone.saas.store.dto.order.BusinessDayDTO;
import com.holderzone.saas.store.dto.organization.*;
import com.holderzone.saas.store.dto.store.store.BindupAccountsDTO;
import com.holderzone.saas.store.dto.store.store.BindupAccountsSaveDTO;
import com.holderzone.saas.store.dto.store.store.StoreProductDTO;
import com.holderzone.saas.store.dto.store.table.TableStatusDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxOrderConfigDTO;
import com.holderzone.saas.store.organization.domain.BrandDO;
import com.holderzone.saas.store.organization.domain.OrganizationDO;
import com.holderzone.saas.store.organization.domain.StoreBrandDO;
import com.holderzone.saas.store.organization.feign.ConfigFeignService;
import com.holderzone.saas.store.organization.feign.EnterpriseClientService;
import com.holderzone.saas.store.organization.feign.WxStoreOrderConfigService;
import com.holderzone.saas.store.organization.mapper.BrandMapper;
import com.holderzone.saas.store.organization.mapper.OrganizationMapper;
import com.holderzone.saas.store.organization.mapper.StoreAttachMapper;
import com.holderzone.saas.store.organization.mapper.StoreBrandMapper;
import com.holderzone.saas.store.organization.mapstruct.BrandMapstruct;
import com.holderzone.saas.store.organization.mapstruct.StoreMapstruct;
import com.holderzone.saas.store.organization.service.BrandService;
import com.holderzone.saas.store.organization.service.BroadcastService;
import com.holderzone.saas.store.organization.service.DistributedIdService;
import com.holderzone.saas.store.organization.service.StoreBrandService;
import com.holderzone.saas.store.organization.service.impl.StoreServiceImpl;
import com.holderzone.saas.store.organization.service.remote.BusinessClient;
import com.holderzone.saas.store.organization.service.remote.EnterpriseClient;
import com.holderzone.saas.store.organization.service.remote.ItemClient;
import com.holderzone.saas.store.organization.service.remote.UserClient;
import com.holderzone.saas.store.organization.utils.DynamicHelper;
import com.holderzone.saas.store.organization.utils.RedisUtils;
import com.holderzone.saas.store.organization.vo.BindupAccountsDo;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.integration.redis.util.RedisLockRegistry;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class StoreServiceImplTest {

    @Mock
    private OrganizationMapper mockOrganizationMapper;
    @Mock
    private StoreMapstruct mockStoreMapstruct;
    @Mock
    private UserClient mockUserClient;
    @Mock
    private EnterpriseClient mockEnterpriseClient;
    @Mock
    private StoreBrandMapper mockStoreBrandMapper;
    @Mock
    private StoreBrandService mockStoreBrandService;
    @Mock
    private BrandMapstruct mockBrandMapstruct;
    @Mock
    private BrandMapper mockBrandMapper;
    @Mock
    private DistributedIdService mockDistributedIdService;
    @Mock
    private BroadcastService mockBroadcastService;
    @Mock
    private RedisUtils mockRedisUtils;
    @Mock
    private StoreAttachMapper mockStoreAttachMapper;
    @Mock
    private ItemClient mockItemClient;
    @Mock
    private WxStoreOrderConfigService mockWxStoreOrderConfigService;
    @Mock
    private ConfigFeignService mockConfigFeignService;
    @Mock
    private BusinessClient mockBusinessClient;
    @Mock
    private BrandService mockBrandService;
    @Mock
    private DynamicHelper mockDynamicHelper;
    @Mock
    private EnterpriseClientService mockEnterpriseClientService;

    private StoreServiceImpl storeServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        storeServiceImplUnderTest = new StoreServiceImpl(mockOrganizationMapper, mockStoreMapstruct, mockUserClient,
                mockEnterpriseClient, mockStoreBrandMapper, mockStoreBrandService, mockBrandMapstruct, mockBrandMapper,
                mockDistributedIdService, mockBroadcastService, mockRedisUtils, mockStoreAttachMapper, mockItemClient,
                mockWxStoreOrderConfigService, new RedisLockRegistry(null, "registryKey"), mockConfigFeignService,
                mockBusinessClient, mockBrandService, mockDynamicHelper, mockEnterpriseClientService);
    }

    @Test
    public void testCreateStore() throws Exception {
        // Setup
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        brandDTO.setStoreList(Arrays.asList(new StoreDTO()));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));

        when(mockOrganizationMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(0);

        // Run the test
        final boolean result = storeServiceImplUnderTest.createStore(storeDTO);

        // Verify the results
        assertTrue(result);
    }

    @Test
    public void testCreateStore_OrganizationMapperReturnsNull() {
        // Setup
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        brandDTO.setStoreList(Arrays.asList(new StoreDTO()));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));

        when(mockOrganizationMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(null);
        when(mockDistributedIdService.nextOrganizationGuid()).thenReturn("result");
        when(mockEnterpriseClient.checkCode("code")).thenReturn(false);
        when(mockStoreBrandService.createStoreBrand("guid", "belongBrandGuid", "createUserGuid",
                "modifiedUserGuid")).thenReturn("belongBrandGuid");

        // Configure BrandService.queryBrandByGuid(...).
        final BrandDTO brandDTO1 = new BrandDTO();
        brandDTO1.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO1.setName("belongBrandName");
        brandDTO1.setLogoUrl("brandLogoUrl");
        brandDTO1.setIsEnable(false);
        final StoreDTO storeDTO1 = new StoreDTO();
        storeDTO1.setGuid("guid");
        storeDTO1.setCode("code");
        storeDTO1.setName("name");
        storeDTO1.setBelongBrandGuid("belongBrandGuid");
        storeDTO1.setBelongBrandName("belongBrandName");
        storeDTO1.setParentIds("parentIds");
        storeDTO1.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO1.setContactTel("contactTel");
        storeDTO1.setCityCode("cityCode");
        storeDTO1.setCityName("cityName");
        storeDTO1.setAddressDetail("addressDetail");
        storeDTO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO1.setCreateUserGuid("createUserGuid");
        storeDTO1.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO1 = new StoreProductDTO();
        storeProductDTO1.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO1.setProductDTOList(Arrays.asList(storeProductDTO1));
        storeDTO1.setBrandDTOList(Arrays.asList(new BrandDTO()));
        storeDTO1.setMchntTypeCode("mchntTypeCode");
        storeDTO1.setIsSelfBuildItems(0);
        storeDTO1.setIsBuAccounts(0);
        storeDTO1.setIsShowCash(0);
        storeDTO1.setIsMultiHandover(0);
        storeDTO1.setBusinessDay(LocalDate.of(2020, 1, 1));
        brandDTO1.setStoreList(Arrays.asList(storeDTO1));
        brandDTO1.setIsBuAccounts(0);
        brandDTO1.setIsShowCash(0);
        brandDTO1.setIsMultiHandover(0);
        when(mockBrandService.queryBrandByGuid("belongBrandGuid")).thenReturn(brandDTO1);

        // Configure StoreMapstruct.storeDTO2DTO(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        when(mockStoreMapstruct.storeDTO2DTO(any(StoreDTO.class))).thenReturn(organizationDO);

        when(mockUserClient.queryMchntType()).thenReturn("mchntTypeCode");

        // Run the test
        final boolean result = storeServiceImplUnderTest.createStore(storeDTO);

        // Verify the results
        assertTrue(result);
        verify(mockBroadcastService).storeCreated(any(StoreDTO.class));

        // Confirm BroadcastService.createStoreInCloud(...).
        final OrganizationDO organizationDO1 = new OrganizationDO();
        organizationDO1.setGuid("guid");
        organizationDO1.setCode("code");
        organizationDO1.setType(0);
        organizationDO1.setName("name");
        organizationDO1.setParentIds("parentIds");
        organizationDO1.setProvinceCode("provinceCode");
        organizationDO1.setProvinceName("provinceName");
        organizationDO1.setCityCode("cityCode");
        organizationDO1.setCityName("cityName");
        organizationDO1.setIsEnable(false);
        organizationDO1.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO1.setModifiedUserGuid("modifiedUserGuid");
        organizationDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setIsSelfBuildItems(0);
        organizationDO1.setIsBuAccounts(0);
        organizationDO1.setIsShowCash(0);
        verify(mockBroadcastService).createStoreInCloud(organizationDO1);

        // Confirm BroadcastService.createWareHouse(...).
        final OrganizationDO organizationDO2 = new OrganizationDO();
        organizationDO2.setGuid("guid");
        organizationDO2.setCode("code");
        organizationDO2.setType(0);
        organizationDO2.setName("name");
        organizationDO2.setParentIds("parentIds");
        organizationDO2.setProvinceCode("provinceCode");
        organizationDO2.setProvinceName("provinceName");
        organizationDO2.setCityCode("cityCode");
        organizationDO2.setCityName("cityName");
        organizationDO2.setIsEnable(false);
        organizationDO2.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO2.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO2.setModifiedUserGuid("modifiedUserGuid");
        organizationDO2.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO2.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO2.setIsSelfBuildItems(0);
        organizationDO2.setIsBuAccounts(0);
        organizationDO2.setIsShowCash(0);
        verify(mockBroadcastService).createWareHouse(organizationDO2);
    }

    @Test
    public void testCreateStore_EnterpriseClientReturnsNull() {
        // Setup
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        brandDTO.setStoreList(Arrays.asList(new StoreDTO()));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));

        when(mockOrganizationMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(null);
        when(mockDistributedIdService.nextOrganizationGuid()).thenReturn("result");
        when(mockEnterpriseClient.checkCode("code")).thenReturn(null);
        when(mockStoreBrandService.createStoreBrand("guid", "belongBrandGuid", "createUserGuid",
                "modifiedUserGuid")).thenReturn("belongBrandGuid");

        // Configure BrandService.queryBrandByGuid(...).
        final BrandDTO brandDTO1 = new BrandDTO();
        brandDTO1.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO1.setName("belongBrandName");
        brandDTO1.setLogoUrl("brandLogoUrl");
        brandDTO1.setIsEnable(false);
        final StoreDTO storeDTO1 = new StoreDTO();
        storeDTO1.setGuid("guid");
        storeDTO1.setCode("code");
        storeDTO1.setName("name");
        storeDTO1.setBelongBrandGuid("belongBrandGuid");
        storeDTO1.setBelongBrandName("belongBrandName");
        storeDTO1.setParentIds("parentIds");
        storeDTO1.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO1.setContactTel("contactTel");
        storeDTO1.setCityCode("cityCode");
        storeDTO1.setCityName("cityName");
        storeDTO1.setAddressDetail("addressDetail");
        storeDTO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO1.setCreateUserGuid("createUserGuid");
        storeDTO1.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO1 = new StoreProductDTO();
        storeProductDTO1.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO1.setProductDTOList(Arrays.asList(storeProductDTO1));
        storeDTO1.setBrandDTOList(Arrays.asList(new BrandDTO()));
        storeDTO1.setMchntTypeCode("mchntTypeCode");
        storeDTO1.setIsSelfBuildItems(0);
        storeDTO1.setIsBuAccounts(0);
        storeDTO1.setIsShowCash(0);
        storeDTO1.setIsMultiHandover(0);
        storeDTO1.setBusinessDay(LocalDate.of(2020, 1, 1));
        brandDTO1.setStoreList(Arrays.asList(storeDTO1));
        brandDTO1.setIsBuAccounts(0);
        brandDTO1.setIsShowCash(0);
        brandDTO1.setIsMultiHandover(0);
        when(mockBrandService.queryBrandByGuid("belongBrandGuid")).thenReturn(brandDTO1);

        // Configure StoreMapstruct.storeDTO2DTO(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        when(mockStoreMapstruct.storeDTO2DTO(any(StoreDTO.class))).thenReturn(organizationDO);

        when(mockUserClient.queryMchntType()).thenReturn("mchntTypeCode");

        // Run the test
        final boolean result = storeServiceImplUnderTest.createStore(storeDTO);

        // Verify the results
        assertTrue(result);
        verify(mockBroadcastService).storeCreated(any(StoreDTO.class));

        // Confirm BroadcastService.createStoreInCloud(...).
        final OrganizationDO organizationDO1 = new OrganizationDO();
        organizationDO1.setGuid("guid");
        organizationDO1.setCode("code");
        organizationDO1.setType(0);
        organizationDO1.setName("name");
        organizationDO1.setParentIds("parentIds");
        organizationDO1.setProvinceCode("provinceCode");
        organizationDO1.setProvinceName("provinceName");
        organizationDO1.setCityCode("cityCode");
        organizationDO1.setCityName("cityName");
        organizationDO1.setIsEnable(false);
        organizationDO1.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO1.setModifiedUserGuid("modifiedUserGuid");
        organizationDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setIsSelfBuildItems(0);
        organizationDO1.setIsBuAccounts(0);
        organizationDO1.setIsShowCash(0);
        verify(mockBroadcastService).createStoreInCloud(organizationDO1);

        // Confirm BroadcastService.createWareHouse(...).
        final OrganizationDO organizationDO2 = new OrganizationDO();
        organizationDO2.setGuid("guid");
        organizationDO2.setCode("code");
        organizationDO2.setType(0);
        organizationDO2.setName("name");
        organizationDO2.setParentIds("parentIds");
        organizationDO2.setProvinceCode("provinceCode");
        organizationDO2.setProvinceName("provinceName");
        organizationDO2.setCityCode("cityCode");
        organizationDO2.setCityName("cityName");
        organizationDO2.setIsEnable(false);
        organizationDO2.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO2.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO2.setModifiedUserGuid("modifiedUserGuid");
        organizationDO2.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO2.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO2.setIsSelfBuildItems(0);
        organizationDO2.setIsBuAccounts(0);
        organizationDO2.setIsShowCash(0);
        verify(mockBroadcastService).createWareHouse(organizationDO2);
    }

    @Test
    public void testItemUploadUpdate() throws Exception {
        // Setup
        final ItemUploadUpdateReq req = new ItemUploadUpdateReq();
        req.setStoreGuid("storeGuid");
        req.setIsItemUpload(0);

        when(mockOrganizationMapper.updateItemUpload("storeGuid", 0)).thenReturn(0);

        // Run the test
        final boolean result = storeServiceImplUnderTest.itemUploadUpdate(req);

        // Verify the results
        assertFalse(result);
        verify(mockBroadcastService).storeUpdated(any(StoreDTO.class));
    }

    @Test
    public void testUpdateStore() throws Exception {
        // Setup
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        brandDTO.setStoreList(Arrays.asList(new StoreDTO()));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));

        // Configure StoreMapstruct.storeDTO2DTO(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        when(mockStoreMapstruct.storeDTO2DTO(any(StoreDTO.class))).thenReturn(organizationDO);

        when(mockOrganizationMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(0);

        // Configure ItemClient.getPlanStoreByStoreGuid(...).
        final PricePlanBingStoreRespDTO pricePlanBingStoreRespDTO = new PricePlanBingStoreRespDTO();
        pricePlanBingStoreRespDTO.setGuid("0edbc86f-ee91-44b7-8116-053346f6e2d5");
        pricePlanBingStoreRespDTO.setBrandGuid("brandGuid");
        pricePlanBingStoreRespDTO.setPlanGuid("planGuid");
        pricePlanBingStoreRespDTO.setStoreGuid("storeGuid");
        final List<PricePlanBingStoreRespDTO> pricePlanBingStoreRespDTOS = Arrays.asList(pricePlanBingStoreRespDTO);
        when(mockItemClient.getPlanStoreByStoreGuid("guid")).thenReturn(pricePlanBingStoreRespDTOS);

        // Configure ConfigFeignService.selectEstimateResetTime(...).
        final ConfigRespDTO configRespDTO = new ConfigRespDTO();
        configRespDTO.setGuid("3560026c-36ee-4f9c-9c45-71f43e620625");
        configRespDTO.setEnterpriseGuid("enterpriseGuid");
        configRespDTO.setStoreGuid("guid");
        configRespDTO.setDicCode(0);
        configRespDTO.setDicName("desc");
        final ConfigReqQueryDTO configReqQueryDTO = new ConfigReqQueryDTO();
        configReqQueryDTO.setEnterpriseGuid("enterpriseGuid");
        configReqQueryDTO.setStoreGuid("guid");
        configReqQueryDTO.setDicCode(0);
        when(mockConfigFeignService.selectEstimateResetTime(configReqQueryDTO)).thenReturn(configRespDTO);

        // Configure ConfigFeignService.getConfigByCode(...).
        final ConfigRespDTO configRespDTO1 = new ConfigRespDTO();
        configRespDTO1.setGuid("3560026c-36ee-4f9c-9c45-71f43e620625");
        configRespDTO1.setEnterpriseGuid("enterpriseGuid");
        configRespDTO1.setStoreGuid("guid");
        configRespDTO1.setDicCode(0);
        configRespDTO1.setDicName("desc");
        final ConfigReqQueryDTO request = new ConfigReqQueryDTO();
        request.setEnterpriseGuid("enterpriseGuid");
        request.setStoreGuid("guid");
        request.setDicCode(0);
        when(mockConfigFeignService.getConfigByCode(request)).thenReturn(configRespDTO1);

        // Run the test
        final boolean result = storeServiceImplUnderTest.updateStore(storeDTO, false);

        // Verify the results
        assertTrue(result);

        // Confirm StoreBrandMapper.update(...).
        final StoreBrandDO entity = new StoreBrandDO();
        entity.setId(0L);
        entity.setGuid("07c02081-ef10-4f32-bc79-55e724f415bc");
        entity.setStoreGuid("storeGuid");
        entity.setBrandGuid("belongBrandGuid");
        entity.setModifiedUserGuid("modifiedUserGuid");
        verify(mockStoreBrandMapper).update(eq(entity), any(LambdaQueryWrapper.class));

        // Confirm ConfigFeignService.saveEstimateResetTime(...).
        final ConfigReqDTO configReqDTO = new ConfigReqDTO();
        configReqDTO.setGuid("3560026c-36ee-4f9c-9c45-71f43e620625");
        configReqDTO.setEnterpriseGuid("enterpriseGuid");
        configReqDTO.setStoreGuid("guid");
        configReqDTO.setDicCode(0);
        configReqDTO.setDicName("desc");
        configReqDTO.setDictValue("dictValue");
        configReqDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        configReqDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        configReqDTO.setIsEnable(0);
        verify(mockConfigFeignService).saveEstimateResetTime(configReqDTO);
        verify(mockItemClient).storeItemEstimateCancel(new HashMap<>());
        verify(mockRedisUtils).delete("key");
        verify(mockBroadcastService).storeUpdated(any(StoreDTO.class));

        // Confirm BroadcastService.updateStoreInCloud(...).
        final OrganizationDO organizationDO1 = new OrganizationDO();
        organizationDO1.setGuid("guid");
        organizationDO1.setCode("code");
        organizationDO1.setType(0);
        organizationDO1.setName("name");
        organizationDO1.setParentIds("parentIds");
        organizationDO1.setProvinceCode("provinceCode");
        organizationDO1.setProvinceName("provinceName");
        organizationDO1.setCityCode("cityCode");
        organizationDO1.setCityName("cityName");
        organizationDO1.setIsEnable(false);
        organizationDO1.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO1.setModifiedUserGuid("modifiedUserGuid");
        organizationDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setIsSelfBuildItems(0);
        organizationDO1.setIsBuAccounts(0);
        organizationDO1.setIsShowCash(0);
        verify(mockBroadcastService).updateStoreInCloud(organizationDO1);
        verify(mockBroadcastService).updateWareHouse(any(StoreDTO.class));
    }

    @Test
    public void testUpdateStore_OrganizationMapperReturnsNull() {
        // Setup
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        brandDTO.setStoreList(Arrays.asList(new StoreDTO()));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));

        // Configure StoreMapstruct.storeDTO2DTO(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        when(mockStoreMapstruct.storeDTO2DTO(any(StoreDTO.class))).thenReturn(organizationDO);

        when(mockOrganizationMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Configure ItemClient.getPlanStoreByStoreGuid(...).
        final PricePlanBingStoreRespDTO pricePlanBingStoreRespDTO = new PricePlanBingStoreRespDTO();
        pricePlanBingStoreRespDTO.setGuid("0edbc86f-ee91-44b7-8116-053346f6e2d5");
        pricePlanBingStoreRespDTO.setBrandGuid("brandGuid");
        pricePlanBingStoreRespDTO.setPlanGuid("planGuid");
        pricePlanBingStoreRespDTO.setStoreGuid("storeGuid");
        final List<PricePlanBingStoreRespDTO> pricePlanBingStoreRespDTOS = Arrays.asList(pricePlanBingStoreRespDTO);
        when(mockItemClient.getPlanStoreByStoreGuid("guid")).thenReturn(pricePlanBingStoreRespDTOS);

        // Configure ConfigFeignService.selectEstimateResetTime(...).
        final ConfigRespDTO configRespDTO = new ConfigRespDTO();
        configRespDTO.setGuid("3560026c-36ee-4f9c-9c45-71f43e620625");
        configRespDTO.setEnterpriseGuid("enterpriseGuid");
        configRespDTO.setStoreGuid("guid");
        configRespDTO.setDicCode(0);
        configRespDTO.setDicName("desc");
        final ConfigReqQueryDTO configReqQueryDTO = new ConfigReqQueryDTO();
        configReqQueryDTO.setEnterpriseGuid("enterpriseGuid");
        configReqQueryDTO.setStoreGuid("guid");
        configReqQueryDTO.setDicCode(0);
        when(mockConfigFeignService.selectEstimateResetTime(configReqQueryDTO)).thenReturn(configRespDTO);

        // Configure ConfigFeignService.getConfigByCode(...).
        final ConfigRespDTO configRespDTO1 = new ConfigRespDTO();
        configRespDTO1.setGuid("3560026c-36ee-4f9c-9c45-71f43e620625");
        configRespDTO1.setEnterpriseGuid("enterpriseGuid");
        configRespDTO1.setStoreGuid("guid");
        configRespDTO1.setDicCode(0);
        configRespDTO1.setDicName("desc");
        final ConfigReqQueryDTO request = new ConfigReqQueryDTO();
        request.setEnterpriseGuid("enterpriseGuid");
        request.setStoreGuid("guid");
        request.setDicCode(0);
        when(mockConfigFeignService.getConfigByCode(request)).thenReturn(configRespDTO1);

        // Run the test
        final boolean result = storeServiceImplUnderTest.updateStore(storeDTO, false);

        // Verify the results
        assertTrue(result);

        // Confirm StoreBrandMapper.update(...).
        final StoreBrandDO entity = new StoreBrandDO();
        entity.setId(0L);
        entity.setGuid("07c02081-ef10-4f32-bc79-55e724f415bc");
        entity.setStoreGuid("storeGuid");
        entity.setBrandGuid("belongBrandGuid");
        entity.setModifiedUserGuid("modifiedUserGuid");
        verify(mockStoreBrandMapper).update(eq(entity), any(LambdaQueryWrapper.class));

        // Confirm ConfigFeignService.saveEstimateResetTime(...).
        final ConfigReqDTO configReqDTO = new ConfigReqDTO();
        configReqDTO.setGuid("3560026c-36ee-4f9c-9c45-71f43e620625");
        configReqDTO.setEnterpriseGuid("enterpriseGuid");
        configReqDTO.setStoreGuid("guid");
        configReqDTO.setDicCode(0);
        configReqDTO.setDicName("desc");
        configReqDTO.setDictValue("dictValue");
        configReqDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        configReqDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        configReqDTO.setIsEnable(0);
        verify(mockConfigFeignService).saveEstimateResetTime(configReqDTO);
        verify(mockItemClient).storeItemEstimateCancel(new HashMap<>());
        verify(mockRedisUtils).delete("key");
        verify(mockBroadcastService).storeUpdated(any(StoreDTO.class));

        // Confirm BroadcastService.updateStoreInCloud(...).
        final OrganizationDO organizationDO1 = new OrganizationDO();
        organizationDO1.setGuid("guid");
        organizationDO1.setCode("code");
        organizationDO1.setType(0);
        organizationDO1.setName("name");
        organizationDO1.setParentIds("parentIds");
        organizationDO1.setProvinceCode("provinceCode");
        organizationDO1.setProvinceName("provinceName");
        organizationDO1.setCityCode("cityCode");
        organizationDO1.setCityName("cityName");
        organizationDO1.setIsEnable(false);
        organizationDO1.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO1.setModifiedUserGuid("modifiedUserGuid");
        organizationDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setIsSelfBuildItems(0);
        organizationDO1.setIsBuAccounts(0);
        organizationDO1.setIsShowCash(0);
        verify(mockBroadcastService).updateStoreInCloud(organizationDO1);
        verify(mockBroadcastService).updateWareHouse(any(StoreDTO.class));
    }

    @Test
    public void testUpdateStore_ItemClientGetPlanStoreByStoreGuidReturnsNoItems() {
        // Setup
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        brandDTO.setStoreList(Arrays.asList(new StoreDTO()));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));

        // Configure StoreMapstruct.storeDTO2DTO(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        when(mockStoreMapstruct.storeDTO2DTO(any(StoreDTO.class))).thenReturn(organizationDO);

        when(mockOrganizationMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(null);
        when(mockItemClient.getPlanStoreByStoreGuid("guid")).thenReturn(Collections.emptyList());

        // Configure ConfigFeignService.selectEstimateResetTime(...).
        final ConfigRespDTO configRespDTO = new ConfigRespDTO();
        configRespDTO.setGuid("3560026c-36ee-4f9c-9c45-71f43e620625");
        configRespDTO.setEnterpriseGuid("enterpriseGuid");
        configRespDTO.setStoreGuid("guid");
        configRespDTO.setDicCode(0);
        configRespDTO.setDicName("desc");
        final ConfigReqQueryDTO configReqQueryDTO = new ConfigReqQueryDTO();
        configReqQueryDTO.setEnterpriseGuid("enterpriseGuid");
        configReqQueryDTO.setStoreGuid("guid");
        configReqQueryDTO.setDicCode(0);
        when(mockConfigFeignService.selectEstimateResetTime(configReqQueryDTO)).thenReturn(configRespDTO);

        // Configure ConfigFeignService.getConfigByCode(...).
        final ConfigRespDTO configRespDTO1 = new ConfigRespDTO();
        configRespDTO1.setGuid("3560026c-36ee-4f9c-9c45-71f43e620625");
        configRespDTO1.setEnterpriseGuid("enterpriseGuid");
        configRespDTO1.setStoreGuid("guid");
        configRespDTO1.setDicCode(0);
        configRespDTO1.setDicName("desc");
        final ConfigReqQueryDTO request = new ConfigReqQueryDTO();
        request.setEnterpriseGuid("enterpriseGuid");
        request.setStoreGuid("guid");
        request.setDicCode(0);
        when(mockConfigFeignService.getConfigByCode(request)).thenReturn(configRespDTO1);

        // Run the test
        final boolean result = storeServiceImplUnderTest.updateStore(storeDTO, false);

        // Verify the results
        assertTrue(result);

        // Confirm StoreBrandMapper.update(...).
        final StoreBrandDO entity = new StoreBrandDO();
        entity.setId(0L);
        entity.setGuid("07c02081-ef10-4f32-bc79-55e724f415bc");
        entity.setStoreGuid("storeGuid");
        entity.setBrandGuid("belongBrandGuid");
        entity.setModifiedUserGuid("modifiedUserGuid");
        verify(mockStoreBrandMapper).update(eq(entity), any(LambdaQueryWrapper.class));

        // Confirm ConfigFeignService.saveEstimateResetTime(...).
        final ConfigReqDTO configReqDTO = new ConfigReqDTO();
        configReqDTO.setGuid("3560026c-36ee-4f9c-9c45-71f43e620625");
        configReqDTO.setEnterpriseGuid("enterpriseGuid");
        configReqDTO.setStoreGuid("guid");
        configReqDTO.setDicCode(0);
        configReqDTO.setDicName("desc");
        configReqDTO.setDictValue("dictValue");
        configReqDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        configReqDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        configReqDTO.setIsEnable(0);
        verify(mockConfigFeignService).saveEstimateResetTime(configReqDTO);
        verify(mockItemClient).storeItemEstimateCancel(new HashMap<>());
        verify(mockRedisUtils).delete("key");
        verify(mockBroadcastService).storeUpdated(any(StoreDTO.class));

        // Confirm BroadcastService.updateStoreInCloud(...).
        final OrganizationDO organizationDO1 = new OrganizationDO();
        organizationDO1.setGuid("guid");
        organizationDO1.setCode("code");
        organizationDO1.setType(0);
        organizationDO1.setName("name");
        organizationDO1.setParentIds("parentIds");
        organizationDO1.setProvinceCode("provinceCode");
        organizationDO1.setProvinceName("provinceName");
        organizationDO1.setCityCode("cityCode");
        organizationDO1.setCityName("cityName");
        organizationDO1.setIsEnable(false);
        organizationDO1.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO1.setModifiedUserGuid("modifiedUserGuid");
        organizationDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setIsSelfBuildItems(0);
        organizationDO1.setIsBuAccounts(0);
        organizationDO1.setIsShowCash(0);
        verify(mockBroadcastService).updateStoreInCloud(organizationDO1);
        verify(mockBroadcastService).updateWareHouse(any(StoreDTO.class));
    }

    @Test
    public void testUpdateStore_ConfigFeignServiceGetConfigByCodeReturnsNull() {
        // Setup
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        brandDTO.setStoreList(Arrays.asList(new StoreDTO()));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));

        // Configure StoreMapstruct.storeDTO2DTO(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        when(mockStoreMapstruct.storeDTO2DTO(any(StoreDTO.class))).thenReturn(organizationDO);

        when(mockOrganizationMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(null);
        when(mockItemClient.getPlanStoreByStoreGuid("guid")).thenReturn(Collections.emptyList());

        // Configure ConfigFeignService.selectEstimateResetTime(...).
        final ConfigRespDTO configRespDTO = new ConfigRespDTO();
        configRespDTO.setGuid("3560026c-36ee-4f9c-9c45-71f43e620625");
        configRespDTO.setEnterpriseGuid("enterpriseGuid");
        configRespDTO.setStoreGuid("guid");
        configRespDTO.setDicCode(0);
        configRespDTO.setDicName("desc");
        final ConfigReqQueryDTO configReqQueryDTO = new ConfigReqQueryDTO();
        configReqQueryDTO.setEnterpriseGuid("enterpriseGuid");
        configReqQueryDTO.setStoreGuid("guid");
        configReqQueryDTO.setDicCode(0);
        when(mockConfigFeignService.selectEstimateResetTime(configReqQueryDTO)).thenReturn(configRespDTO);

        // Configure ConfigFeignService.getConfigByCode(...).
        final ConfigReqQueryDTO request = new ConfigReqQueryDTO();
        request.setEnterpriseGuid("enterpriseGuid");
        request.setStoreGuid("guid");
        request.setDicCode(0);
        when(mockConfigFeignService.getConfigByCode(request)).thenReturn(null);

        // Run the test
        final boolean result = storeServiceImplUnderTest.updateStore(storeDTO, false);

        // Verify the results
        assertTrue(result);

        // Confirm StoreBrandMapper.update(...).
        final StoreBrandDO entity = new StoreBrandDO();
        entity.setId(0L);
        entity.setGuid("07c02081-ef10-4f32-bc79-55e724f415bc");
        entity.setStoreGuid("storeGuid");
        entity.setBrandGuid("belongBrandGuid");
        entity.setModifiedUserGuid("modifiedUserGuid");
        verify(mockStoreBrandMapper).update(eq(entity), any(LambdaQueryWrapper.class));

        // Confirm ConfigFeignService.saveEstimateResetTime(...).
        final ConfigReqDTO configReqDTO = new ConfigReqDTO();
        configReqDTO.setGuid("3560026c-36ee-4f9c-9c45-71f43e620625");
        configReqDTO.setEnterpriseGuid("enterpriseGuid");
        configReqDTO.setStoreGuid("guid");
        configReqDTO.setDicCode(0);
        configReqDTO.setDicName("desc");
        configReqDTO.setDictValue("dictValue");
        configReqDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        configReqDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        configReqDTO.setIsEnable(0);
        verify(mockConfigFeignService).saveEstimateResetTime(configReqDTO);
        verify(mockItemClient).storeItemEstimateCancel(new HashMap<>());
        verify(mockRedisUtils).delete("key");
        verify(mockBroadcastService).storeUpdated(any(StoreDTO.class));

        // Confirm BroadcastService.updateStoreInCloud(...).
        final OrganizationDO organizationDO1 = new OrganizationDO();
        organizationDO1.setGuid("guid");
        organizationDO1.setCode("code");
        organizationDO1.setType(0);
        organizationDO1.setName("name");
        organizationDO1.setParentIds("parentIds");
        organizationDO1.setProvinceCode("provinceCode");
        organizationDO1.setProvinceName("provinceName");
        organizationDO1.setCityCode("cityCode");
        organizationDO1.setCityName("cityName");
        organizationDO1.setIsEnable(false);
        organizationDO1.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO1.setModifiedUserGuid("modifiedUserGuid");
        organizationDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setIsSelfBuildItems(0);
        organizationDO1.setIsBuAccounts(0);
        organizationDO1.setIsShowCash(0);
        verify(mockBroadcastService).updateStoreInCloud(organizationDO1);
        verify(mockBroadcastService).updateWareHouse(any(StoreDTO.class));
    }

    @Test
    public void testCreateStoreByMdm() throws Exception {
        // Setup
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        brandDTO.setStoreList(Arrays.asList(new StoreDTO()));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));

        // Configure StoreMapstruct.organizationDO2DTO(...).
        final StoreDTO storeDTO1 = new StoreDTO();
        storeDTO1.setGuid("guid");
        storeDTO1.setCode("code");
        storeDTO1.setName("name");
        storeDTO1.setBelongBrandGuid("belongBrandGuid");
        storeDTO1.setBelongBrandName("belongBrandName");
        storeDTO1.setParentIds("parentIds");
        storeDTO1.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO1.setContactTel("contactTel");
        storeDTO1.setCityCode("cityCode");
        storeDTO1.setCityName("cityName");
        storeDTO1.setAddressDetail("addressDetail");
        storeDTO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO1.setCreateUserGuid("createUserGuid");
        storeDTO1.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO1 = new StoreProductDTO();
        storeProductDTO1.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO1.setProductDTOList(Arrays.asList(storeProductDTO1));
        final BrandDTO brandDTO1 = new BrandDTO();
        brandDTO1.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO1.setName("belongBrandName");
        brandDTO1.setLogoUrl("brandLogoUrl");
        brandDTO1.setIsEnable(false);
        brandDTO1.setStoreList(Arrays.asList(new StoreDTO()));
        brandDTO1.setIsBuAccounts(0);
        brandDTO1.setIsShowCash(0);
        brandDTO1.setIsMultiHandover(0);
        storeDTO1.setBrandDTOList(Arrays.asList(brandDTO1));
        storeDTO1.setMchntTypeCode("mchntTypeCode");
        storeDTO1.setIsSelfBuildItems(0);
        storeDTO1.setIsBuAccounts(0);
        storeDTO1.setIsShowCash(0);
        storeDTO1.setIsMultiHandover(0);
        storeDTO1.setBusinessDay(LocalDate.of(2020, 1, 1));
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        when(mockStoreMapstruct.organizationDO2DTO(organizationDO, "belongBrandGuid")).thenReturn(storeDTO1);

        when(mockUserClient.queryMchntType()).thenReturn("mchntTypeCode");

        // Run the test
        storeServiceImplUnderTest.createStoreByMdm(storeDTO);

        // Verify the results
        verify(mockBroadcastService).storeCreated(any(StoreDTO.class));

        // Confirm BroadcastService.createStoreInCloud(...).
        final OrganizationDO organizationDO1 = new OrganizationDO();
        organizationDO1.setGuid("guid");
        organizationDO1.setCode("code");
        organizationDO1.setType(0);
        organizationDO1.setName("name");
        organizationDO1.setParentIds("parentIds");
        organizationDO1.setProvinceCode("provinceCode");
        organizationDO1.setProvinceName("provinceName");
        organizationDO1.setCityCode("cityCode");
        organizationDO1.setCityName("cityName");
        organizationDO1.setIsEnable(false);
        organizationDO1.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO1.setModifiedUserGuid("modifiedUserGuid");
        organizationDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setIsSelfBuildItems(0);
        organizationDO1.setIsBuAccounts(0);
        organizationDO1.setIsShowCash(0);
        verify(mockBroadcastService).createStoreInCloud(organizationDO1);

        // Confirm BroadcastService.createWareHouse(...).
        final OrganizationDO organizationDO2 = new OrganizationDO();
        organizationDO2.setGuid("guid");
        organizationDO2.setCode("code");
        organizationDO2.setType(0);
        organizationDO2.setName("name");
        organizationDO2.setParentIds("parentIds");
        organizationDO2.setProvinceCode("provinceCode");
        organizationDO2.setProvinceName("provinceName");
        organizationDO2.setCityCode("cityCode");
        organizationDO2.setCityName("cityName");
        organizationDO2.setIsEnable(false);
        organizationDO2.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO2.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO2.setModifiedUserGuid("modifiedUserGuid");
        organizationDO2.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO2.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO2.setIsSelfBuildItems(0);
        organizationDO2.setIsBuAccounts(0);
        organizationDO2.setIsShowCash(0);
        verify(mockBroadcastService).createWareHouse(organizationDO2);
    }

    @Test
    public void testUpdateStoreByMdm() throws Exception {
        // Setup
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        brandDTO.setStoreList(Arrays.asList(new StoreDTO()));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));

        // Configure StoreMapstruct.organizationDO2DTO(...).
        final StoreDTO storeDTO1 = new StoreDTO();
        storeDTO1.setGuid("guid");
        storeDTO1.setCode("code");
        storeDTO1.setName("name");
        storeDTO1.setBelongBrandGuid("belongBrandGuid");
        storeDTO1.setBelongBrandName("belongBrandName");
        storeDTO1.setParentIds("parentIds");
        storeDTO1.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO1.setContactTel("contactTel");
        storeDTO1.setCityCode("cityCode");
        storeDTO1.setCityName("cityName");
        storeDTO1.setAddressDetail("addressDetail");
        storeDTO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO1.setCreateUserGuid("createUserGuid");
        storeDTO1.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO1 = new StoreProductDTO();
        storeProductDTO1.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO1.setProductDTOList(Arrays.asList(storeProductDTO1));
        final BrandDTO brandDTO1 = new BrandDTO();
        brandDTO1.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO1.setName("belongBrandName");
        brandDTO1.setLogoUrl("brandLogoUrl");
        brandDTO1.setIsEnable(false);
        brandDTO1.setStoreList(Arrays.asList(new StoreDTO()));
        brandDTO1.setIsBuAccounts(0);
        brandDTO1.setIsShowCash(0);
        brandDTO1.setIsMultiHandover(0);
        storeDTO1.setBrandDTOList(Arrays.asList(brandDTO1));
        storeDTO1.setMchntTypeCode("mchntTypeCode");
        storeDTO1.setIsSelfBuildItems(0);
        storeDTO1.setIsBuAccounts(0);
        storeDTO1.setIsShowCash(0);
        storeDTO1.setIsMultiHandover(0);
        storeDTO1.setBusinessDay(LocalDate.of(2020, 1, 1));
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        when(mockStoreMapstruct.organizationDO2DTO(organizationDO, "belongBrandGuid")).thenReturn(storeDTO1);

        // Run the test
        storeServiceImplUnderTest.updateStoreByMdm(storeDTO);

        // Verify the results
        verify(mockRedisUtils).delete("key");
        verify(mockBroadcastService).storeUpdated(any(StoreDTO.class));

        // Confirm BroadcastService.updateStoreInCloud(...).
        final OrganizationDO organizationDO1 = new OrganizationDO();
        organizationDO1.setGuid("guid");
        organizationDO1.setCode("code");
        organizationDO1.setType(0);
        organizationDO1.setName("name");
        organizationDO1.setParentIds("parentIds");
        organizationDO1.setProvinceCode("provinceCode");
        organizationDO1.setProvinceName("provinceName");
        organizationDO1.setCityCode("cityCode");
        organizationDO1.setCityName("cityName");
        organizationDO1.setIsEnable(false);
        organizationDO1.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO1.setModifiedUserGuid("modifiedUserGuid");
        organizationDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setIsSelfBuildItems(0);
        organizationDO1.setIsBuAccounts(0);
        organizationDO1.setIsShowCash(0);
        verify(mockBroadcastService).updateStoreInCloud(organizationDO1);
        verify(mockBroadcastService).updateWareHouse(any(StoreDTO.class));
    }

    @Test
    public void testUpdateBuAccounts() {
        // Setup
        final BindupAccountsSaveDTO bindupAccountsSaveDTO = new BindupAccountsSaveDTO();
        bindupAccountsSaveDTO.setIsBuAccounts(0);
        bindupAccountsSaveDTO.setIsShowCash(0);
        bindupAccountsSaveDTO.setIsMultiHandover(0);
        bindupAccountsSaveDTO.setBrandGuid("eb85852e-3c79-4312-9c64-628e27544f73");

        when(mockBrandService.updateBrandAccountStatus(any(BrandDTO.class))).thenReturn(true);

        // Configure StoreBrandMapper.selectList(...).
        final StoreBrandDO storeBrandDO = new StoreBrandDO();
        storeBrandDO.setId(0L);
        storeBrandDO.setGuid("07c02081-ef10-4f32-bc79-55e724f415bc");
        storeBrandDO.setStoreGuid("storeGuid");
        storeBrandDO.setBrandGuid("belongBrandGuid");
        storeBrandDO.setModifiedUserGuid("modifiedUserGuid");
        final List<StoreBrandDO> storeBrandDOS = Arrays.asList(storeBrandDO);
        when(mockStoreBrandMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(storeBrandDOS);

        // Configure BrandMapper.selectList(...).
        final BrandDO brandDO = new BrandDO();
        brandDO.setId(0L);
        brandDO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDO.setName("belongBrandName");
        brandDO.setDescription("description");
        brandDO.setIsEnable(false);
        final List<BrandDO> brandDOS = Arrays.asList(brandDO);
        when(mockBrandMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(brandDOS);

        // Configure OrganizationMapper.selectList(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOS = Arrays.asList(organizationDO);
        when(mockOrganizationMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(organizationDOS);

        // Configure StoreMapstruct.organizationList2DTOList(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        brandDTO.setStoreList(Arrays.asList(new StoreDTO()));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO);
        final OrganizationDO organizationDO1 = new OrganizationDO();
        organizationDO1.setGuid("guid");
        organizationDO1.setCode("code");
        organizationDO1.setType(0);
        organizationDO1.setName("name");
        organizationDO1.setParentIds("parentIds");
        organizationDO1.setProvinceCode("provinceCode");
        organizationDO1.setProvinceName("provinceName");
        organizationDO1.setCityCode("cityCode");
        organizationDO1.setCityName("cityName");
        organizationDO1.setIsEnable(false);
        organizationDO1.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO1.setModifiedUserGuid("modifiedUserGuid");
        organizationDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setIsSelfBuildItems(0);
        organizationDO1.setIsBuAccounts(0);
        organizationDO1.setIsShowCash(0);
        final List<OrganizationDO> organizationDOList = Arrays.asList(organizationDO1);
        when(mockStoreMapstruct.organizationList2DTOList(organizationDOList)).thenReturn(storeDTOS);

        // Run the test
        storeServiceImplUnderTest.updateBuAccounts(bindupAccountsSaveDTO);

        // Verify the results
        verify(mockOrganizationMapper).batchUpdateBuAccounts(eq(Arrays.asList("value")), any(StoreDTO.class));

        // Confirm ConfigFeignService.saveEstimateResetTime(...).
        final ConfigReqDTO configReqDTO = new ConfigReqDTO();
        configReqDTO.setGuid("3560026c-36ee-4f9c-9c45-71f43e620625");
        configReqDTO.setEnterpriseGuid("enterpriseGuid");
        configReqDTO.setStoreGuid("guid");
        configReqDTO.setDicCode(0);
        configReqDTO.setDicName("desc");
        configReqDTO.setDictValue("dictValue");
        configReqDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        configReqDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        configReqDTO.setIsEnable(0);
        verify(mockConfigFeignService).saveEstimateResetTime(configReqDTO);
    }

    @Test(expected = BusinessException.class)
    public void testUpdateBuAccounts_BrandServiceReturnsFalse() {
        // Setup
        final BindupAccountsSaveDTO bindupAccountsSaveDTO = new BindupAccountsSaveDTO();
        bindupAccountsSaveDTO.setIsBuAccounts(0);
        bindupAccountsSaveDTO.setIsShowCash(0);
        bindupAccountsSaveDTO.setIsMultiHandover(0);
        bindupAccountsSaveDTO.setBrandGuid("eb85852e-3c79-4312-9c64-628e27544f73");

        when(mockBrandService.updateBrandAccountStatus(any(BrandDTO.class))).thenReturn(false);

        // Run the test
        storeServiceImplUnderTest.updateBuAccounts(bindupAccountsSaveDTO);
    }

    @Test
    public void testUpdateBuAccounts_StoreBrandMapperReturnsNoItems() {
        // Setup
        final BindupAccountsSaveDTO bindupAccountsSaveDTO = new BindupAccountsSaveDTO();
        bindupAccountsSaveDTO.setIsBuAccounts(0);
        bindupAccountsSaveDTO.setIsShowCash(0);
        bindupAccountsSaveDTO.setIsMultiHandover(0);
        bindupAccountsSaveDTO.setBrandGuid("eb85852e-3c79-4312-9c64-628e27544f73");

        when(mockBrandService.updateBrandAccountStatus(any(BrandDTO.class))).thenReturn(true);
        when(mockStoreBrandMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure BrandMapper.selectList(...).
        final BrandDO brandDO = new BrandDO();
        brandDO.setId(0L);
        brandDO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDO.setName("belongBrandName");
        brandDO.setDescription("description");
        brandDO.setIsEnable(false);
        final List<BrandDO> brandDOS = Arrays.asList(brandDO);
        when(mockBrandMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(brandDOS);

        // Run the test
        storeServiceImplUnderTest.updateBuAccounts(bindupAccountsSaveDTO);

        // Verify the results
    }

    @Test
    public void testUpdateBuAccounts_BrandMapperReturnsNoItems() {
        // Setup
        final BindupAccountsSaveDTO bindupAccountsSaveDTO = new BindupAccountsSaveDTO();
        bindupAccountsSaveDTO.setIsBuAccounts(0);
        bindupAccountsSaveDTO.setIsShowCash(0);
        bindupAccountsSaveDTO.setIsMultiHandover(0);
        bindupAccountsSaveDTO.setBrandGuid("eb85852e-3c79-4312-9c64-628e27544f73");

        when(mockBrandService.updateBrandAccountStatus(any(BrandDTO.class))).thenReturn(true);

        // Configure StoreBrandMapper.selectList(...).
        final StoreBrandDO storeBrandDO = new StoreBrandDO();
        storeBrandDO.setId(0L);
        storeBrandDO.setGuid("07c02081-ef10-4f32-bc79-55e724f415bc");
        storeBrandDO.setStoreGuid("storeGuid");
        storeBrandDO.setBrandGuid("belongBrandGuid");
        storeBrandDO.setModifiedUserGuid("modifiedUserGuid");
        final List<StoreBrandDO> storeBrandDOS = Arrays.asList(storeBrandDO);
        when(mockStoreBrandMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(storeBrandDOS);

        when(mockBrandMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure OrganizationMapper.selectList(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOS = Arrays.asList(organizationDO);
        when(mockOrganizationMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(organizationDOS);

        // Configure StoreMapstruct.organizationList2DTOList(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        brandDTO.setStoreList(Arrays.asList(new StoreDTO()));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO);
        final OrganizationDO organizationDO1 = new OrganizationDO();
        organizationDO1.setGuid("guid");
        organizationDO1.setCode("code");
        organizationDO1.setType(0);
        organizationDO1.setName("name");
        organizationDO1.setParentIds("parentIds");
        organizationDO1.setProvinceCode("provinceCode");
        organizationDO1.setProvinceName("provinceName");
        organizationDO1.setCityCode("cityCode");
        organizationDO1.setCityName("cityName");
        organizationDO1.setIsEnable(false);
        organizationDO1.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO1.setModifiedUserGuid("modifiedUserGuid");
        organizationDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setIsSelfBuildItems(0);
        organizationDO1.setIsBuAccounts(0);
        organizationDO1.setIsShowCash(0);
        final List<OrganizationDO> organizationDOList = Arrays.asList(organizationDO1);
        when(mockStoreMapstruct.organizationList2DTOList(organizationDOList)).thenReturn(storeDTOS);

        // Run the test
        storeServiceImplUnderTest.updateBuAccounts(bindupAccountsSaveDTO);

        // Verify the results
        verify(mockOrganizationMapper).batchUpdateBuAccounts(eq(Arrays.asList("value")), any(StoreDTO.class));

        // Confirm ConfigFeignService.saveEstimateResetTime(...).
        final ConfigReqDTO configReqDTO = new ConfigReqDTO();
        configReqDTO.setGuid("3560026c-36ee-4f9c-9c45-71f43e620625");
        configReqDTO.setEnterpriseGuid("enterpriseGuid");
        configReqDTO.setStoreGuid("guid");
        configReqDTO.setDicCode(0);
        configReqDTO.setDicName("desc");
        configReqDTO.setDictValue("dictValue");
        configReqDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        configReqDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        configReqDTO.setIsEnable(0);
        verify(mockConfigFeignService).saveEstimateResetTime(configReqDTO);
    }

    @Test
    public void testUpdateBuAccounts_OrganizationMapperSelectListReturnsNoItems() {
        // Setup
        final BindupAccountsSaveDTO bindupAccountsSaveDTO = new BindupAccountsSaveDTO();
        bindupAccountsSaveDTO.setIsBuAccounts(0);
        bindupAccountsSaveDTO.setIsShowCash(0);
        bindupAccountsSaveDTO.setIsMultiHandover(0);
        bindupAccountsSaveDTO.setBrandGuid("eb85852e-3c79-4312-9c64-628e27544f73");

        when(mockBrandService.updateBrandAccountStatus(any(BrandDTO.class))).thenReturn(true);

        // Configure StoreBrandMapper.selectList(...).
        final StoreBrandDO storeBrandDO = new StoreBrandDO();
        storeBrandDO.setId(0L);
        storeBrandDO.setGuid("07c02081-ef10-4f32-bc79-55e724f415bc");
        storeBrandDO.setStoreGuid("storeGuid");
        storeBrandDO.setBrandGuid("belongBrandGuid");
        storeBrandDO.setModifiedUserGuid("modifiedUserGuid");
        final List<StoreBrandDO> storeBrandDOS = Arrays.asList(storeBrandDO);
        when(mockStoreBrandMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(storeBrandDOS);

        // Configure BrandMapper.selectList(...).
        final BrandDO brandDO = new BrandDO();
        brandDO.setId(0L);
        brandDO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDO.setName("belongBrandName");
        brandDO.setDescription("description");
        brandDO.setIsEnable(false);
        final List<BrandDO> brandDOS = Arrays.asList(brandDO);
        when(mockBrandMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(brandDOS);

        when(mockOrganizationMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure StoreMapstruct.organizationList2DTOList(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        brandDTO.setStoreList(Arrays.asList(new StoreDTO()));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO);
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOList = Arrays.asList(organizationDO);
        when(mockStoreMapstruct.organizationList2DTOList(organizationDOList)).thenReturn(storeDTOS);

        // Run the test
        storeServiceImplUnderTest.updateBuAccounts(bindupAccountsSaveDTO);

        // Verify the results
        verify(mockOrganizationMapper).batchUpdateBuAccounts(eq(Arrays.asList("value")), any(StoreDTO.class));

        // Confirm ConfigFeignService.saveEstimateResetTime(...).
        final ConfigReqDTO configReqDTO = new ConfigReqDTO();
        configReqDTO.setGuid("3560026c-36ee-4f9c-9c45-71f43e620625");
        configReqDTO.setEnterpriseGuid("enterpriseGuid");
        configReqDTO.setStoreGuid("guid");
        configReqDTO.setDicCode(0);
        configReqDTO.setDicName("desc");
        configReqDTO.setDictValue("dictValue");
        configReqDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        configReqDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        configReqDTO.setIsEnable(0);
        verify(mockConfigFeignService).saveEstimateResetTime(configReqDTO);
    }

    @Test
    public void testUpdateBuAccounts_StoreMapstructReturnsNoItems() {
        // Setup
        final BindupAccountsSaveDTO bindupAccountsSaveDTO = new BindupAccountsSaveDTO();
        bindupAccountsSaveDTO.setIsBuAccounts(0);
        bindupAccountsSaveDTO.setIsShowCash(0);
        bindupAccountsSaveDTO.setIsMultiHandover(0);
        bindupAccountsSaveDTO.setBrandGuid("eb85852e-3c79-4312-9c64-628e27544f73");

        when(mockBrandService.updateBrandAccountStatus(any(BrandDTO.class))).thenReturn(true);

        // Configure StoreBrandMapper.selectList(...).
        final StoreBrandDO storeBrandDO = new StoreBrandDO();
        storeBrandDO.setId(0L);
        storeBrandDO.setGuid("07c02081-ef10-4f32-bc79-55e724f415bc");
        storeBrandDO.setStoreGuid("storeGuid");
        storeBrandDO.setBrandGuid("belongBrandGuid");
        storeBrandDO.setModifiedUserGuid("modifiedUserGuid");
        final List<StoreBrandDO> storeBrandDOS = Arrays.asList(storeBrandDO);
        when(mockStoreBrandMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(storeBrandDOS);

        // Configure BrandMapper.selectList(...).
        final BrandDO brandDO = new BrandDO();
        brandDO.setId(0L);
        brandDO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDO.setName("belongBrandName");
        brandDO.setDescription("description");
        brandDO.setIsEnable(false);
        final List<BrandDO> brandDOS = Arrays.asList(brandDO);
        when(mockBrandMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(brandDOS);

        // Configure OrganizationMapper.selectList(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOS = Arrays.asList(organizationDO);
        when(mockOrganizationMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(organizationDOS);

        // Configure StoreMapstruct.organizationList2DTOList(...).
        final OrganizationDO organizationDO1 = new OrganizationDO();
        organizationDO1.setGuid("guid");
        organizationDO1.setCode("code");
        organizationDO1.setType(0);
        organizationDO1.setName("name");
        organizationDO1.setParentIds("parentIds");
        organizationDO1.setProvinceCode("provinceCode");
        organizationDO1.setProvinceName("provinceName");
        organizationDO1.setCityCode("cityCode");
        organizationDO1.setCityName("cityName");
        organizationDO1.setIsEnable(false);
        organizationDO1.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO1.setModifiedUserGuid("modifiedUserGuid");
        organizationDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setIsSelfBuildItems(0);
        organizationDO1.setIsBuAccounts(0);
        organizationDO1.setIsShowCash(0);
        final List<OrganizationDO> organizationDOList = Arrays.asList(organizationDO1);
        when(mockStoreMapstruct.organizationList2DTOList(organizationDOList)).thenReturn(Collections.emptyList());

        // Run the test
        storeServiceImplUnderTest.updateBuAccounts(bindupAccountsSaveDTO);

        // Verify the results
    }

    @Test
    public void testEnableStore() throws Exception {
        // Setup
        // Configure StoreMapstruct.organizationDO2DTO(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        brandDTO.setStoreList(Arrays.asList(new StoreDTO()));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        when(mockStoreMapstruct.organizationDO2DTO(organizationDO, "belongBrandGuid")).thenReturn(storeDTO);

        // Run the test
        final boolean result = storeServiceImplUnderTest.enableStore("storeGuid");

        // Verify the results
        assertTrue(result);

        // Confirm BroadcastService.enableStoreInCloud(...).
        final OrganizationDO organizationDO1 = new OrganizationDO();
        organizationDO1.setGuid("guid");
        organizationDO1.setCode("code");
        organizationDO1.setType(0);
        organizationDO1.setName("name");
        organizationDO1.setParentIds("parentIds");
        organizationDO1.setProvinceCode("provinceCode");
        organizationDO1.setProvinceName("provinceName");
        organizationDO1.setCityCode("cityCode");
        organizationDO1.setCityName("cityName");
        organizationDO1.setIsEnable(false);
        organizationDO1.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO1.setModifiedUserGuid("modifiedUserGuid");
        organizationDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setIsSelfBuildItems(0);
        organizationDO1.setIsBuAccounts(0);
        organizationDO1.setIsShowCash(0);
        verify(mockBroadcastService).enableStoreInCloud(organizationDO1);
        verify(mockBroadcastService).updateMemberStore(any(StoreDTO.class));
    }

    @Test
    public void testQueryByCondition() throws Exception {
        // Setup
        final QueryStoreDTO queryStoreDTO = new QueryStoreDTO();
        queryStoreDTO.setCurrentPage(0L);
        queryStoreDTO.setPageSize(0L);
        queryStoreDTO.setTotalCount(0L);
        queryStoreDTO.setStoreGuidList(Arrays.asList("value"));
        queryStoreDTO.setIsEnable(0);

        // Configure OrganizationMapper.selectByCondition(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOS = Arrays.asList(organizationDO);
        final QueryStoreDTO storeParserDTO = new QueryStoreDTO();
        storeParserDTO.setCurrentPage(0L);
        storeParserDTO.setPageSize(0L);
        storeParserDTO.setTotalCount(0L);
        storeParserDTO.setStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setIsEnable(0);
        when(mockOrganizationMapper.selectByCondition(storeParserDTO)).thenReturn(organizationDOS);

        // Configure StoreBrandMapper.selectList(...).
        final StoreBrandDO storeBrandDO = new StoreBrandDO();
        storeBrandDO.setId(0L);
        storeBrandDO.setGuid("07c02081-ef10-4f32-bc79-55e724f415bc");
        storeBrandDO.setStoreGuid("storeGuid");
        storeBrandDO.setBrandGuid("belongBrandGuid");
        storeBrandDO.setModifiedUserGuid("modifiedUserGuid");
        final List<StoreBrandDO> storeBrandDOS = Arrays.asList(storeBrandDO);
        when(mockStoreBrandMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(storeBrandDOS);

        // Configure StoreMapstruct.organizationDO2DTO(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        brandDTO.setStoreList(Arrays.asList(new StoreDTO()));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));
        final OrganizationDO organizationDO1 = new OrganizationDO();
        organizationDO1.setGuid("guid");
        organizationDO1.setCode("code");
        organizationDO1.setType(0);
        organizationDO1.setName("name");
        organizationDO1.setParentIds("parentIds");
        organizationDO1.setProvinceCode("provinceCode");
        organizationDO1.setProvinceName("provinceName");
        organizationDO1.setCityCode("cityCode");
        organizationDO1.setCityName("cityName");
        organizationDO1.setIsEnable(false);
        organizationDO1.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO1.setModifiedUserGuid("modifiedUserGuid");
        organizationDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setIsSelfBuildItems(0);
        organizationDO1.setIsBuAccounts(0);
        organizationDO1.setIsShowCash(0);
        when(mockStoreMapstruct.organizationDO2DTO(organizationDO1, "belongBrandGuid")).thenReturn(storeDTO);

        when(mockUserClient.queryProductByIdList(Arrays.asList("value"))).thenReturn(new HashMap<>());

        // Run the test
        final Page<StoreDTO> result = storeServiceImplUnderTest.queryByCondition(queryStoreDTO);

        // Verify the results
    }

    @Test
    public void testQueryByCondition_OrganizationMapperReturnsNoItems() {
        // Setup
        final QueryStoreDTO queryStoreDTO = new QueryStoreDTO();
        queryStoreDTO.setCurrentPage(0L);
        queryStoreDTO.setPageSize(0L);
        queryStoreDTO.setTotalCount(0L);
        queryStoreDTO.setStoreGuidList(Arrays.asList("value"));
        queryStoreDTO.setIsEnable(0);

        // Configure OrganizationMapper.selectByCondition(...).
        final QueryStoreDTO storeParserDTO = new QueryStoreDTO();
        storeParserDTO.setCurrentPage(0L);
        storeParserDTO.setPageSize(0L);
        storeParserDTO.setTotalCount(0L);
        storeParserDTO.setStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setIsEnable(0);
        when(mockOrganizationMapper.selectByCondition(storeParserDTO)).thenReturn(Collections.emptyList());

        // Run the test
        final Page<StoreDTO> result = storeServiceImplUnderTest.queryByCondition(queryStoreDTO);

        // Verify the results
    }

    @Test
    public void testQueryByCondition_StoreBrandMapperReturnsNoItems() {
        // Setup
        final QueryStoreDTO queryStoreDTO = new QueryStoreDTO();
        queryStoreDTO.setCurrentPage(0L);
        queryStoreDTO.setPageSize(0L);
        queryStoreDTO.setTotalCount(0L);
        queryStoreDTO.setStoreGuidList(Arrays.asList("value"));
        queryStoreDTO.setIsEnable(0);

        // Configure OrganizationMapper.selectByCondition(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOS = Arrays.asList(organizationDO);
        final QueryStoreDTO storeParserDTO = new QueryStoreDTO();
        storeParserDTO.setCurrentPage(0L);
        storeParserDTO.setPageSize(0L);
        storeParserDTO.setTotalCount(0L);
        storeParserDTO.setStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setIsEnable(0);
        when(mockOrganizationMapper.selectByCondition(storeParserDTO)).thenReturn(organizationDOS);

        when(mockStoreBrandMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure StoreMapstruct.organizationDO2DTO(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        brandDTO.setStoreList(Arrays.asList(new StoreDTO()));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));
        final OrganizationDO organizationDO1 = new OrganizationDO();
        organizationDO1.setGuid("guid");
        organizationDO1.setCode("code");
        organizationDO1.setType(0);
        organizationDO1.setName("name");
        organizationDO1.setParentIds("parentIds");
        organizationDO1.setProvinceCode("provinceCode");
        organizationDO1.setProvinceName("provinceName");
        organizationDO1.setCityCode("cityCode");
        organizationDO1.setCityName("cityName");
        organizationDO1.setIsEnable(false);
        organizationDO1.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO1.setModifiedUserGuid("modifiedUserGuid");
        organizationDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setIsSelfBuildItems(0);
        organizationDO1.setIsBuAccounts(0);
        organizationDO1.setIsShowCash(0);
        when(mockStoreMapstruct.organizationDO2DTO(organizationDO1, "belongBrandGuid")).thenReturn(storeDTO);

        when(mockUserClient.queryProductByIdList(Arrays.asList("value"))).thenReturn(new HashMap<>());

        // Run the test
        final Page<StoreDTO> result = storeServiceImplUnderTest.queryByCondition(queryStoreDTO);

        // Verify the results
    }

    @Test
    public void testListStoreByCondition() throws Exception {
        // Setup
        final QueryStoreDTO queryStoreDTO = new QueryStoreDTO();
        queryStoreDTO.setCurrentPage(0L);
        queryStoreDTO.setPageSize(0L);
        queryStoreDTO.setTotalCount(0L);
        queryStoreDTO.setStoreGuidList(Arrays.asList("value"));
        queryStoreDTO.setIsEnable(0);

        // Configure OrganizationMapper.selectByCondition(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOS = Arrays.asList(organizationDO);
        final QueryStoreDTO storeParserDTO = new QueryStoreDTO();
        storeParserDTO.setCurrentPage(0L);
        storeParserDTO.setPageSize(0L);
        storeParserDTO.setTotalCount(0L);
        storeParserDTO.setStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setIsEnable(0);
        when(mockOrganizationMapper.selectByCondition(storeParserDTO)).thenReturn(organizationDOS);

        // Configure StoreMapstruct.organizationList2DTOList(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        brandDTO.setStoreList(Arrays.asList(new StoreDTO()));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO);
        final OrganizationDO organizationDO1 = new OrganizationDO();
        organizationDO1.setGuid("guid");
        organizationDO1.setCode("code");
        organizationDO1.setType(0);
        organizationDO1.setName("name");
        organizationDO1.setParentIds("parentIds");
        organizationDO1.setProvinceCode("provinceCode");
        organizationDO1.setProvinceName("provinceName");
        organizationDO1.setCityCode("cityCode");
        organizationDO1.setCityName("cityName");
        organizationDO1.setIsEnable(false);
        organizationDO1.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO1.setModifiedUserGuid("modifiedUserGuid");
        organizationDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setIsSelfBuildItems(0);
        organizationDO1.setIsBuAccounts(0);
        organizationDO1.setIsShowCash(0);
        final List<OrganizationDO> organizationDOList = Arrays.asList(organizationDO1);
        when(mockStoreMapstruct.organizationList2DTOList(organizationDOList)).thenReturn(storeDTOS);

        // Run the test
        final List<StoreDTO> result = storeServiceImplUnderTest.listStoreByCondition(queryStoreDTO);

        // Verify the results
    }

    @Test
    public void testListStoreByCondition_OrganizationMapperReturnsNoItems() {
        // Setup
        final QueryStoreDTO queryStoreDTO = new QueryStoreDTO();
        queryStoreDTO.setCurrentPage(0L);
        queryStoreDTO.setPageSize(0L);
        queryStoreDTO.setTotalCount(0L);
        queryStoreDTO.setStoreGuidList(Arrays.asList("value"));
        queryStoreDTO.setIsEnable(0);

        // Configure OrganizationMapper.selectByCondition(...).
        final QueryStoreDTO storeParserDTO = new QueryStoreDTO();
        storeParserDTO.setCurrentPage(0L);
        storeParserDTO.setPageSize(0L);
        storeParserDTO.setTotalCount(0L);
        storeParserDTO.setStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setIsEnable(0);
        when(mockOrganizationMapper.selectByCondition(storeParserDTO)).thenReturn(Collections.emptyList());

        // Configure StoreMapstruct.organizationList2DTOList(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        brandDTO.setStoreList(Arrays.asList(new StoreDTO()));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO);
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOList = Arrays.asList(organizationDO);
        when(mockStoreMapstruct.organizationList2DTOList(organizationDOList)).thenReturn(storeDTOS);

        // Run the test
        final List<StoreDTO> result = storeServiceImplUnderTest.listStoreByCondition(queryStoreDTO);

        // Verify the results
    }

    @Test
    public void testListStoreByCondition_StoreMapstructReturnsNoItems() {
        // Setup
        final QueryStoreDTO queryStoreDTO = new QueryStoreDTO();
        queryStoreDTO.setCurrentPage(0L);
        queryStoreDTO.setPageSize(0L);
        queryStoreDTO.setTotalCount(0L);
        queryStoreDTO.setStoreGuidList(Arrays.asList("value"));
        queryStoreDTO.setIsEnable(0);

        // Configure OrganizationMapper.selectByCondition(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOS = Arrays.asList(organizationDO);
        final QueryStoreDTO storeParserDTO = new QueryStoreDTO();
        storeParserDTO.setCurrentPage(0L);
        storeParserDTO.setPageSize(0L);
        storeParserDTO.setTotalCount(0L);
        storeParserDTO.setStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setIsEnable(0);
        when(mockOrganizationMapper.selectByCondition(storeParserDTO)).thenReturn(organizationDOS);

        // Configure StoreMapstruct.organizationList2DTOList(...).
        final OrganizationDO organizationDO1 = new OrganizationDO();
        organizationDO1.setGuid("guid");
        organizationDO1.setCode("code");
        organizationDO1.setType(0);
        organizationDO1.setName("name");
        organizationDO1.setParentIds("parentIds");
        organizationDO1.setProvinceCode("provinceCode");
        organizationDO1.setProvinceName("provinceName");
        organizationDO1.setCityCode("cityCode");
        organizationDO1.setCityName("cityName");
        organizationDO1.setIsEnable(false);
        organizationDO1.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO1.setModifiedUserGuid("modifiedUserGuid");
        organizationDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setIsSelfBuildItems(0);
        organizationDO1.setIsBuAccounts(0);
        organizationDO1.setIsShowCash(0);
        final List<OrganizationDO> organizationDOList = Arrays.asList(organizationDO1);
        when(mockStoreMapstruct.organizationList2DTOList(organizationDOList)).thenReturn(Collections.emptyList());

        // Run the test
        final List<StoreDTO> result = storeServiceImplUnderTest.listStoreByCondition(queryStoreDTO);

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testQueryByConditionNoPage() throws Exception {
        // Setup
        final StoreParserDTO storeParserDTO = new StoreParserDTO();
        storeParserDTO.setAllStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setRegionCodeList(Arrays.asList("value"));
        storeParserDTO.setBrandGuidList(Arrays.asList("value"));
        storeParserDTO.setOrganizationGuidList(Arrays.asList("value"));

        // Configure OrganizationMapper.selectByConditionNoPage(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOS = Arrays.asList(organizationDO);
        final StoreParserDTO storeParserDTO1 = new StoreParserDTO();
        storeParserDTO1.setAllStoreGuidList(Arrays.asList("value"));
        storeParserDTO1.setStoreGuidList(Arrays.asList("value"));
        storeParserDTO1.setRegionCodeList(Arrays.asList("value"));
        storeParserDTO1.setBrandGuidList(Arrays.asList("value"));
        storeParserDTO1.setOrganizationGuidList(Arrays.asList("value"));
        when(mockOrganizationMapper.selectByConditionNoPage(storeParserDTO1)).thenReturn(organizationDOS);

        // Configure StoreBrandMapper.selectList(...).
        final StoreBrandDO storeBrandDO = new StoreBrandDO();
        storeBrandDO.setId(0L);
        storeBrandDO.setGuid("07c02081-ef10-4f32-bc79-55e724f415bc");
        storeBrandDO.setStoreGuid("storeGuid");
        storeBrandDO.setBrandGuid("belongBrandGuid");
        storeBrandDO.setModifiedUserGuid("modifiedUserGuid");
        final List<StoreBrandDO> storeBrandDOS = Arrays.asList(storeBrandDO);
        when(mockStoreBrandMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(storeBrandDOS);

        // Configure StoreMapstruct.organizationDO2DTO(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        brandDTO.setStoreList(Arrays.asList(new StoreDTO()));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));
        final OrganizationDO organizationDO1 = new OrganizationDO();
        organizationDO1.setGuid("guid");
        organizationDO1.setCode("code");
        organizationDO1.setType(0);
        organizationDO1.setName("name");
        organizationDO1.setParentIds("parentIds");
        organizationDO1.setProvinceCode("provinceCode");
        organizationDO1.setProvinceName("provinceName");
        organizationDO1.setCityCode("cityCode");
        organizationDO1.setCityName("cityName");
        organizationDO1.setIsEnable(false);
        organizationDO1.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO1.setModifiedUserGuid("modifiedUserGuid");
        organizationDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setIsSelfBuildItems(0);
        organizationDO1.setIsBuAccounts(0);
        organizationDO1.setIsShowCash(0);
        when(mockStoreMapstruct.organizationDO2DTO(organizationDO1, "belongBrandGuid")).thenReturn(storeDTO);

        when(mockUserClient.queryProductByIdList(Arrays.asList("value"))).thenReturn(new HashMap<>());

        // Run the test
        final List<StoreDTO> result = storeServiceImplUnderTest.queryByConditionNoPage(storeParserDTO);

        // Verify the results
    }

    @Test
    public void testQueryByConditionNoPage_OrganizationMapperReturnsNoItems() {
        // Setup
        final StoreParserDTO storeParserDTO = new StoreParserDTO();
        storeParserDTO.setAllStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setRegionCodeList(Arrays.asList("value"));
        storeParserDTO.setBrandGuidList(Arrays.asList("value"));
        storeParserDTO.setOrganizationGuidList(Arrays.asList("value"));

        // Configure OrganizationMapper.selectByConditionNoPage(...).
        final StoreParserDTO storeParserDTO1 = new StoreParserDTO();
        storeParserDTO1.setAllStoreGuidList(Arrays.asList("value"));
        storeParserDTO1.setStoreGuidList(Arrays.asList("value"));
        storeParserDTO1.setRegionCodeList(Arrays.asList("value"));
        storeParserDTO1.setBrandGuidList(Arrays.asList("value"));
        storeParserDTO1.setOrganizationGuidList(Arrays.asList("value"));
        when(mockOrganizationMapper.selectByConditionNoPage(storeParserDTO1)).thenReturn(Collections.emptyList());

        // Run the test
        final List<StoreDTO> result = storeServiceImplUnderTest.queryByConditionNoPage(storeParserDTO);

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testQueryByConditionNoPage_StoreBrandMapperReturnsNoItems() {
        // Setup
        final StoreParserDTO storeParserDTO = new StoreParserDTO();
        storeParserDTO.setAllStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setRegionCodeList(Arrays.asList("value"));
        storeParserDTO.setBrandGuidList(Arrays.asList("value"));
        storeParserDTO.setOrganizationGuidList(Arrays.asList("value"));

        // Configure OrganizationMapper.selectByConditionNoPage(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOS = Arrays.asList(organizationDO);
        final StoreParserDTO storeParserDTO1 = new StoreParserDTO();
        storeParserDTO1.setAllStoreGuidList(Arrays.asList("value"));
        storeParserDTO1.setStoreGuidList(Arrays.asList("value"));
        storeParserDTO1.setRegionCodeList(Arrays.asList("value"));
        storeParserDTO1.setBrandGuidList(Arrays.asList("value"));
        storeParserDTO1.setOrganizationGuidList(Arrays.asList("value"));
        when(mockOrganizationMapper.selectByConditionNoPage(storeParserDTO1)).thenReturn(organizationDOS);

        when(mockStoreBrandMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure StoreMapstruct.organizationDO2DTO(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        brandDTO.setStoreList(Arrays.asList(new StoreDTO()));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));
        final OrganizationDO organizationDO1 = new OrganizationDO();
        organizationDO1.setGuid("guid");
        organizationDO1.setCode("code");
        organizationDO1.setType(0);
        organizationDO1.setName("name");
        organizationDO1.setParentIds("parentIds");
        organizationDO1.setProvinceCode("provinceCode");
        organizationDO1.setProvinceName("provinceName");
        organizationDO1.setCityCode("cityCode");
        organizationDO1.setCityName("cityName");
        organizationDO1.setIsEnable(false);
        organizationDO1.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO1.setModifiedUserGuid("modifiedUserGuid");
        organizationDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setIsSelfBuildItems(0);
        organizationDO1.setIsBuAccounts(0);
        organizationDO1.setIsShowCash(0);
        when(mockStoreMapstruct.organizationDO2DTO(organizationDO1, "belongBrandGuid")).thenReturn(storeDTO);

        when(mockUserClient.queryProductByIdList(Arrays.asList("value"))).thenReturn(new HashMap<>());

        // Run the test
        final List<StoreDTO> result = storeServiceImplUnderTest.queryByConditionNoPage(storeParserDTO);

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testList() throws Exception {
        // Setup
        final StoreListReq dto = new StoreListReq();
        dto.setStoreGuidList(Arrays.asList("value"));
        dto.setRegionCodeList(Arrays.asList("value"));
        dto.setBrandGuidList(Arrays.asList("value"));

        // Configure OrganizationMapper.selectByConditionNoPage(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOS = Arrays.asList(organizationDO);
        final StoreParserDTO storeParserDTO = new StoreParserDTO();
        storeParserDTO.setAllStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setRegionCodeList(Arrays.asList("value"));
        storeParserDTO.setBrandGuidList(Arrays.asList("value"));
        storeParserDTO.setOrganizationGuidList(Arrays.asList("value"));
        when(mockOrganizationMapper.selectByConditionNoPage(storeParserDTO)).thenReturn(organizationDOS);

        // Configure StoreMapstruct.organizationList2DTOList(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        brandDTO.setStoreList(Arrays.asList(new StoreDTO()));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO);
        final OrganizationDO organizationDO1 = new OrganizationDO();
        organizationDO1.setGuid("guid");
        organizationDO1.setCode("code");
        organizationDO1.setType(0);
        organizationDO1.setName("name");
        organizationDO1.setParentIds("parentIds");
        organizationDO1.setProvinceCode("provinceCode");
        organizationDO1.setProvinceName("provinceName");
        organizationDO1.setCityCode("cityCode");
        organizationDO1.setCityName("cityName");
        organizationDO1.setIsEnable(false);
        organizationDO1.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO1.setModifiedUserGuid("modifiedUserGuid");
        organizationDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setIsSelfBuildItems(0);
        organizationDO1.setIsBuAccounts(0);
        organizationDO1.setIsShowCash(0);
        final List<OrganizationDO> organizationDOList = Arrays.asList(organizationDO1);
        when(mockStoreMapstruct.organizationList2DTOList(organizationDOList)).thenReturn(storeDTOS);

        // Run the test
        final List<StoreDTO> result = storeServiceImplUnderTest.list(dto);

        // Verify the results
    }

    @Test
    public void testList_OrganizationMapperReturnsNoItems() {
        // Setup
        final StoreListReq dto = new StoreListReq();
        dto.setStoreGuidList(Arrays.asList("value"));
        dto.setRegionCodeList(Arrays.asList("value"));
        dto.setBrandGuidList(Arrays.asList("value"));

        // Configure OrganizationMapper.selectByConditionNoPage(...).
        final StoreParserDTO storeParserDTO = new StoreParserDTO();
        storeParserDTO.setAllStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setRegionCodeList(Arrays.asList("value"));
        storeParserDTO.setBrandGuidList(Arrays.asList("value"));
        storeParserDTO.setOrganizationGuidList(Arrays.asList("value"));
        when(mockOrganizationMapper.selectByConditionNoPage(storeParserDTO)).thenReturn(Collections.emptyList());

        // Configure StoreMapstruct.organizationList2DTOList(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        brandDTO.setStoreList(Arrays.asList(new StoreDTO()));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO);
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOList = Arrays.asList(organizationDO);
        when(mockStoreMapstruct.organizationList2DTOList(organizationDOList)).thenReturn(storeDTOS);

        // Run the test
        final List<StoreDTO> result = storeServiceImplUnderTest.list(dto);

        // Verify the results
    }

    @Test
    public void testList_StoreMapstructReturnsNoItems() {
        // Setup
        final StoreListReq dto = new StoreListReq();
        dto.setStoreGuidList(Arrays.asList("value"));
        dto.setRegionCodeList(Arrays.asList("value"));
        dto.setBrandGuidList(Arrays.asList("value"));

        // Configure OrganizationMapper.selectByConditionNoPage(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOS = Arrays.asList(organizationDO);
        final StoreParserDTO storeParserDTO = new StoreParserDTO();
        storeParserDTO.setAllStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setRegionCodeList(Arrays.asList("value"));
        storeParserDTO.setBrandGuidList(Arrays.asList("value"));
        storeParserDTO.setOrganizationGuidList(Arrays.asList("value"));
        when(mockOrganizationMapper.selectByConditionNoPage(storeParserDTO)).thenReturn(organizationDOS);

        // Configure StoreMapstruct.organizationList2DTOList(...).
        final OrganizationDO organizationDO1 = new OrganizationDO();
        organizationDO1.setGuid("guid");
        organizationDO1.setCode("code");
        organizationDO1.setType(0);
        organizationDO1.setName("name");
        organizationDO1.setParentIds("parentIds");
        organizationDO1.setProvinceCode("provinceCode");
        organizationDO1.setProvinceName("provinceName");
        organizationDO1.setCityCode("cityCode");
        organizationDO1.setCityName("cityName");
        organizationDO1.setIsEnable(false);
        organizationDO1.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO1.setModifiedUserGuid("modifiedUserGuid");
        organizationDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setIsSelfBuildItems(0);
        organizationDO1.setIsBuAccounts(0);
        organizationDO1.setIsShowCash(0);
        final List<OrganizationDO> organizationDOList = Arrays.asList(organizationDO1);
        when(mockStoreMapstruct.organizationList2DTOList(organizationDOList)).thenReturn(Collections.emptyList());

        // Run the test
        final List<StoreDTO> result = storeServiceImplUnderTest.list(dto);

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testParseByCondition() throws Exception {
        // Setup
        final StoreParserDTO storeParserDTO = new StoreParserDTO();
        storeParserDTO.setAllStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setRegionCodeList(Arrays.asList("value"));
        storeParserDTO.setBrandGuidList(Arrays.asList("value"));
        storeParserDTO.setOrganizationGuidList(Arrays.asList("value"));

        // Configure OrganizationMapper.parseByCondition(...).
        final StoreParserDTO storeParserDTO1 = new StoreParserDTO();
        storeParserDTO1.setAllStoreGuidList(Arrays.asList("value"));
        storeParserDTO1.setStoreGuidList(Arrays.asList("value"));
        storeParserDTO1.setRegionCodeList(Arrays.asList("value"));
        storeParserDTO1.setBrandGuidList(Arrays.asList("value"));
        storeParserDTO1.setOrganizationGuidList(Arrays.asList("value"));
        when(mockOrganizationMapper.parseByCondition(storeParserDTO1)).thenReturn(Arrays.asList("value"));

        // Run the test
        final List<String> result = storeServiceImplUnderTest.parseByCondition(storeParserDTO);

        // Verify the results
        assertEquals(Arrays.asList("value"), result);
    }

    @Test
    public void testParseByCondition_OrganizationMapperReturnsNoItems() {
        // Setup
        final StoreParserDTO storeParserDTO = new StoreParserDTO();
        storeParserDTO.setAllStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setRegionCodeList(Arrays.asList("value"));
        storeParserDTO.setBrandGuidList(Arrays.asList("value"));
        storeParserDTO.setOrganizationGuidList(Arrays.asList("value"));

        // Configure OrganizationMapper.parseByCondition(...).
        final StoreParserDTO storeParserDTO1 = new StoreParserDTO();
        storeParserDTO1.setAllStoreGuidList(Arrays.asList("value"));
        storeParserDTO1.setStoreGuidList(Arrays.asList("value"));
        storeParserDTO1.setRegionCodeList(Arrays.asList("value"));
        storeParserDTO1.setBrandGuidList(Arrays.asList("value"));
        storeParserDTO1.setOrganizationGuidList(Arrays.asList("value"));
        when(mockOrganizationMapper.parseByCondition(storeParserDTO1)).thenReturn(Collections.emptyList());

        // Run the test
        final List<String> result = storeServiceImplUnderTest.parseByCondition(storeParserDTO);

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testParseByConditionNotUnion() throws Exception {
        // Setup
        final StoreParserDTO storeParserDTO = new StoreParserDTO();
        storeParserDTO.setAllStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setRegionCodeList(Arrays.asList("value"));
        storeParserDTO.setBrandGuidList(Arrays.asList("value"));
        storeParserDTO.setOrganizationGuidList(Arrays.asList("value"));

        // Configure OrganizationMapper.parseByConditionNotUnion(...).
        final StoreParserDTO storeParserDTO1 = new StoreParserDTO();
        storeParserDTO1.setAllStoreGuidList(Arrays.asList("value"));
        storeParserDTO1.setStoreGuidList(Arrays.asList("value"));
        storeParserDTO1.setRegionCodeList(Arrays.asList("value"));
        storeParserDTO1.setBrandGuidList(Arrays.asList("value"));
        storeParserDTO1.setOrganizationGuidList(Arrays.asList("value"));
        when(mockOrganizationMapper.parseByConditionNotUnion(storeParserDTO1)).thenReturn(Arrays.asList("value"));

        // Run the test
        final List<String> result = storeServiceImplUnderTest.parseByConditionNotUnion(storeParserDTO);

        // Verify the results
        assertEquals(Arrays.asList("value"), result);
    }

    @Test
    public void testParseByConditionNotUnion_OrganizationMapperReturnsNoItems() {
        // Setup
        final StoreParserDTO storeParserDTO = new StoreParserDTO();
        storeParserDTO.setAllStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setRegionCodeList(Arrays.asList("value"));
        storeParserDTO.setBrandGuidList(Arrays.asList("value"));
        storeParserDTO.setOrganizationGuidList(Arrays.asList("value"));

        // Configure OrganizationMapper.parseByConditionNotUnion(...).
        final StoreParserDTO storeParserDTO1 = new StoreParserDTO();
        storeParserDTO1.setAllStoreGuidList(Arrays.asList("value"));
        storeParserDTO1.setStoreGuidList(Arrays.asList("value"));
        storeParserDTO1.setRegionCodeList(Arrays.asList("value"));
        storeParserDTO1.setBrandGuidList(Arrays.asList("value"));
        storeParserDTO1.setOrganizationGuidList(Arrays.asList("value"));
        when(mockOrganizationMapper.parseByConditionNotUnion(storeParserDTO1)).thenReturn(Collections.emptyList());

        // Run the test
        final List<String> result = storeServiceImplUnderTest.parseByConditionNotUnion(storeParserDTO);

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testDeleteStore() throws Exception {
        // Setup
        // Configure UserClient.queryProductByStoreGuid(...).
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setProductGuid("productGuid");
        storeProductDTO.setProductName("productName");
        storeProductDTO.setProductType(0);
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<StoreProductDTO> storeProductDTOS = Arrays.asList(storeProductDTO);
        when(mockUserClient.queryProductByStoreGuid("storeGuid", false)).thenReturn(storeProductDTOS);

        // Run the test
        final boolean result = storeServiceImplUnderTest.deleteStore("storeGuid");

        // Verify the results
        assertTrue(result);
        verify(mockStoreBrandMapper).delete(any(LambdaQueryWrapper.class));
        verify(mockBroadcastService).deleteStoreInCloud("storeGuid");
    }

    @Test
    public void testDeleteStore_UserClientReturnsNoItems() {
        // Setup
        when(mockUserClient.queryProductByStoreGuid("storeGuid", false)).thenReturn(Collections.emptyList());

        // Run the test
        final boolean result = storeServiceImplUnderTest.deleteStore("storeGuid");

        // Verify the results
        assertTrue(result);
        verify(mockStoreBrandMapper).delete(any(LambdaQueryWrapper.class));
        verify(mockBroadcastService).deleteStoreInCloud("storeGuid");
    }

    @Test
    public void testQueryStoreByGuid() throws Exception {
        // Setup
        // Configure OrganizationMapper.selectOne(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        when(mockOrganizationMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(organizationDO);

        // Configure StoreBrandMapper.selectOne(...).
        final StoreBrandDO storeBrandDO = new StoreBrandDO();
        storeBrandDO.setId(0L);
        storeBrandDO.setGuid("07c02081-ef10-4f32-bc79-55e724f415bc");
        storeBrandDO.setStoreGuid("storeGuid");
        storeBrandDO.setBrandGuid("belongBrandGuid");
        storeBrandDO.setModifiedUserGuid("modifiedUserGuid");
        when(mockStoreBrandMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(storeBrandDO);

        // Configure StoreMapstruct.organizationDO2DTO(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        brandDTO.setStoreList(Arrays.asList(new StoreDTO()));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));
        final OrganizationDO organizationDO1 = new OrganizationDO();
        organizationDO1.setGuid("guid");
        organizationDO1.setCode("code");
        organizationDO1.setType(0);
        organizationDO1.setName("name");
        organizationDO1.setParentIds("parentIds");
        organizationDO1.setProvinceCode("provinceCode");
        organizationDO1.setProvinceName("provinceName");
        organizationDO1.setCityCode("cityCode");
        organizationDO1.setCityName("cityName");
        organizationDO1.setIsEnable(false);
        organizationDO1.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO1.setModifiedUserGuid("modifiedUserGuid");
        organizationDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setIsSelfBuildItems(0);
        organizationDO1.setIsBuAccounts(0);
        organizationDO1.setIsShowCash(0);
        when(mockStoreMapstruct.organizationDO2DTO(organizationDO1, "belongBrandGuid")).thenReturn(storeDTO);

        // Run the test
        final StoreDTO result = storeServiceImplUnderTest.queryStoreByGuid("fd3f7ddd-b567-464f-ad53-ed000e4bdf1c");

        // Verify the results
    }

    @Test
    public void testQueryStoreByGuid_StoreBrandMapperReturnsNull() {
        // Setup
        // Configure OrganizationMapper.selectOne(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        when(mockOrganizationMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(organizationDO);

        when(mockStoreBrandMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Configure StoreMapstruct.organizationDO2DTO(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        brandDTO.setStoreList(Arrays.asList(new StoreDTO()));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));
        final OrganizationDO organizationDO1 = new OrganizationDO();
        organizationDO1.setGuid("guid");
        organizationDO1.setCode("code");
        organizationDO1.setType(0);
        organizationDO1.setName("name");
        organizationDO1.setParentIds("parentIds");
        organizationDO1.setProvinceCode("provinceCode");
        organizationDO1.setProvinceName("provinceName");
        organizationDO1.setCityCode("cityCode");
        organizationDO1.setCityName("cityName");
        organizationDO1.setIsEnable(false);
        organizationDO1.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO1.setModifiedUserGuid("modifiedUserGuid");
        organizationDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setIsSelfBuildItems(0);
        organizationDO1.setIsBuAccounts(0);
        organizationDO1.setIsShowCash(0);
        when(mockStoreMapstruct.organizationDO2DTO(organizationDO1, "belongBrandGuid")).thenReturn(storeDTO);

        // Run the test
        final StoreDTO result = storeServiceImplUnderTest.queryStoreByGuid("fd3f7ddd-b567-464f-ad53-ed000e4bdf1c");

        // Verify the results
    }

    @Test
    public void testQueryStoreBaseByGuid() throws Exception {
        // Setup
        // Configure OrganizationMapper.getStoreBase(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        brandDTO.setStoreList(Arrays.asList(new StoreDTO()));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));
        when(mockOrganizationMapper.getStoreBase("3764c332-d85e-426a-868e-eb291384e43e")).thenReturn(storeDTO);

        // Run the test
        final StoreDTO result = storeServiceImplUnderTest.queryStoreBaseByGuid("3764c332-d85e-426a-868e-eb291384e43e");

        // Verify the results
    }

    @Test
    public void testQueryStoreBaseByGuid_OrganizationMapperReturnsNull() {
        // Setup
        when(mockOrganizationMapper.getStoreBase("3764c332-d85e-426a-868e-eb291384e43e")).thenReturn(null);

        // Run the test
        final StoreDTO result = storeServiceImplUnderTest.queryStoreBaseByGuid("3764c332-d85e-426a-868e-eb291384e43e");

        // Verify the results
        assertNull(result);
    }

    @Test
    public void testQueryStoreBizByGuid() throws Exception {
        // Setup
        final StoreBizDTO expectedResult = new StoreBizDTO();
        expectedResult.setStoreGuid("acb6c82c-031b-4cc2-a95a-094fba33f192");
        expectedResult.setAddressDetail("addressDetail");
        expectedResult.setContactTel("contactTel");
        expectedResult.setBusinessStart(LocalTime.of(0, 0, 0));
        expectedResult.setBusinessEnd(LocalTime.of(0, 0, 0));

        when(mockRedisUtils.get("key")).thenReturn("result");

        // Run the test
        final StoreBizDTO result = storeServiceImplUnderTest.queryStoreBizByGuid(
                "acb6c82c-031b-4cc2-a95a-094fba33f192");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testQueryStoreBizByGuid_RedisUtilsGetReturnsNull() {
        // Setup
        final StoreBizDTO expectedResult = new StoreBizDTO();
        expectedResult.setStoreGuid("acb6c82c-031b-4cc2-a95a-094fba33f192");
        expectedResult.setAddressDetail("addressDetail");
        expectedResult.setContactTel("contactTel");
        expectedResult.setBusinessStart(LocalTime.of(0, 0, 0));
        expectedResult.setBusinessEnd(LocalTime.of(0, 0, 0));

        when(mockRedisUtils.get("key")).thenReturn(null);

        // Configure OrganizationMapper.selectOne(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        when(mockOrganizationMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(organizationDO);

        // Configure StoreBrandMapper.selectOne(...).
        final StoreBrandDO storeBrandDO = new StoreBrandDO();
        storeBrandDO.setId(0L);
        storeBrandDO.setGuid("07c02081-ef10-4f32-bc79-55e724f415bc");
        storeBrandDO.setStoreGuid("storeGuid");
        storeBrandDO.setBrandGuid("belongBrandGuid");
        storeBrandDO.setModifiedUserGuid("modifiedUserGuid");
        when(mockStoreBrandMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(storeBrandDO);

        // Configure StoreMapstruct.organizationDO2DTO(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        brandDTO.setStoreList(Arrays.asList(new StoreDTO()));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));
        final OrganizationDO organizationDO1 = new OrganizationDO();
        organizationDO1.setGuid("guid");
        organizationDO1.setCode("code");
        organizationDO1.setType(0);
        organizationDO1.setName("name");
        organizationDO1.setParentIds("parentIds");
        organizationDO1.setProvinceCode("provinceCode");
        organizationDO1.setProvinceName("provinceName");
        organizationDO1.setCityCode("cityCode");
        organizationDO1.setCityName("cityName");
        organizationDO1.setIsEnable(false);
        organizationDO1.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO1.setModifiedUserGuid("modifiedUserGuid");
        organizationDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setIsSelfBuildItems(0);
        organizationDO1.setIsBuAccounts(0);
        organizationDO1.setIsShowCash(0);
        when(mockStoreMapstruct.organizationDO2DTO(organizationDO1, "belongBrandGuid")).thenReturn(storeDTO);

        // Run the test
        final StoreBizDTO result = storeServiceImplUnderTest.queryStoreBizByGuid(
                "acb6c82c-031b-4cc2-a95a-094fba33f192");

        // Verify the results
        assertEquals(expectedResult, result);

        // Confirm RedisUtils.setEx(...).
        final StoreBizDTO value = new StoreBizDTO();
        value.setStoreGuid("acb6c82c-031b-4cc2-a95a-094fba33f192");
        value.setAddressDetail("addressDetail");
        value.setContactTel("contactTel");
        value.setBusinessStart(LocalTime.of(0, 0, 0));
        value.setBusinessEnd(LocalTime.of(0, 0, 0));
        verify(mockRedisUtils).setEx("key", value, 7L, TimeUnit.DAYS);
    }

    @Test
    public void testQueryStoreBizByGuid_StoreBrandMapperReturnsNull() {
        // Setup
        final StoreBizDTO expectedResult = new StoreBizDTO();
        expectedResult.setStoreGuid("acb6c82c-031b-4cc2-a95a-094fba33f192");
        expectedResult.setAddressDetail("addressDetail");
        expectedResult.setContactTel("contactTel");
        expectedResult.setBusinessStart(LocalTime.of(0, 0, 0));
        expectedResult.setBusinessEnd(LocalTime.of(0, 0, 0));

        when(mockRedisUtils.get("key")).thenReturn(null);

        // Configure OrganizationMapper.selectOne(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        when(mockOrganizationMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(organizationDO);

        when(mockStoreBrandMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Configure StoreMapstruct.organizationDO2DTO(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        brandDTO.setStoreList(Arrays.asList(new StoreDTO()));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));
        final OrganizationDO organizationDO1 = new OrganizationDO();
        organizationDO1.setGuid("guid");
        organizationDO1.setCode("code");
        organizationDO1.setType(0);
        organizationDO1.setName("name");
        organizationDO1.setParentIds("parentIds");
        organizationDO1.setProvinceCode("provinceCode");
        organizationDO1.setProvinceName("provinceName");
        organizationDO1.setCityCode("cityCode");
        organizationDO1.setCityName("cityName");
        organizationDO1.setIsEnable(false);
        organizationDO1.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO1.setModifiedUserGuid("modifiedUserGuid");
        organizationDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setIsSelfBuildItems(0);
        organizationDO1.setIsBuAccounts(0);
        organizationDO1.setIsShowCash(0);
        when(mockStoreMapstruct.organizationDO2DTO(organizationDO1, "belongBrandGuid")).thenReturn(storeDTO);

        // Run the test
        final StoreBizDTO result = storeServiceImplUnderTest.queryStoreBizByGuid(
                "acb6c82c-031b-4cc2-a95a-094fba33f192");

        // Verify the results
        assertEquals(expectedResult, result);

        // Confirm RedisUtils.setEx(...).
        final StoreBizDTO value = new StoreBizDTO();
        value.setStoreGuid("acb6c82c-031b-4cc2-a95a-094fba33f192");
        value.setAddressDetail("addressDetail");
        value.setContactTel("contactTel");
        value.setBusinessStart(LocalTime.of(0, 0, 0));
        value.setBusinessEnd(LocalTime.of(0, 0, 0));
        verify(mockRedisUtils).setEx("key", value, 7L, TimeUnit.DAYS);
    }

    @Test
    public void testQueryStoreByCode() throws Exception {
        // Setup
        // Configure OrganizationMapper.selectOne(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        when(mockOrganizationMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(organizationDO);

        // Configure StoreBrandMapper.selectOne(...).
        final StoreBrandDO storeBrandDO = new StoreBrandDO();
        storeBrandDO.setId(0L);
        storeBrandDO.setGuid("07c02081-ef10-4f32-bc79-55e724f415bc");
        storeBrandDO.setStoreGuid("storeGuid");
        storeBrandDO.setBrandGuid("belongBrandGuid");
        storeBrandDO.setModifiedUserGuid("modifiedUserGuid");
        when(mockStoreBrandMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(storeBrandDO);

        // Configure StoreMapstruct.organizationDO2DTO(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        brandDTO.setStoreList(Arrays.asList(new StoreDTO()));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));
        final OrganizationDO organizationDO1 = new OrganizationDO();
        organizationDO1.setGuid("guid");
        organizationDO1.setCode("code");
        organizationDO1.setType(0);
        organizationDO1.setName("name");
        organizationDO1.setParentIds("parentIds");
        organizationDO1.setProvinceCode("provinceCode");
        organizationDO1.setProvinceName("provinceName");
        organizationDO1.setCityCode("cityCode");
        organizationDO1.setCityName("cityName");
        organizationDO1.setIsEnable(false);
        organizationDO1.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO1.setModifiedUserGuid("modifiedUserGuid");
        organizationDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setIsSelfBuildItems(0);
        organizationDO1.setIsBuAccounts(0);
        organizationDO1.setIsShowCash(0);
        when(mockStoreMapstruct.organizationDO2DTO(organizationDO1, "belongBrandGuid")).thenReturn(storeDTO);

        // Run the test
        final StoreDTO result = storeServiceImplUnderTest.queryStoreByCode("storeCode");

        // Verify the results
    }

    @Test
    public void testQueryStoreByCode_StoreBrandMapperReturnsNull() {
        // Setup
        // Configure OrganizationMapper.selectOne(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        when(mockOrganizationMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(organizationDO);

        when(mockStoreBrandMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Configure StoreMapstruct.organizationDO2DTO(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        brandDTO.setStoreList(Arrays.asList(new StoreDTO()));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));
        final OrganizationDO organizationDO1 = new OrganizationDO();
        organizationDO1.setGuid("guid");
        organizationDO1.setCode("code");
        organizationDO1.setType(0);
        organizationDO1.setName("name");
        organizationDO1.setParentIds("parentIds");
        organizationDO1.setProvinceCode("provinceCode");
        organizationDO1.setProvinceName("provinceName");
        organizationDO1.setCityCode("cityCode");
        organizationDO1.setCityName("cityName");
        organizationDO1.setIsEnable(false);
        organizationDO1.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO1.setModifiedUserGuid("modifiedUserGuid");
        organizationDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setIsSelfBuildItems(0);
        organizationDO1.setIsBuAccounts(0);
        organizationDO1.setIsShowCash(0);
        when(mockStoreMapstruct.organizationDO2DTO(organizationDO1, "belongBrandGuid")).thenReturn(storeDTO);

        // Run the test
        final StoreDTO result = storeServiceImplUnderTest.queryStoreByCode("storeCode");

        // Verify the results
    }

    @Test
    public void testQueryStoreByRegionList() throws Exception {
        // Setup
        final List<RegionDTO> regionDTOList = Arrays.asList(new RegionDTO("adcode", "name", false, Arrays.asList()));

        // Configure OrganizationMapper.queryStoreByRegionList(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOS = Arrays.asList(organizationDO);
        when(mockOrganizationMapper.queryStoreByRegionList(
                Arrays.asList(new RegionDTO("adcode", "name", false, Arrays.asList())))).thenReturn(organizationDOS);

        // Configure StoreMapstruct.organizationList2DTOList(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        brandDTO.setStoreList(Arrays.asList(new StoreDTO()));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO);
        final OrganizationDO organizationDO1 = new OrganizationDO();
        organizationDO1.setGuid("guid");
        organizationDO1.setCode("code");
        organizationDO1.setType(0);
        organizationDO1.setName("name");
        organizationDO1.setParentIds("parentIds");
        organizationDO1.setProvinceCode("provinceCode");
        organizationDO1.setProvinceName("provinceName");
        organizationDO1.setCityCode("cityCode");
        organizationDO1.setCityName("cityName");
        organizationDO1.setIsEnable(false);
        organizationDO1.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO1.setModifiedUserGuid("modifiedUserGuid");
        organizationDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setIsSelfBuildItems(0);
        organizationDO1.setIsBuAccounts(0);
        organizationDO1.setIsShowCash(0);
        final List<OrganizationDO> organizationDOList = Arrays.asList(organizationDO1);
        when(mockStoreMapstruct.organizationList2DTOList(organizationDOList)).thenReturn(storeDTOS);

        // Run the test
        final List<StoreDTO> result = storeServiceImplUnderTest.queryStoreByRegionList(regionDTOList);

        // Verify the results
    }

    @Test
    public void testQueryStoreByRegionList_OrganizationMapperReturnsNoItems() {
        // Setup
        final List<RegionDTO> regionDTOList = Arrays.asList(new RegionDTO("adcode", "name", false, Arrays.asList()));
        when(mockOrganizationMapper.queryStoreByRegionList(
                Arrays.asList(new RegionDTO("adcode", "name", false, Arrays.asList()))))
                .thenReturn(Collections.emptyList());

        // Configure StoreMapstruct.organizationList2DTOList(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        brandDTO.setStoreList(Arrays.asList(new StoreDTO()));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO);
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOList = Arrays.asList(organizationDO);
        when(mockStoreMapstruct.organizationList2DTOList(organizationDOList)).thenReturn(storeDTOS);

        // Run the test
        final List<StoreDTO> result = storeServiceImplUnderTest.queryStoreByRegionList(regionDTOList);

        // Verify the results
    }

    @Test
    public void testQueryStoreByRegionList_StoreMapstructReturnsNoItems() {
        // Setup
        final List<RegionDTO> regionDTOList = Arrays.asList(new RegionDTO("adcode", "name", false, Arrays.asList()));

        // Configure OrganizationMapper.queryStoreByRegionList(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOS = Arrays.asList(organizationDO);
        when(mockOrganizationMapper.queryStoreByRegionList(
                Arrays.asList(new RegionDTO("adcode", "name", false, Arrays.asList())))).thenReturn(organizationDOS);

        // Configure StoreMapstruct.organizationList2DTOList(...).
        final OrganizationDO organizationDO1 = new OrganizationDO();
        organizationDO1.setGuid("guid");
        organizationDO1.setCode("code");
        organizationDO1.setType(0);
        organizationDO1.setName("name");
        organizationDO1.setParentIds("parentIds");
        organizationDO1.setProvinceCode("provinceCode");
        organizationDO1.setProvinceName("provinceName");
        organizationDO1.setCityCode("cityCode");
        organizationDO1.setCityName("cityName");
        organizationDO1.setIsEnable(false);
        organizationDO1.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO1.setModifiedUserGuid("modifiedUserGuid");
        organizationDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setIsSelfBuildItems(0);
        organizationDO1.setIsBuAccounts(0);
        organizationDO1.setIsShowCash(0);
        final List<OrganizationDO> organizationDOList = Arrays.asList(organizationDO1);
        when(mockStoreMapstruct.organizationList2DTOList(organizationDOList)).thenReturn(Collections.emptyList());

        // Run the test
        final List<StoreDTO> result = storeServiceImplUnderTest.queryStoreByRegionList(regionDTOList);

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testQueryStoreByBrandList() throws Exception {
        // Setup
        // Configure StoreBrandMapper.selectList(...).
        final StoreBrandDO storeBrandDO = new StoreBrandDO();
        storeBrandDO.setId(0L);
        storeBrandDO.setGuid("07c02081-ef10-4f32-bc79-55e724f415bc");
        storeBrandDO.setStoreGuid("storeGuid");
        storeBrandDO.setBrandGuid("belongBrandGuid");
        storeBrandDO.setModifiedUserGuid("modifiedUserGuid");
        final List<StoreBrandDO> storeBrandDOS = Arrays.asList(storeBrandDO);
        when(mockStoreBrandMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(storeBrandDOS);

        // Configure BrandMapper.selectList(...).
        final BrandDO brandDO = new BrandDO();
        brandDO.setId(0L);
        brandDO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDO.setName("belongBrandName");
        brandDO.setDescription("description");
        brandDO.setIsEnable(false);
        final List<BrandDO> brandDOS = Arrays.asList(brandDO);
        when(mockBrandMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(brandDOS);

        // Configure OrganizationMapper.selectList(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOS = Arrays.asList(organizationDO);
        when(mockOrganizationMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(organizationDOS);

        // Configure StoreMapstruct.organizationList2DTOList(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        brandDTO.setStoreList(Arrays.asList(new StoreDTO()));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO);
        final OrganizationDO organizationDO1 = new OrganizationDO();
        organizationDO1.setGuid("guid");
        organizationDO1.setCode("code");
        organizationDO1.setType(0);
        organizationDO1.setName("name");
        organizationDO1.setParentIds("parentIds");
        organizationDO1.setProvinceCode("provinceCode");
        organizationDO1.setProvinceName("provinceName");
        organizationDO1.setCityCode("cityCode");
        organizationDO1.setCityName("cityName");
        organizationDO1.setIsEnable(false);
        organizationDO1.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO1.setModifiedUserGuid("modifiedUserGuid");
        organizationDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setIsSelfBuildItems(0);
        organizationDO1.setIsBuAccounts(0);
        organizationDO1.setIsShowCash(0);
        final List<OrganizationDO> organizationDOList = Arrays.asList(organizationDO1);
        when(mockStoreMapstruct.organizationList2DTOList(organizationDOList)).thenReturn(storeDTOS);

        // Run the test
        final List<StoreDTO> result = storeServiceImplUnderTest.queryStoreByBrandList(Arrays.asList("value"));

        // Verify the results
    }

    @Test
    public void testQueryStoreByBrandList_StoreBrandMapperReturnsNoItems() {
        // Setup
        when(mockStoreBrandMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure BrandMapper.selectList(...).
        final BrandDO brandDO = new BrandDO();
        brandDO.setId(0L);
        brandDO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDO.setName("belongBrandName");
        brandDO.setDescription("description");
        brandDO.setIsEnable(false);
        final List<BrandDO> brandDOS = Arrays.asList(brandDO);
        when(mockBrandMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(brandDOS);

        // Run the test
        final List<StoreDTO> result = storeServiceImplUnderTest.queryStoreByBrandList(Arrays.asList("value"));

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testQueryStoreByBrandList_BrandMapperReturnsNoItems() {
        // Setup
        // Configure StoreBrandMapper.selectList(...).
        final StoreBrandDO storeBrandDO = new StoreBrandDO();
        storeBrandDO.setId(0L);
        storeBrandDO.setGuid("07c02081-ef10-4f32-bc79-55e724f415bc");
        storeBrandDO.setStoreGuid("storeGuid");
        storeBrandDO.setBrandGuid("belongBrandGuid");
        storeBrandDO.setModifiedUserGuid("modifiedUserGuid");
        final List<StoreBrandDO> storeBrandDOS = Arrays.asList(storeBrandDO);
        when(mockStoreBrandMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(storeBrandDOS);

        when(mockBrandMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure OrganizationMapper.selectList(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOS = Arrays.asList(organizationDO);
        when(mockOrganizationMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(organizationDOS);

        // Configure StoreMapstruct.organizationList2DTOList(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        brandDTO.setStoreList(Arrays.asList(new StoreDTO()));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO);
        final OrganizationDO organizationDO1 = new OrganizationDO();
        organizationDO1.setGuid("guid");
        organizationDO1.setCode("code");
        organizationDO1.setType(0);
        organizationDO1.setName("name");
        organizationDO1.setParentIds("parentIds");
        organizationDO1.setProvinceCode("provinceCode");
        organizationDO1.setProvinceName("provinceName");
        organizationDO1.setCityCode("cityCode");
        organizationDO1.setCityName("cityName");
        organizationDO1.setIsEnable(false);
        organizationDO1.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO1.setModifiedUserGuid("modifiedUserGuid");
        organizationDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setIsSelfBuildItems(0);
        organizationDO1.setIsBuAccounts(0);
        organizationDO1.setIsShowCash(0);
        final List<OrganizationDO> organizationDOList = Arrays.asList(organizationDO1);
        when(mockStoreMapstruct.organizationList2DTOList(organizationDOList)).thenReturn(storeDTOS);

        // Run the test
        final List<StoreDTO> result = storeServiceImplUnderTest.queryStoreByBrandList(Arrays.asList("value"));

        // Verify the results
    }

    @Test
    public void testQueryStoreByBrandList_OrganizationMapperReturnsNoItems() {
        // Setup
        // Configure StoreBrandMapper.selectList(...).
        final StoreBrandDO storeBrandDO = new StoreBrandDO();
        storeBrandDO.setId(0L);
        storeBrandDO.setGuid("07c02081-ef10-4f32-bc79-55e724f415bc");
        storeBrandDO.setStoreGuid("storeGuid");
        storeBrandDO.setBrandGuid("belongBrandGuid");
        storeBrandDO.setModifiedUserGuid("modifiedUserGuid");
        final List<StoreBrandDO> storeBrandDOS = Arrays.asList(storeBrandDO);
        when(mockStoreBrandMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(storeBrandDOS);

        // Configure BrandMapper.selectList(...).
        final BrandDO brandDO = new BrandDO();
        brandDO.setId(0L);
        brandDO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDO.setName("belongBrandName");
        brandDO.setDescription("description");
        brandDO.setIsEnable(false);
        final List<BrandDO> brandDOS = Arrays.asList(brandDO);
        when(mockBrandMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(brandDOS);

        when(mockOrganizationMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure StoreMapstruct.organizationList2DTOList(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        brandDTO.setStoreList(Arrays.asList(new StoreDTO()));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO);
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOList = Arrays.asList(organizationDO);
        when(mockStoreMapstruct.organizationList2DTOList(organizationDOList)).thenReturn(storeDTOS);

        // Run the test
        final List<StoreDTO> result = storeServiceImplUnderTest.queryStoreByBrandList(Arrays.asList("value"));

        // Verify the results
    }

    @Test
    public void testQueryStoreByBrandList_StoreMapstructReturnsNoItems() {
        // Setup
        // Configure StoreBrandMapper.selectList(...).
        final StoreBrandDO storeBrandDO = new StoreBrandDO();
        storeBrandDO.setId(0L);
        storeBrandDO.setGuid("07c02081-ef10-4f32-bc79-55e724f415bc");
        storeBrandDO.setStoreGuid("storeGuid");
        storeBrandDO.setBrandGuid("belongBrandGuid");
        storeBrandDO.setModifiedUserGuid("modifiedUserGuid");
        final List<StoreBrandDO> storeBrandDOS = Arrays.asList(storeBrandDO);
        when(mockStoreBrandMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(storeBrandDOS);

        // Configure BrandMapper.selectList(...).
        final BrandDO brandDO = new BrandDO();
        brandDO.setId(0L);
        brandDO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDO.setName("belongBrandName");
        brandDO.setDescription("description");
        brandDO.setIsEnable(false);
        final List<BrandDO> brandDOS = Arrays.asList(brandDO);
        when(mockBrandMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(brandDOS);

        // Configure OrganizationMapper.selectList(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOS = Arrays.asList(organizationDO);
        when(mockOrganizationMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(organizationDOS);

        // Configure StoreMapstruct.organizationList2DTOList(...).
        final OrganizationDO organizationDO1 = new OrganizationDO();
        organizationDO1.setGuid("guid");
        organizationDO1.setCode("code");
        organizationDO1.setType(0);
        organizationDO1.setName("name");
        organizationDO1.setParentIds("parentIds");
        organizationDO1.setProvinceCode("provinceCode");
        organizationDO1.setProvinceName("provinceName");
        organizationDO1.setCityCode("cityCode");
        organizationDO1.setCityName("cityName");
        organizationDO1.setIsEnable(false);
        organizationDO1.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO1.setModifiedUserGuid("modifiedUserGuid");
        organizationDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setIsSelfBuildItems(0);
        organizationDO1.setIsBuAccounts(0);
        organizationDO1.setIsShowCash(0);
        final List<OrganizationDO> organizationDOList = Arrays.asList(organizationDO1);
        when(mockStoreMapstruct.organizationList2DTOList(organizationDOList)).thenReturn(Collections.emptyList());

        // Run the test
        final List<StoreDTO> result = storeServiceImplUnderTest.queryStoreByBrandList(Arrays.asList("value"));

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testQueryDeleteCondition() throws Exception {
        // Setup
        // Configure UserClient.queryProductByStoreGuid(...).
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setProductGuid("productGuid");
        storeProductDTO.setProductName("productName");
        storeProductDTO.setProductType(0);
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<StoreProductDTO> storeProductDTOS = Arrays.asList(storeProductDTO);
        when(mockUserClient.queryProductByStoreGuid("storeGuid", false)).thenReturn(storeProductDTOS);

        // Run the test
        final boolean result = storeServiceImplUnderTest.queryDeleteCondition("storeGuid");

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testQueryDeleteCondition_UserClientReturnsNoItems() {
        // Setup
        when(mockUserClient.queryProductByStoreGuid("storeGuid", false)).thenReturn(Collections.emptyList());

        // Run the test
        final boolean result = storeServiceImplUnderTest.queryDeleteCondition("storeGuid");

        // Verify the results
        assertTrue(result);
    }

    @Test
    public void testQueryBrandByStoreGuid() throws Exception {
        // Setup
        // Configure StoreBrandMapper.selectOne(...).
        final StoreBrandDO storeBrandDO = new StoreBrandDO();
        storeBrandDO.setId(0L);
        storeBrandDO.setGuid("07c02081-ef10-4f32-bc79-55e724f415bc");
        storeBrandDO.setStoreGuid("storeGuid");
        storeBrandDO.setBrandGuid("belongBrandGuid");
        storeBrandDO.setModifiedUserGuid("modifiedUserGuid");
        when(mockStoreBrandMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(storeBrandDO);

        // Configure BrandMapper.selectOne(...).
        final BrandDO brandDO = new BrandDO();
        brandDO.setId(0L);
        brandDO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDO.setName("belongBrandName");
        brandDO.setDescription("description");
        brandDO.setIsEnable(false);
        when(mockBrandMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(brandDO);

        // Configure BrandMapstruct.brandDO2DTO(...).
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        storeDTO.setBrandDTOList(Arrays.asList(new BrandDTO()));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));
        brandDTO.setStoreList(Arrays.asList(storeDTO));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        final BrandDO brandDO1 = new BrandDO();
        brandDO1.setId(0L);
        brandDO1.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDO1.setName("belongBrandName");
        brandDO1.setDescription("description");
        brandDO1.setIsEnable(false);
        when(mockBrandMapstruct.brandDO2DTO(brandDO1)).thenReturn(brandDTO);

        // Run the test
        final BrandDTO result = storeServiceImplUnderTest.queryBrandByStoreGuid("storeGuid");

        // Verify the results
    }

    @Test
    public void testQueryBrandListByStoreGuidList() throws Exception {
        // Setup
        // Configure StoreBrandMapper.selectList(...).
        final StoreBrandDO storeBrandDO = new StoreBrandDO();
        storeBrandDO.setId(0L);
        storeBrandDO.setGuid("07c02081-ef10-4f32-bc79-55e724f415bc");
        storeBrandDO.setStoreGuid("storeGuid");
        storeBrandDO.setBrandGuid("belongBrandGuid");
        storeBrandDO.setModifiedUserGuid("modifiedUserGuid");
        final List<StoreBrandDO> storeBrandDOS = Arrays.asList(storeBrandDO);
        when(mockStoreBrandMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(storeBrandDOS);

        // Configure BrandMapper.selectList(...).
        final BrandDO brandDO = new BrandDO();
        brandDO.setId(0L);
        brandDO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDO.setName("belongBrandName");
        brandDO.setDescription("description");
        brandDO.setIsEnable(false);
        final List<BrandDO> brandDOS = Arrays.asList(brandDO);
        when(mockBrandMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(brandDOS);

        // Configure BrandMapstruct.brandDOList2DTOList(...).
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        storeDTO.setBrandDTOList(Arrays.asList(new BrandDTO()));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));
        brandDTO.setStoreList(Arrays.asList(storeDTO));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        final List<BrandDTO> brandDTOS = Arrays.asList(brandDTO);
        final BrandDO brandDO1 = new BrandDO();
        brandDO1.setId(0L);
        brandDO1.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDO1.setName("belongBrandName");
        brandDO1.setDescription("description");
        brandDO1.setIsEnable(false);
        final List<BrandDO> brandDOList = Arrays.asList(brandDO1);
        when(mockBrandMapstruct.brandDOList2DTOList(brandDOList)).thenReturn(brandDTOS);

        // Configure OrganizationMapper.selectList(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOS = Arrays.asList(organizationDO);
        when(mockOrganizationMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(organizationDOS);

        // Configure StoreMapstruct.organizationList2DTOList(...).
        final StoreDTO storeDTO1 = new StoreDTO();
        storeDTO1.setGuid("guid");
        storeDTO1.setCode("code");
        storeDTO1.setName("name");
        storeDTO1.setBelongBrandGuid("belongBrandGuid");
        storeDTO1.setBelongBrandName("belongBrandName");
        storeDTO1.setParentIds("parentIds");
        storeDTO1.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO1.setContactTel("contactTel");
        storeDTO1.setCityCode("cityCode");
        storeDTO1.setCityName("cityName");
        storeDTO1.setAddressDetail("addressDetail");
        storeDTO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO1.setCreateUserGuid("createUserGuid");
        storeDTO1.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO1 = new StoreProductDTO();
        storeProductDTO1.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO1.setProductDTOList(Arrays.asList(storeProductDTO1));
        final BrandDTO brandDTO1 = new BrandDTO();
        brandDTO1.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO1.setName("belongBrandName");
        brandDTO1.setLogoUrl("brandLogoUrl");
        brandDTO1.setIsEnable(false);
        brandDTO1.setStoreList(Arrays.asList(new StoreDTO()));
        brandDTO1.setIsBuAccounts(0);
        brandDTO1.setIsShowCash(0);
        brandDTO1.setIsMultiHandover(0);
        storeDTO1.setBrandDTOList(Arrays.asList(brandDTO1));
        storeDTO1.setMchntTypeCode("mchntTypeCode");
        storeDTO1.setIsSelfBuildItems(0);
        storeDTO1.setIsBuAccounts(0);
        storeDTO1.setIsShowCash(0);
        storeDTO1.setIsMultiHandover(0);
        storeDTO1.setBusinessDay(LocalDate.of(2020, 1, 1));
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO1);
        final OrganizationDO organizationDO1 = new OrganizationDO();
        organizationDO1.setGuid("guid");
        organizationDO1.setCode("code");
        organizationDO1.setType(0);
        organizationDO1.setName("name");
        organizationDO1.setParentIds("parentIds");
        organizationDO1.setProvinceCode("provinceCode");
        organizationDO1.setProvinceName("provinceName");
        organizationDO1.setCityCode("cityCode");
        organizationDO1.setCityName("cityName");
        organizationDO1.setIsEnable(false);
        organizationDO1.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO1.setModifiedUserGuid("modifiedUserGuid");
        organizationDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setIsSelfBuildItems(0);
        organizationDO1.setIsBuAccounts(0);
        organizationDO1.setIsShowCash(0);
        final List<OrganizationDO> organizationDOList = Arrays.asList(organizationDO1);
        when(mockStoreMapstruct.organizationList2DTOList(organizationDOList)).thenReturn(storeDTOS);

        // Run the test
        final List<BrandDTO> result = storeServiceImplUnderTest.queryBrandListByStoreGuidList(Arrays.asList("value"));

        // Verify the results
    }

    @Test
    public void testQueryBrandListByStoreGuidList_StoreBrandMapperReturnsNoItems() {
        // Setup
        when(mockStoreBrandMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<BrandDTO> result = storeServiceImplUnderTest.queryBrandListByStoreGuidList(Arrays.asList("value"));

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testQueryBrandListByStoreGuidList_BrandMapperReturnsNoItems() {
        // Setup
        // Configure StoreBrandMapper.selectList(...).
        final StoreBrandDO storeBrandDO = new StoreBrandDO();
        storeBrandDO.setId(0L);
        storeBrandDO.setGuid("07c02081-ef10-4f32-bc79-55e724f415bc");
        storeBrandDO.setStoreGuid("storeGuid");
        storeBrandDO.setBrandGuid("belongBrandGuid");
        storeBrandDO.setModifiedUserGuid("modifiedUserGuid");
        final List<StoreBrandDO> storeBrandDOS = Arrays.asList(storeBrandDO);
        when(mockStoreBrandMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(storeBrandDOS);

        when(mockBrandMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure BrandMapstruct.brandDOList2DTOList(...).
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        storeDTO.setBrandDTOList(Arrays.asList(new BrandDTO()));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));
        brandDTO.setStoreList(Arrays.asList(storeDTO));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        final List<BrandDTO> brandDTOS = Arrays.asList(brandDTO);
        final BrandDO brandDO = new BrandDO();
        brandDO.setId(0L);
        brandDO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDO.setName("belongBrandName");
        brandDO.setDescription("description");
        brandDO.setIsEnable(false);
        final List<BrandDO> brandDOList = Arrays.asList(brandDO);
        when(mockBrandMapstruct.brandDOList2DTOList(brandDOList)).thenReturn(brandDTOS);

        // Configure OrganizationMapper.selectList(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOS = Arrays.asList(organizationDO);
        when(mockOrganizationMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(organizationDOS);

        // Configure StoreMapstruct.organizationList2DTOList(...).
        final StoreDTO storeDTO1 = new StoreDTO();
        storeDTO1.setGuid("guid");
        storeDTO1.setCode("code");
        storeDTO1.setName("name");
        storeDTO1.setBelongBrandGuid("belongBrandGuid");
        storeDTO1.setBelongBrandName("belongBrandName");
        storeDTO1.setParentIds("parentIds");
        storeDTO1.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO1.setContactTel("contactTel");
        storeDTO1.setCityCode("cityCode");
        storeDTO1.setCityName("cityName");
        storeDTO1.setAddressDetail("addressDetail");
        storeDTO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO1.setCreateUserGuid("createUserGuid");
        storeDTO1.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO1 = new StoreProductDTO();
        storeProductDTO1.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO1.setProductDTOList(Arrays.asList(storeProductDTO1));
        final BrandDTO brandDTO1 = new BrandDTO();
        brandDTO1.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO1.setName("belongBrandName");
        brandDTO1.setLogoUrl("brandLogoUrl");
        brandDTO1.setIsEnable(false);
        brandDTO1.setStoreList(Arrays.asList(new StoreDTO()));
        brandDTO1.setIsBuAccounts(0);
        brandDTO1.setIsShowCash(0);
        brandDTO1.setIsMultiHandover(0);
        storeDTO1.setBrandDTOList(Arrays.asList(brandDTO1));
        storeDTO1.setMchntTypeCode("mchntTypeCode");
        storeDTO1.setIsSelfBuildItems(0);
        storeDTO1.setIsBuAccounts(0);
        storeDTO1.setIsShowCash(0);
        storeDTO1.setIsMultiHandover(0);
        storeDTO1.setBusinessDay(LocalDate.of(2020, 1, 1));
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO1);
        final OrganizationDO organizationDO1 = new OrganizationDO();
        organizationDO1.setGuid("guid");
        organizationDO1.setCode("code");
        organizationDO1.setType(0);
        organizationDO1.setName("name");
        organizationDO1.setParentIds("parentIds");
        organizationDO1.setProvinceCode("provinceCode");
        organizationDO1.setProvinceName("provinceName");
        organizationDO1.setCityCode("cityCode");
        organizationDO1.setCityName("cityName");
        organizationDO1.setIsEnable(false);
        organizationDO1.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO1.setModifiedUserGuid("modifiedUserGuid");
        organizationDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setIsSelfBuildItems(0);
        organizationDO1.setIsBuAccounts(0);
        organizationDO1.setIsShowCash(0);
        final List<OrganizationDO> organizationDOList = Arrays.asList(organizationDO1);
        when(mockStoreMapstruct.organizationList2DTOList(organizationDOList)).thenReturn(storeDTOS);

        // Run the test
        final List<BrandDTO> result = storeServiceImplUnderTest.queryBrandListByStoreGuidList(Arrays.asList("value"));

        // Verify the results
    }

    @Test
    public void testQueryBrandListByStoreGuidList_BrandMapstructReturnsNoItems() {
        // Setup
        // Configure StoreBrandMapper.selectList(...).
        final StoreBrandDO storeBrandDO = new StoreBrandDO();
        storeBrandDO.setId(0L);
        storeBrandDO.setGuid("07c02081-ef10-4f32-bc79-55e724f415bc");
        storeBrandDO.setStoreGuid("storeGuid");
        storeBrandDO.setBrandGuid("belongBrandGuid");
        storeBrandDO.setModifiedUserGuid("modifiedUserGuid");
        final List<StoreBrandDO> storeBrandDOS = Arrays.asList(storeBrandDO);
        when(mockStoreBrandMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(storeBrandDOS);

        // Configure BrandMapper.selectList(...).
        final BrandDO brandDO = new BrandDO();
        brandDO.setId(0L);
        brandDO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDO.setName("belongBrandName");
        brandDO.setDescription("description");
        brandDO.setIsEnable(false);
        final List<BrandDO> brandDOS = Arrays.asList(brandDO);
        when(mockBrandMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(brandDOS);

        // Configure BrandMapstruct.brandDOList2DTOList(...).
        final BrandDO brandDO1 = new BrandDO();
        brandDO1.setId(0L);
        brandDO1.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDO1.setName("belongBrandName");
        brandDO1.setDescription("description");
        brandDO1.setIsEnable(false);
        final List<BrandDO> brandDOList = Arrays.asList(brandDO1);
        when(mockBrandMapstruct.brandDOList2DTOList(brandDOList)).thenReturn(Collections.emptyList());

        // Run the test
        final List<BrandDTO> result = storeServiceImplUnderTest.queryBrandListByStoreGuidList(Arrays.asList("value"));

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testQueryBrandListByStoreGuidList_OrganizationMapperReturnsNoItems() {
        // Setup
        // Configure StoreBrandMapper.selectList(...).
        final StoreBrandDO storeBrandDO = new StoreBrandDO();
        storeBrandDO.setId(0L);
        storeBrandDO.setGuid("07c02081-ef10-4f32-bc79-55e724f415bc");
        storeBrandDO.setStoreGuid("storeGuid");
        storeBrandDO.setBrandGuid("belongBrandGuid");
        storeBrandDO.setModifiedUserGuid("modifiedUserGuid");
        final List<StoreBrandDO> storeBrandDOS = Arrays.asList(storeBrandDO);
        when(mockStoreBrandMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(storeBrandDOS);

        // Configure BrandMapper.selectList(...).
        final BrandDO brandDO = new BrandDO();
        brandDO.setId(0L);
        brandDO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDO.setName("belongBrandName");
        brandDO.setDescription("description");
        brandDO.setIsEnable(false);
        final List<BrandDO> brandDOS = Arrays.asList(brandDO);
        when(mockBrandMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(brandDOS);

        // Configure BrandMapstruct.brandDOList2DTOList(...).
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        storeDTO.setBrandDTOList(Arrays.asList(new BrandDTO()));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));
        brandDTO.setStoreList(Arrays.asList(storeDTO));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        final List<BrandDTO> brandDTOS = Arrays.asList(brandDTO);
        final BrandDO brandDO1 = new BrandDO();
        brandDO1.setId(0L);
        brandDO1.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDO1.setName("belongBrandName");
        brandDO1.setDescription("description");
        brandDO1.setIsEnable(false);
        final List<BrandDO> brandDOList = Arrays.asList(brandDO1);
        when(mockBrandMapstruct.brandDOList2DTOList(brandDOList)).thenReturn(brandDTOS);

        when(mockOrganizationMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure StoreMapstruct.organizationList2DTOList(...).
        final StoreDTO storeDTO1 = new StoreDTO();
        storeDTO1.setGuid("guid");
        storeDTO1.setCode("code");
        storeDTO1.setName("name");
        storeDTO1.setBelongBrandGuid("belongBrandGuid");
        storeDTO1.setBelongBrandName("belongBrandName");
        storeDTO1.setParentIds("parentIds");
        storeDTO1.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO1.setContactTel("contactTel");
        storeDTO1.setCityCode("cityCode");
        storeDTO1.setCityName("cityName");
        storeDTO1.setAddressDetail("addressDetail");
        storeDTO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO1.setCreateUserGuid("createUserGuid");
        storeDTO1.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO1 = new StoreProductDTO();
        storeProductDTO1.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO1.setProductDTOList(Arrays.asList(storeProductDTO1));
        final BrandDTO brandDTO1 = new BrandDTO();
        brandDTO1.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO1.setName("belongBrandName");
        brandDTO1.setLogoUrl("brandLogoUrl");
        brandDTO1.setIsEnable(false);
        brandDTO1.setStoreList(Arrays.asList(new StoreDTO()));
        brandDTO1.setIsBuAccounts(0);
        brandDTO1.setIsShowCash(0);
        brandDTO1.setIsMultiHandover(0);
        storeDTO1.setBrandDTOList(Arrays.asList(brandDTO1));
        storeDTO1.setMchntTypeCode("mchntTypeCode");
        storeDTO1.setIsSelfBuildItems(0);
        storeDTO1.setIsBuAccounts(0);
        storeDTO1.setIsShowCash(0);
        storeDTO1.setIsMultiHandover(0);
        storeDTO1.setBusinessDay(LocalDate.of(2020, 1, 1));
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO1);
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOList = Arrays.asList(organizationDO);
        when(mockStoreMapstruct.organizationList2DTOList(organizationDOList)).thenReturn(storeDTOS);

        // Run the test
        final List<BrandDTO> result = storeServiceImplUnderTest.queryBrandListByStoreGuidList(Arrays.asList("value"));

        // Verify the results
    }

    @Test
    public void testQueryBrandListByStoreGuidList_StoreMapstructReturnsNoItems() {
        // Setup
        // Configure StoreBrandMapper.selectList(...).
        final StoreBrandDO storeBrandDO = new StoreBrandDO();
        storeBrandDO.setId(0L);
        storeBrandDO.setGuid("07c02081-ef10-4f32-bc79-55e724f415bc");
        storeBrandDO.setStoreGuid("storeGuid");
        storeBrandDO.setBrandGuid("belongBrandGuid");
        storeBrandDO.setModifiedUserGuid("modifiedUserGuid");
        final List<StoreBrandDO> storeBrandDOS = Arrays.asList(storeBrandDO);
        when(mockStoreBrandMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(storeBrandDOS);

        // Configure BrandMapper.selectList(...).
        final BrandDO brandDO = new BrandDO();
        brandDO.setId(0L);
        brandDO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDO.setName("belongBrandName");
        brandDO.setDescription("description");
        brandDO.setIsEnable(false);
        final List<BrandDO> brandDOS = Arrays.asList(brandDO);
        when(mockBrandMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(brandDOS);

        // Configure BrandMapstruct.brandDOList2DTOList(...).
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        storeDTO.setBrandDTOList(Arrays.asList(new BrandDTO()));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));
        brandDTO.setStoreList(Arrays.asList(storeDTO));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        final List<BrandDTO> brandDTOS = Arrays.asList(brandDTO);
        final BrandDO brandDO1 = new BrandDO();
        brandDO1.setId(0L);
        brandDO1.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDO1.setName("belongBrandName");
        brandDO1.setDescription("description");
        brandDO1.setIsEnable(false);
        final List<BrandDO> brandDOList = Arrays.asList(brandDO1);
        when(mockBrandMapstruct.brandDOList2DTOList(brandDOList)).thenReturn(brandDTOS);

        // Configure OrganizationMapper.selectList(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOS = Arrays.asList(organizationDO);
        when(mockOrganizationMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(organizationDOS);

        // Configure StoreMapstruct.organizationList2DTOList(...).
        final OrganizationDO organizationDO1 = new OrganizationDO();
        organizationDO1.setGuid("guid");
        organizationDO1.setCode("code");
        organizationDO1.setType(0);
        organizationDO1.setName("name");
        organizationDO1.setParentIds("parentIds");
        organizationDO1.setProvinceCode("provinceCode");
        organizationDO1.setProvinceName("provinceName");
        organizationDO1.setCityCode("cityCode");
        organizationDO1.setCityName("cityName");
        organizationDO1.setIsEnable(false);
        organizationDO1.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO1.setModifiedUserGuid("modifiedUserGuid");
        organizationDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setIsSelfBuildItems(0);
        organizationDO1.setIsBuAccounts(0);
        organizationDO1.setIsShowCash(0);
        final List<OrganizationDO> organizationDOList = Arrays.asList(organizationDO1);
        when(mockStoreMapstruct.organizationList2DTOList(organizationDOList)).thenReturn(Collections.emptyList());

        // Run the test
        final List<BrandDTO> result = storeServiceImplUnderTest.queryBrandListByStoreGuidList(Arrays.asList("value"));

        // Verify the results
    }

    @Test
    public void testQueryStoreByNameList() throws Exception {
        // Setup
        // Configure OrganizationMapper.selectList(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOS = Arrays.asList(organizationDO);
        when(mockOrganizationMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(organizationDOS);

        // Configure StoreMapstruct.organizationList2DTOList(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        brandDTO.setStoreList(Arrays.asList(new StoreDTO()));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO);
        final OrganizationDO organizationDO1 = new OrganizationDO();
        organizationDO1.setGuid("guid");
        organizationDO1.setCode("code");
        organizationDO1.setType(0);
        organizationDO1.setName("name");
        organizationDO1.setParentIds("parentIds");
        organizationDO1.setProvinceCode("provinceCode");
        organizationDO1.setProvinceName("provinceName");
        organizationDO1.setCityCode("cityCode");
        organizationDO1.setCityName("cityName");
        organizationDO1.setIsEnable(false);
        organizationDO1.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO1.setModifiedUserGuid("modifiedUserGuid");
        organizationDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setIsSelfBuildItems(0);
        organizationDO1.setIsBuAccounts(0);
        organizationDO1.setIsShowCash(0);
        final List<OrganizationDO> organizationDOList = Arrays.asList(organizationDO1);
        when(mockStoreMapstruct.organizationList2DTOList(organizationDOList)).thenReturn(storeDTOS);

        // Run the test
        final List<StoreDTO> result = storeServiceImplUnderTest.queryStoreByNameList(Arrays.asList("value"));

        // Verify the results
    }

    @Test
    public void testQueryStoreByNameList_OrganizationMapperReturnsNoItems() {
        // Setup
        when(mockOrganizationMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<StoreDTO> result = storeServiceImplUnderTest.queryStoreByNameList(Arrays.asList("value"));

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testQueryStoreByNameList_StoreMapstructReturnsNoItems() {
        // Setup
        // Configure OrganizationMapper.selectList(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOS = Arrays.asList(organizationDO);
        when(mockOrganizationMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(organizationDOS);

        // Configure StoreMapstruct.organizationList2DTOList(...).
        final OrganizationDO organizationDO1 = new OrganizationDO();
        organizationDO1.setGuid("guid");
        organizationDO1.setCode("code");
        organizationDO1.setType(0);
        organizationDO1.setName("name");
        organizationDO1.setParentIds("parentIds");
        organizationDO1.setProvinceCode("provinceCode");
        organizationDO1.setProvinceName("provinceName");
        organizationDO1.setCityCode("cityCode");
        organizationDO1.setCityName("cityName");
        organizationDO1.setIsEnable(false);
        organizationDO1.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO1.setModifiedUserGuid("modifiedUserGuid");
        organizationDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setIsSelfBuildItems(0);
        organizationDO1.setIsBuAccounts(0);
        organizationDO1.setIsShowCash(0);
        final List<OrganizationDO> organizationDOList = Arrays.asList(organizationDO1);
        when(mockStoreMapstruct.organizationList2DTOList(organizationDOList)).thenReturn(Collections.emptyList());

        // Run the test
        final List<StoreDTO> result = storeServiceImplUnderTest.queryStoreByNameList(Arrays.asList("value"));

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testQueryBrandByStoreGuidForMember() throws Exception {
        // Setup
        // Configure StoreBrandMapper.selectOne(...).
        final StoreBrandDO storeBrandDO = new StoreBrandDO();
        storeBrandDO.setId(0L);
        storeBrandDO.setGuid("07c02081-ef10-4f32-bc79-55e724f415bc");
        storeBrandDO.setStoreGuid("storeGuid");
        storeBrandDO.setBrandGuid("belongBrandGuid");
        storeBrandDO.setModifiedUserGuid("modifiedUserGuid");
        when(mockStoreBrandMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(storeBrandDO);

        // Configure BrandMapper.selectOne(...).
        final BrandDO brandDO = new BrandDO();
        brandDO.setId(0L);
        brandDO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDO.setName("belongBrandName");
        brandDO.setDescription("description");
        brandDO.setIsEnable(false);
        when(mockBrandMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(brandDO);

        // Configure BrandMapstruct.brandDO2DTO(...).
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        storeDTO.setBrandDTOList(Arrays.asList(new BrandDTO()));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));
        brandDTO.setStoreList(Arrays.asList(storeDTO));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        final BrandDO brandDO1 = new BrandDO();
        brandDO1.setId(0L);
        brandDO1.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDO1.setName("belongBrandName");
        brandDO1.setDescription("description");
        brandDO1.setIsEnable(false);
        when(mockBrandMapstruct.brandDO2DTO(brandDO1)).thenReturn(brandDTO);

        // Run the test
        final BrandDTO result = storeServiceImplUnderTest.queryBrandByStoreGuidForMember("storeGuid");

        // Verify the results
    }

    @Test
    public void testQueryStoreByIdList() {
        // Setup
        // Configure OrganizationMapper.selectList(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOS = Arrays.asList(organizationDO);
        when(mockOrganizationMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(organizationDOS);

        // Configure StoreMapstruct.organizationList2DTOList(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        brandDTO.setStoreList(Arrays.asList(new StoreDTO()));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO);
        final OrganizationDO organizationDO1 = new OrganizationDO();
        organizationDO1.setGuid("guid");
        organizationDO1.setCode("code");
        organizationDO1.setType(0);
        organizationDO1.setName("name");
        organizationDO1.setParentIds("parentIds");
        organizationDO1.setProvinceCode("provinceCode");
        organizationDO1.setProvinceName("provinceName");
        organizationDO1.setCityCode("cityCode");
        organizationDO1.setCityName("cityName");
        organizationDO1.setIsEnable(false);
        organizationDO1.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO1.setModifiedUserGuid("modifiedUserGuid");
        organizationDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setIsSelfBuildItems(0);
        organizationDO1.setIsBuAccounts(0);
        organizationDO1.setIsShowCash(0);
        final List<OrganizationDO> organizationDOList = Arrays.asList(organizationDO1);
        when(mockStoreMapstruct.organizationList2DTOList(organizationDOList)).thenReturn(storeDTOS);

        // Run the test
        final List<StoreDTO> result = storeServiceImplUnderTest.queryStoreByIdList(Arrays.asList("value"));

        // Verify the results
    }

    @Test
    public void testQueryStoreByIdList_OrganizationMapperReturnsNoItems() {
        // Setup
        when(mockOrganizationMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure StoreMapstruct.organizationList2DTOList(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        brandDTO.setStoreList(Arrays.asList(new StoreDTO()));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO);
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOList = Arrays.asList(organizationDO);
        when(mockStoreMapstruct.organizationList2DTOList(organizationDOList)).thenReturn(storeDTOS);

        // Run the test
        final List<StoreDTO> result = storeServiceImplUnderTest.queryStoreByIdList(Arrays.asList("value"));

        // Verify the results
    }

    @Test
    public void testQueryStoreByIdList_StoreMapstructReturnsNoItems() {
        // Setup
        // Configure OrganizationMapper.selectList(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOS = Arrays.asList(organizationDO);
        when(mockOrganizationMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(organizationDOS);

        // Configure StoreMapstruct.organizationList2DTOList(...).
        final OrganizationDO organizationDO1 = new OrganizationDO();
        organizationDO1.setGuid("guid");
        organizationDO1.setCode("code");
        organizationDO1.setType(0);
        organizationDO1.setName("name");
        organizationDO1.setParentIds("parentIds");
        organizationDO1.setProvinceCode("provinceCode");
        organizationDO1.setProvinceName("provinceName");
        organizationDO1.setCityCode("cityCode");
        organizationDO1.setCityName("cityName");
        organizationDO1.setIsEnable(false);
        organizationDO1.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO1.setModifiedUserGuid("modifiedUserGuid");
        organizationDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setIsSelfBuildItems(0);
        organizationDO1.setIsBuAccounts(0);
        organizationDO1.setIsShowCash(0);
        final List<OrganizationDO> organizationDOList = Arrays.asList(organizationDO1);
        when(mockStoreMapstruct.organizationList2DTOList(organizationDOList)).thenReturn(Collections.emptyList());

        // Run the test
        final List<StoreDTO> result = storeServiceImplUnderTest.queryStoreByIdList(Arrays.asList("value"));

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testQueryStoreByIdListAndBrandId() {
        // Setup
        final SingleDataDTO singleDataDTO = new SingleDataDTO("data", Arrays.asList("value"));

        // Configure OrganizationMapper.selectList(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOS = Arrays.asList(organizationDO);
        when(mockOrganizationMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(organizationDOS);

        // Configure StoreMapstruct.organizationList2DTOList(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        brandDTO.setStoreList(Arrays.asList(new StoreDTO()));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO);
        final OrganizationDO organizationDO1 = new OrganizationDO();
        organizationDO1.setGuid("guid");
        organizationDO1.setCode("code");
        organizationDO1.setType(0);
        organizationDO1.setName("name");
        organizationDO1.setParentIds("parentIds");
        organizationDO1.setProvinceCode("provinceCode");
        organizationDO1.setProvinceName("provinceName");
        organizationDO1.setCityCode("cityCode");
        organizationDO1.setCityName("cityName");
        organizationDO1.setIsEnable(false);
        organizationDO1.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO1.setModifiedUserGuid("modifiedUserGuid");
        organizationDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setIsSelfBuildItems(0);
        organizationDO1.setIsBuAccounts(0);
        organizationDO1.setIsShowCash(0);
        final List<OrganizationDO> organizationDOList = Arrays.asList(organizationDO1);
        when(mockStoreMapstruct.organizationList2DTOList(organizationDOList)).thenReturn(storeDTOS);

        // Configure StoreBrandMapper.selectList(...).
        final StoreBrandDO storeBrandDO = new StoreBrandDO();
        storeBrandDO.setId(0L);
        storeBrandDO.setGuid("07c02081-ef10-4f32-bc79-55e724f415bc");
        storeBrandDO.setStoreGuid("storeGuid");
        storeBrandDO.setBrandGuid("belongBrandGuid");
        storeBrandDO.setModifiedUserGuid("modifiedUserGuid");
        final List<StoreBrandDO> storeBrandDOS = Arrays.asList(storeBrandDO);
        when(mockStoreBrandMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(storeBrandDOS);

        // Run the test
        final List<StoreDTO> result = storeServiceImplUnderTest.queryStoreByIdListAndBrandId(singleDataDTO);

        // Verify the results
    }

    @Test
    public void testQueryStoreByIdListAndBrandId_OrganizationMapperReturnsNoItems() {
        // Setup
        final SingleDataDTO singleDataDTO = new SingleDataDTO("data", Arrays.asList("value"));
        when(mockOrganizationMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure StoreMapstruct.organizationList2DTOList(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        brandDTO.setStoreList(Arrays.asList(new StoreDTO()));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO);
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOList = Arrays.asList(organizationDO);
        when(mockStoreMapstruct.organizationList2DTOList(organizationDOList)).thenReturn(storeDTOS);

        // Configure StoreBrandMapper.selectList(...).
        final StoreBrandDO storeBrandDO = new StoreBrandDO();
        storeBrandDO.setId(0L);
        storeBrandDO.setGuid("07c02081-ef10-4f32-bc79-55e724f415bc");
        storeBrandDO.setStoreGuid("storeGuid");
        storeBrandDO.setBrandGuid("belongBrandGuid");
        storeBrandDO.setModifiedUserGuid("modifiedUserGuid");
        final List<StoreBrandDO> storeBrandDOS = Arrays.asList(storeBrandDO);
        when(mockStoreBrandMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(storeBrandDOS);

        // Run the test
        final List<StoreDTO> result = storeServiceImplUnderTest.queryStoreByIdListAndBrandId(singleDataDTO);

        // Verify the results
    }

    @Test
    public void testQueryStoreByIdListAndBrandId_StoreMapstructReturnsNoItems() {
        // Setup
        final SingleDataDTO singleDataDTO = new SingleDataDTO("data", Arrays.asList("value"));

        // Configure OrganizationMapper.selectList(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOS = Arrays.asList(organizationDO);
        when(mockOrganizationMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(organizationDOS);

        // Configure StoreMapstruct.organizationList2DTOList(...).
        final OrganizationDO organizationDO1 = new OrganizationDO();
        organizationDO1.setGuid("guid");
        organizationDO1.setCode("code");
        organizationDO1.setType(0);
        organizationDO1.setName("name");
        organizationDO1.setParentIds("parentIds");
        organizationDO1.setProvinceCode("provinceCode");
        organizationDO1.setProvinceName("provinceName");
        organizationDO1.setCityCode("cityCode");
        organizationDO1.setCityName("cityName");
        organizationDO1.setIsEnable(false);
        organizationDO1.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO1.setModifiedUserGuid("modifiedUserGuid");
        organizationDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setIsSelfBuildItems(0);
        organizationDO1.setIsBuAccounts(0);
        organizationDO1.setIsShowCash(0);
        final List<OrganizationDO> organizationDOList = Arrays.asList(organizationDO1);
        when(mockStoreMapstruct.organizationList2DTOList(organizationDOList)).thenReturn(Collections.emptyList());

        // Configure StoreBrandMapper.selectList(...).
        final StoreBrandDO storeBrandDO = new StoreBrandDO();
        storeBrandDO.setId(0L);
        storeBrandDO.setGuid("07c02081-ef10-4f32-bc79-55e724f415bc");
        storeBrandDO.setStoreGuid("storeGuid");
        storeBrandDO.setBrandGuid("belongBrandGuid");
        storeBrandDO.setModifiedUserGuid("modifiedUserGuid");
        final List<StoreBrandDO> storeBrandDOS = Arrays.asList(storeBrandDO);
        when(mockStoreBrandMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(storeBrandDOS);

        // Run the test
        final List<StoreDTO> result = storeServiceImplUnderTest.queryStoreByIdListAndBrandId(singleDataDTO);

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testQueryStoreByIdListAndBrandId_StoreBrandMapperReturnsNoItems() {
        // Setup
        final SingleDataDTO singleDataDTO = new SingleDataDTO("data", Arrays.asList("value"));

        // Configure OrganizationMapper.selectList(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOS = Arrays.asList(organizationDO);
        when(mockOrganizationMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(organizationDOS);

        // Configure StoreMapstruct.organizationList2DTOList(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        brandDTO.setStoreList(Arrays.asList(new StoreDTO()));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO);
        final OrganizationDO organizationDO1 = new OrganizationDO();
        organizationDO1.setGuid("guid");
        organizationDO1.setCode("code");
        organizationDO1.setType(0);
        organizationDO1.setName("name");
        organizationDO1.setParentIds("parentIds");
        organizationDO1.setProvinceCode("provinceCode");
        organizationDO1.setProvinceName("provinceName");
        organizationDO1.setCityCode("cityCode");
        organizationDO1.setCityName("cityName");
        organizationDO1.setIsEnable(false);
        organizationDO1.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO1.setModifiedUserGuid("modifiedUserGuid");
        organizationDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setIsSelfBuildItems(0);
        organizationDO1.setIsBuAccounts(0);
        organizationDO1.setIsShowCash(0);
        final List<OrganizationDO> organizationDOList = Arrays.asList(organizationDO1);
        when(mockStoreMapstruct.organizationList2DTOList(organizationDOList)).thenReturn(storeDTOS);

        when(mockStoreBrandMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<StoreDTO> result = storeServiceImplUnderTest.queryStoreByIdListAndBrandId(singleDataDTO);

        // Verify the results
    }

    @Test
    public void testQueryStoreDetailByIdList() throws Exception {
        // Setup
        // Configure OrganizationMapper.queryStoreDetail(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        brandDTO.setStoreList(Arrays.asList(new StoreDTO()));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO);
        when(mockOrganizationMapper.queryStoreDetail(Arrays.asList("value"))).thenReturn(storeDTOS);

        // Run the test
        final List<StoreDTO> result = storeServiceImplUnderTest.queryStoreDetailByIdList(Arrays.asList("value"));

        // Verify the results
    }

    @Test
    public void testQueryStoreDetailByIdList_OrganizationMapperReturnsNoItems() {
        // Setup
        when(mockOrganizationMapper.queryStoreDetail(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        final List<StoreDTO> result = storeServiceImplUnderTest.queryStoreDetailByIdList(Arrays.asList("value"));

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testQueryStoreAndBrandByIdList() throws Exception {
        // Setup
        // Configure OrganizationMapper.queryStoreDetail(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        brandDTO.setStoreList(Arrays.asList(new StoreDTO()));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO);
        when(mockOrganizationMapper.queryStoreDetail(Arrays.asList("value"))).thenReturn(storeDTOS);

        // Configure StoreBrandMapper.selectList(...).
        final StoreBrandDO storeBrandDO = new StoreBrandDO();
        storeBrandDO.setId(0L);
        storeBrandDO.setGuid("07c02081-ef10-4f32-bc79-55e724f415bc");
        storeBrandDO.setStoreGuid("storeGuid");
        storeBrandDO.setBrandGuid("belongBrandGuid");
        storeBrandDO.setModifiedUserGuid("modifiedUserGuid");
        final List<StoreBrandDO> storeBrandDOS = Arrays.asList(storeBrandDO);
        when(mockStoreBrandMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(storeBrandDOS);

        // Configure BrandMapper.selectList(...).
        final BrandDO brandDO = new BrandDO();
        brandDO.setId(0L);
        brandDO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDO.setName("belongBrandName");
        brandDO.setDescription("description");
        brandDO.setIsEnable(false);
        final List<BrandDO> brandDOS = Arrays.asList(brandDO);
        when(mockBrandMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(brandDOS);

        // Run the test
        final List<StoreDTO> result = storeServiceImplUnderTest.queryStoreAndBrandByIdList(Arrays.asList("value"));

        // Verify the results
    }

    @Test
    public void testQueryStoreAndBrandByIdList_OrganizationMapperReturnsNoItems() {
        // Setup
        when(mockOrganizationMapper.queryStoreDetail(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Configure StoreBrandMapper.selectList(...).
        final StoreBrandDO storeBrandDO = new StoreBrandDO();
        storeBrandDO.setId(0L);
        storeBrandDO.setGuid("07c02081-ef10-4f32-bc79-55e724f415bc");
        storeBrandDO.setStoreGuid("storeGuid");
        storeBrandDO.setBrandGuid("belongBrandGuid");
        storeBrandDO.setModifiedUserGuid("modifiedUserGuid");
        final List<StoreBrandDO> storeBrandDOS = Arrays.asList(storeBrandDO);
        when(mockStoreBrandMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(storeBrandDOS);

        // Configure BrandMapper.selectList(...).
        final BrandDO brandDO = new BrandDO();
        brandDO.setId(0L);
        brandDO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDO.setName("belongBrandName");
        brandDO.setDescription("description");
        brandDO.setIsEnable(false);
        final List<BrandDO> brandDOS = Arrays.asList(brandDO);
        when(mockBrandMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(brandDOS);

        // Run the test
        final List<StoreDTO> result = storeServiceImplUnderTest.queryStoreAndBrandByIdList(Arrays.asList("value"));

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testQueryStoreAndBrandByIdList_StoreBrandMapperReturnsNoItems() {
        // Setup
        // Configure OrganizationMapper.queryStoreDetail(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        brandDTO.setStoreList(Arrays.asList(new StoreDTO()));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO);
        when(mockOrganizationMapper.queryStoreDetail(Arrays.asList("value"))).thenReturn(storeDTOS);

        when(mockStoreBrandMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<StoreDTO> result = storeServiceImplUnderTest.queryStoreAndBrandByIdList(Arrays.asList("value"));

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testQueryStoreAndBrandByIdList_BrandMapperReturnsNoItems() {
        // Setup
        // Configure OrganizationMapper.queryStoreDetail(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        brandDTO.setStoreList(Arrays.asList(new StoreDTO()));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO);
        when(mockOrganizationMapper.queryStoreDetail(Arrays.asList("value"))).thenReturn(storeDTOS);

        // Configure StoreBrandMapper.selectList(...).
        final StoreBrandDO storeBrandDO = new StoreBrandDO();
        storeBrandDO.setId(0L);
        storeBrandDO.setGuid("07c02081-ef10-4f32-bc79-55e724f415bc");
        storeBrandDO.setStoreGuid("storeGuid");
        storeBrandDO.setBrandGuid("belongBrandGuid");
        storeBrandDO.setModifiedUserGuid("modifiedUserGuid");
        final List<StoreBrandDO> storeBrandDOS = Arrays.asList(storeBrandDO);
        when(mockStoreBrandMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(storeBrandDOS);

        when(mockBrandMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<StoreDTO> result = storeServiceImplUnderTest.queryStoreAndBrandByIdList(Arrays.asList("value"));

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testQueryAllStore() throws Exception {
        // Setup
        // Configure OrganizationMapper.selectList(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOS = Arrays.asList(organizationDO);
        when(mockOrganizationMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(organizationDOS);

        // Configure StoreMapstruct.organizationList2DTOList(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        brandDTO.setStoreList(Arrays.asList(new StoreDTO()));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO);
        final OrganizationDO organizationDO1 = new OrganizationDO();
        organizationDO1.setGuid("guid");
        organizationDO1.setCode("code");
        organizationDO1.setType(0);
        organizationDO1.setName("name");
        organizationDO1.setParentIds("parentIds");
        organizationDO1.setProvinceCode("provinceCode");
        organizationDO1.setProvinceName("provinceName");
        organizationDO1.setCityCode("cityCode");
        organizationDO1.setCityName("cityName");
        organizationDO1.setIsEnable(false);
        organizationDO1.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO1.setModifiedUserGuid("modifiedUserGuid");
        organizationDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setIsSelfBuildItems(0);
        organizationDO1.setIsBuAccounts(0);
        organizationDO1.setIsShowCash(0);
        final List<OrganizationDO> organizationDOList = Arrays.asList(organizationDO1);
        when(mockStoreMapstruct.organizationList2DTOList(organizationDOList)).thenReturn(storeDTOS);

        // Run the test
        final List<StoreDTO> result = storeServiceImplUnderTest.queryAllStore();

        // Verify the results
    }

    @Test
    public void testQueryAllStore_OrganizationMapperReturnsNoItems() {
        // Setup
        when(mockOrganizationMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure StoreMapstruct.organizationList2DTOList(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        brandDTO.setStoreList(Arrays.asList(new StoreDTO()));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO);
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOList = Arrays.asList(organizationDO);
        when(mockStoreMapstruct.organizationList2DTOList(organizationDOList)).thenReturn(storeDTOS);

        // Run the test
        final List<StoreDTO> result = storeServiceImplUnderTest.queryAllStore();

        // Verify the results
    }

    @Test
    public void testQueryAllStore_StoreMapstructReturnsNoItems() {
        // Setup
        // Configure OrganizationMapper.selectList(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOS = Arrays.asList(organizationDO);
        when(mockOrganizationMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(organizationDOS);

        // Configure StoreMapstruct.organizationList2DTOList(...).
        final OrganizationDO organizationDO1 = new OrganizationDO();
        organizationDO1.setGuid("guid");
        organizationDO1.setCode("code");
        organizationDO1.setType(0);
        organizationDO1.setName("name");
        organizationDO1.setParentIds("parentIds");
        organizationDO1.setProvinceCode("provinceCode");
        organizationDO1.setProvinceName("provinceName");
        organizationDO1.setCityCode("cityCode");
        organizationDO1.setCityName("cityName");
        organizationDO1.setIsEnable(false);
        organizationDO1.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO1.setModifiedUserGuid("modifiedUserGuid");
        organizationDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setIsSelfBuildItems(0);
        organizationDO1.setIsBuAccounts(0);
        organizationDO1.setIsShowCash(0);
        final List<OrganizationDO> organizationDOList = Arrays.asList(organizationDO1);
        when(mockStoreMapstruct.organizationList2DTOList(organizationDOList)).thenReturn(Collections.emptyList());

        // Run the test
        final List<StoreDTO> result = storeServiceImplUnderTest.queryAllStore();

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testQueryAllStoreGuid() throws Exception {
        // Setup
        // Configure OrganizationMapper.selectList(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOS = Arrays.asList(organizationDO);
        when(mockOrganizationMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(organizationDOS);

        // Run the test
        final List<String> result = storeServiceImplUnderTest.queryAllStoreGuid();

        // Verify the results
        assertEquals(Arrays.asList("value"), result);
    }

    @Test
    public void testQueryAllStoreGuid_OrganizationMapperReturnsNoItems() {
        // Setup
        when(mockOrganizationMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<String> result = storeServiceImplUnderTest.queryAllStoreGuid();

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testQueryStoreByCondition() throws Exception {
        // Setup
        final QueryStoreDTO queryStoreDTO = new QueryStoreDTO();
        queryStoreDTO.setCurrentPage(0L);
        queryStoreDTO.setPageSize(0L);
        queryStoreDTO.setTotalCount(0L);
        queryStoreDTO.setStoreGuidList(Arrays.asList("value"));
        queryStoreDTO.setIsEnable(0);

        // Configure OrganizationMapper.selectByCondition(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOS = Arrays.asList(organizationDO);
        final QueryStoreDTO storeParserDTO = new QueryStoreDTO();
        storeParserDTO.setCurrentPage(0L);
        storeParserDTO.setPageSize(0L);
        storeParserDTO.setTotalCount(0L);
        storeParserDTO.setStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setIsEnable(0);
        when(mockOrganizationMapper.selectByCondition(storeParserDTO)).thenReturn(organizationDOS);

        // Configure StoreMapstruct.organizationList2DTOList(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        brandDTO.setStoreList(Arrays.asList(new StoreDTO()));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO);
        final OrganizationDO organizationDO1 = new OrganizationDO();
        organizationDO1.setGuid("guid");
        organizationDO1.setCode("code");
        organizationDO1.setType(0);
        organizationDO1.setName("name");
        organizationDO1.setParentIds("parentIds");
        organizationDO1.setProvinceCode("provinceCode");
        organizationDO1.setProvinceName("provinceName");
        organizationDO1.setCityCode("cityCode");
        organizationDO1.setCityName("cityName");
        organizationDO1.setIsEnable(false);
        organizationDO1.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO1.setModifiedUserGuid("modifiedUserGuid");
        organizationDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setIsSelfBuildItems(0);
        organizationDO1.setIsBuAccounts(0);
        organizationDO1.setIsShowCash(0);
        final List<OrganizationDO> organizationDOList = Arrays.asList(organizationDO1);
        when(mockStoreMapstruct.organizationList2DTOList(organizationDOList)).thenReturn(storeDTOS);

        // Configure StoreBrandMapper.selectList(...).
        final StoreBrandDO storeBrandDO = new StoreBrandDO();
        storeBrandDO.setId(0L);
        storeBrandDO.setGuid("07c02081-ef10-4f32-bc79-55e724f415bc");
        storeBrandDO.setStoreGuid("storeGuid");
        storeBrandDO.setBrandGuid("belongBrandGuid");
        storeBrandDO.setModifiedUserGuid("modifiedUserGuid");
        final List<StoreBrandDO> storeBrandDOS = Arrays.asList(storeBrandDO);
        when(mockStoreBrandMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(storeBrandDOS);

        // Configure BrandMapper.selectList(...).
        final BrandDO brandDO = new BrandDO();
        brandDO.setId(0L);
        brandDO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDO.setName("belongBrandName");
        brandDO.setDescription("description");
        brandDO.setIsEnable(false);
        final List<BrandDO> brandDOS = Arrays.asList(brandDO);
        when(mockBrandMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(brandDOS);

        // Run the test
        final Page<StoreDTO> result = storeServiceImplUnderTest.queryStoreByCondition(queryStoreDTO);

        // Verify the results
    }

    @Test
    public void testQueryStoreByCondition_OrganizationMapperReturnsNoItems() {
        // Setup
        final QueryStoreDTO queryStoreDTO = new QueryStoreDTO();
        queryStoreDTO.setCurrentPage(0L);
        queryStoreDTO.setPageSize(0L);
        queryStoreDTO.setTotalCount(0L);
        queryStoreDTO.setStoreGuidList(Arrays.asList("value"));
        queryStoreDTO.setIsEnable(0);

        // Configure OrganizationMapper.selectByCondition(...).
        final QueryStoreDTO storeParserDTO = new QueryStoreDTO();
        storeParserDTO.setCurrentPage(0L);
        storeParserDTO.setPageSize(0L);
        storeParserDTO.setTotalCount(0L);
        storeParserDTO.setStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setIsEnable(0);
        when(mockOrganizationMapper.selectByCondition(storeParserDTO)).thenReturn(Collections.emptyList());

        // Configure StoreMapstruct.organizationList2DTOList(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        brandDTO.setStoreList(Arrays.asList(new StoreDTO()));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO);
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOList = Arrays.asList(organizationDO);
        when(mockStoreMapstruct.organizationList2DTOList(organizationDOList)).thenReturn(storeDTOS);

        // Configure StoreBrandMapper.selectList(...).
        final StoreBrandDO storeBrandDO = new StoreBrandDO();
        storeBrandDO.setId(0L);
        storeBrandDO.setGuid("07c02081-ef10-4f32-bc79-55e724f415bc");
        storeBrandDO.setStoreGuid("storeGuid");
        storeBrandDO.setBrandGuid("belongBrandGuid");
        storeBrandDO.setModifiedUserGuid("modifiedUserGuid");
        final List<StoreBrandDO> storeBrandDOS = Arrays.asList(storeBrandDO);
        when(mockStoreBrandMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(storeBrandDOS);

        // Configure BrandMapper.selectList(...).
        final BrandDO brandDO = new BrandDO();
        brandDO.setId(0L);
        brandDO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDO.setName("belongBrandName");
        brandDO.setDescription("description");
        brandDO.setIsEnable(false);
        final List<BrandDO> brandDOS = Arrays.asList(brandDO);
        when(mockBrandMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(brandDOS);

        // Run the test
        final Page<StoreDTO> result = storeServiceImplUnderTest.queryStoreByCondition(queryStoreDTO);

        // Verify the results
    }

    @Test
    public void testQueryStoreByCondition_StoreMapstructReturnsNoItems() {
        // Setup
        final QueryStoreDTO queryStoreDTO = new QueryStoreDTO();
        queryStoreDTO.setCurrentPage(0L);
        queryStoreDTO.setPageSize(0L);
        queryStoreDTO.setTotalCount(0L);
        queryStoreDTO.setStoreGuidList(Arrays.asList("value"));
        queryStoreDTO.setIsEnable(0);

        // Configure OrganizationMapper.selectByCondition(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOS = Arrays.asList(organizationDO);
        final QueryStoreDTO storeParserDTO = new QueryStoreDTO();
        storeParserDTO.setCurrentPage(0L);
        storeParserDTO.setPageSize(0L);
        storeParserDTO.setTotalCount(0L);
        storeParserDTO.setStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setIsEnable(0);
        when(mockOrganizationMapper.selectByCondition(storeParserDTO)).thenReturn(organizationDOS);

        // Configure StoreMapstruct.organizationList2DTOList(...).
        final OrganizationDO organizationDO1 = new OrganizationDO();
        organizationDO1.setGuid("guid");
        organizationDO1.setCode("code");
        organizationDO1.setType(0);
        organizationDO1.setName("name");
        organizationDO1.setParentIds("parentIds");
        organizationDO1.setProvinceCode("provinceCode");
        organizationDO1.setProvinceName("provinceName");
        organizationDO1.setCityCode("cityCode");
        organizationDO1.setCityName("cityName");
        organizationDO1.setIsEnable(false);
        organizationDO1.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO1.setModifiedUserGuid("modifiedUserGuid");
        organizationDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setIsSelfBuildItems(0);
        organizationDO1.setIsBuAccounts(0);
        organizationDO1.setIsShowCash(0);
        final List<OrganizationDO> organizationDOList = Arrays.asList(organizationDO1);
        when(mockStoreMapstruct.organizationList2DTOList(organizationDOList)).thenReturn(Collections.emptyList());

        // Run the test
        final Page<StoreDTO> result = storeServiceImplUnderTest.queryStoreByCondition(queryStoreDTO);

        // Verify the results
    }

    @Test
    public void testQueryStoreByCondition_StoreBrandMapperReturnsNoItems() {
        // Setup
        final QueryStoreDTO queryStoreDTO = new QueryStoreDTO();
        queryStoreDTO.setCurrentPage(0L);
        queryStoreDTO.setPageSize(0L);
        queryStoreDTO.setTotalCount(0L);
        queryStoreDTO.setStoreGuidList(Arrays.asList("value"));
        queryStoreDTO.setIsEnable(0);

        // Configure OrganizationMapper.selectByCondition(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOS = Arrays.asList(organizationDO);
        final QueryStoreDTO storeParserDTO = new QueryStoreDTO();
        storeParserDTO.setCurrentPage(0L);
        storeParserDTO.setPageSize(0L);
        storeParserDTO.setTotalCount(0L);
        storeParserDTO.setStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setIsEnable(0);
        when(mockOrganizationMapper.selectByCondition(storeParserDTO)).thenReturn(organizationDOS);

        // Configure StoreMapstruct.organizationList2DTOList(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        brandDTO.setStoreList(Arrays.asList(new StoreDTO()));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO);
        final OrganizationDO organizationDO1 = new OrganizationDO();
        organizationDO1.setGuid("guid");
        organizationDO1.setCode("code");
        organizationDO1.setType(0);
        organizationDO1.setName("name");
        organizationDO1.setParentIds("parentIds");
        organizationDO1.setProvinceCode("provinceCode");
        organizationDO1.setProvinceName("provinceName");
        organizationDO1.setCityCode("cityCode");
        organizationDO1.setCityName("cityName");
        organizationDO1.setIsEnable(false);
        organizationDO1.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO1.setModifiedUserGuid("modifiedUserGuid");
        organizationDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setIsSelfBuildItems(0);
        organizationDO1.setIsBuAccounts(0);
        organizationDO1.setIsShowCash(0);
        final List<OrganizationDO> organizationDOList = Arrays.asList(organizationDO1);
        when(mockStoreMapstruct.organizationList2DTOList(organizationDOList)).thenReturn(storeDTOS);

        when(mockStoreBrandMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final Page<StoreDTO> result = storeServiceImplUnderTest.queryStoreByCondition(queryStoreDTO);

        // Verify the results
    }

    @Test
    public void testQueryStoreByCondition_BrandMapperReturnsNoItems() {
        // Setup
        final QueryStoreDTO queryStoreDTO = new QueryStoreDTO();
        queryStoreDTO.setCurrentPage(0L);
        queryStoreDTO.setPageSize(0L);
        queryStoreDTO.setTotalCount(0L);
        queryStoreDTO.setStoreGuidList(Arrays.asList("value"));
        queryStoreDTO.setIsEnable(0);

        // Configure OrganizationMapper.selectByCondition(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOS = Arrays.asList(organizationDO);
        final QueryStoreDTO storeParserDTO = new QueryStoreDTO();
        storeParserDTO.setCurrentPage(0L);
        storeParserDTO.setPageSize(0L);
        storeParserDTO.setTotalCount(0L);
        storeParserDTO.setStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setIsEnable(0);
        when(mockOrganizationMapper.selectByCondition(storeParserDTO)).thenReturn(organizationDOS);

        // Configure StoreMapstruct.organizationList2DTOList(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        brandDTO.setStoreList(Arrays.asList(new StoreDTO()));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO);
        final OrganizationDO organizationDO1 = new OrganizationDO();
        organizationDO1.setGuid("guid");
        organizationDO1.setCode("code");
        organizationDO1.setType(0);
        organizationDO1.setName("name");
        organizationDO1.setParentIds("parentIds");
        organizationDO1.setProvinceCode("provinceCode");
        organizationDO1.setProvinceName("provinceName");
        organizationDO1.setCityCode("cityCode");
        organizationDO1.setCityName("cityName");
        organizationDO1.setIsEnable(false);
        organizationDO1.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO1.setModifiedUserGuid("modifiedUserGuid");
        organizationDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setIsSelfBuildItems(0);
        organizationDO1.setIsBuAccounts(0);
        organizationDO1.setIsShowCash(0);
        final List<OrganizationDO> organizationDOList = Arrays.asList(organizationDO1);
        when(mockStoreMapstruct.organizationList2DTOList(organizationDOList)).thenReturn(storeDTOS);

        // Configure StoreBrandMapper.selectList(...).
        final StoreBrandDO storeBrandDO = new StoreBrandDO();
        storeBrandDO.setId(0L);
        storeBrandDO.setGuid("07c02081-ef10-4f32-bc79-55e724f415bc");
        storeBrandDO.setStoreGuid("storeGuid");
        storeBrandDO.setBrandGuid("belongBrandGuid");
        storeBrandDO.setModifiedUserGuid("modifiedUserGuid");
        final List<StoreBrandDO> storeBrandDOS = Arrays.asList(storeBrandDO);
        when(mockStoreBrandMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(storeBrandDOS);

        when(mockBrandMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final Page<StoreDTO> result = storeServiceImplUnderTest.queryStoreByCondition(queryStoreDTO);

        // Verify the results
    }

    @Test
    public void testQueryStoreByCityAndBrand() throws Exception {
        // Setup
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        brandDTO.setStoreList(Arrays.asList(new StoreDTO()));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));

        // Configure StoreBrandMapper.selectList(...).
        final StoreBrandDO storeBrandDO = new StoreBrandDO();
        storeBrandDO.setId(0L);
        storeBrandDO.setGuid("07c02081-ef10-4f32-bc79-55e724f415bc");
        storeBrandDO.setStoreGuid("storeGuid");
        storeBrandDO.setBrandGuid("belongBrandGuid");
        storeBrandDO.setModifiedUserGuid("modifiedUserGuid");
        final List<StoreBrandDO> storeBrandDOS = Arrays.asList(storeBrandDO);
        when(mockStoreBrandMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(storeBrandDOS);

        // Configure StoreMapstruct.organizationList2DTOList(...).
        final StoreDTO storeDTO1 = new StoreDTO();
        storeDTO1.setGuid("guid");
        storeDTO1.setCode("code");
        storeDTO1.setName("name");
        storeDTO1.setBelongBrandGuid("belongBrandGuid");
        storeDTO1.setBelongBrandName("belongBrandName");
        storeDTO1.setParentIds("parentIds");
        storeDTO1.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO1.setContactTel("contactTel");
        storeDTO1.setCityCode("cityCode");
        storeDTO1.setCityName("cityName");
        storeDTO1.setAddressDetail("addressDetail");
        storeDTO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO1.setCreateUserGuid("createUserGuid");
        storeDTO1.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO1 = new StoreProductDTO();
        storeProductDTO1.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO1.setProductDTOList(Arrays.asList(storeProductDTO1));
        final BrandDTO brandDTO1 = new BrandDTO();
        brandDTO1.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO1.setName("belongBrandName");
        brandDTO1.setLogoUrl("brandLogoUrl");
        brandDTO1.setIsEnable(false);
        brandDTO1.setStoreList(Arrays.asList(new StoreDTO()));
        brandDTO1.setIsBuAccounts(0);
        brandDTO1.setIsShowCash(0);
        brandDTO1.setIsMultiHandover(0);
        storeDTO1.setBrandDTOList(Arrays.asList(brandDTO1));
        storeDTO1.setMchntTypeCode("mchntTypeCode");
        storeDTO1.setIsSelfBuildItems(0);
        storeDTO1.setIsBuAccounts(0);
        storeDTO1.setIsShowCash(0);
        storeDTO1.setIsMultiHandover(0);
        storeDTO1.setBusinessDay(LocalDate.of(2020, 1, 1));
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO1);
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOList = Arrays.asList(organizationDO);
        when(mockStoreMapstruct.organizationList2DTOList(organizationDOList)).thenReturn(storeDTOS);

        // Run the test
        final List<StoreDTO> result = storeServiceImplUnderTest.queryStoreByCityAndBrand(storeDTO);

        // Verify the results
    }

    @Test
    public void testQueryStoreByCityAndBrand_StoreBrandMapperReturnsNoItems() {
        // Setup
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        brandDTO.setStoreList(Arrays.asList(new StoreDTO()));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));

        when(mockStoreBrandMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<StoreDTO> result = storeServiceImplUnderTest.queryStoreByCityAndBrand(storeDTO);

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testQueryStoreByCityAndBrand_StoreMapstructReturnsNoItems() {
        // Setup
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        brandDTO.setStoreList(Arrays.asList(new StoreDTO()));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));

        // Configure StoreBrandMapper.selectList(...).
        final StoreBrandDO storeBrandDO = new StoreBrandDO();
        storeBrandDO.setId(0L);
        storeBrandDO.setGuid("07c02081-ef10-4f32-bc79-55e724f415bc");
        storeBrandDO.setStoreGuid("storeGuid");
        storeBrandDO.setBrandGuid("belongBrandGuid");
        storeBrandDO.setModifiedUserGuid("modifiedUserGuid");
        final List<StoreBrandDO> storeBrandDOS = Arrays.asList(storeBrandDO);
        when(mockStoreBrandMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(storeBrandDOS);

        // Configure StoreMapstruct.organizationList2DTOList(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOList = Arrays.asList(organizationDO);
        when(mockStoreMapstruct.organizationList2DTOList(organizationDOList)).thenReturn(Collections.emptyList());

        // Run the test
        final List<StoreDTO> result = storeServiceImplUnderTest.queryStoreByCityAndBrand(storeDTO);

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testQueryBusinessDay() throws Exception {
        // Setup
        final BusinessDateReqDTO businessDateReqDTO = new BusinessDateReqDTO();
        businessDateReqDTO.setQueryDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        businessDateReqDTO.setStoreGuidList(Arrays.asList("value"));
        businessDateReqDTO.setStoreGuid("storeGuid");

        // Configure OrganizationMapper.selectList(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOS = Arrays.asList(organizationDO);
        when(mockOrganizationMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(organizationDOS);

        // Configure StoreMapstruct.organizationList2DTOList(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        brandDTO.setStoreList(Arrays.asList(new StoreDTO()));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO);
        final OrganizationDO organizationDO1 = new OrganizationDO();
        organizationDO1.setGuid("guid");
        organizationDO1.setCode("code");
        organizationDO1.setType(0);
        organizationDO1.setName("name");
        organizationDO1.setParentIds("parentIds");
        organizationDO1.setProvinceCode("provinceCode");
        organizationDO1.setProvinceName("provinceName");
        organizationDO1.setCityCode("cityCode");
        organizationDO1.setCityName("cityName");
        organizationDO1.setIsEnable(false);
        organizationDO1.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO1.setModifiedUserGuid("modifiedUserGuid");
        organizationDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setIsSelfBuildItems(0);
        organizationDO1.setIsBuAccounts(0);
        organizationDO1.setIsShowCash(0);
        final List<OrganizationDO> organizationDOList = Arrays.asList(organizationDO1);
        when(mockStoreMapstruct.organizationList2DTOList(organizationDOList)).thenReturn(storeDTOS);

        // Run the test
        final LocalDate result = storeServiceImplUnderTest.queryBusinessDay(businessDateReqDTO);

        // Verify the results
        assertEquals(LocalDate.of(2020, 1, 1), result);
    }

    @Test
    public void testQueryBusinessDay_OrganizationMapperReturnsNoItems() {
        // Setup
        final BusinessDateReqDTO businessDateReqDTO = new BusinessDateReqDTO();
        businessDateReqDTO.setQueryDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        businessDateReqDTO.setStoreGuidList(Arrays.asList("value"));
        businessDateReqDTO.setStoreGuid("storeGuid");

        when(mockOrganizationMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure StoreMapstruct.organizationList2DTOList(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        brandDTO.setStoreList(Arrays.asList(new StoreDTO()));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO);
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOList = Arrays.asList(organizationDO);
        when(mockStoreMapstruct.organizationList2DTOList(organizationDOList)).thenReturn(storeDTOS);

        // Run the test
        final LocalDate result = storeServiceImplUnderTest.queryBusinessDay(businessDateReqDTO);

        // Verify the results
        assertEquals(LocalDate.of(2020, 1, 1), result);
    }

    @Test
    public void testQueryBusinessDay_StoreMapstructReturnsNoItems() {
        // Setup
        final BusinessDateReqDTO businessDateReqDTO = new BusinessDateReqDTO();
        businessDateReqDTO.setQueryDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        businessDateReqDTO.setStoreGuidList(Arrays.asList("value"));
        businessDateReqDTO.setStoreGuid("storeGuid");

        // Configure OrganizationMapper.selectList(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOS = Arrays.asList(organizationDO);
        when(mockOrganizationMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(organizationDOS);

        // Configure StoreMapstruct.organizationList2DTOList(...).
        final OrganizationDO organizationDO1 = new OrganizationDO();
        organizationDO1.setGuid("guid");
        organizationDO1.setCode("code");
        organizationDO1.setType(0);
        organizationDO1.setName("name");
        organizationDO1.setParentIds("parentIds");
        organizationDO1.setProvinceCode("provinceCode");
        organizationDO1.setProvinceName("provinceName");
        organizationDO1.setCityCode("cityCode");
        organizationDO1.setCityName("cityName");
        organizationDO1.setIsEnable(false);
        organizationDO1.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO1.setModifiedUserGuid("modifiedUserGuid");
        organizationDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setIsSelfBuildItems(0);
        organizationDO1.setIsBuAccounts(0);
        organizationDO1.setIsShowCash(0);
        final List<OrganizationDO> organizationDOList = Arrays.asList(organizationDO1);
        when(mockStoreMapstruct.organizationList2DTOList(organizationDOList)).thenReturn(Collections.emptyList());

        // Run the test
        final LocalDate result = storeServiceImplUnderTest.queryBusinessDay(businessDateReqDTO);

        // Verify the results
        assertEquals(LocalDate.of(2020, 1, 1), result);
    }

    @Test
    public void testQueryBusinessDayInfo() throws Exception {
        // Setup
        final BusinessDateReqDTO businessDateReqDTO = new BusinessDateReqDTO();
        businessDateReqDTO.setQueryDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        businessDateReqDTO.setStoreGuidList(Arrays.asList("value"));
        businessDateReqDTO.setStoreGuid("storeGuid");

        // Configure OrganizationMapper.selectOne(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        when(mockOrganizationMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(organizationDO);

        // Configure StoreMapstruct.organizationDO2DTO(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        brandDTO.setStoreList(Arrays.asList(new StoreDTO()));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));
        final OrganizationDO organizationDO1 = new OrganizationDO();
        organizationDO1.setGuid("guid");
        organizationDO1.setCode("code");
        organizationDO1.setType(0);
        organizationDO1.setName("name");
        organizationDO1.setParentIds("parentIds");
        organizationDO1.setProvinceCode("provinceCode");
        organizationDO1.setProvinceName("provinceName");
        organizationDO1.setCityCode("cityCode");
        organizationDO1.setCityName("cityName");
        organizationDO1.setIsEnable(false);
        organizationDO1.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO1.setModifiedUserGuid("modifiedUserGuid");
        organizationDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setIsSelfBuildItems(0);
        organizationDO1.setIsBuAccounts(0);
        organizationDO1.setIsShowCash(0);
        when(mockStoreMapstruct.organizationDO2DTO(organizationDO1, "belongBrandGuid")).thenReturn(storeDTO);

        // Run the test
        final StoreDTO result = storeServiceImplUnderTest.queryBusinessDayInfo(businessDateReqDTO);

        // Verify the results
    }

    @Test
    public void testQueryBusinessDate() throws Exception {
        // Setup
        final StoreBusinessDateDTO expectedResult = new StoreBusinessDateDTO(LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Configure OrganizationMapper.selectOne(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        when(mockOrganizationMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(organizationDO);

        // Configure StoreBrandMapper.selectOne(...).
        final StoreBrandDO storeBrandDO = new StoreBrandDO();
        storeBrandDO.setId(0L);
        storeBrandDO.setGuid("07c02081-ef10-4f32-bc79-55e724f415bc");
        storeBrandDO.setStoreGuid("storeGuid");
        storeBrandDO.setBrandGuid("belongBrandGuid");
        storeBrandDO.setModifiedUserGuid("modifiedUserGuid");
        when(mockStoreBrandMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(storeBrandDO);

        // Configure StoreMapstruct.organizationDO2DTO(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        brandDTO.setStoreList(Arrays.asList(new StoreDTO()));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));
        final OrganizationDO organizationDO1 = new OrganizationDO();
        organizationDO1.setGuid("guid");
        organizationDO1.setCode("code");
        organizationDO1.setType(0);
        organizationDO1.setName("name");
        organizationDO1.setParentIds("parentIds");
        organizationDO1.setProvinceCode("provinceCode");
        organizationDO1.setProvinceName("provinceName");
        organizationDO1.setCityCode("cityCode");
        organizationDO1.setCityName("cityName");
        organizationDO1.setIsEnable(false);
        organizationDO1.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO1.setModifiedUserGuid("modifiedUserGuid");
        organizationDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setIsSelfBuildItems(0);
        organizationDO1.setIsBuAccounts(0);
        organizationDO1.setIsShowCash(0);
        when(mockStoreMapstruct.organizationDO2DTO(organizationDO1, "belongBrandGuid")).thenReturn(storeDTO);

        // Run the test
        final StoreBusinessDateDTO result = storeServiceImplUnderTest.queryBusinessDate("storeGuid");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testQueryBusinessDate_StoreBrandMapperReturnsNull() {
        // Setup
        final StoreBusinessDateDTO expectedResult = new StoreBusinessDateDTO(LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Configure OrganizationMapper.selectOne(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        when(mockOrganizationMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(organizationDO);

        when(mockStoreBrandMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Configure StoreMapstruct.organizationDO2DTO(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        brandDTO.setStoreList(Arrays.asList(new StoreDTO()));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));
        final OrganizationDO organizationDO1 = new OrganizationDO();
        organizationDO1.setGuid("guid");
        organizationDO1.setCode("code");
        organizationDO1.setType(0);
        organizationDO1.setName("name");
        organizationDO1.setParentIds("parentIds");
        organizationDO1.setProvinceCode("provinceCode");
        organizationDO1.setProvinceName("provinceName");
        organizationDO1.setCityCode("cityCode");
        organizationDO1.setCityName("cityName");
        organizationDO1.setIsEnable(false);
        organizationDO1.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO1.setModifiedUserGuid("modifiedUserGuid");
        organizationDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setIsSelfBuildItems(0);
        organizationDO1.setIsBuAccounts(0);
        organizationDO1.setIsShowCash(0);
        when(mockStoreMapstruct.organizationDO2DTO(organizationDO1, "belongBrandGuid")).thenReturn(storeDTO);

        // Run the test
        final StoreBusinessDateDTO result = storeServiceImplUnderTest.queryBusinessDate("storeGuid");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetPadStartOrderInfo() throws Exception {
        // Setup
        // Configure OrganizationMapper.selectOne(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        when(mockOrganizationMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(organizationDO);

        // Configure StoreBrandMapper.selectOne(...).
        final StoreBrandDO storeBrandDO = new StoreBrandDO();
        storeBrandDO.setId(0L);
        storeBrandDO.setGuid("07c02081-ef10-4f32-bc79-55e724f415bc");
        storeBrandDO.setStoreGuid("storeGuid");
        storeBrandDO.setBrandGuid("belongBrandGuid");
        storeBrandDO.setModifiedUserGuid("modifiedUserGuid");
        when(mockStoreBrandMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(storeBrandDO);

        // Configure BrandMapper.selectOne(...).
        final BrandDO brandDO = new BrandDO();
        brandDO.setId(0L);
        brandDO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDO.setName("belongBrandName");
        brandDO.setDescription("description");
        brandDO.setIsEnable(false);
        when(mockBrandMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(brandDO);

        // Configure BrandMapstruct.brandDO2DTO(...).
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        storeDTO.setBrandDTOList(Arrays.asList(new BrandDTO()));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));
        brandDTO.setStoreList(Arrays.asList(storeDTO));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        final BrandDO brandDO1 = new BrandDO();
        brandDO1.setId(0L);
        brandDO1.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDO1.setName("belongBrandName");
        brandDO1.setDescription("description");
        brandDO1.setIsEnable(false);
        when(mockBrandMapstruct.brandDO2DTO(brandDO1)).thenReturn(brandDTO);

        // Configure WxStoreOrderConfigService.getStoreConfig(...).
        final WxOrderConfigDTO wxOrderConfigDTO = new WxOrderConfigDTO();
        wxOrderConfigDTO.setGuid("7ba6afa7-85c0-4bc9-bcf6-86cddfc5d325");
        wxOrderConfigDTO.setIsOrderOpen(0);
        wxOrderConfigDTO.setOrderModel(0);
        wxOrderConfigDTO.setTakingModel(0);
        wxOrderConfigDTO.setIsOnlinePayed(0);
        when(mockWxStoreOrderConfigService.getStoreConfig("storeGuid")).thenReturn(wxOrderConfigDTO);

        // Run the test
        final PadStartOrderRespDTO result = storeServiceImplUnderTest.getPadStartOrderInfo("storeGuid");

        // Verify the results
    }

    @Test
    public void testBatchUpdateBuAccounts() throws Exception {
        // Setup
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        brandDTO.setStoreList(Arrays.asList(new StoreDTO()));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));

        // Run the test
        storeServiceImplUnderTest.batchUpdateBuAccounts(Arrays.asList("value"), storeDTO);

        // Verify the results
        verify(mockOrganizationMapper).batchUpdateBuAccounts(eq(Arrays.asList("value")), any(StoreDTO.class));
    }

    @Test
    public void testCheckBindUpAccount() {
        // Setup
        final List<TableStatusDTO> tableStatusDTOS = Arrays.asList(
                new TableStatusDTO(LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, LocalDate.of(2020, 1, 1)));
        final HashMap<String, String> expectedResult = new HashMap<>();

        // Configure OrganizationMapper.selectList(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOS = Arrays.asList(organizationDO);
        when(mockOrganizationMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(organizationDOS);

        // Configure StoreMapstruct.organizationList2DTOList(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        brandDTO.setStoreList(Arrays.asList(new StoreDTO()));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO);
        final OrganizationDO organizationDO1 = new OrganizationDO();
        organizationDO1.setGuid("guid");
        organizationDO1.setCode("code");
        organizationDO1.setType(0);
        organizationDO1.setName("name");
        organizationDO1.setParentIds("parentIds");
        organizationDO1.setProvinceCode("provinceCode");
        organizationDO1.setProvinceName("provinceName");
        organizationDO1.setCityCode("cityCode");
        organizationDO1.setCityName("cityName");
        organizationDO1.setIsEnable(false);
        organizationDO1.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO1.setModifiedUserGuid("modifiedUserGuid");
        organizationDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setIsSelfBuildItems(0);
        organizationDO1.setIsBuAccounts(0);
        organizationDO1.setIsShowCash(0);
        final List<OrganizationDO> organizationDOList = Arrays.asList(organizationDO1);
        when(mockStoreMapstruct.organizationList2DTOList(organizationDOList)).thenReturn(storeDTOS);

        // Configure BusinessClient.queryBindUpAccountsLast(...).
        final BindupAccountsDo bindupAccountsDo = new BindupAccountsDo();
        bindupAccountsDo.setId(0L);
        bindupAccountsDo.setBindupAccounts(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        bindupAccountsDo.setStoreGuid("storeGuid");
        bindupAccountsDo.setUserGuid("userGuid");
        bindupAccountsDo.setUserName("userName");
        when(mockBusinessClient.queryBindUpAccountsLast("storeGuid")).thenReturn(bindupAccountsDo);

        // Configure BusinessClient.saveBindUpAccounts(...).
        final BindupAccountsDTO bindupAccountsDTO = new BindupAccountsDTO();
        bindupAccountsDTO.setId(0L);
        bindupAccountsDTO.setBindupAccounts(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        bindupAccountsDTO.setStoreGuid("storeGuid");
        bindupAccountsDTO.setUserGuid("userGuid");
        bindupAccountsDTO.setUserName("userName");
        when(mockBusinessClient.saveBindUpAccounts("storeGuid", "userGuid", "userName",
                LocalDate.of(2020, 1, 1))).thenReturn(bindupAccountsDTO);

        // Run the test
        final HashMap<String, String> result = storeServiceImplUnderTest.checkBindUpAccount("storeGuid", "userGuid",
                "userName", tableStatusDTOS);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testCheckBindUpAccount_OrganizationMapperReturnsNoItems() {
        // Setup
        final List<TableStatusDTO> tableStatusDTOS = Arrays.asList(
                new TableStatusDTO(LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, LocalDate.of(2020, 1, 1)));
        final HashMap<String, String> expectedResult = new HashMap<>();
        when(mockOrganizationMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure StoreMapstruct.organizationList2DTOList(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        brandDTO.setStoreList(Arrays.asList(new StoreDTO()));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO);
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOList = Arrays.asList(organizationDO);
        when(mockStoreMapstruct.organizationList2DTOList(organizationDOList)).thenReturn(storeDTOS);

        // Configure BusinessClient.queryBindUpAccountsLast(...).
        final BindupAccountsDo bindupAccountsDo = new BindupAccountsDo();
        bindupAccountsDo.setId(0L);
        bindupAccountsDo.setBindupAccounts(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        bindupAccountsDo.setStoreGuid("storeGuid");
        bindupAccountsDo.setUserGuid("userGuid");
        bindupAccountsDo.setUserName("userName");
        when(mockBusinessClient.queryBindUpAccountsLast("storeGuid")).thenReturn(bindupAccountsDo);

        // Configure BusinessClient.saveBindUpAccounts(...).
        final BindupAccountsDTO bindupAccountsDTO = new BindupAccountsDTO();
        bindupAccountsDTO.setId(0L);
        bindupAccountsDTO.setBindupAccounts(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        bindupAccountsDTO.setStoreGuid("storeGuid");
        bindupAccountsDTO.setUserGuid("userGuid");
        bindupAccountsDTO.setUserName("userName");
        when(mockBusinessClient.saveBindUpAccounts("storeGuid", "userGuid", "userName",
                LocalDate.of(2020, 1, 1))).thenReturn(bindupAccountsDTO);

        // Run the test
        final HashMap<String, String> result = storeServiceImplUnderTest.checkBindUpAccount("storeGuid", "userGuid",
                "userName", tableStatusDTOS);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testCheckBindUpAccount_StoreMapstructReturnsNoItems() {
        // Setup
        final List<TableStatusDTO> tableStatusDTOS = Arrays.asList(
                new TableStatusDTO(LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, LocalDate.of(2020, 1, 1)));
        final HashMap<String, String> expectedResult = new HashMap<>();

        // Configure OrganizationMapper.selectList(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOS = Arrays.asList(organizationDO);
        when(mockOrganizationMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(organizationDOS);

        // Configure StoreMapstruct.organizationList2DTOList(...).
        final OrganizationDO organizationDO1 = new OrganizationDO();
        organizationDO1.setGuid("guid");
        organizationDO1.setCode("code");
        organizationDO1.setType(0);
        organizationDO1.setName("name");
        organizationDO1.setParentIds("parentIds");
        organizationDO1.setProvinceCode("provinceCode");
        organizationDO1.setProvinceName("provinceName");
        organizationDO1.setCityCode("cityCode");
        organizationDO1.setCityName("cityName");
        organizationDO1.setIsEnable(false);
        organizationDO1.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO1.setModifiedUserGuid("modifiedUserGuid");
        organizationDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setIsSelfBuildItems(0);
        organizationDO1.setIsBuAccounts(0);
        organizationDO1.setIsShowCash(0);
        final List<OrganizationDO> organizationDOList = Arrays.asList(organizationDO1);
        when(mockStoreMapstruct.organizationList2DTOList(organizationDOList)).thenReturn(Collections.emptyList());

        // Configure BusinessClient.queryBindUpAccountsLast(...).
        final BindupAccountsDo bindupAccountsDo = new BindupAccountsDo();
        bindupAccountsDo.setId(0L);
        bindupAccountsDo.setBindupAccounts(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        bindupAccountsDo.setStoreGuid("storeGuid");
        bindupAccountsDo.setUserGuid("userGuid");
        bindupAccountsDo.setUserName("userName");
        when(mockBusinessClient.queryBindUpAccountsLast("storeGuid")).thenReturn(bindupAccountsDo);

        // Configure BusinessClient.saveBindUpAccounts(...).
        final BindupAccountsDTO bindupAccountsDTO = new BindupAccountsDTO();
        bindupAccountsDTO.setId(0L);
        bindupAccountsDTO.setBindupAccounts(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        bindupAccountsDTO.setStoreGuid("storeGuid");
        bindupAccountsDTO.setUserGuid("userGuid");
        bindupAccountsDTO.setUserName("userName");
        when(mockBusinessClient.saveBindUpAccounts("storeGuid", "userGuid", "userName",
                LocalDate.of(2020, 1, 1))).thenReturn(bindupAccountsDTO);

        // Run the test
        final HashMap<String, String> result = storeServiceImplUnderTest.checkBindUpAccount("storeGuid", "userGuid",
                "userName", tableStatusDTOS);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testQueryStoreBusinessDay() throws Exception {
        // Setup
        final BusinessDayDTO businessDayDTO = new BusinessDayDTO();
        businessDayDTO.setGuid("6692ceca-99c5-46d0-8494-2ac82309fda3");
        businessDayDTO.setStoreGuid("storeGuid");
        businessDayDTO.setEnterpriseGuid("enterpriseGuid");
        businessDayDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        businessDayDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        final List<BusinessDayDTO> businessDayDTOList = Arrays.asList(businessDayDTO);
        final BusinessDayDTO businessDayDTO1 = new BusinessDayDTO();
        businessDayDTO1.setGuid("6692ceca-99c5-46d0-8494-2ac82309fda3");
        businessDayDTO1.setStoreGuid("storeGuid");
        businessDayDTO1.setEnterpriseGuid("enterpriseGuid");
        businessDayDTO1.setBusinessStart(LocalTime.of(0, 0, 0));
        businessDayDTO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        final List<BusinessDayDTO> expectedResult = Arrays.asList(businessDayDTO1);
        when(mockEnterpriseClientService.hasEnterprise("enterpriseGuid")).thenReturn(false);

        // Run the test
        final List<BusinessDayDTO> result = storeServiceImplUnderTest.queryStoreBusinessDay(businessDayDTOList);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testQueryStoreBusinessDay_EnterpriseClientServiceReturnsTrue() {
        // Setup
        final BusinessDayDTO businessDayDTO = new BusinessDayDTO();
        businessDayDTO.setGuid("6692ceca-99c5-46d0-8494-2ac82309fda3");
        businessDayDTO.setStoreGuid("storeGuid");
        businessDayDTO.setEnterpriseGuid("enterpriseGuid");
        businessDayDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        businessDayDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        final List<BusinessDayDTO> businessDayDTOList = Arrays.asList(businessDayDTO);
        final BusinessDayDTO businessDayDTO1 = new BusinessDayDTO();
        businessDayDTO1.setGuid("6692ceca-99c5-46d0-8494-2ac82309fda3");
        businessDayDTO1.setStoreGuid("storeGuid");
        businessDayDTO1.setEnterpriseGuid("enterpriseGuid");
        businessDayDTO1.setBusinessStart(LocalTime.of(0, 0, 0));
        businessDayDTO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        final List<BusinessDayDTO> expectedResult = Arrays.asList(businessDayDTO1);
        when(mockEnterpriseClientService.hasEnterprise("enterpriseGuid")).thenReturn(true);

        // Run the test
        final List<BusinessDayDTO> result = storeServiceImplUnderTest.queryStoreBusinessDay(businessDayDTOList);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
    }

    @Test
    public void testCurrentTimeDay() {
        // Setup
        // Configure OrganizationMapper.selectList(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOS = Arrays.asList(organizationDO);
        when(mockOrganizationMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(organizationDOS);

        // Configure StoreMapstruct.organizationList2DTOList(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        brandDTO.setStoreList(Arrays.asList(new StoreDTO()));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO);
        final OrganizationDO organizationDO1 = new OrganizationDO();
        organizationDO1.setGuid("guid");
        organizationDO1.setCode("code");
        organizationDO1.setType(0);
        organizationDO1.setName("name");
        organizationDO1.setParentIds("parentIds");
        organizationDO1.setProvinceCode("provinceCode");
        organizationDO1.setProvinceName("provinceName");
        organizationDO1.setCityCode("cityCode");
        organizationDO1.setCityName("cityName");
        organizationDO1.setIsEnable(false);
        organizationDO1.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO1.setModifiedUserGuid("modifiedUserGuid");
        organizationDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setIsSelfBuildItems(0);
        organizationDO1.setIsBuAccounts(0);
        organizationDO1.setIsShowCash(0);
        final List<OrganizationDO> organizationDOList = Arrays.asList(organizationDO1);
        when(mockStoreMapstruct.organizationList2DTOList(organizationDOList)).thenReturn(storeDTOS);

        // Run the test
        final LocalDate result = storeServiceImplUnderTest.currentTimeDay("storeGuid",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Verify the results
        assertEquals(LocalDate.of(2020, 1, 1), result);
    }

    @Test
    public void testCurrentTimeDay_OrganizationMapperReturnsNoItems() {
        // Setup
        when(mockOrganizationMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure StoreMapstruct.organizationList2DTOList(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        storeDTO.setParentIds("parentIds");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setContactTel("contactTel");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setAddressDetail("addressDetail");
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        final StoreProductDTO storeProductDTO = new StoreProductDTO();
        storeProductDTO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        storeProductDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDTO.setProductDTOList(Arrays.asList(storeProductDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("eb85852e-3c79-4312-9c64-628e27544f73");
        brandDTO.setName("belongBrandName");
        brandDTO.setLogoUrl("brandLogoUrl");
        brandDTO.setIsEnable(false);
        brandDTO.setStoreList(Arrays.asList(new StoreDTO()));
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        storeDTO.setMchntTypeCode("mchntTypeCode");
        storeDTO.setIsSelfBuildItems(0);
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setIsMultiHandover(0);
        storeDTO.setBusinessDay(LocalDate.of(2020, 1, 1));
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO);
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOList = Arrays.asList(organizationDO);
        when(mockStoreMapstruct.organizationList2DTOList(organizationDOList)).thenReturn(storeDTOS);

        // Run the test
        final LocalDate result = storeServiceImplUnderTest.currentTimeDay("storeGuid",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Verify the results
        assertEquals(LocalDate.of(2020, 1, 1), result);
    }

    @Test
    public void testCurrentTimeDay_StoreMapstructReturnsNoItems() {
        // Setup
        // Configure OrganizationMapper.selectList(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("guid");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setName("name");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("provinceCode");
        organizationDO.setProvinceName("provinceName");
        organizationDO.setCityCode("cityCode");
        organizationDO.setCityName("cityName");
        organizationDO.setIsEnable(false);
        organizationDO.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO.setIsSelfBuildItems(0);
        organizationDO.setIsBuAccounts(0);
        organizationDO.setIsShowCash(0);
        final List<OrganizationDO> organizationDOS = Arrays.asList(organizationDO);
        when(mockOrganizationMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(organizationDOS);

        // Configure StoreMapstruct.organizationList2DTOList(...).
        final OrganizationDO organizationDO1 = new OrganizationDO();
        organizationDO1.setGuid("guid");
        organizationDO1.setCode("code");
        organizationDO1.setType(0);
        organizationDO1.setName("name");
        organizationDO1.setParentIds("parentIds");
        organizationDO1.setProvinceCode("provinceCode");
        organizationDO1.setProvinceName("provinceName");
        organizationDO1.setCityCode("cityCode");
        organizationDO1.setCityName("cityName");
        organizationDO1.setIsEnable(false);
        organizationDO1.setBusinessStart(LocalTime.of(0, 0, 0));
        organizationDO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        organizationDO1.setModifiedUserGuid("modifiedUserGuid");
        organizationDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        organizationDO1.setIsSelfBuildItems(0);
        organizationDO1.setIsBuAccounts(0);
        organizationDO1.setIsShowCash(0);
        final List<OrganizationDO> organizationDOList = Arrays.asList(organizationDO1);
        when(mockStoreMapstruct.organizationList2DTOList(organizationDOList)).thenReturn(Collections.emptyList());

        // Run the test
        final LocalDate result = storeServiceImplUnderTest.currentTimeDay("storeGuid",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Verify the results
        assertEquals(LocalDate.of(2020, 1, 1), result);
    }

    @Test
    public void testQueryStoreBrandDetail() throws Exception {
        // Setup
        final BrandStoreDetailDTO expectedResult = new BrandStoreDetailDTO();
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setBrandGuid("brandGuid");
        expectedResult.setStoreName("storeName");
        expectedResult.setBrandName("brandName");
        expectedResult.setBrandLogoUrl("brandLogoUrl");

        // Configure StoreBrandMapper.getStoreBrandDetail(...).
        final BrandStoreDetailDTO brandStoreDetailDTO = new BrandStoreDetailDTO();
        brandStoreDetailDTO.setStoreGuid("storeGuid");
        brandStoreDetailDTO.setBrandGuid("brandGuid");
        brandStoreDetailDTO.setStoreName("storeName");
        brandStoreDetailDTO.setBrandName("brandName");
        brandStoreDetailDTO.setBrandLogoUrl("brandLogoUrl");
        when(mockStoreBrandMapper.getStoreBrandDetail("storeGuid", "brandGuid")).thenReturn(brandStoreDetailDTO);

        // Run the test
        final BrandStoreDetailDTO result = storeServiceImplUnderTest.queryStoreBrandDetail("storeGuid", "brandGuid");

        // Verify the results
        assertEquals(expectedResult, result);
    }
}
