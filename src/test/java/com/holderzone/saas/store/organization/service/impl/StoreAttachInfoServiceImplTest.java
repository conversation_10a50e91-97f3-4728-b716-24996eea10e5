package com.holderzone.saas.store.organization.service.impl;

import com.holderzone.saas.store.dto.organization.StoreAttachInfoDTO;
import com.holderzone.saas.store.organization.domain.OrganizationDO;
import com.holderzone.saas.store.organization.mapper.OrganizationMapper;
import com.holderzone.saas.store.organization.service.impl.StoreAttachInfoServiceImpl;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class StoreAttachInfoServiceImplTest {

    @Mock
    private OrganizationMapper mockOrganizationMapper;

    private StoreAttachInfoServiceImpl storeAttachInfoServiceImplUnderTest;

    @Before
    public void setUp() {
        storeAttachInfoServiceImplUnderTest = new StoreAttachInfoServiceImpl(mockOrganizationMapper);
    }

    @Test
    public void testInfo() {
        // Setup
        final StoreAttachInfoDTO expectedResult = new StoreAttachInfoDTO();
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setUatAppId("uatAppId");
        expectedResult.setUatSecret("uatSecret");

        // Configure OrganizationMapper.queryByGuid(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setId(0L);
        organizationDO.setGuid("28bb1e06-a9a2-4e8c-a8a8-8d80a1cb9faf");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setIsEnable(false);
        when(mockOrganizationMapper.queryByGuid("storeGuid")).thenReturn(organizationDO);

        // Run the test
        final StoreAttachInfoDTO result = storeAttachInfoServiceImplUnderTest.info("storeGuid");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testSave() {
        // Setup
        final StoreAttachInfoDTO dto = new StoreAttachInfoDTO();
        dto.setStoreGuid("storeGuid");
        dto.setUatAppId("uatAppId");
        dto.setUatSecret("uatSecret");

        // Configure OrganizationMapper.queryByGuid(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setId(0L);
        organizationDO.setGuid("28bb1e06-a9a2-4e8c-a8a8-8d80a1cb9faf");
        organizationDO.setCode("code");
        organizationDO.setType(0);
        organizationDO.setIsEnable(false);
        when(mockOrganizationMapper.queryByGuid("storeGuid")).thenReturn(organizationDO);

        // Run the test
        final boolean result = storeAttachInfoServiceImplUnderTest.save(dto);

        // Verify the results
        assertFalse(result);
    }
}
