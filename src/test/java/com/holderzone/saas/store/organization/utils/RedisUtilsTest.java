package com.holderzone.saas.store.organization.utils;

import com.holderzone.saas.store.organization.utils.RedisUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.connection.DataType;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.data.redis.core.ZSetOperations;

import java.util.*;
import java.util.concurrent.TimeUnit;

import static org.junit.Assert.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class RedisUtilsTest {

    @Mock
    private RedisTemplate mockRedisTemplate;

    private RedisUtils redisUtilsUnderTest;

    @Before
    public void setUp() throws Exception {
        redisUtilsUnderTest = new RedisUtils(mockRedisTemplate);
    }

    @Test
    public void testGenerate() {
        // Setup
        when(mockRedisTemplate.getConnectionFactory()).thenReturn(null);

        // Run the test
        final long result = redisUtilsUnderTest.generate("key",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Verify the results
        assertEquals(0L, result);
    }

    @Test
    public void testGenerate_RedisTemplateReturnsNull() {
        // Setup
        when(mockRedisTemplate.getConnectionFactory()).thenReturn(null);

        // Run the test
        final long result = redisUtilsUnderTest.generate("key",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Verify the results
        assertEquals(0L, result);
    }

    @Test
    public void testGenerateGuid() {
        assertEquals("result", redisUtilsUnderTest.generateGuid("redisKey"));
    }

    @Test
    public void testGeneratdDTOGuid() {
        assertEquals("result", redisUtilsUnderTest.generatdDTOGuid(Object.class));
    }

    @Test
    public void testGeneratedDTOKey() {
        assertEquals("result", redisUtilsUnderTest.generatedDTOKey(Object.class));
    }

    @Test
    public void testKeyGenerate() {
        assertEquals("result", redisUtilsUnderTest.keyGenerate("keyName"));
    }

    @Test
    public void testDelete1() {
        // Setup
        when(mockRedisTemplate.delete("key")).thenReturn(false);

        // Run the test
        final Boolean result = redisUtilsUnderTest.delete("key");

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testDelete1_RedisTemplateReturnsNull() {
        // Setup
        when(mockRedisTemplate.delete("key")).thenReturn(null);

        // Run the test
        final Boolean result = redisUtilsUnderTest.delete("key");

        // Verify the results
        assertNull(result);
    }

    @Test
    public void testDelete1_RedisTemplateReturnsTrue() {
        // Setup
        when(mockRedisTemplate.delete("key")).thenReturn(true);

        // Run the test
        final Boolean result = redisUtilsUnderTest.delete("key");

        // Verify the results
        assertTrue(result);
    }

    @Test
    public void testDelete2() {
        // Setup
        // Run the test
        redisUtilsUnderTest.delete(Arrays.asList("value"));

        // Verify the results
        verify(mockRedisTemplate).delete(Arrays.asList("value"));
    }

    @Test
    public void testDump() {
        // Setup
        when(mockRedisTemplate.dump("key")).thenReturn("content".getBytes());

        // Run the test
        final byte[] result = redisUtilsUnderTest.dump("key");

        // Verify the results
        assertArrayEquals("content".getBytes(), result);
    }

    @Test
    public void testDump_RedisTemplateReturnsNull() {
        // Setup
        when(mockRedisTemplate.dump("key")).thenReturn(null);

        // Run the test
        final byte[] result = redisUtilsUnderTest.dump("key");

        // Verify the results
        assertArrayEquals("content".getBytes(), result);
    }

    @Test
    public void testHasKey() {
        // Setup
        when(mockRedisTemplate.hasKey("key")).thenReturn(false);

        // Run the test
        final Boolean result = redisUtilsUnderTest.hasKey("key");

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testHasKey_RedisTemplateReturnsNull() {
        // Setup
        when(mockRedisTemplate.hasKey("key")).thenReturn(null);

        // Run the test
        final Boolean result = redisUtilsUnderTest.hasKey("key");

        // Verify the results
        assertNull(result);
    }

    @Test
    public void testHasKey_RedisTemplateReturnsTrue() {
        // Setup
        when(mockRedisTemplate.hasKey("key")).thenReturn(true);

        // Run the test
        final Boolean result = redisUtilsUnderTest.hasKey("key");

        // Verify the results
        assertTrue(result);
    }

    @Test
    public void testExpire() {
        // Setup
        when(mockRedisTemplate.expire("key", 0L, TimeUnit.MILLISECONDS)).thenReturn(false);

        // Run the test
        final Boolean result = redisUtilsUnderTest.expire("key", 0L, TimeUnit.MILLISECONDS);

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testExpire_RedisTemplateReturnsNull() {
        // Setup
        when(mockRedisTemplate.expire("key", 0L, TimeUnit.MILLISECONDS)).thenReturn(null);

        // Run the test
        final Boolean result = redisUtilsUnderTest.expire("key", 0L, TimeUnit.MILLISECONDS);

        // Verify the results
        assertNull(result);
    }

    @Test
    public void testExpire_RedisTemplateReturnsTrue() {
        // Setup
        when(mockRedisTemplate.expire("key", 0L, TimeUnit.MILLISECONDS)).thenReturn(true);

        // Run the test
        final Boolean result = redisUtilsUnderTest.expire("key", 0L, TimeUnit.MILLISECONDS);

        // Verify the results
        assertTrue(result);
    }

    @Test
    public void testExpireAt() {
        // Setup
        when(mockRedisTemplate.expireAt("key", new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
                .thenReturn(false);

        // Run the test
        final Boolean result = redisUtilsUnderTest.expireAt("key",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testExpireAt_RedisTemplateReturnsNull() {
        // Setup
        when(mockRedisTemplate.expireAt("key", new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
                .thenReturn(null);

        // Run the test
        final Boolean result = redisUtilsUnderTest.expireAt("key",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Verify the results
        assertNull(result);
    }

    @Test
    public void testExpireAt_RedisTemplateReturnsTrue() {
        // Setup
        when(mockRedisTemplate.expireAt("key", new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
                .thenReturn(true);

        // Run the test
        final Boolean result = redisUtilsUnderTest.expireAt("key",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Verify the results
        assertTrue(result);
    }

    @Test
    public void testKeys() {
        // Setup
        when(mockRedisTemplate.keys("pattern")).thenReturn(new HashSet<>());

        // Run the test
        final Set<String> result = redisUtilsUnderTest.keys("pattern");

        // Verify the results
        assertEquals(new HashSet<>(Arrays.asList("value")), result);
    }

    @Test
    public void testKeys_RedisTemplateReturnsNull() {
        // Setup
        when(mockRedisTemplate.keys("pattern")).thenReturn(null);

        // Run the test
        final Set<String> result = redisUtilsUnderTest.keys("pattern");

        // Verify the results
        assertNull(result);
    }

    @Test
    public void testKeys_RedisTemplateReturnsNoItems() {
        // Setup
        when(mockRedisTemplate.keys("pattern")).thenReturn(Collections.emptySet());

        // Run the test
        final Set<String> result = redisUtilsUnderTest.keys("pattern");

        // Verify the results
        assertEquals(Collections.emptySet(), result);
    }

    @Test
    public void testMove() {
        // Setup
        when(mockRedisTemplate.move("key", 0)).thenReturn(false);

        // Run the test
        final Boolean result = redisUtilsUnderTest.move("key", 0);

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testMove_RedisTemplateReturnsNull() {
        // Setup
        when(mockRedisTemplate.move("key", 0)).thenReturn(null);

        // Run the test
        final Boolean result = redisUtilsUnderTest.move("key", 0);

        // Verify the results
        assertNull(result);
    }

    @Test
    public void testMove_RedisTemplateReturnsTrue() {
        // Setup
        when(mockRedisTemplate.move("key", 0)).thenReturn(true);

        // Run the test
        final Boolean result = redisUtilsUnderTest.move("key", 0);

        // Verify the results
        assertTrue(result);
    }

    @Test
    public void testPersist() {
        // Setup
        when(mockRedisTemplate.persist("key")).thenReturn(false);

        // Run the test
        final Boolean result = redisUtilsUnderTest.persist("key");

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testPersist_RedisTemplateReturnsNull() {
        // Setup
        when(mockRedisTemplate.persist("key")).thenReturn(null);

        // Run the test
        final Boolean result = redisUtilsUnderTest.persist("key");

        // Verify the results
        assertNull(result);
    }

    @Test
    public void testPersist_RedisTemplateReturnsTrue() {
        // Setup
        when(mockRedisTemplate.persist("key")).thenReturn(true);

        // Run the test
        final Boolean result = redisUtilsUnderTest.persist("key");

        // Verify the results
        assertTrue(result);
    }

    @Test
    public void testGetExpire1() {
        // Setup
        when(mockRedisTemplate.getExpire("key", TimeUnit.MILLISECONDS)).thenReturn(0L);

        // Run the test
        final Long result = redisUtilsUnderTest.getExpire("key", TimeUnit.MILLISECONDS);

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testGetExpire1_RedisTemplateReturnsNull() {
        // Setup
        when(mockRedisTemplate.getExpire("key", TimeUnit.MILLISECONDS)).thenReturn(null);

        // Run the test
        final Long result = redisUtilsUnderTest.getExpire("key", TimeUnit.MILLISECONDS);

        // Verify the results
        assertNull(result);
    }

    @Test
    public void testGetExpire2() {
        // Setup
        when(mockRedisTemplate.getExpire("key")).thenReturn(0L);

        // Run the test
        final Long result = redisUtilsUnderTest.getExpire("key");

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testGetExpire2_RedisTemplateReturnsNull() {
        // Setup
        when(mockRedisTemplate.getExpire("key")).thenReturn(null);

        // Run the test
        final Long result = redisUtilsUnderTest.getExpire("key");

        // Verify the results
        assertNull(result);
    }

    @Test
    public void testRandomKey() {
        // Setup
        when(mockRedisTemplate.randomKey()).thenReturn("result");

        // Run the test
        final Object result = redisUtilsUnderTest.randomKey();

        // Verify the results
    }

    @Test
    public void testRandomKey_RedisTemplateReturnsNull() {
        // Setup
        when(mockRedisTemplate.randomKey()).thenReturn(null);

        // Run the test
        final Object result = redisUtilsUnderTest.randomKey();

        // Verify the results
        assertNull(result);
    }

    @Test
    public void testRename() {
        // Setup
        // Run the test
        redisUtilsUnderTest.rename("oldKey", "newKey");

        // Verify the results
        verify(mockRedisTemplate).rename("oldKey", "newKey");
    }

    @Test
    public void testRenameIfAbsent() {
        // Setup
        when(mockRedisTemplate.renameIfAbsent("oldKey", "newKey")).thenReturn(false);

        // Run the test
        final Boolean result = redisUtilsUnderTest.renameIfAbsent("oldKey", "newKey");

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testRenameIfAbsent_RedisTemplateReturnsNull() {
        // Setup
        when(mockRedisTemplate.renameIfAbsent("oldKey", "newKey")).thenReturn(null);

        // Run the test
        final Boolean result = redisUtilsUnderTest.renameIfAbsent("oldKey", "newKey");

        // Verify the results
        assertNull(result);
    }

    @Test
    public void testRenameIfAbsent_RedisTemplateReturnsTrue() {
        // Setup
        when(mockRedisTemplate.renameIfAbsent("oldKey", "newKey")).thenReturn(true);

        // Run the test
        final Boolean result = redisUtilsUnderTest.renameIfAbsent("oldKey", "newKey");

        // Verify the results
        assertTrue(result);
    }

    @Test
    public void testType() {
        // Setup
        when(mockRedisTemplate.type("key")).thenReturn(DataType.NONE);

        // Run the test
        final DataType result = redisUtilsUnderTest.type("key");

        // Verify the results
        assertEquals(DataType.NONE, result);
    }

    @Test
    public void testType_RedisTemplateReturnsNull() {
        // Setup
        when(mockRedisTemplate.type("key")).thenReturn(null);

        // Run the test
        final DataType result = redisUtilsUnderTest.type("key");

        // Verify the results
        assertNull(result);
    }

    @Test
    public void testSet1() {
        // Setup
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        redisUtilsUnderTest.set("key", "value");

        // Verify the results
    }

    @Test
    public void testSetParamList() {
        // Setup
        final List<Pair<String, Object>> paramList = Arrays.asList(Pair.of("left", "right"));
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        redisUtilsUnderTest.setParamList(paramList);

        // Verify the results
    }

    @Test
    public void testSetParamListEx() {
        // Setup
        final List<Pair<String, Object>> paramList = Arrays.asList(Pair.of("left", "right"));
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        redisUtilsUnderTest.setParamListEx(paramList, 0L, TimeUnit.MILLISECONDS);

        // Verify the results
    }

    @Test
    public void testSet2() {
        // Setup
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        redisUtilsUnderTest.set("key", "value");

        // Verify the results
        verify(mockRedisTemplate).expire("key", 24L, TimeUnit.HOURS);
    }

    @Test
    public void testGet() {
        // Setup
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        final Object result = redisUtilsUnderTest.get("key");

        // Verify the results
    }

    @Test
    public void testGetRange() {
        // Setup
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        final String result = redisUtilsUnderTest.getRange("key", 0L, 0L);

        // Verify the results
        assertEquals("result", result);
    }

    @Test
    public void testGetAndSet() {
        // Setup
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        final Object result = redisUtilsUnderTest.getAndSet("key", "value");

        // Verify the results
    }

    @Test
    public void testGetBit() {
        // Setup
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        final Boolean result = redisUtilsUnderTest.getBit("key", 0L);

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testMultiGet() {
        // Setup
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        final List<String> result = redisUtilsUnderTest.multiGet(Arrays.asList("value"));

        // Verify the results
        assertEquals(Arrays.asList("value"), result);
    }

    @Test
    public void testSetBit() {
        // Setup
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        final boolean result = redisUtilsUnderTest.setBit("key", 0L, false);

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testSetEx() {
        // Setup
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        redisUtilsUnderTest.setEx("key", "value", 0L, TimeUnit.MILLISECONDS);

        // Verify the results
    }

    @Test
    public void testSetIfAbsent() {
        // Setup
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        final boolean result = redisUtilsUnderTest.setIfAbsent("key", "value");

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testSetRange() {
        // Setup
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        redisUtilsUnderTest.setRange("key", "value", 0L);

        // Verify the results
    }

    @Test
    public void testSize() {
        // Setup
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        final Long result = redisUtilsUnderTest.size("key");

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testMultiSet() {
        // Setup
        final Map<String, String> maps = new HashMap<>();
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        redisUtilsUnderTest.multiSet(maps);

        // Verify the results
    }

    @Test
    public void testMultiSetIfAbsent() {
        // Setup
        final Map<String, String> maps = new HashMap<>();
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        final boolean result = redisUtilsUnderTest.multiSetIfAbsent(maps);

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testIncrBy() {
        // Setup
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        final Long result = redisUtilsUnderTest.incrBy("key", 0L);

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testIncrByFloat() {
        // Setup
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        final Double result = redisUtilsUnderTest.incrByFloat("key", 0.0);

        // Verify the results
        assertEquals(0.0, result, 0.0001);
    }

    @Test
    public void testAppend() {
        // Setup
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        final Integer result = redisUtilsUnderTest.append("key", "value");

        // Verify the results
        assertEquals(Integer.valueOf(0), result);
    }

    @Test
    public void testSaveObject1() {
        // Setup
        when(mockRedisTemplate.opsForHash()).thenReturn(null);

        // Run the test
        redisUtilsUnderTest.saveObject("key", "object");

        // Verify the results
        verify(mockRedisTemplate).expire("key", 0L, TimeUnit.MILLISECONDS);
    }

    @Test
    public void testSaveObject2() {
        // Setup
        when(mockRedisTemplate.opsForHash()).thenReturn(null);

        // Run the test
        redisUtilsUnderTest.saveObject("key", "object", 0L, TimeUnit.MILLISECONDS);

        // Verify the results
        verify(mockRedisTemplate).expire("key", 0L, TimeUnit.MILLISECONDS);
    }

    @Test
    public void testGetObject() {
        // Setup
        when(mockRedisTemplate.opsForHash()).thenReturn(null);

        // Run the test
        final Object result = redisUtilsUnderTest.getObject("key", Object.class);

        // Verify the results
    }

    @Test
    public void testHGet() {
        // Setup
        when(mockRedisTemplate.opsForHash()).thenReturn(null);

        // Run the test
        final Object result = redisUtilsUnderTest.hGet("key", "field");

        // Verify the results
    }

    @Test
    public void testHGetAll() {
        // Setup
        when(mockRedisTemplate.opsForHash()).thenReturn(null);

        // Run the test
        final Map result = redisUtilsUnderTest.hGetAll("key");

        // Verify the results
    }

    @Test
    public void testHMultiGet() {
        // Setup
        when(mockRedisTemplate.opsForHash()).thenReturn(null);

        // Run the test
        final List<Object> result = redisUtilsUnderTest.hMultiGet("key", Arrays.asList("value"));

        // Verify the results
    }

    @Test
    public void testHPut() {
        // Setup
        when(mockRedisTemplate.opsForHash()).thenReturn(null);

        // Run the test
        redisUtilsUnderTest.hPut("key", "hashKey", "value");

        // Verify the results
        verify(mockRedisTemplate).expire("key", 24L, TimeUnit.HOURS);
    }

    @Test
    public void testHPutAll() {
        // Setup
        final Map<String, String> maps = new HashMap<>();
        when(mockRedisTemplate.opsForHash()).thenReturn(null);

        // Run the test
        redisUtilsUnderTest.hPutAll("key", maps);

        // Verify the results
    }

    @Test
    public void testHPutIfAbsent() {
        // Setup
        when(mockRedisTemplate.opsForHash()).thenReturn(null);

        // Run the test
        final Boolean result = redisUtilsUnderTest.hPutIfAbsent("key", "hashKey", "value");

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testHDelete() {
        // Setup
        when(mockRedisTemplate.opsForHash()).thenReturn(null);

        // Run the test
        final Long result = redisUtilsUnderTest.hDelete("key", "fields");

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testHExists() {
        // Setup
        when(mockRedisTemplate.opsForHash()).thenReturn(null);

        // Run the test
        final boolean result = redisUtilsUnderTest.hExists("key", "field");

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testHIncrBy() {
        // Setup
        when(mockRedisTemplate.opsForHash()).thenReturn(null);

        // Run the test
        final Long result = redisUtilsUnderTest.hIncrBy("key", "field", 0L);

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testHIncrByFloat() {
        // Setup
        when(mockRedisTemplate.opsForHash()).thenReturn(null);

        // Run the test
        final Double result = redisUtilsUnderTest.hIncrByFloat("key", "field", 0.0);

        // Verify the results
        assertEquals(0.0, result, 0.0001);
    }

    @Test
    public void testHKeys() {
        // Setup
        when(mockRedisTemplate.opsForHash()).thenReturn(null);

        // Run the test
        final Set<Object> result = redisUtilsUnderTest.hKeys("key");

        // Verify the results
    }

    @Test
    public void testHSize() {
        // Setup
        when(mockRedisTemplate.opsForHash()).thenReturn(null);

        // Run the test
        final Long result = redisUtilsUnderTest.hSize("key");

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testHValues() {
        // Setup
        when(mockRedisTemplate.opsForHash()).thenReturn(null);

        // Run the test
        final List<String> result = redisUtilsUnderTest.hValues("key");

        // Verify the results
        assertEquals(Arrays.asList("value"), result);
    }

    @Test
    public void testHScan() {
        // Setup
        final ScanOptions options = null;
        when(mockRedisTemplate.opsForHash()).thenReturn(null);

        // Run the test
        final Cursor<Map.Entry<Object, Object>> result = redisUtilsUnderTest.hScan("key", options);

        // Verify the results
    }

    @Test
    public void testLIndex() {
        // Setup
        when(mockRedisTemplate.opsForList()).thenReturn(null);

        // Run the test
        final Object result = redisUtilsUnderTest.lIndex("key", 0L);

        // Verify the results
    }

    @Test
    public void testLRange() {
        // Setup
        when(mockRedisTemplate.opsForList()).thenReturn(null);

        // Run the test
        final List<Object> result = redisUtilsUnderTest.lRange("key", 0L, 0L);

        // Verify the results
    }

    @Test
    public void testLLeftPush1() {
        // Setup
        when(mockRedisTemplate.opsForList()).thenReturn(null);

        // Run the test
        final Long result = redisUtilsUnderTest.lLeftPush("key", "value");

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testLLeftPushAll1() {
        // Setup
        when(mockRedisTemplate.opsForList()).thenReturn(null);

        // Run the test
        final Long result = redisUtilsUnderTest.lLeftPushAll("key", "value");

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testLLeftPushAll2() {
        // Setup
        when(mockRedisTemplate.opsForList()).thenReturn(null);

        // Run the test
        final Long result = redisUtilsUnderTest.lLeftPushAll("key", Arrays.asList("value"));

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testLLeftPushIfPresent() {
        // Setup
        when(mockRedisTemplate.opsForList()).thenReturn(null);

        // Run the test
        final Long result = redisUtilsUnderTest.lLeftPushIfPresent("key", "value");

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testLLeftPush2() {
        // Setup
        when(mockRedisTemplate.opsForList()).thenReturn(null);

        // Run the test
        final Long result = redisUtilsUnderTest.lLeftPush("key", "pivot", "value");

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testLRightPush1() {
        // Setup
        when(mockRedisTemplate.opsForList()).thenReturn(null);

        // Run the test
        final Long result = redisUtilsUnderTest.lRightPush("key", "value");

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testLRightPushAll1() {
        // Setup
        when(mockRedisTemplate.opsForList()).thenReturn(null);

        // Run the test
        final Long result = redisUtilsUnderTest.lRightPushAll("key", "value");

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testLRightPushAll2() {
        // Setup
        when(mockRedisTemplate.opsForList()).thenReturn(null);

        // Run the test
        final Long result = redisUtilsUnderTest.lRightPushAll("key", Arrays.asList("value"));

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testLRightPushIfPresent() {
        // Setup
        when(mockRedisTemplate.opsForList()).thenReturn(null);

        // Run the test
        final Long result = redisUtilsUnderTest.lRightPushIfPresent("key", "value");

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testLRightPush2() {
        // Setup
        when(mockRedisTemplate.opsForList()).thenReturn(null);

        // Run the test
        final Long result = redisUtilsUnderTest.lRightPush("key", "pivot", "value");

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testLSet() {
        // Setup
        when(mockRedisTemplate.opsForList()).thenReturn(null);

        // Run the test
        redisUtilsUnderTest.lSet("key", 0L, "value");

        // Verify the results
    }

    @Test
    public void testLLeftPop() {
        // Setup
        when(mockRedisTemplate.opsForList()).thenReturn(null);

        // Run the test
        final Object result = redisUtilsUnderTest.lLeftPop("key");

        // Verify the results
    }

    @Test
    public void testLBLeftPop() {
        // Setup
        when(mockRedisTemplate.opsForList()).thenReturn(null);

        // Run the test
        final Object result = redisUtilsUnderTest.lBLeftPop("key", 0L, TimeUnit.MILLISECONDS);

        // Verify the results
    }

    @Test
    public void testLRightPop() {
        // Setup
        when(mockRedisTemplate.opsForList()).thenReturn(null);

        // Run the test
        final Object result = redisUtilsUnderTest.lRightPop("key");

        // Verify the results
    }

    @Test
    public void testLBRightPop() {
        // Setup
        when(mockRedisTemplate.opsForList()).thenReturn(null);

        // Run the test
        final Object result = redisUtilsUnderTest.lBRightPop("key", 0L, TimeUnit.MILLISECONDS);

        // Verify the results
    }

    @Test
    public void testLRightPopAndLeftPush() {
        // Setup
        when(mockRedisTemplate.opsForList()).thenReturn(null);

        // Run the test
        final Object result = redisUtilsUnderTest.lRightPopAndLeftPush("sourceKey", "destinationKey");

        // Verify the results
    }

    @Test
    public void testLBRightPopAndLeftPush() {
        // Setup
        when(mockRedisTemplate.opsForList()).thenReturn(null);

        // Run the test
        final Object result = redisUtilsUnderTest.lBRightPopAndLeftPush("sourceKey", "destinationKey", 0L,
                TimeUnit.MILLISECONDS);

        // Verify the results
    }

    @Test
    public void testLRemove() {
        // Setup
        when(mockRedisTemplate.opsForList()).thenReturn(null);

        // Run the test
        final Long result = redisUtilsUnderTest.lRemove("key", 0L, "value");

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testLTrim() {
        // Setup
        when(mockRedisTemplate.opsForList()).thenReturn(null);

        // Run the test
        redisUtilsUnderTest.lTrim("key", 0L, 0L);

        // Verify the results
    }

    @Test
    public void testLLen() {
        // Setup
        when(mockRedisTemplate.opsForList()).thenReturn(null);

        // Run the test
        final Long result = redisUtilsUnderTest.lLen("key");

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testSAdd() {
        // Setup
        when(mockRedisTemplate.opsForSet()).thenReturn(null);

        // Run the test
        final Long result = redisUtilsUnderTest.sAdd("key", "values");

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testSRemove() {
        // Setup
        when(mockRedisTemplate.opsForSet()).thenReturn(null);

        // Run the test
        final Long result = redisUtilsUnderTest.sRemove("key", "values");

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testSPop() {
        // Setup
        when(mockRedisTemplate.opsForSet()).thenReturn(null);

        // Run the test
        final Object result = redisUtilsUnderTest.sPop("key");

        // Verify the results
    }

    @Test
    public void testSMove() {
        // Setup
        when(mockRedisTemplate.opsForSet()).thenReturn(null);

        // Run the test
        final Boolean result = redisUtilsUnderTest.sMove("key", "value", "destKey");

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testSSize() {
        // Setup
        when(mockRedisTemplate.opsForSet()).thenReturn(null);

        // Run the test
        final Long result = redisUtilsUnderTest.sSize("key");

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testSIsMember() {
        // Setup
        when(mockRedisTemplate.opsForSet()).thenReturn(null);

        // Run the test
        final Boolean result = redisUtilsUnderTest.sIsMember("key", "value");

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testSIntersect1() {
        // Setup
        when(mockRedisTemplate.opsForSet()).thenReturn(null);

        // Run the test
        final Set<String> result = redisUtilsUnderTest.sIntersect("key", "otherKey");

        // Verify the results
        assertEquals(new HashSet<>(Arrays.asList("value")), result);
    }

    @Test
    public void testSIntersect2() {
        // Setup
        when(mockRedisTemplate.opsForSet()).thenReturn(null);

        // Run the test
        final Set<String> result = redisUtilsUnderTest.sIntersect("key", Arrays.asList("value"));

        // Verify the results
        assertEquals(new HashSet<>(Arrays.asList("value")), result);
    }

    @Test
    public void testSIntersectAndStore1() {
        // Setup
        when(mockRedisTemplate.opsForSet()).thenReturn(null);

        // Run the test
        final Long result = redisUtilsUnderTest.sIntersectAndStore("key", "otherKey", "destKey");

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testSIntersectAndStore2() {
        // Setup
        when(mockRedisTemplate.opsForSet()).thenReturn(null);

        // Run the test
        final Long result = redisUtilsUnderTest.sIntersectAndStore("key", Arrays.asList("value"), "destKey");

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testSUnion1() {
        // Setup
        when(mockRedisTemplate.opsForSet()).thenReturn(null);

        // Run the test
        final Set<String> result = redisUtilsUnderTest.sUnion("key", "otherKeys");

        // Verify the results
        assertEquals(new HashSet<>(Arrays.asList("value")), result);
    }

    @Test
    public void testSUnion2() {
        // Setup
        when(mockRedisTemplate.opsForSet()).thenReturn(null);

        // Run the test
        final Set<String> result = redisUtilsUnderTest.sUnion("key", Arrays.asList("value"));

        // Verify the results
        assertEquals(new HashSet<>(Arrays.asList("value")), result);
    }

    @Test
    public void testSUnionAndStore1() {
        // Setup
        when(mockRedisTemplate.opsForSet()).thenReturn(null);

        // Run the test
        final Long result = redisUtilsUnderTest.sUnionAndStore("key", "otherKey", "destKey");

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testSUnionAndStore2() {
        // Setup
        when(mockRedisTemplate.opsForSet()).thenReturn(null);

        // Run the test
        final Long result = redisUtilsUnderTest.sUnionAndStore("key", Arrays.asList("value"), "destKey");

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testSDifference1() {
        // Setup
        when(mockRedisTemplate.opsForSet()).thenReturn(null);

        // Run the test
        final Set<String> result = redisUtilsUnderTest.sDifference("key", "otherKey");

        // Verify the results
        assertEquals(new HashSet<>(Arrays.asList("value")), result);
    }

    @Test
    public void testSDifference2() {
        // Setup
        when(mockRedisTemplate.opsForSet()).thenReturn(null);

        // Run the test
        final Set<String> result = redisUtilsUnderTest.sDifference("key", Arrays.asList("value"));

        // Verify the results
        assertEquals(new HashSet<>(Arrays.asList("value")), result);
    }

    @Test
    public void testSDifference3() {
        // Setup
        when(mockRedisTemplate.opsForSet()).thenReturn(null);

        // Run the test
        final Long result = redisUtilsUnderTest.sDifference("key", "otherKey", "destKey");

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testSDifference4() {
        // Setup
        when(mockRedisTemplate.opsForSet()).thenReturn(null);

        // Run the test
        final Long result = redisUtilsUnderTest.sDifference("key", Arrays.asList("value"), "destKey");

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testSetMembers() {
        // Setup
        when(mockRedisTemplate.opsForSet()).thenReturn(null);

        // Run the test
        final Set<String> result = redisUtilsUnderTest.setMembers("key");

        // Verify the results
        assertEquals(new HashSet<>(Arrays.asList("value")), result);
    }

    @Test
    public void testSRandomMember() {
        // Setup
        when(mockRedisTemplate.opsForSet()).thenReturn(null);

        // Run the test
        final Object result = redisUtilsUnderTest.sRandomMember("key");

        // Verify the results
    }

    @Test
    public void testSRandomMembers() {
        // Setup
        when(mockRedisTemplate.opsForSet()).thenReturn(null);

        // Run the test
        final List<String> result = redisUtilsUnderTest.sRandomMembers("key", 0L);

        // Verify the results
        assertEquals(Arrays.asList("value"), result);
    }

    @Test
    public void testSDistinctRandomMembers() {
        // Setup
        when(mockRedisTemplate.opsForSet()).thenReturn(null);

        // Run the test
        final Set<String> result = redisUtilsUnderTest.sDistinctRandomMembers("key", 0L);

        // Verify the results
        assertEquals(new HashSet<>(Arrays.asList("value")), result);
    }

    @Test
    public void testSScan() {
        // Setup
        final ScanOptions options = null;
        when(mockRedisTemplate.opsForSet()).thenReturn(null);

        // Run the test
        final Cursor<String> result = redisUtilsUnderTest.sScan("key", options);

        // Verify the results
    }

    @Test
    public void testZAdd1() {
        // Setup
        when(mockRedisTemplate.opsForZSet()).thenReturn(null);

        // Run the test
        final Boolean result = redisUtilsUnderTest.zAdd("key", "value", 0.0);

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testZAdd2() {
        // Setup
        final Set<ZSetOperations.TypedTuple<String>> values = new HashSet<>();
        when(mockRedisTemplate.opsForZSet()).thenReturn(null);

        // Run the test
        final Long result = redisUtilsUnderTest.zAdd("key", values);

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testZRemove() {
        // Setup
        when(mockRedisTemplate.opsForZSet()).thenReturn(null);

        // Run the test
        final Long result = redisUtilsUnderTest.zRemove("key", "values");

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testZIncrementScore() {
        // Setup
        when(mockRedisTemplate.opsForZSet()).thenReturn(null);

        // Run the test
        final Double result = redisUtilsUnderTest.zIncrementScore("key", "value", 0.0);

        // Verify the results
        assertEquals(0.0, result, 0.0001);
    }

    @Test
    public void testZRank() {
        // Setup
        when(mockRedisTemplate.opsForZSet()).thenReturn(null);

        // Run the test
        final Long result = redisUtilsUnderTest.zRank("key", "value");

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testZReverseRank() {
        // Setup
        when(mockRedisTemplate.opsForZSet()).thenReturn(null);

        // Run the test
        final Long result = redisUtilsUnderTest.zReverseRank("key", "value");

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testZRange() {
        // Setup
        when(mockRedisTemplate.opsForZSet()).thenReturn(null);

        // Run the test
        final Set<String> result = redisUtilsUnderTest.zRange("key", 0L, 0L);

        // Verify the results
        assertEquals(new HashSet<>(Arrays.asList("value")), result);
    }

    @Test
    public void testZRangeWithScores() {
        // Setup
        when(mockRedisTemplate.opsForZSet()).thenReturn(null);

        // Run the test
        final Set<ZSetOperations.TypedTuple<String>> result = redisUtilsUnderTest.zRangeWithScores("key", 0L, 0L);

        // Verify the results
    }

    @Test
    public void testZRangeByScore() {
        // Setup
        when(mockRedisTemplate.opsForZSet()).thenReturn(null);

        // Run the test
        final Set<String> result = redisUtilsUnderTest.zRangeByScore("key", 0.0, 0.0);

        // Verify the results
        assertEquals(new HashSet<>(Arrays.asList("value")), result);
    }

    @Test
    public void testZRangeByScoreWithScores1() {
        // Setup
        when(mockRedisTemplate.opsForZSet()).thenReturn(null);

        // Run the test
        final Set<ZSetOperations.TypedTuple<String>> result = redisUtilsUnderTest.zRangeByScoreWithScores("key", 0.0,
                0.0);

        // Verify the results
    }

    @Test
    public void testZRangeByScoreWithScores2() {
        // Setup
        when(mockRedisTemplate.opsForZSet()).thenReturn(null);

        // Run the test
        final Set<ZSetOperations.TypedTuple<String>> result = redisUtilsUnderTest.zRangeByScoreWithScores("key", 0.0,
                0.0, 0L, 0L);

        // Verify the results
    }

    @Test
    public void testZReverseRange() {
        // Setup
        when(mockRedisTemplate.opsForZSet()).thenReturn(null);

        // Run the test
        final Set<String> result = redisUtilsUnderTest.zReverseRange("key", 0L, 0L);

        // Verify the results
        assertEquals(new HashSet<>(Arrays.asList("value")), result);
    }

    @Test
    public void testZReverseRangeWithScores() {
        // Setup
        when(mockRedisTemplate.opsForZSet()).thenReturn(null);

        // Run the test
        final Set<ZSetOperations.TypedTuple<String>> result = redisUtilsUnderTest.zReverseRangeWithScores("key", 0L,
                0L);

        // Verify the results
    }

    @Test
    public void testZReverseRangeByScore1() {
        // Setup
        when(mockRedisTemplate.opsForZSet()).thenReturn(null);

        // Run the test
        final Set<String> result = redisUtilsUnderTest.zReverseRangeByScore("key", 0.0, 0.0);

        // Verify the results
        assertEquals(new HashSet<>(Arrays.asList("value")), result);
    }

    @Test
    public void testZReverseRangeByScoreWithScores() {
        // Setup
        when(mockRedisTemplate.opsForZSet()).thenReturn(null);

        // Run the test
        final Set<ZSetOperations.TypedTuple<String>> result = redisUtilsUnderTest.zReverseRangeByScoreWithScores("key",
                0.0, 0.0);

        // Verify the results
    }

    @Test
    public void testZReverseRangeByScore2() {
        // Setup
        when(mockRedisTemplate.opsForZSet()).thenReturn(null);

        // Run the test
        final Set<String> result = redisUtilsUnderTest.zReverseRangeByScore("key", 0.0, 0.0, 0L, 0L);

        // Verify the results
        assertEquals(new HashSet<>(Arrays.asList("value")), result);
    }

    @Test
    public void testZCount() {
        // Setup
        when(mockRedisTemplate.opsForZSet()).thenReturn(null);

        // Run the test
        final Long result = redisUtilsUnderTest.zCount("key", 0.0, 0.0);

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testZSize() {
        // Setup
        when(mockRedisTemplate.opsForZSet()).thenReturn(null);

        // Run the test
        final Long result = redisUtilsUnderTest.zSize("key");

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testZZCard() {
        // Setup
        when(mockRedisTemplate.opsForZSet()).thenReturn(null);

        // Run the test
        final Long result = redisUtilsUnderTest.zZCard("key");

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testZScore() {
        // Setup
        when(mockRedisTemplate.opsForZSet()).thenReturn(null);

        // Run the test
        final Double result = redisUtilsUnderTest.zScore("key", "value");

        // Verify the results
        assertEquals(0.0, result, 0.0001);
    }

    @Test
    public void testZRemoveRange() {
        // Setup
        when(mockRedisTemplate.opsForZSet()).thenReturn(null);

        // Run the test
        final Long result = redisUtilsUnderTest.zRemoveRange("key", 0L, 0L);

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testZRemoveRangeByScore() {
        // Setup
        when(mockRedisTemplate.opsForZSet()).thenReturn(null);

        // Run the test
        final Long result = redisUtilsUnderTest.zRemoveRangeByScore("key", 0.0, 0.0);

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testZUnionAndStore1() {
        // Setup
        when(mockRedisTemplate.opsForZSet()).thenReturn(null);

        // Run the test
        final Long result = redisUtilsUnderTest.zUnionAndStore("key", "otherKey", "destKey");

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testZUnionAndStore2() {
        // Setup
        when(mockRedisTemplate.opsForZSet()).thenReturn(null);

        // Run the test
        final Long result = redisUtilsUnderTest.zUnionAndStore("key", Arrays.asList("value"), "destKey");

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testZIntersectAndStore1() {
        // Setup
        when(mockRedisTemplate.opsForZSet()).thenReturn(null);

        // Run the test
        final Long result = redisUtilsUnderTest.zIntersectAndStore("key", "otherKey", "destKey");

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testZIntersectAndStore2() {
        // Setup
        when(mockRedisTemplate.opsForZSet()).thenReturn(null);

        // Run the test
        final Long result = redisUtilsUnderTest.zIntersectAndStore("key", Arrays.asList("value"), "destKey");

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testZScan() {
        // Setup
        final ScanOptions options = null;
        when(mockRedisTemplate.opsForZSet()).thenReturn(null);

        // Run the test
        final Cursor<ZSetOperations.TypedTuple<String>> result = redisUtilsUnderTest.zScan("key", options);

        // Verify the results
    }
}
