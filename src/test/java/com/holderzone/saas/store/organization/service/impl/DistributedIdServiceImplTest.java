package com.holderzone.saas.store.organization.service.impl;

import com.holderzone.saas.store.organization.service.impl.DistributedIdServiceImpl;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.Arrays;
import java.util.Collections;

import static org.junit.Assert.assertEquals;

@RunWith(MockitoJUnitRunner.class)
public class DistributedIdServiceImplTest {

    @Mock
    private RedisTemplate mockRedisTemplate;

    private DistributedIdServiceImpl distributedIdServiceImplUnderTest;

    @Before
    public void setUp() {
        distributedIdServiceImplUnderTest = new DistributedIdServiceImpl(mockRedisTemplate);
    }

    @Test
    public void testRawId() {
        assertEquals(Long.valueOf(0L), distributedIdServiceImplUnderTest.rawId("tag"));
    }

    @Test
    public void testNextId() {
        assertEquals("result", distributedIdServiceImplUnderTest.nextId("tag"));
    }

    @Test
    public void testNextBatchId() {
        assertEquals(Arrays.asList("value"), distributedIdServiceImplUnderTest.nextBatchId("tag", 0L));
        assertEquals(Collections.emptyList(), distributedIdServiceImplUnderTest.nextBatchId("tag", 0L));
    }

    @Test
    public void testNextOrganizationGuid() {
        assertEquals("result", distributedIdServiceImplUnderTest.nextOrganizationGuid());
    }

    @Test
    public void testNextBatchPointItemGuid() {
        assertEquals(Arrays.asList("value"), distributedIdServiceImplUnderTest.nextBatchPointItemGuid(0L));
        assertEquals(Collections.emptyList(), distributedIdServiceImplUnderTest.nextBatchPointItemGuid(0L));
    }

    @Test
    public void testNextStoreBrandGuid() {
        assertEquals("result", distributedIdServiceImplUnderTest.nextStoreBrandGuid());
    }

    @Test
    public void testNextBatchDstAreaGuid() {
        assertEquals(Arrays.asList("value"), distributedIdServiceImplUnderTest.nextBatchDstAreaGuid(0L));
        assertEquals(Collections.emptyList(), distributedIdServiceImplUnderTest.nextBatchDstAreaGuid(0L));
    }

    @Test
    public void testNextBatchDstItemGuid() {
        assertEquals(Arrays.asList("value"), distributedIdServiceImplUnderTest.nextBatchDstItemGuid(0L));
        assertEquals(Collections.emptyList(), distributedIdServiceImplUnderTest.nextBatchDstItemGuid(0L));
    }

    @Test
    public void testNextPrintRecordGuid() {
        assertEquals("result", distributedIdServiceImplUnderTest.nextPrintRecordGuid());
    }

    @Test
    public void testNextBatchPrintRecordGuid() {
        assertEquals(Arrays.asList("value"), distributedIdServiceImplUnderTest.nextBatchPrintRecordGuid(0L));
        assertEquals(Collections.emptyList(), distributedIdServiceImplUnderTest.nextBatchPrintRecordGuid(0L));
    }

    @Test
    public void testNextBrandGuid() {
        assertEquals("result", distributedIdServiceImplUnderTest.nextBrandGuid());
    }

    @Test
    public void testNextBatchKitchenItemGuid() {
        assertEquals(Arrays.asList("value"), distributedIdServiceImplUnderTest.nextBatchKitchenItemGuid(0L));
        assertEquals(Collections.emptyList(), distributedIdServiceImplUnderTest.nextBatchKitchenItemGuid(0L));
    }

    @Test
    public void testNextBatchKitchenAttrGuid() {
        assertEquals(Arrays.asList("value"), distributedIdServiceImplUnderTest.nextBatchKitchenAttrGuid(0L));
        assertEquals(Collections.emptyList(), distributedIdServiceImplUnderTest.nextBatchKitchenAttrGuid(0L));
    }
}
