package com.holderzone.saas.store.organization.service.impl;

import com.holderzone.framework.rocketmq.common.DefaultRocketMqProducer;
import com.holderzone.resource.common.dto.device.DeviceDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceDTO;
import com.holderzone.saas.store.organization.domain.OrganizationDO;
import com.holderzone.saas.store.organization.service.impl.BroadcastServiceImpl;
import org.apache.rocketmq.common.message.Message;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class BroadcastServiceImplTest {

    @Mock
    private DefaultRocketMqProducer mockDefaultRocketMqProducer;

    private BroadcastServiceImpl broadcastServiceImplUnderTest;

    @Before
    public void setUp() {
        broadcastServiceImplUnderTest = new BroadcastServiceImpl(mockDefaultRocketMqProducer);
    }

    @Test
    public void testStoreCreated() {
        // Setup
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("78a3a3cf-6f99-44c0-87cd-7ee920f586e1");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");

        // Run the test
        broadcastServiceImplUnderTest.storeCreated(storeDTO);

        // Verify the results
        verify(mockDefaultRocketMqProducer).sendMessage(any(Message.class));
    }

    @Test
    public void testStoreUpdated() {
        // Setup
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("78a3a3cf-6f99-44c0-87cd-7ee920f586e1");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");

        // Run the test
        broadcastServiceImplUnderTest.storeUpdated(storeDTO);

        // Verify the results
        verify(mockDefaultRocketMqProducer).sendMessage(any(Message.class));
    }

    @Test
    public void testCreateStoreInCloud() {
        // Setup
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("78a3a3cf-6f99-44c0-87cd-7ee920f586e1");
        organizationDO.setCode("code");
        organizationDO.setName("name");
        organizationDO.setContactName("contactName");
        organizationDO.setContactTel("contactNumber");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("province");
        organizationDO.setCityCode("city");
        organizationDO.setCountyCode("countyCode");
        organizationDO.setAddressDetail("addr");
        organizationDO.setIsEnable(false);
        organizationDO.setIsSelfBuildItems(0);

        when(mockDefaultRocketMqProducer.sendMessage(any(Message.class))).thenReturn(false);

        // Run the test
        broadcastServiceImplUnderTest.createStoreInCloud(organizationDO);

        // Verify the results
    }

    @Test
    public void testUpdateStoreInCloud() {
        // Setup
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("78a3a3cf-6f99-44c0-87cd-7ee920f586e1");
        organizationDO.setCode("code");
        organizationDO.setName("name");
        organizationDO.setContactName("contactName");
        organizationDO.setContactTel("contactNumber");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("province");
        organizationDO.setCityCode("city");
        organizationDO.setCountyCode("countyCode");
        organizationDO.setAddressDetail("addr");
        organizationDO.setIsEnable(false);
        organizationDO.setIsSelfBuildItems(0);

        when(mockDefaultRocketMqProducer.sendMessage(any(Message.class))).thenReturn(false);

        // Run the test
        broadcastServiceImplUnderTest.updateStoreInCloud(organizationDO);

        // Verify the results
    }

    @Test
    public void testEnableStoreInCloud() {
        // Setup
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("78a3a3cf-6f99-44c0-87cd-7ee920f586e1");
        organizationDO.setCode("code");
        organizationDO.setName("name");
        organizationDO.setContactName("contactName");
        organizationDO.setContactTel("contactNumber");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("province");
        organizationDO.setCityCode("city");
        organizationDO.setCountyCode("countyCode");
        organizationDO.setAddressDetail("addr");
        organizationDO.setIsEnable(false);
        organizationDO.setIsSelfBuildItems(0);

        when(mockDefaultRocketMqProducer.sendMessage(any(Message.class))).thenReturn(false);

        // Run the test
        broadcastServiceImplUnderTest.enableStoreInCloud(organizationDO);

        // Verify the results
    }

    @Test
    public void testDeleteStoreInCloud() {
        // Setup
        when(mockDefaultRocketMqProducer.sendMessage(any(Message.class))).thenReturn(false);

        // Run the test
        broadcastServiceImplUnderTest.deleteStoreInCloud("78a3a3cf-6f99-44c0-87cd-7ee920f586e1");

        // Verify the results
    }

    @Test
    public void testCreateWareHouse() {
        // Setup
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setGuid("78a3a3cf-6f99-44c0-87cd-7ee920f586e1");
        organizationDO.setCode("code");
        organizationDO.setName("name");
        organizationDO.setContactName("contactName");
        organizationDO.setContactTel("contactNumber");
        organizationDO.setParentIds("parentIds");
        organizationDO.setProvinceCode("province");
        organizationDO.setCityCode("city");
        organizationDO.setCountyCode("countyCode");
        organizationDO.setAddressDetail("addr");
        organizationDO.setIsEnable(false);
        organizationDO.setIsSelfBuildItems(0);

        when(mockDefaultRocketMqProducer.sendMessage(any(Message.class))).thenReturn(false);

        // Run the test
        broadcastServiceImplUnderTest.createWareHouse(organizationDO);

        // Verify the results
    }

    @Test
    public void testUpdateWareHouse() {
        // Setup
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("78a3a3cf-6f99-44c0-87cd-7ee920f586e1");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");

        when(mockDefaultRocketMqProducer.sendMessage(any(Message.class))).thenReturn(false);

        // Run the test
        broadcastServiceImplUnderTest.updateWareHouse(storeDTO);

        // Verify the results
    }

    @Test
    public void testCreateMemberStore() {
        // Setup
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("78a3a3cf-6f99-44c0-87cd-7ee920f586e1");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");

        // Run the test
        broadcastServiceImplUnderTest.createMemberStore(storeDTO);

        // Verify the results
        verify(mockDefaultRocketMqProducer).sendMessage(any(Message.class));
    }

    @Test
    public void testUpdateMemberStore() {
        // Setup
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("78a3a3cf-6f99-44c0-87cd-7ee920f586e1");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");

        // Run the test
        broadcastServiceImplUnderTest.updateMemberStore(storeDTO);

        // Verify the results
        verify(mockDefaultRocketMqProducer).sendMessage(any(Message.class));
    }

    @Test
    public void testDeleteMemberStore() {
        // Setup
        // Run the test
        broadcastServiceImplUnderTest.deleteMemberStore("78a3a3cf-6f99-44c0-87cd-7ee920f586e1");

        // Verify the results
        verify(mockDefaultRocketMqProducer).sendMessage(any(Message.class));
    }

    @Test
    public void testDeviceBind() {
        // Setup
        final StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO();
        storeDeviceDTO.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO.setStoreNo("storeNo");
        storeDeviceDTO.setStoreGuid("storeGuid");
        storeDeviceDTO.setDeviceNo("deviceNo");
        storeDeviceDTO.setDeviceGuid("deviceGuid");

        // Run the test
        broadcastServiceImplUnderTest.deviceBind(storeDeviceDTO);

        // Verify the results
        verify(mockDefaultRocketMqProducer).sendMessage(any(Message.class));
    }

    @Test
    public void testDeviceUnbind() {
        // Setup
        final StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO();
        storeDeviceDTO.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO.setStoreNo("storeNo");
        storeDeviceDTO.setStoreGuid("storeGuid");
        storeDeviceDTO.setDeviceNo("deviceNo");
        storeDeviceDTO.setDeviceGuid("deviceGuid");

        // Run the test
        broadcastServiceImplUnderTest.deviceUnbind(storeDeviceDTO);

        // Verify the results
        verify(mockDefaultRocketMqProducer).sendMessage(any(Message.class));
    }

    @Test
    public void testUnBindDeviceToCloud() {
        // Setup
        final DeviceDTO deviceDTO = new DeviceDTO();
        deviceDTO.setStaffGuid("staffGuid");
        deviceDTO.setDeviceGuid("deviceGuid");
        deviceDTO.setDeviceNo("deviceNo");
        deviceDTO.setEnterpriseGuid("enterpriseGuid");
        deviceDTO.setStoreGuid("storeGuid");

        // Run the test
        broadcastServiceImplUnderTest.unBindDeviceToCloud(deviceDTO);

        // Verify the results
        verify(mockDefaultRocketMqProducer).sendMessage(any(Message.class));
    }

    @Test
    public void testBindDeviceToCloud() {
        // Setup
        final DeviceDTO deviceDTO = new DeviceDTO();
        deviceDTO.setStaffGuid("staffGuid");
        deviceDTO.setDeviceGuid("deviceGuid");
        deviceDTO.setDeviceNo("deviceNo");
        deviceDTO.setEnterpriseGuid("enterpriseGuid");
        deviceDTO.setStoreGuid("storeGuid");

        // Run the test
        broadcastServiceImplUnderTest.bindDeviceToCloud(deviceDTO);

        // Verify the results
        verify(mockDefaultRocketMqProducer).sendMessage(any(Message.class));
    }
}
