package com.holderzone.saas.store.organization.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.rocketmq.common.DefaultRocketMqProducer;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.dto.organization.QueryBrandDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.organization.domain.BrandDO;
import com.holderzone.saas.store.organization.domain.OrganizationDO;
import com.holderzone.saas.store.organization.domain.StoreBrandDO;
import com.holderzone.saas.store.organization.mapper.BrandMapper;
import com.holderzone.saas.store.organization.mapper.OrganizationMapper;
import com.holderzone.saas.store.organization.mapper.StoreBrandMapper;
import com.holderzone.saas.store.organization.mapstruct.BrandMapstruct;
import com.holderzone.saas.store.organization.mapstruct.StoreMapstruct;
import com.holderzone.saas.store.organization.service.DistributedIdService;
import com.holderzone.saas.store.organization.service.impl.BrandServiceImpl;
import com.holderzone.saas.store.organization.service.remote.ItemClient;
import com.holderzone.saas.store.organization.service.remote.UserClient;
import com.holderzone.saas.store.organization.utils.DynamicHelper;
import org.apache.poi.ss.formula.functions.T;
import org.apache.rocketmq.common.message.Message;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.RedisTemplate;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class BrandServiceImplTest {

    @Mock
    private BrandMapper mockBrandMapper;
    @Mock
    private StoreBrandMapper mockStoreBrandMapper;
    @Mock
    private BrandMapstruct mockBrandMapstruct;
    @Mock
    private RedisTemplate<String, String> mockRedisTemplate;
    @Mock
    private ItemClient mockItemClient;
    @Mock
    private DefaultRocketMqProducer mockDefaultRocketMqProducer;
    @Mock
    private DistributedIdService mockDistributedIdService;
    @Mock
    private UserClient mockUserClient;
    @Mock
    private OrganizationMapper mockOrganizationMapper;
    @Mock
    private StoreMapstruct mockStoreMapstruct;
    @Mock
    private DynamicHelper mockDynamicHelper;

    private BrandServiceImpl brandServiceImplUnderTest;

    @Before
    public void setUp() {
        brandServiceImplUnderTest = new BrandServiceImpl(mockBrandMapper, mockStoreBrandMapper, mockBrandMapstruct,
                mockRedisTemplate, mockItemClient, mockDefaultRocketMqProducer, mockDistributedIdService,
                mockUserClient, mockOrganizationMapper, mockStoreMapstruct, mockDynamicHelper);
    }

    @Test
    public void testCreateBrand() {
        // Setup
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("brandGuid");
        brandDTO.setName("name");
        brandDTO.setMchntTypeCode("mchntTypeCode");
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        brandDTO.setStoreList(Arrays.asList(storeDTO));
        brandDTO.setSalesModel(0);
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);

        when(mockBrandMapper.selectCount(any(Wrapper.class))).thenReturn(0);

        // Configure BrandMapstruct.brandDTO2DO(...).
        final BrandDO brandDO = new BrandDO();
        brandDO.setGuid("brandGuid");
        brandDO.setName("我的品牌");
        brandDO.setDescription("默认品牌");
        brandDO.setLogoUrl("logoUrl");
        brandDO.setIsEnable(false);
        brandDO.setIsDeleted(false);
        brandDO.setCreateUserGuid("未定义");
        brandDO.setModifiedUserGuid("未定义");
        brandDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        brandDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        brandDO.setSalesModel(0);
        brandDO.setIsBuAccounts(0);
        brandDO.setIsShowCash(0);
        brandDO.setIsMultiHandover(0);
        when(mockBrandMapstruct.brandDTO2DO(any(BrandDTO.class))).thenReturn(brandDO);

        // Configure BrandMapstruct.brandDO2DTO(...).
        final BrandDTO brandDTO1 = new BrandDTO();
        brandDTO1.setGuid("brandGuid");
        brandDTO1.setName("name");
        brandDTO1.setMchntTypeCode("mchntTypeCode");
        final StoreDTO storeDTO1 = new StoreDTO();
        storeDTO1.setGuid("guid");
        storeDTO1.setCreateUserGuid("createUserGuid");
        storeDTO1.setModifiedUserGuid("modifiedUserGuid");
        storeDTO1.setIsBuAccounts(0);
        storeDTO1.setIsShowCash(0);
        brandDTO1.setStoreList(Arrays.asList(storeDTO1));
        brandDTO1.setSalesModel(0);
        brandDTO1.setIsBuAccounts(0);
        brandDTO1.setIsShowCash(0);
        brandDTO1.setIsMultiHandover(0);
        final BrandDO brandDO1 = new BrandDO();
        brandDO1.setGuid("brandGuid");
        brandDO1.setName("我的品牌");
        brandDO1.setDescription("默认品牌");
        brandDO1.setLogoUrl("logoUrl");
        brandDO1.setIsEnable(false);
        brandDO1.setIsDeleted(false);
        brandDO1.setCreateUserGuid("未定义");
        brandDO1.setModifiedUserGuid("未定义");
        brandDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        brandDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        brandDO1.setSalesModel(0);
        brandDO1.setIsBuAccounts(0);
        brandDO1.setIsShowCash(0);
        brandDO1.setIsMultiHandover(0);
        when(mockBrandMapstruct.brandDO2DTO(brandDO1)).thenReturn(brandDTO1);

        when(mockUserClient.queryMchntType()).thenReturn("mchntTypeCode");

        // Configure OrganizationMapper.queryStoreDetail(...).
        final StoreDTO storeDTO2 = new StoreDTO();
        storeDTO2.setGuid("guid");
        storeDTO2.setCreateUserGuid("createUserGuid");
        storeDTO2.setModifiedUserGuid("modifiedUserGuid");
        storeDTO2.setIsBuAccounts(0);
        storeDTO2.setIsShowCash(0);
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO2);
        when(mockOrganizationMapper.queryStoreDetail(Arrays.asList("value"))).thenReturn(storeDTOS);

        when(mockDistributedIdService.nextStoreBrandGuid()).thenReturn("f8aba60c-935f-4b39-9a17-37c45bb54a04");

        // Run the test
        final BrandDTO result = brandServiceImplUnderTest.createBrand(brandDTO);

        // Verify the results
        verify(mockDefaultRocketMqProducer).sendMessage(any(Message.class));
        verify(mockStoreBrandMapper).delete(any(LambdaQueryWrapper.class));

        // Confirm StoreBrandMapper.insert(...).
        final StoreBrandDO entity = new StoreBrandDO();
        entity.setGuid("f8aba60c-935f-4b39-9a17-37c45bb54a04");
        entity.setStoreGuid("guid");
        entity.setBrandGuid("brandGuid");
        entity.setCreateUserGuid("createUserGuid");
        entity.setModifiedUserGuid("modifiedUserGuid");
        entity.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        verify(mockStoreBrandMapper).insert(entity);
    }

    @Test
    public void testCreateBrand_OrganizationMapperReturnsNoItems() {
        // Setup
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("brandGuid");
        brandDTO.setName("name");
        brandDTO.setMchntTypeCode("mchntTypeCode");
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        brandDTO.setStoreList(Arrays.asList(storeDTO));
        brandDTO.setSalesModel(0);
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);

        when(mockBrandMapper.selectCount(any(Wrapper.class))).thenReturn(0);

        // Configure BrandMapstruct.brandDTO2DO(...).
        final BrandDO brandDO = new BrandDO();
        brandDO.setGuid("brandGuid");
        brandDO.setName("我的品牌");
        brandDO.setDescription("默认品牌");
        brandDO.setLogoUrl("logoUrl");
        brandDO.setIsEnable(false);
        brandDO.setIsDeleted(false);
        brandDO.setCreateUserGuid("未定义");
        brandDO.setModifiedUserGuid("未定义");
        brandDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        brandDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        brandDO.setSalesModel(0);
        brandDO.setIsBuAccounts(0);
        brandDO.setIsShowCash(0);
        brandDO.setIsMultiHandover(0);
        when(mockBrandMapstruct.brandDTO2DO(any(BrandDTO.class))).thenReturn(brandDO);

        // Configure BrandMapstruct.brandDO2DTO(...).
        final BrandDTO brandDTO1 = new BrandDTO();
        brandDTO1.setGuid("brandGuid");
        brandDTO1.setName("name");
        brandDTO1.setMchntTypeCode("mchntTypeCode");
        final StoreDTO storeDTO1 = new StoreDTO();
        storeDTO1.setGuid("guid");
        storeDTO1.setCreateUserGuid("createUserGuid");
        storeDTO1.setModifiedUserGuid("modifiedUserGuid");
        storeDTO1.setIsBuAccounts(0);
        storeDTO1.setIsShowCash(0);
        brandDTO1.setStoreList(Arrays.asList(storeDTO1));
        brandDTO1.setSalesModel(0);
        brandDTO1.setIsBuAccounts(0);
        brandDTO1.setIsShowCash(0);
        brandDTO1.setIsMultiHandover(0);
        final BrandDO brandDO1 = new BrandDO();
        brandDO1.setGuid("brandGuid");
        brandDO1.setName("我的品牌");
        brandDO1.setDescription("默认品牌");
        brandDO1.setLogoUrl("logoUrl");
        brandDO1.setIsEnable(false);
        brandDO1.setIsDeleted(false);
        brandDO1.setCreateUserGuid("未定义");
        brandDO1.setModifiedUserGuid("未定义");
        brandDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        brandDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        brandDO1.setSalesModel(0);
        brandDO1.setIsBuAccounts(0);
        brandDO1.setIsShowCash(0);
        brandDO1.setIsMultiHandover(0);
        when(mockBrandMapstruct.brandDO2DTO(brandDO1)).thenReturn(brandDTO1);

        when(mockUserClient.queryMchntType()).thenReturn("mchntTypeCode");
        when(mockOrganizationMapper.queryStoreDetail(Arrays.asList("value"))).thenReturn(Collections.emptyList());
        when(mockDistributedIdService.nextStoreBrandGuid()).thenReturn("f8aba60c-935f-4b39-9a17-37c45bb54a04");

        // Run the test
        final BrandDTO result = brandServiceImplUnderTest.createBrand(brandDTO);

        // Verify the results
        verify(mockDefaultRocketMqProducer).sendMessage(any(Message.class));
        verify(mockStoreBrandMapper).delete(any(LambdaQueryWrapper.class));

        // Confirm StoreBrandMapper.insert(...).
        final StoreBrandDO entity = new StoreBrandDO();
        entity.setGuid("f8aba60c-935f-4b39-9a17-37c45bb54a04");
        entity.setStoreGuid("guid");
        entity.setBrandGuid("brandGuid");
        entity.setCreateUserGuid("createUserGuid");
        entity.setModifiedUserGuid("modifiedUserGuid");
        entity.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        verify(mockStoreBrandMapper).insert(entity);
    }

    @Test
    public void testUpdateBrand() {
        // Setup
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("brandGuid");
        brandDTO.setName("name");
        brandDTO.setMchntTypeCode("mchntTypeCode");
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        brandDTO.setStoreList(Arrays.asList(storeDTO));
        brandDTO.setSalesModel(0);
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);

        // Configure BrandMapstruct.brandDTO2DO(...).
        final BrandDO brandDO = new BrandDO();
        brandDO.setGuid("brandGuid");
        brandDO.setName("我的品牌");
        brandDO.setDescription("默认品牌");
        brandDO.setLogoUrl("logoUrl");
        brandDO.setIsEnable(false);
        brandDO.setIsDeleted(false);
        brandDO.setCreateUserGuid("未定义");
        brandDO.setModifiedUserGuid("未定义");
        brandDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        brandDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        brandDO.setSalesModel(0);
        brandDO.setIsBuAccounts(0);
        brandDO.setIsShowCash(0);
        brandDO.setIsMultiHandover(0);
        when(mockBrandMapstruct.brandDTO2DO(any(BrandDTO.class))).thenReturn(brandDO);

        when(mockBrandMapper.selectCount(any(Wrapper.class))).thenReturn(0);

        // Configure StoreBrandMapper.selectList(...).
        final StoreBrandDO storeBrandDO = new StoreBrandDO();
        storeBrandDO.setGuid("f8aba60c-935f-4b39-9a17-37c45bb54a04");
        storeBrandDO.setStoreGuid("guid");
        storeBrandDO.setBrandGuid("brandGuid");
        storeBrandDO.setCreateUserGuid("createUserGuid");
        storeBrandDO.setModifiedUserGuid("modifiedUserGuid");
        storeBrandDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeBrandDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<StoreBrandDO> storeBrandDOS = Arrays.asList(storeBrandDO);
        when(mockStoreBrandMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(storeBrandDOS);

        // Configure OrganizationMapper.queryStoreDetail(...).
        final StoreDTO storeDTO1 = new StoreDTO();
        storeDTO1.setGuid("guid");
        storeDTO1.setCreateUserGuid("createUserGuid");
        storeDTO1.setModifiedUserGuid("modifiedUserGuid");
        storeDTO1.setIsBuAccounts(0);
        storeDTO1.setIsShowCash(0);
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO1);
        when(mockOrganizationMapper.queryStoreDetail(Arrays.asList("value"))).thenReturn(storeDTOS);

        when(mockDistributedIdService.nextStoreBrandGuid()).thenReturn("f8aba60c-935f-4b39-9a17-37c45bb54a04");

        // Configure StoreMapstruct.storeDTO2DTO(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setId(0L);
        organizationDO.setGuid("9853abda-f5ea-4599-ad7c-e6ccbbb71c38");
        organizationDO.setType(0);
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockStoreMapstruct.storeDTO2DTO(any(StoreDTO.class))).thenReturn(organizationDO);

        // Run the test
        final boolean result = brandServiceImplUnderTest.updateBrand(brandDTO);

        // Verify the results
        assertTrue(result);
        verify(mockOrganizationMapper).updateStoreLogoUrl("logoUrl", Arrays.asList("value"));
        verify(mockStoreBrandMapper).delete(any(LambdaQueryWrapper.class));

        // Confirm StoreBrandMapper.insert(...).
        final StoreBrandDO entity = new StoreBrandDO();
        entity.setGuid("f8aba60c-935f-4b39-9a17-37c45bb54a04");
        entity.setStoreGuid("guid");
        entity.setBrandGuid("brandGuid");
        entity.setCreateUserGuid("createUserGuid");
        entity.setModifiedUserGuid("modifiedUserGuid");
        entity.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        verify(mockStoreBrandMapper).insert(entity);

        // Confirm OrganizationMapper.update(...).
        final OrganizationDO entity1 = new OrganizationDO();
        entity1.setId(0L);
        entity1.setGuid("9853abda-f5ea-4599-ad7c-e6ccbbb71c38");
        entity1.setType(0);
        entity1.setModifiedUserGuid("modifiedUserGuid");
        entity1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        verify(mockOrganizationMapper).update(eq(entity1), any(LambdaQueryWrapper.class));
    }

    @Test
    public void testUpdateBrand_StoreBrandMapperSelectListReturnsNoItems() {
        // Setup
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("brandGuid");
        brandDTO.setName("name");
        brandDTO.setMchntTypeCode("mchntTypeCode");
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        brandDTO.setStoreList(Arrays.asList(storeDTO));
        brandDTO.setSalesModel(0);
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);

        // Configure BrandMapstruct.brandDTO2DO(...).
        final BrandDO brandDO = new BrandDO();
        brandDO.setGuid("brandGuid");
        brandDO.setName("我的品牌");
        brandDO.setDescription("默认品牌");
        brandDO.setLogoUrl("logoUrl");
        brandDO.setIsEnable(false);
        brandDO.setIsDeleted(false);
        brandDO.setCreateUserGuid("未定义");
        brandDO.setModifiedUserGuid("未定义");
        brandDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        brandDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        brandDO.setSalesModel(0);
        brandDO.setIsBuAccounts(0);
        brandDO.setIsShowCash(0);
        brandDO.setIsMultiHandover(0);
        when(mockBrandMapstruct.brandDTO2DO(any(BrandDTO.class))).thenReturn(brandDO);

        when(mockBrandMapper.selectCount(any(Wrapper.class))).thenReturn(0);
        when(mockStoreBrandMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure OrganizationMapper.queryStoreDetail(...).
        final StoreDTO storeDTO1 = new StoreDTO();
        storeDTO1.setGuid("guid");
        storeDTO1.setCreateUserGuid("createUserGuid");
        storeDTO1.setModifiedUserGuid("modifiedUserGuid");
        storeDTO1.setIsBuAccounts(0);
        storeDTO1.setIsShowCash(0);
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO1);
        when(mockOrganizationMapper.queryStoreDetail(Arrays.asList("value"))).thenReturn(storeDTOS);

        when(mockDistributedIdService.nextStoreBrandGuid()).thenReturn("f8aba60c-935f-4b39-9a17-37c45bb54a04");

        // Configure StoreMapstruct.storeDTO2DTO(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setId(0L);
        organizationDO.setGuid("9853abda-f5ea-4599-ad7c-e6ccbbb71c38");
        organizationDO.setType(0);
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockStoreMapstruct.storeDTO2DTO(any(StoreDTO.class))).thenReturn(organizationDO);

        // Run the test
        final boolean result = brandServiceImplUnderTest.updateBrand(brandDTO);

        // Verify the results
        assertTrue(result);
        verify(mockStoreBrandMapper).delete(any(LambdaQueryWrapper.class));

        // Confirm StoreBrandMapper.insert(...).
        final StoreBrandDO entity = new StoreBrandDO();
        entity.setGuid("f8aba60c-935f-4b39-9a17-37c45bb54a04");
        entity.setStoreGuid("guid");
        entity.setBrandGuid("brandGuid");
        entity.setCreateUserGuid("createUserGuid");
        entity.setModifiedUserGuid("modifiedUserGuid");
        entity.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        verify(mockStoreBrandMapper).insert(entity);

        // Confirm OrganizationMapper.update(...).
        final OrganizationDO entity1 = new OrganizationDO();
        entity1.setId(0L);
        entity1.setGuid("9853abda-f5ea-4599-ad7c-e6ccbbb71c38");
        entity1.setType(0);
        entity1.setModifiedUserGuid("modifiedUserGuid");
        entity1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        verify(mockOrganizationMapper).update(eq(entity1), any(LambdaQueryWrapper.class));
    }

    @Test
    public void testUpdateBrand_OrganizationMapperQueryStoreDetailReturnsNoItems() {
        // Setup
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("brandGuid");
        brandDTO.setName("name");
        brandDTO.setMchntTypeCode("mchntTypeCode");
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        brandDTO.setStoreList(Arrays.asList(storeDTO));
        brandDTO.setSalesModel(0);
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);

        // Configure BrandMapstruct.brandDTO2DO(...).
        final BrandDO brandDO = new BrandDO();
        brandDO.setGuid("brandGuid");
        brandDO.setName("我的品牌");
        brandDO.setDescription("默认品牌");
        brandDO.setLogoUrl("logoUrl");
        brandDO.setIsEnable(false);
        brandDO.setIsDeleted(false);
        brandDO.setCreateUserGuid("未定义");
        brandDO.setModifiedUserGuid("未定义");
        brandDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        brandDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        brandDO.setSalesModel(0);
        brandDO.setIsBuAccounts(0);
        brandDO.setIsShowCash(0);
        brandDO.setIsMultiHandover(0);
        when(mockBrandMapstruct.brandDTO2DO(any(BrandDTO.class))).thenReturn(brandDO);

        when(mockBrandMapper.selectCount(any(Wrapper.class))).thenReturn(0);

        // Configure StoreBrandMapper.selectList(...).
        final StoreBrandDO storeBrandDO = new StoreBrandDO();
        storeBrandDO.setGuid("f8aba60c-935f-4b39-9a17-37c45bb54a04");
        storeBrandDO.setStoreGuid("guid");
        storeBrandDO.setBrandGuid("brandGuid");
        storeBrandDO.setCreateUserGuid("createUserGuid");
        storeBrandDO.setModifiedUserGuid("modifiedUserGuid");
        storeBrandDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeBrandDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<StoreBrandDO> storeBrandDOS = Arrays.asList(storeBrandDO);
        when(mockStoreBrandMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(storeBrandDOS);

        when(mockOrganizationMapper.queryStoreDetail(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        final boolean result = brandServiceImplUnderTest.updateBrand(brandDTO);

        // Verify the results
        assertTrue(result);
        verify(mockOrganizationMapper).updateStoreLogoUrl("logoUrl", Arrays.asList("value"));
        verify(mockStoreBrandMapper).delete(any(LambdaQueryWrapper.class));
    }

    @Test
    public void testDeleteBrand() {
        // Setup
        when(mockStoreBrandMapper.selectCount(any(Wrapper.class))).thenReturn(0);
        when(mockItemClient.countTypeOrItem("brandGuid")).thenReturn(false);

        // Run the test
        final boolean result = brandServiceImplUnderTest.deleteBrand("brandGuid");

        // Verify the results
        assertTrue(result);
    }

    @Test(expected = BusinessException.class)
    public void testDeleteBrand_ItemClientReturnsTrue() {
        // Setup
        when(mockStoreBrandMapper.selectCount(any(Wrapper.class))).thenReturn(0);
        when(mockItemClient.countTypeOrItem("brandGuid")).thenReturn(true);

        // Run the test
        brandServiceImplUnderTest.deleteBrand("brandGuid");
    }

    @Test
    public void testQueryBrandByGuid() {
        // Setup
        // Configure BrandMapstruct.brandDO2DTO(...).
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("brandGuid");
        brandDTO.setName("name");
        brandDTO.setMchntTypeCode("mchntTypeCode");
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        brandDTO.setStoreList(Arrays.asList(storeDTO));
        brandDTO.setSalesModel(0);
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        final BrandDO brandDO = new BrandDO();
        brandDO.setGuid("brandGuid");
        brandDO.setName("我的品牌");
        brandDO.setDescription("默认品牌");
        brandDO.setLogoUrl("logoUrl");
        brandDO.setIsEnable(false);
        brandDO.setIsDeleted(false);
        brandDO.setCreateUserGuid("未定义");
        brandDO.setModifiedUserGuid("未定义");
        brandDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        brandDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        brandDO.setSalesModel(0);
        brandDO.setIsBuAccounts(0);
        brandDO.setIsShowCash(0);
        brandDO.setIsMultiHandover(0);
        when(mockBrandMapstruct.brandDO2DTO(brandDO)).thenReturn(brandDTO);

        // Run the test
        final BrandDTO result = brandServiceImplUnderTest.queryBrandByGuid("brandGuid");

        // Verify the results
    }

    @Test
    public void testQueryBrandByIdList() {
        // Setup
        // Configure BrandMapstruct.brandDOList2DTOList(...).
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("brandGuid");
        brandDTO.setName("name");
        brandDTO.setMchntTypeCode("mchntTypeCode");
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        brandDTO.setStoreList(Arrays.asList(storeDTO));
        brandDTO.setSalesModel(0);
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        final List<BrandDTO> brandDTOS = Arrays.asList(brandDTO);
        final BrandDO brandDO = new BrandDO();
        brandDO.setGuid("brandGuid");
        brandDO.setName("我的品牌");
        brandDO.setDescription("默认品牌");
        brandDO.setLogoUrl("logoUrl");
        brandDO.setIsEnable(false);
        brandDO.setIsDeleted(false);
        brandDO.setCreateUserGuid("未定义");
        brandDO.setModifiedUserGuid("未定义");
        brandDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        brandDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        brandDO.setSalesModel(0);
        brandDO.setIsBuAccounts(0);
        brandDO.setIsShowCash(0);
        brandDO.setIsMultiHandover(0);
        final List<BrandDO> brandDOList = Arrays.asList(brandDO);
        when(mockBrandMapstruct.brandDOList2DTOList(brandDOList)).thenReturn(brandDTOS);

        // Run the test
        final List<BrandDTO> result = brandServiceImplUnderTest.queryBrandByIdList(Arrays.asList("value"));

        // Verify the results
    }

    @Test
    public void testQueryBrandByIdList_BrandMapstructReturnsNoItems() {
        // Setup
        // Configure BrandMapstruct.brandDOList2DTOList(...).
        final BrandDO brandDO = new BrandDO();
        brandDO.setGuid("brandGuid");
        brandDO.setName("我的品牌");
        brandDO.setDescription("默认品牌");
        brandDO.setLogoUrl("logoUrl");
        brandDO.setIsEnable(false);
        brandDO.setIsDeleted(false);
        brandDO.setCreateUserGuid("未定义");
        brandDO.setModifiedUserGuid("未定义");
        brandDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        brandDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        brandDO.setSalesModel(0);
        brandDO.setIsBuAccounts(0);
        brandDO.setIsShowCash(0);
        brandDO.setIsMultiHandover(0);
        final List<BrandDO> brandDOList = Arrays.asList(brandDO);
        when(mockBrandMapstruct.brandDOList2DTOList(brandDOList)).thenReturn(Collections.emptyList());

        // Run the test
        final List<BrandDTO> result = brandServiceImplUnderTest.queryBrandByIdList(Arrays.asList("value"));

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testQueryAllList() {
        // Setup
        final QueryBrandDTO queryBrandDTO = new QueryBrandDTO("brandGuid", "brandName");

        // Configure BrandMapstruct.brandDOList2DTOList(...).
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("brandGuid");
        brandDTO.setName("name");
        brandDTO.setMchntTypeCode("mchntTypeCode");
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        brandDTO.setStoreList(Arrays.asList(storeDTO));
        brandDTO.setSalesModel(0);
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        final List<BrandDTO> brandDTOS = Arrays.asList(brandDTO);
        final BrandDO brandDO = new BrandDO();
        brandDO.setGuid("brandGuid");
        brandDO.setName("我的品牌");
        brandDO.setDescription("默认品牌");
        brandDO.setLogoUrl("logoUrl");
        brandDO.setIsEnable(false);
        brandDO.setIsDeleted(false);
        brandDO.setCreateUserGuid("未定义");
        brandDO.setModifiedUserGuid("未定义");
        brandDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        brandDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        brandDO.setSalesModel(0);
        brandDO.setIsBuAccounts(0);
        brandDO.setIsShowCash(0);
        brandDO.setIsMultiHandover(0);
        final List<BrandDO> brandDOList = Arrays.asList(brandDO);
        when(mockBrandMapstruct.brandDOList2DTOList(brandDOList)).thenReturn(brandDTOS);

        // Configure StoreBrandMapper.selectList(...).
        final StoreBrandDO storeBrandDO = new StoreBrandDO();
        storeBrandDO.setGuid("f8aba60c-935f-4b39-9a17-37c45bb54a04");
        storeBrandDO.setStoreGuid("guid");
        storeBrandDO.setBrandGuid("brandGuid");
        storeBrandDO.setCreateUserGuid("createUserGuid");
        storeBrandDO.setModifiedUserGuid("modifiedUserGuid");
        storeBrandDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeBrandDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<StoreBrandDO> storeBrandDOS = Arrays.asList(storeBrandDO);
        when(mockStoreBrandMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(storeBrandDOS);

        // Configure OrganizationMapper.selectList(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setId(0L);
        organizationDO.setGuid("9853abda-f5ea-4599-ad7c-e6ccbbb71c38");
        organizationDO.setType(0);
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<OrganizationDO> organizationDOS = Arrays.asList(organizationDO);
        when(mockOrganizationMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(organizationDOS);

        // Configure StoreMapstruct.organizationList2DTOList(...).
        final StoreDTO storeDTO1 = new StoreDTO();
        storeDTO1.setGuid("guid");
        storeDTO1.setCreateUserGuid("createUserGuid");
        storeDTO1.setModifiedUserGuid("modifiedUserGuid");
        storeDTO1.setIsBuAccounts(0);
        storeDTO1.setIsShowCash(0);
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO1);
        final OrganizationDO organizationDO1 = new OrganizationDO();
        organizationDO1.setId(0L);
        organizationDO1.setGuid("9853abda-f5ea-4599-ad7c-e6ccbbb71c38");
        organizationDO1.setType(0);
        organizationDO1.setModifiedUserGuid("modifiedUserGuid");
        organizationDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<OrganizationDO> organizationDOList = Arrays.asList(organizationDO1);
        when(mockStoreMapstruct.organizationList2DTOList(organizationDOList)).thenReturn(storeDTOS);

        // Run the test
        final List<BrandDTO> result = brandServiceImplUnderTest.queryAllList(queryBrandDTO);

        // Verify the results
    }

    @Test
    public void testQueryAllList_BrandMapstructReturnsNoItems() {
        // Setup
        final QueryBrandDTO queryBrandDTO = new QueryBrandDTO("brandGuid", "brandName");

        // Configure BrandMapstruct.brandDOList2DTOList(...).
        final BrandDO brandDO = new BrandDO();
        brandDO.setGuid("brandGuid");
        brandDO.setName("我的品牌");
        brandDO.setDescription("默认品牌");
        brandDO.setLogoUrl("logoUrl");
        brandDO.setIsEnable(false);
        brandDO.setIsDeleted(false);
        brandDO.setCreateUserGuid("未定义");
        brandDO.setModifiedUserGuid("未定义");
        brandDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        brandDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        brandDO.setSalesModel(0);
        brandDO.setIsBuAccounts(0);
        brandDO.setIsShowCash(0);
        brandDO.setIsMultiHandover(0);
        final List<BrandDO> brandDOList = Arrays.asList(brandDO);
        when(mockBrandMapstruct.brandDOList2DTOList(brandDOList)).thenReturn(Collections.emptyList());

        // Run the test
        final List<BrandDTO> result = brandServiceImplUnderTest.queryAllList(queryBrandDTO);

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testQueryAllList_StoreBrandMapperReturnsNoItems() {
        // Setup
        final QueryBrandDTO queryBrandDTO = new QueryBrandDTO("brandGuid", "brandName");

        // Configure BrandMapstruct.brandDOList2DTOList(...).
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("brandGuid");
        brandDTO.setName("name");
        brandDTO.setMchntTypeCode("mchntTypeCode");
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        brandDTO.setStoreList(Arrays.asList(storeDTO));
        brandDTO.setSalesModel(0);
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        final List<BrandDTO> brandDTOS = Arrays.asList(brandDTO);
        final BrandDO brandDO = new BrandDO();
        brandDO.setGuid("brandGuid");
        brandDO.setName("我的品牌");
        brandDO.setDescription("默认品牌");
        brandDO.setLogoUrl("logoUrl");
        brandDO.setIsEnable(false);
        brandDO.setIsDeleted(false);
        brandDO.setCreateUserGuid("未定义");
        brandDO.setModifiedUserGuid("未定义");
        brandDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        brandDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        brandDO.setSalesModel(0);
        brandDO.setIsBuAccounts(0);
        brandDO.setIsShowCash(0);
        brandDO.setIsMultiHandover(0);
        final List<BrandDO> brandDOList = Arrays.asList(brandDO);
        when(mockBrandMapstruct.brandDOList2DTOList(brandDOList)).thenReturn(brandDTOS);

        when(mockStoreBrandMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure StoreMapstruct.organizationList2DTOList(...).
        final StoreDTO storeDTO1 = new StoreDTO();
        storeDTO1.setGuid("guid");
        storeDTO1.setCreateUserGuid("createUserGuid");
        storeDTO1.setModifiedUserGuid("modifiedUserGuid");
        storeDTO1.setIsBuAccounts(0);
        storeDTO1.setIsShowCash(0);
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO1);
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setId(0L);
        organizationDO.setGuid("9853abda-f5ea-4599-ad7c-e6ccbbb71c38");
        organizationDO.setType(0);
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<OrganizationDO> organizationDOList = Arrays.asList(organizationDO);
        when(mockStoreMapstruct.organizationList2DTOList(organizationDOList)).thenReturn(storeDTOS);

        // Run the test
        final List<BrandDTO> result = brandServiceImplUnderTest.queryAllList(queryBrandDTO);

        // Verify the results
    }

    @Test
    public void testQueryAllList_OrganizationMapperReturnsNoItems() {
        // Setup
        final QueryBrandDTO queryBrandDTO = new QueryBrandDTO("brandGuid", "brandName");

        // Configure BrandMapstruct.brandDOList2DTOList(...).
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("brandGuid");
        brandDTO.setName("name");
        brandDTO.setMchntTypeCode("mchntTypeCode");
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        brandDTO.setStoreList(Arrays.asList(storeDTO));
        brandDTO.setSalesModel(0);
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        final List<BrandDTO> brandDTOS = Arrays.asList(brandDTO);
        final BrandDO brandDO = new BrandDO();
        brandDO.setGuid("brandGuid");
        brandDO.setName("我的品牌");
        brandDO.setDescription("默认品牌");
        brandDO.setLogoUrl("logoUrl");
        brandDO.setIsEnable(false);
        brandDO.setIsDeleted(false);
        brandDO.setCreateUserGuid("未定义");
        brandDO.setModifiedUserGuid("未定义");
        brandDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        brandDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        brandDO.setSalesModel(0);
        brandDO.setIsBuAccounts(0);
        brandDO.setIsShowCash(0);
        brandDO.setIsMultiHandover(0);
        final List<BrandDO> brandDOList = Arrays.asList(brandDO);
        when(mockBrandMapstruct.brandDOList2DTOList(brandDOList)).thenReturn(brandDTOS);

        // Configure StoreBrandMapper.selectList(...).
        final StoreBrandDO storeBrandDO = new StoreBrandDO();
        storeBrandDO.setGuid("f8aba60c-935f-4b39-9a17-37c45bb54a04");
        storeBrandDO.setStoreGuid("guid");
        storeBrandDO.setBrandGuid("brandGuid");
        storeBrandDO.setCreateUserGuid("createUserGuid");
        storeBrandDO.setModifiedUserGuid("modifiedUserGuid");
        storeBrandDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeBrandDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<StoreBrandDO> storeBrandDOS = Arrays.asList(storeBrandDO);
        when(mockStoreBrandMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(storeBrandDOS);

        when(mockOrganizationMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure StoreMapstruct.organizationList2DTOList(...).
        final StoreDTO storeDTO1 = new StoreDTO();
        storeDTO1.setGuid("guid");
        storeDTO1.setCreateUserGuid("createUserGuid");
        storeDTO1.setModifiedUserGuid("modifiedUserGuid");
        storeDTO1.setIsBuAccounts(0);
        storeDTO1.setIsShowCash(0);
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO1);
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setId(0L);
        organizationDO.setGuid("9853abda-f5ea-4599-ad7c-e6ccbbb71c38");
        organizationDO.setType(0);
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<OrganizationDO> organizationDOList = Arrays.asList(organizationDO);
        when(mockStoreMapstruct.organizationList2DTOList(organizationDOList)).thenReturn(storeDTOS);

        // Run the test
        final List<BrandDTO> result = brandServiceImplUnderTest.queryAllList(queryBrandDTO);

        // Verify the results
    }

    @Test
    public void testQueryAllList_StoreMapstructReturnsNoItems() {
        // Setup
        final QueryBrandDTO queryBrandDTO = new QueryBrandDTO("brandGuid", "brandName");

        // Configure BrandMapstruct.brandDOList2DTOList(...).
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("brandGuid");
        brandDTO.setName("name");
        brandDTO.setMchntTypeCode("mchntTypeCode");
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        brandDTO.setStoreList(Arrays.asList(storeDTO));
        brandDTO.setSalesModel(0);
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        final List<BrandDTO> brandDTOS = Arrays.asList(brandDTO);
        final BrandDO brandDO = new BrandDO();
        brandDO.setGuid("brandGuid");
        brandDO.setName("我的品牌");
        brandDO.setDescription("默认品牌");
        brandDO.setLogoUrl("logoUrl");
        brandDO.setIsEnable(false);
        brandDO.setIsDeleted(false);
        brandDO.setCreateUserGuid("未定义");
        brandDO.setModifiedUserGuid("未定义");
        brandDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        brandDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        brandDO.setSalesModel(0);
        brandDO.setIsBuAccounts(0);
        brandDO.setIsShowCash(0);
        brandDO.setIsMultiHandover(0);
        final List<BrandDO> brandDOList = Arrays.asList(brandDO);
        when(mockBrandMapstruct.brandDOList2DTOList(brandDOList)).thenReturn(brandDTOS);

        // Configure StoreBrandMapper.selectList(...).
        final StoreBrandDO storeBrandDO = new StoreBrandDO();
        storeBrandDO.setGuid("f8aba60c-935f-4b39-9a17-37c45bb54a04");
        storeBrandDO.setStoreGuid("guid");
        storeBrandDO.setBrandGuid("brandGuid");
        storeBrandDO.setCreateUserGuid("createUserGuid");
        storeBrandDO.setModifiedUserGuid("modifiedUserGuid");
        storeBrandDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeBrandDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<StoreBrandDO> storeBrandDOS = Arrays.asList(storeBrandDO);
        when(mockStoreBrandMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(storeBrandDOS);

        // Configure OrganizationMapper.selectList(...).
        final OrganizationDO organizationDO = new OrganizationDO();
        organizationDO.setId(0L);
        organizationDO.setGuid("9853abda-f5ea-4599-ad7c-e6ccbbb71c38");
        organizationDO.setType(0);
        organizationDO.setModifiedUserGuid("modifiedUserGuid");
        organizationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<OrganizationDO> organizationDOS = Arrays.asList(organizationDO);
        when(mockOrganizationMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(organizationDOS);

        // Configure StoreMapstruct.organizationList2DTOList(...).
        final OrganizationDO organizationDO1 = new OrganizationDO();
        organizationDO1.setId(0L);
        organizationDO1.setGuid("9853abda-f5ea-4599-ad7c-e6ccbbb71c38");
        organizationDO1.setType(0);
        organizationDO1.setModifiedUserGuid("modifiedUserGuid");
        organizationDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<OrganizationDO> organizationDOList = Arrays.asList(organizationDO1);
        when(mockStoreMapstruct.organizationList2DTOList(organizationDOList)).thenReturn(Collections.emptyList());

        // Run the test
        final List<BrandDTO> result = brandServiceImplUnderTest.queryAllList(queryBrandDTO);

        // Verify the results
    }

    @Test
    public void testQueryExistStoreAccount() {
        // Setup
        when(mockStoreBrandMapper.selectCount(any(Wrapper.class))).thenReturn(0);
        when(mockItemClient.countTypeOrItem("brandGuid")).thenReturn(false);

        // Run the test
        final boolean result = brandServiceImplUnderTest.queryExistStoreAccount("brandGuid");

        // Verify the results
        assertFalse(result);
    }

    @Test(expected = BusinessException.class)
    public void testQueryExistStoreAccount_ItemClientReturnsTrue() {
        // Setup
        when(mockStoreBrandMapper.selectCount(any(Wrapper.class))).thenReturn(0);
        when(mockItemClient.countTypeOrItem("brandGuid")).thenReturn(true);

        // Run the test
        brandServiceImplUnderTest.queryExistStoreAccount("brandGuid");
    }

    @Test
    public void testQueryStoreGuidListByBrandGuid() {
        // Setup
        // Configure StoreBrandMapper.selectList(...).
        final StoreBrandDO storeBrandDO = new StoreBrandDO();
        storeBrandDO.setGuid("f8aba60c-935f-4b39-9a17-37c45bb54a04");
        storeBrandDO.setStoreGuid("guid");
        storeBrandDO.setBrandGuid("brandGuid");
        storeBrandDO.setCreateUserGuid("createUserGuid");
        storeBrandDO.setModifiedUserGuid("modifiedUserGuid");
        storeBrandDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeBrandDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<StoreBrandDO> storeBrandDOS = Arrays.asList(storeBrandDO);
        when(mockStoreBrandMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(storeBrandDOS);

        // Run the test
        final List<String> result = brandServiceImplUnderTest.queryStoreGuidListByBrandGuid("brandGuid");

        // Verify the results
        assertEquals(Arrays.asList("value"), result);
    }

    @Test
    public void testQueryStoreGuidListByBrandGuid_StoreBrandMapperReturnsNoItems() {
        // Setup
        when(mockStoreBrandMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<String> result = brandServiceImplUnderTest.queryStoreGuidListByBrandGuid("brandGuid");

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testCreateDefaultBrand() {
        // Setup
        when(mockDistributedIdService.nextBrandGuid()).thenReturn("brandGuid");

        // Configure BrandMapstruct.brandDO2DTO(...).
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("brandGuid");
        brandDTO.setName("name");
        brandDTO.setMchntTypeCode("mchntTypeCode");
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        brandDTO.setStoreList(Arrays.asList(storeDTO));
        brandDTO.setSalesModel(0);
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        final BrandDO brandDO = new BrandDO();
        brandDO.setGuid("brandGuid");
        brandDO.setName("我的品牌");
        brandDO.setDescription("默认品牌");
        brandDO.setLogoUrl("logoUrl");
        brandDO.setIsEnable(false);
        brandDO.setIsDeleted(false);
        brandDO.setCreateUserGuid("未定义");
        brandDO.setModifiedUserGuid("未定义");
        brandDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        brandDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        brandDO.setSalesModel(0);
        brandDO.setIsBuAccounts(0);
        brandDO.setIsShowCash(0);
        brandDO.setIsMultiHandover(0);
        when(mockBrandMapstruct.brandDO2DTO(brandDO)).thenReturn(brandDTO);

        when(mockUserClient.queryMchntType()).thenReturn("mchntTypeCode");

        // Run the test
        final BrandDTO result = brandServiceImplUnderTest.createDefaultBrand();

        // Verify the results
        verify(mockDefaultRocketMqProducer).sendMessage(any(Message.class));
    }

    @Test
    public void testQueryDefaultBrand() {
        // Setup
        when(mockDistributedIdService.nextBrandGuid()).thenReturn("brandGuid");

        // Configure BrandMapstruct.brandDO2DTO(...).
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("brandGuid");
        brandDTO.setName("name");
        brandDTO.setMchntTypeCode("mchntTypeCode");
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        brandDTO.setStoreList(Arrays.asList(storeDTO));
        brandDTO.setSalesModel(0);
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);
        final BrandDO brandDO = new BrandDO();
        brandDO.setGuid("brandGuid");
        brandDO.setName("我的品牌");
        brandDO.setDescription("默认品牌");
        brandDO.setLogoUrl("logoUrl");
        brandDO.setIsEnable(false);
        brandDO.setIsDeleted(false);
        brandDO.setCreateUserGuid("未定义");
        brandDO.setModifiedUserGuid("未定义");
        brandDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        brandDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        brandDO.setSalesModel(0);
        brandDO.setIsBuAccounts(0);
        brandDO.setIsShowCash(0);
        brandDO.setIsMultiHandover(0);
        when(mockBrandMapstruct.brandDO2DTO(brandDO)).thenReturn(brandDTO);

        when(mockUserClient.queryMchntType()).thenReturn("mchntTypeCode");

        // Run the test
        final BrandDTO result = brandServiceImplUnderTest.queryDefaultBrand();

        // Verify the results
        assertEquals("brandGuid", result);
        verify(mockDefaultRocketMqProducer).sendMessage(any(Message.class));
    }

    @Test
    public void testUpdateSalesModel() {
        // Setup
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("brandGuid");
        brandDTO.setName("name");
        brandDTO.setMchntTypeCode("mchntTypeCode");
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        brandDTO.setStoreList(Arrays.asList(storeDTO));
        brandDTO.setSalesModel(0);
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);

        // Run the test
        final Boolean result = brandServiceImplUnderTest.updateSalesModel(brandDTO);

        // Verify the results
        assertFalse(result);
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
        verify(mockItemClient).changeSaleModel("brandGuid", 0);
    }

    @Test
    public void testUpdateBrandAccountStatus() {
        // Setup
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("brandGuid");
        brandDTO.setName("name");
        brandDTO.setMchntTypeCode("mchntTypeCode");
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCreateUserGuid("createUserGuid");
        storeDTO.setModifiedUserGuid("modifiedUserGuid");
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        brandDTO.setStoreList(Arrays.asList(storeDTO));
        brandDTO.setSalesModel(0);
        brandDTO.setIsBuAccounts(0);
        brandDTO.setIsShowCash(0);
        brandDTO.setIsMultiHandover(0);

        when(mockBrandMapper.update(any(BrandDO.class), any(LambdaUpdateWrapper.class))).thenReturn(0);

        // Run the test
        final Boolean result = brandServiceImplUnderTest.updateBrandAccountStatus(brandDTO);

        // Verify the results
        assertFalse(result);
    }
}
