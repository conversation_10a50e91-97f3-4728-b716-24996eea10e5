package com.holderzone.saas.store.organization.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.resource.common.dto.device.DeviceDTO;
import com.holderzone.resource.common.dto.device.DeviceStoreDTO;
import com.holderzone.saas.store.dto.organization.PadOrderTypeReqDTO;
import com.holderzone.saas.store.dto.organization.PadOrderTypeRespDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.print.PrinterDTO;
import com.holderzone.saas.store.dto.table.PadAreaDTO;
import com.holderzone.saas.store.dto.table.TableBasicDTO;
import com.holderzone.saas.store.dto.table.TableInfoDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceQueryDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceSortDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceUnbindDTO;
import com.holderzone.saas.store.dto.weixin.MultiMemberDTO;
import com.holderzone.saas.store.organization.domain.StoreDeviceDO;
import com.holderzone.saas.store.organization.feign.EnterpriseClientService;
import com.holderzone.saas.store.organization.feign.TableService;
import com.holderzone.saas.store.organization.mapper.StoreDeviceMapper;
import com.holderzone.saas.store.organization.mapstruct.StoreDeviceMapStruct;
import com.holderzone.saas.store.organization.service.BroadcastService;
import com.holderzone.saas.store.organization.service.PushService;
import com.holderzone.saas.store.organization.service.RedisService;
import com.holderzone.saas.store.organization.service.StoreService;
import com.holderzone.saas.store.organization.service.impl.StoreDeviceServiceImpl;
import com.holderzone.saas.store.organization.service.remote.DeviceClient;
import com.holderzone.saas.store.organization.service.remote.PrintClient;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.RedisTemplate;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class StoreDeviceServiceImplTest {

    @Mock
    private StoreDeviceMapper mockStoreDeviceMapper;
    @Mock
    private PrintClient mockPrintClient;
    @Mock
    private StoreDeviceMapStruct mockStoreDeviceMapStruct;
    @Mock
    private DeviceClient mockDeviceClient;
    @Mock
    private StoreService mockStoreService;
    @Mock
    private RedisTemplate mockRedisTemplate;
    @Mock
    private BroadcastService mockBroadcastService;
    @Mock
    private RedisService mockRedisService;
    @Mock
    private PushService mockPushService;
    @Mock
    private TableService mockTableService;
    @Mock
    private EnterpriseClientService mockEnterpriseClientService;

    private StoreDeviceServiceImpl storeDeviceServiceImplUnderTest;

    @Before
    public void setUp() {
        storeDeviceServiceImplUnderTest = new StoreDeviceServiceImpl(mockStoreDeviceMapper, mockPrintClient,
                mockStoreDeviceMapStruct, mockDeviceClient, mockStoreService, mockRedisTemplate, mockBroadcastService,
                mockRedisService, mockPushService, mockTableService, mockEnterpriseClientService);
    }

    @Test
    public void testCreate() {
        // Setup
        final StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO();
        storeDeviceDTO.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO.setStoreNo("code");
        storeDeviceDTO.setStoreGuid("storeGuid");
        storeDeviceDTO.setStoreName("name");
        storeDeviceDTO.setDeviceNo("deviceNo");
        storeDeviceDTO.setDeviceGuid("deviceGuid");
        storeDeviceDTO.setBinding(false);
        storeDeviceDTO.setRegister(false);

        // Configure StoreDeviceMapper.selectList(...).
        final StoreDeviceDO storeDeviceDO = new StoreDeviceDO();
        storeDeviceDO.setId(0L);
        storeDeviceDO.setGuid("f39752b9-5a7f-4284-8bd9-9837cf5e494d");
        storeDeviceDO.setStoreGuid("storeGuid");
        storeDeviceDO.setDeviceNo("deviceNo");
        storeDeviceDO.setDeviceGuid("deviceGuid");
        storeDeviceDO.setIsBinding(false);
        storeDeviceDO.setDeviceType(0);
        storeDeviceDO.setSort(0);
        storeDeviceDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setGmtUnbind(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setTableGuid("tableGuid");
        storeDeviceDO.setPadOrderType(0);
        final List<StoreDeviceDO> storeDeviceDOS = Arrays.asList(storeDeviceDO);
        when(mockStoreDeviceMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(storeDeviceDOS);

        // Configure StoreDeviceMapper.insert(...).
        final StoreDeviceDO entity = new StoreDeviceDO();
        entity.setId(0L);
        entity.setGuid("f39752b9-5a7f-4284-8bd9-9837cf5e494d");
        entity.setStoreGuid("storeGuid");
        entity.setDeviceNo("deviceNo");
        entity.setDeviceGuid("deviceGuid");
        entity.setIsBinding(false);
        entity.setDeviceType(0);
        entity.setSort(0);
        entity.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setGmtUnbind(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setTableGuid("tableGuid");
        entity.setPadOrderType(0);
        when(mockStoreDeviceMapper.insert(entity)).thenReturn(0);

        // Run the test
        final boolean result = storeDeviceServiceImplUnderTest.create(storeDeviceDTO);

        // Verify the results
        assertFalse(result);
        verify(mockBroadcastService).bindDeviceToCloud(any(DeviceDTO.class));

        // Confirm BroadcastService.deviceBind(...).
        final StoreDeviceDTO storeDeviceDTO1 = new StoreDeviceDTO();
        storeDeviceDTO1.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO1.setStoreNo("code");
        storeDeviceDTO1.setStoreGuid("storeGuid");
        storeDeviceDTO1.setStoreName("name");
        storeDeviceDTO1.setDeviceNo("deviceNo");
        storeDeviceDTO1.setDeviceGuid("deviceGuid");
        storeDeviceDTO1.setBinding(false);
        storeDeviceDTO1.setRegister(false);
        verify(mockBroadcastService).deviceBind(storeDeviceDTO1);
        verify(mockRedisService).removeStoreMaster("storeGuid");
        verify(mockStoreDeviceMapper).delete(any(LambdaQueryWrapper.class));
    }

    @Test
    public void testCreate_StoreDeviceMapperSelectListReturnsNoItems() {
        // Setup
        final StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO();
        storeDeviceDTO.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO.setStoreNo("code");
        storeDeviceDTO.setStoreGuid("storeGuid");
        storeDeviceDTO.setStoreName("name");
        storeDeviceDTO.setDeviceNo("deviceNo");
        storeDeviceDTO.setDeviceGuid("deviceGuid");
        storeDeviceDTO.setBinding(false);
        storeDeviceDTO.setRegister(false);

        when(mockStoreDeviceMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure StoreDeviceMapStruct.toStoreDeviceDO(...).
        final StoreDeviceDO storeDeviceDO = new StoreDeviceDO();
        storeDeviceDO.setId(0L);
        storeDeviceDO.setGuid("f39752b9-5a7f-4284-8bd9-9837cf5e494d");
        storeDeviceDO.setStoreGuid("storeGuid");
        storeDeviceDO.setDeviceNo("deviceNo");
        storeDeviceDO.setDeviceGuid("deviceGuid");
        storeDeviceDO.setIsBinding(false);
        storeDeviceDO.setDeviceType(0);
        storeDeviceDO.setSort(0);
        storeDeviceDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setGmtUnbind(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setTableGuid("tableGuid");
        storeDeviceDO.setPadOrderType(0);
        final StoreDeviceDTO storeDeviceDTO1 = new StoreDeviceDTO();
        storeDeviceDTO1.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO1.setStoreNo("code");
        storeDeviceDTO1.setStoreGuid("storeGuid");
        storeDeviceDTO1.setStoreName("name");
        storeDeviceDTO1.setDeviceNo("deviceNo");
        storeDeviceDTO1.setDeviceGuid("deviceGuid");
        storeDeviceDTO1.setBinding(false);
        storeDeviceDTO1.setRegister(false);
        when(mockStoreDeviceMapStruct.toStoreDeviceDO(storeDeviceDTO1)).thenReturn(storeDeviceDO);

        // Configure StoreDeviceMapper.insert(...).
        final StoreDeviceDO entity = new StoreDeviceDO();
        entity.setId(0L);
        entity.setGuid("f39752b9-5a7f-4284-8bd9-9837cf5e494d");
        entity.setStoreGuid("storeGuid");
        entity.setDeviceNo("deviceNo");
        entity.setDeviceGuid("deviceGuid");
        entity.setIsBinding(false);
        entity.setDeviceType(0);
        entity.setSort(0);
        entity.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setGmtUnbind(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setTableGuid("tableGuid");
        entity.setPadOrderType(0);
        when(mockStoreDeviceMapper.insert(entity)).thenReturn(0);

        // Run the test
        final boolean result = storeDeviceServiceImplUnderTest.create(storeDeviceDTO);

        // Verify the results
        assertFalse(result);
        verify(mockBroadcastService).bindDeviceToCloud(any(DeviceDTO.class));

        // Confirm BroadcastService.deviceBind(...).
        final StoreDeviceDTO storeDeviceDTO2 = new StoreDeviceDTO();
        storeDeviceDTO2.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO2.setStoreNo("code");
        storeDeviceDTO2.setStoreGuid("storeGuid");
        storeDeviceDTO2.setStoreName("name");
        storeDeviceDTO2.setDeviceNo("deviceNo");
        storeDeviceDTO2.setDeviceGuid("deviceGuid");
        storeDeviceDTO2.setBinding(false);
        storeDeviceDTO2.setRegister(false);
        verify(mockBroadcastService).deviceBind(storeDeviceDTO2);
        verify(mockRedisService).removeStoreMaster("storeGuid");
    }

    @Test
    public void testFindStoreDevice() {
        // Setup
        final StoreDeviceQueryDTO storeDeviceQueryDTO = new StoreDeviceQueryDTO("storeGuid", 0);
        final StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO();
        storeDeviceDTO.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO.setStoreNo("code");
        storeDeviceDTO.setStoreGuid("storeGuid");
        storeDeviceDTO.setStoreName("name");
        storeDeviceDTO.setDeviceNo("deviceNo");
        storeDeviceDTO.setDeviceGuid("deviceGuid");
        storeDeviceDTO.setBinding(false);
        storeDeviceDTO.setRegister(false);
        final List<StoreDeviceDTO> expectedResult = Arrays.asList(storeDeviceDTO);

        // Configure StoreDeviceMapper.selectList(...).
        final StoreDeviceDO storeDeviceDO = new StoreDeviceDO();
        storeDeviceDO.setId(0L);
        storeDeviceDO.setGuid("f39752b9-5a7f-4284-8bd9-9837cf5e494d");
        storeDeviceDO.setStoreGuid("storeGuid");
        storeDeviceDO.setDeviceNo("deviceNo");
        storeDeviceDO.setDeviceGuid("deviceGuid");
        storeDeviceDO.setIsBinding(false);
        storeDeviceDO.setDeviceType(0);
        storeDeviceDO.setSort(0);
        storeDeviceDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setGmtUnbind(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setTableGuid("tableGuid");
        storeDeviceDO.setPadOrderType(0);
        final List<StoreDeviceDO> storeDeviceDOS = Arrays.asList(storeDeviceDO);
        when(mockStoreDeviceMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(storeDeviceDOS);

        // Configure StoreDeviceMapStruct.toStoreDeviceDTOList(...).
        final StoreDeviceDTO storeDeviceDTO1 = new StoreDeviceDTO();
        storeDeviceDTO1.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO1.setStoreNo("code");
        storeDeviceDTO1.setStoreGuid("storeGuid");
        storeDeviceDTO1.setStoreName("name");
        storeDeviceDTO1.setDeviceNo("deviceNo");
        storeDeviceDTO1.setDeviceGuid("deviceGuid");
        storeDeviceDTO1.setBinding(false);
        storeDeviceDTO1.setRegister(false);
        final List<StoreDeviceDTO> storeDeviceDTOS = Arrays.asList(storeDeviceDTO1);
        final StoreDeviceDO storeDeviceDO1 = new StoreDeviceDO();
        storeDeviceDO1.setId(0L);
        storeDeviceDO1.setGuid("f39752b9-5a7f-4284-8bd9-9837cf5e494d");
        storeDeviceDO1.setStoreGuid("storeGuid");
        storeDeviceDO1.setDeviceNo("deviceNo");
        storeDeviceDO1.setDeviceGuid("deviceGuid");
        storeDeviceDO1.setIsBinding(false);
        storeDeviceDO1.setDeviceType(0);
        storeDeviceDO1.setSort(0);
        storeDeviceDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO1.setGmtUnbind(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO1.setTableGuid("tableGuid");
        storeDeviceDO1.setPadOrderType(0);
        final List<StoreDeviceDO> storeDeviceDOList = Arrays.asList(storeDeviceDO1);
        when(mockStoreDeviceMapStruct.toStoreDeviceDTOList(storeDeviceDOList)).thenReturn(storeDeviceDTOS);

        // Run the test
        final List<StoreDeviceDTO> result = storeDeviceServiceImplUnderTest.findStoreDevice(storeDeviceQueryDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testFindStoreDevice_StoreDeviceMapperReturnsNoItems() {
        // Setup
        final StoreDeviceQueryDTO storeDeviceQueryDTO = new StoreDeviceQueryDTO("storeGuid", 0);
        final StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO();
        storeDeviceDTO.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO.setStoreNo("code");
        storeDeviceDTO.setStoreGuid("storeGuid");
        storeDeviceDTO.setStoreName("name");
        storeDeviceDTO.setDeviceNo("deviceNo");
        storeDeviceDTO.setDeviceGuid("deviceGuid");
        storeDeviceDTO.setBinding(false);
        storeDeviceDTO.setRegister(false);
        final List<StoreDeviceDTO> expectedResult = Arrays.asList(storeDeviceDTO);
        when(mockStoreDeviceMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure StoreDeviceMapStruct.toStoreDeviceDTOList(...).
        final StoreDeviceDTO storeDeviceDTO1 = new StoreDeviceDTO();
        storeDeviceDTO1.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO1.setStoreNo("code");
        storeDeviceDTO1.setStoreGuid("storeGuid");
        storeDeviceDTO1.setStoreName("name");
        storeDeviceDTO1.setDeviceNo("deviceNo");
        storeDeviceDTO1.setDeviceGuid("deviceGuid");
        storeDeviceDTO1.setBinding(false);
        storeDeviceDTO1.setRegister(false);
        final List<StoreDeviceDTO> storeDeviceDTOS = Arrays.asList(storeDeviceDTO1);
        final StoreDeviceDO storeDeviceDO = new StoreDeviceDO();
        storeDeviceDO.setId(0L);
        storeDeviceDO.setGuid("f39752b9-5a7f-4284-8bd9-9837cf5e494d");
        storeDeviceDO.setStoreGuid("storeGuid");
        storeDeviceDO.setDeviceNo("deviceNo");
        storeDeviceDO.setDeviceGuid("deviceGuid");
        storeDeviceDO.setIsBinding(false);
        storeDeviceDO.setDeviceType(0);
        storeDeviceDO.setSort(0);
        storeDeviceDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setGmtUnbind(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setTableGuid("tableGuid");
        storeDeviceDO.setPadOrderType(0);
        final List<StoreDeviceDO> storeDeviceDOList = Arrays.asList(storeDeviceDO);
        when(mockStoreDeviceMapStruct.toStoreDeviceDTOList(storeDeviceDOList)).thenReturn(storeDeviceDTOS);

        // Run the test
        final List<StoreDeviceDTO> result = storeDeviceServiceImplUnderTest.findStoreDevice(storeDeviceQueryDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testFindStoreDevice_StoreDeviceMapStructReturnsNoItems() {
        // Setup
        final StoreDeviceQueryDTO storeDeviceQueryDTO = new StoreDeviceQueryDTO("storeGuid", 0);

        // Configure StoreDeviceMapper.selectList(...).
        final StoreDeviceDO storeDeviceDO = new StoreDeviceDO();
        storeDeviceDO.setId(0L);
        storeDeviceDO.setGuid("f39752b9-5a7f-4284-8bd9-9837cf5e494d");
        storeDeviceDO.setStoreGuid("storeGuid");
        storeDeviceDO.setDeviceNo("deviceNo");
        storeDeviceDO.setDeviceGuid("deviceGuid");
        storeDeviceDO.setIsBinding(false);
        storeDeviceDO.setDeviceType(0);
        storeDeviceDO.setSort(0);
        storeDeviceDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setGmtUnbind(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setTableGuid("tableGuid");
        storeDeviceDO.setPadOrderType(0);
        final List<StoreDeviceDO> storeDeviceDOS = Arrays.asList(storeDeviceDO);
        when(mockStoreDeviceMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(storeDeviceDOS);

        // Configure StoreDeviceMapStruct.toStoreDeviceDTOList(...).
        final StoreDeviceDO storeDeviceDO1 = new StoreDeviceDO();
        storeDeviceDO1.setId(0L);
        storeDeviceDO1.setGuid("f39752b9-5a7f-4284-8bd9-9837cf5e494d");
        storeDeviceDO1.setStoreGuid("storeGuid");
        storeDeviceDO1.setDeviceNo("deviceNo");
        storeDeviceDO1.setDeviceGuid("deviceGuid");
        storeDeviceDO1.setIsBinding(false);
        storeDeviceDO1.setDeviceType(0);
        storeDeviceDO1.setSort(0);
        storeDeviceDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO1.setGmtUnbind(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO1.setTableGuid("tableGuid");
        storeDeviceDO1.setPadOrderType(0);
        final List<StoreDeviceDO> storeDeviceDOList = Arrays.asList(storeDeviceDO1);
        when(mockStoreDeviceMapStruct.toStoreDeviceDTOList(storeDeviceDOList)).thenReturn(Collections.emptyList());

        // Run the test
        final List<StoreDeviceDTO> result = storeDeviceServiceImplUnderTest.findStoreDevice(storeDeviceQueryDTO);

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testSort() {
        // Setup
        final List<StoreDeviceSortDTO> storeDeviceSortDTOS = Arrays.asList(
                new StoreDeviceSortDTO("storeGuid", "deviceNo", "deviceGuid", 0, 0));

        // Configure StoreDeviceMapStruct.toStoreDeviceDOS(...).
        final StoreDeviceDO storeDeviceDO = new StoreDeviceDO();
        storeDeviceDO.setId(0L);
        storeDeviceDO.setGuid("f39752b9-5a7f-4284-8bd9-9837cf5e494d");
        storeDeviceDO.setStoreGuid("storeGuid");
        storeDeviceDO.setDeviceNo("deviceNo");
        storeDeviceDO.setDeviceGuid("deviceGuid");
        storeDeviceDO.setIsBinding(false);
        storeDeviceDO.setDeviceType(0);
        storeDeviceDO.setSort(0);
        storeDeviceDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setGmtUnbind(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setTableGuid("tableGuid");
        storeDeviceDO.setPadOrderType(0);
        final List<StoreDeviceDO> storeDeviceDOS = Arrays.asList(storeDeviceDO);
        when(mockStoreDeviceMapStruct.toStoreDeviceDOS(
                Arrays.asList(new StoreDeviceSortDTO("storeGuid", "deviceNo", "deviceGuid", 0, 0))))
                .thenReturn(storeDeviceDOS);

        // Configure StoreDeviceMapper.batchSort(...).
        final StoreDeviceDO storeDeviceDO1 = new StoreDeviceDO();
        storeDeviceDO1.setId(0L);
        storeDeviceDO1.setGuid("f39752b9-5a7f-4284-8bd9-9837cf5e494d");
        storeDeviceDO1.setStoreGuid("storeGuid");
        storeDeviceDO1.setDeviceNo("deviceNo");
        storeDeviceDO1.setDeviceGuid("deviceGuid");
        storeDeviceDO1.setIsBinding(false);
        storeDeviceDO1.setDeviceType(0);
        storeDeviceDO1.setSort(0);
        storeDeviceDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO1.setGmtUnbind(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO1.setTableGuid("tableGuid");
        storeDeviceDO1.setPadOrderType(0);
        final List<StoreDeviceDO> list = Arrays.asList(storeDeviceDO1);
        when(mockStoreDeviceMapper.batchSort(list)).thenReturn(0L);

        // Configure RedisService.getStoreMaster(...).
        final StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO();
        storeDeviceDTO.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO.setStoreNo("code");
        storeDeviceDTO.setStoreGuid("storeGuid");
        storeDeviceDTO.setStoreName("name");
        storeDeviceDTO.setDeviceNo("deviceNo");
        storeDeviceDTO.setDeviceGuid("deviceGuid");
        storeDeviceDTO.setBinding(false);
        storeDeviceDTO.setRegister(false);
        when(mockRedisService.getStoreMaster("storeGuid")).thenReturn(storeDeviceDTO);

        // Run the test
        final boolean result = storeDeviceServiceImplUnderTest.sort(storeDeviceSortDTOS);

        // Verify the results
        assertFalse(result);

        // Confirm PrintClient.changeMasterDevice(...).
        final PrinterDTO printerDTO = new PrinterDTO();
        printerDTO.setStoreGuid("storeGuid");
        printerDTO.setStoreName("storeName");
        printerDTO.setDeviceId("deviceGuid");
        printerDTO.setPrinterGuid("printerGuid");
        printerDTO.setPrinterName("printerName");
        verify(mockPrintClient).changeMasterDevice(printerDTO);
        verify(mockRedisService).removeStoreMaster("storeGuid");
        verify(mockPushService).pushMasterChanged("storeGuid", "deviceGuid");
    }

    @Test
    public void testSort_StoreDeviceMapStructToStoreDeviceDOSReturnsNoItems() {
        // Setup
        final List<StoreDeviceSortDTO> storeDeviceSortDTOS = Arrays.asList(
                new StoreDeviceSortDTO("storeGuid", "deviceNo", "deviceGuid", 0, 0));
        when(mockStoreDeviceMapStruct.toStoreDeviceDOS(
                Arrays.asList(new StoreDeviceSortDTO("storeGuid", "deviceNo", "deviceGuid", 0, 0))))
                .thenReturn(Collections.emptyList());

        // Configure StoreDeviceMapper.batchSort(...).
        final StoreDeviceDO storeDeviceDO = new StoreDeviceDO();
        storeDeviceDO.setId(0L);
        storeDeviceDO.setGuid("f39752b9-5a7f-4284-8bd9-9837cf5e494d");
        storeDeviceDO.setStoreGuid("storeGuid");
        storeDeviceDO.setDeviceNo("deviceNo");
        storeDeviceDO.setDeviceGuid("deviceGuid");
        storeDeviceDO.setIsBinding(false);
        storeDeviceDO.setDeviceType(0);
        storeDeviceDO.setSort(0);
        storeDeviceDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setGmtUnbind(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setTableGuid("tableGuid");
        storeDeviceDO.setPadOrderType(0);
        final List<StoreDeviceDO> list = Arrays.asList(storeDeviceDO);
        when(mockStoreDeviceMapper.batchSort(list)).thenReturn(0L);

        // Configure RedisService.getStoreMaster(...).
        final StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO();
        storeDeviceDTO.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO.setStoreNo("code");
        storeDeviceDTO.setStoreGuid("storeGuid");
        storeDeviceDTO.setStoreName("name");
        storeDeviceDTO.setDeviceNo("deviceNo");
        storeDeviceDTO.setDeviceGuid("deviceGuid");
        storeDeviceDTO.setBinding(false);
        storeDeviceDTO.setRegister(false);
        when(mockRedisService.getStoreMaster("storeGuid")).thenReturn(storeDeviceDTO);

        // Run the test
        final boolean result = storeDeviceServiceImplUnderTest.sort(storeDeviceSortDTOS);

        // Verify the results
        assertFalse(result);

        // Confirm PrintClient.changeMasterDevice(...).
        final PrinterDTO printerDTO = new PrinterDTO();
        printerDTO.setStoreGuid("storeGuid");
        printerDTO.setStoreName("storeName");
        printerDTO.setDeviceId("deviceGuid");
        printerDTO.setPrinterGuid("printerGuid");
        printerDTO.setPrinterName("printerName");
        verify(mockPrintClient).changeMasterDevice(printerDTO);
        verify(mockRedisService).removeStoreMaster("storeGuid");
        verify(mockPushService).pushMasterChanged("storeGuid", "deviceGuid");
    }

    @Test
    public void testSort_RedisServiceGetStoreMasterReturnsNull() {
        // Setup
        final List<StoreDeviceSortDTO> storeDeviceSortDTOS = Arrays.asList(
                new StoreDeviceSortDTO("storeGuid", "deviceNo", "deviceGuid", 0, 0));

        // Configure StoreDeviceMapStruct.toStoreDeviceDOS(...).
        final StoreDeviceDO storeDeviceDO = new StoreDeviceDO();
        storeDeviceDO.setId(0L);
        storeDeviceDO.setGuid("f39752b9-5a7f-4284-8bd9-9837cf5e494d");
        storeDeviceDO.setStoreGuid("storeGuid");
        storeDeviceDO.setDeviceNo("deviceNo");
        storeDeviceDO.setDeviceGuid("deviceGuid");
        storeDeviceDO.setIsBinding(false);
        storeDeviceDO.setDeviceType(0);
        storeDeviceDO.setSort(0);
        storeDeviceDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setGmtUnbind(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setTableGuid("tableGuid");
        storeDeviceDO.setPadOrderType(0);
        final List<StoreDeviceDO> storeDeviceDOS = Arrays.asList(storeDeviceDO);
        when(mockStoreDeviceMapStruct.toStoreDeviceDOS(
                Arrays.asList(new StoreDeviceSortDTO("storeGuid", "deviceNo", "deviceGuid", 0, 0))))
                .thenReturn(storeDeviceDOS);

        // Configure StoreDeviceMapper.batchSort(...).
        final StoreDeviceDO storeDeviceDO1 = new StoreDeviceDO();
        storeDeviceDO1.setId(0L);
        storeDeviceDO1.setGuid("f39752b9-5a7f-4284-8bd9-9837cf5e494d");
        storeDeviceDO1.setStoreGuid("storeGuid");
        storeDeviceDO1.setDeviceNo("deviceNo");
        storeDeviceDO1.setDeviceGuid("deviceGuid");
        storeDeviceDO1.setIsBinding(false);
        storeDeviceDO1.setDeviceType(0);
        storeDeviceDO1.setSort(0);
        storeDeviceDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO1.setGmtUnbind(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO1.setTableGuid("tableGuid");
        storeDeviceDO1.setPadOrderType(0);
        final List<StoreDeviceDO> list = Arrays.asList(storeDeviceDO1);
        when(mockStoreDeviceMapper.batchSort(list)).thenReturn(0L);

        when(mockRedisService.getStoreMaster("storeGuid")).thenReturn(null);

        // Configure StoreDeviceMapper.selectOne(...).
        final StoreDeviceDO storeDeviceDO2 = new StoreDeviceDO();
        storeDeviceDO2.setId(0L);
        storeDeviceDO2.setGuid("f39752b9-5a7f-4284-8bd9-9837cf5e494d");
        storeDeviceDO2.setStoreGuid("storeGuid");
        storeDeviceDO2.setDeviceNo("deviceNo");
        storeDeviceDO2.setDeviceGuid("deviceGuid");
        storeDeviceDO2.setIsBinding(false);
        storeDeviceDO2.setDeviceType(0);
        storeDeviceDO2.setSort(0);
        storeDeviceDO2.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO2.setGmtUnbind(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO2.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO2.setTableGuid("tableGuid");
        storeDeviceDO2.setPadOrderType(0);
        when(mockStoreDeviceMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(storeDeviceDO2);

        // Configure StoreDeviceMapStruct.toStoreDeviceDTO(...).
        final StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO();
        storeDeviceDTO.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO.setStoreNo("code");
        storeDeviceDTO.setStoreGuid("storeGuid");
        storeDeviceDTO.setStoreName("name");
        storeDeviceDTO.setDeviceNo("deviceNo");
        storeDeviceDTO.setDeviceGuid("deviceGuid");
        storeDeviceDTO.setBinding(false);
        storeDeviceDTO.setRegister(false);
        final StoreDeviceDO storeDeviceDO3 = new StoreDeviceDO();
        storeDeviceDO3.setId(0L);
        storeDeviceDO3.setGuid("f39752b9-5a7f-4284-8bd9-9837cf5e494d");
        storeDeviceDO3.setStoreGuid("storeGuid");
        storeDeviceDO3.setDeviceNo("deviceNo");
        storeDeviceDO3.setDeviceGuid("deviceGuid");
        storeDeviceDO3.setIsBinding(false);
        storeDeviceDO3.setDeviceType(0);
        storeDeviceDO3.setSort(0);
        storeDeviceDO3.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO3.setGmtUnbind(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO3.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO3.setTableGuid("tableGuid");
        storeDeviceDO3.setPadOrderType(0);
        when(mockStoreDeviceMapStruct.toStoreDeviceDTO(storeDeviceDO3)).thenReturn(storeDeviceDTO);

        // Run the test
        final boolean result = storeDeviceServiceImplUnderTest.sort(storeDeviceSortDTOS);

        // Verify the results
        assertFalse(result);

        // Confirm PrintClient.changeMasterDevice(...).
        final PrinterDTO printerDTO = new PrinterDTO();
        printerDTO.setStoreGuid("storeGuid");
        printerDTO.setStoreName("storeName");
        printerDTO.setDeviceId("deviceGuid");
        printerDTO.setPrinterGuid("printerGuid");
        printerDTO.setPrinterName("printerName");
        verify(mockPrintClient).changeMasterDevice(printerDTO);
        verify(mockRedisService).removeStoreMaster("storeGuid");

        // Confirm RedisService.putStoreMaster(...).
        final StoreDeviceDTO storeDeviceDTO1 = new StoreDeviceDTO();
        storeDeviceDTO1.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO1.setStoreNo("code");
        storeDeviceDTO1.setStoreGuid("storeGuid");
        storeDeviceDTO1.setStoreName("name");
        storeDeviceDTO1.setDeviceNo("deviceNo");
        storeDeviceDTO1.setDeviceGuid("deviceGuid");
        storeDeviceDTO1.setBinding(false);
        storeDeviceDTO1.setRegister(false);
        verify(mockRedisService).putStoreMaster("storeGuid", storeDeviceDTO1);
        verify(mockPushService).pushMasterChanged("storeGuid", "deviceGuid");
    }

    @Test
    public void testSort_StoreDeviceMapperSelectOneReturnsNull() {
        // Setup
        final List<StoreDeviceSortDTO> storeDeviceSortDTOS = Arrays.asList(
                new StoreDeviceSortDTO("storeGuid", "deviceNo", "deviceGuid", 0, 0));

        // Configure StoreDeviceMapStruct.toStoreDeviceDOS(...).
        final StoreDeviceDO storeDeviceDO = new StoreDeviceDO();
        storeDeviceDO.setId(0L);
        storeDeviceDO.setGuid("f39752b9-5a7f-4284-8bd9-9837cf5e494d");
        storeDeviceDO.setStoreGuid("storeGuid");
        storeDeviceDO.setDeviceNo("deviceNo");
        storeDeviceDO.setDeviceGuid("deviceGuid");
        storeDeviceDO.setIsBinding(false);
        storeDeviceDO.setDeviceType(0);
        storeDeviceDO.setSort(0);
        storeDeviceDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setGmtUnbind(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setTableGuid("tableGuid");
        storeDeviceDO.setPadOrderType(0);
        final List<StoreDeviceDO> storeDeviceDOS = Arrays.asList(storeDeviceDO);
        when(mockStoreDeviceMapStruct.toStoreDeviceDOS(
                Arrays.asList(new StoreDeviceSortDTO("storeGuid", "deviceNo", "deviceGuid", 0, 0))))
                .thenReturn(storeDeviceDOS);

        // Configure StoreDeviceMapper.batchSort(...).
        final StoreDeviceDO storeDeviceDO1 = new StoreDeviceDO();
        storeDeviceDO1.setId(0L);
        storeDeviceDO1.setGuid("f39752b9-5a7f-4284-8bd9-9837cf5e494d");
        storeDeviceDO1.setStoreGuid("storeGuid");
        storeDeviceDO1.setDeviceNo("deviceNo");
        storeDeviceDO1.setDeviceGuid("deviceGuid");
        storeDeviceDO1.setIsBinding(false);
        storeDeviceDO1.setDeviceType(0);
        storeDeviceDO1.setSort(0);
        storeDeviceDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO1.setGmtUnbind(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO1.setTableGuid("tableGuid");
        storeDeviceDO1.setPadOrderType(0);
        final List<StoreDeviceDO> list = Arrays.asList(storeDeviceDO1);
        when(mockStoreDeviceMapper.batchSort(list)).thenReturn(0L);

        when(mockRedisService.getStoreMaster("storeGuid")).thenReturn(null);
        when(mockStoreDeviceMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Run the test
        final boolean result = storeDeviceServiceImplUnderTest.sort(storeDeviceSortDTOS);

        // Verify the results
        assertFalse(result);

        // Confirm PrintClient.changeMasterDevice(...).
        final PrinterDTO printerDTO = new PrinterDTO();
        printerDTO.setStoreGuid("storeGuid");
        printerDTO.setStoreName("storeName");
        printerDTO.setDeviceId("deviceGuid");
        printerDTO.setPrinterGuid("printerGuid");
        printerDTO.setPrinterName("printerName");
        verify(mockPrintClient).changeMasterDevice(printerDTO);
        verify(mockRedisService).removeStoreMaster("storeGuid");

        // Confirm RedisService.putStoreMaster(...).
        final StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO();
        storeDeviceDTO.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO.setStoreNo("code");
        storeDeviceDTO.setStoreGuid("storeGuid");
        storeDeviceDTO.setStoreName("name");
        storeDeviceDTO.setDeviceNo("deviceNo");
        storeDeviceDTO.setDeviceGuid("deviceGuid");
        storeDeviceDTO.setBinding(false);
        storeDeviceDTO.setRegister(false);
        verify(mockRedisService).putStoreMaster("storeGuid", storeDeviceDTO);
        verify(mockPushService).pushMasterChanged("storeGuid", "deviceGuid");
    }

    @Test
    public void testUnbind() {
        // Setup
        final StoreDeviceUnbindDTO storeDeviceUnbindDTO = new StoreDeviceUnbindDTO();
        storeDeviceUnbindDTO.setStoreGuid("storeGuid");
        storeDeviceUnbindDTO.setDeviceNo("deviceNo");
        storeDeviceUnbindDTO.setUnbindUserGuid("system");
        storeDeviceUnbindDTO.setUnbindUserName("system");
        storeDeviceUnbindDTO.setFromCloud(false);

        // Configure StoreDeviceMapStruct.toStoreDeviceDO(...).
        final StoreDeviceDO storeDeviceDO = new StoreDeviceDO();
        storeDeviceDO.setId(0L);
        storeDeviceDO.setGuid("f39752b9-5a7f-4284-8bd9-9837cf5e494d");
        storeDeviceDO.setStoreGuid("storeGuid");
        storeDeviceDO.setDeviceNo("deviceNo");
        storeDeviceDO.setDeviceGuid("deviceGuid");
        storeDeviceDO.setIsBinding(false);
        storeDeviceDO.setDeviceType(0);
        storeDeviceDO.setSort(0);
        storeDeviceDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setGmtUnbind(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setTableGuid("tableGuid");
        storeDeviceDO.setPadOrderType(0);
        when(mockStoreDeviceMapStruct.toStoreDeviceDO(any(StoreDeviceUnbindDTO.class))).thenReturn(storeDeviceDO);

        // Configure StoreDeviceMapper.selectList(...).
        final StoreDeviceDO storeDeviceDO1 = new StoreDeviceDO();
        storeDeviceDO1.setId(0L);
        storeDeviceDO1.setGuid("f39752b9-5a7f-4284-8bd9-9837cf5e494d");
        storeDeviceDO1.setStoreGuid("storeGuid");
        storeDeviceDO1.setDeviceNo("deviceNo");
        storeDeviceDO1.setDeviceGuid("deviceGuid");
        storeDeviceDO1.setIsBinding(false);
        storeDeviceDO1.setDeviceType(0);
        storeDeviceDO1.setSort(0);
        storeDeviceDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO1.setGmtUnbind(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO1.setTableGuid("tableGuid");
        storeDeviceDO1.setPadOrderType(0);
        final List<StoreDeviceDO> storeDeviceDOS = Arrays.asList(storeDeviceDO1);
        when(mockStoreDeviceMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(storeDeviceDOS);

        // Configure RedisService.getStoreMaster(...).
        final StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO();
        storeDeviceDTO.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO.setStoreNo("code");
        storeDeviceDTO.setStoreGuid("storeGuid");
        storeDeviceDTO.setStoreName("name");
        storeDeviceDTO.setDeviceNo("deviceNo");
        storeDeviceDTO.setDeviceGuid("deviceGuid");
        storeDeviceDTO.setBinding(false);
        storeDeviceDTO.setRegister(false);
        when(mockRedisService.getStoreMaster("storeGuid")).thenReturn(storeDeviceDTO);

        // Run the test
        final boolean result = storeDeviceServiceImplUnderTest.unbind(storeDeviceUnbindDTO);

        // Verify the results
        assertTrue(result);
        verify(mockBroadcastService).unBindDeviceToCloud(any(DeviceDTO.class));
        verify(mockPrintClient).deletePrinterByDevice("deviceGuid", "storeGuid", 0);

        // Confirm BroadcastService.deviceUnbind(...).
        final StoreDeviceDTO storeDeviceDTO1 = new StoreDeviceDTO();
        storeDeviceDTO1.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO1.setStoreNo("code");
        storeDeviceDTO1.setStoreGuid("storeGuid");
        storeDeviceDTO1.setStoreName("name");
        storeDeviceDTO1.setDeviceNo("deviceNo");
        storeDeviceDTO1.setDeviceGuid("deviceGuid");
        storeDeviceDTO1.setBinding(false);
        storeDeviceDTO1.setRegister(false);
        verify(mockBroadcastService).deviceUnbind(storeDeviceDTO1);
        verify(mockRedisService).removeStoreMaster("storeGuid");
        verify(mockPushService).pushMasterChanged("storeGuid", "deviceGuid");
    }

    @Test
    public void testUnbind_StoreDeviceMapperSelectListReturnsNoItems() {
        // Setup
        final StoreDeviceUnbindDTO storeDeviceUnbindDTO = new StoreDeviceUnbindDTO();
        storeDeviceUnbindDTO.setStoreGuid("storeGuid");
        storeDeviceUnbindDTO.setDeviceNo("deviceNo");
        storeDeviceUnbindDTO.setUnbindUserGuid("system");
        storeDeviceUnbindDTO.setUnbindUserName("system");
        storeDeviceUnbindDTO.setFromCloud(false);

        // Configure StoreDeviceMapStruct.toStoreDeviceDO(...).
        final StoreDeviceDO storeDeviceDO = new StoreDeviceDO();
        storeDeviceDO.setId(0L);
        storeDeviceDO.setGuid("f39752b9-5a7f-4284-8bd9-9837cf5e494d");
        storeDeviceDO.setStoreGuid("storeGuid");
        storeDeviceDO.setDeviceNo("deviceNo");
        storeDeviceDO.setDeviceGuid("deviceGuid");
        storeDeviceDO.setIsBinding(false);
        storeDeviceDO.setDeviceType(0);
        storeDeviceDO.setSort(0);
        storeDeviceDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setGmtUnbind(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setTableGuid("tableGuid");
        storeDeviceDO.setPadOrderType(0);
        when(mockStoreDeviceMapStruct.toStoreDeviceDO(any(StoreDeviceUnbindDTO.class))).thenReturn(storeDeviceDO);

        when(mockStoreDeviceMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final boolean result = storeDeviceServiceImplUnderTest.unbind(storeDeviceUnbindDTO);

        // Verify the results
        assertTrue(result);
    }

    @Test
    public void testUnbind_RedisServiceGetStoreMasterReturnsNull() {
        // Setup
        final StoreDeviceUnbindDTO storeDeviceUnbindDTO = new StoreDeviceUnbindDTO();
        storeDeviceUnbindDTO.setStoreGuid("storeGuid");
        storeDeviceUnbindDTO.setDeviceNo("deviceNo");
        storeDeviceUnbindDTO.setUnbindUserGuid("system");
        storeDeviceUnbindDTO.setUnbindUserName("system");
        storeDeviceUnbindDTO.setFromCloud(false);

        // Configure StoreDeviceMapStruct.toStoreDeviceDO(...).
        final StoreDeviceDO storeDeviceDO = new StoreDeviceDO();
        storeDeviceDO.setId(0L);
        storeDeviceDO.setGuid("f39752b9-5a7f-4284-8bd9-9837cf5e494d");
        storeDeviceDO.setStoreGuid("storeGuid");
        storeDeviceDO.setDeviceNo("deviceNo");
        storeDeviceDO.setDeviceGuid("deviceGuid");
        storeDeviceDO.setIsBinding(false);
        storeDeviceDO.setDeviceType(0);
        storeDeviceDO.setSort(0);
        storeDeviceDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setGmtUnbind(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setTableGuid("tableGuid");
        storeDeviceDO.setPadOrderType(0);
        when(mockStoreDeviceMapStruct.toStoreDeviceDO(any(StoreDeviceUnbindDTO.class))).thenReturn(storeDeviceDO);

        // Configure StoreDeviceMapper.selectList(...).
        final StoreDeviceDO storeDeviceDO1 = new StoreDeviceDO();
        storeDeviceDO1.setId(0L);
        storeDeviceDO1.setGuid("f39752b9-5a7f-4284-8bd9-9837cf5e494d");
        storeDeviceDO1.setStoreGuid("storeGuid");
        storeDeviceDO1.setDeviceNo("deviceNo");
        storeDeviceDO1.setDeviceGuid("deviceGuid");
        storeDeviceDO1.setIsBinding(false);
        storeDeviceDO1.setDeviceType(0);
        storeDeviceDO1.setSort(0);
        storeDeviceDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO1.setGmtUnbind(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO1.setTableGuid("tableGuid");
        storeDeviceDO1.setPadOrderType(0);
        final List<StoreDeviceDO> storeDeviceDOS = Arrays.asList(storeDeviceDO1);
        when(mockStoreDeviceMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(storeDeviceDOS);

        when(mockRedisService.getStoreMaster("storeGuid")).thenReturn(null);

        // Configure StoreDeviceMapper.selectOne(...).
        final StoreDeviceDO storeDeviceDO2 = new StoreDeviceDO();
        storeDeviceDO2.setId(0L);
        storeDeviceDO2.setGuid("f39752b9-5a7f-4284-8bd9-9837cf5e494d");
        storeDeviceDO2.setStoreGuid("storeGuid");
        storeDeviceDO2.setDeviceNo("deviceNo");
        storeDeviceDO2.setDeviceGuid("deviceGuid");
        storeDeviceDO2.setIsBinding(false);
        storeDeviceDO2.setDeviceType(0);
        storeDeviceDO2.setSort(0);
        storeDeviceDO2.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO2.setGmtUnbind(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO2.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO2.setTableGuid("tableGuid");
        storeDeviceDO2.setPadOrderType(0);
        when(mockStoreDeviceMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(storeDeviceDO2);

        // Configure StoreDeviceMapStruct.toStoreDeviceDTO(...).
        final StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO();
        storeDeviceDTO.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO.setStoreNo("code");
        storeDeviceDTO.setStoreGuid("storeGuid");
        storeDeviceDTO.setStoreName("name");
        storeDeviceDTO.setDeviceNo("deviceNo");
        storeDeviceDTO.setDeviceGuid("deviceGuid");
        storeDeviceDTO.setBinding(false);
        storeDeviceDTO.setRegister(false);
        final StoreDeviceDO storeDeviceDO3 = new StoreDeviceDO();
        storeDeviceDO3.setId(0L);
        storeDeviceDO3.setGuid("f39752b9-5a7f-4284-8bd9-9837cf5e494d");
        storeDeviceDO3.setStoreGuid("storeGuid");
        storeDeviceDO3.setDeviceNo("deviceNo");
        storeDeviceDO3.setDeviceGuid("deviceGuid");
        storeDeviceDO3.setIsBinding(false);
        storeDeviceDO3.setDeviceType(0);
        storeDeviceDO3.setSort(0);
        storeDeviceDO3.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO3.setGmtUnbind(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO3.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO3.setTableGuid("tableGuid");
        storeDeviceDO3.setPadOrderType(0);
        when(mockStoreDeviceMapStruct.toStoreDeviceDTO(storeDeviceDO3)).thenReturn(storeDeviceDTO);

        // Run the test
        final boolean result = storeDeviceServiceImplUnderTest.unbind(storeDeviceUnbindDTO);

        // Verify the results
        assertTrue(result);
        verify(mockBroadcastService).unBindDeviceToCloud(any(DeviceDTO.class));
        verify(mockPrintClient).deletePrinterByDevice("deviceGuid", "storeGuid", 0);

        // Confirm BroadcastService.deviceUnbind(...).
        final StoreDeviceDTO storeDeviceDTO1 = new StoreDeviceDTO();
        storeDeviceDTO1.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO1.setStoreNo("code");
        storeDeviceDTO1.setStoreGuid("storeGuid");
        storeDeviceDTO1.setStoreName("name");
        storeDeviceDTO1.setDeviceNo("deviceNo");
        storeDeviceDTO1.setDeviceGuid("deviceGuid");
        storeDeviceDTO1.setBinding(false);
        storeDeviceDTO1.setRegister(false);
        verify(mockBroadcastService).deviceUnbind(storeDeviceDTO1);
        verify(mockRedisService).removeStoreMaster("storeGuid");

        // Confirm RedisService.putStoreMaster(...).
        final StoreDeviceDTO storeDeviceDTO2 = new StoreDeviceDTO();
        storeDeviceDTO2.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO2.setStoreNo("code");
        storeDeviceDTO2.setStoreGuid("storeGuid");
        storeDeviceDTO2.setStoreName("name");
        storeDeviceDTO2.setDeviceNo("deviceNo");
        storeDeviceDTO2.setDeviceGuid("deviceGuid");
        storeDeviceDTO2.setBinding(false);
        storeDeviceDTO2.setRegister(false);
        verify(mockRedisService).putStoreMaster("storeGuid", storeDeviceDTO2);
        verify(mockPushService).pushMasterChanged("storeGuid", "deviceGuid");
    }

    @Test
    public void testUnbind_StoreDeviceMapperSelectOneReturnsNull() {
        // Setup
        final StoreDeviceUnbindDTO storeDeviceUnbindDTO = new StoreDeviceUnbindDTO();
        storeDeviceUnbindDTO.setStoreGuid("storeGuid");
        storeDeviceUnbindDTO.setDeviceNo("deviceNo");
        storeDeviceUnbindDTO.setUnbindUserGuid("system");
        storeDeviceUnbindDTO.setUnbindUserName("system");
        storeDeviceUnbindDTO.setFromCloud(false);

        // Configure StoreDeviceMapStruct.toStoreDeviceDO(...).
        final StoreDeviceDO storeDeviceDO = new StoreDeviceDO();
        storeDeviceDO.setId(0L);
        storeDeviceDO.setGuid("f39752b9-5a7f-4284-8bd9-9837cf5e494d");
        storeDeviceDO.setStoreGuid("storeGuid");
        storeDeviceDO.setDeviceNo("deviceNo");
        storeDeviceDO.setDeviceGuid("deviceGuid");
        storeDeviceDO.setIsBinding(false);
        storeDeviceDO.setDeviceType(0);
        storeDeviceDO.setSort(0);
        storeDeviceDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setGmtUnbind(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setTableGuid("tableGuid");
        storeDeviceDO.setPadOrderType(0);
        when(mockStoreDeviceMapStruct.toStoreDeviceDO(any(StoreDeviceUnbindDTO.class))).thenReturn(storeDeviceDO);

        // Configure StoreDeviceMapper.selectList(...).
        final StoreDeviceDO storeDeviceDO1 = new StoreDeviceDO();
        storeDeviceDO1.setId(0L);
        storeDeviceDO1.setGuid("f39752b9-5a7f-4284-8bd9-9837cf5e494d");
        storeDeviceDO1.setStoreGuid("storeGuid");
        storeDeviceDO1.setDeviceNo("deviceNo");
        storeDeviceDO1.setDeviceGuid("deviceGuid");
        storeDeviceDO1.setIsBinding(false);
        storeDeviceDO1.setDeviceType(0);
        storeDeviceDO1.setSort(0);
        storeDeviceDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO1.setGmtUnbind(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO1.setTableGuid("tableGuid");
        storeDeviceDO1.setPadOrderType(0);
        final List<StoreDeviceDO> storeDeviceDOS = Arrays.asList(storeDeviceDO1);
        when(mockStoreDeviceMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(storeDeviceDOS);

        when(mockRedisService.getStoreMaster("storeGuid")).thenReturn(null);
        when(mockStoreDeviceMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Run the test
        final boolean result = storeDeviceServiceImplUnderTest.unbind(storeDeviceUnbindDTO);

        // Verify the results
        assertTrue(result);
        verify(mockBroadcastService).unBindDeviceToCloud(any(DeviceDTO.class));
        verify(mockPrintClient).deletePrinterByDevice("deviceGuid", "storeGuid", 0);

        // Confirm BroadcastService.deviceUnbind(...).
        final StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO();
        storeDeviceDTO.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO.setStoreNo("code");
        storeDeviceDTO.setStoreGuid("storeGuid");
        storeDeviceDTO.setStoreName("name");
        storeDeviceDTO.setDeviceNo("deviceNo");
        storeDeviceDTO.setDeviceGuid("deviceGuid");
        storeDeviceDTO.setBinding(false);
        storeDeviceDTO.setRegister(false);
        verify(mockBroadcastService).deviceUnbind(storeDeviceDTO);
        verify(mockRedisService).removeStoreMaster("storeGuid");

        // Confirm RedisService.putStoreMaster(...).
        final StoreDeviceDTO storeDeviceDTO1 = new StoreDeviceDTO();
        storeDeviceDTO1.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO1.setStoreNo("code");
        storeDeviceDTO1.setStoreGuid("storeGuid");
        storeDeviceDTO1.setStoreName("name");
        storeDeviceDTO1.setDeviceNo("deviceNo");
        storeDeviceDTO1.setDeviceGuid("deviceGuid");
        storeDeviceDTO1.setBinding(false);
        storeDeviceDTO1.setRegister(false);
        verify(mockRedisService).putStoreMaster("storeGuid", storeDeviceDTO1);
        verify(mockPushService).pushMasterChanged("storeGuid", "deviceGuid");
    }

    @Test
    public void testGetMasterDeviceByStoreGuid() {
        // Setup
        final StoreDeviceDTO expectedResult = new StoreDeviceDTO();
        expectedResult.setEnterpriseGuid("enterpriseGuid");
        expectedResult.setStoreNo("code");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setStoreName("name");
        expectedResult.setDeviceNo("deviceNo");
        expectedResult.setDeviceGuid("deviceGuid");
        expectedResult.setBinding(false);
        expectedResult.setRegister(false);

        // Configure RedisService.getStoreMaster(...).
        final StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO();
        storeDeviceDTO.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO.setStoreNo("code");
        storeDeviceDTO.setStoreGuid("storeGuid");
        storeDeviceDTO.setStoreName("name");
        storeDeviceDTO.setDeviceNo("deviceNo");
        storeDeviceDTO.setDeviceGuid("deviceGuid");
        storeDeviceDTO.setBinding(false);
        storeDeviceDTO.setRegister(false);
        when(mockRedisService.getStoreMaster("storeGuid")).thenReturn(storeDeviceDTO);

        // Run the test
        final StoreDeviceDTO result = storeDeviceServiceImplUnderTest.getMasterDeviceByStoreGuid("storeGuid");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetMasterDeviceByStoreGuid_RedisServiceGetStoreMasterReturnsNull() {
        // Setup
        final StoreDeviceDTO expectedResult = new StoreDeviceDTO();
        expectedResult.setEnterpriseGuid("enterpriseGuid");
        expectedResult.setStoreNo("code");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setStoreName("name");
        expectedResult.setDeviceNo("deviceNo");
        expectedResult.setDeviceGuid("deviceGuid");
        expectedResult.setBinding(false);
        expectedResult.setRegister(false);

        when(mockRedisService.getStoreMaster("storeGuid")).thenReturn(null);

        // Configure StoreDeviceMapper.selectOne(...).
        final StoreDeviceDO storeDeviceDO = new StoreDeviceDO();
        storeDeviceDO.setId(0L);
        storeDeviceDO.setGuid("f39752b9-5a7f-4284-8bd9-9837cf5e494d");
        storeDeviceDO.setStoreGuid("storeGuid");
        storeDeviceDO.setDeviceNo("deviceNo");
        storeDeviceDO.setDeviceGuid("deviceGuid");
        storeDeviceDO.setIsBinding(false);
        storeDeviceDO.setDeviceType(0);
        storeDeviceDO.setSort(0);
        storeDeviceDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setGmtUnbind(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setTableGuid("tableGuid");
        storeDeviceDO.setPadOrderType(0);
        when(mockStoreDeviceMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(storeDeviceDO);

        // Configure StoreDeviceMapStruct.toStoreDeviceDTO(...).
        final StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO();
        storeDeviceDTO.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO.setStoreNo("code");
        storeDeviceDTO.setStoreGuid("storeGuid");
        storeDeviceDTO.setStoreName("name");
        storeDeviceDTO.setDeviceNo("deviceNo");
        storeDeviceDTO.setDeviceGuid("deviceGuid");
        storeDeviceDTO.setBinding(false);
        storeDeviceDTO.setRegister(false);
        final StoreDeviceDO storeDeviceDO1 = new StoreDeviceDO();
        storeDeviceDO1.setId(0L);
        storeDeviceDO1.setGuid("f39752b9-5a7f-4284-8bd9-9837cf5e494d");
        storeDeviceDO1.setStoreGuid("storeGuid");
        storeDeviceDO1.setDeviceNo("deviceNo");
        storeDeviceDO1.setDeviceGuid("deviceGuid");
        storeDeviceDO1.setIsBinding(false);
        storeDeviceDO1.setDeviceType(0);
        storeDeviceDO1.setSort(0);
        storeDeviceDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO1.setGmtUnbind(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO1.setTableGuid("tableGuid");
        storeDeviceDO1.setPadOrderType(0);
        when(mockStoreDeviceMapStruct.toStoreDeviceDTO(storeDeviceDO1)).thenReturn(storeDeviceDTO);

        // Run the test
        final StoreDeviceDTO result = storeDeviceServiceImplUnderTest.getMasterDeviceByStoreGuid("storeGuid");

        // Verify the results
        assertEquals(expectedResult, result);

        // Confirm RedisService.putStoreMaster(...).
        final StoreDeviceDTO storeDeviceDTO1 = new StoreDeviceDTO();
        storeDeviceDTO1.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO1.setStoreNo("code");
        storeDeviceDTO1.setStoreGuid("storeGuid");
        storeDeviceDTO1.setStoreName("name");
        storeDeviceDTO1.setDeviceNo("deviceNo");
        storeDeviceDTO1.setDeviceGuid("deviceGuid");
        storeDeviceDTO1.setBinding(false);
        storeDeviceDTO1.setRegister(false);
        verify(mockRedisService).putStoreMaster("storeGuid", storeDeviceDTO1);
    }

    @Test
    public void testGetMasterDeviceByStoreGuid_StoreDeviceMapperReturnsNull() {
        // Setup
        final StoreDeviceDTO expectedResult = new StoreDeviceDTO();
        expectedResult.setEnterpriseGuid("enterpriseGuid");
        expectedResult.setStoreNo("code");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setStoreName("name");
        expectedResult.setDeviceNo("deviceNo");
        expectedResult.setDeviceGuid("deviceGuid");
        expectedResult.setBinding(false);
        expectedResult.setRegister(false);

        when(mockRedisService.getStoreMaster("storeGuid")).thenReturn(null);
        when(mockStoreDeviceMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Run the test
        final StoreDeviceDTO result = storeDeviceServiceImplUnderTest.getMasterDeviceByStoreGuid("storeGuid");

        // Verify the results
        assertEquals(expectedResult, result);

        // Confirm RedisService.putStoreMaster(...).
        final StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO();
        storeDeviceDTO.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO.setStoreNo("code");
        storeDeviceDTO.setStoreGuid("storeGuid");
        storeDeviceDTO.setStoreName("name");
        storeDeviceDTO.setDeviceNo("deviceNo");
        storeDeviceDTO.setDeviceGuid("deviceGuid");
        storeDeviceDTO.setBinding(false);
        storeDeviceDTO.setRegister(false);
        verify(mockRedisService).putStoreMaster("storeGuid", storeDeviceDTO);
    }

    @Test
    public void testFindDeviceStatus() {
        // Setup
        final StoreDeviceDTO expectedResult = new StoreDeviceDTO();
        expectedResult.setEnterpriseGuid("enterpriseGuid");
        expectedResult.setStoreNo("code");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setStoreName("name");
        expectedResult.setDeviceNo("deviceNo");
        expectedResult.setDeviceGuid("deviceGuid");
        expectedResult.setBinding(false);
        expectedResult.setRegister(false);

        // Configure DeviceClient.findDeviceStatusInCloud(...).
        final DeviceStoreDTO deviceStoreDTO = new DeviceStoreDTO();
        deviceStoreDTO.setId(0L);
        deviceStoreDTO.setDeviceGuid("deviceGuid");
        deviceStoreDTO.setEnterpriseGuid("enterpriseGuid");
        deviceStoreDTO.setEnterpriseName("enterpriseName");
        deviceStoreDTO.setStoreGuid("storeGuid");
        when(mockDeviceClient.findDeviceStatusInCloud("deviceNo")).thenReturn(deviceStoreDTO);

        // Configure StoreDeviceMapper.selectList(...).
        final StoreDeviceDO storeDeviceDO = new StoreDeviceDO();
        storeDeviceDO.setId(0L);
        storeDeviceDO.setGuid("f39752b9-5a7f-4284-8bd9-9837cf5e494d");
        storeDeviceDO.setStoreGuid("storeGuid");
        storeDeviceDO.setDeviceNo("deviceNo");
        storeDeviceDO.setDeviceGuid("deviceGuid");
        storeDeviceDO.setIsBinding(false);
        storeDeviceDO.setDeviceType(0);
        storeDeviceDO.setSort(0);
        storeDeviceDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setGmtUnbind(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setTableGuid("tableGuid");
        storeDeviceDO.setPadOrderType(0);
        final List<StoreDeviceDO> storeDeviceDOS = Arrays.asList(storeDeviceDO);
        when(mockStoreDeviceMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(storeDeviceDOS);

        // Configure StoreService.queryStoreByGuid(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("199a49f2-3bf3-43c1-9c74-81a46e116b17");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        when(mockStoreService.queryStoreByGuid("storeGuid")).thenReturn(storeDTO);

        // Configure StoreDeviceMapStruct.toStoreDeviceDO(...).
        final StoreDeviceDO storeDeviceDO1 = new StoreDeviceDO();
        storeDeviceDO1.setId(0L);
        storeDeviceDO1.setGuid("f39752b9-5a7f-4284-8bd9-9837cf5e494d");
        storeDeviceDO1.setStoreGuid("storeGuid");
        storeDeviceDO1.setDeviceNo("deviceNo");
        storeDeviceDO1.setDeviceGuid("deviceGuid");
        storeDeviceDO1.setIsBinding(false);
        storeDeviceDO1.setDeviceType(0);
        storeDeviceDO1.setSort(0);
        storeDeviceDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO1.setGmtUnbind(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO1.setTableGuid("tableGuid");
        storeDeviceDO1.setPadOrderType(0);
        when(mockStoreDeviceMapStruct.toStoreDeviceDO(any(StoreDeviceUnbindDTO.class))).thenReturn(storeDeviceDO1);

        // Configure RedisService.getStoreMaster(...).
        final StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO();
        storeDeviceDTO.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO.setStoreNo("code");
        storeDeviceDTO.setStoreGuid("storeGuid");
        storeDeviceDTO.setStoreName("name");
        storeDeviceDTO.setDeviceNo("deviceNo");
        storeDeviceDTO.setDeviceGuid("deviceGuid");
        storeDeviceDTO.setBinding(false);
        storeDeviceDTO.setRegister(false);
        when(mockRedisService.getStoreMaster("storeGuid")).thenReturn(storeDeviceDTO);

        // Run the test
        final StoreDeviceDTO result = storeDeviceServiceImplUnderTest.findDeviceStatus("deviceNo");

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockStoreDeviceMapper).delete(any(LambdaQueryWrapper.class));

        // Confirm StoreDeviceMapper.insert(...).
        final StoreDeviceDO entity = new StoreDeviceDO();
        entity.setId(0L);
        entity.setGuid("f39752b9-5a7f-4284-8bd9-9837cf5e494d");
        entity.setStoreGuid("storeGuid");
        entity.setDeviceNo("deviceNo");
        entity.setDeviceGuid("deviceGuid");
        entity.setIsBinding(false);
        entity.setDeviceType(0);
        entity.setSort(0);
        entity.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setGmtUnbind(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setTableGuid("tableGuid");
        entity.setPadOrderType(0);
        verify(mockStoreDeviceMapper).insert(entity);
        verify(mockBroadcastService).unBindDeviceToCloud(any(DeviceDTO.class));
        verify(mockPrintClient).deletePrinterByDevice("deviceGuid", "storeGuid", 0);

        // Confirm BroadcastService.deviceUnbind(...).
        final StoreDeviceDTO storeDeviceDTO1 = new StoreDeviceDTO();
        storeDeviceDTO1.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO1.setStoreNo("code");
        storeDeviceDTO1.setStoreGuid("storeGuid");
        storeDeviceDTO1.setStoreName("name");
        storeDeviceDTO1.setDeviceNo("deviceNo");
        storeDeviceDTO1.setDeviceGuid("deviceGuid");
        storeDeviceDTO1.setBinding(false);
        storeDeviceDTO1.setRegister(false);
        verify(mockBroadcastService).deviceUnbind(storeDeviceDTO1);
        verify(mockRedisService).removeStoreMaster("storeGuid");
        verify(mockPushService).pushMasterChanged("storeGuid", "deviceGuid");
    }

    @Test
    public void testFindDeviceStatus_DeviceClientReturnsNull() {
        // Setup
        final StoreDeviceDTO expectedResult = new StoreDeviceDTO();
        expectedResult.setEnterpriseGuid("enterpriseGuid");
        expectedResult.setStoreNo("code");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setStoreName("name");
        expectedResult.setDeviceNo("deviceNo");
        expectedResult.setDeviceGuid("deviceGuid");
        expectedResult.setBinding(false);
        expectedResult.setRegister(false);

        when(mockDeviceClient.findDeviceStatusInCloud("deviceNo")).thenReturn(null);

        // Run the test
        final StoreDeviceDTO result = storeDeviceServiceImplUnderTest.findDeviceStatus("deviceNo");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testFindDeviceStatus_StoreDeviceMapperSelectListReturnsNoItems() {
        // Setup
        final StoreDeviceDTO expectedResult = new StoreDeviceDTO();
        expectedResult.setEnterpriseGuid("enterpriseGuid");
        expectedResult.setStoreNo("code");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setStoreName("name");
        expectedResult.setDeviceNo("deviceNo");
        expectedResult.setDeviceGuid("deviceGuid");
        expectedResult.setBinding(false);
        expectedResult.setRegister(false);

        // Configure DeviceClient.findDeviceStatusInCloud(...).
        final DeviceStoreDTO deviceStoreDTO = new DeviceStoreDTO();
        deviceStoreDTO.setId(0L);
        deviceStoreDTO.setDeviceGuid("deviceGuid");
        deviceStoreDTO.setEnterpriseGuid("enterpriseGuid");
        deviceStoreDTO.setEnterpriseName("enterpriseName");
        deviceStoreDTO.setStoreGuid("storeGuid");
        when(mockDeviceClient.findDeviceStatusInCloud("deviceNo")).thenReturn(deviceStoreDTO);

        when(mockStoreDeviceMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure StoreService.queryStoreByGuid(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("199a49f2-3bf3-43c1-9c74-81a46e116b17");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        when(mockStoreService.queryStoreByGuid("storeGuid")).thenReturn(storeDTO);

        // Configure StoreDeviceMapStruct.toStoreDeviceDO(...).
        final StoreDeviceDO storeDeviceDO = new StoreDeviceDO();
        storeDeviceDO.setId(0L);
        storeDeviceDO.setGuid("f39752b9-5a7f-4284-8bd9-9837cf5e494d");
        storeDeviceDO.setStoreGuid("storeGuid");
        storeDeviceDO.setDeviceNo("deviceNo");
        storeDeviceDO.setDeviceGuid("deviceGuid");
        storeDeviceDO.setIsBinding(false);
        storeDeviceDO.setDeviceType(0);
        storeDeviceDO.setSort(0);
        storeDeviceDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setGmtUnbind(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setTableGuid("tableGuid");
        storeDeviceDO.setPadOrderType(0);
        when(mockStoreDeviceMapStruct.toStoreDeviceDO(any(StoreDeviceUnbindDTO.class))).thenReturn(storeDeviceDO);

        // Configure RedisService.getStoreMaster(...).
        final StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO();
        storeDeviceDTO.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO.setStoreNo("code");
        storeDeviceDTO.setStoreGuid("storeGuid");
        storeDeviceDTO.setStoreName("name");
        storeDeviceDTO.setDeviceNo("deviceNo");
        storeDeviceDTO.setDeviceGuid("deviceGuid");
        storeDeviceDTO.setBinding(false);
        storeDeviceDTO.setRegister(false);
        when(mockRedisService.getStoreMaster("storeGuid")).thenReturn(storeDeviceDTO);

        // Run the test
        final StoreDeviceDTO result = storeDeviceServiceImplUnderTest.findDeviceStatus("deviceNo");

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockBroadcastService).unBindDeviceToCloud(any(DeviceDTO.class));
        verify(mockPrintClient).deletePrinterByDevice("deviceGuid", "storeGuid", 0);

        // Confirm BroadcastService.deviceUnbind(...).
        final StoreDeviceDTO storeDeviceDTO1 = new StoreDeviceDTO();
        storeDeviceDTO1.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO1.setStoreNo("code");
        storeDeviceDTO1.setStoreGuid("storeGuid");
        storeDeviceDTO1.setStoreName("name");
        storeDeviceDTO1.setDeviceNo("deviceNo");
        storeDeviceDTO1.setDeviceGuid("deviceGuid");
        storeDeviceDTO1.setBinding(false);
        storeDeviceDTO1.setRegister(false);
        verify(mockBroadcastService).deviceUnbind(storeDeviceDTO1);
        verify(mockRedisService).removeStoreMaster("storeGuid");
        verify(mockPushService).pushMasterChanged("storeGuid", "deviceGuid");
    }

    @Test
    public void testFindDeviceStatus_RedisServiceGetStoreMasterReturnsNull() {
        // Setup
        final StoreDeviceDTO expectedResult = new StoreDeviceDTO();
        expectedResult.setEnterpriseGuid("enterpriseGuid");
        expectedResult.setStoreNo("code");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setStoreName("name");
        expectedResult.setDeviceNo("deviceNo");
        expectedResult.setDeviceGuid("deviceGuid");
        expectedResult.setBinding(false);
        expectedResult.setRegister(false);

        // Configure DeviceClient.findDeviceStatusInCloud(...).
        final DeviceStoreDTO deviceStoreDTO = new DeviceStoreDTO();
        deviceStoreDTO.setId(0L);
        deviceStoreDTO.setDeviceGuid("deviceGuid");
        deviceStoreDTO.setEnterpriseGuid("enterpriseGuid");
        deviceStoreDTO.setEnterpriseName("enterpriseName");
        deviceStoreDTO.setStoreGuid("storeGuid");
        when(mockDeviceClient.findDeviceStatusInCloud("deviceNo")).thenReturn(deviceStoreDTO);

        // Configure StoreDeviceMapper.selectList(...).
        final StoreDeviceDO storeDeviceDO = new StoreDeviceDO();
        storeDeviceDO.setId(0L);
        storeDeviceDO.setGuid("f39752b9-5a7f-4284-8bd9-9837cf5e494d");
        storeDeviceDO.setStoreGuid("storeGuid");
        storeDeviceDO.setDeviceNo("deviceNo");
        storeDeviceDO.setDeviceGuid("deviceGuid");
        storeDeviceDO.setIsBinding(false);
        storeDeviceDO.setDeviceType(0);
        storeDeviceDO.setSort(0);
        storeDeviceDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setGmtUnbind(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setTableGuid("tableGuid");
        storeDeviceDO.setPadOrderType(0);
        final List<StoreDeviceDO> storeDeviceDOS = Arrays.asList(storeDeviceDO);
        when(mockStoreDeviceMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(storeDeviceDOS);

        // Configure StoreService.queryStoreByGuid(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("199a49f2-3bf3-43c1-9c74-81a46e116b17");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        when(mockStoreService.queryStoreByGuid("storeGuid")).thenReturn(storeDTO);

        // Configure StoreDeviceMapStruct.toStoreDeviceDO(...).
        final StoreDeviceDO storeDeviceDO1 = new StoreDeviceDO();
        storeDeviceDO1.setId(0L);
        storeDeviceDO1.setGuid("f39752b9-5a7f-4284-8bd9-9837cf5e494d");
        storeDeviceDO1.setStoreGuid("storeGuid");
        storeDeviceDO1.setDeviceNo("deviceNo");
        storeDeviceDO1.setDeviceGuid("deviceGuid");
        storeDeviceDO1.setIsBinding(false);
        storeDeviceDO1.setDeviceType(0);
        storeDeviceDO1.setSort(0);
        storeDeviceDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO1.setGmtUnbind(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO1.setTableGuid("tableGuid");
        storeDeviceDO1.setPadOrderType(0);
        when(mockStoreDeviceMapStruct.toStoreDeviceDO(any(StoreDeviceUnbindDTO.class))).thenReturn(storeDeviceDO1);

        when(mockRedisService.getStoreMaster("storeGuid")).thenReturn(null);

        // Configure StoreDeviceMapper.selectOne(...).
        final StoreDeviceDO storeDeviceDO2 = new StoreDeviceDO();
        storeDeviceDO2.setId(0L);
        storeDeviceDO2.setGuid("f39752b9-5a7f-4284-8bd9-9837cf5e494d");
        storeDeviceDO2.setStoreGuid("storeGuid");
        storeDeviceDO2.setDeviceNo("deviceNo");
        storeDeviceDO2.setDeviceGuid("deviceGuid");
        storeDeviceDO2.setIsBinding(false);
        storeDeviceDO2.setDeviceType(0);
        storeDeviceDO2.setSort(0);
        storeDeviceDO2.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO2.setGmtUnbind(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO2.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO2.setTableGuid("tableGuid");
        storeDeviceDO2.setPadOrderType(0);
        when(mockStoreDeviceMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(storeDeviceDO2);

        // Configure StoreDeviceMapStruct.toStoreDeviceDTO(...).
        final StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO();
        storeDeviceDTO.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO.setStoreNo("code");
        storeDeviceDTO.setStoreGuid("storeGuid");
        storeDeviceDTO.setStoreName("name");
        storeDeviceDTO.setDeviceNo("deviceNo");
        storeDeviceDTO.setDeviceGuid("deviceGuid");
        storeDeviceDTO.setBinding(false);
        storeDeviceDTO.setRegister(false);
        final StoreDeviceDO storeDeviceDO3 = new StoreDeviceDO();
        storeDeviceDO3.setId(0L);
        storeDeviceDO3.setGuid("f39752b9-5a7f-4284-8bd9-9837cf5e494d");
        storeDeviceDO3.setStoreGuid("storeGuid");
        storeDeviceDO3.setDeviceNo("deviceNo");
        storeDeviceDO3.setDeviceGuid("deviceGuid");
        storeDeviceDO3.setIsBinding(false);
        storeDeviceDO3.setDeviceType(0);
        storeDeviceDO3.setSort(0);
        storeDeviceDO3.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO3.setGmtUnbind(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO3.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO3.setTableGuid("tableGuid");
        storeDeviceDO3.setPadOrderType(0);
        when(mockStoreDeviceMapStruct.toStoreDeviceDTO(storeDeviceDO3)).thenReturn(storeDeviceDTO);

        // Run the test
        final StoreDeviceDTO result = storeDeviceServiceImplUnderTest.findDeviceStatus("deviceNo");

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockStoreDeviceMapper).delete(any(LambdaQueryWrapper.class));

        // Confirm StoreDeviceMapper.insert(...).
        final StoreDeviceDO entity = new StoreDeviceDO();
        entity.setId(0L);
        entity.setGuid("f39752b9-5a7f-4284-8bd9-9837cf5e494d");
        entity.setStoreGuid("storeGuid");
        entity.setDeviceNo("deviceNo");
        entity.setDeviceGuid("deviceGuid");
        entity.setIsBinding(false);
        entity.setDeviceType(0);
        entity.setSort(0);
        entity.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setGmtUnbind(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setTableGuid("tableGuid");
        entity.setPadOrderType(0);
        verify(mockStoreDeviceMapper).insert(entity);
        verify(mockBroadcastService).unBindDeviceToCloud(any(DeviceDTO.class));
        verify(mockPrintClient).deletePrinterByDevice("deviceGuid", "storeGuid", 0);

        // Confirm BroadcastService.deviceUnbind(...).
        final StoreDeviceDTO storeDeviceDTO1 = new StoreDeviceDTO();
        storeDeviceDTO1.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO1.setStoreNo("code");
        storeDeviceDTO1.setStoreGuid("storeGuid");
        storeDeviceDTO1.setStoreName("name");
        storeDeviceDTO1.setDeviceNo("deviceNo");
        storeDeviceDTO1.setDeviceGuid("deviceGuid");
        storeDeviceDTO1.setBinding(false);
        storeDeviceDTO1.setRegister(false);
        verify(mockBroadcastService).deviceUnbind(storeDeviceDTO1);
        verify(mockRedisService).removeStoreMaster("storeGuid");

        // Confirm RedisService.putStoreMaster(...).
        final StoreDeviceDTO storeDeviceDTO2 = new StoreDeviceDTO();
        storeDeviceDTO2.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO2.setStoreNo("code");
        storeDeviceDTO2.setStoreGuid("storeGuid");
        storeDeviceDTO2.setStoreName("name");
        storeDeviceDTO2.setDeviceNo("deviceNo");
        storeDeviceDTO2.setDeviceGuid("deviceGuid");
        storeDeviceDTO2.setBinding(false);
        storeDeviceDTO2.setRegister(false);
        verify(mockRedisService).putStoreMaster("storeGuid", storeDeviceDTO2);
        verify(mockPushService).pushMasterChanged("storeGuid", "deviceGuid");
    }

    @Test
    public void testFindDeviceStatus_StoreDeviceMapperSelectOneReturnsNull() {
        // Setup
        final StoreDeviceDTO expectedResult = new StoreDeviceDTO();
        expectedResult.setEnterpriseGuid("enterpriseGuid");
        expectedResult.setStoreNo("code");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setStoreName("name");
        expectedResult.setDeviceNo("deviceNo");
        expectedResult.setDeviceGuid("deviceGuid");
        expectedResult.setBinding(false);
        expectedResult.setRegister(false);

        // Configure DeviceClient.findDeviceStatusInCloud(...).
        final DeviceStoreDTO deviceStoreDTO = new DeviceStoreDTO();
        deviceStoreDTO.setId(0L);
        deviceStoreDTO.setDeviceGuid("deviceGuid");
        deviceStoreDTO.setEnterpriseGuid("enterpriseGuid");
        deviceStoreDTO.setEnterpriseName("enterpriseName");
        deviceStoreDTO.setStoreGuid("storeGuid");
        when(mockDeviceClient.findDeviceStatusInCloud("deviceNo")).thenReturn(deviceStoreDTO);

        // Configure StoreDeviceMapper.selectList(...).
        final StoreDeviceDO storeDeviceDO = new StoreDeviceDO();
        storeDeviceDO.setId(0L);
        storeDeviceDO.setGuid("f39752b9-5a7f-4284-8bd9-9837cf5e494d");
        storeDeviceDO.setStoreGuid("storeGuid");
        storeDeviceDO.setDeviceNo("deviceNo");
        storeDeviceDO.setDeviceGuid("deviceGuid");
        storeDeviceDO.setIsBinding(false);
        storeDeviceDO.setDeviceType(0);
        storeDeviceDO.setSort(0);
        storeDeviceDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setGmtUnbind(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setTableGuid("tableGuid");
        storeDeviceDO.setPadOrderType(0);
        final List<StoreDeviceDO> storeDeviceDOS = Arrays.asList(storeDeviceDO);
        when(mockStoreDeviceMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(storeDeviceDOS);

        // Configure StoreService.queryStoreByGuid(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("199a49f2-3bf3-43c1-9c74-81a46e116b17");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        when(mockStoreService.queryStoreByGuid("storeGuid")).thenReturn(storeDTO);

        // Configure StoreDeviceMapStruct.toStoreDeviceDO(...).
        final StoreDeviceDO storeDeviceDO1 = new StoreDeviceDO();
        storeDeviceDO1.setId(0L);
        storeDeviceDO1.setGuid("f39752b9-5a7f-4284-8bd9-9837cf5e494d");
        storeDeviceDO1.setStoreGuid("storeGuid");
        storeDeviceDO1.setDeviceNo("deviceNo");
        storeDeviceDO1.setDeviceGuid("deviceGuid");
        storeDeviceDO1.setIsBinding(false);
        storeDeviceDO1.setDeviceType(0);
        storeDeviceDO1.setSort(0);
        storeDeviceDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO1.setGmtUnbind(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO1.setTableGuid("tableGuid");
        storeDeviceDO1.setPadOrderType(0);
        when(mockStoreDeviceMapStruct.toStoreDeviceDO(any(StoreDeviceUnbindDTO.class))).thenReturn(storeDeviceDO1);

        when(mockRedisService.getStoreMaster("storeGuid")).thenReturn(null);
        when(mockStoreDeviceMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Run the test
        final StoreDeviceDTO result = storeDeviceServiceImplUnderTest.findDeviceStatus("deviceNo");

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockStoreDeviceMapper).delete(any(LambdaQueryWrapper.class));

        // Confirm StoreDeviceMapper.insert(...).
        final StoreDeviceDO entity = new StoreDeviceDO();
        entity.setId(0L);
        entity.setGuid("f39752b9-5a7f-4284-8bd9-9837cf5e494d");
        entity.setStoreGuid("storeGuid");
        entity.setDeviceNo("deviceNo");
        entity.setDeviceGuid("deviceGuid");
        entity.setIsBinding(false);
        entity.setDeviceType(0);
        entity.setSort(0);
        entity.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setGmtUnbind(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setTableGuid("tableGuid");
        entity.setPadOrderType(0);
        verify(mockStoreDeviceMapper).insert(entity);
        verify(mockBroadcastService).unBindDeviceToCloud(any(DeviceDTO.class));
        verify(mockPrintClient).deletePrinterByDevice("deviceGuid", "storeGuid", 0);

        // Confirm BroadcastService.deviceUnbind(...).
        final StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO();
        storeDeviceDTO.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO.setStoreNo("code");
        storeDeviceDTO.setStoreGuid("storeGuid");
        storeDeviceDTO.setStoreName("name");
        storeDeviceDTO.setDeviceNo("deviceNo");
        storeDeviceDTO.setDeviceGuid("deviceGuid");
        storeDeviceDTO.setBinding(false);
        storeDeviceDTO.setRegister(false);
        verify(mockBroadcastService).deviceUnbind(storeDeviceDTO);
        verify(mockRedisService).removeStoreMaster("storeGuid");

        // Confirm RedisService.putStoreMaster(...).
        final StoreDeviceDTO storeDeviceDTO1 = new StoreDeviceDTO();
        storeDeviceDTO1.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO1.setStoreNo("code");
        storeDeviceDTO1.setStoreGuid("storeGuid");
        storeDeviceDTO1.setStoreName("name");
        storeDeviceDTO1.setDeviceNo("deviceNo");
        storeDeviceDTO1.setDeviceGuid("deviceGuid");
        storeDeviceDTO1.setBinding(false);
        storeDeviceDTO1.setRegister(false);
        verify(mockRedisService).putStoreMaster("storeGuid", storeDeviceDTO1);
        verify(mockPushService).pushMasterChanged("storeGuid", "deviceGuid");
    }

    @Test
    public void testSetMasterDevice() {
        // Setup
        // Run the test
        storeDeviceServiceImplUnderTest.setMasterDevice("storeGuid", "deviceGuid");

        // Verify the results
        verify(mockRedisService).removeStoreMaster("storeGuid");
        verify(mockPushService).pushMasterChanged("storeGuid", "deviceGuid");
    }

    @Test
    public void testQueryPadOrderType() {
        // Setup
        final PadOrderTypeReqDTO padOrderTypeReqDTO = new PadOrderTypeReqDTO("storeGuid", 0, "tableGuid", "deviceNo",
                Arrays.asList("value"));

        // Configure StoreDeviceMapper.selectOne(...).
        final StoreDeviceDO storeDeviceDO = new StoreDeviceDO();
        storeDeviceDO.setId(0L);
        storeDeviceDO.setGuid("f39752b9-5a7f-4284-8bd9-9837cf5e494d");
        storeDeviceDO.setStoreGuid("storeGuid");
        storeDeviceDO.setDeviceNo("deviceNo");
        storeDeviceDO.setDeviceGuid("deviceGuid");
        storeDeviceDO.setIsBinding(false);
        storeDeviceDO.setDeviceType(0);
        storeDeviceDO.setSort(0);
        storeDeviceDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setGmtUnbind(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setTableGuid("tableGuid");
        storeDeviceDO.setPadOrderType(0);
        when(mockStoreDeviceMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(storeDeviceDO);

        // Configure EnterpriseClientService.findMemberInfoByOrganizationGuid(...).
        final MultiMemberDTO multiMemberDTO = new MultiMemberDTO();
        multiMemberDTO.setId(0L);
        multiMemberDTO.setEnterpriseGuid("enterpriseGuid");
        multiMemberDTO.setMultiMemberGuid("multiMemberGuid");
        multiMemberDTO.setMultiMemberName("multiMemberName");
        multiMemberDTO.setEnabled(false);
        when(mockEnterpriseClientService.findMemberInfoByOrganizationGuid("storeGuid")).thenReturn(multiMemberDTO);

        // Run the test
        final PadOrderTypeRespDTO result = storeDeviceServiceImplUnderTest.queryPadOrderType(padOrderTypeReqDTO);

        // Verify the results
    }

    @Test
    public void testQueryBindingTableInfo() {
        // Setup
        final PadOrderTypeReqDTO padOrderTypeReqDTO = new PadOrderTypeReqDTO("storeGuid", 0, "tableGuid", "deviceNo",
                Arrays.asList("value"));
        final TableInfoDTO expectedResult = new TableInfoDTO();
        expectedResult.setTableGuid("tableGuid");
        expectedResult.setTableName("tableCode");
        expectedResult.setAreaGuid("areaGuid");
        expectedResult.setAreaName("areaName");
        expectedResult.setIsBind(false);

        when(mockStoreDeviceMapper.queryTableGuidByDeviceNo("storeGuid", "deviceNo")).thenReturn("tableGuid");

        // Configure TableService.queryTableInfo(...).
        final TableBasicDTO tableBasicDTO = new TableBasicDTO();
        tableBasicDTO.setGuid("efd158b2-9bc4-4e9c-871b-402d221b7a30");
        tableBasicDTO.setStoreGuid("storeGuid");
        tableBasicDTO.setAreaGuid("areaGuid");
        tableBasicDTO.setAreaName("areaName");
        tableBasicDTO.setTableCode("tableCode");
        when(mockTableService.queryTableInfo("tableGuid")).thenReturn(tableBasicDTO);

        // Run the test
        final TableInfoDTO result = storeDeviceServiceImplUnderTest.queryBindingTableInfo(padOrderTypeReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testQueryUnBindingTableInfo() {
        // Setup
        final PadOrderTypeReqDTO padOrderTypeReqDTO = new PadOrderTypeReqDTO("storeGuid", 0, "tableGuid", "deviceNo",
                Arrays.asList("value"));
        final TableInfoDTO tableInfoDTO = new TableInfoDTO();
        tableInfoDTO.setTableGuid("tableGuid");
        tableInfoDTO.setTableName("tableCode");
        tableInfoDTO.setAreaGuid("areaGuid");
        tableInfoDTO.setAreaName("areaName");
        tableInfoDTO.setIsBind(false);
        final List<PadAreaDTO> expectedResult = Arrays.asList(
                new PadAreaDTO("areaGuid", "areaName", Arrays.asList(tableInfoDTO)));
        when(mockStoreDeviceMapper.queryTableGuidByStoreGuid("storeGuid")).thenReturn(Arrays.asList("value"));

        // Configure TableService.queryUnBindingTableInfo(...).
        final TableInfoDTO tableInfoDTO1 = new TableInfoDTO();
        tableInfoDTO1.setTableGuid("tableGuid");
        tableInfoDTO1.setTableName("tableCode");
        tableInfoDTO1.setAreaGuid("areaGuid");
        tableInfoDTO1.setAreaName("areaName");
        tableInfoDTO1.setIsBind(false);
        final List<PadAreaDTO> padAreaDTOS = Arrays.asList(
                new PadAreaDTO("areaGuid", "areaName", Arrays.asList(tableInfoDTO1)));
        when(mockTableService.queryUnBindingTableInfo(Arrays.asList("value"), "storeGuid")).thenReturn(padAreaDTOS);

        // Run the test
        final List<PadAreaDTO> result = storeDeviceServiceImplUnderTest.queryUnBindingTableInfo(padOrderTypeReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testQueryUnBindingTableInfo_StoreDeviceMapperReturnsNoItems() {
        // Setup
        final PadOrderTypeReqDTO padOrderTypeReqDTO = new PadOrderTypeReqDTO("storeGuid", 0, "tableGuid", "deviceNo",
                Arrays.asList("value"));
        final TableInfoDTO tableInfoDTO = new TableInfoDTO();
        tableInfoDTO.setTableGuid("tableGuid");
        tableInfoDTO.setTableName("tableCode");
        tableInfoDTO.setAreaGuid("areaGuid");
        tableInfoDTO.setAreaName("areaName");
        tableInfoDTO.setIsBind(false);
        final List<PadAreaDTO> expectedResult = Arrays.asList(
                new PadAreaDTO("areaGuid", "areaName", Arrays.asList(tableInfoDTO)));
        when(mockStoreDeviceMapper.queryTableGuidByStoreGuid("storeGuid")).thenReturn(Collections.emptyList());

        // Configure TableService.queryUnBindingTableInfo(...).
        final TableInfoDTO tableInfoDTO1 = new TableInfoDTO();
        tableInfoDTO1.setTableGuid("tableGuid");
        tableInfoDTO1.setTableName("tableCode");
        tableInfoDTO1.setAreaGuid("areaGuid");
        tableInfoDTO1.setAreaName("areaName");
        tableInfoDTO1.setIsBind(false);
        final List<PadAreaDTO> padAreaDTOS = Arrays.asList(
                new PadAreaDTO("areaGuid", "areaName", Arrays.asList(tableInfoDTO1)));
        when(mockTableService.queryUnBindingTableInfo(Arrays.asList("value"), "storeGuid")).thenReturn(padAreaDTOS);

        // Run the test
        final List<PadAreaDTO> result = storeDeviceServiceImplUnderTest.queryUnBindingTableInfo(padOrderTypeReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testQueryUnBindingTableInfo_TableServiceReturnsNoItems() {
        // Setup
        final PadOrderTypeReqDTO padOrderTypeReqDTO = new PadOrderTypeReqDTO("storeGuid", 0, "tableGuid", "deviceNo",
                Arrays.asList("value"));
        when(mockStoreDeviceMapper.queryTableGuidByStoreGuid("storeGuid")).thenReturn(Arrays.asList("value"));
        when(mockTableService.queryUnBindingTableInfo(Arrays.asList("value"), "storeGuid"))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<PadAreaDTO> result = storeDeviceServiceImplUnderTest.queryUnBindingTableInfo(padOrderTypeReqDTO);

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testInitializePadOrderTypeSet() {
        // Setup
        final PadOrderTypeReqDTO padOrderTypeReqDTO = new PadOrderTypeReqDTO("storeGuid", 0, "tableGuid", "deviceNo",
                Arrays.asList("value"));

        // Run the test
        final boolean result = storeDeviceServiceImplUnderTest.initializePadOrderTypeSet(padOrderTypeReqDTO);

        // Verify the results
        assertTrue(result);
        verify(mockStoreDeviceMapper).updatePadOrderType(0, "storeGuid", "deviceNo");
    }

    @Test
    public void testPadOrderTypeSet() {
        // Setup
        final PadOrderTypeReqDTO padOrderTypeReqDTO = new PadOrderTypeReqDTO("storeGuid", 0, "tableGuid", "deviceNo",
                Arrays.asList("value"));
        when(mockStoreDeviceMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(0);

        // Run the test
        final boolean result = storeDeviceServiceImplUnderTest.padOrderTypeSet(padOrderTypeReqDTO);

        // Verify the results
        assertFalse(result);
        verify(mockStoreDeviceMapper).updatePadOrderType(0, "storeGuid", "deviceNo");
    }

    @Test
    public void testQueryDeviceByStoreTable() {
        // Setup
        final PadOrderTypeReqDTO padOrderTypeReqDTO = new PadOrderTypeReqDTO("storeGuid", 0, "tableGuid", "deviceNo",
                Arrays.asList("value"));
        final StoreDeviceDTO expectedResult = new StoreDeviceDTO();
        expectedResult.setEnterpriseGuid("enterpriseGuid");
        expectedResult.setStoreNo("code");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setStoreName("name");
        expectedResult.setDeviceNo("deviceNo");
        expectedResult.setDeviceGuid("deviceGuid");
        expectedResult.setBinding(false);
        expectedResult.setRegister(false);

        // Configure StoreDeviceMapStruct.toStoreDeviceDTO(...).
        final StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO();
        storeDeviceDTO.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO.setStoreNo("code");
        storeDeviceDTO.setStoreGuid("storeGuid");
        storeDeviceDTO.setStoreName("name");
        storeDeviceDTO.setDeviceNo("deviceNo");
        storeDeviceDTO.setDeviceGuid("deviceGuid");
        storeDeviceDTO.setBinding(false);
        storeDeviceDTO.setRegister(false);
        final StoreDeviceDO storeDeviceDO = new StoreDeviceDO();
        storeDeviceDO.setId(0L);
        storeDeviceDO.setGuid("f39752b9-5a7f-4284-8bd9-9837cf5e494d");
        storeDeviceDO.setStoreGuid("storeGuid");
        storeDeviceDO.setDeviceNo("deviceNo");
        storeDeviceDO.setDeviceGuid("deviceGuid");
        storeDeviceDO.setIsBinding(false);
        storeDeviceDO.setDeviceType(0);
        storeDeviceDO.setSort(0);
        storeDeviceDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setGmtUnbind(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setTableGuid("tableGuid");
        storeDeviceDO.setPadOrderType(0);
        when(mockStoreDeviceMapStruct.toStoreDeviceDTO(storeDeviceDO)).thenReturn(storeDeviceDTO);

        // Run the test
        final StoreDeviceDTO result = storeDeviceServiceImplUnderTest.queryDeviceByStoreTable(padOrderTypeReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testListDeviceByStoreTable() {
        // Setup
        final PadOrderTypeReqDTO padOrderTypeReqDTO = new PadOrderTypeReqDTO("storeGuid", 0, "tableGuid", "deviceNo",
                Arrays.asList("value"));
        final StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO();
        storeDeviceDTO.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO.setStoreNo("code");
        storeDeviceDTO.setStoreGuid("storeGuid");
        storeDeviceDTO.setStoreName("name");
        storeDeviceDTO.setDeviceNo("deviceNo");
        storeDeviceDTO.setDeviceGuid("deviceGuid");
        storeDeviceDTO.setBinding(false);
        storeDeviceDTO.setRegister(false);
        final List<StoreDeviceDTO> expectedResult = Arrays.asList(storeDeviceDTO);

        // Configure StoreDeviceMapStruct.storeDeviceDOList2StoreDeviceDTOList(...).
        final StoreDeviceDTO storeDeviceDTO1 = new StoreDeviceDTO();
        storeDeviceDTO1.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO1.setStoreNo("code");
        storeDeviceDTO1.setStoreGuid("storeGuid");
        storeDeviceDTO1.setStoreName("name");
        storeDeviceDTO1.setDeviceNo("deviceNo");
        storeDeviceDTO1.setDeviceGuid("deviceGuid");
        storeDeviceDTO1.setBinding(false);
        storeDeviceDTO1.setRegister(false);
        final List<StoreDeviceDTO> storeDeviceDTOS = Arrays.asList(storeDeviceDTO1);
        final StoreDeviceDO storeDeviceDO = new StoreDeviceDO();
        storeDeviceDO.setId(0L);
        storeDeviceDO.setGuid("f39752b9-5a7f-4284-8bd9-9837cf5e494d");
        storeDeviceDO.setStoreGuid("storeGuid");
        storeDeviceDO.setDeviceNo("deviceNo");
        storeDeviceDO.setDeviceGuid("deviceGuid");
        storeDeviceDO.setIsBinding(false);
        storeDeviceDO.setDeviceType(0);
        storeDeviceDO.setSort(0);
        storeDeviceDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setGmtUnbind(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setTableGuid("tableGuid");
        storeDeviceDO.setPadOrderType(0);
        final List<StoreDeviceDO> storeDeviceDOList = Arrays.asList(storeDeviceDO);
        when(mockStoreDeviceMapStruct.storeDeviceDOList2StoreDeviceDTOList(storeDeviceDOList))
                .thenReturn(storeDeviceDTOS);

        // Run the test
        final List<StoreDeviceDTO> result = storeDeviceServiceImplUnderTest.listDeviceByStoreTable(padOrderTypeReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testListDeviceByStoreTable_StoreDeviceMapStructReturnsNoItems() {
        // Setup
        final PadOrderTypeReqDTO padOrderTypeReqDTO = new PadOrderTypeReqDTO("storeGuid", 0, "tableGuid", "deviceNo",
                Arrays.asList("value"));

        // Configure StoreDeviceMapStruct.storeDeviceDOList2StoreDeviceDTOList(...).
        final StoreDeviceDO storeDeviceDO = new StoreDeviceDO();
        storeDeviceDO.setId(0L);
        storeDeviceDO.setGuid("f39752b9-5a7f-4284-8bd9-9837cf5e494d");
        storeDeviceDO.setStoreGuid("storeGuid");
        storeDeviceDO.setDeviceNo("deviceNo");
        storeDeviceDO.setDeviceGuid("deviceGuid");
        storeDeviceDO.setIsBinding(false);
        storeDeviceDO.setDeviceType(0);
        storeDeviceDO.setSort(0);
        storeDeviceDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setGmtUnbind(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setTableGuid("tableGuid");
        storeDeviceDO.setPadOrderType(0);
        final List<StoreDeviceDO> storeDeviceDOList = Arrays.asList(storeDeviceDO);
        when(mockStoreDeviceMapStruct.storeDeviceDOList2StoreDeviceDTOList(storeDeviceDOList))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<StoreDeviceDTO> result = storeDeviceServiceImplUnderTest.listDeviceByStoreTable(padOrderTypeReqDTO);

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testListAllDeviceByStoreTable() {
        // Setup
        final PadOrderTypeReqDTO padOrderTypeReqDTO = new PadOrderTypeReqDTO("storeGuid", 0, "tableGuid", "deviceNo",
                Arrays.asList("value"));
        final StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO();
        storeDeviceDTO.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO.setStoreNo("code");
        storeDeviceDTO.setStoreGuid("storeGuid");
        storeDeviceDTO.setStoreName("name");
        storeDeviceDTO.setDeviceNo("deviceNo");
        storeDeviceDTO.setDeviceGuid("deviceGuid");
        storeDeviceDTO.setBinding(false);
        storeDeviceDTO.setRegister(false);
        final List<StoreDeviceDTO> expectedResult = Arrays.asList(storeDeviceDTO);

        // Configure StoreDeviceMapStruct.storeDeviceDOList2StoreDeviceDTOList(...).
        final StoreDeviceDTO storeDeviceDTO1 = new StoreDeviceDTO();
        storeDeviceDTO1.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO1.setStoreNo("code");
        storeDeviceDTO1.setStoreGuid("storeGuid");
        storeDeviceDTO1.setStoreName("name");
        storeDeviceDTO1.setDeviceNo("deviceNo");
        storeDeviceDTO1.setDeviceGuid("deviceGuid");
        storeDeviceDTO1.setBinding(false);
        storeDeviceDTO1.setRegister(false);
        final List<StoreDeviceDTO> storeDeviceDTOS = Arrays.asList(storeDeviceDTO1);
        final StoreDeviceDO storeDeviceDO = new StoreDeviceDO();
        storeDeviceDO.setId(0L);
        storeDeviceDO.setGuid("f39752b9-5a7f-4284-8bd9-9837cf5e494d");
        storeDeviceDO.setStoreGuid("storeGuid");
        storeDeviceDO.setDeviceNo("deviceNo");
        storeDeviceDO.setDeviceGuid("deviceGuid");
        storeDeviceDO.setIsBinding(false);
        storeDeviceDO.setDeviceType(0);
        storeDeviceDO.setSort(0);
        storeDeviceDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setGmtUnbind(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setTableGuid("tableGuid");
        storeDeviceDO.setPadOrderType(0);
        final List<StoreDeviceDO> storeDeviceDOList = Arrays.asList(storeDeviceDO);
        when(mockStoreDeviceMapStruct.storeDeviceDOList2StoreDeviceDTOList(storeDeviceDOList))
                .thenReturn(storeDeviceDTOS);

        // Run the test
        final List<StoreDeviceDTO> result = storeDeviceServiceImplUnderTest.listAllDeviceByStoreTable(
                padOrderTypeReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testListAllDeviceByStoreTable_StoreDeviceMapStructReturnsNoItems() {
        // Setup
        final PadOrderTypeReqDTO padOrderTypeReqDTO = new PadOrderTypeReqDTO("storeGuid", 0, "tableGuid", "deviceNo",
                Arrays.asList("value"));

        // Configure StoreDeviceMapStruct.storeDeviceDOList2StoreDeviceDTOList(...).
        final StoreDeviceDO storeDeviceDO = new StoreDeviceDO();
        storeDeviceDO.setId(0L);
        storeDeviceDO.setGuid("f39752b9-5a7f-4284-8bd9-9837cf5e494d");
        storeDeviceDO.setStoreGuid("storeGuid");
        storeDeviceDO.setDeviceNo("deviceNo");
        storeDeviceDO.setDeviceGuid("deviceGuid");
        storeDeviceDO.setIsBinding(false);
        storeDeviceDO.setDeviceType(0);
        storeDeviceDO.setSort(0);
        storeDeviceDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setGmtUnbind(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setTableGuid("tableGuid");
        storeDeviceDO.setPadOrderType(0);
        final List<StoreDeviceDO> storeDeviceDOList = Arrays.asList(storeDeviceDO);
        when(mockStoreDeviceMapStruct.storeDeviceDOList2StoreDeviceDTOList(storeDeviceDOList))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<StoreDeviceDTO> result = storeDeviceServiceImplUnderTest.listAllDeviceByStoreTable(
                padOrderTypeReqDTO);

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testQueryDeviceByDeviceId() {
        // Setup
        final StoreDeviceDTO expectedResult = new StoreDeviceDTO();
        expectedResult.setEnterpriseGuid("enterpriseGuid");
        expectedResult.setStoreNo("code");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setStoreName("name");
        expectedResult.setDeviceNo("deviceNo");
        expectedResult.setDeviceGuid("deviceGuid");
        expectedResult.setBinding(false);
        expectedResult.setRegister(false);

        // Configure StoreDeviceMapStruct.toStoreDeviceDTO(...).
        final StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO();
        storeDeviceDTO.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO.setStoreNo("code");
        storeDeviceDTO.setStoreGuid("storeGuid");
        storeDeviceDTO.setStoreName("name");
        storeDeviceDTO.setDeviceNo("deviceNo");
        storeDeviceDTO.setDeviceGuid("deviceGuid");
        storeDeviceDTO.setBinding(false);
        storeDeviceDTO.setRegister(false);
        final StoreDeviceDO storeDeviceDO = new StoreDeviceDO();
        storeDeviceDO.setId(0L);
        storeDeviceDO.setGuid("f39752b9-5a7f-4284-8bd9-9837cf5e494d");
        storeDeviceDO.setStoreGuid("storeGuid");
        storeDeviceDO.setDeviceNo("deviceNo");
        storeDeviceDO.setDeviceGuid("deviceGuid");
        storeDeviceDO.setIsBinding(false);
        storeDeviceDO.setDeviceType(0);
        storeDeviceDO.setSort(0);
        storeDeviceDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setGmtUnbind(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeDeviceDO.setTableGuid("tableGuid");
        storeDeviceDO.setPadOrderType(0);
        when(mockStoreDeviceMapStruct.toStoreDeviceDTO(storeDeviceDO)).thenReturn(storeDeviceDTO);

        // Run the test
        final StoreDeviceDTO result = storeDeviceServiceImplUnderTest.queryDeviceByDeviceId("deviceId");

        // Verify the results
        assertEquals(expectedResult, result);
    }
}
