package com.holderzone.saas.store.organization.service.impl;

import com.holderzone.framework.base.dto.message.MessageDTO;
import com.holderzone.saas.store.organization.service.impl.PushServiceImpl;
import com.holderzone.saas.store.organization.service.remote.MessageClient;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;

@RunWith(MockitoJUnitRunner.class)
public class PushServiceImplTest {

    @Mock
    private MessageClient mockMessageClient;

    @InjectMocks
    private PushServiceImpl pushServiceImplUnderTest;

    @Test
    public void testPushMasterChanged() {
        // Setup
        // Run the test
        pushServiceImplUnderTest.pushMasterChanged("storeGuid", "deviceGuid");

        // Verify the results
        verify(mockMessageClient).sendPrintMessage(any(MessageDTO.class));
    }
}
