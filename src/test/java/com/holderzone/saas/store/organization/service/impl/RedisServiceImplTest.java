package com.holderzone.saas.store.organization.service.impl;

import com.holderzone.saas.store.dto.store.store.StoreAllInfoDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceDTO;
import com.holderzone.saas.store.organization.service.impl.RedisServiceImpl;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.RedisTemplate;

import java.time.LocalTime;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class RedisServiceImplTest {

    @Mock
    private RedisTemplate mockRedisTemplate;

    private RedisServiceImpl redisServiceImplUnderTest;

    @Before
    public void setUp() {
        redisServiceImplUnderTest = new RedisServiceImpl(mockRedisTemplate);
    }

    @Test
    public void testGetStoreMaster() {
        // Setup
        final StoreDeviceDTO expectedResult = new StoreDeviceDTO();
        expectedResult.setEnterpriseGuid("enterpriseGuid");
        expectedResult.setStoreNo("storeNo");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setStoreName("storeName");
        expectedResult.setDeviceNo("deviceNo");

        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        final StoreDeviceDTO result = redisServiceImplUnderTest.getStoreMaster("storeGuid");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testPutStoreMaster() {
        // Setup
        final StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO();
        storeDeviceDTO.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO.setStoreNo("storeNo");
        storeDeviceDTO.setStoreGuid("storeGuid");
        storeDeviceDTO.setStoreName("storeName");
        storeDeviceDTO.setDeviceNo("deviceNo");

        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        redisServiceImplUnderTest.putStoreMaster("storeGuid", storeDeviceDTO);

        // Verify the results
    }

    @Test
    public void testRemoveStoreMaster() {
        // Setup
        // Run the test
        redisServiceImplUnderTest.removeStoreMaster("storeGuid");

        // Verify the results
        verify(mockRedisTemplate).delete("key");
    }

    @Test
    public void testPutStoreAllInfo() {
        // Setup
        final StoreAllInfoDTO storeAllInfoDTO = new StoreAllInfoDTO(LocalTime.of(0, 0, 0), LocalTime.of(0, 0, 0), 0);
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        redisServiceImplUnderTest.putStoreAllInfo(storeAllInfoDTO);

        // Verify the results
    }

    @Test
    public void testGetStoreAllInfo() {
        // Setup
        final StoreAllInfoDTO expectedResult = new StoreAllInfoDTO(LocalTime.of(0, 0, 0), LocalTime.of(0, 0, 0), 0);
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        final StoreAllInfoDTO result = redisServiceImplUnderTest.getStoreAllInfo("storeGuid");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testDeleteStoreAllInfoByStoreGuid() {
        // Setup
        // Run the test
        redisServiceImplUnderTest.deleteStoreAllInfoByStoreGuid("storeGuid");

        // Verify the results
        verify(mockRedisTemplate).delete("key");
    }
}
