package com.holderzone.saas.store.item.controller;

import com.alibaba.fastjson.JSON;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.item.HolderSaasStoreItemApplication;
import com.holderzone.saas.store.item.utils.JsonFileUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.io.UnsupportedEncodingException;

import static com.holderzone.saas.store.dto.common.CommonConstant.USER_INFO;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * <AUTHOR>
 * @date 2023/11/9
 * @description
 */
@Slf4j
@WebAppConfiguration
@ContextConfiguration
@AutoConfigureMockMvc
@RunWith(SpringRunner.class)
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
@SpringBootTest(classes = HolderSaasStoreItemApplication.class)
public class PricePlanControllerTest {

    private static final String USERINFO = "{\"operSubjectGuid\": \"2010121440477930009\"," +
            "\"enterpriseGuid\":" + " \"2009281531195930006\"," +
            "\"enterpriseName\": \"赵氏企业\"," +
            "\"enterpriseNo\": \"********\"," +
            "\"storeGuid\":" + " \"2106221850429620006\"," +
            "\"storeName\": \"交子大道测试门店\"," +
            "\"storeNo\": \"5796807\",\"userGuid\": " +
            "\"6787561298847596545\"," +
            "\"account\": \"196504\"," +
            "\"tel\": \"***********\"," +
            "\"name\": \"靓亮仔\"}\n";

    private static final String PLAN = "/plan";

    private static final String RESPONSE = "response:";

    @Autowired
    private WebApplicationContext wac;

    private MockMvc mockMvc;

    @Before
    public void setupMockMvc() {
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();
    }

    /**
     * 批量下架保存商品菜谱信息
     */
    @Test
    public void saveBatchSoldOutPlanItem() throws UnsupportedEncodingException {
        BaseDTO saveBatchSoldOutPlanItemReqDTO = JSON.parseObject(JsonFileUtil.read("item/saveBatchSoldOutPlanItem.json"),
                BaseDTO.class);
        String saveBatchSoldOutPlanItemJsonString = JSON.toJSONString(saveBatchSoldOutPlanItemReqDTO);
        MvcResult saveBatchSoldOutPlanItemMvcResult = null;
        try {
            saveBatchSoldOutPlanItemMvcResult = mockMvc.perform(post(PLAN + "/save_batch_sold_out_plan_item")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(saveBatchSoldOutPlanItemJsonString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        String contentAsString = saveBatchSoldOutPlanItemMvcResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }
}