package com.holderzone.saas.store.item.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.saas.store.item.mapper.RAttrItemAttrGroupMapper;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;

@RunWith(MockitoJUnitRunner.class)
public class RAttrItemAttrGroupServiceImplTest {

    @Mock
    private RAttrItemAttrGroupMapper mockAttrItemAttrGroupMapper;

    private RAttrItemAttrGroupServiceImpl rAttrItemAttrGroupServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        rAttrItemAttrGroupServiceImplUnderTest = new RAttrItemAttrGroupServiceImpl(mockAttrItemAttrGroupMapper);
    }

    @Test
    public void testDeleteByAttr() {
        // Setup
        // Run the test
        final boolean result = rAttrItemAttrGroupServiceImplUnderTest.deleteByAttr("attrGuid");

        // Verify the results
        assertThat(result).isTrue();
        verify(mockAttrItemAttrGroupMapper).delete(any(LambdaQueryWrapper.class));
    }
}
