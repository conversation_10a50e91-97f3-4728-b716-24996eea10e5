package com.holderzone.saas.store.item.service.impl;

import com.holderzone.framework.rocketmq.common.DefaultRocketMqProducer;
import org.apache.rocketmq.common.message.Message;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ICommonServiceImplTest {

    @Mock
    private DefaultRocketMqProducer mockProducer;
    @Mock
    private RedissonClient mockRedissonSingleClient;

    @InjectMocks
    private ICommonServiceImpl iCommonServiceImplUnderTest;

    @Test
    public void testSendPricePlanToMQ() {
        // Setup
        // Run the test
        iCommonServiceImplUnderTest.sendPricePlanToMQ("value");

        // Verify the results
        verify(mockProducer).sendMessage(any(Message.class));
    }

    @Test
    public void testMd5() {
        assertThat(iCommonServiceImplUnderTest.md5("src")).isEqualTo("");
    }

    @Test
    public void testGetDistributedLock() {
        // Setup
        when(mockRedissonSingleClient.getLock("lockName")).thenReturn(null);

        // Run the test
        final RLock result = iCommonServiceImplUnderTest.getDistributedLock("lockName");

        // Verify the results
    }
}
