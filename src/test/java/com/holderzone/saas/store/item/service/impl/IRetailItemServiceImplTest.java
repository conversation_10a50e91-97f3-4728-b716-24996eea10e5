package com.holderzone.saas.store.item.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.req.ItemQueryReqDTO;
import com.holderzone.saas.store.dto.item.req.ItemRetailSortReqDTO;
import com.holderzone.saas.store.dto.item.req.ItemSortReqDTO;
import com.holderzone.saas.store.dto.item.req.TypeSortReqDTO;
import com.holderzone.saas.store.dto.item.resp.*;
import com.holderzone.saas.store.item.entity.domain.SkuDO;
import com.holderzone.saas.store.item.entity.domain.TypeDO;
import com.holderzone.saas.store.item.helper.ItemHelper;
import com.holderzone.saas.store.item.service.ISkuService;
import com.holderzone.saas.store.item.service.ITypeService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class IRetailItemServiceImplTest {

    @Mock
    private ISkuService mockSkuService;
    @Mock
    private ITypeService mockTypeService;
    @Mock
    private ItemHelper mockItemHelper;

    private IRetailItemServiceImpl iRetailItemServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        iRetailItemServiceImplUnderTest = new IRetailItemServiceImpl(mockSkuService, mockTypeService, mockItemHelper);
    }

    @Test
    public void testSelectItemListForWeb() {
        // Setup
        final ItemQueryReqDTO itemQueryReqDTO = new ItemQueryReqDTO();
        itemQueryReqDTO.setCurrentPage(0L);
        itemQueryReqDTO.setPageSize(0L);
        itemQueryReqDTO.setName("name");
        itemQueryReqDTO.setTypeGuid("typeGuid");
        itemQueryReqDTO.setIsRack(0);
        itemQueryReqDTO.setStoreGuid("storeGuid");
        itemQueryReqDTO.setBrandGuid("brandGuid");

        // Configure ISkuService.list(...).
        final SkuDO skuDO = new SkuDO();
        skuDO.setGuid("bc7675ce-2c34-491f-bba2-5f4d12e9b812");
        skuDO.setBrandGuid("brandGuid");
        skuDO.setStoreGuid("storeGuid");
        skuDO.setItemGuid("itemGuid");
        skuDO.setCode("code");
        skuDO.setSalePrice(new BigDecimal("0.00"));
        skuDO.setIsMemberDiscount(0);
        skuDO.setIsWholeDiscount(0);
        skuDO.setIsRack(0);
        skuDO.setParentGuid("parentGuid");
        final List<SkuDO> skuDOS = Arrays.asList(skuDO);
        when(mockSkuService.list(any(LambdaQueryWrapper.class))).thenReturn(skuDOS);

        // Configure ITypeService.list(...).
        final List<TypeDO> typeDOS = Arrays.asList(
                new TypeDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0,
                        "8ccf941b-2d2f-450d-a70a-a459a0e27db4", "brandGuid", 0, 0, "description", "iconUrl", 0, 0, 0,
                        "pricePlanGuid", 0,1));
        when(mockTypeService.list(any(LambdaQueryWrapper.class))).thenReturn(typeDOS);

        // Configure ItemHelper.mapItem2RelatePkgNum(...).
        final SkuDO skuDO1 = new SkuDO();
        skuDO1.setGuid("bc7675ce-2c34-491f-bba2-5f4d12e9b812");
        skuDO1.setBrandGuid("brandGuid");
        skuDO1.setStoreGuid("storeGuid");
        skuDO1.setItemGuid("itemGuid");
        skuDO1.setCode("code");
        skuDO1.setSalePrice(new BigDecimal("0.00"));
        skuDO1.setIsMemberDiscount(0);
        skuDO1.setIsWholeDiscount(0);
        skuDO1.setIsRack(0);
        skuDO1.setParentGuid("parentGuid");
        final List<SkuDO> skuDOList = Arrays.asList(skuDO1);
        when(mockItemHelper.mapItem2RelatePkgNum(skuDOList)).thenReturn(new HashMap<>());

        // Run the test
        final Page<ItemWebRespDTO> result = iRetailItemServiceImplUnderTest.selectItemListForWeb(itemQueryReqDTO);

        // Verify the results
    }

    @Test
    public void testSelectItemListForWeb_ISkuServiceReturnsNoItems() {
        // Setup
        final ItemQueryReqDTO itemQueryReqDTO = new ItemQueryReqDTO();
        itemQueryReqDTO.setCurrentPage(0L);
        itemQueryReqDTO.setPageSize(0L);
        itemQueryReqDTO.setName("name");
        itemQueryReqDTO.setTypeGuid("typeGuid");
        itemQueryReqDTO.setIsRack(0);
        itemQueryReqDTO.setStoreGuid("storeGuid");
        itemQueryReqDTO.setBrandGuid("brandGuid");

        when(mockSkuService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final Page<ItemWebRespDTO> result = iRetailItemServiceImplUnderTest.selectItemListForWeb(itemQueryReqDTO);

        // Verify the results
    }

    @Test
    public void testSelectItemListForWeb_ITypeServiceReturnsNoItems() {
        // Setup
        final ItemQueryReqDTO itemQueryReqDTO = new ItemQueryReqDTO();
        itemQueryReqDTO.setCurrentPage(0L);
        itemQueryReqDTO.setPageSize(0L);
        itemQueryReqDTO.setName("name");
        itemQueryReqDTO.setTypeGuid("typeGuid");
        itemQueryReqDTO.setIsRack(0);
        itemQueryReqDTO.setStoreGuid("storeGuid");
        itemQueryReqDTO.setBrandGuid("brandGuid");

        // Configure ISkuService.list(...).
        final SkuDO skuDO = new SkuDO();
        skuDO.setGuid("bc7675ce-2c34-491f-bba2-5f4d12e9b812");
        skuDO.setBrandGuid("brandGuid");
        skuDO.setStoreGuid("storeGuid");
        skuDO.setItemGuid("itemGuid");
        skuDO.setCode("code");
        skuDO.setSalePrice(new BigDecimal("0.00"));
        skuDO.setIsMemberDiscount(0);
        skuDO.setIsWholeDiscount(0);
        skuDO.setIsRack(0);
        skuDO.setParentGuid("parentGuid");
        final List<SkuDO> skuDOS = Arrays.asList(skuDO);
        when(mockSkuService.list(any(LambdaQueryWrapper.class))).thenReturn(skuDOS);

        when(mockTypeService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure ItemHelper.mapItem2RelatePkgNum(...).
        final SkuDO skuDO1 = new SkuDO();
        skuDO1.setGuid("bc7675ce-2c34-491f-bba2-5f4d12e9b812");
        skuDO1.setBrandGuid("brandGuid");
        skuDO1.setStoreGuid("storeGuid");
        skuDO1.setItemGuid("itemGuid");
        skuDO1.setCode("code");
        skuDO1.setSalePrice(new BigDecimal("0.00"));
        skuDO1.setIsMemberDiscount(0);
        skuDO1.setIsWholeDiscount(0);
        skuDO1.setIsRack(0);
        skuDO1.setParentGuid("parentGuid");
        final List<SkuDO> skuDOList = Arrays.asList(skuDO1);
        when(mockItemHelper.mapItem2RelatePkgNum(skuDOList)).thenReturn(new HashMap<>());

        // Run the test
        final Page<ItemWebRespDTO> result = iRetailItemServiceImplUnderTest.selectItemListForWeb(itemQueryReqDTO);

        // Verify the results
    }

    @Test
    public void testGetItemSortList() {
        // Setup
        final ItemQueryReqDTO request = new ItemQueryReqDTO();
        request.setCurrentPage(0L);
        request.setPageSize(0L);
        request.setName("name");
        request.setTypeGuid("typeGuid");
        request.setIsRack(0);
        request.setStoreGuid("storeGuid");
        request.setBrandGuid("brandGuid");

        // Run the test
        final Page<ItemSortRespDTO> result = iRetailItemServiceImplUnderTest.getItemSortList(request);

        // Verify the results
    }

    @Test
    public void testRetailUpdateItemSort() {
        // Setup
        final ItemRetailSortReqDTO request = new ItemRetailSortReqDTO(Arrays.asList(
                new TypeSortReqDTO("897b2c38-848c-4bfb-9caf-6462275b0892", "name", 0,
                        Arrays.asList(new ItemSortReqDTO(0, "abdac5f0-a9b1-4864-b855-5ad5c7ea8a7b", "name", 0)),
                        Arrays.asList("value"))));
        when(mockTypeService.retailUpdateTypesort(Arrays.asList(
                new TypeSortReqDTO("897b2c38-848c-4bfb-9caf-6462275b0892", "name", 0,
                        Arrays.asList(new ItemSortReqDTO(0, "abdac5f0-a9b1-4864-b855-5ad5c7ea8a7b", "name", 0)),
                        Arrays.asList("value"))))).thenReturn(false);

        // Run the test
        final Boolean result = iRetailItemServiceImplUnderTest.retailUpdateItemSort(request);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testVerifyThatitemExistsForType() {
        // Setup
        final SingleDataDTO request = new SingleDataDTO("data", Arrays.asList("value"));

        // Run the test
        final boolean result = iRetailItemServiceImplUnderTest.verifyThatitemExistsForType(request);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testGetSortTypeAndItems() {
        // Setup
        final ItemSingleDTO request = new ItemSingleDTO();
        request.setData("data");
        request.setKeywords("keywords");
        request.setModel(0);
        request.setItemQueryType(0);
        request.setStoreGuid("storeGuid");

        final ItemRetailSortRespDTO expectedResult = new ItemRetailSortRespDTO(Arrays.asList(
                new TypeSortRespDTO("0e6f450c-1d34-4253-8247-a12e9b2d5f54", "name", 0,
                        Arrays.asList(new ItemSortRespDTO(0, "c25c70bb-cf07-4bc8-a40c-1b4f6251a84d", "name", 0)))));

        // Configure ITypeService.queryType(...).
        final TypeWebRespDTO typeWebRespDTO = new TypeWebRespDTO();
        typeWebRespDTO.setTypeGuid("typeGuid");
        typeWebRespDTO.setName("name");
        typeWebRespDTO.setBrandGuid("brandGuid");
        typeWebRespDTO.setStoreGuid("storeGuid");
        typeWebRespDTO.setDescription("description");
        final List<TypeWebRespDTO> typeWebRespDTOS = Arrays.asList(typeWebRespDTO);
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("data");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        itemSingleDTO.setStoreGuid("storeGuid");
        when(mockTypeService.queryType(itemSingleDTO)).thenReturn(typeWebRespDTOS);

        // Run the test
        final ItemRetailSortRespDTO result = iRetailItemServiceImplUnderTest.getSortTypeAndItems(request);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetSortTypeAndItems_ITypeServiceReturnsNoItems() {
        // Setup
        final ItemSingleDTO request = new ItemSingleDTO();
        request.setData("data");
        request.setKeywords("keywords");
        request.setModel(0);
        request.setItemQueryType(0);
        request.setStoreGuid("storeGuid");

        final ItemRetailSortRespDTO expectedResult = new ItemRetailSortRespDTO(Arrays.asList(
                new TypeSortRespDTO("0e6f450c-1d34-4253-8247-a12e9b2d5f54", "name", 0,
                        Arrays.asList(new ItemSortRespDTO(0, "c25c70bb-cf07-4bc8-a40c-1b4f6251a84d", "name", 0)))));

        // Configure ITypeService.queryType(...).
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("data");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        itemSingleDTO.setStoreGuid("storeGuid");
        when(mockTypeService.queryType(itemSingleDTO)).thenReturn(Collections.emptyList());

        // Run the test
        final ItemRetailSortRespDTO result = iRetailItemServiceImplUnderTest.getSortTypeAndItems(request);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
