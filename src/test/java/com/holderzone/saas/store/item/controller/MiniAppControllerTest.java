package com.holderzone.saas.store.item.controller;

import com.holderzone.saas.store.dto.item.req.*;
import com.holderzone.saas.store.dto.item.resp.CartCheckResult;
import com.holderzone.saas.store.dto.item.resp.ItemShoppingCartResp;
import com.holderzone.saas.store.dto.item.resp.ItemStockResp;
import com.holderzone.saas.store.item.service.IMiniAppService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.math.BigDecimal;
import java.util.Arrays;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

@RunWith(SpringRunner.class)
@WebMvcTest(MiniAppController.class)
public class MiniAppControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private IMiniAppService mockMiniAppService;

    @Test
    public void testQueryShoppingCardForMiniApp() throws Exception {
        // Setup
        // Configure IMiniAppService.queryShoppingCardForMiniApp(...).
        final ItemShoppingCartResp itemShoppingCartResp = new ItemShoppingCartResp();
        final SingleShoppingCart singleShoppingCart = new SingleShoppingCart();
        singleShoppingCart.setItemGuid("itemGuid");
        singleShoppingCart.setIsOpenStock(0);
        singleShoppingCart.setStock(new BigDecimal("0.00"));
        singleShoppingCart.setTotalStock(new BigDecimal("0.00"));
        itemShoppingCartResp.setData(Arrays.asList(singleShoppingCart));
        final ItemShoppingCardRequest itemShoppingCardRequest = new ItemShoppingCardRequest();
        itemShoppingCardRequest.setEnterpriseGuid("enterpriseGuid");
        itemShoppingCardRequest.setStoreGuid("storeGuid");
        itemShoppingCardRequest.setSkuGuids(Arrays.asList("value"));
        when(mockMiniAppService.queryShoppingCardForMiniApp(itemShoppingCardRequest)).thenReturn(itemShoppingCartResp);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/mini_app/query_shopping_card_for_mini_app")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testCheckShoppingCardForMiniApp() throws Exception {
        // Setup
        // Configure IMiniAppService.checkShoppingCardForMiniApp(...).
        final ItemShoppingCartCheckResponse itemShoppingCartCheckResponse = new ItemShoppingCartCheckResponse();
        itemShoppingCartCheckResponse.setCode(0);
        itemShoppingCartCheckResponse.setMessage("message");
        final CartCheckResult cartCheckResult = new CartCheckResult();
        cartCheckResult.setSkuGuid("skuGuid");
        cartCheckResult.setSuccess(false);
        itemShoppingCartCheckResponse.setData(Arrays.asList(cartCheckResult));
        final ItemShoppingCartCheckRequest itemShoppingCartCheckRequest = new ItemShoppingCartCheckRequest();
        itemShoppingCartCheckRequest.setStoreGuid("storeGuid");
        itemShoppingCartCheckRequest.setIsTakeaway(false);
        final SingleShoppingCartCheck singleShoppingCartCheck = new SingleShoppingCartCheck();
        singleShoppingCartCheck.setSkuGuid("skuGuid");
        singleShoppingCartCheck.setSkuName("skuName");
        itemShoppingCartCheckRequest.setData(Arrays.asList(singleShoppingCartCheck));
        when(mockMiniAppService.checkShoppingCardForMiniApp(itemShoppingCartCheckRequest))
                .thenReturn(itemShoppingCartCheckResponse);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/mini_app/check_shopping_card_for_mini_app")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testDescStock() throws Exception {
        // Setup
        // Configure IMiniAppService.descStock(...).
        final ItemShoppingCartCheckRequest itemShoppingCardRequest = new ItemShoppingCartCheckRequest();
        itemShoppingCardRequest.setStoreGuid("storeGuid");
        itemShoppingCardRequest.setIsTakeaway(false);
        final SingleShoppingCartCheck singleShoppingCartCheck = new SingleShoppingCartCheck();
        singleShoppingCartCheck.setSkuGuid("skuGuid");
        singleShoppingCartCheck.setSkuName("skuName");
        itemShoppingCardRequest.setData(Arrays.asList(singleShoppingCartCheck));
        when(mockMiniAppService.descStock(itemShoppingCardRequest)).thenReturn(new ItemStockResp("code", "message"));

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/mini_app/desc_stock")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }
}
