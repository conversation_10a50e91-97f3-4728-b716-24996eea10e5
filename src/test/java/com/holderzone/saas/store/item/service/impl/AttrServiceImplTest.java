package com.holderzone.saas.store.item.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.req.AttrReqDTO;
import com.holderzone.saas.store.dto.item.req.AttrSaveReqDTO;
import com.holderzone.saas.store.dto.item.resp.AttrRespDTO;
import com.holderzone.saas.store.item.entity.domain.*;
import com.holderzone.saas.store.item.helper.EventPushHelper;
import com.holderzone.saas.store.item.mapper.AttrMapper;
import com.holderzone.saas.store.item.mapper.RTypeAttrMapper;
import com.holderzone.saas.store.item.service.*;
import com.holderzone.saas.store.item.util.DynamicHelper;
import com.holderzone.saas.store.item.util.PushUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.RedisTemplate;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.function.BiConsumer;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class AttrServiceImplTest {

    @Mock
    private AttrMapper mockAttrMapper;
    @Mock
    private IRTypeAttrService mockTypeAttrService;
    @Mock
    private RTypeAttrMapper mockTypeAttrMapper;
    @Mock
    private DynamicHelper mockDynamicHelper;
    @Mock
    private IItemService mockItemService;
    @Mock
    private IRItemAttrGroupService mockItemAttrGroupService;
    @Mock
    private IRAttrItemAttrGroupService mockAttrItemAttrGroupService;
    @Mock
    private IAttrGroupService mockAttrGroupService;
    @Mock
    private EventPushHelper mockEventPushHelper;
    @Mock
    private PushUtils mockPushUtils;
    @Mock
    private RedisTemplate mockRedisTemplate;

    private AttrServiceImpl attrServiceImplUnderTest;

    @Before
    public void setUp() {
        attrServiceImplUnderTest = new AttrServiceImpl(mockAttrMapper, mockTypeAttrService, mockTypeAttrMapper,
                mockDynamicHelper, mockItemService, mockItemAttrGroupService, mockAttrItemAttrGroupService,
                mockAttrGroupService, mockEventPushHelper, mockPushUtils, mockRedisTemplate);
    }

    @Test
    public void testUpdate() {
        // Setup
        final AttrSaveReqDTO attrSaveReqDTO = new AttrSaveReqDTO();
        attrSaveReqDTO.setFrom(0);
        attrSaveReqDTO.setAttrGuid("attrGuid");
        attrSaveReqDTO.setName("name");
        attrSaveReqDTO.setPrice(new BigDecimal("0.00"));
        attrSaveReqDTO.setTypeGuidList(Arrays.asList("value"));

        // Configure AttrMapper.selectById(...).
        final AttrDO attrDO = new AttrDO();
        attrDO.setStoreGuid("storeGuid");
        attrDO.setParentGuid("parentGuid");
        attrDO.setName("name");
        attrDO.setIsDelete(0);
        attrDO.setGuid("attrGuid");
        attrDO.setBrandGuid("brandGuid");
        attrDO.setAttrGroupGuid("attrGroupGuid");
        attrDO.setIsDefault(0);
        attrDO.setAttrFrom(0);
        when(mockAttrMapper.selectById("attrGuid")).thenReturn(attrDO);

        when(mockAttrMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(0);
        when(mockTypeAttrService.listTypeGuidByAttr("attrGuid")).thenReturn(Arrays.asList("value"));

        // Configure IAttrGroupService.getById(...).
        final AttrGroupDO attrGroupDO = new AttrGroupDO();
        attrGroupDO.setStoreGuid("storeGuid");
        attrGroupDO.setParentGuid("parentGuid");
        attrGroupDO.setName("name");
        attrGroupDO.setGuid("attrGroupGuid");
        attrGroupDO.setBrandGuid("brandGuid");
        attrGroupDO.setIsRequired(0);
        attrGroupDO.setIsMultiChoice(0);
        when(mockAttrGroupService.getById("attrGroupGuid")).thenReturn(attrGroupDO);

        // Configure IItemService.list(...).
        final List<ItemDO> itemDOS = Arrays.asList(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "typeGuid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0, "pinyin", "nameAbbr", "description",
                        "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code", 0, 0, 0, "videoUrls", 0, false));
        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(itemDOS);

        // Configure IRItemAttrGroupService.list(...).
        final RItemAttrGroupDO rItemAttrGroupDO = new RItemAttrGroupDO();
        rItemAttrGroupDO.setIsDelete(0);
        rItemAttrGroupDO.setGuid("guid");
        rItemAttrGroupDO.setItemGuid("guid");
        rItemAttrGroupDO.setAttrGroupGuid("attrGroupGuid");
        rItemAttrGroupDO.setIsRequired(0);
        rItemAttrGroupDO.setIsMultiChoice(0);
        rItemAttrGroupDO.setWithDefault(0);
        final List<RItemAttrGroupDO> rItemAttrGroupDOS = Arrays.asList(rItemAttrGroupDO);
        when(mockItemAttrGroupService.list(any(LambdaQueryWrapper.class))).thenReturn(rItemAttrGroupDOS);

        // Configure IRAttrItemAttrGroupService.list(...).
        final RAttrItemAttrGroupDO rAttrItemAttrGroupDO = new RAttrItemAttrGroupDO();
        rAttrItemAttrGroupDO.setIsDelete(0);
        rAttrItemAttrGroupDO.setGuid("d7b44c74-4ae5-480f-a8d1-328f9edcb34a");
        rAttrItemAttrGroupDO.setAttrGuid("attrGuid");
        rAttrItemAttrGroupDO.setItemAttrGroupGuid("guid");
        rAttrItemAttrGroupDO.setIsDefault(0);
        final List<RAttrItemAttrGroupDO> rAttrItemAttrGroupDOS = Arrays.asList(rAttrItemAttrGroupDO);
        when(mockAttrItemAttrGroupService.list(any(LambdaQueryWrapper.class))).thenReturn(rAttrItemAttrGroupDOS);

        // Run the test
        final boolean result = attrServiceImplUnderTest.update(attrSaveReqDTO);

        // Verify the results
        assertThat(result).isTrue();

        // Confirm AttrMapper.update(...).
        final AttrDO entity = new AttrDO();
        entity.setStoreGuid("storeGuid");
        entity.setParentGuid("parentGuid");
        entity.setName("name");
        entity.setIsDelete(0);
        entity.setGuid("attrGuid");
        entity.setBrandGuid("brandGuid");
        entity.setAttrGroupGuid("attrGroupGuid");
        entity.setIsDefault(0);
        entity.setAttrFrom(0);
        verify(mockAttrMapper).update(eq(entity), any(LambdaQueryWrapper.class));
        verify(mockTypeAttrService).removeBind(Arrays.asList("value"), "attrGuid");
        verify(mockTypeAttrService).save(Arrays.asList("value"), "attrGuid");

        // Confirm IRItemAttrGroupService.saveBatch(...).
        final RItemAttrGroupDO rItemAttrGroupDO1 = new RItemAttrGroupDO();
        rItemAttrGroupDO1.setIsDelete(0);
        rItemAttrGroupDO1.setGuid("guid");
        rItemAttrGroupDO1.setItemGuid("guid");
        rItemAttrGroupDO1.setAttrGroupGuid("attrGroupGuid");
        rItemAttrGroupDO1.setIsRequired(0);
        rItemAttrGroupDO1.setIsMultiChoice(0);
        rItemAttrGroupDO1.setWithDefault(0);
        final List<RItemAttrGroupDO> entityList = Arrays.asList(rItemAttrGroupDO1);
        verify(mockItemAttrGroupService).saveBatch(entityList);

        // Confirm IRAttrItemAttrGroupService.saveBatch(...).
        final RAttrItemAttrGroupDO rAttrItemAttrGroupDO1 = new RAttrItemAttrGroupDO();
        rAttrItemAttrGroupDO1.setIsDelete(0);
        rAttrItemAttrGroupDO1.setGuid("d7b44c74-4ae5-480f-a8d1-328f9edcb34a");
        rAttrItemAttrGroupDO1.setAttrGuid("attrGuid");
        rAttrItemAttrGroupDO1.setItemAttrGroupGuid("guid");
        rAttrItemAttrGroupDO1.setIsDefault(0);
        final List<RAttrItemAttrGroupDO> entityList1 = Arrays.asList(rAttrItemAttrGroupDO1);
        verify(mockAttrItemAttrGroupService).saveBatch(entityList1);
        verify(mockItemService).updateBatchById(Arrays.asList(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "typeGuid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0, "pinyin", "nameAbbr", "description",
                        "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code", 0, 0, 0, "videoUrls", 0, false)));
    }

    @Test
    public void testUpdate_AttrMapperSelectByIdReturnsNull() {
        // Setup
        final AttrSaveReqDTO attrSaveReqDTO = new AttrSaveReqDTO();
        attrSaveReqDTO.setFrom(0);
        attrSaveReqDTO.setAttrGuid("attrGuid");
        attrSaveReqDTO.setName("name");
        attrSaveReqDTO.setPrice(new BigDecimal("0.00"));
        attrSaveReqDTO.setTypeGuidList(Arrays.asList("value"));

        when(mockAttrMapper.selectById("attrGuid")).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> attrServiceImplUnderTest.update(attrSaveReqDTO)).isInstanceOf(BusinessException.class);
    }

    @Test
    public void testUpdate_IRTypeAttrServiceListTypeGuidByAttrReturnsNoItems() {
        // Setup
        final AttrSaveReqDTO attrSaveReqDTO = new AttrSaveReqDTO();
        attrSaveReqDTO.setFrom(0);
        attrSaveReqDTO.setAttrGuid("attrGuid");
        attrSaveReqDTO.setName("name");
        attrSaveReqDTO.setPrice(new BigDecimal("0.00"));
        attrSaveReqDTO.setTypeGuidList(Arrays.asList("value"));

        // Configure AttrMapper.selectById(...).
        final AttrDO attrDO = new AttrDO();
        attrDO.setStoreGuid("storeGuid");
        attrDO.setParentGuid("parentGuid");
        attrDO.setName("name");
        attrDO.setIsDelete(0);
        attrDO.setGuid("attrGuid");
        attrDO.setBrandGuid("brandGuid");
        attrDO.setAttrGroupGuid("attrGroupGuid");
        attrDO.setIsDefault(0);
        attrDO.setAttrFrom(0);
        when(mockAttrMapper.selectById("attrGuid")).thenReturn(attrDO);

        when(mockAttrMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(0);
        when(mockTypeAttrService.listTypeGuidByAttr("attrGuid")).thenReturn(Collections.emptyList());

        // Configure IAttrGroupService.getById(...).
        final AttrGroupDO attrGroupDO = new AttrGroupDO();
        attrGroupDO.setStoreGuid("storeGuid");
        attrGroupDO.setParentGuid("parentGuid");
        attrGroupDO.setName("name");
        attrGroupDO.setGuid("attrGroupGuid");
        attrGroupDO.setBrandGuid("brandGuid");
        attrGroupDO.setIsRequired(0);
        attrGroupDO.setIsMultiChoice(0);
        when(mockAttrGroupService.getById("attrGroupGuid")).thenReturn(attrGroupDO);

        // Configure IItemService.list(...).
        final List<ItemDO> itemDOS = Arrays.asList(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "typeGuid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0, "pinyin", "nameAbbr", "description",
                        "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code", 0, 0, 0, "videoUrls", 0, false));
        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(itemDOS);

        // Configure IRItemAttrGroupService.list(...).
        final RItemAttrGroupDO rItemAttrGroupDO = new RItemAttrGroupDO();
        rItemAttrGroupDO.setIsDelete(0);
        rItemAttrGroupDO.setGuid("guid");
        rItemAttrGroupDO.setItemGuid("guid");
        rItemAttrGroupDO.setAttrGroupGuid("attrGroupGuid");
        rItemAttrGroupDO.setIsRequired(0);
        rItemAttrGroupDO.setIsMultiChoice(0);
        rItemAttrGroupDO.setWithDefault(0);
        final List<RItemAttrGroupDO> rItemAttrGroupDOS = Arrays.asList(rItemAttrGroupDO);
        when(mockItemAttrGroupService.list(any(LambdaQueryWrapper.class))).thenReturn(rItemAttrGroupDOS);

        // Configure IRAttrItemAttrGroupService.list(...).
        final RAttrItemAttrGroupDO rAttrItemAttrGroupDO = new RAttrItemAttrGroupDO();
        rAttrItemAttrGroupDO.setIsDelete(0);
        rAttrItemAttrGroupDO.setGuid("d7b44c74-4ae5-480f-a8d1-328f9edcb34a");
        rAttrItemAttrGroupDO.setAttrGuid("attrGuid");
        rAttrItemAttrGroupDO.setItemAttrGroupGuid("guid");
        rAttrItemAttrGroupDO.setIsDefault(0);
        final List<RAttrItemAttrGroupDO> rAttrItemAttrGroupDOS = Arrays.asList(rAttrItemAttrGroupDO);
        when(mockAttrItemAttrGroupService.list(any(LambdaQueryWrapper.class))).thenReturn(rAttrItemAttrGroupDOS);

        // Run the test
        final boolean result = attrServiceImplUnderTest.update(attrSaveReqDTO);

        // Verify the results
        assertThat(result).isTrue();

        // Confirm AttrMapper.update(...).
        final AttrDO entity = new AttrDO();
        entity.setStoreGuid("storeGuid");
        entity.setParentGuid("parentGuid");
        entity.setName("name");
        entity.setIsDelete(0);
        entity.setGuid("attrGuid");
        entity.setBrandGuid("brandGuid");
        entity.setAttrGroupGuid("attrGroupGuid");
        entity.setIsDefault(0);
        entity.setAttrFrom(0);
        verify(mockAttrMapper).update(eq(entity), any(LambdaQueryWrapper.class));
        verify(mockTypeAttrService).removeBind(Arrays.asList("value"), "attrGuid");
        verify(mockTypeAttrService).save(Arrays.asList("value"), "attrGuid");

        // Confirm IRItemAttrGroupService.saveBatch(...).
        final RItemAttrGroupDO rItemAttrGroupDO1 = new RItemAttrGroupDO();
        rItemAttrGroupDO1.setIsDelete(0);
        rItemAttrGroupDO1.setGuid("guid");
        rItemAttrGroupDO1.setItemGuid("guid");
        rItemAttrGroupDO1.setAttrGroupGuid("attrGroupGuid");
        rItemAttrGroupDO1.setIsRequired(0);
        rItemAttrGroupDO1.setIsMultiChoice(0);
        rItemAttrGroupDO1.setWithDefault(0);
        final List<RItemAttrGroupDO> entityList = Arrays.asList(rItemAttrGroupDO1);
        verify(mockItemAttrGroupService).saveBatch(entityList);

        // Confirm IRAttrItemAttrGroupService.saveBatch(...).
        final RAttrItemAttrGroupDO rAttrItemAttrGroupDO1 = new RAttrItemAttrGroupDO();
        rAttrItemAttrGroupDO1.setIsDelete(0);
        rAttrItemAttrGroupDO1.setGuid("d7b44c74-4ae5-480f-a8d1-328f9edcb34a");
        rAttrItemAttrGroupDO1.setAttrGuid("attrGuid");
        rAttrItemAttrGroupDO1.setItemAttrGroupGuid("guid");
        rAttrItemAttrGroupDO1.setIsDefault(0);
        final List<RAttrItemAttrGroupDO> entityList1 = Arrays.asList(rAttrItemAttrGroupDO1);
        verify(mockAttrItemAttrGroupService).saveBatch(entityList1);
        verify(mockItemService).updateBatchById(Arrays.asList(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "typeGuid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0, "pinyin", "nameAbbr", "description",
                        "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code", 0, 0, 0, "videoUrls", 0, false)));
    }

    @Test
    public void testUpdate_IItemServiceListReturnsNoItems() {
        // Setup
        final AttrSaveReqDTO attrSaveReqDTO = new AttrSaveReqDTO();
        attrSaveReqDTO.setFrom(0);
        attrSaveReqDTO.setAttrGuid("attrGuid");
        attrSaveReqDTO.setName("name");
        attrSaveReqDTO.setPrice(new BigDecimal("0.00"));
        attrSaveReqDTO.setTypeGuidList(Arrays.asList("value"));

        // Configure AttrMapper.selectById(...).
        final AttrDO attrDO = new AttrDO();
        attrDO.setStoreGuid("storeGuid");
        attrDO.setParentGuid("parentGuid");
        attrDO.setName("name");
        attrDO.setIsDelete(0);
        attrDO.setGuid("attrGuid");
        attrDO.setBrandGuid("brandGuid");
        attrDO.setAttrGroupGuid("attrGroupGuid");
        attrDO.setIsDefault(0);
        attrDO.setAttrFrom(0);
        when(mockAttrMapper.selectById("attrGuid")).thenReturn(attrDO);

        when(mockAttrMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(0);
        when(mockTypeAttrService.listTypeGuidByAttr("attrGuid")).thenReturn(Arrays.asList("value"));

        // Configure IAttrGroupService.getById(...).
        final AttrGroupDO attrGroupDO = new AttrGroupDO();
        attrGroupDO.setStoreGuid("storeGuid");
        attrGroupDO.setParentGuid("parentGuid");
        attrGroupDO.setName("name");
        attrGroupDO.setGuid("attrGroupGuid");
        attrGroupDO.setBrandGuid("brandGuid");
        attrGroupDO.setIsRequired(0);
        attrGroupDO.setIsMultiChoice(0);
        when(mockAttrGroupService.getById("attrGroupGuid")).thenReturn(attrGroupDO);

        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final boolean result = attrServiceImplUnderTest.update(attrSaveReqDTO);

        // Verify the results
        assertThat(result).isTrue();

        // Confirm AttrMapper.update(...).
        final AttrDO entity = new AttrDO();
        entity.setStoreGuid("storeGuid");
        entity.setParentGuid("parentGuid");
        entity.setName("name");
        entity.setIsDelete(0);
        entity.setGuid("attrGuid");
        entity.setBrandGuid("brandGuid");
        entity.setAttrGroupGuid("attrGroupGuid");
        entity.setIsDefault(0);
        entity.setAttrFrom(0);
        verify(mockAttrMapper).update(eq(entity), any(LambdaQueryWrapper.class));
        verify(mockTypeAttrService).removeBind(Arrays.asList("value"), "attrGuid");
        verify(mockTypeAttrService).save(Arrays.asList("value"), "attrGuid");
    }

    @Test
    public void testUpdate_IRItemAttrGroupServiceListReturnsNoItems() {
        // Setup
        final AttrSaveReqDTO attrSaveReqDTO = new AttrSaveReqDTO();
        attrSaveReqDTO.setFrom(0);
        attrSaveReqDTO.setAttrGuid("attrGuid");
        attrSaveReqDTO.setName("name");
        attrSaveReqDTO.setPrice(new BigDecimal("0.00"));
        attrSaveReqDTO.setTypeGuidList(Arrays.asList("value"));

        // Configure AttrMapper.selectById(...).
        final AttrDO attrDO = new AttrDO();
        attrDO.setStoreGuid("storeGuid");
        attrDO.setParentGuid("parentGuid");
        attrDO.setName("name");
        attrDO.setIsDelete(0);
        attrDO.setGuid("attrGuid");
        attrDO.setBrandGuid("brandGuid");
        attrDO.setAttrGroupGuid("attrGroupGuid");
        attrDO.setIsDefault(0);
        attrDO.setAttrFrom(0);
        when(mockAttrMapper.selectById("attrGuid")).thenReturn(attrDO);

        when(mockAttrMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(0);
        when(mockTypeAttrService.listTypeGuidByAttr("attrGuid")).thenReturn(Arrays.asList("value"));

        // Configure IAttrGroupService.getById(...).
        final AttrGroupDO attrGroupDO = new AttrGroupDO();
        attrGroupDO.setStoreGuid("storeGuid");
        attrGroupDO.setParentGuid("parentGuid");
        attrGroupDO.setName("name");
        attrGroupDO.setGuid("attrGroupGuid");
        attrGroupDO.setBrandGuid("brandGuid");
        attrGroupDO.setIsRequired(0);
        attrGroupDO.setIsMultiChoice(0);
        when(mockAttrGroupService.getById("attrGroupGuid")).thenReturn(attrGroupDO);

        // Configure IItemService.list(...).
        final List<ItemDO> itemDOS = Arrays.asList(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "typeGuid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0, "pinyin", "nameAbbr", "description",
                        "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code", 0, 0, 0, "videoUrls", 0, false));
        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(itemDOS);

        when(mockItemAttrGroupService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final boolean result = attrServiceImplUnderTest.update(attrSaveReqDTO);

        // Verify the results
        assertThat(result).isTrue();

        // Confirm AttrMapper.update(...).
        final AttrDO entity = new AttrDO();
        entity.setStoreGuid("storeGuid");
        entity.setParentGuid("parentGuid");
        entity.setName("name");
        entity.setIsDelete(0);
        entity.setGuid("attrGuid");
        entity.setBrandGuid("brandGuid");
        entity.setAttrGroupGuid("attrGroupGuid");
        entity.setIsDefault(0);
        entity.setAttrFrom(0);
        verify(mockAttrMapper).update(eq(entity), any(LambdaQueryWrapper.class));
        verify(mockTypeAttrService).removeBind(Arrays.asList("value"), "attrGuid");
        verify(mockTypeAttrService).save(Arrays.asList("value"), "attrGuid");

        // Confirm IRItemAttrGroupService.saveBatch(...).
        final RItemAttrGroupDO rItemAttrGroupDO = new RItemAttrGroupDO();
        rItemAttrGroupDO.setIsDelete(0);
        rItemAttrGroupDO.setGuid("guid");
        rItemAttrGroupDO.setItemGuid("guid");
        rItemAttrGroupDO.setAttrGroupGuid("attrGroupGuid");
        rItemAttrGroupDO.setIsRequired(0);
        rItemAttrGroupDO.setIsMultiChoice(0);
        rItemAttrGroupDO.setWithDefault(0);
        final List<RItemAttrGroupDO> entityList = Arrays.asList(rItemAttrGroupDO);
        verify(mockItemAttrGroupService).saveBatch(entityList);

        // Confirm IRAttrItemAttrGroupService.saveBatch(...).
        final RAttrItemAttrGroupDO rAttrItemAttrGroupDO = new RAttrItemAttrGroupDO();
        rAttrItemAttrGroupDO.setIsDelete(0);
        rAttrItemAttrGroupDO.setGuid("d7b44c74-4ae5-480f-a8d1-328f9edcb34a");
        rAttrItemAttrGroupDO.setAttrGuid("attrGuid");
        rAttrItemAttrGroupDO.setItemAttrGroupGuid("guid");
        rAttrItemAttrGroupDO.setIsDefault(0);
        final List<RAttrItemAttrGroupDO> entityList1 = Arrays.asList(rAttrItemAttrGroupDO);
        verify(mockAttrItemAttrGroupService).saveBatch(entityList1);
        verify(mockItemService).updateBatchById(Arrays.asList(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "typeGuid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0, "pinyin", "nameAbbr", "description",
                        "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code", 0, 0, 0, "videoUrls", 0, false)));
    }

    @Test
    public void testUpdate_IRAttrItemAttrGroupServiceListReturnsNoItems() {
        // Setup
        final AttrSaveReqDTO attrSaveReqDTO = new AttrSaveReqDTO();
        attrSaveReqDTO.setFrom(0);
        attrSaveReqDTO.setAttrGuid("attrGuid");
        attrSaveReqDTO.setName("name");
        attrSaveReqDTO.setPrice(new BigDecimal("0.00"));
        attrSaveReqDTO.setTypeGuidList(Arrays.asList("value"));

        // Configure AttrMapper.selectById(...).
        final AttrDO attrDO = new AttrDO();
        attrDO.setStoreGuid("storeGuid");
        attrDO.setParentGuid("parentGuid");
        attrDO.setName("name");
        attrDO.setIsDelete(0);
        attrDO.setGuid("attrGuid");
        attrDO.setBrandGuid("brandGuid");
        attrDO.setAttrGroupGuid("attrGroupGuid");
        attrDO.setIsDefault(0);
        attrDO.setAttrFrom(0);
        when(mockAttrMapper.selectById("attrGuid")).thenReturn(attrDO);

        when(mockAttrMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(0);
        when(mockTypeAttrService.listTypeGuidByAttr("attrGuid")).thenReturn(Arrays.asList("value"));

        // Configure IAttrGroupService.getById(...).
        final AttrGroupDO attrGroupDO = new AttrGroupDO();
        attrGroupDO.setStoreGuid("storeGuid");
        attrGroupDO.setParentGuid("parentGuid");
        attrGroupDO.setName("name");
        attrGroupDO.setGuid("attrGroupGuid");
        attrGroupDO.setBrandGuid("brandGuid");
        attrGroupDO.setIsRequired(0);
        attrGroupDO.setIsMultiChoice(0);
        when(mockAttrGroupService.getById("attrGroupGuid")).thenReturn(attrGroupDO);

        // Configure IItemService.list(...).
        final List<ItemDO> itemDOS = Arrays.asList(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "typeGuid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0, "pinyin", "nameAbbr", "description",
                        "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code", 0, 0, 0, "videoUrls", 0, false));
        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(itemDOS);

        // Configure IRItemAttrGroupService.list(...).
        final RItemAttrGroupDO rItemAttrGroupDO = new RItemAttrGroupDO();
        rItemAttrGroupDO.setIsDelete(0);
        rItemAttrGroupDO.setGuid("guid");
        rItemAttrGroupDO.setItemGuid("guid");
        rItemAttrGroupDO.setAttrGroupGuid("attrGroupGuid");
        rItemAttrGroupDO.setIsRequired(0);
        rItemAttrGroupDO.setIsMultiChoice(0);
        rItemAttrGroupDO.setWithDefault(0);
        final List<RItemAttrGroupDO> rItemAttrGroupDOS = Arrays.asList(rItemAttrGroupDO);
        when(mockItemAttrGroupService.list(any(LambdaQueryWrapper.class))).thenReturn(rItemAttrGroupDOS);

        when(mockAttrItemAttrGroupService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final boolean result = attrServiceImplUnderTest.update(attrSaveReqDTO);

        // Verify the results
        assertThat(result).isTrue();

        // Confirm AttrMapper.update(...).
        final AttrDO entity = new AttrDO();
        entity.setStoreGuid("storeGuid");
        entity.setParentGuid("parentGuid");
        entity.setName("name");
        entity.setIsDelete(0);
        entity.setGuid("attrGuid");
        entity.setBrandGuid("brandGuid");
        entity.setAttrGroupGuid("attrGroupGuid");
        entity.setIsDefault(0);
        entity.setAttrFrom(0);
        verify(mockAttrMapper).update(eq(entity), any(LambdaQueryWrapper.class));
        verify(mockTypeAttrService).removeBind(Arrays.asList("value"), "attrGuid");
        verify(mockTypeAttrService).save(Arrays.asList("value"), "attrGuid");

        // Confirm IRItemAttrGroupService.saveBatch(...).
        final RItemAttrGroupDO rItemAttrGroupDO1 = new RItemAttrGroupDO();
        rItemAttrGroupDO1.setIsDelete(0);
        rItemAttrGroupDO1.setGuid("guid");
        rItemAttrGroupDO1.setItemGuid("guid");
        rItemAttrGroupDO1.setAttrGroupGuid("attrGroupGuid");
        rItemAttrGroupDO1.setIsRequired(0);
        rItemAttrGroupDO1.setIsMultiChoice(0);
        rItemAttrGroupDO1.setWithDefault(0);
        final List<RItemAttrGroupDO> entityList = Arrays.asList(rItemAttrGroupDO1);
        verify(mockItemAttrGroupService).saveBatch(entityList);

        // Confirm IRAttrItemAttrGroupService.saveBatch(...).
        final RAttrItemAttrGroupDO rAttrItemAttrGroupDO = new RAttrItemAttrGroupDO();
        rAttrItemAttrGroupDO.setIsDelete(0);
        rAttrItemAttrGroupDO.setGuid("d7b44c74-4ae5-480f-a8d1-328f9edcb34a");
        rAttrItemAttrGroupDO.setAttrGuid("attrGuid");
        rAttrItemAttrGroupDO.setItemAttrGroupGuid("guid");
        rAttrItemAttrGroupDO.setIsDefault(0);
        final List<RAttrItemAttrGroupDO> entityList1 = Arrays.asList(rAttrItemAttrGroupDO);
        verify(mockAttrItemAttrGroupService).saveBatch(entityList1);
        verify(mockItemService).updateBatchById(Arrays.asList(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "typeGuid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0, "pinyin", "nameAbbr", "description",
                        "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code", 0, 0, 0, "videoUrls", 0, false)));
    }

    @Test
    public void testDeleteOrUpdatePushAttrAfterDeletePushRelation() {
        // Setup
        final AttrDO attrDO = new AttrDO();
        attrDO.setStoreGuid("storeGuid");
        attrDO.setParentGuid("parentGuid");
        attrDO.setName("name");
        attrDO.setIsDelete(0);
        attrDO.setGuid("attrGuid");
        attrDO.setBrandGuid("brandGuid");
        attrDO.setAttrGroupGuid("attrGroupGuid");
        attrDO.setIsDefault(0);
        attrDO.setAttrFrom(0);
        final List<AttrDO> pushAttrDOList = Arrays.asList(attrDO);

        // Configure IRAttrItemAttrGroupService.list(...).
        final RAttrItemAttrGroupDO rAttrItemAttrGroupDO = new RAttrItemAttrGroupDO();
        rAttrItemAttrGroupDO.setIsDelete(0);
        rAttrItemAttrGroupDO.setGuid("d7b44c74-4ae5-480f-a8d1-328f9edcb34a");
        rAttrItemAttrGroupDO.setAttrGuid("attrGuid");
        rAttrItemAttrGroupDO.setItemAttrGroupGuid("guid");
        rAttrItemAttrGroupDO.setIsDefault(0);
        final List<RAttrItemAttrGroupDO> rAttrItemAttrGroupDOS = Arrays.asList(rAttrItemAttrGroupDO);
        when(mockAttrItemAttrGroupService.list(any(LambdaQueryWrapper.class))).thenReturn(rAttrItemAttrGroupDOS);

        // Run the test
        attrServiceImplUnderTest.deleteOrUpdatePushAttrAfterDeletePushRelation(pushAttrDOList);

        // Verify the results
        // Confirm PushUtils.fixFieldsFromPush2SelfCreate(...).
        final AttrDO attrDO1 = new AttrDO();
        attrDO1.setStoreGuid("storeGuid");
        attrDO1.setParentGuid("parentGuid");
        attrDO1.setName("name");
        attrDO1.setIsDelete(0);
        attrDO1.setGuid("attrGuid");
        attrDO1.setBrandGuid("brandGuid");
        attrDO1.setAttrGroupGuid("attrGroupGuid");
        attrDO1.setIsDefault(0);
        attrDO1.setAttrFrom(0);
        final List<AttrDO> withItemTypeDOList = Arrays.asList(attrDO1);
        verify(mockPushUtils).fixFieldsFromPush2SelfCreate(eq(withItemTypeDOList), any(BiConsumer.class),
                any(BiConsumer.class));
        verify(mockTypeAttrService).remove(any(LambdaQueryWrapper.class));
    }

    @Test
    public void testDeleteOrUpdatePushAttrAfterDeletePushRelation_IRAttrItemAttrGroupServiceReturnsNoItems() {
        // Setup
        final AttrDO attrDO = new AttrDO();
        attrDO.setStoreGuid("storeGuid");
        attrDO.setParentGuid("parentGuid");
        attrDO.setName("name");
        attrDO.setIsDelete(0);
        attrDO.setGuid("attrGuid");
        attrDO.setBrandGuid("brandGuid");
        attrDO.setAttrGroupGuid("attrGroupGuid");
        attrDO.setIsDefault(0);
        attrDO.setAttrFrom(0);
        final List<AttrDO> pushAttrDOList = Arrays.asList(attrDO);
        when(mockAttrItemAttrGroupService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        attrServiceImplUnderTest.deleteOrUpdatePushAttrAfterDeletePushRelation(pushAttrDOList);

        // Verify the results
        verify(mockTypeAttrService).remove(any(LambdaQueryWrapper.class));
    }

    @Test
    public void testDeletePushAttrRelate() {
        // Setup
        final RAttrItemAttrGroupDO rAttrItemAttrGroupDO = new RAttrItemAttrGroupDO();
        rAttrItemAttrGroupDO.setIsDelete(0);
        rAttrItemAttrGroupDO.setGuid("d7b44c74-4ae5-480f-a8d1-328f9edcb34a");
        rAttrItemAttrGroupDO.setAttrGuid("attrGuid");
        rAttrItemAttrGroupDO.setItemAttrGroupGuid("guid");
        rAttrItemAttrGroupDO.setIsDefault(0);
        final List<RAttrItemAttrGroupDO> toDelAttrRelationDOList = Arrays.asList(rAttrItemAttrGroupDO);
        final AttrDO attrDO = new AttrDO();
        attrDO.setStoreGuid("storeGuid");
        attrDO.setParentGuid("parentGuid");
        attrDO.setName("name");
        attrDO.setIsDelete(0);
        attrDO.setGuid("attrGuid");
        attrDO.setBrandGuid("brandGuid");
        attrDO.setAttrGroupGuid("attrGroupGuid");
        attrDO.setIsDefault(0);
        attrDO.setAttrFrom(0);
        final List<AttrDO> pushAttrDOList = Arrays.asList(attrDO);

        // Configure IItemService.list(...).
        final List<ItemDO> itemDOS = Arrays.asList(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "typeGuid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0, "pinyin", "nameAbbr", "description",
                        "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code", 0, 0, 0, "videoUrls", 0, false));
        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(itemDOS);

        // Configure IRItemAttrGroupService.list(...).
        final RItemAttrGroupDO rItemAttrGroupDO = new RItemAttrGroupDO();
        rItemAttrGroupDO.setIsDelete(0);
        rItemAttrGroupDO.setGuid("guid");
        rItemAttrGroupDO.setItemGuid("guid");
        rItemAttrGroupDO.setAttrGroupGuid("attrGroupGuid");
        rItemAttrGroupDO.setIsRequired(0);
        rItemAttrGroupDO.setIsMultiChoice(0);
        rItemAttrGroupDO.setWithDefault(0);
        final List<RItemAttrGroupDO> rItemAttrGroupDOS = Arrays.asList(rItemAttrGroupDO);
        when(mockItemAttrGroupService.list(any(LambdaQueryWrapper.class))).thenReturn(rItemAttrGroupDOS);

        // Configure IRAttrItemAttrGroupService.list(...).
        final RAttrItemAttrGroupDO rAttrItemAttrGroupDO1 = new RAttrItemAttrGroupDO();
        rAttrItemAttrGroupDO1.setIsDelete(0);
        rAttrItemAttrGroupDO1.setGuid("d7b44c74-4ae5-480f-a8d1-328f9edcb34a");
        rAttrItemAttrGroupDO1.setAttrGuid("attrGuid");
        rAttrItemAttrGroupDO1.setItemAttrGroupGuid("guid");
        rAttrItemAttrGroupDO1.setIsDefault(0);
        final List<RAttrItemAttrGroupDO> rAttrItemAttrGroupDOS = Arrays.asList(rAttrItemAttrGroupDO1);
        when(mockAttrItemAttrGroupService.list(any(LambdaQueryWrapper.class))).thenReturn(rAttrItemAttrGroupDOS);

        // Run the test
        attrServiceImplUnderTest.deletePushAttrRelate(toDelAttrRelationDOList, pushAttrDOList);

        // Verify the results
        verify(mockAttrItemAttrGroupService).removeByIds(Arrays.asList("value"));
        verify(mockItemAttrGroupService).removeByIds(Arrays.asList("value"));
    }

    @Test
    public void testDeletePushAttrRelate_IItemServiceReturnsNoItems() {
        // Setup
        final RAttrItemAttrGroupDO rAttrItemAttrGroupDO = new RAttrItemAttrGroupDO();
        rAttrItemAttrGroupDO.setIsDelete(0);
        rAttrItemAttrGroupDO.setGuid("d7b44c74-4ae5-480f-a8d1-328f9edcb34a");
        rAttrItemAttrGroupDO.setAttrGuid("attrGuid");
        rAttrItemAttrGroupDO.setItemAttrGroupGuid("guid");
        rAttrItemAttrGroupDO.setIsDefault(0);
        final List<RAttrItemAttrGroupDO> toDelAttrRelationDOList = Arrays.asList(rAttrItemAttrGroupDO);
        final AttrDO attrDO = new AttrDO();
        attrDO.setStoreGuid("storeGuid");
        attrDO.setParentGuid("parentGuid");
        attrDO.setName("name");
        attrDO.setIsDelete(0);
        attrDO.setGuid("attrGuid");
        attrDO.setBrandGuid("brandGuid");
        attrDO.setAttrGroupGuid("attrGroupGuid");
        attrDO.setIsDefault(0);
        attrDO.setAttrFrom(0);
        final List<AttrDO> pushAttrDOList = Arrays.asList(attrDO);
        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure IRAttrItemAttrGroupService.list(...).
        final RAttrItemAttrGroupDO rAttrItemAttrGroupDO1 = new RAttrItemAttrGroupDO();
        rAttrItemAttrGroupDO1.setIsDelete(0);
        rAttrItemAttrGroupDO1.setGuid("d7b44c74-4ae5-480f-a8d1-328f9edcb34a");
        rAttrItemAttrGroupDO1.setAttrGuid("attrGuid");
        rAttrItemAttrGroupDO1.setItemAttrGroupGuid("guid");
        rAttrItemAttrGroupDO1.setIsDefault(0);
        final List<RAttrItemAttrGroupDO> rAttrItemAttrGroupDOS = Arrays.asList(rAttrItemAttrGroupDO1);
        when(mockAttrItemAttrGroupService.list(any(LambdaQueryWrapper.class))).thenReturn(rAttrItemAttrGroupDOS);

        // Run the test
        attrServiceImplUnderTest.deletePushAttrRelate(toDelAttrRelationDOList, pushAttrDOList);

        // Verify the results
        verify(mockAttrItemAttrGroupService).removeByIds(Arrays.asList("value"));
        verify(mockItemAttrGroupService).removeByIds(Arrays.asList("value"));
    }

    @Test
    public void testDeletePushAttrRelate_IRItemAttrGroupServiceListReturnsNoItems() {
        // Setup
        final RAttrItemAttrGroupDO rAttrItemAttrGroupDO = new RAttrItemAttrGroupDO();
        rAttrItemAttrGroupDO.setIsDelete(0);
        rAttrItemAttrGroupDO.setGuid("d7b44c74-4ae5-480f-a8d1-328f9edcb34a");
        rAttrItemAttrGroupDO.setAttrGuid("attrGuid");
        rAttrItemAttrGroupDO.setItemAttrGroupGuid("guid");
        rAttrItemAttrGroupDO.setIsDefault(0);
        final List<RAttrItemAttrGroupDO> toDelAttrRelationDOList = Arrays.asList(rAttrItemAttrGroupDO);
        final AttrDO attrDO = new AttrDO();
        attrDO.setStoreGuid("storeGuid");
        attrDO.setParentGuid("parentGuid");
        attrDO.setName("name");
        attrDO.setIsDelete(0);
        attrDO.setGuid("attrGuid");
        attrDO.setBrandGuid("brandGuid");
        attrDO.setAttrGroupGuid("attrGroupGuid");
        attrDO.setIsDefault(0);
        attrDO.setAttrFrom(0);
        final List<AttrDO> pushAttrDOList = Arrays.asList(attrDO);

        // Configure IItemService.list(...).
        final List<ItemDO> itemDOS = Arrays.asList(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "typeGuid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0, "pinyin", "nameAbbr", "description",
                        "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code", 0, 0, 0, "videoUrls", 0, false));
        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(itemDOS);

        when(mockItemAttrGroupService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure IRAttrItemAttrGroupService.list(...).
        final RAttrItemAttrGroupDO rAttrItemAttrGroupDO1 = new RAttrItemAttrGroupDO();
        rAttrItemAttrGroupDO1.setIsDelete(0);
        rAttrItemAttrGroupDO1.setGuid("d7b44c74-4ae5-480f-a8d1-328f9edcb34a");
        rAttrItemAttrGroupDO1.setAttrGuid("attrGuid");
        rAttrItemAttrGroupDO1.setItemAttrGroupGuid("guid");
        rAttrItemAttrGroupDO1.setIsDefault(0);
        final List<RAttrItemAttrGroupDO> rAttrItemAttrGroupDOS = Arrays.asList(rAttrItemAttrGroupDO1);
        when(mockAttrItemAttrGroupService.list(any(LambdaQueryWrapper.class))).thenReturn(rAttrItemAttrGroupDOS);

        // Run the test
        attrServiceImplUnderTest.deletePushAttrRelate(toDelAttrRelationDOList, pushAttrDOList);

        // Verify the results
        verify(mockAttrItemAttrGroupService).removeByIds(Arrays.asList("value"));
        verify(mockItemAttrGroupService).removeByIds(Arrays.asList("value"));
    }

    @Test
    public void testDeletePushAttrRelate_IRAttrItemAttrGroupServiceListReturnsNoItems() {
        // Setup
        final RAttrItemAttrGroupDO rAttrItemAttrGroupDO = new RAttrItemAttrGroupDO();
        rAttrItemAttrGroupDO.setIsDelete(0);
        rAttrItemAttrGroupDO.setGuid("d7b44c74-4ae5-480f-a8d1-328f9edcb34a");
        rAttrItemAttrGroupDO.setAttrGuid("attrGuid");
        rAttrItemAttrGroupDO.setItemAttrGroupGuid("guid");
        rAttrItemAttrGroupDO.setIsDefault(0);
        final List<RAttrItemAttrGroupDO> toDelAttrRelationDOList = Arrays.asList(rAttrItemAttrGroupDO);
        final AttrDO attrDO = new AttrDO();
        attrDO.setStoreGuid("storeGuid");
        attrDO.setParentGuid("parentGuid");
        attrDO.setName("name");
        attrDO.setIsDelete(0);
        attrDO.setGuid("attrGuid");
        attrDO.setBrandGuid("brandGuid");
        attrDO.setAttrGroupGuid("attrGroupGuid");
        attrDO.setIsDefault(0);
        attrDO.setAttrFrom(0);
        final List<AttrDO> pushAttrDOList = Arrays.asList(attrDO);

        // Configure IItemService.list(...).
        final List<ItemDO> itemDOS = Arrays.asList(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "typeGuid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0, "pinyin", "nameAbbr", "description",
                        "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code", 0, 0, 0, "videoUrls", 0, false));
        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(itemDOS);

        // Configure IRItemAttrGroupService.list(...).
        final RItemAttrGroupDO rItemAttrGroupDO = new RItemAttrGroupDO();
        rItemAttrGroupDO.setIsDelete(0);
        rItemAttrGroupDO.setGuid("guid");
        rItemAttrGroupDO.setItemGuid("guid");
        rItemAttrGroupDO.setAttrGroupGuid("attrGroupGuid");
        rItemAttrGroupDO.setIsRequired(0);
        rItemAttrGroupDO.setIsMultiChoice(0);
        rItemAttrGroupDO.setWithDefault(0);
        final List<RItemAttrGroupDO> rItemAttrGroupDOS = Arrays.asList(rItemAttrGroupDO);
        when(mockItemAttrGroupService.list(any(LambdaQueryWrapper.class))).thenReturn(rItemAttrGroupDOS);

        when(mockAttrItemAttrGroupService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        attrServiceImplUnderTest.deletePushAttrRelate(toDelAttrRelationDOList, pushAttrDOList);

        // Verify the results
        verify(mockAttrItemAttrGroupService).removeByIds(Arrays.asList("value"));
        verify(mockItemAttrGroupService).removeByIds(Arrays.asList("value"));
    }

    @Test
    public void testDeleteByGuid() {
        // Setup
        // Configure AttrMapper.selectByGuid(...).
        final AttrDO attrDO = new AttrDO();
        attrDO.setStoreGuid("storeGuid");
        attrDO.setParentGuid("parentGuid");
        attrDO.setName("name");
        attrDO.setIsDelete(0);
        attrDO.setGuid("attrGuid");
        attrDO.setBrandGuid("brandGuid");
        attrDO.setAttrGroupGuid("attrGroupGuid");
        attrDO.setIsDefault(0);
        attrDO.setAttrFrom(0);
        when(mockAttrMapper.selectByGuid("attrGuid")).thenReturn(attrDO);

        // Configure IRAttrItemAttrGroupService.list(...).
        final RAttrItemAttrGroupDO rAttrItemAttrGroupDO = new RAttrItemAttrGroupDO();
        rAttrItemAttrGroupDO.setIsDelete(0);
        rAttrItemAttrGroupDO.setGuid("d7b44c74-4ae5-480f-a8d1-328f9edcb34a");
        rAttrItemAttrGroupDO.setAttrGuid("attrGuid");
        rAttrItemAttrGroupDO.setItemAttrGroupGuid("guid");
        rAttrItemAttrGroupDO.setIsDefault(0);
        final List<RAttrItemAttrGroupDO> rAttrItemAttrGroupDOS = Arrays.asList(rAttrItemAttrGroupDO);
        when(mockAttrItemAttrGroupService.list(any(LambdaQueryWrapper.class))).thenReturn(rAttrItemAttrGroupDOS);

        // Configure IRItemAttrGroupService.listByIds(...).
        final RItemAttrGroupDO rItemAttrGroupDO = new RItemAttrGroupDO();
        rItemAttrGroupDO.setIsDelete(0);
        rItemAttrGroupDO.setGuid("guid");
        rItemAttrGroupDO.setItemGuid("guid");
        rItemAttrGroupDO.setAttrGroupGuid("attrGroupGuid");
        rItemAttrGroupDO.setIsRequired(0);
        rItemAttrGroupDO.setIsMultiChoice(0);
        rItemAttrGroupDO.setWithDefault(0);
        final Collection<RItemAttrGroupDO> rItemAttrGroupDOS = Arrays.asList(rItemAttrGroupDO);
        when(mockItemAttrGroupService.listByIds(Arrays.asList("value"))).thenReturn(rItemAttrGroupDOS);

        // Configure IItemService.list(...).
        final List<ItemDO> itemDOS = Arrays.asList(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "typeGuid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0, "pinyin", "nameAbbr", "description",
                        "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code", 0, 0, 0, "videoUrls", 0, false));
        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(itemDOS);

        // Configure IRItemAttrGroupService.list(...).
        final RItemAttrGroupDO rItemAttrGroupDO1 = new RItemAttrGroupDO();
        rItemAttrGroupDO1.setIsDelete(0);
        rItemAttrGroupDO1.setGuid("guid");
        rItemAttrGroupDO1.setItemGuid("guid");
        rItemAttrGroupDO1.setAttrGroupGuid("attrGroupGuid");
        rItemAttrGroupDO1.setIsRequired(0);
        rItemAttrGroupDO1.setIsMultiChoice(0);
        rItemAttrGroupDO1.setWithDefault(0);
        final List<RItemAttrGroupDO> rItemAttrGroupDOS1 = Arrays.asList(rItemAttrGroupDO1);
        when(mockItemAttrGroupService.list(any(LambdaQueryWrapper.class))).thenReturn(rItemAttrGroupDOS1);

        // Run the test
        final boolean result = attrServiceImplUnderTest.deleteByGuid("attrGuid");

        // Verify the results
        assertThat(result).isTrue();
        verify(mockTypeAttrService).deleteByAttr("attrGuid");
        verify(mockAttrItemAttrGroupService).deleteByAttr("attrGuid");
        verify(mockItemAttrGroupService).removeByIds(Arrays.asList("value"));
        verify(mockItemService).updateBatchById(Arrays.asList(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "typeGuid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0, "pinyin", "nameAbbr", "description",
                        "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code", 0, 0, 0, "videoUrls", 0, false)), 0);
        verify(mockAttrMapper).deleteById("attrGuid");
        verify(mockAttrItemAttrGroupService).removeByIds(Arrays.asList("value"));

        // Confirm PushUtils.fixFieldsFromPush2SelfCreate(...).
        final AttrDO attrDO1 = new AttrDO();
        attrDO1.setStoreGuid("storeGuid");
        attrDO1.setParentGuid("parentGuid");
        attrDO1.setName("name");
        attrDO1.setIsDelete(0);
        attrDO1.setGuid("attrGuid");
        attrDO1.setBrandGuid("brandGuid");
        attrDO1.setAttrGroupGuid("attrGroupGuid");
        attrDO1.setIsDefault(0);
        attrDO1.setAttrFrom(0);
        final List<AttrDO> withItemTypeDOList = Arrays.asList(attrDO1);
        verify(mockPushUtils).fixFieldsFromPush2SelfCreate(eq(withItemTypeDOList), any(BiConsumer.class),
                any(BiConsumer.class));
        verify(mockTypeAttrService).remove(any(LambdaQueryWrapper.class));
    }

    @Test
    public void testDeleteByGuid_IRAttrItemAttrGroupServiceListReturnsNoItems() {
        // Setup
        // Configure AttrMapper.selectByGuid(...).
        final AttrDO attrDO = new AttrDO();
        attrDO.setStoreGuid("storeGuid");
        attrDO.setParentGuid("parentGuid");
        attrDO.setName("name");
        attrDO.setIsDelete(0);
        attrDO.setGuid("attrGuid");
        attrDO.setBrandGuid("brandGuid");
        attrDO.setAttrGroupGuid("attrGroupGuid");
        attrDO.setIsDefault(0);
        attrDO.setAttrFrom(0);
        when(mockAttrMapper.selectByGuid("attrGuid")).thenReturn(attrDO);

        when(mockAttrItemAttrGroupService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure IItemService.list(...).
        final List<ItemDO> itemDOS = Arrays.asList(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "typeGuid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0, "pinyin", "nameAbbr", "description",
                        "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code", 0, 0, 0, "videoUrls", 0, false));
        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(itemDOS);

        // Configure IRItemAttrGroupService.list(...).
        final RItemAttrGroupDO rItemAttrGroupDO = new RItemAttrGroupDO();
        rItemAttrGroupDO.setIsDelete(0);
        rItemAttrGroupDO.setGuid("guid");
        rItemAttrGroupDO.setItemGuid("guid");
        rItemAttrGroupDO.setAttrGroupGuid("attrGroupGuid");
        rItemAttrGroupDO.setIsRequired(0);
        rItemAttrGroupDO.setIsMultiChoice(0);
        rItemAttrGroupDO.setWithDefault(0);
        final List<RItemAttrGroupDO> rItemAttrGroupDOS = Arrays.asList(rItemAttrGroupDO);
        when(mockItemAttrGroupService.list(any(LambdaQueryWrapper.class))).thenReturn(rItemAttrGroupDOS);

        // Run the test
        final boolean result = attrServiceImplUnderTest.deleteByGuid("attrGuid");

        // Verify the results
        assertThat(result).isTrue();
        verify(mockTypeAttrService).deleteByAttr("attrGuid");
        verify(mockItemAttrGroupService).removeByIds(Arrays.asList("value"));
        verify(mockAttrMapper).deleteById("attrGuid");
        verify(mockAttrItemAttrGroupService).removeByIds(Arrays.asList("value"));

        // Confirm PushUtils.fixFieldsFromPush2SelfCreate(...).
        final AttrDO attrDO1 = new AttrDO();
        attrDO1.setStoreGuid("storeGuid");
        attrDO1.setParentGuid("parentGuid");
        attrDO1.setName("name");
        attrDO1.setIsDelete(0);
        attrDO1.setGuid("attrGuid");
        attrDO1.setBrandGuid("brandGuid");
        attrDO1.setAttrGroupGuid("attrGroupGuid");
        attrDO1.setIsDefault(0);
        attrDO1.setAttrFrom(0);
        final List<AttrDO> withItemTypeDOList = Arrays.asList(attrDO1);
        verify(mockPushUtils).fixFieldsFromPush2SelfCreate(eq(withItemTypeDOList), any(BiConsumer.class),
                any(BiConsumer.class));
        verify(mockTypeAttrService).remove(any(LambdaQueryWrapper.class));
    }

    @Test
    public void testDeleteByGuid_IRItemAttrGroupServiceListByIdsReturnsNoItems() {
        // Setup
        // Configure AttrMapper.selectByGuid(...).
        final AttrDO attrDO = new AttrDO();
        attrDO.setStoreGuid("storeGuid");
        attrDO.setParentGuid("parentGuid");
        attrDO.setName("name");
        attrDO.setIsDelete(0);
        attrDO.setGuid("attrGuid");
        attrDO.setBrandGuid("brandGuid");
        attrDO.setAttrGroupGuid("attrGroupGuid");
        attrDO.setIsDefault(0);
        attrDO.setAttrFrom(0);
        when(mockAttrMapper.selectByGuid("attrGuid")).thenReturn(attrDO);

        // Configure IRAttrItemAttrGroupService.list(...).
        final RAttrItemAttrGroupDO rAttrItemAttrGroupDO = new RAttrItemAttrGroupDO();
        rAttrItemAttrGroupDO.setIsDelete(0);
        rAttrItemAttrGroupDO.setGuid("d7b44c74-4ae5-480f-a8d1-328f9edcb34a");
        rAttrItemAttrGroupDO.setAttrGuid("attrGuid");
        rAttrItemAttrGroupDO.setItemAttrGroupGuid("guid");
        rAttrItemAttrGroupDO.setIsDefault(0);
        final List<RAttrItemAttrGroupDO> rAttrItemAttrGroupDOS = Arrays.asList(rAttrItemAttrGroupDO);
        when(mockAttrItemAttrGroupService.list(any(LambdaQueryWrapper.class))).thenReturn(rAttrItemAttrGroupDOS);

        when(mockItemAttrGroupService.listByIds(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Configure IItemService.list(...).
        final List<ItemDO> itemDOS = Arrays.asList(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "typeGuid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0, "pinyin", "nameAbbr", "description",
                        "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code", 0, 0, 0, "videoUrls", 0, false));
        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(itemDOS);

        // Configure IRItemAttrGroupService.list(...).
        final RItemAttrGroupDO rItemAttrGroupDO = new RItemAttrGroupDO();
        rItemAttrGroupDO.setIsDelete(0);
        rItemAttrGroupDO.setGuid("guid");
        rItemAttrGroupDO.setItemGuid("guid");
        rItemAttrGroupDO.setAttrGroupGuid("attrGroupGuid");
        rItemAttrGroupDO.setIsRequired(0);
        rItemAttrGroupDO.setIsMultiChoice(0);
        rItemAttrGroupDO.setWithDefault(0);
        final List<RItemAttrGroupDO> rItemAttrGroupDOS = Arrays.asList(rItemAttrGroupDO);
        when(mockItemAttrGroupService.list(any(LambdaQueryWrapper.class))).thenReturn(rItemAttrGroupDOS);

        // Run the test
        final boolean result = attrServiceImplUnderTest.deleteByGuid("attrGuid");

        // Verify the results
        assertThat(result).isTrue();
        verify(mockTypeAttrService).deleteByAttr("attrGuid");
        verify(mockAttrItemAttrGroupService).deleteByAttr("attrGuid");
        verify(mockItemAttrGroupService).removeByIds(Arrays.asList("value"));
        verify(mockAttrMapper).deleteById("attrGuid");
        verify(mockAttrItemAttrGroupService).removeByIds(Arrays.asList("value"));

        // Confirm PushUtils.fixFieldsFromPush2SelfCreate(...).
        final AttrDO attrDO1 = new AttrDO();
        attrDO1.setStoreGuid("storeGuid");
        attrDO1.setParentGuid("parentGuid");
        attrDO1.setName("name");
        attrDO1.setIsDelete(0);
        attrDO1.setGuid("attrGuid");
        attrDO1.setBrandGuid("brandGuid");
        attrDO1.setAttrGroupGuid("attrGroupGuid");
        attrDO1.setIsDefault(0);
        attrDO1.setAttrFrom(0);
        final List<AttrDO> withItemTypeDOList = Arrays.asList(attrDO1);
        verify(mockPushUtils).fixFieldsFromPush2SelfCreate(eq(withItemTypeDOList), any(BiConsumer.class),
                any(BiConsumer.class));
        verify(mockTypeAttrService).remove(any(LambdaQueryWrapper.class));
    }

    @Test
    public void testDeleteByGuid_IItemServiceListReturnsNoItems() {
        // Setup
        // Configure AttrMapper.selectByGuid(...).
        final AttrDO attrDO = new AttrDO();
        attrDO.setStoreGuid("storeGuid");
        attrDO.setParentGuid("parentGuid");
        attrDO.setName("name");
        attrDO.setIsDelete(0);
        attrDO.setGuid("attrGuid");
        attrDO.setBrandGuid("brandGuid");
        attrDO.setAttrGroupGuid("attrGroupGuid");
        attrDO.setIsDefault(0);
        attrDO.setAttrFrom(0);
        when(mockAttrMapper.selectByGuid("attrGuid")).thenReturn(attrDO);

        // Configure IRAttrItemAttrGroupService.list(...).
        final RAttrItemAttrGroupDO rAttrItemAttrGroupDO = new RAttrItemAttrGroupDO();
        rAttrItemAttrGroupDO.setIsDelete(0);
        rAttrItemAttrGroupDO.setGuid("d7b44c74-4ae5-480f-a8d1-328f9edcb34a");
        rAttrItemAttrGroupDO.setAttrGuid("attrGuid");
        rAttrItemAttrGroupDO.setItemAttrGroupGuid("guid");
        rAttrItemAttrGroupDO.setIsDefault(0);
        final List<RAttrItemAttrGroupDO> rAttrItemAttrGroupDOS = Arrays.asList(rAttrItemAttrGroupDO);
        when(mockAttrItemAttrGroupService.list(any(LambdaQueryWrapper.class))).thenReturn(rAttrItemAttrGroupDOS);

        // Configure IRItemAttrGroupService.listByIds(...).
        final RItemAttrGroupDO rItemAttrGroupDO = new RItemAttrGroupDO();
        rItemAttrGroupDO.setIsDelete(0);
        rItemAttrGroupDO.setGuid("guid");
        rItemAttrGroupDO.setItemGuid("guid");
        rItemAttrGroupDO.setAttrGroupGuid("attrGroupGuid");
        rItemAttrGroupDO.setIsRequired(0);
        rItemAttrGroupDO.setIsMultiChoice(0);
        rItemAttrGroupDO.setWithDefault(0);
        final Collection<RItemAttrGroupDO> rItemAttrGroupDOS = Arrays.asList(rItemAttrGroupDO);
        when(mockItemAttrGroupService.listByIds(Arrays.asList("value"))).thenReturn(rItemAttrGroupDOS);

        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure IRItemAttrGroupService.list(...).
        final RItemAttrGroupDO rItemAttrGroupDO1 = new RItemAttrGroupDO();
        rItemAttrGroupDO1.setIsDelete(0);
        rItemAttrGroupDO1.setGuid("guid");
        rItemAttrGroupDO1.setItemGuid("guid");
        rItemAttrGroupDO1.setAttrGroupGuid("attrGroupGuid");
        rItemAttrGroupDO1.setIsRequired(0);
        rItemAttrGroupDO1.setIsMultiChoice(0);
        rItemAttrGroupDO1.setWithDefault(0);
        final List<RItemAttrGroupDO> rItemAttrGroupDOS1 = Arrays.asList(rItemAttrGroupDO1);
        when(mockItemAttrGroupService.list(any(LambdaQueryWrapper.class))).thenReturn(rItemAttrGroupDOS1);

        // Run the test
        final boolean result = attrServiceImplUnderTest.deleteByGuid("attrGuid");

        // Verify the results
        assertThat(result).isTrue();
        verify(mockTypeAttrService).deleteByAttr("attrGuid");
        verify(mockAttrItemAttrGroupService).deleteByAttr("attrGuid");
        verify(mockItemAttrGroupService).removeByIds(Arrays.asList("value"));
        verify(mockItemService).updateBatchById(Arrays.asList(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "typeGuid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0, "pinyin", "nameAbbr", "description",
                        "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code", 0, 0, 0, "videoUrls", 0, false)), 0);
        verify(mockAttrMapper).deleteById("attrGuid");
        verify(mockAttrItemAttrGroupService).removeByIds(Arrays.asList("value"));

        // Confirm PushUtils.fixFieldsFromPush2SelfCreate(...).
        final AttrDO attrDO1 = new AttrDO();
        attrDO1.setStoreGuid("storeGuid");
        attrDO1.setParentGuid("parentGuid");
        attrDO1.setName("name");
        attrDO1.setIsDelete(0);
        attrDO1.setGuid("attrGuid");
        attrDO1.setBrandGuid("brandGuid");
        attrDO1.setAttrGroupGuid("attrGroupGuid");
        attrDO1.setIsDefault(0);
        attrDO1.setAttrFrom(0);
        final List<AttrDO> withItemTypeDOList = Arrays.asList(attrDO1);
        verify(mockPushUtils).fixFieldsFromPush2SelfCreate(eq(withItemTypeDOList), any(BiConsumer.class),
                any(BiConsumer.class));
        verify(mockTypeAttrService).remove(any(LambdaQueryWrapper.class));
    }

    @Test
    public void testDeleteByGuid_IRItemAttrGroupServiceListReturnsNoItems() {
        // Setup
        // Configure AttrMapper.selectByGuid(...).
        final AttrDO attrDO = new AttrDO();
        attrDO.setStoreGuid("storeGuid");
        attrDO.setParentGuid("parentGuid");
        attrDO.setName("name");
        attrDO.setIsDelete(0);
        attrDO.setGuid("attrGuid");
        attrDO.setBrandGuid("brandGuid");
        attrDO.setAttrGroupGuid("attrGroupGuid");
        attrDO.setIsDefault(0);
        attrDO.setAttrFrom(0);
        when(mockAttrMapper.selectByGuid("attrGuid")).thenReturn(attrDO);

        // Configure IRAttrItemAttrGroupService.list(...).
        final RAttrItemAttrGroupDO rAttrItemAttrGroupDO = new RAttrItemAttrGroupDO();
        rAttrItemAttrGroupDO.setIsDelete(0);
        rAttrItemAttrGroupDO.setGuid("d7b44c74-4ae5-480f-a8d1-328f9edcb34a");
        rAttrItemAttrGroupDO.setAttrGuid("attrGuid");
        rAttrItemAttrGroupDO.setItemAttrGroupGuid("guid");
        rAttrItemAttrGroupDO.setIsDefault(0);
        final List<RAttrItemAttrGroupDO> rAttrItemAttrGroupDOS = Arrays.asList(rAttrItemAttrGroupDO);
        when(mockAttrItemAttrGroupService.list(any(LambdaQueryWrapper.class))).thenReturn(rAttrItemAttrGroupDOS);

        // Configure IRItemAttrGroupService.listByIds(...).
        final RItemAttrGroupDO rItemAttrGroupDO = new RItemAttrGroupDO();
        rItemAttrGroupDO.setIsDelete(0);
        rItemAttrGroupDO.setGuid("guid");
        rItemAttrGroupDO.setItemGuid("guid");
        rItemAttrGroupDO.setAttrGroupGuid("attrGroupGuid");
        rItemAttrGroupDO.setIsRequired(0);
        rItemAttrGroupDO.setIsMultiChoice(0);
        rItemAttrGroupDO.setWithDefault(0);
        final Collection<RItemAttrGroupDO> rItemAttrGroupDOS = Arrays.asList(rItemAttrGroupDO);
        when(mockItemAttrGroupService.listByIds(Arrays.asList("value"))).thenReturn(rItemAttrGroupDOS);

        // Configure IItemService.list(...).
        final List<ItemDO> itemDOS = Arrays.asList(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "typeGuid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0, "pinyin", "nameAbbr", "description",
                        "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code", 0, 0, 0, "videoUrls", 0, false));
        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(itemDOS);

        when(mockItemAttrGroupService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final boolean result = attrServiceImplUnderTest.deleteByGuid("attrGuid");

        // Verify the results
        assertThat(result).isTrue();
        verify(mockTypeAttrService).deleteByAttr("attrGuid");
        verify(mockAttrItemAttrGroupService).deleteByAttr("attrGuid");
        verify(mockItemAttrGroupService).removeByIds(Arrays.asList("value"));
        verify(mockItemService).updateBatchById(Arrays.asList(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "typeGuid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0, "pinyin", "nameAbbr", "description",
                        "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code", 0, 0, 0, "videoUrls", 0, false)), 0);
        verify(mockAttrMapper).deleteById("attrGuid");
        verify(mockAttrItemAttrGroupService).removeByIds(Arrays.asList("value"));

        // Confirm PushUtils.fixFieldsFromPush2SelfCreate(...).
        final AttrDO attrDO1 = new AttrDO();
        attrDO1.setStoreGuid("storeGuid");
        attrDO1.setParentGuid("parentGuid");
        attrDO1.setName("name");
        attrDO1.setIsDelete(0);
        attrDO1.setGuid("attrGuid");
        attrDO1.setBrandGuid("brandGuid");
        attrDO1.setAttrGroupGuid("attrGroupGuid");
        attrDO1.setIsDefault(0);
        attrDO1.setAttrFrom(0);
        final List<AttrDO> withItemTypeDOList = Arrays.asList(attrDO1);
        verify(mockPushUtils).fixFieldsFromPush2SelfCreate(eq(withItemTypeDOList), any(BiConsumer.class),
                any(BiConsumer.class));
        verify(mockTypeAttrService).remove(any(LambdaQueryWrapper.class));
    }

    @Test
    public void testListAttrByGroup1() {
        // Setup
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setFrom(0);
        itemSingleDTO.setData("data");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);

        final AttrRespDTO attrRespDTO = new AttrRespDTO();
        attrRespDTO.setAttrGroupGuid("attrGroupGuid");
        attrRespDTO.setAttrGuid("attrGuid");
        attrRespDTO.setName("name");
        attrRespDTO.setPrice(new BigDecimal("0.00"));
        attrRespDTO.setIsDefault(0);
        final List<AttrRespDTO> expectedResult = Arrays.asList(attrRespDTO);

        // Configure AttrMapper.selectList(...).
        final AttrDO attrDO = new AttrDO();
        attrDO.setStoreGuid("storeGuid");
        attrDO.setParentGuid("parentGuid");
        attrDO.setName("name");
        attrDO.setIsDelete(0);
        attrDO.setGuid("attrGuid");
        attrDO.setBrandGuid("brandGuid");
        attrDO.setAttrGroupGuid("attrGroupGuid");
        attrDO.setIsDefault(0);
        attrDO.setAttrFrom(0);
        final List<AttrDO> attrDOS = Arrays.asList(attrDO);
        when(mockAttrMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(attrDOS);

        // Run the test
        final List<AttrRespDTO> result = attrServiceImplUnderTest.listAttrByGroup(itemSingleDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testListAttrByGroup1_AttrMapperReturnsNoItems() {
        // Setup
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setFrom(0);
        itemSingleDTO.setData("data");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);

        when(mockAttrMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<AttrRespDTO> result = attrServiceImplUnderTest.listAttrByGroup(itemSingleDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testDeleteByGroup() {
        // Setup
        when(mockAttrMapper.delete(any(LambdaQueryWrapper.class))).thenReturn(0);

        // Run the test
        final boolean result = attrServiceImplUnderTest.deleteByGroup("attrGroupGuid");

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testRemovePushAttr() {
        // Setup
        // Configure IRAttrItemAttrGroupService.list(...).
        final RAttrItemAttrGroupDO rAttrItemAttrGroupDO = new RAttrItemAttrGroupDO();
        rAttrItemAttrGroupDO.setIsDelete(0);
        rAttrItemAttrGroupDO.setGuid("d7b44c74-4ae5-480f-a8d1-328f9edcb34a");
        rAttrItemAttrGroupDO.setAttrGuid("attrGuid");
        rAttrItemAttrGroupDO.setItemAttrGroupGuid("guid");
        rAttrItemAttrGroupDO.setIsDefault(0);
        final List<RAttrItemAttrGroupDO> rAttrItemAttrGroupDOS = Arrays.asList(rAttrItemAttrGroupDO);
        when(mockAttrItemAttrGroupService.list(any(LambdaQueryWrapper.class))).thenReturn(rAttrItemAttrGroupDOS);

        // Run the test
        final Integer result = attrServiceImplUnderTest.removePushAttr("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(0);

        // Confirm PushUtils.fixFieldsFromPush2SelfCreate(...).
        final AttrDO attrDO = new AttrDO();
        attrDO.setStoreGuid("storeGuid");
        attrDO.setParentGuid("parentGuid");
        attrDO.setName("name");
        attrDO.setIsDelete(0);
        attrDO.setGuid("attrGuid");
        attrDO.setBrandGuid("brandGuid");
        attrDO.setAttrGroupGuid("attrGroupGuid");
        attrDO.setIsDefault(0);
        attrDO.setAttrFrom(0);
        final List<AttrDO> withItemTypeDOList = Arrays.asList(attrDO);
        verify(mockPushUtils).fixFieldsFromPush2SelfCreate(eq(withItemTypeDOList), any(BiConsumer.class),
                any(BiConsumer.class));
        verify(mockTypeAttrService).remove(any(LambdaQueryWrapper.class));
    }

    @Test
    public void testRemovePushAttr_IRAttrItemAttrGroupServiceReturnsNoItems() {
        // Setup
        when(mockAttrItemAttrGroupService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final Integer result = attrServiceImplUnderTest.removePushAttr("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(0);
        verify(mockTypeAttrService).remove(any(LambdaQueryWrapper.class));
    }

    @Test
    public void testCheckAttrUsed() {
        // Setup
        // Configure IRAttrItemAttrGroupService.list(...).
        final RAttrItemAttrGroupDO rAttrItemAttrGroupDO = new RAttrItemAttrGroupDO();
        rAttrItemAttrGroupDO.setIsDelete(0);
        rAttrItemAttrGroupDO.setGuid("d7b44c74-4ae5-480f-a8d1-328f9edcb34a");
        rAttrItemAttrGroupDO.setAttrGuid("attrGuid");
        rAttrItemAttrGroupDO.setItemAttrGroupGuid("guid");
        rAttrItemAttrGroupDO.setIsDefault(0);
        final List<RAttrItemAttrGroupDO> rAttrItemAttrGroupDOS = Arrays.asList(rAttrItemAttrGroupDO);
        when(mockAttrItemAttrGroupService.list(any(LambdaQueryWrapper.class))).thenReturn(rAttrItemAttrGroupDOS);

        // Configure IRItemAttrGroupService.list(...).
        final RItemAttrGroupDO rItemAttrGroupDO = new RItemAttrGroupDO();
        rItemAttrGroupDO.setIsDelete(0);
        rItemAttrGroupDO.setGuid("guid");
        rItemAttrGroupDO.setItemGuid("guid");
        rItemAttrGroupDO.setAttrGroupGuid("attrGroupGuid");
        rItemAttrGroupDO.setIsRequired(0);
        rItemAttrGroupDO.setIsMultiChoice(0);
        rItemAttrGroupDO.setWithDefault(0);
        final List<RItemAttrGroupDO> rItemAttrGroupDOS = Arrays.asList(rItemAttrGroupDO);
        when(mockItemAttrGroupService.list(any(LambdaQueryWrapper.class))).thenReturn(rItemAttrGroupDOS);

        when(mockItemService.count(any(LambdaQueryWrapper.class))).thenReturn(0);

        // Run the test
        final Boolean result = attrServiceImplUnderTest.checkAttrUsed("attrGuid");

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testCheckAttrUsed_IRAttrItemAttrGroupServiceReturnsNoItems() {
        // Setup
        when(mockAttrItemAttrGroupService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final Boolean result = attrServiceImplUnderTest.checkAttrUsed("attrGuid");

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testCheckAttrUsed_IRItemAttrGroupServiceReturnsNoItems() {
        // Setup
        // Configure IRAttrItemAttrGroupService.list(...).
        final RAttrItemAttrGroupDO rAttrItemAttrGroupDO = new RAttrItemAttrGroupDO();
        rAttrItemAttrGroupDO.setIsDelete(0);
        rAttrItemAttrGroupDO.setGuid("d7b44c74-4ae5-480f-a8d1-328f9edcb34a");
        rAttrItemAttrGroupDO.setAttrGuid("attrGuid");
        rAttrItemAttrGroupDO.setItemAttrGroupGuid("guid");
        rAttrItemAttrGroupDO.setIsDefault(0);
        final List<RAttrItemAttrGroupDO> rAttrItemAttrGroupDOS = Arrays.asList(rAttrItemAttrGroupDO);
        when(mockAttrItemAttrGroupService.list(any(LambdaQueryWrapper.class))).thenReturn(rAttrItemAttrGroupDOS);

        when(mockItemAttrGroupService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final Boolean result = attrServiceImplUnderTest.checkAttrUsed("attrGuid");

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testListAttrByGroup2() {
        // Setup
        final AttrRespDTO attrRespDTO = new AttrRespDTO();
        attrRespDTO.setAttrGroupGuid("attrGroupGuid");
        attrRespDTO.setAttrGuid("attrGuid");
        attrRespDTO.setName("name");
        attrRespDTO.setPrice(new BigDecimal("0.00"));
        attrRespDTO.setIsDefault(0);
        final List<AttrRespDTO> expectedResult = Arrays.asList(attrRespDTO);

        // Configure AttrMapper.selectList(...).
        final AttrDO attrDO = new AttrDO();
        attrDO.setStoreGuid("storeGuid");
        attrDO.setParentGuid("parentGuid");
        attrDO.setName("name");
        attrDO.setIsDelete(0);
        attrDO.setGuid("attrGuid");
        attrDO.setBrandGuid("brandGuid");
        attrDO.setAttrGroupGuid("attrGroupGuid");
        attrDO.setIsDefault(0);
        attrDO.setAttrFrom(0);
        final List<AttrDO> attrDOS = Arrays.asList(attrDO);
        when(mockAttrMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(attrDOS);

        // Run the test
        final List<AttrRespDTO> result = attrServiceImplUnderTest.listAttrByGroup(Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testListAttrByGroup2_AttrMapperReturnsNoItems() {
        // Setup
        when(mockAttrMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<AttrRespDTO> result = attrServiceImplUnderTest.listAttrByGroup(Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testSaveAttrValue() {
        // Setup
        final AttrReqDTO attrReqDTO = new AttrReqDTO();
        attrReqDTO.setFrom(0);
        attrReqDTO.setAttrGroupGuid("attrGroupGuid");
        attrReqDTO.setName("name");
        attrReqDTO.setPrice(new BigDecimal("0.00"));
        attrReqDTO.setTypeGuidList(Arrays.asList("value"));

        when(mockAttrMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(0);

        // Configure IAttrGroupService.getById(...).
        final AttrGroupDO attrGroupDO = new AttrGroupDO();
        attrGroupDO.setStoreGuid("storeGuid");
        attrGroupDO.setParentGuid("parentGuid");
        attrGroupDO.setName("name");
        attrGroupDO.setGuid("attrGroupGuid");
        attrGroupDO.setBrandGuid("brandGuid");
        attrGroupDO.setIsRequired(0);
        attrGroupDO.setIsMultiChoice(0);
        when(mockAttrGroupService.getById("attrGroupGuid")).thenReturn(attrGroupDO);

        // Configure IItemService.list(...).
        final List<ItemDO> itemDOS = Arrays.asList(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "typeGuid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0, "pinyin", "nameAbbr", "description",
                        "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code", 0, 0, 0, "videoUrls", 0, false));
        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(itemDOS);

        // Configure IRItemAttrGroupService.list(...).
        final RItemAttrGroupDO rItemAttrGroupDO = new RItemAttrGroupDO();
        rItemAttrGroupDO.setIsDelete(0);
        rItemAttrGroupDO.setGuid("guid");
        rItemAttrGroupDO.setItemGuid("guid");
        rItemAttrGroupDO.setAttrGroupGuid("attrGroupGuid");
        rItemAttrGroupDO.setIsRequired(0);
        rItemAttrGroupDO.setIsMultiChoice(0);
        rItemAttrGroupDO.setWithDefault(0);
        final List<RItemAttrGroupDO> rItemAttrGroupDOS = Arrays.asList(rItemAttrGroupDO);
        when(mockItemAttrGroupService.list(any(LambdaQueryWrapper.class))).thenReturn(rItemAttrGroupDOS);

        // Configure IRAttrItemAttrGroupService.list(...).
        final RAttrItemAttrGroupDO rAttrItemAttrGroupDO = new RAttrItemAttrGroupDO();
        rAttrItemAttrGroupDO.setIsDelete(0);
        rAttrItemAttrGroupDO.setGuid("d7b44c74-4ae5-480f-a8d1-328f9edcb34a");
        rAttrItemAttrGroupDO.setAttrGuid("attrGuid");
        rAttrItemAttrGroupDO.setItemAttrGroupGuid("guid");
        rAttrItemAttrGroupDO.setIsDefault(0);
        final List<RAttrItemAttrGroupDO> rAttrItemAttrGroupDOS = Arrays.asList(rAttrItemAttrGroupDO);
        when(mockAttrItemAttrGroupService.list(any(LambdaQueryWrapper.class))).thenReturn(rAttrItemAttrGroupDOS);

        // Run the test
        final boolean result = attrServiceImplUnderTest.saveAttrValue(attrReqDTO);

        // Verify the results
        assertThat(result).isTrue();

        // Confirm AttrMapper.insert(...).
        final AttrDO entity = new AttrDO();
        entity.setStoreGuid("storeGuid");
        entity.setParentGuid("parentGuid");
        entity.setName("name");
        entity.setIsDelete(0);
        entity.setGuid("attrGuid");
        entity.setBrandGuid("brandGuid");
        entity.setAttrGroupGuid("attrGroupGuid");
        entity.setIsDefault(0);
        entity.setAttrFrom(0);
        verify(mockAttrMapper).insert(entity);
        verify(mockTypeAttrService).save(Arrays.asList("value"), "attrGuid");

        // Confirm IRItemAttrGroupService.saveBatch(...).
        final RItemAttrGroupDO rItemAttrGroupDO1 = new RItemAttrGroupDO();
        rItemAttrGroupDO1.setIsDelete(0);
        rItemAttrGroupDO1.setGuid("guid");
        rItemAttrGroupDO1.setItemGuid("guid");
        rItemAttrGroupDO1.setAttrGroupGuid("attrGroupGuid");
        rItemAttrGroupDO1.setIsRequired(0);
        rItemAttrGroupDO1.setIsMultiChoice(0);
        rItemAttrGroupDO1.setWithDefault(0);
        final List<RItemAttrGroupDO> entityList = Arrays.asList(rItemAttrGroupDO1);
        verify(mockItemAttrGroupService).saveBatch(entityList);

        // Confirm IRAttrItemAttrGroupService.saveBatch(...).
        final RAttrItemAttrGroupDO rAttrItemAttrGroupDO1 = new RAttrItemAttrGroupDO();
        rAttrItemAttrGroupDO1.setIsDelete(0);
        rAttrItemAttrGroupDO1.setGuid("d7b44c74-4ae5-480f-a8d1-328f9edcb34a");
        rAttrItemAttrGroupDO1.setAttrGuid("attrGuid");
        rAttrItemAttrGroupDO1.setItemAttrGroupGuid("guid");
        rAttrItemAttrGroupDO1.setIsDefault(0);
        final List<RAttrItemAttrGroupDO> entityList1 = Arrays.asList(rAttrItemAttrGroupDO1);
        verify(mockAttrItemAttrGroupService).saveBatch(entityList1);
        verify(mockItemService).updateBatchById(Arrays.asList(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "typeGuid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0, "pinyin", "nameAbbr", "description",
                        "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code", 0, 0, 0, "videoUrls", 0, false)));
    }

    @Test
    public void testSaveAttrValue_IAttrGroupServiceReturnsNull() {
        // Setup
        final AttrReqDTO attrReqDTO = new AttrReqDTO();
        attrReqDTO.setFrom(0);
        attrReqDTO.setAttrGroupGuid("attrGroupGuid");
        attrReqDTO.setName("name");
        attrReqDTO.setPrice(new BigDecimal("0.00"));
        attrReqDTO.setTypeGuidList(Arrays.asList("value"));

        when(mockAttrMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(0);
        when(mockAttrGroupService.getById("attrGroupGuid")).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> attrServiceImplUnderTest.saveAttrValue(attrReqDTO))
                .isInstanceOf(BusinessException.class);
    }

    @Test
    public void testSaveAttrValue_IItemServiceListReturnsNoItems() {
        // Setup
        final AttrReqDTO attrReqDTO = new AttrReqDTO();
        attrReqDTO.setFrom(0);
        attrReqDTO.setAttrGroupGuid("attrGroupGuid");
        attrReqDTO.setName("name");
        attrReqDTO.setPrice(new BigDecimal("0.00"));
        attrReqDTO.setTypeGuidList(Arrays.asList("value"));

        when(mockAttrMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(0);

        // Configure IAttrGroupService.getById(...).
        final AttrGroupDO attrGroupDO = new AttrGroupDO();
        attrGroupDO.setStoreGuid("storeGuid");
        attrGroupDO.setParentGuid("parentGuid");
        attrGroupDO.setName("name");
        attrGroupDO.setGuid("attrGroupGuid");
        attrGroupDO.setBrandGuid("brandGuid");
        attrGroupDO.setIsRequired(0);
        attrGroupDO.setIsMultiChoice(0);
        when(mockAttrGroupService.getById("attrGroupGuid")).thenReturn(attrGroupDO);

        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final boolean result = attrServiceImplUnderTest.saveAttrValue(attrReqDTO);

        // Verify the results
        assertThat(result).isTrue();

        // Confirm AttrMapper.insert(...).
        final AttrDO entity = new AttrDO();
        entity.setStoreGuid("storeGuid");
        entity.setParentGuid("parentGuid");
        entity.setName("name");
        entity.setIsDelete(0);
        entity.setGuid("attrGuid");
        entity.setBrandGuid("brandGuid");
        entity.setAttrGroupGuid("attrGroupGuid");
        entity.setIsDefault(0);
        entity.setAttrFrom(0);
        verify(mockAttrMapper).insert(entity);
        verify(mockTypeAttrService).save(Arrays.asList("value"), "attrGuid");
    }

    @Test
    public void testSaveAttrValue_IRItemAttrGroupServiceListReturnsNoItems() {
        // Setup
        final AttrReqDTO attrReqDTO = new AttrReqDTO();
        attrReqDTO.setFrom(0);
        attrReqDTO.setAttrGroupGuid("attrGroupGuid");
        attrReqDTO.setName("name");
        attrReqDTO.setPrice(new BigDecimal("0.00"));
        attrReqDTO.setTypeGuidList(Arrays.asList("value"));

        when(mockAttrMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(0);

        // Configure IAttrGroupService.getById(...).
        final AttrGroupDO attrGroupDO = new AttrGroupDO();
        attrGroupDO.setStoreGuid("storeGuid");
        attrGroupDO.setParentGuid("parentGuid");
        attrGroupDO.setName("name");
        attrGroupDO.setGuid("attrGroupGuid");
        attrGroupDO.setBrandGuid("brandGuid");
        attrGroupDO.setIsRequired(0);
        attrGroupDO.setIsMultiChoice(0);
        when(mockAttrGroupService.getById("attrGroupGuid")).thenReturn(attrGroupDO);

        // Configure IItemService.list(...).
        final List<ItemDO> itemDOS = Arrays.asList(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "typeGuid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0, "pinyin", "nameAbbr", "description",
                        "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code", 0, 0, 0, "videoUrls", 0, false));
        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(itemDOS);

        when(mockItemAttrGroupService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final boolean result = attrServiceImplUnderTest.saveAttrValue(attrReqDTO);

        // Verify the results
        assertThat(result).isTrue();

        // Confirm AttrMapper.insert(...).
        final AttrDO entity = new AttrDO();
        entity.setStoreGuid("storeGuid");
        entity.setParentGuid("parentGuid");
        entity.setName("name");
        entity.setIsDelete(0);
        entity.setGuid("attrGuid");
        entity.setBrandGuid("brandGuid");
        entity.setAttrGroupGuid("attrGroupGuid");
        entity.setIsDefault(0);
        entity.setAttrFrom(0);
        verify(mockAttrMapper).insert(entity);
        verify(mockTypeAttrService).save(Arrays.asList("value"), "attrGuid");

        // Confirm IRItemAttrGroupService.saveBatch(...).
        final RItemAttrGroupDO rItemAttrGroupDO = new RItemAttrGroupDO();
        rItemAttrGroupDO.setIsDelete(0);
        rItemAttrGroupDO.setGuid("guid");
        rItemAttrGroupDO.setItemGuid("guid");
        rItemAttrGroupDO.setAttrGroupGuid("attrGroupGuid");
        rItemAttrGroupDO.setIsRequired(0);
        rItemAttrGroupDO.setIsMultiChoice(0);
        rItemAttrGroupDO.setWithDefault(0);
        final List<RItemAttrGroupDO> entityList = Arrays.asList(rItemAttrGroupDO);
        verify(mockItemAttrGroupService).saveBatch(entityList);

        // Confirm IRAttrItemAttrGroupService.saveBatch(...).
        final RAttrItemAttrGroupDO rAttrItemAttrGroupDO = new RAttrItemAttrGroupDO();
        rAttrItemAttrGroupDO.setIsDelete(0);
        rAttrItemAttrGroupDO.setGuid("d7b44c74-4ae5-480f-a8d1-328f9edcb34a");
        rAttrItemAttrGroupDO.setAttrGuid("attrGuid");
        rAttrItemAttrGroupDO.setItemAttrGroupGuid("guid");
        rAttrItemAttrGroupDO.setIsDefault(0);
        final List<RAttrItemAttrGroupDO> entityList1 = Arrays.asList(rAttrItemAttrGroupDO);
        verify(mockAttrItemAttrGroupService).saveBatch(entityList1);
        verify(mockItemService).updateBatchById(Arrays.asList(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "typeGuid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0, "pinyin", "nameAbbr", "description",
                        "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code", 0, 0, 0, "videoUrls", 0, false)));
    }

    @Test
    public void testSaveAttrValue_IRAttrItemAttrGroupServiceListReturnsNoItems() {
        // Setup
        final AttrReqDTO attrReqDTO = new AttrReqDTO();
        attrReqDTO.setFrom(0);
        attrReqDTO.setAttrGroupGuid("attrGroupGuid");
        attrReqDTO.setName("name");
        attrReqDTO.setPrice(new BigDecimal("0.00"));
        attrReqDTO.setTypeGuidList(Arrays.asList("value"));

        when(mockAttrMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(0);

        // Configure IAttrGroupService.getById(...).
        final AttrGroupDO attrGroupDO = new AttrGroupDO();
        attrGroupDO.setStoreGuid("storeGuid");
        attrGroupDO.setParentGuid("parentGuid");
        attrGroupDO.setName("name");
        attrGroupDO.setGuid("attrGroupGuid");
        attrGroupDO.setBrandGuid("brandGuid");
        attrGroupDO.setIsRequired(0);
        attrGroupDO.setIsMultiChoice(0);
        when(mockAttrGroupService.getById("attrGroupGuid")).thenReturn(attrGroupDO);

        // Configure IItemService.list(...).
        final List<ItemDO> itemDOS = Arrays.asList(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "typeGuid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0, "pinyin", "nameAbbr", "description",
                        "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code", 0, 0, 0, "videoUrls", 0, false));
        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(itemDOS);

        // Configure IRItemAttrGroupService.list(...).
        final RItemAttrGroupDO rItemAttrGroupDO = new RItemAttrGroupDO();
        rItemAttrGroupDO.setIsDelete(0);
        rItemAttrGroupDO.setGuid("guid");
        rItemAttrGroupDO.setItemGuid("guid");
        rItemAttrGroupDO.setAttrGroupGuid("attrGroupGuid");
        rItemAttrGroupDO.setIsRequired(0);
        rItemAttrGroupDO.setIsMultiChoice(0);
        rItemAttrGroupDO.setWithDefault(0);
        final List<RItemAttrGroupDO> rItemAttrGroupDOS = Arrays.asList(rItemAttrGroupDO);
        when(mockItemAttrGroupService.list(any(LambdaQueryWrapper.class))).thenReturn(rItemAttrGroupDOS);

        when(mockAttrItemAttrGroupService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final boolean result = attrServiceImplUnderTest.saveAttrValue(attrReqDTO);

        // Verify the results
        assertThat(result).isTrue();

        // Confirm AttrMapper.insert(...).
        final AttrDO entity = new AttrDO();
        entity.setStoreGuid("storeGuid");
        entity.setParentGuid("parentGuid");
        entity.setName("name");
        entity.setIsDelete(0);
        entity.setGuid("attrGuid");
        entity.setBrandGuid("brandGuid");
        entity.setAttrGroupGuid("attrGroupGuid");
        entity.setIsDefault(0);
        entity.setAttrFrom(0);
        verify(mockAttrMapper).insert(entity);
        verify(mockTypeAttrService).save(Arrays.asList("value"), "attrGuid");

        // Confirm IRItemAttrGroupService.saveBatch(...).
        final RItemAttrGroupDO rItemAttrGroupDO1 = new RItemAttrGroupDO();
        rItemAttrGroupDO1.setIsDelete(0);
        rItemAttrGroupDO1.setGuid("guid");
        rItemAttrGroupDO1.setItemGuid("guid");
        rItemAttrGroupDO1.setAttrGroupGuid("attrGroupGuid");
        rItemAttrGroupDO1.setIsRequired(0);
        rItemAttrGroupDO1.setIsMultiChoice(0);
        rItemAttrGroupDO1.setWithDefault(0);
        final List<RItemAttrGroupDO> entityList = Arrays.asList(rItemAttrGroupDO1);
        verify(mockItemAttrGroupService).saveBatch(entityList);

        // Confirm IRAttrItemAttrGroupService.saveBatch(...).
        final RAttrItemAttrGroupDO rAttrItemAttrGroupDO = new RAttrItemAttrGroupDO();
        rAttrItemAttrGroupDO.setIsDelete(0);
        rAttrItemAttrGroupDO.setGuid("d7b44c74-4ae5-480f-a8d1-328f9edcb34a");
        rAttrItemAttrGroupDO.setAttrGuid("attrGuid");
        rAttrItemAttrGroupDO.setItemAttrGroupGuid("guid");
        rAttrItemAttrGroupDO.setIsDefault(0);
        final List<RAttrItemAttrGroupDO> entityList1 = Arrays.asList(rAttrItemAttrGroupDO);
        verify(mockAttrItemAttrGroupService).saveBatch(entityList1);
        verify(mockItemService).updateBatchById(Arrays.asList(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "typeGuid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0, "pinyin", "nameAbbr", "description",
                        "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code", 0, 0, 0, "videoUrls", 0, false)));
    }
}
