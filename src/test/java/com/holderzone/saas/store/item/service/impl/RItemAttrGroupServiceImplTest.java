package com.holderzone.saas.store.item.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.item.entity.bo.AttrGroupListAndAttrListBO;
import com.holderzone.saas.store.item.entity.bo.ItemAttrBO;
import com.holderzone.saas.store.item.entity.bo.ItemAttrGroupBO;
import com.holderzone.saas.store.item.entity.bo.ItemInfoBO;
import com.holderzone.saas.store.item.entity.domain.RAttrItemAttrGroupDO;
import com.holderzone.saas.store.item.entity.domain.RItemAttrGroupDO;
import com.holderzone.saas.store.item.service.IRAttrItemAttrGroupService;
import com.holderzone.saas.store.item.util.DynamicHelper;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.RedisTemplate;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class RItemAttrGroupServiceImplTest {

    @Mock
    private IRAttrItemAttrGroupService mockAttrItemAttrGroupService;
    @Mock
    private DynamicHelper mockDynamicHelper;
    @Mock
    private RedisTemplate mockRedisTemplate;

    private RItemAttrGroupServiceImpl rItemAttrGroupServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        rItemAttrGroupServiceImplUnderTest = new RItemAttrGroupServiceImpl(mockAttrItemAttrGroupService,
                mockDynamicHelper, mockRedisTemplate);
    }

    @Test
    public void testSelectAttrGroupAndAttrByItemGuidList() {
        // Setup
        final RItemAttrGroupDO rItemAttrGroupDO = new RItemAttrGroupDO();
        rItemAttrGroupDO.setId(0L);
        rItemAttrGroupDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        rItemAttrGroupDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        rItemAttrGroupDO.setGuid("guid");
        rItemAttrGroupDO.setItemGuid("itemGuid");
        final RAttrItemAttrGroupDO rAttrItemAttrGroupDO = new RAttrItemAttrGroupDO();
        rAttrItemAttrGroupDO.setId(0L);
        rAttrItemAttrGroupDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        rAttrItemAttrGroupDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        rAttrItemAttrGroupDO.setGuid("d2a15711-695e-4380-81a4-33a6e76b132d");
        rAttrItemAttrGroupDO.setItemAttrGroupGuid("guid");
        final List<AttrGroupListAndAttrListBO> expectedResult = Arrays.asList(
                new AttrGroupListAndAttrListBO(Arrays.asList(rItemAttrGroupDO), Arrays.asList(rAttrItemAttrGroupDO),
                        "itemGuid"));

        // Configure IRAttrItemAttrGroupService.list(...).
        final RAttrItemAttrGroupDO rAttrItemAttrGroupDO1 = new RAttrItemAttrGroupDO();
        rAttrItemAttrGroupDO1.setId(0L);
        rAttrItemAttrGroupDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        rAttrItemAttrGroupDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        rAttrItemAttrGroupDO1.setGuid("d2a15711-695e-4380-81a4-33a6e76b132d");
        rAttrItemAttrGroupDO1.setItemAttrGroupGuid("guid");
        final List<RAttrItemAttrGroupDO> rAttrItemAttrGroupDOS = Arrays.asList(rAttrItemAttrGroupDO1);
        when(mockAttrItemAttrGroupService.list(any(LambdaQueryWrapper.class))).thenReturn(rAttrItemAttrGroupDOS);

        // Run the test
        final List<AttrGroupListAndAttrListBO> result = rItemAttrGroupServiceImplUnderTest.selectAttrGroupAndAttrByItemGuidList(
                Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testSelectAttrGroupAndAttrByItemGuidList_IRAttrItemAttrGroupServiceReturnsNoItems() {
        // Setup
        when(mockAttrItemAttrGroupService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<AttrGroupListAndAttrListBO> result = rItemAttrGroupServiceImplUnderTest.selectAttrGroupAndAttrByItemGuidList(
                Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testSaveOrUpdateAndDeleteAttrGroupAndAttrRelation() {
        // Setup
        final ItemInfoBO itemInfoBO = new ItemInfoBO();
        itemInfoBO.setItemGuid("itemGuid");
        itemInfoBO.setHasAttr(0);
        final ItemAttrGroupBO itemAttrGroupBO = new ItemAttrGroupBO();
        itemAttrGroupBO.setItemGuid("itemGuid");
        itemAttrGroupBO.setItemAttrGroupGuid("itemAttrGroupGuid");
        itemAttrGroupBO.setWithDefault(0);
        final ItemAttrBO itemAttrBO = new ItemAttrBO();
        itemAttrBO.setItemAttrGroupGuid("itemAttrGroupGuid");
        itemAttrBO.setAttrItemAttrGroupGuid("attrItemAttrGroupGuid");
        itemAttrBO.setIsDefault(0);
        itemAttrGroupBO.setAttrList(Arrays.asList(itemAttrBO));
        itemInfoBO.setAttrGroupList(Arrays.asList(itemAttrGroupBO));
        final List<ItemInfoBO> itemInfoBOList = Arrays.asList(itemInfoBO);

        // Configure IRAttrItemAttrGroupService.list(...).
        final RAttrItemAttrGroupDO rAttrItemAttrGroupDO = new RAttrItemAttrGroupDO();
        rAttrItemAttrGroupDO.setId(0L);
        rAttrItemAttrGroupDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        rAttrItemAttrGroupDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        rAttrItemAttrGroupDO.setGuid("d2a15711-695e-4380-81a4-33a6e76b132d");
        rAttrItemAttrGroupDO.setItemAttrGroupGuid("guid");
        final List<RAttrItemAttrGroupDO> rAttrItemAttrGroupDOS = Arrays.asList(rAttrItemAttrGroupDO);
        when(mockAttrItemAttrGroupService.list(any(LambdaQueryWrapper.class))).thenReturn(rAttrItemAttrGroupDOS);

        // Configure IRAttrItemAttrGroupService.updateBatchById(...).
        final RAttrItemAttrGroupDO rAttrItemAttrGroupDO1 = new RAttrItemAttrGroupDO();
        rAttrItemAttrGroupDO1.setId(0L);
        rAttrItemAttrGroupDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        rAttrItemAttrGroupDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        rAttrItemAttrGroupDO1.setGuid("d2a15711-695e-4380-81a4-33a6e76b132d");
        rAttrItemAttrGroupDO1.setItemAttrGroupGuid("guid");
        final List<RAttrItemAttrGroupDO> entityList = Arrays.asList(rAttrItemAttrGroupDO1);
        when(mockAttrItemAttrGroupService.updateBatchById(entityList, 0)).thenReturn(true);

        // Run the test
        final boolean result = rItemAttrGroupServiceImplUnderTest.saveOrUpdateAndDeleteAttrGroupAndAttrRelation(
                itemInfoBOList);

        // Verify the results
        assertThat(result).isTrue();
        verify(mockAttrItemAttrGroupService).removeByIds(Arrays.asList("value"));

        // Confirm IRAttrItemAttrGroupService.saveBatch(...).
        final RAttrItemAttrGroupDO rAttrItemAttrGroupDO2 = new RAttrItemAttrGroupDO();
        rAttrItemAttrGroupDO2.setId(0L);
        rAttrItemAttrGroupDO2.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        rAttrItemAttrGroupDO2.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        rAttrItemAttrGroupDO2.setGuid("d2a15711-695e-4380-81a4-33a6e76b132d");
        rAttrItemAttrGroupDO2.setItemAttrGroupGuid("guid");
        final List<RAttrItemAttrGroupDO> entityList1 = Arrays.asList(rAttrItemAttrGroupDO2);
        verify(mockAttrItemAttrGroupService).saveBatch(entityList1, 0);

        // Confirm IRAttrItemAttrGroupService.saveBatch(...).
        final RAttrItemAttrGroupDO rAttrItemAttrGroupDO3 = new RAttrItemAttrGroupDO();
        rAttrItemAttrGroupDO3.setId(0L);
        rAttrItemAttrGroupDO3.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        rAttrItemAttrGroupDO3.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        rAttrItemAttrGroupDO3.setGuid("d2a15711-695e-4380-81a4-33a6e76b132d");
        rAttrItemAttrGroupDO3.setItemAttrGroupGuid("guid");
        final List<RAttrItemAttrGroupDO> entityList2 = Arrays.asList(rAttrItemAttrGroupDO3);
        verify(mockAttrItemAttrGroupService).saveBatch(entityList2);
        verify(mockAttrItemAttrGroupService).remove(any(LambdaQueryWrapper.class));
    }

    @Test
    public void testSaveOrUpdateAndDeleteAttrGroupAndAttrRelation_IRAttrItemAttrGroupServiceListReturnsNoItems() {
        // Setup
        final ItemInfoBO itemInfoBO = new ItemInfoBO();
        itemInfoBO.setItemGuid("itemGuid");
        itemInfoBO.setHasAttr(0);
        final ItemAttrGroupBO itemAttrGroupBO = new ItemAttrGroupBO();
        itemAttrGroupBO.setItemGuid("itemGuid");
        itemAttrGroupBO.setItemAttrGroupGuid("itemAttrGroupGuid");
        itemAttrGroupBO.setWithDefault(0);
        final ItemAttrBO itemAttrBO = new ItemAttrBO();
        itemAttrBO.setItemAttrGroupGuid("itemAttrGroupGuid");
        itemAttrBO.setAttrItemAttrGroupGuid("attrItemAttrGroupGuid");
        itemAttrBO.setIsDefault(0);
        itemAttrGroupBO.setAttrList(Arrays.asList(itemAttrBO));
        itemInfoBO.setAttrGroupList(Arrays.asList(itemAttrGroupBO));
        final List<ItemInfoBO> itemInfoBOList = Arrays.asList(itemInfoBO);
        when(mockAttrItemAttrGroupService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure IRAttrItemAttrGroupService.updateBatchById(...).
        final RAttrItemAttrGroupDO rAttrItemAttrGroupDO = new RAttrItemAttrGroupDO();
        rAttrItemAttrGroupDO.setId(0L);
        rAttrItemAttrGroupDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        rAttrItemAttrGroupDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        rAttrItemAttrGroupDO.setGuid("d2a15711-695e-4380-81a4-33a6e76b132d");
        rAttrItemAttrGroupDO.setItemAttrGroupGuid("guid");
        final List<RAttrItemAttrGroupDO> entityList = Arrays.asList(rAttrItemAttrGroupDO);
        when(mockAttrItemAttrGroupService.updateBatchById(entityList, 0)).thenReturn(true);

        // Run the test
        final boolean result = rItemAttrGroupServiceImplUnderTest.saveOrUpdateAndDeleteAttrGroupAndAttrRelation(
                itemInfoBOList);

        // Verify the results
        assertThat(result).isTrue();
        verify(mockAttrItemAttrGroupService).removeByIds(Arrays.asList("value"));

        // Confirm IRAttrItemAttrGroupService.saveBatch(...).
        final RAttrItemAttrGroupDO rAttrItemAttrGroupDO1 = new RAttrItemAttrGroupDO();
        rAttrItemAttrGroupDO1.setId(0L);
        rAttrItemAttrGroupDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        rAttrItemAttrGroupDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        rAttrItemAttrGroupDO1.setGuid("d2a15711-695e-4380-81a4-33a6e76b132d");
        rAttrItemAttrGroupDO1.setItemAttrGroupGuid("guid");
        final List<RAttrItemAttrGroupDO> entityList1 = Arrays.asList(rAttrItemAttrGroupDO1);
        verify(mockAttrItemAttrGroupService).saveBatch(entityList1, 0);

        // Confirm IRAttrItemAttrGroupService.saveBatch(...).
        final RAttrItemAttrGroupDO rAttrItemAttrGroupDO2 = new RAttrItemAttrGroupDO();
        rAttrItemAttrGroupDO2.setId(0L);
        rAttrItemAttrGroupDO2.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        rAttrItemAttrGroupDO2.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        rAttrItemAttrGroupDO2.setGuid("d2a15711-695e-4380-81a4-33a6e76b132d");
        rAttrItemAttrGroupDO2.setItemAttrGroupGuid("guid");
        final List<RAttrItemAttrGroupDO> entityList2 = Arrays.asList(rAttrItemAttrGroupDO2);
        verify(mockAttrItemAttrGroupService).saveBatch(entityList2);
        verify(mockAttrItemAttrGroupService).remove(any(LambdaQueryWrapper.class));
    }

    @Test
    public void testSaveOrUpdateAndDeleteAttrGroupAndAttrRelation_IRAttrItemAttrGroupServiceUpdateBatchByIdReturnsFalse() {
        // Setup
        final ItemInfoBO itemInfoBO = new ItemInfoBO();
        itemInfoBO.setItemGuid("itemGuid");
        itemInfoBO.setHasAttr(0);
        final ItemAttrGroupBO itemAttrGroupBO = new ItemAttrGroupBO();
        itemAttrGroupBO.setItemGuid("itemGuid");
        itemAttrGroupBO.setItemAttrGroupGuid("itemAttrGroupGuid");
        itemAttrGroupBO.setWithDefault(0);
        final ItemAttrBO itemAttrBO = new ItemAttrBO();
        itemAttrBO.setItemAttrGroupGuid("itemAttrGroupGuid");
        itemAttrBO.setAttrItemAttrGroupGuid("attrItemAttrGroupGuid");
        itemAttrBO.setIsDefault(0);
        itemAttrGroupBO.setAttrList(Arrays.asList(itemAttrBO));
        itemInfoBO.setAttrGroupList(Arrays.asList(itemAttrGroupBO));
        final List<ItemInfoBO> itemInfoBOList = Arrays.asList(itemInfoBO);

        // Configure IRAttrItemAttrGroupService.list(...).
        final RAttrItemAttrGroupDO rAttrItemAttrGroupDO = new RAttrItemAttrGroupDO();
        rAttrItemAttrGroupDO.setId(0L);
        rAttrItemAttrGroupDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        rAttrItemAttrGroupDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        rAttrItemAttrGroupDO.setGuid("d2a15711-695e-4380-81a4-33a6e76b132d");
        rAttrItemAttrGroupDO.setItemAttrGroupGuid("guid");
        final List<RAttrItemAttrGroupDO> rAttrItemAttrGroupDOS = Arrays.asList(rAttrItemAttrGroupDO);
        when(mockAttrItemAttrGroupService.list(any(LambdaQueryWrapper.class))).thenReturn(rAttrItemAttrGroupDOS);

        // Configure IRAttrItemAttrGroupService.updateBatchById(...).
        final RAttrItemAttrGroupDO rAttrItemAttrGroupDO1 = new RAttrItemAttrGroupDO();
        rAttrItemAttrGroupDO1.setId(0L);
        rAttrItemAttrGroupDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        rAttrItemAttrGroupDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        rAttrItemAttrGroupDO1.setGuid("d2a15711-695e-4380-81a4-33a6e76b132d");
        rAttrItemAttrGroupDO1.setItemAttrGroupGuid("guid");
        final List<RAttrItemAttrGroupDO> entityList = Arrays.asList(rAttrItemAttrGroupDO1);
        when(mockAttrItemAttrGroupService.updateBatchById(entityList, 0)).thenReturn(false);

        // Run the test
        assertThatThrownBy(() -> rItemAttrGroupServiceImplUnderTest.saveOrUpdateAndDeleteAttrGroupAndAttrRelation(
                itemInfoBOList)).isInstanceOf(BusinessException.class);
        verify(mockAttrItemAttrGroupService).removeByIds(Arrays.asList("value"));
    }
}
