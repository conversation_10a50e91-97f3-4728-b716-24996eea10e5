package com.holderzone.saas.store.item.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.saas.store.item.entity.domain.RTypeAttrDO;
import com.holderzone.saas.store.item.mapper.RTypeAttrMapper;
import com.holderzone.saas.store.item.util.DynamicHelper;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class RTypeAttrServiceImplTest {

    @Mock
    private RTypeAttrMapper mockTypeAttrMapper;
    @Mock
    private DynamicHelper mockDynamicHelper;
    @Mock
    private RedisTemplate mockRedisTemplate;

    private RTypeAttrServiceImpl rTypeAttrServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        rTypeAttrServiceImplUnderTest = new RTypeAttrServiceImpl(mockTypeAttrMapper, mockDynamicHelper,
                mockRedisTemplate);
    }

    @Test
    public void testListTypeGuidByAttr() {
        // Setup
        // Configure RTypeAttrMapper.selectList(...).
        final RTypeAttrDO rTypeAttrDO = new RTypeAttrDO();
        rTypeAttrDO.setId(0L);
        rTypeAttrDO.setIsDelete(0);
        rTypeAttrDO.setGuid("bb275558-6348-4e6f-8c1b-cee2a3752b41");
        rTypeAttrDO.setTypeGuid("typeGuid");
        rTypeAttrDO.setAttrGuid("attrGuid");
        final List<RTypeAttrDO> rTypeAttrDOS = Arrays.asList(rTypeAttrDO);
        when(mockTypeAttrMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(rTypeAttrDOS);

        // Run the test
        final List<String> result = rTypeAttrServiceImplUnderTest.listTypeGuidByAttr("attrGuid");

        // Verify the results
        assertThat(result).isEqualTo(Arrays.asList("value"));
    }

    @Test
    public void testListTypeGuidByAttr_RTypeAttrMapperReturnsNoItems() {
        // Setup
        when(mockTypeAttrMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<String> result = rTypeAttrServiceImplUnderTest.listTypeGuidByAttr("attrGuid");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testRemoveByAttr() {
        // Setup
        when(mockTypeAttrMapper.delete(any(LambdaQueryWrapper.class))).thenReturn(0);

        // Run the test
        final Integer result = rTypeAttrServiceImplUnderTest.removeByAttr("attrGuid");

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    public void testRemoveBind() {
        // Setup
        when(mockTypeAttrMapper.delete(any(LambdaQueryWrapper.class))).thenReturn(0);

        // Run the test
        final Integer result = rTypeAttrServiceImplUnderTest.removeBind(Arrays.asList("value"), "attrGuid");

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    public void testSave() {
        assertThat(rTypeAttrServiceImplUnderTest.save(Arrays.asList("value"), "attrGuid")).isFalse();
    }

    @Test
    public void testDeleteByAttr() {
        // Setup
        // Run the test
        rTypeAttrServiceImplUnderTest.deleteByAttr("attrGuid");

        // Verify the results
        verify(mockTypeAttrMapper).delete(any(LambdaQueryWrapper.class));
    }
}
