package com.holderzone.saas.store.item.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.common.ItemStringListDTO;
import com.holderzone.saas.store.dto.item.req.*;
import com.holderzone.saas.store.dto.item.resp.ItemInfoRespDTO;
import com.holderzone.saas.store.dto.item.resp.SubItemSkuWebRespDTO;
import com.holderzone.saas.store.dto.item.resp.SubgroupWebRespDTO;
import com.holderzone.saas.store.dto.item.resp.TypeSkuRespDTO;
import com.holderzone.saas.store.item.entity.bo.ItemInfoBO;
import com.holderzone.saas.store.item.entity.bo.SubgroupBO;
import com.holderzone.saas.store.item.entity.domain.ItemDO;
import com.holderzone.saas.store.item.entity.domain.SkuDO;
import com.holderzone.saas.store.item.entity.domain.SubgroupDO;
import com.holderzone.saas.store.item.entity.domain.TypeDO;
import com.holderzone.saas.store.item.helper.ItemHelper;
import com.holderzone.saas.store.item.service.*;
import com.holderzone.saas.store.item.util.DynamicHelper;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.RedisTemplate;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ItemPkgServiceImplTest {

    @Mock
    private IGroupMealService mockGroupMealService;
    @Mock
    private ITypeService mockTypeService;
    @Mock
    private IItemService mockItemService;
    @Mock
    private ISkuService mockSkuService;
    @Mock
    private ISubgroupService mockSubgroupService;
    @Mock
    private DynamicHelper mockDynamicHelper;
    @Mock
    private ItemHelper mockItemHelper;
    @Mock
    private RedisTemplate mockRedisTemplate;

    private ItemPkgServiceImpl itemPkgServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        itemPkgServiceImplUnderTest = new ItemPkgServiceImpl(mockGroupMealService, mockTypeService, mockItemService,
                mockSkuService, mockSubgroupService, mockDynamicHelper, mockItemHelper, mockRedisTemplate);
    }

    @Test
    public void testSelectSkuListForPkg() {
        // Setup
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setFrom(0);
        itemSingleDTO.setData("data");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);

        final TypeSkuRespDTO typeSkuRespDTO = new TypeSkuRespDTO();
        typeSkuRespDTO.setTypeGuid("typeGuid");
        typeSkuRespDTO.setName("name");
        final TypeSkuRespDTO.SkuNameRespDTO skuNameRespDTO = new TypeSkuRespDTO.SkuNameRespDTO();
        skuNameRespDTO.setItemGuid("itemGuid");
        skuNameRespDTO.setItemType(0);
        typeSkuRespDTO.setSkuNameRespDTOList(Arrays.asList(skuNameRespDTO));
        final List<TypeSkuRespDTO> expectedResult = Arrays.asList(typeSkuRespDTO);

        // Configure ITypeService.list(...).
        final List<TypeDO> typeDOS = Arrays.asList(
                new TypeDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "brandGuid", 0, 0, "description", "iconUrl", 0, 0, 0, "pricePlanGuid", 0,1));
        when(mockTypeService.list(any(LambdaQueryWrapper.class))).thenReturn(typeDOS);

        // Configure IItemService.list(...).
        final List<ItemDO> itemDOS = Arrays.asList(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "guid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0, "pinyin", "nameAbbr", "description",
                        "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code", 0, 0, 0, "videoUrls", 0, false));
        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(itemDOS);

        // Configure ISkuService.list(...).
        final SkuDO skuDO = new SkuDO();
        skuDO.setGuid("ebe01d32-a995-4978-8e46-7f592be34a37");
        skuDO.setStoreGuid("storeGuid");
        skuDO.setItemGuid("guid");
        skuDO.setName("name");
        skuDO.setCode("code");
        skuDO.setIsRack(0);
        skuDO.setIsEnable(false);
        final List<SkuDO> skuDOS = Arrays.asList(skuDO);
        when(mockSkuService.list(any(LambdaQueryWrapper.class))).thenReturn(skuDOS);

        // Run the test
        final List<TypeSkuRespDTO> result = itemPkgServiceImplUnderTest.selectSkuListForPkg(itemSingleDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testSelectSkuListForPkg_ITypeServiceReturnsNoItems() {
        // Setup
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setFrom(0);
        itemSingleDTO.setData("data");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);

        when(mockTypeService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        assertThatThrownBy(() -> itemPkgServiceImplUnderTest.selectSkuListForPkg(itemSingleDTO))
                .isInstanceOf(BusinessException.class);
    }

    @Test
    public void testSelectSkuListForPkg_IItemServiceReturnsNoItems() {
        // Setup
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setFrom(0);
        itemSingleDTO.setData("data");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);

        // Configure ITypeService.list(...).
        final List<TypeDO> typeDOS = Arrays.asList(
                new TypeDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "brandGuid", 0, 0, "description", "iconUrl", 0, 0, 0, "pricePlanGuid", 0,1));
        when(mockTypeService.list(any(LambdaQueryWrapper.class))).thenReturn(typeDOS);

        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        assertThatThrownBy(() -> itemPkgServiceImplUnderTest.selectSkuListForPkg(itemSingleDTO))
                .isInstanceOf(BusinessException.class);
    }

    @Test
    public void testSelectSkuListForPkg_ISkuServiceReturnsNoItems() {
        // Setup
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setFrom(0);
        itemSingleDTO.setData("data");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);

        // Configure ITypeService.list(...).
        final List<TypeDO> typeDOS = Arrays.asList(
                new TypeDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "brandGuid", 0, 0, "description", "iconUrl", 0, 0, 0, "pricePlanGuid", 0,1));
        when(mockTypeService.list(any(LambdaQueryWrapper.class))).thenReturn(typeDOS);

        // Configure IItemService.list(...).
        final List<ItemDO> itemDOS = Arrays.asList(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "guid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0, "pinyin", "nameAbbr", "description",
                        "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code", 0, 0, 0, "videoUrls", 0, false));
        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(itemDOS);

        when(mockSkuService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        assertThatThrownBy(() -> itemPkgServiceImplUnderTest.selectSkuListForPkg(itemSingleDTO))
                .isInstanceOf(BusinessException.class);
    }

    @Test
    public void testGetTypeSkuRespDTOS() {
        // Setup
        final List<TypeDO> typeDOList = Arrays.asList(
                new TypeDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "brandGuid", 0, 0, "description", "iconUrl", 0, 0, 0, "pricePlanGuid", 0,1));
        final LambdaQueryWrapper<ItemDO> itemDOLambdaQueryWrapper = new LambdaQueryWrapper<>(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "guid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0, "pinyin", "nameAbbr", "description",
                        "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code", 0, 0, 0, "videoUrls", 0, false));
        final TypeSkuRespDTO typeSkuRespDTO = new TypeSkuRespDTO();
        typeSkuRespDTO.setTypeGuid("typeGuid");
        typeSkuRespDTO.setName("name");
        final TypeSkuRespDTO.SkuNameRespDTO skuNameRespDTO = new TypeSkuRespDTO.SkuNameRespDTO();
        skuNameRespDTO.setItemGuid("itemGuid");
        skuNameRespDTO.setItemType(0);
        typeSkuRespDTO.setSkuNameRespDTOList(Arrays.asList(skuNameRespDTO));
        final List<TypeSkuRespDTO> expectedResult = Arrays.asList(typeSkuRespDTO);

        // Configure IItemService.list(...).
        final List<ItemDO> itemDOS = Arrays.asList(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "guid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0, "pinyin", "nameAbbr", "description",
                        "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code", 0, 0, 0, "videoUrls", 0, false));
        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(itemDOS);

        // Configure ISkuService.list(...).
        final SkuDO skuDO = new SkuDO();
        skuDO.setGuid("ebe01d32-a995-4978-8e46-7f592be34a37");
        skuDO.setStoreGuid("storeGuid");
        skuDO.setItemGuid("guid");
        skuDO.setName("name");
        skuDO.setCode("code");
        skuDO.setIsRack(0);
        skuDO.setIsEnable(false);
        final List<SkuDO> skuDOS = Arrays.asList(skuDO);
        when(mockSkuService.list(any(LambdaQueryWrapper.class))).thenReturn(skuDOS);

        // Run the test
        final List<TypeSkuRespDTO> result = itemPkgServiceImplUnderTest.getTypeSkuRespDTOS(typeDOList,
                itemDOLambdaQueryWrapper);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetTypeSkuRespDTOS_IItemServiceReturnsNoItems() {
        // Setup
        final List<TypeDO> typeDOList = Arrays.asList(
                new TypeDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "brandGuid", 0, 0, "description", "iconUrl", 0, 0, 0, "pricePlanGuid", 0,1));
        final LambdaQueryWrapper<ItemDO> itemDOLambdaQueryWrapper = new LambdaQueryWrapper<>(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "guid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0, "pinyin", "nameAbbr", "description",
                        "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code", 0, 0, 0, "videoUrls", 0, false));
        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        assertThatThrownBy(() -> itemPkgServiceImplUnderTest.getTypeSkuRespDTOS(typeDOList,
                itemDOLambdaQueryWrapper)).isInstanceOf(BusinessException.class);
    }

    @Test
    public void testGetTypeSkuRespDTOS_ISkuServiceReturnsNoItems() {
        // Setup
        final List<TypeDO> typeDOList = Arrays.asList(
                new TypeDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "brandGuid", 0, 0, "description", "iconUrl", 0, 0, 0, "pricePlanGuid", 0,1));
        final LambdaQueryWrapper<ItemDO> itemDOLambdaQueryWrapper = new LambdaQueryWrapper<>(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "guid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0, "pinyin", "nameAbbr", "description",
                        "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code", 0, 0, 0, "videoUrls", 0, false));

        // Configure IItemService.list(...).
        final List<ItemDO> itemDOS = Arrays.asList(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "guid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0, "pinyin", "nameAbbr", "description",
                        "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code", 0, 0, 0, "videoUrls", 0, false));
        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(itemDOS);

        when(mockSkuService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        assertThatThrownBy(() -> itemPkgServiceImplUnderTest.getTypeSkuRespDTOS(typeDOList,
                itemDOLambdaQueryWrapper)).isInstanceOf(BusinessException.class);
    }

    @Test
    public void testSelectTypeSkuListForPkg() {
        // Setup
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setFrom(0);
        itemSingleDTO.setData("data");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);

        final TypeSkuRespDTO typeSkuRespDTO = new TypeSkuRespDTO();
        typeSkuRespDTO.setTypeGuid("typeGuid");
        typeSkuRespDTO.setName("name");
        final TypeSkuRespDTO.SkuNameRespDTO skuNameRespDTO = new TypeSkuRespDTO.SkuNameRespDTO();
        skuNameRespDTO.setItemGuid("itemGuid");
        skuNameRespDTO.setItemType(0);
        typeSkuRespDTO.setSkuNameRespDTOList(Arrays.asList(skuNameRespDTO));
        final List<TypeSkuRespDTO> expectedResult = Arrays.asList(typeSkuRespDTO);

        // Configure ITypeService.list(...).
        final List<TypeDO> typeDOS = Arrays.asList(
                new TypeDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "brandGuid", 0, 0, "description", "iconUrl", 0, 0, 0, "pricePlanGuid", 0,1));
        when(mockTypeService.list(any(LambdaQueryWrapper.class))).thenReturn(typeDOS);

        // Configure IItemService.list(...).
        final List<ItemDO> itemDOS = Arrays.asList(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "guid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0, "pinyin", "nameAbbr", "description",
                        "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code", 0, 0, 0, "videoUrls", 0, false));
        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(itemDOS);

        // Configure ISkuService.list(...).
        final SkuDO skuDO = new SkuDO();
        skuDO.setGuid("ebe01d32-a995-4978-8e46-7f592be34a37");
        skuDO.setStoreGuid("storeGuid");
        skuDO.setItemGuid("guid");
        skuDO.setName("name");
        skuDO.setCode("code");
        skuDO.setIsRack(0);
        skuDO.setIsEnable(false);
        final List<SkuDO> skuDOS = Arrays.asList(skuDO);
        when(mockSkuService.list(any(LambdaQueryWrapper.class))).thenReturn(skuDOS);

        // Run the test
        final List<TypeSkuRespDTO> result = itemPkgServiceImplUnderTest.selectTypeSkuListForPkg(itemSingleDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testSelectTypeSkuListForPkg_ITypeServiceReturnsNoItems() {
        // Setup
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setFrom(0);
        itemSingleDTO.setData("data");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);

        when(mockTypeService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        assertThatThrownBy(() -> itemPkgServiceImplUnderTest.selectTypeSkuListForPkg(itemSingleDTO))
                .isInstanceOf(BusinessException.class);
    }

    @Test
    public void testSelectTypeSkuListForPkg_IItemServiceReturnsNoItems() {
        // Setup
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setFrom(0);
        itemSingleDTO.setData("data");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);

        // Configure ITypeService.list(...).
        final List<TypeDO> typeDOS = Arrays.asList(
                new TypeDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "brandGuid", 0, 0, "description", "iconUrl", 0, 0, 0, "pricePlanGuid", 0,1));
        when(mockTypeService.list(any(LambdaQueryWrapper.class))).thenReturn(typeDOS);

        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        assertThatThrownBy(() -> itemPkgServiceImplUnderTest.selectTypeSkuListForPkg(itemSingleDTO))
                .isInstanceOf(BusinessException.class);
    }

    @Test
    public void testSelectTypeSkuListForPkg_ISkuServiceReturnsNoItems() {
        // Setup
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setFrom(0);
        itemSingleDTO.setData("data");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);

        // Configure ITypeService.list(...).
        final List<TypeDO> typeDOS = Arrays.asList(
                new TypeDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "brandGuid", 0, 0, "description", "iconUrl", 0, 0, 0, "pricePlanGuid", 0,1));
        when(mockTypeService.list(any(LambdaQueryWrapper.class))).thenReturn(typeDOS);

        // Configure IItemService.list(...).
        final List<ItemDO> itemDOS = Arrays.asList(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "guid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0, "pinyin", "nameAbbr", "description",
                        "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code", 0, 0, 0, "videoUrls", 0, false));
        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(itemDOS);

        when(mockSkuService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        assertThatThrownBy(() -> itemPkgServiceImplUnderTest.selectTypeSkuListForPkg(itemSingleDTO))
                .isInstanceOf(BusinessException.class);
    }

    @Test
    public void testSaveItemPkg() {
        // Setup
        final ItemPkgSaveReqDTO itemPkgSaveReqDTO = new ItemPkgSaveReqDTO();
        itemPkgSaveReqDTO.setFrom(0);
        itemPkgSaveReqDTO.setDataList(Arrays.asList("value"));
        itemPkgSaveReqDTO.setItemGuid("itemGuid");
        itemPkgSaveReqDTO.setStoreGuid("storeGuid");
        itemPkgSaveReqDTO.setBrandGuid("brandGuid");
        itemPkgSaveReqDTO.setItemFrom(0);
        itemPkgSaveReqDTO.setItemType(0);
        itemPkgSaveReqDTO.setHasAttr(0);
        itemPkgSaveReqDTO.setName("name");
        itemPkgSaveReqDTO.setSort(0);
        itemPkgSaveReqDTO.setPictureUrl("pictureUrl");
        final SkuSaveReqDTO skuSaveReqDTO = new SkuSaveReqDTO();
        skuSaveReqDTO.setSkuGuid("skuGuid");
        skuSaveReqDTO.setCode("code");
        skuSaveReqDTO.setMinOrderNum(new BigDecimal("0.00"));
        itemPkgSaveReqDTO.setSkuList(Arrays.asList(skuSaveReqDTO));
        final SubgroupReqDTO subgroupReqDTO = new SubgroupReqDTO();
        subgroupReqDTO.setName("默认分组");
        subgroupReqDTO.setIsFixSubgroup(0);
        subgroupReqDTO.setPickNum(0);
        subgroupReqDTO.setSort(0);
        final SubItemSkuReqDTO subItemSkuReqDTO = new SubItemSkuReqDTO();
        subItemSkuReqDTO.setSkuGuid("skuGuid");
        subItemSkuReqDTO.setAddPrice(new BigDecimal("0.00"));
        subItemSkuReqDTO.setIsDefault(0);
        subItemSkuReqDTO.setIsRepeat(0);
        subItemSkuReqDTO.setSort(0);
        subgroupReqDTO.setSubItemSkuList(Arrays.asList(subItemSkuReqDTO));
        itemPkgSaveReqDTO.setSubgroupList(Arrays.asList(subgroupReqDTO));

        when(mockItemHelper.sort(0, "brandGuid", "storeGuid")).thenReturn(0);
        when(mockItemService.count(any(LambdaQueryWrapper.class))).thenReturn(0);

        // Configure ISkuService.saveOrUpdateAndDeleteSku(...).
        final ItemInfoBO itemInfoBO = new ItemInfoBO();
        itemInfoBO.setFrom(0);
        itemInfoBO.setItemGuid("itemGuid");
        itemInfoBO.setBrandGuid("brandGuid");
        itemInfoBO.setBrandName("brandName");
        itemInfoBO.setStoreGuid("storeGuid");
        when(mockSkuService.saveOrUpdateAndDeleteSku(itemInfoBO)).thenReturn(Arrays.asList("value"));

        // Run the test
        final List<String> result = itemPkgServiceImplUnderTest.saveItemPkg(itemPkgSaveReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(Arrays.asList("value"));
        verify(mockItemHelper).validateOrganizeGuid(0, "brandGuid", "storeGuid");

        // Confirm ISkuService.validateSkuCodeRepeat(...).
        final SkuSaveReqDTO skuSaveReqDTO1 = new SkuSaveReqDTO();
        skuSaveReqDTO1.setItemGuid("itemGuid");
        skuSaveReqDTO1.setSkuGuid("skuGuid");
        skuSaveReqDTO1.setName("name");
        skuSaveReqDTO1.setCode("code");
        skuSaveReqDTO1.setMinOrderNum(new BigDecimal("0.00"));
        final List<SkuSaveReqDTO> skuList = Arrays.asList(skuSaveReqDTO1);
        verify(mockSkuService).validateSkuCodeRepeat(skuList, "brandGuid", "storeGuid", 0, 0);
        verify(mockItemService).save(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "guid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0, "pinyin", "nameAbbr", "description",
                        "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code", 0, 0, 0, "videoUrls", 0, false));

        // Confirm ISubgroupService.saveOrUpdateAndDeleteSubgroup(...).
        final ItemInfoBO itemInfoBO1 = new ItemInfoBO();
        itemInfoBO1.setFrom(0);
        itemInfoBO1.setItemGuid("itemGuid");
        itemInfoBO1.setBrandGuid("brandGuid");
        itemInfoBO1.setBrandName("brandName");
        itemInfoBO1.setStoreGuid("storeGuid");
        final List<ItemInfoBO> itemInfoBOList = Arrays.asList(itemInfoBO1);
        verify(mockSubgroupService).saveOrUpdateAndDeleteSubgroup(itemInfoBOList);
    }

    @Test
    public void testSaveItemPkg_ISkuServiceSaveOrUpdateAndDeleteSkuReturnsNoItems() {
        // Setup
        final ItemPkgSaveReqDTO itemPkgSaveReqDTO = new ItemPkgSaveReqDTO();
        itemPkgSaveReqDTO.setFrom(0);
        itemPkgSaveReqDTO.setDataList(Arrays.asList("value"));
        itemPkgSaveReqDTO.setItemGuid("itemGuid");
        itemPkgSaveReqDTO.setStoreGuid("storeGuid");
        itemPkgSaveReqDTO.setBrandGuid("brandGuid");
        itemPkgSaveReqDTO.setItemFrom(0);
        itemPkgSaveReqDTO.setItemType(0);
        itemPkgSaveReqDTO.setHasAttr(0);
        itemPkgSaveReqDTO.setName("name");
        itemPkgSaveReqDTO.setSort(0);
        itemPkgSaveReqDTO.setPictureUrl("pictureUrl");
        final SkuSaveReqDTO skuSaveReqDTO = new SkuSaveReqDTO();
        skuSaveReqDTO.setSkuGuid("skuGuid");
        skuSaveReqDTO.setCode("code");
        skuSaveReqDTO.setMinOrderNum(new BigDecimal("0.00"));
        itemPkgSaveReqDTO.setSkuList(Arrays.asList(skuSaveReqDTO));
        final SubgroupReqDTO subgroupReqDTO = new SubgroupReqDTO();
        subgroupReqDTO.setName("默认分组");
        subgroupReqDTO.setIsFixSubgroup(0);
        subgroupReqDTO.setPickNum(0);
        subgroupReqDTO.setSort(0);
        final SubItemSkuReqDTO subItemSkuReqDTO = new SubItemSkuReqDTO();
        subItemSkuReqDTO.setSkuGuid("skuGuid");
        subItemSkuReqDTO.setAddPrice(new BigDecimal("0.00"));
        subItemSkuReqDTO.setIsDefault(0);
        subItemSkuReqDTO.setIsRepeat(0);
        subItemSkuReqDTO.setSort(0);
        subgroupReqDTO.setSubItemSkuList(Arrays.asList(subItemSkuReqDTO));
        itemPkgSaveReqDTO.setSubgroupList(Arrays.asList(subgroupReqDTO));

        when(mockItemHelper.sort(0, "brandGuid", "storeGuid")).thenReturn(0);
        when(mockItemService.count(any(LambdaQueryWrapper.class))).thenReturn(0);

        // Configure ISkuService.saveOrUpdateAndDeleteSku(...).
        final ItemInfoBO itemInfoBO = new ItemInfoBO();
        itemInfoBO.setFrom(0);
        itemInfoBO.setItemGuid("itemGuid");
        itemInfoBO.setBrandGuid("brandGuid");
        itemInfoBO.setBrandName("brandName");
        itemInfoBO.setStoreGuid("storeGuid");
        when(mockSkuService.saveOrUpdateAndDeleteSku(itemInfoBO)).thenReturn(Collections.emptyList());

        // Run the test
        assertThatThrownBy(() -> itemPkgServiceImplUnderTest.saveItemPkg(itemPkgSaveReqDTO))
                .isInstanceOf(BusinessException.class);
        verify(mockItemHelper).validateOrganizeGuid(0, "brandGuid", "storeGuid");

        // Confirm ISkuService.validateSkuCodeRepeat(...).
        final SkuSaveReqDTO skuSaveReqDTO1 = new SkuSaveReqDTO();
        skuSaveReqDTO1.setItemGuid("itemGuid");
        skuSaveReqDTO1.setSkuGuid("skuGuid");
        skuSaveReqDTO1.setName("name");
        skuSaveReqDTO1.setCode("code");
        skuSaveReqDTO1.setMinOrderNum(new BigDecimal("0.00"));
        final List<SkuSaveReqDTO> skuList = Arrays.asList(skuSaveReqDTO1);
        verify(mockSkuService).validateSkuCodeRepeat(skuList, "brandGuid", "storeGuid", 0, 0);
        verify(mockItemService).save(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "guid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0, "pinyin", "nameAbbr", "description",
                        "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code", 0, 0, 0, "videoUrls", 0, false));
    }

    @Test
    public void testUpdateItemPkg() {
        // Setup
        final ItemPkgSaveReqDTO itemPkgSaveReqDTO = new ItemPkgSaveReqDTO();
        itemPkgSaveReqDTO.setFrom(0);
        itemPkgSaveReqDTO.setDataList(Arrays.asList("value"));
        itemPkgSaveReqDTO.setItemGuid("itemGuid");
        itemPkgSaveReqDTO.setStoreGuid("storeGuid");
        itemPkgSaveReqDTO.setBrandGuid("brandGuid");
        itemPkgSaveReqDTO.setItemFrom(0);
        itemPkgSaveReqDTO.setItemType(0);
        itemPkgSaveReqDTO.setHasAttr(0);
        itemPkgSaveReqDTO.setName("name");
        itemPkgSaveReqDTO.setSort(0);
        itemPkgSaveReqDTO.setPictureUrl("pictureUrl");
        final SkuSaveReqDTO skuSaveReqDTO = new SkuSaveReqDTO();
        skuSaveReqDTO.setSkuGuid("skuGuid");
        skuSaveReqDTO.setCode("code");
        skuSaveReqDTO.setMinOrderNum(new BigDecimal("0.00"));
        itemPkgSaveReqDTO.setSkuList(Arrays.asList(skuSaveReqDTO));
        final SubgroupReqDTO subgroupReqDTO = new SubgroupReqDTO();
        subgroupReqDTO.setName("默认分组");
        subgroupReqDTO.setIsFixSubgroup(0);
        subgroupReqDTO.setPickNum(0);
        subgroupReqDTO.setSort(0);
        final SubItemSkuReqDTO subItemSkuReqDTO = new SubItemSkuReqDTO();
        subItemSkuReqDTO.setSkuGuid("skuGuid");
        subItemSkuReqDTO.setAddPrice(new BigDecimal("0.00"));
        subItemSkuReqDTO.setIsDefault(0);
        subItemSkuReqDTO.setIsRepeat(0);
        subItemSkuReqDTO.setSort(0);
        subgroupReqDTO.setSubItemSkuList(Arrays.asList(subItemSkuReqDTO));
        itemPkgSaveReqDTO.setSubgroupList(Arrays.asList(subgroupReqDTO));

        when(mockItemHelper.sort(0, "brandGuid", "storeGuid")).thenReturn(0);
        when(mockItemService.count(any(LambdaQueryWrapper.class))).thenReturn(0);
        when(mockItemService.updateById(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "guid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0, "pinyin", "nameAbbr", "description",
                        "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code", 0, 0, 0, "videoUrls", 0,
                        false))).thenReturn(true);

        // Configure ISubgroupService.saveOrUpdateAndDeleteSubgroup(...).
        final ItemInfoBO itemInfoBO = new ItemInfoBO();
        itemInfoBO.setFrom(0);
        itemInfoBO.setItemGuid("itemGuid");
        itemInfoBO.setBrandGuid("brandGuid");
        itemInfoBO.setBrandName("brandName");
        itemInfoBO.setStoreGuid("storeGuid");
        final List<ItemInfoBO> itemInfoBOList = Arrays.asList(itemInfoBO);
        when(mockSubgroupService.saveOrUpdateAndDeleteSubgroup(itemInfoBOList)).thenReturn(true);

        // Run the test
        final List<String> result = itemPkgServiceImplUnderTest.updateItemPkg(itemPkgSaveReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(Arrays.asList("value"));
        verify(mockItemHelper).validateOrganizeGuid(0, "brandGuid", "storeGuid");

        // Confirm ISkuService.validateSkuCodeRepeat(...).
        final SkuSaveReqDTO skuSaveReqDTO1 = new SkuSaveReqDTO();
        skuSaveReqDTO1.setItemGuid("itemGuid");
        skuSaveReqDTO1.setSkuGuid("skuGuid");
        skuSaveReqDTO1.setName("name");
        skuSaveReqDTO1.setCode("code");
        skuSaveReqDTO1.setMinOrderNum(new BigDecimal("0.00"));
        final List<SkuSaveReqDTO> skuList = Arrays.asList(skuSaveReqDTO1);
        verify(mockSkuService).validateSkuCodeRepeat(skuList, "brandGuid", "storeGuid", 0, 0);

        // Confirm ISkuService.updateById(...).
        final SkuDO entity = new SkuDO();
        entity.setGuid("ebe01d32-a995-4978-8e46-7f592be34a37");
        entity.setStoreGuid("storeGuid");
        entity.setItemGuid("guid");
        entity.setName("name");
        entity.setCode("code");
        entity.setIsRack(0);
        entity.setIsEnable(false);
        verify(mockSkuService).updateById(entity);
        verify(mockItemHelper).deletePicUrl("itemGuid");
    }

    @Test
    public void testUpdateItemPkg_IItemServiceUpdateByIdReturnsFalse() {
        // Setup
        final ItemPkgSaveReqDTO itemPkgSaveReqDTO = new ItemPkgSaveReqDTO();
        itemPkgSaveReqDTO.setFrom(0);
        itemPkgSaveReqDTO.setDataList(Arrays.asList("value"));
        itemPkgSaveReqDTO.setItemGuid("itemGuid");
        itemPkgSaveReqDTO.setStoreGuid("storeGuid");
        itemPkgSaveReqDTO.setBrandGuid("brandGuid");
        itemPkgSaveReqDTO.setItemFrom(0);
        itemPkgSaveReqDTO.setItemType(0);
        itemPkgSaveReqDTO.setHasAttr(0);
        itemPkgSaveReqDTO.setName("name");
        itemPkgSaveReqDTO.setSort(0);
        itemPkgSaveReqDTO.setPictureUrl("pictureUrl");
        final SkuSaveReqDTO skuSaveReqDTO = new SkuSaveReqDTO();
        skuSaveReqDTO.setSkuGuid("skuGuid");
        skuSaveReqDTO.setCode("code");
        skuSaveReqDTO.setMinOrderNum(new BigDecimal("0.00"));
        itemPkgSaveReqDTO.setSkuList(Arrays.asList(skuSaveReqDTO));
        final SubgroupReqDTO subgroupReqDTO = new SubgroupReqDTO();
        subgroupReqDTO.setName("默认分组");
        subgroupReqDTO.setIsFixSubgroup(0);
        subgroupReqDTO.setPickNum(0);
        subgroupReqDTO.setSort(0);
        final SubItemSkuReqDTO subItemSkuReqDTO = new SubItemSkuReqDTO();
        subItemSkuReqDTO.setSkuGuid("skuGuid");
        subItemSkuReqDTO.setAddPrice(new BigDecimal("0.00"));
        subItemSkuReqDTO.setIsDefault(0);
        subItemSkuReqDTO.setIsRepeat(0);
        subItemSkuReqDTO.setSort(0);
        subgroupReqDTO.setSubItemSkuList(Arrays.asList(subItemSkuReqDTO));
        itemPkgSaveReqDTO.setSubgroupList(Arrays.asList(subgroupReqDTO));

        when(mockItemHelper.sort(0, "brandGuid", "storeGuid")).thenReturn(0);
        when(mockItemService.count(any(LambdaQueryWrapper.class))).thenReturn(0);
        when(mockItemService.updateById(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "guid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0, "pinyin", "nameAbbr", "description",
                        "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code", 0, 0, 0, "videoUrls", 0,
                        false))).thenReturn(false);

        // Run the test
        assertThatThrownBy(() -> itemPkgServiceImplUnderTest.updateItemPkg(itemPkgSaveReqDTO))
                .isInstanceOf(BusinessException.class);
        verify(mockItemHelper).validateOrganizeGuid(0, "brandGuid", "storeGuid");

        // Confirm ISkuService.validateSkuCodeRepeat(...).
        final SkuSaveReqDTO skuSaveReqDTO1 = new SkuSaveReqDTO();
        skuSaveReqDTO1.setItemGuid("itemGuid");
        skuSaveReqDTO1.setSkuGuid("skuGuid");
        skuSaveReqDTO1.setName("name");
        skuSaveReqDTO1.setCode("code");
        skuSaveReqDTO1.setMinOrderNum(new BigDecimal("0.00"));
        final List<SkuSaveReqDTO> skuList = Arrays.asList(skuSaveReqDTO1);
        verify(mockSkuService).validateSkuCodeRepeat(skuList, "brandGuid", "storeGuid", 0, 0);
    }

    @Test
    public void testUpdateItemPkg_ISubgroupServiceReturnsFalse() {
        // Setup
        final ItemPkgSaveReqDTO itemPkgSaveReqDTO = new ItemPkgSaveReqDTO();
        itemPkgSaveReqDTO.setFrom(0);
        itemPkgSaveReqDTO.setDataList(Arrays.asList("value"));
        itemPkgSaveReqDTO.setItemGuid("itemGuid");
        itemPkgSaveReqDTO.setStoreGuid("storeGuid");
        itemPkgSaveReqDTO.setBrandGuid("brandGuid");
        itemPkgSaveReqDTO.setItemFrom(0);
        itemPkgSaveReqDTO.setItemType(0);
        itemPkgSaveReqDTO.setHasAttr(0);
        itemPkgSaveReqDTO.setName("name");
        itemPkgSaveReqDTO.setSort(0);
        itemPkgSaveReqDTO.setPictureUrl("pictureUrl");
        final SkuSaveReqDTO skuSaveReqDTO = new SkuSaveReqDTO();
        skuSaveReqDTO.setSkuGuid("skuGuid");
        skuSaveReqDTO.setCode("code");
        skuSaveReqDTO.setMinOrderNum(new BigDecimal("0.00"));
        itemPkgSaveReqDTO.setSkuList(Arrays.asList(skuSaveReqDTO));
        final SubgroupReqDTO subgroupReqDTO = new SubgroupReqDTO();
        subgroupReqDTO.setName("默认分组");
        subgroupReqDTO.setIsFixSubgroup(0);
        subgroupReqDTO.setPickNum(0);
        subgroupReqDTO.setSort(0);
        final SubItemSkuReqDTO subItemSkuReqDTO = new SubItemSkuReqDTO();
        subItemSkuReqDTO.setSkuGuid("skuGuid");
        subItemSkuReqDTO.setAddPrice(new BigDecimal("0.00"));
        subItemSkuReqDTO.setIsDefault(0);
        subItemSkuReqDTO.setIsRepeat(0);
        subItemSkuReqDTO.setSort(0);
        subgroupReqDTO.setSubItemSkuList(Arrays.asList(subItemSkuReqDTO));
        itemPkgSaveReqDTO.setSubgroupList(Arrays.asList(subgroupReqDTO));

        when(mockItemHelper.sort(0, "brandGuid", "storeGuid")).thenReturn(0);
        when(mockItemService.count(any(LambdaQueryWrapper.class))).thenReturn(0);
        when(mockItemService.updateById(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "guid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0, "pinyin", "nameAbbr", "description",
                        "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code", 0, 0, 0, "videoUrls", 0,
                        false))).thenReturn(true);

        // Configure ISubgroupService.saveOrUpdateAndDeleteSubgroup(...).
        final ItemInfoBO itemInfoBO = new ItemInfoBO();
        itemInfoBO.setFrom(0);
        itemInfoBO.setItemGuid("itemGuid");
        itemInfoBO.setBrandGuid("brandGuid");
        itemInfoBO.setBrandName("brandName");
        itemInfoBO.setStoreGuid("storeGuid");
        final List<ItemInfoBO> itemInfoBOList = Arrays.asList(itemInfoBO);
        when(mockSubgroupService.saveOrUpdateAndDeleteSubgroup(itemInfoBOList)).thenReturn(false);

        // Run the test
        assertThatThrownBy(() -> itemPkgServiceImplUnderTest.updateItemPkg(itemPkgSaveReqDTO))
                .isInstanceOf(BusinessException.class);
        verify(mockItemHelper).validateOrganizeGuid(0, "brandGuid", "storeGuid");

        // Confirm ISkuService.validateSkuCodeRepeat(...).
        final SkuSaveReqDTO skuSaveReqDTO1 = new SkuSaveReqDTO();
        skuSaveReqDTO1.setItemGuid("itemGuid");
        skuSaveReqDTO1.setSkuGuid("skuGuid");
        skuSaveReqDTO1.setName("name");
        skuSaveReqDTO1.setCode("code");
        skuSaveReqDTO1.setMinOrderNum(new BigDecimal("0.00"));
        final List<SkuSaveReqDTO> skuList = Arrays.asList(skuSaveReqDTO1);
        verify(mockSkuService).validateSkuCodeRepeat(skuList, "brandGuid", "storeGuid", 0, 0);

        // Confirm ISkuService.updateById(...).
        final SkuDO entity = new SkuDO();
        entity.setGuid("ebe01d32-a995-4978-8e46-7f592be34a37");
        entity.setStoreGuid("storeGuid");
        entity.setItemGuid("guid");
        entity.setName("name");
        entity.setCode("code");
        entity.setIsRack(0);
        entity.setIsEnable(false);
        verify(mockSkuService).updateById(entity);
        verify(mockItemHelper).deletePicUrl("itemGuid");
    }

    @Test
    public void testSaveOrUpdateGroupMealPkg() {
        // Setup
        final ItemGroupMealSaveReqDTO request = new ItemGroupMealSaveReqDTO();
        request.setStoreGuid("storeGuid");
        request.setFrom(0);
        final SkuSaveReqDTO sku = new SkuSaveReqDTO();
        sku.setSkuGuid("skuGuid");
        sku.setCode("code");
        sku.setMinOrderNum(new BigDecimal("0.00"));
        request.setSku(sku);
        final GroupMealSkuSaveReqDTO groupMealSkuSaveReqDTO = new GroupMealSkuSaveReqDTO();
        request.setSkuList(Arrays.asList(groupMealSkuSaveReqDTO));

        // Configure ITypeService.getOne(...).
        final TypeDO typeDO = new TypeDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid", "brandGuid", 0, 0, "description", "iconUrl", 0, 0, 0,
                "pricePlanGuid", 0,1);
        when(mockTypeService.getOne(any(LambdaQueryWrapper.class))).thenReturn(typeDO);

        // Run the test
        final Integer result = itemPkgServiceImplUnderTest.saveOrUpdateGroupMealPkg(request);

        // Verify the results
        assertThat(result).isEqualTo(0);
        verify(mockItemService).saveOrUpdate(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "guid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0, "pinyin", "nameAbbr", "description",
                        "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code", 0, 0, 0, "videoUrls", 0, false));

        // Confirm ISkuService.saveOrUpdate(...).
        final SkuDO entity = new SkuDO();
        entity.setGuid("ebe01d32-a995-4978-8e46-7f592be34a37");
        entity.setStoreGuid("storeGuid");
        entity.setItemGuid("guid");
        entity.setName("name");
        entity.setCode("code");
        entity.setIsRack(0);
        entity.setIsEnable(false);
        verify(mockSkuService).saveOrUpdate(entity);
        verify(mockGroupMealService).saveOrUpdateGroupMeal(Arrays.asList(
                new GroupMealSkuSaveReqDTO("be774612-938b-48b2-834b-f6fdd749d4c8", "itemGuid", "skuGuid",
                        new BigDecimal("0.00"), "storeGuid", 0)), "guid");
    }

    @Test
    public void testRemoveAllRelationStore() {
        // Setup
        final ItemPkgSaveReqDTO itemPkgSaveReqDTO = new ItemPkgSaveReqDTO();
        itemPkgSaveReqDTO.setFrom(0);
        itemPkgSaveReqDTO.setDataList(Arrays.asList("value"));
        itemPkgSaveReqDTO.setItemGuid("itemGuid");
        itemPkgSaveReqDTO.setStoreGuid("storeGuid");
        itemPkgSaveReqDTO.setBrandGuid("brandGuid");
        itemPkgSaveReqDTO.setItemFrom(0);
        itemPkgSaveReqDTO.setItemType(0);
        itemPkgSaveReqDTO.setHasAttr(0);
        itemPkgSaveReqDTO.setName("name");
        itemPkgSaveReqDTO.setSort(0);
        itemPkgSaveReqDTO.setPictureUrl("pictureUrl");
        final SkuSaveReqDTO skuSaveReqDTO = new SkuSaveReqDTO();
        skuSaveReqDTO.setSkuGuid("skuGuid");
        skuSaveReqDTO.setCode("code");
        skuSaveReqDTO.setMinOrderNum(new BigDecimal("0.00"));
        itemPkgSaveReqDTO.setSkuList(Arrays.asList(skuSaveReqDTO));
        final SubgroupReqDTO subgroupReqDTO = new SubgroupReqDTO();
        subgroupReqDTO.setName("默认分组");
        subgroupReqDTO.setIsFixSubgroup(0);
        subgroupReqDTO.setPickNum(0);
        subgroupReqDTO.setSort(0);
        final SubItemSkuReqDTO subItemSkuReqDTO = new SubItemSkuReqDTO();
        subItemSkuReqDTO.setSkuGuid("skuGuid");
        subItemSkuReqDTO.setAddPrice(new BigDecimal("0.00"));
        subItemSkuReqDTO.setIsDefault(0);
        subItemSkuReqDTO.setIsRepeat(0);
        subItemSkuReqDTO.setSort(0);
        subgroupReqDTO.setSubItemSkuList(Arrays.asList(subItemSkuReqDTO));
        itemPkgSaveReqDTO.setSubgroupList(Arrays.asList(subgroupReqDTO));

        // Run the test
        itemPkgServiceImplUnderTest.removeAllRelationStore(itemPkgSaveReqDTO, Arrays.asList("value"));

        // Verify the results
        // Confirm IItemService.removeAllRelationStore(...).
        final ItemStringListDTO itemReqDTO = new ItemStringListDTO();
        itemReqDTO.setStoreGuid("storeGuid");
        itemReqDTO.setFrom(0);
        itemReqDTO.setDataList(Arrays.asList("value"));
        itemReqDTO.setItemList(Arrays.asList("value"));
        itemReqDTO.setRecordId(0L);
        verify(mockItemService).removeAllRelationStore(itemReqDTO, Arrays.asList("value"), "itemGuid", "brandGuid");
    }

    @Test
    public void testListPkgItemInfo() {
        // Setup
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setStoreGuid("storeGuid");
        itemStringListDTO.setFrom(0);
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);

        final ItemInfoRespDTO itemInfoRespDTO = new ItemInfoRespDTO();
        itemInfoRespDTO.setItemGuid("itemGuid");
        itemInfoRespDTO.setItemType(0);
        final SubgroupWebRespDTO subgroupWebRespDTO = new SubgroupWebRespDTO();
        subgroupWebRespDTO.setItemGuid("itemGuid");
        final SubItemSkuWebRespDTO subItemSkuWebRespDTO = new SubItemSkuWebRespDTO();
        subItemSkuWebRespDTO.setSkuGuid("skuGuid");
        subItemSkuWebRespDTO.setItemName("itemName");
        subItemSkuWebRespDTO.setSkuName("skuName");
        subItemSkuWebRespDTO.setTypeGuid("typeGuid");
        subItemSkuWebRespDTO.setItemGuid("itemGuid");
        subgroupWebRespDTO.setSubItemSkuList(Arrays.asList(subItemSkuWebRespDTO));
        itemInfoRespDTO.setSubgroupList(Arrays.asList(subgroupWebRespDTO));
        final List<ItemInfoRespDTO> expectedResult = Arrays.asList(itemInfoRespDTO);

        // Configure IItemService.list(...).
        final List<ItemDO> itemDOS = Arrays.asList(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "guid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0, "pinyin", "nameAbbr", "description",
                        "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code", 0, 0, 0, "videoUrls", 0, false));
        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(itemDOS);

        // Configure ISubgroupService.selectSubgroupListByItemGuidList(...).
        final SubgroupBO subgroupBO = new SubgroupBO();
        subgroupBO.setItemGuid("itemGuid");
        subgroupBO.setSubgroupGuid("subgroupGuid");
        subgroupBO.setName("name");
        subgroupBO.setIsFixSubgroup(0);
        subgroupBO.setPickNum(0);
        final List<SubgroupBO> subgroupBOS = Arrays.asList(subgroupBO);
        when(mockSubgroupService.selectSubgroupListByItemGuidList(Arrays.asList("value"))).thenReturn(subgroupBOS);

        // Configure IItemService.listByIds(...).
        final Collection<ItemDO> itemDOS1 = Arrays.asList(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "guid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0, "pinyin", "nameAbbr", "description",
                        "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code", 0, 0, 0, "videoUrls", 0, false));
        when(mockItemService.listByIds(Arrays.asList("value"))).thenReturn(itemDOS1);

        // Configure ISkuService.listByIds(...).
        final SkuDO skuDO = new SkuDO();
        skuDO.setGuid("ebe01d32-a995-4978-8e46-7f592be34a37");
        skuDO.setStoreGuid("storeGuid");
        skuDO.setItemGuid("guid");
        skuDO.setName("name");
        skuDO.setCode("code");
        skuDO.setIsRack(0);
        skuDO.setIsEnable(false);
        final Collection<SkuDO> skuDOS = Arrays.asList(skuDO);
        when(mockSkuService.listByIds(Arrays.asList("value"))).thenReturn(skuDOS);

        // Run the test
        final List<ItemInfoRespDTO> result = itemPkgServiceImplUnderTest.listPkgItemInfo(itemStringListDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testListPkgItemInfo_IItemServiceListReturnsNoItems() {
        // Setup
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setStoreGuid("storeGuid");
        itemStringListDTO.setFrom(0);
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);

        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<ItemInfoRespDTO> result = itemPkgServiceImplUnderTest.listPkgItemInfo(itemStringListDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testListPkgItemInfo_ISubgroupServiceReturnsNoItems() {
        // Setup
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setStoreGuid("storeGuid");
        itemStringListDTO.setFrom(0);
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);

        final ItemInfoRespDTO itemInfoRespDTO = new ItemInfoRespDTO();
        itemInfoRespDTO.setItemGuid("itemGuid");
        itemInfoRespDTO.setItemType(0);
        final SubgroupWebRespDTO subgroupWebRespDTO = new SubgroupWebRespDTO();
        subgroupWebRespDTO.setItemGuid("itemGuid");
        final SubItemSkuWebRespDTO subItemSkuWebRespDTO = new SubItemSkuWebRespDTO();
        subItemSkuWebRespDTO.setSkuGuid("skuGuid");
        subItemSkuWebRespDTO.setItemName("itemName");
        subItemSkuWebRespDTO.setSkuName("skuName");
        subItemSkuWebRespDTO.setTypeGuid("typeGuid");
        subItemSkuWebRespDTO.setItemGuid("itemGuid");
        subgroupWebRespDTO.setSubItemSkuList(Arrays.asList(subItemSkuWebRespDTO));
        itemInfoRespDTO.setSubgroupList(Arrays.asList(subgroupWebRespDTO));
        final List<ItemInfoRespDTO> expectedResult = Arrays.asList(itemInfoRespDTO);

        // Configure IItemService.list(...).
        final List<ItemDO> itemDOS = Arrays.asList(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "guid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0, "pinyin", "nameAbbr", "description",
                        "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code", 0, 0, 0, "videoUrls", 0, false));
        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(itemDOS);

        when(mockSubgroupService.selectSubgroupListByItemGuidList(Arrays.asList("value")))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<ItemInfoRespDTO> result = itemPkgServiceImplUnderTest.listPkgItemInfo(itemStringListDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testListPkgItemInfo_IItemServiceListByIdsReturnsNoItems() {
        // Setup
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setStoreGuid("storeGuid");
        itemStringListDTO.setFrom(0);
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);

        final ItemInfoRespDTO itemInfoRespDTO = new ItemInfoRespDTO();
        itemInfoRespDTO.setItemGuid("itemGuid");
        itemInfoRespDTO.setItemType(0);
        final SubgroupWebRespDTO subgroupWebRespDTO = new SubgroupWebRespDTO();
        subgroupWebRespDTO.setItemGuid("itemGuid");
        final SubItemSkuWebRespDTO subItemSkuWebRespDTO = new SubItemSkuWebRespDTO();
        subItemSkuWebRespDTO.setSkuGuid("skuGuid");
        subItemSkuWebRespDTO.setItemName("itemName");
        subItemSkuWebRespDTO.setSkuName("skuName");
        subItemSkuWebRespDTO.setTypeGuid("typeGuid");
        subItemSkuWebRespDTO.setItemGuid("itemGuid");
        subgroupWebRespDTO.setSubItemSkuList(Arrays.asList(subItemSkuWebRespDTO));
        itemInfoRespDTO.setSubgroupList(Arrays.asList(subgroupWebRespDTO));
        final List<ItemInfoRespDTO> expectedResult = Arrays.asList(itemInfoRespDTO);

        // Configure IItemService.list(...).
        final List<ItemDO> itemDOS = Arrays.asList(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "guid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0, "pinyin", "nameAbbr", "description",
                        "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code", 0, 0, 0, "videoUrls", 0, false));
        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(itemDOS);

        // Configure ISubgroupService.selectSubgroupListByItemGuidList(...).
        final SubgroupBO subgroupBO = new SubgroupBO();
        subgroupBO.setItemGuid("itemGuid");
        subgroupBO.setSubgroupGuid("subgroupGuid");
        subgroupBO.setName("name");
        subgroupBO.setIsFixSubgroup(0);
        subgroupBO.setPickNum(0);
        final List<SubgroupBO> subgroupBOS = Arrays.asList(subgroupBO);
        when(mockSubgroupService.selectSubgroupListByItemGuidList(Arrays.asList("value"))).thenReturn(subgroupBOS);

        when(mockItemService.listByIds(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Configure ISkuService.listByIds(...).
        final SkuDO skuDO = new SkuDO();
        skuDO.setGuid("ebe01d32-a995-4978-8e46-7f592be34a37");
        skuDO.setStoreGuid("storeGuid");
        skuDO.setItemGuid("guid");
        skuDO.setName("name");
        skuDO.setCode("code");
        skuDO.setIsRack(0);
        skuDO.setIsEnable(false);
        final Collection<SkuDO> skuDOS = Arrays.asList(skuDO);
        when(mockSkuService.listByIds(Arrays.asList("value"))).thenReturn(skuDOS);

        // Run the test
        final List<ItemInfoRespDTO> result = itemPkgServiceImplUnderTest.listPkgItemInfo(itemStringListDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testListPkgItemInfo_ISkuServiceReturnsNoItems() {
        // Setup
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setStoreGuid("storeGuid");
        itemStringListDTO.setFrom(0);
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);

        final ItemInfoRespDTO itemInfoRespDTO = new ItemInfoRespDTO();
        itemInfoRespDTO.setItemGuid("itemGuid");
        itemInfoRespDTO.setItemType(0);
        final SubgroupWebRespDTO subgroupWebRespDTO = new SubgroupWebRespDTO();
        subgroupWebRespDTO.setItemGuid("itemGuid");
        final SubItemSkuWebRespDTO subItemSkuWebRespDTO = new SubItemSkuWebRespDTO();
        subItemSkuWebRespDTO.setSkuGuid("skuGuid");
        subItemSkuWebRespDTO.setItemName("itemName");
        subItemSkuWebRespDTO.setSkuName("skuName");
        subItemSkuWebRespDTO.setTypeGuid("typeGuid");
        subItemSkuWebRespDTO.setItemGuid("itemGuid");
        subgroupWebRespDTO.setSubItemSkuList(Arrays.asList(subItemSkuWebRespDTO));
        itemInfoRespDTO.setSubgroupList(Arrays.asList(subgroupWebRespDTO));
        final List<ItemInfoRespDTO> expectedResult = Arrays.asList(itemInfoRespDTO);

        // Configure IItemService.list(...).
        final List<ItemDO> itemDOS = Arrays.asList(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "guid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0, "pinyin", "nameAbbr", "description",
                        "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code", 0, 0, 0, "videoUrls", 0, false));
        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(itemDOS);

        // Configure ISubgroupService.selectSubgroupListByItemGuidList(...).
        final SubgroupBO subgroupBO = new SubgroupBO();
        subgroupBO.setItemGuid("itemGuid");
        subgroupBO.setSubgroupGuid("subgroupGuid");
        subgroupBO.setName("name");
        subgroupBO.setIsFixSubgroup(0);
        subgroupBO.setPickNum(0);
        final List<SubgroupBO> subgroupBOS = Arrays.asList(subgroupBO);
        when(mockSubgroupService.selectSubgroupListByItemGuidList(Arrays.asList("value"))).thenReturn(subgroupBOS);

        // Configure IItemService.listByIds(...).
        final Collection<ItemDO> itemDOS1 = Arrays.asList(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "guid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0, "pinyin", "nameAbbr", "description",
                        "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code", 0, 0, 0, "videoUrls", 0, false));
        when(mockItemService.listByIds(Arrays.asList("value"))).thenReturn(itemDOS1);

        when(mockSkuService.listByIds(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        final List<ItemInfoRespDTO> result = itemPkgServiceImplUnderTest.listPkgItemInfo(itemStringListDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testSelectList() {
        // Setup
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setFrom(0);
        itemSingleDTO.setData("data");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);

        final TypeSkuRespDTO typeSkuRespDTO = new TypeSkuRespDTO();
        typeSkuRespDTO.setTypeGuid("typeGuid");
        typeSkuRespDTO.setName("name");
        final TypeSkuRespDTO.SkuNameRespDTO skuNameRespDTO = new TypeSkuRespDTO.SkuNameRespDTO();
        skuNameRespDTO.setItemGuid("itemGuid");
        skuNameRespDTO.setItemType(0);
        typeSkuRespDTO.setSkuNameRespDTOList(Arrays.asList(skuNameRespDTO));
        final List<TypeSkuRespDTO> expectedResult = Arrays.asList(typeSkuRespDTO);

        // Configure ITypeService.list(...).
        final List<TypeDO> typeDOS = Arrays.asList(
                new TypeDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "brandGuid", 0, 0, "description", "iconUrl", 0, 0, 0, "pricePlanGuid", 0,1));
        when(mockTypeService.list(any(LambdaQueryWrapper.class))).thenReturn(typeDOS);

        // Configure IItemService.list(...).
        final List<ItemDO> itemDOS = Arrays.asList(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "guid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0, "pinyin", "nameAbbr", "description",
                        "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code", 0, 0, 0, "videoUrls", 0, false));
        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(itemDOS);

        // Configure ISkuService.list(...).
        final SkuDO skuDO = new SkuDO();
        skuDO.setGuid("ebe01d32-a995-4978-8e46-7f592be34a37");
        skuDO.setStoreGuid("storeGuid");
        skuDO.setItemGuid("guid");
        skuDO.setName("name");
        skuDO.setCode("code");
        skuDO.setIsRack(0);
        skuDO.setIsEnable(false);
        final List<SkuDO> skuDOS = Arrays.asList(skuDO);
        when(mockSkuService.list(any(LambdaQueryWrapper.class))).thenReturn(skuDOS);

        // Configure ISubgroupService.list(...).
        final SubgroupDO subgroupDO = new SubgroupDO();
        subgroupDO.setId(0L);
        subgroupDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        subgroupDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        subgroupDO.setItemGuid("itemGuid");
        subgroupDO.setPickNum(0);
        final List<SubgroupDO> subgroupDOS = Arrays.asList(subgroupDO);
        when(mockSubgroupService.list(any(LambdaQueryWrapper.class))).thenReturn(subgroupDOS);

        // Run the test
        final List<TypeSkuRespDTO> result = itemPkgServiceImplUnderTest.selectList(itemSingleDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testSelectList_ITypeServiceReturnsNoItems() {
        // Setup
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setFrom(0);
        itemSingleDTO.setData("data");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);

        when(mockTypeService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        assertThatThrownBy(() -> itemPkgServiceImplUnderTest.selectList(itemSingleDTO))
                .isInstanceOf(BusinessException.class);
    }

    @Test
    public void testSelectList_IItemServiceReturnsNoItems() {
        // Setup
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setFrom(0);
        itemSingleDTO.setData("data");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);

        // Configure ITypeService.list(...).
        final List<TypeDO> typeDOS = Arrays.asList(
                new TypeDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "brandGuid", 0, 0, "description", "iconUrl", 0, 0, 0, "pricePlanGuid", 0,1));
        when(mockTypeService.list(any(LambdaQueryWrapper.class))).thenReturn(typeDOS);

        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        assertThatThrownBy(() -> itemPkgServiceImplUnderTest.selectList(itemSingleDTO))
                .isInstanceOf(BusinessException.class);
    }

    @Test
    public void testSelectList_ISkuServiceReturnsNoItems() {
        // Setup
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setFrom(0);
        itemSingleDTO.setData("data");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);

        // Configure ITypeService.list(...).
        final List<TypeDO> typeDOS = Arrays.asList(
                new TypeDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "brandGuid", 0, 0, "description", "iconUrl", 0, 0, 0, "pricePlanGuid", 0,1));
        when(mockTypeService.list(any(LambdaQueryWrapper.class))).thenReturn(typeDOS);

        // Configure IItemService.list(...).
        final List<ItemDO> itemDOS = Arrays.asList(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "guid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0, "pinyin", "nameAbbr", "description",
                        "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code", 0, 0, 0, "videoUrls", 0, false));
        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(itemDOS);

        when(mockSkuService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        assertThatThrownBy(() -> itemPkgServiceImplUnderTest.selectList(itemSingleDTO))
                .isInstanceOf(BusinessException.class);
    }

    @Test
    public void testSelectList_ISubgroupServiceReturnsNoItems() {
        // Setup
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setFrom(0);
        itemSingleDTO.setData("data");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);

        final TypeSkuRespDTO typeSkuRespDTO = new TypeSkuRespDTO();
        typeSkuRespDTO.setTypeGuid("typeGuid");
        typeSkuRespDTO.setName("name");
        final TypeSkuRespDTO.SkuNameRespDTO skuNameRespDTO = new TypeSkuRespDTO.SkuNameRespDTO();
        skuNameRespDTO.setItemGuid("itemGuid");
        skuNameRespDTO.setItemType(0);
        typeSkuRespDTO.setSkuNameRespDTOList(Arrays.asList(skuNameRespDTO));
        final List<TypeSkuRespDTO> expectedResult = Arrays.asList(typeSkuRespDTO);

        // Configure ITypeService.list(...).
        final List<TypeDO> typeDOS = Arrays.asList(
                new TypeDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "brandGuid", 0, 0, "description", "iconUrl", 0, 0, 0, "pricePlanGuid", 0,1));
        when(mockTypeService.list(any(LambdaQueryWrapper.class))).thenReturn(typeDOS);

        // Configure IItemService.list(...).
        final List<ItemDO> itemDOS = Arrays.asList(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "guid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0, "pinyin", "nameAbbr", "description",
                        "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code", 0, 0, 0, "videoUrls", 0, false));
        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(itemDOS);

        // Configure ISkuService.list(...).
        final SkuDO skuDO = new SkuDO();
        skuDO.setGuid("ebe01d32-a995-4978-8e46-7f592be34a37");
        skuDO.setStoreGuid("storeGuid");
        skuDO.setItemGuid("guid");
        skuDO.setName("name");
        skuDO.setCode("code");
        skuDO.setIsRack(0);
        skuDO.setIsEnable(false);
        final List<SkuDO> skuDOS = Arrays.asList(skuDO);
        when(mockSkuService.list(any(LambdaQueryWrapper.class))).thenReturn(skuDOS);

        when(mockSubgroupService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<TypeSkuRespDTO> result = itemPkgServiceImplUnderTest.selectList(itemSingleDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
