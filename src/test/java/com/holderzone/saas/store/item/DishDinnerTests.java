package com.holderzone.saas.store.item;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.common.UserInfoDTO;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.req.*;
import com.holderzone.saas.store.item.util.SpringContextUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.Arrays;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@SpringBootTest
public class DishDinnerTests {
    @Autowired
    private WebApplicationContext wac;
    private MockMvc mockMvc;

    @Autowired
    private ApplicationContext applicationContext;
    @Before
    public void setup() {
        SpringContextUtils.getInstance().setCfgContext((ConfigurableApplicationContext) applicationContext);
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();   //构造MockMvc
    }

    @Test
    public void test1() throws Exception {

        ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("6478514571754243073");
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setEnterpriseGuid("6491846954039715841");
        String encode = URLEncoder.encode(JacksonUtils.writeValueAsString(userInfoDTO), "utf-8");
        MvcResult mvcResult = mockMvc.perform(post("/attr/get_attr_sort").header("userInfo",encode).accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JacksonUtils.writeValueAsString(itemSingleDTO)))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }

    @Test
    public void test11() throws Exception {

        ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("6478514571754243073");
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setEnterpriseGuid("6491846954039715841");
        String encode = URLEncoder.encode(JacksonUtils.writeValueAsString(userInfoDTO), "utf-8");
        MvcResult mvcResult = mockMvc.perform(post("/type/get_sort").header("userInfo",encode).accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JacksonUtils.writeValueAsString(itemSingleDTO)))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }

    @Test
    public void testgetTypeSOrtBrand() throws Exception {

        ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("brandGuid");
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setEnterpriseGuid("6491846954039715841");
        String encode = URLEncoder.encode(JacksonUtils.writeValueAsString(userInfoDTO), "utf-8");
        MvcResult mvcResult = mockMvc.perform(post("/type/get_sort_from_brand").header("userInfo",encode).accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JacksonUtils.writeValueAsString(itemSingleDTO)))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }

    @Test
    public void test2() throws Exception {

        TypeReqDTO typeReqDTO = new TypeReqDTO();
        typeReqDTO.setStoreGuid("6478514571754243073");
        typeReqDTO.setName("菜的分类");
        typeReqDTO.setSort(4);
        typeReqDTO.setIsEnable(1);
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setEnterpriseGuid("6491846954039715841");
        String encode = URLEncoder.encode(JacksonUtils.writeValueAsString(userInfoDTO), "utf-8");
        MvcResult mvcResult = mockMvc.perform(post("/type/save").header("userInfo",encode).accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JacksonUtils.writeValueAsString(typeReqDTO)))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }

    @Test
    public void testTypeUpdate() throws Exception {

        TypeReqDTO typeReqDTO = new TypeReqDTO();
        typeReqDTO.setTypeGuid("6494135121476882433");
        typeReqDTO.setStoreGuid("6478514571754243073");
        typeReqDTO.setName("菜11修改");
        typeReqDTO.setSort(4);
        typeReqDTO.setIsEnable(1);
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setEnterpriseGuid("6491846954039715841");
        String encode = URLEncoder.encode(JacksonUtils.writeValueAsString(userInfoDTO), "utf-8");
        MvcResult mvcResult = mockMvc.perform(post("/type/update").header("userInfo",encode).accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JacksonUtils.writeValueAsString(typeReqDTO)))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }

    @Test
    public void testTypeDelete() throws Exception {

        ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("6493730360721358849");
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setEnterpriseGuid("6491846954039715841");
        String encode = URLEncoder.encode(JacksonUtils.writeValueAsString(userInfoDTO), "utf-8");
        MvcResult mvcResult = mockMvc.perform(post("/type/delete").header("userInfo",encode).accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JacksonUtils.writeValueAsString(itemSingleDTO)))
                .andExpect(status().is4xxClientError()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }

    @Test
    public void test3() throws Exception {

        AttrGroupReqDTO attrGroupReqDTO = new AttrGroupReqDTO();
        attrGroupReqDTO.setName("温度");
        attrGroupReqDTO.setStoreGuid("6478514571754243073");
        attrGroupReqDTO.setSort(2);
        attrGroupReqDTO.setIsEnable(1);
        attrGroupReqDTO.setIsMultiChoice(0);
        attrGroupReqDTO.setIsRequired(0);
        attrGroupReqDTO.setFrom(0);
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setEnterpriseGuid("6491846954039715841");
        String encode = URLEncoder.encode(JacksonUtils.writeValueAsString(userInfoDTO), "utf-8");
        MvcResult mvcResult = mockMvc.perform(post("/attr/save_attr_group").header("userInfo",encode).accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JacksonUtils.writeValueAsString(attrGroupReqDTO)))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }

    @Test
    public void test4() throws Exception {

        AttrReqDTO attrReqDTO = new AttrReqDTO();
        attrReqDTO.setName("冰");
        attrReqDTO.setAttrGroupGuid("6509337355040693249");
        attrReqDTO.setFrom(1);
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setEnterpriseGuid("6506431195651982337");
        String encode = URLEncoder.encode(JacksonUtils.writeValueAsString(userInfoDTO), "utf-8");
        MvcResult mvcResult = mockMvc.perform(post("/attr/save_attr").header("userInfo",encode).accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JacksonUtils.writeValueAsString(attrReqDTO)))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }

    @Test
    public void test5() throws Exception {

        ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("6478514571754243073");
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setEnterpriseGuid("6491846954039715841");
        String encode = URLEncoder.encode(JacksonUtils.writeValueAsString(userInfoDTO), "utf-8");
        MvcResult mvcResult = mockMvc.perform(post("/attr/list").header("userInfo",encode).accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JacksonUtils.writeValueAsString(itemSingleDTO)))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }

    @Test
    public void listAttr() throws Exception {

        ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("6492293472978926593");
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setEnterpriseGuid("6491846954039715841");
        String encode = URLEncoder.encode(JacksonUtils.writeValueAsString(userInfoDTO), "utf-8");
        MvcResult mvcResult = mockMvc.perform(post("/attr/select_attr_list_of_attr_group").header("userInfo",encode).accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JacksonUtils.writeValueAsString(itemSingleDTO)))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }

    @Test
    public void test6() throws Exception {

        ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("6478514571754243073");
        itemSingleDTO.setFrom(0);
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setEnterpriseGuid("6491846954039715841");
        String encode = URLEncoder.encode(JacksonUtils.writeValueAsString(userInfoDTO), "utf-8");
        MvcResult mvcResult = mockMvc.perform(post("/type/query_type").header("userInfo",encode).accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JacksonUtils.writeValueAsString(itemSingleDTO)))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }

    @Test
    public void itemList() throws Exception {

        ItemQueryReqDTO itemQueryReqDTO = new ItemQueryReqDTO();
        itemQueryReqDTO.setStoreGuid("6506453252643487745");
//        itemQueryReqDTO.setBrandGuid("ewqewqe11111");
        itemQueryReqDTO.setIsRack(0);
        itemQueryReqDTO.setPageSize(100);
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setEnterpriseGuid("6506431195651982337");
        String encode = URLEncoder.encode(JacksonUtils.writeValueAsString(userInfoDTO), "utf-8");
        MvcResult mvcResult = mockMvc.perform(post("/item/select_item_for_web").header("userInfo",encode).accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JacksonUtils.writeValueAsString(itemQueryReqDTO)))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }

    @Test
    public void listForApp() throws Exception {

        BaseDTO itemQueryReqDTO = new BaseDTO();
        itemQueryReqDTO.setStoreGuid("6478514571754243073");
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setEnterpriseGuid("6491846954039715841");
        String encode = URLEncoder.encode(JacksonUtils.writeValueAsString(userInfoDTO), "utf-8");
        MvcResult mvcResult = mockMvc.perform(post("/item/query_for_synchronize").header("userInfo",encode).accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JacksonUtils.writeValueAsString(itemQueryReqDTO)))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }
    @Test
    public void itemInfo() throws Exception {

        ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("6503869768951919617");
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setEnterpriseGuid("6491846954039715841");
        String encode = URLEncoder.encode(JacksonUtils.writeValueAsString(userInfoDTO), "utf-8");
        MvcResult mvcResult = mockMvc.perform(post("/item/get_item_info").header("userInfo",encode).accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JacksonUtils.writeValueAsString(itemSingleDTO)))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }

    @Test
    public void itemSave() throws Exception {

        ItemReqDTO itemSaveReqDTO = new ItemReqDTO();
        itemSaveReqDTO.setFrom(0);
        itemSaveReqDTO.setTypeGuid("6493734393178638337");
        itemSaveReqDTO.setName("回锅肉炒腊肉");
        itemSaveReqDTO.setStoreGuid("647851457175424307");
        itemSaveReqDTO.setIsBestseller(1);
        itemSaveReqDTO.setIsNew(1);
        itemSaveReqDTO.setIsSign(1);
        itemSaveReqDTO.setItemFrom(0);
        itemSaveReqDTO.setItemType(2);
        itemSaveReqDTO.setNameAbbr("xc");
        itemSaveReqDTO.setSort(1);
        itemSaveReqDTO.setPinyin("XC");
        SkuSaveReqDTO skuSaveReqDTO = new SkuSaveReqDTO();
        skuSaveReqDTO.setName("big");
        skuSaveReqDTO.setMinOrderNum(BigDecimal.ONE);
        skuSaveReqDTO.setSalePrice(BigDecimal.TEN);
        skuSaveReqDTO.setUnit("个");
        skuSaveReqDTO.setIsJoinElm(0);
        skuSaveReqDTO.setIsJoinMt(0);
        skuSaveReqDTO.setIsJoinWeChat(0);
        skuSaveReqDTO.setIsMemberDiscount(0);
        skuSaveReqDTO.setIsWholeDiscount(0);
        skuSaveReqDTO.setIsRack(1);
        skuSaveReqDTO.setCode("5879781");
        SkuSaveReqDTO skuSaveReqDTO1 = new SkuSaveReqDTO();
        skuSaveReqDTO1.setName("xiao");
        skuSaveReqDTO1.setMinOrderNum(BigDecimal.ONE);
        skuSaveReqDTO1.setSalePrice(new BigDecimal("9"));
        skuSaveReqDTO1.setUnit("个");
        skuSaveReqDTO1.setIsJoinElm(0);
        skuSaveReqDTO1.setIsJoinMt(0);
        skuSaveReqDTO1.setIsJoinWeChat(0);
        skuSaveReqDTO1.setIsMemberDiscount(1);
        skuSaveReqDTO1.setIsWholeDiscount(0);
        skuSaveReqDTO1.setIsRack(1);
        itemSaveReqDTO.setSkuList(Arrays.asList(skuSaveReqDTO));
        ItemAttrGroupReqDTO itemAttrGroupReqDTO = new ItemAttrGroupReqDTO();
        ItemAttrReqDTO itemAttrReqDTO = new ItemAttrReqDTO();
        itemAttrReqDTO.setAttrGuid("6493730844950342657");
        itemAttrReqDTO.setIsDefault(1);
        ItemAttrReqDTO itemAttrReqDTO1 = new ItemAttrReqDTO();
        itemAttrReqDTO1.setAttrGuid("6493005829437267969");
        itemAttrReqDTO1.setIsDefault(1);
        itemAttrGroupReqDTO.setAttrList(Arrays.asList(itemAttrReqDTO));
        itemAttrGroupReqDTO.setIsRequired(1);
        itemAttrGroupReqDTO.setIsMultiChoice(1);
        itemAttrGroupReqDTO.setWithDefault(1);
        itemAttrGroupReqDTO.setAttrGroupGuid("6493730628056067073");

        itemSaveReqDTO.setAttrGroupList(Arrays.asList(itemAttrGroupReqDTO));
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setEnterpriseGuid("6491846954039715841");
        String encode = URLEncoder.encode(JacksonUtils.writeValueAsString(userInfoDTO), "utf-8");
        MvcResult mvcResult = mockMvc.perform(post("/item/save_item").header("userInfo",encode).accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JacksonUtils.writeValueAsString(itemSaveReqDTO)))
                .andExpect(status().is4xxClientError()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }

    @Test
    public void itemUpdate() throws Exception {

        ItemReqDTO itemSaveReqDTO = new ItemReqDTO();
        itemSaveReqDTO.setFrom(1);
        itemSaveReqDTO.setItemGuid("6507544896313528321");
        itemSaveReqDTO.setBrandGuid("6506433399404625921");
        itemSaveReqDTO.setTypeGuid("6507543516105064449");
        itemSaveReqDTO.setName("回锅肉修改debug");
        itemSaveReqDTO.setIsBestseller(1);
        itemSaveReqDTO.setIsNew(1);
        itemSaveReqDTO.setIsSign(1);
        itemSaveReqDTO.setItemFrom(1);
        itemSaveReqDTO.setItemType(2);
        itemSaveReqDTO.setNameAbbr("csxgzjgx");
        itemSaveReqDTO.setSort(1);
        itemSaveReqDTO.setPinyin("CSXGZJGX");
        itemSaveReqDTO.setHasAttr(2);
        SkuSaveReqDTO skuSaveReqDTO = new SkuSaveReqDTO();
        skuSaveReqDTO.setSkuGuid("6507544896326113281");
        skuSaveReqDTO.setMinOrderNum(BigDecimal.ONE);
        skuSaveReqDTO.setSalePrice(BigDecimal.ONE);
        skuSaveReqDTO.setUnit("组");
        skuSaveReqDTO.setIsJoinElm(0);
        skuSaveReqDTO.setIsJoinMt(0);
        skuSaveReqDTO.setIsJoinWeChat(0);
        skuSaveReqDTO.setIsMemberDiscount(0);
        skuSaveReqDTO.setIsWholeDiscount(0);
        skuSaveReqDTO.setIsRack(1);
        SkuSaveReqDTO skuSaveReqDTO1 = new SkuSaveReqDTO();
        skuSaveReqDTO1.setName("xiao11111");
        skuSaveReqDTO1.setMinOrderNum(BigDecimal.ONE);
        skuSaveReqDTO1.setSalePrice(new BigDecimal("9"));
        skuSaveReqDTO1.setUnit("个1");
        skuSaveReqDTO1.setIsJoinElm(0);
        skuSaveReqDTO1.setIsJoinMt(0);
        skuSaveReqDTO1.setIsJoinWeChat(0);
        skuSaveReqDTO1.setIsMemberDiscount(1);
        skuSaveReqDTO1.setIsWholeDiscount(0);
        skuSaveReqDTO1.setIsRack(1);
        itemSaveReqDTO.setSkuList(Arrays.asList(skuSaveReqDTO));
        ItemAttrGroupReqDTO itemAttrGroupReqDTO = new ItemAttrGroupReqDTO();
        ItemAttrReqDTO itemAttrReqDTO = new ItemAttrReqDTO();
        itemAttrReqDTO.setAttrGuid("6510759774820229121");
        itemAttrReqDTO.setIsDefault(0);
        ItemAttrReqDTO itemAttrReqDTO1 = new ItemAttrReqDTO();
        itemAttrReqDTO1.setAttrGuid("6493005829437267969");
        itemAttrReqDTO1.setIsDefault(1);
        itemAttrGroupReqDTO.setAttrList(Arrays.asList(itemAttrReqDTO));
        itemAttrGroupReqDTO.setIsRequired(1);
        itemAttrGroupReqDTO.setIsMultiChoice(0);
        itemAttrGroupReqDTO.setWithDefault(0);
        itemAttrGroupReqDTO.setAttrGroupGuid("6509337355040693249");

        itemSaveReqDTO.setAttrGroupList(Arrays.asList(itemAttrGroupReqDTO));
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setEnterpriseGuid("6506431195651982337");
        String encode = URLEncoder.encode(JacksonUtils.writeValueAsString(userInfoDTO), "utf-8");
        MvcResult mvcResult = mockMvc.perform(post("/item/update_item").header("userInfo",encode).accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JacksonUtils.writeValueAsString(itemSaveReqDTO)))
                .andExpect(status().is4xxClientError()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }

    @Test
    public void listSku() throws Exception {

        ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("6478514571754243073");
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setEnterpriseGuid("6491846954039715841");
        String encode = URLEncoder.encode(JacksonUtils.writeValueAsString(userInfoDTO), "utf-8");
        MvcResult mvcResult = mockMvc.perform(post("/item_pkg/select_sku_list").header("userInfo",encode).accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JacksonUtils.writeValueAsString(itemSingleDTO)))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }



    @Test
    public void count_type_or_item() throws Exception {

        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setEnterpriseGuid("6506431195651982337");
        String encode = URLEncoder.encode(JacksonUtils.writeValueAsString(userInfoDTO), "utf-8");
        MvcResult mvcResult = mockMvc.perform(post("/item/count_type_or_item").param("brandGuid","6506433399404625921").header("userInfo",encode).accept(MediaType.APPLICATION_JSON_VALUE))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }



    @Test
    public void listAttrItem() throws Exception {

        ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("6478514571754243073");
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setEnterpriseGuid("6491846954039715841");
        String encode = URLEncoder.encode(JacksonUtils.writeValueAsString(userInfoDTO), "utf-8");
        MvcResult mvcResult = mockMvc.perform(post("/attr/select_attr_list_for_save_item").header("userInfo",encode).accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JacksonUtils.writeValueAsString(itemSingleDTO)))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }

    @Test
    public void itemPkgSave() throws Exception {

        ItemPkgSaveReqDTO itemSaveReqDTO = new ItemPkgSaveReqDTO();
        itemSaveReqDTO.setTypeGuid("6493734393178638337");
        itemSaveReqDTO.setName("青菜套餐2");
        itemSaveReqDTO.setStoreGuid("6478514571754243073");
        itemSaveReqDTO.setIsBestseller(1);
        itemSaveReqDTO.setIsNew(1);
        itemSaveReqDTO.setIsSign(1);
        itemSaveReqDTO.setItemFrom(0);
        itemSaveReqDTO.setItemType(2);
        itemSaveReqDTO.setNameAbbr("qctc");
        itemSaveReqDTO.setSort(2);
        itemSaveReqDTO.setPinyin("QCTC");
        SkuSaveReqDTO skuSaveReqDTO = new SkuSaveReqDTO();
        skuSaveReqDTO.setMinOrderNum(BigDecimal.ONE);
        skuSaveReqDTO.setSalePrice(BigDecimal.TEN);
        skuSaveReqDTO.setUnit("个");
        skuSaveReqDTO.setIsJoinElm(0);
        skuSaveReqDTO.setIsJoinMt(0);
        skuSaveReqDTO.setIsJoinWeChat(0);
        skuSaveReqDTO.setIsMemberDiscount(0);
        skuSaveReqDTO.setIsWholeDiscount(0);
        skuSaveReqDTO.setIsRack(1);
        itemSaveReqDTO.setSkuList(Arrays.asList(skuSaveReqDTO));

        SubgroupReqDTO subgroupReqDTO = new SubgroupReqDTO();
        subgroupReqDTO.setPickNum(0);
        SubItemSkuReqDTO subItemSkuReqDTO = new SubItemSkuReqDTO();
        subItemSkuReqDTO.setItemGuid("6493736384256340993");
        subItemSkuReqDTO.setIsDefault(0);
        subItemSkuReqDTO.setIsRepeat(0);
        subItemSkuReqDTO.setAddPrice(BigDecimal.ZERO);
        subItemSkuReqDTO.setItemNum(BigDecimal.ONE);
        subItemSkuReqDTO.setSkuGuid("6493736384281512961");
        subgroupReqDTO.setSubItemSkuList(Arrays.asList(subItemSkuReqDTO));

        itemSaveReqDTO.setSubgroupList(Arrays.asList(subgroupReqDTO));
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setEnterpriseGuid("6491846954039715841");
        String encode = URLEncoder.encode(JacksonUtils.writeValueAsString(userInfoDTO), "utf-8");
        MvcResult mvcResult = mockMvc.perform(post("/item_pkg/save_pkg").header("userInfo",encode).accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JacksonUtils.writeValueAsString(itemSaveReqDTO)))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }




    @Test
    public void deleteItem() throws Exception {

        ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("6503869768951919617");
        itemSingleDTO.setFrom(1);
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setEnterpriseGuid("6491846954039715841");
        String encode = URLEncoder.encode(JacksonUtils.writeValueAsString(userInfoDTO), "utf-8");
        MvcResult mvcResult = mockMvc.perform(post("/item/delete_item").header("userInfo",encode).accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JacksonUtils.writeValueAsString(itemSingleDTO)))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }

    @Test
    public void countItem() throws Exception {

        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setEnterpriseGuid("6491846954039715841");
        String encode = URLEncoder.encode(JacksonUtils.writeValueAsString(userInfoDTO), "utf-8");
        MvcResult mvcResult = mockMvc.perform(post("/item/count_type_or_item").header("userInfo",encode)
                .param("brandGuid","brandGuid")
                .contentType(MediaType.APPLICATION_FORM_URLENCODED))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }

    @Test
    public void organizeTest() throws Exception {

        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setEnterpriseGuid("6491846954039715841");
        String encode = URLEncoder.encode(JacksonUtils.writeValueAsString(userInfoDTO), "utf-8");
        MvcResult mvcResult = mockMvc.perform(post("/item/test_get_brand_guid").header("userInfo",encode)
                .param("storeGuid","6494464654763360110")
                .contentType(MediaType.APPLICATION_FORM_URLENCODED))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }

    @Test
    public void pkgInfo() throws Exception {

        ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("6503869768951919617");
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setEnterpriseGuid("6491846954039715841");
        String encode = URLEncoder.encode(JacksonUtils.writeValueAsString(userInfoDTO), "utf-8");
        MvcResult mvcResult = mockMvc.perform(post("/item/get_item_info").header("userInfo",encode).accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JacksonUtils.writeValueAsString(itemSingleDTO)))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }

    @Test
    public void push() throws Exception {

        PushItemReqDTO pushItemReqDTO = new PushItemReqDTO();
        pushItemReqDTO.setStoreGuidList(Arrays.asList("6508998037765881857"));
        pushItemReqDTO.setSkuGuidList(Arrays.asList("6510785719921580033"));
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setEnterpriseGuid("6506431195651982337");
        String encode = URLEncoder.encode(JacksonUtils.writeValueAsString(userInfoDTO), "utf-8");
        MvcResult mvcResult = mockMvc.perform(post("/item/push_item").header("userInfo",encode).accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JacksonUtils.writeValueAsString(pushItemReqDTO)))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }

    @Test
    public void updateFixPkg() throws Exception{
        ItemPkgSaveReqDTO item = new ItemPkgSaveReqDTO();
        item.setFrom(0);
        item.setName("修改用固定套餐");
        item.setTypeGuid("6493730528983390209");
        item.setPinyin("gdtc");
        item.setNameAbbr("tccaiXiuGai");
        item.setStoreGuid("6478514571754243073");
        item.setItemType(1);
        item.setIsNew(0);
        item.setIsSign(1);
        item.setIsBestseller(0);
        item.setSort(56);
        item.setHasAttr(0);
        SkuSaveReqDTO skuSaveReqDTO = new SkuSaveReqDTO();

        skuSaveReqDTO.setIsRack(1);
        skuSaveReqDTO.setIsWholeDiscount(1);
        skuSaveReqDTO.setIsMemberDiscount(1);
        skuSaveReqDTO.setIsJoinWeChat(1);
        skuSaveReqDTO.setMinOrderNum(new BigDecimal(1));
        skuSaveReqDTO.setUnit("份");
        skuSaveReqDTO.setSalePrice(new BigDecimal(5));
        item.setSkuList(Arrays.asList(skuSaveReqDTO));
        SubgroupReqDTO subgroupReqDTO = new SubgroupReqDTO();
        subgroupReqDTO.setIsFixSubgroup(1);
        subgroupReqDTO.setPickNum(0);
        subgroupReqDTO.setSort(1);
        SubItemSkuReqDTO subItemSkuReqDTO = new SubItemSkuReqDTO();
        subItemSkuReqDTO.setItemNum(new BigDecimal(1));
        subItemSkuReqDTO.setAddPrice(BigDecimal.ZERO);
        subItemSkuReqDTO.setIsRepeat(1);
        subItemSkuReqDTO.setIsDefault(1);
        subItemSkuReqDTO.setItemGuid("6500296866972294145");
        subItemSkuReqDTO.setSort(1);
        subItemSkuReqDTO.setSkuGuid("6500296866984880129");
        SubItemSkuReqDTO subItemSkuReqDTO1 = new SubItemSkuReqDTO();
        subItemSkuReqDTO1.setItemNum(new BigDecimal(1));
        subItemSkuReqDTO1.setAddPrice(BigDecimal.ZERO);
        subItemSkuReqDTO1.setIsRepeat(1);
        subItemSkuReqDTO1.setIsDefault(0);
        subItemSkuReqDTO1.setItemGuid("6500296866972294145");
        subItemSkuReqDTO1.setSort(2);
        subItemSkuReqDTO1.setSkuGuid("6500296866993269761");

        SubgroupReqDTO subgroupReqDTO1 = new SubgroupReqDTO();
        subgroupReqDTO1.setIsFixSubgroup(0);
        subgroupReqDTO1.setPickNum(1);
        subgroupReqDTO1.setSort(2);
        SubItemSkuReqDTO subItemSkuReqDTO3 = new SubItemSkuReqDTO();
        subItemSkuReqDTO3.setItemNum(new BigDecimal(2));
        subItemSkuReqDTO3.setAddPrice(BigDecimal.ZERO);
        subItemSkuReqDTO3.setIsRepeat(1);
        subItemSkuReqDTO3.setIsDefault(1);
        subItemSkuReqDTO3.setItemGuid("6500284013174593537");
        subItemSkuReqDTO3.setSort(3);
        subItemSkuReqDTO3.setSkuGuid("6500284013187179521");
        SubItemSkuReqDTO subItemSkuReqDTO4 = new SubItemSkuReqDTO();
        subItemSkuReqDTO4.setItemNum(new BigDecimal(2));
        subItemSkuReqDTO4.setAddPrice(BigDecimal.ZERO);
        subItemSkuReqDTO4.setIsRepeat(1);
        subItemSkuReqDTO4.setIsDefault(1);
        subItemSkuReqDTO4.setItemGuid("6500280328879251457");
        subItemSkuReqDTO4.setSort(4);
        subItemSkuReqDTO4.setSkuGuid("6500280328891839489");
        subgroupReqDTO.setSubItemSkuList(Arrays.asList(subItemSkuReqDTO,subItemSkuReqDTO1));
        subgroupReqDTO1.setSubItemSkuList(Arrays.asList(subItemSkuReqDTO3,subItemSkuReqDTO4));
        item.setSubgroupList(Arrays.asList(subgroupReqDTO,subgroupReqDTO1));
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setEnterpriseGuid("6478436873816698881");
        String encode = URLEncoder.encode(JacksonUtils.writeValueAsString(userInfoDTO), "utf-8");
        MvcResult mvcResult = mockMvc.perform(post("/item_pkg/save_pkg").header("userInfo",encode).accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JacksonUtils.writeValueAsString(item)))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }

    public static void main(String[] args) {
        String a = "四十个字的回锅肉名称回锅肉回锅肉回锅肉回锅肉回锅肉回锅肉回锅肉回锅肉回锅肉回锅肉";
        System.out.println(a.length());
    }
}
