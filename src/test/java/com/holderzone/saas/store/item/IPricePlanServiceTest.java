package com.holderzone.saas.store.item;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.common.UserInfoDTO;
import com.holderzone.saas.store.dto.item.req.PricePlanPushReqDTO;
import com.holderzone.saas.store.item.service.IPricePlanService;
import com.holderzone.saas.store.item.util.SpringContextUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import javax.annotation.Resource;
import java.net.URLEncoder;
import java.time.LocalDateTime;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * <AUTHOR>
 * @version 1.0
 * @className IPricePlanServiceTest
 * @date 2021/03/22 12:29
 * @description 单元测试
 * @program holder-saas-store-item
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class IPricePlanServiceTest {

    @Autowired
    private WebApplicationContext wac;

    private MockMvc mockMvc;

    @Autowired
    private ApplicationContext applicationContext;

    @Resource
    private IPricePlanService pricePlanService;

    @Before
    public void setup() {
        SpringContextUtils.getInstance().setCfgContext((ConfigurableApplicationContext) applicationContext);
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();   //构造MockMvc
    }

    @Test
    public void savePlan() {
    }

    @Test
    public void planList() {
    }

    @Test
    public void getPlan() {
    }

    @Test
    public void pushPlan() {
        try {
            UserInfoDTO userInfoDTO = new UserInfoDTO();
            userInfoDTO.setEnterpriseGuid("6506431195651982337");
            userInfoDTO.setStoreGuid("6506453252643487745");
            String encode = URLEncoder.encode(JacksonUtils.writeValueAsString(userInfoDTO), "utf-8");

            PricePlanPushReqDTO reqDTO = new PricePlanPushReqDTO();
            reqDTO.setPlanGuid("************");
            // 立即推送
            reqDTO.setPushType(1);
            reqDTO.setPushDate(LocalDateTime.now());

            MvcResult mvcResult = mockMvc.perform(post("/plan/push").header("userInfo", encode)
                    .accept(MediaType.APPLICATION_JSON_VALUE)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(JacksonUtils.writeValueAsString(reqDTO)))
                    .andExpect(status().is4xxClientError())
                    .andDo(print())
                    .andReturn();
            String contentAsString = mvcResult.getResponse().getContentAsString();

            System.out.println("sout:" + contentAsString);


        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void planStartNoti() {
    }

    @Test
    public void planEndNoti() {
    }

    @Test
    public void deletePlan() {
    }

    @Test
    public void getPricePlanCacheData() {
    }

    @Test
    public void deletePlanDraft() {
    }

    @Test
    public void queryPlansByStoreGuid() {
    }
}