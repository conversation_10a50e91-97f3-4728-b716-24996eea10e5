package com.holderzone.saas.store.item.controller;

import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.item.req.*;
import com.holderzone.saas.store.dto.item.resp.*;
import com.holderzone.saas.store.item.service.IItemTMenuService;
import com.holderzone.saas.store.item.service.IItemTMenuSubitemService;
import com.holderzone.saas.store.item.service.IItemTMenuValidityService;
import com.holderzone.saas.store.item.service.IItemTemplateService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

@RunWith(SpringRunner.class)
@WebMvcTest(ItemTemplateController.class)
public class ItemTemplateControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private IItemTemplateService mockIItemTemplateService;
    @MockBean
    private IItemTMenuService mockIItemTMenuService;
    @MockBean
    private IItemTMenuValidityService mockItemTMenuValidityService;
    @MockBean
    private IItemTMenuSubitemService mockItemTMenuSubitemService;

    @Test
    public void testSaveItemTemplate() throws Exception {
        // Setup
        // Configure IItemTemplateService.saveOrUpdate(...).
        final ItemTemplateReqDTO request = new ItemTemplateReqDTO();
        request.setGuid("22eec2eb-4cc1-4d29-a48d-661f649fedcb");
        request.setStoreGuid("storeGuid");
        request.setTemplateName("templateName");
        request.setDescription("description");
        request.setEffectiveStartTime("effectiveStartTime");
        when(mockIItemTemplateService.saveOrUpdate(request)).thenReturn(false);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/item_template/save")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testSaveItemMenu() throws Exception {
        // Setup
        // Configure IItemTMenuService.saveItemMenu(...).
        final ItemTemplateMenuSubitemReqDTO request = new ItemTemplateMenuSubitemReqDTO();
        request.setPeriodicMode(0);
        request.setIsItFullTime(0);
        request.setTemplateGuid("templateGuid");
        request.setMenuGuid("menuGuid");
        final ItemMenuSubItemReqDTO itemMenuSubItemReqDTO = new ItemMenuSubItemReqDTO();
        request.setItemMenuSubItemReqDTOS(Arrays.asList(itemMenuSubItemReqDTO));
        when(mockIItemTMenuService.saveItemMenu(request)).thenReturn(false);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/item_template/save_menu")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testGetStoreItemTemplates() throws Exception {
        // Setup
        // Configure IItemTemplateService.getStoreItemTemplates(...).
        final ItemTemplateRespDTO itemTemplateRespDTO = new ItemTemplateRespDTO();
        itemTemplateRespDTO.setGuid("*************-40e7-b145-a5186f497187");
        itemTemplateRespDTO.setStoreGuid("storeGuid");
        itemTemplateRespDTO.setTemplateName("templateName");
        itemTemplateRespDTO.setEffectiveStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemTemplateRespDTO.setEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final ItemTemplatesRespDTO itemTemplatesRespDTO = new ItemTemplatesRespDTO("currentTemplateName",
                new Page<>(0L, 0L, Arrays.asList(itemTemplateRespDTO)));
        final ItemTemplateSearchReqDTO request = new ItemTemplateSearchReqDTO();
        request.setStoreGuid("storeGuid");
        request.setItActivated(0);
        request.setStartTime("startTime");
        request.setEndTime("endTime");
        request.setStatus(0);
        when(mockIItemTemplateService.getStoreItemTemplates(request)).thenReturn(itemTemplatesRespDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/item_template/get_store_item_templates")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testGetItemTemplateMenus() throws Exception {
        // Setup
        // Configure IItemTMenuService.getItemTemplateMenus(...).
        final List<ItemTemplateMenusRespDTO> itemTemplateMenusRespDTOS = Arrays.asList(
                new ItemTemplateMenusRespDTO("templateGuid", "2a20e59f-0459-424b-8ddb-14996852772e",
                        Arrays.asList("value"), "isDelate"));
        when(mockIItemTMenuService.getItemTemplateMenus(new SingleDataDTO("data", Arrays.asList("value"))))
                .thenReturn(itemTemplateMenusRespDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/item_template/get_item_template_menus")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testGetItemTemplateMenus_IItemTMenuServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockIItemTMenuService.getItemTemplateMenus(new SingleDataDTO("data", Arrays.asList("value"))))
                .thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/item_template/get_item_template_menus")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("[]");
    }

    @Test
    public void testGetTimeAndTypeForSyn() throws Exception {
        // Setup
        when(mockItemTMenuValidityService.getTimeAndTypeForSyn(
                new SingleDataDTO("data", Arrays.asList("value")))).thenReturn(Arrays.asList(0L));

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/item_template/get_item_template_execute_time")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testGetTimeAndTypeForSyn_IItemTMenuValidityServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockItemTMenuValidityService.getTimeAndTypeForSyn(
                new SingleDataDTO("data", Arrays.asList("value")))).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/item_template/get_item_template_execute_time")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("[]");
    }

    @Test
    public void testGetItemTemplateMenuDetail() throws Exception {
        // Setup
        // Configure IItemTMenuSubitemService.getItemTemplateMenuDetail(...).
        final ItemTemplateMenuDetailRespDTO itemTemplateMenuDetailRespDTO = new ItemTemplateMenuDetailRespDTO(
                "menuGuid", 0, "timeGuid", 0, Arrays.asList(0),
                Arrays.asList(new ItemTemplateExecuteTimeSlotRespDTO("startTime", "endTime")), Arrays.asList(
                new ItemTemplateMenuSubItemDetailRespDTO("d7984934-dd04-4dda-8bbf-38656aadbea8", "name", "skuGuid",
                        "skuName", "unit", "typeGuid", "typeName", new BigDecimal("0.00"), new BigDecimal("0.00"),
                        new BigDecimal("0.00"))));
        final ItemTemplateMenuDetailsReqDTO request = new ItemTemplateMenuDetailsReqDTO();
        request.setGuid("2697c2c9-953a-4c2f-9d7d-764d49446889");
        request.setTypeGuid("typeGuid");
        request.setKeywords("keywords");
        when(mockItemTMenuSubitemService.getItemTemplateMenuDetail(request)).thenReturn(itemTemplateMenuDetailRespDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/item_template/get_item_template_menu_detail")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testMenuSubItemBatchRemove() throws Exception {
        // Setup
        when(mockItemTMenuSubitemService.menuSubItemBatchRemove(
                new SingleDataDTO("data", Arrays.asList("value")))).thenReturn(false);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/item_template/batch_remove")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }
}
