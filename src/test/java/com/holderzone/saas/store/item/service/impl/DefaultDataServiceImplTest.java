package com.holderzone.saas.store.item.service.impl;

import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.item.entity.domain.*;
import com.holderzone.saas.store.item.service.*;
import com.holderzone.saas.store.item.service.rpc.CloudEnterpriseFeignClient;
import com.holderzone.saas.store.item.util.DynamicHelper;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.RedisTemplate;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.mockito.Mockito.verify;

@RunWith(MockitoJUnitRunner.class)
public class DefaultDataServiceImplTest {

    @Mock
    private IAttrService mockAttrService;
    @Mock
    private IAttrGroupService mockAttrGroupService;
    @Mock
    private DynamicHelper mockDynamicHelper;
    @Mock
    private RedisTemplate mockRedisTemplate;
    @Mock
    private ITypeService mockTypeService;
    @Mock
    private IItemService mockItemService;
    @Mock
    private ISkuService mockSkuService;
    @Mock
    private CloudEnterpriseFeignClient mockCloudEnterpriseFeignClient;

    private DefaultDataServiceImpl defaultDataServiceImplUnderTest;

    @Before
    public void setUp() {
        defaultDataServiceImplUnderTest = new DefaultDataServiceImpl(mockAttrService, mockAttrGroupService,
                mockDynamicHelper, mockRedisTemplate, mockTypeService, mockItemService, mockSkuService,
                mockCloudEnterpriseFeignClient);
    }

    @Test
    public void testAddAttr() {
        // Setup
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("brandGuid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        itemSingleDTO.setRuleType(0);

        // Run the test
        defaultDataServiceImplUnderTest.addAttr(itemSingleDTO);

        // Verify the results
        // Confirm IAttrGroupService.saveBatch(...).
        final AttrGroupDO attrGroupDO = new AttrGroupDO();
        attrGroupDO.setStoreGuid("brandGuid");
        attrGroupDO.setName("默认分类");
        attrGroupDO.setGuid("09289100-a6dd-47fb-abb4-174d999df34d");
        attrGroupDO.setBrandGuid("brandGuid");
        attrGroupDO.setSort(0);
        attrGroupDO.setIsEnable(0);
        attrGroupDO.setAttrGroupFrom(0);
        final List<AttrGroupDO> entityList = Arrays.asList(attrGroupDO);
        verify(mockAttrGroupService).saveBatch(entityList, 0);

        // Confirm IAttrService.saveBatch(...).
        final AttrDO attrDO = new AttrDO();
        attrDO.setStoreGuid("brandGuid");
        attrDO.setName("默认分类");
        attrDO.setGuid("b09c68e9-77e7-4bf3-840f-380f6d6fbb8e");
        attrDO.setBrandGuid("brandGuid");
        attrDO.setAttrGroupGuid("attrGroupGuid");
        attrDO.setIsDefault(0);
        attrDO.setAttrFrom(0);
        final List<AttrDO> entityList1 = Arrays.asList(attrDO);
        verify(mockAttrService).saveBatch(entityList1, 0);
    }

    @Test
    public void testAddInitTypeAndItem() {
        // Setup
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("brandGuid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        itemSingleDTO.setRuleType(0);

        // Run the test
        defaultDataServiceImplUnderTest.addInitTypeAndItem(itemSingleDTO);

        // Verify the results
        verify(mockTypeService).save(
                new TypeDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0,
                        "de6cc6bf-4394-4f81-acab-c95654230b9d", "brandGuid", 0, 0, "description", "iconUrl", 0, 0, 0,
                        "pricePlanGuid", 0, 1));
        verify(mockItemService).save(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0,
                        "4751d464-9970-4082-81f1-954e3e183a6d", "typeGuid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0,
                        "pinyin", "nameAbbr", "description", "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code",
                        0, 0, 0, "videoUrls", 0, false));

        // Confirm ISkuService.save(...).
        final SkuDO entity = new SkuDO();
        entity.setGuid("739d10af-16c2-4372-99dd-bfb2cbd53d05");
        entity.setBrandGuid("brandGuid");
        entity.setStoreGuid("brandGuid");
        entity.setItemGuid("itemGuid");
        entity.setName("");
        entity.setSalePrice(new BigDecimal("0.00"));
        entity.setUnit("份");
        entity.setMinOrderNum(new BigDecimal("0.00"));
        entity.setIsRack(0);
        entity.setIsJoinWeChat(0);
        entity.setSkuFrom(0);
        verify(mockSkuService).save(entity);
    }
}
