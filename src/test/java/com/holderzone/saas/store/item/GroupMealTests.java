package com.holderzone.saas.store.item;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.item.req.GroupMealSkuSaveReqDTO;
import com.holderzone.saas.store.dto.item.req.ItemGroupMealSaveReqDTO;
import com.holderzone.saas.store.dto.item.req.ItemQueryReqDTO;
import com.holderzone.saas.store.dto.item.req.SkuSaveReqDTO;
import com.holderzone.saas.store.item.util.SpringContextUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.Arrays;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * <AUTHOR>
 * @version 1.0
 * @className GroupMealTests
 * @date 2019/12/07 下午4:17
 * @description //
 * @program holder
 */

@RunWith(SpringRunner.class)
@SpringBootTest
public class GroupMealTests {

    @Autowired
    private WebApplicationContext wac;
    private MockMvc mockMvc;

    @Autowired
    private ApplicationContext applicationContext;

    @Before
    public void setup() {
        SpringContextUtils.getInstance().setCfgContext((ConfigurableApplicationContext) applicationContext);
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();   //构造MockMvc
    }

    @Test
    public void saveGroupMealPkg() throws Exception {

        ItemGroupMealSaveReqDTO itemGroupMealSaveReqDTO = new ItemGroupMealSaveReqDTO();
        itemGroupMealSaveReqDTO.setItemGuid("6609027118611300352");  //更新必带字段
        itemGroupMealSaveReqDTO.setName("团购套餐2");
        itemGroupMealSaveReqDTO.setPinyin("CGTC2");
        itemGroupMealSaveReqDTO.setItemFrom(0);
        itemGroupMealSaveReqDTO.setItemType(5);
        itemGroupMealSaveReqDTO.setSort(11);
        itemGroupMealSaveReqDTO.setDescription("团购套餐2描述");
        itemGroupMealSaveReqDTO.setStoreGuid("6506453252643487745");
        SkuSaveReqDTO skuSaveReqDTO = new SkuSaveReqDTO();
        skuSaveReqDTO.setSkuGuid("6609027118862958592");  //更新必带字段
        skuSaveReqDTO.setMinOrderNum(BigDecimal.ONE);
        skuSaveReqDTO.setUnit("份");
        skuSaveReqDTO.setSalePrice(BigDecimal.valueOf(10.1));
        skuSaveReqDTO.setCode("************");
        skuSaveReqDTO.setIsWholeDiscount(1);
        skuSaveReqDTO.setIsRack(1);
        skuSaveReqDTO.setStoreGuid("6506453252643487745");
        itemGroupMealSaveReqDTO.setSku(skuSaveReqDTO);
        itemGroupMealSaveReqDTO.setSkuList(Arrays.asList(
                GroupMealSkuSaveReqDTO.builder()
                        //.guid("6609027119089451008")  //更新必带字段（关系guid）  保存不带
                        .itemGuid("6550655727194710017")
                        .num(BigDecimal.ONE)
                        .skuGuid("6550655727236661249")
                        .sort(10)
                        .storeGuid("6506453252643487745")
                        .build(),
                GroupMealSkuSaveReqDTO.builder()
                        //.guid("6609036222461378560")
                        .itemGuid("6551713783705654273")
                        .num(BigDecimal.ONE)
                        .skuGuid("6551713783760193537")
                        .sort(12)
                        .storeGuid("6506453252643487745")
                        .build()
        ));
        UserContext userContext = new UserContext();
        userContext.setEnterpriseGuid("6506431195651982337");
        String encode = URLEncoder.encode(JacksonUtils.writeValueAsString(userContext), "utf-8");
        MvcResult mvcResult = mockMvc.perform(post("/item_pkg/save_group_meal_pkg").header("userInfo", encode).accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JacksonUtils.writeValueAsString(itemGroupMealSaveReqDTO)))
                .andExpect(status().is4xxClientError()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }


    @Test
    public void setGroupMealStatus() throws Exception {
        SingleDataDTO singleDataDTO = new SingleDataDTO();
        singleDataDTO.setData("1");
        singleDataDTO.setStoreGuid("6506453252643487745");
        UserContext userContext = new UserContext();
        userContext.setEnterpriseGuid("6506431195651982337");
        String encode = URLEncoder.encode(JacksonUtils.writeValueAsString(userContext), "utf-8");
        MvcResult mvcResult = mockMvc.perform(post("/type/set_group_meal_status").header("userInfo", encode).accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JacksonUtils.writeValueAsString(singleDataDTO)))
                .andExpect(status().is4xxClientError()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }



    @Test
    public void selectItemForWeb() throws Exception {
        ItemQueryReqDTO  itemQueryReqDTO = new ItemQueryReqDTO();
        itemQueryReqDTO.setStoreGuid("6506453252643487745");
        itemQueryReqDTO.setModel(1);
        UserContext userContext = new UserContext();
        userContext.setEnterpriseGuid("6506431195651982337");
        String encode = URLEncoder.encode(JacksonUtils.writeValueAsString(userContext), "utf-8");
        MvcResult mvcResult = mockMvc.perform(post("/item/select_item_for_web").header("userInfo", encode).accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JacksonUtils.writeValueAsString(itemQueryReqDTO)))
                .andExpect(status().is4xxClientError()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }


    @Test
    public void selectGroupMealStatus() throws Exception {
        SingleDataDTO  singleDataDTO = new SingleDataDTO();
        singleDataDTO.setData("6589714863180742656");
        UserContext userContext = new UserContext();
        userContext.setEnterpriseGuid("6506431195651982337");
        String encode = URLEncoder.encode(JacksonUtils.writeValueAsString(userContext), "utf-8");
        MvcResult mvcResult = mockMvc.perform(post("/type/select_group_meal_status").header("userInfo", encode).accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JacksonUtils.writeValueAsString(singleDataDTO)))
                .andExpect(status().is4xxClientError()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }


    @Test
    public void selectGroupMealDetail() throws Exception {
        SingleDataDTO  singleDataDTO = new SingleDataDTO();
        singleDataDTO.setData("6609027118611300352");
        UserContext userContext = new UserContext();
        userContext.setEnterpriseGuid("6506431195651982337");
        String encode = URLEncoder.encode(JacksonUtils.writeValueAsString(userContext), "utf-8");
        MvcResult mvcResult = mockMvc.perform(post("/item/select_group_meal_detail").header("userInfo", encode).accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JacksonUtils.writeValueAsString(singleDataDTO)))
                .andExpect(status().is4xxClientError()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }
}
