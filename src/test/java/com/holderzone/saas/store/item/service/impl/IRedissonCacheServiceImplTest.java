package com.holderzone.saas.store.item.service.impl;

import com.holderzone.saas.store.item.dto.SyncPricePlanMessageDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.redisson.api.MapOptions;
import org.redisson.api.RMapCache;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.Codec;

import java.util.concurrent.TimeUnit;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class IRedissonCacheServiceImplTest {

    @Mock
    private RedissonClient mockRedissonSingleClient;

    @InjectMocks
    private IRedissonCacheServiceImpl iRedissonCacheServiceImplUnderTest;

    @Test
    public void testSetCacheMap() {
        // Setup
        final SyncPricePlanMessageDTO messageDTO = new SyncPricePlanMessageDTO();
        messageDTO.setEnterpriseGuid("enterpriseGuid");
        messageDTO.setBrandGuid("brandGuid");
        messageDTO.setPricePlanGuid("pricePlanGuid");
        messageDTO.setMessageType(0);

        when(mockRedissonSingleClient.getMap("key")).thenReturn(null);

        // Run the test
        iRedissonCacheServiceImplUnderTest.setCacheMap("key", messageDTO, 0L);

        // Verify the results
    }

    @Test
    public void testGetCacheMap() {
        // Setup
        when(mockRedissonSingleClient.getMap("key")).thenReturn(null);

        // Run the test
        final Object result = iRedissonCacheServiceImplUnderTest.getCacheMap("key");

        // Verify the results
    }

    @Test
    public void testRemoveCacheMap() {
        // Setup
        when(mockRedissonSingleClient.getMapCache("key")).thenReturn(null);

        // Run the test
        final Object result = iRedissonCacheServiceImplUnderTest.removeCacheMap("key");

        // Verify the results
    }

    @Test
    public void testGetSyncPricePlanItemDTORMapCache() {
        // Setup
        final RMapCache<String, String> expectedResult = null;
        when(mockRedissonSingleClient.getMapCache(eq("price-plan-cache"), any(Codec.class),
                any(MapOptions.class))).thenReturn(null);

        // Run the test
        final RMapCache<String, String> result = iRedissonCacheServiceImplUnderTest.getSyncPricePlanItemDTORMapCache();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testSetCache() {
        // Setup
        when(mockRedissonSingleClient.getMapCache(eq("price-plan-cache"), any(Codec.class),
                any(MapOptions.class))).thenReturn(null);

        // Run the test
        iRedissonCacheServiceImplUnderTest.setCache("key", "value", 0L, TimeUnit.MILLISECONDS);

        // Verify the results
    }

    @Test
    public void testGetCache() {
        // Setup
        when(mockRedissonSingleClient.getMapCache(eq("price-plan-cache"), any(Codec.class),
                any(MapOptions.class))).thenReturn(null);

        // Run the test
        final String result = iRedissonCacheServiceImplUnderTest.getCache("key");

        // Verify the results
        assertThat(result).isEqualTo("result");
    }

    @Test
    public void testRemoveCache() {
        // Setup
        when(mockRedissonSingleClient.getMapCache("price-plan-cache")).thenReturn(null);

        // Run the test
        final Object result = iRedissonCacheServiceImplUnderTest.removeCache("key");

        // Verify the results
    }

    @Test
    public void testGetRedissonClient() {
        assertThat(iRedissonCacheServiceImplUnderTest.getRedissonClient()).isEqualTo(mockRedissonSingleClient);
    }
}
