package com.holderzone.saas.store.item.controller;

import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.item.req.DoubleDataPageDTO;
import com.holderzone.saas.store.dto.item.req.SingleDataPageDTO;
import com.holderzone.saas.store.dto.item.resp.ErpItemDTO;
import com.holderzone.saas.store.dto.item.resp.ErpTypeDTO;
import com.holderzone.saas.store.item.service.ErpService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

@RunWith(SpringRunner.class)
@WebMvcTest(ErpController.class)
public class ErpControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private ErpService mockErpService;

    @Test
    public void testListType() throws Exception {
        // Setup
        // Configure ErpService.listType(...).
        final List<ErpTypeDTO> erpTypeDTOS = Arrays.asList(
                new ErpTypeDTO("53531d15-a2a5-4c4d-929e-531156db670e", "name"));
        when(mockErpService.listType("data")).thenReturn(erpTypeDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/erp/list_type_of_store")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testListType_ErpServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockErpService.listType("data")).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/erp/list_type_of_store")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("[]");
    }

    @Test
    public void testPageItem() throws Exception {
        // Setup
        // Configure ErpService.pageItem(...).
        final Page<ErpItemDTO> erpItemDTOPage = new Page<>(0L, 0L,
                Arrays.asList(new ErpItemDTO("*************-4e78-9f59-6afb905e756d", "name", "skuId", "icon", 0, 0L)));
        final SingleDataPageDTO singleDataPageDTO = new SingleDataPageDTO();
        singleDataPageDTO.setData("data");
        when(mockErpService.pageItem(singleDataPageDTO)).thenReturn(erpItemDTOPage);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/erp/page_item_of_store")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testPageItemOfType() throws Exception {
        // Setup
        // Configure ErpService.pageItemOfType(...).
        final Page<ErpItemDTO> erpItemDTOPage = new Page<>(0L, 0L,
                Arrays.asList(new ErpItemDTO("*************-4e78-9f59-6afb905e756d", "name", "skuId", "icon", 0, 0L)));
        final SingleDataPageDTO singleDataPageDTO = new SingleDataPageDTO();
        singleDataPageDTO.setData("data");
        when(mockErpService.pageItemOfType(singleDataPageDTO)).thenReturn(erpItemDTOPage);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/erp/page_item_of_type")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testPageItemByName() throws Exception {
        // Setup
        // Configure ErpService.pageItemByName(...).
        final Page<ErpItemDTO> erpItemDTOPage = new Page<>(0L, 0L,
                Arrays.asList(new ErpItemDTO("*************-4e78-9f59-6afb905e756d", "name", "skuId", "icon", 0, 0L)));
        final DoubleDataPageDTO doubleDataPageDTO = new DoubleDataPageDTO();
        doubleDataPageDTO.setData1("data1");
        doubleDataPageDTO.setData2("data2");
        when(mockErpService.pageItemByName(doubleDataPageDTO)).thenReturn(erpItemDTOPage);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/erp/page_item_by_name")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }
}
