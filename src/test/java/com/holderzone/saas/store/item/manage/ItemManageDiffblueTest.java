package com.holderzone.saas.store.item.manage;

import com.holderzone.saas.store.dto.item.req.ItemQueryListReq;
import com.holderzone.saas.store.dto.item.resp.ItemAndTypeForAndroidRespDTO;
import com.holderzone.saas.store.dto.item.resp.ItemInfoEstimateSingleDTO;
import com.holderzone.saas.store.dto.item.resp.ItemSynRespDTO;
import com.holderzone.saas.store.item.repository.EstimateRepository;
import com.holderzone.saas.store.item.service.IItemService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import static org.junit.Assert.assertSame;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ContextConfiguration(classes = {ItemManage.class})
@RunWith(SpringJUnit4ClassRunner.class)
public class ItemManageDiffblueTest {
    @MockBean
    private EstimateRepository estimateRepository;

    @MockBean
    private IItemService iItemService;

    @Autowired
    private ItemManage itemManage;

    private final static String EXAMPLE_URL = "https://example.org/example";
    /**
     * Method under test: {@link ItemManage#getItemInfoEstimate(ItemQueryListReq)}
     */
    @Test
    public void testGetItemInfoEstimate() {
        ItemAndTypeForAndroidRespDTO itemAndTypeForAndroidRespDTO = new ItemAndTypeForAndroidRespDTO();
        itemAndTypeForAndroidRespDTO.setItemList(new ArrayList<>());
        itemAndTypeForAndroidRespDTO.setPricePlanGuid("1234");
        itemAndTypeForAndroidRespDTO.setSalesModel(1);
        itemAndTypeForAndroidRespDTO.setTypeList(new ArrayList<>());
        when(iItemService.selectItemDetailAndTypeForSyn(Mockito.<ItemQueryListReq>any()))
                .thenReturn(itemAndTypeForAndroidRespDTO);

        ItemQueryListReq req = new ItemQueryListReq();
        req.setIsJoinAio(1);
        req.setIsJoinPad(1);
        req.setIsJoinPos(1);
        req.setIsRack(1);
        req.setItemGuid("1234");
        req.setOperSubjectGuid("1234");
        req.setSceneCode("Scene Code");
        req.setStoreGuid("1234");
        itemManage.getItemInfoEstimate(req);
        verify(iItemService).selectItemDetailAndTypeForSyn(Mockito.<ItemQueryListReq>any());
    }

    /**
     * Method under test: {@link ItemManage#getItemInfoEstimate(ItemQueryListReq)}
     */
    @Test
    public void testGetItemInfoEstimate2() {
        ItemSynRespDTO itemSynRespDTO = new ItemSynRespDTO();
        itemSynRespDTO.setAttrGroupList(new ArrayList<>());
        itemSynRespDTO.setBigPicture("Big Picture");
        itemSynRespDTO.setBigPictureUrl(EXAMPLE_URL);
        itemSynRespDTO.setDescription("The characteristics of someone or something");
        itemSynRespDTO.setDetailBigPictureUrl(EXAMPLE_URL);
        itemSynRespDTO.setDetailPicture("Detail Picture");
        itemSynRespDTO.setEnglishBrief("English Brief");
        itemSynRespDTO.setEnglishIngredientsDesc("English Ingredients Desc");
        itemSynRespDTO.setHasAttr(1);
        itemSynRespDTO.setIsBestseller(1);
        itemSynRespDTO.setIsFixPkg(1);
        itemSynRespDTO.setIsNew(1);
        itemSynRespDTO.setIsSign(1);
        itemSynRespDTO.setIsSoldOut(1);
        itemSynRespDTO.setItemGuid("1234");
        itemSynRespDTO.setItemType(42);
        itemSynRespDTO.setName("Name");
        itemSynRespDTO.setNameAbbr("Name Abbr");
        itemSynRespDTO.setParentGuid("1234");
        itemSynRespDTO.setPictureUrl(EXAMPLE_URL);
        itemSynRespDTO.setPinyin("Pinyin");
        itemSynRespDTO.setSkuList(new ArrayList<>());
        itemSynRespDTO.setSmallPicture("Small Picture");
        itemSynRespDTO.setSort(1);
        itemSynRespDTO.setSubgroupList(new ArrayList<>());
        itemSynRespDTO.setTagList(new ArrayList<>());
        itemSynRespDTO.setTypeGuid("1234");
        itemSynRespDTO.setTypeName("Type Name");
        itemSynRespDTO.setVerticalPicture("Vertical Picture");

        ArrayList<ItemSynRespDTO> itemList = new ArrayList<>();
        itemList.add(itemSynRespDTO);

        ItemAndTypeForAndroidRespDTO itemAndTypeForAndroidRespDTO = new ItemAndTypeForAndroidRespDTO();
        itemAndTypeForAndroidRespDTO.setItemList(itemList);
        itemAndTypeForAndroidRespDTO.setPricePlanGuid("1234");
        itemAndTypeForAndroidRespDTO.setSalesModel(1);
        itemAndTypeForAndroidRespDTO.setTypeList(new ArrayList<>());
        when(iItemService.selectItemDetailAndTypeForSyn(Mockito.<ItemQueryListReq>any()))
                .thenReturn(itemAndTypeForAndroidRespDTO);
        when(estimateRepository.listEstimateBySkuGuid(Mockito.<Set<String>>any(), Mockito.<String>any()))
                .thenReturn(new ArrayList<>());

        ItemQueryListReq req = new ItemQueryListReq();
        req.setIsJoinAio(1);
        req.setIsJoinPad(1);
        req.setIsJoinPos(1);
        req.setIsRack(1);
        req.setItemGuid("1234");
        req.setOperSubjectGuid("1234");
        req.setSceneCode("Scene Code");
        req.setStoreGuid("1234");
        ItemInfoEstimateSingleDTO actualItemInfoEstimate = itemManage.getItemInfoEstimate(req);
        verify(estimateRepository).listEstimateBySkuGuid(Mockito.<Set<String>>any(), Mockito.<String>any());
        verify(iItemService).selectItemDetailAndTypeForSyn(Mockito.<ItemQueryListReq>any());
        assertTrue(actualItemInfoEstimate.getItemEstimateList().isEmpty());
        assertSame(itemSynRespDTO, actualItemInfoEstimate.getItemInfo());
    }
}
