package com.holderzone.saas.store.item.controller;

import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.req.AttrGroupReqDTO;
import com.holderzone.saas.store.dto.item.req.AttrGroupUpdateReqDTO;
import com.holderzone.saas.store.dto.item.req.AttrReqDTO;
import com.holderzone.saas.store.dto.item.req.AttrSaveReqDTO;
import com.holderzone.saas.store.dto.item.resp.AttrGroupAttrRespDTO;
import com.holderzone.saas.store.dto.item.resp.AttrRespDTO;
import com.holderzone.saas.store.item.service.IAttrGroupService;
import com.holderzone.saas.store.item.service.IAttrService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

@RunWith(SpringRunner.class)
@WebMvcTest(AttrController.class)
public class AttrControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private IAttrGroupService mockAttrGroupService;
    @MockBean
    private IAttrService mockAttrService;

    @Test
    public void testListAttrGroup() throws Exception {
        // Setup
        // Configure IAttrGroupService.listAttrGroupByOrganization(...).
        final AttrGroupAttrRespDTO attrGroupAttrRespDTO = new AttrGroupAttrRespDTO();
        attrGroupAttrRespDTO.setAttrGroupGuid("attrGroupGuid");
        attrGroupAttrRespDTO.setBrandGuid("brandGuid");
        attrGroupAttrRespDTO.setStoreGuid("storeGuid");
        attrGroupAttrRespDTO.setName("name");
        attrGroupAttrRespDTO.setSort(0);
        final List<AttrGroupAttrRespDTO> attrGroupAttrRespDTOS = Arrays.asList(attrGroupAttrRespDTO);
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setFrom(0);
        itemSingleDTO.setData("data");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        when(mockAttrGroupService.listAttrGroupByOrganization(itemSingleDTO)).thenReturn(attrGroupAttrRespDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/attr/list_attr_group")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testListAttrGroup_IAttrGroupServiceReturnsNoItems() throws Exception {
        // Setup
        // Configure IAttrGroupService.listAttrGroupByOrganization(...).
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setFrom(0);
        itemSingleDTO.setData("data");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        when(mockAttrGroupService.listAttrGroupByOrganization(itemSingleDTO)).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/attr/list_attr_group")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("[]");
    }

    @Test
    public void testListAttrForSaveItem() throws Exception {
        // Setup
        // Configure IAttrGroupService.listAttrForSaveItem(...).
        final AttrGroupAttrRespDTO attrGroupAttrRespDTO = new AttrGroupAttrRespDTO();
        attrGroupAttrRespDTO.setAttrGroupGuid("attrGroupGuid");
        attrGroupAttrRespDTO.setBrandGuid("brandGuid");
        attrGroupAttrRespDTO.setStoreGuid("storeGuid");
        attrGroupAttrRespDTO.setName("name");
        attrGroupAttrRespDTO.setSort(0);
        final List<AttrGroupAttrRespDTO> attrGroupAttrRespDTOS = Arrays.asList(attrGroupAttrRespDTO);
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setFrom(0);
        itemSingleDTO.setData("data");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        when(mockAttrGroupService.listAttrForSaveItem(itemSingleDTO)).thenReturn(attrGroupAttrRespDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/attr/list_attr_for_save_item")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testListAttrForSaveItem_IAttrGroupServiceReturnsNoItems() throws Exception {
        // Setup
        // Configure IAttrGroupService.listAttrForSaveItem(...).
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setFrom(0);
        itemSingleDTO.setData("data");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        when(mockAttrGroupService.listAttrForSaveItem(itemSingleDTO)).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/attr/list_attr_for_save_item")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("[]");
    }

    @Test
    public void testListAttrByGroup() throws Exception {
        // Setup
        // Configure IAttrService.listAttrByGroup(...).
        final AttrRespDTO attrRespDTO = new AttrRespDTO();
        attrRespDTO.setAttrGroupGuid("attrGroupGuid");
        attrRespDTO.setAttrGuid("attrGuid");
        attrRespDTO.setName("name");
        attrRespDTO.setPrice(new BigDecimal("0.00"));
        attrRespDTO.setIsDefault(0);
        final List<AttrRespDTO> attrRespDTOS = Arrays.asList(attrRespDTO);
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setFrom(0);
        itemSingleDTO.setData("data");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        when(mockAttrService.listAttrByGroup(itemSingleDTO)).thenReturn(attrRespDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/attr/list_attr_by_group")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testListAttrByGroup_IAttrServiceReturnsNoItems() throws Exception {
        // Setup
        // Configure IAttrService.listAttrByGroup(...).
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setFrom(0);
        itemSingleDTO.setData("data");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        when(mockAttrService.listAttrByGroup(itemSingleDTO)).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/attr/list_attr_by_group")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("[]");
    }

    @Test
    public void testAddAttrGroup() throws Exception {
        // Setup
        // Configure IAttrGroupService.addAttrGroup(...).
        final AttrGroupReqDTO attrGroupReqDTO = new AttrGroupReqDTO();
        attrGroupReqDTO.setFrom(0);
        attrGroupReqDTO.setBrandGuid("brandGuid");
        attrGroupReqDTO.setStoreGuid("storeGuid");
        attrGroupReqDTO.setName("name");
        attrGroupReqDTO.setSort(0);
        when(mockAttrGroupService.addAttrGroup(attrGroupReqDTO)).thenReturn(false);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/attr/save_attr_group")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testAddAttrGroup_IAttrGroupServiceReturnsTrue() throws Exception {
        // Setup
        // Configure IAttrGroupService.addAttrGroup(...).
        final AttrGroupReqDTO attrGroupReqDTO = new AttrGroupReqDTO();
        attrGroupReqDTO.setFrom(0);
        attrGroupReqDTO.setBrandGuid("brandGuid");
        attrGroupReqDTO.setStoreGuid("storeGuid");
        attrGroupReqDTO.setName("name");
        attrGroupReqDTO.setSort(0);
        when(mockAttrGroupService.addAttrGroup(attrGroupReqDTO)).thenReturn(true);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/attr/save_attr_group")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testUpdateAttrGroup() throws Exception {
        // Setup
        // Configure IAttrGroupService.updateAttrGroup(...).
        final AttrGroupUpdateReqDTO attrGroupUpdateReqDTO = new AttrGroupUpdateReqDTO();
        attrGroupUpdateReqDTO.setFrom(0);
        attrGroupUpdateReqDTO.setBrandGuid("brandGuid");
        attrGroupUpdateReqDTO.setStoreGuid("storeGuid");
        attrGroupUpdateReqDTO.setAttrGroupGuid("attrGroupGuid");
        attrGroupUpdateReqDTO.setName("name");
        when(mockAttrGroupService.updateAttrGroup(attrGroupUpdateReqDTO)).thenReturn(false);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/attr/set_attr_group")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testUpdateAttrGroup_IAttrGroupServiceReturnsTrue() throws Exception {
        // Setup
        // Configure IAttrGroupService.updateAttrGroup(...).
        final AttrGroupUpdateReqDTO attrGroupUpdateReqDTO = new AttrGroupUpdateReqDTO();
        attrGroupUpdateReqDTO.setFrom(0);
        attrGroupUpdateReqDTO.setBrandGuid("brandGuid");
        attrGroupUpdateReqDTO.setStoreGuid("storeGuid");
        attrGroupUpdateReqDTO.setAttrGroupGuid("attrGroupGuid");
        attrGroupUpdateReqDTO.setName("name");
        when(mockAttrGroupService.updateAttrGroup(attrGroupUpdateReqDTO)).thenReturn(true);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/attr/set_attr_group")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testSaveAttrValue() throws Exception {
        // Setup
        // Configure IAttrService.saveAttrValue(...).
        final AttrReqDTO attrReqDTO = new AttrReqDTO();
        attrReqDTO.setFrom(0);
        attrReqDTO.setAttrGroupGuid("attrGroupGuid");
        attrReqDTO.setName("name");
        attrReqDTO.setPrice(new BigDecimal("0.00"));
        attrReqDTO.setTypeGuidList(Arrays.asList("value"));
        when(mockAttrService.saveAttrValue(attrReqDTO)).thenReturn(false);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/attr/save_attr")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testSaveAttrValue_IAttrServiceReturnsTrue() throws Exception {
        // Setup
        // Configure IAttrService.saveAttrValue(...).
        final AttrReqDTO attrReqDTO = new AttrReqDTO();
        attrReqDTO.setFrom(0);
        attrReqDTO.setAttrGroupGuid("attrGroupGuid");
        attrReqDTO.setName("name");
        attrReqDTO.setPrice(new BigDecimal("0.00"));
        attrReqDTO.setTypeGuidList(Arrays.asList("value"));
        when(mockAttrService.saveAttrValue(attrReqDTO)).thenReturn(true);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/attr/save_attr")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testUpdateAttrValue() throws Exception {
        // Setup
        // Configure IAttrService.update(...).
        final AttrSaveReqDTO attrSaveReqDTO = new AttrSaveReqDTO();
        attrSaveReqDTO.setFrom(0);
        attrSaveReqDTO.setAttrGuid("attrGuid");
        attrSaveReqDTO.setName("name");
        attrSaveReqDTO.setPrice(new BigDecimal("0.00"));
        attrSaveReqDTO.setTypeGuidList(Arrays.asList("value"));
        when(mockAttrService.update(attrSaveReqDTO)).thenReturn(false);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/attr/update_attr")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testUpdateAttrValue_IAttrServiceReturnsTrue() throws Exception {
        // Setup
        // Configure IAttrService.update(...).
        final AttrSaveReqDTO attrSaveReqDTO = new AttrSaveReqDTO();
        attrSaveReqDTO.setFrom(0);
        attrSaveReqDTO.setAttrGuid("attrGuid");
        attrSaveReqDTO.setName("name");
        attrSaveReqDTO.setPrice(new BigDecimal("0.00"));
        attrSaveReqDTO.setTypeGuidList(Arrays.asList("value"));
        when(mockAttrService.update(attrSaveReqDTO)).thenReturn(true);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/attr/update_attr")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testDeleteAttrValue() throws Exception {
        // Setup
        when(mockAttrService.deleteByGuid("data")).thenReturn(false);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/attr/delete_attr")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testDeleteAttrValue_IAttrServiceReturnsTrue() throws Exception {
        // Setup
        when(mockAttrService.deleteByGuid("data")).thenReturn(true);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/attr/delete_attr")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testDeleteAttrGroup() throws Exception {
        // Setup
        // Configure IAttrGroupService.deleteByGuid(...).
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setFrom(0);
        itemSingleDTO.setData("data");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        when(mockAttrGroupService.deleteByGuid(itemSingleDTO)).thenReturn(false);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/attr/delete_attr_group")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testDeleteAttrGroup_IAttrGroupServiceReturnsTrue() throws Exception {
        // Setup
        // Configure IAttrGroupService.deleteByGuid(...).
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setFrom(0);
        itemSingleDTO.setData("data");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        when(mockAttrGroupService.deleteByGuid(itemSingleDTO)).thenReturn(true);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/attr/delete_attr_group")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testCheckAttrUsed() throws Exception {
        // Setup
        when(mockAttrService.checkAttrUsed("attrGuid")).thenReturn(false);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/attr/check_attr_used")
                        .param("attrGuid", "attrGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testCheckAttrUsed_IAttrServiceReturnsTrue() throws Exception {
        // Setup
        when(mockAttrService.checkAttrUsed("attrGuid")).thenReturn(true);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/attr/check_attr_used")
                        .param("attrGuid", "attrGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }
}
