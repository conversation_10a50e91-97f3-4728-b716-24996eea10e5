package com.holderzone.saas.store.item;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.common.UserInfoDTO;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.common.ItemStringListDTO;
import com.holderzone.saas.store.dto.item.req.*;
import com.holderzone.saas.store.item.util.SpringContextUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.Arrays;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@SpringBootTest
public class LiItemTests {

    private static final String USER_INFO = "userInfo";

    @Autowired
    private WebApplicationContext wac;
    private MockMvc mockMvc;

    @Autowired
    private ApplicationContext applicationContext;
    @Before
    public void setup() {
        SpringContextUtils.getInstance().setCfgContext((ConfigurableApplicationContext) applicationContext);
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();   //构造MockMvc
    }

/*    @Test
    public void testRack() throws Exception {
        ItemRackDTO itemSingleDTO = new ItemRackDTO();
        itemSingleDTO.setItemGuidList(Arrays.asList("6507170103203156993"));
        itemSingleDTO.setRackState(-1);
        itemSingleDTO.setFrom(0);
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setEnterpriseGuid("6506431195651982337");
        String encode = URLEncoder.encode(JacksonUtils.writeValueAsString(userInfoDTO), "utf-8");
        MvcResult mvcResult = mockMvc.perform(post("/item/rack_item").header(USER_INFO,encode).accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JacksonUtils.writeValueAsString(itemSingleDTO)))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }*/

    @Test
    public void testPrint() throws Exception {
        BaseDTO baseDTO = new BaseDTO();

        baseDTO.setStoreGuid("6506453252643487745");
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setEnterpriseGuid("6506431195651982337");
        String encode = URLEncoder.encode(JacksonUtils.writeValueAsString(userInfoDTO), "utf-8");
        MvcResult mvcResult = mockMvc.perform(post("/item/selectTypeItemList")
                .header(USER_INFO,encode).accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JacksonUtils.writeValueAsString(baseDTO)))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }

    @Test
    public void testBatchDel() throws Exception {
        ItemStringListDTO itemSingleDTO = new ItemStringListDTO();
        itemSingleDTO.setDataList(Arrays.asList("6506053414365967361"));
        itemSingleDTO.setFrom(0);
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setEnterpriseGuid("6491846954039715841");
        String encode = URLEncoder.encode(JacksonUtils.writeValueAsString(userInfoDTO), "utf-8");
        MvcResult mvcResult = mockMvc.perform(post("/item/batch_delete").header(USER_INFO,encode).accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JacksonUtils.writeValueAsString(itemSingleDTO)))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }

    @Test
    public void listForApp() throws Exception {

        BaseDTO itemQueryReqDTO = new BaseDTO();
        itemQueryReqDTO.setStoreGuid("6508998037765881857");
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setEnterpriseGuid("6506431195651982337");
        String encode = URLEncoder.encode(JacksonUtils.writeValueAsString(userInfoDTO), "utf-8");
        MvcResult mvcResult = mockMvc.perform(post("/item/query_for_synchronize").header(USER_INFO,encode).accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JacksonUtils.writeValueAsString(itemQueryReqDTO)))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }

/*    @Test
    public void listForFastFoodApp() throws Exception {

        BaseDTO itemQueryReqDTO = new BaseDTO();
        itemQueryReqDTO.setStoreGuid("6506453252643487745");
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setEnterpriseGuid("6506431195651982337");
        String encode = URLEncoder.encode(JacksonUtils.writeValueAsString(userInfoDTO), "utf-8");
        MvcResult mvcResult = mockMvc.perform(post("/item/query_for_synchronize_fast_food").header(USER_INFO,encode).accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JacksonUtils.writeValueAsString(itemQueryReqDTO)))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }*/

    @Test
    public void listForTypes() throws Exception {

        ItemStringListDTO itemQueryReqDTO = new ItemStringListDTO();
        itemQueryReqDTO.setDataList(Arrays.asList("6506453252643487745"));
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setEnterpriseGuid("6506431195651982337");
        String encode = URLEncoder.encode(JacksonUtils.writeValueAsString(userInfoDTO), "utf-8");
        MvcResult mvcResult = mockMvc.perform(post("/type/query_type_by_stores").header(USER_INFO,encode).accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JacksonUtils.writeValueAsString(itemQueryReqDTO)))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }

    @Test
    public void listMapping() throws Exception {

        ItemSingleDTO itemQueryReqDTO = new ItemSingleDTO();
        itemQueryReqDTO.setData("6478514571754243073");
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setEnterpriseGuid("6491846954039715841");
        String encode = URLEncoder.encode(JacksonUtils.writeValueAsString(userInfoDTO), "utf-8");
        MvcResult mvcResult = mockMvc.perform(post("/item/mapping").header(USER_INFO,encode).accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JacksonUtils.writeValueAsString(itemQueryReqDTO)))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }

/*    @Test
    public void testtest() throws Exception {


        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setEnterpriseGuid("6491846954039715841");
        String encode = URLEncoder.encode(JacksonUtils.writeValueAsString(userInfoDTO), "utf-8");
        MvcResult mvcResult = mockMvc.perform(post("/item/test").header(USER_INFO,encode).accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }*/

    @Test
    public void itemSave() throws Exception {

        ItemReqDTO itemSaveReqDTO = new ItemReqDTO();
        itemSaveReqDTO.setFrom(0);
        itemSaveReqDTO.setTypeGuid("6493734393178638337");
        itemSaveReqDTO.setName("回锅肉炒腊肉");
        itemSaveReqDTO.setStoreGuid("6506453252643487745");
        itemSaveReqDTO.setIsBestseller(1);
        itemSaveReqDTO.setIsNew(1);
        itemSaveReqDTO.setIsSign(1);
        itemSaveReqDTO.setItemFrom(0);
        itemSaveReqDTO.setItemType(2);
        itemSaveReqDTO.setNameAbbr("xc");
        itemSaveReqDTO.setSort(1);
        itemSaveReqDTO.setPinyin("XC");
        SkuSaveReqDTO skuSaveReqDTO = new SkuSaveReqDTO();
        skuSaveReqDTO.setName("big");
        skuSaveReqDTO.setMinOrderNum(BigDecimal.ONE);
        skuSaveReqDTO.setSalePrice(BigDecimal.TEN);
        skuSaveReqDTO.setUnit("个");
        skuSaveReqDTO.setIsJoinElm(0);
        skuSaveReqDTO.setIsJoinMt(0);
        skuSaveReqDTO.setIsJoinWeChat(0);
        skuSaveReqDTO.setIsMemberDiscount(0);
        skuSaveReqDTO.setIsWholeDiscount(0);
        skuSaveReqDTO.setIsRack(1);
        skuSaveReqDTO.setCode("5879781");
        SkuSaveReqDTO skuSaveReqDTO1 = new SkuSaveReqDTO();
        skuSaveReqDTO1.setName("xiao");
        skuSaveReqDTO1.setMinOrderNum(BigDecimal.ONE);
        skuSaveReqDTO1.setSalePrice(new BigDecimal("9"));
        skuSaveReqDTO1.setUnit("个");
        skuSaveReqDTO1.setIsJoinElm(0);
        skuSaveReqDTO1.setIsJoinMt(0);
        skuSaveReqDTO1.setIsJoinWeChat(0);
        skuSaveReqDTO1.setIsMemberDiscount(1);
        skuSaveReqDTO1.setIsWholeDiscount(0);
        skuSaveReqDTO1.setIsRack(1);
        itemSaveReqDTO.setSkuList(Arrays.asList(skuSaveReqDTO));
        ItemAttrGroupReqDTO itemAttrGroupReqDTO = new ItemAttrGroupReqDTO();
        ItemAttrReqDTO itemAttrReqDTO = new ItemAttrReqDTO();
        itemAttrReqDTO.setAttrGuid("6493730844950342657");
        itemAttrReqDTO.setIsDefault(1);
        ItemAttrReqDTO itemAttrReqDTO1 = new ItemAttrReqDTO();
        itemAttrReqDTO1.setAttrGuid("6493005829437267969");
        itemAttrReqDTO1.setIsDefault(1);
        itemAttrGroupReqDTO.setAttrList(Arrays.asList(itemAttrReqDTO));
        itemAttrGroupReqDTO.setIsRequired(1);
        itemAttrGroupReqDTO.setIsMultiChoice(1);
        itemAttrGroupReqDTO.setWithDefault(1);
        itemAttrGroupReqDTO.setAttrGroupGuid("6493730628056067073");

        itemSaveReqDTO.setAttrGroupList(Arrays.asList(itemAttrGroupReqDTO));
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setEnterpriseGuid("6506431195651982337");
        String encode = URLEncoder.encode(JacksonUtils.writeValueAsString(userInfoDTO), "utf-8");
        MvcResult mvcResult = mockMvc.perform(post("/item/save_item").header(USER_INFO,encode).accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JacksonUtils.writeValueAsString(itemSaveReqDTO)))
                .andExpect(status().is4xxClientError()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }

    @Test
    public void itemSave1() throws Exception {

        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setEnterpriseGuid("6506431195651982337");
        String encode = URLEncoder.encode(JacksonUtils.writeValueAsString(userInfoDTO), "utf-8");
        MvcResult mvcResult = mockMvc.perform(post("/item/test").header(USER_INFO,encode).accept(MediaType.APPLICATION_JSON_VALUE))
                .andExpect(status().is4xxClientError()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }

    @Test
    public  void  estimateSave() throws Exception {
        EstimateReqDTO estimateReqDTO = new EstimateReqDTO();
        estimateReqDTO.setSkuGuid("111111111111");
        estimateReqDTO.setStoreGuid("************");
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setEnterpriseGuid("6506431195651982337");
        String encode = URLEncoder.encode(JacksonUtils.writeValueAsString(userInfoDTO), "utf-8");
        MvcResult mvcResult = mockMvc.perform(post("/estimate/save").header(USER_INFO,encode).accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JacksonUtils.writeValueAsString(estimateReqDTO)))
                .andExpect(status().is4xxClientError()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);

    }


    @Test
    public  void  getSkuItemList() throws Exception {
        ItemTemplateMenuAllSubItemReqDTO dto = new ItemTemplateMenuAllSubItemReqDTO();
        dto.setStoreGuid("6506453252643487745");
        dto.setTypeGuid("6509347544397972481");
        dto.setKeywords("肉");
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setEnterpriseGuid("6506431195651982337");
        String encode = URLEncoder.encode(JacksonUtils.writeValueAsString(userInfoDTO), "utf-8");
        MvcResult mvcResult = mockMvc.perform(post("/item/get_sku_item_list").header(USER_INFO,encode).accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JacksonUtils.writeValueAsString(dto)))
                .andExpect(status().is4xxClientError()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);

    }
}
