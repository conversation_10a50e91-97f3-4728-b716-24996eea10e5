

package com.holderzone.saas.store.item.service.impl;

import static org.mockito.MockitoAnnotations.initMocks;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import java.math.BigDecimal;import java.time.LocalDate;import java.time.LocalDateTime;import java.time.LocalTime;import java.util.*;import java.util.concurrent.Callable;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.assertj.core.api.Assertions.within;
    import static org.mockito.ArgumentMatchers.any;
    import static org.mockito.ArgumentMatchers.anyInt;
    import static org.mockito.ArgumentMatchers.anyString;
    import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;import com.baomidou.mybatisplus.extension.plugins.pagination.Page;import com.holderzone.framework.exception.unchecked.BusinessException;import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;import com.holderzone.saas.store.dto.item.common.PlanItemDTO;import com.holderzone.saas.store.dto.item.common.PlanItemUpdateDTO;import com.holderzone.saas.store.dto.item.req.*;import com.holderzone.saas.store.dto.item.req.price.PlanPriceAllItemReqDTO;import com.holderzone.saas.store.dto.item.req.price.PlanPriceAllItemSkuReqDTO;import com.holderzone.saas.store.dto.item.req.price.PlanPriceEditReqDTO;import com.holderzone.saas.store.dto.item.resp.*;import com.holderzone.saas.store.dto.item.resp.price.*;import com.holderzone.saas.store.dto.organization.BrandDTO;import com.holderzone.saas.store.dto.organization.StoreDTO;import com.holderzone.saas.store.dto.store.store.StoreProductDTO;import com.holderzone.saas.store.item.dto.SyncItemDTO;import com.holderzone.saas.store.item.dto.SyncStoreAndItemDTO;import com.holderzone.saas.store.item.entity.domain.*;import com.holderzone.saas.store.item.mapper.*;import com.holderzone.saas.store.item.service.ISkuService;import com.holderzone.saas.store.item.service.rpc.OrganizationService;import com.holderzone.saas.store.item.util.QrcodeServer;import org.mockito.junit.MockitoJUnitRunner;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.InjectMocks;
import org.mockito.stubbing.Answer;

@RunWith(MockitoJUnitRunner.class)
public class PricePlanItemServiceImplTest {

            @Mock
        private PricePlanMapper mockPlanMapper;
            @Mock
        private PricePlanItemMapper mockPlanItemMapper;
            @Mock
        private PricePlanStoreMapper mockPlanStoreMapper;
            @Mock
        private ISkuService mockSkuService;
            @Mock
        private TypeMapper mockTypeMapper;
            @Mock
        private ItemMapper mockItemMapper;
            @Mock
        private OrganizationService mockOrganizationService;
            @Mock
        private QrcodeServer mockQrcodeServer;

    private PricePlanItemServiceImpl pricePlanItemServiceImplUnderTest;

@Before
public void setUp() throws Exception {
            pricePlanItemServiceImplUnderTest = new PricePlanItemServiceImpl(mockPlanMapper,mockPlanItemMapper,mockPlanStoreMapper,mockSkuService,mockTypeMapper,mockItemMapper,mockOrganizationService,mockQrcodeServer) ;
}
                
    @Test
    public void testSavePlanItem() throws Exception {
    // Setup
                        final PricePlanItemAddReqDTO reqDTO = new PricePlanItemAddReqDTO();
                reqDTO.setBrandGuid("brandGuid");
                reqDTO.setPlanGuid("planGuid");
                                            final PlanItemDTO planItemDTO = new PlanItemDTO();
                planItemDTO.setItemGuid("itemGuid");
                planItemDTO.setItemType(0);
                reqDTO.setPlanItemDTOList(Arrays.asList(planItemDTO));
                                            final ItemWebRespDTO itemWebRespDTO = new ItemWebRespDTO();
                itemWebRespDTO.setItemGuid("itemGuid");
                itemWebRespDTO.setTypeGuid("typeGuid");
                itemWebRespDTO.setTypeName("itemName");
                itemWebRespDTO.setPictureUrl("");
                itemWebRespDTO.setBigPictureUrl("bigPictureUrl");
                itemWebRespDTO.setDetailBigPictureUrl("detailBigPictureUrl");
                itemWebRespDTO.setItemType(0);
                itemWebRespDTO.setSort(0);
                                            final SkuRespDTO skuRespDTO = new SkuRespDTO();
                skuRespDTO.setSkuGuid("guid");
                skuRespDTO.setSalePrice(new BigDecimal("0.00"));
                skuRespDTO.setMemberPrice(new BigDecimal("0.00"));
                skuRespDTO.setAccountingPrice(new BigDecimal("0.00"));
                skuRespDTO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
                skuRespDTO.setLinePrice(new BigDecimal("0.00"));
                skuRespDTO.setPlanItemGuid("a536644d-c5f0-4f12-b216-b0295a6056d5");
                skuRespDTO.setIsSoldOut(0);
                itemWebRespDTO.setSkuList(Arrays.asList(skuRespDTO));
                itemWebRespDTO.setSalePrice(new BigDecimal("0.00"));
                itemWebRespDTO.setMemberPrice(new BigDecimal("0.00"));
                itemWebRespDTO.setIsSoldOut(0);
                itemWebRespDTO.setDescription("");
                itemWebRespDTO.setPlanItemName("itemName");
                itemWebRespDTO.setSkuGuid("skuGuid");
                itemWebRespDTO.setPlanItemGuid("a536644d-c5f0-4f12-b216-b0295a6056d5");
                itemWebRespDTO.setNewupdateflag(false);
                itemWebRespDTO.setEnglishBrief("englishBrief");
                itemWebRespDTO.setEnglishIngredientsDesc("englishIngredientsDesc");
                itemWebRespDTO.setAccountingPrice(new BigDecimal("0.00"));
                itemWebRespDTO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
                itemWebRespDTO.setLinePrice(new BigDecimal("0.00"));
                reqDTO.setItemWebRespDTOList(Arrays.asList(itemWebRespDTO));
 
                             when( mockSkuService .getSkuGuidMap(Arrays.asList("value"))).thenReturn( new HashMap<>() );

    // Run the test
 final Boolean result =  pricePlanItemServiceImplUnderTest.savePlanItem(reqDTO);

        // Verify the results
 assertThat(result).isFalse() ;
            verify( mockPlanMapper ).updateItemNum("planGuid",0,1);
    }
                                                                                                        
    @Test
    public void testItemList() throws Exception {
    // Setup
                        final PricePlanItemPageReqDTO reqDTO = new PricePlanItemPageReqDTO();
                reqDTO.setCurrentPage(0L);
                reqDTO.setPageSize(0L);
                reqDTO.setPlanGuid("planGuid");
                reqDTO.setTypeGuid("typeGuid");
                reqDTO.setItemNameOrCode("itemNameOrCode");

            // Configure PricePlanItemMapper.itemList(...).
                                                        final PricePlanItemPageRespDTO pricePlanItemPageRespDTO = new PricePlanItemPageRespDTO();
                pricePlanItemPageRespDTO.setItemGuid("itemGuid");
                pricePlanItemPageRespDTO.setName("name");
                pricePlanItemPageRespDTO.setTypeName("typeName");
                pricePlanItemPageRespDTO.setPlanItemName("planItemName");
                                            final PricePlanSkuRespDTO pricePlanSkuRespDTO = new PricePlanSkuRespDTO();
                pricePlanItemPageRespDTO.setSkuList(Arrays.asList(pricePlanSkuRespDTO));
        final List<PricePlanItemPageRespDTO> pricePlanItemPageRespDTOS = Arrays.asList(pricePlanItemPageRespDTO);
        final PricePlanItemPageReqDTO reqDTO1 = new PricePlanItemPageReqDTO();
                reqDTO1.setCurrentPage(0L);
                reqDTO1.setPageSize(0L);
                reqDTO1.setPlanGuid("planGuid");
                reqDTO1.setTypeGuid("typeGuid");
                reqDTO1.setItemNameOrCode("itemNameOrCode");
            when( mockPlanItemMapper .itemList(eq(reqDTO1),any(Page.class))).thenReturn(pricePlanItemPageRespDTOS);

            // Configure PricePlanItemMapper.getSkuList(...).
                                                        final PricePlanSkuRespDTO pricePlanSkuRespDTO1 = new PricePlanSkuRespDTO();
                pricePlanSkuRespDTO1.setPlanItemGuid("planItemGuid");
                pricePlanSkuRespDTO1.setCode("code");
                pricePlanSkuRespDTO1.setUnit("unit");
                pricePlanSkuRespDTO1.setSkuName("skuName");
                pricePlanSkuRespDTO1.setSkuGuid("skuGuid");
        final List<PricePlanSkuRespDTO> pricePlanSkuRespDTOS = Arrays.asList(pricePlanSkuRespDTO1);
            when( mockPlanItemMapper .getSkuList("itemGuid","planGuid")).thenReturn(pricePlanSkuRespDTOS);

    // Run the test
 final com.holderzone.framework.util.Page<PricePlanItemPageRespDTO> result =  pricePlanItemServiceImplUnderTest.itemList(reqDTO);

        // Verify the results
    }
                                                                                                
    @Test
    public void testItemList_PricePlanItemMapperItemListReturnsNoItems() throws Exception {
    // Setup
                        final PricePlanItemPageReqDTO reqDTO = new PricePlanItemPageReqDTO();
                reqDTO.setCurrentPage(0L);
                reqDTO.setPageSize(0L);
                reqDTO.setPlanGuid("planGuid");
                reqDTO.setTypeGuid("typeGuid");
                reqDTO.setItemNameOrCode("itemNameOrCode");

        // Configure PricePlanItemMapper.itemList(...).
        final PricePlanItemPageReqDTO reqDTO1 = new PricePlanItemPageReqDTO();
                reqDTO1.setCurrentPage(0L);
                reqDTO1.setPageSize(0L);
                reqDTO1.setPlanGuid("planGuid");
                reqDTO1.setTypeGuid("typeGuid");
                reqDTO1.setItemNameOrCode("itemNameOrCode");
        when( mockPlanItemMapper .itemList(eq(reqDTO1),any(Page.class))).thenReturn( Collections.emptyList() );

    // Run the test
 final com.holderzone.framework.util.Page<PricePlanItemPageRespDTO> result =  pricePlanItemServiceImplUnderTest.itemList(reqDTO);

        // Verify the results
    }
                                                                        
    @Test
    public void testItemList_PricePlanItemMapperGetSkuListReturnsNoItems() throws Exception {
    // Setup
                        final PricePlanItemPageReqDTO reqDTO = new PricePlanItemPageReqDTO();
                reqDTO.setCurrentPage(0L);
                reqDTO.setPageSize(0L);
                reqDTO.setPlanGuid("planGuid");
                reqDTO.setTypeGuid("typeGuid");
                reqDTO.setItemNameOrCode("itemNameOrCode");

            // Configure PricePlanItemMapper.itemList(...).
                                                        final PricePlanItemPageRespDTO pricePlanItemPageRespDTO = new PricePlanItemPageRespDTO();
                pricePlanItemPageRespDTO.setItemGuid("itemGuid");
                pricePlanItemPageRespDTO.setName("name");
                pricePlanItemPageRespDTO.setTypeName("typeName");
                pricePlanItemPageRespDTO.setPlanItemName("planItemName");
                                            final PricePlanSkuRespDTO pricePlanSkuRespDTO = new PricePlanSkuRespDTO();
                pricePlanItemPageRespDTO.setSkuList(Arrays.asList(pricePlanSkuRespDTO));
        final List<PricePlanItemPageRespDTO> pricePlanItemPageRespDTOS = Arrays.asList(pricePlanItemPageRespDTO);
        final PricePlanItemPageReqDTO reqDTO1 = new PricePlanItemPageReqDTO();
                reqDTO1.setCurrentPage(0L);
                reqDTO1.setPageSize(0L);
                reqDTO1.setPlanGuid("planGuid");
                reqDTO1.setTypeGuid("typeGuid");
                reqDTO1.setItemNameOrCode("itemNameOrCode");
            when( mockPlanItemMapper .itemList(eq(reqDTO1),any(Page.class))).thenReturn(pricePlanItemPageRespDTOS);
 
         when( mockPlanItemMapper .getSkuList("itemGuid","planGuid")).thenReturn( Collections.emptyList() );

    // Run the test
 final com.holderzone.framework.util.Page<PricePlanItemPageRespDTO> result =  pricePlanItemServiceImplUnderTest.itemList(reqDTO);

        // Verify the results
    }
                
    @Test
    public void testRemoveItems() throws Exception {
    // Setup
                        final PricePlanItemRemoveReqDTO reqDTO = new PricePlanItemRemoveReqDTO();
                reqDTO.setPlanGuid("planGuid");
                reqDTO.setItemGuidList(Arrays.asList("value"));
 
                             when( mockPlanItemMapper .logicDeleteByItem(Arrays.asList("value"))).thenReturn( false );
                            when( mockPlanItemMapper .getCount("planGuid")).thenReturn( 0 );

    // Run the test
 final Boolean result =  pricePlanItemServiceImplUnderTest.removeItems(reqDTO);

        // Verify the results
 assertThat(result).isFalse() ;
            verify( mockPlanMapper ).updateItemNum("planGuid",0,3);
    }
                                                                        
    @Test
    public void testRemoveItems_PricePlanItemMapperLogicDeleteByItemReturnsTrue() throws Exception {
    // Setup
                        final PricePlanItemRemoveReqDTO reqDTO = new PricePlanItemRemoveReqDTO();
                reqDTO.setPlanGuid("planGuid");
                reqDTO.setItemGuidList(Arrays.asList("value"));
 
         when( mockPlanItemMapper .logicDeleteByItem(Arrays.asList("value"))).thenReturn( true );
                            when( mockPlanItemMapper .getCount("planGuid")).thenReturn( 0 );

    // Run the test
 final Boolean result =  pricePlanItemServiceImplUnderTest.removeItems(reqDTO);

        // Verify the results
                assertThat(result).isTrue();
            verify( mockPlanMapper ).updateItemNum("planGuid",0,3);
    }
                                                                                
    @Test
    public void testUpdateItems() throws Exception {
    // Setup
                        final PricePlanItemUpdateReqDTO reqDTO = new PricePlanItemUpdateReqDTO();
                reqDTO.setPercentNum(0);
                reqDTO.setFixedPrice(new BigDecimal("0.00"));
                                            final PricePlanItemUpdateDTO pricePlanItemUpdateDTO = new PricePlanItemUpdateDTO();
                pricePlanItemUpdateDTO.setIsBatchModified(false);
                                            final PricePlanItemSkuUpdateDTO pricePlanItemSkuUpdateDTO = new PricePlanItemSkuUpdateDTO();
                pricePlanItemSkuUpdateDTO.setOriginalPrice(new BigDecimal("0.00"));
                pricePlanItemSkuUpdateDTO.setSalePrice(new BigDecimal("0.00"));
                pricePlanItemUpdateDTO.setSkuList(Arrays.asList(pricePlanItemSkuUpdateDTO));
                reqDTO.setItemList(Arrays.asList(pricePlanItemUpdateDTO));

            // Configure PricePlanItemMapper.updateItems(...).
                                                        final PricePlanItemSkuUpdateDTO pricePlanItemSkuUpdateDTO1 = new PricePlanItemSkuUpdateDTO();
                pricePlanItemSkuUpdateDTO1.setPlanItemGuid("planItemGuid");
                pricePlanItemSkuUpdateDTO1.setOriginalPrice(new BigDecimal("0.00"));
                pricePlanItemSkuUpdateDTO1.setSalePrice(new BigDecimal("0.00"));
                pricePlanItemSkuUpdateDTO1.setMemberPrice(new BigDecimal("0.00"));
        final List<PricePlanItemSkuUpdateDTO> reqDTOList = Arrays.asList(pricePlanItemSkuUpdateDTO1);
            when( mockPlanItemMapper .updateItems(reqDTOList)).thenReturn( false );

    // Run the test
 final Boolean result =  pricePlanItemServiceImplUnderTest.updateItems(reqDTO);

        // Verify the results
 assertThat(result).isFalse() ;
    }
                                                                        
    @Test
    public void testUpdateItems_PricePlanItemMapperReturnsTrue() throws Exception {
    // Setup
                        final PricePlanItemUpdateReqDTO reqDTO = new PricePlanItemUpdateReqDTO();
                reqDTO.setPercentNum(0);
                reqDTO.setFixedPrice(new BigDecimal("0.00"));
                                            final PricePlanItemUpdateDTO pricePlanItemUpdateDTO = new PricePlanItemUpdateDTO();
                pricePlanItemUpdateDTO.setIsBatchModified(false);
                                            final PricePlanItemSkuUpdateDTO pricePlanItemSkuUpdateDTO = new PricePlanItemSkuUpdateDTO();
                pricePlanItemSkuUpdateDTO.setOriginalPrice(new BigDecimal("0.00"));
                pricePlanItemSkuUpdateDTO.setSalePrice(new BigDecimal("0.00"));
                pricePlanItemUpdateDTO.setSkuList(Arrays.asList(pricePlanItemSkuUpdateDTO));
                reqDTO.setItemList(Arrays.asList(pricePlanItemUpdateDTO));

        // Configure PricePlanItemMapper.updateItems(...).
                                                        final PricePlanItemSkuUpdateDTO pricePlanItemSkuUpdateDTO1 = new PricePlanItemSkuUpdateDTO();
                pricePlanItemSkuUpdateDTO1.setPlanItemGuid("planItemGuid");
                pricePlanItemSkuUpdateDTO1.setOriginalPrice(new BigDecimal("0.00"));
                pricePlanItemSkuUpdateDTO1.setSalePrice(new BigDecimal("0.00"));
                pricePlanItemSkuUpdateDTO1.setMemberPrice(new BigDecimal("0.00"));
        final List<PricePlanItemSkuUpdateDTO> reqDTOList = Arrays.asList(pricePlanItemSkuUpdateDTO1);
        when( mockPlanItemMapper .updateItems(reqDTOList)).thenReturn( true );

    // Run the test
 final Boolean result =  pricePlanItemServiceImplUnderTest.updateItems(reqDTO);

        // Verify the results
                assertThat(result).isTrue();
    }
                
    @Test
    public void testGetStoreAndItem() throws Exception {
    // Setup
                        final SyncStoreAndItemDTO expectedResult = new SyncStoreAndItemDTO();
                expectedResult.setPricePlanGuid("planGuid");
                expectedResult.setStoreGuidList(Arrays.asList("value"));
                                            final SyncItemDTO syncItemDTO = new SyncItemDTO();
                syncItemDTO.setItemGuid("itemGuid");
                syncItemDTO.setIsPkgItem(false);
                expectedResult.setItemList(Arrays.asList(syncItemDTO));
 
                             when( mockPlanStoreMapper .getPlanStoreGuidList("planGuid",Arrays.asList("value"))).thenReturn( Arrays.asList("value") );
                
            // Configure PricePlanItemMapper.getSyncItems(...).
                                                        final SyncItemDTO syncItemDTO1 = new SyncItemDTO();
                syncItemDTO1.setItemGuid("itemGuid");
                syncItemDTO1.setIsPkgItem(false);
                syncItemDTO1.setSkuGuid("skuGuid");
                syncItemDTO1.setSalePrice(new BigDecimal("0.00"));
                syncItemDTO1.setMemberPrice(new BigDecimal("0.00"));
        final List<SyncItemDTO> syncItemDTOS = Arrays.asList(syncItemDTO1);
            when( mockPlanItemMapper .getSyncItems("planGuid")).thenReturn(syncItemDTOS);

    // Run the test
 final SyncStoreAndItemDTO result =  pricePlanItemServiceImplUnderTest.getStoreAndItem("planGuid");

        // Verify the results
 assertThat(result).isEqualTo(expectedResult ) ;
    }
                                                                                                
    @Test
    public void testGetStoreAndItem_PricePlanStoreMapperReturnsNoItems() throws Exception {
    // Setup
                        final SyncStoreAndItemDTO expectedResult = new SyncStoreAndItemDTO();
                expectedResult.setPricePlanGuid("planGuid");
                expectedResult.setStoreGuidList(Arrays.asList("value"));
                                            final SyncItemDTO syncItemDTO = new SyncItemDTO();
                syncItemDTO.setItemGuid("itemGuid");
                syncItemDTO.setIsPkgItem(false);
                expectedResult.setItemList(Arrays.asList(syncItemDTO));
 
         when( mockPlanStoreMapper .getPlanStoreGuidList("planGuid",Arrays.asList("value"))).thenReturn( Collections.emptyList() );

    // Run the test
 final SyncStoreAndItemDTO result =  pricePlanItemServiceImplUnderTest.getStoreAndItem("planGuid");

        // Verify the results
 assertThat(result).isEqualTo(expectedResult ) ;
    }
                                                                        
    @Test
    public void testGetStoreAndItem_PricePlanItemMapperReturnsNoItems() throws Exception {
    // Setup
                        final SyncStoreAndItemDTO expectedResult = new SyncStoreAndItemDTO();
                expectedResult.setPricePlanGuid("planGuid");
                expectedResult.setStoreGuidList(Arrays.asList("value"));
                                            final SyncItemDTO syncItemDTO = new SyncItemDTO();
                syncItemDTO.setItemGuid("itemGuid");
                syncItemDTO.setIsPkgItem(false);
                expectedResult.setItemList(Arrays.asList(syncItemDTO));
 
                             when( mockPlanStoreMapper .getPlanStoreGuidList("planGuid",Arrays.asList("value"))).thenReturn( Arrays.asList("value") );
        when( mockPlanItemMapper .getSyncItems("planGuid")).thenReturn( Collections.emptyList() );

    // Run the test
 final SyncStoreAndItemDTO result =  pricePlanItemServiceImplUnderTest.getStoreAndItem("planGuid");

        // Verify the results
 assertThat(result).isEqualTo(expectedResult ) ;
    }
                
    @Test
    public void testSelectBrandSkuItemList() throws Exception {
    // Setup
                        final PricePlanItemAddQueryReqDTO request = new PricePlanItemAddQueryReqDTO();
                request.setCurrentPage(0);
                request.setPageSize(0);
                request.setTypeGuid("typeGuid");
                request.setKeywords("keywords");
                request.setPlanGuid("planGuid");

            // Configure PricePlanItemMapper.getBrandSkuItemList(...).
                                                        final PricePlanItemAddQueryRespDTO pricePlanItemAddQueryRespDTO = new PricePlanItemAddQueryRespDTO();
                pricePlanItemAddQueryRespDTO.setItemGuid("itemGuid");
                pricePlanItemAddQueryRespDTO.setName("name");
                pricePlanItemAddQueryRespDTO.setTypeGuid("typeGuid");
                pricePlanItemAddQueryRespDTO.setTypeName("typeName");
                pricePlanItemAddQueryRespDTO.setIsSelected(false);
        final List<PricePlanItemAddQueryRespDTO> pricePlanItemAddQueryRespDTOS = Arrays.asList(pricePlanItemAddQueryRespDTO);
        final PricePlanItemAddQueryReqDTO request1 = new PricePlanItemAddQueryReqDTO();
                request1.setCurrentPage(0);
                request1.setPageSize(0);
                request1.setTypeGuid("typeGuid");
                request1.setKeywords("keywords");
                request1.setPlanGuid("planGuid");
            when( mockPlanItemMapper .getBrandSkuItemList(eq(request1),any(Page.class))).thenReturn(pricePlanItemAddQueryRespDTOS);
 
                             when( mockPlanItemMapper .getPlanItemGuidList("planGuid")).thenReturn( Arrays.asList("value") );

    // Run the test
 final com.holderzone.framework.util.Page<PricePlanItemAddQueryRespDTO> result =  pricePlanItemServiceImplUnderTest.selectBrandSkuItemList(request);

        // Verify the results
    }
                                                                                                
    @Test
    public void testSelectBrandSkuItemList_PricePlanItemMapperGetBrandSkuItemListReturnsNoItems() throws Exception {
    // Setup
                        final PricePlanItemAddQueryReqDTO request = new PricePlanItemAddQueryReqDTO();
                request.setCurrentPage(0);
                request.setPageSize(0);
                request.setTypeGuid("typeGuid");
                request.setKeywords("keywords");
                request.setPlanGuid("planGuid");

        // Configure PricePlanItemMapper.getBrandSkuItemList(...).
        final PricePlanItemAddQueryReqDTO request1 = new PricePlanItemAddQueryReqDTO();
                request1.setCurrentPage(0);
                request1.setPageSize(0);
                request1.setTypeGuid("typeGuid");
                request1.setKeywords("keywords");
                request1.setPlanGuid("planGuid");
        when( mockPlanItemMapper .getBrandSkuItemList(eq(request1),any(Page.class))).thenReturn( Collections.emptyList() );
 
                             when( mockPlanItemMapper .getPlanItemGuidList("planGuid")).thenReturn( Arrays.asList("value") );

    // Run the test
 final com.holderzone.framework.util.Page<PricePlanItemAddQueryRespDTO> result =  pricePlanItemServiceImplUnderTest.selectBrandSkuItemList(request);

        // Verify the results
    }
                                                                        
    @Test
    public void testSelectBrandSkuItemList_PricePlanItemMapperGetPlanItemGuidListReturnsNoItems() throws Exception {
    // Setup
                        final PricePlanItemAddQueryReqDTO request = new PricePlanItemAddQueryReqDTO();
                request.setCurrentPage(0);
                request.setPageSize(0);
                request.setTypeGuid("typeGuid");
                request.setKeywords("keywords");
                request.setPlanGuid("planGuid");

            // Configure PricePlanItemMapper.getBrandSkuItemList(...).
                                                        final PricePlanItemAddQueryRespDTO pricePlanItemAddQueryRespDTO = new PricePlanItemAddQueryRespDTO();
                pricePlanItemAddQueryRespDTO.setItemGuid("itemGuid");
                pricePlanItemAddQueryRespDTO.setName("name");
                pricePlanItemAddQueryRespDTO.setTypeGuid("typeGuid");
                pricePlanItemAddQueryRespDTO.setTypeName("typeName");
                pricePlanItemAddQueryRespDTO.setIsSelected(false);
        final List<PricePlanItemAddQueryRespDTO> pricePlanItemAddQueryRespDTOS = Arrays.asList(pricePlanItemAddQueryRespDTO);
        final PricePlanItemAddQueryReqDTO request1 = new PricePlanItemAddQueryReqDTO();
                request1.setCurrentPage(0);
                request1.setPageSize(0);
                request1.setTypeGuid("typeGuid");
                request1.setKeywords("keywords");
                request1.setPlanGuid("planGuid");
            when( mockPlanItemMapper .getBrandSkuItemList(eq(request1),any(Page.class))).thenReturn(pricePlanItemAddQueryRespDTOS);
 
         when( mockPlanItemMapper .getPlanItemGuidList("planGuid")).thenReturn( Collections.emptyList() );

    // Run the test
 final com.holderzone.framework.util.Page<PricePlanItemAddQueryRespDTO> result =  pricePlanItemServiceImplUnderTest.selectBrandSkuItemList(request);

        // Verify the results
    }
                
    @Test
    public void testGetSyncItem() throws Exception {
    // Setup
                        final SyncItemDTO expectedResult = new SyncItemDTO();
                expectedResult.setItemGuid("itemGuid");
                expectedResult.setIsPkgItem(false);
                expectedResult.setSkuGuid("skuGuid");
                expectedResult.setSalePrice(new BigDecimal("0.00"));
                expectedResult.setMemberPrice(new BigDecimal("0.00"));

            // Configure PricePlanItemMapper.getSyncItem(...).
        final SyncItemDTO syncItemDTO = new SyncItemDTO();
                syncItemDTO.setItemGuid("itemGuid");
                syncItemDTO.setIsPkgItem(false);
                syncItemDTO.setSkuGuid("skuGuid");
                syncItemDTO.setSalePrice(new BigDecimal("0.00"));
                syncItemDTO.setMemberPrice(new BigDecimal("0.00"));
            when( mockPlanItemMapper .getSyncItem("itemGuid","skuGuid")).thenReturn(syncItemDTO);

    // Run the test
 final SyncItemDTO result =  pricePlanItemServiceImplUnderTest.getSyncItem("itemGuid","skuGuid");

        // Verify the results
 assertThat(result).isEqualTo(expectedResult ) ;
    }
                                                                        
    @Test
    public void testQueryTypeByPricePlanGuid() throws Exception {
    // Setup
                                                                        final TypeWebRespDTO typeWebRespDTO = new TypeWebRespDTO();
                typeWebRespDTO.setTypeGuid("typeGuid");
                typeWebRespDTO.setName("name");
                typeWebRespDTO.setBrandGuid("brandGuid");
                typeWebRespDTO.setStoreGuid("storeGuid");
                typeWebRespDTO.setSort(0);
        final List<TypeWebRespDTO> expectedResult = Arrays.asList(typeWebRespDTO);
                
            // Configure TypeMapper.selectList(...).
        final List<TypeDO> typeDOS = Arrays.asList(new TypeDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "9ad04021-9212-4cf7-9fbf-e17e119bb31e", "brandGuid", 0, 0, "description", "iconUrl", 0, 0, 0, "pricePlanGuid", 0,1));
            when( mockTypeMapper .selectList(any(LambdaQueryWrapper.class))).thenReturn(typeDOS);

    // Run the test
 final List<TypeWebRespDTO> result =  pricePlanItemServiceImplUnderTest.queryTypeByPricePlanGuid("pricePlanGuid");

        // Verify the results
 assertThat(result).isEqualTo(expectedResult ) ;
    }
                                                                                                
    @Test
    public void testQueryTypeByPricePlanGuid_TypeMapperReturnsNoItems() throws Exception {
    // Setup
                        when( mockTypeMapper .selectList(any(LambdaQueryWrapper.class))).thenReturn( Collections.emptyList() );

    // Run the test
 final List<TypeWebRespDTO> result =  pricePlanItemServiceImplUnderTest.queryTypeByPricePlanGuid("pricePlanGuid");

        // Verify the results
 assertThat(result).isEqualTo(Collections.emptyList() ) ;
    }
                
    @Test
    public void testUpdateItemList() throws Exception {
    // Setup
                                                                        final PlanItemUpdateDTO planItemUpdateDTO = new PlanItemUpdateDTO();
                planItemUpdateDTO.setPlanItemGuid("planItemGuid");
                planItemUpdateDTO.setPlanGuid("planGuid");
                planItemUpdateDTO.setItemGuid("itemGuid");
                planItemUpdateDTO.setSort(0);
                planItemUpdateDTO.setItemName("itemName");
        final List<PlanItemUpdateDTO> updatePlanItemDTOList = Arrays.asList(planItemUpdateDTO);
                
            // Configure PricePlanItemMapper.updateItemList(...).
                                                        final PlanItemUpdateDTO planItemUpdateDTO1 = new PlanItemUpdateDTO();
                planItemUpdateDTO1.setPlanItemGuid("planItemGuid");
                planItemUpdateDTO1.setPlanGuid("planGuid");
                planItemUpdateDTO1.setItemGuid("itemGuid");
                planItemUpdateDTO1.setSort(0);
                planItemUpdateDTO1.setItemName("itemName");
        final List<PlanItemUpdateDTO> reqDTOList = Arrays.asList(planItemUpdateDTO1);
            when( mockPlanItemMapper .updateItemList(reqDTOList)).thenReturn( false );

    // Run the test
 final Boolean result =  pricePlanItemServiceImplUnderTest.updateItemList(updatePlanItemDTOList);

        // Verify the results
 assertThat(result).isFalse() ;
    }
                                                                        
    @Test
    public void testUpdateItemList_PricePlanItemMapperReturnsTrue() throws Exception {
    // Setup
                                                                        final PlanItemUpdateDTO planItemUpdateDTO = new PlanItemUpdateDTO();
                planItemUpdateDTO.setPlanItemGuid("planItemGuid");
                planItemUpdateDTO.setPlanGuid("planGuid");
                planItemUpdateDTO.setItemGuid("itemGuid");
                planItemUpdateDTO.setSort(0);
                planItemUpdateDTO.setItemName("itemName");
        final List<PlanItemUpdateDTO> updatePlanItemDTOList = Arrays.asList(planItemUpdateDTO);

        // Configure PricePlanItemMapper.updateItemList(...).
                                                        final PlanItemUpdateDTO planItemUpdateDTO1 = new PlanItemUpdateDTO();
                planItemUpdateDTO1.setPlanItemGuid("planItemGuid");
                planItemUpdateDTO1.setPlanGuid("planGuid");
                planItemUpdateDTO1.setItemGuid("itemGuid");
                planItemUpdateDTO1.setSort(0);
                planItemUpdateDTO1.setItemName("itemName");
        final List<PlanItemUpdateDTO> reqDTOList = Arrays.asList(planItemUpdateDTO1);
        when( mockPlanItemMapper .updateItemList(reqDTOList)).thenReturn( true );

    // Run the test
 final Boolean result =  pricePlanItemServiceImplUnderTest.updateItemList(updatePlanItemDTOList);

        // Verify the results
                assertThat(result).isTrue();
    }
                
    @Test
    public void testBatchOperatingItem() throws Exception {
    // Setup
                        final PricePlanItemReqDTO reqDTO = new PricePlanItemReqDTO();
                reqDTO.setPlanGuid("planGuid");
                reqDTO.setIsSoldOut(0);
                reqDTO.setItemGuidList(Arrays.asList("value"));
                reqDTO.setTypeGuid("typeGuid");

    // Run the test
 final Boolean result =  pricePlanItemServiceImplUnderTest.batchOperatingItem(reqDTO);

        // Verify the results
 assertThat(result).isFalse() ;
    }
                                        
    @Test
    public void testQueryPlanItemsByPlan() throws Exception {
    // Setup
                        final ItemQueryReqDTO itemQueryReqDTO = new ItemQueryReqDTO();
                itemQueryReqDTO.setCurrentPage(0L);
                itemQueryReqDTO.setPageSize(0L);
                itemQueryReqDTO.setTypeGuid("typeGuid");
                itemQueryReqDTO.setPlanGuid("planGuid");
                itemQueryReqDTO.setFilterItemList(Arrays.asList("value"));
                itemQueryReqDTO.setFilterSkuList(Arrays.asList("value"));

            // Configure PricePlanItemMapper.queryPlanItemsByPlanAllPage(...).
                                                        final ItemWebRespDTO itemWebRespDTO = new ItemWebRespDTO();
                itemWebRespDTO.setItemGuid("itemGuid");
                itemWebRespDTO.setTypeGuid("typeGuid");
                itemWebRespDTO.setTypeName("itemName");
                itemWebRespDTO.setPictureUrl("");
                itemWebRespDTO.setBigPictureUrl("bigPictureUrl");
                itemWebRespDTO.setDetailBigPictureUrl("detailBigPictureUrl");
                itemWebRespDTO.setItemType(0);
                itemWebRespDTO.setSort(0);
                                            final SkuRespDTO skuRespDTO = new SkuRespDTO();
                skuRespDTO.setSkuGuid("guid");
                skuRespDTO.setSalePrice(new BigDecimal("0.00"));
                skuRespDTO.setMemberPrice(new BigDecimal("0.00"));
                skuRespDTO.setAccountingPrice(new BigDecimal("0.00"));
                skuRespDTO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
                skuRespDTO.setLinePrice(new BigDecimal("0.00"));
                skuRespDTO.setPlanItemGuid("a536644d-c5f0-4f12-b216-b0295a6056d5");
                skuRespDTO.setIsSoldOut(0);
                itemWebRespDTO.setSkuList(Arrays.asList(skuRespDTO));
                itemWebRespDTO.setSalePrice(new BigDecimal("0.00"));
                itemWebRespDTO.setMemberPrice(new BigDecimal("0.00"));
                itemWebRespDTO.setIsSoldOut(0);
                itemWebRespDTO.setDescription("");
                itemWebRespDTO.setPlanItemName("itemName");
                itemWebRespDTO.setSkuGuid("skuGuid");
                itemWebRespDTO.setPlanItemGuid("a536644d-c5f0-4f12-b216-b0295a6056d5");
                itemWebRespDTO.setNewupdateflag(false);
                itemWebRespDTO.setEnglishBrief("englishBrief");
                itemWebRespDTO.setEnglishIngredientsDesc("englishIngredientsDesc");
                itemWebRespDTO.setAccountingPrice(new BigDecimal("0.00"));
                itemWebRespDTO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
                itemWebRespDTO.setLinePrice(new BigDecimal("0.00"));
        final List<ItemWebRespDTO> itemWebRespDTOS = Arrays.asList(itemWebRespDTO);
        final ItemQueryReqDTO itemQueryReqDTO1 = new ItemQueryReqDTO();
                itemQueryReqDTO1.setCurrentPage(0L);
                itemQueryReqDTO1.setPageSize(0L);
                itemQueryReqDTO1.setTypeGuid("typeGuid");
                itemQueryReqDTO1.setPlanGuid("planGuid");
                itemQueryReqDTO1.setFilterItemList(Arrays.asList("value"));
                itemQueryReqDTO1.setFilterSkuList(Arrays.asList("value"));
            when( mockPlanItemMapper .queryPlanItemsByPlanAllPage(eq(itemQueryReqDTO1),any(Page.class))).thenReturn(itemWebRespDTOS);

            // Configure TypeMapper.selectList(...).
        final List<TypeDO> typeDOS = Arrays.asList(new TypeDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "9ad04021-9212-4cf7-9fbf-e17e119bb31e", "brandGuid", 0, 0, "description", "iconUrl", 0, 0, 0, "pricePlanGuid", 0,1));
            when( mockTypeMapper .selectList(any(LambdaQueryWrapper.class))).thenReturn(typeDOS);

            // Configure ISkuService.list(...).
                                                        final SkuDO skuDO = new SkuDO();
                skuDO.setIsDelete(0);
                skuDO.setGuid("guid");
                skuDO.setItemGuid("itemGuid");
                skuDO.setSalePrice(new BigDecimal("0.00"));
                skuDO.setMinOrderNum(new BigDecimal("0.00"));
                skuDO.setIsRack(0);
                skuDO.setIsEnable(false);
        final List<SkuDO> skuDOS = Arrays.asList(skuDO);
            when( mockSkuService .list(any(LambdaQueryWrapper.class))).thenReturn(skuDOS);

            // Configure PricePlanItemMapper.queryPlanItemsByPlanCount(...).
        final ItemQueryReqDTO itemQueryReqDTO2 = new ItemQueryReqDTO();
                itemQueryReqDTO2.setCurrentPage(0L);
                itemQueryReqDTO2.setPageSize(0L);
                itemQueryReqDTO2.setTypeGuid("typeGuid");
                itemQueryReqDTO2.setPlanGuid("planGuid");
                itemQueryReqDTO2.setFilterItemList(Arrays.asList("value"));
                itemQueryReqDTO2.setFilterSkuList(Arrays.asList("value"));
            when( mockPlanItemMapper .queryPlanItemsByPlanCount(itemQueryReqDTO2)).thenReturn( 0 );

    // Run the test
 final com.holderzone.framework.util.Page<ItemWebRespDTO> result =  pricePlanItemServiceImplUnderTest.queryPlanItemsByPlan(itemQueryReqDTO);

        // Verify the results
    }
                                                                                                
    @Test
    public void testQueryPlanItemsByPlan_PricePlanItemMapperQueryPlanItemsByPlanAllPageReturnsNoItems() throws Exception {
    // Setup
                        final ItemQueryReqDTO itemQueryReqDTO = new ItemQueryReqDTO();
                itemQueryReqDTO.setCurrentPage(0L);
                itemQueryReqDTO.setPageSize(0L);
                itemQueryReqDTO.setTypeGuid("typeGuid");
                itemQueryReqDTO.setPlanGuid("planGuid");
                itemQueryReqDTO.setFilterItemList(Arrays.asList("value"));
                itemQueryReqDTO.setFilterSkuList(Arrays.asList("value"));

        // Configure PricePlanItemMapper.queryPlanItemsByPlanAllPage(...).
        final ItemQueryReqDTO itemQueryReqDTO1 = new ItemQueryReqDTO();
                itemQueryReqDTO1.setCurrentPage(0L);
                itemQueryReqDTO1.setPageSize(0L);
                itemQueryReqDTO1.setTypeGuid("typeGuid");
                itemQueryReqDTO1.setPlanGuid("planGuid");
                itemQueryReqDTO1.setFilterItemList(Arrays.asList("value"));
                itemQueryReqDTO1.setFilterSkuList(Arrays.asList("value"));
        when( mockPlanItemMapper .queryPlanItemsByPlanAllPage(eq(itemQueryReqDTO1),any(Page.class))).thenReturn( Collections.emptyList() );

    // Run the test
 final com.holderzone.framework.util.Page<ItemWebRespDTO> result =  pricePlanItemServiceImplUnderTest.queryPlanItemsByPlan(itemQueryReqDTO);

        // Verify the results
    }
                                                                        
    @Test
    public void testQueryPlanItemsByPlan_TypeMapperReturnsNoItems() throws Exception {
    // Setup
                        final ItemQueryReqDTO itemQueryReqDTO = new ItemQueryReqDTO();
                itemQueryReqDTO.setCurrentPage(0L);
                itemQueryReqDTO.setPageSize(0L);
                itemQueryReqDTO.setTypeGuid("typeGuid");
                itemQueryReqDTO.setPlanGuid("planGuid");
                itemQueryReqDTO.setFilterItemList(Arrays.asList("value"));
                itemQueryReqDTO.setFilterSkuList(Arrays.asList("value"));

            // Configure PricePlanItemMapper.queryPlanItemsByPlanAllPage(...).
                                                        final ItemWebRespDTO itemWebRespDTO = new ItemWebRespDTO();
                itemWebRespDTO.setItemGuid("itemGuid");
                itemWebRespDTO.setTypeGuid("typeGuid");
                itemWebRespDTO.setTypeName("itemName");
                itemWebRespDTO.setPictureUrl("");
                itemWebRespDTO.setBigPictureUrl("bigPictureUrl");
                itemWebRespDTO.setDetailBigPictureUrl("detailBigPictureUrl");
                itemWebRespDTO.setItemType(0);
                itemWebRespDTO.setSort(0);
                                            final SkuRespDTO skuRespDTO = new SkuRespDTO();
                skuRespDTO.setSkuGuid("guid");
                skuRespDTO.setSalePrice(new BigDecimal("0.00"));
                skuRespDTO.setMemberPrice(new BigDecimal("0.00"));
                skuRespDTO.setAccountingPrice(new BigDecimal("0.00"));
                skuRespDTO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
                skuRespDTO.setLinePrice(new BigDecimal("0.00"));
                skuRespDTO.setPlanItemGuid("a536644d-c5f0-4f12-b216-b0295a6056d5");
                skuRespDTO.setIsSoldOut(0);
                itemWebRespDTO.setSkuList(Arrays.asList(skuRespDTO));
                itemWebRespDTO.setSalePrice(new BigDecimal("0.00"));
                itemWebRespDTO.setMemberPrice(new BigDecimal("0.00"));
                itemWebRespDTO.setIsSoldOut(0);
                itemWebRespDTO.setDescription("");
                itemWebRespDTO.setPlanItemName("itemName");
                itemWebRespDTO.setSkuGuid("skuGuid");
                itemWebRespDTO.setPlanItemGuid("a536644d-c5f0-4f12-b216-b0295a6056d5");
                itemWebRespDTO.setNewupdateflag(false);
                itemWebRespDTO.setEnglishBrief("englishBrief");
                itemWebRespDTO.setEnglishIngredientsDesc("englishIngredientsDesc");
                itemWebRespDTO.setAccountingPrice(new BigDecimal("0.00"));
                itemWebRespDTO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
                itemWebRespDTO.setLinePrice(new BigDecimal("0.00"));
        final List<ItemWebRespDTO> itemWebRespDTOS = Arrays.asList(itemWebRespDTO);
        final ItemQueryReqDTO itemQueryReqDTO1 = new ItemQueryReqDTO();
                itemQueryReqDTO1.setCurrentPage(0L);
                itemQueryReqDTO1.setPageSize(0L);
                itemQueryReqDTO1.setTypeGuid("typeGuid");
                itemQueryReqDTO1.setPlanGuid("planGuid");
                itemQueryReqDTO1.setFilterItemList(Arrays.asList("value"));
                itemQueryReqDTO1.setFilterSkuList(Arrays.asList("value"));
            when( mockPlanItemMapper .queryPlanItemsByPlanAllPage(eq(itemQueryReqDTO1),any(Page.class))).thenReturn(itemWebRespDTOS);
 
         when( mockTypeMapper .selectList(any(LambdaQueryWrapper.class))).thenReturn( Collections.emptyList() );
                
            // Configure ISkuService.list(...).
                                                        final SkuDO skuDO = new SkuDO();
                skuDO.setIsDelete(0);
                skuDO.setGuid("guid");
                skuDO.setItemGuid("itemGuid");
                skuDO.setSalePrice(new BigDecimal("0.00"));
                skuDO.setMinOrderNum(new BigDecimal("0.00"));
                skuDO.setIsRack(0);
                skuDO.setIsEnable(false);
        final List<SkuDO> skuDOS = Arrays.asList(skuDO);
            when( mockSkuService .list(any(LambdaQueryWrapper.class))).thenReturn(skuDOS);

            // Configure PricePlanItemMapper.queryPlanItemsByPlanCount(...).
        final ItemQueryReqDTO itemQueryReqDTO2 = new ItemQueryReqDTO();
                itemQueryReqDTO2.setCurrentPage(0L);
                itemQueryReqDTO2.setPageSize(0L);
                itemQueryReqDTO2.setTypeGuid("typeGuid");
                itemQueryReqDTO2.setPlanGuid("planGuid");
                itemQueryReqDTO2.setFilterItemList(Arrays.asList("value"));
                itemQueryReqDTO2.setFilterSkuList(Arrays.asList("value"));
            when( mockPlanItemMapper .queryPlanItemsByPlanCount(itemQueryReqDTO2)).thenReturn( 0 );

    // Run the test
 final com.holderzone.framework.util.Page<ItemWebRespDTO> result =  pricePlanItemServiceImplUnderTest.queryPlanItemsByPlan(itemQueryReqDTO);

        // Verify the results
    }
                                                                        
    @Test
    public void testQueryPlanItemsByPlan_ISkuServiceReturnsNoItems() throws Exception {
    // Setup
                        final ItemQueryReqDTO itemQueryReqDTO = new ItemQueryReqDTO();
                itemQueryReqDTO.setCurrentPage(0L);
                itemQueryReqDTO.setPageSize(0L);
                itemQueryReqDTO.setTypeGuid("typeGuid");
                itemQueryReqDTO.setPlanGuid("planGuid");
                itemQueryReqDTO.setFilterItemList(Arrays.asList("value"));
                itemQueryReqDTO.setFilterSkuList(Arrays.asList("value"));

            // Configure PricePlanItemMapper.queryPlanItemsByPlanAllPage(...).
                                                        final ItemWebRespDTO itemWebRespDTO = new ItemWebRespDTO();
                itemWebRespDTO.setItemGuid("itemGuid");
                itemWebRespDTO.setTypeGuid("typeGuid");
                itemWebRespDTO.setTypeName("itemName");
                itemWebRespDTO.setPictureUrl("");
                itemWebRespDTO.setBigPictureUrl("bigPictureUrl");
                itemWebRespDTO.setDetailBigPictureUrl("detailBigPictureUrl");
                itemWebRespDTO.setItemType(0);
                itemWebRespDTO.setSort(0);
                                            final SkuRespDTO skuRespDTO = new SkuRespDTO();
                skuRespDTO.setSkuGuid("guid");
                skuRespDTO.setSalePrice(new BigDecimal("0.00"));
                skuRespDTO.setMemberPrice(new BigDecimal("0.00"));
                skuRespDTO.setAccountingPrice(new BigDecimal("0.00"));
                skuRespDTO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
                skuRespDTO.setLinePrice(new BigDecimal("0.00"));
                skuRespDTO.setPlanItemGuid("a536644d-c5f0-4f12-b216-b0295a6056d5");
                skuRespDTO.setIsSoldOut(0);
                itemWebRespDTO.setSkuList(Arrays.asList(skuRespDTO));
                itemWebRespDTO.setSalePrice(new BigDecimal("0.00"));
                itemWebRespDTO.setMemberPrice(new BigDecimal("0.00"));
                itemWebRespDTO.setIsSoldOut(0);
                itemWebRespDTO.setDescription("");
                itemWebRespDTO.setPlanItemName("itemName");
                itemWebRespDTO.setSkuGuid("skuGuid");
                itemWebRespDTO.setPlanItemGuid("a536644d-c5f0-4f12-b216-b0295a6056d5");
                itemWebRespDTO.setNewupdateflag(false);
                itemWebRespDTO.setEnglishBrief("englishBrief");
                itemWebRespDTO.setEnglishIngredientsDesc("englishIngredientsDesc");
                itemWebRespDTO.setAccountingPrice(new BigDecimal("0.00"));
                itemWebRespDTO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
                itemWebRespDTO.setLinePrice(new BigDecimal("0.00"));
        final List<ItemWebRespDTO> itemWebRespDTOS = Arrays.asList(itemWebRespDTO);
        final ItemQueryReqDTO itemQueryReqDTO1 = new ItemQueryReqDTO();
                itemQueryReqDTO1.setCurrentPage(0L);
                itemQueryReqDTO1.setPageSize(0L);
                itemQueryReqDTO1.setTypeGuid("typeGuid");
                itemQueryReqDTO1.setPlanGuid("planGuid");
                itemQueryReqDTO1.setFilterItemList(Arrays.asList("value"));
                itemQueryReqDTO1.setFilterSkuList(Arrays.asList("value"));
            when( mockPlanItemMapper .queryPlanItemsByPlanAllPage(eq(itemQueryReqDTO1),any(Page.class))).thenReturn(itemWebRespDTOS);

            // Configure TypeMapper.selectList(...).
        final List<TypeDO> typeDOS = Arrays.asList(new TypeDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "9ad04021-9212-4cf7-9fbf-e17e119bb31e", "brandGuid", 0, 0, "description", "iconUrl", 0, 0, 0, "pricePlanGuid", 0,1));
            when( mockTypeMapper .selectList(any(LambdaQueryWrapper.class))).thenReturn(typeDOS);
 
         when( mockSkuService .list(any(LambdaQueryWrapper.class))).thenReturn( Collections.emptyList() );

    // Run the test
        assertThatThrownBy(() -> pricePlanItemServiceImplUnderTest.queryPlanItemsByPlan(itemQueryReqDTO)).isInstanceOf(BusinessException.class);
    }
                                                
    @Test
    public void testCheckTypePricePlan() throws Exception {
    // Setup
                        final ItemQueryReqDTO itemQueryReqDTO = new ItemQueryReqDTO();
                itemQueryReqDTO.setCurrentPage(0L);
                itemQueryReqDTO.setPageSize(0L);
                itemQueryReqDTO.setTypeGuid("typeGuid");
                itemQueryReqDTO.setPlanGuid("planGuid");
                itemQueryReqDTO.setFilterItemList(Arrays.asList("value"));
                itemQueryReqDTO.setFilterSkuList(Arrays.asList("value"));

    // Run the test
 final Boolean result =  pricePlanItemServiceImplUnderTest.checkTypePricePlan(itemQueryReqDTO);

        // Verify the results
 assertThat(result).isFalse() ;
    }
                                        
    @Test
    public void testSaveUpdatePlanItem() throws Exception {
    // Setup
                        final PricePlanItemAddReqDTO reqDTO = new PricePlanItemAddReqDTO();
                reqDTO.setBrandGuid("brandGuid");
                reqDTO.setPlanGuid("planGuid");
                                            final PlanItemDTO planItemDTO = new PlanItemDTO();
                planItemDTO.setItemGuid("itemGuid");
                planItemDTO.setItemType(0);
                reqDTO.setPlanItemDTOList(Arrays.asList(planItemDTO));
                                            final ItemWebRespDTO itemWebRespDTO = new ItemWebRespDTO();
                itemWebRespDTO.setItemGuid("itemGuid");
                itemWebRespDTO.setTypeGuid("typeGuid");
                itemWebRespDTO.setTypeName("itemName");
                itemWebRespDTO.setPictureUrl("");
                itemWebRespDTO.setBigPictureUrl("bigPictureUrl");
                itemWebRespDTO.setDetailBigPictureUrl("detailBigPictureUrl");
                itemWebRespDTO.setItemType(0);
                itemWebRespDTO.setSort(0);
                                            final SkuRespDTO skuRespDTO = new SkuRespDTO();
                skuRespDTO.setSkuGuid("guid");
                skuRespDTO.setSalePrice(new BigDecimal("0.00"));
                skuRespDTO.setMemberPrice(new BigDecimal("0.00"));
                skuRespDTO.setAccountingPrice(new BigDecimal("0.00"));
                skuRespDTO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
                skuRespDTO.setLinePrice(new BigDecimal("0.00"));
                skuRespDTO.setPlanItemGuid("a536644d-c5f0-4f12-b216-b0295a6056d5");
                skuRespDTO.setIsSoldOut(0);
                itemWebRespDTO.setSkuList(Arrays.asList(skuRespDTO));
                itemWebRespDTO.setSalePrice(new BigDecimal("0.00"));
                itemWebRespDTO.setMemberPrice(new BigDecimal("0.00"));
                itemWebRespDTO.setIsSoldOut(0);
                itemWebRespDTO.setDescription("");
                itemWebRespDTO.setPlanItemName("itemName");
                itemWebRespDTO.setSkuGuid("skuGuid");
                itemWebRespDTO.setPlanItemGuid("a536644d-c5f0-4f12-b216-b0295a6056d5");
                itemWebRespDTO.setNewupdateflag(false);
                itemWebRespDTO.setEnglishBrief("englishBrief");
                itemWebRespDTO.setEnglishIngredientsDesc("englishIngredientsDesc");
                itemWebRespDTO.setAccountingPrice(new BigDecimal("0.00"));
                itemWebRespDTO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
                itemWebRespDTO.setLinePrice(new BigDecimal("0.00"));
                reqDTO.setItemWebRespDTOList(Arrays.asList(itemWebRespDTO));
 
         final Map<String,String> saveTypeMap = new HashMap<>();
                                                        final PricePlanItemDO pricePlanItemDO = new PricePlanItemDO();
                pricePlanItemDO.setId(0L);
                pricePlanItemDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
                pricePlanItemDO.setIsDelete(0);
                pricePlanItemDO.setGuid("a536644d-c5f0-4f12-b216-b0295a6056d5");
                pricePlanItemDO.setBrandGuid("brandGuid");
                pricePlanItemDO.setPlanGuid("planGuid");
                pricePlanItemDO.setItemGuid("itemGuid");
                pricePlanItemDO.setSkuGuid("guid");
                pricePlanItemDO.setSalePrice(new BigDecimal("0.00"));
                pricePlanItemDO.setMemberPrice(new BigDecimal("0.00"));
                pricePlanItemDO.setIsPkgItem(false);
                pricePlanItemDO.setPlanItemName("itemName");
                pricePlanItemDO.setIsSoldOut(0);
                pricePlanItemDO.setTypeGuid("typeGuid");
                pricePlanItemDO.setDescription("");
                pricePlanItemDO.setPictureUrl("");
                pricePlanItemDO.setBigPictureUrl("bigPictureUrl");
                pricePlanItemDO.setDetailBigPictureUrl("detailBigPictureUrl");
                pricePlanItemDO.setSort(0);
                pricePlanItemDO.setNewUpdateFlag(false);
                pricePlanItemDO.setEnglishBrief("englishBrief");
                pricePlanItemDO.setEnglishIngredientsDesc("englishIngredientsDesc");
                pricePlanItemDO.setAccountingPrice(new BigDecimal("0.00"));
                pricePlanItemDO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
                pricePlanItemDO.setLinePrice(new BigDecimal("0.00"));
        final List<PricePlanItemDO> expectedResult = Arrays.asList(pricePlanItemDO);

    // Run the test
 final List<PricePlanItemDO> result =  pricePlanItemServiceImplUnderTest.saveUpdatePlanItem(reqDTO,saveTypeMap,false);

        // Verify the results
 assertThat(result).isEqualTo(expectedResult ) ;
    }
                                        
    @Test
    public void testFindPricePlanItemList() throws Exception {
    // Setup
                        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
                itemSingleDTO.setData("data");
                itemSingleDTO.setKeywords("keywords");
                itemSingleDTO.setPlanGuid("planGuid");
                itemSingleDTO.setSkuGuids(Arrays.asList("value"));
                itemSingleDTO.setFilterGuidList(Arrays.asList("value"));
 
                                                         final TypeItemRespDTO typeItemRespDTO = new TypeItemRespDTO();
                typeItemRespDTO.setTypeGuid("typeGuid");
                                            final ItemWebRespDTO itemWebRespDTO = new ItemWebRespDTO();
                itemWebRespDTO.setItemGuid("itemGuid");
                itemWebRespDTO.setTypeGuid("typeGuid");
                itemWebRespDTO.setTypeName("itemName");
                itemWebRespDTO.setPictureUrl("");
                itemWebRespDTO.setBigPictureUrl("bigPictureUrl");
                itemWebRespDTO.setDetailBigPictureUrl("detailBigPictureUrl");
                itemWebRespDTO.setItemType(0);
                itemWebRespDTO.setSort(0);
                                            final SkuRespDTO skuRespDTO = new SkuRespDTO();
                skuRespDTO.setSkuGuid("guid");
                skuRespDTO.setSalePrice(new BigDecimal("0.00"));
                skuRespDTO.setMemberPrice(new BigDecimal("0.00"));
                skuRespDTO.setAccountingPrice(new BigDecimal("0.00"));
                skuRespDTO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
                skuRespDTO.setLinePrice(new BigDecimal("0.00"));
                skuRespDTO.setPlanItemGuid("a536644d-c5f0-4f12-b216-b0295a6056d5");
                skuRespDTO.setIsSoldOut(0);
                itemWebRespDTO.setSkuList(Arrays.asList(skuRespDTO));
                itemWebRespDTO.setSalePrice(new BigDecimal("0.00"));
                itemWebRespDTO.setMemberPrice(new BigDecimal("0.00"));
                itemWebRespDTO.setIsSoldOut(0);
                itemWebRespDTO.setDescription("");
                itemWebRespDTO.setPlanItemName("itemName");
                itemWebRespDTO.setSkuGuid("skuGuid");
                itemWebRespDTO.setPlanItemGuid("a536644d-c5f0-4f12-b216-b0295a6056d5");
                itemWebRespDTO.setNewupdateflag(false);
                itemWebRespDTO.setEnglishBrief("englishBrief");
                itemWebRespDTO.setEnglishIngredientsDesc("englishIngredientsDesc");
                itemWebRespDTO.setAccountingPrice(new BigDecimal("0.00"));
                itemWebRespDTO.setTakeawayAccountingPrice(new BigDecimal("0.00"));
                itemWebRespDTO.setLinePrice(new BigDecimal("0.00"));
                typeItemRespDTO.setItemWebRespDTOList(Arrays.asList(itemWebRespDTO));
        final List<TypeItemRespDTO> expectedResult = Arrays.asList(typeItemRespDTO);
                
            // Configure TypeMapper.selectList(...).
        final List<TypeDO> typeDOS = Arrays.asList(new TypeDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "9ad04021-9212-4cf7-9fbf-e17e119bb31e", "brandGuid", 0, 0, "description", "iconUrl", 0, 0, 0, "pricePlanGuid", 0,1));
            when( mockTypeMapper .selectList(any(LambdaQueryWrapper.class))).thenReturn(typeDOS);

            // Configure ISkuService.list(...).
                                                        final SkuDO skuDO = new SkuDO();
                skuDO.setIsDelete(0);
                skuDO.setGuid("guid");
                skuDO.setItemGuid("itemGuid");
                skuDO.setSalePrice(new BigDecimal("0.00"));
                skuDO.setMinOrderNum(new BigDecimal("0.00"));
                skuDO.setIsRack(0);
                skuDO.setIsEnable(false);
        final List<SkuDO> skuDOS = Arrays.asList(skuDO);
            when( mockSkuService .list(any(LambdaQueryWrapper.class))).thenReturn(skuDOS);

            // Configure ItemMapper.selectList(...).
        final List<ItemDO> itemDOS = Arrays.asList(new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "543f46ff-b015-4eaf-a614-136b50c6a009", "typeGuid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0, "pinyin", "nameAbbr", "description", "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code", 0, 0, 0, "videoUrls", 0, false));
            when( mockItemMapper .selectList(any(LambdaQueryWrapper.class))).thenReturn(itemDOS);

    // Run the test
 final List<TypeItemRespDTO> result =  pricePlanItemServiceImplUnderTest.findPricePlanItemList(itemSingleDTO);

        // Verify the results
 assertThat(result).isEqualTo(expectedResult ) ;
    }
                                                                                                
    @Test
    public void testFindPricePlanItemList_TypeMapperReturnsNoItems() throws Exception {
    // Setup
                        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
                itemSingleDTO.setData("data");
                itemSingleDTO.setKeywords("keywords");
                itemSingleDTO.setPlanGuid("planGuid");
                itemSingleDTO.setSkuGuids(Arrays.asList("value"));
                itemSingleDTO.setFilterGuidList(Arrays.asList("value"));
 
         when( mockTypeMapper .selectList(any(LambdaQueryWrapper.class))).thenReturn( Collections.emptyList() );

    // Run the test
        assertThatThrownBy(() -> pricePlanItemServiceImplUnderTest.findPricePlanItemList(itemSingleDTO)).isInstanceOf(BusinessException.class);
    }
                                                                        
    @Test
    public void testFindPricePlanItemList_ISkuServiceReturnsNoItems() throws Exception {
    // Setup
                        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
                itemSingleDTO.setData("data");
                itemSingleDTO.setKeywords("keywords");
                itemSingleDTO.setPlanGuid("planGuid");
                itemSingleDTO.setSkuGuids(Arrays.asList("value"));
                itemSingleDTO.setFilterGuidList(Arrays.asList("value"));

            // Configure TypeMapper.selectList(...).
        final List<TypeDO> typeDOS = Arrays.asList(new TypeDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "9ad04021-9212-4cf7-9fbf-e17e119bb31e", "brandGuid", 0, 0, "description", "iconUrl", 0, 0, 0, "pricePlanGuid", 0,1));
            when( mockTypeMapper .selectList(any(LambdaQueryWrapper.class))).thenReturn(typeDOS);
 
         when( mockSkuService .list(any(LambdaQueryWrapper.class))).thenReturn( Collections.emptyList() );

    // Run the test
 final List<TypeItemRespDTO> result =  pricePlanItemServiceImplUnderTest.findPricePlanItemList(itemSingleDTO);

        // Verify the results
 assertThat(result).isEqualTo(Collections.emptyList() ) ;
    }
                                                                        
    @Test
    public void testFindPricePlanItemList_ItemMapperReturnsNoItems() throws Exception {
    // Setup
                        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
                itemSingleDTO.setData("data");
                itemSingleDTO.setKeywords("keywords");
                itemSingleDTO.setPlanGuid("planGuid");
                itemSingleDTO.setSkuGuids(Arrays.asList("value"));
                itemSingleDTO.setFilterGuidList(Arrays.asList("value"));

            // Configure TypeMapper.selectList(...).
        final List<TypeDO> typeDOS = Arrays.asList(new TypeDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "9ad04021-9212-4cf7-9fbf-e17e119bb31e", "brandGuid", 0, 0, "description", "iconUrl", 0, 0, 0, "pricePlanGuid", 0,1));
            when( mockTypeMapper .selectList(any(LambdaQueryWrapper.class))).thenReturn(typeDOS);

            // Configure ISkuService.list(...).
                                                        final SkuDO skuDO = new SkuDO();
                skuDO.setIsDelete(0);
                skuDO.setGuid("guid");
                skuDO.setItemGuid("itemGuid");
                skuDO.setSalePrice(new BigDecimal("0.00"));
                skuDO.setMinOrderNum(new BigDecimal("0.00"));
                skuDO.setIsRack(0);
                skuDO.setIsEnable(false);
        final List<SkuDO> skuDOS = Arrays.asList(skuDO);
            when( mockSkuService .list(any(LambdaQueryWrapper.class))).thenReturn(skuDOS);
 
         when( mockItemMapper .selectList(any(LambdaQueryWrapper.class))).thenReturn( Collections.emptyList() );

    // Run the test
 final List<TypeItemRespDTO> result =  pricePlanItemServiceImplUnderTest.findPricePlanItemList(itemSingleDTO);

        // Verify the results
 assertThat(result).isEqualTo(Collections.emptyList() ) ;
    }
                
    @Test
    public void testQueryPlanStoreListByPlan() throws Exception {
    // Setup
                        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
                itemSingleDTO.setData("data");
                itemSingleDTO.setKeywords("keywords");
                itemSingleDTO.setPlanGuid("planGuid");
                itemSingleDTO.setSkuGuids(Arrays.asList("value"));
                itemSingleDTO.setFilterGuidList(Arrays.asList("value"));
 
                             when( mockPlanStoreMapper .getPlanStoreGuidList("planGuid",Arrays.asList("value"))).thenReturn( Arrays.asList("value") );
                
            // Configure OrganizationService.queryStoreByIdList(...).
        final List<StoreDTO> storeDTOS = Arrays.asList(new StoreDTO("4e06c3ae-850f-482c-ac07-1b3fcc9db1d0", "code", "name", "belongBrandGuid", "belongBrandName", "parentIds", LocalTime.of(0, 0, 0), LocalTime.of(0, 0, 0), "contactName", "contactTel", "provinceCode", "provinceName", "cityCode", "cityName", "countyCode", "countyName", "addressDetail", "longitude", "latitude", false, false, "photos", LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "createUserGuid", "modifiedUserGuid", Arrays.asList(new StoreProductDTO("productGuid", "productName", 0, LocalDate.of(2020, 1, 1), LocalDate.of(2020, 1, 1), LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, 0)), Arrays.asList(new BrandDTO("892ecb0a-2219-4d11-93df-7784b04b3c21", "afe4ff5d-0a9d-4876-bfda-e75cbb29dcf1", "name", "description", "logoUrl", false, false, "createUserGuid", "modifiedUserGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "mchntTypeCode", Arrays.asList(), 0, 0, 0, 0, false)), "mchntTypeCode", "logoUrl", "openingHours", 0, 0, 0, 0, "storeNotification", "storeDoorPhoto", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, 0, 0, LocalDate.of(2020, 1, 1)));
            when( mockOrganizationService .queryStoreByIdList(Arrays.asList("value"))).thenReturn(storeDTOS);

    // Run the test
 final List<StoreDTO> result =  pricePlanItemServiceImplUnderTest.queryPlanStoreListByPlan(itemSingleDTO);

        // Verify the results
    }
                                                                                                
    @Test
    public void testQueryPlanStoreListByPlan_PricePlanStoreMapperReturnsNoItems() throws Exception {
    // Setup
                        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
                itemSingleDTO.setData("data");
                itemSingleDTO.setKeywords("keywords");
                itemSingleDTO.setPlanGuid("planGuid");
                itemSingleDTO.setSkuGuids(Arrays.asList("value"));
                itemSingleDTO.setFilterGuidList(Arrays.asList("value"));
 
         when( mockPlanStoreMapper .getPlanStoreGuidList("planGuid",Arrays.asList("value"))).thenReturn( Collections.emptyList() );

    // Run the test
 final List<StoreDTO> result =  pricePlanItemServiceImplUnderTest.queryPlanStoreListByPlan(itemSingleDTO);

        // Verify the results
 assertThat(result).isEqualTo(Collections.emptyList() ) ;
    }
                                                                        
    @Test
    public void testQueryPlanStoreListByPlan_OrganizationServiceReturnsNoItems() throws Exception {
    // Setup
                        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
                itemSingleDTO.setData("data");
                itemSingleDTO.setKeywords("keywords");
                itemSingleDTO.setPlanGuid("planGuid");
                itemSingleDTO.setSkuGuids(Arrays.asList("value"));
                itemSingleDTO.setFilterGuidList(Arrays.asList("value"));
 
                             when( mockPlanStoreMapper .getPlanStoreGuidList("planGuid",Arrays.asList("value"))).thenReturn( Arrays.asList("value") );
        when( mockOrganizationService .queryStoreByIdList(Arrays.asList("value"))).thenReturn( Collections.emptyList() );

    // Run the test
 final List<StoreDTO> result =  pricePlanItemServiceImplUnderTest.queryPlanStoreListByPlan(itemSingleDTO);

        // Verify the results
 assertThat(result).isEqualTo(Collections.emptyList() ) ;
    }
                
    @Test
    public void testUpdatePlanItem() throws Exception {
    // Setup
    // Run the test
 pricePlanItemServiceImplUnderTest.updatePlanItem("planGuid","parentGuid");

        // Verify the results
    }
                                        
    @Test
    public void testPreviewPlanByGuid() throws Exception {
    // Setup
                        final PreviewPlanReqDTO reqDTO = new PreviewPlanReqDTO();
                reqDTO.setPlanGuid("planGuid");
                reqDTO.setBrowseType(0);
                reqDTO.setEnterpriseGuid("enterpriseGuid");
 
         final PreviewPlanRespDTO expectedResult = new PreviewPlanRespDTO();
                expectedResult.setPlanGuid("guid");
                expectedResult.setPlanName("name");
                expectedResult.setBrandGuid("brandGuid");
                expectedResult.setBrandName("brandName");
                expectedResult.setBrandLogoUrl("brandLogoUrl");
                                            final PricePlanItemTypeRespDTO pricePlanItemTypeRespDTO = new PricePlanItemTypeRespDTO();
                pricePlanItemTypeRespDTO.setTypeGuid("typeGuid");
                pricePlanItemTypeRespDTO.setTypeName("itemName");
                                            final PricePlanItemRespDTO pricePlanItemRespDTO = new PricePlanItemRespDTO();
                pricePlanItemRespDTO.setItemGuid("itemGuid");
                pricePlanItemRespDTO.setItemName("itemName");
                pricePlanItemRespDTO.setTypeGuid("typeGuid");
                pricePlanItemRespDTO.setItemType(0);
                pricePlanItemRespDTO.setDescription("");
                pricePlanItemRespDTO.setPictureUrl("");
                pricePlanItemRespDTO.setNewUpdateFlag(false);
                pricePlanItemRespDTO.setMinOrderNum(new BigDecimal("0.00"));
                pricePlanItemRespDTO.setSalePrice(new BigDecimal("0.00"));
                pricePlanItemRespDTO.setOriginalPrice(new BigDecimal("0.00"));
                pricePlanItemRespDTO.setMemberPrice(new BigDecimal("0.00"));
                pricePlanItemTypeRespDTO.setPlanItemRespDTOList(Arrays.asList(pricePlanItemRespDTO));
                expectedResult.setPlanItemTypeRespDTOList(Arrays.asList(pricePlanItemTypeRespDTO));

            // Configure PricePlanMapper.selectOne(...).
        final PricePlanDO pricePlanDO = new PricePlanDO();
                pricePlanDO.setIsDelete(0);
                pricePlanDO.setGuid("guid");
                pricePlanDO.setBrandGuid("brandGuid");
                pricePlanDO.setName("name");
                pricePlanDO.setStatus(0);
            when( mockPlanMapper .selectOne(any(LambdaQueryWrapper.class))).thenReturn(pricePlanDO);

            // Configure OrganizationService.queryBrandByGuid(...).
        final BrandDTO brandDTO = new BrandDTO("892ecb0a-2219-4d11-93df-7784b04b3c21", "afe4ff5d-0a9d-4876-bfda-e75cbb29dcf1", "name", "description", "logoUrl", false, false, "createUserGuid", "modifiedUserGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "mchntTypeCode", Arrays.asList(new StoreDTO("4e06c3ae-850f-482c-ac07-1b3fcc9db1d0", "code", "name", "belongBrandGuid", "belongBrandName", "parentIds", LocalTime.of(0, 0, 0), LocalTime.of(0, 0, 0), "contactName", "contactTel", "provinceCode", "provinceName", "cityCode", "cityName", "countyCode", "countyName", "addressDetail", "longitude", "latitude", false, false, "photos", LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "createUserGuid", "modifiedUserGuid", Arrays.asList(new StoreProductDTO("productGuid", "productName", 0, LocalDate.of(2020, 1, 1), LocalDate.of(2020, 1, 1), LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, 0)), Arrays.asList(), "mchntTypeCode", "logoUrl", "openingHours", 0, 0, 0, 0, "storeNotification", "storeDoorPhoto", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, 0, 0, LocalDate.of(2020, 1, 1))), 0, 0, 0, 0, false);
            when( mockOrganizationService .queryBrandByGuid("brandGuid")).thenReturn(brandDTO);

            // Configure TypeMapper.selectBatchIds(...).
        final List<TypeDO> typeDOS = Arrays.asList(new TypeDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "9ad04021-9212-4cf7-9fbf-e17e119bb31e", "brandGuid", 0, 0, "description", "iconUrl", 0, 0, 0, "pricePlanGuid", 0,1));
            when( mockTypeMapper .selectBatchIds(Arrays.asList("value"))).thenReturn(typeDOS);

            // Configure ItemMapper.selectBatchIds(...).
        final List<ItemDO> itemDOS = Arrays.asList(new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "543f46ff-b015-4eaf-a614-136b50c6a009", "typeGuid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0, "pinyin", "nameAbbr", "description", "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code", 0, 0, 0, "videoUrls", 0, false));
            when( mockItemMapper .selectBatchIds(Arrays.asList("value"))).thenReturn(itemDOS);

            // Configure ISkuService.listByIds(...).
                                                        final SkuDO skuDO = new SkuDO();
                skuDO.setIsDelete(0);
                skuDO.setGuid("guid");
                skuDO.setItemGuid("itemGuid");
                skuDO.setSalePrice(new BigDecimal("0.00"));
                skuDO.setMinOrderNum(new BigDecimal("0.00"));
                skuDO.setIsRack(0);
                skuDO.setIsEnable(false);
        final Collection<SkuDO> skuDOS = Arrays.asList(skuDO);
            when( mockSkuService .listByIds(Arrays.asList("value"))).thenReturn(skuDOS);

    // Run the test
 final PreviewPlanRespDTO result =  pricePlanItemServiceImplUnderTest.previewPlanByGuid(reqDTO);

        // Verify the results
 assertThat(result).isEqualTo(expectedResult ) ;
    }
                                                                                                                                                                
    @Test
    public void testPreviewPlanByGuid_TypeMapperReturnsNoItems() throws Exception {
    // Setup
                        final PreviewPlanReqDTO reqDTO = new PreviewPlanReqDTO();
                reqDTO.setPlanGuid("planGuid");
                reqDTO.setBrowseType(0);
                reqDTO.setEnterpriseGuid("enterpriseGuid");
 
         final PreviewPlanRespDTO expectedResult = new PreviewPlanRespDTO();
                expectedResult.setPlanGuid("guid");
                expectedResult.setPlanName("name");
                expectedResult.setBrandGuid("brandGuid");
                expectedResult.setBrandName("brandName");
                expectedResult.setBrandLogoUrl("brandLogoUrl");
                                            final PricePlanItemTypeRespDTO pricePlanItemTypeRespDTO = new PricePlanItemTypeRespDTO();
                pricePlanItemTypeRespDTO.setTypeGuid("typeGuid");
                pricePlanItemTypeRespDTO.setTypeName("itemName");
                                            final PricePlanItemRespDTO pricePlanItemRespDTO = new PricePlanItemRespDTO();
                pricePlanItemRespDTO.setItemGuid("itemGuid");
                pricePlanItemRespDTO.setItemName("itemName");
                pricePlanItemRespDTO.setTypeGuid("typeGuid");
                pricePlanItemRespDTO.setItemType(0);
                pricePlanItemRespDTO.setDescription("");
                pricePlanItemRespDTO.setPictureUrl("");
                pricePlanItemRespDTO.setNewUpdateFlag(false);
                pricePlanItemRespDTO.setMinOrderNum(new BigDecimal("0.00"));
                pricePlanItemRespDTO.setSalePrice(new BigDecimal("0.00"));
                pricePlanItemRespDTO.setOriginalPrice(new BigDecimal("0.00"));
                pricePlanItemRespDTO.setMemberPrice(new BigDecimal("0.00"));
                pricePlanItemTypeRespDTO.setPlanItemRespDTOList(Arrays.asList(pricePlanItemRespDTO));
                expectedResult.setPlanItemTypeRespDTOList(Arrays.asList(pricePlanItemTypeRespDTO));

            // Configure PricePlanMapper.selectOne(...).
        final PricePlanDO pricePlanDO = new PricePlanDO();
                pricePlanDO.setIsDelete(0);
                pricePlanDO.setGuid("guid");
                pricePlanDO.setBrandGuid("brandGuid");
                pricePlanDO.setName("name");
                pricePlanDO.setStatus(0);
            when( mockPlanMapper .selectOne(any(LambdaQueryWrapper.class))).thenReturn(pricePlanDO);

            // Configure OrganizationService.queryBrandByGuid(...).
        final BrandDTO brandDTO = new BrandDTO("892ecb0a-2219-4d11-93df-7784b04b3c21", "afe4ff5d-0a9d-4876-bfda-e75cbb29dcf1", "name", "description", "logoUrl", false, false, "createUserGuid", "modifiedUserGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "mchntTypeCode", Arrays.asList(new StoreDTO("4e06c3ae-850f-482c-ac07-1b3fcc9db1d0", "code", "name", "belongBrandGuid", "belongBrandName", "parentIds", LocalTime.of(0, 0, 0), LocalTime.of(0, 0, 0), "contactName", "contactTel", "provinceCode", "provinceName", "cityCode", "cityName", "countyCode", "countyName", "addressDetail", "longitude", "latitude", false, false, "photos", LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "createUserGuid", "modifiedUserGuid", Arrays.asList(new StoreProductDTO("productGuid", "productName", 0, LocalDate.of(2020, 1, 1), LocalDate.of(2020, 1, 1), LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, 0)), Arrays.asList(), "mchntTypeCode", "logoUrl", "openingHours", 0, 0, 0, 0, "storeNotification", "storeDoorPhoto", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, 0, 0, LocalDate.of(2020, 1, 1))), 0, 0, 0, 0, false);
            when( mockOrganizationService .queryBrandByGuid("brandGuid")).thenReturn(brandDTO);
 
         when( mockTypeMapper .selectBatchIds(Arrays.asList("value"))).thenReturn( Collections.emptyList() );

    // Run the test
 final PreviewPlanRespDTO result =  pricePlanItemServiceImplUnderTest.previewPlanByGuid(reqDTO);

        // Verify the results
 assertThat(result).isEqualTo(expectedResult ) ;
    }
                                                                        
    @Test
    public void testPreviewPlanByGuid_ItemMapperReturnsNoItems() throws Exception {
    // Setup
                        final PreviewPlanReqDTO reqDTO = new PreviewPlanReqDTO();
                reqDTO.setPlanGuid("planGuid");
                reqDTO.setBrowseType(0);
                reqDTO.setEnterpriseGuid("enterpriseGuid");
 
         final PreviewPlanRespDTO expectedResult = new PreviewPlanRespDTO();
                expectedResult.setPlanGuid("guid");
                expectedResult.setPlanName("name");
                expectedResult.setBrandGuid("brandGuid");
                expectedResult.setBrandName("brandName");
                expectedResult.setBrandLogoUrl("brandLogoUrl");
                                            final PricePlanItemTypeRespDTO pricePlanItemTypeRespDTO = new PricePlanItemTypeRespDTO();
                pricePlanItemTypeRespDTO.setTypeGuid("typeGuid");
                pricePlanItemTypeRespDTO.setTypeName("itemName");
                                            final PricePlanItemRespDTO pricePlanItemRespDTO = new PricePlanItemRespDTO();
                pricePlanItemRespDTO.setItemGuid("itemGuid");
                pricePlanItemRespDTO.setItemName("itemName");
                pricePlanItemRespDTO.setTypeGuid("typeGuid");
                pricePlanItemRespDTO.setItemType(0);
                pricePlanItemRespDTO.setDescription("");
                pricePlanItemRespDTO.setPictureUrl("");
                pricePlanItemRespDTO.setNewUpdateFlag(false);
                pricePlanItemRespDTO.setMinOrderNum(new BigDecimal("0.00"));
                pricePlanItemRespDTO.setSalePrice(new BigDecimal("0.00"));
                pricePlanItemRespDTO.setOriginalPrice(new BigDecimal("0.00"));
                pricePlanItemRespDTO.setMemberPrice(new BigDecimal("0.00"));
                pricePlanItemTypeRespDTO.setPlanItemRespDTOList(Arrays.asList(pricePlanItemRespDTO));
                expectedResult.setPlanItemTypeRespDTOList(Arrays.asList(pricePlanItemTypeRespDTO));

            // Configure PricePlanMapper.selectOne(...).
        final PricePlanDO pricePlanDO = new PricePlanDO();
                pricePlanDO.setIsDelete(0);
                pricePlanDO.setGuid("guid");
                pricePlanDO.setBrandGuid("brandGuid");
                pricePlanDO.setName("name");
                pricePlanDO.setStatus(0);
            when( mockPlanMapper .selectOne(any(LambdaQueryWrapper.class))).thenReturn(pricePlanDO);

            // Configure OrganizationService.queryBrandByGuid(...).
        final BrandDTO brandDTO = new BrandDTO("892ecb0a-2219-4d11-93df-7784b04b3c21", "afe4ff5d-0a9d-4876-bfda-e75cbb29dcf1", "name", "description", "logoUrl", false, false, "createUserGuid", "modifiedUserGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "mchntTypeCode", Arrays.asList(new StoreDTO("4e06c3ae-850f-482c-ac07-1b3fcc9db1d0", "code", "name", "belongBrandGuid", "belongBrandName", "parentIds", LocalTime.of(0, 0, 0), LocalTime.of(0, 0, 0), "contactName", "contactTel", "provinceCode", "provinceName", "cityCode", "cityName", "countyCode", "countyName", "addressDetail", "longitude", "latitude", false, false, "photos", LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "createUserGuid", "modifiedUserGuid", Arrays.asList(new StoreProductDTO("productGuid", "productName", 0, LocalDate.of(2020, 1, 1), LocalDate.of(2020, 1, 1), LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, 0)), Arrays.asList(), "mchntTypeCode", "logoUrl", "openingHours", 0, 0, 0, 0, "storeNotification", "storeDoorPhoto", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, 0, 0, LocalDate.of(2020, 1, 1))), 0, 0, 0, 0, false);
            when( mockOrganizationService .queryBrandByGuid("brandGuid")).thenReturn(brandDTO);

            // Configure TypeMapper.selectBatchIds(...).
        final List<TypeDO> typeDOS = Arrays.asList(new TypeDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "9ad04021-9212-4cf7-9fbf-e17e119bb31e", "brandGuid", 0, 0, "description", "iconUrl", 0, 0, 0, "pricePlanGuid", 0,1));
            when( mockTypeMapper .selectBatchIds(Arrays.asList("value"))).thenReturn(typeDOS);
 
         when( mockItemMapper .selectBatchIds(Arrays.asList("value"))).thenReturn( Collections.emptyList() );

    // Run the test
 final PreviewPlanRespDTO result =  pricePlanItemServiceImplUnderTest.previewPlanByGuid(reqDTO);

        // Verify the results
 assertThat(result).isEqualTo(expectedResult ) ;
    }
                                                                        
    @Test
    public void testPreviewPlanByGuid_ISkuServiceReturnsNoItems() throws Exception {
    // Setup
                        final PreviewPlanReqDTO reqDTO = new PreviewPlanReqDTO();
                reqDTO.setPlanGuid("planGuid");
                reqDTO.setBrowseType(0);
                reqDTO.setEnterpriseGuid("enterpriseGuid");
 
         final PreviewPlanRespDTO expectedResult = new PreviewPlanRespDTO();
                expectedResult.setPlanGuid("guid");
                expectedResult.setPlanName("name");
                expectedResult.setBrandGuid("brandGuid");
                expectedResult.setBrandName("brandName");
                expectedResult.setBrandLogoUrl("brandLogoUrl");
                                            final PricePlanItemTypeRespDTO pricePlanItemTypeRespDTO = new PricePlanItemTypeRespDTO();
                pricePlanItemTypeRespDTO.setTypeGuid("typeGuid");
                pricePlanItemTypeRespDTO.setTypeName("itemName");
                                            final PricePlanItemRespDTO pricePlanItemRespDTO = new PricePlanItemRespDTO();
                pricePlanItemRespDTO.setItemGuid("itemGuid");
                pricePlanItemRespDTO.setItemName("itemName");
                pricePlanItemRespDTO.setTypeGuid("typeGuid");
                pricePlanItemRespDTO.setItemType(0);
                pricePlanItemRespDTO.setDescription("");
                pricePlanItemRespDTO.setPictureUrl("");
                pricePlanItemRespDTO.setNewUpdateFlag(false);
                pricePlanItemRespDTO.setMinOrderNum(new BigDecimal("0.00"));
                pricePlanItemRespDTO.setSalePrice(new BigDecimal("0.00"));
                pricePlanItemRespDTO.setOriginalPrice(new BigDecimal("0.00"));
                pricePlanItemRespDTO.setMemberPrice(new BigDecimal("0.00"));
                pricePlanItemTypeRespDTO.setPlanItemRespDTOList(Arrays.asList(pricePlanItemRespDTO));
                expectedResult.setPlanItemTypeRespDTOList(Arrays.asList(pricePlanItemTypeRespDTO));

            // Configure PricePlanMapper.selectOne(...).
        final PricePlanDO pricePlanDO = new PricePlanDO();
                pricePlanDO.setIsDelete(0);
                pricePlanDO.setGuid("guid");
                pricePlanDO.setBrandGuid("brandGuid");
                pricePlanDO.setName("name");
                pricePlanDO.setStatus(0);
            when( mockPlanMapper .selectOne(any(LambdaQueryWrapper.class))).thenReturn(pricePlanDO);

            // Configure OrganizationService.queryBrandByGuid(...).
        final BrandDTO brandDTO = new BrandDTO("892ecb0a-2219-4d11-93df-7784b04b3c21", "afe4ff5d-0a9d-4876-bfda-e75cbb29dcf1", "name", "description", "logoUrl", false, false, "createUserGuid", "modifiedUserGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "mchntTypeCode", Arrays.asList(new StoreDTO("4e06c3ae-850f-482c-ac07-1b3fcc9db1d0", "code", "name", "belongBrandGuid", "belongBrandName", "parentIds", LocalTime.of(0, 0, 0), LocalTime.of(0, 0, 0), "contactName", "contactTel", "provinceCode", "provinceName", "cityCode", "cityName", "countyCode", "countyName", "addressDetail", "longitude", "latitude", false, false, "photos", LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "createUserGuid", "modifiedUserGuid", Arrays.asList(new StoreProductDTO("productGuid", "productName", 0, LocalDate.of(2020, 1, 1), LocalDate.of(2020, 1, 1), LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, 0)), Arrays.asList(), "mchntTypeCode", "logoUrl", "openingHours", 0, 0, 0, 0, "storeNotification", "storeDoorPhoto", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, 0, 0, LocalDate.of(2020, 1, 1))), 0, 0, 0, 0, false);
            when( mockOrganizationService .queryBrandByGuid("brandGuid")).thenReturn(brandDTO);

            // Configure TypeMapper.selectBatchIds(...).
        final List<TypeDO> typeDOS = Arrays.asList(new TypeDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "9ad04021-9212-4cf7-9fbf-e17e119bb31e", "brandGuid", 0, 0, "description", "iconUrl", 0, 0, 0, "pricePlanGuid", 0,1));
            when( mockTypeMapper .selectBatchIds(Arrays.asList("value"))).thenReturn(typeDOS);

            // Configure ItemMapper.selectBatchIds(...).
        final List<ItemDO> itemDOS = Arrays.asList(new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "543f46ff-b015-4eaf-a614-136b50c6a009", "typeGuid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0, "pinyin", "nameAbbr", "description", "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code", 0, 0, 0, "videoUrls", 0, false));
            when( mockItemMapper .selectBatchIds(Arrays.asList("value"))).thenReturn(itemDOS);
 
         when( mockSkuService .listByIds(Arrays.asList("value"))).thenReturn( Collections.emptyList() );

    // Run the test
 final PreviewPlanRespDTO result =  pricePlanItemServiceImplUnderTest.previewPlanByGuid(reqDTO);

        // Verify the results
 assertThat(result).isEqualTo(expectedResult ) ;
    }
                
    @Test
    public void testListAllPlanPriceItem() throws Exception {
    // Setup
                        final PlanPriceAllItemReqDTO req = new PlanPriceAllItemReqDTO();
                req.setCurrentPage(0L);
                req.setPageSize(0L);
                req.setBrandGuid("brandGuid");
                req.setSearchKey("searchKey");
                req.setExcludeItemGuidList(Arrays.asList("value"));
 
                                                         final PlanPriceAllTypeRespDTO planPriceAllTypeRespDTO = new PlanPriceAllTypeRespDTO();
                planPriceAllTypeRespDTO.setTypeGuid("typeGuid");
                planPriceAllTypeRespDTO.setTypeName("typeName");
                                            final PlanPriceAllItemRespDTO planPriceAllItemRespDTO = new PlanPriceAllItemRespDTO();
                planPriceAllItemRespDTO.setItemGuid("itemGuid");
                planPriceAllItemRespDTO.setTypeGuid("typeGuid");
                planPriceAllTypeRespDTO.setItemList(Arrays.asList(planPriceAllItemRespDTO));
        final List<PlanPriceAllTypeRespDTO> expectedResult = Arrays.asList(planPriceAllTypeRespDTO);
                
            // Configure PricePlanItemMapper.listAllPlanPriceItem(...).
                                                        final PlanPriceAllItemRespDTO planPriceAllItemRespDTO1 = new PlanPriceAllItemRespDTO();
                planPriceAllItemRespDTO1.setItemGuid("itemGuid");
                planPriceAllItemRespDTO1.setItemName("itemName");
                planPriceAllItemRespDTO1.setTypeGuid("typeGuid");
        final List<PlanPriceAllItemRespDTO> planPriceAllItemRespDTOS = Arrays.asList(planPriceAllItemRespDTO1);
        final PlanPriceAllItemReqDTO req1 = new PlanPriceAllItemReqDTO();
                req1.setCurrentPage(0L);
                req1.setPageSize(0L);
                req1.setBrandGuid("brandGuid");
                req1.setSearchKey("searchKey");
                req1.setExcludeItemGuidList(Arrays.asList("value"));
            when( mockPlanItemMapper .listAllPlanPriceItem(req1)).thenReturn(planPriceAllItemRespDTOS);

            // Configure TypeMapper.selectBatchIds(...).
        final List<TypeDO> typeDOS = Arrays.asList(new TypeDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "9ad04021-9212-4cf7-9fbf-e17e119bb31e", "brandGuid", 0, 0, "description", "iconUrl", 0, 0, 0, "pricePlanGuid", 0,1));
            when( mockTypeMapper .selectBatchIds(Arrays.asList("value"))).thenReturn(typeDOS);

    // Run the test
 final List<PlanPriceAllTypeRespDTO> result =  pricePlanItemServiceImplUnderTest.listAllPlanPriceItem(req);

        // Verify the results
 assertThat(result).isEqualTo(expectedResult ) ;
    }
                                                                                                
    @Test
    public void testListAllPlanPriceItem_PricePlanItemMapperReturnsNoItems() throws Exception {
    // Setup
                        final PlanPriceAllItemReqDTO req = new PlanPriceAllItemReqDTO();
                req.setCurrentPage(0L);
                req.setPageSize(0L);
                req.setBrandGuid("brandGuid");
                req.setSearchKey("searchKey");
                req.setExcludeItemGuidList(Arrays.asList("value"));

        // Configure PricePlanItemMapper.listAllPlanPriceItem(...).
        final PlanPriceAllItemReqDTO req1 = new PlanPriceAllItemReqDTO();
                req1.setCurrentPage(0L);
                req1.setPageSize(0L);
                req1.setBrandGuid("brandGuid");
                req1.setSearchKey("searchKey");
                req1.setExcludeItemGuidList(Arrays.asList("value"));
        when( mockPlanItemMapper .listAllPlanPriceItem(req1)).thenReturn( Collections.emptyList() );

    // Run the test
 final List<PlanPriceAllTypeRespDTO> result =  pricePlanItemServiceImplUnderTest.listAllPlanPriceItem(req);

        // Verify the results
 assertThat(result).isEqualTo(Collections.emptyList() ) ;
    }
                                                                        
    @Test
    public void testListAllPlanPriceItem_TypeMapperReturnsNoItems() throws Exception {
    // Setup
                        final PlanPriceAllItemReqDTO req = new PlanPriceAllItemReqDTO();
                req.setCurrentPage(0L);
                req.setPageSize(0L);
                req.setBrandGuid("brandGuid");
                req.setSearchKey("searchKey");
                req.setExcludeItemGuidList(Arrays.asList("value"));

            // Configure PricePlanItemMapper.listAllPlanPriceItem(...).
                                                        final PlanPriceAllItemRespDTO planPriceAllItemRespDTO = new PlanPriceAllItemRespDTO();
                planPriceAllItemRespDTO.setItemGuid("itemGuid");
                planPriceAllItemRespDTO.setItemName("itemName");
                planPriceAllItemRespDTO.setTypeGuid("typeGuid");
        final List<PlanPriceAllItemRespDTO> planPriceAllItemRespDTOS = Arrays.asList(planPriceAllItemRespDTO);
        final PlanPriceAllItemReqDTO req1 = new PlanPriceAllItemReqDTO();
                req1.setCurrentPage(0L);
                req1.setPageSize(0L);
                req1.setBrandGuid("brandGuid");
                req1.setSearchKey("searchKey");
                req1.setExcludeItemGuidList(Arrays.asList("value"));
            when( mockPlanItemMapper .listAllPlanPriceItem(req1)).thenReturn(planPriceAllItemRespDTOS);
 
         when( mockTypeMapper .selectBatchIds(Arrays.asList("value"))).thenReturn( Collections.emptyList() );

    // Run the test
 final List<PlanPriceAllTypeRespDTO> result =  pricePlanItemServiceImplUnderTest.listAllPlanPriceItem(req);

        // Verify the results
 assertThat(result).isEqualTo(Collections.emptyList() ) ;
    }
                
    @Test
    public void testListAllPlanPriceItemType() throws Exception {
    // Setup
                        final PlanPriceAllItemReqDTO req = new PlanPriceAllItemReqDTO();
                req.setCurrentPage(0L);
                req.setPageSize(0L);
                req.setBrandGuid("brandGuid");
                req.setSearchKey("searchKey");
                req.setExcludeItemGuidList(Arrays.asList("value"));
 
                                                         final TypeRespDTO typeRespDTO = new TypeRespDTO();
                typeRespDTO.setTypeGuid("typeGuid");
                typeRespDTO.setName("name");
                typeRespDTO.setStoreGuid("storeGuid");
                typeRespDTO.setDescription("description");
                typeRespDTO.setSort(0);
        final List<TypeRespDTO> expectedResult = Arrays.asList(typeRespDTO);
                            when( mockPlanItemMapper .listAllPlanPriceItemTypeGuid("brandGuid")).thenReturn( Arrays.asList("value") );
                
            // Configure TypeMapper.selectBatchIds(...).
        final List<TypeDO> typeDOS = Arrays.asList(new TypeDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "9ad04021-9212-4cf7-9fbf-e17e119bb31e", "brandGuid", 0, 0, "description", "iconUrl", 0, 0, 0, "pricePlanGuid", 0,1));
            when( mockTypeMapper .selectBatchIds(Arrays.asList("value"))).thenReturn(typeDOS);

    // Run the test
 final List<TypeRespDTO> result =  pricePlanItemServiceImplUnderTest.listAllPlanPriceItemType(req);

        // Verify the results
 assertThat(result).isEqualTo(expectedResult ) ;
    }
                                                                                                
    @Test
    public void testListAllPlanPriceItemType_PricePlanItemMapperReturnsNoItems() throws Exception {
    // Setup
                        final PlanPriceAllItemReqDTO req = new PlanPriceAllItemReqDTO();
                req.setCurrentPage(0L);
                req.setPageSize(0L);
                req.setBrandGuid("brandGuid");
                req.setSearchKey("searchKey");
                req.setExcludeItemGuidList(Arrays.asList("value"));
 
         when( mockPlanItemMapper .listAllPlanPriceItemTypeGuid("brandGuid")).thenReturn( Collections.emptyList() );

    // Run the test
 final List<TypeRespDTO> result =  pricePlanItemServiceImplUnderTest.listAllPlanPriceItemType(req);

        // Verify the results
 assertThat(result).isEqualTo(Collections.emptyList() ) ;
    }
                                                                        
    @Test
    public void testListAllPlanPriceItemType_TypeMapperReturnsNoItems() throws Exception {
    // Setup
                        final PlanPriceAllItemReqDTO req = new PlanPriceAllItemReqDTO();
                req.setCurrentPage(0L);
                req.setPageSize(0L);
                req.setBrandGuid("brandGuid");
                req.setSearchKey("searchKey");
                req.setExcludeItemGuidList(Arrays.asList("value"));
 
                             when( mockPlanItemMapper .listAllPlanPriceItemTypeGuid("brandGuid")).thenReturn( Arrays.asList("value") );
        when( mockTypeMapper .selectBatchIds(Arrays.asList("value"))).thenReturn( Collections.emptyList() );

    // Run the test
 final List<TypeRespDTO> result =  pricePlanItemServiceImplUnderTest.listAllPlanPriceItemType(req);

        // Verify the results
 assertThat(result).isEqualTo(Collections.emptyList() ) ;
    }
                
    @Test
    public void testListItemSkuAndPlanPrice() throws Exception {
    // Setup
                        final ItemSkuAndPlanPriceDTO expectedResult = new ItemSkuAndPlanPriceDTO();
                expectedResult.setItemGuid("itemGuid");
                expectedResult.setItemName("itemName");
                expectedResult.setPictureUrl("pictureUrl");
                expectedResult.setDescription("description");
                expectedResult.setEnglishBrief("englishBrief");

            // Configure PricePlanItemMapper.listItemSkuAndPlanPrice(...).
        final ItemSkuAndPlanPriceDTO itemSkuAndPlanPriceDTO = new ItemSkuAndPlanPriceDTO();
                itemSkuAndPlanPriceDTO.setItemGuid("itemGuid");
                itemSkuAndPlanPriceDTO.setItemName("itemName");
                itemSkuAndPlanPriceDTO.setPictureUrl("pictureUrl");
                itemSkuAndPlanPriceDTO.setDescription("description");
                itemSkuAndPlanPriceDTO.setEnglishBrief("englishBrief");
            when( mockPlanItemMapper .listItemSkuAndPlanPrice("itemGuid")).thenReturn(itemSkuAndPlanPriceDTO);

    // Run the test
 final ItemSkuAndPlanPriceDTO result =  pricePlanItemServiceImplUnderTest.listItemSkuAndPlanPrice("itemGuid");

        // Verify the results
 assertThat(result).isEqualTo(expectedResult ) ;
    }
                                                                        
    @Test
    public void testListItemTypeAndPlanPrice() throws Exception {
    // Setup
                                                                        final PlanPriceEditDTO planPriceEditDTO = new PlanPriceEditDTO();
                planPriceEditDTO.setPlanGuid("planGuid");
                planPriceEditDTO.setPlanName("planName");
                planPriceEditDTO.setStatus(0);
                planPriceEditDTO.setSellTimeType(0);
                planPriceEditDTO.setBrandGuid("brandGuid");
        final List<PlanPriceEditDTO> expectedResult = Arrays.asList(planPriceEditDTO);
                
            // Configure PricePlanItemMapper.listItemTypeAndPlanPrice(...).
                                                        final PlanPriceEditDTO planPriceEditDTO1 = new PlanPriceEditDTO();
                planPriceEditDTO1.setPlanGuid("planGuid");
                planPriceEditDTO1.setPlanName("planName");
                planPriceEditDTO1.setStatus(0);
                planPriceEditDTO1.setSellTimeType(0);
                planPriceEditDTO1.setBrandGuid("brandGuid");
        final List<PlanPriceEditDTO> planPriceEditDTOS = Arrays.asList(planPriceEditDTO1);
            when( mockPlanItemMapper .listItemTypeAndPlanPrice("typeName")).thenReturn(planPriceEditDTOS);

    // Run the test
 final List<PlanPriceEditDTO> result =  pricePlanItemServiceImplUnderTest.listItemTypeAndPlanPrice("typeName");

        // Verify the results
 assertThat(result).isEqualTo(expectedResult ) ;
    }
                                                                                                
    @Test
    public void testListItemTypeAndPlanPrice_PricePlanItemMapperReturnsNoItems() throws Exception {
    // Setup
                        when( mockPlanItemMapper .listItemTypeAndPlanPrice("typeName")).thenReturn( Collections.emptyList() );

    // Run the test
 final List<PlanPriceEditDTO> result =  pricePlanItemServiceImplUnderTest.listItemTypeAndPlanPrice("typeName");

        // Verify the results
 assertThat(result).isEqualTo(Collections.emptyList() ) ;
    }
                
    @Test
    public void testListAllPlanPriceItemSku() throws Exception {
    // Setup
                        final PlanPriceAllItemSkuReqDTO req = new PlanPriceAllItemSkuReqDTO();
                req.setCurrentPage(0L);
                req.setPageSize(0L);
                req.setBrandGuid("brandGuid");
                req.setSearchKey("searchKey");
                req.setExcludeSkuGuidList(Arrays.asList("value"));

            // Configure PricePlanItemMapper.listAllPlanPriceItemSku(...).
                                                        final PlanPriceAllItemSkuRespDTO planPriceAllItemSkuRespDTO = new PlanPriceAllItemSkuRespDTO();
                planPriceAllItemSkuRespDTO.setItemGuid("itemGuid");
                planPriceAllItemSkuRespDTO.setItemName("itemName");
                planPriceAllItemSkuRespDTO.setSkuGuid("skuGuid");
                planPriceAllItemSkuRespDTO.setSkuName("skuName");
                planPriceAllItemSkuRespDTO.setItemSkuName("itemSkuName");
        final List<PlanPriceAllItemSkuRespDTO> planPriceAllItemSkuRespDTOS = Arrays.asList(planPriceAllItemSkuRespDTO);
        final PlanPriceAllItemSkuReqDTO req1 = new PlanPriceAllItemSkuReqDTO();
                req1.setCurrentPage(0L);
                req1.setPageSize(0L);
                req1.setBrandGuid("brandGuid");
                req1.setSearchKey("searchKey");
                req1.setExcludeSkuGuidList(Arrays.asList("value"));
            when( mockPlanItemMapper .listAllPlanPriceItemSku(eq(req1),any(Page.class))).thenReturn(planPriceAllItemSkuRespDTOS);

            // Configure PricePlanItemMapper.listAllPlanPriceItemSkuCount(...).
        final PlanPriceAllItemSkuReqDTO req2 = new PlanPriceAllItemSkuReqDTO();
                req2.setCurrentPage(0L);
                req2.setPageSize(0L);
                req2.setBrandGuid("brandGuid");
                req2.setSearchKey("searchKey");
                req2.setExcludeSkuGuidList(Arrays.asList("value"));
            when( mockPlanItemMapper .listAllPlanPriceItemSkuCount(req2)).thenReturn( 0 );

    // Run the test
 final com.holderzone.framework.util.Page<PlanPriceAllItemSkuRespDTO> result =  pricePlanItemServiceImplUnderTest.listAllPlanPriceItemSku(req);

        // Verify the results
    }
                                                                                                
    @Test
    public void testListAllPlanPriceItemSku_PricePlanItemMapperListAllPlanPriceItemSkuReturnsNoItems() throws Exception {
    // Setup
                        final PlanPriceAllItemSkuReqDTO req = new PlanPriceAllItemSkuReqDTO();
                req.setCurrentPage(0L);
                req.setPageSize(0L);
                req.setBrandGuid("brandGuid");
                req.setSearchKey("searchKey");
                req.setExcludeSkuGuidList(Arrays.asList("value"));

        // Configure PricePlanItemMapper.listAllPlanPriceItemSku(...).
        final PlanPriceAllItemSkuReqDTO req1 = new PlanPriceAllItemSkuReqDTO();
                req1.setCurrentPage(0L);
                req1.setPageSize(0L);
                req1.setBrandGuid("brandGuid");
                req1.setSearchKey("searchKey");
                req1.setExcludeSkuGuidList(Arrays.asList("value"));
        when( mockPlanItemMapper .listAllPlanPriceItemSku(eq(req1),any(Page.class))).thenReturn( Collections.emptyList() );

            // Configure PricePlanItemMapper.listAllPlanPriceItemSkuCount(...).
        final PlanPriceAllItemSkuReqDTO req2 = new PlanPriceAllItemSkuReqDTO();
                req2.setCurrentPage(0L);
                req2.setPageSize(0L);
                req2.setBrandGuid("brandGuid");
                req2.setSearchKey("searchKey");
                req2.setExcludeSkuGuidList(Arrays.asList("value"));
            when( mockPlanItemMapper .listAllPlanPriceItemSkuCount(req2)).thenReturn( 0 );

    // Run the test
 final com.holderzone.framework.util.Page<PlanPriceAllItemSkuRespDTO> result =  pricePlanItemServiceImplUnderTest.listAllPlanPriceItemSku(req);

        // Verify the results
    }
                                    @Test
    public void testSaveBatchEditPlanPrice() throws Exception {
        pricePlanItemServiceImplUnderTest.saveBatchEditPlanPrice(new PlanPriceEditReqDTO());
    }
}

