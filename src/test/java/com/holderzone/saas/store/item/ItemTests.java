package com.holderzone.saas.store.item;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.common.UserInfoDTO;
import com.holderzone.saas.store.item.dto.SyncItemDTO;
import com.holderzone.saas.store.item.service.IItemService;
import com.holderzone.saas.store.item.service.SyncPricePlanToItemService;
import com.holderzone.saas.store.item.util.SpringContextUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.Arrays;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@SpringBootTest
public class ItemTests {
    @Autowired
    private WebApplicationContext wac;
    private MockMvc mockMvc;

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private IItemService iItemService;

    @Autowired
    private SyncPricePlanToItemService syncPricePlanToItemService;

    @Before
    public void setup() {
        String enterpriseGuid = "6506431195651982337";
        UserContext userInfoDTO = UserContextUtils.get();
        userInfoDTO.setEnterpriseGuid(enterpriseGuid);
        SpringContextUtils.getInstance().setCfgContext((ConfigurableApplicationContext) applicationContext);
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();   //构造MockMvc
    }

    @Test
    public  void  test()   {
        String itemGuid ="6613612843482742784";
//        iItemService.updateByGuid(itemGuid);
    }

    @Test
    public  void  getJournalingItem() throws Exception {
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setEnterpriseGuid("6506431195651982337");
        userInfoDTO.setStoreGuid("6506453252643487745");
        String encode = URLEncoder.encode(JacksonUtils.writeValueAsString(userInfoDTO), "utf-8");
        MvcResult mvcResult = mockMvc.perform(post("/item/get_journaling_item").header("userInfo",encode).accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JacksonUtils.writeValueAsString(new BaseDTO())))
                .andExpect(status().is4xxClientError()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }


    @Test
    public  void  getItemPictureUrls() throws Exception {
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setEnterpriseGuid("6506431195651982337");
        userInfoDTO.setStoreGuid("6506453252643487745");
        SingleDataDTO request = new SingleDataDTO();
        request.setDatas(Arrays.asList("6550654253111580673","6550654253346520065"));
        String encode = URLEncoder.encode(JacksonUtils.writeValueAsString(userInfoDTO), "utf-8");
        MvcResult mvcResult = mockMvc.perform(post("/item/get_item_picture_urls").header("userInfo",encode).accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JacksonUtils.writeValueAsString(request)))
                .andExpect(status().is4xxClientError()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }


    @Test
    public void  androidSyncTest() throws Exception{
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setEnterpriseGuid("6506431195651982337");
        String encode = URLEncoder.encode(JacksonUtils.writeValueAsString(userInfoDTO), "utf-8");
        BaseDTO request  = new BaseDTO();
        request.setStoreGuid("6506453252643487745");
        MvcResult mvcResult = mockMvc.perform(post("/item/query_for_synchronize").header("userInfo",encode).accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JacksonUtils.writeValueAsString(request)))
                .andExpect(status().is4xxClientError()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }

    @Test
    public void testAAA(){
        String storeGuid = "6619160595813892096";
        String brandGuid = "6506433399404625921";
        String pricePlanGuid = "6726754601921413120";
        // storeLists.add("6619160595813892096");
        // storeLists.add("6653494971712143360");
        //syncStoreAndItemDTO.setStoreGuid(storeLists);
        SyncItemDTO syncItemDTO = new SyncItemDTO();
        syncItemDTO.setIsPkgItem(false);
        syncItemDTO.setItemGuid("6715199361963261952");
        syncItemDTO.setSalePrice(BigDecimal.valueOf(50.00));
        syncItemDTO.setMemberPrice(BigDecimal.valueOf(40.00));
        syncItemDTO.setSkuGuid("6692326610080628736");
        syncPricePlanToItemService.addPricePlanItem(syncItemDTO, storeGuid, brandGuid, pricePlanGuid);
    }
}
