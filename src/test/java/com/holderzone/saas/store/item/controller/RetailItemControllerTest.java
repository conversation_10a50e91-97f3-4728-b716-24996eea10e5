package com.holderzone.saas.store.item.controller;

import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.req.ItemQueryReqDTO;
import com.holderzone.saas.store.dto.item.req.ItemRetailSortReqDTO;
import com.holderzone.saas.store.dto.item.req.ItemSortReqDTO;
import com.holderzone.saas.store.dto.item.req.TypeSortReqDTO;
import com.holderzone.saas.store.dto.item.resp.ItemRetailSortRespDTO;
import com.holderzone.saas.store.dto.item.resp.ItemSortRespDTO;
import com.holderzone.saas.store.dto.item.resp.ItemWebRespDTO;
import com.holderzone.saas.store.dto.item.resp.TypeSortRespDTO;
import com.holderzone.saas.store.item.service.IRetailItemService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

@RunWith(SpringRunner.class)
@WebMvcTest(RetailItemController.class)
public class RetailItemControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private IRetailItemService mockRetailItemService;

    @Test
    public void testSelectItemForWeb() throws Exception {
        // Setup
        // Configure IRetailItemService.selectItemListForWeb(...).
        final ItemWebRespDTO itemWebRespDTO = new ItemWebRespDTO();
        itemWebRespDTO.setItemGuid("itemGuid");
        itemWebRespDTO.setParentGuid("parentGuid");
        itemWebRespDTO.setName("name");
        itemWebRespDTO.setCode("code");
        itemWebRespDTO.setTypeGuid("typeGuid");
        final Page<ItemWebRespDTO> itemWebRespDTOPage = new Page<>(0L, 0L, Arrays.asList(itemWebRespDTO));
        final ItemQueryReqDTO itemQueryReqDTO = new ItemQueryReqDTO();
        itemQueryReqDTO.setName("name");
        itemQueryReqDTO.setTypeGuid("typeGuid");
        itemQueryReqDTO.setIsRack(0);
        itemQueryReqDTO.setIsJoinAio(0);
        itemQueryReqDTO.setIsJoinPos(0);
        when(mockRetailItemService.selectItemListForWeb(itemQueryReqDTO)).thenReturn(itemWebRespDTOPage);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/retail/item/select_item_for_web")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testGetItemSortList() throws Exception {
        // Setup
        // Configure IRetailItemService.getItemSortList(...).
        final Page<ItemSortRespDTO> itemSortRespDTOPage = new Page<>(0L, 0L,
                Arrays.asList(new ItemSortRespDTO(0, "de7a866b-abcb-4024-a18c-337d7c4ca61e", "name", 0)));
        final ItemQueryReqDTO request = new ItemQueryReqDTO();
        request.setName("name");
        request.setTypeGuid("typeGuid");
        request.setIsRack(0);
        request.setIsJoinAio(0);
        request.setIsJoinPos(0);
        when(mockRetailItemService.getItemSortList(request)).thenReturn(itemSortRespDTOPage);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/retail/item/get_item_sort_list")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testRetailUpdateItemSort() throws Exception {
        // Setup
        when(mockRetailItemService.retailUpdateItemSort(new ItemRetailSortReqDTO(Arrays.asList(
                new TypeSortReqDTO("8ce0bc50-da50-408c-80eb-0773688815ad", "name", 0,
                        Arrays.asList(new ItemSortReqDTO(0, "18be2e69-42ca-461d-ab6d-2c9317f22927", "name", 0)),
                        Arrays.asList("value")))))).thenReturn(false);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/retail/item/retail_update_item_sort")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testGetSortTypeAndItems() throws Exception {
        // Setup
        // Configure IRetailItemService.getSortTypeAndItems(...).
        final ItemRetailSortRespDTO itemRetailSortRespDTO = new ItemRetailSortRespDTO(Arrays.asList(
                new TypeSortRespDTO("86bd9458-252a-4db5-8670-3a0d52843c49", "name", 0,
                        Arrays.asList(new ItemSortRespDTO(0, "de7a866b-abcb-4024-a18c-337d7c4ca61e", "name", 0)))));
        final ItemSingleDTO request = new ItemSingleDTO();
        request.setFrom(0);
        request.setData("data");
        request.setKeywords("keywords");
        request.setModel(0);
        request.setItemQueryType(0);
        when(mockRetailItemService.getSortTypeAndItems(request)).thenReturn(itemRetailSortRespDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/retail/item/get_sort_type_and_item")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }
}
