package com.holderzone.saas.store.item.service.impl;

import com.holderzone.saas.store.item.entity.domain.EstimateSellLogDO;
import com.holderzone.saas.store.item.util.DynamicHelper;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;

@RunWith(MockitoJUnitRunner.class)
public class IEstimateSellLogServiceImplTest {

    @Mock
    private DynamicHelper mockDynamicHelper;

    private IEstimateSellLogServiceImpl iEstimateSellLogServiceImplUnderTest;

    @Before
    public void setUp() {
        iEstimateSellLogServiceImplUnderTest = new IEstimateSellLogServiceImpl(mockDynamicHelper);
    }

    @Test
    public void testSaveSellLog() {
        // Setup
        final List<EstimateSellLogDO> sellLogList = Arrays.asList(EstimateSellLogDO.builder()
                .guid("2f1b18b2-8877-4b43-b28f-570fd8f331b1")
                .storeGuid("storeGuid")
                .skuGuid("skuGuid")
                .sell(0)
                .build());

        // Run the test
        iEstimateSellLogServiceImplUnderTest.saveSellLog(sellLogList, "enterpriseGuid");

        // Verify the results
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
    }

    @Test
    public void testListByStoreGuidAndSkuGuidsLimit1() {
        // Setup
        final List<EstimateSellLogDO> expectedResult = Arrays.asList(EstimateSellLogDO.builder()
                .guid("2f1b18b2-8877-4b43-b28f-570fd8f331b1")
                .storeGuid("storeGuid")
                .skuGuid("skuGuid")
                .sell(0)
                .build());

        // Run the test
        final List<EstimateSellLogDO> result = iEstimateSellLogServiceImplUnderTest.listByStoreGuidAndSkuGuidsLimit1(
                "storeGuid", Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
