package com.holderzone.saas.store.item.controller;

import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.item.service.IDefaultDataService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

@RunWith(SpringRunner.class)
@WebMvcTest(DefaultDataController.class)
public class DefaultDataControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private IDefaultDataService mockDefaultDataService;

    @Test
    public void testAddDefaultData() throws Exception {
        // Setup
        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/default_data/add_data")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");

        // Confirm IDefaultDataService.addAttr(...).
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("data");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        itemSingleDTO.setRuleType(0);
        verify(mockDefaultDataService).addAttr(itemSingleDTO);
    }
}
