package com.holderzone.saas.store.item.controller;

import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.common.ItemStringListDTO;
import com.holderzone.saas.store.dto.item.req.TypeReqDTO;
import com.holderzone.saas.store.dto.item.req.TypeSortSwitchReqDTO;
import com.holderzone.saas.store.dto.item.resp.JournalingItemRespDTO;
import com.holderzone.saas.store.dto.item.resp.TypeWebRespDTO;
import com.holderzone.saas.store.item.entity.domain.TypeDO;
import com.holderzone.saas.store.item.helper.ItemHelper;
import com.holderzone.saas.store.item.service.IRetailItemService;
import com.holderzone.saas.store.item.service.ITypeService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

@RunWith(SpringRunner.class)
@WebMvcTest(TypeController.class)
public class TypeControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private ITypeService mockTypeService;
    @MockBean
    private ItemHelper mockItemHelper;
    @MockBean
    private IRetailItemService mockRetailItemService;

    @Test
    public void testInfo() throws Exception {
        // Setup
        // Configure ITypeService.getById(...).
        final TypeDO typeDO = new TypeDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "87218f7d-4633-4552-9aff-d8e4d39a12ec", "brandGuid", 0, 0,
                "description", "iconUrl", 0, 0, 0, "pricePlanGuid", 0, 1);
        when(mockTypeService.getById("typeGuid")).thenReturn(typeDO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/type/info")
                        .param("typeGuid", "typeGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testGetSort() throws Exception {
        // Setup
        // Configure ITypeService.getSort(...).
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("data");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        itemSingleDTO.setRuleType(0);
        when(mockTypeService.getSort(itemSingleDTO)).thenReturn(0);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/type/get_sort")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testSave() throws Exception {
        // Setup
        // Configure ITypeService.saveType(...).
        final TypeReqDTO typeReqDTO = new TypeReqDTO();
        typeReqDTO.setTypeGuid("typeGuid");
        typeReqDTO.setName("name");
        typeReqDTO.setSort(0);
        typeReqDTO.setIsDelete(0);
        typeReqDTO.setBrandGuid("brandGuid");
        when(mockTypeService.saveType(typeReqDTO)).thenReturn(0);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/type/save")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testUpdate() throws Exception {
        // Setup
        // Configure ITypeService.updateType(...).
        final TypeReqDTO typeReqDTO = new TypeReqDTO();
        typeReqDTO.setTypeGuid("typeGuid");
        typeReqDTO.setName("name");
        typeReqDTO.setSort(0);
        typeReqDTO.setIsDelete(0);
        typeReqDTO.setBrandGuid("brandGuid");
        when(mockTypeService.updateType(typeReqDTO)).thenReturn(0);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/type/update")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testSwitchSort() throws Exception {
        // Setup
        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/type/switch_sort")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
        verify(mockTypeService).switchSort(new TypeSortSwitchReqDTO("sourceTypeGuid", "targetTypeGuid"));
    }

    @Test
    public void testBatchModifySort() throws Exception {
        // Setup
        // Configure ITypeService.batchModifySort(...).
        final TypeReqDTO typeReqDTO = new TypeReqDTO();
        typeReqDTO.setTypeGuid("typeGuid");
        typeReqDTO.setName("name");
        typeReqDTO.setSort(0);
        typeReqDTO.setIsDelete(0);
        typeReqDTO.setBrandGuid("brandGuid");
        final List<TypeReqDTO> typeReqDTOList = Arrays.asList(typeReqDTO);
        when(mockTypeService.batchModifySort(typeReqDTOList)).thenReturn(0);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/type/batch_modify_sort")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testDelete() throws Exception {
        // Setup
        // Configure ITypeService.deleteType(...).
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("data");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        itemSingleDTO.setRuleType(0);
        when(mockTypeService.deleteType(itemSingleDTO)).thenReturn(0);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/type/delete")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testQuickSave() throws Exception {
        // Setup
        // Configure ITypeService.saveType(...).
        final TypeReqDTO typeReqDTO = new TypeReqDTO();
        typeReqDTO.setTypeGuid("typeGuid");
        typeReqDTO.setName("name");
        typeReqDTO.setSort(0);
        typeReqDTO.setIsDelete(0);
        typeReqDTO.setBrandGuid("brandGuid");
        when(mockTypeService.saveType(typeReqDTO)).thenReturn(0);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/type/quick_save")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testQueryType() throws Exception {
        // Setup
        // Configure ITypeService.queryType(...).
        final TypeWebRespDTO typeWebRespDTO = new TypeWebRespDTO();
        typeWebRespDTO.setTypeGuid("typeGuid");
        typeWebRespDTO.setName("name");
        typeWebRespDTO.setBrandGuid("brandGuid");
        typeWebRespDTO.setStoreGuid("storeGuid");
        typeWebRespDTO.setDescription("description");
        final List<TypeWebRespDTO> typeWebRespDTOS = Arrays.asList(typeWebRespDTO);
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("data");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        itemSingleDTO.setRuleType(0);
        when(mockTypeService.queryType(itemSingleDTO)).thenReturn(typeWebRespDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/type/query_type")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testQueryType_ITypeServiceReturnsNoItems() throws Exception {
        // Setup
        // Configure ITypeService.queryType(...).
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("data");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        itemSingleDTO.setRuleType(0);
        when(mockTypeService.queryType(itemSingleDTO)).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/type/query_type")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("[]");
    }

    @Test
    public void testQueryTypeByBrand() throws Exception {
        // Setup
        // Configure ITypeService.queryTypeByBrand(...).
        final TypeWebRespDTO typeWebRespDTO = new TypeWebRespDTO();
        typeWebRespDTO.setTypeGuid("typeGuid");
        typeWebRespDTO.setName("name");
        typeWebRespDTO.setBrandGuid("brandGuid");
        typeWebRespDTO.setStoreGuid("storeGuid");
        typeWebRespDTO.setDescription("description");
        final List<TypeWebRespDTO> typeWebRespDTOS = Arrays.asList(typeWebRespDTO);
        final ItemStringListDTO query = new ItemStringListDTO();
        query.setDataList(Arrays.asList("value"));
        query.setItemList(Arrays.asList("value"));
        query.setRecordId(0L);
        when(mockTypeService.queryTypeByBrand(query)).thenReturn(typeWebRespDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/type/query_type_by_brand")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testQueryTypeByBrand_ITypeServiceReturnsNoItems() throws Exception {
        // Setup
        // Configure ITypeService.queryTypeByBrand(...).
        final ItemStringListDTO query = new ItemStringListDTO();
        query.setDataList(Arrays.asList("value"));
        query.setItemList(Arrays.asList("value"));
        query.setRecordId(0L);
        when(mockTypeService.queryTypeByBrand(query)).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/type/query_type_by_brand")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("[]");
    }

    @Test
    public void testQueryTypeByStoreGuidList() throws Exception {
        // Setup
        // Configure ITypeService.queryTypeByStoreGuidList(...).
        final TypeWebRespDTO typeWebRespDTO = new TypeWebRespDTO();
        typeWebRespDTO.setTypeGuid("typeGuid");
        typeWebRespDTO.setName("name");
        typeWebRespDTO.setBrandGuid("brandGuid");
        typeWebRespDTO.setStoreGuid("storeGuid");
        typeWebRespDTO.setDescription("description");
        final List<TypeWebRespDTO> typeWebRespDTOS = Arrays.asList(typeWebRespDTO);
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        when(mockTypeService.queryTypeByStoreGuidList(itemStringListDTO)).thenReturn(typeWebRespDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/type/query_type_by_stores")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");

        // Confirm ItemHelper.validateItemStringListDTO(...).
        final ItemStringListDTO itemStringListDTO1 = new ItemStringListDTO();
        itemStringListDTO1.setDataList(Arrays.asList("value"));
        itemStringListDTO1.setItemList(Arrays.asList("value"));
        itemStringListDTO1.setRecordId(0L);
        verify(mockItemHelper).validateItemStringListDTO(itemStringListDTO1);
    }

    @Test
    public void testQueryTypeByStoreGuidList_ITypeServiceReturnsNoItems() throws Exception {
        // Setup
        // Configure ITypeService.queryTypeByStoreGuidList(...).
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        when(mockTypeService.queryTypeByStoreGuidList(itemStringListDTO)).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/type/query_type_by_stores")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("[]");

        // Confirm ItemHelper.validateItemStringListDTO(...).
        final ItemStringListDTO itemStringListDTO1 = new ItemStringListDTO();
        itemStringListDTO1.setDataList(Arrays.asList("value"));
        itemStringListDTO1.setItemList(Arrays.asList("value"));
        itemStringListDTO1.setRecordId(0L);
        verify(mockItemHelper).validateItemStringListDTO(itemStringListDTO1);
    }

    @Test
    public void testQueryJournalingItemType() throws Exception {
        // Setup
        // Configure ITypeService.queryJournalingItemType(...).
        final List<JournalingItemRespDTO> journalingItemRespDTOS = Arrays.asList(
                new JournalingItemRespDTO("dacc0f35-0490-4888-9a32-73695f04daf8", "name", Arrays.asList("value"), 0));
        when(mockTypeService.queryJournalingItemType()).thenReturn(journalingItemRespDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/type/query_journaling_item_type")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testQueryJournalingItemType_ITypeServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockTypeService.queryJournalingItemType()).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/type/query_journaling_item_type")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("[]");
    }

    @Test
    public void testVerifyThatitemExistsForType() throws Exception {
        // Setup
        when(mockRetailItemService.verifyThatitemExistsForType(
                new SingleDataDTO("data", Arrays.asList("value")))).thenReturn(false);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/type/verify_item_exists_for_type")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testSetGroupMealStatus() throws Exception {
        // Setup
        when(mockTypeService.setGroupMealStatus(new SingleDataDTO("data", Arrays.asList("value")))).thenReturn(false);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/type/set_group_meal_status")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testSelectGroupMealStatus() throws Exception {
        // Setup
        when(mockTypeService.selectGroupMealStatus("data")).thenReturn("result");

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/type/select_group_meal_status")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testQueryTypeByPlan() throws Exception {
        // Setup
        // Configure ITypeService.queryTypeByPlan(...).
        final TypeWebRespDTO typeWebRespDTO = new TypeWebRespDTO();
        typeWebRespDTO.setTypeGuid("typeGuid");
        typeWebRespDTO.setName("name");
        typeWebRespDTO.setBrandGuid("brandGuid");
        typeWebRespDTO.setStoreGuid("storeGuid");
        typeWebRespDTO.setDescription("description");
        final List<TypeWebRespDTO> typeWebRespDTOS = Arrays.asList(typeWebRespDTO);
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("data");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        itemSingleDTO.setRuleType(0);
        when(mockTypeService.queryTypeByPlan(itemSingleDTO)).thenReturn(typeWebRespDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/type/query_type_by_plan")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testQueryTypeByPlan_ITypeServiceReturnsNoItems() throws Exception {
        // Setup
        // Configure ITypeService.queryTypeByPlan(...).
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("data");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        itemSingleDTO.setRuleType(0);
        when(mockTypeService.queryTypeByPlan(itemSingleDTO)).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/type/query_type_by_plan")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("[]");
    }

    @Test
    public void testQuerySourceTypeInfo() throws Exception {
        // Setup
        // Configure ITypeService.querySourceTypeInfo(...).
        final TypeWebRespDTO typeWebRespDTO = new TypeWebRespDTO();
        typeWebRespDTO.setTypeGuid("typeGuid");
        typeWebRespDTO.setName("name");
        typeWebRespDTO.setBrandGuid("brandGuid");
        typeWebRespDTO.setStoreGuid("storeGuid");
        typeWebRespDTO.setDescription("description");
        final List<TypeWebRespDTO> typeWebRespDTOS = Arrays.asList(typeWebRespDTO);
        final ItemStringListDTO listDTO = new ItemStringListDTO();
        listDTO.setDataList(Arrays.asList("value"));
        listDTO.setItemList(Arrays.asList("value"));
        listDTO.setRecordId(0L);
        when(mockTypeService.querySourceTypeInfo(listDTO)).thenReturn(typeWebRespDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/type/query_source_type_info")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testQuerySourceTypeInfo_ITypeServiceReturnsNoItems() throws Exception {
        // Setup
        // Configure ITypeService.querySourceTypeInfo(...).
        final ItemStringListDTO listDTO = new ItemStringListDTO();
        listDTO.setDataList(Arrays.asList("value"));
        listDTO.setItemList(Arrays.asList("value"));
        listDTO.setRecordId(0L);
        when(mockTypeService.querySourceTypeInfo(listDTO)).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/type/query_source_type_info")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("[]");
    }
}
