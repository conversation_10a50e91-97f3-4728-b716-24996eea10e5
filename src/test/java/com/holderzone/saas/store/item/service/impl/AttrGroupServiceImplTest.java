package com.holderzone.saas.store.item.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.req.AttrGroupReqDTO;
import com.holderzone.saas.store.dto.item.req.AttrGroupUpdateReqDTO;
import com.holderzone.saas.store.dto.item.resp.*;
import com.holderzone.saas.store.item.entity.domain.*;
import com.holderzone.saas.store.item.helper.EventPushHelper;
import com.holderzone.saas.store.item.mapper.AttrGroupMapper;
import com.holderzone.saas.store.item.mapper.AttrMapper;
import com.holderzone.saas.store.item.service.*;
import com.holderzone.saas.store.item.util.DynamicHelper;
import com.holderzone.saas.store.item.util.PushUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.function.BiConsumer;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class AttrGroupServiceImplTest {

    @Mock
    private AttrMapper mockAttrMapper;
    @Mock
    private AttrGroupMapper mockAttrGroupMapper;
    @Mock
    private IAttrService mockAttrService;
    @Mock
    private DynamicHelper mockDynamicHelper;
    @Mock
    private IRTypeAttrService mockTypeAttrService;
    @Mock
    private ITypeService mockTypeService;
    @Mock
    private EventPushHelper mockEventPushHelper;
    @Mock
    private IRItemAttrGroupService mockItemAttrGroupService;
    @Mock
    private PushUtils mockPushUtils;
    @Mock
    private IItemService mockItemService;
    @Mock
    private IRAttrItemAttrGroupService mockAttrItemAttrGroupService;

    private AttrGroupServiceImpl attrGroupServiceImplUnderTest;

    @Before
    public void setUp() {
        attrGroupServiceImplUnderTest = new AttrGroupServiceImpl(mockAttrMapper, mockAttrGroupMapper, mockAttrService,
                mockDynamicHelper, mockTypeAttrService, mockTypeService, mockEventPushHelper, mockItemAttrGroupService,
                mockPushUtils, mockItemService, mockAttrItemAttrGroupService);
    }

    @Test
    public void testListAttrGroupByOrganization() {
        // Setup
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setFrom(0);
        itemSingleDTO.setData("brandGuid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);

        final AttrGroupAttrRespDTO attrGroupAttrRespDTO = new AttrGroupAttrRespDTO();
        attrGroupAttrRespDTO.setAttrGroupGuid("attrGroupGuid");
        final AttrRespDTO attrRespDTO = new AttrRespDTO();
        attrRespDTO.setAttrGroupGuid("attrGroupGuid");
        attrRespDTO.setAttrGuid("attrGuid");
        final TypeWebRespDTO typeWebRespDTO = new TypeWebRespDTO();
        typeWebRespDTO.setTypeGuid("typeGuid");
        typeWebRespDTO.setName("name");
        attrRespDTO.setTypeList(Arrays.asList(typeWebRespDTO));
        attrGroupAttrRespDTO.setAttrList(Arrays.asList(attrRespDTO));
        final List<AttrGroupAttrRespDTO> expectedResult = Arrays.asList(attrGroupAttrRespDTO);

        // Configure AttrMapper.selectList(...).
        final AttrDO attrDO = new AttrDO();
        attrDO.setStoreGuid("storeGuid");
        attrDO.setParentGuid("parentGuid");
        attrDO.setName("name");
        attrDO.setGuid("1b1a2f91-7494-46d8-8685-c089d0121a5d");
        attrDO.setAttrGroupGuid("attrGroupGuid");
        attrDO.setPrice(new BigDecimal("0.00"));
        attrDO.setIsDefault(0);
        final List<AttrDO> attrDOS = Arrays.asList(attrDO);
        when(mockAttrMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(attrDOS);

        // Configure IRTypeAttrService.list(...).
        final RTypeAttrDO rTypeAttrDO = new RTypeAttrDO();
        rTypeAttrDO.setId(0L);
        rTypeAttrDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        rTypeAttrDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        rTypeAttrDO.setTypeGuid("typeGuid");
        rTypeAttrDO.setAttrGuid("attrGuid");
        final List<RTypeAttrDO> rTypeAttrDOS = Arrays.asList(rTypeAttrDO);
        when(mockTypeAttrService.list(any(LambdaQueryWrapper.class))).thenReturn(rTypeAttrDOS);

        // Configure ITypeService.listByIds(...).
        final Collection<TypeDO> typeDOS = Arrays.asList(
                new TypeDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0,
                        "fd8b66be-7bc4-4e9d-b011-e17d84fe9488", "brandGuid", 0, 0, "description", "iconUrl", 0, 0, 0,
                        "pricePlanGuid", 0, 1));
        when(mockTypeService.listByIds(Arrays.asList("value"))).thenReturn(typeDOS);

        // Run the test
        final List<AttrGroupAttrRespDTO> result = attrGroupServiceImplUnderTest.listAttrGroupByOrganization(
                itemSingleDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testListAttrGroupByOrganization_AttrMapperReturnsNoItems() {
        // Setup
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setFrom(0);
        itemSingleDTO.setData("brandGuid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);

        when(mockAttrMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<AttrGroupAttrRespDTO> result = attrGroupServiceImplUnderTest.listAttrGroupByOrganization(
                itemSingleDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testListAttrGroupByOrganization_IRTypeAttrServiceReturnsNoItems() {
        // Setup
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setFrom(0);
        itemSingleDTO.setData("brandGuid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);

        // Configure AttrMapper.selectList(...).
        final AttrDO attrDO = new AttrDO();
        attrDO.setStoreGuid("storeGuid");
        attrDO.setParentGuid("parentGuid");
        attrDO.setName("name");
        attrDO.setGuid("1b1a2f91-7494-46d8-8685-c089d0121a5d");
        attrDO.setAttrGroupGuid("attrGroupGuid");
        attrDO.setPrice(new BigDecimal("0.00"));
        attrDO.setIsDefault(0);
        final List<AttrDO> attrDOS = Arrays.asList(attrDO);
        when(mockAttrMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(attrDOS);

        when(mockTypeAttrService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<AttrGroupAttrRespDTO> result = attrGroupServiceImplUnderTest.listAttrGroupByOrganization(
                itemSingleDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testListAttrGroupByOrganization_ITypeServiceReturnsNoItems() {
        // Setup
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setFrom(0);
        itemSingleDTO.setData("brandGuid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);

        final AttrGroupAttrRespDTO attrGroupAttrRespDTO = new AttrGroupAttrRespDTO();
        attrGroupAttrRespDTO.setAttrGroupGuid("attrGroupGuid");
        final AttrRespDTO attrRespDTO = new AttrRespDTO();
        attrRespDTO.setAttrGroupGuid("attrGroupGuid");
        attrRespDTO.setAttrGuid("attrGuid");
        final TypeWebRespDTO typeWebRespDTO = new TypeWebRespDTO();
        typeWebRespDTO.setTypeGuid("typeGuid");
        typeWebRespDTO.setName("name");
        attrRespDTO.setTypeList(Arrays.asList(typeWebRespDTO));
        attrGroupAttrRespDTO.setAttrList(Arrays.asList(attrRespDTO));
        final List<AttrGroupAttrRespDTO> expectedResult = Arrays.asList(attrGroupAttrRespDTO);

        // Configure AttrMapper.selectList(...).
        final AttrDO attrDO = new AttrDO();
        attrDO.setStoreGuid("storeGuid");
        attrDO.setParentGuid("parentGuid");
        attrDO.setName("name");
        attrDO.setGuid("1b1a2f91-7494-46d8-8685-c089d0121a5d");
        attrDO.setAttrGroupGuid("attrGroupGuid");
        attrDO.setPrice(new BigDecimal("0.00"));
        attrDO.setIsDefault(0);
        final List<AttrDO> attrDOS = Arrays.asList(attrDO);
        when(mockAttrMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(attrDOS);

        // Configure IRTypeAttrService.list(...).
        final RTypeAttrDO rTypeAttrDO = new RTypeAttrDO();
        rTypeAttrDO.setId(0L);
        rTypeAttrDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        rTypeAttrDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        rTypeAttrDO.setTypeGuid("typeGuid");
        rTypeAttrDO.setAttrGuid("attrGuid");
        final List<RTypeAttrDO> rTypeAttrDOS = Arrays.asList(rTypeAttrDO);
        when(mockTypeAttrService.list(any(LambdaQueryWrapper.class))).thenReturn(rTypeAttrDOS);

        when(mockTypeService.listByIds(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        final List<AttrGroupAttrRespDTO> result = attrGroupServiceImplUnderTest.listAttrGroupByOrganization(
                itemSingleDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testListAttrForSaveItem() {
        // Setup
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setFrom(0);
        itemSingleDTO.setData("brandGuid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);

        final AttrGroupAttrRespDTO attrGroupAttrRespDTO = new AttrGroupAttrRespDTO();
        attrGroupAttrRespDTO.setAttrGroupGuid("attrGroupGuid");
        final AttrRespDTO attrRespDTO = new AttrRespDTO();
        attrRespDTO.setAttrGroupGuid("attrGroupGuid");
        attrRespDTO.setAttrGuid("attrGuid");
        final TypeWebRespDTO typeWebRespDTO = new TypeWebRespDTO();
        typeWebRespDTO.setTypeGuid("typeGuid");
        typeWebRespDTO.setName("name");
        attrRespDTO.setTypeList(Arrays.asList(typeWebRespDTO));
        attrGroupAttrRespDTO.setAttrList(Arrays.asList(attrRespDTO));
        final List<AttrGroupAttrRespDTO> expectedResult = Arrays.asList(attrGroupAttrRespDTO);

        // Configure IAttrService.listAttrByGroup(...).
        final AttrRespDTO attrRespDTO1 = new AttrRespDTO();
        attrRespDTO1.setAttrGroupGuid("attrGroupGuid");
        attrRespDTO1.setAttrGuid("attrGuid");
        final TypeWebRespDTO typeWebRespDTO1 = new TypeWebRespDTO();
        typeWebRespDTO1.setTypeGuid("typeGuid");
        typeWebRespDTO1.setName("name");
        attrRespDTO1.setTypeList(Arrays.asList(typeWebRespDTO1));
        final List<AttrRespDTO> attrRespDTOS = Arrays.asList(attrRespDTO1);
        when(mockAttrService.listAttrByGroup(Arrays.asList("value"))).thenReturn(attrRespDTOS);

        // Run the test
        final List<AttrGroupAttrRespDTO> result = attrGroupServiceImplUnderTest.listAttrForSaveItem(itemSingleDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testListAttrForSaveItem_IAttrServiceReturnsNoItems() {
        // Setup
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setFrom(0);
        itemSingleDTO.setData("brandGuid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);

        when(mockAttrService.listAttrByGroup(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        final List<AttrGroupAttrRespDTO> result = attrGroupServiceImplUnderTest.listAttrForSaveItem(itemSingleDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testAddAttrGroup() {
        // Setup
        final AttrGroupReqDTO attrGroupReqDTO = new AttrGroupReqDTO();
        attrGroupReqDTO.setFrom(0);
        attrGroupReqDTO.setBrandGuid("brandGuid");
        attrGroupReqDTO.setStoreGuid("brandGuid");
        attrGroupReqDTO.setName("name");
        attrGroupReqDTO.setSort(0);

        when(mockDynamicHelper.generateGuid("hsi_attr_group")).thenReturn("66f2d530-112d-4945-9e13-78463338ddd0");

        // Run the test
        final boolean result = attrGroupServiceImplUnderTest.addAttrGroup(attrGroupReqDTO);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testGetLastSort() {
        // Setup
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setFrom(0);
        itemSingleDTO.setData("brandGuid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);

        // Run the test
        final int result = attrGroupServiceImplUnderTest.getLastSort(itemSingleDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    public void testRemovePushAttrGroup() {
        // Setup
        // Configure IRItemAttrGroupService.list(...).
        final RItemAttrGroupDO rItemAttrGroupDO = new RItemAttrGroupDO();
        rItemAttrGroupDO.setId(0L);
        rItemAttrGroupDO.setGuid("f00d1609-3fb5-4f42-ae82-91baa309b3bd");
        rItemAttrGroupDO.setItemGuid("itemGuid");
        rItemAttrGroupDO.setAttrGroupGuid("attrGroupGuid");
        rItemAttrGroupDO.setIsRequired(0);
        final List<RItemAttrGroupDO> rItemAttrGroupDOS = Arrays.asList(rItemAttrGroupDO);
        when(mockItemAttrGroupService.list(any(LambdaQueryWrapper.class))).thenReturn(rItemAttrGroupDOS);

        // Configure IAttrService.list(...).
        final AttrDO attrDO = new AttrDO();
        attrDO.setStoreGuid("storeGuid");
        attrDO.setParentGuid("parentGuid");
        attrDO.setName("name");
        attrDO.setGuid("1b1a2f91-7494-46d8-8685-c089d0121a5d");
        attrDO.setAttrGroupGuid("attrGroupGuid");
        attrDO.setPrice(new BigDecimal("0.00"));
        attrDO.setIsDefault(0);
        final List<AttrDO> attrDOS = Arrays.asList(attrDO);
        when(mockAttrService.list(any(LambdaQueryWrapper.class))).thenReturn(attrDOS);

        // Run the test
        final Integer result = attrGroupServiceImplUnderTest.removePushAttrGroup("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(0);

        // Confirm PushUtils.fixFieldsFromPush2SelfCreate(...).
        final AttrGroupDO attrGroupDO = new AttrGroupDO();
        attrGroupDO.setStoreGuid("storeGuid");
        attrGroupDO.setParentGuid("parentGuid");
        attrGroupDO.setName("name");
        attrGroupDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        attrGroupDO.setGuid("66f2d530-112d-4945-9e13-78463338ddd0");
        attrGroupDO.setBrandGuid("brandGuid");
        attrGroupDO.setSort(0);
        attrGroupDO.setIsEnable(0);
        attrGroupDO.setIconUrl("iconUrl");
        attrGroupDO.setIsRequired(0);
        attrGroupDO.setIsMultiChoice(0);
        attrGroupDO.setWithDefault(0);
        attrGroupDO.setAttrGroupFrom(0);
        final List<AttrGroupDO> withItemTypeDOList = Arrays.asList(attrGroupDO);
        verify(mockPushUtils).fixFieldsFromPush2SelfCreate(eq(withItemTypeDOList), any(BiConsumer.class),
                any(BiConsumer.class));
    }

    @Test
    public void testRemovePushAttrGroup_IRItemAttrGroupServiceReturnsNoItems() {
        // Setup
        when(mockItemAttrGroupService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure IAttrService.list(...).
        final AttrDO attrDO = new AttrDO();
        attrDO.setStoreGuid("storeGuid");
        attrDO.setParentGuid("parentGuid");
        attrDO.setName("name");
        attrDO.setGuid("1b1a2f91-7494-46d8-8685-c089d0121a5d");
        attrDO.setAttrGroupGuid("attrGroupGuid");
        attrDO.setPrice(new BigDecimal("0.00"));
        attrDO.setIsDefault(0);
        final List<AttrDO> attrDOS = Arrays.asList(attrDO);
        when(mockAttrService.list(any(LambdaQueryWrapper.class))).thenReturn(attrDOS);

        // Run the test
        final Integer result = attrGroupServiceImplUnderTest.removePushAttrGroup("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    public void testRemovePushAttrGroup_IAttrServiceReturnsNoItems() {
        // Setup
        // Configure IRItemAttrGroupService.list(...).
        final RItemAttrGroupDO rItemAttrGroupDO = new RItemAttrGroupDO();
        rItemAttrGroupDO.setId(0L);
        rItemAttrGroupDO.setGuid("f00d1609-3fb5-4f42-ae82-91baa309b3bd");
        rItemAttrGroupDO.setItemGuid("itemGuid");
        rItemAttrGroupDO.setAttrGroupGuid("attrGroupGuid");
        rItemAttrGroupDO.setIsRequired(0);
        final List<RItemAttrGroupDO> rItemAttrGroupDOS = Arrays.asList(rItemAttrGroupDO);
        when(mockItemAttrGroupService.list(any(LambdaQueryWrapper.class))).thenReturn(rItemAttrGroupDOS);

        when(mockAttrService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final Integer result = attrGroupServiceImplUnderTest.removePushAttrGroup("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    public void testSelectAttrGroupSynRespDtoByItemGuidList() {
        // Setup
        final AttrGroupSynRespDTO attrGroupSynRespDTO = new AttrGroupSynRespDTO();
        attrGroupSynRespDTO.setAttrGroupGuid("attrGroupGuid");
        attrGroupSynRespDTO.setName("name");
        attrGroupSynRespDTO.setIconUrl("iconUrl");
        attrGroupSynRespDTO.setShowPrice(0);
        final AttrSynRespDTO attrSynRespDTO = new AttrSynRespDTO();
        attrSynRespDTO.setAttrGuid("attrGuid");
        attrSynRespDTO.setName("name");
        attrSynRespDTO.setPrice(new BigDecimal("0.00"));
        attrGroupSynRespDTO.setAttrList(Arrays.asList(attrSynRespDTO));
        final List<AttrGroupSynRespDTO> expectedResult = Arrays.asList(attrGroupSynRespDTO);

        // Configure IRItemAttrGroupService.list(...).
        final RItemAttrGroupDO rItemAttrGroupDO = new RItemAttrGroupDO();
        rItemAttrGroupDO.setId(0L);
        rItemAttrGroupDO.setGuid("f00d1609-3fb5-4f42-ae82-91baa309b3bd");
        rItemAttrGroupDO.setItemGuid("itemGuid");
        rItemAttrGroupDO.setAttrGroupGuid("attrGroupGuid");
        rItemAttrGroupDO.setIsRequired(0);
        final List<RItemAttrGroupDO> rItemAttrGroupDOS = Arrays.asList(rItemAttrGroupDO);
        when(mockItemAttrGroupService.list(any(LambdaQueryWrapper.class))).thenReturn(rItemAttrGroupDOS);

        // Configure IRAttrItemAttrGroupService.list(...).
        final RAttrItemAttrGroupDO rAttrItemAttrGroupDO = new RAttrItemAttrGroupDO();
        rAttrItemAttrGroupDO.setId(0L);
        rAttrItemAttrGroupDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        rAttrItemAttrGroupDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        rAttrItemAttrGroupDO.setAttrGuid("attrGuid");
        rAttrItemAttrGroupDO.setItemAttrGroupGuid("itemAttrGroupGuid");
        final List<RAttrItemAttrGroupDO> rAttrItemAttrGroupDOS = Arrays.asList(rAttrItemAttrGroupDO);
        when(mockAttrItemAttrGroupService.list(any(LambdaQueryWrapper.class))).thenReturn(rAttrItemAttrGroupDOS);

        // Configure IAttrService.listByIds(...).
        final AttrDO attrDO = new AttrDO();
        attrDO.setStoreGuid("storeGuid");
        attrDO.setParentGuid("parentGuid");
        attrDO.setName("name");
        attrDO.setGuid("1b1a2f91-7494-46d8-8685-c089d0121a5d");
        attrDO.setAttrGroupGuid("attrGroupGuid");
        attrDO.setPrice(new BigDecimal("0.00"));
        attrDO.setIsDefault(0);
        final Collection<AttrDO> attrDOS = Arrays.asList(attrDO);
        when(mockAttrService.listByIds(Arrays.asList("value"))).thenReturn(attrDOS);

        // Run the test
        final List<AttrGroupSynRespDTO> result = attrGroupServiceImplUnderTest.selectAttrGroupSynRespDtoByItemGuidList(
                Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testSelectAttrGroupSynRespDtoByItemGuidList_IRItemAttrGroupServiceReturnsNoItems() {
        // Setup
        when(mockItemAttrGroupService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<AttrGroupSynRespDTO> result = attrGroupServiceImplUnderTest.selectAttrGroupSynRespDtoByItemGuidList(
                Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testSelectAttrGroupSynRespDtoByItemGuidList_IRAttrItemAttrGroupServiceReturnsNoItems() {
        // Setup
        // Configure IRItemAttrGroupService.list(...).
        final RItemAttrGroupDO rItemAttrGroupDO = new RItemAttrGroupDO();
        rItemAttrGroupDO.setId(0L);
        rItemAttrGroupDO.setGuid("f00d1609-3fb5-4f42-ae82-91baa309b3bd");
        rItemAttrGroupDO.setItemGuid("itemGuid");
        rItemAttrGroupDO.setAttrGroupGuid("attrGroupGuid");
        rItemAttrGroupDO.setIsRequired(0);
        final List<RItemAttrGroupDO> rItemAttrGroupDOS = Arrays.asList(rItemAttrGroupDO);
        when(mockItemAttrGroupService.list(any(LambdaQueryWrapper.class))).thenReturn(rItemAttrGroupDOS);

        when(mockAttrItemAttrGroupService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        assertThatThrownBy(() -> attrGroupServiceImplUnderTest.selectAttrGroupSynRespDtoByItemGuidList(
                Arrays.asList("value"))).isInstanceOf(BusinessException.class);
    }

    @Test
    public void testSelectAttrGroupSynRespDtoByItemGuidList_IAttrServiceReturnsNoItems() {
        // Setup
        final AttrGroupSynRespDTO attrGroupSynRespDTO = new AttrGroupSynRespDTO();
        attrGroupSynRespDTO.setAttrGroupGuid("attrGroupGuid");
        attrGroupSynRespDTO.setName("name");
        attrGroupSynRespDTO.setIconUrl("iconUrl");
        attrGroupSynRespDTO.setShowPrice(0);
        final AttrSynRespDTO attrSynRespDTO = new AttrSynRespDTO();
        attrSynRespDTO.setAttrGuid("attrGuid");
        attrSynRespDTO.setName("name");
        attrSynRespDTO.setPrice(new BigDecimal("0.00"));
        attrGroupSynRespDTO.setAttrList(Arrays.asList(attrSynRespDTO));
        final List<AttrGroupSynRespDTO> expectedResult = Arrays.asList(attrGroupSynRespDTO);

        // Configure IRItemAttrGroupService.list(...).
        final RItemAttrGroupDO rItemAttrGroupDO = new RItemAttrGroupDO();
        rItemAttrGroupDO.setId(0L);
        rItemAttrGroupDO.setGuid("f00d1609-3fb5-4f42-ae82-91baa309b3bd");
        rItemAttrGroupDO.setItemGuid("itemGuid");
        rItemAttrGroupDO.setAttrGroupGuid("attrGroupGuid");
        rItemAttrGroupDO.setIsRequired(0);
        final List<RItemAttrGroupDO> rItemAttrGroupDOS = Arrays.asList(rItemAttrGroupDO);
        when(mockItemAttrGroupService.list(any(LambdaQueryWrapper.class))).thenReturn(rItemAttrGroupDOS);

        // Configure IRAttrItemAttrGroupService.list(...).
        final RAttrItemAttrGroupDO rAttrItemAttrGroupDO = new RAttrItemAttrGroupDO();
        rAttrItemAttrGroupDO.setId(0L);
        rAttrItemAttrGroupDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        rAttrItemAttrGroupDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        rAttrItemAttrGroupDO.setAttrGuid("attrGuid");
        rAttrItemAttrGroupDO.setItemAttrGroupGuid("itemAttrGroupGuid");
        final List<RAttrItemAttrGroupDO> rAttrItemAttrGroupDOS = Arrays.asList(rAttrItemAttrGroupDO);
        when(mockAttrItemAttrGroupService.list(any(LambdaQueryWrapper.class))).thenReturn(rAttrItemAttrGroupDOS);

        when(mockAttrService.listByIds(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        final List<AttrGroupSynRespDTO> result = attrGroupServiceImplUnderTest.selectAttrGroupSynRespDtoByItemGuidList(
                Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testUpdateAttrGroup() {
        // Setup
        final AttrGroupUpdateReqDTO attrGroupUpdateReqDTO = new AttrGroupUpdateReqDTO();
        attrGroupUpdateReqDTO.setFrom(0);
        attrGroupUpdateReqDTO.setBrandGuid("brandGuid");
        attrGroupUpdateReqDTO.setStoreGuid("storeGuid");
        attrGroupUpdateReqDTO.setAttrGroupGuid("attrGroupGuid");
        attrGroupUpdateReqDTO.setName("name");
        attrGroupUpdateReqDTO.setIsMultiChoice(0);
        attrGroupUpdateReqDTO.setDefaultAttrGuidList(Arrays.asList("value"));

        // Configure IAttrService.list(...).
        final AttrDO attrDO = new AttrDO();
        attrDO.setStoreGuid("storeGuid");
        attrDO.setParentGuid("parentGuid");
        attrDO.setName("name");
        attrDO.setGuid("1b1a2f91-7494-46d8-8685-c089d0121a5d");
        attrDO.setAttrGroupGuid("attrGroupGuid");
        attrDO.setPrice(new BigDecimal("0.00"));
        attrDO.setIsDefault(0);
        final List<AttrDO> attrDOS = Arrays.asList(attrDO);
        when(mockAttrService.list(any(LambdaQueryWrapper.class))).thenReturn(attrDOS);

        // Run the test
        final boolean result = attrGroupServiceImplUnderTest.updateAttrGroup(attrGroupUpdateReqDTO);

        // Verify the results
        assertThat(result).isTrue();

        // Confirm AttrGroupMapper.update(...).
        final AttrGroupDO entity = new AttrGroupDO();
        entity.setStoreGuid("storeGuid");
        entity.setParentGuid("parentGuid");
        entity.setName("name");
        entity.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setGuid("66f2d530-112d-4945-9e13-78463338ddd0");
        entity.setBrandGuid("brandGuid");
        entity.setSort(0);
        entity.setIsEnable(0);
        entity.setIconUrl("iconUrl");
        entity.setIsRequired(0);
        entity.setIsMultiChoice(0);
        entity.setWithDefault(0);
        entity.setAttrGroupFrom(0);
        verify(mockAttrGroupMapper).update(eq(entity), any(LambdaQueryWrapper.class));

        // Confirm IAttrService.updateBatchById(...).
        final AttrDO attrDO1 = new AttrDO();
        attrDO1.setStoreGuid("storeGuid");
        attrDO1.setParentGuid("parentGuid");
        attrDO1.setName("name");
        attrDO1.setGuid("1b1a2f91-7494-46d8-8685-c089d0121a5d");
        attrDO1.setAttrGroupGuid("attrGroupGuid");
        attrDO1.setPrice(new BigDecimal("0.00"));
        attrDO1.setIsDefault(0);
        final List<AttrDO> entityList = Arrays.asList(attrDO1);
        verify(mockAttrService).updateBatchById(entityList, 0);
    }

    @Test
    public void testUpdateAttrGroup_IAttrServiceListReturnsNoItems() {
        // Setup
        final AttrGroupUpdateReqDTO attrGroupUpdateReqDTO = new AttrGroupUpdateReqDTO();
        attrGroupUpdateReqDTO.setFrom(0);
        attrGroupUpdateReqDTO.setBrandGuid("brandGuid");
        attrGroupUpdateReqDTO.setStoreGuid("storeGuid");
        attrGroupUpdateReqDTO.setAttrGroupGuid("attrGroupGuid");
        attrGroupUpdateReqDTO.setName("name");
        attrGroupUpdateReqDTO.setIsMultiChoice(0);
        attrGroupUpdateReqDTO.setDefaultAttrGuidList(Arrays.asList("value"));

        when(mockAttrService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final boolean result = attrGroupServiceImplUnderTest.updateAttrGroup(attrGroupUpdateReqDTO);

        // Verify the results
        assertThat(result).isTrue();

        // Confirm AttrGroupMapper.update(...).
        final AttrGroupDO entity = new AttrGroupDO();
        entity.setStoreGuid("storeGuid");
        entity.setParentGuid("parentGuid");
        entity.setName("name");
        entity.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setGuid("66f2d530-112d-4945-9e13-78463338ddd0");
        entity.setBrandGuid("brandGuid");
        entity.setSort(0);
        entity.setIsEnable(0);
        entity.setIconUrl("iconUrl");
        entity.setIsRequired(0);
        entity.setIsMultiChoice(0);
        entity.setWithDefault(0);
        entity.setAttrGroupFrom(0);
        verify(mockAttrGroupMapper).update(eq(entity), any(LambdaQueryWrapper.class));

        // Confirm IAttrService.updateBatchById(...).
        final AttrDO attrDO = new AttrDO();
        attrDO.setStoreGuid("storeGuid");
        attrDO.setParentGuid("parentGuid");
        attrDO.setName("name");
        attrDO.setGuid("1b1a2f91-7494-46d8-8685-c089d0121a5d");
        attrDO.setAttrGroupGuid("attrGroupGuid");
        attrDO.setPrice(new BigDecimal("0.00"));
        attrDO.setIsDefault(0);
        final List<AttrDO> entityList = Arrays.asList(attrDO);
        verify(mockAttrService).updateBatchById(entityList, 0);
    }

    @Test
    public void testDeleteByGuid() {
        // Setup
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setFrom(0);
        itemSingleDTO.setData("brandGuid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);

        when(mockAttrGroupMapper.delete(any(LambdaQueryWrapper.class))).thenReturn(0);

        // Configure IAttrService.list(...).
        final AttrDO attrDO = new AttrDO();
        attrDO.setStoreGuid("storeGuid");
        attrDO.setParentGuid("parentGuid");
        attrDO.setName("name");
        attrDO.setGuid("1b1a2f91-7494-46d8-8685-c089d0121a5d");
        attrDO.setAttrGroupGuid("attrGroupGuid");
        attrDO.setPrice(new BigDecimal("0.00"));
        attrDO.setIsDefault(0);
        final List<AttrDO> attrDOS = Arrays.asList(attrDO);
        when(mockAttrService.list(any(LambdaQueryWrapper.class))).thenReturn(attrDOS);

        // Configure IRAttrItemAttrGroupService.list(...).
        final RAttrItemAttrGroupDO rAttrItemAttrGroupDO = new RAttrItemAttrGroupDO();
        rAttrItemAttrGroupDO.setId(0L);
        rAttrItemAttrGroupDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        rAttrItemAttrGroupDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        rAttrItemAttrGroupDO.setAttrGuid("attrGuid");
        rAttrItemAttrGroupDO.setItemAttrGroupGuid("itemAttrGroupGuid");
        final List<RAttrItemAttrGroupDO> rAttrItemAttrGroupDOS = Arrays.asList(rAttrItemAttrGroupDO);
        when(mockAttrItemAttrGroupService.list(any(LambdaQueryWrapper.class))).thenReturn(rAttrItemAttrGroupDOS);

        // Configure IRItemAttrGroupService.list(...).
        final RItemAttrGroupDO rItemAttrGroupDO = new RItemAttrGroupDO();
        rItemAttrGroupDO.setId(0L);
        rItemAttrGroupDO.setGuid("f00d1609-3fb5-4f42-ae82-91baa309b3bd");
        rItemAttrGroupDO.setItemGuid("itemGuid");
        rItemAttrGroupDO.setAttrGroupGuid("attrGroupGuid");
        rItemAttrGroupDO.setIsRequired(0);
        final List<RItemAttrGroupDO> rItemAttrGroupDOS = Arrays.asList(rItemAttrGroupDO);
        when(mockItemAttrGroupService.list(any(LambdaQueryWrapper.class))).thenReturn(rItemAttrGroupDOS);

        // Run the test
        final boolean result = attrGroupServiceImplUnderTest.deleteByGuid(itemSingleDTO);

        // Verify the results
        assertThat(result).isFalse();
        verify(mockAttrService).deleteByGroup("brandGuid");
        verify(mockTypeAttrService).remove(any(LambdaQueryWrapper.class));

        // Confirm IAttrService.deletePushAttrRelate(...).
        final RAttrItemAttrGroupDO rAttrItemAttrGroupDO1 = new RAttrItemAttrGroupDO();
        rAttrItemAttrGroupDO1.setId(0L);
        rAttrItemAttrGroupDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        rAttrItemAttrGroupDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        rAttrItemAttrGroupDO1.setAttrGuid("attrGuid");
        rAttrItemAttrGroupDO1.setItemAttrGroupGuid("itemAttrGroupGuid");
        final List<RAttrItemAttrGroupDO> toDelAttrRelationDOList = Arrays.asList(rAttrItemAttrGroupDO1);
        final AttrDO attrDO1 = new AttrDO();
        attrDO1.setStoreGuid("storeGuid");
        attrDO1.setParentGuid("parentGuid");
        attrDO1.setName("name");
        attrDO1.setGuid("1b1a2f91-7494-46d8-8685-c089d0121a5d");
        attrDO1.setAttrGroupGuid("attrGroupGuid");
        attrDO1.setPrice(new BigDecimal("0.00"));
        attrDO1.setIsDefault(0);
        final List<AttrDO> pushAttrDOList = Arrays.asList(attrDO1);
        verify(mockAttrService).deletePushAttrRelate(toDelAttrRelationDOList, pushAttrDOList);

        // Confirm IAttrService.deleteOrUpdatePushAttrAfterDeletePushRelation(...).
        final AttrDO attrDO2 = new AttrDO();
        attrDO2.setStoreGuid("storeGuid");
        attrDO2.setParentGuid("parentGuid");
        attrDO2.setName("name");
        attrDO2.setGuid("1b1a2f91-7494-46d8-8685-c089d0121a5d");
        attrDO2.setAttrGroupGuid("attrGroupGuid");
        attrDO2.setPrice(new BigDecimal("0.00"));
        attrDO2.setIsDefault(0);
        final List<AttrDO> pushAttrDOList1 = Arrays.asList(attrDO2);
        verify(mockAttrService).deleteOrUpdatePushAttrAfterDeletePushRelation(pushAttrDOList1);

        // Confirm PushUtils.fixFieldsFromPush2SelfCreate(...).
        final AttrGroupDO attrGroupDO = new AttrGroupDO();
        attrGroupDO.setStoreGuid("storeGuid");
        attrGroupDO.setParentGuid("parentGuid");
        attrGroupDO.setName("name");
        attrGroupDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        attrGroupDO.setGuid("66f2d530-112d-4945-9e13-78463338ddd0");
        attrGroupDO.setBrandGuid("brandGuid");
        attrGroupDO.setSort(0);
        attrGroupDO.setIsEnable(0);
        attrGroupDO.setIconUrl("iconUrl");
        attrGroupDO.setIsRequired(0);
        attrGroupDO.setIsMultiChoice(0);
        attrGroupDO.setWithDefault(0);
        attrGroupDO.setAttrGroupFrom(0);
        final List<AttrGroupDO> withItemTypeDOList = Arrays.asList(attrGroupDO);
        verify(mockPushUtils).fixFieldsFromPush2SelfCreate(eq(withItemTypeDOList), any(BiConsumer.class),
                any(BiConsumer.class));
        verify(mockItemService).update(
                eq(new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0,
                        "7d2474be-e92d-490a-97e3-864139eb86f2", "typeGuid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0,
                        "pinyin", "nameAbbr", "description", "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code",
                        0, 0, 0, "videoUrls", 0, false)), any(LambdaQueryWrapper.class));
    }

    @Test
    public void testDeleteByGuid_IAttrServiceListReturnsNoItems() {
        // Setup
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setFrom(0);
        itemSingleDTO.setData("brandGuid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);

        when(mockAttrGroupMapper.delete(any(LambdaQueryWrapper.class))).thenReturn(0);
        when(mockAttrService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure IRItemAttrGroupService.list(...).
        final RItemAttrGroupDO rItemAttrGroupDO = new RItemAttrGroupDO();
        rItemAttrGroupDO.setId(0L);
        rItemAttrGroupDO.setGuid("f00d1609-3fb5-4f42-ae82-91baa309b3bd");
        rItemAttrGroupDO.setItemGuid("itemGuid");
        rItemAttrGroupDO.setAttrGroupGuid("attrGroupGuid");
        rItemAttrGroupDO.setIsRequired(0);
        final List<RItemAttrGroupDO> rItemAttrGroupDOS = Arrays.asList(rItemAttrGroupDO);
        when(mockItemAttrGroupService.list(any(LambdaQueryWrapper.class))).thenReturn(rItemAttrGroupDOS);

        // Run the test
        final boolean result = attrGroupServiceImplUnderTest.deleteByGuid(itemSingleDTO);

        // Verify the results
        assertThat(result).isFalse();

        // Confirm IAttrService.deleteOrUpdatePushAttrAfterDeletePushRelation(...).
        final AttrDO attrDO = new AttrDO();
        attrDO.setStoreGuid("storeGuid");
        attrDO.setParentGuid("parentGuid");
        attrDO.setName("name");
        attrDO.setGuid("1b1a2f91-7494-46d8-8685-c089d0121a5d");
        attrDO.setAttrGroupGuid("attrGroupGuid");
        attrDO.setPrice(new BigDecimal("0.00"));
        attrDO.setIsDefault(0);
        final List<AttrDO> pushAttrDOList = Arrays.asList(attrDO);
        verify(mockAttrService).deleteOrUpdatePushAttrAfterDeletePushRelation(pushAttrDOList);

        // Confirm PushUtils.fixFieldsFromPush2SelfCreate(...).
        final AttrGroupDO attrGroupDO = new AttrGroupDO();
        attrGroupDO.setStoreGuid("storeGuid");
        attrGroupDO.setParentGuid("parentGuid");
        attrGroupDO.setName("name");
        attrGroupDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        attrGroupDO.setGuid("66f2d530-112d-4945-9e13-78463338ddd0");
        attrGroupDO.setBrandGuid("brandGuid");
        attrGroupDO.setSort(0);
        attrGroupDO.setIsEnable(0);
        attrGroupDO.setIconUrl("iconUrl");
        attrGroupDO.setIsRequired(0);
        attrGroupDO.setIsMultiChoice(0);
        attrGroupDO.setWithDefault(0);
        attrGroupDO.setAttrGroupFrom(0);
        final List<AttrGroupDO> withItemTypeDOList = Arrays.asList(attrGroupDO);
        verify(mockPushUtils).fixFieldsFromPush2SelfCreate(eq(withItemTypeDOList), any(BiConsumer.class),
                any(BiConsumer.class));
        verify(mockItemService).update(
                eq(new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0,
                        "7d2474be-e92d-490a-97e3-864139eb86f2", "typeGuid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0,
                        "pinyin", "nameAbbr", "description", "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code",
                        0, 0, 0, "videoUrls", 0, false)), any(LambdaQueryWrapper.class));
    }

    @Test
    public void testDeleteByGuid_IRAttrItemAttrGroupServiceReturnsNoItems() {
        // Setup
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setFrom(0);
        itemSingleDTO.setData("brandGuid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);

        when(mockAttrGroupMapper.delete(any(LambdaQueryWrapper.class))).thenReturn(0);

        // Configure IAttrService.list(...).
        final AttrDO attrDO = new AttrDO();
        attrDO.setStoreGuid("storeGuid");
        attrDO.setParentGuid("parentGuid");
        attrDO.setName("name");
        attrDO.setGuid("1b1a2f91-7494-46d8-8685-c089d0121a5d");
        attrDO.setAttrGroupGuid("attrGroupGuid");
        attrDO.setPrice(new BigDecimal("0.00"));
        attrDO.setIsDefault(0);
        final List<AttrDO> attrDOS = Arrays.asList(attrDO);
        when(mockAttrService.list(any(LambdaQueryWrapper.class))).thenReturn(attrDOS);

        when(mockAttrItemAttrGroupService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure IRItemAttrGroupService.list(...).
        final RItemAttrGroupDO rItemAttrGroupDO = new RItemAttrGroupDO();
        rItemAttrGroupDO.setId(0L);
        rItemAttrGroupDO.setGuid("f00d1609-3fb5-4f42-ae82-91baa309b3bd");
        rItemAttrGroupDO.setItemGuid("itemGuid");
        rItemAttrGroupDO.setAttrGroupGuid("attrGroupGuid");
        rItemAttrGroupDO.setIsRequired(0);
        final List<RItemAttrGroupDO> rItemAttrGroupDOS = Arrays.asList(rItemAttrGroupDO);
        when(mockItemAttrGroupService.list(any(LambdaQueryWrapper.class))).thenReturn(rItemAttrGroupDOS);

        // Run the test
        final boolean result = attrGroupServiceImplUnderTest.deleteByGuid(itemSingleDTO);

        // Verify the results
        assertThat(result).isFalse();
        verify(mockAttrService).deleteByGroup("brandGuid");
        verify(mockTypeAttrService).remove(any(LambdaQueryWrapper.class));

        // Confirm IAttrService.deletePushAttrRelate(...).
        final RAttrItemAttrGroupDO rAttrItemAttrGroupDO = new RAttrItemAttrGroupDO();
        rAttrItemAttrGroupDO.setId(0L);
        rAttrItemAttrGroupDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        rAttrItemAttrGroupDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        rAttrItemAttrGroupDO.setAttrGuid("attrGuid");
        rAttrItemAttrGroupDO.setItemAttrGroupGuid("itemAttrGroupGuid");
        final List<RAttrItemAttrGroupDO> toDelAttrRelationDOList = Arrays.asList(rAttrItemAttrGroupDO);
        final AttrDO attrDO1 = new AttrDO();
        attrDO1.setStoreGuid("storeGuid");
        attrDO1.setParentGuid("parentGuid");
        attrDO1.setName("name");
        attrDO1.setGuid("1b1a2f91-7494-46d8-8685-c089d0121a5d");
        attrDO1.setAttrGroupGuid("attrGroupGuid");
        attrDO1.setPrice(new BigDecimal("0.00"));
        attrDO1.setIsDefault(0);
        final List<AttrDO> pushAttrDOList = Arrays.asList(attrDO1);
        verify(mockAttrService).deletePushAttrRelate(toDelAttrRelationDOList, pushAttrDOList);

        // Confirm IAttrService.deleteOrUpdatePushAttrAfterDeletePushRelation(...).
        final AttrDO attrDO2 = new AttrDO();
        attrDO2.setStoreGuid("storeGuid");
        attrDO2.setParentGuid("parentGuid");
        attrDO2.setName("name");
        attrDO2.setGuid("1b1a2f91-7494-46d8-8685-c089d0121a5d");
        attrDO2.setAttrGroupGuid("attrGroupGuid");
        attrDO2.setPrice(new BigDecimal("0.00"));
        attrDO2.setIsDefault(0);
        final List<AttrDO> pushAttrDOList1 = Arrays.asList(attrDO2);
        verify(mockAttrService).deleteOrUpdatePushAttrAfterDeletePushRelation(pushAttrDOList1);

        // Confirm PushUtils.fixFieldsFromPush2SelfCreate(...).
        final AttrGroupDO attrGroupDO = new AttrGroupDO();
        attrGroupDO.setStoreGuid("storeGuid");
        attrGroupDO.setParentGuid("parentGuid");
        attrGroupDO.setName("name");
        attrGroupDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        attrGroupDO.setGuid("66f2d530-112d-4945-9e13-78463338ddd0");
        attrGroupDO.setBrandGuid("brandGuid");
        attrGroupDO.setSort(0);
        attrGroupDO.setIsEnable(0);
        attrGroupDO.setIconUrl("iconUrl");
        attrGroupDO.setIsRequired(0);
        attrGroupDO.setIsMultiChoice(0);
        attrGroupDO.setWithDefault(0);
        attrGroupDO.setAttrGroupFrom(0);
        final List<AttrGroupDO> withItemTypeDOList = Arrays.asList(attrGroupDO);
        verify(mockPushUtils).fixFieldsFromPush2SelfCreate(eq(withItemTypeDOList), any(BiConsumer.class),
                any(BiConsumer.class));
        verify(mockItemService).update(
                eq(new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0,
                        "7d2474be-e92d-490a-97e3-864139eb86f2", "typeGuid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0,
                        "pinyin", "nameAbbr", "description", "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code",
                        0, 0, 0, "videoUrls", 0, false)), any(LambdaQueryWrapper.class));
    }

    @Test
    public void testDeleteByGuid_IRItemAttrGroupServiceReturnsNoItems() {
        // Setup
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setFrom(0);
        itemSingleDTO.setData("brandGuid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);

        when(mockAttrGroupMapper.delete(any(LambdaQueryWrapper.class))).thenReturn(0);

        // Configure IAttrService.list(...).
        final AttrDO attrDO = new AttrDO();
        attrDO.setStoreGuid("storeGuid");
        attrDO.setParentGuid("parentGuid");
        attrDO.setName("name");
        attrDO.setGuid("1b1a2f91-7494-46d8-8685-c089d0121a5d");
        attrDO.setAttrGroupGuid("attrGroupGuid");
        attrDO.setPrice(new BigDecimal("0.00"));
        attrDO.setIsDefault(0);
        final List<AttrDO> attrDOS = Arrays.asList(attrDO);
        when(mockAttrService.list(any(LambdaQueryWrapper.class))).thenReturn(attrDOS);

        // Configure IRAttrItemAttrGroupService.list(...).
        final RAttrItemAttrGroupDO rAttrItemAttrGroupDO = new RAttrItemAttrGroupDO();
        rAttrItemAttrGroupDO.setId(0L);
        rAttrItemAttrGroupDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        rAttrItemAttrGroupDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        rAttrItemAttrGroupDO.setAttrGuid("attrGuid");
        rAttrItemAttrGroupDO.setItemAttrGroupGuid("itemAttrGroupGuid");
        final List<RAttrItemAttrGroupDO> rAttrItemAttrGroupDOS = Arrays.asList(rAttrItemAttrGroupDO);
        when(mockAttrItemAttrGroupService.list(any(LambdaQueryWrapper.class))).thenReturn(rAttrItemAttrGroupDOS);

        when(mockItemAttrGroupService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final boolean result = attrGroupServiceImplUnderTest.deleteByGuid(itemSingleDTO);

        // Verify the results
        assertThat(result).isFalse();
        verify(mockAttrService).deleteByGroup("brandGuid");
        verify(mockTypeAttrService).remove(any(LambdaQueryWrapper.class));

        // Confirm IAttrService.deletePushAttrRelate(...).
        final RAttrItemAttrGroupDO rAttrItemAttrGroupDO1 = new RAttrItemAttrGroupDO();
        rAttrItemAttrGroupDO1.setId(0L);
        rAttrItemAttrGroupDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        rAttrItemAttrGroupDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        rAttrItemAttrGroupDO1.setAttrGuid("attrGuid");
        rAttrItemAttrGroupDO1.setItemAttrGroupGuid("itemAttrGroupGuid");
        final List<RAttrItemAttrGroupDO> toDelAttrRelationDOList = Arrays.asList(rAttrItemAttrGroupDO1);
        final AttrDO attrDO1 = new AttrDO();
        attrDO1.setStoreGuid("storeGuid");
        attrDO1.setParentGuid("parentGuid");
        attrDO1.setName("name");
        attrDO1.setGuid("1b1a2f91-7494-46d8-8685-c089d0121a5d");
        attrDO1.setAttrGroupGuid("attrGroupGuid");
        attrDO1.setPrice(new BigDecimal("0.00"));
        attrDO1.setIsDefault(0);
        final List<AttrDO> pushAttrDOList = Arrays.asList(attrDO1);
        verify(mockAttrService).deletePushAttrRelate(toDelAttrRelationDOList, pushAttrDOList);

        // Confirm IAttrService.deleteOrUpdatePushAttrAfterDeletePushRelation(...).
        final AttrDO attrDO2 = new AttrDO();
        attrDO2.setStoreGuid("storeGuid");
        attrDO2.setParentGuid("parentGuid");
        attrDO2.setName("name");
        attrDO2.setGuid("1b1a2f91-7494-46d8-8685-c089d0121a5d");
        attrDO2.setAttrGroupGuid("attrGroupGuid");
        attrDO2.setPrice(new BigDecimal("0.00"));
        attrDO2.setIsDefault(0);
        final List<AttrDO> pushAttrDOList1 = Arrays.asList(attrDO2);
        verify(mockAttrService).deleteOrUpdatePushAttrAfterDeletePushRelation(pushAttrDOList1);

        // Confirm PushUtils.fixFieldsFromPush2SelfCreate(...).
        final AttrGroupDO attrGroupDO = new AttrGroupDO();
        attrGroupDO.setStoreGuid("storeGuid");
        attrGroupDO.setParentGuid("parentGuid");
        attrGroupDO.setName("name");
        attrGroupDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        attrGroupDO.setGuid("66f2d530-112d-4945-9e13-78463338ddd0");
        attrGroupDO.setBrandGuid("brandGuid");
        attrGroupDO.setSort(0);
        attrGroupDO.setIsEnable(0);
        attrGroupDO.setIconUrl("iconUrl");
        attrGroupDO.setIsRequired(0);
        attrGroupDO.setIsMultiChoice(0);
        attrGroupDO.setWithDefault(0);
        attrGroupDO.setAttrGroupFrom(0);
        final List<AttrGroupDO> withItemTypeDOList = Arrays.asList(attrGroupDO);
        verify(mockPushUtils).fixFieldsFromPush2SelfCreate(eq(withItemTypeDOList), any(BiConsumer.class),
                any(BiConsumer.class));
        verify(mockItemService).update(
                eq(new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0,
                        "7d2474be-e92d-490a-97e3-864139eb86f2", "typeGuid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0,
                        "pinyin", "nameAbbr", "description", "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code",
                        0, 0, 0, "videoUrls", 0, false)), any(LambdaQueryWrapper.class));
    }
}
