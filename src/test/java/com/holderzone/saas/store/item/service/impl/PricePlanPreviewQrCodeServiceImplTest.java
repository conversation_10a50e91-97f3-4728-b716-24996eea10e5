

package com.holderzone.saas.store.item.service.impl;

import static org.mockito.MockitoAnnotations.initMocks;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import java.time.LocalDateTime;import java.util.concurrent.Callable;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.assertj.core.api.Assertions.within;
    import static org.mockito.ArgumentMatchers.any;
    import static org.mockito.ArgumentMatchers.anyInt;
    import static org.mockito.ArgumentMatchers.anyString;
    import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;import com.holderzone.saas.store.dto.item.req.CheckQrCodeDTO;import com.holderzone.saas.store.dto.item.resp.CheckQrCodeRespDTO;import com.holderzone.saas.store.item.entity.domain.PricePlanDO;import com.holderzone.saas.store.item.entity.domain.PricePlanPreviewQrCodeDO;import com.holderzone.saas.store.item.mapper.PricePlanMapper;import com.holderzone.saas.store.item.mapper.PricePlanPreviewQrCodeMapper;import org.mockito.junit.MockitoJUnitRunner;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.InjectMocks;
import org.mockito.stubbing.Answer;import org.springframework.data.redis.core.RedisTemplate;

@RunWith(MockitoJUnitRunner.class)
public class PricePlanPreviewQrCodeServiceImplTest {

            @Mock
        private PricePlanPreviewQrCodeMapper mockPlanPreviewQrCodeMapper;
            @Mock
        private PricePlanMapper mockPlanMapper;
            @Mock
        private RedisTemplate<String,String> mockRedisTemplate;

    private PricePlanPreviewQrCodeServiceImpl pricePlanPreviewQrCodeServiceImplUnderTest;

@Before
public void setUp() throws Exception {
            pricePlanPreviewQrCodeServiceImplUnderTest = new PricePlanPreviewQrCodeServiceImpl(mockPlanPreviewQrCodeMapper,mockPlanMapper,mockRedisTemplate) ;
}
                
    @Test
    public void testCheckQrCode() throws Exception {
    // Setup
                        final CheckQrCodeDTO reqDTO = new CheckQrCodeDTO();
                reqDTO.setPlanGuid("planGuid");
                reqDTO.setQrCodeGuid("qrCodeGuid");
                reqDTO.setEnterpriseGuid("enterpriseGuid");
 
         final CheckQrCodeRespDTO expectedResult = new CheckQrCodeRespDTO();
                expectedResult.setIsDeletePlan(false);
                expectedResult.setIsExpired(false);

            // Configure PricePlanMapper.selectOne(...).
        final PricePlanDO pricePlanDO = new PricePlanDO();
                pricePlanDO.setId(0L);
                pricePlanDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
                pricePlanDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
                pricePlanDO.setIsDelete(0);
                pricePlanDO.setGuid("1d6b6f91-c80f-49c4-9606-16d152172065");
            when( mockPlanMapper .selectOne(any(LambdaQueryWrapper.class))).thenReturn(pricePlanDO);

            // Configure PricePlanPreviewQrCodeMapper.selectOne(...).
        final PricePlanPreviewQrCodeDO pricePlanPreviewQrCodeDO = new PricePlanPreviewQrCodeDO();
                pricePlanPreviewQrCodeDO.setGuid("9841f50b-f43b-4051-afe2-29b85d8f776c");
                pricePlanPreviewQrCodeDO.setPlanGuid("planGuid");
                pricePlanPreviewQrCodeDO.setQrCodeGuid("qrCodeGuid");
                pricePlanPreviewQrCodeDO.setQrCodeTime("qrCodeTime");
                pricePlanPreviewQrCodeDO.setIsExpire(false);
            when( mockPlanPreviewQrCodeMapper .selectOne(any(LambdaQueryWrapper.class))).thenReturn(pricePlanPreviewQrCodeDO);

    // Run the test
 final CheckQrCodeRespDTO result =  pricePlanPreviewQrCodeServiceImplUnderTest.checkQrCode(reqDTO);

        // Verify the results
 assertThat(result).isEqualTo(expectedResult ) ;
    }
                                                                                        }

