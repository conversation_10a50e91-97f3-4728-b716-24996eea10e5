package com.holderzone.saas.store.item.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.req.DoubleDataPageDTO;
import com.holderzone.saas.store.dto.item.req.SingleDataPageDTO;
import com.holderzone.saas.store.dto.item.resp.ErpItemDTO;
import com.holderzone.saas.store.dto.item.resp.ErpTypeDTO;
import com.holderzone.saas.store.dto.item.resp.TypeWebRespDTO;
import com.holderzone.saas.store.item.entity.MPPage;
import com.holderzone.saas.store.item.entity.domain.ItemDO;
import com.holderzone.saas.store.item.entity.domain.SkuDO;
import com.holderzone.saas.store.item.service.IItemService;
import com.holderzone.saas.store.item.service.ISkuService;
import com.holderzone.saas.store.item.service.ITypeService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ErpServiceImplTest {

    @Mock
    private ITypeService mockTypeService;
    @Mock
    private IItemService mockItemService;
    @Mock
    private ISkuService mockSkuService;

    private ErpServiceImpl erpServiceImplUnderTest;

    @Before
    public void setUp() {
        erpServiceImplUnderTest = new ErpServiceImpl(mockTypeService, mockItemService, mockSkuService);
    }

    @Test
    public void testListType() {
        // Setup
        final List<ErpTypeDTO> expectedResult = Arrays.asList(
                new ErpTypeDTO("5c33fb7e-9b82-4ad6-9006-bc4cbcaba564", "name"));

        // Configure ITypeService.queryType(...).
        final TypeWebRespDTO typeWebRespDTO = new TypeWebRespDTO();
        typeWebRespDTO.setTypeGuid("5c33fb7e-9b82-4ad6-9006-bc4cbcaba564");
        typeWebRespDTO.setName("name");
        typeWebRespDTO.setBrandGuid("brandGuid");
        typeWebRespDTO.setStoreGuid("storeGuid");
        typeWebRespDTO.setDescription("description");
        final List<TypeWebRespDTO> typeWebRespDTOS = Arrays.asList(typeWebRespDTO);
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setFrom(0);
        itemSingleDTO.setData("storeGuid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        when(mockTypeService.queryType(itemSingleDTO)).thenReturn(typeWebRespDTOS);

        // Run the test
        final List<ErpTypeDTO> result = erpServiceImplUnderTest.listType("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testListType_ITypeServiceReturnsNoItems() {
        // Setup
        // Configure ITypeService.queryType(...).
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setFrom(0);
        itemSingleDTO.setData("storeGuid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        when(mockTypeService.queryType(itemSingleDTO)).thenReturn(Collections.emptyList());

        // Run the test
        final List<ErpTypeDTO> result = erpServiceImplUnderTest.listType("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testPageItem() {
        // Setup
        final SingleDataPageDTO singleDataPageDTO = new SingleDataPageDTO();
        singleDataPageDTO.setCurrentPage(0L);
        singleDataPageDTO.setPageSize(0L);
        singleDataPageDTO.setData("data");

        // Configure IItemService.list(...).
        final List<ItemDO> itemDOS = Arrays.asList(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0,
                        "dfcf5250-0025-46e4-8538-e8d64b36e3ac", "typeGuid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0,
                        "pinyin", "nameAbbr", "description", "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code",
                        0, 0, 0, "videoUrls", 0, false));
        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(itemDOS);

        when(mockSkuService.count(any(LambdaQueryWrapper.class))).thenReturn(0);
        when(mockItemService.page(eq(new MPPage<>(0L, 0L, 0L)), any(LambdaQueryWrapper.class))).thenReturn(null);

        // Configure ISkuService.list(...).
        final SkuDO skuDO = new SkuDO();
        skuDO.setId(0L);
        skuDO.setIsDelete(0);
        skuDO.setGuid("guid");
        skuDO.setItemGuid("42660dd9-526a-4c9f-ae58-704ed0d8df19");
        skuDO.setName("name");
        final List<SkuDO> skuDOS = Arrays.asList(skuDO);
        when(mockSkuService.list(any(LambdaQueryWrapper.class))).thenReturn(skuDOS);

        // Run the test
        final Page<ErpItemDTO> result = erpServiceImplUnderTest.pageItem(singleDataPageDTO);

        // Verify the results
    }

    @Test
    public void testPageItem_IItemServiceListReturnsNoItems() {
        // Setup
        final SingleDataPageDTO singleDataPageDTO = new SingleDataPageDTO();
        singleDataPageDTO.setCurrentPage(0L);
        singleDataPageDTO.setPageSize(0L);
        singleDataPageDTO.setData("data");

        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final Page<ErpItemDTO> result = erpServiceImplUnderTest.pageItem(singleDataPageDTO);

        // Verify the results
    }

    @Test
    public void testPageItem_ISkuServiceListReturnsNoItems() {
        // Setup
        final SingleDataPageDTO singleDataPageDTO = new SingleDataPageDTO();
        singleDataPageDTO.setCurrentPage(0L);
        singleDataPageDTO.setPageSize(0L);
        singleDataPageDTO.setData("data");

        // Configure IItemService.list(...).
        final List<ItemDO> itemDOS = Arrays.asList(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0,
                        "dfcf5250-0025-46e4-8538-e8d64b36e3ac", "typeGuid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0,
                        "pinyin", "nameAbbr", "description", "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code",
                        0, 0, 0, "videoUrls", 0, false));
        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(itemDOS);

        when(mockSkuService.count(any(LambdaQueryWrapper.class))).thenReturn(0);
        when(mockItemService.page(eq(new MPPage<>(0L, 0L, 0L)), any(LambdaQueryWrapper.class))).thenReturn(null);
        when(mockSkuService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final Page<ErpItemDTO> result = erpServiceImplUnderTest.pageItem(singleDataPageDTO);

        // Verify the results
    }

    @Test
    public void testPageItemOfType() {
        // Setup
        final SingleDataPageDTO singleDataPageDTO = new SingleDataPageDTO();
        singleDataPageDTO.setCurrentPage(0L);
        singleDataPageDTO.setPageSize(0L);
        singleDataPageDTO.setData("data");

        // Configure IItemService.list(...).
        final List<ItemDO> itemDOS = Arrays.asList(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0,
                        "dfcf5250-0025-46e4-8538-e8d64b36e3ac", "typeGuid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0,
                        "pinyin", "nameAbbr", "description", "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code",
                        0, 0, 0, "videoUrls", 0, false));
        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(itemDOS);

        when(mockSkuService.count(any(LambdaQueryWrapper.class))).thenReturn(0);
        when(mockItemService.page(eq(new MPPage<>(0L, 0L, 0L)), any(LambdaQueryWrapper.class))).thenReturn(null);

        // Configure ISkuService.list(...).
        final SkuDO skuDO = new SkuDO();
        skuDO.setId(0L);
        skuDO.setIsDelete(0);
        skuDO.setGuid("guid");
        skuDO.setItemGuid("42660dd9-526a-4c9f-ae58-704ed0d8df19");
        skuDO.setName("name");
        final List<SkuDO> skuDOS = Arrays.asList(skuDO);
        when(mockSkuService.list(any(LambdaQueryWrapper.class))).thenReturn(skuDOS);

        // Run the test
        final Page<ErpItemDTO> result = erpServiceImplUnderTest.pageItemOfType(singleDataPageDTO);

        // Verify the results
    }

    @Test
    public void testPageItemOfType_IItemServiceListReturnsNoItems() {
        // Setup
        final SingleDataPageDTO singleDataPageDTO = new SingleDataPageDTO();
        singleDataPageDTO.setCurrentPage(0L);
        singleDataPageDTO.setPageSize(0L);
        singleDataPageDTO.setData("data");

        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final Page<ErpItemDTO> result = erpServiceImplUnderTest.pageItemOfType(singleDataPageDTO);

        // Verify the results
    }

    @Test
    public void testPageItemOfType_ISkuServiceListReturnsNoItems() {
        // Setup
        final SingleDataPageDTO singleDataPageDTO = new SingleDataPageDTO();
        singleDataPageDTO.setCurrentPage(0L);
        singleDataPageDTO.setPageSize(0L);
        singleDataPageDTO.setData("data");

        // Configure IItemService.list(...).
        final List<ItemDO> itemDOS = Arrays.asList(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0,
                        "dfcf5250-0025-46e4-8538-e8d64b36e3ac", "typeGuid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0,
                        "pinyin", "nameAbbr", "description", "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code",
                        0, 0, 0, "videoUrls", 0, false));
        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(itemDOS);

        when(mockSkuService.count(any(LambdaQueryWrapper.class))).thenReturn(0);
        when(mockItemService.page(eq(new MPPage<>(0L, 0L, 0L)), any(LambdaQueryWrapper.class))).thenReturn(null);
        when(mockSkuService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final Page<ErpItemDTO> result = erpServiceImplUnderTest.pageItemOfType(singleDataPageDTO);

        // Verify the results
    }

    @Test
    public void testPageItemByName() {
        // Setup
        final DoubleDataPageDTO doubleDataPageDTO = new DoubleDataPageDTO();
        doubleDataPageDTO.setCurrentPage(0L);
        doubleDataPageDTO.setPageSize(0L);
        doubleDataPageDTO.setData1("data1");
        doubleDataPageDTO.setData2("data2");

        // Configure IItemService.list(...).
        final List<ItemDO> itemDOS = Arrays.asList(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0,
                        "dfcf5250-0025-46e4-8538-e8d64b36e3ac", "typeGuid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0,
                        "pinyin", "nameAbbr", "description", "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code",
                        0, 0, 0, "videoUrls", 0, false));
        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(itemDOS);

        when(mockSkuService.count(any(LambdaQueryWrapper.class))).thenReturn(0);
        when(mockItemService.page(eq(new MPPage<>(0L, 0L, 0L)), any(LambdaQueryWrapper.class))).thenReturn(null);

        // Configure ISkuService.list(...).
        final SkuDO skuDO = new SkuDO();
        skuDO.setId(0L);
        skuDO.setIsDelete(0);
        skuDO.setGuid("guid");
        skuDO.setItemGuid("42660dd9-526a-4c9f-ae58-704ed0d8df19");
        skuDO.setName("name");
        final List<SkuDO> skuDOS = Arrays.asList(skuDO);
        when(mockSkuService.list(any(LambdaQueryWrapper.class))).thenReturn(skuDOS);

        // Run the test
        final Page<ErpItemDTO> result = erpServiceImplUnderTest.pageItemByName(doubleDataPageDTO);

        // Verify the results
    }

    @Test
    public void testPageItemByName_IItemServiceListReturnsNoItems() {
        // Setup
        final DoubleDataPageDTO doubleDataPageDTO = new DoubleDataPageDTO();
        doubleDataPageDTO.setCurrentPage(0L);
        doubleDataPageDTO.setPageSize(0L);
        doubleDataPageDTO.setData1("data1");
        doubleDataPageDTO.setData2("data2");

        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final Page<ErpItemDTO> result = erpServiceImplUnderTest.pageItemByName(doubleDataPageDTO);

        // Verify the results
    }

    @Test
    public void testPageItemByName_ISkuServiceListReturnsNoItems() {
        // Setup
        final DoubleDataPageDTO doubleDataPageDTO = new DoubleDataPageDTO();
        doubleDataPageDTO.setCurrentPage(0L);
        doubleDataPageDTO.setPageSize(0L);
        doubleDataPageDTO.setData1("data1");
        doubleDataPageDTO.setData2("data2");

        // Configure IItemService.list(...).
        final List<ItemDO> itemDOS = Arrays.asList(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0,
                        "dfcf5250-0025-46e4-8538-e8d64b36e3ac", "typeGuid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0,
                        "pinyin", "nameAbbr", "description", "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code",
                        0, 0, 0, "videoUrls", 0, false));
        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(itemDOS);

        when(mockSkuService.count(any(LambdaQueryWrapper.class))).thenReturn(0);
        when(mockItemService.page(eq(new MPPage<>(0L, 0L, 0L)), any(LambdaQueryWrapper.class))).thenReturn(null);
        when(mockSkuService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final Page<ErpItemDTO> result = erpServiceImplUnderTest.pageItemByName(doubleDataPageDTO);

        // Verify the results
    }
}
