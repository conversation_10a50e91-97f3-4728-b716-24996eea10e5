package com.holderzone.saas.store.item.controller;

import com.alibaba.fastjson.JSON;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.common.ItemStringListDTO;
import com.holderzone.saas.store.item.HolderSaasStoreItemApplication;
import com.holderzone.saas.store.item.utils.JsonFileUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.List;

import static com.holderzone.saas.store.dto.common.CommonConstant.USER_INFO;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * <AUTHOR>
 * @description 商品控制器测试类
 * @date 2022/5/10 15:43
 * @className: ItemControllerTest
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = HolderSaasStoreItemApplication.class)
@WebAppConfiguration
@ContextConfiguration
@AutoConfigureMockMvc
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class ItemControllerTest {

    private static final String ITEM = "/item";

    private static final String USERINFO = "{\"operSubjectGuid\": \"2010121440477930009\"," +
            "\"enterpriseGuid\":" + " \"2009281531195930006\"," +
            "\"enterpriseName\": \"赵氏企业\"," +
            "\"enterpriseNo\": \"********\"," +
            "\"storeGuid\":" + " \"2106221850429620006\"," +
            "\"storeName\": \"交子大道测试门店\"," +
            "\"storeNo\": \"5796807\"," +
            "\"userGuid\": \"6787561298847596545\"," +
            "\"account\": \"196504\"," +
            "\"tel\": \"***********\"," +
            "\"name\": \"靓亮仔\"}\n";

    private static final String RESPONSE = "response:";

    @Autowired
    private WebApplicationContext wac;

    private MockMvc mockMvc;

    @Before
    public void setUp() {
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();
    }

    @Test
    public void queryErpItem() throws Exception {
        ItemSingleDTO query = new ItemSingleDTO();
        query.setData("6619160595813892096");
        String jsonString = JSON.toJSONString(query);
        MvcResult mvcResult = mockMvc.perform(post(ITEM + "/query_erp_item")
                        .header(USER_INFO, USERINFO)
                        .accept(MediaType.APPLICATION_JSON_VALUE)
                        .contentType(MediaType.APPLICATION_JSON).content(jsonString))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    @Test
    public void listTypeAndItemByBrand() throws Exception {
        ItemSingleDTO query = new ItemSingleDTO();
        query.setData("6788282061309345792");
        String jsonString = JSON.toJSONString(query);
        MvcResult mvcResult = mockMvc.perform(post(ITEM + "/list_type_and_item_by_brand")
                        .header(USER_INFO, USERINFO)
                        .accept(MediaType.APPLICATION_JSON_VALUE)
                        .contentType(MediaType.APPLICATION_JSON).content(jsonString))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    @Test
    public void getItemNameListEmpty() throws UnsupportedEncodingException {
        ItemStringListDTO query = new ItemStringListDTO();
        query.setDataList(new ArrayList<>());
        String getItemNameListEmptyJsonString = JSON.toJSONString(query);
        MvcResult getItemNameListEmptyResult = null;
        try {
            getItemNameListEmptyResult = mockMvc.perform(post(ITEM + "/get_item_name_list")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(getItemNameListEmptyJsonString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        String contentAsString = getItemNameListEmptyResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    @Test
    public void getItemNameList() throws UnsupportedEncodingException {
        ItemStringListDTO query = new ItemStringListDTO();
        List<String> dataList = new ArrayList<>();
        dataList.add("7156572253239377920");
        dataList.add("7107580966939394048");
        dataList.add("7107580966964559872");
        dataList.add("7107580966968754176");
        query.setDataList(dataList);
        String getItemNameListJsonString = JSON.toJSONString(query);
        MvcResult getItemNameListResult = null;
        try {
            getItemNameListResult = mockMvc.perform(post(ITEM + "/get_item_name_list")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(getItemNameListJsonString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        String contentAsString = getItemNameListResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    @Test
    public void queryParentItemGuidBySku() throws UnsupportedEncodingException {
        ItemStringListDTO queryParentItemGuidBySkuReqDTO = JSON.parseObject(JsonFileUtil.read("item/queryParentItemGuidBySku.json"),
                ItemStringListDTO.class);
        testQueryParentItemGuidBySku(queryParentItemGuidBySkuReqDTO);
    }

    @Test
    public void queryParentItemGuidBySkuEmpty() throws UnsupportedEncodingException {
        ItemStringListDTO queryParentItemGuidBySkuReqDTO = JSON.parseObject(JsonFileUtil.read("item/queryParentItemGuidBySkuEmpty.json"),
                ItemStringListDTO.class);
        testQueryParentItemGuidBySku(queryParentItemGuidBySkuReqDTO);
    }

    @Test
    public void queryParentItemGuidBySkuError() throws UnsupportedEncodingException {
        ItemStringListDTO queryParentItemGuidBySkuReqDTO = JSON.parseObject(JsonFileUtil.read("item/queryParentItemGuidBySkuError.json"),
                ItemStringListDTO.class);
        testQueryParentItemGuidBySku(queryParentItemGuidBySkuReqDTO);
    }

    private void testQueryParentItemGuidBySku(ItemStringListDTO queryParentItemGuidBySkuReqDTO) throws UnsupportedEncodingException {
        String queryParentItemGuidBySkuJsonString = JSON.toJSONString(queryParentItemGuidBySkuReqDTO);
        MvcResult queryParentItemGuidBySkuResult = null;
        try {
            queryParentItemGuidBySkuResult = mockMvc.perform(post(ITEM + "/query_parent_item_guid_by_sku")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(queryParentItemGuidBySkuJsonString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        String contentAsString = queryParentItemGuidBySkuResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    @Test
    public void queryParentItemGuidByItem() throws UnsupportedEncodingException {
        ItemStringListDTO queryParentItemGuidByItemReqDTO = JSON.parseObject(JsonFileUtil.read("item/queryParentItemGuidByItem.json"),
                ItemStringListDTO.class);
        testQueryParentItemGuidByItem(queryParentItemGuidByItemReqDTO);
    }

    @Test
    public void queryParentItemGuidByItemEmpty() throws UnsupportedEncodingException {
        ItemStringListDTO queryParentItemGuidByItemReqDTO = JSON.parseObject(JsonFileUtil.read("item/queryParentItemGuidByItemEmpty.json"),
                ItemStringListDTO.class);
        testQueryParentItemGuidByItem(queryParentItemGuidByItemReqDTO);
    }

    @Test
    public void queryParentItemGuidByItemError() throws UnsupportedEncodingException {
        ItemStringListDTO queryParentItemGuidByItemReqDTO = JSON.parseObject(JsonFileUtil.read("item/queryParentItemGuidByItemError.json"),
                ItemStringListDTO.class);
        testQueryParentItemGuidByItem(queryParentItemGuidByItemReqDTO);
    }

    private void testQueryParentItemGuidByItem(ItemStringListDTO queryParentItemGuidByItemReqDTO) throws UnsupportedEncodingException {
        String queryParentItemGuidByItemJsonString = JSON.toJSONString(queryParentItemGuidByItemReqDTO);
        MvcResult queryParentItemGuidByItemResult = null;
        try {
            queryParentItemGuidByItemResult = mockMvc.perform(post(ITEM + "/query_parent_item_guid_by_item")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(queryParentItemGuidByItemJsonString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        String contentAsString = queryParentItemGuidByItemResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }
}