package com.holderzone.saas.store.item.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.common.ItemStringListDTO;
import com.holderzone.saas.store.dto.item.req.ItemSortReqDTO;
import com.holderzone.saas.store.dto.item.req.TypeReqDTO;
import com.holderzone.saas.store.dto.item.req.TypeSortReqDTO;
import com.holderzone.saas.store.dto.item.req.TypeSortSwitchReqDTO;
import com.holderzone.saas.store.dto.item.resp.JournalingItemRespDTO;
import com.holderzone.saas.store.dto.item.resp.TypeWebRespDTO;
import com.holderzone.saas.store.item.entity.domain.ItemDO;
import com.holderzone.saas.store.item.entity.domain.TypeDO;
import com.holderzone.saas.store.item.entity.query.JournalingItemsQuery;
import com.holderzone.saas.store.item.helper.EventPushHelper;
import com.holderzone.saas.store.item.mapper.ItemMapper;
import com.holderzone.saas.store.item.mapper.TypeMapper;
import com.holderzone.saas.store.item.service.IItemService;
import com.holderzone.saas.store.item.service.IRTypeAttrService;
import com.holderzone.saas.store.item.service.rpc.OrganizationService;
import com.holderzone.saas.store.item.util.DynamicHelper;
import com.holderzone.saas.store.item.util.PushUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.RedisTemplate;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.function.BiConsumer;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class TypeServiceImplTest {

    @Mock
    private TypeMapper mockTypeMapper;
    @Mock
    private DynamicHelper mockDynamicHelper;
    @Mock
    private IItemService mockItemService;
    @Mock
    private ItemMapper mockItemMapper;
    @Mock
    private IRTypeAttrService mockTypeAttrService;
    @Mock
    private EventPushHelper mockEventPushHelper;
    @Mock
    private PushUtils mockPushUtils;
    @Mock
    private RedisTemplate mockRedisTemplate;

    private TypeServiceImpl typeServiceImplUnderTest;
    private OrganizationService organizationServiceTest;

    @Before
    public void setUp() throws Exception {
        typeServiceImplUnderTest = new TypeServiceImpl(mockTypeMapper, mockDynamicHelper, mockItemService,
                mockItemMapper, mockTypeAttrService, mockEventPushHelper, mockPushUtils, mockRedisTemplate,
                organizationServiceTest);
    }

    @Test
    public void testGetSort() {
        // Setup
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setFrom(0);
        itemSingleDTO.setData("guid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);

        when(mockTypeMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(0);

        // Run the test
        final Integer result = typeServiceImplUnderTest.getSort(itemSingleDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    public void testSaveType() {
        // Setup
        final TypeReqDTO typeReqDTO = new TypeReqDTO();
        typeReqDTO.setFrom(0);
        typeReqDTO.setTypeGuid("typeGuid");
        typeReqDTO.setName("name");
        typeReqDTO.setSort(0);
        typeReqDTO.setBrandGuid("brandGuid");
        typeReqDTO.setStoreGuid("storeGuid");
        typeReqDTO.setDescription("description");
        typeReqDTO.setIsEnable(0);
        typeReqDTO.setIconUrl("iconUrl");
        typeReqDTO.setPricePlanGuid("pricePlanGuid");
        typeReqDTO.setParentGuid("parentGuid");
        typeReqDTO.setMenuClassifyPictureType(0);

        when(mockTypeMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(0);
        when(mockTypeMapper.insert(
                new TypeDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "brandGuid", 0, 0, "description", "iconUrl", 0, 0, 0, "pricePlanGuid", 0, 1))).thenReturn(0);

        // Run the test
        final Integer result = typeServiceImplUnderTest.saveType(typeReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    public void testUpdateType() {
        // Setup
        final TypeReqDTO typeReqDTO = new TypeReqDTO();
        typeReqDTO.setFrom(0);
        typeReqDTO.setTypeGuid("typeGuid");
        typeReqDTO.setName("name");
        typeReqDTO.setSort(0);
        typeReqDTO.setBrandGuid("brandGuid");
        typeReqDTO.setStoreGuid("storeGuid");
        typeReqDTO.setDescription("description");
        typeReqDTO.setIsEnable(0);
        typeReqDTO.setIconUrl("iconUrl");
        typeReqDTO.setPricePlanGuid("pricePlanGuid");
        typeReqDTO.setParentGuid("parentGuid");
        typeReqDTO.setMenuClassifyPictureType(0);

        when(mockTypeMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(0);
        when(mockItemService.count(any(LambdaQueryWrapper.class))).thenReturn(0);
        when(mockTypeAttrService.count(any(LambdaQueryWrapper.class))).thenReturn(0);

        // Configure IItemService.list(...).
        final List<ItemDO> itemDOS = Arrays.asList(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0,
                        "9dcfeed6-0eee-4e89-99fa-307ea289e8ce", "typeGuid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0,
                        "pinyin", "nameAbbr", "description", "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code",
                        0, 0, 0, "videoUrls", 0, false));
        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(itemDOS);

        // Run the test
        final Integer result = typeServiceImplUnderTest.updateType(typeReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
        verify(mockPushUtils).fixFieldsFromPush2SelfCreate(eq(Arrays.asList(
                        new TypeDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                                "brandGuid", 0, 0, "description", "iconUrl", 0, 0, 0, "pricePlanGuid", 0, 1))),
                any(BiConsumer.class), any(BiConsumer.class));
        verify(mockTypeAttrService).remove(any(LambdaQueryWrapper.class));
    }

    @Test
    public void testUpdateType_IItemServiceListReturnsNoItems() {
        // Setup
        final TypeReqDTO typeReqDTO = new TypeReqDTO();
        typeReqDTO.setFrom(0);
        typeReqDTO.setTypeGuid("typeGuid");
        typeReqDTO.setName("name");
        typeReqDTO.setSort(0);
        typeReqDTO.setBrandGuid("brandGuid");
        typeReqDTO.setStoreGuid("storeGuid");
        typeReqDTO.setDescription("description");
        typeReqDTO.setIsEnable(0);
        typeReqDTO.setIconUrl("iconUrl");
        typeReqDTO.setPricePlanGuid("pricePlanGuid");
        typeReqDTO.setParentGuid("parentGuid");
        typeReqDTO.setMenuClassifyPictureType(0);

        when(mockTypeMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(0);
        when(mockItemService.count(any(LambdaQueryWrapper.class))).thenReturn(0);
        when(mockTypeAttrService.count(any(LambdaQueryWrapper.class))).thenReturn(0);
        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final Integer result = typeServiceImplUnderTest.updateType(typeReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
        verify(mockTypeAttrService).remove(any(LambdaQueryWrapper.class));
    }

    @Test
    public void testQueryType() {
        // Setup
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setFrom(0);
        itemSingleDTO.setData("guid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);

        final TypeWebRespDTO typeWebRespDTO = new TypeWebRespDTO();
        typeWebRespDTO.setTypeGuid("typeGuid");
        typeWebRespDTO.setSort(0);
        typeWebRespDTO.setItemNum(0);
        typeWebRespDTO.setRackItemNum(0);
        typeWebRespDTO.setUnRackItemNum(0);
        typeWebRespDTO.setTypeFrom(0);
        final List<TypeWebRespDTO> expectedResult = Arrays.asList(typeWebRespDTO);

        // Configure IItemService.list(...).
        final List<ItemDO> itemDOS = Arrays.asList(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0,
                        "9dcfeed6-0eee-4e89-99fa-307ea289e8ce", "typeGuid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0,
                        "pinyin", "nameAbbr", "description", "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code",
                        0, 0, 0, "videoUrls", 0, false));
        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(itemDOS);

        // Configure TypeMapper.selectList(...).
        final List<TypeDO> typeDOS = Arrays.asList(
                new TypeDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "brandGuid", 0, 0, "description", "iconUrl", 0, 0, 0, "pricePlanGuid", 0, 1));
        when(mockTypeMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(typeDOS);

        // Configure ItemMapper.listRackItemByType(...).
        final List<ItemDO> itemDOS1 = Arrays.asList(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0,
                        "9dcfeed6-0eee-4e89-99fa-307ea289e8ce", "typeGuid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0,
                        "pinyin", "nameAbbr", "description", "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code",
                        0, 0, 0, "videoUrls", 0, false));
        when(mockItemMapper.listRackItemByType(Arrays.asList("value"), 1)).thenReturn(itemDOS1);

        // Run the test
        final List<TypeWebRespDTO> result = typeServiceImplUnderTest.queryType(itemSingleDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryType_IItemServiceReturnsNoItems() {
        // Setup
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setFrom(0);
        itemSingleDTO.setData("guid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);

        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure TypeMapper.selectList(...).
        final List<TypeDO> typeDOS = Arrays.asList(
                new TypeDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "brandGuid", 0, 0, "description", "iconUrl", 0, 0, 0, "pricePlanGuid", 0, 1));
        when(mockTypeMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(typeDOS);

        // Configure ItemMapper.listRackItemByType(...).
        final List<ItemDO> itemDOS = Arrays.asList(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0,
                        "9dcfeed6-0eee-4e89-99fa-307ea289e8ce", "typeGuid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0,
                        "pinyin", "nameAbbr", "description", "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code",
                        0, 0, 0, "videoUrls", 0, false));
        when(mockItemMapper.listRackItemByType(Arrays.asList("value"), 1)).thenReturn(itemDOS);

        // Run the test
        final List<TypeWebRespDTO> result = typeServiceImplUnderTest.queryType(itemSingleDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testQueryType_TypeMapperReturnsNoItems() {
        // Setup
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setFrom(0);
        itemSingleDTO.setData("guid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);

        final TypeWebRespDTO typeWebRespDTO = new TypeWebRespDTO();
        typeWebRespDTO.setTypeGuid("typeGuid");
        typeWebRespDTO.setSort(0);
        typeWebRespDTO.setItemNum(0);
        typeWebRespDTO.setRackItemNum(0);
        typeWebRespDTO.setUnRackItemNum(0);
        typeWebRespDTO.setTypeFrom(0);
        final List<TypeWebRespDTO> expectedResult = Arrays.asList(typeWebRespDTO);

        // Configure IItemService.list(...).
        final List<ItemDO> itemDOS = Arrays.asList(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0,
                        "9dcfeed6-0eee-4e89-99fa-307ea289e8ce", "typeGuid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0,
                        "pinyin", "nameAbbr", "description", "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code",
                        0, 0, 0, "videoUrls", 0, false));
        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(itemDOS);

        when(mockTypeMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure ItemMapper.listRackItemByType(...).
        final List<ItemDO> itemDOS1 = Arrays.asList(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0,
                        "9dcfeed6-0eee-4e89-99fa-307ea289e8ce", "typeGuid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0,
                        "pinyin", "nameAbbr", "description", "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code",
                        0, 0, 0, "videoUrls", 0, false));
        when(mockItemMapper.listRackItemByType(Arrays.asList("value"), 1)).thenReturn(itemDOS1);

        // Run the test
        final List<TypeWebRespDTO> result = typeServiceImplUnderTest.queryType(itemSingleDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryType_ItemMapperReturnsNoItems() {
        // Setup
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setFrom(0);
        itemSingleDTO.setData("guid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);

        final TypeWebRespDTO typeWebRespDTO = new TypeWebRespDTO();
        typeWebRespDTO.setTypeGuid("typeGuid");
        typeWebRespDTO.setSort(0);
        typeWebRespDTO.setItemNum(0);
        typeWebRespDTO.setRackItemNum(0);
        typeWebRespDTO.setUnRackItemNum(0);
        typeWebRespDTO.setTypeFrom(0);
        final List<TypeWebRespDTO> expectedResult = Arrays.asList(typeWebRespDTO);

        // Configure IItemService.list(...).
        final List<ItemDO> itemDOS = Arrays.asList(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0,
                        "9dcfeed6-0eee-4e89-99fa-307ea289e8ce", "typeGuid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0,
                        "pinyin", "nameAbbr", "description", "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code",
                        0, 0, 0, "videoUrls", 0, false));
        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(itemDOS);

        // Configure TypeMapper.selectList(...).
        final List<TypeDO> typeDOS = Arrays.asList(
                new TypeDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "brandGuid", 0, 0, "description", "iconUrl", 0, 0, 0, "pricePlanGuid", 0, 1));
        when(mockTypeMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(typeDOS);

        when(mockItemMapper.listRackItemByType(Arrays.asList("value"), 1)).thenReturn(Collections.emptyList());

        // Run the test
        final List<TypeWebRespDTO> result = typeServiceImplUnderTest.queryType(itemSingleDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryTypeByStoreGuidList() {
        // Setup
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setStoreGuid("storeGuid");
        itemStringListDTO.setFrom(0);
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);

        final TypeWebRespDTO typeWebRespDTO = new TypeWebRespDTO();
        typeWebRespDTO.setTypeGuid("typeGuid");
        typeWebRespDTO.setSort(0);
        typeWebRespDTO.setItemNum(0);
        typeWebRespDTO.setRackItemNum(0);
        typeWebRespDTO.setUnRackItemNum(0);
        typeWebRespDTO.setTypeFrom(0);
        final List<TypeWebRespDTO> expectedResult = Arrays.asList(typeWebRespDTO);

        // Run the test
        final List<TypeWebRespDTO> result = typeServiceImplUnderTest.queryTypeByStoreGuidList(itemStringListDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testDeleteType() {
        // Setup
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setFrom(0);
        itemSingleDTO.setData("guid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);

        when(mockItemService.count(any(LambdaQueryWrapper.class))).thenReturn(0);
        when(mockTypeAttrService.count(any(LambdaQueryWrapper.class))).thenReturn(0);

        // Configure IItemService.list(...).
        final List<ItemDO> itemDOS = Arrays.asList(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0,
                        "9dcfeed6-0eee-4e89-99fa-307ea289e8ce", "typeGuid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0,
                        "pinyin", "nameAbbr", "description", "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code",
                        0, 0, 0, "videoUrls", 0, false));
        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(itemDOS);

        // Run the test
        final Integer result = typeServiceImplUnderTest.deleteType(itemSingleDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
        verify(mockPushUtils).fixFieldsFromPush2SelfCreate(eq(Arrays.asList(
                        new TypeDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                                "brandGuid", 0, 0, "description", "iconUrl", 0, 0, 0, "pricePlanGuid", 0,1))),
                any(BiConsumer.class), any(BiConsumer.class));
        verify(mockTypeAttrService).remove(any(LambdaQueryWrapper.class));
    }

    @Test
    public void testDeleteType_IItemServiceListReturnsNoItems() {
        // Setup
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setFrom(0);
        itemSingleDTO.setData("guid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);

        when(mockItemService.count(any(LambdaQueryWrapper.class))).thenReturn(0);
        when(mockTypeAttrService.count(any(LambdaQueryWrapper.class))).thenReturn(0);
        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final Integer result = typeServiceImplUnderTest.deleteType(itemSingleDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
        verify(mockTypeAttrService).remove(any(LambdaQueryWrapper.class));
    }

    @Test
    public void testRemovePushType() {
        // Setup
        // Configure IItemService.list(...).
        final List<ItemDO> itemDOS = Arrays.asList(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0,
                        "9dcfeed6-0eee-4e89-99fa-307ea289e8ce", "typeGuid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0,
                        "pinyin", "nameAbbr", "description", "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code",
                        0, 0, 0, "videoUrls", 0, false));
        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(itemDOS);

        // Run the test
        final Integer result = typeServiceImplUnderTest.removePushType("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(0);
        verify(mockPushUtils).fixFieldsFromPush2SelfCreate(eq(Arrays.asList(
                        new TypeDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                                "brandGuid", 0, 0, "description", "iconUrl", 0, 0, 0, "pricePlanGuid", 0,1))),
                any(BiConsumer.class), any(BiConsumer.class));
        verify(mockTypeAttrService).remove(any(LambdaQueryWrapper.class));
    }

    @Test
    public void testRemovePushType_IItemServiceReturnsNoItems() {
        // Setup
        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final Integer result = typeServiceImplUnderTest.removePushType("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(0);
        verify(mockTypeAttrService).remove(any(LambdaQueryWrapper.class));
    }

    @Test
    public void testQueryJournalingItemType() {
        // Setup
        final List<JournalingItemRespDTO> expectedResult = Arrays.asList(
                new JournalingItemRespDTO("a3c2b69a-8465-4912-b543-de64e6d9d1e0", "name", Arrays.asList("value"), 0));

        // Configure TypeMapper.queryJournalingItemType(...).
        final List<JournalingItemsQuery> journalingItemsQueries = Arrays.asList(JournalingItemsQuery.builder()
                .guid("6785554f-fce6-4b71-898b-b837f5765e46")
                .name("name")
                .parentGuid("parentGuid")
                .itemFrom(0)
                .build());
        when(mockTypeMapper.queryJournalingItemType()).thenReturn(journalingItemsQueries);

        // Run the test
        final List<JournalingItemRespDTO> result = typeServiceImplUnderTest.queryJournalingItemType();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryJournalingItemType_TypeMapperReturnsNoItems() {
        // Setup
        when(mockTypeMapper.queryJournalingItemType()).thenReturn(Collections.emptyList());

        // Run the test
        final List<JournalingItemRespDTO> result = typeServiceImplUnderTest.queryJournalingItemType();

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testRetailUpdateTypesort() {
        // Setup
        final List<TypeSortReqDTO> typeList = Arrays.asList(
                new TypeSortReqDTO("35cb37ff-cab4-4474-b288-01a4e19d1256", "name", 0,
                        Arrays.asList(new ItemSortReqDTO(0, "609443b6-30e7-4ebc-9107-5bab54ac0964", "name", 0)),
                        Arrays.asList("value")));

        // Run the test
        final Boolean result = typeServiceImplUnderTest.retailUpdateTypesort(typeList);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testMdmRemoveType() {
        typeServiceImplUnderTest.mdmRemoveType(new SingleDataDTO("data", Arrays.asList("value")));
    }

    @Test
    public void testSetGroupMealStatus() {
        // Setup
        final SingleDataDTO request = new SingleDataDTO("data", Arrays.asList("value"));

        // Run the test
        final Boolean result = typeServiceImplUnderTest.setGroupMealStatus(request);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testSelectGroupMealStatus() {
        assertThat(typeServiceImplUnderTest.selectGroupMealStatus("storeGuid")).isEqualTo("isEnable");
    }

    @Test
    public void testSwitchSort() {
        // Setup
        final TypeSortSwitchReqDTO typeReqDTO = new TypeSortSwitchReqDTO("sourceTypeGuid", "targetTypeGuid");

        // Run the test
        final Boolean result = typeServiceImplUnderTest.switchSort(typeReqDTO);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testBatchModifySort() {
        // Setup
        final TypeReqDTO typeReqDTO = new TypeReqDTO();
        typeReqDTO.setFrom(0);
        typeReqDTO.setTypeGuid("typeGuid");
        typeReqDTO.setName("name");
        typeReqDTO.setSort(0);
        typeReqDTO.setBrandGuid("brandGuid");
        typeReqDTO.setStoreGuid("storeGuid");
        typeReqDTO.setDescription("description");
        typeReqDTO.setIsEnable(0);
        typeReqDTO.setIconUrl("iconUrl");
        typeReqDTO.setPricePlanGuid("pricePlanGuid");
        typeReqDTO.setParentGuid("parentGuid");
        typeReqDTO.setMenuClassifyPictureType(0);
        final List<TypeReqDTO> typeReqDTOList = Arrays.asList(typeReqDTO);

        // Run the test
        final Integer result = typeServiceImplUnderTest.batchModifySort(typeReqDTOList);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    public void testQueryTypeByPlan() {
        // Setup
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setFrom(0);
        itemSingleDTO.setData("guid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);

        final TypeWebRespDTO typeWebRespDTO = new TypeWebRespDTO();
        typeWebRespDTO.setTypeGuid("typeGuid");
        typeWebRespDTO.setSort(0);
        typeWebRespDTO.setItemNum(0);
        typeWebRespDTO.setRackItemNum(0);
        typeWebRespDTO.setUnRackItemNum(0);
        typeWebRespDTO.setTypeFrom(0);
        final List<TypeWebRespDTO> expectedResult = Arrays.asList(typeWebRespDTO);

        // Run the test
        final List<TypeWebRespDTO> result = typeServiceImplUnderTest.queryTypeByPlan(itemSingleDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testSaveTypePlan() {
        // Setup
        final TypeReqDTO typeReqDTO = new TypeReqDTO();
        typeReqDTO.setFrom(0);
        typeReqDTO.setTypeGuid("typeGuid");
        typeReqDTO.setName("name");
        typeReqDTO.setSort(0);
        typeReqDTO.setBrandGuid("brandGuid");
        typeReqDTO.setStoreGuid("storeGuid");
        typeReqDTO.setDescription("description");
        typeReqDTO.setIsEnable(0);
        typeReqDTO.setIconUrl("iconUrl");
        typeReqDTO.setPricePlanGuid("pricePlanGuid");
        typeReqDTO.setParentGuid("parentGuid");
        typeReqDTO.setMenuClassifyPictureType(0);

        when(mockTypeMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(0);

        // Run the test
        final String result = typeServiceImplUnderTest.saveTypePlan(typeReqDTO);

        // Verify the results
        assertThat(result).isEqualTo("guid");
        verify(mockTypeMapper).insert(
                new TypeDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "brandGuid", 0, 0, "description", "iconUrl", 0, 0, 0, "pricePlanGuid", 0, 1));
    }

    @Test
    public void testUpdateTypePlan() {
        // Setup
        // Run the test
        typeServiceImplUnderTest.updateTypePlan("planGuid", "parentGuid");

        // Verify the results
    }

    @Test
    public void testQuerySourceTypeInfo() {
        // Setup
        final ItemStringListDTO listDTO = new ItemStringListDTO();
        listDTO.setStoreGuid("storeGuid");
        listDTO.setFrom(0);
        listDTO.setDataList(Arrays.asList("value"));
        listDTO.setItemList(Arrays.asList("value"));
        listDTO.setRecordId(0L);

        final TypeWebRespDTO typeWebRespDTO = new TypeWebRespDTO();
        typeWebRespDTO.setTypeGuid("typeGuid");
        typeWebRespDTO.setSort(0);
        typeWebRespDTO.setItemNum(0);
        typeWebRespDTO.setRackItemNum(0);
        typeWebRespDTO.setUnRackItemNum(0);
        typeWebRespDTO.setTypeFrom(0);
        final List<TypeWebRespDTO> expectedResult = Arrays.asList(typeWebRespDTO);

        // Run the test
        final List<TypeWebRespDTO> result = typeServiceImplUnderTest.querySourceTypeInfo(listDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryTypeByBrand() {
        // Setup
        final ItemStringListDTO query = new ItemStringListDTO();
        query.setStoreGuid("storeGuid");
        query.setFrom(0);
        query.setDataList(Arrays.asList("value"));
        query.setItemList(Arrays.asList("value"));
        query.setRecordId(0L);

        final TypeWebRespDTO typeWebRespDTO = new TypeWebRespDTO();
        typeWebRespDTO.setTypeGuid("typeGuid");
        typeWebRespDTO.setSort(0);
        typeWebRespDTO.setItemNum(0);
        typeWebRespDTO.setRackItemNum(0);
        typeWebRespDTO.setUnRackItemNum(0);
        typeWebRespDTO.setTypeFrom(0);
        final List<TypeWebRespDTO> expectedResult = Arrays.asList(typeWebRespDTO);

        // Run the test
        final List<TypeWebRespDTO> result = typeServiceImplUnderTest.queryTypeByBrand(query);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
