package com.holderzone.saas.store.item.controller;

import com.alibaba.fastjson.JSON;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.item.req.estimate.EstimateForAndroidReqDTO;
import com.holderzone.saas.store.dto.item.req.estimate.EstimateSkuReqDTO;
import com.holderzone.saas.store.item.HolderSaasStoreItemApplication;
import com.holderzone.saas.store.item.utils.MockHttpUtil;
import org.junit.Before;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.context.WebApplicationContext;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static com.holderzone.saas.store.dto.common.CommonConstant.USER_INFO;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * <AUTHOR>
 * @description 估清测试类
 * @date 2022/4/12 16:36
 * @className: EstimateControllerTest
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = HolderSaasStoreItemApplication.class)
@WebAppConfiguration
@ContextConfiguration
@AutoConfigureMockMvc
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class EstimateControllerTest {

    private static final String ESTIMATE = "/estimate";

    private static final String userInfo = "{\"operSubjectGuid\": \"2008141005594470007\",\"enterpriseGuid\":" +
            " \"6506431195651982337\",\"enterpriseName\": \"企业0227\",\"enterpriseNo\": \"********\",\"storeGuid\":" +
            " \"6619160595813892096\",\"storeName\": \"听雨阁\",\"storeNo\": \"6148139\",\"userGuid\": " +
            "\"6653489337230950401\",\"account\": \"200003\",\"tel\": \"***********\",\"name\": \"赵亮\"}\n";

    @Autowired
    private WebApplicationContext wac;

    private MockMvc mockMvc;

    @Before
    public void setUp() {
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();
    }

    @Test
    public void saveSoldOut() throws Exception {
        EstimateForAndroidReqDTO save = new EstimateForAndroidReqDTO();
        save.setItemGuid("6856868884511719424");
        List<EstimateSkuReqDTO> skuReqList = new ArrayList<>();
        EstimateSkuReqDTO reqDTO = new EstimateSkuReqDTO();
        reqDTO.setSkuGuid("685686888451171942401");
        reqDTO.setLimitQuantity(new BigDecimal("10"));
        reqDTO.setIsForeverEstimate(2);
        skuReqList.add(reqDTO);
        save.setSkuReqList(skuReqList);
        save.setStoreGuid("6619160595813892096");
        String jsonString = JSON.toJSONString(save);
        MvcResult mvcResult = mockMvc.perform(post(ESTIMATE + "/save_sold_out")
                .header(USER_INFO, userInfo)
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(jsonString))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("response:" + contentAsString);
    }

    @Test
    public void listEstimate() {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("storeGuid", "6619160595813892096");
        String content = MockHttpUtil.get(ESTIMATE + "/list_estimate", params, mockMvc);
        System.out.println(content);
    }

    @Test
    public void batchCancelEstimate() throws Exception {
        SingleDataDTO cancel = new SingleDataDTO();
        ArrayList<String> list = new ArrayList<>();
        list.add("6856868884511719424");
        cancel.setDatas(list);
        cancel.setStoreGuid("6619160595813892096");
        String jsonString = JSON.toJSONString(cancel);
        MvcResult mvcResult = mockMvc.perform(post(ESTIMATE + "/batch_cancel_estimate")
                .header(USER_INFO, userInfo)
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(jsonString))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("response:" + contentAsString);
    }

    @Test
    public void batchStopSell() throws Exception {
        SingleDataDTO cancel = new SingleDataDTO();
        ArrayList<String> list = new ArrayList<>();
        list.add("6856868884511719424");
        cancel.setDatas(list);
        cancel.setStoreGuid("6619160595813892096");
        String jsonString = JSON.toJSONString(cancel);
        MvcResult mvcResult = mockMvc.perform(post(ESTIMATE + "/batch_stop_sell")
                .header(USER_INFO, userInfo)
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(jsonString))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("response:" + contentAsString);
    }

    @Test
    public void listEstimateByItem() throws Exception {
        SingleDataDTO query = new SingleDataDTO();
        query.setData("6774508199513948160");
        String jsonString = JSON.toJSONString(query);
        MvcResult mvcResult = mockMvc.perform(post(ESTIMATE + "/list_estimate_by_item")
                .header(USER_INFO, userInfo)
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(jsonString))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("response:" + contentAsString);
    }
}