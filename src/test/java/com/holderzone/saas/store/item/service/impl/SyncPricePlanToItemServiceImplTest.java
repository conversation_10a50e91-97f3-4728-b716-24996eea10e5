package com.holderzone.saas.store.item.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.common.ItemStringListDTO;
import com.holderzone.saas.store.dto.item.req.*;
import com.holderzone.saas.store.dto.item.resp.*;
import com.holderzone.saas.store.item.dto.PushRecordStatusUpdateReqDTO;
import com.holderzone.saas.store.item.dto.SyncItemDTO;
import com.holderzone.saas.store.item.dto.UpdateTerminalStatusDTO;
import com.holderzone.saas.store.item.entity.domain.RSkuSubgroupDO;
import com.holderzone.saas.store.item.entity.domain.SubgroupDO;
import com.holderzone.saas.store.item.feign.ItemClientService;
import com.holderzone.saas.store.item.helper.ItemHelper;
import com.holderzone.saas.store.item.service.*;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class SyncPricePlanToItemServiceImplTest {

    @Mock
    private IItemService mockIItemService;
    @Mock
    private IItemPkgService mockItemPkgService;
    @Mock
    private IPricePlanItemService mockIPricePlanItemService;
    @Mock
    private IPricePlanPushRecordService mockIPricePlanPushRecordService;
    @Mock
    private ItemHelper mockItemHelper;
    @Mock
    private ItemClientService mockItemClientService;
    @Mock
    private ISkuService mockISkuService;
    @Mock
    private ISubgroupService mockISubgroupService;
    @Mock
    private IRSkuSubgroupService mockIrSkuSubgroupService;
    @Mock
    private ICommonService mockICommonService;
    @Mock
    private IRedissonCacheService mockIRedissonCacheService;

    @InjectMocks
    private SyncPricePlanToItemServiceImpl syncPricePlanToItemServiceImplUnderTest;

    @Test
    public void testAddPricePlanItem() {
        // Setup
        final SyncItemDTO itemDTO = new SyncItemDTO();
        itemDTO.setItemGuid("parentGuid");
        itemDTO.setSkuGuid("skuGuid");
        itemDTO.setSalePrice(new BigDecimal("0.00"));
        itemDTO.setMemberPrice(new BigDecimal("0.00"));
        itemDTO.setOriginalSalePrice(new BigDecimal("0.00"));
        itemDTO.setOriginalMemberPrice(new BigDecimal("0.00"));
        itemDTO.setIsRack(0);
        itemDTO.setIsJoinAio(0);
        itemDTO.setIsJoinPos(0);
        itemDTO.setIsJoinPad(0);

        when(mockIItemService.getItemInfoListByParentId("parentGuid", "storeGuid"))
                .thenReturn(Arrays.asList(new HashMap<>()));

        // Configure IItemService.getItemInfo(...).
        final ItemInfoRespDTO itemInfoRespDTO = new ItemInfoRespDTO();
        itemInfoRespDTO.setItemGuid("itemGuid");
        itemInfoRespDTO.setBrandGuid("brandGuid");
        itemInfoRespDTO.setTypeGuid("typeGuid");
        itemInfoRespDTO.setItemType(0);
        itemInfoRespDTO.setHasAttr(0);
        itemInfoRespDTO.setName("name");
        itemInfoRespDTO.setPinyin("pinyin");
        itemInfoRespDTO.setSort(0);
        itemInfoRespDTO.setDescription("description");
        itemInfoRespDTO.setPictureUrl("pictureUrl");
        itemInfoRespDTO.setIsBestseller(0);
        itemInfoRespDTO.setIsNew(0);
        itemInfoRespDTO.setIsSign(0);
        itemInfoRespDTO.setUpCount(0);
        itemInfoRespDTO.setDownCount(0);
        final SkuInfoRespDTO skuInfoRespDTO = new SkuInfoRespDTO();
        skuInfoRespDTO.setItemGuid("itemGuid");
        skuInfoRespDTO.setSkuGuid("parentGuid");
        skuInfoRespDTO.setMemberPrice(new BigDecimal("0.00"));
        skuInfoRespDTO.setName("name");
        skuInfoRespDTO.setSalePrice(new BigDecimal("0.00"));
        skuInfoRespDTO.setIsWholeDiscount(0);
        skuInfoRespDTO.setIsMemberDiscount(0);
        skuInfoRespDTO.setMinOrderNum(new BigDecimal("0.00"));
        skuInfoRespDTO.setUnit("unit");
        skuInfoRespDTO.setIsRack(0);
        skuInfoRespDTO.setIsJoinBuffet(0);
        skuInfoRespDTO.setIsJoinWeChat(0);
        skuInfoRespDTO.setIsJoinMt(0);
        skuInfoRespDTO.setIsJoinElm(0);
        skuInfoRespDTO.setIsOpenStock(0);
        skuInfoRespDTO.setBrandGuid("brandGuid");
        skuInfoRespDTO.setIsJoinMiniAppMall(0);
        skuInfoRespDTO.setIsJoinMiniAppTakeaway(0);
        skuInfoRespDTO.setIsJoinStore(0);
        itemInfoRespDTO.setSkuList(Arrays.asList(skuInfoRespDTO));
        final AttrGroupWebRespDTO attrGroupWebRespDTO = new AttrGroupWebRespDTO();
        attrGroupWebRespDTO.setItemGuid("itemGuid");
        attrGroupWebRespDTO.setItemAttrGroupGuid("itemAttrGroupGuid");
        attrGroupWebRespDTO.setAttrGroupGuid("attrGroupGuid");
        attrGroupWebRespDTO.setIsRequired(0);
        attrGroupWebRespDTO.setIsMultiChoice(0);
        attrGroupWebRespDTO.setWithDefault(0);
        final AttrWebRespDTO attrWebRespDTO = new AttrWebRespDTO();
        attrWebRespDTO.setAttrItemAttrGroupGuid("attrItemAttrGroupGuid");
        attrWebRespDTO.setAttrGuid("attrGuid");
        attrWebRespDTO.setIsDefault(0);
        attrGroupWebRespDTO.setAttrList(Arrays.asList(attrWebRespDTO));
        itemInfoRespDTO.setAttrGroupList(Arrays.asList(attrGroupWebRespDTO));
        final SubgroupWebRespDTO subgroupWebRespDTO = new SubgroupWebRespDTO();
        subgroupWebRespDTO.setSubgroupGuid("subgroupGuid");
        subgroupWebRespDTO.setName("name");
        subgroupWebRespDTO.setIsFixSubgroup(0);
        subgroupWebRespDTO.setPickNum(0);
        subgroupWebRespDTO.setSort(0);
        final SubItemSkuWebRespDTO subItemSkuWebRespDTO = new SubItemSkuWebRespDTO();
        subItemSkuWebRespDTO.setSkuGuid("skuGuid");
        subItemSkuWebRespDTO.setItemGuid("itemGuid");
        subItemSkuWebRespDTO.setItemNum(new BigDecimal("0.00"));
        subItemSkuWebRespDTO.setAddPrice(new BigDecimal("0.00"));
        subItemSkuWebRespDTO.setIsDefault(0);
        subItemSkuWebRespDTO.setIsRepeat(0);
        subgroupWebRespDTO.setSubItemSkuList(Arrays.asList(subItemSkuWebRespDTO));
        itemInfoRespDTO.setSubgroupList(Arrays.asList(subgroupWebRespDTO));
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setFrom(0);
        itemSingleDTO.setData("parentGuid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        when(mockIItemService.getItemInfo(itemSingleDTO)).thenReturn(itemInfoRespDTO);

        // Configure IPricePlanItemService.getSyncItem(...).
        final SyncItemDTO syncItemDTO = new SyncItemDTO();
        syncItemDTO.setItemGuid("parentGuid");
        syncItemDTO.setSkuGuid("skuGuid");
        syncItemDTO.setSalePrice(new BigDecimal("0.00"));
        syncItemDTO.setMemberPrice(new BigDecimal("0.00"));
        syncItemDTO.setOriginalSalePrice(new BigDecimal("0.00"));
        syncItemDTO.setOriginalMemberPrice(new BigDecimal("0.00"));
        syncItemDTO.setIsRack(0);
        syncItemDTO.setIsJoinAio(0);
        syncItemDTO.setIsJoinPos(0);
        syncItemDTO.setIsJoinPad(0);
        when(mockIPricePlanItemService.getSyncItem("parentGuid", "parentGuid")).thenReturn(syncItemDTO);

        // Configure IItemService.updateItem(...).
        final ItemReqDTO itemUpdateReqDTO = new ItemReqDTO();
        itemUpdateReqDTO.setFrom(0);
        itemUpdateReqDTO.setDataList(Arrays.asList("value"));
        itemUpdateReqDTO.setItemGuid("itemGuid");
        itemUpdateReqDTO.setTypeGuid("typeGuid");
        itemUpdateReqDTO.setStoreGuid("storeGuid");
        itemUpdateReqDTO.setBrandGuid("brandGuid");
        itemUpdateReqDTO.setItemFrom(0);
        itemUpdateReqDTO.setItemType(0);
        itemUpdateReqDTO.setHasAttr(0);
        itemUpdateReqDTO.setName("name");
        itemUpdateReqDTO.setPinyin("pinyin");
        itemUpdateReqDTO.setSort(0);
        itemUpdateReqDTO.setPictureUrl("pictureUrl");
        itemUpdateReqDTO.setIsBestseller(0);
        itemUpdateReqDTO.setIsNew(0);
        itemUpdateReqDTO.setIsSign(0);
        itemUpdateReqDTO.setDescription("description");
        final SkuSaveReqDTO skuSaveReqDTO = new SkuSaveReqDTO();
        skuSaveReqDTO.setItemGuid("itemGuid");
        skuSaveReqDTO.setSkuGuid("parentGuid");
        skuSaveReqDTO.setName("name");
        skuSaveReqDTO.setSalePrice(new BigDecimal("0.00"));
        skuSaveReqDTO.setMemberPrice(new BigDecimal("0.00"));
        skuSaveReqDTO.setUnit("unit");
        skuSaveReqDTO.setIsMemberDiscount(0);
        skuSaveReqDTO.setIsWholeDiscount(0);
        skuSaveReqDTO.setIsRack(0);
        skuSaveReqDTO.setIsJoinAio(0);
        skuSaveReqDTO.setIsJoinPos(0);
        skuSaveReqDTO.setIsJoinPad(0);
        skuSaveReqDTO.setMinOrderNum(new BigDecimal("0.00"));
        skuSaveReqDTO.setIsJoinBuffet(0);
        skuSaveReqDTO.setIsJoinWeChat(0);
        skuSaveReqDTO.setIsJoinMt(0);
        skuSaveReqDTO.setIsJoinElm(0);
        skuSaveReqDTO.setIsOpenStock(0);
        skuSaveReqDTO.setStoreGuid("storeGuid");
        skuSaveReqDTO.setBrandGuid("brandGuid");
        skuSaveReqDTO.setSkuFrom(0);
        skuSaveReqDTO.setIsJoinMiniAppMall(0);
        skuSaveReqDTO.setIsJoinMiniAppTakeaway(0);
        skuSaveReqDTO.setIsJoinStore(0);
        itemUpdateReqDTO.setSkuList(Arrays.asList(skuSaveReqDTO));
        final ItemAttrGroupReqDTO itemAttrGroupReqDTO = new ItemAttrGroupReqDTO();
        itemAttrGroupReqDTO.setItemGuid("itemGuid");
        itemAttrGroupReqDTO.setItemAttrGroupGuid("itemAttrGroupGuid");
        itemAttrGroupReqDTO.setAttrGroupGuid("attrGroupGuid");
        itemAttrGroupReqDTO.setIsRequired(0);
        itemAttrGroupReqDTO.setIsMultiChoice(0);
        itemAttrGroupReqDTO.setWithDefault(0);
        final ItemAttrReqDTO itemAttrReqDTO = new ItemAttrReqDTO();
        itemAttrReqDTO.setItemAttrGroupGuid("默认的ItemAttrGroupGuid");
        itemAttrReqDTO.setAttrItemAttrGroupGuid("attrItemAttrGroupGuid");
        itemAttrReqDTO.setAttrGuid("attrGuid");
        itemAttrReqDTO.setIsDefault(0);
        itemAttrGroupReqDTO.setAttrList(Arrays.asList(itemAttrReqDTO));
        itemUpdateReqDTO.setAttrGroupList(Arrays.asList(itemAttrGroupReqDTO));
        itemUpdateReqDTO.setUpCount(0);
        itemUpdateReqDTO.setDownCount(0);
        when(mockIItemService.updateItem(itemUpdateReqDTO)).thenReturn(Arrays.asList("value"));

        when(mockIItemService.updateOriginItemStoreGuid("parentGuid", "storeGuid", "brandGuid")).thenReturn(0);

        // Configure IRedissonCacheService.getCache(...).
        final SyncItemDTO syncItemDTO1 = new SyncItemDTO();
        syncItemDTO1.setItemGuid("parentGuid");
        syncItemDTO1.setSkuGuid("skuGuid");
        syncItemDTO1.setSalePrice(new BigDecimal("0.00"));
        syncItemDTO1.setMemberPrice(new BigDecimal("0.00"));
        syncItemDTO1.setOriginalSalePrice(new BigDecimal("0.00"));
        syncItemDTO1.setOriginalMemberPrice(new BigDecimal("0.00"));
        syncItemDTO1.setIsRack(0);
        syncItemDTO1.setIsJoinAio(0);
        syncItemDTO1.setIsJoinPos(0);
        syncItemDTO1.setIsJoinPad(0);
        when(mockIRedissonCacheService.getCache("key")).thenReturn(syncItemDTO1);

        when(mockISkuService.updateOriginPriceStoreGuid("parentGuid", "storeGuid", "skuGuid", new BigDecimal("0.00"),
                new BigDecimal("0.00"))).thenReturn(0);
        when(mockIItemService.getGuidByStoreGuidAndParentGuid("storeGuid", "parentGuid")).thenReturn("result");

        // Run the test
        final String result = syncPricePlanToItemServiceImplUnderTest.addPricePlanItem(itemDTO, "storeGuid",
                "brandGuid", "pricePlanGuid");

        // Verify the results
        assertThat(result).isEqualTo("result");
        verify(mockIItemService).deleteByGuidAndFrom("guid", 0L, "storeGuid");
        verify(mockISkuService).deleteSkuByItemGuidAndStoreGuid("itemGuid", "storeGuid");

        // Confirm IRedissonCacheService.setCache(...).
        final SyncItemDTO value = new SyncItemDTO();
        value.setItemGuid("parentGuid");
        value.setSkuGuid("skuGuid");
        value.setSalePrice(new BigDecimal("0.00"));
        value.setMemberPrice(new BigDecimal("0.00"));
        value.setOriginalSalePrice(new BigDecimal("0.00"));
        value.setOriginalMemberPrice(new BigDecimal("0.00"));
        value.setIsRack(0);
        value.setIsJoinAio(0);
        value.setIsJoinPos(0);
        value.setIsJoinPad(0);
        verify(mockIRedissonCacheService).setCache("key", value, 20L, TimeUnit.MINUTES);

        // Confirm ItemHelper.distributeItems2Stores(...).
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setFrom(0);
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        verify(mockItemHelper).distributeItems2Stores(itemStringListDTO, Arrays.asList("value"), true);

        // Confirm ISkuService.updateTerminalStatus(...).
        final UpdateTerminalStatusDTO updateTerminalStatusDTO = new UpdateTerminalStatusDTO();
        updateTerminalStatusDTO.setStoreGuid("storeGuid");
        updateTerminalStatusDTO.setParentGuid("parentGuid");
        updateTerminalStatusDTO.setIsJoinAio(0);
        updateTerminalStatusDTO.setIsJoinPos(0);
        updateTerminalStatusDTO.setIsJoinPad(0);
        updateTerminalStatusDTO.setIsJoinMiniAppMall(0);
        updateTerminalStatusDTO.setIsJoinMiniAppTakeaway(0);
        updateTerminalStatusDTO.setIsJoinStore(0);
        verify(mockISkuService).updateTerminalStatus(updateTerminalStatusDTO);
        verify(mockIRedissonCacheService).removeCache("key");
    }

    @Test
    public void testAddPricePlanItem_IItemServiceGetItemInfoListByParentIdReturnsNoItems() {
        // Setup
        final SyncItemDTO itemDTO = new SyncItemDTO();
        itemDTO.setItemGuid("parentGuid");
        itemDTO.setSkuGuid("skuGuid");
        itemDTO.setSalePrice(new BigDecimal("0.00"));
        itemDTO.setMemberPrice(new BigDecimal("0.00"));
        itemDTO.setOriginalSalePrice(new BigDecimal("0.00"));
        itemDTO.setOriginalMemberPrice(new BigDecimal("0.00"));
        itemDTO.setIsRack(0);
        itemDTO.setIsJoinAio(0);
        itemDTO.setIsJoinPos(0);
        itemDTO.setIsJoinPad(0);

        when(mockIItemService.getItemInfoListByParentId("parentGuid", "storeGuid")).thenReturn(Collections.emptyList());

        // Configure IItemService.getItemInfo(...).
        final ItemInfoRespDTO itemInfoRespDTO = new ItemInfoRespDTO();
        itemInfoRespDTO.setItemGuid("itemGuid");
        itemInfoRespDTO.setBrandGuid("brandGuid");
        itemInfoRespDTO.setTypeGuid("typeGuid");
        itemInfoRespDTO.setItemType(0);
        itemInfoRespDTO.setHasAttr(0);
        itemInfoRespDTO.setName("name");
        itemInfoRespDTO.setPinyin("pinyin");
        itemInfoRespDTO.setSort(0);
        itemInfoRespDTO.setDescription("description");
        itemInfoRespDTO.setPictureUrl("pictureUrl");
        itemInfoRespDTO.setIsBestseller(0);
        itemInfoRespDTO.setIsNew(0);
        itemInfoRespDTO.setIsSign(0);
        itemInfoRespDTO.setUpCount(0);
        itemInfoRespDTO.setDownCount(0);
        final SkuInfoRespDTO skuInfoRespDTO = new SkuInfoRespDTO();
        skuInfoRespDTO.setItemGuid("itemGuid");
        skuInfoRespDTO.setSkuGuid("parentGuid");
        skuInfoRespDTO.setMemberPrice(new BigDecimal("0.00"));
        skuInfoRespDTO.setName("name");
        skuInfoRespDTO.setSalePrice(new BigDecimal("0.00"));
        skuInfoRespDTO.setIsWholeDiscount(0);
        skuInfoRespDTO.setIsMemberDiscount(0);
        skuInfoRespDTO.setMinOrderNum(new BigDecimal("0.00"));
        skuInfoRespDTO.setUnit("unit");
        skuInfoRespDTO.setIsRack(0);
        skuInfoRespDTO.setIsJoinBuffet(0);
        skuInfoRespDTO.setIsJoinWeChat(0);
        skuInfoRespDTO.setIsJoinMt(0);
        skuInfoRespDTO.setIsJoinElm(0);
        skuInfoRespDTO.setIsOpenStock(0);
        skuInfoRespDTO.setBrandGuid("brandGuid");
        skuInfoRespDTO.setIsJoinMiniAppMall(0);
        skuInfoRespDTO.setIsJoinMiniAppTakeaway(0);
        skuInfoRespDTO.setIsJoinStore(0);
        itemInfoRespDTO.setSkuList(Arrays.asList(skuInfoRespDTO));
        final AttrGroupWebRespDTO attrGroupWebRespDTO = new AttrGroupWebRespDTO();
        attrGroupWebRespDTO.setItemGuid("itemGuid");
        attrGroupWebRespDTO.setItemAttrGroupGuid("itemAttrGroupGuid");
        attrGroupWebRespDTO.setAttrGroupGuid("attrGroupGuid");
        attrGroupWebRespDTO.setIsRequired(0);
        attrGroupWebRespDTO.setIsMultiChoice(0);
        attrGroupWebRespDTO.setWithDefault(0);
        final AttrWebRespDTO attrWebRespDTO = new AttrWebRespDTO();
        attrWebRespDTO.setAttrItemAttrGroupGuid("attrItemAttrGroupGuid");
        attrWebRespDTO.setAttrGuid("attrGuid");
        attrWebRespDTO.setIsDefault(0);
        attrGroupWebRespDTO.setAttrList(Arrays.asList(attrWebRespDTO));
        itemInfoRespDTO.setAttrGroupList(Arrays.asList(attrGroupWebRespDTO));
        final SubgroupWebRespDTO subgroupWebRespDTO = new SubgroupWebRespDTO();
        subgroupWebRespDTO.setSubgroupGuid("subgroupGuid");
        subgroupWebRespDTO.setName("name");
        subgroupWebRespDTO.setIsFixSubgroup(0);
        subgroupWebRespDTO.setPickNum(0);
        subgroupWebRespDTO.setSort(0);
        final SubItemSkuWebRespDTO subItemSkuWebRespDTO = new SubItemSkuWebRespDTO();
        subItemSkuWebRespDTO.setSkuGuid("skuGuid");
        subItemSkuWebRespDTO.setItemGuid("itemGuid");
        subItemSkuWebRespDTO.setItemNum(new BigDecimal("0.00"));
        subItemSkuWebRespDTO.setAddPrice(new BigDecimal("0.00"));
        subItemSkuWebRespDTO.setIsDefault(0);
        subItemSkuWebRespDTO.setIsRepeat(0);
        subgroupWebRespDTO.setSubItemSkuList(Arrays.asList(subItemSkuWebRespDTO));
        itemInfoRespDTO.setSubgroupList(Arrays.asList(subgroupWebRespDTO));
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setFrom(0);
        itemSingleDTO.setData("parentGuid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        when(mockIItemService.getItemInfo(itemSingleDTO)).thenReturn(itemInfoRespDTO);

        // Configure IPricePlanItemService.getSyncItem(...).
        final SyncItemDTO syncItemDTO = new SyncItemDTO();
        syncItemDTO.setItemGuid("parentGuid");
        syncItemDTO.setSkuGuid("skuGuid");
        syncItemDTO.setSalePrice(new BigDecimal("0.00"));
        syncItemDTO.setMemberPrice(new BigDecimal("0.00"));
        syncItemDTO.setOriginalSalePrice(new BigDecimal("0.00"));
        syncItemDTO.setOriginalMemberPrice(new BigDecimal("0.00"));
        syncItemDTO.setIsRack(0);
        syncItemDTO.setIsJoinAio(0);
        syncItemDTO.setIsJoinPos(0);
        syncItemDTO.setIsJoinPad(0);
        when(mockIPricePlanItemService.getSyncItem("parentGuid", "parentGuid")).thenReturn(syncItemDTO);

        // Configure IItemService.updateItem(...).
        final ItemReqDTO itemUpdateReqDTO = new ItemReqDTO();
        itemUpdateReqDTO.setFrom(0);
        itemUpdateReqDTO.setDataList(Arrays.asList("value"));
        itemUpdateReqDTO.setItemGuid("itemGuid");
        itemUpdateReqDTO.setTypeGuid("typeGuid");
        itemUpdateReqDTO.setStoreGuid("storeGuid");
        itemUpdateReqDTO.setBrandGuid("brandGuid");
        itemUpdateReqDTO.setItemFrom(0);
        itemUpdateReqDTO.setItemType(0);
        itemUpdateReqDTO.setHasAttr(0);
        itemUpdateReqDTO.setName("name");
        itemUpdateReqDTO.setPinyin("pinyin");
        itemUpdateReqDTO.setSort(0);
        itemUpdateReqDTO.setPictureUrl("pictureUrl");
        itemUpdateReqDTO.setIsBestseller(0);
        itemUpdateReqDTO.setIsNew(0);
        itemUpdateReqDTO.setIsSign(0);
        itemUpdateReqDTO.setDescription("description");
        final SkuSaveReqDTO skuSaveReqDTO = new SkuSaveReqDTO();
        skuSaveReqDTO.setItemGuid("itemGuid");
        skuSaveReqDTO.setSkuGuid("parentGuid");
        skuSaveReqDTO.setName("name");
        skuSaveReqDTO.setSalePrice(new BigDecimal("0.00"));
        skuSaveReqDTO.setMemberPrice(new BigDecimal("0.00"));
        skuSaveReqDTO.setUnit("unit");
        skuSaveReqDTO.setIsMemberDiscount(0);
        skuSaveReqDTO.setIsWholeDiscount(0);
        skuSaveReqDTO.setIsRack(0);
        skuSaveReqDTO.setIsJoinAio(0);
        skuSaveReqDTO.setIsJoinPos(0);
        skuSaveReqDTO.setIsJoinPad(0);
        skuSaveReqDTO.setMinOrderNum(new BigDecimal("0.00"));
        skuSaveReqDTO.setIsJoinBuffet(0);
        skuSaveReqDTO.setIsJoinWeChat(0);
        skuSaveReqDTO.setIsJoinMt(0);
        skuSaveReqDTO.setIsJoinElm(0);
        skuSaveReqDTO.setIsOpenStock(0);
        skuSaveReqDTO.setStoreGuid("storeGuid");
        skuSaveReqDTO.setBrandGuid("brandGuid");
        skuSaveReqDTO.setSkuFrom(0);
        skuSaveReqDTO.setIsJoinMiniAppMall(0);
        skuSaveReqDTO.setIsJoinMiniAppTakeaway(0);
        skuSaveReqDTO.setIsJoinStore(0);
        itemUpdateReqDTO.setSkuList(Arrays.asList(skuSaveReqDTO));
        final ItemAttrGroupReqDTO itemAttrGroupReqDTO = new ItemAttrGroupReqDTO();
        itemAttrGroupReqDTO.setItemGuid("itemGuid");
        itemAttrGroupReqDTO.setItemAttrGroupGuid("itemAttrGroupGuid");
        itemAttrGroupReqDTO.setAttrGroupGuid("attrGroupGuid");
        itemAttrGroupReqDTO.setIsRequired(0);
        itemAttrGroupReqDTO.setIsMultiChoice(0);
        itemAttrGroupReqDTO.setWithDefault(0);
        final ItemAttrReqDTO itemAttrReqDTO = new ItemAttrReqDTO();
        itemAttrReqDTO.setItemAttrGroupGuid("默认的ItemAttrGroupGuid");
        itemAttrReqDTO.setAttrItemAttrGroupGuid("attrItemAttrGroupGuid");
        itemAttrReqDTO.setAttrGuid("attrGuid");
        itemAttrReqDTO.setIsDefault(0);
        itemAttrGroupReqDTO.setAttrList(Arrays.asList(itemAttrReqDTO));
        itemUpdateReqDTO.setAttrGroupList(Arrays.asList(itemAttrGroupReqDTO));
        itemUpdateReqDTO.setUpCount(0);
        itemUpdateReqDTO.setDownCount(0);
        when(mockIItemService.updateItem(itemUpdateReqDTO)).thenReturn(Arrays.asList("value"));

        when(mockIItemService.updateOriginItemStoreGuid("parentGuid", "storeGuid", "brandGuid")).thenReturn(0);

        // Configure IRedissonCacheService.getCache(...).
        final SyncItemDTO syncItemDTO1 = new SyncItemDTO();
        syncItemDTO1.setItemGuid("parentGuid");
        syncItemDTO1.setSkuGuid("skuGuid");
        syncItemDTO1.setSalePrice(new BigDecimal("0.00"));
        syncItemDTO1.setMemberPrice(new BigDecimal("0.00"));
        syncItemDTO1.setOriginalSalePrice(new BigDecimal("0.00"));
        syncItemDTO1.setOriginalMemberPrice(new BigDecimal("0.00"));
        syncItemDTO1.setIsRack(0);
        syncItemDTO1.setIsJoinAio(0);
        syncItemDTO1.setIsJoinPos(0);
        syncItemDTO1.setIsJoinPad(0);
        when(mockIRedissonCacheService.getCache("key")).thenReturn(syncItemDTO1);

        when(mockISkuService.updateOriginPriceStoreGuid("parentGuid", "storeGuid", "skuGuid", new BigDecimal("0.00"),
                new BigDecimal("0.00"))).thenReturn(0);
        when(mockIItemService.getGuidByStoreGuidAndParentGuid("storeGuid", "parentGuid")).thenReturn("result");

        // Run the test
        final String result = syncPricePlanToItemServiceImplUnderTest.addPricePlanItem(itemDTO, "storeGuid",
                "brandGuid", "pricePlanGuid");

        // Verify the results
        assertThat(result).isEqualTo("result");

        // Confirm IRedissonCacheService.setCache(...).
        final SyncItemDTO value = new SyncItemDTO();
        value.setItemGuid("parentGuid");
        value.setSkuGuid("skuGuid");
        value.setSalePrice(new BigDecimal("0.00"));
        value.setMemberPrice(new BigDecimal("0.00"));
        value.setOriginalSalePrice(new BigDecimal("0.00"));
        value.setOriginalMemberPrice(new BigDecimal("0.00"));
        value.setIsRack(0);
        value.setIsJoinAio(0);
        value.setIsJoinPos(0);
        value.setIsJoinPad(0);
        verify(mockIRedissonCacheService).setCache("key", value, 20L, TimeUnit.MINUTES);

        // Confirm ItemHelper.distributeItems2Stores(...).
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setFrom(0);
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        verify(mockItemHelper).distributeItems2Stores(itemStringListDTO, Arrays.asList("value"), true);

        // Confirm ISkuService.updateTerminalStatus(...).
        final UpdateTerminalStatusDTO updateTerminalStatusDTO = new UpdateTerminalStatusDTO();
        updateTerminalStatusDTO.setStoreGuid("storeGuid");
        updateTerminalStatusDTO.setParentGuid("parentGuid");
        updateTerminalStatusDTO.setIsJoinAio(0);
        updateTerminalStatusDTO.setIsJoinPos(0);
        updateTerminalStatusDTO.setIsJoinPad(0);
        updateTerminalStatusDTO.setIsJoinMiniAppMall(0);
        updateTerminalStatusDTO.setIsJoinMiniAppTakeaway(0);
        updateTerminalStatusDTO.setIsJoinStore(0);
        verify(mockISkuService).updateTerminalStatus(updateTerminalStatusDTO);
        verify(mockIRedissonCacheService).removeCache("key");
    }

    @Test
    public void testAddPricePlanItem_IItemServiceUpdateItemReturnsNoItems() {
        // Setup
        final SyncItemDTO itemDTO = new SyncItemDTO();
        itemDTO.setItemGuid("parentGuid");
        itemDTO.setSkuGuid("skuGuid");
        itemDTO.setSalePrice(new BigDecimal("0.00"));
        itemDTO.setMemberPrice(new BigDecimal("0.00"));
        itemDTO.setOriginalSalePrice(new BigDecimal("0.00"));
        itemDTO.setOriginalMemberPrice(new BigDecimal("0.00"));
        itemDTO.setIsRack(0);
        itemDTO.setIsJoinAio(0);
        itemDTO.setIsJoinPos(0);
        itemDTO.setIsJoinPad(0);

        when(mockIItemService.getItemInfoListByParentId("parentGuid", "storeGuid"))
                .thenReturn(Arrays.asList(new HashMap<>()));

        // Configure IItemService.getItemInfo(...).
        final ItemInfoRespDTO itemInfoRespDTO = new ItemInfoRespDTO();
        itemInfoRespDTO.setItemGuid("itemGuid");
        itemInfoRespDTO.setBrandGuid("brandGuid");
        itemInfoRespDTO.setTypeGuid("typeGuid");
        itemInfoRespDTO.setItemType(0);
        itemInfoRespDTO.setHasAttr(0);
        itemInfoRespDTO.setName("name");
        itemInfoRespDTO.setPinyin("pinyin");
        itemInfoRespDTO.setSort(0);
        itemInfoRespDTO.setDescription("description");
        itemInfoRespDTO.setPictureUrl("pictureUrl");
        itemInfoRespDTO.setIsBestseller(0);
        itemInfoRespDTO.setIsNew(0);
        itemInfoRespDTO.setIsSign(0);
        itemInfoRespDTO.setUpCount(0);
        itemInfoRespDTO.setDownCount(0);
        final SkuInfoRespDTO skuInfoRespDTO = new SkuInfoRespDTO();
        skuInfoRespDTO.setItemGuid("itemGuid");
        skuInfoRespDTO.setSkuGuid("parentGuid");
        skuInfoRespDTO.setMemberPrice(new BigDecimal("0.00"));
        skuInfoRespDTO.setName("name");
        skuInfoRespDTO.setSalePrice(new BigDecimal("0.00"));
        skuInfoRespDTO.setIsWholeDiscount(0);
        skuInfoRespDTO.setIsMemberDiscount(0);
        skuInfoRespDTO.setMinOrderNum(new BigDecimal("0.00"));
        skuInfoRespDTO.setUnit("unit");
        skuInfoRespDTO.setIsRack(0);
        skuInfoRespDTO.setIsJoinBuffet(0);
        skuInfoRespDTO.setIsJoinWeChat(0);
        skuInfoRespDTO.setIsJoinMt(0);
        skuInfoRespDTO.setIsJoinElm(0);
        skuInfoRespDTO.setIsOpenStock(0);
        skuInfoRespDTO.setBrandGuid("brandGuid");
        skuInfoRespDTO.setIsJoinMiniAppMall(0);
        skuInfoRespDTO.setIsJoinMiniAppTakeaway(0);
        skuInfoRespDTO.setIsJoinStore(0);
        itemInfoRespDTO.setSkuList(Arrays.asList(skuInfoRespDTO));
        final AttrGroupWebRespDTO attrGroupWebRespDTO = new AttrGroupWebRespDTO();
        attrGroupWebRespDTO.setItemGuid("itemGuid");
        attrGroupWebRespDTO.setItemAttrGroupGuid("itemAttrGroupGuid");
        attrGroupWebRespDTO.setAttrGroupGuid("attrGroupGuid");
        attrGroupWebRespDTO.setIsRequired(0);
        attrGroupWebRespDTO.setIsMultiChoice(0);
        attrGroupWebRespDTO.setWithDefault(0);
        final AttrWebRespDTO attrWebRespDTO = new AttrWebRespDTO();
        attrWebRespDTO.setAttrItemAttrGroupGuid("attrItemAttrGroupGuid");
        attrWebRespDTO.setAttrGuid("attrGuid");
        attrWebRespDTO.setIsDefault(0);
        attrGroupWebRespDTO.setAttrList(Arrays.asList(attrWebRespDTO));
        itemInfoRespDTO.setAttrGroupList(Arrays.asList(attrGroupWebRespDTO));
        final SubgroupWebRespDTO subgroupWebRespDTO = new SubgroupWebRespDTO();
        subgroupWebRespDTO.setSubgroupGuid("subgroupGuid");
        subgroupWebRespDTO.setName("name");
        subgroupWebRespDTO.setIsFixSubgroup(0);
        subgroupWebRespDTO.setPickNum(0);
        subgroupWebRespDTO.setSort(0);
        final SubItemSkuWebRespDTO subItemSkuWebRespDTO = new SubItemSkuWebRespDTO();
        subItemSkuWebRespDTO.setSkuGuid("skuGuid");
        subItemSkuWebRespDTO.setItemGuid("itemGuid");
        subItemSkuWebRespDTO.setItemNum(new BigDecimal("0.00"));
        subItemSkuWebRespDTO.setAddPrice(new BigDecimal("0.00"));
        subItemSkuWebRespDTO.setIsDefault(0);
        subItemSkuWebRespDTO.setIsRepeat(0);
        subgroupWebRespDTO.setSubItemSkuList(Arrays.asList(subItemSkuWebRespDTO));
        itemInfoRespDTO.setSubgroupList(Arrays.asList(subgroupWebRespDTO));
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setFrom(0);
        itemSingleDTO.setData("parentGuid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        when(mockIItemService.getItemInfo(itemSingleDTO)).thenReturn(itemInfoRespDTO);

        // Configure IPricePlanItemService.getSyncItem(...).
        final SyncItemDTO syncItemDTO = new SyncItemDTO();
        syncItemDTO.setItemGuid("parentGuid");
        syncItemDTO.setSkuGuid("skuGuid");
        syncItemDTO.setSalePrice(new BigDecimal("0.00"));
        syncItemDTO.setMemberPrice(new BigDecimal("0.00"));
        syncItemDTO.setOriginalSalePrice(new BigDecimal("0.00"));
        syncItemDTO.setOriginalMemberPrice(new BigDecimal("0.00"));
        syncItemDTO.setIsRack(0);
        syncItemDTO.setIsJoinAio(0);
        syncItemDTO.setIsJoinPos(0);
        syncItemDTO.setIsJoinPad(0);
        when(mockIPricePlanItemService.getSyncItem("parentGuid", "parentGuid")).thenReturn(syncItemDTO);

        // Configure IItemService.updateItem(...).
        final ItemReqDTO itemUpdateReqDTO = new ItemReqDTO();
        itemUpdateReqDTO.setFrom(0);
        itemUpdateReqDTO.setDataList(Arrays.asList("value"));
        itemUpdateReqDTO.setItemGuid("itemGuid");
        itemUpdateReqDTO.setTypeGuid("typeGuid");
        itemUpdateReqDTO.setStoreGuid("storeGuid");
        itemUpdateReqDTO.setBrandGuid("brandGuid");
        itemUpdateReqDTO.setItemFrom(0);
        itemUpdateReqDTO.setItemType(0);
        itemUpdateReqDTO.setHasAttr(0);
        itemUpdateReqDTO.setName("name");
        itemUpdateReqDTO.setPinyin("pinyin");
        itemUpdateReqDTO.setSort(0);
        itemUpdateReqDTO.setPictureUrl("pictureUrl");
        itemUpdateReqDTO.setIsBestseller(0);
        itemUpdateReqDTO.setIsNew(0);
        itemUpdateReqDTO.setIsSign(0);
        itemUpdateReqDTO.setDescription("description");
        final SkuSaveReqDTO skuSaveReqDTO = new SkuSaveReqDTO();
        skuSaveReqDTO.setItemGuid("itemGuid");
        skuSaveReqDTO.setSkuGuid("parentGuid");
        skuSaveReqDTO.setName("name");
        skuSaveReqDTO.setSalePrice(new BigDecimal("0.00"));
        skuSaveReqDTO.setMemberPrice(new BigDecimal("0.00"));
        skuSaveReqDTO.setUnit("unit");
        skuSaveReqDTO.setIsMemberDiscount(0);
        skuSaveReqDTO.setIsWholeDiscount(0);
        skuSaveReqDTO.setIsRack(0);
        skuSaveReqDTO.setIsJoinAio(0);
        skuSaveReqDTO.setIsJoinPos(0);
        skuSaveReqDTO.setIsJoinPad(0);
        skuSaveReqDTO.setMinOrderNum(new BigDecimal("0.00"));
        skuSaveReqDTO.setIsJoinBuffet(0);
        skuSaveReqDTO.setIsJoinWeChat(0);
        skuSaveReqDTO.setIsJoinMt(0);
        skuSaveReqDTO.setIsJoinElm(0);
        skuSaveReqDTO.setIsOpenStock(0);
        skuSaveReqDTO.setStoreGuid("storeGuid");
        skuSaveReqDTO.setBrandGuid("brandGuid");
        skuSaveReqDTO.setSkuFrom(0);
        skuSaveReqDTO.setIsJoinMiniAppMall(0);
        skuSaveReqDTO.setIsJoinMiniAppTakeaway(0);
        skuSaveReqDTO.setIsJoinStore(0);
        itemUpdateReqDTO.setSkuList(Arrays.asList(skuSaveReqDTO));
        final ItemAttrGroupReqDTO itemAttrGroupReqDTO = new ItemAttrGroupReqDTO();
        itemAttrGroupReqDTO.setItemGuid("itemGuid");
        itemAttrGroupReqDTO.setItemAttrGroupGuid("itemAttrGroupGuid");
        itemAttrGroupReqDTO.setAttrGroupGuid("attrGroupGuid");
        itemAttrGroupReqDTO.setIsRequired(0);
        itemAttrGroupReqDTO.setIsMultiChoice(0);
        itemAttrGroupReqDTO.setWithDefault(0);
        final ItemAttrReqDTO itemAttrReqDTO = new ItemAttrReqDTO();
        itemAttrReqDTO.setItemAttrGroupGuid("默认的ItemAttrGroupGuid");
        itemAttrReqDTO.setAttrItemAttrGroupGuid("attrItemAttrGroupGuid");
        itemAttrReqDTO.setAttrGuid("attrGuid");
        itemAttrReqDTO.setIsDefault(0);
        itemAttrGroupReqDTO.setAttrList(Arrays.asList(itemAttrReqDTO));
        itemUpdateReqDTO.setAttrGroupList(Arrays.asList(itemAttrGroupReqDTO));
        itemUpdateReqDTO.setUpCount(0);
        itemUpdateReqDTO.setDownCount(0);
        when(mockIItemService.updateItem(itemUpdateReqDTO)).thenReturn(Collections.emptyList());

        when(mockIItemService.updateOriginItemStoreGuid("parentGuid", "storeGuid", "brandGuid")).thenReturn(0);

        // Configure IRedissonCacheService.getCache(...).
        final SyncItemDTO syncItemDTO1 = new SyncItemDTO();
        syncItemDTO1.setItemGuid("parentGuid");
        syncItemDTO1.setSkuGuid("skuGuid");
        syncItemDTO1.setSalePrice(new BigDecimal("0.00"));
        syncItemDTO1.setMemberPrice(new BigDecimal("0.00"));
        syncItemDTO1.setOriginalSalePrice(new BigDecimal("0.00"));
        syncItemDTO1.setOriginalMemberPrice(new BigDecimal("0.00"));
        syncItemDTO1.setIsRack(0);
        syncItemDTO1.setIsJoinAio(0);
        syncItemDTO1.setIsJoinPos(0);
        syncItemDTO1.setIsJoinPad(0);
        when(mockIRedissonCacheService.getCache("key")).thenReturn(syncItemDTO1);

        when(mockISkuService.updateOriginPriceStoreGuid("parentGuid", "storeGuid", "skuGuid", new BigDecimal("0.00"),
                new BigDecimal("0.00"))).thenReturn(0);
        when(mockIItemService.getGuidByStoreGuidAndParentGuid("storeGuid", "parentGuid")).thenReturn("result");

        // Run the test
        final String result = syncPricePlanToItemServiceImplUnderTest.addPricePlanItem(itemDTO, "storeGuid",
                "brandGuid", "pricePlanGuid");

        // Verify the results
        assertThat(result).isEqualTo("result");
        verify(mockIItemService).deleteByGuidAndFrom("guid", 0L, "storeGuid");
        verify(mockISkuService).deleteSkuByItemGuidAndStoreGuid("itemGuid", "storeGuid");

        // Confirm IRedissonCacheService.setCache(...).
        final SyncItemDTO value = new SyncItemDTO();
        value.setItemGuid("parentGuid");
        value.setSkuGuid("skuGuid");
        value.setSalePrice(new BigDecimal("0.00"));
        value.setMemberPrice(new BigDecimal("0.00"));
        value.setOriginalSalePrice(new BigDecimal("0.00"));
        value.setOriginalMemberPrice(new BigDecimal("0.00"));
        value.setIsRack(0);
        value.setIsJoinAio(0);
        value.setIsJoinPos(0);
        value.setIsJoinPad(0);
        verify(mockIRedissonCacheService).setCache("key", value, 20L, TimeUnit.MINUTES);

        // Confirm ItemHelper.distributeItems2Stores(...).
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setFrom(0);
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        verify(mockItemHelper).distributeItems2Stores(itemStringListDTO, Arrays.asList("value"), true);

        // Confirm ISkuService.updateTerminalStatus(...).
        final UpdateTerminalStatusDTO updateTerminalStatusDTO = new UpdateTerminalStatusDTO();
        updateTerminalStatusDTO.setStoreGuid("storeGuid");
        updateTerminalStatusDTO.setParentGuid("parentGuid");
        updateTerminalStatusDTO.setIsJoinAio(0);
        updateTerminalStatusDTO.setIsJoinPos(0);
        updateTerminalStatusDTO.setIsJoinPad(0);
        updateTerminalStatusDTO.setIsJoinMiniAppMall(0);
        updateTerminalStatusDTO.setIsJoinMiniAppTakeaway(0);
        updateTerminalStatusDTO.setIsJoinStore(0);
        verify(mockISkuService).updateTerminalStatus(updateTerminalStatusDTO);
        verify(mockIRedissonCacheService).removeCache("key");
    }

    @Test
    public void testAddPricePlanPkgItem() {
        // Setup
        final SyncItemDTO itemDTO = new SyncItemDTO();
        itemDTO.setItemGuid("parentGuid");
        itemDTO.setSkuGuid("skuGuid");
        itemDTO.setSalePrice(new BigDecimal("0.00"));
        itemDTO.setMemberPrice(new BigDecimal("0.00"));
        itemDTO.setOriginalSalePrice(new BigDecimal("0.00"));
        itemDTO.setOriginalMemberPrice(new BigDecimal("0.00"));
        itemDTO.setIsRack(0);
        itemDTO.setIsJoinAio(0);
        itemDTO.setIsJoinPos(0);
        itemDTO.setIsJoinPad(0);

        when(mockIItemService.getItemInfoListByParentId("parentGuid", "storeGuid"))
                .thenReturn(Arrays.asList(new HashMap<>()));

        // Configure IItemService.getItemInfo(...).
        final ItemInfoRespDTO itemInfoRespDTO = new ItemInfoRespDTO();
        itemInfoRespDTO.setItemGuid("itemGuid");
        itemInfoRespDTO.setBrandGuid("brandGuid");
        itemInfoRespDTO.setTypeGuid("typeGuid");
        itemInfoRespDTO.setItemType(0);
        itemInfoRespDTO.setHasAttr(0);
        itemInfoRespDTO.setName("name");
        itemInfoRespDTO.setPinyin("pinyin");
        itemInfoRespDTO.setSort(0);
        itemInfoRespDTO.setDescription("description");
        itemInfoRespDTO.setPictureUrl("pictureUrl");
        itemInfoRespDTO.setIsBestseller(0);
        itemInfoRespDTO.setIsNew(0);
        itemInfoRespDTO.setIsSign(0);
        itemInfoRespDTO.setUpCount(0);
        itemInfoRespDTO.setDownCount(0);
        final SkuInfoRespDTO skuInfoRespDTO = new SkuInfoRespDTO();
        skuInfoRespDTO.setItemGuid("itemGuid");
        skuInfoRespDTO.setSkuGuid("parentGuid");
        skuInfoRespDTO.setMemberPrice(new BigDecimal("0.00"));
        skuInfoRespDTO.setName("name");
        skuInfoRespDTO.setSalePrice(new BigDecimal("0.00"));
        skuInfoRespDTO.setIsWholeDiscount(0);
        skuInfoRespDTO.setIsMemberDiscount(0);
        skuInfoRespDTO.setMinOrderNum(new BigDecimal("0.00"));
        skuInfoRespDTO.setUnit("unit");
        skuInfoRespDTO.setIsRack(0);
        skuInfoRespDTO.setIsJoinBuffet(0);
        skuInfoRespDTO.setIsJoinWeChat(0);
        skuInfoRespDTO.setIsJoinMt(0);
        skuInfoRespDTO.setIsJoinElm(0);
        skuInfoRespDTO.setIsOpenStock(0);
        skuInfoRespDTO.setBrandGuid("brandGuid");
        skuInfoRespDTO.setIsJoinMiniAppMall(0);
        skuInfoRespDTO.setIsJoinMiniAppTakeaway(0);
        skuInfoRespDTO.setIsJoinStore(0);
        itemInfoRespDTO.setSkuList(Arrays.asList(skuInfoRespDTO));
        final AttrGroupWebRespDTO attrGroupWebRespDTO = new AttrGroupWebRespDTO();
        attrGroupWebRespDTO.setItemGuid("itemGuid");
        attrGroupWebRespDTO.setItemAttrGroupGuid("itemAttrGroupGuid");
        attrGroupWebRespDTO.setAttrGroupGuid("attrGroupGuid");
        attrGroupWebRespDTO.setIsRequired(0);
        attrGroupWebRespDTO.setIsMultiChoice(0);
        attrGroupWebRespDTO.setWithDefault(0);
        final AttrWebRespDTO attrWebRespDTO = new AttrWebRespDTO();
        attrWebRespDTO.setAttrItemAttrGroupGuid("attrItemAttrGroupGuid");
        attrWebRespDTO.setAttrGuid("attrGuid");
        attrWebRespDTO.setIsDefault(0);
        attrGroupWebRespDTO.setAttrList(Arrays.asList(attrWebRespDTO));
        itemInfoRespDTO.setAttrGroupList(Arrays.asList(attrGroupWebRespDTO));
        final SubgroupWebRespDTO subgroupWebRespDTO = new SubgroupWebRespDTO();
        subgroupWebRespDTO.setSubgroupGuid("subgroupGuid");
        subgroupWebRespDTO.setName("name");
        subgroupWebRespDTO.setIsFixSubgroup(0);
        subgroupWebRespDTO.setPickNum(0);
        subgroupWebRespDTO.setSort(0);
        final SubItemSkuWebRespDTO subItemSkuWebRespDTO = new SubItemSkuWebRespDTO();
        subItemSkuWebRespDTO.setSkuGuid("skuGuid");
        subItemSkuWebRespDTO.setItemGuid("itemGuid");
        subItemSkuWebRespDTO.setItemNum(new BigDecimal("0.00"));
        subItemSkuWebRespDTO.setAddPrice(new BigDecimal("0.00"));
        subItemSkuWebRespDTO.setIsDefault(0);
        subItemSkuWebRespDTO.setIsRepeat(0);
        subgroupWebRespDTO.setSubItemSkuList(Arrays.asList(subItemSkuWebRespDTO));
        itemInfoRespDTO.setSubgroupList(Arrays.asList(subgroupWebRespDTO));
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setFrom(0);
        itemSingleDTO.setData("parentGuid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        when(mockIItemService.getItemInfo(itemSingleDTO)).thenReturn(itemInfoRespDTO);

        // Configure IPricePlanItemService.getSyncItem(...).
        final SyncItemDTO syncItemDTO = new SyncItemDTO();
        syncItemDTO.setItemGuid("parentGuid");
        syncItemDTO.setSkuGuid("skuGuid");
        syncItemDTO.setSalePrice(new BigDecimal("0.00"));
        syncItemDTO.setMemberPrice(new BigDecimal("0.00"));
        syncItemDTO.setOriginalSalePrice(new BigDecimal("0.00"));
        syncItemDTO.setOriginalMemberPrice(new BigDecimal("0.00"));
        syncItemDTO.setIsRack(0);
        syncItemDTO.setIsJoinAio(0);
        syncItemDTO.setIsJoinPos(0);
        syncItemDTO.setIsJoinPad(0);
        when(mockIPricePlanItemService.getSyncItem("parentGuid", "parentGuid")).thenReturn(syncItemDTO);

        // Configure ISubgroupService.list(...).
        final SubgroupDO subgroupDO = new SubgroupDO();
        subgroupDO.setId(0L);
        subgroupDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        subgroupDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        subgroupDO.setGuid("9fbb2f86-9c26-492d-bb2e-2590fd13b1d4");
        subgroupDO.setItemGuid("newItemGuid");
        final List<SubgroupDO> subgroupDOS = Arrays.asList(subgroupDO);
        when(mockISubgroupService.list(any(QueryWrapper.class))).thenReturn(subgroupDOS);

        // Configure IRSkuSubgroupService.list(...).
        final RSkuSubgroupDO rSkuSubgroupDO = new RSkuSubgroupDO();
        rSkuSubgroupDO.setId(0L);
        rSkuSubgroupDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        rSkuSubgroupDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        rSkuSubgroupDO.setGuid("guid");
        rSkuSubgroupDO.setItemGuid("itemGuid");
        final List<RSkuSubgroupDO> rSkuSubgroupDOS = Arrays.asList(rSkuSubgroupDO);
        when(mockIrSkuSubgroupService.list(any(QueryWrapper.class))).thenReturn(rSkuSubgroupDOS);

        // Configure IItemPkgService.updateItemPkg(...).
        final ItemPkgSaveReqDTO itemPkgSaveReqDTO = new ItemPkgSaveReqDTO();
        itemPkgSaveReqDTO.setFrom(0);
        itemPkgSaveReqDTO.setDataList(Arrays.asList("value"));
        itemPkgSaveReqDTO.setItemGuid("parentGuid");
        itemPkgSaveReqDTO.setTypeGuid("typeGuid");
        itemPkgSaveReqDTO.setStoreGuid("storeGuid");
        itemPkgSaveReqDTO.setBrandGuid("brandGuid");
        itemPkgSaveReqDTO.setItemType(0);
        itemPkgSaveReqDTO.setName("name");
        itemPkgSaveReqDTO.setPinyin("pinyin");
        itemPkgSaveReqDTO.setSort(0);
        itemPkgSaveReqDTO.setPictureUrl("pictureUrl");
        itemPkgSaveReqDTO.setIsBestseller(0);
        itemPkgSaveReqDTO.setIsNew(0);
        itemPkgSaveReqDTO.setIsSign(0);
        itemPkgSaveReqDTO.setDescription("description");
        final SkuSaveReqDTO skuSaveReqDTO = new SkuSaveReqDTO();
        skuSaveReqDTO.setItemGuid("itemGuid");
        skuSaveReqDTO.setSkuGuid("parentGuid");
        skuSaveReqDTO.setName("name");
        skuSaveReqDTO.setSalePrice(new BigDecimal("0.00"));
        skuSaveReqDTO.setMemberPrice(new BigDecimal("0.00"));
        skuSaveReqDTO.setUnit("unit");
        skuSaveReqDTO.setIsMemberDiscount(0);
        skuSaveReqDTO.setIsWholeDiscount(0);
        skuSaveReqDTO.setIsRack(0);
        skuSaveReqDTO.setIsJoinAio(0);
        skuSaveReqDTO.setIsJoinPos(0);
        skuSaveReqDTO.setIsJoinPad(0);
        skuSaveReqDTO.setMinOrderNum(new BigDecimal("0.00"));
        skuSaveReqDTO.setIsJoinBuffet(0);
        skuSaveReqDTO.setIsJoinWeChat(0);
        skuSaveReqDTO.setIsJoinMt(0);
        skuSaveReqDTO.setIsJoinElm(0);
        skuSaveReqDTO.setIsOpenStock(0);
        skuSaveReqDTO.setStoreGuid("storeGuid");
        skuSaveReqDTO.setBrandGuid("brandGuid");
        skuSaveReqDTO.setSkuFrom(0);
        skuSaveReqDTO.setIsJoinMiniAppMall(0);
        skuSaveReqDTO.setIsJoinMiniAppTakeaway(0);
        skuSaveReqDTO.setIsJoinStore(0);
        itemPkgSaveReqDTO.setSkuList(Arrays.asList(skuSaveReqDTO));
        final SubgroupReqDTO subgroupReqDTO = new SubgroupReqDTO();
        subgroupReqDTO.setSubgroupGuid("subgroupGuid");
        subgroupReqDTO.setName("name");
        subgroupReqDTO.setIsFixSubgroup(0);
        subgroupReqDTO.setPickNum(0);
        subgroupReqDTO.setSort(0);
        final SubItemSkuReqDTO subItemSkuReqDTO = new SubItemSkuReqDTO();
        subItemSkuReqDTO.setSkuGuid("skuGuid");
        subItemSkuReqDTO.setItemGuid("itemGuid");
        subItemSkuReqDTO.setItemNum(new BigDecimal("0.00"));
        subItemSkuReqDTO.setAddPrice(new BigDecimal("0.00"));
        subItemSkuReqDTO.setIsDefault(0);
        subItemSkuReqDTO.setIsRepeat(0);
        subgroupReqDTO.setSubItemSkuList(Arrays.asList(subItemSkuReqDTO));
        itemPkgSaveReqDTO.setSubgroupList(Arrays.asList(subgroupReqDTO));
        when(mockItemPkgService.updateItemPkg(itemPkgSaveReqDTO)).thenReturn(Arrays.asList("value"));

        when(mockIItemService.updateOriginItemStoreGuid("parentGuid", "storeGuid", "brandGuid")).thenReturn(0);

        // Configure IRedissonCacheService.getCache(...).
        final SyncItemDTO syncItemDTO1 = new SyncItemDTO();
        syncItemDTO1.setItemGuid("parentGuid");
        syncItemDTO1.setSkuGuid("skuGuid");
        syncItemDTO1.setSalePrice(new BigDecimal("0.00"));
        syncItemDTO1.setMemberPrice(new BigDecimal("0.00"));
        syncItemDTO1.setOriginalSalePrice(new BigDecimal("0.00"));
        syncItemDTO1.setOriginalMemberPrice(new BigDecimal("0.00"));
        syncItemDTO1.setIsRack(0);
        syncItemDTO1.setIsJoinAio(0);
        syncItemDTO1.setIsJoinPos(0);
        syncItemDTO1.setIsJoinPad(0);
        when(mockIRedissonCacheService.getCache("key")).thenReturn(syncItemDTO1);

        when(mockISkuService.updateOriginPriceStoreGuid("parentGuid", "storeGuid", "skuGuid", new BigDecimal("0.00"),
                new BigDecimal("0.00"))).thenReturn(0);
        when(mockIItemService.getGuidByStoreGuidAndParentGuid("storeGuid", "parentGuid")).thenReturn("newItemGuid");
        when(mockICommonService.md5("src")).thenReturn("key");

        // Run the test
        final String result = syncPricePlanToItemServiceImplUnderTest.addPricePlanPkgItem(itemDTO, "storeGuid",
                "brandGuid", "pricePlanGuid");

        // Verify the results
        assertThat(result).isEqualTo("newItemGuid");
        verify(mockIItemService).deleteByGuidAndFrom("guid", 0L, "storeGuid");
        verify(mockISkuService).deleteSkuByItemGuidAndStoreGuid("itemGuid", "storeGuid");

        // Confirm IRedissonCacheService.setCache(...).
        final SyncItemDTO value = new SyncItemDTO();
        value.setItemGuid("parentGuid");
        value.setSkuGuid("skuGuid");
        value.setSalePrice(new BigDecimal("0.00"));
        value.setMemberPrice(new BigDecimal("0.00"));
        value.setOriginalSalePrice(new BigDecimal("0.00"));
        value.setOriginalMemberPrice(new BigDecimal("0.00"));
        value.setIsRack(0);
        value.setIsJoinAio(0);
        value.setIsJoinPos(0);
        value.setIsJoinPad(0);
        verify(mockIRedissonCacheService).setCache("key", value, 20L, TimeUnit.MINUTES);
        verify(mockIRedissonCacheService).removeCache("key");
        verify(mockIrSkuSubgroupService).updateIsDelete("guid");
        verify(mockISkuService).list(any(QueryWrapper.class));

        // Confirm ItemHelper.distributeItems2Stores(...).
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setFrom(0);
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        verify(mockItemHelper).distributeItems2Stores(itemStringListDTO, Arrays.asList("value"), true);

        // Confirm ISkuService.updateTerminalStatus(...).
        final UpdateTerminalStatusDTO updateTerminalStatusDTO = new UpdateTerminalStatusDTO();
        updateTerminalStatusDTO.setStoreGuid("storeGuid");
        updateTerminalStatusDTO.setParentGuid("parentGuid");
        updateTerminalStatusDTO.setIsJoinAio(0);
        updateTerminalStatusDTO.setIsJoinPos(0);
        updateTerminalStatusDTO.setIsJoinPad(0);
        updateTerminalStatusDTO.setIsJoinMiniAppMall(0);
        updateTerminalStatusDTO.setIsJoinMiniAppTakeaway(0);
        updateTerminalStatusDTO.setIsJoinStore(0);
        verify(mockISkuService).updateTerminalStatus(updateTerminalStatusDTO);

        // Confirm ISubgroupService.updateById(...).
        final SubgroupDO entity = new SubgroupDO();
        entity.setId(0L);
        entity.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setGuid("9fbb2f86-9c26-492d-bb2e-2590fd13b1d4");
        entity.setItemGuid("newItemGuid");
        verify(mockISubgroupService).updateById(entity);
    }

    @Test
    public void testAddPricePlanPkgItem_IItemServiceGetItemInfoListByParentIdReturnsNoItems() {
        // Setup
        final SyncItemDTO itemDTO = new SyncItemDTO();
        itemDTO.setItemGuid("parentGuid");
        itemDTO.setSkuGuid("skuGuid");
        itemDTO.setSalePrice(new BigDecimal("0.00"));
        itemDTO.setMemberPrice(new BigDecimal("0.00"));
        itemDTO.setOriginalSalePrice(new BigDecimal("0.00"));
        itemDTO.setOriginalMemberPrice(new BigDecimal("0.00"));
        itemDTO.setIsRack(0);
        itemDTO.setIsJoinAio(0);
        itemDTO.setIsJoinPos(0);
        itemDTO.setIsJoinPad(0);

        when(mockIItemService.getItemInfoListByParentId("parentGuid", "storeGuid")).thenReturn(Collections.emptyList());

        // Configure IItemService.getItemInfo(...).
        final ItemInfoRespDTO itemInfoRespDTO = new ItemInfoRespDTO();
        itemInfoRespDTO.setItemGuid("itemGuid");
        itemInfoRespDTO.setBrandGuid("brandGuid");
        itemInfoRespDTO.setTypeGuid("typeGuid");
        itemInfoRespDTO.setItemType(0);
        itemInfoRespDTO.setHasAttr(0);
        itemInfoRespDTO.setName("name");
        itemInfoRespDTO.setPinyin("pinyin");
        itemInfoRespDTO.setSort(0);
        itemInfoRespDTO.setDescription("description");
        itemInfoRespDTO.setPictureUrl("pictureUrl");
        itemInfoRespDTO.setIsBestseller(0);
        itemInfoRespDTO.setIsNew(0);
        itemInfoRespDTO.setIsSign(0);
        itemInfoRespDTO.setUpCount(0);
        itemInfoRespDTO.setDownCount(0);
        final SkuInfoRespDTO skuInfoRespDTO = new SkuInfoRespDTO();
        skuInfoRespDTO.setItemGuid("itemGuid");
        skuInfoRespDTO.setSkuGuid("parentGuid");
        skuInfoRespDTO.setMemberPrice(new BigDecimal("0.00"));
        skuInfoRespDTO.setName("name");
        skuInfoRespDTO.setSalePrice(new BigDecimal("0.00"));
        skuInfoRespDTO.setIsWholeDiscount(0);
        skuInfoRespDTO.setIsMemberDiscount(0);
        skuInfoRespDTO.setMinOrderNum(new BigDecimal("0.00"));
        skuInfoRespDTO.setUnit("unit");
        skuInfoRespDTO.setIsRack(0);
        skuInfoRespDTO.setIsJoinBuffet(0);
        skuInfoRespDTO.setIsJoinWeChat(0);
        skuInfoRespDTO.setIsJoinMt(0);
        skuInfoRespDTO.setIsJoinElm(0);
        skuInfoRespDTO.setIsOpenStock(0);
        skuInfoRespDTO.setBrandGuid("brandGuid");
        skuInfoRespDTO.setIsJoinMiniAppMall(0);
        skuInfoRespDTO.setIsJoinMiniAppTakeaway(0);
        skuInfoRespDTO.setIsJoinStore(0);
        itemInfoRespDTO.setSkuList(Arrays.asList(skuInfoRespDTO));
        final AttrGroupWebRespDTO attrGroupWebRespDTO = new AttrGroupWebRespDTO();
        attrGroupWebRespDTO.setItemGuid("itemGuid");
        attrGroupWebRespDTO.setItemAttrGroupGuid("itemAttrGroupGuid");
        attrGroupWebRespDTO.setAttrGroupGuid("attrGroupGuid");
        attrGroupWebRespDTO.setIsRequired(0);
        attrGroupWebRespDTO.setIsMultiChoice(0);
        attrGroupWebRespDTO.setWithDefault(0);
        final AttrWebRespDTO attrWebRespDTO = new AttrWebRespDTO();
        attrWebRespDTO.setAttrItemAttrGroupGuid("attrItemAttrGroupGuid");
        attrWebRespDTO.setAttrGuid("attrGuid");
        attrWebRespDTO.setIsDefault(0);
        attrGroupWebRespDTO.setAttrList(Arrays.asList(attrWebRespDTO));
        itemInfoRespDTO.setAttrGroupList(Arrays.asList(attrGroupWebRespDTO));
        final SubgroupWebRespDTO subgroupWebRespDTO = new SubgroupWebRespDTO();
        subgroupWebRespDTO.setSubgroupGuid("subgroupGuid");
        subgroupWebRespDTO.setName("name");
        subgroupWebRespDTO.setIsFixSubgroup(0);
        subgroupWebRespDTO.setPickNum(0);
        subgroupWebRespDTO.setSort(0);
        final SubItemSkuWebRespDTO subItemSkuWebRespDTO = new SubItemSkuWebRespDTO();
        subItemSkuWebRespDTO.setSkuGuid("skuGuid");
        subItemSkuWebRespDTO.setItemGuid("itemGuid");
        subItemSkuWebRespDTO.setItemNum(new BigDecimal("0.00"));
        subItemSkuWebRespDTO.setAddPrice(new BigDecimal("0.00"));
        subItemSkuWebRespDTO.setIsDefault(0);
        subItemSkuWebRespDTO.setIsRepeat(0);
        subgroupWebRespDTO.setSubItemSkuList(Arrays.asList(subItemSkuWebRespDTO));
        itemInfoRespDTO.setSubgroupList(Arrays.asList(subgroupWebRespDTO));
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setFrom(0);
        itemSingleDTO.setData("parentGuid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        when(mockIItemService.getItemInfo(itemSingleDTO)).thenReturn(itemInfoRespDTO);

        // Configure IPricePlanItemService.getSyncItem(...).
        final SyncItemDTO syncItemDTO = new SyncItemDTO();
        syncItemDTO.setItemGuid("parentGuid");
        syncItemDTO.setSkuGuid("skuGuid");
        syncItemDTO.setSalePrice(new BigDecimal("0.00"));
        syncItemDTO.setMemberPrice(new BigDecimal("0.00"));
        syncItemDTO.setOriginalSalePrice(new BigDecimal("0.00"));
        syncItemDTO.setOriginalMemberPrice(new BigDecimal("0.00"));
        syncItemDTO.setIsRack(0);
        syncItemDTO.setIsJoinAio(0);
        syncItemDTO.setIsJoinPos(0);
        syncItemDTO.setIsJoinPad(0);
        when(mockIPricePlanItemService.getSyncItem("parentGuid", "parentGuid")).thenReturn(syncItemDTO);

        // Configure ISubgroupService.list(...).
        final SubgroupDO subgroupDO = new SubgroupDO();
        subgroupDO.setId(0L);
        subgroupDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        subgroupDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        subgroupDO.setGuid("9fbb2f86-9c26-492d-bb2e-2590fd13b1d4");
        subgroupDO.setItemGuid("newItemGuid");
        final List<SubgroupDO> subgroupDOS = Arrays.asList(subgroupDO);
        when(mockISubgroupService.list(any(QueryWrapper.class))).thenReturn(subgroupDOS);

        // Configure IRSkuSubgroupService.list(...).
        final RSkuSubgroupDO rSkuSubgroupDO = new RSkuSubgroupDO();
        rSkuSubgroupDO.setId(0L);
        rSkuSubgroupDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        rSkuSubgroupDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        rSkuSubgroupDO.setGuid("guid");
        rSkuSubgroupDO.setItemGuid("itemGuid");
        final List<RSkuSubgroupDO> rSkuSubgroupDOS = Arrays.asList(rSkuSubgroupDO);
        when(mockIrSkuSubgroupService.list(any(QueryWrapper.class))).thenReturn(rSkuSubgroupDOS);

        // Configure IItemPkgService.updateItemPkg(...).
        final ItemPkgSaveReqDTO itemPkgSaveReqDTO = new ItemPkgSaveReqDTO();
        itemPkgSaveReqDTO.setFrom(0);
        itemPkgSaveReqDTO.setDataList(Arrays.asList("value"));
        itemPkgSaveReqDTO.setItemGuid("parentGuid");
        itemPkgSaveReqDTO.setTypeGuid("typeGuid");
        itemPkgSaveReqDTO.setStoreGuid("storeGuid");
        itemPkgSaveReqDTO.setBrandGuid("brandGuid");
        itemPkgSaveReqDTO.setItemType(0);
        itemPkgSaveReqDTO.setName("name");
        itemPkgSaveReqDTO.setPinyin("pinyin");
        itemPkgSaveReqDTO.setSort(0);
        itemPkgSaveReqDTO.setPictureUrl("pictureUrl");
        itemPkgSaveReqDTO.setIsBestseller(0);
        itemPkgSaveReqDTO.setIsNew(0);
        itemPkgSaveReqDTO.setIsSign(0);
        itemPkgSaveReqDTO.setDescription("description");
        final SkuSaveReqDTO skuSaveReqDTO = new SkuSaveReqDTO();
        skuSaveReqDTO.setItemGuid("itemGuid");
        skuSaveReqDTO.setSkuGuid("parentGuid");
        skuSaveReqDTO.setName("name");
        skuSaveReqDTO.setSalePrice(new BigDecimal("0.00"));
        skuSaveReqDTO.setMemberPrice(new BigDecimal("0.00"));
        skuSaveReqDTO.setUnit("unit");
        skuSaveReqDTO.setIsMemberDiscount(0);
        skuSaveReqDTO.setIsWholeDiscount(0);
        skuSaveReqDTO.setIsRack(0);
        skuSaveReqDTO.setIsJoinAio(0);
        skuSaveReqDTO.setIsJoinPos(0);
        skuSaveReqDTO.setIsJoinPad(0);
        skuSaveReqDTO.setMinOrderNum(new BigDecimal("0.00"));
        skuSaveReqDTO.setIsJoinBuffet(0);
        skuSaveReqDTO.setIsJoinWeChat(0);
        skuSaveReqDTO.setIsJoinMt(0);
        skuSaveReqDTO.setIsJoinElm(0);
        skuSaveReqDTO.setIsOpenStock(0);
        skuSaveReqDTO.setStoreGuid("storeGuid");
        skuSaveReqDTO.setBrandGuid("brandGuid");
        skuSaveReqDTO.setSkuFrom(0);
        skuSaveReqDTO.setIsJoinMiniAppMall(0);
        skuSaveReqDTO.setIsJoinMiniAppTakeaway(0);
        skuSaveReqDTO.setIsJoinStore(0);
        itemPkgSaveReqDTO.setSkuList(Arrays.asList(skuSaveReqDTO));
        final SubgroupReqDTO subgroupReqDTO = new SubgroupReqDTO();
        subgroupReqDTO.setSubgroupGuid("subgroupGuid");
        subgroupReqDTO.setName("name");
        subgroupReqDTO.setIsFixSubgroup(0);
        subgroupReqDTO.setPickNum(0);
        subgroupReqDTO.setSort(0);
        final SubItemSkuReqDTO subItemSkuReqDTO = new SubItemSkuReqDTO();
        subItemSkuReqDTO.setSkuGuid("skuGuid");
        subItemSkuReqDTO.setItemGuid("itemGuid");
        subItemSkuReqDTO.setItemNum(new BigDecimal("0.00"));
        subItemSkuReqDTO.setAddPrice(new BigDecimal("0.00"));
        subItemSkuReqDTO.setIsDefault(0);
        subItemSkuReqDTO.setIsRepeat(0);
        subgroupReqDTO.setSubItemSkuList(Arrays.asList(subItemSkuReqDTO));
        itemPkgSaveReqDTO.setSubgroupList(Arrays.asList(subgroupReqDTO));
        when(mockItemPkgService.updateItemPkg(itemPkgSaveReqDTO)).thenReturn(Arrays.asList("value"));

        when(mockIItemService.updateOriginItemStoreGuid("parentGuid", "storeGuid", "brandGuid")).thenReturn(0);

        // Configure IRedissonCacheService.getCache(...).
        final SyncItemDTO syncItemDTO1 = new SyncItemDTO();
        syncItemDTO1.setItemGuid("parentGuid");
        syncItemDTO1.setSkuGuid("skuGuid");
        syncItemDTO1.setSalePrice(new BigDecimal("0.00"));
        syncItemDTO1.setMemberPrice(new BigDecimal("0.00"));
        syncItemDTO1.setOriginalSalePrice(new BigDecimal("0.00"));
        syncItemDTO1.setOriginalMemberPrice(new BigDecimal("0.00"));
        syncItemDTO1.setIsRack(0);
        syncItemDTO1.setIsJoinAio(0);
        syncItemDTO1.setIsJoinPos(0);
        syncItemDTO1.setIsJoinPad(0);
        when(mockIRedissonCacheService.getCache("key")).thenReturn(syncItemDTO1);

        when(mockISkuService.updateOriginPriceStoreGuid("parentGuid", "storeGuid", "skuGuid", new BigDecimal("0.00"),
                new BigDecimal("0.00"))).thenReturn(0);
        when(mockIItemService.getGuidByStoreGuidAndParentGuid("storeGuid", "parentGuid")).thenReturn("newItemGuid");
        when(mockICommonService.md5("src")).thenReturn("key");

        // Run the test
        final String result = syncPricePlanToItemServiceImplUnderTest.addPricePlanPkgItem(itemDTO, "storeGuid",
                "brandGuid", "pricePlanGuid");

        // Verify the results
        assertThat(result).isEqualTo("newItemGuid");

        // Confirm IRedissonCacheService.setCache(...).
        final SyncItemDTO value = new SyncItemDTO();
        value.setItemGuid("parentGuid");
        value.setSkuGuid("skuGuid");
        value.setSalePrice(new BigDecimal("0.00"));
        value.setMemberPrice(new BigDecimal("0.00"));
        value.setOriginalSalePrice(new BigDecimal("0.00"));
        value.setOriginalMemberPrice(new BigDecimal("0.00"));
        value.setIsRack(0);
        value.setIsJoinAio(0);
        value.setIsJoinPos(0);
        value.setIsJoinPad(0);
        verify(mockIRedissonCacheService).setCache("key", value, 20L, TimeUnit.MINUTES);
        verify(mockIRedissonCacheService).removeCache("key");
        verify(mockIrSkuSubgroupService).updateIsDelete("guid");
        verify(mockISkuService).list(any(QueryWrapper.class));

        // Confirm ItemHelper.distributeItems2Stores(...).
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setFrom(0);
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        verify(mockItemHelper).distributeItems2Stores(itemStringListDTO, Arrays.asList("value"), true);

        // Confirm ISkuService.updateTerminalStatus(...).
        final UpdateTerminalStatusDTO updateTerminalStatusDTO = new UpdateTerminalStatusDTO();
        updateTerminalStatusDTO.setStoreGuid("storeGuid");
        updateTerminalStatusDTO.setParentGuid("parentGuid");
        updateTerminalStatusDTO.setIsJoinAio(0);
        updateTerminalStatusDTO.setIsJoinPos(0);
        updateTerminalStatusDTO.setIsJoinPad(0);
        updateTerminalStatusDTO.setIsJoinMiniAppMall(0);
        updateTerminalStatusDTO.setIsJoinMiniAppTakeaway(0);
        updateTerminalStatusDTO.setIsJoinStore(0);
        verify(mockISkuService).updateTerminalStatus(updateTerminalStatusDTO);

        // Confirm ISubgroupService.updateById(...).
        final SubgroupDO entity = new SubgroupDO();
        entity.setId(0L);
        entity.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setGuid("9fbb2f86-9c26-492d-bb2e-2590fd13b1d4");
        entity.setItemGuid("newItemGuid");
        verify(mockISubgroupService).updateById(entity);
    }

    @Test
    public void testAddPricePlanPkgItem_ISubgroupServiceListReturnsNoItems() {
        // Setup
        final SyncItemDTO itemDTO = new SyncItemDTO();
        itemDTO.setItemGuid("parentGuid");
        itemDTO.setSkuGuid("skuGuid");
        itemDTO.setSalePrice(new BigDecimal("0.00"));
        itemDTO.setMemberPrice(new BigDecimal("0.00"));
        itemDTO.setOriginalSalePrice(new BigDecimal("0.00"));
        itemDTO.setOriginalMemberPrice(new BigDecimal("0.00"));
        itemDTO.setIsRack(0);
        itemDTO.setIsJoinAio(0);
        itemDTO.setIsJoinPos(0);
        itemDTO.setIsJoinPad(0);

        when(mockIItemService.getItemInfoListByParentId("parentGuid", "storeGuid"))
                .thenReturn(Arrays.asList(new HashMap<>()));

        // Configure IItemService.getItemInfo(...).
        final ItemInfoRespDTO itemInfoRespDTO = new ItemInfoRespDTO();
        itemInfoRespDTO.setItemGuid("itemGuid");
        itemInfoRespDTO.setBrandGuid("brandGuid");
        itemInfoRespDTO.setTypeGuid("typeGuid");
        itemInfoRespDTO.setItemType(0);
        itemInfoRespDTO.setHasAttr(0);
        itemInfoRespDTO.setName("name");
        itemInfoRespDTO.setPinyin("pinyin");
        itemInfoRespDTO.setSort(0);
        itemInfoRespDTO.setDescription("description");
        itemInfoRespDTO.setPictureUrl("pictureUrl");
        itemInfoRespDTO.setIsBestseller(0);
        itemInfoRespDTO.setIsNew(0);
        itemInfoRespDTO.setIsSign(0);
        itemInfoRespDTO.setUpCount(0);
        itemInfoRespDTO.setDownCount(0);
        final SkuInfoRespDTO skuInfoRespDTO = new SkuInfoRespDTO();
        skuInfoRespDTO.setItemGuid("itemGuid");
        skuInfoRespDTO.setSkuGuid("parentGuid");
        skuInfoRespDTO.setMemberPrice(new BigDecimal("0.00"));
        skuInfoRespDTO.setName("name");
        skuInfoRespDTO.setSalePrice(new BigDecimal("0.00"));
        skuInfoRespDTO.setIsWholeDiscount(0);
        skuInfoRespDTO.setIsMemberDiscount(0);
        skuInfoRespDTO.setMinOrderNum(new BigDecimal("0.00"));
        skuInfoRespDTO.setUnit("unit");
        skuInfoRespDTO.setIsRack(0);
        skuInfoRespDTO.setIsJoinBuffet(0);
        skuInfoRespDTO.setIsJoinWeChat(0);
        skuInfoRespDTO.setIsJoinMt(0);
        skuInfoRespDTO.setIsJoinElm(0);
        skuInfoRespDTO.setIsOpenStock(0);
        skuInfoRespDTO.setBrandGuid("brandGuid");
        skuInfoRespDTO.setIsJoinMiniAppMall(0);
        skuInfoRespDTO.setIsJoinMiniAppTakeaway(0);
        skuInfoRespDTO.setIsJoinStore(0);
        itemInfoRespDTO.setSkuList(Arrays.asList(skuInfoRespDTO));
        final AttrGroupWebRespDTO attrGroupWebRespDTO = new AttrGroupWebRespDTO();
        attrGroupWebRespDTO.setItemGuid("itemGuid");
        attrGroupWebRespDTO.setItemAttrGroupGuid("itemAttrGroupGuid");
        attrGroupWebRespDTO.setAttrGroupGuid("attrGroupGuid");
        attrGroupWebRespDTO.setIsRequired(0);
        attrGroupWebRespDTO.setIsMultiChoice(0);
        attrGroupWebRespDTO.setWithDefault(0);
        final AttrWebRespDTO attrWebRespDTO = new AttrWebRespDTO();
        attrWebRespDTO.setAttrItemAttrGroupGuid("attrItemAttrGroupGuid");
        attrWebRespDTO.setAttrGuid("attrGuid");
        attrWebRespDTO.setIsDefault(0);
        attrGroupWebRespDTO.setAttrList(Arrays.asList(attrWebRespDTO));
        itemInfoRespDTO.setAttrGroupList(Arrays.asList(attrGroupWebRespDTO));
        final SubgroupWebRespDTO subgroupWebRespDTO = new SubgroupWebRespDTO();
        subgroupWebRespDTO.setSubgroupGuid("subgroupGuid");
        subgroupWebRespDTO.setName("name");
        subgroupWebRespDTO.setIsFixSubgroup(0);
        subgroupWebRespDTO.setPickNum(0);
        subgroupWebRespDTO.setSort(0);
        final SubItemSkuWebRespDTO subItemSkuWebRespDTO = new SubItemSkuWebRespDTO();
        subItemSkuWebRespDTO.setSkuGuid("skuGuid");
        subItemSkuWebRespDTO.setItemGuid("itemGuid");
        subItemSkuWebRespDTO.setItemNum(new BigDecimal("0.00"));
        subItemSkuWebRespDTO.setAddPrice(new BigDecimal("0.00"));
        subItemSkuWebRespDTO.setIsDefault(0);
        subItemSkuWebRespDTO.setIsRepeat(0);
        subgroupWebRespDTO.setSubItemSkuList(Arrays.asList(subItemSkuWebRespDTO));
        itemInfoRespDTO.setSubgroupList(Arrays.asList(subgroupWebRespDTO));
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setFrom(0);
        itemSingleDTO.setData("parentGuid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        when(mockIItemService.getItemInfo(itemSingleDTO)).thenReturn(itemInfoRespDTO);

        // Configure IPricePlanItemService.getSyncItem(...).
        final SyncItemDTO syncItemDTO = new SyncItemDTO();
        syncItemDTO.setItemGuid("parentGuid");
        syncItemDTO.setSkuGuid("skuGuid");
        syncItemDTO.setSalePrice(new BigDecimal("0.00"));
        syncItemDTO.setMemberPrice(new BigDecimal("0.00"));
        syncItemDTO.setOriginalSalePrice(new BigDecimal("0.00"));
        syncItemDTO.setOriginalMemberPrice(new BigDecimal("0.00"));
        syncItemDTO.setIsRack(0);
        syncItemDTO.setIsJoinAio(0);
        syncItemDTO.setIsJoinPos(0);
        syncItemDTO.setIsJoinPad(0);
        when(mockIPricePlanItemService.getSyncItem("parentGuid", "parentGuid")).thenReturn(syncItemDTO);

        when(mockISubgroupService.list(any(QueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure IItemPkgService.updateItemPkg(...).
        final ItemPkgSaveReqDTO itemPkgSaveReqDTO = new ItemPkgSaveReqDTO();
        itemPkgSaveReqDTO.setFrom(0);
        itemPkgSaveReqDTO.setDataList(Arrays.asList("value"));
        itemPkgSaveReqDTO.setItemGuid("parentGuid");
        itemPkgSaveReqDTO.setTypeGuid("typeGuid");
        itemPkgSaveReqDTO.setStoreGuid("storeGuid");
        itemPkgSaveReqDTO.setBrandGuid("brandGuid");
        itemPkgSaveReqDTO.setItemType(0);
        itemPkgSaveReqDTO.setName("name");
        itemPkgSaveReqDTO.setPinyin("pinyin");
        itemPkgSaveReqDTO.setSort(0);
        itemPkgSaveReqDTO.setPictureUrl("pictureUrl");
        itemPkgSaveReqDTO.setIsBestseller(0);
        itemPkgSaveReqDTO.setIsNew(0);
        itemPkgSaveReqDTO.setIsSign(0);
        itemPkgSaveReqDTO.setDescription("description");
        final SkuSaveReqDTO skuSaveReqDTO = new SkuSaveReqDTO();
        skuSaveReqDTO.setItemGuid("itemGuid");
        skuSaveReqDTO.setSkuGuid("parentGuid");
        skuSaveReqDTO.setName("name");
        skuSaveReqDTO.setSalePrice(new BigDecimal("0.00"));
        skuSaveReqDTO.setMemberPrice(new BigDecimal("0.00"));
        skuSaveReqDTO.setUnit("unit");
        skuSaveReqDTO.setIsMemberDiscount(0);
        skuSaveReqDTO.setIsWholeDiscount(0);
        skuSaveReqDTO.setIsRack(0);
        skuSaveReqDTO.setIsJoinAio(0);
        skuSaveReqDTO.setIsJoinPos(0);
        skuSaveReqDTO.setIsJoinPad(0);
        skuSaveReqDTO.setMinOrderNum(new BigDecimal("0.00"));
        skuSaveReqDTO.setIsJoinBuffet(0);
        skuSaveReqDTO.setIsJoinWeChat(0);
        skuSaveReqDTO.setIsJoinMt(0);
        skuSaveReqDTO.setIsJoinElm(0);
        skuSaveReqDTO.setIsOpenStock(0);
        skuSaveReqDTO.setStoreGuid("storeGuid");
        skuSaveReqDTO.setBrandGuid("brandGuid");
        skuSaveReqDTO.setSkuFrom(0);
        skuSaveReqDTO.setIsJoinMiniAppMall(0);
        skuSaveReqDTO.setIsJoinMiniAppTakeaway(0);
        skuSaveReqDTO.setIsJoinStore(0);
        itemPkgSaveReqDTO.setSkuList(Arrays.asList(skuSaveReqDTO));
        final SubgroupReqDTO subgroupReqDTO = new SubgroupReqDTO();
        subgroupReqDTO.setSubgroupGuid("subgroupGuid");
        subgroupReqDTO.setName("name");
        subgroupReqDTO.setIsFixSubgroup(0);
        subgroupReqDTO.setPickNum(0);
        subgroupReqDTO.setSort(0);
        final SubItemSkuReqDTO subItemSkuReqDTO = new SubItemSkuReqDTO();
        subItemSkuReqDTO.setSkuGuid("skuGuid");
        subItemSkuReqDTO.setItemGuid("itemGuid");
        subItemSkuReqDTO.setItemNum(new BigDecimal("0.00"));
        subItemSkuReqDTO.setAddPrice(new BigDecimal("0.00"));
        subItemSkuReqDTO.setIsDefault(0);
        subItemSkuReqDTO.setIsRepeat(0);
        subgroupReqDTO.setSubItemSkuList(Arrays.asList(subItemSkuReqDTO));
        itemPkgSaveReqDTO.setSubgroupList(Arrays.asList(subgroupReqDTO));
        when(mockItemPkgService.updateItemPkg(itemPkgSaveReqDTO)).thenReturn(Arrays.asList("value"));

        when(mockIItemService.updateOriginItemStoreGuid("parentGuid", "storeGuid", "brandGuid")).thenReturn(0);

        // Configure IRedissonCacheService.getCache(...).
        final SyncItemDTO syncItemDTO1 = new SyncItemDTO();
        syncItemDTO1.setItemGuid("parentGuid");
        syncItemDTO1.setSkuGuid("skuGuid");
        syncItemDTO1.setSalePrice(new BigDecimal("0.00"));
        syncItemDTO1.setMemberPrice(new BigDecimal("0.00"));
        syncItemDTO1.setOriginalSalePrice(new BigDecimal("0.00"));
        syncItemDTO1.setOriginalMemberPrice(new BigDecimal("0.00"));
        syncItemDTO1.setIsRack(0);
        syncItemDTO1.setIsJoinAio(0);
        syncItemDTO1.setIsJoinPos(0);
        syncItemDTO1.setIsJoinPad(0);
        when(mockIRedissonCacheService.getCache("key")).thenReturn(syncItemDTO1);

        when(mockISkuService.updateOriginPriceStoreGuid("parentGuid", "storeGuid", "skuGuid", new BigDecimal("0.00"),
                new BigDecimal("0.00"))).thenReturn(0);
        when(mockIItemService.getGuidByStoreGuidAndParentGuid("storeGuid", "parentGuid")).thenReturn("newItemGuid");
        when(mockICommonService.md5("src")).thenReturn("key");

        // Run the test
        final String result = syncPricePlanToItemServiceImplUnderTest.addPricePlanPkgItem(itemDTO, "storeGuid",
                "brandGuid", "pricePlanGuid");

        // Verify the results
        assertThat(result).isEqualTo("newItemGuid");
        verify(mockIItemService).deleteByGuidAndFrom("guid", 0L, "storeGuid");
        verify(mockISkuService).deleteSkuByItemGuidAndStoreGuid("itemGuid", "storeGuid");

        // Confirm IRedissonCacheService.setCache(...).
        final SyncItemDTO value = new SyncItemDTO();
        value.setItemGuid("parentGuid");
        value.setSkuGuid("skuGuid");
        value.setSalePrice(new BigDecimal("0.00"));
        value.setMemberPrice(new BigDecimal("0.00"));
        value.setOriginalSalePrice(new BigDecimal("0.00"));
        value.setOriginalMemberPrice(new BigDecimal("0.00"));
        value.setIsRack(0);
        value.setIsJoinAio(0);
        value.setIsJoinPos(0);
        value.setIsJoinPad(0);
        verify(mockIRedissonCacheService).setCache("key", value, 20L, TimeUnit.MINUTES);
        verify(mockIRedissonCacheService).removeCache("key");

        // Confirm ItemHelper.distributeItems2Stores(...).
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setFrom(0);
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        verify(mockItemHelper).distributeItems2Stores(itemStringListDTO, Arrays.asList("value"), true);

        // Confirm ISkuService.updateTerminalStatus(...).
        final UpdateTerminalStatusDTO updateTerminalStatusDTO = new UpdateTerminalStatusDTO();
        updateTerminalStatusDTO.setStoreGuid("storeGuid");
        updateTerminalStatusDTO.setParentGuid("parentGuid");
        updateTerminalStatusDTO.setIsJoinAio(0);
        updateTerminalStatusDTO.setIsJoinPos(0);
        updateTerminalStatusDTO.setIsJoinPad(0);
        updateTerminalStatusDTO.setIsJoinMiniAppMall(0);
        updateTerminalStatusDTO.setIsJoinMiniAppTakeaway(0);
        updateTerminalStatusDTO.setIsJoinStore(0);
        verify(mockISkuService).updateTerminalStatus(updateTerminalStatusDTO);

        // Confirm ISubgroupService.updateById(...).
        final SubgroupDO entity = new SubgroupDO();
        entity.setId(0L);
        entity.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setGuid("9fbb2f86-9c26-492d-bb2e-2590fd13b1d4");
        entity.setItemGuid("newItemGuid");
        verify(mockISubgroupService).updateById(entity);
    }

    @Test
    public void testAddPricePlanPkgItem_IRSkuSubgroupServiceListReturnsNoItems() {
        // Setup
        final SyncItemDTO itemDTO = new SyncItemDTO();
        itemDTO.setItemGuid("parentGuid");
        itemDTO.setSkuGuid("skuGuid");
        itemDTO.setSalePrice(new BigDecimal("0.00"));
        itemDTO.setMemberPrice(new BigDecimal("0.00"));
        itemDTO.setOriginalSalePrice(new BigDecimal("0.00"));
        itemDTO.setOriginalMemberPrice(new BigDecimal("0.00"));
        itemDTO.setIsRack(0);
        itemDTO.setIsJoinAio(0);
        itemDTO.setIsJoinPos(0);
        itemDTO.setIsJoinPad(0);

        when(mockIItemService.getItemInfoListByParentId("parentGuid", "storeGuid"))
                .thenReturn(Arrays.asList(new HashMap<>()));

        // Configure IItemService.getItemInfo(...).
        final ItemInfoRespDTO itemInfoRespDTO = new ItemInfoRespDTO();
        itemInfoRespDTO.setItemGuid("itemGuid");
        itemInfoRespDTO.setBrandGuid("brandGuid");
        itemInfoRespDTO.setTypeGuid("typeGuid");
        itemInfoRespDTO.setItemType(0);
        itemInfoRespDTO.setHasAttr(0);
        itemInfoRespDTO.setName("name");
        itemInfoRespDTO.setPinyin("pinyin");
        itemInfoRespDTO.setSort(0);
        itemInfoRespDTO.setDescription("description");
        itemInfoRespDTO.setPictureUrl("pictureUrl");
        itemInfoRespDTO.setIsBestseller(0);
        itemInfoRespDTO.setIsNew(0);
        itemInfoRespDTO.setIsSign(0);
        itemInfoRespDTO.setUpCount(0);
        itemInfoRespDTO.setDownCount(0);
        final SkuInfoRespDTO skuInfoRespDTO = new SkuInfoRespDTO();
        skuInfoRespDTO.setItemGuid("itemGuid");
        skuInfoRespDTO.setSkuGuid("parentGuid");
        skuInfoRespDTO.setMemberPrice(new BigDecimal("0.00"));
        skuInfoRespDTO.setName("name");
        skuInfoRespDTO.setSalePrice(new BigDecimal("0.00"));
        skuInfoRespDTO.setIsWholeDiscount(0);
        skuInfoRespDTO.setIsMemberDiscount(0);
        skuInfoRespDTO.setMinOrderNum(new BigDecimal("0.00"));
        skuInfoRespDTO.setUnit("unit");
        skuInfoRespDTO.setIsRack(0);
        skuInfoRespDTO.setIsJoinBuffet(0);
        skuInfoRespDTO.setIsJoinWeChat(0);
        skuInfoRespDTO.setIsJoinMt(0);
        skuInfoRespDTO.setIsJoinElm(0);
        skuInfoRespDTO.setIsOpenStock(0);
        skuInfoRespDTO.setBrandGuid("brandGuid");
        skuInfoRespDTO.setIsJoinMiniAppMall(0);
        skuInfoRespDTO.setIsJoinMiniAppTakeaway(0);
        skuInfoRespDTO.setIsJoinStore(0);
        itemInfoRespDTO.setSkuList(Arrays.asList(skuInfoRespDTO));
        final AttrGroupWebRespDTO attrGroupWebRespDTO = new AttrGroupWebRespDTO();
        attrGroupWebRespDTO.setItemGuid("itemGuid");
        attrGroupWebRespDTO.setItemAttrGroupGuid("itemAttrGroupGuid");
        attrGroupWebRespDTO.setAttrGroupGuid("attrGroupGuid");
        attrGroupWebRespDTO.setIsRequired(0);
        attrGroupWebRespDTO.setIsMultiChoice(0);
        attrGroupWebRespDTO.setWithDefault(0);
        final AttrWebRespDTO attrWebRespDTO = new AttrWebRespDTO();
        attrWebRespDTO.setAttrItemAttrGroupGuid("attrItemAttrGroupGuid");
        attrWebRespDTO.setAttrGuid("attrGuid");
        attrWebRespDTO.setIsDefault(0);
        attrGroupWebRespDTO.setAttrList(Arrays.asList(attrWebRespDTO));
        itemInfoRespDTO.setAttrGroupList(Arrays.asList(attrGroupWebRespDTO));
        final SubgroupWebRespDTO subgroupWebRespDTO = new SubgroupWebRespDTO();
        subgroupWebRespDTO.setSubgroupGuid("subgroupGuid");
        subgroupWebRespDTO.setName("name");
        subgroupWebRespDTO.setIsFixSubgroup(0);
        subgroupWebRespDTO.setPickNum(0);
        subgroupWebRespDTO.setSort(0);
        final SubItemSkuWebRespDTO subItemSkuWebRespDTO = new SubItemSkuWebRespDTO();
        subItemSkuWebRespDTO.setSkuGuid("skuGuid");
        subItemSkuWebRespDTO.setItemGuid("itemGuid");
        subItemSkuWebRespDTO.setItemNum(new BigDecimal("0.00"));
        subItemSkuWebRespDTO.setAddPrice(new BigDecimal("0.00"));
        subItemSkuWebRespDTO.setIsDefault(0);
        subItemSkuWebRespDTO.setIsRepeat(0);
        subgroupWebRespDTO.setSubItemSkuList(Arrays.asList(subItemSkuWebRespDTO));
        itemInfoRespDTO.setSubgroupList(Arrays.asList(subgroupWebRespDTO));
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setFrom(0);
        itemSingleDTO.setData("parentGuid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        when(mockIItemService.getItemInfo(itemSingleDTO)).thenReturn(itemInfoRespDTO);

        // Configure IPricePlanItemService.getSyncItem(...).
        final SyncItemDTO syncItemDTO = new SyncItemDTO();
        syncItemDTO.setItemGuid("parentGuid");
        syncItemDTO.setSkuGuid("skuGuid");
        syncItemDTO.setSalePrice(new BigDecimal("0.00"));
        syncItemDTO.setMemberPrice(new BigDecimal("0.00"));
        syncItemDTO.setOriginalSalePrice(new BigDecimal("0.00"));
        syncItemDTO.setOriginalMemberPrice(new BigDecimal("0.00"));
        syncItemDTO.setIsRack(0);
        syncItemDTO.setIsJoinAio(0);
        syncItemDTO.setIsJoinPos(0);
        syncItemDTO.setIsJoinPad(0);
        when(mockIPricePlanItemService.getSyncItem("parentGuid", "parentGuid")).thenReturn(syncItemDTO);

        // Configure ISubgroupService.list(...).
        final SubgroupDO subgroupDO = new SubgroupDO();
        subgroupDO.setId(0L);
        subgroupDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        subgroupDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        subgroupDO.setGuid("9fbb2f86-9c26-492d-bb2e-2590fd13b1d4");
        subgroupDO.setItemGuid("newItemGuid");
        final List<SubgroupDO> subgroupDOS = Arrays.asList(subgroupDO);
        when(mockISubgroupService.list(any(QueryWrapper.class))).thenReturn(subgroupDOS);

        when(mockIrSkuSubgroupService.list(any(QueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure IItemPkgService.updateItemPkg(...).
        final ItemPkgSaveReqDTO itemPkgSaveReqDTO = new ItemPkgSaveReqDTO();
        itemPkgSaveReqDTO.setFrom(0);
        itemPkgSaveReqDTO.setDataList(Arrays.asList("value"));
        itemPkgSaveReqDTO.setItemGuid("parentGuid");
        itemPkgSaveReqDTO.setTypeGuid("typeGuid");
        itemPkgSaveReqDTO.setStoreGuid("storeGuid");
        itemPkgSaveReqDTO.setBrandGuid("brandGuid");
        itemPkgSaveReqDTO.setItemType(0);
        itemPkgSaveReqDTO.setName("name");
        itemPkgSaveReqDTO.setPinyin("pinyin");
        itemPkgSaveReqDTO.setSort(0);
        itemPkgSaveReqDTO.setPictureUrl("pictureUrl");
        itemPkgSaveReqDTO.setIsBestseller(0);
        itemPkgSaveReqDTO.setIsNew(0);
        itemPkgSaveReqDTO.setIsSign(0);
        itemPkgSaveReqDTO.setDescription("description");
        final SkuSaveReqDTO skuSaveReqDTO = new SkuSaveReqDTO();
        skuSaveReqDTO.setItemGuid("itemGuid");
        skuSaveReqDTO.setSkuGuid("parentGuid");
        skuSaveReqDTO.setName("name");
        skuSaveReqDTO.setSalePrice(new BigDecimal("0.00"));
        skuSaveReqDTO.setMemberPrice(new BigDecimal("0.00"));
        skuSaveReqDTO.setUnit("unit");
        skuSaveReqDTO.setIsMemberDiscount(0);
        skuSaveReqDTO.setIsWholeDiscount(0);
        skuSaveReqDTO.setIsRack(0);
        skuSaveReqDTO.setIsJoinAio(0);
        skuSaveReqDTO.setIsJoinPos(0);
        skuSaveReqDTO.setIsJoinPad(0);
        skuSaveReqDTO.setMinOrderNum(new BigDecimal("0.00"));
        skuSaveReqDTO.setIsJoinBuffet(0);
        skuSaveReqDTO.setIsJoinWeChat(0);
        skuSaveReqDTO.setIsJoinMt(0);
        skuSaveReqDTO.setIsJoinElm(0);
        skuSaveReqDTO.setIsOpenStock(0);
        skuSaveReqDTO.setStoreGuid("storeGuid");
        skuSaveReqDTO.setBrandGuid("brandGuid");
        skuSaveReqDTO.setSkuFrom(0);
        skuSaveReqDTO.setIsJoinMiniAppMall(0);
        skuSaveReqDTO.setIsJoinMiniAppTakeaway(0);
        skuSaveReqDTO.setIsJoinStore(0);
        itemPkgSaveReqDTO.setSkuList(Arrays.asList(skuSaveReqDTO));
        final SubgroupReqDTO subgroupReqDTO = new SubgroupReqDTO();
        subgroupReqDTO.setSubgroupGuid("subgroupGuid");
        subgroupReqDTO.setName("name");
        subgroupReqDTO.setIsFixSubgroup(0);
        subgroupReqDTO.setPickNum(0);
        subgroupReqDTO.setSort(0);
        final SubItemSkuReqDTO subItemSkuReqDTO = new SubItemSkuReqDTO();
        subItemSkuReqDTO.setSkuGuid("skuGuid");
        subItemSkuReqDTO.setItemGuid("itemGuid");
        subItemSkuReqDTO.setItemNum(new BigDecimal("0.00"));
        subItemSkuReqDTO.setAddPrice(new BigDecimal("0.00"));
        subItemSkuReqDTO.setIsDefault(0);
        subItemSkuReqDTO.setIsRepeat(0);
        subgroupReqDTO.setSubItemSkuList(Arrays.asList(subItemSkuReqDTO));
        itemPkgSaveReqDTO.setSubgroupList(Arrays.asList(subgroupReqDTO));
        when(mockItemPkgService.updateItemPkg(itemPkgSaveReqDTO)).thenReturn(Arrays.asList("value"));

        when(mockIItemService.updateOriginItemStoreGuid("parentGuid", "storeGuid", "brandGuid")).thenReturn(0);

        // Configure IRedissonCacheService.getCache(...).
        final SyncItemDTO syncItemDTO1 = new SyncItemDTO();
        syncItemDTO1.setItemGuid("parentGuid");
        syncItemDTO1.setSkuGuid("skuGuid");
        syncItemDTO1.setSalePrice(new BigDecimal("0.00"));
        syncItemDTO1.setMemberPrice(new BigDecimal("0.00"));
        syncItemDTO1.setOriginalSalePrice(new BigDecimal("0.00"));
        syncItemDTO1.setOriginalMemberPrice(new BigDecimal("0.00"));
        syncItemDTO1.setIsRack(0);
        syncItemDTO1.setIsJoinAio(0);
        syncItemDTO1.setIsJoinPos(0);
        syncItemDTO1.setIsJoinPad(0);
        when(mockIRedissonCacheService.getCache("key")).thenReturn(syncItemDTO1);

        when(mockISkuService.updateOriginPriceStoreGuid("parentGuid", "storeGuid", "skuGuid", new BigDecimal("0.00"),
                new BigDecimal("0.00"))).thenReturn(0);
        when(mockIItemService.getGuidByStoreGuidAndParentGuid("storeGuid", "parentGuid")).thenReturn("newItemGuid");
        when(mockICommonService.md5("src")).thenReturn("key");

        // Run the test
        final String result = syncPricePlanToItemServiceImplUnderTest.addPricePlanPkgItem(itemDTO, "storeGuid",
                "brandGuid", "pricePlanGuid");

        // Verify the results
        assertThat(result).isEqualTo("newItemGuid");
        verify(mockIItemService).deleteByGuidAndFrom("guid", 0L, "storeGuid");
        verify(mockISkuService).deleteSkuByItemGuidAndStoreGuid("itemGuid", "storeGuid");

        // Confirm IRedissonCacheService.setCache(...).
        final SyncItemDTO value = new SyncItemDTO();
        value.setItemGuid("parentGuid");
        value.setSkuGuid("skuGuid");
        value.setSalePrice(new BigDecimal("0.00"));
        value.setMemberPrice(new BigDecimal("0.00"));
        value.setOriginalSalePrice(new BigDecimal("0.00"));
        value.setOriginalMemberPrice(new BigDecimal("0.00"));
        value.setIsRack(0);
        value.setIsJoinAio(0);
        value.setIsJoinPos(0);
        value.setIsJoinPad(0);
        verify(mockIRedissonCacheService).setCache("key", value, 20L, TimeUnit.MINUTES);
        verify(mockIRedissonCacheService).removeCache("key");

        // Confirm ItemHelper.distributeItems2Stores(...).
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setFrom(0);
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        verify(mockItemHelper).distributeItems2Stores(itemStringListDTO, Arrays.asList("value"), true);

        // Confirm ISkuService.updateTerminalStatus(...).
        final UpdateTerminalStatusDTO updateTerminalStatusDTO = new UpdateTerminalStatusDTO();
        updateTerminalStatusDTO.setStoreGuid("storeGuid");
        updateTerminalStatusDTO.setParentGuid("parentGuid");
        updateTerminalStatusDTO.setIsJoinAio(0);
        updateTerminalStatusDTO.setIsJoinPos(0);
        updateTerminalStatusDTO.setIsJoinPad(0);
        updateTerminalStatusDTO.setIsJoinMiniAppMall(0);
        updateTerminalStatusDTO.setIsJoinMiniAppTakeaway(0);
        updateTerminalStatusDTO.setIsJoinStore(0);
        verify(mockISkuService).updateTerminalStatus(updateTerminalStatusDTO);

        // Confirm ISubgroupService.updateById(...).
        final SubgroupDO entity = new SubgroupDO();
        entity.setId(0L);
        entity.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setGuid("9fbb2f86-9c26-492d-bb2e-2590fd13b1d4");
        entity.setItemGuid("newItemGuid");
        verify(mockISubgroupService).updateById(entity);
    }

    @Test
    public void testAddPricePlanPkgItem_IItemPkgServiceReturnsNoItems() {
        // Setup
        final SyncItemDTO itemDTO = new SyncItemDTO();
        itemDTO.setItemGuid("parentGuid");
        itemDTO.setSkuGuid("skuGuid");
        itemDTO.setSalePrice(new BigDecimal("0.00"));
        itemDTO.setMemberPrice(new BigDecimal("0.00"));
        itemDTO.setOriginalSalePrice(new BigDecimal("0.00"));
        itemDTO.setOriginalMemberPrice(new BigDecimal("0.00"));
        itemDTO.setIsRack(0);
        itemDTO.setIsJoinAio(0);
        itemDTO.setIsJoinPos(0);
        itemDTO.setIsJoinPad(0);

        when(mockIItemService.getItemInfoListByParentId("parentGuid", "storeGuid"))
                .thenReturn(Arrays.asList(new HashMap<>()));

        // Configure IItemService.getItemInfo(...).
        final ItemInfoRespDTO itemInfoRespDTO = new ItemInfoRespDTO();
        itemInfoRespDTO.setItemGuid("itemGuid");
        itemInfoRespDTO.setBrandGuid("brandGuid");
        itemInfoRespDTO.setTypeGuid("typeGuid");
        itemInfoRespDTO.setItemType(0);
        itemInfoRespDTO.setHasAttr(0);
        itemInfoRespDTO.setName("name");
        itemInfoRespDTO.setPinyin("pinyin");
        itemInfoRespDTO.setSort(0);
        itemInfoRespDTO.setDescription("description");
        itemInfoRespDTO.setPictureUrl("pictureUrl");
        itemInfoRespDTO.setIsBestseller(0);
        itemInfoRespDTO.setIsNew(0);
        itemInfoRespDTO.setIsSign(0);
        itemInfoRespDTO.setUpCount(0);
        itemInfoRespDTO.setDownCount(0);
        final SkuInfoRespDTO skuInfoRespDTO = new SkuInfoRespDTO();
        skuInfoRespDTO.setItemGuid("itemGuid");
        skuInfoRespDTO.setSkuGuid("parentGuid");
        skuInfoRespDTO.setMemberPrice(new BigDecimal("0.00"));
        skuInfoRespDTO.setName("name");
        skuInfoRespDTO.setSalePrice(new BigDecimal("0.00"));
        skuInfoRespDTO.setIsWholeDiscount(0);
        skuInfoRespDTO.setIsMemberDiscount(0);
        skuInfoRespDTO.setMinOrderNum(new BigDecimal("0.00"));
        skuInfoRespDTO.setUnit("unit");
        skuInfoRespDTO.setIsRack(0);
        skuInfoRespDTO.setIsJoinBuffet(0);
        skuInfoRespDTO.setIsJoinWeChat(0);
        skuInfoRespDTO.setIsJoinMt(0);
        skuInfoRespDTO.setIsJoinElm(0);
        skuInfoRespDTO.setIsOpenStock(0);
        skuInfoRespDTO.setBrandGuid("brandGuid");
        skuInfoRespDTO.setIsJoinMiniAppMall(0);
        skuInfoRespDTO.setIsJoinMiniAppTakeaway(0);
        skuInfoRespDTO.setIsJoinStore(0);
        itemInfoRespDTO.setSkuList(Arrays.asList(skuInfoRespDTO));
        final AttrGroupWebRespDTO attrGroupWebRespDTO = new AttrGroupWebRespDTO();
        attrGroupWebRespDTO.setItemGuid("itemGuid");
        attrGroupWebRespDTO.setItemAttrGroupGuid("itemAttrGroupGuid");
        attrGroupWebRespDTO.setAttrGroupGuid("attrGroupGuid");
        attrGroupWebRespDTO.setIsRequired(0);
        attrGroupWebRespDTO.setIsMultiChoice(0);
        attrGroupWebRespDTO.setWithDefault(0);
        final AttrWebRespDTO attrWebRespDTO = new AttrWebRespDTO();
        attrWebRespDTO.setAttrItemAttrGroupGuid("attrItemAttrGroupGuid");
        attrWebRespDTO.setAttrGuid("attrGuid");
        attrWebRespDTO.setIsDefault(0);
        attrGroupWebRespDTO.setAttrList(Arrays.asList(attrWebRespDTO));
        itemInfoRespDTO.setAttrGroupList(Arrays.asList(attrGroupWebRespDTO));
        final SubgroupWebRespDTO subgroupWebRespDTO = new SubgroupWebRespDTO();
        subgroupWebRespDTO.setSubgroupGuid("subgroupGuid");
        subgroupWebRespDTO.setName("name");
        subgroupWebRespDTO.setIsFixSubgroup(0);
        subgroupWebRespDTO.setPickNum(0);
        subgroupWebRespDTO.setSort(0);
        final SubItemSkuWebRespDTO subItemSkuWebRespDTO = new SubItemSkuWebRespDTO();
        subItemSkuWebRespDTO.setSkuGuid("skuGuid");
        subItemSkuWebRespDTO.setItemGuid("itemGuid");
        subItemSkuWebRespDTO.setItemNum(new BigDecimal("0.00"));
        subItemSkuWebRespDTO.setAddPrice(new BigDecimal("0.00"));
        subItemSkuWebRespDTO.setIsDefault(0);
        subItemSkuWebRespDTO.setIsRepeat(0);
        subgroupWebRespDTO.setSubItemSkuList(Arrays.asList(subItemSkuWebRespDTO));
        itemInfoRespDTO.setSubgroupList(Arrays.asList(subgroupWebRespDTO));
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setFrom(0);
        itemSingleDTO.setData("parentGuid");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        when(mockIItemService.getItemInfo(itemSingleDTO)).thenReturn(itemInfoRespDTO);

        // Configure IPricePlanItemService.getSyncItem(...).
        final SyncItemDTO syncItemDTO = new SyncItemDTO();
        syncItemDTO.setItemGuid("parentGuid");
        syncItemDTO.setSkuGuid("skuGuid");
        syncItemDTO.setSalePrice(new BigDecimal("0.00"));
        syncItemDTO.setMemberPrice(new BigDecimal("0.00"));
        syncItemDTO.setOriginalSalePrice(new BigDecimal("0.00"));
        syncItemDTO.setOriginalMemberPrice(new BigDecimal("0.00"));
        syncItemDTO.setIsRack(0);
        syncItemDTO.setIsJoinAio(0);
        syncItemDTO.setIsJoinPos(0);
        syncItemDTO.setIsJoinPad(0);
        when(mockIPricePlanItemService.getSyncItem("parentGuid", "parentGuid")).thenReturn(syncItemDTO);

        // Configure ISubgroupService.list(...).
        final SubgroupDO subgroupDO = new SubgroupDO();
        subgroupDO.setId(0L);
        subgroupDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        subgroupDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        subgroupDO.setGuid("9fbb2f86-9c26-492d-bb2e-2590fd13b1d4");
        subgroupDO.setItemGuid("newItemGuid");
        final List<SubgroupDO> subgroupDOS = Arrays.asList(subgroupDO);
        when(mockISubgroupService.list(any(QueryWrapper.class))).thenReturn(subgroupDOS);

        // Configure IRSkuSubgroupService.list(...).
        final RSkuSubgroupDO rSkuSubgroupDO = new RSkuSubgroupDO();
        rSkuSubgroupDO.setId(0L);
        rSkuSubgroupDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        rSkuSubgroupDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        rSkuSubgroupDO.setGuid("guid");
        rSkuSubgroupDO.setItemGuid("itemGuid");
        final List<RSkuSubgroupDO> rSkuSubgroupDOS = Arrays.asList(rSkuSubgroupDO);
        when(mockIrSkuSubgroupService.list(any(QueryWrapper.class))).thenReturn(rSkuSubgroupDOS);

        // Configure IItemPkgService.updateItemPkg(...).
        final ItemPkgSaveReqDTO itemPkgSaveReqDTO = new ItemPkgSaveReqDTO();
        itemPkgSaveReqDTO.setFrom(0);
        itemPkgSaveReqDTO.setDataList(Arrays.asList("value"));
        itemPkgSaveReqDTO.setItemGuid("parentGuid");
        itemPkgSaveReqDTO.setTypeGuid("typeGuid");
        itemPkgSaveReqDTO.setStoreGuid("storeGuid");
        itemPkgSaveReqDTO.setBrandGuid("brandGuid");
        itemPkgSaveReqDTO.setItemType(0);
        itemPkgSaveReqDTO.setName("name");
        itemPkgSaveReqDTO.setPinyin("pinyin");
        itemPkgSaveReqDTO.setSort(0);
        itemPkgSaveReqDTO.setPictureUrl("pictureUrl");
        itemPkgSaveReqDTO.setIsBestseller(0);
        itemPkgSaveReqDTO.setIsNew(0);
        itemPkgSaveReqDTO.setIsSign(0);
        itemPkgSaveReqDTO.setDescription("description");
        final SkuSaveReqDTO skuSaveReqDTO = new SkuSaveReqDTO();
        skuSaveReqDTO.setItemGuid("itemGuid");
        skuSaveReqDTO.setSkuGuid("parentGuid");
        skuSaveReqDTO.setName("name");
        skuSaveReqDTO.setSalePrice(new BigDecimal("0.00"));
        skuSaveReqDTO.setMemberPrice(new BigDecimal("0.00"));
        skuSaveReqDTO.setUnit("unit");
        skuSaveReqDTO.setIsMemberDiscount(0);
        skuSaveReqDTO.setIsWholeDiscount(0);
        skuSaveReqDTO.setIsRack(0);
        skuSaveReqDTO.setIsJoinAio(0);
        skuSaveReqDTO.setIsJoinPos(0);
        skuSaveReqDTO.setIsJoinPad(0);
        skuSaveReqDTO.setMinOrderNum(new BigDecimal("0.00"));
        skuSaveReqDTO.setIsJoinBuffet(0);
        skuSaveReqDTO.setIsJoinWeChat(0);
        skuSaveReqDTO.setIsJoinMt(0);
        skuSaveReqDTO.setIsJoinElm(0);
        skuSaveReqDTO.setIsOpenStock(0);
        skuSaveReqDTO.setStoreGuid("storeGuid");
        skuSaveReqDTO.setBrandGuid("brandGuid");
        skuSaveReqDTO.setSkuFrom(0);
        skuSaveReqDTO.setIsJoinMiniAppMall(0);
        skuSaveReqDTO.setIsJoinMiniAppTakeaway(0);
        skuSaveReqDTO.setIsJoinStore(0);
        itemPkgSaveReqDTO.setSkuList(Arrays.asList(skuSaveReqDTO));
        final SubgroupReqDTO subgroupReqDTO = new SubgroupReqDTO();
        subgroupReqDTO.setSubgroupGuid("subgroupGuid");
        subgroupReqDTO.setName("name");
        subgroupReqDTO.setIsFixSubgroup(0);
        subgroupReqDTO.setPickNum(0);
        subgroupReqDTO.setSort(0);
        final SubItemSkuReqDTO subItemSkuReqDTO = new SubItemSkuReqDTO();
        subItemSkuReqDTO.setSkuGuid("skuGuid");
        subItemSkuReqDTO.setItemGuid("itemGuid");
        subItemSkuReqDTO.setItemNum(new BigDecimal("0.00"));
        subItemSkuReqDTO.setAddPrice(new BigDecimal("0.00"));
        subItemSkuReqDTO.setIsDefault(0);
        subItemSkuReqDTO.setIsRepeat(0);
        subgroupReqDTO.setSubItemSkuList(Arrays.asList(subItemSkuReqDTO));
        itemPkgSaveReqDTO.setSubgroupList(Arrays.asList(subgroupReqDTO));
        when(mockItemPkgService.updateItemPkg(itemPkgSaveReqDTO)).thenReturn(Collections.emptyList());

        when(mockIItemService.updateOriginItemStoreGuid("parentGuid", "storeGuid", "brandGuid")).thenReturn(0);

        // Configure IRedissonCacheService.getCache(...).
        final SyncItemDTO syncItemDTO1 = new SyncItemDTO();
        syncItemDTO1.setItemGuid("parentGuid");
        syncItemDTO1.setSkuGuid("skuGuid");
        syncItemDTO1.setSalePrice(new BigDecimal("0.00"));
        syncItemDTO1.setMemberPrice(new BigDecimal("0.00"));
        syncItemDTO1.setOriginalSalePrice(new BigDecimal("0.00"));
        syncItemDTO1.setOriginalMemberPrice(new BigDecimal("0.00"));
        syncItemDTO1.setIsRack(0);
        syncItemDTO1.setIsJoinAio(0);
        syncItemDTO1.setIsJoinPos(0);
        syncItemDTO1.setIsJoinPad(0);
        when(mockIRedissonCacheService.getCache("key")).thenReturn(syncItemDTO1);

        when(mockISkuService.updateOriginPriceStoreGuid("parentGuid", "storeGuid", "skuGuid", new BigDecimal("0.00"),
                new BigDecimal("0.00"))).thenReturn(0);
        when(mockIItemService.getGuidByStoreGuidAndParentGuid("storeGuid", "parentGuid")).thenReturn("newItemGuid");
        when(mockICommonService.md5("src")).thenReturn("key");

        // Run the test
        final String result = syncPricePlanToItemServiceImplUnderTest.addPricePlanPkgItem(itemDTO, "storeGuid",
                "brandGuid", "pricePlanGuid");

        // Verify the results
        assertThat(result).isEqualTo("newItemGuid");
        verify(mockIItemService).deleteByGuidAndFrom("guid", 0L, "storeGuid");
        verify(mockISkuService).deleteSkuByItemGuidAndStoreGuid("itemGuid", "storeGuid");

        // Confirm IRedissonCacheService.setCache(...).
        final SyncItemDTO value = new SyncItemDTO();
        value.setItemGuid("parentGuid");
        value.setSkuGuid("skuGuid");
        value.setSalePrice(new BigDecimal("0.00"));
        value.setMemberPrice(new BigDecimal("0.00"));
        value.setOriginalSalePrice(new BigDecimal("0.00"));
        value.setOriginalMemberPrice(new BigDecimal("0.00"));
        value.setIsRack(0);
        value.setIsJoinAio(0);
        value.setIsJoinPos(0);
        value.setIsJoinPad(0);
        verify(mockIRedissonCacheService).setCache("key", value, 20L, TimeUnit.MINUTES);
        verify(mockIRedissonCacheService).removeCache("key");
        verify(mockIrSkuSubgroupService).updateIsDelete("guid");
        verify(mockISkuService).list(any(QueryWrapper.class));

        // Confirm ItemHelper.distributeItems2Stores(...).
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setFrom(0);
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        verify(mockItemHelper).distributeItems2Stores(itemStringListDTO, Arrays.asList("value"), true);

        // Confirm ISkuService.updateTerminalStatus(...).
        final UpdateTerminalStatusDTO updateTerminalStatusDTO = new UpdateTerminalStatusDTO();
        updateTerminalStatusDTO.setStoreGuid("storeGuid");
        updateTerminalStatusDTO.setParentGuid("parentGuid");
        updateTerminalStatusDTO.setIsJoinAio(0);
        updateTerminalStatusDTO.setIsJoinPos(0);
        updateTerminalStatusDTO.setIsJoinPad(0);
        updateTerminalStatusDTO.setIsJoinMiniAppMall(0);
        updateTerminalStatusDTO.setIsJoinMiniAppTakeaway(0);
        updateTerminalStatusDTO.setIsJoinStore(0);
        verify(mockISkuService).updateTerminalStatus(updateTerminalStatusDTO);

        // Confirm ISubgroupService.updateById(...).
        final SubgroupDO entity = new SubgroupDO();
        entity.setId(0L);
        entity.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setGuid("9fbb2f86-9c26-492d-bb2e-2590fd13b1d4");
        entity.setItemGuid("newItemGuid");
        verify(mockISubgroupService).updateById(entity);
    }

    @Test
    public void testDeleteItemByParentGuid() {
        // Setup
        when(mockIItemService.getItemInfoListByParentId("parentGuid", "storeGuid"))
                .thenReturn(Arrays.asList(new HashMap<>()));

        // Run the test
        final String result = syncPricePlanToItemServiceImplUnderTest.deleteItemByParentGuid("parentGuid", "storeGuid");

        // Verify the results
        assertThat(result).isEqualTo("");
        verify(mockIItemService).deleteByGuidAndFrom("guid", 0L, "storeGuid");
        verify(mockISkuService).deleteSkuByItemGuidAndStoreGuid("itemGuid", "storeGuid");
    }

    @Test
    public void testDeleteItemByParentGuid_IItemServiceGetItemInfoListByParentIdReturnsNoItems() {
        // Setup
        when(mockIItemService.getItemInfoListByParentId("parentGuid", "storeGuid")).thenReturn(Collections.emptyList());

        // Run the test
        final String result = syncPricePlanToItemServiceImplUnderTest.deleteItemByParentGuid("parentGuid", "storeGuid");

        // Verify the results
        assertThat(result).isEqualTo("");
    }

    @Test
    public void testUpdatePushRecordData() {
        // Setup
        final PushRecordStatusUpdateReqDTO recordStatusUpdateReqDTO = new PushRecordStatusUpdateReqDTO();
        recordStatusUpdateReqDTO.setPlanGuid("planGuid");
        recordStatusUpdateReqDTO.setStoreGuid("storeGuid");
        recordStatusUpdateReqDTO.setItemGuid("itemGuid");
        recordStatusUpdateReqDTO.setStatus(0);
        recordStatusUpdateReqDTO.setNewItemGuid("newItemGuid");

        // Run the test
        final String result = syncPricePlanToItemServiceImplUnderTest.updatePushRecordData(recordStatusUpdateReqDTO);

        // Verify the results
        assertThat(result).isEqualTo("newItemGuid");

        // Confirm IPricePlanPushRecordService.updatePushStatus(...).
        final PushRecordStatusUpdateReqDTO reqDTO = new PushRecordStatusUpdateReqDTO();
        reqDTO.setPlanGuid("planGuid");
        reqDTO.setStoreGuid("storeGuid");
        reqDTO.setItemGuid("itemGuid");
        reqDTO.setStatus(0);
        reqDTO.setNewItemGuid("newItemGuid");
        verify(mockIPricePlanPushRecordService).updatePushStatus(reqDTO);
    }

    @Test
    public void testGetSubGroupListDTO() {
        // Setup
        final SubgroupWebRespDTO subgroupWebRespDTO = new SubgroupWebRespDTO();
        subgroupWebRespDTO.setSubgroupGuid("subgroupGuid");
        subgroupWebRespDTO.setName("name");
        subgroupWebRespDTO.setIsFixSubgroup(0);
        subgroupWebRespDTO.setPickNum(0);
        subgroupWebRespDTO.setSort(0);
        final SubItemSkuWebRespDTO subItemSkuWebRespDTO = new SubItemSkuWebRespDTO();
        subItemSkuWebRespDTO.setSkuGuid("skuGuid");
        subItemSkuWebRespDTO.setItemGuid("itemGuid");
        subItemSkuWebRespDTO.setItemNum(new BigDecimal("0.00"));
        subItemSkuWebRespDTO.setAddPrice(new BigDecimal("0.00"));
        subItemSkuWebRespDTO.setIsDefault(0);
        subItemSkuWebRespDTO.setIsRepeat(0);
        subgroupWebRespDTO.setSubItemSkuList(Arrays.asList(subItemSkuWebRespDTO));
        final List<SubgroupWebRespDTO> subgroupWebRespDTOList = Arrays.asList(subgroupWebRespDTO);
        final SubgroupReqDTO subgroupReqDTO = new SubgroupReqDTO();
        subgroupReqDTO.setSubgroupGuid("subgroupGuid");
        subgroupReqDTO.setName("name");
        subgroupReqDTO.setIsFixSubgroup(0);
        subgroupReqDTO.setPickNum(0);
        subgroupReqDTO.setSort(0);
        final SubItemSkuReqDTO subItemSkuReqDTO = new SubItemSkuReqDTO();
        subItemSkuReqDTO.setSkuGuid("skuGuid");
        subItemSkuReqDTO.setItemGuid("itemGuid");
        subItemSkuReqDTO.setItemNum(new BigDecimal("0.00"));
        subItemSkuReqDTO.setAddPrice(new BigDecimal("0.00"));
        subItemSkuReqDTO.setIsDefault(0);
        subItemSkuReqDTO.setIsRepeat(0);
        subgroupReqDTO.setSubItemSkuList(Arrays.asList(subItemSkuReqDTO));
        final List<SubgroupReqDTO> expectedResult = Arrays.asList(subgroupReqDTO);

        // Run the test
        final List<SubgroupReqDTO> result = syncPricePlanToItemServiceImplUnderTest.getSubGroupListDTO(
                subgroupWebRespDTOList);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetSubItemSkuList() {
        // Setup
        final SubItemSkuWebRespDTO subItemSkuWebRespDTO = new SubItemSkuWebRespDTO();
        subItemSkuWebRespDTO.setSkuGuid("skuGuid");
        subItemSkuWebRespDTO.setItemGuid("itemGuid");
        subItemSkuWebRespDTO.setItemNum(new BigDecimal("0.00"));
        subItemSkuWebRespDTO.setAddPrice(new BigDecimal("0.00"));
        subItemSkuWebRespDTO.setIsDefault(0);
        subItemSkuWebRespDTO.setIsRepeat(0);
        final List<SubItemSkuWebRespDTO> subItemSkuWebRespDTOList = Arrays.asList(subItemSkuWebRespDTO);
        final SubItemSkuReqDTO subItemSkuReqDTO = new SubItemSkuReqDTO();
        subItemSkuReqDTO.setSkuGuid("skuGuid");
        subItemSkuReqDTO.setItemGuid("itemGuid");
        subItemSkuReqDTO.setItemNum(new BigDecimal("0.00"));
        subItemSkuReqDTO.setAddPrice(new BigDecimal("0.00"));
        subItemSkuReqDTO.setIsDefault(0);
        subItemSkuReqDTO.setIsRepeat(0);
        final List<SubItemSkuReqDTO> expectedResult = Arrays.asList(subItemSkuReqDTO);

        // Run the test
        final List<SubItemSkuReqDTO> result = syncPricePlanToItemServiceImplUnderTest.getSubItemSkuList(
                subItemSkuWebRespDTOList);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testDealPkgInfo() {
        // Setup
        // Configure ISubgroupService.list(...).
        final SubgroupDO subgroupDO = new SubgroupDO();
        subgroupDO.setId(0L);
        subgroupDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        subgroupDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        subgroupDO.setGuid("9fbb2f86-9c26-492d-bb2e-2590fd13b1d4");
        subgroupDO.setItemGuid("newItemGuid");
        final List<SubgroupDO> subgroupDOS = Arrays.asList(subgroupDO);
        when(mockISubgroupService.list(any(QueryWrapper.class))).thenReturn(subgroupDOS);

        // Configure IRSkuSubgroupService.list(...).
        final RSkuSubgroupDO rSkuSubgroupDO = new RSkuSubgroupDO();
        rSkuSubgroupDO.setId(0L);
        rSkuSubgroupDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        rSkuSubgroupDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        rSkuSubgroupDO.setGuid("guid");
        rSkuSubgroupDO.setItemGuid("itemGuid");
        final List<RSkuSubgroupDO> rSkuSubgroupDOS = Arrays.asList(rSkuSubgroupDO);
        when(mockIrSkuSubgroupService.list(any(QueryWrapper.class))).thenReturn(rSkuSubgroupDOS);

        // Run the test
        syncPricePlanToItemServiceImplUnderTest.dealPkgInfo("itemGuid", "storeGuid");

        // Verify the results
        verify(mockIrSkuSubgroupService).updateIsDelete("guid");
        verify(mockISkuService).list(any(QueryWrapper.class));
    }

    @Test
    public void testDealPkgInfo_ISubgroupServiceReturnsNoItems() {
        // Setup
        when(mockISubgroupService.list(any(QueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        syncPricePlanToItemServiceImplUnderTest.dealPkgInfo("itemGuid", "storeGuid");

        // Verify the results
    }

    @Test
    public void testDealPkgInfo_IRSkuSubgroupServiceListReturnsNoItems() {
        // Setup
        // Configure ISubgroupService.list(...).
        final SubgroupDO subgroupDO = new SubgroupDO();
        subgroupDO.setId(0L);
        subgroupDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        subgroupDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        subgroupDO.setGuid("9fbb2f86-9c26-492d-bb2e-2590fd13b1d4");
        subgroupDO.setItemGuid("newItemGuid");
        final List<SubgroupDO> subgroupDOS = Arrays.asList(subgroupDO);
        when(mockISubgroupService.list(any(QueryWrapper.class))).thenReturn(subgroupDOS);

        when(mockIrSkuSubgroupService.list(any(QueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        syncPricePlanToItemServiceImplUnderTest.dealPkgInfo("itemGuid", "storeGuid");

        // Verify the results
    }

    @Test
    public void testUpdateSubGroupItemToNewItem() {
        // Setup
        // Configure ISubgroupService.list(...).
        final SubgroupDO subgroupDO = new SubgroupDO();
        subgroupDO.setId(0L);
        subgroupDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        subgroupDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        subgroupDO.setGuid("9fbb2f86-9c26-492d-bb2e-2590fd13b1d4");
        subgroupDO.setItemGuid("newItemGuid");
        final List<SubgroupDO> subgroupDOS = Arrays.asList(subgroupDO);
        when(mockISubgroupService.list(any(QueryWrapper.class))).thenReturn(subgroupDOS);

        // Run the test
        syncPricePlanToItemServiceImplUnderTest.updateSubGroupItemToNewItem("itemGuid", "storeGuid", "newItemGuid");

        // Verify the results
        // Confirm ISubgroupService.updateById(...).
        final SubgroupDO entity = new SubgroupDO();
        entity.setId(0L);
        entity.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setGuid("9fbb2f86-9c26-492d-bb2e-2590fd13b1d4");
        entity.setItemGuid("newItemGuid");
        verify(mockISubgroupService).updateById(entity);
    }

    @Test
    public void testUpdateSubGroupItemToNewItem_ISubgroupServiceListReturnsNoItems() {
        // Setup
        when(mockISubgroupService.list(any(QueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        syncPricePlanToItemServiceImplUnderTest.updateSubGroupItemToNewItem("itemGuid", "storeGuid", "newItemGuid");

        // Verify the results
    }
}
