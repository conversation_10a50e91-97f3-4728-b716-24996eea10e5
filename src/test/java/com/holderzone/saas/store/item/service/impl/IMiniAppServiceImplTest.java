package com.holderzone.saas.store.item.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.saas.store.dto.item.common.ItemSkuStockDTO;
import com.holderzone.saas.store.dto.item.req.*;
import com.holderzone.saas.store.dto.item.resp.*;
import com.holderzone.saas.store.dto.item.resp.estimate.EstimateResultRespDTO;
import com.holderzone.saas.store.item.entity.domain.EstimateDO;
import com.holderzone.saas.store.item.entity.domain.ItemDO;
import com.holderzone.saas.store.item.entity.domain.SkuDO;
import com.holderzone.saas.store.item.service.IEstimateService;
import com.holderzone.saas.store.item.service.IItemService;
import com.holderzone.saas.store.item.service.ISkuService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class IMiniAppServiceImplTest {

    @Mock
    private ISkuService mockSkuService;
    @Mock
    private IItemService mockItemService;
    @Mock
    private IEstimateService mockIEstimateService;

    private IMiniAppServiceImpl iMiniAppServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        iMiniAppServiceImplUnderTest = new IMiniAppServiceImpl(mockSkuService, mockItemService, mockIEstimateService);
    }

    @Test
    public void testCheckShoppingCardForMiniApp() {
        // Setup
        final ItemShoppingCartCheckRequest itemShoppingCartCheckRequest = new ItemShoppingCartCheckRequest();
        itemShoppingCartCheckRequest.setStoreGuid("storeGuid");
        itemShoppingCartCheckRequest.setIsTakeaway(false);
        final SingleShoppingCartCheck singleShoppingCartCheck = new SingleShoppingCartCheck();
        singleShoppingCartCheck.setSkuGuid("skuGuid");
        singleShoppingCartCheck.setSkuName("skuName");
        singleShoppingCartCheck.setStock(new BigDecimal("0.00"));
        itemShoppingCartCheckRequest.setData(Arrays.asList(singleShoppingCartCheck));

        final ItemShoppingCartCheckResponse expectedResult = new ItemShoppingCartCheckResponse();
        expectedResult.setCode(0);
        expectedResult.setMessage("校验失败");
        final CartCheckResult cartCheckResult = new CartCheckResult();
        cartCheckResult.setSkuGuid("guid");
        cartCheckResult.setSuccess(false);
        cartCheckResult.setErrorMsg("校验成功");
        expectedResult.setData(Arrays.asList(cartCheckResult));

        // Configure ISkuService.list(...).
        final SkuDO skuDO = new SkuDO();
        skuDO.setGuid("guid");
        skuDO.setStoreGuid("storeGuid");
        skuDO.setItemGuid("itemGuid");
        skuDO.setStock(new BigDecimal("0.00"));
        skuDO.setName("name");
        final List<SkuDO> skuDOS = Arrays.asList(skuDO);
        when(mockSkuService.list(any(LambdaQueryWrapper.class))).thenReturn(skuDOS);

        // Run the test
        final ItemShoppingCartCheckResponse result = iMiniAppServiceImplUnderTest.checkShoppingCardForMiniApp(
                itemShoppingCartCheckRequest);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCheckShoppingCardForMiniApp_ISkuServiceReturnsNoItems() {
        // Setup
        final ItemShoppingCartCheckRequest itemShoppingCartCheckRequest = new ItemShoppingCartCheckRequest();
        itemShoppingCartCheckRequest.setStoreGuid("storeGuid");
        itemShoppingCartCheckRequest.setIsTakeaway(false);
        final SingleShoppingCartCheck singleShoppingCartCheck = new SingleShoppingCartCheck();
        singleShoppingCartCheck.setSkuGuid("skuGuid");
        singleShoppingCartCheck.setSkuName("skuName");
        singleShoppingCartCheck.setStock(new BigDecimal("0.00"));
        itemShoppingCartCheckRequest.setData(Arrays.asList(singleShoppingCartCheck));

        final ItemShoppingCartCheckResponse expectedResult = new ItemShoppingCartCheckResponse();
        expectedResult.setCode(0);
        expectedResult.setMessage("校验失败");
        final CartCheckResult cartCheckResult = new CartCheckResult();
        cartCheckResult.setSkuGuid("guid");
        cartCheckResult.setSuccess(false);
        cartCheckResult.setErrorMsg("校验成功");
        expectedResult.setData(Arrays.asList(cartCheckResult));

        when(mockSkuService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final ItemShoppingCartCheckResponse result = iMiniAppServiceImplUnderTest.checkShoppingCardForMiniApp(
                itemShoppingCartCheckRequest);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryShoppingCardForMiniApp() {
        // Setup
        final ItemShoppingCardRequest itemShoppingCardRequest = new ItemShoppingCardRequest();
        itemShoppingCardRequest.setEnterpriseGuid("enterpriseGuid");
        itemShoppingCardRequest.setStoreGuid("storeGuid");
        itemShoppingCardRequest.setSkuGuids(Arrays.asList("value"));

        final ItemShoppingCartResp expectedResult = new ItemShoppingCartResp();
        final SingleShoppingCart singleShoppingCart = new SingleShoppingCart();
        singleShoppingCart.setItemGuid("guid");
        singleShoppingCart.setStock(new BigDecimal("0.00"));
        singleShoppingCart.setName("name");
        singleShoppingCart.setSkuGuid("guid");
        singleShoppingCart.setSkuName("name");
        final AttrGroupWebRespDTO attrGroupWebRespDTO = new AttrGroupWebRespDTO();
        singleShoppingCart.setAttrGroupList(Arrays.asList(attrGroupWebRespDTO));
        final SubgroupWebRespDTO subgroupWebRespDTO = new SubgroupWebRespDTO();
        singleShoppingCart.setSubgroupList(Arrays.asList(subgroupWebRespDTO));
        expectedResult.setData(Arrays.asList(singleShoppingCart));

        // Configure ISkuService.list(...).
        final SkuDO skuDO = new SkuDO();
        skuDO.setGuid("guid");
        skuDO.setStoreGuid("storeGuid");
        skuDO.setItemGuid("itemGuid");
        skuDO.setStock(new BigDecimal("0.00"));
        skuDO.setName("name");
        final List<SkuDO> skuDOS = Arrays.asList(skuDO);
        when(mockSkuService.list(any(LambdaQueryWrapper.class))).thenReturn(skuDOS);

        // Configure IEstimateService.list(...).
        final List<EstimateDO> estimateDOS = Arrays.asList(EstimateDO.builder()
                .skuGuid("skuGuid")
                .residueQuantity(new BigDecimal("0.00"))
                .storeGuid("storeGuid")
                .isDelete(0)
                .build());
        when(mockIEstimateService.list(any(LambdaQueryWrapper.class))).thenReturn(estimateDOS);

        // Configure IItemService.list(...).
        final List<ItemDO> itemDOS = Arrays.asList(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "typeGuid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0, "pinyin", "nameAbbr", "description",
                        "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code", 0, 0, 0, "videoUrls", 0, false));
        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(itemDOS);

        // Configure IItemService.selectItemAttrList(...).
        final AttrGroupWebRespDTO attrGroupWebRespDTO1 = new AttrGroupWebRespDTO();
        attrGroupWebRespDTO1.setItemGuid("itemGuid");
        attrGroupWebRespDTO1.setItemAttrGroupGuid("itemAttrGroupGuid");
        attrGroupWebRespDTO1.setAttrGroupGuid("attrGroupGuid");
        attrGroupWebRespDTO1.setName("name");
        attrGroupWebRespDTO1.setIsRequired(0);
        final List<AttrGroupWebRespDTO> attrGroupWebRespDTOS = Arrays.asList(attrGroupWebRespDTO1);
        when(mockItemService.selectItemAttrList("guid")).thenReturn(attrGroupWebRespDTOS);

        // Configure IItemService.selectSubgroupList(...).
        final SubgroupWebRespDTO subgroupWebRespDTO1 = new SubgroupWebRespDTO();
        subgroupWebRespDTO1.setItemGuid("itemGuid");
        subgroupWebRespDTO1.setSubgroupGuid("subgroupGuid");
        subgroupWebRespDTO1.setName("name");
        subgroupWebRespDTO1.setIsFixSubgroup(0);
        subgroupWebRespDTO1.setPickNum(0);
        final List<SubgroupWebRespDTO> subgroupWebRespDTOS = Arrays.asList(subgroupWebRespDTO1);
        when(mockItemService.selectSubgroupList("guid")).thenReturn(subgroupWebRespDTOS);

        // Run the test
        final ItemShoppingCartResp result = iMiniAppServiceImplUnderTest.queryShoppingCardForMiniApp(
                itemShoppingCardRequest);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryShoppingCardForMiniApp_ISkuServiceReturnsNoItems() {
        // Setup
        final ItemShoppingCardRequest itemShoppingCardRequest = new ItemShoppingCardRequest();
        itemShoppingCardRequest.setEnterpriseGuid("enterpriseGuid");
        itemShoppingCardRequest.setStoreGuid("storeGuid");
        itemShoppingCardRequest.setSkuGuids(Arrays.asList("value"));

        final ItemShoppingCartResp expectedResult = new ItemShoppingCartResp();
        final SingleShoppingCart singleShoppingCart = new SingleShoppingCart();
        singleShoppingCart.setItemGuid("guid");
        singleShoppingCart.setStock(new BigDecimal("0.00"));
        singleShoppingCart.setName("name");
        singleShoppingCart.setSkuGuid("guid");
        singleShoppingCart.setSkuName("name");
        final AttrGroupWebRespDTO attrGroupWebRespDTO = new AttrGroupWebRespDTO();
        singleShoppingCart.setAttrGroupList(Arrays.asList(attrGroupWebRespDTO));
        final SubgroupWebRespDTO subgroupWebRespDTO = new SubgroupWebRespDTO();
        singleShoppingCart.setSubgroupList(Arrays.asList(subgroupWebRespDTO));
        expectedResult.setData(Arrays.asList(singleShoppingCart));

        when(mockSkuService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure IEstimateService.list(...).
        final List<EstimateDO> estimateDOS = Arrays.asList(EstimateDO.builder()
                .skuGuid("skuGuid")
                .residueQuantity(new BigDecimal("0.00"))
                .storeGuid("storeGuid")
                .isDelete(0)
                .build());
        when(mockIEstimateService.list(any(LambdaQueryWrapper.class))).thenReturn(estimateDOS);

        // Configure IItemService.list(...).
        final List<ItemDO> itemDOS = Arrays.asList(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "typeGuid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0, "pinyin", "nameAbbr", "description",
                        "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code", 0, 0, 0, "videoUrls", 0, false));
        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(itemDOS);

        // Configure IItemService.selectItemAttrList(...).
        final AttrGroupWebRespDTO attrGroupWebRespDTO1 = new AttrGroupWebRespDTO();
        attrGroupWebRespDTO1.setItemGuid("itemGuid");
        attrGroupWebRespDTO1.setItemAttrGroupGuid("itemAttrGroupGuid");
        attrGroupWebRespDTO1.setAttrGroupGuid("attrGroupGuid");
        attrGroupWebRespDTO1.setName("name");
        attrGroupWebRespDTO1.setIsRequired(0);
        final List<AttrGroupWebRespDTO> attrGroupWebRespDTOS = Arrays.asList(attrGroupWebRespDTO1);
        when(mockItemService.selectItemAttrList("guid")).thenReturn(attrGroupWebRespDTOS);

        // Configure IItemService.selectSubgroupList(...).
        final SubgroupWebRespDTO subgroupWebRespDTO1 = new SubgroupWebRespDTO();
        subgroupWebRespDTO1.setItemGuid("itemGuid");
        subgroupWebRespDTO1.setSubgroupGuid("subgroupGuid");
        subgroupWebRespDTO1.setName("name");
        subgroupWebRespDTO1.setIsFixSubgroup(0);
        subgroupWebRespDTO1.setPickNum(0);
        final List<SubgroupWebRespDTO> subgroupWebRespDTOS = Arrays.asList(subgroupWebRespDTO1);
        when(mockItemService.selectSubgroupList("guid")).thenReturn(subgroupWebRespDTOS);

        // Run the test
        final ItemShoppingCartResp result = iMiniAppServiceImplUnderTest.queryShoppingCardForMiniApp(
                itemShoppingCardRequest);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryShoppingCardForMiniApp_IEstimateServiceReturnsNoItems() {
        // Setup
        final ItemShoppingCardRequest itemShoppingCardRequest = new ItemShoppingCardRequest();
        itemShoppingCardRequest.setEnterpriseGuid("enterpriseGuid");
        itemShoppingCardRequest.setStoreGuid("storeGuid");
        itemShoppingCardRequest.setSkuGuids(Arrays.asList("value"));

        final ItemShoppingCartResp expectedResult = new ItemShoppingCartResp();
        final SingleShoppingCart singleShoppingCart = new SingleShoppingCart();
        singleShoppingCart.setItemGuid("guid");
        singleShoppingCart.setStock(new BigDecimal("0.00"));
        singleShoppingCart.setName("name");
        singleShoppingCart.setSkuGuid("guid");
        singleShoppingCart.setSkuName("name");
        final AttrGroupWebRespDTO attrGroupWebRespDTO = new AttrGroupWebRespDTO();
        singleShoppingCart.setAttrGroupList(Arrays.asList(attrGroupWebRespDTO));
        final SubgroupWebRespDTO subgroupWebRespDTO = new SubgroupWebRespDTO();
        singleShoppingCart.setSubgroupList(Arrays.asList(subgroupWebRespDTO));
        expectedResult.setData(Arrays.asList(singleShoppingCart));

        // Configure ISkuService.list(...).
        final SkuDO skuDO = new SkuDO();
        skuDO.setGuid("guid");
        skuDO.setStoreGuid("storeGuid");
        skuDO.setItemGuid("itemGuid");
        skuDO.setStock(new BigDecimal("0.00"));
        skuDO.setName("name");
        final List<SkuDO> skuDOS = Arrays.asList(skuDO);
        when(mockSkuService.list(any(LambdaQueryWrapper.class))).thenReturn(skuDOS);

        when(mockIEstimateService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure IItemService.list(...).
        final List<ItemDO> itemDOS = Arrays.asList(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "typeGuid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0, "pinyin", "nameAbbr", "description",
                        "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code", 0, 0, 0, "videoUrls", 0, false));
        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(itemDOS);

        // Configure IItemService.selectItemAttrList(...).
        final AttrGroupWebRespDTO attrGroupWebRespDTO1 = new AttrGroupWebRespDTO();
        attrGroupWebRespDTO1.setItemGuid("itemGuid");
        attrGroupWebRespDTO1.setItemAttrGroupGuid("itemAttrGroupGuid");
        attrGroupWebRespDTO1.setAttrGroupGuid("attrGroupGuid");
        attrGroupWebRespDTO1.setName("name");
        attrGroupWebRespDTO1.setIsRequired(0);
        final List<AttrGroupWebRespDTO> attrGroupWebRespDTOS = Arrays.asList(attrGroupWebRespDTO1);
        when(mockItemService.selectItemAttrList("guid")).thenReturn(attrGroupWebRespDTOS);

        // Configure IItemService.selectSubgroupList(...).
        final SubgroupWebRespDTO subgroupWebRespDTO1 = new SubgroupWebRespDTO();
        subgroupWebRespDTO1.setItemGuid("itemGuid");
        subgroupWebRespDTO1.setSubgroupGuid("subgroupGuid");
        subgroupWebRespDTO1.setName("name");
        subgroupWebRespDTO1.setIsFixSubgroup(0);
        subgroupWebRespDTO1.setPickNum(0);
        final List<SubgroupWebRespDTO> subgroupWebRespDTOS = Arrays.asList(subgroupWebRespDTO1);
        when(mockItemService.selectSubgroupList("guid")).thenReturn(subgroupWebRespDTOS);

        // Run the test
        final ItemShoppingCartResp result = iMiniAppServiceImplUnderTest.queryShoppingCardForMiniApp(
                itemShoppingCardRequest);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryShoppingCardForMiniApp_IItemServiceListReturnsNoItems() {
        // Setup
        final ItemShoppingCardRequest itemShoppingCardRequest = new ItemShoppingCardRequest();
        itemShoppingCardRequest.setEnterpriseGuid("enterpriseGuid");
        itemShoppingCardRequest.setStoreGuid("storeGuid");
        itemShoppingCardRequest.setSkuGuids(Arrays.asList("value"));

        final ItemShoppingCartResp expectedResult = new ItemShoppingCartResp();
        final SingleShoppingCart singleShoppingCart = new SingleShoppingCart();
        singleShoppingCart.setItemGuid("guid");
        singleShoppingCart.setStock(new BigDecimal("0.00"));
        singleShoppingCart.setName("name");
        singleShoppingCart.setSkuGuid("guid");
        singleShoppingCart.setSkuName("name");
        final AttrGroupWebRespDTO attrGroupWebRespDTO = new AttrGroupWebRespDTO();
        singleShoppingCart.setAttrGroupList(Arrays.asList(attrGroupWebRespDTO));
        final SubgroupWebRespDTO subgroupWebRespDTO = new SubgroupWebRespDTO();
        singleShoppingCart.setSubgroupList(Arrays.asList(subgroupWebRespDTO));
        expectedResult.setData(Arrays.asList(singleShoppingCart));

        // Configure ISkuService.list(...).
        final SkuDO skuDO = new SkuDO();
        skuDO.setGuid("guid");
        skuDO.setStoreGuid("storeGuid");
        skuDO.setItemGuid("itemGuid");
        skuDO.setStock(new BigDecimal("0.00"));
        skuDO.setName("name");
        final List<SkuDO> skuDOS = Arrays.asList(skuDO);
        when(mockSkuService.list(any(LambdaQueryWrapper.class))).thenReturn(skuDOS);

        // Configure IEstimateService.list(...).
        final List<EstimateDO> estimateDOS = Arrays.asList(EstimateDO.builder()
                .skuGuid("skuGuid")
                .residueQuantity(new BigDecimal("0.00"))
                .storeGuid("storeGuid")
                .isDelete(0)
                .build());
        when(mockIEstimateService.list(any(LambdaQueryWrapper.class))).thenReturn(estimateDOS);

        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final ItemShoppingCartResp result = iMiniAppServiceImplUnderTest.queryShoppingCardForMiniApp(
                itemShoppingCardRequest);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryShoppingCardForMiniApp_IItemServiceSelectItemAttrListReturnsNoItems() {
        // Setup
        final ItemShoppingCardRequest itemShoppingCardRequest = new ItemShoppingCardRequest();
        itemShoppingCardRequest.setEnterpriseGuid("enterpriseGuid");
        itemShoppingCardRequest.setStoreGuid("storeGuid");
        itemShoppingCardRequest.setSkuGuids(Arrays.asList("value"));

        final ItemShoppingCartResp expectedResult = new ItemShoppingCartResp();
        final SingleShoppingCart singleShoppingCart = new SingleShoppingCart();
        singleShoppingCart.setItemGuid("guid");
        singleShoppingCart.setStock(new BigDecimal("0.00"));
        singleShoppingCart.setName("name");
        singleShoppingCart.setSkuGuid("guid");
        singleShoppingCart.setSkuName("name");
        final AttrGroupWebRespDTO attrGroupWebRespDTO = new AttrGroupWebRespDTO();
        singleShoppingCart.setAttrGroupList(Arrays.asList(attrGroupWebRespDTO));
        final SubgroupWebRespDTO subgroupWebRespDTO = new SubgroupWebRespDTO();
        singleShoppingCart.setSubgroupList(Arrays.asList(subgroupWebRespDTO));
        expectedResult.setData(Arrays.asList(singleShoppingCart));

        // Configure ISkuService.list(...).
        final SkuDO skuDO = new SkuDO();
        skuDO.setGuid("guid");
        skuDO.setStoreGuid("storeGuid");
        skuDO.setItemGuid("itemGuid");
        skuDO.setStock(new BigDecimal("0.00"));
        skuDO.setName("name");
        final List<SkuDO> skuDOS = Arrays.asList(skuDO);
        when(mockSkuService.list(any(LambdaQueryWrapper.class))).thenReturn(skuDOS);

        // Configure IEstimateService.list(...).
        final List<EstimateDO> estimateDOS = Arrays.asList(EstimateDO.builder()
                .skuGuid("skuGuid")
                .residueQuantity(new BigDecimal("0.00"))
                .storeGuid("storeGuid")
                .isDelete(0)
                .build());
        when(mockIEstimateService.list(any(LambdaQueryWrapper.class))).thenReturn(estimateDOS);

        // Configure IItemService.list(...).
        final List<ItemDO> itemDOS = Arrays.asList(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "typeGuid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0, "pinyin", "nameAbbr", "description",
                        "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code", 0, 0, 0, "videoUrls", 0, false));
        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(itemDOS);

        when(mockItemService.selectItemAttrList("guid")).thenReturn(Collections.emptyList());

        // Configure IItemService.selectSubgroupList(...).
        final SubgroupWebRespDTO subgroupWebRespDTO1 = new SubgroupWebRespDTO();
        subgroupWebRespDTO1.setItemGuid("itemGuid");
        subgroupWebRespDTO1.setSubgroupGuid("subgroupGuid");
        subgroupWebRespDTO1.setName("name");
        subgroupWebRespDTO1.setIsFixSubgroup(0);
        subgroupWebRespDTO1.setPickNum(0);
        final List<SubgroupWebRespDTO> subgroupWebRespDTOS = Arrays.asList(subgroupWebRespDTO1);
        when(mockItemService.selectSubgroupList("guid")).thenReturn(subgroupWebRespDTOS);

        // Run the test
        final ItemShoppingCartResp result = iMiniAppServiceImplUnderTest.queryShoppingCardForMiniApp(
                itemShoppingCardRequest);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryShoppingCardForMiniApp_IItemServiceSelectSubgroupListReturnsNoItems() {
        // Setup
        final ItemShoppingCardRequest itemShoppingCardRequest = new ItemShoppingCardRequest();
        itemShoppingCardRequest.setEnterpriseGuid("enterpriseGuid");
        itemShoppingCardRequest.setStoreGuid("storeGuid");
        itemShoppingCardRequest.setSkuGuids(Arrays.asList("value"));

        final ItemShoppingCartResp expectedResult = new ItemShoppingCartResp();
        final SingleShoppingCart singleShoppingCart = new SingleShoppingCart();
        singleShoppingCart.setItemGuid("guid");
        singleShoppingCart.setStock(new BigDecimal("0.00"));
        singleShoppingCart.setName("name");
        singleShoppingCart.setSkuGuid("guid");
        singleShoppingCart.setSkuName("name");
        final AttrGroupWebRespDTO attrGroupWebRespDTO = new AttrGroupWebRespDTO();
        singleShoppingCart.setAttrGroupList(Arrays.asList(attrGroupWebRespDTO));
        final SubgroupWebRespDTO subgroupWebRespDTO = new SubgroupWebRespDTO();
        singleShoppingCart.setSubgroupList(Arrays.asList(subgroupWebRespDTO));
        expectedResult.setData(Arrays.asList(singleShoppingCart));

        // Configure ISkuService.list(...).
        final SkuDO skuDO = new SkuDO();
        skuDO.setGuid("guid");
        skuDO.setStoreGuid("storeGuid");
        skuDO.setItemGuid("itemGuid");
        skuDO.setStock(new BigDecimal("0.00"));
        skuDO.setName("name");
        final List<SkuDO> skuDOS = Arrays.asList(skuDO);
        when(mockSkuService.list(any(LambdaQueryWrapper.class))).thenReturn(skuDOS);

        // Configure IEstimateService.list(...).
        final List<EstimateDO> estimateDOS = Arrays.asList(EstimateDO.builder()
                .skuGuid("skuGuid")
                .residueQuantity(new BigDecimal("0.00"))
                .storeGuid("storeGuid")
                .isDelete(0)
                .build());
        when(mockIEstimateService.list(any(LambdaQueryWrapper.class))).thenReturn(estimateDOS);

        // Configure IItemService.list(...).
        final List<ItemDO> itemDOS = Arrays.asList(
                new ItemDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "guid",
                        "typeGuid", "pricePlanGuid", "brandGuid", 0, 0, 0, 0, "pinyin", "nameAbbr", "description",
                        "pictureUrl", 0, 0, 0, 0, 0, 0, "remarkDetail", 0, "code", 0, 0, 0, "videoUrls", 0, false));
        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(itemDOS);

        // Configure IItemService.selectItemAttrList(...).
        final AttrGroupWebRespDTO attrGroupWebRespDTO1 = new AttrGroupWebRespDTO();
        attrGroupWebRespDTO1.setItemGuid("itemGuid");
        attrGroupWebRespDTO1.setItemAttrGroupGuid("itemAttrGroupGuid");
        attrGroupWebRespDTO1.setAttrGroupGuid("attrGroupGuid");
        attrGroupWebRespDTO1.setName("name");
        attrGroupWebRespDTO1.setIsRequired(0);
        final List<AttrGroupWebRespDTO> attrGroupWebRespDTOS = Arrays.asList(attrGroupWebRespDTO1);
        when(mockItemService.selectItemAttrList("guid")).thenReturn(attrGroupWebRespDTOS);

        when(mockItemService.selectSubgroupList("guid")).thenReturn(Collections.emptyList());

        // Run the test
        final ItemShoppingCartResp result = iMiniAppServiceImplUnderTest.queryShoppingCardForMiniApp(
                itemShoppingCardRequest);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testDescStock() {
        // Setup
        final ItemShoppingCartCheckRequest itemShoppingCartCheckRequest = new ItemShoppingCartCheckRequest();
        itemShoppingCartCheckRequest.setStoreGuid("storeGuid");
        itemShoppingCartCheckRequest.setIsTakeaway(false);
        final SingleShoppingCartCheck singleShoppingCartCheck = new SingleShoppingCartCheck();
        singleShoppingCartCheck.setSkuGuid("skuGuid");
        singleShoppingCartCheck.setSkuName("skuName");
        singleShoppingCartCheck.setStock(new BigDecimal("0.00"));
        itemShoppingCartCheckRequest.setData(Arrays.asList(singleShoppingCartCheck));

        final ItemStockResp expectedResult = new ItemStockResp("code", "message");

        // Configure ISkuService.list(...).
        final SkuDO skuDO = new SkuDO();
        skuDO.setGuid("guid");
        skuDO.setStoreGuid("storeGuid");
        skuDO.setItemGuid("itemGuid");
        skuDO.setStock(new BigDecimal("0.00"));
        skuDO.setName("name");
        final List<SkuDO> skuDOS = Arrays.asList(skuDO);
        when(mockSkuService.list(any(LambdaQueryWrapper.class))).thenReturn(skuDOS);

        // Configure IEstimateService.descStock(...).
        final EstimateResultRespDTO estimateResultRespDTO = new EstimateResultRespDTO(false, "errorMsg",
                Arrays.asList("value"));
        when(mockIEstimateService.descStock(Arrays.asList(
                new ItemSkuStockDTO("skuGuid", new BigDecimal("0.00"), "productName",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0))))).thenReturn(estimateResultRespDTO);

        // Run the test
        final ItemStockResp result = iMiniAppServiceImplUnderTest.descStock(itemShoppingCartCheckRequest);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testDescStock_ISkuServiceReturnsNoItems() {
        // Setup
        final ItemShoppingCartCheckRequest itemShoppingCartCheckRequest = new ItemShoppingCartCheckRequest();
        itemShoppingCartCheckRequest.setStoreGuid("storeGuid");
        itemShoppingCartCheckRequest.setIsTakeaway(false);
        final SingleShoppingCartCheck singleShoppingCartCheck = new SingleShoppingCartCheck();
        singleShoppingCartCheck.setSkuGuid("skuGuid");
        singleShoppingCartCheck.setSkuName("skuName");
        singleShoppingCartCheck.setStock(new BigDecimal("0.00"));
        itemShoppingCartCheckRequest.setData(Arrays.asList(singleShoppingCartCheck));

        final ItemStockResp expectedResult = new ItemStockResp("code", "message");
        when(mockSkuService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure IEstimateService.descStock(...).
        final EstimateResultRespDTO estimateResultRespDTO = new EstimateResultRespDTO(false, "errorMsg",
                Arrays.asList("value"));
        when(mockIEstimateService.descStock(Arrays.asList(
                new ItemSkuStockDTO("skuGuid", new BigDecimal("0.00"), "productName",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0))))).thenReturn(estimateResultRespDTO);

        // Run the test
        final ItemStockResp result = iMiniAppServiceImplUnderTest.descStock(itemShoppingCartCheckRequest);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
