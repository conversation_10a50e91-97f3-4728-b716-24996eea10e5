package com.holderzone.saas.store.item.manage;

import com.holderzone.saas.store.dto.item.req.ItemQueryListReq;
import com.holderzone.saas.store.dto.item.resp.*;
import com.holderzone.saas.store.item.repository.EstimateRepository;
import com.holderzone.saas.store.item.service.IItemService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ItemManageTest {

    @Mock
    private IItemService mockItemService;
    @Mock
    private EstimateRepository mockEstimateRepository;

    private ItemManage itemManageUnderTest;

    @Before
    public void setUp() throws Exception {
        itemManageUnderTest = new ItemManage(mockItemService, mockEstimateRepository);
    }

    @Test
    public void testGetItemInfoEstimate() {
        // Setup
        final ItemQueryListReq req = new ItemQueryListReq();
        req.setStoreGuid("storeGuid");
        req.setSceneCode("sceneCode");
        req.setIsRack(0);
        req.setIsJoinAio(0);
        req.setIsJoinPos(0);

        final ItemInfoEstimateSingleDTO expectedResult = new ItemInfoEstimateSingleDTO();
        final ItemSynRespDTO itemInfo = new ItemSynRespDTO();
        itemInfo.setItemGuid("itemGuid");
        final SkuSynRespDTO skuSynRespDTO = new SkuSynRespDTO();
        skuSynRespDTO.setSkuGuid("skuGuid");
        itemInfo.setSkuList(Arrays.asList(skuSynRespDTO));
        expectedResult.setItemInfo(itemInfo);
        final ItemEstimateForAndroidRespDTO itemEstimateForAndroidRespDTO = new ItemEstimateForAndroidRespDTO();
        expectedResult.setItemEstimateList(Arrays.asList(itemEstimateForAndroidRespDTO));

        // Configure IItemService.selectItemDetailAndTypeForSyn(...).
        final ItemAndTypeForAndroidRespDTO itemAndTypeForAndroidRespDTO = new ItemAndTypeForAndroidRespDTO();
        final ItemSynRespDTO itemSynRespDTO = new ItemSynRespDTO();
        itemSynRespDTO.setItemGuid("itemGuid");
        itemSynRespDTO.setParentGuid("parentGuid");
        final SkuSynRespDTO skuSynRespDTO1 = new SkuSynRespDTO();
        skuSynRespDTO1.setSkuGuid("skuGuid");
        itemSynRespDTO.setSkuList(Arrays.asList(skuSynRespDTO1));
        itemAndTypeForAndroidRespDTO.setItemList(Arrays.asList(itemSynRespDTO));
        final ItemQueryListReq req1 = new ItemQueryListReq();
        req1.setStoreGuid("storeGuid");
        req1.setSceneCode("sceneCode");
        req1.setIsRack(0);
        req1.setIsJoinAio(0);
        req1.setIsJoinPos(0);
        when(mockItemService.selectItemDetailAndTypeForSyn(req1)).thenReturn(itemAndTypeForAndroidRespDTO);

        // Configure EstimateRepository.listEstimateBySkuGuid(...).
        final List<ItemEstimateForAndroidRespDTO> itemEstimateForAndroidRespDTOS = Arrays.asList(
                new ItemEstimateForAndroidRespDTO("skuGuid", 0, new BigDecimal("0.00"), new BigDecimal("0.00")));
        Set<String> set = new HashSet<>();
        set.add("value");
        when(mockEstimateRepository.listEstimateBySkuGuid(set, "storeGuid"))
                .thenReturn(itemEstimateForAndroidRespDTOS);

        // Run the test
        final ItemInfoEstimateSingleDTO result = itemManageUnderTest.getItemInfoEstimate(req);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetItemInfoEstimate_IItemServiceReturnsNull() {
        // Setup
        final ItemQueryListReq req = new ItemQueryListReq();
        req.setStoreGuid("storeGuid");
        req.setSceneCode("sceneCode");
        req.setIsRack(0);
        req.setIsJoinAio(0);
        req.setIsJoinPos(0);

        final ItemInfoEstimateSingleDTO expectedResult = new ItemInfoEstimateSingleDTO();
        final ItemSynRespDTO itemInfo = new ItemSynRespDTO();
        itemInfo.setItemGuid("itemGuid");
        final SkuSynRespDTO skuSynRespDTO = new SkuSynRespDTO();
        skuSynRespDTO.setSkuGuid("skuGuid");
        itemInfo.setSkuList(Arrays.asList(skuSynRespDTO));
        expectedResult.setItemInfo(itemInfo);
        final ItemEstimateForAndroidRespDTO itemEstimateForAndroidRespDTO = new ItemEstimateForAndroidRespDTO();
        expectedResult.setItemEstimateList(Arrays.asList(itemEstimateForAndroidRespDTO));

        // Configure IItemService.selectItemDetailAndTypeForSyn(...).
        final ItemQueryListReq req1 = new ItemQueryListReq();
        req1.setStoreGuid("storeGuid");
        req1.setSceneCode("sceneCode");
        req1.setIsRack(0);
        req1.setIsJoinAio(0);
        req1.setIsJoinPos(0);
        when(mockItemService.selectItemDetailAndTypeForSyn(req1)).thenReturn(null);

        // Run the test
        final ItemInfoEstimateSingleDTO result = itemManageUnderTest.getItemInfoEstimate(req);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetItemInfoEstimate_EstimateRepositoryReturnsNoItems() {
        // Setup
        final ItemQueryListReq req = new ItemQueryListReq();
        req.setStoreGuid("storeGuid");
        req.setSceneCode("sceneCode");
        req.setIsRack(0);
        req.setIsJoinAio(0);
        req.setIsJoinPos(0);

        final ItemInfoEstimateSingleDTO expectedResult = new ItemInfoEstimateSingleDTO();
        final ItemSynRespDTO itemInfo = new ItemSynRespDTO();
        itemInfo.setItemGuid("itemGuid");
        final SkuSynRespDTO skuSynRespDTO = new SkuSynRespDTO();
        skuSynRespDTO.setSkuGuid("skuGuid");
        itemInfo.setSkuList(Arrays.asList(skuSynRespDTO));
        expectedResult.setItemInfo(itemInfo);
        final ItemEstimateForAndroidRespDTO itemEstimateForAndroidRespDTO = new ItemEstimateForAndroidRespDTO();
        expectedResult.setItemEstimateList(Arrays.asList(itemEstimateForAndroidRespDTO));

        // Configure IItemService.selectItemDetailAndTypeForSyn(...).
        final ItemAndTypeForAndroidRespDTO itemAndTypeForAndroidRespDTO = new ItemAndTypeForAndroidRespDTO();
        final ItemSynRespDTO itemSynRespDTO = new ItemSynRespDTO();
        itemSynRespDTO.setItemGuid("itemGuid");
        itemSynRespDTO.setParentGuid("parentGuid");
        final SkuSynRespDTO skuSynRespDTO1 = new SkuSynRespDTO();
        skuSynRespDTO1.setSkuGuid("skuGuid");
        itemSynRespDTO.setSkuList(Arrays.asList(skuSynRespDTO1));
        itemAndTypeForAndroidRespDTO.setItemList(Arrays.asList(itemSynRespDTO));
        final ItemQueryListReq req1 = new ItemQueryListReq();
        req1.setStoreGuid("storeGuid");
        req1.setSceneCode("sceneCode");
        req1.setIsRack(0);
        req1.setIsJoinAio(0);
        req1.setIsJoinPos(0);
        when(mockItemService.selectItemDetailAndTypeForSyn(req1)).thenReturn(itemAndTypeForAndroidRespDTO);
        Set<String> set = new HashSet<>();
        set.add("value");
        when(mockEstimateRepository.listEstimateBySkuGuid(set, "storeGuid"))
                .thenReturn(Collections.emptyList());

        // Run the test
        final ItemInfoEstimateSingleDTO result = itemManageUnderTest.getItemInfoEstimate(req);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
