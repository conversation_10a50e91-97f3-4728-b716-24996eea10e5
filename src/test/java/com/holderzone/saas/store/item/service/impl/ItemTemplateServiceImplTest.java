

package com.holderzone.saas.store.item.service.impl;

import static org.mockito.MockitoAnnotations.initMocks;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import java.math.BigDecimal;import java.time.LocalDateTime;import java.util.Arrays;import java.util.Collections;import java.util.List;import java.util.concurrent.Callable;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.assertj.core.api.Assertions.within;
    import static org.mockito.ArgumentMatchers.any;
    import static org.mockito.ArgumentMatchers.anyInt;
    import static org.mockito.ArgumentMatchers.anyString;
    import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import com.holderzone.framework.exception.unchecked.ParameterException;import com.holderzone.framework.util.Page;import com.holderzone.saas.store.dto.item.req.ItemTemplateReqDTO;import com.holderzone.saas.store.dto.item.req.ItemTemplateSearchReqDTO;import com.holderzone.saas.store.dto.item.resp.ItemTemplateMenuSubItemDetailRespDTO;import com.holderzone.saas.store.dto.item.resp.ItemTemplateRespDTO;import com.holderzone.saas.store.dto.item.resp.ItemTemplatesRespDTO;import com.holderzone.saas.store.item.entity.domain.ItemTemplateDO;import com.holderzone.saas.store.item.entity.query.ItemTemplateExecuteTimeQuery;import com.holderzone.saas.store.item.helper.ItemHelper;import com.holderzone.saas.store.item.helper.PageAdapter;import com.holderzone.saas.store.item.mapper.ItemTemplateMapper;import com.holderzone.saas.store.item.service.IItemTMenuSubitemService;import com.holderzone.saas.store.item.service.IItemTMenuValidityService;import org.mockito.junit.MockitoJUnitRunner;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.InjectMocks;
import org.mockito.stubbing.Answer;import org.springframework.data.redis.core.RedisTemplate;

@RunWith(MockitoJUnitRunner.class)
public class ItemTemplateServiceImplTest {

            @Mock
        private ItemTemplateMapper mockItemTemplateMapper;
            @Mock
        private IItemTMenuValidityService mockItemTMenuValidityService;
            @Mock
        private ItemHelper mockItemHelper;
            @Mock
        private IItemTMenuSubitemService mockItemTMenuSubitemService;
            @Mock
        private RedisTemplate mockRedisTemplate;

    private ItemTemplateServiceImpl itemTemplateServiceImplUnderTest;

@Before
public void setUp() throws Exception {
            itemTemplateServiceImplUnderTest = new ItemTemplateServiceImpl(mockItemTemplateMapper,mockItemTMenuValidityService,mockItemHelper,mockItemTMenuSubitemService,mockRedisTemplate) ;
}
                
    @Test
    public void testSaveOrUpdate() throws Exception {
    // Setup
                        final ItemTemplateReqDTO request = new ItemTemplateReqDTO();
                request.setGuid("531f0908-2c56-4aee-bbc9-95ebe4684a62");
                request.setStoreGuid("storeGuid");
                request.setEffectiveStartTime("effectiveStartTime");
                request.setEffectiveEndTime("effectiveEndTime");
                request.setIsDelete(0);

            // Configure ItemHelper.validateTemplateTime(...).
        final ItemTemplateReqDTO request1 = new ItemTemplateReqDTO();
                request1.setGuid("531f0908-2c56-4aee-bbc9-95ebe4684a62");
                request1.setStoreGuid("storeGuid");
                request1.setEffectiveStartTime("effectiveStartTime");
                request1.setEffectiveEndTime("effectiveEndTime");
                request1.setIsDelete(0);
                                                        final ItemTemplateDO itemTemplateDO = new ItemTemplateDO();
                itemTemplateDO.setGuid("d6337708-7a09-4ae3-9239-f8b6c2ca9125");
                itemTemplateDO.setStoreGuid("storeGuid");
                itemTemplateDO.setTemplateName("templateName");
                itemTemplateDO.setEffectiveStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
                itemTemplateDO.setEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<ItemTemplateDO> itemTemplateDOS = Arrays.asList(itemTemplateDO);
            when( mockItemHelper .validateTemplateTime(request1,itemTemplateDOS)).thenReturn( true );

    // Run the test
 final Boolean result =  itemTemplateServiceImplUnderTest.saveOrUpdate(request);

        // Verify the results
 assertThat(result).isFalse() ;
            verify( mockItemHelper ).pushMsg("storeGuid");
    }
                                                                                                        
    @Test
    public void testSaveOrUpdate_ItemHelperValidateTemplateTimeReturnsFalse() throws Exception {
    // Setup
                        final ItemTemplateReqDTO request = new ItemTemplateReqDTO();
                request.setGuid("531f0908-2c56-4aee-bbc9-95ebe4684a62");
                request.setStoreGuid("storeGuid");
                request.setEffectiveStartTime("effectiveStartTime");
                request.setEffectiveEndTime("effectiveEndTime");
                request.setIsDelete(0);

        // Configure ItemHelper.validateTemplateTime(...).
        final ItemTemplateReqDTO request1 = new ItemTemplateReqDTO();
                request1.setGuid("531f0908-2c56-4aee-bbc9-95ebe4684a62");
                request1.setStoreGuid("storeGuid");
                request1.setEffectiveStartTime("effectiveStartTime");
                request1.setEffectiveEndTime("effectiveEndTime");
                request1.setIsDelete(0);
                                                        final ItemTemplateDO itemTemplateDO = new ItemTemplateDO();
                itemTemplateDO.setGuid("d6337708-7a09-4ae3-9239-f8b6c2ca9125");
                itemTemplateDO.setStoreGuid("storeGuid");
                itemTemplateDO.setTemplateName("templateName");
                itemTemplateDO.setEffectiveStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
                itemTemplateDO.setEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<ItemTemplateDO> itemTemplateDOS = Arrays.asList(itemTemplateDO);
        when( mockItemHelper .validateTemplateTime(request1,itemTemplateDOS)).thenReturn( false );

    // Run the test
        assertThatThrownBy(() -> itemTemplateServiceImplUnderTest.saveOrUpdate(request)).isInstanceOf(ParameterException.class);
    }
                
    @Test
    public void testGetStoreItemTemplates() throws Exception {
    // Setup
                        final ItemTemplateSearchReqDTO request = new ItemTemplateSearchReqDTO();
                request.setStoreGuid("storeGuid");
                request.setItActivated(0);
                request.setStartTime("startTime");
                request.setEndTime("endTime");
                request.setStatus(0);
 
                                                         final ItemTemplateRespDTO itemTemplateRespDTO = new ItemTemplateRespDTO();
                itemTemplateRespDTO.setGuid("9b4c1ff3-07aa-45c6-b50f-6307f73a6f50");
                itemTemplateRespDTO.setStoreGuid("storeGuid");
                itemTemplateRespDTO.setTemplateName("templateName");
                itemTemplateRespDTO.setEffectiveStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
                itemTemplateRespDTO.setEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final ItemTemplatesRespDTO expectedResult = new ItemTemplatesRespDTO("currentTemplateName", new Page<>(0L, 0L, Arrays.asList(itemTemplateRespDTO)));
                
            // Configure ItemTemplateMapper.getStoreCurrentUsedTemplateName(...).
        final ItemTemplateDO itemTemplateDO = new ItemTemplateDO();
                itemTemplateDO.setGuid("d6337708-7a09-4ae3-9239-f8b6c2ca9125");
                itemTemplateDO.setStoreGuid("storeGuid");
                itemTemplateDO.setTemplateName("templateName");
                itemTemplateDO.setEffectiveStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
                itemTemplateDO.setEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
            when( mockItemTemplateMapper .getStoreCurrentUsedTemplateName("storeGuid")).thenReturn(itemTemplateDO);

            // Configure ItemTemplateMapper.getStoreItemTemplates(...).
        final ItemTemplateSearchReqDTO requset = new ItemTemplateSearchReqDTO();
                requset.setStoreGuid("storeGuid");
                requset.setItActivated(0);
                requset.setStartTime("startTime");
                requset.setEndTime("endTime");
                requset.setStatus(0);
            when( mockItemTemplateMapper .getStoreItemTemplates(any(PageAdapter.class),eq(requset))).thenReturn( null );

    // Run the test
 final ItemTemplatesRespDTO result =  itemTemplateServiceImplUnderTest.getStoreItemTemplates(request);

        // Verify the results
 assertThat(result).isEqualTo(expectedResult ) ;
    }
                                                                                                
    @Test
    public void testGetStoreItemTemplates_ItemTemplateMapperGetStoreCurrentUsedTemplateNameReturnsNull() throws Exception {
    // Setup
                        final ItemTemplateSearchReqDTO request = new ItemTemplateSearchReqDTO();
                request.setStoreGuid("storeGuid");
                request.setItActivated(0);
                request.setStartTime("startTime");
                request.setEndTime("endTime");
                request.setStatus(0);
 
                                                         final ItemTemplateRespDTO itemTemplateRespDTO = new ItemTemplateRespDTO();
                itemTemplateRespDTO.setGuid("9b4c1ff3-07aa-45c6-b50f-6307f73a6f50");
                itemTemplateRespDTO.setStoreGuid("storeGuid");
                itemTemplateRespDTO.setTemplateName("templateName");
                itemTemplateRespDTO.setEffectiveStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
                itemTemplateRespDTO.setEffectiveEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final ItemTemplatesRespDTO expectedResult = new ItemTemplatesRespDTO("currentTemplateName", new Page<>(0L, 0L, Arrays.asList(itemTemplateRespDTO)));
        when( mockItemTemplateMapper .getStoreCurrentUsedTemplateName("storeGuid")).thenReturn( null );
                
            // Configure ItemTemplateMapper.getStoreItemTemplates(...).
        final ItemTemplateSearchReqDTO requset = new ItemTemplateSearchReqDTO();
                requset.setStoreGuid("storeGuid");
                requset.setItActivated(0);
                requset.setStartTime("startTime");
                requset.setEndTime("endTime");
                requset.setStatus(0);
            when( mockItemTemplateMapper .getStoreItemTemplates(any(PageAdapter.class),eq(requset))).thenReturn( null );

    // Run the test
 final ItemTemplatesRespDTO result =  itemTemplateServiceImplUnderTest.getStoreItemTemplates(request);

        // Verify the results
 assertThat(result).isEqualTo(expectedResult ) ;
    }
                                                
    @Test
    public void testGetStoreGuidByCode() throws Exception {
    // Setup
                                            when( mockItemTemplateMapper .getStoreGuidByCode(0,"8dc4050f-00b7-43be-80bc-6cd96fa12d5c")).thenReturn( Arrays.asList("value") );

    // Run the test
 final String result =  itemTemplateServiceImplUnderTest.getStoreGuidByCode(0,"8dc4050f-00b7-43be-80bc-6cd96fa12d5c");

        // Verify the results
 assertThat(result).isEqualTo( "result" ) ;
    }
                                                                                                
    @Test
    public void testGetStoreGuidByCode_ItemTemplateMapperReturnsNoItems() throws Exception {
    // Setup
                        when( mockItemTemplateMapper .getStoreGuidByCode(0,"8dc4050f-00b7-43be-80bc-6cd96fa12d5c")).thenReturn( Collections.emptyList() );

    // Run the test
 final String result =  itemTemplateServiceImplUnderTest.getStoreGuidByCode(0,"8dc4050f-00b7-43be-80bc-6cd96fa12d5c");

        // Verify the results
                assertThat(result).isNull();
    }
                
    @Test
    public void testGetNowMeunSubItemForSyn() throws Exception {
    // Setup
                        final List<ItemTemplateMenuSubItemDetailRespDTO> expectedResult = Arrays.asList(new ItemTemplateMenuSubItemDetailRespDTO("0613f51d-c011-4d49-990b-f318b64020ae", "name", "skuGuid", "skuName", "unit", "typeGuid", "typeName", new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00")));
                
            // Configure IItemTMenuValidityService.getNowMenuExecuteTimes(...).
                                                        final ItemTemplateExecuteTimeQuery itemTemplateExecuteTimeQuery = new ItemTemplateExecuteTimeQuery();
                itemTemplateExecuteTimeQuery.setGuid("316cbf3e-b919-406b-b465-1cff62a322df");
                itemTemplateExecuteTimeQuery.setDifference(0);
                itemTemplateExecuteTimeQuery.setNow(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
                itemTemplateExecuteTimeQuery.setWeeks("weeks");
                itemTemplateExecuteTimeQuery.setTimes("times");
        final List<ItemTemplateExecuteTimeQuery> itemTemplateExecuteTimeQueries = Arrays.asList(itemTemplateExecuteTimeQuery);
            when( mockItemTMenuValidityService .getNowMenuExecuteTimes("storeGuid")).thenReturn(itemTemplateExecuteTimeQueries);

            // Configure ItemHelper.getNowExecuteMenu(...).
                                                        final ItemTemplateExecuteTimeQuery itemTemplateExecuteTimeQuery1 = new ItemTemplateExecuteTimeQuery();
                itemTemplateExecuteTimeQuery1.setGuid("316cbf3e-b919-406b-b465-1cff62a322df");
                itemTemplateExecuteTimeQuery1.setDifference(0);
                itemTemplateExecuteTimeQuery1.setNow(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
                itemTemplateExecuteTimeQuery1.setWeeks("weeks");
                itemTemplateExecuteTimeQuery1.setTimes("times");
        final List<ItemTemplateExecuteTimeQuery> list = Arrays.asList(itemTemplateExecuteTimeQuery1);
            when( mockItemHelper .getNowExecuteMenu(list)).thenReturn( "menuGuid" );

            // Configure IItemTMenuSubitemService.getNowMeunSubItemForSyn(...).
        final List<ItemTemplateMenuSubItemDetailRespDTO> itemTemplateMenuSubItemDetailRespDTOS = Arrays.asList(new ItemTemplateMenuSubItemDetailRespDTO("0613f51d-c011-4d49-990b-f318b64020ae", "name", "skuGuid", "skuName", "unit", "typeGuid", "typeName", new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00")));
            when( mockItemTMenuSubitemService .getNowMeunSubItemForSyn("menuGuid")).thenReturn(itemTemplateMenuSubItemDetailRespDTOS);

    // Run the test
 final List<ItemTemplateMenuSubItemDetailRespDTO> result =  itemTemplateServiceImplUnderTest.getNowMeunSubItemForSyn("storeGuid");

        // Verify the results
 assertThat(result).isEqualTo(expectedResult ) ;
    }
                                                                                                
    @Test
    public void testGetNowMeunSubItemForSyn_IItemTMenuValidityServiceReturnsNull() throws Exception {
    // Setup
                        when( mockItemTMenuValidityService .getNowMenuExecuteTimes("storeGuid")).thenReturn( null );

    // Run the test
 final List<ItemTemplateMenuSubItemDetailRespDTO> result =  itemTemplateServiceImplUnderTest.getNowMeunSubItemForSyn("storeGuid");

        // Verify the results
 assertThat(result).isEqualTo(Collections.emptyList() ) ;
    }
                                        
    @Test
    public void testGetNowMeunSubItemForSyn_IItemTMenuValidityServiceReturnsNoItems() throws Exception {
    // Setup
                        when( mockItemTMenuValidityService .getNowMenuExecuteTimes("storeGuid")).thenReturn( Collections.emptyList() );

    // Run the test
 final List<ItemTemplateMenuSubItemDetailRespDTO> result =  itemTemplateServiceImplUnderTest.getNowMeunSubItemForSyn("storeGuid");

        // Verify the results
 assertThat(result).isEqualTo(Collections.emptyList() ) ;
    }
                                                                        
    @Test
    public void testGetNowMeunSubItemForSyn_ItemHelperReturnsNull() throws Exception {
    // Setup
            // Configure IItemTMenuValidityService.getNowMenuExecuteTimes(...).
                                                        final ItemTemplateExecuteTimeQuery itemTemplateExecuteTimeQuery = new ItemTemplateExecuteTimeQuery();
                itemTemplateExecuteTimeQuery.setGuid("316cbf3e-b919-406b-b465-1cff62a322df");
                itemTemplateExecuteTimeQuery.setDifference(0);
                itemTemplateExecuteTimeQuery.setNow(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
                itemTemplateExecuteTimeQuery.setWeeks("weeks");
                itemTemplateExecuteTimeQuery.setTimes("times");
        final List<ItemTemplateExecuteTimeQuery> itemTemplateExecuteTimeQueries = Arrays.asList(itemTemplateExecuteTimeQuery);
            when( mockItemTMenuValidityService .getNowMenuExecuteTimes("storeGuid")).thenReturn(itemTemplateExecuteTimeQueries);

            // Configure ItemHelper.getNowExecuteMenu(...).
                                                        final ItemTemplateExecuteTimeQuery itemTemplateExecuteTimeQuery1 = new ItemTemplateExecuteTimeQuery();
                itemTemplateExecuteTimeQuery1.setGuid("316cbf3e-b919-406b-b465-1cff62a322df");
                itemTemplateExecuteTimeQuery1.setDifference(0);
                itemTemplateExecuteTimeQuery1.setNow(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
                itemTemplateExecuteTimeQuery1.setWeeks("weeks");
                itemTemplateExecuteTimeQuery1.setTimes("times");
        final List<ItemTemplateExecuteTimeQuery> list = Arrays.asList(itemTemplateExecuteTimeQuery1);
        when( mockItemHelper .getNowExecuteMenu(list)).thenReturn( null );

    // Run the test
 final List<ItemTemplateMenuSubItemDetailRespDTO> result =  itemTemplateServiceImplUnderTest.getNowMeunSubItemForSyn("storeGuid");

        // Verify the results
 assertThat(result).isEqualTo(Collections.emptyList() ) ;
    }
                                                                        
    @Test
    public void testGetNowMeunSubItemForSyn_IItemTMenuSubitemServiceReturnsNoItems() throws Exception {
    // Setup
            // Configure IItemTMenuValidityService.getNowMenuExecuteTimes(...).
                                                        final ItemTemplateExecuteTimeQuery itemTemplateExecuteTimeQuery = new ItemTemplateExecuteTimeQuery();
                itemTemplateExecuteTimeQuery.setGuid("316cbf3e-b919-406b-b465-1cff62a322df");
                itemTemplateExecuteTimeQuery.setDifference(0);
                itemTemplateExecuteTimeQuery.setNow(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
                itemTemplateExecuteTimeQuery.setWeeks("weeks");
                itemTemplateExecuteTimeQuery.setTimes("times");
        final List<ItemTemplateExecuteTimeQuery> itemTemplateExecuteTimeQueries = Arrays.asList(itemTemplateExecuteTimeQuery);
            when( mockItemTMenuValidityService .getNowMenuExecuteTimes("storeGuid")).thenReturn(itemTemplateExecuteTimeQueries);

            // Configure ItemHelper.getNowExecuteMenu(...).
                                                        final ItemTemplateExecuteTimeQuery itemTemplateExecuteTimeQuery1 = new ItemTemplateExecuteTimeQuery();
                itemTemplateExecuteTimeQuery1.setGuid("316cbf3e-b919-406b-b465-1cff62a322df");
                itemTemplateExecuteTimeQuery1.setDifference(0);
                itemTemplateExecuteTimeQuery1.setNow(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
                itemTemplateExecuteTimeQuery1.setWeeks("weeks");
                itemTemplateExecuteTimeQuery1.setTimes("times");
        final List<ItemTemplateExecuteTimeQuery> list = Arrays.asList(itemTemplateExecuteTimeQuery1);
            when( mockItemHelper .getNowExecuteMenu(list)).thenReturn( "menuGuid" );
 
         when( mockItemTMenuSubitemService .getNowMeunSubItemForSyn("menuGuid")).thenReturn( Collections.emptyList() );

    // Run the test
 final List<ItemTemplateMenuSubItemDetailRespDTO> result =  itemTemplateServiceImplUnderTest.getNowMeunSubItemForSyn("storeGuid");

        // Verify the results
 assertThat(result).isEqualTo(Collections.emptyList() ) ;
    }
}

