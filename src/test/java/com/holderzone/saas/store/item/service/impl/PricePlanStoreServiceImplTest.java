package com.holderzone.saas.store.item.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.saas.store.dto.item.req.PricePlanReqDTO;
import com.holderzone.saas.store.dto.item.req.PricePlanStoreReqDTO;
import com.holderzone.saas.store.dto.item.resp.*;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.store.store.StoreProductDTO;
import com.holderzone.saas.store.item.entity.domain.PricePlanDO;
import com.holderzone.saas.store.item.entity.domain.PricePlanStoreDO;
import com.holderzone.saas.store.item.mapper.PricePlanMapper;
import com.holderzone.saas.store.item.mapper.PricePlanStoreMapper;
import com.holderzone.saas.store.item.service.rpc.OrganizationService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PricePlanStoreServiceImplTest {

    @Mock
    private OrganizationService mockOrganizationService;
    @Mock
    private PricePlanStoreMapper mockPricePlanStoreMapper;
    @Mock
    private PricePlanMapper mockPricePlanMapper;

    private PricePlanStoreServiceImpl pricePlanStoreServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        pricePlanStoreServiceImplUnderTest = new PricePlanStoreServiceImpl(mockOrganizationService,
                mockPricePlanStoreMapper, mockPricePlanMapper);
    }

    @Test
    public void testStoreControlList() {
        // Setup
        final PricePlanStoreReqDTO reqDTO = new PricePlanStoreReqDTO();
        reqDTO.setStoreGuidList(Arrays.asList("value"));
        reqDTO.setBrandGuid("brandGuid");
        reqDTO.setPlanGuid("planGuid");

        final PricePlanStoreRespDTO pricePlanStoreRespDTO = new PricePlanStoreRespDTO();
        pricePlanStoreRespDTO.setStoreGuid("guid");
        pricePlanStoreRespDTO.setStoreName("name");
        pricePlanStoreRespDTO.setHasPricePlan(false);
        pricePlanStoreRespDTO.setIsCurrent(false);
        pricePlanStoreRespDTO.setIsCurrentUser(false);
        final List<PricePlanStoreRespDTO> expectedResult = Arrays.asList(pricePlanStoreRespDTO);

        // Configure OrganizationService.queryStoreByBrandList(...).
        final List<StoreDTO> storeDTOS = Arrays.asList(
                new StoreDTO("88356c81-cc18-4cde-997d-8c87539025c8", "code", "name", "belongBrandGuid",
                        "belongBrandName", "parentIds", LocalTime.of(0, 0, 0), LocalTime.of(0, 0, 0), "contactName",
                        "contactTel", "provinceCode", "provinceName", "cityCode", "cityName", "countyCode",
                        "countyName", "addressDetail", "longitude", "latitude", false, false, "photos",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "createUserGuid",
                        "modifiedUserGuid", Arrays.asList(
                        new StoreProductDTO("productGuid", "productName", 0, LocalDate.of(2020, 1, 1),
                                LocalDate.of(2020, 1, 1), LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, 0)),
                        Arrays.asList(new BrandDTO("395ca347-59c0-4168-8ddf-50f81fdd9337",
                                "6cc78538-d601-4a03-9334-eb130ff61473", "name", "description", "logoUrl", false, false,
                                "createUserGuid", "modifiedUserGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "mchntTypeCode", Arrays.asList(), 0, 0, 0, 0, false)),
                        "mchntTypeCode", "logoUrl", "openingHours", 0, 0, 0, 0, "storeNotification", "storeDoorPhoto",
                        0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, 0, 0,
                        LocalDate.of(2020, 1, 1)));
        when(mockOrganizationService.queryStoreByBrandList(Arrays.asList("value"))).thenReturn(storeDTOS);

        // Configure PricePlanStoreMapper.selectList(...).
        final PricePlanStoreDO pricePlanStoreDO = new PricePlanStoreDO();
        pricePlanStoreDO.setIsDelete(0);
        pricePlanStoreDO.setGuid("f62691d6-cbab-4d49-9d25-631577de4bb0");
        pricePlanStoreDO.setBrandGuid("brandGuid");
        pricePlanStoreDO.setPlanGuid("planGuid");
        pricePlanStoreDO.setStoreGuid("storeGuid");
        final List<PricePlanStoreDO> pricePlanStoreDOS = Arrays.asList(pricePlanStoreDO);
        when(mockPricePlanStoreMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(pricePlanStoreDOS);

        // Run the test
        final List<PricePlanStoreRespDTO> result = pricePlanStoreServiceImplUnderTest.storeControlList(reqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testStoreControlList_OrganizationServiceReturnsNoItems() {
        // Setup
        final PricePlanStoreReqDTO reqDTO = new PricePlanStoreReqDTO();
        reqDTO.setStoreGuidList(Arrays.asList("value"));
        reqDTO.setBrandGuid("brandGuid");
        reqDTO.setPlanGuid("planGuid");

        when(mockOrganizationService.queryStoreByBrandList(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        final List<PricePlanStoreRespDTO> result = pricePlanStoreServiceImplUnderTest.storeControlList(reqDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testStoreControlList_PricePlanStoreMapperReturnsNoItems() {
        // Setup
        final PricePlanStoreReqDTO reqDTO = new PricePlanStoreReqDTO();
        reqDTO.setStoreGuidList(Arrays.asList("value"));
        reqDTO.setBrandGuid("brandGuid");
        reqDTO.setPlanGuid("planGuid");

        final PricePlanStoreRespDTO pricePlanStoreRespDTO = new PricePlanStoreRespDTO();
        pricePlanStoreRespDTO.setStoreGuid("guid");
        pricePlanStoreRespDTO.setStoreName("name");
        pricePlanStoreRespDTO.setHasPricePlan(false);
        pricePlanStoreRespDTO.setIsCurrent(false);
        pricePlanStoreRespDTO.setIsCurrentUser(false);
        final List<PricePlanStoreRespDTO> expectedResult = Arrays.asList(pricePlanStoreRespDTO);

        // Configure OrganizationService.queryStoreByBrandList(...).
        final List<StoreDTO> storeDTOS = Arrays.asList(
                new StoreDTO("88356c81-cc18-4cde-997d-8c87539025c8", "code", "name", "belongBrandGuid",
                        "belongBrandName", "parentIds", LocalTime.of(0, 0, 0), LocalTime.of(0, 0, 0), "contactName",
                        "contactTel", "provinceCode", "provinceName", "cityCode", "cityName", "countyCode",
                        "countyName", "addressDetail", "longitude", "latitude", false, false, "photos",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "createUserGuid",
                        "modifiedUserGuid", Arrays.asList(
                        new StoreProductDTO("productGuid", "productName", 0, LocalDate.of(2020, 1, 1),
                                LocalDate.of(2020, 1, 1), LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, 0)),
                        Arrays.asList(new BrandDTO("395ca347-59c0-4168-8ddf-50f81fdd9337",
                                "6cc78538-d601-4a03-9334-eb130ff61473", "name", "description", "logoUrl", false, false,
                                "createUserGuid", "modifiedUserGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "mchntTypeCode", Arrays.asList(), 0, 0, 0, 0, false)),
                        "mchntTypeCode", "logoUrl", "openingHours", 0, 0, 0, 0, "storeNotification", "storeDoorPhoto",
                        0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, 0, 0,
                        LocalDate.of(2020, 1, 1)));
        when(mockOrganizationService.queryStoreByBrandList(Arrays.asList("value"))).thenReturn(storeDTOS);

        when(mockPricePlanStoreMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<PricePlanStoreRespDTO> result = pricePlanStoreServiceImplUnderTest.storeControlList(reqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testBingPlanStore() {
        // Setup
        final PricePlanStoreReqDTO reqDTO = new PricePlanStoreReqDTO();
        reqDTO.setStoreGuidList(Arrays.asList("value"));
        reqDTO.setBrandGuid("brandGuid");
        reqDTO.setPlanGuid("planGuid");

        // Run the test
        final Boolean result = pricePlanStoreServiceImplUnderTest.bingPlanStore(reqDTO);

        // Verify the results
        assertThat(result).isFalse();
        verify(mockPricePlanStoreMapper).delete(any(LambdaQueryWrapper.class));
        verify(mockPricePlanMapper).updateStoreNum("planGuid", 0, 3);
    }

    @Test
    public void testDeletePlanStoreByStoreGuid() {
        // Setup
        // Configure PricePlanStoreMapper.selectList(...).
        final PricePlanStoreDO pricePlanStoreDO = new PricePlanStoreDO();
        pricePlanStoreDO.setIsDelete(0);
        pricePlanStoreDO.setGuid("f62691d6-cbab-4d49-9d25-631577de4bb0");
        pricePlanStoreDO.setBrandGuid("brandGuid");
        pricePlanStoreDO.setPlanGuid("planGuid");
        pricePlanStoreDO.setStoreGuid("storeGuid");
        final List<PricePlanStoreDO> pricePlanStoreDOS = Arrays.asList(pricePlanStoreDO);
        when(mockPricePlanStoreMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(pricePlanStoreDOS);

        when(mockPricePlanStoreMapper.delete(any(LambdaQueryWrapper.class))).thenReturn(0);

        // Run the test
        final Boolean result = pricePlanStoreServiceImplUnderTest.deletePlanStoreByStoreGuid("storeGuid");

        // Verify the results
        assertThat(result).isFalse();
        verify(mockPricePlanMapper).updateStoreNum("planGuid", 0, 3);
    }

    @Test
    public void testDeletePlanStoreByStoreGuid_PricePlanStoreMapperSelectListReturnsNoItems() {
        // Setup
        when(mockPricePlanStoreMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());
        when(mockPricePlanStoreMapper.delete(any(LambdaQueryWrapper.class))).thenReturn(0);

        // Run the test
        final Boolean result = pricePlanStoreServiceImplUnderTest.deletePlanStoreByStoreGuid("storeGuid");

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testGetPlanStoreByStoreGuid() {
        // Setup
        final PricePlanBingStoreRespDTO pricePlanBingStoreRespDTO = new PricePlanBingStoreRespDTO();
        pricePlanBingStoreRespDTO.setGuid("b6f26e2d-81f2-42e9-a466-15ec43a1d90e");
        pricePlanBingStoreRespDTO.setBrandGuid("brandGuid");
        pricePlanBingStoreRespDTO.setPlanGuid("planGuid");
        pricePlanBingStoreRespDTO.setStoreGuid("storeGuid");
        final List<PricePlanBingStoreRespDTO> expectedResult = Arrays.asList(pricePlanBingStoreRespDTO);

        // Configure PricePlanStoreMapper.selectList(...).
        final PricePlanStoreDO pricePlanStoreDO = new PricePlanStoreDO();
        pricePlanStoreDO.setIsDelete(0);
        pricePlanStoreDO.setGuid("f62691d6-cbab-4d49-9d25-631577de4bb0");
        pricePlanStoreDO.setBrandGuid("brandGuid");
        pricePlanStoreDO.setPlanGuid("planGuid");
        pricePlanStoreDO.setStoreGuid("storeGuid");
        final List<PricePlanStoreDO> pricePlanStoreDOS = Arrays.asList(pricePlanStoreDO);
        when(mockPricePlanStoreMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(pricePlanStoreDOS);

        // Run the test
        final List<PricePlanBingStoreRespDTO> result = pricePlanStoreServiceImplUnderTest.getPlanStoreByStoreGuid(
                "storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetPlanStoreByStoreGuid_PricePlanStoreMapperReturnsNoItems() {
        // Setup
        when(mockPricePlanStoreMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<PricePlanBingStoreRespDTO> result = pricePlanStoreServiceImplUnderTest.getPlanStoreByStoreGuid(
                "storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testCheckStorePricePlanRule() {
        // Setup
        final PricePlanReqDTO reqDTO = new PricePlanReqDTO();
        reqDTO.setGuid("a233a8dc-daac-4afd-9096-e38276f06a8e");
        reqDTO.setStartTime(LocalTime.of(0, 0, 0));
        reqDTO.setEndTime(LocalTime.of(0, 0, 0));
        reqDTO.setSellTimeType(0);
        reqDTO.setPushDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO.setPushType(0);
        reqDTO.setStoreGuidList(Arrays.asList("value"));

        final PricePlanStoreCheckRespDTO pricePlanStoreCheckRespDTO = new PricePlanStoreCheckRespDTO();
        pricePlanStoreCheckRespDTO.setStoreGuid("storeGuid");
        pricePlanStoreCheckRespDTO.setIsDefault(false);
        pricePlanStoreCheckRespDTO.setIsOverlapping(false);
        final List<PricePlanStoreCheckRespDTO> expectedResult = Arrays.asList(pricePlanStoreCheckRespDTO);

        // Configure PricePlanStoreMapper.findPlanStoreGuid(...).
        final PricePlanStoreInfoDTO pricePlanStoreInfoDTO = new PricePlanStoreInfoDTO();
        pricePlanStoreInfoDTO.setBrandGuid("brandGuid");
        pricePlanStoreInfoDTO.setPlanGuid("planGuid");
        pricePlanStoreInfoDTO.setStoreGuid("storeGuid");
        pricePlanStoreInfoDTO.setStartTime(LocalTime.of(0, 0, 0));
        pricePlanStoreInfoDTO.setEndTime(LocalTime.of(0, 0, 0));
        final List<PricePlanStoreInfoDTO> pricePlanStoreInfoDTOS = Arrays.asList(pricePlanStoreInfoDTO);
        final PricePlanReqDTO reqDTO1 = new PricePlanReqDTO();
        reqDTO1.setGuid("a233a8dc-daac-4afd-9096-e38276f06a8e");
        reqDTO1.setStartTime(LocalTime.of(0, 0, 0));
        reqDTO1.setEndTime(LocalTime.of(0, 0, 0));
        reqDTO1.setSellTimeType(0);
        reqDTO1.setPushDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO1.setPushType(0);
        reqDTO1.setStoreGuidList(Arrays.asList("value"));
        when(mockPricePlanStoreMapper.findPlanStoreGuid(reqDTO1)).thenReturn(pricePlanStoreInfoDTOS);

        // Run the test
        final List<PricePlanStoreCheckRespDTO> result = pricePlanStoreServiceImplUnderTest.checkStorePricePlanRule(
                reqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCheckStorePricePlanRule_PricePlanStoreMapperReturnsNoItems() {
        // Setup
        final PricePlanReqDTO reqDTO = new PricePlanReqDTO();
        reqDTO.setGuid("a233a8dc-daac-4afd-9096-e38276f06a8e");
        reqDTO.setStartTime(LocalTime.of(0, 0, 0));
        reqDTO.setEndTime(LocalTime.of(0, 0, 0));
        reqDTO.setSellTimeType(0);
        reqDTO.setPushDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO.setPushType(0);
        reqDTO.setStoreGuidList(Arrays.asList("value"));

        // Configure PricePlanStoreMapper.findPlanStoreGuid(...).
        final PricePlanReqDTO reqDTO1 = new PricePlanReqDTO();
        reqDTO1.setGuid("a233a8dc-daac-4afd-9096-e38276f06a8e");
        reqDTO1.setStartTime(LocalTime.of(0, 0, 0));
        reqDTO1.setEndTime(LocalTime.of(0, 0, 0));
        reqDTO1.setSellTimeType(0);
        reqDTO1.setPushDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO1.setPushType(0);
        reqDTO1.setStoreGuidList(Arrays.asList("value"));
        when(mockPricePlanStoreMapper.findPlanStoreGuid(reqDTO1)).thenReturn(Collections.emptyList());

        // Run the test
        final List<PricePlanStoreCheckRespDTO> result = pricePlanStoreServiceImplUnderTest.checkStorePricePlanRule(
                reqDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testFindPlanNowStoreGuid() {
        // Setup
        final PricePlanNowDTO pricePlanNowDTO = new PricePlanNowDTO();
        pricePlanNowDTO.setBrandGuid("brandGuid");
        pricePlanNowDTO.setPlanGuid("planGuid");
        pricePlanNowDTO.setStoreGuid("storeGuid");
        pricePlanNowDTO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        pricePlanNowDTO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<PricePlanNowDTO> expectedResult = Arrays.asList(pricePlanNowDTO);

        // Configure PricePlanStoreMapper.findPlanNowStoreGuid(...).
        final PricePlanNowDTO pricePlanNowDTO1 = new PricePlanNowDTO();
        pricePlanNowDTO1.setBrandGuid("brandGuid");
        pricePlanNowDTO1.setPlanGuid("planGuid");
        pricePlanNowDTO1.setStoreGuid("storeGuid");
        pricePlanNowDTO1.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        pricePlanNowDTO1.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<PricePlanNowDTO> pricePlanNowDTOS = Arrays.asList(pricePlanNowDTO1);
        when(mockPricePlanStoreMapper.findPlanNowStoreGuid(LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                "storeGuid")).thenReturn(pricePlanNowDTOS);

        // Run the test
        final List<PricePlanNowDTO> result = pricePlanStoreServiceImplUnderTest.findPlanNowStoreGuid(
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testFindPlanNowStoreGuid_PricePlanStoreMapperReturnsNoItems() {
        // Setup
        when(mockPricePlanStoreMapper.findPlanNowStoreGuid(LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                "storeGuid")).thenReturn(Collections.emptyList());

        // Run the test
        final List<PricePlanNowDTO> result = pricePlanStoreServiceImplUnderTest.findPlanNowStoreGuid(
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testFindPlanNowStoreList() {
        // Setup
        final PricePlanNowDTO pricePlanNowDTO = new PricePlanNowDTO();
        pricePlanNowDTO.setBrandGuid("brandGuid");
        pricePlanNowDTO.setPlanGuid("planGuid");
        pricePlanNowDTO.setStoreGuid("storeGuid");
        pricePlanNowDTO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        pricePlanNowDTO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<PricePlanNowDTO> expectedResult = Arrays.asList(pricePlanNowDTO);

        // Configure PricePlanStoreMapper.findPlanNowStoreGuidList(...).
        final PricePlanNowDTO pricePlanNowDTO1 = new PricePlanNowDTO();
        pricePlanNowDTO1.setBrandGuid("brandGuid");
        pricePlanNowDTO1.setPlanGuid("planGuid");
        pricePlanNowDTO1.setStoreGuid("storeGuid");
        pricePlanNowDTO1.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        pricePlanNowDTO1.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<PricePlanNowDTO> pricePlanNowDTOS = Arrays.asList(pricePlanNowDTO1);
        when(mockPricePlanStoreMapper.findPlanNowStoreGuidList(LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                Arrays.asList("value"))).thenReturn(pricePlanNowDTOS);

        // Run the test
        final List<PricePlanNowDTO> result = pricePlanStoreServiceImplUnderTest.findPlanNowStoreList(
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testFindPlanNowStoreList_PricePlanStoreMapperReturnsNoItems() {
        // Setup
        when(mockPricePlanStoreMapper.findPlanNowStoreGuidList(LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        final List<PricePlanNowDTO> result = pricePlanStoreServiceImplUnderTest.findPlanNowStoreList(
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testFindPlanMemberStoreGuid() {
        // Setup
        final PricePlanNowDTO pricePlanNowDTO = new PricePlanNowDTO();
        pricePlanNowDTO.setBrandGuid("brandGuid");
        pricePlanNowDTO.setPlanGuid("planGuid");
        pricePlanNowDTO.setStoreGuid("storeGuid");
        pricePlanNowDTO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        pricePlanNowDTO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<PricePlanNowDTO> expectedResult = Arrays.asList(pricePlanNowDTO);

        // Configure PricePlanStoreMapper.findPlanMemberStoreGuid(...).
        final PricePlanNowDTO pricePlanNowDTO1 = new PricePlanNowDTO();
        pricePlanNowDTO1.setBrandGuid("brandGuid");
        pricePlanNowDTO1.setPlanGuid("planGuid");
        pricePlanNowDTO1.setStoreGuid("storeGuid");
        pricePlanNowDTO1.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        pricePlanNowDTO1.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<PricePlanNowDTO> pricePlanNowDTOS = Arrays.asList(pricePlanNowDTO1);
        when(mockPricePlanStoreMapper.findPlanMemberStoreGuid("storeGuid")).thenReturn(pricePlanNowDTOS);

        // Run the test
        final List<PricePlanNowDTO> result = pricePlanStoreServiceImplUnderTest.findPlanMemberStoreGuid("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testFindPlanMemberStoreGuid_PricePlanStoreMapperReturnsNoItems() {
        // Setup
        when(mockPricePlanStoreMapper.findPlanMemberStoreGuid("storeGuid")).thenReturn(Collections.emptyList());

        // Run the test
        final List<PricePlanNowDTO> result = pricePlanStoreServiceImplUnderTest.findPlanMemberStoreGuid("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testFindPlanStoreGuid() {
        // Setup
        final PricePlanNowDTO pricePlanNowDTO = new PricePlanNowDTO();
        pricePlanNowDTO.setBrandGuid("brandGuid");
        pricePlanNowDTO.setPlanGuid("planGuid");
        pricePlanNowDTO.setStoreGuid("storeGuid");
        pricePlanNowDTO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        pricePlanNowDTO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<PricePlanNowDTO> expectedResult = Arrays.asList(pricePlanNowDTO);

        // Configure PricePlanStoreMapper.findPlanByStoreGuid(...).
        final PricePlanNowDTO pricePlanNowDTO1 = new PricePlanNowDTO();
        pricePlanNowDTO1.setBrandGuid("brandGuid");
        pricePlanNowDTO1.setPlanGuid("planGuid");
        pricePlanNowDTO1.setStoreGuid("storeGuid");
        pricePlanNowDTO1.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        pricePlanNowDTO1.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<PricePlanNowDTO> pricePlanNowDTOS = Arrays.asList(pricePlanNowDTO1);
        when(mockPricePlanStoreMapper.findPlanByStoreGuid("storeGuid")).thenReturn(pricePlanNowDTOS);

        // Run the test
        final List<PricePlanNowDTO> result = pricePlanStoreServiceImplUnderTest.findPlanStoreGuid("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testFindPlanStoreGuid_PricePlanStoreMapperReturnsNoItems() {
        // Setup
        when(mockPricePlanStoreMapper.findPlanByStoreGuid("storeGuid")).thenReturn(Collections.emptyList());

        // Run the test
        final List<PricePlanNowDTO> result = pricePlanStoreServiceImplUnderTest.findPlanStoreGuid("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testQueryBelongPlansByStoreGuid() {
        // Setup
        final PricePlanRespDTO pricePlanRespDTO = new PricePlanRespDTO();
        pricePlanRespDTO.setGuid("21b21461-1eee-4735-abb8-bc9ffc46452f");
        pricePlanRespDTO.setBrandGuid("brandGuid");
        pricePlanRespDTO.setName("name");
        pricePlanRespDTO.setStatus(0);
        pricePlanRespDTO.setStatusName("statusName");
        final List<PricePlanRespDTO> expectedResult = Arrays.asList(pricePlanRespDTO);

        // Configure PricePlanMapper.selectList(...).
        final PricePlanDO pricePlanDO = new PricePlanDO();
        pricePlanDO.setId(0L);
        pricePlanDO.setIsDelete(0);
        pricePlanDO.setGuid("61773ec5-415f-465b-aedf-71b22eaf7f7f");
        pricePlanDO.setStatus(0);
        pricePlanDO.setParentGuid("parentGuid");
        final List<PricePlanDO> pricePlanDOS = Arrays.asList(pricePlanDO);
        when(mockPricePlanMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(pricePlanDOS);

        // Run the test
        final List<PricePlanRespDTO> result = pricePlanStoreServiceImplUnderTest.queryBelongPlansByStoreGuid(
                "storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryBelongPlansByStoreGuid_PricePlanMapperReturnsNoItems() {
        // Setup
        when(mockPricePlanMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<PricePlanRespDTO> result = pricePlanStoreServiceImplUnderTest.queryBelongPlansByStoreGuid(
                "storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testUpdatePlanStore() {
        // Setup
        // Run the test
        pricePlanStoreServiceImplUnderTest.updatePlanStore("planGuid", "parentPlanGuid");

        // Verify the results
    }
}
