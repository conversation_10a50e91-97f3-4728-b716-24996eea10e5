package com.holderzone.saas.store.item.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.item.entity.bo.ItemInfoBO;
import com.holderzone.saas.store.item.entity.bo.SubItemSkuBO;
import com.holderzone.saas.store.item.entity.bo.SubgroupBO;
import com.holderzone.saas.store.item.entity.domain.RSkuSubgroupDO;
import com.holderzone.saas.store.item.mapper.SubgroupMapper;
import com.holderzone.saas.store.item.service.IRSkuSubgroupService;
import com.holderzone.saas.store.item.util.DynamicHelper;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class SubgroupServiceImplTest {

    @Mock
    private IRSkuSubgroupService mockSkuSubgroupService;
    @Mock
    private DynamicHelper mockDynamicHelper;
    @Mock
    private RedisTemplate mockRedisTemplate;
    @Mock
    private SubgroupMapper mockSubgroupMapper;

    private SubgroupServiceImpl subgroupServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        subgroupServiceImplUnderTest = new SubgroupServiceImpl(mockSkuSubgroupService, mockDynamicHelper,
                mockRedisTemplate);
        ReflectionTestUtils.setField(subgroupServiceImplUnderTest, "subgroupMapper", mockSubgroupMapper);
    }

    @Test
    public void testRemoveSubgroupByItemGuidList() throws Exception {
        // Setup
        // Run the test
        subgroupServiceImplUnderTest.removeSubgroupByItemGuidList(Arrays.asList("value"));

        // Verify the results
        verify(mockSkuSubgroupService).remove(any(LambdaQueryWrapper.class));
    }

    @Test
    public void testSelectSubgroupListByItemGuidList() {
        // Setup
        final SubgroupBO subgroupBO = new SubgroupBO();
        subgroupBO.setItemGuid("itemGuid");
        subgroupBO.setSubgroupGuid("subgroupGuid");
        subgroupBO.setIsFixSubgroup(0);
        subgroupBO.setPickNum(0);
        subgroupBO.setBrandGuid("brandGuid");
        subgroupBO.setStoreGuid("storeGuid");
        subgroupBO.setSubgroupFrom(0);
        final SubItemSkuBO subItemSkuBO = new SubItemSkuBO();
        subItemSkuBO.setSubgroupGuid("subgroupGuid");
        subItemSkuBO.setSkuSubgroupGuid("skuSubgroupGuid");
        subgroupBO.setSubItemSkuList(Arrays.asList(subItemSkuBO));
        final List<SubgroupBO> expectedResult = Arrays.asList(subgroupBO);

        // Configure IRSkuSubgroupService.list(...).
        final RSkuSubgroupDO rSkuSubgroupDO = new RSkuSubgroupDO();
        rSkuSubgroupDO.setGuid("ff9d4e6f-fbc8-481c-bb3e-071d28ae2a70");
        rSkuSubgroupDO.setSubgroupGuid("guid");
        rSkuSubgroupDO.setSort(0);
        rSkuSubgroupDO.setAddPrice(new BigDecimal("0.00"));
        rSkuSubgroupDO.setIsDefault(0);
        rSkuSubgroupDO.setIsRepeat(0);
        final List<RSkuSubgroupDO> rSkuSubgroupDOS = Arrays.asList(rSkuSubgroupDO);
        when(mockSkuSubgroupService.list(any(LambdaQueryWrapper.class))).thenReturn(rSkuSubgroupDOS);

        // Run the test
        final List<SubgroupBO> result = subgroupServiceImplUnderTest.selectSubgroupListByItemGuidList(
                Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testSelectSubgroupListByItemGuidList_IRSkuSubgroupServiceReturnsNoItems() {
        // Setup
        when(mockSkuSubgroupService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<SubgroupBO> result = subgroupServiceImplUnderTest.selectSubgroupListByItemGuidList(
                Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testSaveOrUpdateAndDeleteSubgroup() {
        // Setup
        final ItemInfoBO itemInfoBO = new ItemInfoBO();
        itemInfoBO.setItemGuid("itemGuid");
        itemInfoBO.setBrandGuid("brandGuid");
        itemInfoBO.setStoreGuid("storeGuid");
        itemInfoBO.setItemFrom(0);
        final SubgroupBO subgroupBO = new SubgroupBO();
        subgroupBO.setItemGuid("itemGuid");
        subgroupBO.setSubgroupGuid("subgroupGuid");
        subgroupBO.setIsFixSubgroup(0);
        subgroupBO.setPickNum(0);
        subgroupBO.setBrandGuid("brandGuid");
        subgroupBO.setStoreGuid("storeGuid");
        subgroupBO.setSubgroupFrom(0);
        final SubItemSkuBO subItemSkuBO = new SubItemSkuBO();
        subItemSkuBO.setSubgroupGuid("subgroupGuid");
        subItemSkuBO.setSkuSubgroupGuid("skuSubgroupGuid");
        subgroupBO.setSubItemSkuList(Arrays.asList(subItemSkuBO));
        itemInfoBO.setSubgroupList(Arrays.asList(subgroupBO));
        final List<ItemInfoBO> itemInfoBOList = Arrays.asList(itemInfoBO);

        // Configure IRSkuSubgroupService.list(...).
        final RSkuSubgroupDO rSkuSubgroupDO = new RSkuSubgroupDO();
        rSkuSubgroupDO.setGuid("ff9d4e6f-fbc8-481c-bb3e-071d28ae2a70");
        rSkuSubgroupDO.setSubgroupGuid("guid");
        rSkuSubgroupDO.setSort(0);
        rSkuSubgroupDO.setAddPrice(new BigDecimal("0.00"));
        rSkuSubgroupDO.setIsDefault(0);
        rSkuSubgroupDO.setIsRepeat(0);
        final List<RSkuSubgroupDO> rSkuSubgroupDOS = Arrays.asList(rSkuSubgroupDO);
        when(mockSkuSubgroupService.list(any(LambdaQueryWrapper.class))).thenReturn(rSkuSubgroupDOS);

        // Configure IRSkuSubgroupService.updateBatchById(...).
        final RSkuSubgroupDO rSkuSubgroupDO1 = new RSkuSubgroupDO();
        rSkuSubgroupDO1.setGuid("ff9d4e6f-fbc8-481c-bb3e-071d28ae2a70");
        rSkuSubgroupDO1.setSubgroupGuid("guid");
        rSkuSubgroupDO1.setSort(0);
        rSkuSubgroupDO1.setAddPrice(new BigDecimal("0.00"));
        rSkuSubgroupDO1.setIsDefault(0);
        rSkuSubgroupDO1.setIsRepeat(0);
        final List<RSkuSubgroupDO> entityList = Arrays.asList(rSkuSubgroupDO1);
        when(mockSkuSubgroupService.updateBatchById(entityList, 0)).thenReturn(true);

        // Run the test
        final boolean result = subgroupServiceImplUnderTest.saveOrUpdateAndDeleteSubgroup(itemInfoBOList);

        // Verify the results
        assertThat(result).isTrue();
        verify(mockSkuSubgroupService).removeByIds(Arrays.asList("value"));

        // Confirm IRSkuSubgroupService.saveBatch(...).
        final RSkuSubgroupDO rSkuSubgroupDO2 = new RSkuSubgroupDO();
        rSkuSubgroupDO2.setGuid("ff9d4e6f-fbc8-481c-bb3e-071d28ae2a70");
        rSkuSubgroupDO2.setSubgroupGuid("guid");
        rSkuSubgroupDO2.setSort(0);
        rSkuSubgroupDO2.setAddPrice(new BigDecimal("0.00"));
        rSkuSubgroupDO2.setIsDefault(0);
        rSkuSubgroupDO2.setIsRepeat(0);
        final List<RSkuSubgroupDO> entityList1 = Arrays.asList(rSkuSubgroupDO2);
        verify(mockSkuSubgroupService).saveBatch(entityList1);
    }

    @Test
    public void testSaveOrUpdateAndDeleteSubgroup_IRSkuSubgroupServiceListReturnsNoItems() {
        // Setup
        final ItemInfoBO itemInfoBO = new ItemInfoBO();
        itemInfoBO.setItemGuid("itemGuid");
        itemInfoBO.setBrandGuid("brandGuid");
        itemInfoBO.setStoreGuid("storeGuid");
        itemInfoBO.setItemFrom(0);
        final SubgroupBO subgroupBO = new SubgroupBO();
        subgroupBO.setItemGuid("itemGuid");
        subgroupBO.setSubgroupGuid("subgroupGuid");
        subgroupBO.setIsFixSubgroup(0);
        subgroupBO.setPickNum(0);
        subgroupBO.setBrandGuid("brandGuid");
        subgroupBO.setStoreGuid("storeGuid");
        subgroupBO.setSubgroupFrom(0);
        final SubItemSkuBO subItemSkuBO = new SubItemSkuBO();
        subItemSkuBO.setSubgroupGuid("subgroupGuid");
        subItemSkuBO.setSkuSubgroupGuid("skuSubgroupGuid");
        subgroupBO.setSubItemSkuList(Arrays.asList(subItemSkuBO));
        itemInfoBO.setSubgroupList(Arrays.asList(subgroupBO));
        final List<ItemInfoBO> itemInfoBOList = Arrays.asList(itemInfoBO);
        when(mockSkuSubgroupService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure IRSkuSubgroupService.updateBatchById(...).
        final RSkuSubgroupDO rSkuSubgroupDO = new RSkuSubgroupDO();
        rSkuSubgroupDO.setGuid("ff9d4e6f-fbc8-481c-bb3e-071d28ae2a70");
        rSkuSubgroupDO.setSubgroupGuid("guid");
        rSkuSubgroupDO.setSort(0);
        rSkuSubgroupDO.setAddPrice(new BigDecimal("0.00"));
        rSkuSubgroupDO.setIsDefault(0);
        rSkuSubgroupDO.setIsRepeat(0);
        final List<RSkuSubgroupDO> entityList = Arrays.asList(rSkuSubgroupDO);
        when(mockSkuSubgroupService.updateBatchById(entityList, 0)).thenReturn(true);

        // Run the test
        final boolean result = subgroupServiceImplUnderTest.saveOrUpdateAndDeleteSubgroup(itemInfoBOList);

        // Verify the results
        assertThat(result).isTrue();
        verify(mockSkuSubgroupService).removeByIds(Arrays.asList("value"));

        // Confirm IRSkuSubgroupService.saveBatch(...).
        final RSkuSubgroupDO rSkuSubgroupDO1 = new RSkuSubgroupDO();
        rSkuSubgroupDO1.setGuid("ff9d4e6f-fbc8-481c-bb3e-071d28ae2a70");
        rSkuSubgroupDO1.setSubgroupGuid("guid");
        rSkuSubgroupDO1.setSort(0);
        rSkuSubgroupDO1.setAddPrice(new BigDecimal("0.00"));
        rSkuSubgroupDO1.setIsDefault(0);
        rSkuSubgroupDO1.setIsRepeat(0);
        final List<RSkuSubgroupDO> entityList1 = Arrays.asList(rSkuSubgroupDO1);
        verify(mockSkuSubgroupService).saveBatch(entityList1);
    }

    @Test
    public void testSaveOrUpdateAndDeleteSubgroup_IRSkuSubgroupServiceUpdateBatchByIdReturnsFalse() {
        // Setup
        final ItemInfoBO itemInfoBO = new ItemInfoBO();
        itemInfoBO.setItemGuid("itemGuid");
        itemInfoBO.setBrandGuid("brandGuid");
        itemInfoBO.setStoreGuid("storeGuid");
        itemInfoBO.setItemFrom(0);
        final SubgroupBO subgroupBO = new SubgroupBO();
        subgroupBO.setItemGuid("itemGuid");
        subgroupBO.setSubgroupGuid("subgroupGuid");
        subgroupBO.setIsFixSubgroup(0);
        subgroupBO.setPickNum(0);
        subgroupBO.setBrandGuid("brandGuid");
        subgroupBO.setStoreGuid("storeGuid");
        subgroupBO.setSubgroupFrom(0);
        final SubItemSkuBO subItemSkuBO = new SubItemSkuBO();
        subItemSkuBO.setSubgroupGuid("subgroupGuid");
        subItemSkuBO.setSkuSubgroupGuid("skuSubgroupGuid");
        subgroupBO.setSubItemSkuList(Arrays.asList(subItemSkuBO));
        itemInfoBO.setSubgroupList(Arrays.asList(subgroupBO));
        final List<ItemInfoBO> itemInfoBOList = Arrays.asList(itemInfoBO);

        // Configure IRSkuSubgroupService.list(...).
        final RSkuSubgroupDO rSkuSubgroupDO = new RSkuSubgroupDO();
        rSkuSubgroupDO.setGuid("ff9d4e6f-fbc8-481c-bb3e-071d28ae2a70");
        rSkuSubgroupDO.setSubgroupGuid("guid");
        rSkuSubgroupDO.setSort(0);
        rSkuSubgroupDO.setAddPrice(new BigDecimal("0.00"));
        rSkuSubgroupDO.setIsDefault(0);
        rSkuSubgroupDO.setIsRepeat(0);
        final List<RSkuSubgroupDO> rSkuSubgroupDOS = Arrays.asList(rSkuSubgroupDO);
        when(mockSkuSubgroupService.list(any(LambdaQueryWrapper.class))).thenReturn(rSkuSubgroupDOS);

        // Configure IRSkuSubgroupService.updateBatchById(...).
        final RSkuSubgroupDO rSkuSubgroupDO1 = new RSkuSubgroupDO();
        rSkuSubgroupDO1.setGuid("ff9d4e6f-fbc8-481c-bb3e-071d28ae2a70");
        rSkuSubgroupDO1.setSubgroupGuid("guid");
        rSkuSubgroupDO1.setSort(0);
        rSkuSubgroupDO1.setAddPrice(new BigDecimal("0.00"));
        rSkuSubgroupDO1.setIsDefault(0);
        rSkuSubgroupDO1.setIsRepeat(0);
        final List<RSkuSubgroupDO> entityList = Arrays.asList(rSkuSubgroupDO1);
        when(mockSkuSubgroupService.updateBatchById(entityList, 0)).thenReturn(false);

        // Run the test
        assertThatThrownBy(
                () -> subgroupServiceImplUnderTest.saveOrUpdateAndDeleteSubgroup(itemInfoBOList))
                .isInstanceOf(BusinessException.class);
        verify(mockSkuSubgroupService).removeByIds(Arrays.asList("value"));

        // Confirm IRSkuSubgroupService.saveBatch(...).
        final RSkuSubgroupDO rSkuSubgroupDO2 = new RSkuSubgroupDO();
        rSkuSubgroupDO2.setGuid("ff9d4e6f-fbc8-481c-bb3e-071d28ae2a70");
        rSkuSubgroupDO2.setSubgroupGuid("guid");
        rSkuSubgroupDO2.setSort(0);
        rSkuSubgroupDO2.setAddPrice(new BigDecimal("0.00"));
        rSkuSubgroupDO2.setIsDefault(0);
        rSkuSubgroupDO2.setIsRepeat(0);
        final List<RSkuSubgroupDO> entityList1 = Arrays.asList(rSkuSubgroupDO2);
        verify(mockSkuSubgroupService).saveBatch(entityList1);
    }

    @Test
    public void testSelectSkuSubgroupByItemGuidList() {
        // Setup
        final RSkuSubgroupDO rSkuSubgroupDO = new RSkuSubgroupDO();
        rSkuSubgroupDO.setGuid("ff9d4e6f-fbc8-481c-bb3e-071d28ae2a70");
        rSkuSubgroupDO.setSubgroupGuid("guid");
        rSkuSubgroupDO.setSort(0);
        rSkuSubgroupDO.setAddPrice(new BigDecimal("0.00"));
        rSkuSubgroupDO.setIsDefault(0);
        rSkuSubgroupDO.setIsRepeat(0);
        final List<RSkuSubgroupDO> expectedResult = Arrays.asList(rSkuSubgroupDO);

        // Configure IRSkuSubgroupService.list(...).
        final RSkuSubgroupDO rSkuSubgroupDO1 = new RSkuSubgroupDO();
        rSkuSubgroupDO1.setGuid("ff9d4e6f-fbc8-481c-bb3e-071d28ae2a70");
        rSkuSubgroupDO1.setSubgroupGuid("guid");
        rSkuSubgroupDO1.setSort(0);
        rSkuSubgroupDO1.setAddPrice(new BigDecimal("0.00"));
        rSkuSubgroupDO1.setIsDefault(0);
        rSkuSubgroupDO1.setIsRepeat(0);
        final List<RSkuSubgroupDO> rSkuSubgroupDOS = Arrays.asList(rSkuSubgroupDO1);
        when(mockSkuSubgroupService.list(any(LambdaQueryWrapper.class))).thenReturn(rSkuSubgroupDOS);

        // Run the test
        final List<RSkuSubgroupDO> result = subgroupServiceImplUnderTest.selectSkuSubgroupByItemGuidList(
                Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testSelectSkuSubgroupByItemGuidList_IRSkuSubgroupServiceReturnsNoItems() {
        // Setup
        when(mockSkuSubgroupService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<RSkuSubgroupDO> result = subgroupServiceImplUnderTest.selectSkuSubgroupByItemGuidList(
                Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testGetGuidListByItemGuidAndStoreGuid() {
        // Setup
        when(mockSubgroupMapper.getGuidListByItemGuidAndStoreGuid("itemGuid", "storeGuid"))
                .thenReturn(Arrays.asList("value"));

        // Run the test
        final List<String> result = subgroupServiceImplUnderTest.getGuidListByItemGuidAndStoreGuid("itemGuid",
                "storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(Arrays.asList("value"));
    }

    @Test
    public void testGetGuidListByItemGuidAndStoreGuid_SubgroupMapperReturnsNoItems() {
        // Setup
        when(mockSubgroupMapper.getGuidListByItemGuidAndStoreGuid("itemGuid", "storeGuid"))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<String> result = subgroupServiceImplUnderTest.getGuidListByItemGuidAndStoreGuid("itemGuid",
                "storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testGetGuidListByStoreGuidAndBrandGuid() {
        // Setup
        when(mockSubgroupMapper.getGuidListByStoreGuidAndBrandGuid("storeGuid", "brandGuid"))
                .thenReturn(Arrays.asList("value"));

        // Run the test
        final List<String> result = subgroupServiceImplUnderTest.getGuidListByStoreGuidAndBrandGuid("storeGuid",
                "brandGuid");

        // Verify the results
        assertThat(result).isEqualTo(Arrays.asList("value"));
    }

    @Test
    public void testGetGuidListByStoreGuidAndBrandGuid_SubgroupMapperReturnsNoItems() {
        // Setup
        when(mockSubgroupMapper.getGuidListByStoreGuidAndBrandGuid("storeGuid", "brandGuid"))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<String> result = subgroupServiceImplUnderTest.getGuidListByStoreGuidAndBrandGuid("storeGuid",
                "brandGuid");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testDeleteByItemGuidAndStoreGuid() {
        // Setup
        // Run the test
        subgroupServiceImplUnderTest.deleteByItemGuidAndStoreGuid("itemGuid", "storeGuid");

        // Verify the results
        verify(mockSubgroupMapper).deleteByItemGuidAndStoreGuid("itemGuid", "storeGuid");
    }
}
