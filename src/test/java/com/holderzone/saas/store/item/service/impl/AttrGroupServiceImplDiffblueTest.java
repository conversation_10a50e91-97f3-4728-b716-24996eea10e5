package com.holderzone.saas.store.item.service.impl;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.anyInt;
import static org.mockito.Mockito.atLeast;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.google.common.collect.Lists;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.req.AttrGroupReqDTO;
import com.holderzone.saas.store.dto.item.req.AttrGroupUpdateReqDTO;
import com.holderzone.saas.store.dto.item.resp.AttrGroupAttrRespDTO;
import com.holderzone.saas.store.dto.item.resp.AttrGroupSynRespDTO;
import com.holderzone.saas.store.dto.item.resp.AttrRespDTO;
import com.holderzone.saas.store.item.entity.domain.AttrDO;
import com.holderzone.saas.store.item.entity.domain.AttrGroupDO;
import com.holderzone.saas.store.item.entity.domain.ItemDO;
import com.holderzone.saas.store.item.entity.domain.RAttrItemAttrGroupDO;
import com.holderzone.saas.store.item.entity.domain.RItemAttrGroupDO;
import com.holderzone.saas.store.item.entity.domain.RTypeAttrDO;
import com.holderzone.saas.store.item.helper.EventPushHelper;
import com.holderzone.saas.store.item.mapper.AttrGroupMapper;
import com.holderzone.saas.store.item.mapper.AttrMapper;
import com.holderzone.saas.store.item.service.IAttrService;
import com.holderzone.saas.store.item.service.IItemService;
import com.holderzone.saas.store.item.service.IRAttrItemAttrGroupService;
import com.holderzone.saas.store.item.service.IRItemAttrGroupService;
import com.holderzone.saas.store.item.service.IRTypeAttrService;
import com.holderzone.saas.store.item.service.ITypeService;
import com.holderzone.saas.store.item.util.DynamicHelper;
import com.holderzone.saas.store.item.util.PushUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.function.BiConsumer;

import org.jetbrains.annotations.NotNull;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@ContextConfiguration(classes = {AttrGroupServiceImpl.class})
@RunWith(SpringJUnit4ClassRunner.class)
public class AttrGroupServiceImplDiffblueTest {
  @Rule
  public ExpectedException thrown = ExpectedException.none();

  @MockBean
  private AttrGroupMapper attrGroupMapper;

  @Autowired
  private AttrGroupServiceImpl attrGroupServiceImpl;

  @MockBean
  private AttrMapper attrMapper;

  @MockBean
  private DynamicHelper dynamicHelper;

  @MockBean
  private EventPushHelper eventPushHelper;

  @MockBean
  private IAttrService iAttrService;

  @MockBean
  private IItemService iItemService;

  @MockBean
  private IRAttrItemAttrGroupService iRAttrItemAttrGroupService;

  @MockBean
  private IRItemAttrGroupService iRItemAttrGroupService;

  @MockBean
  private IRTypeAttrService iRTypeAttrService;

  @MockBean
  private ITypeService iTypeService;

  @MockBean
  private PushUtils pushUtils;

  private final static String PARENT_GUID = "Parent Guid";

  private final static String STORE_GUID = "Store Guid";

  private final static String ERROR_OCCURRED = "An error occurred";

  private final static String ENTERPRISE_NAME = "Enterprise Name";

  private final static String STORE_NAME = "Store Name";

  private final static String USER_NAME = "janedoe";

  private final static String DESCRIPTION = "The characteristics of someone or something";


  /**
   * Method under test:
   * {@link AttrGroupServiceImpl#listAttrGroupByOrganization(ItemSingleDTO)}
   */
  @Test
  public void testListAttrGroupByOrganization() {
    when(attrGroupMapper.selectList(Mockito.<Wrapper<AttrGroupDO>>any())).thenReturn(new ArrayList<>());

    ItemSingleDTO itemSingleDTO = getSingleDTO();
    List<AttrGroupAttrRespDTO> actualListAttrGroupByOrganizationResult = attrGroupServiceImpl
            .listAttrGroupByOrganization(itemSingleDTO);
    verify(attrGroupMapper).selectList(Mockito.<Wrapper<AttrGroupDO>>any());
    assertTrue(actualListAttrGroupByOrganizationResult.isEmpty());
  }

  /**
   * Method under test:
   * {@link AttrGroupServiceImpl#listAttrGroupByOrganization(ItemSingleDTO)}
   */
  @Test
  public void testListAttrGroupByOrganization2() {
    AttrGroupDO attrGroupDO = getAttrGroupDO1();

    ArrayList<AttrGroupDO> attrGroupDOList = new ArrayList<>();
    attrGroupDOList.add(attrGroupDO);
    when(attrGroupMapper.selectList(Mockito.<Wrapper<AttrGroupDO>>any())).thenReturn(attrGroupDOList);
    when(attrMapper.selectList(Mockito.<Wrapper<AttrDO>>any())).thenReturn(new ArrayList<>());

    ItemSingleDTO itemSingleDTO = getItemSingleDTO();
    List<AttrGroupAttrRespDTO> actualListAttrGroupByOrganizationResult = attrGroupServiceImpl
            .listAttrGroupByOrganization(itemSingleDTO);
    verify(attrGroupMapper).selectList(Mockito.<Wrapper<AttrGroupDO>>any());
    verify(attrMapper).selectList(Mockito.<Wrapper<AttrDO>>any());
    assertEquals(1, actualListAttrGroupByOrganizationResult.size());
  }

  @NotNull
  private static ItemSingleDTO getItemSingleDTO() {
    ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
    extracted1(itemSingleDTO);
    return itemSingleDTO;
  }

  @NotNull
  private static AttrGroupDO getAttrGroupDO1() {
    AttrGroupDO attrGroupDO = getAttrGroupDO2();
    attrGroupDO.setName("Name");
    return attrGroupDO;
  }

  /**
   * Method under test:
   * {@link AttrGroupServiceImpl#listAttrGroupByOrganization(ItemSingleDTO)}
   */
  @Test
  public void testListAttrGroupByOrganization3() {
    AttrGroupDO attrGroupDO = getAttrGroupDO1();

    AttrGroupDO attrGroupDO2 = getAttrGroupDO2();

    ArrayList<AttrGroupDO> attrGroupDOList = new ArrayList<>();
    attrGroupDOList.add(attrGroupDO2);
    attrGroupDOList.add(attrGroupDO);
    when(attrGroupMapper.selectList(Mockito.<Wrapper<AttrGroupDO>>any())).thenReturn(attrGroupDOList);
    when(attrMapper.selectList(Mockito.<Wrapper<AttrDO>>any())).thenReturn(new ArrayList<>());

    ItemSingleDTO itemSingleDTO = getItemSingleDTO();
    List<AttrGroupAttrRespDTO> actualListAttrGroupByOrganizationResult = attrGroupServiceImpl
            .listAttrGroupByOrganization(itemSingleDTO);
    verify(attrGroupMapper).selectList(Mockito.<Wrapper<AttrGroupDO>>any());
    verify(attrMapper).selectList(Mockito.<Wrapper<AttrDO>>any());
    assertEquals(2, actualListAttrGroupByOrganizationResult.size());
  }

  @NotNull
  private static AttrGroupDO getAttrGroupDO2() {
    AttrGroupDO attrGroupDO2 = new AttrGroupDO();
    attrGroupDO2.setAttrGroupFrom(4);
    attrGroupDO2.setBrandGuid("Brand Guid");
    attrGroupDO2.setDescription("Description");
    attrGroupDO2.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
    attrGroupDO2.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
    attrGroupDO2.setGuid("Guid");
    attrGroupDO2.setIconUrl("Icon Url");
    attrGroupDO2.setId(2L);
    attrGroupDO2.setIsDelete(4);
    attrGroupDO2.setIsEnable(4);
    attrGroupDO2.setIsMultiChoice(4);
    attrGroupDO2.setIsRequired(4);
    attrGroupDO2.setName("com.holderzone.saas.store.item.entity.domain.AttrGroupDO");
    attrGroupDO2.setNameChange(4);
    attrGroupDO2.setParentGuid(PARENT_GUID);
    attrGroupDO2.setSort(4);
    attrGroupDO2.setStoreGuid(STORE_GUID);
    attrGroupDO2.setWithDefault(4);
    return attrGroupDO2;
  }

  /**
   * Method under test:
   * {@link AttrGroupServiceImpl#listAttrGroupByOrganization(ItemSingleDTO)}
   */
  @Test
  public void testListAttrGroupByOrganization4() {
    AttrGroupDO attrGroupDO = getAttrGroupDO1();

    ArrayList<AttrGroupDO> attrGroupDOList = new ArrayList<>();
    attrGroupDOList.add(attrGroupDO);
    when(attrGroupMapper.selectList(Mockito.<Wrapper<AttrGroupDO>>any())).thenReturn(attrGroupDOList);

    AttrDO attrDO = getAttrDO3();

    extracted(attrDO);

    ItemSingleDTO itemSingleDTO = getItemSingleDTO();
    List<AttrGroupAttrRespDTO> actualListAttrGroupByOrganizationResult = attrGroupServiceImpl
            .listAttrGroupByOrganization(itemSingleDTO);
    extracted2();
    assertEquals(1, actualListAttrGroupByOrganizationResult.size());
  }

  private void extracted2() {
    verify(attrGroupMapper).selectList(Mockito.<Wrapper<AttrGroupDO>>any());
    verify(attrMapper).selectList(Mockito.<Wrapper<AttrDO>>any());
    verify(iRTypeAttrService).list(Mockito.<Wrapper<RTypeAttrDO>>any());
  }

  private void extracted(AttrDO attrDO) {
    ArrayList<AttrDO> attrDOList = new ArrayList<>();
    attrDOList.add(attrDO);
    when(attrMapper.selectList(Mockito.<Wrapper<AttrDO>>any())).thenReturn(attrDOList);
    when(iRTypeAttrService.list(Mockito.<Wrapper<RTypeAttrDO>>any())).thenReturn(new ArrayList<>());
  }

  @NotNull
  private static AttrDO getAttrDO3() {
    AttrDO attrDO = getAttrDO2();
    attrDO.setName("Name");
    attrDO.setParentGuid("1234");
    attrDO.setPrice(new BigDecimal("2.3"));
    attrDO.setStoreGuid("1234");
    return attrDO;
  }

  /**
   * Method under test:
   * {@link AttrGroupServiceImpl#listAttrGroupByOrganization(ItemSingleDTO)}
   */
  @Test
  public void testListAttrGroupByOrganization5() {
    AttrGroupDO attrGroupDO = getAttrGroupDO1();
    AttrGroupDO attrGroupDO2 = getAttrGroupDO2();

    ArrayList<AttrGroupDO> attrGroupDOList = new ArrayList<>();
    attrGroupDOList.add(attrGroupDO2);
    attrGroupDOList.add(attrGroupDO);
    when(attrGroupMapper.selectList(Mockito.<Wrapper<AttrGroupDO>>any())).thenReturn(attrGroupDOList);

    AttrDO attrDO = getAttrDO3();

    extracted(attrDO);

    ItemSingleDTO itemSingleDTO = getItemSingleDTO();
    List<AttrGroupAttrRespDTO> actualListAttrGroupByOrganizationResult = attrGroupServiceImpl
            .listAttrGroupByOrganization(itemSingleDTO);
    extracted2();
    assertEquals(2, actualListAttrGroupByOrganizationResult.size());
  }

  /**
   * Method under test:
   * {@link AttrGroupServiceImpl#listAttrForSaveItem(ItemSingleDTO)}
   */
  @Test
  public void testListAttrForSaveItem() {
    when(attrGroupMapper.selectList(Mockito.<Wrapper<AttrGroupDO>>any())).thenReturn(new ArrayList<>());

    ItemSingleDTO itemSingleDTO = getItemSingleDTO();
    List<AttrGroupAttrRespDTO> actualListAttrForSaveItemResult = attrGroupServiceImpl
            .listAttrForSaveItem(itemSingleDTO);
    verify(attrGroupMapper).selectList(Mockito.<Wrapper<AttrGroupDO>>any());
    assertTrue(actualListAttrForSaveItemResult.isEmpty());
  }

  /**
   * Method under test:
   * {@link AttrGroupServiceImpl#listAttrForSaveItem(ItemSingleDTO)}
   */
  @Test
  public void testListAttrForSaveItem2() {
    AttrGroupDO attrGroupDO = getAttrGroupDO1();

    ArrayList<AttrGroupDO> attrGroupDOList = new ArrayList<>();
    attrGroupDOList.add(attrGroupDO);
    when(attrGroupMapper.selectList(Mockito.<Wrapper<AttrGroupDO>>any())).thenReturn(attrGroupDOList);
    when(iAttrService.listAttrByGroup(Mockito.<List<String>>any())).thenReturn(new ArrayList<>());

    ItemSingleDTO itemSingleDTO = getItemSingleDTO();
    List<AttrGroupAttrRespDTO> actualListAttrForSaveItemResult = attrGroupServiceImpl
            .listAttrForSaveItem(itemSingleDTO);
    verify(attrGroupMapper).selectList(Mockito.<Wrapper<AttrGroupDO>>any());
    verify(iAttrService).listAttrByGroup(Mockito.<List<String>>any());
    assertTrue(actualListAttrForSaveItemResult.isEmpty());
  }

  /**
   * Method under test:
   * {@link AttrGroupServiceImpl#listAttrForSaveItem(ItemSingleDTO)}
   */
  @Test
  public void testListAttrForSaveItem3() {
    AttrGroupDO attrGroupDO = getAttrGroupDO1();

    ArrayList<AttrGroupDO> attrGroupDOList = new ArrayList<>();
    attrGroupDOList.add(attrGroupDO);
    when(attrGroupMapper.selectList(Mockito.<Wrapper<AttrGroupDO>>any())).thenReturn(attrGroupDOList);

    AttrRespDTO attrRespDTO = getAttrRespDTO();

    ArrayList<AttrRespDTO> attrRespDTOList = new ArrayList<>();
    attrRespDTOList.add(attrRespDTO);
    when(iAttrService.listAttrByGroup(Mockito.<List<String>>any())).thenReturn(attrRespDTOList);

    ItemSingleDTO itemSingleDTO = getItemSingleDTO();
    List<AttrGroupAttrRespDTO> actualListAttrForSaveItemResult = attrGroupServiceImpl
            .listAttrForSaveItem(itemSingleDTO);
    verify(attrGroupMapper).selectList(Mockito.<Wrapper<AttrGroupDO>>any());
    verify(iAttrService).listAttrByGroup(Mockito.<List<String>>any());
    assertEquals(1, actualListAttrForSaveItemResult.size());
  }

  @NotNull
  private static AttrRespDTO getAttrRespDTO() {
    AttrRespDTO attrRespDTO = new AttrRespDTO();
    attrRespDTO.setAttrFrom(1);
    attrRespDTO.setAttrGroupGuid("1234");
    attrRespDTO.setAttrGuid("1234");
    attrRespDTO.setIsClick(true);
    attrRespDTO.setIsDefault(1);
    attrRespDTO.setName("Name");
    attrRespDTO.setPrice(new BigDecimal("2.3"));
    attrRespDTO.setTypeList(new ArrayList<>());
    return attrRespDTO;
  }

  /**
   * Method under test:
   * {@link AttrGroupServiceImpl#listAttrForSaveItem(ItemSingleDTO)}
   */
  @Test
  public void testListAttrForSaveItem4() {
    AttrGroupDO attrGroupDO = getAttrGroupDO1();

    ArrayList<AttrGroupDO> attrGroupDOList = new ArrayList<>();
    attrGroupDOList.add(attrGroupDO);
    when(attrGroupMapper.selectList(Mockito.<Wrapper<AttrGroupDO>>any())).thenReturn(attrGroupDOList);

    AttrRespDTO attrRespDTO = getAttrRespDTO();

    AttrRespDTO attrRespDTO2 = new AttrRespDTO();
    attrRespDTO2.setAttrFrom(4);
    attrRespDTO2.setAttrGroupGuid("Attr Group Guid");
    attrRespDTO2.setAttrGuid("Attr Guid");
    attrRespDTO2.setIsClick(false);
    attrRespDTO2.setIsDefault(4);
    attrRespDTO2.setName("Name");
    attrRespDTO2.setPrice(new BigDecimal("2.3"));
    attrRespDTO2.setTypeList(new ArrayList<>());

    ArrayList<AttrRespDTO> attrRespDTOList = new ArrayList<>();
    attrRespDTOList.add(attrRespDTO2);
    attrRespDTOList.add(attrRespDTO);
    when(iAttrService.listAttrByGroup(Mockito.<List<String>>any())).thenReturn(attrRespDTOList);

    ItemSingleDTO itemSingleDTO = getItemSingleDTO();
    List<AttrGroupAttrRespDTO> actualListAttrForSaveItemResult = attrGroupServiceImpl
            .listAttrForSaveItem(itemSingleDTO);
    verify(attrGroupMapper).selectList(Mockito.<Wrapper<AttrGroupDO>>any());
    verify(iAttrService).listAttrByGroup(Mockito.<List<String>>any());
    assertEquals(1, actualListAttrForSaveItemResult.size());
  }

  /**
   * Method under test:
   * {@link AttrGroupServiceImpl#listAttrForSaveItem(ItemSingleDTO)}
   */
  @Test
  public void testListAttrForSaveItem5() {
    ItemSingleDTO itemSingleDTO = mock(ItemSingleDTO.class);
    when(itemSingleDTO.getFrom()).thenThrow(new ParameterException(ERROR_OCCURRED));
    when(itemSingleDTO.getData()).thenReturn("Data");
    extracted(itemSingleDTO);
    extracted1(itemSingleDTO);
    attrGroupServiceImpl.listAttrForSaveItem(itemSingleDTO);
    extracted2(itemSingleDTO);
    verify(itemSingleDTO).getData();
    extracted4(itemSingleDTO);
    verify(itemSingleDTO).getFrom();
    verify(itemSingleDTO).setFrom(Mockito.<Integer>any());
  }

  private static void extracted4(ItemSingleDTO itemSingleDTO) {
    verify(itemSingleDTO).setData(Mockito.<String>any());
    verify(itemSingleDTO).setFilterGuidList(Mockito.<List<String>>any());
    verify(itemSingleDTO).setItemList(Mockito.<List<String>>any());
    verify(itemSingleDTO).setItemQueryType(Mockito.<Integer>any());
    verify(itemSingleDTO).setKeywords(Mockito.<String>any());
    verify(itemSingleDTO).setModel(Mockito.<Integer>any());
    verify(itemSingleDTO).setPlanGuid(Mockito.<String>any());
    verify(itemSingleDTO).setRuleGuid(Mockito.<String>any());
    verify(itemSingleDTO).setRuleType(Mockito.<Integer>any());
    verify(itemSingleDTO).setSkuGuids(Mockito.<List<String>>any());
    verify(itemSingleDTO).setStoreGuid(Mockito.<String>any());
    verify(itemSingleDTO).setStoreGuids(Mockito.<List<String>>any());
    verify(itemSingleDTO).setWxtoken(Mockito.<String>any());
  }

  private static void extracted1(ItemSingleDTO itemSingleDTO) {
    itemSingleDTO.setAccount("3");
    itemSingleDTO.setButton("Button");
    itemSingleDTO.setData("Data");
    itemSingleDTO.setDeviceId("42");
    itemSingleDTO.setDeviceType(1);
    itemSingleDTO.setEnterpriseGuid("1234");
    itemSingleDTO.setEnterpriseName(ENTERPRISE_NAME);
    itemSingleDTO.setFilterGuidList(new ArrayList<>());


    itemSingleDTO.setFrom(1);
    itemSingleDTO.setItemList(new ArrayList<>());
    itemSingleDTO.setItemQueryType(42);
    itemSingleDTO.setKeywords("Keywords");
    itemSingleDTO.setModel(1);
    itemSingleDTO.setModule("Module");
    itemSingleDTO.setOperSubjectGuid("1234");
    itemSingleDTO.setOperationTarget(new ArrayList<>());
    itemSingleDTO.setPlanGuid("1234");
    itemSingleDTO.setPlatform(Platform.PAYMENT);
    itemSingleDTO.setRequestTimestamp(1L);
    itemSingleDTO.setRequestUri("Request Uri");
    itemSingleDTO.setRuleGuid("1234");
    itemSingleDTO.setRuleType(1);
    itemSingleDTO.setSkuGuids(new ArrayList<>());
    itemSingleDTO.setStoreGuid("1234");
    itemSingleDTO.setStoreGuids(new ArrayList<>());
    itemSingleDTO.setStoreName(STORE_NAME);
    itemSingleDTO.setUserGuid("1234");
    itemSingleDTO.setUserName(USER_NAME);
    itemSingleDTO.setWxtoken("ABC123");
  }

  private static void extracted(ItemSingleDTO itemSingleDTO) {
    doNothing().when(itemSingleDTO).setAccount(Mockito.<String>any());
    doNothing().when(itemSingleDTO).setDeviceId(Mockito.<String>any());
    doNothing().when(itemSingleDTO).setDeviceType(Mockito.<Integer>any());
    doNothing().when(itemSingleDTO).setEnterpriseGuid(Mockito.<String>any());
    doNothing().when(itemSingleDTO).setEnterpriseName(Mockito.<String>any());
    doNothing().when(itemSingleDTO).setOperSubjectGuid(Mockito.<String>any());
    doNothing().when(itemSingleDTO).setRequestTimestamp(Mockito.<Long>any());
    doNothing().when(itemSingleDTO).setStoreName(Mockito.<String>any());
    doNothing().when(itemSingleDTO).setUserGuid(Mockito.<String>any());
    doNothing().when(itemSingleDTO).setUserName(Mockito.<String>any());
    doNothing().when(itemSingleDTO).setButton(Mockito.<String>any());
    doNothing().when(itemSingleDTO).setModule(Mockito.<String>any());
    doNothing().when(itemSingleDTO).setOperationTarget(Mockito.<List<String>>any());
    doNothing().when(itemSingleDTO).setPlatform(Mockito.<Platform>any());
    doNothing().when(itemSingleDTO).setRequestUri(Mockito.<String>any());
    doNothing().when(itemSingleDTO).setData(Mockito.<String>any());
    doNothing().when(itemSingleDTO).setFilterGuidList(Mockito.<List<String>>any());
    doNothing().when(itemSingleDTO).setItemList(Mockito.<List<String>>any());
    doNothing().when(itemSingleDTO).setItemQueryType(Mockito.<Integer>any());
    doNothing().when(itemSingleDTO).setKeywords(Mockito.<String>any());
    doNothing().when(itemSingleDTO).setModel(Mockito.<Integer>any());
    doNothing().when(itemSingleDTO).setPlanGuid(Mockito.<String>any());
    doNothing().when(itemSingleDTO).setRuleGuid(Mockito.<String>any());
    doNothing().when(itemSingleDTO).setRuleType(Mockito.<Integer>any());
    doNothing().when(itemSingleDTO).setSkuGuids(Mockito.<List<String>>any());
    doNothing().when(itemSingleDTO).setStoreGuid(Mockito.<String>any());
    doNothing().when(itemSingleDTO).setStoreGuids(Mockito.<List<String>>any());
    doNothing().when(itemSingleDTO).setWxtoken(Mockito.<String>any());
    doNothing().when(itemSingleDTO).setFrom(Mockito.<Integer>any());
  }

  /**
   * Method under test:
   * {@link AttrGroupServiceImpl#listAttrForSaveItem(ItemSingleDTO)}
   */
  @Test
  public void testListAttrForSaveItem6() {
    AttrGroupDO attrGroupDO = getAttrGroupDO();

    ArrayList<AttrGroupDO> attrGroupDOList = new ArrayList<>();
    attrGroupDOList.add(attrGroupDO);
    when(attrGroupMapper.selectList(Mockito.<Wrapper<AttrGroupDO>>any())).thenReturn(attrGroupDOList);
    when(iAttrService.listAttrByGroup(Mockito.<List<String>>any())).thenReturn(new ArrayList<>());
    ItemSingleDTO itemSingleDTO = mock(ItemSingleDTO.class);
    when(itemSingleDTO.getFrom()).thenReturn(-1);
    when(itemSingleDTO.getData()).thenReturn("Data");
    extracted(itemSingleDTO);
    extracted1(itemSingleDTO);
    List<AttrGroupAttrRespDTO> actualListAttrForSaveItemResult = getAttrGroupAttrRespDTOS(itemSingleDTO);
    assertTrue(actualListAttrForSaveItemResult.isEmpty());
  }

  private List<AttrGroupAttrRespDTO> getAttrGroupAttrRespDTOS(ItemSingleDTO itemSingleDTO) {
    List<AttrGroupAttrRespDTO> actualListAttrForSaveItemResult = attrGroupServiceImpl
            .listAttrForSaveItem(itemSingleDTO);
    verify(attrGroupMapper).selectList(Mockito.<Wrapper<AttrGroupDO>>any());
    extracted2(itemSingleDTO);
    extracted3(itemSingleDTO);
    verify(iAttrService).listAttrByGroup(Mockito.<List<String>>any());
    return actualListAttrForSaveItemResult;
  }

  private static void extracted3(ItemSingleDTO itemSingleDTO) {
    verify(itemSingleDTO, atLeast(1)).getData();
    extracted4(itemSingleDTO);
    verify(itemSingleDTO, atLeast(1)).getFrom();
    verify(itemSingleDTO).setFrom(Mockito.<Integer>any());
  }

  private static void extracted2(ItemSingleDTO itemSingleDTO) {
    verify(itemSingleDTO).setAccount(Mockito.<String>any());
    verify(itemSingleDTO).setDeviceId(Mockito.<String>any());
    verify(itemSingleDTO).setDeviceType(Mockito.<Integer>any());
    verify(itemSingleDTO).setEnterpriseGuid(Mockito.<String>any());
    verify(itemSingleDTO).setEnterpriseName(Mockito.<String>any());
    verify(itemSingleDTO).setOperSubjectGuid(Mockito.<String>any());
    verify(itemSingleDTO).setRequestTimestamp(Mockito.<Long>any());
    verify(itemSingleDTO).setStoreName(Mockito.<String>any());
    verify(itemSingleDTO).setUserGuid(Mockito.<String>any());
    verify(itemSingleDTO).setUserName(Mockito.<String>any());
    verify(itemSingleDTO).setButton(Mockito.<String>any());
    verify(itemSingleDTO).setModule(Mockito.<String>any());
    verify(itemSingleDTO).setOperationTarget(Mockito.<List<String>>any());
    verify(itemSingleDTO).setPlatform(Mockito.<Platform>any());
    verify(itemSingleDTO).setRequestUri(Mockito.<String>any());
  }

  @NotNull
  private static AttrGroupDO getAttrGroupDO() {
    AttrGroupDO attrGroupDO = getAttrGroupDO1();
    return attrGroupDO;
  }

  /**
   * Method under test:
   * {@link AttrGroupServiceImpl#listAttrForSaveItem(ItemSingleDTO)}
   */
  @Test
  public void testListAttrForSaveItem7() {
    AttrGroupDO attrGroupDO = getAttrGroupDO();

    ArrayList<AttrGroupDO> attrGroupDOList = new ArrayList<>();
    attrGroupDOList.add(attrGroupDO);
    when(attrGroupMapper.selectList(Mockito.<Wrapper<AttrGroupDO>>any())).thenReturn(attrGroupDOList);
    when(iAttrService.listAttrByGroup(Mockito.<List<String>>any())).thenReturn(new ArrayList<>());
    ItemSingleDTO itemSingleDTO = mock(ItemSingleDTO.class);
    when(itemSingleDTO.getFrom()).thenReturn(0);
    when(itemSingleDTO.getData()).thenReturn("Data");
    extracted(itemSingleDTO);
    extracted1(itemSingleDTO);
    List<AttrGroupAttrRespDTO> actualListAttrForSaveItemResult = getAttrGroupAttrRespDTOS(itemSingleDTO);
    assertTrue(actualListAttrForSaveItemResult.isEmpty());
  }

  /**
   * Method under test: {@link AttrGroupServiceImpl#addAttrGroup(AttrGroupReqDTO)}
   */
  @Test
  public void testAddAttrGroup() {
    when(attrGroupMapper.selectCount(Mockito.<Wrapper<AttrGroupDO>>any())).thenReturn(3);

    AttrGroupReqDTO attrGroupReqDTO = getAttrGroupReqDTO();
    thrown.expect(ParameterException.class);
    attrGroupServiceImpl.addAttrGroup(attrGroupReqDTO);
    verify(attrGroupMapper).selectCount(Mockito.<Wrapper<AttrGroupDO>>any());
  }

  @NotNull
  private static AttrGroupReqDTO getAttrGroupReqDTO() {
    AttrGroupReqDTO attrGroupReqDTO = new AttrGroupReqDTO();
    extracted1(attrGroupReqDTO);
    return attrGroupReqDTO;
  }

  /**
   * Method under test: {@link AttrGroupServiceImpl#addAttrGroup(AttrGroupReqDTO)}
   */
  @Test
  public void testAddAttrGroup2() {
    when(attrGroupMapper.insert(Mockito.<AttrGroupDO>any())).thenReturn(1);
    when(attrGroupMapper.selectCount(Mockito.<Wrapper<AttrGroupDO>>any())).thenReturn(0);
    when(dynamicHelper.generateGuid(Mockito.<String>any())).thenReturn("1234");

    AttrGroupReqDTO attrGroupReqDTO = getAttrGroupReqDTO();
    boolean actualAddAttrGroupResult = attrGroupServiceImpl.addAttrGroup(attrGroupReqDTO);
    verify(attrGroupMapper).insert(Mockito.<AttrGroupDO>any());
    verify(attrGroupMapper).selectCount(Mockito.<Wrapper<AttrGroupDO>>any());
    verify(dynamicHelper).generateGuid(Mockito.<String>any());
    assertTrue(actualAddAttrGroupResult);
  }

  /**
   * Method under test: {@link AttrGroupServiceImpl#addAttrGroup(AttrGroupReqDTO)}
   */
  @Test
  public void testAddAttrGroup3() {
    when(attrGroupMapper.insert(Mockito.<AttrGroupDO>any())).thenReturn(0);
    when(attrGroupMapper.selectCount(Mockito.<Wrapper<AttrGroupDO>>any())).thenReturn(0);
    when(dynamicHelper.generateGuid(Mockito.<String>any())).thenReturn("1234");

    AttrGroupReqDTO attrGroupReqDTO = getAttrGroupReqDTO();
    boolean actualAddAttrGroupResult = attrGroupServiceImpl.addAttrGroup(attrGroupReqDTO);
    verify(attrGroupMapper).insert(Mockito.<AttrGroupDO>any());
    verify(attrGroupMapper).selectCount(Mockito.<Wrapper<AttrGroupDO>>any());
    verify(dynamicHelper).generateGuid(Mockito.<String>any());
    assertFalse(actualAddAttrGroupResult);
  }

  /**
   * Method under test: {@link AttrGroupServiceImpl#addAttrGroup(AttrGroupReqDTO)}
   */
  @Test
  public void testAddAttrGroup4() {
    when(attrGroupMapper.insert(Mockito.<AttrGroupDO>any())).thenReturn(1);
    when(attrGroupMapper.selectCount(Mockito.<Wrapper<AttrGroupDO>>any())).thenReturn(0);
    when(dynamicHelper.generateGuid(Mockito.<String>any())).thenReturn("1234");
    AttrGroupReqDTO attrGroupReqDTO = mock(AttrGroupReqDTO.class);
    when(attrGroupReqDTO.getFrom()).thenReturn(-1);
    extracted3(attrGroupReqDTO);
  }

  private void extracted3(AttrGroupReqDTO attrGroupReqDTO) {
    when(attrGroupReqDTO.getIsEnable()).thenReturn(1);
    when(attrGroupReqDTO.getIsMultiChoice()).thenReturn(1);
    when(attrGroupReqDTO.getIsRequired()).thenReturn(1);
    when(attrGroupReqDTO.getSort()).thenReturn(1);
    when(attrGroupReqDTO.getBrandGuid()).thenReturn("1234");
    when(attrGroupReqDTO.getDescription()).thenReturn(DESCRIPTION);
    when(attrGroupReqDTO.getName()).thenReturn("Name");
    when(attrGroupReqDTO.getStoreGuid()).thenReturn("1234");
    extracted(attrGroupReqDTO);
    extracted1(attrGroupReqDTO);
    boolean actualAddAttrGroupResult = attrGroupServiceImpl.addAttrGroup(attrGroupReqDTO);
    extracted2(attrGroupReqDTO);
    assertTrue(actualAddAttrGroupResult);
  }

  private void extracted2(AttrGroupReqDTO attrGroupReqDTO) {
    verify(attrGroupMapper).insert(Mockito.<AttrGroupDO>any());
    verify(attrGroupMapper).selectCount(Mockito.<Wrapper<AttrGroupDO>>any());
    verify(attrGroupReqDTO).setAccount(Mockito.<String>any());
    verify(attrGroupReqDTO).setDeviceId(Mockito.<String>any());
    verify(attrGroupReqDTO).setDeviceType(Mockito.<Integer>any());
    verify(attrGroupReqDTO).setEnterpriseGuid(Mockito.<String>any());
    verify(attrGroupReqDTO).setEnterpriseName(Mockito.<String>any());
    verify(attrGroupReqDTO).setOperSubjectGuid(Mockito.<String>any());
    verify(attrGroupReqDTO).setRequestTimestamp(Mockito.<Long>any());
    verify(attrGroupReqDTO).setStoreName(Mockito.<String>any());
    verify(attrGroupReqDTO).setUserGuid(Mockito.<String>any());
    verify(attrGroupReqDTO).setUserName(Mockito.<String>any());
    verify(attrGroupReqDTO, atLeast(1)).getFrom();
    verify(attrGroupReqDTO).setFrom(Mockito.<Integer>any());
    verify(attrGroupReqDTO, atLeast(1)).getBrandGuid();
    verify(attrGroupReqDTO).getDescription();
    verify(attrGroupReqDTO).getIsEnable();
    verify(attrGroupReqDTO).getIsMultiChoice();
    verify(attrGroupReqDTO).getIsRequired();
    verify(attrGroupReqDTO, atLeast(1)).getName();
    verify(attrGroupReqDTO).getSort();
    verify(attrGroupReqDTO, atLeast(1)).getStoreGuid();
    verify(attrGroupReqDTO).setBrandGuid(Mockito.<String>any());
    verify(attrGroupReqDTO).setDefaultAttrGuidList(Mockito.<List<String>>any());
    verify(attrGroupReqDTO).setDescription(Mockito.<String>any());
    verify(attrGroupReqDTO).setIsEnable(Mockito.<Integer>any());
    verify(attrGroupReqDTO).setIsMultiChoice(Mockito.<Integer>any());
    verify(attrGroupReqDTO).setIsRequired(Mockito.<Integer>any());
    verify(attrGroupReqDTO).setName(Mockito.<String>any());
    verify(attrGroupReqDTO).setSort(Mockito.<Integer>any());
    verify(attrGroupReqDTO).setStoreGuid(Mockito.<String>any());
    verify(dynamicHelper).generateGuid(Mockito.<String>any());
  }

  private static void extracted1(AttrGroupReqDTO attrGroupReqDTO) {
    attrGroupReqDTO.setAccount("3");
    attrGroupReqDTO.setBrandGuid("1234");
    attrGroupReqDTO.setDefaultAttrGuidList(new ArrayList<>());
    attrGroupReqDTO.setDescription(DESCRIPTION);
    attrGroupReqDTO.setDeviceId("42");
    attrGroupReqDTO.setDeviceType(1);
    attrGroupReqDTO.setEnterpriseGuid("1234");
    attrGroupReqDTO.setEnterpriseName(ENTERPRISE_NAME);
    attrGroupReqDTO.setFrom(1);
    attrGroupReqDTO.setIsEnable(1);
    attrGroupReqDTO.setIsMultiChoice(1);
    attrGroupReqDTO.setIsRequired(1);
    attrGroupReqDTO.setName("Name");
    attrGroupReqDTO.setOperSubjectGuid("1234");
    attrGroupReqDTO.setRequestTimestamp(1L);
    attrGroupReqDTO.setSort(1);
    attrGroupReqDTO.setStoreGuid("1234");
    attrGroupReqDTO.setStoreName(STORE_NAME);
    attrGroupReqDTO.setUserGuid("1234");
    attrGroupReqDTO.setUserName(USER_NAME);
  }

  private static void extracted(AttrGroupReqDTO attrGroupReqDTO) {
    doNothing().when(attrGroupReqDTO).setAccount(Mockito.<String>any());
    doNothing().when(attrGroupReqDTO).setDeviceId(Mockito.<String>any());
    doNothing().when(attrGroupReqDTO).setDeviceType(Mockito.<Integer>any());
    doNothing().when(attrGroupReqDTO).setEnterpriseGuid(Mockito.<String>any());
    doNothing().when(attrGroupReqDTO).setEnterpriseName(Mockito.<String>any());
    doNothing().when(attrGroupReqDTO).setOperSubjectGuid(Mockito.<String>any());
    doNothing().when(attrGroupReqDTO).setRequestTimestamp(Mockito.<Long>any());
    doNothing().when(attrGroupReqDTO).setStoreName(Mockito.<String>any());
    doNothing().when(attrGroupReqDTO).setUserGuid(Mockito.<String>any());
    doNothing().when(attrGroupReqDTO).setUserName(Mockito.<String>any());
    doNothing().when(attrGroupReqDTO).setFrom(Mockito.<Integer>any());
    doNothing().when(attrGroupReqDTO).setBrandGuid(Mockito.<String>any());
    doNothing().when(attrGroupReqDTO).setDefaultAttrGuidList(Mockito.<List<String>>any());
    doNothing().when(attrGroupReqDTO).setDescription(Mockito.<String>any());
    doNothing().when(attrGroupReqDTO).setIsEnable(Mockito.<Integer>any());
    doNothing().when(attrGroupReqDTO).setIsMultiChoice(Mockito.<Integer>any());
    doNothing().when(attrGroupReqDTO).setIsRequired(Mockito.<Integer>any());
    doNothing().when(attrGroupReqDTO).setName(Mockito.<String>any());
    doNothing().when(attrGroupReqDTO).setSort(Mockito.<Integer>any());
    doNothing().when(attrGroupReqDTO).setStoreGuid(Mockito.<String>any());
  }

  /**
   * Method under test: {@link AttrGroupServiceImpl#addAttrGroup(AttrGroupReqDTO)}
   */
  @Test
  public void testAddAttrGroup5() {
    when(attrGroupMapper.insert(Mockito.<AttrGroupDO>any())).thenReturn(1);
    when(attrGroupMapper.selectCount(Mockito.<Wrapper<AttrGroupDO>>any())).thenReturn(0);
    when(dynamicHelper.generateGuid(Mockito.<String>any())).thenReturn("1234");
    AttrGroupReqDTO attrGroupReqDTO = mock(AttrGroupReqDTO.class);
    when(attrGroupReqDTO.getFrom()).thenReturn(0);
    extracted3(attrGroupReqDTO);
  }

  /**
   * Method under test: {@link AttrGroupServiceImpl#getLastSort(ItemSingleDTO)}
   */
  @Test
  public void testGetLastSort() {
    when(attrGroupMapper.selectCount(Mockito.<Wrapper<AttrGroupDO>>any())).thenReturn(3);

    ItemSingleDTO itemSingleDTO = getSingleDTO();
    int actualLastSort = attrGroupServiceImpl.getLastSort(itemSingleDTO);
    verify(attrGroupMapper).selectCount(Mockito.<Wrapper<AttrGroupDO>>any());
    assertEquals(4, actualLastSort);
  }

  @NotNull
  private static ItemSingleDTO getSingleDTO() {
    ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
    extracted1(itemSingleDTO);
    return itemSingleDTO;
  }

  /**
   * Method under test: {@link AttrGroupServiceImpl#getLastSort(ItemSingleDTO)}
   */
  @Test
  public void testGetLastSort2() {
    when(attrGroupMapper.selectCount(Mockito.<Wrapper<AttrGroupDO>>any())).thenReturn(3);
    ItemSingleDTO itemSingleDTO = mock(ItemSingleDTO.class);
    when(itemSingleDTO.getFrom()).thenReturn(-1);
    when(itemSingleDTO.getData()).thenReturn("Data");
    extracted(itemSingleDTO);
    extracted1(itemSingleDTO);
    int actualLastSort = attrGroupServiceImpl.getLastSort(itemSingleDTO);
    verify(attrGroupMapper).selectCount(Mockito.<Wrapper<AttrGroupDO>>any());
    extracted2(itemSingleDTO);
    extracted3(itemSingleDTO);
    assertEquals(4, actualLastSort);
  }

  /**
   * Method under test: {@link AttrGroupServiceImpl#getLastSort(ItemSingleDTO)}
   */
  @Test
  public void testGetLastSort3() {
    when(attrGroupMapper.selectCount(Mockito.<Wrapper<AttrGroupDO>>any())).thenReturn(3);
    ItemSingleDTO itemSingleDTO = mock(ItemSingleDTO.class);
    when(itemSingleDTO.getFrom()).thenReturn(0);
    when(itemSingleDTO.getData()).thenReturn("Data");
    extracted(itemSingleDTO);
    extracted1(itemSingleDTO);
    int actualLastSort = attrGroupServiceImpl.getLastSort(itemSingleDTO);
    verify(attrGroupMapper).selectCount(Mockito.<Wrapper<AttrGroupDO>>any());
    extracted2(itemSingleDTO);
    extracted3(itemSingleDTO);
    assertEquals(4, actualLastSort);
  }

  /**
   * Method under test: {@link AttrGroupServiceImpl#removePushAttrGroup(String)}
   */
  @Test
  public void testRemovePushAttrGroup() {
    when(attrGroupMapper.selectList(Mockito.<Wrapper<AttrGroupDO>>any())).thenReturn(new ArrayList<>());
    Integer actualRemovePushAttrGroupResult = attrGroupServiceImpl.removePushAttrGroup("1234");
    verify(attrGroupMapper).selectList(Mockito.<Wrapper<AttrGroupDO>>any());
    assertEquals(1, actualRemovePushAttrGroupResult.intValue());
  }

  /**
   * Method under test: {@link AttrGroupServiceImpl#removePushAttrGroup(String)}
   */
  @Test
  public void testRemovePushAttrGroup2() {
    AttrGroupDO attrGroupDO = getGroupDO();

    ArrayList<AttrGroupDO> attrGroupDOList = new ArrayList<>();
    attrGroupDOList.add(attrGroupDO);
    when(attrGroupMapper.deleteBatchIds(Mockito.<Collection<Serializable>>any())).thenReturn(1);
    when(attrGroupMapper.selectList(Mockito.<Wrapper<AttrGroupDO>>any())).thenReturn(attrGroupDOList);
    when(iAttrService.list(Mockito.<Wrapper<AttrDO>>any())).thenReturn(new ArrayList<>());
    when(iRItemAttrGroupService.list(Mockito.<Wrapper<RItemAttrGroupDO>>any())).thenReturn(new ArrayList<>());
    Integer actualRemovePushAttrGroupResult = attrGroupServiceImpl.removePushAttrGroup("1234");
    verify(attrGroupMapper).deleteBatchIds(Mockito.<Collection<Serializable>>any());
    verify(attrGroupMapper).selectList(Mockito.<Wrapper<AttrGroupDO>>any());
    verify(iAttrService).list(Mockito.<Wrapper<AttrDO>>any());
    verify(iRItemAttrGroupService).list(Mockito.<Wrapper<RItemAttrGroupDO>>any());
    assertEquals(1, actualRemovePushAttrGroupResult.intValue());
  }

  @NotNull
  private static AttrGroupDO getGroupDO() {
    AttrGroupDO attrGroupDO = new AttrGroupDO();
    attrGroupDO.setAttrGroupFrom(2);
    attrGroupDO.setBrandGuid("1234");
    attrGroupDO.setDescription(DESCRIPTION);
    attrGroupDO.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
    attrGroupDO.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
    attrGroupDO.setGuid("1234");
    attrGroupDO.setIconUrl("https://example.org/example");
    attrGroupDO.setId(1L);
    attrGroupDO.setIsDelete(2);
    attrGroupDO.setIsEnable(2);
    attrGroupDO.setIsMultiChoice(2);
    attrGroupDO.setIsRequired(2);
    attrGroupDO.setName("Name");
    attrGroupDO.setNameChange(2);
    attrGroupDO.setParentGuid("1234");
    attrGroupDO.setSort(2);
    attrGroupDO.setStoreGuid("1234");
    attrGroupDO.setWithDefault(2);
    return attrGroupDO;
  }

  /**
   * Method under test: {@link AttrGroupServiceImpl#removePushAttrGroup(String)}
   */
  @Test
  public void testRemovePushAttrGroup3() {
    AttrGroupDO attrGroupDO = getGroupDO();

    ArrayList<AttrGroupDO> attrGroupDOList = new ArrayList<>();
    attrGroupDOList.add(attrGroupDO);
    when(attrGroupMapper.selectList(Mockito.<Wrapper<AttrGroupDO>>any())).thenReturn(attrGroupDOList);
    when(iAttrService.list(Mockito.<Wrapper<AttrDO>>any())).thenReturn(new ArrayList<>());
    when(iRItemAttrGroupService.list(Mockito.<Wrapper<RItemAttrGroupDO>>any()))
            .thenThrow(new ParameterException(ERROR_OCCURRED));
    thrown.expect(ParameterException.class);
    attrGroupServiceImpl.removePushAttrGroup("1234");
    verify(attrGroupMapper).selectList(Mockito.<Wrapper<AttrGroupDO>>any());
    verify(iRItemAttrGroupService).list(Mockito.<Wrapper<RItemAttrGroupDO>>any());
  }

  /**
   * Method under test: {@link AttrGroupServiceImpl#removePushAttrGroup(String)}
   */
  @Test
  public void testRemovePushAttrGroup4() {
    AttrGroupDO attrGroupDO = getGroupDO();

    ArrayList<AttrGroupDO> attrGroupDOList = new ArrayList<>();
    attrGroupDOList.add(attrGroupDO);
    when(attrGroupMapper.deleteBatchIds(Mockito.<Collection<Serializable>>any())).thenReturn(-1);
    when(attrGroupMapper.selectList(Mockito.<Wrapper<AttrGroupDO>>any())).thenReturn(attrGroupDOList);
    when(iAttrService.list(Mockito.<Wrapper<AttrDO>>any())).thenReturn(new ArrayList<>());
    when(iRItemAttrGroupService.list(Mockito.<Wrapper<RItemAttrGroupDO>>any())).thenReturn(new ArrayList<>());
    Integer actualRemovePushAttrGroupResult = attrGroupServiceImpl.removePushAttrGroup("1234");
    verify(attrGroupMapper).deleteBatchIds(Mockito.<Collection<Serializable>>any());
    verify(attrGroupMapper).selectList(Mockito.<Wrapper<AttrGroupDO>>any());
    verify(iAttrService).list(Mockito.<Wrapper<AttrDO>>any());
    verify(iRItemAttrGroupService).list(Mockito.<Wrapper<RItemAttrGroupDO>>any());
    assertEquals(0, actualRemovePushAttrGroupResult.intValue());
  }

  /**
   * Method under test: {@link AttrGroupServiceImpl#removePushAttrGroup(String)}
   */
  @Test
  public void testRemovePushAttrGroup5() {
    AttrGroupDO attrGroupDO = getGroupDO();

    ArrayList<AttrGroupDO> attrGroupDOList = new ArrayList<>();
    attrGroupDOList.add(attrGroupDO);
    when(attrGroupMapper.selectBatchIds(Mockito.<Collection<Serializable>>any())).thenReturn(new ArrayList<>());
    when(attrGroupMapper.selectList(Mockito.<Wrapper<AttrGroupDO>>any())).thenReturn(attrGroupDOList);

    AttrDO attrDO = getAttrDO3();
    ArrayList<AttrDO> attrDOList = new ArrayList<>();
    attrDOList.add(attrDO);
    when(iAttrService.list(Mockito.<Wrapper<AttrDO>>any())).thenReturn(attrDOList);
    when(iRItemAttrGroupService.list(Mockito.<Wrapper<RItemAttrGroupDO>>any())).thenReturn(new ArrayList<>());
    doThrow(new ParameterException(ERROR_OCCURRED)).when(pushUtils)
            .fixFieldsFromPush2SelfCreate(Mockito.<List<Object>>any(), Mockito.<BiConsumer<Object, Integer>>any(),
                    Mockito.<BiConsumer<Object, String>>any());
    thrown.expect(ParameterException.class);
    attrGroupServiceImpl.removePushAttrGroup("1234");
    verify(attrGroupMapper).selectBatchIds(Mockito.<Collection<Serializable>>any());
    verify(attrGroupMapper).selectList(Mockito.<Wrapper<AttrGroupDO>>any());
    verify(iAttrService).list(Mockito.<Wrapper<AttrDO>>any());
    verify(iRItemAttrGroupService).list(Mockito.<Wrapper<RItemAttrGroupDO>>any());
    verify(pushUtils).fixFieldsFromPush2SelfCreate(Mockito.<List<Object>>any(),
            Mockito.<BiConsumer<Object, Integer>>any(), Mockito.<BiConsumer<Object, String>>any());
  }

  /**
   * Method under test:
   * {@link AttrGroupServiceImpl#selectAttrGroupSynRespDtoByItemGuidList(Collection)}
   */
  @Test
  public void testSelectAttrGroupSynRespDtoByItemGuidList() {
    assertTrue(attrGroupServiceImpl.selectAttrGroupSynRespDtoByItemGuidList(new ArrayList<>()).isEmpty());
  }

  /**
   * Method under test:
   * {@link AttrGroupServiceImpl#selectAttrGroupSynRespDtoByItemGuidList(Collection)}
   */
  @Test
  public void testSelectAttrGroupSynRespDtoByItemGuidList2() {
    when(iRItemAttrGroupService.list(Mockito.<Wrapper<RItemAttrGroupDO>>any())).thenReturn(new ArrayList<>());

    ArrayList<String> itemGuidList = new ArrayList<>();
    itemGuidList.add("foo");
    List<AttrGroupSynRespDTO> actualSelectAttrGroupSynRespDtoByItemGuidListResult = attrGroupServiceImpl
            .selectAttrGroupSynRespDtoByItemGuidList(itemGuidList);
    verify(iRItemAttrGroupService).list(Mockito.<Wrapper<RItemAttrGroupDO>>any());
    assertTrue(actualSelectAttrGroupSynRespDtoByItemGuidListResult.isEmpty());
  }

  /**
   * Method under test:
   * {@link AttrGroupServiceImpl#selectAttrGroupSynRespDtoByItemGuidList(Collection)}
   */
  @Test
  public void testSelectAttrGroupSynRespDtoByItemGuidList3() {
    RItemAttrGroupDO rItemAttrGroupDO = getrItemAttrGroupDO("1234", 1L, 1);

    ArrayList<RItemAttrGroupDO> rItemAttrGroupDOList = new ArrayList<>();
    rItemAttrGroupDOList.add(rItemAttrGroupDO);
    when(iRItemAttrGroupService.list(Mockito.<Wrapper<RItemAttrGroupDO>>any())).thenReturn(rItemAttrGroupDOList);
    when(iRAttrItemAttrGroupService.list(Mockito.<Wrapper<RAttrItemAttrGroupDO>>any())).thenReturn(new ArrayList<>());

    ArrayList<String> itemGuidList = new ArrayList<>();
    itemGuidList.add("foo");
    thrown.expect(BusinessException.class);
    attrGroupServiceImpl.selectAttrGroupSynRespDtoByItemGuidList(itemGuidList);
    verify(iRAttrItemAttrGroupService).list(Mockito.<Wrapper<RAttrItemAttrGroupDO>>any());
    verify(iRItemAttrGroupService).list(Mockito.<Wrapper<RItemAttrGroupDO>>any());
  }

  @NotNull
  private static RItemAttrGroupDO getrItemAttrGroupDO(String number, long id, int isDelete) {
    RItemAttrGroupDO rItemAttrGroupDO = new RItemAttrGroupDO();
    rItemAttrGroupDO.setAttrGroupGuid(number);
    rItemAttrGroupDO.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
    rItemAttrGroupDO.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
    rItemAttrGroupDO.setGuid(number);
    rItemAttrGroupDO.setId(id);
    rItemAttrGroupDO.setIsDelete(isDelete);
    rItemAttrGroupDO.setIsMultiChoice(isDelete);
    rItemAttrGroupDO.setIsRequired(isDelete);
    rItemAttrGroupDO.setItemGuid(number);
    rItemAttrGroupDO.setWithDefault(isDelete);
    return rItemAttrGroupDO;
  }

  /**
   * Method under test:
   * {@link AttrGroupServiceImpl#selectAttrGroupSynRespDtoByItemGuidList(Collection)}
   */
  @Test
  public void testSelectAttrGroupSynRespDtoByItemGuidList4() {
    when(attrGroupMapper.selectBatchIds(Mockito.<Collection<Serializable>>any())).thenReturn(new ArrayList<>());
    extracted3(new ArrayList<>());
  }

  private void extracted3(ArrayList<AttrDO> t) {
    when(iAttrService.listByIds(Mockito.<Collection<Serializable>>any())).thenReturn(t);

    RItemAttrGroupDO rItemAttrGroupDO = getrItemAttrGroupDO("1234", 1L, 1);

    ArrayList<RItemAttrGroupDO> rItemAttrGroupDOList = new ArrayList<>();
    rItemAttrGroupDOList.add(rItemAttrGroupDO);
    when(iRItemAttrGroupService.list(Mockito.<Wrapper<RItemAttrGroupDO>>any())).thenReturn(rItemAttrGroupDOList);

    RAttrItemAttrGroupDO rAttrItemAttrGroupDO = getrAttrItemAttrGroupDO("1234", 1L, 1);

    ArrayList<RAttrItemAttrGroupDO> rAttrItemAttrGroupDOList = new ArrayList<>();
    extracted(rAttrItemAttrGroupDO, rAttrItemAttrGroupDOList);
  }

  @NotNull
  private static RAttrItemAttrGroupDO getrAttrItemAttrGroupDO(String number, long id, int isDefault) {
    RAttrItemAttrGroupDO rAttrItemAttrGroupDO = new RAttrItemAttrGroupDO();
    rAttrItemAttrGroupDO.setAttrGuid(number);
    rAttrItemAttrGroupDO.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
    rAttrItemAttrGroupDO.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
    rAttrItemAttrGroupDO.setGuid(number);
    rAttrItemAttrGroupDO.setId(id);
    rAttrItemAttrGroupDO.setIsDefault(isDefault);
    rAttrItemAttrGroupDO.setIsDelete(isDefault);
    rAttrItemAttrGroupDO.setItemAttrGroupGuid(number);
    return rAttrItemAttrGroupDO;
  }

  /**
   * Method under test:
   * {@link AttrGroupServiceImpl#selectAttrGroupSynRespDtoByItemGuidList(Collection)}
   */
  @Test
  public void testSelectAttrGroupSynRespDtoByItemGuidList6() {
    AttrGroupDO attrGroupDO = getAttrGroupDO1();

    ArrayList<AttrGroupDO> attrGroupDOList = new ArrayList<>();
    attrGroupDOList.add(attrGroupDO);
    when(attrGroupMapper.selectBatchIds(Mockito.<Collection<Serializable>>any())).thenReturn(attrGroupDOList);
    extracted3(new ArrayList<>());
  }

  /**
   * Method under test:
   * {@link AttrGroupServiceImpl#selectAttrGroupSynRespDtoByItemGuidList(Collection)}
   */
  @Test
  public void testSelectAttrGroupSynRespDtoByItemGuidList7() {
    AttrGroupDO attrGroupDO = getAttrGroupDO1();

    AttrGroupDO attrGroupDO2 = getAttrGroupDO2();
    ArrayList<AttrGroupDO> attrGroupDOList = new ArrayList<>();
    attrGroupDOList.add(attrGroupDO2);
    attrGroupDOList.add(attrGroupDO);
    when(attrGroupMapper.selectBatchIds(Mockito.<Collection<Serializable>>any())).thenReturn(attrGroupDOList);
    extracted3(new ArrayList<>());
  }

  /**
   * Method under test:
   * {@link AttrGroupServiceImpl#selectAttrGroupSynRespDtoByItemGuidList(Collection)}
   */
  @Test
  public void testSelectAttrGroupSynRespDtoByItemGuidList8() {
    when(attrGroupMapper.selectBatchIds(Mockito.<Collection<Serializable>>any())).thenReturn(new ArrayList<>());

    AttrDO attrDO = getAttrDO();

    ArrayList<AttrDO> attrDOList = new ArrayList<>();
    attrDOList.add(attrDO);
    extracted3(attrDOList);
  }

  @NotNull
  private static AttrDO getAttrDO() {
    AttrDO attrDO = new AttrDO();
    attrDO.setAttrFrom(1);
    attrDO.setAttrGroupGuid("1234");
    attrDO.setBrandGuid("1234");
    attrDO.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
    attrDO.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
    attrDO.setGuid("1234");
    attrDO.setId(1L);
    attrDO.setIsDefault(1);
    attrDO.setIsDelete(1);
    attrDO.setName("1234");
    attrDO.setParentGuid("1234");
    attrDO.setPrice(new BigDecimal("2.3"));
    attrDO.setStoreGuid("1234");
    return attrDO;
  }

  /**
   * Method under test:
   * {@link AttrGroupServiceImpl#selectAttrGroupSynRespDtoByItemGuidList(Collection)}
   */
  @Test
  public void testSelectAttrGroupSynRespDtoByItemGuidList9() {
    when(attrGroupMapper.selectBatchIds(Mockito.<Collection<Serializable>>any())).thenReturn(new ArrayList<>());

    AttrDO attrDO = getAttrDO();

    AttrDO attrDO2 = getAttrDO2();
    attrDO2.setName("Name");
    attrDO2.setParentGuid(PARENT_GUID);
    attrDO2.setPrice(new BigDecimal("2.3"));
    attrDO2.setStoreGuid(STORE_GUID);

    ArrayList<AttrDO> attrDOList = new ArrayList<>();
    attrDOList.add(attrDO2);
    attrDOList.add(attrDO);
    extracted3(attrDOList);
  }

  /**
   * Method under test:
   * {@link AttrGroupServiceImpl#selectAttrGroupSynRespDtoByItemGuidList(Collection)}
   */
  @Test
  public void testSelectAttrGroupSynRespDtoByItemGuidList11() {
    when(attrGroupMapper.selectBatchIds(Mockito.<Collection<Serializable>>any())).thenReturn(new ArrayList<>());
    when(iAttrService.listByIds(Mockito.<Collection<Serializable>>any())).thenReturn(new ArrayList<>());

    RItemAttrGroupDO rItemAttrGroupDO = getrItemAttrGroupDO("1234", 1L, 1);

    ArrayList<RItemAttrGroupDO> rItemAttrGroupDOList = new ArrayList<>();
    rItemAttrGroupDOList.add(rItemAttrGroupDO);
    when(iRItemAttrGroupService.list(Mockito.<Wrapper<RItemAttrGroupDO>>any())).thenReturn(rItemAttrGroupDOList);

    RAttrItemAttrGroupDO rAttrItemAttrGroupDO = getrAttrItemAttrGroupDO("1234", 1L, 1);

    RAttrItemAttrGroupDO rAttrItemAttrGroupDO2 = getrAttrItemAttrGroupDO("Attr Guid", 2L, 4);

    ArrayList<RAttrItemAttrGroupDO> rAttrItemAttrGroupDOList = new ArrayList<>();
    rAttrItemAttrGroupDOList.add(rAttrItemAttrGroupDO2);
    extracted(rAttrItemAttrGroupDO, rAttrItemAttrGroupDOList);
  }

  private void extracted(RAttrItemAttrGroupDO rAttrItemAttrGroupDO, ArrayList<RAttrItemAttrGroupDO> rAttrItemAttrGroupDOList) {
    rAttrItemAttrGroupDOList.add(rAttrItemAttrGroupDO);
    when(iRAttrItemAttrGroupService.list(Mockito.<Wrapper<RAttrItemAttrGroupDO>>any()))
            .thenReturn(rAttrItemAttrGroupDOList);

    ArrayList<String> itemGuidList = new ArrayList<>();
    itemGuidList.add("foo");
    List<AttrGroupSynRespDTO> actualSelectAttrGroupSynRespDtoByItemGuidListResult = attrGroupServiceImpl
            .selectAttrGroupSynRespDtoByItemGuidList(itemGuidList);
    verify(attrGroupMapper).selectBatchIds(Mockito.<Collection<Serializable>>any());
    verify(iRAttrItemAttrGroupService).list(Mockito.<Wrapper<RAttrItemAttrGroupDO>>any());
    verify(iRItemAttrGroupService).list(Mockito.<Wrapper<RItemAttrGroupDO>>any());
    verify(iAttrService).listByIds(Mockito.<Collection<Serializable>>any());
    assertEquals(1, actualSelectAttrGroupSynRespDtoByItemGuidListResult.size());
  }

  /**
   * Method under test:
   * {@link AttrGroupServiceImpl#selectAttrGroupSynRespDtoByItemGuidList(Collection)}
   */
  @Test
  public void testSelectAttrGroupSynRespDtoByItemGuidList12() {
    when(attrGroupMapper.selectBatchIds(Mockito.<Collection<Serializable>>any())).thenReturn(new ArrayList<>());
    when(iAttrService.listByIds(Mockito.<Collection<Serializable>>any())).thenReturn(Lists.newArrayList(getAttrDO()));
    when(iRItemAttrGroupService.list(Mockito.any())).thenReturn(Lists.newArrayList(getrItemAttrGroupDO("1234", 1L, 1)));

    RAttrItemAttrGroupDO rAttrItemAttrGroupDO = getrAttrItemAttrGroupDO("1234", 1L, 1);

    RAttrItemAttrGroupDO rAttrItemAttrGroupDO2 = getrAttrItemAttrGroupDO("1234", 1L, 1);

    RAttrItemAttrGroupDO rAttrItemAttrGroupDO3 = getrAttrItemAttrGroupDO("1234", 1L, 1);

    RAttrItemAttrGroupDO rAttrItemAttrGroupDO4 = getrAttrItemAttrGroupDO("1234", 1L, 1);

    RAttrItemAttrGroupDO rAttrItemAttrGroupDO5 = getrAttrItemAttrGroupDO("1234", 1L, 1);

    RAttrItemAttrGroupDO rAttrItemAttrGroupDO6 = getrAttrItemAttrGroupDO("1234", 1L, 1);

    RAttrItemAttrGroupDO rAttrItemAttrGroupDO7 = getrAttrItemAttrGroupDO("1234", 1L, 1);

    RAttrItemAttrGroupDO rAttrItemAttrGroupDO8 = getrAttrItemAttrGroupDO("1234", 1L, 1);
    RAttrItemAttrGroupDO rAttrItemAttrGroupDO9 = mock(RAttrItemAttrGroupDO.class);
    when(rAttrItemAttrGroupDO9.getIsDefault()).thenReturn(1);
    when(rAttrItemAttrGroupDO9.setAttrGuid(Mockito.<String>any())).thenReturn(rAttrItemAttrGroupDO);
    when(rAttrItemAttrGroupDO9.setGmtCreate(Mockito.<LocalDateTime>any())).thenReturn(rAttrItemAttrGroupDO2);
    when(rAttrItemAttrGroupDO9.setGmtModified(Mockito.<LocalDateTime>any())).thenReturn(rAttrItemAttrGroupDO3);
    when(rAttrItemAttrGroupDO9.setGuid(Mockito.<String>any())).thenReturn(rAttrItemAttrGroupDO4);
    when(rAttrItemAttrGroupDO9.setId(Mockito.<Long>any())).thenReturn(rAttrItemAttrGroupDO5);
    when(rAttrItemAttrGroupDO9.setIsDefault(Mockito.<Integer>any())).thenReturn(rAttrItemAttrGroupDO6);
    when(rAttrItemAttrGroupDO9.setIsDelete(Mockito.<Integer>any())).thenReturn(rAttrItemAttrGroupDO7);
    when(rAttrItemAttrGroupDO9.setItemAttrGroupGuid(Mockito.<String>any())).thenReturn(rAttrItemAttrGroupDO8);
    when(rAttrItemAttrGroupDO9.getAttrGuid()).thenReturn("1234");
    when(rAttrItemAttrGroupDO9.getItemAttrGroupGuid()).thenReturn("1234");
    rAttrItemAttrGroupDO9.setAttrGuid("1234");
    rAttrItemAttrGroupDO9.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
    rAttrItemAttrGroupDO9.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
    rAttrItemAttrGroupDO9.setGuid("1234");
    rAttrItemAttrGroupDO9.setId(1L);
    rAttrItemAttrGroupDO9.setIsDefault(1);
    rAttrItemAttrGroupDO9.setIsDelete(1);
    rAttrItemAttrGroupDO9.setItemAttrGroupGuid("1234");

    ArrayList<RAttrItemAttrGroupDO> rAttrItemAttrGroupDOList = new ArrayList<>();
    rAttrItemAttrGroupDOList.add(rAttrItemAttrGroupDO9);
    when(iRAttrItemAttrGroupService.list(Mockito.<Wrapper<RAttrItemAttrGroupDO>>any()))
            .thenReturn(rAttrItemAttrGroupDOList);

    ArrayList<String> itemGuidList = new ArrayList<>();
    itemGuidList.add("foo");
    List<AttrGroupSynRespDTO> actualSelectAttrGroupSynRespDtoByItemGuidListResult = attrGroupServiceImpl
            .selectAttrGroupSynRespDtoByItemGuidList(itemGuidList);
    verify(attrGroupMapper).selectBatchIds(Mockito.<Collection<Serializable>>any());
    verify(iRAttrItemAttrGroupService).list(Mockito.<Wrapper<RAttrItemAttrGroupDO>>any());
    verify(iRItemAttrGroupService).list(Mockito.<Wrapper<RItemAttrGroupDO>>any());
    verify(iAttrService).listByIds(Mockito.<Collection<Serializable>>any());
    verify(rAttrItemAttrGroupDO9, atLeast(1)).getAttrGuid();
    verify(rAttrItemAttrGroupDO9).getIsDefault();
    verify(rAttrItemAttrGroupDO9).getItemAttrGroupGuid();
    verify(rAttrItemAttrGroupDO9).setAttrGuid(Mockito.<String>any());
    verify(rAttrItemAttrGroupDO9).setGmtCreate(Mockito.<LocalDateTime>any());
    verify(rAttrItemAttrGroupDO9).setGmtModified(Mockito.<LocalDateTime>any());
    verify(rAttrItemAttrGroupDO9).setGuid(Mockito.<String>any());
    verify(rAttrItemAttrGroupDO9).setId(Mockito.<Long>any());
    verify(rAttrItemAttrGroupDO9).setIsDefault(Mockito.<Integer>any());
    verify(rAttrItemAttrGroupDO9).setIsDelete(Mockito.<Integer>any());
    verify(rAttrItemAttrGroupDO9).setItemAttrGroupGuid(Mockito.<String>any());
    assertEquals(1, actualSelectAttrGroupSynRespDtoByItemGuidListResult.size());
  }

  /**
   * Method under test:
   * {@link AttrGroupServiceImpl#updateAttrGroup(AttrGroupUpdateReqDTO)}
   */
  @Test
  public void testUpdateAttrGroup() {
    when(attrGroupMapper.selectCount(Mockito.<Wrapper<AttrGroupDO>>any())).thenReturn(3);

    AttrGroupUpdateReqDTO attrGroupUpdateReqDTO = getAttrGroupUpdateReqDTO();
    thrown.expect(ParameterException.class);
    attrGroupServiceImpl.updateAttrGroup(attrGroupUpdateReqDTO);
    verify(attrGroupMapper).selectCount(Mockito.<Wrapper<AttrGroupDO>>any());
  }

  @NotNull
  private static AttrGroupUpdateReqDTO getAttrGroupUpdateReqDTO() {
    AttrGroupUpdateReqDTO attrGroupUpdateReqDTO = new AttrGroupUpdateReqDTO();
    attrGroupUpdateReqDTO.setAccount("3");
    attrGroupUpdateReqDTO.setAttrGroupGuid("1234");
    attrGroupUpdateReqDTO.setBrandGuid("1234");
    attrGroupUpdateReqDTO.setDefaultAttrGuidList(new ArrayList<>());
    attrGroupUpdateReqDTO.setDescription(DESCRIPTION);
    attrGroupUpdateReqDTO.setDeviceId("42");
    attrGroupUpdateReqDTO.setDeviceType(1);
    attrGroupUpdateReqDTO.setEnterpriseGuid("1234");
    attrGroupUpdateReqDTO.setEnterpriseName(ENTERPRISE_NAME);
    attrGroupUpdateReqDTO.setFrom(1);
    attrGroupUpdateReqDTO.setIsEnable(1);
    attrGroupUpdateReqDTO.setIsMultiChoice(1);
    attrGroupUpdateReqDTO.setIsRequired(1);
    attrGroupUpdateReqDTO.setName("Name");
    attrGroupUpdateReqDTO.setOperSubjectGuid("1234");
    attrGroupUpdateReqDTO.setRequestTimestamp(1L);
    attrGroupUpdateReqDTO.setSort(1);
    attrGroupUpdateReqDTO.setStoreGuid("1234");
    attrGroupUpdateReqDTO.setStoreName(STORE_NAME);
    attrGroupUpdateReqDTO.setUserGuid("1234");
    attrGroupUpdateReqDTO.setUserName(USER_NAME);
    return attrGroupUpdateReqDTO;
  }

  /**
   * Method under test:
   * {@link AttrGroupServiceImpl#updateAttrGroup(AttrGroupUpdateReqDTO)}
   */
  @Test
  public void testUpdateAttrGroup2() {
    when(attrGroupMapper.update(Mockito.<AttrGroupDO>any(), Mockito.<Wrapper<AttrGroupDO>>any())).thenReturn(1);
    when(attrGroupMapper.updateById(Mockito.<AttrGroupDO>any())).thenReturn(1);
    when(attrGroupMapper.selectCount(Mockito.<Wrapper<AttrGroupDO>>any())).thenReturn(0);
    when(iAttrService.list(Mockito.<Wrapper<AttrDO>>any())).thenReturn(new ArrayList<>());

    AttrGroupUpdateReqDTO attrGroupUpdateReqDTO = getAttrGroupUpdateReqDTO();
    boolean actualUpdateAttrGroupResult = attrGroupServiceImpl.updateAttrGroup(attrGroupUpdateReqDTO);
    verify(attrGroupMapper).selectCount(Mockito.<Wrapper<AttrGroupDO>>any());
    verify(attrGroupMapper).update(Mockito.<AttrGroupDO>any(), Mockito.<Wrapper<AttrGroupDO>>any());
    verify(attrGroupMapper).updateById(Mockito.<AttrGroupDO>any());
    verify(iAttrService).list(Mockito.<Wrapper<AttrDO>>any());
    assertTrue(actualUpdateAttrGroupResult);
  }

  /**
   * Method under test:
   * {@link AttrGroupServiceImpl#updateAttrGroup(AttrGroupUpdateReqDTO)}
   */
  @Test
  public void testUpdateAttrGroup3() {
    when(attrGroupMapper.update(Mockito.<AttrGroupDO>any(), Mockito.<Wrapper<AttrGroupDO>>any())).thenReturn(1);
    when(attrGroupMapper.updateById(Mockito.<AttrGroupDO>any())).thenReturn(1);
    when(attrGroupMapper.selectCount(Mockito.<Wrapper<AttrGroupDO>>any())).thenReturn(0);
    when(iAttrService.list(Mockito.<Wrapper<AttrDO>>any())).thenThrow(new ParameterException(ERROR_OCCURRED));

    AttrGroupUpdateReqDTO attrGroupUpdateReqDTO = getAttrGroupUpdateReqDTO();
    thrown.expect(ParameterException.class);
    attrGroupServiceImpl.updateAttrGroup(attrGroupUpdateReqDTO);
    verify(attrGroupMapper).selectCount(Mockito.<Wrapper<AttrGroupDO>>any());
    verify(attrGroupMapper).update(Mockito.<AttrGroupDO>any(), Mockito.<Wrapper<AttrGroupDO>>any());
    verify(attrGroupMapper).updateById(Mockito.<AttrGroupDO>any());
    verify(iAttrService).list(Mockito.<Wrapper<AttrDO>>any());
  }

  /**
   * Method under test:
   * {@link AttrGroupServiceImpl#updateAttrGroup(AttrGroupUpdateReqDTO)}
   */
  @Test
  public void testUpdateAttrGroup4() {
    when(attrGroupMapper.updateById(Mockito.<AttrGroupDO>any())).thenReturn(0);
    when(attrGroupMapper.selectCount(Mockito.<Wrapper<AttrGroupDO>>any())).thenReturn(0);

    AttrGroupUpdateReqDTO attrGroupUpdateReqDTO = getAttrGroupUpdateReqDTO();
    thrown.expect(BusinessException.class);
    attrGroupServiceImpl.updateAttrGroup(attrGroupUpdateReqDTO);
    verify(attrGroupMapper).selectCount(Mockito.<Wrapper<AttrGroupDO>>any());
    verify(attrGroupMapper).updateById(Mockito.<AttrGroupDO>any());
  }

  /**
   * Method under test:
   * {@link AttrGroupServiceImpl#updateAttrGroup(AttrGroupUpdateReqDTO)}
   */
  @Test
  public void testUpdateAttrGroup5() {
    when(attrGroupMapper.update(Mockito.<AttrGroupDO>any(), Mockito.<Wrapper<AttrGroupDO>>any())).thenReturn(1);
    when(attrGroupMapper.updateById(Mockito.<AttrGroupDO>any())).thenReturn(1);
    when(attrGroupMapper.selectCount(Mockito.<Wrapper<AttrGroupDO>>any())).thenReturn(0);

    AttrDO attrDO = getAttrDO3();

    ArrayList<AttrDO> attrDOList = new ArrayList<>();
    extracted(attrDO, attrDOList);
  }

  /**
   * Method under test:
   * {@link AttrGroupServiceImpl#updateAttrGroup(AttrGroupUpdateReqDTO)}
   */
  @Test
  public void testUpdateAttrGroup6() {
    when(attrGroupMapper.update(Mockito.<AttrGroupDO>any(), Mockito.<Wrapper<AttrGroupDO>>any())).thenReturn(1);
    when(attrGroupMapper.updateById(Mockito.<AttrGroupDO>any())).thenReturn(1);
    when(attrGroupMapper.selectCount(Mockito.<Wrapper<AttrGroupDO>>any())).thenReturn(0);

    AttrDO attrDO = getAttrDO3();

    AttrDO attrDO2 = getAttrDO2();
    attrDO2.setName("com.holderzone.saas.store.item.entity.domain.AttrDO");
    attrDO2.setParentGuid(PARENT_GUID);
    attrDO2.setPrice(new BigDecimal("2.3"));
    attrDO2.setStoreGuid(STORE_GUID);

    ArrayList<AttrDO> attrDOList = new ArrayList<>();
    attrDOList.add(attrDO2);
    extracted(attrDO, attrDOList);
  }

  private void extracted(AttrDO attrDO, ArrayList<AttrDO> attrDOList) {
    attrDOList.add(attrDO);
    when(iAttrService.updateBatchById(Mockito.<Collection<AttrDO>>any(), anyInt())).thenReturn(true);
    when(iAttrService.list(Mockito.<Wrapper<AttrDO>>any())).thenReturn(attrDOList);

    AttrGroupUpdateReqDTO attrGroupUpdateReqDTO = getAttrGroupUpdateReqDTO();
    boolean actualUpdateAttrGroupResult = attrGroupServiceImpl.updateAttrGroup(attrGroupUpdateReqDTO);
    verify(attrGroupMapper).selectCount(Mockito.<Wrapper<AttrGroupDO>>any());
    verify(attrGroupMapper).update(Mockito.<AttrGroupDO>any(), Mockito.<Wrapper<AttrGroupDO>>any());
    verify(attrGroupMapper).updateById(Mockito.<AttrGroupDO>any());
    verify(iAttrService).list(Mockito.<Wrapper<AttrDO>>any());
    verify(iAttrService).updateBatchById(Mockito.<Collection<AttrDO>>any(), anyInt());
    assertTrue(actualUpdateAttrGroupResult);
  }

  @NotNull
  private static AttrDO getAttrDO2() {
    AttrDO attrDO2 = new AttrDO();
    attrDO2.setAttrFrom(4);
    attrDO2.setAttrGroupGuid("Attr Group Guid");
    attrDO2.setBrandGuid("Brand Guid");
    attrDO2.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
    attrDO2.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
    attrDO2.setGuid("Guid");
    attrDO2.setId(2L);
    attrDO2.setIsDefault(4);
    attrDO2.setIsDelete(4);
    return attrDO2;
  }

  /**
   * Method under test: {@link AttrGroupServiceImpl#deleteByGuid(ItemSingleDTO)}
   */
  @Test
  public void testDeleteByGuid() {
    when(attrGroupMapper.delete(Mockito.<Wrapper<AttrGroupDO>>any())).thenReturn(1);
    when(attrGroupMapper.selectList(Mockito.<Wrapper<AttrGroupDO>>any())).thenReturn(new ArrayList<>());
    when(iAttrService.list(Mockito.<Wrapper<AttrDO>>any())).thenReturn(new ArrayList<>());
    when(iRItemAttrGroupService.list(Mockito.<Wrapper<RItemAttrGroupDO>>any())).thenReturn(new ArrayList<>());
    when(iItemService.update(Mockito.<ItemDO>any(), Mockito.<Wrapper<ItemDO>>any())).thenReturn(true);

    ItemSingleDTO itemSingleDTO = getSingleDTO();
    boolean actualDeleteByGuidResult = attrGroupServiceImpl.deleteByGuid(itemSingleDTO);
    extracted();
    assertTrue(actualDeleteByGuidResult);
  }

  /**
   * Method under test: {@link AttrGroupServiceImpl#deleteByGuid(ItemSingleDTO)}
   */
  @Test
  public void testDeleteByGuid2() {
    when(attrGroupMapper.delete(Mockito.<Wrapper<AttrGroupDO>>any())).thenReturn(0);
    when(attrGroupMapper.selectList(Mockito.<Wrapper<AttrGroupDO>>any())).thenReturn(new ArrayList<>());
    when(iAttrService.list(Mockito.<Wrapper<AttrDO>>any())).thenReturn(new ArrayList<>());
    when(iRItemAttrGroupService.list(Mockito.<Wrapper<RItemAttrGroupDO>>any())).thenReturn(new ArrayList<>());
    when(iItemService.update(Mockito.<ItemDO>any(), Mockito.<Wrapper<ItemDO>>any())).thenReturn(true);

    ItemSingleDTO itemSingleDTO = getSingleDTO();
    boolean actualDeleteByGuidResult = attrGroupServiceImpl.deleteByGuid(itemSingleDTO);
    extracted();
    assertFalse(actualDeleteByGuidResult);
  }

  private void extracted() {
    verify(attrGroupMapper).delete(Mockito.<Wrapper<AttrGroupDO>>any());
    verify(attrGroupMapper).selectList(Mockito.<Wrapper<AttrGroupDO>>any());
    verify(iAttrService).list(Mockito.<Wrapper<AttrDO>>any());
    verify(iRItemAttrGroupService).list(Mockito.<Wrapper<RItemAttrGroupDO>>any());
    verify(iItemService).update(Mockito.<ItemDO>any(), Mockito.<Wrapper<ItemDO>>any());
  }

  /**
   * Method under test: {@link AttrGroupServiceImpl#deleteByGuid(ItemSingleDTO)}
   */
  @Test
  public void testDeleteByGuid3() {
    AttrGroupDO attrGroupDO = getAttrGroupDO();

    ArrayList<AttrGroupDO> attrGroupDOList = new ArrayList<>();
    attrGroupDOList.add(attrGroupDO);
    extracted(attrGroupDOList);
    when(iAttrService.list(Mockito.<Wrapper<AttrDO>>any())).thenReturn(new ArrayList<>());
    when(iRItemAttrGroupService.list(Mockito.<Wrapper<RItemAttrGroupDO>>any())).thenReturn(new ArrayList<>());
    when(iItemService.update(Mockito.<ItemDO>any(), Mockito.<Wrapper<ItemDO>>any())).thenReturn(true);

    ItemSingleDTO itemSingleDTO = getSingleDTO();
    boolean actualDeleteByGuidResult = attrGroupServiceImpl.deleteByGuid(itemSingleDTO);
    extracted1();
    verify(iAttrService, atLeast(1)).list(Mockito.<Wrapper<AttrDO>>any());
    verify(iRItemAttrGroupService, atLeast(1)).list(Mockito.<Wrapper<RItemAttrGroupDO>>any());
    verify(iItemService).update(Mockito.<ItemDO>any(), Mockito.<Wrapper<ItemDO>>any());
    assertTrue(actualDeleteByGuidResult);
  }

  /**
   * Method under test: {@link AttrGroupServiceImpl#deleteByGuid(ItemSingleDTO)}
   */
  @Test
  public void testDeleteByGuid4() {
    AttrGroupDO attrGroupDO = getAttrGroupDO();

    ArrayList<AttrGroupDO> attrGroupDOList = new ArrayList<>();
    attrGroupDOList.add(attrGroupDO);
    extracted(attrGroupDOList);

    AttrDO attrDO = getAttrDO3();

    ArrayList<AttrDO> attrDOList = new ArrayList<>();
    attrDOList.add(attrDO);
    when(iAttrService.deleteByGroup(Mockito.<String>any())).thenReturn(true);
    doNothing().when(iAttrService).deleteOrUpdatePushAttrAfterDeletePushRelation(Mockito.<List<AttrDO>>any());
    doNothing().when(iAttrService)
            .deletePushAttrRelate(Mockito.<List<RAttrItemAttrGroupDO>>any(), Mockito.<List<AttrDO>>any());
    when(iAttrService.list(Mockito.<Wrapper<AttrDO>>any())).thenReturn(attrDOList);
    when(iRTypeAttrService.remove(Mockito.<Wrapper<RTypeAttrDO>>any())).thenReturn(true);
    when(iRItemAttrGroupService.list(Mockito.<Wrapper<RItemAttrGroupDO>>any())).thenReturn(new ArrayList<>());
    when(iItemService.update(Mockito.<ItemDO>any(), Mockito.<Wrapper<ItemDO>>any())).thenReturn(true);
    when(iRAttrItemAttrGroupService.list(Mockito.<Wrapper<RAttrItemAttrGroupDO>>any())).thenReturn(new ArrayList<>());

    ItemSingleDTO itemSingleDTO = getSingleDTO();
    boolean actualDeleteByGuidResult = attrGroupServiceImpl.deleteByGuid(itemSingleDTO);
    extracted1();
    verify(iRAttrItemAttrGroupService).list(Mockito.<Wrapper<RAttrItemAttrGroupDO>>any());
    verify(iRItemAttrGroupService, atLeast(1)).list(Mockito.<Wrapper<RItemAttrGroupDO>>any());
    verify(iAttrService, atLeast(1)).list(Mockito.<Wrapper<AttrDO>>any());
    verify(iRTypeAttrService).remove(Mockito.<Wrapper<RTypeAttrDO>>any());
    verify(iItemService).update(Mockito.<ItemDO>any(), Mockito.<Wrapper<ItemDO>>any());
    verify(iAttrService).deleteByGroup(Mockito.<String>any());
    verify(iAttrService).deleteOrUpdatePushAttrAfterDeletePushRelation(Mockito.<List<AttrDO>>any());
    verify(iAttrService).deletePushAttrRelate(Mockito.<List<RAttrItemAttrGroupDO>>any(), Mockito.<List<AttrDO>>any());
    assertTrue(actualDeleteByGuidResult);
  }

  private void extracted1() {
    verify(attrGroupMapper).delete(Mockito.<Wrapper<AttrGroupDO>>any());
    verify(attrGroupMapper).deleteBatchIds(Mockito.<Collection<Serializable>>any());
    verify(attrGroupMapper).selectList(Mockito.<Wrapper<AttrGroupDO>>any());
  }

  private void extracted(ArrayList<AttrGroupDO> attrGroupDOList) {
    when(attrGroupMapper.deleteBatchIds(Mockito.<Collection<Serializable>>any())).thenReturn(1);
    when(attrGroupMapper.delete(Mockito.<Wrapper<AttrGroupDO>>any())).thenReturn(1);
    when(attrGroupMapper.selectList(Mockito.<Wrapper<AttrGroupDO>>any())).thenReturn(attrGroupDOList);
  }

  /**
   * Method under test: {@link AttrGroupServiceImpl#deleteByGuid(ItemSingleDTO)}
   */
  @Test
  public void testDeleteByGuid5() {
    when(attrGroupMapper.delete(Mockito.<Wrapper<AttrGroupDO>>any())).thenReturn(1);

    AttrDO attrDO = getAttrDO3();

    ArrayList<AttrDO> attrDOList = new ArrayList<>();
    attrDOList.add(attrDO);
    when(iAttrService.deleteByGroup(Mockito.<String>any())).thenReturn(true);
    when(iAttrService.list(Mockito.<Wrapper<AttrDO>>any())).thenReturn(attrDOList);
    when(iRTypeAttrService.remove(Mockito.<Wrapper<RTypeAttrDO>>any())).thenReturn(true);
    when(iRItemAttrGroupService.list(Mockito.<Wrapper<RItemAttrGroupDO>>any())).thenReturn(new ArrayList<>());
    when(iRAttrItemAttrGroupService.list(Mockito.<Wrapper<RAttrItemAttrGroupDO>>any()))
            .thenThrow(new ParameterException(ERROR_OCCURRED));

    ItemSingleDTO itemSingleDTO = getSingleDTO();
    thrown.expect(ParameterException.class);
    attrGroupServiceImpl.deleteByGuid(itemSingleDTO);
    verify(attrGroupMapper).delete(Mockito.<Wrapper<AttrGroupDO>>any());
    verify(iAttrService).list(Mockito.<Wrapper<AttrDO>>any());
    verify(iRAttrItemAttrGroupService).list(Mockito.<Wrapper<RAttrItemAttrGroupDO>>any());
    verify(iRTypeAttrService).remove(Mockito.<Wrapper<RTypeAttrDO>>any());
    verify(iAttrService).deleteByGroup(Mockito.<String>any());
  }
}
