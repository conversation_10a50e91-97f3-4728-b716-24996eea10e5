package com.holderzone.saas.store.item.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.item.req.SubItemSkuReqDTO;
import com.holderzone.saas.store.item.entity.domain.RSkuSubgroupDO;
import com.holderzone.saas.store.item.entity.domain.SubgroupDO;
import com.holderzone.saas.store.item.mapper.RSkuSubgroupMapper;
import com.holderzone.saas.store.item.service.ISubgroupService;
import com.holderzone.saas.store.item.util.DynamicHelper;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.RedisTemplate;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class RSkuSubgroupServiceImplTest {

    @Mock
    private ISubgroupService mockSubgroupService;
    @Mock
    private DynamicHelper mockDynamicHelper;
    @Mock
    private RedisTemplate mockRedisTemplate;
    @Mock
    private RSkuSubgroupMapper mockRSkuSubgroupMapper;

    private RSkuSubgroupServiceImpl rSkuSubgroupServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        rSkuSubgroupServiceImplUnderTest = new RSkuSubgroupServiceImpl(mockSubgroupService, mockDynamicHelper,
                mockRedisTemplate, mockRSkuSubgroupMapper);
    }

    @Test
    public void testRemoveSkuSubgroupByPkgGuidList() {
        // Setup
        // Configure ISubgroupService.list(...).
        final SubgroupDO subgroupDO = new SubgroupDO();
        subgroupDO.setId(0L);
        subgroupDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        subgroupDO.setGuid("guid");
        subgroupDO.setItemGuid("itemGuid");
        subgroupDO.setPickNum(0);
        final List<SubgroupDO> subgroupDOS = Arrays.asList(subgroupDO);
        when(mockSubgroupService.list(any(LambdaQueryWrapper.class))).thenReturn(subgroupDOS);

        // Run the test
        final Integer result = rSkuSubgroupServiceImplUnderTest.removeSkuSubgroupByPkgGuidList(Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    public void testRemoveSkuSubgroupByPkgGuidList_ISubgroupServiceReturnsNoItems() {
        // Setup
        when(mockSubgroupService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        assertThatThrownBy(() -> rSkuSubgroupServiceImplUnderTest.removeSkuSubgroupByPkgGuidList(
                Arrays.asList("value"))).isInstanceOf(BusinessException.class);
    }

    @Test
    public void testBuildRSkuSubgroupDOWithSubgroupDO() {
        // Setup
        final SubItemSkuReqDTO subItemSkuReqDTO = new SubItemSkuReqDTO();
        subItemSkuReqDTO.setSkuSubgroupGuid("skuSubgroupGuid");
        subItemSkuReqDTO.setSkuGuid("skuGuid");
        subItemSkuReqDTO.setItemGuid("itemGuid");
        subItemSkuReqDTO.setItemNum(new BigDecimal("0.00"));
        subItemSkuReqDTO.setAddPrice(new BigDecimal("0.00"));
        final List<SubItemSkuReqDTO> subItemSkuList = Arrays.asList(subItemSkuReqDTO);
        final SubgroupDO subgroupDO = new SubgroupDO();
        subgroupDO.setId(0L);
        subgroupDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        subgroupDO.setGuid("guid");
        subgroupDO.setItemGuid("itemGuid");
        subgroupDO.setPickNum(0);

        final RSkuSubgroupDO rSkuSubgroupDO = new RSkuSubgroupDO();
        rSkuSubgroupDO.setGuid("64920936-ad8e-4b4f-a4af-4645d23bd947");
        rSkuSubgroupDO.setSubgroupGuid("guid");
        rSkuSubgroupDO.setAddPrice(new BigDecimal("0.00"));
        rSkuSubgroupDO.setIsDefault(0);
        rSkuSubgroupDO.setIsRepeat(0);
        final List<RSkuSubgroupDO> expectedResult = Arrays.asList(rSkuSubgroupDO);

        // Run the test
        final List<RSkuSubgroupDO> result = rSkuSubgroupServiceImplUnderTest.buildRSkuSubgroupDOWithSubgroupDO(
                subItemSkuList, subgroupDO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testUpdateIsDelete() {
        // Setup
        when(mockRSkuSubgroupMapper.updateIsDelete("be905c4f-0ae1-415e-8589-de3393426278")).thenReturn(false);

        // Run the test
        final Boolean result = rSkuSubgroupServiceImplUnderTest.updateIsDelete("be905c4f-0ae1-415e-8589-de3393426278");

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testUpdateIsDelete_RSkuSubgroupMapperReturnsTrue() {
        // Setup
        when(mockRSkuSubgroupMapper.updateIsDelete("be905c4f-0ae1-415e-8589-de3393426278")).thenReturn(true);

        // Run the test
        final Boolean result = rSkuSubgroupServiceImplUnderTest.updateIsDelete("be905c4f-0ae1-415e-8589-de3393426278");

        // Verify the results
        assertThat(result).isTrue();
    }

    @Test
    public void testDeleteByItemGuid() {
        // Setup
        when(mockRSkuSubgroupMapper.deleteByItemGuid("itemGuid")).thenReturn(false);

        // Run the test
        final Boolean result = rSkuSubgroupServiceImplUnderTest.deleteByItemGuid("itemGuid");

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testDeleteByItemGuid_RSkuSubgroupMapperReturnsTrue() {
        // Setup
        when(mockRSkuSubgroupMapper.deleteByItemGuid("itemGuid")).thenReturn(true);

        // Run the test
        final Boolean result = rSkuSubgroupServiceImplUnderTest.deleteByItemGuid("itemGuid");

        // Verify the results
        assertThat(result).isTrue();
    }
}
