package com.holderzone.saas.store.item.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.config.req.ConfigReqDTO;
import com.holderzone.saas.store.dto.config.req.ConfigReqQueryDTO;
import com.holderzone.saas.store.dto.config.resp.ConfigRespDTO;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.common.ItemSkuStockDTO;
import com.holderzone.saas.store.dto.item.req.EstimateBatchReqDTO;
import com.holderzone.saas.store.dto.item.req.EstimateForManualReqDTO;
import com.holderzone.saas.store.dto.item.req.EstimateMerchantReqDTO;
import com.holderzone.saas.store.dto.item.req.EstimateReqDTO;
import com.holderzone.saas.store.dto.item.resp.*;
import com.holderzone.saas.store.dto.item.resp.estimate.EstimateForAndroidRespDTO;
import com.holderzone.saas.store.dto.item.resp.estimate.EstimateItemRespDTO;
import com.holderzone.saas.store.dto.item.resp.estimate.EstimateResultRespDTO;
import com.holderzone.saas.store.dto.item.resp.estimate.EstimateSkuRespDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.common.PackageSubgroupDTO;
import com.holderzone.saas.store.dto.order.common.SubDineInItemDTO;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.dto.organization.BusinessDateReqDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.store.store.StoreProductDTO;
import com.holderzone.saas.store.item.entity.bo.EstimateBO;
import com.holderzone.saas.store.item.entity.domain.EstimateDO;
import com.holderzone.saas.store.item.entity.domain.EstimateOpLogDO;
import com.holderzone.saas.store.item.entity.query.SetMealEstimateQuery;
import com.holderzone.saas.store.item.helper.EventPushHelper;
import com.holderzone.saas.store.item.helper.ItemHelper;
import com.holderzone.saas.store.item.helper.PageAdapter;
import com.holderzone.saas.store.item.mapper.EstimateMapper;
import com.holderzone.saas.store.item.mapper.SkuMapper;
import com.holderzone.saas.store.item.mapper.SubgroupMapper;
import com.holderzone.saas.store.item.repository.EstimateRepository;
import com.holderzone.saas.store.item.service.IEstimateOpLogService;
import com.holderzone.saas.store.item.service.IItemService;
import com.holderzone.saas.store.item.service.IPricePlanStoreService;
import com.holderzone.saas.store.item.service.ISkuService;
import com.holderzone.saas.store.item.service.rpc.ConfigFeginService;
import com.holderzone.saas.store.item.service.rpc.OrganizationService;
import com.holderzone.saas.store.item.util.DynamicHelper;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionException;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class IEstimateServiceImplTest {

    @Mock
    private SkuMapper mockSkuMapper;
    @Mock
    private EstimateMapper mockEstimateMapper;
    @Mock
    private DynamicHelper mockDynamicHelper;
    @Mock
    private ItemHelper mockItemHelper;
    @Mock
    private ISkuService mockSkuService;
    @Mock
    private IItemService mockItemService;
    @Mock
    private ConfigFeginService mockConfigFeginService;
    @Mock
    private PlatformTransactionManager mockTxManager;
    @Mock
    private RedisTemplate mockRedisTemplate;
    @Mock
    private OrganizationService mockOrganizationService;
    @Mock
    private EventPushHelper mockEventPushHelper;
    @Mock
    private IEstimateOpLogService mockEstimateOpLogService;
    @Mock
    private SubgroupMapper mockSubgroupMapper;
    @Mock
    private IPricePlanStoreService mockPricePlanStoreService;
    @Mock
    private EstimateRepository mockEstimateRepository;

    private IEstimateServiceImpl iEstimateServiceImplUnderTest;

    @Before
    public void setUp() {
        iEstimateServiceImplUnderTest = new IEstimateServiceImpl(mockSkuMapper, mockEstimateMapper, mockDynamicHelper,
                mockItemHelper, mockSkuService, mockItemService, mockConfigFeginService, mockTxManager,
                mockRedisTemplate, mockOrganizationService, mockEventPushHelper, mockEstimateOpLogService);
        ReflectionTestUtils.setField(iEstimateServiceImplUnderTest, "corePoolSize", 0);
        ReflectionTestUtils.setField(iEstimateServiceImplUnderTest, "maximumPoolSize", 0);
        ReflectionTestUtils.setField(iEstimateServiceImplUnderTest, "keepAliveTime", 0);
        ReflectionTestUtils.setField(iEstimateServiceImplUnderTest, "maxQueueSize", 0);
        ReflectionTestUtils.setField(iEstimateServiceImplUnderTest, "subgroupMapper", mockSubgroupMapper);
        ReflectionTestUtils.setField(iEstimateServiceImplUnderTest, "pricePlanStoreService", mockPricePlanStoreService);
        ReflectionTestUtils.setField(iEstimateServiceImplUnderTest, "estimateRepository", mockEstimateRepository);
    }

    @Test
    public void testSaveOrUpdate() {
        // Setup
        final EstimateReqDTO request = new EstimateReqDTO();
        request.setSkuGuid("skuGuid");
        request.setIsSoldOut(0);
        request.setLimitQuantity(new BigDecimal("0.00"));
        request.setIsItReset(0);
        request.setStoreGuid("storeGuid");

        // Configure ConfigFeginService.selectEstimateResetTime(...).
        final ConfigRespDTO configRespDTO = new ConfigRespDTO();
        configRespDTO.setGuid("8332cc82-c836-4ef1-82b0-1e29be41974c");
        configRespDTO.setEnterpriseGuid("enterpriseGuid");
        configRespDTO.setStoreGuid("storeGuid");
        configRespDTO.setDicCode(0);
        configRespDTO.setDicName("desc");
        configRespDTO.setDictValue("dictValue");
        final ConfigReqQueryDTO configReqQueryDTO = new ConfigReqQueryDTO();
        configReqQueryDTO.setEnterpriseGuid("enterpriseGuid");
        configReqQueryDTO.setStoreGuid("storeGuid");
        configReqQueryDTO.setDicCode(0);
        when(mockConfigFeginService.selectEstimateResetTime(configReqQueryDTO)).thenReturn(configRespDTO);

        // Run the test
        final Boolean result = iEstimateServiceImplUnderTest.saveOrUpdate(request);

        // Verify the results
        assertThat(result).isFalse();
        verify(mockConfigFeginService).saveEstimateResetTime(
                new ConfigReqDTO("6a6349ef-a531-47af-a6d2-25dd0f30aac3", "enterpriseGuid", "storeGuid", 0, "dicName",
                        "dictValue", LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0));
        verify(mockSkuMapper).updateStockStatus("skuGuid", new BigDecimal("0.00"), 0);
        verify(mockRedisTemplate).delete("key");
    }

    @Test
    public void testBatchSave() {
        // Setup
        final EstimateBatchReqDTO request = new EstimateBatchReqDTO();
        request.setSkuGuidList(Arrays.asList("value"));
        request.setIsSoldOut(0);
        request.setIsTheLimit(0);
        request.setIsTheLimitReset(0);
        request.setStoreGuid("storeGuid");

        // Configure ConfigFeginService.selectEstimateResetTime(...).
        final ConfigRespDTO configRespDTO = new ConfigRespDTO();
        configRespDTO.setGuid("8332cc82-c836-4ef1-82b0-1e29be41974c");
        configRespDTO.setEnterpriseGuid("enterpriseGuid");
        configRespDTO.setStoreGuid("storeGuid");
        configRespDTO.setDicCode(0);
        configRespDTO.setDicName("desc");
        configRespDTO.setDictValue("dictValue");
        final ConfigReqQueryDTO configReqQueryDTO = new ConfigReqQueryDTO();
        configReqQueryDTO.setEnterpriseGuid("enterpriseGuid");
        configReqQueryDTO.setStoreGuid("storeGuid");
        configReqQueryDTO.setDicCode(0);
        when(mockConfigFeginService.selectEstimateResetTime(configReqQueryDTO)).thenReturn(configRespDTO);

        // Run the test
        final Boolean result = iEstimateServiceImplUnderTest.batchSave(request);

        // Verify the results
        assertThat(result).isFalse();
        verify(mockConfigFeginService).saveEstimateResetTime(
                new ConfigReqDTO("6a6349ef-a531-47af-a6d2-25dd0f30aac3", "enterpriseGuid", "storeGuid", 0, "dicName",
                        "dictValue", LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0));
        verify(mockSkuMapper).updateStockStatus("skuGuid", new BigDecimal("0.00"), 0);
        verify(mockRedisTemplate).delete("key");
    }

    @Test
    public void testGetEstimateReqDTO() {
        // Setup
        final EstimateReqDTO request = new EstimateReqDTO();
        request.setSkuGuid("skuGuid");
        request.setIsSoldOut(0);
        request.setLimitQuantity(new BigDecimal("0.00"));
        request.setIsItReset(0);
        request.setStoreGuid("storeGuid");

        final EstimateRespDTO expectedResult = new EstimateRespDTO(0L, "15d052be-386c-4855-8887-1b1a081dce99",
                "skuGuid", 0, 0, 0, 0, 0, 0, "storeGuid");

        // Run the test
        final EstimateRespDTO result = iEstimateServiceImplUnderTest.getEstimateReqDTO(request);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetItemEstimates() {
        // Setup
        final EstimateMerchantReqDTO request = new EstimateMerchantReqDTO();
        request.setStoreGuid("storeGuid");
        request.setSortGuid("sortGuid");
        request.setTypGuid(0);
        request.setIsTheLimit(0);
        request.setKeywords("keywords");

        // Configure EstimateMapper.getItemEstimates(...).
        final EstimateMerchantReqDTO request1 = new EstimateMerchantReqDTO();
        request1.setStoreGuid("storeGuid");
        request1.setSortGuid("sortGuid");
        request1.setTypGuid(0);
        request1.setIsTheLimit(0);
        request1.setKeywords("keywords");
        when(mockEstimateMapper.getItemEstimates(any(PageAdapter.class), eq(request1))).thenReturn(null);

        // Run the test
        final Page<EstimateMerchantConfigRespDTO> result = iEstimateServiceImplUnderTest.getItemEstimates(request);

        // Verify the results
    }

    @Test
    public void testGetEstimateItemResidue() {
        // Setup
        final EstimateMerchantReqDTO request = new EstimateMerchantReqDTO();
        request.setStoreGuid("storeGuid");
        request.setSortGuid("sortGuid");
        request.setTypGuid(0);
        request.setIsTheLimit(0);
        request.setKeywords("keywords");

        // Configure EstimateMapper.getEstimateItemResidue(...).
        final EstimateMerchantReqDTO request1 = new EstimateMerchantReqDTO();
        request1.setStoreGuid("storeGuid");
        request1.setSortGuid("sortGuid");
        request1.setTypGuid(0);
        request1.setIsTheLimit(0);
        request1.setKeywords("keywords");
        when(mockEstimateMapper.getEstimateItemResidue(any(PageAdapter.class), eq(request1))).thenReturn(null);

        // Run the test
        final Page<EstimateItemResidueMemchantRespDTO> result = iEstimateServiceImplUnderTest.getEstimateItemResidue(
                request);

        // Verify the results
    }

    @Test
    public void testVerifyDineInItemEstimate() {
        // Setup
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineInItemDTO.setItemName("itemName");
        dineInItemDTO.setSkuGuid("skuGuid");
        dineInItemDTO.setSkuName("skuName");
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setFreeCount(new BigDecimal("0.00"));
        final PackageSubgroupDTO packageSubgroupDTO = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO = new SubDineInItemDTO();
        subDineInItemDTO.setSkuGuid("skuGuid");
        subDineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO.setPackageDefaultCount(new BigDecimal("0.00"));
        packageSubgroupDTO.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO));
        dineInItemDTO.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO));
        final List<DineInItemDTO> request = Arrays.asList(dineInItemDTO);
        final EstimateResultRespDTO expectedResult = new EstimateResultRespDTO(false, "errorMsg",
                Arrays.asList("value"));
        when(mockTxManager.getTransaction(new DefaultTransactionDefinition(0))).thenReturn(null);

        // Run the test
        final EstimateResultRespDTO result = iEstimateServiceImplUnderTest.verifyDineInItemEstimate(request);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockSkuMapper).updateStockStatus("skuGuid", new BigDecimal("0.00"), 1);
        verify(mockTxManager).commit(any(TransactionStatus.class));
        verify(mockSkuService).sellOutRackItem(Arrays.asList("value"));
        verify(mockEstimateOpLogService).saveOpLog(Arrays.asList(EstimateOpLogDO.builder().build()), "enterpriseGuid");
        verify(mockRedisTemplate).delete("key");
    }

    @Test
    public void testVerifyDineInItemEstimate_PlatformTransactionManagerGetTransactionThrowsTransactionException() {
        // Setup
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineInItemDTO.setItemName("itemName");
        dineInItemDTO.setSkuGuid("skuGuid");
        dineInItemDTO.setSkuName("skuName");
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setFreeCount(new BigDecimal("0.00"));
        final PackageSubgroupDTO packageSubgroupDTO = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO = new SubDineInItemDTO();
        subDineInItemDTO.setSkuGuid("skuGuid");
        subDineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO.setPackageDefaultCount(new BigDecimal("0.00"));
        packageSubgroupDTO.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO));
        dineInItemDTO.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO));
        final List<DineInItemDTO> request = Arrays.asList(dineInItemDTO);
        when(mockTxManager.getTransaction(new DefaultTransactionDefinition(0))).thenThrow(TransactionException.class);

        // Run the test
        assertThatThrownBy(() -> iEstimateServiceImplUnderTest.verifyDineInItemEstimate(request))
                .isInstanceOf(TransactionException.class);
    }

    @Test
    public void testVerifyDineInItemEstimate_PlatformTransactionManagerCommitThrowsTransactionException() {
        // Setup
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineInItemDTO.setItemName("itemName");
        dineInItemDTO.setSkuGuid("skuGuid");
        dineInItemDTO.setSkuName("skuName");
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setFreeCount(new BigDecimal("0.00"));
        final PackageSubgroupDTO packageSubgroupDTO = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO = new SubDineInItemDTO();
        subDineInItemDTO.setSkuGuid("skuGuid");
        subDineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO.setPackageDefaultCount(new BigDecimal("0.00"));
        packageSubgroupDTO.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO));
        dineInItemDTO.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO));
        final List<DineInItemDTO> request = Arrays.asList(dineInItemDTO);
        final EstimateResultRespDTO expectedResult = new EstimateResultRespDTO(false, "errorMsg",
                Arrays.asList("value"));
        when(mockTxManager.getTransaction(new DefaultTransactionDefinition(0))).thenReturn(null);
        doThrow(TransactionException.class).when(mockTxManager).commit(any(TransactionStatus.class));

        // Run the test
        final EstimateResultRespDTO result = iEstimateServiceImplUnderTest.verifyDineInItemEstimate(request);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockSkuMapper).updateStockStatus("skuGuid", new BigDecimal("0.00"), 1);
        verify(mockTxManager).rollback(any(TransactionStatus.class));
    }

    @Test
    public void testVerifyDineInItemEstimate_PlatformTransactionManagerRollbackThrowsTransactionException() {
        // Setup
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineInItemDTO.setItemName("itemName");
        dineInItemDTO.setSkuGuid("skuGuid");
        dineInItemDTO.setSkuName("skuName");
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setFreeCount(new BigDecimal("0.00"));
        final PackageSubgroupDTO packageSubgroupDTO = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO = new SubDineInItemDTO();
        subDineInItemDTO.setSkuGuid("skuGuid");
        subDineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO.setPackageDefaultCount(new BigDecimal("0.00"));
        packageSubgroupDTO.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO));
        dineInItemDTO.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO));
        final List<DineInItemDTO> request = Arrays.asList(dineInItemDTO);
        final EstimateResultRespDTO expectedResult = new EstimateResultRespDTO(false, "errorMsg",
                Arrays.asList("value"));
        when(mockTxManager.getTransaction(new DefaultTransactionDefinition(0))).thenReturn(null);
        doThrow(TransactionException.class).when(mockTxManager).rollback(any(TransactionStatus.class));

        // Run the test
        final EstimateResultRespDTO result = iEstimateServiceImplUnderTest.verifyDineInItemEstimate(request);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockSkuMapper).updateStockStatus("skuGuid", new BigDecimal("0.00"), 1);
    }

    @Test
    public void testDescStock() {
        // Setup
        final List<ItemSkuStockDTO> request = Arrays.asList(
                new ItemSkuStockDTO("skuGuid", new BigDecimal("0.00"), "productName",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0)));
        final EstimateResultRespDTO expectedResult = new EstimateResultRespDTO(false, "errorMsg",
                Arrays.asList("value"));
        when(mockTxManager.getTransaction(new DefaultTransactionDefinition(0))).thenReturn(null);

        // Run the test
        final EstimateResultRespDTO result = iEstimateServiceImplUnderTest.descStock(request);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockSkuMapper).updateStockStatus("skuGuid", new BigDecimal("0.00"), 1);
        verify(mockTxManager).commit(any(TransactionStatus.class));
        verify(mockSkuService).sellOutRackItem(Arrays.asList("value"));
        verify(mockEstimateOpLogService).saveOpLog(Arrays.asList(EstimateOpLogDO.builder().build()), "enterpriseGuid");
        verify(mockRedisTemplate).delete("key");
    }

    @Test
    public void testDescStock_PlatformTransactionManagerGetTransactionThrowsTransactionException() {
        // Setup
        final List<ItemSkuStockDTO> request = Arrays.asList(
                new ItemSkuStockDTO("skuGuid", new BigDecimal("0.00"), "productName",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0)));
        when(mockTxManager.getTransaction(new DefaultTransactionDefinition(0))).thenThrow(TransactionException.class);

        // Run the test
        assertThatThrownBy(() -> iEstimateServiceImplUnderTest.descStock(request))
                .isInstanceOf(TransactionException.class);
    }

    @Test
    public void testDescStock_PlatformTransactionManagerCommitThrowsTransactionException() {
        // Setup
        final List<ItemSkuStockDTO> request = Arrays.asList(
                new ItemSkuStockDTO("skuGuid", new BigDecimal("0.00"), "productName",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0)));
        final EstimateResultRespDTO expectedResult = new EstimateResultRespDTO(false, "errorMsg",
                Arrays.asList("value"));
        when(mockTxManager.getTransaction(new DefaultTransactionDefinition(0))).thenReturn(null);
        doThrow(TransactionException.class).when(mockTxManager).commit(any(TransactionStatus.class));

        // Run the test
        final EstimateResultRespDTO result = iEstimateServiceImplUnderTest.descStock(request);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockSkuMapper).updateStockStatus("skuGuid", new BigDecimal("0.00"), 1);
        verify(mockTxManager).rollback(any(TransactionStatus.class));
    }

    @Test
    public void testDescStock_PlatformTransactionManagerRollbackThrowsTransactionException() {
        // Setup
        final List<ItemSkuStockDTO> request = Arrays.asList(
                new ItemSkuStockDTO("skuGuid", new BigDecimal("0.00"), "productName",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0)));
        final EstimateResultRespDTO expectedResult = new EstimateResultRespDTO(false, "errorMsg",
                Arrays.asList("value"));
        when(mockTxManager.getTransaction(new DefaultTransactionDefinition(0))).thenReturn(null);
        doThrow(TransactionException.class).when(mockTxManager).rollback(any(TransactionStatus.class));

        // Run the test
        final EstimateResultRespDTO result = iEstimateServiceImplUnderTest.descStock(request);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockSkuMapper).updateStockStatus("skuGuid", new BigDecimal("0.00"), 1);
    }

    @Test
    public void testIncStock() {
        // Setup
        final List<ItemSkuStockDTO> request = Arrays.asList(
                new ItemSkuStockDTO("skuGuid", new BigDecimal("0.00"), "productName",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0)));

        // Run the test
        final Boolean result = iEstimateServiceImplUnderTest.incStock(request);

        // Verify the results
        assertThat(result).isFalse();
        verify(mockSkuMapper).updateStockStatus("skuGuid", new BigDecimal("0.00"), 1);
        verify(mockEstimateOpLogService).saveOpLog(Arrays.asList(EstimateOpLogDO.builder().build()), "enterpriseGuid");
        verify(mockRedisTemplate).delete("key");
    }

    @Test
    public void testDineinFail() {
        // Setup
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineInItemDTO.setItemName("itemName");
        dineInItemDTO.setSkuGuid("skuGuid");
        dineInItemDTO.setSkuName("skuName");
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setFreeCount(new BigDecimal("0.00"));
        final PackageSubgroupDTO packageSubgroupDTO = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO = new SubDineInItemDTO();
        subDineInItemDTO.setSkuGuid("skuGuid");
        subDineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO.setPackageDefaultCount(new BigDecimal("0.00"));
        packageSubgroupDTO.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO));
        dineInItemDTO.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO));
        final List<DineInItemDTO> request = Arrays.asList(dineInItemDTO);

        // Run the test
        final Boolean result = iEstimateServiceImplUnderTest.dineinFail(request);

        // Verify the results
        assertThat(result).isFalse();
        verify(mockSkuMapper).updateStockStatus("skuGuid", new BigDecimal("0.00"), 1);
        verify(mockEstimateOpLogService).saveOpLog(Arrays.asList(EstimateOpLogDO.builder().build()), "enterpriseGuid");
        verify(mockRedisTemplate).delete("key");
    }

    @Test
    public void testQueryEstimateForSyn() {
        // Setup
        final BaseDTO baseDTO = new BaseDTO();
        baseDTO.setDeviceType(0);
        baseDTO.setDeviceId("deviceId");
        baseDTO.setEnterpriseGuid("enterpriseGuid");
        baseDTO.setEnterpriseName("enterpriseName");
        baseDTO.setStoreGuid("storeGuid");

        final List<ItemEstimateForAndroidRespDTO> expectedResult = Arrays.asList(
                new ItemEstimateForAndroidRespDTO("skuGuid", 0, new BigDecimal("0.00"), new BigDecimal("0.00")));

        // Configure OrganizationService.queryBrandByStoreGuid(...).
        final BrandDTO brandDTO = new BrandDTO("356f5fa6-70a9-48e4-ae77-1cd297a330fe",
                "2225690a-ab50-4c86-9955-11e47a4e7283", "name", "description", "logoUrl", false, false,
                "createUserGuid", "modifiedUserGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "mchntTypeCode", Arrays.asList(
                new StoreDTO("b524d163-12e5-4adf-a189-5a405ffa1cab", "code", "name", "belongBrandGuid",
                        "belongBrandName", "parentIds", LocalTime.of(0, 0, 0), LocalTime.of(0, 0, 0), "contactName",
                        "contactTel", "provinceCode", "provinceName", "cityCode", "cityName", "countyCode",
                        "countyName", "addressDetail", "longitude", "latitude", false, false, "photos",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "createUserGuid",
                        "modifiedUserGuid", Arrays.asList(
                        new StoreProductDTO("productGuid", "productName", 0, LocalDate.of(2020, 1, 1),
                                LocalDate.of(2020, 1, 1), LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, 0)),
                        Arrays.asList(), "mchntTypeCode", "logoUrl", "openingHours", 0, 0, 0, 0, "storeNotification",
                        "storeDoorPhoto", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0,
                        0, 0, LocalDate.of(2020, 1, 1))), 0, 0, 0, 0, false);
        when(mockOrganizationService.queryBrandByStoreGuid("storeGuid")).thenReturn(brandDTO);

        // Configure IPricePlanStoreService.findPlanNowStoreGuid(...).
        final PricePlanNowDTO pricePlanNowDTO = new PricePlanNowDTO();
        pricePlanNowDTO.setBrandGuid("brandGuid");
        pricePlanNowDTO.setPlanGuid("planGuid");
        pricePlanNowDTO.setStoreGuid("storeGuid");
        pricePlanNowDTO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        pricePlanNowDTO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<PricePlanNowDTO> pricePlanNowDTOS = Arrays.asList(pricePlanNowDTO);
        when(mockPricePlanStoreService.findPlanNowStoreGuid(LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                "storeGuid")).thenReturn(pricePlanNowDTOS);

        // Configure ItemHelper.getEffectStorePlanGuid(...).
        final PricePlanNowDTO pricePlanNowDTO1 = new PricePlanNowDTO();
        pricePlanNowDTO1.setBrandGuid("brandGuid");
        pricePlanNowDTO1.setPlanGuid("planGuid");
        pricePlanNowDTO1.setStoreGuid("storeGuid");
        pricePlanNowDTO1.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        pricePlanNowDTO1.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<PricePlanNowDTO> planNowList = Arrays.asList(pricePlanNowDTO1);
        when(mockItemHelper.getEffectStorePlanGuid(planNowList)).thenReturn("result");

        // Configure EstimateRepository.listNormalEstimateItem(...).
        final List<ItemEstimateForAndroidRespDTO> itemEstimateForAndroidRespDTOS = Arrays.asList(
                new ItemEstimateForAndroidRespDTO("skuGuid", 0, new BigDecimal("0.00"), new BigDecimal("0.00")));
        final BaseDTO baseDTO1 = new BaseDTO();
        baseDTO1.setDeviceType(0);
        baseDTO1.setDeviceId("deviceId");
        baseDTO1.setEnterpriseGuid("enterpriseGuid");
        baseDTO1.setEnterpriseName("enterpriseName");
        baseDTO1.setStoreGuid("storeGuid");
        when(mockEstimateRepository.listNormalEstimateItem(baseDTO1)).thenReturn(itemEstimateForAndroidRespDTOS);

        // Configure EstimateRepository.listPlanEstimateItem(...).
        final List<ItemEstimateForAndroidRespDTO> itemEstimateForAndroidRespDTOS1 = Arrays.asList(
                new ItemEstimateForAndroidRespDTO("skuGuid", 0, new BigDecimal("0.00"), new BigDecimal("0.00")));
        when(mockEstimateRepository.listPlanEstimateItem("storeGuid", "planGuid"))
                .thenReturn(itemEstimateForAndroidRespDTOS1);

        // Run the test
        final List<ItemEstimateForAndroidRespDTO> result = iEstimateServiceImplUnderTest.queryEstimateForSyn(baseDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryEstimateForSyn_IPricePlanStoreServiceReturnsNoItems() {
        // Setup
        final BaseDTO baseDTO = new BaseDTO();
        baseDTO.setDeviceType(0);
        baseDTO.setDeviceId("deviceId");
        baseDTO.setEnterpriseGuid("enterpriseGuid");
        baseDTO.setEnterpriseName("enterpriseName");
        baseDTO.setStoreGuid("storeGuid");

        final List<ItemEstimateForAndroidRespDTO> expectedResult = Arrays.asList(
                new ItemEstimateForAndroidRespDTO("skuGuid", 0, new BigDecimal("0.00"), new BigDecimal("0.00")));

        // Configure OrganizationService.queryBrandByStoreGuid(...).
        final BrandDTO brandDTO = new BrandDTO("356f5fa6-70a9-48e4-ae77-1cd297a330fe",
                "2225690a-ab50-4c86-9955-11e47a4e7283", "name", "description", "logoUrl", false, false,
                "createUserGuid", "modifiedUserGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "mchntTypeCode", Arrays.asList(
                new StoreDTO("b524d163-12e5-4adf-a189-5a405ffa1cab", "code", "name", "belongBrandGuid",
                        "belongBrandName", "parentIds", LocalTime.of(0, 0, 0), LocalTime.of(0, 0, 0), "contactName",
                        "contactTel", "provinceCode", "provinceName", "cityCode", "cityName", "countyCode",
                        "countyName", "addressDetail", "longitude", "latitude", false, false, "photos",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "createUserGuid",
                        "modifiedUserGuid", Arrays.asList(
                        new StoreProductDTO("productGuid", "productName", 0, LocalDate.of(2020, 1, 1),
                                LocalDate.of(2020, 1, 1), LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, 0)),
                        Arrays.asList(), "mchntTypeCode", "logoUrl", "openingHours", 0, 0, 0, 0, "storeNotification",
                        "storeDoorPhoto", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0,
                        0, 0, LocalDate.of(2020, 1, 1))), 0, 0, 0, 0, false);
        when(mockOrganizationService.queryBrandByStoreGuid("storeGuid")).thenReturn(brandDTO);

        when(mockPricePlanStoreService.findPlanNowStoreGuid(LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                "storeGuid")).thenReturn(Collections.emptyList());

        // Configure ItemHelper.getEffectStorePlanGuid(...).
        final PricePlanNowDTO pricePlanNowDTO = new PricePlanNowDTO();
        pricePlanNowDTO.setBrandGuid("brandGuid");
        pricePlanNowDTO.setPlanGuid("planGuid");
        pricePlanNowDTO.setStoreGuid("storeGuid");
        pricePlanNowDTO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        pricePlanNowDTO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<PricePlanNowDTO> planNowList = Arrays.asList(pricePlanNowDTO);
        when(mockItemHelper.getEffectStorePlanGuid(planNowList)).thenReturn("result");

        // Configure EstimateRepository.listNormalEstimateItem(...).
        final List<ItemEstimateForAndroidRespDTO> itemEstimateForAndroidRespDTOS = Arrays.asList(
                new ItemEstimateForAndroidRespDTO("skuGuid", 0, new BigDecimal("0.00"), new BigDecimal("0.00")));
        final BaseDTO baseDTO1 = new BaseDTO();
        baseDTO1.setDeviceType(0);
        baseDTO1.setDeviceId("deviceId");
        baseDTO1.setEnterpriseGuid("enterpriseGuid");
        baseDTO1.setEnterpriseName("enterpriseName");
        baseDTO1.setStoreGuid("storeGuid");
        when(mockEstimateRepository.listNormalEstimateItem(baseDTO1)).thenReturn(itemEstimateForAndroidRespDTOS);

        // Configure EstimateRepository.listPlanEstimateItem(...).
        final List<ItemEstimateForAndroidRespDTO> itemEstimateForAndroidRespDTOS1 = Arrays.asList(
                new ItemEstimateForAndroidRespDTO("skuGuid", 0, new BigDecimal("0.00"), new BigDecimal("0.00")));
        when(mockEstimateRepository.listPlanEstimateItem("storeGuid", "planGuid"))
                .thenReturn(itemEstimateForAndroidRespDTOS1);

        // Run the test
        final List<ItemEstimateForAndroidRespDTO> result = iEstimateServiceImplUnderTest.queryEstimateForSyn(baseDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryEstimateForSyn_EstimateRepositoryListNormalEstimateItemReturnsNoItems() {
        // Setup
        final BaseDTO baseDTO = new BaseDTO();
        baseDTO.setDeviceType(0);
        baseDTO.setDeviceId("deviceId");
        baseDTO.setEnterpriseGuid("enterpriseGuid");
        baseDTO.setEnterpriseName("enterpriseName");
        baseDTO.setStoreGuid("storeGuid");

        // Configure OrganizationService.queryBrandByStoreGuid(...).
        final BrandDTO brandDTO = new BrandDTO("356f5fa6-70a9-48e4-ae77-1cd297a330fe",
                "2225690a-ab50-4c86-9955-11e47a4e7283", "name", "description", "logoUrl", false, false,
                "createUserGuid", "modifiedUserGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "mchntTypeCode", Arrays.asList(
                new StoreDTO("b524d163-12e5-4adf-a189-5a405ffa1cab", "code", "name", "belongBrandGuid",
                        "belongBrandName", "parentIds", LocalTime.of(0, 0, 0), LocalTime.of(0, 0, 0), "contactName",
                        "contactTel", "provinceCode", "provinceName", "cityCode", "cityName", "countyCode",
                        "countyName", "addressDetail", "longitude", "latitude", false, false, "photos",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "createUserGuid",
                        "modifiedUserGuid", Arrays.asList(
                        new StoreProductDTO("productGuid", "productName", 0, LocalDate.of(2020, 1, 1),
                                LocalDate.of(2020, 1, 1), LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, 0)),
                        Arrays.asList(), "mchntTypeCode", "logoUrl", "openingHours", 0, 0, 0, 0, "storeNotification",
                        "storeDoorPhoto", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0,
                        0, 0, LocalDate.of(2020, 1, 1))), 0, 0, 0, 0, false);
        when(mockOrganizationService.queryBrandByStoreGuid("storeGuid")).thenReturn(brandDTO);

        // Configure IPricePlanStoreService.findPlanNowStoreGuid(...).
        final PricePlanNowDTO pricePlanNowDTO = new PricePlanNowDTO();
        pricePlanNowDTO.setBrandGuid("brandGuid");
        pricePlanNowDTO.setPlanGuid("planGuid");
        pricePlanNowDTO.setStoreGuid("storeGuid");
        pricePlanNowDTO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        pricePlanNowDTO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<PricePlanNowDTO> pricePlanNowDTOS = Arrays.asList(pricePlanNowDTO);
        when(mockPricePlanStoreService.findPlanNowStoreGuid(LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                "storeGuid")).thenReturn(pricePlanNowDTOS);

        // Configure EstimateRepository.listNormalEstimateItem(...).
        final BaseDTO baseDTO1 = new BaseDTO();
        baseDTO1.setDeviceType(0);
        baseDTO1.setDeviceId("deviceId");
        baseDTO1.setEnterpriseGuid("enterpriseGuid");
        baseDTO1.setEnterpriseName("enterpriseName");
        baseDTO1.setStoreGuid("storeGuid");
        when(mockEstimateRepository.listNormalEstimateItem(baseDTO1)).thenReturn(Collections.emptyList());

        // Run the test
        final List<ItemEstimateForAndroidRespDTO> result = iEstimateServiceImplUnderTest.queryEstimateForSyn(baseDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testQueryEstimateForSyn_EstimateRepositoryListPlanEstimateItemReturnsNoItems() {
        // Setup
        final BaseDTO baseDTO = new BaseDTO();
        baseDTO.setDeviceType(0);
        baseDTO.setDeviceId("deviceId");
        baseDTO.setEnterpriseGuid("enterpriseGuid");
        baseDTO.setEnterpriseName("enterpriseName");
        baseDTO.setStoreGuid("storeGuid");

        // Configure OrganizationService.queryBrandByStoreGuid(...).
        final BrandDTO brandDTO = new BrandDTO("356f5fa6-70a9-48e4-ae77-1cd297a330fe",
                "2225690a-ab50-4c86-9955-11e47a4e7283", "name", "description", "logoUrl", false, false,
                "createUserGuid", "modifiedUserGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "mchntTypeCode", Arrays.asList(
                new StoreDTO("b524d163-12e5-4adf-a189-5a405ffa1cab", "code", "name", "belongBrandGuid",
                        "belongBrandName", "parentIds", LocalTime.of(0, 0, 0), LocalTime.of(0, 0, 0), "contactName",
                        "contactTel", "provinceCode", "provinceName", "cityCode", "cityName", "countyCode",
                        "countyName", "addressDetail", "longitude", "latitude", false, false, "photos",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "createUserGuid",
                        "modifiedUserGuid", Arrays.asList(
                        new StoreProductDTO("productGuid", "productName", 0, LocalDate.of(2020, 1, 1),
                                LocalDate.of(2020, 1, 1), LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, 0)),
                        Arrays.asList(), "mchntTypeCode", "logoUrl", "openingHours", 0, 0, 0, 0, "storeNotification",
                        "storeDoorPhoto", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0,
                        0, 0, LocalDate.of(2020, 1, 1))), 0, 0, 0, 0, false);
        when(mockOrganizationService.queryBrandByStoreGuid("storeGuid")).thenReturn(brandDTO);

        // Configure IPricePlanStoreService.findPlanNowStoreGuid(...).
        final PricePlanNowDTO pricePlanNowDTO = new PricePlanNowDTO();
        pricePlanNowDTO.setBrandGuid("brandGuid");
        pricePlanNowDTO.setPlanGuid("planGuid");
        pricePlanNowDTO.setStoreGuid("storeGuid");
        pricePlanNowDTO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        pricePlanNowDTO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<PricePlanNowDTO> pricePlanNowDTOS = Arrays.asList(pricePlanNowDTO);
        when(mockPricePlanStoreService.findPlanNowStoreGuid(LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                "storeGuid")).thenReturn(pricePlanNowDTOS);

        // Configure ItemHelper.getEffectStorePlanGuid(...).
        final PricePlanNowDTO pricePlanNowDTO1 = new PricePlanNowDTO();
        pricePlanNowDTO1.setBrandGuid("brandGuid");
        pricePlanNowDTO1.setPlanGuid("planGuid");
        pricePlanNowDTO1.setStoreGuid("storeGuid");
        pricePlanNowDTO1.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        pricePlanNowDTO1.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<PricePlanNowDTO> planNowList = Arrays.asList(pricePlanNowDTO1);
        when(mockItemHelper.getEffectStorePlanGuid(planNowList)).thenReturn("result");

        when(mockEstimateRepository.listPlanEstimateItem("storeGuid", "planGuid")).thenReturn(Collections.emptyList());

        // Run the test
        final List<ItemEstimateForAndroidRespDTO> result = iEstimateServiceImplUnderTest.queryEstimateForSyn(baseDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testIsPlan() {
        assertThat(IEstimateServiceImpl.isPlan(0)).isFalse();
    }

    @Test
    public void testSaveSoldOutStatus() {
        // Setup
        final EstimateForManualReqDTO request = new EstimateForManualReqDTO();
        request.setItemGuid("data");
        request.setIsSoldOut(0);
        request.setStoreGuid("storeGuid");
        request.setSkuGuidList(Arrays.asList("value"));
        request.setItemType(0);

        // Configure IItemService.getItemInfo(...).
        final ItemInfoRespDTO itemInfoRespDTO = new ItemInfoRespDTO();
        itemInfoRespDTO.setItemGuid("itemGuid");
        itemInfoRespDTO.setBrandGuid("brandGuid");
        itemInfoRespDTO.setStoreGuid("storeGuid");
        itemInfoRespDTO.setItemType(0);
        itemInfoRespDTO.setName("itemName");
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("data");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        itemSingleDTO.setRuleType(0);
        when(mockItemService.getItemInfo(itemSingleDTO)).thenReturn(itemInfoRespDTO);

        when(mockItemHelper.setmealVerifyEstimate("data", "storeGuid")).thenReturn(true);
        when(mockSkuService.listSkuGuidByItemGuid(Arrays.asList("value"))).thenReturn(Arrays.asList("value"));

        // Run the test
        final boolean result = iEstimateServiceImplUnderTest.saveSoldOutStatus(request);

        // Verify the results
        assertThat(result).isFalse();
        verify(mockEstimateMapper).batchUpdateEstimateBySkuGuid(Arrays.asList("value"), 1, "storeGuid");
        verify(mockEventPushHelper).soldOutPushMsgToAndriod("storeGuid");
        verify(mockRedisTemplate).delete("key");
    }

    @Test
    public void testSaveSoldOutStatus_ItemHelperReturnsFalse() {
        // Setup
        final EstimateForManualReqDTO request = new EstimateForManualReqDTO();
        request.setItemGuid("data");
        request.setIsSoldOut(0);
        request.setStoreGuid("storeGuid");
        request.setSkuGuidList(Arrays.asList("value"));
        request.setItemType(0);

        // Configure IItemService.getItemInfo(...).
        final ItemInfoRespDTO itemInfoRespDTO = new ItemInfoRespDTO();
        itemInfoRespDTO.setItemGuid("itemGuid");
        itemInfoRespDTO.setBrandGuid("brandGuid");
        itemInfoRespDTO.setStoreGuid("storeGuid");
        itemInfoRespDTO.setItemType(0);
        itemInfoRespDTO.setName("itemName");
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("data");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        itemSingleDTO.setRuleType(0);
        when(mockItemService.getItemInfo(itemSingleDTO)).thenReturn(itemInfoRespDTO);

        when(mockItemHelper.setmealVerifyEstimate("data", "storeGuid")).thenReturn(false);

        // Run the test
        assertThatThrownBy(() -> iEstimateServiceImplUnderTest.saveSoldOutStatus(request))
                .isInstanceOf(BusinessException.class);
    }

    @Test
    public void testSaveSoldOutStatus_ISkuServiceReturnsNoItems() {
        // Setup
        final EstimateForManualReqDTO request = new EstimateForManualReqDTO();
        request.setItemGuid("data");
        request.setIsSoldOut(0);
        request.setStoreGuid("storeGuid");
        request.setSkuGuidList(Arrays.asList("value"));
        request.setItemType(0);

        // Configure IItemService.getItemInfo(...).
        final ItemInfoRespDTO itemInfoRespDTO = new ItemInfoRespDTO();
        itemInfoRespDTO.setItemGuid("itemGuid");
        itemInfoRespDTO.setBrandGuid("brandGuid");
        itemInfoRespDTO.setStoreGuid("storeGuid");
        itemInfoRespDTO.setItemType(0);
        itemInfoRespDTO.setName("itemName");
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("data");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        itemSingleDTO.setRuleType(0);
        when(mockItemService.getItemInfo(itemSingleDTO)).thenReturn(itemInfoRespDTO);

        when(mockItemHelper.setmealVerifyEstimate("data", "storeGuid")).thenReturn(true);
        when(mockSkuService.listSkuGuidByItemGuid(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        final boolean result = iEstimateServiceImplUnderTest.saveSoldOutStatus(request);

        // Verify the results
        assertThat(result).isFalse();
        verify(mockEstimateMapper).batchUpdateEstimateBySkuGuid(Arrays.asList("value"), 1, "storeGuid");
        verify(mockEventPushHelper).soldOutPushMsgToAndriod("storeGuid");
        verify(mockRedisTemplate).delete("key");
    }

    @Test
    public void testStoreItemEstimateReset() {
        // Setup
        final Map<String, List<String>> request = new HashMap<>();
        when(mockEstimateMapper.storeItemEstimateReset(Arrays.asList("value"))).thenReturn(0);

        // Configure EstimateMapper.queryEstimateByStoreGuidList(...).
        final List<EstimateDO> estimateDOS = Arrays.asList(EstimateDO.builder()
                .id(0L)
                .guid("fadf78fc-29ae-41af-b609-1dbfb215ac7a")
                .itemGuid("itemGuid")
                .skuGuid("skuGuid")
                .isSoldOut(0)
                .isTheLimit(0)
                .uniqueResetFlag(0)
                .limitQuantity(new BigDecimal("0.00"))
                .residueQuantity(new BigDecimal("0.00"))
                .storeGuid("storeGuid")
                .gmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .isDelete(0)
                .version(0)
                .isForeverEstimate(0)
                .build());
        when(mockEstimateMapper.queryEstimateByStoreGuidList(Arrays.asList("value"))).thenReturn(estimateDOS);

        // Run the test
        final Integer result = iEstimateServiceImplUnderTest.storeItemEstimateReset(request);

        // Verify the results
        assertThat(result).isEqualTo(0);
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
        verify(mockEstimateMapper).storeItemEstimateLimitReset(Arrays.asList("value"));
        verify(mockEstimateMapper).storeItemEstimateUniqueReset(Arrays.asList("value"));
        verify(mockSkuMapper).updateStockStatus("skuGuid", new BigDecimal("0.00"), 0);
        verify(mockRedisTemplate).delete(Arrays.asList("value"));
    }

    @Test
    public void testStoreItemEstimateReset_EstimateMapperQueryEstimateByStoreGuidListReturnsNoItems() {
        // Setup
        final Map<String, List<String>> request = new HashMap<>();
        when(mockEstimateMapper.storeItemEstimateReset(Arrays.asList("value"))).thenReturn(0);
        when(mockEstimateMapper.queryEstimateByStoreGuidList(Arrays.asList("value")))
                .thenReturn(Collections.emptyList());

        // Run the test
        final Integer result = iEstimateServiceImplUnderTest.storeItemEstimateReset(request);

        // Verify the results
        assertThat(result).isEqualTo(0);
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
        verify(mockEstimateMapper).storeItemEstimateLimitReset(Arrays.asList("value"));
        verify(mockEstimateMapper).storeItemEstimateUniqueReset(Arrays.asList("value"));
        verify(mockRedisTemplate).delete(Arrays.asList("value"));
    }

    @Test
    public void testHandle() {
        // Setup
        when(mockEstimateMapper.storeItemEstimateReset(Arrays.asList("value"))).thenReturn(0);

        // Configure EstimateMapper.queryEstimateByStoreGuidList(...).
        final List<EstimateDO> estimateDOS = Arrays.asList(EstimateDO.builder()
                .id(0L)
                .guid("fadf78fc-29ae-41af-b609-1dbfb215ac7a")
                .itemGuid("itemGuid")
                .skuGuid("skuGuid")
                .isSoldOut(0)
                .isTheLimit(0)
                .uniqueResetFlag(0)
                .limitQuantity(new BigDecimal("0.00"))
                .residueQuantity(new BigDecimal("0.00"))
                .storeGuid("storeGuid")
                .gmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .isDelete(0)
                .version(0)
                .isForeverEstimate(0)
                .build());
        when(mockEstimateMapper.queryEstimateByStoreGuidList(Arrays.asList("value"))).thenReturn(estimateDOS);

        // Run the test
        final CompletableFuture<Void> result = iEstimateServiceImplUnderTest.handle("enterpriseGuid",
                Arrays.asList("value"));

        // Verify the results
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
        verify(mockEstimateMapper).storeItemEstimateLimitReset(Arrays.asList("value"));
        verify(mockEstimateMapper).storeItemEstimateUniqueReset(Arrays.asList("value"));
        verify(mockSkuMapper).updateStockStatus("skuGuid", new BigDecimal("0.00"), 0);
        verify(mockRedisTemplate).delete(Arrays.asList("value"));
    }

    @Test
    public void testHandle_EstimateMapperQueryEstimateByStoreGuidListReturnsNoItems() {
        // Setup
        when(mockEstimateMapper.storeItemEstimateReset(Arrays.asList("value"))).thenReturn(0);
        when(mockEstimateMapper.queryEstimateByStoreGuidList(Arrays.asList("value")))
                .thenReturn(Collections.emptyList());

        // Run the test
        final CompletableFuture<Void> result = iEstimateServiceImplUnderTest.handle("enterpriseGuid",
                Arrays.asList("value"));

        // Verify the results
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
        verify(mockEstimateMapper).storeItemEstimateLimitReset(Arrays.asList("value"));
        verify(mockEstimateMapper).storeItemEstimateUniqueReset(Arrays.asList("value"));
        verify(mockRedisTemplate).delete(Arrays.asList("value"));
    }

    @Test
    public void testGetSetMealSubitemEstimate() {
        // Setup
        final SetMealEstimateQuery setMealEstimateQuery = new SetMealEstimateQuery();
        setMealEstimateQuery.setItemGuid("itemGuid");
        setMealEstimateQuery.setPickNum(0);
        setMealEstimateQuery.setSkuGuid("skuGuid");
        setMealEstimateQuery.setIsSoldOut(0);
        setMealEstimateQuery.setIsTheLimit(0);
        final List<SetMealEstimateQuery> expectedResult = Arrays.asList(setMealEstimateQuery);

        // Configure EstimateMapper.getSetMealSubitemEstimate(...).
        final SetMealEstimateQuery setMealEstimateQuery1 = new SetMealEstimateQuery();
        setMealEstimateQuery1.setItemGuid("itemGuid");
        setMealEstimateQuery1.setPickNum(0);
        setMealEstimateQuery1.setSkuGuid("skuGuid");
        setMealEstimateQuery1.setIsSoldOut(0);
        setMealEstimateQuery1.setIsTheLimit(0);
        final List<SetMealEstimateQuery> setMealEstimateQueries = Arrays.asList(setMealEstimateQuery1);
        when(mockEstimateMapper.getSetMealSubitemEstimate("itemGuid", "storeGuid")).thenReturn(setMealEstimateQueries);

        // Run the test
        final List<SetMealEstimateQuery> result = iEstimateServiceImplUnderTest.getSetMealSubitemEstimate("itemGuid",
                "storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetSetMealSubitemEstimate_EstimateMapperReturnsNoItems() {
        // Setup
        when(mockEstimateMapper.getSetMealSubitemEstimate("itemGuid", "storeGuid")).thenReturn(Collections.emptyList());

        // Run the test
        final List<SetMealEstimateQuery> result = iEstimateServiceImplUnderTest.getSetMealSubitemEstimate("itemGuid",
                "storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testQueryEstimateByGuids() {
        // Setup
        final ItemEstimateForAndroidDTO itemEstimateForAndroidDTO = new ItemEstimateForAndroidDTO();
        itemEstimateForAndroidDTO.setSkuGuid("skuGuid");
        itemEstimateForAndroidDTO.setIsSoldOut(0);
        itemEstimateForAndroidDTO.setIsTheLimit(0);
        itemEstimateForAndroidDTO.setResidueQuantity(new BigDecimal("0.00"));
        itemEstimateForAndroidDTO.setReminderThreshold(new BigDecimal("0.00"));
        final List<ItemEstimateForAndroidDTO> expectedResult = Arrays.asList(itemEstimateForAndroidDTO);

        // Configure EstimateMapper.selectList(...).
        final List<EstimateDO> estimateDOS = Arrays.asList(EstimateDO.builder()
                .id(0L)
                .guid("fadf78fc-29ae-41af-b609-1dbfb215ac7a")
                .itemGuid("itemGuid")
                .skuGuid("skuGuid")
                .isSoldOut(0)
                .isTheLimit(0)
                .uniqueResetFlag(0)
                .limitQuantity(new BigDecimal("0.00"))
                .residueQuantity(new BigDecimal("0.00"))
                .storeGuid("storeGuid")
                .gmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .isDelete(0)
                .version(0)
                .isForeverEstimate(0)
                .build());
        when(mockEstimateMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(estimateDOS);

        // Run the test
        final List<ItemEstimateForAndroidDTO> result = iEstimateServiceImplUnderTest.queryEstimateByGuids("storeGuid",
                Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryEstimateByGuids_EstimateMapperReturnsNoItems() {
        // Setup
        when(mockEstimateMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<ItemEstimateForAndroidDTO> result = iEstimateServiceImplUnderTest.queryEstimateByGuids("storeGuid",
                Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testSaveSoldOut() {
        // Setup
        final EstimateBO biz = new EstimateBO();
        biz.setItemGuid("itemGuid");
        biz.setStoreGuid("storeGuid");
        biz.setSkuList(Arrays.asList(EstimateDO.builder()
                .id(0L)
                .guid("fadf78fc-29ae-41af-b609-1dbfb215ac7a")
                .itemGuid("itemGuid")
                .skuGuid("skuGuid")
                .isSoldOut(0)
                .isTheLimit(0)
                .uniqueResetFlag(0)
                .limitQuantity(new BigDecimal("0.00"))
                .residueQuantity(new BigDecimal("0.00"))
                .storeGuid("storeGuid")
                .gmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .isDelete(0)
                .version(0)
                .isForeverEstimate(0)
                .build()));
        biz.setItemGuidList(Arrays.asList("value"));
        biz.setCreateSkuList(Arrays.asList(EstimateDO.builder()
                .id(0L)
                .guid("fadf78fc-29ae-41af-b609-1dbfb215ac7a")
                .itemGuid("itemGuid")
                .skuGuid("skuGuid")
                .isSoldOut(0)
                .isTheLimit(0)
                .uniqueResetFlag(0)
                .limitQuantity(new BigDecimal("0.00"))
                .residueQuantity(new BigDecimal("0.00"))
                .storeGuid("storeGuid")
                .gmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .isDelete(0)
                .version(0)
                .isForeverEstimate(0)
                .build()));
        biz.setUpdateSkuList(Arrays.asList(EstimateDO.builder()
                .id(0L)
                .guid("fadf78fc-29ae-41af-b609-1dbfb215ac7a")
                .itemGuid("itemGuid")
                .skuGuid("skuGuid")
                .isSoldOut(0)
                .isTheLimit(0)
                .uniqueResetFlag(0)
                .limitQuantity(new BigDecimal("0.00"))
                .residueQuantity(new BigDecimal("0.00"))
                .storeGuid("storeGuid")
                .gmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .isDelete(0)
                .version(0)
                .isForeverEstimate(0)
                .build()));
        biz.setRemoveSkuList(Arrays.asList(EstimateDO.builder()
                .id(0L)
                .guid("fadf78fc-29ae-41af-b609-1dbfb215ac7a")
                .itemGuid("itemGuid")
                .skuGuid("skuGuid")
                .isSoldOut(0)
                .isTheLimit(0)
                .uniqueResetFlag(0)
                .limitQuantity(new BigDecimal("0.00"))
                .residueQuantity(new BigDecimal("0.00"))
                .storeGuid("storeGuid")
                .gmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .isDelete(0)
                .version(0)
                .isForeverEstimate(0)
                .build()));
        biz.setBatchCancelSkuList(Arrays.asList(EstimateDO.builder()
                .id(0L)
                .guid("fadf78fc-29ae-41af-b609-1dbfb215ac7a")
                .itemGuid("itemGuid")
                .skuGuid("skuGuid")
                .isSoldOut(0)
                .isTheLimit(0)
                .uniqueResetFlag(0)
                .limitQuantity(new BigDecimal("0.00"))
                .residueQuantity(new BigDecimal("0.00"))
                .storeGuid("storeGuid")
                .gmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .isDelete(0)
                .version(0)
                .isForeverEstimate(0)
                .build()));
        biz.setBatchStopSkuList(Arrays.asList(EstimateDO.builder()
                .id(0L)
                .guid("fadf78fc-29ae-41af-b609-1dbfb215ac7a")
                .itemGuid("itemGuid")
                .skuGuid("skuGuid")
                .isSoldOut(0)
                .isTheLimit(0)
                .uniqueResetFlag(0)
                .limitQuantity(new BigDecimal("0.00"))
                .residueQuantity(new BigDecimal("0.00"))
                .storeGuid("storeGuid")
                .gmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .isDelete(0)
                .version(0)
                .isForeverEstimate(0)
                .build()));
        biz.setSchedulingSkuList(Arrays.asList(EstimateDO.builder()
                .id(0L)
                .guid("fadf78fc-29ae-41af-b609-1dbfb215ac7a")
                .itemGuid("itemGuid")
                .skuGuid("skuGuid")
                .isSoldOut(0)
                .isTheLimit(0)
                .uniqueResetFlag(0)
                .limitQuantity(new BigDecimal("0.00"))
                .residueQuantity(new BigDecimal("0.00"))
                .storeGuid("storeGuid")
                .gmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .isDelete(0)
                .version(0)
                .isForeverEstimate(0)
                .build()));
        biz.setReduceStockSkuList(Arrays.asList(EstimateDO.builder()
                .id(0L)
                .guid("fadf78fc-29ae-41af-b609-1dbfb215ac7a")
                .itemGuid("itemGuid")
                .skuGuid("skuGuid")
                .isSoldOut(0)
                .isTheLimit(0)
                .uniqueResetFlag(0)
                .limitQuantity(new BigDecimal("0.00"))
                .residueQuantity(new BigDecimal("0.00"))
                .storeGuid("storeGuid")
                .gmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .isDelete(0)
                .version(0)
                .isForeverEstimate(0)
                .build()));
        biz.setReturnStockSkuList(Arrays.asList(EstimateDO.builder()
                .id(0L)
                .guid("fadf78fc-29ae-41af-b609-1dbfb215ac7a")
                .itemGuid("itemGuid")
                .skuGuid("skuGuid")
                .isSoldOut(0)
                .isTheLimit(0)
                .uniqueResetFlag(0)
                .limitQuantity(new BigDecimal("0.00"))
                .residueQuantity(new BigDecimal("0.00"))
                .storeGuid("storeGuid")
                .gmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .isDelete(0)
                .version(0)
                .isForeverEstimate(0)
                .build()));

        // Configure OrganizationService.queryStoreByGuid(...).
        final StoreDTO storeDTO = new StoreDTO("b524d163-12e5-4adf-a189-5a405ffa1cab", "code", "name",
                "belongBrandGuid", "belongBrandName", "parentIds", LocalTime.of(0, 0, 0), LocalTime.of(0, 0, 0),
                "contactName", "contactTel", "provinceCode", "provinceName", "cityCode", "cityName", "countyCode",
                "countyName", "addressDetail", "longitude", "latitude", false, false, "photos",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "createUserGuid",
                "modifiedUserGuid", Arrays.asList(
                new StoreProductDTO("productGuid", "productName", 0, LocalDate.of(2020, 1, 1), LocalDate.of(2020, 1, 1),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, 0)), Arrays.asList(
                new BrandDTO("356f5fa6-70a9-48e4-ae77-1cd297a330fe", "2225690a-ab50-4c86-9955-11e47a4e7283", "name",
                        "description", "logoUrl", false, false, "createUserGuid", "modifiedUserGuid",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "mchntTypeCode",
                        Arrays.asList(), 0, 0, 0, 0, false)), "mchntTypeCode", "logoUrl", "openingHours", 0, 0, 0, 0,
                "storeNotification", "storeDoorPhoto", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, 0, 0, LocalDate.of(2020, 1, 1));
        when(mockOrganizationService.queryStoreByGuid("storeGuid")).thenReturn(storeDTO);

        // Configure ConfigFeginService.selectEstimateResetTime(...).
        final ConfigRespDTO configRespDTO = new ConfigRespDTO();
        configRespDTO.setGuid("8332cc82-c836-4ef1-82b0-1e29be41974c");
        configRespDTO.setEnterpriseGuid("enterpriseGuid");
        configRespDTO.setStoreGuid("storeGuid");
        configRespDTO.setDicCode(0);
        configRespDTO.setDicName("desc");
        configRespDTO.setDictValue("dictValue");
        final ConfigReqQueryDTO configReqQueryDTO = new ConfigReqQueryDTO();
        configReqQueryDTO.setEnterpriseGuid("enterpriseGuid");
        configReqQueryDTO.setStoreGuid("storeGuid");
        configReqQueryDTO.setDicCode(0);
        when(mockConfigFeginService.selectEstimateResetTime(configReqQueryDTO)).thenReturn(configRespDTO);

        // Run the test
        final Boolean result = iEstimateServiceImplUnderTest.saveSoldOut(biz);

        // Verify the results
        assertThat(result).isTrue();
        verify(mockEstimateMapper).batchDeleteEstimateBySkuGuid(Arrays.asList("value"), "storeGuid");
        verify(mockEventPushHelper).soldOutPushMsgToAndriod("storeGuid");
        verify(mockConfigFeginService).saveEstimateResetTime(
                new ConfigReqDTO("6a6349ef-a531-47af-a6d2-25dd0f30aac3", "enterpriseGuid", "storeGuid", 0, "dicName",
                        "dictValue", LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0));
        verify(mockEstimateOpLogService).saveOpLog(Arrays.asList(EstimateOpLogDO.builder().build()), "enterpriseGuid");
        verify(mockRedisTemplate).delete("key");
    }

    @Test
    public void testListEstimate() {
        // Setup
        final EstimateForAndroidRespDTO expectedResult = new EstimateForAndroidRespDTO();
        final EstimateItemRespDTO estimateItemRespDTO = new EstimateItemRespDTO();
        estimateItemRespDTO.setItemGuid("itemGuid");
        estimateItemRespDTO.setItemName("itemName");
        final EstimateSkuRespDTO estimateSkuRespDTO = new EstimateSkuRespDTO();
        estimateSkuRespDTO.setSkuGuid("skuGuid");
        estimateSkuRespDTO.setSkuName("name");
        estimateSkuRespDTO.setLimitQuantity(new BigDecimal("0.00"));
        estimateSkuRespDTO.setResidueQuantity(new BigDecimal("0.00"));
        estimateSkuRespDTO.setIsForeverEstimate(0);
        estimateSkuRespDTO.setItemGuid("itemGuid");
        estimateSkuRespDTO.setEstimateGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        estimateItemRespDTO.setSkuRespList(Arrays.asList(estimateSkuRespDTO));
        estimateItemRespDTO.setEstimateGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setItemRespList(Arrays.asList(estimateItemRespDTO));
        expectedResult.setResumeSaleTime("resumeSaleTime");

        // Configure OrganizationService.queryBusinessDay(...).
        final BusinessDateReqDTO businessDateReqDTO = new BusinessDateReqDTO();
        businessDateReqDTO.setQueryDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        businessDateReqDTO.setStoreGuidList(Arrays.asList("value"));
        businessDateReqDTO.setStoreGuid("storeGuid");
        when(mockOrganizationService.queryBusinessDay(businessDateReqDTO)).thenReturn(LocalDate.of(2020, 1, 1));

        // Configure OrganizationService.queryStoreByGuid(...).
        final StoreDTO storeDTO = new StoreDTO("b524d163-12e5-4adf-a189-5a405ffa1cab", "code", "name",
                "belongBrandGuid", "belongBrandName", "parentIds", LocalTime.of(0, 0, 0), LocalTime.of(0, 0, 0),
                "contactName", "contactTel", "provinceCode", "provinceName", "cityCode", "cityName", "countyCode",
                "countyName", "addressDetail", "longitude", "latitude", false, false, "photos",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "createUserGuid",
                "modifiedUserGuid", Arrays.asList(
                new StoreProductDTO("productGuid", "productName", 0, LocalDate.of(2020, 1, 1), LocalDate.of(2020, 1, 1),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, 0)), Arrays.asList(
                new BrandDTO("356f5fa6-70a9-48e4-ae77-1cd297a330fe", "2225690a-ab50-4c86-9955-11e47a4e7283", "name",
                        "description", "logoUrl", false, false, "createUserGuid", "modifiedUserGuid",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "mchntTypeCode",
                        Arrays.asList(), 0, 0, 0, 0, false)), "mchntTypeCode", "logoUrl", "openingHours", 0, 0, 0, 0,
                "storeNotification", "storeDoorPhoto", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, 0, 0, LocalDate.of(2020, 1, 1));
        when(mockOrganizationService.queryStoreByGuid("storeGuid")).thenReturn(storeDTO);

        when(mockSubgroupMapper.listParentItemGuidBySubGroupSku(Arrays.asList("value")))
                .thenReturn(Arrays.asList("value"));

        // Configure ISkuService.listSkuInfo(...).
        final SkuInfoRespDTO skuInfoRespDTO = new SkuInfoRespDTO();
        skuInfoRespDTO.setId(0L);
        skuInfoRespDTO.setTypeGuid("typeGuid");
        skuInfoRespDTO.setItemGuid("itemGuid");
        skuInfoRespDTO.setSkuGuid("skuGuid");
        skuInfoRespDTO.setName("name");
        final List<SkuInfoRespDTO> skuInfoRespDTOS = Arrays.asList(skuInfoRespDTO);
        when(mockSkuService.listSkuInfo(Arrays.asList("value"))).thenReturn(skuInfoRespDTOS);

        // Configure IItemService.listItemInfoBySku(...).
        final ItemInfoRespDTO itemInfoRespDTO = new ItemInfoRespDTO();
        itemInfoRespDTO.setItemGuid("itemGuid");
        itemInfoRespDTO.setBrandGuid("brandGuid");
        itemInfoRespDTO.setStoreGuid("storeGuid");
        itemInfoRespDTO.setItemType(0);
        itemInfoRespDTO.setName("itemName");
        final List<ItemInfoRespDTO> itemInfoRespDTOS = Arrays.asList(itemInfoRespDTO);
        when(mockItemService.listItemInfoBySku(Arrays.asList("value"))).thenReturn(itemInfoRespDTOS);

        // Run the test
        final EstimateForAndroidRespDTO result = iEstimateServiceImplUnderTest.listEstimate("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testListEstimate_SubgroupMapperReturnsNoItems() {
        // Setup
        final EstimateForAndroidRespDTO expectedResult = new EstimateForAndroidRespDTO();
        final EstimateItemRespDTO estimateItemRespDTO = new EstimateItemRespDTO();
        estimateItemRespDTO.setItemGuid("itemGuid");
        estimateItemRespDTO.setItemName("itemName");
        final EstimateSkuRespDTO estimateSkuRespDTO = new EstimateSkuRespDTO();
        estimateSkuRespDTO.setSkuGuid("skuGuid");
        estimateSkuRespDTO.setSkuName("name");
        estimateSkuRespDTO.setLimitQuantity(new BigDecimal("0.00"));
        estimateSkuRespDTO.setResidueQuantity(new BigDecimal("0.00"));
        estimateSkuRespDTO.setIsForeverEstimate(0);
        estimateSkuRespDTO.setItemGuid("itemGuid");
        estimateSkuRespDTO.setEstimateGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        estimateItemRespDTO.setSkuRespList(Arrays.asList(estimateSkuRespDTO));
        estimateItemRespDTO.setEstimateGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setItemRespList(Arrays.asList(estimateItemRespDTO));
        expectedResult.setResumeSaleTime("resumeSaleTime");

        // Configure OrganizationService.queryBusinessDay(...).
        final BusinessDateReqDTO businessDateReqDTO = new BusinessDateReqDTO();
        businessDateReqDTO.setQueryDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        businessDateReqDTO.setStoreGuidList(Arrays.asList("value"));
        businessDateReqDTO.setStoreGuid("storeGuid");
        when(mockOrganizationService.queryBusinessDay(businessDateReqDTO)).thenReturn(LocalDate.of(2020, 1, 1));

        // Configure OrganizationService.queryStoreByGuid(...).
        final StoreDTO storeDTO = new StoreDTO("b524d163-12e5-4adf-a189-5a405ffa1cab", "code", "name",
                "belongBrandGuid", "belongBrandName", "parentIds", LocalTime.of(0, 0, 0), LocalTime.of(0, 0, 0),
                "contactName", "contactTel", "provinceCode", "provinceName", "cityCode", "cityName", "countyCode",
                "countyName", "addressDetail", "longitude", "latitude", false, false, "photos",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "createUserGuid",
                "modifiedUserGuid", Arrays.asList(
                new StoreProductDTO("productGuid", "productName", 0, LocalDate.of(2020, 1, 1), LocalDate.of(2020, 1, 1),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, 0)), Arrays.asList(
                new BrandDTO("356f5fa6-70a9-48e4-ae77-1cd297a330fe", "2225690a-ab50-4c86-9955-11e47a4e7283", "name",
                        "description", "logoUrl", false, false, "createUserGuid", "modifiedUserGuid",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "mchntTypeCode",
                        Arrays.asList(), 0, 0, 0, 0, false)), "mchntTypeCode", "logoUrl", "openingHours", 0, 0, 0, 0,
                "storeNotification", "storeDoorPhoto", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, 0, 0, LocalDate.of(2020, 1, 1));
        when(mockOrganizationService.queryStoreByGuid("storeGuid")).thenReturn(storeDTO);

        when(mockSubgroupMapper.listParentItemGuidBySubGroupSku(Arrays.asList("value")))
                .thenReturn(Collections.emptyList());

        // Configure ISkuService.listSkuInfo(...).
        final SkuInfoRespDTO skuInfoRespDTO = new SkuInfoRespDTO();
        skuInfoRespDTO.setId(0L);
        skuInfoRespDTO.setTypeGuid("typeGuid");
        skuInfoRespDTO.setItemGuid("itemGuid");
        skuInfoRespDTO.setSkuGuid("skuGuid");
        skuInfoRespDTO.setName("name");
        final List<SkuInfoRespDTO> skuInfoRespDTOS = Arrays.asList(skuInfoRespDTO);
        when(mockSkuService.listSkuInfo(Arrays.asList("value"))).thenReturn(skuInfoRespDTOS);

        // Configure IItemService.listItemInfoBySku(...).
        final ItemInfoRespDTO itemInfoRespDTO = new ItemInfoRespDTO();
        itemInfoRespDTO.setItemGuid("itemGuid");
        itemInfoRespDTO.setBrandGuid("brandGuid");
        itemInfoRespDTO.setStoreGuid("storeGuid");
        itemInfoRespDTO.setItemType(0);
        itemInfoRespDTO.setName("itemName");
        final List<ItemInfoRespDTO> itemInfoRespDTOS = Arrays.asList(itemInfoRespDTO);
        when(mockItemService.listItemInfoBySku(Arrays.asList("value"))).thenReturn(itemInfoRespDTOS);

        // Run the test
        final EstimateForAndroidRespDTO result = iEstimateServiceImplUnderTest.listEstimate("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testListEstimate_ISkuServiceReturnsNoItems() {
        // Setup
        final EstimateForAndroidRespDTO expectedResult = new EstimateForAndroidRespDTO();
        final EstimateItemRespDTO estimateItemRespDTO = new EstimateItemRespDTO();
        estimateItemRespDTO.setItemGuid("itemGuid");
        estimateItemRespDTO.setItemName("itemName");
        final EstimateSkuRespDTO estimateSkuRespDTO = new EstimateSkuRespDTO();
        estimateSkuRespDTO.setSkuGuid("skuGuid");
        estimateSkuRespDTO.setSkuName("name");
        estimateSkuRespDTO.setLimitQuantity(new BigDecimal("0.00"));
        estimateSkuRespDTO.setResidueQuantity(new BigDecimal("0.00"));
        estimateSkuRespDTO.setIsForeverEstimate(0);
        estimateSkuRespDTO.setItemGuid("itemGuid");
        estimateSkuRespDTO.setEstimateGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        estimateItemRespDTO.setSkuRespList(Arrays.asList(estimateSkuRespDTO));
        estimateItemRespDTO.setEstimateGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setItemRespList(Arrays.asList(estimateItemRespDTO));
        expectedResult.setResumeSaleTime("resumeSaleTime");

        // Configure OrganizationService.queryBusinessDay(...).
        final BusinessDateReqDTO businessDateReqDTO = new BusinessDateReqDTO();
        businessDateReqDTO.setQueryDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        businessDateReqDTO.setStoreGuidList(Arrays.asList("value"));
        businessDateReqDTO.setStoreGuid("storeGuid");
        when(mockOrganizationService.queryBusinessDay(businessDateReqDTO)).thenReturn(LocalDate.of(2020, 1, 1));

        // Configure OrganizationService.queryStoreByGuid(...).
        final StoreDTO storeDTO = new StoreDTO("b524d163-12e5-4adf-a189-5a405ffa1cab", "code", "name",
                "belongBrandGuid", "belongBrandName", "parentIds", LocalTime.of(0, 0, 0), LocalTime.of(0, 0, 0),
                "contactName", "contactTel", "provinceCode", "provinceName", "cityCode", "cityName", "countyCode",
                "countyName", "addressDetail", "longitude", "latitude", false, false, "photos",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "createUserGuid",
                "modifiedUserGuid", Arrays.asList(
                new StoreProductDTO("productGuid", "productName", 0, LocalDate.of(2020, 1, 1), LocalDate.of(2020, 1, 1),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, 0)), Arrays.asList(
                new BrandDTO("356f5fa6-70a9-48e4-ae77-1cd297a330fe", "2225690a-ab50-4c86-9955-11e47a4e7283", "name",
                        "description", "logoUrl", false, false, "createUserGuid", "modifiedUserGuid",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "mchntTypeCode",
                        Arrays.asList(), 0, 0, 0, 0, false)), "mchntTypeCode", "logoUrl", "openingHours", 0, 0, 0, 0,
                "storeNotification", "storeDoorPhoto", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, 0, 0, LocalDate.of(2020, 1, 1));
        when(mockOrganizationService.queryStoreByGuid("storeGuid")).thenReturn(storeDTO);

        when(mockSubgroupMapper.listParentItemGuidBySubGroupSku(Arrays.asList("value")))
                .thenReturn(Arrays.asList("value"));
        when(mockSkuService.listSkuInfo(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Configure IItemService.listItemInfoBySku(...).
        final ItemInfoRespDTO itemInfoRespDTO = new ItemInfoRespDTO();
        itemInfoRespDTO.setItemGuid("itemGuid");
        itemInfoRespDTO.setBrandGuid("brandGuid");
        itemInfoRespDTO.setStoreGuid("storeGuid");
        itemInfoRespDTO.setItemType(0);
        itemInfoRespDTO.setName("itemName");
        final List<ItemInfoRespDTO> itemInfoRespDTOS = Arrays.asList(itemInfoRespDTO);
        when(mockItemService.listItemInfoBySku(Arrays.asList("value"))).thenReturn(itemInfoRespDTOS);

        // Run the test
        final EstimateForAndroidRespDTO result = iEstimateServiceImplUnderTest.listEstimate("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testListEstimate_IItemServiceReturnsNoItems() {
        // Setup
        final EstimateForAndroidRespDTO expectedResult = new EstimateForAndroidRespDTO();
        final EstimateItemRespDTO estimateItemRespDTO = new EstimateItemRespDTO();
        estimateItemRespDTO.setItemGuid("itemGuid");
        estimateItemRespDTO.setItemName("itemName");
        final EstimateSkuRespDTO estimateSkuRespDTO = new EstimateSkuRespDTO();
        estimateSkuRespDTO.setSkuGuid("skuGuid");
        estimateSkuRespDTO.setSkuName("name");
        estimateSkuRespDTO.setLimitQuantity(new BigDecimal("0.00"));
        estimateSkuRespDTO.setResidueQuantity(new BigDecimal("0.00"));
        estimateSkuRespDTO.setIsForeverEstimate(0);
        estimateSkuRespDTO.setItemGuid("itemGuid");
        estimateSkuRespDTO.setEstimateGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        estimateItemRespDTO.setSkuRespList(Arrays.asList(estimateSkuRespDTO));
        estimateItemRespDTO.setEstimateGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setItemRespList(Arrays.asList(estimateItemRespDTO));
        expectedResult.setResumeSaleTime("resumeSaleTime");

        // Configure OrganizationService.queryBusinessDay(...).
        final BusinessDateReqDTO businessDateReqDTO = new BusinessDateReqDTO();
        businessDateReqDTO.setQueryDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        businessDateReqDTO.setStoreGuidList(Arrays.asList("value"));
        businessDateReqDTO.setStoreGuid("storeGuid");
        when(mockOrganizationService.queryBusinessDay(businessDateReqDTO)).thenReturn(LocalDate.of(2020, 1, 1));

        // Configure OrganizationService.queryStoreByGuid(...).
        final StoreDTO storeDTO = new StoreDTO("b524d163-12e5-4adf-a189-5a405ffa1cab", "code", "name",
                "belongBrandGuid", "belongBrandName", "parentIds", LocalTime.of(0, 0, 0), LocalTime.of(0, 0, 0),
                "contactName", "contactTel", "provinceCode", "provinceName", "cityCode", "cityName", "countyCode",
                "countyName", "addressDetail", "longitude", "latitude", false, false, "photos",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "createUserGuid",
                "modifiedUserGuid", Arrays.asList(
                new StoreProductDTO("productGuid", "productName", 0, LocalDate.of(2020, 1, 1), LocalDate.of(2020, 1, 1),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, 0)), Arrays.asList(
                new BrandDTO("356f5fa6-70a9-48e4-ae77-1cd297a330fe", "2225690a-ab50-4c86-9955-11e47a4e7283", "name",
                        "description", "logoUrl", false, false, "createUserGuid", "modifiedUserGuid",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "mchntTypeCode",
                        Arrays.asList(), 0, 0, 0, 0, false)), "mchntTypeCode", "logoUrl", "openingHours", 0, 0, 0, 0,
                "storeNotification", "storeDoorPhoto", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, 0, 0, LocalDate.of(2020, 1, 1));
        when(mockOrganizationService.queryStoreByGuid("storeGuid")).thenReturn(storeDTO);

        when(mockSubgroupMapper.listParentItemGuidBySubGroupSku(Arrays.asList("value")))
                .thenReturn(Arrays.asList("value"));

        // Configure ISkuService.listSkuInfo(...).
        final SkuInfoRespDTO skuInfoRespDTO = new SkuInfoRespDTO();
        skuInfoRespDTO.setId(0L);
        skuInfoRespDTO.setTypeGuid("typeGuid");
        skuInfoRespDTO.setItemGuid("itemGuid");
        skuInfoRespDTO.setSkuGuid("skuGuid");
        skuInfoRespDTO.setName("name");
        final List<SkuInfoRespDTO> skuInfoRespDTOS = Arrays.asList(skuInfoRespDTO);
        when(mockSkuService.listSkuInfo(Arrays.asList("value"))).thenReturn(skuInfoRespDTOS);

        when(mockItemService.listItemInfoBySku(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        final EstimateForAndroidRespDTO result = iEstimateServiceImplUnderTest.listEstimate("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testBatchCancelEstimate() {
        // Setup
        final EstimateBO biz = new EstimateBO();
        biz.setItemGuid("itemGuid");
        biz.setStoreGuid("storeGuid");
        biz.setSkuList(Arrays.asList(EstimateDO.builder()
                .id(0L)
                .guid("fadf78fc-29ae-41af-b609-1dbfb215ac7a")
                .itemGuid("itemGuid")
                .skuGuid("skuGuid")
                .isSoldOut(0)
                .isTheLimit(0)
                .uniqueResetFlag(0)
                .limitQuantity(new BigDecimal("0.00"))
                .residueQuantity(new BigDecimal("0.00"))
                .storeGuid("storeGuid")
                .gmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .isDelete(0)
                .version(0)
                .isForeverEstimate(0)
                .build()));
        biz.setItemGuidList(Arrays.asList("value"));
        biz.setCreateSkuList(Arrays.asList(EstimateDO.builder()
                .id(0L)
                .guid("fadf78fc-29ae-41af-b609-1dbfb215ac7a")
                .itemGuid("itemGuid")
                .skuGuid("skuGuid")
                .isSoldOut(0)
                .isTheLimit(0)
                .uniqueResetFlag(0)
                .limitQuantity(new BigDecimal("0.00"))
                .residueQuantity(new BigDecimal("0.00"))
                .storeGuid("storeGuid")
                .gmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .isDelete(0)
                .version(0)
                .isForeverEstimate(0)
                .build()));
        biz.setUpdateSkuList(Arrays.asList(EstimateDO.builder()
                .id(0L)
                .guid("fadf78fc-29ae-41af-b609-1dbfb215ac7a")
                .itemGuid("itemGuid")
                .skuGuid("skuGuid")
                .isSoldOut(0)
                .isTheLimit(0)
                .uniqueResetFlag(0)
                .limitQuantity(new BigDecimal("0.00"))
                .residueQuantity(new BigDecimal("0.00"))
                .storeGuid("storeGuid")
                .gmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .isDelete(0)
                .version(0)
                .isForeverEstimate(0)
                .build()));
        biz.setRemoveSkuList(Arrays.asList(EstimateDO.builder()
                .id(0L)
                .guid("fadf78fc-29ae-41af-b609-1dbfb215ac7a")
                .itemGuid("itemGuid")
                .skuGuid("skuGuid")
                .isSoldOut(0)
                .isTheLimit(0)
                .uniqueResetFlag(0)
                .limitQuantity(new BigDecimal("0.00"))
                .residueQuantity(new BigDecimal("0.00"))
                .storeGuid("storeGuid")
                .gmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .isDelete(0)
                .version(0)
                .isForeverEstimate(0)
                .build()));
        biz.setBatchCancelSkuList(Arrays.asList(EstimateDO.builder()
                .id(0L)
                .guid("fadf78fc-29ae-41af-b609-1dbfb215ac7a")
                .itemGuid("itemGuid")
                .skuGuid("skuGuid")
                .isSoldOut(0)
                .isTheLimit(0)
                .uniqueResetFlag(0)
                .limitQuantity(new BigDecimal("0.00"))
                .residueQuantity(new BigDecimal("0.00"))
                .storeGuid("storeGuid")
                .gmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .isDelete(0)
                .version(0)
                .isForeverEstimate(0)
                .build()));
        biz.setBatchStopSkuList(Arrays.asList(EstimateDO.builder()
                .id(0L)
                .guid("fadf78fc-29ae-41af-b609-1dbfb215ac7a")
                .itemGuid("itemGuid")
                .skuGuid("skuGuid")
                .isSoldOut(0)
                .isTheLimit(0)
                .uniqueResetFlag(0)
                .limitQuantity(new BigDecimal("0.00"))
                .residueQuantity(new BigDecimal("0.00"))
                .storeGuid("storeGuid")
                .gmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .isDelete(0)
                .version(0)
                .isForeverEstimate(0)
                .build()));
        biz.setSchedulingSkuList(Arrays.asList(EstimateDO.builder()
                .id(0L)
                .guid("fadf78fc-29ae-41af-b609-1dbfb215ac7a")
                .itemGuid("itemGuid")
                .skuGuid("skuGuid")
                .isSoldOut(0)
                .isTheLimit(0)
                .uniqueResetFlag(0)
                .limitQuantity(new BigDecimal("0.00"))
                .residueQuantity(new BigDecimal("0.00"))
                .storeGuid("storeGuid")
                .gmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .isDelete(0)
                .version(0)
                .isForeverEstimate(0)
                .build()));
        biz.setReduceStockSkuList(Arrays.asList(EstimateDO.builder()
                .id(0L)
                .guid("fadf78fc-29ae-41af-b609-1dbfb215ac7a")
                .itemGuid("itemGuid")
                .skuGuid("skuGuid")
                .isSoldOut(0)
                .isTheLimit(0)
                .uniqueResetFlag(0)
                .limitQuantity(new BigDecimal("0.00"))
                .residueQuantity(new BigDecimal("0.00"))
                .storeGuid("storeGuid")
                .gmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .isDelete(0)
                .version(0)
                .isForeverEstimate(0)
                .build()));
        biz.setReturnStockSkuList(Arrays.asList(EstimateDO.builder()
                .id(0L)
                .guid("fadf78fc-29ae-41af-b609-1dbfb215ac7a")
                .itemGuid("itemGuid")
                .skuGuid("skuGuid")
                .isSoldOut(0)
                .isTheLimit(0)
                .uniqueResetFlag(0)
                .limitQuantity(new BigDecimal("0.00"))
                .residueQuantity(new BigDecimal("0.00"))
                .storeGuid("storeGuid")
                .gmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .isDelete(0)
                .version(0)
                .isForeverEstimate(0)
                .build()));

        when(mockEstimateMapper.listSubGroupEstimateSkuGuidBySku(Arrays.asList("value")))
                .thenReturn(Arrays.asList("value"));

        // Run the test
        final Boolean result = iEstimateServiceImplUnderTest.batchCancelEstimate(biz);

        // Verify the results
        assertThat(result).isTrue();
        verify(mockEstimateMapper).batchDeleteEstimateBySkuGuid(Arrays.asList("value"), "storeGuid");
        verify(mockEstimateOpLogService).saveOpLog(Arrays.asList(EstimateOpLogDO.builder().build()), "enterpriseGuid");
        verify(mockRedisTemplate).delete("key");
    }

    @Test
    public void testBatchCancelEstimate_EstimateMapperListSubGroupEstimateSkuGuidBySkuReturnsNoItems() {
        // Setup
        final EstimateBO biz = new EstimateBO();
        biz.setItemGuid("itemGuid");
        biz.setStoreGuid("storeGuid");
        biz.setSkuList(Arrays.asList(EstimateDO.builder()
                .id(0L)
                .guid("fadf78fc-29ae-41af-b609-1dbfb215ac7a")
                .itemGuid("itemGuid")
                .skuGuid("skuGuid")
                .isSoldOut(0)
                .isTheLimit(0)
                .uniqueResetFlag(0)
                .limitQuantity(new BigDecimal("0.00"))
                .residueQuantity(new BigDecimal("0.00"))
                .storeGuid("storeGuid")
                .gmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .isDelete(0)
                .version(0)
                .isForeverEstimate(0)
                .build()));
        biz.setItemGuidList(Arrays.asList("value"));
        biz.setCreateSkuList(Arrays.asList(EstimateDO.builder()
                .id(0L)
                .guid("fadf78fc-29ae-41af-b609-1dbfb215ac7a")
                .itemGuid("itemGuid")
                .skuGuid("skuGuid")
                .isSoldOut(0)
                .isTheLimit(0)
                .uniqueResetFlag(0)
                .limitQuantity(new BigDecimal("0.00"))
                .residueQuantity(new BigDecimal("0.00"))
                .storeGuid("storeGuid")
                .gmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .isDelete(0)
                .version(0)
                .isForeverEstimate(0)
                .build()));
        biz.setUpdateSkuList(Arrays.asList(EstimateDO.builder()
                .id(0L)
                .guid("fadf78fc-29ae-41af-b609-1dbfb215ac7a")
                .itemGuid("itemGuid")
                .skuGuid("skuGuid")
                .isSoldOut(0)
                .isTheLimit(0)
                .uniqueResetFlag(0)
                .limitQuantity(new BigDecimal("0.00"))
                .residueQuantity(new BigDecimal("0.00"))
                .storeGuid("storeGuid")
                .gmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .isDelete(0)
                .version(0)
                .isForeverEstimate(0)
                .build()));
        biz.setRemoveSkuList(Arrays.asList(EstimateDO.builder()
                .id(0L)
                .guid("fadf78fc-29ae-41af-b609-1dbfb215ac7a")
                .itemGuid("itemGuid")
                .skuGuid("skuGuid")
                .isSoldOut(0)
                .isTheLimit(0)
                .uniqueResetFlag(0)
                .limitQuantity(new BigDecimal("0.00"))
                .residueQuantity(new BigDecimal("0.00"))
                .storeGuid("storeGuid")
                .gmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .isDelete(0)
                .version(0)
                .isForeverEstimate(0)
                .build()));
        biz.setBatchCancelSkuList(Arrays.asList(EstimateDO.builder()
                .id(0L)
                .guid("fadf78fc-29ae-41af-b609-1dbfb215ac7a")
                .itemGuid("itemGuid")
                .skuGuid("skuGuid")
                .isSoldOut(0)
                .isTheLimit(0)
                .uniqueResetFlag(0)
                .limitQuantity(new BigDecimal("0.00"))
                .residueQuantity(new BigDecimal("0.00"))
                .storeGuid("storeGuid")
                .gmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .isDelete(0)
                .version(0)
                .isForeverEstimate(0)
                .build()));
        biz.setBatchStopSkuList(Arrays.asList(EstimateDO.builder()
                .id(0L)
                .guid("fadf78fc-29ae-41af-b609-1dbfb215ac7a")
                .itemGuid("itemGuid")
                .skuGuid("skuGuid")
                .isSoldOut(0)
                .isTheLimit(0)
                .uniqueResetFlag(0)
                .limitQuantity(new BigDecimal("0.00"))
                .residueQuantity(new BigDecimal("0.00"))
                .storeGuid("storeGuid")
                .gmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .isDelete(0)
                .version(0)
                .isForeverEstimate(0)
                .build()));
        biz.setSchedulingSkuList(Arrays.asList(EstimateDO.builder()
                .id(0L)
                .guid("fadf78fc-29ae-41af-b609-1dbfb215ac7a")
                .itemGuid("itemGuid")
                .skuGuid("skuGuid")
                .isSoldOut(0)
                .isTheLimit(0)
                .uniqueResetFlag(0)
                .limitQuantity(new BigDecimal("0.00"))
                .residueQuantity(new BigDecimal("0.00"))
                .storeGuid("storeGuid")
                .gmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .isDelete(0)
                .version(0)
                .isForeverEstimate(0)
                .build()));
        biz.setReduceStockSkuList(Arrays.asList(EstimateDO.builder()
                .id(0L)
                .guid("fadf78fc-29ae-41af-b609-1dbfb215ac7a")
                .itemGuid("itemGuid")
                .skuGuid("skuGuid")
                .isSoldOut(0)
                .isTheLimit(0)
                .uniqueResetFlag(0)
                .limitQuantity(new BigDecimal("0.00"))
                .residueQuantity(new BigDecimal("0.00"))
                .storeGuid("storeGuid")
                .gmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .isDelete(0)
                .version(0)
                .isForeverEstimate(0)
                .build()));
        biz.setReturnStockSkuList(Arrays.asList(EstimateDO.builder()
                .id(0L)
                .guid("fadf78fc-29ae-41af-b609-1dbfb215ac7a")
                .itemGuid("itemGuid")
                .skuGuid("skuGuid")
                .isSoldOut(0)
                .isTheLimit(0)
                .uniqueResetFlag(0)
                .limitQuantity(new BigDecimal("0.00"))
                .residueQuantity(new BigDecimal("0.00"))
                .storeGuid("storeGuid")
                .gmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .isDelete(0)
                .version(0)
                .isForeverEstimate(0)
                .build()));

        when(mockEstimateMapper.listSubGroupEstimateSkuGuidBySku(Arrays.asList("value")))
                .thenReturn(Collections.emptyList());

        // Run the test
        final Boolean result = iEstimateServiceImplUnderTest.batchCancelEstimate(biz);

        // Verify the results
        assertThat(result).isTrue();
        verify(mockEstimateMapper).batchDeleteEstimateBySkuGuid(Arrays.asList("value"), "storeGuid");
        verify(mockEstimateOpLogService).saveOpLog(Arrays.asList(EstimateOpLogDO.builder().build()), "enterpriseGuid");
        verify(mockRedisTemplate).delete("key");
    }

    @Test
    public void testBatchStopSell() {
        // Setup
        final EstimateBO biz = new EstimateBO();
        biz.setItemGuid("itemGuid");
        biz.setStoreGuid("storeGuid");
        biz.setSkuList(Arrays.asList(EstimateDO.builder()
                .id(0L)
                .guid("fadf78fc-29ae-41af-b609-1dbfb215ac7a")
                .itemGuid("itemGuid")
                .skuGuid("skuGuid")
                .isSoldOut(0)
                .isTheLimit(0)
                .uniqueResetFlag(0)
                .limitQuantity(new BigDecimal("0.00"))
                .residueQuantity(new BigDecimal("0.00"))
                .storeGuid("storeGuid")
                .gmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .isDelete(0)
                .version(0)
                .isForeverEstimate(0)
                .build()));
        biz.setItemGuidList(Arrays.asList("value"));
        biz.setCreateSkuList(Arrays.asList(EstimateDO.builder()
                .id(0L)
                .guid("fadf78fc-29ae-41af-b609-1dbfb215ac7a")
                .itemGuid("itemGuid")
                .skuGuid("skuGuid")
                .isSoldOut(0)
                .isTheLimit(0)
                .uniqueResetFlag(0)
                .limitQuantity(new BigDecimal("0.00"))
                .residueQuantity(new BigDecimal("0.00"))
                .storeGuid("storeGuid")
                .gmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .isDelete(0)
                .version(0)
                .isForeverEstimate(0)
                .build()));
        biz.setUpdateSkuList(Arrays.asList(EstimateDO.builder()
                .id(0L)
                .guid("fadf78fc-29ae-41af-b609-1dbfb215ac7a")
                .itemGuid("itemGuid")
                .skuGuid("skuGuid")
                .isSoldOut(0)
                .isTheLimit(0)
                .uniqueResetFlag(0)
                .limitQuantity(new BigDecimal("0.00"))
                .residueQuantity(new BigDecimal("0.00"))
                .storeGuid("storeGuid")
                .gmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .isDelete(0)
                .version(0)
                .isForeverEstimate(0)
                .build()));
        biz.setRemoveSkuList(Arrays.asList(EstimateDO.builder()
                .id(0L)
                .guid("fadf78fc-29ae-41af-b609-1dbfb215ac7a")
                .itemGuid("itemGuid")
                .skuGuid("skuGuid")
                .isSoldOut(0)
                .isTheLimit(0)
                .uniqueResetFlag(0)
                .limitQuantity(new BigDecimal("0.00"))
                .residueQuantity(new BigDecimal("0.00"))
                .storeGuid("storeGuid")
                .gmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .isDelete(0)
                .version(0)
                .isForeverEstimate(0)
                .build()));
        biz.setBatchCancelSkuList(Arrays.asList(EstimateDO.builder()
                .id(0L)
                .guid("fadf78fc-29ae-41af-b609-1dbfb215ac7a")
                .itemGuid("itemGuid")
                .skuGuid("skuGuid")
                .isSoldOut(0)
                .isTheLimit(0)
                .uniqueResetFlag(0)
                .limitQuantity(new BigDecimal("0.00"))
                .residueQuantity(new BigDecimal("0.00"))
                .storeGuid("storeGuid")
                .gmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .isDelete(0)
                .version(0)
                .isForeverEstimate(0)
                .build()));
        biz.setBatchStopSkuList(Arrays.asList(EstimateDO.builder()
                .id(0L)
                .guid("fadf78fc-29ae-41af-b609-1dbfb215ac7a")
                .itemGuid("itemGuid")
                .skuGuid("skuGuid")
                .isSoldOut(0)
                .isTheLimit(0)
                .uniqueResetFlag(0)
                .limitQuantity(new BigDecimal("0.00"))
                .residueQuantity(new BigDecimal("0.00"))
                .storeGuid("storeGuid")
                .gmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .isDelete(0)
                .version(0)
                .isForeverEstimate(0)
                .build()));
        biz.setSchedulingSkuList(Arrays.asList(EstimateDO.builder()
                .id(0L)
                .guid("fadf78fc-29ae-41af-b609-1dbfb215ac7a")
                .itemGuid("itemGuid")
                .skuGuid("skuGuid")
                .isSoldOut(0)
                .isTheLimit(0)
                .uniqueResetFlag(0)
                .limitQuantity(new BigDecimal("0.00"))
                .residueQuantity(new BigDecimal("0.00"))
                .storeGuid("storeGuid")
                .gmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .isDelete(0)
                .version(0)
                .isForeverEstimate(0)
                .build()));
        biz.setReduceStockSkuList(Arrays.asList(EstimateDO.builder()
                .id(0L)
                .guid("fadf78fc-29ae-41af-b609-1dbfb215ac7a")
                .itemGuid("itemGuid")
                .skuGuid("skuGuid")
                .isSoldOut(0)
                .isTheLimit(0)
                .uniqueResetFlag(0)
                .limitQuantity(new BigDecimal("0.00"))
                .residueQuantity(new BigDecimal("0.00"))
                .storeGuid("storeGuid")
                .gmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .isDelete(0)
                .version(0)
                .isForeverEstimate(0)
                .build()));
        biz.setReturnStockSkuList(Arrays.asList(EstimateDO.builder()
                .id(0L)
                .guid("fadf78fc-29ae-41af-b609-1dbfb215ac7a")
                .itemGuid("itemGuid")
                .skuGuid("skuGuid")
                .isSoldOut(0)
                .isTheLimit(0)
                .uniqueResetFlag(0)
                .limitQuantity(new BigDecimal("0.00"))
                .residueQuantity(new BigDecimal("0.00"))
                .storeGuid("storeGuid")
                .gmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .isDelete(0)
                .version(0)
                .isForeverEstimate(0)
                .build()));

        // Run the test
        final Boolean result = iEstimateServiceImplUnderTest.batchStopSell(biz);

        // Verify the results
        assertThat(result).isTrue();
        verify(mockEstimateMapper).batchStopSellBySkuGuid(Arrays.asList("value"), "storeGuid");
        verify(mockEstimateOpLogService).saveOpLog(Arrays.asList(EstimateOpLogDO.builder().build()), "enterpriseGuid");
        verify(mockRedisTemplate).delete("key");
    }

    @Test
    public void testListByStoreGuidAndSkuGuidList() {
        // Setup
        final List<EstimateDO> expectedResult = Arrays.asList(EstimateDO.builder()
                .id(0L)
                .guid("fadf78fc-29ae-41af-b609-1dbfb215ac7a")
                .itemGuid("itemGuid")
                .skuGuid("skuGuid")
                .isSoldOut(0)
                .isTheLimit(0)
                .uniqueResetFlag(0)
                .limitQuantity(new BigDecimal("0.00"))
                .residueQuantity(new BigDecimal("0.00"))
                .storeGuid("storeGuid")
                .gmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .isDelete(0)
                .version(0)
                .isForeverEstimate(0)
                .build());

        // Run the test
        final List<EstimateDO> result = iEstimateServiceImplUnderTest.listByStoreGuidAndSkuGuidList("storeGuid",
                Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testListByStoreGuidsAndIsForeverList() {
        // Setup
        final List<EstimateDO> expectedResult = Arrays.asList(EstimateDO.builder()
                .id(0L)
                .guid("fadf78fc-29ae-41af-b609-1dbfb215ac7a")
                .itemGuid("itemGuid")
                .skuGuid("skuGuid")
                .isSoldOut(0)
                .isTheLimit(0)
                .uniqueResetFlag(0)
                .limitQuantity(new BigDecimal("0.00"))
                .residueQuantity(new BigDecimal("0.00"))
                .storeGuid("storeGuid")
                .gmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .isDelete(0)
                .version(0)
                .isForeverEstimate(0)
                .build());

        // Run the test
        final List<EstimateDO> result = iEstimateServiceImplUnderTest.listByStoreGuidsAndIsForeverList(
                Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testStoreItemEstimateCancel() {
        // Setup
        final Map<String, List<String>> request = new HashMap<>();

        // Run the test
        final Boolean result = iEstimateServiceImplUnderTest.storeItemEstimateCancel(request);

        // Verify the results
        assertThat(result).isTrue();
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
        verify(mockEstimateMapper).batchDeleteEstimateByStoreGuid(Arrays.asList("value"));
        verify(mockEstimateOpLogService).saveOpLog(Arrays.asList(EstimateOpLogDO.builder().build()), "enterpriseGuid");
        verify(mockRedisTemplate).delete(Arrays.asList("value"));
    }

    @Test
    public void testListEstimateByItem() {
        // Setup
        final EstimateItemRespDTO expectedResult = new EstimateItemRespDTO();
        expectedResult.setItemGuid("itemGuid");
        expectedResult.setItemName("itemName");
        final EstimateSkuRespDTO estimateSkuRespDTO = new EstimateSkuRespDTO();
        estimateSkuRespDTO.setSkuGuid("skuGuid");
        estimateSkuRespDTO.setSkuName("name");
        estimateSkuRespDTO.setLimitQuantity(new BigDecimal("0.00"));
        estimateSkuRespDTO.setResidueQuantity(new BigDecimal("0.00"));
        estimateSkuRespDTO.setIsForeverEstimate(0);
        estimateSkuRespDTO.setItemGuid("itemGuid");
        estimateSkuRespDTO.setEstimateGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setSkuRespList(Arrays.asList(estimateSkuRespDTO));
        expectedResult.setEstimateGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Run the test
        final EstimateItemRespDTO result = iEstimateServiceImplUnderTest.listEstimateByItem("itemGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
