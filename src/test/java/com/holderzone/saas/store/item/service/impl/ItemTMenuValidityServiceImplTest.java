

package com.holderzone.saas.store.item.service.impl;

import static org.mockito.MockitoAnnotations.initMocks;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import java.time.LocalDateTime;import java.util.Arrays;import java.util.Collections;import java.util.List;import java.util.concurrent.Callable;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.assertj.core.api.Assertions.within;
    import static org.mockito.ArgumentMatchers.any;
    import static org.mockito.ArgumentMatchers.anyInt;
    import static org.mockito.ArgumentMatchers.anyString;
    import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import com.holderzone.saas.store.dto.common.SingleDataDTO;import com.holderzone.saas.store.dto.item.req.ItemTemplateMenuTimeReqDTO;import com.holderzone.saas.store.dto.item.req.ItmeTemplateMenuValidityReqDTO;import com.holderzone.saas.store.item.entity.domain.ItemTMenuValidityDO;import com.holderzone.saas.store.item.entity.query.ItemTemplateExecuteTimeQuery;import com.holderzone.saas.store.item.helper.ItemHelper;import com.holderzone.saas.store.item.mapper.ItemTMenuValidityMapper;import com.holderzone.saas.store.item.util.DynamicHelper;import org.mockito.junit.MockitoJUnitRunner;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.InjectMocks;
import org.mockito.stubbing.Answer;import org.springframework.data.redis.core.RedisTemplate;

@RunWith(MockitoJUnitRunner.class)
public class ItemTMenuValidityServiceImplTest {

            @Mock
        private ItemTMenuValidityMapper mockItemTMenuValidityMapper;
            @Mock
        private DynamicHelper mockDynamicHelper;
            @Mock
        private ItemHelper mockItemHelper;
            @Mock
        private RedisTemplate mockRedisTemplate;

    private ItemTMenuValidityServiceImpl itemTMenuValidityServiceImplUnderTest;

@Before
public void setUp() throws Exception {
            itemTMenuValidityServiceImplUnderTest = new ItemTMenuValidityServiceImpl(mockItemTMenuValidityMapper,mockDynamicHelper,mockItemHelper,mockRedisTemplate) ;
}
                
    @Test
    public void testGetMenuValiditys() throws Exception {
    // Setup
                                                                        final ItemTMenuValidityDO itemTMenuValidityDO = new ItemTMenuValidityDO();
                itemTMenuValidityDO.setGuid("d60c83cf-1507-4156-8f94-edafdf18dac4");
                itemTMenuValidityDO.setItemMenuGuid("menuGuid");
                itemTMenuValidityDO.setWeeks("weeks");
                itemTMenuValidityDO.setTimes("times");
                itemTMenuValidityDO.setIsItFullTime(0);
                itemTMenuValidityDO.setPeriodicMode(0);
                itemTMenuValidityDO.setWeeksQuantum(0);
                itemTMenuValidityDO.setTimesQuantum(0);
        final List<ItemTMenuValidityDO> expectedResult = Arrays.asList(itemTMenuValidityDO);
                
            // Configure ItemTMenuValidityMapper.getMenuValiditys(...).
                                                        final ItemTMenuValidityDO itemTMenuValidityDO1 = new ItemTMenuValidityDO();
                itemTMenuValidityDO1.setGuid("d60c83cf-1507-4156-8f94-edafdf18dac4");
                itemTMenuValidityDO1.setItemMenuGuid("menuGuid");
                itemTMenuValidityDO1.setWeeks("weeks");
                itemTMenuValidityDO1.setTimes("times");
                itemTMenuValidityDO1.setIsItFullTime(0);
                itemTMenuValidityDO1.setPeriodicMode(0);
                itemTMenuValidityDO1.setWeeksQuantum(0);
                itemTMenuValidityDO1.setTimesQuantum(0);
        final List<ItemTMenuValidityDO> itemTMenuValidityDOS = Arrays.asList(itemTMenuValidityDO1);
            when( mockItemTMenuValidityMapper .getMenuValiditys("0b693cd6-8f16-409a-9e77-d26448253bf3",0,"menuGuid")).thenReturn(itemTMenuValidityDOS);

    // Run the test
 final List<ItemTMenuValidityDO> result =  itemTMenuValidityServiceImplUnderTest.getMenuValiditys("0b693cd6-8f16-409a-9e77-d26448253bf3",0,"menuGuid");

        // Verify the results
 assertThat(result).isEqualTo(expectedResult ) ;
    }
                                                                                                
    @Test
    public void testGetMenuValiditys_ItemTMenuValidityMapperReturnsNoItems() throws Exception {
    // Setup
                        when( mockItemTMenuValidityMapper .getMenuValiditys("0b693cd6-8f16-409a-9e77-d26448253bf3",0,"menuGuid")).thenReturn( Collections.emptyList() );

    // Run the test
 final List<ItemTMenuValidityDO> result =  itemTMenuValidityServiceImplUnderTest.getMenuValiditys("0b693cd6-8f16-409a-9e77-d26448253bf3",0,"menuGuid");

        // Verify the results
 assertThat(result).isEqualTo(Collections.emptyList() ) ;
    }
                
    @Test
    public void testSaveItemMenuValidity() throws Exception {
    // Setup
                        final ItmeTemplateMenuValidityReqDTO request = new ItmeTemplateMenuValidityReqDTO("b756a49a-a247-4c9a-9bf7-2cb1e77f3c52", "itemMenuGuid", Arrays.asList(0), 0, Arrays.asList(new ItemTemplateMenuTimeReqDTO("startTime", "endTime")), 0, 0);

    // Run the test
 final Boolean result =  itemTMenuValidityServiceImplUnderTest.saveItemMenuValidity(request,0,"menuGuid",0);

        // Verify the results
 assertThat(result).isFalse() ;
    }
                                        
    @Test
    public void testGetTimeAndTypeForSyn() throws Exception {
    // Setup
                        final SingleDataDTO request = new SingleDataDTO("data", Arrays.asList("value"));
                
            // Configure ItemTMenuValidityMapper.getTimeAndTypeForSyn(...).
                                                        final ItemTemplateExecuteTimeQuery itemTemplateExecuteTimeQuery = new ItemTemplateExecuteTimeQuery();
                itemTemplateExecuteTimeQuery.setGuid("61d23860-fb0f-4714-bf4a-0fb7dbe84505");
                itemTemplateExecuteTimeQuery.setDifference(0);
                itemTemplateExecuteTimeQuery.setNow(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
                itemTemplateExecuteTimeQuery.setWeeks("weeks");
                itemTemplateExecuteTimeQuery.setTimes("times");
        final List<ItemTemplateExecuteTimeQuery> itemTemplateExecuteTimeQueries = Arrays.asList(itemTemplateExecuteTimeQuery);
            when( mockItemTMenuValidityMapper .getTimeAndTypeForSyn(new SingleDataDTO("data", Arrays.asList("value")))).thenReturn(itemTemplateExecuteTimeQueries);

            // Configure ItemHelper.templateExecuteHander(...).
                                                        final ItemTemplateExecuteTimeQuery itemTemplateExecuteTimeQuery1 = new ItemTemplateExecuteTimeQuery();
                itemTemplateExecuteTimeQuery1.setGuid("61d23860-fb0f-4714-bf4a-0fb7dbe84505");
                itemTemplateExecuteTimeQuery1.setDifference(0);
                itemTemplateExecuteTimeQuery1.setNow(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
                itemTemplateExecuteTimeQuery1.setWeeks("weeks");
                itemTemplateExecuteTimeQuery1.setTimes("times");
        final List<ItemTemplateExecuteTimeQuery> list = Arrays.asList(itemTemplateExecuteTimeQuery1);
            when( mockItemHelper .templateExecuteHander(list)).thenReturn( Arrays.asList(0L) );

    // Run the test
 final List<Long> result =  itemTMenuValidityServiceImplUnderTest.getTimeAndTypeForSyn(request);

        // Verify the results
 assertThat(result).isEqualTo( Arrays.asList(0L) ) ;
    }
                                                                                                
    @Test
    public void testGetTimeAndTypeForSyn_ItemTMenuValidityMapperReturnsNoItems() throws Exception {
    // Setup
                        final SingleDataDTO request = new SingleDataDTO("data", Arrays.asList("value"));
        when( mockItemTMenuValidityMapper .getTimeAndTypeForSyn(new SingleDataDTO("data", Arrays.asList("value")))).thenReturn( Collections.emptyList() );
                
            // Configure ItemHelper.templateExecuteHander(...).
                                                        final ItemTemplateExecuteTimeQuery itemTemplateExecuteTimeQuery = new ItemTemplateExecuteTimeQuery();
                itemTemplateExecuteTimeQuery.setGuid("61d23860-fb0f-4714-bf4a-0fb7dbe84505");
                itemTemplateExecuteTimeQuery.setDifference(0);
                itemTemplateExecuteTimeQuery.setNow(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
                itemTemplateExecuteTimeQuery.setWeeks("weeks");
                itemTemplateExecuteTimeQuery.setTimes("times");
        final List<ItemTemplateExecuteTimeQuery> list = Arrays.asList(itemTemplateExecuteTimeQuery);
            when( mockItemHelper .templateExecuteHander(list)).thenReturn( Arrays.asList(0L) );

    // Run the test
 final List<Long> result =  itemTMenuValidityServiceImplUnderTest.getTimeAndTypeForSyn(request);

        // Verify the results
 assertThat(result).isEqualTo( Arrays.asList(0L) ) ;
    }
                                                                        
    @Test
    public void testGetTimeAndTypeForSyn_ItemHelperReturnsNoItems() throws Exception {
    // Setup
                        final SingleDataDTO request = new SingleDataDTO("data", Arrays.asList("value"));
                
            // Configure ItemTMenuValidityMapper.getTimeAndTypeForSyn(...).
                                                        final ItemTemplateExecuteTimeQuery itemTemplateExecuteTimeQuery = new ItemTemplateExecuteTimeQuery();
                itemTemplateExecuteTimeQuery.setGuid("61d23860-fb0f-4714-bf4a-0fb7dbe84505");
                itemTemplateExecuteTimeQuery.setDifference(0);
                itemTemplateExecuteTimeQuery.setNow(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
                itemTemplateExecuteTimeQuery.setWeeks("weeks");
                itemTemplateExecuteTimeQuery.setTimes("times");
        final List<ItemTemplateExecuteTimeQuery> itemTemplateExecuteTimeQueries = Arrays.asList(itemTemplateExecuteTimeQuery);
            when( mockItemTMenuValidityMapper .getTimeAndTypeForSyn(new SingleDataDTO("data", Arrays.asList("value")))).thenReturn(itemTemplateExecuteTimeQueries);

        // Configure ItemHelper.templateExecuteHander(...).
                                                        final ItemTemplateExecuteTimeQuery itemTemplateExecuteTimeQuery1 = new ItemTemplateExecuteTimeQuery();
                itemTemplateExecuteTimeQuery1.setGuid("61d23860-fb0f-4714-bf4a-0fb7dbe84505");
                itemTemplateExecuteTimeQuery1.setDifference(0);
                itemTemplateExecuteTimeQuery1.setNow(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
                itemTemplateExecuteTimeQuery1.setWeeks("weeks");
                itemTemplateExecuteTimeQuery1.setTimes("times");
        final List<ItemTemplateExecuteTimeQuery> list = Arrays.asList(itemTemplateExecuteTimeQuery1);
        when( mockItemHelper .templateExecuteHander(list)).thenReturn( Collections.emptyList() );

    // Run the test
 final List<Long> result =  itemTMenuValidityServiceImplUnderTest.getTimeAndTypeForSyn(request);

        // Verify the results
 assertThat(result).isEqualTo(Collections.emptyList() ) ;
    }
                
    @Test
    public void testGetNowMenuExecuteTimes() throws Exception {
    // Setup
                                                                        final ItemTemplateExecuteTimeQuery itemTemplateExecuteTimeQuery = new ItemTemplateExecuteTimeQuery();
                itemTemplateExecuteTimeQuery.setGuid("61d23860-fb0f-4714-bf4a-0fb7dbe84505");
                itemTemplateExecuteTimeQuery.setDifference(0);
                itemTemplateExecuteTimeQuery.setNow(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
                itemTemplateExecuteTimeQuery.setWeeks("weeks");
                itemTemplateExecuteTimeQuery.setTimes("times");
        final List<ItemTemplateExecuteTimeQuery> expectedResult = Arrays.asList(itemTemplateExecuteTimeQuery);
                
            // Configure ItemTMenuValidityMapper.getNowMenuExecuteTimes(...).
                                                        final ItemTemplateExecuteTimeQuery itemTemplateExecuteTimeQuery1 = new ItemTemplateExecuteTimeQuery();
                itemTemplateExecuteTimeQuery1.setGuid("61d23860-fb0f-4714-bf4a-0fb7dbe84505");
                itemTemplateExecuteTimeQuery1.setDifference(0);
                itemTemplateExecuteTimeQuery1.setNow(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
                itemTemplateExecuteTimeQuery1.setWeeks("weeks");
                itemTemplateExecuteTimeQuery1.setTimes("times");
        final List<ItemTemplateExecuteTimeQuery> itemTemplateExecuteTimeQueries = Arrays.asList(itemTemplateExecuteTimeQuery1);
            when( mockItemTMenuValidityMapper .getNowMenuExecuteTimes("storeGuid")).thenReturn(itemTemplateExecuteTimeQueries);

    // Run the test
 final List<ItemTemplateExecuteTimeQuery> result =  itemTMenuValidityServiceImplUnderTest.getNowMenuExecuteTimes("storeGuid");

        // Verify the results
 assertThat(result).isEqualTo(expectedResult ) ;
    }
                                                                                                
    @Test
    public void testGetNowMenuExecuteTimes_ItemTMenuValidityMapperReturnsNoItems() throws Exception {
    // Setup
                        when( mockItemTMenuValidityMapper .getNowMenuExecuteTimes("storeGuid")).thenReturn( Collections.emptyList() );

    // Run the test
 final List<ItemTemplateExecuteTimeQuery> result =  itemTMenuValidityServiceImplUnderTest.getNowMenuExecuteTimes("storeGuid");

        // Verify the results
 assertThat(result).isEqualTo(Collections.emptyList() ) ;
    }
    @Test
    public void testRemoveMenuTime() throws Exception {
                 assertThat( itemTMenuValidityServiceImplUnderTest.removeMenuTime("menuGuid") ).isFalse() ;
                                    }
}

