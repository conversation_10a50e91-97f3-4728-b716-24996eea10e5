

package com.holderzone.saas.store.item.service.impl;

import static org.mockito.MockitoAnnotations.initMocks;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import java.math.BigDecimal;import java.time.LocalDateTime;import java.util.Arrays;import java.util.Collections;import java.util.List;import java.util.concurrent.Callable;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.assertj.core.api.Assertions.within;
    import static org.mockito.ArgumentMatchers.any;
    import static org.mockito.ArgumentMatchers.anyInt;
    import static org.mockito.ArgumentMatchers.anyString;
    import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import com.holderzone.saas.store.dto.common.SingleDataDTO;import com.holderzone.saas.store.dto.item.req.*;import com.holderzone.saas.store.dto.item.resp.ItemTemplateMenusRespDTO;import com.holderzone.saas.store.item.entity.query.ItemTemplatesQuery;import com.holderzone.saas.store.item.helper.ItemHelper;import com.holderzone.saas.store.item.mapper.ItemTMenuMapper;import com.holderzone.saas.store.item.service.IItemTMenuSubitemService;import com.holderzone.saas.store.item.service.IItemTMenuValidityService;import com.holderzone.saas.store.item.service.IItemTemplateService;import com.holderzone.saas.store.item.util.DynamicHelper;import org.mockito.junit.MockitoJUnitRunner;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.InjectMocks;
import org.mockito.stubbing.Answer;import org.springframework.data.redis.core.RedisTemplate;

@RunWith(MockitoJUnitRunner.class)
public class ItemTMenuServiceImplTest {

            @Mock
        private DynamicHelper mockDynamicHelper;
            @Mock
        private IItemTMenuSubitemService mockItemTMenuSubitemService;
            @Mock
        private IItemTMenuValidityService mockItemTMenuValidityService;
            @Mock
        private ItemHelper mockItemHelper;
            @Mock
        private RedisTemplate mockRedisTemplate;
            @Mock
        private IItemTemplateService mockIItemTemplateService;
            @Mock
        private ItemTMenuMapper mockItemTMenuMapper;

    private ItemTMenuServiceImpl itemTMenuServiceImplUnderTest;

@Before
public void setUp() throws Exception {
            itemTMenuServiceImplUnderTest = new ItemTMenuServiceImpl(mockDynamicHelper,mockItemTMenuSubitemService,mockItemTMenuValidityService,mockItemHelper,mockRedisTemplate,mockIItemTemplateService,mockItemTMenuMapper) ;
}
                
    @Test
    public void testSaveOrUpdate() throws Exception {
    // Setup
                        final ItemTMenuReqDTO request = new ItemTMenuReqDTO("e7f9e42e-fe11-4b98-98db-4ab07a2596bd", "templateGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0);

    // Run the test
 final Boolean result =  itemTMenuServiceImplUnderTest.saveOrUpdate(request);

        // Verify the results
 assertThat(result).isFalse() ;
    }
                                        
    @Test
    public void testSaveItemMenu() throws Exception {
    // Setup
                        final ItemTemplateMenuSubitemReqDTO request = new ItemTemplateMenuSubitemReqDTO();
                request.setPeriodicMode(0);
                request.setIsItFullTime(0);
                request.setTemplateGuid("templateGuid");
                request.setMenuGuid("86919f8b-7b9f-4593-92e0-85ffd5895029");
                                            final ItemMenuSubItemReqDTO itemMenuSubItemReqDTO = new ItemMenuSubItemReqDTO();
                request.setItemMenuSubItemReqDTOS(Arrays.asList(itemMenuSubItemReqDTO));
                final ItmeTemplateMenuValidityReqDTO itmeTemplateMenuValidityReqDTO = new ItmeTemplateMenuValidityReqDTO();
                                                request.setItmeTemplateMenuValidityReqDTO(itmeTemplateMenuValidityReqDTO);
                request.setIsDelete(0);
 
                             when( mockIItemTemplateService .getStoreGuidByCode(0,"templateGuid")).thenReturn( "storeGuid" );
                            when( mockItemHelper .validateMenuTime(0,"templateGuid",new ItmeTemplateMenuValidityReqDTO("89da2a7a-06eb-4a72-b57e-413f70257ad8", "itemMenuGuid", Arrays.asList(0), 0, Arrays.asList(new ItemTemplateMenuTimeReqDTO("startTime", "endTime")), 0, 0),"86919f8b-7b9f-4593-92e0-85ffd5895029")).thenReturn( "result" );
                            when( mockItemTMenuValidityService .saveItemMenuValidity(new ItmeTemplateMenuValidityReqDTO("89da2a7a-06eb-4a72-b57e-413f70257ad8", "itemMenuGuid", Arrays.asList(0), 0, Arrays.asList(new ItemTemplateMenuTimeReqDTO("startTime", "endTime")), 0, 0),0,"86919f8b-7b9f-4593-92e0-85ffd5895029",0)).thenReturn( false );
                            when( mockItemTMenuSubitemService .saveItemMenuSubItem(Arrays.asList(new ItemMenuSubItemReqDTO("fba45820-1f1c-46dc-91b2-6467b3c5821e", "itemMenuGuid", "skuGuid", new BigDecimal("0.00"), 0)),"86919f8b-7b9f-4593-92e0-85ffd5895029")).thenReturn( false );

    // Run the test
 final Boolean result =  itemTMenuServiceImplUnderTest.saveItemMenu(request);

        // Verify the results
 assertThat(result).isFalse() ;
            verify( mockItemHelper ).pushMsg("storeGuid");
            verify( mockItemTMenuValidityService ).removeMenuTime("86919f8b-7b9f-4593-92e0-85ffd5895029");
            verify( mockItemTMenuSubitemService ).removeMenuSubItem("86919f8b-7b9f-4593-92e0-85ffd5895029");
    }
                                                                                                                                                                                                                                
    @Test
    public void testSaveItemMenu_ItemHelperValidateMenuTimeReturnsNull() throws Exception {
    // Setup
                        final ItemTemplateMenuSubitemReqDTO request = new ItemTemplateMenuSubitemReqDTO();
                request.setPeriodicMode(0);
                request.setIsItFullTime(0);
                request.setTemplateGuid("templateGuid");
                request.setMenuGuid("86919f8b-7b9f-4593-92e0-85ffd5895029");
                                            final ItemMenuSubItemReqDTO itemMenuSubItemReqDTO = new ItemMenuSubItemReqDTO();
                request.setItemMenuSubItemReqDTOS(Arrays.asList(itemMenuSubItemReqDTO));
                final ItmeTemplateMenuValidityReqDTO itmeTemplateMenuValidityReqDTO = new ItmeTemplateMenuValidityReqDTO();
                                                request.setItmeTemplateMenuValidityReqDTO(itmeTemplateMenuValidityReqDTO);
                request.setIsDelete(0);
 
         when( mockItemHelper .validateMenuTime(0,"templateGuid",new ItmeTemplateMenuValidityReqDTO("89da2a7a-06eb-4a72-b57e-413f70257ad8", "itemMenuGuid", Arrays.asList(0), 0, Arrays.asList(new ItemTemplateMenuTimeReqDTO("startTime", "endTime")), 0, 0),"86919f8b-7b9f-4593-92e0-85ffd5895029")).thenReturn( null );
                            when( mockIItemTemplateService .getStoreGuidByCode(0,"templateGuid")).thenReturn( "storeGuid" );
                            when( mockItemTMenuValidityService .saveItemMenuValidity(new ItmeTemplateMenuValidityReqDTO("89da2a7a-06eb-4a72-b57e-413f70257ad8", "itemMenuGuid", Arrays.asList(0), 0, Arrays.asList(new ItemTemplateMenuTimeReqDTO("startTime", "endTime")), 0, 0),0,"86919f8b-7b9f-4593-92e0-85ffd5895029",0)).thenReturn( false );
                            when( mockItemTMenuSubitemService .saveItemMenuSubItem(Arrays.asList(new ItemMenuSubItemReqDTO("fba45820-1f1c-46dc-91b2-6467b3c5821e", "itemMenuGuid", "skuGuid", new BigDecimal("0.00"), 0)),"86919f8b-7b9f-4593-92e0-85ffd5895029")).thenReturn( false );

    // Run the test
 final Boolean result =  itemTMenuServiceImplUnderTest.saveItemMenu(request);

        // Verify the results
 assertThat(result).isFalse() ;
            verify( mockItemHelper ).pushMsg("storeGuid");
    }
                                                                                
    @Test
    public void testGetItemTemplateMenus() throws Exception {
    // Setup
                        final SingleDataDTO request = new SingleDataDTO("data", Arrays.asList("value"));
        final List<ItemTemplateMenusRespDTO> expectedResult = Arrays.asList(new ItemTemplateMenusRespDTO("templateGuid", "a1193ef8-fb66-45c1-86d1-600c0bf902be", Arrays.asList("value"), "isDelate"));
                
            // Configure ItemTMenuMapper.getItemTemplateMenus(...).
                                                        final ItemTemplatesQuery itemTemplatesQuery = new ItemTemplatesQuery();
                itemTemplatesQuery.setPeriodicMode(0);
                itemTemplatesQuery.setGuid("guid");
                itemTemplatesQuery.setMenuGuid("menuGuid");
                itemTemplatesQuery.setWeeks("weeks");
                itemTemplatesQuery.setTimes("times");
        final List<ItemTemplatesQuery> itemTemplatesQueries = Arrays.asList(itemTemplatesQuery);
            when( mockItemTMenuMapper .getItemTemplateMenus(new SingleDataDTO("data", Arrays.asList("value")))).thenReturn(itemTemplatesQueries);

    // Run the test
 final List<ItemTemplateMenusRespDTO> result =  itemTMenuServiceImplUnderTest.getItemTemplateMenus(request);

        // Verify the results
 assertThat(result).isEqualTo(expectedResult ) ;
    }
                                                                                                
    @Test
    public void testGetItemTemplateMenus_ItemTMenuMapperReturnsNoItems() throws Exception {
    // Setup
                        final SingleDataDTO request = new SingleDataDTO("data", Arrays.asList("value"));
        when( mockItemTMenuMapper .getItemTemplateMenus(new SingleDataDTO("data", Arrays.asList("value")))).thenReturn( Collections.emptyList() );

    // Run the test
 final List<ItemTemplateMenusRespDTO> result =  itemTMenuServiceImplUnderTest.getItemTemplateMenus(request);

        // Verify the results
 assertThat(result).isEqualTo(Collections.emptyList() ) ;
    }
}

