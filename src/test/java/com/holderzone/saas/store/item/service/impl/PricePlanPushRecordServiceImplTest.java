

package com.holderzone.saas.store.item.service.impl;

import static org.mockito.MockitoAnnotations.initMocks;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import java.time.LocalDateTime;import java.util.Arrays;import java.util.Collections;import java.util.List;import java.util.concurrent.Callable;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.assertj.core.api.Assertions.within;
    import static org.mockito.ArgumentMatchers.any;
    import static org.mockito.ArgumentMatchers.anyInt;
    import static org.mockito.ArgumentMatchers.anyString;
    import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import com.holderzone.saas.store.item.dto.PushRecordStatusUpdateReqDTO;import com.holderzone.saas.store.item.entity.domain.PricePlanPushRecordDO;import com.holderzone.saas.store.item.mapper.PricePlanPushRecordMapper;import org.mockito.junit.MockitoJUnitRunner;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.InjectMocks;
import org.mockito.stubbing.Answer;

@RunWith(MockitoJUnitRunner.class)
public class PricePlanPushRecordServiceImplTest {

            @Mock
        private PricePlanPushRecordMapper mockPushRecordMapper;

 @InjectMocks     private PricePlanPushRecordServiceImpl pricePlanPushRecordServiceImplUnderTest;

    @Test
    public void testGetPushRecords() throws Exception {
    // Setup
                                                                        final PricePlanPushRecordDO pricePlanPushRecordDO = new PricePlanPushRecordDO();
                pricePlanPushRecordDO.setId(0L);
                pricePlanPushRecordDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
                pricePlanPushRecordDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
                pricePlanPushRecordDO.setIsDelete(0);
                pricePlanPushRecordDO.setGuid("0587b96b-b759-403f-9772-264f4966b94a");
        final List<PricePlanPushRecordDO> expectedResult = Arrays.asList(pricePlanPushRecordDO);
                
            // Configure PricePlanPushRecordMapper.getPushRecords(...).
                                                        final PricePlanPushRecordDO pricePlanPushRecordDO1 = new PricePlanPushRecordDO();
                pricePlanPushRecordDO1.setId(0L);
                pricePlanPushRecordDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
                pricePlanPushRecordDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
                pricePlanPushRecordDO1.setIsDelete(0);
                pricePlanPushRecordDO1.setGuid("0587b96b-b759-403f-9772-264f4966b94a");
        final List<PricePlanPushRecordDO> pricePlanPushRecordDOS = Arrays.asList(pricePlanPushRecordDO1);
            when( mockPushRecordMapper .getPushRecords("planGuid")).thenReturn(pricePlanPushRecordDOS);

    // Run the test
 final List<PricePlanPushRecordDO> result =  pricePlanPushRecordServiceImplUnderTest.getPushRecords("planGuid");

        // Verify the results
 assertThat(result).isEqualTo(expectedResult ) ;
    }
                                                                                                
    @Test
    public void testGetPushRecords_PricePlanPushRecordMapperReturnsNoItems() throws Exception {
    // Setup
                        when( mockPushRecordMapper .getPushRecords("planGuid")).thenReturn( Collections.emptyList() );

    // Run the test
 final List<PricePlanPushRecordDO> result =  pricePlanPushRecordServiceImplUnderTest.getPushRecords("planGuid");

        // Verify the results
 assertThat(result).isEqualTo(Collections.emptyList() ) ;
    }
                
    @Test
    public void testUpdatePushStatus() throws Exception {
    // Setup
                        final PushRecordStatusUpdateReqDTO reqDTO = new PushRecordStatusUpdateReqDTO();
                reqDTO.setPlanGuid("planGuid");
                reqDTO.setStoreGuid("storeGuid");
                reqDTO.setItemGuid("itemGuid");
                reqDTO.setStatus(0);
                reqDTO.setNewItemGuid("newItemGuid");

    // Run the test
 pricePlanPushRecordServiceImplUnderTest.updatePushStatus(reqDTO);

        // Verify the results
        // Confirm PricePlanPushRecordMapper.updatePushStatus(...).
        final PushRecordStatusUpdateReqDTO reqDTO1 = new PushRecordStatusUpdateReqDTO();
                reqDTO1.setPlanGuid("planGuid");
                reqDTO1.setStoreGuid("storeGuid");
                reqDTO1.setItemGuid("itemGuid");
                reqDTO1.setStatus(0);
                reqDTO1.setNewItemGuid("newItemGuid");
            verify( mockPushRecordMapper ).updatePushStatus(reqDTO1);
    }
                                                                        
    @Test
    public void testDeletePushRecords() throws Exception {
    // Setup
    // Run the test
 pricePlanPushRecordServiceImplUnderTest.deletePushRecords("planGuid");

        // Verify the results
            verify( mockPushRecordMapper ).deletePushRecords("planGuid");
    }
                                                        }

