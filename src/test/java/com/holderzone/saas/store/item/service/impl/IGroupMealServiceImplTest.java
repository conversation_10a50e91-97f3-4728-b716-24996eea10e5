package com.holderzone.saas.store.item.service.impl;

import com.holderzone.saas.store.dto.item.req.GroupMealSkuSaveReqDTO;
import com.holderzone.saas.store.item.entity.bo.ItemRelateGroupMealBO;
import com.holderzone.saas.store.item.entity.domain.SkuDO;
import com.holderzone.saas.store.item.mapper.GroupMealMapper;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.RedisTemplate;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class IGroupMealServiceImplTest {

    @Mock
    private GroupMealMapper mockGroupMealMapper;
    @Mock
    private RedisTemplate mockRedisTemplate;

    private IGroupMealServiceImpl iGroupMealServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        iGroupMealServiceImplUnderTest = new IGroupMealServiceImpl(mockGroupMealMapper, mockRedisTemplate);
    }

    @Test
    public void testSaveOrUpdateGroupMeal() {
        // Setup
        final List<GroupMealSkuSaveReqDTO> skuList = Arrays.asList(
                new GroupMealSkuSaveReqDTO("ffc549d9-0a0c-4e0e-9934-134fc55feba5", "itemGuid", "skuGuid",
                        new BigDecimal("0.00"), "storeGuid", 0));

        // Run the test
        final Boolean result = iGroupMealServiceImplUnderTest.saveOrUpdateGroupMeal(skuList,
                "96750134-db10-4cb7-af84-ca03b2119006");

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testMapItem2RelateGroupMealPakNum() {
        // Setup
        final SkuDO skuDO = new SkuDO();
        skuDO.setId(0L);
        skuDO.setIsDelete(0);
        skuDO.setGuid("3d5db47f-267b-4951-9baf-2a8e3af1f32f");
        skuDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        skuDO.setBrandGuid("brandGuid");
        final List<SkuDO> skuDOList = Arrays.asList(skuDO);
        final ItemRelateGroupMealBO itemRelateGroupMealBO = new ItemRelateGroupMealBO();
        itemRelateGroupMealBO.setItemGuid("itemGuid");
        itemRelateGroupMealBO.setNum(0);
        final List<ItemRelateGroupMealBO> expectedResult = Arrays.asList(itemRelateGroupMealBO);

        // Configure GroupMealMapper.mapItem2RelateGroupMealPakNum(...).
        final ItemRelateGroupMealBO itemRelateGroupMealBO1 = new ItemRelateGroupMealBO();
        itemRelateGroupMealBO1.setItemGuid("itemGuid");
        itemRelateGroupMealBO1.setNum(0);
        final List<ItemRelateGroupMealBO> itemRelateGroupMealBOS = Arrays.asList(itemRelateGroupMealBO1);
        when(mockGroupMealMapper.mapItem2RelateGroupMealPakNum(Arrays.asList("value")))
                .thenReturn(itemRelateGroupMealBOS);

        // Run the test
        final List<ItemRelateGroupMealBO> result = iGroupMealServiceImplUnderTest.mapItem2RelateGroupMealPakNum(
                skuDOList);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testMapItem2RelateGroupMealPakNum_GroupMealMapperReturnsNoItems() {
        // Setup
        final SkuDO skuDO = new SkuDO();
        skuDO.setId(0L);
        skuDO.setIsDelete(0);
        skuDO.setGuid("3d5db47f-267b-4951-9baf-2a8e3af1f32f");
        skuDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        skuDO.setBrandGuid("brandGuid");
        final List<SkuDO> skuDOList = Arrays.asList(skuDO);
        when(mockGroupMealMapper.mapItem2RelateGroupMealPakNum(Arrays.asList("value")))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<ItemRelateGroupMealBO> result = iGroupMealServiceImplUnderTest.mapItem2RelateGroupMealPakNum(
                skuDOList);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }
}
