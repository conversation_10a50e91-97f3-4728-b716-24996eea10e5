

package com.holderzone.saas.store.item.service.impl;

import static org.mockito.MockitoAnnotations.initMocks;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import java.math.BigDecimal;import java.util.Arrays;import java.util.Collections;import java.util.List;import java.util.concurrent.Callable;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.assertj.core.api.Assertions.within;
    import static org.mockito.ArgumentMatchers.any;
    import static org.mockito.ArgumentMatchers.anyInt;
    import static org.mockito.ArgumentMatchers.anyString;
    import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import com.holderzone.saas.store.dto.common.SingleDataDTO;import com.holderzone.saas.store.dto.item.req.ItemMenuSubItemReqDTO;import com.holderzone.saas.store.dto.item.req.ItemTemplateMenuDetailsReqDTO;import com.holderzone.saas.store.dto.item.resp.ItemTemplateExecuteTimeSlotRespDTO;import com.holderzone.saas.store.dto.item.resp.ItemTemplateMenuDetailRespDTO;import com.holderzone.saas.store.dto.item.resp.ItemTemplateMenuSubItemDetailRespDTO;import com.holderzone.saas.store.item.entity.query.ItemTemplateMenuDetailQuery;import com.holderzone.saas.store.item.entity.query.ItemTemplateMenuSubItemDetailQuery;import com.holderzone.saas.store.item.helper.ItemHelper;import com.holderzone.saas.store.item.mapper.ItemTMenuSubitemMapper;import com.holderzone.saas.store.item.service.IItemTMenuService;import com.holderzone.saas.store.item.util.DynamicHelper;import org.mockito.junit.MockitoJUnitRunner;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.InjectMocks;
import org.mockito.stubbing.Answer;import org.springframework.data.redis.core.RedisTemplate;

@RunWith(MockitoJUnitRunner.class)
public class ItemTMenuSubitemServiceImplTest {

            @Mock
        private ItemTMenuSubitemMapper mockItemTMenuSubitemMapper;
            @Mock
        private DynamicHelper mockDynamicHelper;
            @Mock
        private ItemHelper mockItemHelper;
            @Mock
        private IItemTMenuService mockIItemTMenuService;
            @Mock
        private RedisTemplate mockRedisTemplate;

    private ItemTMenuSubitemServiceImpl itemTMenuSubitemServiceImplUnderTest;

@Before
public void setUp() throws Exception {
            itemTMenuSubitemServiceImplUnderTest = new ItemTMenuSubitemServiceImpl(mockItemTMenuSubitemMapper,mockDynamicHelper,mockItemHelper,mockIItemTMenuService,mockRedisTemplate) ;
}
                
    @Test
    public void testSaveItemMenuSubItem() throws Exception {
    // Setup
                        final List<ItemMenuSubItemReqDTO> request = Arrays.asList(new ItemMenuSubItemReqDTO("a26db289-d7b4-4dda-84f0-3f97df1a6293", "itemMenuGuid", "skuGuid", new BigDecimal("0.00"), 0));

    // Run the test
 final Boolean result =  itemTMenuSubitemServiceImplUnderTest.saveItemMenuSubItem(request,"menuGuid");

        // Verify the results
 assertThat(result).isFalse() ;
    }
                                        
    @Test
    public void testGetItemTemplateMenuDetail() throws Exception {
    // Setup
                        final ItemTemplateMenuDetailsReqDTO request = new ItemTemplateMenuDetailsReqDTO();
                request.setGuid("9817c420-647d-4253-be8b-b4d3c1faef01");
                request.setTypeGuid("typeGuid");
                request.setKeywords("keywords");
 
         final ItemTemplateMenuDetailRespDTO expectedResult = new ItemTemplateMenuDetailRespDTO("menuGuid", 0, "timeGuid", 0, Arrays.asList(0), Arrays.asList(new ItemTemplateExecuteTimeSlotRespDTO("startTime", "endTime")), Arrays.asList(new ItemTemplateMenuSubItemDetailRespDTO("c4e9ccd4-43fe-422c-b384-77a78a89d129", "name", "skuGuid", "skuName", "unit", "typeGuid", "typeName", new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"))));
                
            // Configure ItemTMenuSubitemMapper.getItemTemplateMenuDetail(...).
        final ItemTemplateMenuDetailQuery itemTemplateMenuDetailQuery = new ItemTemplateMenuDetailQuery();
                itemTemplateMenuDetailQuery.setGuid("guid");
                itemTemplateMenuDetailQuery.setMenuGuid("menuGuid");
                itemTemplateMenuDetailQuery.setPeriodicMode(0);
                itemTemplateMenuDetailQuery.setIsItFullTime(0);
                itemTemplateMenuDetailQuery.setWeeks("weeks");
                itemTemplateMenuDetailQuery.setTimes("times");
        final ItemTemplateMenuDetailsReqDTO request1 = new ItemTemplateMenuDetailsReqDTO();
                request1.setGuid("9817c420-647d-4253-be8b-b4d3c1faef01");
                request1.setTypeGuid("typeGuid");
                request1.setKeywords("keywords");
            when( mockItemTMenuSubitemMapper .getItemTemplateMenuDetail(request1)).thenReturn(itemTemplateMenuDetailQuery);

            // Configure ItemTMenuSubitemMapper.getItemTemplateMenuSubItemDetailQuery(...).
                                                        final ItemTemplateMenuSubItemDetailQuery itemTemplateMenuSubItemDetailQuery = new ItemTemplateMenuSubItemDetailQuery();
                itemTemplateMenuSubItemDetailQuery.setGuid("6ee6a5b1-695c-43e2-bf54-27ddd0799246");
                itemTemplateMenuSubItemDetailQuery.setMenuGuid("menuGuid");
                itemTemplateMenuSubItemDetailQuery.setName("name");
                itemTemplateMenuSubItemDetailQuery.setSkuGuid("skuGuid");
                itemTemplateMenuSubItemDetailQuery.setSkuName("skuName");
        final List<ItemTemplateMenuSubItemDetailQuery> itemTemplateMenuSubItemDetailQueries = Arrays.asList(itemTemplateMenuSubItemDetailQuery);
        final ItemTemplateMenuDetailsReqDTO request2 = new ItemTemplateMenuDetailsReqDTO();
                request2.setGuid("9817c420-647d-4253-be8b-b4d3c1faef01");
                request2.setTypeGuid("typeGuid");
                request2.setKeywords("keywords");
            when( mockItemTMenuSubitemMapper .getItemTemplateMenuSubItemDetailQuery(request2)).thenReturn(itemTemplateMenuSubItemDetailQueries);

    // Run the test
 final ItemTemplateMenuDetailRespDTO result =  itemTMenuSubitemServiceImplUnderTest.getItemTemplateMenuDetail(request);

        // Verify the results
 assertThat(result).isEqualTo(expectedResult ) ;
    }
                                                                                                                                
    @Test
    public void testGetItemTemplateMenuDetail_ItemTMenuSubitemMapperGetItemTemplateMenuSubItemDetailQueryReturnsNoItems() throws Exception {
    // Setup
                        final ItemTemplateMenuDetailsReqDTO request = new ItemTemplateMenuDetailsReqDTO();
                request.setGuid("9817c420-647d-4253-be8b-b4d3c1faef01");
                request.setTypeGuid("typeGuid");
                request.setKeywords("keywords");
 
         final ItemTemplateMenuDetailRespDTO expectedResult = new ItemTemplateMenuDetailRespDTO("menuGuid", 0, "timeGuid", 0, Arrays.asList(0), Arrays.asList(new ItemTemplateExecuteTimeSlotRespDTO("startTime", "endTime")), Arrays.asList(new ItemTemplateMenuSubItemDetailRespDTO("c4e9ccd4-43fe-422c-b384-77a78a89d129", "name", "skuGuid", "skuName", "unit", "typeGuid", "typeName", new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"))));
                
            // Configure ItemTMenuSubitemMapper.getItemTemplateMenuDetail(...).
        final ItemTemplateMenuDetailQuery itemTemplateMenuDetailQuery = new ItemTemplateMenuDetailQuery();
                itemTemplateMenuDetailQuery.setGuid("guid");
                itemTemplateMenuDetailQuery.setMenuGuid("menuGuid");
                itemTemplateMenuDetailQuery.setPeriodicMode(0);
                itemTemplateMenuDetailQuery.setIsItFullTime(0);
                itemTemplateMenuDetailQuery.setWeeks("weeks");
                itemTemplateMenuDetailQuery.setTimes("times");
        final ItemTemplateMenuDetailsReqDTO request1 = new ItemTemplateMenuDetailsReqDTO();
                request1.setGuid("9817c420-647d-4253-be8b-b4d3c1faef01");
                request1.setTypeGuid("typeGuid");
                request1.setKeywords("keywords");
            when( mockItemTMenuSubitemMapper .getItemTemplateMenuDetail(request1)).thenReturn(itemTemplateMenuDetailQuery);

        // Configure ItemTMenuSubitemMapper.getItemTemplateMenuSubItemDetailQuery(...).
        final ItemTemplateMenuDetailsReqDTO request2 = new ItemTemplateMenuDetailsReqDTO();
                request2.setGuid("9817c420-647d-4253-be8b-b4d3c1faef01");
                request2.setTypeGuid("typeGuid");
                request2.setKeywords("keywords");
        when( mockItemTMenuSubitemMapper .getItemTemplateMenuSubItemDetailQuery(request2)).thenReturn( Collections.emptyList() );

    // Run the test
 final ItemTemplateMenuDetailRespDTO result =  itemTMenuSubitemServiceImplUnderTest.getItemTemplateMenuDetail(request);

        // Verify the results
 assertThat(result).isEqualTo(expectedResult ) ;
    }
                
    @Test
    public void testMenuSubItemBatchRemove() throws Exception {
    // Setup
                        final SingleDataDTO request = new SingleDataDTO("data", Arrays.asList("value"));
                            when( mockItemTMenuSubitemMapper .getStoreGuid("guid")).thenReturn( "storeGuid" );

    // Run the test
 final Boolean result =  itemTMenuSubitemServiceImplUnderTest.menuSubItemBatchRemove(request);

        // Verify the results
 assertThat(result).isFalse() ;
            verify( mockItemHelper ).pushMsg("storeGuid");
    }
                                                                                                        
    @Test
    public void testGetNowMeunSubItemForSyn() throws Exception {
    // Setup
                        final List<ItemTemplateMenuSubItemDetailRespDTO> expectedResult = Arrays.asList(new ItemTemplateMenuSubItemDetailRespDTO("c4e9ccd4-43fe-422c-b384-77a78a89d129", "name", "skuGuid", "skuName", "unit", "typeGuid", "typeName", new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00")));

    // Run the test
 final List<ItemTemplateMenuSubItemDetailRespDTO> result =  itemTMenuSubitemServiceImplUnderTest.getNowMeunSubItemForSyn("menuGuid");

        // Verify the results
 assertThat(result).isEqualTo(expectedResult ) ;
    }
                            @Test
    public void testRemoveMenuSubItem() throws Exception {
                 assertThat( itemTMenuSubitemServiceImplUnderTest.removeMenuSubItem("menuGuid") ).isFalse() ;
                                    }
}

