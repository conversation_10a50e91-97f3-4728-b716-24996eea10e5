package com.holderzone.saas.store.item.service.impl;

import com.holderzone.saas.store.dto.item.resp.SkuInfoRespDTO;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.store.store.StoreProductDTO;
import com.holderzone.saas.store.item.entity.domain.EstimateOpLogDO;
import com.holderzone.saas.store.item.entity.domain.EstimateSellLogDO;
import com.holderzone.saas.store.item.service.IEstimateSellLogService;
import com.holderzone.saas.store.item.service.ISkuService;
import com.holderzone.saas.store.item.service.rpc.OrganizationService;
import com.holderzone.saas.store.item.util.DynamicHelper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class IEstimateOpLogServiceImplTest {

    @Mock
    private OrganizationService mockOrganizationService;
    @Mock
    private ISkuService mockSkuService;
    @Mock
    private DynamicHelper mockDynamicHelper;
    @Mock
    private IEstimateSellLogService mockEstimateSellLogService;

    @InjectMocks
    private IEstimateOpLogServiceImpl iEstimateOpLogServiceImplUnderTest;

    @Test
    public void testSaveOpLog() {
        // Setup
        final List<EstimateOpLogDO> opLogList = Arrays.asList(EstimateOpLogDO.builder()
                .guid("69e01a89-aa92-447f-9dfb-5fcde451e784")
                .storeGuid("storeGuid")
                .storeName("storeName")
                .businessStart(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .itemGuid("itemGuid")
                .itemName("itemName")
                .skuGuid("skuGuid")
                .skuName("skuName")
                .params("params")
                .opType("opType")
                .build());

        // Configure OrganizationService.queryStoreByIdList(...).
        final List<StoreDTO> storeDTOS = Arrays.asList(
                new StoreDTO("471b363f-f532-4523-a093-7c18cfaa978d", "code", "name", "belongBrandGuid",
                        "belongBrandName", "parentIds", LocalTime.of(0, 0, 0), LocalTime.of(0, 0, 0), "contactName",
                        "contactTel", "provinceCode", "provinceName", "cityCode", "cityName", "countyCode",
                        "countyName", "addressDetail", "longitude", "latitude", false, false, "photos",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "createUserGuid",
                        "modifiedUserGuid", Arrays.asList(
                        new StoreProductDTO("productGuid", "productName", 0, LocalDate.of(2020, 1, 1),
                                LocalDate.of(2020, 1, 1), LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, 0)),
                        Arrays.asList(new BrandDTO("664f2556-af80-49f0-9245-84b05a60adcd",
                                "e6f9bd3b-2827-487c-8385-4d7295495975", "name", "description", "logoUrl", false, false,
                                "createUserGuid", "modifiedUserGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "mchntTypeCode", Arrays.asList(), 0, 0, 0, 0, false)),
                        "mchntTypeCode", "logoUrl", "openingHours", 0, 0, 0, 0, "storeNotification", "storeDoorPhoto",
                        0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, 0, 0,
                        LocalDate.of(2020, 1, 1)));
        when(mockOrganizationService.queryStoreByIdList(Arrays.asList("value"))).thenReturn(storeDTOS);

        // Configure ISkuService.listSkuInfoByRecipeMode(...).
        final SkuInfoRespDTO skuInfoRespDTO = new SkuInfoRespDTO();
        skuInfoRespDTO.setId(0L);
        skuInfoRespDTO.setItemGuid("itemGuid");
        skuInfoRespDTO.setSkuGuid("skuGuid");
        skuInfoRespDTO.setName("skuName");
        skuInfoRespDTO.setItemName("itemName");
        final List<SkuInfoRespDTO> skuInfoRespDTOS = Arrays.asList(skuInfoRespDTO);
        when(mockSkuService.listSkuInfoByRecipeMode("storeGuid", Arrays.asList("value"))).thenReturn(skuInfoRespDTOS);

        // Run the test
        iEstimateOpLogServiceImplUnderTest.saveOpLog(opLogList, "enterpriseGuid");

        // Verify the results
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
        verify(mockEstimateSellLogService).saveSellLog(Arrays.asList(EstimateSellLogDO.builder()
                .sell(0)
                .build()), "enterpriseGuid");
    }

    @Test
    public void testSaveOpLog_OrganizationServiceReturnsNoItems() {
        // Setup
        final List<EstimateOpLogDO> opLogList = Arrays.asList(EstimateOpLogDO.builder()
                .guid("69e01a89-aa92-447f-9dfb-5fcde451e784")
                .storeGuid("storeGuid")
                .storeName("storeName")
                .businessStart(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .itemGuid("itemGuid")
                .itemName("itemName")
                .skuGuid("skuGuid")
                .skuName("skuName")
                .params("params")
                .opType("opType")
                .build());
        when(mockOrganizationService.queryStoreByIdList(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        iEstimateOpLogServiceImplUnderTest.saveOpLog(opLogList, "enterpriseGuid");

        // Verify the results
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
    }

    @Test
    public void testSaveOpLog_ISkuServiceReturnsNoItems() {
        // Setup
        final List<EstimateOpLogDO> opLogList = Arrays.asList(EstimateOpLogDO.builder()
                .guid("69e01a89-aa92-447f-9dfb-5fcde451e784")
                .storeGuid("storeGuid")
                .storeName("storeName")
                .businessStart(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .itemGuid("itemGuid")
                .itemName("itemName")
                .skuGuid("skuGuid")
                .skuName("skuName")
                .params("params")
                .opType("opType")
                .build());

        // Configure OrganizationService.queryStoreByIdList(...).
        final List<StoreDTO> storeDTOS = Arrays.asList(
                new StoreDTO("471b363f-f532-4523-a093-7c18cfaa978d", "code", "name", "belongBrandGuid",
                        "belongBrandName", "parentIds", LocalTime.of(0, 0, 0), LocalTime.of(0, 0, 0), "contactName",
                        "contactTel", "provinceCode", "provinceName", "cityCode", "cityName", "countyCode",
                        "countyName", "addressDetail", "longitude", "latitude", false, false, "photos",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "createUserGuid",
                        "modifiedUserGuid", Arrays.asList(
                        new StoreProductDTO("productGuid", "productName", 0, LocalDate.of(2020, 1, 1),
                                LocalDate.of(2020, 1, 1), LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, 0)),
                        Arrays.asList(new BrandDTO("664f2556-af80-49f0-9245-84b05a60adcd",
                                "e6f9bd3b-2827-487c-8385-4d7295495975", "name", "description", "logoUrl", false, false,
                                "createUserGuid", "modifiedUserGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "mchntTypeCode", Arrays.asList(), 0, 0, 0, 0, false)),
                        "mchntTypeCode", "logoUrl", "openingHours", 0, 0, 0, 0, "storeNotification", "storeDoorPhoto",
                        0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, 0, 0,
                        LocalDate.of(2020, 1, 1)));
        when(mockOrganizationService.queryStoreByIdList(Arrays.asList("value"))).thenReturn(storeDTOS);

        when(mockSkuService.listSkuInfoByRecipeMode("storeGuid", Arrays.asList("value")))
                .thenReturn(Collections.emptyList());

        // Run the test
        iEstimateOpLogServiceImplUnderTest.saveOpLog(opLogList, "enterpriseGuid");

        // Verify the results
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
    }
}
