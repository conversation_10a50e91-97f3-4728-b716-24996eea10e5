package com.holderzone.saas.store.item;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.common.UserInfoDTO;
import com.holderzone.saas.store.dto.item.req.*;
import com.holderzone.saas.store.item.util.SpringContextUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.Arrays;
import java.util.List;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@SpringBootTest
public class ItemTemplateTests {
    @Autowired
    private WebApplicationContext wac;
    private MockMvc mockMvc;

    @Autowired
    private ApplicationContext applicationContext;
    @Before
    public void setup() {
        SpringContextUtils.getInstance().setCfgContext((ConfigurableApplicationContext) applicationContext);
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();   //构造MockMvc
    }


    @Test
    public  void  itemTemplateSave() throws Exception {
        ItemTemplateReqDTO itemTemplateReqDTO = new ItemTemplateReqDTO();
        itemTemplateReqDTO.setGuid("6539801325595131905");
        itemTemplateReqDTO.setDescription("测试详细描述");
        itemTemplateReqDTO.setEffectiveStartTime("2019-05-25 00:00:00");
        itemTemplateReqDTO.setEffectiveEndTime("2019-05-26 23:59:59");
        itemTemplateReqDTO.setTemplateName("国庆节套餐1");
        itemTemplateReqDTO.setIsItActivated(1);
        itemTemplateReqDTO.setPeriodicMode(1);
        itemTemplateReqDTO.setIsDelete(1);
        itemTemplateReqDTO.setStoreGuid("6506453252643487745");
        UserContext userContext = new UserContext();
        userContext.setEnterpriseGuid("6506431195651982337");
        String encode = URLEncoder.encode(JacksonUtils.writeValueAsString(userContext), "utf-8");
        MvcResult mvcResult = mockMvc.perform(post("/item_template/save").header("userInfo",encode).accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JacksonUtils.writeValueAsString(itemTemplateReqDTO)))
                .andExpect(status().is4xxClientError()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }


    @Test
    public  void  itemTemplateMenuSubItemSave() throws Exception {
        ItemTemplateMenuSubitemReqDTO itemTemplateMenuSubitemReqDTO = new ItemTemplateMenuSubitemReqDTO();
        ItemMenuSubItemReqDTO itemMenuSubItemReqDTO1 = ItemMenuSubItemReqDTO.builder()
                .guid("6541870967707338753")
                .price(new BigDecimal(10))
                .skuGuid("6507193825812581377").build();
        ItemMenuSubItemReqDTO itemMenuSubItemReqDTO2 = ItemMenuSubItemReqDTO.builder()
                .guid("6541548305019568129")
                .price(new BigDecimal(52))
                .isDelete(1)
                .skuGuid("6507166182064744449").build();
        ItemTemplateMenuTimeReqDTO  itemTemplateMenuTimeReqDTO1 = ItemTemplateMenuTimeReqDTO.builder()
                .startTime("16:12")
                .endTime("16:30").build();
        List<ItemTemplateMenuTimeReqDTO> times = Arrays.asList(itemTemplateMenuTimeReqDTO1);
        ItmeTemplateMenuValidityReqDTO itmeTemplateMenuValidityReqDTO1 = ItmeTemplateMenuValidityReqDTO.builder()
                .guid("6541937489075294209")
               /* .times(times)*/
                .weeks(Arrays.asList(0,1,3,2)).build();
        itemTemplateMenuSubitemReqDTO.setItemMenuSubItemReqDTOS(Arrays.asList(itemMenuSubItemReqDTO1,itemMenuSubItemReqDTO2));
        itemTemplateMenuSubitemReqDTO.setTemplateGuid("6541937247604783105");
        itemTemplateMenuSubitemReqDTO.setMenuGuid("6541871078522738689");
        itemTemplateMenuSubitemReqDTO.setItmeTemplateMenuValidityReqDTO(itmeTemplateMenuValidityReqDTO1);
        itemTemplateMenuSubitemReqDTO.setPeriodicMode(1);
        itemTemplateMenuSubitemReqDTO.setIsItFullTime(1);
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setEnterpriseGuid("6506431195651982337");
        String encode = URLEncoder.encode(JacksonUtils.writeValueAsString(userInfoDTO), "utf-8");
        MvcResult mvcResult = mockMvc.perform(post("/item_template/save_menu").header("userInfo",encode).accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JacksonUtils.writeValueAsString(itemTemplateMenuSubitemReqDTO)))
                .andExpect(status().is4xxClientError()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }

     @Test
    public  void  getItemTemplateExecuteTimes() throws Exception {
         SingleDataDTO dto = new SingleDataDTO();
         dto.setData("6537168585591359490");
        String guid = "6537168585591359490";
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setEnterpriseGuid("6506431195651982337");
        String encode = URLEncoder.encode(JacksonUtils.writeValueAsString(userInfoDTO), "utf-8");
        MvcResult mvcResult = mockMvc.perform(post("/item_template/get_execute_times").header("userInfo",encode).accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JacksonUtils.writeValueAsString(dto)))
                .andExpect(status().is4xxClientError()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }


    @Test
    public  void  getStoreItemTemplates() throws Exception {
        ItemTemplateSearchReqDTO dto = new ItemTemplateSearchReqDTO();
        dto.setStatus(3);
        dto.setStoreGuid("6506453252643487745");
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setEnterpriseGuid("6506431195651982337");
        String encode = URLEncoder.encode(JacksonUtils.writeValueAsString(userInfoDTO), "utf-8");
        MvcResult mvcResult = mockMvc.perform(post("/item_template/get_store_item_templates").header("userInfo",encode).accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JacksonUtils.writeValueAsString(dto)))
                .andExpect(status().is4xxClientError()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }


    @Test
    public  void  getTimeAndTypeForSyn() throws Exception {
        SingleDataDTO dto = new SingleDataDTO();
        dto.setData("6506453252643487745");
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setEnterpriseGuid("6506431195651982337");
        userInfoDTO.setStoreGuid("6506453252643487745");
        String encode = URLEncoder.encode(JacksonUtils.writeValueAsString(userInfoDTO), "utf-8");
        MvcResult mvcResult = mockMvc.perform(post("/item_template/get_item_template_execute_time").header("userInfo",encode).accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JacksonUtils.writeValueAsString(dto)))
                .andExpect(status().is4xxClientError()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }

    @Test
    public  void  getItemTemplateMenus() throws Exception {
        SingleDataDTO dto = new SingleDataDTO();
       dto.setData("6541487258787652609");
        //dto.setData("6539802319166337025");
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setEnterpriseGuid("6506431195651982337");
        String encode = URLEncoder.encode(JacksonUtils.writeValueAsString(userInfoDTO), "utf-8");
        MvcResult mvcResult = mockMvc.perform(post("/item_template/get_item_template_menus").header("userInfo",encode).accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JacksonUtils.writeValueAsString(dto)))
                .andExpect(status().is4xxClientError()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }


    @Test
    public  void  getItemTemplateMenuDetail() throws Exception {
        ItemTemplateMenuDetailsReqDTO dto = new ItemTemplateMenuDetailsReqDTO();
        dto.setGuid("6541937642032766977");
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setEnterpriseGuid("6506431195651982337");
        String encode = URLEncoder.encode(JacksonUtils.writeValueAsString(userInfoDTO), "utf-8");
        MvcResult mvcResult = mockMvc.perform(post("/item_template/get_item_template_menu_detail").header("userInfo",encode).accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JacksonUtils.writeValueAsString(dto)))
                .andExpect(status().is4xxClientError()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }


    @Test
    public  void  batchRemove() throws Exception {
        SingleDataDTO dto = new SingleDataDTO();
      //  dto.setData("6539457186047721474");
        dto.setDatas(Arrays.asList("6541871078568888321"));
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setEnterpriseGuid("6506431195651982337");
        String encode = URLEncoder.encode(JacksonUtils.writeValueAsString(userInfoDTO), "utf-8");
        MvcResult mvcResult = mockMvc.perform(post("/item_template/batch_remove").header("userInfo",encode).accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JacksonUtils.writeValueAsString(dto)))
                .andExpect(status().is4xxClientError()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }
}
