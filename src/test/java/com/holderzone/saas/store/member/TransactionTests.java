package com.holderzone.saas.store.member;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.member.grade.AccountValidityDTO;
import com.holderzone.saas.store.dto.member.grade.MemberGradeDTO;
import com.holderzone.saas.store.member.HolderSaasStoreMemberApplication;
import com.holderzone.saas.store.member.entity.enums.ConsumeIntegralRuleTypeEnum;
import com.holderzone.saas.store.member.entity.enums.GetIntegralRuleTypeEnum;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/*
@RunWith(SpringRunner.class)
@SpringBootTest(classes = HolderSaasStoreMemberApplication.class)
@WebAppConfiguration
@AutoConfigureMockMvc
public class TransactionTests {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private RedisTemplate redisTemplate;

    private MockMvc mockMvc;

    @Autowired
    private WebApplicationContext context;

    @Before
    public void setupMockMvc() throws Exception {
        mockMvc = MockMvcBuilders.webAppContextSetup(context).build();
    }

    @Test
    public void getAccountValidity() throws Exception {
        mockMvc.perform(post("/grade/get_account_validity")).andDo(print())
                .andExpect(status().isOk()).andReturn().getResponse().getContentAsString();
    }

    @Test
    public void updateAccountValidity() throws Exception {
        AccountValidityDTO accountValidityDTO = new AccountValidityDTO();
        accountValidityDTO.setExpiryType((byte)0);
        accountValidityDTO.setYear(0);
        String writeValueAsString = JSON.toJSONString(accountValidityDTO);
        mockMvc.perform(post("/grade/update_account_validity").contentType(MediaType.APPLICATION_JSON).content(writeValueAsString)).andDo(print())
                .andExpect(status().isOk()).andReturn().getResponse().getContentAsString();
    }

    @Test
    public void memberGradeList() throws Exception {

        String contentAsString = mockMvc.perform(post("/grade/member_grade_list")).andDo(print())
                .andExpect(status().isOk()).andReturn().getResponse().getContentAsString();
        System.out.println(contentAsString);
       */
/* JSONArray tdata = JSON.parseObject(contentAsString).getJSONArray("tdata");
//        List<MemberGradeDTO> memberGradeDTOS = new ArrayList<>();
//        List<MemberGradeDTO> list = JacksonUtils.toObject(memberGradeDTOS.getClass(), tdata);
//        System.out.println(list);
        MemberGradeDTO memberGradeDTO = tdata.getObject( 5,MemberGradeDTO.class);
        memberGradeDTO.setName("超级小菜鸡最强王者");*//*

//        mockMvc.perform(post("/grade/update_member_grade").contentType(MediaType.APPLICATION_JSON).content(JacksonUtils.writeValueAsString(memberGradeDTO))).andDo(print())
//                .andExpect(status().isOk()).andReturn().getResponse().getContentAsString();
//        mockMvc.perform(post("/grade/delete_member_grade").contentType(MediaType.APPLICATION_JSON).content(JacksonUtils.writeValueAsString(memberGradeDTO))).andDo(print())
//                .andExpect(status().isOk()).andReturn().getResponse().getContentAsString();

    }

    @Test
    public void addMemberGrade() throws Exception {
        MemberGradeDTO memberGradeDTO = new MemberGradeDTO();
        memberGradeDTO.setName("被删除的最强王者");
        memberGradeDTO.setNeedIntegral(10000);
        memberGradeDTO.setIsDefault((byte)0);
        memberGradeDTO.setDiscount(new BigDecimal(1));
        memberGradeDTO.setPrepaidRule((byte)1);
        List<Integer> list = new ArrayList<>();
        list.add(100);
        list.add(200);
        String string = JacksonUtils.writeValueAsString(list);
//        memberGradeDTO.setPrepaidLimit(string);
        memberGradeDTO.setStyle((byte)0);
        memberGradeDTO.setColor(1);
        List<MemberGradeDTO.IntegralRuleDTO> integralRuleDTOS = new ArrayList<>();
        MemberGradeDTO.IntegralRuleDTO integralRuleDTO1 = new MemberGradeDTO.IntegralRuleDTO();
        MemberGradeDTO.IntegralRuleDTO integralRuleDTO2 = new MemberGradeDTO.IntegralRuleDTO();
        MemberGradeDTO.IntegralRuleDTO integralRuleDTO3 = new MemberGradeDTO.IntegralRuleDTO();
        integralRuleDTO1.setType((byte)1);
        integralRuleDTO1.setIsOpen((byte)1);
        integralRuleDTO1.setConsumeFeeMin(new BigDecimal(10));
        integralRuleDTO1.setConsumeIntegralMax(100);
        integralRuleDTO1.setConsumeType(ConsumeIntegralRuleTypeEnum.OFFSET.getCode());
        integralRuleDTO1.setConsumeFeeUnit(new BigDecimal(1));
        integralRuleDTO1.setConsumeIntegralUnit(10);
        integralRuleDTO2.setType((byte)0);
        integralRuleDTO2.setIsOpen((byte)1);
        integralRuleDTO2.setConsumeType(GetIntegralRuleTypeEnum.PREPAID.getCode());
        integralRuleDTO2.setGetFeeUnit(new BigDecimal(1));
        integralRuleDTO2.setGetIntegralUnit(2);
        integralRuleDTO3.setType((byte)0);
        integralRuleDTO3.setIsOpen((byte)1);
        integralRuleDTO3.setConsumeType(GetIntegralRuleTypeEnum.CONSUME.getCode());
        integralRuleDTO3.setGetFeeUnit(new BigDecimal(1));
        integralRuleDTO3.setGetIntegralUnit(2);
        integralRuleDTOS.add(integralRuleDTO1);
        integralRuleDTOS.add(integralRuleDTO2);
        integralRuleDTOS.add(integralRuleDTO3);
        memberGradeDTO.setIntegralRuleDTOS(integralRuleDTOS);
        String writeValueAsString = JSON.toJSONString(memberGradeDTO);
        mockMvc.perform(post("/grade/add_member_grade").contentType(MediaType.APPLICATION_JSON).content(writeValueAsString)).andDo(print())
                .andExpect(status().isOk()).andReturn().getResponse().getContentAsString();
    }





}
*/
