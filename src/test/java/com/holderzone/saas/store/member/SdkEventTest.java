package com.holderzone.saas.store.member;

import com.alibaba.fastjson.JSON;
import com.holderzone.framework.event.CustomerEvent;
import com.holderzone.framework.event.publish.impl.CustomerPublishImpl;
import com.holderzone.saas.store.dto.member.common.BaseMemberDTO;
import com.holderzone.saas.store.member.entity.bo.MemberPayBO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SdkEventTest
 * @date 2018/11/30 16:31
 * @description //TODO
 * @program holder-saas-store-dto
 */
/*
@RunWith(SpringRunner.class)
@SpringBootTest(classes = HolderSaasStoreMemberApplication.class)
@WebAppConfiguration
@AutoConfigureMockMvc
public class SdkEventTest {

    @Autowired
    private CustomerPublishImpl customerPublish;

    @Test
    public void testGetMember() throws Exception {
        MemberPayBO memberPayBO = new MemberPayBO();
        customerPublish.publish(new CustomerEvent<>(memberPayBO));
        System.out.println("okkkkkkkkkkkkkkkkkkk");
    }
}
*/
