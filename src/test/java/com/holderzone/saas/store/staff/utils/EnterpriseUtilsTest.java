package com.holderzone.saas.store.staff.utils;

import com.holderzone.saas.store.staff.service.remote.EnterpriseClient;
import com.holderzone.saas.store.staff.service.remote.EnterpriseDataClient;
import org.junit.Test;

import static org.assertj.core.api.Assertions.assertThat;

public class EnterpriseUtilsTest {

    @Test
    public void testCheckEnterpriseExists() {
        // Setup
        final EnterpriseDataClient enterpriseDataClient = null;
        final EnterpriseClient enterpriseClient = null;

        // Run the test
        final boolean result = EnterpriseUtils.checkEnterpriseExists(enterpriseDataClient, enterpriseClient,
                "enterpriseGuid");

        // Verify the results
        assertThat(result).isFalse();
    }
}
