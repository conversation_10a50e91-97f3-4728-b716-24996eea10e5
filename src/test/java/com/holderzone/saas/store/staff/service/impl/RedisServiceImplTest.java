package com.holderzone.saas.store.staff.service.impl;

import com.holderzone.saas.store.dto.user.AioPermissionDTO;
import com.holderzone.saas.store.dto.user.RolePermissionDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.RedisTemplate;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class RedisServiceImplTest {

    @Mock
    private RedisTemplate mockRedisTemplate;

    @InjectMocks
    private RedisServiceImpl redisServiceImplUnderTest;

    @Test
    public void testInitRedisService() {
        // Setup
        // Run the test
        redisServiceImplUnderTest.initRedisService();

        // Verify the results
    }

    @Test
    public void testDeleteAllPermissionByRoleGuid() {
        // Setup
        // Run the test
        RedisServiceImpl.deleteAllPermissionByRoleGuid("roleGuid");

        // Verify the results
    }

    @Test
    public void testPutPermissionByUserGuid() {
        // Setup
        final List<RolePermissionDTO> rolePermissionDTOList = Arrays.asList(new RolePermissionDTO(
                Arrays.asList(new AioPermissionDTO(0, "permissionName", new BigDecimal("0.00")))));
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        redisServiceImplUnderTest.putPermissionByUserGuid("userGuid", rolePermissionDTOList);

        // Verify the results
    }

    @Test
    public void testPutPermissionByRoleGuid() {
        // Setup
        final List<RolePermissionDTO> rolePermissionDTOList = Arrays.asList(new RolePermissionDTO(
                Arrays.asList(new AioPermissionDTO(0, "permissionName", new BigDecimal("0.00")))));
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        redisServiceImplUnderTest.putPermissionByRoleGuid("roleGuid", rolePermissionDTOList);

        // Verify the results
    }

    @Test
    public void testDeletePermissionByUserGuidList() {
        // Setup
        // Run the test
        redisServiceImplUnderTest.deletePermissionByUserGuidList(Arrays.asList("value"));

        // Verify the results
        verify(mockRedisTemplate).delete(Arrays.asList("value"));
    }

    @Test
    public void testDeletePermissionByRoleGuid() {
        // Setup
        // Run the test
        redisServiceImplUnderTest.deletePermissionByRoleGuid("roleGuid");

        // Verify the results
        verify(mockRedisTemplate).delete("key");
    }

    @Test
    public void testGetPermissionByUserGuid() {
        // Setup
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        final List<RolePermissionDTO> result = redisServiceImplUnderTest.getPermissionByUserGuid("userGuid");

        // Verify the results
    }

    @Test
    public void testGetPermissionByRoleGuid() {
        // Setup
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        final List<RolePermissionDTO> result = redisServiceImplUnderTest.getPermissionByRoleGuid("roleGuid");

        // Verify the results
    }

    @Test
    public void testPutAioPermissionByUserGuid() {
        // Setup
        final List<AioPermissionDTO> aioPermissionDTOList = Arrays.asList(
                new AioPermissionDTO(0, "permissionName", new BigDecimal("0.00")));
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        redisServiceImplUnderTest.putAioPermissionByUserGuid("userGuid", aioPermissionDTOList);

        // Verify the results
    }

    @Test
    public void testDeleteAioPermissionByUserGuidList() {
        // Setup
        // Run the test
        redisServiceImplUnderTest.deleteAioPermissionByUserGuidList(Arrays.asList("value"));

        // Verify the results
        verify(mockRedisTemplate).delete(Arrays.asList("value"));
    }

    @Test
    public void testGetAioPermissionByUserGuid() {
        // Setup
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        final List<AioPermissionDTO> result = redisServiceImplUnderTest.getAioPermissionByUserGuid("userGuid");

        // Verify the results
    }
}
