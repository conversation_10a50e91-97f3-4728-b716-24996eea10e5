package com.holderzone.saas.store.staff.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.organization.*;
import com.holderzone.saas.store.dto.user.*;
import com.holderzone.saas.store.staff.entity.domain.RoleDO;
import com.holderzone.saas.store.staff.entity.domain.UserDO;
import com.holderzone.saas.store.staff.entity.domain.UserDataDO;
import com.holderzone.saas.store.staff.entity.read.UserReadDO;
import com.holderzone.saas.store.staff.mapper.RoleMapper;
import com.holderzone.saas.store.staff.mapper.UserDataMapper;
import com.holderzone.saas.store.staff.mapper.UserMapper;
import com.holderzone.saas.store.staff.service.DistributedService;
import com.holderzone.saas.store.staff.service.remote.OrgFeignClient;
import com.holderzone.saas.store.staff.service.remote.UserClient;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class UserDataServiceImplTest {

    @Mock
    private UserMapper mockUserMapper;
    @Mock
    private RoleMapper mockRoleMapper;
    @Mock
    private OrgFeignClient mockOrgFeignClient;
    @Mock
    private DistributedService mockDistributedService;
    @Mock
    private UserDataMapper mockUserDataMapper;
    @Mock
    private UserClient mockUserClient;

    private UserDataServiceImpl userDataServiceImplUnderTest;

    @Before
    public void setUp() {
        userDataServiceImplUnderTest = new UserDataServiceImpl(mockUserMapper, mockRoleMapper, mockOrgFeignClient,
                mockDistributedService, mockUserDataMapper, mockUserClient);
    }

    @Test
    public void testSave() {
        // Setup
        final UserDTO userDTO = new UserDTO();
        userDTO.setGuid("userGuid");
        userDTO.setAccount("account");
        userDTO.setPassword("password");
        userDTO.setName("name");
        final UserDataStoreRuleDTO userDataStoreRule = new UserDataStoreRuleDTO();
        final UserDataDTO userDataDTO = new UserDataDTO();
        userDataDTO.setGuid("storeGuid");
        userDataDTO.setName("name");
        userDataDTO.setIsDeleted(false);
        userDataStoreRule.setUserStoreData(Arrays.asList(userDataDTO));
        userDTO.setUserDataStoreRule(userDataStoreRule);
        final UserDataCondRuleDTO userDataCondRuleDTO = new UserDataCondRuleDTO();
        final UserDataDTO userDataDTO1 = new UserDataDTO();
        userDataDTO1.setGuid("storeGuid");
        userDataDTO1.setName("name");
        userDataDTO1.setIsDeleted(false);
        userDataCondRuleDTO.setUserOrgData(Arrays.asList(userDataDTO1));
        final UserDataDTO userDataDTO2 = new UserDataDTO();
        userDataDTO2.setGuid("storeGuid");
        userDataDTO2.setName("name");
        userDataDTO2.setIsDeleted(false);
        userDataCondRuleDTO.setUserBrandData(Arrays.asList(userDataDTO2));
        final UserRegionDTO userRegionDTO = new UserRegionDTO();
        userRegionDTO.setAdcode("regionCode");
        userRegionDTO.setName("regionName");
        userRegionDTO.setIsDeleted(false);
        userDataCondRuleDTO.setUserRegionData(Arrays.asList(userRegionDTO));
        userDTO.setUserDataCondRuleList(Arrays.asList(userDataCondRuleDTO));
        userDTO.setDiscountThreshold(new BigDecimal("0.00"));
        userDTO.setAllowanceThreshold(new BigDecimal("0.00"));
        userDTO.setProductDiscountThreshold(new BigDecimal("0.00"));
        userDTO.setRefundThreshold(new BigDecimal("0.00"));
        final UserRoleDistDTO userRoleDistDTO = new UserRoleDistDTO();
        userRoleDistDTO.setGuid("c820b147-d4ec-47c9-93f9-201cdcf6a589");
        userRoleDistDTO.setIsChecked(false);
        userDTO.setUserRolesDistributable(Arrays.asList(userRoleDistDTO));

        when(mockDistributedService.nextBatchUserDataGuid(0L)).thenReturn(Arrays.asList("value"));

        // Run the test
        userDataServiceImplUnderTest.save(userDTO);

        // Verify the results
        // Confirm UserMapper.update(...).
        final UserDO entity = new UserDO();
        entity.setGuid("userGuid");
        entity.setEnterpriseNo("enterpriseGuid");
        entity.setAccount("account");
        entity.setPassword("password");
        entity.setName("name");
        entity.setPhone("phone");
        entity.setDiscountThreshold(new BigDecimal("0.00"));
        entity.setAllowanceThreshold(new BigDecimal("0.00"));
        entity.setProductDiscountThreshold(new BigDecimal("0.00"));
        entity.setRefundThreshold(new BigDecimal("0.00"));
        entity.setRolesDistributable("roleGuid");
        verify(mockUserMapper).update(eq(entity), any(LambdaQueryWrapper.class));
    }

    @Test
    public void testSave_DistributedServiceReturnsNoItems() {
        // Setup
        final UserDTO userDTO = new UserDTO();
        userDTO.setGuid("userGuid");
        userDTO.setAccount("account");
        userDTO.setPassword("password");
        userDTO.setName("name");
        final UserDataStoreRuleDTO userDataStoreRule = new UserDataStoreRuleDTO();
        final UserDataDTO userDataDTO = new UserDataDTO();
        userDataDTO.setGuid("storeGuid");
        userDataDTO.setName("name");
        userDataDTO.setIsDeleted(false);
        userDataStoreRule.setUserStoreData(Arrays.asList(userDataDTO));
        userDTO.setUserDataStoreRule(userDataStoreRule);
        final UserDataCondRuleDTO userDataCondRuleDTO = new UserDataCondRuleDTO();
        final UserDataDTO userDataDTO1 = new UserDataDTO();
        userDataDTO1.setGuid("storeGuid");
        userDataDTO1.setName("name");
        userDataDTO1.setIsDeleted(false);
        userDataCondRuleDTO.setUserOrgData(Arrays.asList(userDataDTO1));
        final UserDataDTO userDataDTO2 = new UserDataDTO();
        userDataDTO2.setGuid("storeGuid");
        userDataDTO2.setName("name");
        userDataDTO2.setIsDeleted(false);
        userDataCondRuleDTO.setUserBrandData(Arrays.asList(userDataDTO2));
        final UserRegionDTO userRegionDTO = new UserRegionDTO();
        userRegionDTO.setAdcode("regionCode");
        userRegionDTO.setName("regionName");
        userRegionDTO.setIsDeleted(false);
        userDataCondRuleDTO.setUserRegionData(Arrays.asList(userRegionDTO));
        userDTO.setUserDataCondRuleList(Arrays.asList(userDataCondRuleDTO));
        userDTO.setDiscountThreshold(new BigDecimal("0.00"));
        userDTO.setAllowanceThreshold(new BigDecimal("0.00"));
        userDTO.setProductDiscountThreshold(new BigDecimal("0.00"));
        userDTO.setRefundThreshold(new BigDecimal("0.00"));
        final UserRoleDistDTO userRoleDistDTO = new UserRoleDistDTO();
        userRoleDistDTO.setGuid("c820b147-d4ec-47c9-93f9-201cdcf6a589");
        userRoleDistDTO.setIsChecked(false);
        userDTO.setUserRolesDistributable(Arrays.asList(userRoleDistDTO));

        when(mockDistributedService.nextBatchUserDataGuid(0L)).thenReturn(Collections.emptyList());

        // Run the test
        userDataServiceImplUnderTest.save(userDTO);

        // Verify the results
        // Confirm UserMapper.update(...).
        final UserDO entity = new UserDO();
        entity.setGuid("userGuid");
        entity.setEnterpriseNo("enterpriseGuid");
        entity.setAccount("account");
        entity.setPassword("password");
        entity.setName("name");
        entity.setPhone("phone");
        entity.setDiscountThreshold(new BigDecimal("0.00"));
        entity.setAllowanceThreshold(new BigDecimal("0.00"));
        entity.setProductDiscountThreshold(new BigDecimal("0.00"));
        entity.setRefundThreshold(new BigDecimal("0.00"));
        entity.setRolesDistributable("roleGuid");
        verify(mockUserMapper).update(eq(entity), any(LambdaQueryWrapper.class));
    }

    @Test
    public void testAppendRole() {
        // Setup
        // Configure UserMapper.selectOne(...).
        final UserDO userDO = new UserDO();
        userDO.setGuid("userGuid");
        userDO.setEnterpriseNo("enterpriseGuid");
        userDO.setAccount("account");
        userDO.setPassword("password");
        userDO.setName("name");
        userDO.setPhone("phone");
        userDO.setDiscountThreshold(new BigDecimal("0.00"));
        userDO.setAllowanceThreshold(new BigDecimal("0.00"));
        userDO.setProductDiscountThreshold(new BigDecimal("0.00"));
        userDO.setRefundThreshold(new BigDecimal("0.00"));
        userDO.setRolesDistributable("roleGuid");
        when(mockUserMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(userDO);

        // Run the test
        userDataServiceImplUnderTest.appendRole("roleGuid");

        // Verify the results
        // Confirm UserMapper.update(...).
        final UserDO entity = new UserDO();
        entity.setGuid("userGuid");
        entity.setEnterpriseNo("enterpriseGuid");
        entity.setAccount("account");
        entity.setPassword("password");
        entity.setName("name");
        entity.setPhone("phone");
        entity.setDiscountThreshold(new BigDecimal("0.00"));
        entity.setAllowanceThreshold(new BigDecimal("0.00"));
        entity.setProductDiscountThreshold(new BigDecimal("0.00"));
        entity.setRefundThreshold(new BigDecimal("0.00"));
        entity.setRolesDistributable("roleGuid");
        verify(mockUserMapper).update(eq(entity), any(LambdaQueryWrapper.class));
    }

    @Test
    public void testUpdateNewStore() {
        // Setup
        // Configure UserDataMapper.selectList(...).
        final List<UserDataDO> userDataDOS = Arrays.asList(UserDataDO.builder()
                .userGuid("userGuid")
                .ruleGuid("ruleGuid")
                .storeGuid("storeGuid")
                .organizationGuid("storeGuid")
                .regionCode("regionCode")
                .regionName("regionName")
                .brandGuid("storeGuid")
                .build());
        when(mockUserDataMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(userDataDOS);

        // Run the test
        userDataServiceImplUnderTest.updateNewStore("storeGuid");

        // Verify the results
        verify(mockUserDataMapper).insert(UserDataDO.builder()
                .userGuid("userGuid")
                .ruleGuid("ruleGuid")
                .storeGuid("storeGuid")
                .organizationGuid("storeGuid")
                .regionCode("regionCode")
                .regionName("regionName")
                .brandGuid("storeGuid")
                .build());
    }

    @Test
    public void testUpdateNewStore_UserDataMapperSelectListReturnsNoItems() {
        // Setup
        when(mockUserDataMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());
        when(mockDistributedService.nextBatchUserDataGuid(2L)).thenReturn(Arrays.asList("value"));

        // Run the test
        userDataServiceImplUnderTest.updateNewStore("storeGuid");

        // Verify the results
        verify(mockUserDataMapper).insert(UserDataDO.builder()
                .userGuid("userGuid")
                .ruleGuid("ruleGuid")
                .storeGuid("storeGuid")
                .organizationGuid("storeGuid")
                .regionCode("regionCode")
                .regionName("regionName")
                .brandGuid("storeGuid")
                .build());
    }

    @Test
    public void testUpdateNewStore_DistributedServiceReturnsNoItems() {
        // Setup
        when(mockUserDataMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());
        when(mockDistributedService.nextBatchUserDataGuid(2L)).thenReturn(Collections.emptyList());

        // Run the test
        userDataServiceImplUnderTest.updateNewStore("storeGuid");

        // Verify the results
        verify(mockUserDataMapper).insert(UserDataDO.builder()
                .userGuid("userGuid")
                .ruleGuid("ruleGuid")
                .storeGuid("storeGuid")
                .organizationGuid("storeGuid")
                .regionCode("regionCode")
                .regionName("regionName")
                .brandGuid("storeGuid")
                .build());
    }

    @Test
    public void testQuery() {
        // Setup
        final UserDTO userDTO = new UserDTO();
        userDTO.setGuid("userGuid");
        userDTO.setAccount("account");
        userDTO.setPassword("password");
        userDTO.setName("name");
        final UserDataStoreRuleDTO userDataStoreRule = new UserDataStoreRuleDTO();
        final UserDataDTO userDataDTO = new UserDataDTO();
        userDataDTO.setGuid("storeGuid");
        userDataDTO.setName("name");
        userDataDTO.setIsDeleted(false);
        userDataStoreRule.setUserStoreData(Arrays.asList(userDataDTO));
        userDTO.setUserDataStoreRule(userDataStoreRule);
        final UserDataCondRuleDTO userDataCondRuleDTO = new UserDataCondRuleDTO();
        final UserDataDTO userDataDTO1 = new UserDataDTO();
        userDataDTO1.setGuid("storeGuid");
        userDataDTO1.setName("name");
        userDataDTO1.setIsDeleted(false);
        userDataCondRuleDTO.setUserOrgData(Arrays.asList(userDataDTO1));
        final UserDataDTO userDataDTO2 = new UserDataDTO();
        userDataDTO2.setGuid("storeGuid");
        userDataDTO2.setName("name");
        userDataDTO2.setIsDeleted(false);
        userDataCondRuleDTO.setUserBrandData(Arrays.asList(userDataDTO2));
        final UserRegionDTO userRegionDTO = new UserRegionDTO();
        userRegionDTO.setAdcode("regionCode");
        userRegionDTO.setName("regionName");
        userRegionDTO.setIsDeleted(false);
        userDataCondRuleDTO.setUserRegionData(Arrays.asList(userRegionDTO));
        userDTO.setUserDataCondRuleList(Arrays.asList(userDataCondRuleDTO));
        userDTO.setDiscountThreshold(new BigDecimal("0.00"));
        userDTO.setAllowanceThreshold(new BigDecimal("0.00"));
        userDTO.setProductDiscountThreshold(new BigDecimal("0.00"));
        userDTO.setRefundThreshold(new BigDecimal("0.00"));
        final UserRoleDistDTO userRoleDistDTO = new UserRoleDistDTO();
        userRoleDistDTO.setGuid("c820b147-d4ec-47c9-93f9-201cdcf6a589");
        userRoleDistDTO.setIsChecked(false);
        userDTO.setUserRolesDistributable(Arrays.asList(userRoleDistDTO));

        final UserDTO expectedResult = new UserDTO();
        expectedResult.setGuid("userGuid");
        expectedResult.setAccount("account");
        expectedResult.setPassword("password");
        expectedResult.setName("name");
        final UserDataStoreRuleDTO userDataStoreRule1 = new UserDataStoreRuleDTO();
        final UserDataDTO userDataDTO3 = new UserDataDTO();
        userDataDTO3.setGuid("storeGuid");
        userDataDTO3.setName("name");
        userDataDTO3.setIsDeleted(false);
        userDataStoreRule1.setUserStoreData(Arrays.asList(userDataDTO3));
        expectedResult.setUserDataStoreRule(userDataStoreRule1);
        final UserDataCondRuleDTO userDataCondRuleDTO1 = new UserDataCondRuleDTO();
        final UserDataDTO userDataDTO4 = new UserDataDTO();
        userDataDTO4.setGuid("storeGuid");
        userDataDTO4.setName("name");
        userDataDTO4.setIsDeleted(false);
        userDataCondRuleDTO1.setUserOrgData(Arrays.asList(userDataDTO4));
        final UserDataDTO userDataDTO5 = new UserDataDTO();
        userDataDTO5.setGuid("storeGuid");
        userDataDTO5.setName("name");
        userDataDTO5.setIsDeleted(false);
        userDataCondRuleDTO1.setUserBrandData(Arrays.asList(userDataDTO5));
        final UserRegionDTO userRegionDTO1 = new UserRegionDTO();
        userRegionDTO1.setAdcode("regionCode");
        userRegionDTO1.setName("regionName");
        userRegionDTO1.setIsDeleted(false);
        userDataCondRuleDTO1.setUserRegionData(Arrays.asList(userRegionDTO1));
        expectedResult.setUserDataCondRuleList(Arrays.asList(userDataCondRuleDTO1));
        expectedResult.setDiscountThreshold(new BigDecimal("0.00"));
        expectedResult.setAllowanceThreshold(new BigDecimal("0.00"));
        expectedResult.setProductDiscountThreshold(new BigDecimal("0.00"));
        expectedResult.setRefundThreshold(new BigDecimal("0.00"));
        final UserRoleDistDTO userRoleDistDTO1 = new UserRoleDistDTO();
        userRoleDistDTO1.setGuid("c820b147-d4ec-47c9-93f9-201cdcf6a589");
        userRoleDistDTO1.setIsChecked(false);
        expectedResult.setUserRolesDistributable(Arrays.asList(userRoleDistDTO1));

        // Configure RoleMapper.selectList(...).
        final RoleDO roleDO = new RoleDO();
        roleDO.setId(0L);
        roleDO.setGuid("********-3e56-412f-8ded-e8f75f628b26");
        roleDO.setName("name");
        roleDO.setIsEnable(false);
        roleDO.setIsDeleted(false);
        final List<RoleDO> roleDOS = Arrays.asList(roleDO);
        when(mockRoleMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(roleDOS);

        // Run the test
        final UserDTO result = userDataServiceImplUnderTest.query(userDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQuery_RoleMapperReturnsNoItems() {
        // Setup
        final UserDTO userDTO = new UserDTO();
        userDTO.setGuid("userGuid");
        userDTO.setAccount("account");
        userDTO.setPassword("password");
        userDTO.setName("name");
        final UserDataStoreRuleDTO userDataStoreRule = new UserDataStoreRuleDTO();
        final UserDataDTO userDataDTO = new UserDataDTO();
        userDataDTO.setGuid("storeGuid");
        userDataDTO.setName("name");
        userDataDTO.setIsDeleted(false);
        userDataStoreRule.setUserStoreData(Arrays.asList(userDataDTO));
        userDTO.setUserDataStoreRule(userDataStoreRule);
        final UserDataCondRuleDTO userDataCondRuleDTO = new UserDataCondRuleDTO();
        final UserDataDTO userDataDTO1 = new UserDataDTO();
        userDataDTO1.setGuid("storeGuid");
        userDataDTO1.setName("name");
        userDataDTO1.setIsDeleted(false);
        userDataCondRuleDTO.setUserOrgData(Arrays.asList(userDataDTO1));
        final UserDataDTO userDataDTO2 = new UserDataDTO();
        userDataDTO2.setGuid("storeGuid");
        userDataDTO2.setName("name");
        userDataDTO2.setIsDeleted(false);
        userDataCondRuleDTO.setUserBrandData(Arrays.asList(userDataDTO2));
        final UserRegionDTO userRegionDTO = new UserRegionDTO();
        userRegionDTO.setAdcode("regionCode");
        userRegionDTO.setName("regionName");
        userRegionDTO.setIsDeleted(false);
        userDataCondRuleDTO.setUserRegionData(Arrays.asList(userRegionDTO));
        userDTO.setUserDataCondRuleList(Arrays.asList(userDataCondRuleDTO));
        userDTO.setDiscountThreshold(new BigDecimal("0.00"));
        userDTO.setAllowanceThreshold(new BigDecimal("0.00"));
        userDTO.setProductDiscountThreshold(new BigDecimal("0.00"));
        userDTO.setRefundThreshold(new BigDecimal("0.00"));
        final UserRoleDistDTO userRoleDistDTO = new UserRoleDistDTO();
        userRoleDistDTO.setGuid("c820b147-d4ec-47c9-93f9-201cdcf6a589");
        userRoleDistDTO.setIsChecked(false);
        userDTO.setUserRolesDistributable(Arrays.asList(userRoleDistDTO));

        final UserDTO expectedResult = new UserDTO();
        expectedResult.setGuid("userGuid");
        expectedResult.setAccount("account");
        expectedResult.setPassword("password");
        expectedResult.setName("name");
        final UserDataStoreRuleDTO userDataStoreRule1 = new UserDataStoreRuleDTO();
        final UserDataDTO userDataDTO3 = new UserDataDTO();
        userDataDTO3.setGuid("storeGuid");
        userDataDTO3.setName("name");
        userDataDTO3.setIsDeleted(false);
        userDataStoreRule1.setUserStoreData(Arrays.asList(userDataDTO3));
        expectedResult.setUserDataStoreRule(userDataStoreRule1);
        final UserDataCondRuleDTO userDataCondRuleDTO1 = new UserDataCondRuleDTO();
        final UserDataDTO userDataDTO4 = new UserDataDTO();
        userDataDTO4.setGuid("storeGuid");
        userDataDTO4.setName("name");
        userDataDTO4.setIsDeleted(false);
        userDataCondRuleDTO1.setUserOrgData(Arrays.asList(userDataDTO4));
        final UserDataDTO userDataDTO5 = new UserDataDTO();
        userDataDTO5.setGuid("storeGuid");
        userDataDTO5.setName("name");
        userDataDTO5.setIsDeleted(false);
        userDataCondRuleDTO1.setUserBrandData(Arrays.asList(userDataDTO5));
        final UserRegionDTO userRegionDTO1 = new UserRegionDTO();
        userRegionDTO1.setAdcode("regionCode");
        userRegionDTO1.setName("regionName");
        userRegionDTO1.setIsDeleted(false);
        userDataCondRuleDTO1.setUserRegionData(Arrays.asList(userRegionDTO1));
        expectedResult.setUserDataCondRuleList(Arrays.asList(userDataCondRuleDTO1));
        expectedResult.setDiscountThreshold(new BigDecimal("0.00"));
        expectedResult.setAllowanceThreshold(new BigDecimal("0.00"));
        expectedResult.setProductDiscountThreshold(new BigDecimal("0.00"));
        expectedResult.setRefundThreshold(new BigDecimal("0.00"));
        final UserRoleDistDTO userRoleDistDTO1 = new UserRoleDistDTO();
        userRoleDistDTO1.setGuid("c820b147-d4ec-47c9-93f9-201cdcf6a589");
        userRoleDistDTO1.setIsChecked(false);
        expectedResult.setUserRolesDistributable(Arrays.asList(userRoleDistDTO1));

        when(mockRoleMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final UserDTO result = userDataServiceImplUnderTest.query(userDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQuery_OrgFeignClientQueryStoreByGuidListReturnsNoItems() {
        // Setup
        final UserDTO userDTO = new UserDTO();
        userDTO.setGuid("userGuid");
        userDTO.setAccount("account");
        userDTO.setPassword("password");
        userDTO.setName("name");
        final UserDataStoreRuleDTO userDataStoreRule = new UserDataStoreRuleDTO();
        final UserDataDTO userDataDTO = new UserDataDTO();
        userDataDTO.setGuid("storeGuid");
        userDataDTO.setName("name");
        userDataDTO.setIsDeleted(false);
        userDataStoreRule.setUserStoreData(Arrays.asList(userDataDTO));
        userDTO.setUserDataStoreRule(userDataStoreRule);
        final UserDataCondRuleDTO userDataCondRuleDTO = new UserDataCondRuleDTO();
        final UserDataDTO userDataDTO1 = new UserDataDTO();
        userDataDTO1.setGuid("storeGuid");
        userDataDTO1.setName("name");
        userDataDTO1.setIsDeleted(false);
        userDataCondRuleDTO.setUserOrgData(Arrays.asList(userDataDTO1));
        final UserDataDTO userDataDTO2 = new UserDataDTO();
        userDataDTO2.setGuid("storeGuid");
        userDataDTO2.setName("name");
        userDataDTO2.setIsDeleted(false);
        userDataCondRuleDTO.setUserBrandData(Arrays.asList(userDataDTO2));
        final UserRegionDTO userRegionDTO = new UserRegionDTO();
        userRegionDTO.setAdcode("regionCode");
        userRegionDTO.setName("regionName");
        userRegionDTO.setIsDeleted(false);
        userDataCondRuleDTO.setUserRegionData(Arrays.asList(userRegionDTO));
        userDTO.setUserDataCondRuleList(Arrays.asList(userDataCondRuleDTO));
        userDTO.setDiscountThreshold(new BigDecimal("0.00"));
        userDTO.setAllowanceThreshold(new BigDecimal("0.00"));
        userDTO.setProductDiscountThreshold(new BigDecimal("0.00"));
        userDTO.setRefundThreshold(new BigDecimal("0.00"));
        final UserRoleDistDTO userRoleDistDTO = new UserRoleDistDTO();
        userRoleDistDTO.setGuid("c820b147-d4ec-47c9-93f9-201cdcf6a589");
        userRoleDistDTO.setIsChecked(false);
        userDTO.setUserRolesDistributable(Arrays.asList(userRoleDistDTO));

        final UserDTO expectedResult = new UserDTO();
        expectedResult.setGuid("userGuid");
        expectedResult.setAccount("account");
        expectedResult.setPassword("password");
        expectedResult.setName("name");
        final UserDataStoreRuleDTO userDataStoreRule1 = new UserDataStoreRuleDTO();
        final UserDataDTO userDataDTO3 = new UserDataDTO();
        userDataDTO3.setGuid("storeGuid");
        userDataDTO3.setName("name");
        userDataDTO3.setIsDeleted(false);
        userDataStoreRule1.setUserStoreData(Arrays.asList(userDataDTO3));
        expectedResult.setUserDataStoreRule(userDataStoreRule1);
        final UserDataCondRuleDTO userDataCondRuleDTO1 = new UserDataCondRuleDTO();
        final UserDataDTO userDataDTO4 = new UserDataDTO();
        userDataDTO4.setGuid("storeGuid");
        userDataDTO4.setName("name");
        userDataDTO4.setIsDeleted(false);
        userDataCondRuleDTO1.setUserOrgData(Arrays.asList(userDataDTO4));
        final UserDataDTO userDataDTO5 = new UserDataDTO();
        userDataDTO5.setGuid("storeGuid");
        userDataDTO5.setName("name");
        userDataDTO5.setIsDeleted(false);
        userDataCondRuleDTO1.setUserBrandData(Arrays.asList(userDataDTO5));
        final UserRegionDTO userRegionDTO1 = new UserRegionDTO();
        userRegionDTO1.setAdcode("regionCode");
        userRegionDTO1.setName("regionName");
        userRegionDTO1.setIsDeleted(false);
        userDataCondRuleDTO1.setUserRegionData(Arrays.asList(userRegionDTO1));
        expectedResult.setUserDataCondRuleList(Arrays.asList(userDataCondRuleDTO1));
        expectedResult.setDiscountThreshold(new BigDecimal("0.00"));
        expectedResult.setAllowanceThreshold(new BigDecimal("0.00"));
        expectedResult.setProductDiscountThreshold(new BigDecimal("0.00"));
        expectedResult.setRefundThreshold(new BigDecimal("0.00"));
        final UserRoleDistDTO userRoleDistDTO1 = new UserRoleDistDTO();
        userRoleDistDTO1.setGuid("c820b147-d4ec-47c9-93f9-201cdcf6a589");
        userRoleDistDTO1.setIsChecked(false);
        expectedResult.setUserRolesDistributable(Arrays.asList(userRoleDistDTO1));

        when(mockOrgFeignClient.queryStoreByGuidList(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Configure RoleMapper.selectList(...).
        final RoleDO roleDO = new RoleDO();
        roleDO.setId(0L);
        roleDO.setGuid("********-3e56-412f-8ded-e8f75f628b26");
        roleDO.setName("name");
        roleDO.setIsEnable(false);
        roleDO.setIsDeleted(false);
        final List<RoleDO> roleDOS = Arrays.asList(roleDO);
        when(mockRoleMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(roleDOS);

        // Configure OrgFeignClient.queryOrgNameByIdList(...).
        final OrganizationDTO organizationDTO = new OrganizationDTO();
        organizationDTO.setGuid("storeGuid");
        organizationDTO.setUuid("2a5992e2-e91c-416d-9be3-4be89dae8554");
        organizationDTO.setName("name");
        organizationDTO.setContactName("contactName");
        organizationDTO.setIsDeleted(false);
        final List<OrganizationDTO> organizationDTOS = Arrays.asList(organizationDTO);
        when(mockOrgFeignClient.queryOrgNameByIdList(Arrays.asList("value"))).thenReturn(organizationDTOS);

        // Configure OrgFeignClient.queryBrandByIdList(...).
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("storeGuid");
        brandDTO.setUuid("0db9815e-f80b-41a5-8ce4-7ff2f7e03e87");
        brandDTO.setName("name");
        brandDTO.setDescription("description");
        brandDTO.setIsDeleted(false);
        final List<BrandDTO> brandDTOS = Arrays.asList(brandDTO);
        when(mockOrgFeignClient.queryBrandByIdList(Arrays.asList("value"))).thenReturn(brandDTOS);

        // Configure UserMapper.selectOne(...).
        final UserDO userDO = new UserDO();
        userDO.setGuid("userGuid");
        userDO.setEnterpriseNo("enterpriseGuid");
        userDO.setAccount("account");
        userDO.setPassword("password");
        userDO.setName("name");
        userDO.setPhone("phone");
        userDO.setDiscountThreshold(new BigDecimal("0.00"));
        userDO.setAllowanceThreshold(new BigDecimal("0.00"));
        userDO.setProductDiscountThreshold(new BigDecimal("0.00"));
        userDO.setRefundThreshold(new BigDecimal("0.00"));
        userDO.setRolesDistributable("roleGuid");
        when(mockUserMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(userDO);

        // Run the test
        final UserDTO result = userDataServiceImplUnderTest.query(userDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQuery_OrgFeignClientQueryOrgNameByIdListReturnsNoItems() {
        // Setup
        final UserDTO userDTO = new UserDTO();
        userDTO.setGuid("userGuid");
        userDTO.setAccount("account");
        userDTO.setPassword("password");
        userDTO.setName("name");
        final UserDataStoreRuleDTO userDataStoreRule = new UserDataStoreRuleDTO();
        final UserDataDTO userDataDTO = new UserDataDTO();
        userDataDTO.setGuid("storeGuid");
        userDataDTO.setName("name");
        userDataDTO.setIsDeleted(false);
        userDataStoreRule.setUserStoreData(Arrays.asList(userDataDTO));
        userDTO.setUserDataStoreRule(userDataStoreRule);
        final UserDataCondRuleDTO userDataCondRuleDTO = new UserDataCondRuleDTO();
        final UserDataDTO userDataDTO1 = new UserDataDTO();
        userDataDTO1.setGuid("storeGuid");
        userDataDTO1.setName("name");
        userDataDTO1.setIsDeleted(false);
        userDataCondRuleDTO.setUserOrgData(Arrays.asList(userDataDTO1));
        final UserDataDTO userDataDTO2 = new UserDataDTO();
        userDataDTO2.setGuid("storeGuid");
        userDataDTO2.setName("name");
        userDataDTO2.setIsDeleted(false);
        userDataCondRuleDTO.setUserBrandData(Arrays.asList(userDataDTO2));
        final UserRegionDTO userRegionDTO = new UserRegionDTO();
        userRegionDTO.setAdcode("regionCode");
        userRegionDTO.setName("regionName");
        userRegionDTO.setIsDeleted(false);
        userDataCondRuleDTO.setUserRegionData(Arrays.asList(userRegionDTO));
        userDTO.setUserDataCondRuleList(Arrays.asList(userDataCondRuleDTO));
        userDTO.setDiscountThreshold(new BigDecimal("0.00"));
        userDTO.setAllowanceThreshold(new BigDecimal("0.00"));
        userDTO.setProductDiscountThreshold(new BigDecimal("0.00"));
        userDTO.setRefundThreshold(new BigDecimal("0.00"));
        final UserRoleDistDTO userRoleDistDTO = new UserRoleDistDTO();
        userRoleDistDTO.setGuid("c820b147-d4ec-47c9-93f9-201cdcf6a589");
        userRoleDistDTO.setIsChecked(false);
        userDTO.setUserRolesDistributable(Arrays.asList(userRoleDistDTO));

        final UserDTO expectedResult = new UserDTO();
        expectedResult.setGuid("userGuid");
        expectedResult.setAccount("account");
        expectedResult.setPassword("password");
        expectedResult.setName("name");
        final UserDataStoreRuleDTO userDataStoreRule1 = new UserDataStoreRuleDTO();
        final UserDataDTO userDataDTO3 = new UserDataDTO();
        userDataDTO3.setGuid("storeGuid");
        userDataDTO3.setName("name");
        userDataDTO3.setIsDeleted(false);
        userDataStoreRule1.setUserStoreData(Arrays.asList(userDataDTO3));
        expectedResult.setUserDataStoreRule(userDataStoreRule1);
        final UserDataCondRuleDTO userDataCondRuleDTO1 = new UserDataCondRuleDTO();
        final UserDataDTO userDataDTO4 = new UserDataDTO();
        userDataDTO4.setGuid("storeGuid");
        userDataDTO4.setName("name");
        userDataDTO4.setIsDeleted(false);
        userDataCondRuleDTO1.setUserOrgData(Arrays.asList(userDataDTO4));
        final UserDataDTO userDataDTO5 = new UserDataDTO();
        userDataDTO5.setGuid("storeGuid");
        userDataDTO5.setName("name");
        userDataDTO5.setIsDeleted(false);
        userDataCondRuleDTO1.setUserBrandData(Arrays.asList(userDataDTO5));
        final UserRegionDTO userRegionDTO1 = new UserRegionDTO();
        userRegionDTO1.setAdcode("regionCode");
        userRegionDTO1.setName("regionName");
        userRegionDTO1.setIsDeleted(false);
        userDataCondRuleDTO1.setUserRegionData(Arrays.asList(userRegionDTO1));
        expectedResult.setUserDataCondRuleList(Arrays.asList(userDataCondRuleDTO1));
        expectedResult.setDiscountThreshold(new BigDecimal("0.00"));
        expectedResult.setAllowanceThreshold(new BigDecimal("0.00"));
        expectedResult.setProductDiscountThreshold(new BigDecimal("0.00"));
        expectedResult.setRefundThreshold(new BigDecimal("0.00"));
        final UserRoleDistDTO userRoleDistDTO1 = new UserRoleDistDTO();
        userRoleDistDTO1.setGuid("c820b147-d4ec-47c9-93f9-201cdcf6a589");
        userRoleDistDTO1.setIsChecked(false);
        expectedResult.setUserRolesDistributable(Arrays.asList(userRoleDistDTO1));

        // Configure OrgFeignClient.queryStoreByGuidList(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("storeGuid");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setProvinceCode("provinceCode");
        storeDTO.setProvinceName("provinceName");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setCountyCode("countyCode");
        storeDTO.setCountyName("countyName");
        storeDTO.setIsDeleted(false);
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO);
        when(mockOrgFeignClient.queryStoreByGuidList(Arrays.asList("value"))).thenReturn(storeDTOS);

        when(mockOrgFeignClient.queryOrgNameByIdList(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Configure RoleMapper.selectList(...).
        final RoleDO roleDO = new RoleDO();
        roleDO.setId(0L);
        roleDO.setGuid("********-3e56-412f-8ded-e8f75f628b26");
        roleDO.setName("name");
        roleDO.setIsEnable(false);
        roleDO.setIsDeleted(false);
        final List<RoleDO> roleDOS = Arrays.asList(roleDO);
        when(mockRoleMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(roleDOS);

        // Configure OrgFeignClient.queryBrandByIdList(...).
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("storeGuid");
        brandDTO.setUuid("0db9815e-f80b-41a5-8ce4-7ff2f7e03e87");
        brandDTO.setName("name");
        brandDTO.setDescription("description");
        brandDTO.setIsDeleted(false);
        final List<BrandDTO> brandDTOS = Arrays.asList(brandDTO);
        when(mockOrgFeignClient.queryBrandByIdList(Arrays.asList("value"))).thenReturn(brandDTOS);

        // Configure UserMapper.selectOne(...).
        final UserDO userDO = new UserDO();
        userDO.setGuid("userGuid");
        userDO.setEnterpriseNo("enterpriseGuid");
        userDO.setAccount("account");
        userDO.setPassword("password");
        userDO.setName("name");
        userDO.setPhone("phone");
        userDO.setDiscountThreshold(new BigDecimal("0.00"));
        userDO.setAllowanceThreshold(new BigDecimal("0.00"));
        userDO.setProductDiscountThreshold(new BigDecimal("0.00"));
        userDO.setRefundThreshold(new BigDecimal("0.00"));
        userDO.setRolesDistributable("roleGuid");
        when(mockUserMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(userDO);

        // Run the test
        final UserDTO result = userDataServiceImplUnderTest.query(userDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQuery_OrgFeignClientQueryBrandByIdListReturnsNoItems() {
        // Setup
        final UserDTO userDTO = new UserDTO();
        userDTO.setGuid("userGuid");
        userDTO.setAccount("account");
        userDTO.setPassword("password");
        userDTO.setName("name");
        final UserDataStoreRuleDTO userDataStoreRule = new UserDataStoreRuleDTO();
        final UserDataDTO userDataDTO = new UserDataDTO();
        userDataDTO.setGuid("storeGuid");
        userDataDTO.setName("name");
        userDataDTO.setIsDeleted(false);
        userDataStoreRule.setUserStoreData(Arrays.asList(userDataDTO));
        userDTO.setUserDataStoreRule(userDataStoreRule);
        final UserDataCondRuleDTO userDataCondRuleDTO = new UserDataCondRuleDTO();
        final UserDataDTO userDataDTO1 = new UserDataDTO();
        userDataDTO1.setGuid("storeGuid");
        userDataDTO1.setName("name");
        userDataDTO1.setIsDeleted(false);
        userDataCondRuleDTO.setUserOrgData(Arrays.asList(userDataDTO1));
        final UserDataDTO userDataDTO2 = new UserDataDTO();
        userDataDTO2.setGuid("storeGuid");
        userDataDTO2.setName("name");
        userDataDTO2.setIsDeleted(false);
        userDataCondRuleDTO.setUserBrandData(Arrays.asList(userDataDTO2));
        final UserRegionDTO userRegionDTO = new UserRegionDTO();
        userRegionDTO.setAdcode("regionCode");
        userRegionDTO.setName("regionName");
        userRegionDTO.setIsDeleted(false);
        userDataCondRuleDTO.setUserRegionData(Arrays.asList(userRegionDTO));
        userDTO.setUserDataCondRuleList(Arrays.asList(userDataCondRuleDTO));
        userDTO.setDiscountThreshold(new BigDecimal("0.00"));
        userDTO.setAllowanceThreshold(new BigDecimal("0.00"));
        userDTO.setProductDiscountThreshold(new BigDecimal("0.00"));
        userDTO.setRefundThreshold(new BigDecimal("0.00"));
        final UserRoleDistDTO userRoleDistDTO = new UserRoleDistDTO();
        userRoleDistDTO.setGuid("c820b147-d4ec-47c9-93f9-201cdcf6a589");
        userRoleDistDTO.setIsChecked(false);
        userDTO.setUserRolesDistributable(Arrays.asList(userRoleDistDTO));

        final UserDTO expectedResult = new UserDTO();
        expectedResult.setGuid("userGuid");
        expectedResult.setAccount("account");
        expectedResult.setPassword("password");
        expectedResult.setName("name");
        final UserDataStoreRuleDTO userDataStoreRule1 = new UserDataStoreRuleDTO();
        final UserDataDTO userDataDTO3 = new UserDataDTO();
        userDataDTO3.setGuid("storeGuid");
        userDataDTO3.setName("name");
        userDataDTO3.setIsDeleted(false);
        userDataStoreRule1.setUserStoreData(Arrays.asList(userDataDTO3));
        expectedResult.setUserDataStoreRule(userDataStoreRule1);
        final UserDataCondRuleDTO userDataCondRuleDTO1 = new UserDataCondRuleDTO();
        final UserDataDTO userDataDTO4 = new UserDataDTO();
        userDataDTO4.setGuid("storeGuid");
        userDataDTO4.setName("name");
        userDataDTO4.setIsDeleted(false);
        userDataCondRuleDTO1.setUserOrgData(Arrays.asList(userDataDTO4));
        final UserDataDTO userDataDTO5 = new UserDataDTO();
        userDataDTO5.setGuid("storeGuid");
        userDataDTO5.setName("name");
        userDataDTO5.setIsDeleted(false);
        userDataCondRuleDTO1.setUserBrandData(Arrays.asList(userDataDTO5));
        final UserRegionDTO userRegionDTO1 = new UserRegionDTO();
        userRegionDTO1.setAdcode("regionCode");
        userRegionDTO1.setName("regionName");
        userRegionDTO1.setIsDeleted(false);
        userDataCondRuleDTO1.setUserRegionData(Arrays.asList(userRegionDTO1));
        expectedResult.setUserDataCondRuleList(Arrays.asList(userDataCondRuleDTO1));
        expectedResult.setDiscountThreshold(new BigDecimal("0.00"));
        expectedResult.setAllowanceThreshold(new BigDecimal("0.00"));
        expectedResult.setProductDiscountThreshold(new BigDecimal("0.00"));
        expectedResult.setRefundThreshold(new BigDecimal("0.00"));
        final UserRoleDistDTO userRoleDistDTO1 = new UserRoleDistDTO();
        userRoleDistDTO1.setGuid("c820b147-d4ec-47c9-93f9-201cdcf6a589");
        userRoleDistDTO1.setIsChecked(false);
        expectedResult.setUserRolesDistributable(Arrays.asList(userRoleDistDTO1));

        // Configure OrgFeignClient.queryStoreByGuidList(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("storeGuid");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setProvinceCode("provinceCode");
        storeDTO.setProvinceName("provinceName");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setCountyCode("countyCode");
        storeDTO.setCountyName("countyName");
        storeDTO.setIsDeleted(false);
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO);
        when(mockOrgFeignClient.queryStoreByGuidList(Arrays.asList("value"))).thenReturn(storeDTOS);

        // Configure OrgFeignClient.queryOrgNameByIdList(...).
        final OrganizationDTO organizationDTO = new OrganizationDTO();
        organizationDTO.setGuid("storeGuid");
        organizationDTO.setUuid("2a5992e2-e91c-416d-9be3-4be89dae8554");
        organizationDTO.setName("name");
        organizationDTO.setContactName("contactName");
        organizationDTO.setIsDeleted(false);
        final List<OrganizationDTO> organizationDTOS = Arrays.asList(organizationDTO);
        when(mockOrgFeignClient.queryOrgNameByIdList(Arrays.asList("value"))).thenReturn(organizationDTOS);

        when(mockOrgFeignClient.queryBrandByIdList(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Configure RoleMapper.selectList(...).
        final RoleDO roleDO = new RoleDO();
        roleDO.setId(0L);
        roleDO.setGuid("********-3e56-412f-8ded-e8f75f628b26");
        roleDO.setName("name");
        roleDO.setIsEnable(false);
        roleDO.setIsDeleted(false);
        final List<RoleDO> roleDOS = Arrays.asList(roleDO);
        when(mockRoleMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(roleDOS);

        // Configure UserMapper.selectOne(...).
        final UserDO userDO = new UserDO();
        userDO.setGuid("userGuid");
        userDO.setEnterpriseNo("enterpriseGuid");
        userDO.setAccount("account");
        userDO.setPassword("password");
        userDO.setName("name");
        userDO.setPhone("phone");
        userDO.setDiscountThreshold(new BigDecimal("0.00"));
        userDO.setAllowanceThreshold(new BigDecimal("0.00"));
        userDO.setProductDiscountThreshold(new BigDecimal("0.00"));
        userDO.setRefundThreshold(new BigDecimal("0.00"));
        userDO.setRolesDistributable("roleGuid");
        when(mockUserMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(userDO);

        // Run the test
        final UserDTO result = userDataServiceImplUnderTest.query(userDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQuery_UserMapperReturnsNull() {
        // Setup
        final UserDTO userDTO = new UserDTO();
        userDTO.setGuid("userGuid");
        userDTO.setAccount("account");
        userDTO.setPassword("password");
        userDTO.setName("name");
        final UserDataStoreRuleDTO userDataStoreRule = new UserDataStoreRuleDTO();
        final UserDataDTO userDataDTO = new UserDataDTO();
        userDataDTO.setGuid("storeGuid");
        userDataDTO.setName("name");
        userDataDTO.setIsDeleted(false);
        userDataStoreRule.setUserStoreData(Arrays.asList(userDataDTO));
        userDTO.setUserDataStoreRule(userDataStoreRule);
        final UserDataCondRuleDTO userDataCondRuleDTO = new UserDataCondRuleDTO();
        final UserDataDTO userDataDTO1 = new UserDataDTO();
        userDataDTO1.setGuid("storeGuid");
        userDataDTO1.setName("name");
        userDataDTO1.setIsDeleted(false);
        userDataCondRuleDTO.setUserOrgData(Arrays.asList(userDataDTO1));
        final UserDataDTO userDataDTO2 = new UserDataDTO();
        userDataDTO2.setGuid("storeGuid");
        userDataDTO2.setName("name");
        userDataDTO2.setIsDeleted(false);
        userDataCondRuleDTO.setUserBrandData(Arrays.asList(userDataDTO2));
        final UserRegionDTO userRegionDTO = new UserRegionDTO();
        userRegionDTO.setAdcode("regionCode");
        userRegionDTO.setName("regionName");
        userRegionDTO.setIsDeleted(false);
        userDataCondRuleDTO.setUserRegionData(Arrays.asList(userRegionDTO));
        userDTO.setUserDataCondRuleList(Arrays.asList(userDataCondRuleDTO));
        userDTO.setDiscountThreshold(new BigDecimal("0.00"));
        userDTO.setAllowanceThreshold(new BigDecimal("0.00"));
        userDTO.setProductDiscountThreshold(new BigDecimal("0.00"));
        userDTO.setRefundThreshold(new BigDecimal("0.00"));
        final UserRoleDistDTO userRoleDistDTO = new UserRoleDistDTO();
        userRoleDistDTO.setGuid("c820b147-d4ec-47c9-93f9-201cdcf6a589");
        userRoleDistDTO.setIsChecked(false);
        userDTO.setUserRolesDistributable(Arrays.asList(userRoleDistDTO));

        final UserDTO expectedResult = new UserDTO();
        expectedResult.setGuid("userGuid");
        expectedResult.setAccount("account");
        expectedResult.setPassword("password");
        expectedResult.setName("name");
        final UserDataStoreRuleDTO userDataStoreRule1 = new UserDataStoreRuleDTO();
        final UserDataDTO userDataDTO3 = new UserDataDTO();
        userDataDTO3.setGuid("storeGuid");
        userDataDTO3.setName("name");
        userDataDTO3.setIsDeleted(false);
        userDataStoreRule1.setUserStoreData(Arrays.asList(userDataDTO3));
        expectedResult.setUserDataStoreRule(userDataStoreRule1);
        final UserDataCondRuleDTO userDataCondRuleDTO1 = new UserDataCondRuleDTO();
        final UserDataDTO userDataDTO4 = new UserDataDTO();
        userDataDTO4.setGuid("storeGuid");
        userDataDTO4.setName("name");
        userDataDTO4.setIsDeleted(false);
        userDataCondRuleDTO1.setUserOrgData(Arrays.asList(userDataDTO4));
        final UserDataDTO userDataDTO5 = new UserDataDTO();
        userDataDTO5.setGuid("storeGuid");
        userDataDTO5.setName("name");
        userDataDTO5.setIsDeleted(false);
        userDataCondRuleDTO1.setUserBrandData(Arrays.asList(userDataDTO5));
        final UserRegionDTO userRegionDTO1 = new UserRegionDTO();
        userRegionDTO1.setAdcode("regionCode");
        userRegionDTO1.setName("regionName");
        userRegionDTO1.setIsDeleted(false);
        userDataCondRuleDTO1.setUserRegionData(Arrays.asList(userRegionDTO1));
        expectedResult.setUserDataCondRuleList(Arrays.asList(userDataCondRuleDTO1));
        expectedResult.setDiscountThreshold(new BigDecimal("0.00"));
        expectedResult.setAllowanceThreshold(new BigDecimal("0.00"));
        expectedResult.setProductDiscountThreshold(new BigDecimal("0.00"));
        expectedResult.setRefundThreshold(new BigDecimal("0.00"));
        final UserRoleDistDTO userRoleDistDTO1 = new UserRoleDistDTO();
        userRoleDistDTO1.setGuid("c820b147-d4ec-47c9-93f9-201cdcf6a589");
        userRoleDistDTO1.setIsChecked(false);
        expectedResult.setUserRolesDistributable(Arrays.asList(userRoleDistDTO1));

        // Configure OrgFeignClient.queryStoreByGuidList(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("storeGuid");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setProvinceCode("provinceCode");
        storeDTO.setProvinceName("provinceName");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setCountyCode("countyCode");
        storeDTO.setCountyName("countyName");
        storeDTO.setIsDeleted(false);
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO);
        when(mockOrgFeignClient.queryStoreByGuidList(Arrays.asList("value"))).thenReturn(storeDTOS);

        // Configure OrgFeignClient.queryOrgNameByIdList(...).
        final OrganizationDTO organizationDTO = new OrganizationDTO();
        organizationDTO.setGuid("storeGuid");
        organizationDTO.setUuid("2a5992e2-e91c-416d-9be3-4be89dae8554");
        organizationDTO.setName("name");
        organizationDTO.setContactName("contactName");
        organizationDTO.setIsDeleted(false);
        final List<OrganizationDTO> organizationDTOS = Arrays.asList(organizationDTO);
        when(mockOrgFeignClient.queryOrgNameByIdList(Arrays.asList("value"))).thenReturn(organizationDTOS);

        // Configure OrgFeignClient.queryBrandByIdList(...).
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("storeGuid");
        brandDTO.setUuid("0db9815e-f80b-41a5-8ce4-7ff2f7e03e87");
        brandDTO.setName("name");
        brandDTO.setDescription("description");
        brandDTO.setIsDeleted(false);
        final List<BrandDTO> brandDTOS = Arrays.asList(brandDTO);
        when(mockOrgFeignClient.queryBrandByIdList(Arrays.asList("value"))).thenReturn(brandDTOS);

        when(mockUserMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Configure RoleMapper.selectList(...).
        final RoleDO roleDO = new RoleDO();
        roleDO.setId(0L);
        roleDO.setGuid("********-3e56-412f-8ded-e8f75f628b26");
        roleDO.setName("name");
        roleDO.setIsEnable(false);
        roleDO.setIsDeleted(false);
        final List<RoleDO> roleDOS = Arrays.asList(roleDO);
        when(mockRoleMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(roleDOS);

        // Run the test
        final UserDTO result = userDataServiceImplUnderTest.query(userDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testUpdate() {
        // Setup
        final UserDTO userDTO = new UserDTO();
        userDTO.setGuid("userGuid");
        userDTO.setAccount("account");
        userDTO.setPassword("password");
        userDTO.setName("name");
        final UserDataStoreRuleDTO userDataStoreRule = new UserDataStoreRuleDTO();
        final UserDataDTO userDataDTO = new UserDataDTO();
        userDataDTO.setGuid("storeGuid");
        userDataDTO.setName("name");
        userDataDTO.setIsDeleted(false);
        userDataStoreRule.setUserStoreData(Arrays.asList(userDataDTO));
        userDTO.setUserDataStoreRule(userDataStoreRule);
        final UserDataCondRuleDTO userDataCondRuleDTO = new UserDataCondRuleDTO();
        final UserDataDTO userDataDTO1 = new UserDataDTO();
        userDataDTO1.setGuid("storeGuid");
        userDataDTO1.setName("name");
        userDataDTO1.setIsDeleted(false);
        userDataCondRuleDTO.setUserOrgData(Arrays.asList(userDataDTO1));
        final UserDataDTO userDataDTO2 = new UserDataDTO();
        userDataDTO2.setGuid("storeGuid");
        userDataDTO2.setName("name");
        userDataDTO2.setIsDeleted(false);
        userDataCondRuleDTO.setUserBrandData(Arrays.asList(userDataDTO2));
        final UserRegionDTO userRegionDTO = new UserRegionDTO();
        userRegionDTO.setAdcode("regionCode");
        userRegionDTO.setName("regionName");
        userRegionDTO.setIsDeleted(false);
        userDataCondRuleDTO.setUserRegionData(Arrays.asList(userRegionDTO));
        userDTO.setUserDataCondRuleList(Arrays.asList(userDataCondRuleDTO));
        userDTO.setDiscountThreshold(new BigDecimal("0.00"));
        userDTO.setAllowanceThreshold(new BigDecimal("0.00"));
        userDTO.setProductDiscountThreshold(new BigDecimal("0.00"));
        userDTO.setRefundThreshold(new BigDecimal("0.00"));
        final UserRoleDistDTO userRoleDistDTO = new UserRoleDistDTO();
        userRoleDistDTO.setGuid("c820b147-d4ec-47c9-93f9-201cdcf6a589");
        userRoleDistDTO.setIsChecked(false);
        userDTO.setUserRolesDistributable(Arrays.asList(userRoleDistDTO));

        when(mockDistributedService.nextBatchUserDataGuid(0L)).thenReturn(Arrays.asList("value"));

        // Run the test
        userDataServiceImplUnderTest.update(userDTO);

        // Verify the results
        // Confirm UserMapper.update(...).
        final UserDO entity = new UserDO();
        entity.setGuid("userGuid");
        entity.setEnterpriseNo("enterpriseGuid");
        entity.setAccount("account");
        entity.setPassword("password");
        entity.setName("name");
        entity.setPhone("phone");
        entity.setDiscountThreshold(new BigDecimal("0.00"));
        entity.setAllowanceThreshold(new BigDecimal("0.00"));
        entity.setProductDiscountThreshold(new BigDecimal("0.00"));
        entity.setRefundThreshold(new BigDecimal("0.00"));
        entity.setRolesDistributable("roleGuid");
        verify(mockUserMapper).update(eq(entity), any(LambdaQueryWrapper.class));
    }

    @Test
    public void testUpdate_DistributedServiceReturnsNoItems() {
        // Setup
        final UserDTO userDTO = new UserDTO();
        userDTO.setGuid("userGuid");
        userDTO.setAccount("account");
        userDTO.setPassword("password");
        userDTO.setName("name");
        final UserDataStoreRuleDTO userDataStoreRule = new UserDataStoreRuleDTO();
        final UserDataDTO userDataDTO = new UserDataDTO();
        userDataDTO.setGuid("storeGuid");
        userDataDTO.setName("name");
        userDataDTO.setIsDeleted(false);
        userDataStoreRule.setUserStoreData(Arrays.asList(userDataDTO));
        userDTO.setUserDataStoreRule(userDataStoreRule);
        final UserDataCondRuleDTO userDataCondRuleDTO = new UserDataCondRuleDTO();
        final UserDataDTO userDataDTO1 = new UserDataDTO();
        userDataDTO1.setGuid("storeGuid");
        userDataDTO1.setName("name");
        userDataDTO1.setIsDeleted(false);
        userDataCondRuleDTO.setUserOrgData(Arrays.asList(userDataDTO1));
        final UserDataDTO userDataDTO2 = new UserDataDTO();
        userDataDTO2.setGuid("storeGuid");
        userDataDTO2.setName("name");
        userDataDTO2.setIsDeleted(false);
        userDataCondRuleDTO.setUserBrandData(Arrays.asList(userDataDTO2));
        final UserRegionDTO userRegionDTO = new UserRegionDTO();
        userRegionDTO.setAdcode("regionCode");
        userRegionDTO.setName("regionName");
        userRegionDTO.setIsDeleted(false);
        userDataCondRuleDTO.setUserRegionData(Arrays.asList(userRegionDTO));
        userDTO.setUserDataCondRuleList(Arrays.asList(userDataCondRuleDTO));
        userDTO.setDiscountThreshold(new BigDecimal("0.00"));
        userDTO.setAllowanceThreshold(new BigDecimal("0.00"));
        userDTO.setProductDiscountThreshold(new BigDecimal("0.00"));
        userDTO.setRefundThreshold(new BigDecimal("0.00"));
        final UserRoleDistDTO userRoleDistDTO = new UserRoleDistDTO();
        userRoleDistDTO.setGuid("c820b147-d4ec-47c9-93f9-201cdcf6a589");
        userRoleDistDTO.setIsChecked(false);
        userDTO.setUserRolesDistributable(Arrays.asList(userRoleDistDTO));

        when(mockDistributedService.nextBatchUserDataGuid(0L)).thenReturn(Collections.emptyList());

        // Run the test
        userDataServiceImplUnderTest.update(userDTO);

        // Verify the results
        // Confirm UserMapper.update(...).
        final UserDO entity = new UserDO();
        entity.setGuid("userGuid");
        entity.setEnterpriseNo("enterpriseGuid");
        entity.setAccount("account");
        entity.setPassword("password");
        entity.setName("name");
        entity.setPhone("phone");
        entity.setDiscountThreshold(new BigDecimal("0.00"));
        entity.setAllowanceThreshold(new BigDecimal("0.00"));
        entity.setProductDiscountThreshold(new BigDecimal("0.00"));
        entity.setRefundThreshold(new BigDecimal("0.00"));
        entity.setRolesDistributable("roleGuid");
        verify(mockUserMapper).update(eq(entity), any(LambdaQueryWrapper.class));
    }

    @Test
    public void testDeleteUserDataRules() {
        // Setup
        // Run the test
        userDataServiceImplUnderTest.deleteUserDataRules("userGuid");

        // Verify the results
    }

    @Test
    public void testQueryUserRolesDistributable() {
        // Setup
        final UserDTO expectedResult = new UserDTO();
        expectedResult.setGuid("userGuid");
        expectedResult.setAccount("account");
        expectedResult.setPassword("password");
        expectedResult.setName("name");
        final UserDataStoreRuleDTO userDataStoreRule = new UserDataStoreRuleDTO();
        final UserDataDTO userDataDTO = new UserDataDTO();
        userDataDTO.setGuid("storeGuid");
        userDataDTO.setName("name");
        userDataDTO.setIsDeleted(false);
        userDataStoreRule.setUserStoreData(Arrays.asList(userDataDTO));
        expectedResult.setUserDataStoreRule(userDataStoreRule);
        final UserDataCondRuleDTO userDataCondRuleDTO = new UserDataCondRuleDTO();
        final UserDataDTO userDataDTO1 = new UserDataDTO();
        userDataDTO1.setGuid("storeGuid");
        userDataDTO1.setName("name");
        userDataDTO1.setIsDeleted(false);
        userDataCondRuleDTO.setUserOrgData(Arrays.asList(userDataDTO1));
        final UserDataDTO userDataDTO2 = new UserDataDTO();
        userDataDTO2.setGuid("storeGuid");
        userDataDTO2.setName("name");
        userDataDTO2.setIsDeleted(false);
        userDataCondRuleDTO.setUserBrandData(Arrays.asList(userDataDTO2));
        final UserRegionDTO userRegionDTO = new UserRegionDTO();
        userRegionDTO.setAdcode("regionCode");
        userRegionDTO.setName("regionName");
        userRegionDTO.setIsDeleted(false);
        userDataCondRuleDTO.setUserRegionData(Arrays.asList(userRegionDTO));
        expectedResult.setUserDataCondRuleList(Arrays.asList(userDataCondRuleDTO));
        expectedResult.setDiscountThreshold(new BigDecimal("0.00"));
        expectedResult.setAllowanceThreshold(new BigDecimal("0.00"));
        expectedResult.setProductDiscountThreshold(new BigDecimal("0.00"));
        expectedResult.setRefundThreshold(new BigDecimal("0.00"));
        final UserRoleDistDTO userRoleDistDTO = new UserRoleDistDTO();
        userRoleDistDTO.setGuid("c820b147-d4ec-47c9-93f9-201cdcf6a589");
        userRoleDistDTO.setIsChecked(false);
        expectedResult.setUserRolesDistributable(Arrays.asList(userRoleDistDTO));

        // Configure RoleMapper.selectList(...).
        final RoleDO roleDO = new RoleDO();
        roleDO.setId(0L);
        roleDO.setGuid("********-3e56-412f-8ded-e8f75f628b26");
        roleDO.setName("name");
        roleDO.setIsEnable(false);
        roleDO.setIsDeleted(false);
        final List<RoleDO> roleDOS = Arrays.asList(roleDO);
        when(mockRoleMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(roleDOS);

        // Configure UserMapper.selectOne(...).
        final UserDO userDO = new UserDO();
        userDO.setGuid("userGuid");
        userDO.setEnterpriseNo("enterpriseGuid");
        userDO.setAccount("account");
        userDO.setPassword("password");
        userDO.setName("name");
        userDO.setPhone("phone");
        userDO.setDiscountThreshold(new BigDecimal("0.00"));
        userDO.setAllowanceThreshold(new BigDecimal("0.00"));
        userDO.setProductDiscountThreshold(new BigDecimal("0.00"));
        userDO.setRefundThreshold(new BigDecimal("0.00"));
        userDO.setRolesDistributable("roleGuid");
        when(mockUserMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(userDO);

        // Run the test
        final UserDTO result = userDataServiceImplUnderTest.queryUserRolesDistributable();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryUserRolesDistributable_RoleMapperReturnsNoItems() {
        // Setup
        final UserDTO expectedResult = new UserDTO();
        expectedResult.setGuid("userGuid");
        expectedResult.setAccount("account");
        expectedResult.setPassword("password");
        expectedResult.setName("name");
        final UserDataStoreRuleDTO userDataStoreRule = new UserDataStoreRuleDTO();
        final UserDataDTO userDataDTO = new UserDataDTO();
        userDataDTO.setGuid("storeGuid");
        userDataDTO.setName("name");
        userDataDTO.setIsDeleted(false);
        userDataStoreRule.setUserStoreData(Arrays.asList(userDataDTO));
        expectedResult.setUserDataStoreRule(userDataStoreRule);
        final UserDataCondRuleDTO userDataCondRuleDTO = new UserDataCondRuleDTO();
        final UserDataDTO userDataDTO1 = new UserDataDTO();
        userDataDTO1.setGuid("storeGuid");
        userDataDTO1.setName("name");
        userDataDTO1.setIsDeleted(false);
        userDataCondRuleDTO.setUserOrgData(Arrays.asList(userDataDTO1));
        final UserDataDTO userDataDTO2 = new UserDataDTO();
        userDataDTO2.setGuid("storeGuid");
        userDataDTO2.setName("name");
        userDataDTO2.setIsDeleted(false);
        userDataCondRuleDTO.setUserBrandData(Arrays.asList(userDataDTO2));
        final UserRegionDTO userRegionDTO = new UserRegionDTO();
        userRegionDTO.setAdcode("regionCode");
        userRegionDTO.setName("regionName");
        userRegionDTO.setIsDeleted(false);
        userDataCondRuleDTO.setUserRegionData(Arrays.asList(userRegionDTO));
        expectedResult.setUserDataCondRuleList(Arrays.asList(userDataCondRuleDTO));
        expectedResult.setDiscountThreshold(new BigDecimal("0.00"));
        expectedResult.setAllowanceThreshold(new BigDecimal("0.00"));
        expectedResult.setProductDiscountThreshold(new BigDecimal("0.00"));
        expectedResult.setRefundThreshold(new BigDecimal("0.00"));
        final UserRoleDistDTO userRoleDistDTO = new UserRoleDistDTO();
        userRoleDistDTO.setGuid("c820b147-d4ec-47c9-93f9-201cdcf6a589");
        userRoleDistDTO.setIsChecked(false);
        expectedResult.setUserRolesDistributable(Arrays.asList(userRoleDistDTO));

        when(mockRoleMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final UserDTO result = userDataServiceImplUnderTest.queryUserRolesDistributable();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryUserRolesDistributable_UserMapperReturnsNull() {
        // Setup
        final UserDTO expectedResult = new UserDTO();
        expectedResult.setGuid("userGuid");
        expectedResult.setAccount("account");
        expectedResult.setPassword("password");
        expectedResult.setName("name");
        final UserDataStoreRuleDTO userDataStoreRule = new UserDataStoreRuleDTO();
        final UserDataDTO userDataDTO = new UserDataDTO();
        userDataDTO.setGuid("storeGuid");
        userDataDTO.setName("name");
        userDataDTO.setIsDeleted(false);
        userDataStoreRule.setUserStoreData(Arrays.asList(userDataDTO));
        expectedResult.setUserDataStoreRule(userDataStoreRule);
        final UserDataCondRuleDTO userDataCondRuleDTO = new UserDataCondRuleDTO();
        final UserDataDTO userDataDTO1 = new UserDataDTO();
        userDataDTO1.setGuid("storeGuid");
        userDataDTO1.setName("name");
        userDataDTO1.setIsDeleted(false);
        userDataCondRuleDTO.setUserOrgData(Arrays.asList(userDataDTO1));
        final UserDataDTO userDataDTO2 = new UserDataDTO();
        userDataDTO2.setGuid("storeGuid");
        userDataDTO2.setName("name");
        userDataDTO2.setIsDeleted(false);
        userDataCondRuleDTO.setUserBrandData(Arrays.asList(userDataDTO2));
        final UserRegionDTO userRegionDTO = new UserRegionDTO();
        userRegionDTO.setAdcode("regionCode");
        userRegionDTO.setName("regionName");
        userRegionDTO.setIsDeleted(false);
        userDataCondRuleDTO.setUserRegionData(Arrays.asList(userRegionDTO));
        expectedResult.setUserDataCondRuleList(Arrays.asList(userDataCondRuleDTO));
        expectedResult.setDiscountThreshold(new BigDecimal("0.00"));
        expectedResult.setAllowanceThreshold(new BigDecimal("0.00"));
        expectedResult.setProductDiscountThreshold(new BigDecimal("0.00"));
        expectedResult.setRefundThreshold(new BigDecimal("0.00"));
        final UserRoleDistDTO userRoleDistDTO = new UserRoleDistDTO();
        userRoleDistDTO.setGuid("c820b147-d4ec-47c9-93f9-201cdcf6a589");
        userRoleDistDTO.setIsChecked(false);
        expectedResult.setUserRolesDistributable(Arrays.asList(userRoleDistDTO));

        when(mockUserMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Run the test
        final UserDTO result = userDataServiceImplUnderTest.queryUserRolesDistributable();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryUserDataThreshold() {
        // Setup
        final UserDTO expectedResult = new UserDTO();
        expectedResult.setGuid("userGuid");
        expectedResult.setAccount("account");
        expectedResult.setPassword("password");
        expectedResult.setName("name");
        final UserDataStoreRuleDTO userDataStoreRule = new UserDataStoreRuleDTO();
        final UserDataDTO userDataDTO = new UserDataDTO();
        userDataDTO.setGuid("storeGuid");
        userDataDTO.setName("name");
        userDataDTO.setIsDeleted(false);
        userDataStoreRule.setUserStoreData(Arrays.asList(userDataDTO));
        expectedResult.setUserDataStoreRule(userDataStoreRule);
        final UserDataCondRuleDTO userDataCondRuleDTO = new UserDataCondRuleDTO();
        final UserDataDTO userDataDTO1 = new UserDataDTO();
        userDataDTO1.setGuid("storeGuid");
        userDataDTO1.setName("name");
        userDataDTO1.setIsDeleted(false);
        userDataCondRuleDTO.setUserOrgData(Arrays.asList(userDataDTO1));
        final UserDataDTO userDataDTO2 = new UserDataDTO();
        userDataDTO2.setGuid("storeGuid");
        userDataDTO2.setName("name");
        userDataDTO2.setIsDeleted(false);
        userDataCondRuleDTO.setUserBrandData(Arrays.asList(userDataDTO2));
        final UserRegionDTO userRegionDTO = new UserRegionDTO();
        userRegionDTO.setAdcode("regionCode");
        userRegionDTO.setName("regionName");
        userRegionDTO.setIsDeleted(false);
        userDataCondRuleDTO.setUserRegionData(Arrays.asList(userRegionDTO));
        expectedResult.setUserDataCondRuleList(Arrays.asList(userDataCondRuleDTO));
        expectedResult.setDiscountThreshold(new BigDecimal("0.00"));
        expectedResult.setAllowanceThreshold(new BigDecimal("0.00"));
        expectedResult.setProductDiscountThreshold(new BigDecimal("0.00"));
        expectedResult.setRefundThreshold(new BigDecimal("0.00"));
        final UserRoleDistDTO userRoleDistDTO = new UserRoleDistDTO();
        userRoleDistDTO.setGuid("c820b147-d4ec-47c9-93f9-201cdcf6a589");
        userRoleDistDTO.setIsChecked(false);
        expectedResult.setUserRolesDistributable(Arrays.asList(userRoleDistDTO));

        // Configure UserMapper.selectOne(...).
        final UserDO userDO = new UserDO();
        userDO.setGuid("userGuid");
        userDO.setEnterpriseNo("enterpriseGuid");
        userDO.setAccount("account");
        userDO.setPassword("password");
        userDO.setName("name");
        userDO.setPhone("phone");
        userDO.setDiscountThreshold(new BigDecimal("0.00"));
        userDO.setAllowanceThreshold(new BigDecimal("0.00"));
        userDO.setProductDiscountThreshold(new BigDecimal("0.00"));
        userDO.setRefundThreshold(new BigDecimal("0.00"));
        userDO.setRolesDistributable("roleGuid");
        when(mockUserMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(userDO);

        // Run the test
        final UserDTO result = userDataServiceImplUnderTest.queryUserDataThreshold();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryStoreMergedSpinner() {
        // Setup
        final UserSpinnerDTO expectedResult = new UserSpinnerDTO();
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("storeGuid");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setProvinceCode("provinceCode");
        storeDTO.setProvinceName("provinceName");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setCountyCode("countyCode");
        storeDTO.setCountyName("countyName");
        storeDTO.setIsDeleted(false);
        expectedResult.setArrayOfStoreDTO(Arrays.asList(storeDTO));
        final OrgGeneralDTO orgGeneralDTO = new OrgGeneralDTO();
        expectedResult.setArrayOfOrgDTO(Arrays.asList(orgGeneralDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("storeGuid");
        brandDTO.setName("name");
        brandDTO.setIsDeleted(false);
        expectedResult.setArrayOfBrandDTO(Arrays.asList(brandDTO));

        // Configure OrgFeignClient.queryAllStore(...).
        final StoreDTO storeDTO1 = new StoreDTO();
        storeDTO1.setGuid("storeGuid");
        storeDTO1.setName("name");
        storeDTO1.setBelongBrandGuid("belongBrandGuid");
        storeDTO1.setProvinceCode("provinceCode");
        storeDTO1.setProvinceName("provinceName");
        storeDTO1.setCityCode("cityCode");
        storeDTO1.setCityName("cityName");
        storeDTO1.setCountyCode("countyCode");
        storeDTO1.setCountyName("countyName");
        storeDTO1.setIsDeleted(false);
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO1);
        when(mockOrgFeignClient.queryAllStore()).thenReturn(storeDTOS);

        // Configure OrgFeignClient.queryStoreByGuidList(...).
        final StoreDTO storeDTO2 = new StoreDTO();
        storeDTO2.setGuid("storeGuid");
        storeDTO2.setName("name");
        storeDTO2.setBelongBrandGuid("belongBrandGuid");
        storeDTO2.setProvinceCode("provinceCode");
        storeDTO2.setProvinceName("provinceName");
        storeDTO2.setCityCode("cityCode");
        storeDTO2.setCityName("cityName");
        storeDTO2.setCountyCode("countyCode");
        storeDTO2.setCountyName("countyName");
        storeDTO2.setIsDeleted(false);
        final List<StoreDTO> storeDTOS1 = Arrays.asList(storeDTO2);
        when(mockOrgFeignClient.queryStoreByGuidList(Arrays.asList("value"))).thenReturn(storeDTOS1);

        when(mockOrgFeignClient.queryAllStoreGuid()).thenReturn(Arrays.asList("value"));

        // Configure OrgFeignClient.parseByCondition(...).
        final StoreParserDTO storeParserDTO = new StoreParserDTO();
        storeParserDTO.setAllStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setRegionCodeList(Arrays.asList("value"));
        storeParserDTO.setBrandGuidList(Arrays.asList("value"));
        storeParserDTO.setOrganizationGuidList(Arrays.asList("value"));
        when(mockOrgFeignClient.parseByCondition(storeParserDTO)).thenReturn(Arrays.asList("value"));

        // Run the test
        final UserSpinnerDTO result = userDataServiceImplUnderTest.queryStoreMergedSpinner();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryStoreMergedSpinner_OrgFeignClientQueryAllStoreReturnsNoItems() {
        // Setup
        final UserSpinnerDTO expectedResult = new UserSpinnerDTO();
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("storeGuid");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setProvinceCode("provinceCode");
        storeDTO.setProvinceName("provinceName");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setCountyCode("countyCode");
        storeDTO.setCountyName("countyName");
        storeDTO.setIsDeleted(false);
        expectedResult.setArrayOfStoreDTO(Arrays.asList(storeDTO));
        final OrgGeneralDTO orgGeneralDTO = new OrgGeneralDTO();
        expectedResult.setArrayOfOrgDTO(Arrays.asList(orgGeneralDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("storeGuid");
        brandDTO.setName("name");
        brandDTO.setIsDeleted(false);
        expectedResult.setArrayOfBrandDTO(Arrays.asList(brandDTO));

        when(mockOrgFeignClient.queryAllStore()).thenReturn(Collections.emptyList());

        // Run the test
        final UserSpinnerDTO result = userDataServiceImplUnderTest.queryStoreMergedSpinner();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryStoreMergedSpinner_OrgFeignClientQueryStoreByGuidListReturnsNoItems() {
        // Setup
        final UserSpinnerDTO expectedResult = new UserSpinnerDTO();
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("storeGuid");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setProvinceCode("provinceCode");
        storeDTO.setProvinceName("provinceName");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setCountyCode("countyCode");
        storeDTO.setCountyName("countyName");
        storeDTO.setIsDeleted(false);
        expectedResult.setArrayOfStoreDTO(Arrays.asList(storeDTO));
        final OrgGeneralDTO orgGeneralDTO = new OrgGeneralDTO();
        expectedResult.setArrayOfOrgDTO(Arrays.asList(orgGeneralDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("storeGuid");
        brandDTO.setName("name");
        brandDTO.setIsDeleted(false);
        expectedResult.setArrayOfBrandDTO(Arrays.asList(brandDTO));

        when(mockOrgFeignClient.queryStoreByGuidList(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        final UserSpinnerDTO result = userDataServiceImplUnderTest.queryStoreMergedSpinner();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryStoreMergedSpinner_OrgFeignClientQueryAllStoreGuidReturnsNoItems() {
        // Setup
        final UserSpinnerDTO expectedResult = new UserSpinnerDTO();
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("storeGuid");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setProvinceCode("provinceCode");
        storeDTO.setProvinceName("provinceName");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setCountyCode("countyCode");
        storeDTO.setCountyName("countyName");
        storeDTO.setIsDeleted(false);
        expectedResult.setArrayOfStoreDTO(Arrays.asList(storeDTO));
        final OrgGeneralDTO orgGeneralDTO = new OrgGeneralDTO();
        expectedResult.setArrayOfOrgDTO(Arrays.asList(orgGeneralDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("storeGuid");
        brandDTO.setName("name");
        brandDTO.setIsDeleted(false);
        expectedResult.setArrayOfBrandDTO(Arrays.asList(brandDTO));

        when(mockOrgFeignClient.queryAllStoreGuid()).thenReturn(Collections.emptyList());

        // Run the test
        final UserSpinnerDTO result = userDataServiceImplUnderTest.queryStoreMergedSpinner();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryStoreMergedSpinner_OrgFeignClientParseByConditionReturnsNoItems() {
        // Setup
        final UserSpinnerDTO expectedResult = new UserSpinnerDTO();
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("storeGuid");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setProvinceCode("provinceCode");
        storeDTO.setProvinceName("provinceName");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setCountyCode("countyCode");
        storeDTO.setCountyName("countyName");
        storeDTO.setIsDeleted(false);
        expectedResult.setArrayOfStoreDTO(Arrays.asList(storeDTO));
        final OrgGeneralDTO orgGeneralDTO = new OrgGeneralDTO();
        expectedResult.setArrayOfOrgDTO(Arrays.asList(orgGeneralDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("storeGuid");
        brandDTO.setName("name");
        brandDTO.setIsDeleted(false);
        expectedResult.setArrayOfBrandDTO(Arrays.asList(brandDTO));

        // Configure OrgFeignClient.parseByCondition(...).
        final StoreParserDTO storeParserDTO = new StoreParserDTO();
        storeParserDTO.setAllStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setRegionCodeList(Arrays.asList("value"));
        storeParserDTO.setBrandGuidList(Arrays.asList("value"));
        storeParserDTO.setOrganizationGuidList(Arrays.asList("value"));
        when(mockOrgFeignClient.parseByCondition(storeParserDTO)).thenReturn(Collections.emptyList());

        // Configure OrgFeignClient.queryStoreByGuidList(...).
        final StoreDTO storeDTO1 = new StoreDTO();
        storeDTO1.setGuid("storeGuid");
        storeDTO1.setName("name");
        storeDTO1.setBelongBrandGuid("belongBrandGuid");
        storeDTO1.setProvinceCode("provinceCode");
        storeDTO1.setProvinceName("provinceName");
        storeDTO1.setCityCode("cityCode");
        storeDTO1.setCityName("cityName");
        storeDTO1.setCountyCode("countyCode");
        storeDTO1.setCountyName("countyName");
        storeDTO1.setIsDeleted(false);
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO1);
        when(mockOrgFeignClient.queryStoreByGuidList(Arrays.asList("value"))).thenReturn(storeDTOS);

        // Run the test
        final UserSpinnerDTO result = userDataServiceImplUnderTest.queryStoreMergedSpinner();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryStoreMergedSpinnerByBrandGuid() {
        // Setup
        final SingleDataDTO singleDataDTO = new SingleDataDTO("data", Arrays.asList("value"));
        final UserSpinnerDTO expectedResult = new UserSpinnerDTO();
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("storeGuid");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setProvinceCode("provinceCode");
        storeDTO.setProvinceName("provinceName");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setCountyCode("countyCode");
        storeDTO.setCountyName("countyName");
        storeDTO.setIsDeleted(false);
        expectedResult.setArrayOfStoreDTO(Arrays.asList(storeDTO));
        final OrgGeneralDTO orgGeneralDTO = new OrgGeneralDTO();
        expectedResult.setArrayOfOrgDTO(Arrays.asList(orgGeneralDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("storeGuid");
        brandDTO.setName("name");
        brandDTO.setIsDeleted(false);
        expectedResult.setArrayOfBrandDTO(Arrays.asList(brandDTO));

        when(mockOrgFeignClient.queryAllStoreGuid()).thenReturn(Arrays.asList("value"));

        // Configure OrgFeignClient.parseByCondition(...).
        final StoreParserDTO storeParserDTO = new StoreParserDTO();
        storeParserDTO.setAllStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setRegionCodeList(Arrays.asList("value"));
        storeParserDTO.setBrandGuidList(Arrays.asList("value"));
        storeParserDTO.setOrganizationGuidList(Arrays.asList("value"));
        when(mockOrgFeignClient.parseByCondition(storeParserDTO)).thenReturn(Arrays.asList("value"));

        // Configure OrgFeignClient.queryStoreByGuidListAndBrandId(...).
        final StoreDTO storeDTO1 = new StoreDTO();
        storeDTO1.setGuid("storeGuid");
        storeDTO1.setName("name");
        storeDTO1.setBelongBrandGuid("belongBrandGuid");
        storeDTO1.setProvinceCode("provinceCode");
        storeDTO1.setProvinceName("provinceName");
        storeDTO1.setCityCode("cityCode");
        storeDTO1.setCityName("cityName");
        storeDTO1.setCountyCode("countyCode");
        storeDTO1.setCountyName("countyName");
        storeDTO1.setIsDeleted(false);
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO1);
        when(mockOrgFeignClient.queryStoreByGuidListAndBrandId(
                new SingleDataDTO("data", Arrays.asList("value")))).thenReturn(storeDTOS);

        // Run the test
        final UserSpinnerDTO result = userDataServiceImplUnderTest.queryStoreMergedSpinnerByBrandGuid(singleDataDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryStoreMergedSpinnerByBrandGuid_OrgFeignClientQueryAllStoreGuidReturnsNoItems() {
        // Setup
        final SingleDataDTO singleDataDTO = new SingleDataDTO("data", Arrays.asList("value"));
        final UserSpinnerDTO expectedResult = new UserSpinnerDTO();
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("storeGuid");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setProvinceCode("provinceCode");
        storeDTO.setProvinceName("provinceName");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setCountyCode("countyCode");
        storeDTO.setCountyName("countyName");
        storeDTO.setIsDeleted(false);
        expectedResult.setArrayOfStoreDTO(Arrays.asList(storeDTO));
        final OrgGeneralDTO orgGeneralDTO = new OrgGeneralDTO();
        expectedResult.setArrayOfOrgDTO(Arrays.asList(orgGeneralDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("storeGuid");
        brandDTO.setName("name");
        brandDTO.setIsDeleted(false);
        expectedResult.setArrayOfBrandDTO(Arrays.asList(brandDTO));

        when(mockOrgFeignClient.queryAllStoreGuid()).thenReturn(Collections.emptyList());

        // Run the test
        final UserSpinnerDTO result = userDataServiceImplUnderTest.queryStoreMergedSpinnerByBrandGuid(singleDataDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryStoreMergedSpinnerByBrandGuid_OrgFeignClientParseByConditionReturnsNoItems() {
        // Setup
        final SingleDataDTO singleDataDTO = new SingleDataDTO("data", Arrays.asList("value"));
        final UserSpinnerDTO expectedResult = new UserSpinnerDTO();
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("storeGuid");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setProvinceCode("provinceCode");
        storeDTO.setProvinceName("provinceName");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setCountyCode("countyCode");
        storeDTO.setCountyName("countyName");
        storeDTO.setIsDeleted(false);
        expectedResult.setArrayOfStoreDTO(Arrays.asList(storeDTO));
        final OrgGeneralDTO orgGeneralDTO = new OrgGeneralDTO();
        expectedResult.setArrayOfOrgDTO(Arrays.asList(orgGeneralDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("storeGuid");
        brandDTO.setName("name");
        brandDTO.setIsDeleted(false);
        expectedResult.setArrayOfBrandDTO(Arrays.asList(brandDTO));

        // Configure OrgFeignClient.parseByCondition(...).
        final StoreParserDTO storeParserDTO = new StoreParserDTO();
        storeParserDTO.setAllStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setRegionCodeList(Arrays.asList("value"));
        storeParserDTO.setBrandGuidList(Arrays.asList("value"));
        storeParserDTO.setOrganizationGuidList(Arrays.asList("value"));
        when(mockOrgFeignClient.parseByCondition(storeParserDTO)).thenReturn(Collections.emptyList());

        // Configure OrgFeignClient.queryStoreByGuidListAndBrandId(...).
        final StoreDTO storeDTO1 = new StoreDTO();
        storeDTO1.setGuid("storeGuid");
        storeDTO1.setName("name");
        storeDTO1.setBelongBrandGuid("belongBrandGuid");
        storeDTO1.setProvinceCode("provinceCode");
        storeDTO1.setProvinceName("provinceName");
        storeDTO1.setCityCode("cityCode");
        storeDTO1.setCityName("cityName");
        storeDTO1.setCountyCode("countyCode");
        storeDTO1.setCountyName("countyName");
        storeDTO1.setIsDeleted(false);
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO1);
        when(mockOrgFeignClient.queryStoreByGuidListAndBrandId(
                new SingleDataDTO("data", Arrays.asList("value")))).thenReturn(storeDTOS);

        // Run the test
        final UserSpinnerDTO result = userDataServiceImplUnderTest.queryStoreMergedSpinnerByBrandGuid(singleDataDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryStoreMergedSpinnerByBrandGuid_OrgFeignClientQueryStoreByGuidListAndBrandIdReturnsNoItems() {
        // Setup
        final SingleDataDTO singleDataDTO = new SingleDataDTO("data", Arrays.asList("value"));
        final UserSpinnerDTO expectedResult = new UserSpinnerDTO();
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("storeGuid");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setProvinceCode("provinceCode");
        storeDTO.setProvinceName("provinceName");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setCountyCode("countyCode");
        storeDTO.setCountyName("countyName");
        storeDTO.setIsDeleted(false);
        expectedResult.setArrayOfStoreDTO(Arrays.asList(storeDTO));
        final OrgGeneralDTO orgGeneralDTO = new OrgGeneralDTO();
        expectedResult.setArrayOfOrgDTO(Arrays.asList(orgGeneralDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("storeGuid");
        brandDTO.setName("name");
        brandDTO.setIsDeleted(false);
        expectedResult.setArrayOfBrandDTO(Arrays.asList(brandDTO));

        when(mockOrgFeignClient.queryAllStoreGuid()).thenReturn(Arrays.asList("value"));
        when(mockOrgFeignClient.queryStoreByGuidListAndBrandId(
                new SingleDataDTO("data", Arrays.asList("value")))).thenReturn(Collections.emptyList());

        // Run the test
        final UserSpinnerDTO result = userDataServiceImplUnderTest.queryStoreMergedSpinnerByBrandGuid(singleDataDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryOrgMergedSpinner() {
        // Setup
        final UserSpinnerDTO expectedResult = new UserSpinnerDTO();
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("storeGuid");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setProvinceCode("provinceCode");
        storeDTO.setProvinceName("provinceName");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setCountyCode("countyCode");
        storeDTO.setCountyName("countyName");
        storeDTO.setIsDeleted(false);
        expectedResult.setArrayOfStoreDTO(Arrays.asList(storeDTO));
        final OrgGeneralDTO orgGeneralDTO = new OrgGeneralDTO();
        expectedResult.setArrayOfOrgDTO(Arrays.asList(orgGeneralDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("storeGuid");
        brandDTO.setName("name");
        brandDTO.setIsDeleted(false);
        expectedResult.setArrayOfBrandDTO(Arrays.asList(brandDTO));

        // Configure OrgFeignClient.queryErpOrgStore(...).
        final List<OrgGeneralDTO> orgGeneralDTOS = Arrays.asList(
                new OrgGeneralDTO("8165fc96-d41e-4dde-940c-eaaa5f03a066", "name", 0, false, Arrays.asList()));
        when(mockOrgFeignClient.queryErpOrgStore(1, 0)).thenReturn(orgGeneralDTOS);

        // Run the test
        final UserSpinnerDTO result = userDataServiceImplUnderTest.queryOrgMergedSpinner();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryOrgMergedSpinner_OrgFeignClientQueryErpOrgStoreReturnsNoItems() {
        // Setup
        final UserSpinnerDTO expectedResult = new UserSpinnerDTO();
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("storeGuid");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setProvinceCode("provinceCode");
        storeDTO.setProvinceName("provinceName");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setCountyCode("countyCode");
        storeDTO.setCountyName("countyName");
        storeDTO.setIsDeleted(false);
        expectedResult.setArrayOfStoreDTO(Arrays.asList(storeDTO));
        final OrgGeneralDTO orgGeneralDTO = new OrgGeneralDTO();
        expectedResult.setArrayOfOrgDTO(Arrays.asList(orgGeneralDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("storeGuid");
        brandDTO.setName("name");
        brandDTO.setIsDeleted(false);
        expectedResult.setArrayOfBrandDTO(Arrays.asList(brandDTO));

        when(mockOrgFeignClient.queryErpOrgStore(1, 0)).thenReturn(Collections.emptyList());

        // Run the test
        final UserSpinnerDTO result = userDataServiceImplUnderTest.queryOrgMergedSpinner();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryOrgMergedSpinner_OrgFeignClientQueryOrgByChildIdListReturnsNoItems() {
        // Setup
        final UserSpinnerDTO expectedResult = new UserSpinnerDTO();
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("storeGuid");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setProvinceCode("provinceCode");
        storeDTO.setProvinceName("provinceName");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setCountyCode("countyCode");
        storeDTO.setCountyName("countyName");
        storeDTO.setIsDeleted(false);
        expectedResult.setArrayOfStoreDTO(Arrays.asList(storeDTO));
        final OrgGeneralDTO orgGeneralDTO = new OrgGeneralDTO();
        expectedResult.setArrayOfOrgDTO(Arrays.asList(orgGeneralDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("storeGuid");
        brandDTO.setName("name");
        brandDTO.setIsDeleted(false);
        expectedResult.setArrayOfBrandDTO(Arrays.asList(brandDTO));

        when(mockOrgFeignClient.queryOrgByChildIdList(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        final UserSpinnerDTO result = userDataServiceImplUnderTest.queryOrgMergedSpinner();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryOrgMergedSpinner_OrgFeignClientQueryStoreDetailByGuidListReturnsNoItems() {
        // Setup
        final UserSpinnerDTO expectedResult = new UserSpinnerDTO();
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("storeGuid");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setProvinceCode("provinceCode");
        storeDTO.setProvinceName("provinceName");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setCountyCode("countyCode");
        storeDTO.setCountyName("countyName");
        storeDTO.setIsDeleted(false);
        expectedResult.setArrayOfStoreDTO(Arrays.asList(storeDTO));
        final OrgGeneralDTO orgGeneralDTO = new OrgGeneralDTO();
        expectedResult.setArrayOfOrgDTO(Arrays.asList(orgGeneralDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("storeGuid");
        brandDTO.setName("name");
        brandDTO.setIsDeleted(false);
        expectedResult.setArrayOfBrandDTO(Arrays.asList(brandDTO));

        when(mockOrgFeignClient.queryStoreDetailByGuidList(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Configure OrgFeignClient.queryErpOrgStore(...).
        final List<OrgGeneralDTO> orgGeneralDTOS = Arrays.asList(
                new OrgGeneralDTO("8165fc96-d41e-4dde-940c-eaaa5f03a066", "name", 0, false, Arrays.asList()));
        when(mockOrgFeignClient.queryErpOrgStore(1, 0)).thenReturn(orgGeneralDTOS);

        // Configure OrgFeignClient.queryOrgByChildIdList(...).
        final List<OrgGeneralDTO> orgGeneralDTOS1 = Arrays.asList(
                new OrgGeneralDTO("8165fc96-d41e-4dde-940c-eaaa5f03a066", "name", 0, false, Arrays.asList()));
        when(mockOrgFeignClient.queryOrgByChildIdList(Arrays.asList("value"))).thenReturn(orgGeneralDTOS1);

        // Configure OrgFeignClient.parseByCondition(...).
        final StoreParserDTO storeParserDTO = new StoreParserDTO();
        storeParserDTO.setAllStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setRegionCodeList(Arrays.asList("value"));
        storeParserDTO.setBrandGuidList(Arrays.asList("value"));
        storeParserDTO.setOrganizationGuidList(Arrays.asList("value"));
        when(mockOrgFeignClient.parseByCondition(storeParserDTO)).thenReturn(Arrays.asList("value"));

        // Run the test
        final UserSpinnerDTO result = userDataServiceImplUnderTest.queryOrgMergedSpinner();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryOrgMergedSpinner_OrgFeignClientParseByConditionReturnsNoItems() {
        // Setup
        final UserSpinnerDTO expectedResult = new UserSpinnerDTO();
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("storeGuid");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setProvinceCode("provinceCode");
        storeDTO.setProvinceName("provinceName");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setCountyCode("countyCode");
        storeDTO.setCountyName("countyName");
        storeDTO.setIsDeleted(false);
        expectedResult.setArrayOfStoreDTO(Arrays.asList(storeDTO));
        final OrgGeneralDTO orgGeneralDTO = new OrgGeneralDTO();
        expectedResult.setArrayOfOrgDTO(Arrays.asList(orgGeneralDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("storeGuid");
        brandDTO.setName("name");
        brandDTO.setIsDeleted(false);
        expectedResult.setArrayOfBrandDTO(Arrays.asList(brandDTO));

        // Configure OrgFeignClient.queryStoreDetailByGuidList(...).
        final StoreDTO storeDTO1 = new StoreDTO();
        storeDTO1.setGuid("storeGuid");
        storeDTO1.setName("name");
        storeDTO1.setBelongBrandGuid("belongBrandGuid");
        storeDTO1.setProvinceCode("provinceCode");
        storeDTO1.setProvinceName("provinceName");
        storeDTO1.setCityCode("cityCode");
        storeDTO1.setCityName("cityName");
        storeDTO1.setCountyCode("countyCode");
        storeDTO1.setCountyName("countyName");
        storeDTO1.setIsDeleted(false);
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO1);
        when(mockOrgFeignClient.queryStoreDetailByGuidList(Arrays.asList("value"))).thenReturn(storeDTOS);

        // Configure OrgFeignClient.parseByCondition(...).
        final StoreParserDTO storeParserDTO = new StoreParserDTO();
        storeParserDTO.setAllStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setRegionCodeList(Arrays.asList("value"));
        storeParserDTO.setBrandGuidList(Arrays.asList("value"));
        storeParserDTO.setOrganizationGuidList(Arrays.asList("value"));
        when(mockOrgFeignClient.parseByCondition(storeParserDTO)).thenReturn(Collections.emptyList());

        // Configure OrgFeignClient.queryErpOrgStore(...).
        final List<OrgGeneralDTO> orgGeneralDTOS = Arrays.asList(
                new OrgGeneralDTO("8165fc96-d41e-4dde-940c-eaaa5f03a066", "name", 0, false, Arrays.asList()));
        when(mockOrgFeignClient.queryErpOrgStore(1, 0)).thenReturn(orgGeneralDTOS);

        // Configure OrgFeignClient.queryOrgByChildIdList(...).
        final List<OrgGeneralDTO> orgGeneralDTOS1 = Arrays.asList(
                new OrgGeneralDTO("8165fc96-d41e-4dde-940c-eaaa5f03a066", "name", 0, false, Arrays.asList()));
        when(mockOrgFeignClient.queryOrgByChildIdList(Arrays.asList("value"))).thenReturn(orgGeneralDTOS1);

        // Run the test
        final UserSpinnerDTO result = userDataServiceImplUnderTest.queryOrgMergedSpinner();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryBrandMergedSpinner() {
        // Setup
        final UserSpinnerDTO expectedResult = new UserSpinnerDTO();
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("storeGuid");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setProvinceCode("provinceCode");
        storeDTO.setProvinceName("provinceName");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setCountyCode("countyCode");
        storeDTO.setCountyName("countyName");
        storeDTO.setIsDeleted(false);
        expectedResult.setArrayOfStoreDTO(Arrays.asList(storeDTO));
        final OrgGeneralDTO orgGeneralDTO = new OrgGeneralDTO();
        expectedResult.setArrayOfOrgDTO(Arrays.asList(orgGeneralDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("storeGuid");
        brandDTO.setName("name");
        brandDTO.setIsDeleted(false);
        expectedResult.setArrayOfBrandDTO(Arrays.asList(brandDTO));

        // Configure OrgFeignClient.queryBrandList(...).
        final BrandDTO brandDTO1 = new BrandDTO();
        brandDTO1.setGuid("storeGuid");
        brandDTO1.setUuid("0db9815e-f80b-41a5-8ce4-7ff2f7e03e87");
        brandDTO1.setName("name");
        brandDTO1.setDescription("description");
        brandDTO1.setIsDeleted(false);
        final List<BrandDTO> brandDTOS = Arrays.asList(brandDTO1);
        when(mockOrgFeignClient.queryBrandList()).thenReturn(brandDTOS);

        // Run the test
        final UserSpinnerDTO result = userDataServiceImplUnderTest.queryBrandMergedSpinner();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryBrandMergedSpinner_OrgFeignClientQueryBrandListReturnsNoItems() {
        // Setup
        final UserSpinnerDTO expectedResult = new UserSpinnerDTO();
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("storeGuid");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setProvinceCode("provinceCode");
        storeDTO.setProvinceName("provinceName");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setCountyCode("countyCode");
        storeDTO.setCountyName("countyName");
        storeDTO.setIsDeleted(false);
        expectedResult.setArrayOfStoreDTO(Arrays.asList(storeDTO));
        final OrgGeneralDTO orgGeneralDTO = new OrgGeneralDTO();
        expectedResult.setArrayOfOrgDTO(Arrays.asList(orgGeneralDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("storeGuid");
        brandDTO.setName("name");
        brandDTO.setIsDeleted(false);
        expectedResult.setArrayOfBrandDTO(Arrays.asList(brandDTO));

        when(mockOrgFeignClient.queryBrandList()).thenReturn(Collections.emptyList());

        // Run the test
        final UserSpinnerDTO result = userDataServiceImplUnderTest.queryBrandMergedSpinner();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryBrandMergedSpinner_OrgFeignClientQueryBrandByIdListReturnsNoItems() {
        // Setup
        final UserSpinnerDTO expectedResult = new UserSpinnerDTO();
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("storeGuid");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setProvinceCode("provinceCode");
        storeDTO.setProvinceName("provinceName");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setCountyCode("countyCode");
        storeDTO.setCountyName("countyName");
        storeDTO.setIsDeleted(false);
        expectedResult.setArrayOfStoreDTO(Arrays.asList(storeDTO));
        final OrgGeneralDTO orgGeneralDTO = new OrgGeneralDTO();
        expectedResult.setArrayOfOrgDTO(Arrays.asList(orgGeneralDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("storeGuid");
        brandDTO.setName("name");
        brandDTO.setIsDeleted(false);
        expectedResult.setArrayOfBrandDTO(Arrays.asList(brandDTO));

        when(mockOrgFeignClient.queryBrandByIdList(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        final UserSpinnerDTO result = userDataServiceImplUnderTest.queryBrandMergedSpinner();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryBrandMergedSpinner_OrgFeignClientQueryStoreDetailByGuidListReturnsNoItems() {
        // Setup
        final UserSpinnerDTO expectedResult = new UserSpinnerDTO();
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("storeGuid");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setProvinceCode("provinceCode");
        storeDTO.setProvinceName("provinceName");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setCountyCode("countyCode");
        storeDTO.setCountyName("countyName");
        storeDTO.setIsDeleted(false);
        expectedResult.setArrayOfStoreDTO(Arrays.asList(storeDTO));
        final OrgGeneralDTO orgGeneralDTO = new OrgGeneralDTO();
        expectedResult.setArrayOfOrgDTO(Arrays.asList(orgGeneralDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("storeGuid");
        brandDTO.setName("name");
        brandDTO.setIsDeleted(false);
        expectedResult.setArrayOfBrandDTO(Arrays.asList(brandDTO));

        when(mockOrgFeignClient.queryStoreDetailByGuidList(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Configure OrgFeignClient.queryBrandList(...).
        final BrandDTO brandDTO1 = new BrandDTO();
        brandDTO1.setGuid("storeGuid");
        brandDTO1.setUuid("0db9815e-f80b-41a5-8ce4-7ff2f7e03e87");
        brandDTO1.setName("name");
        brandDTO1.setDescription("description");
        brandDTO1.setIsDeleted(false);
        final List<BrandDTO> brandDTOS = Arrays.asList(brandDTO1);
        when(mockOrgFeignClient.queryBrandList()).thenReturn(brandDTOS);

        // Configure OrgFeignClient.queryBrandByIdList(...).
        final BrandDTO brandDTO2 = new BrandDTO();
        brandDTO2.setGuid("storeGuid");
        brandDTO2.setUuid("0db9815e-f80b-41a5-8ce4-7ff2f7e03e87");
        brandDTO2.setName("name");
        brandDTO2.setDescription("description");
        brandDTO2.setIsDeleted(false);
        final List<BrandDTO> brandDTOS1 = Arrays.asList(brandDTO2);
        when(mockOrgFeignClient.queryBrandByIdList(Arrays.asList("value"))).thenReturn(brandDTOS1);

        // Configure OrgFeignClient.parseByCondition(...).
        final StoreParserDTO storeParserDTO = new StoreParserDTO();
        storeParserDTO.setAllStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setRegionCodeList(Arrays.asList("value"));
        storeParserDTO.setBrandGuidList(Arrays.asList("value"));
        storeParserDTO.setOrganizationGuidList(Arrays.asList("value"));
        when(mockOrgFeignClient.parseByCondition(storeParserDTO)).thenReturn(Arrays.asList("value"));

        // Run the test
        final UserSpinnerDTO result = userDataServiceImplUnderTest.queryBrandMergedSpinner();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryBrandMergedSpinner_OrgFeignClientParseByConditionReturnsNoItems() {
        // Setup
        final UserSpinnerDTO expectedResult = new UserSpinnerDTO();
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("storeGuid");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setProvinceCode("provinceCode");
        storeDTO.setProvinceName("provinceName");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setCountyCode("countyCode");
        storeDTO.setCountyName("countyName");
        storeDTO.setIsDeleted(false);
        expectedResult.setArrayOfStoreDTO(Arrays.asList(storeDTO));
        final OrgGeneralDTO orgGeneralDTO = new OrgGeneralDTO();
        expectedResult.setArrayOfOrgDTO(Arrays.asList(orgGeneralDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("storeGuid");
        brandDTO.setName("name");
        brandDTO.setIsDeleted(false);
        expectedResult.setArrayOfBrandDTO(Arrays.asList(brandDTO));

        // Configure OrgFeignClient.queryStoreDetailByGuidList(...).
        final StoreDTO storeDTO1 = new StoreDTO();
        storeDTO1.setGuid("storeGuid");
        storeDTO1.setName("name");
        storeDTO1.setBelongBrandGuid("belongBrandGuid");
        storeDTO1.setProvinceCode("provinceCode");
        storeDTO1.setProvinceName("provinceName");
        storeDTO1.setCityCode("cityCode");
        storeDTO1.setCityName("cityName");
        storeDTO1.setCountyCode("countyCode");
        storeDTO1.setCountyName("countyName");
        storeDTO1.setIsDeleted(false);
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO1);
        when(mockOrgFeignClient.queryStoreDetailByGuidList(Arrays.asList("value"))).thenReturn(storeDTOS);

        // Configure OrgFeignClient.parseByCondition(...).
        final StoreParserDTO storeParserDTO = new StoreParserDTO();
        storeParserDTO.setAllStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setRegionCodeList(Arrays.asList("value"));
        storeParserDTO.setBrandGuidList(Arrays.asList("value"));
        storeParserDTO.setOrganizationGuidList(Arrays.asList("value"));
        when(mockOrgFeignClient.parseByCondition(storeParserDTO)).thenReturn(Collections.emptyList());

        // Configure OrgFeignClient.queryBrandList(...).
        final BrandDTO brandDTO1 = new BrandDTO();
        brandDTO1.setGuid("storeGuid");
        brandDTO1.setUuid("0db9815e-f80b-41a5-8ce4-7ff2f7e03e87");
        brandDTO1.setName("name");
        brandDTO1.setDescription("description");
        brandDTO1.setIsDeleted(false);
        final List<BrandDTO> brandDTOS = Arrays.asList(brandDTO1);
        when(mockOrgFeignClient.queryBrandList()).thenReturn(brandDTOS);

        // Configure OrgFeignClient.queryBrandByIdList(...).
        final BrandDTO brandDTO2 = new BrandDTO();
        brandDTO2.setGuid("storeGuid");
        brandDTO2.setUuid("0db9815e-f80b-41a5-8ce4-7ff2f7e03e87");
        brandDTO2.setName("name");
        brandDTO2.setDescription("description");
        brandDTO2.setIsDeleted(false);
        final List<BrandDTO> brandDTOS1 = Arrays.asList(brandDTO2);
        when(mockOrgFeignClient.queryBrandByIdList(Arrays.asList("value"))).thenReturn(brandDTOS1);

        // Run the test
        final UserSpinnerDTO result = userDataServiceImplUnderTest.queryBrandMergedSpinner();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryCondMergedSpinner() {
        // Setup
        final UserSpinnerDTO expectedResult = new UserSpinnerDTO();
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("storeGuid");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setProvinceCode("provinceCode");
        storeDTO.setProvinceName("provinceName");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setCountyCode("countyCode");
        storeDTO.setCountyName("countyName");
        storeDTO.setIsDeleted(false);
        expectedResult.setArrayOfStoreDTO(Arrays.asList(storeDTO));
        final OrgGeneralDTO orgGeneralDTO = new OrgGeneralDTO();
        expectedResult.setArrayOfOrgDTO(Arrays.asList(orgGeneralDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("storeGuid");
        brandDTO.setName("name");
        brandDTO.setIsDeleted(false);
        expectedResult.setArrayOfBrandDTO(Arrays.asList(brandDTO));

        // Configure OrgFeignClient.queryErpOrgStore(...).
        final List<OrgGeneralDTO> orgGeneralDTOS = Arrays.asList(
                new OrgGeneralDTO("8165fc96-d41e-4dde-940c-eaaa5f03a066", "name", 0, false, Arrays.asList()));
        when(mockOrgFeignClient.queryErpOrgStore(1, 0)).thenReturn(orgGeneralDTOS);

        // Configure OrgFeignClient.queryBrandList(...).
        final BrandDTO brandDTO1 = new BrandDTO();
        brandDTO1.setGuid("storeGuid");
        brandDTO1.setUuid("0db9815e-f80b-41a5-8ce4-7ff2f7e03e87");
        brandDTO1.setName("name");
        brandDTO1.setDescription("description");
        brandDTO1.setIsDeleted(false);
        final List<BrandDTO> brandDTOS = Arrays.asList(brandDTO1);
        when(mockOrgFeignClient.queryBrandList()).thenReturn(brandDTOS);

        // Configure OrgFeignClient.queryBrandByIdList(...).
        final BrandDTO brandDTO2 = new BrandDTO();
        brandDTO2.setGuid("storeGuid");
        brandDTO2.setUuid("0db9815e-f80b-41a5-8ce4-7ff2f7e03e87");
        brandDTO2.setName("name");
        brandDTO2.setDescription("description");
        brandDTO2.setIsDeleted(false);
        final List<BrandDTO> brandDTOS1 = Arrays.asList(brandDTO2);
        when(mockOrgFeignClient.queryBrandByIdList(Arrays.asList("value"))).thenReturn(brandDTOS1);

        // Run the test
        final UserSpinnerDTO result = userDataServiceImplUnderTest.queryCondMergedSpinner();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryCondMergedSpinner_OrgFeignClientQueryErpOrgStoreReturnsNoItems() {
        // Setup
        final UserSpinnerDTO expectedResult = new UserSpinnerDTO();
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("storeGuid");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setProvinceCode("provinceCode");
        storeDTO.setProvinceName("provinceName");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setCountyCode("countyCode");
        storeDTO.setCountyName("countyName");
        storeDTO.setIsDeleted(false);
        expectedResult.setArrayOfStoreDTO(Arrays.asList(storeDTO));
        final OrgGeneralDTO orgGeneralDTO = new OrgGeneralDTO();
        expectedResult.setArrayOfOrgDTO(Arrays.asList(orgGeneralDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("storeGuid");
        brandDTO.setName("name");
        brandDTO.setIsDeleted(false);
        expectedResult.setArrayOfBrandDTO(Arrays.asList(brandDTO));

        when(mockOrgFeignClient.queryErpOrgStore(1, 0)).thenReturn(Collections.emptyList());

        // Configure OrgFeignClient.queryBrandList(...).
        final BrandDTO brandDTO1 = new BrandDTO();
        brandDTO1.setGuid("storeGuid");
        brandDTO1.setUuid("0db9815e-f80b-41a5-8ce4-7ff2f7e03e87");
        brandDTO1.setName("name");
        brandDTO1.setDescription("description");
        brandDTO1.setIsDeleted(false);
        final List<BrandDTO> brandDTOS = Arrays.asList(brandDTO1);
        when(mockOrgFeignClient.queryBrandList()).thenReturn(brandDTOS);

        // Configure OrgFeignClient.queryBrandByIdList(...).
        final BrandDTO brandDTO2 = new BrandDTO();
        brandDTO2.setGuid("storeGuid");
        brandDTO2.setUuid("0db9815e-f80b-41a5-8ce4-7ff2f7e03e87");
        brandDTO2.setName("name");
        brandDTO2.setDescription("description");
        brandDTO2.setIsDeleted(false);
        final List<BrandDTO> brandDTOS1 = Arrays.asList(brandDTO2);
        when(mockOrgFeignClient.queryBrandByIdList(Arrays.asList("value"))).thenReturn(brandDTOS1);

        // Run the test
        final UserSpinnerDTO result = userDataServiceImplUnderTest.queryCondMergedSpinner();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryCondMergedSpinner_OrgFeignClientQueryOrgByChildIdListReturnsNoItems() {
        // Setup
        final UserSpinnerDTO expectedResult = new UserSpinnerDTO();
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("storeGuid");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setProvinceCode("provinceCode");
        storeDTO.setProvinceName("provinceName");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setCountyCode("countyCode");
        storeDTO.setCountyName("countyName");
        storeDTO.setIsDeleted(false);
        expectedResult.setArrayOfStoreDTO(Arrays.asList(storeDTO));
        final OrgGeneralDTO orgGeneralDTO = new OrgGeneralDTO();
        expectedResult.setArrayOfOrgDTO(Arrays.asList(orgGeneralDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("storeGuid");
        brandDTO.setName("name");
        brandDTO.setIsDeleted(false);
        expectedResult.setArrayOfBrandDTO(Arrays.asList(brandDTO));

        when(mockOrgFeignClient.queryOrgByChildIdList(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Configure OrgFeignClient.queryBrandList(...).
        final BrandDTO brandDTO1 = new BrandDTO();
        brandDTO1.setGuid("storeGuid");
        brandDTO1.setUuid("0db9815e-f80b-41a5-8ce4-7ff2f7e03e87");
        brandDTO1.setName("name");
        brandDTO1.setDescription("description");
        brandDTO1.setIsDeleted(false);
        final List<BrandDTO> brandDTOS = Arrays.asList(brandDTO1);
        when(mockOrgFeignClient.queryBrandList()).thenReturn(brandDTOS);

        // Configure OrgFeignClient.queryBrandByIdList(...).
        final BrandDTO brandDTO2 = new BrandDTO();
        brandDTO2.setGuid("storeGuid");
        brandDTO2.setUuid("0db9815e-f80b-41a5-8ce4-7ff2f7e03e87");
        brandDTO2.setName("name");
        brandDTO2.setDescription("description");
        brandDTO2.setIsDeleted(false);
        final List<BrandDTO> brandDTOS1 = Arrays.asList(brandDTO2);
        when(mockOrgFeignClient.queryBrandByIdList(Arrays.asList("value"))).thenReturn(brandDTOS1);

        // Run the test
        final UserSpinnerDTO result = userDataServiceImplUnderTest.queryCondMergedSpinner();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryCondMergedSpinner_OrgFeignClientQueryBrandListReturnsNoItems() {
        // Setup
        final UserSpinnerDTO expectedResult = new UserSpinnerDTO();
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("storeGuid");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setProvinceCode("provinceCode");
        storeDTO.setProvinceName("provinceName");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setCountyCode("countyCode");
        storeDTO.setCountyName("countyName");
        storeDTO.setIsDeleted(false);
        expectedResult.setArrayOfStoreDTO(Arrays.asList(storeDTO));
        final OrgGeneralDTO orgGeneralDTO = new OrgGeneralDTO();
        expectedResult.setArrayOfOrgDTO(Arrays.asList(orgGeneralDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("storeGuid");
        brandDTO.setName("name");
        brandDTO.setIsDeleted(false);
        expectedResult.setArrayOfBrandDTO(Arrays.asList(brandDTO));

        // Configure OrgFeignClient.queryErpOrgStore(...).
        final List<OrgGeneralDTO> orgGeneralDTOS = Arrays.asList(
                new OrgGeneralDTO("8165fc96-d41e-4dde-940c-eaaa5f03a066", "name", 0, false, Arrays.asList()));
        when(mockOrgFeignClient.queryErpOrgStore(1, 0)).thenReturn(orgGeneralDTOS);

        when(mockOrgFeignClient.queryBrandList()).thenReturn(Collections.emptyList());

        // Run the test
        final UserSpinnerDTO result = userDataServiceImplUnderTest.queryCondMergedSpinner();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryCondMergedSpinner_OrgFeignClientQueryBrandByIdListReturnsNoItems() {
        // Setup
        final UserSpinnerDTO expectedResult = new UserSpinnerDTO();
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("storeGuid");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setProvinceCode("provinceCode");
        storeDTO.setProvinceName("provinceName");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setCountyCode("countyCode");
        storeDTO.setCountyName("countyName");
        storeDTO.setIsDeleted(false);
        expectedResult.setArrayOfStoreDTO(Arrays.asList(storeDTO));
        final OrgGeneralDTO orgGeneralDTO = new OrgGeneralDTO();
        expectedResult.setArrayOfOrgDTO(Arrays.asList(orgGeneralDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("storeGuid");
        brandDTO.setName("name");
        brandDTO.setIsDeleted(false);
        expectedResult.setArrayOfBrandDTO(Arrays.asList(brandDTO));

        // Configure OrgFeignClient.queryErpOrgStore(...).
        final List<OrgGeneralDTO> orgGeneralDTOS = Arrays.asList(
                new OrgGeneralDTO("8165fc96-d41e-4dde-940c-eaaa5f03a066", "name", 0, false, Arrays.asList()));
        when(mockOrgFeignClient.queryErpOrgStore(1, 0)).thenReturn(orgGeneralDTOS);

        when(mockOrgFeignClient.queryBrandByIdList(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        final UserSpinnerDTO result = userDataServiceImplUnderTest.queryCondMergedSpinner();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryCondMergedSpinner_OrgFeignClientQueryStoreDetailByGuidListReturnsNoItems() {
        // Setup
        final UserSpinnerDTO expectedResult = new UserSpinnerDTO();
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("storeGuid");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setProvinceCode("provinceCode");
        storeDTO.setProvinceName("provinceName");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setCountyCode("countyCode");
        storeDTO.setCountyName("countyName");
        storeDTO.setIsDeleted(false);
        expectedResult.setArrayOfStoreDTO(Arrays.asList(storeDTO));
        final OrgGeneralDTO orgGeneralDTO = new OrgGeneralDTO();
        expectedResult.setArrayOfOrgDTO(Arrays.asList(orgGeneralDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("storeGuid");
        brandDTO.setName("name");
        brandDTO.setIsDeleted(false);
        expectedResult.setArrayOfBrandDTO(Arrays.asList(brandDTO));

        when(mockOrgFeignClient.queryStoreDetailByGuidList(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Configure OrgFeignClient.queryErpOrgStore(...).
        final List<OrgGeneralDTO> orgGeneralDTOS = Arrays.asList(
                new OrgGeneralDTO("8165fc96-d41e-4dde-940c-eaaa5f03a066", "name", 0, false, Arrays.asList()));
        when(mockOrgFeignClient.queryErpOrgStore(1, 0)).thenReturn(orgGeneralDTOS);

        // Configure OrgFeignClient.queryOrgByChildIdList(...).
        final List<OrgGeneralDTO> orgGeneralDTOS1 = Arrays.asList(
                new OrgGeneralDTO("8165fc96-d41e-4dde-940c-eaaa5f03a066", "name", 0, false, Arrays.asList()));
        when(mockOrgFeignClient.queryOrgByChildIdList(Arrays.asList("value"))).thenReturn(orgGeneralDTOS1);

        // Configure OrgFeignClient.queryBrandList(...).
        final BrandDTO brandDTO1 = new BrandDTO();
        brandDTO1.setGuid("storeGuid");
        brandDTO1.setUuid("0db9815e-f80b-41a5-8ce4-7ff2f7e03e87");
        brandDTO1.setName("name");
        brandDTO1.setDescription("description");
        brandDTO1.setIsDeleted(false);
        final List<BrandDTO> brandDTOS = Arrays.asList(brandDTO1);
        when(mockOrgFeignClient.queryBrandList()).thenReturn(brandDTOS);

        // Configure OrgFeignClient.queryBrandByIdList(...).
        final BrandDTO brandDTO2 = new BrandDTO();
        brandDTO2.setGuid("storeGuid");
        brandDTO2.setUuid("0db9815e-f80b-41a5-8ce4-7ff2f7e03e87");
        brandDTO2.setName("name");
        brandDTO2.setDescription("description");
        brandDTO2.setIsDeleted(false);
        final List<BrandDTO> brandDTOS1 = Arrays.asList(brandDTO2);
        when(mockOrgFeignClient.queryBrandByIdList(Arrays.asList("value"))).thenReturn(brandDTOS1);

        // Configure OrgFeignClient.parseByCondition(...).
        final StoreParserDTO storeParserDTO = new StoreParserDTO();
        storeParserDTO.setAllStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setRegionCodeList(Arrays.asList("value"));
        storeParserDTO.setBrandGuidList(Arrays.asList("value"));
        storeParserDTO.setOrganizationGuidList(Arrays.asList("value"));
        when(mockOrgFeignClient.parseByCondition(storeParserDTO)).thenReturn(Arrays.asList("value"));

        // Run the test
        final UserSpinnerDTO result = userDataServiceImplUnderTest.queryCondMergedSpinner();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryCondMergedSpinner_OrgFeignClientParseByConditionReturnsNoItems() {
        // Setup
        final UserSpinnerDTO expectedResult = new UserSpinnerDTO();
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("storeGuid");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setProvinceCode("provinceCode");
        storeDTO.setProvinceName("provinceName");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setCountyCode("countyCode");
        storeDTO.setCountyName("countyName");
        storeDTO.setIsDeleted(false);
        expectedResult.setArrayOfStoreDTO(Arrays.asList(storeDTO));
        final OrgGeneralDTO orgGeneralDTO = new OrgGeneralDTO();
        expectedResult.setArrayOfOrgDTO(Arrays.asList(orgGeneralDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("storeGuid");
        brandDTO.setName("name");
        brandDTO.setIsDeleted(false);
        expectedResult.setArrayOfBrandDTO(Arrays.asList(brandDTO));

        // Configure OrgFeignClient.queryStoreDetailByGuidList(...).
        final StoreDTO storeDTO1 = new StoreDTO();
        storeDTO1.setGuid("storeGuid");
        storeDTO1.setName("name");
        storeDTO1.setBelongBrandGuid("belongBrandGuid");
        storeDTO1.setProvinceCode("provinceCode");
        storeDTO1.setProvinceName("provinceName");
        storeDTO1.setCityCode("cityCode");
        storeDTO1.setCityName("cityName");
        storeDTO1.setCountyCode("countyCode");
        storeDTO1.setCountyName("countyName");
        storeDTO1.setIsDeleted(false);
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO1);
        when(mockOrgFeignClient.queryStoreDetailByGuidList(Arrays.asList("value"))).thenReturn(storeDTOS);

        // Configure OrgFeignClient.parseByCondition(...).
        final StoreParserDTO storeParserDTO = new StoreParserDTO();
        storeParserDTO.setAllStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setRegionCodeList(Arrays.asList("value"));
        storeParserDTO.setBrandGuidList(Arrays.asList("value"));
        storeParserDTO.setOrganizationGuidList(Arrays.asList("value"));
        when(mockOrgFeignClient.parseByCondition(storeParserDTO)).thenReturn(Collections.emptyList());

        // Configure OrgFeignClient.queryErpOrgStore(...).
        final List<OrgGeneralDTO> orgGeneralDTOS = Arrays.asList(
                new OrgGeneralDTO("8165fc96-d41e-4dde-940c-eaaa5f03a066", "name", 0, false, Arrays.asList()));
        when(mockOrgFeignClient.queryErpOrgStore(1, 0)).thenReturn(orgGeneralDTOS);

        // Configure OrgFeignClient.queryOrgByChildIdList(...).
        final List<OrgGeneralDTO> orgGeneralDTOS1 = Arrays.asList(
                new OrgGeneralDTO("8165fc96-d41e-4dde-940c-eaaa5f03a066", "name", 0, false, Arrays.asList()));
        when(mockOrgFeignClient.queryOrgByChildIdList(Arrays.asList("value"))).thenReturn(orgGeneralDTOS1);

        // Configure OrgFeignClient.queryBrandList(...).
        final BrandDTO brandDTO1 = new BrandDTO();
        brandDTO1.setGuid("storeGuid");
        brandDTO1.setUuid("0db9815e-f80b-41a5-8ce4-7ff2f7e03e87");
        brandDTO1.setName("name");
        brandDTO1.setDescription("description");
        brandDTO1.setIsDeleted(false);
        final List<BrandDTO> brandDTOS = Arrays.asList(brandDTO1);
        when(mockOrgFeignClient.queryBrandList()).thenReturn(brandDTOS);

        // Configure OrgFeignClient.queryBrandByIdList(...).
        final BrandDTO brandDTO2 = new BrandDTO();
        brandDTO2.setGuid("storeGuid");
        brandDTO2.setUuid("0db9815e-f80b-41a5-8ce4-7ff2f7e03e87");
        brandDTO2.setName("name");
        brandDTO2.setDescription("description");
        brandDTO2.setIsDeleted(false);
        final List<BrandDTO> brandDTOS1 = Arrays.asList(brandDTO2);
        when(mockOrgFeignClient.queryBrandByIdList(Arrays.asList("value"))).thenReturn(brandDTOS1);

        // Run the test
        final UserSpinnerDTO result = userDataServiceImplUnderTest.queryCondMergedSpinner();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryStoreAndCondSpinner() {
        // Setup
        final UserSpinnerDTO expectedResult = new UserSpinnerDTO();
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("storeGuid");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setProvinceCode("provinceCode");
        storeDTO.setProvinceName("provinceName");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setCountyCode("countyCode");
        storeDTO.setCountyName("countyName");
        storeDTO.setIsDeleted(false);
        expectedResult.setArrayOfStoreDTO(Arrays.asList(storeDTO));
        final OrgGeneralDTO orgGeneralDTO = new OrgGeneralDTO();
        expectedResult.setArrayOfOrgDTO(Arrays.asList(orgGeneralDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("storeGuid");
        brandDTO.setName("name");
        brandDTO.setIsDeleted(false);
        expectedResult.setArrayOfBrandDTO(Arrays.asList(brandDTO));

        // Configure OrgFeignClient.queryAllStore(...).
        final StoreDTO storeDTO1 = new StoreDTO();
        storeDTO1.setGuid("storeGuid");
        storeDTO1.setName("name");
        storeDTO1.setBelongBrandGuid("belongBrandGuid");
        storeDTO1.setProvinceCode("provinceCode");
        storeDTO1.setProvinceName("provinceName");
        storeDTO1.setCityCode("cityCode");
        storeDTO1.setCityName("cityName");
        storeDTO1.setCountyCode("countyCode");
        storeDTO1.setCountyName("countyName");
        storeDTO1.setIsDeleted(false);
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO1);
        when(mockOrgFeignClient.queryAllStore()).thenReturn(storeDTOS);

        // Configure OrgFeignClient.queryErpOrgStore(...).
        final List<OrgGeneralDTO> orgGeneralDTOS = Arrays.asList(
                new OrgGeneralDTO("8165fc96-d41e-4dde-940c-eaaa5f03a066", "name", 0, false, Arrays.asList()));
        when(mockOrgFeignClient.queryErpOrgStore(1, 0)).thenReturn(orgGeneralDTOS);

        // Configure OrgFeignClient.queryOrgByChildIdList(...).
        final List<OrgGeneralDTO> orgGeneralDTOS1 = Arrays.asList(
                new OrgGeneralDTO("8165fc96-d41e-4dde-940c-eaaa5f03a066", "name", 0, false, Arrays.asList()));
        when(mockOrgFeignClient.queryOrgByChildIdList(Arrays.asList("value"))).thenReturn(orgGeneralDTOS1);

        // Configure OrgFeignClient.queryBrandList(...).
        final BrandDTO brandDTO1 = new BrandDTO();
        brandDTO1.setGuid("storeGuid");
        brandDTO1.setUuid("0db9815e-f80b-41a5-8ce4-7ff2f7e03e87");
        brandDTO1.setName("name");
        brandDTO1.setDescription("description");
        brandDTO1.setIsDeleted(false);
        final List<BrandDTO> brandDTOS = Arrays.asList(brandDTO1);
        when(mockOrgFeignClient.queryBrandList()).thenReturn(brandDTOS);

        // Configure OrgFeignClient.queryBrandByIdList(...).
        final BrandDTO brandDTO2 = new BrandDTO();
        brandDTO2.setGuid("storeGuid");
        brandDTO2.setUuid("0db9815e-f80b-41a5-8ce4-7ff2f7e03e87");
        brandDTO2.setName("name");
        brandDTO2.setDescription("description");
        brandDTO2.setIsDeleted(false);
        final List<BrandDTO> brandDTOS1 = Arrays.asList(brandDTO2);
        when(mockOrgFeignClient.queryBrandByIdList(Arrays.asList("value"))).thenReturn(brandDTOS1);

        // Run the test
        final UserSpinnerDTO result = userDataServiceImplUnderTest.queryStoreAndCondSpinner();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryStoreAndCondSpinner_OrgFeignClientQueryAllStoreReturnsNoItems() {
        // Setup
        final UserSpinnerDTO expectedResult = new UserSpinnerDTO();
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("storeGuid");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setProvinceCode("provinceCode");
        storeDTO.setProvinceName("provinceName");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setCountyCode("countyCode");
        storeDTO.setCountyName("countyName");
        storeDTO.setIsDeleted(false);
        expectedResult.setArrayOfStoreDTO(Arrays.asList(storeDTO));
        final OrgGeneralDTO orgGeneralDTO = new OrgGeneralDTO();
        expectedResult.setArrayOfOrgDTO(Arrays.asList(orgGeneralDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("storeGuid");
        brandDTO.setName("name");
        brandDTO.setIsDeleted(false);
        expectedResult.setArrayOfBrandDTO(Arrays.asList(brandDTO));

        when(mockOrgFeignClient.queryAllStore()).thenReturn(Collections.emptyList());

        // Configure OrgFeignClient.queryErpOrgStore(...).
        final List<OrgGeneralDTO> orgGeneralDTOS = Arrays.asList(
                new OrgGeneralDTO("8165fc96-d41e-4dde-940c-eaaa5f03a066", "name", 0, false, Arrays.asList()));
        when(mockOrgFeignClient.queryErpOrgStore(1, 0)).thenReturn(orgGeneralDTOS);

        // Configure OrgFeignClient.queryOrgByChildIdList(...).
        final List<OrgGeneralDTO> orgGeneralDTOS1 = Arrays.asList(
                new OrgGeneralDTO("8165fc96-d41e-4dde-940c-eaaa5f03a066", "name", 0, false, Arrays.asList()));
        when(mockOrgFeignClient.queryOrgByChildIdList(Arrays.asList("value"))).thenReturn(orgGeneralDTOS1);

        // Configure OrgFeignClient.queryBrandList(...).
        final BrandDTO brandDTO1 = new BrandDTO();
        brandDTO1.setGuid("storeGuid");
        brandDTO1.setUuid("0db9815e-f80b-41a5-8ce4-7ff2f7e03e87");
        brandDTO1.setName("name");
        brandDTO1.setDescription("description");
        brandDTO1.setIsDeleted(false);
        final List<BrandDTO> brandDTOS = Arrays.asList(brandDTO1);
        when(mockOrgFeignClient.queryBrandList()).thenReturn(brandDTOS);

        // Configure OrgFeignClient.queryBrandByIdList(...).
        final BrandDTO brandDTO2 = new BrandDTO();
        brandDTO2.setGuid("storeGuid");
        brandDTO2.setUuid("0db9815e-f80b-41a5-8ce4-7ff2f7e03e87");
        brandDTO2.setName("name");
        brandDTO2.setDescription("description");
        brandDTO2.setIsDeleted(false);
        final List<BrandDTO> brandDTOS1 = Arrays.asList(brandDTO2);
        when(mockOrgFeignClient.queryBrandByIdList(Arrays.asList("value"))).thenReturn(brandDTOS1);

        // Run the test
        final UserSpinnerDTO result = userDataServiceImplUnderTest.queryStoreAndCondSpinner();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryStoreAndCondSpinner_OrgFeignClientQueryStoreByGuidListReturnsNoItems() {
        // Setup
        final UserSpinnerDTO expectedResult = new UserSpinnerDTO();
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("storeGuid");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setProvinceCode("provinceCode");
        storeDTO.setProvinceName("provinceName");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setCountyCode("countyCode");
        storeDTO.setCountyName("countyName");
        storeDTO.setIsDeleted(false);
        expectedResult.setArrayOfStoreDTO(Arrays.asList(storeDTO));
        final OrgGeneralDTO orgGeneralDTO = new OrgGeneralDTO();
        expectedResult.setArrayOfOrgDTO(Arrays.asList(orgGeneralDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("storeGuid");
        brandDTO.setName("name");
        brandDTO.setIsDeleted(false);
        expectedResult.setArrayOfBrandDTO(Arrays.asList(brandDTO));

        when(mockOrgFeignClient.queryStoreByGuidList(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Configure OrgFeignClient.queryErpOrgStore(...).
        final List<OrgGeneralDTO> orgGeneralDTOS = Arrays.asList(
                new OrgGeneralDTO("8165fc96-d41e-4dde-940c-eaaa5f03a066", "name", 0, false, Arrays.asList()));
        when(mockOrgFeignClient.queryErpOrgStore(1, 0)).thenReturn(orgGeneralDTOS);

        // Configure OrgFeignClient.queryOrgByChildIdList(...).
        final List<OrgGeneralDTO> orgGeneralDTOS1 = Arrays.asList(
                new OrgGeneralDTO("8165fc96-d41e-4dde-940c-eaaa5f03a066", "name", 0, false, Arrays.asList()));
        when(mockOrgFeignClient.queryOrgByChildIdList(Arrays.asList("value"))).thenReturn(orgGeneralDTOS1);

        // Configure OrgFeignClient.queryBrandList(...).
        final BrandDTO brandDTO1 = new BrandDTO();
        brandDTO1.setGuid("storeGuid");
        brandDTO1.setUuid("0db9815e-f80b-41a5-8ce4-7ff2f7e03e87");
        brandDTO1.setName("name");
        brandDTO1.setDescription("description");
        brandDTO1.setIsDeleted(false);
        final List<BrandDTO> brandDTOS = Arrays.asList(brandDTO1);
        when(mockOrgFeignClient.queryBrandList()).thenReturn(brandDTOS);

        // Configure OrgFeignClient.queryBrandByIdList(...).
        final BrandDTO brandDTO2 = new BrandDTO();
        brandDTO2.setGuid("storeGuid");
        brandDTO2.setUuid("0db9815e-f80b-41a5-8ce4-7ff2f7e03e87");
        brandDTO2.setName("name");
        brandDTO2.setDescription("description");
        brandDTO2.setIsDeleted(false);
        final List<BrandDTO> brandDTOS1 = Arrays.asList(brandDTO2);
        when(mockOrgFeignClient.queryBrandByIdList(Arrays.asList("value"))).thenReturn(brandDTOS1);

        // Run the test
        final UserSpinnerDTO result = userDataServiceImplUnderTest.queryStoreAndCondSpinner();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryStoreAndCondSpinner_OrgFeignClientQueryErpOrgStoreReturnsNoItems() {
        // Setup
        final UserSpinnerDTO expectedResult = new UserSpinnerDTO();
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("storeGuid");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setProvinceCode("provinceCode");
        storeDTO.setProvinceName("provinceName");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setCountyCode("countyCode");
        storeDTO.setCountyName("countyName");
        storeDTO.setIsDeleted(false);
        expectedResult.setArrayOfStoreDTO(Arrays.asList(storeDTO));
        final OrgGeneralDTO orgGeneralDTO = new OrgGeneralDTO();
        expectedResult.setArrayOfOrgDTO(Arrays.asList(orgGeneralDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("storeGuid");
        brandDTO.setName("name");
        brandDTO.setIsDeleted(false);
        expectedResult.setArrayOfBrandDTO(Arrays.asList(brandDTO));

        // Configure OrgFeignClient.queryAllStore(...).
        final StoreDTO storeDTO1 = new StoreDTO();
        storeDTO1.setGuid("storeGuid");
        storeDTO1.setName("name");
        storeDTO1.setBelongBrandGuid("belongBrandGuid");
        storeDTO1.setProvinceCode("provinceCode");
        storeDTO1.setProvinceName("provinceName");
        storeDTO1.setCityCode("cityCode");
        storeDTO1.setCityName("cityName");
        storeDTO1.setCountyCode("countyCode");
        storeDTO1.setCountyName("countyName");
        storeDTO1.setIsDeleted(false);
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO1);
        when(mockOrgFeignClient.queryAllStore()).thenReturn(storeDTOS);

        when(mockOrgFeignClient.queryErpOrgStore(1, 0)).thenReturn(Collections.emptyList());

        // Configure OrgFeignClient.queryBrandList(...).
        final BrandDTO brandDTO1 = new BrandDTO();
        brandDTO1.setGuid("storeGuid");
        brandDTO1.setUuid("0db9815e-f80b-41a5-8ce4-7ff2f7e03e87");
        brandDTO1.setName("name");
        brandDTO1.setDescription("description");
        brandDTO1.setIsDeleted(false);
        final List<BrandDTO> brandDTOS = Arrays.asList(brandDTO1);
        when(mockOrgFeignClient.queryBrandList()).thenReturn(brandDTOS);

        // Configure OrgFeignClient.queryBrandByIdList(...).
        final BrandDTO brandDTO2 = new BrandDTO();
        brandDTO2.setGuid("storeGuid");
        brandDTO2.setUuid("0db9815e-f80b-41a5-8ce4-7ff2f7e03e87");
        brandDTO2.setName("name");
        brandDTO2.setDescription("description");
        brandDTO2.setIsDeleted(false);
        final List<BrandDTO> brandDTOS1 = Arrays.asList(brandDTO2);
        when(mockOrgFeignClient.queryBrandByIdList(Arrays.asList("value"))).thenReturn(brandDTOS1);

        // Run the test
        final UserSpinnerDTO result = userDataServiceImplUnderTest.queryStoreAndCondSpinner();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryStoreAndCondSpinner_OrgFeignClientQueryOrgByChildIdListReturnsNoItems() {
        // Setup
        final UserSpinnerDTO expectedResult = new UserSpinnerDTO();
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("storeGuid");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setProvinceCode("provinceCode");
        storeDTO.setProvinceName("provinceName");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setCountyCode("countyCode");
        storeDTO.setCountyName("countyName");
        storeDTO.setIsDeleted(false);
        expectedResult.setArrayOfStoreDTO(Arrays.asList(storeDTO));
        final OrgGeneralDTO orgGeneralDTO = new OrgGeneralDTO();
        expectedResult.setArrayOfOrgDTO(Arrays.asList(orgGeneralDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("storeGuid");
        brandDTO.setName("name");
        brandDTO.setIsDeleted(false);
        expectedResult.setArrayOfBrandDTO(Arrays.asList(brandDTO));

        // Configure OrgFeignClient.queryAllStore(...).
        final StoreDTO storeDTO1 = new StoreDTO();
        storeDTO1.setGuid("storeGuid");
        storeDTO1.setName("name");
        storeDTO1.setBelongBrandGuid("belongBrandGuid");
        storeDTO1.setProvinceCode("provinceCode");
        storeDTO1.setProvinceName("provinceName");
        storeDTO1.setCityCode("cityCode");
        storeDTO1.setCityName("cityName");
        storeDTO1.setCountyCode("countyCode");
        storeDTO1.setCountyName("countyName");
        storeDTO1.setIsDeleted(false);
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO1);
        when(mockOrgFeignClient.queryAllStore()).thenReturn(storeDTOS);

        when(mockOrgFeignClient.queryOrgByChildIdList(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Configure OrgFeignClient.queryBrandList(...).
        final BrandDTO brandDTO1 = new BrandDTO();
        brandDTO1.setGuid("storeGuid");
        brandDTO1.setUuid("0db9815e-f80b-41a5-8ce4-7ff2f7e03e87");
        brandDTO1.setName("name");
        brandDTO1.setDescription("description");
        brandDTO1.setIsDeleted(false);
        final List<BrandDTO> brandDTOS = Arrays.asList(brandDTO1);
        when(mockOrgFeignClient.queryBrandList()).thenReturn(brandDTOS);

        // Configure OrgFeignClient.queryBrandByIdList(...).
        final BrandDTO brandDTO2 = new BrandDTO();
        brandDTO2.setGuid("storeGuid");
        brandDTO2.setUuid("0db9815e-f80b-41a5-8ce4-7ff2f7e03e87");
        brandDTO2.setName("name");
        brandDTO2.setDescription("description");
        brandDTO2.setIsDeleted(false);
        final List<BrandDTO> brandDTOS1 = Arrays.asList(brandDTO2);
        when(mockOrgFeignClient.queryBrandByIdList(Arrays.asList("value"))).thenReturn(brandDTOS1);

        // Run the test
        final UserSpinnerDTO result = userDataServiceImplUnderTest.queryStoreAndCondSpinner();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryStoreAndCondSpinner_OrgFeignClientQueryBrandListReturnsNoItems() {
        // Setup
        final UserSpinnerDTO expectedResult = new UserSpinnerDTO();
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("storeGuid");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setProvinceCode("provinceCode");
        storeDTO.setProvinceName("provinceName");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setCountyCode("countyCode");
        storeDTO.setCountyName("countyName");
        storeDTO.setIsDeleted(false);
        expectedResult.setArrayOfStoreDTO(Arrays.asList(storeDTO));
        final OrgGeneralDTO orgGeneralDTO = new OrgGeneralDTO();
        expectedResult.setArrayOfOrgDTO(Arrays.asList(orgGeneralDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("storeGuid");
        brandDTO.setName("name");
        brandDTO.setIsDeleted(false);
        expectedResult.setArrayOfBrandDTO(Arrays.asList(brandDTO));

        // Configure OrgFeignClient.queryAllStore(...).
        final StoreDTO storeDTO1 = new StoreDTO();
        storeDTO1.setGuid("storeGuid");
        storeDTO1.setName("name");
        storeDTO1.setBelongBrandGuid("belongBrandGuid");
        storeDTO1.setProvinceCode("provinceCode");
        storeDTO1.setProvinceName("provinceName");
        storeDTO1.setCityCode("cityCode");
        storeDTO1.setCityName("cityName");
        storeDTO1.setCountyCode("countyCode");
        storeDTO1.setCountyName("countyName");
        storeDTO1.setIsDeleted(false);
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO1);
        when(mockOrgFeignClient.queryAllStore()).thenReturn(storeDTOS);

        // Configure OrgFeignClient.queryErpOrgStore(...).
        final List<OrgGeneralDTO> orgGeneralDTOS = Arrays.asList(
                new OrgGeneralDTO("8165fc96-d41e-4dde-940c-eaaa5f03a066", "name", 0, false, Arrays.asList()));
        when(mockOrgFeignClient.queryErpOrgStore(1, 0)).thenReturn(orgGeneralDTOS);

        when(mockOrgFeignClient.queryBrandList()).thenReturn(Collections.emptyList());

        // Run the test
        final UserSpinnerDTO result = userDataServiceImplUnderTest.queryStoreAndCondSpinner();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryStoreAndCondSpinner_OrgFeignClientQueryBrandByIdListReturnsNoItems() {
        // Setup
        final UserSpinnerDTO expectedResult = new UserSpinnerDTO();
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("storeGuid");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setProvinceCode("provinceCode");
        storeDTO.setProvinceName("provinceName");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setCountyCode("countyCode");
        storeDTO.setCountyName("countyName");
        storeDTO.setIsDeleted(false);
        expectedResult.setArrayOfStoreDTO(Arrays.asList(storeDTO));
        final OrgGeneralDTO orgGeneralDTO = new OrgGeneralDTO();
        expectedResult.setArrayOfOrgDTO(Arrays.asList(orgGeneralDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("storeGuid");
        brandDTO.setName("name");
        brandDTO.setIsDeleted(false);
        expectedResult.setArrayOfBrandDTO(Arrays.asList(brandDTO));

        // Configure OrgFeignClient.queryAllStore(...).
        final StoreDTO storeDTO1 = new StoreDTO();
        storeDTO1.setGuid("storeGuid");
        storeDTO1.setName("name");
        storeDTO1.setBelongBrandGuid("belongBrandGuid");
        storeDTO1.setProvinceCode("provinceCode");
        storeDTO1.setProvinceName("provinceName");
        storeDTO1.setCityCode("cityCode");
        storeDTO1.setCityName("cityName");
        storeDTO1.setCountyCode("countyCode");
        storeDTO1.setCountyName("countyName");
        storeDTO1.setIsDeleted(false);
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO1);
        when(mockOrgFeignClient.queryAllStore()).thenReturn(storeDTOS);

        // Configure OrgFeignClient.queryErpOrgStore(...).
        final List<OrgGeneralDTO> orgGeneralDTOS = Arrays.asList(
                new OrgGeneralDTO("8165fc96-d41e-4dde-940c-eaaa5f03a066", "name", 0, false, Arrays.asList()));
        when(mockOrgFeignClient.queryErpOrgStore(1, 0)).thenReturn(orgGeneralDTOS);

        when(mockOrgFeignClient.queryBrandByIdList(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        final UserSpinnerDTO result = userDataServiceImplUnderTest.queryStoreAndCondSpinner();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryStoreAndCondSpinner_OrgFeignClientQueryStoreDetailByGuidListReturnsNoItems() {
        // Setup
        final UserSpinnerDTO expectedResult = new UserSpinnerDTO();
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("storeGuid");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setProvinceCode("provinceCode");
        storeDTO.setProvinceName("provinceName");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setCountyCode("countyCode");
        storeDTO.setCountyName("countyName");
        storeDTO.setIsDeleted(false);
        expectedResult.setArrayOfStoreDTO(Arrays.asList(storeDTO));
        final OrgGeneralDTO orgGeneralDTO = new OrgGeneralDTO();
        expectedResult.setArrayOfOrgDTO(Arrays.asList(orgGeneralDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("storeGuid");
        brandDTO.setName("name");
        brandDTO.setIsDeleted(false);
        expectedResult.setArrayOfBrandDTO(Arrays.asList(brandDTO));

        when(mockOrgFeignClient.queryStoreDetailByGuidList(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Configure OrgFeignClient.queryAllStore(...).
        final StoreDTO storeDTO1 = new StoreDTO();
        storeDTO1.setGuid("storeGuid");
        storeDTO1.setName("name");
        storeDTO1.setBelongBrandGuid("belongBrandGuid");
        storeDTO1.setProvinceCode("provinceCode");
        storeDTO1.setProvinceName("provinceName");
        storeDTO1.setCityCode("cityCode");
        storeDTO1.setCityName("cityName");
        storeDTO1.setCountyCode("countyCode");
        storeDTO1.setCountyName("countyName");
        storeDTO1.setIsDeleted(false);
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO1);
        when(mockOrgFeignClient.queryAllStore()).thenReturn(storeDTOS);

        // Configure OrgFeignClient.queryStoreByGuidList(...).
        final StoreDTO storeDTO2 = new StoreDTO();
        storeDTO2.setGuid("storeGuid");
        storeDTO2.setName("name");
        storeDTO2.setBelongBrandGuid("belongBrandGuid");
        storeDTO2.setProvinceCode("provinceCode");
        storeDTO2.setProvinceName("provinceName");
        storeDTO2.setCityCode("cityCode");
        storeDTO2.setCityName("cityName");
        storeDTO2.setCountyCode("countyCode");
        storeDTO2.setCountyName("countyName");
        storeDTO2.setIsDeleted(false);
        final List<StoreDTO> storeDTOS1 = Arrays.asList(storeDTO2);
        when(mockOrgFeignClient.queryStoreByGuidList(Arrays.asList("value"))).thenReturn(storeDTOS1);

        // Configure OrgFeignClient.queryErpOrgStore(...).
        final List<OrgGeneralDTO> orgGeneralDTOS = Arrays.asList(
                new OrgGeneralDTO("8165fc96-d41e-4dde-940c-eaaa5f03a066", "name", 0, false, Arrays.asList()));
        when(mockOrgFeignClient.queryErpOrgStore(1, 0)).thenReturn(orgGeneralDTOS);

        // Configure OrgFeignClient.queryOrgByChildIdList(...).
        final List<OrgGeneralDTO> orgGeneralDTOS1 = Arrays.asList(
                new OrgGeneralDTO("8165fc96-d41e-4dde-940c-eaaa5f03a066", "name", 0, false, Arrays.asList()));
        when(mockOrgFeignClient.queryOrgByChildIdList(Arrays.asList("value"))).thenReturn(orgGeneralDTOS1);

        // Configure OrgFeignClient.queryBrandList(...).
        final BrandDTO brandDTO1 = new BrandDTO();
        brandDTO1.setGuid("storeGuid");
        brandDTO1.setUuid("0db9815e-f80b-41a5-8ce4-7ff2f7e03e87");
        brandDTO1.setName("name");
        brandDTO1.setDescription("description");
        brandDTO1.setIsDeleted(false);
        final List<BrandDTO> brandDTOS = Arrays.asList(brandDTO1);
        when(mockOrgFeignClient.queryBrandList()).thenReturn(brandDTOS);

        // Configure OrgFeignClient.queryBrandByIdList(...).
        final BrandDTO brandDTO2 = new BrandDTO();
        brandDTO2.setGuid("storeGuid");
        brandDTO2.setUuid("0db9815e-f80b-41a5-8ce4-7ff2f7e03e87");
        brandDTO2.setName("name");
        brandDTO2.setDescription("description");
        brandDTO2.setIsDeleted(false);
        final List<BrandDTO> brandDTOS1 = Arrays.asList(brandDTO2);
        when(mockOrgFeignClient.queryBrandByIdList(Arrays.asList("value"))).thenReturn(brandDTOS1);

        // Configure OrgFeignClient.parseByCondition(...).
        final StoreParserDTO storeParserDTO = new StoreParserDTO();
        storeParserDTO.setAllStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setRegionCodeList(Arrays.asList("value"));
        storeParserDTO.setBrandGuidList(Arrays.asList("value"));
        storeParserDTO.setOrganizationGuidList(Arrays.asList("value"));
        when(mockOrgFeignClient.parseByCondition(storeParserDTO)).thenReturn(Arrays.asList("value"));

        // Run the test
        final UserSpinnerDTO result = userDataServiceImplUnderTest.queryStoreAndCondSpinner();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryStoreAndCondSpinner_OrgFeignClientParseByConditionReturnsNoItems() {
        // Setup
        final UserSpinnerDTO expectedResult = new UserSpinnerDTO();
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("storeGuid");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setProvinceCode("provinceCode");
        storeDTO.setProvinceName("provinceName");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setCountyCode("countyCode");
        storeDTO.setCountyName("countyName");
        storeDTO.setIsDeleted(false);
        expectedResult.setArrayOfStoreDTO(Arrays.asList(storeDTO));
        final OrgGeneralDTO orgGeneralDTO = new OrgGeneralDTO();
        expectedResult.setArrayOfOrgDTO(Arrays.asList(orgGeneralDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("storeGuid");
        brandDTO.setName("name");
        brandDTO.setIsDeleted(false);
        expectedResult.setArrayOfBrandDTO(Arrays.asList(brandDTO));

        // Configure OrgFeignClient.queryStoreDetailByGuidList(...).
        final StoreDTO storeDTO1 = new StoreDTO();
        storeDTO1.setGuid("storeGuid");
        storeDTO1.setName("name");
        storeDTO1.setBelongBrandGuid("belongBrandGuid");
        storeDTO1.setProvinceCode("provinceCode");
        storeDTO1.setProvinceName("provinceName");
        storeDTO1.setCityCode("cityCode");
        storeDTO1.setCityName("cityName");
        storeDTO1.setCountyCode("countyCode");
        storeDTO1.setCountyName("countyName");
        storeDTO1.setIsDeleted(false);
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO1);
        when(mockOrgFeignClient.queryStoreDetailByGuidList(Arrays.asList("value"))).thenReturn(storeDTOS);

        // Configure OrgFeignClient.parseByCondition(...).
        final StoreParserDTO storeParserDTO = new StoreParserDTO();
        storeParserDTO.setAllStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setRegionCodeList(Arrays.asList("value"));
        storeParserDTO.setBrandGuidList(Arrays.asList("value"));
        storeParserDTO.setOrganizationGuidList(Arrays.asList("value"));
        when(mockOrgFeignClient.parseByCondition(storeParserDTO)).thenReturn(Collections.emptyList());

        // Configure OrgFeignClient.queryAllStore(...).
        final StoreDTO storeDTO2 = new StoreDTO();
        storeDTO2.setGuid("storeGuid");
        storeDTO2.setName("name");
        storeDTO2.setBelongBrandGuid("belongBrandGuid");
        storeDTO2.setProvinceCode("provinceCode");
        storeDTO2.setProvinceName("provinceName");
        storeDTO2.setCityCode("cityCode");
        storeDTO2.setCityName("cityName");
        storeDTO2.setCountyCode("countyCode");
        storeDTO2.setCountyName("countyName");
        storeDTO2.setIsDeleted(false);
        final List<StoreDTO> storeDTOS1 = Arrays.asList(storeDTO2);
        when(mockOrgFeignClient.queryAllStore()).thenReturn(storeDTOS1);

        // Configure OrgFeignClient.queryStoreByGuidList(...).
        final StoreDTO storeDTO3 = new StoreDTO();
        storeDTO3.setGuid("storeGuid");
        storeDTO3.setName("name");
        storeDTO3.setBelongBrandGuid("belongBrandGuid");
        storeDTO3.setProvinceCode("provinceCode");
        storeDTO3.setProvinceName("provinceName");
        storeDTO3.setCityCode("cityCode");
        storeDTO3.setCityName("cityName");
        storeDTO3.setCountyCode("countyCode");
        storeDTO3.setCountyName("countyName");
        storeDTO3.setIsDeleted(false);
        final List<StoreDTO> storeDTOS2 = Arrays.asList(storeDTO3);
        when(mockOrgFeignClient.queryStoreByGuidList(Arrays.asList("value"))).thenReturn(storeDTOS2);

        // Configure OrgFeignClient.queryErpOrgStore(...).
        final List<OrgGeneralDTO> orgGeneralDTOS = Arrays.asList(
                new OrgGeneralDTO("8165fc96-d41e-4dde-940c-eaaa5f03a066", "name", 0, false, Arrays.asList()));
        when(mockOrgFeignClient.queryErpOrgStore(1, 0)).thenReturn(orgGeneralDTOS);

        // Configure OrgFeignClient.queryOrgByChildIdList(...).
        final List<OrgGeneralDTO> orgGeneralDTOS1 = Arrays.asList(
                new OrgGeneralDTO("8165fc96-d41e-4dde-940c-eaaa5f03a066", "name", 0, false, Arrays.asList()));
        when(mockOrgFeignClient.queryOrgByChildIdList(Arrays.asList("value"))).thenReturn(orgGeneralDTOS1);

        // Configure OrgFeignClient.queryBrandList(...).
        final BrandDTO brandDTO1 = new BrandDTO();
        brandDTO1.setGuid("storeGuid");
        brandDTO1.setUuid("0db9815e-f80b-41a5-8ce4-7ff2f7e03e87");
        brandDTO1.setName("name");
        brandDTO1.setDescription("description");
        brandDTO1.setIsDeleted(false);
        final List<BrandDTO> brandDTOS = Arrays.asList(brandDTO1);
        when(mockOrgFeignClient.queryBrandList()).thenReturn(brandDTOS);

        // Configure OrgFeignClient.queryBrandByIdList(...).
        final BrandDTO brandDTO2 = new BrandDTO();
        brandDTO2.setGuid("storeGuid");
        brandDTO2.setUuid("0db9815e-f80b-41a5-8ce4-7ff2f7e03e87");
        brandDTO2.setName("name");
        brandDTO2.setDescription("description");
        brandDTO2.setIsDeleted(false);
        final List<BrandDTO> brandDTOS1 = Arrays.asList(brandDTO2);
        when(mockOrgFeignClient.queryBrandByIdList(Arrays.asList("value"))).thenReturn(brandDTOS1);

        // Run the test
        final UserSpinnerDTO result = userDataServiceImplUnderTest.queryStoreAndCondSpinner();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryGenericOrgSpinner() {
        // Setup
        final UserSpinnerDTO expectedResult = new UserSpinnerDTO();
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("storeGuid");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setProvinceCode("provinceCode");
        storeDTO.setProvinceName("provinceName");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setCountyCode("countyCode");
        storeDTO.setCountyName("countyName");
        storeDTO.setIsDeleted(false);
        expectedResult.setArrayOfStoreDTO(Arrays.asList(storeDTO));
        final OrgGeneralDTO orgGeneralDTO = new OrgGeneralDTO();
        expectedResult.setArrayOfOrgDTO(Arrays.asList(orgGeneralDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("storeGuid");
        brandDTO.setName("name");
        brandDTO.setIsDeleted(false);
        expectedResult.setArrayOfBrandDTO(Arrays.asList(brandDTO));

        // Configure OrgFeignClient.queryErpOrgStore(...).
        final List<OrgGeneralDTO> orgGeneralDTOS = Arrays.asList(
                new OrgGeneralDTO("8165fc96-d41e-4dde-940c-eaaa5f03a066", "name", 0, false, Arrays.asList()));
        when(mockOrgFeignClient.queryErpOrgStore(1, 1)).thenReturn(orgGeneralDTOS);

        // Run the test
        final UserSpinnerDTO result = userDataServiceImplUnderTest.queryGenericOrgSpinner();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryGenericOrgSpinner_OrgFeignClientReturnsNoItems() {
        // Setup
        final UserSpinnerDTO expectedResult = new UserSpinnerDTO();
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("storeGuid");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setProvinceCode("provinceCode");
        storeDTO.setProvinceName("provinceName");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setCountyCode("countyCode");
        storeDTO.setCountyName("countyName");
        storeDTO.setIsDeleted(false);
        expectedResult.setArrayOfStoreDTO(Arrays.asList(storeDTO));
        final OrgGeneralDTO orgGeneralDTO = new OrgGeneralDTO();
        expectedResult.setArrayOfOrgDTO(Arrays.asList(orgGeneralDTO));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("storeGuid");
        brandDTO.setName("name");
        brandDTO.setIsDeleted(false);
        expectedResult.setArrayOfBrandDTO(Arrays.asList(brandDTO));

        when(mockOrgFeignClient.queryErpOrgStore(1, 1)).thenReturn(Collections.emptyList());

        // Run the test
        final UserSpinnerDTO result = userDataServiceImplUnderTest.queryGenericOrgSpinner();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryAllStoreGuid() {
        // Setup
        when(mockOrgFeignClient.queryAllStoreGuid()).thenReturn(Arrays.asList("value"));

        // Configure OrgFeignClient.parseByCondition(...).
        final StoreParserDTO storeParserDTO = new StoreParserDTO();
        storeParserDTO.setAllStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setRegionCodeList(Arrays.asList("value"));
        storeParserDTO.setBrandGuidList(Arrays.asList("value"));
        storeParserDTO.setOrganizationGuidList(Arrays.asList("value"));
        when(mockOrgFeignClient.parseByCondition(storeParserDTO)).thenReturn(Arrays.asList("value"));

        // Run the test
        final List<String> result = userDataServiceImplUnderTest.queryAllStoreGuid();

        // Verify the results
        assertThat(result).isEqualTo(Arrays.asList("value"));
    }

    @Test
    public void testQueryAllStoreGuid_OrgFeignClientQueryAllStoreGuidReturnsNoItems() {
        // Setup
        when(mockOrgFeignClient.queryAllStoreGuid()).thenReturn(Collections.emptyList());

        // Run the test
        final List<String> result = userDataServiceImplUnderTest.queryAllStoreGuid();

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testQueryAllStoreGuid_OrgFeignClientParseByConditionReturnsNoItems() {
        // Setup
        // Configure OrgFeignClient.parseByCondition(...).
        final StoreParserDTO storeParserDTO = new StoreParserDTO();
        storeParserDTO.setAllStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setRegionCodeList(Arrays.asList("value"));
        storeParserDTO.setBrandGuidList(Arrays.asList("value"));
        storeParserDTO.setOrganizationGuidList(Arrays.asList("value"));
        when(mockOrgFeignClient.parseByCondition(storeParserDTO)).thenReturn(Collections.emptyList());

        // Run the test
        final List<String> result = userDataServiceImplUnderTest.queryAllStoreGuid();

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testQueryAllUserToLocal() {
        // Setup
        final LocalUserReqDTO localUserReqDTO = new LocalUserReqDTO();
        localUserReqDTO.setStoreGuid("storeGuid");
        localUserReqDTO.setTerminalCode("terminalCode");

        final LocalUserInfoDTO localUserInfoDTO = new LocalUserInfoDTO();
        localUserInfoDTO.setUserGuid("userGuid");
        localUserInfoDTO.setEnterpriseNo("enterpriseNo");
        localUserInfoDTO.setAccount("account");
        localUserInfoDTO.setPassword("password");
        localUserInfoDTO.setName("name");
        final List<LocalUserInfoDTO> expectedResult = Arrays.asList(localUserInfoDTO);

        // Configure UserMapper.selectList(...).
        final UserDO userDO = new UserDO();
        userDO.setGuid("userGuid");
        userDO.setEnterpriseNo("enterpriseGuid");
        userDO.setAccount("account");
        userDO.setPassword("password");
        userDO.setName("name");
        userDO.setPhone("phone");
        userDO.setDiscountThreshold(new BigDecimal("0.00"));
        userDO.setAllowanceThreshold(new BigDecimal("0.00"));
        userDO.setProductDiscountThreshold(new BigDecimal("0.00"));
        userDO.setRefundThreshold(new BigDecimal("0.00"));
        userDO.setRolesDistributable("roleGuid");
        final List<UserDO> userDOS = Arrays.asList(userDO);
        when(mockUserMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(userDOS);

        // Configure OrgFeignClient.queryAllStore(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("storeGuid");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setProvinceCode("provinceCode");
        storeDTO.setProvinceName("provinceName");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setCountyCode("countyCode");
        storeDTO.setCountyName("countyName");
        storeDTO.setIsDeleted(false);
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO);
        when(mockOrgFeignClient.queryAllStore()).thenReturn(storeDTOS);

        // Configure OrgFeignClient.queryStoreByGuidList(...).
        final StoreDTO storeDTO1 = new StoreDTO();
        storeDTO1.setGuid("storeGuid");
        storeDTO1.setName("name");
        storeDTO1.setBelongBrandGuid("belongBrandGuid");
        storeDTO1.setProvinceCode("provinceCode");
        storeDTO1.setProvinceName("provinceName");
        storeDTO1.setCityCode("cityCode");
        storeDTO1.setCityName("cityName");
        storeDTO1.setCountyCode("countyCode");
        storeDTO1.setCountyName("countyName");
        storeDTO1.setIsDeleted(false);
        final List<StoreDTO> storeDTOS1 = Arrays.asList(storeDTO1);
        when(mockOrgFeignClient.queryStoreByGuidList(Arrays.asList("value"))).thenReturn(storeDTOS1);

        when(mockOrgFeignClient.queryAllStoreGuid()).thenReturn(Arrays.asList("value"));

        // Configure OrgFeignClient.parseByCondition(...).
        final StoreParserDTO storeParserDTO = new StoreParserDTO();
        storeParserDTO.setAllStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setRegionCodeList(Arrays.asList("value"));
        storeParserDTO.setBrandGuidList(Arrays.asList("value"));
        storeParserDTO.setOrganizationGuidList(Arrays.asList("value"));
        when(mockOrgFeignClient.parseByCondition(storeParserDTO)).thenReturn(Arrays.asList("value"));

        // Configure UserClient.getAdminByEnterpriseGuid(...).
        final com.holderzone.resource.common.dto.user.UserDTO userDTO = new com.holderzone.resource.common.dto.user.UserDTO();
        userDTO.setUserGuid("userGuid");
        userDTO.setEnterpriseGuid("enterpriseGuid");
        userDTO.setAccount("account");
        userDTO.setPassword("password");
        userDTO.setName("name");
        userDTO.setTel("phone");
        when(mockUserClient.getAdminByEnterpriseGuid("enterpriseGuid")).thenReturn(userDTO);

        // Run the test
        final List<LocalUserInfoDTO> result = userDataServiceImplUnderTest.queryAllUserToLocal(localUserReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryAllUserToLocal_UserMapperReturnsNoItems() {
        // Setup
        final LocalUserReqDTO localUserReqDTO = new LocalUserReqDTO();
        localUserReqDTO.setStoreGuid("storeGuid");
        localUserReqDTO.setTerminalCode("terminalCode");

        when(mockUserMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure UserClient.getAdminByEnterpriseGuid(...).
        final com.holderzone.resource.common.dto.user.UserDTO userDTO = new com.holderzone.resource.common.dto.user.UserDTO();
        userDTO.setUserGuid("userGuid");
        userDTO.setEnterpriseGuid("enterpriseGuid");
        userDTO.setAccount("account");
        userDTO.setPassword("password");
        userDTO.setName("name");
        userDTO.setTel("phone");
        when(mockUserClient.getAdminByEnterpriseGuid("enterpriseGuid")).thenReturn(userDTO);

        // Run the test
        final List<LocalUserInfoDTO> result = userDataServiceImplUnderTest.queryAllUserToLocal(localUserReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testQueryAllUserToLocal_OrgFeignClientQueryAllStoreReturnsNoItems() {
        // Setup
        final LocalUserReqDTO localUserReqDTO = new LocalUserReqDTO();
        localUserReqDTO.setStoreGuid("storeGuid");
        localUserReqDTO.setTerminalCode("terminalCode");

        // Configure UserMapper.selectList(...).
        final UserDO userDO = new UserDO();
        userDO.setGuid("userGuid");
        userDO.setEnterpriseNo("enterpriseGuid");
        userDO.setAccount("account");
        userDO.setPassword("password");
        userDO.setName("name");
        userDO.setPhone("phone");
        userDO.setDiscountThreshold(new BigDecimal("0.00"));
        userDO.setAllowanceThreshold(new BigDecimal("0.00"));
        userDO.setProductDiscountThreshold(new BigDecimal("0.00"));
        userDO.setRefundThreshold(new BigDecimal("0.00"));
        userDO.setRolesDistributable("roleGuid");
        final List<UserDO> userDOS = Arrays.asList(userDO);
        when(mockUserMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(userDOS);

        when(mockOrgFeignClient.queryAllStore()).thenReturn(Collections.emptyList());

        // Configure UserClient.getAdminByEnterpriseGuid(...).
        final com.holderzone.resource.common.dto.user.UserDTO userDTO = new com.holderzone.resource.common.dto.user.UserDTO();
        userDTO.setUserGuid("userGuid");
        userDTO.setEnterpriseGuid("enterpriseGuid");
        userDTO.setAccount("account");
        userDTO.setPassword("password");
        userDTO.setName("name");
        userDTO.setTel("phone");
        when(mockUserClient.getAdminByEnterpriseGuid("enterpriseGuid")).thenReturn(userDTO);

        // Run the test
        final List<LocalUserInfoDTO> result = userDataServiceImplUnderTest.queryAllUserToLocal(localUserReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testQueryAllUserToLocal_OrgFeignClientQueryStoreByGuidListReturnsNoItems() {
        // Setup
        final LocalUserReqDTO localUserReqDTO = new LocalUserReqDTO();
        localUserReqDTO.setStoreGuid("storeGuid");
        localUserReqDTO.setTerminalCode("terminalCode");

        // Configure UserMapper.selectList(...).
        final UserDO userDO = new UserDO();
        userDO.setGuid("userGuid");
        userDO.setEnterpriseNo("enterpriseGuid");
        userDO.setAccount("account");
        userDO.setPassword("password");
        userDO.setName("name");
        userDO.setPhone("phone");
        userDO.setDiscountThreshold(new BigDecimal("0.00"));
        userDO.setAllowanceThreshold(new BigDecimal("0.00"));
        userDO.setProductDiscountThreshold(new BigDecimal("0.00"));
        userDO.setRefundThreshold(new BigDecimal("0.00"));
        userDO.setRolesDistributable("roleGuid");
        final List<UserDO> userDOS = Arrays.asList(userDO);
        when(mockUserMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(userDOS);

        when(mockOrgFeignClient.queryStoreByGuidList(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Configure UserClient.getAdminByEnterpriseGuid(...).
        final com.holderzone.resource.common.dto.user.UserDTO userDTO = new com.holderzone.resource.common.dto.user.UserDTO();
        userDTO.setUserGuid("userGuid");
        userDTO.setEnterpriseGuid("enterpriseGuid");
        userDTO.setAccount("account");
        userDTO.setPassword("password");
        userDTO.setName("name");
        userDTO.setTel("phone");
        when(mockUserClient.getAdminByEnterpriseGuid("enterpriseGuid")).thenReturn(userDTO);

        // Run the test
        final List<LocalUserInfoDTO> result = userDataServiceImplUnderTest.queryAllUserToLocal(localUserReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testQueryAllUserToLocal_OrgFeignClientQueryAllStoreGuidReturnsNoItems() {
        // Setup
        final LocalUserReqDTO localUserReqDTO = new LocalUserReqDTO();
        localUserReqDTO.setStoreGuid("storeGuid");
        localUserReqDTO.setTerminalCode("terminalCode");

        // Configure UserMapper.selectList(...).
        final UserDO userDO = new UserDO();
        userDO.setGuid("userGuid");
        userDO.setEnterpriseNo("enterpriseGuid");
        userDO.setAccount("account");
        userDO.setPassword("password");
        userDO.setName("name");
        userDO.setPhone("phone");
        userDO.setDiscountThreshold(new BigDecimal("0.00"));
        userDO.setAllowanceThreshold(new BigDecimal("0.00"));
        userDO.setProductDiscountThreshold(new BigDecimal("0.00"));
        userDO.setRefundThreshold(new BigDecimal("0.00"));
        userDO.setRolesDistributable("roleGuid");
        final List<UserDO> userDOS = Arrays.asList(userDO);
        when(mockUserMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(userDOS);

        when(mockOrgFeignClient.queryAllStoreGuid()).thenReturn(Collections.emptyList());

        // Configure UserClient.getAdminByEnterpriseGuid(...).
        final com.holderzone.resource.common.dto.user.UserDTO userDTO = new com.holderzone.resource.common.dto.user.UserDTO();
        userDTO.setUserGuid("userGuid");
        userDTO.setEnterpriseGuid("enterpriseGuid");
        userDTO.setAccount("account");
        userDTO.setPassword("password");
        userDTO.setName("name");
        userDTO.setTel("phone");
        when(mockUserClient.getAdminByEnterpriseGuid("enterpriseGuid")).thenReturn(userDTO);

        // Run the test
        final List<LocalUserInfoDTO> result = userDataServiceImplUnderTest.queryAllUserToLocal(localUserReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testQueryAllUserToLocal_OrgFeignClientParseByConditionReturnsNoItems() {
        // Setup
        final LocalUserReqDTO localUserReqDTO = new LocalUserReqDTO();
        localUserReqDTO.setStoreGuid("storeGuid");
        localUserReqDTO.setTerminalCode("terminalCode");

        final LocalUserInfoDTO localUserInfoDTO = new LocalUserInfoDTO();
        localUserInfoDTO.setUserGuid("userGuid");
        localUserInfoDTO.setEnterpriseNo("enterpriseNo");
        localUserInfoDTO.setAccount("account");
        localUserInfoDTO.setPassword("password");
        localUserInfoDTO.setName("name");
        final List<LocalUserInfoDTO> expectedResult = Arrays.asList(localUserInfoDTO);

        // Configure UserMapper.selectList(...).
        final UserDO userDO = new UserDO();
        userDO.setGuid("userGuid");
        userDO.setEnterpriseNo("enterpriseGuid");
        userDO.setAccount("account");
        userDO.setPassword("password");
        userDO.setName("name");
        userDO.setPhone("phone");
        userDO.setDiscountThreshold(new BigDecimal("0.00"));
        userDO.setAllowanceThreshold(new BigDecimal("0.00"));
        userDO.setProductDiscountThreshold(new BigDecimal("0.00"));
        userDO.setRefundThreshold(new BigDecimal("0.00"));
        userDO.setRolesDistributable("roleGuid");
        final List<UserDO> userDOS = Arrays.asList(userDO);
        when(mockUserMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(userDOS);

        // Configure OrgFeignClient.parseByCondition(...).
        final StoreParserDTO storeParserDTO = new StoreParserDTO();
        storeParserDTO.setAllStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setRegionCodeList(Arrays.asList("value"));
        storeParserDTO.setBrandGuidList(Arrays.asList("value"));
        storeParserDTO.setOrganizationGuidList(Arrays.asList("value"));
        when(mockOrgFeignClient.parseByCondition(storeParserDTO)).thenReturn(Collections.emptyList());

        // Configure OrgFeignClient.queryStoreByGuidList(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("storeGuid");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setProvinceCode("provinceCode");
        storeDTO.setProvinceName("provinceName");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setCountyCode("countyCode");
        storeDTO.setCountyName("countyName");
        storeDTO.setIsDeleted(false);
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO);
        when(mockOrgFeignClient.queryStoreByGuidList(Arrays.asList("value"))).thenReturn(storeDTOS);

        // Configure UserClient.getAdminByEnterpriseGuid(...).
        final com.holderzone.resource.common.dto.user.UserDTO userDTO = new com.holderzone.resource.common.dto.user.UserDTO();
        userDTO.setUserGuid("userGuid");
        userDTO.setEnterpriseGuid("enterpriseGuid");
        userDTO.setAccount("account");
        userDTO.setPassword("password");
        userDTO.setName("name");
        userDTO.setTel("phone");
        when(mockUserClient.getAdminByEnterpriseGuid("enterpriseGuid")).thenReturn(userDTO);

        // Run the test
        final List<LocalUserInfoDTO> result = userDataServiceImplUnderTest.queryAllUserToLocal(localUserReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryAllUserToLocal_UserClientReturnsNull() {
        // Setup
        final LocalUserReqDTO localUserReqDTO = new LocalUserReqDTO();
        localUserReqDTO.setStoreGuid("storeGuid");
        localUserReqDTO.setTerminalCode("terminalCode");

        final LocalUserInfoDTO localUserInfoDTO = new LocalUserInfoDTO();
        localUserInfoDTO.setUserGuid("userGuid");
        localUserInfoDTO.setEnterpriseNo("enterpriseNo");
        localUserInfoDTO.setAccount("account");
        localUserInfoDTO.setPassword("password");
        localUserInfoDTO.setName("name");
        final List<LocalUserInfoDTO> expectedResult = Arrays.asList(localUserInfoDTO);

        // Configure UserMapper.selectList(...).
        final UserDO userDO = new UserDO();
        userDO.setGuid("userGuid");
        userDO.setEnterpriseNo("enterpriseGuid");
        userDO.setAccount("account");
        userDO.setPassword("password");
        userDO.setName("name");
        userDO.setPhone("phone");
        userDO.setDiscountThreshold(new BigDecimal("0.00"));
        userDO.setAllowanceThreshold(new BigDecimal("0.00"));
        userDO.setProductDiscountThreshold(new BigDecimal("0.00"));
        userDO.setRefundThreshold(new BigDecimal("0.00"));
        userDO.setRolesDistributable("roleGuid");
        final List<UserDO> userDOS = Arrays.asList(userDO);
        when(mockUserMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(userDOS);

        // Configure OrgFeignClient.queryAllStore(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("storeGuid");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setProvinceCode("provinceCode");
        storeDTO.setProvinceName("provinceName");
        storeDTO.setCityCode("cityCode");
        storeDTO.setCityName("cityName");
        storeDTO.setCountyCode("countyCode");
        storeDTO.setCountyName("countyName");
        storeDTO.setIsDeleted(false);
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO);
        when(mockOrgFeignClient.queryAllStore()).thenReturn(storeDTOS);

        when(mockUserClient.getAdminByEnterpriseGuid("enterpriseGuid")).thenReturn(null);

        // Run the test
        final List<LocalUserInfoDTO> result = userDataServiceImplUnderTest.queryAllUserToLocal(localUserReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryAIOUsers() {
        // Setup
        final BaseDTO baseDTO = new BaseDTO();
        baseDTO.setDeviceType(0);
        baseDTO.setDeviceId("deviceId");
        baseDTO.setEnterpriseGuid("enterpriseGuid");
        baseDTO.setEnterpriseName("enterpriseName");
        baseDTO.setStoreGuid("storeGuid");

        final UserDTO userDTO = new UserDTO();
        userDTO.setGuid("userGuid");
        userDTO.setAccount("account");
        userDTO.setPassword("password");
        userDTO.setName("name");
        final UserDataStoreRuleDTO userDataStoreRule = new UserDataStoreRuleDTO();
        final UserDataDTO userDataDTO = new UserDataDTO();
        userDataDTO.setGuid("storeGuid");
        userDataDTO.setName("name");
        userDataDTO.setIsDeleted(false);
        userDataStoreRule.setUserStoreData(Arrays.asList(userDataDTO));
        userDTO.setUserDataStoreRule(userDataStoreRule);
        final UserDataCondRuleDTO userDataCondRuleDTO = new UserDataCondRuleDTO();
        final UserDataDTO userDataDTO1 = new UserDataDTO();
        userDataDTO1.setGuid("storeGuid");
        userDataDTO1.setName("name");
        userDataDTO1.setIsDeleted(false);
        userDataCondRuleDTO.setUserOrgData(Arrays.asList(userDataDTO1));
        final UserDataDTO userDataDTO2 = new UserDataDTO();
        userDataDTO2.setGuid("storeGuid");
        userDataDTO2.setName("name");
        userDataDTO2.setIsDeleted(false);
        userDataCondRuleDTO.setUserBrandData(Arrays.asList(userDataDTO2));
        final UserRegionDTO userRegionDTO = new UserRegionDTO();
        userRegionDTO.setAdcode("regionCode");
        userRegionDTO.setName("regionName");
        userRegionDTO.setIsDeleted(false);
        userDataCondRuleDTO.setUserRegionData(Arrays.asList(userRegionDTO));
        userDTO.setUserDataCondRuleList(Arrays.asList(userDataCondRuleDTO));
        userDTO.setDiscountThreshold(new BigDecimal("0.00"));
        userDTO.setAllowanceThreshold(new BigDecimal("0.00"));
        userDTO.setProductDiscountThreshold(new BigDecimal("0.00"));
        userDTO.setRefundThreshold(new BigDecimal("0.00"));
        final UserRoleDistDTO userRoleDistDTO = new UserRoleDistDTO();
        userRoleDistDTO.setGuid("c820b147-d4ec-47c9-93f9-201cdcf6a589");
        userRoleDistDTO.setIsChecked(false);
        userDTO.setUserRolesDistributable(Arrays.asList(userRoleDistDTO));
        final List<UserDTO> expectedResult = Arrays.asList(userDTO);

        // Configure UserMapper.queryAIOUsers(...).
        final UserReadDO userReadDO = new UserReadDO();
        userReadDO.setId(0L);
        userReadDO.setGuid("291bf94a-2e50-4551-80b3-5c4b48108024");
        userReadDO.setEnterpriseNo("enterpriseNo");
        userReadDO.setAccount("account");
        userReadDO.setPassword("password");
        final List<UserReadDO> userReadDOS = Arrays.asList(userReadDO);
        when(mockUserMapper.queryAIOUsers("storeGuid")).thenReturn(userReadDOS);

        // Configure UserClient.getAdminByEnterpriseGuid(...).
        final com.holderzone.resource.common.dto.user.UserDTO userDTO1 = new com.holderzone.resource.common.dto.user.UserDTO();
        userDTO1.setUserGuid("userGuid");
        userDTO1.setEnterpriseGuid("enterpriseGuid");
        userDTO1.setAccount("account");
        userDTO1.setPassword("password");
        userDTO1.setName("name");
        userDTO1.setTel("phone");
        when(mockUserClient.getAdminByEnterpriseGuid("enterpriseGuid")).thenReturn(userDTO1);

        // Run the test
        final List<UserDTO> result = userDataServiceImplUnderTest.queryAIOUsers(baseDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryAIOUsers_UserMapperReturnsNoItems() {
        // Setup
        final BaseDTO baseDTO = new BaseDTO();
        baseDTO.setDeviceType(0);
        baseDTO.setDeviceId("deviceId");
        baseDTO.setEnterpriseGuid("enterpriseGuid");
        baseDTO.setEnterpriseName("enterpriseName");
        baseDTO.setStoreGuid("storeGuid");

        when(mockUserMapper.queryAIOUsers("storeGuid")).thenReturn(Collections.emptyList());

        // Configure UserClient.getAdminByEnterpriseGuid(...).
        final com.holderzone.resource.common.dto.user.UserDTO userDTO = new com.holderzone.resource.common.dto.user.UserDTO();
        userDTO.setUserGuid("userGuid");
        userDTO.setEnterpriseGuid("enterpriseGuid");
        userDTO.setAccount("account");
        userDTO.setPassword("password");
        userDTO.setName("name");
        userDTO.setTel("phone");
        when(mockUserClient.getAdminByEnterpriseGuid("enterpriseGuid")).thenReturn(userDTO);

        // Run the test
        final List<UserDTO> result = userDataServiceImplUnderTest.queryAIOUsers(baseDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testQueryAIOUsers_UserClientReturnsNull() {
        // Setup
        final BaseDTO baseDTO = new BaseDTO();
        baseDTO.setDeviceType(0);
        baseDTO.setDeviceId("deviceId");
        baseDTO.setEnterpriseGuid("enterpriseGuid");
        baseDTO.setEnterpriseName("enterpriseName");
        baseDTO.setStoreGuid("storeGuid");

        final UserDTO userDTO = new UserDTO();
        userDTO.setGuid("userGuid");
        userDTO.setAccount("account");
        userDTO.setPassword("password");
        userDTO.setName("name");
        final UserDataStoreRuleDTO userDataStoreRule = new UserDataStoreRuleDTO();
        final UserDataDTO userDataDTO = new UserDataDTO();
        userDataDTO.setGuid("storeGuid");
        userDataDTO.setName("name");
        userDataDTO.setIsDeleted(false);
        userDataStoreRule.setUserStoreData(Arrays.asList(userDataDTO));
        userDTO.setUserDataStoreRule(userDataStoreRule);
        final UserDataCondRuleDTO userDataCondRuleDTO = new UserDataCondRuleDTO();
        final UserDataDTO userDataDTO1 = new UserDataDTO();
        userDataDTO1.setGuid("storeGuid");
        userDataDTO1.setName("name");
        userDataDTO1.setIsDeleted(false);
        userDataCondRuleDTO.setUserOrgData(Arrays.asList(userDataDTO1));
        final UserDataDTO userDataDTO2 = new UserDataDTO();
        userDataDTO2.setGuid("storeGuid");
        userDataDTO2.setName("name");
        userDataDTO2.setIsDeleted(false);
        userDataCondRuleDTO.setUserBrandData(Arrays.asList(userDataDTO2));
        final UserRegionDTO userRegionDTO = new UserRegionDTO();
        userRegionDTO.setAdcode("regionCode");
        userRegionDTO.setName("regionName");
        userRegionDTO.setIsDeleted(false);
        userDataCondRuleDTO.setUserRegionData(Arrays.asList(userRegionDTO));
        userDTO.setUserDataCondRuleList(Arrays.asList(userDataCondRuleDTO));
        userDTO.setDiscountThreshold(new BigDecimal("0.00"));
        userDTO.setAllowanceThreshold(new BigDecimal("0.00"));
        userDTO.setProductDiscountThreshold(new BigDecimal("0.00"));
        userDTO.setRefundThreshold(new BigDecimal("0.00"));
        final UserRoleDistDTO userRoleDistDTO = new UserRoleDistDTO();
        userRoleDistDTO.setGuid("c820b147-d4ec-47c9-93f9-201cdcf6a589");
        userRoleDistDTO.setIsChecked(false);
        userDTO.setUserRolesDistributable(Arrays.asList(userRoleDistDTO));
        final List<UserDTO> expectedResult = Arrays.asList(userDTO);

        // Configure UserMapper.queryAIOUsers(...).
        final UserReadDO userReadDO = new UserReadDO();
        userReadDO.setId(0L);
        userReadDO.setGuid("291bf94a-2e50-4551-80b3-5c4b48108024");
        userReadDO.setEnterpriseNo("enterpriseNo");
        userReadDO.setAccount("account");
        userReadDO.setPassword("password");
        final List<UserReadDO> userReadDOS = Arrays.asList(userReadDO);
        when(mockUserMapper.queryAIOUsers("storeGuid")).thenReturn(userReadDOS);

        when(mockUserClient.getAdminByEnterpriseGuid("enterpriseGuid")).thenReturn(null);

        // Run the test
        final List<UserDTO> result = userDataServiceImplUnderTest.queryAIOUsers(baseDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testBatchDeleteUserDataRules() {
        // Setup
        // Run the test
        userDataServiceImplUnderTest.batchDeleteUserDataRules(Arrays.asList("value"));

        // Verify the results
    }

    @Test
    public void testGetStaffAliveStoreCount() {
        // Setup
        when(mockOrgFeignClient.queryAllStoreGuid()).thenReturn(Arrays.asList("value"));

        // Configure OrgFeignClient.parseByCondition(...).
        final StoreParserDTO storeParserDTO = new StoreParserDTO();
        storeParserDTO.setAllStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setRegionCodeList(Arrays.asList("value"));
        storeParserDTO.setBrandGuidList(Arrays.asList("value"));
        storeParserDTO.setOrganizationGuidList(Arrays.asList("value"));
        when(mockOrgFeignClient.parseByCondition(storeParserDTO)).thenReturn(Arrays.asList("value"));

        // Run the test
        final Integer result = userDataServiceImplUnderTest.getStaffAliveStoreCount();

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    public void testGetStaffAliveStoreCount_OrgFeignClientQueryAllStoreGuidReturnsNoItems() {
        // Setup
        when(mockOrgFeignClient.queryAllStoreGuid()).thenReturn(Collections.emptyList());

        // Run the test
        final Integer result = userDataServiceImplUnderTest.getStaffAliveStoreCount();

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    public void testGetStaffAliveStoreCount_OrgFeignClientParseByConditionReturnsNoItems() {
        // Setup
        // Configure OrgFeignClient.parseByCondition(...).
        final StoreParserDTO storeParserDTO = new StoreParserDTO();
        storeParserDTO.setAllStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setRegionCodeList(Arrays.asList("value"));
        storeParserDTO.setBrandGuidList(Arrays.asList("value"));
        storeParserDTO.setOrganizationGuidList(Arrays.asList("value"));
        when(mockOrgFeignClient.parseByCondition(storeParserDTO)).thenReturn(Collections.emptyList());

        // Run the test
        final Integer result = userDataServiceImplUnderTest.getStaffAliveStoreCount();

        // Verify the results
        assertThat(result).isEqualTo(0);
    }
}
