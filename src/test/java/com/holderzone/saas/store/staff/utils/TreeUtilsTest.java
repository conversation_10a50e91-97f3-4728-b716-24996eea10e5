package com.holderzone.saas.store.staff.utils;

import com.holderzone.saas.store.dto.organization.RegionDTO;
import com.holderzone.saas.store.staff.entity.domain.UserDataDO;
import org.junit.Test;

import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

public class TreeUtilsTest {


    @Test
    public void testCollectRegionAsTree() {
        // Setup
        final List<UserDataDO> arrayOfRegionDO = Arrays.asList(UserDataDO.builder()
                .regionCode("regionCode")
                .regionName("regionName")
                .build());
        final List<RegionDTO> expectedResult = Arrays.asList(new RegionDTO("adcode", "name", false, Arrays.asList()));

        // Run the test
        final List<RegionDTO> result = TreeUtils.collectRegionAsTree(arrayOfRegionDO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
