package com.holderzone.saas.store.staff;

import com.alibaba.fastjson.JSON;
import com.holderzone.resource.common.dto.authorization.RoleResourceDTO;
import com.holderzone.resource.common.dto.user.UserDTO;
import com.holderzone.resource.common.dto.user.UserQuery;
import com.holderzone.saas.store.dto.user.ModuleSourceDTO;
import com.holderzone.saas.store.staff.mapstruct.StaffMapStruct;
import com.holderzone.saas.store.staff.utils.MathRandomUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * MockMVC的使用教程：https://blog.csdn.net/Adam_allen/article/details/79919921
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class HolderSaasStoreStaffApplicationTests {
    @Autowired
    private WebApplicationContext wac;

    private MockMvc mockMvc;

    @Value("${test.eureka.hostname1:defalut}")
    private String testValue;


    @Before
    public void setup() {
        // 构造MockMvc
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();
    }

    @Test
    public void testStream() {
        List<Integer> list = new ArrayList<>();
        for (int i = 0; i < 100000; i++) {
            list.add(i);
        }
        //方法一：使用流遍历操作
        int step = 1000;
        List<Integer> result = new ArrayList<>();
        IntStream.range(0, (list.size() + step - 1) / step)
                .parallel()
                .mapToObj(i -> list.stream()
                        .skip(i * step).limit(step)
                        .collect(Collectors.toList()))
                .forEachOrdered(result::addAll);
        System.out.println(result);

    }
    @Test
    public void testLog() {
       /* try {
            publisher.send(this, new DefaultLogBuilder("system", "localhost:8801",
                                                       OperatorType.ADD, Platform.OTHER, RecordType.NORMAL)
                    .setModule("测试员工服务日志")
                    .setTitle("this is test log.")
                    .setParams("test params")
                    .setContentAfter(null).build());
        } catch (ParamException ignored) {
        }*/
    }

    @Test
    public void testLog4j2() {
        // 注意日志的输出位置
        log.debug("this is debug level log output.");
        log.error("this is error level log output.");
        log.warn(testValue);
    }

    @Test
    @Ignore
    public void testCreateUser() throws Exception {
        UserDTO userDTO = new UserDTO();
        userDTO.setAccount("testAccount2");
        userDTO.setAddress("holder1");
        userDTO.setEmail("<EMAIL>");
        userDTO.setGender("1");
        userDTO.setName("userFirs1t");
        userDTO.setPassword("********");
        userDTO.setTel("***********");
        userDTO.setEnterpriseGuid("testEnterprise1");
        userDTO.setCreateStaffGuid("createStuffGuid");
        userDTO.setUpdateStaffGuid("updateStuffGuid");
        userDTO.setIsEnabled("0");
        String result = mockMvc.perform(post("/user")
                                                .accept(MediaType.APPLICATION_JSON_UTF8)
                                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                                .content(JSON.toJSONString(userDTO)))
                               .andDo(print())
                               .andExpect(status().isOk()).andReturn().getResponse().getContentAsString();
        System.out.println("-----testCreateUser方法的返回值 = " + result);
    }

    @Test
    @Ignore
    public void testDeleteUser() throws Exception {
        String result = mockMvc.perform(delete("/user/6438242020285261825"))
                               .andDo(print())
                               .andExpect(status().isOk())
                               .andReturn()
                               .getResponse()
                               .getContentAsString();
        System.out.println("-----testDeleteUser方法的返回值 = " + result);
    }

    @Test
    @Ignore
    public void testUpdateUserInfo() throws Exception {
        UserDTO userDTO = new UserDTO();
        userDTO.setUserGuid("6438242020285261825");
        userDTO.setAccount("testAccount");
        userDTO.setIsEnabled("1");
        String result = mockMvc.perform(put("/user")
                                                .accept(MediaType.APPLICATION_JSON_UTF8)
                                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                                .content(JSON.toJSONString(userDTO)))
                               .andDo(print())
                               .andExpect(status().isOk())
                               .andReturn()
                               .getResponse()
                               .getContentAsString();
        System.out.println("-----testUpdateUser方法的返回值 = " + result);
    }

    @Test
    @Ignore
    public void testQueryUserInfoByPage() throws Exception {
        UserQuery userQuery = new UserQuery();
        userQuery.setPageIndex(1);
        userQuery.setPageSize(100);
        String result = mockMvc.perform(post("/user/querybypage")
                                                .accept(MediaType.APPLICATION_JSON_UTF8)
                                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                                .content(JSON.toJSONString(userQuery)))
                               .andDo(print())
                               .andExpect(status().isOk())
                               .andReturn()
                               .getResponse()
                               .getContentAsString();
        System.out.println("-----testQueryUserInfoByPage方法的返回值 = " + result);
    }

    @Test
    public void testCreateRole() throws Exception {
//        RoleDTO roleDTO = new RoleDTO();
//        roleDTO.setRoleName("");
//        // 所属企业（门店）
//        roleDTO.setEnterpriseGuid("");
//        // 员工Id（创建人）
//        roleDTO.setCreateStaffGuid("");
//        roleDTO.setIsEnabled("1");
//        String result = mockMvc.perform(post("/role")
//                                                .accept(MediaType.APPLICATION_JSON_UTF8)
//                                                .contentType(MediaType.APPLICATION_JSON_UTF8)
//                                                .content(JSON.toJSONString(roleDTO)))
//                               .andDo(print())
//                               .andExpect(status().isOk()).andReturn().getResponse().getContentAsString();
//        System.out.println("-----testCreateRole方法的返回值 = " + result);
    }

    @Test
    @Ignore
    public void testDeleteRole() throws Exception {
        String result = mockMvc.perform(delete("/role/6424520079145501698"))
                               .andDo(print())
                               .andExpect(status().isOk())
                               .andReturn()
                               .getResponse()
                               .getContentAsString();
        System.out.println("-----testDeleteUser方法的返回值 = " + result);
    }

    @Test
    @Ignore
    public void testUpdateRoleInfo() throws Exception {
//        RoleDTO roleDTO = new RoleDTO();
//        roleDTO.setRoleGuid("6436122149787536385");
//        roleDTO.setRoleName("testModify");
//        roleDTO.setIsEnabled("1");
//        String result = mockMvc.perform(put("/role")
//                                                .accept(MediaType.APPLICATION_JSON_UTF8)
//                                                .contentType(MediaType.APPLICATION_JSON_UTF8)
//                                                .content(JSON.toJSONString(roleDTO)))
//                               .andDo(print())
//                               .andExpect(status().isOk())
//                               .andReturn()
//                               .getResponse()
//                               .getContentAsString();
//        System.out.println("-----testUpdateRoleInfo方法的返回值 = " + result);
    }

    @Test
    @Ignore
    public void testQueryRoleInfoByPage() throws Exception {
//        RoleQuery roleQuery = new RoleQuery();
//        roleQuery.setPageIndex(1);
//        roleQuery.setPageSize(100);
//        String result = mockMvc.perform(post("/role/querybypage")
//                                                .accept(MediaType.APPLICATION_JSON_UTF8)
//                                                .contentType(MediaType.APPLICATION_JSON_UTF8)
//                                                .content(JSON.toJSONString(roleQuery)))
//                               .andDo(print())
//                               .andExpect(status().isOk())
//                               .andReturn()
//                               .getResponse()
//                               .getContentAsString();
//        System.out.println("-----testQueryRoleInfoByPage方法的返回值 = " + result);
    }

    @Test
    public void addRoleAndStoreRelation() throws Exception {
//        UserRoleDTO roleDTO = new UserRoleDTO();
//        roleDTO.setUserGuid("6438242020285261825");
//        roleDTO.setAccount("testAccount2");
//        // storeList为门店的guid组成的List
//        // roleDTO.setStoreList(null);
//        // roleDTO.setRoleList(null);
//        String result = mockMvc.perform(post("user/role")
//                                                .accept(MediaType.APPLICATION_JSON_UTF8)
//                                                .contentType(MediaType.APPLICATION_JSON_UTF8)
//                                                .content(JSON.toJSONString(roleDTO)))
//                               .andDo(print())
//                               .andExpect(status().isOk())
//                               .andReturn()
//                               .getResponse()
//                               .getContentAsString();
//        System.out.println("-----addUserRoleRelation方法的返回值 = " + result);
    }

    @Test
    public void batchAddRoleAndStoreRelation() throws Exception {
//        UserRoleDTO roleDTO1 = new UserRoleDTO();
//        UserRoleDTO roleDTO2 = new UserRoleDTO();
//
//        List<UserRoleDTO> list = Arrays.asList(roleDTO1, roleDTO2);
//
//        String result = mockMvc.perform(post("user/role/batch")
//                                                .accept(MediaType.APPLICATION_JSON_UTF8)
//                                                .contentType(MediaType.APPLICATION_JSON_UTF8)
//                                                .content(JSON.toJSONString(list)))
//                               .andDo(print())
//                               .andExpect(status().isOk())
//                               .andReturn()
//                               .getResponse()
//                               .getContentAsString();
//        System.out.println("-----batchAddRoleAndStoreRelation方法的返回值 = " + result);
    }

    @Test
    @Ignore
    public void testPermissionByRoleGuid() throws Exception {
        String result = mockMvc.perform(get("/permission/findpermissionbyid/6424520079145501698"))
                               .andDo(print())
                               .andExpect(status().isOk())
                               .andReturn()
                               .getResponse()
                               .getContentAsString();
        System.out.println("-----testPermissionByRoleGuid方法的返回值 = " + result);
    }

    @Test
    @Ignore
    public void testPermissionListByRoleGuidList() throws Exception {
        List<String> request = Arrays.asList("6424520079145501698", "6425256965306361858", "6435719502736177153", "6435736740240825345");
        String result = mockMvc.perform(post("/permission/findpermissionbyrolelist")
                                                .accept(MediaType.APPLICATION_JSON_UTF8)
                                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                                .content(JSON.toJSONString(request)))
                               .andDo(print())
                               .andExpect(status().isOk())
                               .andReturn()
                               .getResponse()
                               .getContentAsString();
        System.out.println("-----testPermissionListByRoleGuidList方法的返回值 = " + result);
    }

    @Test
    public void testGrantPermission() throws Exception {
        RoleResourceDTO roleResourceDTO = new RoleResourceDTO();
        String result = mockMvc.perform(post("/permission/grantpermission")
                                                .accept(MediaType.APPLICATION_JSON_UTF8)
                                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                                .content(JSON.toJSONString(roleResourceDTO)))
                               .andDo(print())
                               .andExpect(status().isOk())
                               .andReturn()
                               .getResponse()
                               .getContentAsString();
        System.out.println("-----testGrantPermission方法的返回值 = " + result);
    }

    @Test
    public void testForgetPwd() throws Exception {
        String result = mockMvc.perform(get("/user/forgetPwd/1234556/12344")
                                                .accept(MediaType.APPLICATION_JSON_UTF8)
                                                .contentType(MediaType.APPLICATION_JSON_UTF8))
                               .andDo(print())
                               .andExpect(status().isOk())
                               .andReturn()
                               .getResponse()
                               .getContentAsString();
        System.out.println("-----testForgetPwd方法的返回值 = " + result);
    }

    /**
     * 查找ModuleSourceDTO中最低级的ModuleSourceDTO
     *
     * @param moduleSourceDTO moduleSourceDTO
     * @return 最低级的ModuleSourceDTO
     */
   private ModuleSourceDTO findLastSource(ModuleSourceDTO moduleSourceDTO) {
        // ModuleSourceDTO dto1 = moduleSourceDTO;

        if (moduleSourceDTO.getModules() == null || moduleSourceDTO.getModules().isEmpty()) {
            return moduleSourceDTO;
        }

        moduleSourceDTO.getModules().forEach(p -> this.findLastSource(StaffMapStruct.INSTANCE.toModuleDTO(p)));

        return null;
    }

    @Test
    public void testFlatmap() {
        Stream<List<Integer>> inputStream = Stream.of(
                Arrays.asList(1),
                Arrays.asList(2, 3),
                Arrays.asList(4, 5, 6)
        );
        Stream<Integer> outputStream = inputStream.flatMap(Collection::stream);
        // 扁平化
        List<Integer> result = outputStream.collect(Collectors.toList());
    }
}