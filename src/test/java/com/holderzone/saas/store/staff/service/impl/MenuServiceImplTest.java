package com.holderzone.saas.store.staff.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.resource.common.dto.product.MenuDTO;
import com.holderzone.saas.store.dto.user.MenuSourceDTO;
import com.holderzone.saas.store.dto.user.req.PermissionsReqDTO;
import com.holderzone.saas.store.dto.user.resp.PermissionsRespDTO;
import com.holderzone.saas.store.staff.config.MarketingConfig;
import com.holderzone.saas.store.staff.entity.domain.MenuDO;
import com.holderzone.saas.store.staff.entity.domain.StoreSourceDO;
import com.holderzone.saas.store.staff.entity.domain.UserDO;
import com.holderzone.saas.store.staff.entity.query.UserSourceQuery;
import com.holderzone.saas.store.staff.mapper.StoreSourceMapper;
import com.holderzone.saas.store.staff.mapper.UserMapper;
import com.holderzone.saas.store.staff.mapstruct.MenuMapStruct;
import com.holderzone.saas.store.staff.service.ProductService;
import com.holderzone.saas.store.staff.service.UserAuthorityService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class MenuServiceImplTest {

    @Mock
    private StoreSourceMapper mockStoreSourceMapper;
    @Mock
    private MenuMapStruct mockMenuMapStruct;
    @Mock
    private UserMapper mockUserMapper;
    @Mock
    private ProductService mockProductService;
    @Mock
    private UserAuthorityService mockUserAuthorityService;
    @Mock
    private MarketingConfig marketingConfig;

    private MenuServiceImpl menuServiceImplUnderTest;

    @Before
    public void setUp() {
        menuServiceImplUnderTest = new MenuServiceImpl(mockStoreSourceMapper, mockMenuMapStruct, mockUserMapper,
                mockProductService, mockUserAuthorityService, marketingConfig);
    }

    @Test
    public void testInsertMenu() {
        // Setup
        final MenuDTO menuDTO = new MenuDTO();
        menuDTO.setUrlGuid("moduleGuid");
        menuDTO.setMenuName("menuName");
        menuDTO.setParentId("parentIds");
        menuDTO.setMenuIcon("menuIcon");
        menuDTO.setIsEnabled(false);
        menuDTO.setCreateStaffGuid("createStaffGuid");
        menuDTO.setMenuGuid("menuGuid");
        menuDTO.setTerminalGuid("terminalGuid");
        menuDTO.setMenuSort(0);
        menuDTO.setCreateTime(0L);
        final List<MenuDTO> menuList = Arrays.asList(menuDTO);

        // Run the test
        final boolean result = menuServiceImplUnderTest.insertMenu(menuList);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testDeleteMenu() {
        // Setup
        // Run the test
        final boolean result = menuServiceImplUnderTest.deleteMenu("menuGuid");

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testUpdateMenu() {
        // Setup
        final MenuDTO menuDTO = new MenuDTO();
        menuDTO.setUrlGuid("moduleGuid");
        menuDTO.setMenuName("menuName");
        menuDTO.setParentId("parentIds");
        menuDTO.setMenuIcon("menuIcon");
        menuDTO.setIsEnabled(false);
        menuDTO.setCreateStaffGuid("createStaffGuid");
        menuDTO.setMenuGuid("menuGuid");
        menuDTO.setTerminalGuid("terminalGuid");
        menuDTO.setMenuSort(0);
        menuDTO.setCreateTime(0L);

        // Run the test
        final boolean result = menuServiceImplUnderTest.updateMenu(menuDTO);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testBindMenuAndPage() {
        // Setup
        final MenuDTO menuDTO = new MenuDTO();
        menuDTO.setUrlGuid("moduleGuid");
        menuDTO.setMenuName("menuName");
        menuDTO.setParentId("parentIds");
        menuDTO.setMenuIcon("menuIcon");
        menuDTO.setIsEnabled(false);
        menuDTO.setCreateStaffGuid("createStaffGuid");
        menuDTO.setMenuGuid("menuGuid");
        menuDTO.setTerminalGuid("terminalGuid");
        menuDTO.setMenuSort(0);
        menuDTO.setCreateTime(0L);
        final List<MenuDTO> menuList = Arrays.asList(menuDTO);

        // Run the test
        final boolean result = menuServiceImplUnderTest.bindMenuAndPage(menuList);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testGetMerchantMenu() {
        // Setup
        when(mockProductService.count(any(LambdaQueryWrapper.class))).thenReturn(0);

        // Configure StoreSourceMapper.selectList(...).
        final StoreSourceDO storeSourceDO = new StoreSourceDO();
        storeSourceDO.setTerminalCode("terminalCode");
        storeSourceDO.setModuleGuid("moduleGuid");
        storeSourceDO.setModuleType("moduleType");
        storeSourceDO.setPageTitle("pageTitle");
        storeSourceDO.setPageUrl("pageUrl");
        storeSourceDO.setSourceGuid("sourceGuid");
        storeSourceDO.setSourceName("sourceName");
        storeSourceDO.setSourceCode("sourceCode");
        storeSourceDO.setSourceUrl("sourceUrl");
        final List<StoreSourceDO> storeSourceDOS = Arrays.asList(storeSourceDO);
        when(mockStoreSourceMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(storeSourceDOS);

        // Configure MenuMapStruct.menuDOList2DTOList(...).
        final com.holderzone.saas.store.dto.user.MenuDTO menuDTO = new com.holderzone.saas.store.dto.user.MenuDTO();
        menuDTO.setMenuGuid("menuGuid");
        menuDTO.setMenuSort(0L);
        menuDTO.setParentIds("parentIds");
        menuDTO.setModuleGuid("moduleGuid");
        menuDTO.setPageTitle("pageTitle");
        menuDTO.setPageUrl("pageUrl");
        menuDTO.setMenus(Arrays.asList(new com.holderzone.saas.store.dto.user.MenuDTO()));
        final List<com.holderzone.saas.store.dto.user.MenuDTO> list = Arrays.asList(menuDTO);
        final MenuDO menuDO = new MenuDO();
        menuDO.setId(0L);
        menuDO.setMenuGuid("menuGuid");
        menuDO.setMenuName("menuName");
        menuDO.setParentIds("parentIds");
        menuDO.setMenuIcon("menuIcon");
        menuDO.setMenuSort(0L);
        menuDO.setModuleGuid("moduleGuid");
        menuDO.setTerminalGuid("terminalGuid");
        menuDO.setIsEnable(false);
        menuDO.setCreateStaffGuid("createStaffGuid");
        menuDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        menuDO.setGmtSync(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<MenuDO> menuDOList = Arrays.asList(menuDO);
        when(mockMenuMapStruct.menuDOList2DTOList(menuDOList)).thenReturn(list);

        // Run the test
        final List<com.holderzone.saas.store.dto.user.MenuDTO> result = menuServiceImplUnderTest.getMerchantMenu(
                "userGuid", "terminalCode");

        // Verify the results
    }

    @Test
    public void testGetMerchantMenu_StoreSourceMapperReturnsNoItems() {
        // Setup
        when(mockProductService.count(any(LambdaQueryWrapper.class))).thenReturn(0);
        when(mockStoreSourceMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        assertThatThrownBy(() -> menuServiceImplUnderTest.getMerchantMenu("userGuid", "terminalCode"))
                .isInstanceOf(BusinessException.class);
    }

    @Test
    public void testGetMerchantMenu_MenuMapStructReturnsNoItems() {
        // Setup
        when(mockProductService.count(any(LambdaQueryWrapper.class))).thenReturn(0);

        // Configure StoreSourceMapper.selectList(...).
        final StoreSourceDO storeSourceDO = new StoreSourceDO();
        storeSourceDO.setTerminalCode("terminalCode");
        storeSourceDO.setModuleGuid("moduleGuid");
        storeSourceDO.setModuleType("moduleType");
        storeSourceDO.setPageTitle("pageTitle");
        storeSourceDO.setPageUrl("pageUrl");
        storeSourceDO.setSourceGuid("sourceGuid");
        storeSourceDO.setSourceName("sourceName");
        storeSourceDO.setSourceCode("sourceCode");
        storeSourceDO.setSourceUrl("sourceUrl");
        final List<StoreSourceDO> storeSourceDOS = Arrays.asList(storeSourceDO);
        when(mockStoreSourceMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(storeSourceDOS);

        // Configure MenuMapStruct.menuDOList2DTOList(...).
        final MenuDO menuDO = new MenuDO();
        menuDO.setId(0L);
        menuDO.setMenuGuid("menuGuid");
        menuDO.setMenuName("menuName");
        menuDO.setParentIds("parentIds");
        menuDO.setMenuIcon("menuIcon");
        menuDO.setMenuSort(0L);
        menuDO.setModuleGuid("moduleGuid");
        menuDO.setTerminalGuid("terminalGuid");
        menuDO.setIsEnable(false);
        menuDO.setCreateStaffGuid("createStaffGuid");
        menuDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        menuDO.setGmtSync(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<MenuDO> menuDOList = Arrays.asList(menuDO);
        when(mockMenuMapStruct.menuDOList2DTOList(menuDOList)).thenReturn(Collections.emptyList());

        // Run the test
        final List<com.holderzone.saas.store.dto.user.MenuDTO> result = menuServiceImplUnderTest.getMerchantMenu(
                "userGuid", "terminalCode");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testQueryUserSourceOnOut() {
        // Setup
        // Configure UserMapper.selectOne(...).
        final UserDO userDO = new UserDO();
        userDO.setId(0L);
        userDO.setGuid("guid");
        userDO.setEnterpriseNo("enterpriseNo");
        userDO.setAccount("account");
        userDO.setPhone("phone");
        when(mockUserMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(userDO);

        // Configure UserMapper.queryUserSourceOfAdminOnOut(...).
        final MenuSourceDTO menuSourceDTO = new MenuSourceDTO();
        menuSourceDTO.setSourceGuid("");
        menuSourceDTO.setSourceName("sourceName");
        menuSourceDTO.setSourceCode("sourceCode");
        menuSourceDTO.setIsElevatedPrivileges(false);
        menuSourceDTO.setType(0);
        final List<MenuSourceDTO> menuSourceDTOS = Arrays.asList(menuSourceDTO);
        when(mockUserMapper.queryUserSourceOfAdminOnOut(0)).thenReturn(menuSourceDTOS);

        // Configure UserMapper.queryUserSourceOnOut(...).
        final MenuSourceDTO menuSourceDTO1 = new MenuSourceDTO();
        menuSourceDTO1.setSourceGuid("");
        menuSourceDTO1.setSourceName("sourceName");
        menuSourceDTO1.setSourceCode("sourceCode");
        menuSourceDTO1.setIsElevatedPrivileges(false);
        menuSourceDTO1.setType(0);
        final List<MenuSourceDTO> menuSourceDTOS1 = Arrays.asList(menuSourceDTO1);
        when(mockUserMapper.queryUserSourceOnOut("guid", 0)).thenReturn(menuSourceDTOS1);

        // Run the test
        final List<String> result = menuServiceImplUnderTest.queryUserSourceOnOut(0, "userOrPhone", "enterpriseGuid");

        // Verify the results
        assertThat(result).isEqualTo(Arrays.asList("value"));
    }

    @Test
    public void testQueryUserSourceOnOut_UserMapperQueryUserSourceOfAdminOnOutReturnsNoItems() {
        // Setup
        // Configure UserMapper.selectOne(...).
        final UserDO userDO = new UserDO();
        userDO.setId(0L);
        userDO.setGuid("guid");
        userDO.setEnterpriseNo("enterpriseNo");
        userDO.setAccount("account");
        userDO.setPhone("phone");
        when(mockUserMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(userDO);

        when(mockUserMapper.queryUserSourceOfAdminOnOut(0)).thenReturn(Collections.emptyList());

        // Run the test
        final List<String> result = menuServiceImplUnderTest.queryUserSourceOnOut(0, "userOrPhone", "enterpriseGuid");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testQueryUserSourceOnOut_UserMapperQueryUserSourceOnOutReturnsNoItems() {
        // Setup
        // Configure UserMapper.selectOne(...).
        final UserDO userDO = new UserDO();
        userDO.setId(0L);
        userDO.setGuid("guid");
        userDO.setEnterpriseNo("enterpriseNo");
        userDO.setAccount("account");
        userDO.setPhone("phone");
        when(mockUserMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(userDO);

        when(mockUserMapper.queryUserSourceOnOut("guid", 0)).thenReturn(Collections.emptyList());

        // Run the test
        final List<String> result = menuServiceImplUnderTest.queryUserSourceOnOut(0, "userOrPhone", "enterpriseGuid");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testGetSourceByUser() {
        // Setup
        // Configure UserAuthorityService.queryEmployeePermissions(...).
        final PermissionsRespDTO permissionsRespDTO = new PermissionsRespDTO();
        permissionsRespDTO.setAuthorizerGuid("authorizerGuid");
        permissionsRespDTO.setAuthorizerAccount("authorizerAccount");
        permissionsRespDTO.setSourceFrom(0);
        permissionsRespDTO.setSourceName("sourceName");
        permissionsRespDTO.setSourceCode("sourceCode");
        final List<PermissionsRespDTO> permissionsRespDTOS = Arrays.asList(permissionsRespDTO);
        final PermissionsReqDTO reqDTO = new PermissionsReqDTO();
        reqDTO.setSourceCode("sourceCode");
        reqDTO.setSourceFrom(0);
        reqDTO.setUserGuid("userGuid");
        reqDTO.setTerminalCode("terminalCode");
        reqDTO.setStoreGuid("storeGuid");
        when(mockUserAuthorityService.queryEmployeePermissions(reqDTO)).thenReturn(permissionsRespDTOS);

        // Configure UserMapper.queryUserSourceOfAdmin(...).
        final MenuSourceDTO menuSourceDTO = new MenuSourceDTO();
        menuSourceDTO.setSourceGuid("");
        menuSourceDTO.setSourceName("sourceName");
        menuSourceDTO.setSourceCode("sourceCode");
        menuSourceDTO.setIsElevatedPrivileges(false);
        menuSourceDTO.setType(0);
        final List<MenuSourceDTO> menuSourceDTOS = Arrays.asList(menuSourceDTO);
        final UserSourceQuery userSourceQuery = new UserSourceQuery();
        userSourceQuery.setUserGuid("userGuid");
        userSourceQuery.setTerminalCode("terminalCode");
        userSourceQuery.setMenuGuid("menuGuid");
        userSourceQuery.setRequestUri("requestUri");
        userSourceQuery.setStoreGuid("storeGuid");
        when(mockUserMapper.queryUserSourceOfAdmin(userSourceQuery)).thenReturn(menuSourceDTOS);

        // Configure UserMapper.queryUserSource(...).
        final MenuSourceDTO menuSourceDTO1 = new MenuSourceDTO();
        menuSourceDTO1.setSourceGuid("");
        menuSourceDTO1.setSourceName("sourceName");
        menuSourceDTO1.setSourceCode("sourceCode");
        menuSourceDTO1.setIsElevatedPrivileges(false);
        menuSourceDTO1.setType(0);
        final List<MenuSourceDTO> menuSourceDTOS1 = Arrays.asList(menuSourceDTO1);
        final UserSourceQuery userSourceQuery1 = new UserSourceQuery();
        userSourceQuery1.setUserGuid("userGuid");
        userSourceQuery1.setTerminalCode("terminalCode");
        userSourceQuery1.setMenuGuid("menuGuid");
        userSourceQuery1.setRequestUri("requestUri");
        userSourceQuery1.setStoreGuid("storeGuid");
        when(mockUserMapper.queryUserSource(userSourceQuery1)).thenReturn(menuSourceDTOS1);

        // Configure UserAuthorityService.queryUserAuthority(...).
        final PermissionsReqDTO reqDTO1 = new PermissionsReqDTO();
        reqDTO1.setSourceCode("sourceCode");
        reqDTO1.setSourceFrom(0);
        reqDTO1.setUserGuid("userGuid");
        reqDTO1.setTerminalCode("terminalCode");
        reqDTO1.setStoreGuid("storeGuid");
        when(mockUserAuthorityService.queryUserAuthority(reqDTO1)).thenReturn(0);

        // Run the test
        final List<MenuSourceDTO> result = menuServiceImplUnderTest.getSourceByUser("terminalCode");

        // Verify the results
    }

    @Test
    public void testGetSourceByUser_UserAuthorityServiceQueryEmployeePermissionsReturnsNoItems() {
        // Setup
        // Configure UserAuthorityService.queryEmployeePermissions(...).
        final PermissionsReqDTO reqDTO = new PermissionsReqDTO();
        reqDTO.setSourceCode("sourceCode");
        reqDTO.setSourceFrom(0);
        reqDTO.setUserGuid("userGuid");
        reqDTO.setTerminalCode("terminalCode");
        reqDTO.setStoreGuid("storeGuid");
        when(mockUserAuthorityService.queryEmployeePermissions(reqDTO)).thenReturn(Collections.emptyList());

        // Run the test
        assertThatThrownBy(() -> menuServiceImplUnderTest.getSourceByUser("terminalCode"))
                .isInstanceOf(BusinessException.class);
    }

    @Test
    public void testGetSourceByUser_UserMapperQueryUserSourceOfAdminReturnsNoItems() {
        // Setup
        // Configure UserAuthorityService.queryEmployeePermissions(...).
        final PermissionsRespDTO permissionsRespDTO = new PermissionsRespDTO();
        permissionsRespDTO.setAuthorizerGuid("authorizerGuid");
        permissionsRespDTO.setAuthorizerAccount("authorizerAccount");
        permissionsRespDTO.setSourceFrom(0);
        permissionsRespDTO.setSourceName("sourceName");
        permissionsRespDTO.setSourceCode("sourceCode");
        final List<PermissionsRespDTO> permissionsRespDTOS = Arrays.asList(permissionsRespDTO);
        final PermissionsReqDTO reqDTO = new PermissionsReqDTO();
        reqDTO.setSourceCode("sourceCode");
        reqDTO.setSourceFrom(0);
        reqDTO.setUserGuid("userGuid");
        reqDTO.setTerminalCode("terminalCode");
        reqDTO.setStoreGuid("storeGuid");
        when(mockUserAuthorityService.queryEmployeePermissions(reqDTO)).thenReturn(permissionsRespDTOS);

        // Configure UserMapper.queryUserSourceOfAdmin(...).
        final UserSourceQuery userSourceQuery = new UserSourceQuery();
        userSourceQuery.setUserGuid("userGuid");
        userSourceQuery.setTerminalCode("terminalCode");
        userSourceQuery.setMenuGuid("menuGuid");
        userSourceQuery.setRequestUri("requestUri");
        userSourceQuery.setStoreGuid("storeGuid");
        when(mockUserMapper.queryUserSourceOfAdmin(userSourceQuery)).thenReturn(Collections.emptyList());

        // Run the test
        final List<MenuSourceDTO> result = menuServiceImplUnderTest.getSourceByUser("terminalCode");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testGetSourceByUser_UserMapperQueryUserSourceReturnsNoItems() {
        // Setup
        // Configure UserAuthorityService.queryEmployeePermissions(...).
        final PermissionsRespDTO permissionsRespDTO = new PermissionsRespDTO();
        permissionsRespDTO.setAuthorizerGuid("authorizerGuid");
        permissionsRespDTO.setAuthorizerAccount("authorizerAccount");
        permissionsRespDTO.setSourceFrom(0);
        permissionsRespDTO.setSourceName("sourceName");
        permissionsRespDTO.setSourceCode("sourceCode");
        final List<PermissionsRespDTO> permissionsRespDTOS = Arrays.asList(permissionsRespDTO);
        final PermissionsReqDTO reqDTO = new PermissionsReqDTO();
        reqDTO.setSourceCode("sourceCode");
        reqDTO.setSourceFrom(0);
        reqDTO.setUserGuid("userGuid");
        reqDTO.setTerminalCode("terminalCode");
        reqDTO.setStoreGuid("storeGuid");
        when(mockUserAuthorityService.queryEmployeePermissions(reqDTO)).thenReturn(permissionsRespDTOS);

        // Configure UserMapper.queryUserSource(...).
        final UserSourceQuery userSourceQuery = new UserSourceQuery();
        userSourceQuery.setUserGuid("userGuid");
        userSourceQuery.setTerminalCode("terminalCode");
        userSourceQuery.setMenuGuid("menuGuid");
        userSourceQuery.setRequestUri("requestUri");
        userSourceQuery.setStoreGuid("storeGuid");
        when(mockUserMapper.queryUserSource(userSourceQuery)).thenReturn(Collections.emptyList());

        // Run the test
        final List<MenuSourceDTO> result = menuServiceImplUnderTest.getSourceByUser("terminalCode");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testGetPageUrlByMenu() {
        // Setup
        // Configure StoreSourceMapper.queryAdminSource(...).
        final StoreSourceDO storeSourceDO = new StoreSourceDO();
        storeSourceDO.setTerminalCode("terminalCode");
        storeSourceDO.setModuleGuid("moduleGuid");
        storeSourceDO.setModuleType("moduleType");
        storeSourceDO.setPageTitle("pageTitle");
        storeSourceDO.setPageUrl("pageUrl");
        storeSourceDO.setSourceGuid("sourceGuid");
        storeSourceDO.setSourceName("sourceName");
        storeSourceDO.setSourceCode("sourceCode");
        storeSourceDO.setSourceUrl("sourceUrl");
        final List<StoreSourceDO> storeSourceDOS = Arrays.asList(storeSourceDO);
        when(mockStoreSourceMapper.queryAdminSource("pageUrl")).thenReturn(storeSourceDOS);

        // Configure StoreSourceMapper.querySourceByUserAndMenu(...).
        final StoreSourceDO storeSourceDO1 = new StoreSourceDO();
        storeSourceDO1.setTerminalCode("terminalCode");
        storeSourceDO1.setModuleGuid("moduleGuid");
        storeSourceDO1.setModuleType("moduleType");
        storeSourceDO1.setPageTitle("pageTitle");
        storeSourceDO1.setPageUrl("pageUrl");
        storeSourceDO1.setSourceGuid("sourceGuid");
        storeSourceDO1.setSourceName("sourceName");
        storeSourceDO1.setSourceCode("sourceCode");
        storeSourceDO1.setSourceUrl("sourceUrl");
        final List<StoreSourceDO> storeSourceDOS1 = Arrays.asList(storeSourceDO1);
        when(mockStoreSourceMapper.querySourceByUserAndMenu("userGuid", "menuGuid", "pageUrl"))
                .thenReturn(storeSourceDOS1);

        // Configure MenuMapStruct.storeSourceDOList2MenuSource(...).
        final MenuSourceDTO menuSourceDTO = new MenuSourceDTO();
        menuSourceDTO.setSourceGuid("");
        menuSourceDTO.setSourceName("sourceName");
        menuSourceDTO.setSourceCode("sourceCode");
        menuSourceDTO.setIsElevatedPrivileges(false);
        menuSourceDTO.setType(0);
        final List<MenuSourceDTO> menuSourceDTOS = Arrays.asList(menuSourceDTO);
        final StoreSourceDO storeSourceDO2 = new StoreSourceDO();
        storeSourceDO2.setTerminalCode("terminalCode");
        storeSourceDO2.setModuleGuid("moduleGuid");
        storeSourceDO2.setModuleType("moduleType");
        storeSourceDO2.setPageTitle("pageTitle");
        storeSourceDO2.setPageUrl("pageUrl");
        storeSourceDO2.setSourceGuid("sourceGuid");
        storeSourceDO2.setSourceName("sourceName");
        storeSourceDO2.setSourceCode("sourceCode");
        storeSourceDO2.setSourceUrl("sourceUrl");
        final List<StoreSourceDO> storeSourceDOList = Arrays.asList(storeSourceDO2);
        when(mockMenuMapStruct.storeSourceDOList2MenuSource(storeSourceDOList)).thenReturn(menuSourceDTOS);

        // Run the test
        final List<MenuSourceDTO> result = menuServiceImplUnderTest.getPageUrlByMenu("pageUrl");

        // Verify the results
    }

    @Test
    public void testGetPageUrlByMenu_StoreSourceMapperQueryAdminSourceReturnsNoItems() {
        // Setup
        when(mockStoreSourceMapper.queryAdminSource("pageUrl")).thenReturn(Collections.emptyList());

        // Run the test
        final List<MenuSourceDTO> result = menuServiceImplUnderTest.getPageUrlByMenu("pageUrl");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testGetPageUrlByMenu_StoreSourceMapperQuerySourceByUserAndMenuReturnsNoItems() {
        // Setup
        when(mockStoreSourceMapper.querySourceByUserAndMenu("userGuid", "menuGuid", "pageUrl"))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<MenuSourceDTO> result = menuServiceImplUnderTest.getPageUrlByMenu("pageUrl");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testGetPageUrlByMenu_MenuMapStructReturnsNoItems() {
        // Setup
        // Configure StoreSourceMapper.queryAdminSource(...).
        final StoreSourceDO storeSourceDO = new StoreSourceDO();
        storeSourceDO.setTerminalCode("terminalCode");
        storeSourceDO.setModuleGuid("moduleGuid");
        storeSourceDO.setModuleType("moduleType");
        storeSourceDO.setPageTitle("pageTitle");
        storeSourceDO.setPageUrl("pageUrl");
        storeSourceDO.setSourceGuid("sourceGuid");
        storeSourceDO.setSourceName("sourceName");
        storeSourceDO.setSourceCode("sourceCode");
        storeSourceDO.setSourceUrl("sourceUrl");
        final List<StoreSourceDO> storeSourceDOS = Arrays.asList(storeSourceDO);
        when(mockStoreSourceMapper.queryAdminSource("pageUrl")).thenReturn(storeSourceDOS);

        // Configure MenuMapStruct.storeSourceDOList2MenuSource(...).
        final StoreSourceDO storeSourceDO1 = new StoreSourceDO();
        storeSourceDO1.setTerminalCode("terminalCode");
        storeSourceDO1.setModuleGuid("moduleGuid");
        storeSourceDO1.setModuleType("moduleType");
        storeSourceDO1.setPageTitle("pageTitle");
        storeSourceDO1.setPageUrl("pageUrl");
        storeSourceDO1.setSourceGuid("sourceGuid");
        storeSourceDO1.setSourceName("sourceName");
        storeSourceDO1.setSourceCode("sourceCode");
        storeSourceDO1.setSourceUrl("sourceUrl");
        final List<StoreSourceDO> storeSourceDOList = Arrays.asList(storeSourceDO1);
        when(mockMenuMapStruct.storeSourceDOList2MenuSource(storeSourceDOList)).thenReturn(Collections.emptyList());

        // Run the test
        final List<MenuSourceDTO> result = menuServiceImplUnderTest.getPageUrlByMenu("pageUrl");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testGetSourceByMenu() {
        // Setup
        // Configure StoreSourceMapper.selectList(...).
        final StoreSourceDO storeSourceDO = new StoreSourceDO();
        storeSourceDO.setTerminalCode("terminalCode");
        storeSourceDO.setModuleGuid("moduleGuid");
        storeSourceDO.setModuleType("moduleType");
        storeSourceDO.setPageTitle("pageTitle");
        storeSourceDO.setPageUrl("pageUrl");
        storeSourceDO.setSourceGuid("sourceGuid");
        storeSourceDO.setSourceName("sourceName");
        storeSourceDO.setSourceCode("sourceCode");
        storeSourceDO.setSourceUrl("sourceUrl");
        final List<StoreSourceDO> storeSourceDOS = Arrays.asList(storeSourceDO);
        when(mockStoreSourceMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(storeSourceDOS);

        // Configure StoreSourceMapper.querySourceByUserAndMenu(...).
        final StoreSourceDO storeSourceDO1 = new StoreSourceDO();
        storeSourceDO1.setTerminalCode("terminalCode");
        storeSourceDO1.setModuleGuid("moduleGuid");
        storeSourceDO1.setModuleType("moduleType");
        storeSourceDO1.setPageTitle("pageTitle");
        storeSourceDO1.setPageUrl("pageUrl");
        storeSourceDO1.setSourceGuid("sourceGuid");
        storeSourceDO1.setSourceName("sourceName");
        storeSourceDO1.setSourceCode("sourceCode");
        storeSourceDO1.setSourceUrl("sourceUrl");
        final List<StoreSourceDO> storeSourceDOS1 = Arrays.asList(storeSourceDO1);
        when(mockStoreSourceMapper.querySourceByUserAndMenu("userGuid", "menuGuid", "pageUrl"))
                .thenReturn(storeSourceDOS1);

        // Configure StoreSourceMapper.querySourceByCode(...).
        final StoreSourceDO storeSourceDO2 = new StoreSourceDO();
        storeSourceDO2.setTerminalCode("terminalCode");
        storeSourceDO2.setModuleGuid("moduleGuid");
        storeSourceDO2.setModuleType("moduleType");
        storeSourceDO2.setPageTitle("pageTitle");
        storeSourceDO2.setPageUrl("pageUrl");
        storeSourceDO2.setSourceGuid("sourceGuid");
        storeSourceDO2.setSourceName("sourceName");
        storeSourceDO2.setSourceCode("sourceCode");
        storeSourceDO2.setSourceUrl("sourceUrl");
        when(mockStoreSourceMapper.querySourceByCode("organization_create")).thenReturn(storeSourceDO2);

        // Configure StoreSourceMapper.querySourceByUserAndCode(...).
        final StoreSourceDO storeSourceDO3 = new StoreSourceDO();
        storeSourceDO3.setTerminalCode("terminalCode");
        storeSourceDO3.setModuleGuid("moduleGuid");
        storeSourceDO3.setModuleType("moduleType");
        storeSourceDO3.setPageTitle("pageTitle");
        storeSourceDO3.setPageUrl("pageUrl");
        storeSourceDO3.setSourceGuid("sourceGuid");
        storeSourceDO3.setSourceName("sourceName");
        storeSourceDO3.setSourceCode("sourceCode");
        storeSourceDO3.setSourceUrl("sourceUrl");
        when(mockStoreSourceMapper.querySourceByUserAndCode("userGuid", "organization_create"))
                .thenReturn(storeSourceDO3);

        // Configure MenuMapStruct.storeSourceDOList2MenuSource(...).
        final MenuSourceDTO menuSourceDTO = new MenuSourceDTO();
        menuSourceDTO.setSourceGuid("");
        menuSourceDTO.setSourceName("sourceName");
        menuSourceDTO.setSourceCode("sourceCode");
        menuSourceDTO.setIsElevatedPrivileges(false);
        menuSourceDTO.setType(0);
        final List<MenuSourceDTO> menuSourceDTOS = Arrays.asList(menuSourceDTO);
        final StoreSourceDO storeSourceDO4 = new StoreSourceDO();
        storeSourceDO4.setTerminalCode("terminalCode");
        storeSourceDO4.setModuleGuid("moduleGuid");
        storeSourceDO4.setModuleType("moduleType");
        storeSourceDO4.setPageTitle("pageTitle");
        storeSourceDO4.setPageUrl("pageUrl");
        storeSourceDO4.setSourceGuid("sourceGuid");
        storeSourceDO4.setSourceName("sourceName");
        storeSourceDO4.setSourceCode("sourceCode");
        storeSourceDO4.setSourceUrl("sourceUrl");
        final List<StoreSourceDO> storeSourceDOList = Arrays.asList(storeSourceDO4);
        when(mockMenuMapStruct.storeSourceDOList2MenuSource(storeSourceDOList)).thenReturn(menuSourceDTOS);

        // Run the test
        final List<MenuSourceDTO> result = menuServiceImplUnderTest.getSourceByMenu("menuGuid");

        // Verify the results
    }

    @Test
    public void testGetSourceByMenu_StoreSourceMapperSelectListReturnsNoItems() {
        // Setup
        when(mockStoreSourceMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure StoreSourceMapper.querySourceByCode(...).
        final StoreSourceDO storeSourceDO = new StoreSourceDO();
        storeSourceDO.setTerminalCode("terminalCode");
        storeSourceDO.setModuleGuid("moduleGuid");
        storeSourceDO.setModuleType("moduleType");
        storeSourceDO.setPageTitle("pageTitle");
        storeSourceDO.setPageUrl("pageUrl");
        storeSourceDO.setSourceGuid("sourceGuid");
        storeSourceDO.setSourceName("sourceName");
        storeSourceDO.setSourceCode("sourceCode");
        storeSourceDO.setSourceUrl("sourceUrl");
        when(mockStoreSourceMapper.querySourceByCode("organization_create")).thenReturn(storeSourceDO);

        // Configure StoreSourceMapper.querySourceByUserAndCode(...).
        final StoreSourceDO storeSourceDO1 = new StoreSourceDO();
        storeSourceDO1.setTerminalCode("terminalCode");
        storeSourceDO1.setModuleGuid("moduleGuid");
        storeSourceDO1.setModuleType("moduleType");
        storeSourceDO1.setPageTitle("pageTitle");
        storeSourceDO1.setPageUrl("pageUrl");
        storeSourceDO1.setSourceGuid("sourceGuid");
        storeSourceDO1.setSourceName("sourceName");
        storeSourceDO1.setSourceCode("sourceCode");
        storeSourceDO1.setSourceUrl("sourceUrl");
        when(mockStoreSourceMapper.querySourceByUserAndCode("userGuid", "organization_create"))
                .thenReturn(storeSourceDO1);

        // Configure MenuMapStruct.storeSourceDOList2MenuSource(...).
        final MenuSourceDTO menuSourceDTO = new MenuSourceDTO();
        menuSourceDTO.setSourceGuid("");
        menuSourceDTO.setSourceName("sourceName");
        menuSourceDTO.setSourceCode("sourceCode");
        menuSourceDTO.setIsElevatedPrivileges(false);
        menuSourceDTO.setType(0);
        final List<MenuSourceDTO> menuSourceDTOS = Arrays.asList(menuSourceDTO);
        final StoreSourceDO storeSourceDO2 = new StoreSourceDO();
        storeSourceDO2.setTerminalCode("terminalCode");
        storeSourceDO2.setModuleGuid("moduleGuid");
        storeSourceDO2.setModuleType("moduleType");
        storeSourceDO2.setPageTitle("pageTitle");
        storeSourceDO2.setPageUrl("pageUrl");
        storeSourceDO2.setSourceGuid("sourceGuid");
        storeSourceDO2.setSourceName("sourceName");
        storeSourceDO2.setSourceCode("sourceCode");
        storeSourceDO2.setSourceUrl("sourceUrl");
        final List<StoreSourceDO> storeSourceDOList = Arrays.asList(storeSourceDO2);
        when(mockMenuMapStruct.storeSourceDOList2MenuSource(storeSourceDOList)).thenReturn(menuSourceDTOS);

        // Run the test
        final List<MenuSourceDTO> result = menuServiceImplUnderTest.getSourceByMenu("menuGuid");

        // Verify the results
    }

    @Test
    public void testGetSourceByMenu_StoreSourceMapperQuerySourceByUserAndMenuReturnsNoItems() {
        // Setup
        when(mockStoreSourceMapper.querySourceByUserAndMenu("userGuid", "menuGuid", "pageUrl"))
                .thenReturn(Collections.emptyList());

        // Configure StoreSourceMapper.querySourceByCode(...).
        final StoreSourceDO storeSourceDO = new StoreSourceDO();
        storeSourceDO.setTerminalCode("terminalCode");
        storeSourceDO.setModuleGuid("moduleGuid");
        storeSourceDO.setModuleType("moduleType");
        storeSourceDO.setPageTitle("pageTitle");
        storeSourceDO.setPageUrl("pageUrl");
        storeSourceDO.setSourceGuid("sourceGuid");
        storeSourceDO.setSourceName("sourceName");
        storeSourceDO.setSourceCode("sourceCode");
        storeSourceDO.setSourceUrl("sourceUrl");
        when(mockStoreSourceMapper.querySourceByCode("organization_create")).thenReturn(storeSourceDO);

        // Configure StoreSourceMapper.querySourceByUserAndCode(...).
        final StoreSourceDO storeSourceDO1 = new StoreSourceDO();
        storeSourceDO1.setTerminalCode("terminalCode");
        storeSourceDO1.setModuleGuid("moduleGuid");
        storeSourceDO1.setModuleType("moduleType");
        storeSourceDO1.setPageTitle("pageTitle");
        storeSourceDO1.setPageUrl("pageUrl");
        storeSourceDO1.setSourceGuid("sourceGuid");
        storeSourceDO1.setSourceName("sourceName");
        storeSourceDO1.setSourceCode("sourceCode");
        storeSourceDO1.setSourceUrl("sourceUrl");
        when(mockStoreSourceMapper.querySourceByUserAndCode("userGuid", "organization_create"))
                .thenReturn(storeSourceDO1);

        // Configure MenuMapStruct.storeSourceDOList2MenuSource(...).
        final MenuSourceDTO menuSourceDTO = new MenuSourceDTO();
        menuSourceDTO.setSourceGuid("");
        menuSourceDTO.setSourceName("sourceName");
        menuSourceDTO.setSourceCode("sourceCode");
        menuSourceDTO.setIsElevatedPrivileges(false);
        menuSourceDTO.setType(0);
        final List<MenuSourceDTO> menuSourceDTOS = Arrays.asList(menuSourceDTO);
        final StoreSourceDO storeSourceDO2 = new StoreSourceDO();
        storeSourceDO2.setTerminalCode("terminalCode");
        storeSourceDO2.setModuleGuid("moduleGuid");
        storeSourceDO2.setModuleType("moduleType");
        storeSourceDO2.setPageTitle("pageTitle");
        storeSourceDO2.setPageUrl("pageUrl");
        storeSourceDO2.setSourceGuid("sourceGuid");
        storeSourceDO2.setSourceName("sourceName");
        storeSourceDO2.setSourceCode("sourceCode");
        storeSourceDO2.setSourceUrl("sourceUrl");
        final List<StoreSourceDO> storeSourceDOList = Arrays.asList(storeSourceDO2);
        when(mockMenuMapStruct.storeSourceDOList2MenuSource(storeSourceDOList)).thenReturn(menuSourceDTOS);

        // Run the test
        final List<MenuSourceDTO> result = menuServiceImplUnderTest.getSourceByMenu("menuGuid");

        // Verify the results
    }

    @Test
    public void testGetSourceByMenu_MenuMapStructReturnsNoItems() {
        // Setup
        // Configure StoreSourceMapper.selectList(...).
        final StoreSourceDO storeSourceDO = new StoreSourceDO();
        storeSourceDO.setTerminalCode("terminalCode");
        storeSourceDO.setModuleGuid("moduleGuid");
        storeSourceDO.setModuleType("moduleType");
        storeSourceDO.setPageTitle("pageTitle");
        storeSourceDO.setPageUrl("pageUrl");
        storeSourceDO.setSourceGuid("sourceGuid");
        storeSourceDO.setSourceName("sourceName");
        storeSourceDO.setSourceCode("sourceCode");
        storeSourceDO.setSourceUrl("sourceUrl");
        final List<StoreSourceDO> storeSourceDOS = Arrays.asList(storeSourceDO);
        when(mockStoreSourceMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(storeSourceDOS);

        // Configure StoreSourceMapper.querySourceByCode(...).
        final StoreSourceDO storeSourceDO1 = new StoreSourceDO();
        storeSourceDO1.setTerminalCode("terminalCode");
        storeSourceDO1.setModuleGuid("moduleGuid");
        storeSourceDO1.setModuleType("moduleType");
        storeSourceDO1.setPageTitle("pageTitle");
        storeSourceDO1.setPageUrl("pageUrl");
        storeSourceDO1.setSourceGuid("sourceGuid");
        storeSourceDO1.setSourceName("sourceName");
        storeSourceDO1.setSourceCode("sourceCode");
        storeSourceDO1.setSourceUrl("sourceUrl");
        when(mockStoreSourceMapper.querySourceByCode("organization_create")).thenReturn(storeSourceDO1);

        // Configure MenuMapStruct.storeSourceDOList2MenuSource(...).
        final StoreSourceDO storeSourceDO2 = new StoreSourceDO();
        storeSourceDO2.setTerminalCode("terminalCode");
        storeSourceDO2.setModuleGuid("moduleGuid");
        storeSourceDO2.setModuleType("moduleType");
        storeSourceDO2.setPageTitle("pageTitle");
        storeSourceDO2.setPageUrl("pageUrl");
        storeSourceDO2.setSourceGuid("sourceGuid");
        storeSourceDO2.setSourceName("sourceName");
        storeSourceDO2.setSourceCode("sourceCode");
        storeSourceDO2.setSourceUrl("sourceUrl");
        final List<StoreSourceDO> storeSourceDOList = Arrays.asList(storeSourceDO2);
        when(mockMenuMapStruct.storeSourceDOList2MenuSource(storeSourceDOList)).thenReturn(Collections.emptyList());

        // Run the test
        final List<MenuSourceDTO> result = menuServiceImplUnderTest.getSourceByMenu("menuGuid");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testGetModuleSourceByUser() {
        // Setup
        // Configure UserMapper.queryUserSourceOfAdmin(...).
        final MenuSourceDTO menuSourceDTO = new MenuSourceDTO();
        menuSourceDTO.setSourceGuid("");
        menuSourceDTO.setSourceName("sourceName");
        menuSourceDTO.setSourceCode("sourceCode");
        menuSourceDTO.setIsElevatedPrivileges(false);
        menuSourceDTO.setType(0);
        final List<MenuSourceDTO> menuSourceDTOS = Arrays.asList(menuSourceDTO);
        final UserSourceQuery userSourceQuery = new UserSourceQuery();
        userSourceQuery.setUserGuid("userGuid");
        userSourceQuery.setTerminalCode("terminalCode");
        userSourceQuery.setMenuGuid("menuGuid");
        userSourceQuery.setRequestUri("requestUri");
        userSourceQuery.setStoreGuid("storeGuid");
        when(mockUserMapper.queryUserSourceOfAdmin(userSourceQuery)).thenReturn(menuSourceDTOS);

        // Configure UserMapper.queryUserModuleSource(...).
        final MenuSourceDTO menuSourceDTO1 = new MenuSourceDTO();
        menuSourceDTO1.setSourceGuid("");
        menuSourceDTO1.setSourceName("sourceName");
        menuSourceDTO1.setSourceCode("sourceCode");
        menuSourceDTO1.setIsElevatedPrivileges(false);
        menuSourceDTO1.setType(0);
        final List<MenuSourceDTO> menuSourceDTOS1 = Arrays.asList(menuSourceDTO1);
        final UserSourceQuery userSourceQuery1 = new UserSourceQuery();
        userSourceQuery1.setUserGuid("userGuid");
        userSourceQuery1.setTerminalCode("terminalCode");
        userSourceQuery1.setMenuGuid("menuGuid");
        userSourceQuery1.setRequestUri("requestUri");
        userSourceQuery1.setStoreGuid("storeGuid");
        when(mockUserMapper.queryUserModuleSource(userSourceQuery1)).thenReturn(menuSourceDTOS1);

        // Run the test
        final List<MenuSourceDTO> result = menuServiceImplUnderTest.getModuleSourceByUser("terminalCode");

        // Verify the results
    }

    @Test
    public void testGetModuleSourceByUser_UserMapperQueryUserSourceOfAdminReturnsNoItems() {
        // Setup
        // Configure UserMapper.queryUserSourceOfAdmin(...).
        final UserSourceQuery userSourceQuery = new UserSourceQuery();
        userSourceQuery.setUserGuid("userGuid");
        userSourceQuery.setTerminalCode("terminalCode");
        userSourceQuery.setMenuGuid("menuGuid");
        userSourceQuery.setRequestUri("requestUri");
        userSourceQuery.setStoreGuid("storeGuid");
        when(mockUserMapper.queryUserSourceOfAdmin(userSourceQuery)).thenReturn(Collections.emptyList());

        // Run the test
        final List<MenuSourceDTO> result = menuServiceImplUnderTest.getModuleSourceByUser("terminalCode");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testGetModuleSourceByUser_UserMapperQueryUserModuleSourceReturnsNoItems() {
        // Setup
        // Configure UserMapper.queryUserModuleSource(...).
        final UserSourceQuery userSourceQuery = new UserSourceQuery();
        userSourceQuery.setUserGuid("userGuid");
        userSourceQuery.setTerminalCode("terminalCode");
        userSourceQuery.setMenuGuid("menuGuid");
        userSourceQuery.setRequestUri("requestUri");
        userSourceQuery.setStoreGuid("storeGuid");
        when(mockUserMapper.queryUserModuleSource(userSourceQuery)).thenReturn(Collections.emptyList());

        // Run the test
        final List<MenuSourceDTO> result = menuServiceImplUnderTest.getModuleSourceByUser("terminalCode");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }
}
