package com.holderzone.saas.store.staff.event;

import com.holderzone.framework.dds.starter.utils.DynamicInfoHelper;
import com.holderzone.resource.common.dto.mq.UnMessage;
import com.holderzone.resource.common.dto.product.ProductThemeSyncDTO;
import com.holderzone.saas.store.staff.service.ProductThemeService;
import com.holderzone.saas.store.staff.service.remote.EnterpriseClient;
import com.holderzone.saas.store.staff.service.remote.EnterpriseDataClient;
import com.holderzone.saas.store.staff.utils.DynamicHelper;
import org.apache.rocketmq.common.message.MessageExt;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.net.InetSocketAddress;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;

@RunWith(MockitoJUnitRunner.class)
public class SyncErpThemeListenerTest {

    @Mock
    private DynamicHelper mockDynamicHelper;
    @Mock
    private ProductThemeService mockProductThemeService;
    @Mock
    private EnterpriseClient mockEnterpriseClient;
    @Mock
    private EnterpriseDataClient mockEnterpriseDataClient;
    @Mock
    private DynamicInfoHelper mockDynamicInfoHelper;

    private SyncErpThemeListener syncErpThemeListenerUnderTest;

    @Before
    public void setUp() throws Exception {
        syncErpThemeListenerUnderTest = new SyncErpThemeListener(mockDynamicHelper, mockProductThemeService,
                mockEnterpriseClient, mockEnterpriseDataClient, mockDynamicInfoHelper);
    }

    @Test
    public void testConsumeMsg() {
        // Setup
        final UnMessage<String> unMessage = new UnMessage<>("messageType", "enterpriseGuid", "storeGuid", "message");
        final MessageExt messageExt = new MessageExt(0, 0L, new InetSocketAddress("localhost", 80), 0L,
                new InetSocketAddress("localhost", 80), "msgId");

        // Run the test
        final boolean result = syncErpThemeListenerUnderTest.consumeMsg(unMessage, messageExt);

        // Verify the results
        assertThat(result).isTrue();
        verify(mockProductThemeService).save(any(ProductThemeSyncDTO.class));
        verify(mockDynamicHelper).clear();
    }
}
