package com.holderzone.saas.store.staff;

import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
//@SpringBootTest
//@RunWith(SpringRunner.class)
public class OrgTest {
//
//    @Autowired
//    private WebApplicationContext wac;
//
//    private MockMvc mockMvc;
//
//    @Before
//    public void setup() {
//        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();
//    }

    //    @Test
    public static void main(String[] args) {
        List<OrgDO> orgDOList = new ArrayList<>();
        OrgDO orgDO1 = new OrgDO("111,11,1", "111,11,1");
        OrgDO orgDO2 = new OrgDO("112,11,1", "112,11,1");
        OrgDO orgDO3 = new OrgDO("121,12,1", "121,12,1");
        OrgDO orgDO4 = new OrgDO("122,12,1", "122,12,1");
        OrgDO orgDO5 = new OrgDO("211,21,2", "211,21,2");
        OrgDO orgDO6 = new OrgDO("212,21,2", "212,21,2");
        OrgDO orgDO7 = new OrgDO("221,22,2", "221,22,2");
        OrgDO orgDO8 = new OrgDO("222,22,2", "222,22,2");
        orgDOList.add(orgDO1);
        orgDOList.add(orgDO2);
        orgDOList.add(orgDO3);
        orgDOList.add(orgDO4);
        orgDOList.add(orgDO5);
        orgDOList.add(orgDO6);
        orgDOList.add(orgDO7);
        orgDOList.add(orgDO8);

        List<OrgDTO> arrayOfOrgDTO = new ArrayList<>();
        recursive(arrayOfOrgDTO, do2Bo(orgDOList));
        System.out.println(arrayOfOrgDTO);
    }

    private static List<OrgBO> do2Bo(List<OrgDO> arrayOfOrgDO) {
        return arrayOfOrgDO.stream()
                .map(orgDO -> {
                    String[] guids = orgDO.getGuid().split(",");
                    String[] names = orgDO.getName().split(",");
                    OrgBO root = new OrgBO();
                    OrgBO temp = root;
                    int depth = guids.length;
                    for (int i = 0; i < depth; i++) {
                        int j = depth - i - 1;
                        temp.setGuid(guids[j]);
                        temp.setName(names[j]);
                        if (i < depth - 1) {
                            OrgBO subOrgBO = new OrgBO();
                            temp.setSubOrgBO(subOrgBO);
                            temp = subOrgBO;
                        }
                    }
                    return root;
                })
                .collect(Collectors.toList());
    }

    private static void recursive(List<OrgDTO> arrayOfOrgDTO, List<OrgBO> arrayOfOrgBO) {
        Map<String, List<OrgBO>> mapOfArrayOfOrgBO = arrayOfOrgBO.stream()
                .collect(Collectors.groupingBy(
                        o -> o.getGuid() + "_" + o.getName(),
                        LinkedHashMap::new, Collectors.toList()
                ));
        mapOfArrayOfOrgBO.forEach((guidNamePair, arrayOfOrgBoGrouped) -> {
            List<OrgDTO> arrayOfSubOrgDTO = new ArrayList<>();
            String[] guidAndName = guidNamePair.split("_");
            OrgDTO orgDTO = new OrgDTO(guidAndName[0], guidAndName[1], arrayOfSubOrgDTO);
            arrayOfOrgDTO.add(orgDTO);
            List<OrgBO> arrayOfSubOrgBO = arrayOfOrgBoGrouped.stream()
                    .map(OrgBO::getSubOrgBO)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            recursive(arrayOfSubOrgDTO, arrayOfSubOrgBO);
            if (arrayOfSubOrgDTO.isEmpty()) {
                orgDTO.setArrayOfSubOrgDTO(null);
            }
        });
    }

    public static class OrgDO {

        private String guid;

        private String name;

        public OrgDO(String guid, String name) {
            this.guid = guid;
            this.name = name;
        }

        public String getGuid() {
            return guid;
        }

        public void setGuid(String guid) {
            this.guid = guid;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }


    public static class OrgBO {

        private String guid;

        private String name;

        private OrgBO subOrgBO;

        public OrgBO() {
        }

        public OrgBO(String guid, String name, OrgBO subOrgBO) {
            this.guid = guid;
            this.name = name;
            this.subOrgBO = subOrgBO;
        }

        public String getGuid() {
            return guid;
        }

        public void setGuid(String guid) {
            this.guid = guid;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public OrgBO getSubOrgBO() {
            return subOrgBO;
        }

        public void setSubOrgBO(OrgBO subOrgBO) {
            this.subOrgBO = subOrgBO;
        }

        @Override
        public String toString() {
            return guid + "_" + name;
        }
    }

    public static class OrgDTO {

        private String guid;

        private String name;

        private List<OrgDTO> arrayOfSubOrgDTO;

        public OrgDTO(String guid, String name, List<OrgDTO> arrayOfSubOrgDTO) {
            this.guid = guid;
            this.name = name;
            this.arrayOfSubOrgDTO = arrayOfSubOrgDTO;
        }

        public String getGuid() {
            return guid;
        }

        public void setGuid(String guid) {
            this.guid = guid;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public List<OrgDTO> getArrayOfSubOrgDTO() {
            return arrayOfSubOrgDTO;
        }

        public void setArrayOfSubOrgDTO(List<OrgDTO> arrayOfSubOrgDTO) {
            this.arrayOfSubOrgDTO = arrayOfSubOrgDTO;
        }

        @Override
        public String toString() {
            StringBuilder subToString = new StringBuilder();
            if (arrayOfSubOrgDTO != null) {
                subToString.append("[");
                for (int i = 0; i < arrayOfSubOrgDTO.size(); i++) {
                    OrgDTO orgDTO = arrayOfSubOrgDTO.get(i);
                    subToString.append(orgDTO.toString());
                    if (i < arrayOfSubOrgDTO.size() - 1) {
                        subToString.append(",");
                    }
                }
                subToString.append("]");
            }
            return guid + "_" + name + subToString.toString();
        }
    }
}
