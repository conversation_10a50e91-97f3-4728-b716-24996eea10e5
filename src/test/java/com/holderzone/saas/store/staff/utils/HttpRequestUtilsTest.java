package com.holderzone.saas.store.staff.utils;

import org.junit.Test;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

import static org.assertj.core.api.Assertions.assertThat;

public class HttpRequestUtilsTest {

    @Test
    public void testGetRequestUri() {
        assertThat(HttpRequestUtils.getRequestUri()).isEqualTo("result");
    }

    @Test
    public void testGetRequest() {
        // Setup
        // Run the test
        final MockHttpServletRequest result = (MockHttpServletRequest) HttpRequestUtils.getRequest();

        // Verify the results
    }

    @Test
    public void testGetResponse() {
        // Setup
        // Run the test
        final MockHttpServletResponse result = (MockHttpServletResponse) HttpRequestUtils.getResponse();

        // Verify the results
    }
}
