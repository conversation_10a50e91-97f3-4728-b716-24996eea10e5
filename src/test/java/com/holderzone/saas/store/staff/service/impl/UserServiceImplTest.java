package com.holderzone.saas.store.staff.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.framework.base.dto.message.MessageDTO;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.Page;
import com.holderzone.resource.common.dto.enterprise.MessageConfigDTO;
import com.holderzone.resource.common.dto.extension.BaseDictionaryDTO;
import com.holderzone.saas.store.dto.business.manage.HandoverRecordConfirmDTO;
import com.holderzone.saas.store.dto.business.manage.HandoverRecordCreateDTO;
import com.holderzone.saas.store.dto.business.manage.HandoverRecordDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.organization.OrgGeneralDTO;
import com.holderzone.saas.store.dto.organization.OrganizationDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceDTO;
import com.holderzone.saas.store.dto.user.*;
import com.holderzone.saas.store.dto.user.req.UserFaceInputReqDTO;
import com.holderzone.saas.store.dto.user.resp.UserBriefDTO;
import com.holderzone.saas.store.dto.user.resp.UserFaceDTO;
import com.holderzone.saas.store.staff.controller.AuthDTO;
import com.holderzone.saas.store.staff.entity.domain.*;
import com.holderzone.saas.store.staff.entity.query.ModuleTypeQuery;
import com.holderzone.saas.store.staff.entity.query.UserCondQuery;
import com.holderzone.saas.store.staff.entity.read.UserReadDO;
import com.holderzone.saas.store.staff.mapper.DataDicMapper;
import com.holderzone.saas.store.staff.mapper.MenuMapper;
import com.holderzone.saas.store.staff.mapper.RoleMapper;
import com.holderzone.saas.store.staff.mapper.StoreSourceMapper;
import com.holderzone.saas.store.staff.mapstruct.UserMapstruct;
import com.holderzone.saas.store.staff.service.*;
import com.holderzone.saas.store.staff.service.remote.*;
import com.holderzone.saas.store.staff.utils.DynamicHelper;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class UserServiceImplTest {

    @Mock
    private UserUploadService mockUserUploadService;
    @Mock
    private UserRoleService mockUserRoleService;
    @Mock
    private UserDataService mockUserDataService;
    @Mock
    private RoleMapper mockRoleMapper;
    @Mock
    private DataDicMapper mockDataDicMapper;
    @Mock
    private StoreSourceMapper mockStoreSourceMapper;
    @Mock
    private MenuMapper mockMenuMapper;
    @Mock
    private UserMapstruct mockUserMapstruct;
    @Mock
    private DistributedService mockDistributedService;
    @Mock
    private UserClient mockUserClient;
    @Mock
    private OrgFeignClient mockOrgFeignClient;
    @Mock
    private SmsClient mockSmsClient;
    @Mock
    private BusinessClient mockBusinessClient;
    @Mock
    private EnterpriseClient mockEnterpriseClient;
    @Mock
    private AuthClient mockAuthClient;
    @Mock
    private DynamicHelper mockDynamicHelper;
    @Mock
    private ProductService mockProductService;

    private UserServiceImpl userServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        userServiceImplUnderTest = new UserServiceImpl(mockUserUploadService, mockUserRoleService, mockUserDataService,
                mockRoleMapper, mockDataDicMapper, mockStoreSourceMapper, mockMenuMapper, mockUserMapstruct,
                mockDistributedService, mockUserClient, mockOrgFeignClient, mockSmsClient, mockBusinessClient,
                mockEnterpriseClient, mockAuthClient, mockDynamicHelper, mockProductService);
        ReflectionTestUtils.setField(userServiceImplUnderTest, "holderRequestHost", "holderRequestHost");
    }

    @Test
    public void testNewAccount() {
        assertThat(userServiceImplUnderTest.newAccount()).isEqualTo("result");
    }

    @Test
    public void testNewAuthCode() {
        assertThat(userServiceImplUnderTest.newAuthCode()).isEqualTo("result");
    }

    @Test
    public void testCreate() {
        // Setup
        final UserDTO userDTO = new UserDTO();
        userDTO.setGuid("userGuid");
        userDTO.setEnterpriseNo("enterpriseNo");
        userDTO.setAccount("account");
        userDTO.setPassword("password");
        userDTO.setName("name");
        userDTO.setPhone("phone");
        final RoleDTO roleDTO = new RoleDTO();
        userDTO.setUserRoles(Arrays.asList(roleDTO));
        userDTO.setOrgGuid("orgGuid");
        final UserOrgDTO userOrg = new UserOrgDTO();
        userOrg.setGuid("9d51a484-e3c4-4006-9e4d-e9a4b1c21c1f");
        userOrg.setName("name");
        userOrg.setOrgNameTreeJoined("orgNameTreeJoined");
        userDTO.setUserOrg(userOrg);
        final OrgGeneralDTO orgGeneralDTO = new OrgGeneralDTO();
        userDTO.setEntireOrgTree(Arrays.asList(orgGeneralDTO));
        final UserOfficeDTO userOffice = new UserOfficeDTO();
        userOffice.setCode(0);
        userOffice.setName("itemName");
        userDTO.setUserOffice(userOffice);
        userDTO.setIsEnable(false);
        userDTO.setPhoneList(Arrays.asList("value"));
        userDTO.setEnterpriseGuid("enterpriseGuid");
        userDTO.setIntegrateFlag(false);
        userDTO.setErrorMsg("当前账号正在门店使用，不能删除");

        when(mockUserClient.queryExistTel("phone", "")).thenReturn(0);

        // Configure UserMapstruct.fromUserDTO(...).
        final UserDO userDO = new UserDO();
        userDO.setId(0L);
        userDO.setGuid("userGuid");
        userDO.setEnterpriseNo("enterpriseNo");
        userDO.setAccount("account");
        userDO.setPassword("password");
        userDO.setAuthCode("authCode");
        userDO.setName("name");
        userDO.setPhone("phone");
        userDO.setOrgGuid("orgGuid");
        userDO.setOfficeCode(0);
        userDO.setOfficeName("itemName");
        userDO.setCreateStaffGuid("createStaffGuid");
        userDO.setUpdateStaffGuid("updateStaffGuid");
        userDO.setIsEnable(false);
        userDO.setIsDeleted(false);
        userDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        userDO.setRegType("regType");
        userDO.setFaceCode("faceCode");
        final UserDTO userDTO1 = new UserDTO();
        userDTO1.setGuid("userGuid");
        userDTO1.setEnterpriseNo("enterpriseNo");
        userDTO1.setAccount("account");
        userDTO1.setPassword("password");
        userDTO1.setName("name");
        userDTO1.setPhone("phone");
        final RoleDTO roleDTO1 = new RoleDTO();
        userDTO1.setUserRoles(Arrays.asList(roleDTO1));
        userDTO1.setOrgGuid("orgGuid");
        final UserOrgDTO userOrg1 = new UserOrgDTO();
        userOrg1.setGuid("9d51a484-e3c4-4006-9e4d-e9a4b1c21c1f");
        userOrg1.setName("name");
        userOrg1.setOrgNameTreeJoined("orgNameTreeJoined");
        userDTO1.setUserOrg(userOrg1);
        final OrgGeneralDTO orgGeneralDTO1 = new OrgGeneralDTO();
        userDTO1.setEntireOrgTree(Arrays.asList(orgGeneralDTO1));
        final UserOfficeDTO userOffice1 = new UserOfficeDTO();
        userOffice1.setCode(0);
        userOffice1.setName("itemName");
        userDTO1.setUserOffice(userOffice1);
        userDTO1.setIsEnable(false);
        userDTO1.setPhoneList(Arrays.asList("value"));
        userDTO1.setEnterpriseGuid("enterpriseGuid");
        userDTO1.setIntegrateFlag(false);
        userDTO1.setErrorMsg("当前账号正在门店使用，不能删除");
        when(mockUserMapstruct.fromUserDTO(userDTO1)).thenReturn(userDO);

        when(mockDistributedService.nextUserGuid()).thenReturn("userGuid");

        // Configure DataDicMapper.selectOne(...).
        final DataDicDO dataDicDO = new DataDicDO();
        dataDicDO.setGuid("5d4e9761-d5bf-479f-9085-efcde3d17e94");
        dataDicDO.setTypeCode("typeCode");
        dataDicDO.setTypeName("typeName");
        dataDicDO.setItemCode(0);
        dataDicDO.setItemName("itemName");
        when(mockDataDicMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(dataDicDO);

        when(mockDistributedService.nextOfficeGuid()).thenReturn("5d4e9761-d5bf-479f-9085-efcde3d17e94");

        // Run the test
        final String result = userServiceImplUnderTest.create(userDTO);

        // Verify the results
        assertThat(result).isEqualTo("userGuid");

        // Confirm DataDicMapper.insert(...).
        final DataDicDO entity = new DataDicDO();
        entity.setGuid("5d4e9761-d5bf-479f-9085-efcde3d17e94");
        entity.setTypeCode("typeCode");
        entity.setTypeName("typeName");
        entity.setItemCode(0);
        entity.setItemName("itemName");
        verify(mockDataDicMapper).insert(entity);

        // Confirm UserRoleService.addUserRoleRelation(...).
        final RoleDTO roleDTO2 = new RoleDTO();
        roleDTO2.setGuid("c2ee67b6-e602-4053-9e32-b563d41a16ea");
        roleDTO2.setName("name");
        roleDTO2.setIsEnable(false);
        roleDTO2.setIsDeleted(false);
        roleDTO2.setCreateStaffGuid("createStaffGuid");
        final List<RoleDTO> arrayOfRoleDTO = Arrays.asList(roleDTO2);
        verify(mockUserRoleService).addUserRoleRelation("userGuid", arrayOfRoleDTO);

        // Confirm UserUploadService.addUser(...).
        final UserDO userDO1 = new UserDO();
        userDO1.setId(0L);
        userDO1.setGuid("userGuid");
        userDO1.setEnterpriseNo("enterpriseNo");
        userDO1.setAccount("account");
        userDO1.setPassword("password");
        userDO1.setAuthCode("authCode");
        userDO1.setName("name");
        userDO1.setPhone("phone");
        userDO1.setOrgGuid("orgGuid");
        userDO1.setOfficeCode(0);
        userDO1.setOfficeName("itemName");
        userDO1.setCreateStaffGuid("createStaffGuid");
        userDO1.setUpdateStaffGuid("updateStaffGuid");
        userDO1.setIsEnable(false);
        userDO1.setIsDeleted(false);
        userDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        userDO1.setRegType("regType");
        userDO1.setFaceCode("faceCode");
        verify(mockUserUploadService).addUser(userDO1);
    }

    @Test
    public void testCreate_DataDicMapperSelectOneReturnsNull() {
        // Setup
        final UserDTO userDTO = new UserDTO();
        userDTO.setGuid("userGuid");
        userDTO.setEnterpriseNo("enterpriseNo");
        userDTO.setAccount("account");
        userDTO.setPassword("password");
        userDTO.setName("name");
        userDTO.setPhone("phone");
        final RoleDTO roleDTO = new RoleDTO();
        userDTO.setUserRoles(Arrays.asList(roleDTO));
        userDTO.setOrgGuid("orgGuid");
        final UserOrgDTO userOrg = new UserOrgDTO();
        userOrg.setGuid("9d51a484-e3c4-4006-9e4d-e9a4b1c21c1f");
        userOrg.setName("name");
        userOrg.setOrgNameTreeJoined("orgNameTreeJoined");
        userDTO.setUserOrg(userOrg);
        final OrgGeneralDTO orgGeneralDTO = new OrgGeneralDTO();
        userDTO.setEntireOrgTree(Arrays.asList(orgGeneralDTO));
        final UserOfficeDTO userOffice = new UserOfficeDTO();
        userOffice.setCode(0);
        userOffice.setName("itemName");
        userDTO.setUserOffice(userOffice);
        userDTO.setIsEnable(false);
        userDTO.setPhoneList(Arrays.asList("value"));
        userDTO.setEnterpriseGuid("enterpriseGuid");
        userDTO.setIntegrateFlag(false);
        userDTO.setErrorMsg("当前账号正在门店使用，不能删除");

        when(mockUserClient.queryExistTel("phone", "")).thenReturn(0);

        // Configure UserMapstruct.fromUserDTO(...).
        final UserDO userDO = new UserDO();
        userDO.setId(0L);
        userDO.setGuid("userGuid");
        userDO.setEnterpriseNo("enterpriseNo");
        userDO.setAccount("account");
        userDO.setPassword("password");
        userDO.setAuthCode("authCode");
        userDO.setName("name");
        userDO.setPhone("phone");
        userDO.setOrgGuid("orgGuid");
        userDO.setOfficeCode(0);
        userDO.setOfficeName("itemName");
        userDO.setCreateStaffGuid("createStaffGuid");
        userDO.setUpdateStaffGuid("updateStaffGuid");
        userDO.setIsEnable(false);
        userDO.setIsDeleted(false);
        userDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        userDO.setRegType("regType");
        userDO.setFaceCode("faceCode");
        final UserDTO userDTO1 = new UserDTO();
        userDTO1.setGuid("userGuid");
        userDTO1.setEnterpriseNo("enterpriseNo");
        userDTO1.setAccount("account");
        userDTO1.setPassword("password");
        userDTO1.setName("name");
        userDTO1.setPhone("phone");
        final RoleDTO roleDTO1 = new RoleDTO();
        userDTO1.setUserRoles(Arrays.asList(roleDTO1));
        userDTO1.setOrgGuid("orgGuid");
        final UserOrgDTO userOrg1 = new UserOrgDTO();
        userOrg1.setGuid("9d51a484-e3c4-4006-9e4d-e9a4b1c21c1f");
        userOrg1.setName("name");
        userOrg1.setOrgNameTreeJoined("orgNameTreeJoined");
        userDTO1.setUserOrg(userOrg1);
        final OrgGeneralDTO orgGeneralDTO1 = new OrgGeneralDTO();
        userDTO1.setEntireOrgTree(Arrays.asList(orgGeneralDTO1));
        final UserOfficeDTO userOffice1 = new UserOfficeDTO();
        userOffice1.setCode(0);
        userOffice1.setName("itemName");
        userDTO1.setUserOffice(userOffice1);
        userDTO1.setIsEnable(false);
        userDTO1.setPhoneList(Arrays.asList("value"));
        userDTO1.setEnterpriseGuid("enterpriseGuid");
        userDTO1.setIntegrateFlag(false);
        userDTO1.setErrorMsg("当前账号正在门店使用，不能删除");
        when(mockUserMapstruct.fromUserDTO(userDTO1)).thenReturn(userDO);

        when(mockDistributedService.nextUserGuid()).thenReturn("userGuid");
        when(mockDataDicMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);
        when(mockDistributedService.nextOfficeGuid()).thenReturn("5d4e9761-d5bf-479f-9085-efcde3d17e94");

        // Run the test
        final String result = userServiceImplUnderTest.create(userDTO);

        // Verify the results
        assertThat(result).isEqualTo("userGuid");

        // Confirm DataDicMapper.insert(...).
        final DataDicDO entity = new DataDicDO();
        entity.setGuid("5d4e9761-d5bf-479f-9085-efcde3d17e94");
        entity.setTypeCode("typeCode");
        entity.setTypeName("typeName");
        entity.setItemCode(0);
        entity.setItemName("itemName");
        verify(mockDataDicMapper).insert(entity);

        // Confirm UserRoleService.addUserRoleRelation(...).
        final RoleDTO roleDTO2 = new RoleDTO();
        roleDTO2.setGuid("c2ee67b6-e602-4053-9e32-b563d41a16ea");
        roleDTO2.setName("name");
        roleDTO2.setIsEnable(false);
        roleDTO2.setIsDeleted(false);
        roleDTO2.setCreateStaffGuid("createStaffGuid");
        final List<RoleDTO> arrayOfRoleDTO = Arrays.asList(roleDTO2);
        verify(mockUserRoleService).addUserRoleRelation("userGuid", arrayOfRoleDTO);

        // Confirm UserUploadService.addUser(...).
        final UserDO userDO1 = new UserDO();
        userDO1.setId(0L);
        userDO1.setGuid("userGuid");
        userDO1.setEnterpriseNo("enterpriseNo");
        userDO1.setAccount("account");
        userDO1.setPassword("password");
        userDO1.setAuthCode("authCode");
        userDO1.setName("name");
        userDO1.setPhone("phone");
        userDO1.setOrgGuid("orgGuid");
        userDO1.setOfficeCode(0);
        userDO1.setOfficeName("itemName");
        userDO1.setCreateStaffGuid("createStaffGuid");
        userDO1.setUpdateStaffGuid("updateStaffGuid");
        userDO1.setIsEnable(false);
        userDO1.setIsDeleted(false);
        userDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        userDO1.setRegType("regType");
        userDO1.setFaceCode("faceCode");
        verify(mockUserUploadService).addUser(userDO1);
    }

    @Test
    public void testSyncHolderUser() {
        // Setup
        // Configure UserMapstruct.holderUserDTOS2UserDTOS(...).
        final UserDTO userDTO = new UserDTO();
        userDTO.setGuid("userGuid");
        userDTO.setEnterpriseNo("enterpriseNo");
        userDTO.setAccount("account");
        userDTO.setPassword("password");
        userDTO.setName("name");
        userDTO.setPhone("phone");
        final RoleDTO roleDTO = new RoleDTO();
        userDTO.setUserRoles(Arrays.asList(roleDTO));
        userDTO.setOrgGuid("orgGuid");
        final UserOrgDTO userOrg = new UserOrgDTO();
        userOrg.setGuid("9d51a484-e3c4-4006-9e4d-e9a4b1c21c1f");
        userOrg.setName("name");
        userOrg.setOrgNameTreeJoined("orgNameTreeJoined");
        userDTO.setUserOrg(userOrg);
        final OrgGeneralDTO orgGeneralDTO = new OrgGeneralDTO();
        userDTO.setEntireOrgTree(Arrays.asList(orgGeneralDTO));
        final UserOfficeDTO userOffice = new UserOfficeDTO();
        userOffice.setCode(0);
        userOffice.setName("itemName");
        userDTO.setUserOffice(userOffice);
        userDTO.setIsEnable(false);
        userDTO.setPhoneList(Arrays.asList("value"));
        userDTO.setEnterpriseGuid("enterpriseGuid");
        userDTO.setIntegrateFlag(false);
        userDTO.setErrorMsg("当前账号正在门店使用，不能删除");
        final List<UserDTO> userDTOS = Arrays.asList(userDTO);
        final HolderUserDTO holderUserDTO = new HolderUserDTO();
        holderUserDTO.setUserId(0L);
        holderUserDTO.setUsername("username");
        holderUserDTO.setPassword("password");
        holderUserDTO.setAccount("account");
        holderUserDTO.setDeptId(0L);
        final List<HolderUserDTO> holderUserDTOS = Arrays.asList(holderUserDTO);
        when(mockUserMapstruct.holderUserDTOS2UserDTOS(holderUserDTOS)).thenReturn(userDTOS);

        // Configure UserClient.getAdminByEnterpriseGuid(...).
        final com.holderzone.resource.common.dto.user.UserDTO userDTO1 = new com.holderzone.resource.common.dto.user.UserDTO();
        userDTO1.setPhoneList(Arrays.asList("value"));
        userDTO1.setUserGuid("userGuid");
        userDTO1.setEnterpriseGuid("enterpriseGuid");
        userDTO1.setCreateStaffGuid("createStaffGuid");
        userDTO1.setPassword("password");
        when(mockUserClient.getAdminByEnterpriseGuid("enterpriseGuid")).thenReturn(userDTO1);

        // Configure UserMapstruct.fromUserDTO(...).
        final UserDO userDO = new UserDO();
        userDO.setId(0L);
        userDO.setGuid("userGuid");
        userDO.setEnterpriseNo("enterpriseNo");
        userDO.setAccount("account");
        userDO.setPassword("password");
        userDO.setAuthCode("authCode");
        userDO.setName("name");
        userDO.setPhone("phone");
        userDO.setOrgGuid("orgGuid");
        userDO.setOfficeCode(0);
        userDO.setOfficeName("itemName");
        userDO.setCreateStaffGuid("createStaffGuid");
        userDO.setUpdateStaffGuid("updateStaffGuid");
        userDO.setIsEnable(false);
        userDO.setIsDeleted(false);
        userDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        userDO.setRegType("regType");
        userDO.setFaceCode("faceCode");
        final UserDTO userDTO2 = new UserDTO();
        userDTO2.setGuid("userGuid");
        userDTO2.setEnterpriseNo("enterpriseNo");
        userDTO2.setAccount("account");
        userDTO2.setPassword("password");
        userDTO2.setName("name");
        userDTO2.setPhone("phone");
        final RoleDTO roleDTO1 = new RoleDTO();
        userDTO2.setUserRoles(Arrays.asList(roleDTO1));
        userDTO2.setOrgGuid("orgGuid");
        final UserOrgDTO userOrg1 = new UserOrgDTO();
        userOrg1.setGuid("9d51a484-e3c4-4006-9e4d-e9a4b1c21c1f");
        userOrg1.setName("name");
        userOrg1.setOrgNameTreeJoined("orgNameTreeJoined");
        userDTO2.setUserOrg(userOrg1);
        final OrgGeneralDTO orgGeneralDTO1 = new OrgGeneralDTO();
        userDTO2.setEntireOrgTree(Arrays.asList(orgGeneralDTO1));
        final UserOfficeDTO userOffice1 = new UserOfficeDTO();
        userOffice1.setCode(0);
        userOffice1.setName("itemName");
        userDTO2.setUserOffice(userOffice1);
        userDTO2.setIsEnable(false);
        userDTO2.setPhoneList(Arrays.asList("value"));
        userDTO2.setEnterpriseGuid("enterpriseGuid");
        userDTO2.setIntegrateFlag(false);
        userDTO2.setErrorMsg("当前账号正在门店使用，不能删除");
        when(mockUserMapstruct.fromUserDTO(userDTO2)).thenReturn(userDO);

        // Configure BusinessClient.queryByUserGuid(...).
        final HandoverRecordDTO handoverRecordDTO = new HandoverRecordDTO();
        handoverRecordDTO.setStoreGuid("storeGuid");
        handoverRecordDTO.setStoreName("storeName");
        handoverRecordDTO.setTerminalId("terminalId");
        handoverRecordDTO.setCreateGuid("createGuid");
        handoverRecordDTO.setStatus(0);
        when(mockBusinessClient.queryByUserGuid(
                new HandoverRecordConfirmDTO("terminalId", new BigDecimal("0.00"), Arrays.asList("value"),
                        0))).thenReturn(handoverRecordDTO);

        // Run the test
        userServiceImplUnderTest.syncHolderUser(Arrays.asList("value"));

        // Verify the results
        // Confirm UserUploadService.addUser(...).
        final UserDO userDO1 = new UserDO();
        userDO1.setId(0L);
        userDO1.setGuid("userGuid");
        userDO1.setEnterpriseNo("enterpriseNo");
        userDO1.setAccount("account");
        userDO1.setPassword("password");
        userDO1.setAuthCode("authCode");
        userDO1.setName("name");
        userDO1.setPhone("phone");
        userDO1.setOrgGuid("orgGuid");
        userDO1.setOfficeCode(0);
        userDO1.setOfficeName("itemName");
        userDO1.setCreateStaffGuid("createStaffGuid");
        userDO1.setUpdateStaffGuid("updateStaffGuid");
        userDO1.setIsEnable(false);
        userDO1.setIsDeleted(false);
        userDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        userDO1.setRegType("regType");
        userDO1.setFaceCode("faceCode");
        verify(mockUserUploadService).addUser(userDO1);

        // Confirm UserUploadService.updateUser(...).
        final UserDO userDO2 = new UserDO();
        userDO2.setId(0L);
        userDO2.setGuid("userGuid");
        userDO2.setEnterpriseNo("enterpriseNo");
        userDO2.setAccount("account");
        userDO2.setPassword("password");
        userDO2.setAuthCode("authCode");
        userDO2.setName("name");
        userDO2.setPhone("phone");
        userDO2.setOrgGuid("orgGuid");
        userDO2.setOfficeCode(0);
        userDO2.setOfficeName("itemName");
        userDO2.setCreateStaffGuid("createStaffGuid");
        userDO2.setUpdateStaffGuid("updateStaffGuid");
        userDO2.setIsEnable(false);
        userDO2.setIsDeleted(false);
        userDO2.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        userDO2.setRegType("regType");
        userDO2.setFaceCode("faceCode");
        verify(mockUserUploadService).updateUser(userDO2);
    }

    @Test
    public void testSyncHolderUser_UserMapstructHolderUserDTOS2UserDTOSReturnsNoItems() {
        // Setup
        // Configure UserMapstruct.holderUserDTOS2UserDTOS(...).
        final HolderUserDTO holderUserDTO = new HolderUserDTO();
        holderUserDTO.setUserId(0L);
        holderUserDTO.setUsername("username");
        holderUserDTO.setPassword("password");
        holderUserDTO.setAccount("account");
        holderUserDTO.setDeptId(0L);
        final List<HolderUserDTO> holderUserDTOS = Arrays.asList(holderUserDTO);
        when(mockUserMapstruct.holderUserDTOS2UserDTOS(holderUserDTOS)).thenReturn(Collections.emptyList());

        // Configure UserClient.getAdminByEnterpriseGuid(...).
        final com.holderzone.resource.common.dto.user.UserDTO userDTO = new com.holderzone.resource.common.dto.user.UserDTO();
        userDTO.setPhoneList(Arrays.asList("value"));
        userDTO.setUserGuid("userGuid");
        userDTO.setEnterpriseGuid("enterpriseGuid");
        userDTO.setCreateStaffGuid("createStaffGuid");
        userDTO.setPassword("password");
        when(mockUserClient.getAdminByEnterpriseGuid("enterpriseGuid")).thenReturn(userDTO);

        // Configure UserMapstruct.fromUserDTO(...).
        final UserDO userDO = new UserDO();
        userDO.setId(0L);
        userDO.setGuid("userGuid");
        userDO.setEnterpriseNo("enterpriseNo");
        userDO.setAccount("account");
        userDO.setPassword("password");
        userDO.setAuthCode("authCode");
        userDO.setName("name");
        userDO.setPhone("phone");
        userDO.setOrgGuid("orgGuid");
        userDO.setOfficeCode(0);
        userDO.setOfficeName("itemName");
        userDO.setCreateStaffGuid("createStaffGuid");
        userDO.setUpdateStaffGuid("updateStaffGuid");
        userDO.setIsEnable(false);
        userDO.setIsDeleted(false);
        userDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        userDO.setRegType("regType");
        userDO.setFaceCode("faceCode");
        final UserDTO userDTO1 = new UserDTO();
        userDTO1.setGuid("userGuid");
        userDTO1.setEnterpriseNo("enterpriseNo");
        userDTO1.setAccount("account");
        userDTO1.setPassword("password");
        userDTO1.setName("name");
        userDTO1.setPhone("phone");
        final RoleDTO roleDTO = new RoleDTO();
        userDTO1.setUserRoles(Arrays.asList(roleDTO));
        userDTO1.setOrgGuid("orgGuid");
        final UserOrgDTO userOrg = new UserOrgDTO();
        userOrg.setGuid("9d51a484-e3c4-4006-9e4d-e9a4b1c21c1f");
        userOrg.setName("name");
        userOrg.setOrgNameTreeJoined("orgNameTreeJoined");
        userDTO1.setUserOrg(userOrg);
        final OrgGeneralDTO orgGeneralDTO = new OrgGeneralDTO();
        userDTO1.setEntireOrgTree(Arrays.asList(orgGeneralDTO));
        final UserOfficeDTO userOffice = new UserOfficeDTO();
        userOffice.setCode(0);
        userOffice.setName("itemName");
        userDTO1.setUserOffice(userOffice);
        userDTO1.setIsEnable(false);
        userDTO1.setPhoneList(Arrays.asList("value"));
        userDTO1.setEnterpriseGuid("enterpriseGuid");
        userDTO1.setIntegrateFlag(false);
        userDTO1.setErrorMsg("当前账号正在门店使用，不能删除");
        when(mockUserMapstruct.fromUserDTO(userDTO1)).thenReturn(userDO);

        // Configure BusinessClient.queryByUserGuid(...).
        final HandoverRecordDTO handoverRecordDTO = new HandoverRecordDTO();
        handoverRecordDTO.setStoreGuid("storeGuid");
        handoverRecordDTO.setStoreName("storeName");
        handoverRecordDTO.setTerminalId("terminalId");
        handoverRecordDTO.setCreateGuid("createGuid");
        handoverRecordDTO.setStatus(0);
        when(mockBusinessClient.queryByUserGuid(
                new HandoverRecordConfirmDTO("terminalId", new BigDecimal("0.00"), Arrays.asList("value"),
                        0))).thenReturn(handoverRecordDTO);

        // Run the test
        userServiceImplUnderTest.syncHolderUser(Arrays.asList("value"));

        // Verify the results
        // Confirm UserUploadService.addUser(...).
        final UserDO userDO1 = new UserDO();
        userDO1.setId(0L);
        userDO1.setGuid("userGuid");
        userDO1.setEnterpriseNo("enterpriseNo");
        userDO1.setAccount("account");
        userDO1.setPassword("password");
        userDO1.setAuthCode("authCode");
        userDO1.setName("name");
        userDO1.setPhone("phone");
        userDO1.setOrgGuid("orgGuid");
        userDO1.setOfficeCode(0);
        userDO1.setOfficeName("itemName");
        userDO1.setCreateStaffGuid("createStaffGuid");
        userDO1.setUpdateStaffGuid("updateStaffGuid");
        userDO1.setIsEnable(false);
        userDO1.setIsDeleted(false);
        userDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        userDO1.setRegType("regType");
        userDO1.setFaceCode("faceCode");
        verify(mockUserUploadService).addUser(userDO1);

        // Confirm UserUploadService.updateUser(...).
        final UserDO userDO2 = new UserDO();
        userDO2.setId(0L);
        userDO2.setGuid("userGuid");
        userDO2.setEnterpriseNo("enterpriseNo");
        userDO2.setAccount("account");
        userDO2.setPassword("password");
        userDO2.setAuthCode("authCode");
        userDO2.setName("name");
        userDO2.setPhone("phone");
        userDO2.setOrgGuid("orgGuid");
        userDO2.setOfficeCode(0);
        userDO2.setOfficeName("itemName");
        userDO2.setCreateStaffGuid("createStaffGuid");
        userDO2.setUpdateStaffGuid("updateStaffGuid");
        userDO2.setIsEnable(false);
        userDO2.setIsDeleted(false);
        userDO2.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        userDO2.setRegType("regType");
        userDO2.setFaceCode("faceCode");
        verify(mockUserUploadService).updateUser(userDO2);
    }

    @Test
    public void testSyncHolderUser_BusinessClientReturnsNull() {
        // Setup
        // Configure UserMapstruct.holderUserDTOS2UserDTOS(...).
        final UserDTO userDTO = new UserDTO();
        userDTO.setGuid("userGuid");
        userDTO.setEnterpriseNo("enterpriseNo");
        userDTO.setAccount("account");
        userDTO.setPassword("password");
        userDTO.setName("name");
        userDTO.setPhone("phone");
        final RoleDTO roleDTO = new RoleDTO();
        userDTO.setUserRoles(Arrays.asList(roleDTO));
        userDTO.setOrgGuid("orgGuid");
        final UserOrgDTO userOrg = new UserOrgDTO();
        userOrg.setGuid("9d51a484-e3c4-4006-9e4d-e9a4b1c21c1f");
        userOrg.setName("name");
        userOrg.setOrgNameTreeJoined("orgNameTreeJoined");
        userDTO.setUserOrg(userOrg);
        final OrgGeneralDTO orgGeneralDTO = new OrgGeneralDTO();
        userDTO.setEntireOrgTree(Arrays.asList(orgGeneralDTO));
        final UserOfficeDTO userOffice = new UserOfficeDTO();
        userOffice.setCode(0);
        userOffice.setName("itemName");
        userDTO.setUserOffice(userOffice);
        userDTO.setIsEnable(false);
        userDTO.setPhoneList(Arrays.asList("value"));
        userDTO.setEnterpriseGuid("enterpriseGuid");
        userDTO.setIntegrateFlag(false);
        userDTO.setErrorMsg("当前账号正在门店使用，不能删除");
        final List<UserDTO> userDTOS = Arrays.asList(userDTO);
        final HolderUserDTO holderUserDTO = new HolderUserDTO();
        holderUserDTO.setUserId(0L);
        holderUserDTO.setUsername("username");
        holderUserDTO.setPassword("password");
        holderUserDTO.setAccount("account");
        holderUserDTO.setDeptId(0L);
        final List<HolderUserDTO> holderUserDTOS = Arrays.asList(holderUserDTO);
        when(mockUserMapstruct.holderUserDTOS2UserDTOS(holderUserDTOS)).thenReturn(userDTOS);

        // Configure UserClient.getAdminByEnterpriseGuid(...).
        final com.holderzone.resource.common.dto.user.UserDTO userDTO1 = new com.holderzone.resource.common.dto.user.UserDTO();
        userDTO1.setPhoneList(Arrays.asList("value"));
        userDTO1.setUserGuid("userGuid");
        userDTO1.setEnterpriseGuid("enterpriseGuid");
        userDTO1.setCreateStaffGuid("createStaffGuid");
        userDTO1.setPassword("password");
        when(mockUserClient.getAdminByEnterpriseGuid("enterpriseGuid")).thenReturn(userDTO1);

        // Configure UserMapstruct.fromUserDTO(...).
        final UserDO userDO = new UserDO();
        userDO.setId(0L);
        userDO.setGuid("userGuid");
        userDO.setEnterpriseNo("enterpriseNo");
        userDO.setAccount("account");
        userDO.setPassword("password");
        userDO.setAuthCode("authCode");
        userDO.setName("name");
        userDO.setPhone("phone");
        userDO.setOrgGuid("orgGuid");
        userDO.setOfficeCode(0);
        userDO.setOfficeName("itemName");
        userDO.setCreateStaffGuid("createStaffGuid");
        userDO.setUpdateStaffGuid("updateStaffGuid");
        userDO.setIsEnable(false);
        userDO.setIsDeleted(false);
        userDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        userDO.setRegType("regType");
        userDO.setFaceCode("faceCode");
        final UserDTO userDTO2 = new UserDTO();
        userDTO2.setGuid("userGuid");
        userDTO2.setEnterpriseNo("enterpriseNo");
        userDTO2.setAccount("account");
        userDTO2.setPassword("password");
        userDTO2.setName("name");
        userDTO2.setPhone("phone");
        final RoleDTO roleDTO1 = new RoleDTO();
        userDTO2.setUserRoles(Arrays.asList(roleDTO1));
        userDTO2.setOrgGuid("orgGuid");
        final UserOrgDTO userOrg1 = new UserOrgDTO();
        userOrg1.setGuid("9d51a484-e3c4-4006-9e4d-e9a4b1c21c1f");
        userOrg1.setName("name");
        userOrg1.setOrgNameTreeJoined("orgNameTreeJoined");
        userDTO2.setUserOrg(userOrg1);
        final OrgGeneralDTO orgGeneralDTO1 = new OrgGeneralDTO();
        userDTO2.setEntireOrgTree(Arrays.asList(orgGeneralDTO1));
        final UserOfficeDTO userOffice1 = new UserOfficeDTO();
        userOffice1.setCode(0);
        userOffice1.setName("itemName");
        userDTO2.setUserOffice(userOffice1);
        userDTO2.setIsEnable(false);
        userDTO2.setPhoneList(Arrays.asList("value"));
        userDTO2.setEnterpriseGuid("enterpriseGuid");
        userDTO2.setIntegrateFlag(false);
        userDTO2.setErrorMsg("当前账号正在门店使用，不能删除");
        when(mockUserMapstruct.fromUserDTO(userDTO2)).thenReturn(userDO);

        when(mockBusinessClient.queryByUserGuid(
                new HandoverRecordConfirmDTO("terminalId", new BigDecimal("0.00"), Arrays.asList("value"),
                        0))).thenReturn(null);

        // Run the test
        userServiceImplUnderTest.syncHolderUser(Arrays.asList("value"));

        // Verify the results
        // Confirm UserUploadService.addUser(...).
        final UserDO userDO1 = new UserDO();
        userDO1.setId(0L);
        userDO1.setGuid("userGuid");
        userDO1.setEnterpriseNo("enterpriseNo");
        userDO1.setAccount("account");
        userDO1.setPassword("password");
        userDO1.setAuthCode("authCode");
        userDO1.setName("name");
        userDO1.setPhone("phone");
        userDO1.setOrgGuid("orgGuid");
        userDO1.setOfficeCode(0);
        userDO1.setOfficeName("itemName");
        userDO1.setCreateStaffGuid("createStaffGuid");
        userDO1.setUpdateStaffGuid("updateStaffGuid");
        userDO1.setIsEnable(false);
        userDO1.setIsDeleted(false);
        userDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        userDO1.setRegType("regType");
        userDO1.setFaceCode("faceCode");
        verify(mockUserUploadService).addUser(userDO1);

        // Confirm UserUploadService.updateUser(...).
        final UserDO userDO2 = new UserDO();
        userDO2.setId(0L);
        userDO2.setGuid("userGuid");
        userDO2.setEnterpriseNo("enterpriseNo");
        userDO2.setAccount("account");
        userDO2.setPassword("password");
        userDO2.setAuthCode("authCode");
        userDO2.setName("name");
        userDO2.setPhone("phone");
        userDO2.setOrgGuid("orgGuid");
        userDO2.setOfficeCode(0);
        userDO2.setOfficeName("itemName");
        userDO2.setCreateStaffGuid("createStaffGuid");
        userDO2.setUpdateStaffGuid("updateStaffGuid");
        userDO2.setIsEnable(false);
        userDO2.setIsDeleted(false);
        userDO2.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        userDO2.setRegType("regType");
        userDO2.setFaceCode("faceCode");
        verify(mockUserUploadService).updateUser(userDO2);
        verify(mockUserRoleService).deleteUserRoleRelation("userGuid");
        verify(mockUserDataService).deleteUserDataRules("userGuid");
        verify(mockUserUploadService).deleteUser("userGuid");
    }

    @Test
    public void testCreateFromCloud() {
        // Setup
        final UserDTO userDTO = new UserDTO();
        userDTO.setGuid("userGuid");
        userDTO.setEnterpriseNo("enterpriseNo");
        userDTO.setAccount("account");
        userDTO.setPassword("password");
        userDTO.setName("name");
        userDTO.setPhone("phone");
        final RoleDTO roleDTO = new RoleDTO();
        userDTO.setUserRoles(Arrays.asList(roleDTO));
        userDTO.setOrgGuid("orgGuid");
        final UserOrgDTO userOrg = new UserOrgDTO();
        userOrg.setGuid("9d51a484-e3c4-4006-9e4d-e9a4b1c21c1f");
        userOrg.setName("name");
        userOrg.setOrgNameTreeJoined("orgNameTreeJoined");
        userDTO.setUserOrg(userOrg);
        final OrgGeneralDTO orgGeneralDTO = new OrgGeneralDTO();
        userDTO.setEntireOrgTree(Arrays.asList(orgGeneralDTO));
        final UserOfficeDTO userOffice = new UserOfficeDTO();
        userOffice.setCode(0);
        userOffice.setName("itemName");
        userDTO.setUserOffice(userOffice);
        userDTO.setIsEnable(false);
        userDTO.setPhoneList(Arrays.asList("value"));
        userDTO.setEnterpriseGuid("enterpriseGuid");
        userDTO.setIntegrateFlag(false);
        userDTO.setErrorMsg("当前账号正在门店使用，不能删除");

        // Configure UserMapstruct.fromUserDTO(...).
        final UserDO userDO = new UserDO();
        userDO.setId(0L);
        userDO.setGuid("userGuid");
        userDO.setEnterpriseNo("enterpriseNo");
        userDO.setAccount("account");
        userDO.setPassword("password");
        userDO.setAuthCode("authCode");
        userDO.setName("name");
        userDO.setPhone("phone");
        userDO.setOrgGuid("orgGuid");
        userDO.setOfficeCode(0);
        userDO.setOfficeName("itemName");
        userDO.setCreateStaffGuid("createStaffGuid");
        userDO.setUpdateStaffGuid("updateStaffGuid");
        userDO.setIsEnable(false);
        userDO.setIsDeleted(false);
        userDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        userDO.setRegType("regType");
        userDO.setFaceCode("faceCode");
        final UserDTO userDTO1 = new UserDTO();
        userDTO1.setGuid("userGuid");
        userDTO1.setEnterpriseNo("enterpriseNo");
        userDTO1.setAccount("account");
        userDTO1.setPassword("password");
        userDTO1.setName("name");
        userDTO1.setPhone("phone");
        final RoleDTO roleDTO1 = new RoleDTO();
        userDTO1.setUserRoles(Arrays.asList(roleDTO1));
        userDTO1.setOrgGuid("orgGuid");
        final UserOrgDTO userOrg1 = new UserOrgDTO();
        userOrg1.setGuid("9d51a484-e3c4-4006-9e4d-e9a4b1c21c1f");
        userOrg1.setName("name");
        userOrg1.setOrgNameTreeJoined("orgNameTreeJoined");
        userDTO1.setUserOrg(userOrg1);
        final OrgGeneralDTO orgGeneralDTO1 = new OrgGeneralDTO();
        userDTO1.setEntireOrgTree(Arrays.asList(orgGeneralDTO1));
        final UserOfficeDTO userOffice1 = new UserOfficeDTO();
        userOffice1.setCode(0);
        userOffice1.setName("itemName");
        userDTO1.setUserOffice(userOffice1);
        userDTO1.setIsEnable(false);
        userDTO1.setPhoneList(Arrays.asList("value"));
        userDTO1.setEnterpriseGuid("enterpriseGuid");
        userDTO1.setIntegrateFlag(false);
        userDTO1.setErrorMsg("当前账号正在门店使用，不能删除");
        when(mockUserMapstruct.fromUserDTO(userDTO1)).thenReturn(userDO);

        // Run the test
        userServiceImplUnderTest.createFromCloud(userDTO);

        // Verify the results
    }

    @Test
    public void testUpdateFromCloud() {
        // Setup
        final UserDTO userDTO = new UserDTO();
        userDTO.setGuid("userGuid");
        userDTO.setEnterpriseNo("enterpriseNo");
        userDTO.setAccount("account");
        userDTO.setPassword("password");
        userDTO.setName("name");
        userDTO.setPhone("phone");
        final RoleDTO roleDTO = new RoleDTO();
        userDTO.setUserRoles(Arrays.asList(roleDTO));
        userDTO.setOrgGuid("orgGuid");
        final UserOrgDTO userOrg = new UserOrgDTO();
        userOrg.setGuid("9d51a484-e3c4-4006-9e4d-e9a4b1c21c1f");
        userOrg.setName("name");
        userOrg.setOrgNameTreeJoined("orgNameTreeJoined");
        userDTO.setUserOrg(userOrg);
        final OrgGeneralDTO orgGeneralDTO = new OrgGeneralDTO();
        userDTO.setEntireOrgTree(Arrays.asList(orgGeneralDTO));
        final UserOfficeDTO userOffice = new UserOfficeDTO();
        userOffice.setCode(0);
        userOffice.setName("itemName");
        userDTO.setUserOffice(userOffice);
        userDTO.setIsEnable(false);
        userDTO.setPhoneList(Arrays.asList("value"));
        userDTO.setEnterpriseGuid("enterpriseGuid");
        userDTO.setIntegrateFlag(false);
        userDTO.setErrorMsg("当前账号正在门店使用，不能删除");

        // Configure UserMapstruct.fromUserDTO(...).
        final UserDO userDO = new UserDO();
        userDO.setId(0L);
        userDO.setGuid("userGuid");
        userDO.setEnterpriseNo("enterpriseNo");
        userDO.setAccount("account");
        userDO.setPassword("password");
        userDO.setAuthCode("authCode");
        userDO.setName("name");
        userDO.setPhone("phone");
        userDO.setOrgGuid("orgGuid");
        userDO.setOfficeCode(0);
        userDO.setOfficeName("itemName");
        userDO.setCreateStaffGuid("createStaffGuid");
        userDO.setUpdateStaffGuid("updateStaffGuid");
        userDO.setIsEnable(false);
        userDO.setIsDeleted(false);
        userDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        userDO.setRegType("regType");
        userDO.setFaceCode("faceCode");
        final UserDTO userDTO1 = new UserDTO();
        userDTO1.setGuid("userGuid");
        userDTO1.setEnterpriseNo("enterpriseNo");
        userDTO1.setAccount("account");
        userDTO1.setPassword("password");
        userDTO1.setName("name");
        userDTO1.setPhone("phone");
        final RoleDTO roleDTO1 = new RoleDTO();
        userDTO1.setUserRoles(Arrays.asList(roleDTO1));
        userDTO1.setOrgGuid("orgGuid");
        final UserOrgDTO userOrg1 = new UserOrgDTO();
        userOrg1.setGuid("9d51a484-e3c4-4006-9e4d-e9a4b1c21c1f");
        userOrg1.setName("name");
        userOrg1.setOrgNameTreeJoined("orgNameTreeJoined");
        userDTO1.setUserOrg(userOrg1);
        final OrgGeneralDTO orgGeneralDTO1 = new OrgGeneralDTO();
        userDTO1.setEntireOrgTree(Arrays.asList(orgGeneralDTO1));
        final UserOfficeDTO userOffice1 = new UserOfficeDTO();
        userOffice1.setCode(0);
        userOffice1.setName("itemName");
        userDTO1.setUserOffice(userOffice1);
        userDTO1.setIsEnable(false);
        userDTO1.setPhoneList(Arrays.asList("value"));
        userDTO1.setEnterpriseGuid("enterpriseGuid");
        userDTO1.setIntegrateFlag(false);
        userDTO1.setErrorMsg("当前账号正在门店使用，不能删除");
        when(mockUserMapstruct.fromUserDTO(userDTO1)).thenReturn(userDO);

        // Run the test
        userServiceImplUnderTest.updateFromCloud(userDTO);

        // Verify the results
    }

    @Test
    public void testCreateBatchFromCloud() {
        // Setup
        final UserDO userDO = new UserDO();
        userDO.setId(0L);
        userDO.setGuid("userGuid");
        userDO.setEnterpriseNo("enterpriseNo");
        userDO.setAccount("account");
        userDO.setPassword("password");
        userDO.setAuthCode("authCode");
        userDO.setName("name");
        userDO.setPhone("phone");
        userDO.setOrgGuid("orgGuid");
        userDO.setOfficeCode(0);
        userDO.setOfficeName("itemName");
        userDO.setCreateStaffGuid("createStaffGuid");
        userDO.setUpdateStaffGuid("updateStaffGuid");
        userDO.setIsEnable(false);
        userDO.setIsDeleted(false);
        userDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        userDO.setRegType("regType");
        userDO.setFaceCode("faceCode");
        final List<UserDO> userList = Arrays.asList(userDO);

        // Run the test
        userServiceImplUnderTest.createBatchFromCloud(userList);

        // Verify the results
    }

    @Test
    public void testUpdateBatchFromCloud() {
        // Setup
        final UserDO userDO = new UserDO();
        userDO.setId(0L);
        userDO.setGuid("userGuid");
        userDO.setEnterpriseNo("enterpriseNo");
        userDO.setAccount("account");
        userDO.setPassword("password");
        userDO.setAuthCode("authCode");
        userDO.setName("name");
        userDO.setPhone("phone");
        userDO.setOrgGuid("orgGuid");
        userDO.setOfficeCode(0);
        userDO.setOfficeName("itemName");
        userDO.setCreateStaffGuid("createStaffGuid");
        userDO.setUpdateStaffGuid("updateStaffGuid");
        userDO.setIsEnable(false);
        userDO.setIsDeleted(false);
        userDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        userDO.setRegType("regType");
        userDO.setFaceCode("faceCode");
        final List<UserDO> userList = Arrays.asList(userDO);

        // Run the test
        userServiceImplUnderTest.updateBatchFromCloud(userList);

        // Verify the results
    }

    @Test
    public void testNewUserOffice() {
        // Setup
        final UserOfficeDTO userOfficeDTO = new UserOfficeDTO(0, "itemName");
        final UserOfficeDTO expectedResult = new UserOfficeDTO(0, "itemName");

        // Configure DataDicMapper.selectOne(...).
        final DataDicDO dataDicDO = new DataDicDO();
        dataDicDO.setGuid("5d4e9761-d5bf-479f-9085-efcde3d17e94");
        dataDicDO.setTypeCode("typeCode");
        dataDicDO.setTypeName("typeName");
        dataDicDO.setItemCode(0);
        dataDicDO.setItemName("itemName");
        when(mockDataDicMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(dataDicDO);

        when(mockDistributedService.nextOfficeGuid()).thenReturn("5d4e9761-d5bf-479f-9085-efcde3d17e94");

        // Run the test
        final UserOfficeDTO result = userServiceImplUnderTest.newUserOffice(userOfficeDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);

        // Confirm DataDicMapper.insert(...).
        final DataDicDO entity = new DataDicDO();
        entity.setGuid("5d4e9761-d5bf-479f-9085-efcde3d17e94");
        entity.setTypeCode("typeCode");
        entity.setTypeName("typeName");
        entity.setItemCode(0);
        entity.setItemName("itemName");
        verify(mockDataDicMapper).insert(entity);
    }

    @Test
    public void testNewUserOffice_DataDicMapperSelectOneReturnsNull() {
        // Setup
        final UserOfficeDTO userOfficeDTO = new UserOfficeDTO(0, "itemName");
        final UserOfficeDTO expectedResult = new UserOfficeDTO(0, "itemName");
        when(mockDataDicMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);
        when(mockDistributedService.nextOfficeGuid()).thenReturn("5d4e9761-d5bf-479f-9085-efcde3d17e94");

        // Run the test
        final UserOfficeDTO result = userServiceImplUnderTest.newUserOffice(userOfficeDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);

        // Confirm DataDicMapper.insert(...).
        final DataDicDO entity = new DataDicDO();
        entity.setGuid("5d4e9761-d5bf-479f-9085-efcde3d17e94");
        entity.setTypeCode("typeCode");
        entity.setTypeName("typeName");
        entity.setItemCode(0);
        entity.setItemName("itemName");
        verify(mockDataDicMapper).insert(entity);
    }

    @Test
    public void testUserOfficeSpinner() {
        // Setup
        final List<UserOfficeDTO> expectedResult = Arrays.asList(new UserOfficeDTO(0, "itemName"));

        // Configure DataDicMapper.selectList(...).
        final DataDicDO dataDicDO = new DataDicDO();
        dataDicDO.setGuid("5d4e9761-d5bf-479f-9085-efcde3d17e94");
        dataDicDO.setTypeCode("typeCode");
        dataDicDO.setTypeName("typeName");
        dataDicDO.setItemCode(0);
        dataDicDO.setItemName("itemName");
        final List<DataDicDO> dataDicDOS = Arrays.asList(dataDicDO);
        when(mockDataDicMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(dataDicDOS);

        // Run the test
        final List<UserOfficeDTO> result = userServiceImplUnderTest.userOfficeSpinner();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testUserOfficeSpinner_DataDicMapperReturnsNoItems() {
        // Setup
        when(mockDataDicMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<UserOfficeDTO> result = userServiceImplUnderTest.userOfficeSpinner();

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testUpdate() {
        // Setup
        final UserDTO userDTO = new UserDTO();
        userDTO.setGuid("userGuid");
        userDTO.setEnterpriseNo("enterpriseNo");
        userDTO.setAccount("account");
        userDTO.setPassword("password");
        userDTO.setName("name");
        userDTO.setPhone("phone");
        final RoleDTO roleDTO = new RoleDTO();
        userDTO.setUserRoles(Arrays.asList(roleDTO));
        userDTO.setOrgGuid("orgGuid");
        final UserOrgDTO userOrg = new UserOrgDTO();
        userOrg.setGuid("9d51a484-e3c4-4006-9e4d-e9a4b1c21c1f");
        userOrg.setName("name");
        userOrg.setOrgNameTreeJoined("orgNameTreeJoined");
        userDTO.setUserOrg(userOrg);
        final OrgGeneralDTO orgGeneralDTO = new OrgGeneralDTO();
        userDTO.setEntireOrgTree(Arrays.asList(orgGeneralDTO));
        final UserOfficeDTO userOffice = new UserOfficeDTO();
        userOffice.setCode(0);
        userOffice.setName("itemName");
        userDTO.setUserOffice(userOffice);
        userDTO.setIsEnable(false);
        userDTO.setPhoneList(Arrays.asList("value"));
        userDTO.setEnterpriseGuid("enterpriseGuid");
        userDTO.setIntegrateFlag(false);
        userDTO.setErrorMsg("当前账号正在门店使用，不能删除");

        // Configure UserMapstruct.fromUserDTO(...).
        final UserDO userDO = new UserDO();
        userDO.setId(0L);
        userDO.setGuid("userGuid");
        userDO.setEnterpriseNo("enterpriseNo");
        userDO.setAccount("account");
        userDO.setPassword("password");
        userDO.setAuthCode("authCode");
        userDO.setName("name");
        userDO.setPhone("phone");
        userDO.setOrgGuid("orgGuid");
        userDO.setOfficeCode(0);
        userDO.setOfficeName("itemName");
        userDO.setCreateStaffGuid("createStaffGuid");
        userDO.setUpdateStaffGuid("updateStaffGuid");
        userDO.setIsEnable(false);
        userDO.setIsDeleted(false);
        userDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        userDO.setRegType("regType");
        userDO.setFaceCode("faceCode");
        final UserDTO userDTO1 = new UserDTO();
        userDTO1.setGuid("userGuid");
        userDTO1.setEnterpriseNo("enterpriseNo");
        userDTO1.setAccount("account");
        userDTO1.setPassword("password");
        userDTO1.setName("name");
        userDTO1.setPhone("phone");
        final RoleDTO roleDTO1 = new RoleDTO();
        userDTO1.setUserRoles(Arrays.asList(roleDTO1));
        userDTO1.setOrgGuid("orgGuid");
        final UserOrgDTO userOrg1 = new UserOrgDTO();
        userOrg1.setGuid("9d51a484-e3c4-4006-9e4d-e9a4b1c21c1f");
        userOrg1.setName("name");
        userOrg1.setOrgNameTreeJoined("orgNameTreeJoined");
        userDTO1.setUserOrg(userOrg1);
        final OrgGeneralDTO orgGeneralDTO1 = new OrgGeneralDTO();
        userDTO1.setEntireOrgTree(Arrays.asList(orgGeneralDTO1));
        final UserOfficeDTO userOffice1 = new UserOfficeDTO();
        userOffice1.setCode(0);
        userOffice1.setName("itemName");
        userDTO1.setUserOffice(userOffice1);
        userDTO1.setIsEnable(false);
        userDTO1.setPhoneList(Arrays.asList("value"));
        userDTO1.setEnterpriseGuid("enterpriseGuid");
        userDTO1.setIntegrateFlag(false);
        userDTO1.setErrorMsg("当前账号正在门店使用，不能删除");
        when(mockUserMapstruct.fromUserDTO(userDTO1)).thenReturn(userDO);

        when(mockUserClient.queryExistTel("phone", "userGuid")).thenReturn(0);

        // Configure DataDicMapper.selectOne(...).
        final DataDicDO dataDicDO = new DataDicDO();
        dataDicDO.setGuid("5d4e9761-d5bf-479f-9085-efcde3d17e94");
        dataDicDO.setTypeCode("typeCode");
        dataDicDO.setTypeName("typeName");
        dataDicDO.setItemCode(0);
        dataDicDO.setItemName("itemName");
        when(mockDataDicMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(dataDicDO);

        when(mockDistributedService.nextOfficeGuid()).thenReturn("5d4e9761-d5bf-479f-9085-efcde3d17e94");

        // Run the test
        final String result = userServiceImplUnderTest.update(userDTO);

        // Verify the results
        assertThat(result).isEqualTo("userGuid");

        // Confirm DataDicMapper.insert(...).
        final DataDicDO entity = new DataDicDO();
        entity.setGuid("5d4e9761-d5bf-479f-9085-efcde3d17e94");
        entity.setTypeCode("typeCode");
        entity.setTypeName("typeName");
        entity.setItemCode(0);
        entity.setItemName("itemName");
        verify(mockDataDicMapper).insert(entity);

        // Confirm UserRoleService.updateUserRoleRelation(...).
        final RoleDTO roleDTO2 = new RoleDTO();
        roleDTO2.setGuid("c2ee67b6-e602-4053-9e32-b563d41a16ea");
        roleDTO2.setName("name");
        roleDTO2.setIsEnable(false);
        roleDTO2.setIsDeleted(false);
        roleDTO2.setCreateStaffGuid("createStaffGuid");
        final List<RoleDTO> arrayOfRoleDTO = Arrays.asList(roleDTO2);
        verify(mockUserRoleService).updateUserRoleRelation("userGuid", arrayOfRoleDTO);

        // Confirm UserUploadService.updateUser(...).
        final UserDO userDO1 = new UserDO();
        userDO1.setId(0L);
        userDO1.setGuid("userGuid");
        userDO1.setEnterpriseNo("enterpriseNo");
        userDO1.setAccount("account");
        userDO1.setPassword("password");
        userDO1.setAuthCode("authCode");
        userDO1.setName("name");
        userDO1.setPhone("phone");
        userDO1.setOrgGuid("orgGuid");
        userDO1.setOfficeCode(0);
        userDO1.setOfficeName("itemName");
        userDO1.setCreateStaffGuid("createStaffGuid");
        userDO1.setUpdateStaffGuid("updateStaffGuid");
        userDO1.setIsEnable(false);
        userDO1.setIsDeleted(false);
        userDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        userDO1.setRegType("regType");
        userDO1.setFaceCode("faceCode");
        verify(mockUserUploadService).updateUser(userDO1);
    }

    @Test
    public void testUpdate_DataDicMapperSelectOneReturnsNull() {
        // Setup
        final UserDTO userDTO = new UserDTO();
        userDTO.setGuid("userGuid");
        userDTO.setEnterpriseNo("enterpriseNo");
        userDTO.setAccount("account");
        userDTO.setPassword("password");
        userDTO.setName("name");
        userDTO.setPhone("phone");
        final RoleDTO roleDTO = new RoleDTO();
        userDTO.setUserRoles(Arrays.asList(roleDTO));
        userDTO.setOrgGuid("orgGuid");
        final UserOrgDTO userOrg = new UserOrgDTO();
        userOrg.setGuid("9d51a484-e3c4-4006-9e4d-e9a4b1c21c1f");
        userOrg.setName("name");
        userOrg.setOrgNameTreeJoined("orgNameTreeJoined");
        userDTO.setUserOrg(userOrg);
        final OrgGeneralDTO orgGeneralDTO = new OrgGeneralDTO();
        userDTO.setEntireOrgTree(Arrays.asList(orgGeneralDTO));
        final UserOfficeDTO userOffice = new UserOfficeDTO();
        userOffice.setCode(0);
        userOffice.setName("itemName");
        userDTO.setUserOffice(userOffice);
        userDTO.setIsEnable(false);
        userDTO.setPhoneList(Arrays.asList("value"));
        userDTO.setEnterpriseGuid("enterpriseGuid");
        userDTO.setIntegrateFlag(false);
        userDTO.setErrorMsg("当前账号正在门店使用，不能删除");

        // Configure UserMapstruct.fromUserDTO(...).
        final UserDO userDO = new UserDO();
        userDO.setId(0L);
        userDO.setGuid("userGuid");
        userDO.setEnterpriseNo("enterpriseNo");
        userDO.setAccount("account");
        userDO.setPassword("password");
        userDO.setAuthCode("authCode");
        userDO.setName("name");
        userDO.setPhone("phone");
        userDO.setOrgGuid("orgGuid");
        userDO.setOfficeCode(0);
        userDO.setOfficeName("itemName");
        userDO.setCreateStaffGuid("createStaffGuid");
        userDO.setUpdateStaffGuid("updateStaffGuid");
        userDO.setIsEnable(false);
        userDO.setIsDeleted(false);
        userDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        userDO.setRegType("regType");
        userDO.setFaceCode("faceCode");
        final UserDTO userDTO1 = new UserDTO();
        userDTO1.setGuid("userGuid");
        userDTO1.setEnterpriseNo("enterpriseNo");
        userDTO1.setAccount("account");
        userDTO1.setPassword("password");
        userDTO1.setName("name");
        userDTO1.setPhone("phone");
        final RoleDTO roleDTO1 = new RoleDTO();
        userDTO1.setUserRoles(Arrays.asList(roleDTO1));
        userDTO1.setOrgGuid("orgGuid");
        final UserOrgDTO userOrg1 = new UserOrgDTO();
        userOrg1.setGuid("9d51a484-e3c4-4006-9e4d-e9a4b1c21c1f");
        userOrg1.setName("name");
        userOrg1.setOrgNameTreeJoined("orgNameTreeJoined");
        userDTO1.setUserOrg(userOrg1);
        final OrgGeneralDTO orgGeneralDTO1 = new OrgGeneralDTO();
        userDTO1.setEntireOrgTree(Arrays.asList(orgGeneralDTO1));
        final UserOfficeDTO userOffice1 = new UserOfficeDTO();
        userOffice1.setCode(0);
        userOffice1.setName("itemName");
        userDTO1.setUserOffice(userOffice1);
        userDTO1.setIsEnable(false);
        userDTO1.setPhoneList(Arrays.asList("value"));
        userDTO1.setEnterpriseGuid("enterpriseGuid");
        userDTO1.setIntegrateFlag(false);
        userDTO1.setErrorMsg("当前账号正在门店使用，不能删除");
        when(mockUserMapstruct.fromUserDTO(userDTO1)).thenReturn(userDO);

        when(mockUserClient.queryExistTel("phone", "userGuid")).thenReturn(0);
        when(mockDataDicMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);
        when(mockDistributedService.nextOfficeGuid()).thenReturn("5d4e9761-d5bf-479f-9085-efcde3d17e94");

        // Run the test
        final String result = userServiceImplUnderTest.update(userDTO);

        // Verify the results
        assertThat(result).isEqualTo("userGuid");

        // Confirm DataDicMapper.insert(...).
        final DataDicDO entity = new DataDicDO();
        entity.setGuid("5d4e9761-d5bf-479f-9085-efcde3d17e94");
        entity.setTypeCode("typeCode");
        entity.setTypeName("typeName");
        entity.setItemCode(0);
        entity.setItemName("itemName");
        verify(mockDataDicMapper).insert(entity);

        // Confirm UserRoleService.updateUserRoleRelation(...).
        final RoleDTO roleDTO2 = new RoleDTO();
        roleDTO2.setGuid("c2ee67b6-e602-4053-9e32-b563d41a16ea");
        roleDTO2.setName("name");
        roleDTO2.setIsEnable(false);
        roleDTO2.setIsDeleted(false);
        roleDTO2.setCreateStaffGuid("createStaffGuid");
        final List<RoleDTO> arrayOfRoleDTO = Arrays.asList(roleDTO2);
        verify(mockUserRoleService).updateUserRoleRelation("userGuid", arrayOfRoleDTO);

        // Confirm UserUploadService.updateUser(...).
        final UserDO userDO1 = new UserDO();
        userDO1.setId(0L);
        userDO1.setGuid("userGuid");
        userDO1.setEnterpriseNo("enterpriseNo");
        userDO1.setAccount("account");
        userDO1.setPassword("password");
        userDO1.setAuthCode("authCode");
        userDO1.setName("name");
        userDO1.setPhone("phone");
        userDO1.setOrgGuid("orgGuid");
        userDO1.setOfficeCode(0);
        userDO1.setOfficeName("itemName");
        userDO1.setCreateStaffGuid("createStaffGuid");
        userDO1.setUpdateStaffGuid("updateStaffGuid");
        userDO1.setIsEnable(false);
        userDO1.setIsDeleted(false);
        userDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        userDO1.setRegType("regType");
        userDO1.setFaceCode("faceCode");
        verify(mockUserUploadService).updateUser(userDO1);
    }

    @Test
    public void testEnableOrDisable() {
        // Setup
        final UserDTO userDTO = new UserDTO();
        userDTO.setGuid("userGuid");
        userDTO.setEnterpriseNo("enterpriseNo");
        userDTO.setAccount("account");
        userDTO.setPassword("password");
        userDTO.setName("name");
        userDTO.setPhone("phone");
        final RoleDTO roleDTO = new RoleDTO();
        userDTO.setUserRoles(Arrays.asList(roleDTO));
        userDTO.setOrgGuid("orgGuid");
        final UserOrgDTO userOrg = new UserOrgDTO();
        userOrg.setGuid("9d51a484-e3c4-4006-9e4d-e9a4b1c21c1f");
        userOrg.setName("name");
        userOrg.setOrgNameTreeJoined("orgNameTreeJoined");
        userDTO.setUserOrg(userOrg);
        final OrgGeneralDTO orgGeneralDTO = new OrgGeneralDTO();
        userDTO.setEntireOrgTree(Arrays.asList(orgGeneralDTO));
        final UserOfficeDTO userOffice = new UserOfficeDTO();
        userOffice.setCode(0);
        userOffice.setName("itemName");
        userDTO.setUserOffice(userOffice);
        userDTO.setIsEnable(false);
        userDTO.setPhoneList(Arrays.asList("value"));
        userDTO.setEnterpriseGuid("enterpriseGuid");
        userDTO.setIntegrateFlag(false);
        userDTO.setErrorMsg("当前账号正在门店使用，不能删除");

        // Run the test
        userServiceImplUnderTest.enableOrDisable(userDTO);

        // Verify the results
        // Confirm UserUploadService.updateUser(...).
        final UserDO userDO = new UserDO();
        userDO.setId(0L);
        userDO.setGuid("userGuid");
        userDO.setEnterpriseNo("enterpriseNo");
        userDO.setAccount("account");
        userDO.setPassword("password");
        userDO.setAuthCode("authCode");
        userDO.setName("name");
        userDO.setPhone("phone");
        userDO.setOrgGuid("orgGuid");
        userDO.setOfficeCode(0);
        userDO.setOfficeName("itemName");
        userDO.setCreateStaffGuid("createStaffGuid");
        userDO.setUpdateStaffGuid("updateStaffGuid");
        userDO.setIsEnable(false);
        userDO.setIsDeleted(false);
        userDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        userDO.setRegType("regType");
        userDO.setFaceCode("faceCode");
        verify(mockUserUploadService).updateUser(userDO);
    }

    @Test
    public void testDelete_ThrowsBusinessException() {
        // Setup
        final UserDTO userDTO = new UserDTO();
        userDTO.setGuid("userGuid");
        userDTO.setEnterpriseNo("enterpriseNo");
        userDTO.setAccount("account");
        userDTO.setPassword("password");
        userDTO.setName("name");
        userDTO.setPhone("phone");
        final RoleDTO roleDTO = new RoleDTO();
        userDTO.setUserRoles(Arrays.asList(roleDTO));
        userDTO.setOrgGuid("orgGuid");
        final UserOrgDTO userOrg = new UserOrgDTO();
        userOrg.setGuid("9d51a484-e3c4-4006-9e4d-e9a4b1c21c1f");
        userOrg.setName("name");
        userOrg.setOrgNameTreeJoined("orgNameTreeJoined");
        userDTO.setUserOrg(userOrg);
        final OrgGeneralDTO orgGeneralDTO = new OrgGeneralDTO();
        userDTO.setEntireOrgTree(Arrays.asList(orgGeneralDTO));
        final UserOfficeDTO userOffice = new UserOfficeDTO();
        userOffice.setCode(0);
        userOffice.setName("itemName");
        userDTO.setUserOffice(userOffice);
        userDTO.setIsEnable(false);
        userDTO.setPhoneList(Arrays.asList("value"));
        userDTO.setEnterpriseGuid("enterpriseGuid");
        userDTO.setIntegrateFlag(false);
        userDTO.setErrorMsg("当前账号正在门店使用，不能删除");

        when(mockRoleMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(0);

        // Configure BusinessClient.queryByUserGuid(...).
        final HandoverRecordDTO handoverRecordDTO = new HandoverRecordDTO();
        handoverRecordDTO.setStoreGuid("storeGuid");
        handoverRecordDTO.setStoreName("storeName");
        handoverRecordDTO.setTerminalId("terminalId");
        handoverRecordDTO.setCreateGuid("createGuid");
        handoverRecordDTO.setStatus(0);
        when(mockBusinessClient.queryByUserGuid(
                new HandoverRecordConfirmDTO("terminalId", new BigDecimal("0.00"), Arrays.asList("value"),
                        0))).thenReturn(handoverRecordDTO);

        // Run the test
        assertThatThrownBy(() -> userServiceImplUnderTest.delete(userDTO)).isInstanceOf(BusinessException.class);
    }

    @Test
    public void testDelete_BusinessClientReturnsNull() {
        // Setup
        final UserDTO userDTO = new UserDTO();
        userDTO.setGuid("userGuid");
        userDTO.setEnterpriseNo("enterpriseNo");
        userDTO.setAccount("account");
        userDTO.setPassword("password");
        userDTO.setName("name");
        userDTO.setPhone("phone");
        final RoleDTO roleDTO = new RoleDTO();
        userDTO.setUserRoles(Arrays.asList(roleDTO));
        userDTO.setOrgGuid("orgGuid");
        final UserOrgDTO userOrg = new UserOrgDTO();
        userOrg.setGuid("9d51a484-e3c4-4006-9e4d-e9a4b1c21c1f");
        userOrg.setName("name");
        userOrg.setOrgNameTreeJoined("orgNameTreeJoined");
        userDTO.setUserOrg(userOrg);
        final OrgGeneralDTO orgGeneralDTO = new OrgGeneralDTO();
        userDTO.setEntireOrgTree(Arrays.asList(orgGeneralDTO));
        final UserOfficeDTO userOffice = new UserOfficeDTO();
        userOffice.setCode(0);
        userOffice.setName("itemName");
        userDTO.setUserOffice(userOffice);
        userDTO.setIsEnable(false);
        userDTO.setPhoneList(Arrays.asList("value"));
        userDTO.setEnterpriseGuid("enterpriseGuid");
        userDTO.setIntegrateFlag(false);
        userDTO.setErrorMsg("当前账号正在门店使用，不能删除");

        when(mockRoleMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(0);
        when(mockBusinessClient.queryByUserGuid(
                new HandoverRecordConfirmDTO("terminalId", new BigDecimal("0.00"), Arrays.asList("value"),
                        0))).thenReturn(null);

        // Run the test
        userServiceImplUnderTest.delete(userDTO);

        // Verify the results
        verify(mockUserRoleService).deleteUserRoleRelation("userGuid");
        verify(mockUserDataService).deleteUserDataRules("userGuid");
        verify(mockUserUploadService).deleteUser("userGuid");
    }

    @Test
    public void testUpdatePwd() {
        // Setup
        final UpdatePwdDTO updatePwdDTO = new UpdatePwdDTO();
        updatePwdDTO.setDeviceId("deviceId");
        updatePwdDTO.setUserGuid("userGuid");
        updatePwdDTO.setGuid("2c6ac89d-3df1-4feb-80db-f056b8d2c86b");
        updatePwdDTO.setOldPwd("oldPwd");
        updatePwdDTO.setNewPwd("newPwd");

        // Configure UserClient.findUserById(...).
        final com.holderzone.resource.common.dto.user.UserDTO userDTO = new com.holderzone.resource.common.dto.user.UserDTO();
        userDTO.setPhoneList(Arrays.asList("value"));
        userDTO.setUserGuid("userGuid");
        userDTO.setEnterpriseGuid("enterpriseGuid");
        userDTO.setCreateStaffGuid("createStaffGuid");
        userDTO.setPassword("password");
        when(mockUserClient.findUserById("userGuid")).thenReturn(userDTO);

        // Run the test
        userServiceImplUnderTest.updatePwd(updatePwdDTO);

        // Verify the results
        // Confirm UserUploadService.updateUser(...).
        final UserDO userDO = new UserDO();
        userDO.setId(0L);
        userDO.setGuid("userGuid");
        userDO.setEnterpriseNo("enterpriseNo");
        userDO.setAccount("account");
        userDO.setPassword("password");
        userDO.setAuthCode("authCode");
        userDO.setName("name");
        userDO.setPhone("phone");
        userDO.setOrgGuid("orgGuid");
        userDO.setOfficeCode(0);
        userDO.setOfficeName("itemName");
        userDO.setCreateStaffGuid("createStaffGuid");
        userDO.setUpdateStaffGuid("updateStaffGuid");
        userDO.setIsEnable(false);
        userDO.setIsDeleted(false);
        userDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        userDO.setRegType("regType");
        userDO.setFaceCode("faceCode");
        verify(mockUserUploadService).updateUser(userDO);
    }

    @Test
    public void testResetPwd() {
        // Setup
        final ResetPwdDTO resetPwdDTO = new ResetPwdDTO();
        resetPwdDTO.setDeviceId("deviceId");
        resetPwdDTO.setUserGuid("userGuid");
        resetPwdDTO.setGuid("678576f9-abc2-4248-8d4a-317e78c4bc50");

        // Configure EnterpriseClient.getMessageInfo(...).
        final MessageConfigDTO messageConfigDTO = new MessageConfigDTO();
        messageConfigDTO.setId(0);
        messageConfigDTO.setAppreciateGuid("appreciateGuid");
        messageConfigDTO.setAfterConsume(0);
        messageConfigDTO.setAfterCharge(0);
        messageConfigDTO.setResidueCount(0);
        when(mockEnterpriseClient.getMessageInfo("enterpriseGuid")).thenReturn(messageConfigDTO);

        // Run the test
        userServiceImplUnderTest.resetPwd(resetPwdDTO);

        // Verify the results
        verify(mockSmsClient).sendMessage(any(MessageDTO.class));

        // Confirm UserUploadService.updateUser(...).
        final UserDO userDO = new UserDO();
        userDO.setId(0L);
        userDO.setGuid("userGuid");
        userDO.setEnterpriseNo("enterpriseNo");
        userDO.setAccount("account");
        userDO.setPassword("password");
        userDO.setAuthCode("authCode");
        userDO.setName("name");
        userDO.setPhone("phone");
        userDO.setOrgGuid("orgGuid");
        userDO.setOfficeCode(0);
        userDO.setOfficeName("itemName");
        userDO.setCreateStaffGuid("createStaffGuid");
        userDO.setUpdateStaffGuid("updateStaffGuid");
        userDO.setIsEnable(false);
        userDO.setIsDeleted(false);
        userDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        userDO.setRegType("regType");
        userDO.setFaceCode("faceCode");
        verify(mockUserUploadService).updateUser(userDO);
    }

    @Test
    public void testResetPwd_EnterpriseClientReturnsNull() {
        // Setup
        final ResetPwdDTO resetPwdDTO = new ResetPwdDTO();
        resetPwdDTO.setDeviceId("deviceId");
        resetPwdDTO.setUserGuid("userGuid");
        resetPwdDTO.setGuid("678576f9-abc2-4248-8d4a-317e78c4bc50");

        when(mockEnterpriseClient.getMessageInfo("enterpriseGuid")).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> userServiceImplUnderTest.resetPwd(resetPwdDTO)).isInstanceOf(BusinessException.class);
    }

    @Test
    public void testResetPwdFromCloud() {
        // Setup
        final UserDTO userDTO = new UserDTO();
        userDTO.setGuid("userGuid");
        userDTO.setEnterpriseNo("enterpriseNo");
        userDTO.setAccount("account");
        userDTO.setPassword("password");
        userDTO.setName("name");
        userDTO.setPhone("phone");
        final RoleDTO roleDTO = new RoleDTO();
        userDTO.setUserRoles(Arrays.asList(roleDTO));
        userDTO.setOrgGuid("orgGuid");
        final UserOrgDTO userOrg = new UserOrgDTO();
        userOrg.setGuid("9d51a484-e3c4-4006-9e4d-e9a4b1c21c1f");
        userOrg.setName("name");
        userOrg.setOrgNameTreeJoined("orgNameTreeJoined");
        userDTO.setUserOrg(userOrg);
        final OrgGeneralDTO orgGeneralDTO = new OrgGeneralDTO();
        userDTO.setEntireOrgTree(Arrays.asList(orgGeneralDTO));
        final UserOfficeDTO userOffice = new UserOfficeDTO();
        userOffice.setCode(0);
        userOffice.setName("itemName");
        userDTO.setUserOffice(userOffice);
        userDTO.setIsEnable(false);
        userDTO.setPhoneList(Arrays.asList("value"));
        userDTO.setEnterpriseGuid("enterpriseGuid");
        userDTO.setIntegrateFlag(false);
        userDTO.setErrorMsg("当前账号正在门店使用，不能删除");

        // Run the test
        userServiceImplUnderTest.resetPwdFromCloud(userDTO);

        // Verify the results
    }

    @Test
    public void testPageQuery() {
        // Setup
        final UserQueryDTO userQueryDTO = new UserQueryDTO();
        userQueryDTO.setGenericOrgGuids(Arrays.asList("value"));
        userQueryDTO.setSearchKey("searchKey");
        userQueryDTO.setCreateStaffGuid("createStaffGuid");
        userQueryDTO.setShowPassword(0);
        userQueryDTO.setDeviceTypeCode(0);

        // Configure UserMapstruct.fromUserQueryDTO(...).
        final UserCondQuery userCondQuery = new UserCondQuery();
        userCondQuery.setOrgGuidsWithChildren(Arrays.asList("value"));
        userCondQuery.setSearchKey("searchKey");
        userCondQuery.setCurrentStaffGuid("currentStaffGuid");
        userCondQuery.setShowPassword(0);
        userCondQuery.setIsWaiter(0);
        final UserQueryDTO userQueryDTO1 = new UserQueryDTO();
        userQueryDTO1.setGenericOrgGuids(Arrays.asList("value"));
        userQueryDTO1.setSearchKey("searchKey");
        userQueryDTO1.setCreateStaffGuid("createStaffGuid");
        userQueryDTO1.setShowPassword(0);
        userQueryDTO1.setDeviceTypeCode(0);
        when(mockUserMapstruct.fromUserQueryDTO(userQueryDTO1)).thenReturn(userCondQuery);

        when(mockOrgFeignClient.queryOrgChildren(Arrays.asList("value"))).thenReturn(Arrays.asList("value"));

        // Configure OrgFeignClient.queryOrgNameByIdList(...).
        final OrganizationDTO organizationDTO = new OrganizationDTO();
        organizationDTO.setGuid("09a54b01-9927-45b2-89df-d95317d8b7de");
        organizationDTO.setUuid("900c6923-d933-4f1c-944f-6a3f37ff821c");
        organizationDTO.setName("name");
        organizationDTO.setContactName("contactName");
        organizationDTO.setContactTel("contactTel");
        final List<OrganizationDTO> organizationDTOS = Arrays.asList(organizationDTO);
        when(mockOrgFeignClient.queryOrgNameByIdList(Arrays.asList("value"))).thenReturn(organizationDTOS);

        // Configure UserMapstruct.toUserDTO(...).
        final UserDTO userDTO = new UserDTO();
        userDTO.setGuid("userGuid");
        userDTO.setEnterpriseNo("enterpriseNo");
        userDTO.setAccount("account");
        userDTO.setPassword("password");
        userDTO.setName("name");
        userDTO.setPhone("phone");
        final RoleDTO roleDTO = new RoleDTO();
        userDTO.setUserRoles(Arrays.asList(roleDTO));
        userDTO.setOrgGuid("orgGuid");
        final UserOrgDTO userOrg = new UserOrgDTO();
        userOrg.setGuid("9d51a484-e3c4-4006-9e4d-e9a4b1c21c1f");
        userOrg.setName("name");
        userOrg.setOrgNameTreeJoined("orgNameTreeJoined");
        userDTO.setUserOrg(userOrg);
        final OrgGeneralDTO orgGeneralDTO = new OrgGeneralDTO();
        userDTO.setEntireOrgTree(Arrays.asList(orgGeneralDTO));
        final UserOfficeDTO userOffice = new UserOfficeDTO();
        userOffice.setCode(0);
        userOffice.setName("itemName");
        userDTO.setUserOffice(userOffice);
        userDTO.setIsEnable(false);
        userDTO.setPhoneList(Arrays.asList("value"));
        userDTO.setEnterpriseGuid("enterpriseGuid");
        userDTO.setIntegrateFlag(false);
        userDTO.setErrorMsg("当前账号正在门店使用，不能删除");
        final UserReadDO userReadDO = new UserReadDO();
        userReadDO.setId(0L);
        userReadDO.setGuid("b93ec5af-5c9d-46a9-871e-3648af2dbf5d");
        userReadDO.setOrgGuid("9d51a484-e3c4-4006-9e4d-e9a4b1c21c1f");
        userReadDO.setOfficeCode(0);
        userReadDO.setOfficeName("itemName");
        when(mockUserMapstruct.toUserDTO(userReadDO)).thenReturn(userDTO);

        // Run the test
        final Page<UserDTO> result = userServiceImplUnderTest.pageQuery(userQueryDTO);

        // Verify the results
    }

    @Test
    public void testPageQuery_OrgFeignClientQueryOrgChildrenReturnsNoItems() {
        // Setup
        final UserQueryDTO userQueryDTO = new UserQueryDTO();
        userQueryDTO.setGenericOrgGuids(Arrays.asList("value"));
        userQueryDTO.setSearchKey("searchKey");
        userQueryDTO.setCreateStaffGuid("createStaffGuid");
        userQueryDTO.setShowPassword(0);
        userQueryDTO.setDeviceTypeCode(0);

        // Configure UserMapstruct.fromUserQueryDTO(...).
        final UserCondQuery userCondQuery = new UserCondQuery();
        userCondQuery.setOrgGuidsWithChildren(Arrays.asList("value"));
        userCondQuery.setSearchKey("searchKey");
        userCondQuery.setCurrentStaffGuid("currentStaffGuid");
        userCondQuery.setShowPassword(0);
        userCondQuery.setIsWaiter(0);
        final UserQueryDTO userQueryDTO1 = new UserQueryDTO();
        userQueryDTO1.setGenericOrgGuids(Arrays.asList("value"));
        userQueryDTO1.setSearchKey("searchKey");
        userQueryDTO1.setCreateStaffGuid("createStaffGuid");
        userQueryDTO1.setShowPassword(0);
        userQueryDTO1.setDeviceTypeCode(0);
        when(mockUserMapstruct.fromUserQueryDTO(userQueryDTO1)).thenReturn(userCondQuery);

        when(mockOrgFeignClient.queryOrgChildren(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Configure OrgFeignClient.queryOrgNameByIdList(...).
        final OrganizationDTO organizationDTO = new OrganizationDTO();
        organizationDTO.setGuid("09a54b01-9927-45b2-89df-d95317d8b7de");
        organizationDTO.setUuid("900c6923-d933-4f1c-944f-6a3f37ff821c");
        organizationDTO.setName("name");
        organizationDTO.setContactName("contactName");
        organizationDTO.setContactTel("contactTel");
        final List<OrganizationDTO> organizationDTOS = Arrays.asList(organizationDTO);
        when(mockOrgFeignClient.queryOrgNameByIdList(Arrays.asList("value"))).thenReturn(organizationDTOS);

        // Configure UserMapstruct.toUserDTO(...).
        final UserDTO userDTO = new UserDTO();
        userDTO.setGuid("userGuid");
        userDTO.setEnterpriseNo("enterpriseNo");
        userDTO.setAccount("account");
        userDTO.setPassword("password");
        userDTO.setName("name");
        userDTO.setPhone("phone");
        final RoleDTO roleDTO = new RoleDTO();
        userDTO.setUserRoles(Arrays.asList(roleDTO));
        userDTO.setOrgGuid("orgGuid");
        final UserOrgDTO userOrg = new UserOrgDTO();
        userOrg.setGuid("9d51a484-e3c4-4006-9e4d-e9a4b1c21c1f");
        userOrg.setName("name");
        userOrg.setOrgNameTreeJoined("orgNameTreeJoined");
        userDTO.setUserOrg(userOrg);
        final OrgGeneralDTO orgGeneralDTO = new OrgGeneralDTO();
        userDTO.setEntireOrgTree(Arrays.asList(orgGeneralDTO));
        final UserOfficeDTO userOffice = new UserOfficeDTO();
        userOffice.setCode(0);
        userOffice.setName("itemName");
        userDTO.setUserOffice(userOffice);
        userDTO.setIsEnable(false);
        userDTO.setPhoneList(Arrays.asList("value"));
        userDTO.setEnterpriseGuid("enterpriseGuid");
        userDTO.setIntegrateFlag(false);
        userDTO.setErrorMsg("当前账号正在门店使用，不能删除");
        final UserReadDO userReadDO = new UserReadDO();
        userReadDO.setId(0L);
        userReadDO.setGuid("b93ec5af-5c9d-46a9-871e-3648af2dbf5d");
        userReadDO.setOrgGuid("9d51a484-e3c4-4006-9e4d-e9a4b1c21c1f");
        userReadDO.setOfficeCode(0);
        userReadDO.setOfficeName("itemName");
        when(mockUserMapstruct.toUserDTO(userReadDO)).thenReturn(userDTO);

        // Run the test
        final Page<UserDTO> result = userServiceImplUnderTest.pageQuery(userQueryDTO);

        // Verify the results
    }

    @Test
    public void testPageQuery_OrgFeignClientQueryOrgNameByIdListReturnsNoItems() {
        // Setup
        final UserQueryDTO userQueryDTO = new UserQueryDTO();
        userQueryDTO.setGenericOrgGuids(Arrays.asList("value"));
        userQueryDTO.setSearchKey("searchKey");
        userQueryDTO.setCreateStaffGuid("createStaffGuid");
        userQueryDTO.setShowPassword(0);
        userQueryDTO.setDeviceTypeCode(0);

        // Configure UserMapstruct.fromUserQueryDTO(...).
        final UserCondQuery userCondQuery = new UserCondQuery();
        userCondQuery.setOrgGuidsWithChildren(Arrays.asList("value"));
        userCondQuery.setSearchKey("searchKey");
        userCondQuery.setCurrentStaffGuid("currentStaffGuid");
        userCondQuery.setShowPassword(0);
        userCondQuery.setIsWaiter(0);
        final UserQueryDTO userQueryDTO1 = new UserQueryDTO();
        userQueryDTO1.setGenericOrgGuids(Arrays.asList("value"));
        userQueryDTO1.setSearchKey("searchKey");
        userQueryDTO1.setCreateStaffGuid("createStaffGuid");
        userQueryDTO1.setShowPassword(0);
        userQueryDTO1.setDeviceTypeCode(0);
        when(mockUserMapstruct.fromUserQueryDTO(userQueryDTO1)).thenReturn(userCondQuery);

        when(mockOrgFeignClient.queryOrgChildren(Arrays.asList("value"))).thenReturn(Arrays.asList("value"));
        when(mockOrgFeignClient.queryOrgNameByIdList(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Configure UserMapstruct.toUserDTO(...).
        final UserDTO userDTO = new UserDTO();
        userDTO.setGuid("userGuid");
        userDTO.setEnterpriseNo("enterpriseNo");
        userDTO.setAccount("account");
        userDTO.setPassword("password");
        userDTO.setName("name");
        userDTO.setPhone("phone");
        final RoleDTO roleDTO = new RoleDTO();
        userDTO.setUserRoles(Arrays.asList(roleDTO));
        userDTO.setOrgGuid("orgGuid");
        final UserOrgDTO userOrg = new UserOrgDTO();
        userOrg.setGuid("9d51a484-e3c4-4006-9e4d-e9a4b1c21c1f");
        userOrg.setName("name");
        userOrg.setOrgNameTreeJoined("orgNameTreeJoined");
        userDTO.setUserOrg(userOrg);
        final OrgGeneralDTO orgGeneralDTO = new OrgGeneralDTO();
        userDTO.setEntireOrgTree(Arrays.asList(orgGeneralDTO));
        final UserOfficeDTO userOffice = new UserOfficeDTO();
        userOffice.setCode(0);
        userOffice.setName("itemName");
        userDTO.setUserOffice(userOffice);
        userDTO.setIsEnable(false);
        userDTO.setPhoneList(Arrays.asList("value"));
        userDTO.setEnterpriseGuid("enterpriseGuid");
        userDTO.setIntegrateFlag(false);
        userDTO.setErrorMsg("当前账号正在门店使用，不能删除");
        final UserReadDO userReadDO = new UserReadDO();
        userReadDO.setId(0L);
        userReadDO.setGuid("b93ec5af-5c9d-46a9-871e-3648af2dbf5d");
        userReadDO.setOrgGuid("9d51a484-e3c4-4006-9e4d-e9a4b1c21c1f");
        userReadDO.setOfficeCode(0);
        userReadDO.setOfficeName("itemName");
        when(mockUserMapstruct.toUserDTO(userReadDO)).thenReturn(userDTO);

        // Run the test
        final Page<UserDTO> result = userServiceImplUnderTest.pageQuery(userQueryDTO);

        // Verify the results
    }

    @Test
    public void testGetByUserGuid() {
        // Setup
        final UserDO expectedResult = new UserDO();
        expectedResult.setId(0L);
        expectedResult.setGuid("userGuid");
        expectedResult.setEnterpriseNo("enterpriseNo");
        expectedResult.setAccount("account");
        expectedResult.setPassword("password");
        expectedResult.setAuthCode("authCode");
        expectedResult.setName("name");
        expectedResult.setPhone("phone");
        expectedResult.setOrgGuid("orgGuid");
        expectedResult.setOfficeCode(0);
        expectedResult.setOfficeName("itemName");
        expectedResult.setCreateStaffGuid("createStaffGuid");
        expectedResult.setUpdateStaffGuid("updateStaffGuid");
        expectedResult.setIsEnable(false);
        expectedResult.setIsDeleted(false);
        expectedResult.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRegType("regType");
        expectedResult.setFaceCode("faceCode");

        // Run the test
        final UserDO result = userServiceImplUnderTest.getByUserGuid("userGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQuery() {
        // Setup
        final UserDTO userDTO = new UserDTO();
        userDTO.setGuid("userGuid");
        userDTO.setEnterpriseNo("enterpriseNo");
        userDTO.setAccount("account");
        userDTO.setPassword("password");
        userDTO.setName("name");
        userDTO.setPhone("phone");
        final RoleDTO roleDTO = new RoleDTO();
        userDTO.setUserRoles(Arrays.asList(roleDTO));
        userDTO.setOrgGuid("orgGuid");
        final UserOrgDTO userOrg = new UserOrgDTO();
        userOrg.setGuid("9d51a484-e3c4-4006-9e4d-e9a4b1c21c1f");
        userOrg.setName("name");
        userOrg.setOrgNameTreeJoined("orgNameTreeJoined");
        userDTO.setUserOrg(userOrg);
        final OrgGeneralDTO orgGeneralDTO = new OrgGeneralDTO();
        userDTO.setEntireOrgTree(Arrays.asList(orgGeneralDTO));
        final UserOfficeDTO userOffice = new UserOfficeDTO();
        userOffice.setCode(0);
        userOffice.setName("itemName");
        userDTO.setUserOffice(userOffice);
        userDTO.setIsEnable(false);
        userDTO.setPhoneList(Arrays.asList("value"));
        userDTO.setEnterpriseGuid("enterpriseGuid");
        userDTO.setIntegrateFlag(false);
        userDTO.setErrorMsg("当前账号正在门店使用，不能删除");

        final UserDTO expectedResult = new UserDTO();
        expectedResult.setGuid("userGuid");
        expectedResult.setEnterpriseNo("enterpriseNo");
        expectedResult.setAccount("account");
        expectedResult.setPassword("password");
        expectedResult.setName("name");
        expectedResult.setPhone("phone");
        final RoleDTO roleDTO1 = new RoleDTO();
        expectedResult.setUserRoles(Arrays.asList(roleDTO1));
        expectedResult.setOrgGuid("orgGuid");
        final UserOrgDTO userOrg1 = new UserOrgDTO();
        userOrg1.setGuid("9d51a484-e3c4-4006-9e4d-e9a4b1c21c1f");
        userOrg1.setName("name");
        userOrg1.setOrgNameTreeJoined("orgNameTreeJoined");
        expectedResult.setUserOrg(userOrg1);
        final OrgGeneralDTO orgGeneralDTO1 = new OrgGeneralDTO();
        expectedResult.setEntireOrgTree(Arrays.asList(orgGeneralDTO1));
        final UserOfficeDTO userOffice1 = new UserOfficeDTO();
        userOffice1.setCode(0);
        userOffice1.setName("itemName");
        expectedResult.setUserOffice(userOffice1);
        expectedResult.setIsEnable(false);
        expectedResult.setPhoneList(Arrays.asList("value"));
        expectedResult.setEnterpriseGuid("enterpriseGuid");
        expectedResult.setIntegrateFlag(false);
        expectedResult.setErrorMsg("当前账号正在门店使用，不能删除");

        // Configure UserMapstruct.fromUserDTO(...).
        final UserDO userDO = new UserDO();
        userDO.setId(0L);
        userDO.setGuid("userGuid");
        userDO.setEnterpriseNo("enterpriseNo");
        userDO.setAccount("account");
        userDO.setPassword("password");
        userDO.setAuthCode("authCode");
        userDO.setName("name");
        userDO.setPhone("phone");
        userDO.setOrgGuid("orgGuid");
        userDO.setOfficeCode(0);
        userDO.setOfficeName("itemName");
        userDO.setCreateStaffGuid("createStaffGuid");
        userDO.setUpdateStaffGuid("updateStaffGuid");
        userDO.setIsEnable(false);
        userDO.setIsDeleted(false);
        userDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        userDO.setRegType("regType");
        userDO.setFaceCode("faceCode");
        final UserDTO userDTO1 = new UserDTO();
        userDTO1.setGuid("userGuid");
        userDTO1.setEnterpriseNo("enterpriseNo");
        userDTO1.setAccount("account");
        userDTO1.setPassword("password");
        userDTO1.setName("name");
        userDTO1.setPhone("phone");
        final RoleDTO roleDTO2 = new RoleDTO();
        userDTO1.setUserRoles(Arrays.asList(roleDTO2));
        userDTO1.setOrgGuid("orgGuid");
        final UserOrgDTO userOrg2 = new UserOrgDTO();
        userOrg2.setGuid("9d51a484-e3c4-4006-9e4d-e9a4b1c21c1f");
        userOrg2.setName("name");
        userOrg2.setOrgNameTreeJoined("orgNameTreeJoined");
        userDTO1.setUserOrg(userOrg2);
        final OrgGeneralDTO orgGeneralDTO2 = new OrgGeneralDTO();
        userDTO1.setEntireOrgTree(Arrays.asList(orgGeneralDTO2));
        final UserOfficeDTO userOffice2 = new UserOfficeDTO();
        userOffice2.setCode(0);
        userOffice2.setName("itemName");
        userDTO1.setUserOffice(userOffice2);
        userDTO1.setIsEnable(false);
        userDTO1.setPhoneList(Arrays.asList("value"));
        userDTO1.setEnterpriseGuid("enterpriseGuid");
        userDTO1.setIntegrateFlag(false);
        userDTO1.setErrorMsg("当前账号正在门店使用，不能删除");
        when(mockUserMapstruct.fromUserDTO(userDTO1)).thenReturn(userDO);

        // Configure UserMapstruct.toUserDTO(...).
        final UserDTO userDTO2 = new UserDTO();
        userDTO2.setGuid("userGuid");
        userDTO2.setEnterpriseNo("enterpriseNo");
        userDTO2.setAccount("account");
        userDTO2.setPassword("password");
        userDTO2.setName("name");
        userDTO2.setPhone("phone");
        final RoleDTO roleDTO3 = new RoleDTO();
        userDTO2.setUserRoles(Arrays.asList(roleDTO3));
        userDTO2.setOrgGuid("orgGuid");
        final UserOrgDTO userOrg3 = new UserOrgDTO();
        userOrg3.setGuid("9d51a484-e3c4-4006-9e4d-e9a4b1c21c1f");
        userOrg3.setName("name");
        userOrg3.setOrgNameTreeJoined("orgNameTreeJoined");
        userDTO2.setUserOrg(userOrg3);
        final OrgGeneralDTO orgGeneralDTO3 = new OrgGeneralDTO();
        userDTO2.setEntireOrgTree(Arrays.asList(orgGeneralDTO3));
        final UserOfficeDTO userOffice3 = new UserOfficeDTO();
        userOffice3.setCode(0);
        userOffice3.setName("itemName");
        userDTO2.setUserOffice(userOffice3);
        userDTO2.setIsEnable(false);
        userDTO2.setPhoneList(Arrays.asList("value"));
        userDTO2.setEnterpriseGuid("enterpriseGuid");
        userDTO2.setIntegrateFlag(false);
        userDTO2.setErrorMsg("当前账号正在门店使用，不能删除");
        final UserReadDO userReadDO = new UserReadDO();
        userReadDO.setId(0L);
        userReadDO.setGuid("b93ec5af-5c9d-46a9-871e-3648af2dbf5d");
        userReadDO.setOrgGuid("9d51a484-e3c4-4006-9e4d-e9a4b1c21c1f");
        userReadDO.setOfficeCode(0);
        userReadDO.setOfficeName("itemName");
        when(mockUserMapstruct.toUserDTO(userReadDO)).thenReturn(userDTO2);

        when(mockOrgFeignClient.queryOrgWithParentByIdList(Arrays.asList("value"))).thenReturn(new HashMap<>());

        // Configure OrgFeignClient.queryErpOrgStore(...).
        final List<OrgGeneralDTO> orgGeneralDTOS = Arrays.asList(
                new OrgGeneralDTO("4c51e25b-eccc-4d20-a910-4f4ec7548a4f", "name", 0, false, Arrays.asList()));
        when(mockOrgFeignClient.queryErpOrgStore(1, 1)).thenReturn(orgGeneralDTOS);

        // Run the test
        final UserDTO result = userServiceImplUnderTest.query(userDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQuery_OrgFeignClientQueryErpOrgStoreReturnsNoItems() {
        // Setup
        final UserDTO userDTO = new UserDTO();
        userDTO.setGuid("userGuid");
        userDTO.setEnterpriseNo("enterpriseNo");
        userDTO.setAccount("account");
        userDTO.setPassword("password");
        userDTO.setName("name");
        userDTO.setPhone("phone");
        final RoleDTO roleDTO = new RoleDTO();
        userDTO.setUserRoles(Arrays.asList(roleDTO));
        userDTO.setOrgGuid("orgGuid");
        final UserOrgDTO userOrg = new UserOrgDTO();
        userOrg.setGuid("9d51a484-e3c4-4006-9e4d-e9a4b1c21c1f");
        userOrg.setName("name");
        userOrg.setOrgNameTreeJoined("orgNameTreeJoined");
        userDTO.setUserOrg(userOrg);
        final OrgGeneralDTO orgGeneralDTO = new OrgGeneralDTO();
        userDTO.setEntireOrgTree(Arrays.asList(orgGeneralDTO));
        final UserOfficeDTO userOffice = new UserOfficeDTO();
        userOffice.setCode(0);
        userOffice.setName("itemName");
        userDTO.setUserOffice(userOffice);
        userDTO.setIsEnable(false);
        userDTO.setPhoneList(Arrays.asList("value"));
        userDTO.setEnterpriseGuid("enterpriseGuid");
        userDTO.setIntegrateFlag(false);
        userDTO.setErrorMsg("当前账号正在门店使用，不能删除");

        final UserDTO expectedResult = new UserDTO();
        expectedResult.setGuid("userGuid");
        expectedResult.setEnterpriseNo("enterpriseNo");
        expectedResult.setAccount("account");
        expectedResult.setPassword("password");
        expectedResult.setName("name");
        expectedResult.setPhone("phone");
        final RoleDTO roleDTO1 = new RoleDTO();
        expectedResult.setUserRoles(Arrays.asList(roleDTO1));
        expectedResult.setOrgGuid("orgGuid");
        final UserOrgDTO userOrg1 = new UserOrgDTO();
        userOrg1.setGuid("9d51a484-e3c4-4006-9e4d-e9a4b1c21c1f");
        userOrg1.setName("name");
        userOrg1.setOrgNameTreeJoined("orgNameTreeJoined");
        expectedResult.setUserOrg(userOrg1);
        final OrgGeneralDTO orgGeneralDTO1 = new OrgGeneralDTO();
        expectedResult.setEntireOrgTree(Arrays.asList(orgGeneralDTO1));
        final UserOfficeDTO userOffice1 = new UserOfficeDTO();
        userOffice1.setCode(0);
        userOffice1.setName("itemName");
        expectedResult.setUserOffice(userOffice1);
        expectedResult.setIsEnable(false);
        expectedResult.setPhoneList(Arrays.asList("value"));
        expectedResult.setEnterpriseGuid("enterpriseGuid");
        expectedResult.setIntegrateFlag(false);
        expectedResult.setErrorMsg("当前账号正在门店使用，不能删除");

        // Configure UserMapstruct.fromUserDTO(...).
        final UserDO userDO = new UserDO();
        userDO.setId(0L);
        userDO.setGuid("userGuid");
        userDO.setEnterpriseNo("enterpriseNo");
        userDO.setAccount("account");
        userDO.setPassword("password");
        userDO.setAuthCode("authCode");
        userDO.setName("name");
        userDO.setPhone("phone");
        userDO.setOrgGuid("orgGuid");
        userDO.setOfficeCode(0);
        userDO.setOfficeName("itemName");
        userDO.setCreateStaffGuid("createStaffGuid");
        userDO.setUpdateStaffGuid("updateStaffGuid");
        userDO.setIsEnable(false);
        userDO.setIsDeleted(false);
        userDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        userDO.setRegType("regType");
        userDO.setFaceCode("faceCode");
        final UserDTO userDTO1 = new UserDTO();
        userDTO1.setGuid("userGuid");
        userDTO1.setEnterpriseNo("enterpriseNo");
        userDTO1.setAccount("account");
        userDTO1.setPassword("password");
        userDTO1.setName("name");
        userDTO1.setPhone("phone");
        final RoleDTO roleDTO2 = new RoleDTO();
        userDTO1.setUserRoles(Arrays.asList(roleDTO2));
        userDTO1.setOrgGuid("orgGuid");
        final UserOrgDTO userOrg2 = new UserOrgDTO();
        userOrg2.setGuid("9d51a484-e3c4-4006-9e4d-e9a4b1c21c1f");
        userOrg2.setName("name");
        userOrg2.setOrgNameTreeJoined("orgNameTreeJoined");
        userDTO1.setUserOrg(userOrg2);
        final OrgGeneralDTO orgGeneralDTO2 = new OrgGeneralDTO();
        userDTO1.setEntireOrgTree(Arrays.asList(orgGeneralDTO2));
        final UserOfficeDTO userOffice2 = new UserOfficeDTO();
        userOffice2.setCode(0);
        userOffice2.setName("itemName");
        userDTO1.setUserOffice(userOffice2);
        userDTO1.setIsEnable(false);
        userDTO1.setPhoneList(Arrays.asList("value"));
        userDTO1.setEnterpriseGuid("enterpriseGuid");
        userDTO1.setIntegrateFlag(false);
        userDTO1.setErrorMsg("当前账号正在门店使用，不能删除");
        when(mockUserMapstruct.fromUserDTO(userDTO1)).thenReturn(userDO);

        // Configure UserMapstruct.toUserDTO(...).
        final UserDTO userDTO2 = new UserDTO();
        userDTO2.setGuid("userGuid");
        userDTO2.setEnterpriseNo("enterpriseNo");
        userDTO2.setAccount("account");
        userDTO2.setPassword("password");
        userDTO2.setName("name");
        userDTO2.setPhone("phone");
        final RoleDTO roleDTO3 = new RoleDTO();
        userDTO2.setUserRoles(Arrays.asList(roleDTO3));
        userDTO2.setOrgGuid("orgGuid");
        final UserOrgDTO userOrg3 = new UserOrgDTO();
        userOrg3.setGuid("9d51a484-e3c4-4006-9e4d-e9a4b1c21c1f");
        userOrg3.setName("name");
        userOrg3.setOrgNameTreeJoined("orgNameTreeJoined");
        userDTO2.setUserOrg(userOrg3);
        final OrgGeneralDTO orgGeneralDTO3 = new OrgGeneralDTO();
        userDTO2.setEntireOrgTree(Arrays.asList(orgGeneralDTO3));
        final UserOfficeDTO userOffice3 = new UserOfficeDTO();
        userOffice3.setCode(0);
        userOffice3.setName("itemName");
        userDTO2.setUserOffice(userOffice3);
        userDTO2.setIsEnable(false);
        userDTO2.setPhoneList(Arrays.asList("value"));
        userDTO2.setEnterpriseGuid("enterpriseGuid");
        userDTO2.setIntegrateFlag(false);
        userDTO2.setErrorMsg("当前账号正在门店使用，不能删除");
        final UserReadDO userReadDO = new UserReadDO();
        userReadDO.setId(0L);
        userReadDO.setGuid("b93ec5af-5c9d-46a9-871e-3648af2dbf5d");
        userReadDO.setOrgGuid("9d51a484-e3c4-4006-9e4d-e9a4b1c21c1f");
        userReadDO.setOfficeCode(0);
        userReadDO.setOfficeName("itemName");
        when(mockUserMapstruct.toUserDTO(userReadDO)).thenReturn(userDTO2);

        when(mockOrgFeignClient.queryOrgWithParentByIdList(Arrays.asList("value"))).thenReturn(new HashMap<>());
        when(mockOrgFeignClient.queryErpOrgStore(1, 1)).thenReturn(Collections.emptyList());

        // Run the test
        final UserDTO result = userServiceImplUnderTest.query(userDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testValidate() {
        // Setup
        final ValidateDTO validateDTO = new ValidateDTO("storeNo", 0, false);

        // Configure OrgFeignClient.findDeviceStatus(...).
        final StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO();
        storeDeviceDTO.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO.setStoreNo("code");
        storeDeviceDTO.setStoreGuid("guid");
        storeDeviceDTO.setStoreName("name");
        storeDeviceDTO.setDeviceNo("deviceId");
        storeDeviceDTO.setDeviceGuid("deviceGuid");
        storeDeviceDTO.setBinding(false);
        storeDeviceDTO.setRegister(false);
        storeDeviceDTO.setDeviceType(0);
        storeDeviceDTO.setDeviceName("deviceName");
        storeDeviceDTO.setSort(0);
        storeDeviceDTO.setCreateUserGuid("createUserGuid");
        storeDeviceDTO.setCreateUserName("createUserName");
        storeDeviceDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockOrgFeignClient.findDeviceStatus("deviceId")).thenReturn(storeDeviceDTO);

        // Configure OrgFeignClient.queryStoreByCode(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setIsEnable(false);
        when(mockOrgFeignClient.queryStoreByCode("storeNo")).thenReturn(storeDTO);

        when(mockProductService.queryProductByIdList(Arrays.asList("value"))).thenReturn(new HashMap<>());
        when(mockUserDataService.queryAllStoreGuid()).thenReturn(Arrays.asList("value"));

        // Configure BusinessClient.queryByStoreGuidAndUserGuid(...).
        final HandoverRecordDTO handoverRecordDTO = new HandoverRecordDTO();
        handoverRecordDTO.setStoreGuid("storeGuid");
        handoverRecordDTO.setStoreName("storeName");
        handoverRecordDTO.setTerminalId("terminalId");
        handoverRecordDTO.setCreateGuid("createGuid");
        handoverRecordDTO.setStatus(0);
        when(mockBusinessClient.queryByStoreGuidAndUserGuid("guid", "userGuid")).thenReturn(handoverRecordDTO);

        // Configure EnterpriseClient.queryManagementType(...).
        final BaseDictionaryDTO baseDictionaryDTO = new BaseDictionaryDTO();
        baseDictionaryDTO.setGuid("ac583272-3413-458d-befa-246cc045a721");
        baseDictionaryDTO.setValue("value");
        baseDictionaryDTO.setType("type");
        baseDictionaryDTO.setStaffGuid("staffGuid");
        baseDictionaryDTO.setCode("code");
        when(mockEnterpriseClient.queryManagementType("enterpriseGuid")).thenReturn(baseDictionaryDTO);

        // Run the test
        final ValidateRespDTO result = userServiceImplUnderTest.validate(validateDTO);

        // Verify the results
        // Confirm OrgFeignClient.bindDeviceStatus(...).
        final StoreDeviceDTO storeDeviceDTO1 = new StoreDeviceDTO();
        storeDeviceDTO1.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO1.setStoreNo("code");
        storeDeviceDTO1.setStoreGuid("guid");
        storeDeviceDTO1.setStoreName("name");
        storeDeviceDTO1.setDeviceNo("deviceId");
        storeDeviceDTO1.setDeviceGuid("deviceGuid");
        storeDeviceDTO1.setBinding(false);
        storeDeviceDTO1.setRegister(false);
        storeDeviceDTO1.setDeviceType(0);
        storeDeviceDTO1.setDeviceName("deviceName");
        storeDeviceDTO1.setSort(0);
        storeDeviceDTO1.setCreateUserGuid("createUserGuid");
        storeDeviceDTO1.setCreateUserName("createUserName");
        storeDeviceDTO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        verify(mockOrgFeignClient).bindDeviceStatus(storeDeviceDTO1);
    }

    @Test
    public void testValidate_OrgFeignClientQueryStoreByCodeReturnsNull() {
        // Setup
        final ValidateDTO validateDTO = new ValidateDTO("storeNo", 0, false);

        // Configure OrgFeignClient.findDeviceStatus(...).
        final StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO();
        storeDeviceDTO.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO.setStoreNo("code");
        storeDeviceDTO.setStoreGuid("guid");
        storeDeviceDTO.setStoreName("name");
        storeDeviceDTO.setDeviceNo("deviceId");
        storeDeviceDTO.setDeviceGuid("deviceGuid");
        storeDeviceDTO.setBinding(false);
        storeDeviceDTO.setRegister(false);
        storeDeviceDTO.setDeviceType(0);
        storeDeviceDTO.setDeviceName("deviceName");
        storeDeviceDTO.setSort(0);
        storeDeviceDTO.setCreateUserGuid("createUserGuid");
        storeDeviceDTO.setCreateUserName("createUserName");
        storeDeviceDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockOrgFeignClient.findDeviceStatus("deviceId")).thenReturn(storeDeviceDTO);

        when(mockOrgFeignClient.queryStoreByCode("storeNo")).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> userServiceImplUnderTest.validate(validateDTO)).isInstanceOf(BusinessException.class);
    }

    @Test
    public void testValidate_UserDataServiceReturnsNoItems() {
        // Setup
        final ValidateDTO validateDTO = new ValidateDTO("storeNo", 0, false);

        // Configure OrgFeignClient.findDeviceStatus(...).
        final StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO();
        storeDeviceDTO.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO.setStoreNo("code");
        storeDeviceDTO.setStoreGuid("guid");
        storeDeviceDTO.setStoreName("name");
        storeDeviceDTO.setDeviceNo("deviceId");
        storeDeviceDTO.setDeviceGuid("deviceGuid");
        storeDeviceDTO.setBinding(false);
        storeDeviceDTO.setRegister(false);
        storeDeviceDTO.setDeviceType(0);
        storeDeviceDTO.setDeviceName("deviceName");
        storeDeviceDTO.setSort(0);
        storeDeviceDTO.setCreateUserGuid("createUserGuid");
        storeDeviceDTO.setCreateUserName("createUserName");
        storeDeviceDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockOrgFeignClient.findDeviceStatus("deviceId")).thenReturn(storeDeviceDTO);

        // Configure OrgFeignClient.queryStoreByCode(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setIsEnable(false);
        when(mockOrgFeignClient.queryStoreByCode("storeNo")).thenReturn(storeDTO);

        when(mockProductService.queryProductByIdList(Arrays.asList("value"))).thenReturn(new HashMap<>());
        when(mockUserDataService.queryAllStoreGuid()).thenReturn(Collections.emptyList());

        // Run the test
        assertThatThrownBy(() -> userServiceImplUnderTest.validate(validateDTO)).isInstanceOf(BusinessException.class);
    }

    @Test
    public void testValidate_BusinessClientQueryByStoreGuidAndUserGuidReturnsNull() {
        // Setup
        final ValidateDTO validateDTO = new ValidateDTO("storeNo", 0, false);

        // Configure OrgFeignClient.findDeviceStatus(...).
        final StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO();
        storeDeviceDTO.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO.setStoreNo("code");
        storeDeviceDTO.setStoreGuid("guid");
        storeDeviceDTO.setStoreName("name");
        storeDeviceDTO.setDeviceNo("deviceId");
        storeDeviceDTO.setDeviceGuid("deviceGuid");
        storeDeviceDTO.setBinding(false);
        storeDeviceDTO.setRegister(false);
        storeDeviceDTO.setDeviceType(0);
        storeDeviceDTO.setDeviceName("deviceName");
        storeDeviceDTO.setSort(0);
        storeDeviceDTO.setCreateUserGuid("createUserGuid");
        storeDeviceDTO.setCreateUserName("createUserName");
        storeDeviceDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockOrgFeignClient.findDeviceStatus("deviceId")).thenReturn(storeDeviceDTO);

        // Configure OrgFeignClient.queryStoreByCode(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setIsEnable(false);
        when(mockOrgFeignClient.queryStoreByCode("storeNo")).thenReturn(storeDTO);

        when(mockProductService.queryProductByIdList(Arrays.asList("value"))).thenReturn(new HashMap<>());
        when(mockUserDataService.queryAllStoreGuid()).thenReturn(Arrays.asList("value"));
        when(mockBusinessClient.queryByStoreGuidAndUserGuid("guid", "userGuid")).thenReturn(null);

        // Configure EnterpriseClient.queryManagementType(...).
        final BaseDictionaryDTO baseDictionaryDTO = new BaseDictionaryDTO();
        baseDictionaryDTO.setGuid("ac583272-3413-458d-befa-246cc045a721");
        baseDictionaryDTO.setValue("value");
        baseDictionaryDTO.setType("type");
        baseDictionaryDTO.setStaffGuid("staffGuid");
        baseDictionaryDTO.setCode("code");
        when(mockEnterpriseClient.queryManagementType("enterpriseGuid")).thenReturn(baseDictionaryDTO);

        // Run the test
        final ValidateRespDTO result = userServiceImplUnderTest.validate(validateDTO);

        // Verify the results
        // Confirm OrgFeignClient.bindDeviceStatus(...).
        final StoreDeviceDTO storeDeviceDTO1 = new StoreDeviceDTO();
        storeDeviceDTO1.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO1.setStoreNo("code");
        storeDeviceDTO1.setStoreGuid("guid");
        storeDeviceDTO1.setStoreName("name");
        storeDeviceDTO1.setDeviceNo("deviceId");
        storeDeviceDTO1.setDeviceGuid("deviceGuid");
        storeDeviceDTO1.setBinding(false);
        storeDeviceDTO1.setRegister(false);
        storeDeviceDTO1.setDeviceType(0);
        storeDeviceDTO1.setDeviceName("deviceName");
        storeDeviceDTO1.setSort(0);
        storeDeviceDTO1.setCreateUserGuid("createUserGuid");
        storeDeviceDTO1.setCreateUserName("createUserName");
        storeDeviceDTO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        verify(mockOrgFeignClient).bindDeviceStatus(storeDeviceDTO1);
        verify(mockBusinessClient).create(
                new HandoverRecordCreateDTO("storeGuid", "storeName", "createUserGuid", "createUserName",
                        "terminalId"));
    }

    @Test
    public void testCheckUserAuthentication() {
        // Setup
        final AuthDTO authDTO = new AuthDTO();
        authDTO.setEnterpriseGuid("enterpriseGuid");
        authDTO.setUserGuid("userGuid");
        authDTO.setUserAccount("userAccount");
        authDTO.setTerminalCode("terminalCode");
        authDTO.setMenuGuid("menuGuid");
        authDTO.setRequestUri("requestUri");

        // Configure MenuMapper.selectOne(...).
        final MenuDO menuDO = new MenuDO();
        menuDO.setMenuGuid("menuGuid");
        menuDO.setMenuName("menuName");
        menuDO.setMenuUrl("menuUrl");
        menuDO.setModuleGuid("moduleGuid");
        menuDO.setIsEnable(false);
        when(mockMenuMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(menuDO);

        // Configure StoreSourceMapper.queryModuleType(...).
        final StoreSourceDO storeSourceDO = new StoreSourceDO();
        storeSourceDO.setId(0L);
        storeSourceDO.setStoreGuid("storeGuid");
        storeSourceDO.setProductGuid("productGuid");
        storeSourceDO.setChargeGuid("chargeGuid");
        storeSourceDO.setModuleType("moduleType");
        final List<StoreSourceDO> storeSourceDOS = Arrays.asList(storeSourceDO);
        final ModuleTypeQuery moduleTypeQuery = new ModuleTypeQuery();
        moduleTypeQuery.setModuleGuid("moduleGuid");
        moduleTypeQuery.setRequestUri("requestUri");
        when(mockStoreSourceMapper.queryModuleType(moduleTypeQuery)).thenReturn(storeSourceDOS);

        // Run the test
        final boolean result = userServiceImplUnderTest.checkUserAuthentication(authDTO);

        // Verify the results
        assertThat(result).isFalse();
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
        verify(mockDynamicHelper).changeRedis("enterpriseGuid");
        verify(mockDynamicHelper).clear();
    }

    @Test
    public void testCheckUserAuthentication_MenuMapperReturnsNull() {
        // Setup
        final AuthDTO authDTO = new AuthDTO();
        authDTO.setEnterpriseGuid("enterpriseGuid");
        authDTO.setUserGuid("userGuid");
        authDTO.setUserAccount("userAccount");
        authDTO.setTerminalCode("terminalCode");
        authDTO.setMenuGuid("menuGuid");
        authDTO.setRequestUri("requestUri");

        when(mockMenuMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Run the test
        final boolean result = userServiceImplUnderTest.checkUserAuthentication(authDTO);

        // Verify the results
        assertThat(result).isFalse();
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
        verify(mockDynamicHelper).changeRedis("enterpriseGuid");
        verify(mockDynamicHelper).clear();
    }

    @Test
    public void testCheckUserAuthentication_StoreSourceMapperReturnsNoItems() {
        // Setup
        final AuthDTO authDTO = new AuthDTO();
        authDTO.setEnterpriseGuid("enterpriseGuid");
        authDTO.setUserGuid("userGuid");
        authDTO.setUserAccount("userAccount");
        authDTO.setTerminalCode("terminalCode");
        authDTO.setMenuGuid("menuGuid");
        authDTO.setRequestUri("requestUri");

        // Configure MenuMapper.selectOne(...).
        final MenuDO menuDO = new MenuDO();
        menuDO.setMenuGuid("menuGuid");
        menuDO.setMenuName("menuName");
        menuDO.setMenuUrl("menuUrl");
        menuDO.setModuleGuid("moduleGuid");
        menuDO.setIsEnable(false);
        when(mockMenuMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(menuDO);

        // Configure StoreSourceMapper.queryModuleType(...).
        final ModuleTypeQuery moduleTypeQuery = new ModuleTypeQuery();
        moduleTypeQuery.setModuleGuid("moduleGuid");
        moduleTypeQuery.setRequestUri("requestUri");
        when(mockStoreSourceMapper.queryModuleType(moduleTypeQuery)).thenReturn(Collections.emptyList());

        // Run the test
        final boolean result = userServiceImplUnderTest.checkUserAuthentication(authDTO);

        // Verify the results
        assertThat(result).isFalse();
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
        verify(mockDynamicHelper).changeRedis("enterpriseGuid");
        verify(mockDynamicHelper).clear();
    }

    @Test
    public void testListUser() {
        // Setup
        final UserDTO userDTO = new UserDTO();
        userDTO.setGuid("userGuid");
        userDTO.setEnterpriseNo("enterpriseNo");
        userDTO.setAccount("account");
        userDTO.setPassword("password");
        userDTO.setName("name");
        userDTO.setPhone("phone");
        final RoleDTO roleDTO = new RoleDTO();
        userDTO.setUserRoles(Arrays.asList(roleDTO));
        userDTO.setOrgGuid("orgGuid");
        final UserOrgDTO userOrg = new UserOrgDTO();
        userOrg.setGuid("9d51a484-e3c4-4006-9e4d-e9a4b1c21c1f");
        userOrg.setName("name");
        userOrg.setOrgNameTreeJoined("orgNameTreeJoined");
        userDTO.setUserOrg(userOrg);
        final OrgGeneralDTO orgGeneralDTO = new OrgGeneralDTO();
        userDTO.setEntireOrgTree(Arrays.asList(orgGeneralDTO));
        final UserOfficeDTO userOffice = new UserOfficeDTO();
        userOffice.setCode(0);
        userOffice.setName("itemName");
        userDTO.setUserOffice(userOffice);
        userDTO.setIsEnable(false);
        userDTO.setPhoneList(Arrays.asList("value"));
        userDTO.setEnterpriseGuid("enterpriseGuid");
        userDTO.setIntegrateFlag(false);
        userDTO.setErrorMsg("当前账号正在门店使用，不能删除");
        final List<UserDTO> expectedResult = Arrays.asList(userDTO);

        // Configure UserMapstruct.toUserDTO(...).
        final UserDTO userDTO1 = new UserDTO();
        userDTO1.setGuid("userGuid");
        userDTO1.setEnterpriseNo("enterpriseNo");
        userDTO1.setAccount("account");
        userDTO1.setPassword("password");
        userDTO1.setName("name");
        userDTO1.setPhone("phone");
        final RoleDTO roleDTO1 = new RoleDTO();
        userDTO1.setUserRoles(Arrays.asList(roleDTO1));
        userDTO1.setOrgGuid("orgGuid");
        final UserOrgDTO userOrg1 = new UserOrgDTO();
        userOrg1.setGuid("9d51a484-e3c4-4006-9e4d-e9a4b1c21c1f");
        userOrg1.setName("name");
        userOrg1.setOrgNameTreeJoined("orgNameTreeJoined");
        userDTO1.setUserOrg(userOrg1);
        final OrgGeneralDTO orgGeneralDTO1 = new OrgGeneralDTO();
        userDTO1.setEntireOrgTree(Arrays.asList(orgGeneralDTO1));
        final UserOfficeDTO userOffice1 = new UserOfficeDTO();
        userOffice1.setCode(0);
        userOffice1.setName("itemName");
        userDTO1.setUserOffice(userOffice1);
        userDTO1.setIsEnable(false);
        userDTO1.setPhoneList(Arrays.asList("value"));
        userDTO1.setEnterpriseGuid("enterpriseGuid");
        userDTO1.setIntegrateFlag(false);
        userDTO1.setErrorMsg("当前账号正在门店使用，不能删除");
        final List<UserDTO> userDTOS = Arrays.asList(userDTO1);
        final UserReadDO userReadDO1 = new UserReadDO();
        userReadDO1.setId(0L);
        userReadDO1.setGuid("b93ec5af-5c9d-46a9-871e-3648af2dbf5d");
        userReadDO1.setOrgGuid("9d51a484-e3c4-4006-9e4d-e9a4b1c21c1f");
        userReadDO1.setOfficeCode(0);
        userReadDO1.setOfficeName("itemName");
        final List<UserReadDO> userReadDO = Arrays.asList(userReadDO1);
        when(mockUserMapstruct.toUserDTO(userReadDO)).thenReturn(userDTOS);

        // Run the test
        final List<UserDTO> result = userServiceImplUnderTest.listUser(Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testListUser_UserMapstructReturnsNoItems() {
        // Setup
        // Configure UserMapstruct.toUserDTO(...).
        final UserReadDO userReadDO1 = new UserReadDO();
        userReadDO1.setId(0L);
        userReadDO1.setGuid("b93ec5af-5c9d-46a9-871e-3648af2dbf5d");
        userReadDO1.setOrgGuid("9d51a484-e3c4-4006-9e4d-e9a4b1c21c1f");
        userReadDO1.setOfficeCode(0);
        userReadDO1.setOfficeName("itemName");
        final List<UserReadDO> userReadDO = Arrays.asList(userReadDO1);
        when(mockUserMapstruct.toUserDTO(userReadDO)).thenReturn(Collections.emptyList());

        // Run the test
        final List<UserDTO> result = userServiceImplUnderTest.listUser(Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testDeleteUserDownStreamOp() {
        // Setup
        // Run the test
        final boolean result = userServiceImplUnderTest.deleteUserDownStreamOp("userGuid");

        // Verify the results
        assertThat(result).isTrue();
        verify(mockUserRoleService).deleteUserRoleRelation("userGuid");
        verify(mockUserDataService).deleteUserDataRules("userGuid");
        verify(mockUserUploadService).deleteUser("userGuid");
    }

    @Test
    public void testFindByStoreGuid() {
        // Setup
        final UserQueryDTO userQueryDTO = new UserQueryDTO();
        userQueryDTO.setGenericOrgGuids(Arrays.asList("value"));
        userQueryDTO.setSearchKey("searchKey");
        userQueryDTO.setCreateStaffGuid("createStaffGuid");
        userQueryDTO.setShowPassword(0);
        userQueryDTO.setDeviceTypeCode(0);

        final UserDTO userDTO = new UserDTO();
        userDTO.setGuid("userGuid");
        userDTO.setEnterpriseNo("enterpriseNo");
        userDTO.setAccount("account");
        userDTO.setPassword("password");
        userDTO.setName("name");
        userDTO.setPhone("phone");
        final RoleDTO roleDTO = new RoleDTO();
        userDTO.setUserRoles(Arrays.asList(roleDTO));
        userDTO.setOrgGuid("orgGuid");
        final UserOrgDTO userOrg = new UserOrgDTO();
        userOrg.setGuid("9d51a484-e3c4-4006-9e4d-e9a4b1c21c1f");
        userOrg.setName("name");
        userOrg.setOrgNameTreeJoined("orgNameTreeJoined");
        userDTO.setUserOrg(userOrg);
        final OrgGeneralDTO orgGeneralDTO = new OrgGeneralDTO();
        userDTO.setEntireOrgTree(Arrays.asList(orgGeneralDTO));
        final UserOfficeDTO userOffice = new UserOfficeDTO();
        userOffice.setCode(0);
        userOffice.setName("itemName");
        userDTO.setUserOffice(userOffice);
        userDTO.setIsEnable(false);
        userDTO.setPhoneList(Arrays.asList("value"));
        userDTO.setEnterpriseGuid("enterpriseGuid");
        userDTO.setIntegrateFlag(false);
        userDTO.setErrorMsg("当前账号正在门店使用，不能删除");
        final List<UserDTO> expectedResult = Arrays.asList(userDTO);

        // Configure UserMapstruct.toUserDTO(...).
        final UserDTO userDTO1 = new UserDTO();
        userDTO1.setGuid("userGuid");
        userDTO1.setEnterpriseNo("enterpriseNo");
        userDTO1.setAccount("account");
        userDTO1.setPassword("password");
        userDTO1.setName("name");
        userDTO1.setPhone("phone");
        final RoleDTO roleDTO1 = new RoleDTO();
        userDTO1.setUserRoles(Arrays.asList(roleDTO1));
        userDTO1.setOrgGuid("orgGuid");
        final UserOrgDTO userOrg1 = new UserOrgDTO();
        userOrg1.setGuid("9d51a484-e3c4-4006-9e4d-e9a4b1c21c1f");
        userOrg1.setName("name");
        userOrg1.setOrgNameTreeJoined("orgNameTreeJoined");
        userDTO1.setUserOrg(userOrg1);
        final OrgGeneralDTO orgGeneralDTO1 = new OrgGeneralDTO();
        userDTO1.setEntireOrgTree(Arrays.asList(orgGeneralDTO1));
        final UserOfficeDTO userOffice1 = new UserOfficeDTO();
        userOffice1.setCode(0);
        userOffice1.setName("itemName");
        userDTO1.setUserOffice(userOffice1);
        userDTO1.setIsEnable(false);
        userDTO1.setPhoneList(Arrays.asList("value"));
        userDTO1.setEnterpriseGuid("enterpriseGuid");
        userDTO1.setIntegrateFlag(false);
        userDTO1.setErrorMsg("当前账号正在门店使用，不能删除");
        final List<UserDTO> userDTOS = Arrays.asList(userDTO1);
        final UserReadDO userReadDO1 = new UserReadDO();
        userReadDO1.setId(0L);
        userReadDO1.setGuid("b93ec5af-5c9d-46a9-871e-3648af2dbf5d");
        userReadDO1.setOrgGuid("9d51a484-e3c4-4006-9e4d-e9a4b1c21c1f");
        userReadDO1.setOfficeCode(0);
        userReadDO1.setOfficeName("itemName");
        final List<UserReadDO> userReadDO = Arrays.asList(userReadDO1);
        when(mockUserMapstruct.toUserDTO(userReadDO)).thenReturn(userDTOS);

        // Run the test
        final List<UserDTO> result = userServiceImplUnderTest.findByStoreGuid(userQueryDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testFindByStoreGuid_UserMapstructReturnsNoItems() {
        // Setup
        final UserQueryDTO userQueryDTO = new UserQueryDTO();
        userQueryDTO.setGenericOrgGuids(Arrays.asList("value"));
        userQueryDTO.setSearchKey("searchKey");
        userQueryDTO.setCreateStaffGuid("createStaffGuid");
        userQueryDTO.setShowPassword(0);
        userQueryDTO.setDeviceTypeCode(0);

        // Configure UserMapstruct.toUserDTO(...).
        final UserReadDO userReadDO1 = new UserReadDO();
        userReadDO1.setId(0L);
        userReadDO1.setGuid("b93ec5af-5c9d-46a9-871e-3648af2dbf5d");
        userReadDO1.setOrgGuid("9d51a484-e3c4-4006-9e4d-e9a4b1c21c1f");
        userReadDO1.setOfficeCode(0);
        userReadDO1.setOfficeName("itemName");
        final List<UserReadDO> userReadDO = Arrays.asList(userReadDO1);
        when(mockUserMapstruct.toUserDTO(userReadDO)).thenReturn(Collections.emptyList());

        // Run the test
        final List<UserDTO> result = userServiceImplUnderTest.findByStoreGuid(userQueryDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testFindAll() {
        // Setup
        final UserDO userDO = new UserDO();
        userDO.setId(0L);
        userDO.setGuid("userGuid");
        userDO.setEnterpriseNo("enterpriseNo");
        userDO.setAccount("account");
        userDO.setPassword("password");
        userDO.setAuthCode("authCode");
        userDO.setName("name");
        userDO.setPhone("phone");
        userDO.setOrgGuid("orgGuid");
        userDO.setOfficeCode(0);
        userDO.setOfficeName("itemName");
        userDO.setCreateStaffGuid("createStaffGuid");
        userDO.setUpdateStaffGuid("updateStaffGuid");
        userDO.setIsEnable(false);
        userDO.setIsDeleted(false);
        userDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        userDO.setRegType("regType");
        userDO.setFaceCode("faceCode");
        final List<UserDO> expectedResult = Arrays.asList(userDO);

        // Run the test
        final List<UserDO> result = userServiceImplUnderTest.findAll();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testFindHolderUserByOrganizationIds() {
        // Setup
        final HolderUserDTO holderUserDTO = new HolderUserDTO();
        holderUserDTO.setUserId(0L);
        holderUserDTO.setUsername("username");
        holderUserDTO.setPassword("password");
        holderUserDTO.setAccount("account");
        holderUserDTO.setDeptId(0L);
        final List<HolderUserDTO> expectedResult = Arrays.asList(holderUserDTO);

        // Run the test
        final List<HolderUserDTO> result = userServiceImplUnderTest.findHolderUserByOrganizationIds(
                Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testBatchDelete() {
        // Setup
        final UserDTO userDTO = new UserDTO();
        userDTO.setGuid("userGuid");
        userDTO.setEnterpriseNo("enterpriseNo");
        userDTO.setAccount("account");
        userDTO.setPassword("password");
        userDTO.setName("name");
        userDTO.setPhone("phone");
        final RoleDTO roleDTO = new RoleDTO();
        userDTO.setUserRoles(Arrays.asList(roleDTO));
        userDTO.setOrgGuid("orgGuid");
        final UserOrgDTO userOrg = new UserOrgDTO();
        userOrg.setGuid("9d51a484-e3c4-4006-9e4d-e9a4b1c21c1f");
        userOrg.setName("name");
        userOrg.setOrgNameTreeJoined("orgNameTreeJoined");
        userDTO.setUserOrg(userOrg);
        final OrgGeneralDTO orgGeneralDTO = new OrgGeneralDTO();
        userDTO.setEntireOrgTree(Arrays.asList(orgGeneralDTO));
        final UserOfficeDTO userOffice = new UserOfficeDTO();
        userOffice.setCode(0);
        userOffice.setName("itemName");
        userDTO.setUserOffice(userOffice);
        userDTO.setIsEnable(false);
        userDTO.setPhoneList(Arrays.asList("value"));
        userDTO.setEnterpriseGuid("enterpriseGuid");
        userDTO.setIntegrateFlag(false);
        userDTO.setErrorMsg("当前账号正在门店使用，不能删除");

        final UserDTO userDTO1 = new UserDTO();
        userDTO1.setGuid("userGuid");
        userDTO1.setEnterpriseNo("enterpriseNo");
        userDTO1.setAccount("account");
        userDTO1.setPassword("password");
        userDTO1.setName("name");
        userDTO1.setPhone("phone");
        final RoleDTO roleDTO1 = new RoleDTO();
        userDTO1.setUserRoles(Arrays.asList(roleDTO1));
        userDTO1.setOrgGuid("orgGuid");
        final UserOrgDTO userOrg1 = new UserOrgDTO();
        userOrg1.setGuid("9d51a484-e3c4-4006-9e4d-e9a4b1c21c1f");
        userOrg1.setName("name");
        userOrg1.setOrgNameTreeJoined("orgNameTreeJoined");
        userDTO1.setUserOrg(userOrg1);
        final OrgGeneralDTO orgGeneralDTO1 = new OrgGeneralDTO();
        userDTO1.setEntireOrgTree(Arrays.asList(orgGeneralDTO1));
        final UserOfficeDTO userOffice1 = new UserOfficeDTO();
        userOffice1.setCode(0);
        userOffice1.setName("itemName");
        userDTO1.setUserOffice(userOffice1);
        userDTO1.setIsEnable(false);
        userDTO1.setPhoneList(Arrays.asList("value"));
        userDTO1.setEnterpriseGuid("enterpriseGuid");
        userDTO1.setIntegrateFlag(false);
        userDTO1.setErrorMsg("当前账号正在门店使用，不能删除");
        final List<UserDTO> expectedResult = Arrays.asList(userDTO1);

        // Configure RoleMapper.selectList(...).
        final RoleDO roleDO = new RoleDO();
        roleDO.setId(0L);
        roleDO.setGuid("313498ad-b30f-4b17-88f1-f2dc872deeec");
        roleDO.setName("name");
        roleDO.setIsEnable(false);
        roleDO.setCreateStaffGuid("createStaffGuid");
        final List<RoleDO> roleDOS = Arrays.asList(roleDO);
        when(mockRoleMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(roleDOS);

        // Configure BusinessClient.listByUserGuid(...).
        final HandoverRecordDTO handoverRecordDTO = new HandoverRecordDTO();
        handoverRecordDTO.setStoreGuid("storeGuid");
        handoverRecordDTO.setStoreName("storeName");
        handoverRecordDTO.setTerminalId("terminalId");
        handoverRecordDTO.setCreateGuid("createGuid");
        handoverRecordDTO.setStatus(0);
        final List<HandoverRecordDTO> handoverRecordDTOS = Arrays.asList(handoverRecordDTO);
        when(mockBusinessClient.listByUserGuid(
                new HandoverRecordConfirmDTO("terminalId", new BigDecimal("0.00"), Arrays.asList("value"),
                        0))).thenReturn(handoverRecordDTOS);

        // Run the test
        final List<UserDTO> result = userServiceImplUnderTest.batchDelete(userDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockUserRoleService).batchDeleteUserRoleRelation(Arrays.asList("value"));
        verify(mockUserDataService).batchDeleteUserDataRules(Arrays.asList("value"));
    }

    @Test
    public void testBatchDelete_RoleMapperReturnsNoItems() {
        // Setup
        final UserDTO userDTO = new UserDTO();
        userDTO.setGuid("userGuid");
        userDTO.setEnterpriseNo("enterpriseNo");
        userDTO.setAccount("account");
        userDTO.setPassword("password");
        userDTO.setName("name");
        userDTO.setPhone("phone");
        final RoleDTO roleDTO = new RoleDTO();
        userDTO.setUserRoles(Arrays.asList(roleDTO));
        userDTO.setOrgGuid("orgGuid");
        final UserOrgDTO userOrg = new UserOrgDTO();
        userOrg.setGuid("9d51a484-e3c4-4006-9e4d-e9a4b1c21c1f");
        userOrg.setName("name");
        userOrg.setOrgNameTreeJoined("orgNameTreeJoined");
        userDTO.setUserOrg(userOrg);
        final OrgGeneralDTO orgGeneralDTO = new OrgGeneralDTO();
        userDTO.setEntireOrgTree(Arrays.asList(orgGeneralDTO));
        final UserOfficeDTO userOffice = new UserOfficeDTO();
        userOffice.setCode(0);
        userOffice.setName("itemName");
        userDTO.setUserOffice(userOffice);
        userDTO.setIsEnable(false);
        userDTO.setPhoneList(Arrays.asList("value"));
        userDTO.setEnterpriseGuid("enterpriseGuid");
        userDTO.setIntegrateFlag(false);
        userDTO.setErrorMsg("当前账号正在门店使用，不能删除");

        final UserDTO userDTO1 = new UserDTO();
        userDTO1.setGuid("userGuid");
        userDTO1.setEnterpriseNo("enterpriseNo");
        userDTO1.setAccount("account");
        userDTO1.setPassword("password");
        userDTO1.setName("name");
        userDTO1.setPhone("phone");
        final RoleDTO roleDTO1 = new RoleDTO();
        userDTO1.setUserRoles(Arrays.asList(roleDTO1));
        userDTO1.setOrgGuid("orgGuid");
        final UserOrgDTO userOrg1 = new UserOrgDTO();
        userOrg1.setGuid("9d51a484-e3c4-4006-9e4d-e9a4b1c21c1f");
        userOrg1.setName("name");
        userOrg1.setOrgNameTreeJoined("orgNameTreeJoined");
        userDTO1.setUserOrg(userOrg1);
        final OrgGeneralDTO orgGeneralDTO1 = new OrgGeneralDTO();
        userDTO1.setEntireOrgTree(Arrays.asList(orgGeneralDTO1));
        final UserOfficeDTO userOffice1 = new UserOfficeDTO();
        userOffice1.setCode(0);
        userOffice1.setName("itemName");
        userDTO1.setUserOffice(userOffice1);
        userDTO1.setIsEnable(false);
        userDTO1.setPhoneList(Arrays.asList("value"));
        userDTO1.setEnterpriseGuid("enterpriseGuid");
        userDTO1.setIntegrateFlag(false);
        userDTO1.setErrorMsg("当前账号正在门店使用，不能删除");
        final List<UserDTO> expectedResult = Arrays.asList(userDTO1);
        when(mockRoleMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure BusinessClient.listByUserGuid(...).
        final HandoverRecordDTO handoverRecordDTO = new HandoverRecordDTO();
        handoverRecordDTO.setStoreGuid("storeGuid");
        handoverRecordDTO.setStoreName("storeName");
        handoverRecordDTO.setTerminalId("terminalId");
        handoverRecordDTO.setCreateGuid("createGuid");
        handoverRecordDTO.setStatus(0);
        final List<HandoverRecordDTO> handoverRecordDTOS = Arrays.asList(handoverRecordDTO);
        when(mockBusinessClient.listByUserGuid(
                new HandoverRecordConfirmDTO("terminalId", new BigDecimal("0.00"), Arrays.asList("value"),
                        0))).thenReturn(handoverRecordDTOS);

        // Run the test
        final List<UserDTO> result = userServiceImplUnderTest.batchDelete(userDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockUserRoleService).batchDeleteUserRoleRelation(Arrays.asList("value"));
        verify(mockUserDataService).batchDeleteUserDataRules(Arrays.asList("value"));
    }

    @Test
    public void testBatchDelete_BusinessClientReturnsNoItems() {
        // Setup
        final UserDTO userDTO = new UserDTO();
        userDTO.setGuid("userGuid");
        userDTO.setEnterpriseNo("enterpriseNo");
        userDTO.setAccount("account");
        userDTO.setPassword("password");
        userDTO.setName("name");
        userDTO.setPhone("phone");
        final RoleDTO roleDTO = new RoleDTO();
        userDTO.setUserRoles(Arrays.asList(roleDTO));
        userDTO.setOrgGuid("orgGuid");
        final UserOrgDTO userOrg = new UserOrgDTO();
        userOrg.setGuid("9d51a484-e3c4-4006-9e4d-e9a4b1c21c1f");
        userOrg.setName("name");
        userOrg.setOrgNameTreeJoined("orgNameTreeJoined");
        userDTO.setUserOrg(userOrg);
        final OrgGeneralDTO orgGeneralDTO = new OrgGeneralDTO();
        userDTO.setEntireOrgTree(Arrays.asList(orgGeneralDTO));
        final UserOfficeDTO userOffice = new UserOfficeDTO();
        userOffice.setCode(0);
        userOffice.setName("itemName");
        userDTO.setUserOffice(userOffice);
        userDTO.setIsEnable(false);
        userDTO.setPhoneList(Arrays.asList("value"));
        userDTO.setEnterpriseGuid("enterpriseGuid");
        userDTO.setIntegrateFlag(false);
        userDTO.setErrorMsg("当前账号正在门店使用，不能删除");

        // Configure RoleMapper.selectList(...).
        final RoleDO roleDO = new RoleDO();
        roleDO.setId(0L);
        roleDO.setGuid("313498ad-b30f-4b17-88f1-f2dc872deeec");
        roleDO.setName("name");
        roleDO.setIsEnable(false);
        roleDO.setCreateStaffGuid("createStaffGuid");
        final List<RoleDO> roleDOS = Arrays.asList(roleDO);
        when(mockRoleMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(roleDOS);

        when(mockBusinessClient.listByUserGuid(
                new HandoverRecordConfirmDTO("terminalId", new BigDecimal("0.00"), Arrays.asList("value"),
                        0))).thenReturn(Collections.emptyList());

        // Run the test
        final List<UserDTO> result = userServiceImplUnderTest.batchDelete(userDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
        verify(mockUserRoleService).batchDeleteUserRoleRelation(Arrays.asList("value"));
        verify(mockUserDataService).batchDeleteUserDataRules(Arrays.asList("value"));
    }

    @Test
    public void testDeleteByPhone_ThrowsBusinessException() {
        // Setup
        when(mockRoleMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(0);

        // Configure BusinessClient.queryByUserGuid(...).
        final HandoverRecordDTO handoverRecordDTO = new HandoverRecordDTO();
        handoverRecordDTO.setStoreGuid("storeGuid");
        handoverRecordDTO.setStoreName("storeName");
        handoverRecordDTO.setTerminalId("terminalId");
        handoverRecordDTO.setCreateGuid("createGuid");
        handoverRecordDTO.setStatus(0);
        when(mockBusinessClient.queryByUserGuid(
                new HandoverRecordConfirmDTO("terminalId", new BigDecimal("0.00"), Arrays.asList("value"),
                        0))).thenReturn(handoverRecordDTO);

        // Run the test
        assertThatThrownBy(() -> userServiceImplUnderTest.deleteByPhone("phone", false))
                .isInstanceOf(BusinessException.class);
    }

    @Test
    public void testDeleteByPhone_BusinessClientReturnsNull() {
        // Setup
        when(mockRoleMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(0);
        when(mockBusinessClient.queryByUserGuid(
                new HandoverRecordConfirmDTO("terminalId", new BigDecimal("0.00"), Arrays.asList("value"),
                        0))).thenReturn(null);

        // Run the test
        userServiceImplUnderTest.deleteByPhone("phone", false);

        // Verify the results
        verify(mockUserRoleService).deleteUserRoleRelation("userGuid");
        verify(mockUserDataService).deleteUserDataRules("userGuid");
    }

    @Test
    public void testGetUserFaceInfo() {
        // Setup
        final SingleDataDTO dto = new SingleDataDTO("data", Arrays.asList("value"));
        final UserFaceDTO expectedResult = new UserFaceDTO();
        expectedResult.setPhone("phone");
        expectedResult.setUserGuid("userGuid");
        expectedResult.setUserName("name");
        expectedResult.setAccount("account");
        expectedResult.setFaceCode("faceCode");
        expectedResult.setIsInputFace(false);

        // Run the test
        final UserFaceDTO result = userServiceImplUnderTest.getUserFaceInfo(dto);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testInputFace() {
        // Setup
        final UserFaceInputReqDTO reqDTO = new UserFaceInputReqDTO();
        reqDTO.setUserGuid("userGuid");
        reqDTO.setFaceCode("faceCode");

        // Run the test
        userServiceImplUnderTest.inputFace(reqDTO);

        // Verify the results
    }

    @Test
    public void testStoreUsers() {
        // Setup
        final List<UserBriefDTO> expectedResult = Arrays.asList(new UserBriefDTO("userGuid", "name"));

        // Configure UserDataService.list(...).
        final List<UserDataDO> userDataDOS = Arrays.asList(UserDataDO.builder()
                .userGuid("userGuid")
                .storeGuid("storeGuid")
                .build());
        when(mockUserDataService.list(any(LambdaQueryWrapper.class))).thenReturn(userDataDOS);

        // Run the test
        final List<UserBriefDTO> result = userServiceImplUnderTest.storeUsers("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testStoreUsers_UserDataServiceReturnsNoItems() {
        // Setup
        when(mockUserDataService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<UserBriefDTO> result = userServiceImplUnderTest.storeUsers("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }
}
