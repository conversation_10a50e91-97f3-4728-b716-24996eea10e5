package com.holderzone.saas.store.staff.service.impl;

import com.holderzone.resource.common.dto.enterprise.MessageConfigDTO;
import com.holderzone.saas.store.dto.store.store.StoreProductDTO;
import com.holderzone.saas.store.staff.entity.bo.ProductBasicBO;
import com.holderzone.saas.store.staff.entity.domain.ProductDO;
import com.holderzone.saas.store.staff.mapstruct.ProductMapstruct;
import com.holderzone.saas.store.staff.service.StoreSourceService;
import com.holderzone.saas.store.staff.service.remote.EnterpriseClient;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ProductServiceImplTest {

    @Mock
    private ProductMapstruct mockProductMapstruct;
    @Mock
    private StoreSourceService mockStoreSourceService;
    @Mock
    private EnterpriseClient mockEnterpriseClient;

    private ProductServiceImpl productServiceImplUnderTest;

    @Before
    public void setUp() {
        productServiceImplUnderTest = new ProductServiceImpl(mockProductMapstruct, mockStoreSourceService,
                mockEnterpriseClient);
    }

    @Test
    public void testSaveOrUpdate() {
        // Setup
        final ProductBasicBO productBasic = new ProductBasicBO();
        productBasic.setEnterpriseGuid("enterpriseGuid");
        productBasic.setStoreGuid("storeGuid");
        productBasic.setProductGuid("productGuid");
        productBasic.setProductName("productName");
        productBasic.setChargeGuid("chargeGuid");

        // Configure ProductMapstruct.bo2Do(...).
        final ProductDO productDO = new ProductDO();
        productDO.setStoreGuid("storeGuid");
        productDO.setProductGuid("productGuid");
        productDO.setProductName("productName");
        productDO.setProductType(0);
        productDO.setChargeGuid("chargeGuid");
        productDO.setMchntTypeCode("mchntTypeCode");
        productDO.setGmtProductStart(LocalDate.of(2020, 1, 1));
        productDO.setGmtProductEnd(LocalDate.of(2020, 1, 1));
        productDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockProductMapstruct.bo2Do(any(ProductBasicBO.class))).thenReturn(productDO);

        // Run the test
        productServiceImplUnderTest.saveOrUpdate(productBasic);

        // Verify the results
    }

    @Test
    public void testDeleteProduct() {
        // Setup
        final ProductBasicBO productBasic = new ProductBasicBO();
        productBasic.setEnterpriseGuid("enterpriseGuid");
        productBasic.setStoreGuid("storeGuid");
        productBasic.setProductGuid("productGuid");
        productBasic.setProductName("productName");
        productBasic.setChargeGuid("chargeGuid");

        // Run the test
        productServiceImplUnderTest.deleteProduct(productBasic);

        // Verify the results
        verify(mockStoreSourceService).deleteProductAuth("storeGuid", "productGuid", "chargeGuid");
    }

    @Test
    public void testQueryMchntType() {
        assertThat(productServiceImplUnderTest.queryMchntType()).isEqualTo("result");
    }

    @Test
    public void testQueryProductByStoreGuid() {
        // Setup
        // Configure EnterpriseClient.getMessageInfo(...).
        final MessageConfigDTO messageConfigDTO = new MessageConfigDTO();
        messageConfigDTO.setId(0);
        messageConfigDTO.setAppreciateGuid("appreciateGuid");
        messageConfigDTO.setAfterConsume(0);
        messageConfigDTO.setAfterCharge(0);
        messageConfigDTO.setResidueCount(0);
        when(mockEnterpriseClient.getMessageInfo("enterpriseGuid")).thenReturn(messageConfigDTO);

        // Run the test
        final List<StoreProductDTO> result = productServiceImplUnderTest.queryProductByStoreGuid("storeGuid", false);

        // Verify the results
    }

    @Test
    public void testQueryProductByIdList() {
        // Setup
        // Configure EnterpriseClient.getMessageInfo(...).
        final MessageConfigDTO messageConfigDTO = new MessageConfigDTO();
        messageConfigDTO.setId(0);
        messageConfigDTO.setAppreciateGuid("appreciateGuid");
        messageConfigDTO.setAfterConsume(0);
        messageConfigDTO.setAfterCharge(0);
        messageConfigDTO.setResidueCount(0);
        when(mockEnterpriseClient.getMessageInfo("enterpriseGuid")).thenReturn(messageConfigDTO);

        // Run the test
        final Map<String, List<StoreProductDTO>> result = productServiceImplUnderTest.queryProductByIdList(
                Arrays.asList("value"));

        // Verify the results
    }

    @Test
    public void testUpdateExpirationDate() {
        // Setup
        // Run the test
        final Integer result = productServiceImplUnderTest.updateExpirationDate("enterpriseGuid", "storeGuid",
                "endDate", "productGuid");

        // Verify the results
        assertThat(result).isEqualTo(0);
    }
}
