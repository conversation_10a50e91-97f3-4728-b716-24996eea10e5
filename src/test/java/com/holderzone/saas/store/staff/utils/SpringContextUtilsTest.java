package com.holderzone.saas.store.staff.utils;

import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.ConfigurableApplicationContext;

import java.lang.annotation.Annotation;
import java.util.HashMap;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;

public class SpringContextUtilsTest {

    private SpringContextUtils springContextUtilsUnderTest;

    @Before
    public void setUp() throws Exception {
        springContextUtilsUnderTest = SpringContextUtils.getInstance();
    }

    @Test
    public void testGetInstance() {
        // Setup
        // Run the test
        final SpringContextUtils result = SpringContextUtils.getInstance();

        // Verify the results
    }

    @Test
    public void testGetActiveProfile() {
        // Setup
        // Run the test
        final String result = springContextUtilsUnderTest.getActiveProfile();

        // Verify the results
        assertThat(result).isEqualTo("result");
    }

    @Test
    public void testIsDevEnv() {
        // Setup
        // Run the test
        final boolean result = springContextUtilsUnderTest.isDevEnv();

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testIsTestEnv() {
        // Setup
        // Run the test
        final boolean result = springContextUtilsUnderTest.isTestEnv();

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testIsProdEnv() {
        // Setup
        // Run the test
        final boolean result = springContextUtilsUnderTest.isProdEnv();

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testGetBean1() {
        // Setup
        // Run the test
        final String result = springContextUtilsUnderTest.getBean("name");

        // Verify the results
        assertThat(result).isEqualTo("result");
    }

    @Test
    public void testGetBeanName() {
        // Setup
        // Run the test
        final String result = springContextUtilsUnderTest.getBeanName(Object.class);

        // Verify the results
        assertThat(result).isEqualTo("result");
    }

    @Test
    public void testGetBean2() {
        // Setup
        // Run the test
        final String result = springContextUtilsUnderTest.getBean(String.class);

        // Verify the results
        assertThat(result).isEqualTo("result");
    }

    @Test
    public void testExistsBean() {
        // Setup
        // Run the test
        final boolean result = springContextUtilsUnderTest.existsBean(Object.class);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testRegisterBean1() {
        // Setup
        final Map<String, Object> propertys = new HashMap<>();

        // Run the test
        springContextUtilsUnderTest.registerBean("beanName", Object.class, propertys);

        // Verify the results
    }

    @Test
    public void testRegisterBean2() {
        // Setup
        // Run the test
        springContextUtilsUnderTest.registerBean("beanName", "obj");

        // Verify the results
    }

    @Test
    public void testRegisterBean3() {
        // Setup
        final BeanDefinition beanDefinition = null;

        // Run the test
        springContextUtilsUnderTest.registerBean("beanName", beanDefinition);

        // Verify the results
    }

    @Test
    public void testGetBeanWithAnnotation() {
        // Setup
        // Run the test
        final Map<String, Object> result = springContextUtilsUnderTest.getBeanWithAnnotation(Annotation.class);

        // Verify the results
    }

    @Test
    public void testRegisterBean4() {
        // Setup
        // Run the test
        springContextUtilsUnderTest.registerBean("beanName", Object.class);

        // Verify the results
    }

    @Test
    public void testSetCfgContext() {
        final ConfigurableApplicationContext cfgContext = null;
        springContextUtilsUnderTest.setCfgContext(cfgContext);
    }
}
