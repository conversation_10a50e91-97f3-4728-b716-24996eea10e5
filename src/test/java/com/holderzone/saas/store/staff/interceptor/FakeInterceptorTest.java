package com.holderzone.saas.store.staff.interceptor;

import org.junit.Before;
import org.junit.Test;
import org.springframework.http.HttpStatus;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.web.servlet.ModelAndView;

import java.util.HashMap;

import static org.assertj.core.api.Assertions.assertThat;

public class FakeInterceptorTest {

    private FakeInterceptor fakeInterceptorUnderTest;

    @Before
    public void setUp() throws Exception {
        fakeInterceptorUnderTest = new FakeInterceptor();
    }

    @Test
    public void testPreHandle() {
        // Setup
        final MockHttpServletRequest request = new MockHttpServletRequest();
        final MockHttpServletResponse response = new MockHttpServletResponse();

        // Run the test
        final boolean result = fakeInterceptorUnderTest.preHandle(request, response, "handler");

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testPostHandle() {
        fakeInterceptorUnderTest.postHandle(new MockHttpServletRequest(), new MockHttpServletResponse(), "handler",
                new ModelAndView("viewName", new HashMap<>(), HttpStatus.OK));
    }

    @Test
    public void testAfterCompletion() {
        fakeInterceptorUnderTest.afterCompletion(new MockHttpServletRequest(), new MockHttpServletResponse(), "handler",
                new Exception("message"));
    }
}
