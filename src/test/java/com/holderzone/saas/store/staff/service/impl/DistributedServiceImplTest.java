package com.holderzone.saas.store.staff.service.impl;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.Arrays;
import java.util.Collections;

import static org.assertj.core.api.Assertions.assertThat;

@RunWith(MockitoJUnitRunner.class)
public class DistributedServiceImplTest {

    @Mock
    private RedisTemplate mockRedisTemplate;

    private DistributedServiceImpl distributedServiceImplUnderTest;

    @Before
    public void setUp() {
        distributedServiceImplUnderTest = new DistributedServiceImpl(mockRedisTemplate);
    }

    @Test
    public void testNextId() {
        assertThat(distributedServiceImplUnderTest.nextId("tag")).isEqualTo("result");
    }

    @Test
    public void testNextBatchId() {
        assertThat(distributedServiceImplUnderTest.nextBatchId("tag", 0L)).isEqualTo(Arrays.asList("value"));
        assertThat(distributedServiceImplUnderTest.nextBatchId("tag", 0L)).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testNextUserGuid() {
        assertThat(distributedServiceImplUnderTest.nextUserGuid()).isEqualTo("result");
    }

    @Test
    public void testNextBatchUserGuid() {
        assertThat(distributedServiceImplUnderTest.nextBatchUserGuid(0L)).isEqualTo(Arrays.asList("value"));
        assertThat(distributedServiceImplUnderTest.nextBatchUserGuid(0L)).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testNextUserRoleGuid() {
        assertThat(distributedServiceImplUnderTest.nextUserRoleGuid()).isEqualTo("result");
    }

    @Test
    public void testNextBatchUserRoleGuid() {
        assertThat(distributedServiceImplUnderTest.nextBatchUserRoleGuid(0L)).isEqualTo(Arrays.asList("value"));
        assertThat(distributedServiceImplUnderTest.nextBatchUserRoleGuid(0L)).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testNextBatchUserDataGuid() {
        assertThat(distributedServiceImplUnderTest.nextBatchUserDataGuid(0L)).isEqualTo(Arrays.asList("value"));
        assertThat(distributedServiceImplUnderTest.nextBatchUserDataGuid(0L)).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testNextOfficeGuid() {
        assertThat(distributedServiceImplUnderTest.nextOfficeGuid()).isEqualTo("result");
    }

    @Test
    public void testNextAuthorityGuid() {
        assertThat(distributedServiceImplUnderTest.nextAuthorityGuid()).isEqualTo("result");
    }
}
