package com.holderzone.saas.store.staff.service.impl;

import com.holderzone.resource.common.dto.product.ProductThemeSyncDTO;
import com.holderzone.resource.common.dto.product.ThemeSyncDTO;
import com.holderzone.saas.store.staff.entity.domain.ProductThemeDO;
import com.holderzone.saas.store.staff.mapstruct.ProductMapstruct;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Calendar;
import java.util.GregorianCalendar;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ProductThemeServiceImplTest {

    @Mock
    private ProductMapstruct mockProductMapstruct;

    private ProductThemeServiceImpl productThemeServiceImplUnderTest;

    @Before
    public void setUp() {
        productThemeServiceImplUnderTest = new ProductThemeServiceImpl(mockProductMapstruct);
    }

    @Test
    public void testSave() {
        // Setup
        final ProductThemeSyncDTO productThemeSyncDTO = new ProductThemeSyncDTO();
        productThemeSyncDTO.setEnterpriseGuid("enterpriseGuid");
        productThemeSyncDTO.setStoreGuid("storeGuid");
        productThemeSyncDTO.setProductGuid("productGuid");
        final ThemeSyncDTO themeSyncDTO = new ThemeSyncDTO();
        themeSyncDTO.setExpireTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        productThemeSyncDTO.setSyncDTOList(Arrays.asList(themeSyncDTO));

        // Configure ProductMapstruct.bo2Do(...).
        final ProductThemeDO productThemeDO = new ProductThemeDO();
        productThemeDO.setStoreGuid("storeGuid");
        productThemeDO.setProductGuid("productGuid");
        productThemeDO.setTerminalCode("terminalCode");
        productThemeDO.setThemeCode("themeCode");
        productThemeDO.setThemeExpireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockProductMapstruct.bo2Do(any(ThemeSyncDTO.class))).thenReturn(productThemeDO);

        // Run the test
        productThemeServiceImplUnderTest.save(productThemeSyncDTO);

        // Verify the results
    }

    @Test
    public void testQuery() {
        assertThat(productThemeServiceImplUnderTest.query("storeGuid", "terminalCode")).isEqualTo("themeCode");
    }
}
