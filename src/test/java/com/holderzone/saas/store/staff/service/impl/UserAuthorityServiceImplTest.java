package com.holderzone.saas.store.staff.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.user.*;
import com.holderzone.saas.store.dto.user.req.AuthorizationReqDTO;
import com.holderzone.saas.store.dto.user.req.PermissionsReqDTO;
import com.holderzone.saas.store.dto.user.resp.PermissionsRespDTO;
import com.holderzone.saas.store.dto.user.resp.UserFaceDTO;
import com.holderzone.saas.store.staff.entity.domain.*;
import com.holderzone.saas.store.staff.entity.query.UserSourceQuery;
import com.holderzone.saas.store.staff.mapper.AuthorityMapper;
import com.holderzone.saas.store.staff.mapper.UserAuthorityMapper;
import com.holderzone.saas.store.staff.mapper.UserMapper;
import com.holderzone.saas.store.staff.service.AuthorizationRecordService;
import com.holderzone.saas.store.staff.service.DistributedService;
import com.holderzone.saas.store.staff.service.UserDataService;
import com.holderzone.saas.store.staff.service.UserService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.RedisTemplate;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class UserAuthorityServiceImplTest {

    @Mock
    private AuthorityMapper mockAuthorityMapper;
    @Mock
    private UserAuthorityMapper mockUserAuthorityMapper;
    @Mock
    private AuthorizationRecordService mockAuthorizationRecordService;
    @Mock
    private DistributedService mockDistributedService;
    @Mock
    private UserService mockUserService;
    @Mock
    private RedisTemplate mockRedisTemplate;
    @Mock
    private UserMapper mockUserMapper;
    @Mock
    private UserDataService mockUserDataService;

    private UserAuthorityServiceImpl userAuthorityServiceImplUnderTest;

    @Before
    public void setUp() {
        userAuthorityServiceImplUnderTest = new UserAuthorityServiceImpl(mockAuthorityMapper, mockUserAuthorityMapper,
                mockAuthorizationRecordService, mockDistributedService, mockUserService, mockRedisTemplate,
                mockUserMapper, mockUserDataService);
    }

    @Test
    public void testSaveUserAuthority() {
        // Setup
        final UserAuthoritySaveDTO userAuthoritySaveDTO = new UserAuthoritySaveDTO();
        final UserAuthorityDTO userAuthorityDTO = new UserAuthorityDTO();
        userAuthorityDTO.setInputFace(false);
        userAuthorityDTO.setAuthorizerGuid("authorizerGuid");
        userAuthorityDTO.setAuthorizerAccount("authorizerAccount");
        userAuthorityDTO.setGuid("192f6d20-9cc9-4840-8737-ce4b801eb720");
        userAuthorityDTO.setUserGuid("userGuid");
        userAuthorityDTO.setSourceCode("sourceCode");
        userAuthorityDTO.setAuthorityCode("authorityCode");
        userAuthorityDTO.setIsDelete(0);
        userAuthoritySaveDTO.setUserAuthorityDTOList(Arrays.asList(userAuthorityDTO));
        userAuthoritySaveDTO.setUserGuid("userGuid");

        when(mockDistributedService.nextAuthorityGuid()).thenReturn("192f6d20-9cc9-4840-8737-ce4b801eb720");

        // Run the test
        userAuthorityServiceImplUnderTest.saveUserAuthority(userAuthoritySaveDTO);

        // Verify the results
    }

    @Test
    public void testUpdateAuthority() throws Exception {
        // Setup
        final UserAuthorityUpdateDTO userAuthorityUpdateDTO = new UserAuthorityUpdateDTO();
        userAuthorityUpdateDTO.setIsReturn(0);
        userAuthorityUpdateDTO.setIsDebt(0);
        final UserAuthorityDTO userAuthorityDTO = new UserAuthorityDTO();
        userAuthorityDTO.setInputFace(false);
        userAuthorityDTO.setAuthorizerGuid("authorizerGuid");
        userAuthorityDTO.setAuthorizerAccount("authorizerAccount");
        userAuthorityDTO.setGuid("192f6d20-9cc9-4840-8737-ce4b801eb720");
        userAuthorityDTO.setUserGuid("userGuid");
        userAuthorityDTO.setSourceCode("sourceCode");
        userAuthorityDTO.setAuthorityCode("authorityCode");
        userAuthorityDTO.setIsDelete(0);
        userAuthorityUpdateDTO.setUserAuthorityDTOS(Arrays.asList(userAuthorityDTO));

        // Configure AuthorityMapper.selectList(...).
        final AuthorityDO authorityDO = new AuthorityDO();
        authorityDO.setIsDelete(0);
        authorityDO.setTerminalCode("terminalCode");
        authorityDO.setTerminalName("terminalName");
        authorityDO.setSourceName("sourceName");
        authorityDO.setSourceCode("sourceCode");
        authorityDO.setSourceUrl("sourceUrl");
        final List<AuthorityDO> authorityDOS = Arrays.asList(authorityDO);
        when(mockAuthorityMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(authorityDOS);

        // Run the test
        final Boolean result = userAuthorityServiceImplUnderTest.updateAuthority(userAuthorityUpdateDTO);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testUpdateAuthority_AuthorityMapperReturnsNoItems() throws Exception {
        // Setup
        final UserAuthorityUpdateDTO userAuthorityUpdateDTO = new UserAuthorityUpdateDTO();
        userAuthorityUpdateDTO.setIsReturn(0);
        userAuthorityUpdateDTO.setIsDebt(0);
        final UserAuthorityDTO userAuthorityDTO = new UserAuthorityDTO();
        userAuthorityDTO.setInputFace(false);
        userAuthorityDTO.setAuthorizerGuid("authorizerGuid");
        userAuthorityDTO.setAuthorizerAccount("authorizerAccount");
        userAuthorityDTO.setGuid("192f6d20-9cc9-4840-8737-ce4b801eb720");
        userAuthorityDTO.setUserGuid("userGuid");
        userAuthorityDTO.setSourceCode("sourceCode");
        userAuthorityDTO.setAuthorityCode("authorityCode");
        userAuthorityDTO.setIsDelete(0);
        userAuthorityUpdateDTO.setUserAuthorityDTOS(Arrays.asList(userAuthorityDTO));

        when(mockAuthorityMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final Boolean result = userAuthorityServiceImplUnderTest.updateAuthority(userAuthorityUpdateDTO);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testQueryUserAuthority1() {
        // Setup
        final UserAuthorityQueryDTO userAuthorityQueryDTO = new UserAuthorityQueryDTO("userGuid", "storeGuid");

        // Configure UserService.getByUserGuid(...).
        final UserDO userDO = new UserDO();
        userDO.setGuid("authorizationStaffGuid");
        userDO.setAccount("account");
        userDO.setName("authorizationStaffName");
        userDO.setPhone("phone");
        userDO.setIsEnable(false);
        userDO.setIsDeleted(false);
        userDO.setFaceCode("faceCode");
        when(mockUserService.getByUserGuid("userGuid")).thenReturn(userDO);

        // Run the test
        final List<UserAuthorityDTO> result = userAuthorityServiceImplUnderTest.queryUserAuthority(
                userAuthorityQueryDTO);

        // Verify the results
    }

    @Test
    public void testQueryEmployeePermissions() {
        // Setup
        final PermissionsReqDTO reqDTO = new PermissionsReqDTO();
        reqDTO.setSourceCode("sourceCode");
        reqDTO.setSourceFrom(0);
        reqDTO.setUserGuid("userGuid");
        reqDTO.setTerminalCode("terminalCode");
        reqDTO.setStoreGuid("storeGuid");

        final PermissionsRespDTO permissionsRespDTO = new PermissionsRespDTO();
        permissionsRespDTO.setAuthorizerGuid("authorizationStaffGuid");
        permissionsRespDTO.setAuthorizerAccount("authorizerAccount");
        permissionsRespDTO.setSourceFrom(0);
        permissionsRespDTO.setTerminalCode("terminalCode");
        permissionsRespDTO.setTerminalName("terminalName");
        permissionsRespDTO.setSourceName("sourceName");
        permissionsRespDTO.setSourceCode("sourceCode");
        permissionsRespDTO.setSourceUrl("sourceUrl");
        permissionsRespDTO.setUserGuid("authorizerGuid");
        permissionsRespDTO.setAuthorityCode("authorityCode");
        final List<PermissionsRespDTO> expectedResult = Arrays.asList(permissionsRespDTO);

        // Configure UserAuthorityMapper.queryEmployeePermissions(...).
        final PermissionsRespDTO permissionsRespDTO1 = new PermissionsRespDTO();
        permissionsRespDTO1.setAuthorizerGuid("authorizationStaffGuid");
        permissionsRespDTO1.setAuthorizerAccount("authorizerAccount");
        permissionsRespDTO1.setSourceFrom(0);
        permissionsRespDTO1.setTerminalCode("terminalCode");
        permissionsRespDTO1.setTerminalName("terminalName");
        permissionsRespDTO1.setSourceName("sourceName");
        permissionsRespDTO1.setSourceCode("sourceCode");
        permissionsRespDTO1.setSourceUrl("sourceUrl");
        permissionsRespDTO1.setUserGuid("authorizerGuid");
        permissionsRespDTO1.setAuthorityCode("authorityCode");
        final List<PermissionsRespDTO> permissionsRespDTOS = Arrays.asList(permissionsRespDTO1);
        final PermissionsReqDTO reqDTO1 = new PermissionsReqDTO();
        reqDTO1.setSourceCode("sourceCode");
        reqDTO1.setSourceFrom(0);
        reqDTO1.setUserGuid("userGuid");
        reqDTO1.setTerminalCode("terminalCode");
        reqDTO1.setStoreGuid("storeGuid");
        when(mockUserAuthorityMapper.queryEmployeePermissions(reqDTO1)).thenReturn(permissionsRespDTOS);

        // Run the test
        final List<PermissionsRespDTO> result = userAuthorityServiceImplUnderTest.queryEmployeePermissions(reqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryEmployeePermissions_UserAuthorityMapperReturnsNoItems() {
        // Setup
        final PermissionsReqDTO reqDTO = new PermissionsReqDTO();
        reqDTO.setSourceCode("sourceCode");
        reqDTO.setSourceFrom(0);
        reqDTO.setUserGuid("userGuid");
        reqDTO.setTerminalCode("terminalCode");
        reqDTO.setStoreGuid("storeGuid");

        // Configure UserAuthorityMapper.queryEmployeePermissions(...).
        final PermissionsReqDTO reqDTO1 = new PermissionsReqDTO();
        reqDTO1.setSourceCode("sourceCode");
        reqDTO1.setSourceFrom(0);
        reqDTO1.setUserGuid("userGuid");
        reqDTO1.setTerminalCode("terminalCode");
        reqDTO1.setStoreGuid("storeGuid");
        when(mockUserAuthorityMapper.queryEmployeePermissions(reqDTO1)).thenReturn(Collections.emptyList());

        // Run the test
        final List<PermissionsRespDTO> result = userAuthorityServiceImplUnderTest.queryEmployeePermissions(reqDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testAuthorize() {
        // Setup
        final AuthorizationReqDTO reqDTO = new AuthorizationReqDTO();
        reqDTO.setUserGuid("userGuid");
        reqDTO.setSourceCode("sourceCode");
        reqDTO.setAuthorityCode("authorityCode");

        // Configure AuthorityMapper.selectOne(...).
        final AuthorityDO authorityDO = new AuthorityDO();
        authorityDO.setIsDelete(0);
        authorityDO.setTerminalCode("terminalCode");
        authorityDO.setTerminalName("terminalName");
        authorityDO.setSourceName("sourceName");
        authorityDO.setSourceCode("sourceCode");
        authorityDO.setSourceUrl("sourceUrl");
        when(mockAuthorityMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(authorityDO);

        // Configure UserAuthorityMapper.queryAuthorize(...).
        final PermissionsRespDTO permissionsRespDTO = new PermissionsRespDTO();
        permissionsRespDTO.setAuthorizerGuid("authorizationStaffGuid");
        permissionsRespDTO.setAuthorizerAccount("authorizerAccount");
        permissionsRespDTO.setSourceFrom(0);
        permissionsRespDTO.setTerminalCode("terminalCode");
        permissionsRespDTO.setTerminalName("terminalName");
        permissionsRespDTO.setSourceName("sourceName");
        permissionsRespDTO.setSourceCode("sourceCode");
        permissionsRespDTO.setSourceUrl("sourceUrl");
        permissionsRespDTO.setUserGuid("authorizerGuid");
        permissionsRespDTO.setAuthorityCode("authorityCode");
        when(mockUserAuthorityMapper.queryAuthorize(any(UserAuthorityDTO.class))).thenReturn(permissionsRespDTO);

        // Configure UserMapper.queryUserSource(...).
        final MenuSourceDTO menuSourceDTO = new MenuSourceDTO();
        menuSourceDTO.setModuleName("moduleName");
        menuSourceDTO.setSourceGuid("sourceGuid");
        menuSourceDTO.setSourceName("sourceName");
        menuSourceDTO.setSourceCode("sourceCode");
        menuSourceDTO.setSourceUrl("sourceUrl");
        final List<MenuSourceDTO> menuSourceDTOS = Arrays.asList(menuSourceDTO);
        final UserSourceQuery userSourceQuery = new UserSourceQuery();
        userSourceQuery.setUserGuid("authorizerGuid");
        userSourceQuery.setTerminalCode("terminalCode");
        userSourceQuery.setMenuGuid("menuGuid");
        userSourceQuery.setRequestUri("requestUri");
        userSourceQuery.setStoreGuid("storeGuid");
        when(mockUserMapper.queryUserSource(userSourceQuery)).thenReturn(menuSourceDTOS);

        // Configure UserDataService.list(...).
        final List<UserDataDO> userDataDOS = Arrays.asList(UserDataDO.builder()
                .userGuid("userGuid")
                .storeGuid("storeGuid")
                .build());
        when(mockUserDataService.list(any(LambdaQueryWrapper.class))).thenReturn(userDataDOS);

        when(mockDistributedService.nextAuthorityGuid()).thenReturn("5256f32f-829d-4bb0-a808-2a24fa14ebad");

        // Configure UserService.getOne(...).
        final UserDO userDO = new UserDO();
        userDO.setGuid("authorizationStaffGuid");
        userDO.setAccount("account");
        userDO.setName("authorizationStaffName");
        userDO.setPhone("phone");
        userDO.setIsEnable(false);
        userDO.setIsDeleted(false);
        userDO.setFaceCode("faceCode");
        when(mockUserService.getOne(any(LambdaQueryWrapper.class))).thenReturn(userDO);

        when(mockAuthorizationRecordService.count(any(LambdaQueryWrapper.class))).thenReturn(0);

        // Run the test
        final Boolean result = userAuthorityServiceImplUnderTest.authorize(reqDTO);

        // Verify the results
        assertThat(result).isFalse();

        // Confirm AuthorizationRecordService.save(...).
        final AuthorizationRecordDO entity = new AuthorizationRecordDO();
        entity.setGuid("5256f32f-829d-4bb0-a808-2a24fa14ebad");
        entity.setAuthorizationCode("authorityCode");
        entity.setSourceGuid("sourceGuid");
        entity.setSourceName("sourceName");
        entity.setSourceCode("sourceCode");
        entity.setAuthorizationStaffGuid("authorizationStaffGuid");
        entity.setAuthorizationStaffName("authorizationStaffName");
        entity.setStoreName("storeName");
        entity.setStoreGuid("storeGuid");
        entity.setOperatorGuid("operatorGuid");
        entity.setOperatorName("operatorName");
        entity.setAuthorizationTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setLastUseTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setUseCount(0);
        verify(mockAuthorizationRecordService).save(entity);
    }

    @Test
    public void testAuthorize_UserMapperReturnsNoItems() {
        // Setup
        final AuthorizationReqDTO reqDTO = new AuthorizationReqDTO();
        reqDTO.setUserGuid("userGuid");
        reqDTO.setSourceCode("sourceCode");
        reqDTO.setAuthorityCode("authorityCode");

        // Configure AuthorityMapper.selectOne(...).
        final AuthorityDO authorityDO = new AuthorityDO();
        authorityDO.setIsDelete(0);
        authorityDO.setTerminalCode("terminalCode");
        authorityDO.setTerminalName("terminalName");
        authorityDO.setSourceName("sourceName");
        authorityDO.setSourceCode("sourceCode");
        authorityDO.setSourceUrl("sourceUrl");
        when(mockAuthorityMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(authorityDO);

        // Configure UserAuthorityMapper.queryAuthorize(...).
        final PermissionsRespDTO permissionsRespDTO = new PermissionsRespDTO();
        permissionsRespDTO.setAuthorizerGuid("authorizationStaffGuid");
        permissionsRespDTO.setAuthorizerAccount("authorizerAccount");
        permissionsRespDTO.setSourceFrom(0);
        permissionsRespDTO.setTerminalCode("terminalCode");
        permissionsRespDTO.setTerminalName("terminalName");
        permissionsRespDTO.setSourceName("sourceName");
        permissionsRespDTO.setSourceCode("sourceCode");
        permissionsRespDTO.setSourceUrl("sourceUrl");
        permissionsRespDTO.setUserGuid("authorizerGuid");
        permissionsRespDTO.setAuthorityCode("authorityCode");
        when(mockUserAuthorityMapper.queryAuthorize(any(UserAuthorityDTO.class))).thenReturn(permissionsRespDTO);

        // Configure UserMapper.queryUserSource(...).
        final UserSourceQuery userSourceQuery = new UserSourceQuery();
        userSourceQuery.setUserGuid("authorizerGuid");
        userSourceQuery.setTerminalCode("terminalCode");
        userSourceQuery.setMenuGuid("menuGuid");
        userSourceQuery.setRequestUri("requestUri");
        userSourceQuery.setStoreGuid("storeGuid");
        when(mockUserMapper.queryUserSource(userSourceQuery)).thenReturn(Collections.emptyList());

        // Run the test
        final Boolean result = userAuthorityServiceImplUnderTest.authorize(reqDTO);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testAuthorize_UserDataServiceReturnsNoItems() {
        // Setup
        final AuthorizationReqDTO reqDTO = new AuthorizationReqDTO();
        reqDTO.setUserGuid("userGuid");
        reqDTO.setSourceCode("sourceCode");
        reqDTO.setAuthorityCode("authorityCode");

        // Configure AuthorityMapper.selectOne(...).
        final AuthorityDO authorityDO = new AuthorityDO();
        authorityDO.setIsDelete(0);
        authorityDO.setTerminalCode("terminalCode");
        authorityDO.setTerminalName("terminalName");
        authorityDO.setSourceName("sourceName");
        authorityDO.setSourceCode("sourceCode");
        authorityDO.setSourceUrl("sourceUrl");
        when(mockAuthorityMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(authorityDO);

        // Configure UserAuthorityMapper.queryAuthorize(...).
        final PermissionsRespDTO permissionsRespDTO = new PermissionsRespDTO();
        permissionsRespDTO.setAuthorizerGuid("authorizationStaffGuid");
        permissionsRespDTO.setAuthorizerAccount("authorizerAccount");
        permissionsRespDTO.setSourceFrom(0);
        permissionsRespDTO.setTerminalCode("terminalCode");
        permissionsRespDTO.setTerminalName("terminalName");
        permissionsRespDTO.setSourceName("sourceName");
        permissionsRespDTO.setSourceCode("sourceCode");
        permissionsRespDTO.setSourceUrl("sourceUrl");
        permissionsRespDTO.setUserGuid("authorizerGuid");
        permissionsRespDTO.setAuthorityCode("authorityCode");
        when(mockUserAuthorityMapper.queryAuthorize(any(UserAuthorityDTO.class))).thenReturn(permissionsRespDTO);

        // Configure UserMapper.queryUserSource(...).
        final MenuSourceDTO menuSourceDTO = new MenuSourceDTO();
        menuSourceDTO.setModuleName("moduleName");
        menuSourceDTO.setSourceGuid("sourceGuid");
        menuSourceDTO.setSourceName("sourceName");
        menuSourceDTO.setSourceCode("sourceCode");
        menuSourceDTO.setSourceUrl("sourceUrl");
        final List<MenuSourceDTO> menuSourceDTOS = Arrays.asList(menuSourceDTO);
        final UserSourceQuery userSourceQuery = new UserSourceQuery();
        userSourceQuery.setUserGuid("authorizerGuid");
        userSourceQuery.setTerminalCode("terminalCode");
        userSourceQuery.setMenuGuid("menuGuid");
        userSourceQuery.setRequestUri("requestUri");
        userSourceQuery.setStoreGuid("storeGuid");
        when(mockUserMapper.queryUserSource(userSourceQuery)).thenReturn(menuSourceDTOS);

        when(mockUserDataService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        assertThatThrownBy(() -> userAuthorityServiceImplUnderTest.authorize(reqDTO))
                .isInstanceOf(BusinessException.class);
    }

    @Test
    public void testDeleteAuthority() {
        // Setup
        final AuthorizationReqDTO deleteDTO = new AuthorizationReqDTO();
        deleteDTO.setUserGuid("userGuid");
        deleteDTO.setSourceCode("sourceCode");
        deleteDTO.setAuthorityCode("authorityCode");

        // Run the test
        final Boolean result = userAuthorityServiceImplUnderTest.deleteAuthority(deleteDTO);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testQueryAuthority() {
        // Setup
        final PermissionsRespDTO permissionsRespDTO = new PermissionsRespDTO();
        permissionsRespDTO.setAuthorizerGuid("authorizationStaffGuid");
        permissionsRespDTO.setAuthorizerAccount("authorizerAccount");
        permissionsRespDTO.setSourceFrom(0);
        permissionsRespDTO.setTerminalCode("terminalCode");
        permissionsRespDTO.setTerminalName("terminalName");
        permissionsRespDTO.setSourceName("sourceName");
        permissionsRespDTO.setSourceCode("sourceCode");
        permissionsRespDTO.setSourceUrl("sourceUrl");
        permissionsRespDTO.setUserGuid("authorizerGuid");
        permissionsRespDTO.setAuthorityCode("authorityCode");
        final List<PermissionsRespDTO> expectedResult = Arrays.asList(permissionsRespDTO);

        // Configure AuthorityMapper.selectList(...).
        final AuthorityDO authorityDO = new AuthorityDO();
        authorityDO.setIsDelete(0);
        authorityDO.setTerminalCode("terminalCode");
        authorityDO.setTerminalName("terminalName");
        authorityDO.setSourceName("sourceName");
        authorityDO.setSourceCode("sourceCode");
        authorityDO.setSourceUrl("sourceUrl");
        final List<AuthorityDO> authorityDOS = Arrays.asList(authorityDO);
        when(mockAuthorityMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(authorityDOS);

        // Run the test
        final List<PermissionsRespDTO> result = userAuthorityServiceImplUnderTest.queryAuthority();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryAuthority_AuthorityMapperReturnsNoItems() {
        // Setup
        when(mockAuthorityMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<PermissionsRespDTO> result = userAuthorityServiceImplUnderTest.queryAuthority();

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testQueryUserAuthority2() {
        // Setup
        final PermissionsReqDTO reqDTO = new PermissionsReqDTO();
        reqDTO.setSourceCode("sourceCode");
        reqDTO.setSourceFrom(0);
        reqDTO.setUserGuid("userGuid");
        reqDTO.setTerminalCode("terminalCode");
        reqDTO.setStoreGuid("storeGuid");

        // Configure UserAuthorityMapper.queryUserAuthority(...).
        final PermissionsReqDTO reqDTO1 = new PermissionsReqDTO();
        reqDTO1.setSourceCode("sourceCode");
        reqDTO1.setSourceFrom(0);
        reqDTO1.setUserGuid("userGuid");
        reqDTO1.setTerminalCode("terminalCode");
        reqDTO1.setStoreGuid("storeGuid");
        when(mockUserAuthorityMapper.queryUserAuthority(reqDTO1)).thenReturn(0);

        // Run the test
        final Integer result = userAuthorityServiceImplUnderTest.queryUserAuthority(reqDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    public void testInputFace() {
        // Setup
        final UserAuthoritySaveDTO inputFaceDTO = new UserAuthoritySaveDTO();
        final UserAuthorityDTO userAuthorityDTO = new UserAuthorityDTO();
        userAuthorityDTO.setInputFace(false);
        userAuthorityDTO.setAuthorizerGuid("authorizerGuid");
        userAuthorityDTO.setAuthorizerAccount("authorizerAccount");
        userAuthorityDTO.setGuid("192f6d20-9cc9-4840-8737-ce4b801eb720");
        userAuthorityDTO.setUserGuid("userGuid");
        userAuthorityDTO.setSourceCode("sourceCode");
        userAuthorityDTO.setAuthorityCode("authorityCode");
        userAuthorityDTO.setIsDelete(0);
        inputFaceDTO.setUserAuthorityDTOList(Arrays.asList(userAuthorityDTO));
        inputFaceDTO.setUserGuid("userGuid");

        // Run the test
        userAuthorityServiceImplUnderTest.inputFace(inputFaceDTO);

        // Verify the results
    }

    @Test
    public void testAuthorizeFace() {
        // Setup
        final AuthorizationReqDTO reqDTO = new AuthorizationReqDTO();
        reqDTO.setUserGuid("userGuid");
        reqDTO.setSourceCode("sourceCode");
        reqDTO.setAuthorityCode("authorityCode");

        final UserFaceDTO expectedResult = new UserFaceDTO();
        expectedResult.setPhone("phone");
        expectedResult.setUserGuid("authorizationStaffGuid");
        expectedResult.setUserName("authorizationStaffName");
        expectedResult.setAccount("account");
        expectedResult.setFaceCode("faceCode");

        // Configure AuthorityMapper.selectOne(...).
        final AuthorityDO authorityDO = new AuthorityDO();
        authorityDO.setIsDelete(0);
        authorityDO.setTerminalCode("terminalCode");
        authorityDO.setTerminalName("terminalName");
        authorityDO.setSourceName("sourceName");
        authorityDO.setSourceCode("sourceCode");
        authorityDO.setSourceUrl("sourceUrl");
        when(mockAuthorityMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(authorityDO);

        // Configure UserMapper.selectOne(...).
        final UserDO userDO = new UserDO();
        userDO.setGuid("authorizationStaffGuid");
        userDO.setAccount("account");
        userDO.setName("authorizationStaffName");
        userDO.setPhone("phone");
        userDO.setIsEnable(false);
        userDO.setIsDeleted(false);
        userDO.setFaceCode("faceCode");
        when(mockUserMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(userDO);

        // Configure UserAuthorityMapper.selectOne(...).
        final UserAuthorityDO userAuthorityDO = new UserAuthorityDO("192f6d20-9cc9-4840-8737-ce4b801eb720", "userGuid",
                "sourceCode", "authorityCode");
        when(mockUserAuthorityMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(userAuthorityDO);

        // Configure UserDataService.list(...).
        final List<UserDataDO> userDataDOS = Arrays.asList(UserDataDO.builder()
                .userGuid("userGuid")
                .storeGuid("storeGuid")
                .build());
        when(mockUserDataService.list(any(LambdaQueryWrapper.class))).thenReturn(userDataDOS);

        when(mockDistributedService.nextAuthorityGuid()).thenReturn("5256f32f-829d-4bb0-a808-2a24fa14ebad");
        when(mockAuthorizationRecordService.count(any(LambdaQueryWrapper.class))).thenReturn(0);

        // Run the test
        final UserFaceDTO result = userAuthorityServiceImplUnderTest.authorizeFace(reqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);

        // Confirm AuthorizationRecordService.save(...).
        final AuthorizationRecordDO entity = new AuthorizationRecordDO();
        entity.setGuid("5256f32f-829d-4bb0-a808-2a24fa14ebad");
        entity.setAuthorizationCode("authorityCode");
        entity.setSourceGuid("sourceGuid");
        entity.setSourceName("sourceName");
        entity.setSourceCode("sourceCode");
        entity.setAuthorizationStaffGuid("authorizationStaffGuid");
        entity.setAuthorizationStaffName("authorizationStaffName");
        entity.setStoreName("storeName");
        entity.setStoreGuid("storeGuid");
        entity.setOperatorGuid("operatorGuid");
        entity.setOperatorName("operatorName");
        entity.setAuthorizationTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setLastUseTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setUseCount(0);
        verify(mockAuthorizationRecordService).save(entity);
    }

    @Test
    public void testAuthorizeFace_UserDataServiceReturnsNoItems() {
        // Setup
        final AuthorizationReqDTO reqDTO = new AuthorizationReqDTO();
        reqDTO.setUserGuid("userGuid");
        reqDTO.setSourceCode("sourceCode");
        reqDTO.setAuthorityCode("authorityCode");

        // Configure AuthorityMapper.selectOne(...).
        final AuthorityDO authorityDO = new AuthorityDO();
        authorityDO.setIsDelete(0);
        authorityDO.setTerminalCode("terminalCode");
        authorityDO.setTerminalName("terminalName");
        authorityDO.setSourceName("sourceName");
        authorityDO.setSourceCode("sourceCode");
        authorityDO.setSourceUrl("sourceUrl");
        when(mockAuthorityMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(authorityDO);

        // Configure UserMapper.selectOne(...).
        final UserDO userDO = new UserDO();
        userDO.setGuid("authorizationStaffGuid");
        userDO.setAccount("account");
        userDO.setName("authorizationStaffName");
        userDO.setPhone("phone");
        userDO.setIsEnable(false);
        userDO.setIsDeleted(false);
        userDO.setFaceCode("faceCode");
        when(mockUserMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(userDO);

        // Configure UserAuthorityMapper.selectOne(...).
        final UserAuthorityDO userAuthorityDO = new UserAuthorityDO("192f6d20-9cc9-4840-8737-ce4b801eb720", "userGuid",
                "sourceCode", "authorityCode");
        when(mockUserAuthorityMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(userAuthorityDO);

        when(mockUserDataService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        assertThatThrownBy(() -> userAuthorityServiceImplUnderTest.authorizeFace(reqDTO))
                .isInstanceOf(BusinessException.class);
    }
}
