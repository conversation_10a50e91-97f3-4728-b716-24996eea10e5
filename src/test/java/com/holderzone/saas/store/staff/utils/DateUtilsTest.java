package com.holderzone.saas.store.staff.utils;

import org.junit.Test;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

public class DateUtilsTest {

    @Test
    public void testGetTodayStartTime() {
        assertThat(DateUtils.getTodayStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
                .isEqualTo(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
    }

    @Test
    public void testGetTodayEndTime() {
        assertThat(DateUtils.getTodayEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
                .isEqualTo(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
    }

    @Test
    public void testGetYear() {
        assertThat(DateUtils.getYear(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).isEqualTo(0);
    }

    @Test
    public void testGetMonth() {
        assertThat(DateUtils.getMonth(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).isEqualTo(0);
    }

    @Test
    public void testGetDay() {
        assertThat(DateUtils.getDay(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).isEqualTo(0);
    }

    @Test
    public void testParseDateToStr1() {
        assertThat(DateUtils.parseDateToStr(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                "dateFormat")).isEqualTo("result");
    }

    @Test
    public void testParseTimestampToStr() {
        assertThat(DateUtils.parseTimestampToStr(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)),
                "dateFormat")).isEqualTo("result");
    }

    @Test
    public void testParseDateToStr2() {
        assertThat(DateUtils.parseDateToStr(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), "dateFormat",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).isEqualTo("result");
    }

    @Test
    public void testParseDateToStr3() {
        assertThat(DateUtils.parseDateToStr(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), "dateFormat",
                "defaultValue")).isEqualTo("defaultValue");
    }

    @Test
    public void testParseStrToDate1() {
        assertThat(DateUtils.parseStrToDate("time", "dateFormat"))
                .isEqualTo(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
    }

    @Test
    public void testParseStrToDate2() {
        assertThat(DateUtils.parseStrToDate("strTime", "dateFormat",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
                .isEqualTo(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
    }

    @Test
    public void testStrToDate() {
        assertThat(DateUtils.strToDate("strTime"))
                .isEqualTo(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
    }

    @Test
    public void testGetMonthListOfDate() {
        assertThat(DateUtils.getMonthListOfDate("beginDateStr", "endDateStr")).isEqualTo(Arrays.asList("value"));
        assertThat(DateUtils.getMonthListOfDate("beginDateStr", "endDateStr")).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testGetDayListOfDate() {
        assertThat(DateUtils.getDayListOfDate("beginDateStr", "endDateStr")).isEqualTo(Arrays.asList("value"));
        assertThat(DateUtils.getDayListOfDate("beginDateStr", "endDateStr")).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testGetYearListOfYears() {
        assertThat(DateUtils.getYearListOfYears(0, 0)).isEqualTo(Arrays.asList(0));
        assertThat(DateUtils.getYearListOfYears(0, 0)).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testGetWeekthOfYear() {
        assertThat(DateUtils.getWeekthOfYear(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).isEqualTo(0);
    }

    @Test
    public void testGetWeekTimeOfYear() {
        // Setup
        final HashMap<Integer, String> expectedResult = new HashMap<>();

        // Run the test
        final HashMap<Integer, String> result = DateUtils.getWeekTimeOfYear(2020);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetWeekCountOfYear() {
        assertThat(DateUtils.getWeekCountOfYear(2020)).isEqualTo(0);
    }

    @Test
    public void testGetFirstDayOfWeek1() {
        assertThat(DateUtils.getFirstDayOfWeek(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
                .isEqualTo(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
    }

    @Test
    public void testGetLastDayOfWeek1() {
        assertThat(DateUtils.getLastDayOfWeek(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
                .isEqualTo(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
    }

    @Test
    public void testGetFirstDayOfWeek2() {
        assertThat(DateUtils.getFirstDayOfWeek(2020, 1))
                .isEqualTo(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
    }

    @Test
    public void testGetLastDayOfWeek2() {
        assertThat(DateUtils.getLastDayOfWeek(2020, 1))
                .isEqualTo(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
    }

    @Test
    public void testGetFirstDayOfMonth() {
        assertThat(DateUtils.getFirstDayOfMonth(2020, 1))
                .isEqualTo(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
    }

    @Test
    public void testGetLastDayOfMonth() {
        assertThat(DateUtils.getLastDayOfMonth(2020, 1))
                .isEqualTo(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
    }

    @Test
    public void testGetDayWeekOfDate1() {
        assertThat(DateUtils.getDayWeekOfDate1(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
                .isEqualTo("result");
    }

    @Test
    public void testGetDayWeekOfDate2() {
        assertThat(DateUtils.getDayWeekOfDate2(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
                .isEqualTo(0);
    }

    @Test
    public void testValidateIsDate() {
        assertThat(DateUtils.validateIsDate("strTime")).isFalse();
    }

    @Test
    public void testFormatHhMmSsOfDate() {
        assertThat(DateUtils.formatHhMmSsOfDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
                .isEqualTo(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
    }

    @Test
    public void testAddDate() {
        assertThat(DateUtils.addDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 2020, 1, 1, 0, 0, 0,
                0)).isEqualTo(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
    }

    @Test
    public void testGetDistanceTimestamp() {
        assertThat(DateUtils.getDistanceTimestamp(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).isEqualTo(0L);
    }

    @Test
    public void testCompareIsSameMonth() {
        assertThat(DateUtils.compareIsSameMonth(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).isFalse();
    }

    @Test
    public void testGetDistanceTime1() {
        assertThat(DateUtils.getDistanceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).isEqualTo(new long[]{0L});
        assertThat(DateUtils.getDistanceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).isEqualTo(new long[]{});
    }

    @Test
    public void testGetDistanceTime2() {
        assertThat(DateUtils.getDistanceTime("str1", "str2")).isEqualTo(new long[]{0L});
        assertThat(DateUtils.getDistanceTime("str1", "str2")).isEqualTo(new long[]{});
    }

    @Test
    public void testGetDistanceDays() throws Exception {
        assertThat(DateUtils.getDistanceDays("str1", "str2")).isEqualTo(0L);
        assertThatThrownBy(() -> DateUtils.getDistanceDays("str1", "str2")).isInstanceOf(Exception.class);
    }

    @Test
    public void testGetDayBeginTime() {
        assertThat(DateUtils.getDayBeginTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
                .isEqualTo(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
    }

    @Test
    public void testGetDayEndTime() {
        assertThat(DateUtils.getDayEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
                .isEqualTo(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
    }

    @Test
    public void testStampToDate() {
        assertThat(DateUtils.stampToDate("s", "dateFormat")).isEqualTo("result");
    }
}
