package com.holderzone.saas.store.staff.utils;

import org.junit.Test;

import java.util.function.Predicate;

import static org.assertj.core.api.Assertions.assertThat;

public class RandomCodeUtilsTest {

    @Test
    public void testGenerate1() {
        // Setup
        final Predicate<String> predicate = val -> {
            return false;
        };

        // Run the test
        final Integer result = RandomCodeUtils.generate(predicate, 0);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    public void testGenerateIgnoreDuplicates() {
        // Setup
        final Predicate<String> predicate = val -> {
            return false;
        };

        // Run the test
        final Integer result = RandomCodeUtils.generateIgnoreDuplicates(predicate, 0);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    public void testGenerate2() {
        assertThat(RandomCodeUtils.generate()).isEqualTo(0);
    }
}
