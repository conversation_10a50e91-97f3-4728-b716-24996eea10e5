package com.holderzone.saas.store.staff.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.resource.common.dto.enterprise.EnterpriseDTO;
import com.holderzone.resource.common.dto.enterprise.EnterpriseQueryDTO;
import com.holderzone.resource.common.dto.extension.BaseDictionaryDTO;
import com.holderzone.saas.store.dto.user.*;
import com.holderzone.saas.store.staff.entity.domain.MenuDO;
import com.holderzone.saas.store.staff.entity.domain.RoleSourceDO;
import com.holderzone.saas.store.staff.entity.domain.StoreSourceDO;
import com.holderzone.saas.store.staff.mapper.MenuMapper;
import com.holderzone.saas.store.staff.mapper.RoleSourceMapper;
import com.holderzone.saas.store.staff.mapper.StoreSourceMapper;
import com.holderzone.saas.store.staff.mapstruct.MenuMapStruct;
import com.holderzone.saas.store.staff.service.remote.EnterpriseClient;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class RoleDataServiceImplTest {

    @Mock
    private RoleSourceMapper mockRoleSourceMapper;
    @Mock
    private StoreSourceMapper mockStoreSourceMapper;
    @Mock
    private MenuMapper mockMenuMapper;
    @Mock
    private MenuMapStruct mockMenuMapStruct;
    @Mock
    private EnterpriseClient mockEnterpriseClient;

    @InjectMocks
    private RoleDataServiceImpl roleDataServiceImplUnderTest;

    @Test
    public void testQueryRoleTerminal() {
        // Setup
        final TerminalDTO terminalDTO = new TerminalDTO();
        terminalDTO.setTerminalName("terminalName");
        terminalDTO.setTerminalGuid("terminalGuid");
        terminalDTO.setTerminalCode("terminalCode");
        terminalDTO.setIsChecked(false);
        final List<TerminalDTO> expectedResult = Arrays.asList(terminalDTO);

        // Configure RoleSourceMapper.selectList(...).
        final RoleSourceDO roleSourceDO = new RoleSourceDO();
        roleSourceDO.setRoleGuid("roleGuid");
        roleSourceDO.setTerminalGuid("terminalGuid");
        roleSourceDO.setTerminalCode("terminalCode");
        roleSourceDO.setTerminalName("terminalName");
        roleSourceDO.setMenuGuid("menuGuid");
        roleSourceDO.setSourceGuid("sourceGuid");
        roleSourceDO.setSourceCode("sourceCode");
        roleSourceDO.setSourceUrl("sourceUrl");
        final List<RoleSourceDO> roleSourceDOS = Arrays.asList(roleSourceDO);
        when(mockRoleSourceMapper.selectList(any(Wrapper.class))).thenReturn(roleSourceDOS);

        // Configure StoreSourceMapper.selectList(...).
        final StoreSourceDO storeSourceDO = new StoreSourceDO();
        storeSourceDO.setTerminalGuid("terminalGuid");
        storeSourceDO.setTerminalName("terminalName");
        storeSourceDO.setTerminalCode("terminalCode");
        storeSourceDO.setModuleGuid("moduleGuid");
        storeSourceDO.setModuleType("moduleType");
        storeSourceDO.setSourceGuid("sourceGuid");
        storeSourceDO.setSourceName("sourceName");
        storeSourceDO.setSourceCode("sourceCode");
        storeSourceDO.setSourceUrl("sourceUrl");
        final List<StoreSourceDO> storeSourceDOS = Arrays.asList(storeSourceDO);
        when(mockStoreSourceMapper.selectList(any(Wrapper.class))).thenReturn(storeSourceDOS);

        // Run the test
        final List<TerminalDTO> result = roleDataServiceImplUnderTest.queryRoleTerminal("roleGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryRoleTerminal_RoleSourceMapperReturnsNoItems() {
        // Setup
        when(mockRoleSourceMapper.selectList(any(Wrapper.class))).thenReturn(Collections.emptyList());

        // Configure StoreSourceMapper.selectList(...).
        final StoreSourceDO storeSourceDO = new StoreSourceDO();
        storeSourceDO.setTerminalGuid("terminalGuid");
        storeSourceDO.setTerminalName("terminalName");
        storeSourceDO.setTerminalCode("terminalCode");
        storeSourceDO.setModuleGuid("moduleGuid");
        storeSourceDO.setModuleType("moduleType");
        storeSourceDO.setSourceGuid("sourceGuid");
        storeSourceDO.setSourceName("sourceName");
        storeSourceDO.setSourceCode("sourceCode");
        storeSourceDO.setSourceUrl("sourceUrl");
        final List<StoreSourceDO> storeSourceDOS = Arrays.asList(storeSourceDO);
        when(mockStoreSourceMapper.selectList(any(Wrapper.class))).thenReturn(storeSourceDOS);

        // Run the test
        final List<TerminalDTO> result = roleDataServiceImplUnderTest.queryRoleTerminal("roleGuid");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testQueryRoleTerminal_StoreSourceMapperReturnsNoItems() {
        // Setup
        // Configure RoleSourceMapper.selectList(...).
        final RoleSourceDO roleSourceDO = new RoleSourceDO();
        roleSourceDO.setRoleGuid("roleGuid");
        roleSourceDO.setTerminalGuid("terminalGuid");
        roleSourceDO.setTerminalCode("terminalCode");
        roleSourceDO.setTerminalName("terminalName");
        roleSourceDO.setMenuGuid("menuGuid");
        roleSourceDO.setSourceGuid("sourceGuid");
        roleSourceDO.setSourceCode("sourceCode");
        roleSourceDO.setSourceUrl("sourceUrl");
        final List<RoleSourceDO> roleSourceDOS = Arrays.asList(roleSourceDO);
        when(mockRoleSourceMapper.selectList(any(Wrapper.class))).thenReturn(roleSourceDOS);

        when(mockStoreSourceMapper.selectList(any(Wrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<TerminalDTO> result = roleDataServiceImplUnderTest.queryRoleTerminal("roleGuid");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testQueryRoleData() {
        // Setup
        // Configure StoreSourceMapper.selectList(...).
        final StoreSourceDO storeSourceDO = new StoreSourceDO();
        storeSourceDO.setTerminalGuid("terminalGuid");
        storeSourceDO.setTerminalName("terminalName");
        storeSourceDO.setTerminalCode("terminalCode");
        storeSourceDO.setModuleGuid("moduleGuid");
        storeSourceDO.setModuleType("moduleType");
        storeSourceDO.setSourceGuid("sourceGuid");
        storeSourceDO.setSourceName("sourceName");
        storeSourceDO.setSourceCode("sourceCode");
        storeSourceDO.setSourceUrl("sourceUrl");
        final List<StoreSourceDO> storeSourceDOS = Arrays.asList(storeSourceDO);
        when(mockStoreSourceMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(storeSourceDOS);

        // Configure EnterpriseClient.findEnterprise(...).
        final EnterpriseDTO enterpriseDTO = new EnterpriseDTO();
        enterpriseDTO.setId(0L);
        enterpriseDTO.setUid("uid");
        enterpriseDTO.setEnterpriseGuid("enterpriseGuid");
        enterpriseDTO.setRegType("regType");
        enterpriseDTO.setManagementModel(EnterpriseDTO.ManagementModel.SINGLE);
        final EnterpriseQueryDTO query = new EnterpriseQueryDTO();
        query.setEnterpriseGuid("enterpriseGuid");
        query.setUid("uid");
        query.setRegTel("regTel");
        query.setDateType(0);
        query.setBeginTime(0L);
        when(mockEnterpriseClient.findEnterprise(query)).thenReturn(enterpriseDTO);

        // Configure RoleSourceMapper.selectList(...).
        final RoleSourceDO roleSourceDO = new RoleSourceDO();
        roleSourceDO.setRoleGuid("roleGuid");
        roleSourceDO.setTerminalGuid("terminalGuid");
        roleSourceDO.setTerminalCode("terminalCode");
        roleSourceDO.setTerminalName("terminalName");
        roleSourceDO.setMenuGuid("menuGuid");
        roleSourceDO.setSourceGuid("sourceGuid");
        roleSourceDO.setSourceCode("sourceCode");
        roleSourceDO.setSourceUrl("sourceUrl");
        final List<RoleSourceDO> roleSourceDOS = Arrays.asList(roleSourceDO);
        when(mockRoleSourceMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(roleSourceDOS);

        // Configure MenuMapper.selectList(...).
        final MenuDO menuDO = new MenuDO();
        menuDO.setId(0L);
        menuDO.setMenuSort(0L);
        menuDO.setModuleGuid("moduleGuid");
        menuDO.setTerminalGuid("terminalGuid");
        menuDO.setIsEnable(false);
        final List<MenuDO> menuDOS = Arrays.asList(menuDO);
        when(mockMenuMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(menuDOS);

        // Configure MenuMapStruct.menuDOList2DTOList(...).
        final MenuDTO menuDTO = new MenuDTO();
        menuDTO.setMenuGuid("menuGuid");
        menuDTO.setMenuName("menuName");
        menuDTO.setMenuSort(0L);
        menuDTO.setParentIds("parentIds");
        menuDTO.setModuleGuid("moduleGuid");
        menuDTO.setMenus(Arrays.asList(new MenuDTO()));
        final SourceDTO sourceDTO = new SourceDTO();
        sourceDTO.setSourceName("sourceName");
        sourceDTO.setSourceGuid("sourceGuid");
        sourceDTO.setSourceCode("sourceCode");
        sourceDTO.setSourceUrl("sourceUrl");
        sourceDTO.setIsChecked(false);
        menuDTO.setSourceDTOList(Arrays.asList(sourceDTO));
        menuDTO.setIsCheckedList(Arrays.asList("value"));
        final List<MenuDTO> list = Arrays.asList(menuDTO);
        final MenuDO menuDO1 = new MenuDO();
        menuDO1.setId(0L);
        menuDO1.setMenuSort(0L);
        menuDO1.setModuleGuid("moduleGuid");
        menuDO1.setTerminalGuid("terminalGuid");
        menuDO1.setIsEnable(false);
        final List<MenuDO> menuDOList = Arrays.asList(menuDO1);
        when(mockMenuMapStruct.menuDOList2DTOList(menuDOList)).thenReturn(list);

        // Configure EnterpriseClient.queryManagementType(...).
        final BaseDictionaryDTO baseDictionaryDTO = new BaseDictionaryDTO();
        baseDictionaryDTO.setGuid("eb64e034-e021-465e-8f6f-f38a9cf52363");
        baseDictionaryDTO.setValue("value");
        baseDictionaryDTO.setType("type");
        baseDictionaryDTO.setStaffGuid("staffGuid");
        baseDictionaryDTO.setCode("code");
        when(mockEnterpriseClient.queryManagementType("enterpriseGuid")).thenReturn(baseDictionaryDTO);

        // Run the test
        final DefaultMenuChecked result = roleDataServiceImplUnderTest.queryRoleData("roleGuid", "terminalGuid");

        // Verify the results
    }

    @Test
    public void testQueryRoleData_StoreSourceMapperReturnsNoItems() {
        // Setup
        when(mockStoreSourceMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        assertThatThrownBy(() -> roleDataServiceImplUnderTest.queryRoleData("roleGuid", "terminalGuid"))
                .isInstanceOf(BusinessException.class);
    }

    @Test
    public void testQueryRoleData_RoleSourceMapperReturnsNoItems() {
        // Setup
        // Configure StoreSourceMapper.selectList(...).
        final StoreSourceDO storeSourceDO = new StoreSourceDO();
        storeSourceDO.setTerminalGuid("terminalGuid");
        storeSourceDO.setTerminalName("terminalName");
        storeSourceDO.setTerminalCode("terminalCode");
        storeSourceDO.setModuleGuid("moduleGuid");
        storeSourceDO.setModuleType("moduleType");
        storeSourceDO.setSourceGuid("sourceGuid");
        storeSourceDO.setSourceName("sourceName");
        storeSourceDO.setSourceCode("sourceCode");
        storeSourceDO.setSourceUrl("sourceUrl");
        final List<StoreSourceDO> storeSourceDOS = Arrays.asList(storeSourceDO);
        when(mockStoreSourceMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(storeSourceDOS);

        // Configure EnterpriseClient.findEnterprise(...).
        final EnterpriseDTO enterpriseDTO = new EnterpriseDTO();
        enterpriseDTO.setId(0L);
        enterpriseDTO.setUid("uid");
        enterpriseDTO.setEnterpriseGuid("enterpriseGuid");
        enterpriseDTO.setRegType("regType");
        enterpriseDTO.setManagementModel(EnterpriseDTO.ManagementModel.SINGLE);
        final EnterpriseQueryDTO query = new EnterpriseQueryDTO();
        query.setEnterpriseGuid("enterpriseGuid");
        query.setUid("uid");
        query.setRegTel("regTel");
        query.setDateType(0);
        query.setBeginTime(0L);
        when(mockEnterpriseClient.findEnterprise(query)).thenReturn(enterpriseDTO);

        when(mockRoleSourceMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure MenuMapper.selectList(...).
        final MenuDO menuDO = new MenuDO();
        menuDO.setId(0L);
        menuDO.setMenuSort(0L);
        menuDO.setModuleGuid("moduleGuid");
        menuDO.setTerminalGuid("terminalGuid");
        menuDO.setIsEnable(false);
        final List<MenuDO> menuDOS = Arrays.asList(menuDO);
        when(mockMenuMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(menuDOS);

        // Configure MenuMapStruct.menuDOList2DTOList(...).
        final MenuDTO menuDTO = new MenuDTO();
        menuDTO.setMenuGuid("menuGuid");
        menuDTO.setMenuName("menuName");
        menuDTO.setMenuSort(0L);
        menuDTO.setParentIds("parentIds");
        menuDTO.setModuleGuid("moduleGuid");
        menuDTO.setMenus(Arrays.asList(new MenuDTO()));
        final SourceDTO sourceDTO = new SourceDTO();
        sourceDTO.setSourceName("sourceName");
        sourceDTO.setSourceGuid("sourceGuid");
        sourceDTO.setSourceCode("sourceCode");
        sourceDTO.setSourceUrl("sourceUrl");
        sourceDTO.setIsChecked(false);
        menuDTO.setSourceDTOList(Arrays.asList(sourceDTO));
        menuDTO.setIsCheckedList(Arrays.asList("value"));
        final List<MenuDTO> list = Arrays.asList(menuDTO);
        final MenuDO menuDO1 = new MenuDO();
        menuDO1.setId(0L);
        menuDO1.setMenuSort(0L);
        menuDO1.setModuleGuid("moduleGuid");
        menuDO1.setTerminalGuid("terminalGuid");
        menuDO1.setIsEnable(false);
        final List<MenuDO> menuDOList = Arrays.asList(menuDO1);
        when(mockMenuMapStruct.menuDOList2DTOList(menuDOList)).thenReturn(list);

        // Configure EnterpriseClient.queryManagementType(...).
        final BaseDictionaryDTO baseDictionaryDTO = new BaseDictionaryDTO();
        baseDictionaryDTO.setGuid("eb64e034-e021-465e-8f6f-f38a9cf52363");
        baseDictionaryDTO.setValue("value");
        baseDictionaryDTO.setType("type");
        baseDictionaryDTO.setStaffGuid("staffGuid");
        baseDictionaryDTO.setCode("code");
        when(mockEnterpriseClient.queryManagementType("enterpriseGuid")).thenReturn(baseDictionaryDTO);

        // Run the test
        final DefaultMenuChecked result = roleDataServiceImplUnderTest.queryRoleData("roleGuid", "terminalGuid");

        // Verify the results
    }

    @Test
    public void testQueryRoleData_MenuMapperReturnsNoItems() {
        // Setup
        // Configure StoreSourceMapper.selectList(...).
        final StoreSourceDO storeSourceDO = new StoreSourceDO();
        storeSourceDO.setTerminalGuid("terminalGuid");
        storeSourceDO.setTerminalName("terminalName");
        storeSourceDO.setTerminalCode("terminalCode");
        storeSourceDO.setModuleGuid("moduleGuid");
        storeSourceDO.setModuleType("moduleType");
        storeSourceDO.setSourceGuid("sourceGuid");
        storeSourceDO.setSourceName("sourceName");
        storeSourceDO.setSourceCode("sourceCode");
        storeSourceDO.setSourceUrl("sourceUrl");
        final List<StoreSourceDO> storeSourceDOS = Arrays.asList(storeSourceDO);
        when(mockStoreSourceMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(storeSourceDOS);

        // Configure EnterpriseClient.findEnterprise(...).
        final EnterpriseDTO enterpriseDTO = new EnterpriseDTO();
        enterpriseDTO.setId(0L);
        enterpriseDTO.setUid("uid");
        enterpriseDTO.setEnterpriseGuid("enterpriseGuid");
        enterpriseDTO.setRegType("regType");
        enterpriseDTO.setManagementModel(EnterpriseDTO.ManagementModel.SINGLE);
        final EnterpriseQueryDTO query = new EnterpriseQueryDTO();
        query.setEnterpriseGuid("enterpriseGuid");
        query.setUid("uid");
        query.setRegTel("regTel");
        query.setDateType(0);
        query.setBeginTime(0L);
        when(mockEnterpriseClient.findEnterprise(query)).thenReturn(enterpriseDTO);

        // Configure RoleSourceMapper.selectList(...).
        final RoleSourceDO roleSourceDO = new RoleSourceDO();
        roleSourceDO.setRoleGuid("roleGuid");
        roleSourceDO.setTerminalGuid("terminalGuid");
        roleSourceDO.setTerminalCode("terminalCode");
        roleSourceDO.setTerminalName("terminalName");
        roleSourceDO.setMenuGuid("menuGuid");
        roleSourceDO.setSourceGuid("sourceGuid");
        roleSourceDO.setSourceCode("sourceCode");
        roleSourceDO.setSourceUrl("sourceUrl");
        final List<RoleSourceDO> roleSourceDOS = Arrays.asList(roleSourceDO);
        when(mockRoleSourceMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(roleSourceDOS);

        when(mockMenuMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure MenuMapStruct.menuDOList2DTOList(...).
        final MenuDTO menuDTO = new MenuDTO();
        menuDTO.setMenuGuid("menuGuid");
        menuDTO.setMenuName("menuName");
        menuDTO.setMenuSort(0L);
        menuDTO.setParentIds("parentIds");
        menuDTO.setModuleGuid("moduleGuid");
        menuDTO.setMenus(Arrays.asList(new MenuDTO()));
        final SourceDTO sourceDTO = new SourceDTO();
        sourceDTO.setSourceName("sourceName");
        sourceDTO.setSourceGuid("sourceGuid");
        sourceDTO.setSourceCode("sourceCode");
        sourceDTO.setSourceUrl("sourceUrl");
        sourceDTO.setIsChecked(false);
        menuDTO.setSourceDTOList(Arrays.asList(sourceDTO));
        menuDTO.setIsCheckedList(Arrays.asList("value"));
        final List<MenuDTO> list = Arrays.asList(menuDTO);
        final MenuDO menuDO = new MenuDO();
        menuDO.setId(0L);
        menuDO.setMenuSort(0L);
        menuDO.setModuleGuid("moduleGuid");
        menuDO.setTerminalGuid("terminalGuid");
        menuDO.setIsEnable(false);
        final List<MenuDO> menuDOList = Arrays.asList(menuDO);
        when(mockMenuMapStruct.menuDOList2DTOList(menuDOList)).thenReturn(list);

        // Configure EnterpriseClient.queryManagementType(...).
        final BaseDictionaryDTO baseDictionaryDTO = new BaseDictionaryDTO();
        baseDictionaryDTO.setGuid("eb64e034-e021-465e-8f6f-f38a9cf52363");
        baseDictionaryDTO.setValue("value");
        baseDictionaryDTO.setType("type");
        baseDictionaryDTO.setStaffGuid("staffGuid");
        baseDictionaryDTO.setCode("code");
        when(mockEnterpriseClient.queryManagementType("enterpriseGuid")).thenReturn(baseDictionaryDTO);

        // Run the test
        final DefaultMenuChecked result = roleDataServiceImplUnderTest.queryRoleData("roleGuid", "terminalGuid");

        // Verify the results
    }

    @Test
    public void testQueryRoleData_MenuMapStructReturnsNoItems() {
        // Setup
        // Configure StoreSourceMapper.selectList(...).
        final StoreSourceDO storeSourceDO = new StoreSourceDO();
        storeSourceDO.setTerminalGuid("terminalGuid");
        storeSourceDO.setTerminalName("terminalName");
        storeSourceDO.setTerminalCode("terminalCode");
        storeSourceDO.setModuleGuid("moduleGuid");
        storeSourceDO.setModuleType("moduleType");
        storeSourceDO.setSourceGuid("sourceGuid");
        storeSourceDO.setSourceName("sourceName");
        storeSourceDO.setSourceCode("sourceCode");
        storeSourceDO.setSourceUrl("sourceUrl");
        final List<StoreSourceDO> storeSourceDOS = Arrays.asList(storeSourceDO);
        when(mockStoreSourceMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(storeSourceDOS);

        // Configure EnterpriseClient.findEnterprise(...).
        final EnterpriseDTO enterpriseDTO = new EnterpriseDTO();
        enterpriseDTO.setId(0L);
        enterpriseDTO.setUid("uid");
        enterpriseDTO.setEnterpriseGuid("enterpriseGuid");
        enterpriseDTO.setRegType("regType");
        enterpriseDTO.setManagementModel(EnterpriseDTO.ManagementModel.SINGLE);
        final EnterpriseQueryDTO query = new EnterpriseQueryDTO();
        query.setEnterpriseGuid("enterpriseGuid");
        query.setUid("uid");
        query.setRegTel("regTel");
        query.setDateType(0);
        query.setBeginTime(0L);
        when(mockEnterpriseClient.findEnterprise(query)).thenReturn(enterpriseDTO);

        // Configure RoleSourceMapper.selectList(...).
        final RoleSourceDO roleSourceDO = new RoleSourceDO();
        roleSourceDO.setRoleGuid("roleGuid");
        roleSourceDO.setTerminalGuid("terminalGuid");
        roleSourceDO.setTerminalCode("terminalCode");
        roleSourceDO.setTerminalName("terminalName");
        roleSourceDO.setMenuGuid("menuGuid");
        roleSourceDO.setSourceGuid("sourceGuid");
        roleSourceDO.setSourceCode("sourceCode");
        roleSourceDO.setSourceUrl("sourceUrl");
        final List<RoleSourceDO> roleSourceDOS = Arrays.asList(roleSourceDO);
        when(mockRoleSourceMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(roleSourceDOS);

        // Configure MenuMapper.selectList(...).
        final MenuDO menuDO = new MenuDO();
        menuDO.setId(0L);
        menuDO.setMenuSort(0L);
        menuDO.setModuleGuid("moduleGuid");
        menuDO.setTerminalGuid("terminalGuid");
        menuDO.setIsEnable(false);
        final List<MenuDO> menuDOS = Arrays.asList(menuDO);
        when(mockMenuMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(menuDOS);

        // Configure MenuMapStruct.menuDOList2DTOList(...).
        final MenuDO menuDO1 = new MenuDO();
        menuDO1.setId(0L);
        menuDO1.setMenuSort(0L);
        menuDO1.setModuleGuid("moduleGuid");
        menuDO1.setTerminalGuid("terminalGuid");
        menuDO1.setIsEnable(false);
        final List<MenuDO> menuDOList = Arrays.asList(menuDO1);
        when(mockMenuMapStruct.menuDOList2DTOList(menuDOList)).thenReturn(Collections.emptyList());

        // Run the test
        assertThatThrownBy(() -> roleDataServiceImplUnderTest.queryRoleData("roleGuid", "terminalGuid"))
                .isInstanceOf(BusinessException.class);
    }

    @Test
    public void testSaveRoleData() {
        // Setup
        final TerminalSourceDTO terminalSourceDTO = new TerminalSourceDTO();
        terminalSourceDTO.setRoleGuid("roleGuid");
        terminalSourceDTO.setTerminalGuid("terminalGuid");
        terminalSourceDTO.setTerminalCode("terminalCode");
        terminalSourceDTO.setTerminalName("terminalName");
        final MenuDTO menuDTO = new MenuDTO();
        menuDTO.setMenuGuid("menuGuid");
        menuDTO.setMenuName("menuName");
        menuDTO.setMenuSort(0L);
        menuDTO.setParentIds("parentIds");
        menuDTO.setModuleGuid("moduleGuid");
        menuDTO.setMenus(Arrays.asList(new MenuDTO()));
        final SourceDTO sourceDTO = new SourceDTO();
        sourceDTO.setSourceName("sourceName");
        sourceDTO.setSourceGuid("sourceGuid");
        sourceDTO.setSourceCode("sourceCode");
        sourceDTO.setSourceUrl("sourceUrl");
        sourceDTO.setIsChecked(false);
        menuDTO.setSourceDTOList(Arrays.asList(sourceDTO));
        menuDTO.setIsCheckedList(Arrays.asList("value"));
        terminalSourceDTO.setMenuDTOList(Arrays.asList(menuDTO));

        // Run the test
        final boolean result = roleDataServiceImplUnderTest.saveRoleData(terminalSourceDTO);

        // Verify the results
        assertThat(result).isFalse();
        verify(mockRoleSourceMapper).delete(any(LambdaQueryWrapper.class));
    }

    @Test
    public void testBatchSaveRoleData() {
        // Setup
        final RoleSourceDO roleSourceDO = new RoleSourceDO();
        roleSourceDO.setRoleGuid("roleGuid");
        roleSourceDO.setTerminalGuid("terminalGuid");
        roleSourceDO.setTerminalCode("terminalCode");
        roleSourceDO.setTerminalName("terminalName");
        roleSourceDO.setMenuGuid("menuGuid");
        roleSourceDO.setSourceGuid("sourceGuid");
        roleSourceDO.setSourceCode("sourceCode");
        roleSourceDO.setSourceUrl("sourceUrl");
        final List<RoleSourceDO> roleSourceDOList = Arrays.asList(roleSourceDO);

        // Run the test
        final boolean result = roleDataServiceImplUnderTest.batchSaveRoleData(roleSourceDOList);

        // Verify the results
        assertThat(result).isFalse();
    }
}
