package com.holderzone.saas.store.staff.utils;

import com.holderzone.framework.dds.starter.base.ServerProperties;
import com.holderzone.framework.dds.starter.utils.DynamicInfoHelper;
import com.holderzone.framework.dds.starter.utils.HttpHelper;
import org.junit.Before;
import org.junit.Test;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.client.RestTemplate;

import java.util.Arrays;

import static org.assertj.core.api.Assertions.assertThat;

public class DynamicHelperTest {

    private DynamicHelper dynamicHelperUnderTest;

    @Before
    public void setUp() throws Exception {
        dynamicHelperUnderTest = new DynamicHelper();
        ReflectionTestUtils.setField(dynamicHelperUnderTest, "openDynamicDatasource", false);
    }

    @Test
    public void testChangeDatasource() {
        // Setup
        // Run the test
        dynamicHelperUnderTest.changeDatasource("enterpriseGuid");

        // Verify the results
    }

    @Test
    public void testClear() {
        // Setup
        // Run the test
        dynamicHelperUnderTest.clear();

        // Verify the results
    }

    @Test
    public void testChangeRedis() {
        // Setup
        // Run the test
        dynamicHelperUnderTest.changeRedis("enterpriseGuid");

        // Verify the results
    }

    @Test
    public void testChangeAndCheckDatasource() {
        // Setup
        final ServerProperties serverProperties = new ServerProperties();
        serverProperties.setShardingTables("shardingTables");
        serverProperties.setServerCode("serverCode");
        final DynamicInfoHelper dynamicInfoHelper = new DynamicInfoHelper(null, null,
                new HttpHelper(new RestTemplate(Arrays.asList())), serverProperties);

        // Run the test
        final boolean result = DynamicHelper.changeAndCheckDatasource(dynamicInfoHelper, "enterpriseGuid");

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testGenerateGuid() {
        assertThat(dynamicHelperUnderTest.generateGuid("redisKey")).isEqualTo("result");
    }
}
