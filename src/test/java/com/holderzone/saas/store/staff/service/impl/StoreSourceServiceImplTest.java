package com.holderzone.saas.store.staff.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.resource.common.dto.authorization.ModuleResourceDTO;
import com.holderzone.resource.common.dto.authorization.ProductResource;
import com.holderzone.resource.common.dto.authorization.TerminalResourceDTO;
import com.holderzone.resource.common.dto.data.ServerDTO;
import com.holderzone.resource.common.dto.data.SourceDTO;
import com.holderzone.saas.store.staff.entity.domain.RoleSourceDO;
import com.holderzone.saas.store.staff.entity.domain.StoreSourceDO;
import com.holderzone.saas.store.staff.mapper.RoleSourceMapper;
import com.holderzone.saas.store.staff.mapper.StoreSourceMapper;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class StoreSourceServiceImplTest {

    @Mock
    private StoreSourceMapper mockStoreSourceMapper;
    @Mock
    private RoleSourceMapper mockRoleSourceMapper;

    private StoreSourceServiceImpl storeSourceServiceImplUnderTest;

    @Before
    public void setUp() {
        storeSourceServiceImplUnderTest = new StoreSourceServiceImpl(mockStoreSourceMapper, mockRoleSourceMapper);
    }

    @Test
    public void testSaveProductAuth() {
        // Setup
        final ProductResource productResource = new ProductResource();
        productResource.setChargeGuid("chargeGuid");
        productResource.setProductGuid("productGuid");
        final TerminalResourceDTO terminalResourceDTO = new TerminalResourceDTO();
        terminalResourceDTO.setTerminalGuid("terminalGuid");
        final ModuleResourceDTO moduleResourceDTO = new ModuleResourceDTO();
        moduleResourceDTO.setModuleResourceList(Arrays.asList(new ModuleResourceDTO()));
        moduleResourceDTO.setModuleGuid("moduleGuid");
        final ServerDTO serverDTO = new ServerDTO();
        final SourceDTO sourceDTO = new SourceDTO();
        sourceDTO.setSourceGuid("sourceGuid");
        sourceDTO.setName("name");
        sourceDTO.setUrl("sourceUrl");
        sourceDTO.setCode("sourceCode");
        serverDTO.setSourceList(Arrays.asList(sourceDTO));
        moduleResourceDTO.setServerList(Arrays.asList(serverDTO));
        moduleResourceDTO.setPageNo("pageNo");
        moduleResourceDTO.setTitle("pageTitle");
        moduleResourceDTO.setBusinessType("businessType");
        moduleResourceDTO.setModuleName("moduleName");
        terminalResourceDTO.setModuleResourceList(Arrays.asList(moduleResourceDTO));
        terminalResourceDTO.setTerminalName("terminalName");
        terminalResourceDTO.setTerminalCode("terminalCode");
        productResource.setTerminalList(Arrays.asList(terminalResourceDTO));

        // Run the test
        final boolean result = storeSourceServiceImplUnderTest.saveProductAuth(productResource, "storeGuid");

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testUpdateProductAuth() {
        // Setup
        final ProductResource productResource = new ProductResource();
        productResource.setChargeGuid("chargeGuid");
        productResource.setProductGuid("productGuid");
        final TerminalResourceDTO terminalResourceDTO = new TerminalResourceDTO();
        terminalResourceDTO.setTerminalGuid("terminalGuid");
        final ModuleResourceDTO moduleResourceDTO = new ModuleResourceDTO();
        moduleResourceDTO.setModuleResourceList(Arrays.asList(new ModuleResourceDTO()));
        moduleResourceDTO.setModuleGuid("moduleGuid");
        final ServerDTO serverDTO = new ServerDTO();
        final SourceDTO sourceDTO = new SourceDTO();
        sourceDTO.setSourceGuid("sourceGuid");
        sourceDTO.setName("name");
        sourceDTO.setUrl("sourceUrl");
        sourceDTO.setCode("sourceCode");
        serverDTO.setSourceList(Arrays.asList(sourceDTO));
        moduleResourceDTO.setServerList(Arrays.asList(serverDTO));
        moduleResourceDTO.setPageNo("pageNo");
        moduleResourceDTO.setTitle("pageTitle");
        moduleResourceDTO.setBusinessType("businessType");
        moduleResourceDTO.setModuleName("moduleName");
        terminalResourceDTO.setModuleResourceList(Arrays.asList(moduleResourceDTO));
        terminalResourceDTO.setTerminalName("terminalName");
        terminalResourceDTO.setTerminalCode("terminalCode");
        productResource.setTerminalList(Arrays.asList(terminalResourceDTO));

        // Configure StoreSourceMapper.selectList(...).
        final StoreSourceDO storeSourceDO = new StoreSourceDO();
        storeSourceDO.setId(0L);
        storeSourceDO.setStoreGuid("storeGuid");
        storeSourceDO.setProductGuid("productGuid");
        storeSourceDO.setChargeGuid("chargeGuid");
        storeSourceDO.setTerminalGuid("terminalGuid");
        storeSourceDO.setTerminalName("terminalName");
        storeSourceDO.setTerminalCode("terminalCode");
        storeSourceDO.setModuleGuid("moduleGuid");
        storeSourceDO.setModuleName("moduleName");
        storeSourceDO.setModuleType("businessType");
        storeSourceDO.setPageTitle("pageTitle");
        storeSourceDO.setPageUrl("pageNo");
        storeSourceDO.setSourceGuid("sourceGuid");
        storeSourceDO.setSourceName("name");
        storeSourceDO.setSourceCode("sourceCode");
        storeSourceDO.setSourceUrl("sourceUrl");
        storeSourceDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeSourceDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<StoreSourceDO> storeSourceDOS = Arrays.asList(storeSourceDO);
        when(mockStoreSourceMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(storeSourceDOS);

        // Configure RoleSourceMapper.selectList(...).
        final RoleSourceDO roleSourceDO = new RoleSourceDO();
        roleSourceDO.setId(0L);
        roleSourceDO.setTerminalGuid("terminalGuid");
        roleSourceDO.setSourceGuid("sourceGuid");
        roleSourceDO.setSourceCode("sourceCode");
        roleSourceDO.setSourceUrl("sourceUrl");
        final List<RoleSourceDO> roleSourceDOS = Arrays.asList(roleSourceDO);
        when(mockRoleSourceMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(roleSourceDOS);

        when(mockRoleSourceMapper.deleteUnnecessaryRoleSource(Arrays.asList("value"))).thenReturn(0);

        // Run the test
        final boolean result = storeSourceServiceImplUnderTest.updateProductAuth(productResource, "storeGuid");

        // Verify the results
        assertThat(result).isTrue();
        verify(mockStoreSourceMapper).deleteBatchIds(Arrays.asList("value"));

        // Confirm RoleSourceMapper.updateById(...).
        final RoleSourceDO entity = new RoleSourceDO();
        entity.setId(0L);
        entity.setTerminalGuid("terminalGuid");
        entity.setSourceGuid("sourceGuid");
        entity.setSourceCode("sourceCode");
        entity.setSourceUrl("sourceUrl");
        verify(mockRoleSourceMapper).updateById(entity);
    }

    @Test
    public void testUpdateProductAuth_StoreSourceMapperSelectListReturnsNoItems() {
        // Setup
        final ProductResource productResource = new ProductResource();
        productResource.setChargeGuid("chargeGuid");
        productResource.setProductGuid("productGuid");
        final TerminalResourceDTO terminalResourceDTO = new TerminalResourceDTO();
        terminalResourceDTO.setTerminalGuid("terminalGuid");
        final ModuleResourceDTO moduleResourceDTO = new ModuleResourceDTO();
        moduleResourceDTO.setModuleResourceList(Arrays.asList(new ModuleResourceDTO()));
        moduleResourceDTO.setModuleGuid("moduleGuid");
        final ServerDTO serverDTO = new ServerDTO();
        final SourceDTO sourceDTO = new SourceDTO();
        sourceDTO.setSourceGuid("sourceGuid");
        sourceDTO.setName("name");
        sourceDTO.setUrl("sourceUrl");
        sourceDTO.setCode("sourceCode");
        serverDTO.setSourceList(Arrays.asList(sourceDTO));
        moduleResourceDTO.setServerList(Arrays.asList(serverDTO));
        moduleResourceDTO.setPageNo("pageNo");
        moduleResourceDTO.setTitle("pageTitle");
        moduleResourceDTO.setBusinessType("businessType");
        moduleResourceDTO.setModuleName("moduleName");
        terminalResourceDTO.setModuleResourceList(Arrays.asList(moduleResourceDTO));
        terminalResourceDTO.setTerminalName("terminalName");
        terminalResourceDTO.setTerminalCode("terminalCode");
        productResource.setTerminalList(Arrays.asList(terminalResourceDTO));

        when(mockStoreSourceMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure RoleSourceMapper.selectList(...).
        final RoleSourceDO roleSourceDO = new RoleSourceDO();
        roleSourceDO.setId(0L);
        roleSourceDO.setTerminalGuid("terminalGuid");
        roleSourceDO.setSourceGuid("sourceGuid");
        roleSourceDO.setSourceCode("sourceCode");
        roleSourceDO.setSourceUrl("sourceUrl");
        final List<RoleSourceDO> roleSourceDOS = Arrays.asList(roleSourceDO);
        when(mockRoleSourceMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(roleSourceDOS);

        when(mockRoleSourceMapper.deleteUnnecessaryRoleSource(Arrays.asList("value"))).thenReturn(0);

        // Run the test
        final boolean result = storeSourceServiceImplUnderTest.updateProductAuth(productResource, "storeGuid");

        // Verify the results
        assertThat(result).isTrue();

        // Confirm RoleSourceMapper.updateById(...).
        final RoleSourceDO entity = new RoleSourceDO();
        entity.setId(0L);
        entity.setTerminalGuid("terminalGuid");
        entity.setSourceGuid("sourceGuid");
        entity.setSourceCode("sourceCode");
        entity.setSourceUrl("sourceUrl");
        verify(mockRoleSourceMapper).updateById(entity);
    }

    @Test
    public void testUpdateProductAuth_RoleSourceMapperSelectListReturnsNoItems() {
        // Setup
        final ProductResource productResource = new ProductResource();
        productResource.setChargeGuid("chargeGuid");
        productResource.setProductGuid("productGuid");
        final TerminalResourceDTO terminalResourceDTO = new TerminalResourceDTO();
        terminalResourceDTO.setTerminalGuid("terminalGuid");
        final ModuleResourceDTO moduleResourceDTO = new ModuleResourceDTO();
        moduleResourceDTO.setModuleResourceList(Arrays.asList(new ModuleResourceDTO()));
        moduleResourceDTO.setModuleGuid("moduleGuid");
        final ServerDTO serverDTO = new ServerDTO();
        final SourceDTO sourceDTO = new SourceDTO();
        sourceDTO.setSourceGuid("sourceGuid");
        sourceDTO.setName("name");
        sourceDTO.setUrl("sourceUrl");
        sourceDTO.setCode("sourceCode");
        serverDTO.setSourceList(Arrays.asList(sourceDTO));
        moduleResourceDTO.setServerList(Arrays.asList(serverDTO));
        moduleResourceDTO.setPageNo("pageNo");
        moduleResourceDTO.setTitle("pageTitle");
        moduleResourceDTO.setBusinessType("businessType");
        moduleResourceDTO.setModuleName("moduleName");
        terminalResourceDTO.setModuleResourceList(Arrays.asList(moduleResourceDTO));
        terminalResourceDTO.setTerminalName("terminalName");
        terminalResourceDTO.setTerminalCode("terminalCode");
        productResource.setTerminalList(Arrays.asList(terminalResourceDTO));

        // Configure StoreSourceMapper.selectList(...).
        final StoreSourceDO storeSourceDO = new StoreSourceDO();
        storeSourceDO.setId(0L);
        storeSourceDO.setStoreGuid("storeGuid");
        storeSourceDO.setProductGuid("productGuid");
        storeSourceDO.setChargeGuid("chargeGuid");
        storeSourceDO.setTerminalGuid("terminalGuid");
        storeSourceDO.setTerminalName("terminalName");
        storeSourceDO.setTerminalCode("terminalCode");
        storeSourceDO.setModuleGuid("moduleGuid");
        storeSourceDO.setModuleName("moduleName");
        storeSourceDO.setModuleType("businessType");
        storeSourceDO.setPageTitle("pageTitle");
        storeSourceDO.setPageUrl("pageNo");
        storeSourceDO.setSourceGuid("sourceGuid");
        storeSourceDO.setSourceName("name");
        storeSourceDO.setSourceCode("sourceCode");
        storeSourceDO.setSourceUrl("sourceUrl");
        storeSourceDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeSourceDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<StoreSourceDO> storeSourceDOS = Arrays.asList(storeSourceDO);
        when(mockStoreSourceMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(storeSourceDOS);

        when(mockRoleSourceMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());
        when(mockRoleSourceMapper.deleteUnnecessaryRoleSource(Arrays.asList("value"))).thenReturn(0);

        // Run the test
        final boolean result = storeSourceServiceImplUnderTest.updateProductAuth(productResource, "storeGuid");

        // Verify the results
        assertThat(result).isTrue();
        verify(mockStoreSourceMapper).deleteBatchIds(Arrays.asList("value"));
    }

    @Test
    public void testDeleteProductAuth() {
        // Setup
        // Run the test
        storeSourceServiceImplUnderTest.deleteProductAuth("storeGuid", "productGuid", "chargeGuid");

        // Verify the results
    }
}
