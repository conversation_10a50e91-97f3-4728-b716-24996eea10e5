package com.holderzone.saas.store.staff.utils;

import org.junit.Test;

import java.util.HashMap;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;

public class HttpsClientUtilsTest {

    @Test
    public void testDoGet1() {
        assertThat(HttpsClientUtils.doGet("url")).isEqualTo("result");
    }

    @Test
    public void testDoGet2() {
        // Setup
        final Map<String, Object> params = new HashMap<>();

        // Run the test
        final String result = HttpsClientUtils.doGet("url", params);

        // Verify the results
        assertThat(result).isEqualTo("result");
    }

    @Test
    public void testDoPost1() {
        assertThat(HttpsClientUtils.doPost("apiUrl")).isEqualTo("result");
    }

    @Test
    public void testDoPost2() {
        // Setup
        final Map<String, Object> params = new HashMap<>();

        // Run the test
        final String result = HttpsClientUtils.doPost("apiUrl", params);

        // Verify the results
        assertThat(result).isEqualTo("result");
    }

    @Test
    public void testDoPost3() {
        assertThat(HttpsClientUtils.doPost("apiUrl", "json")).isEqualTo("result");
    }

    @Test
    public void testDoPostJSON() {
        assertThat(HttpsClientUtils.doPostJSON("apiUrl", "date")).isEqualTo("result");
    }
}
