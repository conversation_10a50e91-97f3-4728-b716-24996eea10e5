package com.holderzone.saas.store.staff;

import com.holderzone.saas.store.dto.user.MenuDTO;
import com.holderzone.saas.store.staff.entity.domain.MenuDO;
import com.holderzone.saas.store.staff.mapstruct.MenuMapStruct;
import com.holderzone.saas.store.staff.service.MenuService;
import com.holderzone.saas.store.staff.utils.DynamicHelper;
import com.holderzone.saas.store.staff.utils.SpringContextUtils;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@Slf4j
@SpringBootTest
@RunWith(SpringRunner.class)
public class UserTests {

    @Autowired
    private WebApplicationContext wac;

    @Autowired
    private ApplicationContext app;

    @Autowired
    private DynamicHelper dynamicHelper;

    @Autowired
    private MenuService menuService;

    @Autowired
    private MenuMapStruct menuMapStruct;

    private MockMvc mockMvc;

    @Before
    public void setup() {
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();
        SpringContextUtils.getInstance().setCfgContext((ConfigurableApplicationContext) app);
        dynamicHelper.changeDatasource("6506431195651982337");
        dynamicHelper.changeRedis("6506431195651982337");
        UserContextUtils
                .put(
                        "{\"userGuid\":\"6511894347305189377\",\"enterpriseGuid\":\"6506431195651982337\",\"enterpriseName\":\"test\",\"name\":\"默认管理员\",\"tel\":\"***********\",\"account\":\"960311\",\"storeNo\":\"\"}");
    }

    @Test
    public  void  getSourceModuleByUser() throws Exception {
        MvcResult mvcResult = mockMvc.perform(post("/menu/get_module_source_by_user?terminalCode=3")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }

    @Test
    public void testNewAccount() {
        
    }

    @Test
    public void testNewAuthCode() {
        
    }

    @Test
    public void testCreateUser() {
        
    }

    @Test
    public void testUpdateUser() {
        
    }

    @Test
    public void testQueryUser() {
        
    }

    @Test
    public void testEnableUser() {
        
    }

    @Test
    public void testDisableUser() {
        
    }

    @Test
    public void testDeleteUser() {
        
    }

    @Test
    public void testPageQueryUser() {
        
    }

    @Test
    public void testUpdateUserPwd() {
        
    }


    @Test
    public void testResetUserPwd() {
        
    }

    /**
     * 测试下如果parentId字段存的是为上级的id而不是上级ids情况下的的遍历逻辑
     * 结果：貌似逻辑都差不多，可预见处理时间可能两种方式差别不大
     */
    @Test
    public void testQueryMenuTree() {
        List<MenuDO> original = menuService.list(null);

        // 最顶级菜单的parentId肯定不在当前的菜单列表中
        // 数据量不大情况下不用区分是否为最顶级菜单或其他菜单
        List<MenuDTO> firstChild = original.stream().filter(p -> original.stream().noneMatch(k -> k.getMenuGuid().equals(p.getParentIds())))
                                           .map(menuMapStruct::menuDO2DTO).collect(Collectors.toList());

        firstChild.forEach(p -> p.setMenus(this.findChildMenuById(menuMapStruct.menuDOList2DTOList(original), p.getMenuGuid())));
    }

    private List<MenuDTO> findChildMenuById(List<MenuDTO> menuDTOList, String parentId) {
        List<MenuDTO> firstChild = new ArrayList<>();
        List<MenuDTO> notFirstChild = menuDTOList.stream().filter(p -> {
            boolean flag = p.getParentIds().equals(parentId);
            if (p.getParentIds().equals(parentId)) {
                firstChild.add(p);
            }
            return !flag;
        }).collect(Collectors.toList());

        if (!firstChild.isEmpty()) {
            firstChild.forEach(p -> p.setMenus(this.findChildMenuById(notFirstChild, p.getMenuGuid())));
        }
        return firstChild.isEmpty() ? Collections.emptyList() : firstChild;
    }
}
