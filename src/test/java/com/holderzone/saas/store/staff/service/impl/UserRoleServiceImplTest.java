package com.holderzone.saas.store.staff.service.impl;

import com.holderzone.saas.store.dto.user.RoleDTO;
import com.holderzone.saas.store.staff.service.DistributedService;
import com.holderzone.saas.store.staff.service.RedisService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class UserRoleServiceImplTest {

    @Mock
    private DistributedService mockDistributedService;
    @Mock
    private RedisService mockRedisService;

    private UserRoleServiceImpl userRoleServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        userRoleServiceImplUnderTest = new UserRoleServiceImpl(mockDistributedService);
        ReflectionTestUtils.setField(userRoleServiceImplUnderTest, "redisService", mockRedisService);
    }

    @Test
    public void testAddUserRoleRelation() {
        // Setup
        final RoleDTO roleDTO = new RoleDTO();
        roleDTO.setGuid("guid");
        roleDTO.setName("name");
        roleDTO.setIsEnable(false);
        roleDTO.setIsDeleted(false);
        roleDTO.setCreateStaffGuid("createStaffGuid");
        final List<RoleDTO> arrayOfRoleDTO = Arrays.asList(roleDTO);
        when(mockDistributedService.nextBatchUserRoleGuid(0L)).thenReturn(Arrays.asList("value"));

        // Run the test
        userRoleServiceImplUnderTest.addUserRoleRelation("userGuid", arrayOfRoleDTO);

        // Verify the results
    }

    @Test
    public void testAddUserRoleRelation_DistributedServiceReturnsNoItems() {
        // Setup
        final RoleDTO roleDTO = new RoleDTO();
        roleDTO.setGuid("guid");
        roleDTO.setName("name");
        roleDTO.setIsEnable(false);
        roleDTO.setIsDeleted(false);
        roleDTO.setCreateStaffGuid("createStaffGuid");
        final List<RoleDTO> arrayOfRoleDTO = Arrays.asList(roleDTO);
        when(mockDistributedService.nextBatchUserRoleGuid(0L)).thenReturn(Collections.emptyList());

        // Run the test
        userRoleServiceImplUnderTest.addUserRoleRelation("userGuid", arrayOfRoleDTO);

        // Verify the results
    }

    @Test
    public void testUpdateUserRoleRelation() {
        // Setup
        final RoleDTO roleDTO = new RoleDTO();
        roleDTO.setGuid("guid");
        roleDTO.setName("name");
        roleDTO.setIsEnable(false);
        roleDTO.setIsDeleted(false);
        roleDTO.setCreateStaffGuid("createStaffGuid");
        final List<RoleDTO> arrayOfRoleDTO = Arrays.asList(roleDTO);
        when(mockDistributedService.nextBatchUserRoleGuid(0L)).thenReturn(Arrays.asList("value"));

        // Run the test
        userRoleServiceImplUnderTest.updateUserRoleRelation("userGuid", arrayOfRoleDTO);

        // Verify the results
    }

    @Test
    public void testUpdateUserRoleRelation_DistributedServiceReturnsNoItems() {
        // Setup
        final RoleDTO roleDTO = new RoleDTO();
        roleDTO.setGuid("guid");
        roleDTO.setName("name");
        roleDTO.setIsEnable(false);
        roleDTO.setIsDeleted(false);
        roleDTO.setCreateStaffGuid("createStaffGuid");
        final List<RoleDTO> arrayOfRoleDTO = Arrays.asList(roleDTO);
        when(mockDistributedService.nextBatchUserRoleGuid(0L)).thenReturn(Collections.emptyList());

        // Run the test
        userRoleServiceImplUnderTest.updateUserRoleRelation("userGuid", arrayOfRoleDTO);

        // Verify the results
    }

    @Test
    public void testDeleteUserRoleRelation() {
        // Setup
        // Run the test
        userRoleServiceImplUnderTest.deleteUserRoleRelation("userGuid");

        // Verify the results
    }

    @Test
    public void testFindRoleByUserGuid() {
        assertThat(userRoleServiceImplUnderTest.findRoleByUserGuid("userGuid")).isNull();
    }

    @Test
    public void testBatchDeleteUserRoleRelation() {
        // Setup
        // Run the test
        userRoleServiceImplUnderTest.batchDeleteUserRoleRelation(Arrays.asList("value"));

        // Verify the results
    }
}
