package com.holderzone.saas.store.staff.service.impl;

import com.holderzone.framework.rocketmq.common.DefaultRocketMqProducer;
import com.holderzone.saas.store.staff.entity.domain.UserDO;
import org.apache.rocketmq.common.message.Message;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;

@RunWith(MockitoJUnitRunner.class)
public class UserUploadServiceImplTest {

    @Mock
    private DefaultRocketMqProducer mockRocketMqProducer;

    private UserUploadServiceImpl userUploadServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        userUploadServiceImplUnderTest = new UserUploadServiceImpl(mockRocketMqProducer);
    }

    @Test
    public void testAddUser() {
        // Setup
        final UserDO userDO = new UserDO();
        userDO.setGuid("userGuid");
        userDO.setEnterpriseNo("enterpriseNo");
        userDO.setAccount("account");
        userDO.setPassword("password");
        userDO.setAuthCode("authCode");
        userDO.setName("name");
        userDO.setPhone("phone");
        userDO.setAddress("address");
        userDO.setBirthday(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        userDO.setOnBoardingTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        userDO.setIsEnable(false);
        userDO.setRegType("regType");

        // Run the test
        userUploadServiceImplUnderTest.addUser(userDO);

        // Verify the results
        verify(mockRocketMqProducer).sendMessage(any(Message.class));
    }

    @Test
    public void testUpdateUser() {
        // Setup
        final UserDO userDO = new UserDO();
        userDO.setGuid("userGuid");
        userDO.setEnterpriseNo("enterpriseNo");
        userDO.setAccount("account");
        userDO.setPassword("password");
        userDO.setAuthCode("authCode");
        userDO.setName("name");
        userDO.setPhone("phone");
        userDO.setAddress("address");
        userDO.setBirthday(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        userDO.setOnBoardingTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        userDO.setIsEnable(false);
        userDO.setRegType("regType");

        // Run the test
        userUploadServiceImplUnderTest.updateUser(userDO);

        // Verify the results
        verify(mockRocketMqProducer).sendMessage(any(Message.class));
    }

    @Test
    public void testDeleteUser() {
        // Setup
        // Run the test
        userUploadServiceImplUnderTest.deleteUser("userGuid");

        // Verify the results
        verify(mockRocketMqProducer).sendMessage(any(Message.class));
    }
}
