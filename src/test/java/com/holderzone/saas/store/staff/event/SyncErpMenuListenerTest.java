package com.holderzone.saas.store.staff.event;

import com.holderzone.framework.dds.starter.utils.DynamicInfoHelper;
import com.holderzone.resource.common.dto.mq.UnMessage;
import com.holderzone.resource.common.dto.product.MenuDTO;
import com.holderzone.saas.store.staff.service.MenuService;
import com.holderzone.saas.store.staff.service.remote.EnterpriseClient;
import com.holderzone.saas.store.staff.service.remote.EnterpriseDataClient;
import com.holderzone.saas.store.staff.utils.DynamicHelper;
import org.apache.rocketmq.common.message.MessageExt;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.net.InetSocketAddress;
import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class SyncErpMenuListenerTest {

    @Mock
    private MenuService mockMenuService;
    @Mock
    private DynamicHelper mockDynamicHelper;
    @Mock
    private EnterpriseClient mockEnterpriseClient;
    @Mock
    private EnterpriseDataClient mockEnterpriseDataClient;
    @Mock
    private DynamicInfoHelper mockDynamicInfoHelper;

    private SyncErpMenuListener syncErpMenuListenerUnderTest;

    @Before
    public void setUp() throws Exception {
        syncErpMenuListenerUnderTest = new SyncErpMenuListener(mockMenuService, mockDynamicHelper, mockEnterpriseClient,
                mockEnterpriseDataClient, mockDynamicInfoHelper);
    }

    @Test
    public void testConsumeMsg() {
        // Setup
        final UnMessage<String> unMessage = new UnMessage<>("messageType", "enterpriseGuid", "storeGuid", "message");
        final MessageExt messageExt = new MessageExt(0, 0L, new InetSocketAddress("localhost", 80), 0L,
                new InetSocketAddress("localhost", 80), "msgId");

        // Configure MenuService.insertMenu(...).
        final MenuDTO menuDTO = new MenuDTO();
        menuDTO.setTerminalName("terminalName");
        menuDTO.setUrlGuid("urlGuid");
        menuDTO.setUrlName("urlName");
        menuDTO.setMenus(Arrays.asList(new MenuDTO()));
        menuDTO.setMenuCode("menuCode");
        final List<MenuDTO> menuList = Arrays.asList(menuDTO);
        when(mockMenuService.insertMenu(menuList)).thenReturn(false);

        when(mockMenuService.updateMenu(any(MenuDTO.class))).thenReturn(false);
        when(mockMenuService.deleteMenu("message")).thenReturn(false);

        // Configure MenuService.bindMenuAndPage(...).
        final MenuDTO menuDTO1 = new MenuDTO();
        menuDTO1.setTerminalName("terminalName");
        menuDTO1.setUrlGuid("urlGuid");
        menuDTO1.setUrlName("urlName");
        menuDTO1.setMenus(Arrays.asList(new MenuDTO()));
        menuDTO1.setMenuCode("menuCode");
        final List<MenuDTO> menuList1 = Arrays.asList(menuDTO1);
        when(mockMenuService.bindMenuAndPage(menuList1)).thenReturn(false);

        // Run the test
        final boolean result = syncErpMenuListenerUnderTest.consumeMsg(unMessage, messageExt);

        // Verify the results
        assertThat(result).isFalse();
        verify(mockDynamicHelper).clear();
    }

    @Test
    public void testConsumeMsg_MenuServiceInsertMenuReturnsTrue() {
        // Setup
        final UnMessage<String> unMessage = new UnMessage<>("messageType", "enterpriseGuid", "storeGuid", "message");
        final MessageExt messageExt = new MessageExt(0, 0L, new InetSocketAddress("localhost", 80), 0L,
                new InetSocketAddress("localhost", 80), "msgId");

        // Configure MenuService.insertMenu(...).
        final MenuDTO menuDTO = new MenuDTO();
        menuDTO.setTerminalName("terminalName");
        menuDTO.setUrlGuid("urlGuid");
        menuDTO.setUrlName("urlName");
        menuDTO.setMenus(Arrays.asList(new MenuDTO()));
        menuDTO.setMenuCode("menuCode");
        final List<MenuDTO> menuList = Arrays.asList(menuDTO);
        when(mockMenuService.insertMenu(menuList)).thenReturn(true);

        // Run the test
        final boolean result = syncErpMenuListenerUnderTest.consumeMsg(unMessage, messageExt);

        // Verify the results
        assertThat(result).isTrue();
        verify(mockDynamicHelper).clear();
    }

    @Test
    public void testConsumeMsg_MenuServiceUpdateMenuReturnsTrue() {
        // Setup
        final UnMessage<String> unMessage = new UnMessage<>("messageType", "enterpriseGuid", "storeGuid", "message");
        final MessageExt messageExt = new MessageExt(0, 0L, new InetSocketAddress("localhost", 80), 0L,
                new InetSocketAddress("localhost", 80), "msgId");
        when(mockMenuService.updateMenu(any(MenuDTO.class))).thenReturn(true);

        // Run the test
        final boolean result = syncErpMenuListenerUnderTest.consumeMsg(unMessage, messageExt);

        // Verify the results
        assertThat(result).isTrue();
        verify(mockDynamicHelper).clear();
    }

    @Test
    public void testConsumeMsg_MenuServiceDeleteMenuReturnsTrue() {
        // Setup
        final UnMessage<String> unMessage = new UnMessage<>("messageType", "enterpriseGuid", "storeGuid", "message");
        final MessageExt messageExt = new MessageExt(0, 0L, new InetSocketAddress("localhost", 80), 0L,
                new InetSocketAddress("localhost", 80), "msgId");
        when(mockMenuService.deleteMenu("message")).thenReturn(true);

        // Run the test
        final boolean result = syncErpMenuListenerUnderTest.consumeMsg(unMessage, messageExt);

        // Verify the results
        assertThat(result).isTrue();
        verify(mockDynamicHelper).clear();
    }

    @Test
    public void testConsumeMsg_MenuServiceBindMenuAndPageReturnsTrue() {
        // Setup
        final UnMessage<String> unMessage = new UnMessage<>("messageType", "enterpriseGuid", "storeGuid", "message");
        final MessageExt messageExt = new MessageExt(0, 0L, new InetSocketAddress("localhost", 80), 0L,
                new InetSocketAddress("localhost", 80), "msgId");

        // Configure MenuService.bindMenuAndPage(...).
        final MenuDTO menuDTO = new MenuDTO();
        menuDTO.setTerminalName("terminalName");
        menuDTO.setUrlGuid("urlGuid");
        menuDTO.setUrlName("urlName");
        menuDTO.setMenus(Arrays.asList(new MenuDTO()));
        menuDTO.setMenuCode("menuCode");
        final List<MenuDTO> menuList = Arrays.asList(menuDTO);
        when(mockMenuService.bindMenuAndPage(menuList)).thenReturn(true);

        // Run the test
        final boolean result = syncErpMenuListenerUnderTest.consumeMsg(unMessage, messageExt);

        // Verify the results
        assertThat(result).isTrue();
        verify(mockDynamicHelper).clear();
    }
}
