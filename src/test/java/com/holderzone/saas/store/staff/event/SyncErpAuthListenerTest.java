package com.holderzone.saas.store.staff.event;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.holderzone.framework.dds.starter.utils.DynamicInfoHelper;
import com.holderzone.resource.common.dto.authorization.ProductResource;
import com.holderzone.saas.store.staff.service.StoreSourceService;
import com.holderzone.saas.store.staff.service.remote.EnterpriseClient;
import com.holderzone.saas.store.staff.service.remote.EnterpriseDataClient;
import com.holderzone.saas.store.staff.utils.DynamicHelper;
import org.apache.rocketmq.common.message.MessageExt;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.RedisTemplate;

import java.net.InetSocketAddress;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class SyncErpAuthListenerTest {

    @Mock
    private StoreSourceService mockStoreSourceService;
    @Mock
    private DynamicHelper mockDynamicHelper;
    @Mock
    private RedisTemplate mockRedisTemplate;
    @Mock
    private EnterpriseClient mockEnterpriseClient;
    @Mock
    private EnterpriseDataClient mockEnterpriseDataClient;
    @Mock
    private DynamicInfoHelper mockDynamicInfoHelper;

    private SyncErpAuthListener syncErpAuthListenerUnderTest;

    @Before
    public void setUp() throws Exception {
        syncErpAuthListenerUnderTest = new SyncErpAuthListener(mockStoreSourceService, mockDynamicHelper,
                mockRedisTemplate, mockEnterpriseClient, mockEnterpriseDataClient, mockDynamicInfoHelper,
                new ObjectMapper());
    }

    @Test
    public void testConsumeMsg() {
        // Setup
        final MessageExt messageExt = new MessageExt(0, 0L, new InetSocketAddress("localhost", 80), 0L,
                new InetSocketAddress("localhost", 80), "msgId");
        when(mockStoreSourceService.count(any(LambdaQueryWrapper.class))).thenReturn(0);
        when(mockStoreSourceService.saveProductAuth(any(ProductResource.class), eq("storeGuid"))).thenReturn(false);
        when(mockStoreSourceService.updateProductAuth(any(ProductResource.class), eq("storeGuid"))).thenReturn(false);

        // Run the test
        final boolean result = syncErpAuthListenerUnderTest.consumeMsg("message", messageExt);

        // Verify the results
        assertThat(result).isFalse();
        verify(mockDynamicHelper).clear();
    }

    @Test
    public void testConsumeMsg_StoreSourceServiceSaveProductAuthReturnsTrue() {
        // Setup
        final MessageExt messageExt = new MessageExt(0, 0L, new InetSocketAddress("localhost", 80), 0L,
                new InetSocketAddress("localhost", 80), "msgId");
        when(mockStoreSourceService.count(any(LambdaQueryWrapper.class))).thenReturn(0);
        when(mockStoreSourceService.saveProductAuth(any(ProductResource.class), eq("storeGuid"))).thenReturn(true);

        // Run the test
        final boolean result = syncErpAuthListenerUnderTest.consumeMsg("message", messageExt);

        // Verify the results
        assertThat(result).isTrue();
        verify(mockDynamicHelper).clear();
    }

    @Test
    public void testConsumeMsg_StoreSourceServiceUpdateProductAuthReturnsTrue() {
        // Setup
        final MessageExt messageExt = new MessageExt(0, 0L, new InetSocketAddress("localhost", 80), 0L,
                new InetSocketAddress("localhost", 80), "msgId");
        when(mockStoreSourceService.count(any(LambdaQueryWrapper.class))).thenReturn(0);
        when(mockStoreSourceService.updateProductAuth(any(ProductResource.class), eq("storeGuid"))).thenReturn(true);

        // Run the test
        final boolean result = syncErpAuthListenerUnderTest.consumeMsg("message", messageExt);

        // Verify the results
        assertThat(result).isTrue();
        verify(mockDynamicHelper).clear();
    }
}
