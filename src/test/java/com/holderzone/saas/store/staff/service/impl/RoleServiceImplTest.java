package com.holderzone.saas.store.staff.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.user.RoleDTO;
import com.holderzone.saas.store.dto.user.RoleQueryDTO;
import com.holderzone.saas.store.staff.entity.domain.RoleDO;
import com.holderzone.saas.store.staff.entity.domain.RoleSourceDO;
import com.holderzone.saas.store.staff.mapper.RoleMapper;
import com.holderzone.saas.store.staff.mapper.RoleSourceMapper;
import com.holderzone.saas.store.staff.mapper.UserRoleMapper;
import com.holderzone.saas.store.staff.mapstruct.RoleMapStruct;
import com.holderzone.saas.store.staff.service.RoleDataService;
import com.holderzone.saas.store.staff.service.UserDataService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.RedisTemplate;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class RoleServiceImplTest {

    @Mock
    private RoleMapStruct mockRoleMapStruct;
    @Mock
    private RoleMapper mockRoleMapper;
    @Mock
    private UserRoleMapper mockUserRoleMapper;
    @Mock
    private RedisTemplate mockRedisTemplate;
    @Mock
    private RoleSourceMapper mockRoleSourceMapper;
    @Mock
    private RoleDataService mockRoleDataService;
    @Mock
    private UserDataService mockUserDataService;

    private RoleServiceImpl roleServiceImplUnderTest;

    @Before
    public void setUp() {
        roleServiceImplUnderTest = new RoleServiceImpl(mockRoleMapStruct, mockRoleMapper, mockUserRoleMapper,
                mockRedisTemplate, mockRoleSourceMapper, mockRoleDataService, mockUserDataService);
    }

    @Test
    public void testCreateRole() {
        // Setup
        final RoleDTO roleDTO = new RoleDTO();
        roleDTO.setGuid("be0e027b-c57c-45a8-98f5-54d17bf0b4ce");
        roleDTO.setName("name");
        roleDTO.setIsEnable(false);
        roleDTO.setIsDeleted(false);
        roleDTO.setCreateStaffGuid("createStaffGuid");

        // Configure RoleMapStruct.roleDTO2DO(...).
        final RoleDO roleDO = new RoleDO();
        roleDO.setGuid("guid");
        roleDO.setName("name");
        roleDO.setCreateStaffGuid("createStaffGuid");
        roleDO.setModifiedStaffGuid("modifiedStaffGuid");
        roleDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        roleDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final RoleDTO roleDTO1 = new RoleDTO();
        roleDTO1.setGuid("be0e027b-c57c-45a8-98f5-54d17bf0b4ce");
        roleDTO1.setName("name");
        roleDTO1.setIsEnable(false);
        roleDTO1.setIsDeleted(false);
        roleDTO1.setCreateStaffGuid("createStaffGuid");
        when(mockRoleMapStruct.roleDTO2DO(roleDTO1)).thenReturn(roleDO);

        // Run the test
        final boolean result = roleServiceImplUnderTest.createRole(roleDTO);

        // Verify the results
        assertThat(result).isFalse();
        verify(mockUserDataService).appendRole("guid");
    }

    @Test
    public void testUpdateRole() {
        // Setup
        final RoleDTO roleDTO = new RoleDTO();
        roleDTO.setGuid("be0e027b-c57c-45a8-98f5-54d17bf0b4ce");
        roleDTO.setName("name");
        roleDTO.setIsEnable(false);
        roleDTO.setIsDeleted(false);
        roleDTO.setCreateStaffGuid("createStaffGuid");

        // Configure RoleMapStruct.roleDTO2DO(...).
        final RoleDO roleDO = new RoleDO();
        roleDO.setGuid("guid");
        roleDO.setName("name");
        roleDO.setCreateStaffGuid("createStaffGuid");
        roleDO.setModifiedStaffGuid("modifiedStaffGuid");
        roleDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        roleDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final RoleDTO roleDTO1 = new RoleDTO();
        roleDTO1.setGuid("be0e027b-c57c-45a8-98f5-54d17bf0b4ce");
        roleDTO1.setName("name");
        roleDTO1.setIsEnable(false);
        roleDTO1.setIsDeleted(false);
        roleDTO1.setCreateStaffGuid("createStaffGuid");
        when(mockRoleMapStruct.roleDTO2DO(roleDTO1)).thenReturn(roleDO);

        // Run the test
        final boolean result = roleServiceImplUnderTest.updateRole(roleDTO);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testQueryPageByName() {
        // Setup
        final RoleQueryDTO roleQueryDTO = new RoleQueryDTO();
        roleQueryDTO.setCurrentPage(0L);
        roleQueryDTO.setPageSize(0L);
        roleQueryDTO.setRoleName("roleName");
        roleQueryDTO.setOffsetIndex(0L);

        // Configure RoleMapper.countByRoleQueryDTO(...).
        final RoleQueryDTO roleQueryDTO1 = new RoleQueryDTO();
        roleQueryDTO1.setCurrentPage(0L);
        roleQueryDTO1.setPageSize(0L);
        roleQueryDTO1.setRoleName("roleName");
        roleQueryDTO1.setOffsetIndex(0L);
        when(mockRoleMapper.countByRoleQueryDTO(roleQueryDTO1)).thenReturn(0);

        // Configure RoleMapper.selectListByRoleQueryDTO(...).
        final RoleDTO roleDTO = new RoleDTO();
        roleDTO.setGuid("be0e027b-c57c-45a8-98f5-54d17bf0b4ce");
        roleDTO.setName("name");
        roleDTO.setIsEnable(false);
        roleDTO.setIsDeleted(false);
        roleDTO.setCreateStaffGuid("createStaffGuid");
        final List<RoleDTO> roleDTOS = Arrays.asList(roleDTO);
        final RoleQueryDTO roleQueryDTO2 = new RoleQueryDTO();
        roleQueryDTO2.setCurrentPage(0L);
        roleQueryDTO2.setPageSize(0L);
        roleQueryDTO2.setRoleName("roleName");
        roleQueryDTO2.setOffsetIndex(0L);
        when(mockRoleMapper.selectListByRoleQueryDTO(roleQueryDTO2)).thenReturn(roleDTOS);

        // Run the test
        final Page<RoleDTO> result = roleServiceImplUnderTest.queryPageByName(roleQueryDTO);

        // Verify the results
    }

    @Test
    public void testQueryPageByName_RoleMapperCountByRoleQueryDTOReturnsNull() {
        // Setup
        final RoleQueryDTO roleQueryDTO = new RoleQueryDTO();
        roleQueryDTO.setCurrentPage(0L);
        roleQueryDTO.setPageSize(0L);
        roleQueryDTO.setRoleName("roleName");
        roleQueryDTO.setOffsetIndex(0L);

        // Configure RoleMapper.countByRoleQueryDTO(...).
        final RoleQueryDTO roleQueryDTO1 = new RoleQueryDTO();
        roleQueryDTO1.setCurrentPage(0L);
        roleQueryDTO1.setPageSize(0L);
        roleQueryDTO1.setRoleName("roleName");
        roleQueryDTO1.setOffsetIndex(0L);
        when(mockRoleMapper.countByRoleQueryDTO(roleQueryDTO1)).thenReturn(null);

        // Run the test
        final Page<RoleDTO> result = roleServiceImplUnderTest.queryPageByName(roleQueryDTO);

        // Verify the results
    }

    @Test
    public void testQueryPageByName_RoleMapperSelectListByRoleQueryDTOReturnsNoItems() {
        // Setup
        final RoleQueryDTO roleQueryDTO = new RoleQueryDTO();
        roleQueryDTO.setCurrentPage(0L);
        roleQueryDTO.setPageSize(0L);
        roleQueryDTO.setRoleName("roleName");
        roleQueryDTO.setOffsetIndex(0L);

        // Configure RoleMapper.countByRoleQueryDTO(...).
        final RoleQueryDTO roleQueryDTO1 = new RoleQueryDTO();
        roleQueryDTO1.setCurrentPage(0L);
        roleQueryDTO1.setPageSize(0L);
        roleQueryDTO1.setRoleName("roleName");
        roleQueryDTO1.setOffsetIndex(0L);
        when(mockRoleMapper.countByRoleQueryDTO(roleQueryDTO1)).thenReturn(0);

        // Configure RoleMapper.selectListByRoleQueryDTO(...).
        final RoleQueryDTO roleQueryDTO2 = new RoleQueryDTO();
        roleQueryDTO2.setCurrentPage(0L);
        roleQueryDTO2.setPageSize(0L);
        roleQueryDTO2.setRoleName("roleName");
        roleQueryDTO2.setOffsetIndex(0L);
        when(mockRoleMapper.selectListByRoleQueryDTO(roleQueryDTO2)).thenReturn(Collections.emptyList());

        // Run the test
        final Page<RoleDTO> result = roleServiceImplUnderTest.queryPageByName(roleQueryDTO);

        // Verify the results
    }

    @Test
    public void testDeleteRole() {
        // Setup
        when(mockUserRoleMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(0);

        // Run the test
        final boolean result = roleServiceImplUnderTest.deleteRole("roleGuid");

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testDeleteRole_UserRoleMapperReturnsNull() {
        // Setup
        when(mockUserRoleMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> roleServiceImplUnderTest.deleteRole("roleGuid")).isInstanceOf(BusinessException.class);
    }

    @Test
    public void testQueryExistUser() {
        // Setup
        when(mockUserRoleMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(0);

        // Run the test
        final boolean result = roleServiceImplUnderTest.queryExistUser("roleGuid");

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testQueryExistUser_UserRoleMapperReturnsNull() {
        // Setup
        when(mockUserRoleMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Run the test
        final boolean result = roleServiceImplUnderTest.queryExistUser("roleGuid");

        // Verify the results
        assertThat(result).isTrue();
    }

    @Test
    public void testCopyRole() {
        // Setup
        // Configure RoleMapper.selectOne(...).
        final RoleDO roleDO = new RoleDO();
        roleDO.setGuid("guid");
        roleDO.setName("name");
        roleDO.setCreateStaffGuid("createStaffGuid");
        roleDO.setModifiedStaffGuid("modifiedStaffGuid");
        roleDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        roleDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockRoleMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(roleDO);

        // Configure RoleMapper.selectList(...).
        final RoleDO roleDO1 = new RoleDO();
        roleDO1.setGuid("guid");
        roleDO1.setName("name");
        roleDO1.setCreateStaffGuid("createStaffGuid");
        roleDO1.setModifiedStaffGuid("modifiedStaffGuid");
        roleDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        roleDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<RoleDO> roleDOS = Arrays.asList(roleDO1);
        when(mockRoleMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(roleDOS);

        // Configure RoleSourceMapper.selectList(...).
        final RoleSourceDO roleSourceDO = new RoleSourceDO();
        roleSourceDO.setId(0L);
        roleSourceDO.setRoleGuid("roleGuid");
        roleSourceDO.setTerminalGuid("terminalGuid");
        roleSourceDO.setTerminalCode("terminalCode");
        roleSourceDO.setTerminalName("terminalName");
        final List<RoleSourceDO> roleSourceDOS = Arrays.asList(roleSourceDO);
        when(mockRoleSourceMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(roleSourceDOS);

        // Configure RoleDataService.batchSaveRoleData(...).
        final RoleSourceDO roleSourceDO1 = new RoleSourceDO();
        roleSourceDO1.setId(0L);
        roleSourceDO1.setRoleGuid("roleGuid");
        roleSourceDO1.setTerminalGuid("terminalGuid");
        roleSourceDO1.setTerminalCode("terminalCode");
        roleSourceDO1.setTerminalName("terminalName");
        final List<RoleSourceDO> roleSourceDOList = Arrays.asList(roleSourceDO1);
        when(mockRoleDataService.batchSaveRoleData(roleSourceDOList)).thenReturn(false);

        // Run the test
        final boolean result = roleServiceImplUnderTest.copyRole("roleGuid");

        // Verify the results
        assertThat(result).isFalse();
        verify(mockUserDataService).appendRole("roleGuid");
    }

    @Test
    public void testCopyRole_RoleMapperSelectListReturnsNoItems() {
        // Setup
        // Configure RoleMapper.selectOne(...).
        final RoleDO roleDO = new RoleDO();
        roleDO.setGuid("guid");
        roleDO.setName("name");
        roleDO.setCreateStaffGuid("createStaffGuid");
        roleDO.setModifiedStaffGuid("modifiedStaffGuid");
        roleDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        roleDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockRoleMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(roleDO);

        when(mockRoleMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure RoleSourceMapper.selectList(...).
        final RoleSourceDO roleSourceDO = new RoleSourceDO();
        roleSourceDO.setId(0L);
        roleSourceDO.setRoleGuid("roleGuid");
        roleSourceDO.setTerminalGuid("terminalGuid");
        roleSourceDO.setTerminalCode("terminalCode");
        roleSourceDO.setTerminalName("terminalName");
        final List<RoleSourceDO> roleSourceDOS = Arrays.asList(roleSourceDO);
        when(mockRoleSourceMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(roleSourceDOS);

        // Configure RoleDataService.batchSaveRoleData(...).
        final RoleSourceDO roleSourceDO1 = new RoleSourceDO();
        roleSourceDO1.setId(0L);
        roleSourceDO1.setRoleGuid("roleGuid");
        roleSourceDO1.setTerminalGuid("terminalGuid");
        roleSourceDO1.setTerminalCode("terminalCode");
        roleSourceDO1.setTerminalName("terminalName");
        final List<RoleSourceDO> roleSourceDOList = Arrays.asList(roleSourceDO1);
        when(mockRoleDataService.batchSaveRoleData(roleSourceDOList)).thenReturn(false);

        // Run the test
        final boolean result = roleServiceImplUnderTest.copyRole("roleGuid");

        // Verify the results
        assertThat(result).isFalse();
        verify(mockUserDataService).appendRole("roleGuid");
    }

    @Test
    public void testCopyRole_RoleSourceMapperReturnsNoItems() {
        // Setup
        // Configure RoleMapper.selectOne(...).
        final RoleDO roleDO = new RoleDO();
        roleDO.setGuid("guid");
        roleDO.setName("name");
        roleDO.setCreateStaffGuid("createStaffGuid");
        roleDO.setModifiedStaffGuid("modifiedStaffGuid");
        roleDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        roleDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockRoleMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(roleDO);

        // Configure RoleMapper.selectList(...).
        final RoleDO roleDO1 = new RoleDO();
        roleDO1.setGuid("guid");
        roleDO1.setName("name");
        roleDO1.setCreateStaffGuid("createStaffGuid");
        roleDO1.setModifiedStaffGuid("modifiedStaffGuid");
        roleDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        roleDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<RoleDO> roleDOS = Arrays.asList(roleDO1);
        when(mockRoleMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(roleDOS);

        when(mockRoleSourceMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure RoleDataService.batchSaveRoleData(...).
        final RoleSourceDO roleSourceDO = new RoleSourceDO();
        roleSourceDO.setId(0L);
        roleSourceDO.setRoleGuid("roleGuid");
        roleSourceDO.setTerminalGuid("terminalGuid");
        roleSourceDO.setTerminalCode("terminalCode");
        roleSourceDO.setTerminalName("terminalName");
        final List<RoleSourceDO> roleSourceDOList = Arrays.asList(roleSourceDO);
        when(mockRoleDataService.batchSaveRoleData(roleSourceDOList)).thenReturn(false);

        // Run the test
        final boolean result = roleServiceImplUnderTest.copyRole("roleGuid");

        // Verify the results
        assertThat(result).isFalse();
        verify(mockUserDataService).appendRole("roleGuid");
    }

    @Test
    public void testCopyRole_RoleDataServiceReturnsTrue() {
        // Setup
        // Configure RoleMapper.selectOne(...).
        final RoleDO roleDO = new RoleDO();
        roleDO.setGuid("guid");
        roleDO.setName("name");
        roleDO.setCreateStaffGuid("createStaffGuid");
        roleDO.setModifiedStaffGuid("modifiedStaffGuid");
        roleDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        roleDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockRoleMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(roleDO);

        // Configure RoleMapper.selectList(...).
        final RoleDO roleDO1 = new RoleDO();
        roleDO1.setGuid("guid");
        roleDO1.setName("name");
        roleDO1.setCreateStaffGuid("createStaffGuid");
        roleDO1.setModifiedStaffGuid("modifiedStaffGuid");
        roleDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        roleDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<RoleDO> roleDOS = Arrays.asList(roleDO1);
        when(mockRoleMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(roleDOS);

        // Configure RoleSourceMapper.selectList(...).
        final RoleSourceDO roleSourceDO = new RoleSourceDO();
        roleSourceDO.setId(0L);
        roleSourceDO.setRoleGuid("roleGuid");
        roleSourceDO.setTerminalGuid("terminalGuid");
        roleSourceDO.setTerminalCode("terminalCode");
        roleSourceDO.setTerminalName("terminalName");
        final List<RoleSourceDO> roleSourceDOS = Arrays.asList(roleSourceDO);
        when(mockRoleSourceMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(roleSourceDOS);

        // Configure RoleDataService.batchSaveRoleData(...).
        final RoleSourceDO roleSourceDO1 = new RoleSourceDO();
        roleSourceDO1.setId(0L);
        roleSourceDO1.setRoleGuid("roleGuid");
        roleSourceDO1.setTerminalGuid("terminalGuid");
        roleSourceDO1.setTerminalCode("terminalCode");
        roleSourceDO1.setTerminalName("terminalName");
        final List<RoleSourceDO> roleSourceDOList = Arrays.asList(roleSourceDO1);
        when(mockRoleDataService.batchSaveRoleData(roleSourceDOList)).thenReturn(true);

        // Run the test
        final boolean result = roleServiceImplUnderTest.copyRole("roleGuid");

        // Verify the results
        assertThat(result).isTrue();
        verify(mockUserDataService).appendRole("roleGuid");
    }
}
