package com.holderzone.saas.store.staff.event;

import com.holderzone.framework.dds.starter.utils.DynamicInfoHelper;
import com.holderzone.resource.common.dto.mq.UnMessage;
import com.holderzone.saas.store.staff.entity.bo.ProductBasicBO;
import com.holderzone.saas.store.staff.service.ProductService;
import com.holderzone.saas.store.staff.service.remote.EnterpriseClient;
import com.holderzone.saas.store.staff.service.remote.EnterpriseDataClient;
import com.holderzone.saas.store.staff.utils.DynamicHelper;
import org.apache.rocketmq.common.message.MessageExt;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.net.InetSocketAddress;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;

@RunWith(MockitoJUnitRunner.class)
public class SyncErpProductListenerTest {

    @Mock
    private DynamicHelper mockDynamicHelper;
    @Mock
    private ProductService mockProductService;
    @Mock
    private EnterpriseClient mockEnterpriseClient;
    @Mock
    private EnterpriseDataClient mockEnterpriseDataClient;
    @Mock
    private DynamicInfoHelper mockDynamicInfoHelper;

    private SyncErpProductListener syncErpProductListenerUnderTest;

    @Before
    public void setUp() throws Exception {
        syncErpProductListenerUnderTest = new SyncErpProductListener(mockDynamicHelper, mockProductService,
                mockEnterpriseClient, mockEnterpriseDataClient, mockDynamicInfoHelper);
    }

    @Test
    public void testConsumeMsg() {
        // Setup
        final UnMessage<String> unMessage = new UnMessage<>("messageType", "enterpriseGuid", "storeGuid", "message");
        final MessageExt messageExt = new MessageExt(0, 0L, new InetSocketAddress("localhost", 80), 0L,
                new InetSocketAddress("localhost", 80), "msgId");

        // Run the test
        final boolean result = syncErpProductListenerUnderTest.consumeMsg(unMessage, messageExt);

        // Verify the results
        assertThat(result).isFalse();
        verify(mockProductService).saveOrUpdate(any(ProductBasicBO.class));
        verify(mockProductService).deleteProduct(any(ProductBasicBO.class));
        verify(mockDynamicHelper).clear();
    }
}
