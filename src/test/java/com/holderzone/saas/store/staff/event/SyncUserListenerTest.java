package com.holderzone.saas.store.staff.event;

import com.holderzone.framework.dds.starter.utils.DynamicInfoHelper;
import com.holderzone.resource.common.dto.mq.UnMessage;
import com.holderzone.saas.store.dto.user.UserDTO;
import com.holderzone.saas.store.staff.entity.domain.UserDO;
import com.holderzone.saas.store.staff.service.UserService;
import com.holderzone.saas.store.staff.service.remote.EnterpriseClient;
import com.holderzone.saas.store.staff.service.remote.EnterpriseDataClient;
import com.holderzone.saas.store.staff.utils.DynamicHelper;
import org.apache.rocketmq.common.message.MessageExt;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.net.InetSocketAddress;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;

@RunWith(MockitoJUnitRunner.class)
public class SyncUserListenerTest {

    @Mock
    private UserService mockUserService;
    @Mock
    private DynamicHelper mockDynamicHelper;
    @Mock
    private EnterpriseClient mockEnterpriseClient;
    @Mock
    private EnterpriseDataClient mockEnterpriseDataClient;
    @Mock
    private DynamicInfoHelper mockDynamicInfoHelper;

    private SyncUserListener syncUserListenerUnderTest;

    @Before
    public void setUp() throws Exception {
        syncUserListenerUnderTest = new SyncUserListener(mockUserService, mockDynamicHelper, mockEnterpriseClient,
                mockEnterpriseDataClient, mockDynamicInfoHelper);
    }

    @Test
    public void testConsumeMsg() {
        // Setup
        final UnMessage unMessage = new UnMessage<>("messageType", "enterpriseGuid", "storeGuid", "message");
        final MessageExt messageExt = new MessageExt(0, 0L, new InetSocketAddress("localhost", 80), 0L,
                new InetSocketAddress("localhost", 80), "msgId");

        // Run the test
        final boolean result = syncUserListenerUnderTest.consumeMsg(unMessage, messageExt);

        // Verify the results
        assertThat(result).isFalse();

        // Confirm UserService.createFromCloud(...).
        final UserDTO userDTO = new UserDTO();
        userDTO.setGuid("f5e26fbd-364e-430f-8b68-f12436380824");
        userDTO.setEnterpriseNo("enterpriseNo");
        userDTO.setAccount("account");
        userDTO.setPassword("password");
        userDTO.setAuthCode("authCode");
        userDTO.setName("name");
        userDTO.setPhone("phone");
        userDTO.setIdCardAddress("address");
        userDTO.setAddress("address");
        userDTO.setBirthday(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        userDTO.setOnBoardingTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        userDTO.setCreateStaffGuid("0");
        userDTO.setUpdateStaffGuid("0");
        userDTO.setIsEnable(false);
        userDTO.setIsDeleted(false);
        userDTO.setPhoneList(Arrays.asList("value"));
        userDTO.setIntegrateFlag(false);
        verify(mockUserService).createFromCloud(userDTO);

        // Confirm UserService.updateFromCloud(...).
        final UserDTO userDTO1 = new UserDTO();
        userDTO1.setGuid("f5e26fbd-364e-430f-8b68-f12436380824");
        userDTO1.setEnterpriseNo("enterpriseNo");
        userDTO1.setAccount("account");
        userDTO1.setPassword("password");
        userDTO1.setAuthCode("authCode");
        userDTO1.setName("name");
        userDTO1.setPhone("phone");
        userDTO1.setIdCardAddress("address");
        userDTO1.setAddress("address");
        userDTO1.setBirthday(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        userDTO1.setOnBoardingTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        userDTO1.setCreateStaffGuid("0");
        userDTO1.setUpdateStaffGuid("0");
        userDTO1.setIsEnable(false);
        userDTO1.setIsDeleted(false);
        userDTO1.setPhoneList(Arrays.asList("value"));
        userDTO1.setIntegrateFlag(false);
        verify(mockUserService).updateFromCloud(userDTO1);

        // Confirm UserService.resetPwdFromCloud(...).
        final UserDTO userDTO2 = new UserDTO();
        userDTO2.setGuid("f5e26fbd-364e-430f-8b68-f12436380824");
        userDTO2.setEnterpriseNo("enterpriseNo");
        userDTO2.setAccount("account");
        userDTO2.setPassword("password");
        userDTO2.setAuthCode("authCode");
        userDTO2.setName("name");
        userDTO2.setPhone("phone");
        userDTO2.setIdCardAddress("address");
        userDTO2.setAddress("address");
        userDTO2.setBirthday(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        userDTO2.setOnBoardingTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        userDTO2.setCreateStaffGuid("0");
        userDTO2.setUpdateStaffGuid("0");
        userDTO2.setIsEnable(false);
        userDTO2.setIsDeleted(false);
        userDTO2.setPhoneList(Arrays.asList("value"));
        userDTO2.setIntegrateFlag(false);
        verify(mockUserService).resetPwdFromCloud(userDTO2);

        // Confirm UserService.createBatchFromCloud(...).
        final UserDO userDO = new UserDO();
        userDO.setGuid("f5e26fbd-364e-430f-8b68-f12436380824");
        userDO.setEnterpriseNo("enterpriseNo");
        userDO.setAccount("account");
        userDO.setPassword("password");
        userDO.setAuthCode("authCode");
        userDO.setName("name");
        userDO.setPhone("phone");
        userDO.setIdCardAddress("address");
        userDO.setAddress("address");
        userDO.setBirthday(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        userDO.setOnBoardingTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        userDO.setCreateStaffGuid("0");
        userDO.setUpdateStaffGuid("0");
        userDO.setIsEnable(false);
        userDO.setIsDeleted(false);
        userDO.setRegType("regType");
        userDO.setIntegrateFlag(false);
        final List<UserDO> userList = Arrays.asList(userDO);
        verify(mockUserService).createBatchFromCloud(userList);

        // Confirm UserService.updateBatchFromCloud(...).
        final UserDO userDO1 = new UserDO();
        userDO1.setGuid("f5e26fbd-364e-430f-8b68-f12436380824");
        userDO1.setEnterpriseNo("enterpriseNo");
        userDO1.setAccount("account");
        userDO1.setPassword("password");
        userDO1.setAuthCode("authCode");
        userDO1.setName("name");
        userDO1.setPhone("phone");
        userDO1.setIdCardAddress("address");
        userDO1.setAddress("address");
        userDO1.setBirthday(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        userDO1.setOnBoardingTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        userDO1.setCreateStaffGuid("0");
        userDO1.setUpdateStaffGuid("0");
        userDO1.setIsEnable(false);
        userDO1.setIsDeleted(false);
        userDO1.setRegType("regType");
        userDO1.setIntegrateFlag(false);
        final List<UserDO> userList1 = Arrays.asList(userDO1);
        verify(mockUserService).updateBatchFromCloud(userList1);
        verify(mockDynamicHelper).clear();
    }
}
