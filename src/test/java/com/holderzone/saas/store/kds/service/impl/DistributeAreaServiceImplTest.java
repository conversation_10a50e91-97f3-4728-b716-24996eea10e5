package com.holderzone.saas.store.kds.service.impl;

import com.holderzone.saas.store.dto.kds.req.DeviceQueryReqDTO;
import com.holderzone.saas.store.dto.kds.resp.DstBindStatusRespDTO;
import com.holderzone.saas.store.kds.entity.domain.DistributeAreaDO;
import com.holderzone.saas.store.kds.service.DistributedIdService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class DistributeAreaServiceImplTest {

    @Mock
    private DistributedIdService mockDistributedIdService;

    private DistributeAreaServiceImpl distributeAreaServiceImplUnderTest;

    @Before
    public void setUp() {
        distributeAreaServiceImplUnderTest = new DistributeAreaServiceImpl(mockDistributedIdService);
    }

    @Test
    public void testQueryAreaBindingPreview() {
        // Setup
        final DeviceQueryReqDTO deviceQueryReqDTO = new DeviceQueryReqDTO("storeGuid", "deviceId");
        final DstBindStatusRespDTO expectedResult = new DstBindStatusRespDTO();
        expectedResult.setBoundAreaCount(0);
        expectedResult.setIsSnackBound(false);
        expectedResult.setIsTakeoutBound(false);
        expectedResult.setBoundItemCount(0);

        // Run the test
        final DstBindStatusRespDTO result = distributeAreaServiceImplUnderTest.queryAreaBindingPreview(
                deviceQueryReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryBoundAreaOfStore() {
        // Setup
        final DistributeAreaDO distributeAreaDO = new DistributeAreaDO();
        distributeAreaDO.setGuid("83a34150-9f5c-40b7-9414-6f8a95e30c40");
        distributeAreaDO.setStoreGuid("storeGuid");
        distributeAreaDO.setDeviceId("deviceId");
        distributeAreaDO.setAreaGuid("areaGuid");
        final List<DistributeAreaDO> expectedResult = Arrays.asList(distributeAreaDO);

        // Run the test
        final List<DistributeAreaDO> result = distributeAreaServiceImplUnderTest.queryBoundAreaOfStore("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryBoundAreaOfDevice() {
        // Setup
        final DistributeAreaDO distributeAreaDO = new DistributeAreaDO();
        distributeAreaDO.setGuid("83a34150-9f5c-40b7-9414-6f8a95e30c40");
        distributeAreaDO.setStoreGuid("storeGuid");
        distributeAreaDO.setDeviceId("deviceId");
        distributeAreaDO.setAreaGuid("areaGuid");
        final List<DistributeAreaDO> expectedResult = Arrays.asList(distributeAreaDO);

        // Run the test
        final List<DistributeAreaDO> result = distributeAreaServiceImplUnderTest.queryBoundAreaOfDevice("storeGuid",
                "deviceId");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryBoundAreaGuidOfDevice() {
        assertThat(distributeAreaServiceImplUnderTest.queryBoundAreaGuidOfDevice("storeGuid", "deviceId"))
                .isEqualTo(Arrays.asList("value"));
        assertThat(distributeAreaServiceImplUnderTest.queryBoundAreaGuidOfDevice("storeGuid", "deviceId"))
                .isEqualTo(Collections.emptyList());
    }

    @Test
    public void testQueryBoundAreaGuidOfDeviceList() {
        assertThat(distributeAreaServiceImplUnderTest.queryBoundAreaGuidOfDeviceList("storeGuid",
                Arrays.asList("value"))).isEqualTo(Arrays.asList("value"));
        assertThat(distributeAreaServiceImplUnderTest.queryBoundAreaGuidOfDeviceList("storeGuid",
                Arrays.asList("value"))).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testQueryOccupiedDeviceId() {
        assertThat(distributeAreaServiceImplUnderTest.queryOccupiedDeviceId("storeGuid", "deviceId"))
                .isEqualTo(Arrays.asList("value"));
        assertThat(distributeAreaServiceImplUnderTest.queryOccupiedDeviceId("storeGuid", "deviceId"))
                .isEqualTo(Collections.emptyList());
    }

    @Test
    public void testSimpleSaveBatchArea() {
        // Setup
        when(mockDistributedIdService.nextBatchDstAreaGuid(0L)).thenReturn(Arrays.asList("value"));

        // Run the test
        distributeAreaServiceImplUnderTest.simpleSaveBatchArea("storeGuid", "deviceId", Arrays.asList("value"));

        // Verify the results
    }

    @Test
    public void testSimpleSaveBatchArea_DistributedIdServiceReturnsNoItems() {
        // Setup
        when(mockDistributedIdService.nextBatchDstAreaGuid(0L)).thenReturn(Collections.emptyList());

        // Run the test
        distributeAreaServiceImplUnderTest.simpleSaveBatchArea("storeGuid", "deviceId", Arrays.asList("value"));

        // Verify the results
    }

    @Test
    public void testSimpleRemoveBatchArea() {
        // Setup
        // Run the test
        distributeAreaServiceImplUnderTest.simpleRemoveBatchArea("storeGuid", "deviceId", Arrays.asList("value"));

        // Verify the results
    }

    @Test
    public void testReInitialize() {
        // Setup
        // Run the test
        distributeAreaServiceImplUnderTest.reInitialize("storeGuid", "deviceId");

        // Verify the results
    }

    @Test
    public void testQueryDstAreaMap() {
        // Setup
        final Map<String, List<String>> expectedResult = new HashMap<>();

        // Run the test
        final Map<String, List<String>> result = distributeAreaServiceImplUnderTest.queryDstAreaMap("storeGuid",
                Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
