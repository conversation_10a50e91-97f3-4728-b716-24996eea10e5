package com.holderzone.saas.store.kds.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.framework.rocketmq.common.DefaultRocketMqProducer;
import com.holderzone.saas.store.dto.kds.req.ItemPrepareReqDTO;
import com.holderzone.saas.store.dto.kds.req.ItemStateTransReqDTO;
import com.holderzone.saas.store.dto.kds.req.QueueQueryReqDTO;
import com.holderzone.saas.store.dto.kds.req.TradeSnackInfoDTO;
import com.holderzone.saas.store.dto.kds.resp.*;
import com.holderzone.saas.store.kds.entity.bo.AsyncTask;
import com.holderzone.saas.store.kds.entity.domain.KitchenItemDO;
import com.holderzone.saas.store.kds.entity.domain.QueueConfigDO;
import com.holderzone.saas.store.kds.entity.domain.QueueItemDO;
import com.holderzone.saas.store.kds.mapstruct.QueueMapstruct;
import com.holderzone.saas.store.kds.service.DistributedIdService;
import com.holderzone.saas.store.kds.service.KitchenItemService;
import com.holderzone.saas.store.kds.service.QueueConfigService;
import com.holderzone.saas.store.kds.service.QueuePushService;
import org.apache.rocketmq.common.message.Message;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class QueueItemServiceImplTest {

    @Mock
    private QueueConfigService mockQueueConfigService;
    @Mock
    private DistributedIdService mockDistributedIdService;
    @Mock
    private QueuePushService mockQueuePushService;
    @Mock
    private QueueMapstruct mockQueueMapstruct;
    @Mock
    private DefaultRocketMqProducer mockDefaultRocketMqProducer;
    @Mock
    private KitchenItemService mockKitchenItemService;

    private QueueItemServiceImpl queueItemServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        queueItemServiceImplUnderTest = new QueueItemServiceImpl(mockQueueConfigService, mockDistributedIdService,
                mockQueuePushService, mockQueueMapstruct, mockDefaultRocketMqProducer, mockKitchenItemService);
    }

    @Test
    public void testInPreparedQueue() {
        // Setup
        final ItemPrepareReqDTO itemPrepareReqDTO = new ItemPrepareReqDTO();
        itemPrepareReqDTO.setStoreGuid("storeGuid");
        itemPrepareReqDTO.setOrderGuid("orderGuid");
        itemPrepareReqDTO.setTradeMode(0);
        final TradeSnackInfoDTO tradeSnackInfoDTO = new TradeSnackInfoDTO();
        tradeSnackInfoDTO.setOrderNo("orderNo");
        tradeSnackInfoDTO.setMarkNo("markNo");
        itemPrepareReqDTO.setTradeSnackInfoDTO(tradeSnackInfoDTO);
        final AsyncTask<ItemPrepareReqDTO> asyncTask = AsyncTask.wrapper(itemPrepareReqDTO);

        // Configure QueueConfigService.list(...).
        final QueueConfigDO queueConfigDO = new QueueConfigDO();
        queueConfigDO.setId(0L);
        queueConfigDO.setGuid("deviceGuid");
        queueConfigDO.setStoreGuid("storeGuid");
        queueConfigDO.setSource(0);
        queueConfigDO.setConfirmTtlLevel(0);
        final List<QueueConfigDO> queueConfigDOS = Arrays.asList(queueConfigDO);
        when(mockQueueConfigService.list(any(LambdaQueryWrapper.class))).thenReturn(queueConfigDOS);

        when(mockDistributedIdService.nextQueueItemGuid()).thenReturn("2bb975ef-d1f2-4615-a82f-430a8da75495");

        // Run the test
        queueItemServiceImplUnderTest.inPreparedQueue(asyncTask);

        // Verify the results
        verify(mockQueuePushService).statusChanged("enterpriseGuid", "storeGuid", "deviceGuid", "");
    }

    @Test
    public void testInPreparedQueue_QueueConfigServiceReturnsNoItems() {
        // Setup
        final ItemPrepareReqDTO itemPrepareReqDTO = new ItemPrepareReqDTO();
        itemPrepareReqDTO.setStoreGuid("storeGuid");
        itemPrepareReqDTO.setOrderGuid("orderGuid");
        itemPrepareReqDTO.setTradeMode(0);
        final TradeSnackInfoDTO tradeSnackInfoDTO = new TradeSnackInfoDTO();
        tradeSnackInfoDTO.setOrderNo("orderNo");
        tradeSnackInfoDTO.setMarkNo("markNo");
        itemPrepareReqDTO.setTradeSnackInfoDTO(tradeSnackInfoDTO);
        final AsyncTask<ItemPrepareReqDTO> asyncTask = AsyncTask.wrapper(itemPrepareReqDTO);
        when(mockQueueConfigService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        queueItemServiceImplUnderTest.inPreparedQueue(asyncTask);

        // Verify the results
    }

    @Test
    public void testInDistributedQueue() {
        // Setup
        final ItemStateTransReqDTO itemStateTransReqDTO = new ItemStateTransReqDTO();
        itemStateTransReqDTO.setStoreGuid("storeGuid");
        final PrdDstItemDTO prdDstItemDTO = new PrdDstItemDTO();
        prdDstItemDTO.setIsWeight(false);
        prdDstItemDTO.setCurrentCount(new BigDecimal("0.00"));
        final PrdDstItemTableDTO weightKitchenItem = new PrdDstItemTableDTO();
        weightKitchenItem.setDisplayType(0);
        weightKitchenItem.setOrderGuid("orderGuid");
        weightKitchenItem.setOrderNumber("orderNo");
        weightKitchenItem.setOrderSerialNo("orderDesc");
        weightKitchenItem.setKitchenItemGuid("kitchenItemGuid");
        prdDstItemDTO.setWeightKitchenItem(weightKitchenItem);
        final PrdDstItemTableDTO prdDstItemTableDTO = new PrdDstItemTableDTO();
        prdDstItemTableDTO.setDisplayType(0);
        prdDstItemTableDTO.setOrderGuid("orderGuid");
        prdDstItemTableDTO.setOrderNumber("orderNo");
        prdDstItemTableDTO.setOrderSerialNo("orderDesc");
        prdDstItemTableDTO.setKitchenItemGuid("kitchenItemGuid");
        prdDstItemDTO.setKitchenItemList(Arrays.asList(prdDstItemTableDTO));
        itemStateTransReqDTO.setPrdDstItemList(Arrays.asList(prdDstItemDTO));
        final AsyncTask<ItemStateTransReqDTO> asyncTask = AsyncTask.wrapper(itemStateTransReqDTO);

        // Configure QueueConfigService.list(...).
        final QueueConfigDO queueConfigDO = new QueueConfigDO();
        queueConfigDO.setId(0L);
        queueConfigDO.setGuid("deviceGuid");
        queueConfigDO.setStoreGuid("storeGuid");
        queueConfigDO.setSource(0);
        queueConfigDO.setConfirmTtlLevel(0);
        final List<QueueConfigDO> queueConfigDOS = Arrays.asList(queueConfigDO);
        when(mockQueueConfigService.list(any(LambdaQueryWrapper.class))).thenReturn(queueConfigDOS);

        when(mockDistributedIdService.nextQueueItemGuid()).thenReturn("2bb975ef-d1f2-4615-a82f-430a8da75495");

        // Configure KitchenItemService.list(...).
        final KitchenItemDO kitchenItemDO = new KitchenItemDO();
        kitchenItemDO.setGuid("8fdbcd99-b71c-4aee-a2f7-80e8bc7cc936");
        kitchenItemDO.setDisplayType(0);
        kitchenItemDO.setOrderGuid("orderGuid");
        kitchenItemDO.setItemState(0);
        kitchenItemDO.setKitchenState(0);
        kitchenItemDO.setDistributeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<KitchenItemDO> kitchenItemDOS = Arrays.asList(kitchenItemDO);
        when(mockKitchenItemService.list(any(LambdaQueryWrapper.class))).thenReturn(kitchenItemDOS);

        // Run the test
        queueItemServiceImplUnderTest.inDistributedQueue(asyncTask);

        // Verify the results
        verify(mockDefaultRocketMqProducer).sendMessage(any(Message.class));
        verify(mockQueuePushService).statusChanged("enterpriseGuid", "storeGuid", "deviceGuid", "voiceMsg");
    }

    @Test
    public void testInDistributedQueue_QueueConfigServiceReturnsNoItems() {
        // Setup
        final ItemStateTransReqDTO itemStateTransReqDTO = new ItemStateTransReqDTO();
        itemStateTransReqDTO.setStoreGuid("storeGuid");
        final PrdDstItemDTO prdDstItemDTO = new PrdDstItemDTO();
        prdDstItemDTO.setIsWeight(false);
        prdDstItemDTO.setCurrentCount(new BigDecimal("0.00"));
        final PrdDstItemTableDTO weightKitchenItem = new PrdDstItemTableDTO();
        weightKitchenItem.setDisplayType(0);
        weightKitchenItem.setOrderGuid("orderGuid");
        weightKitchenItem.setOrderNumber("orderNo");
        weightKitchenItem.setOrderSerialNo("orderDesc");
        weightKitchenItem.setKitchenItemGuid("kitchenItemGuid");
        prdDstItemDTO.setWeightKitchenItem(weightKitchenItem);
        final PrdDstItemTableDTO prdDstItemTableDTO = new PrdDstItemTableDTO();
        prdDstItemTableDTO.setDisplayType(0);
        prdDstItemTableDTO.setOrderGuid("orderGuid");
        prdDstItemTableDTO.setOrderNumber("orderNo");
        prdDstItemTableDTO.setOrderSerialNo("orderDesc");
        prdDstItemTableDTO.setKitchenItemGuid("kitchenItemGuid");
        prdDstItemDTO.setKitchenItemList(Arrays.asList(prdDstItemTableDTO));
        itemStateTransReqDTO.setPrdDstItemList(Arrays.asList(prdDstItemDTO));
        final AsyncTask<ItemStateTransReqDTO> asyncTask = AsyncTask.wrapper(itemStateTransReqDTO);
        when(mockQueueConfigService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        queueItemServiceImplUnderTest.inDistributedQueue(asyncTask);

        // Verify the results
    }

    @Test
    public void testInDistributedQueue_KitchenItemServiceReturnsNoItems() {
        // Setup
        final ItemStateTransReqDTO itemStateTransReqDTO = new ItemStateTransReqDTO();
        itemStateTransReqDTO.setStoreGuid("storeGuid");
        final PrdDstItemDTO prdDstItemDTO = new PrdDstItemDTO();
        prdDstItemDTO.setIsWeight(false);
        prdDstItemDTO.setCurrentCount(new BigDecimal("0.00"));
        final PrdDstItemTableDTO weightKitchenItem = new PrdDstItemTableDTO();
        weightKitchenItem.setDisplayType(0);
        weightKitchenItem.setOrderGuid("orderGuid");
        weightKitchenItem.setOrderNumber("orderNo");
        weightKitchenItem.setOrderSerialNo("orderDesc");
        weightKitchenItem.setKitchenItemGuid("kitchenItemGuid");
        prdDstItemDTO.setWeightKitchenItem(weightKitchenItem);
        final PrdDstItemTableDTO prdDstItemTableDTO = new PrdDstItemTableDTO();
        prdDstItemTableDTO.setDisplayType(0);
        prdDstItemTableDTO.setOrderGuid("orderGuid");
        prdDstItemTableDTO.setOrderNumber("orderNo");
        prdDstItemTableDTO.setOrderSerialNo("orderDesc");
        prdDstItemTableDTO.setKitchenItemGuid("kitchenItemGuid");
        prdDstItemDTO.setKitchenItemList(Arrays.asList(prdDstItemTableDTO));
        itemStateTransReqDTO.setPrdDstItemList(Arrays.asList(prdDstItemDTO));
        final AsyncTask<ItemStateTransReqDTO> asyncTask = AsyncTask.wrapper(itemStateTransReqDTO);

        // Configure QueueConfigService.list(...).
        final QueueConfigDO queueConfigDO = new QueueConfigDO();
        queueConfigDO.setId(0L);
        queueConfigDO.setGuid("deviceGuid");
        queueConfigDO.setStoreGuid("storeGuid");
        queueConfigDO.setSource(0);
        queueConfigDO.setConfirmTtlLevel(0);
        final List<QueueConfigDO> queueConfigDOS = Arrays.asList(queueConfigDO);
        when(mockQueueConfigService.list(any(LambdaQueryWrapper.class))).thenReturn(queueConfigDOS);

        when(mockDistributedIdService.nextQueueItemGuid()).thenReturn("2bb975ef-d1f2-4615-a82f-430a8da75495");
        when(mockKitchenItemService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        queueItemServiceImplUnderTest.inDistributedQueue(asyncTask);

        // Verify the results
        verify(mockDefaultRocketMqProducer).sendMessage(any(Message.class));
        verify(mockQueuePushService).statusChanged("enterpriseGuid", "storeGuid", "deviceGuid", "voiceMsg");
    }

    @Test
    public void testBackPreparedQueue() {
        // Setup
        final KitchenItemDO kitchenItemDO = new KitchenItemDO();
        kitchenItemDO.setGuid("8fdbcd99-b71c-4aee-a2f7-80e8bc7cc936");
        kitchenItemDO.setDisplayType(0);
        kitchenItemDO.setOrderGuid("orderGuid");
        kitchenItemDO.setItemState(0);
        kitchenItemDO.setKitchenState(0);
        kitchenItemDO.setDistributeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final AsyncTask<KitchenItemDO> asyncTask = AsyncTask.wrapper(kitchenItemDO);
        when(mockDistributedIdService.nextQueueItemGuid()).thenReturn("2bb975ef-d1f2-4615-a82f-430a8da75495");

        // Run the test
        queueItemServiceImplUnderTest.backPreparedQueue(asyncTask);

        // Verify the results
        verify(mockQueuePushService).statusChanged("enterpriseGuid", "storeGuid", "deviceGuid", "");
    }

    @Test
    public void testOutPrepareQueue() {
        // Setup
        final KitchenItemDO kitchenItemDO = new KitchenItemDO();
        kitchenItemDO.setGuid("8fdbcd99-b71c-4aee-a2f7-80e8bc7cc936");
        kitchenItemDO.setDisplayType(0);
        kitchenItemDO.setOrderGuid("orderGuid");
        kitchenItemDO.setItemState(0);
        kitchenItemDO.setKitchenState(0);
        kitchenItemDO.setDistributeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final AsyncTask<KitchenItemDO> asyncTask = AsyncTask.wrapper(kitchenItemDO);
        when(mockKitchenItemService.count(any(LambdaQueryWrapper.class))).thenReturn(0);

        // Run the test
        queueItemServiceImplUnderTest.outPrepareQueue(asyncTask);

        // Verify the results
        verify(mockQueuePushService).statusChanged("enterpriseGuid", "storeGuid", "deviceId", "");
    }

    @Test
    public void testQuery() {
        // Setup
        final QueueQueryReqDTO queueQueryReqDTO = new QueueQueryReqDTO("storeGuid", "deviceId");
        final KdsQueueRespDTO expectedResult = new KdsQueueRespDTO();
        final QueueItemDTO queueItemDTO = new QueueItemDTO();
        queueItemDTO.setGuid("2bb975ef-d1f2-4615-a82f-430a8da75495");
        queueItemDTO.setStoreGuid("storeGuid");
        queueItemDTO.setDeviceGuid("deviceGuid");
        expectedResult.setWaitingQueue(Arrays.asList(queueItemDTO));
        final QueueItemDTO queueItemDTO1 = new QueueItemDTO();
        queueItemDTO1.setGuid("2bb975ef-d1f2-4615-a82f-430a8da75495");
        queueItemDTO1.setStoreGuid("storeGuid");
        queueItemDTO1.setDeviceGuid("deviceGuid");
        expectedResult.setTakeMealQueue(Arrays.asList(queueItemDTO1));

        // Configure QueueMapstruct.doToDTO(...).
        final QueueItemDTO queueItemDTO2 = new QueueItemDTO();
        queueItemDTO2.setGuid("2bb975ef-d1f2-4615-a82f-430a8da75495");
        queueItemDTO2.setStoreGuid("storeGuid");
        queueItemDTO2.setDeviceGuid("deviceGuid");
        queueItemDTO2.setOrderGuid("orderGuid");
        queueItemDTO2.setOrderNo("orderNo");
        final List<QueueItemDTO> queueItemDTOS = Arrays.asList(queueItemDTO2);
        final QueueItemDO queueItemDO1 = new QueueItemDO();
        queueItemDO1.setId(0L);
        queueItemDO1.setGuid("2bb975ef-d1f2-4615-a82f-430a8da75495");
        queueItemDO1.setStoreGuid("storeGuid");
        queueItemDO1.setDeviceGuid("deviceGuid");
        queueItemDO1.setOrderGuid("orderGuid");
        queueItemDO1.setOrderNo("orderNo");
        queueItemDO1.setOrderMode(0);
        queueItemDO1.setOrderDesc("orderDesc");
        queueItemDO1.setStatus(0);
        queueItemDO1.setInTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        queueItemDO1.setDstTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        queueItemDO1.setDstItems("dstItems");
        queueItemDO1.setDstExpireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<QueueItemDO> queueItemDO = Arrays.asList(queueItemDO1);
        when(mockQueueMapstruct.doToDTO(queueItemDO)).thenReturn(queueItemDTOS);

        // Run the test
        final KdsQueueRespDTO result = queueItemServiceImplUnderTest.query(queueQueryReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQuery_QueueMapstructReturnsNoItems() {
        // Setup
        final QueueQueryReqDTO queueQueryReqDTO = new QueueQueryReqDTO("storeGuid", "deviceId");
        final KdsQueueRespDTO expectedResult = new KdsQueueRespDTO();
        final QueueItemDTO queueItemDTO = new QueueItemDTO();
        queueItemDTO.setGuid("2bb975ef-d1f2-4615-a82f-430a8da75495");
        queueItemDTO.setStoreGuid("storeGuid");
        queueItemDTO.setDeviceGuid("deviceGuid");
        expectedResult.setWaitingQueue(Arrays.asList(queueItemDTO));
        final QueueItemDTO queueItemDTO1 = new QueueItemDTO();
        queueItemDTO1.setGuid("2bb975ef-d1f2-4615-a82f-430a8da75495");
        queueItemDTO1.setStoreGuid("storeGuid");
        queueItemDTO1.setDeviceGuid("deviceGuid");
        expectedResult.setTakeMealQueue(Arrays.asList(queueItemDTO1));

        // Configure QueueMapstruct.doToDTO(...).
        final QueueItemDO queueItemDO1 = new QueueItemDO();
        queueItemDO1.setId(0L);
        queueItemDO1.setGuid("2bb975ef-d1f2-4615-a82f-430a8da75495");
        queueItemDO1.setStoreGuid("storeGuid");
        queueItemDO1.setDeviceGuid("deviceGuid");
        queueItemDO1.setOrderGuid("orderGuid");
        queueItemDO1.setOrderNo("orderNo");
        queueItemDO1.setOrderMode(0);
        queueItemDO1.setOrderDesc("orderDesc");
        queueItemDO1.setStatus(0);
        queueItemDO1.setInTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        queueItemDO1.setDstTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        queueItemDO1.setDstItems("dstItems");
        queueItemDO1.setDstExpireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<QueueItemDO> queueItemDO = Arrays.asList(queueItemDO1);
        when(mockQueueMapstruct.doToDTO(queueItemDO)).thenReturn(Collections.emptyList());

        // Run the test
        final KdsQueueRespDTO result = queueItemServiceImplUnderTest.query(queueQueryReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testExpireAndNotify() {
        // Setup
        final QueueItemDTO queueItemDTO = new QueueItemDTO();
        queueItemDTO.setGuid("2bb975ef-d1f2-4615-a82f-430a8da75495");
        queueItemDTO.setStoreGuid("storeGuid");
        queueItemDTO.setDeviceGuid("deviceGuid");
        queueItemDTO.setOrderGuid("orderGuid");
        queueItemDTO.setOrderNo("orderNo");

        // Run the test
        queueItemServiceImplUnderTest.expireAndNotify(queueItemDTO);

        // Verify the results
        verify(mockQueuePushService).statusChanged("enterpriseGuid", "storeGuid", "deviceGuid", "");
    }

    @Test
    public void testDstCallForMealAgain() {
        // Setup
        final KitchenItemDTO kitchenItemDTO = new KitchenItemDTO();
        kitchenItemDTO.setGuid("2a371d7b-71ab-4856-860c-648dde411f2e");
        kitchenItemDTO.setDisplayType(0);
        kitchenItemDTO.setOrderGuid("orderGuid");
        kitchenItemDTO.setOrderNumber("orderNo");
        kitchenItemDTO.setOrderSerialNo("orderSerialNo");

        // Configure QueueConfigService.list(...).
        final QueueConfigDO queueConfigDO = new QueueConfigDO();
        queueConfigDO.setId(0L);
        queueConfigDO.setGuid("deviceGuid");
        queueConfigDO.setStoreGuid("storeGuid");
        queueConfigDO.setSource(0);
        queueConfigDO.setConfirmTtlLevel(0);
        final List<QueueConfigDO> queueConfigDOS = Arrays.asList(queueConfigDO);
        when(mockQueueConfigService.list(any(LambdaQueryWrapper.class))).thenReturn(queueConfigDOS);

        when(mockDistributedIdService.nextQueueItemGuid()).thenReturn("2bb975ef-d1f2-4615-a82f-430a8da75495");

        // Run the test
        queueItemServiceImplUnderTest.dstCallForMealAgain(kitchenItemDTO);

        // Verify the results
        verify(mockQueuePushService).statusChanged("enterpriseGuid", "storeGuid", "deviceGuid", "voiceMsg");
        verify(mockDefaultRocketMqProducer).sendMessage(any(Message.class));
    }

    @Test
    public void testDstCallForMealAgain_QueueConfigServiceReturnsNoItems() {
        // Setup
        final KitchenItemDTO kitchenItemDTO = new KitchenItemDTO();
        kitchenItemDTO.setGuid("2a371d7b-71ab-4856-860c-648dde411f2e");
        kitchenItemDTO.setDisplayType(0);
        kitchenItemDTO.setOrderGuid("orderGuid");
        kitchenItemDTO.setOrderNumber("orderNo");
        kitchenItemDTO.setOrderSerialNo("orderSerialNo");

        when(mockQueueConfigService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        queueItemServiceImplUnderTest.dstCallForMealAgain(kitchenItemDTO);

        // Verify the results
    }
}
