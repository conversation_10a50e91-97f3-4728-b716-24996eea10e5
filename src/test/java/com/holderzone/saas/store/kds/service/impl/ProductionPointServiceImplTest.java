package com.holderzone.saas.store.kds.service.impl;

import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.resp.MappingRespDTO;
import com.holderzone.saas.store.dto.kds.req.*;
import com.holderzone.saas.store.dto.kds.resp.*;
import com.holderzone.saas.store.kds.entity.domain.ItemConfigDO;
import com.holderzone.saas.store.kds.entity.domain.PrdPointItemDO;
import com.holderzone.saas.store.kds.entity.domain.ProductionPointDO;
import com.holderzone.saas.store.kds.entity.read.KitchenItemReadDO;
import com.holderzone.saas.store.kds.entity.read.PointItemReadDO;
import com.holderzone.saas.store.kds.mapstruct.DeviceConfigMapstruct;
import com.holderzone.saas.store.kds.service.*;
import com.holderzone.saas.store.kds.service.rpc.ItemRpcService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ProductionPointServiceImplTest {

    @Mock
    private DeviceConfigService mockDeviceConfigService;
    @Mock
    private PrdPointItemService mockPrdPointItemService;
    @Mock
    private ItemConfigService mockItemConfigService;
    @Mock
    private ItemRpcService mockItemRpcService;
    @Mock
    private DeviceConfigMapstruct mockDeviceConfigMapstruct;
    @Mock
    private DistributedIdService mockDistributedIdService;

    @Mock
    private DeviceBindItemGroupService mockDeviceBindItemGroupService;

    @Mock
    private BindItemService mockBindItemService;

    private ProductionPointServiceImpl productionPointServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        productionPointServiceImplUnderTest = new ProductionPointServiceImpl(mockDeviceConfigService,
                mockPrdPointItemService, mockItemConfigService, mockItemRpcService, mockDeviceConfigMapstruct,
                mockDistributedIdService, mockDeviceBindItemGroupService, mockBindItemService);
    }

    @Test
    public void testCreatePoint() {
        // Setup
        final PrdPointCreateReqDTO prdPointCreateReqDTO = new PrdPointCreateReqDTO();
        prdPointCreateReqDTO.setStoreGuid("storeGuid");
        prdPointCreateReqDTO.setDeviceId("deviceId");
        prdPointCreateReqDTO.setName("name");

        // Configure DeviceConfigMapstruct.fromPointCreateReq(...).
        final ProductionPointDO productionPointDO = new ProductionPointDO();
        productionPointDO.setId(0L);
        productionPointDO.setGuid("9841eaf3-607f-46d1-9c33-e728c5e5e0fa");
        productionPointDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        productionPointDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        productionPointDO.setStoreGuid("storeGuid");
        productionPointDO.setDeviceId("deviceId");
        productionPointDO.setName("name");
        final PrdPointCreateReqDTO prdPointCreateReqDTO1 = new PrdPointCreateReqDTO();
        prdPointCreateReqDTO1.setStoreGuid("storeGuid");
        prdPointCreateReqDTO1.setDeviceId("deviceId");
        prdPointCreateReqDTO1.setName("name");
        when(mockDeviceConfigMapstruct.fromPointCreateReq(prdPointCreateReqDTO1)).thenReturn(productionPointDO);

        when(mockDistributedIdService.nextPrdPointGuid()).thenReturn("9841eaf3-607f-46d1-9c33-e728c5e5e0fa");

        // Run the test
        final String result = productionPointServiceImplUnderTest.createPoint(prdPointCreateReqDTO);

        // Verify the results
        assertThat(result).isEqualTo("9841eaf3-607f-46d1-9c33-e728c5e5e0fa");
        verify(mockDeviceConfigService).assertThatDeviceExists("storeGuid", "deviceId");
    }

    @Test
    public void testDeletePoint() {
        // Setup
        final PrdPointDelReqDTO prdPointDelReqDTO = new PrdPointDelReqDTO();
        prdPointDelReqDTO.setStoreGuid("storeGuid");
        prdPointDelReqDTO.setDeviceId("deviceId");
        prdPointDelReqDTO.setPointGuid("pointGuid");

        // Run the test
        productionPointServiceImplUnderTest.deletePoint(prdPointDelReqDTO);

        // Verify the results
        verify(mockDeviceConfigService).assertThatDeviceExists("storeGuid", "deviceId");

        // Confirm PrdPointItemService.unbindItem(...).
        final PrdPointDelReqDTO prdPointDelReqDTO1 = new PrdPointDelReqDTO();
        prdPointDelReqDTO1.setStoreGuid("storeGuid");
        prdPointDelReqDTO1.setDeviceId("deviceId");
        prdPointDelReqDTO1.setPointGuid("pointGuid");
        verify(mockPrdPointItemService).unbindItem(prdPointDelReqDTO1);
    }

    @Test
    public void testReInitialize() {
        // Setup
        // Run the test
        productionPointServiceImplUnderTest.reInitialize("storeGuid", "deviceId");

        // Verify the results
        verify(mockPrdPointItemService).reInitialize("storeGuid", "deviceId");
    }

    @Test
    public void testListPoint() {
        // Setup
        final PrdPointListReqDTO prdPointListReqDTO = new PrdPointListReqDTO();
        prdPointListReqDTO.setStoreGuid("storeGuid");
        prdPointListReqDTO.setDeviceId("deviceId");

        final ProductionPointRespDTO productionPointRespDTO = new ProductionPointRespDTO();
        productionPointRespDTO.setGuid("b40e731b-5b51-424c-a79d-1016143988e4");
        productionPointRespDTO.setName("name");
        productionPointRespDTO.setStoreGuid("storeGuid");
        productionPointRespDTO.setDeviceId("deviceId");
        productionPointRespDTO.setBoundItemCount(0);
        final List<ProductionPointRespDTO> expectedResult = Arrays.asList(productionPointRespDTO);

        // Configure DeviceConfigMapstruct.toPointResp(...).
        final ProductionPointRespDTO productionPointRespDTO1 = new ProductionPointRespDTO();
        productionPointRespDTO1.setGuid("b40e731b-5b51-424c-a79d-1016143988e4");
        productionPointRespDTO1.setName("name");
        productionPointRespDTO1.setStoreGuid("storeGuid");
        productionPointRespDTO1.setDeviceId("deviceId");
        productionPointRespDTO1.setBoundItemCount(0);
        final List<ProductionPointRespDTO> productionPointRespDTOS = Arrays.asList(productionPointRespDTO1);
        final PointItemReadDO pointItemReadDO = new PointItemReadDO();
        pointItemReadDO.setId(0L);
        pointItemReadDO.setGuid("9841eaf3-607f-46d1-9c33-e728c5e5e0fa");
        pointItemReadDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        pointItemReadDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        pointItemReadDO.setStoreGuid("storeGuid");
        pointItemReadDO.setDeviceId("deviceId");
        pointItemReadDO.setPointName("name");
        pointItemReadDO.setItemCount(0);
        final KitchenItemReadDO kitchenItemReadDO = new KitchenItemReadDO();
        kitchenItemReadDO.setId(0L);
        kitchenItemReadDO.setGuid("9841eaf3-607f-46d1-9c33-e728c5e5e0fa");
        kitchenItemReadDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kitchenItemReadDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        pointItemReadDO.setItems(Arrays.asList(kitchenItemReadDO));
        final List<PointItemReadDO> pointItemReadDOS = Arrays.asList(pointItemReadDO);
        when(mockDeviceConfigMapstruct.toPointResp(pointItemReadDOS)).thenReturn(productionPointRespDTOS);

        // Run the test
        final List<ProductionPointRespDTO> result = productionPointServiceImplUnderTest.listPoint(prdPointListReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testListPoint_DeviceConfigMapstructReturnsNoItems() {
        // Setup
        final PrdPointListReqDTO prdPointListReqDTO = new PrdPointListReqDTO();
        prdPointListReqDTO.setStoreGuid("storeGuid");
        prdPointListReqDTO.setDeviceId("deviceId");

        // Configure DeviceConfigMapstruct.toPointResp(...).
        final PointItemReadDO pointItemReadDO = new PointItemReadDO();
        pointItemReadDO.setId(0L);
        pointItemReadDO.setGuid("9841eaf3-607f-46d1-9c33-e728c5e5e0fa");
        pointItemReadDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        pointItemReadDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        pointItemReadDO.setStoreGuid("storeGuid");
        pointItemReadDO.setDeviceId("deviceId");
        pointItemReadDO.setPointName("name");
        pointItemReadDO.setItemCount(0);
        final KitchenItemReadDO kitchenItemReadDO = new KitchenItemReadDO();
        kitchenItemReadDO.setId(0L);
        kitchenItemReadDO.setGuid("9841eaf3-607f-46d1-9c33-e728c5e5e0fa");
        kitchenItemReadDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kitchenItemReadDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        pointItemReadDO.setItems(Arrays.asList(kitchenItemReadDO));
        final List<PointItemReadDO> pointItemReadDOS = Arrays.asList(pointItemReadDO);
        when(mockDeviceConfigMapstruct.toPointResp(pointItemReadDOS)).thenReturn(Collections.emptyList());

        // Run the test
        final List<ProductionPointRespDTO> result = productionPointServiceImplUnderTest.listPoint(prdPointListReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testUpdatePoint() {
        // Setup
        final PrdPointUpdateReqDTO prdPointUpdateReqDTO = new PrdPointUpdateReqDTO();
        prdPointUpdateReqDTO.setStoreGuid("storeGuid");
        prdPointUpdateReqDTO.setDeviceId("deviceId");
        prdPointUpdateReqDTO.setPointGuid("pointGuid");
        prdPointUpdateReqDTO.setName("name");

        // Configure DeviceConfigMapstruct.fromPointUpdateReq(...).
        final ProductionPointDO productionPointDO = new ProductionPointDO();
        productionPointDO.setId(0L);
        productionPointDO.setGuid("9841eaf3-607f-46d1-9c33-e728c5e5e0fa");
        productionPointDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        productionPointDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        productionPointDO.setStoreGuid("storeGuid");
        productionPointDO.setDeviceId("deviceId");
        productionPointDO.setName("name");
        final PrdPointUpdateReqDTO prdPointUpdateReqDTO1 = new PrdPointUpdateReqDTO();
        prdPointUpdateReqDTO1.setStoreGuid("storeGuid");
        prdPointUpdateReqDTO1.setDeviceId("deviceId");
        prdPointUpdateReqDTO1.setPointGuid("pointGuid");
        prdPointUpdateReqDTO1.setName("name");
        when(mockDeviceConfigMapstruct.fromPointUpdateReq(prdPointUpdateReqDTO1)).thenReturn(productionPointDO);

        // Run the test
        productionPointServiceImplUnderTest.updatePoint(prdPointUpdateReqDTO);

        // Verify the results
        verify(mockDeviceConfigService).assertThatDeviceExists("storeGuid", "deviceId");
    }

    @Test
    public void testBindPointItem() {
        // Setup
        final PrdPointItemBindReqDTO prdPointItemBindReqDTO = new PrdPointItemBindReqDTO();
        prdPointItemBindReqDTO.setStoreGuid("storeGuid");
        prdPointItemBindReqDTO.setDeviceId("deviceId");
        prdPointItemBindReqDTO.setPointGuid("pointGuid");
        final PrdDstItemBindDTO prdDstItemBindDTO = new PrdDstItemBindDTO();
        prdDstItemBindDTO.setItemGuid("itemGuid");
        prdPointItemBindReqDTO.setBindingItems(Arrays.asList(prdDstItemBindDTO));

        // Run the test
        productionPointServiceImplUnderTest.bindPointItem(prdPointItemBindReqDTO);

        // Verify the results
        verify(mockDeviceConfigService).assertThatDeviceExists("storeGuid", "deviceId");

        // Confirm PrdPointItemService.bindItem(...).
        final PrdPointItemBindReqDTO prdPointItemBindReqDTO1 = new PrdPointItemBindReqDTO();
        prdPointItemBindReqDTO1.setStoreGuid("storeGuid");
        prdPointItemBindReqDTO1.setDeviceId("deviceId");
        prdPointItemBindReqDTO1.setPointGuid("pointGuid");
        final PrdDstItemBindDTO prdDstItemBindDTO1 = new PrdDstItemBindDTO();
        prdDstItemBindDTO1.setItemGuid("itemGuid");
        prdPointItemBindReqDTO1.setBindingItems(Arrays.asList(prdDstItemBindDTO1));
        verify(mockPrdPointItemService).bindItem(prdPointItemBindReqDTO1);
    }

    @Test
    public void testUnbindPointItem() {
        // Setup
        final PrdPointItemBindReqDTO prdPointItemBindReqDTO = new PrdPointItemBindReqDTO();
        prdPointItemBindReqDTO.setStoreGuid("storeGuid");
        prdPointItemBindReqDTO.setDeviceId("deviceId");
        prdPointItemBindReqDTO.setPointGuid("pointGuid");
        final PrdDstItemBindDTO prdDstItemBindDTO = new PrdDstItemBindDTO();
        prdDstItemBindDTO.setItemGuid("itemGuid");
        prdPointItemBindReqDTO.setBindingItems(Arrays.asList(prdDstItemBindDTO));

        // Run the test
        productionPointServiceImplUnderTest.unbindPointItem(prdPointItemBindReqDTO);

        // Verify the results
        verify(mockDeviceConfigService).assertThatDeviceExists("storeGuid", "deviceId");

        // Confirm PrdPointItemService.unbindItem(...).
        final PrdPointItemBindReqDTO prdPointItemBindReqDTO1 = new PrdPointItemBindReqDTO();
        prdPointItemBindReqDTO1.setStoreGuid("storeGuid");
        prdPointItemBindReqDTO1.setDeviceId("deviceId");
        prdPointItemBindReqDTO1.setPointGuid("pointGuid");
        final PrdDstItemBindDTO prdDstItemBindDTO1 = new PrdDstItemBindDTO();
        prdDstItemBindDTO1.setItemGuid("itemGuid");
        prdPointItemBindReqDTO1.setBindingItems(Arrays.asList(prdDstItemBindDTO1));
        verify(mockPrdPointItemService).unbindItem(prdPointItemBindReqDTO1);
    }

    @Test
    public void testQueryBindingDetails() {
        // Setup
        final PrdPointItemQueryReqDTO prdPointItemQueryReqDTO = new PrdPointItemQueryReqDTO();
        prdPointItemQueryReqDTO.setStoreGuid("data");
        prdPointItemQueryReqDTO.setDeviceId("deviceId");
        prdPointItemQueryReqDTO.setPointGuid("pointGuid");
        prdPointItemQueryReqDTO.setSearchKey("searchKey");

        final PointBindDetailsRespDTO expectedResult = new PointBindDetailsRespDTO();
        expectedResult.setBoundItemCount(0);
        final PointTypeBindRespDTO pointTypeBindRespDTO = new PointTypeBindRespDTO();
        pointTypeBindRespDTO.setTypeGuid("first");
        pointTypeBindRespDTO.setTypeName("itemName");
        final PointItemBindRespDTO pointItemBindRespDTO = new PointItemBindRespDTO();
        pointItemBindRespDTO.setItemGuid("first");
        pointItemBindRespDTO.setItemName("itemName");
        pointItemBindRespDTO.setPinyin("pinyin");
        pointItemBindRespDTO.setIsBoundBySelf(false);
        pointItemBindRespDTO.setIsBoundByOthers(false);
        final PointSkuBindRespDTO pointSkuBindRespDTO = new PointSkuBindRespDTO();
        pointSkuBindRespDTO.setDeviceId("deviceId");
        pointSkuBindRespDTO.setDeviceName("deviceName");
        pointSkuBindRespDTO.setPointGuid("pointGuid");
        pointSkuBindRespDTO.setPointName("pointName");
        pointSkuBindRespDTO.setSkuGuid("eDishSkuCode");
        pointSkuBindRespDTO.setSkuName("eDishSkuName");
        pointSkuBindRespDTO.setSkuCode("skuCode");
        pointSkuBindRespDTO.setTimeout(0);
        pointSkuBindRespDTO.setMaxCopies(0);
        pointSkuBindRespDTO.setDisplayType(0);
        pointSkuBindRespDTO.setIsBoundBySelf(false);
        pointSkuBindRespDTO.setIsBoundByOthers(false);
        pointItemBindRespDTO.setSkus(Arrays.asList(pointSkuBindRespDTO));
        pointTypeBindRespDTO.setItemList(Arrays.asList(pointItemBindRespDTO));
        expectedResult.setPointTypeBindList(Arrays.asList(pointTypeBindRespDTO));

        when(mockDeviceConfigService.getDeviceNameMapOfStore("data")).thenReturn(new HashMap<>());

        // Configure PrdPointItemService.queryBoundItem(...).
        final PrdPointItemDO prdPointItemDO = new PrdPointItemDO();
        prdPointItemDO.setId(0L);
        prdPointItemDO.setGuid("9841eaf3-607f-46d1-9c33-e728c5e5e0fa");
        prdPointItemDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        prdPointItemDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        prdPointItemDO.setDeviceId("deviceId");
        prdPointItemDO.setPointGuid("pointGuid");
        prdPointItemDO.setSkuGuid("skuGuid");
        final List<PrdPointItemDO> prdPointItemDOS = Arrays.asList(prdPointItemDO);
        final PrdPointItemQueryReqDTO prdPointItemQueryReqDTO1 = new PrdPointItemQueryReqDTO();
        prdPointItemQueryReqDTO1.setStoreGuid("data");
        prdPointItemQueryReqDTO1.setDeviceId("deviceId");
        prdPointItemQueryReqDTO1.setPointGuid("pointGuid");
        prdPointItemQueryReqDTO1.setSearchKey("searchKey");
        when(mockPrdPointItemService.queryBoundItem(prdPointItemQueryReqDTO1)).thenReturn(prdPointItemDOS);

        // Configure ItemRpcService.kdsMapping(...).
        final MappingRespDTO mappingRespDTO = new MappingRespDTO();
        mappingRespDTO.setPinyin("pinyin");
        mappingRespDTO.seteDishSkuCode("eDishSkuCode");
        mappingRespDTO.setSkuCode("skuCode");
        mappingRespDTO.seteDishSkuName("eDishSkuName");
        mappingRespDTO.seteDishCode("eDishCode");
        mappingRespDTO.seteDishName("eDishName");
        mappingRespDTO.setCategoryName("categoryName");
        mappingRespDTO.setCategoryId("categoryId");
        final List<MappingRespDTO> mappingRespDTOS = Arrays.asList(mappingRespDTO);
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("data");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        itemSingleDTO.setSkuGuids(Arrays.asList("value"));
        when(mockItemRpcService.kdsMapping(itemSingleDTO)).thenReturn(mappingRespDTOS);

        // Configure ItemConfigService.queryBatchByStoreGuid(...).
        final ItemConfigDO itemConfigDO = new ItemConfigDO();
        itemConfigDO.setId(0L);
        itemConfigDO.setGuid("9841eaf3-607f-46d1-9c33-e728c5e5e0fa");
        itemConfigDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemConfigDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemConfigDO.setSkuGuid("skuGuid");
        itemConfigDO.setTimeout(0);
        itemConfigDO.setMaxCopies(0);
        itemConfigDO.setDisplayType(0);
        final List<ItemConfigDO> itemConfigDOS = Arrays.asList(itemConfigDO);
        when(mockItemConfigService.queryBatchByStoreGuid("data")).thenReturn(itemConfigDOS);

        // Run the test
        final PointBindDetailsRespDTO result = productionPointServiceImplUnderTest.queryBindingDetails(
                prdPointItemQueryReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryBindingDetails_PrdPointItemServiceReturnsNoItems() {
        // Setup
        final PrdPointItemQueryReqDTO prdPointItemQueryReqDTO = new PrdPointItemQueryReqDTO();
        prdPointItemQueryReqDTO.setStoreGuid("data");
        prdPointItemQueryReqDTO.setDeviceId("deviceId");
        prdPointItemQueryReqDTO.setPointGuid("pointGuid");
        prdPointItemQueryReqDTO.setSearchKey("searchKey");

        final PointBindDetailsRespDTO expectedResult = new PointBindDetailsRespDTO();
        expectedResult.setBoundItemCount(0);
        final PointTypeBindRespDTO pointTypeBindRespDTO = new PointTypeBindRespDTO();
        pointTypeBindRespDTO.setTypeGuid("first");
        pointTypeBindRespDTO.setTypeName("itemName");
        final PointItemBindRespDTO pointItemBindRespDTO = new PointItemBindRespDTO();
        pointItemBindRespDTO.setItemGuid("first");
        pointItemBindRespDTO.setItemName("itemName");
        pointItemBindRespDTO.setPinyin("pinyin");
        pointItemBindRespDTO.setIsBoundBySelf(false);
        pointItemBindRespDTO.setIsBoundByOthers(false);
        final PointSkuBindRespDTO pointSkuBindRespDTO = new PointSkuBindRespDTO();
        pointSkuBindRespDTO.setDeviceId("deviceId");
        pointSkuBindRespDTO.setDeviceName("deviceName");
        pointSkuBindRespDTO.setPointGuid("pointGuid");
        pointSkuBindRespDTO.setPointName("pointName");
        pointSkuBindRespDTO.setSkuGuid("eDishSkuCode");
        pointSkuBindRespDTO.setSkuName("eDishSkuName");
        pointSkuBindRespDTO.setSkuCode("skuCode");
        pointSkuBindRespDTO.setTimeout(0);
        pointSkuBindRespDTO.setMaxCopies(0);
        pointSkuBindRespDTO.setDisplayType(0);
        pointSkuBindRespDTO.setIsBoundBySelf(false);
        pointSkuBindRespDTO.setIsBoundByOthers(false);
        pointItemBindRespDTO.setSkus(Arrays.asList(pointSkuBindRespDTO));
        pointTypeBindRespDTO.setItemList(Arrays.asList(pointItemBindRespDTO));
        expectedResult.setPointTypeBindList(Arrays.asList(pointTypeBindRespDTO));

        when(mockDeviceConfigService.getDeviceNameMapOfStore("data")).thenReturn(new HashMap<>());

        // Configure PrdPointItemService.queryBoundItem(...).
        final PrdPointItemQueryReqDTO prdPointItemQueryReqDTO1 = new PrdPointItemQueryReqDTO();
        prdPointItemQueryReqDTO1.setStoreGuid("data");
        prdPointItemQueryReqDTO1.setDeviceId("deviceId");
        prdPointItemQueryReqDTO1.setPointGuid("pointGuid");
        prdPointItemQueryReqDTO1.setSearchKey("searchKey");
        when(mockPrdPointItemService.queryBoundItem(prdPointItemQueryReqDTO1)).thenReturn(Collections.emptyList());

        // Configure ItemRpcService.kdsMapping(...).
        final MappingRespDTO mappingRespDTO = new MappingRespDTO();
        mappingRespDTO.setPinyin("pinyin");
        mappingRespDTO.seteDishSkuCode("eDishSkuCode");
        mappingRespDTO.setSkuCode("skuCode");
        mappingRespDTO.seteDishSkuName("eDishSkuName");
        mappingRespDTO.seteDishCode("eDishCode");
        mappingRespDTO.seteDishName("eDishName");
        mappingRespDTO.setCategoryName("categoryName");
        mappingRespDTO.setCategoryId("categoryId");
        final List<MappingRespDTO> mappingRespDTOS = Arrays.asList(mappingRespDTO);
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("data");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        itemSingleDTO.setSkuGuids(Arrays.asList("value"));
        when(mockItemRpcService.kdsMapping(itemSingleDTO)).thenReturn(mappingRespDTOS);

        // Configure ItemConfigService.queryBatchByStoreGuid(...).
        final ItemConfigDO itemConfigDO = new ItemConfigDO();
        itemConfigDO.setId(0L);
        itemConfigDO.setGuid("9841eaf3-607f-46d1-9c33-e728c5e5e0fa");
        itemConfigDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemConfigDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemConfigDO.setSkuGuid("skuGuid");
        itemConfigDO.setTimeout(0);
        itemConfigDO.setMaxCopies(0);
        itemConfigDO.setDisplayType(0);
        final List<ItemConfigDO> itemConfigDOS = Arrays.asList(itemConfigDO);
        when(mockItemConfigService.queryBatchByStoreGuid("data")).thenReturn(itemConfigDOS);

        // Run the test
        final PointBindDetailsRespDTO result = productionPointServiceImplUnderTest.queryBindingDetails(
                prdPointItemQueryReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryBindingDetails_ItemRpcServiceReturnsNoItems() {
        // Setup
        final PrdPointItemQueryReqDTO prdPointItemQueryReqDTO = new PrdPointItemQueryReqDTO();
        prdPointItemQueryReqDTO.setStoreGuid("data");
        prdPointItemQueryReqDTO.setDeviceId("deviceId");
        prdPointItemQueryReqDTO.setPointGuid("pointGuid");
        prdPointItemQueryReqDTO.setSearchKey("searchKey");

        final PointBindDetailsRespDTO expectedResult = new PointBindDetailsRespDTO();
        expectedResult.setBoundItemCount(0);
        final PointTypeBindRespDTO pointTypeBindRespDTO = new PointTypeBindRespDTO();
        pointTypeBindRespDTO.setTypeGuid("first");
        pointTypeBindRespDTO.setTypeName("itemName");
        final PointItemBindRespDTO pointItemBindRespDTO = new PointItemBindRespDTO();
        pointItemBindRespDTO.setItemGuid("first");
        pointItemBindRespDTO.setItemName("itemName");
        pointItemBindRespDTO.setPinyin("pinyin");
        pointItemBindRespDTO.setIsBoundBySelf(false);
        pointItemBindRespDTO.setIsBoundByOthers(false);
        final PointSkuBindRespDTO pointSkuBindRespDTO = new PointSkuBindRespDTO();
        pointSkuBindRespDTO.setDeviceId("deviceId");
        pointSkuBindRespDTO.setDeviceName("deviceName");
        pointSkuBindRespDTO.setPointGuid("pointGuid");
        pointSkuBindRespDTO.setPointName("pointName");
        pointSkuBindRespDTO.setSkuGuid("eDishSkuCode");
        pointSkuBindRespDTO.setSkuName("eDishSkuName");
        pointSkuBindRespDTO.setSkuCode("skuCode");
        pointSkuBindRespDTO.setTimeout(0);
        pointSkuBindRespDTO.setMaxCopies(0);
        pointSkuBindRespDTO.setDisplayType(0);
        pointSkuBindRespDTO.setIsBoundBySelf(false);
        pointSkuBindRespDTO.setIsBoundByOthers(false);
        pointItemBindRespDTO.setSkus(Arrays.asList(pointSkuBindRespDTO));
        pointTypeBindRespDTO.setItemList(Arrays.asList(pointItemBindRespDTO));
        expectedResult.setPointTypeBindList(Arrays.asList(pointTypeBindRespDTO));

        when(mockDeviceConfigService.getDeviceNameMapOfStore("data")).thenReturn(new HashMap<>());

        // Configure PrdPointItemService.queryBoundItem(...).
        final PrdPointItemDO prdPointItemDO = new PrdPointItemDO();
        prdPointItemDO.setId(0L);
        prdPointItemDO.setGuid("9841eaf3-607f-46d1-9c33-e728c5e5e0fa");
        prdPointItemDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        prdPointItemDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        prdPointItemDO.setDeviceId("deviceId");
        prdPointItemDO.setPointGuid("pointGuid");
        prdPointItemDO.setSkuGuid("skuGuid");
        final List<PrdPointItemDO> prdPointItemDOS = Arrays.asList(prdPointItemDO);
        final PrdPointItemQueryReqDTO prdPointItemQueryReqDTO1 = new PrdPointItemQueryReqDTO();
        prdPointItemQueryReqDTO1.setStoreGuid("data");
        prdPointItemQueryReqDTO1.setDeviceId("deviceId");
        prdPointItemQueryReqDTO1.setPointGuid("pointGuid");
        prdPointItemQueryReqDTO1.setSearchKey("searchKey");
        when(mockPrdPointItemService.queryBoundItem(prdPointItemQueryReqDTO1)).thenReturn(prdPointItemDOS);

        // Configure ItemRpcService.kdsMapping(...).
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("data");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        itemSingleDTO.setSkuGuids(Arrays.asList("value"));
        when(mockItemRpcService.kdsMapping(itemSingleDTO)).thenReturn(Collections.emptyList());

        // Configure ItemConfigService.queryBatchByStoreGuid(...).
        final ItemConfigDO itemConfigDO = new ItemConfigDO();
        itemConfigDO.setId(0L);
        itemConfigDO.setGuid("9841eaf3-607f-46d1-9c33-e728c5e5e0fa");
        itemConfigDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemConfigDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemConfigDO.setSkuGuid("skuGuid");
        itemConfigDO.setTimeout(0);
        itemConfigDO.setMaxCopies(0);
        itemConfigDO.setDisplayType(0);
        final List<ItemConfigDO> itemConfigDOS = Arrays.asList(itemConfigDO);
        when(mockItemConfigService.queryBatchByStoreGuid("data")).thenReturn(itemConfigDOS);

        // Run the test
        final PointBindDetailsRespDTO result = productionPointServiceImplUnderTest.queryBindingDetails(
                prdPointItemQueryReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryBindingDetails_ItemConfigServiceReturnsNoItems() {
        // Setup
        final PrdPointItemQueryReqDTO prdPointItemQueryReqDTO = new PrdPointItemQueryReqDTO();
        prdPointItemQueryReqDTO.setStoreGuid("data");
        prdPointItemQueryReqDTO.setDeviceId("deviceId");
        prdPointItemQueryReqDTO.setPointGuid("pointGuid");
        prdPointItemQueryReqDTO.setSearchKey("searchKey");

        final PointBindDetailsRespDTO expectedResult = new PointBindDetailsRespDTO();
        expectedResult.setBoundItemCount(0);
        final PointTypeBindRespDTO pointTypeBindRespDTO = new PointTypeBindRespDTO();
        pointTypeBindRespDTO.setTypeGuid("first");
        pointTypeBindRespDTO.setTypeName("itemName");
        final PointItemBindRespDTO pointItemBindRespDTO = new PointItemBindRespDTO();
        pointItemBindRespDTO.setItemGuid("first");
        pointItemBindRespDTO.setItemName("itemName");
        pointItemBindRespDTO.setPinyin("pinyin");
        pointItemBindRespDTO.setIsBoundBySelf(false);
        pointItemBindRespDTO.setIsBoundByOthers(false);
        final PointSkuBindRespDTO pointSkuBindRespDTO = new PointSkuBindRespDTO();
        pointSkuBindRespDTO.setDeviceId("deviceId");
        pointSkuBindRespDTO.setDeviceName("deviceName");
        pointSkuBindRespDTO.setPointGuid("pointGuid");
        pointSkuBindRespDTO.setPointName("pointName");
        pointSkuBindRespDTO.setSkuGuid("eDishSkuCode");
        pointSkuBindRespDTO.setSkuName("eDishSkuName");
        pointSkuBindRespDTO.setSkuCode("skuCode");
        pointSkuBindRespDTO.setTimeout(0);
        pointSkuBindRespDTO.setMaxCopies(0);
        pointSkuBindRespDTO.setDisplayType(0);
        pointSkuBindRespDTO.setIsBoundBySelf(false);
        pointSkuBindRespDTO.setIsBoundByOthers(false);
        pointItemBindRespDTO.setSkus(Arrays.asList(pointSkuBindRespDTO));
        pointTypeBindRespDTO.setItemList(Arrays.asList(pointItemBindRespDTO));
        expectedResult.setPointTypeBindList(Arrays.asList(pointTypeBindRespDTO));

        when(mockDeviceConfigService.getDeviceNameMapOfStore("data")).thenReturn(new HashMap<>());

        // Configure PrdPointItemService.queryBoundItem(...).
        final PrdPointItemDO prdPointItemDO = new PrdPointItemDO();
        prdPointItemDO.setId(0L);
        prdPointItemDO.setGuid("9841eaf3-607f-46d1-9c33-e728c5e5e0fa");
        prdPointItemDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        prdPointItemDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        prdPointItemDO.setDeviceId("deviceId");
        prdPointItemDO.setPointGuid("pointGuid");
        prdPointItemDO.setSkuGuid("skuGuid");
        final List<PrdPointItemDO> prdPointItemDOS = Arrays.asList(prdPointItemDO);
        final PrdPointItemQueryReqDTO prdPointItemQueryReqDTO1 = new PrdPointItemQueryReqDTO();
        prdPointItemQueryReqDTO1.setStoreGuid("data");
        prdPointItemQueryReqDTO1.setDeviceId("deviceId");
        prdPointItemQueryReqDTO1.setPointGuid("pointGuid");
        prdPointItemQueryReqDTO1.setSearchKey("searchKey");
        when(mockPrdPointItemService.queryBoundItem(prdPointItemQueryReqDTO1)).thenReturn(prdPointItemDOS);

        // Configure ItemRpcService.kdsMapping(...).
        final MappingRespDTO mappingRespDTO = new MappingRespDTO();
        mappingRespDTO.setPinyin("pinyin");
        mappingRespDTO.seteDishSkuCode("eDishSkuCode");
        mappingRespDTO.setSkuCode("skuCode");
        mappingRespDTO.seteDishSkuName("eDishSkuName");
        mappingRespDTO.seteDishCode("eDishCode");
        mappingRespDTO.seteDishName("eDishName");
        mappingRespDTO.setCategoryName("categoryName");
        mappingRespDTO.setCategoryId("categoryId");
        final List<MappingRespDTO> mappingRespDTOS = Arrays.asList(mappingRespDTO);
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("data");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        itemSingleDTO.setSkuGuids(Arrays.asList("value"));
        when(mockItemRpcService.kdsMapping(itemSingleDTO)).thenReturn(mappingRespDTOS);

        when(mockItemConfigService.queryBatchByStoreGuid("data")).thenReturn(Collections.emptyList());

        // Run the test
        final PointBindDetailsRespDTO result = productionPointServiceImplUnderTest.queryBindingDetails(
                prdPointItemQueryReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryBoundPointItem() {
        // Setup
        final PrdPointItemQueryReqDTO prdPointItemQueryReqDTO = new PrdPointItemQueryReqDTO();
        prdPointItemQueryReqDTO.setStoreGuid("data");
        prdPointItemQueryReqDTO.setDeviceId("deviceId");
        prdPointItemQueryReqDTO.setPointGuid("pointGuid");
        prdPointItemQueryReqDTO.setSearchKey("searchKey");

        final PointTypeBindRespDTO pointTypeBindRespDTO = new PointTypeBindRespDTO();
        pointTypeBindRespDTO.setTypeGuid("first");
        pointTypeBindRespDTO.setTypeName("itemName");
        final PointItemBindRespDTO pointItemBindRespDTO = new PointItemBindRespDTO();
        pointItemBindRespDTO.setItemGuid("first");
        pointItemBindRespDTO.setItemName("itemName");
        pointItemBindRespDTO.setPinyin("pinyin");
        pointItemBindRespDTO.setIsBoundBySelf(false);
        pointItemBindRespDTO.setIsBoundByOthers(false);
        final PointSkuBindRespDTO pointSkuBindRespDTO = new PointSkuBindRespDTO();
        pointSkuBindRespDTO.setDeviceId("deviceId");
        pointSkuBindRespDTO.setDeviceName("deviceName");
        pointSkuBindRespDTO.setPointGuid("pointGuid");
        pointSkuBindRespDTO.setPointName("pointName");
        pointSkuBindRespDTO.setSkuGuid("eDishSkuCode");
        pointSkuBindRespDTO.setSkuName("eDishSkuName");
        pointSkuBindRespDTO.setSkuCode("skuCode");
        pointSkuBindRespDTO.setTimeout(0);
        pointSkuBindRespDTO.setMaxCopies(0);
        pointSkuBindRespDTO.setDisplayType(0);
        pointSkuBindRespDTO.setIsBoundBySelf(false);
        pointSkuBindRespDTO.setIsBoundByOthers(false);
        pointItemBindRespDTO.setSkus(Arrays.asList(pointSkuBindRespDTO));
        pointTypeBindRespDTO.setItemList(Arrays.asList(pointItemBindRespDTO));
        final List<PointTypeBindRespDTO> expectedResult = Arrays.asList(pointTypeBindRespDTO);
        when(mockDeviceConfigService.getDeviceNameMapOfStore("data")).thenReturn(new HashMap<>());

        // Configure PrdPointItemService.queryBoundItem(...).
        final PrdPointItemDO prdPointItemDO = new PrdPointItemDO();
        prdPointItemDO.setId(0L);
        prdPointItemDO.setGuid("9841eaf3-607f-46d1-9c33-e728c5e5e0fa");
        prdPointItemDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        prdPointItemDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        prdPointItemDO.setDeviceId("deviceId");
        prdPointItemDO.setPointGuid("pointGuid");
        prdPointItemDO.setSkuGuid("skuGuid");
        final List<PrdPointItemDO> prdPointItemDOS = Arrays.asList(prdPointItemDO);
        final PrdPointItemQueryReqDTO prdPointItemQueryReqDTO1 = new PrdPointItemQueryReqDTO();
        prdPointItemQueryReqDTO1.setStoreGuid("data");
        prdPointItemQueryReqDTO1.setDeviceId("deviceId");
        prdPointItemQueryReqDTO1.setPointGuid("pointGuid");
        prdPointItemQueryReqDTO1.setSearchKey("searchKey");
        when(mockPrdPointItemService.queryBoundItem(prdPointItemQueryReqDTO1)).thenReturn(prdPointItemDOS);

        // Configure ItemRpcService.kdsMapping(...).
        final MappingRespDTO mappingRespDTO = new MappingRespDTO();
        mappingRespDTO.setPinyin("pinyin");
        mappingRespDTO.seteDishSkuCode("eDishSkuCode");
        mappingRespDTO.setSkuCode("skuCode");
        mappingRespDTO.seteDishSkuName("eDishSkuName");
        mappingRespDTO.seteDishCode("eDishCode");
        mappingRespDTO.seteDishName("eDishName");
        mappingRespDTO.setCategoryName("categoryName");
        mappingRespDTO.setCategoryId("categoryId");
        final List<MappingRespDTO> mappingRespDTOS = Arrays.asList(mappingRespDTO);
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("data");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        itemSingleDTO.setSkuGuids(Arrays.asList("value"));
        when(mockItemRpcService.kdsMapping(itemSingleDTO)).thenReturn(mappingRespDTOS);

        // Configure ItemConfigService.queryBatchByStoreGuid(...).
        final ItemConfigDO itemConfigDO = new ItemConfigDO();
        itemConfigDO.setId(0L);
        itemConfigDO.setGuid("9841eaf3-607f-46d1-9c33-e728c5e5e0fa");
        itemConfigDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemConfigDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemConfigDO.setSkuGuid("skuGuid");
        itemConfigDO.setTimeout(0);
        itemConfigDO.setMaxCopies(0);
        itemConfigDO.setDisplayType(0);
        final List<ItemConfigDO> itemConfigDOS = Arrays.asList(itemConfigDO);
        when(mockItemConfigService.queryBatchByStoreGuid("data")).thenReturn(itemConfigDOS);

        // Run the test
        final List<PointTypeBindRespDTO> result = productionPointServiceImplUnderTest.queryBoundPointItem(
                prdPointItemQueryReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryBoundPointItem_PrdPointItemServiceReturnsNoItems() {
        // Setup
        final PrdPointItemQueryReqDTO prdPointItemQueryReqDTO = new PrdPointItemQueryReqDTO();
        prdPointItemQueryReqDTO.setStoreGuid("data");
        prdPointItemQueryReqDTO.setDeviceId("deviceId");
        prdPointItemQueryReqDTO.setPointGuid("pointGuid");
        prdPointItemQueryReqDTO.setSearchKey("searchKey");

        final PointTypeBindRespDTO pointTypeBindRespDTO = new PointTypeBindRespDTO();
        pointTypeBindRespDTO.setTypeGuid("first");
        pointTypeBindRespDTO.setTypeName("itemName");
        final PointItemBindRespDTO pointItemBindRespDTO = new PointItemBindRespDTO();
        pointItemBindRespDTO.setItemGuid("first");
        pointItemBindRespDTO.setItemName("itemName");
        pointItemBindRespDTO.setPinyin("pinyin");
        pointItemBindRespDTO.setIsBoundBySelf(false);
        pointItemBindRespDTO.setIsBoundByOthers(false);
        final PointSkuBindRespDTO pointSkuBindRespDTO = new PointSkuBindRespDTO();
        pointSkuBindRespDTO.setDeviceId("deviceId");
        pointSkuBindRespDTO.setDeviceName("deviceName");
        pointSkuBindRespDTO.setPointGuid("pointGuid");
        pointSkuBindRespDTO.setPointName("pointName");
        pointSkuBindRespDTO.setSkuGuid("eDishSkuCode");
        pointSkuBindRespDTO.setSkuName("eDishSkuName");
        pointSkuBindRespDTO.setSkuCode("skuCode");
        pointSkuBindRespDTO.setTimeout(0);
        pointSkuBindRespDTO.setMaxCopies(0);
        pointSkuBindRespDTO.setDisplayType(0);
        pointSkuBindRespDTO.setIsBoundBySelf(false);
        pointSkuBindRespDTO.setIsBoundByOthers(false);
        pointItemBindRespDTO.setSkus(Arrays.asList(pointSkuBindRespDTO));
        pointTypeBindRespDTO.setItemList(Arrays.asList(pointItemBindRespDTO));
        final List<PointTypeBindRespDTO> expectedResult = Arrays.asList(pointTypeBindRespDTO);
        when(mockDeviceConfigService.getDeviceNameMapOfStore("data")).thenReturn(new HashMap<>());

        // Configure PrdPointItemService.queryBoundItem(...).
        final PrdPointItemQueryReqDTO prdPointItemQueryReqDTO1 = new PrdPointItemQueryReqDTO();
        prdPointItemQueryReqDTO1.setStoreGuid("data");
        prdPointItemQueryReqDTO1.setDeviceId("deviceId");
        prdPointItemQueryReqDTO1.setPointGuid("pointGuid");
        prdPointItemQueryReqDTO1.setSearchKey("searchKey");
        when(mockPrdPointItemService.queryBoundItem(prdPointItemQueryReqDTO1)).thenReturn(Collections.emptyList());

        // Configure ItemRpcService.kdsMapping(...).
        final MappingRespDTO mappingRespDTO = new MappingRespDTO();
        mappingRespDTO.setPinyin("pinyin");
        mappingRespDTO.seteDishSkuCode("eDishSkuCode");
        mappingRespDTO.setSkuCode("skuCode");
        mappingRespDTO.seteDishSkuName("eDishSkuName");
        mappingRespDTO.seteDishCode("eDishCode");
        mappingRespDTO.seteDishName("eDishName");
        mappingRespDTO.setCategoryName("categoryName");
        mappingRespDTO.setCategoryId("categoryId");
        final List<MappingRespDTO> mappingRespDTOS = Arrays.asList(mappingRespDTO);
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("data");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        itemSingleDTO.setSkuGuids(Arrays.asList("value"));
        when(mockItemRpcService.kdsMapping(itemSingleDTO)).thenReturn(mappingRespDTOS);

        // Configure ItemConfigService.queryBatchByStoreGuid(...).
        final ItemConfigDO itemConfigDO = new ItemConfigDO();
        itemConfigDO.setId(0L);
        itemConfigDO.setGuid("9841eaf3-607f-46d1-9c33-e728c5e5e0fa");
        itemConfigDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemConfigDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemConfigDO.setSkuGuid("skuGuid");
        itemConfigDO.setTimeout(0);
        itemConfigDO.setMaxCopies(0);
        itemConfigDO.setDisplayType(0);
        final List<ItemConfigDO> itemConfigDOS = Arrays.asList(itemConfigDO);
        when(mockItemConfigService.queryBatchByStoreGuid("data")).thenReturn(itemConfigDOS);

        // Run the test
        final List<PointTypeBindRespDTO> result = productionPointServiceImplUnderTest.queryBoundPointItem(
                prdPointItemQueryReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryBoundPointItem_ItemRpcServiceReturnsNoItems() {
        // Setup
        final PrdPointItemQueryReqDTO prdPointItemQueryReqDTO = new PrdPointItemQueryReqDTO();
        prdPointItemQueryReqDTO.setStoreGuid("data");
        prdPointItemQueryReqDTO.setDeviceId("deviceId");
        prdPointItemQueryReqDTO.setPointGuid("pointGuid");
        prdPointItemQueryReqDTO.setSearchKey("searchKey");

        when(mockDeviceConfigService.getDeviceNameMapOfStore("data")).thenReturn(new HashMap<>());

        // Configure PrdPointItemService.queryBoundItem(...).
        final PrdPointItemDO prdPointItemDO = new PrdPointItemDO();
        prdPointItemDO.setId(0L);
        prdPointItemDO.setGuid("9841eaf3-607f-46d1-9c33-e728c5e5e0fa");
        prdPointItemDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        prdPointItemDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        prdPointItemDO.setDeviceId("deviceId");
        prdPointItemDO.setPointGuid("pointGuid");
        prdPointItemDO.setSkuGuid("skuGuid");
        final List<PrdPointItemDO> prdPointItemDOS = Arrays.asList(prdPointItemDO);
        final PrdPointItemQueryReqDTO prdPointItemQueryReqDTO1 = new PrdPointItemQueryReqDTO();
        prdPointItemQueryReqDTO1.setStoreGuid("data");
        prdPointItemQueryReqDTO1.setDeviceId("deviceId");
        prdPointItemQueryReqDTO1.setPointGuid("pointGuid");
        prdPointItemQueryReqDTO1.setSearchKey("searchKey");
        when(mockPrdPointItemService.queryBoundItem(prdPointItemQueryReqDTO1)).thenReturn(prdPointItemDOS);

        // Configure ItemRpcService.kdsMapping(...).
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("data");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        itemSingleDTO.setSkuGuids(Arrays.asList("value"));
        when(mockItemRpcService.kdsMapping(itemSingleDTO)).thenReturn(Collections.emptyList());

        // Configure ItemConfigService.queryBatchByStoreGuid(...).
        final ItemConfigDO itemConfigDO = new ItemConfigDO();
        itemConfigDO.setId(0L);
        itemConfigDO.setGuid("9841eaf3-607f-46d1-9c33-e728c5e5e0fa");
        itemConfigDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemConfigDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemConfigDO.setSkuGuid("skuGuid");
        itemConfigDO.setTimeout(0);
        itemConfigDO.setMaxCopies(0);
        itemConfigDO.setDisplayType(0);
        final List<ItemConfigDO> itemConfigDOS = Arrays.asList(itemConfigDO);
        when(mockItemConfigService.queryBatchByStoreGuid("data")).thenReturn(itemConfigDOS);

        // Run the test
        final List<PointTypeBindRespDTO> result = productionPointServiceImplUnderTest.queryBoundPointItem(
                prdPointItemQueryReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testQueryBoundPointItem_ItemConfigServiceReturnsNoItems() {
        // Setup
        final PrdPointItemQueryReqDTO prdPointItemQueryReqDTO = new PrdPointItemQueryReqDTO();
        prdPointItemQueryReqDTO.setStoreGuid("data");
        prdPointItemQueryReqDTO.setDeviceId("deviceId");
        prdPointItemQueryReqDTO.setPointGuid("pointGuid");
        prdPointItemQueryReqDTO.setSearchKey("searchKey");

        final PointTypeBindRespDTO pointTypeBindRespDTO = new PointTypeBindRespDTO();
        pointTypeBindRespDTO.setTypeGuid("first");
        pointTypeBindRespDTO.setTypeName("itemName");
        final PointItemBindRespDTO pointItemBindRespDTO = new PointItemBindRespDTO();
        pointItemBindRespDTO.setItemGuid("first");
        pointItemBindRespDTO.setItemName("itemName");
        pointItemBindRespDTO.setPinyin("pinyin");
        pointItemBindRespDTO.setIsBoundBySelf(false);
        pointItemBindRespDTO.setIsBoundByOthers(false);
        final PointSkuBindRespDTO pointSkuBindRespDTO = new PointSkuBindRespDTO();
        pointSkuBindRespDTO.setDeviceId("deviceId");
        pointSkuBindRespDTO.setDeviceName("deviceName");
        pointSkuBindRespDTO.setPointGuid("pointGuid");
        pointSkuBindRespDTO.setPointName("pointName");
        pointSkuBindRespDTO.setSkuGuid("eDishSkuCode");
        pointSkuBindRespDTO.setSkuName("eDishSkuName");
        pointSkuBindRespDTO.setSkuCode("skuCode");
        pointSkuBindRespDTO.setTimeout(0);
        pointSkuBindRespDTO.setMaxCopies(0);
        pointSkuBindRespDTO.setDisplayType(0);
        pointSkuBindRespDTO.setIsBoundBySelf(false);
        pointSkuBindRespDTO.setIsBoundByOthers(false);
        pointItemBindRespDTO.setSkus(Arrays.asList(pointSkuBindRespDTO));
        pointTypeBindRespDTO.setItemList(Arrays.asList(pointItemBindRespDTO));
        final List<PointTypeBindRespDTO> expectedResult = Arrays.asList(pointTypeBindRespDTO);
        when(mockDeviceConfigService.getDeviceNameMapOfStore("data")).thenReturn(new HashMap<>());

        // Configure PrdPointItemService.queryBoundItem(...).
        final PrdPointItemDO prdPointItemDO = new PrdPointItemDO();
        prdPointItemDO.setId(0L);
        prdPointItemDO.setGuid("9841eaf3-607f-46d1-9c33-e728c5e5e0fa");
        prdPointItemDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        prdPointItemDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        prdPointItemDO.setDeviceId("deviceId");
        prdPointItemDO.setPointGuid("pointGuid");
        prdPointItemDO.setSkuGuid("skuGuid");
        final List<PrdPointItemDO> prdPointItemDOS = Arrays.asList(prdPointItemDO);
        final PrdPointItemQueryReqDTO prdPointItemQueryReqDTO1 = new PrdPointItemQueryReqDTO();
        prdPointItemQueryReqDTO1.setStoreGuid("data");
        prdPointItemQueryReqDTO1.setDeviceId("deviceId");
        prdPointItemQueryReqDTO1.setPointGuid("pointGuid");
        prdPointItemQueryReqDTO1.setSearchKey("searchKey");
        when(mockPrdPointItemService.queryBoundItem(prdPointItemQueryReqDTO1)).thenReturn(prdPointItemDOS);

        // Configure ItemRpcService.kdsMapping(...).
        final MappingRespDTO mappingRespDTO = new MappingRespDTO();
        mappingRespDTO.setPinyin("pinyin");
        mappingRespDTO.seteDishSkuCode("eDishSkuCode");
        mappingRespDTO.setSkuCode("skuCode");
        mappingRespDTO.seteDishSkuName("eDishSkuName");
        mappingRespDTO.seteDishCode("eDishCode");
        mappingRespDTO.seteDishName("eDishName");
        mappingRespDTO.setCategoryName("categoryName");
        mappingRespDTO.setCategoryId("categoryId");
        final List<MappingRespDTO> mappingRespDTOS = Arrays.asList(mappingRespDTO);
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("data");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        itemSingleDTO.setSkuGuids(Arrays.asList("value"));
        when(mockItemRpcService.kdsMapping(itemSingleDTO)).thenReturn(mappingRespDTOS);

        when(mockItemConfigService.queryBatchByStoreGuid("data")).thenReturn(Collections.emptyList());

        // Run the test
        final List<PointTypeBindRespDTO> result = productionPointServiceImplUnderTest.queryBoundPointItem(
                prdPointItemQueryReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryAllPointItem() {
        // Setup
        final PrdPointItemQueryReqDTO prdPointItemQueryReqDTO = new PrdPointItemQueryReqDTO();
        prdPointItemQueryReqDTO.setStoreGuid("data");
        prdPointItemQueryReqDTO.setDeviceId("deviceId");
        prdPointItemQueryReqDTO.setPointGuid("pointGuid");
        prdPointItemQueryReqDTO.setSearchKey("searchKey");

        final PointTypeBindRespDTO pointTypeBindRespDTO = new PointTypeBindRespDTO();
        pointTypeBindRespDTO.setTypeGuid("first");
        pointTypeBindRespDTO.setTypeName("itemName");
        final PointItemBindRespDTO pointItemBindRespDTO = new PointItemBindRespDTO();
        pointItemBindRespDTO.setItemGuid("first");
        pointItemBindRespDTO.setItemName("itemName");
        pointItemBindRespDTO.setPinyin("pinyin");
        pointItemBindRespDTO.setIsBoundBySelf(false);
        pointItemBindRespDTO.setIsBoundByOthers(false);
        final PointSkuBindRespDTO pointSkuBindRespDTO = new PointSkuBindRespDTO();
        pointSkuBindRespDTO.setDeviceId("deviceId");
        pointSkuBindRespDTO.setDeviceName("deviceName");
        pointSkuBindRespDTO.setPointGuid("pointGuid");
        pointSkuBindRespDTO.setPointName("pointName");
        pointSkuBindRespDTO.setSkuGuid("eDishSkuCode");
        pointSkuBindRespDTO.setSkuName("eDishSkuName");
        pointSkuBindRespDTO.setSkuCode("skuCode");
        pointSkuBindRespDTO.setTimeout(0);
        pointSkuBindRespDTO.setMaxCopies(0);
        pointSkuBindRespDTO.setDisplayType(0);
        pointSkuBindRespDTO.setIsBoundBySelf(false);
        pointSkuBindRespDTO.setIsBoundByOthers(false);
        pointItemBindRespDTO.setSkus(Arrays.asList(pointSkuBindRespDTO));
        pointTypeBindRespDTO.setItemList(Arrays.asList(pointItemBindRespDTO));
        final List<PointTypeBindRespDTO> expectedResult = Arrays.asList(pointTypeBindRespDTO);
        when(mockDeviceConfigService.getDeviceNameMapOfStore("data")).thenReturn(new HashMap<>());

        // Configure PrdPointItemService.queryBoundItem(...).
        final PrdPointItemDO prdPointItemDO = new PrdPointItemDO();
        prdPointItemDO.setId(0L);
        prdPointItemDO.setGuid("9841eaf3-607f-46d1-9c33-e728c5e5e0fa");
        prdPointItemDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        prdPointItemDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        prdPointItemDO.setDeviceId("deviceId");
        prdPointItemDO.setPointGuid("pointGuid");
        prdPointItemDO.setSkuGuid("skuGuid");
        final List<PrdPointItemDO> prdPointItemDOS = Arrays.asList(prdPointItemDO);
        final PrdPointItemQueryReqDTO prdPointItemQueryReqDTO1 = new PrdPointItemQueryReqDTO();
        prdPointItemQueryReqDTO1.setStoreGuid("data");
        prdPointItemQueryReqDTO1.setDeviceId("deviceId");
        prdPointItemQueryReqDTO1.setPointGuid("pointGuid");
        prdPointItemQueryReqDTO1.setSearchKey("searchKey");
        when(mockPrdPointItemService.queryBoundItem(prdPointItemQueryReqDTO1)).thenReturn(prdPointItemDOS);

        // Configure ItemRpcService.kdsMapping(...).
        final MappingRespDTO mappingRespDTO = new MappingRespDTO();
        mappingRespDTO.setPinyin("pinyin");
        mappingRespDTO.seteDishSkuCode("eDishSkuCode");
        mappingRespDTO.setSkuCode("skuCode");
        mappingRespDTO.seteDishSkuName("eDishSkuName");
        mappingRespDTO.seteDishCode("eDishCode");
        mappingRespDTO.seteDishName("eDishName");
        mappingRespDTO.setCategoryName("categoryName");
        mappingRespDTO.setCategoryId("categoryId");
        final List<MappingRespDTO> mappingRespDTOS = Arrays.asList(mappingRespDTO);
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("data");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        itemSingleDTO.setSkuGuids(Arrays.asList("value"));
        when(mockItemRpcService.kdsMapping(itemSingleDTO)).thenReturn(mappingRespDTOS);

        // Configure ItemConfigService.queryBatchByStoreGuid(...).
        final ItemConfigDO itemConfigDO = new ItemConfigDO();
        itemConfigDO.setId(0L);
        itemConfigDO.setGuid("9841eaf3-607f-46d1-9c33-e728c5e5e0fa");
        itemConfigDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemConfigDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemConfigDO.setSkuGuid("skuGuid");
        itemConfigDO.setTimeout(0);
        itemConfigDO.setMaxCopies(0);
        itemConfigDO.setDisplayType(0);
        final List<ItemConfigDO> itemConfigDOS = Arrays.asList(itemConfigDO);
        when(mockItemConfigService.queryBatchByStoreGuid("data")).thenReturn(itemConfigDOS);

        // Run the test
        final List<PointTypeBindRespDTO> result = productionPointServiceImplUnderTest.queryAllPointItem(
                prdPointItemQueryReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryAllPointItem_PrdPointItemServiceReturnsNoItems() {
        // Setup
        final PrdPointItemQueryReqDTO prdPointItemQueryReqDTO = new PrdPointItemQueryReqDTO();
        prdPointItemQueryReqDTO.setStoreGuid("data");
        prdPointItemQueryReqDTO.setDeviceId("deviceId");
        prdPointItemQueryReqDTO.setPointGuid("pointGuid");
        prdPointItemQueryReqDTO.setSearchKey("searchKey");

        final PointTypeBindRespDTO pointTypeBindRespDTO = new PointTypeBindRespDTO();
        pointTypeBindRespDTO.setTypeGuid("first");
        pointTypeBindRespDTO.setTypeName("itemName");
        final PointItemBindRespDTO pointItemBindRespDTO = new PointItemBindRespDTO();
        pointItemBindRespDTO.setItemGuid("first");
        pointItemBindRespDTO.setItemName("itemName");
        pointItemBindRespDTO.setPinyin("pinyin");
        pointItemBindRespDTO.setIsBoundBySelf(false);
        pointItemBindRespDTO.setIsBoundByOthers(false);
        final PointSkuBindRespDTO pointSkuBindRespDTO = new PointSkuBindRespDTO();
        pointSkuBindRespDTO.setDeviceId("deviceId");
        pointSkuBindRespDTO.setDeviceName("deviceName");
        pointSkuBindRespDTO.setPointGuid("pointGuid");
        pointSkuBindRespDTO.setPointName("pointName");
        pointSkuBindRespDTO.setSkuGuid("eDishSkuCode");
        pointSkuBindRespDTO.setSkuName("eDishSkuName");
        pointSkuBindRespDTO.setSkuCode("skuCode");
        pointSkuBindRespDTO.setTimeout(0);
        pointSkuBindRespDTO.setMaxCopies(0);
        pointSkuBindRespDTO.setDisplayType(0);
        pointSkuBindRespDTO.setIsBoundBySelf(false);
        pointSkuBindRespDTO.setIsBoundByOthers(false);
        pointItemBindRespDTO.setSkus(Arrays.asList(pointSkuBindRespDTO));
        pointTypeBindRespDTO.setItemList(Arrays.asList(pointItemBindRespDTO));
        final List<PointTypeBindRespDTO> expectedResult = Arrays.asList(pointTypeBindRespDTO);
        when(mockDeviceConfigService.getDeviceNameMapOfStore("data")).thenReturn(new HashMap<>());

        // Configure PrdPointItemService.queryBoundItem(...).
        final PrdPointItemQueryReqDTO prdPointItemQueryReqDTO1 = new PrdPointItemQueryReqDTO();
        prdPointItemQueryReqDTO1.setStoreGuid("data");
        prdPointItemQueryReqDTO1.setDeviceId("deviceId");
        prdPointItemQueryReqDTO1.setPointGuid("pointGuid");
        prdPointItemQueryReqDTO1.setSearchKey("searchKey");
        when(mockPrdPointItemService.queryBoundItem(prdPointItemQueryReqDTO1)).thenReturn(Collections.emptyList());

        // Configure ItemRpcService.kdsMapping(...).
        final MappingRespDTO mappingRespDTO = new MappingRespDTO();
        mappingRespDTO.setPinyin("pinyin");
        mappingRespDTO.seteDishSkuCode("eDishSkuCode");
        mappingRespDTO.setSkuCode("skuCode");
        mappingRespDTO.seteDishSkuName("eDishSkuName");
        mappingRespDTO.seteDishCode("eDishCode");
        mappingRespDTO.seteDishName("eDishName");
        mappingRespDTO.setCategoryName("categoryName");
        mappingRespDTO.setCategoryId("categoryId");
        final List<MappingRespDTO> mappingRespDTOS = Arrays.asList(mappingRespDTO);
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("data");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        itemSingleDTO.setSkuGuids(Arrays.asList("value"));
        when(mockItemRpcService.kdsMapping(itemSingleDTO)).thenReturn(mappingRespDTOS);

        // Configure ItemConfigService.queryBatchByStoreGuid(...).
        final ItemConfigDO itemConfigDO = new ItemConfigDO();
        itemConfigDO.setId(0L);
        itemConfigDO.setGuid("9841eaf3-607f-46d1-9c33-e728c5e5e0fa");
        itemConfigDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemConfigDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemConfigDO.setSkuGuid("skuGuid");
        itemConfigDO.setTimeout(0);
        itemConfigDO.setMaxCopies(0);
        itemConfigDO.setDisplayType(0);
        final List<ItemConfigDO> itemConfigDOS = Arrays.asList(itemConfigDO);
        when(mockItemConfigService.queryBatchByStoreGuid("data")).thenReturn(itemConfigDOS);

        // Run the test
        final List<PointTypeBindRespDTO> result = productionPointServiceImplUnderTest.queryAllPointItem(
                prdPointItemQueryReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryAllPointItem_ItemRpcServiceReturnsNoItems() {
        // Setup
        final PrdPointItemQueryReqDTO prdPointItemQueryReqDTO = new PrdPointItemQueryReqDTO();
        prdPointItemQueryReqDTO.setStoreGuid("data");
        prdPointItemQueryReqDTO.setDeviceId("deviceId");
        prdPointItemQueryReqDTO.setPointGuid("pointGuid");
        prdPointItemQueryReqDTO.setSearchKey("searchKey");

        when(mockDeviceConfigService.getDeviceNameMapOfStore("data")).thenReturn(new HashMap<>());

        // Configure PrdPointItemService.queryBoundItem(...).
        final PrdPointItemDO prdPointItemDO = new PrdPointItemDO();
        prdPointItemDO.setId(0L);
        prdPointItemDO.setGuid("9841eaf3-607f-46d1-9c33-e728c5e5e0fa");
        prdPointItemDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        prdPointItemDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        prdPointItemDO.setDeviceId("deviceId");
        prdPointItemDO.setPointGuid("pointGuid");
        prdPointItemDO.setSkuGuid("skuGuid");
        final List<PrdPointItemDO> prdPointItemDOS = Arrays.asList(prdPointItemDO);
        final PrdPointItemQueryReqDTO prdPointItemQueryReqDTO1 = new PrdPointItemQueryReqDTO();
        prdPointItemQueryReqDTO1.setStoreGuid("data");
        prdPointItemQueryReqDTO1.setDeviceId("deviceId");
        prdPointItemQueryReqDTO1.setPointGuid("pointGuid");
        prdPointItemQueryReqDTO1.setSearchKey("searchKey");
        when(mockPrdPointItemService.queryBoundItem(prdPointItemQueryReqDTO1)).thenReturn(prdPointItemDOS);

        // Configure ItemRpcService.kdsMapping(...).
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("data");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        itemSingleDTO.setSkuGuids(Arrays.asList("value"));
        when(mockItemRpcService.kdsMapping(itemSingleDTO)).thenReturn(Collections.emptyList());

        // Configure ItemConfigService.queryBatchByStoreGuid(...).
        final ItemConfigDO itemConfigDO = new ItemConfigDO();
        itemConfigDO.setId(0L);
        itemConfigDO.setGuid("9841eaf3-607f-46d1-9c33-e728c5e5e0fa");
        itemConfigDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemConfigDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        itemConfigDO.setSkuGuid("skuGuid");
        itemConfigDO.setTimeout(0);
        itemConfigDO.setMaxCopies(0);
        itemConfigDO.setDisplayType(0);
        final List<ItemConfigDO> itemConfigDOS = Arrays.asList(itemConfigDO);
        when(mockItemConfigService.queryBatchByStoreGuid("data")).thenReturn(itemConfigDOS);

        // Run the test
        final List<PointTypeBindRespDTO> result = productionPointServiceImplUnderTest.queryAllPointItem(
                prdPointItemQueryReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testQueryAllPointItem_ItemConfigServiceReturnsNoItems() {
        // Setup
        final PrdPointItemQueryReqDTO prdPointItemQueryReqDTO = new PrdPointItemQueryReqDTO();
        prdPointItemQueryReqDTO.setStoreGuid("data");
        prdPointItemQueryReqDTO.setDeviceId("deviceId");
        prdPointItemQueryReqDTO.setPointGuid("pointGuid");
        prdPointItemQueryReqDTO.setSearchKey("searchKey");

        final PointTypeBindRespDTO pointTypeBindRespDTO = new PointTypeBindRespDTO();
        pointTypeBindRespDTO.setTypeGuid("first");
        pointTypeBindRespDTO.setTypeName("itemName");
        final PointItemBindRespDTO pointItemBindRespDTO = new PointItemBindRespDTO();
        pointItemBindRespDTO.setItemGuid("first");
        pointItemBindRespDTO.setItemName("itemName");
        pointItemBindRespDTO.setPinyin("pinyin");
        pointItemBindRespDTO.setIsBoundBySelf(false);
        pointItemBindRespDTO.setIsBoundByOthers(false);
        final PointSkuBindRespDTO pointSkuBindRespDTO = new PointSkuBindRespDTO();
        pointSkuBindRespDTO.setDeviceId("deviceId");
        pointSkuBindRespDTO.setDeviceName("deviceName");
        pointSkuBindRespDTO.setPointGuid("pointGuid");
        pointSkuBindRespDTO.setPointName("pointName");
        pointSkuBindRespDTO.setSkuGuid("eDishSkuCode");
        pointSkuBindRespDTO.setSkuName("eDishSkuName");
        pointSkuBindRespDTO.setSkuCode("skuCode");
        pointSkuBindRespDTO.setTimeout(0);
        pointSkuBindRespDTO.setMaxCopies(0);
        pointSkuBindRespDTO.setDisplayType(0);
        pointSkuBindRespDTO.setIsBoundBySelf(false);
        pointSkuBindRespDTO.setIsBoundByOthers(false);
        pointItemBindRespDTO.setSkus(Arrays.asList(pointSkuBindRespDTO));
        pointTypeBindRespDTO.setItemList(Arrays.asList(pointItemBindRespDTO));
        final List<PointTypeBindRespDTO> expectedResult = Arrays.asList(pointTypeBindRespDTO);
        when(mockDeviceConfigService.getDeviceNameMapOfStore("data")).thenReturn(new HashMap<>());

        // Configure PrdPointItemService.queryBoundItem(...).
        final PrdPointItemDO prdPointItemDO = new PrdPointItemDO();
        prdPointItemDO.setId(0L);
        prdPointItemDO.setGuid("9841eaf3-607f-46d1-9c33-e728c5e5e0fa");
        prdPointItemDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        prdPointItemDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        prdPointItemDO.setDeviceId("deviceId");
        prdPointItemDO.setPointGuid("pointGuid");
        prdPointItemDO.setSkuGuid("skuGuid");
        final List<PrdPointItemDO> prdPointItemDOS = Arrays.asList(prdPointItemDO);
        final PrdPointItemQueryReqDTO prdPointItemQueryReqDTO1 = new PrdPointItemQueryReqDTO();
        prdPointItemQueryReqDTO1.setStoreGuid("data");
        prdPointItemQueryReqDTO1.setDeviceId("deviceId");
        prdPointItemQueryReqDTO1.setPointGuid("pointGuid");
        prdPointItemQueryReqDTO1.setSearchKey("searchKey");
        when(mockPrdPointItemService.queryBoundItem(prdPointItemQueryReqDTO1)).thenReturn(prdPointItemDOS);

        // Configure ItemRpcService.kdsMapping(...).
        final MappingRespDTO mappingRespDTO = new MappingRespDTO();
        mappingRespDTO.setPinyin("pinyin");
        mappingRespDTO.seteDishSkuCode("eDishSkuCode");
        mappingRespDTO.setSkuCode("skuCode");
        mappingRespDTO.seteDishSkuName("eDishSkuName");
        mappingRespDTO.seteDishCode("eDishCode");
        mappingRespDTO.seteDishName("eDishName");
        mappingRespDTO.setCategoryName("categoryName");
        mappingRespDTO.setCategoryId("categoryId");
        final List<MappingRespDTO> mappingRespDTOS = Arrays.asList(mappingRespDTO);
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("data");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        itemSingleDTO.setSkuGuids(Arrays.asList("value"));
        when(mockItemRpcService.kdsMapping(itemSingleDTO)).thenReturn(mappingRespDTOS);

        when(mockItemConfigService.queryBatchByStoreGuid("data")).thenReturn(Collections.emptyList());

        // Run the test
        final List<PointTypeBindRespDTO> result = productionPointServiceImplUnderTest.queryAllPointItem(
                prdPointItemQueryReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testUpdateBasicConfig() {
        // Setup
        final DeviceBasicConfUpdateReqDTO deviceBasicConfUpdateReqDTO = new DeviceBasicConfUpdateReqDTO();
        deviceBasicConfUpdateReqDTO.setStoreGuid("storeGuid");
        deviceBasicConfUpdateReqDTO.setDeviceId("deviceId");
        deviceBasicConfUpdateReqDTO.setDisplayMode(0);

        // Run the test
        productionPointServiceImplUnderTest.updateBasicConfig(deviceBasicConfUpdateReqDTO);

        // Verify the results
        verify(mockDeviceConfigService).updateBasic("storeGuid", "deviceId", 0, 1);
    }

    @Test
    public void testUpdateAdvancedConfig() {
        // Setup
        final DeviceAdvanConfUpdateReqDTO deviceAdvanConfUpdateReqDTO = new DeviceAdvanConfUpdateReqDTO();
        deviceAdvanConfUpdateReqDTO.setStoreGuid("storeGuid");
        deviceAdvanConfUpdateReqDTO.setDeviceId("deviceId");
        final DevicePrdConfDTO devicePrdConfDTO = new DevicePrdConfDTO();
        devicePrdConfDTO.setDisplayType(0);
        devicePrdConfDTO.setIsItemSort(false);
        deviceAdvanConfUpdateReqDTO.setDevicePrdConfDTO(devicePrdConfDTO);

        // Run the test
        productionPointServiceImplUnderTest.updateAdvancedConfig(deviceAdvanConfUpdateReqDTO);

        // Verify the results
        // Confirm DeviceConfigService.updatePrdAdvanced(...).
        final DevicePrdConfDTO devicePrdConfDTO1 = new DevicePrdConfDTO();
        devicePrdConfDTO1.setDisplayType(0);
        devicePrdConfDTO1.setIsItemSort(false);
        devicePrdConfDTO1.setIsItemTimeout(false);
        devicePrdConfDTO1.setIsShowHangedItem(false);
        devicePrdConfDTO1.setIsProduceHangedItem(false);
        verify(mockDeviceConfigService).updatePrdAdvanced("storeGuid", "deviceId", devicePrdConfDTO1);
    }

    @Test
    public void testFillEmptyPoint() {
        // Setup
        final PointItemReadDO pointItemReadDO = new PointItemReadDO();
        pointItemReadDO.setId(0L);
        pointItemReadDO.setGuid("9841eaf3-607f-46d1-9c33-e728c5e5e0fa");
        pointItemReadDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        pointItemReadDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        pointItemReadDO.setStoreGuid("storeGuid");
        pointItemReadDO.setDeviceId("deviceId");
        pointItemReadDO.setPointName("name");
        pointItemReadDO.setItemCount(0);
        final KitchenItemReadDO kitchenItemReadDO = new KitchenItemReadDO();
        kitchenItemReadDO.setId(0L);
        kitchenItemReadDO.setGuid("9841eaf3-607f-46d1-9c33-e728c5e5e0fa");
        kitchenItemReadDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kitchenItemReadDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        pointItemReadDO.setItems(Arrays.asList(kitchenItemReadDO));
        final List<PointItemReadDO> pointItemReadInSql = Arrays.asList(pointItemReadDO);
        final PointItemReadDO pointItemReadDO1 = new PointItemReadDO();
        pointItemReadDO1.setId(0L);
        pointItemReadDO1.setGuid("9841eaf3-607f-46d1-9c33-e728c5e5e0fa");
        pointItemReadDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        pointItemReadDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        pointItemReadDO1.setStoreGuid("storeGuid");
        pointItemReadDO1.setDeviceId("deviceId");
        pointItemReadDO1.setPointName("name");
        pointItemReadDO1.setItemCount(0);
        final KitchenItemReadDO kitchenItemReadDO1 = new KitchenItemReadDO();
        kitchenItemReadDO1.setId(0L);
        kitchenItemReadDO1.setGuid("9841eaf3-607f-46d1-9c33-e728c5e5e0fa");
        kitchenItemReadDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kitchenItemReadDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        pointItemReadDO1.setItems(Arrays.asList(kitchenItemReadDO1));
        final List<PointItemReadDO> expectedResult = Arrays.asList(pointItemReadDO1);

        // Run the test
        final List<PointItemReadDO> result = productionPointServiceImplUnderTest.fillEmptyPoint("storeGuid", "deviceId",
                pointItemReadInSql);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
