package com.holderzone.saas.store.kds.service.impl;

import com.holderzone.saas.store.dto.kds.req.PrdDstItemBindDTO;
import com.holderzone.saas.store.dto.kds.req.PrdPointDelReqDTO;
import com.holderzone.saas.store.dto.kds.req.PrdPointItemBindReqDTO;
import com.holderzone.saas.store.dto.kds.req.PrdPointItemQueryReqDTO;
import com.holderzone.saas.store.dto.kds.resp.PrdPointItemDTO;
import com.holderzone.saas.store.kds.entity.domain.PrdPointItemDO;
import com.holderzone.saas.store.kds.mapper.PrdPointItemMapper;
import com.holderzone.saas.store.kds.mapstruct.DeviceConfigMapstruct;
import com.holderzone.saas.store.kds.service.DeviceBindItemGroupService;
import com.holderzone.saas.store.kds.service.DistributedIdService;
import com.holderzone.saas.store.kds.service.rpc.ItemRpcService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PrdPointItemServiceImplTest {

    @Mock
    private DeviceConfigMapstruct mockDeviceConfigMapstruct;
    @Mock
    private DistributedIdService mockDistributedIdService;
    @Mock
    private PrdPointItemMapper mockPrdPointItemMapper;
    @Mock
    private ItemRpcService mockItemRpcService;

    @Mock
    private DeviceBindItemGroupService mockDeviceBindItemGroupService;

    private PrdPointItemServiceImpl prdPointItemServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        prdPointItemServiceImplUnderTest = new PrdPointItemServiceImpl(mockDeviceConfigMapstruct,
                mockDistributedIdService, mockPrdPointItemMapper, mockItemRpcService, mockDeviceBindItemGroupService);
    }

    @Test
    public void testBindItem() {
        // Setup
        final PrdPointItemBindReqDTO prdPointItemBindReqDTO = new PrdPointItemBindReqDTO();
        prdPointItemBindReqDTO.setStoreGuid("storeGuid");
        prdPointItemBindReqDTO.setDeviceId("deviceId");
        prdPointItemBindReqDTO.setPointGuid("pointGuid");
        final PrdDstItemBindDTO prdDstItemBindDTO = new PrdDstItemBindDTO();
        prdDstItemBindDTO.setSkuGuid("skuGuid");
        prdPointItemBindReqDTO.setBindingItems(Arrays.asList(prdDstItemBindDTO));

        when(mockDistributedIdService.nextBatchPointItemGuid(0L)).thenReturn(Arrays.asList("value"));

        // Configure DeviceConfigMapstruct.fromItemConfigUpdateReq(...).
        final PrdPointItemDO prdPointItemDO = new PrdPointItemDO();
        prdPointItemDO.setGuid("58fa3199-f3d0-414b-859c-61de69996beb");
        prdPointItemDO.setStoreGuid("storeGuid");
        prdPointItemDO.setDeviceId("deviceId");
        prdPointItemDO.setPointGuid("pointGuid");
        prdPointItemDO.setItemGuid("itemGuid");
        prdPointItemDO.setSkuGuid("skuGuid");
        prdPointItemDO.setSkuCode("skuCode");
        final PrdDstItemBindDTO prdDstItemBindDTO1 = new PrdDstItemBindDTO();
        prdDstItemBindDTO1.setItemGuid("itemGuid");
        prdDstItemBindDTO1.setSkuGuid("skuGuid");
        final PrdPointItemBindReqDTO prdPointItemBindReqDTO1 = new PrdPointItemBindReqDTO();
        prdPointItemBindReqDTO1.setStoreGuid("storeGuid");
        prdPointItemBindReqDTO1.setDeviceId("deviceId");
        prdPointItemBindReqDTO1.setPointGuid("pointGuid");
        final PrdDstItemBindDTO prdDstItemBindDTO2 = new PrdDstItemBindDTO();
        prdDstItemBindDTO2.setSkuGuid("skuGuid");
        prdPointItemBindReqDTO1.setBindingItems(Arrays.asList(prdDstItemBindDTO2));
        when(mockDeviceConfigMapstruct.fromItemConfigUpdateReq(prdDstItemBindDTO1, prdPointItemBindReqDTO1))
                .thenReturn(prdPointItemDO);

        // Run the test
        prdPointItemServiceImplUnderTest.bindItem(prdPointItemBindReqDTO);

        // Verify the results
    }

    @Test
    public void testBindItem_DistributedIdServiceReturnsNoItems() {
        // Setup
        final PrdPointItemBindReqDTO prdPointItemBindReqDTO = new PrdPointItemBindReqDTO();
        prdPointItemBindReqDTO.setStoreGuid("storeGuid");
        prdPointItemBindReqDTO.setDeviceId("deviceId");
        prdPointItemBindReqDTO.setPointGuid("pointGuid");
        final PrdDstItemBindDTO prdDstItemBindDTO = new PrdDstItemBindDTO();
        prdDstItemBindDTO.setSkuGuid("skuGuid");
        prdPointItemBindReqDTO.setBindingItems(Arrays.asList(prdDstItemBindDTO));

        when(mockDistributedIdService.nextBatchPointItemGuid(0L)).thenReturn(Collections.emptyList());

        // Configure DeviceConfigMapstruct.fromItemConfigUpdateReq(...).
        final PrdPointItemDO prdPointItemDO = new PrdPointItemDO();
        prdPointItemDO.setGuid("58fa3199-f3d0-414b-859c-61de69996beb");
        prdPointItemDO.setStoreGuid("storeGuid");
        prdPointItemDO.setDeviceId("deviceId");
        prdPointItemDO.setPointGuid("pointGuid");
        prdPointItemDO.setItemGuid("itemGuid");
        prdPointItemDO.setSkuGuid("skuGuid");
        prdPointItemDO.setSkuCode("skuCode");
        final PrdDstItemBindDTO prdDstItemBindDTO1 = new PrdDstItemBindDTO();
        prdDstItemBindDTO1.setItemGuid("itemGuid");
        prdDstItemBindDTO1.setSkuGuid("skuGuid");
        final PrdPointItemBindReqDTO prdPointItemBindReqDTO1 = new PrdPointItemBindReqDTO();
        prdPointItemBindReqDTO1.setStoreGuid("storeGuid");
        prdPointItemBindReqDTO1.setDeviceId("deviceId");
        prdPointItemBindReqDTO1.setPointGuid("pointGuid");
        final PrdDstItemBindDTO prdDstItemBindDTO2 = new PrdDstItemBindDTO();
        prdDstItemBindDTO2.setSkuGuid("skuGuid");
        prdPointItemBindReqDTO1.setBindingItems(Arrays.asList(prdDstItemBindDTO2));
        when(mockDeviceConfigMapstruct.fromItemConfigUpdateReq(prdDstItemBindDTO1, prdPointItemBindReqDTO1))
                .thenReturn(prdPointItemDO);

        // Run the test
        prdPointItemServiceImplUnderTest.bindItem(prdPointItemBindReqDTO);

        // Verify the results
    }

    @Test
    public void testUnbindItem1() {
        // Setup
        final PrdPointItemBindReqDTO prdPointItemBindReqDTO = new PrdPointItemBindReqDTO();
        prdPointItemBindReqDTO.setStoreGuid("storeGuid");
        prdPointItemBindReqDTO.setDeviceId("deviceId");
        prdPointItemBindReqDTO.setPointGuid("pointGuid");
        final PrdDstItemBindDTO prdDstItemBindDTO = new PrdDstItemBindDTO();
        prdDstItemBindDTO.setSkuGuid("skuGuid");
        prdPointItemBindReqDTO.setBindingItems(Arrays.asList(prdDstItemBindDTO));

        when(mockItemRpcService.listSkuGuid(Arrays.asList("value"))).thenReturn(Arrays.asList("value"));

        // Run the test
        prdPointItemServiceImplUnderTest.unbindItem(prdPointItemBindReqDTO);

        // Verify the results
    }

    @Test
    public void testUnbindItem1_ItemRpcServiceReturnsNoItems() {
        // Setup
        final PrdPointItemBindReqDTO prdPointItemBindReqDTO = new PrdPointItemBindReqDTO();
        prdPointItemBindReqDTO.setStoreGuid("storeGuid");
        prdPointItemBindReqDTO.setDeviceId("deviceId");
        prdPointItemBindReqDTO.setPointGuid("pointGuid");
        final PrdDstItemBindDTO prdDstItemBindDTO = new PrdDstItemBindDTO();
        prdDstItemBindDTO.setSkuGuid("skuGuid");
        prdPointItemBindReqDTO.setBindingItems(Arrays.asList(prdDstItemBindDTO));

        when(mockItemRpcService.listSkuGuid(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        prdPointItemServiceImplUnderTest.unbindItem(prdPointItemBindReqDTO);

        // Verify the results
    }

    @Test
    public void testUnbindItem2() {
        // Setup
        final PrdPointDelReqDTO prdPointDelReqDTO = new PrdPointDelReqDTO();
        prdPointDelReqDTO.setStoreGuid("storeGuid");
        prdPointDelReqDTO.setDeviceId("deviceId");
        prdPointDelReqDTO.setPointGuid("pointGuid");

        // Run the test
        prdPointItemServiceImplUnderTest.unbindItem(prdPointDelReqDTO);

        // Verify the results
    }

    @Test
    public void testReInitialize() {
        // Setup
        // Run the test
        prdPointItemServiceImplUnderTest.reInitialize("storeGuid", "deviceId");

        // Verify the results
    }

    @Test
    public void testQueryBoundItem() {
        // Setup
        final PrdPointItemQueryReqDTO prdPointItemQueryReqDTO = new PrdPointItemQueryReqDTO();
        prdPointItemQueryReqDTO.setStoreGuid("storeGuid");
        prdPointItemQueryReqDTO.setDeviceId("deviceId");
        prdPointItemQueryReqDTO.setPointGuid("pointGuid");
        prdPointItemQueryReqDTO.setSearchKey("searchKey");

        final PrdPointItemDO prdPointItemDO = new PrdPointItemDO();
        prdPointItemDO.setGuid("58fa3199-f3d0-414b-859c-61de69996beb");
        prdPointItemDO.setStoreGuid("storeGuid");
        prdPointItemDO.setDeviceId("deviceId");
        prdPointItemDO.setPointGuid("pointGuid");
        prdPointItemDO.setItemGuid("itemGuid");
        prdPointItemDO.setSkuGuid("skuGuid");
        prdPointItemDO.setSkuCode("skuCode");
        final List<PrdPointItemDO> expectedResult = Arrays.asList(prdPointItemDO);

        // Run the test
        final List<PrdPointItemDO> result = prdPointItemServiceImplUnderTest.queryBoundItem(prdPointItemQueryReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryBoundItemBySku() {
        // Setup
        final PrdPointItemQueryReqDTO prdPointItemQueryReqDTO = new PrdPointItemQueryReqDTO();
        prdPointItemQueryReqDTO.setStoreGuid("storeGuid");
        prdPointItemQueryReqDTO.setDeviceId("deviceId");
        prdPointItemQueryReqDTO.setPointGuid("pointGuid");
        prdPointItemQueryReqDTO.setSearchKey("searchKey");

        final PrdPointItemDTO prdPointItemDTO = new PrdPointItemDTO();
        prdPointItemDTO.setStoreGuid("storeGuid");
        prdPointItemDTO.setDeviceId("deviceId");
        prdPointItemDTO.setPointGuid("pointGuid");
        prdPointItemDTO.setItemGuid("itemGuid");
        prdPointItemDTO.setSkuGuid("skuGuid");
        prdPointItemDTO.setSkuCode("skuCode");
        final List<PrdPointItemDTO> expectedResult = Arrays.asList(prdPointItemDTO);

        // Run the test
        final List<PrdPointItemDTO> result = prdPointItemServiceImplUnderTest.queryBoundItemBySku(
                prdPointItemQueryReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryPrdPointByItem() {
        // Setup
        final Map<String, PrdPointItemDO> expectedResult = new HashMap<>();

        // Run the test
        final Map<String, PrdPointItemDO> result = prdPointItemServiceImplUnderTest.queryPrdPointByItem(
                Arrays.asList("value"), "storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryAllBoundItem() {
        // Setup
        final PrdPointItemDTO prdPointItemDTO = new PrdPointItemDTO();
        prdPointItemDTO.setStoreGuid("storeGuid");
        prdPointItemDTO.setDeviceId("deviceId");
        prdPointItemDTO.setPointGuid("pointGuid");
        prdPointItemDTO.setItemGuid("itemGuid");
        prdPointItemDTO.setSkuGuid("skuGuid");
        prdPointItemDTO.setSkuCode("skuCode");
        final List<PrdPointItemDTO> expectedResult = Arrays.asList(prdPointItemDTO);

        // Configure PrdPointItemMapper.queryAll(...).
        final PrdPointItemDTO prdPointItemDTO1 = new PrdPointItemDTO();
        prdPointItemDTO1.setStoreGuid("storeGuid");
        prdPointItemDTO1.setDeviceId("deviceId");
        prdPointItemDTO1.setPointGuid("pointGuid");
        prdPointItemDTO1.setItemGuid("itemGuid");
        prdPointItemDTO1.setSkuGuid("skuGuid");
        prdPointItemDTO1.setSkuCode("skuCode");
        final List<PrdPointItemDTO> prdPointItemDTOS = Arrays.asList(prdPointItemDTO1);
        when(mockPrdPointItemMapper.queryAll()).thenReturn(prdPointItemDTOS);

        // Run the test
        final List<PrdPointItemDTO> result = prdPointItemServiceImplUnderTest.queryAllBoundItem();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryAllBoundItem_PrdPointItemMapperReturnsNoItems() {
        // Setup
        when(mockPrdPointItemMapper.queryAll()).thenReturn(Collections.emptyList());

        // Run the test
        final List<PrdPointItemDTO> result = prdPointItemServiceImplUnderTest.queryAllBoundItem();

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }
}
