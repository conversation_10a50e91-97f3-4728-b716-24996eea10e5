package com.holderzone.saas.store.kds.service.impl;

import com.holderzone.saas.store.dto.kds.req.ItemConfBatchUpdateReqDTO;
import com.holderzone.saas.store.dto.kds.req.ItemConfigUpdateReqDTO;
import com.holderzone.saas.store.kds.entity.domain.ItemConfigDO;
import com.holderzone.saas.store.kds.mapstruct.DeviceConfigMapstruct;
import com.holderzone.saas.store.kds.service.DistributedIdService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ItemConfigServiceImplTest {

    @Mock
    private DistributedIdService mockDistributedIdService;
    @Mock
    private DeviceConfigMapstruct mockDeviceConfigMapstruct;

    private ItemConfigServiceImpl itemConfigServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        itemConfigServiceImplUnderTest = new ItemConfigServiceImpl(mockDistributedIdService, mockDeviceConfigMapstruct);
    }

    @Test
    public void testInsertOrUpdateBatch() {
        // Setup
        final ItemConfBatchUpdateReqDTO itemConfBatchUpdateReqDTO = new ItemConfBatchUpdateReqDTO();
        itemConfBatchUpdateReqDTO.setStoreGuid("storeGuid");
        final ItemConfigUpdateReqDTO itemConfigUpdateReqDTO = new ItemConfigUpdateReqDTO();
        itemConfigUpdateReqDTO.setSkuGuid("skuGuid");
        itemConfigUpdateReqDTO.setTimeout(0);
        itemConfigUpdateReqDTO.setMaxCopies(0);
        itemConfigUpdateReqDTO.setDisplayType(0);
        itemConfBatchUpdateReqDTO.setItemConfigs(Arrays.asList(itemConfigUpdateReqDTO));

        when(mockDistributedIdService.nextBatchPointItemGuid(0L)).thenReturn(Arrays.asList("value"));

        // Configure DeviceConfigMapstruct.fromItemConfigUpdateReq(...).
        final ItemConfigDO itemConfigDO = new ItemConfigDO();
        itemConfigDO.setGuid("e354811e-099a-412e-80c1-5d2c8c024e05");
        itemConfigDO.setStoreGuid("storeGuid");
        itemConfigDO.setSkuGuid("skuGuid");
        itemConfigDO.setTimeout(0);
        itemConfigDO.setMaxCopies(0);
        itemConfigDO.setDisplayType(0);
        final ItemConfigUpdateReqDTO prdPointItemConfigUpdateReqDTO = new ItemConfigUpdateReqDTO();
        prdPointItemConfigUpdateReqDTO.setItemGuid("itemGuid");
        prdPointItemConfigUpdateReqDTO.setSkuGuid("skuGuid");
        prdPointItemConfigUpdateReqDTO.setTimeout(0);
        prdPointItemConfigUpdateReqDTO.setMaxCopies(0);
        prdPointItemConfigUpdateReqDTO.setDisplayType(0);
        when(mockDeviceConfigMapstruct.fromItemConfigUpdateReq(prdPointItemConfigUpdateReqDTO))
                .thenReturn(itemConfigDO);

        // Run the test
        itemConfigServiceImplUnderTest.insertOrUpdateBatch(itemConfBatchUpdateReqDTO);

        // Verify the results
    }

    @Test
    public void testInsertOrUpdateBatch_DistributedIdServiceReturnsNoItems() {
        // Setup
        final ItemConfBatchUpdateReqDTO itemConfBatchUpdateReqDTO = new ItemConfBatchUpdateReqDTO();
        itemConfBatchUpdateReqDTO.setStoreGuid("storeGuid");
        final ItemConfigUpdateReqDTO itemConfigUpdateReqDTO = new ItemConfigUpdateReqDTO();
        itemConfigUpdateReqDTO.setSkuGuid("skuGuid");
        itemConfigUpdateReqDTO.setTimeout(0);
        itemConfigUpdateReqDTO.setMaxCopies(0);
        itemConfigUpdateReqDTO.setDisplayType(0);
        itemConfBatchUpdateReqDTO.setItemConfigs(Arrays.asList(itemConfigUpdateReqDTO));

        when(mockDistributedIdService.nextBatchPointItemGuid(0L)).thenReturn(Collections.emptyList());

        // Configure DeviceConfigMapstruct.fromItemConfigUpdateReq(...).
        final ItemConfigDO itemConfigDO = new ItemConfigDO();
        itemConfigDO.setGuid("e354811e-099a-412e-80c1-5d2c8c024e05");
        itemConfigDO.setStoreGuid("storeGuid");
        itemConfigDO.setSkuGuid("skuGuid");
        itemConfigDO.setTimeout(0);
        itemConfigDO.setMaxCopies(0);
        itemConfigDO.setDisplayType(0);
        final ItemConfigUpdateReqDTO prdPointItemConfigUpdateReqDTO = new ItemConfigUpdateReqDTO();
        prdPointItemConfigUpdateReqDTO.setItemGuid("itemGuid");
        prdPointItemConfigUpdateReqDTO.setSkuGuid("skuGuid");
        prdPointItemConfigUpdateReqDTO.setTimeout(0);
        prdPointItemConfigUpdateReqDTO.setMaxCopies(0);
        prdPointItemConfigUpdateReqDTO.setDisplayType(0);
        when(mockDeviceConfigMapstruct.fromItemConfigUpdateReq(prdPointItemConfigUpdateReqDTO))
                .thenReturn(itemConfigDO);

        // Run the test
        itemConfigServiceImplUnderTest.insertOrUpdateBatch(itemConfBatchUpdateReqDTO);

        // Verify the results
    }

    @Test
    public void testQueryBatchBySkuGuid() {
        // Setup
        final ItemConfigDO itemConfigDO = new ItemConfigDO();
        itemConfigDO.setGuid("e354811e-099a-412e-80c1-5d2c8c024e05");
        itemConfigDO.setStoreGuid("storeGuid");
        itemConfigDO.setSkuGuid("skuGuid");
        itemConfigDO.setTimeout(0);
        itemConfigDO.setMaxCopies(0);
        itemConfigDO.setDisplayType(0);
        final List<ItemConfigDO> expectedResult = Arrays.asList(itemConfigDO);

        // Run the test
        final List<ItemConfigDO> result = itemConfigServiceImplUnderTest.queryBatchBySkuGuid(Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryBatchByStoreGuid() {
        // Setup
        final ItemConfigDO itemConfigDO = new ItemConfigDO();
        itemConfigDO.setGuid("e354811e-099a-412e-80c1-5d2c8c024e05");
        itemConfigDO.setStoreGuid("storeGuid");
        itemConfigDO.setSkuGuid("skuGuid");
        itemConfigDO.setTimeout(0);
        itemConfigDO.setMaxCopies(0);
        itemConfigDO.setDisplayType(0);
        final List<ItemConfigDO> expectedResult = Arrays.asList(itemConfigDO);

        // Run the test
        final List<ItemConfigDO> result = itemConfigServiceImplUnderTest.queryBatchByStoreGuid("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
