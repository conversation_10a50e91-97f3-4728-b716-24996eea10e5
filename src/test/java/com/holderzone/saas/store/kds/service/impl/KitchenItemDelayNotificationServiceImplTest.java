package com.holderzone.saas.store.kds.service.impl;

import com.holderzone.framework.rocketmq.common.DefaultRocketMqProducer;
import com.holderzone.saas.store.kds.entity.dto.KitchenItemDelayNotificationDTO;
import org.apache.rocketmq.common.message.Message;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;

@RunWith(MockitoJUnitRunner.class)
public class KitchenItemDelayNotificationServiceImplTest {

    @Mock
    private DefaultRocketMqProducer mockRocketMqProducer;

    private KitchenItemDelayNotificationServiceImpl kitchenItemDelayNotificationServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        kitchenItemDelayNotificationServiceImplUnderTest = new KitchenItemDelayNotificationServiceImpl(
                mockRocketMqProducer);
    }

    @Test
    public void testSendMessage() {
        // Setup
        final KitchenItemDelayNotificationDTO dto = new KitchenItemDelayNotificationDTO();
        dto.setStoreGuid("storeGuid");
        dto.setOrderGuid("orderGuid");
        dto.setDelayTimeTotal(0);
        dto.setDelayTimeLeft(0);
        dto.setKitchenItemGuidList(Arrays.asList("value"));

        // Run the test
        kitchenItemDelayNotificationServiceImplUnderTest.sendMessage(dto);

        // Verify the results
        verify(mockRocketMqProducer).sendMessage(any(Message.class));
    }
}
