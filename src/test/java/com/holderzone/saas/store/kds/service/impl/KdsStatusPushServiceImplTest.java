package com.holderzone.saas.store.kds.service.impl;

import com.holderzone.saas.store.kds.entity.domain.DeviceConfigDO;
import com.holderzone.saas.store.kds.service.KdsNotificationService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collection;

import static org.mockito.Mockito.verify;

@RunWith(MockitoJUnitRunner.class)
public class KdsStatusPushServiceImplTest {

    @Mock
    private KdsNotificationService mockKdsNotificationService;

    private KdsStatusPushServiceImpl kdsStatusPushServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        kdsStatusPushServiceImplUnderTest = new KdsStatusPushServiceImpl(mockKdsNotificationService);
    }

    @Test
    public void testStatusChanged1() {
        // Setup
        final DeviceConfigDO deviceConfigDO = new DeviceConfigDO();
        deviceConfigDO.setGuid("deviceId");
        deviceConfigDO.setStoreGuid("storeGuid");
        deviceConfigDO.setIsDineInOrderNotice(false);
        deviceConfigDO.setIsSnackOrderNotice(false);
        deviceConfigDO.setIsTakeoutOrderNotice(false);
        final Collection<DeviceConfigDO> deviceConfigInSql = Arrays.asList(deviceConfigDO);

        // Run the test
        kdsStatusPushServiceImplUnderTest.statusChanged(0, deviceConfigInSql);

        // Verify the results
        verify(mockKdsNotificationService).sendMessage("enterpriseGuid", "storeGuid", "deviceId", "kds_status",
                "voiceMsg");
    }

    @Test
    public void testStatusChanged2() {
        // Setup
        // Run the test
        kdsStatusPushServiceImplUnderTest.statusChanged("enterpriseGuid", "storeGuid", "deviceId", "voiceMsg");

        // Verify the results
        verify(mockKdsNotificationService).sendMessage("enterpriseGuid", "storeGuid", "deviceId", "kds_status",
                "voiceMsg");
    }

    @Test
    public void testVoiceBroadcast() {
        // Setup
        // Run the test
        kdsStatusPushServiceImplUnderTest.voiceBroadcast("enterpriseGuid", "storeGuid", "deviceId", "voiceMsg");

        // Verify the results
        verify(mockKdsNotificationService).sendMessage("enterpriseGuid", "storeGuid", "deviceId", "kds_voice_msg",
                "voiceMsg");
    }
}
