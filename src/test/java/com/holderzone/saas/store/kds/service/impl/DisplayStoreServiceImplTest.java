package com.holderzone.saas.store.kds.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.saas.store.dto.kds.req.DisplayRuleQueryDTO;
import com.holderzone.saas.store.dto.kds.resp.DisplayStoreRespDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.kds.entity.domain.DisplayRuleDO;
import com.holderzone.saas.store.kds.entity.domain.DisplayStoreDO;
import com.holderzone.saas.store.kds.mapper.DisplayRuleMapper;
import com.holderzone.saas.store.kds.mapstruct.DisplayRuleMapstruct;
import com.holderzone.saas.store.kds.service.rpc.StoreClientService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class DisplayStoreServiceImplTest {

    @Mock
    private DisplayRuleMapstruct mockRuleMapstruct;
    @Mock
    private StoreClientService mockStoreClientService;
    @Mock
    private DisplayRuleMapper mockDisplayRuleMapper;

    private DisplayStoreServiceImpl displayStoreServiceImplUnderTest;

    @Before
    public void setUp() {
        displayStoreServiceImplUnderTest = new DisplayStoreServiceImpl(mockRuleMapstruct, mockStoreClientService,
                mockDisplayRuleMapper);
    }

    @Test
    public void testListStore() {
        // Setup
        final DisplayRuleQueryDTO reqDTO = new DisplayRuleQueryDTO();
        reqDTO.setRuleGuid("ruleGuid");
        reqDTO.setRuleType(0);
        reqDTO.setBrandGuid("brandGuid");

        final DisplayStoreRespDTO displayStoreRespDTO = new DisplayStoreRespDTO();
        displayStoreRespDTO.setGuid("ae1d0207-fd4d-43d6-b1a9-4c8e38b14ef9");
        displayStoreRespDTO.setRuleGuid("ruleGuid");
        displayStoreRespDTO.setStoreGuid("storeGuid");
        displayStoreRespDTO.setStoreName("name");
        displayStoreRespDTO.setRuleType(0);
        final List<DisplayStoreRespDTO> expectedResult = Arrays.asList(displayStoreRespDTO);

        // Configure DisplayRuleMapstruct.toStoreRespList(...).
        final DisplayStoreRespDTO displayStoreRespDTO1 = new DisplayStoreRespDTO();
        displayStoreRespDTO1.setGuid("ae1d0207-fd4d-43d6-b1a9-4c8e38b14ef9");
        displayStoreRespDTO1.setRuleGuid("ruleGuid");
        displayStoreRespDTO1.setStoreGuid("storeGuid");
        displayStoreRespDTO1.setStoreName("name");
        displayStoreRespDTO1.setRuleType(0);
        final List<DisplayStoreRespDTO> displayStoreRespDTOS = Arrays.asList(displayStoreRespDTO1);
        final DisplayStoreDO displayStoreDO1 = new DisplayStoreDO();
        displayStoreDO1.setId(0L);
        displayStoreDO1.setGuid("c87f3937-80fa-4075-b6e1-21480465ad1f");
        displayStoreDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        displayStoreDO1.setRuleGuid("ruleGuid");
        displayStoreDO1.setRuleType(0);
        final List<DisplayStoreDO> displayStoreDO = Arrays.asList(displayStoreDO1);
        when(mockRuleMapstruct.toStoreRespList(displayStoreDO)).thenReturn(displayStoreRespDTOS);

        // Configure StoreClientService.queryStoreByIdList(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("a59a06c6-4232-404c-9018-000785f2563e");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO);
        when(mockStoreClientService.queryStoreByIdList(Arrays.asList("value"))).thenReturn(storeDTOS);

        // Configure DisplayRuleMapper.selectList(...).
        final DisplayRuleDO displayRuleDO = new DisplayRuleDO();
        displayRuleDO.setId(0L);
        displayRuleDO.setGuid("2d44abd9-be93-4d88-9446-7714b396def7");
        displayRuleDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        displayRuleDO.setRuleType(0);
        displayRuleDO.setBrandGuid("brandGuid");
        final List<DisplayRuleDO> displayRuleDOS = Arrays.asList(displayRuleDO);
        when(mockDisplayRuleMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(displayRuleDOS);

        // Run the test
        final List<DisplayStoreRespDTO> result = displayStoreServiceImplUnderTest.listStore(reqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testListStore_DisplayRuleMapstructReturnsNoItems() {
        // Setup
        final DisplayRuleQueryDTO reqDTO = new DisplayRuleQueryDTO();
        reqDTO.setRuleGuid("ruleGuid");
        reqDTO.setRuleType(0);
        reqDTO.setBrandGuid("brandGuid");

        // Configure DisplayRuleMapstruct.toStoreRespList(...).
        final DisplayStoreDO displayStoreDO1 = new DisplayStoreDO();
        displayStoreDO1.setId(0L);
        displayStoreDO1.setGuid("c87f3937-80fa-4075-b6e1-21480465ad1f");
        displayStoreDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        displayStoreDO1.setRuleGuid("ruleGuid");
        displayStoreDO1.setRuleType(0);
        final List<DisplayStoreDO> displayStoreDO = Arrays.asList(displayStoreDO1);
        when(mockRuleMapstruct.toStoreRespList(displayStoreDO)).thenReturn(Collections.emptyList());

        // Configure DisplayRuleMapper.selectList(...).
        final DisplayRuleDO displayRuleDO = new DisplayRuleDO();
        displayRuleDO.setId(0L);
        displayRuleDO.setGuid("2d44abd9-be93-4d88-9446-7714b396def7");
        displayRuleDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        displayRuleDO.setRuleType(0);
        displayRuleDO.setBrandGuid("brandGuid");
        final List<DisplayRuleDO> displayRuleDOS = Arrays.asList(displayRuleDO);
        when(mockDisplayRuleMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(displayRuleDOS);

        // Run the test
        final List<DisplayStoreRespDTO> result = displayStoreServiceImplUnderTest.listStore(reqDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testListStore_StoreClientServiceReturnsNoItems() {
        // Setup
        final DisplayRuleQueryDTO reqDTO = new DisplayRuleQueryDTO();
        reqDTO.setRuleGuid("ruleGuid");
        reqDTO.setRuleType(0);
        reqDTO.setBrandGuid("brandGuid");

        final DisplayStoreRespDTO displayStoreRespDTO = new DisplayStoreRespDTO();
        displayStoreRespDTO.setGuid("ae1d0207-fd4d-43d6-b1a9-4c8e38b14ef9");
        displayStoreRespDTO.setRuleGuid("ruleGuid");
        displayStoreRespDTO.setStoreGuid("storeGuid");
        displayStoreRespDTO.setStoreName("name");
        displayStoreRespDTO.setRuleType(0);
        final List<DisplayStoreRespDTO> expectedResult = Arrays.asList(displayStoreRespDTO);

        // Configure DisplayRuleMapstruct.toStoreRespList(...).
        final DisplayStoreRespDTO displayStoreRespDTO1 = new DisplayStoreRespDTO();
        displayStoreRespDTO1.setGuid("ae1d0207-fd4d-43d6-b1a9-4c8e38b14ef9");
        displayStoreRespDTO1.setRuleGuid("ruleGuid");
        displayStoreRespDTO1.setStoreGuid("storeGuid");
        displayStoreRespDTO1.setStoreName("name");
        displayStoreRespDTO1.setRuleType(0);
        final List<DisplayStoreRespDTO> displayStoreRespDTOS = Arrays.asList(displayStoreRespDTO1);
        final DisplayStoreDO displayStoreDO1 = new DisplayStoreDO();
        displayStoreDO1.setId(0L);
        displayStoreDO1.setGuid("c87f3937-80fa-4075-b6e1-21480465ad1f");
        displayStoreDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        displayStoreDO1.setRuleGuid("ruleGuid");
        displayStoreDO1.setRuleType(0);
        final List<DisplayStoreDO> displayStoreDO = Arrays.asList(displayStoreDO1);
        when(mockRuleMapstruct.toStoreRespList(displayStoreDO)).thenReturn(displayStoreRespDTOS);

        when(mockStoreClientService.queryStoreByIdList(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Configure DisplayRuleMapper.selectList(...).
        final DisplayRuleDO displayRuleDO = new DisplayRuleDO();
        displayRuleDO.setId(0L);
        displayRuleDO.setGuid("2d44abd9-be93-4d88-9446-7714b396def7");
        displayRuleDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        displayRuleDO.setRuleType(0);
        displayRuleDO.setBrandGuid("brandGuid");
        final List<DisplayRuleDO> displayRuleDOS = Arrays.asList(displayRuleDO);
        when(mockDisplayRuleMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(displayRuleDOS);

        // Run the test
        final List<DisplayStoreRespDTO> result = displayStoreServiceImplUnderTest.listStore(reqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testListStore_DisplayRuleMapperReturnsNoItems() {
        // Setup
        final DisplayRuleQueryDTO reqDTO = new DisplayRuleQueryDTO();
        reqDTO.setRuleGuid("ruleGuid");
        reqDTO.setRuleType(0);
        reqDTO.setBrandGuid("brandGuid");

        final DisplayStoreRespDTO displayStoreRespDTO = new DisplayStoreRespDTO();
        displayStoreRespDTO.setGuid("ae1d0207-fd4d-43d6-b1a9-4c8e38b14ef9");
        displayStoreRespDTO.setRuleGuid("ruleGuid");
        displayStoreRespDTO.setStoreGuid("storeGuid");
        displayStoreRespDTO.setStoreName("name");
        displayStoreRespDTO.setRuleType(0);
        final List<DisplayStoreRespDTO> expectedResult = Arrays.asList(displayStoreRespDTO);

        // Configure DisplayRuleMapstruct.toStoreRespList(...).
        final DisplayStoreRespDTO displayStoreRespDTO1 = new DisplayStoreRespDTO();
        displayStoreRespDTO1.setGuid("ae1d0207-fd4d-43d6-b1a9-4c8e38b14ef9");
        displayStoreRespDTO1.setRuleGuid("ruleGuid");
        displayStoreRespDTO1.setStoreGuid("storeGuid");
        displayStoreRespDTO1.setStoreName("name");
        displayStoreRespDTO1.setRuleType(0);
        final List<DisplayStoreRespDTO> displayStoreRespDTOS = Arrays.asList(displayStoreRespDTO1);
        final DisplayStoreDO displayStoreDO1 = new DisplayStoreDO();
        displayStoreDO1.setId(0L);
        displayStoreDO1.setGuid("c87f3937-80fa-4075-b6e1-21480465ad1f");
        displayStoreDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        displayStoreDO1.setRuleGuid("ruleGuid");
        displayStoreDO1.setRuleType(0);
        final List<DisplayStoreDO> displayStoreDO = Arrays.asList(displayStoreDO1);
        when(mockRuleMapstruct.toStoreRespList(displayStoreDO)).thenReturn(displayStoreRespDTOS);

        // Configure StoreClientService.queryStoreByIdList(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("a59a06c6-4232-404c-9018-000785f2563e");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO);
        when(mockStoreClientService.queryStoreByIdList(Arrays.asList("value"))).thenReturn(storeDTOS);

        when(mockDisplayRuleMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<DisplayStoreRespDTO> result = displayStoreServiceImplUnderTest.listStore(reqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryStoreInfo() {
        // Setup
        final DisplayStoreRespDTO displayStoreRespDTO = new DisplayStoreRespDTO();
        displayStoreRespDTO.setGuid("ae1d0207-fd4d-43d6-b1a9-4c8e38b14ef9");
        displayStoreRespDTO.setRuleGuid("ruleGuid");
        displayStoreRespDTO.setStoreGuid("storeGuid");
        displayStoreRespDTO.setStoreName("name");
        displayStoreRespDTO.setRuleType(0);
        final List<DisplayStoreRespDTO> displayStoreRespList = Arrays.asList(displayStoreRespDTO);
        final DisplayStoreRespDTO displayStoreRespDTO1 = new DisplayStoreRespDTO();
        displayStoreRespDTO1.setGuid("ae1d0207-fd4d-43d6-b1a9-4c8e38b14ef9");
        displayStoreRespDTO1.setRuleGuid("ruleGuid");
        displayStoreRespDTO1.setStoreGuid("storeGuid");
        displayStoreRespDTO1.setStoreName("name");
        displayStoreRespDTO1.setRuleType(0);
        final List<DisplayStoreRespDTO> expectedResult = Arrays.asList(displayStoreRespDTO1);

        // Configure StoreClientService.queryStoreByIdList(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("a59a06c6-4232-404c-9018-000785f2563e");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO);
        when(mockStoreClientService.queryStoreByIdList(Arrays.asList("value"))).thenReturn(storeDTOS);

        // Run the test
        final List<DisplayStoreRespDTO> result = displayStoreServiceImplUnderTest.queryStoreInfo(Arrays.asList("value"),
                displayStoreRespList);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryStoreInfo_StoreClientServiceReturnsNoItems() {
        // Setup
        final DisplayStoreRespDTO displayStoreRespDTO = new DisplayStoreRespDTO();
        displayStoreRespDTO.setGuid("ae1d0207-fd4d-43d6-b1a9-4c8e38b14ef9");
        displayStoreRespDTO.setRuleGuid("ruleGuid");
        displayStoreRespDTO.setStoreGuid("storeGuid");
        displayStoreRespDTO.setStoreName("name");
        displayStoreRespDTO.setRuleType(0);
        final List<DisplayStoreRespDTO> displayStoreRespList = Arrays.asList(displayStoreRespDTO);
        when(mockStoreClientService.queryStoreByIdList(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        final List<DisplayStoreRespDTO> result = displayStoreServiceImplUnderTest.queryStoreInfo(Arrays.asList("value"),
                displayStoreRespList);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }
}
