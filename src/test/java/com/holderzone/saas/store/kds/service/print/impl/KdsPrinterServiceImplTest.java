package com.holderzone.saas.store.kds.service.print.impl;

import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.kds.req.*;
import com.holderzone.saas.store.dto.kds.resp.KdsPrinterRespDTO;
import com.holderzone.saas.store.kds.entity.domain.KdsPrinterDO;
import com.holderzone.saas.store.kds.mapstruct.KdsPrintMapstruct;
import com.holderzone.saas.store.kds.service.DeviceConfigService;
import com.holderzone.saas.store.kds.service.DistributedIdService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class KdsPrinterServiceImplTest {

    @Mock
    private DistributedIdService mockDistributedIdService;
    @Mock
    private DeviceConfigService mockDeviceConfigService;
    @Mock
    private KdsPrintMapstruct mockKdsPrintMapstruct;

    private KdsPrinterServiceImpl kdsPrinterServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        kdsPrinterServiceImplUnderTest = new KdsPrinterServiceImpl(mockDistributedIdService, mockDeviceConfigService,
                mockKdsPrintMapstruct);
    }

    @Test
    public void testCreatePrinter() {
        // Setup
        final KdsPrinterCreateReqDTO kdsPrinterCreateReqDTO = new KdsPrinterCreateReqDTO();
        kdsPrinterCreateReqDTO.setStoreGuid("storeGuid");
        kdsPrinterCreateReqDTO.setPrinterName("printerName");
        kdsPrinterCreateReqDTO.setPrinterIp("printerIp");
        kdsPrinterCreateReqDTO.setPrinterPort(0);
        kdsPrinterCreateReqDTO.setPageSize("pageSize");

        // Configure KdsPrintMapstruct.fromCreateReq(...).
        final KdsPrinterDO kdsPrinterDO = new KdsPrinterDO();
        kdsPrinterDO.setGuid("2524de45-ae60-4a53-bdac-b351787256dd");
        kdsPrinterDO.setStoreGuid("storeGuid");
        kdsPrinterDO.setPrinterName("printerName");
        kdsPrinterDO.setPrinterIp("printerIp");
        kdsPrinterDO.setPrinterPort(0);
        final KdsPrinterCreateReqDTO kdsPrinterCreateReqDTO1 = new KdsPrinterCreateReqDTO();
        kdsPrinterCreateReqDTO1.setStoreGuid("storeGuid");
        kdsPrinterCreateReqDTO1.setPrinterName("printerName");
        kdsPrinterCreateReqDTO1.setPrinterIp("printerIp");
        kdsPrinterCreateReqDTO1.setPrinterPort(0);
        kdsPrinterCreateReqDTO1.setPageSize("pageSize");
        when(mockKdsPrintMapstruct.fromCreateReq(kdsPrinterCreateReqDTO1)).thenReturn(kdsPrinterDO);

        when(mockDistributedIdService.nextPrinterGuid()).thenReturn("2524de45-ae60-4a53-bdac-b351787256dd");

        // Run the test
        kdsPrinterServiceImplUnderTest.createPrinter(kdsPrinterCreateReqDTO);

        // Verify the results
    }

    @Test
    public void testUpdatePrinter() {
        // Setup
        final KdsPrinterUpdateReqDTO kdsPrinterUpdateReqDTO = new KdsPrinterUpdateReqDTO();
        kdsPrinterUpdateReqDTO.setStoreGuid("storeGuid");
        kdsPrinterUpdateReqDTO.setPrinterGuid("printerGuid");
        kdsPrinterUpdateReqDTO.setPrinterName("printerName");
        kdsPrinterUpdateReqDTO.setPrinterIp("printerIp");
        kdsPrinterUpdateReqDTO.setPrinterPort(0);

        // Configure KdsPrintMapstruct.fromUpdateReq(...).
        final KdsPrinterDO kdsPrinterDO = new KdsPrinterDO();
        kdsPrinterDO.setGuid("2524de45-ae60-4a53-bdac-b351787256dd");
        kdsPrinterDO.setStoreGuid("storeGuid");
        kdsPrinterDO.setPrinterName("printerName");
        kdsPrinterDO.setPrinterIp("printerIp");
        kdsPrinterDO.setPrinterPort(0);
        final KdsPrinterUpdateReqDTO kdsPrinterUpdateReqDTO1 = new KdsPrinterUpdateReqDTO();
        kdsPrinterUpdateReqDTO1.setStoreGuid("storeGuid");
        kdsPrinterUpdateReqDTO1.setPrinterGuid("printerGuid");
        kdsPrinterUpdateReqDTO1.setPrinterName("printerName");
        kdsPrinterUpdateReqDTO1.setPrinterIp("printerIp");
        kdsPrinterUpdateReqDTO1.setPrinterPort(0);
        when(mockKdsPrintMapstruct.fromUpdateReq(kdsPrinterUpdateReqDTO1)).thenReturn(kdsPrinterDO);

        // Run the test
        kdsPrinterServiceImplUnderTest.updatePrinter(kdsPrinterUpdateReqDTO);

        // Verify the results
    }

    @Test
    public void testDeletePrinter() {
        // Setup
        final KdsPrinterDeleteReqDTO kdsPrinterDeleteReqDTO = new KdsPrinterDeleteReqDTO();
        kdsPrinterDeleteReqDTO.setPrinterGuid("printerGuid");

        // Configure KdsPrintMapstruct.fromDeleteReq(...).
        final KdsPrinterDO kdsPrinterDO = new KdsPrinterDO();
        kdsPrinterDO.setGuid("2524de45-ae60-4a53-bdac-b351787256dd");
        kdsPrinterDO.setStoreGuid("storeGuid");
        kdsPrinterDO.setPrinterName("printerName");
        kdsPrinterDO.setPrinterIp("printerIp");
        kdsPrinterDO.setPrinterPort(0);
        final KdsPrinterDeleteReqDTO kdsPrinterDeleteReqDTO1 = new KdsPrinterDeleteReqDTO();
        kdsPrinterDeleteReqDTO1.setPrinterGuid("printerGuid");
        when(mockKdsPrintMapstruct.fromDeleteReq(kdsPrinterDeleteReqDTO1)).thenReturn(kdsPrinterDO);

        // Run the test
        kdsPrinterServiceImplUnderTest.deletePrinter(kdsPrinterDeleteReqDTO);

        // Verify the results
        verify(mockDeviceConfigService).unbindPrinter("printerGuid");
    }

    @Test
    public void testPageAllPrinter() {
        // Setup
        final KdsPrinterPageReqDTO kdsPrinterPageReqDTO = new KdsPrinterPageReqDTO();
        kdsPrinterPageReqDTO.setStoreGuid("storeGuid");
        kdsPrinterPageReqDTO.setDeviceId("deviceId");

        when(mockDeviceConfigService.queryBoundPrinterGuidByDeviceId("storeGuid", "deviceId")).thenReturn("result");

        // Configure KdsPrintMapstruct.toKdsPrinterResp(...).
        final KdsPrinterRespDTO kdsPrinterRespDTO = new KdsPrinterRespDTO();
        kdsPrinterRespDTO.setStoreGuid("storeGuid");
        kdsPrinterRespDTO.setDeviceId("deviceId");
        kdsPrinterRespDTO.setPrinterGuid("printerGuid");
        kdsPrinterRespDTO.setPrinterName("printerName");
        kdsPrinterRespDTO.setIsBoundBySelf(false);
        final KdsPrinterDO kdsPrinterDeleteReqDTO = new KdsPrinterDO();
        kdsPrinterDeleteReqDTO.setGuid("2524de45-ae60-4a53-bdac-b351787256dd");
        kdsPrinterDeleteReqDTO.setStoreGuid("storeGuid");
        kdsPrinterDeleteReqDTO.setPrinterName("printerName");
        kdsPrinterDeleteReqDTO.setPrinterIp("printerIp");
        kdsPrinterDeleteReqDTO.setPrinterPort(0);
        when(mockKdsPrintMapstruct.toKdsPrinterResp(kdsPrinterDeleteReqDTO)).thenReturn(kdsPrinterRespDTO);

        // Run the test
        final Page<KdsPrinterRespDTO> result = kdsPrinterServiceImplUnderTest.pageAllPrinter(kdsPrinterPageReqDTO);

        // Verify the results
    }

    @Test
    public void testBindPrinter() {
        // Setup
        final KdsPrinterBindUnbindReqDTO kdsPrinterBindUnbindReqDTO = new KdsPrinterBindUnbindReqDTO();
        kdsPrinterBindUnbindReqDTO.setStoreGuid("storeGuid");
        kdsPrinterBindUnbindReqDTO.setDeviceId("deviceId");
        kdsPrinterBindUnbindReqDTO.setPrinterGuid("printerGuid");

        // Configure KdsPrintMapstruct.fromBindUnbindReq(...).
        final KdsPrinterDO kdsPrinterDO = new KdsPrinterDO();
        kdsPrinterDO.setGuid("2524de45-ae60-4a53-bdac-b351787256dd");
        kdsPrinterDO.setStoreGuid("storeGuid");
        kdsPrinterDO.setPrinterName("printerName");
        kdsPrinterDO.setPrinterIp("printerIp");
        kdsPrinterDO.setPrinterPort(0);
        final KdsPrinterBindUnbindReqDTO kdsPrinterBindUnbindReqDTO1 = new KdsPrinterBindUnbindReqDTO();
        kdsPrinterBindUnbindReqDTO1.setStoreGuid("storeGuid");
        kdsPrinterBindUnbindReqDTO1.setDeviceId("deviceId");
        kdsPrinterBindUnbindReqDTO1.setPrinterGuid("printerGuid");
        when(mockKdsPrintMapstruct.fromBindUnbindReq(kdsPrinterBindUnbindReqDTO1)).thenReturn(kdsPrinterDO);

        // Run the test
        kdsPrinterServiceImplUnderTest.bindPrinter(kdsPrinterBindUnbindReqDTO);

        // Verify the results
        // Confirm DeviceConfigService.bindPrinter(...).
        final KdsPrinterBindUnbindReqDTO kdsPrinterBindUnbindReqDTO2 = new KdsPrinterBindUnbindReqDTO();
        kdsPrinterBindUnbindReqDTO2.setStoreGuid("storeGuid");
        kdsPrinterBindUnbindReqDTO2.setDeviceId("deviceId");
        kdsPrinterBindUnbindReqDTO2.setPrinterGuid("printerGuid");
        verify(mockDeviceConfigService).bindPrinter(kdsPrinterBindUnbindReqDTO2);
    }

    @Test
    public void testRebindPrinter() {
        // Setup
        final KdsPrinterBindUnbindReqDTO kdsPrinterBindUnbindReqDTO = new KdsPrinterBindUnbindReqDTO();
        kdsPrinterBindUnbindReqDTO.setStoreGuid("storeGuid");
        kdsPrinterBindUnbindReqDTO.setDeviceId("deviceId");
        kdsPrinterBindUnbindReqDTO.setPrinterGuid("printerGuid");

        // Configure KdsPrintMapstruct.fromBindUnbindReq(...).
        final KdsPrinterDO kdsPrinterDO = new KdsPrinterDO();
        kdsPrinterDO.setGuid("2524de45-ae60-4a53-bdac-b351787256dd");
        kdsPrinterDO.setStoreGuid("storeGuid");
        kdsPrinterDO.setPrinterName("printerName");
        kdsPrinterDO.setPrinterIp("printerIp");
        kdsPrinterDO.setPrinterPort(0);
        final KdsPrinterBindUnbindReqDTO kdsPrinterBindUnbindReqDTO1 = new KdsPrinterBindUnbindReqDTO();
        kdsPrinterBindUnbindReqDTO1.setStoreGuid("storeGuid");
        kdsPrinterBindUnbindReqDTO1.setDeviceId("deviceId");
        kdsPrinterBindUnbindReqDTO1.setPrinterGuid("printerGuid");
        when(mockKdsPrintMapstruct.fromBindUnbindReq(kdsPrinterBindUnbindReqDTO1)).thenReturn(kdsPrinterDO);

        // Run the test
        kdsPrinterServiceImplUnderTest.rebindPrinter(kdsPrinterBindUnbindReqDTO);

        // Verify the results
        // Confirm DeviceConfigService.rebindPrinter(...).
        final KdsPrinterBindUnbindReqDTO kdsPrinterBindUnbindReqDTO2 = new KdsPrinterBindUnbindReqDTO();
        kdsPrinterBindUnbindReqDTO2.setStoreGuid("storeGuid");
        kdsPrinterBindUnbindReqDTO2.setDeviceId("deviceId");
        kdsPrinterBindUnbindReqDTO2.setPrinterGuid("printerGuid");
        verify(mockDeviceConfigService).rebindPrinter(kdsPrinterBindUnbindReqDTO2);
    }

    @Test
    public void testUnbindPrinter() {
        // Setup
        final KdsPrinterBindUnbindReqDTO kdsPrinterBindUnbindReqDTO = new KdsPrinterBindUnbindReqDTO();
        kdsPrinterBindUnbindReqDTO.setStoreGuid("storeGuid");
        kdsPrinterBindUnbindReqDTO.setDeviceId("deviceId");
        kdsPrinterBindUnbindReqDTO.setPrinterGuid("printerGuid");

        // Configure KdsPrintMapstruct.fromBindUnbindReq(...).
        final KdsPrinterDO kdsPrinterDO = new KdsPrinterDO();
        kdsPrinterDO.setGuid("2524de45-ae60-4a53-bdac-b351787256dd");
        kdsPrinterDO.setStoreGuid("storeGuid");
        kdsPrinterDO.setPrinterName("printerName");
        kdsPrinterDO.setPrinterIp("printerIp");
        kdsPrinterDO.setPrinterPort(0);
        final KdsPrinterBindUnbindReqDTO kdsPrinterBindUnbindReqDTO1 = new KdsPrinterBindUnbindReqDTO();
        kdsPrinterBindUnbindReqDTO1.setStoreGuid("storeGuid");
        kdsPrinterBindUnbindReqDTO1.setDeviceId("deviceId");
        kdsPrinterBindUnbindReqDTO1.setPrinterGuid("printerGuid");
        when(mockKdsPrintMapstruct.fromBindUnbindReq(kdsPrinterBindUnbindReqDTO1)).thenReturn(kdsPrinterDO);

        // Run the test
        kdsPrinterServiceImplUnderTest.unbindPrinter(kdsPrinterBindUnbindReqDTO);

        // Verify the results
        // Confirm DeviceConfigService.unbindPrinter(...).
        final KdsPrinterBindUnbindReqDTO kdsPrinterBindUnbindReqDTO2 = new KdsPrinterBindUnbindReqDTO();
        kdsPrinterBindUnbindReqDTO2.setStoreGuid("storeGuid");
        kdsPrinterBindUnbindReqDTO2.setDeviceId("deviceId");
        kdsPrinterBindUnbindReqDTO2.setPrinterGuid("printerGuid");
        verify(mockDeviceConfigService).unbindPrinter(kdsPrinterBindUnbindReqDTO2);
    }
}
