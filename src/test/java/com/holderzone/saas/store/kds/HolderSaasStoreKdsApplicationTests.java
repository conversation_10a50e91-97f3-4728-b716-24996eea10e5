package com.holderzone.saas.store.kds;

import com.alibaba.fastjson.JSON;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.kds.req.DstItemStatusReqDTO;
import com.holderzone.saas.store.dto.kds.req.PrdItemStatusReqDTO;
import com.holderzone.saas.store.dto.trade.req.adjust.AdjustOrderReqDTO;
import com.holderzone.saas.store.kds.entity.domain.QueueConfigDO;
import com.holderzone.saas.store.kds.mapper.KitchenItemMapper;
import com.holderzone.saas.store.kds.service.QueueConfigService;
import com.holderzone.saas.store.kds.utils.JsonFileUtil;
import com.holderzone.saas.store.kds.utils.MockHttpUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.web.context.WebApplicationContext;
import sun.misc.Unsafe;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.Arrays;

import static com.holderzone.saas.store.dto.common.CommonConstant.USER_INFO;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@SpringBootTest
public class HolderSaasStoreKdsApplicationTests {

    @Autowired
    private QueueConfigService queueConfigService;

    @Autowired
    private DataSourceTransactionManager dstManager;

    @Resource
    private KitchenItemMapper kitchenItemMapper;

    private static final String KITCHEN_ITEM = "/kitchen_item";

    private static final String userInfo = "{\"operSubjectGuid\": \"2008141005594470007\",\"enterpriseGuid\":" +
            " \"6506431195651982337\",\"enterpriseName\": \"企业0227\",\"enterpriseNo\": \"********\",\"storeGuid\":" +
            " \"6619160595813892096\",\"storeName\": \"听雨阁\",\"storeNo\": \"6148139\",\"userGuid\": " +
            "\"6653489337230950401\",\"account\": \"200003\",\"tel\": \"***********\",\"name\": \"赵亮\"}\n";

    @Autowired
    private WebApplicationContext wac;

    private MockMvc mockMvc;

    @Before
    public void setUp() {
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();
    }

    @Test
    public void contextLoads() {
        String s = JacksonUtils.writeValueAsString(Arrays.asList(null, "s"));
        System.out.println(s);
//        List<String> list = JacksonUtils.toObjectList(String.class, s);
    }

//    @Before
    public void before() {
        EnterpriseIdentifier.setEnterpriseGuid("6506431195651982337");
    }

    //    @Test
    public void testMybatis() {
        EnterpriseIdentifier.setEnterpriseGuid("6506431195651982337");
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW); // 事物隔离级别，开启新事务，这样会比较安全些。
        TransactionStatus transaction = dstManager.getTransaction(def); // 获得事务状态
        try {
            QueueConfigDO byId = queueConfigService.getById(1);
            printAddresses("address", byId);
            byId.setSource(3);
            byId = queueConfigService.getById(1);
            printAddresses("address", byId);
            dstManager.commit(transaction);
        } catch (Throwable throwable) {
            dstManager.rollback(transaction);
        }
    }

    static final Unsafe unsafe = getUnsafe();
    static final boolean is64bit = true; // auto detect if possible.

    private static Unsafe getUnsafe() {
        try {
            Field theUnsafe = Unsafe.class.getDeclaredField("theUnsafe");
            theUnsafe.setAccessible(true);
            return (Unsafe) theUnsafe.get(null);
        } catch (Exception e) {
            throw new AssertionError(e);
        }
    }

    public static void printAddresses(String label, Object... objects) {
        System.out.print(label + ":         0x");
        long last = 0;
        int offset = unsafe.arrayBaseOffset(objects.getClass());
        int scale = unsafe.arrayIndexScale(objects.getClass());
        switch (scale) {
            case 4:
                long factor = is64bit ? 8 : 1;
                final long i1 = (unsafe.getInt(objects, offset) & 0xFFFFFFFFL) * factor;
                System.out.print(Long.toHexString(i1));
                last = i1;
                for (int i = 1; i < objects.length; i++) {
                    final long i2 = (unsafe.getInt(objects, offset + i * 4) & 0xFFFFFFFFL) * factor;
                    if (i2 > last)
                        System.out.print(", +" + Long.toHexString(i2 - last));
                    else
                        System.out.print(", -" + Long.toHexString(last - i2));
                    last = i2;
                }
                break;
            case 8:
                throw new AssertionError("Not supported");
        }
        System.out.println();
    }

    /**
     * {"deviceType":9,"deviceId":"2102051043031950008","userGuid":"6724876849488330753","userName":"靓亮仔",
     * "enterpriseGuid":"6506431195651982337","enterpriseName":"企业0227","storeGuid":"6619160595813892096",
     * "storeName":"听雨阁xxx","requestTimestamp":1644205214160,"currentPage":1,"displayMode":3,"displayType":"8",
     * "kitchenState":4,"pageSize":6,"pointGuid":null,"queryModeForPoint":null}
     */
    @Test
    public void testPrdQuery() throws Exception {
        PrdItemStatusReqDTO prdItemStatusReqDTO = new PrdItemStatusReqDTO();
        prdItemStatusReqDTO.setStoreGuid("6619160595813892096");
        prdItemStatusReqDTO.setDeviceId("2102051043031950008");
        prdItemStatusReqDTO.setPointGuid(null);
        prdItemStatusReqDTO.setDisplayType("8");
        prdItemStatusReqDTO.setQueryModeForPoint(null);
        prdItemStatusReqDTO.setKitchenState(4);
        prdItemStatusReqDTO.setCurrentPage(1);

        // 制作点: 菜品汇总模式
//        prdItemStatusReqDTO.setDisplayMode(1);
//        prdItemStatusReqDTO.setPageSize(8);

        // 混合-订单模式
//        prdItemStatusReqDTO.setDisplayMode(4);
//        prdItemStatusReqDTO.setPageSize(5);

        // 混合-汇总模式
        prdItemStatusReqDTO.setDisplayMode(5);

        String jsonString = JSON.toJSONString(prdItemStatusReqDTO);
        MvcResult mvcResult = mockMvc.perform(post("/kitchen_item/prd_query")
                .header(USER_INFO, userInfo)
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(jsonString))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("response:" + contentAsString);
    }

    @Test
    public void testDstQuery() throws Exception {
        DstItemStatusReqDTO reqVO = JSON.parseObject(JsonFileUtil.read("kitchen/testDstQuery.json"), DstItemStatusReqDTO.class);
        String content = MockHttpUtil.post(KITCHEN_ITEM + "/dst_query", reqVO, mockMvc);
        System.out.println(content);
    }
}
