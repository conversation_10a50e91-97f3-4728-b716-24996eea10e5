package com.holderzone.saas.store.kds.service.impl;

import com.holderzone.saas.store.dto.kds.req.*;
import com.holderzone.saas.store.dto.kds.resp.DeviceStatusRespDTO;
import com.holderzone.saas.store.kds.entity.domain.DeviceConfigDO;
import com.holderzone.saas.store.kds.mapstruct.DeviceConfigMapstruct;
import com.holderzone.saas.store.kds.service.DeviceBindItemGroupService;
import com.holderzone.saas.store.kds.service.DisplayRepeatItemService;
import com.holderzone.saas.store.kds.service.DistributeService;
import com.holderzone.saas.store.kds.service.ProductionPointService;
import com.holderzone.saas.store.kds.service.rpc.StoreClientService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class DeviceConfigServiceImplTest {

    @Mock
    private DeviceConfigMapstruct mockDeviceConfigMapstruct;
    @Mock
    private DistributeService mockDistributeService;
    @Mock
    private ProductionPointService mockProductionPointService;

    @Mock
    private DisplayRepeatItemService displayRepeatItemService;

    @Mock
    private DeviceBindItemGroupService deviceBindItemGroupService;

    @Mock
    private StoreClientService mockStoreClientService;

    private DeviceConfigServiceImpl deviceConfigServiceImplUnderTest;

    @Before
    public void setUp() {
        deviceConfigServiceImplUnderTest = new DeviceConfigServiceImpl(mockDeviceConfigMapstruct, mockDistributeService,
                mockProductionPointService, displayRepeatItemService, deviceBindItemGroupService, mockStoreClientService);
    }

    @Test
    public void testCreate() {
        // Setup
        final DeviceCreateReqDTO deviceCreateReqDTO = new DeviceCreateReqDTO();
        deviceCreateReqDTO.setStoreGuid("storeGuid");
        deviceCreateReqDTO.setDeviceId("deviceId");
        deviceCreateReqDTO.setName("name");
        deviceCreateReqDTO.setPointMode(0);

        final DeviceStatusRespDTO expectedResult = new DeviceStatusRespDTO();
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setDeviceId("deviceId");
        expectedResult.setName("name");
        final DevicePrdConfDTO devicePrdConfDTO = new DevicePrdConfDTO();
        expectedResult.setDevicePrdConfDTO(devicePrdConfDTO);
        final DeviceDstConfDTO deviceDstConfDTO = new DeviceDstConfDTO();
        expectedResult.setDeviceDstConfDTO(deviceDstConfDTO);

        // Configure DeviceConfigMapstruct.fromCreateReq(...).
        final DeviceConfigDO deviceConfigDO = new DeviceConfigDO();
        deviceConfigDO.setGuid("4315480d-f7e6-4064-98fb-6f73b12acd8d");
        deviceConfigDO.setStoreGuid("storeGuid");
        deviceConfigDO.setName("name");
        deviceConfigDO.setPointMode(0);
        deviceConfigDO.setDisplayMode(0);
        deviceConfigDO.setDisplayType(0);
        deviceConfigDO.setIsShowHangedItem(false);
        deviceConfigDO.setIsProduceHangedItem(false);
        deviceConfigDO.setIsDisplayByMaxCopied(false);
        deviceConfigDO.setIsPrintAutomatic(false);
        deviceConfigDO.setIsPrintPerOrder(false);
        deviceConfigDO.setIsFilterTakeOutOrder(false);
        deviceConfigDO.setIsDineInOrderNotice(false);
        deviceConfigDO.setIsSnackOrderNotice(false);
        deviceConfigDO.setIsTakeoutOrderNotice(false);
        deviceConfigDO.setIsDispatchAsPrint(false);
        deviceConfigDO.setPrinterGuid("printerGuid");
        deviceConfigDO.setIsInitialized(false);
        final DeviceCreateReqDTO deviceCreateReqDTO1 = new DeviceCreateReqDTO();
        deviceCreateReqDTO1.setStoreGuid("storeGuid");
        deviceCreateReqDTO1.setDeviceId("deviceId");
        deviceCreateReqDTO1.setName("name");
        deviceCreateReqDTO1.setPointMode(0);
        when(mockDeviceConfigMapstruct.fromCreateReq(deviceCreateReqDTO1)).thenReturn(deviceConfigDO);

        // Configure DeviceConfigMapstruct.toStatusResp(...).
        final DeviceStatusRespDTO deviceStatusRespDTO = new DeviceStatusRespDTO();
        deviceStatusRespDTO.setStoreGuid("storeGuid");
        deviceStatusRespDTO.setDeviceId("deviceId");
        deviceStatusRespDTO.setName("name");
        final DevicePrdConfDTO devicePrdConfDTO1 = new DevicePrdConfDTO();
        deviceStatusRespDTO.setDevicePrdConfDTO(devicePrdConfDTO1);
        final DeviceDstConfDTO deviceDstConfDTO1 = new DeviceDstConfDTO();
        deviceStatusRespDTO.setDeviceDstConfDTO(deviceDstConfDTO1);
        final DeviceConfigDO deviceConfigDO1 = new DeviceConfigDO();
        deviceConfigDO1.setGuid("4315480d-f7e6-4064-98fb-6f73b12acd8d");
        deviceConfigDO1.setStoreGuid("storeGuid");
        deviceConfigDO1.setName("name");
        deviceConfigDO1.setPointMode(0);
        deviceConfigDO1.setDisplayMode(0);
        deviceConfigDO1.setDisplayType(0);
        deviceConfigDO1.setIsShowHangedItem(false);
        deviceConfigDO1.setIsProduceHangedItem(false);
        deviceConfigDO1.setIsDisplayByMaxCopied(false);
        deviceConfigDO1.setIsPrintAutomatic(false);
        deviceConfigDO1.setIsPrintPerOrder(false);
        deviceConfigDO1.setIsFilterTakeOutOrder(false);
        deviceConfigDO1.setIsDineInOrderNotice(false);
        deviceConfigDO1.setIsSnackOrderNotice(false);
        deviceConfigDO1.setIsTakeoutOrderNotice(false);
        deviceConfigDO1.setIsDispatchAsPrint(false);
        deviceConfigDO1.setPrinterGuid("printerGuid");
        deviceConfigDO1.setIsInitialized(false);
        when(mockDeviceConfigMapstruct.toStatusResp(deviceConfigDO1)).thenReturn(deviceStatusRespDTO);

        // Configure DeviceConfigMapstruct.toPrdConfResp(...).
        final DevicePrdConfDTO devicePrdConfDTO2 = new DevicePrdConfDTO();
        devicePrdConfDTO2.setDisplayType(0);
        devicePrdConfDTO2.setIsItemSort(false);
        devicePrdConfDTO2.setIsItemTimeout(false);
        devicePrdConfDTO2.setIsShowHangedItem(false);
        devicePrdConfDTO2.setIsProduceHangedItem(false);
        final DeviceConfigDO deviceConfigDO2 = new DeviceConfigDO();
        deviceConfigDO2.setGuid("4315480d-f7e6-4064-98fb-6f73b12acd8d");
        deviceConfigDO2.setStoreGuid("storeGuid");
        deviceConfigDO2.setName("name");
        deviceConfigDO2.setPointMode(0);
        deviceConfigDO2.setDisplayMode(0);
        deviceConfigDO2.setDisplayType(0);
        deviceConfigDO2.setIsShowHangedItem(false);
        deviceConfigDO2.setIsProduceHangedItem(false);
        deviceConfigDO2.setIsDisplayByMaxCopied(false);
        deviceConfigDO2.setIsPrintAutomatic(false);
        deviceConfigDO2.setIsPrintPerOrder(false);
        deviceConfigDO2.setIsFilterTakeOutOrder(false);
        deviceConfigDO2.setIsDineInOrderNotice(false);
        deviceConfigDO2.setIsSnackOrderNotice(false);
        deviceConfigDO2.setIsTakeoutOrderNotice(false);
        deviceConfigDO2.setIsDispatchAsPrint(false);
        deviceConfigDO2.setPrinterGuid("printerGuid");
        deviceConfigDO2.setIsInitialized(false);
        when(mockDeviceConfigMapstruct.toPrdConfResp(deviceConfigDO2)).thenReturn(devicePrdConfDTO2);

        // Configure DeviceConfigMapstruct.toDstConfResp(...).
        final DeviceDstConfDTO deviceDstConfDTO2 = new DeviceDstConfDTO();
        deviceDstConfDTO2.setDisplayType(0);
        deviceDstConfDTO2.setIsDisplayItemUnProduced(false);
        deviceDstConfDTO2.setIsDisplayItemTimeout(false);
        deviceDstConfDTO2.setIsDispatchAsPrint(false);
        deviceDstConfDTO2.setIsDineInDispatchNotice(false);
        final DeviceConfigDO deviceConfigDO3 = new DeviceConfigDO();
        deviceConfigDO3.setGuid("4315480d-f7e6-4064-98fb-6f73b12acd8d");
        deviceConfigDO3.setStoreGuid("storeGuid");
        deviceConfigDO3.setName("name");
        deviceConfigDO3.setPointMode(0);
        deviceConfigDO3.setDisplayMode(0);
        deviceConfigDO3.setDisplayType(0);
        deviceConfigDO3.setIsShowHangedItem(false);
        deviceConfigDO3.setIsProduceHangedItem(false);
        deviceConfigDO3.setIsDisplayByMaxCopied(false);
        deviceConfigDO3.setIsPrintAutomatic(false);
        deviceConfigDO3.setIsPrintPerOrder(false);
        deviceConfigDO3.setIsFilterTakeOutOrder(false);
        deviceConfigDO3.setIsDineInOrderNotice(false);
        deviceConfigDO3.setIsSnackOrderNotice(false);
        deviceConfigDO3.setIsTakeoutOrderNotice(false);
        deviceConfigDO3.setIsDispatchAsPrint(false);
        deviceConfigDO3.setPrinterGuid("printerGuid");
        deviceConfigDO3.setIsInitialized(false);
        when(mockDeviceConfigMapstruct.toDstConfResp(deviceConfigDO3)).thenReturn(deviceDstConfDTO2);

        // Run the test
        final DeviceStatusRespDTO result = deviceConfigServiceImplUnderTest.create(deviceCreateReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockProductionPointService).reInitialize("storeGuid", "deviceId");
        verify(mockDistributeService).reInitialize("storeGuid", "deviceId");
    }

    @Test
    public void testQuery() {
        // Setup
        final DeviceQueryReqDTO deviceQueryReqDTO = new DeviceQueryReqDTO("storeGuid", "deviceId");
        final DeviceStatusRespDTO expectedResult = new DeviceStatusRespDTO();
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setDeviceId("deviceId");
        expectedResult.setName("name");
        final DevicePrdConfDTO devicePrdConfDTO = new DevicePrdConfDTO();
        expectedResult.setDevicePrdConfDTO(devicePrdConfDTO);
        final DeviceDstConfDTO deviceDstConfDTO = new DeviceDstConfDTO();
        expectedResult.setDeviceDstConfDTO(deviceDstConfDTO);

        // Configure DeviceConfigMapstruct.toStatusResp(...).
        final DeviceStatusRespDTO deviceStatusRespDTO = new DeviceStatusRespDTO();
        deviceStatusRespDTO.setStoreGuid("storeGuid");
        deviceStatusRespDTO.setDeviceId("deviceId");
        deviceStatusRespDTO.setName("name");
        final DevicePrdConfDTO devicePrdConfDTO1 = new DevicePrdConfDTO();
        deviceStatusRespDTO.setDevicePrdConfDTO(devicePrdConfDTO1);
        final DeviceDstConfDTO deviceDstConfDTO1 = new DeviceDstConfDTO();
        deviceStatusRespDTO.setDeviceDstConfDTO(deviceDstConfDTO1);
        final DeviceConfigDO deviceConfigDO = new DeviceConfigDO();
        deviceConfigDO.setGuid("4315480d-f7e6-4064-98fb-6f73b12acd8d");
        deviceConfigDO.setStoreGuid("storeGuid");
        deviceConfigDO.setName("name");
        deviceConfigDO.setPointMode(0);
        deviceConfigDO.setDisplayMode(0);
        deviceConfigDO.setDisplayType(0);
        deviceConfigDO.setIsShowHangedItem(false);
        deviceConfigDO.setIsProduceHangedItem(false);
        deviceConfigDO.setIsDisplayByMaxCopied(false);
        deviceConfigDO.setIsPrintAutomatic(false);
        deviceConfigDO.setIsPrintPerOrder(false);
        deviceConfigDO.setIsFilterTakeOutOrder(false);
        deviceConfigDO.setIsDineInOrderNotice(false);
        deviceConfigDO.setIsSnackOrderNotice(false);
        deviceConfigDO.setIsTakeoutOrderNotice(false);
        deviceConfigDO.setIsDispatchAsPrint(false);
        deviceConfigDO.setPrinterGuid("printerGuid");
        deviceConfigDO.setIsInitialized(false);
        when(mockDeviceConfigMapstruct.toStatusResp(deviceConfigDO)).thenReturn(deviceStatusRespDTO);

        // Configure DeviceConfigMapstruct.toPrdConfResp(...).
        final DevicePrdConfDTO devicePrdConfDTO2 = new DevicePrdConfDTO();
        devicePrdConfDTO2.setDisplayType(0);
        devicePrdConfDTO2.setIsItemSort(false);
        devicePrdConfDTO2.setIsItemTimeout(false);
        devicePrdConfDTO2.setIsShowHangedItem(false);
        devicePrdConfDTO2.setIsProduceHangedItem(false);
        final DeviceConfigDO deviceConfigDO1 = new DeviceConfigDO();
        deviceConfigDO1.setGuid("4315480d-f7e6-4064-98fb-6f73b12acd8d");
        deviceConfigDO1.setStoreGuid("storeGuid");
        deviceConfigDO1.setName("name");
        deviceConfigDO1.setPointMode(0);
        deviceConfigDO1.setDisplayMode(0);
        deviceConfigDO1.setDisplayType(0);
        deviceConfigDO1.setIsShowHangedItem(false);
        deviceConfigDO1.setIsProduceHangedItem(false);
        deviceConfigDO1.setIsDisplayByMaxCopied(false);
        deviceConfigDO1.setIsPrintAutomatic(false);
        deviceConfigDO1.setIsPrintPerOrder(false);
        deviceConfigDO1.setIsFilterTakeOutOrder(false);
        deviceConfigDO1.setIsDineInOrderNotice(false);
        deviceConfigDO1.setIsSnackOrderNotice(false);
        deviceConfigDO1.setIsTakeoutOrderNotice(false);
        deviceConfigDO1.setIsDispatchAsPrint(false);
        deviceConfigDO1.setPrinterGuid("printerGuid");
        deviceConfigDO1.setIsInitialized(false);
        when(mockDeviceConfigMapstruct.toPrdConfResp(deviceConfigDO1)).thenReturn(devicePrdConfDTO2);

        // Configure DeviceConfigMapstruct.toDstConfResp(...).
        final DeviceDstConfDTO deviceDstConfDTO2 = new DeviceDstConfDTO();
        deviceDstConfDTO2.setDisplayType(0);
        deviceDstConfDTO2.setIsDisplayItemUnProduced(false);
        deviceDstConfDTO2.setIsDisplayItemTimeout(false);
        deviceDstConfDTO2.setIsDispatchAsPrint(false);
        deviceDstConfDTO2.setIsDineInDispatchNotice(false);
        final DeviceConfigDO deviceConfigDO2 = new DeviceConfigDO();
        deviceConfigDO2.setGuid("4315480d-f7e6-4064-98fb-6f73b12acd8d");
        deviceConfigDO2.setStoreGuid("storeGuid");
        deviceConfigDO2.setName("name");
        deviceConfigDO2.setPointMode(0);
        deviceConfigDO2.setDisplayMode(0);
        deviceConfigDO2.setDisplayType(0);
        deviceConfigDO2.setIsShowHangedItem(false);
        deviceConfigDO2.setIsProduceHangedItem(false);
        deviceConfigDO2.setIsDisplayByMaxCopied(false);
        deviceConfigDO2.setIsPrintAutomatic(false);
        deviceConfigDO2.setIsPrintPerOrder(false);
        deviceConfigDO2.setIsFilterTakeOutOrder(false);
        deviceConfigDO2.setIsDineInOrderNotice(false);
        deviceConfigDO2.setIsSnackOrderNotice(false);
        deviceConfigDO2.setIsTakeoutOrderNotice(false);
        deviceConfigDO2.setIsDispatchAsPrint(false);
        deviceConfigDO2.setPrinterGuid("printerGuid");
        deviceConfigDO2.setIsInitialized(false);
        when(mockDeviceConfigMapstruct.toDstConfResp(deviceConfigDO2)).thenReturn(deviceDstConfDTO2);

        // Run the test
        final DeviceStatusRespDTO result = deviceConfigServiceImplUnderTest.query(deviceQueryReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testInitialize() {
        // Setup
        final DeviceQueryReqDTO deviceQueryReqDTO = new DeviceQueryReqDTO("storeGuid", "deviceId");

        // Run the test
        deviceConfigServiceImplUnderTest.initialize(deviceQueryReqDTO);

        // Verify the results
        verify(mockProductionPointService).reInitialize("storeGuid", "deviceId");
        verify(mockDistributeService).reInitialize("storeGuid", "deviceId");
    }

    @Test
    public void testAssertThatDeviceExists() {
        // Setup
        // Run the test
        deviceConfigServiceImplUnderTest.assertThatDeviceExists("storeGuid", "deviceId");

        // Verify the results
    }

    @Test
    public void testQueryDeviceByGuid() {
        // Setup
        final DeviceConfigDO expectedResult = new DeviceConfigDO();
        expectedResult.setGuid("4315480d-f7e6-4064-98fb-6f73b12acd8d");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setName("name");
        expectedResult.setPointMode(0);
        expectedResult.setDisplayMode(0);
        expectedResult.setDisplayType(0);
        expectedResult.setIsShowHangedItem(false);
        expectedResult.setIsProduceHangedItem(false);
        expectedResult.setIsDisplayByMaxCopied(false);
        expectedResult.setIsPrintAutomatic(false);
        expectedResult.setIsPrintPerOrder(false);
        expectedResult.setIsFilterTakeOutOrder(false);
        expectedResult.setIsDineInOrderNotice(false);
        expectedResult.setIsSnackOrderNotice(false);
        expectedResult.setIsTakeoutOrderNotice(false);
        expectedResult.setIsDispatchAsPrint(false);
        expectedResult.setPrinterGuid("printerGuid");
        expectedResult.setIsInitialized(false);

        // Run the test
        final DeviceConfigDO result = deviceConfigServiceImplUnderTest.queryDeviceByGuid("storeGuid", "deviceId");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryPrdDeviceByGuid() {
        // Setup
        final DeviceConfigDO expectedResult = new DeviceConfigDO();
        expectedResult.setGuid("4315480d-f7e6-4064-98fb-6f73b12acd8d");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setName("name");
        expectedResult.setPointMode(0);
        expectedResult.setDisplayMode(0);
        expectedResult.setDisplayType(0);
        expectedResult.setIsShowHangedItem(false);
        expectedResult.setIsProduceHangedItem(false);
        expectedResult.setIsDisplayByMaxCopied(false);
        expectedResult.setIsPrintAutomatic(false);
        expectedResult.setIsPrintPerOrder(false);
        expectedResult.setIsFilterTakeOutOrder(false);
        expectedResult.setIsDineInOrderNotice(false);
        expectedResult.setIsSnackOrderNotice(false);
        expectedResult.setIsTakeoutOrderNotice(false);
        expectedResult.setIsDispatchAsPrint(false);
        expectedResult.setPrinterGuid("printerGuid");
        expectedResult.setIsInitialized(false);

        // Run the test
        final DeviceConfigDO result = deviceConfigServiceImplUnderTest.queryPrdDeviceByGuid("storeGuid", "deviceId");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testListPrdDeviceByGuid() {
        // Setup
        final Map<String, DeviceConfigDO> expectedResult = new HashMap<>();

        // Run the test
        final Map<String, DeviceConfigDO> result = deviceConfigServiceImplUnderTest.listPrdDeviceByGuid("storeGuid",
                Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryDstDispatchAsPrintByGuid() {
        // Setup
        final DeviceConfigDO expectedResult = new DeviceConfigDO();
        expectedResult.setGuid("4315480d-f7e6-4064-98fb-6f73b12acd8d");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setName("name");
        expectedResult.setPointMode(0);
        expectedResult.setDisplayMode(0);
        expectedResult.setDisplayType(0);
        expectedResult.setIsShowHangedItem(false);
        expectedResult.setIsProduceHangedItem(false);
        expectedResult.setIsDisplayByMaxCopied(false);
        expectedResult.setIsPrintAutomatic(false);
        expectedResult.setIsPrintPerOrder(false);
        expectedResult.setIsFilterTakeOutOrder(false);
        expectedResult.setIsDineInOrderNotice(false);
        expectedResult.setIsSnackOrderNotice(false);
        expectedResult.setIsTakeoutOrderNotice(false);
        expectedResult.setIsDispatchAsPrint(false);
        expectedResult.setPrinterGuid("printerGuid");
        expectedResult.setIsInitialized(false);

        // Run the test
        final DeviceConfigDO result = deviceConfigServiceImplUnderTest.queryDstDispatchAsPrintByGuid("storeGuid",
                "deviceId");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryDstDisplayTypeByGuid() {
        // Setup
        final DeviceConfigDO expectedResult = new DeviceConfigDO();
        expectedResult.setGuid("4315480d-f7e6-4064-98fb-6f73b12acd8d");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setName("name");
        expectedResult.setPointMode(0);
        expectedResult.setDisplayMode(0);
        expectedResult.setDisplayType(0);
        expectedResult.setIsShowHangedItem(false);
        expectedResult.setIsProduceHangedItem(false);
        expectedResult.setIsDisplayByMaxCopied(false);
        expectedResult.setIsPrintAutomatic(false);
        expectedResult.setIsPrintPerOrder(false);
        expectedResult.setIsFilterTakeOutOrder(false);
        expectedResult.setIsDineInOrderNotice(false);
        expectedResult.setIsSnackOrderNotice(false);
        expectedResult.setIsTakeoutOrderNotice(false);
        expectedResult.setIsDispatchAsPrint(false);
        expectedResult.setPrinterGuid("printerGuid");
        expectedResult.setIsInitialized(false);

        // Run the test
        final DeviceConfigDO result = deviceConfigServiceImplUnderTest.queryDstDisplayTypeByGuid("storeGuid",
                "deviceId");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testListDeviceOfStore() {
        // Setup
        final DeviceConfigDO deviceConfigDO = new DeviceConfigDO();
        deviceConfigDO.setGuid("4315480d-f7e6-4064-98fb-6f73b12acd8d");
        deviceConfigDO.setStoreGuid("storeGuid");
        deviceConfigDO.setName("name");
        deviceConfigDO.setPointMode(0);
        deviceConfigDO.setDisplayMode(0);
        deviceConfigDO.setDisplayType(0);
        deviceConfigDO.setIsShowHangedItem(false);
        deviceConfigDO.setIsProduceHangedItem(false);
        deviceConfigDO.setIsDisplayByMaxCopied(false);
        deviceConfigDO.setIsPrintAutomatic(false);
        deviceConfigDO.setIsPrintPerOrder(false);
        deviceConfigDO.setIsFilterTakeOutOrder(false);
        deviceConfigDO.setIsDineInOrderNotice(false);
        deviceConfigDO.setIsSnackOrderNotice(false);
        deviceConfigDO.setIsTakeoutOrderNotice(false);
        deviceConfigDO.setIsDispatchAsPrint(false);
        deviceConfigDO.setPrinterGuid("printerGuid");
        deviceConfigDO.setIsInitialized(false);
        final List<DeviceConfigDO> expectedResult = Arrays.asList(deviceConfigDO);

        // Run the test
        final List<DeviceConfigDO> result = deviceConfigServiceImplUnderTest.listDeviceOfStore("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetDeviceNameMapOfStore() {
        // Setup
        final Map<String, String> expectedResult = new HashMap<>();

        // Run the test
        final Map<String, String> result = deviceConfigServiceImplUnderTest.getDeviceNameMapOfStore("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testUpdateBasic() {
        // Setup
        // Run the test
        deviceConfigServiceImplUnderTest.updateBasic("storeGuid", "deviceId", 0, 0);

        // Verify the results
    }

    @Test
    public void testUpdatePrdAdvanced() {
        // Setup
        final DevicePrdConfDTO devicePrdConfDTO = new DevicePrdConfDTO();
        devicePrdConfDTO.setDisplayType(0);
        devicePrdConfDTO.setIsItemSort(false);
        devicePrdConfDTO.setIsItemTimeout(false);
        devicePrdConfDTO.setIsShowHangedItem(false);
        devicePrdConfDTO.setIsProduceHangedItem(false);

        // Configure DeviceConfigMapstruct.fromDevicePrdConfReq(...).
        final DeviceConfigDO deviceConfigDO = new DeviceConfigDO();
        deviceConfigDO.setGuid("4315480d-f7e6-4064-98fb-6f73b12acd8d");
        deviceConfigDO.setStoreGuid("storeGuid");
        deviceConfigDO.setName("name");
        deviceConfigDO.setPointMode(0);
        deviceConfigDO.setDisplayMode(0);
        deviceConfigDO.setDisplayType(0);
        deviceConfigDO.setIsShowHangedItem(false);
        deviceConfigDO.setIsProduceHangedItem(false);
        deviceConfigDO.setIsDisplayByMaxCopied(false);
        deviceConfigDO.setIsPrintAutomatic(false);
        deviceConfigDO.setIsPrintPerOrder(false);
        deviceConfigDO.setIsFilterTakeOutOrder(false);
        deviceConfigDO.setIsDineInOrderNotice(false);
        deviceConfigDO.setIsSnackOrderNotice(false);
        deviceConfigDO.setIsTakeoutOrderNotice(false);
        deviceConfigDO.setIsDispatchAsPrint(false);
        deviceConfigDO.setPrinterGuid("printerGuid");
        deviceConfigDO.setIsInitialized(false);
        final DevicePrdConfDTO devicePrdConfDTO1 = new DevicePrdConfDTO();
        devicePrdConfDTO1.setDisplayType(0);
        devicePrdConfDTO1.setIsItemSort(false);
        devicePrdConfDTO1.setIsItemTimeout(false);
        devicePrdConfDTO1.setIsShowHangedItem(false);
        devicePrdConfDTO1.setIsProduceHangedItem(false);
        when(mockDeviceConfigMapstruct.fromDevicePrdConfReq(devicePrdConfDTO1)).thenReturn(deviceConfigDO);

        // Run the test
        deviceConfigServiceImplUnderTest.updatePrdAdvanced("storeGuid", "deviceId", devicePrdConfDTO);

        // Verify the results
    }

    @Test
    public void testUpdateDstAdvanced() {
        // Setup
        final DeviceDstConfDTO deviceDstConfDTO = new DeviceDstConfDTO();
        deviceDstConfDTO.setDisplayType(0);
        deviceDstConfDTO.setIsDisplayItemUnProduced(false);
        deviceDstConfDTO.setIsDisplayItemTimeout(false);
        deviceDstConfDTO.setIsDispatchAsPrint(false);
        deviceDstConfDTO.setIsDineInDispatchNotice(false);

        // Configure DeviceConfigMapstruct.fromDeviceDstConfReq(...).
        final DeviceConfigDO deviceConfigDO = new DeviceConfigDO();
        deviceConfigDO.setGuid("4315480d-f7e6-4064-98fb-6f73b12acd8d");
        deviceConfigDO.setStoreGuid("storeGuid");
        deviceConfigDO.setName("name");
        deviceConfigDO.setPointMode(0);
        deviceConfigDO.setDisplayMode(0);
        deviceConfigDO.setDisplayType(0);
        deviceConfigDO.setIsShowHangedItem(false);
        deviceConfigDO.setIsProduceHangedItem(false);
        deviceConfigDO.setIsDisplayByMaxCopied(false);
        deviceConfigDO.setIsPrintAutomatic(false);
        deviceConfigDO.setIsPrintPerOrder(false);
        deviceConfigDO.setIsFilterTakeOutOrder(false);
        deviceConfigDO.setIsDineInOrderNotice(false);
        deviceConfigDO.setIsSnackOrderNotice(false);
        deviceConfigDO.setIsTakeoutOrderNotice(false);
        deviceConfigDO.setIsDispatchAsPrint(false);
        deviceConfigDO.setPrinterGuid("printerGuid");
        deviceConfigDO.setIsInitialized(false);
        final DeviceDstConfDTO deviceDstConfDTO1 = new DeviceDstConfDTO();
        deviceDstConfDTO1.setDisplayType(0);
        deviceDstConfDTO1.setIsDisplayItemUnProduced(false);
        deviceDstConfDTO1.setIsDisplayItemTimeout(false);
        deviceDstConfDTO1.setIsDispatchAsPrint(false);
        deviceDstConfDTO1.setIsDineInDispatchNotice(false);
        when(mockDeviceConfigMapstruct.fromDeviceDstConfReq(deviceDstConfDTO1)).thenReturn(deviceConfigDO);

        // Run the test
        deviceConfigServiceImplUnderTest.updateDstAdvanced("storeGuid", "deviceId", deviceDstConfDTO);

        // Verify the results
    }

    @Test
    public void testQueryBoundPrinterGuidByDeviceId() {
        assertThat(deviceConfigServiceImplUnderTest.queryBoundPrinterGuidByDeviceId("storeGuid", "deviceId"))
                .isEqualTo("printerGuid");
    }

    @Test
    public void testBindPrinter() {
        // Setup
        final KdsPrinterBindUnbindReqDTO kdsPrinterBindUnbindReqDTO = new KdsPrinterBindUnbindReqDTO();
        kdsPrinterBindUnbindReqDTO.setStoreGuid("storeGuid");
        kdsPrinterBindUnbindReqDTO.setDeviceId("deviceId");
        kdsPrinterBindUnbindReqDTO.setPrinterGuid("printerGuid");

        // Run the test
        deviceConfigServiceImplUnderTest.bindPrinter(kdsPrinterBindUnbindReqDTO);

        // Verify the results
    }

    @Test
    public void testRebindPrinter() {
        // Setup
        final KdsPrinterBindUnbindReqDTO kdsPrinterBindUnbindReqDTO = new KdsPrinterBindUnbindReqDTO();
        kdsPrinterBindUnbindReqDTO.setStoreGuid("storeGuid");
        kdsPrinterBindUnbindReqDTO.setDeviceId("deviceId");
        kdsPrinterBindUnbindReqDTO.setPrinterGuid("printerGuid");

        // Run the test
        deviceConfigServiceImplUnderTest.rebindPrinter(kdsPrinterBindUnbindReqDTO);

        // Verify the results
    }

    @Test
    public void testUnbindPrinter1() {
        // Setup
        final KdsPrinterBindUnbindReqDTO kdsPrinterBindUnbindReqDTO = new KdsPrinterBindUnbindReqDTO();
        kdsPrinterBindUnbindReqDTO.setStoreGuid("storeGuid");
        kdsPrinterBindUnbindReqDTO.setDeviceId("deviceId");
        kdsPrinterBindUnbindReqDTO.setPrinterGuid("printerGuid");

        // Run the test
        deviceConfigServiceImplUnderTest.unbindPrinter(kdsPrinterBindUnbindReqDTO);

        // Verify the results
    }

    @Test
    public void testUnbindPrinter2() {
        // Setup
        // Run the test
        deviceConfigServiceImplUnderTest.unbindPrinter("printerGuid");

        // Verify the results
    }
}
