package com.holderzone.saas.store.kds.service.impl;

import com.google.common.util.concurrent.MoreExecutors;
import com.holderzone.framework.base.dto.message.MessageDTO;
import com.holderzone.saas.store.kds.service.rpc.MsgRpcService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;

@RunWith(MockitoJUnitRunner.class)
public class KdsNotificationServiceImplTest {

    @Mock
    private MsgRpcService mockMsgRpcService;

    private KdsNotificationServiceImpl kdsNotificationServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        kdsNotificationServiceImplUnderTest = new KdsNotificationServiceImpl(MoreExecutors.newDirectExecutorService(),
                mockMsgRpcService);
    }

    @Test
    public void testSendMessage() {
        // Setup
        // Run the test
        kdsNotificationServiceImplUnderTest.sendMessage("enterpriseGuid", "storeGuid", "deviceId", "topic", "message");

        // Verify the results
        verify(mockMsgRpcService).sendPrintMessage(any(MessageDTO.class));
    }
}
