package com.holderzone.saas.store.kds.service.impl;

import com.holderzone.saas.store.dto.kds.req.QueueConfigDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceDTO;
import com.holderzone.saas.store.kds.entity.domain.QueueConfigDO;
import com.holderzone.saas.store.kds.mapstruct.QueueMapstruct;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class QueueConfigServiceImplTest {

    @Mock
    private QueueMapstruct mockQueueMapstruct;

    private QueueConfigServiceImpl queueConfigServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        queueConfigServiceImplUnderTest = new QueueConfigServiceImpl(mockQueueMapstruct);
    }

    @Test
    public void testCreate() {
        // Setup
        final StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO();
        storeDeviceDTO.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO.setStoreNo("storeNo");
        storeDeviceDTO.setStoreGuid("storeGuid");
        storeDeviceDTO.setStoreName("storeName");
        storeDeviceDTO.setDeviceGuid("79ac2644-1bb1-4133-b21a-4b602519bcaf");

        // Run the test
        queueConfigServiceImplUnderTest.create(storeDeviceDTO);

        // Verify the results
    }

    @Test
    public void testUpdate() {
        // Setup
        final QueueConfigDTO queueConfigDTO = new QueueConfigDTO();
        queueConfigDTO.setGuid("79ac2644-1bb1-4133-b21a-4b602519bcaf");
        queueConfigDTO.setStoreGuid("storeGuid");
        queueConfigDTO.setSource(0);
        queueConfigDTO.setContent(0);
        queueConfigDTO.setLayout(0);

        // Configure QueueMapstruct.fromDTO(...).
        final QueueConfigDO queueConfigDO = new QueueConfigDO();
        queueConfigDO.setId(0L);
        queueConfigDO.setGuid("79ac2644-1bb1-4133-b21a-4b602519bcaf");
        queueConfigDO.setStoreGuid("storeGuid");
        queueConfigDO.setSource(0);
        queueConfigDO.setContent(0);
        final QueueConfigDTO queueConfigDTO1 = new QueueConfigDTO();
        queueConfigDTO1.setGuid("79ac2644-1bb1-4133-b21a-4b602519bcaf");
        queueConfigDTO1.setStoreGuid("storeGuid");
        queueConfigDTO1.setSource(0);
        queueConfigDTO1.setContent(0);
        queueConfigDTO1.setLayout(0);
        when(mockQueueMapstruct.fromDTO(queueConfigDTO1)).thenReturn(queueConfigDO);

        // Run the test
        queueConfigServiceImplUnderTest.update(queueConfigDTO);

        // Verify the results
    }

    @Test
    public void testQuery() {
        // Setup
        final QueueConfigDTO queueConfigDTO = new QueueConfigDTO();
        queueConfigDTO.setGuid("79ac2644-1bb1-4133-b21a-4b602519bcaf");
        queueConfigDTO.setStoreGuid("storeGuid");
        queueConfigDTO.setSource(0);
        queueConfigDTO.setContent(0);
        queueConfigDTO.setLayout(0);

        final QueueConfigDTO expectedResult = new QueueConfigDTO();
        expectedResult.setGuid("79ac2644-1bb1-4133-b21a-4b602519bcaf");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setSource(0);
        expectedResult.setContent(0);
        expectedResult.setLayout(0);

        // Configure QueueMapstruct.doToDTO(...).
        final QueueConfigDTO queueConfigDTO1 = new QueueConfigDTO();
        queueConfigDTO1.setGuid("79ac2644-1bb1-4133-b21a-4b602519bcaf");
        queueConfigDTO1.setStoreGuid("storeGuid");
        queueConfigDTO1.setSource(0);
        queueConfigDTO1.setContent(0);
        queueConfigDTO1.setLayout(0);
        final QueueConfigDO queueConfigDO = new QueueConfigDO();
        queueConfigDO.setId(0L);
        queueConfigDO.setGuid("79ac2644-1bb1-4133-b21a-4b602519bcaf");
        queueConfigDO.setStoreGuid("storeGuid");
        queueConfigDO.setSource(0);
        queueConfigDO.setContent(0);
        when(mockQueueMapstruct.doToDTO(queueConfigDO)).thenReturn(queueConfigDTO1);

        // Run the test
        final QueueConfigDTO result = queueConfigServiceImplUnderTest.query(queueConfigDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testDelete() {
        // Setup
        final StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO();
        storeDeviceDTO.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO.setStoreNo("storeNo");
        storeDeviceDTO.setStoreGuid("storeGuid");
        storeDeviceDTO.setStoreName("storeName");
        storeDeviceDTO.setDeviceGuid("79ac2644-1bb1-4133-b21a-4b602519bcaf");

        // Run the test
        queueConfigServiceImplUnderTest.delete(storeDeviceDTO);

        // Verify the results
    }
}
