package com.holderzone.saas.store.kds.service.impl;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.Arrays;
import java.util.Collections;

import static org.assertj.core.api.Assertions.assertThat;

@RunWith(MockitoJUnitRunner.class)
public class DistributedIdServiceImplTest {

    @Mock
    private RedisTemplate mockRedisTemplate;

    private DistributedIdServiceImpl distributedIdServiceImplUnderTest;

    @Before
    public void setUp() {
        distributedIdServiceImplUnderTest = new DistributedIdServiceImpl(mockRedisTemplate);
    }

    @Test
    public void testRawId() {
        assertThat(distributedIdServiceImplUnderTest.rawId("tag")).isEqualTo(0L);
    }

    @Test
    public void testNextId() {
        assertThat(distributedIdServiceImplUnderTest.nextId("tag")).isEqualTo("result");
    }

    @Test
    public void testNextBatchId() {
        assertThat(distributedIdServiceImplUnderTest.nextBatchId("tag", 0L)).isEqualTo(Arrays.asList("value"));
        assertThat(distributedIdServiceImplUnderTest.nextBatchId("tag", 0L)).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testNextPrdPointGuid() {
        assertThat(distributedIdServiceImplUnderTest.nextPrdPointGuid()).isEqualTo("result");
    }

    @Test
    public void testNextBatchPointItemGuid() {
        assertThat(distributedIdServiceImplUnderTest.nextBatchPointItemGuid(0L)).isEqualTo(Arrays.asList("value"));
        assertThat(distributedIdServiceImplUnderTest.nextBatchPointItemGuid(0L)).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testNextPrinterGuid() {
        assertThat(distributedIdServiceImplUnderTest.nextPrinterGuid()).isEqualTo("result");
    }

    @Test
    public void testNextBatchDstAreaGuid() {
        assertThat(distributedIdServiceImplUnderTest.nextBatchDstAreaGuid(0L)).isEqualTo(Arrays.asList("value"));
        assertThat(distributedIdServiceImplUnderTest.nextBatchDstAreaGuid(0L)).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testNextBatchDstItemGuid() {
        assertThat(distributedIdServiceImplUnderTest.nextBatchDstItemGuid(0L)).isEqualTo(Arrays.asList("value"));
        assertThat(distributedIdServiceImplUnderTest.nextBatchDstItemGuid(0L)).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testNextPrintRecordGuid() {
        assertThat(distributedIdServiceImplUnderTest.nextPrintRecordGuid()).isEqualTo("result");
    }

    @Test
    public void testNextBatchPrintRecordGuid() {
        assertThat(distributedIdServiceImplUnderTest.nextBatchPrintRecordGuid(0L)).isEqualTo(Arrays.asList("value"));
        assertThat(distributedIdServiceImplUnderTest.nextBatchPrintRecordGuid(0L)).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testNextKitchenItemGuid() {
        assertThat(distributedIdServiceImplUnderTest.nextKitchenItemGuid()).isEqualTo("result");
    }

    @Test
    public void testNextBatchKitchenItemGuid() {
        assertThat(distributedIdServiceImplUnderTest.nextBatchKitchenItemGuid(0L)).isEqualTo(Arrays.asList("value"));
        assertThat(distributedIdServiceImplUnderTest.nextBatchKitchenItemGuid(0L)).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testNextBatchKitchenAttrGuid() {
        assertThat(distributedIdServiceImplUnderTest.nextBatchKitchenAttrGuid(0L)).isEqualTo(Arrays.asList("value"));
        assertThat(distributedIdServiceImplUnderTest.nextBatchKitchenAttrGuid(0L)).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testNextQueueItemGuid() {
        assertThat(distributedIdServiceImplUnderTest.nextQueueItemGuid()).isEqualTo("result");
    }

    @Test
    public void testNextDisplayRuleGuid() {
        assertThat(distributedIdServiceImplUnderTest.nextDisplayRuleGuid()).isEqualTo("result");
    }

    @Test
    public void testNextBatchDisplayItemGuid() {
        assertThat(distributedIdServiceImplUnderTest.nextBatchDisplayItemGuid(0L)).isEqualTo(Arrays.asList("value"));
        assertThat(distributedIdServiceImplUnderTest.nextBatchDisplayItemGuid(0L)).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testNextBatchDisplayStoreGuid() {
        assertThat(distributedIdServiceImplUnderTest.nextBatchDisplayStoreGuid(0L)).isEqualTo(Arrays.asList("value"));
        assertThat(distributedIdServiceImplUnderTest.nextBatchDisplayStoreGuid(0L)).isEqualTo(Collections.emptyList());
    }
}
