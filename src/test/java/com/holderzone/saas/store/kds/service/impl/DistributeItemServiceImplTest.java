package com.holderzone.saas.store.kds.service.impl;

import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.kds.req.DeviceQueryReqDTO;
import com.holderzone.saas.store.dto.kds.req.PrdDstItemBindDTO;
import com.holderzone.saas.store.dto.kds.resp.DistributeItemDTO;
import com.holderzone.saas.store.dto.kds.resp.DstBindStatusRespDTO;
import com.holderzone.saas.store.kds.entity.domain.DistributeItemDO;
import com.holderzone.saas.store.kds.service.DistributedIdService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class DistributeItemServiceImplTest {

    @Mock
    private DistributedIdService mockDistributedIdService;

    private DistributeItemServiceImpl distributeItemServiceImplUnderTest;

    @Before
    public void setUp() {
        distributeItemServiceImplUnderTest = new DistributeItemServiceImpl(mockDistributedIdService);
    }

    @Test
    public void testQueryItemBindingPreview() {
        // Setup
        final DeviceQueryReqDTO deviceQueryReqDTO = new DeviceQueryReqDTO("storeGuid", "deviceId");
        final DstBindStatusRespDTO expectedResult = new DstBindStatusRespDTO();
        expectedResult.setBoundAreaCount(0);
        expectedResult.setIsSnackBound(false);
        expectedResult.setIsTakeoutBound(false);
        expectedResult.setBoundItemCount(0);

        // Run the test
        final DstBindStatusRespDTO result = distributeItemServiceImplUnderTest.queryItemBindingPreview(
                deviceQueryReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryBoundItemOfStore() {
        // Setup
        final DistributeItemDO distributeItemDO = new DistributeItemDO();
        distributeItemDO.setGuid("cfe2ea5b-b9eb-4ff3-bd74-6b99b9c51859");
        distributeItemDO.setStoreGuid("storeGuid");
        distributeItemDO.setDeviceId("deviceId");
        distributeItemDO.setItemGuid("itemGuid");
        distributeItemDO.setSkuGuid("skuGuid");
        final List<DistributeItemDO> expectedResult = Arrays.asList(distributeItemDO);

        // Run the test
        final List<DistributeItemDO> result = distributeItemServiceImplUnderTest.queryBoundItemOfStore("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryBoundItemOfDevice() {
        // Setup
        final DistributeItemDO distributeItemDO = new DistributeItemDO();
        distributeItemDO.setGuid("cfe2ea5b-b9eb-4ff3-bd74-6b99b9c51859");
        distributeItemDO.setStoreGuid("storeGuid");
        distributeItemDO.setDeviceId("deviceId");
        distributeItemDO.setItemGuid("itemGuid");
        distributeItemDO.setSkuGuid("skuGuid");
        final List<DistributeItemDO> expectedResult = Arrays.asList(distributeItemDO);

        // Run the test
        final List<DistributeItemDO> result = distributeItemServiceImplUnderTest.queryBoundItemOfDevice("storeGuid",
                "deviceId");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryOccupiedDeviceId() {
        assertThat(distributeItemServiceImplUnderTest.queryOccupiedDeviceId("storeGuid", "deviceId"))
                .isEqualTo(Arrays.asList("value"));
        assertThat(distributeItemServiceImplUnderTest.queryOccupiedDeviceId("storeGuid", "deviceId"))
                .isEqualTo(Collections.emptyList());
    }

    @Test
    public void testQueryBoundSkuGuidOfDevice() {
        assertThat(distributeItemServiceImplUnderTest.queryBoundSkuGuidOfDevice("storeGuid", "deviceId"))
                .isEqualTo(Arrays.asList("value"));
        assertThat(distributeItemServiceImplUnderTest.queryBoundSkuGuidOfDevice("storeGuid", "deviceId"))
                .isEqualTo(Collections.emptyList());
    }

    @Test
    public void testQueryBoundSkuGuidOfDeviceList() {
        assertThat(distributeItemServiceImplUnderTest.queryBoundSkuGuidOfDeviceList("storeGuid",
                Arrays.asList("value"))).isEqualTo(Arrays.asList("value"));
        assertThat(distributeItemServiceImplUnderTest.queryBoundSkuGuidOfDeviceList("storeGuid",
                Arrays.asList("value"))).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testSimpleSaveBatchSku() {
        // Setup
        final PrdDstItemBindDTO prdDstItemBindDTO = new PrdDstItemBindDTO();
        prdDstItemBindDTO.setItemGuid("itemGuid");
        prdDstItemBindDTO.setSkuGuid("skuGuid");
        final List<PrdDstItemBindDTO> toBeBoundSkuList = Arrays.asList(prdDstItemBindDTO);
        when(mockDistributedIdService.nextBatchDstItemGuid(0L)).thenReturn(Arrays.asList("value"));

        // Run the test
        distributeItemServiceImplUnderTest.simpleSaveBatchSku("storeGuid", "deviceId", toBeBoundSkuList);

        // Verify the results
    }

    @Test
    public void testSimpleSaveBatchSku_DistributedIdServiceReturnsNoItems() {
        // Setup
        final PrdDstItemBindDTO prdDstItemBindDTO = new PrdDstItemBindDTO();
        prdDstItemBindDTO.setItemGuid("itemGuid");
        prdDstItemBindDTO.setSkuGuid("skuGuid");
        final List<PrdDstItemBindDTO> toBeBoundSkuList = Arrays.asList(prdDstItemBindDTO);
        when(mockDistributedIdService.nextBatchDstItemGuid(0L)).thenReturn(Collections.emptyList());

        // Run the test
        distributeItemServiceImplUnderTest.simpleSaveBatchSku("storeGuid", "deviceId", toBeBoundSkuList);

        // Verify the results
    }

    @Test
    public void testSimpleRemoveBatchSku() {
        // Setup
        // Run the test
        distributeItemServiceImplUnderTest.simpleRemoveBatchSku("storeGuid", "deviceId", Arrays.asList("value"));

        // Verify the results
    }

    @Test
    public void testReInitialize() {
        // Setup
        // Run the test
        distributeItemServiceImplUnderTest.reInitialize("storeGuid", "deviceId");

        // Verify the results
    }

    @Test
    public void testQueryDstSkuMap() {
        // Setup
        final Map<String, List<String>> expectedResult = new HashMap<>();

        // Run the test
        final Map<String, List<String>> result = distributeItemServiceImplUnderTest.queryDstSkuMap("storeGuid",
                Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryDistributeItemBySku() {
        // Setup
        final SingleDataDTO reqDTO = new SingleDataDTO("data", Arrays.asList("value"));
        final DistributeItemDTO distributeItemDTO = new DistributeItemDTO();
        distributeItemDTO.setStoreGuid("storeGuid");
        distributeItemDTO.setDeviceId("deviceId");
        distributeItemDTO.setItemGuid("itemGuid");
        distributeItemDTO.setSkuGuid("skuGuid");
        final List<DistributeItemDTO> expectedResult = Arrays.asList(distributeItemDTO);

        // Run the test
        final List<DistributeItemDTO> result = distributeItemServiceImplUnderTest.queryDistributeItemBySku(reqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
