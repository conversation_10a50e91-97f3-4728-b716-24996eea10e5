package com.holderzone.saas.store.kds.service.print.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.util.concurrent.MoreExecutors;
import com.holderzone.saas.store.dto.kds.req.*;
import com.holderzone.saas.store.dto.kds.resp.KdsPrintOrderDTO;
import com.holderzone.saas.store.dto.kds.resp.KdsPrintRecordDTO;
import com.holderzone.saas.store.dto.kds.resp.PrdDstItemDTO;
import com.holderzone.saas.store.dto.kds.resp.PrdDstItemTableDTO;
import com.holderzone.saas.store.dto.print.template.PrintRow;
import com.holderzone.saas.store.kds.entity.domain.DeviceConfigDO;
import com.holderzone.saas.store.kds.entity.domain.KdsPrintRecordDO;
import com.holderzone.saas.store.kds.entity.domain.KdsPrinterDO;
import com.holderzone.saas.store.kds.entity.query.PrintRecordQuery;
import com.holderzone.saas.store.kds.entity.read.KdsPrintRecordReadDO;
import com.holderzone.saas.store.kds.mapstruct.KdsPrintMapstruct;
import com.holderzone.saas.store.kds.mapstruct.KitchenItemMapstruct;
import com.holderzone.saas.store.kds.service.DeviceConfigService;
import com.holderzone.saas.store.kds.service.DistributedIdService;
import com.holderzone.saas.store.kds.service.KitchenAssociatedOrderService;
import com.holderzone.saas.store.kds.service.print.KdsInvoiceTypeEnum;
import com.holderzone.saas.store.kds.service.print.KdsPrintCacheService;
import com.holderzone.saas.store.kds.service.print.KdsPrintPushService;
import com.holderzone.saas.store.kds.service.print.KdsPrinterService;
import com.holderzone.saas.store.kds.service.template.PrintComponentFactory;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class KdsPrintRecordServiceImplTest {

    @Mock
    private KdsPrinterService mockKdsPrinterService;
    @Mock
    private KdsPrintCacheService mockKdsPrintCacheService;
    @Mock
    private KdsPrintPushService mockKdsPrintPushService;
    @Mock
    private KdsPrintMapstruct mockKdsPrintMapstruct;
    @Mock
    private KitchenItemMapstruct mockKitchenItemMapstruct;
    @Mock
    private DeviceConfigService mockDeviceConfigService;
    @Mock
    private DistributedIdService mockDistributedIdService;
    @Mock
    private PrintComponentFactory mockPrintComponentFactory;
    @Mock
    private KitchenAssociatedOrderService kitchenAssociatedOrderService;

    private KdsPrintRecordServiceImpl kdsPrintRecordServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        kdsPrintRecordServiceImplUnderTest = new KdsPrintRecordServiceImpl(mockKdsPrinterService,
                mockKdsPrintCacheService, mockKdsPrintPushService, mockKdsPrintMapstruct, mockKitchenItemMapstruct,
                mockDeviceConfigService, mockDistributedIdService, MoreExecutors.newDirectExecutorService(),
                mockPrintComponentFactory, kitchenAssociatedOrderService);
    }

    @Test
    public void testPrintSingleItem() {
        // Setup
        final PrdDstItemDTO prdDstItemDTO = new PrdDstItemDTO();
        prdDstItemDTO.setIsWeight(false);
        prdDstItemDTO.setCurrentCount(new BigDecimal("0.00"));
        prdDstItemDTO.setItemAttrMd5("itemAttrMd5");
        final PrdDstItemTableDTO weightKitchenItem = new PrdDstItemTableDTO();
        prdDstItemDTO.setWeightKitchenItem(weightKitchenItem);
        final PrdDstItemTableDTO prdDstItemTableDTO = new PrdDstItemTableDTO();
        prdDstItemDTO.setKitchenItemList(Arrays.asList(prdDstItemTableDTO));
        prdDstItemDTO.setOrderGuid("orderGuid");
        prdDstItemDTO.setOrderDesc("orderDesc");
        prdDstItemDTO.setOrderNumber("orderNumber");
        prdDstItemDTO.setOrderSerialNo("orderSerialNo");
        prdDstItemDTO.setIsUrged(false);
        prdDstItemDTO.setIsHanged(false);
        prdDstItemDTO.setBatch(0);

        // Configure DeviceConfigService.getOne(...).
        final DeviceConfigDO deviceConfigDO = new DeviceConfigDO();
        deviceConfigDO.setGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        deviceConfigDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        deviceConfigDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        deviceConfigDO.setStoreGuid("storeGuid");
        deviceConfigDO.setPrinterGuid("printerGuid");
        when(mockDeviceConfigService.getOne(any(LambdaQueryWrapper.class))).thenReturn(deviceConfigDO);

        // Configure KdsPrinterService.getOne(...).
        final KdsPrinterDO kdsPrinterDO = new KdsPrinterDO();
        kdsPrinterDO.setGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        kdsPrinterDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrinterDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrinterDO.setPrinterIp("printerIp");
        kdsPrinterDO.setPrinterPort(0);
        kdsPrinterDO.setPageSize(0);
        when(mockKdsPrinterService.getOne(any(LambdaQueryWrapper.class))).thenReturn(kdsPrinterDO);

        when(mockDistributedIdService.nextBatchPrintRecordGuid(0L)).thenReturn(Arrays.asList("value"));

        // Run the test
        kdsPrintRecordServiceImplUnderTest.printSingleItem("deviceId", prdDstItemDTO, KdsInvoiceTypeEnum.NULL);

        // Verify the results
        // Confirm KdsPrintCacheService.save(...).
        final KdsPrintRecordReadDO kdsPrintRecordReadDO = new KdsPrintRecordReadDO();
        kdsPrintRecordReadDO.setGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        kdsPrintRecordReadDO.setRecordUid("orderGuid");
        kdsPrintRecordReadDO.setStoreGuid("storeGuid");
        kdsPrintRecordReadDO.setInvoiceType(0);
        kdsPrintRecordReadDO.setPrintContent("printContent");
        final KdsPrinterDO kdsPrinterDO1 = new KdsPrinterDO();
        kdsPrinterDO1.setGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        kdsPrinterDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrinterDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrinterDO1.setPrinterIp("printerIp");
        kdsPrinterDO1.setPrinterPort(0);
        kdsPrinterDO1.setPageSize(0);
        kdsPrintRecordReadDO.setKdsPrinterDO(kdsPrinterDO1);
        final List<KdsPrintRecordReadDO> arrayOfPrintRecordReadDO = Arrays.asList(kdsPrintRecordReadDO);
        verify(mockKdsPrintCacheService).save(arrayOfPrintRecordReadDO);

        // Confirm KdsPrintPushService.pushPrintTaskMsg(...).
        final KdsPrintDTO kdsPrintDTO = new KdsPrintDTO();
        kdsPrintDTO.setInvoiceType(0);
        kdsPrintDTO.setEnterpriseGuid("enterpriseGuid");
        kdsPrintDTO.setStoreGuid("storeGuid");
        kdsPrintDTO.setPrintUid("orderGuid");
        kdsPrintDTO.setOperatorStaffGuid("createStaffGuid");
        kdsPrintDTO.setOperatorStaffName("operatorStaffName");
        kdsPrintDTO.setOperatorStaffAccount("account");
        kdsPrintDTO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrintDTO.setDeviceId("deviceId");
        final KdsPrintRecordDO kdsPrintRecordDO = new KdsPrintRecordDO();
        kdsPrintRecordDO.setGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        kdsPrintRecordDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrintRecordDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrintRecordDO.setRecordUid("orderGuid");
        kdsPrintRecordDO.setStoreGuid("storeGuid");
        kdsPrintRecordDO.setDeviceId("deviceId");
        kdsPrintRecordDO.setInvoiceType(0);
        kdsPrintRecordDO.setPrinterGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        kdsPrintRecordDO.setPrintStatus(0);
        kdsPrintRecordDO.setPrintStatusMsg("printStatusMsg");
        kdsPrintRecordDO.setPrintContent("printContent");
        kdsPrintRecordDO.setCreateStaffGuid("createStaffGuid");
        final List<KdsPrintRecordDO> arrayOfPrintRecord = Arrays.asList(kdsPrintRecordDO);
        verify(mockKdsPrintPushService).pushPrintTaskMsg(kdsPrintDTO, arrayOfPrintRecord);
    }

    @Test
    public void testPrintSingleItem_KdsPrinterServiceReturnsNull() {
        // Setup
        final PrdDstItemDTO prdDstItemDTO = new PrdDstItemDTO();
        prdDstItemDTO.setIsWeight(false);
        prdDstItemDTO.setCurrentCount(new BigDecimal("0.00"));
        prdDstItemDTO.setItemAttrMd5("itemAttrMd5");
        final PrdDstItemTableDTO weightKitchenItem = new PrdDstItemTableDTO();
        prdDstItemDTO.setWeightKitchenItem(weightKitchenItem);
        final PrdDstItemTableDTO prdDstItemTableDTO = new PrdDstItemTableDTO();
        prdDstItemDTO.setKitchenItemList(Arrays.asList(prdDstItemTableDTO));
        prdDstItemDTO.setOrderGuid("orderGuid");
        prdDstItemDTO.setOrderDesc("orderDesc");
        prdDstItemDTO.setOrderNumber("orderNumber");
        prdDstItemDTO.setOrderSerialNo("orderSerialNo");
        prdDstItemDTO.setIsUrged(false);
        prdDstItemDTO.setIsHanged(false);
        prdDstItemDTO.setBatch(0);

        // Configure DeviceConfigService.getOne(...).
        final DeviceConfigDO deviceConfigDO = new DeviceConfigDO();
        deviceConfigDO.setGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        deviceConfigDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        deviceConfigDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        deviceConfigDO.setStoreGuid("storeGuid");
        deviceConfigDO.setPrinterGuid("printerGuid");
        when(mockDeviceConfigService.getOne(any(LambdaQueryWrapper.class))).thenReturn(deviceConfigDO);

        when(mockKdsPrinterService.getOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Run the test
        kdsPrintRecordServiceImplUnderTest.printSingleItem("deviceId", prdDstItemDTO, KdsInvoiceTypeEnum.NULL);

        // Verify the results
    }

    @Test
    public void testPrintSingleItem_DistributedIdServiceReturnsNoItems() {
        // Setup
        final PrdDstItemDTO prdDstItemDTO = new PrdDstItemDTO();
        prdDstItemDTO.setIsWeight(false);
        prdDstItemDTO.setCurrentCount(new BigDecimal("0.00"));
        prdDstItemDTO.setItemAttrMd5("itemAttrMd5");
        final PrdDstItemTableDTO weightKitchenItem = new PrdDstItemTableDTO();
        prdDstItemDTO.setWeightKitchenItem(weightKitchenItem);
        final PrdDstItemTableDTO prdDstItemTableDTO = new PrdDstItemTableDTO();
        prdDstItemDTO.setKitchenItemList(Arrays.asList(prdDstItemTableDTO));
        prdDstItemDTO.setOrderGuid("orderGuid");
        prdDstItemDTO.setOrderDesc("orderDesc");
        prdDstItemDTO.setOrderNumber("orderNumber");
        prdDstItemDTO.setOrderSerialNo("orderSerialNo");
        prdDstItemDTO.setIsUrged(false);
        prdDstItemDTO.setIsHanged(false);
        prdDstItemDTO.setBatch(0);

        // Configure DeviceConfigService.getOne(...).
        final DeviceConfigDO deviceConfigDO = new DeviceConfigDO();
        deviceConfigDO.setGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        deviceConfigDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        deviceConfigDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        deviceConfigDO.setStoreGuid("storeGuid");
        deviceConfigDO.setPrinterGuid("printerGuid");
        when(mockDeviceConfigService.getOne(any(LambdaQueryWrapper.class))).thenReturn(deviceConfigDO);

        // Configure KdsPrinterService.getOne(...).
        final KdsPrinterDO kdsPrinterDO = new KdsPrinterDO();
        kdsPrinterDO.setGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        kdsPrinterDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrinterDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrinterDO.setPrinterIp("printerIp");
        kdsPrinterDO.setPrinterPort(0);
        kdsPrinterDO.setPageSize(0);
        when(mockKdsPrinterService.getOne(any(LambdaQueryWrapper.class))).thenReturn(kdsPrinterDO);

        when(mockDistributedIdService.nextBatchPrintRecordGuid(0L)).thenReturn(Collections.emptyList());

        // Run the test
        kdsPrintRecordServiceImplUnderTest.printSingleItem("deviceId", prdDstItemDTO, KdsInvoiceTypeEnum.NULL);

        // Verify the results
        // Confirm KdsPrintCacheService.save(...).
        final KdsPrintRecordReadDO kdsPrintRecordReadDO = new KdsPrintRecordReadDO();
        kdsPrintRecordReadDO.setGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        kdsPrintRecordReadDO.setRecordUid("orderGuid");
        kdsPrintRecordReadDO.setStoreGuid("storeGuid");
        kdsPrintRecordReadDO.setInvoiceType(0);
        kdsPrintRecordReadDO.setPrintContent("printContent");
        final KdsPrinterDO kdsPrinterDO1 = new KdsPrinterDO();
        kdsPrinterDO1.setGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        kdsPrinterDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrinterDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrinterDO1.setPrinterIp("printerIp");
        kdsPrinterDO1.setPrinterPort(0);
        kdsPrinterDO1.setPageSize(0);
        kdsPrintRecordReadDO.setKdsPrinterDO(kdsPrinterDO1);
        final List<KdsPrintRecordReadDO> arrayOfPrintRecordReadDO = Arrays.asList(kdsPrintRecordReadDO);
        verify(mockKdsPrintCacheService).save(arrayOfPrintRecordReadDO);

        // Confirm KdsPrintPushService.pushPrintTaskMsg(...).
        final KdsPrintDTO kdsPrintDTO = new KdsPrintDTO();
        kdsPrintDTO.setInvoiceType(0);
        kdsPrintDTO.setEnterpriseGuid("enterpriseGuid");
        kdsPrintDTO.setStoreGuid("storeGuid");
        kdsPrintDTO.setPrintUid("orderGuid");
        kdsPrintDTO.setOperatorStaffGuid("createStaffGuid");
        kdsPrintDTO.setOperatorStaffName("operatorStaffName");
        kdsPrintDTO.setOperatorStaffAccount("account");
        kdsPrintDTO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrintDTO.setDeviceId("deviceId");
        final KdsPrintRecordDO kdsPrintRecordDO = new KdsPrintRecordDO();
        kdsPrintRecordDO.setGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        kdsPrintRecordDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrintRecordDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrintRecordDO.setRecordUid("orderGuid");
        kdsPrintRecordDO.setStoreGuid("storeGuid");
        kdsPrintRecordDO.setDeviceId("deviceId");
        kdsPrintRecordDO.setInvoiceType(0);
        kdsPrintRecordDO.setPrinterGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        kdsPrintRecordDO.setPrintStatus(0);
        kdsPrintRecordDO.setPrintStatusMsg("printStatusMsg");
        kdsPrintRecordDO.setPrintContent("printContent");
        kdsPrintRecordDO.setCreateStaffGuid("createStaffGuid");
        final List<KdsPrintRecordDO> arrayOfPrintRecord = Arrays.asList(kdsPrintRecordDO);
        verify(mockKdsPrintPushService).pushPrintTaskMsg(kdsPrintDTO, arrayOfPrintRecord);
    }

    @Test
    public void testAutoPrintPrdItem() {
        // Setup
        final PrdDstItemDTO prdDstItemDTO = new PrdDstItemDTO();
        prdDstItemDTO.setIsWeight(false);
        prdDstItemDTO.setCurrentCount(new BigDecimal("0.00"));
        prdDstItemDTO.setItemAttrMd5("itemAttrMd5");
        final PrdDstItemTableDTO weightKitchenItem = new PrdDstItemTableDTO();
        prdDstItemDTO.setWeightKitchenItem(weightKitchenItem);
        final PrdDstItemTableDTO prdDstItemTableDTO = new PrdDstItemTableDTO();
        prdDstItemDTO.setKitchenItemList(Arrays.asList(prdDstItemTableDTO));
        prdDstItemDTO.setOrderGuid("orderGuid");
        prdDstItemDTO.setOrderDesc("orderDesc");
        prdDstItemDTO.setOrderNumber("orderNumber");
        prdDstItemDTO.setOrderSerialNo("orderSerialNo");
        prdDstItemDTO.setIsUrged(false);
        prdDstItemDTO.setIsHanged(false);
        prdDstItemDTO.setBatch(0);
        final List<PrdDstItemDTO> prdDstItemDTOS = Arrays.asList(prdDstItemDTO);

        // Configure DeviceConfigService.getOne(...).
        final DeviceConfigDO deviceConfigDO = new DeviceConfigDO();
        deviceConfigDO.setGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        deviceConfigDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        deviceConfigDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        deviceConfigDO.setStoreGuid("storeGuid");
        deviceConfigDO.setPrinterGuid("printerGuid");
        when(mockDeviceConfigService.getOne(any(LambdaQueryWrapper.class))).thenReturn(deviceConfigDO);

        // Configure KdsPrinterService.getOne(...).
        final KdsPrinterDO kdsPrinterDO = new KdsPrinterDO();
        kdsPrinterDO.setGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        kdsPrinterDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrinterDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrinterDO.setPrinterIp("printerIp");
        kdsPrinterDO.setPrinterPort(0);
        kdsPrinterDO.setPageSize(0);
        when(mockKdsPrinterService.getOne(any(LambdaQueryWrapper.class))).thenReturn(kdsPrinterDO);

        when(mockDistributedIdService.nextBatchPrintRecordGuid(0L)).thenReturn(Arrays.asList("value"));

        // Run the test
        kdsPrintRecordServiceImplUnderTest.autoPrintPrdItem("deviceId", prdDstItemDTOS, false);

        // Verify the results
        // Confirm KdsPrintCacheService.save(...).
        final KdsPrintRecordReadDO kdsPrintRecordReadDO = new KdsPrintRecordReadDO();
        kdsPrintRecordReadDO.setGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        kdsPrintRecordReadDO.setRecordUid("orderGuid");
        kdsPrintRecordReadDO.setStoreGuid("storeGuid");
        kdsPrintRecordReadDO.setInvoiceType(0);
        kdsPrintRecordReadDO.setPrintContent("printContent");
        final KdsPrinterDO kdsPrinterDO1 = new KdsPrinterDO();
        kdsPrinterDO1.setGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        kdsPrinterDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrinterDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrinterDO1.setPrinterIp("printerIp");
        kdsPrinterDO1.setPrinterPort(0);
        kdsPrinterDO1.setPageSize(0);
        kdsPrintRecordReadDO.setKdsPrinterDO(kdsPrinterDO1);
        final List<KdsPrintRecordReadDO> arrayOfPrintRecordReadDO = Arrays.asList(kdsPrintRecordReadDO);
        verify(mockKdsPrintCacheService).save(arrayOfPrintRecordReadDO);

        // Confirm KdsPrintPushService.pushPrintTaskMsg(...).
        final KdsPrintDTO kdsPrintDTO = new KdsPrintDTO();
        kdsPrintDTO.setInvoiceType(0);
        kdsPrintDTO.setEnterpriseGuid("enterpriseGuid");
        kdsPrintDTO.setStoreGuid("storeGuid");
        kdsPrintDTO.setPrintUid("orderGuid");
        kdsPrintDTO.setOperatorStaffGuid("createStaffGuid");
        kdsPrintDTO.setOperatorStaffName("operatorStaffName");
        kdsPrintDTO.setOperatorStaffAccount("account");
        kdsPrintDTO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrintDTO.setDeviceId("deviceId");
        final KdsPrintRecordDO kdsPrintRecordDO = new KdsPrintRecordDO();
        kdsPrintRecordDO.setGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        kdsPrintRecordDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrintRecordDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrintRecordDO.setRecordUid("orderGuid");
        kdsPrintRecordDO.setStoreGuid("storeGuid");
        kdsPrintRecordDO.setDeviceId("deviceId");
        kdsPrintRecordDO.setInvoiceType(0);
        kdsPrintRecordDO.setPrinterGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        kdsPrintRecordDO.setPrintStatus(0);
        kdsPrintRecordDO.setPrintStatusMsg("printStatusMsg");
        kdsPrintRecordDO.setPrintContent("printContent");
        kdsPrintRecordDO.setCreateStaffGuid("createStaffGuid");
        final List<KdsPrintRecordDO> arrayOfPrintRecord = Arrays.asList(kdsPrintRecordDO);
        verify(mockKdsPrintPushService).pushPrintTaskMsg(kdsPrintDTO, arrayOfPrintRecord);
    }

    @Test
    public void testAutoPrintPrdItem_KdsPrinterServiceReturnsNull() {
        // Setup
        final PrdDstItemDTO prdDstItemDTO = new PrdDstItemDTO();
        prdDstItemDTO.setIsWeight(false);
        prdDstItemDTO.setCurrentCount(new BigDecimal("0.00"));
        prdDstItemDTO.setItemAttrMd5("itemAttrMd5");
        final PrdDstItemTableDTO weightKitchenItem = new PrdDstItemTableDTO();
        prdDstItemDTO.setWeightKitchenItem(weightKitchenItem);
        final PrdDstItemTableDTO prdDstItemTableDTO = new PrdDstItemTableDTO();
        prdDstItemDTO.setKitchenItemList(Arrays.asList(prdDstItemTableDTO));
        prdDstItemDTO.setOrderGuid("orderGuid");
        prdDstItemDTO.setOrderDesc("orderDesc");
        prdDstItemDTO.setOrderNumber("orderNumber");
        prdDstItemDTO.setOrderSerialNo("orderSerialNo");
        prdDstItemDTO.setIsUrged(false);
        prdDstItemDTO.setIsHanged(false);
        prdDstItemDTO.setBatch(0);
        final List<PrdDstItemDTO> prdDstItemDTOS = Arrays.asList(prdDstItemDTO);

        // Configure DeviceConfigService.getOne(...).
        final DeviceConfigDO deviceConfigDO = new DeviceConfigDO();
        deviceConfigDO.setGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        deviceConfigDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        deviceConfigDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        deviceConfigDO.setStoreGuid("storeGuid");
        deviceConfigDO.setPrinterGuid("printerGuid");
        when(mockDeviceConfigService.getOne(any(LambdaQueryWrapper.class))).thenReturn(deviceConfigDO);

        when(mockKdsPrinterService.getOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Run the test
        kdsPrintRecordServiceImplUnderTest.autoPrintPrdItem("deviceId", prdDstItemDTOS, false);

        // Verify the results
    }

    @Test
    public void testAutoPrintPrdItem_DistributedIdServiceReturnsNoItems() {
        // Setup
        final PrdDstItemDTO prdDstItemDTO = new PrdDstItemDTO();
        prdDstItemDTO.setIsWeight(false);
        prdDstItemDTO.setCurrentCount(new BigDecimal("0.00"));
        prdDstItemDTO.setItemAttrMd5("itemAttrMd5");
        final PrdDstItemTableDTO weightKitchenItem = new PrdDstItemTableDTO();
        prdDstItemDTO.setWeightKitchenItem(weightKitchenItem);
        final PrdDstItemTableDTO prdDstItemTableDTO = new PrdDstItemTableDTO();
        prdDstItemDTO.setKitchenItemList(Arrays.asList(prdDstItemTableDTO));
        prdDstItemDTO.setOrderGuid("orderGuid");
        prdDstItemDTO.setOrderDesc("orderDesc");
        prdDstItemDTO.setOrderNumber("orderNumber");
        prdDstItemDTO.setOrderSerialNo("orderSerialNo");
        prdDstItemDTO.setIsUrged(false);
        prdDstItemDTO.setIsHanged(false);
        prdDstItemDTO.setBatch(0);
        final List<PrdDstItemDTO> prdDstItemDTOS = Arrays.asList(prdDstItemDTO);

        // Configure DeviceConfigService.getOne(...).
        final DeviceConfigDO deviceConfigDO = new DeviceConfigDO();
        deviceConfigDO.setGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        deviceConfigDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        deviceConfigDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        deviceConfigDO.setStoreGuid("storeGuid");
        deviceConfigDO.setPrinterGuid("printerGuid");
        when(mockDeviceConfigService.getOne(any(LambdaQueryWrapper.class))).thenReturn(deviceConfigDO);

        // Configure KdsPrinterService.getOne(...).
        final KdsPrinterDO kdsPrinterDO = new KdsPrinterDO();
        kdsPrinterDO.setGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        kdsPrinterDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrinterDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrinterDO.setPrinterIp("printerIp");
        kdsPrinterDO.setPrinterPort(0);
        kdsPrinterDO.setPageSize(0);
        when(mockKdsPrinterService.getOne(any(LambdaQueryWrapper.class))).thenReturn(kdsPrinterDO);

        when(mockDistributedIdService.nextBatchPrintRecordGuid(0L)).thenReturn(Collections.emptyList());

        // Run the test
        kdsPrintRecordServiceImplUnderTest.autoPrintPrdItem("deviceId", prdDstItemDTOS, false);

        // Verify the results
        // Confirm KdsPrintCacheService.save(...).
        final KdsPrintRecordReadDO kdsPrintRecordReadDO = new KdsPrintRecordReadDO();
        kdsPrintRecordReadDO.setGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        kdsPrintRecordReadDO.setRecordUid("orderGuid");
        kdsPrintRecordReadDO.setStoreGuid("storeGuid");
        kdsPrintRecordReadDO.setInvoiceType(0);
        kdsPrintRecordReadDO.setPrintContent("printContent");
        final KdsPrinterDO kdsPrinterDO1 = new KdsPrinterDO();
        kdsPrinterDO1.setGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        kdsPrinterDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrinterDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrinterDO1.setPrinterIp("printerIp");
        kdsPrinterDO1.setPrinterPort(0);
        kdsPrinterDO1.setPageSize(0);
        kdsPrintRecordReadDO.setKdsPrinterDO(kdsPrinterDO1);
        final List<KdsPrintRecordReadDO> arrayOfPrintRecordReadDO = Arrays.asList(kdsPrintRecordReadDO);
        verify(mockKdsPrintCacheService).save(arrayOfPrintRecordReadDO);

        // Confirm KdsPrintPushService.pushPrintTaskMsg(...).
        final KdsPrintDTO kdsPrintDTO = new KdsPrintDTO();
        kdsPrintDTO.setInvoiceType(0);
        kdsPrintDTO.setEnterpriseGuid("enterpriseGuid");
        kdsPrintDTO.setStoreGuid("storeGuid");
        kdsPrintDTO.setPrintUid("orderGuid");
        kdsPrintDTO.setOperatorStaffGuid("createStaffGuid");
        kdsPrintDTO.setOperatorStaffName("operatorStaffName");
        kdsPrintDTO.setOperatorStaffAccount("account");
        kdsPrintDTO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrintDTO.setDeviceId("deviceId");
        final KdsPrintRecordDO kdsPrintRecordDO = new KdsPrintRecordDO();
        kdsPrintRecordDO.setGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        kdsPrintRecordDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrintRecordDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrintRecordDO.setRecordUid("orderGuid");
        kdsPrintRecordDO.setStoreGuid("storeGuid");
        kdsPrintRecordDO.setDeviceId("deviceId");
        kdsPrintRecordDO.setInvoiceType(0);
        kdsPrintRecordDO.setPrinterGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        kdsPrintRecordDO.setPrintStatus(0);
        kdsPrintRecordDO.setPrintStatusMsg("printStatusMsg");
        kdsPrintRecordDO.setPrintContent("printContent");
        kdsPrintRecordDO.setCreateStaffGuid("createStaffGuid");
        final List<KdsPrintRecordDO> arrayOfPrintRecord = Arrays.asList(kdsPrintRecordDO);
        verify(mockKdsPrintPushService).pushPrintTaskMsg(kdsPrintDTO, arrayOfPrintRecord);
    }

    @Test
    public void testManualPrintPrdItem() {
        // Setup
        final ItemStateTransReqDTO itemStateTransReqDTO = new ItemStateTransReqDTO();
        itemStateTransReqDTO.setDeviceId("deviceId");
        itemStateTransReqDTO.setUserGuid("createStaffGuid");
        itemStateTransReqDTO.setUserName("operatorStaffName");
        itemStateTransReqDTO.setAccount("account");
        final PrdDstItemDTO prdDstItemDTO = new PrdDstItemDTO();
        prdDstItemDTO.setIsWeight(false);
        prdDstItemDTO.setCurrentCount(new BigDecimal("0.00"));
        prdDstItemDTO.setItemAttrMd5("itemAttrMd5");
        final PrdDstItemTableDTO weightKitchenItem = new PrdDstItemTableDTO();
        prdDstItemDTO.setWeightKitchenItem(weightKitchenItem);
        final PrdDstItemTableDTO prdDstItemTableDTO = new PrdDstItemTableDTO();
        prdDstItemDTO.setKitchenItemList(Arrays.asList(prdDstItemTableDTO));
        prdDstItemDTO.setOrderGuid("orderGuid");
        prdDstItemDTO.setOrderDesc("orderDesc");
        prdDstItemDTO.setOrderNumber("orderNumber");
        prdDstItemDTO.setOrderSerialNo("orderSerialNo");
        prdDstItemDTO.setIsUrged(false);
        prdDstItemDTO.setIsHanged(false);
        prdDstItemDTO.setBatch(0);
        itemStateTransReqDTO.setPrdDstItemList(Arrays.asList(prdDstItemDTO));

        // Configure DeviceConfigService.getOne(...).
        final DeviceConfigDO deviceConfigDO = new DeviceConfigDO();
        deviceConfigDO.setGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        deviceConfigDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        deviceConfigDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        deviceConfigDO.setStoreGuid("storeGuid");
        deviceConfigDO.setPrinterGuid("printerGuid");
        when(mockDeviceConfigService.getOne(any(LambdaQueryWrapper.class))).thenReturn(deviceConfigDO);

        // Configure KdsPrinterService.getOne(...).
        final KdsPrinterDO kdsPrinterDO = new KdsPrinterDO();
        kdsPrinterDO.setGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        kdsPrinterDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrinterDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrinterDO.setPrinterIp("printerIp");
        kdsPrinterDO.setPrinterPort(0);
        kdsPrinterDO.setPageSize(0);
        when(mockKdsPrinterService.getOne(any(LambdaQueryWrapper.class))).thenReturn(kdsPrinterDO);

        when(mockDistributedIdService.nextBatchPrintRecordGuid(0L)).thenReturn(Arrays.asList("value"));

        // Run the test
        kdsPrintRecordServiceImplUnderTest.manualPrintPrdItem(itemStateTransReqDTO, false);

        // Verify the results
        // Confirm KdsPrintCacheService.save(...).
        final KdsPrintRecordReadDO kdsPrintRecordReadDO = new KdsPrintRecordReadDO();
        kdsPrintRecordReadDO.setGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        kdsPrintRecordReadDO.setRecordUid("orderGuid");
        kdsPrintRecordReadDO.setStoreGuid("storeGuid");
        kdsPrintRecordReadDO.setInvoiceType(0);
        kdsPrintRecordReadDO.setPrintContent("printContent");
        final KdsPrinterDO kdsPrinterDO1 = new KdsPrinterDO();
        kdsPrinterDO1.setGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        kdsPrinterDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrinterDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrinterDO1.setPrinterIp("printerIp");
        kdsPrinterDO1.setPrinterPort(0);
        kdsPrinterDO1.setPageSize(0);
        kdsPrintRecordReadDO.setKdsPrinterDO(kdsPrinterDO1);
        final List<KdsPrintRecordReadDO> arrayOfPrintRecordReadDO = Arrays.asList(kdsPrintRecordReadDO);
        verify(mockKdsPrintCacheService).save(arrayOfPrintRecordReadDO);

        // Confirm KdsPrintPushService.pushPrintTaskMsg(...).
        final KdsPrintDTO kdsPrintDTO = new KdsPrintDTO();
        kdsPrintDTO.setInvoiceType(0);
        kdsPrintDTO.setEnterpriseGuid("enterpriseGuid");
        kdsPrintDTO.setStoreGuid("storeGuid");
        kdsPrintDTO.setPrintUid("orderGuid");
        kdsPrintDTO.setOperatorStaffGuid("createStaffGuid");
        kdsPrintDTO.setOperatorStaffName("operatorStaffName");
        kdsPrintDTO.setOperatorStaffAccount("account");
        kdsPrintDTO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrintDTO.setDeviceId("deviceId");
        final KdsPrintRecordDO kdsPrintRecordDO = new KdsPrintRecordDO();
        kdsPrintRecordDO.setGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        kdsPrintRecordDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrintRecordDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrintRecordDO.setRecordUid("orderGuid");
        kdsPrintRecordDO.setStoreGuid("storeGuid");
        kdsPrintRecordDO.setDeviceId("deviceId");
        kdsPrintRecordDO.setInvoiceType(0);
        kdsPrintRecordDO.setPrinterGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        kdsPrintRecordDO.setPrintStatus(0);
        kdsPrintRecordDO.setPrintStatusMsg("printStatusMsg");
        kdsPrintRecordDO.setPrintContent("printContent");
        kdsPrintRecordDO.setCreateStaffGuid("createStaffGuid");
        final List<KdsPrintRecordDO> arrayOfPrintRecord = Arrays.asList(kdsPrintRecordDO);
        verify(mockKdsPrintPushService).pushPrintTaskMsg(kdsPrintDTO, arrayOfPrintRecord);
    }

    @Test
    public void testManualPrintPrdItem_KdsPrinterServiceReturnsNull() {
        // Setup
        final ItemStateTransReqDTO itemStateTransReqDTO = new ItemStateTransReqDTO();
        itemStateTransReqDTO.setDeviceId("deviceId");
        itemStateTransReqDTO.setUserGuid("createStaffGuid");
        itemStateTransReqDTO.setUserName("operatorStaffName");
        itemStateTransReqDTO.setAccount("account");
        final PrdDstItemDTO prdDstItemDTO = new PrdDstItemDTO();
        prdDstItemDTO.setIsWeight(false);
        prdDstItemDTO.setCurrentCount(new BigDecimal("0.00"));
        prdDstItemDTO.setItemAttrMd5("itemAttrMd5");
        final PrdDstItemTableDTO weightKitchenItem = new PrdDstItemTableDTO();
        prdDstItemDTO.setWeightKitchenItem(weightKitchenItem);
        final PrdDstItemTableDTO prdDstItemTableDTO = new PrdDstItemTableDTO();
        prdDstItemDTO.setKitchenItemList(Arrays.asList(prdDstItemTableDTO));
        prdDstItemDTO.setOrderGuid("orderGuid");
        prdDstItemDTO.setOrderDesc("orderDesc");
        prdDstItemDTO.setOrderNumber("orderNumber");
        prdDstItemDTO.setOrderSerialNo("orderSerialNo");
        prdDstItemDTO.setIsUrged(false);
        prdDstItemDTO.setIsHanged(false);
        prdDstItemDTO.setBatch(0);
        itemStateTransReqDTO.setPrdDstItemList(Arrays.asList(prdDstItemDTO));

        // Configure DeviceConfigService.getOne(...).
        final DeviceConfigDO deviceConfigDO = new DeviceConfigDO();
        deviceConfigDO.setGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        deviceConfigDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        deviceConfigDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        deviceConfigDO.setStoreGuid("storeGuid");
        deviceConfigDO.setPrinterGuid("printerGuid");
        when(mockDeviceConfigService.getOne(any(LambdaQueryWrapper.class))).thenReturn(deviceConfigDO);

        when(mockKdsPrinterService.getOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Run the test
        kdsPrintRecordServiceImplUnderTest.manualPrintPrdItem(itemStateTransReqDTO, false);

        // Verify the results
    }

    @Test
    public void testManualPrintPrdItem_DistributedIdServiceReturnsNoItems() {
        // Setup
        final ItemStateTransReqDTO itemStateTransReqDTO = new ItemStateTransReqDTO();
        itemStateTransReqDTO.setDeviceId("deviceId");
        itemStateTransReqDTO.setUserGuid("createStaffGuid");
        itemStateTransReqDTO.setUserName("operatorStaffName");
        itemStateTransReqDTO.setAccount("account");
        final PrdDstItemDTO prdDstItemDTO = new PrdDstItemDTO();
        prdDstItemDTO.setIsWeight(false);
        prdDstItemDTO.setCurrentCount(new BigDecimal("0.00"));
        prdDstItemDTO.setItemAttrMd5("itemAttrMd5");
        final PrdDstItemTableDTO weightKitchenItem = new PrdDstItemTableDTO();
        prdDstItemDTO.setWeightKitchenItem(weightKitchenItem);
        final PrdDstItemTableDTO prdDstItemTableDTO = new PrdDstItemTableDTO();
        prdDstItemDTO.setKitchenItemList(Arrays.asList(prdDstItemTableDTO));
        prdDstItemDTO.setOrderGuid("orderGuid");
        prdDstItemDTO.setOrderDesc("orderDesc");
        prdDstItemDTO.setOrderNumber("orderNumber");
        prdDstItemDTO.setOrderSerialNo("orderSerialNo");
        prdDstItemDTO.setIsUrged(false);
        prdDstItemDTO.setIsHanged(false);
        prdDstItemDTO.setBatch(0);
        itemStateTransReqDTO.setPrdDstItemList(Arrays.asList(prdDstItemDTO));

        // Configure DeviceConfigService.getOne(...).
        final DeviceConfigDO deviceConfigDO = new DeviceConfigDO();
        deviceConfigDO.setGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        deviceConfigDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        deviceConfigDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        deviceConfigDO.setStoreGuid("storeGuid");
        deviceConfigDO.setPrinterGuid("printerGuid");
        when(mockDeviceConfigService.getOne(any(LambdaQueryWrapper.class))).thenReturn(deviceConfigDO);

        // Configure KdsPrinterService.getOne(...).
        final KdsPrinterDO kdsPrinterDO = new KdsPrinterDO();
        kdsPrinterDO.setGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        kdsPrinterDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrinterDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrinterDO.setPrinterIp("printerIp");
        kdsPrinterDO.setPrinterPort(0);
        kdsPrinterDO.setPageSize(0);
        when(mockKdsPrinterService.getOne(any(LambdaQueryWrapper.class))).thenReturn(kdsPrinterDO);

        when(mockDistributedIdService.nextBatchPrintRecordGuid(0L)).thenReturn(Collections.emptyList());

        // Run the test
        kdsPrintRecordServiceImplUnderTest.manualPrintPrdItem(itemStateTransReqDTO, false);

        // Verify the results
        // Confirm KdsPrintCacheService.save(...).
        final KdsPrintRecordReadDO kdsPrintRecordReadDO = new KdsPrintRecordReadDO();
        kdsPrintRecordReadDO.setGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        kdsPrintRecordReadDO.setRecordUid("orderGuid");
        kdsPrintRecordReadDO.setStoreGuid("storeGuid");
        kdsPrintRecordReadDO.setInvoiceType(0);
        kdsPrintRecordReadDO.setPrintContent("printContent");
        final KdsPrinterDO kdsPrinterDO1 = new KdsPrinterDO();
        kdsPrinterDO1.setGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        kdsPrinterDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrinterDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrinterDO1.setPrinterIp("printerIp");
        kdsPrinterDO1.setPrinterPort(0);
        kdsPrinterDO1.setPageSize(0);
        kdsPrintRecordReadDO.setKdsPrinterDO(kdsPrinterDO1);
        final List<KdsPrintRecordReadDO> arrayOfPrintRecordReadDO = Arrays.asList(kdsPrintRecordReadDO);
        verify(mockKdsPrintCacheService).save(arrayOfPrintRecordReadDO);

        // Confirm KdsPrintPushService.pushPrintTaskMsg(...).
        final KdsPrintDTO kdsPrintDTO = new KdsPrintDTO();
        kdsPrintDTO.setInvoiceType(0);
        kdsPrintDTO.setEnterpriseGuid("enterpriseGuid");
        kdsPrintDTO.setStoreGuid("storeGuid");
        kdsPrintDTO.setPrintUid("orderGuid");
        kdsPrintDTO.setOperatorStaffGuid("createStaffGuid");
        kdsPrintDTO.setOperatorStaffName("operatorStaffName");
        kdsPrintDTO.setOperatorStaffAccount("account");
        kdsPrintDTO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrintDTO.setDeviceId("deviceId");
        final KdsPrintRecordDO kdsPrintRecordDO = new KdsPrintRecordDO();
        kdsPrintRecordDO.setGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        kdsPrintRecordDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrintRecordDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrintRecordDO.setRecordUid("orderGuid");
        kdsPrintRecordDO.setStoreGuid("storeGuid");
        kdsPrintRecordDO.setDeviceId("deviceId");
        kdsPrintRecordDO.setInvoiceType(0);
        kdsPrintRecordDO.setPrinterGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        kdsPrintRecordDO.setPrintStatus(0);
        kdsPrintRecordDO.setPrintStatusMsg("printStatusMsg");
        kdsPrintRecordDO.setPrintContent("printContent");
        kdsPrintRecordDO.setCreateStaffGuid("createStaffGuid");
        final List<KdsPrintRecordDO> arrayOfPrintRecord = Arrays.asList(kdsPrintRecordDO);
        verify(mockKdsPrintPushService).pushPrintTaskMsg(kdsPrintDTO, arrayOfPrintRecord);
    }

    @Test
    public void testManualPrintDstItem() {
        // Setup
        final ItemStateTransReqDTO itemStateTransReqDTO = new ItemStateTransReqDTO();
        itemStateTransReqDTO.setDeviceId("deviceId");
        itemStateTransReqDTO.setUserGuid("createStaffGuid");
        itemStateTransReqDTO.setUserName("operatorStaffName");
        itemStateTransReqDTO.setAccount("account");
        final PrdDstItemDTO prdDstItemDTO = new PrdDstItemDTO();
        prdDstItemDTO.setIsWeight(false);
        prdDstItemDTO.setCurrentCount(new BigDecimal("0.00"));
        prdDstItemDTO.setItemAttrMd5("itemAttrMd5");
        final PrdDstItemTableDTO weightKitchenItem = new PrdDstItemTableDTO();
        prdDstItemDTO.setWeightKitchenItem(weightKitchenItem);
        final PrdDstItemTableDTO prdDstItemTableDTO = new PrdDstItemTableDTO();
        prdDstItemDTO.setKitchenItemList(Arrays.asList(prdDstItemTableDTO));
        prdDstItemDTO.setOrderGuid("orderGuid");
        prdDstItemDTO.setOrderDesc("orderDesc");
        prdDstItemDTO.setOrderNumber("orderNumber");
        prdDstItemDTO.setOrderSerialNo("orderSerialNo");
        prdDstItemDTO.setIsUrged(false);
        prdDstItemDTO.setIsHanged(false);
        prdDstItemDTO.setBatch(0);
        itemStateTransReqDTO.setPrdDstItemList(Arrays.asList(prdDstItemDTO));

        // Configure DeviceConfigService.getOne(...).
        final DeviceConfigDO deviceConfigDO = new DeviceConfigDO();
        deviceConfigDO.setGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        deviceConfigDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        deviceConfigDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        deviceConfigDO.setStoreGuid("storeGuid");
        deviceConfigDO.setPrinterGuid("printerGuid");
        when(mockDeviceConfigService.getOne(any(LambdaQueryWrapper.class))).thenReturn(deviceConfigDO);

        // Configure KdsPrinterService.getOne(...).
        final KdsPrinterDO kdsPrinterDO = new KdsPrinterDO();
        kdsPrinterDO.setGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        kdsPrinterDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrinterDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrinterDO.setPrinterIp("printerIp");
        kdsPrinterDO.setPrinterPort(0);
        kdsPrinterDO.setPageSize(0);
        when(mockKdsPrinterService.getOne(any(LambdaQueryWrapper.class))).thenReturn(kdsPrinterDO);

        when(mockDistributedIdService.nextBatchPrintRecordGuid(0L)).thenReturn(Arrays.asList("value"));

        // Run the test
        kdsPrintRecordServiceImplUnderTest.manualPrintDstItem(itemStateTransReqDTO);

        // Verify the results
        // Confirm KdsPrintCacheService.save(...).
        final KdsPrintRecordReadDO kdsPrintRecordReadDO = new KdsPrintRecordReadDO();
        kdsPrintRecordReadDO.setGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        kdsPrintRecordReadDO.setRecordUid("orderGuid");
        kdsPrintRecordReadDO.setStoreGuid("storeGuid");
        kdsPrintRecordReadDO.setInvoiceType(0);
        kdsPrintRecordReadDO.setPrintContent("printContent");
        final KdsPrinterDO kdsPrinterDO1 = new KdsPrinterDO();
        kdsPrinterDO1.setGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        kdsPrinterDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrinterDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrinterDO1.setPrinterIp("printerIp");
        kdsPrinterDO1.setPrinterPort(0);
        kdsPrinterDO1.setPageSize(0);
        kdsPrintRecordReadDO.setKdsPrinterDO(kdsPrinterDO1);
        final List<KdsPrintRecordReadDO> arrayOfPrintRecordReadDO = Arrays.asList(kdsPrintRecordReadDO);
        verify(mockKdsPrintCacheService).save(arrayOfPrintRecordReadDO);

        // Confirm KdsPrintPushService.pushPrintTaskMsg(...).
        final KdsPrintDTO kdsPrintDTO = new KdsPrintDTO();
        kdsPrintDTO.setInvoiceType(0);
        kdsPrintDTO.setEnterpriseGuid("enterpriseGuid");
        kdsPrintDTO.setStoreGuid("storeGuid");
        kdsPrintDTO.setPrintUid("orderGuid");
        kdsPrintDTO.setOperatorStaffGuid("createStaffGuid");
        kdsPrintDTO.setOperatorStaffName("operatorStaffName");
        kdsPrintDTO.setOperatorStaffAccount("account");
        kdsPrintDTO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrintDTO.setDeviceId("deviceId");
        final KdsPrintRecordDO kdsPrintRecordDO = new KdsPrintRecordDO();
        kdsPrintRecordDO.setGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        kdsPrintRecordDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrintRecordDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrintRecordDO.setRecordUid("orderGuid");
        kdsPrintRecordDO.setStoreGuid("storeGuid");
        kdsPrintRecordDO.setDeviceId("deviceId");
        kdsPrintRecordDO.setInvoiceType(0);
        kdsPrintRecordDO.setPrinterGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        kdsPrintRecordDO.setPrintStatus(0);
        kdsPrintRecordDO.setPrintStatusMsg("printStatusMsg");
        kdsPrintRecordDO.setPrintContent("printContent");
        kdsPrintRecordDO.setCreateStaffGuid("createStaffGuid");
        final List<KdsPrintRecordDO> arrayOfPrintRecord = Arrays.asList(kdsPrintRecordDO);
        verify(mockKdsPrintPushService).pushPrintTaskMsg(kdsPrintDTO, arrayOfPrintRecord);
    }

    @Test
    public void testManualPrintDstItem_KdsPrinterServiceReturnsNull() {
        // Setup
        final ItemStateTransReqDTO itemStateTransReqDTO = new ItemStateTransReqDTO();
        itemStateTransReqDTO.setDeviceId("deviceId");
        itemStateTransReqDTO.setUserGuid("createStaffGuid");
        itemStateTransReqDTO.setUserName("operatorStaffName");
        itemStateTransReqDTO.setAccount("account");
        final PrdDstItemDTO prdDstItemDTO = new PrdDstItemDTO();
        prdDstItemDTO.setIsWeight(false);
        prdDstItemDTO.setCurrentCount(new BigDecimal("0.00"));
        prdDstItemDTO.setItemAttrMd5("itemAttrMd5");
        final PrdDstItemTableDTO weightKitchenItem = new PrdDstItemTableDTO();
        prdDstItemDTO.setWeightKitchenItem(weightKitchenItem);
        final PrdDstItemTableDTO prdDstItemTableDTO = new PrdDstItemTableDTO();
        prdDstItemDTO.setKitchenItemList(Arrays.asList(prdDstItemTableDTO));
        prdDstItemDTO.setOrderGuid("orderGuid");
        prdDstItemDTO.setOrderDesc("orderDesc");
        prdDstItemDTO.setOrderNumber("orderNumber");
        prdDstItemDTO.setOrderSerialNo("orderSerialNo");
        prdDstItemDTO.setIsUrged(false);
        prdDstItemDTO.setIsHanged(false);
        prdDstItemDTO.setBatch(0);
        itemStateTransReqDTO.setPrdDstItemList(Arrays.asList(prdDstItemDTO));

        // Configure DeviceConfigService.getOne(...).
        final DeviceConfigDO deviceConfigDO = new DeviceConfigDO();
        deviceConfigDO.setGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        deviceConfigDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        deviceConfigDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        deviceConfigDO.setStoreGuid("storeGuid");
        deviceConfigDO.setPrinterGuid("printerGuid");
        when(mockDeviceConfigService.getOne(any(LambdaQueryWrapper.class))).thenReturn(deviceConfigDO);

        when(mockKdsPrinterService.getOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Run the test
        kdsPrintRecordServiceImplUnderTest.manualPrintDstItem(itemStateTransReqDTO);

        // Verify the results
    }

    @Test
    public void testManualPrintDstItem_DistributedIdServiceReturnsNoItems() {
        // Setup
        final ItemStateTransReqDTO itemStateTransReqDTO = new ItemStateTransReqDTO();
        itemStateTransReqDTO.setDeviceId("deviceId");
        itemStateTransReqDTO.setUserGuid("createStaffGuid");
        itemStateTransReqDTO.setUserName("operatorStaffName");
        itemStateTransReqDTO.setAccount("account");
        final PrdDstItemDTO prdDstItemDTO = new PrdDstItemDTO();
        prdDstItemDTO.setIsWeight(false);
        prdDstItemDTO.setCurrentCount(new BigDecimal("0.00"));
        prdDstItemDTO.setItemAttrMd5("itemAttrMd5");
        final PrdDstItemTableDTO weightKitchenItem = new PrdDstItemTableDTO();
        prdDstItemDTO.setWeightKitchenItem(weightKitchenItem);
        final PrdDstItemTableDTO prdDstItemTableDTO = new PrdDstItemTableDTO();
        prdDstItemDTO.setKitchenItemList(Arrays.asList(prdDstItemTableDTO));
        prdDstItemDTO.setOrderGuid("orderGuid");
        prdDstItemDTO.setOrderDesc("orderDesc");
        prdDstItemDTO.setOrderNumber("orderNumber");
        prdDstItemDTO.setOrderSerialNo("orderSerialNo");
        prdDstItemDTO.setIsUrged(false);
        prdDstItemDTO.setIsHanged(false);
        prdDstItemDTO.setBatch(0);
        itemStateTransReqDTO.setPrdDstItemList(Arrays.asList(prdDstItemDTO));

        // Configure DeviceConfigService.getOne(...).
        final DeviceConfigDO deviceConfigDO = new DeviceConfigDO();
        deviceConfigDO.setGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        deviceConfigDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        deviceConfigDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        deviceConfigDO.setStoreGuid("storeGuid");
        deviceConfigDO.setPrinterGuid("printerGuid");
        when(mockDeviceConfigService.getOne(any(LambdaQueryWrapper.class))).thenReturn(deviceConfigDO);

        // Configure KdsPrinterService.getOne(...).
        final KdsPrinterDO kdsPrinterDO = new KdsPrinterDO();
        kdsPrinterDO.setGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        kdsPrinterDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrinterDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrinterDO.setPrinterIp("printerIp");
        kdsPrinterDO.setPrinterPort(0);
        kdsPrinterDO.setPageSize(0);
        when(mockKdsPrinterService.getOne(any(LambdaQueryWrapper.class))).thenReturn(kdsPrinterDO);

        when(mockDistributedIdService.nextBatchPrintRecordGuid(0L)).thenReturn(Collections.emptyList());

        // Run the test
        kdsPrintRecordServiceImplUnderTest.manualPrintDstItem(itemStateTransReqDTO);

        // Verify the results
        // Confirm KdsPrintCacheService.save(...).
        final KdsPrintRecordReadDO kdsPrintRecordReadDO = new KdsPrintRecordReadDO();
        kdsPrintRecordReadDO.setGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        kdsPrintRecordReadDO.setRecordUid("orderGuid");
        kdsPrintRecordReadDO.setStoreGuid("storeGuid");
        kdsPrintRecordReadDO.setInvoiceType(0);
        kdsPrintRecordReadDO.setPrintContent("printContent");
        final KdsPrinterDO kdsPrinterDO1 = new KdsPrinterDO();
        kdsPrinterDO1.setGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        kdsPrinterDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrinterDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrinterDO1.setPrinterIp("printerIp");
        kdsPrinterDO1.setPrinterPort(0);
        kdsPrinterDO1.setPageSize(0);
        kdsPrintRecordReadDO.setKdsPrinterDO(kdsPrinterDO1);
        final List<KdsPrintRecordReadDO> arrayOfPrintRecordReadDO = Arrays.asList(kdsPrintRecordReadDO);
        verify(mockKdsPrintCacheService).save(arrayOfPrintRecordReadDO);

        // Confirm KdsPrintPushService.pushPrintTaskMsg(...).
        final KdsPrintDTO kdsPrintDTO = new KdsPrintDTO();
        kdsPrintDTO.setInvoiceType(0);
        kdsPrintDTO.setEnterpriseGuid("enterpriseGuid");
        kdsPrintDTO.setStoreGuid("storeGuid");
        kdsPrintDTO.setPrintUid("orderGuid");
        kdsPrintDTO.setOperatorStaffGuid("createStaffGuid");
        kdsPrintDTO.setOperatorStaffName("operatorStaffName");
        kdsPrintDTO.setOperatorStaffAccount("account");
        kdsPrintDTO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrintDTO.setDeviceId("deviceId");
        final KdsPrintRecordDO kdsPrintRecordDO = new KdsPrintRecordDO();
        kdsPrintRecordDO.setGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        kdsPrintRecordDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrintRecordDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrintRecordDO.setRecordUid("orderGuid");
        kdsPrintRecordDO.setStoreGuid("storeGuid");
        kdsPrintRecordDO.setDeviceId("deviceId");
        kdsPrintRecordDO.setInvoiceType(0);
        kdsPrintRecordDO.setPrinterGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        kdsPrintRecordDO.setPrintStatus(0);
        kdsPrintRecordDO.setPrintStatusMsg("printStatusMsg");
        kdsPrintRecordDO.setPrintContent("printContent");
        kdsPrintRecordDO.setCreateStaffGuid("createStaffGuid");
        final List<KdsPrintRecordDO> arrayOfPrintRecord = Arrays.asList(kdsPrintRecordDO);
        verify(mockKdsPrintPushService).pushPrintTaskMsg(kdsPrintDTO, arrayOfPrintRecord);
    }

    @Test
    public void testPrintChangesItem() {
        // Setup
        final KdsChangesItemDTO kdsChangesItemDTO = new KdsChangesItemDTO();
        kdsChangesItemDTO.setDeviceId("deviceId");
        kdsChangesItemDTO.setUserGuid("createStaffGuid");
        kdsChangesItemDTO.setUserName("operatorStaffName");
        kdsChangesItemDTO.setAccount("account");
        kdsChangesItemDTO.setOrderGuid("orderGuid");
        kdsChangesItemDTO.setOrderNo("orderNo");
        kdsChangesItemDTO.setDiningTableName("diningTableName");
        kdsChangesItemDTO.setCreateStaffName("createStaffName");
        kdsChangesItemDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final KdsItemDTO originalKdsItem = new KdsItemDTO();
        kdsChangesItemDTO.setOriginalKdsItem(originalKdsItem);
        kdsChangesItemDTO.setDeviceIds(new HashSet<>(Arrays.asList("value")));
        final KdsItemDTO changesKdsItem = new KdsItemDTO();
        kdsChangesItemDTO.setChangesKdsItem(changesKdsItem);

        // Configure DeviceConfigService.getOne(...).
        final DeviceConfigDO deviceConfigDO = new DeviceConfigDO();
        deviceConfigDO.setGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        deviceConfigDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        deviceConfigDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        deviceConfigDO.setStoreGuid("storeGuid");
        deviceConfigDO.setPrinterGuid("printerGuid");
        when(mockDeviceConfigService.getOne(any(LambdaQueryWrapper.class))).thenReturn(deviceConfigDO);

        // Configure KdsPrinterService.getOne(...).
        final KdsPrinterDO kdsPrinterDO = new KdsPrinterDO();
        kdsPrinterDO.setGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        kdsPrinterDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrinterDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrinterDO.setPrinterIp("printerIp");
        kdsPrinterDO.setPrinterPort(0);
        kdsPrinterDO.setPageSize(0);
        when(mockKdsPrinterService.getOne(any(LambdaQueryWrapper.class))).thenReturn(kdsPrinterDO);

        when(mockDistributedIdService.nextPrintRecordGuid()).thenReturn("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");

        // Run the test
        kdsPrintRecordServiceImplUnderTest.printChangesItem(kdsChangesItemDTO);

        // Verify the results
        // Confirm KdsPrintCacheService.save(...).
        final KdsPrintRecordReadDO kdsPrintRecordReadDO = new KdsPrintRecordReadDO();
        kdsPrintRecordReadDO.setGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        kdsPrintRecordReadDO.setRecordUid("orderGuid");
        kdsPrintRecordReadDO.setStoreGuid("storeGuid");
        kdsPrintRecordReadDO.setInvoiceType(0);
        kdsPrintRecordReadDO.setPrintContent("printContent");
        final KdsPrinterDO kdsPrinterDO1 = new KdsPrinterDO();
        kdsPrinterDO1.setGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        kdsPrinterDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrinterDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrinterDO1.setPrinterIp("printerIp");
        kdsPrinterDO1.setPrinterPort(0);
        kdsPrinterDO1.setPageSize(0);
        kdsPrintRecordReadDO.setKdsPrinterDO(kdsPrinterDO1);
        final List<KdsPrintRecordReadDO> arrayOfPrintRecordReadDO = Arrays.asList(kdsPrintRecordReadDO);
        verify(mockKdsPrintCacheService).save(arrayOfPrintRecordReadDO);

        // Confirm KdsPrintPushService.pushPrintTaskMsg(...).
        final KdsPrintDTO kdsPrintDTO = new KdsPrintDTO();
        kdsPrintDTO.setInvoiceType(0);
        kdsPrintDTO.setEnterpriseGuid("enterpriseGuid");
        kdsPrintDTO.setStoreGuid("storeGuid");
        kdsPrintDTO.setPrintUid("orderGuid");
        kdsPrintDTO.setOperatorStaffGuid("createStaffGuid");
        kdsPrintDTO.setOperatorStaffName("operatorStaffName");
        kdsPrintDTO.setOperatorStaffAccount("account");
        kdsPrintDTO.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrintDTO.setDeviceId("deviceId");
        final KdsPrintRecordDO kdsPrintRecordDO = new KdsPrintRecordDO();
        kdsPrintRecordDO.setGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        kdsPrintRecordDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrintRecordDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrintRecordDO.setRecordUid("orderGuid");
        kdsPrintRecordDO.setStoreGuid("storeGuid");
        kdsPrintRecordDO.setDeviceId("deviceId");
        kdsPrintRecordDO.setInvoiceType(0);
        kdsPrintRecordDO.setPrinterGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        kdsPrintRecordDO.setPrintStatus(0);
        kdsPrintRecordDO.setPrintStatusMsg("printStatusMsg");
        kdsPrintRecordDO.setPrintContent("printContent");
        kdsPrintRecordDO.setCreateStaffGuid("createStaffGuid");
        final List<KdsPrintRecordDO> arrayOfPrintRecord = Arrays.asList(kdsPrintRecordDO);
        verify(mockKdsPrintPushService).pushPrintTaskMsg(kdsPrintDTO, arrayOfPrintRecord);
    }

    @Test
    public void testPrintChangesItem_KdsPrinterServiceReturnsNull() {
        // Setup
        final KdsChangesItemDTO kdsChangesItemDTO = new KdsChangesItemDTO();
        kdsChangesItemDTO.setDeviceId("deviceId");
        kdsChangesItemDTO.setUserGuid("createStaffGuid");
        kdsChangesItemDTO.setUserName("operatorStaffName");
        kdsChangesItemDTO.setAccount("account");
        kdsChangesItemDTO.setOrderGuid("orderGuid");
        kdsChangesItemDTO.setOrderNo("orderNo");
        kdsChangesItemDTO.setDiningTableName("diningTableName");
        kdsChangesItemDTO.setCreateStaffName("createStaffName");
        kdsChangesItemDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final KdsItemDTO originalKdsItem = new KdsItemDTO();
        kdsChangesItemDTO.setOriginalKdsItem(originalKdsItem);
        kdsChangesItemDTO.setDeviceIds(new HashSet<>(Arrays.asList("value")));
        final KdsItemDTO changesKdsItem = new KdsItemDTO();
        kdsChangesItemDTO.setChangesKdsItem(changesKdsItem);

        // Configure DeviceConfigService.getOne(...).
        final DeviceConfigDO deviceConfigDO = new DeviceConfigDO();
        deviceConfigDO.setGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        deviceConfigDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        deviceConfigDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        deviceConfigDO.setStoreGuid("storeGuid");
        deviceConfigDO.setPrinterGuid("printerGuid");
        when(mockDeviceConfigService.getOne(any(LambdaQueryWrapper.class))).thenReturn(deviceConfigDO);

        when(mockKdsPrinterService.getOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Run the test
        kdsPrintRecordServiceImplUnderTest.printChangesItem(kdsChangesItemDTO);

        // Verify the results
    }

    @Test
    public void testGetPrintOrder() {
        // Setup
        final KdsPrintRecordReqDTO kdsPrintRecordReqDTO = new KdsPrintRecordReqDTO();
        kdsPrintRecordReqDTO.setUserGuid("createStaffGuid");
        kdsPrintRecordReqDTO.setUserName("operatorStaffName");
        kdsPrintRecordReqDTO.setAccount("account");
        kdsPrintRecordReqDTO.setDeviceId("deviceId");
        kdsPrintRecordReqDTO.setRecordGuid("recordGuid");
        kdsPrintRecordReqDTO.setPrintStatus(0);
        kdsPrintRecordReqDTO.setPrintStatusMsg("printStatusMsg");

        final KdsPrintOrderDTO kdsPrintOrderDTO = new KdsPrintOrderDTO();
        kdsPrintOrderDTO.setPrintKey("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        kdsPrintOrderDTO.setPrinterIp("printerIp");
        kdsPrintOrderDTO.setPrinterPort(0);
        kdsPrintOrderDTO.setPrintTimes(0);
        kdsPrintOrderDTO.setPrinterType(0);
        kdsPrintOrderDTO.setPageSize(0);
        final PrintRow printRow = new PrintRow();
        kdsPrintOrderDTO.setPrintRows(Arrays.asList(printRow));
        final List<KdsPrintOrderDTO> expectedResult = Arrays.asList(kdsPrintOrderDTO);

        // Configure KdsPrintMapstruct.printRecordReqToQuery(...).
        final PrintRecordQuery printRecordQuery = new PrintRecordQuery();
        printRecordQuery.setRecordGuid("recordGuid");
        printRecordQuery.setArrayOfRecordGuid(Arrays.asList("value"));
        final KdsPrintRecordReqDTO kdsPrintRecordReqDTO1 = new KdsPrintRecordReqDTO();
        kdsPrintRecordReqDTO1.setUserGuid("createStaffGuid");
        kdsPrintRecordReqDTO1.setUserName("operatorStaffName");
        kdsPrintRecordReqDTO1.setAccount("account");
        kdsPrintRecordReqDTO1.setDeviceId("deviceId");
        kdsPrintRecordReqDTO1.setRecordGuid("recordGuid");
        kdsPrintRecordReqDTO1.setPrintStatus(0);
        kdsPrintRecordReqDTO1.setPrintStatusMsg("printStatusMsg");
        when(mockKdsPrintMapstruct.printRecordReqToQuery(kdsPrintRecordReqDTO1)).thenReturn(printRecordQuery);

        // Configure KdsPrintCacheService.popSingle(...).
        final KdsPrintRecordReadDO kdsPrintRecordReadDO = new KdsPrintRecordReadDO();
        kdsPrintRecordReadDO.setGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        kdsPrintRecordReadDO.setRecordUid("orderGuid");
        kdsPrintRecordReadDO.setStoreGuid("storeGuid");
        kdsPrintRecordReadDO.setInvoiceType(0);
        kdsPrintRecordReadDO.setPrintContent("printContent");
        final KdsPrinterDO kdsPrinterDO = new KdsPrinterDO();
        kdsPrinterDO.setGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        kdsPrinterDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrinterDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrinterDO.setPrinterIp("printerIp");
        kdsPrinterDO.setPrinterPort(0);
        kdsPrinterDO.setPageSize(0);
        kdsPrintRecordReadDO.setKdsPrinterDO(kdsPrinterDO);
        when(mockKdsPrintCacheService.popSingle("recordGuid")).thenReturn(kdsPrintRecordReadDO);

        doReturn(null).when(mockPrintComponentFactory).create(0, 0, "printContent");

        // Configure KdsPrintCacheService.popBatch(...).
        final KdsPrintRecordReadDO kdsPrintRecordReadDO1 = new KdsPrintRecordReadDO();
        kdsPrintRecordReadDO1.setGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        kdsPrintRecordReadDO1.setRecordUid("orderGuid");
        kdsPrintRecordReadDO1.setStoreGuid("storeGuid");
        kdsPrintRecordReadDO1.setInvoiceType(0);
        kdsPrintRecordReadDO1.setPrintContent("printContent");
        final KdsPrinterDO kdsPrinterDO1 = new KdsPrinterDO();
        kdsPrinterDO1.setGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        kdsPrinterDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrinterDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrinterDO1.setPrinterIp("printerIp");
        kdsPrinterDO1.setPrinterPort(0);
        kdsPrinterDO1.setPageSize(0);
        kdsPrintRecordReadDO1.setKdsPrinterDO(kdsPrinterDO1);
        final List<KdsPrintRecordReadDO> kdsPrintRecordReadDOS = Arrays.asList(kdsPrintRecordReadDO1);
        when(mockKdsPrintCacheService.popBatch(Arrays.asList("value"))).thenReturn(kdsPrintRecordReadDOS);

        // Run the test
        final List<KdsPrintOrderDTO> result = kdsPrintRecordServiceImplUnderTest.getPrintOrder(kdsPrintRecordReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetPrintOrder_KdsPrintCacheServicePopSingleReturnsNull() {
        // Setup
        final KdsPrintRecordReqDTO kdsPrintRecordReqDTO = new KdsPrintRecordReqDTO();
        kdsPrintRecordReqDTO.setUserGuid("createStaffGuid");
        kdsPrintRecordReqDTO.setUserName("operatorStaffName");
        kdsPrintRecordReqDTO.setAccount("account");
        kdsPrintRecordReqDTO.setDeviceId("deviceId");
        kdsPrintRecordReqDTO.setRecordGuid("recordGuid");
        kdsPrintRecordReqDTO.setPrintStatus(0);
        kdsPrintRecordReqDTO.setPrintStatusMsg("printStatusMsg");

        final KdsPrintOrderDTO kdsPrintOrderDTO = new KdsPrintOrderDTO();
        kdsPrintOrderDTO.setPrintKey("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        kdsPrintOrderDTO.setPrinterIp("printerIp");
        kdsPrintOrderDTO.setPrinterPort(0);
        kdsPrintOrderDTO.setPrintTimes(0);
        kdsPrintOrderDTO.setPrinterType(0);
        kdsPrintOrderDTO.setPageSize(0);
        final PrintRow printRow = new PrintRow();
        kdsPrintOrderDTO.setPrintRows(Arrays.asList(printRow));
        final List<KdsPrintOrderDTO> expectedResult = Arrays.asList(kdsPrintOrderDTO);

        // Configure KdsPrintMapstruct.printRecordReqToQuery(...).
        final PrintRecordQuery printRecordQuery = new PrintRecordQuery();
        printRecordQuery.setRecordGuid("recordGuid");
        printRecordQuery.setArrayOfRecordGuid(Arrays.asList("value"));
        final KdsPrintRecordReqDTO kdsPrintRecordReqDTO1 = new KdsPrintRecordReqDTO();
        kdsPrintRecordReqDTO1.setUserGuid("createStaffGuid");
        kdsPrintRecordReqDTO1.setUserName("operatorStaffName");
        kdsPrintRecordReqDTO1.setAccount("account");
        kdsPrintRecordReqDTO1.setDeviceId("deviceId");
        kdsPrintRecordReqDTO1.setRecordGuid("recordGuid");
        kdsPrintRecordReqDTO1.setPrintStatus(0);
        kdsPrintRecordReqDTO1.setPrintStatusMsg("printStatusMsg");
        when(mockKdsPrintMapstruct.printRecordReqToQuery(kdsPrintRecordReqDTO1)).thenReturn(printRecordQuery);

        when(mockKdsPrintCacheService.popSingle("recordGuid")).thenReturn(null);
        doReturn(null).when(mockPrintComponentFactory).create(0, 0, "printContent");

        // Run the test
        final List<KdsPrintOrderDTO> result = kdsPrintRecordServiceImplUnderTest.getPrintOrder(kdsPrintRecordReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetPrintOrder_KdsPrintCacheServicePopBatchReturnsNoItems() {
        // Setup
        final KdsPrintRecordReqDTO kdsPrintRecordReqDTO = new KdsPrintRecordReqDTO();
        kdsPrintRecordReqDTO.setUserGuid("createStaffGuid");
        kdsPrintRecordReqDTO.setUserName("operatorStaffName");
        kdsPrintRecordReqDTO.setAccount("account");
        kdsPrintRecordReqDTO.setDeviceId("deviceId");
        kdsPrintRecordReqDTO.setRecordGuid("recordGuid");
        kdsPrintRecordReqDTO.setPrintStatus(0);
        kdsPrintRecordReqDTO.setPrintStatusMsg("printStatusMsg");

        final KdsPrintOrderDTO kdsPrintOrderDTO = new KdsPrintOrderDTO();
        kdsPrintOrderDTO.setPrintKey("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        kdsPrintOrderDTO.setPrinterIp("printerIp");
        kdsPrintOrderDTO.setPrinterPort(0);
        kdsPrintOrderDTO.setPrintTimes(0);
        kdsPrintOrderDTO.setPrinterType(0);
        kdsPrintOrderDTO.setPageSize(0);
        final PrintRow printRow = new PrintRow();
        kdsPrintOrderDTO.setPrintRows(Arrays.asList(printRow));
        final List<KdsPrintOrderDTO> expectedResult = Arrays.asList(kdsPrintOrderDTO);

        // Configure KdsPrintMapstruct.printRecordReqToQuery(...).
        final PrintRecordQuery printRecordQuery = new PrintRecordQuery();
        printRecordQuery.setRecordGuid("recordGuid");
        printRecordQuery.setArrayOfRecordGuid(Arrays.asList("value"));
        final KdsPrintRecordReqDTO kdsPrintRecordReqDTO1 = new KdsPrintRecordReqDTO();
        kdsPrintRecordReqDTO1.setUserGuid("createStaffGuid");
        kdsPrintRecordReqDTO1.setUserName("operatorStaffName");
        kdsPrintRecordReqDTO1.setAccount("account");
        kdsPrintRecordReqDTO1.setDeviceId("deviceId");
        kdsPrintRecordReqDTO1.setRecordGuid("recordGuid");
        kdsPrintRecordReqDTO1.setPrintStatus(0);
        kdsPrintRecordReqDTO1.setPrintStatusMsg("printStatusMsg");
        when(mockKdsPrintMapstruct.printRecordReqToQuery(kdsPrintRecordReqDTO1)).thenReturn(printRecordQuery);

        when(mockKdsPrintCacheService.popBatch(Arrays.asList("value"))).thenReturn(Collections.emptyList());
        doReturn(null).when(mockPrintComponentFactory).create(0, 0, "printContent");

        // Run the test
        final List<KdsPrintOrderDTO> result = kdsPrintRecordServiceImplUnderTest.getPrintOrder(kdsPrintRecordReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testListRecord() {
        // Setup
        final KdsPrintRecordReqDTO kdsPrintRecordReqDTO = new KdsPrintRecordReqDTO();
        kdsPrintRecordReqDTO.setUserGuid("createStaffGuid");
        kdsPrintRecordReqDTO.setUserName("operatorStaffName");
        kdsPrintRecordReqDTO.setAccount("account");
        kdsPrintRecordReqDTO.setDeviceId("deviceId");
        kdsPrintRecordReqDTO.setRecordGuid("recordGuid");
        kdsPrintRecordReqDTO.setPrintStatus(0);
        kdsPrintRecordReqDTO.setPrintStatusMsg("printStatusMsg");

        final KdsPrintRecordDTO kdsPrintRecordDTO = new KdsPrintRecordDTO();
        kdsPrintRecordDTO.setRecordUid("orderGuid");
        kdsPrintRecordDTO.setRecordGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        kdsPrintRecordDTO.setStoreGuid("storeGuid");
        kdsPrintRecordDTO.setDeviceId("deviceId");
        kdsPrintRecordDTO.setInvoiceType(0);
        kdsPrintRecordDTO.setPrinterGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        kdsPrintRecordDTO.setPrintStatus(0);
        final KdsPrintDTO printContent = new KdsPrintDTO();
        printContent.setInvoiceType(0);
        printContent.setEnterpriseGuid("enterpriseGuid");
        printContent.setStoreGuid("storeGuid");
        printContent.setPrintUid("orderGuid");
        printContent.setOperatorStaffGuid("createStaffGuid");
        printContent.setOperatorStaffName("operatorStaffName");
        printContent.setOperatorStaffAccount("account");
        printContent.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        printContent.setDeviceId("deviceId");
        kdsPrintRecordDTO.setPrintContent(printContent);
        kdsPrintRecordDTO.setCreateStaffGuid("createStaffGuid");
        kdsPrintRecordDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrintRecordDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        kdsPrintRecordDTO.setMarkName("");
        kdsPrintRecordDTO.setMarkNo("");
        kdsPrintRecordDTO.setPrinterIp("");
        kdsPrintRecordDTO.setPrinterName("");
        final List<KdsPrintRecordDTO> expectedResult = Arrays.asList(kdsPrintRecordDTO);

        // Run the test
        final List<KdsPrintRecordDTO> result = kdsPrintRecordServiceImplUnderTest.listRecord(kdsPrintRecordReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testUpdatePrintResult() {
        // Setup
        final KdsPrintRecordReqDTO kdsPrintRecordReqDTO = new KdsPrintRecordReqDTO();
        kdsPrintRecordReqDTO.setUserGuid("createStaffGuid");
        kdsPrintRecordReqDTO.setUserName("operatorStaffName");
        kdsPrintRecordReqDTO.setAccount("account");
        kdsPrintRecordReqDTO.setDeviceId("deviceId");
        kdsPrintRecordReqDTO.setRecordGuid("recordGuid");
        kdsPrintRecordReqDTO.setPrintStatus(0);
        kdsPrintRecordReqDTO.setPrintStatusMsg("printStatusMsg");

        // Run the test
        kdsPrintRecordServiceImplUnderTest.updatePrintResult(kdsPrintRecordReqDTO);

        // Verify the results
        // Confirm KdsPrintPushService.pushPrintFailedMsg(...).
        final KdsPrintRecordReqDTO kdsPrintRecordReqDTO1 = new KdsPrintRecordReqDTO();
        kdsPrintRecordReqDTO1.setUserGuid("createStaffGuid");
        kdsPrintRecordReqDTO1.setUserName("operatorStaffName");
        kdsPrintRecordReqDTO1.setAccount("account");
        kdsPrintRecordReqDTO1.setDeviceId("deviceId");
        kdsPrintRecordReqDTO1.setRecordGuid("recordGuid");
        kdsPrintRecordReqDTO1.setPrintStatus(0);
        kdsPrintRecordReqDTO1.setPrintStatusMsg("printStatusMsg");
        final KdsPrintRecordDO printRecordDO = new KdsPrintRecordDO();
        printRecordDO.setGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        printRecordDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        printRecordDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        printRecordDO.setRecordUid("orderGuid");
        printRecordDO.setStoreGuid("storeGuid");
        printRecordDO.setDeviceId("deviceId");
        printRecordDO.setInvoiceType(0);
        printRecordDO.setPrinterGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        printRecordDO.setPrintStatus(0);
        printRecordDO.setPrintStatusMsg("printStatusMsg");
        printRecordDO.setPrintContent("printContent");
        printRecordDO.setCreateStaffGuid("createStaffGuid");
        verify(mockKdsPrintPushService).pushPrintFailedMsg(kdsPrintRecordReqDTO1, printRecordDO, 0);

        // Confirm KdsPrintPushService.pushPrintSucceedMsg(...).
        final KdsPrintRecordReqDTO kdsPrintRecordReqDTO2 = new KdsPrintRecordReqDTO();
        kdsPrintRecordReqDTO2.setUserGuid("createStaffGuid");
        kdsPrintRecordReqDTO2.setUserName("operatorStaffName");
        kdsPrintRecordReqDTO2.setAccount("account");
        kdsPrintRecordReqDTO2.setDeviceId("deviceId");
        kdsPrintRecordReqDTO2.setRecordGuid("recordGuid");
        kdsPrintRecordReqDTO2.setPrintStatus(0);
        kdsPrintRecordReqDTO2.setPrintStatusMsg("printStatusMsg");
        final KdsPrintRecordDO printRecordDO1 = new KdsPrintRecordDO();
        printRecordDO1.setGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        printRecordDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        printRecordDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        printRecordDO1.setRecordUid("orderGuid");
        printRecordDO1.setStoreGuid("storeGuid");
        printRecordDO1.setDeviceId("deviceId");
        printRecordDO1.setInvoiceType(0);
        printRecordDO1.setPrinterGuid("9b5db549-2aa3-453b-bb45-bebbb2bf64e6");
        printRecordDO1.setPrintStatus(0);
        printRecordDO1.setPrintStatusMsg("printStatusMsg");
        printRecordDO1.setPrintContent("printContent");
        printRecordDO1.setCreateStaffGuid("createStaffGuid");
        verify(mockKdsPrintPushService).pushPrintSucceedMsg(kdsPrintRecordReqDTO2, printRecordDO1, 0);
    }

    @Test
    public void testDeleteRecord() {
        // Setup
        final KdsPrintRecordReqDTO kdsPrintRecordReqDTO = new KdsPrintRecordReqDTO();
        kdsPrintRecordReqDTO.setUserGuid("createStaffGuid");
        kdsPrintRecordReqDTO.setUserName("operatorStaffName");
        kdsPrintRecordReqDTO.setAccount("account");
        kdsPrintRecordReqDTO.setDeviceId("deviceId");
        kdsPrintRecordReqDTO.setRecordGuid("recordGuid");
        kdsPrintRecordReqDTO.setPrintStatus(0);
        kdsPrintRecordReqDTO.setPrintStatusMsg("printStatusMsg");

        // Run the test
        kdsPrintRecordServiceImplUnderTest.deleteRecord(kdsPrintRecordReqDTO);

        // Verify the results
    }

    @Test
    public void testBatchDeleteRecord() {
        // Setup
        // Run the test
        kdsPrintRecordServiceImplUnderTest.batchDeleteRecord(Arrays.asList("value"));

        // Verify the results
    }
}
