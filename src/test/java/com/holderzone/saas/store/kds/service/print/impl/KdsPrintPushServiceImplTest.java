package com.holderzone.saas.store.kds.service.print.impl;

import com.holderzone.framework.base.dto.message.MessageDTO;
import com.holderzone.saas.store.dto.kds.req.KdsPrintDTO;
import com.holderzone.saas.store.dto.kds.req.KdsPrintRecordReqDTO;
import com.holderzone.saas.store.kds.entity.domain.KdsPrintRecordDO;
import com.holderzone.saas.store.kds.service.rpc.MsgRpcService;
import com.holderzone.saas.store.kds.service.template.PrintComponentFactory;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.verify;

@RunWith(MockitoJUnitRunner.class)
public class KdsPrintPushServiceImplTest {

    @Mock
    private MsgRpcService mockMsgRpcService;
    @Mock
    private PrintComponentFactory mockPrintComponentFactory;

    private KdsPrintPushServiceImpl kdsPrintPushServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        kdsPrintPushServiceImplUnderTest = new KdsPrintPushServiceImpl(mockMsgRpcService, mockPrintComponentFactory);
        ReflectionTestUtils.setField(kdsPrintPushServiceImplUnderTest, "batchPushEnable", false);
    }

    @Test
    public void testPushPrintTaskMsg() {
        // Setup
        final KdsPrintDTO kdsPrintDTO = new KdsPrintDTO();
        kdsPrintDTO.setInvoiceType(0);
        kdsPrintDTO.setEnterpriseGuid("enterpriseGuid");
        kdsPrintDTO.setStoreGuid("storeGuid");
        kdsPrintDTO.setPrintUid("printUid");
        kdsPrintDTO.setOperatorStaffGuid("operatorStaffGuid");

        final KdsPrintRecordDO kdsPrintRecordDO = new KdsPrintRecordDO();
        kdsPrintRecordDO.setGuid("16510061-1b48-4a26-8219-a90dec5be7f7");
        kdsPrintRecordDO.setRecordUid("recordUid");
        kdsPrintRecordDO.setDeviceId("deviceId");
        kdsPrintRecordDO.setInvoiceType(0);
        kdsPrintRecordDO.setPrintContent("printContent");
        final List<KdsPrintRecordDO> arrayOfPrintRecord = Arrays.asList(kdsPrintRecordDO);

        // Run the test
        kdsPrintPushServiceImplUnderTest.pushPrintTaskMsg(kdsPrintDTO, arrayOfPrintRecord);

        // Verify the results
        verify(mockMsgRpcService).sendPrintMessage(any(MessageDTO.class));
    }

    @Test
    public void testPushPrintSucceedMsg() {
        // Setup
        final KdsPrintRecordReqDTO kdsPrintRecordReqDTO = new KdsPrintRecordReqDTO();
        kdsPrintRecordReqDTO.setDeviceId("deviceId");
        kdsPrintRecordReqDTO.setRecordGuid("recordGuid");
        kdsPrintRecordReqDTO.setPrintStatus(0);
        kdsPrintRecordReqDTO.setPrintStatusMsg("printStatusMsg");
        kdsPrintRecordReqDTO.setArrayOfRecordGuid(Arrays.asList("value"));

        final KdsPrintRecordDO kdsPrintRecordDO = new KdsPrintRecordDO();
        kdsPrintRecordDO.setGuid("16510061-1b48-4a26-8219-a90dec5be7f7");
        kdsPrintRecordDO.setRecordUid("recordUid");
        kdsPrintRecordDO.setDeviceId("deviceId");
        kdsPrintRecordDO.setInvoiceType(0);
        kdsPrintRecordDO.setPrintContent("printContent");

        // Run the test
        kdsPrintPushServiceImplUnderTest.pushPrintSucceedMsg(kdsPrintRecordReqDTO, kdsPrintRecordDO, 0);

        // Verify the results
        verify(mockMsgRpcService).sendPrintMessage(any(MessageDTO.class));
    }

    @Test
    public void testPushPrintFailedMsg() {
        // Setup
        final KdsPrintRecordReqDTO kdsPrintRecordReqDTO = new KdsPrintRecordReqDTO();
        kdsPrintRecordReqDTO.setDeviceId("deviceId");
        kdsPrintRecordReqDTO.setRecordGuid("recordGuid");
        kdsPrintRecordReqDTO.setPrintStatus(0);
        kdsPrintRecordReqDTO.setPrintStatusMsg("printStatusMsg");
        kdsPrintRecordReqDTO.setArrayOfRecordGuid(Arrays.asList("value"));

        final KdsPrintRecordDO kdsPrintRecordDO = new KdsPrintRecordDO();
        kdsPrintRecordDO.setGuid("16510061-1b48-4a26-8219-a90dec5be7f7");
        kdsPrintRecordDO.setRecordUid("recordUid");
        kdsPrintRecordDO.setDeviceId("deviceId");
        kdsPrintRecordDO.setInvoiceType(0);
        kdsPrintRecordDO.setPrintContent("printContent");

        doReturn(null).when(mockPrintComponentFactory).create(0, "printContent");

        // Run the test
        kdsPrintPushServiceImplUnderTest.pushPrintFailedMsg(kdsPrintRecordReqDTO, kdsPrintRecordDO, 0);

        // Verify the results
        verify(mockMsgRpcService).sendPrintMessage(any(MessageDTO.class));
    }
}
