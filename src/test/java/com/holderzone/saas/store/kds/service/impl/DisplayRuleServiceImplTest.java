package com.holderzone.saas.store.kds.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.kds.req.DisplayRuleQueryDTO;
import com.holderzone.saas.store.dto.kds.req.DisplayRuleSaveOrUpdateDTO;
import com.holderzone.saas.store.dto.kds.resp.DisplayItemRespDTO;
import com.holderzone.saas.store.dto.kds.resp.DisplayRuleRespDTO;
import com.holderzone.saas.store.dto.kds.resp.DisplayStoreRespDTO;
import com.holderzone.saas.store.kds.entity.domain.DisplayItemDO;
import com.holderzone.saas.store.kds.entity.domain.DisplayRuleDO;
import com.holderzone.saas.store.kds.entity.domain.DisplayStoreDO;
import com.holderzone.saas.store.kds.mapper.DisplayRuleMapper;
import com.holderzone.saas.store.kds.mapstruct.DisplayRuleMapstruct;
import com.holderzone.saas.store.kds.service.DisplayItemService;
import com.holderzone.saas.store.kds.service.DisplayStoreService;
import com.holderzone.saas.store.kds.service.DistributedIdService;
import com.holderzone.saas.store.kds.service.rpc.StoreClientService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class DisplayRuleServiceImplTest {

    @Mock
    private DisplayRuleMapper mockRuleMapper;
    @Mock
    private DisplayStoreService mockStoreService;
    @Mock
    private DisplayItemService mockItemService;
    @Mock
    private DisplayRuleMapstruct mockRuleMapstruct;
    @Mock
    private DistributedIdService mockDistributedIdService;

    private DisplayRuleServiceImpl displayRuleServiceImplUnderTest;

    @Mock
    private StoreClientService storeClient;

    @Before
    public void setUp() {
        displayRuleServiceImplUnderTest = new DisplayRuleServiceImpl(mockRuleMapper, mockStoreService, mockItemService,
                mockRuleMapstruct, mockDistributedIdService, storeClient);
    }

    @Test
    public void testSaveRule() {
        // Setup
        final DisplayRuleSaveOrUpdateDTO reqDTO = new DisplayRuleSaveOrUpdateDTO();
        reqDTO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        reqDTO.setDisplayState(0);
        reqDTO.setDelayTime(0);
        reqDTO.setBatch(0);
        reqDTO.setEffectiveState(0);
        reqDTO.setEffectiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO.setIsAllStore(false);
        reqDTO.setRuleType(0);
        final DisplayStoreRespDTO displayStoreRespDTO = new DisplayStoreRespDTO();
        displayStoreRespDTO.setStoreGuid("storeGuid");
        displayStoreRespDTO.setStoreName("storeName");
        reqDTO.setStoreList(Arrays.asList(displayStoreRespDTO));
        final DisplayItemRespDTO displayItemRespDTO = new DisplayItemRespDTO();
        displayItemRespDTO.setItemGuid("itemGuid");
        displayItemRespDTO.setItemName("itemName");
        reqDTO.setItemList(Arrays.asList(displayItemRespDTO));
        reqDTO.setBrandGuid("brandGuid");

        when(mockDistributedIdService.nextDisplayRuleGuid()).thenReturn("805f0a9e-d573-49ad-8183-f5a37fb61668");

        // Configure DisplayItemService.listItem(...).
        final DisplayItemRespDTO displayItemRespDTO1 = new DisplayItemRespDTO();
        displayItemRespDTO1.setItemGuid("itemGuid");
        displayItemRespDTO1.setItemName("itemName");
        displayItemRespDTO1.setSort(0);
        displayItemRespDTO1.setRuleType(0);
        final List<DisplayItemRespDTO> displayItemRespDTOS = Arrays.asList(displayItemRespDTO1);
        final DisplayRuleQueryDTO reqDTO1 = new DisplayRuleQueryDTO();
        reqDTO1.setCurrentPage(0L);
        reqDTO1.setPageSize(0L);
        reqDTO1.setRuleGuid("ruleGuid");
        reqDTO1.setRuleType(0);
        reqDTO1.setBrandGuid("brandGuid");
        when(mockItemService.listItem(reqDTO1)).thenReturn(displayItemRespDTOS);

        // Configure DisplayRuleMapstruct.toItemDOList(...).
        final DisplayItemDO displayItemDO = new DisplayItemDO();
        displayItemDO.setGuid("7e5ba61b-f27d-4746-abc1-480b55cad459");
        displayItemDO.setIsDelete(0);
        displayItemDO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayItemDO.setItemGuid("itemGuid");
        displayItemDO.setSort(0);
        displayItemDO.setRuleType(0);
        final List<DisplayItemDO> displayItemDOS = Arrays.asList(displayItemDO);
        final DisplayItemRespDTO displayItemRespDTO2 = new DisplayItemRespDTO();
        displayItemRespDTO2.setItemGuid("itemGuid");
        displayItemRespDTO2.setItemName("itemName");
        displayItemRespDTO2.setSort(0);
        displayItemRespDTO2.setRuleType(0);
        final List<DisplayItemRespDTO> itemList = Arrays.asList(displayItemRespDTO2);
        when(mockRuleMapstruct.toItemDOList(itemList)).thenReturn(displayItemDOS);

        when(mockDistributedIdService.nextBatchDisplayItemGuid(0L)).thenReturn(Arrays.asList("value"));
        when(mockItemService.count(any(LambdaQueryWrapper.class))).thenReturn(0);

        // Configure DisplayItemService.saveBatch(...).
        final DisplayItemDO displayItemDO1 = new DisplayItemDO();
        displayItemDO1.setGuid("7e5ba61b-f27d-4746-abc1-480b55cad459");
        displayItemDO1.setIsDelete(0);
        displayItemDO1.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayItemDO1.setItemGuid("itemGuid");
        displayItemDO1.setSort(0);
        displayItemDO1.setRuleType(0);
        final List<DisplayItemDO> entityList = Arrays.asList(displayItemDO1);
        when(mockItemService.saveBatch(entityList)).thenReturn(false);

        // Configure DisplayStoreService.listStore(...).
        final DisplayStoreRespDTO displayStoreRespDTO1 = new DisplayStoreRespDTO();
        displayStoreRespDTO1.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO1.setRuleGuid("ruleGuid");
        displayStoreRespDTO1.setStoreGuid("storeGuid");
        displayStoreRespDTO1.setStoreName("storeName");
        displayStoreRespDTO1.setRuleType(0);
        final List<DisplayStoreRespDTO> displayStoreRespDTOS = Arrays.asList(displayStoreRespDTO1);
        final DisplayRuleQueryDTO reqDTO2 = new DisplayRuleQueryDTO();
        reqDTO2.setCurrentPage(0L);
        reqDTO2.setPageSize(0L);
        reqDTO2.setRuleGuid("ruleGuid");
        reqDTO2.setRuleType(0);
        reqDTO2.setBrandGuid("brandGuid");
        when(mockStoreService.listStore(reqDTO2)).thenReturn(displayStoreRespDTOS);

        // Configure DisplayRuleMapstruct.toStoreDOList(...).
        final DisplayStoreDO displayStoreDO = new DisplayStoreDO();
        displayStoreDO.setId(0L);
        displayStoreDO.setGuid("ded0f5ab-d2f9-478e-8348-d38f33b9516b");
        displayStoreDO.setIsDelete(0);
        displayStoreDO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayStoreDO.setRuleType(0);
        final List<DisplayStoreDO> displayStoreDOS = Arrays.asList(displayStoreDO);
        final DisplayStoreRespDTO displayStoreRespDTO2 = new DisplayStoreRespDTO();
        displayStoreRespDTO2.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO2.setRuleGuid("ruleGuid");
        displayStoreRespDTO2.setStoreGuid("storeGuid");
        displayStoreRespDTO2.setStoreName("storeName");
        displayStoreRespDTO2.setRuleType(0);
        final List<DisplayStoreRespDTO> storeList = Arrays.asList(displayStoreRespDTO2);
        when(mockRuleMapstruct.toStoreDOList(storeList)).thenReturn(displayStoreDOS);

        when(mockDistributedIdService.nextBatchDisplayStoreGuid(0L)).thenReturn(Arrays.asList("value"));

        // Configure DisplayStoreService.saveBatch(...).
        final DisplayStoreDO displayStoreDO1 = new DisplayStoreDO();
        displayStoreDO1.setId(0L);
        displayStoreDO1.setGuid("ded0f5ab-d2f9-478e-8348-d38f33b9516b");
        displayStoreDO1.setIsDelete(0);
        displayStoreDO1.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayStoreDO1.setRuleType(0);
        final List<DisplayStoreDO> entityList1 = Arrays.asList(displayStoreDO1);
        when(mockStoreService.saveBatch(entityList1)).thenReturn(false);

        // Configure DisplayRuleMapstruct.ruleReqToRuleDO(...).
        final DisplayRuleDO displayRuleDO = new DisplayRuleDO();
        displayRuleDO.setGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayRuleDO.setIsDelete(0);
        displayRuleDO.setDelayTime(0);
        displayRuleDO.setBatch(0);
        displayRuleDO.setIsAllStore(false);
        displayRuleDO.setRuleType(0);
        displayRuleDO.setBrandGuid("brandGuid");
        final DisplayRuleSaveOrUpdateDTO ruleReqDTO = new DisplayRuleSaveOrUpdateDTO();
        ruleReqDTO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        ruleReqDTO.setDisplayState(0);
        ruleReqDTO.setDelayTime(0);
        ruleReqDTO.setBatch(0);
        ruleReqDTO.setEffectiveState(0);
        ruleReqDTO.setEffectiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        ruleReqDTO.setIsAllStore(false);
        ruleReqDTO.setRuleType(0);
        final DisplayStoreRespDTO displayStoreRespDTO3 = new DisplayStoreRespDTO();
        displayStoreRespDTO3.setStoreGuid("storeGuid");
        displayStoreRespDTO3.setStoreName("storeName");
        ruleReqDTO.setStoreList(Arrays.asList(displayStoreRespDTO3));
        final DisplayItemRespDTO displayItemRespDTO3 = new DisplayItemRespDTO();
        displayItemRespDTO3.setItemGuid("itemGuid");
        displayItemRespDTO3.setItemName("itemName");
        ruleReqDTO.setItemList(Arrays.asList(displayItemRespDTO3));
        ruleReqDTO.setBrandGuid("brandGuid");
        when(mockRuleMapstruct.ruleReqToRuleDO(ruleReqDTO)).thenReturn(displayRuleDO);

        // Run the test
        final Boolean result = displayRuleServiceImplUnderTest.saveRule(reqDTO);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testSaveRule_DisplayItemServiceListItemReturnsNoItems() {
        // Setup
        final DisplayRuleSaveOrUpdateDTO reqDTO = new DisplayRuleSaveOrUpdateDTO();
        reqDTO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        reqDTO.setDisplayState(0);
        reqDTO.setDelayTime(0);
        reqDTO.setBatch(0);
        reqDTO.setEffectiveState(0);
        reqDTO.setEffectiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO.setIsAllStore(false);
        reqDTO.setRuleType(0);
        final DisplayStoreRespDTO displayStoreRespDTO = new DisplayStoreRespDTO();
        displayStoreRespDTO.setStoreGuid("storeGuid");
        displayStoreRespDTO.setStoreName("storeName");
        reqDTO.setStoreList(Arrays.asList(displayStoreRespDTO));
        final DisplayItemRespDTO displayItemRespDTO = new DisplayItemRespDTO();
        displayItemRespDTO.setItemGuid("itemGuid");
        displayItemRespDTO.setItemName("itemName");
        reqDTO.setItemList(Arrays.asList(displayItemRespDTO));
        reqDTO.setBrandGuid("brandGuid");

        when(mockDistributedIdService.nextDisplayRuleGuid()).thenReturn("805f0a9e-d573-49ad-8183-f5a37fb61668");

        // Configure DisplayItemService.listItem(...).
        final DisplayRuleQueryDTO reqDTO1 = new DisplayRuleQueryDTO();
        reqDTO1.setCurrentPage(0L);
        reqDTO1.setPageSize(0L);
        reqDTO1.setRuleGuid("ruleGuid");
        reqDTO1.setRuleType(0);
        reqDTO1.setBrandGuid("brandGuid");
        when(mockItemService.listItem(reqDTO1)).thenReturn(Collections.emptyList());

        // Configure DisplayRuleMapstruct.toItemDOList(...).
        final DisplayItemDO displayItemDO = new DisplayItemDO();
        displayItemDO.setGuid("7e5ba61b-f27d-4746-abc1-480b55cad459");
        displayItemDO.setIsDelete(0);
        displayItemDO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayItemDO.setItemGuid("itemGuid");
        displayItemDO.setSort(0);
        displayItemDO.setRuleType(0);
        final List<DisplayItemDO> displayItemDOS = Arrays.asList(displayItemDO);
        final DisplayItemRespDTO displayItemRespDTO1 = new DisplayItemRespDTO();
        displayItemRespDTO1.setItemGuid("itemGuid");
        displayItemRespDTO1.setItemName("itemName");
        displayItemRespDTO1.setSort(0);
        displayItemRespDTO1.setRuleType(0);
        final List<DisplayItemRespDTO> itemList = Arrays.asList(displayItemRespDTO1);
        when(mockRuleMapstruct.toItemDOList(itemList)).thenReturn(displayItemDOS);

        when(mockDistributedIdService.nextBatchDisplayItemGuid(0L)).thenReturn(Arrays.asList("value"));
        when(mockItemService.count(any(LambdaQueryWrapper.class))).thenReturn(0);

        // Configure DisplayItemService.saveBatch(...).
        final DisplayItemDO displayItemDO1 = new DisplayItemDO();
        displayItemDO1.setGuid("7e5ba61b-f27d-4746-abc1-480b55cad459");
        displayItemDO1.setIsDelete(0);
        displayItemDO1.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayItemDO1.setItemGuid("itemGuid");
        displayItemDO1.setSort(0);
        displayItemDO1.setRuleType(0);
        final List<DisplayItemDO> entityList = Arrays.asList(displayItemDO1);
        when(mockItemService.saveBatch(entityList)).thenReturn(false);

        // Configure DisplayStoreService.listStore(...).
        final DisplayStoreRespDTO displayStoreRespDTO1 = new DisplayStoreRespDTO();
        displayStoreRespDTO1.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO1.setRuleGuid("ruleGuid");
        displayStoreRespDTO1.setStoreGuid("storeGuid");
        displayStoreRespDTO1.setStoreName("storeName");
        displayStoreRespDTO1.setRuleType(0);
        final List<DisplayStoreRespDTO> displayStoreRespDTOS = Arrays.asList(displayStoreRespDTO1);
        final DisplayRuleQueryDTO reqDTO2 = new DisplayRuleQueryDTO();
        reqDTO2.setCurrentPage(0L);
        reqDTO2.setPageSize(0L);
        reqDTO2.setRuleGuid("ruleGuid");
        reqDTO2.setRuleType(0);
        reqDTO2.setBrandGuid("brandGuid");
        when(mockStoreService.listStore(reqDTO2)).thenReturn(displayStoreRespDTOS);

        // Configure DisplayRuleMapstruct.toStoreDOList(...).
        final DisplayStoreDO displayStoreDO = new DisplayStoreDO();
        displayStoreDO.setId(0L);
        displayStoreDO.setGuid("ded0f5ab-d2f9-478e-8348-d38f33b9516b");
        displayStoreDO.setIsDelete(0);
        displayStoreDO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayStoreDO.setRuleType(0);
        final List<DisplayStoreDO> displayStoreDOS = Arrays.asList(displayStoreDO);
        final DisplayStoreRespDTO displayStoreRespDTO2 = new DisplayStoreRespDTO();
        displayStoreRespDTO2.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO2.setRuleGuid("ruleGuid");
        displayStoreRespDTO2.setStoreGuid("storeGuid");
        displayStoreRespDTO2.setStoreName("storeName");
        displayStoreRespDTO2.setRuleType(0);
        final List<DisplayStoreRespDTO> storeList = Arrays.asList(displayStoreRespDTO2);
        when(mockRuleMapstruct.toStoreDOList(storeList)).thenReturn(displayStoreDOS);

        when(mockDistributedIdService.nextBatchDisplayStoreGuid(0L)).thenReturn(Arrays.asList("value"));

        // Configure DisplayStoreService.saveBatch(...).
        final DisplayStoreDO displayStoreDO1 = new DisplayStoreDO();
        displayStoreDO1.setId(0L);
        displayStoreDO1.setGuid("ded0f5ab-d2f9-478e-8348-d38f33b9516b");
        displayStoreDO1.setIsDelete(0);
        displayStoreDO1.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayStoreDO1.setRuleType(0);
        final List<DisplayStoreDO> entityList1 = Arrays.asList(displayStoreDO1);
        when(mockStoreService.saveBatch(entityList1)).thenReturn(false);

        // Configure DisplayRuleMapstruct.ruleReqToRuleDO(...).
        final DisplayRuleDO displayRuleDO = new DisplayRuleDO();
        displayRuleDO.setGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayRuleDO.setIsDelete(0);
        displayRuleDO.setDelayTime(0);
        displayRuleDO.setBatch(0);
        displayRuleDO.setIsAllStore(false);
        displayRuleDO.setRuleType(0);
        displayRuleDO.setBrandGuid("brandGuid");
        final DisplayRuleSaveOrUpdateDTO ruleReqDTO = new DisplayRuleSaveOrUpdateDTO();
        ruleReqDTO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        ruleReqDTO.setDisplayState(0);
        ruleReqDTO.setDelayTime(0);
        ruleReqDTO.setBatch(0);
        ruleReqDTO.setEffectiveState(0);
        ruleReqDTO.setEffectiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        ruleReqDTO.setIsAllStore(false);
        ruleReqDTO.setRuleType(0);
        final DisplayStoreRespDTO displayStoreRespDTO3 = new DisplayStoreRespDTO();
        displayStoreRespDTO3.setStoreGuid("storeGuid");
        displayStoreRespDTO3.setStoreName("storeName");
        ruleReqDTO.setStoreList(Arrays.asList(displayStoreRespDTO3));
        final DisplayItemRespDTO displayItemRespDTO2 = new DisplayItemRespDTO();
        displayItemRespDTO2.setItemGuid("itemGuid");
        displayItemRespDTO2.setItemName("itemName");
        ruleReqDTO.setItemList(Arrays.asList(displayItemRespDTO2));
        ruleReqDTO.setBrandGuid("brandGuid");
        when(mockRuleMapstruct.ruleReqToRuleDO(ruleReqDTO)).thenReturn(displayRuleDO);

        // Run the test
        final Boolean result = displayRuleServiceImplUnderTest.saveRule(reqDTO);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testSaveRule_DisplayRuleMapstructToItemDOListReturnsNoItems() {
        // Setup
        final DisplayRuleSaveOrUpdateDTO reqDTO = new DisplayRuleSaveOrUpdateDTO();
        reqDTO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        reqDTO.setDisplayState(0);
        reqDTO.setDelayTime(0);
        reqDTO.setBatch(0);
        reqDTO.setEffectiveState(0);
        reqDTO.setEffectiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO.setIsAllStore(false);
        reqDTO.setRuleType(0);
        final DisplayStoreRespDTO displayStoreRespDTO = new DisplayStoreRespDTO();
        displayStoreRespDTO.setStoreGuid("storeGuid");
        displayStoreRespDTO.setStoreName("storeName");
        reqDTO.setStoreList(Arrays.asList(displayStoreRespDTO));
        final DisplayItemRespDTO displayItemRespDTO = new DisplayItemRespDTO();
        displayItemRespDTO.setItemGuid("itemGuid");
        displayItemRespDTO.setItemName("itemName");
        reqDTO.setItemList(Arrays.asList(displayItemRespDTO));
        reqDTO.setBrandGuid("brandGuid");

        when(mockDistributedIdService.nextDisplayRuleGuid()).thenReturn("805f0a9e-d573-49ad-8183-f5a37fb61668");

        // Configure DisplayItemService.listItem(...).
        final DisplayItemRespDTO displayItemRespDTO1 = new DisplayItemRespDTO();
        displayItemRespDTO1.setItemGuid("itemGuid");
        displayItemRespDTO1.setItemName("itemName");
        displayItemRespDTO1.setSort(0);
        displayItemRespDTO1.setRuleType(0);
        final List<DisplayItemRespDTO> displayItemRespDTOS = Arrays.asList(displayItemRespDTO1);
        final DisplayRuleQueryDTO reqDTO1 = new DisplayRuleQueryDTO();
        reqDTO1.setCurrentPage(0L);
        reqDTO1.setPageSize(0L);
        reqDTO1.setRuleGuid("ruleGuid");
        reqDTO1.setRuleType(0);
        reqDTO1.setBrandGuid("brandGuid");
        when(mockItemService.listItem(reqDTO1)).thenReturn(displayItemRespDTOS);

        // Configure DisplayRuleMapstruct.toItemDOList(...).
        final DisplayItemRespDTO displayItemRespDTO2 = new DisplayItemRespDTO();
        displayItemRespDTO2.setItemGuid("itemGuid");
        displayItemRespDTO2.setItemName("itemName");
        displayItemRespDTO2.setSort(0);
        displayItemRespDTO2.setRuleType(0);
        final List<DisplayItemRespDTO> itemList = Arrays.asList(displayItemRespDTO2);
        when(mockRuleMapstruct.toItemDOList(itemList)).thenReturn(Collections.emptyList());

        when(mockDistributedIdService.nextBatchDisplayItemGuid(0L)).thenReturn(Arrays.asList("value"));

        // Configure DisplayItemService.saveBatch(...).
        final DisplayItemDO displayItemDO = new DisplayItemDO();
        displayItemDO.setGuid("7e5ba61b-f27d-4746-abc1-480b55cad459");
        displayItemDO.setIsDelete(0);
        displayItemDO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayItemDO.setItemGuid("itemGuid");
        displayItemDO.setSort(0);
        displayItemDO.setRuleType(0);
        final List<DisplayItemDO> entityList = Arrays.asList(displayItemDO);
        when(mockItemService.saveBatch(entityList)).thenReturn(false);

        // Configure DisplayStoreService.listStore(...).
        final DisplayStoreRespDTO displayStoreRespDTO1 = new DisplayStoreRespDTO();
        displayStoreRespDTO1.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO1.setRuleGuid("ruleGuid");
        displayStoreRespDTO1.setStoreGuid("storeGuid");
        displayStoreRespDTO1.setStoreName("storeName");
        displayStoreRespDTO1.setRuleType(0);
        final List<DisplayStoreRespDTO> displayStoreRespDTOS = Arrays.asList(displayStoreRespDTO1);
        final DisplayRuleQueryDTO reqDTO2 = new DisplayRuleQueryDTO();
        reqDTO2.setCurrentPage(0L);
        reqDTO2.setPageSize(0L);
        reqDTO2.setRuleGuid("ruleGuid");
        reqDTO2.setRuleType(0);
        reqDTO2.setBrandGuid("brandGuid");
        when(mockStoreService.listStore(reqDTO2)).thenReturn(displayStoreRespDTOS);

        // Configure DisplayRuleMapstruct.toStoreDOList(...).
        final DisplayStoreDO displayStoreDO = new DisplayStoreDO();
        displayStoreDO.setId(0L);
        displayStoreDO.setGuid("ded0f5ab-d2f9-478e-8348-d38f33b9516b");
        displayStoreDO.setIsDelete(0);
        displayStoreDO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayStoreDO.setRuleType(0);
        final List<DisplayStoreDO> displayStoreDOS = Arrays.asList(displayStoreDO);
        final DisplayStoreRespDTO displayStoreRespDTO2 = new DisplayStoreRespDTO();
        displayStoreRespDTO2.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO2.setRuleGuid("ruleGuid");
        displayStoreRespDTO2.setStoreGuid("storeGuid");
        displayStoreRespDTO2.setStoreName("storeName");
        displayStoreRespDTO2.setRuleType(0);
        final List<DisplayStoreRespDTO> storeList = Arrays.asList(displayStoreRespDTO2);
        when(mockRuleMapstruct.toStoreDOList(storeList)).thenReturn(displayStoreDOS);

        when(mockDistributedIdService.nextBatchDisplayStoreGuid(0L)).thenReturn(Arrays.asList("value"));

        // Configure DisplayStoreService.saveBatch(...).
        final DisplayStoreDO displayStoreDO1 = new DisplayStoreDO();
        displayStoreDO1.setId(0L);
        displayStoreDO1.setGuid("ded0f5ab-d2f9-478e-8348-d38f33b9516b");
        displayStoreDO1.setIsDelete(0);
        displayStoreDO1.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayStoreDO1.setRuleType(0);
        final List<DisplayStoreDO> entityList1 = Arrays.asList(displayStoreDO1);
        when(mockStoreService.saveBatch(entityList1)).thenReturn(false);

        // Configure DisplayRuleMapstruct.ruleReqToRuleDO(...).
        final DisplayRuleDO displayRuleDO = new DisplayRuleDO();
        displayRuleDO.setGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayRuleDO.setIsDelete(0);
        displayRuleDO.setDelayTime(0);
        displayRuleDO.setBatch(0);
        displayRuleDO.setIsAllStore(false);
        displayRuleDO.setRuleType(0);
        displayRuleDO.setBrandGuid("brandGuid");
        final DisplayRuleSaveOrUpdateDTO ruleReqDTO = new DisplayRuleSaveOrUpdateDTO();
        ruleReqDTO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        ruleReqDTO.setDisplayState(0);
        ruleReqDTO.setDelayTime(0);
        ruleReqDTO.setBatch(0);
        ruleReqDTO.setEffectiveState(0);
        ruleReqDTO.setEffectiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        ruleReqDTO.setIsAllStore(false);
        ruleReqDTO.setRuleType(0);
        final DisplayStoreRespDTO displayStoreRespDTO3 = new DisplayStoreRespDTO();
        displayStoreRespDTO3.setStoreGuid("storeGuid");
        displayStoreRespDTO3.setStoreName("storeName");
        ruleReqDTO.setStoreList(Arrays.asList(displayStoreRespDTO3));
        final DisplayItemRespDTO displayItemRespDTO3 = new DisplayItemRespDTO();
        displayItemRespDTO3.setItemGuid("itemGuid");
        displayItemRespDTO3.setItemName("itemName");
        ruleReqDTO.setItemList(Arrays.asList(displayItemRespDTO3));
        ruleReqDTO.setBrandGuid("brandGuid");
        when(mockRuleMapstruct.ruleReqToRuleDO(ruleReqDTO)).thenReturn(displayRuleDO);

        // Run the test
        final Boolean result = displayRuleServiceImplUnderTest.saveRule(reqDTO);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testSaveRule_DistributedIdServiceNextBatchDisplayItemGuidReturnsNoItems() {
        // Setup
        final DisplayRuleSaveOrUpdateDTO reqDTO = new DisplayRuleSaveOrUpdateDTO();
        reqDTO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        reqDTO.setDisplayState(0);
        reqDTO.setDelayTime(0);
        reqDTO.setBatch(0);
        reqDTO.setEffectiveState(0);
        reqDTO.setEffectiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO.setIsAllStore(false);
        reqDTO.setRuleType(0);
        final DisplayStoreRespDTO displayStoreRespDTO = new DisplayStoreRespDTO();
        displayStoreRespDTO.setStoreGuid("storeGuid");
        displayStoreRespDTO.setStoreName("storeName");
        reqDTO.setStoreList(Arrays.asList(displayStoreRespDTO));
        final DisplayItemRespDTO displayItemRespDTO = new DisplayItemRespDTO();
        displayItemRespDTO.setItemGuid("itemGuid");
        displayItemRespDTO.setItemName("itemName");
        reqDTO.setItemList(Arrays.asList(displayItemRespDTO));
        reqDTO.setBrandGuid("brandGuid");

        when(mockDistributedIdService.nextDisplayRuleGuid()).thenReturn("805f0a9e-d573-49ad-8183-f5a37fb61668");

        // Configure DisplayItemService.listItem(...).
        final DisplayItemRespDTO displayItemRespDTO1 = new DisplayItemRespDTO();
        displayItemRespDTO1.setItemGuid("itemGuid");
        displayItemRespDTO1.setItemName("itemName");
        displayItemRespDTO1.setSort(0);
        displayItemRespDTO1.setRuleType(0);
        final List<DisplayItemRespDTO> displayItemRespDTOS = Arrays.asList(displayItemRespDTO1);
        final DisplayRuleQueryDTO reqDTO1 = new DisplayRuleQueryDTO();
        reqDTO1.setCurrentPage(0L);
        reqDTO1.setPageSize(0L);
        reqDTO1.setRuleGuid("ruleGuid");
        reqDTO1.setRuleType(0);
        reqDTO1.setBrandGuid("brandGuid");
        when(mockItemService.listItem(reqDTO1)).thenReturn(displayItemRespDTOS);

        // Configure DisplayRuleMapstruct.toItemDOList(...).
        final DisplayItemDO displayItemDO = new DisplayItemDO();
        displayItemDO.setGuid("7e5ba61b-f27d-4746-abc1-480b55cad459");
        displayItemDO.setIsDelete(0);
        displayItemDO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayItemDO.setItemGuid("itemGuid");
        displayItemDO.setSort(0);
        displayItemDO.setRuleType(0);
        final List<DisplayItemDO> displayItemDOS = Arrays.asList(displayItemDO);
        final DisplayItemRespDTO displayItemRespDTO2 = new DisplayItemRespDTO();
        displayItemRespDTO2.setItemGuid("itemGuid");
        displayItemRespDTO2.setItemName("itemName");
        displayItemRespDTO2.setSort(0);
        displayItemRespDTO2.setRuleType(0);
        final List<DisplayItemRespDTO> itemList = Arrays.asList(displayItemRespDTO2);
        when(mockRuleMapstruct.toItemDOList(itemList)).thenReturn(displayItemDOS);

        when(mockDistributedIdService.nextBatchDisplayItemGuid(0L)).thenReturn(Collections.emptyList());
        when(mockItemService.count(any(LambdaQueryWrapper.class))).thenReturn(0);

        // Configure DisplayItemService.saveBatch(...).
        final DisplayItemDO displayItemDO1 = new DisplayItemDO();
        displayItemDO1.setGuid("7e5ba61b-f27d-4746-abc1-480b55cad459");
        displayItemDO1.setIsDelete(0);
        displayItemDO1.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayItemDO1.setItemGuid("itemGuid");
        displayItemDO1.setSort(0);
        displayItemDO1.setRuleType(0);
        final List<DisplayItemDO> entityList = Arrays.asList(displayItemDO1);
        when(mockItemService.saveBatch(entityList)).thenReturn(false);

        // Configure DisplayStoreService.listStore(...).
        final DisplayStoreRespDTO displayStoreRespDTO1 = new DisplayStoreRespDTO();
        displayStoreRespDTO1.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO1.setRuleGuid("ruleGuid");
        displayStoreRespDTO1.setStoreGuid("storeGuid");
        displayStoreRespDTO1.setStoreName("storeName");
        displayStoreRespDTO1.setRuleType(0);
        final List<DisplayStoreRespDTO> displayStoreRespDTOS = Arrays.asList(displayStoreRespDTO1);
        final DisplayRuleQueryDTO reqDTO2 = new DisplayRuleQueryDTO();
        reqDTO2.setCurrentPage(0L);
        reqDTO2.setPageSize(0L);
        reqDTO2.setRuleGuid("ruleGuid");
        reqDTO2.setRuleType(0);
        reqDTO2.setBrandGuid("brandGuid");
        when(mockStoreService.listStore(reqDTO2)).thenReturn(displayStoreRespDTOS);

        // Configure DisplayRuleMapstruct.toStoreDOList(...).
        final DisplayStoreDO displayStoreDO = new DisplayStoreDO();
        displayStoreDO.setId(0L);
        displayStoreDO.setGuid("ded0f5ab-d2f9-478e-8348-d38f33b9516b");
        displayStoreDO.setIsDelete(0);
        displayStoreDO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayStoreDO.setRuleType(0);
        final List<DisplayStoreDO> displayStoreDOS = Arrays.asList(displayStoreDO);
        final DisplayStoreRespDTO displayStoreRespDTO2 = new DisplayStoreRespDTO();
        displayStoreRespDTO2.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO2.setRuleGuid("ruleGuid");
        displayStoreRespDTO2.setStoreGuid("storeGuid");
        displayStoreRespDTO2.setStoreName("storeName");
        displayStoreRespDTO2.setRuleType(0);
        final List<DisplayStoreRespDTO> storeList = Arrays.asList(displayStoreRespDTO2);
        when(mockRuleMapstruct.toStoreDOList(storeList)).thenReturn(displayStoreDOS);

        when(mockDistributedIdService.nextBatchDisplayStoreGuid(0L)).thenReturn(Arrays.asList("value"));

        // Configure DisplayStoreService.saveBatch(...).
        final DisplayStoreDO displayStoreDO1 = new DisplayStoreDO();
        displayStoreDO1.setId(0L);
        displayStoreDO1.setGuid("ded0f5ab-d2f9-478e-8348-d38f33b9516b");
        displayStoreDO1.setIsDelete(0);
        displayStoreDO1.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayStoreDO1.setRuleType(0);
        final List<DisplayStoreDO> entityList1 = Arrays.asList(displayStoreDO1);
        when(mockStoreService.saveBatch(entityList1)).thenReturn(false);

        // Configure DisplayRuleMapstruct.ruleReqToRuleDO(...).
        final DisplayRuleDO displayRuleDO = new DisplayRuleDO();
        displayRuleDO.setGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayRuleDO.setIsDelete(0);
        displayRuleDO.setDelayTime(0);
        displayRuleDO.setBatch(0);
        displayRuleDO.setIsAllStore(false);
        displayRuleDO.setRuleType(0);
        displayRuleDO.setBrandGuid("brandGuid");
        final DisplayRuleSaveOrUpdateDTO ruleReqDTO = new DisplayRuleSaveOrUpdateDTO();
        ruleReqDTO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        ruleReqDTO.setDisplayState(0);
        ruleReqDTO.setDelayTime(0);
        ruleReqDTO.setBatch(0);
        ruleReqDTO.setEffectiveState(0);
        ruleReqDTO.setEffectiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        ruleReqDTO.setIsAllStore(false);
        ruleReqDTO.setRuleType(0);
        final DisplayStoreRespDTO displayStoreRespDTO3 = new DisplayStoreRespDTO();
        displayStoreRespDTO3.setStoreGuid("storeGuid");
        displayStoreRespDTO3.setStoreName("storeName");
        ruleReqDTO.setStoreList(Arrays.asList(displayStoreRespDTO3));
        final DisplayItemRespDTO displayItemRespDTO3 = new DisplayItemRespDTO();
        displayItemRespDTO3.setItemGuid("itemGuid");
        displayItemRespDTO3.setItemName("itemName");
        ruleReqDTO.setItemList(Arrays.asList(displayItemRespDTO3));
        ruleReqDTO.setBrandGuid("brandGuid");
        when(mockRuleMapstruct.ruleReqToRuleDO(ruleReqDTO)).thenReturn(displayRuleDO);

        // Run the test
        final Boolean result = displayRuleServiceImplUnderTest.saveRule(reqDTO);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testSaveRule_DisplayItemServiceSaveBatchReturnsTrue() {
        // Setup
        final DisplayRuleSaveOrUpdateDTO reqDTO = new DisplayRuleSaveOrUpdateDTO();
        reqDTO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        reqDTO.setDisplayState(0);
        reqDTO.setDelayTime(0);
        reqDTO.setBatch(0);
        reqDTO.setEffectiveState(0);
        reqDTO.setEffectiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO.setIsAllStore(false);
        reqDTO.setRuleType(0);
        final DisplayStoreRespDTO displayStoreRespDTO = new DisplayStoreRespDTO();
        displayStoreRespDTO.setStoreGuid("storeGuid");
        displayStoreRespDTO.setStoreName("storeName");
        reqDTO.setStoreList(Arrays.asList(displayStoreRespDTO));
        final DisplayItemRespDTO displayItemRespDTO = new DisplayItemRespDTO();
        displayItemRespDTO.setItemGuid("itemGuid");
        displayItemRespDTO.setItemName("itemName");
        reqDTO.setItemList(Arrays.asList(displayItemRespDTO));
        reqDTO.setBrandGuid("brandGuid");

        when(mockDistributedIdService.nextDisplayRuleGuid()).thenReturn("805f0a9e-d573-49ad-8183-f5a37fb61668");

        // Configure DisplayItemService.listItem(...).
        final DisplayItemRespDTO displayItemRespDTO1 = new DisplayItemRespDTO();
        displayItemRespDTO1.setItemGuid("itemGuid");
        displayItemRespDTO1.setItemName("itemName");
        displayItemRespDTO1.setSort(0);
        displayItemRespDTO1.setRuleType(0);
        final List<DisplayItemRespDTO> displayItemRespDTOS = Arrays.asList(displayItemRespDTO1);
        final DisplayRuleQueryDTO reqDTO1 = new DisplayRuleQueryDTO();
        reqDTO1.setCurrentPage(0L);
        reqDTO1.setPageSize(0L);
        reqDTO1.setRuleGuid("ruleGuid");
        reqDTO1.setRuleType(0);
        reqDTO1.setBrandGuid("brandGuid");
        when(mockItemService.listItem(reqDTO1)).thenReturn(displayItemRespDTOS);

        // Configure DisplayRuleMapstruct.toItemDOList(...).
        final DisplayItemDO displayItemDO = new DisplayItemDO();
        displayItemDO.setGuid("7e5ba61b-f27d-4746-abc1-480b55cad459");
        displayItemDO.setIsDelete(0);
        displayItemDO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayItemDO.setItemGuid("itemGuid");
        displayItemDO.setSort(0);
        displayItemDO.setRuleType(0);
        final List<DisplayItemDO> displayItemDOS = Arrays.asList(displayItemDO);
        final DisplayItemRespDTO displayItemRespDTO2 = new DisplayItemRespDTO();
        displayItemRespDTO2.setItemGuid("itemGuid");
        displayItemRespDTO2.setItemName("itemName");
        displayItemRespDTO2.setSort(0);
        displayItemRespDTO2.setRuleType(0);
        final List<DisplayItemRespDTO> itemList = Arrays.asList(displayItemRespDTO2);
        when(mockRuleMapstruct.toItemDOList(itemList)).thenReturn(displayItemDOS);

        when(mockDistributedIdService.nextBatchDisplayItemGuid(0L)).thenReturn(Arrays.asList("value"));
        when(mockItemService.count(any(LambdaQueryWrapper.class))).thenReturn(0);

        // Configure DisplayItemService.saveBatch(...).
        final DisplayItemDO displayItemDO1 = new DisplayItemDO();
        displayItemDO1.setGuid("7e5ba61b-f27d-4746-abc1-480b55cad459");
        displayItemDO1.setIsDelete(0);
        displayItemDO1.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayItemDO1.setItemGuid("itemGuid");
        displayItemDO1.setSort(0);
        displayItemDO1.setRuleType(0);
        final List<DisplayItemDO> entityList = Arrays.asList(displayItemDO1);
        when(mockItemService.saveBatch(entityList)).thenReturn(true);

        // Configure DisplayStoreService.listStore(...).
        final DisplayStoreRespDTO displayStoreRespDTO1 = new DisplayStoreRespDTO();
        displayStoreRespDTO1.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO1.setRuleGuid("ruleGuid");
        displayStoreRespDTO1.setStoreGuid("storeGuid");
        displayStoreRespDTO1.setStoreName("storeName");
        displayStoreRespDTO1.setRuleType(0);
        final List<DisplayStoreRespDTO> displayStoreRespDTOS = Arrays.asList(displayStoreRespDTO1);
        final DisplayRuleQueryDTO reqDTO2 = new DisplayRuleQueryDTO();
        reqDTO2.setCurrentPage(0L);
        reqDTO2.setPageSize(0L);
        reqDTO2.setRuleGuid("ruleGuid");
        reqDTO2.setRuleType(0);
        reqDTO2.setBrandGuid("brandGuid");
        when(mockStoreService.listStore(reqDTO2)).thenReturn(displayStoreRespDTOS);

        // Configure DisplayRuleMapstruct.toStoreDOList(...).
        final DisplayStoreDO displayStoreDO = new DisplayStoreDO();
        displayStoreDO.setId(0L);
        displayStoreDO.setGuid("ded0f5ab-d2f9-478e-8348-d38f33b9516b");
        displayStoreDO.setIsDelete(0);
        displayStoreDO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayStoreDO.setRuleType(0);
        final List<DisplayStoreDO> displayStoreDOS = Arrays.asList(displayStoreDO);
        final DisplayStoreRespDTO displayStoreRespDTO2 = new DisplayStoreRespDTO();
        displayStoreRespDTO2.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO2.setRuleGuid("ruleGuid");
        displayStoreRespDTO2.setStoreGuid("storeGuid");
        displayStoreRespDTO2.setStoreName("storeName");
        displayStoreRespDTO2.setRuleType(0);
        final List<DisplayStoreRespDTO> storeList = Arrays.asList(displayStoreRespDTO2);
        when(mockRuleMapstruct.toStoreDOList(storeList)).thenReturn(displayStoreDOS);

        when(mockDistributedIdService.nextBatchDisplayStoreGuid(0L)).thenReturn(Arrays.asList("value"));

        // Configure DisplayStoreService.saveBatch(...).
        final DisplayStoreDO displayStoreDO1 = new DisplayStoreDO();
        displayStoreDO1.setId(0L);
        displayStoreDO1.setGuid("ded0f5ab-d2f9-478e-8348-d38f33b9516b");
        displayStoreDO1.setIsDelete(0);
        displayStoreDO1.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayStoreDO1.setRuleType(0);
        final List<DisplayStoreDO> entityList1 = Arrays.asList(displayStoreDO1);
        when(mockStoreService.saveBatch(entityList1)).thenReturn(false);

        // Configure DisplayRuleMapstruct.ruleReqToRuleDO(...).
        final DisplayRuleDO displayRuleDO = new DisplayRuleDO();
        displayRuleDO.setGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayRuleDO.setIsDelete(0);
        displayRuleDO.setDelayTime(0);
        displayRuleDO.setBatch(0);
        displayRuleDO.setIsAllStore(false);
        displayRuleDO.setRuleType(0);
        displayRuleDO.setBrandGuid("brandGuid");
        final DisplayRuleSaveOrUpdateDTO ruleReqDTO = new DisplayRuleSaveOrUpdateDTO();
        ruleReqDTO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        ruleReqDTO.setDisplayState(0);
        ruleReqDTO.setDelayTime(0);
        ruleReqDTO.setBatch(0);
        ruleReqDTO.setEffectiveState(0);
        ruleReqDTO.setEffectiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        ruleReqDTO.setIsAllStore(false);
        ruleReqDTO.setRuleType(0);
        final DisplayStoreRespDTO displayStoreRespDTO3 = new DisplayStoreRespDTO();
        displayStoreRespDTO3.setStoreGuid("storeGuid");
        displayStoreRespDTO3.setStoreName("storeName");
        ruleReqDTO.setStoreList(Arrays.asList(displayStoreRespDTO3));
        final DisplayItemRespDTO displayItemRespDTO3 = new DisplayItemRespDTO();
        displayItemRespDTO3.setItemGuid("itemGuid");
        displayItemRespDTO3.setItemName("itemName");
        ruleReqDTO.setItemList(Arrays.asList(displayItemRespDTO3));
        ruleReqDTO.setBrandGuid("brandGuid");
        when(mockRuleMapstruct.ruleReqToRuleDO(ruleReqDTO)).thenReturn(displayRuleDO);

        // Run the test
        final Boolean result = displayRuleServiceImplUnderTest.saveRule(reqDTO);

        // Verify the results
        assertThat(result).isTrue();
    }

    @Test
    public void testSaveRule_DisplayStoreServiceListStoreReturnsNoItems() {
        // Setup
        final DisplayRuleSaveOrUpdateDTO reqDTO = new DisplayRuleSaveOrUpdateDTO();
        reqDTO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        reqDTO.setDisplayState(0);
        reqDTO.setDelayTime(0);
        reqDTO.setBatch(0);
        reqDTO.setEffectiveState(0);
        reqDTO.setEffectiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO.setIsAllStore(false);
        reqDTO.setRuleType(0);
        final DisplayStoreRespDTO displayStoreRespDTO = new DisplayStoreRespDTO();
        displayStoreRespDTO.setStoreGuid("storeGuid");
        displayStoreRespDTO.setStoreName("storeName");
        reqDTO.setStoreList(Arrays.asList(displayStoreRespDTO));
        final DisplayItemRespDTO displayItemRespDTO = new DisplayItemRespDTO();
        displayItemRespDTO.setItemGuid("itemGuid");
        displayItemRespDTO.setItemName("itemName");
        reqDTO.setItemList(Arrays.asList(displayItemRespDTO));
        reqDTO.setBrandGuid("brandGuid");

        when(mockDistributedIdService.nextDisplayRuleGuid()).thenReturn("805f0a9e-d573-49ad-8183-f5a37fb61668");

        // Configure DisplayItemService.listItem(...).
        final DisplayItemRespDTO displayItemRespDTO1 = new DisplayItemRespDTO();
        displayItemRespDTO1.setItemGuid("itemGuid");
        displayItemRespDTO1.setItemName("itemName");
        displayItemRespDTO1.setSort(0);
        displayItemRespDTO1.setRuleType(0);
        final List<DisplayItemRespDTO> displayItemRespDTOS = Arrays.asList(displayItemRespDTO1);
        final DisplayRuleQueryDTO reqDTO1 = new DisplayRuleQueryDTO();
        reqDTO1.setCurrentPage(0L);
        reqDTO1.setPageSize(0L);
        reqDTO1.setRuleGuid("ruleGuid");
        reqDTO1.setRuleType(0);
        reqDTO1.setBrandGuid("brandGuid");
        when(mockItemService.listItem(reqDTO1)).thenReturn(displayItemRespDTOS);

        // Configure DisplayRuleMapstruct.toItemDOList(...).
        final DisplayItemDO displayItemDO = new DisplayItemDO();
        displayItemDO.setGuid("7e5ba61b-f27d-4746-abc1-480b55cad459");
        displayItemDO.setIsDelete(0);
        displayItemDO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayItemDO.setItemGuid("itemGuid");
        displayItemDO.setSort(0);
        displayItemDO.setRuleType(0);
        final List<DisplayItemDO> displayItemDOS = Arrays.asList(displayItemDO);
        final DisplayItemRespDTO displayItemRespDTO2 = new DisplayItemRespDTO();
        displayItemRespDTO2.setItemGuid("itemGuid");
        displayItemRespDTO2.setItemName("itemName");
        displayItemRespDTO2.setSort(0);
        displayItemRespDTO2.setRuleType(0);
        final List<DisplayItemRespDTO> itemList = Arrays.asList(displayItemRespDTO2);
        when(mockRuleMapstruct.toItemDOList(itemList)).thenReturn(displayItemDOS);

        when(mockDistributedIdService.nextBatchDisplayItemGuid(0L)).thenReturn(Arrays.asList("value"));
        when(mockItemService.count(any(LambdaQueryWrapper.class))).thenReturn(0);

        // Configure DisplayItemService.saveBatch(...).
        final DisplayItemDO displayItemDO1 = new DisplayItemDO();
        displayItemDO1.setGuid("7e5ba61b-f27d-4746-abc1-480b55cad459");
        displayItemDO1.setIsDelete(0);
        displayItemDO1.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayItemDO1.setItemGuid("itemGuid");
        displayItemDO1.setSort(0);
        displayItemDO1.setRuleType(0);
        final List<DisplayItemDO> entityList = Arrays.asList(displayItemDO1);
        when(mockItemService.saveBatch(entityList)).thenReturn(false);

        // Configure DisplayStoreService.listStore(...).
        final DisplayRuleQueryDTO reqDTO2 = new DisplayRuleQueryDTO();
        reqDTO2.setCurrentPage(0L);
        reqDTO2.setPageSize(0L);
        reqDTO2.setRuleGuid("ruleGuid");
        reqDTO2.setRuleType(0);
        reqDTO2.setBrandGuid("brandGuid");
        when(mockStoreService.listStore(reqDTO2)).thenReturn(Collections.emptyList());

        // Configure DisplayRuleMapstruct.toStoreDOList(...).
        final DisplayStoreDO displayStoreDO = new DisplayStoreDO();
        displayStoreDO.setId(0L);
        displayStoreDO.setGuid("ded0f5ab-d2f9-478e-8348-d38f33b9516b");
        displayStoreDO.setIsDelete(0);
        displayStoreDO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayStoreDO.setRuleType(0);
        final List<DisplayStoreDO> displayStoreDOS = Arrays.asList(displayStoreDO);
        final DisplayStoreRespDTO displayStoreRespDTO1 = new DisplayStoreRespDTO();
        displayStoreRespDTO1.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO1.setRuleGuid("ruleGuid");
        displayStoreRespDTO1.setStoreGuid("storeGuid");
        displayStoreRespDTO1.setStoreName("storeName");
        displayStoreRespDTO1.setRuleType(0);
        final List<DisplayStoreRespDTO> storeList = Arrays.asList(displayStoreRespDTO1);
        when(mockRuleMapstruct.toStoreDOList(storeList)).thenReturn(displayStoreDOS);

        when(mockDistributedIdService.nextBatchDisplayStoreGuid(0L)).thenReturn(Arrays.asList("value"));

        // Configure DisplayStoreService.saveBatch(...).
        final DisplayStoreDO displayStoreDO1 = new DisplayStoreDO();
        displayStoreDO1.setId(0L);
        displayStoreDO1.setGuid("ded0f5ab-d2f9-478e-8348-d38f33b9516b");
        displayStoreDO1.setIsDelete(0);
        displayStoreDO1.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayStoreDO1.setRuleType(0);
        final List<DisplayStoreDO> entityList1 = Arrays.asList(displayStoreDO1);
        when(mockStoreService.saveBatch(entityList1)).thenReturn(false);

        // Configure DisplayRuleMapstruct.ruleReqToRuleDO(...).
        final DisplayRuleDO displayRuleDO = new DisplayRuleDO();
        displayRuleDO.setGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayRuleDO.setIsDelete(0);
        displayRuleDO.setDelayTime(0);
        displayRuleDO.setBatch(0);
        displayRuleDO.setIsAllStore(false);
        displayRuleDO.setRuleType(0);
        displayRuleDO.setBrandGuid("brandGuid");
        final DisplayRuleSaveOrUpdateDTO ruleReqDTO = new DisplayRuleSaveOrUpdateDTO();
        ruleReqDTO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        ruleReqDTO.setDisplayState(0);
        ruleReqDTO.setDelayTime(0);
        ruleReqDTO.setBatch(0);
        ruleReqDTO.setEffectiveState(0);
        ruleReqDTO.setEffectiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        ruleReqDTO.setIsAllStore(false);
        ruleReqDTO.setRuleType(0);
        final DisplayStoreRespDTO displayStoreRespDTO2 = new DisplayStoreRespDTO();
        displayStoreRespDTO2.setStoreGuid("storeGuid");
        displayStoreRespDTO2.setStoreName("storeName");
        ruleReqDTO.setStoreList(Arrays.asList(displayStoreRespDTO2));
        final DisplayItemRespDTO displayItemRespDTO3 = new DisplayItemRespDTO();
        displayItemRespDTO3.setItemGuid("itemGuid");
        displayItemRespDTO3.setItemName("itemName");
        ruleReqDTO.setItemList(Arrays.asList(displayItemRespDTO3));
        ruleReqDTO.setBrandGuid("brandGuid");
        when(mockRuleMapstruct.ruleReqToRuleDO(ruleReqDTO)).thenReturn(displayRuleDO);

        // Run the test
        final Boolean result = displayRuleServiceImplUnderTest.saveRule(reqDTO);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testSaveRule_DisplayRuleMapstructToStoreDOListReturnsNoItems() {
        // Setup
        final DisplayRuleSaveOrUpdateDTO reqDTO = new DisplayRuleSaveOrUpdateDTO();
        reqDTO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        reqDTO.setDisplayState(0);
        reqDTO.setDelayTime(0);
        reqDTO.setBatch(0);
        reqDTO.setEffectiveState(0);
        reqDTO.setEffectiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO.setIsAllStore(false);
        reqDTO.setRuleType(0);
        final DisplayStoreRespDTO displayStoreRespDTO = new DisplayStoreRespDTO();
        displayStoreRespDTO.setStoreGuid("storeGuid");
        displayStoreRespDTO.setStoreName("storeName");
        reqDTO.setStoreList(Arrays.asList(displayStoreRespDTO));
        final DisplayItemRespDTO displayItemRespDTO = new DisplayItemRespDTO();
        displayItemRespDTO.setItemGuid("itemGuid");
        displayItemRespDTO.setItemName("itemName");
        reqDTO.setItemList(Arrays.asList(displayItemRespDTO));
        reqDTO.setBrandGuid("brandGuid");

        when(mockDistributedIdService.nextDisplayRuleGuid()).thenReturn("805f0a9e-d573-49ad-8183-f5a37fb61668");

        // Configure DisplayItemService.listItem(...).
        final DisplayItemRespDTO displayItemRespDTO1 = new DisplayItemRespDTO();
        displayItemRespDTO1.setItemGuid("itemGuid");
        displayItemRespDTO1.setItemName("itemName");
        displayItemRespDTO1.setSort(0);
        displayItemRespDTO1.setRuleType(0);
        final List<DisplayItemRespDTO> displayItemRespDTOS = Arrays.asList(displayItemRespDTO1);
        final DisplayRuleQueryDTO reqDTO1 = new DisplayRuleQueryDTO();
        reqDTO1.setCurrentPage(0L);
        reqDTO1.setPageSize(0L);
        reqDTO1.setRuleGuid("ruleGuid");
        reqDTO1.setRuleType(0);
        reqDTO1.setBrandGuid("brandGuid");
        when(mockItemService.listItem(reqDTO1)).thenReturn(displayItemRespDTOS);

        // Configure DisplayRuleMapstruct.toItemDOList(...).
        final DisplayItemDO displayItemDO = new DisplayItemDO();
        displayItemDO.setGuid("7e5ba61b-f27d-4746-abc1-480b55cad459");
        displayItemDO.setIsDelete(0);
        displayItemDO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayItemDO.setItemGuid("itemGuid");
        displayItemDO.setSort(0);
        displayItemDO.setRuleType(0);
        final List<DisplayItemDO> displayItemDOS = Arrays.asList(displayItemDO);
        final DisplayItemRespDTO displayItemRespDTO2 = new DisplayItemRespDTO();
        displayItemRespDTO2.setItemGuid("itemGuid");
        displayItemRespDTO2.setItemName("itemName");
        displayItemRespDTO2.setSort(0);
        displayItemRespDTO2.setRuleType(0);
        final List<DisplayItemRespDTO> itemList = Arrays.asList(displayItemRespDTO2);
        when(mockRuleMapstruct.toItemDOList(itemList)).thenReturn(displayItemDOS);

        when(mockDistributedIdService.nextBatchDisplayItemGuid(0L)).thenReturn(Arrays.asList("value"));
        when(mockItemService.count(any(LambdaQueryWrapper.class))).thenReturn(0);

        // Configure DisplayItemService.saveBatch(...).
        final DisplayItemDO displayItemDO1 = new DisplayItemDO();
        displayItemDO1.setGuid("7e5ba61b-f27d-4746-abc1-480b55cad459");
        displayItemDO1.setIsDelete(0);
        displayItemDO1.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayItemDO1.setItemGuid("itemGuid");
        displayItemDO1.setSort(0);
        displayItemDO1.setRuleType(0);
        final List<DisplayItemDO> entityList = Arrays.asList(displayItemDO1);
        when(mockItemService.saveBatch(entityList)).thenReturn(false);

        // Configure DisplayStoreService.listStore(...).
        final DisplayStoreRespDTO displayStoreRespDTO1 = new DisplayStoreRespDTO();
        displayStoreRespDTO1.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO1.setRuleGuid("ruleGuid");
        displayStoreRespDTO1.setStoreGuid("storeGuid");
        displayStoreRespDTO1.setStoreName("storeName");
        displayStoreRespDTO1.setRuleType(0);
        final List<DisplayStoreRespDTO> displayStoreRespDTOS = Arrays.asList(displayStoreRespDTO1);
        final DisplayRuleQueryDTO reqDTO2 = new DisplayRuleQueryDTO();
        reqDTO2.setCurrentPage(0L);
        reqDTO2.setPageSize(0L);
        reqDTO2.setRuleGuid("ruleGuid");
        reqDTO2.setRuleType(0);
        reqDTO2.setBrandGuid("brandGuid");
        when(mockStoreService.listStore(reqDTO2)).thenReturn(displayStoreRespDTOS);

        // Configure DisplayRuleMapstruct.toStoreDOList(...).
        final DisplayStoreRespDTO displayStoreRespDTO2 = new DisplayStoreRespDTO();
        displayStoreRespDTO2.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO2.setRuleGuid("ruleGuid");
        displayStoreRespDTO2.setStoreGuid("storeGuid");
        displayStoreRespDTO2.setStoreName("storeName");
        displayStoreRespDTO2.setRuleType(0);
        final List<DisplayStoreRespDTO> storeList = Arrays.asList(displayStoreRespDTO2);
        when(mockRuleMapstruct.toStoreDOList(storeList)).thenReturn(Collections.emptyList());

        when(mockDistributedIdService.nextBatchDisplayStoreGuid(0L)).thenReturn(Arrays.asList("value"));

        // Configure DisplayStoreService.saveBatch(...).
        final DisplayStoreDO displayStoreDO = new DisplayStoreDO();
        displayStoreDO.setId(0L);
        displayStoreDO.setGuid("ded0f5ab-d2f9-478e-8348-d38f33b9516b");
        displayStoreDO.setIsDelete(0);
        displayStoreDO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayStoreDO.setRuleType(0);
        final List<DisplayStoreDO> entityList1 = Arrays.asList(displayStoreDO);
        when(mockStoreService.saveBatch(entityList1)).thenReturn(false);

        // Configure DisplayRuleMapstruct.ruleReqToRuleDO(...).
        final DisplayRuleDO displayRuleDO = new DisplayRuleDO();
        displayRuleDO.setGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayRuleDO.setIsDelete(0);
        displayRuleDO.setDelayTime(0);
        displayRuleDO.setBatch(0);
        displayRuleDO.setIsAllStore(false);
        displayRuleDO.setRuleType(0);
        displayRuleDO.setBrandGuid("brandGuid");
        final DisplayRuleSaveOrUpdateDTO ruleReqDTO = new DisplayRuleSaveOrUpdateDTO();
        ruleReqDTO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        ruleReqDTO.setDisplayState(0);
        ruleReqDTO.setDelayTime(0);
        ruleReqDTO.setBatch(0);
        ruleReqDTO.setEffectiveState(0);
        ruleReqDTO.setEffectiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        ruleReqDTO.setIsAllStore(false);
        ruleReqDTO.setRuleType(0);
        final DisplayStoreRespDTO displayStoreRespDTO3 = new DisplayStoreRespDTO();
        displayStoreRespDTO3.setStoreGuid("storeGuid");
        displayStoreRespDTO3.setStoreName("storeName");
        ruleReqDTO.setStoreList(Arrays.asList(displayStoreRespDTO3));
        final DisplayItemRespDTO displayItemRespDTO3 = new DisplayItemRespDTO();
        displayItemRespDTO3.setItemGuid("itemGuid");
        displayItemRespDTO3.setItemName("itemName");
        ruleReqDTO.setItemList(Arrays.asList(displayItemRespDTO3));
        ruleReqDTO.setBrandGuid("brandGuid");
        when(mockRuleMapstruct.ruleReqToRuleDO(ruleReqDTO)).thenReturn(displayRuleDO);

        // Run the test
        final Boolean result = displayRuleServiceImplUnderTest.saveRule(reqDTO);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testSaveRule_DistributedIdServiceNextBatchDisplayStoreGuidReturnsNoItems() {
        // Setup
        final DisplayRuleSaveOrUpdateDTO reqDTO = new DisplayRuleSaveOrUpdateDTO();
        reqDTO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        reqDTO.setDisplayState(0);
        reqDTO.setDelayTime(0);
        reqDTO.setBatch(0);
        reqDTO.setEffectiveState(0);
        reqDTO.setEffectiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO.setIsAllStore(false);
        reqDTO.setRuleType(0);
        final DisplayStoreRespDTO displayStoreRespDTO = new DisplayStoreRespDTO();
        displayStoreRespDTO.setStoreGuid("storeGuid");
        displayStoreRespDTO.setStoreName("storeName");
        reqDTO.setStoreList(Arrays.asList(displayStoreRespDTO));
        final DisplayItemRespDTO displayItemRespDTO = new DisplayItemRespDTO();
        displayItemRespDTO.setItemGuid("itemGuid");
        displayItemRespDTO.setItemName("itemName");
        reqDTO.setItemList(Arrays.asList(displayItemRespDTO));
        reqDTO.setBrandGuid("brandGuid");

        when(mockDistributedIdService.nextDisplayRuleGuid()).thenReturn("805f0a9e-d573-49ad-8183-f5a37fb61668");

        // Configure DisplayItemService.listItem(...).
        final DisplayItemRespDTO displayItemRespDTO1 = new DisplayItemRespDTO();
        displayItemRespDTO1.setItemGuid("itemGuid");
        displayItemRespDTO1.setItemName("itemName");
        displayItemRespDTO1.setSort(0);
        displayItemRespDTO1.setRuleType(0);
        final List<DisplayItemRespDTO> displayItemRespDTOS = Arrays.asList(displayItemRespDTO1);
        final DisplayRuleQueryDTO reqDTO1 = new DisplayRuleQueryDTO();
        reqDTO1.setCurrentPage(0L);
        reqDTO1.setPageSize(0L);
        reqDTO1.setRuleGuid("ruleGuid");
        reqDTO1.setRuleType(0);
        reqDTO1.setBrandGuid("brandGuid");
        when(mockItemService.listItem(reqDTO1)).thenReturn(displayItemRespDTOS);

        // Configure DisplayRuleMapstruct.toItemDOList(...).
        final DisplayItemDO displayItemDO = new DisplayItemDO();
        displayItemDO.setGuid("7e5ba61b-f27d-4746-abc1-480b55cad459");
        displayItemDO.setIsDelete(0);
        displayItemDO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayItemDO.setItemGuid("itemGuid");
        displayItemDO.setSort(0);
        displayItemDO.setRuleType(0);
        final List<DisplayItemDO> displayItemDOS = Arrays.asList(displayItemDO);
        final DisplayItemRespDTO displayItemRespDTO2 = new DisplayItemRespDTO();
        displayItemRespDTO2.setItemGuid("itemGuid");
        displayItemRespDTO2.setItemName("itemName");
        displayItemRespDTO2.setSort(0);
        displayItemRespDTO2.setRuleType(0);
        final List<DisplayItemRespDTO> itemList = Arrays.asList(displayItemRespDTO2);
        when(mockRuleMapstruct.toItemDOList(itemList)).thenReturn(displayItemDOS);

        when(mockDistributedIdService.nextBatchDisplayItemGuid(0L)).thenReturn(Arrays.asList("value"));
        when(mockItemService.count(any(LambdaQueryWrapper.class))).thenReturn(0);

        // Configure DisplayItemService.saveBatch(...).
        final DisplayItemDO displayItemDO1 = new DisplayItemDO();
        displayItemDO1.setGuid("7e5ba61b-f27d-4746-abc1-480b55cad459");
        displayItemDO1.setIsDelete(0);
        displayItemDO1.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayItemDO1.setItemGuid("itemGuid");
        displayItemDO1.setSort(0);
        displayItemDO1.setRuleType(0);
        final List<DisplayItemDO> entityList = Arrays.asList(displayItemDO1);
        when(mockItemService.saveBatch(entityList)).thenReturn(false);

        // Configure DisplayStoreService.listStore(...).
        final DisplayStoreRespDTO displayStoreRespDTO1 = new DisplayStoreRespDTO();
        displayStoreRespDTO1.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO1.setRuleGuid("ruleGuid");
        displayStoreRespDTO1.setStoreGuid("storeGuid");
        displayStoreRespDTO1.setStoreName("storeName");
        displayStoreRespDTO1.setRuleType(0);
        final List<DisplayStoreRespDTO> displayStoreRespDTOS = Arrays.asList(displayStoreRespDTO1);
        final DisplayRuleQueryDTO reqDTO2 = new DisplayRuleQueryDTO();
        reqDTO2.setCurrentPage(0L);
        reqDTO2.setPageSize(0L);
        reqDTO2.setRuleGuid("ruleGuid");
        reqDTO2.setRuleType(0);
        reqDTO2.setBrandGuid("brandGuid");
        when(mockStoreService.listStore(reqDTO2)).thenReturn(displayStoreRespDTOS);

        // Configure DisplayRuleMapstruct.toStoreDOList(...).
        final DisplayStoreDO displayStoreDO = new DisplayStoreDO();
        displayStoreDO.setId(0L);
        displayStoreDO.setGuid("ded0f5ab-d2f9-478e-8348-d38f33b9516b");
        displayStoreDO.setIsDelete(0);
        displayStoreDO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayStoreDO.setRuleType(0);
        final List<DisplayStoreDO> displayStoreDOS = Arrays.asList(displayStoreDO);
        final DisplayStoreRespDTO displayStoreRespDTO2 = new DisplayStoreRespDTO();
        displayStoreRespDTO2.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO2.setRuleGuid("ruleGuid");
        displayStoreRespDTO2.setStoreGuid("storeGuid");
        displayStoreRespDTO2.setStoreName("storeName");
        displayStoreRespDTO2.setRuleType(0);
        final List<DisplayStoreRespDTO> storeList = Arrays.asList(displayStoreRespDTO2);
        when(mockRuleMapstruct.toStoreDOList(storeList)).thenReturn(displayStoreDOS);

        when(mockDistributedIdService.nextBatchDisplayStoreGuid(0L)).thenReturn(Collections.emptyList());

        // Configure DisplayStoreService.saveBatch(...).
        final DisplayStoreDO displayStoreDO1 = new DisplayStoreDO();
        displayStoreDO1.setId(0L);
        displayStoreDO1.setGuid("ded0f5ab-d2f9-478e-8348-d38f33b9516b");
        displayStoreDO1.setIsDelete(0);
        displayStoreDO1.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayStoreDO1.setRuleType(0);
        final List<DisplayStoreDO> entityList1 = Arrays.asList(displayStoreDO1);
        when(mockStoreService.saveBatch(entityList1)).thenReturn(false);

        // Configure DisplayRuleMapstruct.ruleReqToRuleDO(...).
        final DisplayRuleDO displayRuleDO = new DisplayRuleDO();
        displayRuleDO.setGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayRuleDO.setIsDelete(0);
        displayRuleDO.setDelayTime(0);
        displayRuleDO.setBatch(0);
        displayRuleDO.setIsAllStore(false);
        displayRuleDO.setRuleType(0);
        displayRuleDO.setBrandGuid("brandGuid");
        final DisplayRuleSaveOrUpdateDTO ruleReqDTO = new DisplayRuleSaveOrUpdateDTO();
        ruleReqDTO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        ruleReqDTO.setDisplayState(0);
        ruleReqDTO.setDelayTime(0);
        ruleReqDTO.setBatch(0);
        ruleReqDTO.setEffectiveState(0);
        ruleReqDTO.setEffectiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        ruleReqDTO.setIsAllStore(false);
        ruleReqDTO.setRuleType(0);
        final DisplayStoreRespDTO displayStoreRespDTO3 = new DisplayStoreRespDTO();
        displayStoreRespDTO3.setStoreGuid("storeGuid");
        displayStoreRespDTO3.setStoreName("storeName");
        ruleReqDTO.setStoreList(Arrays.asList(displayStoreRespDTO3));
        final DisplayItemRespDTO displayItemRespDTO3 = new DisplayItemRespDTO();
        displayItemRespDTO3.setItemGuid("itemGuid");
        displayItemRespDTO3.setItemName("itemName");
        ruleReqDTO.setItemList(Arrays.asList(displayItemRespDTO3));
        ruleReqDTO.setBrandGuid("brandGuid");
        when(mockRuleMapstruct.ruleReqToRuleDO(ruleReqDTO)).thenReturn(displayRuleDO);

        // Run the test
        final Boolean result = displayRuleServiceImplUnderTest.saveRule(reqDTO);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testSaveRule_DisplayStoreServiceSaveBatchReturnsTrue() {
        // Setup
        final DisplayRuleSaveOrUpdateDTO reqDTO = new DisplayRuleSaveOrUpdateDTO();
        reqDTO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        reqDTO.setDisplayState(0);
        reqDTO.setDelayTime(0);
        reqDTO.setBatch(0);
        reqDTO.setEffectiveState(0);
        reqDTO.setEffectiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO.setIsAllStore(false);
        reqDTO.setRuleType(0);
        final DisplayStoreRespDTO displayStoreRespDTO = new DisplayStoreRespDTO();
        displayStoreRespDTO.setStoreGuid("storeGuid");
        displayStoreRespDTO.setStoreName("storeName");
        reqDTO.setStoreList(Arrays.asList(displayStoreRespDTO));
        final DisplayItemRespDTO displayItemRespDTO = new DisplayItemRespDTO();
        displayItemRespDTO.setItemGuid("itemGuid");
        displayItemRespDTO.setItemName("itemName");
        reqDTO.setItemList(Arrays.asList(displayItemRespDTO));
        reqDTO.setBrandGuid("brandGuid");

        when(mockDistributedIdService.nextDisplayRuleGuid()).thenReturn("805f0a9e-d573-49ad-8183-f5a37fb61668");

        // Configure DisplayItemService.listItem(...).
        final DisplayItemRespDTO displayItemRespDTO1 = new DisplayItemRespDTO();
        displayItemRespDTO1.setItemGuid("itemGuid");
        displayItemRespDTO1.setItemName("itemName");
        displayItemRespDTO1.setSort(0);
        displayItemRespDTO1.setRuleType(0);
        final List<DisplayItemRespDTO> displayItemRespDTOS = Arrays.asList(displayItemRespDTO1);
        final DisplayRuleQueryDTO reqDTO1 = new DisplayRuleQueryDTO();
        reqDTO1.setCurrentPage(0L);
        reqDTO1.setPageSize(0L);
        reqDTO1.setRuleGuid("ruleGuid");
        reqDTO1.setRuleType(0);
        reqDTO1.setBrandGuid("brandGuid");
        when(mockItemService.listItem(reqDTO1)).thenReturn(displayItemRespDTOS);

        // Configure DisplayRuleMapstruct.toItemDOList(...).
        final DisplayItemDO displayItemDO = new DisplayItemDO();
        displayItemDO.setGuid("7e5ba61b-f27d-4746-abc1-480b55cad459");
        displayItemDO.setIsDelete(0);
        displayItemDO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayItemDO.setItemGuid("itemGuid");
        displayItemDO.setSort(0);
        displayItemDO.setRuleType(0);
        final List<DisplayItemDO> displayItemDOS = Arrays.asList(displayItemDO);
        final DisplayItemRespDTO displayItemRespDTO2 = new DisplayItemRespDTO();
        displayItemRespDTO2.setItemGuid("itemGuid");
        displayItemRespDTO2.setItemName("itemName");
        displayItemRespDTO2.setSort(0);
        displayItemRespDTO2.setRuleType(0);
        final List<DisplayItemRespDTO> itemList = Arrays.asList(displayItemRespDTO2);
        when(mockRuleMapstruct.toItemDOList(itemList)).thenReturn(displayItemDOS);

        when(mockDistributedIdService.nextBatchDisplayItemGuid(0L)).thenReturn(Arrays.asList("value"));
        when(mockItemService.count(any(LambdaQueryWrapper.class))).thenReturn(0);

        // Configure DisplayItemService.saveBatch(...).
        final DisplayItemDO displayItemDO1 = new DisplayItemDO();
        displayItemDO1.setGuid("7e5ba61b-f27d-4746-abc1-480b55cad459");
        displayItemDO1.setIsDelete(0);
        displayItemDO1.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayItemDO1.setItemGuid("itemGuid");
        displayItemDO1.setSort(0);
        displayItemDO1.setRuleType(0);
        final List<DisplayItemDO> entityList = Arrays.asList(displayItemDO1);
        when(mockItemService.saveBatch(entityList)).thenReturn(false);

        // Configure DisplayStoreService.listStore(...).
        final DisplayStoreRespDTO displayStoreRespDTO1 = new DisplayStoreRespDTO();
        displayStoreRespDTO1.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO1.setRuleGuid("ruleGuid");
        displayStoreRespDTO1.setStoreGuid("storeGuid");
        displayStoreRespDTO1.setStoreName("storeName");
        displayStoreRespDTO1.setRuleType(0);
        final List<DisplayStoreRespDTO> displayStoreRespDTOS = Arrays.asList(displayStoreRespDTO1);
        final DisplayRuleQueryDTO reqDTO2 = new DisplayRuleQueryDTO();
        reqDTO2.setCurrentPage(0L);
        reqDTO2.setPageSize(0L);
        reqDTO2.setRuleGuid("ruleGuid");
        reqDTO2.setRuleType(0);
        reqDTO2.setBrandGuid("brandGuid");
        when(mockStoreService.listStore(reqDTO2)).thenReturn(displayStoreRespDTOS);

        // Configure DisplayRuleMapstruct.toStoreDOList(...).
        final DisplayStoreDO displayStoreDO = new DisplayStoreDO();
        displayStoreDO.setId(0L);
        displayStoreDO.setGuid("ded0f5ab-d2f9-478e-8348-d38f33b9516b");
        displayStoreDO.setIsDelete(0);
        displayStoreDO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayStoreDO.setRuleType(0);
        final List<DisplayStoreDO> displayStoreDOS = Arrays.asList(displayStoreDO);
        final DisplayStoreRespDTO displayStoreRespDTO2 = new DisplayStoreRespDTO();
        displayStoreRespDTO2.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO2.setRuleGuid("ruleGuid");
        displayStoreRespDTO2.setStoreGuid("storeGuid");
        displayStoreRespDTO2.setStoreName("storeName");
        displayStoreRespDTO2.setRuleType(0);
        final List<DisplayStoreRespDTO> storeList = Arrays.asList(displayStoreRespDTO2);
        when(mockRuleMapstruct.toStoreDOList(storeList)).thenReturn(displayStoreDOS);

        when(mockDistributedIdService.nextBatchDisplayStoreGuid(0L)).thenReturn(Arrays.asList("value"));

        // Configure DisplayStoreService.saveBatch(...).
        final DisplayStoreDO displayStoreDO1 = new DisplayStoreDO();
        displayStoreDO1.setId(0L);
        displayStoreDO1.setGuid("ded0f5ab-d2f9-478e-8348-d38f33b9516b");
        displayStoreDO1.setIsDelete(0);
        displayStoreDO1.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayStoreDO1.setRuleType(0);
        final List<DisplayStoreDO> entityList1 = Arrays.asList(displayStoreDO1);
        when(mockStoreService.saveBatch(entityList1)).thenReturn(true);

        // Configure DisplayRuleMapstruct.ruleReqToRuleDO(...).
        final DisplayRuleDO displayRuleDO = new DisplayRuleDO();
        displayRuleDO.setGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayRuleDO.setIsDelete(0);
        displayRuleDO.setDelayTime(0);
        displayRuleDO.setBatch(0);
        displayRuleDO.setIsAllStore(false);
        displayRuleDO.setRuleType(0);
        displayRuleDO.setBrandGuid("brandGuid");
        final DisplayRuleSaveOrUpdateDTO ruleReqDTO = new DisplayRuleSaveOrUpdateDTO();
        ruleReqDTO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        ruleReqDTO.setDisplayState(0);
        ruleReqDTO.setDelayTime(0);
        ruleReqDTO.setBatch(0);
        ruleReqDTO.setEffectiveState(0);
        ruleReqDTO.setEffectiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        ruleReqDTO.setIsAllStore(false);
        ruleReqDTO.setRuleType(0);
        final DisplayStoreRespDTO displayStoreRespDTO3 = new DisplayStoreRespDTO();
        displayStoreRespDTO3.setStoreGuid("storeGuid");
        displayStoreRespDTO3.setStoreName("storeName");
        ruleReqDTO.setStoreList(Arrays.asList(displayStoreRespDTO3));
        final DisplayItemRespDTO displayItemRespDTO3 = new DisplayItemRespDTO();
        displayItemRespDTO3.setItemGuid("itemGuid");
        displayItemRespDTO3.setItemName("itemName");
        ruleReqDTO.setItemList(Arrays.asList(displayItemRespDTO3));
        ruleReqDTO.setBrandGuid("brandGuid");
        when(mockRuleMapstruct.ruleReqToRuleDO(ruleReqDTO)).thenReturn(displayRuleDO);

        // Run the test
        final Boolean result = displayRuleServiceImplUnderTest.saveRule(reqDTO);

        // Verify the results
        assertThat(result).isTrue();
    }

    @Test
    public void testBatchList() {
        // Setup
        final DisplayRuleQueryDTO reqDTO = new DisplayRuleQueryDTO();
        reqDTO.setCurrentPage(0L);
        reqDTO.setPageSize(0L);
        reqDTO.setRuleGuid("ruleGuid");
        reqDTO.setRuleType(0);
        reqDTO.setBrandGuid("brandGuid");

        // Configure DisplayRuleMapstruct.toRuleRespDTOList(...).
        final DisplayRuleRespDTO displayRuleRespDTO = new DisplayRuleRespDTO();
        displayRuleRespDTO.setGuid("10e656ef-1e92-4000-9e03-72882340639f");
        displayRuleRespDTO.setBatch(0);
        displayRuleRespDTO.setIsAllStore(false);
        displayRuleRespDTO.setRuleType(0);
        final DisplayStoreRespDTO displayStoreRespDTO = new DisplayStoreRespDTO();
        displayStoreRespDTO.setStoreGuid("storeGuid");
        displayStoreRespDTO.setStoreName("storeName");
        displayRuleRespDTO.setStoreList(Arrays.asList(displayStoreRespDTO));
        final DisplayItemRespDTO displayItemRespDTO = new DisplayItemRespDTO();
        displayItemRespDTO.setItemGuid("itemGuid");
        displayItemRespDTO.setItemName("itemName");
        displayRuleRespDTO.setItemList(Arrays.asList(displayItemRespDTO));
        displayRuleRespDTO.setRuleSort(0);
        final List<DisplayRuleRespDTO> displayRuleRespDTOS = Arrays.asList(displayRuleRespDTO);
        final DisplayRuleDO displayRuleDO = new DisplayRuleDO();
        displayRuleDO.setGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayRuleDO.setIsDelete(0);
        displayRuleDO.setDelayTime(0);
        displayRuleDO.setBatch(0);
        displayRuleDO.setIsAllStore(false);
        displayRuleDO.setRuleType(0);
        displayRuleDO.setBrandGuid("brandGuid");
        final List<DisplayRuleDO> ruleDOList = Arrays.asList(displayRuleDO);
        when(mockRuleMapstruct.toRuleRespDTOList(ruleDOList)).thenReturn(displayRuleRespDTOS);

        // Configure DisplayItemService.list(...).
        final DisplayItemDO displayItemDO = new DisplayItemDO();
        displayItemDO.setGuid("7e5ba61b-f27d-4746-abc1-480b55cad459");
        displayItemDO.setIsDelete(0);
        displayItemDO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayItemDO.setItemGuid("itemGuid");
        displayItemDO.setSort(0);
        displayItemDO.setRuleType(0);
        final List<DisplayItemDO> displayItemDOS = Arrays.asList(displayItemDO);
        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(displayItemDOS);

        // Configure DisplayRuleMapstruct.toItemRespList(...).
        final DisplayItemRespDTO displayItemRespDTO1 = new DisplayItemRespDTO();
        displayItemRespDTO1.setItemGuid("itemGuid");
        displayItemRespDTO1.setItemName("itemName");
        displayItemRespDTO1.setSort(0);
        displayItemRespDTO1.setRuleType(0);
        final List<DisplayItemRespDTO> displayItemRespDTOS = Arrays.asList(displayItemRespDTO1);
        final DisplayItemDO displayItemDO2 = new DisplayItemDO();
        displayItemDO2.setGuid("7e5ba61b-f27d-4746-abc1-480b55cad459");
        displayItemDO2.setIsDelete(0);
        displayItemDO2.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayItemDO2.setItemGuid("itemGuid");
        displayItemDO2.setSort(0);
        displayItemDO2.setRuleType(0);
        final List<DisplayItemDO> displayItemDO1 = Arrays.asList(displayItemDO2);
        when(mockRuleMapstruct.toItemRespList(displayItemDO1)).thenReturn(displayItemRespDTOS);

        // Configure DisplayItemService.queryItemInfo(...).
        final DisplayItemRespDTO displayItemRespDTO2 = new DisplayItemRespDTO();
        displayItemRespDTO2.setItemGuid("itemGuid");
        displayItemRespDTO2.setItemName("itemName");
        displayItemRespDTO2.setSort(0);
        displayItemRespDTO2.setRuleType(0);
        final List<DisplayItemRespDTO> displayItemRespDTOS1 = Arrays.asList(displayItemRespDTO2);
        final DisplayItemRespDTO displayItemRespDTO3 = new DisplayItemRespDTO();
        displayItemRespDTO3.setItemGuid("itemGuid");
        displayItemRespDTO3.setItemName("itemName");
        displayItemRespDTO3.setSort(0);
        displayItemRespDTO3.setRuleType(0);
        final List<DisplayItemRespDTO> displayItemRespDTOS2 = Arrays.asList(displayItemRespDTO3);
        when(mockItemService.queryItemInfo(Arrays.asList("value"), displayItemRespDTOS2))
                .thenReturn(displayItemRespDTOS1);

        // Configure DisplayStoreService.list(...).
        final DisplayStoreDO displayStoreDO = new DisplayStoreDO();
        displayStoreDO.setId(0L);
        displayStoreDO.setGuid("ded0f5ab-d2f9-478e-8348-d38f33b9516b");
        displayStoreDO.setIsDelete(0);
        displayStoreDO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayStoreDO.setRuleType(0);
        final List<DisplayStoreDO> displayStoreDOS = Arrays.asList(displayStoreDO);
        when(mockStoreService.list(any(LambdaQueryWrapper.class))).thenReturn(displayStoreDOS);

        // Configure DisplayRuleMapstruct.toStoreRespList(...).
        final DisplayStoreRespDTO displayStoreRespDTO1 = new DisplayStoreRespDTO();
        displayStoreRespDTO1.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO1.setRuleGuid("ruleGuid");
        displayStoreRespDTO1.setStoreGuid("storeGuid");
        displayStoreRespDTO1.setStoreName("storeName");
        displayStoreRespDTO1.setRuleType(0);
        final List<DisplayStoreRespDTO> displayStoreRespDTOS = Arrays.asList(displayStoreRespDTO1);
        final DisplayStoreDO displayStoreDO2 = new DisplayStoreDO();
        displayStoreDO2.setId(0L);
        displayStoreDO2.setGuid("ded0f5ab-d2f9-478e-8348-d38f33b9516b");
        displayStoreDO2.setIsDelete(0);
        displayStoreDO2.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayStoreDO2.setRuleType(0);
        final List<DisplayStoreDO> displayStoreDO1 = Arrays.asList(displayStoreDO2);
        when(mockRuleMapstruct.toStoreRespList(displayStoreDO1)).thenReturn(displayStoreRespDTOS);

        // Configure DisplayStoreService.queryStoreInfo(...).
        final DisplayStoreRespDTO displayStoreRespDTO2 = new DisplayStoreRespDTO();
        displayStoreRespDTO2.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO2.setRuleGuid("ruleGuid");
        displayStoreRespDTO2.setStoreGuid("storeGuid");
        displayStoreRespDTO2.setStoreName("storeName");
        displayStoreRespDTO2.setRuleType(0);
        final List<DisplayStoreRespDTO> displayStoreRespDTOS1 = Arrays.asList(displayStoreRespDTO2);
        final DisplayStoreRespDTO displayStoreRespDTO3 = new DisplayStoreRespDTO();
        displayStoreRespDTO3.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO3.setRuleGuid("ruleGuid");
        displayStoreRespDTO3.setStoreGuid("storeGuid");
        displayStoreRespDTO3.setStoreName("storeName");
        displayStoreRespDTO3.setRuleType(0);
        final List<DisplayStoreRespDTO> displayStoreRespList = Arrays.asList(displayStoreRespDTO3);
        when(mockStoreService.queryStoreInfo(Arrays.asList("value"), displayStoreRespList))
                .thenReturn(displayStoreRespDTOS1);

        // Run the test
        final Page<DisplayRuleRespDTO> result = displayRuleServiceImplUnderTest.batchList(reqDTO);

        // Verify the results
    }

    @Test
    public void testBatchList_DisplayRuleMapstructToRuleRespDTOListReturnsNoItems() {
        // Setup
        final DisplayRuleQueryDTO reqDTO = new DisplayRuleQueryDTO();
        reqDTO.setCurrentPage(0L);
        reqDTO.setPageSize(0L);
        reqDTO.setRuleGuid("ruleGuid");
        reqDTO.setRuleType(0);
        reqDTO.setBrandGuid("brandGuid");

        // Configure DisplayRuleMapstruct.toRuleRespDTOList(...).
        final DisplayRuleDO displayRuleDO = new DisplayRuleDO();
        displayRuleDO.setGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayRuleDO.setIsDelete(0);
        displayRuleDO.setDelayTime(0);
        displayRuleDO.setBatch(0);
        displayRuleDO.setIsAllStore(false);
        displayRuleDO.setRuleType(0);
        displayRuleDO.setBrandGuid("brandGuid");
        final List<DisplayRuleDO> ruleDOList = Arrays.asList(displayRuleDO);
        when(mockRuleMapstruct.toRuleRespDTOList(ruleDOList)).thenReturn(Collections.emptyList());

        // Configure DisplayItemService.list(...).
        final DisplayItemDO displayItemDO = new DisplayItemDO();
        displayItemDO.setGuid("7e5ba61b-f27d-4746-abc1-480b55cad459");
        displayItemDO.setIsDelete(0);
        displayItemDO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayItemDO.setItemGuid("itemGuid");
        displayItemDO.setSort(0);
        displayItemDO.setRuleType(0);
        final List<DisplayItemDO> displayItemDOS = Arrays.asList(displayItemDO);
        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(displayItemDOS);

        // Configure DisplayRuleMapstruct.toItemRespList(...).
        final DisplayItemRespDTO displayItemRespDTO = new DisplayItemRespDTO();
        displayItemRespDTO.setItemGuid("itemGuid");
        displayItemRespDTO.setItemName("itemName");
        displayItemRespDTO.setSort(0);
        displayItemRespDTO.setRuleType(0);
        final List<DisplayItemRespDTO> displayItemRespDTOS = Arrays.asList(displayItemRespDTO);
        final DisplayItemDO displayItemDO2 = new DisplayItemDO();
        displayItemDO2.setGuid("7e5ba61b-f27d-4746-abc1-480b55cad459");
        displayItemDO2.setIsDelete(0);
        displayItemDO2.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayItemDO2.setItemGuid("itemGuid");
        displayItemDO2.setSort(0);
        displayItemDO2.setRuleType(0);
        final List<DisplayItemDO> displayItemDO1 = Arrays.asList(displayItemDO2);
        when(mockRuleMapstruct.toItemRespList(displayItemDO1)).thenReturn(displayItemRespDTOS);

        // Configure DisplayItemService.queryItemInfo(...).
        final DisplayItemRespDTO displayItemRespDTO1 = new DisplayItemRespDTO();
        displayItemRespDTO1.setItemGuid("itemGuid");
        displayItemRespDTO1.setItemName("itemName");
        displayItemRespDTO1.setSort(0);
        displayItemRespDTO1.setRuleType(0);
        final List<DisplayItemRespDTO> displayItemRespDTOS1 = Arrays.asList(displayItemRespDTO1);
        final DisplayItemRespDTO displayItemRespDTO2 = new DisplayItemRespDTO();
        displayItemRespDTO2.setItemGuid("itemGuid");
        displayItemRespDTO2.setItemName("itemName");
        displayItemRespDTO2.setSort(0);
        displayItemRespDTO2.setRuleType(0);
        final List<DisplayItemRespDTO> displayItemRespDTOS2 = Arrays.asList(displayItemRespDTO2);
        when(mockItemService.queryItemInfo(Arrays.asList("value"), displayItemRespDTOS2))
                .thenReturn(displayItemRespDTOS1);

        // Configure DisplayStoreService.list(...).
        final DisplayStoreDO displayStoreDO = new DisplayStoreDO();
        displayStoreDO.setId(0L);
        displayStoreDO.setGuid("ded0f5ab-d2f9-478e-8348-d38f33b9516b");
        displayStoreDO.setIsDelete(0);
        displayStoreDO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayStoreDO.setRuleType(0);
        final List<DisplayStoreDO> displayStoreDOS = Arrays.asList(displayStoreDO);
        when(mockStoreService.list(any(LambdaQueryWrapper.class))).thenReturn(displayStoreDOS);

        // Configure DisplayRuleMapstruct.toStoreRespList(...).
        final DisplayStoreRespDTO displayStoreRespDTO = new DisplayStoreRespDTO();
        displayStoreRespDTO.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO.setRuleGuid("ruleGuid");
        displayStoreRespDTO.setStoreGuid("storeGuid");
        displayStoreRespDTO.setStoreName("storeName");
        displayStoreRespDTO.setRuleType(0);
        final List<DisplayStoreRespDTO> displayStoreRespDTOS = Arrays.asList(displayStoreRespDTO);
        final DisplayStoreDO displayStoreDO2 = new DisplayStoreDO();
        displayStoreDO2.setId(0L);
        displayStoreDO2.setGuid("ded0f5ab-d2f9-478e-8348-d38f33b9516b");
        displayStoreDO2.setIsDelete(0);
        displayStoreDO2.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayStoreDO2.setRuleType(0);
        final List<DisplayStoreDO> displayStoreDO1 = Arrays.asList(displayStoreDO2);
        when(mockRuleMapstruct.toStoreRespList(displayStoreDO1)).thenReturn(displayStoreRespDTOS);

        // Configure DisplayStoreService.queryStoreInfo(...).
        final DisplayStoreRespDTO displayStoreRespDTO1 = new DisplayStoreRespDTO();
        displayStoreRespDTO1.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO1.setRuleGuid("ruleGuid");
        displayStoreRespDTO1.setStoreGuid("storeGuid");
        displayStoreRespDTO1.setStoreName("storeName");
        displayStoreRespDTO1.setRuleType(0);
        final List<DisplayStoreRespDTO> displayStoreRespDTOS1 = Arrays.asList(displayStoreRespDTO1);
        final DisplayStoreRespDTO displayStoreRespDTO2 = new DisplayStoreRespDTO();
        displayStoreRespDTO2.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO2.setRuleGuid("ruleGuid");
        displayStoreRespDTO2.setStoreGuid("storeGuid");
        displayStoreRespDTO2.setStoreName("storeName");
        displayStoreRespDTO2.setRuleType(0);
        final List<DisplayStoreRespDTO> displayStoreRespList = Arrays.asList(displayStoreRespDTO2);
        when(mockStoreService.queryStoreInfo(Arrays.asList("value"), displayStoreRespList))
                .thenReturn(displayStoreRespDTOS1);

        // Run the test
        final Page<DisplayRuleRespDTO> result = displayRuleServiceImplUnderTest.batchList(reqDTO);

        // Verify the results
    }

    @Test
    public void testBatchList_DisplayItemServiceListReturnsNoItems() {
        // Setup
        final DisplayRuleQueryDTO reqDTO = new DisplayRuleQueryDTO();
        reqDTO.setCurrentPage(0L);
        reqDTO.setPageSize(0L);
        reqDTO.setRuleGuid("ruleGuid");
        reqDTO.setRuleType(0);
        reqDTO.setBrandGuid("brandGuid");

        // Configure DisplayRuleMapstruct.toRuleRespDTOList(...).
        final DisplayRuleRespDTO displayRuleRespDTO = new DisplayRuleRespDTO();
        displayRuleRespDTO.setGuid("10e656ef-1e92-4000-9e03-72882340639f");
        displayRuleRespDTO.setBatch(0);
        displayRuleRespDTO.setIsAllStore(false);
        displayRuleRespDTO.setRuleType(0);
        final DisplayStoreRespDTO displayStoreRespDTO = new DisplayStoreRespDTO();
        displayStoreRespDTO.setStoreGuid("storeGuid");
        displayStoreRespDTO.setStoreName("storeName");
        displayRuleRespDTO.setStoreList(Arrays.asList(displayStoreRespDTO));
        final DisplayItemRespDTO displayItemRespDTO = new DisplayItemRespDTO();
        displayItemRespDTO.setItemGuid("itemGuid");
        displayItemRespDTO.setItemName("itemName");
        displayRuleRespDTO.setItemList(Arrays.asList(displayItemRespDTO));
        displayRuleRespDTO.setRuleSort(0);
        final List<DisplayRuleRespDTO> displayRuleRespDTOS = Arrays.asList(displayRuleRespDTO);
        final DisplayRuleDO displayRuleDO = new DisplayRuleDO();
        displayRuleDO.setGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayRuleDO.setIsDelete(0);
        displayRuleDO.setDelayTime(0);
        displayRuleDO.setBatch(0);
        displayRuleDO.setIsAllStore(false);
        displayRuleDO.setRuleType(0);
        displayRuleDO.setBrandGuid("brandGuid");
        final List<DisplayRuleDO> ruleDOList = Arrays.asList(displayRuleDO);
        when(mockRuleMapstruct.toRuleRespDTOList(ruleDOList)).thenReturn(displayRuleRespDTOS);

        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure DisplayRuleMapstruct.toItemRespList(...).
        final DisplayItemRespDTO displayItemRespDTO1 = new DisplayItemRespDTO();
        displayItemRespDTO1.setItemGuid("itemGuid");
        displayItemRespDTO1.setItemName("itemName");
        displayItemRespDTO1.setSort(0);
        displayItemRespDTO1.setRuleType(0);
        final List<DisplayItemRespDTO> displayItemRespDTOS = Arrays.asList(displayItemRespDTO1);
        final DisplayItemDO displayItemDO1 = new DisplayItemDO();
        displayItemDO1.setGuid("7e5ba61b-f27d-4746-abc1-480b55cad459");
        displayItemDO1.setIsDelete(0);
        displayItemDO1.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayItemDO1.setItemGuid("itemGuid");
        displayItemDO1.setSort(0);
        displayItemDO1.setRuleType(0);
        final List<DisplayItemDO> displayItemDO = Arrays.asList(displayItemDO1);
        when(mockRuleMapstruct.toItemRespList(displayItemDO)).thenReturn(displayItemRespDTOS);

        // Configure DisplayItemService.queryItemInfo(...).
        final DisplayItemRespDTO displayItemRespDTO2 = new DisplayItemRespDTO();
        displayItemRespDTO2.setItemGuid("itemGuid");
        displayItemRespDTO2.setItemName("itemName");
        displayItemRespDTO2.setSort(0);
        displayItemRespDTO2.setRuleType(0);
        final List<DisplayItemRespDTO> displayItemRespDTOS1 = Arrays.asList(displayItemRespDTO2);
        final DisplayItemRespDTO displayItemRespDTO3 = new DisplayItemRespDTO();
        displayItemRespDTO3.setItemGuid("itemGuid");
        displayItemRespDTO3.setItemName("itemName");
        displayItemRespDTO3.setSort(0);
        displayItemRespDTO3.setRuleType(0);
        final List<DisplayItemRespDTO> displayItemRespDTOS2 = Arrays.asList(displayItemRespDTO3);
        when(mockItemService.queryItemInfo(Arrays.asList("value"), displayItemRespDTOS2))
                .thenReturn(displayItemRespDTOS1);

        // Configure DisplayStoreService.list(...).
        final DisplayStoreDO displayStoreDO = new DisplayStoreDO();
        displayStoreDO.setId(0L);
        displayStoreDO.setGuid("ded0f5ab-d2f9-478e-8348-d38f33b9516b");
        displayStoreDO.setIsDelete(0);
        displayStoreDO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayStoreDO.setRuleType(0);
        final List<DisplayStoreDO> displayStoreDOS = Arrays.asList(displayStoreDO);
        when(mockStoreService.list(any(LambdaQueryWrapper.class))).thenReturn(displayStoreDOS);

        // Configure DisplayRuleMapstruct.toStoreRespList(...).
        final DisplayStoreRespDTO displayStoreRespDTO1 = new DisplayStoreRespDTO();
        displayStoreRespDTO1.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO1.setRuleGuid("ruleGuid");
        displayStoreRespDTO1.setStoreGuid("storeGuid");
        displayStoreRespDTO1.setStoreName("storeName");
        displayStoreRespDTO1.setRuleType(0);
        final List<DisplayStoreRespDTO> displayStoreRespDTOS = Arrays.asList(displayStoreRespDTO1);
        final DisplayStoreDO displayStoreDO2 = new DisplayStoreDO();
        displayStoreDO2.setId(0L);
        displayStoreDO2.setGuid("ded0f5ab-d2f9-478e-8348-d38f33b9516b");
        displayStoreDO2.setIsDelete(0);
        displayStoreDO2.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayStoreDO2.setRuleType(0);
        final List<DisplayStoreDO> displayStoreDO1 = Arrays.asList(displayStoreDO2);
        when(mockRuleMapstruct.toStoreRespList(displayStoreDO1)).thenReturn(displayStoreRespDTOS);

        // Configure DisplayStoreService.queryStoreInfo(...).
        final DisplayStoreRespDTO displayStoreRespDTO2 = new DisplayStoreRespDTO();
        displayStoreRespDTO2.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO2.setRuleGuid("ruleGuid");
        displayStoreRespDTO2.setStoreGuid("storeGuid");
        displayStoreRespDTO2.setStoreName("storeName");
        displayStoreRespDTO2.setRuleType(0);
        final List<DisplayStoreRespDTO> displayStoreRespDTOS1 = Arrays.asList(displayStoreRespDTO2);
        final DisplayStoreRespDTO displayStoreRespDTO3 = new DisplayStoreRespDTO();
        displayStoreRespDTO3.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO3.setRuleGuid("ruleGuid");
        displayStoreRespDTO3.setStoreGuid("storeGuid");
        displayStoreRespDTO3.setStoreName("storeName");
        displayStoreRespDTO3.setRuleType(0);
        final List<DisplayStoreRespDTO> displayStoreRespList = Arrays.asList(displayStoreRespDTO3);
        when(mockStoreService.queryStoreInfo(Arrays.asList("value"), displayStoreRespList))
                .thenReturn(displayStoreRespDTOS1);

        // Run the test
        final Page<DisplayRuleRespDTO> result = displayRuleServiceImplUnderTest.batchList(reqDTO);

        // Verify the results
    }

    @Test
    public void testBatchList_DisplayRuleMapstructToItemRespListReturnsNoItems() {
        // Setup
        final DisplayRuleQueryDTO reqDTO = new DisplayRuleQueryDTO();
        reqDTO.setCurrentPage(0L);
        reqDTO.setPageSize(0L);
        reqDTO.setRuleGuid("ruleGuid");
        reqDTO.setRuleType(0);
        reqDTO.setBrandGuid("brandGuid");

        // Configure DisplayRuleMapstruct.toRuleRespDTOList(...).
        final DisplayRuleRespDTO displayRuleRespDTO = new DisplayRuleRespDTO();
        displayRuleRespDTO.setGuid("10e656ef-1e92-4000-9e03-72882340639f");
        displayRuleRespDTO.setBatch(0);
        displayRuleRespDTO.setIsAllStore(false);
        displayRuleRespDTO.setRuleType(0);
        final DisplayStoreRespDTO displayStoreRespDTO = new DisplayStoreRespDTO();
        displayStoreRespDTO.setStoreGuid("storeGuid");
        displayStoreRespDTO.setStoreName("storeName");
        displayRuleRespDTO.setStoreList(Arrays.asList(displayStoreRespDTO));
        final DisplayItemRespDTO displayItemRespDTO = new DisplayItemRespDTO();
        displayItemRespDTO.setItemGuid("itemGuid");
        displayItemRespDTO.setItemName("itemName");
        displayRuleRespDTO.setItemList(Arrays.asList(displayItemRespDTO));
        displayRuleRespDTO.setRuleSort(0);
        final List<DisplayRuleRespDTO> displayRuleRespDTOS = Arrays.asList(displayRuleRespDTO);
        final DisplayRuleDO displayRuleDO = new DisplayRuleDO();
        displayRuleDO.setGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayRuleDO.setIsDelete(0);
        displayRuleDO.setDelayTime(0);
        displayRuleDO.setBatch(0);
        displayRuleDO.setIsAllStore(false);
        displayRuleDO.setRuleType(0);
        displayRuleDO.setBrandGuid("brandGuid");
        final List<DisplayRuleDO> ruleDOList = Arrays.asList(displayRuleDO);
        when(mockRuleMapstruct.toRuleRespDTOList(ruleDOList)).thenReturn(displayRuleRespDTOS);

        // Configure DisplayItemService.list(...).
        final DisplayItemDO displayItemDO = new DisplayItemDO();
        displayItemDO.setGuid("7e5ba61b-f27d-4746-abc1-480b55cad459");
        displayItemDO.setIsDelete(0);
        displayItemDO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayItemDO.setItemGuid("itemGuid");
        displayItemDO.setSort(0);
        displayItemDO.setRuleType(0);
        final List<DisplayItemDO> displayItemDOS = Arrays.asList(displayItemDO);
        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(displayItemDOS);

        // Configure DisplayRuleMapstruct.toItemRespList(...).
        final DisplayItemDO displayItemDO2 = new DisplayItemDO();
        displayItemDO2.setGuid("7e5ba61b-f27d-4746-abc1-480b55cad459");
        displayItemDO2.setIsDelete(0);
        displayItemDO2.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayItemDO2.setItemGuid("itemGuid");
        displayItemDO2.setSort(0);
        displayItemDO2.setRuleType(0);
        final List<DisplayItemDO> displayItemDO1 = Arrays.asList(displayItemDO2);
        when(mockRuleMapstruct.toItemRespList(displayItemDO1)).thenReturn(Collections.emptyList());

        // Configure DisplayStoreService.list(...).
        final DisplayStoreDO displayStoreDO = new DisplayStoreDO();
        displayStoreDO.setId(0L);
        displayStoreDO.setGuid("ded0f5ab-d2f9-478e-8348-d38f33b9516b");
        displayStoreDO.setIsDelete(0);
        displayStoreDO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayStoreDO.setRuleType(0);
        final List<DisplayStoreDO> displayStoreDOS = Arrays.asList(displayStoreDO);
        when(mockStoreService.list(any(LambdaQueryWrapper.class))).thenReturn(displayStoreDOS);

        // Configure DisplayRuleMapstruct.toStoreRespList(...).
        final DisplayStoreRespDTO displayStoreRespDTO1 = new DisplayStoreRespDTO();
        displayStoreRespDTO1.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO1.setRuleGuid("ruleGuid");
        displayStoreRespDTO1.setStoreGuid("storeGuid");
        displayStoreRespDTO1.setStoreName("storeName");
        displayStoreRespDTO1.setRuleType(0);
        final List<DisplayStoreRespDTO> displayStoreRespDTOS = Arrays.asList(displayStoreRespDTO1);
        final DisplayStoreDO displayStoreDO2 = new DisplayStoreDO();
        displayStoreDO2.setId(0L);
        displayStoreDO2.setGuid("ded0f5ab-d2f9-478e-8348-d38f33b9516b");
        displayStoreDO2.setIsDelete(0);
        displayStoreDO2.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayStoreDO2.setRuleType(0);
        final List<DisplayStoreDO> displayStoreDO1 = Arrays.asList(displayStoreDO2);
        when(mockRuleMapstruct.toStoreRespList(displayStoreDO1)).thenReturn(displayStoreRespDTOS);

        // Configure DisplayStoreService.queryStoreInfo(...).
        final DisplayStoreRespDTO displayStoreRespDTO2 = new DisplayStoreRespDTO();
        displayStoreRespDTO2.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO2.setRuleGuid("ruleGuid");
        displayStoreRespDTO2.setStoreGuid("storeGuid");
        displayStoreRespDTO2.setStoreName("storeName");
        displayStoreRespDTO2.setRuleType(0);
        final List<DisplayStoreRespDTO> displayStoreRespDTOS1 = Arrays.asList(displayStoreRespDTO2);
        final DisplayStoreRespDTO displayStoreRespDTO3 = new DisplayStoreRespDTO();
        displayStoreRespDTO3.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO3.setRuleGuid("ruleGuid");
        displayStoreRespDTO3.setStoreGuid("storeGuid");
        displayStoreRespDTO3.setStoreName("storeName");
        displayStoreRespDTO3.setRuleType(0);
        final List<DisplayStoreRespDTO> displayStoreRespList = Arrays.asList(displayStoreRespDTO3);
        when(mockStoreService.queryStoreInfo(Arrays.asList("value"), displayStoreRespList))
                .thenReturn(displayStoreRespDTOS1);

        // Run the test
        final Page<DisplayRuleRespDTO> result = displayRuleServiceImplUnderTest.batchList(reqDTO);

        // Verify the results
    }

    @Test
    public void testBatchList_DisplayItemServiceQueryItemInfoReturnsNoItems() {
        // Setup
        final DisplayRuleQueryDTO reqDTO = new DisplayRuleQueryDTO();
        reqDTO.setCurrentPage(0L);
        reqDTO.setPageSize(0L);
        reqDTO.setRuleGuid("ruleGuid");
        reqDTO.setRuleType(0);
        reqDTO.setBrandGuid("brandGuid");

        // Configure DisplayRuleMapstruct.toRuleRespDTOList(...).
        final DisplayRuleRespDTO displayRuleRespDTO = new DisplayRuleRespDTO();
        displayRuleRespDTO.setGuid("10e656ef-1e92-4000-9e03-72882340639f");
        displayRuleRespDTO.setBatch(0);
        displayRuleRespDTO.setIsAllStore(false);
        displayRuleRespDTO.setRuleType(0);
        final DisplayStoreRespDTO displayStoreRespDTO = new DisplayStoreRespDTO();
        displayStoreRespDTO.setStoreGuid("storeGuid");
        displayStoreRespDTO.setStoreName("storeName");
        displayRuleRespDTO.setStoreList(Arrays.asList(displayStoreRespDTO));
        final DisplayItemRespDTO displayItemRespDTO = new DisplayItemRespDTO();
        displayItemRespDTO.setItemGuid("itemGuid");
        displayItemRespDTO.setItemName("itemName");
        displayRuleRespDTO.setItemList(Arrays.asList(displayItemRespDTO));
        displayRuleRespDTO.setRuleSort(0);
        final List<DisplayRuleRespDTO> displayRuleRespDTOS = Arrays.asList(displayRuleRespDTO);
        final DisplayRuleDO displayRuleDO = new DisplayRuleDO();
        displayRuleDO.setGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayRuleDO.setIsDelete(0);
        displayRuleDO.setDelayTime(0);
        displayRuleDO.setBatch(0);
        displayRuleDO.setIsAllStore(false);
        displayRuleDO.setRuleType(0);
        displayRuleDO.setBrandGuid("brandGuid");
        final List<DisplayRuleDO> ruleDOList = Arrays.asList(displayRuleDO);
        when(mockRuleMapstruct.toRuleRespDTOList(ruleDOList)).thenReturn(displayRuleRespDTOS);

        // Configure DisplayItemService.list(...).
        final DisplayItemDO displayItemDO = new DisplayItemDO();
        displayItemDO.setGuid("7e5ba61b-f27d-4746-abc1-480b55cad459");
        displayItemDO.setIsDelete(0);
        displayItemDO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayItemDO.setItemGuid("itemGuid");
        displayItemDO.setSort(0);
        displayItemDO.setRuleType(0);
        final List<DisplayItemDO> displayItemDOS = Arrays.asList(displayItemDO);
        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(displayItemDOS);

        // Configure DisplayRuleMapstruct.toItemRespList(...).
        final DisplayItemRespDTO displayItemRespDTO1 = new DisplayItemRespDTO();
        displayItemRespDTO1.setItemGuid("itemGuid");
        displayItemRespDTO1.setItemName("itemName");
        displayItemRespDTO1.setSort(0);
        displayItemRespDTO1.setRuleType(0);
        final List<DisplayItemRespDTO> displayItemRespDTOS = Arrays.asList(displayItemRespDTO1);
        final DisplayItemDO displayItemDO2 = new DisplayItemDO();
        displayItemDO2.setGuid("7e5ba61b-f27d-4746-abc1-480b55cad459");
        displayItemDO2.setIsDelete(0);
        displayItemDO2.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayItemDO2.setItemGuid("itemGuid");
        displayItemDO2.setSort(0);
        displayItemDO2.setRuleType(0);
        final List<DisplayItemDO> displayItemDO1 = Arrays.asList(displayItemDO2);
        when(mockRuleMapstruct.toItemRespList(displayItemDO1)).thenReturn(displayItemRespDTOS);

        // Configure DisplayItemService.queryItemInfo(...).
        final DisplayItemRespDTO displayItemRespDTO2 = new DisplayItemRespDTO();
        displayItemRespDTO2.setItemGuid("itemGuid");
        displayItemRespDTO2.setItemName("itemName");
        displayItemRespDTO2.setSort(0);
        displayItemRespDTO2.setRuleType(0);
        final List<DisplayItemRespDTO> displayItemRespDTOS1 = Arrays.asList(displayItemRespDTO2);
        when(mockItemService.queryItemInfo(Arrays.asList("value"), displayItemRespDTOS1))
                .thenReturn(Collections.emptyList());

        // Configure DisplayStoreService.list(...).
        final DisplayStoreDO displayStoreDO = new DisplayStoreDO();
        displayStoreDO.setId(0L);
        displayStoreDO.setGuid("ded0f5ab-d2f9-478e-8348-d38f33b9516b");
        displayStoreDO.setIsDelete(0);
        displayStoreDO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayStoreDO.setRuleType(0);
        final List<DisplayStoreDO> displayStoreDOS = Arrays.asList(displayStoreDO);
        when(mockStoreService.list(any(LambdaQueryWrapper.class))).thenReturn(displayStoreDOS);

        // Configure DisplayRuleMapstruct.toStoreRespList(...).
        final DisplayStoreRespDTO displayStoreRespDTO1 = new DisplayStoreRespDTO();
        displayStoreRespDTO1.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO1.setRuleGuid("ruleGuid");
        displayStoreRespDTO1.setStoreGuid("storeGuid");
        displayStoreRespDTO1.setStoreName("storeName");
        displayStoreRespDTO1.setRuleType(0);
        final List<DisplayStoreRespDTO> displayStoreRespDTOS = Arrays.asList(displayStoreRespDTO1);
        final DisplayStoreDO displayStoreDO2 = new DisplayStoreDO();
        displayStoreDO2.setId(0L);
        displayStoreDO2.setGuid("ded0f5ab-d2f9-478e-8348-d38f33b9516b");
        displayStoreDO2.setIsDelete(0);
        displayStoreDO2.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayStoreDO2.setRuleType(0);
        final List<DisplayStoreDO> displayStoreDO1 = Arrays.asList(displayStoreDO2);
        when(mockRuleMapstruct.toStoreRespList(displayStoreDO1)).thenReturn(displayStoreRespDTOS);

        // Configure DisplayStoreService.queryStoreInfo(...).
        final DisplayStoreRespDTO displayStoreRespDTO2 = new DisplayStoreRespDTO();
        displayStoreRespDTO2.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO2.setRuleGuid("ruleGuid");
        displayStoreRespDTO2.setStoreGuid("storeGuid");
        displayStoreRespDTO2.setStoreName("storeName");
        displayStoreRespDTO2.setRuleType(0);
        final List<DisplayStoreRespDTO> displayStoreRespDTOS1 = Arrays.asList(displayStoreRespDTO2);
        final DisplayStoreRespDTO displayStoreRespDTO3 = new DisplayStoreRespDTO();
        displayStoreRespDTO3.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO3.setRuleGuid("ruleGuid");
        displayStoreRespDTO3.setStoreGuid("storeGuid");
        displayStoreRespDTO3.setStoreName("storeName");
        displayStoreRespDTO3.setRuleType(0);
        final List<DisplayStoreRespDTO> displayStoreRespList = Arrays.asList(displayStoreRespDTO3);
        when(mockStoreService.queryStoreInfo(Arrays.asList("value"), displayStoreRespList))
                .thenReturn(displayStoreRespDTOS1);

        // Run the test
        final Page<DisplayRuleRespDTO> result = displayRuleServiceImplUnderTest.batchList(reqDTO);

        // Verify the results
    }

    @Test
    public void testBatchList_DisplayStoreServiceListReturnsNoItems() {
        // Setup
        final DisplayRuleQueryDTO reqDTO = new DisplayRuleQueryDTO();
        reqDTO.setCurrentPage(0L);
        reqDTO.setPageSize(0L);
        reqDTO.setRuleGuid("ruleGuid");
        reqDTO.setRuleType(0);
        reqDTO.setBrandGuid("brandGuid");

        // Configure DisplayRuleMapstruct.toRuleRespDTOList(...).
        final DisplayRuleRespDTO displayRuleRespDTO = new DisplayRuleRespDTO();
        displayRuleRespDTO.setGuid("10e656ef-1e92-4000-9e03-72882340639f");
        displayRuleRespDTO.setBatch(0);
        displayRuleRespDTO.setIsAllStore(false);
        displayRuleRespDTO.setRuleType(0);
        final DisplayStoreRespDTO displayStoreRespDTO = new DisplayStoreRespDTO();
        displayStoreRespDTO.setStoreGuid("storeGuid");
        displayStoreRespDTO.setStoreName("storeName");
        displayRuleRespDTO.setStoreList(Arrays.asList(displayStoreRespDTO));
        final DisplayItemRespDTO displayItemRespDTO = new DisplayItemRespDTO();
        displayItemRespDTO.setItemGuid("itemGuid");
        displayItemRespDTO.setItemName("itemName");
        displayRuleRespDTO.setItemList(Arrays.asList(displayItemRespDTO));
        displayRuleRespDTO.setRuleSort(0);
        final List<DisplayRuleRespDTO> displayRuleRespDTOS = Arrays.asList(displayRuleRespDTO);
        final DisplayRuleDO displayRuleDO = new DisplayRuleDO();
        displayRuleDO.setGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayRuleDO.setIsDelete(0);
        displayRuleDO.setDelayTime(0);
        displayRuleDO.setBatch(0);
        displayRuleDO.setIsAllStore(false);
        displayRuleDO.setRuleType(0);
        displayRuleDO.setBrandGuid("brandGuid");
        final List<DisplayRuleDO> ruleDOList = Arrays.asList(displayRuleDO);
        when(mockRuleMapstruct.toRuleRespDTOList(ruleDOList)).thenReturn(displayRuleRespDTOS);

        // Configure DisplayItemService.list(...).
        final DisplayItemDO displayItemDO = new DisplayItemDO();
        displayItemDO.setGuid("7e5ba61b-f27d-4746-abc1-480b55cad459");
        displayItemDO.setIsDelete(0);
        displayItemDO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayItemDO.setItemGuid("itemGuid");
        displayItemDO.setSort(0);
        displayItemDO.setRuleType(0);
        final List<DisplayItemDO> displayItemDOS = Arrays.asList(displayItemDO);
        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(displayItemDOS);

        // Configure DisplayRuleMapstruct.toItemRespList(...).
        final DisplayItemRespDTO displayItemRespDTO1 = new DisplayItemRespDTO();
        displayItemRespDTO1.setItemGuid("itemGuid");
        displayItemRespDTO1.setItemName("itemName");
        displayItemRespDTO1.setSort(0);
        displayItemRespDTO1.setRuleType(0);
        final List<DisplayItemRespDTO> displayItemRespDTOS = Arrays.asList(displayItemRespDTO1);
        final DisplayItemDO displayItemDO2 = new DisplayItemDO();
        displayItemDO2.setGuid("7e5ba61b-f27d-4746-abc1-480b55cad459");
        displayItemDO2.setIsDelete(0);
        displayItemDO2.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayItemDO2.setItemGuid("itemGuid");
        displayItemDO2.setSort(0);
        displayItemDO2.setRuleType(0);
        final List<DisplayItemDO> displayItemDO1 = Arrays.asList(displayItemDO2);
        when(mockRuleMapstruct.toItemRespList(displayItemDO1)).thenReturn(displayItemRespDTOS);

        // Configure DisplayItemService.queryItemInfo(...).
        final DisplayItemRespDTO displayItemRespDTO2 = new DisplayItemRespDTO();
        displayItemRespDTO2.setItemGuid("itemGuid");
        displayItemRespDTO2.setItemName("itemName");
        displayItemRespDTO2.setSort(0);
        displayItemRespDTO2.setRuleType(0);
        final List<DisplayItemRespDTO> displayItemRespDTOS1 = Arrays.asList(displayItemRespDTO2);
        final DisplayItemRespDTO displayItemRespDTO3 = new DisplayItemRespDTO();
        displayItemRespDTO3.setItemGuid("itemGuid");
        displayItemRespDTO3.setItemName("itemName");
        displayItemRespDTO3.setSort(0);
        displayItemRespDTO3.setRuleType(0);
        final List<DisplayItemRespDTO> displayItemRespDTOS2 = Arrays.asList(displayItemRespDTO3);
        when(mockItemService.queryItemInfo(Arrays.asList("value"), displayItemRespDTOS2))
                .thenReturn(displayItemRespDTOS1);

        when(mockStoreService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure DisplayRuleMapstruct.toStoreRespList(...).
        final DisplayStoreRespDTO displayStoreRespDTO1 = new DisplayStoreRespDTO();
        displayStoreRespDTO1.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO1.setRuleGuid("ruleGuid");
        displayStoreRespDTO1.setStoreGuid("storeGuid");
        displayStoreRespDTO1.setStoreName("storeName");
        displayStoreRespDTO1.setRuleType(0);
        final List<DisplayStoreRespDTO> displayStoreRespDTOS = Arrays.asList(displayStoreRespDTO1);
        final DisplayStoreDO displayStoreDO1 = new DisplayStoreDO();
        displayStoreDO1.setId(0L);
        displayStoreDO1.setGuid("ded0f5ab-d2f9-478e-8348-d38f33b9516b");
        displayStoreDO1.setIsDelete(0);
        displayStoreDO1.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayStoreDO1.setRuleType(0);
        final List<DisplayStoreDO> displayStoreDO = Arrays.asList(displayStoreDO1);
        when(mockRuleMapstruct.toStoreRespList(displayStoreDO)).thenReturn(displayStoreRespDTOS);

        // Configure DisplayStoreService.queryStoreInfo(...).
        final DisplayStoreRespDTO displayStoreRespDTO2 = new DisplayStoreRespDTO();
        displayStoreRespDTO2.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO2.setRuleGuid("ruleGuid");
        displayStoreRespDTO2.setStoreGuid("storeGuid");
        displayStoreRespDTO2.setStoreName("storeName");
        displayStoreRespDTO2.setRuleType(0);
        final List<DisplayStoreRespDTO> displayStoreRespDTOS1 = Arrays.asList(displayStoreRespDTO2);
        final DisplayStoreRespDTO displayStoreRespDTO3 = new DisplayStoreRespDTO();
        displayStoreRespDTO3.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO3.setRuleGuid("ruleGuid");
        displayStoreRespDTO3.setStoreGuid("storeGuid");
        displayStoreRespDTO3.setStoreName("storeName");
        displayStoreRespDTO3.setRuleType(0);
        final List<DisplayStoreRespDTO> displayStoreRespList = Arrays.asList(displayStoreRespDTO3);
        when(mockStoreService.queryStoreInfo(Arrays.asList("value"), displayStoreRespList))
                .thenReturn(displayStoreRespDTOS1);

        // Run the test
        final Page<DisplayRuleRespDTO> result = displayRuleServiceImplUnderTest.batchList(reqDTO);

        // Verify the results
    }

    @Test
    public void testBatchList_DisplayRuleMapstructToStoreRespListReturnsNoItems() {
        // Setup
        final DisplayRuleQueryDTO reqDTO = new DisplayRuleQueryDTO();
        reqDTO.setCurrentPage(0L);
        reqDTO.setPageSize(0L);
        reqDTO.setRuleGuid("ruleGuid");
        reqDTO.setRuleType(0);
        reqDTO.setBrandGuid("brandGuid");

        // Configure DisplayRuleMapstruct.toRuleRespDTOList(...).
        final DisplayRuleRespDTO displayRuleRespDTO = new DisplayRuleRespDTO();
        displayRuleRespDTO.setGuid("10e656ef-1e92-4000-9e03-72882340639f");
        displayRuleRespDTO.setBatch(0);
        displayRuleRespDTO.setIsAllStore(false);
        displayRuleRespDTO.setRuleType(0);
        final DisplayStoreRespDTO displayStoreRespDTO = new DisplayStoreRespDTO();
        displayStoreRespDTO.setStoreGuid("storeGuid");
        displayStoreRespDTO.setStoreName("storeName");
        displayRuleRespDTO.setStoreList(Arrays.asList(displayStoreRespDTO));
        final DisplayItemRespDTO displayItemRespDTO = new DisplayItemRespDTO();
        displayItemRespDTO.setItemGuid("itemGuid");
        displayItemRespDTO.setItemName("itemName");
        displayRuleRespDTO.setItemList(Arrays.asList(displayItemRespDTO));
        displayRuleRespDTO.setRuleSort(0);
        final List<DisplayRuleRespDTO> displayRuleRespDTOS = Arrays.asList(displayRuleRespDTO);
        final DisplayRuleDO displayRuleDO = new DisplayRuleDO();
        displayRuleDO.setGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayRuleDO.setIsDelete(0);
        displayRuleDO.setDelayTime(0);
        displayRuleDO.setBatch(0);
        displayRuleDO.setIsAllStore(false);
        displayRuleDO.setRuleType(0);
        displayRuleDO.setBrandGuid("brandGuid");
        final List<DisplayRuleDO> ruleDOList = Arrays.asList(displayRuleDO);
        when(mockRuleMapstruct.toRuleRespDTOList(ruleDOList)).thenReturn(displayRuleRespDTOS);

        // Configure DisplayItemService.list(...).
        final DisplayItemDO displayItemDO = new DisplayItemDO();
        displayItemDO.setGuid("7e5ba61b-f27d-4746-abc1-480b55cad459");
        displayItemDO.setIsDelete(0);
        displayItemDO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayItemDO.setItemGuid("itemGuid");
        displayItemDO.setSort(0);
        displayItemDO.setRuleType(0);
        final List<DisplayItemDO> displayItemDOS = Arrays.asList(displayItemDO);
        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(displayItemDOS);

        // Configure DisplayRuleMapstruct.toItemRespList(...).
        final DisplayItemRespDTO displayItemRespDTO1 = new DisplayItemRespDTO();
        displayItemRespDTO1.setItemGuid("itemGuid");
        displayItemRespDTO1.setItemName("itemName");
        displayItemRespDTO1.setSort(0);
        displayItemRespDTO1.setRuleType(0);
        final List<DisplayItemRespDTO> displayItemRespDTOS = Arrays.asList(displayItemRespDTO1);
        final DisplayItemDO displayItemDO2 = new DisplayItemDO();
        displayItemDO2.setGuid("7e5ba61b-f27d-4746-abc1-480b55cad459");
        displayItemDO2.setIsDelete(0);
        displayItemDO2.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayItemDO2.setItemGuid("itemGuid");
        displayItemDO2.setSort(0);
        displayItemDO2.setRuleType(0);
        final List<DisplayItemDO> displayItemDO1 = Arrays.asList(displayItemDO2);
        when(mockRuleMapstruct.toItemRespList(displayItemDO1)).thenReturn(displayItemRespDTOS);

        // Configure DisplayItemService.queryItemInfo(...).
        final DisplayItemRespDTO displayItemRespDTO2 = new DisplayItemRespDTO();
        displayItemRespDTO2.setItemGuid("itemGuid");
        displayItemRespDTO2.setItemName("itemName");
        displayItemRespDTO2.setSort(0);
        displayItemRespDTO2.setRuleType(0);
        final List<DisplayItemRespDTO> displayItemRespDTOS1 = Arrays.asList(displayItemRespDTO2);
        final DisplayItemRespDTO displayItemRespDTO3 = new DisplayItemRespDTO();
        displayItemRespDTO3.setItemGuid("itemGuid");
        displayItemRespDTO3.setItemName("itemName");
        displayItemRespDTO3.setSort(0);
        displayItemRespDTO3.setRuleType(0);
        final List<DisplayItemRespDTO> displayItemRespDTOS2 = Arrays.asList(displayItemRespDTO3);
        when(mockItemService.queryItemInfo(Arrays.asList("value"), displayItemRespDTOS2))
                .thenReturn(displayItemRespDTOS1);

        // Configure DisplayStoreService.list(...).
        final DisplayStoreDO displayStoreDO = new DisplayStoreDO();
        displayStoreDO.setId(0L);
        displayStoreDO.setGuid("ded0f5ab-d2f9-478e-8348-d38f33b9516b");
        displayStoreDO.setIsDelete(0);
        displayStoreDO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayStoreDO.setRuleType(0);
        final List<DisplayStoreDO> displayStoreDOS = Arrays.asList(displayStoreDO);
        when(mockStoreService.list(any(LambdaQueryWrapper.class))).thenReturn(displayStoreDOS);

        // Configure DisplayRuleMapstruct.toStoreRespList(...).
        final DisplayStoreDO displayStoreDO2 = new DisplayStoreDO();
        displayStoreDO2.setId(0L);
        displayStoreDO2.setGuid("ded0f5ab-d2f9-478e-8348-d38f33b9516b");
        displayStoreDO2.setIsDelete(0);
        displayStoreDO2.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayStoreDO2.setRuleType(0);
        final List<DisplayStoreDO> displayStoreDO1 = Arrays.asList(displayStoreDO2);
        when(mockRuleMapstruct.toStoreRespList(displayStoreDO1)).thenReturn(Collections.emptyList());

        // Run the test
        final Page<DisplayRuleRespDTO> result = displayRuleServiceImplUnderTest.batchList(reqDTO);

        // Verify the results
    }

    @Test
    public void testBatchList_DisplayStoreServiceQueryStoreInfoReturnsNoItems() {
        // Setup
        final DisplayRuleQueryDTO reqDTO = new DisplayRuleQueryDTO();
        reqDTO.setCurrentPage(0L);
        reqDTO.setPageSize(0L);
        reqDTO.setRuleGuid("ruleGuid");
        reqDTO.setRuleType(0);
        reqDTO.setBrandGuid("brandGuid");

        // Configure DisplayRuleMapstruct.toRuleRespDTOList(...).
        final DisplayRuleRespDTO displayRuleRespDTO = new DisplayRuleRespDTO();
        displayRuleRespDTO.setGuid("10e656ef-1e92-4000-9e03-72882340639f");
        displayRuleRespDTO.setBatch(0);
        displayRuleRespDTO.setIsAllStore(false);
        displayRuleRespDTO.setRuleType(0);
        final DisplayStoreRespDTO displayStoreRespDTO = new DisplayStoreRespDTO();
        displayStoreRespDTO.setStoreGuid("storeGuid");
        displayStoreRespDTO.setStoreName("storeName");
        displayRuleRespDTO.setStoreList(Arrays.asList(displayStoreRespDTO));
        final DisplayItemRespDTO displayItemRespDTO = new DisplayItemRespDTO();
        displayItemRespDTO.setItemGuid("itemGuid");
        displayItemRespDTO.setItemName("itemName");
        displayRuleRespDTO.setItemList(Arrays.asList(displayItemRespDTO));
        displayRuleRespDTO.setRuleSort(0);
        final List<DisplayRuleRespDTO> displayRuleRespDTOS = Arrays.asList(displayRuleRespDTO);
        final DisplayRuleDO displayRuleDO = new DisplayRuleDO();
        displayRuleDO.setGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayRuleDO.setIsDelete(0);
        displayRuleDO.setDelayTime(0);
        displayRuleDO.setBatch(0);
        displayRuleDO.setIsAllStore(false);
        displayRuleDO.setRuleType(0);
        displayRuleDO.setBrandGuid("brandGuid");
        final List<DisplayRuleDO> ruleDOList = Arrays.asList(displayRuleDO);
        when(mockRuleMapstruct.toRuleRespDTOList(ruleDOList)).thenReturn(displayRuleRespDTOS);

        // Configure DisplayItemService.list(...).
        final DisplayItemDO displayItemDO = new DisplayItemDO();
        displayItemDO.setGuid("7e5ba61b-f27d-4746-abc1-480b55cad459");
        displayItemDO.setIsDelete(0);
        displayItemDO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayItemDO.setItemGuid("itemGuid");
        displayItemDO.setSort(0);
        displayItemDO.setRuleType(0);
        final List<DisplayItemDO> displayItemDOS = Arrays.asList(displayItemDO);
        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(displayItemDOS);

        // Configure DisplayRuleMapstruct.toItemRespList(...).
        final DisplayItemRespDTO displayItemRespDTO1 = new DisplayItemRespDTO();
        displayItemRespDTO1.setItemGuid("itemGuid");
        displayItemRespDTO1.setItemName("itemName");
        displayItemRespDTO1.setSort(0);
        displayItemRespDTO1.setRuleType(0);
        final List<DisplayItemRespDTO> displayItemRespDTOS = Arrays.asList(displayItemRespDTO1);
        final DisplayItemDO displayItemDO2 = new DisplayItemDO();
        displayItemDO2.setGuid("7e5ba61b-f27d-4746-abc1-480b55cad459");
        displayItemDO2.setIsDelete(0);
        displayItemDO2.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayItemDO2.setItemGuid("itemGuid");
        displayItemDO2.setSort(0);
        displayItemDO2.setRuleType(0);
        final List<DisplayItemDO> displayItemDO1 = Arrays.asList(displayItemDO2);
        when(mockRuleMapstruct.toItemRespList(displayItemDO1)).thenReturn(displayItemRespDTOS);

        // Configure DisplayItemService.queryItemInfo(...).
        final DisplayItemRespDTO displayItemRespDTO2 = new DisplayItemRespDTO();
        displayItemRespDTO2.setItemGuid("itemGuid");
        displayItemRespDTO2.setItemName("itemName");
        displayItemRespDTO2.setSort(0);
        displayItemRespDTO2.setRuleType(0);
        final List<DisplayItemRespDTO> displayItemRespDTOS1 = Arrays.asList(displayItemRespDTO2);
        final DisplayItemRespDTO displayItemRespDTO3 = new DisplayItemRespDTO();
        displayItemRespDTO3.setItemGuid("itemGuid");
        displayItemRespDTO3.setItemName("itemName");
        displayItemRespDTO3.setSort(0);
        displayItemRespDTO3.setRuleType(0);
        final List<DisplayItemRespDTO> displayItemRespDTOS2 = Arrays.asList(displayItemRespDTO3);
        when(mockItemService.queryItemInfo(Arrays.asList("value"), displayItemRespDTOS2))
                .thenReturn(displayItemRespDTOS1);

        // Configure DisplayStoreService.list(...).
        final DisplayStoreDO displayStoreDO = new DisplayStoreDO();
        displayStoreDO.setId(0L);
        displayStoreDO.setGuid("ded0f5ab-d2f9-478e-8348-d38f33b9516b");
        displayStoreDO.setIsDelete(0);
        displayStoreDO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayStoreDO.setRuleType(0);
        final List<DisplayStoreDO> displayStoreDOS = Arrays.asList(displayStoreDO);
        when(mockStoreService.list(any(LambdaQueryWrapper.class))).thenReturn(displayStoreDOS);

        // Configure DisplayRuleMapstruct.toStoreRespList(...).
        final DisplayStoreRespDTO displayStoreRespDTO1 = new DisplayStoreRespDTO();
        displayStoreRespDTO1.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO1.setRuleGuid("ruleGuid");
        displayStoreRespDTO1.setStoreGuid("storeGuid");
        displayStoreRespDTO1.setStoreName("storeName");
        displayStoreRespDTO1.setRuleType(0);
        final List<DisplayStoreRespDTO> displayStoreRespDTOS = Arrays.asList(displayStoreRespDTO1);
        final DisplayStoreDO displayStoreDO2 = new DisplayStoreDO();
        displayStoreDO2.setId(0L);
        displayStoreDO2.setGuid("ded0f5ab-d2f9-478e-8348-d38f33b9516b");
        displayStoreDO2.setIsDelete(0);
        displayStoreDO2.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayStoreDO2.setRuleType(0);
        final List<DisplayStoreDO> displayStoreDO1 = Arrays.asList(displayStoreDO2);
        when(mockRuleMapstruct.toStoreRespList(displayStoreDO1)).thenReturn(displayStoreRespDTOS);

        // Configure DisplayStoreService.queryStoreInfo(...).
        final DisplayStoreRespDTO displayStoreRespDTO2 = new DisplayStoreRespDTO();
        displayStoreRespDTO2.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO2.setRuleGuid("ruleGuid");
        displayStoreRespDTO2.setStoreGuid("storeGuid");
        displayStoreRespDTO2.setStoreName("storeName");
        displayStoreRespDTO2.setRuleType(0);
        final List<DisplayStoreRespDTO> displayStoreRespList = Arrays.asList(displayStoreRespDTO2);
        when(mockStoreService.queryStoreInfo(Arrays.asList("value"), displayStoreRespList))
                .thenReturn(Collections.emptyList());

        // Run the test
        final Page<DisplayRuleRespDTO> result = displayRuleServiceImplUnderTest.batchList(reqDTO);

        // Verify the results
    }

    @Test
    public void testDeleteRule() {
        // Setup
        final DisplayRuleQueryDTO reqDTO = new DisplayRuleQueryDTO();
        reqDTO.setCurrentPage(0L);
        reqDTO.setPageSize(0L);
        reqDTO.setRuleGuid("ruleGuid");
        reqDTO.setRuleType(0);
        reqDTO.setBrandGuid("brandGuid");

        when(mockStoreService.remove(any(LambdaQueryWrapper.class))).thenReturn(false);
        when(mockItemService.remove(any(LambdaQueryWrapper.class))).thenReturn(false);

        // Run the test
        final Boolean result = displayRuleServiceImplUnderTest.deleteRule(reqDTO);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testDeleteRule_DisplayStoreServiceReturnsTrue() {
        // Setup
        final DisplayRuleQueryDTO reqDTO = new DisplayRuleQueryDTO();
        reqDTO.setCurrentPage(0L);
        reqDTO.setPageSize(0L);
        reqDTO.setRuleGuid("ruleGuid");
        reqDTO.setRuleType(0);
        reqDTO.setBrandGuid("brandGuid");

        when(mockStoreService.remove(any(LambdaQueryWrapper.class))).thenReturn(true);
        when(mockItemService.remove(any(LambdaQueryWrapper.class))).thenReturn(false);

        // Run the test
        final Boolean result = displayRuleServiceImplUnderTest.deleteRule(reqDTO);

        // Verify the results
        assertThat(result).isTrue();
    }

    @Test
    public void testDeleteRule_DisplayItemServiceReturnsTrue() {
        // Setup
        final DisplayRuleQueryDTO reqDTO = new DisplayRuleQueryDTO();
        reqDTO.setCurrentPage(0L);
        reqDTO.setPageSize(0L);
        reqDTO.setRuleGuid("ruleGuid");
        reqDTO.setRuleType(0);
        reqDTO.setBrandGuid("brandGuid");

        when(mockStoreService.remove(any(LambdaQueryWrapper.class))).thenReturn(false);
        when(mockItemService.remove(any(LambdaQueryWrapper.class))).thenReturn(true);

        // Run the test
        final Boolean result = displayRuleServiceImplUnderTest.deleteRule(reqDTO);

        // Verify the results
        assertThat(result).isTrue();
    }

    @Test
    public void testGetRule() {
        // Setup
        final DisplayRuleQueryDTO reqDTO = new DisplayRuleQueryDTO();
        reqDTO.setCurrentPage(0L);
        reqDTO.setPageSize(0L);
        reqDTO.setRuleGuid("ruleGuid");
        reqDTO.setRuleType(0);
        reqDTO.setBrandGuid("brandGuid");

        final DisplayRuleRespDTO expectedResult = new DisplayRuleRespDTO();
        expectedResult.setGuid("10e656ef-1e92-4000-9e03-72882340639f");
        expectedResult.setBatch(0);
        expectedResult.setIsAllStore(false);
        expectedResult.setRuleType(0);
        final DisplayStoreRespDTO displayStoreRespDTO = new DisplayStoreRespDTO();
        displayStoreRespDTO.setStoreGuid("storeGuid");
        displayStoreRespDTO.setStoreName("storeName");
        expectedResult.setStoreList(Arrays.asList(displayStoreRespDTO));
        final DisplayItemRespDTO displayItemRespDTO = new DisplayItemRespDTO();
        displayItemRespDTO.setItemGuid("itemGuid");
        displayItemRespDTO.setItemName("itemName");
        expectedResult.setItemList(Arrays.asList(displayItemRespDTO));
        expectedResult.setRuleSort(0);

        // Configure DisplayRuleMapstruct.toRuleRespDTO(...).
        final DisplayRuleRespDTO displayRuleRespDTO = new DisplayRuleRespDTO();
        displayRuleRespDTO.setGuid("10e656ef-1e92-4000-9e03-72882340639f");
        displayRuleRespDTO.setBatch(0);
        displayRuleRespDTO.setIsAllStore(false);
        displayRuleRespDTO.setRuleType(0);
        final DisplayStoreRespDTO displayStoreRespDTO1 = new DisplayStoreRespDTO();
        displayStoreRespDTO1.setStoreGuid("storeGuid");
        displayStoreRespDTO1.setStoreName("storeName");
        displayRuleRespDTO.setStoreList(Arrays.asList(displayStoreRespDTO1));
        final DisplayItemRespDTO displayItemRespDTO1 = new DisplayItemRespDTO();
        displayItemRespDTO1.setItemGuid("itemGuid");
        displayItemRespDTO1.setItemName("itemName");
        displayRuleRespDTO.setItemList(Arrays.asList(displayItemRespDTO1));
        displayRuleRespDTO.setRuleSort(0);
        final DisplayRuleDO ruleDO = new DisplayRuleDO();
        ruleDO.setGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        ruleDO.setIsDelete(0);
        ruleDO.setDelayTime(0);
        ruleDO.setBatch(0);
        ruleDO.setIsAllStore(false);
        ruleDO.setRuleType(0);
        ruleDO.setBrandGuid("brandGuid");
        when(mockRuleMapstruct.toRuleRespDTO(ruleDO)).thenReturn(displayRuleRespDTO);

        // Configure DisplayItemService.list(...).
        final DisplayItemDO displayItemDO = new DisplayItemDO();
        displayItemDO.setGuid("7e5ba61b-f27d-4746-abc1-480b55cad459");
        displayItemDO.setIsDelete(0);
        displayItemDO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayItemDO.setItemGuid("itemGuid");
        displayItemDO.setSort(0);
        displayItemDO.setRuleType(0);
        final List<DisplayItemDO> displayItemDOS = Arrays.asList(displayItemDO);
        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(displayItemDOS);

        // Configure DisplayRuleMapstruct.toItemRespList(...).
        final DisplayItemRespDTO displayItemRespDTO2 = new DisplayItemRespDTO();
        displayItemRespDTO2.setItemGuid("itemGuid");
        displayItemRespDTO2.setItemName("itemName");
        displayItemRespDTO2.setSort(0);
        displayItemRespDTO2.setRuleType(0);
        final List<DisplayItemRespDTO> displayItemRespDTOS = Arrays.asList(displayItemRespDTO2);
        final DisplayItemDO displayItemDO2 = new DisplayItemDO();
        displayItemDO2.setGuid("7e5ba61b-f27d-4746-abc1-480b55cad459");
        displayItemDO2.setIsDelete(0);
        displayItemDO2.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayItemDO2.setItemGuid("itemGuid");
        displayItemDO2.setSort(0);
        displayItemDO2.setRuleType(0);
        final List<DisplayItemDO> displayItemDO1 = Arrays.asList(displayItemDO2);
        when(mockRuleMapstruct.toItemRespList(displayItemDO1)).thenReturn(displayItemRespDTOS);

        // Configure DisplayItemService.queryItemInfo(...).
        final DisplayItemRespDTO displayItemRespDTO3 = new DisplayItemRespDTO();
        displayItemRespDTO3.setItemGuid("itemGuid");
        displayItemRespDTO3.setItemName("itemName");
        displayItemRespDTO3.setSort(0);
        displayItemRespDTO3.setRuleType(0);
        final List<DisplayItemRespDTO> displayItemRespDTOS1 = Arrays.asList(displayItemRespDTO3);
        final DisplayItemRespDTO displayItemRespDTO4 = new DisplayItemRespDTO();
        displayItemRespDTO4.setItemGuid("itemGuid");
        displayItemRespDTO4.setItemName("itemName");
        displayItemRespDTO4.setSort(0);
        displayItemRespDTO4.setRuleType(0);
        final List<DisplayItemRespDTO> displayItemRespDTOS2 = Arrays.asList(displayItemRespDTO4);
        when(mockItemService.queryItemInfo(Arrays.asList("value"), displayItemRespDTOS2))
                .thenReturn(displayItemRespDTOS1);

        // Configure DisplayStoreService.list(...).
        final DisplayStoreDO displayStoreDO = new DisplayStoreDO();
        displayStoreDO.setId(0L);
        displayStoreDO.setGuid("ded0f5ab-d2f9-478e-8348-d38f33b9516b");
        displayStoreDO.setIsDelete(0);
        displayStoreDO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayStoreDO.setRuleType(0);
        final List<DisplayStoreDO> displayStoreDOS = Arrays.asList(displayStoreDO);
        when(mockStoreService.list(any(LambdaQueryWrapper.class))).thenReturn(displayStoreDOS);

        // Configure DisplayRuleMapstruct.toStoreRespList(...).
        final DisplayStoreRespDTO displayStoreRespDTO2 = new DisplayStoreRespDTO();
        displayStoreRespDTO2.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO2.setRuleGuid("ruleGuid");
        displayStoreRespDTO2.setStoreGuid("storeGuid");
        displayStoreRespDTO2.setStoreName("storeName");
        displayStoreRespDTO2.setRuleType(0);
        final List<DisplayStoreRespDTO> displayStoreRespDTOS = Arrays.asList(displayStoreRespDTO2);
        final DisplayStoreDO displayStoreDO2 = new DisplayStoreDO();
        displayStoreDO2.setId(0L);
        displayStoreDO2.setGuid("ded0f5ab-d2f9-478e-8348-d38f33b9516b");
        displayStoreDO2.setIsDelete(0);
        displayStoreDO2.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayStoreDO2.setRuleType(0);
        final List<DisplayStoreDO> displayStoreDO1 = Arrays.asList(displayStoreDO2);
        when(mockRuleMapstruct.toStoreRespList(displayStoreDO1)).thenReturn(displayStoreRespDTOS);

        // Configure DisplayStoreService.queryStoreInfo(...).
        final DisplayStoreRespDTO displayStoreRespDTO3 = new DisplayStoreRespDTO();
        displayStoreRespDTO3.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO3.setRuleGuid("ruleGuid");
        displayStoreRespDTO3.setStoreGuid("storeGuid");
        displayStoreRespDTO3.setStoreName("storeName");
        displayStoreRespDTO3.setRuleType(0);
        final List<DisplayStoreRespDTO> displayStoreRespDTOS1 = Arrays.asList(displayStoreRespDTO3);
        final DisplayStoreRespDTO displayStoreRespDTO4 = new DisplayStoreRespDTO();
        displayStoreRespDTO4.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO4.setRuleGuid("ruleGuid");
        displayStoreRespDTO4.setStoreGuid("storeGuid");
        displayStoreRespDTO4.setStoreName("storeName");
        displayStoreRespDTO4.setRuleType(0);
        final List<DisplayStoreRespDTO> displayStoreRespList = Arrays.asList(displayStoreRespDTO4);
        when(mockStoreService.queryStoreInfo(Arrays.asList("value"), displayStoreRespList))
                .thenReturn(displayStoreRespDTOS1);

        // Run the test
        final DisplayRuleRespDTO result = displayRuleServiceImplUnderTest.getRule(reqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetRule_DisplayItemServiceListReturnsNoItems() {
        // Setup
        final DisplayRuleQueryDTO reqDTO = new DisplayRuleQueryDTO();
        reqDTO.setCurrentPage(0L);
        reqDTO.setPageSize(0L);
        reqDTO.setRuleGuid("ruleGuid");
        reqDTO.setRuleType(0);
        reqDTO.setBrandGuid("brandGuid");

        final DisplayRuleRespDTO expectedResult = new DisplayRuleRespDTO();
        expectedResult.setGuid("10e656ef-1e92-4000-9e03-72882340639f");
        expectedResult.setBatch(0);
        expectedResult.setIsAllStore(false);
        expectedResult.setRuleType(0);
        final DisplayStoreRespDTO displayStoreRespDTO = new DisplayStoreRespDTO();
        displayStoreRespDTO.setStoreGuid("storeGuid");
        displayStoreRespDTO.setStoreName("storeName");
        expectedResult.setStoreList(Arrays.asList(displayStoreRespDTO));
        final DisplayItemRespDTO displayItemRespDTO = new DisplayItemRespDTO();
        displayItemRespDTO.setItemGuid("itemGuid");
        displayItemRespDTO.setItemName("itemName");
        expectedResult.setItemList(Arrays.asList(displayItemRespDTO));
        expectedResult.setRuleSort(0);

        // Configure DisplayRuleMapstruct.toRuleRespDTO(...).
        final DisplayRuleRespDTO displayRuleRespDTO = new DisplayRuleRespDTO();
        displayRuleRespDTO.setGuid("10e656ef-1e92-4000-9e03-72882340639f");
        displayRuleRespDTO.setBatch(0);
        displayRuleRespDTO.setIsAllStore(false);
        displayRuleRespDTO.setRuleType(0);
        final DisplayStoreRespDTO displayStoreRespDTO1 = new DisplayStoreRespDTO();
        displayStoreRespDTO1.setStoreGuid("storeGuid");
        displayStoreRespDTO1.setStoreName("storeName");
        displayRuleRespDTO.setStoreList(Arrays.asList(displayStoreRespDTO1));
        final DisplayItemRespDTO displayItemRespDTO1 = new DisplayItemRespDTO();
        displayItemRespDTO1.setItemGuid("itemGuid");
        displayItemRespDTO1.setItemName("itemName");
        displayRuleRespDTO.setItemList(Arrays.asList(displayItemRespDTO1));
        displayRuleRespDTO.setRuleSort(0);
        final DisplayRuleDO ruleDO = new DisplayRuleDO();
        ruleDO.setGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        ruleDO.setIsDelete(0);
        ruleDO.setDelayTime(0);
        ruleDO.setBatch(0);
        ruleDO.setIsAllStore(false);
        ruleDO.setRuleType(0);
        ruleDO.setBrandGuid("brandGuid");
        when(mockRuleMapstruct.toRuleRespDTO(ruleDO)).thenReturn(displayRuleRespDTO);

        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure DisplayRuleMapstruct.toItemRespList(...).
        final DisplayItemRespDTO displayItemRespDTO2 = new DisplayItemRespDTO();
        displayItemRespDTO2.setItemGuid("itemGuid");
        displayItemRespDTO2.setItemName("itemName");
        displayItemRespDTO2.setSort(0);
        displayItemRespDTO2.setRuleType(0);
        final List<DisplayItemRespDTO> displayItemRespDTOS = Arrays.asList(displayItemRespDTO2);
        final DisplayItemDO displayItemDO1 = new DisplayItemDO();
        displayItemDO1.setGuid("7e5ba61b-f27d-4746-abc1-480b55cad459");
        displayItemDO1.setIsDelete(0);
        displayItemDO1.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayItemDO1.setItemGuid("itemGuid");
        displayItemDO1.setSort(0);
        displayItemDO1.setRuleType(0);
        final List<DisplayItemDO> displayItemDO = Arrays.asList(displayItemDO1);
        when(mockRuleMapstruct.toItemRespList(displayItemDO)).thenReturn(displayItemRespDTOS);

        // Configure DisplayItemService.queryItemInfo(...).
        final DisplayItemRespDTO displayItemRespDTO3 = new DisplayItemRespDTO();
        displayItemRespDTO3.setItemGuid("itemGuid");
        displayItemRespDTO3.setItemName("itemName");
        displayItemRespDTO3.setSort(0);
        displayItemRespDTO3.setRuleType(0);
        final List<DisplayItemRespDTO> displayItemRespDTOS1 = Arrays.asList(displayItemRespDTO3);
        final DisplayItemRespDTO displayItemRespDTO4 = new DisplayItemRespDTO();
        displayItemRespDTO4.setItemGuid("itemGuid");
        displayItemRespDTO4.setItemName("itemName");
        displayItemRespDTO4.setSort(0);
        displayItemRespDTO4.setRuleType(0);
        final List<DisplayItemRespDTO> displayItemRespDTOS2 = Arrays.asList(displayItemRespDTO4);
        when(mockItemService.queryItemInfo(Arrays.asList("value"), displayItemRespDTOS2))
                .thenReturn(displayItemRespDTOS1);

        // Configure DisplayStoreService.list(...).
        final DisplayStoreDO displayStoreDO = new DisplayStoreDO();
        displayStoreDO.setId(0L);
        displayStoreDO.setGuid("ded0f5ab-d2f9-478e-8348-d38f33b9516b");
        displayStoreDO.setIsDelete(0);
        displayStoreDO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayStoreDO.setRuleType(0);
        final List<DisplayStoreDO> displayStoreDOS = Arrays.asList(displayStoreDO);
        when(mockStoreService.list(any(LambdaQueryWrapper.class))).thenReturn(displayStoreDOS);

        // Configure DisplayRuleMapstruct.toStoreRespList(...).
        final DisplayStoreRespDTO displayStoreRespDTO2 = new DisplayStoreRespDTO();
        displayStoreRespDTO2.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO2.setRuleGuid("ruleGuid");
        displayStoreRespDTO2.setStoreGuid("storeGuid");
        displayStoreRespDTO2.setStoreName("storeName");
        displayStoreRespDTO2.setRuleType(0);
        final List<DisplayStoreRespDTO> displayStoreRespDTOS = Arrays.asList(displayStoreRespDTO2);
        final DisplayStoreDO displayStoreDO2 = new DisplayStoreDO();
        displayStoreDO2.setId(0L);
        displayStoreDO2.setGuid("ded0f5ab-d2f9-478e-8348-d38f33b9516b");
        displayStoreDO2.setIsDelete(0);
        displayStoreDO2.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayStoreDO2.setRuleType(0);
        final List<DisplayStoreDO> displayStoreDO1 = Arrays.asList(displayStoreDO2);
        when(mockRuleMapstruct.toStoreRespList(displayStoreDO1)).thenReturn(displayStoreRespDTOS);

        // Configure DisplayStoreService.queryStoreInfo(...).
        final DisplayStoreRespDTO displayStoreRespDTO3 = new DisplayStoreRespDTO();
        displayStoreRespDTO3.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO3.setRuleGuid("ruleGuid");
        displayStoreRespDTO3.setStoreGuid("storeGuid");
        displayStoreRespDTO3.setStoreName("storeName");
        displayStoreRespDTO3.setRuleType(0);
        final List<DisplayStoreRespDTO> displayStoreRespDTOS1 = Arrays.asList(displayStoreRespDTO3);
        final DisplayStoreRespDTO displayStoreRespDTO4 = new DisplayStoreRespDTO();
        displayStoreRespDTO4.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO4.setRuleGuid("ruleGuid");
        displayStoreRespDTO4.setStoreGuid("storeGuid");
        displayStoreRespDTO4.setStoreName("storeName");
        displayStoreRespDTO4.setRuleType(0);
        final List<DisplayStoreRespDTO> displayStoreRespList = Arrays.asList(displayStoreRespDTO4);
        when(mockStoreService.queryStoreInfo(Arrays.asList("value"), displayStoreRespList))
                .thenReturn(displayStoreRespDTOS1);

        // Run the test
        final DisplayRuleRespDTO result = displayRuleServiceImplUnderTest.getRule(reqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetRule_DisplayRuleMapstructToItemRespListReturnsNoItems() {
        // Setup
        final DisplayRuleQueryDTO reqDTO = new DisplayRuleQueryDTO();
        reqDTO.setCurrentPage(0L);
        reqDTO.setPageSize(0L);
        reqDTO.setRuleGuid("ruleGuid");
        reqDTO.setRuleType(0);
        reqDTO.setBrandGuid("brandGuid");

        final DisplayRuleRespDTO expectedResult = new DisplayRuleRespDTO();
        expectedResult.setGuid("10e656ef-1e92-4000-9e03-72882340639f");
        expectedResult.setBatch(0);
        expectedResult.setIsAllStore(false);
        expectedResult.setRuleType(0);
        final DisplayStoreRespDTO displayStoreRespDTO = new DisplayStoreRespDTO();
        displayStoreRespDTO.setStoreGuid("storeGuid");
        displayStoreRespDTO.setStoreName("storeName");
        expectedResult.setStoreList(Arrays.asList(displayStoreRespDTO));
        final DisplayItemRespDTO displayItemRespDTO = new DisplayItemRespDTO();
        displayItemRespDTO.setItemGuid("itemGuid");
        displayItemRespDTO.setItemName("itemName");
        expectedResult.setItemList(Arrays.asList(displayItemRespDTO));
        expectedResult.setRuleSort(0);

        // Configure DisplayRuleMapstruct.toRuleRespDTO(...).
        final DisplayRuleRespDTO displayRuleRespDTO = new DisplayRuleRespDTO();
        displayRuleRespDTO.setGuid("10e656ef-1e92-4000-9e03-72882340639f");
        displayRuleRespDTO.setBatch(0);
        displayRuleRespDTO.setIsAllStore(false);
        displayRuleRespDTO.setRuleType(0);
        final DisplayStoreRespDTO displayStoreRespDTO1 = new DisplayStoreRespDTO();
        displayStoreRespDTO1.setStoreGuid("storeGuid");
        displayStoreRespDTO1.setStoreName("storeName");
        displayRuleRespDTO.setStoreList(Arrays.asList(displayStoreRespDTO1));
        final DisplayItemRespDTO displayItemRespDTO1 = new DisplayItemRespDTO();
        displayItemRespDTO1.setItemGuid("itemGuid");
        displayItemRespDTO1.setItemName("itemName");
        displayRuleRespDTO.setItemList(Arrays.asList(displayItemRespDTO1));
        displayRuleRespDTO.setRuleSort(0);
        final DisplayRuleDO ruleDO = new DisplayRuleDO();
        ruleDO.setGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        ruleDO.setIsDelete(0);
        ruleDO.setDelayTime(0);
        ruleDO.setBatch(0);
        ruleDO.setIsAllStore(false);
        ruleDO.setRuleType(0);
        ruleDO.setBrandGuid("brandGuid");
        when(mockRuleMapstruct.toRuleRespDTO(ruleDO)).thenReturn(displayRuleRespDTO);

        // Configure DisplayItemService.list(...).
        final DisplayItemDO displayItemDO = new DisplayItemDO();
        displayItemDO.setGuid("7e5ba61b-f27d-4746-abc1-480b55cad459");
        displayItemDO.setIsDelete(0);
        displayItemDO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayItemDO.setItemGuid("itemGuid");
        displayItemDO.setSort(0);
        displayItemDO.setRuleType(0);
        final List<DisplayItemDO> displayItemDOS = Arrays.asList(displayItemDO);
        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(displayItemDOS);

        // Configure DisplayRuleMapstruct.toItemRespList(...).
        final DisplayItemDO displayItemDO2 = new DisplayItemDO();
        displayItemDO2.setGuid("7e5ba61b-f27d-4746-abc1-480b55cad459");
        displayItemDO2.setIsDelete(0);
        displayItemDO2.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayItemDO2.setItemGuid("itemGuid");
        displayItemDO2.setSort(0);
        displayItemDO2.setRuleType(0);
        final List<DisplayItemDO> displayItemDO1 = Arrays.asList(displayItemDO2);
        when(mockRuleMapstruct.toItemRespList(displayItemDO1)).thenReturn(Collections.emptyList());

        // Configure DisplayStoreService.list(...).
        final DisplayStoreDO displayStoreDO = new DisplayStoreDO();
        displayStoreDO.setId(0L);
        displayStoreDO.setGuid("ded0f5ab-d2f9-478e-8348-d38f33b9516b");
        displayStoreDO.setIsDelete(0);
        displayStoreDO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayStoreDO.setRuleType(0);
        final List<DisplayStoreDO> displayStoreDOS = Arrays.asList(displayStoreDO);
        when(mockStoreService.list(any(LambdaQueryWrapper.class))).thenReturn(displayStoreDOS);

        // Configure DisplayRuleMapstruct.toStoreRespList(...).
        final DisplayStoreRespDTO displayStoreRespDTO2 = new DisplayStoreRespDTO();
        displayStoreRespDTO2.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO2.setRuleGuid("ruleGuid");
        displayStoreRespDTO2.setStoreGuid("storeGuid");
        displayStoreRespDTO2.setStoreName("storeName");
        displayStoreRespDTO2.setRuleType(0);
        final List<DisplayStoreRespDTO> displayStoreRespDTOS = Arrays.asList(displayStoreRespDTO2);
        final DisplayStoreDO displayStoreDO2 = new DisplayStoreDO();
        displayStoreDO2.setId(0L);
        displayStoreDO2.setGuid("ded0f5ab-d2f9-478e-8348-d38f33b9516b");
        displayStoreDO2.setIsDelete(0);
        displayStoreDO2.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayStoreDO2.setRuleType(0);
        final List<DisplayStoreDO> displayStoreDO1 = Arrays.asList(displayStoreDO2);
        when(mockRuleMapstruct.toStoreRespList(displayStoreDO1)).thenReturn(displayStoreRespDTOS);

        // Configure DisplayStoreService.queryStoreInfo(...).
        final DisplayStoreRespDTO displayStoreRespDTO3 = new DisplayStoreRespDTO();
        displayStoreRespDTO3.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO3.setRuleGuid("ruleGuid");
        displayStoreRespDTO3.setStoreGuid("storeGuid");
        displayStoreRespDTO3.setStoreName("storeName");
        displayStoreRespDTO3.setRuleType(0);
        final List<DisplayStoreRespDTO> displayStoreRespDTOS1 = Arrays.asList(displayStoreRespDTO3);
        final DisplayStoreRespDTO displayStoreRespDTO4 = new DisplayStoreRespDTO();
        displayStoreRespDTO4.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO4.setRuleGuid("ruleGuid");
        displayStoreRespDTO4.setStoreGuid("storeGuid");
        displayStoreRespDTO4.setStoreName("storeName");
        displayStoreRespDTO4.setRuleType(0);
        final List<DisplayStoreRespDTO> displayStoreRespList = Arrays.asList(displayStoreRespDTO4);
        when(mockStoreService.queryStoreInfo(Arrays.asList("value"), displayStoreRespList))
                .thenReturn(displayStoreRespDTOS1);

        // Run the test
        final DisplayRuleRespDTO result = displayRuleServiceImplUnderTest.getRule(reqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetRule_DisplayItemServiceQueryItemInfoReturnsNoItems() {
        // Setup
        final DisplayRuleQueryDTO reqDTO = new DisplayRuleQueryDTO();
        reqDTO.setCurrentPage(0L);
        reqDTO.setPageSize(0L);
        reqDTO.setRuleGuid("ruleGuid");
        reqDTO.setRuleType(0);
        reqDTO.setBrandGuid("brandGuid");

        final DisplayRuleRespDTO expectedResult = new DisplayRuleRespDTO();
        expectedResult.setGuid("10e656ef-1e92-4000-9e03-72882340639f");
        expectedResult.setBatch(0);
        expectedResult.setIsAllStore(false);
        expectedResult.setRuleType(0);
        final DisplayStoreRespDTO displayStoreRespDTO = new DisplayStoreRespDTO();
        displayStoreRespDTO.setStoreGuid("storeGuid");
        displayStoreRespDTO.setStoreName("storeName");
        expectedResult.setStoreList(Arrays.asList(displayStoreRespDTO));
        final DisplayItemRespDTO displayItemRespDTO = new DisplayItemRespDTO();
        displayItemRespDTO.setItemGuid("itemGuid");
        displayItemRespDTO.setItemName("itemName");
        expectedResult.setItemList(Arrays.asList(displayItemRespDTO));
        expectedResult.setRuleSort(0);

        // Configure DisplayRuleMapstruct.toRuleRespDTO(...).
        final DisplayRuleRespDTO displayRuleRespDTO = new DisplayRuleRespDTO();
        displayRuleRespDTO.setGuid("10e656ef-1e92-4000-9e03-72882340639f");
        displayRuleRespDTO.setBatch(0);
        displayRuleRespDTO.setIsAllStore(false);
        displayRuleRespDTO.setRuleType(0);
        final DisplayStoreRespDTO displayStoreRespDTO1 = new DisplayStoreRespDTO();
        displayStoreRespDTO1.setStoreGuid("storeGuid");
        displayStoreRespDTO1.setStoreName("storeName");
        displayRuleRespDTO.setStoreList(Arrays.asList(displayStoreRespDTO1));
        final DisplayItemRespDTO displayItemRespDTO1 = new DisplayItemRespDTO();
        displayItemRespDTO1.setItemGuid("itemGuid");
        displayItemRespDTO1.setItemName("itemName");
        displayRuleRespDTO.setItemList(Arrays.asList(displayItemRespDTO1));
        displayRuleRespDTO.setRuleSort(0);
        final DisplayRuleDO ruleDO = new DisplayRuleDO();
        ruleDO.setGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        ruleDO.setIsDelete(0);
        ruleDO.setDelayTime(0);
        ruleDO.setBatch(0);
        ruleDO.setIsAllStore(false);
        ruleDO.setRuleType(0);
        ruleDO.setBrandGuid("brandGuid");
        when(mockRuleMapstruct.toRuleRespDTO(ruleDO)).thenReturn(displayRuleRespDTO);

        // Configure DisplayItemService.list(...).
        final DisplayItemDO displayItemDO = new DisplayItemDO();
        displayItemDO.setGuid("7e5ba61b-f27d-4746-abc1-480b55cad459");
        displayItemDO.setIsDelete(0);
        displayItemDO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayItemDO.setItemGuid("itemGuid");
        displayItemDO.setSort(0);
        displayItemDO.setRuleType(0);
        final List<DisplayItemDO> displayItemDOS = Arrays.asList(displayItemDO);
        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(displayItemDOS);

        // Configure DisplayRuleMapstruct.toItemRespList(...).
        final DisplayItemRespDTO displayItemRespDTO2 = new DisplayItemRespDTO();
        displayItemRespDTO2.setItemGuid("itemGuid");
        displayItemRespDTO2.setItemName("itemName");
        displayItemRespDTO2.setSort(0);
        displayItemRespDTO2.setRuleType(0);
        final List<DisplayItemRespDTO> displayItemRespDTOS = Arrays.asList(displayItemRespDTO2);
        final DisplayItemDO displayItemDO2 = new DisplayItemDO();
        displayItemDO2.setGuid("7e5ba61b-f27d-4746-abc1-480b55cad459");
        displayItemDO2.setIsDelete(0);
        displayItemDO2.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayItemDO2.setItemGuid("itemGuid");
        displayItemDO2.setSort(0);
        displayItemDO2.setRuleType(0);
        final List<DisplayItemDO> displayItemDO1 = Arrays.asList(displayItemDO2);
        when(mockRuleMapstruct.toItemRespList(displayItemDO1)).thenReturn(displayItemRespDTOS);

        // Configure DisplayItemService.queryItemInfo(...).
        final DisplayItemRespDTO displayItemRespDTO3 = new DisplayItemRespDTO();
        displayItemRespDTO3.setItemGuid("itemGuid");
        displayItemRespDTO3.setItemName("itemName");
        displayItemRespDTO3.setSort(0);
        displayItemRespDTO3.setRuleType(0);
        final List<DisplayItemRespDTO> displayItemRespDTOS1 = Arrays.asList(displayItemRespDTO3);
        when(mockItemService.queryItemInfo(Arrays.asList("value"), displayItemRespDTOS1))
                .thenReturn(Collections.emptyList());

        // Configure DisplayStoreService.list(...).
        final DisplayStoreDO displayStoreDO = new DisplayStoreDO();
        displayStoreDO.setId(0L);
        displayStoreDO.setGuid("ded0f5ab-d2f9-478e-8348-d38f33b9516b");
        displayStoreDO.setIsDelete(0);
        displayStoreDO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayStoreDO.setRuleType(0);
        final List<DisplayStoreDO> displayStoreDOS = Arrays.asList(displayStoreDO);
        when(mockStoreService.list(any(LambdaQueryWrapper.class))).thenReturn(displayStoreDOS);

        // Configure DisplayRuleMapstruct.toStoreRespList(...).
        final DisplayStoreRespDTO displayStoreRespDTO2 = new DisplayStoreRespDTO();
        displayStoreRespDTO2.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO2.setRuleGuid("ruleGuid");
        displayStoreRespDTO2.setStoreGuid("storeGuid");
        displayStoreRespDTO2.setStoreName("storeName");
        displayStoreRespDTO2.setRuleType(0);
        final List<DisplayStoreRespDTO> displayStoreRespDTOS = Arrays.asList(displayStoreRespDTO2);
        final DisplayStoreDO displayStoreDO2 = new DisplayStoreDO();
        displayStoreDO2.setId(0L);
        displayStoreDO2.setGuid("ded0f5ab-d2f9-478e-8348-d38f33b9516b");
        displayStoreDO2.setIsDelete(0);
        displayStoreDO2.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayStoreDO2.setRuleType(0);
        final List<DisplayStoreDO> displayStoreDO1 = Arrays.asList(displayStoreDO2);
        when(mockRuleMapstruct.toStoreRespList(displayStoreDO1)).thenReturn(displayStoreRespDTOS);

        // Configure DisplayStoreService.queryStoreInfo(...).
        final DisplayStoreRespDTO displayStoreRespDTO3 = new DisplayStoreRespDTO();
        displayStoreRespDTO3.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO3.setRuleGuid("ruleGuid");
        displayStoreRespDTO3.setStoreGuid("storeGuid");
        displayStoreRespDTO3.setStoreName("storeName");
        displayStoreRespDTO3.setRuleType(0);
        final List<DisplayStoreRespDTO> displayStoreRespDTOS1 = Arrays.asList(displayStoreRespDTO3);
        final DisplayStoreRespDTO displayStoreRespDTO4 = new DisplayStoreRespDTO();
        displayStoreRespDTO4.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO4.setRuleGuid("ruleGuid");
        displayStoreRespDTO4.setStoreGuid("storeGuid");
        displayStoreRespDTO4.setStoreName("storeName");
        displayStoreRespDTO4.setRuleType(0);
        final List<DisplayStoreRespDTO> displayStoreRespList = Arrays.asList(displayStoreRespDTO4);
        when(mockStoreService.queryStoreInfo(Arrays.asList("value"), displayStoreRespList))
                .thenReturn(displayStoreRespDTOS1);

        // Run the test
        final DisplayRuleRespDTO result = displayRuleServiceImplUnderTest.getRule(reqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetRule_DisplayStoreServiceListReturnsNoItems() {
        // Setup
        final DisplayRuleQueryDTO reqDTO = new DisplayRuleQueryDTO();
        reqDTO.setCurrentPage(0L);
        reqDTO.setPageSize(0L);
        reqDTO.setRuleGuid("ruleGuid");
        reqDTO.setRuleType(0);
        reqDTO.setBrandGuid("brandGuid");

        final DisplayRuleRespDTO expectedResult = new DisplayRuleRespDTO();
        expectedResult.setGuid("10e656ef-1e92-4000-9e03-72882340639f");
        expectedResult.setBatch(0);
        expectedResult.setIsAllStore(false);
        expectedResult.setRuleType(0);
        final DisplayStoreRespDTO displayStoreRespDTO = new DisplayStoreRespDTO();
        displayStoreRespDTO.setStoreGuid("storeGuid");
        displayStoreRespDTO.setStoreName("storeName");
        expectedResult.setStoreList(Arrays.asList(displayStoreRespDTO));
        final DisplayItemRespDTO displayItemRespDTO = new DisplayItemRespDTO();
        displayItemRespDTO.setItemGuid("itemGuid");
        displayItemRespDTO.setItemName("itemName");
        expectedResult.setItemList(Arrays.asList(displayItemRespDTO));
        expectedResult.setRuleSort(0);

        // Configure DisplayRuleMapstruct.toRuleRespDTO(...).
        final DisplayRuleRespDTO displayRuleRespDTO = new DisplayRuleRespDTO();
        displayRuleRespDTO.setGuid("10e656ef-1e92-4000-9e03-72882340639f");
        displayRuleRespDTO.setBatch(0);
        displayRuleRespDTO.setIsAllStore(false);
        displayRuleRespDTO.setRuleType(0);
        final DisplayStoreRespDTO displayStoreRespDTO1 = new DisplayStoreRespDTO();
        displayStoreRespDTO1.setStoreGuid("storeGuid");
        displayStoreRespDTO1.setStoreName("storeName");
        displayRuleRespDTO.setStoreList(Arrays.asList(displayStoreRespDTO1));
        final DisplayItemRespDTO displayItemRespDTO1 = new DisplayItemRespDTO();
        displayItemRespDTO1.setItemGuid("itemGuid");
        displayItemRespDTO1.setItemName("itemName");
        displayRuleRespDTO.setItemList(Arrays.asList(displayItemRespDTO1));
        displayRuleRespDTO.setRuleSort(0);
        final DisplayRuleDO ruleDO = new DisplayRuleDO();
        ruleDO.setGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        ruleDO.setIsDelete(0);
        ruleDO.setDelayTime(0);
        ruleDO.setBatch(0);
        ruleDO.setIsAllStore(false);
        ruleDO.setRuleType(0);
        ruleDO.setBrandGuid("brandGuid");
        when(mockRuleMapstruct.toRuleRespDTO(ruleDO)).thenReturn(displayRuleRespDTO);

        // Configure DisplayItemService.list(...).
        final DisplayItemDO displayItemDO = new DisplayItemDO();
        displayItemDO.setGuid("7e5ba61b-f27d-4746-abc1-480b55cad459");
        displayItemDO.setIsDelete(0);
        displayItemDO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayItemDO.setItemGuid("itemGuid");
        displayItemDO.setSort(0);
        displayItemDO.setRuleType(0);
        final List<DisplayItemDO> displayItemDOS = Arrays.asList(displayItemDO);
        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(displayItemDOS);

        // Configure DisplayRuleMapstruct.toItemRespList(...).
        final DisplayItemRespDTO displayItemRespDTO2 = new DisplayItemRespDTO();
        displayItemRespDTO2.setItemGuid("itemGuid");
        displayItemRespDTO2.setItemName("itemName");
        displayItemRespDTO2.setSort(0);
        displayItemRespDTO2.setRuleType(0);
        final List<DisplayItemRespDTO> displayItemRespDTOS = Arrays.asList(displayItemRespDTO2);
        final DisplayItemDO displayItemDO2 = new DisplayItemDO();
        displayItemDO2.setGuid("7e5ba61b-f27d-4746-abc1-480b55cad459");
        displayItemDO2.setIsDelete(0);
        displayItemDO2.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayItemDO2.setItemGuid("itemGuid");
        displayItemDO2.setSort(0);
        displayItemDO2.setRuleType(0);
        final List<DisplayItemDO> displayItemDO1 = Arrays.asList(displayItemDO2);
        when(mockRuleMapstruct.toItemRespList(displayItemDO1)).thenReturn(displayItemRespDTOS);

        // Configure DisplayItemService.queryItemInfo(...).
        final DisplayItemRespDTO displayItemRespDTO3 = new DisplayItemRespDTO();
        displayItemRespDTO3.setItemGuid("itemGuid");
        displayItemRespDTO3.setItemName("itemName");
        displayItemRespDTO3.setSort(0);
        displayItemRespDTO3.setRuleType(0);
        final List<DisplayItemRespDTO> displayItemRespDTOS1 = Arrays.asList(displayItemRespDTO3);
        final DisplayItemRespDTO displayItemRespDTO4 = new DisplayItemRespDTO();
        displayItemRespDTO4.setItemGuid("itemGuid");
        displayItemRespDTO4.setItemName("itemName");
        displayItemRespDTO4.setSort(0);
        displayItemRespDTO4.setRuleType(0);
        final List<DisplayItemRespDTO> displayItemRespDTOS2 = Arrays.asList(displayItemRespDTO4);
        when(mockItemService.queryItemInfo(Arrays.asList("value"), displayItemRespDTOS2))
                .thenReturn(displayItemRespDTOS1);

        when(mockStoreService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure DisplayRuleMapstruct.toStoreRespList(...).
        final DisplayStoreRespDTO displayStoreRespDTO2 = new DisplayStoreRespDTO();
        displayStoreRespDTO2.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO2.setRuleGuid("ruleGuid");
        displayStoreRespDTO2.setStoreGuid("storeGuid");
        displayStoreRespDTO2.setStoreName("storeName");
        displayStoreRespDTO2.setRuleType(0);
        final List<DisplayStoreRespDTO> displayStoreRespDTOS = Arrays.asList(displayStoreRespDTO2);
        final DisplayStoreDO displayStoreDO1 = new DisplayStoreDO();
        displayStoreDO1.setId(0L);
        displayStoreDO1.setGuid("ded0f5ab-d2f9-478e-8348-d38f33b9516b");
        displayStoreDO1.setIsDelete(0);
        displayStoreDO1.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayStoreDO1.setRuleType(0);
        final List<DisplayStoreDO> displayStoreDO = Arrays.asList(displayStoreDO1);
        when(mockRuleMapstruct.toStoreRespList(displayStoreDO)).thenReturn(displayStoreRespDTOS);

        // Configure DisplayStoreService.queryStoreInfo(...).
        final DisplayStoreRespDTO displayStoreRespDTO3 = new DisplayStoreRespDTO();
        displayStoreRespDTO3.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO3.setRuleGuid("ruleGuid");
        displayStoreRespDTO3.setStoreGuid("storeGuid");
        displayStoreRespDTO3.setStoreName("storeName");
        displayStoreRespDTO3.setRuleType(0);
        final List<DisplayStoreRespDTO> displayStoreRespDTOS1 = Arrays.asList(displayStoreRespDTO3);
        final DisplayStoreRespDTO displayStoreRespDTO4 = new DisplayStoreRespDTO();
        displayStoreRespDTO4.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO4.setRuleGuid("ruleGuid");
        displayStoreRespDTO4.setStoreGuid("storeGuid");
        displayStoreRespDTO4.setStoreName("storeName");
        displayStoreRespDTO4.setRuleType(0);
        final List<DisplayStoreRespDTO> displayStoreRespList = Arrays.asList(displayStoreRespDTO4);
        when(mockStoreService.queryStoreInfo(Arrays.asList("value"), displayStoreRespList))
                .thenReturn(displayStoreRespDTOS1);

        // Run the test
        final DisplayRuleRespDTO result = displayRuleServiceImplUnderTest.getRule(reqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetRule_DisplayRuleMapstructToStoreRespListReturnsNoItems() {
        // Setup
        final DisplayRuleQueryDTO reqDTO = new DisplayRuleQueryDTO();
        reqDTO.setCurrentPage(0L);
        reqDTO.setPageSize(0L);
        reqDTO.setRuleGuid("ruleGuid");
        reqDTO.setRuleType(0);
        reqDTO.setBrandGuid("brandGuid");

        final DisplayRuleRespDTO expectedResult = new DisplayRuleRespDTO();
        expectedResult.setGuid("10e656ef-1e92-4000-9e03-72882340639f");
        expectedResult.setBatch(0);
        expectedResult.setIsAllStore(false);
        expectedResult.setRuleType(0);
        final DisplayStoreRespDTO displayStoreRespDTO = new DisplayStoreRespDTO();
        displayStoreRespDTO.setStoreGuid("storeGuid");
        displayStoreRespDTO.setStoreName("storeName");
        expectedResult.setStoreList(Arrays.asList(displayStoreRespDTO));
        final DisplayItemRespDTO displayItemRespDTO = new DisplayItemRespDTO();
        displayItemRespDTO.setItemGuid("itemGuid");
        displayItemRespDTO.setItemName("itemName");
        expectedResult.setItemList(Arrays.asList(displayItemRespDTO));
        expectedResult.setRuleSort(0);

        // Configure DisplayRuleMapstruct.toRuleRespDTO(...).
        final DisplayRuleRespDTO displayRuleRespDTO = new DisplayRuleRespDTO();
        displayRuleRespDTO.setGuid("10e656ef-1e92-4000-9e03-72882340639f");
        displayRuleRespDTO.setBatch(0);
        displayRuleRespDTO.setIsAllStore(false);
        displayRuleRespDTO.setRuleType(0);
        final DisplayStoreRespDTO displayStoreRespDTO1 = new DisplayStoreRespDTO();
        displayStoreRespDTO1.setStoreGuid("storeGuid");
        displayStoreRespDTO1.setStoreName("storeName");
        displayRuleRespDTO.setStoreList(Arrays.asList(displayStoreRespDTO1));
        final DisplayItemRespDTO displayItemRespDTO1 = new DisplayItemRespDTO();
        displayItemRespDTO1.setItemGuid("itemGuid");
        displayItemRespDTO1.setItemName("itemName");
        displayRuleRespDTO.setItemList(Arrays.asList(displayItemRespDTO1));
        displayRuleRespDTO.setRuleSort(0);
        final DisplayRuleDO ruleDO = new DisplayRuleDO();
        ruleDO.setGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        ruleDO.setIsDelete(0);
        ruleDO.setDelayTime(0);
        ruleDO.setBatch(0);
        ruleDO.setIsAllStore(false);
        ruleDO.setRuleType(0);
        ruleDO.setBrandGuid("brandGuid");
        when(mockRuleMapstruct.toRuleRespDTO(ruleDO)).thenReturn(displayRuleRespDTO);

        // Configure DisplayItemService.list(...).
        final DisplayItemDO displayItemDO = new DisplayItemDO();
        displayItemDO.setGuid("7e5ba61b-f27d-4746-abc1-480b55cad459");
        displayItemDO.setIsDelete(0);
        displayItemDO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayItemDO.setItemGuid("itemGuid");
        displayItemDO.setSort(0);
        displayItemDO.setRuleType(0);
        final List<DisplayItemDO> displayItemDOS = Arrays.asList(displayItemDO);
        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(displayItemDOS);

        // Configure DisplayRuleMapstruct.toItemRespList(...).
        final DisplayItemRespDTO displayItemRespDTO2 = new DisplayItemRespDTO();
        displayItemRespDTO2.setItemGuid("itemGuid");
        displayItemRespDTO2.setItemName("itemName");
        displayItemRespDTO2.setSort(0);
        displayItemRespDTO2.setRuleType(0);
        final List<DisplayItemRespDTO> displayItemRespDTOS = Arrays.asList(displayItemRespDTO2);
        final DisplayItemDO displayItemDO2 = new DisplayItemDO();
        displayItemDO2.setGuid("7e5ba61b-f27d-4746-abc1-480b55cad459");
        displayItemDO2.setIsDelete(0);
        displayItemDO2.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayItemDO2.setItemGuid("itemGuid");
        displayItemDO2.setSort(0);
        displayItemDO2.setRuleType(0);
        final List<DisplayItemDO> displayItemDO1 = Arrays.asList(displayItemDO2);
        when(mockRuleMapstruct.toItemRespList(displayItemDO1)).thenReturn(displayItemRespDTOS);

        // Configure DisplayItemService.queryItemInfo(...).
        final DisplayItemRespDTO displayItemRespDTO3 = new DisplayItemRespDTO();
        displayItemRespDTO3.setItemGuid("itemGuid");
        displayItemRespDTO3.setItemName("itemName");
        displayItemRespDTO3.setSort(0);
        displayItemRespDTO3.setRuleType(0);
        final List<DisplayItemRespDTO> displayItemRespDTOS1 = Arrays.asList(displayItemRespDTO3);
        final DisplayItemRespDTO displayItemRespDTO4 = new DisplayItemRespDTO();
        displayItemRespDTO4.setItemGuid("itemGuid");
        displayItemRespDTO4.setItemName("itemName");
        displayItemRespDTO4.setSort(0);
        displayItemRespDTO4.setRuleType(0);
        final List<DisplayItemRespDTO> displayItemRespDTOS2 = Arrays.asList(displayItemRespDTO4);
        when(mockItemService.queryItemInfo(Arrays.asList("value"), displayItemRespDTOS2))
                .thenReturn(displayItemRespDTOS1);

        // Configure DisplayStoreService.list(...).
        final DisplayStoreDO displayStoreDO = new DisplayStoreDO();
        displayStoreDO.setId(0L);
        displayStoreDO.setGuid("ded0f5ab-d2f9-478e-8348-d38f33b9516b");
        displayStoreDO.setIsDelete(0);
        displayStoreDO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayStoreDO.setRuleType(0);
        final List<DisplayStoreDO> displayStoreDOS = Arrays.asList(displayStoreDO);
        when(mockStoreService.list(any(LambdaQueryWrapper.class))).thenReturn(displayStoreDOS);

        // Configure DisplayRuleMapstruct.toStoreRespList(...).
        final DisplayStoreDO displayStoreDO2 = new DisplayStoreDO();
        displayStoreDO2.setId(0L);
        displayStoreDO2.setGuid("ded0f5ab-d2f9-478e-8348-d38f33b9516b");
        displayStoreDO2.setIsDelete(0);
        displayStoreDO2.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayStoreDO2.setRuleType(0);
        final List<DisplayStoreDO> displayStoreDO1 = Arrays.asList(displayStoreDO2);
        when(mockRuleMapstruct.toStoreRespList(displayStoreDO1)).thenReturn(Collections.emptyList());

        // Run the test
        final DisplayRuleRespDTO result = displayRuleServiceImplUnderTest.getRule(reqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetRule_DisplayStoreServiceQueryStoreInfoReturnsNoItems() {
        // Setup
        final DisplayRuleQueryDTO reqDTO = new DisplayRuleQueryDTO();
        reqDTO.setCurrentPage(0L);
        reqDTO.setPageSize(0L);
        reqDTO.setRuleGuid("ruleGuid");
        reqDTO.setRuleType(0);
        reqDTO.setBrandGuid("brandGuid");

        final DisplayRuleRespDTO expectedResult = new DisplayRuleRespDTO();
        expectedResult.setGuid("10e656ef-1e92-4000-9e03-72882340639f");
        expectedResult.setBatch(0);
        expectedResult.setIsAllStore(false);
        expectedResult.setRuleType(0);
        final DisplayStoreRespDTO displayStoreRespDTO = new DisplayStoreRespDTO();
        displayStoreRespDTO.setStoreGuid("storeGuid");
        displayStoreRespDTO.setStoreName("storeName");
        expectedResult.setStoreList(Arrays.asList(displayStoreRespDTO));
        final DisplayItemRespDTO displayItemRespDTO = new DisplayItemRespDTO();
        displayItemRespDTO.setItemGuid("itemGuid");
        displayItemRespDTO.setItemName("itemName");
        expectedResult.setItemList(Arrays.asList(displayItemRespDTO));
        expectedResult.setRuleSort(0);

        // Configure DisplayRuleMapstruct.toRuleRespDTO(...).
        final DisplayRuleRespDTO displayRuleRespDTO = new DisplayRuleRespDTO();
        displayRuleRespDTO.setGuid("10e656ef-1e92-4000-9e03-72882340639f");
        displayRuleRespDTO.setBatch(0);
        displayRuleRespDTO.setIsAllStore(false);
        displayRuleRespDTO.setRuleType(0);
        final DisplayStoreRespDTO displayStoreRespDTO1 = new DisplayStoreRespDTO();
        displayStoreRespDTO1.setStoreGuid("storeGuid");
        displayStoreRespDTO1.setStoreName("storeName");
        displayRuleRespDTO.setStoreList(Arrays.asList(displayStoreRespDTO1));
        final DisplayItemRespDTO displayItemRespDTO1 = new DisplayItemRespDTO();
        displayItemRespDTO1.setItemGuid("itemGuid");
        displayItemRespDTO1.setItemName("itemName");
        displayRuleRespDTO.setItemList(Arrays.asList(displayItemRespDTO1));
        displayRuleRespDTO.setRuleSort(0);
        final DisplayRuleDO ruleDO = new DisplayRuleDO();
        ruleDO.setGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        ruleDO.setIsDelete(0);
        ruleDO.setDelayTime(0);
        ruleDO.setBatch(0);
        ruleDO.setIsAllStore(false);
        ruleDO.setRuleType(0);
        ruleDO.setBrandGuid("brandGuid");
        when(mockRuleMapstruct.toRuleRespDTO(ruleDO)).thenReturn(displayRuleRespDTO);

        // Configure DisplayItemService.list(...).
        final DisplayItemDO displayItemDO = new DisplayItemDO();
        displayItemDO.setGuid("7e5ba61b-f27d-4746-abc1-480b55cad459");
        displayItemDO.setIsDelete(0);
        displayItemDO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayItemDO.setItemGuid("itemGuid");
        displayItemDO.setSort(0);
        displayItemDO.setRuleType(0);
        final List<DisplayItemDO> displayItemDOS = Arrays.asList(displayItemDO);
        when(mockItemService.list(any(LambdaQueryWrapper.class))).thenReturn(displayItemDOS);

        // Configure DisplayRuleMapstruct.toItemRespList(...).
        final DisplayItemRespDTO displayItemRespDTO2 = new DisplayItemRespDTO();
        displayItemRespDTO2.setItemGuid("itemGuid");
        displayItemRespDTO2.setItemName("itemName");
        displayItemRespDTO2.setSort(0);
        displayItemRespDTO2.setRuleType(0);
        final List<DisplayItemRespDTO> displayItemRespDTOS = Arrays.asList(displayItemRespDTO2);
        final DisplayItemDO displayItemDO2 = new DisplayItemDO();
        displayItemDO2.setGuid("7e5ba61b-f27d-4746-abc1-480b55cad459");
        displayItemDO2.setIsDelete(0);
        displayItemDO2.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayItemDO2.setItemGuid("itemGuid");
        displayItemDO2.setSort(0);
        displayItemDO2.setRuleType(0);
        final List<DisplayItemDO> displayItemDO1 = Arrays.asList(displayItemDO2);
        when(mockRuleMapstruct.toItemRespList(displayItemDO1)).thenReturn(displayItemRespDTOS);

        // Configure DisplayItemService.queryItemInfo(...).
        final DisplayItemRespDTO displayItemRespDTO3 = new DisplayItemRespDTO();
        displayItemRespDTO3.setItemGuid("itemGuid");
        displayItemRespDTO3.setItemName("itemName");
        displayItemRespDTO3.setSort(0);
        displayItemRespDTO3.setRuleType(0);
        final List<DisplayItemRespDTO> displayItemRespDTOS1 = Arrays.asList(displayItemRespDTO3);
        final DisplayItemRespDTO displayItemRespDTO4 = new DisplayItemRespDTO();
        displayItemRespDTO4.setItemGuid("itemGuid");
        displayItemRespDTO4.setItemName("itemName");
        displayItemRespDTO4.setSort(0);
        displayItemRespDTO4.setRuleType(0);
        final List<DisplayItemRespDTO> displayItemRespDTOS2 = Arrays.asList(displayItemRespDTO4);
        when(mockItemService.queryItemInfo(Arrays.asList("value"), displayItemRespDTOS2))
                .thenReturn(displayItemRespDTOS1);

        // Configure DisplayStoreService.list(...).
        final DisplayStoreDO displayStoreDO = new DisplayStoreDO();
        displayStoreDO.setId(0L);
        displayStoreDO.setGuid("ded0f5ab-d2f9-478e-8348-d38f33b9516b");
        displayStoreDO.setIsDelete(0);
        displayStoreDO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayStoreDO.setRuleType(0);
        final List<DisplayStoreDO> displayStoreDOS = Arrays.asList(displayStoreDO);
        when(mockStoreService.list(any(LambdaQueryWrapper.class))).thenReturn(displayStoreDOS);

        // Configure DisplayRuleMapstruct.toStoreRespList(...).
        final DisplayStoreRespDTO displayStoreRespDTO2 = new DisplayStoreRespDTO();
        displayStoreRespDTO2.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO2.setRuleGuid("ruleGuid");
        displayStoreRespDTO2.setStoreGuid("storeGuid");
        displayStoreRespDTO2.setStoreName("storeName");
        displayStoreRespDTO2.setRuleType(0);
        final List<DisplayStoreRespDTO> displayStoreRespDTOS = Arrays.asList(displayStoreRespDTO2);
        final DisplayStoreDO displayStoreDO2 = new DisplayStoreDO();
        displayStoreDO2.setId(0L);
        displayStoreDO2.setGuid("ded0f5ab-d2f9-478e-8348-d38f33b9516b");
        displayStoreDO2.setIsDelete(0);
        displayStoreDO2.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayStoreDO2.setRuleType(0);
        final List<DisplayStoreDO> displayStoreDO1 = Arrays.asList(displayStoreDO2);
        when(mockRuleMapstruct.toStoreRespList(displayStoreDO1)).thenReturn(displayStoreRespDTOS);

        // Configure DisplayStoreService.queryStoreInfo(...).
        final DisplayStoreRespDTO displayStoreRespDTO3 = new DisplayStoreRespDTO();
        displayStoreRespDTO3.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO3.setRuleGuid("ruleGuid");
        displayStoreRespDTO3.setStoreGuid("storeGuid");
        displayStoreRespDTO3.setStoreName("storeName");
        displayStoreRespDTO3.setRuleType(0);
        final List<DisplayStoreRespDTO> displayStoreRespList = Arrays.asList(displayStoreRespDTO3);
        when(mockStoreService.queryStoreInfo(Arrays.asList("value"), displayStoreRespList))
                .thenReturn(Collections.emptyList());

        // Run the test
        final DisplayRuleRespDTO result = displayRuleServiceImplUnderTest.getRule(reqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testUpdateRule() {
        // Setup
        final DisplayRuleSaveOrUpdateDTO reqDTO = new DisplayRuleSaveOrUpdateDTO();
        reqDTO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        reqDTO.setDisplayState(0);
        reqDTO.setDelayTime(0);
        reqDTO.setBatch(0);
        reqDTO.setEffectiveState(0);
        reqDTO.setEffectiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO.setIsAllStore(false);
        reqDTO.setRuleType(0);
        final DisplayStoreRespDTO displayStoreRespDTO = new DisplayStoreRespDTO();
        displayStoreRespDTO.setStoreGuid("storeGuid");
        displayStoreRespDTO.setStoreName("storeName");
        reqDTO.setStoreList(Arrays.asList(displayStoreRespDTO));
        final DisplayItemRespDTO displayItemRespDTO = new DisplayItemRespDTO();
        displayItemRespDTO.setItemGuid("itemGuid");
        displayItemRespDTO.setItemName("itemName");
        reqDTO.setItemList(Arrays.asList(displayItemRespDTO));
        reqDTO.setBrandGuid("brandGuid");

        // Configure DisplayRuleMapstruct.ruleReqToRuleDO(...).
        final DisplayRuleDO displayRuleDO = new DisplayRuleDO();
        displayRuleDO.setGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayRuleDO.setIsDelete(0);
        displayRuleDO.setDelayTime(0);
        displayRuleDO.setBatch(0);
        displayRuleDO.setIsAllStore(false);
        displayRuleDO.setRuleType(0);
        displayRuleDO.setBrandGuid("brandGuid");
        final DisplayRuleSaveOrUpdateDTO ruleReqDTO = new DisplayRuleSaveOrUpdateDTO();
        ruleReqDTO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        ruleReqDTO.setDisplayState(0);
        ruleReqDTO.setDelayTime(0);
        ruleReqDTO.setBatch(0);
        ruleReqDTO.setEffectiveState(0);
        ruleReqDTO.setEffectiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        ruleReqDTO.setIsAllStore(false);
        ruleReqDTO.setRuleType(0);
        final DisplayStoreRespDTO displayStoreRespDTO1 = new DisplayStoreRespDTO();
        displayStoreRespDTO1.setStoreGuid("storeGuid");
        displayStoreRespDTO1.setStoreName("storeName");
        ruleReqDTO.setStoreList(Arrays.asList(displayStoreRespDTO1));
        final DisplayItemRespDTO displayItemRespDTO1 = new DisplayItemRespDTO();
        displayItemRespDTO1.setItemGuid("itemGuid");
        displayItemRespDTO1.setItemName("itemName");
        ruleReqDTO.setItemList(Arrays.asList(displayItemRespDTO1));
        ruleReqDTO.setBrandGuid("brandGuid");
        when(mockRuleMapstruct.ruleReqToRuleDO(ruleReqDTO)).thenReturn(displayRuleDO);

        // Configure DisplayRuleMapstruct.toItemDOList(...).
        final DisplayItemDO displayItemDO = new DisplayItemDO();
        displayItemDO.setGuid("7e5ba61b-f27d-4746-abc1-480b55cad459");
        displayItemDO.setIsDelete(0);
        displayItemDO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayItemDO.setItemGuid("itemGuid");
        displayItemDO.setSort(0);
        displayItemDO.setRuleType(0);
        final List<DisplayItemDO> displayItemDOS = Arrays.asList(displayItemDO);
        final DisplayItemRespDTO displayItemRespDTO2 = new DisplayItemRespDTO();
        displayItemRespDTO2.setItemGuid("itemGuid");
        displayItemRespDTO2.setItemName("itemName");
        displayItemRespDTO2.setSort(0);
        displayItemRespDTO2.setRuleType(0);
        final List<DisplayItemRespDTO> itemList = Arrays.asList(displayItemRespDTO2);
        when(mockRuleMapstruct.toItemDOList(itemList)).thenReturn(displayItemDOS);

        when(mockDistributedIdService.nextBatchDisplayItemGuid(0L)).thenReturn(Arrays.asList("value"));

        // Configure DisplayItemService.listItem(...).
        final DisplayItemRespDTO displayItemRespDTO3 = new DisplayItemRespDTO();
        displayItemRespDTO3.setItemGuid("itemGuid");
        displayItemRespDTO3.setItemName("itemName");
        displayItemRespDTO3.setSort(0);
        displayItemRespDTO3.setRuleType(0);
        final List<DisplayItemRespDTO> displayItemRespDTOS = Arrays.asList(displayItemRespDTO3);
        final DisplayRuleQueryDTO reqDTO1 = new DisplayRuleQueryDTO();
        reqDTO1.setCurrentPage(0L);
        reqDTO1.setPageSize(0L);
        reqDTO1.setRuleGuid("ruleGuid");
        reqDTO1.setRuleType(0);
        reqDTO1.setBrandGuid("brandGuid");
        when(mockItemService.listItem(reqDTO1)).thenReturn(displayItemRespDTOS);

        // Configure DisplayItemService.saveBatch(...).
        final DisplayItemDO displayItemDO1 = new DisplayItemDO();
        displayItemDO1.setGuid("7e5ba61b-f27d-4746-abc1-480b55cad459");
        displayItemDO1.setIsDelete(0);
        displayItemDO1.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayItemDO1.setItemGuid("itemGuid");
        displayItemDO1.setSort(0);
        displayItemDO1.setRuleType(0);
        final List<DisplayItemDO> entityList = Arrays.asList(displayItemDO1);
        when(mockItemService.saveBatch(entityList)).thenReturn(false);

        // Configure DisplayRuleMapstruct.toStoreDOList(...).
        final DisplayStoreDO displayStoreDO = new DisplayStoreDO();
        displayStoreDO.setId(0L);
        displayStoreDO.setGuid("ded0f5ab-d2f9-478e-8348-d38f33b9516b");
        displayStoreDO.setIsDelete(0);
        displayStoreDO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayStoreDO.setRuleType(0);
        final List<DisplayStoreDO> displayStoreDOS = Arrays.asList(displayStoreDO);
        final DisplayStoreRespDTO displayStoreRespDTO2 = new DisplayStoreRespDTO();
        displayStoreRespDTO2.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO2.setRuleGuid("ruleGuid");
        displayStoreRespDTO2.setStoreGuid("storeGuid");
        displayStoreRespDTO2.setStoreName("storeName");
        displayStoreRespDTO2.setRuleType(0);
        final List<DisplayStoreRespDTO> storeList = Arrays.asList(displayStoreRespDTO2);
        when(mockRuleMapstruct.toStoreDOList(storeList)).thenReturn(displayStoreDOS);

        when(mockDistributedIdService.nextBatchDisplayStoreGuid(0L)).thenReturn(Arrays.asList("value"));

        // Configure DisplayStoreService.listStore(...).
        final DisplayStoreRespDTO displayStoreRespDTO3 = new DisplayStoreRespDTO();
        displayStoreRespDTO3.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO3.setRuleGuid("ruleGuid");
        displayStoreRespDTO3.setStoreGuid("storeGuid");
        displayStoreRespDTO3.setStoreName("storeName");
        displayStoreRespDTO3.setRuleType(0);
        final List<DisplayStoreRespDTO> displayStoreRespDTOS = Arrays.asList(displayStoreRespDTO3);
        final DisplayRuleQueryDTO reqDTO2 = new DisplayRuleQueryDTO();
        reqDTO2.setCurrentPage(0L);
        reqDTO2.setPageSize(0L);
        reqDTO2.setRuleGuid("ruleGuid");
        reqDTO2.setRuleType(0);
        reqDTO2.setBrandGuid("brandGuid");
        when(mockStoreService.listStore(reqDTO2)).thenReturn(displayStoreRespDTOS);

        // Configure DisplayStoreService.saveBatch(...).
        final DisplayStoreDO displayStoreDO1 = new DisplayStoreDO();
        displayStoreDO1.setId(0L);
        displayStoreDO1.setGuid("ded0f5ab-d2f9-478e-8348-d38f33b9516b");
        displayStoreDO1.setIsDelete(0);
        displayStoreDO1.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayStoreDO1.setRuleType(0);
        final List<DisplayStoreDO> entityList1 = Arrays.asList(displayStoreDO1);
        when(mockStoreService.saveBatch(entityList1)).thenReturn(false);

        // Run the test
        final Boolean result = displayRuleServiceImplUnderTest.updateRule(reqDTO);

        // Verify the results
        assertThat(result).isFalse();
        verify(mockItemService).remove(any(LambdaQueryWrapper.class));
        verify(mockStoreService).remove(any(LambdaQueryWrapper.class));
    }

    @Test
    public void testUpdateRule_DisplayRuleMapstructToItemDOListReturnsNoItems() {
        // Setup
        final DisplayRuleSaveOrUpdateDTO reqDTO = new DisplayRuleSaveOrUpdateDTO();
        reqDTO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        reqDTO.setDisplayState(0);
        reqDTO.setDelayTime(0);
        reqDTO.setBatch(0);
        reqDTO.setEffectiveState(0);
        reqDTO.setEffectiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO.setIsAllStore(false);
        reqDTO.setRuleType(0);
        final DisplayStoreRespDTO displayStoreRespDTO = new DisplayStoreRespDTO();
        displayStoreRespDTO.setStoreGuid("storeGuid");
        displayStoreRespDTO.setStoreName("storeName");
        reqDTO.setStoreList(Arrays.asList(displayStoreRespDTO));
        final DisplayItemRespDTO displayItemRespDTO = new DisplayItemRespDTO();
        displayItemRespDTO.setItemGuid("itemGuid");
        displayItemRespDTO.setItemName("itemName");
        reqDTO.setItemList(Arrays.asList(displayItemRespDTO));
        reqDTO.setBrandGuid("brandGuid");

        // Configure DisplayRuleMapstruct.ruleReqToRuleDO(...).
        final DisplayRuleDO displayRuleDO = new DisplayRuleDO();
        displayRuleDO.setGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayRuleDO.setIsDelete(0);
        displayRuleDO.setDelayTime(0);
        displayRuleDO.setBatch(0);
        displayRuleDO.setIsAllStore(false);
        displayRuleDO.setRuleType(0);
        displayRuleDO.setBrandGuid("brandGuid");
        final DisplayRuleSaveOrUpdateDTO ruleReqDTO = new DisplayRuleSaveOrUpdateDTO();
        ruleReqDTO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        ruleReqDTO.setDisplayState(0);
        ruleReqDTO.setDelayTime(0);
        ruleReqDTO.setBatch(0);
        ruleReqDTO.setEffectiveState(0);
        ruleReqDTO.setEffectiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        ruleReqDTO.setIsAllStore(false);
        ruleReqDTO.setRuleType(0);
        final DisplayStoreRespDTO displayStoreRespDTO1 = new DisplayStoreRespDTO();
        displayStoreRespDTO1.setStoreGuid("storeGuid");
        displayStoreRespDTO1.setStoreName("storeName");
        ruleReqDTO.setStoreList(Arrays.asList(displayStoreRespDTO1));
        final DisplayItemRespDTO displayItemRespDTO1 = new DisplayItemRespDTO();
        displayItemRespDTO1.setItemGuid("itemGuid");
        displayItemRespDTO1.setItemName("itemName");
        ruleReqDTO.setItemList(Arrays.asList(displayItemRespDTO1));
        ruleReqDTO.setBrandGuid("brandGuid");
        when(mockRuleMapstruct.ruleReqToRuleDO(ruleReqDTO)).thenReturn(displayRuleDO);

        // Configure DisplayRuleMapstruct.toItemDOList(...).
        final DisplayItemRespDTO displayItemRespDTO2 = new DisplayItemRespDTO();
        displayItemRespDTO2.setItemGuid("itemGuid");
        displayItemRespDTO2.setItemName("itemName");
        displayItemRespDTO2.setSort(0);
        displayItemRespDTO2.setRuleType(0);
        final List<DisplayItemRespDTO> itemList = Arrays.asList(displayItemRespDTO2);
        when(mockRuleMapstruct.toItemDOList(itemList)).thenReturn(Collections.emptyList());

        when(mockDistributedIdService.nextBatchDisplayItemGuid(0L)).thenReturn(Arrays.asList("value"));

        // Configure DisplayItemService.listItem(...).
        final DisplayItemRespDTO displayItemRespDTO3 = new DisplayItemRespDTO();
        displayItemRespDTO3.setItemGuid("itemGuid");
        displayItemRespDTO3.setItemName("itemName");
        displayItemRespDTO3.setSort(0);
        displayItemRespDTO3.setRuleType(0);
        final List<DisplayItemRespDTO> displayItemRespDTOS = Arrays.asList(displayItemRespDTO3);
        final DisplayRuleQueryDTO reqDTO1 = new DisplayRuleQueryDTO();
        reqDTO1.setCurrentPage(0L);
        reqDTO1.setPageSize(0L);
        reqDTO1.setRuleGuid("ruleGuid");
        reqDTO1.setRuleType(0);
        reqDTO1.setBrandGuid("brandGuid");
        when(mockItemService.listItem(reqDTO1)).thenReturn(displayItemRespDTOS);

        // Configure DisplayItemService.saveBatch(...).
        final DisplayItemDO displayItemDO = new DisplayItemDO();
        displayItemDO.setGuid("7e5ba61b-f27d-4746-abc1-480b55cad459");
        displayItemDO.setIsDelete(0);
        displayItemDO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayItemDO.setItemGuid("itemGuid");
        displayItemDO.setSort(0);
        displayItemDO.setRuleType(0);
        final List<DisplayItemDO> entityList = Arrays.asList(displayItemDO);
        when(mockItemService.saveBatch(entityList)).thenReturn(false);

        // Configure DisplayRuleMapstruct.toStoreDOList(...).
        final DisplayStoreDO displayStoreDO = new DisplayStoreDO();
        displayStoreDO.setId(0L);
        displayStoreDO.setGuid("ded0f5ab-d2f9-478e-8348-d38f33b9516b");
        displayStoreDO.setIsDelete(0);
        displayStoreDO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayStoreDO.setRuleType(0);
        final List<DisplayStoreDO> displayStoreDOS = Arrays.asList(displayStoreDO);
        final DisplayStoreRespDTO displayStoreRespDTO2 = new DisplayStoreRespDTO();
        displayStoreRespDTO2.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO2.setRuleGuid("ruleGuid");
        displayStoreRespDTO2.setStoreGuid("storeGuid");
        displayStoreRespDTO2.setStoreName("storeName");
        displayStoreRespDTO2.setRuleType(0);
        final List<DisplayStoreRespDTO> storeList = Arrays.asList(displayStoreRespDTO2);
        when(mockRuleMapstruct.toStoreDOList(storeList)).thenReturn(displayStoreDOS);

        when(mockDistributedIdService.nextBatchDisplayStoreGuid(0L)).thenReturn(Arrays.asList("value"));

        // Configure DisplayStoreService.listStore(...).
        final DisplayStoreRespDTO displayStoreRespDTO3 = new DisplayStoreRespDTO();
        displayStoreRespDTO3.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO3.setRuleGuid("ruleGuid");
        displayStoreRespDTO3.setStoreGuid("storeGuid");
        displayStoreRespDTO3.setStoreName("storeName");
        displayStoreRespDTO3.setRuleType(0);
        final List<DisplayStoreRespDTO> displayStoreRespDTOS = Arrays.asList(displayStoreRespDTO3);
        final DisplayRuleQueryDTO reqDTO2 = new DisplayRuleQueryDTO();
        reqDTO2.setCurrentPage(0L);
        reqDTO2.setPageSize(0L);
        reqDTO2.setRuleGuid("ruleGuid");
        reqDTO2.setRuleType(0);
        reqDTO2.setBrandGuid("brandGuid");
        when(mockStoreService.listStore(reqDTO2)).thenReturn(displayStoreRespDTOS);

        // Configure DisplayStoreService.saveBatch(...).
        final DisplayStoreDO displayStoreDO1 = new DisplayStoreDO();
        displayStoreDO1.setId(0L);
        displayStoreDO1.setGuid("ded0f5ab-d2f9-478e-8348-d38f33b9516b");
        displayStoreDO1.setIsDelete(0);
        displayStoreDO1.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayStoreDO1.setRuleType(0);
        final List<DisplayStoreDO> entityList1 = Arrays.asList(displayStoreDO1);
        when(mockStoreService.saveBatch(entityList1)).thenReturn(false);

        // Run the test
        final Boolean result = displayRuleServiceImplUnderTest.updateRule(reqDTO);

        // Verify the results
        assertThat(result).isFalse();
        verify(mockItemService).remove(any(LambdaQueryWrapper.class));
        verify(mockStoreService).remove(any(LambdaQueryWrapper.class));
    }

    @Test
    public void testUpdateRule_DistributedIdServiceNextBatchDisplayItemGuidReturnsNoItems() {
        // Setup
        final DisplayRuleSaveOrUpdateDTO reqDTO = new DisplayRuleSaveOrUpdateDTO();
        reqDTO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        reqDTO.setDisplayState(0);
        reqDTO.setDelayTime(0);
        reqDTO.setBatch(0);
        reqDTO.setEffectiveState(0);
        reqDTO.setEffectiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO.setIsAllStore(false);
        reqDTO.setRuleType(0);
        final DisplayStoreRespDTO displayStoreRespDTO = new DisplayStoreRespDTO();
        displayStoreRespDTO.setStoreGuid("storeGuid");
        displayStoreRespDTO.setStoreName("storeName");
        reqDTO.setStoreList(Arrays.asList(displayStoreRespDTO));
        final DisplayItemRespDTO displayItemRespDTO = new DisplayItemRespDTO();
        displayItemRespDTO.setItemGuid("itemGuid");
        displayItemRespDTO.setItemName("itemName");
        reqDTO.setItemList(Arrays.asList(displayItemRespDTO));
        reqDTO.setBrandGuid("brandGuid");

        // Configure DisplayRuleMapstruct.ruleReqToRuleDO(...).
        final DisplayRuleDO displayRuleDO = new DisplayRuleDO();
        displayRuleDO.setGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayRuleDO.setIsDelete(0);
        displayRuleDO.setDelayTime(0);
        displayRuleDO.setBatch(0);
        displayRuleDO.setIsAllStore(false);
        displayRuleDO.setRuleType(0);
        displayRuleDO.setBrandGuid("brandGuid");
        final DisplayRuleSaveOrUpdateDTO ruleReqDTO = new DisplayRuleSaveOrUpdateDTO();
        ruleReqDTO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        ruleReqDTO.setDisplayState(0);
        ruleReqDTO.setDelayTime(0);
        ruleReqDTO.setBatch(0);
        ruleReqDTO.setEffectiveState(0);
        ruleReqDTO.setEffectiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        ruleReqDTO.setIsAllStore(false);
        ruleReqDTO.setRuleType(0);
        final DisplayStoreRespDTO displayStoreRespDTO1 = new DisplayStoreRespDTO();
        displayStoreRespDTO1.setStoreGuid("storeGuid");
        displayStoreRespDTO1.setStoreName("storeName");
        ruleReqDTO.setStoreList(Arrays.asList(displayStoreRespDTO1));
        final DisplayItemRespDTO displayItemRespDTO1 = new DisplayItemRespDTO();
        displayItemRespDTO1.setItemGuid("itemGuid");
        displayItemRespDTO1.setItemName("itemName");
        ruleReqDTO.setItemList(Arrays.asList(displayItemRespDTO1));
        ruleReqDTO.setBrandGuid("brandGuid");
        when(mockRuleMapstruct.ruleReqToRuleDO(ruleReqDTO)).thenReturn(displayRuleDO);

        // Configure DisplayRuleMapstruct.toItemDOList(...).
        final DisplayItemDO displayItemDO = new DisplayItemDO();
        displayItemDO.setGuid("7e5ba61b-f27d-4746-abc1-480b55cad459");
        displayItemDO.setIsDelete(0);
        displayItemDO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayItemDO.setItemGuid("itemGuid");
        displayItemDO.setSort(0);
        displayItemDO.setRuleType(0);
        final List<DisplayItemDO> displayItemDOS = Arrays.asList(displayItemDO);
        final DisplayItemRespDTO displayItemRespDTO2 = new DisplayItemRespDTO();
        displayItemRespDTO2.setItemGuid("itemGuid");
        displayItemRespDTO2.setItemName("itemName");
        displayItemRespDTO2.setSort(0);
        displayItemRespDTO2.setRuleType(0);
        final List<DisplayItemRespDTO> itemList = Arrays.asList(displayItemRespDTO2);
        when(mockRuleMapstruct.toItemDOList(itemList)).thenReturn(displayItemDOS);

        when(mockDistributedIdService.nextBatchDisplayItemGuid(0L)).thenReturn(Collections.emptyList());

        // Configure DisplayItemService.listItem(...).
        final DisplayItemRespDTO displayItemRespDTO3 = new DisplayItemRespDTO();
        displayItemRespDTO3.setItemGuid("itemGuid");
        displayItemRespDTO3.setItemName("itemName");
        displayItemRespDTO3.setSort(0);
        displayItemRespDTO3.setRuleType(0);
        final List<DisplayItemRespDTO> displayItemRespDTOS = Arrays.asList(displayItemRespDTO3);
        final DisplayRuleQueryDTO reqDTO1 = new DisplayRuleQueryDTO();
        reqDTO1.setCurrentPage(0L);
        reqDTO1.setPageSize(0L);
        reqDTO1.setRuleGuid("ruleGuid");
        reqDTO1.setRuleType(0);
        reqDTO1.setBrandGuid("brandGuid");
        when(mockItemService.listItem(reqDTO1)).thenReturn(displayItemRespDTOS);

        // Configure DisplayItemService.saveBatch(...).
        final DisplayItemDO displayItemDO1 = new DisplayItemDO();
        displayItemDO1.setGuid("7e5ba61b-f27d-4746-abc1-480b55cad459");
        displayItemDO1.setIsDelete(0);
        displayItemDO1.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayItemDO1.setItemGuid("itemGuid");
        displayItemDO1.setSort(0);
        displayItemDO1.setRuleType(0);
        final List<DisplayItemDO> entityList = Arrays.asList(displayItemDO1);
        when(mockItemService.saveBatch(entityList)).thenReturn(false);

        // Configure DisplayRuleMapstruct.toStoreDOList(...).
        final DisplayStoreDO displayStoreDO = new DisplayStoreDO();
        displayStoreDO.setId(0L);
        displayStoreDO.setGuid("ded0f5ab-d2f9-478e-8348-d38f33b9516b");
        displayStoreDO.setIsDelete(0);
        displayStoreDO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayStoreDO.setRuleType(0);
        final List<DisplayStoreDO> displayStoreDOS = Arrays.asList(displayStoreDO);
        final DisplayStoreRespDTO displayStoreRespDTO2 = new DisplayStoreRespDTO();
        displayStoreRespDTO2.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO2.setRuleGuid("ruleGuid");
        displayStoreRespDTO2.setStoreGuid("storeGuid");
        displayStoreRespDTO2.setStoreName("storeName");
        displayStoreRespDTO2.setRuleType(0);
        final List<DisplayStoreRespDTO> storeList = Arrays.asList(displayStoreRespDTO2);
        when(mockRuleMapstruct.toStoreDOList(storeList)).thenReturn(displayStoreDOS);

        when(mockDistributedIdService.nextBatchDisplayStoreGuid(0L)).thenReturn(Arrays.asList("value"));

        // Configure DisplayStoreService.listStore(...).
        final DisplayStoreRespDTO displayStoreRespDTO3 = new DisplayStoreRespDTO();
        displayStoreRespDTO3.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO3.setRuleGuid("ruleGuid");
        displayStoreRespDTO3.setStoreGuid("storeGuid");
        displayStoreRespDTO3.setStoreName("storeName");
        displayStoreRespDTO3.setRuleType(0);
        final List<DisplayStoreRespDTO> displayStoreRespDTOS = Arrays.asList(displayStoreRespDTO3);
        final DisplayRuleQueryDTO reqDTO2 = new DisplayRuleQueryDTO();
        reqDTO2.setCurrentPage(0L);
        reqDTO2.setPageSize(0L);
        reqDTO2.setRuleGuid("ruleGuid");
        reqDTO2.setRuleType(0);
        reqDTO2.setBrandGuid("brandGuid");
        when(mockStoreService.listStore(reqDTO2)).thenReturn(displayStoreRespDTOS);

        // Configure DisplayStoreService.saveBatch(...).
        final DisplayStoreDO displayStoreDO1 = new DisplayStoreDO();
        displayStoreDO1.setId(0L);
        displayStoreDO1.setGuid("ded0f5ab-d2f9-478e-8348-d38f33b9516b");
        displayStoreDO1.setIsDelete(0);
        displayStoreDO1.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayStoreDO1.setRuleType(0);
        final List<DisplayStoreDO> entityList1 = Arrays.asList(displayStoreDO1);
        when(mockStoreService.saveBatch(entityList1)).thenReturn(false);

        // Run the test
        final Boolean result = displayRuleServiceImplUnderTest.updateRule(reqDTO);

        // Verify the results
        assertThat(result).isFalse();
        verify(mockItemService).remove(any(LambdaQueryWrapper.class));
        verify(mockStoreService).remove(any(LambdaQueryWrapper.class));
    }

    @Test
    public void testUpdateRule_DisplayItemServiceListItemReturnsNoItems() {
        // Setup
        final DisplayRuleSaveOrUpdateDTO reqDTO = new DisplayRuleSaveOrUpdateDTO();
        reqDTO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        reqDTO.setDisplayState(0);
        reqDTO.setDelayTime(0);
        reqDTO.setBatch(0);
        reqDTO.setEffectiveState(0);
        reqDTO.setEffectiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO.setIsAllStore(false);
        reqDTO.setRuleType(0);
        final DisplayStoreRespDTO displayStoreRespDTO = new DisplayStoreRespDTO();
        displayStoreRespDTO.setStoreGuid("storeGuid");
        displayStoreRespDTO.setStoreName("storeName");
        reqDTO.setStoreList(Arrays.asList(displayStoreRespDTO));
        final DisplayItemRespDTO displayItemRespDTO = new DisplayItemRespDTO();
        displayItemRespDTO.setItemGuid("itemGuid");
        displayItemRespDTO.setItemName("itemName");
        reqDTO.setItemList(Arrays.asList(displayItemRespDTO));
        reqDTO.setBrandGuid("brandGuid");

        // Configure DisplayRuleMapstruct.ruleReqToRuleDO(...).
        final DisplayRuleDO displayRuleDO = new DisplayRuleDO();
        displayRuleDO.setGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayRuleDO.setIsDelete(0);
        displayRuleDO.setDelayTime(0);
        displayRuleDO.setBatch(0);
        displayRuleDO.setIsAllStore(false);
        displayRuleDO.setRuleType(0);
        displayRuleDO.setBrandGuid("brandGuid");
        final DisplayRuleSaveOrUpdateDTO ruleReqDTO = new DisplayRuleSaveOrUpdateDTO();
        ruleReqDTO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        ruleReqDTO.setDisplayState(0);
        ruleReqDTO.setDelayTime(0);
        ruleReqDTO.setBatch(0);
        ruleReqDTO.setEffectiveState(0);
        ruleReqDTO.setEffectiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        ruleReqDTO.setIsAllStore(false);
        ruleReqDTO.setRuleType(0);
        final DisplayStoreRespDTO displayStoreRespDTO1 = new DisplayStoreRespDTO();
        displayStoreRespDTO1.setStoreGuid("storeGuid");
        displayStoreRespDTO1.setStoreName("storeName");
        ruleReqDTO.setStoreList(Arrays.asList(displayStoreRespDTO1));
        final DisplayItemRespDTO displayItemRespDTO1 = new DisplayItemRespDTO();
        displayItemRespDTO1.setItemGuid("itemGuid");
        displayItemRespDTO1.setItemName("itemName");
        ruleReqDTO.setItemList(Arrays.asList(displayItemRespDTO1));
        ruleReqDTO.setBrandGuid("brandGuid");
        when(mockRuleMapstruct.ruleReqToRuleDO(ruleReqDTO)).thenReturn(displayRuleDO);

        // Configure DisplayRuleMapstruct.toItemDOList(...).
        final DisplayItemDO displayItemDO = new DisplayItemDO();
        displayItemDO.setGuid("7e5ba61b-f27d-4746-abc1-480b55cad459");
        displayItemDO.setIsDelete(0);
        displayItemDO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayItemDO.setItemGuid("itemGuid");
        displayItemDO.setSort(0);
        displayItemDO.setRuleType(0);
        final List<DisplayItemDO> displayItemDOS = Arrays.asList(displayItemDO);
        final DisplayItemRespDTO displayItemRespDTO2 = new DisplayItemRespDTO();
        displayItemRespDTO2.setItemGuid("itemGuid");
        displayItemRespDTO2.setItemName("itemName");
        displayItemRespDTO2.setSort(0);
        displayItemRespDTO2.setRuleType(0);
        final List<DisplayItemRespDTO> itemList = Arrays.asList(displayItemRespDTO2);
        when(mockRuleMapstruct.toItemDOList(itemList)).thenReturn(displayItemDOS);

        when(mockDistributedIdService.nextBatchDisplayItemGuid(0L)).thenReturn(Arrays.asList("value"));

        // Configure DisplayItemService.listItem(...).
        final DisplayRuleQueryDTO reqDTO1 = new DisplayRuleQueryDTO();
        reqDTO1.setCurrentPage(0L);
        reqDTO1.setPageSize(0L);
        reqDTO1.setRuleGuid("ruleGuid");
        reqDTO1.setRuleType(0);
        reqDTO1.setBrandGuid("brandGuid");
        when(mockItemService.listItem(reqDTO1)).thenReturn(Collections.emptyList());

        // Configure DisplayItemService.saveBatch(...).
        final DisplayItemDO displayItemDO1 = new DisplayItemDO();
        displayItemDO1.setGuid("7e5ba61b-f27d-4746-abc1-480b55cad459");
        displayItemDO1.setIsDelete(0);
        displayItemDO1.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayItemDO1.setItemGuid("itemGuid");
        displayItemDO1.setSort(0);
        displayItemDO1.setRuleType(0);
        final List<DisplayItemDO> entityList = Arrays.asList(displayItemDO1);
        when(mockItemService.saveBatch(entityList)).thenReturn(false);

        // Configure DisplayRuleMapstruct.toStoreDOList(...).
        final DisplayStoreDO displayStoreDO = new DisplayStoreDO();
        displayStoreDO.setId(0L);
        displayStoreDO.setGuid("ded0f5ab-d2f9-478e-8348-d38f33b9516b");
        displayStoreDO.setIsDelete(0);
        displayStoreDO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayStoreDO.setRuleType(0);
        final List<DisplayStoreDO> displayStoreDOS = Arrays.asList(displayStoreDO);
        final DisplayStoreRespDTO displayStoreRespDTO2 = new DisplayStoreRespDTO();
        displayStoreRespDTO2.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO2.setRuleGuid("ruleGuid");
        displayStoreRespDTO2.setStoreGuid("storeGuid");
        displayStoreRespDTO2.setStoreName("storeName");
        displayStoreRespDTO2.setRuleType(0);
        final List<DisplayStoreRespDTO> storeList = Arrays.asList(displayStoreRespDTO2);
        when(mockRuleMapstruct.toStoreDOList(storeList)).thenReturn(displayStoreDOS);

        when(mockDistributedIdService.nextBatchDisplayStoreGuid(0L)).thenReturn(Arrays.asList("value"));

        // Configure DisplayStoreService.listStore(...).
        final DisplayStoreRespDTO displayStoreRespDTO3 = new DisplayStoreRespDTO();
        displayStoreRespDTO3.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO3.setRuleGuid("ruleGuid");
        displayStoreRespDTO3.setStoreGuid("storeGuid");
        displayStoreRespDTO3.setStoreName("storeName");
        displayStoreRespDTO3.setRuleType(0);
        final List<DisplayStoreRespDTO> displayStoreRespDTOS = Arrays.asList(displayStoreRespDTO3);
        final DisplayRuleQueryDTO reqDTO2 = new DisplayRuleQueryDTO();
        reqDTO2.setCurrentPage(0L);
        reqDTO2.setPageSize(0L);
        reqDTO2.setRuleGuid("ruleGuid");
        reqDTO2.setRuleType(0);
        reqDTO2.setBrandGuid("brandGuid");
        when(mockStoreService.listStore(reqDTO2)).thenReturn(displayStoreRespDTOS);

        // Configure DisplayStoreService.saveBatch(...).
        final DisplayStoreDO displayStoreDO1 = new DisplayStoreDO();
        displayStoreDO1.setId(0L);
        displayStoreDO1.setGuid("ded0f5ab-d2f9-478e-8348-d38f33b9516b");
        displayStoreDO1.setIsDelete(0);
        displayStoreDO1.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayStoreDO1.setRuleType(0);
        final List<DisplayStoreDO> entityList1 = Arrays.asList(displayStoreDO1);
        when(mockStoreService.saveBatch(entityList1)).thenReturn(false);

        // Run the test
        final Boolean result = displayRuleServiceImplUnderTest.updateRule(reqDTO);

        // Verify the results
        assertThat(result).isFalse();
        verify(mockItemService).remove(any(LambdaQueryWrapper.class));
        verify(mockStoreService).remove(any(LambdaQueryWrapper.class));
    }

    @Test
    public void testUpdateRule_DisplayItemServiceSaveBatchReturnsTrue() {
        // Setup
        final DisplayRuleSaveOrUpdateDTO reqDTO = new DisplayRuleSaveOrUpdateDTO();
        reqDTO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        reqDTO.setDisplayState(0);
        reqDTO.setDelayTime(0);
        reqDTO.setBatch(0);
        reqDTO.setEffectiveState(0);
        reqDTO.setEffectiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO.setIsAllStore(false);
        reqDTO.setRuleType(0);
        final DisplayStoreRespDTO displayStoreRespDTO = new DisplayStoreRespDTO();
        displayStoreRespDTO.setStoreGuid("storeGuid");
        displayStoreRespDTO.setStoreName("storeName");
        reqDTO.setStoreList(Arrays.asList(displayStoreRespDTO));
        final DisplayItemRespDTO displayItemRespDTO = new DisplayItemRespDTO();
        displayItemRespDTO.setItemGuid("itemGuid");
        displayItemRespDTO.setItemName("itemName");
        reqDTO.setItemList(Arrays.asList(displayItemRespDTO));
        reqDTO.setBrandGuid("brandGuid");

        // Configure DisplayRuleMapstruct.ruleReqToRuleDO(...).
        final DisplayRuleDO displayRuleDO = new DisplayRuleDO();
        displayRuleDO.setGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayRuleDO.setIsDelete(0);
        displayRuleDO.setDelayTime(0);
        displayRuleDO.setBatch(0);
        displayRuleDO.setIsAllStore(false);
        displayRuleDO.setRuleType(0);
        displayRuleDO.setBrandGuid("brandGuid");
        final DisplayRuleSaveOrUpdateDTO ruleReqDTO = new DisplayRuleSaveOrUpdateDTO();
        ruleReqDTO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        ruleReqDTO.setDisplayState(0);
        ruleReqDTO.setDelayTime(0);
        ruleReqDTO.setBatch(0);
        ruleReqDTO.setEffectiveState(0);
        ruleReqDTO.setEffectiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        ruleReqDTO.setIsAllStore(false);
        ruleReqDTO.setRuleType(0);
        final DisplayStoreRespDTO displayStoreRespDTO1 = new DisplayStoreRespDTO();
        displayStoreRespDTO1.setStoreGuid("storeGuid");
        displayStoreRespDTO1.setStoreName("storeName");
        ruleReqDTO.setStoreList(Arrays.asList(displayStoreRespDTO1));
        final DisplayItemRespDTO displayItemRespDTO1 = new DisplayItemRespDTO();
        displayItemRespDTO1.setItemGuid("itemGuid");
        displayItemRespDTO1.setItemName("itemName");
        ruleReqDTO.setItemList(Arrays.asList(displayItemRespDTO1));
        ruleReqDTO.setBrandGuid("brandGuid");
        when(mockRuleMapstruct.ruleReqToRuleDO(ruleReqDTO)).thenReturn(displayRuleDO);

        // Configure DisplayRuleMapstruct.toItemDOList(...).
        final DisplayItemDO displayItemDO = new DisplayItemDO();
        displayItemDO.setGuid("7e5ba61b-f27d-4746-abc1-480b55cad459");
        displayItemDO.setIsDelete(0);
        displayItemDO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayItemDO.setItemGuid("itemGuid");
        displayItemDO.setSort(0);
        displayItemDO.setRuleType(0);
        final List<DisplayItemDO> displayItemDOS = Arrays.asList(displayItemDO);
        final DisplayItemRespDTO displayItemRespDTO2 = new DisplayItemRespDTO();
        displayItemRespDTO2.setItemGuid("itemGuid");
        displayItemRespDTO2.setItemName("itemName");
        displayItemRespDTO2.setSort(0);
        displayItemRespDTO2.setRuleType(0);
        final List<DisplayItemRespDTO> itemList = Arrays.asList(displayItemRespDTO2);
        when(mockRuleMapstruct.toItemDOList(itemList)).thenReturn(displayItemDOS);

        when(mockDistributedIdService.nextBatchDisplayItemGuid(0L)).thenReturn(Arrays.asList("value"));

        // Configure DisplayItemService.listItem(...).
        final DisplayItemRespDTO displayItemRespDTO3 = new DisplayItemRespDTO();
        displayItemRespDTO3.setItemGuid("itemGuid");
        displayItemRespDTO3.setItemName("itemName");
        displayItemRespDTO3.setSort(0);
        displayItemRespDTO3.setRuleType(0);
        final List<DisplayItemRespDTO> displayItemRespDTOS = Arrays.asList(displayItemRespDTO3);
        final DisplayRuleQueryDTO reqDTO1 = new DisplayRuleQueryDTO();
        reqDTO1.setCurrentPage(0L);
        reqDTO1.setPageSize(0L);
        reqDTO1.setRuleGuid("ruleGuid");
        reqDTO1.setRuleType(0);
        reqDTO1.setBrandGuid("brandGuid");
        when(mockItemService.listItem(reqDTO1)).thenReturn(displayItemRespDTOS);

        // Configure DisplayItemService.saveBatch(...).
        final DisplayItemDO displayItemDO1 = new DisplayItemDO();
        displayItemDO1.setGuid("7e5ba61b-f27d-4746-abc1-480b55cad459");
        displayItemDO1.setIsDelete(0);
        displayItemDO1.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayItemDO1.setItemGuid("itemGuid");
        displayItemDO1.setSort(0);
        displayItemDO1.setRuleType(0);
        final List<DisplayItemDO> entityList = Arrays.asList(displayItemDO1);
        when(mockItemService.saveBatch(entityList)).thenReturn(true);

        // Configure DisplayRuleMapstruct.toStoreDOList(...).
        final DisplayStoreDO displayStoreDO = new DisplayStoreDO();
        displayStoreDO.setId(0L);
        displayStoreDO.setGuid("ded0f5ab-d2f9-478e-8348-d38f33b9516b");
        displayStoreDO.setIsDelete(0);
        displayStoreDO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayStoreDO.setRuleType(0);
        final List<DisplayStoreDO> displayStoreDOS = Arrays.asList(displayStoreDO);
        final DisplayStoreRespDTO displayStoreRespDTO2 = new DisplayStoreRespDTO();
        displayStoreRespDTO2.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO2.setRuleGuid("ruleGuid");
        displayStoreRespDTO2.setStoreGuid("storeGuid");
        displayStoreRespDTO2.setStoreName("storeName");
        displayStoreRespDTO2.setRuleType(0);
        final List<DisplayStoreRespDTO> storeList = Arrays.asList(displayStoreRespDTO2);
        when(mockRuleMapstruct.toStoreDOList(storeList)).thenReturn(displayStoreDOS);

        when(mockDistributedIdService.nextBatchDisplayStoreGuid(0L)).thenReturn(Arrays.asList("value"));

        // Configure DisplayStoreService.listStore(...).
        final DisplayStoreRespDTO displayStoreRespDTO3 = new DisplayStoreRespDTO();
        displayStoreRespDTO3.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO3.setRuleGuid("ruleGuid");
        displayStoreRespDTO3.setStoreGuid("storeGuid");
        displayStoreRespDTO3.setStoreName("storeName");
        displayStoreRespDTO3.setRuleType(0);
        final List<DisplayStoreRespDTO> displayStoreRespDTOS = Arrays.asList(displayStoreRespDTO3);
        final DisplayRuleQueryDTO reqDTO2 = new DisplayRuleQueryDTO();
        reqDTO2.setCurrentPage(0L);
        reqDTO2.setPageSize(0L);
        reqDTO2.setRuleGuid("ruleGuid");
        reqDTO2.setRuleType(0);
        reqDTO2.setBrandGuid("brandGuid");
        when(mockStoreService.listStore(reqDTO2)).thenReturn(displayStoreRespDTOS);

        // Configure DisplayStoreService.saveBatch(...).
        final DisplayStoreDO displayStoreDO1 = new DisplayStoreDO();
        displayStoreDO1.setId(0L);
        displayStoreDO1.setGuid("ded0f5ab-d2f9-478e-8348-d38f33b9516b");
        displayStoreDO1.setIsDelete(0);
        displayStoreDO1.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayStoreDO1.setRuleType(0);
        final List<DisplayStoreDO> entityList1 = Arrays.asList(displayStoreDO1);
        when(mockStoreService.saveBatch(entityList1)).thenReturn(false);

        // Run the test
        final Boolean result = displayRuleServiceImplUnderTest.updateRule(reqDTO);

        // Verify the results
        assertThat(result).isTrue();
        verify(mockItemService).remove(any(LambdaQueryWrapper.class));
        verify(mockStoreService).remove(any(LambdaQueryWrapper.class));
    }

    @Test
    public void testUpdateRule_DisplayRuleMapstructToStoreDOListReturnsNoItems() {
        // Setup
        final DisplayRuleSaveOrUpdateDTO reqDTO = new DisplayRuleSaveOrUpdateDTO();
        reqDTO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        reqDTO.setDisplayState(0);
        reqDTO.setDelayTime(0);
        reqDTO.setBatch(0);
        reqDTO.setEffectiveState(0);
        reqDTO.setEffectiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO.setIsAllStore(false);
        reqDTO.setRuleType(0);
        final DisplayStoreRespDTO displayStoreRespDTO = new DisplayStoreRespDTO();
        displayStoreRespDTO.setStoreGuid("storeGuid");
        displayStoreRespDTO.setStoreName("storeName");
        reqDTO.setStoreList(Arrays.asList(displayStoreRespDTO));
        final DisplayItemRespDTO displayItemRespDTO = new DisplayItemRespDTO();
        displayItemRespDTO.setItemGuid("itemGuid");
        displayItemRespDTO.setItemName("itemName");
        reqDTO.setItemList(Arrays.asList(displayItemRespDTO));
        reqDTO.setBrandGuid("brandGuid");

        // Configure DisplayRuleMapstruct.ruleReqToRuleDO(...).
        final DisplayRuleDO displayRuleDO = new DisplayRuleDO();
        displayRuleDO.setGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayRuleDO.setIsDelete(0);
        displayRuleDO.setDelayTime(0);
        displayRuleDO.setBatch(0);
        displayRuleDO.setIsAllStore(false);
        displayRuleDO.setRuleType(0);
        displayRuleDO.setBrandGuid("brandGuid");
        final DisplayRuleSaveOrUpdateDTO ruleReqDTO = new DisplayRuleSaveOrUpdateDTO();
        ruleReqDTO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        ruleReqDTO.setDisplayState(0);
        ruleReqDTO.setDelayTime(0);
        ruleReqDTO.setBatch(0);
        ruleReqDTO.setEffectiveState(0);
        ruleReqDTO.setEffectiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        ruleReqDTO.setIsAllStore(false);
        ruleReqDTO.setRuleType(0);
        final DisplayStoreRespDTO displayStoreRespDTO1 = new DisplayStoreRespDTO();
        displayStoreRespDTO1.setStoreGuid("storeGuid");
        displayStoreRespDTO1.setStoreName("storeName");
        ruleReqDTO.setStoreList(Arrays.asList(displayStoreRespDTO1));
        final DisplayItemRespDTO displayItemRespDTO1 = new DisplayItemRespDTO();
        displayItemRespDTO1.setItemGuid("itemGuid");
        displayItemRespDTO1.setItemName("itemName");
        ruleReqDTO.setItemList(Arrays.asList(displayItemRespDTO1));
        ruleReqDTO.setBrandGuid("brandGuid");
        when(mockRuleMapstruct.ruleReqToRuleDO(ruleReqDTO)).thenReturn(displayRuleDO);

        // Configure DisplayRuleMapstruct.toItemDOList(...).
        final DisplayItemDO displayItemDO = new DisplayItemDO();
        displayItemDO.setGuid("7e5ba61b-f27d-4746-abc1-480b55cad459");
        displayItemDO.setIsDelete(0);
        displayItemDO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayItemDO.setItemGuid("itemGuid");
        displayItemDO.setSort(0);
        displayItemDO.setRuleType(0);
        final List<DisplayItemDO> displayItemDOS = Arrays.asList(displayItemDO);
        final DisplayItemRespDTO displayItemRespDTO2 = new DisplayItemRespDTO();
        displayItemRespDTO2.setItemGuid("itemGuid");
        displayItemRespDTO2.setItemName("itemName");
        displayItemRespDTO2.setSort(0);
        displayItemRespDTO2.setRuleType(0);
        final List<DisplayItemRespDTO> itemList = Arrays.asList(displayItemRespDTO2);
        when(mockRuleMapstruct.toItemDOList(itemList)).thenReturn(displayItemDOS);

        when(mockDistributedIdService.nextBatchDisplayItemGuid(0L)).thenReturn(Arrays.asList("value"));

        // Configure DisplayItemService.listItem(...).
        final DisplayItemRespDTO displayItemRespDTO3 = new DisplayItemRespDTO();
        displayItemRespDTO3.setItemGuid("itemGuid");
        displayItemRespDTO3.setItemName("itemName");
        displayItemRespDTO3.setSort(0);
        displayItemRespDTO3.setRuleType(0);
        final List<DisplayItemRespDTO> displayItemRespDTOS = Arrays.asList(displayItemRespDTO3);
        final DisplayRuleQueryDTO reqDTO1 = new DisplayRuleQueryDTO();
        reqDTO1.setCurrentPage(0L);
        reqDTO1.setPageSize(0L);
        reqDTO1.setRuleGuid("ruleGuid");
        reqDTO1.setRuleType(0);
        reqDTO1.setBrandGuid("brandGuid");
        when(mockItemService.listItem(reqDTO1)).thenReturn(displayItemRespDTOS);

        // Configure DisplayItemService.saveBatch(...).
        final DisplayItemDO displayItemDO1 = new DisplayItemDO();
        displayItemDO1.setGuid("7e5ba61b-f27d-4746-abc1-480b55cad459");
        displayItemDO1.setIsDelete(0);
        displayItemDO1.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayItemDO1.setItemGuid("itemGuid");
        displayItemDO1.setSort(0);
        displayItemDO1.setRuleType(0);
        final List<DisplayItemDO> entityList = Arrays.asList(displayItemDO1);
        when(mockItemService.saveBatch(entityList)).thenReturn(false);

        // Configure DisplayRuleMapstruct.toStoreDOList(...).
        final DisplayStoreRespDTO displayStoreRespDTO2 = new DisplayStoreRespDTO();
        displayStoreRespDTO2.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO2.setRuleGuid("ruleGuid");
        displayStoreRespDTO2.setStoreGuid("storeGuid");
        displayStoreRespDTO2.setStoreName("storeName");
        displayStoreRespDTO2.setRuleType(0);
        final List<DisplayStoreRespDTO> storeList = Arrays.asList(displayStoreRespDTO2);
        when(mockRuleMapstruct.toStoreDOList(storeList)).thenReturn(Collections.emptyList());

        when(mockDistributedIdService.nextBatchDisplayStoreGuid(0L)).thenReturn(Arrays.asList("value"));

        // Configure DisplayStoreService.listStore(...).
        final DisplayStoreRespDTO displayStoreRespDTO3 = new DisplayStoreRespDTO();
        displayStoreRespDTO3.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO3.setRuleGuid("ruleGuid");
        displayStoreRespDTO3.setStoreGuid("storeGuid");
        displayStoreRespDTO3.setStoreName("storeName");
        displayStoreRespDTO3.setRuleType(0);
        final List<DisplayStoreRespDTO> displayStoreRespDTOS = Arrays.asList(displayStoreRespDTO3);
        final DisplayRuleQueryDTO reqDTO2 = new DisplayRuleQueryDTO();
        reqDTO2.setCurrentPage(0L);
        reqDTO2.setPageSize(0L);
        reqDTO2.setRuleGuid("ruleGuid");
        reqDTO2.setRuleType(0);
        reqDTO2.setBrandGuid("brandGuid");
        when(mockStoreService.listStore(reqDTO2)).thenReturn(displayStoreRespDTOS);

        // Configure DisplayStoreService.saveBatch(...).
        final DisplayStoreDO displayStoreDO = new DisplayStoreDO();
        displayStoreDO.setId(0L);
        displayStoreDO.setGuid("ded0f5ab-d2f9-478e-8348-d38f33b9516b");
        displayStoreDO.setIsDelete(0);
        displayStoreDO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayStoreDO.setRuleType(0);
        final List<DisplayStoreDO> entityList1 = Arrays.asList(displayStoreDO);
        when(mockStoreService.saveBatch(entityList1)).thenReturn(false);

        // Run the test
        final Boolean result = displayRuleServiceImplUnderTest.updateRule(reqDTO);

        // Verify the results
        assertThat(result).isFalse();
        verify(mockItemService).remove(any(LambdaQueryWrapper.class));
        verify(mockStoreService).remove(any(LambdaQueryWrapper.class));
    }

    @Test
    public void testUpdateRule_DistributedIdServiceNextBatchDisplayStoreGuidReturnsNoItems() {
        // Setup
        final DisplayRuleSaveOrUpdateDTO reqDTO = new DisplayRuleSaveOrUpdateDTO();
        reqDTO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        reqDTO.setDisplayState(0);
        reqDTO.setDelayTime(0);
        reqDTO.setBatch(0);
        reqDTO.setEffectiveState(0);
        reqDTO.setEffectiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO.setIsAllStore(false);
        reqDTO.setRuleType(0);
        final DisplayStoreRespDTO displayStoreRespDTO = new DisplayStoreRespDTO();
        displayStoreRespDTO.setStoreGuid("storeGuid");
        displayStoreRespDTO.setStoreName("storeName");
        reqDTO.setStoreList(Arrays.asList(displayStoreRespDTO));
        final DisplayItemRespDTO displayItemRespDTO = new DisplayItemRespDTO();
        displayItemRespDTO.setItemGuid("itemGuid");
        displayItemRespDTO.setItemName("itemName");
        reqDTO.setItemList(Arrays.asList(displayItemRespDTO));
        reqDTO.setBrandGuid("brandGuid");

        // Configure DisplayRuleMapstruct.ruleReqToRuleDO(...).
        final DisplayRuleDO displayRuleDO = new DisplayRuleDO();
        displayRuleDO.setGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayRuleDO.setIsDelete(0);
        displayRuleDO.setDelayTime(0);
        displayRuleDO.setBatch(0);
        displayRuleDO.setIsAllStore(false);
        displayRuleDO.setRuleType(0);
        displayRuleDO.setBrandGuid("brandGuid");
        final DisplayRuleSaveOrUpdateDTO ruleReqDTO = new DisplayRuleSaveOrUpdateDTO();
        ruleReqDTO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        ruleReqDTO.setDisplayState(0);
        ruleReqDTO.setDelayTime(0);
        ruleReqDTO.setBatch(0);
        ruleReqDTO.setEffectiveState(0);
        ruleReqDTO.setEffectiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        ruleReqDTO.setIsAllStore(false);
        ruleReqDTO.setRuleType(0);
        final DisplayStoreRespDTO displayStoreRespDTO1 = new DisplayStoreRespDTO();
        displayStoreRespDTO1.setStoreGuid("storeGuid");
        displayStoreRespDTO1.setStoreName("storeName");
        ruleReqDTO.setStoreList(Arrays.asList(displayStoreRespDTO1));
        final DisplayItemRespDTO displayItemRespDTO1 = new DisplayItemRespDTO();
        displayItemRespDTO1.setItemGuid("itemGuid");
        displayItemRespDTO1.setItemName("itemName");
        ruleReqDTO.setItemList(Arrays.asList(displayItemRespDTO1));
        ruleReqDTO.setBrandGuid("brandGuid");
        when(mockRuleMapstruct.ruleReqToRuleDO(ruleReqDTO)).thenReturn(displayRuleDO);

        // Configure DisplayRuleMapstruct.toItemDOList(...).
        final DisplayItemDO displayItemDO = new DisplayItemDO();
        displayItemDO.setGuid("7e5ba61b-f27d-4746-abc1-480b55cad459");
        displayItemDO.setIsDelete(0);
        displayItemDO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayItemDO.setItemGuid("itemGuid");
        displayItemDO.setSort(0);
        displayItemDO.setRuleType(0);
        final List<DisplayItemDO> displayItemDOS = Arrays.asList(displayItemDO);
        final DisplayItemRespDTO displayItemRespDTO2 = new DisplayItemRespDTO();
        displayItemRespDTO2.setItemGuid("itemGuid");
        displayItemRespDTO2.setItemName("itemName");
        displayItemRespDTO2.setSort(0);
        displayItemRespDTO2.setRuleType(0);
        final List<DisplayItemRespDTO> itemList = Arrays.asList(displayItemRespDTO2);
        when(mockRuleMapstruct.toItemDOList(itemList)).thenReturn(displayItemDOS);

        when(mockDistributedIdService.nextBatchDisplayItemGuid(0L)).thenReturn(Arrays.asList("value"));

        // Configure DisplayItemService.listItem(...).
        final DisplayItemRespDTO displayItemRespDTO3 = new DisplayItemRespDTO();
        displayItemRespDTO3.setItemGuid("itemGuid");
        displayItemRespDTO3.setItemName("itemName");
        displayItemRespDTO3.setSort(0);
        displayItemRespDTO3.setRuleType(0);
        final List<DisplayItemRespDTO> displayItemRespDTOS = Arrays.asList(displayItemRespDTO3);
        final DisplayRuleQueryDTO reqDTO1 = new DisplayRuleQueryDTO();
        reqDTO1.setCurrentPage(0L);
        reqDTO1.setPageSize(0L);
        reqDTO1.setRuleGuid("ruleGuid");
        reqDTO1.setRuleType(0);
        reqDTO1.setBrandGuid("brandGuid");
        when(mockItemService.listItem(reqDTO1)).thenReturn(displayItemRespDTOS);

        // Configure DisplayItemService.saveBatch(...).
        final DisplayItemDO displayItemDO1 = new DisplayItemDO();
        displayItemDO1.setGuid("7e5ba61b-f27d-4746-abc1-480b55cad459");
        displayItemDO1.setIsDelete(0);
        displayItemDO1.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayItemDO1.setItemGuid("itemGuid");
        displayItemDO1.setSort(0);
        displayItemDO1.setRuleType(0);
        final List<DisplayItemDO> entityList = Arrays.asList(displayItemDO1);
        when(mockItemService.saveBatch(entityList)).thenReturn(false);

        // Configure DisplayRuleMapstruct.toStoreDOList(...).
        final DisplayStoreDO displayStoreDO = new DisplayStoreDO();
        displayStoreDO.setId(0L);
        displayStoreDO.setGuid("ded0f5ab-d2f9-478e-8348-d38f33b9516b");
        displayStoreDO.setIsDelete(0);
        displayStoreDO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayStoreDO.setRuleType(0);
        final List<DisplayStoreDO> displayStoreDOS = Arrays.asList(displayStoreDO);
        final DisplayStoreRespDTO displayStoreRespDTO2 = new DisplayStoreRespDTO();
        displayStoreRespDTO2.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO2.setRuleGuid("ruleGuid");
        displayStoreRespDTO2.setStoreGuid("storeGuid");
        displayStoreRespDTO2.setStoreName("storeName");
        displayStoreRespDTO2.setRuleType(0);
        final List<DisplayStoreRespDTO> storeList = Arrays.asList(displayStoreRespDTO2);
        when(mockRuleMapstruct.toStoreDOList(storeList)).thenReturn(displayStoreDOS);

        when(mockDistributedIdService.nextBatchDisplayStoreGuid(0L)).thenReturn(Collections.emptyList());

        // Configure DisplayStoreService.listStore(...).
        final DisplayStoreRespDTO displayStoreRespDTO3 = new DisplayStoreRespDTO();
        displayStoreRespDTO3.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO3.setRuleGuid("ruleGuid");
        displayStoreRespDTO3.setStoreGuid("storeGuid");
        displayStoreRespDTO3.setStoreName("storeName");
        displayStoreRespDTO3.setRuleType(0);
        final List<DisplayStoreRespDTO> displayStoreRespDTOS = Arrays.asList(displayStoreRespDTO3);
        final DisplayRuleQueryDTO reqDTO2 = new DisplayRuleQueryDTO();
        reqDTO2.setCurrentPage(0L);
        reqDTO2.setPageSize(0L);
        reqDTO2.setRuleGuid("ruleGuid");
        reqDTO2.setRuleType(0);
        reqDTO2.setBrandGuid("brandGuid");
        when(mockStoreService.listStore(reqDTO2)).thenReturn(displayStoreRespDTOS);

        // Configure DisplayStoreService.saveBatch(...).
        final DisplayStoreDO displayStoreDO1 = new DisplayStoreDO();
        displayStoreDO1.setId(0L);
        displayStoreDO1.setGuid("ded0f5ab-d2f9-478e-8348-d38f33b9516b");
        displayStoreDO1.setIsDelete(0);
        displayStoreDO1.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayStoreDO1.setRuleType(0);
        final List<DisplayStoreDO> entityList1 = Arrays.asList(displayStoreDO1);
        when(mockStoreService.saveBatch(entityList1)).thenReturn(false);

        // Run the test
        final Boolean result = displayRuleServiceImplUnderTest.updateRule(reqDTO);

        // Verify the results
        assertThat(result).isFalse();
        verify(mockItemService).remove(any(LambdaQueryWrapper.class));
        verify(mockStoreService).remove(any(LambdaQueryWrapper.class));
    }

    @Test
    public void testUpdateRule_DisplayStoreServiceListStoreReturnsNoItems() {
        // Setup
        final DisplayRuleSaveOrUpdateDTO reqDTO = new DisplayRuleSaveOrUpdateDTO();
        reqDTO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        reqDTO.setDisplayState(0);
        reqDTO.setDelayTime(0);
        reqDTO.setBatch(0);
        reqDTO.setEffectiveState(0);
        reqDTO.setEffectiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO.setIsAllStore(false);
        reqDTO.setRuleType(0);
        final DisplayStoreRespDTO displayStoreRespDTO = new DisplayStoreRespDTO();
        displayStoreRespDTO.setStoreGuid("storeGuid");
        displayStoreRespDTO.setStoreName("storeName");
        reqDTO.setStoreList(Arrays.asList(displayStoreRespDTO));
        final DisplayItemRespDTO displayItemRespDTO = new DisplayItemRespDTO();
        displayItemRespDTO.setItemGuid("itemGuid");
        displayItemRespDTO.setItemName("itemName");
        reqDTO.setItemList(Arrays.asList(displayItemRespDTO));
        reqDTO.setBrandGuid("brandGuid");

        // Configure DisplayRuleMapstruct.ruleReqToRuleDO(...).
        final DisplayRuleDO displayRuleDO = new DisplayRuleDO();
        displayRuleDO.setGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayRuleDO.setIsDelete(0);
        displayRuleDO.setDelayTime(0);
        displayRuleDO.setBatch(0);
        displayRuleDO.setIsAllStore(false);
        displayRuleDO.setRuleType(0);
        displayRuleDO.setBrandGuid("brandGuid");
        final DisplayRuleSaveOrUpdateDTO ruleReqDTO = new DisplayRuleSaveOrUpdateDTO();
        ruleReqDTO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        ruleReqDTO.setDisplayState(0);
        ruleReqDTO.setDelayTime(0);
        ruleReqDTO.setBatch(0);
        ruleReqDTO.setEffectiveState(0);
        ruleReqDTO.setEffectiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        ruleReqDTO.setIsAllStore(false);
        ruleReqDTO.setRuleType(0);
        final DisplayStoreRespDTO displayStoreRespDTO1 = new DisplayStoreRespDTO();
        displayStoreRespDTO1.setStoreGuid("storeGuid");
        displayStoreRespDTO1.setStoreName("storeName");
        ruleReqDTO.setStoreList(Arrays.asList(displayStoreRespDTO1));
        final DisplayItemRespDTO displayItemRespDTO1 = new DisplayItemRespDTO();
        displayItemRespDTO1.setItemGuid("itemGuid");
        displayItemRespDTO1.setItemName("itemName");
        ruleReqDTO.setItemList(Arrays.asList(displayItemRespDTO1));
        ruleReqDTO.setBrandGuid("brandGuid");
        when(mockRuleMapstruct.ruleReqToRuleDO(ruleReqDTO)).thenReturn(displayRuleDO);

        // Configure DisplayRuleMapstruct.toItemDOList(...).
        final DisplayItemDO displayItemDO = new DisplayItemDO();
        displayItemDO.setGuid("7e5ba61b-f27d-4746-abc1-480b55cad459");
        displayItemDO.setIsDelete(0);
        displayItemDO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayItemDO.setItemGuid("itemGuid");
        displayItemDO.setSort(0);
        displayItemDO.setRuleType(0);
        final List<DisplayItemDO> displayItemDOS = Arrays.asList(displayItemDO);
        final DisplayItemRespDTO displayItemRespDTO2 = new DisplayItemRespDTO();
        displayItemRespDTO2.setItemGuid("itemGuid");
        displayItemRespDTO2.setItemName("itemName");
        displayItemRespDTO2.setSort(0);
        displayItemRespDTO2.setRuleType(0);
        final List<DisplayItemRespDTO> itemList = Arrays.asList(displayItemRespDTO2);
        when(mockRuleMapstruct.toItemDOList(itemList)).thenReturn(displayItemDOS);

        when(mockDistributedIdService.nextBatchDisplayItemGuid(0L)).thenReturn(Arrays.asList("value"));

        // Configure DisplayItemService.listItem(...).
        final DisplayItemRespDTO displayItemRespDTO3 = new DisplayItemRespDTO();
        displayItemRespDTO3.setItemGuid("itemGuid");
        displayItemRespDTO3.setItemName("itemName");
        displayItemRespDTO3.setSort(0);
        displayItemRespDTO3.setRuleType(0);
        final List<DisplayItemRespDTO> displayItemRespDTOS = Arrays.asList(displayItemRespDTO3);
        final DisplayRuleQueryDTO reqDTO1 = new DisplayRuleQueryDTO();
        reqDTO1.setCurrentPage(0L);
        reqDTO1.setPageSize(0L);
        reqDTO1.setRuleGuid("ruleGuid");
        reqDTO1.setRuleType(0);
        reqDTO1.setBrandGuid("brandGuid");
        when(mockItemService.listItem(reqDTO1)).thenReturn(displayItemRespDTOS);

        // Configure DisplayItemService.saveBatch(...).
        final DisplayItemDO displayItemDO1 = new DisplayItemDO();
        displayItemDO1.setGuid("7e5ba61b-f27d-4746-abc1-480b55cad459");
        displayItemDO1.setIsDelete(0);
        displayItemDO1.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayItemDO1.setItemGuid("itemGuid");
        displayItemDO1.setSort(0);
        displayItemDO1.setRuleType(0);
        final List<DisplayItemDO> entityList = Arrays.asList(displayItemDO1);
        when(mockItemService.saveBatch(entityList)).thenReturn(false);

        // Configure DisplayRuleMapstruct.toStoreDOList(...).
        final DisplayStoreDO displayStoreDO = new DisplayStoreDO();
        displayStoreDO.setId(0L);
        displayStoreDO.setGuid("ded0f5ab-d2f9-478e-8348-d38f33b9516b");
        displayStoreDO.setIsDelete(0);
        displayStoreDO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayStoreDO.setRuleType(0);
        final List<DisplayStoreDO> displayStoreDOS = Arrays.asList(displayStoreDO);
        final DisplayStoreRespDTO displayStoreRespDTO2 = new DisplayStoreRespDTO();
        displayStoreRespDTO2.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO2.setRuleGuid("ruleGuid");
        displayStoreRespDTO2.setStoreGuid("storeGuid");
        displayStoreRespDTO2.setStoreName("storeName");
        displayStoreRespDTO2.setRuleType(0);
        final List<DisplayStoreRespDTO> storeList = Arrays.asList(displayStoreRespDTO2);
        when(mockRuleMapstruct.toStoreDOList(storeList)).thenReturn(displayStoreDOS);

        when(mockDistributedIdService.nextBatchDisplayStoreGuid(0L)).thenReturn(Arrays.asList("value"));

        // Configure DisplayStoreService.listStore(...).
        final DisplayRuleQueryDTO reqDTO2 = new DisplayRuleQueryDTO();
        reqDTO2.setCurrentPage(0L);
        reqDTO2.setPageSize(0L);
        reqDTO2.setRuleGuid("ruleGuid");
        reqDTO2.setRuleType(0);
        reqDTO2.setBrandGuid("brandGuid");
        when(mockStoreService.listStore(reqDTO2)).thenReturn(Collections.emptyList());

        // Configure DisplayStoreService.saveBatch(...).
        final DisplayStoreDO displayStoreDO1 = new DisplayStoreDO();
        displayStoreDO1.setId(0L);
        displayStoreDO1.setGuid("ded0f5ab-d2f9-478e-8348-d38f33b9516b");
        displayStoreDO1.setIsDelete(0);
        displayStoreDO1.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayStoreDO1.setRuleType(0);
        final List<DisplayStoreDO> entityList1 = Arrays.asList(displayStoreDO1);
        when(mockStoreService.saveBatch(entityList1)).thenReturn(false);

        // Run the test
        final Boolean result = displayRuleServiceImplUnderTest.updateRule(reqDTO);

        // Verify the results
        assertThat(result).isFalse();
        verify(mockItemService).remove(any(LambdaQueryWrapper.class));
        verify(mockStoreService).remove(any(LambdaQueryWrapper.class));
    }

    @Test
    public void testUpdateRule_DisplayStoreServiceSaveBatchReturnsTrue() {
        // Setup
        final DisplayRuleSaveOrUpdateDTO reqDTO = new DisplayRuleSaveOrUpdateDTO();
        reqDTO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        reqDTO.setDisplayState(0);
        reqDTO.setDelayTime(0);
        reqDTO.setBatch(0);
        reqDTO.setEffectiveState(0);
        reqDTO.setEffectiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO.setIsAllStore(false);
        reqDTO.setRuleType(0);
        final DisplayStoreRespDTO displayStoreRespDTO = new DisplayStoreRespDTO();
        displayStoreRespDTO.setStoreGuid("storeGuid");
        displayStoreRespDTO.setStoreName("storeName");
        reqDTO.setStoreList(Arrays.asList(displayStoreRespDTO));
        final DisplayItemRespDTO displayItemRespDTO = new DisplayItemRespDTO();
        displayItemRespDTO.setItemGuid("itemGuid");
        displayItemRespDTO.setItemName("itemName");
        reqDTO.setItemList(Arrays.asList(displayItemRespDTO));
        reqDTO.setBrandGuid("brandGuid");

        // Configure DisplayRuleMapstruct.ruleReqToRuleDO(...).
        final DisplayRuleDO displayRuleDO = new DisplayRuleDO();
        displayRuleDO.setGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayRuleDO.setIsDelete(0);
        displayRuleDO.setDelayTime(0);
        displayRuleDO.setBatch(0);
        displayRuleDO.setIsAllStore(false);
        displayRuleDO.setRuleType(0);
        displayRuleDO.setBrandGuid("brandGuid");
        final DisplayRuleSaveOrUpdateDTO ruleReqDTO = new DisplayRuleSaveOrUpdateDTO();
        ruleReqDTO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        ruleReqDTO.setDisplayState(0);
        ruleReqDTO.setDelayTime(0);
        ruleReqDTO.setBatch(0);
        ruleReqDTO.setEffectiveState(0);
        ruleReqDTO.setEffectiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        ruleReqDTO.setIsAllStore(false);
        ruleReqDTO.setRuleType(0);
        final DisplayStoreRespDTO displayStoreRespDTO1 = new DisplayStoreRespDTO();
        displayStoreRespDTO1.setStoreGuid("storeGuid");
        displayStoreRespDTO1.setStoreName("storeName");
        ruleReqDTO.setStoreList(Arrays.asList(displayStoreRespDTO1));
        final DisplayItemRespDTO displayItemRespDTO1 = new DisplayItemRespDTO();
        displayItemRespDTO1.setItemGuid("itemGuid");
        displayItemRespDTO1.setItemName("itemName");
        ruleReqDTO.setItemList(Arrays.asList(displayItemRespDTO1));
        ruleReqDTO.setBrandGuid("brandGuid");
        when(mockRuleMapstruct.ruleReqToRuleDO(ruleReqDTO)).thenReturn(displayRuleDO);

        // Configure DisplayRuleMapstruct.toItemDOList(...).
        final DisplayItemDO displayItemDO = new DisplayItemDO();
        displayItemDO.setGuid("7e5ba61b-f27d-4746-abc1-480b55cad459");
        displayItemDO.setIsDelete(0);
        displayItemDO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayItemDO.setItemGuid("itemGuid");
        displayItemDO.setSort(0);
        displayItemDO.setRuleType(0);
        final List<DisplayItemDO> displayItemDOS = Arrays.asList(displayItemDO);
        final DisplayItemRespDTO displayItemRespDTO2 = new DisplayItemRespDTO();
        displayItemRespDTO2.setItemGuid("itemGuid");
        displayItemRespDTO2.setItemName("itemName");
        displayItemRespDTO2.setSort(0);
        displayItemRespDTO2.setRuleType(0);
        final List<DisplayItemRespDTO> itemList = Arrays.asList(displayItemRespDTO2);
        when(mockRuleMapstruct.toItemDOList(itemList)).thenReturn(displayItemDOS);

        when(mockDistributedIdService.nextBatchDisplayItemGuid(0L)).thenReturn(Arrays.asList("value"));

        // Configure DisplayItemService.listItem(...).
        final DisplayItemRespDTO displayItemRespDTO3 = new DisplayItemRespDTO();
        displayItemRespDTO3.setItemGuid("itemGuid");
        displayItemRespDTO3.setItemName("itemName");
        displayItemRespDTO3.setSort(0);
        displayItemRespDTO3.setRuleType(0);
        final List<DisplayItemRespDTO> displayItemRespDTOS = Arrays.asList(displayItemRespDTO3);
        final DisplayRuleQueryDTO reqDTO1 = new DisplayRuleQueryDTO();
        reqDTO1.setCurrentPage(0L);
        reqDTO1.setPageSize(0L);
        reqDTO1.setRuleGuid("ruleGuid");
        reqDTO1.setRuleType(0);
        reqDTO1.setBrandGuid("brandGuid");
        when(mockItemService.listItem(reqDTO1)).thenReturn(displayItemRespDTOS);

        // Configure DisplayItemService.saveBatch(...).
        final DisplayItemDO displayItemDO1 = new DisplayItemDO();
        displayItemDO1.setGuid("7e5ba61b-f27d-4746-abc1-480b55cad459");
        displayItemDO1.setIsDelete(0);
        displayItemDO1.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayItemDO1.setItemGuid("itemGuid");
        displayItemDO1.setSort(0);
        displayItemDO1.setRuleType(0);
        final List<DisplayItemDO> entityList = Arrays.asList(displayItemDO1);
        when(mockItemService.saveBatch(entityList)).thenReturn(false);

        // Configure DisplayRuleMapstruct.toStoreDOList(...).
        final DisplayStoreDO displayStoreDO = new DisplayStoreDO();
        displayStoreDO.setId(0L);
        displayStoreDO.setGuid("ded0f5ab-d2f9-478e-8348-d38f33b9516b");
        displayStoreDO.setIsDelete(0);
        displayStoreDO.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayStoreDO.setRuleType(0);
        final List<DisplayStoreDO> displayStoreDOS = Arrays.asList(displayStoreDO);
        final DisplayStoreRespDTO displayStoreRespDTO2 = new DisplayStoreRespDTO();
        displayStoreRespDTO2.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO2.setRuleGuid("ruleGuid");
        displayStoreRespDTO2.setStoreGuid("storeGuid");
        displayStoreRespDTO2.setStoreName("storeName");
        displayStoreRespDTO2.setRuleType(0);
        final List<DisplayStoreRespDTO> storeList = Arrays.asList(displayStoreRespDTO2);
        when(mockRuleMapstruct.toStoreDOList(storeList)).thenReturn(displayStoreDOS);

        when(mockDistributedIdService.nextBatchDisplayStoreGuid(0L)).thenReturn(Arrays.asList("value"));

        // Configure DisplayStoreService.listStore(...).
        final DisplayStoreRespDTO displayStoreRespDTO3 = new DisplayStoreRespDTO();
        displayStoreRespDTO3.setGuid("8122f0bc-f791-490f-9af8-569b41a65be3");
        displayStoreRespDTO3.setRuleGuid("ruleGuid");
        displayStoreRespDTO3.setStoreGuid("storeGuid");
        displayStoreRespDTO3.setStoreName("storeName");
        displayStoreRespDTO3.setRuleType(0);
        final List<DisplayStoreRespDTO> displayStoreRespDTOS = Arrays.asList(displayStoreRespDTO3);
        final DisplayRuleQueryDTO reqDTO2 = new DisplayRuleQueryDTO();
        reqDTO2.setCurrentPage(0L);
        reqDTO2.setPageSize(0L);
        reqDTO2.setRuleGuid("ruleGuid");
        reqDTO2.setRuleType(0);
        reqDTO2.setBrandGuid("brandGuid");
        when(mockStoreService.listStore(reqDTO2)).thenReturn(displayStoreRespDTOS);

        // Configure DisplayStoreService.saveBatch(...).
        final DisplayStoreDO displayStoreDO1 = new DisplayStoreDO();
        displayStoreDO1.setId(0L);
        displayStoreDO1.setGuid("ded0f5ab-d2f9-478e-8348-d38f33b9516b");
        displayStoreDO1.setIsDelete(0);
        displayStoreDO1.setRuleGuid("805f0a9e-d573-49ad-8183-f5a37fb61668");
        displayStoreDO1.setRuleType(0);
        final List<DisplayStoreDO> entityList1 = Arrays.asList(displayStoreDO1);
        when(mockStoreService.saveBatch(entityList1)).thenReturn(true);

        // Run the test
        final Boolean result = displayRuleServiceImplUnderTest.updateRule(reqDTO);

        // Verify the results
        assertThat(result).isTrue();
        verify(mockItemService).remove(any(LambdaQueryWrapper.class));
        verify(mockStoreService).remove(any(LambdaQueryWrapper.class));
    }

    @Test
    public void testQueryHasAllStore() {
        // Setup
        final DisplayRuleQueryDTO reqDTO = new DisplayRuleQueryDTO();
        reqDTO.setCurrentPage(0L);
        reqDTO.setPageSize(0L);
        reqDTO.setRuleGuid("ruleGuid");
        reqDTO.setRuleType(0);
        reqDTO.setBrandGuid("brandGuid");

        // Run the test
        final Boolean result = displayRuleServiceImplUnderTest.queryHasAllStore(reqDTO);

        // Verify the results
        assertThat(result).isFalse();
    }
}
