package com.holderzone.saas.store.kds.service.print.impl;

import com.holderzone.saas.store.kds.entity.read.KdsPrintRecordReadDO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class KdsPrintCacheServiceImplTest {

    @Mock
    private RedisTemplate<String, Object> mockRedisTemplateObject;
    @Mock
    private RedisTemplate<String, KdsPrintRecordReadDO> mockRedisTemplateKdsPrintRecord;
    @Mock
    private RedisTemplate<String, List<KdsPrintRecordReadDO>> mockRedisTemplateListKdsPrintRecord;

    private KdsPrintCacheServiceImpl kdsPrintCacheServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        kdsPrintCacheServiceImplUnderTest = new KdsPrintCacheServiceImpl(mockRedisTemplateObject,
                mockRedisTemplateKdsPrintRecord, mockRedisTemplateListKdsPrintRecord);
        ReflectionTestUtils.setField(kdsPrintCacheServiceImplUnderTest, "batchPushEnable", false);
    }

    @Test
    public void testSave() {
        // Setup
        final KdsPrintRecordReadDO kdsPrintRecordReadDO = new KdsPrintRecordReadDO();
        kdsPrintRecordReadDO.setId(0L);
        kdsPrintRecordReadDO.setGuid("655afa5d-5478-4c16-8d3f-5d7d6541883f");
        kdsPrintRecordReadDO.setRecordUid("recordUid");
        kdsPrintRecordReadDO.setStoreGuid("storeGuid");
        kdsPrintRecordReadDO.setDeviceId("deviceId");
        final List<KdsPrintRecordReadDO> arrayOfPrintRecordReadDO = Arrays.asList(kdsPrintRecordReadDO);
        when(mockRedisTemplateListKdsPrintRecord.opsForValue()).thenReturn(null);
        when(mockRedisTemplateKdsPrintRecord.opsForValue()).thenReturn(null);

        // Run the test
        kdsPrintCacheServiceImplUnderTest.save(arrayOfPrintRecordReadDO);

        // Verify the results
    }

    @Test
    public void testPopSingle() {
        // Setup
        final KdsPrintRecordReadDO expectedResult = new KdsPrintRecordReadDO();
        expectedResult.setId(0L);
        expectedResult.setGuid("655afa5d-5478-4c16-8d3f-5d7d6541883f");
        expectedResult.setRecordUid("recordUid");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setDeviceId("deviceId");

        when(mockRedisTemplateKdsPrintRecord.opsForValue()).thenReturn(null);

        // Run the test
        final KdsPrintRecordReadDO result = kdsPrintCacheServiceImplUnderTest.popSingle("recordGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockRedisTemplateKdsPrintRecord).delete("key");
    }

    @Test
    public void testPopBatch() {
        // Setup
        final KdsPrintRecordReadDO kdsPrintRecordReadDO = new KdsPrintRecordReadDO();
        kdsPrintRecordReadDO.setId(0L);
        kdsPrintRecordReadDO.setGuid("655afa5d-5478-4c16-8d3f-5d7d6541883f");
        kdsPrintRecordReadDO.setRecordUid("recordUid");
        kdsPrintRecordReadDO.setStoreGuid("storeGuid");
        kdsPrintRecordReadDO.setDeviceId("deviceId");
        final List<KdsPrintRecordReadDO> expectedResult = Arrays.asList(kdsPrintRecordReadDO);
        when(mockRedisTemplateListKdsPrintRecord.opsForValue()).thenReturn(null);

        // Run the test
        final List<KdsPrintRecordReadDO> result = kdsPrintCacheServiceImplUnderTest.popBatch(Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockRedisTemplateListKdsPrintRecord).delete("key");
    }
}
