package com.holderzone.saas.store.kds.utils;


import com.holderzone.saas.store.dto.kds.req.PrdItemStatusReqDTO;
import com.holderzone.saas.store.dto.kds.resp.PrdDstItemDTO;
import com.holderzone.saas.store.dto.kds.resp.PrdDstOrderDTO;
import com.holderzone.saas.store.dto.kds.resp.PrdDstRespDTO;
import com.holderzone.saas.store.enums.kds.KdsItemStateEnum;
import com.holderzone.saas.store.kds.entity.bo.PrdRespBO;
import com.holderzone.saas.store.kds.entity.domain.DeviceConfigDO;
import com.holderzone.saas.store.kds.entity.enums.DisplayRuleType;
import com.holderzone.saas.store.kds.entity.enums.KdsKitchenStateEnum;
import com.holderzone.saas.store.kds.entity.read.KitchenItemReadDO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@SpringBootTest
@Slf4j
public class KitchenItemUtilsTest {
    public static KitchenItemReadDO buildKitchenItemReadDO(){
        KitchenItemReadDO kitchenItemReadDO = new KitchenItemReadDO();
        kitchenItemReadDO.setOrderItemGuid(UUID.randomUUID().toString());
        kitchenItemReadDO.setItemGuid(UUID.randomUUID().toString());
        kitchenItemReadDO.setItemAttrMd5(UUID.randomUUID().toString());
        kitchenItemReadDO.setIsWeight(false);
        kitchenItemReadDO.setAttrs(new ArrayList<>());
        kitchenItemReadDO.setUrgedTime(LocalDateTime.now());
        kitchenItemReadDO.setCallUpTime(LocalDateTime.now());
        kitchenItemReadDO.setOrderNumber("100");
        kitchenItemReadDO.setPrepareTime(LocalDateTime.now());
        kitchenItemReadDO.setHangUpTime(LocalDateTime.now());
        kitchenItemReadDO.setItemState(KdsItemStateEnum.PREPARE.getCode());
        kitchenItemReadDO.setKitchenState(KdsKitchenStateEnum.TO_PRD.getCode());
        return kitchenItemReadDO;
    }


    @Test
   public  void testQueryPrdStatusBySummaryItemOrder() {
        PrdItemStatusReqDTO prdItemStatusReqDTO = new PrdItemStatusReqDTO();
        // 构建返回对象
        PrdDstRespDTO prdDstRespDTO = new PrdDstRespDTO();
        // 查询设备信息，即该设备作为制作点位或出堂口时设备的相关信息
        PrdRespBO.PrdRespBOBuilder builder = PrdRespBO.builder();
        DeviceConfigDO deviceConfig = new DeviceConfigDO();
        deviceConfig.setIsDisplayByMaxCopied(true);
        deviceConfig.setIsShowHangedItem(false);
        builder.deviceConfig(deviceConfig);

        List<KitchenItemReadDO> kitchenItemReadInSql = new ArrayList<>();
        int[] delayTimeList = new int[]{-30,0,30};
        for (int i=0;i<delayTimeList.length;i++){
            KitchenItemReadDO summeryItem =  buildKitchenItemReadDO();
            summeryItem.setDisplayRuleType(DisplayRuleType.SUMMERY);
            summeryItem.setSort(i+1);
            kitchenItemReadInSql.add(summeryItem);
        }
        for (int i=0;i<3;i++){
            KitchenItemReadDO summeryItem =  buildKitchenItemReadDO();
            summeryItem.setDisplayRuleType(DisplayRuleType.SUMMERY);
            summeryItem.setSort(i+1);
            kitchenItemReadInSql.add(summeryItem);
        }
        for (int i=0;i<delayTimeList.length;i++) {
            String orderGuid = UUID.randomUUID().toString();
            for (int j = 1; j <= 5; j++) {
                KitchenItemReadDO orderItem = buildKitchenItemReadDO();
                orderItem.setOrderGuid(orderGuid);
                orderItem.setDisplayRuleType(DisplayRuleType.BATCH);
                if (j != 1) {
                    orderItem.setBatch(j);
                }else{
                    orderItem.setBatch(-1);
                }
                orderItem.setOrderNumber(""+j);
                kitchenItemReadInSql.add(orderItem);
            }
        }
        KitchenItemUtils.queryPrdStatusBySummaryItemOrder(prdItemStatusReqDTO,kitchenItemReadInSql,builder,prdDstRespDTO);
        assert(prdDstRespDTO.getItems().getTotalCount()==5);
        assert(prdDstRespDTO.getOrders().getTotalCount()==2);
        for (PrdDstOrderDTO datum : prdDstRespDTO.getOrders().getData()) {
            for (PrdDstItemDTO item : datum.getItems()) {
                log.info(datum.getOrderGuid()+" "+item.getItemGuid()+" "+item.getBatch());
            }
            log.info("------------------------------------------------------------------------");
        }
    }
}
