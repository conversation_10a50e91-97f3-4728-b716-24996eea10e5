package com.holderzone.saas.store.kds.utils;

import com.alibaba.fastjson.JSON;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.util.MultiValueMap;

/**
 * <AUTHOR>
 * @description
 * @date 2022/2/9 14:43
 * @className: MockHttpUtil
 */
public class MockHttpUtil {

    private static String Authorization = "token";

    private static String token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJlbnRlcnByaXNlR3VpZCI6IjY1MDY0MzExOTU2NTE5ODIzMzciLCJzdG9yZU5vIjoiMzE4NzI4OSIsImRldmljZUd1aWQiOiIyMTEyMTUxNzUzMjAwODQwMDA2IiwidXNlckd1aWQiOiI2ODYzNjkyNTc4MTc3NDE3MjE3IiwiaWF0IjoxNjQyNTc4NjExLCJpc3MiOiJIb2xkZXIuY29tIn0=.Osaj77+977+977WdSO+/vXk977+9Owzvv70sT++/ve+/ve+/ve+/vQLehXrvv73vv71+SO+/vUc=";

    private static String headerOfUserInfo = "userInfo";

    private static String userInfo = "{\"operSubjectGuid\": \"2008141005594470007\",\"enterpriseGuid\":" +
            " \"6506431195651982337\",\"enterpriseName\": \"企业0227\",\"enterpriseNo\": \"********\",\"storeGuid\":" +
            " \"6619160595813892096\",\"storeName\": \"听雨阁\",\"storeNo\": \"6148139\",\"userGuid\": " +
            "\"6653489337230950401\",\"account\": \"200003\",\"tel\": \"***********\",\"name\": \"赵亮\"}\n";

    /**
     * get
     */
    public static String get(String uri, MultiValueMap<String, String> params, MockMvc mockMvc) {
        try {
            MockHttpServletRequestBuilder request = MockMvcRequestBuilders.get(uri);
            request.header(Authorization, token);
            request.header(headerOfUserInfo, userInfo);
            if (params != null) {
                request.params(params);
            }
            MvcResult result = mockMvc.perform(request).andExpect(MockMvcResultMatchers.status().isOk()).andReturn();
            MockHttpServletResponse response = result.getResponse();
            return response.getContentAsString();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * post
     */
    public static String post(String uri, MultiValueMap<String, String> params, MockMvc mockMvc) {
        try {
            MockHttpServletRequestBuilder request = MockMvcRequestBuilders.post(uri);
            request.header(Authorization, token);
            request.header(headerOfUserInfo, userInfo);
            if (params != null) {
                request.params(params);
            }
            MvcResult result = mockMvc.perform(request).andExpect(MockMvcResultMatchers.status().isOk()).andReturn();
            MockHttpServletResponse response = result.getResponse();
            return response.getContentAsString();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * post
     */
    public static String post(String uri, Object body, MockMvc mockMvc) {
        try {
            MockHttpServletRequestBuilder request = MockMvcRequestBuilders.post(uri)
                    .contentType(MediaType.APPLICATION_JSON).accept(MediaType.APPLICATION_JSON)
                    .content(JSON.toJSONString(body));
            request.header(Authorization, token);
            request.header(headerOfUserInfo, userInfo);
            MvcResult result = null;
            result = mockMvc.perform(request).andExpect(MockMvcResultMatchers.status().isOk()).andReturn();
            MockHttpServletResponse response = result.getResponse();
            return response.getContentAsString();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * put
     */
    public static String put(String uri, MultiValueMap<String, String> params, MockMvc mockMvc) {
        try {
            MockHttpServletRequestBuilder request = MockMvcRequestBuilders.put(uri);
            request.header(Authorization, token);
            request.header(headerOfUserInfo, userInfo);
            if (params != null) {
                request.params(params);
            }
            MvcResult result = mockMvc.perform(request).andExpect(MockMvcResultMatchers.status().isOk()).andReturn();
            MockHttpServletResponse response = result.getResponse();
            return response.getContentAsString();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * put
     */
    public static String put(String uri, Object body, MockMvc mockMvc) {
        try {
            MockHttpServletRequestBuilder request = MockMvcRequestBuilders.put(uri)
                    .contentType(MediaType.APPLICATION_JSON).accept(MediaType.APPLICATION_JSON)
                    .content(JSON.toJSONString(body));
            request.header(Authorization, token);
            request.header(headerOfUserInfo, userInfo);
            MvcResult result = null;
            result = mockMvc.perform(request).andExpect(MockMvcResultMatchers.status().isOk()).andReturn();
            MockHttpServletResponse response = result.getResponse();
            return response.getContentAsString();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * delete
     */
    public static String delete(String uri, MockMvc mockMvc) {
        try {
            MockHttpServletRequestBuilder request = MockMvcRequestBuilders.delete(uri);
            request.header(Authorization, token);
            request.header(headerOfUserInfo, userInfo);
            MvcResult result = mockMvc.perform(request).andExpect(MockMvcResultMatchers.status().isOk()).andReturn();
            MockHttpServletResponse response = result.getResponse();
            return response.getContentAsString();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

}
