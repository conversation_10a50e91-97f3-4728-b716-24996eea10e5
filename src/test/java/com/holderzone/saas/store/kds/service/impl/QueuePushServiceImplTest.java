package com.holderzone.saas.store.kds.service.impl;

import com.google.common.util.concurrent.MoreExecutors;
import com.holderzone.framework.base.dto.message.MessageDTO;
import com.holderzone.saas.store.kds.service.rpc.MsgRpcService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;

@RunWith(MockitoJUnitRunner.class)
public class QueuePushServiceImplTest {

    @Mock
    private MsgRpcService mockMsgRpcService;

    private QueuePushServiceImpl queuePushServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        queuePushServiceImplUnderTest = new QueuePushServiceImpl(MoreExecutors.newDirectExecutorService(),
                mockMsgRpcService);
    }

    @Test
    public void testStatusChanged() {
        // Setup
        // Run the test
        queuePushServiceImplUnderTest.statusChanged("enterpriseGuid", "storeGuid", "deviceId", "voiceMsg");

        // Verify the results
        verify(mockMsgRpcService).sendPrintMessage(any(MessageDTO.class));
    }
}
