package com.holderzone.saas.store.kds.service.impl;

import com.holderzone.saas.store.dto.item.resp.ItemInfoRespDTO;
import com.holderzone.saas.store.dto.kds.req.DisplayRuleQueryDTO;
import com.holderzone.saas.store.dto.kds.resp.DisplayItemRespDTO;
import com.holderzone.saas.store.kds.entity.domain.DisplayItemDO;
import com.holderzone.saas.store.kds.mapstruct.DisplayRuleMapstruct;
import com.holderzone.saas.store.kds.service.rpc.ItemRpcService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class DisplayItemServiceImplTest {

    @Mock
    private DisplayRuleMapstruct mockRuleMapstruct;
    @Mock
    private ItemRpcService mockItemRpcService;

    private DisplayItemServiceImpl displayItemServiceImplUnderTest;

    @Before
    public void setUp() {
        displayItemServiceImplUnderTest = new DisplayItemServiceImpl(mockRuleMapstruct, mockItemRpcService);
    }

    @Test
    public void testListItem() {
        // Setup
        final DisplayRuleQueryDTO reqDTO = new DisplayRuleQueryDTO();
        reqDTO.setRuleGuid("ruleGuid");
        reqDTO.setRuleType(0);
        reqDTO.setBrandGuid("brandGuid");

        final DisplayItemRespDTO displayItemRespDTO = new DisplayItemRespDTO();
        displayItemRespDTO.setItemGuid("itemGuid");
        displayItemRespDTO.setItemName("itemName");
        displayItemRespDTO.setSort(0);
        displayItemRespDTO.setRuleType(0);
        final List<DisplayItemRespDTO> expectedResult = Arrays.asList(displayItemRespDTO);

        // Configure DisplayRuleMapstruct.toItemRespList(...).
        final DisplayItemRespDTO displayItemRespDTO1 = new DisplayItemRespDTO();
        displayItemRespDTO1.setItemGuid("itemGuid");
        displayItemRespDTO1.setItemName("itemName");
        displayItemRespDTO1.setSort(0);
        displayItemRespDTO1.setRuleType(0);
        final List<DisplayItemRespDTO> displayItemRespDTOS = Arrays.asList(displayItemRespDTO1);
        final DisplayItemDO displayItemDO1 = new DisplayItemDO();
        displayItemDO1.setId(0L);
        displayItemDO1.setGuid("4f64c6f0-d05b-4bee-aa81-1488dcbcf98a");
        displayItemDO1.setRuleGuid("ruleGuid");
        displayItemDO1.setItemGuid("itemGuid");
        displayItemDO1.setRuleType(0);
        final List<DisplayItemDO> displayItemDO = Arrays.asList(displayItemDO1);
        when(mockRuleMapstruct.toItemRespList(displayItemDO)).thenReturn(displayItemRespDTOS);

        // Configure ItemRpcService.kdsItemParentMapping(...).
        final ItemInfoRespDTO itemInfoRespDTO = new ItemInfoRespDTO();
        itemInfoRespDTO.setItemGuid("itemGuid");
        itemInfoRespDTO.setBrandGuid("brandGuid");
        itemInfoRespDTO.setStoreGuid("storeGuid");
        itemInfoRespDTO.setParentGuid("parentGuid");
        itemInfoRespDTO.setName("itemName");
        final List<ItemInfoRespDTO> itemInfoRespDTOS = Arrays.asList(itemInfoRespDTO);
        when(mockItemRpcService.kdsItemParentMapping(Arrays.asList("value"))).thenReturn(itemInfoRespDTOS);

        // Run the test
        final List<DisplayItemRespDTO> result = displayItemServiceImplUnderTest.listItem(reqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testListItem_DisplayRuleMapstructReturnsNoItems() {
        // Setup
        final DisplayRuleQueryDTO reqDTO = new DisplayRuleQueryDTO();
        reqDTO.setRuleGuid("ruleGuid");
        reqDTO.setRuleType(0);
        reqDTO.setBrandGuid("brandGuid");

        // Configure DisplayRuleMapstruct.toItemRespList(...).
        final DisplayItemDO displayItemDO1 = new DisplayItemDO();
        displayItemDO1.setId(0L);
        displayItemDO1.setGuid("4f64c6f0-d05b-4bee-aa81-1488dcbcf98a");
        displayItemDO1.setRuleGuid("ruleGuid");
        displayItemDO1.setItemGuid("itemGuid");
        displayItemDO1.setRuleType(0);
        final List<DisplayItemDO> displayItemDO = Arrays.asList(displayItemDO1);
        when(mockRuleMapstruct.toItemRespList(displayItemDO)).thenReturn(Collections.emptyList());

        // Configure ItemRpcService.kdsItemParentMapping(...).
        final ItemInfoRespDTO itemInfoRespDTO = new ItemInfoRespDTO();
        itemInfoRespDTO.setItemGuid("itemGuid");
        itemInfoRespDTO.setBrandGuid("brandGuid");
        itemInfoRespDTO.setStoreGuid("storeGuid");
        itemInfoRespDTO.setParentGuid("parentGuid");
        itemInfoRespDTO.setName("itemName");
        final List<ItemInfoRespDTO> itemInfoRespDTOS = Arrays.asList(itemInfoRespDTO);
        when(mockItemRpcService.kdsItemParentMapping(Arrays.asList("value"))).thenReturn(itemInfoRespDTOS);

        // Run the test
        final List<DisplayItemRespDTO> result = displayItemServiceImplUnderTest.listItem(reqDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testListItem_ItemRpcServiceReturnsNoItems() {
        // Setup
        final DisplayRuleQueryDTO reqDTO = new DisplayRuleQueryDTO();
        reqDTO.setRuleGuid("ruleGuid");
        reqDTO.setRuleType(0);
        reqDTO.setBrandGuid("brandGuid");

        final DisplayItemRespDTO displayItemRespDTO = new DisplayItemRespDTO();
        displayItemRespDTO.setItemGuid("itemGuid");
        displayItemRespDTO.setItemName("itemName");
        displayItemRespDTO.setSort(0);
        displayItemRespDTO.setRuleType(0);
        final List<DisplayItemRespDTO> expectedResult = Arrays.asList(displayItemRespDTO);

        // Configure DisplayRuleMapstruct.toItemRespList(...).
        final DisplayItemRespDTO displayItemRespDTO1 = new DisplayItemRespDTO();
        displayItemRespDTO1.setItemGuid("itemGuid");
        displayItemRespDTO1.setItemName("itemName");
        displayItemRespDTO1.setSort(0);
        displayItemRespDTO1.setRuleType(0);
        final List<DisplayItemRespDTO> displayItemRespDTOS = Arrays.asList(displayItemRespDTO1);
        final DisplayItemDO displayItemDO1 = new DisplayItemDO();
        displayItemDO1.setId(0L);
        displayItemDO1.setGuid("4f64c6f0-d05b-4bee-aa81-1488dcbcf98a");
        displayItemDO1.setRuleGuid("ruleGuid");
        displayItemDO1.setItemGuid("itemGuid");
        displayItemDO1.setRuleType(0);
        final List<DisplayItemDO> displayItemDO = Arrays.asList(displayItemDO1);
        when(mockRuleMapstruct.toItemRespList(displayItemDO)).thenReturn(displayItemRespDTOS);

        when(mockItemRpcService.kdsItemParentMapping(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        final List<DisplayItemRespDTO> result = displayItemServiceImplUnderTest.listItem(reqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryItemInfo() {
        // Setup
        final DisplayItemRespDTO displayItemRespDTO = new DisplayItemRespDTO();
        displayItemRespDTO.setItemGuid("itemGuid");
        displayItemRespDTO.setItemName("itemName");
        displayItemRespDTO.setSort(0);
        displayItemRespDTO.setRuleType(0);
        final List<DisplayItemRespDTO> displayItemRespDTOS = Arrays.asList(displayItemRespDTO);
        final DisplayItemRespDTO displayItemRespDTO1 = new DisplayItemRespDTO();
        displayItemRespDTO1.setItemGuid("itemGuid");
        displayItemRespDTO1.setItemName("itemName");
        displayItemRespDTO1.setSort(0);
        displayItemRespDTO1.setRuleType(0);
        final List<DisplayItemRespDTO> expectedResult = Arrays.asList(displayItemRespDTO1);

        // Configure ItemRpcService.kdsItemParentMapping(...).
        final ItemInfoRespDTO itemInfoRespDTO = new ItemInfoRespDTO();
        itemInfoRespDTO.setItemGuid("itemGuid");
        itemInfoRespDTO.setBrandGuid("brandGuid");
        itemInfoRespDTO.setStoreGuid("storeGuid");
        itemInfoRespDTO.setParentGuid("parentGuid");
        itemInfoRespDTO.setName("itemName");
        final List<ItemInfoRespDTO> itemInfoRespDTOS = Arrays.asList(itemInfoRespDTO);
        when(mockItemRpcService.kdsItemParentMapping(Arrays.asList("value"))).thenReturn(itemInfoRespDTOS);

        // Run the test
        final List<DisplayItemRespDTO> result = displayItemServiceImplUnderTest.queryItemInfo(Arrays.asList("value"),
                displayItemRespDTOS);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryItemInfo_ItemRpcServiceReturnsNoItems() {
        // Setup
        final DisplayItemRespDTO displayItemRespDTO = new DisplayItemRespDTO();
        displayItemRespDTO.setItemGuid("itemGuid");
        displayItemRespDTO.setItemName("itemName");
        displayItemRespDTO.setSort(0);
        displayItemRespDTO.setRuleType(0);
        final List<DisplayItemRespDTO> displayItemRespDTOS = Arrays.asList(displayItemRespDTO);
        when(mockItemRpcService.kdsItemParentMapping(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        final List<DisplayItemRespDTO> result = displayItemServiceImplUnderTest.queryItemInfo(Arrays.asList("value"),
                displayItemRespDTOS);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }
}
