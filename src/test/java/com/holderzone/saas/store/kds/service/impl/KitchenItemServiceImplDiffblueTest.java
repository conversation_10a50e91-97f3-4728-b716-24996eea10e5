package com.holderzone.saas.store.kds.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.kds.req.*;
import com.holderzone.saas.store.dto.kds.resp.*;
import com.holderzone.saas.store.kds.entity.MPPage;
import com.holderzone.saas.store.kds.entity.bo.AsyncTask;
import com.holderzone.saas.store.kds.entity.domain.*;
import com.holderzone.saas.store.kds.entity.query.ItemQuery;
import com.holderzone.saas.store.kds.entity.read.KitchenItemReadDO;
import com.holderzone.saas.store.kds.handler.KitchenItemHandlerFactory;
import com.holderzone.saas.store.kds.mapper.DeviceBindItemGroupMapper;
import com.holderzone.saas.store.kds.mapper.KitchenItemMapper;
import com.holderzone.saas.store.kds.mapstruct.DisplayRuleMapstruct;
import com.holderzone.saas.store.kds.mapstruct.KitchenItemMapstruct;
import com.holderzone.saas.store.kds.service.*;
import com.holderzone.saas.store.kds.service.print.KdsInvoiceTypeEnum;
import com.holderzone.saas.store.kds.service.print.KdsPrintRecordService;
import com.holderzone.saas.store.kds.service.rpc.ItemRpcService;
import com.holderzone.saas.store.kds.service.rpc.TableRpcService;
import com.holderzone.saas.store.kds.service.rpc.TradeRpcService;
import com.holderzone.saas.store.kds.utils.PageAdapter;
import com.holderzone.saas.store.kds.utils.RedisUtils;
import org.junit.Ignore;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@ContextConfiguration(classes = {KitchenItemServiceImpl.class})
@RunWith(SpringJUnit4ClassRunner.class)
public class KitchenItemServiceImplDiffblueTest {
    @Rule
    public ExpectedException thrown = ExpectedException.none();

    @MockBean
    private DeviceConfigService deviceConfigService;

    @MockBean
    private DisplayItemService displayItemService;

    @MockBean
    private DisplayRuleMapstruct displayRuleMapstruct;

    @MockBean
    private DisplayRuleService displayRuleService;

    @MockBean
    private DisplayStoreService displayStoreService;

    @MockBean
    private DistributeAreaService distributeAreaService;

    @MockBean
    private DistributeItemService distributeItemService;

    @MockBean
    private DistributedIdService distributedIdService;

    @MockBean
    private ItemRpcService itemRpcService;

    @MockBean
    private KdsNotificationService kdsNotificationService;

    @MockBean
    private KdsPrintRecordService kdsPrintRecordService;

    @MockBean
    private KdsStatusPushService kdsStatusPushService;

    @MockBean
    private KitchenItemAttrService kitchenItemAttrService;

    @MockBean
    private KitchenItemDelayNotificationService kitchenItemDelayNotificationService;

    @MockBean
    private TableRpcService tableRpcService;

    @MockBean
    private KitchenItemMapper kitchenItemMapper;

    @MockBean
    private KitchenItemMapstruct kitchenItemMapstruct;

    @Autowired
    private KitchenItemServiceImpl kitchenItemServiceImpl;

    @MockBean
    private PrdPointItemService prdPointItemService;

    @MockBean
    private ProductionPointService productionPointService;

    @MockBean
    private QueueItemService queueItemService;

    @MockBean
    private RedisUtils redisUtils;

    /**
     * Method under test: {@link KitchenItemServiceImpl#prepare(ItemPrepareReqDTO)}
     */
    @Test
    public void testPrepare() {
        when(displayStoreService.list(Mockito.<Wrapper<DisplayStoreDO>>any())).thenReturn(new ArrayList<>());
        when(displayRuleService.list(Mockito.<Wrapper<DisplayRuleDO>>any())).thenReturn(new ArrayList<>());
        when(redisUtils.get(Mockito.<String>any())).thenReturn("Get");
        when(itemRpcService.kdsItemParentMapping(Mockito.<List<String>>any())).thenReturn(new ArrayList<>());

        TradeDineInInfoDTO tradeDineInInfoDTO = new TradeDineInInfoDTO();
        tradeDineInInfoDTO.setAreaGuid("1234");
        tradeDineInInfoDTO.setAreaName("Area Name");
        tradeDineInInfoDTO.setOrderNo("Order No");
        tradeDineInInfoDTO.setTableGuid("1234");
        tradeDineInInfoDTO.setTableName("Table Name");

        TradeSnackInfoDTO tradeSnackInfoDTO = new TradeSnackInfoDTO();
        tradeSnackInfoDTO.setMarkNo("Mark No");
        tradeSnackInfoDTO.setOrderNo("Order No");

        TradeTakeoutInfoDTO tradeTakeoutInfoDTO = new TradeTakeoutInfoDTO();
        tradeTakeoutInfoDTO.setPlatformName("Platform Name");
        tradeTakeoutInfoDTO.setPlatformNumber("42");
        tradeTakeoutInfoDTO.setPlatformSerialNo("Platform Serial No");

        ItemPrepareReqDTO itemPrepareReqDTO = new ItemPrepareReqDTO();
        itemPrepareReqDTO.setAccount("3");
        itemPrepareReqDTO.setAccountName("Dr Jane Doe");
        itemPrepareReqDTO.setActuallyPayFee(new BigDecimal("2.3"));
        itemPrepareReqDTO.setDeviceId("42");
        itemPrepareReqDTO.setDeviceType(1);
        itemPrepareReqDTO.setEnterpriseGuid("1234");
        itemPrepareReqDTO.setEnterpriseName("Enterprise Name");
        itemPrepareReqDTO.setInvoiceCode("Invoice Code");
        itemPrepareReqDTO.setInvoicePhone("**********");
        itemPrepareReqDTO.setIsInvoiceCode(1);
        itemPrepareReqDTO.setItems(new ArrayList<>());
        itemPrepareReqDTO.setLatitude("Latitude");
        itemPrepareReqDTO.setLongitude("Longitude");
        itemPrepareReqDTO.setOperSubjectGuid("1234");
        itemPrepareReqDTO.setOrderGuid("1234");
        itemPrepareReqDTO.setOrderRemark("Order Remark");
        itemPrepareReqDTO.setRequestTimestamp(1L);
        itemPrepareReqDTO.setStoreGuid("1234");
        itemPrepareReqDTO.setStoreName("Store Name");
        itemPrepareReqDTO.setTradeDineInInfoDTO(tradeDineInInfoDTO);
        itemPrepareReqDTO.setTradeMode(1);
        itemPrepareReqDTO.setTradeSnackInfoDTO(tradeSnackInfoDTO);
        itemPrepareReqDTO.setTradeTakeoutInfoDTO(tradeTakeoutInfoDTO);
        itemPrepareReqDTO.setUserGuid("1234");
        itemPrepareReqDTO.setUserName("janedoe");
        thrown.expect(IllegalArgumentException.class);
        kitchenItemServiceImpl.prepare(itemPrepareReqDTO);
        verify(displayRuleService).list(Mockito.<Wrapper<DisplayRuleDO>>any());
        verify(displayStoreService).list(Mockito.<Wrapper<DisplayStoreDO>>any());
        verify(itemRpcService).kdsItemParentMapping(Mockito.<List<String>>any());
        verify(redisUtils).get(Mockito.<String>any());
    }

    /**
     * Method under test: {@link KitchenItemServiceImpl#prepare(ItemPrepareReqDTO)}
     */
    @Test
    public void testPrepare2() {
        when(displayStoreService.list(Mockito.<Wrapper<DisplayStoreDO>>any())).thenReturn(new ArrayList<>());
        when(displayRuleService.list(Mockito.<Wrapper<DisplayRuleDO>>any())).thenReturn(new ArrayList<>());
        when(redisUtils.get(Mockito.<String>any())).thenReturn("Get");
        when(itemRpcService.kdsItemParentMapping(Mockito.<List<String>>any()))
                .thenThrow(new IllegalArgumentException("快餐信息不得为空"));

        TradeDineInInfoDTO tradeDineInInfoDTO = new TradeDineInInfoDTO();
        tradeDineInInfoDTO.setAreaGuid("1234");
        tradeDineInInfoDTO.setAreaName("Area Name");
        tradeDineInInfoDTO.setOrderNo("Order No");
        tradeDineInInfoDTO.setTableGuid("1234");
        tradeDineInInfoDTO.setTableName("Table Name");

        TradeSnackInfoDTO tradeSnackInfoDTO = new TradeSnackInfoDTO();
        tradeSnackInfoDTO.setMarkNo("Mark No");
        tradeSnackInfoDTO.setOrderNo("Order No");

        TradeTakeoutInfoDTO tradeTakeoutInfoDTO = new TradeTakeoutInfoDTO();
        tradeTakeoutInfoDTO.setPlatformName("Platform Name");
        tradeTakeoutInfoDTO.setPlatformNumber("42");
        tradeTakeoutInfoDTO.setPlatformSerialNo("Platform Serial No");

        ItemPrepareReqDTO itemPrepareReqDTO = new ItemPrepareReqDTO();
        itemPrepareReqDTO.setAccount("3");
        itemPrepareReqDTO.setAccountName("Dr Jane Doe");
        itemPrepareReqDTO.setActuallyPayFee(new BigDecimal("2.3"));
        itemPrepareReqDTO.setDeviceId("42");
        itemPrepareReqDTO.setDeviceType(1);
        itemPrepareReqDTO.setEnterpriseGuid("1234");
        itemPrepareReqDTO.setEnterpriseName("Enterprise Name");
        itemPrepareReqDTO.setInvoiceCode("Invoice Code");
        itemPrepareReqDTO.setInvoicePhone("**********");
        itemPrepareReqDTO.setIsInvoiceCode(1);
        itemPrepareReqDTO.setItems(new ArrayList<>());
        itemPrepareReqDTO.setLatitude("Latitude");
        itemPrepareReqDTO.setLongitude("Longitude");
        itemPrepareReqDTO.setOperSubjectGuid("1234");
        itemPrepareReqDTO.setOrderGuid("1234");
        itemPrepareReqDTO.setOrderRemark("Order Remark");
        itemPrepareReqDTO.setRequestTimestamp(1L);
        itemPrepareReqDTO.setStoreGuid("1234");
        itemPrepareReqDTO.setStoreName("Store Name");
        itemPrepareReqDTO.setTradeDineInInfoDTO(tradeDineInInfoDTO);
        itemPrepareReqDTO.setTradeMode(1);
        itemPrepareReqDTO.setTradeSnackInfoDTO(tradeSnackInfoDTO);
        itemPrepareReqDTO.setTradeTakeoutInfoDTO(tradeTakeoutInfoDTO);
        itemPrepareReqDTO.setUserGuid("1234");
        itemPrepareReqDTO.setUserName("janedoe");
        thrown.expect(IllegalArgumentException.class);
        kitchenItemServiceImpl.prepare(itemPrepareReqDTO);
        verify(displayRuleService).list(Mockito.<Wrapper<DisplayRuleDO>>any());
        verify(displayStoreService).list(Mockito.<Wrapper<DisplayStoreDO>>any());
        verify(itemRpcService).kdsItemParentMapping(Mockito.<List<String>>any());
        verify(redisUtils).get(Mockito.<String>any());
    }

    /**
     * Method under test: {@link KitchenItemServiceImpl#prepare(ItemPrepareReqDTO)}
     */
    @Test
    public void testPrepare3() {
        DisplayStoreDO displayStoreDO = new DisplayStoreDO();
        displayStoreDO.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        displayStoreDO.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        displayStoreDO.setGuid("1234");
        displayStoreDO.setId(1L);
        displayStoreDO.setIsDelete(1);
        displayStoreDO.setRuleGuid("1234");
        displayStoreDO.setRuleType(1);
        displayStoreDO.setStoreGuid("1234");

        ArrayList<DisplayStoreDO> displayStoreDOList = new ArrayList<>();
        displayStoreDOList.add(displayStoreDO);
        when(displayStoreService.list(Mockito.<Wrapper<DisplayStoreDO>>any())).thenReturn(displayStoreDOList);
        when(displayRuleService.list(Mockito.<Wrapper<DisplayRuleDO>>any())).thenReturn(new ArrayList<>());
        when(redisUtils.get(Mockito.<String>any())).thenReturn("Get");
        when(itemRpcService.kdsItemParentMapping(Mockito.<List<String>>any())).thenReturn(new ArrayList<>());

        TradeDineInInfoDTO tradeDineInInfoDTO = new TradeDineInInfoDTO();
        tradeDineInInfoDTO.setAreaGuid("1234");
        tradeDineInInfoDTO.setAreaName("Area Name");
        tradeDineInInfoDTO.setOrderNo("Order No");
        tradeDineInInfoDTO.setTableGuid("1234");
        tradeDineInInfoDTO.setTableName("Table Name");

        TradeSnackInfoDTO tradeSnackInfoDTO = new TradeSnackInfoDTO();
        tradeSnackInfoDTO.setMarkNo("Mark No");
        tradeSnackInfoDTO.setOrderNo("Order No");

        TradeTakeoutInfoDTO tradeTakeoutInfoDTO = new TradeTakeoutInfoDTO();
        tradeTakeoutInfoDTO.setPlatformName("Platform Name");
        tradeTakeoutInfoDTO.setPlatformNumber("42");
        tradeTakeoutInfoDTO.setPlatformSerialNo("Platform Serial No");

        ItemPrepareReqDTO itemPrepareReqDTO = new ItemPrepareReqDTO();
        itemPrepareReqDTO.setAccount("3");
        itemPrepareReqDTO.setAccountName("Dr Jane Doe");
        itemPrepareReqDTO.setActuallyPayFee(new BigDecimal("2.3"));
        itemPrepareReqDTO.setDeviceId("42");
        itemPrepareReqDTO.setDeviceType(1);
        itemPrepareReqDTO.setEnterpriseGuid("1234");
        itemPrepareReqDTO.setEnterpriseName("Enterprise Name");
        itemPrepareReqDTO.setInvoiceCode("Invoice Code");
        itemPrepareReqDTO.setInvoicePhone("**********");
        itemPrepareReqDTO.setIsInvoiceCode(1);
        itemPrepareReqDTO.setItems(new ArrayList<>());
        itemPrepareReqDTO.setLatitude("Latitude");
        itemPrepareReqDTO.setLongitude("Longitude");
        itemPrepareReqDTO.setOperSubjectGuid("1234");
        itemPrepareReqDTO.setOrderGuid("1234");
        itemPrepareReqDTO.setOrderRemark("Order Remark");
        itemPrepareReqDTO.setRequestTimestamp(1L);
        itemPrepareReqDTO.setStoreGuid("1234");
        itemPrepareReqDTO.setStoreName("Store Name");
        itemPrepareReqDTO.setTradeDineInInfoDTO(tradeDineInInfoDTO);
        itemPrepareReqDTO.setTradeMode(1);
        itemPrepareReqDTO.setTradeSnackInfoDTO(tradeSnackInfoDTO);
        itemPrepareReqDTO.setTradeTakeoutInfoDTO(tradeTakeoutInfoDTO);
        itemPrepareReqDTO.setUserGuid("1234");
        itemPrepareReqDTO.setUserName("janedoe");
        thrown.expect(IllegalArgumentException.class);
        kitchenItemServiceImpl.prepare(itemPrepareReqDTO);
        verify(displayStoreService).list(Mockito.<Wrapper<DisplayStoreDO>>any());
        verify(displayRuleService, atLeast(1)).list(Mockito.<Wrapper<DisplayRuleDO>>any());
        verify(itemRpcService).kdsItemParentMapping(Mockito.<List<String>>any());
        verify(redisUtils).get(Mockito.<String>any());
    }

    /**
     * Method under test: {@link KitchenItemServiceImpl#prepare(ItemPrepareReqDTO)}
     */
    @Test
    public void testPrepare4() {
        when(displayStoreService.list(Mockito.<Wrapper<DisplayStoreDO>>any())).thenReturn(new ArrayList<>());

        DisplayRuleDO displayRuleDO = new DisplayRuleDO();
        displayRuleDO.setBatch(1);
        displayRuleDO.setBrandGuid("1234");
        displayRuleDO.setDelayTime(1);
        displayRuleDO.setDisplayState(1);
        displayRuleDO.setEffectiveState(1);
        displayRuleDO.setEffectiveTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        displayRuleDO.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        displayRuleDO.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        displayRuleDO.setGuid("1234");
        displayRuleDO.setId(1L);
        displayRuleDO.setIsAllStore(true);
        displayRuleDO.setIsDelete(1);
        displayRuleDO.setRuleType(1);

        ArrayList<DisplayRuleDO> displayRuleDOList = new ArrayList<>();
        displayRuleDOList.add(displayRuleDO);
        when(displayRuleService.list(Mockito.<Wrapper<DisplayRuleDO>>any())).thenReturn(displayRuleDOList);
        when(displayItemService.list(Mockito.<Wrapper<DisplayItemDO>>any())).thenReturn(new ArrayList<>());
        when(redisUtils.get(Mockito.<String>any())).thenReturn("Get");
        when(itemRpcService.kdsItemParentMapping(Mockito.<List<String>>any())).thenReturn(new ArrayList<>());

        TradeDineInInfoDTO tradeDineInInfoDTO = new TradeDineInInfoDTO();
        tradeDineInInfoDTO.setAreaGuid("1234");
        tradeDineInInfoDTO.setAreaName("Area Name");
        tradeDineInInfoDTO.setOrderNo("Order No");
        tradeDineInInfoDTO.setTableGuid("1234");
        tradeDineInInfoDTO.setTableName("Table Name");

        TradeSnackInfoDTO tradeSnackInfoDTO = new TradeSnackInfoDTO();
        tradeSnackInfoDTO.setMarkNo("Mark No");
        tradeSnackInfoDTO.setOrderNo("Order No");

        TradeTakeoutInfoDTO tradeTakeoutInfoDTO = new TradeTakeoutInfoDTO();
        tradeTakeoutInfoDTO.setPlatformName("Platform Name");
        tradeTakeoutInfoDTO.setPlatformNumber("42");
        tradeTakeoutInfoDTO.setPlatformSerialNo("Platform Serial No");

        ItemPrepareReqDTO itemPrepareReqDTO = new ItemPrepareReqDTO();
        itemPrepareReqDTO.setAccount("3");
        itemPrepareReqDTO.setAccountName("Dr Jane Doe");
        itemPrepareReqDTO.setActuallyPayFee(new BigDecimal("2.3"));
        itemPrepareReqDTO.setDeviceId("42");
        itemPrepareReqDTO.setDeviceType(1);
        itemPrepareReqDTO.setEnterpriseGuid("1234");
        itemPrepareReqDTO.setEnterpriseName("Enterprise Name");
        itemPrepareReqDTO.setInvoiceCode("Invoice Code");
        itemPrepareReqDTO.setInvoicePhone("**********");
        itemPrepareReqDTO.setIsInvoiceCode(1);
        itemPrepareReqDTO.setItems(new ArrayList<>());
        itemPrepareReqDTO.setLatitude("Latitude");
        itemPrepareReqDTO.setLongitude("Longitude");
        itemPrepareReqDTO.setOperSubjectGuid("1234");
        itemPrepareReqDTO.setOrderGuid("1234");
        itemPrepareReqDTO.setOrderRemark("Order Remark");
        itemPrepareReqDTO.setRequestTimestamp(1L);
        itemPrepareReqDTO.setStoreGuid("1234");
        itemPrepareReqDTO.setStoreName("Store Name");
        itemPrepareReqDTO.setTradeDineInInfoDTO(tradeDineInInfoDTO);
        itemPrepareReqDTO.setTradeMode(1);
        itemPrepareReqDTO.setTradeSnackInfoDTO(tradeSnackInfoDTO);
        itemPrepareReqDTO.setTradeTakeoutInfoDTO(tradeTakeoutInfoDTO);
        itemPrepareReqDTO.setUserGuid("1234");
        itemPrepareReqDTO.setUserName("janedoe");
        thrown.expect(IllegalArgumentException.class);
        kitchenItemServiceImpl.prepare(itemPrepareReqDTO);
        verify(displayItemService).list(Mockito.<Wrapper<DisplayItemDO>>any());
        verify(displayRuleService).list(Mockito.<Wrapper<DisplayRuleDO>>any());
        verify(displayStoreService).list(Mockito.<Wrapper<DisplayStoreDO>>any());
        verify(itemRpcService).kdsItemParentMapping(Mockito.<List<String>>any());
        verify(redisUtils).get(Mockito.<String>any());
    }

    /**
     * Method under test: {@link KitchenItemServiceImpl#prepare(ItemPrepareReqDTO)}
     */
    @Test
    public void testPrepare5() {
        when(displayStoreService.list(Mockito.<Wrapper<DisplayStoreDO>>any())).thenReturn(new ArrayList<>());

        DisplayRuleDO displayRuleDO = new DisplayRuleDO();
        displayRuleDO.setBatch(1);
        displayRuleDO.setBrandGuid("1234");
        displayRuleDO.setDelayTime(1);
        displayRuleDO.setDisplayState(1);
        displayRuleDO.setEffectiveState(1);
        displayRuleDO.setEffectiveTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        displayRuleDO.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        displayRuleDO.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        displayRuleDO.setGuid("1234");
        displayRuleDO.setId(1L);
        displayRuleDO.setIsAllStore(true);
        displayRuleDO.setIsDelete(1);
        displayRuleDO.setRuleType(1);

        ArrayList<DisplayRuleDO> displayRuleDOList = new ArrayList<>();
        displayRuleDOList.add(displayRuleDO);
        when(displayRuleService.list(Mockito.<Wrapper<DisplayRuleDO>>any())).thenReturn(displayRuleDOList);

        DisplayItemDO displayItemDO = new DisplayItemDO();
        displayItemDO.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        displayItemDO.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        displayItemDO.setGuid("1234");
        displayItemDO.setId(1L);
        displayItemDO.setIsDelete(1);
        displayItemDO.setItemGuid("1234");
        displayItemDO.setRuleGuid("1234");
        displayItemDO.setRuleType(1);
        displayItemDO.setSort(1);

        ArrayList<DisplayItemDO> displayItemDOList = new ArrayList<>();
        displayItemDOList.add(displayItemDO);
        when(displayItemService.list(Mockito.<Wrapper<DisplayItemDO>>any())).thenReturn(displayItemDOList);
        when(displayRuleMapstruct.toItemRespList(Mockito.<List<DisplayItemDO>>any())).thenReturn(new ArrayList<>());
        when(displayRuleMapstruct.toStoreRespList(Mockito.<List<DisplayStoreDO>>any())).thenReturn(new ArrayList<>());
        doNothing().when(redisUtils).set(Mockito.<String>any(), Mockito.<Object>any(), anyInt());
        when(redisUtils.get(Mockito.<String>any())).thenReturn("Get");
        when(itemRpcService.kdsItemParentMapping(Mockito.<List<String>>any())).thenReturn(new ArrayList<>());

        TradeDineInInfoDTO tradeDineInInfoDTO = new TradeDineInInfoDTO();
        tradeDineInInfoDTO.setAreaGuid("1234");
        tradeDineInInfoDTO.setAreaName("Area Name");
        tradeDineInInfoDTO.setOrderNo("Order No");
        tradeDineInInfoDTO.setTableGuid("1234");
        tradeDineInInfoDTO.setTableName("Table Name");

        TradeSnackInfoDTO tradeSnackInfoDTO = new TradeSnackInfoDTO();
        tradeSnackInfoDTO.setMarkNo("Mark No");
        tradeSnackInfoDTO.setOrderNo("Order No");

        TradeTakeoutInfoDTO tradeTakeoutInfoDTO = new TradeTakeoutInfoDTO();
        tradeTakeoutInfoDTO.setPlatformName("Platform Name");
        tradeTakeoutInfoDTO.setPlatformNumber("42");
        tradeTakeoutInfoDTO.setPlatformSerialNo("Platform Serial No");

        ItemPrepareReqDTO itemPrepareReqDTO = new ItemPrepareReqDTO();
        itemPrepareReqDTO.setAccount("3");
        itemPrepareReqDTO.setAccountName("Dr Jane Doe");
        itemPrepareReqDTO.setActuallyPayFee(new BigDecimal("2.3"));
        itemPrepareReqDTO.setDeviceId("42");
        itemPrepareReqDTO.setDeviceType(1);
        itemPrepareReqDTO.setEnterpriseGuid("1234");
        itemPrepareReqDTO.setEnterpriseName("Enterprise Name");
        itemPrepareReqDTO.setInvoiceCode("Invoice Code");
        itemPrepareReqDTO.setInvoicePhone("**********");
        itemPrepareReqDTO.setIsInvoiceCode(1);
        itemPrepareReqDTO.setItems(new ArrayList<>());
        itemPrepareReqDTO.setLatitude("Latitude");
        itemPrepareReqDTO.setLongitude("Longitude");
        itemPrepareReqDTO.setOperSubjectGuid("1234");
        itemPrepareReqDTO.setOrderGuid("1234");
        itemPrepareReqDTO.setOrderRemark("Order Remark");
        itemPrepareReqDTO.setRequestTimestamp(1L);
        itemPrepareReqDTO.setStoreGuid("1234");
        itemPrepareReqDTO.setStoreName("Store Name");
        itemPrepareReqDTO.setTradeDineInInfoDTO(tradeDineInInfoDTO);
        itemPrepareReqDTO.setTradeMode(1);
        itemPrepareReqDTO.setTradeSnackInfoDTO(tradeSnackInfoDTO);
        itemPrepareReqDTO.setTradeTakeoutInfoDTO(tradeTakeoutInfoDTO);
        itemPrepareReqDTO.setUserGuid("1234");
        itemPrepareReqDTO.setUserName("janedoe");
        thrown.expect(IllegalArgumentException.class);
        kitchenItemServiceImpl.prepare(itemPrepareReqDTO);
        verify(displayItemService).list(Mockito.<Wrapper<DisplayItemDO>>any());
        verify(displayRuleService).list(Mockito.<Wrapper<DisplayRuleDO>>any());
        verify(displayStoreService).list(Mockito.<Wrapper<DisplayStoreDO>>any());
        verify(displayRuleMapstruct).toItemRespList(Mockito.<List<DisplayItemDO>>any());
        verify(displayRuleMapstruct).toStoreRespList(Mockito.<List<DisplayStoreDO>>any());
        verify(itemRpcService).kdsItemParentMapping(Mockito.<List<String>>any());
        verify(redisUtils).get(Mockito.<String>any());
        verify(redisUtils).set(Mockito.<String>any(), Mockito.<Object>any(), anyInt());
    }

    /**
     * Method under test: {@link KitchenItemServiceImpl#prepare(ItemPrepareReqDTO)}
     */
    @Test
    public void testPrepare6() {
        when(displayStoreService.list(Mockito.<Wrapper<DisplayStoreDO>>any())).thenReturn(new ArrayList<>());

        DisplayRuleDO displayRuleDO = new DisplayRuleDO();
        displayRuleDO.setBatch(1);
        displayRuleDO.setBrandGuid("1234");
        displayRuleDO.setDelayTime(1);
        displayRuleDO.setDisplayState(1);
        displayRuleDO.setEffectiveState(1);
        displayRuleDO.setEffectiveTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        displayRuleDO.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        displayRuleDO.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        displayRuleDO.setGuid("1234");
        displayRuleDO.setId(1L);
        displayRuleDO.setIsAllStore(true);
        displayRuleDO.setIsDelete(1);
        displayRuleDO.setRuleType(1);

        ArrayList<DisplayRuleDO> displayRuleDOList = new ArrayList<>();
        displayRuleDOList.add(displayRuleDO);
        when(displayRuleService.list(Mockito.<Wrapper<DisplayRuleDO>>any())).thenReturn(displayRuleDOList);

        DisplayItemDO displayItemDO = new DisplayItemDO();
        displayItemDO.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        displayItemDO.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        displayItemDO.setGuid("1234");
        displayItemDO.setId(1L);
        displayItemDO.setIsDelete(1);
        displayItemDO.setItemGuid("1234");
        displayItemDO.setRuleGuid("1234");
        displayItemDO.setRuleType(1);
        displayItemDO.setSort(1);

        ArrayList<DisplayItemDO> displayItemDOList = new ArrayList<>();
        displayItemDOList.add(displayItemDO);
        when(displayItemService.list(Mockito.<Wrapper<DisplayItemDO>>any())).thenReturn(displayItemDOList);
        when(displayRuleMapstruct.toItemRespList(Mockito.<List<DisplayItemDO>>any())).thenReturn(new ArrayList<>());
        when(displayRuleMapstruct.toStoreRespList(Mockito.<List<DisplayStoreDO>>any())).thenReturn(new ArrayList<>());
        doThrow(new IllegalArgumentException("快餐信息不得为空")).when(redisUtils)
                .set(Mockito.<String>any(), Mockito.<Object>any(), anyInt());
        when(redisUtils.get(Mockito.<String>any())).thenReturn("Get");

        TradeDineInInfoDTO tradeDineInInfoDTO = new TradeDineInInfoDTO();
        tradeDineInInfoDTO.setAreaGuid("1234");
        tradeDineInInfoDTO.setAreaName("Area Name");
        tradeDineInInfoDTO.setOrderNo("Order No");
        tradeDineInInfoDTO.setTableGuid("1234");
        tradeDineInInfoDTO.setTableName("Table Name");

        TradeSnackInfoDTO tradeSnackInfoDTO = new TradeSnackInfoDTO();
        tradeSnackInfoDTO.setMarkNo("Mark No");
        tradeSnackInfoDTO.setOrderNo("Order No");

        TradeTakeoutInfoDTO tradeTakeoutInfoDTO = new TradeTakeoutInfoDTO();
        tradeTakeoutInfoDTO.setPlatformName("Platform Name");
        tradeTakeoutInfoDTO.setPlatformNumber("42");
        tradeTakeoutInfoDTO.setPlatformSerialNo("Platform Serial No");

        ItemPrepareReqDTO itemPrepareReqDTO = new ItemPrepareReqDTO();
        itemPrepareReqDTO.setAccount("3");
        itemPrepareReqDTO.setAccountName("Dr Jane Doe");
        itemPrepareReqDTO.setActuallyPayFee(new BigDecimal("2.3"));
        itemPrepareReqDTO.setDeviceId("42");
        itemPrepareReqDTO.setDeviceType(1);
        itemPrepareReqDTO.setEnterpriseGuid("1234");
        itemPrepareReqDTO.setEnterpriseName("Enterprise Name");
        itemPrepareReqDTO.setInvoiceCode("Invoice Code");
        itemPrepareReqDTO.setInvoicePhone("**********");
        itemPrepareReqDTO.setIsInvoiceCode(1);
        itemPrepareReqDTO.setItems(new ArrayList<>());
        itemPrepareReqDTO.setLatitude("Latitude");
        itemPrepareReqDTO.setLongitude("Longitude");
        itemPrepareReqDTO.setOperSubjectGuid("1234");
        itemPrepareReqDTO.setOrderGuid("1234");
        itemPrepareReqDTO.setOrderRemark("Order Remark");
        itemPrepareReqDTO.setRequestTimestamp(1L);
        itemPrepareReqDTO.setStoreGuid("1234");
        itemPrepareReqDTO.setStoreName("Store Name");
        itemPrepareReqDTO.setTradeDineInInfoDTO(tradeDineInInfoDTO);
        itemPrepareReqDTO.setTradeMode(1);
        itemPrepareReqDTO.setTradeSnackInfoDTO(tradeSnackInfoDTO);
        itemPrepareReqDTO.setTradeTakeoutInfoDTO(tradeTakeoutInfoDTO);
        itemPrepareReqDTO.setUserGuid("1234");
        itemPrepareReqDTO.setUserName("janedoe");
        thrown.expect(IllegalArgumentException.class);
        kitchenItemServiceImpl.prepare(itemPrepareReqDTO);
        verify(displayItemService).list(Mockito.<Wrapper<DisplayItemDO>>any());
        verify(displayRuleService).list(Mockito.<Wrapper<DisplayRuleDO>>any());
        verify(displayStoreService).list(Mockito.<Wrapper<DisplayStoreDO>>any());
        verify(displayRuleMapstruct).toItemRespList(Mockito.<List<DisplayItemDO>>any());
        verify(displayRuleMapstruct).toStoreRespList(Mockito.<List<DisplayStoreDO>>any());
        verify(redisUtils).get(Mockito.<String>any());
        verify(redisUtils).set(Mockito.<String>any(), Mockito.<Object>any(), anyInt());
    }

    /**
     * Method under test: {@link KitchenItemServiceImpl#prepare(ItemPrepareReqDTO)}
     */
    @Test
    public void testPrepare7() {
        DisplayStoreDO displayStoreDO = new DisplayStoreDO();
        displayStoreDO.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        displayStoreDO.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        displayStoreDO.setGuid("1234");
        displayStoreDO.setId(1L);
        displayStoreDO.setIsDelete(1);
        displayStoreDO.setRuleGuid("1234");
        displayStoreDO.setRuleType(1);
        displayStoreDO.setStoreGuid("1234");

        ArrayList<DisplayStoreDO> displayStoreDOList = new ArrayList<>();
        displayStoreDOList.add(displayStoreDO);
        when(displayStoreService.list(Mockito.<Wrapper<DisplayStoreDO>>any())).thenReturn(displayStoreDOList);

        DisplayRuleDO displayRuleDO = new DisplayRuleDO();
        displayRuleDO.setBatch(1);
        displayRuleDO.setBrandGuid("1234");
        displayRuleDO.setDelayTime(1);
        displayRuleDO.setDisplayState(1);
        displayRuleDO.setEffectiveState(1);
        displayRuleDO.setEffectiveTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        displayRuleDO.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        displayRuleDO.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        displayRuleDO.setGuid("1234");
        displayRuleDO.setId(1L);
        displayRuleDO.setIsAllStore(true);
        displayRuleDO.setIsDelete(1);
        displayRuleDO.setRuleType(1);

        ArrayList<DisplayRuleDO> displayRuleDOList = new ArrayList<>();
        displayRuleDOList.add(displayRuleDO);
        when(displayRuleService.list(Mockito.<Wrapper<DisplayRuleDO>>any())).thenReturn(displayRuleDOList);

        DisplayItemDO displayItemDO = new DisplayItemDO();
        displayItemDO.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        displayItemDO.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        displayItemDO.setGuid("1234");
        displayItemDO.setId(1L);
        displayItemDO.setIsDelete(1);
        displayItemDO.setItemGuid("1234");
        displayItemDO.setRuleGuid("1234");
        displayItemDO.setRuleType(1);
        displayItemDO.setSort(1);

        ArrayList<DisplayItemDO> displayItemDOList = new ArrayList<>();
        displayItemDOList.add(displayItemDO);
        when(displayItemService.list(Mockito.<Wrapper<DisplayItemDO>>any())).thenReturn(displayItemDOList);
        when(displayRuleMapstruct.toItemRespList(Mockito.<List<DisplayItemDO>>any())).thenReturn(new ArrayList<>());
        when(displayRuleMapstruct.toStoreRespList(Mockito.<List<DisplayStoreDO>>any())).thenReturn(new ArrayList<>());
        doNothing().when(redisUtils).set(Mockito.<String>any(), Mockito.<Object>any(), anyInt());
        when(redisUtils.get(Mockito.<String>any())).thenReturn("Get");
        when(itemRpcService.kdsItemParentMapping(Mockito.<List<String>>any())).thenReturn(new ArrayList<>());

        TradeDineInInfoDTO tradeDineInInfoDTO = new TradeDineInInfoDTO();
        tradeDineInInfoDTO.setAreaGuid("1234");
        tradeDineInInfoDTO.setAreaName("Area Name");
        tradeDineInInfoDTO.setOrderNo("Order No");
        tradeDineInInfoDTO.setTableGuid("1234");
        tradeDineInInfoDTO.setTableName("Table Name");

        TradeSnackInfoDTO tradeSnackInfoDTO = new TradeSnackInfoDTO();
        tradeSnackInfoDTO.setMarkNo("Mark No");
        tradeSnackInfoDTO.setOrderNo("Order No");

        TradeTakeoutInfoDTO tradeTakeoutInfoDTO = new TradeTakeoutInfoDTO();
        tradeTakeoutInfoDTO.setPlatformName("Platform Name");
        tradeTakeoutInfoDTO.setPlatformNumber("42");
        tradeTakeoutInfoDTO.setPlatformSerialNo("Platform Serial No");

        ItemPrepareReqDTO itemPrepareReqDTO = new ItemPrepareReqDTO();
        itemPrepareReqDTO.setAccount("3");
        itemPrepareReqDTO.setAccountName("Dr Jane Doe");
        itemPrepareReqDTO.setActuallyPayFee(new BigDecimal("2.3"));
        itemPrepareReqDTO.setDeviceId("42");
        itemPrepareReqDTO.setDeviceType(1);
        itemPrepareReqDTO.setEnterpriseGuid("1234");
        itemPrepareReqDTO.setEnterpriseName("Enterprise Name");
        itemPrepareReqDTO.setInvoiceCode("Invoice Code");
        itemPrepareReqDTO.setInvoicePhone("**********");
        itemPrepareReqDTO.setIsInvoiceCode(1);
        itemPrepareReqDTO.setItems(new ArrayList<>());
        itemPrepareReqDTO.setLatitude("Latitude");
        itemPrepareReqDTO.setLongitude("Longitude");
        itemPrepareReqDTO.setOperSubjectGuid("1234");
        itemPrepareReqDTO.setOrderGuid("1234");
        itemPrepareReqDTO.setOrderRemark("Order Remark");
        itemPrepareReqDTO.setRequestTimestamp(1L);
        itemPrepareReqDTO.setStoreGuid("1234");
        itemPrepareReqDTO.setStoreName("Store Name");
        itemPrepareReqDTO.setTradeDineInInfoDTO(tradeDineInInfoDTO);
        itemPrepareReqDTO.setTradeMode(1);
        itemPrepareReqDTO.setTradeSnackInfoDTO(tradeSnackInfoDTO);
        itemPrepareReqDTO.setTradeTakeoutInfoDTO(tradeTakeoutInfoDTO);
        itemPrepareReqDTO.setUserGuid("1234");
        itemPrepareReqDTO.setUserName("janedoe");
        thrown.expect(IllegalArgumentException.class);
        kitchenItemServiceImpl.prepare(itemPrepareReqDTO);
        verify(displayItemService).list(Mockito.<Wrapper<DisplayItemDO>>any());
        verify(displayStoreService).list(Mockito.<Wrapper<DisplayStoreDO>>any());
        verify(displayRuleService, atLeast(1)).list(Mockito.<Wrapper<DisplayRuleDO>>any());
        verify(displayRuleMapstruct, atLeast(1)).toItemRespList(Mockito.<List<DisplayItemDO>>any());
        verify(displayRuleMapstruct, atLeast(1)).toStoreRespList(Mockito.<List<DisplayStoreDO>>any());
        verify(itemRpcService).kdsItemParentMapping(Mockito.<List<String>>any());
        verify(redisUtils).get(Mockito.<String>any());
        verify(redisUtils).set(Mockito.<String>any(), Mockito.<Object>any(), anyInt());
    }

    /**
     * Method under test: {@link KitchenItemServiceImpl#changes(ItemChangesReqDTO)}
     */
    @Test
    public void testChanges() {
        when(itemRpcService.findParentSkus(Mockito.<List<String>>any())).thenThrow(new IllegalArgumentException("foo"));

        KdsItemDTO changesKdsItem = new KdsItemDTO();
        changesKdsItem.setAttrGroup(new ArrayList<>());
        changesKdsItem.setCurrentCount(new BigDecimal("2.3"));
        changesKdsItem.setIsWeight(true);
        changesKdsItem.setItemGuid("1234");
        changesKdsItem.setItemName("Item Name");
        changesKdsItem.setItemRemark("Item Remark");
        changesKdsItem.setItemState(42);
        changesKdsItem.setOrderItemGuid("1234");
        changesKdsItem.setOrderRemark("Order Remark");
        changesKdsItem.setSkuCode("Sku Code");
        changesKdsItem.setSkuGuid("1234");
        changesKdsItem.setSkuName("Sku Name");
        changesKdsItem.setSkuUnit("Sku Unit");

        KdsItemDTO originalKdsItem = new KdsItemDTO();
        originalKdsItem.setAttrGroup(new ArrayList<>());
        originalKdsItem.setCurrentCount(new BigDecimal("2.3"));
        originalKdsItem.setIsWeight(true);
        originalKdsItem.setItemGuid("1234");
        originalKdsItem.setItemName("Item Name");
        originalKdsItem.setItemRemark("Item Remark");
        originalKdsItem.setItemState(42);
        originalKdsItem.setOrderItemGuid("1234");
        originalKdsItem.setOrderRemark("Order Remark");
        originalKdsItem.setSkuCode("Sku Code");
        originalKdsItem.setSkuGuid("1234");
        originalKdsItem.setSkuName("Sku Name");
        originalKdsItem.setSkuUnit("Sku Unit");

        ItemChangesReqDTO itemChangesReqDTO = new ItemChangesReqDTO();
        itemChangesReqDTO.setAccount("3");
        itemChangesReqDTO.setAccountName("Dr Jane Doe");
        itemChangesReqDTO.setActuallyPayFee(new BigDecimal("2.3"));
        itemChangesReqDTO.setAreaGuid("1234");
        itemChangesReqDTO.setChangesKdsItem(changesKdsItem);
        itemChangesReqDTO.setCreateStaffName("Create Staff Name");
        itemChangesReqDTO.setDeviceId("42");
        itemChangesReqDTO.setDeviceType(1);
        itemChangesReqDTO.setDiningTableName("Dining Table Name");
        itemChangesReqDTO.setEnterpriseGuid("1234");
        itemChangesReqDTO.setEnterpriseName("Enterprise Name");
        itemChangesReqDTO.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        itemChangesReqDTO.setInvoiceCode("Invoice Code");
        itemChangesReqDTO.setInvoicePhone("**********");
        itemChangesReqDTO.setIsInvoiceCode(1);
        itemChangesReqDTO.setLatitude("Latitude");
        itemChangesReqDTO.setLongitude("Longitude");
        itemChangesReqDTO.setOperSubjectGuid("1234");
        itemChangesReqDTO.setOrderGuid("1234");
        itemChangesReqDTO.setOrderNo("Order No");
        itemChangesReqDTO.setOriginalKdsItem(originalKdsItem);
        itemChangesReqDTO.setRequestTimestamp(1L);
        itemChangesReqDTO.setStoreGuid("1234");
        itemChangesReqDTO.setStoreName("Store Name");
        itemChangesReqDTO.setTradeMode(1);
        itemChangesReqDTO.setUserGuid("1234");
        itemChangesReqDTO.setUserName("janedoe");
        thrown.expect(IllegalArgumentException.class);
        kitchenItemServiceImpl.changes(itemChangesReqDTO);
        verify(itemRpcService).findParentSkus(Mockito.<List<String>>any());
    }

    /**
     * Method under test: {@link KitchenItemServiceImpl#changes(ItemChangesReqDTO)}
     */
    @Test
    public void testChanges2() {
        KitchenItemDO kitchenItemDO = new KitchenItemDO();
        kitchenItemDO.setAreaGuid("1234");
        kitchenItemDO.setBatch(2);
        kitchenItemDO.setCallUpTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setCancelDstStaffGuid("1234");
        kitchenItemDO.setCancelDstStaffName("Cancel Dst Staff Name");
        kitchenItemDO.setCancelDstTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setCompleteStaffGuid("1234");
        kitchenItemDO.setCompleteStaffName("Complete Staff Name");
        kitchenItemDO.setCompleteTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setCookStaffGuid("1234");
        kitchenItemDO.setCookStaffName("Cook Staff Name");
        kitchenItemDO.setCookTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setCurrentCount(new BigDecimal("2.3"));
        kitchenItemDO.setDelayTimeMinutes(2);
        kitchenItemDO.setDisplayRuleType(2);
        kitchenItemDO.setDisplayTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setDisplayType(2);
        kitchenItemDO.setDistributeStaffGuid("1234");
        kitchenItemDO.setDistributeStaffName("Distribute Staff Name");
        kitchenItemDO.setDistributeTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setDstDeviceId("42");
        kitchenItemDO.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setGuid("1234");
        kitchenItemDO.setHangUpTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setId(1L);
        kitchenItemDO.setIsPrintAutomatic(true);
        kitchenItemDO.setIsWeight(true);
        kitchenItemDO.setItemAttrMd5("27c7cf400229103e00c6d8830029e29b");
        kitchenItemDO.setItemGuid("1234");
        kitchenItemDO.setItemName("Item Name");
        kitchenItemDO.setItemRemark("Item Remark");
        kitchenItemDO.setItemState(42);
        kitchenItemDO.setKitchenState(2);
        kitchenItemDO.setOrderDesc("Order Desc");
        kitchenItemDO.setOrderGuid("1234");
        kitchenItemDO.setOrderItemGuid("1234");
        kitchenItemDO.setOrderNumber("42");
        kitchenItemDO.setOrderRemark("Order Remark");
        kitchenItemDO.setOrderSerialNo("Order Serial No");
        kitchenItemDO.setOrderSortTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setOriginalItemGuid("1234");
        kitchenItemDO.setOriginalItemSkuName("Original Item Sku Name");
        kitchenItemDO.setOriginalSkuGuid("1234");
        kitchenItemDO.setPointGuid("1234");
        kitchenItemDO.setPrdDeviceId("42");
        kitchenItemDO.setPrepareTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setReturnCount(new BigDecimal("2.3"));
        kitchenItemDO.setSkuCode("Sku Code");
        kitchenItemDO.setSkuGuid("1234");
        kitchenItemDO.setSkuName("Sku Name");
        kitchenItemDO.setSkuUnit("Sku Unit");
        kitchenItemDO.setSort(2);
        kitchenItemDO.setStoreGuid("1234");
        kitchenItemDO.setTableGuid("1234");
        kitchenItemDO.setTimeout(10);
        kitchenItemDO.setUrgedTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setUrgedTimes(2);

        ArrayList<KitchenItemDO> kitchenItemDOList = new ArrayList<>();
        kitchenItemDOList.add(kitchenItemDO);
        doNothing().when(kitchenItemMapper).batchUpdateById(Mockito.<List<KitchenItemDO>>any());
        when(kitchenItemMapper.selectList(Mockito.<Wrapper<KitchenItemDO>>any())).thenReturn(kitchenItemDOList);
        when(deviceConfigService.listDeviceOfStore(Mockito.<String>any())).thenReturn(new ArrayList<>());
        when(deviceConfigService.listPrdDeviceByGuid(Mockito.<String>any(), Mockito.<List<String>>any()))
                .thenReturn(new HashMap<>());
        when(prdPointItemService.queryPrdPointByItem(Mockito.<List<String>>any(), Mockito.<String>any()))
                .thenReturn(new HashMap<>());
        when(kitchenItemAttrService.remove(Mockito.<Wrapper<KitchenItemAttrDO>>any())).thenReturn(true);
        when(distributeItemService.queryDstSkuMap(Mockito.<String>any(), Mockito.<List<String>>any()))
                .thenReturn(new HashMap<>());
        when(distributeAreaService.queryDstAreaMap(Mockito.<String>any(), Mockito.<List<String>>any()))
                .thenReturn(new HashMap<>());
        when(kitchenItemMapstruct.itemChangesDTO2KdsChangesItemDTO(Mockito.<ItemChangesReqDTO>any()))
                .thenThrow(new IllegalArgumentException("displayRule:"));
        doNothing().when(kdsStatusPushService).statusChanged(anyInt(), Mockito.<Collection<DeviceConfigDO>>any());
        when(displayStoreService.list(Mockito.<Wrapper<DisplayStoreDO>>any())).thenReturn(new ArrayList<>());
        when(displayRuleService.list(Mockito.<Wrapper<DisplayRuleDO>>any())).thenReturn(new ArrayList<>());
        when(redisUtils.get(Mockito.<String>any())).thenReturn("Get");
        when(itemRpcService.kdsItemParentMapping(Mockito.<List<String>>any())).thenReturn(new ArrayList<>());
        when(itemRpcService.findParentSkus(Mockito.<List<String>>any())).thenReturn(new ArrayList<>());

        KdsItemDTO changesKdsItem = new KdsItemDTO();
        changesKdsItem.setAttrGroup(new ArrayList<>());
        changesKdsItem.setCurrentCount(new BigDecimal("2.3"));
        changesKdsItem.setIsWeight(true);
        changesKdsItem.setItemGuid("1234");
        changesKdsItem.setItemName("Item Name");
        changesKdsItem.setItemRemark("Item Remark");
        changesKdsItem.setItemState(42);
        changesKdsItem.setOrderItemGuid("1234");
        changesKdsItem.setOrderRemark("Order Remark");
        changesKdsItem.setSkuCode("Sku Code");
        changesKdsItem.setSkuGuid("1234");
        changesKdsItem.setSkuName("Sku Name");
        changesKdsItem.setSkuUnit("Sku Unit");

        KdsItemDTO originalKdsItem = new KdsItemDTO();
        originalKdsItem.setAttrGroup(new ArrayList<>());
        originalKdsItem.setCurrentCount(new BigDecimal("2.3"));
        originalKdsItem.setIsWeight(true);
        originalKdsItem.setItemGuid("1234");
        originalKdsItem.setItemName("Item Name");
        originalKdsItem.setItemRemark("Item Remark");
        originalKdsItem.setItemState(42);
        originalKdsItem.setOrderItemGuid("1234");
        originalKdsItem.setOrderRemark("Order Remark");
        originalKdsItem.setSkuCode("Sku Code");
        originalKdsItem.setSkuGuid("1234");
        originalKdsItem.setSkuName("Sku Name");
        originalKdsItem.setSkuUnit("Sku Unit");

        ItemChangesReqDTO itemChangesReqDTO = new ItemChangesReqDTO();
        itemChangesReqDTO.setAccount("3");
        itemChangesReqDTO.setAccountName("Dr Jane Doe");
        itemChangesReqDTO.setActuallyPayFee(new BigDecimal("2.3"));
        itemChangesReqDTO.setAreaGuid("1234");
        itemChangesReqDTO.setChangesKdsItem(changesKdsItem);
        itemChangesReqDTO.setCreateStaffName("Create Staff Name");
        itemChangesReqDTO.setDeviceId("42");
        itemChangesReqDTO.setDeviceType(1);
        itemChangesReqDTO.setDiningTableName("Dining Table Name");
        itemChangesReqDTO.setEnterpriseGuid("1234");
        itemChangesReqDTO.setEnterpriseName("Enterprise Name");
        itemChangesReqDTO.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        itemChangesReqDTO.setInvoiceCode("Invoice Code");
        itemChangesReqDTO.setInvoicePhone("**********");
        itemChangesReqDTO.setIsInvoiceCode(1);
        itemChangesReqDTO.setLatitude("Latitude");
        itemChangesReqDTO.setLongitude("Longitude");
        itemChangesReqDTO.setOperSubjectGuid("1234");
        itemChangesReqDTO.setOrderGuid("1234");
        itemChangesReqDTO.setOrderNo("Order No");
        itemChangesReqDTO.setOriginalKdsItem(originalKdsItem);
        itemChangesReqDTO.setRequestTimestamp(1L);
        itemChangesReqDTO.setStoreGuid("1234");
        itemChangesReqDTO.setStoreName("Store Name");
        itemChangesReqDTO.setTradeMode(1);
        itemChangesReqDTO.setUserGuid("1234");
        itemChangesReqDTO.setUserName("janedoe");
        thrown.expect(IllegalArgumentException.class);
        kitchenItemServiceImpl.changes(itemChangesReqDTO);
        verify(kitchenItemMapper).selectList(Mockito.<Wrapper<KitchenItemDO>>any());
        verify(displayRuleService).list(Mockito.<Wrapper<DisplayRuleDO>>any());
        verify(displayStoreService).list(Mockito.<Wrapper<DisplayStoreDO>>any());
        verify(kitchenItemAttrService).remove(Mockito.<Wrapper<KitchenItemAttrDO>>any());
        verify(kitchenItemMapper).batchUpdateById(Mockito.<List<KitchenItemDO>>any());
        verify(kitchenItemMapstruct).itemChangesDTO2KdsChangesItemDTO(Mockito.<ItemChangesReqDTO>any());
        verify(deviceConfigService, atLeast(1)).listDeviceOfStore(Mockito.<String>any());
        verify(deviceConfigService).listPrdDeviceByGuid(Mockito.<String>any(), Mockito.<List<String>>any());
        verify(distributeAreaService).queryDstAreaMap(Mockito.<String>any(), Mockito.<List<String>>any());
        verify(distributeItemService).queryDstSkuMap(Mockito.<String>any(), Mockito.<List<String>>any());
        verify(kdsStatusPushService).statusChanged(anyInt(), Mockito.<Collection<DeviceConfigDO>>any());
        verify(prdPointItemService).queryPrdPointByItem(Mockito.<List<String>>any(), Mockito.<String>any());
        verify(itemRpcService).findParentSkus(Mockito.<List<String>>any());
        verify(itemRpcService).kdsItemParentMapping(Mockito.<List<String>>any());
        verify(redisUtils).get(Mockito.<String>any());
    }

    /**
     * Method under test: {@link KitchenItemServiceImpl#changes(ItemChangesReqDTO)}
     */
    @Test
    public void testChanges3() {
        KitchenItemDO kitchenItemDO = new KitchenItemDO();
        kitchenItemDO.setAreaGuid("1234");
        kitchenItemDO.setBatch(2);
        kitchenItemDO.setCallUpTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setCancelDstStaffGuid("1234");
        kitchenItemDO.setCancelDstStaffName("Cancel Dst Staff Name");
        kitchenItemDO.setCancelDstTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setCompleteStaffGuid("1234");
        kitchenItemDO.setCompleteStaffName("Complete Staff Name");
        kitchenItemDO.setCompleteTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setCookStaffGuid("1234");
        kitchenItemDO.setCookStaffName("Cook Staff Name");
        kitchenItemDO.setCookTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setCurrentCount(new BigDecimal("2.3"));
        kitchenItemDO.setDelayTimeMinutes(2);
        kitchenItemDO.setDisplayRuleType(2);
        kitchenItemDO.setDisplayTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setDisplayType(2);
        kitchenItemDO.setDistributeStaffGuid("1234");
        kitchenItemDO.setDistributeStaffName("Distribute Staff Name");
        kitchenItemDO.setDistributeTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setDstDeviceId("42");
        kitchenItemDO.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setGuid("1234");
        kitchenItemDO.setHangUpTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setId(1L);
        kitchenItemDO.setIsPrintAutomatic(true);
        kitchenItemDO.setIsWeight(true);
        kitchenItemDO.setItemAttrMd5("27c7cf400229103e00c6d8830029e29b");
        kitchenItemDO.setItemGuid("1234");
        kitchenItemDO.setItemName("Item Name");
        kitchenItemDO.setItemRemark("Item Remark");
        kitchenItemDO.setItemState(42);
        kitchenItemDO.setKitchenState(2);
        kitchenItemDO.setOrderDesc("Order Desc");
        kitchenItemDO.setOrderGuid("1234");
        kitchenItemDO.setOrderItemGuid("1234");
        kitchenItemDO.setOrderNumber("42");
        kitchenItemDO.setOrderRemark("Order Remark");
        kitchenItemDO.setOrderSerialNo("Order Serial No");
        kitchenItemDO.setOrderSortTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setOriginalItemGuid("1234");
        kitchenItemDO.setOriginalItemSkuName("Original Item Sku Name");
        kitchenItemDO.setOriginalSkuGuid("1234");
        kitchenItemDO.setPointGuid("1234");
        kitchenItemDO.setPrdDeviceId("42");
        kitchenItemDO.setPrepareTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setReturnCount(new BigDecimal("2.3"));
        kitchenItemDO.setSkuCode("Sku Code");
        kitchenItemDO.setSkuGuid("1234");
        kitchenItemDO.setSkuName("Sku Name");
        kitchenItemDO.setSkuUnit("Sku Unit");
        kitchenItemDO.setSort(2);
        kitchenItemDO.setStoreGuid("1234");
        kitchenItemDO.setTableGuid("1234");
        kitchenItemDO.setTimeout(10);
        kitchenItemDO.setUrgedTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setUrgedTimes(2);

        ArrayList<KitchenItemDO> kitchenItemDOList = new ArrayList<>();
        kitchenItemDOList.add(kitchenItemDO);
        when(kitchenItemMapper.selectList(Mockito.<Wrapper<KitchenItemDO>>any())).thenReturn(kitchenItemDOList);
        when(prdPointItemService.queryPrdPointByItem(Mockito.<List<String>>any(), Mockito.<String>any()))
                .thenReturn(new HashMap<>());
        when(displayStoreService.list(Mockito.<Wrapper<DisplayStoreDO>>any())).thenReturn(new ArrayList<>());
        when(displayRuleService.list(Mockito.<Wrapper<DisplayRuleDO>>any())).thenReturn(new ArrayList<>());
        when(redisUtils.get(Mockito.<String>any())).thenReturn("Get");
        when(itemRpcService.kdsItemParentMapping(Mockito.<List<String>>any()))
                .thenThrow(new IllegalArgumentException("displayRule:"));
        when(itemRpcService.findParentSkus(Mockito.<List<String>>any())).thenReturn(new ArrayList<>());

        KdsItemDTO changesKdsItem = new KdsItemDTO();
        changesKdsItem.setAttrGroup(new ArrayList<>());
        changesKdsItem.setCurrentCount(new BigDecimal("2.3"));
        changesKdsItem.setIsWeight(true);
        changesKdsItem.setItemGuid("1234");
        changesKdsItem.setItemName("Item Name");
        changesKdsItem.setItemRemark("Item Remark");
        changesKdsItem.setItemState(42);
        changesKdsItem.setOrderItemGuid("1234");
        changesKdsItem.setOrderRemark("Order Remark");
        changesKdsItem.setSkuCode("Sku Code");
        changesKdsItem.setSkuGuid("1234");
        changesKdsItem.setSkuName("Sku Name");
        changesKdsItem.setSkuUnit("Sku Unit");

        KdsItemDTO originalKdsItem = new KdsItemDTO();
        originalKdsItem.setAttrGroup(new ArrayList<>());
        originalKdsItem.setCurrentCount(new BigDecimal("2.3"));
        originalKdsItem.setIsWeight(true);
        originalKdsItem.setItemGuid("1234");
        originalKdsItem.setItemName("Item Name");
        originalKdsItem.setItemRemark("Item Remark");
        originalKdsItem.setItemState(42);
        originalKdsItem.setOrderItemGuid("1234");
        originalKdsItem.setOrderRemark("Order Remark");
        originalKdsItem.setSkuCode("Sku Code");
        originalKdsItem.setSkuGuid("1234");
        originalKdsItem.setSkuName("Sku Name");
        originalKdsItem.setSkuUnit("Sku Unit");

        ItemChangesReqDTO itemChangesReqDTO = new ItemChangesReqDTO();
        itemChangesReqDTO.setAccount("3");
        itemChangesReqDTO.setAccountName("Dr Jane Doe");
        itemChangesReqDTO.setActuallyPayFee(new BigDecimal("2.3"));
        itemChangesReqDTO.setAreaGuid("1234");
        itemChangesReqDTO.setChangesKdsItem(changesKdsItem);
        itemChangesReqDTO.setCreateStaffName("Create Staff Name");
        itemChangesReqDTO.setDeviceId("42");
        itemChangesReqDTO.setDeviceType(1);
        itemChangesReqDTO.setDiningTableName("Dining Table Name");
        itemChangesReqDTO.setEnterpriseGuid("1234");
        itemChangesReqDTO.setEnterpriseName("Enterprise Name");
        itemChangesReqDTO.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        itemChangesReqDTO.setInvoiceCode("Invoice Code");
        itemChangesReqDTO.setInvoicePhone("**********");
        itemChangesReqDTO.setIsInvoiceCode(1);
        itemChangesReqDTO.setLatitude("Latitude");
        itemChangesReqDTO.setLongitude("Longitude");
        itemChangesReqDTO.setOperSubjectGuid("1234");
        itemChangesReqDTO.setOrderGuid("1234");
        itemChangesReqDTO.setOrderNo("Order No");
        itemChangesReqDTO.setOriginalKdsItem(originalKdsItem);
        itemChangesReqDTO.setRequestTimestamp(1L);
        itemChangesReqDTO.setStoreGuid("1234");
        itemChangesReqDTO.setStoreName("Store Name");
        itemChangesReqDTO.setTradeMode(1);
        itemChangesReqDTO.setUserGuid("1234");
        itemChangesReqDTO.setUserName("janedoe");
        thrown.expect(IllegalArgumentException.class);
        kitchenItemServiceImpl.changes(itemChangesReqDTO);
        verify(kitchenItemMapper).selectList(Mockito.<Wrapper<KitchenItemDO>>any());
        verify(displayRuleService).list(Mockito.<Wrapper<DisplayRuleDO>>any());
        verify(displayStoreService).list(Mockito.<Wrapper<DisplayStoreDO>>any());
        verify(prdPointItemService).queryPrdPointByItem(Mockito.<List<String>>any(), Mockito.<String>any());
        verify(itemRpcService).findParentSkus(Mockito.<List<String>>any());
        verify(itemRpcService).kdsItemParentMapping(Mockito.<List<String>>any());
        verify(redisUtils).get(Mockito.<String>any());
    }

    /**
     * Method under test: {@link KitchenItemServiceImpl#changes(ItemChangesReqDTO)}
     */
    @Test
    public void testChanges4() {
        KitchenItemDO kitchenItemDO = new KitchenItemDO();
        kitchenItemDO.setAreaGuid("1234");
        kitchenItemDO.setBatch(2);
        kitchenItemDO.setCallUpTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setCancelDstStaffGuid("1234");
        kitchenItemDO.setCancelDstStaffName("Cancel Dst Staff Name");
        kitchenItemDO.setCancelDstTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setCompleteStaffGuid("1234");
        kitchenItemDO.setCompleteStaffName("Complete Staff Name");
        kitchenItemDO.setCompleteTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setCookStaffGuid("1234");
        kitchenItemDO.setCookStaffName("Cook Staff Name");
        kitchenItemDO.setCookTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setCurrentCount(new BigDecimal("2.3"));
        kitchenItemDO.setDelayTimeMinutes(2);
        kitchenItemDO.setDisplayRuleType(2);
        kitchenItemDO.setDisplayTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setDisplayType(2);
        kitchenItemDO.setDistributeStaffGuid("1234");
        kitchenItemDO.setDistributeStaffName("Distribute Staff Name");
        kitchenItemDO.setDistributeTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setDstDeviceId("42");
        kitchenItemDO.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setGuid("1234");
        kitchenItemDO.setHangUpTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setId(1L);
        kitchenItemDO.setIsPrintAutomatic(true);
        kitchenItemDO.setIsWeight(true);
        kitchenItemDO.setItemAttrMd5("27c7cf400229103e00c6d8830029e29b");
        kitchenItemDO.setItemGuid("1234");
        kitchenItemDO.setItemName("Item Name");
        kitchenItemDO.setItemRemark("Item Remark");
        kitchenItemDO.setItemState(42);
        kitchenItemDO.setKitchenState(2);
        kitchenItemDO.setOrderDesc("Order Desc");
        kitchenItemDO.setOrderGuid("1234");
        kitchenItemDO.setOrderItemGuid("1234");
        kitchenItemDO.setOrderNumber("42");
        kitchenItemDO.setOrderRemark("Order Remark");
        kitchenItemDO.setOrderSerialNo("Order Serial No");
        kitchenItemDO.setOrderSortTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setOriginalItemGuid("1234");
        kitchenItemDO.setOriginalItemSkuName("Original Item Sku Name");
        kitchenItemDO.setOriginalSkuGuid("1234");
        kitchenItemDO.setPointGuid("1234");
        kitchenItemDO.setPrdDeviceId("42");
        kitchenItemDO.setPrepareTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setReturnCount(new BigDecimal("2.3"));
        kitchenItemDO.setSkuCode("Sku Code");
        kitchenItemDO.setSkuGuid("1234");
        kitchenItemDO.setSkuName("Sku Name");
        kitchenItemDO.setSkuUnit("Sku Unit");
        kitchenItemDO.setSort(2);
        kitchenItemDO.setStoreGuid("1234");
        kitchenItemDO.setTableGuid("1234");
        kitchenItemDO.setTimeout(10);
        kitchenItemDO.setUrgedTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setUrgedTimes(2);

        KitchenItemDO kitchenItemDO2 = new KitchenItemDO();
        kitchenItemDO2.setAreaGuid("displayRule:");
        kitchenItemDO2.setBatch(1);
        kitchenItemDO2.setCallUpTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO2.setCancelDstStaffGuid("displayRule:");
        kitchenItemDO2.setCancelDstStaffName("无该门店匹配的显示规则！");
        kitchenItemDO2.setCancelDstTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO2.setCompleteStaffGuid("displayRule:");
        kitchenItemDO2.setCompleteStaffName("无该门店匹配的显示规则！");
        kitchenItemDO2.setCompleteTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO2.setCookStaffGuid("displayRule:");
        kitchenItemDO2.setCookStaffName("无该门店匹配的显示规则！");
        kitchenItemDO2.setCookTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO2.setCurrentCount(new BigDecimal("2.3"));
        kitchenItemDO2.setDelayTimeMinutes(1);
        kitchenItemDO2.setDisplayRuleType(1);
        kitchenItemDO2.setDisplayTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO2.setDisplayType(1);
        kitchenItemDO2.setDistributeStaffGuid("displayRule:");
        kitchenItemDO2.setDistributeStaffName("无该门店匹配的显示规则！");
        kitchenItemDO2.setDistributeTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO2.setDstDeviceId("displayRule:");
        kitchenItemDO2.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO2.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO2.setGuid("displayRule:");
        kitchenItemDO2.setHangUpTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO2.setId(2L);
        kitchenItemDO2.setIsPrintAutomatic(false);
        kitchenItemDO2.setIsWeight(false);
        kitchenItemDO2.setItemAttrMd5("displayRule:");
        kitchenItemDO2.setItemGuid("displayRule:");
        kitchenItemDO2.setItemName("无该门店匹配的显示规则！");
        kitchenItemDO2.setItemRemark("无该门店匹配的显示规则！");
        kitchenItemDO2.setItemState(2);
        kitchenItemDO2.setKitchenState(1);
        kitchenItemDO2.setOrderDesc("无该门店匹配的显示规则！");
        kitchenItemDO2.setOrderGuid("displayRule:");
        kitchenItemDO2.setOrderItemGuid("displayRule:");
        kitchenItemDO2.setOrderNumber("displayRule:");
        kitchenItemDO2.setOrderRemark("无该门店匹配的显示规则！");
        kitchenItemDO2.setOrderSerialNo("无该门店匹配的显示规则！");
        kitchenItemDO2.setOrderSortTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO2.setOriginalItemGuid("displayRule:");
        kitchenItemDO2.setOriginalItemSkuName("无该门店匹配的显示规则！");
        kitchenItemDO2.setOriginalSkuGuid("displayRule:");
        kitchenItemDO2.setPointGuid("displayRule:");
        kitchenItemDO2.setPrdDeviceId("displayRule:");
        kitchenItemDO2.setPrepareTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO2.setReturnCount(new BigDecimal("2.3"));
        kitchenItemDO2.setSkuCode("无该门店匹配的显示规则！");
        kitchenItemDO2.setSkuGuid("displayRule:");
        kitchenItemDO2.setSkuName("无该门店匹配的显示规则！");
        kitchenItemDO2.setSkuUnit("无该门店匹配的显示规则！");
        kitchenItemDO2.setSort(1);
        kitchenItemDO2.setStoreGuid("displayRule:");
        kitchenItemDO2.setTableGuid("displayRule:");
        kitchenItemDO2.setTimeout(2);
        kitchenItemDO2.setUrgedTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO2.setUrgedTimes(1);

        ArrayList<KitchenItemDO> kitchenItemDOList = new ArrayList<>();
        kitchenItemDOList.add(kitchenItemDO2);
        kitchenItemDOList.add(kitchenItemDO);
        doNothing().when(kitchenItemMapper).batchUpdateById(Mockito.<List<KitchenItemDO>>any());
        when(kitchenItemMapper.selectList(Mockito.<Wrapper<KitchenItemDO>>any())).thenReturn(kitchenItemDOList);
        when(deviceConfigService.listDeviceOfStore(Mockito.<String>any())).thenReturn(new ArrayList<>());
        when(deviceConfigService.listPrdDeviceByGuid(Mockito.<String>any(), Mockito.<List<String>>any()))
                .thenReturn(new HashMap<>());
        when(prdPointItemService.queryPrdPointByItem(Mockito.<List<String>>any(), Mockito.<String>any()))
                .thenReturn(new HashMap<>());
        when(kitchenItemAttrService.remove(Mockito.<Wrapper<KitchenItemAttrDO>>any())).thenReturn(true);
        when(distributeItemService.queryDstSkuMap(Mockito.<String>any(), Mockito.<List<String>>any()))
                .thenReturn(new HashMap<>());
        when(distributeAreaService.queryDstAreaMap(Mockito.<String>any(), Mockito.<List<String>>any()))
                .thenReturn(new HashMap<>());
        when(kitchenItemMapstruct.itemChangesDTO2KdsChangesItemDTO(Mockito.<ItemChangesReqDTO>any()))
                .thenThrow(new IllegalArgumentException("displayRule:"));
        doNothing().when(kdsStatusPushService).statusChanged(anyInt(), Mockito.<Collection<DeviceConfigDO>>any());
        when(displayStoreService.list(Mockito.<Wrapper<DisplayStoreDO>>any())).thenReturn(new ArrayList<>());
        when(displayRuleService.list(Mockito.<Wrapper<DisplayRuleDO>>any())).thenReturn(new ArrayList<>());
        when(redisUtils.get(Mockito.<String>any())).thenReturn("Get");
        when(itemRpcService.kdsItemParentMapping(Mockito.<List<String>>any())).thenReturn(new ArrayList<>());
        when(itemRpcService.findParentSkus(Mockito.<List<String>>any())).thenReturn(new ArrayList<>());

        KdsItemDTO changesKdsItem = new KdsItemDTO();
        changesKdsItem.setAttrGroup(new ArrayList<>());
        changesKdsItem.setCurrentCount(new BigDecimal("2.3"));
        changesKdsItem.setIsWeight(true);
        changesKdsItem.setItemGuid("1234");
        changesKdsItem.setItemName("Item Name");
        changesKdsItem.setItemRemark("Item Remark");
        changesKdsItem.setItemState(42);
        changesKdsItem.setOrderItemGuid("1234");
        changesKdsItem.setOrderRemark("Order Remark");
        changesKdsItem.setSkuCode("Sku Code");
        changesKdsItem.setSkuGuid("1234");
        changesKdsItem.setSkuName("Sku Name");
        changesKdsItem.setSkuUnit("Sku Unit");

        KdsItemDTO originalKdsItem = new KdsItemDTO();
        originalKdsItem.setAttrGroup(new ArrayList<>());
        originalKdsItem.setCurrentCount(new BigDecimal("2.3"));
        originalKdsItem.setIsWeight(true);
        originalKdsItem.setItemGuid("1234");
        originalKdsItem.setItemName("Item Name");
        originalKdsItem.setItemRemark("Item Remark");
        originalKdsItem.setItemState(42);
        originalKdsItem.setOrderItemGuid("1234");
        originalKdsItem.setOrderRemark("Order Remark");
        originalKdsItem.setSkuCode("Sku Code");
        originalKdsItem.setSkuGuid("1234");
        originalKdsItem.setSkuName("Sku Name");
        originalKdsItem.setSkuUnit("Sku Unit");

        ItemChangesReqDTO itemChangesReqDTO = new ItemChangesReqDTO();
        itemChangesReqDTO.setAccount("3");
        itemChangesReqDTO.setAccountName("Dr Jane Doe");
        itemChangesReqDTO.setActuallyPayFee(new BigDecimal("2.3"));
        itemChangesReqDTO.setAreaGuid("1234");
        itemChangesReqDTO.setChangesKdsItem(changesKdsItem);
        itemChangesReqDTO.setCreateStaffName("Create Staff Name");
        itemChangesReqDTO.setDeviceId("42");
        itemChangesReqDTO.setDeviceType(1);
        itemChangesReqDTO.setDiningTableName("Dining Table Name");
        itemChangesReqDTO.setEnterpriseGuid("1234");
        itemChangesReqDTO.setEnterpriseName("Enterprise Name");
        itemChangesReqDTO.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        itemChangesReqDTO.setInvoiceCode("Invoice Code");
        itemChangesReqDTO.setInvoicePhone("**********");
        itemChangesReqDTO.setIsInvoiceCode(1);
        itemChangesReqDTO.setLatitude("Latitude");
        itemChangesReqDTO.setLongitude("Longitude");
        itemChangesReqDTO.setOperSubjectGuid("1234");
        itemChangesReqDTO.setOrderGuid("1234");
        itemChangesReqDTO.setOrderNo("Order No");
        itemChangesReqDTO.setOriginalKdsItem(originalKdsItem);
        itemChangesReqDTO.setRequestTimestamp(1L);
        itemChangesReqDTO.setStoreGuid("1234");
        itemChangesReqDTO.setStoreName("Store Name");
        itemChangesReqDTO.setTradeMode(1);
        itemChangesReqDTO.setUserGuid("1234");
        itemChangesReqDTO.setUserName("janedoe");
        thrown.expect(IllegalArgumentException.class);
        kitchenItemServiceImpl.changes(itemChangesReqDTO);
        verify(kitchenItemMapper).selectList(Mockito.<Wrapper<KitchenItemDO>>any());
        verify(displayRuleService).list(Mockito.<Wrapper<DisplayRuleDO>>any());
        verify(displayStoreService).list(Mockito.<Wrapper<DisplayStoreDO>>any());
        verify(kitchenItemAttrService).remove(Mockito.<Wrapper<KitchenItemAttrDO>>any());
        verify(kitchenItemMapper).batchUpdateById(Mockito.<List<KitchenItemDO>>any());
        verify(kitchenItemMapstruct).itemChangesDTO2KdsChangesItemDTO(Mockito.<ItemChangesReqDTO>any());
        verify(deviceConfigService, atLeast(1)).listDeviceOfStore(Mockito.<String>any());
        verify(deviceConfigService).listPrdDeviceByGuid(Mockito.<String>any(), Mockito.<List<String>>any());
        verify(distributeAreaService, atLeast(1)).queryDstAreaMap(Mockito.<String>any(), Mockito.<List<String>>any());
        verify(distributeItemService, atLeast(1)).queryDstSkuMap(Mockito.<String>any(), Mockito.<List<String>>any());
        verify(kdsStatusPushService).statusChanged(anyInt(), Mockito.<Collection<DeviceConfigDO>>any());
        verify(prdPointItemService).queryPrdPointByItem(Mockito.<List<String>>any(), Mockito.<String>any());
        verify(itemRpcService).findParentSkus(Mockito.<List<String>>any());
        verify(itemRpcService).kdsItemParentMapping(Mockito.<List<String>>any());
        verify(redisUtils).get(Mockito.<String>any());
    }

    /**
     * Method under test: {@link KitchenItemServiceImpl#changes(ItemChangesReqDTO)}
     */
    @Test
    public void testChanges5() {
        KitchenItemDO kitchenItemDO = mock(KitchenItemDO.class);
        when(kitchenItemDO.getItemState()).thenReturn(42);
        when(kitchenItemDO.getOrderItemGuid()).thenReturn("1234");
        when(kitchenItemDO.getId()).thenReturn(1L);
        when(kitchenItemDO.getGuid()).thenReturn("1234");
        when(kitchenItemDO.getAreaGuid()).thenReturn("1234");
        doNothing().when(kitchenItemDO).setGmtCreate(Mockito.<LocalDateTime>any());
        doNothing().when(kitchenItemDO).setGmtModified(Mockito.<LocalDateTime>any());
        doNothing().when(kitchenItemDO).setGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setId(Mockito.<Long>any());
        doNothing().when(kitchenItemDO).setAreaGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setBatch(Mockito.<Integer>any());
        doNothing().when(kitchenItemDO).setCallUpTime(Mockito.<LocalDateTime>any());
        doNothing().when(kitchenItemDO).setCancelDstStaffGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setCancelDstStaffName(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setCancelDstTime(Mockito.<LocalDateTime>any());
        doNothing().when(kitchenItemDO).setCompleteStaffGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setCompleteStaffName(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setCompleteTime(Mockito.<LocalDateTime>any());
        doNothing().when(kitchenItemDO).setCookStaffGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setCookStaffName(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setCookTime(Mockito.<LocalDateTime>any());
        doNothing().when(kitchenItemDO).setCurrentCount(Mockito.<BigDecimal>any());
        doNothing().when(kitchenItemDO).setDelayTimeMinutes(Mockito.<Integer>any());
        doNothing().when(kitchenItemDO).setDisplayRuleType(Mockito.<Integer>any());
        doNothing().when(kitchenItemDO).setDisplayTime(Mockito.<LocalDateTime>any());
        doNothing().when(kitchenItemDO).setDisplayType(Mockito.<Integer>any());
        doNothing().when(kitchenItemDO).setDistributeStaffGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setDistributeStaffName(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setDistributeTime(Mockito.<LocalDateTime>any());
        doNothing().when(kitchenItemDO).setDstDeviceId(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setHangUpTime(Mockito.<LocalDateTime>any());
        doNothing().when(kitchenItemDO).setIsPrintAutomatic(Mockito.<Boolean>any());
        doNothing().when(kitchenItemDO).setIsWeight(Mockito.<Boolean>any());
        doNothing().when(kitchenItemDO).setItemAttrMd5(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setItemGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setItemName(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setItemRemark(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setItemState(Mockito.<Integer>any());
        doNothing().when(kitchenItemDO).setKitchenState(Mockito.<Integer>any());
        doNothing().when(kitchenItemDO).setOrderDesc(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setOrderGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setOrderItemGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setOrderNumber(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setOrderRemark(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setOrderSerialNo(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setOrderSortTime(Mockito.<LocalDateTime>any());
        doNothing().when(kitchenItemDO).setOriginalItemGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setOriginalItemSkuName(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setOriginalSkuGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setPointGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setPrdDeviceId(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setPrepareTime(Mockito.<LocalDateTime>any());
        doNothing().when(kitchenItemDO).setReturnCount(Mockito.<BigDecimal>any());
        doNothing().when(kitchenItemDO).setSkuCode(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setSkuGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setSkuName(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setSkuUnit(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setSort(Mockito.<Integer>any());
        doNothing().when(kitchenItemDO).setStoreGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setTableGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setTimeout(Mockito.<Integer>any());
        doNothing().when(kitchenItemDO).setUrgedTime(Mockito.<LocalDateTime>any());
        doNothing().when(kitchenItemDO).setUrgedTimes(Mockito.<Integer>any());
        kitchenItemDO.setAreaGuid("1234");
        kitchenItemDO.setBatch(2);
        kitchenItemDO.setCallUpTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setCancelDstStaffGuid("1234");
        kitchenItemDO.setCancelDstStaffName("Cancel Dst Staff Name");
        kitchenItemDO.setCancelDstTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setCompleteStaffGuid("1234");
        kitchenItemDO.setCompleteStaffName("Complete Staff Name");
        kitchenItemDO.setCompleteTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setCookStaffGuid("1234");
        kitchenItemDO.setCookStaffName("Cook Staff Name");
        kitchenItemDO.setCookTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setCurrentCount(new BigDecimal("2.3"));
        kitchenItemDO.setDelayTimeMinutes(2);
        kitchenItemDO.setDisplayRuleType(2);
        kitchenItemDO.setDisplayTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setDisplayType(2);
        kitchenItemDO.setDistributeStaffGuid("1234");
        kitchenItemDO.setDistributeStaffName("Distribute Staff Name");
        kitchenItemDO.setDistributeTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setDstDeviceId("42");
        kitchenItemDO.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setGuid("1234");
        kitchenItemDO.setHangUpTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setId(1L);
        kitchenItemDO.setIsPrintAutomatic(true);
        kitchenItemDO.setIsWeight(true);
        kitchenItemDO.setItemAttrMd5("27c7cf400229103e00c6d8830029e29b");
        kitchenItemDO.setItemGuid("1234");
        kitchenItemDO.setItemName("Item Name");
        kitchenItemDO.setItemRemark("Item Remark");
        kitchenItemDO.setItemState(42);
        kitchenItemDO.setKitchenState(2);
        kitchenItemDO.setOrderDesc("Order Desc");
        kitchenItemDO.setOrderGuid("1234");
        kitchenItemDO.setOrderItemGuid("1234");
        kitchenItemDO.setOrderNumber("42");
        kitchenItemDO.setOrderRemark("Order Remark");
        kitchenItemDO.setOrderSerialNo("Order Serial No");
        kitchenItemDO.setOrderSortTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setOriginalItemGuid("1234");
        kitchenItemDO.setOriginalItemSkuName("Original Item Sku Name");
        kitchenItemDO.setOriginalSkuGuid("1234");
        kitchenItemDO.setPointGuid("1234");
        kitchenItemDO.setPrdDeviceId("42");
        kitchenItemDO.setPrepareTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setReturnCount(new BigDecimal("2.3"));
        kitchenItemDO.setSkuCode("Sku Code");
        kitchenItemDO.setSkuGuid("1234");
        kitchenItemDO.setSkuName("Sku Name");
        kitchenItemDO.setSkuUnit("Sku Unit");
        kitchenItemDO.setSort(2);
        kitchenItemDO.setStoreGuid("1234");
        kitchenItemDO.setTableGuid("1234");
        kitchenItemDO.setTimeout(10);
        kitchenItemDO.setUrgedTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setUrgedTimes(2);

        ArrayList<KitchenItemDO> kitchenItemDOList = new ArrayList<>();
        kitchenItemDOList.add(kitchenItemDO);
        doNothing().when(kitchenItemMapper).batchUpdateById(Mockito.<List<KitchenItemDO>>any());
        when(kitchenItemMapper.selectList(Mockito.<Wrapper<KitchenItemDO>>any())).thenReturn(kitchenItemDOList);
        when(deviceConfigService.listDeviceOfStore(Mockito.<String>any())).thenReturn(new ArrayList<>());
        when(deviceConfigService.listPrdDeviceByGuid(Mockito.<String>any(), Mockito.<List<String>>any()))
                .thenReturn(new HashMap<>());
        when(prdPointItemService.queryPrdPointByItem(Mockito.<List<String>>any(), Mockito.<String>any()))
                .thenReturn(new HashMap<>());
        when(kitchenItemAttrService.remove(Mockito.<Wrapper<KitchenItemAttrDO>>any())).thenReturn(true);
        when(distributeItemService.queryDstSkuMap(Mockito.<String>any(), Mockito.<List<String>>any()))
                .thenReturn(new HashMap<>());
        when(distributeAreaService.queryDstAreaMap(Mockito.<String>any(), Mockito.<List<String>>any()))
                .thenReturn(new HashMap<>());
        when(kitchenItemMapstruct.itemChangesDTO2KdsChangesItemDTO(Mockito.<ItemChangesReqDTO>any()))
                .thenThrow(new IllegalArgumentException("displayRule:"));
        doNothing().when(kdsStatusPushService).statusChanged(anyInt(), Mockito.<Collection<DeviceConfigDO>>any());
        when(displayStoreService.list(Mockito.<Wrapper<DisplayStoreDO>>any())).thenReturn(new ArrayList<>());
        when(displayRuleService.list(Mockito.<Wrapper<DisplayRuleDO>>any())).thenReturn(new ArrayList<>());
        when(redisUtils.get(Mockito.<String>any())).thenReturn("Get");
        when(itemRpcService.kdsItemParentMapping(Mockito.<List<String>>any())).thenReturn(new ArrayList<>());
        when(itemRpcService.findParentSkus(Mockito.<List<String>>any())).thenReturn(new ArrayList<>());

        KdsItemDTO changesKdsItem = new KdsItemDTO();
        changesKdsItem.setAttrGroup(new ArrayList<>());
        changesKdsItem.setCurrentCount(new BigDecimal("2.3"));
        changesKdsItem.setIsWeight(true);
        changesKdsItem.setItemGuid("1234");
        changesKdsItem.setItemName("Item Name");
        changesKdsItem.setItemRemark("Item Remark");
        changesKdsItem.setItemState(42);
        changesKdsItem.setOrderItemGuid("1234");
        changesKdsItem.setOrderRemark("Order Remark");
        changesKdsItem.setSkuCode("Sku Code");
        changesKdsItem.setSkuGuid("1234");
        changesKdsItem.setSkuName("Sku Name");
        changesKdsItem.setSkuUnit("Sku Unit");

        KdsItemDTO originalKdsItem = new KdsItemDTO();
        originalKdsItem.setAttrGroup(new ArrayList<>());
        originalKdsItem.setCurrentCount(new BigDecimal("2.3"));
        originalKdsItem.setIsWeight(true);
        originalKdsItem.setItemGuid("1234");
        originalKdsItem.setItemName("Item Name");
        originalKdsItem.setItemRemark("Item Remark");
        originalKdsItem.setItemState(42);
        originalKdsItem.setOrderItemGuid("1234");
        originalKdsItem.setOrderRemark("Order Remark");
        originalKdsItem.setSkuCode("Sku Code");
        originalKdsItem.setSkuGuid("1234");
        originalKdsItem.setSkuName("Sku Name");
        originalKdsItem.setSkuUnit("Sku Unit");

        ItemChangesReqDTO itemChangesReqDTO = new ItemChangesReqDTO();
        itemChangesReqDTO.setAccount("3");
        itemChangesReqDTO.setAccountName("Dr Jane Doe");
        itemChangesReqDTO.setActuallyPayFee(new BigDecimal("2.3"));
        itemChangesReqDTO.setAreaGuid("1234");
        itemChangesReqDTO.setChangesKdsItem(changesKdsItem);
        itemChangesReqDTO.setCreateStaffName("Create Staff Name");
        itemChangesReqDTO.setDeviceId("42");
        itemChangesReqDTO.setDeviceType(1);
        itemChangesReqDTO.setDiningTableName("Dining Table Name");
        itemChangesReqDTO.setEnterpriseGuid("1234");
        itemChangesReqDTO.setEnterpriseName("Enterprise Name");
        itemChangesReqDTO.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        itemChangesReqDTO.setInvoiceCode("Invoice Code");
        itemChangesReqDTO.setInvoicePhone("**********");
        itemChangesReqDTO.setIsInvoiceCode(1);
        itemChangesReqDTO.setLatitude("Latitude");
        itemChangesReqDTO.setLongitude("Longitude");
        itemChangesReqDTO.setOperSubjectGuid("1234");
        itemChangesReqDTO.setOrderGuid("1234");
        itemChangesReqDTO.setOrderNo("Order No");
        itemChangesReqDTO.setOriginalKdsItem(originalKdsItem);
        itemChangesReqDTO.setRequestTimestamp(1L);
        itemChangesReqDTO.setStoreGuid("1234");
        itemChangesReqDTO.setStoreName("Store Name");
        itemChangesReqDTO.setTradeMode(1);
        itemChangesReqDTO.setUserGuid("1234");
        itemChangesReqDTO.setUserName("janedoe");
        thrown.expect(IllegalArgumentException.class);
        kitchenItemServiceImpl.changes(itemChangesReqDTO);
        verify(kitchenItemMapper).selectList(Mockito.<Wrapper<KitchenItemDO>>any());
        verify(displayRuleService).list(Mockito.<Wrapper<DisplayRuleDO>>any());
        verify(displayStoreService).list(Mockito.<Wrapper<DisplayStoreDO>>any());
        verify(kitchenItemAttrService).remove(Mockito.<Wrapper<KitchenItemAttrDO>>any());
        verify(kitchenItemDO).getGuid();
        verify(kitchenItemDO).getId();
        verify(kitchenItemDO).setGmtCreate(Mockito.<LocalDateTime>any());
        verify(kitchenItemDO).setGmtModified(Mockito.<LocalDateTime>any());
        verify(kitchenItemDO).setGuid(Mockito.<String>any());
        verify(kitchenItemDO).setId(Mockito.<Long>any());
        verify(kitchenItemDO, atLeast(1)).getAreaGuid();
        verify(kitchenItemDO).getItemState();
        verify(kitchenItemDO).getOrderItemGuid();
        verify(kitchenItemDO).setAreaGuid(Mockito.<String>any());
        verify(kitchenItemDO).setBatch(Mockito.<Integer>any());
        verify(kitchenItemDO).setCallUpTime(Mockito.<LocalDateTime>any());
        verify(kitchenItemDO).setCancelDstStaffGuid(Mockito.<String>any());
        verify(kitchenItemDO).setCancelDstStaffName(Mockito.<String>any());
        verify(kitchenItemDO).setCancelDstTime(Mockito.<LocalDateTime>any());
        verify(kitchenItemDO).setCompleteStaffGuid(Mockito.<String>any());
        verify(kitchenItemDO).setCompleteStaffName(Mockito.<String>any());
        verify(kitchenItemDO).setCompleteTime(Mockito.<LocalDateTime>any());
        verify(kitchenItemDO).setCookStaffGuid(Mockito.<String>any());
        verify(kitchenItemDO).setCookStaffName(Mockito.<String>any());
        verify(kitchenItemDO).setCookTime(Mockito.<LocalDateTime>any());
        verify(kitchenItemDO).setCurrentCount(Mockito.<BigDecimal>any());
        verify(kitchenItemDO).setDelayTimeMinutes(Mockito.<Integer>any());
        verify(kitchenItemDO).setDisplayRuleType(Mockito.<Integer>any());
        verify(kitchenItemDO).setDisplayTime(Mockito.<LocalDateTime>any());
        verify(kitchenItemDO).setDisplayType(Mockito.<Integer>any());
        verify(kitchenItemDO).setDistributeStaffGuid(Mockito.<String>any());
        verify(kitchenItemDO).setDistributeStaffName(Mockito.<String>any());
        verify(kitchenItemDO).setDistributeTime(Mockito.<LocalDateTime>any());
        verify(kitchenItemDO).setDstDeviceId(Mockito.<String>any());
        verify(kitchenItemDO).setHangUpTime(Mockito.<LocalDateTime>any());
        verify(kitchenItemDO).setIsPrintAutomatic(Mockito.<Boolean>any());
        verify(kitchenItemDO).setIsWeight(Mockito.<Boolean>any());
        verify(kitchenItemDO).setItemAttrMd5(Mockito.<String>any());
        verify(kitchenItemDO).setItemGuid(Mockito.<String>any());
        verify(kitchenItemDO).setItemName(Mockito.<String>any());
        verify(kitchenItemDO).setItemRemark(Mockito.<String>any());
        verify(kitchenItemDO).setItemState(Mockito.<Integer>any());
        verify(kitchenItemDO).setKitchenState(Mockito.<Integer>any());
        verify(kitchenItemDO).setOrderDesc(Mockito.<String>any());
        verify(kitchenItemDO).setOrderGuid(Mockito.<String>any());
        verify(kitchenItemDO).setOrderItemGuid(Mockito.<String>any());
        verify(kitchenItemDO).setOrderNumber(Mockito.<String>any());
        verify(kitchenItemDO).setOrderRemark(Mockito.<String>any());
        verify(kitchenItemDO).setOrderSerialNo(Mockito.<String>any());
        verify(kitchenItemDO).setOrderSortTime(Mockito.<LocalDateTime>any());
        verify(kitchenItemDO).setOriginalItemGuid(Mockito.<String>any());
        verify(kitchenItemDO).setOriginalItemSkuName(Mockito.<String>any());
        verify(kitchenItemDO).setOriginalSkuGuid(Mockito.<String>any());
        verify(kitchenItemDO).setPointGuid(Mockito.<String>any());
        verify(kitchenItemDO).setPrdDeviceId(Mockito.<String>any());
        verify(kitchenItemDO).setPrepareTime(Mockito.<LocalDateTime>any());
        verify(kitchenItemDO).setReturnCount(Mockito.<BigDecimal>any());
        verify(kitchenItemDO).setSkuCode(Mockito.<String>any());
        verify(kitchenItemDO).setSkuGuid(Mockito.<String>any());
        verify(kitchenItemDO).setSkuName(Mockito.<String>any());
        verify(kitchenItemDO).setSkuUnit(Mockito.<String>any());
        verify(kitchenItemDO).setSort(Mockito.<Integer>any());
        verify(kitchenItemDO).setStoreGuid(Mockito.<String>any());
        verify(kitchenItemDO).setTableGuid(Mockito.<String>any());
        verify(kitchenItemDO).setTimeout(Mockito.<Integer>any());
        verify(kitchenItemDO).setUrgedTime(Mockito.<LocalDateTime>any());
        verify(kitchenItemDO).setUrgedTimes(Mockito.<Integer>any());
        verify(kitchenItemMapper).batchUpdateById(Mockito.<List<KitchenItemDO>>any());
        verify(kitchenItemMapstruct).itemChangesDTO2KdsChangesItemDTO(Mockito.<ItemChangesReqDTO>any());
        verify(deviceConfigService, atLeast(1)).listDeviceOfStore(Mockito.<String>any());
        verify(deviceConfigService).listPrdDeviceByGuid(Mockito.<String>any(), Mockito.<List<String>>any());
        verify(distributeAreaService).queryDstAreaMap(Mockito.<String>any(), Mockito.<List<String>>any());
        verify(distributeItemService).queryDstSkuMap(Mockito.<String>any(), Mockito.<List<String>>any());
        verify(kdsStatusPushService).statusChanged(anyInt(), Mockito.<Collection<DeviceConfigDO>>any());
        verify(prdPointItemService).queryPrdPointByItem(Mockito.<List<String>>any(), Mockito.<String>any());
        verify(itemRpcService).findParentSkus(Mockito.<List<String>>any());
        verify(itemRpcService).kdsItemParentMapping(Mockito.<List<String>>any());
        verify(redisUtils).get(Mockito.<String>any());
    }

    /**
     * Method under test: {@link KitchenItemServiceImpl#changes(ItemChangesReqDTO)}
     */
    @Test
    public void testChanges6() {
        KitchenItemDO kitchenItemDO = mock(KitchenItemDO.class);
        when(kitchenItemDO.getItemState()).thenThrow(new IllegalStateException("displayRule:"));
        when(kitchenItemDO.getId()).thenReturn(1L);
        when(kitchenItemDO.getGuid()).thenReturn("1234");
        when(kitchenItemDO.getAreaGuid()).thenReturn("1234");
        doNothing().when(kitchenItemDO).setGmtCreate(Mockito.<LocalDateTime>any());
        doNothing().when(kitchenItemDO).setGmtModified(Mockito.<LocalDateTime>any());
        doNothing().when(kitchenItemDO).setGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setId(Mockito.<Long>any());
        doNothing().when(kitchenItemDO).setAreaGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setBatch(Mockito.<Integer>any());
        doNothing().when(kitchenItemDO).setCallUpTime(Mockito.<LocalDateTime>any());
        doNothing().when(kitchenItemDO).setCancelDstStaffGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setCancelDstStaffName(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setCancelDstTime(Mockito.<LocalDateTime>any());
        doNothing().when(kitchenItemDO).setCompleteStaffGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setCompleteStaffName(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setCompleteTime(Mockito.<LocalDateTime>any());
        doNothing().when(kitchenItemDO).setCookStaffGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setCookStaffName(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setCookTime(Mockito.<LocalDateTime>any());
        doNothing().when(kitchenItemDO).setCurrentCount(Mockito.<BigDecimal>any());
        doNothing().when(kitchenItemDO).setDelayTimeMinutes(Mockito.<Integer>any());
        doNothing().when(kitchenItemDO).setDisplayRuleType(Mockito.<Integer>any());
        doNothing().when(kitchenItemDO).setDisplayTime(Mockito.<LocalDateTime>any());
        doNothing().when(kitchenItemDO).setDisplayType(Mockito.<Integer>any());
        doNothing().when(kitchenItemDO).setDistributeStaffGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setDistributeStaffName(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setDistributeTime(Mockito.<LocalDateTime>any());
        doNothing().when(kitchenItemDO).setDstDeviceId(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setHangUpTime(Mockito.<LocalDateTime>any());
        doNothing().when(kitchenItemDO).setIsPrintAutomatic(Mockito.<Boolean>any());
        doNothing().when(kitchenItemDO).setIsWeight(Mockito.<Boolean>any());
        doNothing().when(kitchenItemDO).setItemAttrMd5(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setItemGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setItemName(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setItemRemark(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setItemState(Mockito.<Integer>any());
        doNothing().when(kitchenItemDO).setKitchenState(Mockito.<Integer>any());
        doNothing().when(kitchenItemDO).setOrderDesc(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setOrderGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setOrderItemGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setOrderNumber(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setOrderRemark(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setOrderSerialNo(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setOrderSortTime(Mockito.<LocalDateTime>any());
        doNothing().when(kitchenItemDO).setOriginalItemGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setOriginalItemSkuName(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setOriginalSkuGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setPointGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setPrdDeviceId(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setPrepareTime(Mockito.<LocalDateTime>any());
        doNothing().when(kitchenItemDO).setReturnCount(Mockito.<BigDecimal>any());
        doNothing().when(kitchenItemDO).setSkuCode(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setSkuGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setSkuName(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setSkuUnit(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setSort(Mockito.<Integer>any());
        doNothing().when(kitchenItemDO).setStoreGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setTableGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setTimeout(Mockito.<Integer>any());
        doNothing().when(kitchenItemDO).setUrgedTime(Mockito.<LocalDateTime>any());
        doNothing().when(kitchenItemDO).setUrgedTimes(Mockito.<Integer>any());
        kitchenItemDO.setAreaGuid("1234");
        kitchenItemDO.setBatch(2);
        kitchenItemDO.setCallUpTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setCancelDstStaffGuid("1234");
        kitchenItemDO.setCancelDstStaffName("Cancel Dst Staff Name");
        kitchenItemDO.setCancelDstTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setCompleteStaffGuid("1234");
        kitchenItemDO.setCompleteStaffName("Complete Staff Name");
        kitchenItemDO.setCompleteTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setCookStaffGuid("1234");
        kitchenItemDO.setCookStaffName("Cook Staff Name");
        kitchenItemDO.setCookTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setCurrentCount(new BigDecimal("2.3"));
        kitchenItemDO.setDelayTimeMinutes(2);
        kitchenItemDO.setDisplayRuleType(2);
        kitchenItemDO.setDisplayTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setDisplayType(2);
        kitchenItemDO.setDistributeStaffGuid("1234");
        kitchenItemDO.setDistributeStaffName("Distribute Staff Name");
        kitchenItemDO.setDistributeTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setDstDeviceId("42");
        kitchenItemDO.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setGuid("1234");
        kitchenItemDO.setHangUpTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setId(1L);
        kitchenItemDO.setIsPrintAutomatic(true);
        kitchenItemDO.setIsWeight(true);
        kitchenItemDO.setItemAttrMd5("27c7cf400229103e00c6d8830029e29b");
        kitchenItemDO.setItemGuid("1234");
        kitchenItemDO.setItemName("Item Name");
        kitchenItemDO.setItemRemark("Item Remark");
        kitchenItemDO.setItemState(42);
        kitchenItemDO.setKitchenState(2);
        kitchenItemDO.setOrderDesc("Order Desc");
        kitchenItemDO.setOrderGuid("1234");
        kitchenItemDO.setOrderItemGuid("1234");
        kitchenItemDO.setOrderNumber("42");
        kitchenItemDO.setOrderRemark("Order Remark");
        kitchenItemDO.setOrderSerialNo("Order Serial No");
        kitchenItemDO.setOrderSortTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setOriginalItemGuid("1234");
        kitchenItemDO.setOriginalItemSkuName("Original Item Sku Name");
        kitchenItemDO.setOriginalSkuGuid("1234");
        kitchenItemDO.setPointGuid("1234");
        kitchenItemDO.setPrdDeviceId("42");
        kitchenItemDO.setPrepareTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setReturnCount(new BigDecimal("2.3"));
        kitchenItemDO.setSkuCode("Sku Code");
        kitchenItemDO.setSkuGuid("1234");
        kitchenItemDO.setSkuName("Sku Name");
        kitchenItemDO.setSkuUnit("Sku Unit");
        kitchenItemDO.setSort(2);
        kitchenItemDO.setStoreGuid("1234");
        kitchenItemDO.setTableGuid("1234");
        kitchenItemDO.setTimeout(10);
        kitchenItemDO.setUrgedTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setUrgedTimes(2);

        ArrayList<KitchenItemDO> kitchenItemDOList = new ArrayList<>();
        kitchenItemDOList.add(kitchenItemDO);
        when(kitchenItemMapper.selectList(Mockito.<Wrapper<KitchenItemDO>>any())).thenReturn(kitchenItemDOList);
        when(prdPointItemService.queryPrdPointByItem(Mockito.<List<String>>any(), Mockito.<String>any()))
                .thenReturn(new HashMap<>());
        when(distributeItemService.queryDstSkuMap(Mockito.<String>any(), Mockito.<List<String>>any()))
                .thenReturn(new HashMap<>());
        when(distributeAreaService.queryDstAreaMap(Mockito.<String>any(), Mockito.<List<String>>any()))
                .thenReturn(new HashMap<>());
        when(displayStoreService.list(Mockito.<Wrapper<DisplayStoreDO>>any())).thenReturn(new ArrayList<>());
        when(displayRuleService.list(Mockito.<Wrapper<DisplayRuleDO>>any())).thenReturn(new ArrayList<>());
        when(redisUtils.get(Mockito.<String>any())).thenReturn("Get");
        when(itemRpcService.kdsItemParentMapping(Mockito.<List<String>>any())).thenReturn(new ArrayList<>());
        when(itemRpcService.findParentSkus(Mockito.<List<String>>any())).thenReturn(new ArrayList<>());

        KdsItemDTO changesKdsItem = new KdsItemDTO();
        changesKdsItem.setAttrGroup(new ArrayList<>());
        changesKdsItem.setCurrentCount(new BigDecimal("2.3"));
        changesKdsItem.setIsWeight(true);
        changesKdsItem.setItemGuid("1234");
        changesKdsItem.setItemName("Item Name");
        changesKdsItem.setItemRemark("Item Remark");
        changesKdsItem.setItemState(42);
        changesKdsItem.setOrderItemGuid("1234");
        changesKdsItem.setOrderRemark("Order Remark");
        changesKdsItem.setSkuCode("Sku Code");
        changesKdsItem.setSkuGuid("1234");
        changesKdsItem.setSkuName("Sku Name");
        changesKdsItem.setSkuUnit("Sku Unit");

        KdsItemDTO originalKdsItem = new KdsItemDTO();
        originalKdsItem.setAttrGroup(new ArrayList<>());
        originalKdsItem.setCurrentCount(new BigDecimal("2.3"));
        originalKdsItem.setIsWeight(true);
        originalKdsItem.setItemGuid("1234");
        originalKdsItem.setItemName("Item Name");
        originalKdsItem.setItemRemark("Item Remark");
        originalKdsItem.setItemState(42);
        originalKdsItem.setOrderItemGuid("1234");
        originalKdsItem.setOrderRemark("Order Remark");
        originalKdsItem.setSkuCode("Sku Code");
        originalKdsItem.setSkuGuid("1234");
        originalKdsItem.setSkuName("Sku Name");
        originalKdsItem.setSkuUnit("Sku Unit");

        ItemChangesReqDTO itemChangesReqDTO = new ItemChangesReqDTO();
        itemChangesReqDTO.setAccount("3");
        itemChangesReqDTO.setAccountName("Dr Jane Doe");
        itemChangesReqDTO.setActuallyPayFee(new BigDecimal("2.3"));
        itemChangesReqDTO.setAreaGuid("1234");
        itemChangesReqDTO.setChangesKdsItem(changesKdsItem);
        itemChangesReqDTO.setCreateStaffName("Create Staff Name");
        itemChangesReqDTO.setDeviceId("42");
        itemChangesReqDTO.setDeviceType(1);
        itemChangesReqDTO.setDiningTableName("Dining Table Name");
        itemChangesReqDTO.setEnterpriseGuid("1234");
        itemChangesReqDTO.setEnterpriseName("Enterprise Name");
        itemChangesReqDTO.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        itemChangesReqDTO.setInvoiceCode("Invoice Code");
        itemChangesReqDTO.setInvoicePhone("**********");
        itemChangesReqDTO.setIsInvoiceCode(1);
        itemChangesReqDTO.setLatitude("Latitude");
        itemChangesReqDTO.setLongitude("Longitude");
        itemChangesReqDTO.setOperSubjectGuid("1234");
        itemChangesReqDTO.setOrderGuid("1234");
        itemChangesReqDTO.setOrderNo("Order No");
        itemChangesReqDTO.setOriginalKdsItem(originalKdsItem);
        itemChangesReqDTO.setRequestTimestamp(1L);
        itemChangesReqDTO.setStoreGuid("1234");
        itemChangesReqDTO.setStoreName("Store Name");
        itemChangesReqDTO.setTradeMode(1);
        itemChangesReqDTO.setUserGuid("1234");
        itemChangesReqDTO.setUserName("janedoe");
        thrown.expect(IllegalStateException.class);
        kitchenItemServiceImpl.changes(itemChangesReqDTO);
        verify(kitchenItemMapper).selectList(Mockito.<Wrapper<KitchenItemDO>>any());
        verify(displayRuleService).list(Mockito.<Wrapper<DisplayRuleDO>>any());
        verify(displayStoreService).list(Mockito.<Wrapper<DisplayStoreDO>>any());
        verify(kitchenItemDO).getGuid();
        verify(kitchenItemDO).getId();
        verify(kitchenItemDO).setGmtCreate(Mockito.<LocalDateTime>any());
        verify(kitchenItemDO).setGmtModified(Mockito.<LocalDateTime>any());
        verify(kitchenItemDO).setGuid(Mockito.<String>any());
        verify(kitchenItemDO).setId(Mockito.<Long>any());
        verify(kitchenItemDO, atLeast(1)).getAreaGuid();
        verify(kitchenItemDO).getItemState();
        verify(kitchenItemDO).setAreaGuid(Mockito.<String>any());
        verify(kitchenItemDO).setBatch(Mockito.<Integer>any());
        verify(kitchenItemDO).setCallUpTime(Mockito.<LocalDateTime>any());
        verify(kitchenItemDO).setCancelDstStaffGuid(Mockito.<String>any());
        verify(kitchenItemDO).setCancelDstStaffName(Mockito.<String>any());
        verify(kitchenItemDO).setCancelDstTime(Mockito.<LocalDateTime>any());
        verify(kitchenItemDO).setCompleteStaffGuid(Mockito.<String>any());
        verify(kitchenItemDO).setCompleteStaffName(Mockito.<String>any());
        verify(kitchenItemDO).setCompleteTime(Mockito.<LocalDateTime>any());
        verify(kitchenItemDO).setCookStaffGuid(Mockito.<String>any());
        verify(kitchenItemDO).setCookStaffName(Mockito.<String>any());
        verify(kitchenItemDO).setCookTime(Mockito.<LocalDateTime>any());
        verify(kitchenItemDO).setCurrentCount(Mockito.<BigDecimal>any());
        verify(kitchenItemDO).setDelayTimeMinutes(Mockito.<Integer>any());
        verify(kitchenItemDO).setDisplayRuleType(Mockito.<Integer>any());
        verify(kitchenItemDO).setDisplayTime(Mockito.<LocalDateTime>any());
        verify(kitchenItemDO).setDisplayType(Mockito.<Integer>any());
        verify(kitchenItemDO).setDistributeStaffGuid(Mockito.<String>any());
        verify(kitchenItemDO).setDistributeStaffName(Mockito.<String>any());
        verify(kitchenItemDO).setDistributeTime(Mockito.<LocalDateTime>any());
        verify(kitchenItemDO).setDstDeviceId(Mockito.<String>any());
        verify(kitchenItemDO).setHangUpTime(Mockito.<LocalDateTime>any());
        verify(kitchenItemDO).setIsPrintAutomatic(Mockito.<Boolean>any());
        verify(kitchenItemDO).setIsWeight(Mockito.<Boolean>any());
        verify(kitchenItemDO).setItemAttrMd5(Mockito.<String>any());
        verify(kitchenItemDO).setItemGuid(Mockito.<String>any());
        verify(kitchenItemDO).setItemName(Mockito.<String>any());
        verify(kitchenItemDO).setItemRemark(Mockito.<String>any());
        verify(kitchenItemDO).setItemState(Mockito.<Integer>any());
        verify(kitchenItemDO).setKitchenState(Mockito.<Integer>any());
        verify(kitchenItemDO).setOrderDesc(Mockito.<String>any());
        verify(kitchenItemDO).setOrderGuid(Mockito.<String>any());
        verify(kitchenItemDO).setOrderItemGuid(Mockito.<String>any());
        verify(kitchenItemDO).setOrderNumber(Mockito.<String>any());
        verify(kitchenItemDO).setOrderRemark(Mockito.<String>any());
        verify(kitchenItemDO).setOrderSerialNo(Mockito.<String>any());
        verify(kitchenItemDO).setOrderSortTime(Mockito.<LocalDateTime>any());
        verify(kitchenItemDO).setOriginalItemGuid(Mockito.<String>any());
        verify(kitchenItemDO).setOriginalItemSkuName(Mockito.<String>any());
        verify(kitchenItemDO).setOriginalSkuGuid(Mockito.<String>any());
        verify(kitchenItemDO).setPointGuid(Mockito.<String>any());
        verify(kitchenItemDO).setPrdDeviceId(Mockito.<String>any());
        verify(kitchenItemDO).setPrepareTime(Mockito.<LocalDateTime>any());
        verify(kitchenItemDO).setReturnCount(Mockito.<BigDecimal>any());
        verify(kitchenItemDO).setSkuCode(Mockito.<String>any());
        verify(kitchenItemDO).setSkuGuid(Mockito.<String>any());
        verify(kitchenItemDO).setSkuName(Mockito.<String>any());
        verify(kitchenItemDO).setSkuUnit(Mockito.<String>any());
        verify(kitchenItemDO).setSort(Mockito.<Integer>any());
        verify(kitchenItemDO).setStoreGuid(Mockito.<String>any());
        verify(kitchenItemDO).setTableGuid(Mockito.<String>any());
        verify(kitchenItemDO).setTimeout(Mockito.<Integer>any());
        verify(kitchenItemDO).setUrgedTime(Mockito.<LocalDateTime>any());
        verify(kitchenItemDO).setUrgedTimes(Mockito.<Integer>any());
        verify(distributeAreaService).queryDstAreaMap(Mockito.<String>any(), Mockito.<List<String>>any());
        verify(distributeItemService).queryDstSkuMap(Mockito.<String>any(), Mockito.<List<String>>any());
        verify(prdPointItemService).queryPrdPointByItem(Mockito.<List<String>>any(), Mockito.<String>any());
        verify(itemRpcService).findParentSkus(Mockito.<List<String>>any());
        verify(itemRpcService).kdsItemParentMapping(Mockito.<List<String>>any());
        verify(redisUtils).get(Mockito.<String>any());
    }

    /**
     * Method under test:
     * {@link KitchenItemServiceImpl#queryPrdStatus(PrdItemStatusReqDTO)}
     */
    @Test
    public void testQueryPrdStatus() {
        when(deviceConfigService.queryPrdDeviceByGuid(Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn(DeviceConfigDO.defaultConfig());
        PrdItemStatusReqDTO prdItemStatusReqDTO = mock(PrdItemStatusReqDTO.class);
        when(prdItemStatusReqDTO.getCurrentPage()).thenThrow(new IllegalStateException("foo"));
        when(prdItemStatusReqDTO.getDisplayMode()).thenReturn(1);
        when(prdItemStatusReqDTO.getDeviceId()).thenReturn("42");
        when(prdItemStatusReqDTO.getStoreGuid()).thenReturn("1234");
        doNothing().when(prdItemStatusReqDTO).setCurrentPage(anyLong());
        doNothing().when(prdItemStatusReqDTO).setPageSize(anyLong());
        doNothing().when(prdItemStatusReqDTO).setDeviceId(Mockito.<String>any());
        doNothing().when(prdItemStatusReqDTO).setDisplayMode(Mockito.<Integer>any());
        doNothing().when(prdItemStatusReqDTO).setDisplayType(Mockito.<String>any());
        doNothing().when(prdItemStatusReqDTO).setKitchenState(Mockito.<Integer>any());
        doNothing().when(prdItemStatusReqDTO).setPointGuid(Mockito.<String>any());
        doNothing().when(prdItemStatusReqDTO).setQueryModeForPoint(Mockito.<Integer>any());
        doNothing().when(prdItemStatusReqDTO).setStoreGuid(Mockito.<String>any());
        prdItemStatusReqDTO.setCurrentPage(1L);
        prdItemStatusReqDTO.setDeviceId("42");
        prdItemStatusReqDTO.setDisplayMode(1);
        prdItemStatusReqDTO.setDisplayType("Display Type");
        prdItemStatusReqDTO.setKitchenState(1);
        prdItemStatusReqDTO.setPageSize(3L);
        prdItemStatusReqDTO.setPointGuid("1234");
        prdItemStatusReqDTO.setQueryModeForPoint(1);
        prdItemStatusReqDTO.setStoreGuid("1234");
        kitchenItemServiceImpl.queryPrdStatus(prdItemStatusReqDTO);
        verify(prdItemStatusReqDTO).getCurrentPage();
        verify(prdItemStatusReqDTO).setCurrentPage(anyLong());
        verify(prdItemStatusReqDTO).setPageSize(anyLong());
        verify(prdItemStatusReqDTO).getDeviceId();
        verify(prdItemStatusReqDTO).getDisplayMode();
        verify(prdItemStatusReqDTO).getStoreGuid();
        verify(prdItemStatusReqDTO).setDeviceId(Mockito.<String>any());
        verify(prdItemStatusReqDTO).setDisplayMode(Mockito.<Integer>any());
        verify(prdItemStatusReqDTO).setDisplayType(Mockito.<String>any());
        verify(prdItemStatusReqDTO).setKitchenState(Mockito.<Integer>any());
        verify(prdItemStatusReqDTO).setPointGuid(Mockito.<String>any());
        verify(prdItemStatusReqDTO).setQueryModeForPoint(Mockito.<Integer>any());
        verify(prdItemStatusReqDTO).setStoreGuid(Mockito.<String>any());
        verify(deviceConfigService).queryPrdDeviceByGuid(Mockito.<String>any(), Mockito.<String>any());
    }

    /**
     * Method under test:
     * {@link KitchenItemServiceImpl#queryPrdStatus(PrdItemStatusReqDTO)}
     */
    @Test
    public void testQueryPrdStatus2() {
        when(deviceConfigService.queryPrdDeviceByGuid(Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn(DeviceConfigDO.defaultConfig());
        PrdItemStatusReqDTO prdItemStatusReqDTO = mock(PrdItemStatusReqDTO.class);
        when(prdItemStatusReqDTO.getCurrentPage()).thenThrow(new IllegalStateException("foo"));
        when(prdItemStatusReqDTO.getDisplayMode()).thenReturn(4);
        when(prdItemStatusReqDTO.getDeviceId()).thenReturn("42");
        when(prdItemStatusReqDTO.getStoreGuid()).thenReturn("1234");
        doNothing().when(prdItemStatusReqDTO).setCurrentPage(anyLong());
        doNothing().when(prdItemStatusReqDTO).setPageSize(anyLong());
        doNothing().when(prdItemStatusReqDTO).setDeviceId(Mockito.<String>any());
        doNothing().when(prdItemStatusReqDTO).setDisplayMode(Mockito.<Integer>any());
        doNothing().when(prdItemStatusReqDTO).setDisplayType(Mockito.<String>any());
        doNothing().when(prdItemStatusReqDTO).setKitchenState(Mockito.<Integer>any());
        doNothing().when(prdItemStatusReqDTO).setPointGuid(Mockito.<String>any());
        doNothing().when(prdItemStatusReqDTO).setQueryModeForPoint(Mockito.<Integer>any());
        doNothing().when(prdItemStatusReqDTO).setStoreGuid(Mockito.<String>any());
        prdItemStatusReqDTO.setCurrentPage(1L);
        prdItemStatusReqDTO.setDeviceId("42");
        prdItemStatusReqDTO.setDisplayMode(1);
        prdItemStatusReqDTO.setDisplayType("Display Type");
        prdItemStatusReqDTO.setKitchenState(1);
        prdItemStatusReqDTO.setPageSize(3L);
        prdItemStatusReqDTO.setPointGuid("1234");
        prdItemStatusReqDTO.setQueryModeForPoint(1);
        prdItemStatusReqDTO.setStoreGuid("1234");
        kitchenItemServiceImpl.queryPrdStatus(prdItemStatusReqDTO);
        verify(prdItemStatusReqDTO).getCurrentPage();
        verify(prdItemStatusReqDTO, atLeast(1)).setCurrentPage(anyLong());
        verify(prdItemStatusReqDTO, atLeast(1)).setPageSize(anyLong());
        verify(prdItemStatusReqDTO).getDeviceId();
        verify(prdItemStatusReqDTO).getDisplayMode();
        verify(prdItemStatusReqDTO).getStoreGuid();
        verify(prdItemStatusReqDTO).setDeviceId(Mockito.<String>any());
        verify(prdItemStatusReqDTO).setDisplayMode(Mockito.<Integer>any());
        verify(prdItemStatusReqDTO).setDisplayType(Mockito.<String>any());
        verify(prdItemStatusReqDTO).setKitchenState(Mockito.<Integer>any());
        verify(prdItemStatusReqDTO, atLeast(1)).setPointGuid(Mockito.<String>any());
        verify(prdItemStatusReqDTO).setQueryModeForPoint(Mockito.<Integer>any());
        verify(prdItemStatusReqDTO).setStoreGuid(Mockito.<String>any());
        verify(deviceConfigService).queryPrdDeviceByGuid(Mockito.<String>any(), Mockito.<String>any());
    }

    /**
     * Method under test:
     * {@link KitchenItemServiceImpl#queryDstStatus(DstItemStatusReqDTO)}
     */
    @Test
    public void testQueryDstStatus() {
        when(kitchenItemMapper.pageDstItemGroup(Mockito.<PageAdapter<KitchenItemReadDO>>any(), Mockito.<ItemQuery>any()))
                .thenReturn(new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>());
        doNothing().when(deviceConfigService).assertThatDeviceExists(Mockito.<String>any(), Mockito.<String>any());
        DstItemStatusReqDTO dstItemStatusReqDTO = mock(DstItemStatusReqDTO.class);
        when(dstItemStatusReqDTO.getCurrentPage()).thenReturn(1L);
        when(dstItemStatusReqDTO.getPageSize()).thenReturn(3L);
        when(dstItemStatusReqDTO.getDisplayMode()).thenReturn(1);
        when(dstItemStatusReqDTO.getDeviceId()).thenReturn("42");
        when(dstItemStatusReqDTO.getDisplayType()).thenReturn("42");
        when(dstItemStatusReqDTO.getStoreGuid()).thenReturn("1234");
        doNothing().when(dstItemStatusReqDTO).setCurrentPage(anyLong());
        doNothing().when(dstItemStatusReqDTO).setPageSize(anyLong());
        doNothing().when(dstItemStatusReqDTO).setDeviceId(Mockito.<String>any());
        doNothing().when(dstItemStatusReqDTO).setDisplayMode(Mockito.<Integer>any());
        doNothing().when(dstItemStatusReqDTO).setDisplayType(Mockito.<String>any());
        doNothing().when(dstItemStatusReqDTO).setStoreGuid(Mockito.<String>any());
        dstItemStatusReqDTO.setCurrentPage(1L);
        dstItemStatusReqDTO.setDeviceId("42");
        dstItemStatusReqDTO.setDisplayMode(1);
        dstItemStatusReqDTO.setDisplayType("Display Type");
        dstItemStatusReqDTO.setPageSize(3L);
        dstItemStatusReqDTO.setStoreGuid("1234");
        PrdDstRespDTO actualQueryDstStatusResult = kitchenItemServiceImpl.queryDstStatus(dstItemStatusReqDTO);
        verify(dstItemStatusReqDTO).getCurrentPage();
        verify(dstItemStatusReqDTO).getPageSize();
        verify(dstItemStatusReqDTO).setCurrentPage(anyLong());
        verify(dstItemStatusReqDTO).setPageSize(anyLong());
        verify(dstItemStatusReqDTO, atLeast(1)).getDeviceId();
        verify(dstItemStatusReqDTO).getDisplayMode();
        verify(dstItemStatusReqDTO).getDisplayType();
        verify(dstItemStatusReqDTO, atLeast(1)).getStoreGuid();
        verify(dstItemStatusReqDTO).setDeviceId(Mockito.<String>any());
        verify(dstItemStatusReqDTO).setDisplayMode(Mockito.<Integer>any());
        verify(dstItemStatusReqDTO).setDisplayType(Mockito.<String>any());
        verify(dstItemStatusReqDTO).setStoreGuid(Mockito.<String>any());
        verify(kitchenItemMapper).pageDstItemGroup(Mockito.<PageAdapter<KitchenItemReadDO>>any(), Mockito.<ItemQuery>any());
        verify(deviceConfigService).assertThatDeviceExists(Mockito.<String>any(), Mockito.<String>any());
        com.holderzone.framework.util.Page<PrdDstItemDTO> items = actualQueryDstStatusResult.getItems();
        assertTrue(items instanceof PageAdapter);
        assertEquals(0L, items.getTotalCount());
        assertEquals(3L, ((PageAdapter<PrdDstItemDTO>) items).getSize());
        assertFalse(((PageAdapter<PrdDstItemDTO>) items).hasPrevious());
        assertTrue(((PageAdapter<PrdDstItemDTO>) items).optimizeCountSql());
        assertTrue(((PageAdapter<PrdDstItemDTO>) items).getRecords().isEmpty());
    }

    /**
     * Method under test:
     * {@link KitchenItemServiceImpl#queryDstStatus(DstItemStatusReqDTO)}
     */
    @Test
    public void testQueryDstStatus2() {
        doNothing().when(deviceConfigService).assertThatDeviceExists(Mockito.<String>any(), Mockito.<String>any());
        DstItemStatusReqDTO dstItemStatusReqDTO = mock(DstItemStatusReqDTO.class);
        when(dstItemStatusReqDTO.getCurrentPage()).thenThrow(new IllegalStateException("dst汇总-itemGroupPage={}"));
        when(dstItemStatusReqDTO.getDisplayMode()).thenReturn(1);
        when(dstItemStatusReqDTO.getDeviceId()).thenReturn("42");
        when(dstItemStatusReqDTO.getDisplayType()).thenReturn("42");
        when(dstItemStatusReqDTO.getStoreGuid()).thenReturn("1234");
        doNothing().when(dstItemStatusReqDTO).setCurrentPage(anyLong());
        doNothing().when(dstItemStatusReqDTO).setPageSize(anyLong());
        doNothing().when(dstItemStatusReqDTO).setDeviceId(Mockito.<String>any());
        doNothing().when(dstItemStatusReqDTO).setDisplayMode(Mockito.<Integer>any());
        doNothing().when(dstItemStatusReqDTO).setDisplayType(Mockito.<String>any());
        doNothing().when(dstItemStatusReqDTO).setStoreGuid(Mockito.<String>any());
        dstItemStatusReqDTO.setCurrentPage(1L);
        dstItemStatusReqDTO.setDeviceId("42");
        dstItemStatusReqDTO.setDisplayMode(1);
        dstItemStatusReqDTO.setDisplayType("Display Type");
        dstItemStatusReqDTO.setPageSize(3L);
        dstItemStatusReqDTO.setStoreGuid("1234");
        kitchenItemServiceImpl.queryDstStatus(dstItemStatusReqDTO);
        verify(dstItemStatusReqDTO).getCurrentPage();
        verify(dstItemStatusReqDTO).setCurrentPage(anyLong());
        verify(dstItemStatusReqDTO).setPageSize(anyLong());
        verify(dstItemStatusReqDTO, atLeast(1)).getDeviceId();
        verify(dstItemStatusReqDTO).getDisplayMode();
        verify(dstItemStatusReqDTO).getDisplayType();
        verify(dstItemStatusReqDTO, atLeast(1)).getStoreGuid();
        verify(dstItemStatusReqDTO).setDeviceId(Mockito.<String>any());
        verify(dstItemStatusReqDTO).setDisplayMode(Mockito.<Integer>any());
        verify(dstItemStatusReqDTO).setDisplayType(Mockito.<String>any());
        verify(dstItemStatusReqDTO).setStoreGuid(Mockito.<String>any());
        verify(deviceConfigService).assertThatDeviceExists(Mockito.<String>any(), Mockito.<String>any());
    }

    /**
     * Method under test:
     * {@link KitchenItemServiceImpl#queryDstStatus(DstItemStatusReqDTO)}
     */
    @Test
    public void testQueryDstStatus3() {
        when(kitchenItemMapper.pageDstItemGroup(Mockito.<PageAdapter<KitchenItemReadDO>>any(), Mockito.<ItemQuery>any()))
                .thenReturn(new MPPage<>());
        doNothing().when(deviceConfigService).assertThatDeviceExists(Mockito.<String>any(), Mockito.<String>any());
        DstItemStatusReqDTO dstItemStatusReqDTO = mock(DstItemStatusReqDTO.class);
        when(dstItemStatusReqDTO.getCurrentPage()).thenReturn(1L);
        when(dstItemStatusReqDTO.getPageSize()).thenReturn(3L);
        when(dstItemStatusReqDTO.getDisplayMode()).thenReturn(1);
        when(dstItemStatusReqDTO.getDeviceId()).thenReturn("42");
        when(dstItemStatusReqDTO.getDisplayType()).thenReturn("42");
        when(dstItemStatusReqDTO.getStoreGuid()).thenReturn("1234");
        doNothing().when(dstItemStatusReqDTO).setCurrentPage(anyLong());
        doNothing().when(dstItemStatusReqDTO).setPageSize(anyLong());
        doNothing().when(dstItemStatusReqDTO).setDeviceId(Mockito.<String>any());
        doNothing().when(dstItemStatusReqDTO).setDisplayMode(Mockito.<Integer>any());
        doNothing().when(dstItemStatusReqDTO).setDisplayType(Mockito.<String>any());
        doNothing().when(dstItemStatusReqDTO).setStoreGuid(Mockito.<String>any());
        dstItemStatusReqDTO.setCurrentPage(1L);
        dstItemStatusReqDTO.setDeviceId("42");
        dstItemStatusReqDTO.setDisplayMode(1);
        dstItemStatusReqDTO.setDisplayType("Display Type");
        dstItemStatusReqDTO.setPageSize(3L);
        dstItemStatusReqDTO.setStoreGuid("1234");
        PrdDstRespDTO actualQueryDstStatusResult = kitchenItemServiceImpl.queryDstStatus(dstItemStatusReqDTO);
        verify(dstItemStatusReqDTO).getCurrentPage();
        verify(dstItemStatusReqDTO).getPageSize();
        verify(dstItemStatusReqDTO).setCurrentPage(anyLong());
        verify(dstItemStatusReqDTO).setPageSize(anyLong());
        verify(dstItemStatusReqDTO, atLeast(1)).getDeviceId();
        verify(dstItemStatusReqDTO).getDisplayMode();
        verify(dstItemStatusReqDTO).getDisplayType();
        verify(dstItemStatusReqDTO, atLeast(1)).getStoreGuid();
        verify(dstItemStatusReqDTO).setDeviceId(Mockito.<String>any());
        verify(dstItemStatusReqDTO).setDisplayMode(Mockito.<Integer>any());
        verify(dstItemStatusReqDTO).setDisplayType(Mockito.<String>any());
        verify(dstItemStatusReqDTO).setStoreGuid(Mockito.<String>any());
        verify(kitchenItemMapper).pageDstItemGroup(Mockito.<PageAdapter<KitchenItemReadDO>>any(), Mockito.<ItemQuery>any());
        verify(deviceConfigService).assertThatDeviceExists(Mockito.<String>any(), Mockito.<String>any());
        Page<PrdDstItemDTO> items = actualQueryDstStatusResult.getItems();
        assertTrue(items instanceof PageAdapter);
        assertEquals(0L, items.getTotalCount());
        assertEquals(3L, ((PageAdapter<PrdDstItemDTO>) items).getSize());
        assertFalse(((PageAdapter<PrdDstItemDTO>) items).hasPrevious());
        assertTrue(((PageAdapter<PrdDstItemDTO>) items).optimizeCountSql());
        assertTrue(((PageAdapter<PrdDstItemDTO>) items).getRecords().isEmpty());
    }

    /**
     * Method under test:
     * {@link KitchenItemServiceImpl#queryDstStatus(DstItemStatusReqDTO)}
     */
    @Test
    public void testQueryDstStatus4() {
        when(kitchenItemMapper.pageDstItemGroup(Mockito.<PageAdapter<KitchenItemReadDO>>any(), Mockito.<ItemQuery>any()))
                .thenReturn(new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(4L, 3L, 4L));
        doNothing().when(deviceConfigService).assertThatDeviceExists(Mockito.<String>any(), Mockito.<String>any());
        DstItemStatusReqDTO dstItemStatusReqDTO = mock(DstItemStatusReqDTO.class);
        when(dstItemStatusReqDTO.getCurrentPage()).thenReturn(1L);
        when(dstItemStatusReqDTO.getPageSize()).thenReturn(3L);
        when(dstItemStatusReqDTO.getDisplayMode()).thenReturn(1);
        when(dstItemStatusReqDTO.getDeviceId()).thenReturn("42");
        when(dstItemStatusReqDTO.getDisplayType()).thenReturn("42");
        when(dstItemStatusReqDTO.getStoreGuid()).thenReturn("1234");
        doNothing().when(dstItemStatusReqDTO).setCurrentPage(anyLong());
        doNothing().when(dstItemStatusReqDTO).setPageSize(anyLong());
        doNothing().when(dstItemStatusReqDTO).setDeviceId(Mockito.<String>any());
        doNothing().when(dstItemStatusReqDTO).setDisplayMode(Mockito.<Integer>any());
        doNothing().when(dstItemStatusReqDTO).setDisplayType(Mockito.<String>any());
        doNothing().when(dstItemStatusReqDTO).setStoreGuid(Mockito.<String>any());
        dstItemStatusReqDTO.setCurrentPage(1L);
        dstItemStatusReqDTO.setDeviceId("42");
        dstItemStatusReqDTO.setDisplayMode(1);
        dstItemStatusReqDTO.setDisplayType("Display Type");
        dstItemStatusReqDTO.setPageSize(3L);
        dstItemStatusReqDTO.setStoreGuid("1234");
        PrdDstRespDTO actualQueryDstStatusResult = kitchenItemServiceImpl.queryDstStatus(dstItemStatusReqDTO);
        verify(dstItemStatusReqDTO).getCurrentPage();
        verify(dstItemStatusReqDTO).getPageSize();
        verify(dstItemStatusReqDTO).setCurrentPage(anyLong());
        verify(dstItemStatusReqDTO).setPageSize(anyLong());
        verify(dstItemStatusReqDTO, atLeast(1)).getDeviceId();
        verify(dstItemStatusReqDTO).getDisplayMode();
        verify(dstItemStatusReqDTO).getDisplayType();
        verify(dstItemStatusReqDTO, atLeast(1)).getStoreGuid();
        verify(dstItemStatusReqDTO).setDeviceId(Mockito.<String>any());
        verify(dstItemStatusReqDTO).setDisplayMode(Mockito.<Integer>any());
        verify(dstItemStatusReqDTO).setDisplayType(Mockito.<String>any());
        verify(dstItemStatusReqDTO).setStoreGuid(Mockito.<String>any());
        verify(kitchenItemMapper).pageDstItemGroup(Mockito.<PageAdapter<KitchenItemReadDO>>any(), Mockito.<ItemQuery>any());
        verify(deviceConfigService).assertThatDeviceExists(Mockito.<String>any(), Mockito.<String>any());
        com.holderzone.framework.util.Page<PrdDstItemDTO> items = actualQueryDstStatusResult.getItems();
        assertTrue(items instanceof PageAdapter);
        assertEquals(0L, items.getTotalCount());
        assertEquals(3L, ((PageAdapter<PrdDstItemDTO>) items).getSize());
        assertFalse(((PageAdapter<PrdDstItemDTO>) items).hasPrevious());
        assertTrue(((PageAdapter<PrdDstItemDTO>) items).optimizeCountSql());
        assertTrue(((PageAdapter<PrdDstItemDTO>) items).getRecords().isEmpty());
    }

    /**
     * Method under test:
     * {@link KitchenItemServiceImpl#queryDstStatus(DstItemStatusReqDTO)}
     */
    @Test
    public void testQueryDstStatus5() {
        when(kitchenItemMapper.pageDstItemGroup(Mockito.<PageAdapter<KitchenItemReadDO>>any(), Mockito.<ItemQuery>any()))
                .thenReturn(new PageAdapter<>());
        doNothing().when(deviceConfigService).assertThatDeviceExists(Mockito.<String>any(), Mockito.<String>any());
        DstItemStatusReqDTO dstItemStatusReqDTO = mock(DstItemStatusReqDTO.class);
        when(dstItemStatusReqDTO.getCurrentPage()).thenReturn(1L);
        when(dstItemStatusReqDTO.getPageSize()).thenReturn(3L);
        when(dstItemStatusReqDTO.getDisplayMode()).thenReturn(1);
        when(dstItemStatusReqDTO.getDeviceId()).thenReturn("42");
        when(dstItemStatusReqDTO.getDisplayType()).thenReturn("42");
        when(dstItemStatusReqDTO.getStoreGuid()).thenReturn("1234");
        doNothing().when(dstItemStatusReqDTO).setCurrentPage(anyLong());
        doNothing().when(dstItemStatusReqDTO).setPageSize(anyLong());
        doNothing().when(dstItemStatusReqDTO).setDeviceId(Mockito.<String>any());
        doNothing().when(dstItemStatusReqDTO).setDisplayMode(Mockito.<Integer>any());
        doNothing().when(dstItemStatusReqDTO).setDisplayType(Mockito.<String>any());
        doNothing().when(dstItemStatusReqDTO).setStoreGuid(Mockito.<String>any());
        dstItemStatusReqDTO.setCurrentPage(1L);
        dstItemStatusReqDTO.setDeviceId("42");
        dstItemStatusReqDTO.setDisplayMode(1);
        dstItemStatusReqDTO.setDisplayType("Display Type");
        dstItemStatusReqDTO.setPageSize(3L);
        dstItemStatusReqDTO.setStoreGuid("1234");
        PrdDstRespDTO actualQueryDstStatusResult = kitchenItemServiceImpl.queryDstStatus(dstItemStatusReqDTO);
        verify(dstItemStatusReqDTO).getCurrentPage();
        verify(dstItemStatusReqDTO).getPageSize();
        verify(dstItemStatusReqDTO).setCurrentPage(anyLong());
        verify(dstItemStatusReqDTO).setPageSize(anyLong());
        verify(dstItemStatusReqDTO, atLeast(1)).getDeviceId();
        verify(dstItemStatusReqDTO).getDisplayMode();
        verify(dstItemStatusReqDTO).getDisplayType();
        verify(dstItemStatusReqDTO, atLeast(1)).getStoreGuid();
        verify(dstItemStatusReqDTO).setDeviceId(Mockito.<String>any());
        verify(dstItemStatusReqDTO).setDisplayMode(Mockito.<Integer>any());
        verify(dstItemStatusReqDTO).setDisplayType(Mockito.<String>any());
        verify(dstItemStatusReqDTO).setStoreGuid(Mockito.<String>any());
        verify(kitchenItemMapper).pageDstItemGroup(Mockito.<PageAdapter<KitchenItemReadDO>>any(), Mockito.<ItemQuery>any());
        verify(deviceConfigService).assertThatDeviceExists(Mockito.<String>any(), Mockito.<String>any());
        Page<PrdDstItemDTO> items = actualQueryDstStatusResult.getItems();
        assertTrue(items instanceof PageAdapter);
        assertEquals(0L, items.getTotalCount());
        assertEquals(3L, ((PageAdapter<PrdDstItemDTO>) items).getSize());
        assertFalse(((PageAdapter<PrdDstItemDTO>) items).hasPrevious());
        assertTrue(((PageAdapter<PrdDstItemDTO>) items).optimizeCountSql());
        assertTrue(((PageAdapter<PrdDstItemDTO>) items).getRecords().isEmpty());
    }

    /**
     * Method under test:
     * {@link KitchenItemServiceImpl#queryDstStatus(DstItemStatusReqDTO)}
     */
    @Test
    public void testQueryDstStatus6() {
        when(kitchenItemMapper.queryDstItemByOrder(Mockito.<ItemQuery>any()))
                .thenThrow(new IllegalArgumentException("出堂 订单模式 prdItem={}"));
        when(kitchenItemMapper.pageDstOrderItem(Mockito.<PageAdapter<PrdDstOrderDTO>>any(), Mockito.<ItemQuery>any()))
                .thenReturn(new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>());
        doNothing().when(deviceConfigService).assertThatDeviceExists(Mockito.<String>any(), Mockito.<String>any());
        DstItemStatusReqDTO dstItemStatusReqDTO = mock(DstItemStatusReqDTO.class);
        when(dstItemStatusReqDTO.getCurrentPage()).thenReturn(1L);
        when(dstItemStatusReqDTO.getPageSize()).thenReturn(3L);
        when(dstItemStatusReqDTO.getDisplayMode()).thenReturn(4);
        when(dstItemStatusReqDTO.getDeviceId()).thenReturn("42");
        when(dstItemStatusReqDTO.getDisplayType()).thenReturn("42");
        when(dstItemStatusReqDTO.getStoreGuid()).thenReturn("1234");
        doNothing().when(dstItemStatusReqDTO).setCurrentPage(anyLong());
        doNothing().when(dstItemStatusReqDTO).setPageSize(anyLong());
        doNothing().when(dstItemStatusReqDTO).setDeviceId(Mockito.<String>any());
        doNothing().when(dstItemStatusReqDTO).setDisplayMode(Mockito.<Integer>any());
        doNothing().when(dstItemStatusReqDTO).setDisplayType(Mockito.<String>any());
        doNothing().when(dstItemStatusReqDTO).setStoreGuid(Mockito.<String>any());
        dstItemStatusReqDTO.setCurrentPage(1L);
        dstItemStatusReqDTO.setDeviceId("42");
        dstItemStatusReqDTO.setDisplayMode(1);
        dstItemStatusReqDTO.setDisplayType("Display Type");
        dstItemStatusReqDTO.setPageSize(3L);
        dstItemStatusReqDTO.setStoreGuid("1234");
        thrown.expect(IllegalArgumentException.class);
        kitchenItemServiceImpl.queryDstStatus(dstItemStatusReqDTO);
        verify(dstItemStatusReqDTO).getCurrentPage();
        verify(dstItemStatusReqDTO).getPageSize();
        verify(dstItemStatusReqDTO, atLeast(1)).setCurrentPage(anyLong());
        verify(dstItemStatusReqDTO, atLeast(1)).setPageSize(anyLong());
        verify(dstItemStatusReqDTO, atLeast(1)).getDeviceId();
        verify(dstItemStatusReqDTO).getDisplayMode();
        verify(dstItemStatusReqDTO).getDisplayType();
        verify(dstItemStatusReqDTO, atLeast(1)).getStoreGuid();
        verify(dstItemStatusReqDTO).setDeviceId(Mockito.<String>any());
        verify(dstItemStatusReqDTO).setDisplayMode(Mockito.<Integer>any());
        verify(dstItemStatusReqDTO).setDisplayType(Mockito.<String>any());
        verify(dstItemStatusReqDTO).setStoreGuid(Mockito.<String>any());
        verify(kitchenItemMapper).pageDstOrderItem(Mockito.<PageAdapter<PrdDstOrderDTO>>any(), Mockito.<ItemQuery>any());
        verify(kitchenItemMapper).queryDstItemByOrder(Mockito.<ItemQuery>any());
        verify(deviceConfigService).assertThatDeviceExists(Mockito.<String>any(), Mockito.<String>any());
    }

    /**
     * Method under test:
     * {@link KitchenItemServiceImpl#queryDstStatus(DstItemStatusReqDTO)}
     */
    @Test
    public void testQueryDstStatus7() {
        when(kitchenItemMapper.queryDstItem(Mockito.<ItemQuery>any())).thenReturn(new ArrayList<>());
        doNothing().when(deviceConfigService).assertThatDeviceExists(Mockito.<String>any(), Mockito.<String>any());
        DstItemStatusReqDTO dstItemStatusReqDTO = mock(DstItemStatusReqDTO.class);
        when(dstItemStatusReqDTO.getCurrentPage()).thenReturn(1L);
        when(dstItemStatusReqDTO.getPageSize()).thenReturn(3L);
        when(dstItemStatusReqDTO.getDisplayMode()).thenReturn(-1);
        when(dstItemStatusReqDTO.getDeviceId()).thenReturn("42");
        when(dstItemStatusReqDTO.getDisplayType()).thenReturn("42");
        when(dstItemStatusReqDTO.getStoreGuid()).thenReturn("1234");
        doNothing().when(dstItemStatusReqDTO).setCurrentPage(anyLong());
        doNothing().when(dstItemStatusReqDTO).setPageSize(anyLong());
        doNothing().when(dstItemStatusReqDTO).setDeviceId(Mockito.<String>any());
        doNothing().when(dstItemStatusReqDTO).setDisplayMode(Mockito.<Integer>any());
        doNothing().when(dstItemStatusReqDTO).setDisplayType(Mockito.<String>any());
        doNothing().when(dstItemStatusReqDTO).setStoreGuid(Mockito.<String>any());
        dstItemStatusReqDTO.setCurrentPage(1L);
        dstItemStatusReqDTO.setDeviceId("42");
        dstItemStatusReqDTO.setDisplayMode(1);
        dstItemStatusReqDTO.setDisplayType("Display Type");
        dstItemStatusReqDTO.setPageSize(3L);
        dstItemStatusReqDTO.setStoreGuid("1234");
        PrdDstRespDTO actualQueryDstStatusResult = kitchenItemServiceImpl.queryDstStatus(dstItemStatusReqDTO);
        verify(dstItemStatusReqDTO, atLeast(1)).getCurrentPage();
        verify(dstItemStatusReqDTO, atLeast(1)).getPageSize();
        verify(dstItemStatusReqDTO, atLeast(1)).setCurrentPage(anyLong());
        verify(dstItemStatusReqDTO, atLeast(1)).setPageSize(anyLong());
        verify(dstItemStatusReqDTO, atLeast(1)).getDeviceId();
        verify(dstItemStatusReqDTO).getDisplayMode();
        verify(dstItemStatusReqDTO).getDisplayType();
        verify(dstItemStatusReqDTO, atLeast(1)).getStoreGuid();
        verify(dstItemStatusReqDTO).setDeviceId(Mockito.<String>any());
        verify(dstItemStatusReqDTO).setDisplayMode(Mockito.<Integer>any());
        verify(dstItemStatusReqDTO).setDisplayType(Mockito.<String>any());
        verify(dstItemStatusReqDTO).setStoreGuid(Mockito.<String>any());
        verify(kitchenItemMapper).queryDstItem(Mockito.<ItemQuery>any());
        verify(deviceConfigService).assertThatDeviceExists(Mockito.<String>any(), Mockito.<String>any());
        Page<PrdDstItemDTO> items = actualQueryDstStatusResult.getItems();
        assertTrue(items instanceof PageAdapter);
        Page<PrdDstOrderDTO> orders = actualQueryDstStatusResult.getOrders();
        assertTrue(orders instanceof PageAdapter);
        assertEquals(0L, items.getTotalCount());
        assertEquals(0L, orders.getTotalCount());
        assertEquals(3L, items.getPageSize());
        assertEquals(3L, ((PageAdapter<PrdDstOrderDTO>) orders).getSize());
        assertFalse(((PageAdapter<PrdDstItemDTO>) items).hasPrevious());
        assertFalse(((PageAdapter<PrdDstOrderDTO>) orders).hasPrevious());
        assertTrue(((PageAdapter<PrdDstItemDTO>) items).optimizeCountSql());
        assertTrue(((PageAdapter<PrdDstOrderDTO>) orders).optimizeCountSql());
        assertTrue(((PageAdapter<PrdDstItemDTO>) items).getRecords().isEmpty());
        assertTrue(((PageAdapter<PrdDstOrderDTO>) orders).getRecords().isEmpty());
    }

    /**
     * Method under test:
     * {@link KitchenItemServiceImpl#queryDstStatus(DstItemStatusReqDTO)}
     */
    @Test
    public void testQueryDstStatus8() {
        when(kitchenItemMapper.pageDstItemAll(Mockito.<PageAdapter<KitchenItemReadDO>>any(), Mockito.<ItemQuery>any()))
                .thenReturn(new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>());
        doNothing().when(deviceConfigService).assertThatDeviceExists(Mockito.<String>any(), Mockito.<String>any());
        DstItemStatusReqDTO dstItemStatusReqDTO = mock(DstItemStatusReqDTO.class);
        when(dstItemStatusReqDTO.getCurrentPage()).thenReturn(1L);
        when(dstItemStatusReqDTO.getPageSize()).thenReturn(3L);
        when(dstItemStatusReqDTO.getDisplayMode()).thenReturn(0);
        when(dstItemStatusReqDTO.getDeviceId()).thenReturn("42");
        when(dstItemStatusReqDTO.getDisplayType()).thenReturn("42");
        when(dstItemStatusReqDTO.getStoreGuid()).thenReturn("1234");
        doNothing().when(dstItemStatusReqDTO).setCurrentPage(anyLong());
        doNothing().when(dstItemStatusReqDTO).setPageSize(anyLong());
        doNothing().when(dstItemStatusReqDTO).setDeviceId(Mockito.<String>any());
        doNothing().when(dstItemStatusReqDTO).setDisplayMode(Mockito.<Integer>any());
        doNothing().when(dstItemStatusReqDTO).setDisplayType(Mockito.<String>any());
        doNothing().when(dstItemStatusReqDTO).setStoreGuid(Mockito.<String>any());
        dstItemStatusReqDTO.setCurrentPage(1L);
        dstItemStatusReqDTO.setDeviceId("42");
        dstItemStatusReqDTO.setDisplayMode(1);
        dstItemStatusReqDTO.setDisplayType("Display Type");
        dstItemStatusReqDTO.setPageSize(3L);
        dstItemStatusReqDTO.setStoreGuid("1234");
        PrdDstRespDTO actualQueryDstStatusResult = kitchenItemServiceImpl.queryDstStatus(dstItemStatusReqDTO);
        verify(dstItemStatusReqDTO).getCurrentPage();
        verify(dstItemStatusReqDTO).getPageSize();
        verify(dstItemStatusReqDTO).setCurrentPage(anyLong());
        verify(dstItemStatusReqDTO).setPageSize(anyLong());
        verify(dstItemStatusReqDTO, atLeast(1)).getDeviceId();
        verify(dstItemStatusReqDTO).getDisplayMode();
        verify(dstItemStatusReqDTO).getDisplayType();
        verify(dstItemStatusReqDTO, atLeast(1)).getStoreGuid();
        verify(dstItemStatusReqDTO).setDeviceId(Mockito.<String>any());
        verify(dstItemStatusReqDTO).setDisplayMode(Mockito.<Integer>any());
        verify(dstItemStatusReqDTO).setDisplayType(Mockito.<String>any());
        verify(dstItemStatusReqDTO).setStoreGuid(Mockito.<String>any());
        verify(kitchenItemMapper).pageDstItemAll(Mockito.<PageAdapter<KitchenItemReadDO>>any(), Mockito.<ItemQuery>any());
        verify(deviceConfigService).assertThatDeviceExists(Mockito.<String>any(), Mockito.<String>any());
        com.holderzone.framework.util.Page<PrdDstItemDTO> items = actualQueryDstStatusResult.getItems();
        assertTrue(items instanceof PageAdapter);
        assertEquals(0L, items.getTotalCount());
        assertEquals(3L, ((PageAdapter<PrdDstItemDTO>) items).getSize());
        assertFalse(((PageAdapter<PrdDstItemDTO>) items).hasPrevious());
        assertTrue(((PageAdapter<PrdDstItemDTO>) items).optimizeCountSql());
        assertTrue(((PageAdapter<PrdDstItemDTO>) items).getRecords().isEmpty());
    }

    /**
     * Method under test:
     * {@link KitchenItemServiceImpl#queryDstStatus(DstItemStatusReqDTO)}
     */
    @Test
    public void testQueryDstStatus9() {
        when(kitchenItemMapper.pageDstItemAll(Mockito.<PageAdapter<KitchenItemReadDO>>any(), Mockito.<ItemQuery>any()))
                .thenReturn(new PageAdapter<>());
        doNothing().when(deviceConfigService).assertThatDeviceExists(Mockito.<String>any(), Mockito.<String>any());
        DstItemStatusReqDTO dstItemStatusReqDTO = mock(DstItemStatusReqDTO.class);
        when(dstItemStatusReqDTO.getCurrentPage()).thenReturn(1L);
        when(dstItemStatusReqDTO.getPageSize()).thenReturn(3L);
        when(dstItemStatusReqDTO.getDisplayMode()).thenReturn(0);
        when(dstItemStatusReqDTO.getDeviceId()).thenReturn("42");
        when(dstItemStatusReqDTO.getDisplayType()).thenReturn("42");
        when(dstItemStatusReqDTO.getStoreGuid()).thenReturn("1234");
        doNothing().when(dstItemStatusReqDTO).setCurrentPage(anyLong());
        doNothing().when(dstItemStatusReqDTO).setPageSize(anyLong());
        doNothing().when(dstItemStatusReqDTO).setDeviceId(Mockito.<String>any());
        doNothing().when(dstItemStatusReqDTO).setDisplayMode(Mockito.<Integer>any());
        doNothing().when(dstItemStatusReqDTO).setDisplayType(Mockito.<String>any());
        doNothing().when(dstItemStatusReqDTO).setStoreGuid(Mockito.<String>any());
        dstItemStatusReqDTO.setCurrentPage(1L);
        dstItemStatusReqDTO.setDeviceId("42");
        dstItemStatusReqDTO.setDisplayMode(1);
        dstItemStatusReqDTO.setDisplayType("Display Type");
        dstItemStatusReqDTO.setPageSize(3L);
        dstItemStatusReqDTO.setStoreGuid("1234");
        PrdDstRespDTO actualQueryDstStatusResult = kitchenItemServiceImpl.queryDstStatus(dstItemStatusReqDTO);
        verify(dstItemStatusReqDTO).getCurrentPage();
        verify(dstItemStatusReqDTO).getPageSize();
        verify(dstItemStatusReqDTO).setCurrentPage(anyLong());
        verify(dstItemStatusReqDTO).setPageSize(anyLong());
        verify(dstItemStatusReqDTO, atLeast(1)).getDeviceId();
        verify(dstItemStatusReqDTO).getDisplayMode();
        verify(dstItemStatusReqDTO).getDisplayType();
        verify(dstItemStatusReqDTO, atLeast(1)).getStoreGuid();
        verify(dstItemStatusReqDTO).setDeviceId(Mockito.<String>any());
        verify(dstItemStatusReqDTO).setDisplayMode(Mockito.<Integer>any());
        verify(dstItemStatusReqDTO).setDisplayType(Mockito.<String>any());
        verify(dstItemStatusReqDTO).setStoreGuid(Mockito.<String>any());
        verify(kitchenItemMapper).pageDstItemAll(Mockito.<PageAdapter<KitchenItemReadDO>>any(), Mockito.<ItemQuery>any());
        verify(deviceConfigService).assertThatDeviceExists(Mockito.<String>any(), Mockito.<String>any());
        Page<PrdDstItemDTO> items = actualQueryDstStatusResult.getItems();
        assertTrue(items instanceof PageAdapter);
        assertEquals(0L, items.getTotalCount());
        assertEquals(3L, ((PageAdapter<PrdDstItemDTO>) items).getSize());
        assertFalse(((PageAdapter<PrdDstItemDTO>) items).hasPrevious());
        assertTrue(((PageAdapter<PrdDstItemDTO>) items).optimizeCountSql());
        assertTrue(((PageAdapter<PrdDstItemDTO>) items).getRecords().isEmpty());
    }

    /**
     * Method under test: {@link KitchenItemServiceImpl#call(ItemCallUpReqDTO)}
     */
    @Test
    public void testCall() {
        when(kitchenItemMapper.update(Mockito.<KitchenItemDO>any(), Mockito.<Wrapper<KitchenItemDO>>any())).thenReturn(1);

        ItemCallUpReqDTO itemCallUpReqDTO = new ItemCallUpReqDTO();
        itemCallUpReqDTO.setOrderItemGuidList(new ArrayList<>());
        boolean actualCallResult = kitchenItemServiceImpl.call(itemCallUpReqDTO);
        verify(kitchenItemMapper).update(Mockito.<KitchenItemDO>any(), Mockito.<Wrapper<KitchenItemDO>>any());
        assertTrue(actualCallResult);
    }

    /**
     * Method under test: {@link KitchenItemServiceImpl#call(ItemCallUpReqDTO)}
     */
    @Test
    public void testCall2() {
        when(kitchenItemMapper.update(Mockito.<KitchenItemDO>any(), Mockito.<Wrapper<KitchenItemDO>>any())).thenReturn(0);

        ItemCallUpReqDTO itemCallUpReqDTO = new ItemCallUpReqDTO();
        itemCallUpReqDTO.setOrderItemGuidList(new ArrayList<>());
        boolean actualCallResult = kitchenItemServiceImpl.call(itemCallUpReqDTO);
        verify(kitchenItemMapper).update(Mockito.<KitchenItemDO>any(), Mockito.<Wrapper<KitchenItemDO>>any());
        assertFalse(actualCallResult);
    }

    /**
     * Method under test: {@link KitchenItemServiceImpl#call(ItemCallUpReqDTO)}
     */
    @Test
    public void testCall3() {
        when(kitchenItemMapper.update(Mockito.<KitchenItemDO>any(), Mockito.<Wrapper<KitchenItemDO>>any())).thenReturn(1);

        ArrayList<String> orderItemGuidList = new ArrayList<>();
        orderItemGuidList.add(System.getProperty("user.timezone"));

        ItemCallUpReqDTO itemCallUpReqDTO = new ItemCallUpReqDTO();
        itemCallUpReqDTO.setOrderItemGuidList(orderItemGuidList);
        boolean actualCallResult = kitchenItemServiceImpl.call(itemCallUpReqDTO);
        verify(kitchenItemMapper).update(Mockito.<KitchenItemDO>any(), Mockito.<Wrapper<KitchenItemDO>>any());
        assertTrue(actualCallResult);
    }

    /**
     * Method under test: {@link KitchenItemServiceImpl#remark(OrderRemarkReqDTO)}
     */
    @Test
    public void testRemark() {
        when(kitchenItemMapper.queryOrderItem(Mockito.<ItemQuery>any())).thenReturn(new ArrayList<>());

        OrderRemarkReqDTO orderRemarkReqDTO = new OrderRemarkReqDTO();
        orderRemarkReqDTO.setOrderGuid("1234");
        orderRemarkReqDTO.setOrderRemark("Order Remark");
        boolean actualRemarkResult = kitchenItemServiceImpl.remark(orderRemarkReqDTO);
        verify(kitchenItemMapper).queryOrderItem(Mockito.<ItemQuery>any());
        assertFalse(actualRemarkResult);
    }

    /**
     * Method under test: {@link KitchenItemServiceImpl#remark(OrderRemarkReqDTO)}
     */
    @Test
    public void testRemark2() {
        when(kitchenItemMapper.queryOrderItem(Mockito.<ItemQuery>any())).thenThrow(new IllegalArgumentException("foo"));

        OrderRemarkReqDTO orderRemarkReqDTO = new OrderRemarkReqDTO();
        orderRemarkReqDTO.setOrderGuid("1234");
        orderRemarkReqDTO.setOrderRemark("Order Remark");
        thrown.expect(IllegalArgumentException.class);
        kitchenItemServiceImpl.remark(orderRemarkReqDTO);
        verify(kitchenItemMapper).queryOrderItem(Mockito.<ItemQuery>any());
    }

    /**
     * Method under test: {@link KitchenItemServiceImpl#remark(OrderRemarkReqDTO)}
     */
    @Test
    public void testRemark3() {
        KitchenItemReadDO kitchenItemReadDO = new KitchenItemReadDO();
        kitchenItemReadDO.setOrderItemGuid("1234");

        ArrayList<KitchenItemReadDO> kitchenItemReadDOList = new ArrayList<>();
        kitchenItemReadDOList.add(kitchenItemReadDO);
        when(kitchenItemMapper.updateOrderRemark(Mockito.<List<KitchenItemDO>>any())).thenReturn(1);
        when(kitchenItemMapper.queryOrderItem(Mockito.<ItemQuery>any())).thenReturn(kitchenItemReadDOList);

        OrderRemarkReqDTO orderRemarkReqDTO = new OrderRemarkReqDTO();
        orderRemarkReqDTO.setOrderGuid("1234");
        orderRemarkReqDTO.setOrderRemark("Order Remark");
        boolean actualRemarkResult = kitchenItemServiceImpl.remark(orderRemarkReqDTO);
        verify(kitchenItemMapper).queryOrderItem(Mockito.<ItemQuery>any());
        verify(kitchenItemMapper).updateOrderRemark(Mockito.<List<KitchenItemDO>>any());
        assertTrue(actualRemarkResult);
    }

    /**
     * Method under test: {@link KitchenItemServiceImpl#remark(OrderRemarkReqDTO)}
     */
    @Test
    public void testRemark4() {
        KitchenItemReadDO kitchenItemReadDO = new KitchenItemReadDO();
        kitchenItemReadDO.setOrderItemGuid("1234");

        ArrayList<KitchenItemReadDO> kitchenItemReadDOList = new ArrayList<>();
        kitchenItemReadDOList.add(kitchenItemReadDO);
        when(kitchenItemMapper.updateOrderRemark(Mockito.<List<KitchenItemDO>>any())).thenReturn(0);
        when(kitchenItemMapper.queryOrderItem(Mockito.<ItemQuery>any())).thenReturn(kitchenItemReadDOList);

        OrderRemarkReqDTO orderRemarkReqDTO = new OrderRemarkReqDTO();
        orderRemarkReqDTO.setOrderGuid("1234");
        orderRemarkReqDTO.setOrderRemark("Order Remark");
        boolean actualRemarkResult = kitchenItemServiceImpl.remark(orderRemarkReqDTO);
        verify(kitchenItemMapper).queryOrderItem(Mockito.<ItemQuery>any());
        verify(kitchenItemMapper).updateOrderRemark(Mockito.<List<KitchenItemDO>>any());
        assertFalse(actualRemarkResult);
    }

    /**
     * Method under test: {@link KitchenItemServiceImpl#remark(OrderRemarkReqDTO)}
     */
    @Test
    public void testRemark5() {
        KitchenItemReadDO kitchenItemReadDO = mock(KitchenItemReadDO.class);
        when(kitchenItemReadDO.getOrderItemGuid()).thenReturn("1234");
        doNothing().when(kitchenItemReadDO).setOrderItemGuid(Mockito.<String>any());
        kitchenItemReadDO.setOrderItemGuid("1234");

        ArrayList<KitchenItemReadDO> kitchenItemReadDOList = new ArrayList<>();
        kitchenItemReadDOList.add(kitchenItemReadDO);
        when(kitchenItemMapper.updateOrderRemark(Mockito.<List<KitchenItemDO>>any())).thenReturn(1);
        when(kitchenItemMapper.queryOrderItem(Mockito.<ItemQuery>any())).thenReturn(kitchenItemReadDOList);

        OrderRemarkReqDTO orderRemarkReqDTO = new OrderRemarkReqDTO();
        orderRemarkReqDTO.setOrderGuid("1234");
        orderRemarkReqDTO.setOrderRemark("Order Remark");
        boolean actualRemarkResult = kitchenItemServiceImpl.remark(orderRemarkReqDTO);
        verify(kitchenItemReadDO, atLeast(1)).getOrderItemGuid();
        verify(kitchenItemReadDO).setOrderItemGuid(Mockito.<String>any());
        verify(kitchenItemMapper).queryOrderItem(Mockito.<ItemQuery>any());
        verify(kitchenItemMapper).updateOrderRemark(Mockito.<List<KitchenItemDO>>any());
        assertTrue(actualRemarkResult);
    }

    /**
     * Method under test: {@link KitchenItemServiceImpl#remark(OrderRemarkReqDTO)}
     */
    @Test
    public void testRemark6() {
        KitchenItemReadDO kitchenItemReadDO = mock(KitchenItemReadDO.class);
        when(kitchenItemReadDO.getItemRemark()).thenReturn("Item Remark");
        when(kitchenItemReadDO.getOrderItemGuid()).thenReturn("1234");
        doNothing().when(kitchenItemReadDO).setOrderItemGuid(Mockito.<String>any());
        kitchenItemReadDO.setOrderItemGuid("1234");

        ArrayList<KitchenItemReadDO> kitchenItemReadDOList = new ArrayList<>();
        kitchenItemReadDOList.add(kitchenItemReadDO);
        when(kitchenItemMapper.updateOrderRemark(Mockito.<List<KitchenItemDO>>any())).thenReturn(1);
        when(kitchenItemMapper.queryOrderItem(Mockito.<ItemQuery>any())).thenReturn(kitchenItemReadDOList);
        OrderRemarkReqDTO orderRemarkReqDTO = mock(OrderRemarkReqDTO.class);
        when(orderRemarkReqDTO.getOrderGuid()).thenReturn("1234");
        when(orderRemarkReqDTO.getOrderRemark()).thenReturn("");
        doNothing().when(orderRemarkReqDTO).setOrderGuid(Mockito.<String>any());
        doNothing().when(orderRemarkReqDTO).setOrderRemark(Mockito.<String>any());
        orderRemarkReqDTO.setOrderGuid("1234");
        orderRemarkReqDTO.setOrderRemark("Order Remark");
        boolean actualRemarkResult = kitchenItemServiceImpl.remark(orderRemarkReqDTO);
        verify(orderRemarkReqDTO).getOrderGuid();
        verify(orderRemarkReqDTO).getOrderRemark();
        verify(orderRemarkReqDTO).setOrderGuid(Mockito.<String>any());
        verify(orderRemarkReqDTO).setOrderRemark(Mockito.<String>any());
        verify(kitchenItemReadDO).getItemRemark();
        verify(kitchenItemReadDO, atLeast(1)).getOrderItemGuid();
        verify(kitchenItemReadDO).setOrderItemGuid(Mockito.<String>any());
        verify(kitchenItemMapper).queryOrderItem(Mockito.<ItemQuery>any());
        verify(kitchenItemMapper).updateOrderRemark(Mockito.<List<KitchenItemDO>>any());
        assertTrue(actualRemarkResult);
    }

    /**
     * Method under test:
     * {@link KitchenItemServiceImpl#changeTable(OrderTableReqDTO)}
     */
    @Test
    public void testChangeTable() {
        when(kitchenItemMapper.update(Mockito.<KitchenItemDO>any(), Mockito.<Wrapper<KitchenItemDO>>any())).thenReturn(1);

        OrderTableReqDTO orderTableReqDTO = new OrderTableReqDTO();
        orderTableReqDTO.setAreaGuid("1234");
        orderTableReqDTO.setNewTableGuid("1234");
        orderTableReqDTO.setNewTableName("New Table Name");
        orderTableReqDTO.setOrderGuid("1234");
        orderTableReqDTO.setOriTableGuid("1234");
        boolean actualChangeTableResult = kitchenItemServiceImpl.changeTable(orderTableReqDTO);
        verify(kitchenItemMapper).update(Mockito.<KitchenItemDO>any(), Mockito.<Wrapper<KitchenItemDO>>any());
        assertTrue(actualChangeTableResult);
    }

    /**
     * Method under test:
     * {@link KitchenItemServiceImpl#changeTable(OrderTableReqDTO)}
     */
    @Test
    public void testChangeTable2() {
        when(kitchenItemMapper.update(Mockito.<KitchenItemDO>any(), Mockito.<Wrapper<KitchenItemDO>>any())).thenReturn(0);

        OrderTableReqDTO orderTableReqDTO = new OrderTableReqDTO();
        orderTableReqDTO.setAreaGuid("1234");
        orderTableReqDTO.setNewTableGuid("1234");
        orderTableReqDTO.setNewTableName("New Table Name");
        orderTableReqDTO.setOrderGuid("1234");
        orderTableReqDTO.setOriTableGuid("1234");
        boolean actualChangeTableResult = kitchenItemServiceImpl.changeTable(orderTableReqDTO);
        verify(kitchenItemMapper).update(Mockito.<KitchenItemDO>any(), Mockito.<Wrapper<KitchenItemDO>>any());
        assertFalse(actualChangeTableResult);
    }

    /**
     * Method under test:
     * {@link KitchenItemServiceImpl#refund(ItemBatchRefundReqDTO)}
     */
    @Test
    public void testRefund() {
        ItemRefundReqDTO itemRefundReqDTO = mock(ItemRefundReqDTO.class);
        when(itemRefundReqDTO.getIsWeight()).thenThrow(new IllegalArgumentException("foo"));
        when(itemRefundReqDTO.getOrderItemGuid()).thenReturn("1234");
        doNothing().when(itemRefundReqDTO).setIsWeight(Mockito.<Boolean>any());
        doNothing().when(itemRefundReqDTO).setNumber(Mockito.<Integer>any());
        doNothing().when(itemRefundReqDTO).setOrderItemGuid(Mockito.<String>any());
        itemRefundReqDTO.setIsWeight(true);
        itemRefundReqDTO.setNumber(10);
        itemRefundReqDTO.setOrderItemGuid("1234");
        ItemRefundReqDTO itemRefundReqDTO2 = mock(ItemRefundReqDTO.class);
        when(itemRefundReqDTO2.getIsWeight()).thenReturn(true);
        when(itemRefundReqDTO2.getOrderItemGuid()).thenReturn("1234");
        doNothing().when(itemRefundReqDTO2).setIsWeight(Mockito.<Boolean>any());
        doNothing().when(itemRefundReqDTO2).setNumber(Mockito.<Integer>any());
        doNothing().when(itemRefundReqDTO2).setOrderItemGuid(Mockito.<String>any());
        itemRefundReqDTO2.setIsWeight(false);
        itemRefundReqDTO2.setNumber(10);
        itemRefundReqDTO2.setOrderItemGuid("1234");

        ArrayList<ItemRefundReqDTO> itemRefundReqDTOList = new ArrayList<>();
        itemRefundReqDTOList.add(itemRefundReqDTO2);
        itemRefundReqDTOList.add(itemRefundReqDTO);
        ItemBatchRefundReqDTO itemBatchRefundReqDTO = mock(ItemBatchRefundReqDTO.class);
        when(itemBatchRefundReqDTO.getItemRefundList()).thenReturn(itemRefundReqDTOList);
        thrown.expect(IllegalArgumentException.class);
        kitchenItemServiceImpl.refund(itemBatchRefundReqDTO);
        verify(itemBatchRefundReqDTO).getItemRefundList();
        verify(itemRefundReqDTO2).getIsWeight();
        verify(itemRefundReqDTO).getIsWeight();
        verify(itemRefundReqDTO2).getOrderItemGuid();
        verify(itemRefundReqDTO).getOrderItemGuid();
        verify(itemRefundReqDTO2).setIsWeight(Mockito.<Boolean>any());
        verify(itemRefundReqDTO).setIsWeight(Mockito.<Boolean>any());
        verify(itemRefundReqDTO2).setNumber(Mockito.<Integer>any());
        verify(itemRefundReqDTO).setNumber(Mockito.<Integer>any());
        verify(itemRefundReqDTO2).setOrderItemGuid(Mockito.<String>any());
        verify(itemRefundReqDTO).setOrderItemGuid(Mockito.<String>any());
    }

    /**
     * Method under test:
     * {@link KitchenItemServiceImpl#refund(ItemBatchRefundReqDTO)}
     */
    @Test
    public void testRefund2() {
        ItemRefundReqDTO itemRefundReqDTO = mock(ItemRefundReqDTO.class);
        when(itemRefundReqDTO.getIsWeight()).thenThrow(new IllegalArgumentException("foo"));
        when(itemRefundReqDTO.getNumber()).thenReturn(10);
        when(itemRefundReqDTO.getOrderItemGuid()).thenReturn("1234");
        doNothing().when(itemRefundReqDTO).setIsWeight(Mockito.<Boolean>any());
        doNothing().when(itemRefundReqDTO).setNumber(Mockito.<Integer>any());
        doNothing().when(itemRefundReqDTO).setOrderItemGuid(Mockito.<String>any());
        itemRefundReqDTO.setIsWeight(true);
        itemRefundReqDTO.setNumber(10);
        itemRefundReqDTO.setOrderItemGuid("1234");
        ItemRefundReqDTO itemRefundReqDTO2 = mock(ItemRefundReqDTO.class);
        when(itemRefundReqDTO2.getIsWeight()).thenReturn(true);
        when(itemRefundReqDTO2.getNumber()).thenReturn(10);
        when(itemRefundReqDTO2.getOrderItemGuid()).thenReturn("1234");
        doNothing().when(itemRefundReqDTO2).setIsWeight(Mockito.<Boolean>any());
        doNothing().when(itemRefundReqDTO2).setNumber(Mockito.<Integer>any());
        doNothing().when(itemRefundReqDTO2).setOrderItemGuid(Mockito.<String>any());
        itemRefundReqDTO2.setIsWeight(false);
        itemRefundReqDTO2.setNumber(10);
        itemRefundReqDTO2.setOrderItemGuid("1234");

        ItemRefundReqDTO itemRefundReqDTO3 = new ItemRefundReqDTO();
        itemRefundReqDTO3.setIsWeight(true);
        itemRefundReqDTO3.setNumber(10);
        itemRefundReqDTO3.setOrderItemGuid("1234");

        ArrayList<ItemRefundReqDTO> itemRefundReqDTOList = new ArrayList<>();
        itemRefundReqDTOList.add(itemRefundReqDTO3);
        itemRefundReqDTOList.add(itemRefundReqDTO2);
        itemRefundReqDTOList.add(itemRefundReqDTO);
        ItemBatchRefundReqDTO itemBatchRefundReqDTO = mock(ItemBatchRefundReqDTO.class);
        when(itemBatchRefundReqDTO.getItemRefundList()).thenReturn(itemRefundReqDTOList);
        thrown.expect(IllegalArgumentException.class);
        kitchenItemServiceImpl.refund(itemBatchRefundReqDTO);
        verify(itemBatchRefundReqDTO).getItemRefundList();
        verify(itemRefundReqDTO2).getIsWeight();
        verify(itemRefundReqDTO).getIsWeight();
        verify(itemRefundReqDTO2).getNumber();
        verify(itemRefundReqDTO2).getOrderItemGuid();
        verify(itemRefundReqDTO).getOrderItemGuid();
        verify(itemRefundReqDTO2).setIsWeight(Mockito.<Boolean>any());
        verify(itemRefundReqDTO).setIsWeight(Mockito.<Boolean>any());
        verify(itemRefundReqDTO2).setNumber(Mockito.<Integer>any());
        verify(itemRefundReqDTO).setNumber(Mockito.<Integer>any());
        verify(itemRefundReqDTO2).setOrderItemGuid(Mockito.<String>any());
        verify(itemRefundReqDTO).setOrderItemGuid(Mockito.<String>any());
    }

    /**
     * Method under test:
     * {@link KitchenItemServiceImpl#cooking(ItemStateTransReqDTO)}
     */
    @Test
    public void testCooking() {
        ItemStateTransReqDTO itemStateTransReqDTO = mock(ItemStateTransReqDTO.class);
        when(itemStateTransReqDTO.getPrdDstItemList()).thenReturn(new ArrayList<>());
        doNothing().when(itemStateTransReqDTO).setAccount(Mockito.<String>any());
        doNothing().when(itemStateTransReqDTO).setAccountName(Mockito.<String>any());
        doNothing().when(itemStateTransReqDTO).setActuallyPayFee(Mockito.<BigDecimal>any());
        doNothing().when(itemStateTransReqDTO).setDeviceId(Mockito.<String>any());
        doNothing().when(itemStateTransReqDTO).setDeviceType(Mockito.<Integer>any());
        doNothing().when(itemStateTransReqDTO).setEnterpriseGuid(Mockito.<String>any());
        doNothing().when(itemStateTransReqDTO).setEnterpriseName(Mockito.<String>any());
        doNothing().when(itemStateTransReqDTO).setInvoiceCode(Mockito.<String>any());
        doNothing().when(itemStateTransReqDTO).setInvoicePhone(Mockito.<String>any());
        doNothing().when(itemStateTransReqDTO).setIsInvoiceCode(Mockito.<Integer>any());
        doNothing().when(itemStateTransReqDTO).setLatitude(Mockito.<String>any());
        doNothing().when(itemStateTransReqDTO).setLongitude(Mockito.<String>any());
        doNothing().when(itemStateTransReqDTO).setOperSubjectGuid(Mockito.<String>any());
        doNothing().when(itemStateTransReqDTO).setRequestTimestamp(Mockito.<Long>any());
        doNothing().when(itemStateTransReqDTO).setStoreGuid(Mockito.<String>any());
        doNothing().when(itemStateTransReqDTO).setStoreName(Mockito.<String>any());
        doNothing().when(itemStateTransReqDTO).setUserGuid(Mockito.<String>any());
        doNothing().when(itemStateTransReqDTO).setUserName(Mockito.<String>any());
        doNothing().when(itemStateTransReqDTO).setOrderDesc(Mockito.<String>any());
        doNothing().when(itemStateTransReqDTO).setOrderDifference(Mockito.<Integer>any());
        doNothing().when(itemStateTransReqDTO).setPrdDstItemList(Mockito.<List<PrdDstItemDTO>>any());
        doNothing().when(itemStateTransReqDTO).setQueryModeForPoint(Mockito.<Integer>any());
        itemStateTransReqDTO.setAccount("3");
        itemStateTransReqDTO.setAccountName("Dr Jane Doe");
        itemStateTransReqDTO.setActuallyPayFee(new BigDecimal("2.3"));
        itemStateTransReqDTO.setDeviceId("42");
        itemStateTransReqDTO.setDeviceType(1);
        itemStateTransReqDTO.setEnterpriseGuid("1234");
        itemStateTransReqDTO.setEnterpriseName("Enterprise Name");
        itemStateTransReqDTO.setInvoiceCode("Invoice Code");
        itemStateTransReqDTO.setInvoicePhone("**********");
        itemStateTransReqDTO.setIsInvoiceCode(1);
        itemStateTransReqDTO.setLatitude("Latitude");
        itemStateTransReqDTO.setLongitude("Longitude");
        itemStateTransReqDTO.setOperSubjectGuid("1234");
        itemStateTransReqDTO.setOrderDesc("Order Desc");
        itemStateTransReqDTO.setOrderDifference(1);
        itemStateTransReqDTO.setPrdDstItemList(new ArrayList<>());
        itemStateTransReqDTO.setQueryModeForPoint(1);
        itemStateTransReqDTO.setRequestTimestamp(1L);
        itemStateTransReqDTO.setStoreGuid("1234");
        itemStateTransReqDTO.setStoreName("Store Name");
        itemStateTransReqDTO.setUserGuid("1234");
        itemStateTransReqDTO.setUserName("janedoe");
        kitchenItemServiceImpl.cooking(itemStateTransReqDTO);
        verify(itemStateTransReqDTO).setAccount(Mockito.<String>any());
        verify(itemStateTransReqDTO).setAccountName(Mockito.<String>any());
        verify(itemStateTransReqDTO).setActuallyPayFee(Mockito.<BigDecimal>any());
        verify(itemStateTransReqDTO).setDeviceId(Mockito.<String>any());
        verify(itemStateTransReqDTO).setDeviceType(Mockito.<Integer>any());
        verify(itemStateTransReqDTO).setEnterpriseGuid(Mockito.<String>any());
        verify(itemStateTransReqDTO).setEnterpriseName(Mockito.<String>any());
        verify(itemStateTransReqDTO).setInvoiceCode(Mockito.<String>any());
        verify(itemStateTransReqDTO).setInvoicePhone(Mockito.<String>any());
        verify(itemStateTransReqDTO).setIsInvoiceCode(Mockito.<Integer>any());
        verify(itemStateTransReqDTO).setLatitude(Mockito.<String>any());
        verify(itemStateTransReqDTO).setLongitude(Mockito.<String>any());
        verify(itemStateTransReqDTO).setOperSubjectGuid(Mockito.<String>any());
        verify(itemStateTransReqDTO).setRequestTimestamp(Mockito.<Long>any());
        verify(itemStateTransReqDTO).setStoreGuid(Mockito.<String>any());
        verify(itemStateTransReqDTO).setStoreName(Mockito.<String>any());
        verify(itemStateTransReqDTO).setUserGuid(Mockito.<String>any());
        verify(itemStateTransReqDTO).setUserName(Mockito.<String>any());
        verify(itemStateTransReqDTO, atLeast(1)).getPrdDstItemList();
        verify(itemStateTransReqDTO).setOrderDesc(Mockito.<String>any());
        verify(itemStateTransReqDTO).setOrderDifference(Mockito.<Integer>any());
        verify(itemStateTransReqDTO).setPrdDstItemList(Mockito.<List<PrdDstItemDTO>>any());
        verify(itemStateTransReqDTO).setQueryModeForPoint(Mockito.<Integer>any());
    }

    /**
     * Method under test:
     * {@link KitchenItemServiceImpl#complete(ItemStateTransReqDTO)}
     */
    @Test
    public void testComplete() {
        ItemStateTransReqDTO itemStateTransReqDTO = mock(ItemStateTransReqDTO.class);
        when(itemStateTransReqDTO.getPrdDstItemList()).thenReturn(new ArrayList<>());
        doNothing().when(itemStateTransReqDTO).setAccount(Mockito.<String>any());
        doNothing().when(itemStateTransReqDTO).setAccountName(Mockito.<String>any());
        doNothing().when(itemStateTransReqDTO).setActuallyPayFee(Mockito.<BigDecimal>any());
        doNothing().when(itemStateTransReqDTO).setDeviceId(Mockito.<String>any());
        doNothing().when(itemStateTransReqDTO).setDeviceType(Mockito.<Integer>any());
        doNothing().when(itemStateTransReqDTO).setEnterpriseGuid(Mockito.<String>any());
        doNothing().when(itemStateTransReqDTO).setEnterpriseName(Mockito.<String>any());
        doNothing().when(itemStateTransReqDTO).setInvoiceCode(Mockito.<String>any());
        doNothing().when(itemStateTransReqDTO).setInvoicePhone(Mockito.<String>any());
        doNothing().when(itemStateTransReqDTO).setIsInvoiceCode(Mockito.<Integer>any());
        doNothing().when(itemStateTransReqDTO).setLatitude(Mockito.<String>any());
        doNothing().when(itemStateTransReqDTO).setLongitude(Mockito.<String>any());
        doNothing().when(itemStateTransReqDTO).setOperSubjectGuid(Mockito.<String>any());
        doNothing().when(itemStateTransReqDTO).setRequestTimestamp(Mockito.<Long>any());
        doNothing().when(itemStateTransReqDTO).setStoreGuid(Mockito.<String>any());
        doNothing().when(itemStateTransReqDTO).setStoreName(Mockito.<String>any());
        doNothing().when(itemStateTransReqDTO).setUserGuid(Mockito.<String>any());
        doNothing().when(itemStateTransReqDTO).setUserName(Mockito.<String>any());
        doNothing().when(itemStateTransReqDTO).setOrderDesc(Mockito.<String>any());
        doNothing().when(itemStateTransReqDTO).setOrderDifference(Mockito.<Integer>any());
        doNothing().when(itemStateTransReqDTO).setPrdDstItemList(Mockito.<List<PrdDstItemDTO>>any());
        doNothing().when(itemStateTransReqDTO).setQueryModeForPoint(Mockito.<Integer>any());
        itemStateTransReqDTO.setAccount("3");
        itemStateTransReqDTO.setAccountName("Dr Jane Doe");
        itemStateTransReqDTO.setActuallyPayFee(new BigDecimal("2.3"));
        itemStateTransReqDTO.setDeviceId("42");
        itemStateTransReqDTO.setDeviceType(1);
        itemStateTransReqDTO.setEnterpriseGuid("1234");
        itemStateTransReqDTO.setEnterpriseName("Enterprise Name");
        itemStateTransReqDTO.setInvoiceCode("Invoice Code");
        itemStateTransReqDTO.setInvoicePhone("**********");
        itemStateTransReqDTO.setIsInvoiceCode(1);
        itemStateTransReqDTO.setLatitude("Latitude");
        itemStateTransReqDTO.setLongitude("Longitude");
        itemStateTransReqDTO.setOperSubjectGuid("1234");
        itemStateTransReqDTO.setOrderDesc("Order Desc");
        itemStateTransReqDTO.setOrderDifference(1);
        itemStateTransReqDTO.setPrdDstItemList(new ArrayList<>());
        itemStateTransReqDTO.setQueryModeForPoint(1);
        itemStateTransReqDTO.setRequestTimestamp(1L);
        itemStateTransReqDTO.setStoreGuid("1234");
        itemStateTransReqDTO.setStoreName("Store Name");
        itemStateTransReqDTO.setUserGuid("1234");
        itemStateTransReqDTO.setUserName("janedoe");
        kitchenItemServiceImpl.complete(itemStateTransReqDTO);
        verify(itemStateTransReqDTO).setAccount(Mockito.<String>any());
        verify(itemStateTransReqDTO).setAccountName(Mockito.<String>any());
        verify(itemStateTransReqDTO).setActuallyPayFee(Mockito.<BigDecimal>any());
        verify(itemStateTransReqDTO).setDeviceId(Mockito.<String>any());
        verify(itemStateTransReqDTO).setDeviceType(Mockito.<Integer>any());
        verify(itemStateTransReqDTO).setEnterpriseGuid(Mockito.<String>any());
        verify(itemStateTransReqDTO).setEnterpriseName(Mockito.<String>any());
        verify(itemStateTransReqDTO).setInvoiceCode(Mockito.<String>any());
        verify(itemStateTransReqDTO).setInvoicePhone(Mockito.<String>any());
        verify(itemStateTransReqDTO).setIsInvoiceCode(Mockito.<Integer>any());
        verify(itemStateTransReqDTO).setLatitude(Mockito.<String>any());
        verify(itemStateTransReqDTO).setLongitude(Mockito.<String>any());
        verify(itemStateTransReqDTO).setOperSubjectGuid(Mockito.<String>any());
        verify(itemStateTransReqDTO).setRequestTimestamp(Mockito.<Long>any());
        verify(itemStateTransReqDTO).setStoreGuid(Mockito.<String>any());
        verify(itemStateTransReqDTO).setStoreName(Mockito.<String>any());
        verify(itemStateTransReqDTO).setUserGuid(Mockito.<String>any());
        verify(itemStateTransReqDTO).setUserName(Mockito.<String>any());
        verify(itemStateTransReqDTO).getPrdDstItemList();
        verify(itemStateTransReqDTO).setOrderDesc(Mockito.<String>any());
        verify(itemStateTransReqDTO).setOrderDifference(Mockito.<Integer>any());
        verify(itemStateTransReqDTO).setPrdDstItemList(Mockito.<List<PrdDstItemDTO>>any());
        verify(itemStateTransReqDTO).setQueryModeForPoint(Mockito.<Integer>any());
    }

    /**
     * Method under test:
     * {@link KitchenItemServiceImpl#cancelComplete(ItemStateTransReqDTO)}
     */
    @Test
    public void testCancelComplete() {
        ItemStateTransReqDTO itemStateTransReqDTO = mock(ItemStateTransReqDTO.class);
        when(itemStateTransReqDTO.getPrdDstItemList()).thenReturn(new ArrayList<>());
        doNothing().when(itemStateTransReqDTO).setAccount(Mockito.<String>any());
        doNothing().when(itemStateTransReqDTO).setAccountName(Mockito.<String>any());
        doNothing().when(itemStateTransReqDTO).setActuallyPayFee(Mockito.<BigDecimal>any());
        doNothing().when(itemStateTransReqDTO).setDeviceId(Mockito.<String>any());
        doNothing().when(itemStateTransReqDTO).setDeviceType(Mockito.<Integer>any());
        doNothing().when(itemStateTransReqDTO).setEnterpriseGuid(Mockito.<String>any());
        doNothing().when(itemStateTransReqDTO).setEnterpriseName(Mockito.<String>any());
        doNothing().when(itemStateTransReqDTO).setInvoiceCode(Mockito.<String>any());
        doNothing().when(itemStateTransReqDTO).setInvoicePhone(Mockito.<String>any());
        doNothing().when(itemStateTransReqDTO).setIsInvoiceCode(Mockito.<Integer>any());
        doNothing().when(itemStateTransReqDTO).setLatitude(Mockito.<String>any());
        doNothing().when(itemStateTransReqDTO).setLongitude(Mockito.<String>any());
        doNothing().when(itemStateTransReqDTO).setOperSubjectGuid(Mockito.<String>any());
        doNothing().when(itemStateTransReqDTO).setRequestTimestamp(Mockito.<Long>any());
        doNothing().when(itemStateTransReqDTO).setStoreGuid(Mockito.<String>any());
        doNothing().when(itemStateTransReqDTO).setStoreName(Mockito.<String>any());
        doNothing().when(itemStateTransReqDTO).setUserGuid(Mockito.<String>any());
        doNothing().when(itemStateTransReqDTO).setUserName(Mockito.<String>any());
        doNothing().when(itemStateTransReqDTO).setOrderDesc(Mockito.<String>any());
        doNothing().when(itemStateTransReqDTO).setOrderDifference(Mockito.<Integer>any());
        doNothing().when(itemStateTransReqDTO).setPrdDstItemList(Mockito.<List<PrdDstItemDTO>>any());
        doNothing().when(itemStateTransReqDTO).setQueryModeForPoint(Mockito.<Integer>any());
        itemStateTransReqDTO.setAccount("3");
        itemStateTransReqDTO.setAccountName("Dr Jane Doe");
        itemStateTransReqDTO.setActuallyPayFee(new BigDecimal("2.3"));
        itemStateTransReqDTO.setDeviceId("42");
        itemStateTransReqDTO.setDeviceType(1);
        itemStateTransReqDTO.setEnterpriseGuid("1234");
        itemStateTransReqDTO.setEnterpriseName("Enterprise Name");
        itemStateTransReqDTO.setInvoiceCode("Invoice Code");
        itemStateTransReqDTO.setInvoicePhone("**********");
        itemStateTransReqDTO.setIsInvoiceCode(1);
        itemStateTransReqDTO.setLatitude("Latitude");
        itemStateTransReqDTO.setLongitude("Longitude");
        itemStateTransReqDTO.setOperSubjectGuid("1234");
        itemStateTransReqDTO.setOrderDesc("Order Desc");
        itemStateTransReqDTO.setOrderDifference(1);
        itemStateTransReqDTO.setPrdDstItemList(new ArrayList<>());
        itemStateTransReqDTO.setQueryModeForPoint(1);
        itemStateTransReqDTO.setRequestTimestamp(1L);
        itemStateTransReqDTO.setStoreGuid("1234");
        itemStateTransReqDTO.setStoreName("Store Name");
        itemStateTransReqDTO.setUserGuid("1234");
        itemStateTransReqDTO.setUserName("janedoe");
        kitchenItemServiceImpl.cancelComplete(itemStateTransReqDTO);
        verify(itemStateTransReqDTO).setAccount(Mockito.<String>any());
        verify(itemStateTransReqDTO).setAccountName(Mockito.<String>any());
        verify(itemStateTransReqDTO).setActuallyPayFee(Mockito.<BigDecimal>any());
        verify(itemStateTransReqDTO).setDeviceId(Mockito.<String>any());
        verify(itemStateTransReqDTO).setDeviceType(Mockito.<Integer>any());
        verify(itemStateTransReqDTO).setEnterpriseGuid(Mockito.<String>any());
        verify(itemStateTransReqDTO).setEnterpriseName(Mockito.<String>any());
        verify(itemStateTransReqDTO).setInvoiceCode(Mockito.<String>any());
        verify(itemStateTransReqDTO).setInvoicePhone(Mockito.<String>any());
        verify(itemStateTransReqDTO).setIsInvoiceCode(Mockito.<Integer>any());
        verify(itemStateTransReqDTO).setLatitude(Mockito.<String>any());
        verify(itemStateTransReqDTO).setLongitude(Mockito.<String>any());
        verify(itemStateTransReqDTO).setOperSubjectGuid(Mockito.<String>any());
        verify(itemStateTransReqDTO).setRequestTimestamp(Mockito.<Long>any());
        verify(itemStateTransReqDTO).setStoreGuid(Mockito.<String>any());
        verify(itemStateTransReqDTO).setStoreName(Mockito.<String>any());
        verify(itemStateTransReqDTO).setUserGuid(Mockito.<String>any());
        verify(itemStateTransReqDTO).setUserName(Mockito.<String>any());
        verify(itemStateTransReqDTO).getPrdDstItemList();
        verify(itemStateTransReqDTO).setOrderDesc(Mockito.<String>any());
        verify(itemStateTransReqDTO).setOrderDifference(Mockito.<Integer>any());
        verify(itemStateTransReqDTO).setPrdDstItemList(Mockito.<List<PrdDstItemDTO>>any());
        verify(itemStateTransReqDTO).setQueryModeForPoint(Mockito.<Integer>any());
    }

    /**
     * Method under test:
     * {@link KitchenItemServiceImpl#distribute(ItemStateTransReqDTO)}
     */
    @Test
    public void testDistribute() {
        when(deviceConfigService.queryDstDispatchAsPrintByGuid(Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn(DeviceConfigDO.defaultConfig());
        doNothing().when(queueItemService).inDistributedQueue(Mockito.<AsyncTask<ItemStateTransReqDTO>>any());

        ItemStateTransReqDTO itemStateTransReqDTO = new ItemStateTransReqDTO();
        itemStateTransReqDTO.setAccount("3");
        itemStateTransReqDTO.setAccountName("Dr Jane Doe");
        itemStateTransReqDTO.setActuallyPayFee(new BigDecimal("2.3"));
        itemStateTransReqDTO.setDeviceId("42");
        itemStateTransReqDTO.setDeviceType(1);
        itemStateTransReqDTO.setEnterpriseGuid("1234");
        itemStateTransReqDTO.setEnterpriseName("Enterprise Name");
        itemStateTransReqDTO.setInvoiceCode("Invoice Code");
        itemStateTransReqDTO.setInvoicePhone("**********");
        itemStateTransReqDTO.setIsInvoiceCode(1);
        itemStateTransReqDTO.setLatitude("Latitude");
        itemStateTransReqDTO.setLongitude("Longitude");
        itemStateTransReqDTO.setOperSubjectGuid("1234");
        itemStateTransReqDTO.setOrderDesc("Order Desc");
        itemStateTransReqDTO.setOrderDifference(1);
        itemStateTransReqDTO.setPrdDstItemList(new ArrayList<>());
        itemStateTransReqDTO.setQueryModeForPoint(1);
        itemStateTransReqDTO.setRequestTimestamp(1L);
        itemStateTransReqDTO.setStoreGuid("1234");
        itemStateTransReqDTO.setStoreName("Store Name");
        itemStateTransReqDTO.setUserGuid("1234");
        itemStateTransReqDTO.setUserName("janedoe");
        List<PrdDstItemDTO> actualDistributeResult = kitchenItemServiceImpl.distribute(itemStateTransReqDTO);
        verify(deviceConfigService).queryDstDispatchAsPrintByGuid(Mockito.<String>any(), Mockito.<String>any());
        verify(queueItemService).inDistributedQueue(Mockito.<AsyncTask<ItemStateTransReqDTO>>any());
        assertTrue(actualDistributeResult.isEmpty());
    }

    /**
     * Method under test:
     * {@link KitchenItemServiceImpl#distribute(ItemStateTransReqDTO)}
     */
    @Test
    public void testDistribute2() {
        doThrow(new IllegalArgumentException("撤销状态：{}===>撤销菜品参数：{}")).when(queueItemService)
                .inDistributedQueue(Mockito.<AsyncTask<ItemStateTransReqDTO>>any());

        ItemStateTransReqDTO itemStateTransReqDTO = new ItemStateTransReqDTO();
        itemStateTransReqDTO.setAccount("3");
        itemStateTransReqDTO.setAccountName("Dr Jane Doe");
        itemStateTransReqDTO.setActuallyPayFee(new BigDecimal("2.3"));
        itemStateTransReqDTO.setDeviceId("42");
        itemStateTransReqDTO.setDeviceType(1);
        itemStateTransReqDTO.setEnterpriseGuid("1234");
        itemStateTransReqDTO.setEnterpriseName("Enterprise Name");
        itemStateTransReqDTO.setInvoiceCode("Invoice Code");
        itemStateTransReqDTO.setInvoicePhone("**********");
        itemStateTransReqDTO.setIsInvoiceCode(1);
        itemStateTransReqDTO.setLatitude("Latitude");
        itemStateTransReqDTO.setLongitude("Longitude");
        itemStateTransReqDTO.setOperSubjectGuid("1234");
        itemStateTransReqDTO.setOrderDesc("Order Desc");
        itemStateTransReqDTO.setOrderDifference(1);
        itemStateTransReqDTO.setPrdDstItemList(new ArrayList<>());
        itemStateTransReqDTO.setQueryModeForPoint(1);
        itemStateTransReqDTO.setRequestTimestamp(1L);
        itemStateTransReqDTO.setStoreGuid("1234");
        itemStateTransReqDTO.setStoreName("Store Name");
        itemStateTransReqDTO.setUserGuid("1234");
        itemStateTransReqDTO.setUserName("janedoe");
        thrown.expect(IllegalArgumentException.class);
        kitchenItemServiceImpl.distribute(itemStateTransReqDTO);
        verify(queueItemService).inDistributedQueue(Mockito.<AsyncTask<ItemStateTransReqDTO>>any());
    }

    /**
     * Method under test:
     * {@link KitchenItemServiceImpl#distribute(ItemStateTransReqDTO)}
     */
    @Test
    public void testDistribute3() {
        DeviceConfigDO deviceConfigDO = mock(DeviceConfigDO.class);
        when(deviceConfigDO.getIsDispatchAsPrint()).thenReturn(true);
        when(deviceConfigService.queryDstDispatchAsPrintByGuid(Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn(deviceConfigDO);
        doNothing().when(kdsPrintRecordService).manualPrintDstItem(Mockito.<ItemStateTransReqDTO>any());
        doNothing().when(queueItemService).inDistributedQueue(Mockito.<AsyncTask<ItemStateTransReqDTO>>any());

        ItemStateTransReqDTO itemStateTransReqDTO = new ItemStateTransReqDTO();
        itemStateTransReqDTO.setAccount("3");
        itemStateTransReqDTO.setAccountName("Dr Jane Doe");
        itemStateTransReqDTO.setActuallyPayFee(new BigDecimal("2.3"));
        itemStateTransReqDTO.setDeviceId("42");
        itemStateTransReqDTO.setDeviceType(1);
        itemStateTransReqDTO.setEnterpriseGuid("1234");
        itemStateTransReqDTO.setEnterpriseName("Enterprise Name");
        itemStateTransReqDTO.setInvoiceCode("Invoice Code");
        itemStateTransReqDTO.setInvoicePhone("**********");
        itemStateTransReqDTO.setIsInvoiceCode(1);
        itemStateTransReqDTO.setLatitude("Latitude");
        itemStateTransReqDTO.setLongitude("Longitude");
        itemStateTransReqDTO.setOperSubjectGuid("1234");
        itemStateTransReqDTO.setOrderDesc("Order Desc");
        itemStateTransReqDTO.setOrderDifference(1);
        itemStateTransReqDTO.setPrdDstItemList(new ArrayList<>());
        itemStateTransReqDTO.setQueryModeForPoint(1);
        itemStateTransReqDTO.setRequestTimestamp(1L);
        itemStateTransReqDTO.setStoreGuid("1234");
        itemStateTransReqDTO.setStoreName("Store Name");
        itemStateTransReqDTO.setUserGuid("1234");
        itemStateTransReqDTO.setUserName("janedoe");
        List<PrdDstItemDTO> actualDistributeResult = kitchenItemServiceImpl.distribute(itemStateTransReqDTO);
        verify(deviceConfigDO).getIsDispatchAsPrint();
        verify(deviceConfigService).queryDstDispatchAsPrintByGuid(Mockito.<String>any(), Mockito.<String>any());
        verify(queueItemService).inDistributedQueue(Mockito.<AsyncTask<ItemStateTransReqDTO>>any());
        verify(kdsPrintRecordService).manualPrintDstItem(Mockito.<ItemStateTransReqDTO>any());
        assertTrue(actualDistributeResult.isEmpty());
    }

    /**
     * Method under test:
     * {@link KitchenItemServiceImpl#distribute(ItemStateTransReqDTO)}
     */
    @Test
    public void testDistribute4() {
        DeviceConfigDO deviceConfigDO = mock(DeviceConfigDO.class);
        when(deviceConfigDO.getIsDispatchAsPrint()).thenReturn(true);
        when(deviceConfigService.queryDstDispatchAsPrintByGuid(Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn(deviceConfigDO);
        doThrow(new IllegalArgumentException("撤销状态：{}===>撤销菜品参数：{}")).when(kdsPrintRecordService)
                .manualPrintDstItem(Mockito.<ItemStateTransReqDTO>any());
        doNothing().when(queueItemService).inDistributedQueue(Mockito.<AsyncTask<ItemStateTransReqDTO>>any());

        ItemStateTransReqDTO itemStateTransReqDTO = new ItemStateTransReqDTO();
        itemStateTransReqDTO.setAccount("3");
        itemStateTransReqDTO.setAccountName("Dr Jane Doe");
        itemStateTransReqDTO.setActuallyPayFee(new BigDecimal("2.3"));
        itemStateTransReqDTO.setDeviceId("42");
        itemStateTransReqDTO.setDeviceType(1);
        itemStateTransReqDTO.setEnterpriseGuid("1234");
        itemStateTransReqDTO.setEnterpriseName("Enterprise Name");
        itemStateTransReqDTO.setInvoiceCode("Invoice Code");
        itemStateTransReqDTO.setInvoicePhone("**********");
        itemStateTransReqDTO.setIsInvoiceCode(1);
        itemStateTransReqDTO.setLatitude("Latitude");
        itemStateTransReqDTO.setLongitude("Longitude");
        itemStateTransReqDTO.setOperSubjectGuid("1234");
        itemStateTransReqDTO.setOrderDesc("Order Desc");
        itemStateTransReqDTO.setOrderDifference(1);
        itemStateTransReqDTO.setPrdDstItemList(new ArrayList<>());
        itemStateTransReqDTO.setQueryModeForPoint(1);
        itemStateTransReqDTO.setRequestTimestamp(1L);
        itemStateTransReqDTO.setStoreGuid("1234");
        itemStateTransReqDTO.setStoreName("Store Name");
        itemStateTransReqDTO.setUserGuid("1234");
        itemStateTransReqDTO.setUserName("janedoe");
        thrown.expect(IllegalArgumentException.class);
        kitchenItemServiceImpl.distribute(itemStateTransReqDTO);
        verify(deviceConfigDO).getIsDispatchAsPrint();
        verify(deviceConfigService).queryDstDispatchAsPrintByGuid(Mockito.<String>any(), Mockito.<String>any());
        verify(queueItemService).inDistributedQueue(Mockito.<AsyncTask<ItemStateTransReqDTO>>any());
        verify(kdsPrintRecordService).manualPrintDstItem(Mockito.<ItemStateTransReqDTO>any());
    }

    /**
     * Method under test:
     * {@link KitchenItemServiceImpl#cancelDistribute(ItemCancelDstReqDTO)}
     */
    @Test
    @Ignore("TODO: Complete this test")
    public void testCancelDistribute() {
        // TODO: Complete this test.
        //   Reason: R013 No inputs found that don't throw a trivial exception.
        //   Diffblue Cover tried to run the arrange/act section, but the method under
        //   test threw
        //   com.baomidou.mybatisplus.core.exceptions.MybatisPlusException: 该模式不能应用于非 baseMapper 的泛型 entity 之外的 entity!
        //       at com.baomidou.mybatisplus.core.toolkit.ExceptionUtils.mpe(ExceptionUtils.java:51)
        //       at com.baomidou.mybatisplus.core.toolkit.Assert.isTrue(Assert.java:41)
        //       at com.baomidou.mybatisplus.core.toolkit.Assert.notEmpty(Assert.java:97)
        //       at com.baomidou.mybatisplus.core.conditions.AbstractLambdaWrapper.getColumn(AbstractLambdaWrapper.java:55)
        //       at com.baomidou.mybatisplus.core.conditions.AbstractLambdaWrapper.columnToString(AbstractLambdaWrapper.java:47)
        //       at com.baomidou.mybatisplus.core.conditions.AbstractLambdaWrapper.columnToString(AbstractLambdaWrapper.java:38)
        //       at java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:193)
        //       at java.util.Spliterators$ArraySpliterator.forEachRemaining(Spliterators.java:948)
        //       at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
        //       at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
        //       at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
        //       at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
        //       at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:499)
        //       at com.baomidou.mybatisplus.core.conditions.AbstractWrapper.columnsToString(AbstractWrapper.java:492)
        //       at com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper.select(LambdaQueryWrapper.java:75)
        //       at com.holderzone.saas.store.kds.service.impl.KitchenItemServiceImpl.cancelDistribute(KitchenItemServiceImpl.java:1303)
        //   See https://diff.blue/R013 to resolve this issue.

        ItemCancelDstReqDTO itemCancelDstReqDTO = new ItemCancelDstReqDTO();
        itemCancelDstReqDTO.setKitchenItemGuid("1234");
        kitchenItemServiceImpl.cancelDistribute(itemCancelDstReqDTO);
    }

    /**
     * Method under test:
     * {@link KitchenItemServiceImpl#prdHistory(PrdDstItemHistoryReqDTO)}
     */
    @Test
    public void testPrdHistory() {
        when(kitchenItemMapper.selectPage(Mockito.<IPage<KitchenItemDO>>any(), Mockito.<Wrapper<KitchenItemDO>>any()))
                .thenReturn(new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>());
        when(kitchenItemMapstruct.toKitchenItemDTO(Mockito.<List<KitchenItemDO>>any())).thenReturn(new ArrayList<>());

        PrdDstItemHistoryReqDTO prdDstItemHistoryReqDTO = new PrdDstItemHistoryReqDTO();
        prdDstItemHistoryReqDTO.setAccount("3");
        prdDstItemHistoryReqDTO.setAccountName("Dr Jane Doe");
        prdDstItemHistoryReqDTO.setActuallyPayFee(new BigDecimal("2.3"));
        prdDstItemHistoryReqDTO.setCurrentPage(1);
        prdDstItemHistoryReqDTO.setDeviceId("42");
        prdDstItemHistoryReqDTO.setDeviceType(1);
        prdDstItemHistoryReqDTO.setEnterpriseGuid("1234");
        prdDstItemHistoryReqDTO.setEnterpriseName("Enterprise Name");
        prdDstItemHistoryReqDTO.setInvoiceCode("Invoice Code");
        prdDstItemHistoryReqDTO.setInvoicePhone("**********");
        prdDstItemHistoryReqDTO.setIsInvoiceCode(1);
        prdDstItemHistoryReqDTO.setItemSearchKey("Item Search Key");
        prdDstItemHistoryReqDTO.setLatitude("Latitude");
        prdDstItemHistoryReqDTO.setLongitude("Longitude");
        prdDstItemHistoryReqDTO.setMaxId(1L);
        prdDstItemHistoryReqDTO.setOperSubjectGuid("1234");
        prdDstItemHistoryReqDTO.setOrderNumber("42");
        prdDstItemHistoryReqDTO.setPageSize(3);
        prdDstItemHistoryReqDTO.setRequestTimestamp(1L);
        prdDstItemHistoryReqDTO.setStoreGuid("1234");
        prdDstItemHistoryReqDTO.setStoreName("Store Name");
        prdDstItemHistoryReqDTO.setTableName("Table Name");
        prdDstItemHistoryReqDTO.setUserGuid("1234");
        prdDstItemHistoryReqDTO.setUserName("janedoe");
        com.holderzone.framework.util.Page<KitchenItemDTO> actualPrdHistoryResult = kitchenItemServiceImpl
                .prdHistory(prdDstItemHistoryReqDTO);
        verify(kitchenItemMapper).selectPage(Mockito.<IPage<KitchenItemDO>>any(), Mockito.<Wrapper<KitchenItemDO>>any());
        verify(kitchenItemMapstruct).toKitchenItemDTO(Mockito.<List<KitchenItemDO>>any());
        assertEquals(0L, actualPrdHistoryResult.getTotalCount());
        assertEquals(10L, ((PageAdapter<KitchenItemDTO>) actualPrdHistoryResult).getSize());
        assertFalse(((PageAdapter<KitchenItemDTO>) actualPrdHistoryResult).hasPrevious());
        assertTrue(((PageAdapter<KitchenItemDTO>) actualPrdHistoryResult).optimizeCountSql());
        assertTrue(((PageAdapter<KitchenItemDTO>) actualPrdHistoryResult).getRecords().isEmpty());
    }

    /**
     * Method under test:
     * {@link KitchenItemServiceImpl#prdHistory(PrdDstItemHistoryReqDTO)}
     */
    @Test
    public void testPrdHistory2() {
        when(kitchenItemMapper.selectPage(Mockito.<IPage<KitchenItemDO>>any(), Mockito.<Wrapper<KitchenItemDO>>any()))
                .thenReturn(new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>());
        when(kitchenItemMapstruct.toKitchenItemDTO(Mockito.<List<KitchenItemDO>>any()))
                .thenThrow(new IllegalArgumentException("foo"));

        PrdDstItemHistoryReqDTO prdDstItemHistoryReqDTO = new PrdDstItemHistoryReqDTO();
        prdDstItemHistoryReqDTO.setAccount("3");
        prdDstItemHistoryReqDTO.setAccountName("Dr Jane Doe");
        prdDstItemHistoryReqDTO.setActuallyPayFee(new BigDecimal("2.3"));
        prdDstItemHistoryReqDTO.setCurrentPage(1);
        prdDstItemHistoryReqDTO.setDeviceId("42");
        prdDstItemHistoryReqDTO.setDeviceType(1);
        prdDstItemHistoryReqDTO.setEnterpriseGuid("1234");
        prdDstItemHistoryReqDTO.setEnterpriseName("Enterprise Name");
        prdDstItemHistoryReqDTO.setInvoiceCode("Invoice Code");
        prdDstItemHistoryReqDTO.setInvoicePhone("**********");
        prdDstItemHistoryReqDTO.setIsInvoiceCode(1);
        prdDstItemHistoryReqDTO.setItemSearchKey("Item Search Key");
        prdDstItemHistoryReqDTO.setLatitude("Latitude");
        prdDstItemHistoryReqDTO.setLongitude("Longitude");
        prdDstItemHistoryReqDTO.setMaxId(1L);
        prdDstItemHistoryReqDTO.setOperSubjectGuid("1234");
        prdDstItemHistoryReqDTO.setOrderNumber("42");
        prdDstItemHistoryReqDTO.setPageSize(3);
        prdDstItemHistoryReqDTO.setRequestTimestamp(1L);
        prdDstItemHistoryReqDTO.setStoreGuid("1234");
        prdDstItemHistoryReqDTO.setStoreName("Store Name");
        prdDstItemHistoryReqDTO.setTableName("Table Name");
        prdDstItemHistoryReqDTO.setUserGuid("1234");
        prdDstItemHistoryReqDTO.setUserName("janedoe");
        thrown.expect(IllegalArgumentException.class);
        kitchenItemServiceImpl.prdHistory(prdDstItemHistoryReqDTO);
        verify(kitchenItemMapper).selectPage(Mockito.<IPage<KitchenItemDO>>any(), Mockito.<Wrapper<KitchenItemDO>>any());
        verify(kitchenItemMapstruct).toKitchenItemDTO(Mockito.<List<KitchenItemDO>>any());
    }

    /**
     * Method under test:
     * {@link KitchenItemServiceImpl#prdHistory(PrdDstItemHistoryReqDTO)}
     */
    @Test
    public void testPrdHistory3() {
        when(kitchenItemMapper.selectPage(Mockito.<IPage<KitchenItemDO>>any(), Mockito.<Wrapper<KitchenItemDO>>any()))
                .thenReturn(new PageAdapter<>());
        when(kitchenItemMapstruct.toKitchenItemDTO(Mockito.<List<KitchenItemDO>>any())).thenReturn(new ArrayList<>());

        PrdDstItemHistoryReqDTO prdDstItemHistoryReqDTO = new PrdDstItemHistoryReqDTO();
        prdDstItemHistoryReqDTO.setAccount("3");
        prdDstItemHistoryReqDTO.setAccountName("Dr Jane Doe");
        prdDstItemHistoryReqDTO.setActuallyPayFee(new BigDecimal("2.3"));
        prdDstItemHistoryReqDTO.setCurrentPage(1);
        prdDstItemHistoryReqDTO.setDeviceId("42");
        prdDstItemHistoryReqDTO.setDeviceType(1);
        prdDstItemHistoryReqDTO.setEnterpriseGuid("1234");
        prdDstItemHistoryReqDTO.setEnterpriseName("Enterprise Name");
        prdDstItemHistoryReqDTO.setInvoiceCode("Invoice Code");
        prdDstItemHistoryReqDTO.setInvoicePhone("**********");
        prdDstItemHistoryReqDTO.setIsInvoiceCode(1);
        prdDstItemHistoryReqDTO.setItemSearchKey("Item Search Key");
        prdDstItemHistoryReqDTO.setLatitude("Latitude");
        prdDstItemHistoryReqDTO.setLongitude("Longitude");
        prdDstItemHistoryReqDTO.setMaxId(1L);
        prdDstItemHistoryReqDTO.setOperSubjectGuid("1234");
        prdDstItemHistoryReqDTO.setOrderNumber("42");
        prdDstItemHistoryReqDTO.setPageSize(3);
        prdDstItemHistoryReqDTO.setRequestTimestamp(1L);
        prdDstItemHistoryReqDTO.setStoreGuid("1234");
        prdDstItemHistoryReqDTO.setStoreName("Store Name");
        prdDstItemHistoryReqDTO.setTableName("Table Name");
        prdDstItemHistoryReqDTO.setUserGuid("1234");
        prdDstItemHistoryReqDTO.setUserName("janedoe");
        Page<KitchenItemDTO> actualPrdHistoryResult = kitchenItemServiceImpl.prdHistory(prdDstItemHistoryReqDTO);
        verify(kitchenItemMapper).selectPage(Mockito.<IPage<KitchenItemDO>>any(), Mockito.<Wrapper<KitchenItemDO>>any());
        verify(kitchenItemMapstruct).toKitchenItemDTO(Mockito.<List<KitchenItemDO>>any());
        assertEquals(0L, actualPrdHistoryResult.getTotalCount());
        assertEquals(10L, ((PageAdapter<KitchenItemDTO>) actualPrdHistoryResult).getSize());
        assertFalse(((PageAdapter<KitchenItemDTO>) actualPrdHistoryResult).hasPrevious());
        assertTrue(((PageAdapter<KitchenItemDTO>) actualPrdHistoryResult).optimizeCountSql());
        assertTrue(((PageAdapter<KitchenItemDTO>) actualPrdHistoryResult).getRecords().isEmpty());
    }

    /**
     * Method under test:
     * {@link KitchenItemServiceImpl#prdHistory(PrdDstItemHistoryReqDTO)}
     */
    @Test
    public void testPrdHistory4() {
        when(kitchenItemMapper.selectPage(Mockito.<IPage<KitchenItemDO>>any(), Mockito.<Wrapper<KitchenItemDO>>any()))
                .thenReturn(new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>());
        when(kitchenItemMapstruct.toKitchenItemDTO(Mockito.<List<KitchenItemDO>>any())).thenReturn(new ArrayList<>());
        PrdDstItemHistoryReqDTO prdDstItemHistoryReqDTO = mock(PrdDstItemHistoryReqDTO.class);
        when(prdDstItemHistoryReqDTO.getCurrentPage()).thenReturn(1);
        when(prdDstItemHistoryReqDTO.getPageSize()).thenReturn(3);
        when(prdDstItemHistoryReqDTO.getDeviceId()).thenReturn("42");
        when(prdDstItemHistoryReqDTO.getStoreGuid()).thenReturn("1234");
        when(prdDstItemHistoryReqDTO.getItemSearchKey()).thenReturn("");
        when(prdDstItemHistoryReqDTO.getOrderNumber()).thenReturn("42");
        when(prdDstItemHistoryReqDTO.getTableName()).thenReturn("Table Name");
        doNothing().when(prdDstItemHistoryReqDTO).setAccount(Mockito.<String>any());
        doNothing().when(prdDstItemHistoryReqDTO).setAccountName(Mockito.<String>any());
        doNothing().when(prdDstItemHistoryReqDTO).setActuallyPayFee(Mockito.<BigDecimal>any());
        doNothing().when(prdDstItemHistoryReqDTO).setDeviceId(Mockito.<String>any());
        doNothing().when(prdDstItemHistoryReqDTO).setDeviceType(Mockito.<Integer>any());
        doNothing().when(prdDstItemHistoryReqDTO).setEnterpriseGuid(Mockito.<String>any());
        doNothing().when(prdDstItemHistoryReqDTO).setEnterpriseName(Mockito.<String>any());
        doNothing().when(prdDstItemHistoryReqDTO).setInvoiceCode(Mockito.<String>any());
        doNothing().when(prdDstItemHistoryReqDTO).setInvoicePhone(Mockito.<String>any());
        doNothing().when(prdDstItemHistoryReqDTO).setIsInvoiceCode(Mockito.<Integer>any());
        doNothing().when(prdDstItemHistoryReqDTO).setLatitude(Mockito.<String>any());
        doNothing().when(prdDstItemHistoryReqDTO).setLongitude(Mockito.<String>any());
        doNothing().when(prdDstItemHistoryReqDTO).setOperSubjectGuid(Mockito.<String>any());
        doNothing().when(prdDstItemHistoryReqDTO).setRequestTimestamp(Mockito.<Long>any());
        doNothing().when(prdDstItemHistoryReqDTO).setStoreGuid(Mockito.<String>any());
        doNothing().when(prdDstItemHistoryReqDTO).setStoreName(Mockito.<String>any());
        doNothing().when(prdDstItemHistoryReqDTO).setUserGuid(Mockito.<String>any());
        doNothing().when(prdDstItemHistoryReqDTO).setUserName(Mockito.<String>any());
        doNothing().when(prdDstItemHistoryReqDTO).setCurrentPage(Mockito.<Integer>any());
        doNothing().when(prdDstItemHistoryReqDTO).setMaxId(Mockito.<Long>any());
        doNothing().when(prdDstItemHistoryReqDTO).setPageSize(Mockito.<Integer>any());
        doNothing().when(prdDstItemHistoryReqDTO).setItemSearchKey(Mockito.<String>any());
        doNothing().when(prdDstItemHistoryReqDTO).setOrderNumber(Mockito.<String>any());
        doNothing().when(prdDstItemHistoryReqDTO).setTableName(Mockito.<String>any());
        prdDstItemHistoryReqDTO.setAccount("3");
        prdDstItemHistoryReqDTO.setAccountName("Dr Jane Doe");
        prdDstItemHistoryReqDTO.setActuallyPayFee(new BigDecimal("2.3"));
        prdDstItemHistoryReqDTO.setCurrentPage(1);
        prdDstItemHistoryReqDTO.setDeviceId("42");
        prdDstItemHistoryReqDTO.setDeviceType(1);
        prdDstItemHistoryReqDTO.setEnterpriseGuid("1234");
        prdDstItemHistoryReqDTO.setEnterpriseName("Enterprise Name");
        prdDstItemHistoryReqDTO.setInvoiceCode("Invoice Code");
        prdDstItemHistoryReqDTO.setInvoicePhone("**********");
        prdDstItemHistoryReqDTO.setIsInvoiceCode(1);
        prdDstItemHistoryReqDTO.setItemSearchKey("Item Search Key");
        prdDstItemHistoryReqDTO.setLatitude("Latitude");
        prdDstItemHistoryReqDTO.setLongitude("Longitude");
        prdDstItemHistoryReqDTO.setMaxId(1L);
        prdDstItemHistoryReqDTO.setOperSubjectGuid("1234");
        prdDstItemHistoryReqDTO.setOrderNumber("42");
        prdDstItemHistoryReqDTO.setPageSize(3);
        prdDstItemHistoryReqDTO.setRequestTimestamp(1L);
        prdDstItemHistoryReqDTO.setStoreGuid("1234");
        prdDstItemHistoryReqDTO.setStoreName("Store Name");
        prdDstItemHistoryReqDTO.setTableName("Table Name");
        prdDstItemHistoryReqDTO.setUserGuid("1234");
        prdDstItemHistoryReqDTO.setUserName("janedoe");
        com.holderzone.framework.util.Page<KitchenItemDTO> actualPrdHistoryResult = kitchenItemServiceImpl
                .prdHistory(prdDstItemHistoryReqDTO);
        verify(kitchenItemMapper).selectPage(Mockito.<IPage<KitchenItemDO>>any(), Mockito.<Wrapper<KitchenItemDO>>any());
        verify(prdDstItemHistoryReqDTO).getDeviceId();
        verify(prdDstItemHistoryReqDTO).getStoreGuid();
        verify(prdDstItemHistoryReqDTO).setAccount(Mockito.<String>any());
        verify(prdDstItemHistoryReqDTO).setAccountName(Mockito.<String>any());
        verify(prdDstItemHistoryReqDTO).setActuallyPayFee(Mockito.<BigDecimal>any());
        verify(prdDstItemHistoryReqDTO).setDeviceId(Mockito.<String>any());
        verify(prdDstItemHistoryReqDTO).setDeviceType(Mockito.<Integer>any());
        verify(prdDstItemHistoryReqDTO).setEnterpriseGuid(Mockito.<String>any());
        verify(prdDstItemHistoryReqDTO).setEnterpriseName(Mockito.<String>any());
        verify(prdDstItemHistoryReqDTO).setInvoiceCode(Mockito.<String>any());
        verify(prdDstItemHistoryReqDTO).setInvoicePhone(Mockito.<String>any());
        verify(prdDstItemHistoryReqDTO).setIsInvoiceCode(Mockito.<Integer>any());
        verify(prdDstItemHistoryReqDTO).setLatitude(Mockito.<String>any());
        verify(prdDstItemHistoryReqDTO).setLongitude(Mockito.<String>any());
        verify(prdDstItemHistoryReqDTO).setOperSubjectGuid(Mockito.<String>any());
        verify(prdDstItemHistoryReqDTO).setRequestTimestamp(Mockito.<Long>any());
        verify(prdDstItemHistoryReqDTO).setStoreGuid(Mockito.<String>any());
        verify(prdDstItemHistoryReqDTO).setStoreName(Mockito.<String>any());
        verify(prdDstItemHistoryReqDTO).setUserGuid(Mockito.<String>any());
        verify(prdDstItemHistoryReqDTO).setUserName(Mockito.<String>any());
        verify(prdDstItemHistoryReqDTO).getCurrentPage();
        verify(prdDstItemHistoryReqDTO).getPageSize();
        verify(prdDstItemHistoryReqDTO).setCurrentPage(Mockito.<Integer>any());
        verify(prdDstItemHistoryReqDTO).setMaxId(Mockito.<Long>any());
        verify(prdDstItemHistoryReqDTO).setPageSize(Mockito.<Integer>any());
        verify(prdDstItemHistoryReqDTO, atLeast(1)).getItemSearchKey();
        verify(prdDstItemHistoryReqDTO, atLeast(1)).getOrderNumber();
        verify(prdDstItemHistoryReqDTO, atLeast(1)).getTableName();
        verify(prdDstItemHistoryReqDTO).setItemSearchKey(Mockito.<String>any());
        verify(prdDstItemHistoryReqDTO).setOrderNumber(Mockito.<String>any());
        verify(prdDstItemHistoryReqDTO).setTableName(Mockito.<String>any());
        verify(kitchenItemMapstruct).toKitchenItemDTO(Mockito.<List<KitchenItemDO>>any());
        assertEquals(0L, actualPrdHistoryResult.getTotalCount());
        assertEquals(10L, ((PageAdapter<KitchenItemDTO>) actualPrdHistoryResult).getSize());
        assertFalse(((PageAdapter<KitchenItemDTO>) actualPrdHistoryResult).hasPrevious());
        assertTrue(((PageAdapter<KitchenItemDTO>) actualPrdHistoryResult).optimizeCountSql());
        assertTrue(((PageAdapter<KitchenItemDTO>) actualPrdHistoryResult).getRecords().isEmpty());
    }

    /**
     * Method under test:
     * {@link KitchenItemServiceImpl#prdHistory(PrdDstItemHistoryReqDTO)}
     */
    @Test
    public void testPrdHistory5() {
        when(kitchenItemMapper.selectPage(Mockito.<IPage<KitchenItemDO>>any(), Mockito.<Wrapper<KitchenItemDO>>any()))
                .thenReturn(new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>());
        when(kitchenItemMapstruct.toKitchenItemDTO(Mockito.<List<KitchenItemDO>>any())).thenReturn(new ArrayList<>());
        PrdDstItemHistoryReqDTO prdDstItemHistoryReqDTO = mock(PrdDstItemHistoryReqDTO.class);
        when(prdDstItemHistoryReqDTO.getCurrentPage()).thenReturn(1);
        when(prdDstItemHistoryReqDTO.getPageSize()).thenReturn(3);
        when(prdDstItemHistoryReqDTO.getDeviceId()).thenReturn("42");
        when(prdDstItemHistoryReqDTO.getStoreGuid()).thenReturn("1234");
        when(prdDstItemHistoryReqDTO.getItemSearchKey()).thenReturn("Item Search Key");
        when(prdDstItemHistoryReqDTO.getOrderNumber()).thenReturn("");
        when(prdDstItemHistoryReqDTO.getTableName()).thenReturn("Table Name");
        doNothing().when(prdDstItemHistoryReqDTO).setAccount(Mockito.<String>any());
        doNothing().when(prdDstItemHistoryReqDTO).setAccountName(Mockito.<String>any());
        doNothing().when(prdDstItemHistoryReqDTO).setActuallyPayFee(Mockito.<BigDecimal>any());
        doNothing().when(prdDstItemHistoryReqDTO).setDeviceId(Mockito.<String>any());
        doNothing().when(prdDstItemHistoryReqDTO).setDeviceType(Mockito.<Integer>any());
        doNothing().when(prdDstItemHistoryReqDTO).setEnterpriseGuid(Mockito.<String>any());
        doNothing().when(prdDstItemHistoryReqDTO).setEnterpriseName(Mockito.<String>any());
        doNothing().when(prdDstItemHistoryReqDTO).setInvoiceCode(Mockito.<String>any());
        doNothing().when(prdDstItemHistoryReqDTO).setInvoicePhone(Mockito.<String>any());
        doNothing().when(prdDstItemHistoryReqDTO).setIsInvoiceCode(Mockito.<Integer>any());
        doNothing().when(prdDstItemHistoryReqDTO).setLatitude(Mockito.<String>any());
        doNothing().when(prdDstItemHistoryReqDTO).setLongitude(Mockito.<String>any());
        doNothing().when(prdDstItemHistoryReqDTO).setOperSubjectGuid(Mockito.<String>any());
        doNothing().when(prdDstItemHistoryReqDTO).setRequestTimestamp(Mockito.<Long>any());
        doNothing().when(prdDstItemHistoryReqDTO).setStoreGuid(Mockito.<String>any());
        doNothing().when(prdDstItemHistoryReqDTO).setStoreName(Mockito.<String>any());
        doNothing().when(prdDstItemHistoryReqDTO).setUserGuid(Mockito.<String>any());
        doNothing().when(prdDstItemHistoryReqDTO).setUserName(Mockito.<String>any());
        doNothing().when(prdDstItemHistoryReqDTO).setCurrentPage(Mockito.<Integer>any());
        doNothing().when(prdDstItemHistoryReqDTO).setMaxId(Mockito.<Long>any());
        doNothing().when(prdDstItemHistoryReqDTO).setPageSize(Mockito.<Integer>any());
        doNothing().when(prdDstItemHistoryReqDTO).setItemSearchKey(Mockito.<String>any());
        doNothing().when(prdDstItemHistoryReqDTO).setOrderNumber(Mockito.<String>any());
        doNothing().when(prdDstItemHistoryReqDTO).setTableName(Mockito.<String>any());
        prdDstItemHistoryReqDTO.setAccount("3");
        prdDstItemHistoryReqDTO.setAccountName("Dr Jane Doe");
        prdDstItemHistoryReqDTO.setActuallyPayFee(new BigDecimal("2.3"));
        prdDstItemHistoryReqDTO.setCurrentPage(1);
        prdDstItemHistoryReqDTO.setDeviceId("42");
        prdDstItemHistoryReqDTO.setDeviceType(1);
        prdDstItemHistoryReqDTO.setEnterpriseGuid("1234");
        prdDstItemHistoryReqDTO.setEnterpriseName("Enterprise Name");
        prdDstItemHistoryReqDTO.setInvoiceCode("Invoice Code");
        prdDstItemHistoryReqDTO.setInvoicePhone("**********");
        prdDstItemHistoryReqDTO.setIsInvoiceCode(1);
        prdDstItemHistoryReqDTO.setItemSearchKey("Item Search Key");
        prdDstItemHistoryReqDTO.setLatitude("Latitude");
        prdDstItemHistoryReqDTO.setLongitude("Longitude");
        prdDstItemHistoryReqDTO.setMaxId(1L);
        prdDstItemHistoryReqDTO.setOperSubjectGuid("1234");
        prdDstItemHistoryReqDTO.setOrderNumber("42");
        prdDstItemHistoryReqDTO.setPageSize(3);
        prdDstItemHistoryReqDTO.setRequestTimestamp(1L);
        prdDstItemHistoryReqDTO.setStoreGuid("1234");
        prdDstItemHistoryReqDTO.setStoreName("Store Name");
        prdDstItemHistoryReqDTO.setTableName("Table Name");
        prdDstItemHistoryReqDTO.setUserGuid("1234");
        prdDstItemHistoryReqDTO.setUserName("janedoe");
        com.holderzone.framework.util.Page<KitchenItemDTO> actualPrdHistoryResult = kitchenItemServiceImpl
                .prdHistory(prdDstItemHistoryReqDTO);
        verify(kitchenItemMapper).selectPage(Mockito.<IPage<KitchenItemDO>>any(), Mockito.<Wrapper<KitchenItemDO>>any());
        verify(prdDstItemHistoryReqDTO).getDeviceId();
        verify(prdDstItemHistoryReqDTO).getStoreGuid();
        verify(prdDstItemHistoryReqDTO).setAccount(Mockito.<String>any());
        verify(prdDstItemHistoryReqDTO).setAccountName(Mockito.<String>any());
        verify(prdDstItemHistoryReqDTO).setActuallyPayFee(Mockito.<BigDecimal>any());
        verify(prdDstItemHistoryReqDTO).setDeviceId(Mockito.<String>any());
        verify(prdDstItemHistoryReqDTO).setDeviceType(Mockito.<Integer>any());
        verify(prdDstItemHistoryReqDTO).setEnterpriseGuid(Mockito.<String>any());
        verify(prdDstItemHistoryReqDTO).setEnterpriseName(Mockito.<String>any());
        verify(prdDstItemHistoryReqDTO).setInvoiceCode(Mockito.<String>any());
        verify(prdDstItemHistoryReqDTO).setInvoicePhone(Mockito.<String>any());
        verify(prdDstItemHistoryReqDTO).setIsInvoiceCode(Mockito.<Integer>any());
        verify(prdDstItemHistoryReqDTO).setLatitude(Mockito.<String>any());
        verify(prdDstItemHistoryReqDTO).setLongitude(Mockito.<String>any());
        verify(prdDstItemHistoryReqDTO).setOperSubjectGuid(Mockito.<String>any());
        verify(prdDstItemHistoryReqDTO).setRequestTimestamp(Mockito.<Long>any());
        verify(prdDstItemHistoryReqDTO).setStoreGuid(Mockito.<String>any());
        verify(prdDstItemHistoryReqDTO).setStoreName(Mockito.<String>any());
        verify(prdDstItemHistoryReqDTO).setUserGuid(Mockito.<String>any());
        verify(prdDstItemHistoryReqDTO).setUserName(Mockito.<String>any());
        verify(prdDstItemHistoryReqDTO).getCurrentPage();
        verify(prdDstItemHistoryReqDTO).getPageSize();
        verify(prdDstItemHistoryReqDTO).setCurrentPage(Mockito.<Integer>any());
        verify(prdDstItemHistoryReqDTO).setMaxId(Mockito.<Long>any());
        verify(prdDstItemHistoryReqDTO).setPageSize(Mockito.<Integer>any());
        verify(prdDstItemHistoryReqDTO, atLeast(1)).getItemSearchKey();
        verify(prdDstItemHistoryReqDTO, atLeast(1)).getOrderNumber();
        verify(prdDstItemHistoryReqDTO, atLeast(1)).getTableName();
        verify(prdDstItemHistoryReqDTO).setItemSearchKey(Mockito.<String>any());
        verify(prdDstItemHistoryReqDTO).setOrderNumber(Mockito.<String>any());
        verify(prdDstItemHistoryReqDTO).setTableName(Mockito.<String>any());
        verify(kitchenItemMapstruct).toKitchenItemDTO(Mockito.<List<KitchenItemDO>>any());
        assertEquals(0L, actualPrdHistoryResult.getTotalCount());
        assertEquals(10L, ((PageAdapter<KitchenItemDTO>) actualPrdHistoryResult).getSize());
        assertFalse(((PageAdapter<KitchenItemDTO>) actualPrdHistoryResult).hasPrevious());
        assertTrue(((PageAdapter<KitchenItemDTO>) actualPrdHistoryResult).optimizeCountSql());
        assertTrue(((PageAdapter<KitchenItemDTO>) actualPrdHistoryResult).getRecords().isEmpty());
    }

    /**
     * Method under test:
     * {@link KitchenItemServiceImpl#dstHistory(PrdDstItemHistoryReqDTO)}
     */
    @Test
    public void testDstHistory() {
        when(kitchenItemMapper.selectPage(Mockito.<IPage<KitchenItemDO>>any(), Mockito.<Wrapper<KitchenItemDO>>any()))
                .thenReturn(new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>());
        when(kitchenItemMapstruct.toKitchenItemDTO(Mockito.<List<KitchenItemDO>>any())).thenReturn(new ArrayList<>());

        PrdDstItemHistoryReqDTO prdDstItemHistoryReqDTO = new PrdDstItemHistoryReqDTO();
        prdDstItemHistoryReqDTO.setAccount("3");
        prdDstItemHistoryReqDTO.setAccountName("Dr Jane Doe");
        prdDstItemHistoryReqDTO.setActuallyPayFee(new BigDecimal("2.3"));
        prdDstItemHistoryReqDTO.setCurrentPage(1);
        prdDstItemHistoryReqDTO.setDeviceId("42");
        prdDstItemHistoryReqDTO.setDeviceType(1);
        prdDstItemHistoryReqDTO.setEnterpriseGuid("1234");
        prdDstItemHistoryReqDTO.setEnterpriseName("Enterprise Name");
        prdDstItemHistoryReqDTO.setInvoiceCode("Invoice Code");
        prdDstItemHistoryReqDTO.setInvoicePhone("**********");
        prdDstItemHistoryReqDTO.setIsInvoiceCode(1);
        prdDstItemHistoryReqDTO.setItemSearchKey("Item Search Key");
        prdDstItemHistoryReqDTO.setLatitude("Latitude");
        prdDstItemHistoryReqDTO.setLongitude("Longitude");
        prdDstItemHistoryReqDTO.setMaxId(1L);
        prdDstItemHistoryReqDTO.setOperSubjectGuid("1234");
        prdDstItemHistoryReqDTO.setOrderNumber("42");
        prdDstItemHistoryReqDTO.setPageSize(3);
        prdDstItemHistoryReqDTO.setRequestTimestamp(1L);
        prdDstItemHistoryReqDTO.setStoreGuid("1234");
        prdDstItemHistoryReqDTO.setStoreName("Store Name");
        prdDstItemHistoryReqDTO.setTableName("Table Name");
        prdDstItemHistoryReqDTO.setUserGuid("1234");
        prdDstItemHistoryReqDTO.setUserName("janedoe");
        com.holderzone.framework.util.Page<KitchenItemDTO> actualDstHistoryResult = kitchenItemServiceImpl
                .dstHistory(prdDstItemHistoryReqDTO);
        verify(kitchenItemMapper).selectPage(Mockito.<IPage<KitchenItemDO>>any(), Mockito.<Wrapper<KitchenItemDO>>any());
        verify(kitchenItemMapstruct).toKitchenItemDTO(Mockito.<List<KitchenItemDO>>any());
        assertEquals(0L, actualDstHistoryResult.getTotalCount());
        assertEquals(10L, ((PageAdapter<KitchenItemDTO>) actualDstHistoryResult).getSize());
        assertFalse(((PageAdapter<KitchenItemDTO>) actualDstHistoryResult).hasPrevious());
        assertTrue(((PageAdapter<KitchenItemDTO>) actualDstHistoryResult).optimizeCountSql());
        assertTrue(((PageAdapter<KitchenItemDTO>) actualDstHistoryResult).getRecords().isEmpty());
    }

    /**
     * Method under test:
     * {@link KitchenItemServiceImpl#dstHistory(PrdDstItemHistoryReqDTO)}
     */
    @Test
    public void testDstHistory2() {
        when(kitchenItemMapper.selectPage(Mockito.<IPage<KitchenItemDO>>any(), Mockito.<Wrapper<KitchenItemDO>>any()))
                .thenReturn(new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>());
        when(kitchenItemMapstruct.toKitchenItemDTO(Mockito.<List<KitchenItemDO>>any()))
                .thenThrow(new IllegalArgumentException("foo"));

        PrdDstItemHistoryReqDTO prdDstItemHistoryReqDTO = new PrdDstItemHistoryReqDTO();
        prdDstItemHistoryReqDTO.setAccount("3");
        prdDstItemHistoryReqDTO.setAccountName("Dr Jane Doe");
        prdDstItemHistoryReqDTO.setActuallyPayFee(new BigDecimal("2.3"));
        prdDstItemHistoryReqDTO.setCurrentPage(1);
        prdDstItemHistoryReqDTO.setDeviceId("42");
        prdDstItemHistoryReqDTO.setDeviceType(1);
        prdDstItemHistoryReqDTO.setEnterpriseGuid("1234");
        prdDstItemHistoryReqDTO.setEnterpriseName("Enterprise Name");
        prdDstItemHistoryReqDTO.setInvoiceCode("Invoice Code");
        prdDstItemHistoryReqDTO.setInvoicePhone("**********");
        prdDstItemHistoryReqDTO.setIsInvoiceCode(1);
        prdDstItemHistoryReqDTO.setItemSearchKey("Item Search Key");
        prdDstItemHistoryReqDTO.setLatitude("Latitude");
        prdDstItemHistoryReqDTO.setLongitude("Longitude");
        prdDstItemHistoryReqDTO.setMaxId(1L);
        prdDstItemHistoryReqDTO.setOperSubjectGuid("1234");
        prdDstItemHistoryReqDTO.setOrderNumber("42");
        prdDstItemHistoryReqDTO.setPageSize(3);
        prdDstItemHistoryReqDTO.setRequestTimestamp(1L);
        prdDstItemHistoryReqDTO.setStoreGuid("1234");
        prdDstItemHistoryReqDTO.setStoreName("Store Name");
        prdDstItemHistoryReqDTO.setTableName("Table Name");
        prdDstItemHistoryReqDTO.setUserGuid("1234");
        prdDstItemHistoryReqDTO.setUserName("janedoe");
        thrown.expect(IllegalArgumentException.class);
        kitchenItemServiceImpl.dstHistory(prdDstItemHistoryReqDTO);
        verify(kitchenItemMapper).selectPage(Mockito.<IPage<KitchenItemDO>>any(), Mockito.<Wrapper<KitchenItemDO>>any());
        verify(kitchenItemMapstruct).toKitchenItemDTO(Mockito.<List<KitchenItemDO>>any());
    }

    /**
     * Method under test:
     * {@link KitchenItemServiceImpl#dstHistory(PrdDstItemHistoryReqDTO)}
     */
    @Test
    public void testDstHistory3() {
        when(kitchenItemMapper.selectPage(Mockito.<IPage<KitchenItemDO>>any(), Mockito.<Wrapper<KitchenItemDO>>any()))
                .thenReturn(new PageAdapter<>());
        when(kitchenItemMapstruct.toKitchenItemDTO(Mockito.<List<KitchenItemDO>>any())).thenReturn(new ArrayList<>());

        PrdDstItemHistoryReqDTO prdDstItemHistoryReqDTO = new PrdDstItemHistoryReqDTO();
        prdDstItemHistoryReqDTO.setAccount("3");
        prdDstItemHistoryReqDTO.setAccountName("Dr Jane Doe");
        prdDstItemHistoryReqDTO.setActuallyPayFee(new BigDecimal("2.3"));
        prdDstItemHistoryReqDTO.setCurrentPage(1);
        prdDstItemHistoryReqDTO.setDeviceId("42");
        prdDstItemHistoryReqDTO.setDeviceType(1);
        prdDstItemHistoryReqDTO.setEnterpriseGuid("1234");
        prdDstItemHistoryReqDTO.setEnterpriseName("Enterprise Name");
        prdDstItemHistoryReqDTO.setInvoiceCode("Invoice Code");
        prdDstItemHistoryReqDTO.setInvoicePhone("**********");
        prdDstItemHistoryReqDTO.setIsInvoiceCode(1);
        prdDstItemHistoryReqDTO.setItemSearchKey("Item Search Key");
        prdDstItemHistoryReqDTO.setLatitude("Latitude");
        prdDstItemHistoryReqDTO.setLongitude("Longitude");
        prdDstItemHistoryReqDTO.setMaxId(1L);
        prdDstItemHistoryReqDTO.setOperSubjectGuid("1234");
        prdDstItemHistoryReqDTO.setOrderNumber("42");
        prdDstItemHistoryReqDTO.setPageSize(3);
        prdDstItemHistoryReqDTO.setRequestTimestamp(1L);
        prdDstItemHistoryReqDTO.setStoreGuid("1234");
        prdDstItemHistoryReqDTO.setStoreName("Store Name");
        prdDstItemHistoryReqDTO.setTableName("Table Name");
        prdDstItemHistoryReqDTO.setUserGuid("1234");
        prdDstItemHistoryReqDTO.setUserName("janedoe");
        Page<KitchenItemDTO> actualDstHistoryResult = kitchenItemServiceImpl.dstHistory(prdDstItemHistoryReqDTO);
        verify(kitchenItemMapper).selectPage(Mockito.<IPage<KitchenItemDO>>any(), Mockito.<Wrapper<KitchenItemDO>>any());
        verify(kitchenItemMapstruct).toKitchenItemDTO(Mockito.<List<KitchenItemDO>>any());
        assertEquals(0L, actualDstHistoryResult.getTotalCount());
        assertEquals(10L, ((PageAdapter<KitchenItemDTO>) actualDstHistoryResult).getSize());
        assertFalse(((PageAdapter<KitchenItemDTO>) actualDstHistoryResult).hasPrevious());
        assertTrue(((PageAdapter<KitchenItemDTO>) actualDstHistoryResult).optimizeCountSql());
        assertTrue(((PageAdapter<KitchenItemDTO>) actualDstHistoryResult).getRecords().isEmpty());
    }

    /**
     * Method under test:
     * {@link KitchenItemServiceImpl#dstHistory(PrdDstItemHistoryReqDTO)}
     */
    @Test
    public void testDstHistory4() {
        when(kitchenItemMapper.selectPage(Mockito.<IPage<KitchenItemDO>>any(), Mockito.<Wrapper<KitchenItemDO>>any()))
                .thenReturn(new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>());
        when(kitchenItemMapstruct.toKitchenItemDTO(Mockito.<List<KitchenItemDO>>any())).thenReturn(new ArrayList<>());
        PrdDstItemHistoryReqDTO prdDstItemHistoryReqDTO = mock(PrdDstItemHistoryReqDTO.class);
        when(prdDstItemHistoryReqDTO.getCurrentPage()).thenReturn(1);
        when(prdDstItemHistoryReqDTO.getPageSize()).thenReturn(3);
        when(prdDstItemHistoryReqDTO.getDeviceId()).thenReturn("42");
        when(prdDstItemHistoryReqDTO.getStoreGuid()).thenReturn("1234");
        when(prdDstItemHistoryReqDTO.getItemSearchKey()).thenReturn("");
        when(prdDstItemHistoryReqDTO.getOrderNumber()).thenReturn("42");
        when(prdDstItemHistoryReqDTO.getTableName()).thenReturn("Table Name");
        doNothing().when(prdDstItemHistoryReqDTO).setAccount(Mockito.<String>any());
        doNothing().when(prdDstItemHistoryReqDTO).setAccountName(Mockito.<String>any());
        doNothing().when(prdDstItemHistoryReqDTO).setActuallyPayFee(Mockito.<BigDecimal>any());
        doNothing().when(prdDstItemHistoryReqDTO).setDeviceId(Mockito.<String>any());
        doNothing().when(prdDstItemHistoryReqDTO).setDeviceType(Mockito.<Integer>any());
        doNothing().when(prdDstItemHistoryReqDTO).setEnterpriseGuid(Mockito.<String>any());
        doNothing().when(prdDstItemHistoryReqDTO).setEnterpriseName(Mockito.<String>any());
        doNothing().when(prdDstItemHistoryReqDTO).setInvoiceCode(Mockito.<String>any());
        doNothing().when(prdDstItemHistoryReqDTO).setInvoicePhone(Mockito.<String>any());
        doNothing().when(prdDstItemHistoryReqDTO).setIsInvoiceCode(Mockito.<Integer>any());
        doNothing().when(prdDstItemHistoryReqDTO).setLatitude(Mockito.<String>any());
        doNothing().when(prdDstItemHistoryReqDTO).setLongitude(Mockito.<String>any());
        doNothing().when(prdDstItemHistoryReqDTO).setOperSubjectGuid(Mockito.<String>any());
        doNothing().when(prdDstItemHistoryReqDTO).setRequestTimestamp(Mockito.<Long>any());
        doNothing().when(prdDstItemHistoryReqDTO).setStoreGuid(Mockito.<String>any());
        doNothing().when(prdDstItemHistoryReqDTO).setStoreName(Mockito.<String>any());
        doNothing().when(prdDstItemHistoryReqDTO).setUserGuid(Mockito.<String>any());
        doNothing().when(prdDstItemHistoryReqDTO).setUserName(Mockito.<String>any());
        doNothing().when(prdDstItemHistoryReqDTO).setCurrentPage(Mockito.<Integer>any());
        doNothing().when(prdDstItemHistoryReqDTO).setMaxId(Mockito.<Long>any());
        doNothing().when(prdDstItemHistoryReqDTO).setPageSize(Mockito.<Integer>any());
        doNothing().when(prdDstItemHistoryReqDTO).setItemSearchKey(Mockito.<String>any());
        doNothing().when(prdDstItemHistoryReqDTO).setOrderNumber(Mockito.<String>any());
        doNothing().when(prdDstItemHistoryReqDTO).setTableName(Mockito.<String>any());
        prdDstItemHistoryReqDTO.setAccount("3");
        prdDstItemHistoryReqDTO.setAccountName("Dr Jane Doe");
        prdDstItemHistoryReqDTO.setActuallyPayFee(new BigDecimal("2.3"));
        prdDstItemHistoryReqDTO.setCurrentPage(1);
        prdDstItemHistoryReqDTO.setDeviceId("42");
        prdDstItemHistoryReqDTO.setDeviceType(1);
        prdDstItemHistoryReqDTO.setEnterpriseGuid("1234");
        prdDstItemHistoryReqDTO.setEnterpriseName("Enterprise Name");
        prdDstItemHistoryReqDTO.setInvoiceCode("Invoice Code");
        prdDstItemHistoryReqDTO.setInvoicePhone("**********");
        prdDstItemHistoryReqDTO.setIsInvoiceCode(1);
        prdDstItemHistoryReqDTO.setItemSearchKey("Item Search Key");
        prdDstItemHistoryReqDTO.setLatitude("Latitude");
        prdDstItemHistoryReqDTO.setLongitude("Longitude");
        prdDstItemHistoryReqDTO.setMaxId(1L);
        prdDstItemHistoryReqDTO.setOperSubjectGuid("1234");
        prdDstItemHistoryReqDTO.setOrderNumber("42");
        prdDstItemHistoryReqDTO.setPageSize(3);
        prdDstItemHistoryReqDTO.setRequestTimestamp(1L);
        prdDstItemHistoryReqDTO.setStoreGuid("1234");
        prdDstItemHistoryReqDTO.setStoreName("Store Name");
        prdDstItemHistoryReqDTO.setTableName("Table Name");
        prdDstItemHistoryReqDTO.setUserGuid("1234");
        prdDstItemHistoryReqDTO.setUserName("janedoe");
        com.holderzone.framework.util.Page<KitchenItemDTO> actualDstHistoryResult = kitchenItemServiceImpl
                .dstHistory(prdDstItemHistoryReqDTO);
        verify(kitchenItemMapper).selectPage(Mockito.<IPage<KitchenItemDO>>any(), Mockito.<Wrapper<KitchenItemDO>>any());
        verify(prdDstItemHistoryReqDTO).getDeviceId();
        verify(prdDstItemHistoryReqDTO).getStoreGuid();
        verify(prdDstItemHistoryReqDTO).setAccount(Mockito.<String>any());
        verify(prdDstItemHistoryReqDTO).setAccountName(Mockito.<String>any());
        verify(prdDstItemHistoryReqDTO).setActuallyPayFee(Mockito.<BigDecimal>any());
        verify(prdDstItemHistoryReqDTO).setDeviceId(Mockito.<String>any());
        verify(prdDstItemHistoryReqDTO).setDeviceType(Mockito.<Integer>any());
        verify(prdDstItemHistoryReqDTO).setEnterpriseGuid(Mockito.<String>any());
        verify(prdDstItemHistoryReqDTO).setEnterpriseName(Mockito.<String>any());
        verify(prdDstItemHistoryReqDTO).setInvoiceCode(Mockito.<String>any());
        verify(prdDstItemHistoryReqDTO).setInvoicePhone(Mockito.<String>any());
        verify(prdDstItemHistoryReqDTO).setIsInvoiceCode(Mockito.<Integer>any());
        verify(prdDstItemHistoryReqDTO).setLatitude(Mockito.<String>any());
        verify(prdDstItemHistoryReqDTO).setLongitude(Mockito.<String>any());
        verify(prdDstItemHistoryReqDTO).setOperSubjectGuid(Mockito.<String>any());
        verify(prdDstItemHistoryReqDTO).setRequestTimestamp(Mockito.<Long>any());
        verify(prdDstItemHistoryReqDTO).setStoreGuid(Mockito.<String>any());
        verify(prdDstItemHistoryReqDTO).setStoreName(Mockito.<String>any());
        verify(prdDstItemHistoryReqDTO).setUserGuid(Mockito.<String>any());
        verify(prdDstItemHistoryReqDTO).setUserName(Mockito.<String>any());
        verify(prdDstItemHistoryReqDTO).getCurrentPage();
        verify(prdDstItemHistoryReqDTO).getPageSize();
        verify(prdDstItemHistoryReqDTO).setCurrentPage(Mockito.<Integer>any());
        verify(prdDstItemHistoryReqDTO).setMaxId(Mockito.<Long>any());
        verify(prdDstItemHistoryReqDTO).setPageSize(Mockito.<Integer>any());
        verify(prdDstItemHistoryReqDTO, atLeast(1)).getItemSearchKey();
        verify(prdDstItemHistoryReqDTO, atLeast(1)).getOrderNumber();
        verify(prdDstItemHistoryReqDTO, atLeast(1)).getTableName();
        verify(prdDstItemHistoryReqDTO).setItemSearchKey(Mockito.<String>any());
        verify(prdDstItemHistoryReqDTO).setOrderNumber(Mockito.<String>any());
        verify(prdDstItemHistoryReqDTO).setTableName(Mockito.<String>any());
        verify(kitchenItemMapstruct).toKitchenItemDTO(Mockito.<List<KitchenItemDO>>any());
        assertEquals(0L, actualDstHistoryResult.getTotalCount());
        assertEquals(10L, ((PageAdapter<KitchenItemDTO>) actualDstHistoryResult).getSize());
        assertFalse(((PageAdapter<KitchenItemDTO>) actualDstHistoryResult).hasPrevious());
        assertTrue(((PageAdapter<KitchenItemDTO>) actualDstHistoryResult).optimizeCountSql());
        assertTrue(((PageAdapter<KitchenItemDTO>) actualDstHistoryResult).getRecords().isEmpty());
    }

    /**
     * Method under test:
     * {@link KitchenItemServiceImpl#dstHistory(PrdDstItemHistoryReqDTO)}
     */
    @Test
    public void testDstHistory5() {
        when(kitchenItemMapper.selectPage(Mockito.<IPage<KitchenItemDO>>any(), Mockito.<Wrapper<KitchenItemDO>>any()))
                .thenReturn(new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>());
        when(kitchenItemMapstruct.toKitchenItemDTO(Mockito.<List<KitchenItemDO>>any())).thenReturn(new ArrayList<>());
        PrdDstItemHistoryReqDTO prdDstItemHistoryReqDTO = mock(PrdDstItemHistoryReqDTO.class);
        when(prdDstItemHistoryReqDTO.getCurrentPage()).thenReturn(1);
        when(prdDstItemHistoryReqDTO.getPageSize()).thenReturn(3);
        when(prdDstItemHistoryReqDTO.getDeviceId()).thenReturn("42");
        when(prdDstItemHistoryReqDTO.getStoreGuid()).thenReturn("1234");
        when(prdDstItemHistoryReqDTO.getItemSearchKey()).thenReturn("Item Search Key");
        when(prdDstItemHistoryReqDTO.getOrderNumber()).thenReturn("");
        when(prdDstItemHistoryReqDTO.getTableName()).thenReturn("Table Name");
        doNothing().when(prdDstItemHistoryReqDTO).setAccount(Mockito.<String>any());
        doNothing().when(prdDstItemHistoryReqDTO).setAccountName(Mockito.<String>any());
        doNothing().when(prdDstItemHistoryReqDTO).setActuallyPayFee(Mockito.<BigDecimal>any());
        doNothing().when(prdDstItemHistoryReqDTO).setDeviceId(Mockito.<String>any());
        doNothing().when(prdDstItemHistoryReqDTO).setDeviceType(Mockito.<Integer>any());
        doNothing().when(prdDstItemHistoryReqDTO).setEnterpriseGuid(Mockito.<String>any());
        doNothing().when(prdDstItemHistoryReqDTO).setEnterpriseName(Mockito.<String>any());
        doNothing().when(prdDstItemHistoryReqDTO).setInvoiceCode(Mockito.<String>any());
        doNothing().when(prdDstItemHistoryReqDTO).setInvoicePhone(Mockito.<String>any());
        doNothing().when(prdDstItemHistoryReqDTO).setIsInvoiceCode(Mockito.<Integer>any());
        doNothing().when(prdDstItemHistoryReqDTO).setLatitude(Mockito.<String>any());
        doNothing().when(prdDstItemHistoryReqDTO).setLongitude(Mockito.<String>any());
        doNothing().when(prdDstItemHistoryReqDTO).setOperSubjectGuid(Mockito.<String>any());
        doNothing().when(prdDstItemHistoryReqDTO).setRequestTimestamp(Mockito.<Long>any());
        doNothing().when(prdDstItemHistoryReqDTO).setStoreGuid(Mockito.<String>any());
        doNothing().when(prdDstItemHistoryReqDTO).setStoreName(Mockito.<String>any());
        doNothing().when(prdDstItemHistoryReqDTO).setUserGuid(Mockito.<String>any());
        doNothing().when(prdDstItemHistoryReqDTO).setUserName(Mockito.<String>any());
        doNothing().when(prdDstItemHistoryReqDTO).setCurrentPage(Mockito.<Integer>any());
        doNothing().when(prdDstItemHistoryReqDTO).setMaxId(Mockito.<Long>any());
        doNothing().when(prdDstItemHistoryReqDTO).setPageSize(Mockito.<Integer>any());
        doNothing().when(prdDstItemHistoryReqDTO).setItemSearchKey(Mockito.<String>any());
        doNothing().when(prdDstItemHistoryReqDTO).setOrderNumber(Mockito.<String>any());
        doNothing().when(prdDstItemHistoryReqDTO).setTableName(Mockito.<String>any());
        prdDstItemHistoryReqDTO.setAccount("3");
        prdDstItemHistoryReqDTO.setAccountName("Dr Jane Doe");
        prdDstItemHistoryReqDTO.setActuallyPayFee(new BigDecimal("2.3"));
        prdDstItemHistoryReqDTO.setCurrentPage(1);
        prdDstItemHistoryReqDTO.setDeviceId("42");
        prdDstItemHistoryReqDTO.setDeviceType(1);
        prdDstItemHistoryReqDTO.setEnterpriseGuid("1234");
        prdDstItemHistoryReqDTO.setEnterpriseName("Enterprise Name");
        prdDstItemHistoryReqDTO.setInvoiceCode("Invoice Code");
        prdDstItemHistoryReqDTO.setInvoicePhone("**********");
        prdDstItemHistoryReqDTO.setIsInvoiceCode(1);
        prdDstItemHistoryReqDTO.setItemSearchKey("Item Search Key");
        prdDstItemHistoryReqDTO.setLatitude("Latitude");
        prdDstItemHistoryReqDTO.setLongitude("Longitude");
        prdDstItemHistoryReqDTO.setMaxId(1L);
        prdDstItemHistoryReqDTO.setOperSubjectGuid("1234");
        prdDstItemHistoryReqDTO.setOrderNumber("42");
        prdDstItemHistoryReqDTO.setPageSize(3);
        prdDstItemHistoryReqDTO.setRequestTimestamp(1L);
        prdDstItemHistoryReqDTO.setStoreGuid("1234");
        prdDstItemHistoryReqDTO.setStoreName("Store Name");
        prdDstItemHistoryReqDTO.setTableName("Table Name");
        prdDstItemHistoryReqDTO.setUserGuid("1234");
        prdDstItemHistoryReqDTO.setUserName("janedoe");
        com.holderzone.framework.util.Page<KitchenItemDTO> actualDstHistoryResult = kitchenItemServiceImpl
                .dstHistory(prdDstItemHistoryReqDTO);
        verify(kitchenItemMapper).selectPage(Mockito.<IPage<KitchenItemDO>>any(), Mockito.<Wrapper<KitchenItemDO>>any());
        verify(prdDstItemHistoryReqDTO).getDeviceId();
        verify(prdDstItemHistoryReqDTO).getStoreGuid();
        verify(prdDstItemHistoryReqDTO).setAccount(Mockito.<String>any());
        verify(prdDstItemHistoryReqDTO).setAccountName(Mockito.<String>any());
        verify(prdDstItemHistoryReqDTO).setActuallyPayFee(Mockito.<BigDecimal>any());
        verify(prdDstItemHistoryReqDTO).setDeviceId(Mockito.<String>any());
        verify(prdDstItemHistoryReqDTO).setDeviceType(Mockito.<Integer>any());
        verify(prdDstItemHistoryReqDTO).setEnterpriseGuid(Mockito.<String>any());
        verify(prdDstItemHistoryReqDTO).setEnterpriseName(Mockito.<String>any());
        verify(prdDstItemHistoryReqDTO).setInvoiceCode(Mockito.<String>any());
        verify(prdDstItemHistoryReqDTO).setInvoicePhone(Mockito.<String>any());
        verify(prdDstItemHistoryReqDTO).setIsInvoiceCode(Mockito.<Integer>any());
        verify(prdDstItemHistoryReqDTO).setLatitude(Mockito.<String>any());
        verify(prdDstItemHistoryReqDTO).setLongitude(Mockito.<String>any());
        verify(prdDstItemHistoryReqDTO).setOperSubjectGuid(Mockito.<String>any());
        verify(prdDstItemHistoryReqDTO).setRequestTimestamp(Mockito.<Long>any());
        verify(prdDstItemHistoryReqDTO).setStoreGuid(Mockito.<String>any());
        verify(prdDstItemHistoryReqDTO).setStoreName(Mockito.<String>any());
        verify(prdDstItemHistoryReqDTO).setUserGuid(Mockito.<String>any());
        verify(prdDstItemHistoryReqDTO).setUserName(Mockito.<String>any());
        verify(prdDstItemHistoryReqDTO).getCurrentPage();
        verify(prdDstItemHistoryReqDTO).getPageSize();
        verify(prdDstItemHistoryReqDTO).setCurrentPage(Mockito.<Integer>any());
        verify(prdDstItemHistoryReqDTO).setMaxId(Mockito.<Long>any());
        verify(prdDstItemHistoryReqDTO).setPageSize(Mockito.<Integer>any());
        verify(prdDstItemHistoryReqDTO, atLeast(1)).getItemSearchKey();
        verify(prdDstItemHistoryReqDTO, atLeast(1)).getOrderNumber();
        verify(prdDstItemHistoryReqDTO, atLeast(1)).getTableName();
        verify(prdDstItemHistoryReqDTO).setItemSearchKey(Mockito.<String>any());
        verify(prdDstItemHistoryReqDTO).setOrderNumber(Mockito.<String>any());
        verify(prdDstItemHistoryReqDTO).setTableName(Mockito.<String>any());
        verify(kitchenItemMapstruct).toKitchenItemDTO(Mockito.<List<KitchenItemDO>>any());
        assertEquals(0L, actualDstHistoryResult.getTotalCount());
        assertEquals(10L, ((PageAdapter<KitchenItemDTO>) actualDstHistoryResult).getSize());
        assertFalse(((PageAdapter<KitchenItemDTO>) actualDstHistoryResult).hasPrevious());
        assertTrue(((PageAdapter<KitchenItemDTO>) actualDstHistoryResult).optimizeCountSql());
        assertTrue(((PageAdapter<KitchenItemDTO>) actualDstHistoryResult).getRecords().isEmpty());
    }

    /**
     * Method under test:
     * {@link KitchenItemServiceImpl#prdPrintAgain(KitchenItemDTO)}
     */
    @Test
    public void testPrdPrintAgain() {
        doNothing().when(kdsPrintRecordService)
                .printSingleItem(Mockito.<String>any(), Mockito.<PrdDstItemDTO>any(), Mockito.<KdsInvoiceTypeEnum>any());
        when(kitchenItemMapstruct.kitchenToPrdDstItem(Mockito.<KitchenItemDTO>any())).thenReturn(new PrdDstItemDTO());

        KitchenItemDTO kitchenItemDTO = new KitchenItemDTO();
        kitchenItemDTO.setAreaGuid("1234");
        kitchenItemDTO.setCallUpTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDTO.setCancelDstStaffGuid("1234");
        kitchenItemDTO.setCancelDstStaffName("Cancel Dst Staff Name");
        kitchenItemDTO.setCancelDstTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDTO.setCompleteStaffGuid("1234");
        kitchenItemDTO.setCompleteStaffName("Complete Staff Name");
        kitchenItemDTO.setCompleteTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDTO.setCookStaffGuid("1234");
        kitchenItemDTO.setCookStaffName("Cook Staff Name");
        kitchenItemDTO.setCookTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDTO.setCurrentCount(new BigDecimal("2.3"));
        kitchenItemDTO.setDisplayType(1);
        kitchenItemDTO.setDistributeStaffGuid("1234");
        kitchenItemDTO.setDistributeStaffName("Distribute Staff Name");
        kitchenItemDTO.setDistributeTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDTO.setDstDeviceId("42");
        kitchenItemDTO.setGuid("1234");
        kitchenItemDTO.setHangUpTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDTO.setIsWeight(true);
        kitchenItemDTO.setItemAttrMd5("27c7cf400229103e00c6d8830029e29b");
        kitchenItemDTO.setItemGuid("1234");
        kitchenItemDTO.setItemName("Item Name");
        kitchenItemDTO.setItemRemark("Item Remark");
        kitchenItemDTO.setItemState(42);
        kitchenItemDTO.setKitchenState(1);
        kitchenItemDTO.setOrderDesc("Order Desc");
        kitchenItemDTO.setOrderGuid("1234");
        kitchenItemDTO.setOrderItemGuid("1234");
        kitchenItemDTO.setOrderNumber("42");
        kitchenItemDTO.setOrderRemark("Order Remark");
        kitchenItemDTO.setOrderSerialNo("Order Serial No");
        kitchenItemDTO.setPointGuid("1234");
        kitchenItemDTO.setPrdDeviceId("42");
        kitchenItemDTO.setPrepareTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDTO.setReturnCount(new BigDecimal("2.3"));
        kitchenItemDTO.setSkuCode("Sku Code");
        kitchenItemDTO.setSkuGuid("1234");
        kitchenItemDTO.setSkuName("Sku Name");
        kitchenItemDTO.setSkuUnit("Sku Unit");
        kitchenItemDTO.setStoreGuid("1234");
        kitchenItemDTO.setTableGuid("1234");
        kitchenItemDTO.setTimeout(10);
        kitchenItemDTO.setUrgedTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDTO.setUrgedTimes(1);
        kitchenItemServiceImpl.prdPrintAgain(kitchenItemDTO);
        verify(kitchenItemMapstruct).kitchenToPrdDstItem(Mockito.<KitchenItemDTO>any());
        verify(kdsPrintRecordService).printSingleItem(Mockito.<String>any(), Mockito.<PrdDstItemDTO>any(),
                Mockito.<KdsInvoiceTypeEnum>any());
    }

    /**
     * Method under test:
     * {@link KitchenItemServiceImpl#prdPrintAgain(KitchenItemDTO)}
     */
    @Test
    public void testPrdPrintAgain2() {
        doThrow(new IllegalArgumentException("foo")).when(kdsPrintRecordService)
                .printSingleItem(Mockito.<String>any(), Mockito.<PrdDstItemDTO>any(), Mockito.<KdsInvoiceTypeEnum>any());
        when(kitchenItemMapstruct.kitchenToPrdDstItem(Mockito.<KitchenItemDTO>any())).thenReturn(new PrdDstItemDTO());

        KitchenItemDTO kitchenItemDTO = new KitchenItemDTO();
        kitchenItemDTO.setAreaGuid("1234");
        kitchenItemDTO.setCallUpTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDTO.setCancelDstStaffGuid("1234");
        kitchenItemDTO.setCancelDstStaffName("Cancel Dst Staff Name");
        kitchenItemDTO.setCancelDstTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDTO.setCompleteStaffGuid("1234");
        kitchenItemDTO.setCompleteStaffName("Complete Staff Name");
        kitchenItemDTO.setCompleteTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDTO.setCookStaffGuid("1234");
        kitchenItemDTO.setCookStaffName("Cook Staff Name");
        kitchenItemDTO.setCookTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDTO.setCurrentCount(new BigDecimal("2.3"));
        kitchenItemDTO.setDisplayType(1);
        kitchenItemDTO.setDistributeStaffGuid("1234");
        kitchenItemDTO.setDistributeStaffName("Distribute Staff Name");
        kitchenItemDTO.setDistributeTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDTO.setDstDeviceId("42");
        kitchenItemDTO.setGuid("1234");
        kitchenItemDTO.setHangUpTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDTO.setIsWeight(true);
        kitchenItemDTO.setItemAttrMd5("27c7cf400229103e00c6d8830029e29b");
        kitchenItemDTO.setItemGuid("1234");
        kitchenItemDTO.setItemName("Item Name");
        kitchenItemDTO.setItemRemark("Item Remark");
        kitchenItemDTO.setItemState(42);
        kitchenItemDTO.setKitchenState(1);
        kitchenItemDTO.setOrderDesc("Order Desc");
        kitchenItemDTO.setOrderGuid("1234");
        kitchenItemDTO.setOrderItemGuid("1234");
        kitchenItemDTO.setOrderNumber("42");
        kitchenItemDTO.setOrderRemark("Order Remark");
        kitchenItemDTO.setOrderSerialNo("Order Serial No");
        kitchenItemDTO.setPointGuid("1234");
        kitchenItemDTO.setPrdDeviceId("42");
        kitchenItemDTO.setPrepareTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDTO.setReturnCount(new BigDecimal("2.3"));
        kitchenItemDTO.setSkuCode("Sku Code");
        kitchenItemDTO.setSkuGuid("1234");
        kitchenItemDTO.setSkuName("Sku Name");
        kitchenItemDTO.setSkuUnit("Sku Unit");
        kitchenItemDTO.setStoreGuid("1234");
        kitchenItemDTO.setTableGuid("1234");
        kitchenItemDTO.setTimeout(10);
        kitchenItemDTO.setUrgedTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDTO.setUrgedTimes(1);
        thrown.expect(IllegalArgumentException.class);
        kitchenItemServiceImpl.prdPrintAgain(kitchenItemDTO);
        verify(kitchenItemMapstruct).kitchenToPrdDstItem(Mockito.<KitchenItemDTO>any());
        verify(kdsPrintRecordService).printSingleItem(Mockito.<String>any(), Mockito.<PrdDstItemDTO>any(),
                Mockito.<KdsInvoiceTypeEnum>any());
    }

    /**
     * Method under test:
     * {@link KitchenItemServiceImpl#prdPrintAgain(KitchenItemDTO)}
     */
    @Test
    public void testPrdPrintAgain3() {
        doNothing().when(kdsPrintRecordService)
                .printSingleItem(Mockito.<String>any(), Mockito.<PrdDstItemDTO>any(), Mockito.<KdsInvoiceTypeEnum>any());
        when(kitchenItemMapstruct.kitchenToPrdDstItem(Mockito.<KitchenItemDTO>any())).thenReturn(new PrdDstItemDTO());
        KitchenItemDTO kitchenItemDTO = mock(KitchenItemDTO.class);
        when(kitchenItemDTO.getIsWeight()).thenReturn(false);
        when(kitchenItemDTO.getDisplayType()).thenReturn(1);
        when(kitchenItemDTO.getItemState()).thenReturn(42);
        when(kitchenItemDTO.getAreaGuid()).thenReturn("1234");
        when(kitchenItemDTO.getGuid()).thenReturn("1234");
        when(kitchenItemDTO.getOrderDesc()).thenReturn("Order Desc");
        when(kitchenItemDTO.getOrderGuid()).thenReturn("1234");
        when(kitchenItemDTO.getOrderNumber()).thenReturn("42");
        when(kitchenItemDTO.getOrderSerialNo()).thenReturn("Order Serial No");
        when(kitchenItemDTO.getPrdDeviceId()).thenReturn("42");
        when(kitchenItemDTO.getUrgedTime()).thenReturn(LocalDate.of(1970, 1, 1).atStartOfDay());
        doNothing().when(kitchenItemDTO).setAreaGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDTO).setCallUpTime(Mockito.<LocalDateTime>any());
        doNothing().when(kitchenItemDTO).setCancelDstStaffGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDTO).setCancelDstStaffName(Mockito.<String>any());
        doNothing().when(kitchenItemDTO).setCancelDstTime(Mockito.<LocalDateTime>any());
        doNothing().when(kitchenItemDTO).setCompleteStaffGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDTO).setCompleteStaffName(Mockito.<String>any());
        doNothing().when(kitchenItemDTO).setCompleteTime(Mockito.<LocalDateTime>any());
        doNothing().when(kitchenItemDTO).setCookStaffGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDTO).setCookStaffName(Mockito.<String>any());
        doNothing().when(kitchenItemDTO).setCookTime(Mockito.<LocalDateTime>any());
        doNothing().when(kitchenItemDTO).setCurrentCount(Mockito.<BigDecimal>any());
        doNothing().when(kitchenItemDTO).setDisplayType(Mockito.<Integer>any());
        doNothing().when(kitchenItemDTO).setDistributeStaffGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDTO).setDistributeStaffName(Mockito.<String>any());
        doNothing().when(kitchenItemDTO).setDistributeTime(Mockito.<LocalDateTime>any());
        doNothing().when(kitchenItemDTO).setDstDeviceId(Mockito.<String>any());
        doNothing().when(kitchenItemDTO).setGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDTO).setHangUpTime(Mockito.<LocalDateTime>any());
        doNothing().when(kitchenItemDTO).setIsWeight(Mockito.<Boolean>any());
        doNothing().when(kitchenItemDTO).setItemAttrMd5(Mockito.<String>any());
        doNothing().when(kitchenItemDTO).setItemGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDTO).setItemName(Mockito.<String>any());
        doNothing().when(kitchenItemDTO).setItemRemark(Mockito.<String>any());
        doNothing().when(kitchenItemDTO).setItemState(Mockito.<Integer>any());
        doNothing().when(kitchenItemDTO).setKitchenState(Mockito.<Integer>any());
        doNothing().when(kitchenItemDTO).setOrderDesc(Mockito.<String>any());
        doNothing().when(kitchenItemDTO).setOrderGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDTO).setOrderItemGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDTO).setOrderNumber(Mockito.<String>any());
        doNothing().when(kitchenItemDTO).setOrderRemark(Mockito.<String>any());
        doNothing().when(kitchenItemDTO).setOrderSerialNo(Mockito.<String>any());
        doNothing().when(kitchenItemDTO).setPointGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDTO).setPrdDeviceId(Mockito.<String>any());
        doNothing().when(kitchenItemDTO).setPrepareTime(Mockito.<LocalDateTime>any());
        doNothing().when(kitchenItemDTO).setReturnCount(Mockito.<BigDecimal>any());
        doNothing().when(kitchenItemDTO).setSkuCode(Mockito.<String>any());
        doNothing().when(kitchenItemDTO).setSkuGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDTO).setSkuName(Mockito.<String>any());
        doNothing().when(kitchenItemDTO).setSkuUnit(Mockito.<String>any());
        doNothing().when(kitchenItemDTO).setStoreGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDTO).setTableGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDTO).setTimeout(Mockito.<Integer>any());
        doNothing().when(kitchenItemDTO).setUrgedTime(Mockito.<LocalDateTime>any());
        doNothing().when(kitchenItemDTO).setUrgedTimes(Mockito.<Integer>any());
        kitchenItemDTO.setAreaGuid("1234");
        kitchenItemDTO.setCallUpTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDTO.setCancelDstStaffGuid("1234");
        kitchenItemDTO.setCancelDstStaffName("Cancel Dst Staff Name");
        kitchenItemDTO.setCancelDstTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDTO.setCompleteStaffGuid("1234");
        kitchenItemDTO.setCompleteStaffName("Complete Staff Name");
        kitchenItemDTO.setCompleteTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDTO.setCookStaffGuid("1234");
        kitchenItemDTO.setCookStaffName("Cook Staff Name");
        kitchenItemDTO.setCookTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDTO.setCurrentCount(new BigDecimal("2.3"));
        kitchenItemDTO.setDisplayType(1);
        kitchenItemDTO.setDistributeStaffGuid("1234");
        kitchenItemDTO.setDistributeStaffName("Distribute Staff Name");
        kitchenItemDTO.setDistributeTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDTO.setDstDeviceId("42");
        kitchenItemDTO.setGuid("1234");
        kitchenItemDTO.setHangUpTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDTO.setIsWeight(true);
        kitchenItemDTO.setItemAttrMd5("27c7cf400229103e00c6d8830029e29b");
        kitchenItemDTO.setItemGuid("1234");
        kitchenItemDTO.setItemName("Item Name");
        kitchenItemDTO.setItemRemark("Item Remark");
        kitchenItemDTO.setItemState(42);
        kitchenItemDTO.setKitchenState(1);
        kitchenItemDTO.setOrderDesc("Order Desc");
        kitchenItemDTO.setOrderGuid("1234");
        kitchenItemDTO.setOrderItemGuid("1234");
        kitchenItemDTO.setOrderNumber("42");
        kitchenItemDTO.setOrderRemark("Order Remark");
        kitchenItemDTO.setOrderSerialNo("Order Serial No");
        kitchenItemDTO.setPointGuid("1234");
        kitchenItemDTO.setPrdDeviceId("42");
        kitchenItemDTO.setPrepareTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDTO.setReturnCount(new BigDecimal("2.3"));
        kitchenItemDTO.setSkuCode("Sku Code");
        kitchenItemDTO.setSkuGuid("1234");
        kitchenItemDTO.setSkuName("Sku Name");
        kitchenItemDTO.setSkuUnit("Sku Unit");
        kitchenItemDTO.setStoreGuid("1234");
        kitchenItemDTO.setTableGuid("1234");
        kitchenItemDTO.setTimeout(10);
        kitchenItemDTO.setUrgedTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDTO.setUrgedTimes(1);
        kitchenItemServiceImpl.prdPrintAgain(kitchenItemDTO);
        verify(kitchenItemDTO).getAreaGuid();
        verify(kitchenItemDTO).getDisplayType();
        verify(kitchenItemDTO).getGuid();
        verify(kitchenItemDTO).getIsWeight();
        verify(kitchenItemDTO).getItemState();
        verify(kitchenItemDTO).getOrderDesc();
        verify(kitchenItemDTO).getOrderGuid();
        verify(kitchenItemDTO).getOrderNumber();
        verify(kitchenItemDTO).getOrderSerialNo();
        verify(kitchenItemDTO).getPrdDeviceId();
        verify(kitchenItemDTO).getUrgedTime();
        verify(kitchenItemDTO).setAreaGuid(Mockito.<String>any());
        verify(kitchenItemDTO).setCallUpTime(Mockito.<LocalDateTime>any());
        verify(kitchenItemDTO).setCancelDstStaffGuid(Mockito.<String>any());
        verify(kitchenItemDTO).setCancelDstStaffName(Mockito.<String>any());
        verify(kitchenItemDTO).setCancelDstTime(Mockito.<LocalDateTime>any());
        verify(kitchenItemDTO).setCompleteStaffGuid(Mockito.<String>any());
        verify(kitchenItemDTO).setCompleteStaffName(Mockito.<String>any());
        verify(kitchenItemDTO).setCompleteTime(Mockito.<LocalDateTime>any());
        verify(kitchenItemDTO).setCookStaffGuid(Mockito.<String>any());
        verify(kitchenItemDTO).setCookStaffName(Mockito.<String>any());
        verify(kitchenItemDTO).setCookTime(Mockito.<LocalDateTime>any());
        verify(kitchenItemDTO).setCurrentCount(Mockito.<BigDecimal>any());
        verify(kitchenItemDTO).setDisplayType(Mockito.<Integer>any());
        verify(kitchenItemDTO).setDistributeStaffGuid(Mockito.<String>any());
        verify(kitchenItemDTO).setDistributeStaffName(Mockito.<String>any());
        verify(kitchenItemDTO).setDistributeTime(Mockito.<LocalDateTime>any());
        verify(kitchenItemDTO).setDstDeviceId(Mockito.<String>any());
        verify(kitchenItemDTO).setGuid(Mockito.<String>any());
        verify(kitchenItemDTO).setHangUpTime(Mockito.<LocalDateTime>any());
        verify(kitchenItemDTO).setIsWeight(Mockito.<Boolean>any());
        verify(kitchenItemDTO).setItemAttrMd5(Mockito.<String>any());
        verify(kitchenItemDTO).setItemGuid(Mockito.<String>any());
        verify(kitchenItemDTO).setItemName(Mockito.<String>any());
        verify(kitchenItemDTO).setItemRemark(Mockito.<String>any());
        verify(kitchenItemDTO).setItemState(Mockito.<Integer>any());
        verify(kitchenItemDTO).setKitchenState(Mockito.<Integer>any());
        verify(kitchenItemDTO).setOrderDesc(Mockito.<String>any());
        verify(kitchenItemDTO).setOrderGuid(Mockito.<String>any());
        verify(kitchenItemDTO).setOrderItemGuid(Mockito.<String>any());
        verify(kitchenItemDTO).setOrderNumber(Mockito.<String>any());
        verify(kitchenItemDTO).setOrderRemark(Mockito.<String>any());
        verify(kitchenItemDTO).setOrderSerialNo(Mockito.<String>any());
        verify(kitchenItemDTO).setPointGuid(Mockito.<String>any());
        verify(kitchenItemDTO).setPrdDeviceId(Mockito.<String>any());
        verify(kitchenItemDTO).setPrepareTime(Mockito.<LocalDateTime>any());
        verify(kitchenItemDTO).setReturnCount(Mockito.<BigDecimal>any());
        verify(kitchenItemDTO).setSkuCode(Mockito.<String>any());
        verify(kitchenItemDTO).setSkuGuid(Mockito.<String>any());
        verify(kitchenItemDTO).setSkuName(Mockito.<String>any());
        verify(kitchenItemDTO).setSkuUnit(Mockito.<String>any());
        verify(kitchenItemDTO).setStoreGuid(Mockito.<String>any());
        verify(kitchenItemDTO).setTableGuid(Mockito.<String>any());
        verify(kitchenItemDTO).setTimeout(Mockito.<Integer>any());
        verify(kitchenItemDTO).setUrgedTime(Mockito.<LocalDateTime>any());
        verify(kitchenItemDTO).setUrgedTimes(Mockito.<Integer>any());
        verify(kitchenItemMapstruct).kitchenToPrdDstItem(Mockito.<KitchenItemDTO>any());
        verify(kdsPrintRecordService).printSingleItem(Mockito.<String>any(), Mockito.<PrdDstItemDTO>any(),
                Mockito.<KdsInvoiceTypeEnum>any());
    }

    /**
     * Method under test:
     * {@link KitchenItemServiceImpl#dstPrintAgain(KitchenItemDTO)}
     */
    @Test
    public void testDstPrintAgain() {
        doNothing().when(kdsPrintRecordService)
                .printSingleItem(Mockito.<String>any(), Mockito.<PrdDstItemDTO>any(), Mockito.<KdsInvoiceTypeEnum>any());
        when(kitchenItemMapstruct.kitchenToPrdDstItem(Mockito.<KitchenItemDTO>any())).thenReturn(new PrdDstItemDTO());

        KitchenItemDTO kitchenItemDTO = new KitchenItemDTO();
        kitchenItemDTO.setAreaGuid("1234");
        kitchenItemDTO.setCallUpTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDTO.setCancelDstStaffGuid("1234");
        kitchenItemDTO.setCancelDstStaffName("Cancel Dst Staff Name");
        kitchenItemDTO.setCancelDstTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDTO.setCompleteStaffGuid("1234");
        kitchenItemDTO.setCompleteStaffName("Complete Staff Name");
        kitchenItemDTO.setCompleteTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDTO.setCookStaffGuid("1234");
        kitchenItemDTO.setCookStaffName("Cook Staff Name");
        kitchenItemDTO.setCookTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDTO.setCurrentCount(new BigDecimal("2.3"));
        kitchenItemDTO.setDisplayType(1);
        kitchenItemDTO.setDistributeStaffGuid("1234");
        kitchenItemDTO.setDistributeStaffName("Distribute Staff Name");
        kitchenItemDTO.setDistributeTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDTO.setDstDeviceId("42");
        kitchenItemDTO.setGuid("1234");
        kitchenItemDTO.setHangUpTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDTO.setIsWeight(true);
        kitchenItemDTO.setItemAttrMd5("27c7cf400229103e00c6d8830029e29b");
        kitchenItemDTO.setItemGuid("1234");
        kitchenItemDTO.setItemName("Item Name");
        kitchenItemDTO.setItemRemark("Item Remark");
        kitchenItemDTO.setItemState(42);
        kitchenItemDTO.setKitchenState(1);
        kitchenItemDTO.setOrderDesc("Order Desc");
        kitchenItemDTO.setOrderGuid("1234");
        kitchenItemDTO.setOrderItemGuid("1234");
        kitchenItemDTO.setOrderNumber("42");
        kitchenItemDTO.setOrderRemark("Order Remark");
        kitchenItemDTO.setOrderSerialNo("Order Serial No");
        kitchenItemDTO.setPointGuid("1234");
        kitchenItemDTO.setPrdDeviceId("42");
        kitchenItemDTO.setPrepareTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDTO.setReturnCount(new BigDecimal("2.3"));
        kitchenItemDTO.setSkuCode("Sku Code");
        kitchenItemDTO.setSkuGuid("1234");
        kitchenItemDTO.setSkuName("Sku Name");
        kitchenItemDTO.setSkuUnit("Sku Unit");
        kitchenItemDTO.setStoreGuid("1234");
        kitchenItemDTO.setTableGuid("1234");
        kitchenItemDTO.setTimeout(10);
        kitchenItemDTO.setUrgedTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDTO.setUrgedTimes(1);
        kitchenItemServiceImpl.dstPrintAgain(kitchenItemDTO);
        verify(kitchenItemMapstruct).kitchenToPrdDstItem(Mockito.<KitchenItemDTO>any());
        verify(kdsPrintRecordService).printSingleItem(Mockito.<String>any(), Mockito.<PrdDstItemDTO>any(),
                Mockito.<KdsInvoiceTypeEnum>any());
    }

    /**
     * Method under test:
     * {@link KitchenItemServiceImpl#dstPrintAgain(KitchenItemDTO)}
     */
    @Test
    public void testDstPrintAgain2() {
        doThrow(new IllegalArgumentException("foo")).when(kdsPrintRecordService)
                .printSingleItem(Mockito.<String>any(), Mockito.<PrdDstItemDTO>any(), Mockito.<KdsInvoiceTypeEnum>any());
        when(kitchenItemMapstruct.kitchenToPrdDstItem(Mockito.<KitchenItemDTO>any())).thenReturn(new PrdDstItemDTO());

        KitchenItemDTO kitchenItemDTO = new KitchenItemDTO();
        kitchenItemDTO.setAreaGuid("1234");
        kitchenItemDTO.setCallUpTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDTO.setCancelDstStaffGuid("1234");
        kitchenItemDTO.setCancelDstStaffName("Cancel Dst Staff Name");
        kitchenItemDTO.setCancelDstTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDTO.setCompleteStaffGuid("1234");
        kitchenItemDTO.setCompleteStaffName("Complete Staff Name");
        kitchenItemDTO.setCompleteTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDTO.setCookStaffGuid("1234");
        kitchenItemDTO.setCookStaffName("Cook Staff Name");
        kitchenItemDTO.setCookTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDTO.setCurrentCount(new BigDecimal("2.3"));
        kitchenItemDTO.setDisplayType(1);
        kitchenItemDTO.setDistributeStaffGuid("1234");
        kitchenItemDTO.setDistributeStaffName("Distribute Staff Name");
        kitchenItemDTO.setDistributeTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDTO.setDstDeviceId("42");
        kitchenItemDTO.setGuid("1234");
        kitchenItemDTO.setHangUpTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDTO.setIsWeight(true);
        kitchenItemDTO.setItemAttrMd5("27c7cf400229103e00c6d8830029e29b");
        kitchenItemDTO.setItemGuid("1234");
        kitchenItemDTO.setItemName("Item Name");
        kitchenItemDTO.setItemRemark("Item Remark");
        kitchenItemDTO.setItemState(42);
        kitchenItemDTO.setKitchenState(1);
        kitchenItemDTO.setOrderDesc("Order Desc");
        kitchenItemDTO.setOrderGuid("1234");
        kitchenItemDTO.setOrderItemGuid("1234");
        kitchenItemDTO.setOrderNumber("42");
        kitchenItemDTO.setOrderRemark("Order Remark");
        kitchenItemDTO.setOrderSerialNo("Order Serial No");
        kitchenItemDTO.setPointGuid("1234");
        kitchenItemDTO.setPrdDeviceId("42");
        kitchenItemDTO.setPrepareTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDTO.setReturnCount(new BigDecimal("2.3"));
        kitchenItemDTO.setSkuCode("Sku Code");
        kitchenItemDTO.setSkuGuid("1234");
        kitchenItemDTO.setSkuName("Sku Name");
        kitchenItemDTO.setSkuUnit("Sku Unit");
        kitchenItemDTO.setStoreGuid("1234");
        kitchenItemDTO.setTableGuid("1234");
        kitchenItemDTO.setTimeout(10);
        kitchenItemDTO.setUrgedTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDTO.setUrgedTimes(1);
        thrown.expect(IllegalArgumentException.class);
        kitchenItemServiceImpl.dstPrintAgain(kitchenItemDTO);
        verify(kitchenItemMapstruct).kitchenToPrdDstItem(Mockito.<KitchenItemDTO>any());
        verify(kdsPrintRecordService).printSingleItem(Mockito.<String>any(), Mockito.<PrdDstItemDTO>any(),
                Mockito.<KdsInvoiceTypeEnum>any());
    }

    /**
     * Method under test:
     * {@link KitchenItemServiceImpl#dstPrintAgain(KitchenItemDTO)}
     */
    @Test
    public void testDstPrintAgain3() {
        doNothing().when(kdsPrintRecordService)
                .printSingleItem(Mockito.<String>any(), Mockito.<PrdDstItemDTO>any(), Mockito.<KdsInvoiceTypeEnum>any());
        when(kitchenItemMapstruct.kitchenToPrdDstItem(Mockito.<KitchenItemDTO>any())).thenReturn(new PrdDstItemDTO());
        KitchenItemDTO kitchenItemDTO = mock(KitchenItemDTO.class);
        when(kitchenItemDTO.getIsWeight()).thenReturn(false);
        when(kitchenItemDTO.getDisplayType()).thenReturn(1);
        when(kitchenItemDTO.getItemState()).thenReturn(42);
        when(kitchenItemDTO.getAreaGuid()).thenReturn("1234");
        when(kitchenItemDTO.getDstDeviceId()).thenReturn("42");
        when(kitchenItemDTO.getGuid()).thenReturn("1234");
        when(kitchenItemDTO.getOrderDesc()).thenReturn("Order Desc");
        when(kitchenItemDTO.getOrderGuid()).thenReturn("1234");
        when(kitchenItemDTO.getOrderNumber()).thenReturn("42");
        when(kitchenItemDTO.getOrderSerialNo()).thenReturn("Order Serial No");
        when(kitchenItemDTO.getUrgedTime()).thenReturn(LocalDate.of(1970, 1, 1).atStartOfDay());
        doNothing().when(kitchenItemDTO).setAreaGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDTO).setCallUpTime(Mockito.<LocalDateTime>any());
        doNothing().when(kitchenItemDTO).setCancelDstStaffGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDTO).setCancelDstStaffName(Mockito.<String>any());
        doNothing().when(kitchenItemDTO).setCancelDstTime(Mockito.<LocalDateTime>any());
        doNothing().when(kitchenItemDTO).setCompleteStaffGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDTO).setCompleteStaffName(Mockito.<String>any());
        doNothing().when(kitchenItemDTO).setCompleteTime(Mockito.<LocalDateTime>any());
        doNothing().when(kitchenItemDTO).setCookStaffGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDTO).setCookStaffName(Mockito.<String>any());
        doNothing().when(kitchenItemDTO).setCookTime(Mockito.<LocalDateTime>any());
        doNothing().when(kitchenItemDTO).setCurrentCount(Mockito.<BigDecimal>any());
        doNothing().when(kitchenItemDTO).setDisplayType(Mockito.<Integer>any());
        doNothing().when(kitchenItemDTO).setDistributeStaffGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDTO).setDistributeStaffName(Mockito.<String>any());
        doNothing().when(kitchenItemDTO).setDistributeTime(Mockito.<LocalDateTime>any());
        doNothing().when(kitchenItemDTO).setDstDeviceId(Mockito.<String>any());
        doNothing().when(kitchenItemDTO).setGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDTO).setHangUpTime(Mockito.<LocalDateTime>any());
        doNothing().when(kitchenItemDTO).setIsWeight(Mockito.<Boolean>any());
        doNothing().when(kitchenItemDTO).setItemAttrMd5(Mockito.<String>any());
        doNothing().when(kitchenItemDTO).setItemGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDTO).setItemName(Mockito.<String>any());
        doNothing().when(kitchenItemDTO).setItemRemark(Mockito.<String>any());
        doNothing().when(kitchenItemDTO).setItemState(Mockito.<Integer>any());
        doNothing().when(kitchenItemDTO).setKitchenState(Mockito.<Integer>any());
        doNothing().when(kitchenItemDTO).setOrderDesc(Mockito.<String>any());
        doNothing().when(kitchenItemDTO).setOrderGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDTO).setOrderItemGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDTO).setOrderNumber(Mockito.<String>any());
        doNothing().when(kitchenItemDTO).setOrderRemark(Mockito.<String>any());
        doNothing().when(kitchenItemDTO).setOrderSerialNo(Mockito.<String>any());
        doNothing().when(kitchenItemDTO).setPointGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDTO).setPrdDeviceId(Mockito.<String>any());
        doNothing().when(kitchenItemDTO).setPrepareTime(Mockito.<LocalDateTime>any());
        doNothing().when(kitchenItemDTO).setReturnCount(Mockito.<BigDecimal>any());
        doNothing().when(kitchenItemDTO).setSkuCode(Mockito.<String>any());
        doNothing().when(kitchenItemDTO).setSkuGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDTO).setSkuName(Mockito.<String>any());
        doNothing().when(kitchenItemDTO).setSkuUnit(Mockito.<String>any());
        doNothing().when(kitchenItemDTO).setStoreGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDTO).setTableGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDTO).setTimeout(Mockito.<Integer>any());
        doNothing().when(kitchenItemDTO).setUrgedTime(Mockito.<LocalDateTime>any());
        doNothing().when(kitchenItemDTO).setUrgedTimes(Mockito.<Integer>any());
        kitchenItemDTO.setAreaGuid("1234");
        kitchenItemDTO.setCallUpTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDTO.setCancelDstStaffGuid("1234");
        kitchenItemDTO.setCancelDstStaffName("Cancel Dst Staff Name");
        kitchenItemDTO.setCancelDstTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDTO.setCompleteStaffGuid("1234");
        kitchenItemDTO.setCompleteStaffName("Complete Staff Name");
        kitchenItemDTO.setCompleteTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDTO.setCookStaffGuid("1234");
        kitchenItemDTO.setCookStaffName("Cook Staff Name");
        kitchenItemDTO.setCookTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDTO.setCurrentCount(new BigDecimal("2.3"));
        kitchenItemDTO.setDisplayType(1);
        kitchenItemDTO.setDistributeStaffGuid("1234");
        kitchenItemDTO.setDistributeStaffName("Distribute Staff Name");
        kitchenItemDTO.setDistributeTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDTO.setDstDeviceId("42");
        kitchenItemDTO.setGuid("1234");
        kitchenItemDTO.setHangUpTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDTO.setIsWeight(true);
        kitchenItemDTO.setItemAttrMd5("27c7cf400229103e00c6d8830029e29b");
        kitchenItemDTO.setItemGuid("1234");
        kitchenItemDTO.setItemName("Item Name");
        kitchenItemDTO.setItemRemark("Item Remark");
        kitchenItemDTO.setItemState(42);
        kitchenItemDTO.setKitchenState(1);
        kitchenItemDTO.setOrderDesc("Order Desc");
        kitchenItemDTO.setOrderGuid("1234");
        kitchenItemDTO.setOrderItemGuid("1234");
        kitchenItemDTO.setOrderNumber("42");
        kitchenItemDTO.setOrderRemark("Order Remark");
        kitchenItemDTO.setOrderSerialNo("Order Serial No");
        kitchenItemDTO.setPointGuid("1234");
        kitchenItemDTO.setPrdDeviceId("42");
        kitchenItemDTO.setPrepareTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDTO.setReturnCount(new BigDecimal("2.3"));
        kitchenItemDTO.setSkuCode("Sku Code");
        kitchenItemDTO.setSkuGuid("1234");
        kitchenItemDTO.setSkuName("Sku Name");
        kitchenItemDTO.setSkuUnit("Sku Unit");
        kitchenItemDTO.setStoreGuid("1234");
        kitchenItemDTO.setTableGuid("1234");
        kitchenItemDTO.setTimeout(10);
        kitchenItemDTO.setUrgedTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDTO.setUrgedTimes(1);
        kitchenItemServiceImpl.dstPrintAgain(kitchenItemDTO);
        verify(kitchenItemDTO).getAreaGuid();
        verify(kitchenItemDTO).getDisplayType();
        verify(kitchenItemDTO).getDstDeviceId();
        verify(kitchenItemDTO).getGuid();
        verify(kitchenItemDTO).getIsWeight();
        verify(kitchenItemDTO).getItemState();
        verify(kitchenItemDTO).getOrderDesc();
        verify(kitchenItemDTO).getOrderGuid();
        verify(kitchenItemDTO).getOrderNumber();
        verify(kitchenItemDTO).getOrderSerialNo();
        verify(kitchenItemDTO).getUrgedTime();
        verify(kitchenItemDTO).setAreaGuid(Mockito.<String>any());
        verify(kitchenItemDTO).setCallUpTime(Mockito.<LocalDateTime>any());
        verify(kitchenItemDTO).setCancelDstStaffGuid(Mockito.<String>any());
        verify(kitchenItemDTO).setCancelDstStaffName(Mockito.<String>any());
        verify(kitchenItemDTO).setCancelDstTime(Mockito.<LocalDateTime>any());
        verify(kitchenItemDTO).setCompleteStaffGuid(Mockito.<String>any());
        verify(kitchenItemDTO).setCompleteStaffName(Mockito.<String>any());
        verify(kitchenItemDTO).setCompleteTime(Mockito.<LocalDateTime>any());
        verify(kitchenItemDTO).setCookStaffGuid(Mockito.<String>any());
        verify(kitchenItemDTO).setCookStaffName(Mockito.<String>any());
        verify(kitchenItemDTO).setCookTime(Mockito.<LocalDateTime>any());
        verify(kitchenItemDTO).setCurrentCount(Mockito.<BigDecimal>any());
        verify(kitchenItemDTO).setDisplayType(Mockito.<Integer>any());
        verify(kitchenItemDTO).setDistributeStaffGuid(Mockito.<String>any());
        verify(kitchenItemDTO).setDistributeStaffName(Mockito.<String>any());
        verify(kitchenItemDTO).setDistributeTime(Mockito.<LocalDateTime>any());
        verify(kitchenItemDTO).setDstDeviceId(Mockito.<String>any());
        verify(kitchenItemDTO).setGuid(Mockito.<String>any());
        verify(kitchenItemDTO).setHangUpTime(Mockito.<LocalDateTime>any());
        verify(kitchenItemDTO).setIsWeight(Mockito.<Boolean>any());
        verify(kitchenItemDTO).setItemAttrMd5(Mockito.<String>any());
        verify(kitchenItemDTO).setItemGuid(Mockito.<String>any());
        verify(kitchenItemDTO).setItemName(Mockito.<String>any());
        verify(kitchenItemDTO).setItemRemark(Mockito.<String>any());
        verify(kitchenItemDTO).setItemState(Mockito.<Integer>any());
        verify(kitchenItemDTO).setKitchenState(Mockito.<Integer>any());
        verify(kitchenItemDTO).setOrderDesc(Mockito.<String>any());
        verify(kitchenItemDTO).setOrderGuid(Mockito.<String>any());
        verify(kitchenItemDTO).setOrderItemGuid(Mockito.<String>any());
        verify(kitchenItemDTO).setOrderNumber(Mockito.<String>any());
        verify(kitchenItemDTO).setOrderRemark(Mockito.<String>any());
        verify(kitchenItemDTO).setOrderSerialNo(Mockito.<String>any());
        verify(kitchenItemDTO).setPointGuid(Mockito.<String>any());
        verify(kitchenItemDTO).setPrdDeviceId(Mockito.<String>any());
        verify(kitchenItemDTO).setPrepareTime(Mockito.<LocalDateTime>any());
        verify(kitchenItemDTO).setReturnCount(Mockito.<BigDecimal>any());
        verify(kitchenItemDTO).setSkuCode(Mockito.<String>any());
        verify(kitchenItemDTO).setSkuGuid(Mockito.<String>any());
        verify(kitchenItemDTO).setSkuName(Mockito.<String>any());
        verify(kitchenItemDTO).setSkuUnit(Mockito.<String>any());
        verify(kitchenItemDTO).setStoreGuid(Mockito.<String>any());
        verify(kitchenItemDTO).setTableGuid(Mockito.<String>any());
        verify(kitchenItemDTO).setTimeout(Mockito.<Integer>any());
        verify(kitchenItemDTO).setUrgedTime(Mockito.<LocalDateTime>any());
        verify(kitchenItemDTO).setUrgedTimes(Mockito.<Integer>any());
        verify(kitchenItemMapstruct).kitchenToPrdDstItem(Mockito.<KitchenItemDTO>any());
        verify(kdsPrintRecordService).printSingleItem(Mockito.<String>any(), Mockito.<PrdDstItemDTO>any(),
                Mockito.<KdsInvoiceTypeEnum>any());
    }

    /**
     * Method under test:
     * {@link KitchenItemServiceImpl#autoPrint(String, String, List, List)}
     */
    @Test
    public void testAutoPrint() {
        when(deviceConfigService.listPrdDeviceByGuid(Mockito.<String>any(), Mockito.<List<String>>any()))
                .thenReturn(new HashMap<>());
        ArrayList<String> kitchenItemGuidList = new ArrayList<>();
        kitchenItemServiceImpl.autoPrint("1234", "1234", kitchenItemGuidList, new ArrayList<>());
        verify(deviceConfigService).listPrdDeviceByGuid(Mockito.<String>any(), Mockito.<List<String>>any());
    }

    /**
     * Method under test:
     * {@link KitchenItemServiceImpl#autoPrint(String, String, List, List)}
     */
    @Test
    public void testAutoPrint2() {
        HashMap<String, DeviceConfigDO> stringDeviceConfigDOMap = new HashMap<>();
        stringDeviceConfigDOMap.put("foo", DeviceConfigDO.defaultConfig());
        when(deviceConfigService.listPrdDeviceByGuid(Mockito.<String>any(), Mockito.<List<String>>any()))
                .thenReturn(stringDeviceConfigDOMap);
        ArrayList<String> kitchenItemGuidList = new ArrayList<>();
        kitchenItemServiceImpl.autoPrint("1234", "1234", kitchenItemGuidList, new ArrayList<>());
        verify(deviceConfigService).listPrdDeviceByGuid(Mockito.<String>any(), Mockito.<List<String>>any());
    }

    /**
     * Method under test:
     * {@link KitchenItemServiceImpl#autoPrint(String, String, List, List)}
     */
    @Test
    public void testAutoPrint3() {
        HashMap<String, DeviceConfigDO> stringDeviceConfigDOMap = new HashMap<>();
        stringDeviceConfigDOMap.put("42", DeviceConfigDO.defaultConfig());
        stringDeviceConfigDOMap.put("foo", DeviceConfigDO.defaultConfig());
        when(deviceConfigService.listPrdDeviceByGuid(Mockito.<String>any(), Mockito.<List<String>>any()))
                .thenReturn(stringDeviceConfigDOMap);
        ArrayList<String> kitchenItemGuidList = new ArrayList<>();
        kitchenItemServiceImpl.autoPrint("1234", "1234", kitchenItemGuidList, new ArrayList<>());
        verify(deviceConfigService).listPrdDeviceByGuid(Mockito.<String>any(), Mockito.<List<String>>any());
    }

    /**
     * Method under test:
     * {@link KitchenItemServiceImpl#autoPrint(String, String, List, List)}
     */
    @Test
    public void testAutoPrint4() {
        when(deviceConfigService.listPrdDeviceByGuid(Mockito.<String>any(), Mockito.<List<String>>any()))
                .thenReturn(new HashMap<>());

        ArrayList<String> kitchenItemGuidList = new ArrayList<>();
        kitchenItemGuidList.add("foo");
        kitchenItemServiceImpl.autoPrint("1234", "1234", kitchenItemGuidList, new ArrayList<>());
        verify(deviceConfigService).listPrdDeviceByGuid(Mockito.<String>any(), Mockito.<List<String>>any());
    }

    /**
     * Method under test:
     * {@link KitchenItemServiceImpl#autoPrint(String, String, List, List)}
     */
    @Test
    public void testAutoPrint5() {
        when(deviceConfigService.listPrdDeviceByGuid(Mockito.<String>any(), Mockito.<List<String>>any()))
                .thenReturn(new HashMap<>());

        ArrayList<String> kitchenItemGuidList = new ArrayList<>();
        kitchenItemGuidList.add("42");
        kitchenItemGuidList.add("foo");
        kitchenItemServiceImpl.autoPrint("1234", "1234", kitchenItemGuidList, new ArrayList<>());
        verify(deviceConfigService).listPrdDeviceByGuid(Mockito.<String>any(), Mockito.<List<String>>any());
    }

    /**
     * Method under test:
     * {@link KitchenItemServiceImpl#autoPrint(String, String, List, List)}
     */
    @Test
    public void testAutoPrint6() {
        when(deviceConfigService.listPrdDeviceByGuid(Mockito.<String>any(), Mockito.<List<String>>any()))
                .thenReturn(new HashMap<>());
        ArrayList<String> kitchenItemGuidList = new ArrayList<>();

        ArrayList<String> deviceGuidList = new ArrayList<>();
        deviceGuidList.add("foo");
        kitchenItemServiceImpl.autoPrint("1234", "1234", kitchenItemGuidList, deviceGuidList);
        verify(deviceConfigService).listPrdDeviceByGuid(Mockito.<String>any(), Mockito.<List<String>>any());
    }

    /**
     * Method under test:
     * {@link KitchenItemServiceImpl#autoPrint(String, String, List, List)}
     */
    @Test
    public void testAutoPrint7() {
        when(deviceConfigService.listPrdDeviceByGuid(Mockito.<String>any(), Mockito.<List<String>>any()))
                .thenReturn(new HashMap<>());
        ArrayList<String> kitchenItemGuidList = new ArrayList<>();

        ArrayList<String> deviceGuidList = new ArrayList<>();
        deviceGuidList.add("42");
        deviceGuidList.add("foo");
        kitchenItemServiceImpl.autoPrint("1234", "1234", kitchenItemGuidList, deviceGuidList);
        verify(deviceConfigService).listPrdDeviceByGuid(Mockito.<String>any(), Mockito.<List<String>>any());
    }

    /**
     * Method under test:
     * {@link KitchenItemServiceImpl#autoPrint(String, String, List, List)}
     */
    @Test
    public void testAutoPrint8() {
        when(deviceConfigService.listPrdDeviceByGuid(Mockito.<String>any(), Mockito.<List<String>>any()))
                .thenThrow(new IllegalArgumentException("foo"));
        ArrayList<String> kitchenItemGuidList = new ArrayList<>();
        thrown.expect(IllegalArgumentException.class);
        kitchenItemServiceImpl.autoPrint("1234", "1234", kitchenItemGuidList, new ArrayList<>());
        verify(deviceConfigService).listPrdDeviceByGuid(Mockito.<String>any(), Mockito.<List<String>>any());
    }

    /**
     * Method under test:
     * {@link KitchenItemServiceImpl#autoPrint(String, String, List, List)}
     */
    @Test
    public void testAutoPrint9() {
        when(kitchenItemMapper.queryPrepareItem(Mockito.<ItemQuery>any())).thenReturn(new ArrayList<>());

        HashMap<String, DeviceConfigDO> stringDeviceConfigDOMap = new HashMap<>();
        stringDeviceConfigDOMap.put("foo", DeviceConfigDO.defaultConfig());
        when(deviceConfigService.listPrdDeviceByGuid(Mockito.<String>any(), Mockito.<List<String>>any()))
                .thenReturn(stringDeviceConfigDOMap);

        ArrayList<String> kitchenItemGuidList = new ArrayList<>();
        kitchenItemGuidList.add("");
        kitchenItemServiceImpl.autoPrint("1234", "1234", kitchenItemGuidList, new ArrayList<>());
        verify(kitchenItemMapper).queryPrepareItem(Mockito.<ItemQuery>any());
        verify(deviceConfigService).listPrdDeviceByGuid(Mockito.<String>any(), Mockito.<List<String>>any());
    }

    /**
     * Method under test:
     * {@link KitchenItemServiceImpl#autoPrint(String, String, List, List)}
     */
    @Test
    public void testAutoPrint10() {
        when(kitchenItemMapper.queryPrepareItem(Mockito.<ItemQuery>any())).thenReturn(new ArrayList<>());
        DeviceConfigDO defaultConfigResult = DeviceConfigDO.defaultConfig();
        defaultConfigResult.setDisplayMode(2);

        HashMap<String, DeviceConfigDO> stringDeviceConfigDOMap = new HashMap<>();
        stringDeviceConfigDOMap.put("foo", defaultConfigResult);
        when(deviceConfigService.listPrdDeviceByGuid(Mockito.<String>any(), Mockito.<List<String>>any()))
                .thenReturn(stringDeviceConfigDOMap);

        ArrayList<String> kitchenItemGuidList = new ArrayList<>();
        kitchenItemGuidList.add("");
        kitchenItemServiceImpl.autoPrint("1234", "1234", kitchenItemGuidList, new ArrayList<>());
        verify(kitchenItemMapper).queryPrepareItem(Mockito.<ItemQuery>any());
        verify(deviceConfigService).listPrdDeviceByGuid(Mockito.<String>any(), Mockito.<List<String>>any());
    }

    /**
     * Method under test:
     * {@link KitchenItemServiceImpl#autoPrint(String, String, List, List)}
     */
    @Test
    public void testAutoPrint11() {
        when(kitchenItemMapper.queryPrepareItem(Mockito.<ItemQuery>any())).thenReturn(new ArrayList<>());
        DeviceConfigDO deviceConfigDO = mock(DeviceConfigDO.class);
        when(deviceConfigDO.shouldPrintPerOrder()).thenReturn(true);
        when(deviceConfigDO.getGuid()).thenReturn("1234");
        when(deviceConfigDO.getIsPrintAutomatic()).thenReturn(true);

        HashMap<String, DeviceConfigDO> stringDeviceConfigDOMap = new HashMap<>();
        stringDeviceConfigDOMap.put("foo", deviceConfigDO);
        when(deviceConfigService.listPrdDeviceByGuid(Mockito.<String>any(), Mockito.<List<String>>any()))
                .thenReturn(stringDeviceConfigDOMap);

        ArrayList<String> kitchenItemGuidList = new ArrayList<>();
        kitchenItemGuidList.add("");
        kitchenItemServiceImpl.autoPrint("1234", "1234", kitchenItemGuidList, new ArrayList<>());
        verify(deviceConfigDO, atLeast(1)).getGuid();
        verify(deviceConfigDO).getIsPrintAutomatic();
        verify(deviceConfigDO).shouldPrintPerOrder();
        verify(kitchenItemMapper).queryPrepareItem(Mockito.<ItemQuery>any());
        verify(deviceConfigService).listPrdDeviceByGuid(Mockito.<String>any(), Mockito.<List<String>>any());
    }

    /**
     * Method under test:
     * {@link KitchenItemServiceImpl#autoPrint(String, String, List, List, boolean)}
     */
    @Test
    public void testAutoPrint12() {
        when(deviceConfigService.listPrdDeviceByGuid(Mockito.<String>any(), Mockito.<List<String>>any()))
                .thenReturn(new HashMap<>());
        ArrayList<String> kitchenItemGuidList = new ArrayList<>();
        kitchenItemServiceImpl.autoPrint("1234", "1234", kitchenItemGuidList, new ArrayList<>(), true);
        verify(deviceConfigService).listPrdDeviceByGuid(Mockito.<String>any(), Mockito.<List<String>>any());
    }

    /**
     * Method under test:
     * {@link KitchenItemServiceImpl#autoPrint(String, String, List, List, boolean)}
     */
    @Test
    public void testAutoPrint13() {
        HashMap<String, DeviceConfigDO> stringDeviceConfigDOMap = new HashMap<>();
        stringDeviceConfigDOMap.put("foo", DeviceConfigDO.defaultConfig());
        when(deviceConfigService.listPrdDeviceByGuid(Mockito.<String>any(), Mockito.<List<String>>any()))
                .thenReturn(stringDeviceConfigDOMap);
        ArrayList<String> kitchenItemGuidList = new ArrayList<>();
        kitchenItemServiceImpl.autoPrint("1234", "1234", kitchenItemGuidList, new ArrayList<>(), true);
        verify(deviceConfigService).listPrdDeviceByGuid(Mockito.<String>any(), Mockito.<List<String>>any());
    }

    /**
     * Method under test:
     * {@link KitchenItemServiceImpl#autoPrint(String, String, List, List, boolean)}
     */
    @Test
    public void testAutoPrint14() {
        HashMap<String, DeviceConfigDO> stringDeviceConfigDOMap = new HashMap<>();
        stringDeviceConfigDOMap.put("42", DeviceConfigDO.defaultConfig());
        stringDeviceConfigDOMap.put("foo", DeviceConfigDO.defaultConfig());
        when(deviceConfigService.listPrdDeviceByGuid(Mockito.<String>any(), Mockito.<List<String>>any()))
                .thenReturn(stringDeviceConfigDOMap);
        ArrayList<String> kitchenItemGuidList = new ArrayList<>();
        kitchenItemServiceImpl.autoPrint("1234", "1234", kitchenItemGuidList, new ArrayList<>(), true);
        verify(deviceConfigService).listPrdDeviceByGuid(Mockito.<String>any(), Mockito.<List<String>>any());
    }

    /**
     * Method under test:
     * {@link KitchenItemServiceImpl#autoPrint(String, String, List, List, boolean)}
     */
    @Test
    public void testAutoPrint15() {
        when(deviceConfigService.listPrdDeviceByGuid(Mockito.<String>any(), Mockito.<List<String>>any()))
                .thenReturn(new HashMap<>());

        ArrayList<String> kitchenItemGuidList = new ArrayList<>();
        kitchenItemGuidList.add("foo");
        kitchenItemServiceImpl.autoPrint("1234", "1234", kitchenItemGuidList, new ArrayList<>(), true);
        verify(deviceConfigService).listPrdDeviceByGuid(Mockito.<String>any(), Mockito.<List<String>>any());
    }

    /**
     * Method under test:
     * {@link KitchenItemServiceImpl#autoPrint(String, String, List, List, boolean)}
     */
    @Test
    public void testAutoPrint16() {
        when(deviceConfigService.listPrdDeviceByGuid(Mockito.<String>any(), Mockito.<List<String>>any()))
                .thenReturn(new HashMap<>());

        ArrayList<String> kitchenItemGuidList = new ArrayList<>();
        kitchenItemGuidList.add("42");
        kitchenItemGuidList.add("foo");
        kitchenItemServiceImpl.autoPrint("1234", "1234", kitchenItemGuidList, new ArrayList<>(), true);
        verify(deviceConfigService).listPrdDeviceByGuid(Mockito.<String>any(), Mockito.<List<String>>any());
    }

    /**
     * Method under test:
     * {@link KitchenItemServiceImpl#autoPrint(String, String, List, List, boolean)}
     */
    @Test
    public void testAutoPrint17() {
        when(deviceConfigService.listPrdDeviceByGuid(Mockito.<String>any(), Mockito.<List<String>>any()))
                .thenReturn(new HashMap<>());
        ArrayList<String> kitchenItemGuidList = new ArrayList<>();

        ArrayList<String> deviceGuidList = new ArrayList<>();
        deviceGuidList.add("foo");
        kitchenItemServiceImpl.autoPrint("1234", "1234", kitchenItemGuidList, deviceGuidList, true);
        verify(deviceConfigService).listPrdDeviceByGuid(Mockito.<String>any(), Mockito.<List<String>>any());
    }

    /**
     * Method under test:
     * {@link KitchenItemServiceImpl#autoPrint(String, String, List, List, boolean)}
     */
    @Test
    public void testAutoPrint18() {
        when(deviceConfigService.listPrdDeviceByGuid(Mockito.<String>any(), Mockito.<List<String>>any()))
                .thenReturn(new HashMap<>());
        ArrayList<String> kitchenItemGuidList = new ArrayList<>();

        ArrayList<String> deviceGuidList = new ArrayList<>();
        deviceGuidList.add("42");
        deviceGuidList.add("foo");
        kitchenItemServiceImpl.autoPrint("1234", "1234", kitchenItemGuidList, deviceGuidList, true);
        verify(deviceConfigService).listPrdDeviceByGuid(Mockito.<String>any(), Mockito.<List<String>>any());
    }

    /**
     * Method under test:
     * {@link KitchenItemServiceImpl#autoPrint(String, String, List, List, boolean)}
     */
    @Test
    public void testAutoPrint19() {
        when(deviceConfigService.listPrdDeviceByGuid(Mockito.<String>any(), Mockito.<List<String>>any()))
                .thenThrow(new IllegalArgumentException("foo"));
        ArrayList<String> kitchenItemGuidList = new ArrayList<>();
        thrown.expect(IllegalArgumentException.class);
        kitchenItemServiceImpl.autoPrint("1234", "1234", kitchenItemGuidList, new ArrayList<>(), true);
        verify(deviceConfigService).listPrdDeviceByGuid(Mockito.<String>any(), Mockito.<List<String>>any());
    }

    /**
     * Method under test:
     * {@link KitchenItemServiceImpl#autoPrint(String, String, List, List, boolean)}
     */
    @Test
    public void testAutoPrint20() {
        when(kitchenItemMapper.queryPrepareItem(Mockito.<ItemQuery>any())).thenReturn(new ArrayList<>());

        HashMap<String, DeviceConfigDO> stringDeviceConfigDOMap = new HashMap<>();
        stringDeviceConfigDOMap.put("foo", DeviceConfigDO.defaultConfig());
        when(deviceConfigService.listPrdDeviceByGuid(Mockito.<String>any(), Mockito.<List<String>>any()))
                .thenReturn(stringDeviceConfigDOMap);

        ArrayList<String> kitchenItemGuidList = new ArrayList<>();
        kitchenItemGuidList.add("");
        kitchenItemServiceImpl.autoPrint("1234", "1234", kitchenItemGuidList, new ArrayList<>(), true);
        verify(kitchenItemMapper).queryPrepareItem(Mockito.<ItemQuery>any());
        verify(deviceConfigService).listPrdDeviceByGuid(Mockito.<String>any(), Mockito.<List<String>>any());
    }

    /**
     * Method under test:
     * {@link KitchenItemServiceImpl#autoPrint(String, String, List, List, boolean)}
     */
    @Test
    public void testAutoPrint21() {
        when(kitchenItemMapper.queryPrepareItem(Mockito.<ItemQuery>any())).thenReturn(new ArrayList<>());
        DeviceConfigDO defaultConfigResult = DeviceConfigDO.defaultConfig();
        defaultConfigResult.setDisplayMode(2);

        HashMap<String, DeviceConfigDO> stringDeviceConfigDOMap = new HashMap<>();
        stringDeviceConfigDOMap.put("foo", defaultConfigResult);
        when(deviceConfigService.listPrdDeviceByGuid(Mockito.<String>any(), Mockito.<List<String>>any()))
                .thenReturn(stringDeviceConfigDOMap);

        ArrayList<String> kitchenItemGuidList = new ArrayList<>();
        kitchenItemGuidList.add("");
        kitchenItemServiceImpl.autoPrint("1234", "1234", kitchenItemGuidList, new ArrayList<>(), true);
        verify(kitchenItemMapper).queryPrepareItem(Mockito.<ItemQuery>any());
        verify(deviceConfigService).listPrdDeviceByGuid(Mockito.<String>any(), Mockito.<List<String>>any());
    }

    /**
     * Method under test:
     * {@link KitchenItemServiceImpl#autoPrint(String, String, List, List, boolean)}
     */
    @Test
    public void testAutoPrint22() {
        when(kitchenItemMapper.queryPrepareItem(Mockito.<ItemQuery>any())).thenReturn(new ArrayList<>());
        DeviceConfigDO deviceConfigDO = mock(DeviceConfigDO.class);
        when(deviceConfigDO.shouldPrintPerOrder()).thenReturn(true);
        when(deviceConfigDO.getGuid()).thenReturn("1234");
        when(deviceConfigDO.getIsPrintAutomatic()).thenReturn(true);

        HashMap<String, DeviceConfigDO> stringDeviceConfigDOMap = new HashMap<>();
        stringDeviceConfigDOMap.put("foo", deviceConfigDO);
        when(deviceConfigService.listPrdDeviceByGuid(Mockito.<String>any(), Mockito.<List<String>>any()))
                .thenReturn(stringDeviceConfigDOMap);

        ArrayList<String> kitchenItemGuidList = new ArrayList<>();
        kitchenItemGuidList.add("");
        kitchenItemServiceImpl.autoPrint("1234", "1234", kitchenItemGuidList, new ArrayList<>(), true);
        verify(deviceConfigDO, atLeast(1)).getGuid();
        verify(deviceConfigDO).getIsPrintAutomatic();
        verify(deviceConfigDO).shouldPrintPerOrder();
        verify(kitchenItemMapper).queryPrepareItem(Mockito.<ItemQuery>any());
        verify(deviceConfigService).listPrdDeviceByGuid(Mockito.<String>any(), Mockito.<List<String>>any());
    }

    /**
     * Method under test:
     * {@link KitchenItemServiceImpl#queryByOrderItem(ItemBatchRefundReqDTO)}
     */
    @Test
    @Ignore("TODO: Complete this test")
    public void testQueryByOrderItem() {
        // TODO: Complete this test.
        //   Reason: R013 No inputs found that don't throw a trivial exception.
        //   Diffblue Cover tried to run the arrange/act section, but the method under
        //   test threw
        //   java.lang.NullPointerException
        //       at com.holderzone.saas.store.kds.service.impl.KitchenItemServiceImpl.queryByOrderItem(KitchenItemServiceImpl.java:1445)
        //   See https://diff.blue/R013 to resolve this issue.

        kitchenItemServiceImpl.queryByOrderItem(new ItemBatchRefundReqDTO());
    }

    /**
     * Method under test:
     * {@link KitchenItemServiceImpl#queryByOrder(PrdDstItemQueryDTO)}
     */
    @Test
    public void testQueryByOrder() {
        when(kitchenItemMapper.selectList(Mockito.<Wrapper<KitchenItemDO>>any())).thenReturn(new ArrayList<>());

        PrdDstItemQueryDTO query = new PrdDstItemQueryDTO();
        query.setOrderGuid("1234");
        query.setOrderItemGuidList(new ArrayList<>());
        List<PrdDstItemDTO> actualQueryByOrderResult = kitchenItemServiceImpl.queryByOrder(query);
        verify(kitchenItemMapper).selectList(Mockito.<Wrapper<KitchenItemDO>>any());
        assertTrue(actualQueryByOrderResult.isEmpty());
    }

    /**
     * Method under test:
     * {@link KitchenItemServiceImpl#queryByOrder(PrdDstItemQueryDTO)}
     */
    @Test
    public void testQueryByOrder2() {
        KitchenItemDO kitchenItemDO = new KitchenItemDO();
        kitchenItemDO.setAreaGuid("1234");
        kitchenItemDO.setBatch(4);
        kitchenItemDO.setCallUpTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setCancelDstStaffGuid("1234");
        kitchenItemDO.setCancelDstStaffName("Cancel Dst Staff Name");
        kitchenItemDO.setCancelDstTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setCompleteStaffGuid("1234");
        kitchenItemDO.setCompleteStaffName("Complete Staff Name");
        kitchenItemDO.setCompleteTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setCookStaffGuid("1234");
        kitchenItemDO.setCookStaffName("Cook Staff Name");
        kitchenItemDO.setCookTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setCurrentCount(new BigDecimal("2.3"));
        kitchenItemDO.setDelayTimeMinutes(4);
        kitchenItemDO.setDisplayRuleType(4);
        kitchenItemDO.setDisplayTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setDisplayType(4);
        kitchenItemDO.setDistributeStaffGuid("1234");
        kitchenItemDO.setDistributeStaffName("Distribute Staff Name");
        kitchenItemDO.setDistributeTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setDstDeviceId("42");
        kitchenItemDO.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setGuid("1234");
        kitchenItemDO.setHangUpTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setId(1L);
        kitchenItemDO.setIsPrintAutomatic(true);
        kitchenItemDO.setIsWeight(true);
        kitchenItemDO.setItemAttrMd5("27c7cf400229103e00c6d8830029e29b");
        kitchenItemDO.setItemGuid("1234");
        kitchenItemDO.setItemName("Item Name");
        kitchenItemDO.setItemRemark("Item Remark");
        kitchenItemDO.setItemState(42);
        kitchenItemDO.setKitchenState(4);
        kitchenItemDO.setOrderDesc("Order Desc");
        kitchenItemDO.setOrderGuid("1234");
        kitchenItemDO.setOrderItemGuid("1234");
        kitchenItemDO.setOrderNumber("42");
        kitchenItemDO.setOrderRemark("Order Remark");
        kitchenItemDO.setOrderSerialNo("Order Serial No");
        kitchenItemDO.setOrderSortTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setOriginalItemGuid("1234");
        kitchenItemDO.setOriginalItemSkuName("Original Item Sku Name");
        kitchenItemDO.setOriginalSkuGuid("1234");
        kitchenItemDO.setPointGuid("1234");
        kitchenItemDO.setPrdDeviceId("42");
        kitchenItemDO.setPrepareTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setReturnCount(new BigDecimal("2.3"));
        kitchenItemDO.setSkuCode("Sku Code");
        kitchenItemDO.setSkuGuid("1234");
        kitchenItemDO.setSkuName("Sku Name");
        kitchenItemDO.setSkuUnit("Sku Unit");
        kitchenItemDO.setSort(4);
        kitchenItemDO.setStoreGuid("1234");
        kitchenItemDO.setTableGuid("1234");
        kitchenItemDO.setTimeout(10);
        kitchenItemDO.setUrgedTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setUrgedTimes(4);

        ArrayList<KitchenItemDO> kitchenItemDOList = new ArrayList<>();
        kitchenItemDOList.add(kitchenItemDO);
        when(kitchenItemMapper.selectList(Mockito.<Wrapper<KitchenItemDO>>any())).thenReturn(kitchenItemDOList);

        PrdDstItemQueryDTO query = new PrdDstItemQueryDTO();
        query.setOrderGuid("1234");
        query.setOrderItemGuidList(new ArrayList<>());
        List<PrdDstItemDTO> actualQueryByOrderResult = kitchenItemServiceImpl.queryByOrder(query);
        verify(kitchenItemMapper).selectList(Mockito.<Wrapper<KitchenItemDO>>any());
        assertEquals(1, actualQueryByOrderResult.size());
    }

    /**
     * Method under test:
     * {@link KitchenItemServiceImpl#queryByOrder(PrdDstItemQueryDTO)}
     */
    @Test
    public void testQueryByOrder3() {
        KitchenItemDO kitchenItemDO = new KitchenItemDO();
        kitchenItemDO.setAreaGuid("1234");
        kitchenItemDO.setBatch(4);
        kitchenItemDO.setCallUpTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setCancelDstStaffGuid("1234");
        kitchenItemDO.setCancelDstStaffName("Cancel Dst Staff Name");
        kitchenItemDO.setCancelDstTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setCompleteStaffGuid("1234");
        kitchenItemDO.setCompleteStaffName("Complete Staff Name");
        kitchenItemDO.setCompleteTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setCookStaffGuid("1234");
        kitchenItemDO.setCookStaffName("Cook Staff Name");
        kitchenItemDO.setCookTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setCurrentCount(new BigDecimal("2.3"));
        kitchenItemDO.setDelayTimeMinutes(4);
        kitchenItemDO.setDisplayRuleType(4);
        kitchenItemDO.setDisplayTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setDisplayType(4);
        kitchenItemDO.setDistributeStaffGuid("1234");
        kitchenItemDO.setDistributeStaffName("Distribute Staff Name");
        kitchenItemDO.setDistributeTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setDstDeviceId("42");
        kitchenItemDO.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setGuid("1234");
        kitchenItemDO.setHangUpTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setId(1L);
        kitchenItemDO.setIsPrintAutomatic(true);
        kitchenItemDO.setIsWeight(true);
        kitchenItemDO.setItemAttrMd5("27c7cf400229103e00c6d8830029e29b");
        kitchenItemDO.setItemGuid("1234");
        kitchenItemDO.setItemName("Item Name");
        kitchenItemDO.setItemRemark("Item Remark");
        kitchenItemDO.setItemState(42);
        kitchenItemDO.setKitchenState(4);
        kitchenItemDO.setOrderDesc("Order Desc");
        kitchenItemDO.setOrderGuid("1234");
        kitchenItemDO.setOrderItemGuid("1234");
        kitchenItemDO.setOrderNumber("42");
        kitchenItemDO.setOrderRemark("Order Remark");
        kitchenItemDO.setOrderSerialNo("Order Serial No");
        kitchenItemDO.setOrderSortTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setOriginalItemGuid("1234");
        kitchenItemDO.setOriginalItemSkuName("Original Item Sku Name");
        kitchenItemDO.setOriginalSkuGuid("1234");
        kitchenItemDO.setPointGuid("1234");
        kitchenItemDO.setPrdDeviceId("42");
        kitchenItemDO.setPrepareTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setReturnCount(new BigDecimal("2.3"));
        kitchenItemDO.setSkuCode("Sku Code");
        kitchenItemDO.setSkuGuid("1234");
        kitchenItemDO.setSkuName("Sku Name");
        kitchenItemDO.setSkuUnit("Sku Unit");
        kitchenItemDO.setSort(4);
        kitchenItemDO.setStoreGuid("1234");
        kitchenItemDO.setTableGuid("1234");
        kitchenItemDO.setTimeout(10);
        kitchenItemDO.setUrgedTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setUrgedTimes(4);

        KitchenItemDO kitchenItemDO2 = new KitchenItemDO();
        kitchenItemDO2.setAreaGuid("snack_area_guid");
        kitchenItemDO2.setBatch(1);
        kitchenItemDO2.setCallUpTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO2.setCancelDstStaffGuid("snack_area_guid");
        kitchenItemDO2.setCancelDstStaffName("Cancel Dst Staff Name");
        kitchenItemDO2.setCancelDstTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO2.setCompleteStaffGuid("snack_area_guid");
        kitchenItemDO2.setCompleteStaffName("Complete Staff Name");
        kitchenItemDO2.setCompleteTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO2.setCookStaffGuid("snack_area_guid");
        kitchenItemDO2.setCookStaffName("Cook Staff Name");
        kitchenItemDO2.setCookTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO2.setCurrentCount(new BigDecimal("2.3"));
        kitchenItemDO2.setDelayTimeMinutes(1);
        kitchenItemDO2.setDisplayRuleType(1);
        kitchenItemDO2.setDisplayTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO2.setDisplayType(1);
        kitchenItemDO2.setDistributeStaffGuid("snack_area_guid");
        kitchenItemDO2.setDistributeStaffName("Distribute Staff Name");
        kitchenItemDO2.setDistributeTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO2.setDstDeviceId("snack_area_guid");
        kitchenItemDO2.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO2.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO2.setGuid("snack_area_guid");
        kitchenItemDO2.setHangUpTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO2.setId(2L);
        kitchenItemDO2.setIsPrintAutomatic(false);
        kitchenItemDO2.setIsWeight(false);
        kitchenItemDO2.setItemAttrMd5("snack_area_guid");
        kitchenItemDO2.setItemGuid("snack_area_guid");
        kitchenItemDO2.setItemName("Item Name");
        kitchenItemDO2.setItemRemark("Item Remark");
        kitchenItemDO2.setItemState(4);
        kitchenItemDO2.setKitchenState(1);
        kitchenItemDO2.setOrderDesc("Order Desc");
        kitchenItemDO2.setOrderGuid("snack_area_guid");
        kitchenItemDO2.setOrderItemGuid("snack_area_guid");
        kitchenItemDO2.setOrderNumber("snack_area_guid");
        kitchenItemDO2.setOrderRemark("Order Remark");
        kitchenItemDO2.setOrderSerialNo("Order Serial No");
        kitchenItemDO2.setOrderSortTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO2.setOriginalItemGuid("snack_area_guid");
        kitchenItemDO2.setOriginalItemSkuName("Original Item Sku Name");
        kitchenItemDO2.setOriginalSkuGuid("snack_area_guid");
        kitchenItemDO2.setPointGuid("snack_area_guid");
        kitchenItemDO2.setPrdDeviceId("snack_area_guid");
        kitchenItemDO2.setPrepareTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO2.setReturnCount(new BigDecimal("2.3"));
        kitchenItemDO2.setSkuCode("Sku Code");
        kitchenItemDO2.setSkuGuid("snack_area_guid");
        kitchenItemDO2.setSkuName("Sku Name");
        kitchenItemDO2.setSkuUnit("Sku Unit");
        kitchenItemDO2.setSort(1);
        kitchenItemDO2.setStoreGuid("snack_area_guid");
        kitchenItemDO2.setTableGuid("snack_area_guid");
        kitchenItemDO2.setTimeout(4);
        kitchenItemDO2.setUrgedTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO2.setUrgedTimes(1);

        ArrayList<KitchenItemDO> kitchenItemDOList = new ArrayList<>();
        kitchenItemDOList.add(kitchenItemDO2);
        kitchenItemDOList.add(kitchenItemDO);
        when(kitchenItemMapper.selectList(Mockito.<Wrapper<KitchenItemDO>>any())).thenReturn(kitchenItemDOList);

        PrdDstItemQueryDTO query = new PrdDstItemQueryDTO();
        query.setOrderGuid("1234");
        query.setOrderItemGuidList(new ArrayList<>());
        List<PrdDstItemDTO> actualQueryByOrderResult = kitchenItemServiceImpl.queryByOrder(query);
        verify(kitchenItemMapper).selectList(Mockito.<Wrapper<KitchenItemDO>>any());
        assertEquals(2, actualQueryByOrderResult.size());
    }

    /**
     * Method under test:
     * {@link KitchenItemServiceImpl#queryByOrder(PrdDstItemQueryDTO)}
     */
    @Test
    public void testQueryByOrder4() {
        when(kitchenItemMapper.selectList(Mockito.<Wrapper<KitchenItemDO>>any())).thenReturn(new ArrayList<>());
        PrdDstItemQueryDTO query = mock(PrdDstItemQueryDTO.class);
        when(query.getOrderGuid()).thenReturn("");
        when(query.getOrderItemGuidList()).thenReturn(new ArrayList<>());
        doNothing().when(query).setOrderGuid(Mockito.<String>any());
        doNothing().when(query).setOrderItemGuidList(Mockito.<List<String>>any());
        query.setOrderGuid("1234");
        query.setOrderItemGuidList(new ArrayList<>());
        List<PrdDstItemDTO> actualQueryByOrderResult = kitchenItemServiceImpl.queryByOrder(query);
        verify(kitchenItemMapper).selectList(Mockito.<Wrapper<KitchenItemDO>>any());
        verify(query, atLeast(1)).getOrderGuid();
        verify(query, atLeast(1)).getOrderItemGuidList();
        verify(query).setOrderGuid(Mockito.<String>any());
        verify(query).setOrderItemGuidList(Mockito.<List<String>>any());
        assertTrue(actualQueryByOrderResult.isEmpty());
    }

    /**
     * Method under test:
     * {@link KitchenItemServiceImpl#queryByOrder(PrdDstItemQueryDTO)}
     */
    @Test
    public void testQueryByOrder5() {
        when(kitchenItemMapper.selectList(Mockito.<Wrapper<KitchenItemDO>>any())).thenReturn(new ArrayList<>());

        ArrayList<String> stringList = new ArrayList<>();
        stringList.add("foo");
        PrdDstItemQueryDTO query = mock(PrdDstItemQueryDTO.class);
        when(query.getOrderGuid()).thenReturn("1234");
        when(query.getOrderItemGuidList()).thenReturn(stringList);
        doNothing().when(query).setOrderGuid(Mockito.<String>any());
        doNothing().when(query).setOrderItemGuidList(Mockito.<List<String>>any());
        query.setOrderGuid("1234");
        query.setOrderItemGuidList(new ArrayList<>());
        List<PrdDstItemDTO> actualQueryByOrderResult = kitchenItemServiceImpl.queryByOrder(query);
        verify(kitchenItemMapper).selectList(Mockito.<Wrapper<KitchenItemDO>>any());
        verify(query, atLeast(1)).getOrderGuid();
        verify(query, atLeast(1)).getOrderItemGuidList();
        verify(query).setOrderGuid(Mockito.<String>any());
        verify(query).setOrderItemGuidList(Mockito.<List<String>>any());
        assertTrue(actualQueryByOrderResult.isEmpty());
    }

    /**
     * Method under test:
     * {@link KitchenItemServiceImpl#queryByOrder(PrdDstItemQueryDTO)}
     */
    @Test
    public void testQueryByOrder6() {
        KitchenItemDO kitchenItemDO = mock(KitchenItemDO.class);
        when(kitchenItemDO.getIsWeight()).thenReturn(true);
        when(kitchenItemDO.getBatch()).thenReturn(1);
        when(kitchenItemDO.getDisplayType()).thenReturn(1);
        when(kitchenItemDO.getItemState()).thenReturn(42);
        when(kitchenItemDO.getSort()).thenReturn(1);
        when(kitchenItemDO.getGuid()).thenReturn("1234");
        when(kitchenItemDO.getItemAttrMd5()).thenReturn("27c7cf400229103e00c6d8830029e29b");
        when(kitchenItemDO.getItemGuid()).thenReturn("1234");
        when(kitchenItemDO.getItemName()).thenReturn("Item Name");
        when(kitchenItemDO.getItemRemark()).thenReturn("Item Remark");
        when(kitchenItemDO.getOrderDesc()).thenReturn("Order Desc");
        when(kitchenItemDO.getOrderGuid()).thenReturn("1234");
        when(kitchenItemDO.getOrderNumber()).thenReturn("42");
        when(kitchenItemDO.getOrderRemark()).thenReturn("Order Remark");
        when(kitchenItemDO.getOrderSerialNo()).thenReturn("Order Serial No");
        when(kitchenItemDO.getSkuGuid()).thenReturn("1234");
        when(kitchenItemDO.getSkuName()).thenReturn("Sku Name");
        when(kitchenItemDO.getSkuUnit()).thenReturn("Sku Unit");
        when(kitchenItemDO.getCurrentCount()).thenReturn(new BigDecimal("2.3"));
        when(kitchenItemDO.getCallUpTime()).thenReturn(LocalDate.of(1970, 1, 1).atStartOfDay());
        when(kitchenItemDO.getHangUpTime()).thenReturn(LocalDate.of(1970, 1, 1).atStartOfDay());
        when(kitchenItemDO.getPrepareTime()).thenReturn(LocalDate.of(1970, 1, 1).atStartOfDay());
        when(kitchenItemDO.getUrgedTime()).thenReturn(LocalDate.of(1970, 1, 1).atStartOfDay());
        doNothing().when(kitchenItemDO).setGmtCreate(Mockito.<LocalDateTime>any());
        doNothing().when(kitchenItemDO).setGmtModified(Mockito.<LocalDateTime>any());
        doNothing().when(kitchenItemDO).setGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setId(Mockito.<Long>any());
        doNothing().when(kitchenItemDO).setAreaGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setBatch(Mockito.<Integer>any());
        doNothing().when(kitchenItemDO).setCallUpTime(Mockito.<LocalDateTime>any());
        doNothing().when(kitchenItemDO).setCancelDstStaffGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setCancelDstStaffName(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setCancelDstTime(Mockito.<LocalDateTime>any());
        doNothing().when(kitchenItemDO).setCompleteStaffGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setCompleteStaffName(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setCompleteTime(Mockito.<LocalDateTime>any());
        doNothing().when(kitchenItemDO).setCookStaffGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setCookStaffName(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setCookTime(Mockito.<LocalDateTime>any());
        doNothing().when(kitchenItemDO).setCurrentCount(Mockito.<BigDecimal>any());
        doNothing().when(kitchenItemDO).setDelayTimeMinutes(Mockito.<Integer>any());
        doNothing().when(kitchenItemDO).setDisplayRuleType(Mockito.<Integer>any());
        doNothing().when(kitchenItemDO).setDisplayTime(Mockito.<LocalDateTime>any());
        doNothing().when(kitchenItemDO).setDisplayType(Mockito.<Integer>any());
        doNothing().when(kitchenItemDO).setDistributeStaffGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setDistributeStaffName(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setDistributeTime(Mockito.<LocalDateTime>any());
        doNothing().when(kitchenItemDO).setDstDeviceId(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setHangUpTime(Mockito.<LocalDateTime>any());
        doNothing().when(kitchenItemDO).setIsPrintAutomatic(Mockito.<Boolean>any());
        doNothing().when(kitchenItemDO).setIsWeight(Mockito.<Boolean>any());
        doNothing().when(kitchenItemDO).setItemAttrMd5(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setItemGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setItemName(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setItemRemark(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setItemState(Mockito.<Integer>any());
        doNothing().when(kitchenItemDO).setKitchenState(Mockito.<Integer>any());
        doNothing().when(kitchenItemDO).setOrderDesc(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setOrderGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setOrderItemGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setOrderNumber(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setOrderRemark(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setOrderSerialNo(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setOrderSortTime(Mockito.<LocalDateTime>any());
        doNothing().when(kitchenItemDO).setOriginalItemGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setOriginalItemSkuName(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setOriginalSkuGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setPointGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setPrdDeviceId(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setPrepareTime(Mockito.<LocalDateTime>any());
        doNothing().when(kitchenItemDO).setReturnCount(Mockito.<BigDecimal>any());
        doNothing().when(kitchenItemDO).setSkuCode(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setSkuGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setSkuName(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setSkuUnit(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setSort(Mockito.<Integer>any());
        doNothing().when(kitchenItemDO).setStoreGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setTableGuid(Mockito.<String>any());
        doNothing().when(kitchenItemDO).setTimeout(Mockito.<Integer>any());
        doNothing().when(kitchenItemDO).setUrgedTime(Mockito.<LocalDateTime>any());
        doNothing().when(kitchenItemDO).setUrgedTimes(Mockito.<Integer>any());
        kitchenItemDO.setAreaGuid("Area Guid");
        kitchenItemDO.setBatch(1);
        kitchenItemDO.setCallUpTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setCancelDstStaffGuid("Cancel Dst Staff Guid");
        kitchenItemDO.setCancelDstStaffName("com.holderzone.saas.store.kds.entity.domain.KitchenItemDO");
        kitchenItemDO.setCancelDstTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setCompleteStaffGuid("Complete Staff Guid");
        kitchenItemDO.setCompleteStaffName("com.holderzone.saas.store.kds.entity.domain.KitchenItemDO");
        kitchenItemDO.setCompleteTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setCookStaffGuid("Cook Staff Guid");
        kitchenItemDO.setCookStaffName("com.holderzone.saas.store.kds.entity.domain.KitchenItemDO");
        kitchenItemDO.setCookTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setCurrentCount(new BigDecimal("2.3"));
        kitchenItemDO.setDelayTimeMinutes(1);
        kitchenItemDO.setDisplayRuleType(1);
        kitchenItemDO.setDisplayTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setDisplayType(1);
        kitchenItemDO.setDistributeStaffGuid("Distribute Staff Guid");
        kitchenItemDO.setDistributeStaffName("com.holderzone.saas.store.kds.entity.domain.KitchenItemDO");
        kitchenItemDO.setDistributeTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setDstDeviceId("Dst Device Id");
        kitchenItemDO.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setGuid("Guid");
        kitchenItemDO.setHangUpTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setId(2L);
        kitchenItemDO.setIsPrintAutomatic(true);
        kitchenItemDO.setIsWeight(true);
        kitchenItemDO.setItemAttrMd5("Item Attr Md5");
        kitchenItemDO.setItemGuid("Item Guid");
        kitchenItemDO.setItemName("com.holderzone.saas.store.kds.entity.domain.KitchenItemDO");
        kitchenItemDO.setItemRemark("com.holderzone.saas.store.kds.entity.domain.KitchenItemDO");
        kitchenItemDO.setItemState(4);
        kitchenItemDO.setKitchenState(1);
        kitchenItemDO.setOrderDesc("com.holderzone.saas.store.kds.entity.domain.KitchenItemDO");
        kitchenItemDO.setOrderGuid("Order Guid");
        kitchenItemDO.setOrderItemGuid("Order Item Guid");
        kitchenItemDO.setOrderNumber("Order Number");
        kitchenItemDO.setOrderRemark("com.holderzone.saas.store.kds.entity.domain.KitchenItemDO");
        kitchenItemDO.setOrderSerialNo("com.holderzone.saas.store.kds.entity.domain.KitchenItemDO");
        kitchenItemDO.setOrderSortTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setOriginalItemGuid("Original Item Guid");
        kitchenItemDO.setOriginalItemSkuName("com.holderzone.saas.store.kds.entity.domain.KitchenItemDO");
        kitchenItemDO.setOriginalSkuGuid("Original Sku Guid");
        kitchenItemDO.setPointGuid("Point Guid");
        kitchenItemDO.setPrdDeviceId("Prd Device Id");
        kitchenItemDO.setPrepareTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setReturnCount(new BigDecimal("2.3"));
        kitchenItemDO.setSkuCode("com.holderzone.saas.store.kds.entity.domain.KitchenItemDO");
        kitchenItemDO.setSkuGuid("Sku Guid");
        kitchenItemDO.setSkuName("com.holderzone.saas.store.kds.entity.domain.KitchenItemDO");
        kitchenItemDO.setSkuUnit("com.holderzone.saas.store.kds.entity.domain.KitchenItemDO");
        kitchenItemDO.setSort(1);
        kitchenItemDO.setStoreGuid("Store Guid");
        kitchenItemDO.setTableGuid("Table Guid");
        kitchenItemDO.setTimeout(4);
        kitchenItemDO.setUrgedTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        kitchenItemDO.setUrgedTimes(1);

        ArrayList<KitchenItemDO> kitchenItemDOList = new ArrayList<>();
        kitchenItemDOList.add(kitchenItemDO);
        when(kitchenItemMapper.selectList(Mockito.<Wrapper<KitchenItemDO>>any())).thenReturn(kitchenItemDOList);
        PrdDstItemQueryDTO query = mock(PrdDstItemQueryDTO.class);
        when(query.getOrderGuid()).thenReturn("");
        when(query.getOrderItemGuidList()).thenReturn(new ArrayList<>());
        doNothing().when(query).setOrderGuid(Mockito.<String>any());
        doNothing().when(query).setOrderItemGuidList(Mockito.<List<String>>any());
        query.setOrderGuid("1234");
        query.setOrderItemGuidList(new ArrayList<>());
        List<PrdDstItemDTO> actualQueryByOrderResult = kitchenItemServiceImpl.queryByOrder(query);
        verify(kitchenItemMapper).selectList(Mockito.<Wrapper<KitchenItemDO>>any());
        verify(query, atLeast(1)).getOrderGuid();
        verify(query, atLeast(1)).getOrderItemGuidList();
        verify(query).setOrderGuid(Mockito.<String>any());
        verify(query).setOrderItemGuidList(Mockito.<List<String>>any());
        verify(kitchenItemDO).getGuid();
        verify(kitchenItemDO).setGmtCreate(Mockito.<LocalDateTime>any());
        verify(kitchenItemDO).setGmtModified(Mockito.<LocalDateTime>any());
        verify(kitchenItemDO).setGuid(Mockito.<String>any());
        verify(kitchenItemDO).setId(Mockito.<Long>any());
        verify(kitchenItemDO).getBatch();
        verify(kitchenItemDO).getCallUpTime();
        verify(kitchenItemDO).getCurrentCount();
        verify(kitchenItemDO, atLeast(1)).getDisplayType();
        verify(kitchenItemDO).getHangUpTime();
        verify(kitchenItemDO, atLeast(1)).getIsWeight();
        verify(kitchenItemDO).getItemAttrMd5();
        verify(kitchenItemDO).getItemGuid();
        verify(kitchenItemDO).getItemName();
        verify(kitchenItemDO).getItemRemark();
        verify(kitchenItemDO, atLeast(1)).getItemState();
        verify(kitchenItemDO, atLeast(1)).getOrderDesc();
        verify(kitchenItemDO, atLeast(1)).getOrderGuid();
        verify(kitchenItemDO, atLeast(1)).getOrderNumber();
        verify(kitchenItemDO).getOrderRemark();
        verify(kitchenItemDO, atLeast(1)).getOrderSerialNo();
        verify(kitchenItemDO).getPrepareTime();
        verify(kitchenItemDO).getSkuGuid();
        verify(kitchenItemDO).getSkuName();
        verify(kitchenItemDO).getSkuUnit();
        verify(kitchenItemDO).getSort();
        verify(kitchenItemDO, atLeast(1)).getUrgedTime();
        verify(kitchenItemDO).setAreaGuid(Mockito.<String>any());
        verify(kitchenItemDO).setBatch(Mockito.<Integer>any());
        verify(kitchenItemDO).setCallUpTime(Mockito.<LocalDateTime>any());
        verify(kitchenItemDO).setCancelDstStaffGuid(Mockito.<String>any());
        verify(kitchenItemDO).setCancelDstStaffName(Mockito.<String>any());
        verify(kitchenItemDO).setCancelDstTime(Mockito.<LocalDateTime>any());
        verify(kitchenItemDO).setCompleteStaffGuid(Mockito.<String>any());
        verify(kitchenItemDO).setCompleteStaffName(Mockito.<String>any());
        verify(kitchenItemDO).setCompleteTime(Mockito.<LocalDateTime>any());
        verify(kitchenItemDO).setCookStaffGuid(Mockito.<String>any());
        verify(kitchenItemDO).setCookStaffName(Mockito.<String>any());
        verify(kitchenItemDO).setCookTime(Mockito.<LocalDateTime>any());
        verify(kitchenItemDO).setCurrentCount(Mockito.<BigDecimal>any());
        verify(kitchenItemDO).setDelayTimeMinutes(Mockito.<Integer>any());
        verify(kitchenItemDO).setDisplayRuleType(Mockito.<Integer>any());
        verify(kitchenItemDO).setDisplayTime(Mockito.<LocalDateTime>any());
        verify(kitchenItemDO).setDisplayType(Mockito.<Integer>any());
        verify(kitchenItemDO).setDistributeStaffGuid(Mockito.<String>any());
        verify(kitchenItemDO).setDistributeStaffName(Mockito.<String>any());
        verify(kitchenItemDO).setDistributeTime(Mockito.<LocalDateTime>any());
        verify(kitchenItemDO).setDstDeviceId(Mockito.<String>any());
        verify(kitchenItemDO).setHangUpTime(Mockito.<LocalDateTime>any());
        verify(kitchenItemDO).setIsPrintAutomatic(Mockito.<Boolean>any());
        verify(kitchenItemDO).setIsWeight(Mockito.<Boolean>any());
        verify(kitchenItemDO).setItemAttrMd5(Mockito.<String>any());
        verify(kitchenItemDO).setItemGuid(Mockito.<String>any());
        verify(kitchenItemDO).setItemName(Mockito.<String>any());
        verify(kitchenItemDO).setItemRemark(Mockito.<String>any());
        verify(kitchenItemDO).setItemState(Mockito.<Integer>any());
        verify(kitchenItemDO).setKitchenState(Mockito.<Integer>any());
        verify(kitchenItemDO).setOrderDesc(Mockito.<String>any());
        verify(kitchenItemDO).setOrderGuid(Mockito.<String>any());
        verify(kitchenItemDO).setOrderItemGuid(Mockito.<String>any());
        verify(kitchenItemDO).setOrderNumber(Mockito.<String>any());
        verify(kitchenItemDO).setOrderRemark(Mockito.<String>any());
        verify(kitchenItemDO).setOrderSerialNo(Mockito.<String>any());
        verify(kitchenItemDO).setOrderSortTime(Mockito.<LocalDateTime>any());
        verify(kitchenItemDO).setOriginalItemGuid(Mockito.<String>any());
        verify(kitchenItemDO).setOriginalItemSkuName(Mockito.<String>any());
        verify(kitchenItemDO).setOriginalSkuGuid(Mockito.<String>any());
        verify(kitchenItemDO).setPointGuid(Mockito.<String>any());
        verify(kitchenItemDO).setPrdDeviceId(Mockito.<String>any());
        verify(kitchenItemDO).setPrepareTime(Mockito.<LocalDateTime>any());
        verify(kitchenItemDO).setReturnCount(Mockito.<BigDecimal>any());
        verify(kitchenItemDO).setSkuCode(Mockito.<String>any());
        verify(kitchenItemDO).setSkuGuid(Mockito.<String>any());
        verify(kitchenItemDO).setSkuName(Mockito.<String>any());
        verify(kitchenItemDO).setSkuUnit(Mockito.<String>any());
        verify(kitchenItemDO).setSort(Mockito.<Integer>any());
        verify(kitchenItemDO).setStoreGuid(Mockito.<String>any());
        verify(kitchenItemDO).setTableGuid(Mockito.<String>any());
        verify(kitchenItemDO).setTimeout(Mockito.<Integer>any());
        verify(kitchenItemDO).setUrgedTime(Mockito.<LocalDateTime>any());
        verify(kitchenItemDO).setUrgedTimes(Mockito.<Integer>any());
        assertEquals(1, actualQueryByOrderResult.size());
    }

    /**
     * Method under test: {@link KitchenItemServiceImpl#batchDistribute(List)}
     */
    @Test
    public void testBatchDistribute() {
        //   Diffblue Cover was unable to write a Spring test,
        //   so wrote a non-Spring test instead.
        //   Reason: R002 Missing observers.
        //   Diffblue Cover was unable to create an assertion.
        //   Add getters for the following fields or make them package-private:
        //     ServiceImpl.baseMapper
        //     KitchenItemServiceImpl.deviceConfigService
        //     KitchenItemServiceImpl.displayItemService
        //     KitchenItemServiceImpl.displayRuleMapstruct
        //     KitchenItemServiceImpl.displayRuleService
        //     KitchenItemServiceImpl.displayStoreService
        //     KitchenItemServiceImpl.distributeAreaService
        //     KitchenItemServiceImpl.distributeItemService
        //     KitchenItemServiceImpl.distributedIdService
        //     KitchenItemServiceImpl.itemRpcService
        //     KitchenItemServiceImpl.kdsNotificationService
        //     KitchenItemServiceImpl.kdsPrintRecordService
        //     KitchenItemServiceImpl.kdsStatusPushService
        //     KitchenItemServiceImpl.kitchenItemAttrService
        //     KitchenItemServiceImpl.kitchenItemDelayNotificationService
        //     KitchenItemServiceImpl.kitchenItemMapper
        //     KitchenItemServiceImpl.kitchenItemMapstruct
        //     KitchenItemServiceImpl.prdPointItemService
        //     KitchenItemServiceImpl.productionPointService
        //     KitchenItemServiceImpl.queueItemService
        //     KitchenItemServiceImpl.redisUtils

        DeviceConfigService deviceConfigService = mock(DeviceConfigService.class);
        when(deviceConfigService.queryDstDispatchAsPrintByGuid(Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn(DeviceConfigDO.defaultConfig());

        DstBindStatusRespDTO dstBindStatusRespDTO = new DstBindStatusRespDTO();
        dstBindStatusRespDTO.setBoundAreaCount(3);
        dstBindStatusRespDTO.setBoundItemCount(3);
        dstBindStatusRespDTO.setIsSnackBound(true);
        dstBindStatusRespDTO.setIsTakeoutBound(true);
        DistributeAreaService distributeAreaService = mock(DistributeAreaService.class);
        when(distributeAreaService.queryAreaBindingPreview(Mockito.<DeviceQueryReqDTO>any()))
                .thenReturn(dstBindStatusRespDTO);
        KdsStatusPushService kdsStatusPushService = mock(KdsStatusPushService.class);
        doNothing().when(kdsStatusPushService)
                .statusChanged(Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any());
        doNothing().when(kdsStatusPushService)
                .voiceBroadcast(Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any());
        QueueItemService queueItemService = mock(QueueItemService.class);
        doNothing().when(queueItemService).inDistributedQueue(Mockito.<AsyncTask<ItemStateTransReqDTO>>any());
        KitchenItemServiceImpl kitchenItemServiceImpl = new KitchenItemServiceImpl(deviceConfigService,
                mock(ProductionPointService.class), mock(PrdPointItemService.class), mock(KitchenItemAttrService.class),
                mock(DistributeItemService.class), distributeAreaService, mock(KdsPrintRecordService.class),
                mock(KitchenItemMapstruct.class), mock(DistributedIdService.class), kdsStatusPushService, queueItemService,
                mock(DisplayStoreService.class), mock(DisplayRuleService.class), mock(DisplayItemService.class),
                mock(DisplayRuleMapstruct.class), mock(KdsNotificationService.class),
                mock(ItemRpcService.class), mock(TradeRpcService.class), mock(TableRpcService.class), mock(KitchenItemMapper.class),
                mock(KitchenAssociatedOrderService.class),
                mock(KitchenItemDelayNotificationService.class),
                mock(DisplayItemSortService.class), mock(DisplayRepeatItemService.class), mock(KitchenItemDeviceService.class),
                mock(KitchenItemHandlerFactory.class), mock(DeviceBindItemGroupMapper.class));

        ItemStateTransReqDTO itemStateTransReqDTO = new ItemStateTransReqDTO();
        itemStateTransReqDTO.setAccount("3");
        itemStateTransReqDTO.setAccountName("Dr Jane Doe");
        itemStateTransReqDTO.setActuallyPayFee(new BigDecimal("2.3"));
        itemStateTransReqDTO.setDeviceId("42");
        itemStateTransReqDTO.setDeviceType(1);
        itemStateTransReqDTO.setEnterpriseGuid("1234");
        itemStateTransReqDTO.setEnterpriseName("Enterprise Name");
        itemStateTransReqDTO.setInvoiceCode("Invoice Code");
        itemStateTransReqDTO.setInvoicePhone("**********");
        itemStateTransReqDTO.setIsInvoiceCode(1);
        itemStateTransReqDTO.setLatitude("Latitude");
        itemStateTransReqDTO.setLongitude("Longitude");
        itemStateTransReqDTO.setOperSubjectGuid("1234");
        itemStateTransReqDTO.setOrderDesc("Order Desc");
        itemStateTransReqDTO.setOrderDifference(1);
        itemStateTransReqDTO.setPrdDstItemList(new ArrayList<>());
        itemStateTransReqDTO.setQueryModeForPoint(1);
        itemStateTransReqDTO.setRequestTimestamp(1L);
        itemStateTransReqDTO.setStoreGuid("1234");
        itemStateTransReqDTO.setStoreName("Store Name");
        itemStateTransReqDTO.setUserGuid("1234");
        itemStateTransReqDTO.setUserName("janedoe");

        ArrayList<ItemStateTransReqDTO> reqDTOList = new ArrayList<>();
        reqDTOList.add(itemStateTransReqDTO);
        kitchenItemServiceImpl.batchDistribute(reqDTOList);
        verify(deviceConfigService).queryDstDispatchAsPrintByGuid(Mockito.<String>any(), Mockito.<String>any());
        verify(distributeAreaService).queryAreaBindingPreview(Mockito.<DeviceQueryReqDTO>any());
        verify(kdsStatusPushService).statusChanged(Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(),
                Mockito.<String>any());
        verify(kdsStatusPushService).voiceBroadcast(Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(),
                Mockito.<String>any());
        verify(queueItemService).inDistributedQueue(Mockito.<AsyncTask<ItemStateTransReqDTO>>any());
    }

    /**
     * Method under test: {@link KitchenItemServiceImpl#batchDistribute(List)}
     */
    @Test
    public void testBatchDistribute2() {
        //   Diffblue Cover was unable to write a Spring test,
        //   so wrote a non-Spring test instead.
        //   Reason: R002 Missing observers.
        //   Diffblue Cover was unable to create an assertion.
        //   Add getters for the following fields or make them package-private:
        //     ServiceImpl.baseMapper
        //     KitchenItemServiceImpl.deviceConfigService
        //     KitchenItemServiceImpl.displayItemService
        //     KitchenItemServiceImpl.displayRuleMapstruct
        //     KitchenItemServiceImpl.displayRuleService
        //     KitchenItemServiceImpl.displayStoreService
        //     KitchenItemServiceImpl.distributeAreaService
        //     KitchenItemServiceImpl.distributeItemService
        //     KitchenItemServiceImpl.distributedIdService
        //     KitchenItemServiceImpl.itemRpcService
        //     KitchenItemServiceImpl.kdsNotificationService
        //     KitchenItemServiceImpl.kdsPrintRecordService
        //     KitchenItemServiceImpl.kdsStatusPushService
        //     KitchenItemServiceImpl.kitchenItemAttrService
        //     KitchenItemServiceImpl.kitchenItemDelayNotificationService
        //     KitchenItemServiceImpl.kitchenItemMapper
        //     KitchenItemServiceImpl.kitchenItemMapstruct
        //     KitchenItemServiceImpl.prdPointItemService
        //     KitchenItemServiceImpl.productionPointService
        //     KitchenItemServiceImpl.queueItemService
        //     KitchenItemServiceImpl.redisUtils

        QueueItemService queueItemService = mock(QueueItemService.class);
        doThrow(new IllegalArgumentException("撤销状态：{}===>撤销菜品参数：{}")).when(queueItemService)
                .inDistributedQueue(Mockito.<AsyncTask<ItemStateTransReqDTO>>any());
        KitchenItemServiceImpl kitchenItemServiceImpl = new KitchenItemServiceImpl(mock(DeviceConfigService.class),
                mock(ProductionPointService.class), mock(PrdPointItemService.class), mock(KitchenItemAttrService.class),
                mock(DistributeItemService.class), mock(DistributeAreaService.class), mock(KdsPrintRecordService.class),
                mock(KitchenItemMapstruct.class), mock(DistributedIdService.class), mock(KdsStatusPushService.class),
                queueItemService, mock(DisplayStoreService.class), mock(DisplayRuleService.class),
                mock(DisplayItemService.class), mock(DisplayRuleMapstruct.class),
                mock(KdsNotificationService.class), mock(ItemRpcService.class), mock(TradeRpcService.class), mock(TableRpcService.class), mock(KitchenItemMapper.class),
                mock(KitchenAssociatedOrderService.class), mock(KitchenItemDelayNotificationService.class),
                mock(DisplayItemSortService.class), mock(DisplayRepeatItemService.class), mock(KitchenItemDeviceService.class),
                mock(KitchenItemHandlerFactory.class), mock(DeviceBindItemGroupMapper.class));

        ItemStateTransReqDTO itemStateTransReqDTO = new ItemStateTransReqDTO();
        itemStateTransReqDTO.setAccount("3");
        itemStateTransReqDTO.setAccountName("Dr Jane Doe");
        itemStateTransReqDTO.setActuallyPayFee(new BigDecimal("2.3"));
        itemStateTransReqDTO.setDeviceId("42");
        itemStateTransReqDTO.setDeviceType(1);
        itemStateTransReqDTO.setEnterpriseGuid("1234");
        itemStateTransReqDTO.setEnterpriseName("Enterprise Name");
        itemStateTransReqDTO.setInvoiceCode("Invoice Code");
        itemStateTransReqDTO.setInvoicePhone("**********");
        itemStateTransReqDTO.setIsInvoiceCode(1);
        itemStateTransReqDTO.setLatitude("Latitude");
        itemStateTransReqDTO.setLongitude("Longitude");
        itemStateTransReqDTO.setOperSubjectGuid("1234");
        itemStateTransReqDTO.setOrderDesc("Order Desc");
        itemStateTransReqDTO.setOrderDifference(1);
        itemStateTransReqDTO.setPrdDstItemList(new ArrayList<>());
        itemStateTransReqDTO.setQueryModeForPoint(1);
        itemStateTransReqDTO.setRequestTimestamp(1L);
        itemStateTransReqDTO.setStoreGuid("1234");
        itemStateTransReqDTO.setStoreName("Store Name");
        itemStateTransReqDTO.setUserGuid("1234");
        itemStateTransReqDTO.setUserName("janedoe");

        ArrayList<ItemStateTransReqDTO> reqDTOList = new ArrayList<>();
        reqDTOList.add(itemStateTransReqDTO);
        thrown.expect(IllegalArgumentException.class);
        kitchenItemServiceImpl.batchDistribute(reqDTOList);
        verify(queueItemService).inDistributedQueue(Mockito.<AsyncTask<ItemStateTransReqDTO>>any());
    }

    /**
     * Method under test: {@link KitchenItemServiceImpl#batchDistribute(List)}
     */
    @Test
    public void testBatchDistribute3() {
        //   Diffblue Cover was unable to write a Spring test,
        //   so wrote a non-Spring test instead.
        //   Reason: R002 Missing observers.
        //   Diffblue Cover was unable to create an assertion.
        //   Add getters for the following fields or make them package-private:
        //     ServiceImpl.baseMapper
        //     KitchenItemServiceImpl.deviceConfigService
        //     KitchenItemServiceImpl.displayItemService
        //     KitchenItemServiceImpl.displayRuleMapstruct
        //     KitchenItemServiceImpl.displayRuleService
        //     KitchenItemServiceImpl.displayStoreService
        //     KitchenItemServiceImpl.distributeAreaService
        //     KitchenItemServiceImpl.distributeItemService
        //     KitchenItemServiceImpl.distributedIdService
        //     KitchenItemServiceImpl.itemRpcService
        //     KitchenItemServiceImpl.kdsNotificationService
        //     KitchenItemServiceImpl.kdsPrintRecordService
        //     KitchenItemServiceImpl.kdsStatusPushService
        //     KitchenItemServiceImpl.kitchenItemAttrService
        //     KitchenItemServiceImpl.kitchenItemDelayNotificationService
        //     KitchenItemServiceImpl.kitchenItemMapper
        //     KitchenItemServiceImpl.kitchenItemMapstruct
        //     KitchenItemServiceImpl.prdPointItemService
        //     KitchenItemServiceImpl.productionPointService
        //     KitchenItemServiceImpl.queueItemService
        //     KitchenItemServiceImpl.redisUtils

        DeviceConfigDO deviceConfigDO = mock(DeviceConfigDO.class);
        when(deviceConfigDO.getIsDispatchAsPrint()).thenReturn(true);
        DeviceConfigService deviceConfigService = mock(DeviceConfigService.class);
        when(deviceConfigService.queryDstDispatchAsPrintByGuid(Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn(deviceConfigDO);

        DstBindStatusRespDTO dstBindStatusRespDTO = new DstBindStatusRespDTO();
        dstBindStatusRespDTO.setBoundAreaCount(3);
        dstBindStatusRespDTO.setBoundItemCount(3);
        dstBindStatusRespDTO.setIsSnackBound(true);
        dstBindStatusRespDTO.setIsTakeoutBound(true);
        DistributeAreaService distributeAreaService = mock(DistributeAreaService.class);
        when(distributeAreaService.queryAreaBindingPreview(Mockito.<DeviceQueryReqDTO>any()))
                .thenReturn(dstBindStatusRespDTO);
        KdsPrintRecordService kdsPrintRecordService = mock(KdsPrintRecordService.class);
        doNothing().when(kdsPrintRecordService).manualPrintDstItem(Mockito.<ItemStateTransReqDTO>any());
        KdsStatusPushService kdsStatusPushService = mock(KdsStatusPushService.class);
        doNothing().when(kdsStatusPushService)
                .statusChanged(Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any());
        doNothing().when(kdsStatusPushService)
                .voiceBroadcast(Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any());
        QueueItemService queueItemService = mock(QueueItemService.class);
        doNothing().when(queueItemService).inDistributedQueue(Mockito.<AsyncTask<ItemStateTransReqDTO>>any());
        KitchenItemServiceImpl kitchenItemServiceImpl = new KitchenItemServiceImpl(deviceConfigService,
                mock(ProductionPointService.class), mock(PrdPointItemService.class), mock(KitchenItemAttrService.class),
                mock(DistributeItemService.class), distributeAreaService, kdsPrintRecordService,
                mock(KitchenItemMapstruct.class), mock(DistributedIdService.class), kdsStatusPushService, queueItemService,
                mock(DisplayStoreService.class), mock(DisplayRuleService.class), mock(DisplayItemService.class),
                mock(DisplayRuleMapstruct.class),  mock(KdsNotificationService.class),
                mock(ItemRpcService.class), mock(TradeRpcService.class), mock(TableRpcService.class), mock(KitchenItemMapper.class),
                mock(KitchenAssociatedOrderService.class),
                mock(KitchenItemDelayNotificationService.class),
                mock(DisplayItemSortService.class), mock(DisplayRepeatItemService.class), mock(KitchenItemDeviceService.class),
                mock(KitchenItemHandlerFactory.class), mock(DeviceBindItemGroupMapper.class));

        ItemStateTransReqDTO itemStateTransReqDTO = new ItemStateTransReqDTO();
        itemStateTransReqDTO.setAccount("3");
        itemStateTransReqDTO.setAccountName("Dr Jane Doe");
        itemStateTransReqDTO.setActuallyPayFee(new BigDecimal("2.3"));
        itemStateTransReqDTO.setDeviceId("42");
        itemStateTransReqDTO.setDeviceType(1);
        itemStateTransReqDTO.setEnterpriseGuid("1234");
        itemStateTransReqDTO.setEnterpriseName("Enterprise Name");
        itemStateTransReqDTO.setInvoiceCode("Invoice Code");
        itemStateTransReqDTO.setInvoicePhone("**********");
        itemStateTransReqDTO.setIsInvoiceCode(1);
        itemStateTransReqDTO.setLatitude("Latitude");
        itemStateTransReqDTO.setLongitude("Longitude");
        itemStateTransReqDTO.setOperSubjectGuid("1234");
        itemStateTransReqDTO.setOrderDesc("Order Desc");
        itemStateTransReqDTO.setOrderDifference(1);
        itemStateTransReqDTO.setPrdDstItemList(new ArrayList<>());
        itemStateTransReqDTO.setQueryModeForPoint(1);
        itemStateTransReqDTO.setRequestTimestamp(1L);
        itemStateTransReqDTO.setStoreGuid("1234");
        itemStateTransReqDTO.setStoreName("Store Name");
        itemStateTransReqDTO.setUserGuid("1234");
        itemStateTransReqDTO.setUserName("janedoe");

        ArrayList<ItemStateTransReqDTO> reqDTOList = new ArrayList<>();
        reqDTOList.add(itemStateTransReqDTO);
        kitchenItemServiceImpl.batchDistribute(reqDTOList);
        verify(deviceConfigDO).getIsDispatchAsPrint();
        verify(deviceConfigService).queryDstDispatchAsPrintByGuid(Mockito.<String>any(), Mockito.<String>any());
        verify(distributeAreaService).queryAreaBindingPreview(Mockito.<DeviceQueryReqDTO>any());
        verify(kdsStatusPushService).statusChanged(Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(),
                Mockito.<String>any());
        verify(kdsStatusPushService).voiceBroadcast(Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(),
                Mockito.<String>any());
        verify(queueItemService).inDistributedQueue(Mockito.<AsyncTask<ItemStateTransReqDTO>>any());
        verify(kdsPrintRecordService).manualPrintDstItem(Mockito.<ItemStateTransReqDTO>any());
    }

    /**
     * Method under test: {@link KitchenItemServiceImpl#batchDistribute(List)}
     */
    @Test
    public void testBatchDistribute4() {
        //   Diffblue Cover was unable to write a Spring test,
        //   so wrote a non-Spring test instead.
        //   Reason: R002 Missing observers.
        //   Diffblue Cover was unable to create an assertion.
        //   Add getters for the following fields or make them package-private:
        //     ServiceImpl.baseMapper
        //     KitchenItemServiceImpl.deviceConfigService
        //     KitchenItemServiceImpl.displayItemService
        //     KitchenItemServiceImpl.displayRuleMapstruct
        //     KitchenItemServiceImpl.displayRuleService
        //     KitchenItemServiceImpl.displayStoreService
        //     KitchenItemServiceImpl.distributeAreaService
        //     KitchenItemServiceImpl.distributeItemService
        //     KitchenItemServiceImpl.distributedIdService
        //     KitchenItemServiceImpl.itemRpcService
        //     KitchenItemServiceImpl.kdsNotificationService
        //     KitchenItemServiceImpl.kdsPrintRecordService
        //     KitchenItemServiceImpl.kdsStatusPushService
        //     KitchenItemServiceImpl.kitchenItemAttrService
        //     KitchenItemServiceImpl.kitchenItemDelayNotificationService
        //     KitchenItemServiceImpl.kitchenItemMapper
        //     KitchenItemServiceImpl.kitchenItemMapstruct
        //     KitchenItemServiceImpl.prdPointItemService
        //     KitchenItemServiceImpl.productionPointService
        //     KitchenItemServiceImpl.queueItemService
        //     KitchenItemServiceImpl.redisUtils

        DeviceConfigDO deviceConfigDO = mock(DeviceConfigDO.class);
        when(deviceConfigDO.getIsDispatchAsPrint()).thenReturn(true);
        DeviceConfigService deviceConfigService = mock(DeviceConfigService.class);
        when(deviceConfigService.queryDstDispatchAsPrintByGuid(Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn(deviceConfigDO);
        KdsPrintRecordService kdsPrintRecordService = mock(KdsPrintRecordService.class);
        doThrow(new IllegalArgumentException("撤销状态：{}===>撤销菜品参数：{}")).when(kdsPrintRecordService)
                .manualPrintDstItem(Mockito.<ItemStateTransReqDTO>any());
        QueueItemService queueItemService = mock(QueueItemService.class);
        doNothing().when(queueItemService).inDistributedQueue(Mockito.<AsyncTask<ItemStateTransReqDTO>>any());
        KitchenItemServiceImpl kitchenItemServiceImpl = new KitchenItemServiceImpl(deviceConfigService,
                mock(ProductionPointService.class), mock(PrdPointItemService.class), mock(KitchenItemAttrService.class),
                mock(DistributeItemService.class), mock(DistributeAreaService.class), kdsPrintRecordService,
                mock(KitchenItemMapstruct.class), mock(DistributedIdService.class), mock(KdsStatusPushService.class),
                queueItemService, mock(DisplayStoreService.class), mock(DisplayRuleService.class),
                mock(DisplayItemService.class), mock(DisplayRuleMapstruct.class),
                mock(KdsNotificationService.class), mock(ItemRpcService.class), mock(TradeRpcService.class), mock(TableRpcService.class), mock(KitchenItemMapper.class),
                mock(KitchenAssociatedOrderService.class), mock(KitchenItemDelayNotificationService.class),
                mock(DisplayItemSortService.class), mock(DisplayRepeatItemService.class), mock(KitchenItemDeviceService.class),
                mock(KitchenItemHandlerFactory.class), mock(DeviceBindItemGroupMapper.class));

        ItemStateTransReqDTO itemStateTransReqDTO = new ItemStateTransReqDTO();
        itemStateTransReqDTO.setAccount("3");
        itemStateTransReqDTO.setAccountName("Dr Jane Doe");
        itemStateTransReqDTO.setActuallyPayFee(new BigDecimal("2.3"));
        itemStateTransReqDTO.setDeviceId("42");
        itemStateTransReqDTO.setDeviceType(1);
        itemStateTransReqDTO.setEnterpriseGuid("1234");
        itemStateTransReqDTO.setEnterpriseName("Enterprise Name");
        itemStateTransReqDTO.setInvoiceCode("Invoice Code");
        itemStateTransReqDTO.setInvoicePhone("**********");
        itemStateTransReqDTO.setIsInvoiceCode(1);
        itemStateTransReqDTO.setLatitude("Latitude");
        itemStateTransReqDTO.setLongitude("Longitude");
        itemStateTransReqDTO.setOperSubjectGuid("1234");
        itemStateTransReqDTO.setOrderDesc("Order Desc");
        itemStateTransReqDTO.setOrderDifference(1);
        itemStateTransReqDTO.setPrdDstItemList(new ArrayList<>());
        itemStateTransReqDTO.setQueryModeForPoint(1);
        itemStateTransReqDTO.setRequestTimestamp(1L);
        itemStateTransReqDTO.setStoreGuid("1234");
        itemStateTransReqDTO.setStoreName("Store Name");
        itemStateTransReqDTO.setUserGuid("1234");
        itemStateTransReqDTO.setUserName("janedoe");

        ArrayList<ItemStateTransReqDTO> reqDTOList = new ArrayList<>();
        reqDTOList.add(itemStateTransReqDTO);
        thrown.expect(IllegalArgumentException.class);
        kitchenItemServiceImpl.batchDistribute(reqDTOList);
        verify(deviceConfigDO).getIsDispatchAsPrint();
        verify(deviceConfigService).queryDstDispatchAsPrintByGuid(Mockito.<String>any(), Mockito.<String>any());
        verify(queueItemService).inDistributedQueue(Mockito.<AsyncTask<ItemStateTransReqDTO>>any());
        verify(kdsPrintRecordService).manualPrintDstItem(Mockito.<ItemStateTransReqDTO>any());
    }

    /**
     * Method under test: {@link KitchenItemServiceImpl#batchDistribute(List)}
     */
    @Test
    public void testBatchDistribute5() {
        //   Diffblue Cover was unable to write a Spring test,
        //   so wrote a non-Spring test instead.
        //   Reason: R002 Missing observers.
        //   Diffblue Cover was unable to create an assertion.
        //   Add getters for the following fields or make them package-private:
        //     ServiceImpl.baseMapper
        //     KitchenItemServiceImpl.deviceConfigService
        //     KitchenItemServiceImpl.displayItemService
        //     KitchenItemServiceImpl.displayRuleMapstruct
        //     KitchenItemServiceImpl.displayRuleService
        //     KitchenItemServiceImpl.displayStoreService
        //     KitchenItemServiceImpl.distributeAreaService
        //     KitchenItemServiceImpl.distributeItemService
        //     KitchenItemServiceImpl.distributedIdService
        //     KitchenItemServiceImpl.itemRpcService
        //     KitchenItemServiceImpl.kdsNotificationService
        //     KitchenItemServiceImpl.kdsPrintRecordService
        //     KitchenItemServiceImpl.kdsStatusPushService
        //     KitchenItemServiceImpl.kitchenItemAttrService
        //     KitchenItemServiceImpl.kitchenItemDelayNotificationService
        //     KitchenItemServiceImpl.kitchenItemMapper
        //     KitchenItemServiceImpl.kitchenItemMapstruct
        //     KitchenItemServiceImpl.prdPointItemService
        //     KitchenItemServiceImpl.productionPointService
        //     KitchenItemServiceImpl.queueItemService
        //     KitchenItemServiceImpl.redisUtils

        DeviceConfigDO deviceConfigDO = mock(DeviceConfigDO.class);
        when(deviceConfigDO.getIsDispatchAsPrint()).thenReturn(true);
        DeviceConfigService deviceConfigService = mock(DeviceConfigService.class);
        when(deviceConfigService.queryDstDispatchAsPrintByGuid(Mockito.<String>any(), Mockito.<String>any()))
                .thenReturn(deviceConfigDO);

        DstBindStatusRespDTO dstBindStatusRespDTO = new DstBindStatusRespDTO();
        dstBindStatusRespDTO.setBoundAreaCount(3);
        dstBindStatusRespDTO.setBoundItemCount(3);
        dstBindStatusRespDTO.setIsSnackBound(true);
        dstBindStatusRespDTO.setIsTakeoutBound(true);
        DistributeAreaService distributeAreaService = mock(DistributeAreaService.class);
        when(distributeAreaService.queryAreaBindingPreview(Mockito.<DeviceQueryReqDTO>any()))
                .thenReturn(dstBindStatusRespDTO);
        KdsPrintRecordService kdsPrintRecordService = mock(KdsPrintRecordService.class);
        doNothing().when(kdsPrintRecordService).manualPrintDstItem(Mockito.<ItemStateTransReqDTO>any());
        KdsStatusPushService kdsStatusPushService = mock(KdsStatusPushService.class);
        doNothing().when(kdsStatusPushService)
                .statusChanged(Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any());
        doNothing().when(kdsStatusPushService)
                .voiceBroadcast(Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any(), Mockito.<String>any());
        QueueItemService queueItemService = mock(QueueItemService.class);
        doNothing().when(queueItemService).inDistributedQueue(Mockito.<AsyncTask<ItemStateTransReqDTO>>any());
        KitchenItemServiceImpl kitchenItemServiceImpl = new KitchenItemServiceImpl(deviceConfigService,
                mock(ProductionPointService.class), mock(PrdPointItemService.class), mock(KitchenItemAttrService.class),
                mock(DistributeItemService.class), distributeAreaService, kdsPrintRecordService,
                mock(KitchenItemMapstruct.class), mock(DistributedIdService.class), kdsStatusPushService, queueItemService,
                mock(DisplayStoreService.class), mock(DisplayRuleService.class), mock(DisplayItemService.class),
                mock(DisplayRuleMapstruct.class),  mock(KdsNotificationService.class),
                mock(ItemRpcService.class), mock(TradeRpcService.class), mock(TableRpcService.class), mock(KitchenItemMapper.class),
                mock(KitchenAssociatedOrderService.class),
                mock(KitchenItemDelayNotificationService.class),
                mock(DisplayItemSortService.class), mock(DisplayRepeatItemService.class), mock(KitchenItemDeviceService.class),
                mock(KitchenItemHandlerFactory.class), mock(DeviceBindItemGroupMapper.class));

        ItemStateTransReqDTO itemStateTransReqDTO = new ItemStateTransReqDTO();
        itemStateTransReqDTO.setAccount("3");
        itemStateTransReqDTO.setAccountName("Dr Jane Doe");
        itemStateTransReqDTO.setActuallyPayFee(new BigDecimal("2.3"));
        itemStateTransReqDTO.setDeviceId("42");
        itemStateTransReqDTO.setDeviceType(1);
        itemStateTransReqDTO.setEnterpriseGuid("1234");
        itemStateTransReqDTO.setEnterpriseName("Enterprise Name");
        itemStateTransReqDTO.setInvoiceCode("Invoice Code");
        itemStateTransReqDTO.setInvoicePhone("**********");
        itemStateTransReqDTO.setIsInvoiceCode(1);
        itemStateTransReqDTO.setLatitude("Latitude");
        itemStateTransReqDTO.setLongitude("Longitude");
        itemStateTransReqDTO.setOperSubjectGuid("1234");
        itemStateTransReqDTO.setOrderDesc("Order Desc");
        itemStateTransReqDTO.setOrderDifference(1);
        itemStateTransReqDTO.setPrdDstItemList(new ArrayList<>());
        itemStateTransReqDTO.setQueryModeForPoint(1);
        itemStateTransReqDTO.setRequestTimestamp(1L);
        itemStateTransReqDTO.setStoreGuid("1234");
        itemStateTransReqDTO.setStoreName("Store Name");
        itemStateTransReqDTO.setUserGuid("1234");
        itemStateTransReqDTO.setUserName("janedoe");

        ItemStateTransReqDTO itemStateTransReqDTO2 = new ItemStateTransReqDTO();
        itemStateTransReqDTO2.setAccount("撤销状态：{}===>撤销菜品参数：{}");
        itemStateTransReqDTO2.setAccountName("Mr John Smith");
        itemStateTransReqDTO2.setActuallyPayFee(new BigDecimal("2.3"));
        itemStateTransReqDTO2.setDeviceId("撤销状态：{}===>撤销菜品参数：{}");
        itemStateTransReqDTO2.setDeviceType(0);
        itemStateTransReqDTO2.setEnterpriseGuid("撤销状态：{}===>撤销菜品参数：{}");
        itemStateTransReqDTO2.setEnterpriseName("itemStateTransReqDTO={}");
        itemStateTransReqDTO2.setInvoiceCode("itemStateTransReqDTO={}");
        itemStateTransReqDTO2.setInvoicePhone("**********");
        itemStateTransReqDTO2.setIsInvoiceCode(0);
        itemStateTransReqDTO2.setLatitude("itemStateTransReqDTO={}");
        itemStateTransReqDTO2.setLongitude("itemStateTransReqDTO={}");
        itemStateTransReqDTO2.setOperSubjectGuid("Hello from the Dreaming Spires");
        itemStateTransReqDTO2.setOrderDesc("itemStateTransReqDTO={}");
        itemStateTransReqDTO2.setOrderDifference(0);
        itemStateTransReqDTO2.setPrdDstItemList(new ArrayList<>());
        itemStateTransReqDTO2.setQueryModeForPoint(0);
        itemStateTransReqDTO2.setRequestTimestamp(0L);
        itemStateTransReqDTO2.setStoreGuid("撤销状态：{}===>撤销菜品参数：{}");
        itemStateTransReqDTO2.setStoreName("itemStateTransReqDTO={}");
        itemStateTransReqDTO2.setUserGuid("撤销状态：{}===>撤销菜品参数：{}");
        itemStateTransReqDTO2.setUserName("撤销状态：{}===>撤销菜品参数：{}");

        ArrayList<ItemStateTransReqDTO> reqDTOList = new ArrayList<>();
        reqDTOList.add(itemStateTransReqDTO2);
        reqDTOList.add(itemStateTransReqDTO);
        kitchenItemServiceImpl.batchDistribute(reqDTOList);
        verify(deviceConfigDO, atLeast(1)).getIsDispatchAsPrint();
        verify(deviceConfigService, atLeast(1)).queryDstDispatchAsPrintByGuid(Mockito.<String>any(), Mockito.<String>any());
        verify(distributeAreaService, atLeast(1)).queryAreaBindingPreview(Mockito.<DeviceQueryReqDTO>any());
        verify(kdsStatusPushService, atLeast(1)).statusChanged(Mockito.<String>any(), Mockito.<String>any(),
                Mockito.<String>any(), Mockito.<String>any());
        verify(kdsStatusPushService, atLeast(1)).voiceBroadcast(Mockito.<String>any(), Mockito.<String>any(),
                Mockito.<String>any(), Mockito.<String>any());
        verify(queueItemService, atLeast(1)).inDistributedQueue(Mockito.<AsyncTask<ItemStateTransReqDTO>>any());
        verify(kdsPrintRecordService, atLeast(1)).manualPrintDstItem(Mockito.<ItemStateTransReqDTO>any());
    }
}
