package com.holderzone.saas.store.kds.service.impl;

import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.resp.MappingRespDTO;
import com.holderzone.saas.store.dto.kds.req.*;
import com.holderzone.saas.store.dto.kds.resp.*;
import com.holderzone.saas.store.dto.table.AreaDTO;
import com.holderzone.saas.store.kds.entity.domain.DistributeAreaDO;
import com.holderzone.saas.store.kds.entity.domain.DistributeItemDO;
import com.holderzone.saas.store.kds.entity.domain.ItemConfigDO;
import com.holderzone.saas.store.kds.service.*;
import com.holderzone.saas.store.kds.service.rpc.ItemRpcService;
import com.holderzone.saas.store.kds.service.rpc.TableRpcService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class DistributeServiceImplTest {

    @Mock
    private DistributeAreaService mockDistributeAreaService;
    @Mock
    private DistributeItemService mockDistributeItemService;
    @Mock
    private DeviceConfigService mockDeviceConfigService;
    @Mock
    private ItemConfigService mockItemConfigService;
    @Mock
    private TableRpcService mockTableRpcService;
    @Mock
    private ItemRpcService mockItemRpcService;

    @Mock
    private DeviceBindItemGroupService mockDeviceBindItemGroupService;

    private DistributeServiceImpl distributeServiceImplUnderTest;

    @Before
    public void setUp() {
        distributeServiceImplUnderTest = new DistributeServiceImpl(mockDistributeAreaService, mockDistributeItemService,
                mockDeviceConfigService, mockItemConfigService, mockTableRpcService, mockItemRpcService, mockDeviceBindItemGroupService);
    }

    @Test
    public void testQueryBindingPreview() {
        // Setup
        final DeviceQueryReqDTO deviceQueryReqDTO = new DeviceQueryReqDTO("data", "deviceId");
        final DstBindStatusRespDTO expectedResult = new DstBindStatusRespDTO();
        expectedResult.setBoundAreaCount(0);
        expectedResult.setIsSnackBound(false);
        expectedResult.setIsTakeoutBound(false);
        expectedResult.setBoundItemCount(0);

        // Configure DistributeAreaService.queryAreaBindingPreview(...).
        final DstBindStatusRespDTO dstBindStatusRespDTO = new DstBindStatusRespDTO();
        dstBindStatusRespDTO.setBoundAreaCount(0);
        dstBindStatusRespDTO.setIsSnackBound(false);
        dstBindStatusRespDTO.setIsTakeoutBound(false);
        dstBindStatusRespDTO.setBoundItemCount(0);
        when(mockDistributeAreaService.queryAreaBindingPreview(new DeviceQueryReqDTO("data", "deviceId")))
                .thenReturn(dstBindStatusRespDTO);

        // Configure DistributeItemService.queryItemBindingPreview(...).
        final DstBindStatusRespDTO dstBindStatusRespDTO1 = new DstBindStatusRespDTO();
        dstBindStatusRespDTO1.setBoundAreaCount(0);
        dstBindStatusRespDTO1.setIsSnackBound(false);
        dstBindStatusRespDTO1.setIsTakeoutBound(false);
        dstBindStatusRespDTO1.setBoundItemCount(0);
        when(mockDistributeItemService.queryItemBindingPreview(new DeviceQueryReqDTO("data", "deviceId")))
                .thenReturn(dstBindStatusRespDTO1);

        // Run the test
        final DstBindStatusRespDTO result = distributeServiceImplUnderTest.queryBindingPreview(deviceQueryReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryBindingDetails() {
        // Setup
        final DstItemQueryReqDTO dstItemQueryReqDTO = new DstItemQueryReqDTO();
        dstItemQueryReqDTO.setStoreGuid("data");
        dstItemQueryReqDTO.setDeviceId("deviceId");

        final DstBindDetailsRespDTO expectedResult = new DstBindDetailsRespDTO();
        final DstBindStatusRespDTO dstBindStatus = new DstBindStatusRespDTO();
        dstBindStatus.setBoundAreaCount(0);
        dstBindStatus.setIsSnackBound(false);
        dstBindStatus.setIsTakeoutBound(false);
        dstBindStatus.setBoundItemCount(0);
        expectedResult.setDstBindStatus(dstBindStatus);
        final DstTypeBindRespDTO dstTypeBindRespDTO = new DstTypeBindRespDTO();
        dstTypeBindRespDTO.setTypeGuid("first");
        dstTypeBindRespDTO.setTypeName("itemName");
        final DstItemBindRespDTO dstItemBindRespDTO = new DstItemBindRespDTO();
        dstItemBindRespDTO.setItemGuid("first");
        dstItemBindRespDTO.setItemName("itemName");
        dstItemBindRespDTO.setPinyin("pinyin");
        dstItemBindRespDTO.setIsBoundBySelf(false);
        dstItemBindRespDTO.setIsBoundByOthers(false);
        final DstSkuBindRespDTO dstSkuBindRespDTO = new DstSkuBindRespDTO();
        dstSkuBindRespDTO.setSkuGuid("eDishSkuCode");
        dstSkuBindRespDTO.setSkuName("eDishSkuName");
        dstSkuBindRespDTO.setSkuCode("skuCode");
        final DstAreaBindRespDTO dstAreaBindRespDTO = new DstAreaBindRespDTO();
        dstAreaBindRespDTO.setDeviceId("deviceId");
        dstAreaBindRespDTO.setDeviceName("deviceName");
        dstAreaBindRespDTO.setAreaGuid("areaGuid");
        dstAreaBindRespDTO.setAreaName("areaName");
        dstSkuBindRespDTO.setAreas(Arrays.asList(dstAreaBindRespDTO));
        dstSkuBindRespDTO.setTimeout(0);
        dstSkuBindRespDTO.setIsBoundBySelf(false);
        dstSkuBindRespDTO.setIsBoundByOthers(false);
        dstItemBindRespDTO.setSkus(Arrays.asList(dstSkuBindRespDTO));
        dstTypeBindRespDTO.setItemList(Arrays.asList(dstItemBindRespDTO));
        expectedResult.setDstTypeBindList(Arrays.asList(dstTypeBindRespDTO));

        // Configure DistributeAreaService.queryAreaBindingPreview(...).
        final DstBindStatusRespDTO dstBindStatusRespDTO = new DstBindStatusRespDTO();
        dstBindStatusRespDTO.setBoundAreaCount(0);
        dstBindStatusRespDTO.setIsSnackBound(false);
        dstBindStatusRespDTO.setIsTakeoutBound(false);
        dstBindStatusRespDTO.setBoundItemCount(0);
        when(mockDistributeAreaService.queryAreaBindingPreview(new DeviceQueryReqDTO("data", "deviceId")))
                .thenReturn(dstBindStatusRespDTO);

        // Configure DistributeItemService.queryItemBindingPreview(...).
        final DstBindStatusRespDTO dstBindStatusRespDTO1 = new DstBindStatusRespDTO();
        dstBindStatusRespDTO1.setBoundAreaCount(0);
        dstBindStatusRespDTO1.setIsSnackBound(false);
        dstBindStatusRespDTO1.setIsTakeoutBound(false);
        dstBindStatusRespDTO1.setBoundItemCount(0);
        when(mockDistributeItemService.queryItemBindingPreview(new DeviceQueryReqDTO("data", "deviceId")))
                .thenReturn(dstBindStatusRespDTO1);

        when(mockDeviceConfigService.getDeviceNameMapOfStore("data")).thenReturn(new HashMap<>());

        // Configure DistributeItemService.queryBoundItemOfStore(...).
        final DistributeItemDO distributeItemDO = new DistributeItemDO();
        distributeItemDO.setStoreGuid("storeGuid");
        distributeItemDO.setDeviceId("deviceId");
        distributeItemDO.setItemGuid("itemGuid");
        distributeItemDO.setSkuGuid("skuGuid");
        distributeItemDO.setSkuCode("skuCode");
        final List<DistributeItemDO> distributeItemDOS = Arrays.asList(distributeItemDO);
        when(mockDistributeItemService.queryBoundItemOfStore("data")).thenReturn(distributeItemDOS);

        // Configure ItemRpcService.kdsMapping(...).
        final MappingRespDTO mappingRespDTO = new MappingRespDTO();
        mappingRespDTO.setPinyin("pinyin");
        mappingRespDTO.seteDishSkuCode("eDishSkuCode");
        mappingRespDTO.setSkuCode("skuCode");
        mappingRespDTO.seteDishSkuName("eDishSkuName");
        mappingRespDTO.seteDishCode("eDishCode");
        mappingRespDTO.seteDishName("eDishName");
        mappingRespDTO.setCategoryName("categoryName");
        mappingRespDTO.setCategoryId("categoryId");
        final List<MappingRespDTO> mappingRespDTOS = Arrays.asList(mappingRespDTO);
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("data");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        itemSingleDTO.setSkuGuids(Arrays.asList("value"));
        when(mockItemRpcService.kdsMapping(itemSingleDTO)).thenReturn(mappingRespDTOS);

        // Configure TableRpcService.query(...).
        final AreaDTO areaDTO = new AreaDTO();
        areaDTO.setGuid("snack_area_guid");
        areaDTO.setStoreGuid("storeGuid");
        areaDTO.setStoreName("storeName");
        areaDTO.setAreaName("外卖");
        areaDTO.setSort(0);
        final List<AreaDTO> areaDTOS = Arrays.asList(areaDTO);
        when(mockTableRpcService.query("data")).thenReturn(areaDTOS);

        // Configure DistributeAreaService.queryBoundAreaOfStore(...).
        final DistributeAreaDO distributeAreaDO = new DistributeAreaDO();
        distributeAreaDO.setStoreGuid("storeGuid");
        distributeAreaDO.setDeviceId("deviceId");
        distributeAreaDO.setAreaGuid("areaGuid");
        final List<DistributeAreaDO> distributeAreaDOS = Arrays.asList(distributeAreaDO);
        when(mockDistributeAreaService.queryBoundAreaOfStore("data")).thenReturn(distributeAreaDOS);

        // Configure ItemConfigService.queryBatchByStoreGuid(...).
        final ItemConfigDO itemConfigDO = new ItemConfigDO();
        itemConfigDO.setStoreGuid("storeGuid");
        itemConfigDO.setSkuGuid("skuGuid");
        itemConfigDO.setTimeout(0);
        itemConfigDO.setMaxCopies(0);
        itemConfigDO.setDisplayType(0);
        final List<ItemConfigDO> itemConfigDOS = Arrays.asList(itemConfigDO);
        when(mockItemConfigService.queryBatchByStoreGuid("data")).thenReturn(itemConfigDOS);

        // Run the test
        final DstBindDetailsRespDTO result = distributeServiceImplUnderTest.queryBindingDetails(dstItemQueryReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryBindingDetails_DistributeItemServiceQueryBoundItemOfStoreReturnsNoItems() {
        // Setup
        final DstItemQueryReqDTO dstItemQueryReqDTO = new DstItemQueryReqDTO();
        dstItemQueryReqDTO.setStoreGuid("data");
        dstItemQueryReqDTO.setDeviceId("deviceId");

        final DstBindDetailsRespDTO expectedResult = new DstBindDetailsRespDTO();
        final DstBindStatusRespDTO dstBindStatus = new DstBindStatusRespDTO();
        dstBindStatus.setBoundAreaCount(0);
        dstBindStatus.setIsSnackBound(false);
        dstBindStatus.setIsTakeoutBound(false);
        dstBindStatus.setBoundItemCount(0);
        expectedResult.setDstBindStatus(dstBindStatus);
        final DstTypeBindRespDTO dstTypeBindRespDTO = new DstTypeBindRespDTO();
        dstTypeBindRespDTO.setTypeGuid("first");
        dstTypeBindRespDTO.setTypeName("itemName");
        final DstItemBindRespDTO dstItemBindRespDTO = new DstItemBindRespDTO();
        dstItemBindRespDTO.setItemGuid("first");
        dstItemBindRespDTO.setItemName("itemName");
        dstItemBindRespDTO.setPinyin("pinyin");
        dstItemBindRespDTO.setIsBoundBySelf(false);
        dstItemBindRespDTO.setIsBoundByOthers(false);
        final DstSkuBindRespDTO dstSkuBindRespDTO = new DstSkuBindRespDTO();
        dstSkuBindRespDTO.setSkuGuid("eDishSkuCode");
        dstSkuBindRespDTO.setSkuName("eDishSkuName");
        dstSkuBindRespDTO.setSkuCode("skuCode");
        final DstAreaBindRespDTO dstAreaBindRespDTO = new DstAreaBindRespDTO();
        dstAreaBindRespDTO.setDeviceId("deviceId");
        dstAreaBindRespDTO.setDeviceName("deviceName");
        dstAreaBindRespDTO.setAreaGuid("areaGuid");
        dstAreaBindRespDTO.setAreaName("areaName");
        dstSkuBindRespDTO.setAreas(Arrays.asList(dstAreaBindRespDTO));
        dstSkuBindRespDTO.setTimeout(0);
        dstSkuBindRespDTO.setIsBoundBySelf(false);
        dstSkuBindRespDTO.setIsBoundByOthers(false);
        dstItemBindRespDTO.setSkus(Arrays.asList(dstSkuBindRespDTO));
        dstTypeBindRespDTO.setItemList(Arrays.asList(dstItemBindRespDTO));
        expectedResult.setDstTypeBindList(Arrays.asList(dstTypeBindRespDTO));

        // Configure DistributeAreaService.queryAreaBindingPreview(...).
        final DstBindStatusRespDTO dstBindStatusRespDTO = new DstBindStatusRespDTO();
        dstBindStatusRespDTO.setBoundAreaCount(0);
        dstBindStatusRespDTO.setIsSnackBound(false);
        dstBindStatusRespDTO.setIsTakeoutBound(false);
        dstBindStatusRespDTO.setBoundItemCount(0);
        when(mockDistributeAreaService.queryAreaBindingPreview(new DeviceQueryReqDTO("data", "deviceId")))
                .thenReturn(dstBindStatusRespDTO);

        // Configure DistributeItemService.queryItemBindingPreview(...).
        final DstBindStatusRespDTO dstBindStatusRespDTO1 = new DstBindStatusRespDTO();
        dstBindStatusRespDTO1.setBoundAreaCount(0);
        dstBindStatusRespDTO1.setIsSnackBound(false);
        dstBindStatusRespDTO1.setIsTakeoutBound(false);
        dstBindStatusRespDTO1.setBoundItemCount(0);
        when(mockDistributeItemService.queryItemBindingPreview(new DeviceQueryReqDTO("data", "deviceId")))
                .thenReturn(dstBindStatusRespDTO1);

        when(mockDeviceConfigService.getDeviceNameMapOfStore("data")).thenReturn(new HashMap<>());
        when(mockDistributeItemService.queryBoundItemOfStore("data")).thenReturn(Collections.emptyList());

        // Configure ItemRpcService.kdsMapping(...).
        final MappingRespDTO mappingRespDTO = new MappingRespDTO();
        mappingRespDTO.setPinyin("pinyin");
        mappingRespDTO.seteDishSkuCode("eDishSkuCode");
        mappingRespDTO.setSkuCode("skuCode");
        mappingRespDTO.seteDishSkuName("eDishSkuName");
        mappingRespDTO.seteDishCode("eDishCode");
        mappingRespDTO.seteDishName("eDishName");
        mappingRespDTO.setCategoryName("categoryName");
        mappingRespDTO.setCategoryId("categoryId");
        final List<MappingRespDTO> mappingRespDTOS = Arrays.asList(mappingRespDTO);
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("data");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        itemSingleDTO.setSkuGuids(Arrays.asList("value"));
        when(mockItemRpcService.kdsMapping(itemSingleDTO)).thenReturn(mappingRespDTOS);

        // Configure TableRpcService.query(...).
        final AreaDTO areaDTO = new AreaDTO();
        areaDTO.setGuid("snack_area_guid");
        areaDTO.setStoreGuid("storeGuid");
        areaDTO.setStoreName("storeName");
        areaDTO.setAreaName("外卖");
        areaDTO.setSort(0);
        final List<AreaDTO> areaDTOS = Arrays.asList(areaDTO);
        when(mockTableRpcService.query("data")).thenReturn(areaDTOS);

        // Configure DistributeAreaService.queryBoundAreaOfStore(...).
        final DistributeAreaDO distributeAreaDO = new DistributeAreaDO();
        distributeAreaDO.setStoreGuid("storeGuid");
        distributeAreaDO.setDeviceId("deviceId");
        distributeAreaDO.setAreaGuid("areaGuid");
        final List<DistributeAreaDO> distributeAreaDOS = Arrays.asList(distributeAreaDO);
        when(mockDistributeAreaService.queryBoundAreaOfStore("data")).thenReturn(distributeAreaDOS);

        // Configure ItemConfigService.queryBatchByStoreGuid(...).
        final ItemConfigDO itemConfigDO = new ItemConfigDO();
        itemConfigDO.setStoreGuid("storeGuid");
        itemConfigDO.setSkuGuid("skuGuid");
        itemConfigDO.setTimeout(0);
        itemConfigDO.setMaxCopies(0);
        itemConfigDO.setDisplayType(0);
        final List<ItemConfigDO> itemConfigDOS = Arrays.asList(itemConfigDO);
        when(mockItemConfigService.queryBatchByStoreGuid("data")).thenReturn(itemConfigDOS);

        // Run the test
        final DstBindDetailsRespDTO result = distributeServiceImplUnderTest.queryBindingDetails(dstItemQueryReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryBindingDetails_ItemRpcServiceReturnsNoItems() {
        // Setup
        final DstItemQueryReqDTO dstItemQueryReqDTO = new DstItemQueryReqDTO();
        dstItemQueryReqDTO.setStoreGuid("data");
        dstItemQueryReqDTO.setDeviceId("deviceId");

        final DstBindDetailsRespDTO expectedResult = new DstBindDetailsRespDTO();
        final DstBindStatusRespDTO dstBindStatus = new DstBindStatusRespDTO();
        dstBindStatus.setBoundAreaCount(0);
        dstBindStatus.setIsSnackBound(false);
        dstBindStatus.setIsTakeoutBound(false);
        dstBindStatus.setBoundItemCount(0);
        expectedResult.setDstBindStatus(dstBindStatus);
        final DstTypeBindRespDTO dstTypeBindRespDTO = new DstTypeBindRespDTO();
        dstTypeBindRespDTO.setTypeGuid("first");
        dstTypeBindRespDTO.setTypeName("itemName");
        final DstItemBindRespDTO dstItemBindRespDTO = new DstItemBindRespDTO();
        dstItemBindRespDTO.setItemGuid("first");
        dstItemBindRespDTO.setItemName("itemName");
        dstItemBindRespDTO.setPinyin("pinyin");
        dstItemBindRespDTO.setIsBoundBySelf(false);
        dstItemBindRespDTO.setIsBoundByOthers(false);
        final DstSkuBindRespDTO dstSkuBindRespDTO = new DstSkuBindRespDTO();
        dstSkuBindRespDTO.setSkuGuid("eDishSkuCode");
        dstSkuBindRespDTO.setSkuName("eDishSkuName");
        dstSkuBindRespDTO.setSkuCode("skuCode");
        final DstAreaBindRespDTO dstAreaBindRespDTO = new DstAreaBindRespDTO();
        dstAreaBindRespDTO.setDeviceId("deviceId");
        dstAreaBindRespDTO.setDeviceName("deviceName");
        dstAreaBindRespDTO.setAreaGuid("areaGuid");
        dstAreaBindRespDTO.setAreaName("areaName");
        dstSkuBindRespDTO.setAreas(Arrays.asList(dstAreaBindRespDTO));
        dstSkuBindRespDTO.setTimeout(0);
        dstSkuBindRespDTO.setIsBoundBySelf(false);
        dstSkuBindRespDTO.setIsBoundByOthers(false);
        dstItemBindRespDTO.setSkus(Arrays.asList(dstSkuBindRespDTO));
        dstTypeBindRespDTO.setItemList(Arrays.asList(dstItemBindRespDTO));
        expectedResult.setDstTypeBindList(Arrays.asList(dstTypeBindRespDTO));

        // Configure DistributeAreaService.queryAreaBindingPreview(...).
        final DstBindStatusRespDTO dstBindStatusRespDTO = new DstBindStatusRespDTO();
        dstBindStatusRespDTO.setBoundAreaCount(0);
        dstBindStatusRespDTO.setIsSnackBound(false);
        dstBindStatusRespDTO.setIsTakeoutBound(false);
        dstBindStatusRespDTO.setBoundItemCount(0);
        when(mockDistributeAreaService.queryAreaBindingPreview(new DeviceQueryReqDTO("data", "deviceId")))
                .thenReturn(dstBindStatusRespDTO);

        // Configure DistributeItemService.queryItemBindingPreview(...).
        final DstBindStatusRespDTO dstBindStatusRespDTO1 = new DstBindStatusRespDTO();
        dstBindStatusRespDTO1.setBoundAreaCount(0);
        dstBindStatusRespDTO1.setIsSnackBound(false);
        dstBindStatusRespDTO1.setIsTakeoutBound(false);
        dstBindStatusRespDTO1.setBoundItemCount(0);
        when(mockDistributeItemService.queryItemBindingPreview(new DeviceQueryReqDTO("data", "deviceId")))
                .thenReturn(dstBindStatusRespDTO1);

        when(mockDeviceConfigService.getDeviceNameMapOfStore("data")).thenReturn(new HashMap<>());

        // Configure DistributeItemService.queryBoundItemOfStore(...).
        final DistributeItemDO distributeItemDO = new DistributeItemDO();
        distributeItemDO.setStoreGuid("storeGuid");
        distributeItemDO.setDeviceId("deviceId");
        distributeItemDO.setItemGuid("itemGuid");
        distributeItemDO.setSkuGuid("skuGuid");
        distributeItemDO.setSkuCode("skuCode");
        final List<DistributeItemDO> distributeItemDOS = Arrays.asList(distributeItemDO);
        when(mockDistributeItemService.queryBoundItemOfStore("data")).thenReturn(distributeItemDOS);

        // Configure ItemRpcService.kdsMapping(...).
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("data");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        itemSingleDTO.setSkuGuids(Arrays.asList("value"));
        when(mockItemRpcService.kdsMapping(itemSingleDTO)).thenReturn(Collections.emptyList());

        // Configure TableRpcService.query(...).
        final AreaDTO areaDTO = new AreaDTO();
        areaDTO.setGuid("snack_area_guid");
        areaDTO.setStoreGuid("storeGuid");
        areaDTO.setStoreName("storeName");
        areaDTO.setAreaName("外卖");
        areaDTO.setSort(0);
        final List<AreaDTO> areaDTOS = Arrays.asList(areaDTO);
        when(mockTableRpcService.query("data")).thenReturn(areaDTOS);

        // Configure DistributeAreaService.queryBoundAreaOfStore(...).
        final DistributeAreaDO distributeAreaDO = new DistributeAreaDO();
        distributeAreaDO.setStoreGuid("storeGuid");
        distributeAreaDO.setDeviceId("deviceId");
        distributeAreaDO.setAreaGuid("areaGuid");
        final List<DistributeAreaDO> distributeAreaDOS = Arrays.asList(distributeAreaDO);
        when(mockDistributeAreaService.queryBoundAreaOfStore("data")).thenReturn(distributeAreaDOS);

        // Configure ItemConfigService.queryBatchByStoreGuid(...).
        final ItemConfigDO itemConfigDO = new ItemConfigDO();
        itemConfigDO.setStoreGuid("storeGuid");
        itemConfigDO.setSkuGuid("skuGuid");
        itemConfigDO.setTimeout(0);
        itemConfigDO.setMaxCopies(0);
        itemConfigDO.setDisplayType(0);
        final List<ItemConfigDO> itemConfigDOS = Arrays.asList(itemConfigDO);
        when(mockItemConfigService.queryBatchByStoreGuid("data")).thenReturn(itemConfigDOS);

        // Run the test
        final DstBindDetailsRespDTO result = distributeServiceImplUnderTest.queryBindingDetails(dstItemQueryReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryBindingDetails_TableRpcServiceReturnsNoItems() {
        // Setup
        final DstItemQueryReqDTO dstItemQueryReqDTO = new DstItemQueryReqDTO();
        dstItemQueryReqDTO.setStoreGuid("data");
        dstItemQueryReqDTO.setDeviceId("deviceId");

        final DstBindDetailsRespDTO expectedResult = new DstBindDetailsRespDTO();
        final DstBindStatusRespDTO dstBindStatus = new DstBindStatusRespDTO();
        dstBindStatus.setBoundAreaCount(0);
        dstBindStatus.setIsSnackBound(false);
        dstBindStatus.setIsTakeoutBound(false);
        dstBindStatus.setBoundItemCount(0);
        expectedResult.setDstBindStatus(dstBindStatus);
        final DstTypeBindRespDTO dstTypeBindRespDTO = new DstTypeBindRespDTO();
        dstTypeBindRespDTO.setTypeGuid("first");
        dstTypeBindRespDTO.setTypeName("itemName");
        final DstItemBindRespDTO dstItemBindRespDTO = new DstItemBindRespDTO();
        dstItemBindRespDTO.setItemGuid("first");
        dstItemBindRespDTO.setItemName("itemName");
        dstItemBindRespDTO.setPinyin("pinyin");
        dstItemBindRespDTO.setIsBoundBySelf(false);
        dstItemBindRespDTO.setIsBoundByOthers(false);
        final DstSkuBindRespDTO dstSkuBindRespDTO = new DstSkuBindRespDTO();
        dstSkuBindRespDTO.setSkuGuid("eDishSkuCode");
        dstSkuBindRespDTO.setSkuName("eDishSkuName");
        dstSkuBindRespDTO.setSkuCode("skuCode");
        final DstAreaBindRespDTO dstAreaBindRespDTO = new DstAreaBindRespDTO();
        dstAreaBindRespDTO.setDeviceId("deviceId");
        dstAreaBindRespDTO.setDeviceName("deviceName");
        dstAreaBindRespDTO.setAreaGuid("areaGuid");
        dstAreaBindRespDTO.setAreaName("areaName");
        dstSkuBindRespDTO.setAreas(Arrays.asList(dstAreaBindRespDTO));
        dstSkuBindRespDTO.setTimeout(0);
        dstSkuBindRespDTO.setIsBoundBySelf(false);
        dstSkuBindRespDTO.setIsBoundByOthers(false);
        dstItemBindRespDTO.setSkus(Arrays.asList(dstSkuBindRespDTO));
        dstTypeBindRespDTO.setItemList(Arrays.asList(dstItemBindRespDTO));
        expectedResult.setDstTypeBindList(Arrays.asList(dstTypeBindRespDTO));

        // Configure DistributeAreaService.queryAreaBindingPreview(...).
        final DstBindStatusRespDTO dstBindStatusRespDTO = new DstBindStatusRespDTO();
        dstBindStatusRespDTO.setBoundAreaCount(0);
        dstBindStatusRespDTO.setIsSnackBound(false);
        dstBindStatusRespDTO.setIsTakeoutBound(false);
        dstBindStatusRespDTO.setBoundItemCount(0);
        when(mockDistributeAreaService.queryAreaBindingPreview(new DeviceQueryReqDTO("data", "deviceId")))
                .thenReturn(dstBindStatusRespDTO);

        // Configure DistributeItemService.queryItemBindingPreview(...).
        final DstBindStatusRespDTO dstBindStatusRespDTO1 = new DstBindStatusRespDTO();
        dstBindStatusRespDTO1.setBoundAreaCount(0);
        dstBindStatusRespDTO1.setIsSnackBound(false);
        dstBindStatusRespDTO1.setIsTakeoutBound(false);
        dstBindStatusRespDTO1.setBoundItemCount(0);
        when(mockDistributeItemService.queryItemBindingPreview(new DeviceQueryReqDTO("data", "deviceId")))
                .thenReturn(dstBindStatusRespDTO1);

        when(mockDeviceConfigService.getDeviceNameMapOfStore("data")).thenReturn(new HashMap<>());

        // Configure DistributeItemService.queryBoundItemOfStore(...).
        final DistributeItemDO distributeItemDO = new DistributeItemDO();
        distributeItemDO.setStoreGuid("storeGuid");
        distributeItemDO.setDeviceId("deviceId");
        distributeItemDO.setItemGuid("itemGuid");
        distributeItemDO.setSkuGuid("skuGuid");
        distributeItemDO.setSkuCode("skuCode");
        final List<DistributeItemDO> distributeItemDOS = Arrays.asList(distributeItemDO);
        when(mockDistributeItemService.queryBoundItemOfStore("data")).thenReturn(distributeItemDOS);

        // Configure ItemRpcService.kdsMapping(...).
        final MappingRespDTO mappingRespDTO = new MappingRespDTO();
        mappingRespDTO.setPinyin("pinyin");
        mappingRespDTO.seteDishSkuCode("eDishSkuCode");
        mappingRespDTO.setSkuCode("skuCode");
        mappingRespDTO.seteDishSkuName("eDishSkuName");
        mappingRespDTO.seteDishCode("eDishCode");
        mappingRespDTO.seteDishName("eDishName");
        mappingRespDTO.setCategoryName("categoryName");
        mappingRespDTO.setCategoryId("categoryId");
        final List<MappingRespDTO> mappingRespDTOS = Arrays.asList(mappingRespDTO);
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("data");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        itemSingleDTO.setSkuGuids(Arrays.asList("value"));
        when(mockItemRpcService.kdsMapping(itemSingleDTO)).thenReturn(mappingRespDTOS);

        when(mockTableRpcService.query("data")).thenReturn(Collections.emptyList());

        // Configure DistributeAreaService.queryBoundAreaOfStore(...).
        final DistributeAreaDO distributeAreaDO = new DistributeAreaDO();
        distributeAreaDO.setStoreGuid("storeGuid");
        distributeAreaDO.setDeviceId("deviceId");
        distributeAreaDO.setAreaGuid("areaGuid");
        final List<DistributeAreaDO> distributeAreaDOS = Arrays.asList(distributeAreaDO);
        when(mockDistributeAreaService.queryBoundAreaOfStore("data")).thenReturn(distributeAreaDOS);

        // Configure ItemConfigService.queryBatchByStoreGuid(...).
        final ItemConfigDO itemConfigDO = new ItemConfigDO();
        itemConfigDO.setStoreGuid("storeGuid");
        itemConfigDO.setSkuGuid("skuGuid");
        itemConfigDO.setTimeout(0);
        itemConfigDO.setMaxCopies(0);
        itemConfigDO.setDisplayType(0);
        final List<ItemConfigDO> itemConfigDOS = Arrays.asList(itemConfigDO);
        when(mockItemConfigService.queryBatchByStoreGuid("data")).thenReturn(itemConfigDOS);

        // Run the test
        final DstBindDetailsRespDTO result = distributeServiceImplUnderTest.queryBindingDetails(dstItemQueryReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryBindingDetails_DistributeAreaServiceQueryBoundAreaOfStoreReturnsNoItems() {
        // Setup
        final DstItemQueryReqDTO dstItemQueryReqDTO = new DstItemQueryReqDTO();
        dstItemQueryReqDTO.setStoreGuid("data");
        dstItemQueryReqDTO.setDeviceId("deviceId");

        final DstBindDetailsRespDTO expectedResult = new DstBindDetailsRespDTO();
        final DstBindStatusRespDTO dstBindStatus = new DstBindStatusRespDTO();
        dstBindStatus.setBoundAreaCount(0);
        dstBindStatus.setIsSnackBound(false);
        dstBindStatus.setIsTakeoutBound(false);
        dstBindStatus.setBoundItemCount(0);
        expectedResult.setDstBindStatus(dstBindStatus);
        final DstTypeBindRespDTO dstTypeBindRespDTO = new DstTypeBindRespDTO();
        dstTypeBindRespDTO.setTypeGuid("first");
        dstTypeBindRespDTO.setTypeName("itemName");
        final DstItemBindRespDTO dstItemBindRespDTO = new DstItemBindRespDTO();
        dstItemBindRespDTO.setItemGuid("first");
        dstItemBindRespDTO.setItemName("itemName");
        dstItemBindRespDTO.setPinyin("pinyin");
        dstItemBindRespDTO.setIsBoundBySelf(false);
        dstItemBindRespDTO.setIsBoundByOthers(false);
        final DstSkuBindRespDTO dstSkuBindRespDTO = new DstSkuBindRespDTO();
        dstSkuBindRespDTO.setSkuGuid("eDishSkuCode");
        dstSkuBindRespDTO.setSkuName("eDishSkuName");
        dstSkuBindRespDTO.setSkuCode("skuCode");
        final DstAreaBindRespDTO dstAreaBindRespDTO = new DstAreaBindRespDTO();
        dstAreaBindRespDTO.setDeviceId("deviceId");
        dstAreaBindRespDTO.setDeviceName("deviceName");
        dstAreaBindRespDTO.setAreaGuid("areaGuid");
        dstAreaBindRespDTO.setAreaName("areaName");
        dstSkuBindRespDTO.setAreas(Arrays.asList(dstAreaBindRespDTO));
        dstSkuBindRespDTO.setTimeout(0);
        dstSkuBindRespDTO.setIsBoundBySelf(false);
        dstSkuBindRespDTO.setIsBoundByOthers(false);
        dstItemBindRespDTO.setSkus(Arrays.asList(dstSkuBindRespDTO));
        dstTypeBindRespDTO.setItemList(Arrays.asList(dstItemBindRespDTO));
        expectedResult.setDstTypeBindList(Arrays.asList(dstTypeBindRespDTO));

        // Configure DistributeAreaService.queryAreaBindingPreview(...).
        final DstBindStatusRespDTO dstBindStatusRespDTO = new DstBindStatusRespDTO();
        dstBindStatusRespDTO.setBoundAreaCount(0);
        dstBindStatusRespDTO.setIsSnackBound(false);
        dstBindStatusRespDTO.setIsTakeoutBound(false);
        dstBindStatusRespDTO.setBoundItemCount(0);
        when(mockDistributeAreaService.queryAreaBindingPreview(new DeviceQueryReqDTO("data", "deviceId")))
                .thenReturn(dstBindStatusRespDTO);

        // Configure DistributeItemService.queryItemBindingPreview(...).
        final DstBindStatusRespDTO dstBindStatusRespDTO1 = new DstBindStatusRespDTO();
        dstBindStatusRespDTO1.setBoundAreaCount(0);
        dstBindStatusRespDTO1.setIsSnackBound(false);
        dstBindStatusRespDTO1.setIsTakeoutBound(false);
        dstBindStatusRespDTO1.setBoundItemCount(0);
        when(mockDistributeItemService.queryItemBindingPreview(new DeviceQueryReqDTO("data", "deviceId")))
                .thenReturn(dstBindStatusRespDTO1);

        when(mockDeviceConfigService.getDeviceNameMapOfStore("data")).thenReturn(new HashMap<>());

        // Configure DistributeItemService.queryBoundItemOfStore(...).
        final DistributeItemDO distributeItemDO = new DistributeItemDO();
        distributeItemDO.setStoreGuid("storeGuid");
        distributeItemDO.setDeviceId("deviceId");
        distributeItemDO.setItemGuid("itemGuid");
        distributeItemDO.setSkuGuid("skuGuid");
        distributeItemDO.setSkuCode("skuCode");
        final List<DistributeItemDO> distributeItemDOS = Arrays.asList(distributeItemDO);
        when(mockDistributeItemService.queryBoundItemOfStore("data")).thenReturn(distributeItemDOS);

        // Configure ItemRpcService.kdsMapping(...).
        final MappingRespDTO mappingRespDTO = new MappingRespDTO();
        mappingRespDTO.setPinyin("pinyin");
        mappingRespDTO.seteDishSkuCode("eDishSkuCode");
        mappingRespDTO.setSkuCode("skuCode");
        mappingRespDTO.seteDishSkuName("eDishSkuName");
        mappingRespDTO.seteDishCode("eDishCode");
        mappingRespDTO.seteDishName("eDishName");
        mappingRespDTO.setCategoryName("categoryName");
        mappingRespDTO.setCategoryId("categoryId");
        final List<MappingRespDTO> mappingRespDTOS = Arrays.asList(mappingRespDTO);
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("data");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        itemSingleDTO.setSkuGuids(Arrays.asList("value"));
        when(mockItemRpcService.kdsMapping(itemSingleDTO)).thenReturn(mappingRespDTOS);

        // Configure TableRpcService.query(...).
        final AreaDTO areaDTO = new AreaDTO();
        areaDTO.setGuid("snack_area_guid");
        areaDTO.setStoreGuid("storeGuid");
        areaDTO.setStoreName("storeName");
        areaDTO.setAreaName("外卖");
        areaDTO.setSort(0);
        final List<AreaDTO> areaDTOS = Arrays.asList(areaDTO);
        when(mockTableRpcService.query("data")).thenReturn(areaDTOS);

        when(mockDistributeAreaService.queryBoundAreaOfStore("data")).thenReturn(Collections.emptyList());

        // Configure ItemConfigService.queryBatchByStoreGuid(...).
        final ItemConfigDO itemConfigDO = new ItemConfigDO();
        itemConfigDO.setStoreGuid("storeGuid");
        itemConfigDO.setSkuGuid("skuGuid");
        itemConfigDO.setTimeout(0);
        itemConfigDO.setMaxCopies(0);
        itemConfigDO.setDisplayType(0);
        final List<ItemConfigDO> itemConfigDOS = Arrays.asList(itemConfigDO);
        when(mockItemConfigService.queryBatchByStoreGuid("data")).thenReturn(itemConfigDOS);

        // Run the test
        final DstBindDetailsRespDTO result = distributeServiceImplUnderTest.queryBindingDetails(dstItemQueryReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryBindingDetails_ItemConfigServiceReturnsNoItems() {
        // Setup
        final DstItemQueryReqDTO dstItemQueryReqDTO = new DstItemQueryReqDTO();
        dstItemQueryReqDTO.setStoreGuid("data");
        dstItemQueryReqDTO.setDeviceId("deviceId");

        final DstBindDetailsRespDTO expectedResult = new DstBindDetailsRespDTO();
        final DstBindStatusRespDTO dstBindStatus = new DstBindStatusRespDTO();
        dstBindStatus.setBoundAreaCount(0);
        dstBindStatus.setIsSnackBound(false);
        dstBindStatus.setIsTakeoutBound(false);
        dstBindStatus.setBoundItemCount(0);
        expectedResult.setDstBindStatus(dstBindStatus);
        final DstTypeBindRespDTO dstTypeBindRespDTO = new DstTypeBindRespDTO();
        dstTypeBindRespDTO.setTypeGuid("first");
        dstTypeBindRespDTO.setTypeName("itemName");
        final DstItemBindRespDTO dstItemBindRespDTO = new DstItemBindRespDTO();
        dstItemBindRespDTO.setItemGuid("first");
        dstItemBindRespDTO.setItemName("itemName");
        dstItemBindRespDTO.setPinyin("pinyin");
        dstItemBindRespDTO.setIsBoundBySelf(false);
        dstItemBindRespDTO.setIsBoundByOthers(false);
        final DstSkuBindRespDTO dstSkuBindRespDTO = new DstSkuBindRespDTO();
        dstSkuBindRespDTO.setSkuGuid("eDishSkuCode");
        dstSkuBindRespDTO.setSkuName("eDishSkuName");
        dstSkuBindRespDTO.setSkuCode("skuCode");
        final DstAreaBindRespDTO dstAreaBindRespDTO = new DstAreaBindRespDTO();
        dstAreaBindRespDTO.setDeviceId("deviceId");
        dstAreaBindRespDTO.setDeviceName("deviceName");
        dstAreaBindRespDTO.setAreaGuid("areaGuid");
        dstAreaBindRespDTO.setAreaName("areaName");
        dstSkuBindRespDTO.setAreas(Arrays.asList(dstAreaBindRespDTO));
        dstSkuBindRespDTO.setTimeout(0);
        dstSkuBindRespDTO.setIsBoundBySelf(false);
        dstSkuBindRespDTO.setIsBoundByOthers(false);
        dstItemBindRespDTO.setSkus(Arrays.asList(dstSkuBindRespDTO));
        dstTypeBindRespDTO.setItemList(Arrays.asList(dstItemBindRespDTO));
        expectedResult.setDstTypeBindList(Arrays.asList(dstTypeBindRespDTO));

        // Configure DistributeAreaService.queryAreaBindingPreview(...).
        final DstBindStatusRespDTO dstBindStatusRespDTO = new DstBindStatusRespDTO();
        dstBindStatusRespDTO.setBoundAreaCount(0);
        dstBindStatusRespDTO.setIsSnackBound(false);
        dstBindStatusRespDTO.setIsTakeoutBound(false);
        dstBindStatusRespDTO.setBoundItemCount(0);
        when(mockDistributeAreaService.queryAreaBindingPreview(new DeviceQueryReqDTO("data", "deviceId")))
                .thenReturn(dstBindStatusRespDTO);

        // Configure DistributeItemService.queryItemBindingPreview(...).
        final DstBindStatusRespDTO dstBindStatusRespDTO1 = new DstBindStatusRespDTO();
        dstBindStatusRespDTO1.setBoundAreaCount(0);
        dstBindStatusRespDTO1.setIsSnackBound(false);
        dstBindStatusRespDTO1.setIsTakeoutBound(false);
        dstBindStatusRespDTO1.setBoundItemCount(0);
        when(mockDistributeItemService.queryItemBindingPreview(new DeviceQueryReqDTO("data", "deviceId")))
                .thenReturn(dstBindStatusRespDTO1);

        when(mockDeviceConfigService.getDeviceNameMapOfStore("data")).thenReturn(new HashMap<>());

        // Configure DistributeItemService.queryBoundItemOfStore(...).
        final DistributeItemDO distributeItemDO = new DistributeItemDO();
        distributeItemDO.setStoreGuid("storeGuid");
        distributeItemDO.setDeviceId("deviceId");
        distributeItemDO.setItemGuid("itemGuid");
        distributeItemDO.setSkuGuid("skuGuid");
        distributeItemDO.setSkuCode("skuCode");
        final List<DistributeItemDO> distributeItemDOS = Arrays.asList(distributeItemDO);
        when(mockDistributeItemService.queryBoundItemOfStore("data")).thenReturn(distributeItemDOS);

        // Configure ItemRpcService.kdsMapping(...).
        final MappingRespDTO mappingRespDTO = new MappingRespDTO();
        mappingRespDTO.setPinyin("pinyin");
        mappingRespDTO.seteDishSkuCode("eDishSkuCode");
        mappingRespDTO.setSkuCode("skuCode");
        mappingRespDTO.seteDishSkuName("eDishSkuName");
        mappingRespDTO.seteDishCode("eDishCode");
        mappingRespDTO.seteDishName("eDishName");
        mappingRespDTO.setCategoryName("categoryName");
        mappingRespDTO.setCategoryId("categoryId");
        final List<MappingRespDTO> mappingRespDTOS = Arrays.asList(mappingRespDTO);
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("data");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        itemSingleDTO.setSkuGuids(Arrays.asList("value"));
        when(mockItemRpcService.kdsMapping(itemSingleDTO)).thenReturn(mappingRespDTOS);

        // Configure TableRpcService.query(...).
        final AreaDTO areaDTO = new AreaDTO();
        areaDTO.setGuid("snack_area_guid");
        areaDTO.setStoreGuid("storeGuid");
        areaDTO.setStoreName("storeName");
        areaDTO.setAreaName("外卖");
        areaDTO.setSort(0);
        final List<AreaDTO> areaDTOS = Arrays.asList(areaDTO);
        when(mockTableRpcService.query("data")).thenReturn(areaDTOS);

        // Configure DistributeAreaService.queryBoundAreaOfStore(...).
        final DistributeAreaDO distributeAreaDO = new DistributeAreaDO();
        distributeAreaDO.setStoreGuid("storeGuid");
        distributeAreaDO.setDeviceId("deviceId");
        distributeAreaDO.setAreaGuid("areaGuid");
        final List<DistributeAreaDO> distributeAreaDOS = Arrays.asList(distributeAreaDO);
        when(mockDistributeAreaService.queryBoundAreaOfStore("data")).thenReturn(distributeAreaDOS);

        when(mockItemConfigService.queryBatchByStoreGuid("data")).thenReturn(Collections.emptyList());

        // Run the test
        final DstBindDetailsRespDTO result = distributeServiceImplUnderTest.queryBindingDetails(dstItemQueryReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testIsNumber() {
        assertThat(DistributeServiceImpl.isNumber("str")).isFalse();
    }

    @Test
    public void testQueryBoundArea() {
        // Setup
        final DstBoundAreaReqDTO dstBoundAreaReqDTO = new DstBoundAreaReqDTO();
        dstBoundAreaReqDTO.setDeviceId("deviceId");
        dstBoundAreaReqDTO.setStoreGuid("storeGuid");

        final DstAreaRespDTO expectedResult = new DstAreaRespDTO();
        final DstAreaStatusDTO dstAreaStatusDTO = new DstAreaStatusDTO();
        dstAreaStatusDTO.setAreaGuid("snack_area_guid");
        dstAreaStatusDTO.setAreaName("外卖");
        dstAreaStatusDTO.setIsBoundBySelf(false);
        dstAreaStatusDTO.setIsBoundByOthers(false);
        expectedResult.setAreaList(Arrays.asList(dstAreaStatusDTO));
        final DstAreaStatusDTO snackStatus = new DstAreaStatusDTO();
        snackStatus.setAreaGuid("snack_area_guid");
        snackStatus.setAreaName("外卖");
        snackStatus.setIsBoundBySelf(false);
        snackStatus.setIsBoundByOthers(false);
        expectedResult.setSnackStatus(snackStatus);
        final DstAreaStatusDTO takeoutStatus = new DstAreaStatusDTO();
        takeoutStatus.setAreaGuid("snack_area_guid");
        takeoutStatus.setAreaName("外卖");
        takeoutStatus.setIsBoundBySelf(false);
        takeoutStatus.setIsBoundByOthers(false);
        expectedResult.setTakeoutStatus(takeoutStatus);

        // Configure TableRpcService.query(...).
        final AreaDTO areaDTO = new AreaDTO();
        areaDTO.setGuid("snack_area_guid");
        areaDTO.setStoreGuid("storeGuid");
        areaDTO.setStoreName("storeName");
        areaDTO.setAreaName("外卖");
        areaDTO.setSort(0);
        final List<AreaDTO> areaDTOS = Arrays.asList(areaDTO);
        when(mockTableRpcService.query("storeGuid")).thenReturn(areaDTOS);

        // Configure DistributeItemService.queryBoundItemOfStore(...).
        final DistributeItemDO distributeItemDO = new DistributeItemDO();
        distributeItemDO.setStoreGuid("storeGuid");
        distributeItemDO.setDeviceId("deviceId");
        distributeItemDO.setItemGuid("itemGuid");
        distributeItemDO.setSkuGuid("skuGuid");
        distributeItemDO.setSkuCode("skuCode");
        final List<DistributeItemDO> distributeItemDOS = Arrays.asList(distributeItemDO);
        when(mockDistributeItemService.queryBoundItemOfStore("storeGuid")).thenReturn(distributeItemDOS);

        // Configure DistributeAreaService.queryBoundAreaOfStore(...).
        final DistributeAreaDO distributeAreaDO = new DistributeAreaDO();
        distributeAreaDO.setStoreGuid("storeGuid");
        distributeAreaDO.setDeviceId("deviceId");
        distributeAreaDO.setAreaGuid("areaGuid");
        final List<DistributeAreaDO> distributeAreaDOS = Arrays.asList(distributeAreaDO);
        when(mockDistributeAreaService.queryBoundAreaOfStore("storeGuid")).thenReturn(distributeAreaDOS);

        // Run the test
        final DstAreaRespDTO result = distributeServiceImplUnderTest.queryBoundArea(dstBoundAreaReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockDistributeAreaService).simpleRemoveBatchArea("storeGuid", "deviceId", Arrays.asList("value"));
    }

    @Test
    public void testQueryBoundArea_TableRpcServiceReturnsNoItems() {
        // Setup
        final DstBoundAreaReqDTO dstBoundAreaReqDTO = new DstBoundAreaReqDTO();
        dstBoundAreaReqDTO.setDeviceId("deviceId");
        dstBoundAreaReqDTO.setStoreGuid("storeGuid");

        final DstAreaRespDTO expectedResult = new DstAreaRespDTO();
        final DstAreaStatusDTO dstAreaStatusDTO = new DstAreaStatusDTO();
        dstAreaStatusDTO.setAreaGuid("snack_area_guid");
        dstAreaStatusDTO.setAreaName("外卖");
        dstAreaStatusDTO.setIsBoundBySelf(false);
        dstAreaStatusDTO.setIsBoundByOthers(false);
        expectedResult.setAreaList(Arrays.asList(dstAreaStatusDTO));
        final DstAreaStatusDTO snackStatus = new DstAreaStatusDTO();
        snackStatus.setAreaGuid("snack_area_guid");
        snackStatus.setAreaName("外卖");
        snackStatus.setIsBoundBySelf(false);
        snackStatus.setIsBoundByOthers(false);
        expectedResult.setSnackStatus(snackStatus);
        final DstAreaStatusDTO takeoutStatus = new DstAreaStatusDTO();
        takeoutStatus.setAreaGuid("snack_area_guid");
        takeoutStatus.setAreaName("外卖");
        takeoutStatus.setIsBoundBySelf(false);
        takeoutStatus.setIsBoundByOthers(false);
        expectedResult.setTakeoutStatus(takeoutStatus);

        when(mockTableRpcService.query("storeGuid")).thenReturn(Collections.emptyList());

        // Configure DistributeItemService.queryBoundItemOfStore(...).
        final DistributeItemDO distributeItemDO = new DistributeItemDO();
        distributeItemDO.setStoreGuid("storeGuid");
        distributeItemDO.setDeviceId("deviceId");
        distributeItemDO.setItemGuid("itemGuid");
        distributeItemDO.setSkuGuid("skuGuid");
        distributeItemDO.setSkuCode("skuCode");
        final List<DistributeItemDO> distributeItemDOS = Arrays.asList(distributeItemDO);
        when(mockDistributeItemService.queryBoundItemOfStore("storeGuid")).thenReturn(distributeItemDOS);

        // Configure DistributeAreaService.queryBoundAreaOfStore(...).
        final DistributeAreaDO distributeAreaDO = new DistributeAreaDO();
        distributeAreaDO.setStoreGuid("storeGuid");
        distributeAreaDO.setDeviceId("deviceId");
        distributeAreaDO.setAreaGuid("areaGuid");
        final List<DistributeAreaDO> distributeAreaDOS = Arrays.asList(distributeAreaDO);
        when(mockDistributeAreaService.queryBoundAreaOfStore("storeGuid")).thenReturn(distributeAreaDOS);

        // Run the test
        final DstAreaRespDTO result = distributeServiceImplUnderTest.queryBoundArea(dstBoundAreaReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockDistributeAreaService).simpleRemoveBatchArea("storeGuid", "deviceId", Arrays.asList("value"));
    }

    @Test
    public void testQueryBoundArea_DistributeItemServiceReturnsNoItems() {
        // Setup
        final DstBoundAreaReqDTO dstBoundAreaReqDTO = new DstBoundAreaReqDTO();
        dstBoundAreaReqDTO.setDeviceId("deviceId");
        dstBoundAreaReqDTO.setStoreGuid("storeGuid");

        final DstAreaRespDTO expectedResult = new DstAreaRespDTO();
        final DstAreaStatusDTO dstAreaStatusDTO = new DstAreaStatusDTO();
        dstAreaStatusDTO.setAreaGuid("snack_area_guid");
        dstAreaStatusDTO.setAreaName("外卖");
        dstAreaStatusDTO.setIsBoundBySelf(false);
        dstAreaStatusDTO.setIsBoundByOthers(false);
        expectedResult.setAreaList(Arrays.asList(dstAreaStatusDTO));
        final DstAreaStatusDTO snackStatus = new DstAreaStatusDTO();
        snackStatus.setAreaGuid("snack_area_guid");
        snackStatus.setAreaName("外卖");
        snackStatus.setIsBoundBySelf(false);
        snackStatus.setIsBoundByOthers(false);
        expectedResult.setSnackStatus(snackStatus);
        final DstAreaStatusDTO takeoutStatus = new DstAreaStatusDTO();
        takeoutStatus.setAreaGuid("snack_area_guid");
        takeoutStatus.setAreaName("外卖");
        takeoutStatus.setIsBoundBySelf(false);
        takeoutStatus.setIsBoundByOthers(false);
        expectedResult.setTakeoutStatus(takeoutStatus);

        // Configure TableRpcService.query(...).
        final AreaDTO areaDTO = new AreaDTO();
        areaDTO.setGuid("snack_area_guid");
        areaDTO.setStoreGuid("storeGuid");
        areaDTO.setStoreName("storeName");
        areaDTO.setAreaName("外卖");
        areaDTO.setSort(0);
        final List<AreaDTO> areaDTOS = Arrays.asList(areaDTO);
        when(mockTableRpcService.query("storeGuid")).thenReturn(areaDTOS);

        when(mockDistributeItemService.queryBoundItemOfStore("storeGuid")).thenReturn(Collections.emptyList());

        // Configure DistributeAreaService.queryBoundAreaOfStore(...).
        final DistributeAreaDO distributeAreaDO = new DistributeAreaDO();
        distributeAreaDO.setStoreGuid("storeGuid");
        distributeAreaDO.setDeviceId("deviceId");
        distributeAreaDO.setAreaGuid("areaGuid");
        final List<DistributeAreaDO> distributeAreaDOS = Arrays.asList(distributeAreaDO);
        when(mockDistributeAreaService.queryBoundAreaOfStore("storeGuid")).thenReturn(distributeAreaDOS);

        // Run the test
        final DstAreaRespDTO result = distributeServiceImplUnderTest.queryBoundArea(dstBoundAreaReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockDistributeAreaService).simpleRemoveBatchArea("storeGuid", "deviceId", Arrays.asList("value"));
    }

    @Test
    public void testQueryBoundArea_DistributeAreaServiceQueryBoundAreaOfStoreReturnsNoItems() {
        // Setup
        final DstBoundAreaReqDTO dstBoundAreaReqDTO = new DstBoundAreaReqDTO();
        dstBoundAreaReqDTO.setDeviceId("deviceId");
        dstBoundAreaReqDTO.setStoreGuid("storeGuid");

        final DstAreaRespDTO expectedResult = new DstAreaRespDTO();
        final DstAreaStatusDTO dstAreaStatusDTO = new DstAreaStatusDTO();
        dstAreaStatusDTO.setAreaGuid("snack_area_guid");
        dstAreaStatusDTO.setAreaName("外卖");
        dstAreaStatusDTO.setIsBoundBySelf(false);
        dstAreaStatusDTO.setIsBoundByOthers(false);
        expectedResult.setAreaList(Arrays.asList(dstAreaStatusDTO));
        final DstAreaStatusDTO snackStatus = new DstAreaStatusDTO();
        snackStatus.setAreaGuid("snack_area_guid");
        snackStatus.setAreaName("外卖");
        snackStatus.setIsBoundBySelf(false);
        snackStatus.setIsBoundByOthers(false);
        expectedResult.setSnackStatus(snackStatus);
        final DstAreaStatusDTO takeoutStatus = new DstAreaStatusDTO();
        takeoutStatus.setAreaGuid("snack_area_guid");
        takeoutStatus.setAreaName("外卖");
        takeoutStatus.setIsBoundBySelf(false);
        takeoutStatus.setIsBoundByOthers(false);
        expectedResult.setTakeoutStatus(takeoutStatus);

        // Configure TableRpcService.query(...).
        final AreaDTO areaDTO = new AreaDTO();
        areaDTO.setGuid("snack_area_guid");
        areaDTO.setStoreGuid("storeGuid");
        areaDTO.setStoreName("storeName");
        areaDTO.setAreaName("外卖");
        areaDTO.setSort(0);
        final List<AreaDTO> areaDTOS = Arrays.asList(areaDTO);
        when(mockTableRpcService.query("storeGuid")).thenReturn(areaDTOS);

        // Configure DistributeItemService.queryBoundItemOfStore(...).
        final DistributeItemDO distributeItemDO = new DistributeItemDO();
        distributeItemDO.setStoreGuid("storeGuid");
        distributeItemDO.setDeviceId("deviceId");
        distributeItemDO.setItemGuid("itemGuid");
        distributeItemDO.setSkuGuid("skuGuid");
        distributeItemDO.setSkuCode("skuCode");
        final List<DistributeItemDO> distributeItemDOS = Arrays.asList(distributeItemDO);
        when(mockDistributeItemService.queryBoundItemOfStore("storeGuid")).thenReturn(distributeItemDOS);

        when(mockDistributeAreaService.queryBoundAreaOfStore("storeGuid")).thenReturn(Collections.emptyList());

        // Run the test
        final DstAreaRespDTO result = distributeServiceImplUnderTest.queryBoundArea(dstBoundAreaReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockDistributeAreaService).simpleRemoveBatchArea("storeGuid", "deviceId", Arrays.asList("value"));
    }

    @Test
    public void testQueryBoundItemOfDevice() {
        // Setup
        final DstItemQueryReqDTO dstItemQueryReqDTO = new DstItemQueryReqDTO();
        dstItemQueryReqDTO.setStoreGuid("data");
        dstItemQueryReqDTO.setDeviceId("deviceId");

        final DstTypeBindRespDTO dstTypeBindRespDTO = new DstTypeBindRespDTO();
        dstTypeBindRespDTO.setTypeGuid("first");
        dstTypeBindRespDTO.setTypeName("itemName");
        final DstItemBindRespDTO dstItemBindRespDTO = new DstItemBindRespDTO();
        dstItemBindRespDTO.setItemGuid("first");
        dstItemBindRespDTO.setItemName("itemName");
        dstItemBindRespDTO.setPinyin("pinyin");
        dstItemBindRespDTO.setIsBoundBySelf(false);
        dstItemBindRespDTO.setIsBoundByOthers(false);
        final DstSkuBindRespDTO dstSkuBindRespDTO = new DstSkuBindRespDTO();
        dstSkuBindRespDTO.setSkuGuid("eDishSkuCode");
        dstSkuBindRespDTO.setSkuName("eDishSkuName");
        dstSkuBindRespDTO.setSkuCode("skuCode");
        final DstAreaBindRespDTO dstAreaBindRespDTO = new DstAreaBindRespDTO();
        dstAreaBindRespDTO.setDeviceId("deviceId");
        dstAreaBindRespDTO.setDeviceName("deviceName");
        dstAreaBindRespDTO.setAreaGuid("areaGuid");
        dstAreaBindRespDTO.setAreaName("areaName");
        dstSkuBindRespDTO.setAreas(Arrays.asList(dstAreaBindRespDTO));
        dstSkuBindRespDTO.setTimeout(0);
        dstSkuBindRespDTO.setIsBoundBySelf(false);
        dstSkuBindRespDTO.setIsBoundByOthers(false);
        dstItemBindRespDTO.setSkus(Arrays.asList(dstSkuBindRespDTO));
        dstTypeBindRespDTO.setItemList(Arrays.asList(dstItemBindRespDTO));
        final List<DstTypeBindRespDTO> expectedResult = Arrays.asList(dstTypeBindRespDTO);
        when(mockDeviceConfigService.getDeviceNameMapOfStore("data")).thenReturn(new HashMap<>());

        // Configure DistributeItemService.queryBoundItemOfStore(...).
        final DistributeItemDO distributeItemDO = new DistributeItemDO();
        distributeItemDO.setStoreGuid("storeGuid");
        distributeItemDO.setDeviceId("deviceId");
        distributeItemDO.setItemGuid("itemGuid");
        distributeItemDO.setSkuGuid("skuGuid");
        distributeItemDO.setSkuCode("skuCode");
        final List<DistributeItemDO> distributeItemDOS = Arrays.asList(distributeItemDO);
        when(mockDistributeItemService.queryBoundItemOfStore("data")).thenReturn(distributeItemDOS);

        // Configure ItemRpcService.kdsMapping(...).
        final MappingRespDTO mappingRespDTO = new MappingRespDTO();
        mappingRespDTO.setPinyin("pinyin");
        mappingRespDTO.seteDishSkuCode("eDishSkuCode");
        mappingRespDTO.setSkuCode("skuCode");
        mappingRespDTO.seteDishSkuName("eDishSkuName");
        mappingRespDTO.seteDishCode("eDishCode");
        mappingRespDTO.seteDishName("eDishName");
        mappingRespDTO.setCategoryName("categoryName");
        mappingRespDTO.setCategoryId("categoryId");
        final List<MappingRespDTO> mappingRespDTOS = Arrays.asList(mappingRespDTO);
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("data");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        itemSingleDTO.setSkuGuids(Arrays.asList("value"));
        when(mockItemRpcService.kdsMapping(itemSingleDTO)).thenReturn(mappingRespDTOS);

        // Configure TableRpcService.query(...).
        final AreaDTO areaDTO = new AreaDTO();
        areaDTO.setGuid("snack_area_guid");
        areaDTO.setStoreGuid("storeGuid");
        areaDTO.setStoreName("storeName");
        areaDTO.setAreaName("外卖");
        areaDTO.setSort(0);
        final List<AreaDTO> areaDTOS = Arrays.asList(areaDTO);
        when(mockTableRpcService.query("data")).thenReturn(areaDTOS);

        // Configure DistributeAreaService.queryBoundAreaOfStore(...).
        final DistributeAreaDO distributeAreaDO = new DistributeAreaDO();
        distributeAreaDO.setStoreGuid("storeGuid");
        distributeAreaDO.setDeviceId("deviceId");
        distributeAreaDO.setAreaGuid("areaGuid");
        final List<DistributeAreaDO> distributeAreaDOS = Arrays.asList(distributeAreaDO);
        when(mockDistributeAreaService.queryBoundAreaOfStore("data")).thenReturn(distributeAreaDOS);

        // Configure ItemConfigService.queryBatchByStoreGuid(...).
        final ItemConfigDO itemConfigDO = new ItemConfigDO();
        itemConfigDO.setStoreGuid("storeGuid");
        itemConfigDO.setSkuGuid("skuGuid");
        itemConfigDO.setTimeout(0);
        itemConfigDO.setMaxCopies(0);
        itemConfigDO.setDisplayType(0);
        final List<ItemConfigDO> itemConfigDOS = Arrays.asList(itemConfigDO);
        when(mockItemConfigService.queryBatchByStoreGuid("data")).thenReturn(itemConfigDOS);

        // Run the test
        final List<DstTypeBindRespDTO> result = distributeServiceImplUnderTest.queryBoundItemOfDevice(
                dstItemQueryReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryBoundItemOfDevice_DistributeItemServiceReturnsNoItems() {
        // Setup
        final DstItemQueryReqDTO dstItemQueryReqDTO = new DstItemQueryReqDTO();
        dstItemQueryReqDTO.setStoreGuid("data");
        dstItemQueryReqDTO.setDeviceId("deviceId");

        final DstTypeBindRespDTO dstTypeBindRespDTO = new DstTypeBindRespDTO();
        dstTypeBindRespDTO.setTypeGuid("first");
        dstTypeBindRespDTO.setTypeName("itemName");
        final DstItemBindRespDTO dstItemBindRespDTO = new DstItemBindRespDTO();
        dstItemBindRespDTO.setItemGuid("first");
        dstItemBindRespDTO.setItemName("itemName");
        dstItemBindRespDTO.setPinyin("pinyin");
        dstItemBindRespDTO.setIsBoundBySelf(false);
        dstItemBindRespDTO.setIsBoundByOthers(false);
        final DstSkuBindRespDTO dstSkuBindRespDTO = new DstSkuBindRespDTO();
        dstSkuBindRespDTO.setSkuGuid("eDishSkuCode");
        dstSkuBindRespDTO.setSkuName("eDishSkuName");
        dstSkuBindRespDTO.setSkuCode("skuCode");
        final DstAreaBindRespDTO dstAreaBindRespDTO = new DstAreaBindRespDTO();
        dstAreaBindRespDTO.setDeviceId("deviceId");
        dstAreaBindRespDTO.setDeviceName("deviceName");
        dstAreaBindRespDTO.setAreaGuid("areaGuid");
        dstAreaBindRespDTO.setAreaName("areaName");
        dstSkuBindRespDTO.setAreas(Arrays.asList(dstAreaBindRespDTO));
        dstSkuBindRespDTO.setTimeout(0);
        dstSkuBindRespDTO.setIsBoundBySelf(false);
        dstSkuBindRespDTO.setIsBoundByOthers(false);
        dstItemBindRespDTO.setSkus(Arrays.asList(dstSkuBindRespDTO));
        dstTypeBindRespDTO.setItemList(Arrays.asList(dstItemBindRespDTO));
        final List<DstTypeBindRespDTO> expectedResult = Arrays.asList(dstTypeBindRespDTO);
        when(mockDeviceConfigService.getDeviceNameMapOfStore("data")).thenReturn(new HashMap<>());
        when(mockDistributeItemService.queryBoundItemOfStore("data")).thenReturn(Collections.emptyList());

        // Configure ItemRpcService.kdsMapping(...).
        final MappingRespDTO mappingRespDTO = new MappingRespDTO();
        mappingRespDTO.setPinyin("pinyin");
        mappingRespDTO.seteDishSkuCode("eDishSkuCode");
        mappingRespDTO.setSkuCode("skuCode");
        mappingRespDTO.seteDishSkuName("eDishSkuName");
        mappingRespDTO.seteDishCode("eDishCode");
        mappingRespDTO.seteDishName("eDishName");
        mappingRespDTO.setCategoryName("categoryName");
        mappingRespDTO.setCategoryId("categoryId");
        final List<MappingRespDTO> mappingRespDTOS = Arrays.asList(mappingRespDTO);
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("data");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        itemSingleDTO.setSkuGuids(Arrays.asList("value"));
        when(mockItemRpcService.kdsMapping(itemSingleDTO)).thenReturn(mappingRespDTOS);

        // Configure TableRpcService.query(...).
        final AreaDTO areaDTO = new AreaDTO();
        areaDTO.setGuid("snack_area_guid");
        areaDTO.setStoreGuid("storeGuid");
        areaDTO.setStoreName("storeName");
        areaDTO.setAreaName("外卖");
        areaDTO.setSort(0);
        final List<AreaDTO> areaDTOS = Arrays.asList(areaDTO);
        when(mockTableRpcService.query("data")).thenReturn(areaDTOS);

        // Configure DistributeAreaService.queryBoundAreaOfStore(...).
        final DistributeAreaDO distributeAreaDO = new DistributeAreaDO();
        distributeAreaDO.setStoreGuid("storeGuid");
        distributeAreaDO.setDeviceId("deviceId");
        distributeAreaDO.setAreaGuid("areaGuid");
        final List<DistributeAreaDO> distributeAreaDOS = Arrays.asList(distributeAreaDO);
        when(mockDistributeAreaService.queryBoundAreaOfStore("data")).thenReturn(distributeAreaDOS);

        // Configure ItemConfigService.queryBatchByStoreGuid(...).
        final ItemConfigDO itemConfigDO = new ItemConfigDO();
        itemConfigDO.setStoreGuid("storeGuid");
        itemConfigDO.setSkuGuid("skuGuid");
        itemConfigDO.setTimeout(0);
        itemConfigDO.setMaxCopies(0);
        itemConfigDO.setDisplayType(0);
        final List<ItemConfigDO> itemConfigDOS = Arrays.asList(itemConfigDO);
        when(mockItemConfigService.queryBatchByStoreGuid("data")).thenReturn(itemConfigDOS);

        // Run the test
        final List<DstTypeBindRespDTO> result = distributeServiceImplUnderTest.queryBoundItemOfDevice(
                dstItemQueryReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryBoundItemOfDevice_ItemRpcServiceReturnsNoItems() {
        // Setup
        final DstItemQueryReqDTO dstItemQueryReqDTO = new DstItemQueryReqDTO();
        dstItemQueryReqDTO.setStoreGuid("data");
        dstItemQueryReqDTO.setDeviceId("deviceId");

        when(mockDeviceConfigService.getDeviceNameMapOfStore("data")).thenReturn(new HashMap<>());

        // Configure DistributeItemService.queryBoundItemOfStore(...).
        final DistributeItemDO distributeItemDO = new DistributeItemDO();
        distributeItemDO.setStoreGuid("storeGuid");
        distributeItemDO.setDeviceId("deviceId");
        distributeItemDO.setItemGuid("itemGuid");
        distributeItemDO.setSkuGuid("skuGuid");
        distributeItemDO.setSkuCode("skuCode");
        final List<DistributeItemDO> distributeItemDOS = Arrays.asList(distributeItemDO);
        when(mockDistributeItemService.queryBoundItemOfStore("data")).thenReturn(distributeItemDOS);

        // Configure ItemRpcService.kdsMapping(...).
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("data");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        itemSingleDTO.setSkuGuids(Arrays.asList("value"));
        when(mockItemRpcService.kdsMapping(itemSingleDTO)).thenReturn(Collections.emptyList());

        // Configure TableRpcService.query(...).
        final AreaDTO areaDTO = new AreaDTO();
        areaDTO.setGuid("snack_area_guid");
        areaDTO.setStoreGuid("storeGuid");
        areaDTO.setStoreName("storeName");
        areaDTO.setAreaName("外卖");
        areaDTO.setSort(0);
        final List<AreaDTO> areaDTOS = Arrays.asList(areaDTO);
        when(mockTableRpcService.query("data")).thenReturn(areaDTOS);

        // Configure DistributeAreaService.queryBoundAreaOfStore(...).
        final DistributeAreaDO distributeAreaDO = new DistributeAreaDO();
        distributeAreaDO.setStoreGuid("storeGuid");
        distributeAreaDO.setDeviceId("deviceId");
        distributeAreaDO.setAreaGuid("areaGuid");
        final List<DistributeAreaDO> distributeAreaDOS = Arrays.asList(distributeAreaDO);
        when(mockDistributeAreaService.queryBoundAreaOfStore("data")).thenReturn(distributeAreaDOS);

        // Configure ItemConfigService.queryBatchByStoreGuid(...).
        final ItemConfigDO itemConfigDO = new ItemConfigDO();
        itemConfigDO.setStoreGuid("storeGuid");
        itemConfigDO.setSkuGuid("skuGuid");
        itemConfigDO.setTimeout(0);
        itemConfigDO.setMaxCopies(0);
        itemConfigDO.setDisplayType(0);
        final List<ItemConfigDO> itemConfigDOS = Arrays.asList(itemConfigDO);
        when(mockItemConfigService.queryBatchByStoreGuid("data")).thenReturn(itemConfigDOS);

        // Run the test
        final List<DstTypeBindRespDTO> result = distributeServiceImplUnderTest.queryBoundItemOfDevice(
                dstItemQueryReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testQueryBoundItemOfDevice_TableRpcServiceReturnsNoItems() {
        // Setup
        final DstItemQueryReqDTO dstItemQueryReqDTO = new DstItemQueryReqDTO();
        dstItemQueryReqDTO.setStoreGuid("data");
        dstItemQueryReqDTO.setDeviceId("deviceId");

        final DstTypeBindRespDTO dstTypeBindRespDTO = new DstTypeBindRespDTO();
        dstTypeBindRespDTO.setTypeGuid("first");
        dstTypeBindRespDTO.setTypeName("itemName");
        final DstItemBindRespDTO dstItemBindRespDTO = new DstItemBindRespDTO();
        dstItemBindRespDTO.setItemGuid("first");
        dstItemBindRespDTO.setItemName("itemName");
        dstItemBindRespDTO.setPinyin("pinyin");
        dstItemBindRespDTO.setIsBoundBySelf(false);
        dstItemBindRespDTO.setIsBoundByOthers(false);
        final DstSkuBindRespDTO dstSkuBindRespDTO = new DstSkuBindRespDTO();
        dstSkuBindRespDTO.setSkuGuid("eDishSkuCode");
        dstSkuBindRespDTO.setSkuName("eDishSkuName");
        dstSkuBindRespDTO.setSkuCode("skuCode");
        final DstAreaBindRespDTO dstAreaBindRespDTO = new DstAreaBindRespDTO();
        dstAreaBindRespDTO.setDeviceId("deviceId");
        dstAreaBindRespDTO.setDeviceName("deviceName");
        dstAreaBindRespDTO.setAreaGuid("areaGuid");
        dstAreaBindRespDTO.setAreaName("areaName");
        dstSkuBindRespDTO.setAreas(Arrays.asList(dstAreaBindRespDTO));
        dstSkuBindRespDTO.setTimeout(0);
        dstSkuBindRespDTO.setIsBoundBySelf(false);
        dstSkuBindRespDTO.setIsBoundByOthers(false);
        dstItemBindRespDTO.setSkus(Arrays.asList(dstSkuBindRespDTO));
        dstTypeBindRespDTO.setItemList(Arrays.asList(dstItemBindRespDTO));
        final List<DstTypeBindRespDTO> expectedResult = Arrays.asList(dstTypeBindRespDTO);
        when(mockDeviceConfigService.getDeviceNameMapOfStore("data")).thenReturn(new HashMap<>());

        // Configure DistributeItemService.queryBoundItemOfStore(...).
        final DistributeItemDO distributeItemDO = new DistributeItemDO();
        distributeItemDO.setStoreGuid("storeGuid");
        distributeItemDO.setDeviceId("deviceId");
        distributeItemDO.setItemGuid("itemGuid");
        distributeItemDO.setSkuGuid("skuGuid");
        distributeItemDO.setSkuCode("skuCode");
        final List<DistributeItemDO> distributeItemDOS = Arrays.asList(distributeItemDO);
        when(mockDistributeItemService.queryBoundItemOfStore("data")).thenReturn(distributeItemDOS);

        // Configure ItemRpcService.kdsMapping(...).
        final MappingRespDTO mappingRespDTO = new MappingRespDTO();
        mappingRespDTO.setPinyin("pinyin");
        mappingRespDTO.seteDishSkuCode("eDishSkuCode");
        mappingRespDTO.setSkuCode("skuCode");
        mappingRespDTO.seteDishSkuName("eDishSkuName");
        mappingRespDTO.seteDishCode("eDishCode");
        mappingRespDTO.seteDishName("eDishName");
        mappingRespDTO.setCategoryName("categoryName");
        mappingRespDTO.setCategoryId("categoryId");
        final List<MappingRespDTO> mappingRespDTOS = Arrays.asList(mappingRespDTO);
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("data");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        itemSingleDTO.setSkuGuids(Arrays.asList("value"));
        when(mockItemRpcService.kdsMapping(itemSingleDTO)).thenReturn(mappingRespDTOS);

        when(mockTableRpcService.query("data")).thenReturn(Collections.emptyList());

        // Configure DistributeAreaService.queryBoundAreaOfStore(...).
        final DistributeAreaDO distributeAreaDO = new DistributeAreaDO();
        distributeAreaDO.setStoreGuid("storeGuid");
        distributeAreaDO.setDeviceId("deviceId");
        distributeAreaDO.setAreaGuid("areaGuid");
        final List<DistributeAreaDO> distributeAreaDOS = Arrays.asList(distributeAreaDO);
        when(mockDistributeAreaService.queryBoundAreaOfStore("data")).thenReturn(distributeAreaDOS);

        // Configure ItemConfigService.queryBatchByStoreGuid(...).
        final ItemConfigDO itemConfigDO = new ItemConfigDO();
        itemConfigDO.setStoreGuid("storeGuid");
        itemConfigDO.setSkuGuid("skuGuid");
        itemConfigDO.setTimeout(0);
        itemConfigDO.setMaxCopies(0);
        itemConfigDO.setDisplayType(0);
        final List<ItemConfigDO> itemConfigDOS = Arrays.asList(itemConfigDO);
        when(mockItemConfigService.queryBatchByStoreGuid("data")).thenReturn(itemConfigDOS);

        // Run the test
        final List<DstTypeBindRespDTO> result = distributeServiceImplUnderTest.queryBoundItemOfDevice(
                dstItemQueryReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryBoundItemOfDevice_DistributeAreaServiceReturnsNoItems() {
        // Setup
        final DstItemQueryReqDTO dstItemQueryReqDTO = new DstItemQueryReqDTO();
        dstItemQueryReqDTO.setStoreGuid("data");
        dstItemQueryReqDTO.setDeviceId("deviceId");

        final DstTypeBindRespDTO dstTypeBindRespDTO = new DstTypeBindRespDTO();
        dstTypeBindRespDTO.setTypeGuid("first");
        dstTypeBindRespDTO.setTypeName("itemName");
        final DstItemBindRespDTO dstItemBindRespDTO = new DstItemBindRespDTO();
        dstItemBindRespDTO.setItemGuid("first");
        dstItemBindRespDTO.setItemName("itemName");
        dstItemBindRespDTO.setPinyin("pinyin");
        dstItemBindRespDTO.setIsBoundBySelf(false);
        dstItemBindRespDTO.setIsBoundByOthers(false);
        final DstSkuBindRespDTO dstSkuBindRespDTO = new DstSkuBindRespDTO();
        dstSkuBindRespDTO.setSkuGuid("eDishSkuCode");
        dstSkuBindRespDTO.setSkuName("eDishSkuName");
        dstSkuBindRespDTO.setSkuCode("skuCode");
        final DstAreaBindRespDTO dstAreaBindRespDTO = new DstAreaBindRespDTO();
        dstAreaBindRespDTO.setDeviceId("deviceId");
        dstAreaBindRespDTO.setDeviceName("deviceName");
        dstAreaBindRespDTO.setAreaGuid("areaGuid");
        dstAreaBindRespDTO.setAreaName("areaName");
        dstSkuBindRespDTO.setAreas(Arrays.asList(dstAreaBindRespDTO));
        dstSkuBindRespDTO.setTimeout(0);
        dstSkuBindRespDTO.setIsBoundBySelf(false);
        dstSkuBindRespDTO.setIsBoundByOthers(false);
        dstItemBindRespDTO.setSkus(Arrays.asList(dstSkuBindRespDTO));
        dstTypeBindRespDTO.setItemList(Arrays.asList(dstItemBindRespDTO));
        final List<DstTypeBindRespDTO> expectedResult = Arrays.asList(dstTypeBindRespDTO);
        when(mockDeviceConfigService.getDeviceNameMapOfStore("data")).thenReturn(new HashMap<>());

        // Configure DistributeItemService.queryBoundItemOfStore(...).
        final DistributeItemDO distributeItemDO = new DistributeItemDO();
        distributeItemDO.setStoreGuid("storeGuid");
        distributeItemDO.setDeviceId("deviceId");
        distributeItemDO.setItemGuid("itemGuid");
        distributeItemDO.setSkuGuid("skuGuid");
        distributeItemDO.setSkuCode("skuCode");
        final List<DistributeItemDO> distributeItemDOS = Arrays.asList(distributeItemDO);
        when(mockDistributeItemService.queryBoundItemOfStore("data")).thenReturn(distributeItemDOS);

        // Configure ItemRpcService.kdsMapping(...).
        final MappingRespDTO mappingRespDTO = new MappingRespDTO();
        mappingRespDTO.setPinyin("pinyin");
        mappingRespDTO.seteDishSkuCode("eDishSkuCode");
        mappingRespDTO.setSkuCode("skuCode");
        mappingRespDTO.seteDishSkuName("eDishSkuName");
        mappingRespDTO.seteDishCode("eDishCode");
        mappingRespDTO.seteDishName("eDishName");
        mappingRespDTO.setCategoryName("categoryName");
        mappingRespDTO.setCategoryId("categoryId");
        final List<MappingRespDTO> mappingRespDTOS = Arrays.asList(mappingRespDTO);
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("data");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        itemSingleDTO.setSkuGuids(Arrays.asList("value"));
        when(mockItemRpcService.kdsMapping(itemSingleDTO)).thenReturn(mappingRespDTOS);

        // Configure TableRpcService.query(...).
        final AreaDTO areaDTO = new AreaDTO();
        areaDTO.setGuid("snack_area_guid");
        areaDTO.setStoreGuid("storeGuid");
        areaDTO.setStoreName("storeName");
        areaDTO.setAreaName("外卖");
        areaDTO.setSort(0);
        final List<AreaDTO> areaDTOS = Arrays.asList(areaDTO);
        when(mockTableRpcService.query("data")).thenReturn(areaDTOS);

        when(mockDistributeAreaService.queryBoundAreaOfStore("data")).thenReturn(Collections.emptyList());

        // Configure ItemConfigService.queryBatchByStoreGuid(...).
        final ItemConfigDO itemConfigDO = new ItemConfigDO();
        itemConfigDO.setStoreGuid("storeGuid");
        itemConfigDO.setSkuGuid("skuGuid");
        itemConfigDO.setTimeout(0);
        itemConfigDO.setMaxCopies(0);
        itemConfigDO.setDisplayType(0);
        final List<ItemConfigDO> itemConfigDOS = Arrays.asList(itemConfigDO);
        when(mockItemConfigService.queryBatchByStoreGuid("data")).thenReturn(itemConfigDOS);

        // Run the test
        final List<DstTypeBindRespDTO> result = distributeServiceImplUnderTest.queryBoundItemOfDevice(
                dstItemQueryReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryBoundItemOfDevice_ItemConfigServiceReturnsNoItems() {
        // Setup
        final DstItemQueryReqDTO dstItemQueryReqDTO = new DstItemQueryReqDTO();
        dstItemQueryReqDTO.setStoreGuid("data");
        dstItemQueryReqDTO.setDeviceId("deviceId");

        final DstTypeBindRespDTO dstTypeBindRespDTO = new DstTypeBindRespDTO();
        dstTypeBindRespDTO.setTypeGuid("first");
        dstTypeBindRespDTO.setTypeName("itemName");
        final DstItemBindRespDTO dstItemBindRespDTO = new DstItemBindRespDTO();
        dstItemBindRespDTO.setItemGuid("first");
        dstItemBindRespDTO.setItemName("itemName");
        dstItemBindRespDTO.setPinyin("pinyin");
        dstItemBindRespDTO.setIsBoundBySelf(false);
        dstItemBindRespDTO.setIsBoundByOthers(false);
        final DstSkuBindRespDTO dstSkuBindRespDTO = new DstSkuBindRespDTO();
        dstSkuBindRespDTO.setSkuGuid("eDishSkuCode");
        dstSkuBindRespDTO.setSkuName("eDishSkuName");
        dstSkuBindRespDTO.setSkuCode("skuCode");
        final DstAreaBindRespDTO dstAreaBindRespDTO = new DstAreaBindRespDTO();
        dstAreaBindRespDTO.setDeviceId("deviceId");
        dstAreaBindRespDTO.setDeviceName("deviceName");
        dstAreaBindRespDTO.setAreaGuid("areaGuid");
        dstAreaBindRespDTO.setAreaName("areaName");
        dstSkuBindRespDTO.setAreas(Arrays.asList(dstAreaBindRespDTO));
        dstSkuBindRespDTO.setTimeout(0);
        dstSkuBindRespDTO.setIsBoundBySelf(false);
        dstSkuBindRespDTO.setIsBoundByOthers(false);
        dstItemBindRespDTO.setSkus(Arrays.asList(dstSkuBindRespDTO));
        dstTypeBindRespDTO.setItemList(Arrays.asList(dstItemBindRespDTO));
        final List<DstTypeBindRespDTO> expectedResult = Arrays.asList(dstTypeBindRespDTO);
        when(mockDeviceConfigService.getDeviceNameMapOfStore("data")).thenReturn(new HashMap<>());

        // Configure DistributeItemService.queryBoundItemOfStore(...).
        final DistributeItemDO distributeItemDO = new DistributeItemDO();
        distributeItemDO.setStoreGuid("storeGuid");
        distributeItemDO.setDeviceId("deviceId");
        distributeItemDO.setItemGuid("itemGuid");
        distributeItemDO.setSkuGuid("skuGuid");
        distributeItemDO.setSkuCode("skuCode");
        final List<DistributeItemDO> distributeItemDOS = Arrays.asList(distributeItemDO);
        when(mockDistributeItemService.queryBoundItemOfStore("data")).thenReturn(distributeItemDOS);

        // Configure ItemRpcService.kdsMapping(...).
        final MappingRespDTO mappingRespDTO = new MappingRespDTO();
        mappingRespDTO.setPinyin("pinyin");
        mappingRespDTO.seteDishSkuCode("eDishSkuCode");
        mappingRespDTO.setSkuCode("skuCode");
        mappingRespDTO.seteDishSkuName("eDishSkuName");
        mappingRespDTO.seteDishCode("eDishCode");
        mappingRespDTO.seteDishName("eDishName");
        mappingRespDTO.setCategoryName("categoryName");
        mappingRespDTO.setCategoryId("categoryId");
        final List<MappingRespDTO> mappingRespDTOS = Arrays.asList(mappingRespDTO);
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("data");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        itemSingleDTO.setSkuGuids(Arrays.asList("value"));
        when(mockItemRpcService.kdsMapping(itemSingleDTO)).thenReturn(mappingRespDTOS);

        // Configure TableRpcService.query(...).
        final AreaDTO areaDTO = new AreaDTO();
        areaDTO.setGuid("snack_area_guid");
        areaDTO.setStoreGuid("storeGuid");
        areaDTO.setStoreName("storeName");
        areaDTO.setAreaName("外卖");
        areaDTO.setSort(0);
        final List<AreaDTO> areaDTOS = Arrays.asList(areaDTO);
        when(mockTableRpcService.query("data")).thenReturn(areaDTOS);

        // Configure DistributeAreaService.queryBoundAreaOfStore(...).
        final DistributeAreaDO distributeAreaDO = new DistributeAreaDO();
        distributeAreaDO.setStoreGuid("storeGuid");
        distributeAreaDO.setDeviceId("deviceId");
        distributeAreaDO.setAreaGuid("areaGuid");
        final List<DistributeAreaDO> distributeAreaDOS = Arrays.asList(distributeAreaDO);
        when(mockDistributeAreaService.queryBoundAreaOfStore("data")).thenReturn(distributeAreaDOS);

        when(mockItemConfigService.queryBatchByStoreGuid("data")).thenReturn(Collections.emptyList());

        // Run the test
        final List<DstTypeBindRespDTO> result = distributeServiceImplUnderTest.queryBoundItemOfDevice(
                dstItemQueryReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryAllItemOfStore() {
        // Setup
        final DstItemQueryReqDTO dstItemQueryReqDTO = new DstItemQueryReqDTO();
        dstItemQueryReqDTO.setStoreGuid("data");
        dstItemQueryReqDTO.setDeviceId("deviceId");

        final DstTypeBindRespDTO dstTypeBindRespDTO = new DstTypeBindRespDTO();
        dstTypeBindRespDTO.setTypeGuid("first");
        dstTypeBindRespDTO.setTypeName("itemName");
        final DstItemBindRespDTO dstItemBindRespDTO = new DstItemBindRespDTO();
        dstItemBindRespDTO.setItemGuid("first");
        dstItemBindRespDTO.setItemName("itemName");
        dstItemBindRespDTO.setPinyin("pinyin");
        dstItemBindRespDTO.setIsBoundBySelf(false);
        dstItemBindRespDTO.setIsBoundByOthers(false);
        final DstSkuBindRespDTO dstSkuBindRespDTO = new DstSkuBindRespDTO();
        dstSkuBindRespDTO.setSkuGuid("eDishSkuCode");
        dstSkuBindRespDTO.setSkuName("eDishSkuName");
        dstSkuBindRespDTO.setSkuCode("skuCode");
        final DstAreaBindRespDTO dstAreaBindRespDTO = new DstAreaBindRespDTO();
        dstAreaBindRespDTO.setDeviceId("deviceId");
        dstAreaBindRespDTO.setDeviceName("deviceName");
        dstAreaBindRespDTO.setAreaGuid("areaGuid");
        dstAreaBindRespDTO.setAreaName("areaName");
        dstSkuBindRespDTO.setAreas(Arrays.asList(dstAreaBindRespDTO));
        dstSkuBindRespDTO.setTimeout(0);
        dstSkuBindRespDTO.setIsBoundBySelf(false);
        dstSkuBindRespDTO.setIsBoundByOthers(false);
        dstItemBindRespDTO.setSkus(Arrays.asList(dstSkuBindRespDTO));
        dstTypeBindRespDTO.setItemList(Arrays.asList(dstItemBindRespDTO));
        final List<DstTypeBindRespDTO> expectedResult = Arrays.asList(dstTypeBindRespDTO);
        when(mockDeviceConfigService.getDeviceNameMapOfStore("data")).thenReturn(new HashMap<>());

        // Configure DistributeItemService.queryBoundItemOfStore(...).
        final DistributeItemDO distributeItemDO = new DistributeItemDO();
        distributeItemDO.setStoreGuid("storeGuid");
        distributeItemDO.setDeviceId("deviceId");
        distributeItemDO.setItemGuid("itemGuid");
        distributeItemDO.setSkuGuid("skuGuid");
        distributeItemDO.setSkuCode("skuCode");
        final List<DistributeItemDO> distributeItemDOS = Arrays.asList(distributeItemDO);
        when(mockDistributeItemService.queryBoundItemOfStore("data")).thenReturn(distributeItemDOS);

        // Configure ItemRpcService.kdsMapping(...).
        final MappingRespDTO mappingRespDTO = new MappingRespDTO();
        mappingRespDTO.setPinyin("pinyin");
        mappingRespDTO.seteDishSkuCode("eDishSkuCode");
        mappingRespDTO.setSkuCode("skuCode");
        mappingRespDTO.seteDishSkuName("eDishSkuName");
        mappingRespDTO.seteDishCode("eDishCode");
        mappingRespDTO.seteDishName("eDishName");
        mappingRespDTO.setCategoryName("categoryName");
        mappingRespDTO.setCategoryId("categoryId");
        final List<MappingRespDTO> mappingRespDTOS = Arrays.asList(mappingRespDTO);
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("data");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        itemSingleDTO.setSkuGuids(Arrays.asList("value"));
        when(mockItemRpcService.kdsMapping(itemSingleDTO)).thenReturn(mappingRespDTOS);

        // Configure TableRpcService.query(...).
        final AreaDTO areaDTO = new AreaDTO();
        areaDTO.setGuid("snack_area_guid");
        areaDTO.setStoreGuid("storeGuid");
        areaDTO.setStoreName("storeName");
        areaDTO.setAreaName("外卖");
        areaDTO.setSort(0);
        final List<AreaDTO> areaDTOS = Arrays.asList(areaDTO);
        when(mockTableRpcService.query("data")).thenReturn(areaDTOS);

        // Configure DistributeAreaService.queryBoundAreaOfStore(...).
        final DistributeAreaDO distributeAreaDO = new DistributeAreaDO();
        distributeAreaDO.setStoreGuid("storeGuid");
        distributeAreaDO.setDeviceId("deviceId");
        distributeAreaDO.setAreaGuid("areaGuid");
        final List<DistributeAreaDO> distributeAreaDOS = Arrays.asList(distributeAreaDO);
        when(mockDistributeAreaService.queryBoundAreaOfStore("data")).thenReturn(distributeAreaDOS);

        // Configure ItemConfigService.queryBatchByStoreGuid(...).
        final ItemConfigDO itemConfigDO = new ItemConfigDO();
        itemConfigDO.setStoreGuid("storeGuid");
        itemConfigDO.setSkuGuid("skuGuid");
        itemConfigDO.setTimeout(0);
        itemConfigDO.setMaxCopies(0);
        itemConfigDO.setDisplayType(0);
        final List<ItemConfigDO> itemConfigDOS = Arrays.asList(itemConfigDO);
        when(mockItemConfigService.queryBatchByStoreGuid("data")).thenReturn(itemConfigDOS);

        // Run the test
        final List<DstTypeBindRespDTO> result = distributeServiceImplUnderTest.queryAllItemOfStore(dstItemQueryReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryAllItemOfStore_DistributeItemServiceReturnsNoItems() {
        // Setup
        final DstItemQueryReqDTO dstItemQueryReqDTO = new DstItemQueryReqDTO();
        dstItemQueryReqDTO.setStoreGuid("data");
        dstItemQueryReqDTO.setDeviceId("deviceId");

        final DstTypeBindRespDTO dstTypeBindRespDTO = new DstTypeBindRespDTO();
        dstTypeBindRespDTO.setTypeGuid("first");
        dstTypeBindRespDTO.setTypeName("itemName");
        final DstItemBindRespDTO dstItemBindRespDTO = new DstItemBindRespDTO();
        dstItemBindRespDTO.setItemGuid("first");
        dstItemBindRespDTO.setItemName("itemName");
        dstItemBindRespDTO.setPinyin("pinyin");
        dstItemBindRespDTO.setIsBoundBySelf(false);
        dstItemBindRespDTO.setIsBoundByOthers(false);
        final DstSkuBindRespDTO dstSkuBindRespDTO = new DstSkuBindRespDTO();
        dstSkuBindRespDTO.setSkuGuid("eDishSkuCode");
        dstSkuBindRespDTO.setSkuName("eDishSkuName");
        dstSkuBindRespDTO.setSkuCode("skuCode");
        final DstAreaBindRespDTO dstAreaBindRespDTO = new DstAreaBindRespDTO();
        dstAreaBindRespDTO.setDeviceId("deviceId");
        dstAreaBindRespDTO.setDeviceName("deviceName");
        dstAreaBindRespDTO.setAreaGuid("areaGuid");
        dstAreaBindRespDTO.setAreaName("areaName");
        dstSkuBindRespDTO.setAreas(Arrays.asList(dstAreaBindRespDTO));
        dstSkuBindRespDTO.setTimeout(0);
        dstSkuBindRespDTO.setIsBoundBySelf(false);
        dstSkuBindRespDTO.setIsBoundByOthers(false);
        dstItemBindRespDTO.setSkus(Arrays.asList(dstSkuBindRespDTO));
        dstTypeBindRespDTO.setItemList(Arrays.asList(dstItemBindRespDTO));
        final List<DstTypeBindRespDTO> expectedResult = Arrays.asList(dstTypeBindRespDTO);
        when(mockDeviceConfigService.getDeviceNameMapOfStore("data")).thenReturn(new HashMap<>());
        when(mockDistributeItemService.queryBoundItemOfStore("data")).thenReturn(Collections.emptyList());

        // Configure ItemRpcService.kdsMapping(...).
        final MappingRespDTO mappingRespDTO = new MappingRespDTO();
        mappingRespDTO.setPinyin("pinyin");
        mappingRespDTO.seteDishSkuCode("eDishSkuCode");
        mappingRespDTO.setSkuCode("skuCode");
        mappingRespDTO.seteDishSkuName("eDishSkuName");
        mappingRespDTO.seteDishCode("eDishCode");
        mappingRespDTO.seteDishName("eDishName");
        mappingRespDTO.setCategoryName("categoryName");
        mappingRespDTO.setCategoryId("categoryId");
        final List<MappingRespDTO> mappingRespDTOS = Arrays.asList(mappingRespDTO);
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("data");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        itemSingleDTO.setSkuGuids(Arrays.asList("value"));
        when(mockItemRpcService.kdsMapping(itemSingleDTO)).thenReturn(mappingRespDTOS);

        // Configure TableRpcService.query(...).
        final AreaDTO areaDTO = new AreaDTO();
        areaDTO.setGuid("snack_area_guid");
        areaDTO.setStoreGuid("storeGuid");
        areaDTO.setStoreName("storeName");
        areaDTO.setAreaName("外卖");
        areaDTO.setSort(0);
        final List<AreaDTO> areaDTOS = Arrays.asList(areaDTO);
        when(mockTableRpcService.query("data")).thenReturn(areaDTOS);

        // Configure DistributeAreaService.queryBoundAreaOfStore(...).
        final DistributeAreaDO distributeAreaDO = new DistributeAreaDO();
        distributeAreaDO.setStoreGuid("storeGuid");
        distributeAreaDO.setDeviceId("deviceId");
        distributeAreaDO.setAreaGuid("areaGuid");
        final List<DistributeAreaDO> distributeAreaDOS = Arrays.asList(distributeAreaDO);
        when(mockDistributeAreaService.queryBoundAreaOfStore("data")).thenReturn(distributeAreaDOS);

        // Configure ItemConfigService.queryBatchByStoreGuid(...).
        final ItemConfigDO itemConfigDO = new ItemConfigDO();
        itemConfigDO.setStoreGuid("storeGuid");
        itemConfigDO.setSkuGuid("skuGuid");
        itemConfigDO.setTimeout(0);
        itemConfigDO.setMaxCopies(0);
        itemConfigDO.setDisplayType(0);
        final List<ItemConfigDO> itemConfigDOS = Arrays.asList(itemConfigDO);
        when(mockItemConfigService.queryBatchByStoreGuid("data")).thenReturn(itemConfigDOS);

        // Run the test
        final List<DstTypeBindRespDTO> result = distributeServiceImplUnderTest.queryAllItemOfStore(dstItemQueryReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryAllItemOfStore_ItemRpcServiceReturnsNoItems() {
        // Setup
        final DstItemQueryReqDTO dstItemQueryReqDTO = new DstItemQueryReqDTO();
        dstItemQueryReqDTO.setStoreGuid("data");
        dstItemQueryReqDTO.setDeviceId("deviceId");

        when(mockDeviceConfigService.getDeviceNameMapOfStore("data")).thenReturn(new HashMap<>());

        // Configure DistributeItemService.queryBoundItemOfStore(...).
        final DistributeItemDO distributeItemDO = new DistributeItemDO();
        distributeItemDO.setStoreGuid("storeGuid");
        distributeItemDO.setDeviceId("deviceId");
        distributeItemDO.setItemGuid("itemGuid");
        distributeItemDO.setSkuGuid("skuGuid");
        distributeItemDO.setSkuCode("skuCode");
        final List<DistributeItemDO> distributeItemDOS = Arrays.asList(distributeItemDO);
        when(mockDistributeItemService.queryBoundItemOfStore("data")).thenReturn(distributeItemDOS);

        // Configure ItemRpcService.kdsMapping(...).
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("data");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        itemSingleDTO.setSkuGuids(Arrays.asList("value"));
        when(mockItemRpcService.kdsMapping(itemSingleDTO)).thenReturn(Collections.emptyList());

        // Configure TableRpcService.query(...).
        final AreaDTO areaDTO = new AreaDTO();
        areaDTO.setGuid("snack_area_guid");
        areaDTO.setStoreGuid("storeGuid");
        areaDTO.setStoreName("storeName");
        areaDTO.setAreaName("外卖");
        areaDTO.setSort(0);
        final List<AreaDTO> areaDTOS = Arrays.asList(areaDTO);
        when(mockTableRpcService.query("data")).thenReturn(areaDTOS);

        // Configure DistributeAreaService.queryBoundAreaOfStore(...).
        final DistributeAreaDO distributeAreaDO = new DistributeAreaDO();
        distributeAreaDO.setStoreGuid("storeGuid");
        distributeAreaDO.setDeviceId("deviceId");
        distributeAreaDO.setAreaGuid("areaGuid");
        final List<DistributeAreaDO> distributeAreaDOS = Arrays.asList(distributeAreaDO);
        when(mockDistributeAreaService.queryBoundAreaOfStore("data")).thenReturn(distributeAreaDOS);

        // Configure ItemConfigService.queryBatchByStoreGuid(...).
        final ItemConfigDO itemConfigDO = new ItemConfigDO();
        itemConfigDO.setStoreGuid("storeGuid");
        itemConfigDO.setSkuGuid("skuGuid");
        itemConfigDO.setTimeout(0);
        itemConfigDO.setMaxCopies(0);
        itemConfigDO.setDisplayType(0);
        final List<ItemConfigDO> itemConfigDOS = Arrays.asList(itemConfigDO);
        when(mockItemConfigService.queryBatchByStoreGuid("data")).thenReturn(itemConfigDOS);

        // Run the test
        final List<DstTypeBindRespDTO> result = distributeServiceImplUnderTest.queryAllItemOfStore(dstItemQueryReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testQueryAllItemOfStore_TableRpcServiceReturnsNoItems() {
        // Setup
        final DstItemQueryReqDTO dstItemQueryReqDTO = new DstItemQueryReqDTO();
        dstItemQueryReqDTO.setStoreGuid("data");
        dstItemQueryReqDTO.setDeviceId("deviceId");

        final DstTypeBindRespDTO dstTypeBindRespDTO = new DstTypeBindRespDTO();
        dstTypeBindRespDTO.setTypeGuid("first");
        dstTypeBindRespDTO.setTypeName("itemName");
        final DstItemBindRespDTO dstItemBindRespDTO = new DstItemBindRespDTO();
        dstItemBindRespDTO.setItemGuid("first");
        dstItemBindRespDTO.setItemName("itemName");
        dstItemBindRespDTO.setPinyin("pinyin");
        dstItemBindRespDTO.setIsBoundBySelf(false);
        dstItemBindRespDTO.setIsBoundByOthers(false);
        final DstSkuBindRespDTO dstSkuBindRespDTO = new DstSkuBindRespDTO();
        dstSkuBindRespDTO.setSkuGuid("eDishSkuCode");
        dstSkuBindRespDTO.setSkuName("eDishSkuName");
        dstSkuBindRespDTO.setSkuCode("skuCode");
        final DstAreaBindRespDTO dstAreaBindRespDTO = new DstAreaBindRespDTO();
        dstAreaBindRespDTO.setDeviceId("deviceId");
        dstAreaBindRespDTO.setDeviceName("deviceName");
        dstAreaBindRespDTO.setAreaGuid("areaGuid");
        dstAreaBindRespDTO.setAreaName("areaName");
        dstSkuBindRespDTO.setAreas(Arrays.asList(dstAreaBindRespDTO));
        dstSkuBindRespDTO.setTimeout(0);
        dstSkuBindRespDTO.setIsBoundBySelf(false);
        dstSkuBindRespDTO.setIsBoundByOthers(false);
        dstItemBindRespDTO.setSkus(Arrays.asList(dstSkuBindRespDTO));
        dstTypeBindRespDTO.setItemList(Arrays.asList(dstItemBindRespDTO));
        final List<DstTypeBindRespDTO> expectedResult = Arrays.asList(dstTypeBindRespDTO);
        when(mockDeviceConfigService.getDeviceNameMapOfStore("data")).thenReturn(new HashMap<>());

        // Configure DistributeItemService.queryBoundItemOfStore(...).
        final DistributeItemDO distributeItemDO = new DistributeItemDO();
        distributeItemDO.setStoreGuid("storeGuid");
        distributeItemDO.setDeviceId("deviceId");
        distributeItemDO.setItemGuid("itemGuid");
        distributeItemDO.setSkuGuid("skuGuid");
        distributeItemDO.setSkuCode("skuCode");
        final List<DistributeItemDO> distributeItemDOS = Arrays.asList(distributeItemDO);
        when(mockDistributeItemService.queryBoundItemOfStore("data")).thenReturn(distributeItemDOS);

        // Configure ItemRpcService.kdsMapping(...).
        final MappingRespDTO mappingRespDTO = new MappingRespDTO();
        mappingRespDTO.setPinyin("pinyin");
        mappingRespDTO.seteDishSkuCode("eDishSkuCode");
        mappingRespDTO.setSkuCode("skuCode");
        mappingRespDTO.seteDishSkuName("eDishSkuName");
        mappingRespDTO.seteDishCode("eDishCode");
        mappingRespDTO.seteDishName("eDishName");
        mappingRespDTO.setCategoryName("categoryName");
        mappingRespDTO.setCategoryId("categoryId");
        final List<MappingRespDTO> mappingRespDTOS = Arrays.asList(mappingRespDTO);
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("data");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        itemSingleDTO.setSkuGuids(Arrays.asList("value"));
        when(mockItemRpcService.kdsMapping(itemSingleDTO)).thenReturn(mappingRespDTOS);

        when(mockTableRpcService.query("data")).thenReturn(Collections.emptyList());

        // Configure DistributeAreaService.queryBoundAreaOfStore(...).
        final DistributeAreaDO distributeAreaDO = new DistributeAreaDO();
        distributeAreaDO.setStoreGuid("storeGuid");
        distributeAreaDO.setDeviceId("deviceId");
        distributeAreaDO.setAreaGuid("areaGuid");
        final List<DistributeAreaDO> distributeAreaDOS = Arrays.asList(distributeAreaDO);
        when(mockDistributeAreaService.queryBoundAreaOfStore("data")).thenReturn(distributeAreaDOS);

        // Configure ItemConfigService.queryBatchByStoreGuid(...).
        final ItemConfigDO itemConfigDO = new ItemConfigDO();
        itemConfigDO.setStoreGuid("storeGuid");
        itemConfigDO.setSkuGuid("skuGuid");
        itemConfigDO.setTimeout(0);
        itemConfigDO.setMaxCopies(0);
        itemConfigDO.setDisplayType(0);
        final List<ItemConfigDO> itemConfigDOS = Arrays.asList(itemConfigDO);
        when(mockItemConfigService.queryBatchByStoreGuid("data")).thenReturn(itemConfigDOS);

        // Run the test
        final List<DstTypeBindRespDTO> result = distributeServiceImplUnderTest.queryAllItemOfStore(dstItemQueryReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryAllItemOfStore_DistributeAreaServiceReturnsNoItems() {
        // Setup
        final DstItemQueryReqDTO dstItemQueryReqDTO = new DstItemQueryReqDTO();
        dstItemQueryReqDTO.setStoreGuid("data");
        dstItemQueryReqDTO.setDeviceId("deviceId");

        final DstTypeBindRespDTO dstTypeBindRespDTO = new DstTypeBindRespDTO();
        dstTypeBindRespDTO.setTypeGuid("first");
        dstTypeBindRespDTO.setTypeName("itemName");
        final DstItemBindRespDTO dstItemBindRespDTO = new DstItemBindRespDTO();
        dstItemBindRespDTO.setItemGuid("first");
        dstItemBindRespDTO.setItemName("itemName");
        dstItemBindRespDTO.setPinyin("pinyin");
        dstItemBindRespDTO.setIsBoundBySelf(false);
        dstItemBindRespDTO.setIsBoundByOthers(false);
        final DstSkuBindRespDTO dstSkuBindRespDTO = new DstSkuBindRespDTO();
        dstSkuBindRespDTO.setSkuGuid("eDishSkuCode");
        dstSkuBindRespDTO.setSkuName("eDishSkuName");
        dstSkuBindRespDTO.setSkuCode("skuCode");
        final DstAreaBindRespDTO dstAreaBindRespDTO = new DstAreaBindRespDTO();
        dstAreaBindRespDTO.setDeviceId("deviceId");
        dstAreaBindRespDTO.setDeviceName("deviceName");
        dstAreaBindRespDTO.setAreaGuid("areaGuid");
        dstAreaBindRespDTO.setAreaName("areaName");
        dstSkuBindRespDTO.setAreas(Arrays.asList(dstAreaBindRespDTO));
        dstSkuBindRespDTO.setTimeout(0);
        dstSkuBindRespDTO.setIsBoundBySelf(false);
        dstSkuBindRespDTO.setIsBoundByOthers(false);
        dstItemBindRespDTO.setSkus(Arrays.asList(dstSkuBindRespDTO));
        dstTypeBindRespDTO.setItemList(Arrays.asList(dstItemBindRespDTO));
        final List<DstTypeBindRespDTO> expectedResult = Arrays.asList(dstTypeBindRespDTO);
        when(mockDeviceConfigService.getDeviceNameMapOfStore("data")).thenReturn(new HashMap<>());

        // Configure DistributeItemService.queryBoundItemOfStore(...).
        final DistributeItemDO distributeItemDO = new DistributeItemDO();
        distributeItemDO.setStoreGuid("storeGuid");
        distributeItemDO.setDeviceId("deviceId");
        distributeItemDO.setItemGuid("itemGuid");
        distributeItemDO.setSkuGuid("skuGuid");
        distributeItemDO.setSkuCode("skuCode");
        final List<DistributeItemDO> distributeItemDOS = Arrays.asList(distributeItemDO);
        when(mockDistributeItemService.queryBoundItemOfStore("data")).thenReturn(distributeItemDOS);

        // Configure ItemRpcService.kdsMapping(...).
        final MappingRespDTO mappingRespDTO = new MappingRespDTO();
        mappingRespDTO.setPinyin("pinyin");
        mappingRespDTO.seteDishSkuCode("eDishSkuCode");
        mappingRespDTO.setSkuCode("skuCode");
        mappingRespDTO.seteDishSkuName("eDishSkuName");
        mappingRespDTO.seteDishCode("eDishCode");
        mappingRespDTO.seteDishName("eDishName");
        mappingRespDTO.setCategoryName("categoryName");
        mappingRespDTO.setCategoryId("categoryId");
        final List<MappingRespDTO> mappingRespDTOS = Arrays.asList(mappingRespDTO);
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("data");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        itemSingleDTO.setSkuGuids(Arrays.asList("value"));
        when(mockItemRpcService.kdsMapping(itemSingleDTO)).thenReturn(mappingRespDTOS);

        // Configure TableRpcService.query(...).
        final AreaDTO areaDTO = new AreaDTO();
        areaDTO.setGuid("snack_area_guid");
        areaDTO.setStoreGuid("storeGuid");
        areaDTO.setStoreName("storeName");
        areaDTO.setAreaName("外卖");
        areaDTO.setSort(0);
        final List<AreaDTO> areaDTOS = Arrays.asList(areaDTO);
        when(mockTableRpcService.query("data")).thenReturn(areaDTOS);

        when(mockDistributeAreaService.queryBoundAreaOfStore("data")).thenReturn(Collections.emptyList());

        // Configure ItemConfigService.queryBatchByStoreGuid(...).
        final ItemConfigDO itemConfigDO = new ItemConfigDO();
        itemConfigDO.setStoreGuid("storeGuid");
        itemConfigDO.setSkuGuid("skuGuid");
        itemConfigDO.setTimeout(0);
        itemConfigDO.setMaxCopies(0);
        itemConfigDO.setDisplayType(0);
        final List<ItemConfigDO> itemConfigDOS = Arrays.asList(itemConfigDO);
        when(mockItemConfigService.queryBatchByStoreGuid("data")).thenReturn(itemConfigDOS);

        // Run the test
        final List<DstTypeBindRespDTO> result = distributeServiceImplUnderTest.queryAllItemOfStore(dstItemQueryReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryAllItemOfStore_ItemConfigServiceReturnsNoItems() {
        // Setup
        final DstItemQueryReqDTO dstItemQueryReqDTO = new DstItemQueryReqDTO();
        dstItemQueryReqDTO.setStoreGuid("data");
        dstItemQueryReqDTO.setDeviceId("deviceId");

        final DstTypeBindRespDTO dstTypeBindRespDTO = new DstTypeBindRespDTO();
        dstTypeBindRespDTO.setTypeGuid("first");
        dstTypeBindRespDTO.setTypeName("itemName");
        final DstItemBindRespDTO dstItemBindRespDTO = new DstItemBindRespDTO();
        dstItemBindRespDTO.setItemGuid("first");
        dstItemBindRespDTO.setItemName("itemName");
        dstItemBindRespDTO.setPinyin("pinyin");
        dstItemBindRespDTO.setIsBoundBySelf(false);
        dstItemBindRespDTO.setIsBoundByOthers(false);
        final DstSkuBindRespDTO dstSkuBindRespDTO = new DstSkuBindRespDTO();
        dstSkuBindRespDTO.setSkuGuid("eDishSkuCode");
        dstSkuBindRespDTO.setSkuName("eDishSkuName");
        dstSkuBindRespDTO.setSkuCode("skuCode");
        final DstAreaBindRespDTO dstAreaBindRespDTO = new DstAreaBindRespDTO();
        dstAreaBindRespDTO.setDeviceId("deviceId");
        dstAreaBindRespDTO.setDeviceName("deviceName");
        dstAreaBindRespDTO.setAreaGuid("areaGuid");
        dstAreaBindRespDTO.setAreaName("areaName");
        dstSkuBindRespDTO.setAreas(Arrays.asList(dstAreaBindRespDTO));
        dstSkuBindRespDTO.setTimeout(0);
        dstSkuBindRespDTO.setIsBoundBySelf(false);
        dstSkuBindRespDTO.setIsBoundByOthers(false);
        dstItemBindRespDTO.setSkus(Arrays.asList(dstSkuBindRespDTO));
        dstTypeBindRespDTO.setItemList(Arrays.asList(dstItemBindRespDTO));
        final List<DstTypeBindRespDTO> expectedResult = Arrays.asList(dstTypeBindRespDTO);
        when(mockDeviceConfigService.getDeviceNameMapOfStore("data")).thenReturn(new HashMap<>());

        // Configure DistributeItemService.queryBoundItemOfStore(...).
        final DistributeItemDO distributeItemDO = new DistributeItemDO();
        distributeItemDO.setStoreGuid("storeGuid");
        distributeItemDO.setDeviceId("deviceId");
        distributeItemDO.setItemGuid("itemGuid");
        distributeItemDO.setSkuGuid("skuGuid");
        distributeItemDO.setSkuCode("skuCode");
        final List<DistributeItemDO> distributeItemDOS = Arrays.asList(distributeItemDO);
        when(mockDistributeItemService.queryBoundItemOfStore("data")).thenReturn(distributeItemDOS);

        // Configure ItemRpcService.kdsMapping(...).
        final MappingRespDTO mappingRespDTO = new MappingRespDTO();
        mappingRespDTO.setPinyin("pinyin");
        mappingRespDTO.seteDishSkuCode("eDishSkuCode");
        mappingRespDTO.setSkuCode("skuCode");
        mappingRespDTO.seteDishSkuName("eDishSkuName");
        mappingRespDTO.seteDishCode("eDishCode");
        mappingRespDTO.seteDishName("eDishName");
        mappingRespDTO.setCategoryName("categoryName");
        mappingRespDTO.setCategoryId("categoryId");
        final List<MappingRespDTO> mappingRespDTOS = Arrays.asList(mappingRespDTO);
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData("data");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        itemSingleDTO.setItemQueryType(0);
        itemSingleDTO.setSkuGuids(Arrays.asList("value"));
        when(mockItemRpcService.kdsMapping(itemSingleDTO)).thenReturn(mappingRespDTOS);

        // Configure TableRpcService.query(...).
        final AreaDTO areaDTO = new AreaDTO();
        areaDTO.setGuid("snack_area_guid");
        areaDTO.setStoreGuid("storeGuid");
        areaDTO.setStoreName("storeName");
        areaDTO.setAreaName("外卖");
        areaDTO.setSort(0);
        final List<AreaDTO> areaDTOS = Arrays.asList(areaDTO);
        when(mockTableRpcService.query("data")).thenReturn(areaDTOS);

        // Configure DistributeAreaService.queryBoundAreaOfStore(...).
        final DistributeAreaDO distributeAreaDO = new DistributeAreaDO();
        distributeAreaDO.setStoreGuid("storeGuid");
        distributeAreaDO.setDeviceId("deviceId");
        distributeAreaDO.setAreaGuid("areaGuid");
        final List<DistributeAreaDO> distributeAreaDOS = Arrays.asList(distributeAreaDO);
        when(mockDistributeAreaService.queryBoundAreaOfStore("data")).thenReturn(distributeAreaDOS);

        when(mockItemConfigService.queryBatchByStoreGuid("data")).thenReturn(Collections.emptyList());

        // Run the test
        final List<DstTypeBindRespDTO> result = distributeServiceImplUnderTest.queryAllItemOfStore(dstItemQueryReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testBindArea() {
        // Setup
        final DstBindAreaReqDTO dstBindAreaReqDTO = new DstBindAreaReqDTO();
        dstBindAreaReqDTO.setStoreGuid("storeGuid");
        dstBindAreaReqDTO.setDeviceId("deviceId");
        dstBindAreaReqDTO.setSelectedAreaGuidList(Arrays.asList("value"));
        dstBindAreaReqDTO.setIsSnackSelected(false);
        dstBindAreaReqDTO.setIsTakeoutSelected(false);

        when(mockDistributeAreaService.queryBoundAreaGuidOfDevice("storeGuid", "deviceId"))
                .thenReturn(Arrays.asList("value"));
        when(mockDistributeItemService.queryOccupiedDeviceId("storeGuid", "deviceId"))
                .thenReturn(Arrays.asList("value"));
        when(mockDistributeAreaService.queryBoundAreaGuidOfDeviceList("storeGuid", Arrays.asList("value")))
                .thenReturn(Arrays.asList("value"));

        // Run the test
        distributeServiceImplUnderTest.bindArea(dstBindAreaReqDTO);

        // Verify the results
        verify(mockDistributeAreaService).simpleSaveBatchArea("storeGuid", "deviceId", Arrays.asList("value"));
        verify(mockDistributeAreaService).simpleRemoveBatchArea("storeGuid", "deviceId", Arrays.asList("value"));
    }

    @Test
    public void testBindArea_DistributeAreaServiceQueryBoundAreaGuidOfDeviceReturnsNoItems() {
        // Setup
        final DstBindAreaReqDTO dstBindAreaReqDTO = new DstBindAreaReqDTO();
        dstBindAreaReqDTO.setStoreGuid("storeGuid");
        dstBindAreaReqDTO.setDeviceId("deviceId");
        dstBindAreaReqDTO.setSelectedAreaGuidList(Arrays.asList("value"));
        dstBindAreaReqDTO.setIsSnackSelected(false);
        dstBindAreaReqDTO.setIsTakeoutSelected(false);

        when(mockDistributeAreaService.queryBoundAreaGuidOfDevice("storeGuid", "deviceId"))
                .thenReturn(Collections.emptyList());
        when(mockDistributeItemService.queryOccupiedDeviceId("storeGuid", "deviceId"))
                .thenReturn(Arrays.asList("value"));
        when(mockDistributeAreaService.queryBoundAreaGuidOfDeviceList("storeGuid", Arrays.asList("value")))
                .thenReturn(Arrays.asList("value"));

        // Run the test
        distributeServiceImplUnderTest.bindArea(dstBindAreaReqDTO);

        // Verify the results
        verify(mockDistributeAreaService).simpleSaveBatchArea("storeGuid", "deviceId", Arrays.asList("value"));
        verify(mockDistributeAreaService).simpleRemoveBatchArea("storeGuid", "deviceId", Arrays.asList("value"));
    }

    @Test
    public void testBindArea_DistributeItemServiceReturnsNoItems() {
        // Setup
        final DstBindAreaReqDTO dstBindAreaReqDTO = new DstBindAreaReqDTO();
        dstBindAreaReqDTO.setStoreGuid("storeGuid");
        dstBindAreaReqDTO.setDeviceId("deviceId");
        dstBindAreaReqDTO.setSelectedAreaGuidList(Arrays.asList("value"));
        dstBindAreaReqDTO.setIsSnackSelected(false);
        dstBindAreaReqDTO.setIsTakeoutSelected(false);

        when(mockDistributeAreaService.queryBoundAreaGuidOfDevice("storeGuid", "deviceId"))
                .thenReturn(Arrays.asList("value"));
        when(mockDistributeItemService.queryOccupiedDeviceId("storeGuid", "deviceId"))
                .thenReturn(Collections.emptyList());
        when(mockDistributeAreaService.queryBoundAreaGuidOfDeviceList("storeGuid", Arrays.asList("value")))
                .thenReturn(Arrays.asList("value"));

        // Run the test
        distributeServiceImplUnderTest.bindArea(dstBindAreaReqDTO);

        // Verify the results
        verify(mockDistributeAreaService).simpleSaveBatchArea("storeGuid", "deviceId", Arrays.asList("value"));
        verify(mockDistributeAreaService).simpleRemoveBatchArea("storeGuid", "deviceId", Arrays.asList("value"));
    }

    @Test
    public void testBindArea_DistributeAreaServiceQueryBoundAreaGuidOfDeviceListReturnsNoItems() {
        // Setup
        final DstBindAreaReqDTO dstBindAreaReqDTO = new DstBindAreaReqDTO();
        dstBindAreaReqDTO.setStoreGuid("storeGuid");
        dstBindAreaReqDTO.setDeviceId("deviceId");
        dstBindAreaReqDTO.setSelectedAreaGuidList(Arrays.asList("value"));
        dstBindAreaReqDTO.setIsSnackSelected(false);
        dstBindAreaReqDTO.setIsTakeoutSelected(false);

        when(mockDistributeAreaService.queryBoundAreaGuidOfDevice("storeGuid", "deviceId"))
                .thenReturn(Arrays.asList("value"));
        when(mockDistributeItemService.queryOccupiedDeviceId("storeGuid", "deviceId"))
                .thenReturn(Arrays.asList("value"));
        when(mockDistributeAreaService.queryBoundAreaGuidOfDeviceList("storeGuid", Arrays.asList("value")))
                .thenReturn(Collections.emptyList());

        // Run the test
        distributeServiceImplUnderTest.bindArea(dstBindAreaReqDTO);

        // Verify the results
        verify(mockDistributeAreaService).simpleSaveBatchArea("storeGuid", "deviceId", Arrays.asList("value"));
        verify(mockDistributeAreaService).simpleRemoveBatchArea("storeGuid", "deviceId", Arrays.asList("value"));
    }

    @Test
    public void testBindItem() {
        // Setup
        final DstBindItemReqDTO dstBindAreaReqDTO = new DstBindItemReqDTO();
        dstBindAreaReqDTO.setStoreGuid("storeGuid");
        dstBindAreaReqDTO.setDeviceId("deviceId");
        final PrdDstItemBindDTO prdDstItemBindDTO = new PrdDstItemBindDTO();
        prdDstItemBindDTO.setSkuGuid("skuGuid");
        dstBindAreaReqDTO.setBindItemSkuList(Arrays.asList(prdDstItemBindDTO));
        dstBindAreaReqDTO.setUnbindSkuGuidList(Arrays.asList("value"));

        when(mockDistributeItemService.queryBoundSkuGuidOfDevice("storeGuid", "deviceId"))
                .thenReturn(Arrays.asList("value"));
        when(mockDistributeAreaService.queryOccupiedDeviceId("storeGuid", "deviceId"))
                .thenReturn(Arrays.asList("value"));
        when(mockDistributeItemService.queryBoundSkuGuidOfDeviceList("storeGuid", Arrays.asList("value")))
                .thenReturn(Arrays.asList("value"));

        // Run the test
        distributeServiceImplUnderTest.bindItem(dstBindAreaReqDTO);

        // Verify the results
        // Confirm DistributeItemService.simpleSaveBatchSku(...).
        final PrdDstItemBindDTO prdDstItemBindDTO1 = new PrdDstItemBindDTO();
        prdDstItemBindDTO1.setItemGuid("itemGuid");
        prdDstItemBindDTO1.setSkuGuid("skuGuid");
        final List<PrdDstItemBindDTO> toBeBoundSkuList = Arrays.asList(prdDstItemBindDTO1);
        verify(mockDistributeItemService).simpleSaveBatchSku("storeGuid", "deviceId", toBeBoundSkuList);
    }

    @Test
    public void testBindItem_DistributeItemServiceQueryBoundSkuGuidOfDeviceReturnsNoItems() {
        // Setup
        final DstBindItemReqDTO dstBindAreaReqDTO = new DstBindItemReqDTO();
        dstBindAreaReqDTO.setStoreGuid("storeGuid");
        dstBindAreaReqDTO.setDeviceId("deviceId");
        final PrdDstItemBindDTO prdDstItemBindDTO = new PrdDstItemBindDTO();
        prdDstItemBindDTO.setSkuGuid("skuGuid");
        dstBindAreaReqDTO.setBindItemSkuList(Arrays.asList(prdDstItemBindDTO));
        dstBindAreaReqDTO.setUnbindSkuGuidList(Arrays.asList("value"));

        when(mockDistributeItemService.queryBoundSkuGuidOfDevice("storeGuid", "deviceId"))
                .thenReturn(Collections.emptyList());
        when(mockDistributeAreaService.queryOccupiedDeviceId("storeGuid", "deviceId"))
                .thenReturn(Arrays.asList("value"));
        when(mockDistributeItemService.queryBoundSkuGuidOfDeviceList("storeGuid", Arrays.asList("value")))
                .thenReturn(Arrays.asList("value"));

        // Run the test
        distributeServiceImplUnderTest.bindItem(dstBindAreaReqDTO);

        // Verify the results
        // Confirm DistributeItemService.simpleSaveBatchSku(...).
        final PrdDstItemBindDTO prdDstItemBindDTO1 = new PrdDstItemBindDTO();
        prdDstItemBindDTO1.setItemGuid("itemGuid");
        prdDstItemBindDTO1.setSkuGuid("skuGuid");
        final List<PrdDstItemBindDTO> toBeBoundSkuList = Arrays.asList(prdDstItemBindDTO1);
        verify(mockDistributeItemService).simpleSaveBatchSku("storeGuid", "deviceId", toBeBoundSkuList);
    }

    @Test
    public void testBindItem_DistributeAreaServiceReturnsNoItems() {
        // Setup
        final DstBindItemReqDTO dstBindAreaReqDTO = new DstBindItemReqDTO();
        dstBindAreaReqDTO.setStoreGuid("storeGuid");
        dstBindAreaReqDTO.setDeviceId("deviceId");
        final PrdDstItemBindDTO prdDstItemBindDTO = new PrdDstItemBindDTO();
        prdDstItemBindDTO.setSkuGuid("skuGuid");
        dstBindAreaReqDTO.setBindItemSkuList(Arrays.asList(prdDstItemBindDTO));
        dstBindAreaReqDTO.setUnbindSkuGuidList(Arrays.asList("value"));

        when(mockDistributeItemService.queryBoundSkuGuidOfDevice("storeGuid", "deviceId"))
                .thenReturn(Arrays.asList("value"));
        when(mockDistributeAreaService.queryOccupiedDeviceId("storeGuid", "deviceId"))
                .thenReturn(Collections.emptyList());
        when(mockDistributeItemService.queryBoundSkuGuidOfDeviceList("storeGuid", Arrays.asList("value")))
                .thenReturn(Arrays.asList("value"));

        // Run the test
        distributeServiceImplUnderTest.bindItem(dstBindAreaReqDTO);

        // Verify the results
        // Confirm DistributeItemService.simpleSaveBatchSku(...).
        final PrdDstItemBindDTO prdDstItemBindDTO1 = new PrdDstItemBindDTO();
        prdDstItemBindDTO1.setItemGuid("itemGuid");
        prdDstItemBindDTO1.setSkuGuid("skuGuid");
        final List<PrdDstItemBindDTO> toBeBoundSkuList = Arrays.asList(prdDstItemBindDTO1);
        verify(mockDistributeItemService).simpleSaveBatchSku("storeGuid", "deviceId", toBeBoundSkuList);
    }

    @Test
    public void testBindItem_DistributeItemServiceQueryBoundSkuGuidOfDeviceListReturnsNoItems() {
        // Setup
        final DstBindItemReqDTO dstBindAreaReqDTO = new DstBindItemReqDTO();
        dstBindAreaReqDTO.setStoreGuid("storeGuid");
        dstBindAreaReqDTO.setDeviceId("deviceId");
        final PrdDstItemBindDTO prdDstItemBindDTO = new PrdDstItemBindDTO();
        prdDstItemBindDTO.setSkuGuid("skuGuid");
        dstBindAreaReqDTO.setBindItemSkuList(Arrays.asList(prdDstItemBindDTO));
        dstBindAreaReqDTO.setUnbindSkuGuidList(Arrays.asList("value"));

        when(mockDistributeItemService.queryBoundSkuGuidOfDevice("storeGuid", "deviceId"))
                .thenReturn(Arrays.asList("value"));
        when(mockDistributeAreaService.queryOccupiedDeviceId("storeGuid", "deviceId"))
                .thenReturn(Arrays.asList("value"));
        when(mockDistributeItemService.queryBoundSkuGuidOfDeviceList("storeGuid", Arrays.asList("value")))
                .thenReturn(Collections.emptyList());

        // Run the test
        distributeServiceImplUnderTest.bindItem(dstBindAreaReqDTO);

        // Verify the results
        // Confirm DistributeItemService.simpleSaveBatchSku(...).
        final PrdDstItemBindDTO prdDstItemBindDTO1 = new PrdDstItemBindDTO();
        prdDstItemBindDTO1.setItemGuid("itemGuid");
        prdDstItemBindDTO1.setSkuGuid("skuGuid");
        final List<PrdDstItemBindDTO> toBeBoundSkuList = Arrays.asList(prdDstItemBindDTO1);
        verify(mockDistributeItemService).simpleSaveBatchSku("storeGuid", "deviceId", toBeBoundSkuList);
    }

    @Test
    public void testUnbindItem() {
        // Setup
        final DstBindItemReqDTO dstBindAreaReqDTO = new DstBindItemReqDTO();
        dstBindAreaReqDTO.setStoreGuid("storeGuid");
        dstBindAreaReqDTO.setDeviceId("deviceId");
        final PrdDstItemBindDTO prdDstItemBindDTO = new PrdDstItemBindDTO();
        prdDstItemBindDTO.setSkuGuid("skuGuid");
        dstBindAreaReqDTO.setBindItemSkuList(Arrays.asList(prdDstItemBindDTO));
        dstBindAreaReqDTO.setUnbindSkuGuidList(Arrays.asList("value"));

        // Run the test
        distributeServiceImplUnderTest.unbindItem(dstBindAreaReqDTO);

        // Verify the results
        verify(mockDistributeItemService).simpleRemoveBatchSku("storeGuid", "deviceId", Arrays.asList("value"));
    }

    @Test
    public void testReInitialize() {
        // Setup
        // Run the test
        distributeServiceImplUnderTest.reInitialize("storeGuid", "deviceId");

        // Verify the results
        verify(mockDistributeAreaService).reInitialize("storeGuid", "deviceId");
        verify(mockDistributeItemService).reInitialize("storeGuid", "deviceId");
    }
}
