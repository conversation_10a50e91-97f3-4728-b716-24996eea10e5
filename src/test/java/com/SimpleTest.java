package com;

import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SimpleTest
 * @date 2018/9/28 9:25
 * @description //TODO
 * @program holder-saas-store-print
 */
public class SimpleTest {
    private static final Logger LOGGER = LoggerFactory.getLogger(SimpleTest.class);

    @Test
    public void test() {
        String doubleString = "40 * 30";

        String singleString = "80";

        String substring = doubleString.substring(0, doubleString.indexOf("*")).trim();
        String substring2 = singleString.substring(0, singleString.indexOf("*")).trim();

        LOGGER.info("[{}]", substring);
        LOGGER.info("[{}]", substring2);

    }

    static class User {

        private String guid;

        public String getGuid() {
            return guid;
        }

        public void setGuid(String guid) {
            this.guid = guid;
        }
    }

    static class Student extends User {

        private String name;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }

    @Test
    public void test2() {
        User user = new Student();
        System.out.println(user.getClass());
    }

    @Test
    public void test1() {
        List<User> readDOList = new ArrayList<>();
        User user = new User();
        user.setGuid("1");
        User user2 = new User();
        user.setGuid("2");
        readDOList.add(user);
        readDOList.add(user2);

        System.out.println(user);
        System.out.println(user2);
        readDOList.forEach(System.out::println);

        List<User> collect = readDOList.stream().collect(Collectors.toList());
        collect.forEach(System.out::println);
    }

    @Test
    public void test3() {
        System.out.println("{\"storeGuid\":\"6506453252643487745\",\"invoiceType\":0,\"storeName\":{\"size\":2,\"align\":1,\"bold\":true,\"underline\":false,\"enable\":true},\"invoiceName\":{\"size\":2,\"align\":1,\"bold\":false,\"underline\":false,\"enable\":false},\"markNo\":{\"size\":2,\"align\":0,\"bold\":true,\"underline\":false,\"enable\":true},\"personNumber\":{\"size\":1,\"align\":2,\"bold\":false,\"underline\":false,\"enable\":true},\"openTableTime\":{\"size\":1,\"align\":0,\"bold\":false,\"underline\":false,\"enable\":true},\"operator\":{\"size\":1,\"align\":0,\"bold\":false,\"underline\":false,\"enable\":true},\"printTime\":{\"size\":1,\"align\":0,\"bold\":false,\"underline\":false,\"enable\":true}}".getBytes().length);
        System.out.println("{\"storeGuid\":\"6506453252643487745\",\"invoiceType\":0,\"storeName\":{\"size\":2,\"align\":1,\"bold\":true,\"underline\":false,\"enable\":true},\"invoiceName\":{\"size\":2,\"align\":1,\"bold\":false,\"underline\":false,\"enable\":false},\"markNo\":{\"size\":2,\"align\":0,\"bold\":true,\"underline\":false,\"enable\":true},\"personNumber\":{\"size\":1,\"align\":2,\"bold\":false,\"underline\":false,\"enable\":true},\"openTableTime\":{\"size\":1,\"align\":0,\"bold\":false,\"underline\":false,\"enable\":true},\"operator\":{\"size\":1,\"align\":0,\"bold\":false,\"underline\":false,\"enable\":true},\"printTime\":{\"size\":1,\"align\":0,\"bold\":false,\"underline\":false,\"enable\":true}}".length());
        System.out.println("{\"storeGuid\":\"6506453252643487745\",\"invoiceType\":5,\"storeName\":{\"size\":2,\"align\":1,\"bold\":true,\"underline\":false,\"enable\":true},\"invoiceName\":{\"size\":2,\"align\":1,\"bold\":false,\"underline\":false,\"enable\":true},\"markNo\":{\"size\":2,\"align\":0,\"bold\":true,\"underline\":false,\"enable\":true},\"personNumber\":{\"size\":1,\"align\":2,\"bold\":false,\"underline\":false,\"enable\":true},\"openTableTime\":{\"size\":1,\"align\":0,\"bold\":false,\"underline\":false,\"enable\":true},\"additionalCharge\":{\"size\":1,\"align\":3,\"bold\":false,\"underline\":false,\"enable\":true},\"reduceRecord\":{\"size\":1,\"align\":3,\"bold\":false,\"underline\":false,\"enable\":true},\"payableMoney\":{\"size\":2,\"align\":0,\"bold\":true,\"underline\":false,\"enable\":true},\"storeAddress\":{\"size\":1,\"align\":0,\"bold\":false,\"underline\":false,\"enable\":true},\"storeTelephone\":{\"size\":1,\"align\":0,\"bold\":false,\"underline\":false,\"enable\":true},\"operator\":{\"size\":1,\"align\":0,\"bold\":false,\"underline\":false,\"enable\":true},\"printTime\":{\"size\":1,\"align\":2,\"bold\":false,\"underline\":false,\"enable\":true}}".length());
        System.out.println("{\"storeName\":64,\"invoiceName\":64,\"markNo\":64,\"personNumber\":64,\"openTableTime\":64,\"additionalCharge\":64,\"reduceRecord\":64,\"payableMoney\":64,\"storeAddress\":64,\"storeTelephone\":64,\"operator\":64,\"printTime\":64}".length());
    }
}
