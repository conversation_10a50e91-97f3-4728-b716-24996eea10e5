package com.holder.saas.print.controller;

import com.holder.saas.print.service.PrintRecordService;
import com.holderzone.framework.rocketmq.common.DefaultRocketMqProducer;
import com.holderzone.saas.store.dto.print.PrintOrderDTO;
import com.holderzone.saas.store.dto.print.PrintRecordDTO;
import com.holderzone.saas.store.dto.print.PrintRecordReqDTO;
import com.holderzone.saas.store.dto.print.format.FormatDTO;
import org.apache.rocketmq.common.message.Message;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(PrintRecordController.class)
public class PrintRecordControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private PrintRecordService mockPrintRecordService;
    @MockBean
    private DefaultRocketMqProducer mockDefaultRocketMqProducer;

    @Test
    public void testPrintTask() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(post("/print_record/send")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
        verify(mockDefaultRocketMqProducer).sendMessage(any(Message.class));
    }

    @Test
    public void testGetPrinterOrder() throws Exception {
        // Setup
        // Configure PrintRecordService.getPrintOrder(...).
        final PrintOrderDTO printOrderDTO = new PrintOrderDTO();
        printOrderDTO.setPrintKey("printKey");
        printOrderDTO.setBusinessType(0);
        printOrderDTO.setPrinterType(0);
        printOrderDTO.setPrinterIp("printerIp");
        printOrderDTO.setPrinterPort(0);
        final List<PrintOrderDTO> printOrderDTOS = Arrays.asList(printOrderDTO);
        final PrintRecordReqDTO printRecordReqDTO = new PrintRecordReqDTO();
        printRecordReqDTO.setMsgId("msgId");
        printRecordReqDTO.setDeviceId("deviceId");
        printRecordReqDTO.setRecordGuid("recordGuid");
        printRecordReqDTO.setPrintStatus(0);
        printRecordReqDTO.setArrayOfRecordGuid(Arrays.asList("value"));
        when(mockPrintRecordService.getPrintOrder(printRecordReqDTO)).thenReturn(printOrderDTOS);

        // Run the test and verify the results
        mockMvc.perform(post("/print_record/get_order")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGetPrinterOrder_PrintRecordServiceReturnsNoItems() throws Exception {
        // Setup
        // Configure PrintRecordService.getPrintOrder(...).
        final PrintRecordReqDTO printRecordReqDTO = new PrintRecordReqDTO();
        printRecordReqDTO.setMsgId("msgId");
        printRecordReqDTO.setDeviceId("deviceId");
        printRecordReqDTO.setRecordGuid("recordGuid");
        printRecordReqDTO.setPrintStatus(0);
        printRecordReqDTO.setArrayOfRecordGuid(Arrays.asList("value"));
        when(mockPrintRecordService.getPrintOrder(printRecordReqDTO)).thenReturn(Collections.emptyList());

        // Run the test and verify the results
        mockMvc.perform(post("/print_record/get_order")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("[]", true));
    }

    @Test
    public void testGetTestPrinterOrder() throws Exception {
        // Setup
        // Configure PrintRecordService.getTestPrintOrder(...).
        final PrintOrderDTO printOrderDTO = new PrintOrderDTO();
        printOrderDTO.setPrintKey("printKey");
        printOrderDTO.setBusinessType(0);
        printOrderDTO.setPrinterType(0);
        printOrderDTO.setPrinterIp("printerIp");
        printOrderDTO.setPrinterPort(0);
        final FormatDTO formatDTO = new FormatDTO();
        formatDTO.setGuid("63cf0ef1-0169-437b-b4ad-1a56216ed90b");
        formatDTO.setName("name");
        formatDTO.setStoreGuid("storeGuid");
        formatDTO.setInvoiceType(0);
        formatDTO.setPageSize(0);
        when(mockPrintRecordService.getTestPrintOrder(formatDTO)).thenReturn(printOrderDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/print_record/get_test_order")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGetTestPrinterOrders() throws Exception {
        // Setup
        // Configure PrintRecordService.getTestPrintOrders(...).
        final PrintOrderDTO printOrderDTO = new PrintOrderDTO();
        printOrderDTO.setPrintKey("printKey");
        printOrderDTO.setBusinessType(0);
        printOrderDTO.setPrinterType(0);
        printOrderDTO.setPrinterIp("printerIp");
        printOrderDTO.setPrinterPort(0);
        final List<PrintOrderDTO> printOrderDTOS = Arrays.asList(printOrderDTO);
        final FormatDTO formatDTO = new FormatDTO();
        formatDTO.setGuid("63cf0ef1-0169-437b-b4ad-1a56216ed90b");
        formatDTO.setName("name");
        formatDTO.setStoreGuid("storeGuid");
        formatDTO.setInvoiceType(0);
        formatDTO.setPageSize(0);
        when(mockPrintRecordService.getTestPrintOrders(formatDTO)).thenReturn(printOrderDTOS);

        // Run the test and verify the results
        mockMvc.perform(post("/print_record/get_test_orders")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGetTestPrinterOrders_PrintRecordServiceReturnsNoItems() throws Exception {
        // Setup
        // Configure PrintRecordService.getTestPrintOrders(...).
        final FormatDTO formatDTO = new FormatDTO();
        formatDTO.setGuid("63cf0ef1-0169-437b-b4ad-1a56216ed90b");
        formatDTO.setName("name");
        formatDTO.setStoreGuid("storeGuid");
        formatDTO.setInvoiceType(0);
        formatDTO.setPageSize(0);
        when(mockPrintRecordService.getTestPrintOrders(formatDTO)).thenReturn(Collections.emptyList());

        // Run the test and verify the results
        mockMvc.perform(post("/print_record/get_test_orders")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("[]", true));
    }

    @Test
    public void testListRecord() throws Exception {
        // Setup
        // Configure PrintRecordService.listRecord(...).
        final PrintRecordDTO printRecordDTO = new PrintRecordDTO();
        printRecordDTO.setRecordUid("recordUid");
        printRecordDTO.setRecordGuid("recordGuid");
        printRecordDTO.setStoreGuid("storeGuid");
        printRecordDTO.setDeviceId("deviceId");
        printRecordDTO.setInvoiceType(0);
        final List<PrintRecordDTO> printRecordDTOS = Arrays.asList(printRecordDTO);
        final PrintRecordReqDTO printRecordReqDTO = new PrintRecordReqDTO();
        printRecordReqDTO.setMsgId("msgId");
        printRecordReqDTO.setDeviceId("deviceId");
        printRecordReqDTO.setRecordGuid("recordGuid");
        printRecordReqDTO.setPrintStatus(0);
        printRecordReqDTO.setArrayOfRecordGuid(Arrays.asList("value"));
        when(mockPrintRecordService.listRecord(printRecordReqDTO)).thenReturn(printRecordDTOS);

        // Run the test and verify the results
        mockMvc.perform(post("/print_record/list")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testListRecord_PrintRecordServiceReturnsNoItems() throws Exception {
        // Setup
        // Configure PrintRecordService.listRecord(...).
        final PrintRecordReqDTO printRecordReqDTO = new PrintRecordReqDTO();
        printRecordReqDTO.setMsgId("msgId");
        printRecordReqDTO.setDeviceId("deviceId");
        printRecordReqDTO.setRecordGuid("recordGuid");
        printRecordReqDTO.setPrintStatus(0);
        printRecordReqDTO.setArrayOfRecordGuid(Arrays.asList("value"));
        when(mockPrintRecordService.listRecord(printRecordReqDTO)).thenReturn(Collections.emptyList());

        // Run the test and verify the results
        mockMvc.perform(post("/print_record/list")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("[]", true));
    }

    @Test
    public void testUpdateStatus() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(post("/print_record/update_status")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));

        // Confirm PrintRecordService.updatePrintResult(...).
        final PrintRecordReqDTO printRecordReqDTO = new PrintRecordReqDTO();
        printRecordReqDTO.setMsgId("msgId");
        printRecordReqDTO.setDeviceId("deviceId");
        printRecordReqDTO.setRecordGuid("recordGuid");
        printRecordReqDTO.setPrintStatus(0);
        printRecordReqDTO.setArrayOfRecordGuid(Arrays.asList("value"));
        verify(mockPrintRecordService).updatePrintResult(printRecordReqDTO);
    }

    @Test
    public void testDeleteHistoryRecord() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(post("/print_record/delete_history_record")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
        verify(mockPrintRecordService).deleteHistoryRecord();
    }

    @Test
    public void testDeleteRecord() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(post("/print_record/delete")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));

        // Confirm PrintRecordService.deleteRecord(...).
        final PrintRecordReqDTO printRecordReqDTO = new PrintRecordReqDTO();
        printRecordReqDTO.setMsgId("msgId");
        printRecordReqDTO.setDeviceId("deviceId");
        printRecordReqDTO.setRecordGuid("recordGuid");
        printRecordReqDTO.setPrintStatus(0);
        printRecordReqDTO.setArrayOfRecordGuid(Arrays.asList("value"));
        verify(mockPrintRecordService).deleteRecord(printRecordReqDTO);
    }

    @Test
    public void testBatchDeleteRecord() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(post("/print_record/batch_delete")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
        verify(mockPrintRecordService).batchDeleteRecord(Arrays.asList("value"));
    }

    @Test
    public void testReprintTakeawayPrintOrderList() throws Exception {
        // Setup
        // Configure PrintRecordService.reprintTakeawayPrintOrderList(...).
        final PrintOrderDTO printOrderDTO = new PrintOrderDTO();
        printOrderDTO.setPrintKey("printKey");
        printOrderDTO.setBusinessType(0);
        printOrderDTO.setPrinterType(0);
        printOrderDTO.setPrinterIp("printerIp");
        printOrderDTO.setPrinterPort(0);
        final List<PrintOrderDTO> printOrderDTOS = Arrays.asList(printOrderDTO);
        when(mockPrintRecordService.reprintTakeawayPrintOrderList("storeGuid")).thenReturn(printOrderDTOS);

        // Run the test and verify the results
        mockMvc.perform(get("/print_record/takeaway_timeout/reprint")
                        .param("storeGuid", "storeGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testReprintTakeawayPrintOrderList_PrintRecordServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockPrintRecordService.reprintTakeawayPrintOrderList("storeGuid")).thenReturn(Collections.emptyList());

        // Run the test and verify the results
        mockMvc.perform(get("/print_record/takeaway_timeout/reprint")
                        .param("storeGuid", "storeGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("[]", true));
    }
}
