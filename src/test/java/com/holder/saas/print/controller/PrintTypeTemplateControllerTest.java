package com.holder.saas.print.controller;

import com.alibaba.fastjson.JSON;
import com.holder.saas.print.HolderSaasStorePrintApplication;
import com.holder.saas.print.util.JsonFileUtil;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.item.req.SingleDataPageDTO;
import com.holderzone.saas.store.dto.print.type.PrintTypeTemplateDTO;
import com.holderzone.saas.store.dto.print.type.PrintTypeTemplateEnableDTO;
import com.holderzone.saas.store.dto.print.type.TemplateStoreQO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.io.UnsupportedEncodingException;

import static com.holderzone.saas.store.dto.common.CommonConstant.USER_INFO;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * <AUTHOR>
 * @date 2024/1/26
 * @description
 */
@Slf4j
@WebAppConfiguration
@ContextConfiguration
@AutoConfigureMockMvc
@RunWith(SpringRunner.class)
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
@SpringBootTest(classes = HolderSaasStorePrintApplication.class)
public class PrintTypeTemplateControllerTest {

    private static final String USERINFO = "{\"operSubjectGuid\": \"2010121440477930009\"," +
            "\"enterpriseGuid\":" + " \"2009281531195930006\"," +
            "\"enterpriseName\": \"赵氏企业\"," +
            "\"enterpriseNo\": \"********\"," +
            "\"storeGuid\":" + " \"2106221850429620006\"," +
            "\"storeName\": \"交子大道测试门店\"," +
            "\"storeNo\": \"5796807\"," +
            "\"userGuid\": \"6787561298847596545\"," +
            "\"account\": \"196504\"," +
            "\"tel\": \"***********\"," +
            "\"name\": \"靓亮仔\"}\n";

    private static final String PRINT_TYPE_TEMPLATE = "/print_type_template";

    private static final String RESPONSE = "response:";

    @Autowired
    private WebApplicationContext wac;

    private MockMvc mockMvc;

    @Before
    public void setupMockMvc() {
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();
    }

    @Test
    public void create() throws UnsupportedEncodingException {
        PrintTypeTemplateDTO createReqDTO = JSON.parseObject(JsonFileUtil.read("printTypeTemplate/create.json"),
                PrintTypeTemplateDTO.class);
        testCreate(createReqDTO);
    }

    @Test
    public void createBranch1() throws UnsupportedEncodingException {
        PrintTypeTemplateDTO createBranch1ReqDTO = JSON.parseObject(JsonFileUtil.read("printTypeTemplate/createBranch1.json"),
                PrintTypeTemplateDTO.class);
        testCreate(createBranch1ReqDTO);
    }

    private void testCreate(PrintTypeTemplateDTO createReqDTO) throws UnsupportedEncodingException {
        String createJsonString = JSON.toJSONString(createReqDTO);
        MvcResult createResult = null;
        try {
            createResult = mockMvc.perform(post(PRINT_TYPE_TEMPLATE + "/create")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(createJsonString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        String contentAsString = createResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    @Test
    public void queryPage() throws UnsupportedEncodingException {
        SingleDataPageDTO queryPageReqDTO = JSON.parseObject(JsonFileUtil.read("printTypeTemplate/queryPage.json"),
                SingleDataPageDTO.class);
        String queryPageJsonString = JSON.toJSONString(queryPageReqDTO);
        MvcResult queryPageResult = null;
        try {
            queryPageResult = mockMvc.perform(post(PRINT_TYPE_TEMPLATE + "/query_page")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(queryPageJsonString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        String contentAsString = queryPageResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    @Test
    public void queryDetail() throws UnsupportedEncodingException {
        SingleDataPageDTO queryDetailReqDTO = JSON.parseObject(JsonFileUtil.read("printTypeTemplate/queryDetail.json"),
                SingleDataPageDTO.class);
        String queryDetailJsonString = JSON.toJSONString(queryDetailReqDTO);
        MvcResult queryDetailResult = null;
        try {
            queryDetailResult = mockMvc.perform(post(PRINT_TYPE_TEMPLATE + "/query_detail")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(queryDetailJsonString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        String contentAsString = queryDetailResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    @Test
    public void modify() throws UnsupportedEncodingException {
        PrintTypeTemplateDTO modifyReqDTO = JSON.parseObject(JsonFileUtil.read("printTypeTemplate/modify.json"),
                PrintTypeTemplateDTO.class);
        String modifyJsonString = JSON.toJSONString(modifyReqDTO);
        MvcResult modifyResult = null;
        try {
            modifyResult = mockMvc.perform(post(PRINT_TYPE_TEMPLATE + "/modify")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(modifyJsonString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        String contentAsString = modifyResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    @Test
    public void enable() throws UnsupportedEncodingException {
        PrintTypeTemplateEnableDTO enableReqDTO = JSON.parseObject(JsonFileUtil.read("printTypeTemplate/enable.json"),
                PrintTypeTemplateEnableDTO.class);
        String enableJsonString = JSON.toJSONString(enableReqDTO);
        MvcResult enableResult = null;
        try {
            enableResult = mockMvc.perform(post(PRINT_TYPE_TEMPLATE + "/enable")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(enableJsonString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        String contentAsString = enableResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    @Test
    public void delete() throws UnsupportedEncodingException {
        SingleDataDTO deleteReqDTO = JSON.parseObject(JsonFileUtil.read("printTypeTemplate/delete.json"),
                SingleDataDTO.class);
        String deleteJsonString = JSON.toJSONString(deleteReqDTO);
        MvcResult deleteResult = null;
        try {
            deleteResult = mockMvc.perform(post(PRINT_TYPE_TEMPLATE + "/delete")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(deleteJsonString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        String contentAsString = deleteResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    @Test
    public void queryStoreByBrand() throws UnsupportedEncodingException {
        TemplateStoreQO queryStoreByBrandReqDTO = JSON.parseObject(JsonFileUtil.read("printTypeTemplate/queryStoreByBrand.json"),
                TemplateStoreQO.class);
        String queryStoreByBrandJsonString = JSON.toJSONString(queryStoreByBrandReqDTO);
        MvcResult queryStoreByBrandResult = null;
        try {
            queryStoreByBrandResult = mockMvc.perform(post(PRINT_TYPE_TEMPLATE + "/query_store_by_brand")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(queryStoreByBrandJsonString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        String contentAsString = queryStoreByBrandResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    @Test
    public void checkTemplateName() throws UnsupportedEncodingException {
        PrintTypeTemplateDTO checkTemplateNameReqDTO = JSON.parseObject(JsonFileUtil.read("printTypeTemplate/checkTemplateName.json"),
                PrintTypeTemplateDTO.class);
        String checkTemplateNameJsonString = JSON.toJSONString(checkTemplateNameReqDTO);
        MvcResult checkTemplateNameResult = null;
        try {
            checkTemplateNameResult = mockMvc.perform(post(PRINT_TYPE_TEMPLATE + "/check_template_name")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(checkTemplateNameJsonString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        String contentAsString = checkTemplateNameResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }
}