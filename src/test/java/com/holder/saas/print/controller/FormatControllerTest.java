package com.holder.saas.print.controller;

import com.holder.saas.print.service.PrinterFormatService;
import com.holderzone.saas.store.dto.print.format.FormatDTO;
import com.holderzone.saas.store.dto.print.format.TakeoutFormatDTO;
import com.holderzone.saas.store.dto.print.format.metadata.FormatMetadata;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(FormatController.class)
public class FormatControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private PrinterFormatService mockPrinterFormatService;

    @Test
    public void testAddFormat() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(post("/format/add")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));

        // Confirm PrinterFormatService.add(...).
        final FormatDTO formatDTO = new FormatDTO();
        formatDTO.setGuid("3c342b6c-e079-4fd2-bb83-9eafda9ad02d");
        formatDTO.setName("name");
        formatDTO.setStoreGuid("storeGuid");
        formatDTO.setInvoiceType(0);
        formatDTO.setPageSize(0);
        verify(mockPrinterFormatService).add(formatDTO);
    }

    @Test
    public void testListFormat() throws Exception {
        // Setup
        // Configure PrinterFormatService.list(...).
        final FormatDTO formatDTO = new FormatDTO();
        formatDTO.setGuid("3c342b6c-e079-4fd2-bb83-9eafda9ad02d");
        formatDTO.setName("name");
        formatDTO.setStoreGuid("storeGuid");
        formatDTO.setInvoiceType(0);
        formatDTO.setPageSize(0);
        final List<FormatDTO> formatDTOS = Arrays.asList(formatDTO);
        final FormatDTO formatDTO1 = new FormatDTO();
        formatDTO1.setGuid("3c342b6c-e079-4fd2-bb83-9eafda9ad02d");
        formatDTO1.setName("name");
        formatDTO1.setStoreGuid("storeGuid");
        formatDTO1.setInvoiceType(0);
        formatDTO1.setPageSize(0);
        when(mockPrinterFormatService.list(formatDTO1)).thenReturn(formatDTOS);

        // Run the test and verify the results
        mockMvc.perform(post("/format/list")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testListFormat_PrinterFormatServiceReturnsNoItems() throws Exception {
        // Setup
        // Configure PrinterFormatService.list(...).
        final FormatDTO formatDTO = new FormatDTO();
        formatDTO.setGuid("3c342b6c-e079-4fd2-bb83-9eafda9ad02d");
        formatDTO.setName("name");
        formatDTO.setStoreGuid("storeGuid");
        formatDTO.setInvoiceType(0);
        formatDTO.setPageSize(0);
        when(mockPrinterFormatService.list(formatDTO)).thenReturn(Collections.emptyList());

        // Run the test and verify the results
        mockMvc.perform(post("/format/list")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("[]", true));
    }

    @Test
    public void testDeleteFormat() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(post("/format/delete")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));

        // Confirm PrinterFormatService.delete(...).
        final FormatDTO formatDTO = new FormatDTO();
        formatDTO.setGuid("3c342b6c-e079-4fd2-bb83-9eafda9ad02d");
        formatDTO.setName("name");
        formatDTO.setStoreGuid("storeGuid");
        formatDTO.setInvoiceType(0);
        formatDTO.setPageSize(0);
        verify(mockPrinterFormatService).delete(formatDTO);
    }

    @Test
    public void testEnableFormat() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(post("/format/enable")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));

        // Confirm PrinterFormatService.enable(...).
        final FormatDTO formatDTO = new FormatDTO();
        formatDTO.setGuid("3c342b6c-e079-4fd2-bb83-9eafda9ad02d");
        formatDTO.setName("name");
        formatDTO.setStoreGuid("storeGuid");
        formatDTO.setInvoiceType(0);
        formatDTO.setPageSize(0);
        verify(mockPrinterFormatService).enable(formatDTO);
    }

    @Test
    public void testGetInvoiceUrls() throws Exception {
        // Setup
        when(mockPrinterFormatService.getInvoiceUrls("storeGuid")).thenReturn(Arrays.asList("value"));

        // Run the test and verify the results
        mockMvc.perform(post("/format/urls")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGetInvoiceUrls_PrinterFormatServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockPrinterFormatService.getInvoiceUrls("storeGuid")).thenReturn(Collections.emptyList());

        // Run the test and verify the results
        mockMvc.perform(post("/format/urls")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("[]", true));
    }

    @Test
    public void testJudgeEnablePreCheckFormat() throws Exception {
        // Setup
        when(mockPrinterFormatService.judgeEnablePreCheckFormat("storeGuid")).thenReturn(false);

        // Run the test and verify the results
        mockMvc.perform(get("/format/judge_pre_qr")
                        .param("storeGuid", "storeGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testJudgeEnablePreCheckFormat_PrinterFormatServiceReturnsTrue() throws Exception {
        // Setup
        when(mockPrinterFormatService.judgeEnablePreCheckFormat("storeGuid")).thenReturn(true);

        // Run the test and verify the results
        mockMvc.perform(get("/format/judge_pre_qr")
                        .param("storeGuid", "storeGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testQueryTakeout() throws Exception {
        // Setup
        // Configure PrinterFormatService.query(...).
        final TakeoutFormatDTO takeoutFormatDTO = new TakeoutFormatDTO();
        takeoutFormatDTO.setStoreGuid("storeGuid");
        takeoutFormatDTO.setInvoiceType(0);
        final FormatMetadata platform = new FormatMetadata();
        platform.setSize(0);
        platform.setXm(0);
        takeoutFormatDTO.setPlatform(platform);
        when(mockPrinterFormatService.query("storeGuid", 0)).thenReturn(takeoutFormatDTO);

        // Run the test and verify the results
        mockMvc.perform(get("/format/query_takeout")
                        .param("storeGuid", "storeGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }
}
