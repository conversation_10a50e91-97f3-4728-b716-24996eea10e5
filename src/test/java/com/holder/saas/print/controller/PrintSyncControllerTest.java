package com.holder.saas.print.controller;

import com.holder.saas.print.service.*;
import com.holderzone.saas.store.dto.print.raw.*;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(PrintSyncController.class)
public class PrintSyncControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private PrinterService mockPrinterService;
    @MockBean
    private PrinterItemService mockPrinterItemService;
    @MockBean
    private PrinterAreaService mockPrinterAreaService;
    @MockBean
    private PrinterInvoiceService mockPrinterInvoiceService;
    @MockBean
    private PrinterFormatService mockPrinterFormatService;

    @Test
    public void testSync() throws Exception {
        // Setup
        // Configure PrinterService.listRaw(...).
        final PrinterRawDTO printerRawDTO = new PrinterRawDTO();
        printerRawDTO.setStoreGuid("storeGuid");
        printerRawDTO.setStoreName("storeName");
        printerRawDTO.setDeviceId("deviceId");
        printerRawDTO.setPrinterGuid("printerGuid");
        printerRawDTO.setPrinterName("printerName");
        final List<PrinterRawDTO> printerRawDTOS = Arrays.asList(printerRawDTO);
        when(mockPrinterService.listRaw("storeGuid")).thenReturn(printerRawDTOS);

        // Configure PrinterItemService.listRaw(...).
        final PrinterItemRawDTO printerItemRawDTO = new PrinterItemRawDTO();
        printerItemRawDTO.setGuid("8ac4a69d-8617-4f69-b558-2031a5c2a4ca");
        printerItemRawDTO.setStoreGuid("storeGuid");
        printerItemRawDTO.setPrinterGuid("printerGuid");
        printerItemRawDTO.setItemGuid("itemGuid");
        printerItemRawDTO.setItemName("itemName");
        final List<PrinterItemRawDTO> printerItemRawDTOS = Arrays.asList(printerItemRawDTO);
        when(mockPrinterItemService.listRaw("storeGuid")).thenReturn(printerItemRawDTOS);

        // Configure PrinterAreaService.listRaw(...).
        final PrinterAreaRawDTO printerAreaRawDTO = new PrinterAreaRawDTO();
        printerAreaRawDTO.setGuid("9ae079f2-b4f5-4679-9860-9556ab244c1d");
        printerAreaRawDTO.setStoreGuid("storeGuid");
        printerAreaRawDTO.setPrinterGuid("printerGuid");
        printerAreaRawDTO.setAreaGuid("areaGuid");
        printerAreaRawDTO.setAreaName("areaName");
        final List<PrinterAreaRawDTO> printerAreaRawDTOS = Arrays.asList(printerAreaRawDTO);
        when(mockPrinterAreaService.listRaw("storeGuid")).thenReturn(printerAreaRawDTOS);

        // Configure PrinterInvoiceService.listRaw(...).
        final PrinterInvoiceRawDTO printerInvoiceRawDTO = new PrinterInvoiceRawDTO();
        printerInvoiceRawDTO.setGuid("420e934a-2b51-4c9c-a80a-82645bec3489");
        printerInvoiceRawDTO.setStoreGuid("storeGuid");
        printerInvoiceRawDTO.setPrinterGuid("printerGuid");
        printerInvoiceRawDTO.setInvoiceType(0);
        printerInvoiceRawDTO.setInvoiceName("invoiceName");
        final List<PrinterInvoiceRawDTO> printerInvoiceRawDTOS = Arrays.asList(printerInvoiceRawDTO);
        when(mockPrinterInvoiceService.listRaw("storeGuid")).thenReturn(printerInvoiceRawDTOS);

        // Configure PrinterFormatService.listRaw(...).
        final PrinterFormatRawDTO printerFormatRawDTO = new PrinterFormatRawDTO();
        printerFormatRawDTO.setGuid("1c5bc57b-ed45-4cbf-a166-cb2520a0fc17");
        printerFormatRawDTO.setName("name");
        printerFormatRawDTO.setStoreGuid("storeGuid");
        printerFormatRawDTO.setInvoiceType(0);
        printerFormatRawDTO.setFormatJsonString("formatJsonString");
        final List<PrinterFormatRawDTO> printerFormatRawDTOS = Arrays.asList(printerFormatRawDTO);
        when(mockPrinterFormatService.listRaw("storeGuid")).thenReturn(printerFormatRawDTOS);

        // Run the test and verify the results
        mockMvc.perform(post("/print_table/sync")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testSync_PrinterServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockPrinterService.listRaw("storeGuid")).thenReturn(Collections.emptyList());

        // Configure PrinterItemService.listRaw(...).
        final PrinterItemRawDTO printerItemRawDTO = new PrinterItemRawDTO();
        printerItemRawDTO.setGuid("8ac4a69d-8617-4f69-b558-2031a5c2a4ca");
        printerItemRawDTO.setStoreGuid("storeGuid");
        printerItemRawDTO.setPrinterGuid("printerGuid");
        printerItemRawDTO.setItemGuid("itemGuid");
        printerItemRawDTO.setItemName("itemName");
        final List<PrinterItemRawDTO> printerItemRawDTOS = Arrays.asList(printerItemRawDTO);
        when(mockPrinterItemService.listRaw("storeGuid")).thenReturn(printerItemRawDTOS);

        // Configure PrinterAreaService.listRaw(...).
        final PrinterAreaRawDTO printerAreaRawDTO = new PrinterAreaRawDTO();
        printerAreaRawDTO.setGuid("9ae079f2-b4f5-4679-9860-9556ab244c1d");
        printerAreaRawDTO.setStoreGuid("storeGuid");
        printerAreaRawDTO.setPrinterGuid("printerGuid");
        printerAreaRawDTO.setAreaGuid("areaGuid");
        printerAreaRawDTO.setAreaName("areaName");
        final List<PrinterAreaRawDTO> printerAreaRawDTOS = Arrays.asList(printerAreaRawDTO);
        when(mockPrinterAreaService.listRaw("storeGuid")).thenReturn(printerAreaRawDTOS);

        // Configure PrinterInvoiceService.listRaw(...).
        final PrinterInvoiceRawDTO printerInvoiceRawDTO = new PrinterInvoiceRawDTO();
        printerInvoiceRawDTO.setGuid("420e934a-2b51-4c9c-a80a-82645bec3489");
        printerInvoiceRawDTO.setStoreGuid("storeGuid");
        printerInvoiceRawDTO.setPrinterGuid("printerGuid");
        printerInvoiceRawDTO.setInvoiceType(0);
        printerInvoiceRawDTO.setInvoiceName("invoiceName");
        final List<PrinterInvoiceRawDTO> printerInvoiceRawDTOS = Arrays.asList(printerInvoiceRawDTO);
        when(mockPrinterInvoiceService.listRaw("storeGuid")).thenReturn(printerInvoiceRawDTOS);

        // Configure PrinterFormatService.listRaw(...).
        final PrinterFormatRawDTO printerFormatRawDTO = new PrinterFormatRawDTO();
        printerFormatRawDTO.setGuid("1c5bc57b-ed45-4cbf-a166-cb2520a0fc17");
        printerFormatRawDTO.setName("name");
        printerFormatRawDTO.setStoreGuid("storeGuid");
        printerFormatRawDTO.setInvoiceType(0);
        printerFormatRawDTO.setFormatJsonString("formatJsonString");
        final List<PrinterFormatRawDTO> printerFormatRawDTOS = Arrays.asList(printerFormatRawDTO);
        when(mockPrinterFormatService.listRaw("storeGuid")).thenReturn(printerFormatRawDTOS);

        // Run the test and verify the results
        mockMvc.perform(post("/print_table/sync")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testSync_PrinterItemServiceReturnsNoItems() throws Exception {
        // Setup
        // Configure PrinterService.listRaw(...).
        final PrinterRawDTO printerRawDTO = new PrinterRawDTO();
        printerRawDTO.setStoreGuid("storeGuid");
        printerRawDTO.setStoreName("storeName");
        printerRawDTO.setDeviceId("deviceId");
        printerRawDTO.setPrinterGuid("printerGuid");
        printerRawDTO.setPrinterName("printerName");
        final List<PrinterRawDTO> printerRawDTOS = Arrays.asList(printerRawDTO);
        when(mockPrinterService.listRaw("storeGuid")).thenReturn(printerRawDTOS);

        when(mockPrinterItemService.listRaw("storeGuid")).thenReturn(Collections.emptyList());

        // Configure PrinterAreaService.listRaw(...).
        final PrinterAreaRawDTO printerAreaRawDTO = new PrinterAreaRawDTO();
        printerAreaRawDTO.setGuid("9ae079f2-b4f5-4679-9860-9556ab244c1d");
        printerAreaRawDTO.setStoreGuid("storeGuid");
        printerAreaRawDTO.setPrinterGuid("printerGuid");
        printerAreaRawDTO.setAreaGuid("areaGuid");
        printerAreaRawDTO.setAreaName("areaName");
        final List<PrinterAreaRawDTO> printerAreaRawDTOS = Arrays.asList(printerAreaRawDTO);
        when(mockPrinterAreaService.listRaw("storeGuid")).thenReturn(printerAreaRawDTOS);

        // Configure PrinterInvoiceService.listRaw(...).
        final PrinterInvoiceRawDTO printerInvoiceRawDTO = new PrinterInvoiceRawDTO();
        printerInvoiceRawDTO.setGuid("420e934a-2b51-4c9c-a80a-82645bec3489");
        printerInvoiceRawDTO.setStoreGuid("storeGuid");
        printerInvoiceRawDTO.setPrinterGuid("printerGuid");
        printerInvoiceRawDTO.setInvoiceType(0);
        printerInvoiceRawDTO.setInvoiceName("invoiceName");
        final List<PrinterInvoiceRawDTO> printerInvoiceRawDTOS = Arrays.asList(printerInvoiceRawDTO);
        when(mockPrinterInvoiceService.listRaw("storeGuid")).thenReturn(printerInvoiceRawDTOS);

        // Configure PrinterFormatService.listRaw(...).
        final PrinterFormatRawDTO printerFormatRawDTO = new PrinterFormatRawDTO();
        printerFormatRawDTO.setGuid("1c5bc57b-ed45-4cbf-a166-cb2520a0fc17");
        printerFormatRawDTO.setName("name");
        printerFormatRawDTO.setStoreGuid("storeGuid");
        printerFormatRawDTO.setInvoiceType(0);
        printerFormatRawDTO.setFormatJsonString("formatJsonString");
        final List<PrinterFormatRawDTO> printerFormatRawDTOS = Arrays.asList(printerFormatRawDTO);
        when(mockPrinterFormatService.listRaw("storeGuid")).thenReturn(printerFormatRawDTOS);

        // Run the test and verify the results
        mockMvc.perform(post("/print_table/sync")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testSync_PrinterAreaServiceReturnsNoItems() throws Exception {
        // Setup
        // Configure PrinterService.listRaw(...).
        final PrinterRawDTO printerRawDTO = new PrinterRawDTO();
        printerRawDTO.setStoreGuid("storeGuid");
        printerRawDTO.setStoreName("storeName");
        printerRawDTO.setDeviceId("deviceId");
        printerRawDTO.setPrinterGuid("printerGuid");
        printerRawDTO.setPrinterName("printerName");
        final List<PrinterRawDTO> printerRawDTOS = Arrays.asList(printerRawDTO);
        when(mockPrinterService.listRaw("storeGuid")).thenReturn(printerRawDTOS);

        // Configure PrinterItemService.listRaw(...).
        final PrinterItemRawDTO printerItemRawDTO = new PrinterItemRawDTO();
        printerItemRawDTO.setGuid("8ac4a69d-8617-4f69-b558-2031a5c2a4ca");
        printerItemRawDTO.setStoreGuid("storeGuid");
        printerItemRawDTO.setPrinterGuid("printerGuid");
        printerItemRawDTO.setItemGuid("itemGuid");
        printerItemRawDTO.setItemName("itemName");
        final List<PrinterItemRawDTO> printerItemRawDTOS = Arrays.asList(printerItemRawDTO);
        when(mockPrinterItemService.listRaw("storeGuid")).thenReturn(printerItemRawDTOS);

        when(mockPrinterAreaService.listRaw("storeGuid")).thenReturn(Collections.emptyList());

        // Configure PrinterInvoiceService.listRaw(...).
        final PrinterInvoiceRawDTO printerInvoiceRawDTO = new PrinterInvoiceRawDTO();
        printerInvoiceRawDTO.setGuid("420e934a-2b51-4c9c-a80a-82645bec3489");
        printerInvoiceRawDTO.setStoreGuid("storeGuid");
        printerInvoiceRawDTO.setPrinterGuid("printerGuid");
        printerInvoiceRawDTO.setInvoiceType(0);
        printerInvoiceRawDTO.setInvoiceName("invoiceName");
        final List<PrinterInvoiceRawDTO> printerInvoiceRawDTOS = Arrays.asList(printerInvoiceRawDTO);
        when(mockPrinterInvoiceService.listRaw("storeGuid")).thenReturn(printerInvoiceRawDTOS);

        // Configure PrinterFormatService.listRaw(...).
        final PrinterFormatRawDTO printerFormatRawDTO = new PrinterFormatRawDTO();
        printerFormatRawDTO.setGuid("1c5bc57b-ed45-4cbf-a166-cb2520a0fc17");
        printerFormatRawDTO.setName("name");
        printerFormatRawDTO.setStoreGuid("storeGuid");
        printerFormatRawDTO.setInvoiceType(0);
        printerFormatRawDTO.setFormatJsonString("formatJsonString");
        final List<PrinterFormatRawDTO> printerFormatRawDTOS = Arrays.asList(printerFormatRawDTO);
        when(mockPrinterFormatService.listRaw("storeGuid")).thenReturn(printerFormatRawDTOS);

        // Run the test and verify the results
        mockMvc.perform(post("/print_table/sync")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testSync_PrinterInvoiceServiceReturnsNoItems() throws Exception {
        // Setup
        // Configure PrinterService.listRaw(...).
        final PrinterRawDTO printerRawDTO = new PrinterRawDTO();
        printerRawDTO.setStoreGuid("storeGuid");
        printerRawDTO.setStoreName("storeName");
        printerRawDTO.setDeviceId("deviceId");
        printerRawDTO.setPrinterGuid("printerGuid");
        printerRawDTO.setPrinterName("printerName");
        final List<PrinterRawDTO> printerRawDTOS = Arrays.asList(printerRawDTO);
        when(mockPrinterService.listRaw("storeGuid")).thenReturn(printerRawDTOS);

        // Configure PrinterItemService.listRaw(...).
        final PrinterItemRawDTO printerItemRawDTO = new PrinterItemRawDTO();
        printerItemRawDTO.setGuid("8ac4a69d-8617-4f69-b558-2031a5c2a4ca");
        printerItemRawDTO.setStoreGuid("storeGuid");
        printerItemRawDTO.setPrinterGuid("printerGuid");
        printerItemRawDTO.setItemGuid("itemGuid");
        printerItemRawDTO.setItemName("itemName");
        final List<PrinterItemRawDTO> printerItemRawDTOS = Arrays.asList(printerItemRawDTO);
        when(mockPrinterItemService.listRaw("storeGuid")).thenReturn(printerItemRawDTOS);

        // Configure PrinterAreaService.listRaw(...).
        final PrinterAreaRawDTO printerAreaRawDTO = new PrinterAreaRawDTO();
        printerAreaRawDTO.setGuid("9ae079f2-b4f5-4679-9860-9556ab244c1d");
        printerAreaRawDTO.setStoreGuid("storeGuid");
        printerAreaRawDTO.setPrinterGuid("printerGuid");
        printerAreaRawDTO.setAreaGuid("areaGuid");
        printerAreaRawDTO.setAreaName("areaName");
        final List<PrinterAreaRawDTO> printerAreaRawDTOS = Arrays.asList(printerAreaRawDTO);
        when(mockPrinterAreaService.listRaw("storeGuid")).thenReturn(printerAreaRawDTOS);

        when(mockPrinterInvoiceService.listRaw("storeGuid")).thenReturn(Collections.emptyList());

        // Configure PrinterFormatService.listRaw(...).
        final PrinterFormatRawDTO printerFormatRawDTO = new PrinterFormatRawDTO();
        printerFormatRawDTO.setGuid("1c5bc57b-ed45-4cbf-a166-cb2520a0fc17");
        printerFormatRawDTO.setName("name");
        printerFormatRawDTO.setStoreGuid("storeGuid");
        printerFormatRawDTO.setInvoiceType(0);
        printerFormatRawDTO.setFormatJsonString("formatJsonString");
        final List<PrinterFormatRawDTO> printerFormatRawDTOS = Arrays.asList(printerFormatRawDTO);
        when(mockPrinterFormatService.listRaw("storeGuid")).thenReturn(printerFormatRawDTOS);

        // Run the test and verify the results
        mockMvc.perform(post("/print_table/sync")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testSync_PrinterFormatServiceReturnsNoItems() throws Exception {
        // Setup
        // Configure PrinterService.listRaw(...).
        final PrinterRawDTO printerRawDTO = new PrinterRawDTO();
        printerRawDTO.setStoreGuid("storeGuid");
        printerRawDTO.setStoreName("storeName");
        printerRawDTO.setDeviceId("deviceId");
        printerRawDTO.setPrinterGuid("printerGuid");
        printerRawDTO.setPrinterName("printerName");
        final List<PrinterRawDTO> printerRawDTOS = Arrays.asList(printerRawDTO);
        when(mockPrinterService.listRaw("storeGuid")).thenReturn(printerRawDTOS);

        // Configure PrinterItemService.listRaw(...).
        final PrinterItemRawDTO printerItemRawDTO = new PrinterItemRawDTO();
        printerItemRawDTO.setGuid("8ac4a69d-8617-4f69-b558-2031a5c2a4ca");
        printerItemRawDTO.setStoreGuid("storeGuid");
        printerItemRawDTO.setPrinterGuid("printerGuid");
        printerItemRawDTO.setItemGuid("itemGuid");
        printerItemRawDTO.setItemName("itemName");
        final List<PrinterItemRawDTO> printerItemRawDTOS = Arrays.asList(printerItemRawDTO);
        when(mockPrinterItemService.listRaw("storeGuid")).thenReturn(printerItemRawDTOS);

        // Configure PrinterAreaService.listRaw(...).
        final PrinterAreaRawDTO printerAreaRawDTO = new PrinterAreaRawDTO();
        printerAreaRawDTO.setGuid("9ae079f2-b4f5-4679-9860-9556ab244c1d");
        printerAreaRawDTO.setStoreGuid("storeGuid");
        printerAreaRawDTO.setPrinterGuid("printerGuid");
        printerAreaRawDTO.setAreaGuid("areaGuid");
        printerAreaRawDTO.setAreaName("areaName");
        final List<PrinterAreaRawDTO> printerAreaRawDTOS = Arrays.asList(printerAreaRawDTO);
        when(mockPrinterAreaService.listRaw("storeGuid")).thenReturn(printerAreaRawDTOS);

        // Configure PrinterInvoiceService.listRaw(...).
        final PrinterInvoiceRawDTO printerInvoiceRawDTO = new PrinterInvoiceRawDTO();
        printerInvoiceRawDTO.setGuid("420e934a-2b51-4c9c-a80a-82645bec3489");
        printerInvoiceRawDTO.setStoreGuid("storeGuid");
        printerInvoiceRawDTO.setPrinterGuid("printerGuid");
        printerInvoiceRawDTO.setInvoiceType(0);
        printerInvoiceRawDTO.setInvoiceName("invoiceName");
        final List<PrinterInvoiceRawDTO> printerInvoiceRawDTOS = Arrays.asList(printerInvoiceRawDTO);
        when(mockPrinterInvoiceService.listRaw("storeGuid")).thenReturn(printerInvoiceRawDTOS);

        when(mockPrinterFormatService.listRaw("storeGuid")).thenReturn(Collections.emptyList());

        // Run the test and verify the results
        mockMvc.perform(post("/print_table/sync")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }
}
