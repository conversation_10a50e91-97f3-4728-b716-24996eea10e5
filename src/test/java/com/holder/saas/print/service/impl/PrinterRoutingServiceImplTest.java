package com.holder.saas.print.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holder.saas.print.entity.domain.PrinterDO;
import com.holder.saas.print.entity.query.PrinterQuery;
import com.holder.saas.print.entity.read.PrinterReadDO;
import com.holder.saas.print.service.PrinterService;
import com.holder.saas.print.service.feign.ItemClientService;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.item.req.ItemSpuReqDTO;
import com.holderzone.saas.store.dto.print.content.PrintDTO;
import com.holderzone.saas.store.enums.print.PrintSourceEnum;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PrinterRoutingServiceImplTest {

    @Mock
    private PrinterService mockPrinterService;
    @Mock
    private ItemClientService mockItemClientService;

    private PrinterRoutingServiceImpl printerRoutingServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        printerRoutingServiceImplUnderTest = new PrinterRoutingServiceImpl(mockPrinterService, mockItemClientService);
    }

    @Test
    public void testFindPrinterAvailable() {
        // Setup
        final PrintDTO printDto = new PrintDTO();
        printDto.setInvoiceType(0);
        printDto.setEnterpriseGuid("enterpriseGuid");
        printDto.setStoreGuid("storeGuid");
        printDto.setPrintUid("printUid");
        printDto.setAreaGuid("areaGuid");
        printDto.setDeviceId("deviceId");
        printDto.setPrintSourceEnum(PrintSourceEnum.AIO);

        final PrinterReadDO printerReadDO = new PrinterReadDO();
        printerReadDO.setId(0L);
        printerReadDO.setPrinterGuid("000000");
        printerReadDO.setPrinterType(0);
        printerReadDO.setPrintCount(0);
        printerReadDO.setPrintPage("58");
        final List<PrinterReadDO> expectedResult = Arrays.asList(printerReadDO);
        when(mockPrinterService.findMasterPrinterDeviceIdOrElseThrow("storeGuid")).thenReturn("deviceId");

        // Configure ItemClientService.selectSpuItems(...).
        final ItemSpuReqDTO itemSpuReqDTO = new ItemSpuReqDTO();
        itemSpuReqDTO.setStoreGuid("storeGuid");
        itemSpuReqDTO.setItemGuids(Arrays.asList("value"));
        when(mockItemClientService.selectSpuItems(itemSpuReqDTO)).thenReturn(Arrays.asList("value"));

        // Configure PrinterService.findPrinterOfKitchenTable(...).
        final PrinterReadDO printerReadDO1 = new PrinterReadDO();
        printerReadDO1.setId(0L);
        printerReadDO1.setPrinterGuid("000000");
        printerReadDO1.setPrinterType(0);
        printerReadDO1.setPrintCount(0);
        printerReadDO1.setPrintPage("58");
        final List<PrinterReadDO> printerReadDOS = Arrays.asList(printerReadDO1);
        final PrinterQuery printerQuery = new PrinterQuery();
        printerQuery.setStoreGuid("storeGuid");
        printerQuery.setInvoiceType(0);
        printerQuery.setDeviceId("deviceId");
        printerQuery.setAreaGuid("areaGuid");
        printerQuery.setArrayOfItemGuid(Arrays.asList("value"));
        printerQuery.setBusinessType(0);
        when(mockPrinterService.findPrinterOfKitchenTable(printerQuery)).thenReturn(printerReadDOS);

        // Configure PrinterService.findPrinterByQuery(...).
        final PrinterReadDO printerReadDO2 = new PrinterReadDO();
        printerReadDO2.setId(0L);
        printerReadDO2.setPrinterGuid("000000");
        printerReadDO2.setPrinterType(0);
        printerReadDO2.setPrintCount(0);
        printerReadDO2.setPrintPage("58");
        final List<PrinterReadDO> printerReadDOS1 = Arrays.asList(printerReadDO2);
        final PrinterQuery printerQuery1 = new PrinterQuery();
        printerQuery1.setStoreGuid("storeGuid");
        printerQuery1.setInvoiceType(0);
        printerQuery1.setDeviceId("deviceId");
        printerQuery1.setAreaGuid("areaGuid");
        printerQuery1.setArrayOfItemGuid(Arrays.asList("value"));
        printerQuery1.setBusinessType(0);
        when(mockPrinterService.findPrinterByQuery(printerQuery1)).thenReturn(printerReadDOS1);

        when(mockPrinterService.count(any(LambdaQueryWrapper.class))).thenReturn(0);

        // Run the test
        final List<PrinterReadDO> result = printerRoutingServiceImplUnderTest.findPrinterAvailable(printDto);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testFindPrinterAvailable_ItemClientServiceReturnsNoItems() {
        // Setup
        final PrintDTO printDto = new PrintDTO();
        printDto.setInvoiceType(0);
        printDto.setEnterpriseGuid("enterpriseGuid");
        printDto.setStoreGuid("storeGuid");
        printDto.setPrintUid("printUid");
        printDto.setAreaGuid("areaGuid");
        printDto.setDeviceId("deviceId");
        printDto.setPrintSourceEnum(PrintSourceEnum.AIO);

        when(mockPrinterService.findMasterPrinterDeviceIdOrElseThrow("storeGuid")).thenReturn("deviceId");

        // Configure ItemClientService.selectSpuItems(...).
        final ItemSpuReqDTO itemSpuReqDTO = new ItemSpuReqDTO();
        itemSpuReqDTO.setStoreGuid("storeGuid");
        itemSpuReqDTO.setItemGuids(Arrays.asList("value"));
        when(mockItemClientService.selectSpuItems(itemSpuReqDTO)).thenReturn(Collections.emptyList());

        // Run the test
        assertThatThrownBy(() -> printerRoutingServiceImplUnderTest.findPrinterAvailable(printDto))
                .isInstanceOf(BusinessException.class);
    }

    @Test
    public void testFindPrinterAvailable_PrinterServiceFindPrinterOfKitchenTableReturnsNoItems() {
        // Setup
        final PrintDTO printDto = new PrintDTO();
        printDto.setInvoiceType(0);
        printDto.setEnterpriseGuid("enterpriseGuid");
        printDto.setStoreGuid("storeGuid");
        printDto.setPrintUid("printUid");
        printDto.setAreaGuid("areaGuid");
        printDto.setDeviceId("deviceId");
        printDto.setPrintSourceEnum(PrintSourceEnum.AIO);

        when(mockPrinterService.findMasterPrinterDeviceIdOrElseThrow("storeGuid")).thenReturn("deviceId");

        // Configure ItemClientService.selectSpuItems(...).
        final ItemSpuReqDTO itemSpuReqDTO = new ItemSpuReqDTO();
        itemSpuReqDTO.setStoreGuid("storeGuid");
        itemSpuReqDTO.setItemGuids(Arrays.asList("value"));
        when(mockItemClientService.selectSpuItems(itemSpuReqDTO)).thenReturn(Arrays.asList("value"));

        // Configure PrinterService.findPrinterOfKitchenTable(...).
        final PrinterQuery printerQuery = new PrinterQuery();
        printerQuery.setStoreGuid("storeGuid");
        printerQuery.setInvoiceType(0);
        printerQuery.setDeviceId("deviceId");
        printerQuery.setAreaGuid("areaGuid");
        printerQuery.setArrayOfItemGuid(Arrays.asList("value"));
        printerQuery.setBusinessType(0);
        when(mockPrinterService.findPrinterOfKitchenTable(printerQuery)).thenReturn(Collections.emptyList());

        when(mockPrinterService.count(any(LambdaQueryWrapper.class))).thenReturn(0);

        // Run the test
        final List<PrinterReadDO> result = printerRoutingServiceImplUnderTest.findPrinterAvailable(printDto);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testFindPrinterAvailable_PrinterServiceFindPrinterByQueryReturnsNoItems() {
        // Setup
        final PrintDTO printDto = new PrintDTO();
        printDto.setInvoiceType(0);
        printDto.setEnterpriseGuid("enterpriseGuid");
        printDto.setStoreGuid("storeGuid");
        printDto.setPrintUid("printUid");
        printDto.setAreaGuid("areaGuid");
        printDto.setDeviceId("deviceId");
        printDto.setPrintSourceEnum(PrintSourceEnum.AIO);

        when(mockPrinterService.findMasterPrinterDeviceIdOrElseThrow("storeGuid")).thenReturn("deviceId");

        // Configure ItemClientService.selectSpuItems(...).
        final ItemSpuReqDTO itemSpuReqDTO = new ItemSpuReqDTO();
        itemSpuReqDTO.setStoreGuid("storeGuid");
        itemSpuReqDTO.setItemGuids(Arrays.asList("value"));
        when(mockItemClientService.selectSpuItems(itemSpuReqDTO)).thenReturn(Arrays.asList("value"));

        // Configure PrinterService.findPrinterByQuery(...).
        final PrinterQuery printerQuery = new PrinterQuery();
        printerQuery.setStoreGuid("storeGuid");
        printerQuery.setInvoiceType(0);
        printerQuery.setDeviceId("deviceId");
        printerQuery.setAreaGuid("areaGuid");
        printerQuery.setArrayOfItemGuid(Arrays.asList("value"));
        printerQuery.setBusinessType(0);
        when(mockPrinterService.findPrinterByQuery(printerQuery)).thenReturn(Collections.emptyList());

        when(mockPrinterService.count(any(LambdaQueryWrapper.class))).thenReturn(0);

        // Run the test
        final List<PrinterReadDO> result = printerRoutingServiceImplUnderTest.findPrinterAvailable(printDto);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testFindFakePrinterReadDO() {
        // Setup
        final PrinterReadDO expectedResult = new PrinterReadDO();
        expectedResult.setId(0L);
        expectedResult.setPrinterGuid("000000");
        expectedResult.setPrinterType(0);
        expectedResult.setPrintCount(0);
        expectedResult.setPrintPage("58");

        // Run the test
        final PrinterReadDO result = printerRoutingServiceImplUnderTest.findFakePrinterReadDO();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testFindFakePrinterDO() {
        // Setup
        final PrinterDO expectedResult = new PrinterDO();
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setDeviceId("deviceId");
        expectedResult.setPrinterName("本机");
        expectedResult.setBusinessType(0);
        expectedResult.setPrinterType(0);

        // Run the test
        final PrinterDO result = printerRoutingServiceImplUnderTest.findFakePrinterDO();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testFindCloudPrinterAvailable() {
        // Setup
        final PrintDTO printDTO = new PrintDTO();
        printDTO.setInvoiceType(0);
        printDTO.setEnterpriseGuid("enterpriseGuid");
        printDTO.setStoreGuid("storeGuid");
        printDTO.setPrintUid("printUid");
        printDTO.setAreaGuid("areaGuid");
        printDTO.setDeviceId("deviceId");
        printDTO.setPrintSourceEnum(PrintSourceEnum.AIO);

        final PrinterReadDO printerReadDO = new PrinterReadDO();
        printerReadDO.setId(0L);
        printerReadDO.setPrinterGuid("000000");
        printerReadDO.setPrinterType(0);
        printerReadDO.setPrintCount(0);
        printerReadDO.setPrintPage("58");
        final List<PrinterReadDO> expectedResult = Arrays.asList(printerReadDO);

        // Configure ItemClientService.selectSpuItems(...).
        final ItemSpuReqDTO itemSpuReqDTO = new ItemSpuReqDTO();
        itemSpuReqDTO.setStoreGuid("storeGuid");
        itemSpuReqDTO.setItemGuids(Arrays.asList("value"));
        when(mockItemClientService.selectSpuItems(itemSpuReqDTO)).thenReturn(Arrays.asList("value"));

        // Configure PrinterService.findCloudPrinterByQuery(...).
        final PrinterReadDO printerReadDO1 = new PrinterReadDO();
        printerReadDO1.setId(0L);
        printerReadDO1.setPrinterGuid("000000");
        printerReadDO1.setPrinterType(0);
        printerReadDO1.setPrintCount(0);
        printerReadDO1.setPrintPage("58");
        final List<PrinterReadDO> printerReadDOS = Arrays.asList(printerReadDO1);
        final PrinterQuery printerQuery = new PrinterQuery();
        printerQuery.setStoreGuid("storeGuid");
        printerQuery.setInvoiceType(0);
        printerQuery.setDeviceId("deviceId");
        printerQuery.setAreaGuid("areaGuid");
        printerQuery.setArrayOfItemGuid(Arrays.asList("value"));
        printerQuery.setBusinessType(0);
        when(mockPrinterService.findCloudPrinterByQuery(printerQuery)).thenReturn(printerReadDOS);

        // Run the test
        final List<PrinterReadDO> result = printerRoutingServiceImplUnderTest.findCloudPrinterAvailable(printDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testFindCloudPrinterAvailable_ItemClientServiceReturnsNoItems() {
        // Setup
        final PrintDTO printDTO = new PrintDTO();
        printDTO.setInvoiceType(0);
        printDTO.setEnterpriseGuid("enterpriseGuid");
        printDTO.setStoreGuid("storeGuid");
        printDTO.setPrintUid("printUid");
        printDTO.setAreaGuid("areaGuid");
        printDTO.setDeviceId("deviceId");
        printDTO.setPrintSourceEnum(PrintSourceEnum.AIO);

        // Configure ItemClientService.selectSpuItems(...).
        final ItemSpuReqDTO itemSpuReqDTO = new ItemSpuReqDTO();
        itemSpuReqDTO.setStoreGuid("storeGuid");
        itemSpuReqDTO.setItemGuids(Arrays.asList("value"));
        when(mockItemClientService.selectSpuItems(itemSpuReqDTO)).thenReturn(Collections.emptyList());

        // Run the test
        assertThatThrownBy(() -> printerRoutingServiceImplUnderTest.findCloudPrinterAvailable(printDTO))
                .isInstanceOf(BusinessException.class);
    }

    @Test
    public void testFindCloudPrinterAvailable_PrinterServiceReturnsNoItems() {
        // Setup
        final PrintDTO printDTO = new PrintDTO();
        printDTO.setInvoiceType(0);
        printDTO.setEnterpriseGuid("enterpriseGuid");
        printDTO.setStoreGuid("storeGuid");
        printDTO.setPrintUid("printUid");
        printDTO.setAreaGuid("areaGuid");
        printDTO.setDeviceId("deviceId");
        printDTO.setPrintSourceEnum(PrintSourceEnum.AIO);

        // Configure ItemClientService.selectSpuItems(...).
        final ItemSpuReqDTO itemSpuReqDTO = new ItemSpuReqDTO();
        itemSpuReqDTO.setStoreGuid("storeGuid");
        itemSpuReqDTO.setItemGuids(Arrays.asList("value"));
        when(mockItemClientService.selectSpuItems(itemSpuReqDTO)).thenReturn(Arrays.asList("value"));

        // Configure PrinterService.findCloudPrinterByQuery(...).
        final PrinterQuery printerQuery = new PrinterQuery();
        printerQuery.setStoreGuid("storeGuid");
        printerQuery.setInvoiceType(0);
        printerQuery.setDeviceId("deviceId");
        printerQuery.setAreaGuid("areaGuid");
        printerQuery.setArrayOfItemGuid(Arrays.asList("value"));
        printerQuery.setBusinessType(0);
        when(mockPrinterService.findCloudPrinterByQuery(printerQuery)).thenReturn(Collections.emptyList());

        // Run the test
        final List<PrinterReadDO> result = printerRoutingServiceImplUnderTest.findCloudPrinterAvailable(printDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }
}
