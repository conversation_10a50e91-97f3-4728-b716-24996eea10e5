package com.holder.saas.print.service.impl;

import com.holder.saas.print.entity.read.PrintRecordReadDO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ContentCacheServiceImplTest {

    @Mock
    private RedisTemplate<String, Object> mockRedisTemplate;

    private ContentCacheServiceImpl contentCacheServiceImplUnderTest;

    @Before
    public void setUp() {
        contentCacheServiceImplUnderTest = new ContentCacheServiceImpl(mockRedisTemplate);
        ReflectionTestUtils.setField(contentCacheServiceImplUnderTest, "batchPushEnable", false);
    }

    @Test
    public void testSave() {
        // Setup
        final PrintRecordReadDO printRecordReadDO = new PrintRecordReadDO();
        printRecordReadDO.setId(0L);
        printRecordReadDO.setRecordUid("recordUid");
        printRecordReadDO.setRecordGuid("recordGuid");
        printRecordReadDO.setStoreGuid("storeGuid");
        printRecordReadDO.setDeviceId("deviceId");
        final List<PrintRecordReadDO> arrayOfPrintRecordReadDO = Arrays.asList(printRecordReadDO);
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        contentCacheServiceImplUnderTest.save(arrayOfPrintRecordReadDO);

        // Verify the results
    }

    @Test
    public void testPopSingle() {
        // Setup
        final PrintRecordReadDO expectedResult = new PrintRecordReadDO();
        expectedResult.setId(0L);
        expectedResult.setRecordUid("recordUid");
        expectedResult.setRecordGuid("recordGuid");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setDeviceId("deviceId");

        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        final PrintRecordReadDO result = contentCacheServiceImplUnderTest.popSingle("recordGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockRedisTemplate).delete("key");
    }

    @Test
    public void testPopBatch() {
        // Setup
        final PrintRecordReadDO printRecordReadDO = new PrintRecordReadDO();
        printRecordReadDO.setId(0L);
        printRecordReadDO.setRecordUid("recordUid");
        printRecordReadDO.setRecordGuid("recordGuid");
        printRecordReadDO.setStoreGuid("storeGuid");
        printRecordReadDO.setDeviceId("deviceId");
        final List<PrintRecordReadDO> expectedResult = Arrays.asList(printRecordReadDO);
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        final List<PrintRecordReadDO> result = contentCacheServiceImplUnderTest.popBatch(Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockRedisTemplate).delete("key");
    }

    @Test
    public void testHasMsgId() {
        // Setup
        when(mockRedisTemplate.hasKey("key")).thenReturn(false);

        // Run the test
        final boolean result = contentCacheServiceImplUnderTest.hasMsgId("msgId", "recordGuid", Arrays.asList("value"));

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testHasMsgId_RedisTemplateReturnsNull() {
        // Setup
        when(mockRedisTemplate.hasKey("key")).thenReturn(null);

        // Run the test
        final boolean result = contentCacheServiceImplUnderTest.hasMsgId("msgId", "recordGuid", Arrays.asList("value"));

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testSaveMsgId() {
        // Setup
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        contentCacheServiceImplUnderTest.saveMsgId("msgId", "recordGuid", Arrays.asList("value"));

        // Verify the results
    }
}
