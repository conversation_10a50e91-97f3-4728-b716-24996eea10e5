package com.holder.saas.print.service.impl;

import com.holder.saas.print.entity.domain.PrinterAreaDO;
import com.holder.saas.print.mapstruct.PrinterMapstruct;
import com.holder.saas.print.mapstruct.PrinterRawMaptstruct;
import com.holder.saas.print.service.DistributedService;
import com.holderzone.saas.store.dto.print.PrinterAreaDTO;
import com.holderzone.saas.store.dto.print.PrinterDTO;
import com.holderzone.saas.store.dto.print.raw.PrinterAreaRawDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PrinterAreaServiceImplTest {

    @Mock
    private DistributedService mockDistributedService;
    @Mock
    private PrinterMapstruct mockPrinterMapstruct;
    @Mock
    private PrinterRawMaptstruct mockPrinterRawMaptstruct;

    private PrinterAreaServiceImpl printerAreaServiceImplUnderTest;

    @Before
    public void setUp() {
        printerAreaServiceImplUnderTest = new PrinterAreaServiceImpl(mockDistributedService, mockPrinterMapstruct,
                mockPrinterRawMaptstruct);
    }

    @Test
    public void testBindPrinterArea() {
        // Setup
        final PrinterDTO printerDTO = new PrinterDTO();
        printerDTO.setStoreGuid("storeGuid");
        printerDTO.setPrinterGuid("printerGuid");
        final PrinterAreaDTO printerAreaDTO = new PrinterAreaDTO();
        printerAreaDTO.setAreaGuid("areaGuid");
        printerAreaDTO.setAreaName("areaName");
        printerDTO.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO));

        when(mockDistributedService.nextBatchPrinterAreaGuid(0L)).thenReturn(Arrays.asList("value"));

        // Run the test
        printerAreaServiceImplUnderTest.bindPrinterArea(printerDTO);

        // Verify the results
    }

    @Test
    public void testBindPrinterArea_DistributedServiceReturnsNoItems() {
        // Setup
        final PrinterDTO printerDTO = new PrinterDTO();
        printerDTO.setStoreGuid("storeGuid");
        printerDTO.setPrinterGuid("printerGuid");
        final PrinterAreaDTO printerAreaDTO = new PrinterAreaDTO();
        printerAreaDTO.setAreaGuid("areaGuid");
        printerAreaDTO.setAreaName("areaName");
        printerDTO.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO));

        when(mockDistributedService.nextBatchPrinterAreaGuid(0L)).thenReturn(Collections.emptyList());

        // Run the test
        printerAreaServiceImplUnderTest.bindPrinterArea(printerDTO);

        // Verify the results
    }

    @Test
    public void testListPrinterArea() {
        // Setup
        final PrinterDTO printerDTO = new PrinterDTO();
        printerDTO.setStoreGuid("storeGuid");
        printerDTO.setPrinterGuid("printerGuid");
        final PrinterAreaDTO printerAreaDTO = new PrinterAreaDTO();
        printerAreaDTO.setAreaGuid("areaGuid");
        printerAreaDTO.setAreaName("areaName");
        printerDTO.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO));

        final List<PrinterAreaDTO> expectedResult = Arrays.asList(new PrinterAreaDTO("areaGuid", "areaName"));

        // Configure PrinterMapstruct.toPrinterAreaDTO(...).
        final List<PrinterAreaDTO> printerAreaDTOS = Arrays.asList(new PrinterAreaDTO("areaGuid", "areaName"));
        final PrinterAreaDO printerAreaDO1 = new PrinterAreaDO();
        printerAreaDO1.setGuid("44cbb6b3-8b67-4c43-9c80-b0d2a53717d6");
        printerAreaDO1.setStoreGuid("storeGuid");
        printerAreaDO1.setPrinterGuid("printerGuid");
        printerAreaDO1.setAreaGuid("areaGuid");
        printerAreaDO1.setAreaName("areaName");
        final List<PrinterAreaDO> printerAreaDO = Arrays.asList(printerAreaDO1);
        when(mockPrinterMapstruct.toPrinterAreaDTO(printerAreaDO)).thenReturn(printerAreaDTOS);

        // Run the test
        final List<PrinterAreaDTO> result = printerAreaServiceImplUnderTest.listPrinterArea(printerDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testListPrinterArea_PrinterMapstructReturnsNoItems() {
        // Setup
        final PrinterDTO printerDTO = new PrinterDTO();
        printerDTO.setStoreGuid("storeGuid");
        printerDTO.setPrinterGuid("printerGuid");
        final PrinterAreaDTO printerAreaDTO = new PrinterAreaDTO();
        printerAreaDTO.setAreaGuid("areaGuid");
        printerAreaDTO.setAreaName("areaName");
        printerDTO.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO));

        // Configure PrinterMapstruct.toPrinterAreaDTO(...).
        final PrinterAreaDO printerAreaDO1 = new PrinterAreaDO();
        printerAreaDO1.setGuid("44cbb6b3-8b67-4c43-9c80-b0d2a53717d6");
        printerAreaDO1.setStoreGuid("storeGuid");
        printerAreaDO1.setPrinterGuid("printerGuid");
        printerAreaDO1.setAreaGuid("areaGuid");
        printerAreaDO1.setAreaName("areaName");
        final List<PrinterAreaDO> printerAreaDO = Arrays.asList(printerAreaDO1);
        when(mockPrinterMapstruct.toPrinterAreaDTO(printerAreaDO)).thenReturn(Collections.emptyList());

        // Run the test
        final List<PrinterAreaDTO> result = printerAreaServiceImplUnderTest.listPrinterArea(printerDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testDeletePrinterArea() {
        // Setup
        // Run the test
        printerAreaServiceImplUnderTest.deletePrinterArea("printerGuid");

        // Verify the results
    }

    @Test
    public void testBatchDeletePrinterArea() {
        // Setup
        // Run the test
        printerAreaServiceImplUnderTest.batchDeletePrinterArea(Arrays.asList("value"));

        // Verify the results
    }

    @Test
    public void testDeleteStorePrinterArea() {
        // Setup
        // Run the test
        printerAreaServiceImplUnderTest.deleteStorePrinterArea("storeGuid");

        // Verify the results
    }

    @Test
    public void testBatchDeleteStorePrinterArea() {
        // Setup
        // Run the test
        printerAreaServiceImplUnderTest.batchDeleteStorePrinterArea(Arrays.asList("value"));

        // Verify the results
    }

    @Test
    public void testListRaw() {
        // Setup
        final PrinterAreaRawDTO printerAreaRawDTO = new PrinterAreaRawDTO();
        printerAreaRawDTO.setGuid("3a97a440-5402-45c9-a357-419b5c14ab2e");
        printerAreaRawDTO.setStoreGuid("storeGuid");
        printerAreaRawDTO.setPrinterGuid("printerGuid");
        printerAreaRawDTO.setAreaGuid("areaGuid");
        printerAreaRawDTO.setAreaName("areaName");
        final List<PrinterAreaRawDTO> expectedResult = Arrays.asList(printerAreaRawDTO);

        // Configure PrinterRawMaptstruct.toAreaRawDTO(...).
        final PrinterAreaRawDTO printerAreaRawDTO1 = new PrinterAreaRawDTO();
        printerAreaRawDTO1.setGuid("3a97a440-5402-45c9-a357-419b5c14ab2e");
        printerAreaRawDTO1.setStoreGuid("storeGuid");
        printerAreaRawDTO1.setPrinterGuid("printerGuid");
        printerAreaRawDTO1.setAreaGuid("areaGuid");
        printerAreaRawDTO1.setAreaName("areaName");
        final List<PrinterAreaRawDTO> printerAreaRawDTOS = Arrays.asList(printerAreaRawDTO1);
        final PrinterAreaDO printerAreaDO = new PrinterAreaDO();
        printerAreaDO.setGuid("44cbb6b3-8b67-4c43-9c80-b0d2a53717d6");
        printerAreaDO.setStoreGuid("storeGuid");
        printerAreaDO.setPrinterGuid("printerGuid");
        printerAreaDO.setAreaGuid("areaGuid");
        printerAreaDO.setAreaName("areaName");
        final List<PrinterAreaDO> list = Arrays.asList(printerAreaDO);
        when(mockPrinterRawMaptstruct.toAreaRawDTO(list)).thenReturn(printerAreaRawDTOS);

        // Run the test
        final List<PrinterAreaRawDTO> result = printerAreaServiceImplUnderTest.listRaw("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testListRaw_PrinterRawMaptstructReturnsNoItems() {
        // Setup
        // Configure PrinterRawMaptstruct.toAreaRawDTO(...).
        final PrinterAreaDO printerAreaDO = new PrinterAreaDO();
        printerAreaDO.setGuid("44cbb6b3-8b67-4c43-9c80-b0d2a53717d6");
        printerAreaDO.setStoreGuid("storeGuid");
        printerAreaDO.setPrinterGuid("printerGuid");
        printerAreaDO.setAreaGuid("areaGuid");
        printerAreaDO.setAreaName("areaName");
        final List<PrinterAreaDO> list = Arrays.asList(printerAreaDO);
        when(mockPrinterRawMaptstruct.toAreaRawDTO(list)).thenReturn(Collections.emptyList());

        // Run the test
        final List<PrinterAreaRawDTO> result = printerAreaServiceImplUnderTest.listRaw("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }
}
