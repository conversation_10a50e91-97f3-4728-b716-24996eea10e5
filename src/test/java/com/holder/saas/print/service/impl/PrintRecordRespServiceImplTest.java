package com.holder.saas.print.service.impl;

import com.holder.saas.print.entity.domain.PrinterDO;
import com.holder.saas.print.entity.read.PrintRecordReadDO;
import com.holder.saas.print.mapstruct.PrintRecordMapstruct;
import com.holder.saas.print.template.factory.PrintTemplateFactory;
import com.holderzone.saas.store.dto.print.PrintOrderDTO;
import com.holderzone.saas.store.dto.print.PrintRecordDTO;
import com.holderzone.saas.store.dto.print.PrinterDTO;
import com.holderzone.saas.store.dto.print.content.PrintDTO;
import com.holderzone.saas.store.dto.print.format.FormatDTO;
import com.holderzone.saas.store.dto.print.template.PrintRow;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PrintRecordRespServiceImplTest {

    @Mock
    private PrintRecordMapstruct mockPrintRecordMapstruct;
    @Mock
    private PrintTemplateFactory mockPrintTemplateFactory;

    private PrintRecordRespServiceImpl printRecordRespServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        printRecordRespServiceImplUnderTest = new PrintRecordRespServiceImpl(mockPrintRecordMapstruct,
                mockPrintTemplateFactory);
        ReflectionTestUtils.setField(printRecordRespServiceImplUnderTest, "takeoutWhitelist", "takeoutWhitelist");
        ReflectionTestUtils.setField(printRecordRespServiceImplUnderTest, "takeoutBlacklist", "takeoutBlacklist");
    }

    @Test
    public void testDo2Dto() {
        // Setup
        final PrintRecordReadDO printRecordReadDO = new PrintRecordReadDO();
        printRecordReadDO.setRecordGuid("mock_key");
        printRecordReadDO.setStoreGuid("storeGuid");
        printRecordReadDO.setInvoiceType(0);
        printRecordReadDO.setPrintContent("printContent");
        final PrinterDO printerDO = new PrinterDO();
        printerDO.setPrinterName("printerName");
        printerDO.setBusinessType(0);
        printerDO.setPrinterType(0);
        printerDO.setPrinterIp("USB");
        printerDO.setPrinterPort(0);
        printerDO.setPrintCount(0);
        printerDO.setPrintPage("58");
        printRecordReadDO.setPrinterDO(printerDO);

        final PrintRecordDTO expectedResult = new PrintRecordDTO();
        expectedResult.setRecordUid("markName");
        final PrintDTO printContent = new PrintDTO();
        printContent.setInvoiceType(0);
        printContent.setEnterpriseGuid("enterpriseGuid");
        printContent.setStoreGuid("storeGuid");
        expectedResult.setPrintContent(printContent);
        expectedResult.setMarkName("markName");
        expectedResult.setMarkNo("markName");
        expectedResult.setPrinterType(0);
        expectedResult.setPrinterIp("USB");
        expectedResult.setPrinterName("printerName");

        // Configure PrintRecordMapstruct.mapRecordDTO(...).
        final PrintRecordDTO printRecordDTO = new PrintRecordDTO();
        printRecordDTO.setRecordUid("markName");
        final PrintDTO printContent1 = new PrintDTO();
        printContent1.setInvoiceType(0);
        printContent1.setEnterpriseGuid("enterpriseGuid");
        printContent1.setStoreGuid("storeGuid");
        printRecordDTO.setPrintContent(printContent1);
        printRecordDTO.setMarkName("markName");
        printRecordDTO.setMarkNo("markName");
        printRecordDTO.setPrinterType(0);
        printRecordDTO.setPrinterIp("USB");
        printRecordDTO.setPrinterName("printerName");
        final PrintRecordReadDO printRecordDo = new PrintRecordReadDO();
        printRecordDo.setRecordGuid("mock_key");
        printRecordDo.setStoreGuid("storeGuid");
        printRecordDo.setInvoiceType(0);
        printRecordDo.setPrintContent("printContent");
        final PrinterDO printerDO1 = new PrinterDO();
        printerDO1.setPrinterName("printerName");
        printerDO1.setBusinessType(0);
        printerDO1.setPrinterType(0);
        printerDO1.setPrinterIp("USB");
        printerDO1.setPrinterPort(0);
        printerDO1.setPrintCount(0);
        printerDO1.setPrintPage("58");
        printRecordDo.setPrinterDO(printerDO1);
        when(mockPrintRecordMapstruct.mapRecordDTO(printRecordDo)).thenReturn(printRecordDTO);

        // Run the test
        final PrintRecordDTO result = printRecordRespServiceImplUnderTest.do2Dto(printRecordReadDO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetOrderByPerDO() {
        // Setup
        final PrintRecordReadDO printRecordReadDO = new PrintRecordReadDO();
        printRecordReadDO.setRecordGuid("mock_key");
        printRecordReadDO.setStoreGuid("storeGuid");
        printRecordReadDO.setInvoiceType(0);
        printRecordReadDO.setPrintContent("printContent");
        final PrinterDO printerDO = new PrinterDO();
        printerDO.setPrinterName("printerName");
        printerDO.setBusinessType(0);
        printerDO.setPrinterType(0);
        printerDO.setPrinterIp("USB");
        printerDO.setPrinterPort(0);
        printerDO.setPrintCount(0);
        printerDO.setPrintPage("58");
        printRecordReadDO.setPrinterDO(printerDO);

        final PrintOrderDTO expectedResult = new PrintOrderDTO();
        expectedResult.setPrintKey("mock_key");
        expectedResult.setBusinessType(0);
        expectedResult.setPrinterType(0);
        expectedResult.setPrinterIp("printerIp");
        expectedResult.setPrinterPort(0);
        expectedResult.setPageSize(0);
        expectedResult.setPrintTimes(0);
        final PrintRow printRow = new PrintRow();
        expectedResult.setPrintRows(Arrays.asList(printRow));

        doReturn(null).when(mockPrintTemplateFactory).create(0, 0, "printContent", "storeGuid");

        // Run the test
        final PrintOrderDTO result = printRecordRespServiceImplUnderTest.getOrderByPerDO(printRecordReadDO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetOrderByPerMock() {
        // Setup
        final FormatDTO formatDTO = new FormatDTO();
        formatDTO.setGuid("66458cb0-ccfe-4a7e-b8f5-72693b6de38f");
        formatDTO.setName("name");
        formatDTO.setStoreGuid("storeGuid");
        formatDTO.setInvoiceType(0);
        formatDTO.setPageSize(0);

        final PrinterDTO printerDTO = new PrinterDTO();
        printerDTO.setStoreGuid("storeGuid");
        printerDTO.setPrinterType(0);
        printerDTO.setPrinterIp("printerIp");
        printerDTO.setPrinterPort(0);
        printerDTO.setPrintCount(0);

        final PrintOrderDTO expectedResult = new PrintOrderDTO();
        expectedResult.setPrintKey("mock_key");
        expectedResult.setBusinessType(0);
        expectedResult.setPrinterType(0);
        expectedResult.setPrinterIp("printerIp");
        expectedResult.setPrinterPort(0);
        expectedResult.setPageSize(0);
        expectedResult.setPrintTimes(0);
        final PrintRow printRow = new PrintRow();
        expectedResult.setPrintRows(Arrays.asList(printRow));

        // Configure PrintTemplateFactory.create(...).
        final FormatDTO formatDTO1 = new FormatDTO();
        formatDTO1.setGuid("66458cb0-ccfe-4a7e-b8f5-72693b6de38f");
        formatDTO1.setName("name");
        formatDTO1.setStoreGuid("storeGuid");
        formatDTO1.setInvoiceType(0);
        formatDTO1.setPageSize(0);
        doReturn(null).when(mockPrintTemplateFactory).create(0, 0, "content", formatDTO1);

        // Run the test
        final PrintOrderDTO result = printRecordRespServiceImplUnderTest.getOrderByPerMock(0, 0, formatDTO, printerDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
