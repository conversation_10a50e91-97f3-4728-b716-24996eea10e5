package com.holder.saas.print.service.impl;

import com.holder.saas.print.service.feign.StoreDeviceFeignService;
import com.holderzone.saas.store.dto.terminal.StoreDeviceDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class StoreDeviceServiceImplTest {

    @Mock
    private StoreDeviceFeignService mockStoreDeviceFeignService;

    private StoreDeviceServiceImpl storeDeviceServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        storeDeviceServiceImplUnderTest = new StoreDeviceServiceImpl(mockStoreDeviceFeignService);
    }

    @Test
    public void testFindMasterDevice() {
        // Setup
        // Configure StoreDeviceFeignService.getMasterDeviceByStoreGuid(...).
        final StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO();
        storeDeviceDTO.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO.setStoreNo("storeNo");
        storeDeviceDTO.setStoreGuid("storeGuid");
        storeDeviceDTO.setStoreName("storeName");
        storeDeviceDTO.setDeviceGuid("deviceGuid");
        when(mockStoreDeviceFeignService.getMasterDeviceByStoreGuid("storeGuid")).thenReturn(storeDeviceDTO);

        // Run the test
        final String result = storeDeviceServiceImplUnderTest.findMasterDevice("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo("deviceGuid");
    }

    @Test
    public void testFindMasterDevice_StoreDeviceFeignServiceReturnsNull() {
        // Setup
        when(mockStoreDeviceFeignService.getMasterDeviceByStoreGuid("storeGuid")).thenReturn(null);

        // Run the test
        final String result = storeDeviceServiceImplUnderTest.findMasterDevice("storeGuid");

        // Verify the results
        assertThat(result).isNull();
    }

    @Test
    public void testIsMasterDevice() {
        // Setup
        // Configure StoreDeviceFeignService.getMasterDeviceByStoreGuid(...).
        final StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO();
        storeDeviceDTO.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO.setStoreNo("storeNo");
        storeDeviceDTO.setStoreGuid("storeGuid");
        storeDeviceDTO.setStoreName("storeName");
        storeDeviceDTO.setDeviceGuid("deviceGuid");
        when(mockStoreDeviceFeignService.getMasterDeviceByStoreGuid("storeGuid")).thenReturn(storeDeviceDTO);

        // Run the test
        final boolean result = storeDeviceServiceImplUnderTest.isMasterDevice("storeGuid", "deviceId");

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testIsMasterDevice_StoreDeviceFeignServiceReturnsNull() {
        // Setup
        when(mockStoreDeviceFeignService.getMasterDeviceByStoreGuid("storeGuid")).thenReturn(null);

        // Run the test
        final boolean result = storeDeviceServiceImplUnderTest.isMasterDevice("storeGuid", "deviceId");

        // Verify the results
        assertThat(result).isFalse();
    }
}
