package com.holder.saas.print.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holder.saas.print.entity.domain.PrinterBackupsDO;
import com.holder.saas.print.entity.domain.PrinterDO;
import com.holder.saas.print.entity.query.PrinterQuery;
import com.holder.saas.print.entity.read.PrinterReadDO;
import com.holder.saas.print.mapstruct.PrinterMapstruct;
import com.holder.saas.print.mapstruct.PrinterRawMaptstruct;
import com.holder.saas.print.service.*;
import com.holder.saas.print.service.feign.ItemClientService;
import com.holderzone.saas.store.dto.print.*;
import com.holderzone.saas.store.dto.print.cloud.CloudPrinterDTO;
import com.holderzone.saas.store.dto.print.cloud.DeviceUseDTO;
import com.holderzone.saas.store.dto.print.cloud.FeieRespDTO;
import com.holderzone.saas.store.dto.print.raw.PrinterRawDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PrinterServiceImplTest {

    @Mock
    private PrinterMapstruct mockPrinterMapstruct;
    @Mock
    private PrinterRawMaptstruct mockPrinterRawMaptstruct;
    @Mock
    private StoreDeviceService mockStoreDeviceService;
    @Mock
    private PrinterInvoiceService mockPrinterInvoiceService;
    @Mock
    private PrinterAreaService mockPrinterAreaService;
    @Mock
    private PrinterTableService mockPrinterTableService;
    @Mock
    private PrinterItemService mockPrinterItemService;
    @Mock
    private PrinterLogService mockPrinterLogService;
    @Mock
    private PrinterBackupsService mockPrinterBackupsService;
    @Mock
    private DistributedService mockDistributedService;
    @Mock
    private PrintRecordService mockPrintRecordService;
    @Mock
    private CloudPrinterService mockCloudPrinterService;

    @Mock
    private ItemClientService mockItemClientService;

    private PrinterServiceImpl printerServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        printerServiceImplUnderTest = new PrinterServiceImpl(mockPrinterMapstruct, mockPrinterRawMaptstruct,
                mockStoreDeviceService, mockPrinterInvoiceService, mockPrinterAreaService, mockPrinterTableService, mockPrinterItemService,
                mockPrinterLogService, mockPrinterBackupsService, mockDistributedService, mockPrintRecordService,
                mockCloudPrinterService, mockItemClientService);
    }

    @Test
    public void testAddPrinter() {
        // Setup
        final PrinterDTO printerDTO = new PrinterDTO();
        printerDTO.setStoreGuid("storeGuid");
        printerDTO.setStoreName("storeName");
        printerDTO.setDeviceId("deviceId");
        printerDTO.setPrinterGuid("printerGuid");
        printerDTO.setPrinterName("printerName");
        printerDTO.setBusinessType(0);
        printerDTO.setPrinterType(0);
        printerDTO.setPrinterIp("printerIp");
        printerDTO.setPrinterPort(0);
        printerDTO.setPrintPage("printPage");
        printerDTO.setPrintCut(0);
        printerDTO.setArrayOfInvoiceType(Arrays.asList(0));
        printerDTO.setArrayOfItemGuid(Arrays.asList("value"));
        printerDTO.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterInvoiceDTO printerInvoiceDTO = new PrinterInvoiceDTO();
        printerInvoiceDTO.setInvoiceType(0);
        printerInvoiceDTO.setInvoiceName("invoiceName");
        printerInvoiceDTO.setPrintCount(0);
        printerDTO.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO));
        final PrinterItemDTO printerItemDTO = new PrinterItemDTO();
        printerDTO.setArrayOfItemDTO(Arrays.asList(printerItemDTO));
        final PrinterAreaDTO printerAreaDTO = new PrinterAreaDTO();
        printerDTO.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO));
        printerDTO.setDeviceNo("deviceNo");
        printerDTO.setDeviceKey("deviceKey");
        printerDTO.setManufacturersType(0);
        printerDTO.setDeviceState(0);

        when(mockDistributedService.nextPrinterGuid()).thenReturn("printerGuid");

        // Configure PrinterMapstruct.fromPrinterDTO(...).
        final PrinterDO printerDO = new PrinterDO();
        printerDO.setId(0L);
        printerDO.setStoreGuid("storeGuid");
        printerDO.setDeviceId("0");
        printerDO.setPrinterGuid("printerGuid");
        printerDO.setPrinterName("printerName");
        printerDO.setBusinessType(0);
        printerDO.setPrinterType(0);
        printerDO.setPrinterIp("printerIp");
        printerDO.setPrintCount(0);
        printerDO.setPrintCut(0);
        printerDO.setIsMaster(false);
        final PrinterDTO printerDTO1 = new PrinterDTO();
        printerDTO1.setStoreGuid("storeGuid");
        printerDTO1.setStoreName("storeName");
        printerDTO1.setDeviceId("deviceId");
        printerDTO1.setPrinterGuid("printerGuid");
        printerDTO1.setPrinterName("printerName");
        printerDTO1.setBusinessType(0);
        printerDTO1.setPrinterType(0);
        printerDTO1.setPrinterIp("printerIp");
        printerDTO1.setPrinterPort(0);
        printerDTO1.setPrintPage("printPage");
        printerDTO1.setPrintCut(0);
        printerDTO1.setArrayOfInvoiceType(Arrays.asList(0));
        printerDTO1.setArrayOfItemGuid(Arrays.asList("value"));
        printerDTO1.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterInvoiceDTO printerInvoiceDTO1 = new PrinterInvoiceDTO();
        printerInvoiceDTO1.setInvoiceType(0);
        printerInvoiceDTO1.setInvoiceName("invoiceName");
        printerInvoiceDTO1.setPrintCount(0);
        printerDTO1.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO1));
        final PrinterItemDTO printerItemDTO1 = new PrinterItemDTO();
        printerDTO1.setArrayOfItemDTO(Arrays.asList(printerItemDTO1));
        final PrinterAreaDTO printerAreaDTO1 = new PrinterAreaDTO();
        printerDTO1.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO1));
        printerDTO1.setDeviceNo("deviceNo");
        printerDTO1.setDeviceKey("deviceKey");
        printerDTO1.setManufacturersType(0);
        printerDTO1.setDeviceState(0);
        when(mockPrinterMapstruct.fromPrinterDTO(printerDTO1)).thenReturn(printerDO);

        when(mockStoreDeviceService.isMasterDevice("storeGuid", "deviceId")).thenReturn(false);

        // Run the test
        final String result = printerServiceImplUnderTest.addPrinter(printerDTO);

        // Verify the results
        assertThat(result).isEqualTo("printerGuid");

        // Confirm PrinterInvoiceService.bindPrinterInvoice(...).
        final PrinterDTO printerDTO2 = new PrinterDTO();
        printerDTO2.setStoreGuid("storeGuid");
        printerDTO2.setStoreName("storeName");
        printerDTO2.setDeviceId("deviceId");
        printerDTO2.setPrinterGuid("printerGuid");
        printerDTO2.setPrinterName("printerName");
        printerDTO2.setBusinessType(0);
        printerDTO2.setPrinterType(0);
        printerDTO2.setPrinterIp("printerIp");
        printerDTO2.setPrinterPort(0);
        printerDTO2.setPrintPage("printPage");
        printerDTO2.setPrintCut(0);
        printerDTO2.setArrayOfInvoiceType(Arrays.asList(0));
        printerDTO2.setArrayOfItemGuid(Arrays.asList("value"));
        printerDTO2.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterInvoiceDTO printerInvoiceDTO2 = new PrinterInvoiceDTO();
        printerInvoiceDTO2.setInvoiceType(0);
        printerInvoiceDTO2.setInvoiceName("invoiceName");
        printerInvoiceDTO2.setPrintCount(0);
        printerDTO2.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO2));
        final PrinterItemDTO printerItemDTO2 = new PrinterItemDTO();
        printerDTO2.setArrayOfItemDTO(Arrays.asList(printerItemDTO2));
        final PrinterAreaDTO printerAreaDTO2 = new PrinterAreaDTO();
        printerDTO2.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO2));
        printerDTO2.setDeviceNo("deviceNo");
        printerDTO2.setDeviceKey("deviceKey");
        printerDTO2.setManufacturersType(0);
        printerDTO2.setDeviceState(0);
        verify(mockPrinterInvoiceService).bindPrinterInvoice(printerDTO2);

        // Confirm PrinterItemService.bindPrinterItem(...).
        final PrinterDTO printerDTO3 = new PrinterDTO();
        printerDTO3.setStoreGuid("storeGuid");
        printerDTO3.setStoreName("storeName");
        printerDTO3.setDeviceId("deviceId");
        printerDTO3.setPrinterGuid("printerGuid");
        printerDTO3.setPrinterName("printerName");
        printerDTO3.setBusinessType(0);
        printerDTO3.setPrinterType(0);
        printerDTO3.setPrinterIp("printerIp");
        printerDTO3.setPrinterPort(0);
        printerDTO3.setPrintPage("printPage");
        printerDTO3.setPrintCut(0);
        printerDTO3.setArrayOfInvoiceType(Arrays.asList(0));
        printerDTO3.setArrayOfItemGuid(Arrays.asList("value"));
        printerDTO3.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterInvoiceDTO printerInvoiceDTO3 = new PrinterInvoiceDTO();
        printerInvoiceDTO3.setInvoiceType(0);
        printerInvoiceDTO3.setInvoiceName("invoiceName");
        printerInvoiceDTO3.setPrintCount(0);
        printerDTO3.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO3));
        final PrinterItemDTO printerItemDTO3 = new PrinterItemDTO();
        printerDTO3.setArrayOfItemDTO(Arrays.asList(printerItemDTO3));
        final PrinterAreaDTO printerAreaDTO3 = new PrinterAreaDTO();
        printerDTO3.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO3));
        printerDTO3.setDeviceNo("deviceNo");
        printerDTO3.setDeviceKey("deviceKey");
        printerDTO3.setManufacturersType(0);
        printerDTO3.setDeviceState(0);
        verify(mockPrinterItemService).bindPrinterItem(printerDTO3);

        // Confirm PrinterAreaService.bindPrinterArea(...).
        final PrinterDTO printerDTO4 = new PrinterDTO();
        printerDTO4.setStoreGuid("storeGuid");
        printerDTO4.setStoreName("storeName");
        printerDTO4.setDeviceId("deviceId");
        printerDTO4.setPrinterGuid("printerGuid");
        printerDTO4.setPrinterName("printerName");
        printerDTO4.setBusinessType(0);
        printerDTO4.setPrinterType(0);
        printerDTO4.setPrinterIp("printerIp");
        printerDTO4.setPrinterPort(0);
        printerDTO4.setPrintPage("printPage");
        printerDTO4.setPrintCut(0);
        printerDTO4.setArrayOfInvoiceType(Arrays.asList(0));
        printerDTO4.setArrayOfItemGuid(Arrays.asList("value"));
        printerDTO4.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterInvoiceDTO printerInvoiceDTO4 = new PrinterInvoiceDTO();
        printerInvoiceDTO4.setInvoiceType(0);
        printerInvoiceDTO4.setInvoiceName("invoiceName");
        printerInvoiceDTO4.setPrintCount(0);
        printerDTO4.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO4));
        final PrinterItemDTO printerItemDTO4 = new PrinterItemDTO();
        printerDTO4.setArrayOfItemDTO(Arrays.asList(printerItemDTO4));
        final PrinterAreaDTO printerAreaDTO4 = new PrinterAreaDTO();
        printerDTO4.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO4));
        printerDTO4.setDeviceNo("deviceNo");
        printerDTO4.setDeviceKey("deviceKey");
        printerDTO4.setManufacturersType(0);
        printerDTO4.setDeviceState(0);
        verify(mockPrinterAreaService).bindPrinterArea(printerDTO4);

        // Confirm PrinterLogService.publishAddLog(...).
        final PrinterDTO printerDTO5 = new PrinterDTO();
        printerDTO5.setStoreGuid("storeGuid");
        printerDTO5.setStoreName("storeName");
        printerDTO5.setDeviceId("deviceId");
        printerDTO5.setPrinterGuid("printerGuid");
        printerDTO5.setPrinterName("printerName");
        printerDTO5.setBusinessType(0);
        printerDTO5.setPrinterType(0);
        printerDTO5.setPrinterIp("printerIp");
        printerDTO5.setPrinterPort(0);
        printerDTO5.setPrintPage("printPage");
        printerDTO5.setPrintCut(0);
        printerDTO5.setArrayOfInvoiceType(Arrays.asList(0));
        printerDTO5.setArrayOfItemGuid(Arrays.asList("value"));
        printerDTO5.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterInvoiceDTO printerInvoiceDTO5 = new PrinterInvoiceDTO();
        printerInvoiceDTO5.setInvoiceType(0);
        printerInvoiceDTO5.setInvoiceName("invoiceName");
        printerInvoiceDTO5.setPrintCount(0);
        printerDTO5.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO5));
        final PrinterItemDTO printerItemDTO5 = new PrinterItemDTO();
        printerDTO5.setArrayOfItemDTO(Arrays.asList(printerItemDTO5));
        final PrinterAreaDTO printerAreaDTO5 = new PrinterAreaDTO();
        printerDTO5.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO5));
        printerDTO5.setDeviceNo("deviceNo");
        printerDTO5.setDeviceKey("deviceKey");
        printerDTO5.setManufacturersType(0);
        printerDTO5.setDeviceState(0);
        verify(mockPrinterLogService).publishAddLog(any(Object.class), eq(printerDTO5));
    }

    @Test
    public void testQueryPrinter() {
        // Setup
        final PrinterDTO printerDTO = new PrinterDTO();
        printerDTO.setStoreGuid("storeGuid");
        printerDTO.setStoreName("storeName");
        printerDTO.setDeviceId("deviceId");
        printerDTO.setPrinterGuid("printerGuid");
        printerDTO.setPrinterName("printerName");
        printerDTO.setBusinessType(0);
        printerDTO.setPrinterType(0);
        printerDTO.setPrinterIp("printerIp");
        printerDTO.setPrinterPort(0);
        printerDTO.setPrintPage("printPage");
        printerDTO.setPrintCut(0);
        printerDTO.setArrayOfInvoiceType(Arrays.asList(0));
        printerDTO.setArrayOfItemGuid(Arrays.asList("value"));
        printerDTO.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterInvoiceDTO printerInvoiceDTO = new PrinterInvoiceDTO();
        printerInvoiceDTO.setInvoiceType(0);
        printerInvoiceDTO.setInvoiceName("invoiceName");
        printerInvoiceDTO.setPrintCount(0);
        printerDTO.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO));
        final PrinterItemDTO printerItemDTO = new PrinterItemDTO();
        printerDTO.setArrayOfItemDTO(Arrays.asList(printerItemDTO));
        final PrinterAreaDTO printerAreaDTO = new PrinterAreaDTO();
        printerDTO.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO));
        printerDTO.setDeviceNo("deviceNo");
        printerDTO.setDeviceKey("deviceKey");
        printerDTO.setManufacturersType(0);
        printerDTO.setDeviceState(0);

        final PrinterDTO expectedResult = new PrinterDTO();
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setStoreName("storeName");
        expectedResult.setDeviceId("deviceId");
        expectedResult.setPrinterGuid("printerGuid");
        expectedResult.setPrinterName("printerName");
        expectedResult.setBusinessType(0);
        expectedResult.setPrinterType(0);
        expectedResult.setPrinterIp("printerIp");
        expectedResult.setPrinterPort(0);
        expectedResult.setPrintPage("printPage");
        expectedResult.setPrintCut(0);
        expectedResult.setArrayOfInvoiceType(Arrays.asList(0));
        expectedResult.setArrayOfItemGuid(Arrays.asList("value"));
        expectedResult.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterInvoiceDTO printerInvoiceDTO1 = new PrinterInvoiceDTO();
        printerInvoiceDTO1.setInvoiceType(0);
        printerInvoiceDTO1.setInvoiceName("invoiceName");
        printerInvoiceDTO1.setPrintCount(0);
        expectedResult.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO1));
        final PrinterItemDTO printerItemDTO1 = new PrinterItemDTO();
        expectedResult.setArrayOfItemDTO(Arrays.asList(printerItemDTO1));
        final PrinterAreaDTO printerAreaDTO1 = new PrinterAreaDTO();
        expectedResult.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO1));
        expectedResult.setDeviceNo("deviceNo");
        expectedResult.setDeviceKey("deviceKey");
        expectedResult.setManufacturersType(0);
        expectedResult.setDeviceState(0);

        // Configure PrinterMapstruct.fromPrinterDTO(...).
        final PrinterDO printerDO = new PrinterDO();
        printerDO.setId(0L);
        printerDO.setStoreGuid("storeGuid");
        printerDO.setDeviceId("0");
        printerDO.setPrinterGuid("printerGuid");
        printerDO.setPrinterName("printerName");
        printerDO.setBusinessType(0);
        printerDO.setPrinterType(0);
        printerDO.setPrinterIp("printerIp");
        printerDO.setPrintCount(0);
        printerDO.setPrintCut(0);
        printerDO.setIsMaster(false);
        final PrinterDTO printerDTO1 = new PrinterDTO();
        printerDTO1.setStoreGuid("storeGuid");
        printerDTO1.setStoreName("storeName");
        printerDTO1.setDeviceId("deviceId");
        printerDTO1.setPrinterGuid("printerGuid");
        printerDTO1.setPrinterName("printerName");
        printerDTO1.setBusinessType(0);
        printerDTO1.setPrinterType(0);
        printerDTO1.setPrinterIp("printerIp");
        printerDTO1.setPrinterPort(0);
        printerDTO1.setPrintPage("printPage");
        printerDTO1.setPrintCut(0);
        printerDTO1.setArrayOfInvoiceType(Arrays.asList(0));
        printerDTO1.setArrayOfItemGuid(Arrays.asList("value"));
        printerDTO1.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterInvoiceDTO printerInvoiceDTO2 = new PrinterInvoiceDTO();
        printerInvoiceDTO2.setInvoiceType(0);
        printerInvoiceDTO2.setInvoiceName("invoiceName");
        printerInvoiceDTO2.setPrintCount(0);
        printerDTO1.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO2));
        final PrinterItemDTO printerItemDTO2 = new PrinterItemDTO();
        printerDTO1.setArrayOfItemDTO(Arrays.asList(printerItemDTO2));
        final PrinterAreaDTO printerAreaDTO2 = new PrinterAreaDTO();
        printerDTO1.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO2));
        printerDTO1.setDeviceNo("deviceNo");
        printerDTO1.setDeviceKey("deviceKey");
        printerDTO1.setManufacturersType(0);
        printerDTO1.setDeviceState(0);
        when(mockPrinterMapstruct.fromPrinterDTO(printerDTO1)).thenReturn(printerDO);

        // Configure PrinterMapstruct.toPrinterDTO(...).
        final PrinterDTO printerDTO2 = new PrinterDTO();
        printerDTO2.setStoreGuid("storeGuid");
        printerDTO2.setStoreName("storeName");
        printerDTO2.setDeviceId("deviceId");
        printerDTO2.setPrinterGuid("printerGuid");
        printerDTO2.setPrinterName("printerName");
        printerDTO2.setBusinessType(0);
        printerDTO2.setPrinterType(0);
        printerDTO2.setPrinterIp("printerIp");
        printerDTO2.setPrinterPort(0);
        printerDTO2.setPrintPage("printPage");
        printerDTO2.setPrintCut(0);
        printerDTO2.setArrayOfInvoiceType(Arrays.asList(0));
        printerDTO2.setArrayOfItemGuid(Arrays.asList("value"));
        printerDTO2.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterInvoiceDTO printerInvoiceDTO3 = new PrinterInvoiceDTO();
        printerInvoiceDTO3.setInvoiceType(0);
        printerInvoiceDTO3.setInvoiceName("invoiceName");
        printerInvoiceDTO3.setPrintCount(0);
        printerDTO2.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO3));
        final PrinterItemDTO printerItemDTO3 = new PrinterItemDTO();
        printerDTO2.setArrayOfItemDTO(Arrays.asList(printerItemDTO3));
        final PrinterAreaDTO printerAreaDTO3 = new PrinterAreaDTO();
        printerDTO2.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO3));
        printerDTO2.setDeviceNo("deviceNo");
        printerDTO2.setDeviceKey("deviceKey");
        printerDTO2.setManufacturersType(0);
        printerDTO2.setDeviceState(0);
        final PrinterReadDO printerReadDO = new PrinterReadDO();
        printerReadDO.setId(0L);
        printerReadDO.setPrinterGuid("printerGuid");
        printerReadDO.setPrinterName("printerName");
        printerReadDO.setBusinessType(0);
        printerReadDO.setPrinterType(0);
        printerReadDO.setPrinterPort(0);
        printerReadDO.setDeviceNo("deviceNo");
        when(mockPrinterMapstruct.toPrinterDTO(printerReadDO)).thenReturn(printerDTO2);

        // Run the test
        final PrinterDTO result = printerServiceImplUnderTest.queryPrinter(printerDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testListPrinterOfTheDevice() {
        // Setup
        final PrinterDTO printerDTO = new PrinterDTO();
        printerDTO.setStoreGuid("storeGuid");
        printerDTO.setStoreName("storeName");
        printerDTO.setDeviceId("deviceId");
        printerDTO.setPrinterGuid("printerGuid");
        printerDTO.setPrinterName("printerName");
        printerDTO.setBusinessType(0);
        printerDTO.setPrinterType(0);
        printerDTO.setPrinterIp("printerIp");
        printerDTO.setPrinterPort(0);
        printerDTO.setPrintPage("printPage");
        printerDTO.setPrintCut(0);
        printerDTO.setArrayOfInvoiceType(Arrays.asList(0));
        printerDTO.setArrayOfItemGuid(Arrays.asList("value"));
        printerDTO.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterInvoiceDTO printerInvoiceDTO = new PrinterInvoiceDTO();
        printerInvoiceDTO.setInvoiceType(0);
        printerInvoiceDTO.setInvoiceName("invoiceName");
        printerInvoiceDTO.setPrintCount(0);
        printerDTO.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO));
        final PrinterItemDTO printerItemDTO = new PrinterItemDTO();
        printerDTO.setArrayOfItemDTO(Arrays.asList(printerItemDTO));
        final PrinterAreaDTO printerAreaDTO = new PrinterAreaDTO();
        printerDTO.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO));
        printerDTO.setDeviceNo("deviceNo");
        printerDTO.setDeviceKey("deviceKey");
        printerDTO.setManufacturersType(0);
        printerDTO.setDeviceState(0);

        final PrinterDTO printerDTO1 = new PrinterDTO();
        printerDTO1.setStoreGuid("storeGuid");
        printerDTO1.setStoreName("storeName");
        printerDTO1.setDeviceId("deviceId");
        printerDTO1.setPrinterGuid("printerGuid");
        printerDTO1.setPrinterName("printerName");
        printerDTO1.setBusinessType(0);
        printerDTO1.setPrinterType(0);
        printerDTO1.setPrinterIp("printerIp");
        printerDTO1.setPrinterPort(0);
        printerDTO1.setPrintPage("printPage");
        printerDTO1.setPrintCut(0);
        printerDTO1.setArrayOfInvoiceType(Arrays.asList(0));
        printerDTO1.setArrayOfItemGuid(Arrays.asList("value"));
        printerDTO1.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterInvoiceDTO printerInvoiceDTO1 = new PrinterInvoiceDTO();
        printerInvoiceDTO1.setInvoiceType(0);
        printerInvoiceDTO1.setInvoiceName("invoiceName");
        printerInvoiceDTO1.setPrintCount(0);
        printerDTO1.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO1));
        final PrinterItemDTO printerItemDTO1 = new PrinterItemDTO();
        printerDTO1.setArrayOfItemDTO(Arrays.asList(printerItemDTO1));
        final PrinterAreaDTO printerAreaDTO1 = new PrinterAreaDTO();
        printerDTO1.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO1));
        printerDTO1.setDeviceNo("deviceNo");
        printerDTO1.setDeviceKey("deviceKey");
        printerDTO1.setManufacturersType(0);
        printerDTO1.setDeviceState(0);
        final List<PrinterDTO> expectedResult = Arrays.asList(printerDTO1);

        // Configure PrinterMapstruct.fromPrinterDTO(...).
        final PrinterDO printerDO = new PrinterDO();
        printerDO.setId(0L);
        printerDO.setStoreGuid("storeGuid");
        printerDO.setDeviceId("0");
        printerDO.setPrinterGuid("printerGuid");
        printerDO.setPrinterName("printerName");
        printerDO.setBusinessType(0);
        printerDO.setPrinterType(0);
        printerDO.setPrinterIp("printerIp");
        printerDO.setPrintCount(0);
        printerDO.setPrintCut(0);
        printerDO.setIsMaster(false);
        final PrinterDTO printerDTO2 = new PrinterDTO();
        printerDTO2.setStoreGuid("storeGuid");
        printerDTO2.setStoreName("storeName");
        printerDTO2.setDeviceId("deviceId");
        printerDTO2.setPrinterGuid("printerGuid");
        printerDTO2.setPrinterName("printerName");
        printerDTO2.setBusinessType(0);
        printerDTO2.setPrinterType(0);
        printerDTO2.setPrinterIp("printerIp");
        printerDTO2.setPrinterPort(0);
        printerDTO2.setPrintPage("printPage");
        printerDTO2.setPrintCut(0);
        printerDTO2.setArrayOfInvoiceType(Arrays.asList(0));
        printerDTO2.setArrayOfItemGuid(Arrays.asList("value"));
        printerDTO2.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterInvoiceDTO printerInvoiceDTO2 = new PrinterInvoiceDTO();
        printerInvoiceDTO2.setInvoiceType(0);
        printerInvoiceDTO2.setInvoiceName("invoiceName");
        printerInvoiceDTO2.setPrintCount(0);
        printerDTO2.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO2));
        final PrinterItemDTO printerItemDTO2 = new PrinterItemDTO();
        printerDTO2.setArrayOfItemDTO(Arrays.asList(printerItemDTO2));
        final PrinterAreaDTO printerAreaDTO2 = new PrinterAreaDTO();
        printerDTO2.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO2));
        printerDTO2.setDeviceNo("deviceNo");
        printerDTO2.setDeviceKey("deviceKey");
        printerDTO2.setManufacturersType(0);
        printerDTO2.setDeviceState(0);
        when(mockPrinterMapstruct.fromPrinterDTO(printerDTO2)).thenReturn(printerDO);

        // Configure PrinterMapstruct.toPrinterDTO(...).
        final PrinterDTO printerDTO3 = new PrinterDTO();
        printerDTO3.setStoreGuid("storeGuid");
        printerDTO3.setStoreName("storeName");
        printerDTO3.setDeviceId("deviceId");
        printerDTO3.setPrinterGuid("printerGuid");
        printerDTO3.setPrinterName("printerName");
        printerDTO3.setBusinessType(0);
        printerDTO3.setPrinterType(0);
        printerDTO3.setPrinterIp("printerIp");
        printerDTO3.setPrinterPort(0);
        printerDTO3.setPrintPage("printPage");
        printerDTO3.setPrintCut(0);
        printerDTO3.setArrayOfInvoiceType(Arrays.asList(0));
        printerDTO3.setArrayOfItemGuid(Arrays.asList("value"));
        printerDTO3.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterInvoiceDTO printerInvoiceDTO3 = new PrinterInvoiceDTO();
        printerInvoiceDTO3.setInvoiceType(0);
        printerInvoiceDTO3.setInvoiceName("invoiceName");
        printerInvoiceDTO3.setPrintCount(0);
        printerDTO3.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO3));
        final PrinterItemDTO printerItemDTO3 = new PrinterItemDTO();
        printerDTO3.setArrayOfItemDTO(Arrays.asList(printerItemDTO3));
        final PrinterAreaDTO printerAreaDTO3 = new PrinterAreaDTO();
        printerDTO3.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO3));
        printerDTO3.setDeviceNo("deviceNo");
        printerDTO3.setDeviceKey("deviceKey");
        printerDTO3.setManufacturersType(0);
        printerDTO3.setDeviceState(0);
        final PrinterReadDO printerReadDO = new PrinterReadDO();
        printerReadDO.setId(0L);
        printerReadDO.setPrinterGuid("printerGuid");
        printerReadDO.setPrinterName("printerName");
        printerReadDO.setBusinessType(0);
        printerReadDO.setPrinterType(0);
        printerReadDO.setPrinterPort(0);
        printerReadDO.setDeviceNo("deviceNo");
        when(mockPrinterMapstruct.toPrinterDTO(printerReadDO)).thenReturn(printerDTO3);

        // Run the test
        final List<PrinterDTO> result = printerServiceImplUnderTest.listPrinterOfTheDevice(printerDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testListPrinterOfBizType() {
        // Setup
        final PrinterDTO printerDTO = new PrinterDTO();
        printerDTO.setStoreGuid("storeGuid");
        printerDTO.setStoreName("storeName");
        printerDTO.setDeviceId("deviceId");
        printerDTO.setPrinterGuid("printerGuid");
        printerDTO.setPrinterName("printerName");
        printerDTO.setBusinessType(0);
        printerDTO.setPrinterType(0);
        printerDTO.setPrinterIp("printerIp");
        printerDTO.setPrinterPort(0);
        printerDTO.setPrintPage("printPage");
        printerDTO.setPrintCut(0);
        printerDTO.setArrayOfInvoiceType(Arrays.asList(0));
        printerDTO.setArrayOfItemGuid(Arrays.asList("value"));
        printerDTO.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterInvoiceDTO printerInvoiceDTO = new PrinterInvoiceDTO();
        printerInvoiceDTO.setInvoiceType(0);
        printerInvoiceDTO.setInvoiceName("invoiceName");
        printerInvoiceDTO.setPrintCount(0);
        printerDTO.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO));
        final PrinterItemDTO printerItemDTO = new PrinterItemDTO();
        printerDTO.setArrayOfItemDTO(Arrays.asList(printerItemDTO));
        final PrinterAreaDTO printerAreaDTO = new PrinterAreaDTO();
        printerDTO.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO));
        printerDTO.setDeviceNo("deviceNo");
        printerDTO.setDeviceKey("deviceKey");
        printerDTO.setManufacturersType(0);
        printerDTO.setDeviceState(0);

        final PrinterDTO printerDTO1 = new PrinterDTO();
        printerDTO1.setStoreGuid("storeGuid");
        printerDTO1.setStoreName("storeName");
        printerDTO1.setDeviceId("deviceId");
        printerDTO1.setPrinterGuid("printerGuid");
        printerDTO1.setPrinterName("printerName");
        printerDTO1.setBusinessType(0);
        printerDTO1.setPrinterType(0);
        printerDTO1.setPrinterIp("printerIp");
        printerDTO1.setPrinterPort(0);
        printerDTO1.setPrintPage("printPage");
        printerDTO1.setPrintCut(0);
        printerDTO1.setArrayOfInvoiceType(Arrays.asList(0));
        printerDTO1.setArrayOfItemGuid(Arrays.asList("value"));
        printerDTO1.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterInvoiceDTO printerInvoiceDTO1 = new PrinterInvoiceDTO();
        printerInvoiceDTO1.setInvoiceType(0);
        printerInvoiceDTO1.setInvoiceName("invoiceName");
        printerInvoiceDTO1.setPrintCount(0);
        printerDTO1.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO1));
        final PrinterItemDTO printerItemDTO1 = new PrinterItemDTO();
        printerDTO1.setArrayOfItemDTO(Arrays.asList(printerItemDTO1));
        final PrinterAreaDTO printerAreaDTO1 = new PrinterAreaDTO();
        printerDTO1.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO1));
        printerDTO1.setDeviceNo("deviceNo");
        printerDTO1.setDeviceKey("deviceKey");
        printerDTO1.setManufacturersType(0);
        printerDTO1.setDeviceState(0);
        final List<PrinterDTO> expectedResult = Arrays.asList(printerDTO1);

        // Configure PrinterMapstruct.fromPrinterDTO(...).
        final PrinterDO printerDO = new PrinterDO();
        printerDO.setId(0L);
        printerDO.setStoreGuid("storeGuid");
        printerDO.setDeviceId("0");
        printerDO.setPrinterGuid("printerGuid");
        printerDO.setPrinterName("printerName");
        printerDO.setBusinessType(0);
        printerDO.setPrinterType(0);
        printerDO.setPrinterIp("printerIp");
        printerDO.setPrintCount(0);
        printerDO.setPrintCut(0);
        printerDO.setIsMaster(false);
        final PrinterDTO printerDTO2 = new PrinterDTO();
        printerDTO2.setStoreGuid("storeGuid");
        printerDTO2.setStoreName("storeName");
        printerDTO2.setDeviceId("deviceId");
        printerDTO2.setPrinterGuid("printerGuid");
        printerDTO2.setPrinterName("printerName");
        printerDTO2.setBusinessType(0);
        printerDTO2.setPrinterType(0);
        printerDTO2.setPrinterIp("printerIp");
        printerDTO2.setPrinterPort(0);
        printerDTO2.setPrintPage("printPage");
        printerDTO2.setPrintCut(0);
        printerDTO2.setArrayOfInvoiceType(Arrays.asList(0));
        printerDTO2.setArrayOfItemGuid(Arrays.asList("value"));
        printerDTO2.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterInvoiceDTO printerInvoiceDTO2 = new PrinterInvoiceDTO();
        printerInvoiceDTO2.setInvoiceType(0);
        printerInvoiceDTO2.setInvoiceName("invoiceName");
        printerInvoiceDTO2.setPrintCount(0);
        printerDTO2.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO2));
        final PrinterItemDTO printerItemDTO2 = new PrinterItemDTO();
        printerDTO2.setArrayOfItemDTO(Arrays.asList(printerItemDTO2));
        final PrinterAreaDTO printerAreaDTO2 = new PrinterAreaDTO();
        printerDTO2.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO2));
        printerDTO2.setDeviceNo("deviceNo");
        printerDTO2.setDeviceKey("deviceKey");
        printerDTO2.setManufacturersType(0);
        printerDTO2.setDeviceState(0);
        when(mockPrinterMapstruct.fromPrinterDTO(printerDTO2)).thenReturn(printerDO);

        // Configure PrinterMapstruct.toPrinterDTO(...).
        final PrinterDTO printerDTO3 = new PrinterDTO();
        printerDTO3.setStoreGuid("storeGuid");
        printerDTO3.setStoreName("storeName");
        printerDTO3.setDeviceId("deviceId");
        printerDTO3.setPrinterGuid("printerGuid");
        printerDTO3.setPrinterName("printerName");
        printerDTO3.setBusinessType(0);
        printerDTO3.setPrinterType(0);
        printerDTO3.setPrinterIp("printerIp");
        printerDTO3.setPrinterPort(0);
        printerDTO3.setPrintPage("printPage");
        printerDTO3.setPrintCut(0);
        printerDTO3.setArrayOfInvoiceType(Arrays.asList(0));
        printerDTO3.setArrayOfItemGuid(Arrays.asList("value"));
        printerDTO3.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterInvoiceDTO printerInvoiceDTO3 = new PrinterInvoiceDTO();
        printerInvoiceDTO3.setInvoiceType(0);
        printerInvoiceDTO3.setInvoiceName("invoiceName");
        printerInvoiceDTO3.setPrintCount(0);
        printerDTO3.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO3));
        final PrinterItemDTO printerItemDTO3 = new PrinterItemDTO();
        printerDTO3.setArrayOfItemDTO(Arrays.asList(printerItemDTO3));
        final PrinterAreaDTO printerAreaDTO3 = new PrinterAreaDTO();
        printerDTO3.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO3));
        printerDTO3.setDeviceNo("deviceNo");
        printerDTO3.setDeviceKey("deviceKey");
        printerDTO3.setManufacturersType(0);
        printerDTO3.setDeviceState(0);
        final PrinterReadDO printerReadDO = new PrinterReadDO();
        printerReadDO.setId(0L);
        printerReadDO.setPrinterGuid("printerGuid");
        printerReadDO.setPrinterName("printerName");
        printerReadDO.setBusinessType(0);
        printerReadDO.setPrinterType(0);
        printerReadDO.setPrinterPort(0);
        printerReadDO.setDeviceNo("deviceNo");
        when(mockPrinterMapstruct.toPrinterDTO(printerReadDO)).thenReturn(printerDTO3);

        // Configure CloudPrinterService.queryPrinterInfo(...).
        final FeieRespDTO feieRespDTO = new FeieRespDTO();
        feieRespDTO.setMsg("msg");
        feieRespDTO.setRet(0);
        final FeieRespDTO.DataMsg data = new FeieRespDTO.DataMsg();
        data.setOk(Arrays.asList("value"));
        data.setStatus(0);
        feieRespDTO.setData(data);
        when(mockCloudPrinterService.queryPrinterInfo("deviceNo")).thenReturn(feieRespDTO);

        // Run the test
        final List<PrinterDTO> result = printerServiceImplUnderTest.listPrinterOfBizType(printerDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testUpdatePrinter() {
        // Setup
        final PrinterDTO printerDTO = new PrinterDTO();
        printerDTO.setStoreGuid("storeGuid");
        printerDTO.setStoreName("storeName");
        printerDTO.setDeviceId("deviceId");
        printerDTO.setPrinterGuid("printerGuid");
        printerDTO.setPrinterName("printerName");
        printerDTO.setBusinessType(0);
        printerDTO.setPrinterType(0);
        printerDTO.setPrinterIp("printerIp");
        printerDTO.setPrinterPort(0);
        printerDTO.setPrintPage("printPage");
        printerDTO.setPrintCut(0);
        printerDTO.setArrayOfInvoiceType(Arrays.asList(0));
        printerDTO.setArrayOfItemGuid(Arrays.asList("value"));
        printerDTO.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterInvoiceDTO printerInvoiceDTO = new PrinterInvoiceDTO();
        printerInvoiceDTO.setInvoiceType(0);
        printerInvoiceDTO.setInvoiceName("invoiceName");
        printerInvoiceDTO.setPrintCount(0);
        printerDTO.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO));
        final PrinterItemDTO printerItemDTO = new PrinterItemDTO();
        printerDTO.setArrayOfItemDTO(Arrays.asList(printerItemDTO));
        final PrinterAreaDTO printerAreaDTO = new PrinterAreaDTO();
        printerDTO.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO));
        printerDTO.setDeviceNo("deviceNo");
        printerDTO.setDeviceKey("deviceKey");
        printerDTO.setManufacturersType(0);
        printerDTO.setDeviceState(0);

        // Configure PrinterMapstruct.toPrinterDTO(...).
        final PrinterDTO printerDTO1 = new PrinterDTO();
        printerDTO1.setStoreGuid("storeGuid");
        printerDTO1.setStoreName("storeName");
        printerDTO1.setDeviceId("deviceId");
        printerDTO1.setPrinterGuid("printerGuid");
        printerDTO1.setPrinterName("printerName");
        printerDTO1.setBusinessType(0);
        printerDTO1.setPrinterType(0);
        printerDTO1.setPrinterIp("printerIp");
        printerDTO1.setPrinterPort(0);
        printerDTO1.setPrintPage("printPage");
        printerDTO1.setPrintCut(0);
        printerDTO1.setArrayOfInvoiceType(Arrays.asList(0));
        printerDTO1.setArrayOfItemGuid(Arrays.asList("value"));
        printerDTO1.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterInvoiceDTO printerInvoiceDTO1 = new PrinterInvoiceDTO();
        printerInvoiceDTO1.setInvoiceType(0);
        printerInvoiceDTO1.setInvoiceName("invoiceName");
        printerInvoiceDTO1.setPrintCount(0);
        printerDTO1.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO1));
        final PrinterItemDTO printerItemDTO1 = new PrinterItemDTO();
        printerDTO1.setArrayOfItemDTO(Arrays.asList(printerItemDTO1));
        final PrinterAreaDTO printerAreaDTO1 = new PrinterAreaDTO();
        printerDTO1.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO1));
        printerDTO1.setDeviceNo("deviceNo");
        printerDTO1.setDeviceKey("deviceKey");
        printerDTO1.setManufacturersType(0);
        printerDTO1.setDeviceState(0);
        final PrinterReadDO printerReadDO = new PrinterReadDO();
        printerReadDO.setId(0L);
        printerReadDO.setPrinterGuid("printerGuid");
        printerReadDO.setPrinterName("printerName");
        printerReadDO.setBusinessType(0);
        printerReadDO.setPrinterType(0);
        printerReadDO.setPrinterPort(0);
        printerReadDO.setDeviceNo("deviceNo");
        when(mockPrinterMapstruct.toPrinterDTO(printerReadDO)).thenReturn(printerDTO1);

        // Configure PrinterMapstruct.fromPrinterDTO(...).
        final PrinterDO printerDO = new PrinterDO();
        printerDO.setId(0L);
        printerDO.setStoreGuid("storeGuid");
        printerDO.setDeviceId("0");
        printerDO.setPrinterGuid("printerGuid");
        printerDO.setPrinterName("printerName");
        printerDO.setBusinessType(0);
        printerDO.setPrinterType(0);
        printerDO.setPrinterIp("printerIp");
        printerDO.setPrintCount(0);
        printerDO.setPrintCut(0);
        printerDO.setIsMaster(false);
        final PrinterDTO printerDTO2 = new PrinterDTO();
        printerDTO2.setStoreGuid("storeGuid");
        printerDTO2.setStoreName("storeName");
        printerDTO2.setDeviceId("deviceId");
        printerDTO2.setPrinterGuid("printerGuid");
        printerDTO2.setPrinterName("printerName");
        printerDTO2.setBusinessType(0);
        printerDTO2.setPrinterType(0);
        printerDTO2.setPrinterIp("printerIp");
        printerDTO2.setPrinterPort(0);
        printerDTO2.setPrintPage("printPage");
        printerDTO2.setPrintCut(0);
        printerDTO2.setArrayOfInvoiceType(Arrays.asList(0));
        printerDTO2.setArrayOfItemGuid(Arrays.asList("value"));
        printerDTO2.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterInvoiceDTO printerInvoiceDTO2 = new PrinterInvoiceDTO();
        printerInvoiceDTO2.setInvoiceType(0);
        printerInvoiceDTO2.setInvoiceName("invoiceName");
        printerInvoiceDTO2.setPrintCount(0);
        printerDTO2.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO2));
        final PrinterItemDTO printerItemDTO2 = new PrinterItemDTO();
        printerDTO2.setArrayOfItemDTO(Arrays.asList(printerItemDTO2));
        final PrinterAreaDTO printerAreaDTO2 = new PrinterAreaDTO();
        printerDTO2.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO2));
        printerDTO2.setDeviceNo("deviceNo");
        printerDTO2.setDeviceKey("deviceKey");
        printerDTO2.setManufacturersType(0);
        printerDTO2.setDeviceState(0);
        when(mockPrinterMapstruct.fromPrinterDTO(printerDTO2)).thenReturn(printerDO);

        when(mockStoreDeviceService.isMasterDevice("storeGuid", "deviceId")).thenReturn(false);

        // Run the test
        printerServiceImplUnderTest.updatePrinter(printerDTO);

        // Verify the results
        verify(mockPrinterInvoiceService).deletePrinterInvoice("printerGuid");
        verify(mockPrinterItemService).deletePrinterItem("printerGuid");
        verify(mockPrinterAreaService).deletePrinterArea("printerGuid");

        // Confirm PrinterInvoiceService.bindPrinterInvoice(...).
        final PrinterDTO printerDTO3 = new PrinterDTO();
        printerDTO3.setStoreGuid("storeGuid");
        printerDTO3.setStoreName("storeName");
        printerDTO3.setDeviceId("deviceId");
        printerDTO3.setPrinterGuid("printerGuid");
        printerDTO3.setPrinterName("printerName");
        printerDTO3.setBusinessType(0);
        printerDTO3.setPrinterType(0);
        printerDTO3.setPrinterIp("printerIp");
        printerDTO3.setPrinterPort(0);
        printerDTO3.setPrintPage("printPage");
        printerDTO3.setPrintCut(0);
        printerDTO3.setArrayOfInvoiceType(Arrays.asList(0));
        printerDTO3.setArrayOfItemGuid(Arrays.asList("value"));
        printerDTO3.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterInvoiceDTO printerInvoiceDTO3 = new PrinterInvoiceDTO();
        printerInvoiceDTO3.setInvoiceType(0);
        printerInvoiceDTO3.setInvoiceName("invoiceName");
        printerInvoiceDTO3.setPrintCount(0);
        printerDTO3.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO3));
        final PrinterItemDTO printerItemDTO3 = new PrinterItemDTO();
        printerDTO3.setArrayOfItemDTO(Arrays.asList(printerItemDTO3));
        final PrinterAreaDTO printerAreaDTO3 = new PrinterAreaDTO();
        printerDTO3.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO3));
        printerDTO3.setDeviceNo("deviceNo");
        printerDTO3.setDeviceKey("deviceKey");
        printerDTO3.setManufacturersType(0);
        printerDTO3.setDeviceState(0);
        verify(mockPrinterInvoiceService).bindPrinterInvoice(printerDTO3);

        // Confirm PrinterItemService.bindPrinterItem(...).
        final PrinterDTO printerDTO4 = new PrinterDTO();
        printerDTO4.setStoreGuid("storeGuid");
        printerDTO4.setStoreName("storeName");
        printerDTO4.setDeviceId("deviceId");
        printerDTO4.setPrinterGuid("printerGuid");
        printerDTO4.setPrinterName("printerName");
        printerDTO4.setBusinessType(0);
        printerDTO4.setPrinterType(0);
        printerDTO4.setPrinterIp("printerIp");
        printerDTO4.setPrinterPort(0);
        printerDTO4.setPrintPage("printPage");
        printerDTO4.setPrintCut(0);
        printerDTO4.setArrayOfInvoiceType(Arrays.asList(0));
        printerDTO4.setArrayOfItemGuid(Arrays.asList("value"));
        printerDTO4.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterInvoiceDTO printerInvoiceDTO4 = new PrinterInvoiceDTO();
        printerInvoiceDTO4.setInvoiceType(0);
        printerInvoiceDTO4.setInvoiceName("invoiceName");
        printerInvoiceDTO4.setPrintCount(0);
        printerDTO4.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO4));
        final PrinterItemDTO printerItemDTO4 = new PrinterItemDTO();
        printerDTO4.setArrayOfItemDTO(Arrays.asList(printerItemDTO4));
        final PrinterAreaDTO printerAreaDTO4 = new PrinterAreaDTO();
        printerDTO4.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO4));
        printerDTO4.setDeviceNo("deviceNo");
        printerDTO4.setDeviceKey("deviceKey");
        printerDTO4.setManufacturersType(0);
        printerDTO4.setDeviceState(0);
        verify(mockPrinterItemService).bindPrinterItem(printerDTO4);

        // Confirm PrinterAreaService.bindPrinterArea(...).
        final PrinterDTO printerDTO5 = new PrinterDTO();
        printerDTO5.setStoreGuid("storeGuid");
        printerDTO5.setStoreName("storeName");
        printerDTO5.setDeviceId("deviceId");
        printerDTO5.setPrinterGuid("printerGuid");
        printerDTO5.setPrinterName("printerName");
        printerDTO5.setBusinessType(0);
        printerDTO5.setPrinterType(0);
        printerDTO5.setPrinterIp("printerIp");
        printerDTO5.setPrinterPort(0);
        printerDTO5.setPrintPage("printPage");
        printerDTO5.setPrintCut(0);
        printerDTO5.setArrayOfInvoiceType(Arrays.asList(0));
        printerDTO5.setArrayOfItemGuid(Arrays.asList("value"));
        printerDTO5.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterInvoiceDTO printerInvoiceDTO5 = new PrinterInvoiceDTO();
        printerInvoiceDTO5.setInvoiceType(0);
        printerInvoiceDTO5.setInvoiceName("invoiceName");
        printerInvoiceDTO5.setPrintCount(0);
        printerDTO5.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO5));
        final PrinterItemDTO printerItemDTO5 = new PrinterItemDTO();
        printerDTO5.setArrayOfItemDTO(Arrays.asList(printerItemDTO5));
        final PrinterAreaDTO printerAreaDTO5 = new PrinterAreaDTO();
        printerDTO5.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO5));
        printerDTO5.setDeviceNo("deviceNo");
        printerDTO5.setDeviceKey("deviceKey");
        printerDTO5.setManufacturersType(0);
        printerDTO5.setDeviceState(0);
        verify(mockPrinterAreaService).bindPrinterArea(printerDTO5);

        // Confirm PrinterLogService.publishUpdateLog(...).
        final PrinterDTO printerDtoBefore = new PrinterDTO();
        printerDtoBefore.setStoreGuid("storeGuid");
        printerDtoBefore.setStoreName("storeName");
        printerDtoBefore.setDeviceId("deviceId");
        printerDtoBefore.setPrinterGuid("printerGuid");
        printerDtoBefore.setPrinterName("printerName");
        printerDtoBefore.setBusinessType(0);
        printerDtoBefore.setPrinterType(0);
        printerDtoBefore.setPrinterIp("printerIp");
        printerDtoBefore.setPrinterPort(0);
        printerDtoBefore.setPrintPage("printPage");
        printerDtoBefore.setPrintCut(0);
        printerDtoBefore.setArrayOfInvoiceType(Arrays.asList(0));
        printerDtoBefore.setArrayOfItemGuid(Arrays.asList("value"));
        printerDtoBefore.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterInvoiceDTO printerInvoiceDTO6 = new PrinterInvoiceDTO();
        printerInvoiceDTO6.setInvoiceType(0);
        printerInvoiceDTO6.setInvoiceName("invoiceName");
        printerInvoiceDTO6.setPrintCount(0);
        printerDtoBefore.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO6));
        final PrinterItemDTO printerItemDTO6 = new PrinterItemDTO();
        printerDtoBefore.setArrayOfItemDTO(Arrays.asList(printerItemDTO6));
        final PrinterAreaDTO printerAreaDTO6 = new PrinterAreaDTO();
        printerDtoBefore.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO6));
        printerDtoBefore.setDeviceNo("deviceNo");
        printerDtoBefore.setDeviceKey("deviceKey");
        printerDtoBefore.setManufacturersType(0);
        printerDtoBefore.setDeviceState(0);
        final PrinterDTO printerDtoAfter = new PrinterDTO();
        printerDtoAfter.setStoreGuid("storeGuid");
        printerDtoAfter.setStoreName("storeName");
        printerDtoAfter.setDeviceId("deviceId");
        printerDtoAfter.setPrinterGuid("printerGuid");
        printerDtoAfter.setPrinterName("printerName");
        printerDtoAfter.setBusinessType(0);
        printerDtoAfter.setPrinterType(0);
        printerDtoAfter.setPrinterIp("printerIp");
        printerDtoAfter.setPrinterPort(0);
        printerDtoAfter.setPrintPage("printPage");
        printerDtoAfter.setPrintCut(0);
        printerDtoAfter.setArrayOfInvoiceType(Arrays.asList(0));
        printerDtoAfter.setArrayOfItemGuid(Arrays.asList("value"));
        printerDtoAfter.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterInvoiceDTO printerInvoiceDTO7 = new PrinterInvoiceDTO();
        printerInvoiceDTO7.setInvoiceType(0);
        printerInvoiceDTO7.setInvoiceName("invoiceName");
        printerInvoiceDTO7.setPrintCount(0);
        printerDtoAfter.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO7));
        final PrinterItemDTO printerItemDTO7 = new PrinterItemDTO();
        printerDtoAfter.setArrayOfItemDTO(Arrays.asList(printerItemDTO7));
        final PrinterAreaDTO printerAreaDTO7 = new PrinterAreaDTO();
        printerDtoAfter.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO7));
        printerDtoAfter.setDeviceNo("deviceNo");
        printerDtoAfter.setDeviceKey("deviceKey");
        printerDtoAfter.setManufacturersType(0);
        printerDtoAfter.setDeviceState(0);
        verify(mockPrinterLogService).publishUpdateLog(any(Object.class), eq(printerDtoBefore), eq(printerDtoAfter));
    }

    @Test
    public void testDeletePrinter() {
        // Setup
        final PrinterDTO printerDTO = new PrinterDTO();
        printerDTO.setStoreGuid("storeGuid");
        printerDTO.setStoreName("storeName");
        printerDTO.setDeviceId("deviceId");
        printerDTO.setPrinterGuid("printerGuid");
        printerDTO.setPrinterName("printerName");
        printerDTO.setBusinessType(0);
        printerDTO.setPrinterType(0);
        printerDTO.setPrinterIp("printerIp");
        printerDTO.setPrinterPort(0);
        printerDTO.setPrintPage("printPage");
        printerDTO.setPrintCut(0);
        printerDTO.setArrayOfInvoiceType(Arrays.asList(0));
        printerDTO.setArrayOfItemGuid(Arrays.asList("value"));
        printerDTO.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterInvoiceDTO printerInvoiceDTO = new PrinterInvoiceDTO();
        printerInvoiceDTO.setInvoiceType(0);
        printerInvoiceDTO.setInvoiceName("invoiceName");
        printerInvoiceDTO.setPrintCount(0);
        printerDTO.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO));
        final PrinterItemDTO printerItemDTO = new PrinterItemDTO();
        printerDTO.setArrayOfItemDTO(Arrays.asList(printerItemDTO));
        final PrinterAreaDTO printerAreaDTO = new PrinterAreaDTO();
        printerDTO.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO));
        printerDTO.setDeviceNo("deviceNo");
        printerDTO.setDeviceKey("deviceKey");
        printerDTO.setManufacturersType(0);
        printerDTO.setDeviceState(0);

        // Configure PrinterMapstruct.toPrinterDTO(...).
        final PrinterDTO printerDTO1 = new PrinterDTO();
        printerDTO1.setStoreGuid("storeGuid");
        printerDTO1.setStoreName("storeName");
        printerDTO1.setDeviceId("deviceId");
        printerDTO1.setPrinterGuid("printerGuid");
        printerDTO1.setPrinterName("printerName");
        printerDTO1.setBusinessType(0);
        printerDTO1.setPrinterType(0);
        printerDTO1.setPrinterIp("printerIp");
        printerDTO1.setPrinterPort(0);
        printerDTO1.setPrintPage("printPage");
        printerDTO1.setPrintCut(0);
        printerDTO1.setArrayOfInvoiceType(Arrays.asList(0));
        printerDTO1.setArrayOfItemGuid(Arrays.asList("value"));
        printerDTO1.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterInvoiceDTO printerInvoiceDTO1 = new PrinterInvoiceDTO();
        printerInvoiceDTO1.setInvoiceType(0);
        printerInvoiceDTO1.setInvoiceName("invoiceName");
        printerInvoiceDTO1.setPrintCount(0);
        printerDTO1.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO1));
        final PrinterItemDTO printerItemDTO1 = new PrinterItemDTO();
        printerDTO1.setArrayOfItemDTO(Arrays.asList(printerItemDTO1));
        final PrinterAreaDTO printerAreaDTO1 = new PrinterAreaDTO();
        printerDTO1.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO1));
        printerDTO1.setDeviceNo("deviceNo");
        printerDTO1.setDeviceKey("deviceKey");
        printerDTO1.setManufacturersType(0);
        printerDTO1.setDeviceState(0);
        final PrinterReadDO printerReadDO = new PrinterReadDO();
        printerReadDO.setId(0L);
        printerReadDO.setPrinterGuid("printerGuid");
        printerReadDO.setPrinterName("printerName");
        printerReadDO.setBusinessType(0);
        printerReadDO.setPrinterType(0);
        printerReadDO.setPrinterPort(0);
        printerReadDO.setDeviceNo("deviceNo");
        when(mockPrinterMapstruct.toPrinterDTO(printerReadDO)).thenReturn(printerDTO1);

        // Run the test
        printerServiceImplUnderTest.deletePrinter(printerDTO);

        // Verify the results
        verify(mockCloudPrinterService).deletePrinter("deviceNo");
        verify(mockPrinterInvoiceService).deletePrinterInvoice("printerGuid");
        verify(mockPrinterItemService).deletePrinterItem("printerGuid");
        verify(mockPrinterAreaService).deletePrinterArea("printerGuid");
        verify(mockPrintRecordService).deletePrinterRecord("printerGuid");

        // Confirm PrinterLogService.publishDeleteLog(...).
        final PrinterDTO printerDTO2 = new PrinterDTO();
        printerDTO2.setStoreGuid("storeGuid");
        printerDTO2.setStoreName("storeName");
        printerDTO2.setDeviceId("deviceId");
        printerDTO2.setPrinterGuid("printerGuid");
        printerDTO2.setPrinterName("printerName");
        printerDTO2.setBusinessType(0);
        printerDTO2.setPrinterType(0);
        printerDTO2.setPrinterIp("printerIp");
        printerDTO2.setPrinterPort(0);
        printerDTO2.setPrintPage("printPage");
        printerDTO2.setPrintCut(0);
        printerDTO2.setArrayOfInvoiceType(Arrays.asList(0));
        printerDTO2.setArrayOfItemGuid(Arrays.asList("value"));
        printerDTO2.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterInvoiceDTO printerInvoiceDTO2 = new PrinterInvoiceDTO();
        printerInvoiceDTO2.setInvoiceType(0);
        printerInvoiceDTO2.setInvoiceName("invoiceName");
        printerInvoiceDTO2.setPrintCount(0);
        printerDTO2.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO2));
        final PrinterItemDTO printerItemDTO2 = new PrinterItemDTO();
        printerDTO2.setArrayOfItemDTO(Arrays.asList(printerItemDTO2));
        final PrinterAreaDTO printerAreaDTO2 = new PrinterAreaDTO();
        printerDTO2.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO2));
        printerDTO2.setDeviceNo("deviceNo");
        printerDTO2.setDeviceKey("deviceKey");
        printerDTO2.setManufacturersType(0);
        printerDTO2.setDeviceState(0);
        verify(mockPrinterLogService).publishDeleteLog(any(Object.class), eq(printerDTO2));
    }

    @Test
    public void testDeletePrinterOfTheStore() {
        // Setup
        final PrinterDTO printerDTO = new PrinterDTO();
        printerDTO.setStoreGuid("storeGuid");
        printerDTO.setStoreName("storeName");
        printerDTO.setDeviceId("deviceId");
        printerDTO.setPrinterGuid("printerGuid");
        printerDTO.setPrinterName("printerName");
        printerDTO.setBusinessType(0);
        printerDTO.setPrinterType(0);
        printerDTO.setPrinterIp("printerIp");
        printerDTO.setPrinterPort(0);
        printerDTO.setPrintPage("printPage");
        printerDTO.setPrintCut(0);
        printerDTO.setArrayOfInvoiceType(Arrays.asList(0));
        printerDTO.setArrayOfItemGuid(Arrays.asList("value"));
        printerDTO.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterInvoiceDTO printerInvoiceDTO = new PrinterInvoiceDTO();
        printerInvoiceDTO.setInvoiceType(0);
        printerInvoiceDTO.setInvoiceName("invoiceName");
        printerInvoiceDTO.setPrintCount(0);
        printerDTO.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO));
        final PrinterItemDTO printerItemDTO = new PrinterItemDTO();
        printerDTO.setArrayOfItemDTO(Arrays.asList(printerItemDTO));
        final PrinterAreaDTO printerAreaDTO = new PrinterAreaDTO();
        printerDTO.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO));
        printerDTO.setDeviceNo("deviceNo");
        printerDTO.setDeviceKey("deviceKey");
        printerDTO.setManufacturersType(0);
        printerDTO.setDeviceState(0);

        // Run the test
        printerServiceImplUnderTest.deletePrinterOfTheStore(printerDTO);

        // Verify the results
        verify(mockPrinterInvoiceService).deleteStorePrinterInvoice("storeGuid");
        verify(mockPrinterItemService).deleteStorePrinterItem("storeGuid");
        verify(mockPrinterAreaService).deleteStorePrinterArea("storeGuid");
        verify(mockPrintRecordService).deleteStorePrintRecord("storeGuid");
    }

    @Test
    public void testDeletePrinterOfTheDevice() {
        // Setup
        final PrinterDTO printerDTO = new PrinterDTO();
        printerDTO.setStoreGuid("storeGuid");
        printerDTO.setStoreName("storeName");
        printerDTO.setDeviceId("deviceId");
        printerDTO.setPrinterGuid("printerGuid");
        printerDTO.setPrinterName("printerName");
        printerDTO.setBusinessType(0);
        printerDTO.setPrinterType(0);
        printerDTO.setPrinterIp("printerIp");
        printerDTO.setPrinterPort(0);
        printerDTO.setPrintPage("printPage");
        printerDTO.setPrintCut(0);
        printerDTO.setArrayOfInvoiceType(Arrays.asList(0));
        printerDTO.setArrayOfItemGuid(Arrays.asList("value"));
        printerDTO.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterInvoiceDTO printerInvoiceDTO = new PrinterInvoiceDTO();
        printerInvoiceDTO.setInvoiceType(0);
        printerInvoiceDTO.setInvoiceName("invoiceName");
        printerInvoiceDTO.setPrintCount(0);
        printerDTO.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO));
        final PrinterItemDTO printerItemDTO = new PrinterItemDTO();
        printerDTO.setArrayOfItemDTO(Arrays.asList(printerItemDTO));
        final PrinterAreaDTO printerAreaDTO = new PrinterAreaDTO();
        printerDTO.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO));
        printerDTO.setDeviceNo("deviceNo");
        printerDTO.setDeviceKey("deviceKey");
        printerDTO.setManufacturersType(0);
        printerDTO.setDeviceState(0);

        // Run the test
        printerServiceImplUnderTest.deletePrinterOfTheDevice(printerDTO);

        // Verify the results
        verify(mockPrinterItemService).batchDeletePrinterItem(Arrays.asList("value"));
        verify(mockPrinterAreaService).batchDeletePrinterArea(Arrays.asList("value"));
        verify(mockPrintRecordService).batchDeletePrinterRecord(Arrays.asList("value"));
    }

    @Test
    public void testChangeMasterPrinter() {
        // Setup
        final PrinterDTO printerDTO = new PrinterDTO();
        printerDTO.setStoreGuid("storeGuid");
        printerDTO.setStoreName("storeName");
        printerDTO.setDeviceId("deviceId");
        printerDTO.setPrinterGuid("printerGuid");
        printerDTO.setPrinterName("printerName");
        printerDTO.setBusinessType(0);
        printerDTO.setPrinterType(0);
        printerDTO.setPrinterIp("printerIp");
        printerDTO.setPrinterPort(0);
        printerDTO.setPrintPage("printPage");
        printerDTO.setPrintCut(0);
        printerDTO.setArrayOfInvoiceType(Arrays.asList(0));
        printerDTO.setArrayOfItemGuid(Arrays.asList("value"));
        printerDTO.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterInvoiceDTO printerInvoiceDTO = new PrinterInvoiceDTO();
        printerInvoiceDTO.setInvoiceType(0);
        printerInvoiceDTO.setInvoiceName("invoiceName");
        printerInvoiceDTO.setPrintCount(0);
        printerDTO.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO));
        final PrinterItemDTO printerItemDTO = new PrinterItemDTO();
        printerDTO.setArrayOfItemDTO(Arrays.asList(printerItemDTO));
        final PrinterAreaDTO printerAreaDTO = new PrinterAreaDTO();
        printerDTO.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO));
        printerDTO.setDeviceNo("deviceNo");
        printerDTO.setDeviceKey("deviceKey");
        printerDTO.setManufacturersType(0);
        printerDTO.setDeviceState(0);

        // Run the test
        printerServiceImplUnderTest.changeMasterPrinter(printerDTO);

        // Verify the results
    }

    @Test
    public void testFindTestOrderPrinter() {
        // Setup
        final PrinterDTO expectedResult = new PrinterDTO();
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setStoreName("storeName");
        expectedResult.setDeviceId("deviceId");
        expectedResult.setPrinterGuid("printerGuid");
        expectedResult.setPrinterName("printerName");
        expectedResult.setBusinessType(0);
        expectedResult.setPrinterType(0);
        expectedResult.setPrinterIp("printerIp");
        expectedResult.setPrinterPort(0);
        expectedResult.setPrintPage("printPage");
        expectedResult.setPrintCut(0);
        expectedResult.setArrayOfInvoiceType(Arrays.asList(0));
        expectedResult.setArrayOfItemGuid(Arrays.asList("value"));
        expectedResult.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterInvoiceDTO printerInvoiceDTO = new PrinterInvoiceDTO();
        printerInvoiceDTO.setInvoiceType(0);
        printerInvoiceDTO.setInvoiceName("invoiceName");
        printerInvoiceDTO.setPrintCount(0);
        expectedResult.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO));
        final PrinterItemDTO printerItemDTO = new PrinterItemDTO();
        expectedResult.setArrayOfItemDTO(Arrays.asList(printerItemDTO));
        final PrinterAreaDTO printerAreaDTO = new PrinterAreaDTO();
        expectedResult.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO));
        expectedResult.setDeviceNo("deviceNo");
        expectedResult.setDeviceKey("deviceKey");
        expectedResult.setManufacturersType(0);
        expectedResult.setDeviceState(0);

        // Configure PrinterMapstruct.fromPrinterDTO(...).
        final PrinterDO printerDO = new PrinterDO();
        printerDO.setId(0L);
        printerDO.setStoreGuid("storeGuid");
        printerDO.setDeviceId("0");
        printerDO.setPrinterGuid("printerGuid");
        printerDO.setPrinterName("printerName");
        printerDO.setBusinessType(0);
        printerDO.setPrinterType(0);
        printerDO.setPrinterIp("printerIp");
        printerDO.setPrintCount(0);
        printerDO.setPrintCut(0);
        printerDO.setIsMaster(false);
        final PrinterDTO printerDTO = new PrinterDTO();
        printerDTO.setStoreGuid("storeGuid");
        printerDTO.setStoreName("storeName");
        printerDTO.setDeviceId("deviceId");
        printerDTO.setPrinterGuid("printerGuid");
        printerDTO.setPrinterName("printerName");
        printerDTO.setBusinessType(0);
        printerDTO.setPrinterType(0);
        printerDTO.setPrinterIp("printerIp");
        printerDTO.setPrinterPort(0);
        printerDTO.setPrintPage("printPage");
        printerDTO.setPrintCut(0);
        printerDTO.setArrayOfInvoiceType(Arrays.asList(0));
        printerDTO.setArrayOfItemGuid(Arrays.asList("value"));
        printerDTO.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterInvoiceDTO printerInvoiceDTO1 = new PrinterInvoiceDTO();
        printerInvoiceDTO1.setInvoiceType(0);
        printerInvoiceDTO1.setInvoiceName("invoiceName");
        printerInvoiceDTO1.setPrintCount(0);
        printerDTO.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO1));
        final PrinterItemDTO printerItemDTO1 = new PrinterItemDTO();
        printerDTO.setArrayOfItemDTO(Arrays.asList(printerItemDTO1));
        final PrinterAreaDTO printerAreaDTO1 = new PrinterAreaDTO();
        printerDTO.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO1));
        printerDTO.setDeviceNo("deviceNo");
        printerDTO.setDeviceKey("deviceKey");
        printerDTO.setManufacturersType(0);
        printerDTO.setDeviceState(0);
        when(mockPrinterMapstruct.fromPrinterDTO(printerDTO)).thenReturn(printerDO);

        // Configure PrinterMapstruct.toPrinterDTO(...).
        final PrinterDTO printerDTO1 = new PrinterDTO();
        printerDTO1.setStoreGuid("storeGuid");
        printerDTO1.setStoreName("storeName");
        printerDTO1.setDeviceId("deviceId");
        printerDTO1.setPrinterGuid("printerGuid");
        printerDTO1.setPrinterName("printerName");
        printerDTO1.setBusinessType(0);
        printerDTO1.setPrinterType(0);
        printerDTO1.setPrinterIp("printerIp");
        printerDTO1.setPrinterPort(0);
        printerDTO1.setPrintPage("printPage");
        printerDTO1.setPrintCut(0);
        printerDTO1.setArrayOfInvoiceType(Arrays.asList(0));
        printerDTO1.setArrayOfItemGuid(Arrays.asList("value"));
        printerDTO1.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterInvoiceDTO printerInvoiceDTO2 = new PrinterInvoiceDTO();
        printerInvoiceDTO2.setInvoiceType(0);
        printerInvoiceDTO2.setInvoiceName("invoiceName");
        printerInvoiceDTO2.setPrintCount(0);
        printerDTO1.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO2));
        final PrinterItemDTO printerItemDTO2 = new PrinterItemDTO();
        printerDTO1.setArrayOfItemDTO(Arrays.asList(printerItemDTO2));
        final PrinterAreaDTO printerAreaDTO2 = new PrinterAreaDTO();
        printerDTO1.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO2));
        printerDTO1.setDeviceNo("deviceNo");
        printerDTO1.setDeviceKey("deviceKey");
        printerDTO1.setManufacturersType(0);
        printerDTO1.setDeviceState(0);
        final PrinterReadDO printerReadDO = new PrinterReadDO();
        printerReadDO.setId(0L);
        printerReadDO.setPrinterGuid("printerGuid");
        printerReadDO.setPrinterName("printerName");
        printerReadDO.setBusinessType(0);
        printerReadDO.setPrinterType(0);
        printerReadDO.setPrinterPort(0);
        printerReadDO.setDeviceNo("deviceNo");
        when(mockPrinterMapstruct.toPrinterDTO(printerReadDO)).thenReturn(printerDTO1);

        // Configure CloudPrinterService.queryPrinterInfo(...).
        final FeieRespDTO feieRespDTO = new FeieRespDTO();
        feieRespDTO.setMsg("msg");
        feieRespDTO.setRet(0);
        final FeieRespDTO.DataMsg data = new FeieRespDTO.DataMsg();
        data.setOk(Arrays.asList("value"));
        data.setStatus(0);
        feieRespDTO.setData(data);
        when(mockCloudPrinterService.queryPrinterInfo("deviceNo")).thenReturn(feieRespDTO);

        // Run the test
        final PrinterDTO result = printerServiceImplUnderTest.findTestOrderPrinter("storeGuid", "deviceId", 0, 0);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testFindTestOrderPrinters() {
        // Setup
        final PrinterDTO printerDTO = new PrinterDTO();
        printerDTO.setStoreGuid("storeGuid");
        printerDTO.setStoreName("storeName");
        printerDTO.setDeviceId("deviceId");
        printerDTO.setPrinterGuid("printerGuid");
        printerDTO.setPrinterName("printerName");
        printerDTO.setBusinessType(0);
        printerDTO.setPrinterType(0);
        printerDTO.setPrinterIp("printerIp");
        printerDTO.setPrinterPort(0);
        printerDTO.setPrintPage("printPage");
        printerDTO.setPrintCut(0);
        printerDTO.setArrayOfInvoiceType(Arrays.asList(0));
        printerDTO.setArrayOfItemGuid(Arrays.asList("value"));
        printerDTO.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterInvoiceDTO printerInvoiceDTO = new PrinterInvoiceDTO();
        printerInvoiceDTO.setInvoiceType(0);
        printerInvoiceDTO.setInvoiceName("invoiceName");
        printerInvoiceDTO.setPrintCount(0);
        printerDTO.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO));
        final PrinterItemDTO printerItemDTO = new PrinterItemDTO();
        printerDTO.setArrayOfItemDTO(Arrays.asList(printerItemDTO));
        final PrinterAreaDTO printerAreaDTO = new PrinterAreaDTO();
        printerDTO.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO));
        printerDTO.setDeviceNo("deviceNo");
        printerDTO.setDeviceKey("deviceKey");
        printerDTO.setManufacturersType(0);
        printerDTO.setDeviceState(0);
        final List<PrinterDTO> expectedResult = Arrays.asList(printerDTO);

        // Configure PrinterMapstruct.fromPrinterDTO(...).
        final PrinterDO printerDO = new PrinterDO();
        printerDO.setId(0L);
        printerDO.setStoreGuid("storeGuid");
        printerDO.setDeviceId("0");
        printerDO.setPrinterGuid("printerGuid");
        printerDO.setPrinterName("printerName");
        printerDO.setBusinessType(0);
        printerDO.setPrinterType(0);
        printerDO.setPrinterIp("printerIp");
        printerDO.setPrintCount(0);
        printerDO.setPrintCut(0);
        printerDO.setIsMaster(false);
        final PrinterDTO printerDTO1 = new PrinterDTO();
        printerDTO1.setStoreGuid("storeGuid");
        printerDTO1.setStoreName("storeName");
        printerDTO1.setDeviceId("deviceId");
        printerDTO1.setPrinterGuid("printerGuid");
        printerDTO1.setPrinterName("printerName");
        printerDTO1.setBusinessType(0);
        printerDTO1.setPrinterType(0);
        printerDTO1.setPrinterIp("printerIp");
        printerDTO1.setPrinterPort(0);
        printerDTO1.setPrintPage("printPage");
        printerDTO1.setPrintCut(0);
        printerDTO1.setArrayOfInvoiceType(Arrays.asList(0));
        printerDTO1.setArrayOfItemGuid(Arrays.asList("value"));
        printerDTO1.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterInvoiceDTO printerInvoiceDTO1 = new PrinterInvoiceDTO();
        printerInvoiceDTO1.setInvoiceType(0);
        printerInvoiceDTO1.setInvoiceName("invoiceName");
        printerInvoiceDTO1.setPrintCount(0);
        printerDTO1.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO1));
        final PrinterItemDTO printerItemDTO1 = new PrinterItemDTO();
        printerDTO1.setArrayOfItemDTO(Arrays.asList(printerItemDTO1));
        final PrinterAreaDTO printerAreaDTO1 = new PrinterAreaDTO();
        printerDTO1.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO1));
        printerDTO1.setDeviceNo("deviceNo");
        printerDTO1.setDeviceKey("deviceKey");
        printerDTO1.setManufacturersType(0);
        printerDTO1.setDeviceState(0);
        when(mockPrinterMapstruct.fromPrinterDTO(printerDTO1)).thenReturn(printerDO);

        // Configure PrinterMapstruct.toPrinterDTO(...).
        final PrinterDTO printerDTO2 = new PrinterDTO();
        printerDTO2.setStoreGuid("storeGuid");
        printerDTO2.setStoreName("storeName");
        printerDTO2.setDeviceId("deviceId");
        printerDTO2.setPrinterGuid("printerGuid");
        printerDTO2.setPrinterName("printerName");
        printerDTO2.setBusinessType(0);
        printerDTO2.setPrinterType(0);
        printerDTO2.setPrinterIp("printerIp");
        printerDTO2.setPrinterPort(0);
        printerDTO2.setPrintPage("printPage");
        printerDTO2.setPrintCut(0);
        printerDTO2.setArrayOfInvoiceType(Arrays.asList(0));
        printerDTO2.setArrayOfItemGuid(Arrays.asList("value"));
        printerDTO2.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterInvoiceDTO printerInvoiceDTO2 = new PrinterInvoiceDTO();
        printerInvoiceDTO2.setInvoiceType(0);
        printerInvoiceDTO2.setInvoiceName("invoiceName");
        printerInvoiceDTO2.setPrintCount(0);
        printerDTO2.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO2));
        final PrinterItemDTO printerItemDTO2 = new PrinterItemDTO();
        printerDTO2.setArrayOfItemDTO(Arrays.asList(printerItemDTO2));
        final PrinterAreaDTO printerAreaDTO2 = new PrinterAreaDTO();
        printerDTO2.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO2));
        printerDTO2.setDeviceNo("deviceNo");
        printerDTO2.setDeviceKey("deviceKey");
        printerDTO2.setManufacturersType(0);
        printerDTO2.setDeviceState(0);
        final PrinterReadDO printerReadDO = new PrinterReadDO();
        printerReadDO.setId(0L);
        printerReadDO.setPrinterGuid("printerGuid");
        printerReadDO.setPrinterName("printerName");
        printerReadDO.setBusinessType(0);
        printerReadDO.setPrinterType(0);
        printerReadDO.setPrinterPort(0);
        printerReadDO.setDeviceNo("deviceNo");
        when(mockPrinterMapstruct.toPrinterDTO(printerReadDO)).thenReturn(printerDTO2);

        // Configure CloudPrinterService.queryPrinterInfo(...).
        final FeieRespDTO feieRespDTO = new FeieRespDTO();
        feieRespDTO.setMsg("msg");
        feieRespDTO.setRet(0);
        final FeieRespDTO.DataMsg data = new FeieRespDTO.DataMsg();
        data.setOk(Arrays.asList("value"));
        data.setStatus(0);
        feieRespDTO.setData(data);
        when(mockCloudPrinterService.queryPrinterInfo("deviceNo")).thenReturn(feieRespDTO);

        // Run the test
        final List<PrinterDTO> result = printerServiceImplUnderTest.findTestOrderPrinters("storeGuid", "deviceId", 0);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testFindMasterPrinter() {
        // Setup
        final PrinterDO expectedResult = new PrinterDO();
        expectedResult.setId(0L);
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setDeviceId("0");
        expectedResult.setPrinterGuid("printerGuid");
        expectedResult.setPrinterName("printerName");
        expectedResult.setBusinessType(0);
        expectedResult.setPrinterType(0);
        expectedResult.setPrinterIp("printerIp");
        expectedResult.setPrintCount(0);
        expectedResult.setPrintCut(0);
        expectedResult.setIsMaster(false);

        // Run the test
        final PrinterDO result = printerServiceImplUnderTest.findMasterPrinter("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testFindMasterPrinterDeviceId() {
        // Setup
        when(mockStoreDeviceService.findMasterDevice("storeGuid")).thenReturn("result");

        // Run the test
        final String result = printerServiceImplUnderTest.findMasterPrinterDeviceId("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo("result");
    }

    @Test
    public void testFindMasterPrinterDeviceIdOrElseThrow() {
        // Setup
        when(mockStoreDeviceService.findMasterDevice("storeGuid")).thenReturn("result");

        // Run the test
        final String result = printerServiceImplUnderTest.findMasterPrinterDeviceIdOrElseThrow("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo("result");
    }

    @Test
    public void testIsMasterPrinterDevice() {
        // Setup
        when(mockStoreDeviceService.findMasterDevice("storeGuid")).thenReturn("result");

        // Run the test
        final boolean result = printerServiceImplUnderTest.isMasterPrinterDevice("storeGuid", "deviceId");

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testFindPrinterByQuery() {
        // Setup
        final PrinterQuery printerQuery = new PrinterQuery();
        printerQuery.setStoreGuid("storeGuid");
        printerQuery.setInvoiceType(0);
        printerQuery.setDeviceId("deviceId");
        printerQuery.setAreaGuid("areaGuid");
        printerQuery.setBusinessType(0);

        final PrinterReadDO printerReadDO = new PrinterReadDO();
        printerReadDO.setId(0L);
        printerReadDO.setPrinterGuid("printerGuid");
        printerReadDO.setPrinterName("printerName");
        printerReadDO.setBusinessType(0);
        printerReadDO.setPrinterType(0);
        printerReadDO.setPrinterPort(0);
        printerReadDO.setDeviceNo("deviceNo");
        final List<PrinterReadDO> expectedResult = Arrays.asList(printerReadDO);

        // Run the test
        final List<PrinterReadDO> result = printerServiceImplUnderTest.findPrinterByQuery(printerQuery);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testFindPrinterOfKitchenTable() {
        // Setup
        final PrinterQuery printerQuery = new PrinterQuery();
        printerQuery.setStoreGuid("storeGuid");
        printerQuery.setInvoiceType(0);
        printerQuery.setDeviceId("deviceId");
        printerQuery.setAreaGuid("areaGuid");
        printerQuery.setBusinessType(0);

        final PrinterReadDO printerReadDO = new PrinterReadDO();
        printerReadDO.setId(0L);
        printerReadDO.setPrinterGuid("printerGuid");
        printerReadDO.setPrinterName("printerName");
        printerReadDO.setBusinessType(0);
        printerReadDO.setPrinterType(0);
        printerReadDO.setPrinterPort(0);
        printerReadDO.setDeviceNo("deviceNo");
        final List<PrinterReadDO> expectedResult = Arrays.asList(printerReadDO);

        // Run the test
        final List<PrinterReadDO> result = printerServiceImplUnderTest.findPrinterOfKitchenTable(printerQuery);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testListRaw() {
        // Setup
        final PrinterRawDTO printerRawDTO = new PrinterRawDTO();
        printerRawDTO.setStoreGuid("storeGuid");
        printerRawDTO.setStoreName("storeName");
        printerRawDTO.setDeviceId("deviceId");
        printerRawDTO.setPrinterGuid("printerGuid");
        printerRawDTO.setPrinterName("printerName");
        final List<PrinterRawDTO> expectedResult = Arrays.asList(printerRawDTO);

        // Configure PrinterRawMaptstruct.toPrinterRawDTO(...).
        final PrinterRawDTO printerRawDTO1 = new PrinterRawDTO();
        printerRawDTO1.setStoreGuid("storeGuid");
        printerRawDTO1.setStoreName("storeName");
        printerRawDTO1.setDeviceId("deviceId");
        printerRawDTO1.setPrinterGuid("printerGuid");
        printerRawDTO1.setPrinterName("printerName");
        final List<PrinterRawDTO> printerRawDTOS = Arrays.asList(printerRawDTO1);
        final PrinterDO printerDO = new PrinterDO();
        printerDO.setId(0L);
        printerDO.setStoreGuid("storeGuid");
        printerDO.setDeviceId("0");
        printerDO.setPrinterGuid("printerGuid");
        printerDO.setPrinterName("printerName");
        printerDO.setBusinessType(0);
        printerDO.setPrinterType(0);
        printerDO.setPrinterIp("printerIp");
        printerDO.setPrintCount(0);
        printerDO.setPrintCut(0);
        printerDO.setIsMaster(false);
        final List<PrinterDO> list = Arrays.asList(printerDO);
        when(mockPrinterRawMaptstruct.toPrinterRawDTO(list)).thenReturn(printerRawDTOS);

        // Run the test
        final List<PrinterRawDTO> result = printerServiceImplUnderTest.listRaw("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testListRaw_PrinterRawMaptstructReturnsNoItems() {
        // Setup
        // Configure PrinterRawMaptstruct.toPrinterRawDTO(...).
        final PrinterDO printerDO = new PrinterDO();
        printerDO.setId(0L);
        printerDO.setStoreGuid("storeGuid");
        printerDO.setDeviceId("0");
        printerDO.setPrinterGuid("printerGuid");
        printerDO.setPrinterName("printerName");
        printerDO.setBusinessType(0);
        printerDO.setPrinterType(0);
        printerDO.setPrinterIp("printerIp");
        printerDO.setPrintCount(0);
        printerDO.setPrintCut(0);
        printerDO.setIsMaster(false);
        final List<PrinterDO> list = Arrays.asList(printerDO);
        when(mockPrinterRawMaptstruct.toPrinterRawDTO(list)).thenReturn(Collections.emptyList());

        // Run the test
        final List<PrinterRawDTO> result = printerServiceImplUnderTest.listRaw("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testBackupsPrinter() {
        // Setup
        // Configure PrinterMapstruct.fromPrinterDTO(...).
        final PrinterDO printerDO = new PrinterDO();
        printerDO.setId(0L);
        printerDO.setStoreGuid("storeGuid");
        printerDO.setDeviceId("0");
        printerDO.setPrinterGuid("printerGuid");
        printerDO.setPrinterName("printerName");
        printerDO.setBusinessType(0);
        printerDO.setPrinterType(0);
        printerDO.setPrinterIp("printerIp");
        printerDO.setPrintCount(0);
        printerDO.setPrintCut(0);
        printerDO.setIsMaster(false);
        final PrinterDTO printerDTO = new PrinterDTO();
        printerDTO.setStoreGuid("storeGuid");
        printerDTO.setStoreName("storeName");
        printerDTO.setDeviceId("deviceId");
        printerDTO.setPrinterGuid("printerGuid");
        printerDTO.setPrinterName("printerName");
        printerDTO.setBusinessType(0);
        printerDTO.setPrinterType(0);
        printerDTO.setPrinterIp("printerIp");
        printerDTO.setPrinterPort(0);
        printerDTO.setPrintPage("printPage");
        printerDTO.setPrintCut(0);
        printerDTO.setArrayOfInvoiceType(Arrays.asList(0));
        printerDTO.setArrayOfItemGuid(Arrays.asList("value"));
        printerDTO.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterInvoiceDTO printerInvoiceDTO = new PrinterInvoiceDTO();
        printerInvoiceDTO.setInvoiceType(0);
        printerInvoiceDTO.setInvoiceName("invoiceName");
        printerInvoiceDTO.setPrintCount(0);
        printerDTO.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO));
        final PrinterItemDTO printerItemDTO = new PrinterItemDTO();
        printerDTO.setArrayOfItemDTO(Arrays.asList(printerItemDTO));
        final PrinterAreaDTO printerAreaDTO = new PrinterAreaDTO();
        printerDTO.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO));
        printerDTO.setDeviceNo("deviceNo");
        printerDTO.setDeviceKey("deviceKey");
        printerDTO.setManufacturersType(0);
        printerDTO.setDeviceState(0);
        when(mockPrinterMapstruct.fromPrinterDTO(printerDTO)).thenReturn(printerDO);

        // Configure PrinterMapstruct.toPrinterDTO(...).
        final PrinterDTO printerDTO1 = new PrinterDTO();
        printerDTO1.setStoreGuid("storeGuid");
        printerDTO1.setStoreName("storeName");
        printerDTO1.setDeviceId("deviceId");
        printerDTO1.setPrinterGuid("printerGuid");
        printerDTO1.setPrinterName("printerName");
        printerDTO1.setBusinessType(0);
        printerDTO1.setPrinterType(0);
        printerDTO1.setPrinterIp("printerIp");
        printerDTO1.setPrinterPort(0);
        printerDTO1.setPrintPage("printPage");
        printerDTO1.setPrintCut(0);
        printerDTO1.setArrayOfInvoiceType(Arrays.asList(0));
        printerDTO1.setArrayOfItemGuid(Arrays.asList("value"));
        printerDTO1.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterInvoiceDTO printerInvoiceDTO1 = new PrinterInvoiceDTO();
        printerInvoiceDTO1.setInvoiceType(0);
        printerInvoiceDTO1.setInvoiceName("invoiceName");
        printerInvoiceDTO1.setPrintCount(0);
        printerDTO1.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO1));
        final PrinterItemDTO printerItemDTO1 = new PrinterItemDTO();
        printerDTO1.setArrayOfItemDTO(Arrays.asList(printerItemDTO1));
        final PrinterAreaDTO printerAreaDTO1 = new PrinterAreaDTO();
        printerDTO1.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO1));
        printerDTO1.setDeviceNo("deviceNo");
        printerDTO1.setDeviceKey("deviceKey");
        printerDTO1.setManufacturersType(0);
        printerDTO1.setDeviceState(0);
        final PrinterReadDO printerReadDO = new PrinterReadDO();
        printerReadDO.setId(0L);
        printerReadDO.setPrinterGuid("printerGuid");
        printerReadDO.setPrinterName("printerName");
        printerReadDO.setBusinessType(0);
        printerReadDO.setPrinterType(0);
        printerReadDO.setPrinterPort(0);
        printerReadDO.setDeviceNo("deviceNo");
        when(mockPrinterMapstruct.toPrinterDTO(printerReadDO)).thenReturn(printerDTO1);

        // Configure PrinterBackupsService.backupsPrinter(...).
        final PrinterDTO printerDTO2 = new PrinterDTO();
        printerDTO2.setStoreGuid("storeGuid");
        printerDTO2.setStoreName("storeName");
        printerDTO2.setDeviceId("deviceId");
        printerDTO2.setPrinterGuid("printerGuid");
        printerDTO2.setPrinterName("printerName");
        printerDTO2.setBusinessType(0);
        printerDTO2.setPrinterType(0);
        printerDTO2.setPrinterIp("printerIp");
        printerDTO2.setPrinterPort(0);
        printerDTO2.setPrintPage("printPage");
        printerDTO2.setPrintCut(0);
        printerDTO2.setArrayOfInvoiceType(Arrays.asList(0));
        printerDTO2.setArrayOfItemGuid(Arrays.asList("value"));
        printerDTO2.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterInvoiceDTO printerInvoiceDTO2 = new PrinterInvoiceDTO();
        printerInvoiceDTO2.setInvoiceType(0);
        printerInvoiceDTO2.setInvoiceName("invoiceName");
        printerInvoiceDTO2.setPrintCount(0);
        printerDTO2.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO2));
        final PrinterItemDTO printerItemDTO2 = new PrinterItemDTO();
        printerDTO2.setArrayOfItemDTO(Arrays.asList(printerItemDTO2));
        final PrinterAreaDTO printerAreaDTO2 = new PrinterAreaDTO();
        printerDTO2.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO2));
        printerDTO2.setDeviceNo("deviceNo");
        printerDTO2.setDeviceKey("deviceKey");
        printerDTO2.setManufacturersType(0);
        printerDTO2.setDeviceState(0);
        final List<PrinterDTO> printerBackupsDOS = Arrays.asList(printerDTO2);
        when(mockPrinterBackupsService.backupsPrinter("storeGuid", "deviceId", printerBackupsDOS)).thenReturn(false);

        // Run the test
        final boolean result = printerServiceImplUnderTest.backupsPrinter("deviceId");

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testBackupsPrinter_PrinterBackupsServiceReturnsTrue() {
        // Setup
        // Configure PrinterMapstruct.fromPrinterDTO(...).
        final PrinterDO printerDO = new PrinterDO();
        printerDO.setId(0L);
        printerDO.setStoreGuid("storeGuid");
        printerDO.setDeviceId("0");
        printerDO.setPrinterGuid("printerGuid");
        printerDO.setPrinterName("printerName");
        printerDO.setBusinessType(0);
        printerDO.setPrinterType(0);
        printerDO.setPrinterIp("printerIp");
        printerDO.setPrintCount(0);
        printerDO.setPrintCut(0);
        printerDO.setIsMaster(false);
        final PrinterDTO printerDTO = new PrinterDTO();
        printerDTO.setStoreGuid("storeGuid");
        printerDTO.setStoreName("storeName");
        printerDTO.setDeviceId("deviceId");
        printerDTO.setPrinterGuid("printerGuid");
        printerDTO.setPrinterName("printerName");
        printerDTO.setBusinessType(0);
        printerDTO.setPrinterType(0);
        printerDTO.setPrinterIp("printerIp");
        printerDTO.setPrinterPort(0);
        printerDTO.setPrintPage("printPage");
        printerDTO.setPrintCut(0);
        printerDTO.setArrayOfInvoiceType(Arrays.asList(0));
        printerDTO.setArrayOfItemGuid(Arrays.asList("value"));
        printerDTO.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterInvoiceDTO printerInvoiceDTO = new PrinterInvoiceDTO();
        printerInvoiceDTO.setInvoiceType(0);
        printerInvoiceDTO.setInvoiceName("invoiceName");
        printerInvoiceDTO.setPrintCount(0);
        printerDTO.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO));
        final PrinterItemDTO printerItemDTO = new PrinterItemDTO();
        printerDTO.setArrayOfItemDTO(Arrays.asList(printerItemDTO));
        final PrinterAreaDTO printerAreaDTO = new PrinterAreaDTO();
        printerDTO.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO));
        printerDTO.setDeviceNo("deviceNo");
        printerDTO.setDeviceKey("deviceKey");
        printerDTO.setManufacturersType(0);
        printerDTO.setDeviceState(0);
        when(mockPrinterMapstruct.fromPrinterDTO(printerDTO)).thenReturn(printerDO);

        // Configure PrinterMapstruct.toPrinterDTO(...).
        final PrinterDTO printerDTO1 = new PrinterDTO();
        printerDTO1.setStoreGuid("storeGuid");
        printerDTO1.setStoreName("storeName");
        printerDTO1.setDeviceId("deviceId");
        printerDTO1.setPrinterGuid("printerGuid");
        printerDTO1.setPrinterName("printerName");
        printerDTO1.setBusinessType(0);
        printerDTO1.setPrinterType(0);
        printerDTO1.setPrinterIp("printerIp");
        printerDTO1.setPrinterPort(0);
        printerDTO1.setPrintPage("printPage");
        printerDTO1.setPrintCut(0);
        printerDTO1.setArrayOfInvoiceType(Arrays.asList(0));
        printerDTO1.setArrayOfItemGuid(Arrays.asList("value"));
        printerDTO1.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterInvoiceDTO printerInvoiceDTO1 = new PrinterInvoiceDTO();
        printerInvoiceDTO1.setInvoiceType(0);
        printerInvoiceDTO1.setInvoiceName("invoiceName");
        printerInvoiceDTO1.setPrintCount(0);
        printerDTO1.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO1));
        final PrinterItemDTO printerItemDTO1 = new PrinterItemDTO();
        printerDTO1.setArrayOfItemDTO(Arrays.asList(printerItemDTO1));
        final PrinterAreaDTO printerAreaDTO1 = new PrinterAreaDTO();
        printerDTO1.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO1));
        printerDTO1.setDeviceNo("deviceNo");
        printerDTO1.setDeviceKey("deviceKey");
        printerDTO1.setManufacturersType(0);
        printerDTO1.setDeviceState(0);
        final PrinterReadDO printerReadDO = new PrinterReadDO();
        printerReadDO.setId(0L);
        printerReadDO.setPrinterGuid("printerGuid");
        printerReadDO.setPrinterName("printerName");
        printerReadDO.setBusinessType(0);
        printerReadDO.setPrinterType(0);
        printerReadDO.setPrinterPort(0);
        printerReadDO.setDeviceNo("deviceNo");
        when(mockPrinterMapstruct.toPrinterDTO(printerReadDO)).thenReturn(printerDTO1);

        // Configure PrinterBackupsService.backupsPrinter(...).
        final PrinterDTO printerDTO2 = new PrinterDTO();
        printerDTO2.setStoreGuid("storeGuid");
        printerDTO2.setStoreName("storeName");
        printerDTO2.setDeviceId("deviceId");
        printerDTO2.setPrinterGuid("printerGuid");
        printerDTO2.setPrinterName("printerName");
        printerDTO2.setBusinessType(0);
        printerDTO2.setPrinterType(0);
        printerDTO2.setPrinterIp("printerIp");
        printerDTO2.setPrinterPort(0);
        printerDTO2.setPrintPage("printPage");
        printerDTO2.setPrintCut(0);
        printerDTO2.setArrayOfInvoiceType(Arrays.asList(0));
        printerDTO2.setArrayOfItemGuid(Arrays.asList("value"));
        printerDTO2.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterInvoiceDTO printerInvoiceDTO2 = new PrinterInvoiceDTO();
        printerInvoiceDTO2.setInvoiceType(0);
        printerInvoiceDTO2.setInvoiceName("invoiceName");
        printerInvoiceDTO2.setPrintCount(0);
        printerDTO2.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO2));
        final PrinterItemDTO printerItemDTO2 = new PrinterItemDTO();
        printerDTO2.setArrayOfItemDTO(Arrays.asList(printerItemDTO2));
        final PrinterAreaDTO printerAreaDTO2 = new PrinterAreaDTO();
        printerDTO2.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO2));
        printerDTO2.setDeviceNo("deviceNo");
        printerDTO2.setDeviceKey("deviceKey");
        printerDTO2.setManufacturersType(0);
        printerDTO2.setDeviceState(0);
        final List<PrinterDTO> printerBackupsDOS = Arrays.asList(printerDTO2);
        when(mockPrinterBackupsService.backupsPrinter("storeGuid", "deviceId", printerBackupsDOS)).thenReturn(true);

        // Run the test
        final boolean result = printerServiceImplUnderTest.backupsPrinter("deviceId");

        // Verify the results
        assertThat(result).isTrue();
    }

    @Test
    public void testBackupsPrinterTime() {
        // Setup
        // Configure PrinterBackupsService.list(...).
        final PrinterBackupsDO printerBackupsDO = new PrinterBackupsDO();
        printerBackupsDO.setId(0L);
        printerBackupsDO.setStoreGuid("storeGuid");
        printerBackupsDO.setDeviceId("deviceId");
        printerBackupsDO.setPrintListJson("printListJson");
        printerBackupsDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<PrinterBackupsDO> printerBackupsDOS = Arrays.asList(printerBackupsDO);
        when(mockPrinterBackupsService.list(any(LambdaQueryWrapper.class))).thenReturn(printerBackupsDOS);

        // Run the test
        final String result = printerServiceImplUnderTest.backupsPrinterTime("deviceId");

        // Verify the results
        assertThat(result).isEqualTo("result");
    }

    @Test
    public void testBackupsPrinterTime_PrinterBackupsServiceReturnsNull() {
        // Setup
        when(mockPrinterBackupsService.list(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Run the test
        final String result = printerServiceImplUnderTest.backupsPrinterTime("deviceId");

        // Verify the results
        assertThat(result).isNull();
    }

    @Test
    public void testBackupsPrinterTime_PrinterBackupsServiceReturnsNoItems() {
        // Setup
        when(mockPrinterBackupsService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final String result = printerServiceImplUnderTest.backupsPrinterTime("deviceId");

        // Verify the results
        assertThat(result).isNull();
    }

    @Test
    public void testDeletePrinterList() {
        // Setup
        final PrinterDTO printerDTO = new PrinterDTO();
        printerDTO.setStoreGuid("storeGuid");
        printerDTO.setStoreName("storeName");
        printerDTO.setDeviceId("deviceId");
        printerDTO.setPrinterGuid("printerGuid");
        printerDTO.setPrinterName("printerName");
        printerDTO.setBusinessType(0);
        printerDTO.setPrinterType(0);
        printerDTO.setPrinterIp("printerIp");
        printerDTO.setPrinterPort(0);
        printerDTO.setPrintPage("printPage");
        printerDTO.setPrintCut(0);
        printerDTO.setArrayOfInvoiceType(Arrays.asList(0));
        printerDTO.setArrayOfItemGuid(Arrays.asList("value"));
        printerDTO.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterInvoiceDTO printerInvoiceDTO = new PrinterInvoiceDTO();
        printerInvoiceDTO.setInvoiceType(0);
        printerInvoiceDTO.setInvoiceName("invoiceName");
        printerInvoiceDTO.setPrintCount(0);
        printerDTO.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO));
        final PrinterItemDTO printerItemDTO = new PrinterItemDTO();
        printerDTO.setArrayOfItemDTO(Arrays.asList(printerItemDTO));
        final PrinterAreaDTO printerAreaDTO = new PrinterAreaDTO();
        printerDTO.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO));
        printerDTO.setDeviceNo("deviceNo");
        printerDTO.setDeviceKey("deviceKey");
        printerDTO.setManufacturersType(0);
        printerDTO.setDeviceState(0);

        // Run the test
        printerServiceImplUnderTest.deletePrinterList(printerDTO);

        // Verify the results
    }

    @Test
    public void testQueryByCondition() {
        // Setup
        final PrinterQueryDTO queryDTO = new PrinterQueryDTO("storeGuid", 0, "deviceId", 0);
        final PrinterDTO printerDTO = new PrinterDTO();
        printerDTO.setStoreGuid("storeGuid");
        printerDTO.setStoreName("storeName");
        printerDTO.setDeviceId("deviceId");
        printerDTO.setPrinterGuid("printerGuid");
        printerDTO.setPrinterName("printerName");
        printerDTO.setBusinessType(0);
        printerDTO.setPrinterType(0);
        printerDTO.setPrinterIp("printerIp");
        printerDTO.setPrinterPort(0);
        printerDTO.setPrintPage("printPage");
        printerDTO.setPrintCut(0);
        printerDTO.setArrayOfInvoiceType(Arrays.asList(0));
        printerDTO.setArrayOfItemGuid(Arrays.asList("value"));
        printerDTO.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterInvoiceDTO printerInvoiceDTO = new PrinterInvoiceDTO();
        printerInvoiceDTO.setInvoiceType(0);
        printerInvoiceDTO.setInvoiceName("invoiceName");
        printerInvoiceDTO.setPrintCount(0);
        printerDTO.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO));
        final PrinterItemDTO printerItemDTO = new PrinterItemDTO();
        printerDTO.setArrayOfItemDTO(Arrays.asList(printerItemDTO));
        final PrinterAreaDTO printerAreaDTO = new PrinterAreaDTO();
        printerDTO.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO));
        printerDTO.setDeviceNo("deviceNo");
        printerDTO.setDeviceKey("deviceKey");
        printerDTO.setManufacturersType(0);
        printerDTO.setDeviceState(0);
        final List<PrinterDTO> expectedResult = Arrays.asList(printerDTO);

        // Run the test
        final List<PrinterDTO> result = printerServiceImplUnderTest.queryByCondition(queryDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testAddCloud() {
        // Setup
        final CloudPrinterDTO cloudPrinterDTO = new CloudPrinterDTO();
        cloudPrinterDTO.setStoreGuid("storeGuid");
        cloudPrinterDTO.setStoreName("storeName");
        cloudPrinterDTO.setDeviceId("deviceId");
        cloudPrinterDTO.setPrinterGuid("printerGuid");
        cloudPrinterDTO.setPrinterName("printerName");
        cloudPrinterDTO.setBusinessType(0);
        cloudPrinterDTO.setPrinterType(0);
        cloudPrinterDTO.setPrinterIp("printerIp");
        cloudPrinterDTO.setPrinterPort(0);
        cloudPrinterDTO.setPrintPage("printPage");
        cloudPrinterDTO.setPrintCut(0);
        cloudPrinterDTO.setArrayOfInvoiceType(Arrays.asList(0));
        cloudPrinterDTO.setArrayOfItemGuid(Arrays.asList("value"));
        cloudPrinterDTO.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterInvoiceDTO printerInvoiceDTO = new PrinterInvoiceDTO();
        printerInvoiceDTO.setInvoiceType(0);
        printerInvoiceDTO.setInvoiceName("invoiceName");
        printerInvoiceDTO.setPrintCount(0);
        cloudPrinterDTO.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO));
        final PrinterItemDTO printerItemDTO = new PrinterItemDTO();
        cloudPrinterDTO.setArrayOfItemDTO(Arrays.asList(printerItemDTO));
        final PrinterAreaDTO printerAreaDTO = new PrinterAreaDTO();
        cloudPrinterDTO.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO));
        cloudPrinterDTO.setDeviceNo("deviceNo");
        cloudPrinterDTO.setDeviceKey("deviceKey");
        cloudPrinterDTO.setManufacturersType(0);
        cloudPrinterDTO.setDeviceState(0);
        final DeviceUseDTO deviceUseDTO = new DeviceUseDTO();
        deviceUseDTO.setInvoiceType(0);
        deviceUseDTO.setPrintCount(0);
        final PrinterItemDTO printerItemDTO1 = new PrinterItemDTO();
        deviceUseDTO.setArrayOfItemDTO(Arrays.asList(printerItemDTO1));
        cloudPrinterDTO.setDeviceUse(Arrays.asList(deviceUseDTO));

        when(mockDistributedService.nextPrinterGuid()).thenReturn("printerGuid");

        // Configure PrinterMapstruct.fromPrinterDTO(...).
        final PrinterDO printerDO = new PrinterDO();
        printerDO.setId(0L);
        printerDO.setStoreGuid("storeGuid");
        printerDO.setDeviceId("0");
        printerDO.setPrinterGuid("printerGuid");
        printerDO.setPrinterName("printerName");
        printerDO.setBusinessType(0);
        printerDO.setPrinterType(0);
        printerDO.setPrinterIp("printerIp");
        printerDO.setPrintCount(0);
        printerDO.setPrintCut(0);
        printerDO.setIsMaster(false);
        final PrinterDTO printerDTO = new PrinterDTO();
        printerDTO.setStoreGuid("storeGuid");
        printerDTO.setStoreName("storeName");
        printerDTO.setDeviceId("deviceId");
        printerDTO.setPrinterGuid("printerGuid");
        printerDTO.setPrinterName("printerName");
        printerDTO.setBusinessType(0);
        printerDTO.setPrinterType(0);
        printerDTO.setPrinterIp("printerIp");
        printerDTO.setPrinterPort(0);
        printerDTO.setPrintPage("printPage");
        printerDTO.setPrintCut(0);
        printerDTO.setArrayOfInvoiceType(Arrays.asList(0));
        printerDTO.setArrayOfItemGuid(Arrays.asList("value"));
        printerDTO.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterInvoiceDTO printerInvoiceDTO1 = new PrinterInvoiceDTO();
        printerInvoiceDTO1.setInvoiceType(0);
        printerInvoiceDTO1.setInvoiceName("invoiceName");
        printerInvoiceDTO1.setPrintCount(0);
        printerDTO.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO1));
        final PrinterItemDTO printerItemDTO2 = new PrinterItemDTO();
        printerDTO.setArrayOfItemDTO(Arrays.asList(printerItemDTO2));
        final PrinterAreaDTO printerAreaDTO1 = new PrinterAreaDTO();
        printerDTO.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO1));
        printerDTO.setDeviceNo("deviceNo");
        printerDTO.setDeviceKey("deviceKey");
        printerDTO.setManufacturersType(0);
        printerDTO.setDeviceState(0);
        when(mockPrinterMapstruct.fromPrinterDTO(printerDTO)).thenReturn(printerDO);

        // Run the test
        printerServiceImplUnderTest.addCloud(cloudPrinterDTO);

        // Verify the results
        verify(mockCloudPrinterService).addPrinter("deviceNo", "deviceKey", "printerName");

        // Confirm PrinterInvoiceService.bindPrinterInvoice(...).
        final PrinterDTO printerDTO1 = new PrinterDTO();
        printerDTO1.setStoreGuid("storeGuid");
        printerDTO1.setStoreName("storeName");
        printerDTO1.setDeviceId("deviceId");
        printerDTO1.setPrinterGuid("printerGuid");
        printerDTO1.setPrinterName("printerName");
        printerDTO1.setBusinessType(0);
        printerDTO1.setPrinterType(0);
        printerDTO1.setPrinterIp("printerIp");
        printerDTO1.setPrinterPort(0);
        printerDTO1.setPrintPage("printPage");
        printerDTO1.setPrintCut(0);
        printerDTO1.setArrayOfInvoiceType(Arrays.asList(0));
        printerDTO1.setArrayOfItemGuid(Arrays.asList("value"));
        printerDTO1.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterInvoiceDTO printerInvoiceDTO2 = new PrinterInvoiceDTO();
        printerInvoiceDTO2.setInvoiceType(0);
        printerInvoiceDTO2.setInvoiceName("invoiceName");
        printerInvoiceDTO2.setPrintCount(0);
        printerDTO1.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO2));
        final PrinterItemDTO printerItemDTO3 = new PrinterItemDTO();
        printerDTO1.setArrayOfItemDTO(Arrays.asList(printerItemDTO3));
        final PrinterAreaDTO printerAreaDTO2 = new PrinterAreaDTO();
        printerDTO1.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO2));
        printerDTO1.setDeviceNo("deviceNo");
        printerDTO1.setDeviceKey("deviceKey");
        printerDTO1.setManufacturersType(0);
        printerDTO1.setDeviceState(0);
        verify(mockPrinterInvoiceService).bindPrinterInvoice(printerDTO1);

        // Confirm PrinterItemService.bindPrinterItem(...).
        final PrinterDTO printerDTO2 = new PrinterDTO();
        printerDTO2.setStoreGuid("storeGuid");
        printerDTO2.setStoreName("storeName");
        printerDTO2.setDeviceId("deviceId");
        printerDTO2.setPrinterGuid("printerGuid");
        printerDTO2.setPrinterName("printerName");
        printerDTO2.setBusinessType(0);
        printerDTO2.setPrinterType(0);
        printerDTO2.setPrinterIp("printerIp");
        printerDTO2.setPrinterPort(0);
        printerDTO2.setPrintPage("printPage");
        printerDTO2.setPrintCut(0);
        printerDTO2.setArrayOfInvoiceType(Arrays.asList(0));
        printerDTO2.setArrayOfItemGuid(Arrays.asList("value"));
        printerDTO2.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterInvoiceDTO printerInvoiceDTO3 = new PrinterInvoiceDTO();
        printerInvoiceDTO3.setInvoiceType(0);
        printerInvoiceDTO3.setInvoiceName("invoiceName");
        printerInvoiceDTO3.setPrintCount(0);
        printerDTO2.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO3));
        final PrinterItemDTO printerItemDTO4 = new PrinterItemDTO();
        printerDTO2.setArrayOfItemDTO(Arrays.asList(printerItemDTO4));
        final PrinterAreaDTO printerAreaDTO3 = new PrinterAreaDTO();
        printerDTO2.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO3));
        printerDTO2.setDeviceNo("deviceNo");
        printerDTO2.setDeviceKey("deviceKey");
        printerDTO2.setManufacturersType(0);
        printerDTO2.setDeviceState(0);
        verify(mockPrinterItemService).bindPrinterItem(printerDTO2);

        // Confirm PrinterLogService.publishAddLog(...).
        final PrinterDTO printerDTO3 = new PrinterDTO();
        printerDTO3.setStoreGuid("storeGuid");
        printerDTO3.setStoreName("storeName");
        printerDTO3.setDeviceId("deviceId");
        printerDTO3.setPrinterGuid("printerGuid");
        printerDTO3.setPrinterName("printerName");
        printerDTO3.setBusinessType(0);
        printerDTO3.setPrinterType(0);
        printerDTO3.setPrinterIp("printerIp");
        printerDTO3.setPrinterPort(0);
        printerDTO3.setPrintPage("printPage");
        printerDTO3.setPrintCut(0);
        printerDTO3.setArrayOfInvoiceType(Arrays.asList(0));
        printerDTO3.setArrayOfItemGuid(Arrays.asList("value"));
        printerDTO3.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterInvoiceDTO printerInvoiceDTO4 = new PrinterInvoiceDTO();
        printerInvoiceDTO4.setInvoiceType(0);
        printerInvoiceDTO4.setInvoiceName("invoiceName");
        printerInvoiceDTO4.setPrintCount(0);
        printerDTO3.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO4));
        final PrinterItemDTO printerItemDTO5 = new PrinterItemDTO();
        printerDTO3.setArrayOfItemDTO(Arrays.asList(printerItemDTO5));
        final PrinterAreaDTO printerAreaDTO4 = new PrinterAreaDTO();
        printerDTO3.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO4));
        printerDTO3.setDeviceNo("deviceNo");
        printerDTO3.setDeviceKey("deviceKey");
        printerDTO3.setManufacturersType(0);
        printerDTO3.setDeviceState(0);
        verify(mockPrinterLogService).publishAddLog(any(Object.class), eq(printerDTO3));
    }

    @Test
    public void testTestPrint() {
        // Setup
        final CloudPrinterDTO cloudPrinterDTO = new CloudPrinterDTO();
        cloudPrinterDTO.setStoreGuid("storeGuid");
        cloudPrinterDTO.setStoreName("storeName");
        cloudPrinterDTO.setDeviceId("deviceId");
        cloudPrinterDTO.setPrinterGuid("printerGuid");
        cloudPrinterDTO.setPrinterName("printerName");
        cloudPrinterDTO.setBusinessType(0);
        cloudPrinterDTO.setPrinterType(0);
        cloudPrinterDTO.setPrinterIp("printerIp");
        cloudPrinterDTO.setPrinterPort(0);
        cloudPrinterDTO.setPrintPage("printPage");
        cloudPrinterDTO.setPrintCut(0);
        cloudPrinterDTO.setArrayOfInvoiceType(Arrays.asList(0));
        cloudPrinterDTO.setArrayOfItemGuid(Arrays.asList("value"));
        cloudPrinterDTO.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterInvoiceDTO printerInvoiceDTO = new PrinterInvoiceDTO();
        printerInvoiceDTO.setInvoiceType(0);
        printerInvoiceDTO.setInvoiceName("invoiceName");
        printerInvoiceDTO.setPrintCount(0);
        cloudPrinterDTO.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO));
        final PrinterItemDTO printerItemDTO = new PrinterItemDTO();
        cloudPrinterDTO.setArrayOfItemDTO(Arrays.asList(printerItemDTO));
        final PrinterAreaDTO printerAreaDTO = new PrinterAreaDTO();
        cloudPrinterDTO.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO));
        cloudPrinterDTO.setDeviceNo("deviceNo");
        cloudPrinterDTO.setDeviceKey("deviceKey");
        cloudPrinterDTO.setManufacturersType(0);
        cloudPrinterDTO.setDeviceState(0);
        final DeviceUseDTO deviceUseDTO = new DeviceUseDTO();
        deviceUseDTO.setInvoiceType(0);
        deviceUseDTO.setPrintCount(0);
        final PrinterItemDTO printerItemDTO1 = new PrinterItemDTO();
        deviceUseDTO.setArrayOfItemDTO(Arrays.asList(printerItemDTO1));
        cloudPrinterDTO.setDeviceUse(Arrays.asList(deviceUseDTO));

        // Configure PrinterMapstruct.fromPrinterDTO(...).
        final PrinterDO printerDO = new PrinterDO();
        printerDO.setId(0L);
        printerDO.setStoreGuid("storeGuid");
        printerDO.setDeviceId("0");
        printerDO.setPrinterGuid("printerGuid");
        printerDO.setPrinterName("printerName");
        printerDO.setBusinessType(0);
        printerDO.setPrinterType(0);
        printerDO.setPrinterIp("printerIp");
        printerDO.setPrintCount(0);
        printerDO.setPrintCut(0);
        printerDO.setIsMaster(false);
        final PrinterDTO printerDTO = new PrinterDTO();
        printerDTO.setStoreGuid("storeGuid");
        printerDTO.setStoreName("storeName");
        printerDTO.setDeviceId("deviceId");
        printerDTO.setPrinterGuid("printerGuid");
        printerDTO.setPrinterName("printerName");
        printerDTO.setBusinessType(0);
        printerDTO.setPrinterType(0);
        printerDTO.setPrinterIp("printerIp");
        printerDTO.setPrinterPort(0);
        printerDTO.setPrintPage("printPage");
        printerDTO.setPrintCut(0);
        printerDTO.setArrayOfInvoiceType(Arrays.asList(0));
        printerDTO.setArrayOfItemGuid(Arrays.asList("value"));
        printerDTO.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterInvoiceDTO printerInvoiceDTO1 = new PrinterInvoiceDTO();
        printerInvoiceDTO1.setInvoiceType(0);
        printerInvoiceDTO1.setInvoiceName("invoiceName");
        printerInvoiceDTO1.setPrintCount(0);
        printerDTO.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO1));
        final PrinterItemDTO printerItemDTO2 = new PrinterItemDTO();
        printerDTO.setArrayOfItemDTO(Arrays.asList(printerItemDTO2));
        final PrinterAreaDTO printerAreaDTO1 = new PrinterAreaDTO();
        printerDTO.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO1));
        printerDTO.setDeviceNo("deviceNo");
        printerDTO.setDeviceKey("deviceKey");
        printerDTO.setManufacturersType(0);
        printerDTO.setDeviceState(0);
        when(mockPrinterMapstruct.fromPrinterDTO(printerDTO)).thenReturn(printerDO);

        // Configure CloudPrinterService.queryPrinterInfo(...).
        final FeieRespDTO feieRespDTO = new FeieRespDTO();
        feieRespDTO.setMsg("msg");
        feieRespDTO.setRet(0);
        final FeieRespDTO.DataMsg data = new FeieRespDTO.DataMsg();
        data.setOk(Arrays.asList("value"));
        data.setStatus(0);
        feieRespDTO.setData(data);
        when(mockCloudPrinterService.queryPrinterInfo("deviceNo")).thenReturn(feieRespDTO);

        // Run the test
        printerServiceImplUnderTest.testPrint(cloudPrinterDTO);

        // Verify the results
        verify(mockCloudPrinterService).testPrinter("deviceNo", 1);
    }

    @Test
    public void testUpdateCloudPrinter() {
        // Setup
        final CloudPrinterDTO cloudPrinterDTO = new CloudPrinterDTO();
        cloudPrinterDTO.setStoreGuid("storeGuid");
        cloudPrinterDTO.setStoreName("storeName");
        cloudPrinterDTO.setDeviceId("deviceId");
        cloudPrinterDTO.setPrinterGuid("printerGuid");
        cloudPrinterDTO.setPrinterName("printerName");
        cloudPrinterDTO.setBusinessType(0);
        cloudPrinterDTO.setPrinterType(0);
        cloudPrinterDTO.setPrinterIp("printerIp");
        cloudPrinterDTO.setPrinterPort(0);
        cloudPrinterDTO.setPrintPage("printPage");
        cloudPrinterDTO.setPrintCut(0);
        cloudPrinterDTO.setArrayOfInvoiceType(Arrays.asList(0));
        cloudPrinterDTO.setArrayOfItemGuid(Arrays.asList("value"));
        cloudPrinterDTO.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterInvoiceDTO printerInvoiceDTO = new PrinterInvoiceDTO();
        printerInvoiceDTO.setInvoiceType(0);
        printerInvoiceDTO.setInvoiceName("invoiceName");
        printerInvoiceDTO.setPrintCount(0);
        cloudPrinterDTO.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO));
        final PrinterItemDTO printerItemDTO = new PrinterItemDTO();
        cloudPrinterDTO.setArrayOfItemDTO(Arrays.asList(printerItemDTO));
        final PrinterAreaDTO printerAreaDTO = new PrinterAreaDTO();
        cloudPrinterDTO.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO));
        cloudPrinterDTO.setDeviceNo("deviceNo");
        cloudPrinterDTO.setDeviceKey("deviceKey");
        cloudPrinterDTO.setManufacturersType(0);
        cloudPrinterDTO.setDeviceState(0);
        final DeviceUseDTO deviceUseDTO = new DeviceUseDTO();
        deviceUseDTO.setInvoiceType(0);
        deviceUseDTO.setPrintCount(0);
        final PrinterItemDTO printerItemDTO1 = new PrinterItemDTO();
        deviceUseDTO.setArrayOfItemDTO(Arrays.asList(printerItemDTO1));
        cloudPrinterDTO.setDeviceUse(Arrays.asList(deviceUseDTO));

        // Configure PrinterMapstruct.toPrinterDTO(...).
        final PrinterDTO printerDTO = new PrinterDTO();
        printerDTO.setStoreGuid("storeGuid");
        printerDTO.setStoreName("storeName");
        printerDTO.setDeviceId("deviceId");
        printerDTO.setPrinterGuid("printerGuid");
        printerDTO.setPrinterName("printerName");
        printerDTO.setBusinessType(0);
        printerDTO.setPrinterType(0);
        printerDTO.setPrinterIp("printerIp");
        printerDTO.setPrinterPort(0);
        printerDTO.setPrintPage("printPage");
        printerDTO.setPrintCut(0);
        printerDTO.setArrayOfInvoiceType(Arrays.asList(0));
        printerDTO.setArrayOfItemGuid(Arrays.asList("value"));
        printerDTO.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterInvoiceDTO printerInvoiceDTO1 = new PrinterInvoiceDTO();
        printerInvoiceDTO1.setInvoiceType(0);
        printerInvoiceDTO1.setInvoiceName("invoiceName");
        printerInvoiceDTO1.setPrintCount(0);
        printerDTO.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO1));
        final PrinterItemDTO printerItemDTO2 = new PrinterItemDTO();
        printerDTO.setArrayOfItemDTO(Arrays.asList(printerItemDTO2));
        final PrinterAreaDTO printerAreaDTO1 = new PrinterAreaDTO();
        printerDTO.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO1));
        printerDTO.setDeviceNo("deviceNo");
        printerDTO.setDeviceKey("deviceKey");
        printerDTO.setManufacturersType(0);
        printerDTO.setDeviceState(0);
        final PrinterReadDO printerReadDO = new PrinterReadDO();
        printerReadDO.setId(0L);
        printerReadDO.setPrinterGuid("printerGuid");
        printerReadDO.setPrinterName("printerName");
        printerReadDO.setBusinessType(0);
        printerReadDO.setPrinterType(0);
        printerReadDO.setPrinterPort(0);
        printerReadDO.setDeviceNo("deviceNo");
        when(mockPrinterMapstruct.toPrinterDTO(printerReadDO)).thenReturn(printerDTO);

        // Configure PrinterMapstruct.fromPrinterDTO(...).
        final PrinterDO printerDO = new PrinterDO();
        printerDO.setId(0L);
        printerDO.setStoreGuid("storeGuid");
        printerDO.setDeviceId("0");
        printerDO.setPrinterGuid("printerGuid");
        printerDO.setPrinterName("printerName");
        printerDO.setBusinessType(0);
        printerDO.setPrinterType(0);
        printerDO.setPrinterIp("printerIp");
        printerDO.setPrintCount(0);
        printerDO.setPrintCut(0);
        printerDO.setIsMaster(false);
        final PrinterDTO printerDTO1 = new PrinterDTO();
        printerDTO1.setStoreGuid("storeGuid");
        printerDTO1.setStoreName("storeName");
        printerDTO1.setDeviceId("deviceId");
        printerDTO1.setPrinterGuid("printerGuid");
        printerDTO1.setPrinterName("printerName");
        printerDTO1.setBusinessType(0);
        printerDTO1.setPrinterType(0);
        printerDTO1.setPrinterIp("printerIp");
        printerDTO1.setPrinterPort(0);
        printerDTO1.setPrintPage("printPage");
        printerDTO1.setPrintCut(0);
        printerDTO1.setArrayOfInvoiceType(Arrays.asList(0));
        printerDTO1.setArrayOfItemGuid(Arrays.asList("value"));
        printerDTO1.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterInvoiceDTO printerInvoiceDTO2 = new PrinterInvoiceDTO();
        printerInvoiceDTO2.setInvoiceType(0);
        printerInvoiceDTO2.setInvoiceName("invoiceName");
        printerInvoiceDTO2.setPrintCount(0);
        printerDTO1.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO2));
        final PrinterItemDTO printerItemDTO3 = new PrinterItemDTO();
        printerDTO1.setArrayOfItemDTO(Arrays.asList(printerItemDTO3));
        final PrinterAreaDTO printerAreaDTO2 = new PrinterAreaDTO();
        printerDTO1.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO2));
        printerDTO1.setDeviceNo("deviceNo");
        printerDTO1.setDeviceKey("deviceKey");
        printerDTO1.setManufacturersType(0);
        printerDTO1.setDeviceState(0);
        when(mockPrinterMapstruct.fromPrinterDTO(printerDTO1)).thenReturn(printerDO);

        // Run the test
        printerServiceImplUnderTest.updateCloudPrinter(cloudPrinterDTO);

        // Verify the results
        verify(mockPrinterInvoiceService).deletePrinterInvoice("printerGuid");
        verify(mockPrinterItemService).deletePrinterItem("printerGuid");

        // Confirm PrinterInvoiceService.bindPrinterInvoice(...).
        final PrinterDTO printerDTO2 = new PrinterDTO();
        printerDTO2.setStoreGuid("storeGuid");
        printerDTO2.setStoreName("storeName");
        printerDTO2.setDeviceId("deviceId");
        printerDTO2.setPrinterGuid("printerGuid");
        printerDTO2.setPrinterName("printerName");
        printerDTO2.setBusinessType(0);
        printerDTO2.setPrinterType(0);
        printerDTO2.setPrinterIp("printerIp");
        printerDTO2.setPrinterPort(0);
        printerDTO2.setPrintPage("printPage");
        printerDTO2.setPrintCut(0);
        printerDTO2.setArrayOfInvoiceType(Arrays.asList(0));
        printerDTO2.setArrayOfItemGuid(Arrays.asList("value"));
        printerDTO2.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterInvoiceDTO printerInvoiceDTO3 = new PrinterInvoiceDTO();
        printerInvoiceDTO3.setInvoiceType(0);
        printerInvoiceDTO3.setInvoiceName("invoiceName");
        printerInvoiceDTO3.setPrintCount(0);
        printerDTO2.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO3));
        final PrinterItemDTO printerItemDTO4 = new PrinterItemDTO();
        printerDTO2.setArrayOfItemDTO(Arrays.asList(printerItemDTO4));
        final PrinterAreaDTO printerAreaDTO3 = new PrinterAreaDTO();
        printerDTO2.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO3));
        printerDTO2.setDeviceNo("deviceNo");
        printerDTO2.setDeviceKey("deviceKey");
        printerDTO2.setManufacturersType(0);
        printerDTO2.setDeviceState(0);
        verify(mockPrinterInvoiceService).bindPrinterInvoice(printerDTO2);

        // Confirm PrinterItemService.bindPrinterItem(...).
        final PrinterDTO printerDTO3 = new PrinterDTO();
        printerDTO3.setStoreGuid("storeGuid");
        printerDTO3.setStoreName("storeName");
        printerDTO3.setDeviceId("deviceId");
        printerDTO3.setPrinterGuid("printerGuid");
        printerDTO3.setPrinterName("printerName");
        printerDTO3.setBusinessType(0);
        printerDTO3.setPrinterType(0);
        printerDTO3.setPrinterIp("printerIp");
        printerDTO3.setPrinterPort(0);
        printerDTO3.setPrintPage("printPage");
        printerDTO3.setPrintCut(0);
        printerDTO3.setArrayOfInvoiceType(Arrays.asList(0));
        printerDTO3.setArrayOfItemGuid(Arrays.asList("value"));
        printerDTO3.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterInvoiceDTO printerInvoiceDTO4 = new PrinterInvoiceDTO();
        printerInvoiceDTO4.setInvoiceType(0);
        printerInvoiceDTO4.setInvoiceName("invoiceName");
        printerInvoiceDTO4.setPrintCount(0);
        printerDTO3.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO4));
        final PrinterItemDTO printerItemDTO5 = new PrinterItemDTO();
        printerDTO3.setArrayOfItemDTO(Arrays.asList(printerItemDTO5));
        final PrinterAreaDTO printerAreaDTO4 = new PrinterAreaDTO();
        printerDTO3.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO4));
        printerDTO3.setDeviceNo("deviceNo");
        printerDTO3.setDeviceKey("deviceKey");
        printerDTO3.setManufacturersType(0);
        printerDTO3.setDeviceState(0);
        verify(mockPrinterItemService).bindPrinterItem(printerDTO3);

        // Confirm PrinterLogService.publishUpdateLog(...).
        final PrinterDTO printerDtoBefore = new PrinterDTO();
        printerDtoBefore.setStoreGuid("storeGuid");
        printerDtoBefore.setStoreName("storeName");
        printerDtoBefore.setDeviceId("deviceId");
        printerDtoBefore.setPrinterGuid("printerGuid");
        printerDtoBefore.setPrinterName("printerName");
        printerDtoBefore.setBusinessType(0);
        printerDtoBefore.setPrinterType(0);
        printerDtoBefore.setPrinterIp("printerIp");
        printerDtoBefore.setPrinterPort(0);
        printerDtoBefore.setPrintPage("printPage");
        printerDtoBefore.setPrintCut(0);
        printerDtoBefore.setArrayOfInvoiceType(Arrays.asList(0));
        printerDtoBefore.setArrayOfItemGuid(Arrays.asList("value"));
        printerDtoBefore.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterInvoiceDTO printerInvoiceDTO5 = new PrinterInvoiceDTO();
        printerInvoiceDTO5.setInvoiceType(0);
        printerInvoiceDTO5.setInvoiceName("invoiceName");
        printerInvoiceDTO5.setPrintCount(0);
        printerDtoBefore.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO5));
        final PrinterItemDTO printerItemDTO6 = new PrinterItemDTO();
        printerDtoBefore.setArrayOfItemDTO(Arrays.asList(printerItemDTO6));
        final PrinterAreaDTO printerAreaDTO5 = new PrinterAreaDTO();
        printerDtoBefore.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO5));
        printerDtoBefore.setDeviceNo("deviceNo");
        printerDtoBefore.setDeviceKey("deviceKey");
        printerDtoBefore.setManufacturersType(0);
        printerDtoBefore.setDeviceState(0);
        final PrinterDTO printerDtoAfter = new PrinterDTO();
        printerDtoAfter.setStoreGuid("storeGuid");
        printerDtoAfter.setStoreName("storeName");
        printerDtoAfter.setDeviceId("deviceId");
        printerDtoAfter.setPrinterGuid("printerGuid");
        printerDtoAfter.setPrinterName("printerName");
        printerDtoAfter.setBusinessType(0);
        printerDtoAfter.setPrinterType(0);
        printerDtoAfter.setPrinterIp("printerIp");
        printerDtoAfter.setPrinterPort(0);
        printerDtoAfter.setPrintPage("printPage");
        printerDtoAfter.setPrintCut(0);
        printerDtoAfter.setArrayOfInvoiceType(Arrays.asList(0));
        printerDtoAfter.setArrayOfItemGuid(Arrays.asList("value"));
        printerDtoAfter.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterInvoiceDTO printerInvoiceDTO6 = new PrinterInvoiceDTO();
        printerInvoiceDTO6.setInvoiceType(0);
        printerInvoiceDTO6.setInvoiceName("invoiceName");
        printerInvoiceDTO6.setPrintCount(0);
        printerDtoAfter.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO6));
        final PrinterItemDTO printerItemDTO7 = new PrinterItemDTO();
        printerDtoAfter.setArrayOfItemDTO(Arrays.asList(printerItemDTO7));
        final PrinterAreaDTO printerAreaDTO6 = new PrinterAreaDTO();
        printerDtoAfter.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO6));
        printerDtoAfter.setDeviceNo("deviceNo");
        printerDtoAfter.setDeviceKey("deviceKey");
        printerDtoAfter.setManufacturersType(0);
        printerDtoAfter.setDeviceState(0);
        verify(mockPrinterLogService).publishUpdateLog(any(Object.class), eq(printerDtoBefore), eq(printerDtoAfter));
    }

    @Test
    public void testCheckPrinter() {
        // Setup
        final CloudPrinterDTO cloudPrinterDTO = new CloudPrinterDTO();
        cloudPrinterDTO.setStoreGuid("storeGuid");
        cloudPrinterDTO.setStoreName("storeName");
        cloudPrinterDTO.setDeviceId("deviceId");
        cloudPrinterDTO.setPrinterGuid("printerGuid");
        cloudPrinterDTO.setPrinterName("printerName");
        cloudPrinterDTO.setBusinessType(0);
        cloudPrinterDTO.setPrinterType(0);
        cloudPrinterDTO.setPrinterIp("printerIp");
        cloudPrinterDTO.setPrinterPort(0);
        cloudPrinterDTO.setPrintPage("printPage");
        cloudPrinterDTO.setPrintCut(0);
        cloudPrinterDTO.setArrayOfInvoiceType(Arrays.asList(0));
        cloudPrinterDTO.setArrayOfItemGuid(Arrays.asList("value"));
        cloudPrinterDTO.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterInvoiceDTO printerInvoiceDTO = new PrinterInvoiceDTO();
        printerInvoiceDTO.setInvoiceType(0);
        printerInvoiceDTO.setInvoiceName("invoiceName");
        printerInvoiceDTO.setPrintCount(0);
        cloudPrinterDTO.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO));
        final PrinterItemDTO printerItemDTO = new PrinterItemDTO();
        cloudPrinterDTO.setArrayOfItemDTO(Arrays.asList(printerItemDTO));
        final PrinterAreaDTO printerAreaDTO = new PrinterAreaDTO();
        cloudPrinterDTO.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO));
        cloudPrinterDTO.setDeviceNo("deviceNo");
        cloudPrinterDTO.setDeviceKey("deviceKey");
        cloudPrinterDTO.setManufacturersType(0);
        cloudPrinterDTO.setDeviceState(0);
        final DeviceUseDTO deviceUseDTO = new DeviceUseDTO();
        deviceUseDTO.setInvoiceType(0);
        deviceUseDTO.setPrintCount(0);
        final PrinterItemDTO printerItemDTO1 = new PrinterItemDTO();
        deviceUseDTO.setArrayOfItemDTO(Arrays.asList(printerItemDTO1));
        cloudPrinterDTO.setDeviceUse(Arrays.asList(deviceUseDTO));

        // Run the test
        printerServiceImplUnderTest.checkPrinter(cloudPrinterDTO);

        // Verify the results
        verify(mockCloudPrinterService).addPrinter("deviceNo", "deviceKey", "printerName");
        verify(mockCloudPrinterService).deletePrinter("deviceNo");
    }

    @Test
    public void testFindCloudPrinterByQuery() {
        // Setup
        final PrinterQuery printerQuery = new PrinterQuery();
        printerQuery.setStoreGuid("storeGuid");
        printerQuery.setInvoiceType(0);
        printerQuery.setDeviceId("deviceId");
        printerQuery.setAreaGuid("areaGuid");
        printerQuery.setBusinessType(0);

        final PrinterReadDO printerReadDO = new PrinterReadDO();
        printerReadDO.setId(0L);
        printerReadDO.setPrinterGuid("printerGuid");
        printerReadDO.setPrinterName("printerName");
        printerReadDO.setBusinessType(0);
        printerReadDO.setPrinterType(0);
        printerReadDO.setPrinterPort(0);
        printerReadDO.setDeviceNo("deviceNo");
        final List<PrinterReadDO> expectedResult = Arrays.asList(printerReadDO);

        // Run the test
        final List<PrinterReadDO> result = printerServiceImplUnderTest.findCloudPrinterByQuery(printerQuery);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testRestorePrinter() {
        // Setup
        // Configure PrinterBackupsService.queryPrinters(...).
        final PrinterBackupsDO printerBackupsDO = new PrinterBackupsDO();
        printerBackupsDO.setId(0L);
        printerBackupsDO.setStoreGuid("storeGuid");
        printerBackupsDO.setDeviceId("deviceId");
        printerBackupsDO.setPrintListJson("printListJson");
        printerBackupsDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockPrinterBackupsService.queryPrinters("storeGuid", "deviceId")).thenReturn(printerBackupsDO);

        // Configure PrinterMapstruct.fromPrinterDTO(...).
        final PrinterDO printerDO = new PrinterDO();
        printerDO.setId(0L);
        printerDO.setStoreGuid("storeGuid");
        printerDO.setDeviceId("0");
        printerDO.setPrinterGuid("printerGuid");
        printerDO.setPrinterName("printerName");
        printerDO.setBusinessType(0);
        printerDO.setPrinterType(0);
        printerDO.setPrinterIp("printerIp");
        printerDO.setPrintCount(0);
        printerDO.setPrintCut(0);
        printerDO.setIsMaster(false);
        final PrinterDTO printerDTO = new PrinterDTO();
        printerDTO.setStoreGuid("storeGuid");
        printerDTO.setStoreName("storeName");
        printerDTO.setDeviceId("deviceId");
        printerDTO.setPrinterGuid("printerGuid");
        printerDTO.setPrinterName("printerName");
        printerDTO.setBusinessType(0);
        printerDTO.setPrinterType(0);
        printerDTO.setPrinterIp("printerIp");
        printerDTO.setPrinterPort(0);
        printerDTO.setPrintPage("printPage");
        printerDTO.setPrintCut(0);
        printerDTO.setArrayOfInvoiceType(Arrays.asList(0));
        printerDTO.setArrayOfItemGuid(Arrays.asList("value"));
        printerDTO.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterInvoiceDTO printerInvoiceDTO = new PrinterInvoiceDTO();
        printerInvoiceDTO.setInvoiceType(0);
        printerInvoiceDTO.setInvoiceName("invoiceName");
        printerInvoiceDTO.setPrintCount(0);
        printerDTO.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO));
        final PrinterItemDTO printerItemDTO = new PrinterItemDTO();
        printerDTO.setArrayOfItemDTO(Arrays.asList(printerItemDTO));
        final PrinterAreaDTO printerAreaDTO = new PrinterAreaDTO();
        printerDTO.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO));
        printerDTO.setDeviceNo("deviceNo");
        printerDTO.setDeviceKey("deviceKey");
        printerDTO.setManufacturersType(0);
        printerDTO.setDeviceState(0);
        when(mockPrinterMapstruct.fromPrinterDTO(printerDTO)).thenReturn(printerDO);

        // Configure PrinterMapstruct.toPrinterDTO(...).
        final PrinterDTO printerDTO1 = new PrinterDTO();
        printerDTO1.setStoreGuid("storeGuid");
        printerDTO1.setStoreName("storeName");
        printerDTO1.setDeviceId("deviceId");
        printerDTO1.setPrinterGuid("printerGuid");
        printerDTO1.setPrinterName("printerName");
        printerDTO1.setBusinessType(0);
        printerDTO1.setPrinterType(0);
        printerDTO1.setPrinterIp("printerIp");
        printerDTO1.setPrinterPort(0);
        printerDTO1.setPrintPage("printPage");
        printerDTO1.setPrintCut(0);
        printerDTO1.setArrayOfInvoiceType(Arrays.asList(0));
        printerDTO1.setArrayOfItemGuid(Arrays.asList("value"));
        printerDTO1.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterInvoiceDTO printerInvoiceDTO1 = new PrinterInvoiceDTO();
        printerInvoiceDTO1.setInvoiceType(0);
        printerInvoiceDTO1.setInvoiceName("invoiceName");
        printerInvoiceDTO1.setPrintCount(0);
        printerDTO1.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO1));
        final PrinterItemDTO printerItemDTO1 = new PrinterItemDTO();
        printerDTO1.setArrayOfItemDTO(Arrays.asList(printerItemDTO1));
        final PrinterAreaDTO printerAreaDTO1 = new PrinterAreaDTO();
        printerDTO1.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO1));
        printerDTO1.setDeviceNo("deviceNo");
        printerDTO1.setDeviceKey("deviceKey");
        printerDTO1.setManufacturersType(0);
        printerDTO1.setDeviceState(0);
        final PrinterReadDO printerReadDO = new PrinterReadDO();
        printerReadDO.setId(0L);
        printerReadDO.setPrinterGuid("printerGuid");
        printerReadDO.setPrinterName("printerName");
        printerReadDO.setBusinessType(0);
        printerReadDO.setPrinterType(0);
        printerReadDO.setPrinterPort(0);
        printerReadDO.setDeviceNo("deviceNo");
        when(mockPrinterMapstruct.toPrinterDTO(printerReadDO)).thenReturn(printerDTO1);

        when(mockDistributedService.nextPrinterGuid()).thenReturn("printerGuid");
        when(mockStoreDeviceService.isMasterDevice("storeGuid", "deviceId")).thenReturn(false);

        // Run the test
        final boolean result = printerServiceImplUnderTest.restorePrinter("deviceId");

        // Verify the results
        assertThat(result).isTrue();
        verify(mockCloudPrinterService).deletePrinter("deviceNo");
        verify(mockPrinterInvoiceService).deletePrinterInvoice("printerGuid");
        verify(mockPrinterItemService).deletePrinterItem("printerGuid");
        verify(mockPrinterAreaService).deletePrinterArea("printerGuid");
        verify(mockPrintRecordService).deletePrinterRecord("printerGuid");

        // Confirm PrinterLogService.publishDeleteLog(...).
        final PrinterDTO printerDTO2 = new PrinterDTO();
        printerDTO2.setStoreGuid("storeGuid");
        printerDTO2.setStoreName("storeName");
        printerDTO2.setDeviceId("deviceId");
        printerDTO2.setPrinterGuid("printerGuid");
        printerDTO2.setPrinterName("printerName");
        printerDTO2.setBusinessType(0);
        printerDTO2.setPrinterType(0);
        printerDTO2.setPrinterIp("printerIp");
        printerDTO2.setPrinterPort(0);
        printerDTO2.setPrintPage("printPage");
        printerDTO2.setPrintCut(0);
        printerDTO2.setArrayOfInvoiceType(Arrays.asList(0));
        printerDTO2.setArrayOfItemGuid(Arrays.asList("value"));
        printerDTO2.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterInvoiceDTO printerInvoiceDTO2 = new PrinterInvoiceDTO();
        printerInvoiceDTO2.setInvoiceType(0);
        printerInvoiceDTO2.setInvoiceName("invoiceName");
        printerInvoiceDTO2.setPrintCount(0);
        printerDTO2.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO2));
        final PrinterItemDTO printerItemDTO2 = new PrinterItemDTO();
        printerDTO2.setArrayOfItemDTO(Arrays.asList(printerItemDTO2));
        final PrinterAreaDTO printerAreaDTO2 = new PrinterAreaDTO();
        printerDTO2.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO2));
        printerDTO2.setDeviceNo("deviceNo");
        printerDTO2.setDeviceKey("deviceKey");
        printerDTO2.setManufacturersType(0);
        printerDTO2.setDeviceState(0);
        verify(mockPrinterLogService).publishDeleteLog(any(Object.class), eq(printerDTO2));

        // Confirm PrinterInvoiceService.bindPrinterInvoice(...).
        final PrinterDTO printerDTO3 = new PrinterDTO();
        printerDTO3.setStoreGuid("storeGuid");
        printerDTO3.setStoreName("storeName");
        printerDTO3.setDeviceId("deviceId");
        printerDTO3.setPrinterGuid("printerGuid");
        printerDTO3.setPrinterName("printerName");
        printerDTO3.setBusinessType(0);
        printerDTO3.setPrinterType(0);
        printerDTO3.setPrinterIp("printerIp");
        printerDTO3.setPrinterPort(0);
        printerDTO3.setPrintPage("printPage");
        printerDTO3.setPrintCut(0);
        printerDTO3.setArrayOfInvoiceType(Arrays.asList(0));
        printerDTO3.setArrayOfItemGuid(Arrays.asList("value"));
        printerDTO3.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterInvoiceDTO printerInvoiceDTO3 = new PrinterInvoiceDTO();
        printerInvoiceDTO3.setInvoiceType(0);
        printerInvoiceDTO3.setInvoiceName("invoiceName");
        printerInvoiceDTO3.setPrintCount(0);
        printerDTO3.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO3));
        final PrinterItemDTO printerItemDTO3 = new PrinterItemDTO();
        printerDTO3.setArrayOfItemDTO(Arrays.asList(printerItemDTO3));
        final PrinterAreaDTO printerAreaDTO3 = new PrinterAreaDTO();
        printerDTO3.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO3));
        printerDTO3.setDeviceNo("deviceNo");
        printerDTO3.setDeviceKey("deviceKey");
        printerDTO3.setManufacturersType(0);
        printerDTO3.setDeviceState(0);
        verify(mockPrinterInvoiceService).bindPrinterInvoice(printerDTO3);

        // Confirm PrinterItemService.bindPrinterItem(...).
        final PrinterDTO printerDTO4 = new PrinterDTO();
        printerDTO4.setStoreGuid("storeGuid");
        printerDTO4.setStoreName("storeName");
        printerDTO4.setDeviceId("deviceId");
        printerDTO4.setPrinterGuid("printerGuid");
        printerDTO4.setPrinterName("printerName");
        printerDTO4.setBusinessType(0);
        printerDTO4.setPrinterType(0);
        printerDTO4.setPrinterIp("printerIp");
        printerDTO4.setPrinterPort(0);
        printerDTO4.setPrintPage("printPage");
        printerDTO4.setPrintCut(0);
        printerDTO4.setArrayOfInvoiceType(Arrays.asList(0));
        printerDTO4.setArrayOfItemGuid(Arrays.asList("value"));
        printerDTO4.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterInvoiceDTO printerInvoiceDTO4 = new PrinterInvoiceDTO();
        printerInvoiceDTO4.setInvoiceType(0);
        printerInvoiceDTO4.setInvoiceName("invoiceName");
        printerInvoiceDTO4.setPrintCount(0);
        printerDTO4.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO4));
        final PrinterItemDTO printerItemDTO4 = new PrinterItemDTO();
        printerDTO4.setArrayOfItemDTO(Arrays.asList(printerItemDTO4));
        final PrinterAreaDTO printerAreaDTO4 = new PrinterAreaDTO();
        printerDTO4.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO4));
        printerDTO4.setDeviceNo("deviceNo");
        printerDTO4.setDeviceKey("deviceKey");
        printerDTO4.setManufacturersType(0);
        printerDTO4.setDeviceState(0);
        verify(mockPrinterItemService).bindPrinterItem(printerDTO4);

        // Confirm PrinterAreaService.bindPrinterArea(...).
        final PrinterDTO printerDTO5 = new PrinterDTO();
        printerDTO5.setStoreGuid("storeGuid");
        printerDTO5.setStoreName("storeName");
        printerDTO5.setDeviceId("deviceId");
        printerDTO5.setPrinterGuid("printerGuid");
        printerDTO5.setPrinterName("printerName");
        printerDTO5.setBusinessType(0);
        printerDTO5.setPrinterType(0);
        printerDTO5.setPrinterIp("printerIp");
        printerDTO5.setPrinterPort(0);
        printerDTO5.setPrintPage("printPage");
        printerDTO5.setPrintCut(0);
        printerDTO5.setArrayOfInvoiceType(Arrays.asList(0));
        printerDTO5.setArrayOfItemGuid(Arrays.asList("value"));
        printerDTO5.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterInvoiceDTO printerInvoiceDTO5 = new PrinterInvoiceDTO();
        printerInvoiceDTO5.setInvoiceType(0);
        printerInvoiceDTO5.setInvoiceName("invoiceName");
        printerInvoiceDTO5.setPrintCount(0);
        printerDTO5.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO5));
        final PrinterItemDTO printerItemDTO5 = new PrinterItemDTO();
        printerDTO5.setArrayOfItemDTO(Arrays.asList(printerItemDTO5));
        final PrinterAreaDTO printerAreaDTO5 = new PrinterAreaDTO();
        printerDTO5.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO5));
        printerDTO5.setDeviceNo("deviceNo");
        printerDTO5.setDeviceKey("deviceKey");
        printerDTO5.setManufacturersType(0);
        printerDTO5.setDeviceState(0);
        verify(mockPrinterAreaService).bindPrinterArea(printerDTO5);

        // Confirm PrinterLogService.publishAddLog(...).
        final PrinterDTO printerDTO6 = new PrinterDTO();
        printerDTO6.setStoreGuid("storeGuid");
        printerDTO6.setStoreName("storeName");
        printerDTO6.setDeviceId("deviceId");
        printerDTO6.setPrinterGuid("printerGuid");
        printerDTO6.setPrinterName("printerName");
        printerDTO6.setBusinessType(0);
        printerDTO6.setPrinterType(0);
        printerDTO6.setPrinterIp("printerIp");
        printerDTO6.setPrinterPort(0);
        printerDTO6.setPrintPage("printPage");
        printerDTO6.setPrintCut(0);
        printerDTO6.setArrayOfInvoiceType(Arrays.asList(0));
        printerDTO6.setArrayOfItemGuid(Arrays.asList("value"));
        printerDTO6.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterInvoiceDTO printerInvoiceDTO6 = new PrinterInvoiceDTO();
        printerInvoiceDTO6.setInvoiceType(0);
        printerInvoiceDTO6.setInvoiceName("invoiceName");
        printerInvoiceDTO6.setPrintCount(0);
        printerDTO6.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO6));
        final PrinterItemDTO printerItemDTO6 = new PrinterItemDTO();
        printerDTO6.setArrayOfItemDTO(Arrays.asList(printerItemDTO6));
        final PrinterAreaDTO printerAreaDTO6 = new PrinterAreaDTO();
        printerDTO6.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO6));
        printerDTO6.setDeviceNo("deviceNo");
        printerDTO6.setDeviceKey("deviceKey");
        printerDTO6.setManufacturersType(0);
        printerDTO6.setDeviceState(0);
        verify(mockPrinterLogService).publishAddLog(any(Object.class), eq(printerDTO6));
    }
}
