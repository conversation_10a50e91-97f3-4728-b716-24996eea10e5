package com.holder.saas.print.service.impl;

import com.holderzone.framework.base.dto.log.LogDTO;
import com.holderzone.framework.exception.ParamException;
import com.holderzone.saas.store.dto.print.PrinterAreaDTO;
import com.holderzone.saas.store.dto.print.PrinterDTO;
import com.holderzone.saas.store.dto.print.PrinterItemDTO;
import com.holderzone.sdk.event.LogPublisher;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.verify;

@RunWith(MockitoJUnitRunner.class)
public class PrinterLogServiceImplTest {

    @Mock
    private LogPublisher mockLogPublisher;

    private PrinterLogServiceImpl printerLogServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        printerLogServiceImplUnderTest = new PrinterLogServiceImpl(mockLogPublisher);
    }

    @Test
    public void testPublishAddLog() throws Exception {
        // Setup
        final PrinterDTO printerDtoAfter = new PrinterDTO();
        printerDtoAfter.setStoreGuid("storeGuid");
        printerDtoAfter.setStoreName("storeName");
        printerDtoAfter.setBusinessType(0);
        printerDtoAfter.setArrayOfItemGuid(Arrays.asList("value"));
        printerDtoAfter.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterItemDTO printerItemDTO = new PrinterItemDTO();
        printerItemDTO.setItemName("itemName");
        printerDtoAfter.setArrayOfItemDTO(Arrays.asList(printerItemDTO));
        final PrinterAreaDTO printerAreaDTO = new PrinterAreaDTO();
        printerAreaDTO.setAreaName("areaName");
        printerDtoAfter.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO));

        // Run the test
        printerLogServiceImplUnderTest.publishAddLog("source", printerDtoAfter);

        // Verify the results
        verify(mockLogPublisher).send(any(Object.class), any(LogDTO.class));
    }

    @Test
    public void testPublishAddLog_LogPublisherThrowsParamException() throws Exception {
        // Setup
        final PrinterDTO printerDtoAfter = new PrinterDTO();
        printerDtoAfter.setStoreGuid("storeGuid");
        printerDtoAfter.setStoreName("storeName");
        printerDtoAfter.setBusinessType(0);
        printerDtoAfter.setArrayOfItemGuid(Arrays.asList("value"));
        printerDtoAfter.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterItemDTO printerItemDTO = new PrinterItemDTO();
        printerItemDTO.setItemName("itemName");
        printerDtoAfter.setArrayOfItemDTO(Arrays.asList(printerItemDTO));
        final PrinterAreaDTO printerAreaDTO = new PrinterAreaDTO();
        printerAreaDTO.setAreaName("areaName");
        printerDtoAfter.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO));

        doThrow(ParamException.class).when(mockLogPublisher).send(any(Object.class), any(LogDTO.class));

        // Run the test
        printerLogServiceImplUnderTest.publishAddLog("source", printerDtoAfter);

        // Verify the results
    }

    @Test
    public void testPublishDeleteLog() throws Exception {
        // Setup
        final PrinterDTO printerDtoBefore = new PrinterDTO();
        printerDtoBefore.setStoreGuid("storeGuid");
        printerDtoBefore.setStoreName("storeName");
        printerDtoBefore.setBusinessType(0);
        printerDtoBefore.setArrayOfItemGuid(Arrays.asList("value"));
        printerDtoBefore.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterItemDTO printerItemDTO = new PrinterItemDTO();
        printerItemDTO.setItemName("itemName");
        printerDtoBefore.setArrayOfItemDTO(Arrays.asList(printerItemDTO));
        final PrinterAreaDTO printerAreaDTO = new PrinterAreaDTO();
        printerAreaDTO.setAreaName("areaName");
        printerDtoBefore.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO));

        // Run the test
        printerLogServiceImplUnderTest.publishDeleteLog("source", printerDtoBefore);

        // Verify the results
        verify(mockLogPublisher).send(any(Object.class), any(LogDTO.class));
    }

    @Test
    public void testPublishDeleteLog_LogPublisherThrowsParamException() throws Exception {
        // Setup
        final PrinterDTO printerDtoBefore = new PrinterDTO();
        printerDtoBefore.setStoreGuid("storeGuid");
        printerDtoBefore.setStoreName("storeName");
        printerDtoBefore.setBusinessType(0);
        printerDtoBefore.setArrayOfItemGuid(Arrays.asList("value"));
        printerDtoBefore.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterItemDTO printerItemDTO = new PrinterItemDTO();
        printerItemDTO.setItemName("itemName");
        printerDtoBefore.setArrayOfItemDTO(Arrays.asList(printerItemDTO));
        final PrinterAreaDTO printerAreaDTO = new PrinterAreaDTO();
        printerAreaDTO.setAreaName("areaName");
        printerDtoBefore.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO));

        doThrow(ParamException.class).when(mockLogPublisher).send(any(Object.class), any(LogDTO.class));

        // Run the test
        printerLogServiceImplUnderTest.publishDeleteLog("source", printerDtoBefore);

        // Verify the results
    }

    @Test
    public void testPublishUpdateLog() throws Exception {
        // Setup
        final PrinterDTO printerDtoBefore = new PrinterDTO();
        printerDtoBefore.setStoreGuid("storeGuid");
        printerDtoBefore.setStoreName("storeName");
        printerDtoBefore.setBusinessType(0);
        printerDtoBefore.setArrayOfItemGuid(Arrays.asList("value"));
        printerDtoBefore.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterItemDTO printerItemDTO = new PrinterItemDTO();
        printerItemDTO.setItemName("itemName");
        printerDtoBefore.setArrayOfItemDTO(Arrays.asList(printerItemDTO));
        final PrinterAreaDTO printerAreaDTO = new PrinterAreaDTO();
        printerAreaDTO.setAreaName("areaName");
        printerDtoBefore.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO));

        final PrinterDTO printerDtoAfter = new PrinterDTO();
        printerDtoAfter.setStoreGuid("storeGuid");
        printerDtoAfter.setStoreName("storeName");
        printerDtoAfter.setBusinessType(0);
        printerDtoAfter.setArrayOfItemGuid(Arrays.asList("value"));
        printerDtoAfter.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterItemDTO printerItemDTO1 = new PrinterItemDTO();
        printerItemDTO1.setItemName("itemName");
        printerDtoAfter.setArrayOfItemDTO(Arrays.asList(printerItemDTO1));
        final PrinterAreaDTO printerAreaDTO1 = new PrinterAreaDTO();
        printerAreaDTO1.setAreaName("areaName");
        printerDtoAfter.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO1));

        // Run the test
        printerLogServiceImplUnderTest.publishUpdateLog("source", printerDtoBefore, printerDtoAfter);

        // Verify the results
        verify(mockLogPublisher).send(any(Object.class), any(LogDTO.class));
    }

    @Test
    public void testPublishUpdateLog_LogPublisherThrowsParamException() throws Exception {
        // Setup
        final PrinterDTO printerDtoBefore = new PrinterDTO();
        printerDtoBefore.setStoreGuid("storeGuid");
        printerDtoBefore.setStoreName("storeName");
        printerDtoBefore.setBusinessType(0);
        printerDtoBefore.setArrayOfItemGuid(Arrays.asList("value"));
        printerDtoBefore.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterItemDTO printerItemDTO = new PrinterItemDTO();
        printerItemDTO.setItemName("itemName");
        printerDtoBefore.setArrayOfItemDTO(Arrays.asList(printerItemDTO));
        final PrinterAreaDTO printerAreaDTO = new PrinterAreaDTO();
        printerAreaDTO.setAreaName("areaName");
        printerDtoBefore.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO));

        final PrinterDTO printerDtoAfter = new PrinterDTO();
        printerDtoAfter.setStoreGuid("storeGuid");
        printerDtoAfter.setStoreName("storeName");
        printerDtoAfter.setBusinessType(0);
        printerDtoAfter.setArrayOfItemGuid(Arrays.asList("value"));
        printerDtoAfter.setArrayOfAreaGuid(Arrays.asList("value"));
        final PrinterItemDTO printerItemDTO1 = new PrinterItemDTO();
        printerItemDTO1.setItemName("itemName");
        printerDtoAfter.setArrayOfItemDTO(Arrays.asList(printerItemDTO1));
        final PrinterAreaDTO printerAreaDTO1 = new PrinterAreaDTO();
        printerAreaDTO1.setAreaName("areaName");
        printerDtoAfter.setArrayOfAreaDTO(Arrays.asList(printerAreaDTO1));

        doThrow(ParamException.class).when(mockLogPublisher).send(any(Object.class), any(LogDTO.class));

        // Run the test
        printerLogServiceImplUnderTest.publishUpdateLog("source", printerDtoBefore, printerDtoAfter);

        // Verify the results
    }
}
