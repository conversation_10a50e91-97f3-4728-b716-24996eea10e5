package com.holder.saas.print.service.impl;

import com.holder.saas.print.entity.domain.PrinterFormatDO;
import com.holder.saas.print.mapstruct.PrinterMapstruct;
import com.holder.saas.print.mapstruct.PrinterRawMaptstruct;
import com.holder.saas.print.service.DistributedService;
import com.holderzone.saas.store.dto.print.format.FormatDTO;
import com.holderzone.saas.store.dto.print.format.metadata.CustomMetadata;
import com.holderzone.saas.store.dto.print.raw.PrinterFormatRawDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PrinterFormatServiceImplTest {

    @Mock
    private DistributedService mockDistributedService;
    @Mock
    private PrinterMapstruct mockPrinterMapstruct;
    @Mock
    private PrinterRawMaptstruct mockPrinterRawMaptstruct;

    private PrinterFormatServiceImpl printerFormatServiceImplUnderTest;

    @Before
    public void setUp() {
        printerFormatServiceImplUnderTest = new PrinterFormatServiceImpl(mockDistributedService, mockPrinterMapstruct,
                mockPrinterRawMaptstruct);
        ReflectionTestUtils.setField(printerFormatServiceImplUnderTest, "templateEnable", false);
    }

    @Test
    public void testAdd() {
        // Setup
        final FormatDTO formatDTO = new FormatDTO();
        formatDTO.setGuid("e9c72b7a-297c-4aa8-b295-aa6d7db2d27b");
        formatDTO.setName("默认模板");
        formatDTO.setStoreGuid("storeGuid");
        formatDTO.setInvoiceType(0);
        formatDTO.setDeviceId("deviceId");
        final CustomMetadata customMetadata = new CustomMetadata();
        customMetadata.setEnable(false);
        customMetadata.setType(0);
        customMetadata.setText("text");
        formatDTO.setHeaders(Arrays.asList(customMetadata));
        final CustomMetadata customMetadata1 = new CustomMetadata();
        customMetadata1.setEnable(false);
        customMetadata1.setType(0);
        customMetadata1.setText("text");
        formatDTO.setFooters(Arrays.asList(customMetadata1));
        formatDTO.setIsEnable(false);

        when(mockDistributedService.nextInvoiceFormatId()).thenReturn("e9c72b7a-297c-4aa8-b295-aa6d7db2d27b");

        // Run the test
        printerFormatServiceImplUnderTest.add(formatDTO);

        // Verify the results
    }

    @Test
    public void testList1() {
        // Setup
        final FormatDTO formatDTO = new FormatDTO();
        formatDTO.setGuid("e9c72b7a-297c-4aa8-b295-aa6d7db2d27b");
        formatDTO.setName("默认模板");
        formatDTO.setStoreGuid("storeGuid");
        formatDTO.setInvoiceType(0);
        formatDTO.setDeviceId("deviceId");
        final CustomMetadata customMetadata = new CustomMetadata();
        customMetadata.setEnable(false);
        customMetadata.setType(0);
        customMetadata.setText("text");
        formatDTO.setHeaders(Arrays.asList(customMetadata));
        final CustomMetadata customMetadata1 = new CustomMetadata();
        customMetadata1.setEnable(false);
        customMetadata1.setType(0);
        customMetadata1.setText("text");
        formatDTO.setFooters(Arrays.asList(customMetadata1));
        formatDTO.setIsEnable(false);

        final FormatDTO formatDTO1 = new FormatDTO();
        formatDTO1.setGuid("e9c72b7a-297c-4aa8-b295-aa6d7db2d27b");
        formatDTO1.setName("默认模板");
        formatDTO1.setStoreGuid("storeGuid");
        formatDTO1.setInvoiceType(0);
        formatDTO1.setDeviceId("deviceId");
        final CustomMetadata customMetadata2 = new CustomMetadata();
        customMetadata2.setEnable(false);
        customMetadata2.setType(0);
        customMetadata2.setText("text");
        formatDTO1.setHeaders(Arrays.asList(customMetadata2));
        final CustomMetadata customMetadata3 = new CustomMetadata();
        customMetadata3.setEnable(false);
        customMetadata3.setType(0);
        customMetadata3.setText("text");
        formatDTO1.setFooters(Arrays.asList(customMetadata3));
        formatDTO1.setIsEnable(false);
        final List<FormatDTO> expectedResult = Arrays.asList(formatDTO1);
        when(mockDistributedService.nextInvoiceFormatId()).thenReturn("e9c72b7a-297c-4aa8-b295-aa6d7db2d27b");

        // Run the test
        final List<FormatDTO> result = printerFormatServiceImplUnderTest.list(formatDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testJudgeEnablePreCheckFormat() {
        assertThat(printerFormatServiceImplUnderTest.judgeEnablePreCheckFormat("storeGuid")).isFalse();
    }

    @Test
    public void testEnable() {
        // Setup
        final FormatDTO formatDTO = new FormatDTO();
        formatDTO.setGuid("e9c72b7a-297c-4aa8-b295-aa6d7db2d27b");
        formatDTO.setName("默认模板");
        formatDTO.setStoreGuid("storeGuid");
        formatDTO.setInvoiceType(0);
        formatDTO.setDeviceId("deviceId");
        final CustomMetadata customMetadata = new CustomMetadata();
        customMetadata.setEnable(false);
        customMetadata.setType(0);
        customMetadata.setText("text");
        formatDTO.setHeaders(Arrays.asList(customMetadata));
        final CustomMetadata customMetadata1 = new CustomMetadata();
        customMetadata1.setEnable(false);
        customMetadata1.setType(0);
        customMetadata1.setText("text");
        formatDTO.setFooters(Arrays.asList(customMetadata1));
        formatDTO.setIsEnable(false);

        // Run the test
        printerFormatServiceImplUnderTest.enable(formatDTO);

        // Verify the results
    }

    @Test
    public void testDelete() {
        // Setup
        final FormatDTO formatDTO = new FormatDTO();
        formatDTO.setGuid("e9c72b7a-297c-4aa8-b295-aa6d7db2d27b");
        formatDTO.setName("默认模板");
        formatDTO.setStoreGuid("storeGuid");
        formatDTO.setInvoiceType(0);
        formatDTO.setDeviceId("deviceId");
        final CustomMetadata customMetadata = new CustomMetadata();
        customMetadata.setEnable(false);
        customMetadata.setType(0);
        customMetadata.setText("text");
        formatDTO.setHeaders(Arrays.asList(customMetadata));
        final CustomMetadata customMetadata1 = new CustomMetadata();
        customMetadata1.setEnable(false);
        customMetadata1.setType(0);
        customMetadata1.setText("text");
        formatDTO.setFooters(Arrays.asList(customMetadata1));
        formatDTO.setIsEnable(false);

        // Run the test
        printerFormatServiceImplUnderTest.delete(formatDTO);

        // Verify the results
    }

    @Test
    public void testQuery() {
        // Setup
        final FormatDTO expectedResult = new FormatDTO();
        expectedResult.setGuid("e9c72b7a-297c-4aa8-b295-aa6d7db2d27b");
        expectedResult.setName("默认模板");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setInvoiceType(0);
        expectedResult.setDeviceId("deviceId");
        final CustomMetadata customMetadata = new CustomMetadata();
        customMetadata.setEnable(false);
        customMetadata.setType(0);
        customMetadata.setText("text");
        expectedResult.setHeaders(Arrays.asList(customMetadata));
        final CustomMetadata customMetadata1 = new CustomMetadata();
        customMetadata1.setEnable(false);
        customMetadata1.setType(0);
        customMetadata1.setText("text");
        expectedResult.setFooters(Arrays.asList(customMetadata1));
        expectedResult.setIsEnable(false);

        // Run the test
        final FormatDTO result = printerFormatServiceImplUnderTest.query("storeGuid", 0);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testList2() {
        // Setup
        final FormatDTO formatDTO = new FormatDTO();
        formatDTO.setGuid("e9c72b7a-297c-4aa8-b295-aa6d7db2d27b");
        formatDTO.setName("默认模板");
        formatDTO.setStoreGuid("storeGuid");
        formatDTO.setInvoiceType(0);
        formatDTO.setDeviceId("deviceId");
        final CustomMetadata customMetadata = new CustomMetadata();
        customMetadata.setEnable(false);
        customMetadata.setType(0);
        customMetadata.setText("text");
        formatDTO.setHeaders(Arrays.asList(customMetadata));
        final CustomMetadata customMetadata1 = new CustomMetadata();
        customMetadata1.setEnable(false);
        customMetadata1.setType(0);
        customMetadata1.setText("text");
        formatDTO.setFooters(Arrays.asList(customMetadata1));
        formatDTO.setIsEnable(false);
        final List<FormatDTO> expectedResult = Arrays.asList(formatDTO);
        when(mockDistributedService.nextInvoiceFormatId()).thenReturn("e9c72b7a-297c-4aa8-b295-aa6d7db2d27b");

        // Run the test
        final List<FormatDTO> result = printerFormatServiceImplUnderTest.list("storeGuid", 0);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetInvoiceUrls() {
        assertThat(printerFormatServiceImplUnderTest.getInvoiceUrls("storeGuid")).isEqualTo(Arrays.asList("value"));
        assertThat(printerFormatServiceImplUnderTest.getInvoiceUrls("storeGuid")).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testListRaw() {
        // Setup
        final PrinterFormatRawDTO printerFormatRawDTO = new PrinterFormatRawDTO();
        printerFormatRawDTO.setGuid("5444bf38-a377-4f3c-af8f-f356fad7a2a6");
        printerFormatRawDTO.setName("name");
        printerFormatRawDTO.setStoreGuid("storeGuid");
        printerFormatRawDTO.setInvoiceType(0);
        printerFormatRawDTO.setFormatJsonString("formatJsonString");
        final List<PrinterFormatRawDTO> expectedResult = Arrays.asList(printerFormatRawDTO);

        // Configure PrinterRawMaptstruct.toFormatRawDTO(...).
        final PrinterFormatRawDTO printerFormatRawDTO1 = new PrinterFormatRawDTO();
        printerFormatRawDTO1.setGuid("5444bf38-a377-4f3c-af8f-f356fad7a2a6");
        printerFormatRawDTO1.setName("name");
        printerFormatRawDTO1.setStoreGuid("storeGuid");
        printerFormatRawDTO1.setInvoiceType(0);
        printerFormatRawDTO1.setFormatJsonString("formatJsonString");
        final List<PrinterFormatRawDTO> printerFormatRawDTOS = Arrays.asList(printerFormatRawDTO1);
        final PrinterFormatDO printerFormatDO = new PrinterFormatDO();
        printerFormatDO.setId(0L);
        printerFormatDO.setGuid("e9c72b7a-297c-4aa8-b295-aa6d7db2d27b");
        printerFormatDO.setName("默认模板");
        printerFormatDO.setStoreGuid("storeGuid");
        printerFormatDO.setInvoiceType(0);
        printerFormatDO.setFormatJsonString("formatJsonString");
        printerFormatDO.setIsEnable(false);
        printerFormatDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        printerFormatDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<PrinterFormatDO> list = Arrays.asList(printerFormatDO);
        when(mockPrinterRawMaptstruct.toFormatRawDTO(list)).thenReturn(printerFormatRawDTOS);

        // Run the test
        final List<PrinterFormatRawDTO> result = printerFormatServiceImplUnderTest.listRaw("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testListRaw_PrinterRawMaptstructReturnsNoItems() {
        // Setup
        // Configure PrinterRawMaptstruct.toFormatRawDTO(...).
        final PrinterFormatDO printerFormatDO = new PrinterFormatDO();
        printerFormatDO.setId(0L);
        printerFormatDO.setGuid("e9c72b7a-297c-4aa8-b295-aa6d7db2d27b");
        printerFormatDO.setName("默认模板");
        printerFormatDO.setStoreGuid("storeGuid");
        printerFormatDO.setInvoiceType(0);
        printerFormatDO.setFormatJsonString("formatJsonString");
        printerFormatDO.setIsEnable(false);
        printerFormatDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        printerFormatDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<PrinterFormatDO> list = Arrays.asList(printerFormatDO);
        when(mockPrinterRawMaptstruct.toFormatRawDTO(list)).thenReturn(Collections.emptyList());

        // Run the test
        final List<PrinterFormatRawDTO> result = printerFormatServiceImplUnderTest.listRaw("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }
}
