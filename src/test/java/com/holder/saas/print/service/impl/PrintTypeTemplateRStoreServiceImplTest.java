package com.holder.saas.print.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holder.saas.print.entity.domain.type.PrintTypeTemplateRStoreDO;
import com.holder.saas.print.mapper.PrintTypeTemplateRStoreMapper;
import com.holder.saas.print.service.feign.StoreDeviceFeignService;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.print.type.TemplateStoreVO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PrintTypeTemplateRStoreServiceImplTest {

    @Mock
    private PrintTypeTemplateRStoreMapper mockRStoreMapper;
    @Mock
    private StoreDeviceFeignService mockStoreDeviceFeignService;

    private PrintTypeTemplateRStoreServiceImpl printTypeTemplateRStoreServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        printTypeTemplateRStoreServiceImplUnderTest = new PrintTypeTemplateRStoreServiceImpl(mockRStoreMapper,
                mockStoreDeviceFeignService);
    }

    @Test
    public void testRemoveByTemplateGuid() {
        // Setup
        // Run the test
        printTypeTemplateRStoreServiceImplUnderTest.removeByTemplateGuid("templateGuid");

        // Verify the results
        verify(mockRStoreMapper).delete(any(LambdaQueryWrapper.class));
    }

    @Test
    public void testQueryTemplateStoreVOList() {
        // Setup
        final TemplateStoreVO templateStoreVO = new TemplateStoreVO();
        templateStoreVO.setGuid("0f331bb0-e841-47d1-9f7c-dc5cf561f129");
        templateStoreVO.setName("name");
        final List<TemplateStoreVO> expectedResult = Arrays.asList(templateStoreVO);

        // Configure StoreDeviceFeignService.queryStoreByIdList(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("9359105a-70d2-4c6d-90a6-159bdecc96aa");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO);
        when(mockStoreDeviceFeignService.queryStoreByIdList(
                new SingleDataDTO("data", Arrays.asList("value")))).thenReturn(storeDTOS);

        // Run the test
        final List<TemplateStoreVO> result = printTypeTemplateRStoreServiceImplUnderTest.queryTemplateStoreVOList(false,
                "templateGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryTemplateStoreVOList_StoreDeviceFeignServiceReturnsNoItems() {
        // Setup
        when(mockStoreDeviceFeignService.queryStoreByIdList(
                new SingleDataDTO("data", Arrays.asList("value")))).thenReturn(Collections.emptyList());

        // Run the test
        final List<TemplateStoreVO> result = printTypeTemplateRStoreServiceImplUnderTest.queryTemplateStoreVOList(false,
                "templateGuid");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testQueryRepeatTemplateCount() {
        // Setup
        when(mockRStoreMapper.queryRepeatTemplateCount("brandGuid", Arrays.asList("value"), Arrays.asList("value"),
                "15b04ce2-092a-4fce-b844-f930e14b6780")).thenReturn(0);

        // Run the test
        final int result = printTypeTemplateRStoreServiceImplUnderTest.queryRepeatTemplateCount("brandGuid",
                Arrays.asList("value"), Arrays.asList("value"), "15b04ce2-092a-4fce-b844-f930e14b6780");

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    public void testQueryTemplateStoreGuidList() {
        // Setup
        final PrintTypeTemplateRStoreDO printTypeTemplateRStoreDO = new PrintTypeTemplateRStoreDO();
        printTypeTemplateRStoreDO.setId(0L);
        printTypeTemplateRStoreDO.setTemplateGuid("templateGuid");
        printTypeTemplateRStoreDO.setStoreGuid("storeGuid");
        printTypeTemplateRStoreDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        printTypeTemplateRStoreDO.setIsDelete(false);
        final List<PrintTypeTemplateRStoreDO> expectedResult = Arrays.asList(printTypeTemplateRStoreDO);

        // Configure PrintTypeTemplateRStoreMapper.queryTemplateStoreGuidList(...).
        final PrintTypeTemplateRStoreDO printTypeTemplateRStoreDO1 = new PrintTypeTemplateRStoreDO();
        printTypeTemplateRStoreDO1.setId(0L);
        printTypeTemplateRStoreDO1.setTemplateGuid("templateGuid");
        printTypeTemplateRStoreDO1.setStoreGuid("storeGuid");
        printTypeTemplateRStoreDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        printTypeTemplateRStoreDO1.setIsDelete(false);
        final List<PrintTypeTemplateRStoreDO> printTypeTemplateRStoreDOS = Arrays.asList(printTypeTemplateRStoreDO1);
        when(mockRStoreMapper.queryTemplateStoreGuidList("brandGuid", Arrays.asList("value"),
                "templateGuid")).thenReturn(printTypeTemplateRStoreDOS);

        // Run the test
        final List<PrintTypeTemplateRStoreDO> result = printTypeTemplateRStoreServiceImplUnderTest.queryTemplateStoreGuidList(
                "brandGuid", Arrays.asList("value"), "templateGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryTemplateStoreGuidList_PrintTypeTemplateRStoreMapperReturnsNoItems() {
        // Setup
        when(mockRStoreMapper.queryTemplateStoreGuidList("brandGuid", Arrays.asList("value"),
                "templateGuid")).thenReturn(Collections.emptyList());

        // Run the test
        final List<PrintTypeTemplateRStoreDO> result = printTypeTemplateRStoreServiceImplUnderTest.queryTemplateStoreGuidList(
                "brandGuid", Arrays.asList("value"), "templateGuid");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testQueryTemplateByInvoiceTypeAndStoreGuid() {
        // Setup
        final PrintTypeTemplateRStoreDO expectedResult = new PrintTypeTemplateRStoreDO();
        expectedResult.setId(0L);
        expectedResult.setTemplateGuid("templateGuid");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setIsDelete(false);

        // Configure PrintTypeTemplateRStoreMapper.queryTemplateByInvoiceTypeAndStoreGuid(...).
        final PrintTypeTemplateRStoreDO printTypeTemplateRStoreDO = new PrintTypeTemplateRStoreDO();
        printTypeTemplateRStoreDO.setId(0L);
        printTypeTemplateRStoreDO.setTemplateGuid("templateGuid");
        printTypeTemplateRStoreDO.setStoreGuid("storeGuid");
        printTypeTemplateRStoreDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        printTypeTemplateRStoreDO.setIsDelete(false);
        when(mockRStoreMapper.queryTemplateByInvoiceTypeAndStoreGuid("invoiceType", "storeGuid"))
                .thenReturn(printTypeTemplateRStoreDO);

        // Run the test
        final PrintTypeTemplateRStoreDO result = printTypeTemplateRStoreServiceImplUnderTest.queryTemplateByInvoiceTypeAndStoreGuid(
                "invoiceType", "storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
