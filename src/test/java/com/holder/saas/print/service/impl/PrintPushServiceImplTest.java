package com.holder.saas.print.service.impl;

import com.holder.saas.print.entity.domain.PrintRecordDO;
import com.holder.saas.print.service.feign.PushMsgFeignService;
import com.holder.saas.print.template.factory.PrintTemplateFactory;
import com.holderzone.framework.base.dto.message.MessageDTO;
import com.holderzone.saas.store.dto.print.PrintRecordReqDTO;
import com.holderzone.saas.store.dto.print.content.PrintDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.verify;

@RunWith(MockitoJUnitRunner.class)
public class PrintPushServiceImplTest {

    @Mock
    private PushMsgFeignService mockPushMsgFeignService;
    @Mock
    private PrintTemplateFactory mockPrintTemplateFactory;

    private PrintPushServiceImpl printPushServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        printPushServiceImplUnderTest = new PrintPushServiceImpl(mockPushMsgFeignService, mockPrintTemplateFactory);
        ReflectionTestUtils.setField(printPushServiceImplUnderTest, "batchPushEnable", false);
    }

    @Test
    public void testPushPrintTaskMsg() {
        // Setup
        final PrintDTO printDTO = new PrintDTO();
        printDTO.setInvoiceType(0);
        printDTO.setEnterpriseGuid("enterpriseGuid");
        printDTO.setStoreGuid("storeGuid");
        printDTO.setPrintUid("printUid");
        printDTO.setDeviceId("deviceId");

        // Run the test
        printPushServiceImplUnderTest.pushPrintTaskMsg(printDTO, Arrays.asList("value"));

        // Verify the results
        verify(mockPushMsgFeignService).sendPrintMessage(any(MessageDTO.class));
    }

    @Test
    public void testPushPrintSucceedMsg() {
        // Setup
        final PrintRecordReqDTO printRecordReqDTO = new PrintRecordReqDTO();
        printRecordReqDTO.setMsgId("msgId");
        printRecordReqDTO.setDeviceId("deviceId");
        printRecordReqDTO.setRecordGuid("recordGuid");
        printRecordReqDTO.setPrintStatus(0);
        printRecordReqDTO.setPrintStatusMsg("printStatusMsg");

        final PrintRecordDO printRecordDO = new PrintRecordDO();
        printRecordDO.setId(0L);
        printRecordDO.setRecordUid("recordUid");
        printRecordDO.setRecordGuid("recordGuid");
        printRecordDO.setInvoiceType(0);
        printRecordDO.setPrintContent("printContent");

        // Run the test
        printPushServiceImplUnderTest.pushPrintSucceedMsg(printRecordReqDTO, printRecordDO, 0);

        // Verify the results
        verify(mockPushMsgFeignService).sendPrintMessage(any(MessageDTO.class));
    }

    @Test
    public void testPushPrintFailedMsg() {
        // Setup
        final PrintRecordReqDTO printRecordReqDTO = new PrintRecordReqDTO();
        printRecordReqDTO.setMsgId("msgId");
        printRecordReqDTO.setDeviceId("deviceId");
        printRecordReqDTO.setRecordGuid("recordGuid");
        printRecordReqDTO.setPrintStatus(0);
        printRecordReqDTO.setPrintStatusMsg("printStatusMsg");

        final PrintRecordDO printRecordDO = new PrintRecordDO();
        printRecordDO.setId(0L);
        printRecordDO.setRecordUid("recordUid");
        printRecordDO.setRecordGuid("recordGuid");
        printRecordDO.setInvoiceType(0);
        printRecordDO.setPrintContent("printContent");

        doReturn(null).when(mockPrintTemplateFactory).create(0, "printContent");

        // Run the test
        printPushServiceImplUnderTest.pushPrintFailedMsg(printRecordReqDTO, printRecordDO, 0);

        // Verify the results
        verify(mockPushMsgFeignService).sendPrintMessage(any(MessageDTO.class));
    }
}
