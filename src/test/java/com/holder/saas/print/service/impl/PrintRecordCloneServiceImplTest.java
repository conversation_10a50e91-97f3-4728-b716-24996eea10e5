package com.holder.saas.print.service.impl;

import com.holder.saas.print.entity.domain.PrintRecordDO;
import com.holder.saas.print.entity.domain.PrinterDO;
import com.holder.saas.print.entity.read.PrintRecordReadDO;
import com.holder.saas.print.entity.read.PrinterReadDO;
import com.holder.saas.print.service.DistributedService;
import com.holder.saas.print.service.PrintTypeTemplateService;
import com.holder.saas.print.service.PrinterFormatService;
import com.holder.saas.print.service.feign.ItemClientService;
import com.holder.saas.print.service.feign.TakeoutClientService;
import com.holderzone.saas.store.dto.item.common.ItemStringListDTO;
import com.holderzone.saas.store.dto.item.req.ItemSpuReqDTO;
import com.holderzone.saas.store.dto.item.resp.ItemInfoRespDTO;
import com.holderzone.saas.store.dto.print.content.PrintDTO;
import com.holderzone.saas.store.dto.print.format.OrderItemFormatDTO;
import com.holderzone.saas.store.dto.print.format.metadata.FormatMetadata;
import com.holderzone.saas.store.dto.print.type.PrintTypeTemplateDetailDTO;
import com.holderzone.saas.store.dto.print.type.TemplateDetailQO;
import com.holderzone.saas.store.dto.print.type.TemplateItemVO;
import com.holderzone.saas.store.dto.print.type.TemplateTypeVO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.Codec;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PrintRecordCloneServiceImplTest {

    @Mock
    private DistributedService mockDistributedService;
    @Mock
    private ItemClientService mockItemClientService;
    @Mock
    private PrintTypeTemplateService mockPrintTypeTemplateService;
    @Mock
    private RedissonClient mockRedissonSingleClient;
    @Mock
    private TakeoutClientService mockTakeoutClientService;
    @Mock
    private PrinterFormatService mockPrinterFormatService;

    private PrintRecordCloneServiceImpl printRecordCloneServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        printRecordCloneServiceImplUnderTest = new PrintRecordCloneServiceImpl(mockDistributedService,
                mockItemClientService, mockPrintTypeTemplateService, mockRedissonSingleClient, mockTakeoutClientService,
                mockPrinterFormatService);
    }

    @Test
    public void testCloneRecord() {
        // Setup
        final PrintDTO printDTO = new PrintDTO();
        printDTO.setInvoiceType(0);
        printDTO.setStoreGuid("storeGuid");
        printDTO.setPrintUid("printUid");
        printDTO.setTradeMode(0);
        printDTO.setOperatorStaffGuid("createStaffGuid");
        printDTO.setDeviceId("deviceId");

        final PrinterReadDO printerReadDO = new PrinterReadDO();
        printerReadDO.setPrinterGuid("printerGuid");
        printerReadDO.setBusinessType(0);
        printerReadDO.setPrinterType(0);
        printerReadDO.setPrinterIp("printerIp");
        printerReadDO.setPrinterPort(0);
        printerReadDO.setPrintCount(0);
        printerReadDO.setPrintPage("printPage");
        printerReadDO.setPrintCut(0);
        printerReadDO.setArrayOfItemGuid(Arrays.asList("value"));
        printerReadDO.setDeviceNo("deviceNo");
        printerReadDO.setDeviceKey("deviceKey");
        final List<PrinterReadDO> printers = Arrays.asList(printerReadDO);
        final PrintRecordDO printRecordDO = new PrintRecordDO();
        printRecordDO.setRecordUid("recordUid");
        printRecordDO.setRecordGuid("recordGuid");
        printRecordDO.setStoreGuid("storeGuid");
        printRecordDO.setDeviceId("deviceId");
        printRecordDO.setInvoiceType(0);
        printRecordDO.setPrinterGuid("printerGuid");
        printRecordDO.setPrintContent("printContent");
        printRecordDO.setCreateStaffGuid("createStaffGuid");
        final List<PrintRecordDO> printRecordsToInsert = Arrays.asList(printRecordDO);
        final PrintRecordReadDO printRecordReadDO = new PrintRecordReadDO();
        printRecordReadDO.setRecordUid("recordUid");
        printRecordReadDO.setRecordGuid("recordGuid");
        printRecordReadDO.setStoreGuid("storeGuid");
        printRecordReadDO.setInvoiceType(0);
        printRecordReadDO.setPrintContent("printContent");
        final PrinterDO printerDO = new PrinterDO();
        printerDO.setBusinessType(0);
        printerDO.setPrinterType(0);
        printerDO.setPrinterIp("printerIp");
        printerDO.setPrinterPort(0);
        printerDO.setPrintCount(0);
        printerDO.setPrintPage("printPage");
        printerDO.setDeviceNo("deviceNo");
        printerDO.setDeviceKey("deviceKey");
        printRecordReadDO.setPrinterDO(printerDO);
        final List<PrintRecordReadDO> printRecordsToCache = Arrays.asList(printRecordReadDO);

        // Configure ItemClientService.selectSpuItems(...).
        final ItemSpuReqDTO itemSpuReqDTO = new ItemSpuReqDTO();
        itemSpuReqDTO.setStoreGuid("storeGuid");
        itemSpuReqDTO.setItemGuids(Arrays.asList("value"));
        when(mockItemClientService.selectSpuItems(itemSpuReqDTO)).thenReturn(Arrays.asList("value"));

        when(mockDistributedService.nextBatchPrintRecordGuid(0L)).thenReturn(Arrays.asList("value"));

        // Configure PrinterFormatService.query(...).
        final OrderItemFormatDTO orderItemFormatDTO = new OrderItemFormatDTO();
        final FormatMetadata invoiceName = new FormatMetadata();
        invoiceName.setSize(0);
        invoiceName.setEnable(false);
        orderItemFormatDTO.setInvoiceName(invoiceName);
        final FormatMetadata foodFinishBarCode = new FormatMetadata();
        foodFinishBarCode.setSize(0);
        foodFinishBarCode.setEnable(false);
        orderItemFormatDTO.setFoodFinishBarCode(foodFinishBarCode);
        when(mockPrinterFormatService.query("storeGuid", 0)).thenReturn(orderItemFormatDTO);

        when(mockRedissonSingleClient.getBucket(eq("s"), any(Codec.class))).thenReturn(null);

        // Configure ItemClientService.listItemInfoBySalesModelNew(...).
        final ItemInfoRespDTO itemInfoRespDTO = new ItemInfoRespDTO();
        itemInfoRespDTO.setItemGuid("itemGuid");
        itemInfoRespDTO.setBrandGuid("brandGuid");
        itemInfoRespDTO.setTypeGuid("typeGuid");
        itemInfoRespDTO.setTypeSort(0);
        itemInfoRespDTO.setSort(0);
        final List<ItemInfoRespDTO> itemInfoRespDTOS = Arrays.asList(itemInfoRespDTO);
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setStoreGuid("storeGuid");
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        when(mockItemClientService.listItemInfoBySalesModelNew(itemStringListDTO)).thenReturn(itemInfoRespDTOS);

        // Configure ItemClientService.queryParentItemGuidByItem(...).
        final ItemStringListDTO query = new ItemStringListDTO();
        query.setStoreGuid("storeGuid");
        query.setDataList(Arrays.asList("value"));
        query.setItemList(Arrays.asList("value"));
        query.setRecordId(0L);
        when(mockItemClientService.queryParentItemGuidByItem(query)).thenReturn(new HashMap<>());

        // Configure PrintTypeTemplateService.queryByStoreAndInvoiceType(...).
        final PrintTypeTemplateDetailDTO printTypeTemplateDetailDTO = new PrintTypeTemplateDetailDTO();
        final TemplateTypeVO templateTypeVO = new TemplateTypeVO();
        templateTypeVO.setTypeGuid("0");
        templateTypeVO.setTypeName("其他");
        final TemplateItemVO templateItemVO = new TemplateItemVO();
        templateItemVO.setItemGuid("itemGuid");
        templateTypeVO.setItemList(Arrays.asList(templateItemVO));
        printTypeTemplateDetailDTO.setTypeList(Arrays.asList(templateTypeVO));
        final TemplateDetailQO query1 = new TemplateDetailQO();
        query1.setStoreGuid("storeGuid");
        query1.setInvoiceType(0);
        query1.setItemGuidList(Arrays.asList("value"));
        when(mockPrintTypeTemplateService.queryByStoreAndInvoiceType(query1)).thenReturn(printTypeTemplateDetailDTO);

        // Run the test
        printRecordCloneServiceImplUnderTest.cloneRecord(printDTO, printers, printRecordsToInsert, printRecordsToCache);

        // Verify the results
    }

    @Test
    public void testCloneRecord_ItemClientServiceSelectSpuItemsReturnsNoItems() {
        // Setup
        final PrintDTO printDTO = new PrintDTO();
        printDTO.setInvoiceType(0);
        printDTO.setStoreGuid("storeGuid");
        printDTO.setPrintUid("printUid");
        printDTO.setTradeMode(0);
        printDTO.setOperatorStaffGuid("createStaffGuid");
        printDTO.setDeviceId("deviceId");

        final PrinterReadDO printerReadDO = new PrinterReadDO();
        printerReadDO.setPrinterGuid("printerGuid");
        printerReadDO.setBusinessType(0);
        printerReadDO.setPrinterType(0);
        printerReadDO.setPrinterIp("printerIp");
        printerReadDO.setPrinterPort(0);
        printerReadDO.setPrintCount(0);
        printerReadDO.setPrintPage("printPage");
        printerReadDO.setPrintCut(0);
        printerReadDO.setArrayOfItemGuid(Arrays.asList("value"));
        printerReadDO.setDeviceNo("deviceNo");
        printerReadDO.setDeviceKey("deviceKey");
        final List<PrinterReadDO> printers = Arrays.asList(printerReadDO);
        final PrintRecordDO printRecordDO = new PrintRecordDO();
        printRecordDO.setRecordUid("recordUid");
        printRecordDO.setRecordGuid("recordGuid");
        printRecordDO.setStoreGuid("storeGuid");
        printRecordDO.setDeviceId("deviceId");
        printRecordDO.setInvoiceType(0);
        printRecordDO.setPrinterGuid("printerGuid");
        printRecordDO.setPrintContent("printContent");
        printRecordDO.setCreateStaffGuid("createStaffGuid");
        final List<PrintRecordDO> printRecordsToInsert = Arrays.asList(printRecordDO);
        final PrintRecordReadDO printRecordReadDO = new PrintRecordReadDO();
        printRecordReadDO.setRecordUid("recordUid");
        printRecordReadDO.setRecordGuid("recordGuid");
        printRecordReadDO.setStoreGuid("storeGuid");
        printRecordReadDO.setInvoiceType(0);
        printRecordReadDO.setPrintContent("printContent");
        final PrinterDO printerDO = new PrinterDO();
        printerDO.setBusinessType(0);
        printerDO.setPrinterType(0);
        printerDO.setPrinterIp("printerIp");
        printerDO.setPrinterPort(0);
        printerDO.setPrintCount(0);
        printerDO.setPrintPage("printPage");
        printerDO.setDeviceNo("deviceNo");
        printerDO.setDeviceKey("deviceKey");
        printRecordReadDO.setPrinterDO(printerDO);
        final List<PrintRecordReadDO> printRecordsToCache = Arrays.asList(printRecordReadDO);

        // Configure ItemClientService.selectSpuItems(...).
        final ItemSpuReqDTO itemSpuReqDTO = new ItemSpuReqDTO();
        itemSpuReqDTO.setStoreGuid("storeGuid");
        itemSpuReqDTO.setItemGuids(Arrays.asList("value"));
        when(mockItemClientService.selectSpuItems(itemSpuReqDTO)).thenReturn(Collections.emptyList());

        when(mockDistributedService.nextBatchPrintRecordGuid(0L)).thenReturn(Arrays.asList("value"));

        // Configure PrinterFormatService.query(...).
        final OrderItemFormatDTO orderItemFormatDTO = new OrderItemFormatDTO();
        final FormatMetadata invoiceName = new FormatMetadata();
        invoiceName.setSize(0);
        invoiceName.setEnable(false);
        orderItemFormatDTO.setInvoiceName(invoiceName);
        final FormatMetadata foodFinishBarCode = new FormatMetadata();
        foodFinishBarCode.setSize(0);
        foodFinishBarCode.setEnable(false);
        orderItemFormatDTO.setFoodFinishBarCode(foodFinishBarCode);
        when(mockPrinterFormatService.query("storeGuid", 0)).thenReturn(orderItemFormatDTO);

        when(mockRedissonSingleClient.getBucket(eq("s"), any(Codec.class))).thenReturn(null);

        // Configure ItemClientService.listItemInfoBySalesModelNew(...).
        final ItemInfoRespDTO itemInfoRespDTO = new ItemInfoRespDTO();
        itemInfoRespDTO.setItemGuid("itemGuid");
        itemInfoRespDTO.setBrandGuid("brandGuid");
        itemInfoRespDTO.setTypeGuid("typeGuid");
        itemInfoRespDTO.setTypeSort(0);
        itemInfoRespDTO.setSort(0);
        final List<ItemInfoRespDTO> itemInfoRespDTOS = Arrays.asList(itemInfoRespDTO);
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setStoreGuid("storeGuid");
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        when(mockItemClientService.listItemInfoBySalesModelNew(itemStringListDTO)).thenReturn(itemInfoRespDTOS);

        // Configure ItemClientService.queryParentItemGuidByItem(...).
        final ItemStringListDTO query = new ItemStringListDTO();
        query.setStoreGuid("storeGuid");
        query.setDataList(Arrays.asList("value"));
        query.setItemList(Arrays.asList("value"));
        query.setRecordId(0L);
        when(mockItemClientService.queryParentItemGuidByItem(query)).thenReturn(new HashMap<>());

        // Configure PrintTypeTemplateService.queryByStoreAndInvoiceType(...).
        final PrintTypeTemplateDetailDTO printTypeTemplateDetailDTO = new PrintTypeTemplateDetailDTO();
        final TemplateTypeVO templateTypeVO = new TemplateTypeVO();
        templateTypeVO.setTypeGuid("0");
        templateTypeVO.setTypeName("其他");
        final TemplateItemVO templateItemVO = new TemplateItemVO();
        templateItemVO.setItemGuid("itemGuid");
        templateTypeVO.setItemList(Arrays.asList(templateItemVO));
        printTypeTemplateDetailDTO.setTypeList(Arrays.asList(templateTypeVO));
        final TemplateDetailQO query1 = new TemplateDetailQO();
        query1.setStoreGuid("storeGuid");
        query1.setInvoiceType(0);
        query1.setItemGuidList(Arrays.asList("value"));
        when(mockPrintTypeTemplateService.queryByStoreAndInvoiceType(query1)).thenReturn(printTypeTemplateDetailDTO);

        // Run the test
        printRecordCloneServiceImplUnderTest.cloneRecord(printDTO, printers, printRecordsToInsert, printRecordsToCache);

        // Verify the results
    }

    @Test
    public void testCloneRecord_DistributedServiceReturnsNoItems() {
        // Setup
        final PrintDTO printDTO = new PrintDTO();
        printDTO.setInvoiceType(0);
        printDTO.setStoreGuid("storeGuid");
        printDTO.setPrintUid("printUid");
        printDTO.setTradeMode(0);
        printDTO.setOperatorStaffGuid("createStaffGuid");
        printDTO.setDeviceId("deviceId");

        final PrinterReadDO printerReadDO = new PrinterReadDO();
        printerReadDO.setPrinterGuid("printerGuid");
        printerReadDO.setBusinessType(0);
        printerReadDO.setPrinterType(0);
        printerReadDO.setPrinterIp("printerIp");
        printerReadDO.setPrinterPort(0);
        printerReadDO.setPrintCount(0);
        printerReadDO.setPrintPage("printPage");
        printerReadDO.setPrintCut(0);
        printerReadDO.setArrayOfItemGuid(Arrays.asList("value"));
        printerReadDO.setDeviceNo("deviceNo");
        printerReadDO.setDeviceKey("deviceKey");
        final List<PrinterReadDO> printers = Arrays.asList(printerReadDO);
        final PrintRecordDO printRecordDO = new PrintRecordDO();
        printRecordDO.setRecordUid("recordUid");
        printRecordDO.setRecordGuid("recordGuid");
        printRecordDO.setStoreGuid("storeGuid");
        printRecordDO.setDeviceId("deviceId");
        printRecordDO.setInvoiceType(0);
        printRecordDO.setPrinterGuid("printerGuid");
        printRecordDO.setPrintContent("printContent");
        printRecordDO.setCreateStaffGuid("createStaffGuid");
        final List<PrintRecordDO> printRecordsToInsert = Arrays.asList(printRecordDO);
        final PrintRecordReadDO printRecordReadDO = new PrintRecordReadDO();
        printRecordReadDO.setRecordUid("recordUid");
        printRecordReadDO.setRecordGuid("recordGuid");
        printRecordReadDO.setStoreGuid("storeGuid");
        printRecordReadDO.setInvoiceType(0);
        printRecordReadDO.setPrintContent("printContent");
        final PrinterDO printerDO = new PrinterDO();
        printerDO.setBusinessType(0);
        printerDO.setPrinterType(0);
        printerDO.setPrinterIp("printerIp");
        printerDO.setPrinterPort(0);
        printerDO.setPrintCount(0);
        printerDO.setPrintPage("printPage");
        printerDO.setDeviceNo("deviceNo");
        printerDO.setDeviceKey("deviceKey");
        printRecordReadDO.setPrinterDO(printerDO);
        final List<PrintRecordReadDO> printRecordsToCache = Arrays.asList(printRecordReadDO);

        // Configure ItemClientService.selectSpuItems(...).
        final ItemSpuReqDTO itemSpuReqDTO = new ItemSpuReqDTO();
        itemSpuReqDTO.setStoreGuid("storeGuid");
        itemSpuReqDTO.setItemGuids(Arrays.asList("value"));
        when(mockItemClientService.selectSpuItems(itemSpuReqDTO)).thenReturn(Arrays.asList("value"));

        when(mockDistributedService.nextBatchPrintRecordGuid(0L)).thenReturn(Collections.emptyList());

        // Configure PrinterFormatService.query(...).
        final OrderItemFormatDTO orderItemFormatDTO = new OrderItemFormatDTO();
        final FormatMetadata invoiceName = new FormatMetadata();
        invoiceName.setSize(0);
        invoiceName.setEnable(false);
        orderItemFormatDTO.setInvoiceName(invoiceName);
        final FormatMetadata foodFinishBarCode = new FormatMetadata();
        foodFinishBarCode.setSize(0);
        foodFinishBarCode.setEnable(false);
        orderItemFormatDTO.setFoodFinishBarCode(foodFinishBarCode);
        when(mockPrinterFormatService.query("storeGuid", 0)).thenReturn(orderItemFormatDTO);

        when(mockRedissonSingleClient.getBucket(eq("s"), any(Codec.class))).thenReturn(null);

        // Configure ItemClientService.listItemInfoBySalesModelNew(...).
        final ItemInfoRespDTO itemInfoRespDTO = new ItemInfoRespDTO();
        itemInfoRespDTO.setItemGuid("itemGuid");
        itemInfoRespDTO.setBrandGuid("brandGuid");
        itemInfoRespDTO.setTypeGuid("typeGuid");
        itemInfoRespDTO.setTypeSort(0);
        itemInfoRespDTO.setSort(0);
        final List<ItemInfoRespDTO> itemInfoRespDTOS = Arrays.asList(itemInfoRespDTO);
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setStoreGuid("storeGuid");
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        when(mockItemClientService.listItemInfoBySalesModelNew(itemStringListDTO)).thenReturn(itemInfoRespDTOS);

        // Configure ItemClientService.queryParentItemGuidByItem(...).
        final ItemStringListDTO query = new ItemStringListDTO();
        query.setStoreGuid("storeGuid");
        query.setDataList(Arrays.asList("value"));
        query.setItemList(Arrays.asList("value"));
        query.setRecordId(0L);
        when(mockItemClientService.queryParentItemGuidByItem(query)).thenReturn(new HashMap<>());

        // Configure PrintTypeTemplateService.queryByStoreAndInvoiceType(...).
        final PrintTypeTemplateDetailDTO printTypeTemplateDetailDTO = new PrintTypeTemplateDetailDTO();
        final TemplateTypeVO templateTypeVO = new TemplateTypeVO();
        templateTypeVO.setTypeGuid("0");
        templateTypeVO.setTypeName("其他");
        final TemplateItemVO templateItemVO = new TemplateItemVO();
        templateItemVO.setItemGuid("itemGuid");
        templateTypeVO.setItemList(Arrays.asList(templateItemVO));
        printTypeTemplateDetailDTO.setTypeList(Arrays.asList(templateTypeVO));
        final TemplateDetailQO query1 = new TemplateDetailQO();
        query1.setStoreGuid("storeGuid");
        query1.setInvoiceType(0);
        query1.setItemGuidList(Arrays.asList("value"));
        when(mockPrintTypeTemplateService.queryByStoreAndInvoiceType(query1)).thenReturn(printTypeTemplateDetailDTO);

        // Run the test
        printRecordCloneServiceImplUnderTest.cloneRecord(printDTO, printers, printRecordsToInsert, printRecordsToCache);

        // Verify the results
    }

    @Test
    public void testCloneRecord_ItemClientServiceListItemInfoBySalesModelNewReturnsNoItems() {
        // Setup
        final PrintDTO printDTO = new PrintDTO();
        printDTO.setInvoiceType(0);
        printDTO.setStoreGuid("storeGuid");
        printDTO.setPrintUid("printUid");
        printDTO.setTradeMode(0);
        printDTO.setOperatorStaffGuid("createStaffGuid");
        printDTO.setDeviceId("deviceId");

        final PrinterReadDO printerReadDO = new PrinterReadDO();
        printerReadDO.setPrinterGuid("printerGuid");
        printerReadDO.setBusinessType(0);
        printerReadDO.setPrinterType(0);
        printerReadDO.setPrinterIp("printerIp");
        printerReadDO.setPrinterPort(0);
        printerReadDO.setPrintCount(0);
        printerReadDO.setPrintPage("printPage");
        printerReadDO.setPrintCut(0);
        printerReadDO.setArrayOfItemGuid(Arrays.asList("value"));
        printerReadDO.setDeviceNo("deviceNo");
        printerReadDO.setDeviceKey("deviceKey");
        final List<PrinterReadDO> printers = Arrays.asList(printerReadDO);
        final PrintRecordDO printRecordDO = new PrintRecordDO();
        printRecordDO.setRecordUid("recordUid");
        printRecordDO.setRecordGuid("recordGuid");
        printRecordDO.setStoreGuid("storeGuid");
        printRecordDO.setDeviceId("deviceId");
        printRecordDO.setInvoiceType(0);
        printRecordDO.setPrinterGuid("printerGuid");
        printRecordDO.setPrintContent("printContent");
        printRecordDO.setCreateStaffGuid("createStaffGuid");
        final List<PrintRecordDO> printRecordsToInsert = Arrays.asList(printRecordDO);
        final PrintRecordReadDO printRecordReadDO = new PrintRecordReadDO();
        printRecordReadDO.setRecordUid("recordUid");
        printRecordReadDO.setRecordGuid("recordGuid");
        printRecordReadDO.setStoreGuid("storeGuid");
        printRecordReadDO.setInvoiceType(0);
        printRecordReadDO.setPrintContent("printContent");
        final PrinterDO printerDO = new PrinterDO();
        printerDO.setBusinessType(0);
        printerDO.setPrinterType(0);
        printerDO.setPrinterIp("printerIp");
        printerDO.setPrinterPort(0);
        printerDO.setPrintCount(0);
        printerDO.setPrintPage("printPage");
        printerDO.setDeviceNo("deviceNo");
        printerDO.setDeviceKey("deviceKey");
        printRecordReadDO.setPrinterDO(printerDO);
        final List<PrintRecordReadDO> printRecordsToCache = Arrays.asList(printRecordReadDO);

        // Configure ItemClientService.selectSpuItems(...).
        final ItemSpuReqDTO itemSpuReqDTO = new ItemSpuReqDTO();
        itemSpuReqDTO.setStoreGuid("storeGuid");
        itemSpuReqDTO.setItemGuids(Arrays.asList("value"));
        when(mockItemClientService.selectSpuItems(itemSpuReqDTO)).thenReturn(Arrays.asList("value"));

        when(mockDistributedService.nextBatchPrintRecordGuid(0L)).thenReturn(Arrays.asList("value"));

        // Configure PrinterFormatService.query(...).
        final OrderItemFormatDTO orderItemFormatDTO = new OrderItemFormatDTO();
        final FormatMetadata invoiceName = new FormatMetadata();
        invoiceName.setSize(0);
        invoiceName.setEnable(false);
        orderItemFormatDTO.setInvoiceName(invoiceName);
        final FormatMetadata foodFinishBarCode = new FormatMetadata();
        foodFinishBarCode.setSize(0);
        foodFinishBarCode.setEnable(false);
        orderItemFormatDTO.setFoodFinishBarCode(foodFinishBarCode);
        when(mockPrinterFormatService.query("storeGuid", 0)).thenReturn(orderItemFormatDTO);

        when(mockRedissonSingleClient.getBucket(eq("s"), any(Codec.class))).thenReturn(null);

        // Configure ItemClientService.listItemInfoBySalesModelNew(...).
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setStoreGuid("storeGuid");
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        when(mockItemClientService.listItemInfoBySalesModelNew(itemStringListDTO)).thenReturn(Collections.emptyList());

        // Configure ItemClientService.queryParentItemGuidByItem(...).
        final ItemStringListDTO query = new ItemStringListDTO();
        query.setStoreGuid("storeGuid");
        query.setDataList(Arrays.asList("value"));
        query.setItemList(Arrays.asList("value"));
        query.setRecordId(0L);
        when(mockItemClientService.queryParentItemGuidByItem(query)).thenReturn(new HashMap<>());

        // Configure PrintTypeTemplateService.queryByStoreAndInvoiceType(...).
        final PrintTypeTemplateDetailDTO printTypeTemplateDetailDTO = new PrintTypeTemplateDetailDTO();
        final TemplateTypeVO templateTypeVO = new TemplateTypeVO();
        templateTypeVO.setTypeGuid("0");
        templateTypeVO.setTypeName("其他");
        final TemplateItemVO templateItemVO = new TemplateItemVO();
        templateItemVO.setItemGuid("itemGuid");
        templateTypeVO.setItemList(Arrays.asList(templateItemVO));
        printTypeTemplateDetailDTO.setTypeList(Arrays.asList(templateTypeVO));
        final TemplateDetailQO query1 = new TemplateDetailQO();
        query1.setStoreGuid("storeGuid");
        query1.setInvoiceType(0);
        query1.setItemGuidList(Arrays.asList("value"));
        when(mockPrintTypeTemplateService.queryByStoreAndInvoiceType(query1)).thenReturn(printTypeTemplateDetailDTO);

        // Run the test
        printRecordCloneServiceImplUnderTest.cloneRecord(printDTO, printers, printRecordsToInsert, printRecordsToCache);

        // Verify the results
    }
}
