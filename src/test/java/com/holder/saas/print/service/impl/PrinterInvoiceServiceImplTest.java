package com.holder.saas.print.service.impl;

import com.holder.saas.print.entity.domain.PrinterInvoiceDO;
import com.holder.saas.print.mapper.PrinterInvoiceMapper;
import com.holder.saas.print.mapstruct.PrinterRawMaptstruct;
import com.holder.saas.print.service.DistributedService;
import com.holderzone.saas.store.dto.print.PrinterDTO;
import com.holderzone.saas.store.dto.print.PrinterInvoiceDTO;
import com.holderzone.saas.store.dto.print.raw.PrinterInvoiceRawDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PrinterInvoiceServiceImplTest {

    @Mock
    private DistributedService mockDistributedService;
    @Mock
    private PrinterRawMaptstruct mockPrinterRawMaptstruct;

    private PrinterInvoiceServiceImpl printerInvoiceServiceImplUnderTest;
    @Mock
    private  PrinterInvoiceMapper printerInvoiceMapper;


    @Before
    public void setUp() {
        printerInvoiceServiceImplUnderTest = new PrinterInvoiceServiceImpl(mockDistributedService,
                mockPrinterRawMaptstruct,printerInvoiceMapper);
    }

    @Test
    public void testBindPrinterInvoice() {
        // Setup
        final PrinterDTO printerDTO = new PrinterDTO();
        printerDTO.setStoreGuid("storeGuid");
        printerDTO.setPrinterGuid("printerGuid");
        final PrinterInvoiceDTO printerInvoiceDTO = new PrinterInvoiceDTO();
        printerInvoiceDTO.setInvoiceType(0);
        printerInvoiceDTO.setInvoiceName("invoiceName");
        printerInvoiceDTO.setPrintCount(0);
        printerDTO.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO));

        when(mockDistributedService.nextBatchPrinterInvoiceGuid(0L)).thenReturn(Arrays.asList("value"));

        // Run the test
        printerInvoiceServiceImplUnderTest.bindPrinterInvoice(printerDTO);

        // Verify the results
    }

    @Test
    public void testBindPrinterInvoice_DistributedServiceReturnsNoItems() {
        // Setup
        final PrinterDTO printerDTO = new PrinterDTO();
        printerDTO.setStoreGuid("storeGuid");
        printerDTO.setPrinterGuid("printerGuid");
        final PrinterInvoiceDTO printerInvoiceDTO = new PrinterInvoiceDTO();
        printerInvoiceDTO.setInvoiceType(0);
        printerInvoiceDTO.setInvoiceName("invoiceName");
        printerInvoiceDTO.setPrintCount(0);
        printerDTO.setArrayOfInvoiceDTO(Arrays.asList(printerInvoiceDTO));

        when(mockDistributedService.nextBatchPrinterInvoiceGuid(0L)).thenReturn(Collections.emptyList());

        // Run the test
        printerInvoiceServiceImplUnderTest.bindPrinterInvoice(printerDTO);

        // Verify the results
    }

    @Test
    public void testDeletePrinterInvoice() {
        // Setup
        // Run the test
        printerInvoiceServiceImplUnderTest.deletePrinterInvoice("printerGuid");

        // Verify the results
    }

    @Test
    public void testBatchDeletePrinterInvoice() {
        // Setup
        // Run the test
        printerInvoiceServiceImplUnderTest.batchDeletePrinterInvoice(Arrays.asList("value"));

        // Verify the results
    }

    @Test
    public void testDeleteStorePrinterInvoice() {
        // Setup
        // Run the test
        printerInvoiceServiceImplUnderTest.deleteStorePrinterInvoice("storeGuid");

        // Verify the results
    }

    @Test
    public void testListRaw() {
        // Setup
        final PrinterInvoiceRawDTO printerInvoiceRawDTO = new PrinterInvoiceRawDTO();
        printerInvoiceRawDTO.setGuid("ff429a9e-6d3f-408a-b7ab-1bc622d76e7c");
        printerInvoiceRawDTO.setStoreGuid("storeGuid");
        printerInvoiceRawDTO.setPrinterGuid("printerGuid");
        printerInvoiceRawDTO.setInvoiceType(0);
        printerInvoiceRawDTO.setInvoiceName("invoiceName");
        final List<PrinterInvoiceRawDTO> expectedResult = Arrays.asList(printerInvoiceRawDTO);

        // Configure PrinterRawMaptstruct.toInvoiceRawDTO(...).
        final PrinterInvoiceRawDTO printerInvoiceRawDTO1 = new PrinterInvoiceRawDTO();
        printerInvoiceRawDTO1.setGuid("ff429a9e-6d3f-408a-b7ab-1bc622d76e7c");
        printerInvoiceRawDTO1.setStoreGuid("storeGuid");
        printerInvoiceRawDTO1.setPrinterGuid("printerGuid");
        printerInvoiceRawDTO1.setInvoiceType(0);
        printerInvoiceRawDTO1.setInvoiceName("invoiceName");
        final List<PrinterInvoiceRawDTO> printerInvoiceRawDTOS = Arrays.asList(printerInvoiceRawDTO1);
        final PrinterInvoiceDO printerInvoiceDO = new PrinterInvoiceDO();
        printerInvoiceDO.setGuid("d941efde-d9d5-448d-8eae-5fd95c3e55cc");
        printerInvoiceDO.setStoreGuid("storeGuid");
        printerInvoiceDO.setPrinterGuid("printerGuid");
        printerInvoiceDO.setInvoiceType(0);
        printerInvoiceDO.setInvoiceName("invoiceName");
        printerInvoiceDO.setPrintCount(0);
        final List<PrinterInvoiceDO> list = Arrays.asList(printerInvoiceDO);
        when(mockPrinterRawMaptstruct.toInvoiceRawDTO(list)).thenReturn(printerInvoiceRawDTOS);

        // Run the test
        final List<PrinterInvoiceRawDTO> result = printerInvoiceServiceImplUnderTest.listRaw("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testListRaw_PrinterRawMaptstructReturnsNoItems() {
        // Setup
        // Configure PrinterRawMaptstruct.toInvoiceRawDTO(...).
        final PrinterInvoiceDO printerInvoiceDO = new PrinterInvoiceDO();
        printerInvoiceDO.setGuid("d941efde-d9d5-448d-8eae-5fd95c3e55cc");
        printerInvoiceDO.setStoreGuid("storeGuid");
        printerInvoiceDO.setPrinterGuid("printerGuid");
        printerInvoiceDO.setInvoiceType(0);
        printerInvoiceDO.setInvoiceName("invoiceName");
        printerInvoiceDO.setPrintCount(0);
        final List<PrinterInvoiceDO> list = Arrays.asList(printerInvoiceDO);
        when(mockPrinterRawMaptstruct.toInvoiceRawDTO(list)).thenReturn(Collections.emptyList());

        // Run the test
        final List<PrinterInvoiceRawDTO> result = printerInvoiceServiceImplUnderTest.listRaw("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }
}
