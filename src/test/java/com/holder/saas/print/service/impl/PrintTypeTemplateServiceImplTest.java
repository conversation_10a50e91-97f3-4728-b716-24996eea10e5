package com.holder.saas.print.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holder.saas.print.entity.domain.type.PrintTypeTemplateDO;
import com.holder.saas.print.entity.domain.type.PrintTypeTemplateRItemDO;
import com.holder.saas.print.entity.domain.type.PrintTypeTemplateRStoreDO;
import com.holder.saas.print.entity.domain.type.PrintTypeTemplateRTypeDO;
import com.holder.saas.print.mapper.PrintTypeTemplateMapper;
import com.holder.saas.print.service.DistributedService;
import com.holder.saas.print.service.PrintTypeTemplateRItemService;
import com.holder.saas.print.service.PrintTypeTemplateRStoreService;
import com.holder.saas.print.service.PrintTypeTemplateRTypeService;
import com.holder.saas.print.service.feign.StoreDeviceFeignService;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.item.req.SingleDataPageDTO;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.print.type.*;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PrintTypeTemplateServiceImplTest {

    @Mock
    private PrintTypeTemplateMapper mockPrintTypeTemplateMapper;
    @Mock
    private DistributedService mockDistributedService;
    @Mock
    private PrintTypeTemplateRTypeService mockTemplateRTypeService;
    @Mock
    private PrintTypeTemplateRItemService mockTemplateRItemService;
    @Mock
    private PrintTypeTemplateRStoreService mockTemplateRStoreService;
    @Mock
    private StoreDeviceFeignService mockStoreDeviceFeignService;

    private PrintTypeTemplateServiceImpl printTypeTemplateServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        printTypeTemplateServiceImplUnderTest = new PrintTypeTemplateServiceImpl(mockPrintTypeTemplateMapper,
                mockDistributedService, mockTemplateRTypeService, mockTemplateRItemService, mockTemplateRStoreService,
                mockStoreDeviceFeignService);
    }

    @Test
    public void testCreate() {
        // Setup
        final PrintTypeTemplateDTO createDTO = new PrintTypeTemplateDTO();
        createDTO.setGuid("b6e704b6-96f8-4079-a88e-518293d0f39f");
        createDTO.setBrandGuid("brandGuid");
        createDTO.setName("name");
        final TemplateTypeDTO templateTypeDTO = new TemplateTypeDTO();
        templateTypeDTO.setName("name");
        templateTypeDTO.setItemGuidList(Arrays.asList(new TemplateItemQO()));
        createDTO.setTypeDTOList(Arrays.asList(templateTypeDTO));
        createDTO.setInvoiceType(Arrays.asList("value"));
        createDTO.setIsAllStore(false);
        createDTO.setStoreGuidList(Arrays.asList("value"));

        when(mockTemplateRStoreService.queryRepeatTemplateCount("brandGuid", Arrays.asList("value"),
                Arrays.asList("value"), "b6e704b6-96f8-4079-a88e-518293d0f39f")).thenReturn(0);
        when(mockPrintTypeTemplateMapper.queryRepeatTemplateCount("brandGuid", Arrays.asList("value"),
                "b6e704b6-96f8-4079-a88e-518293d0f39f")).thenReturn(0);
        when(mockPrintTypeTemplateMapper.checkTemplateName("brandGuid", "name",
                "b6e704b6-96f8-4079-a88e-518293d0f39f")).thenReturn(0);
        when(mockDistributedService.nextPrintTypeTemplateGuid()).thenReturn("b6e704b6-96f8-4079-a88e-518293d0f39f");
        when(mockDistributedService.nextBatchTemplateRTypeGuid(0L)).thenReturn(Arrays.asList("value"));

        // Run the test
        printTypeTemplateServiceImplUnderTest.create(createDTO);

        // Verify the results
        // Confirm PrintTypeTemplateMapper.insert(...).
        final PrintTypeTemplateDO entity = new PrintTypeTemplateDO();
        entity.setId(0L);
        entity.setGuid("b6e704b6-96f8-4079-a88e-518293d0f39f");
        entity.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setIsDelete(false);
        entity.setIsEnable(false);
        entity.setBrandGuid("brandGuid");
        entity.setName("name");
        entity.setInvoiceType("invoiceType");
        entity.setIsAllStore(false);
        verify(mockPrintTypeTemplateMapper).insert(entity);

        // Confirm PrintTypeTemplateRTypeService.saveBatch(...).
        final PrintTypeTemplateRTypeDO printTypeTemplateRTypeDO = new PrintTypeTemplateRTypeDO();
        printTypeTemplateRTypeDO.setId(0L);
        printTypeTemplateRTypeDO.setGuid("e9bc6879-934f-4b49-b475-9e924d48a5fe");
        printTypeTemplateRTypeDO.setTemplateGuid("b6e704b6-96f8-4079-a88e-518293d0f39f");
        printTypeTemplateRTypeDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        printTypeTemplateRTypeDO.setName("name");
        final List<PrintTypeTemplateRTypeDO> entityList = Arrays.asList(printTypeTemplateRTypeDO);
        verify(mockTemplateRTypeService).saveBatch(entityList);

        // Confirm PrintTypeTemplateRItemService.saveBatch(...).
        final PrintTypeTemplateRItemDO printTypeTemplateRItemDO = new PrintTypeTemplateRItemDO();
        printTypeTemplateRItemDO.setId(0L);
        printTypeTemplateRItemDO.setTypeGuid("typeGuid");
        printTypeTemplateRItemDO.setItemGuid("itemGuid");
        printTypeTemplateRItemDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        printTypeTemplateRItemDO.setTemplateGuid("b6e704b6-96f8-4079-a88e-518293d0f39f");
        final List<PrintTypeTemplateRItemDO> entityList1 = Arrays.asList(printTypeTemplateRItemDO);
        verify(mockTemplateRItemService).saveBatch(entityList1);

        // Confirm PrintTypeTemplateRStoreService.saveBatch(...).
        final PrintTypeTemplateRStoreDO printTypeTemplateRStoreDO = new PrintTypeTemplateRStoreDO();
        printTypeTemplateRStoreDO.setId(0L);
        printTypeTemplateRStoreDO.setTemplateGuid("b6e704b6-96f8-4079-a88e-518293d0f39f");
        printTypeTemplateRStoreDO.setStoreGuid("storeGuid");
        printTypeTemplateRStoreDO.setIsDelete(false);
        printTypeTemplateRStoreDO.setBrandGuid("brandGuid");
        final List<PrintTypeTemplateRStoreDO> entityList2 = Arrays.asList(printTypeTemplateRStoreDO);
        verify(mockTemplateRStoreService).saveBatch(entityList2);
    }

    @Test
    public void testCreate_DistributedServiceNextBatchTemplateRTypeGuidReturnsNoItems() {
        // Setup
        final PrintTypeTemplateDTO createDTO = new PrintTypeTemplateDTO();
        createDTO.setGuid("b6e704b6-96f8-4079-a88e-518293d0f39f");
        createDTO.setBrandGuid("brandGuid");
        createDTO.setName("name");
        final TemplateTypeDTO templateTypeDTO = new TemplateTypeDTO();
        templateTypeDTO.setName("name");
        templateTypeDTO.setItemGuidList(Arrays.asList(new TemplateItemQO()));
        createDTO.setTypeDTOList(Arrays.asList(templateTypeDTO));
        createDTO.setInvoiceType(Arrays.asList("value"));
        createDTO.setIsAllStore(false);
        createDTO.setStoreGuidList(Arrays.asList("value"));

        when(mockTemplateRStoreService.queryRepeatTemplateCount("brandGuid", Arrays.asList("value"),
                Arrays.asList("value"), "b6e704b6-96f8-4079-a88e-518293d0f39f")).thenReturn(0);
        when(mockPrintTypeTemplateMapper.queryRepeatTemplateCount("brandGuid", Arrays.asList("value"),
                "b6e704b6-96f8-4079-a88e-518293d0f39f")).thenReturn(0);
        when(mockPrintTypeTemplateMapper.checkTemplateName("brandGuid", "name",
                "b6e704b6-96f8-4079-a88e-518293d0f39f")).thenReturn(0);
        when(mockDistributedService.nextPrintTypeTemplateGuid()).thenReturn("b6e704b6-96f8-4079-a88e-518293d0f39f");
        when(mockDistributedService.nextBatchTemplateRTypeGuid(0L)).thenReturn(Collections.emptyList());

        // Run the test
        printTypeTemplateServiceImplUnderTest.create(createDTO);

        // Verify the results
        // Confirm PrintTypeTemplateMapper.insert(...).
        final PrintTypeTemplateDO entity = new PrintTypeTemplateDO();
        entity.setId(0L);
        entity.setGuid("b6e704b6-96f8-4079-a88e-518293d0f39f");
        entity.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setIsDelete(false);
        entity.setIsEnable(false);
        entity.setBrandGuid("brandGuid");
        entity.setName("name");
        entity.setInvoiceType("invoiceType");
        entity.setIsAllStore(false);
        verify(mockPrintTypeTemplateMapper).insert(entity);

        // Confirm PrintTypeTemplateRTypeService.saveBatch(...).
        final PrintTypeTemplateRTypeDO printTypeTemplateRTypeDO = new PrintTypeTemplateRTypeDO();
        printTypeTemplateRTypeDO.setId(0L);
        printTypeTemplateRTypeDO.setGuid("e9bc6879-934f-4b49-b475-9e924d48a5fe");
        printTypeTemplateRTypeDO.setTemplateGuid("b6e704b6-96f8-4079-a88e-518293d0f39f");
        printTypeTemplateRTypeDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        printTypeTemplateRTypeDO.setName("name");
        final List<PrintTypeTemplateRTypeDO> entityList = Arrays.asList(printTypeTemplateRTypeDO);
        verify(mockTemplateRTypeService).saveBatch(entityList);

        // Confirm PrintTypeTemplateRItemService.saveBatch(...).
        final PrintTypeTemplateRItemDO printTypeTemplateRItemDO = new PrintTypeTemplateRItemDO();
        printTypeTemplateRItemDO.setId(0L);
        printTypeTemplateRItemDO.setTypeGuid("typeGuid");
        printTypeTemplateRItemDO.setItemGuid("itemGuid");
        printTypeTemplateRItemDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        printTypeTemplateRItemDO.setTemplateGuid("b6e704b6-96f8-4079-a88e-518293d0f39f");
        final List<PrintTypeTemplateRItemDO> entityList1 = Arrays.asList(printTypeTemplateRItemDO);
        verify(mockTemplateRItemService).saveBatch(entityList1);

        // Confirm PrintTypeTemplateRStoreService.saveBatch(...).
        final PrintTypeTemplateRStoreDO printTypeTemplateRStoreDO = new PrintTypeTemplateRStoreDO();
        printTypeTemplateRStoreDO.setId(0L);
        printTypeTemplateRStoreDO.setTemplateGuid("b6e704b6-96f8-4079-a88e-518293d0f39f");
        printTypeTemplateRStoreDO.setStoreGuid("storeGuid");
        printTypeTemplateRStoreDO.setIsDelete(false);
        printTypeTemplateRStoreDO.setBrandGuid("brandGuid");
        final List<PrintTypeTemplateRStoreDO> entityList2 = Arrays.asList(printTypeTemplateRStoreDO);
        verify(mockTemplateRStoreService).saveBatch(entityList2);
    }

    @Test
    public void testQueryPage() {
        // Setup
        final SingleDataPageDTO query = new SingleDataPageDTO();
        query.setCurrentPage(0L);
        query.setPageSize(0L);
        query.setData("data");

        // Configure PrintTypeTemplateMapper.queryPage(...).
        final PrintTypeTemplateDO printTypeTemplateDO = new PrintTypeTemplateDO();
        printTypeTemplateDO.setId(0L);
        printTypeTemplateDO.setGuid("b6e704b6-96f8-4079-a88e-518293d0f39f");
        printTypeTemplateDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        printTypeTemplateDO.setIsDelete(false);
        printTypeTemplateDO.setIsEnable(false);
        printTypeTemplateDO.setBrandGuid("brandGuid");
        printTypeTemplateDO.setName("name");
        printTypeTemplateDO.setInvoiceType("invoiceType");
        printTypeTemplateDO.setIsAllStore(false);
        final List<PrintTypeTemplateDO> printTypeTemplateDOS = Arrays.asList(printTypeTemplateDO);
        final SingleDataPageDTO query1 = new SingleDataPageDTO();
        query1.setCurrentPage(0L);
        query1.setPageSize(0L);
        query1.setData("data");
        when(mockPrintTypeTemplateMapper.queryPage(query1)).thenReturn(printTypeTemplateDOS);

        // Configure PrintTypeTemplateRStoreService.list(...).
        final PrintTypeTemplateRStoreDO printTypeTemplateRStoreDO = new PrintTypeTemplateRStoreDO();
        printTypeTemplateRStoreDO.setId(0L);
        printTypeTemplateRStoreDO.setTemplateGuid("b6e704b6-96f8-4079-a88e-518293d0f39f");
        printTypeTemplateRStoreDO.setStoreGuid("storeGuid");
        printTypeTemplateRStoreDO.setIsDelete(false);
        printTypeTemplateRStoreDO.setBrandGuid("brandGuid");
        final List<PrintTypeTemplateRStoreDO> printTypeTemplateRStoreDOS = Arrays.asList(printTypeTemplateRStoreDO);
        when(mockTemplateRStoreService.list(any(LambdaQueryWrapper.class))).thenReturn(printTypeTemplateRStoreDOS);

        // Configure StoreDeviceFeignService.queryStoreByIdList(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("215ab543-2907-403a-8e9f-e4638e3e2d87");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO);
        when(mockStoreDeviceFeignService.queryStoreByIdList(
                new SingleDataDTO("data", Arrays.asList("value")))).thenReturn(storeDTOS);

        // Run the test
        final Page<PrintTypeTemplateVO> result = printTypeTemplateServiceImplUnderTest.queryPage(query);

        // Verify the results
    }

    @Test
    public void testQueryPage_PrintTypeTemplateMapperReturnsNoItems() {
        // Setup
        final SingleDataPageDTO query = new SingleDataPageDTO();
        query.setCurrentPage(0L);
        query.setPageSize(0L);
        query.setData("data");

        // Configure PrintTypeTemplateMapper.queryPage(...).
        final SingleDataPageDTO query1 = new SingleDataPageDTO();
        query1.setCurrentPage(0L);
        query1.setPageSize(0L);
        query1.setData("data");
        when(mockPrintTypeTemplateMapper.queryPage(query1)).thenReturn(Collections.emptyList());

        // Run the test
        final Page<PrintTypeTemplateVO> result = printTypeTemplateServiceImplUnderTest.queryPage(query);

        // Verify the results
    }

    @Test
    public void testQueryPage_PrintTypeTemplateRStoreServiceReturnsNoItems() {
        // Setup
        final SingleDataPageDTO query = new SingleDataPageDTO();
        query.setCurrentPage(0L);
        query.setPageSize(0L);
        query.setData("data");

        // Configure PrintTypeTemplateMapper.queryPage(...).
        final PrintTypeTemplateDO printTypeTemplateDO = new PrintTypeTemplateDO();
        printTypeTemplateDO.setId(0L);
        printTypeTemplateDO.setGuid("b6e704b6-96f8-4079-a88e-518293d0f39f");
        printTypeTemplateDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        printTypeTemplateDO.setIsDelete(false);
        printTypeTemplateDO.setIsEnable(false);
        printTypeTemplateDO.setBrandGuid("brandGuid");
        printTypeTemplateDO.setName("name");
        printTypeTemplateDO.setInvoiceType("invoiceType");
        printTypeTemplateDO.setIsAllStore(false);
        final List<PrintTypeTemplateDO> printTypeTemplateDOS = Arrays.asList(printTypeTemplateDO);
        final SingleDataPageDTO query1 = new SingleDataPageDTO();
        query1.setCurrentPage(0L);
        query1.setPageSize(0L);
        query1.setData("data");
        when(mockPrintTypeTemplateMapper.queryPage(query1)).thenReturn(printTypeTemplateDOS);

        when(mockTemplateRStoreService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final Page<PrintTypeTemplateVO> result = printTypeTemplateServiceImplUnderTest.queryPage(query);

        // Verify the results
    }

    @Test
    public void testQueryPage_StoreDeviceFeignServiceReturnsNoItems() {
        // Setup
        final SingleDataPageDTO query = new SingleDataPageDTO();
        query.setCurrentPage(0L);
        query.setPageSize(0L);
        query.setData("data");

        // Configure PrintTypeTemplateMapper.queryPage(...).
        final PrintTypeTemplateDO printTypeTemplateDO = new PrintTypeTemplateDO();
        printTypeTemplateDO.setId(0L);
        printTypeTemplateDO.setGuid("b6e704b6-96f8-4079-a88e-518293d0f39f");
        printTypeTemplateDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        printTypeTemplateDO.setIsDelete(false);
        printTypeTemplateDO.setIsEnable(false);
        printTypeTemplateDO.setBrandGuid("brandGuid");
        printTypeTemplateDO.setName("name");
        printTypeTemplateDO.setInvoiceType("invoiceType");
        printTypeTemplateDO.setIsAllStore(false);
        final List<PrintTypeTemplateDO> printTypeTemplateDOS = Arrays.asList(printTypeTemplateDO);
        final SingleDataPageDTO query1 = new SingleDataPageDTO();
        query1.setCurrentPage(0L);
        query1.setPageSize(0L);
        query1.setData("data");
        when(mockPrintTypeTemplateMapper.queryPage(query1)).thenReturn(printTypeTemplateDOS);

        // Configure PrintTypeTemplateRStoreService.list(...).
        final PrintTypeTemplateRStoreDO printTypeTemplateRStoreDO = new PrintTypeTemplateRStoreDO();
        printTypeTemplateRStoreDO.setId(0L);
        printTypeTemplateRStoreDO.setTemplateGuid("b6e704b6-96f8-4079-a88e-518293d0f39f");
        printTypeTemplateRStoreDO.setStoreGuid("storeGuid");
        printTypeTemplateRStoreDO.setIsDelete(false);
        printTypeTemplateRStoreDO.setBrandGuid("brandGuid");
        final List<PrintTypeTemplateRStoreDO> printTypeTemplateRStoreDOS = Arrays.asList(printTypeTemplateRStoreDO);
        when(mockTemplateRStoreService.list(any(LambdaQueryWrapper.class))).thenReturn(printTypeTemplateRStoreDOS);

        when(mockStoreDeviceFeignService.queryStoreByIdList(
                new SingleDataDTO("data", Arrays.asList("value")))).thenReturn(Collections.emptyList());

        // Run the test
        final Page<PrintTypeTemplateVO> result = printTypeTemplateServiceImplUnderTest.queryPage(query);

        // Verify the results
    }

    @Test
    public void testQueryDetail() {
        // Setup
        final SingleDataDTO query = new SingleDataDTO("data", Arrays.asList("value"));
        final PrintTypeTemplateDetailDTO expectedResult = new PrintTypeTemplateDetailDTO();
        expectedResult.setGuid("b6e704b6-96f8-4079-a88e-518293d0f39f");
        expectedResult.setName("name");
        final TemplateTypeVO templateTypeVO = new TemplateTypeVO();
        expectedResult.setTypeList(Arrays.asList(templateTypeVO));
        expectedResult.setInvoiceType(Arrays.asList("value"));
        expectedResult.setIsAllStore(false);
        final TemplateStoreVO templateStoreVO = new TemplateStoreVO();
        expectedResult.setStoreList(Arrays.asList(templateStoreVO));

        // Configure PrintTypeTemplateRTypeService.queryTemplateTypeVOList(...).
        final TemplateTypeVO templateTypeVO1 = new TemplateTypeVO();
        templateTypeVO1.setTypeGuid("typeGuid");
        templateTypeVO1.setTypeName("typeName");
        final TemplateItemVO templateItemVO = new TemplateItemVO();
        templateItemVO.setItemGuid("itemGuid");
        templateItemVO.setName("name");
        templateTypeVO1.setItemList(Arrays.asList(templateItemVO));
        final List<TemplateTypeVO> templateTypeVOS = Arrays.asList(templateTypeVO1);
        when(mockTemplateRTypeService.queryTemplateTypeVOList("data", Arrays.asList("value")))
                .thenReturn(templateTypeVOS);

        // Configure PrintTypeTemplateRStoreService.queryTemplateStoreVOList(...).
        final TemplateStoreVO templateStoreVO1 = new TemplateStoreVO();
        templateStoreVO1.setGuid("f48caae9-cfc1-4ad9-83b5-eb7ba19d5c1d");
        templateStoreVO1.setName("name");
        final List<TemplateStoreVO> templateStoreVOS = Arrays.asList(templateStoreVO1);
        when(mockTemplateRStoreService.queryTemplateStoreVOList(false, "data")).thenReturn(templateStoreVOS);

        // Run the test
        final PrintTypeTemplateDetailDTO result = printTypeTemplateServiceImplUnderTest.queryDetail(query);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryDetail_PrintTypeTemplateRTypeServiceReturnsNoItems() {
        // Setup
        final SingleDataDTO query = new SingleDataDTO("data", Arrays.asList("value"));
        final PrintTypeTemplateDetailDTO expectedResult = new PrintTypeTemplateDetailDTO();
        expectedResult.setGuid("b6e704b6-96f8-4079-a88e-518293d0f39f");
        expectedResult.setName("name");
        final TemplateTypeVO templateTypeVO = new TemplateTypeVO();
        expectedResult.setTypeList(Arrays.asList(templateTypeVO));
        expectedResult.setInvoiceType(Arrays.asList("value"));
        expectedResult.setIsAllStore(false);
        final TemplateStoreVO templateStoreVO = new TemplateStoreVO();
        expectedResult.setStoreList(Arrays.asList(templateStoreVO));

        when(mockTemplateRTypeService.queryTemplateTypeVOList("data", Arrays.asList("value")))
                .thenReturn(Collections.emptyList());

        // Configure PrintTypeTemplateRStoreService.queryTemplateStoreVOList(...).
        final TemplateStoreVO templateStoreVO1 = new TemplateStoreVO();
        templateStoreVO1.setGuid("f48caae9-cfc1-4ad9-83b5-eb7ba19d5c1d");
        templateStoreVO1.setName("name");
        final List<TemplateStoreVO> templateStoreVOS = Arrays.asList(templateStoreVO1);
        when(mockTemplateRStoreService.queryTemplateStoreVOList(false, "data")).thenReturn(templateStoreVOS);

        // Run the test
        final PrintTypeTemplateDetailDTO result = printTypeTemplateServiceImplUnderTest.queryDetail(query);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryDetail_PrintTypeTemplateRStoreServiceReturnsNoItems() {
        // Setup
        final SingleDataDTO query = new SingleDataDTO("data", Arrays.asList("value"));
        final PrintTypeTemplateDetailDTO expectedResult = new PrintTypeTemplateDetailDTO();
        expectedResult.setGuid("b6e704b6-96f8-4079-a88e-518293d0f39f");
        expectedResult.setName("name");
        final TemplateTypeVO templateTypeVO = new TemplateTypeVO();
        expectedResult.setTypeList(Arrays.asList(templateTypeVO));
        expectedResult.setInvoiceType(Arrays.asList("value"));
        expectedResult.setIsAllStore(false);
        final TemplateStoreVO templateStoreVO = new TemplateStoreVO();
        expectedResult.setStoreList(Arrays.asList(templateStoreVO));

        // Configure PrintTypeTemplateRTypeService.queryTemplateTypeVOList(...).
        final TemplateTypeVO templateTypeVO1 = new TemplateTypeVO();
        templateTypeVO1.setTypeGuid("typeGuid");
        templateTypeVO1.setTypeName("typeName");
        final TemplateItemVO templateItemVO = new TemplateItemVO();
        templateItemVO.setItemGuid("itemGuid");
        templateItemVO.setName("name");
        templateTypeVO1.setItemList(Arrays.asList(templateItemVO));
        final List<TemplateTypeVO> templateTypeVOS = Arrays.asList(templateTypeVO1);
        when(mockTemplateRTypeService.queryTemplateTypeVOList("data", Arrays.asList("value")))
                .thenReturn(templateTypeVOS);

        when(mockTemplateRStoreService.queryTemplateStoreVOList(false, "data")).thenReturn(Collections.emptyList());

        // Run the test
        final PrintTypeTemplateDetailDTO result = printTypeTemplateServiceImplUnderTest.queryDetail(query);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testModify() {
        // Setup
        final PrintTypeTemplateDTO modifyDTO = new PrintTypeTemplateDTO();
        modifyDTO.setGuid("b6e704b6-96f8-4079-a88e-518293d0f39f");
        modifyDTO.setBrandGuid("brandGuid");
        modifyDTO.setName("name");
        final TemplateTypeDTO templateTypeDTO = new TemplateTypeDTO();
        templateTypeDTO.setName("name");
        templateTypeDTO.setItemGuidList(Arrays.asList(new TemplateItemQO()));
        modifyDTO.setTypeDTOList(Arrays.asList(templateTypeDTO));
        modifyDTO.setInvoiceType(Arrays.asList("value"));
        modifyDTO.setIsAllStore(false);
        modifyDTO.setStoreGuidList(Arrays.asList("value"));

        when(mockTemplateRStoreService.queryRepeatTemplateCount("brandGuid", Arrays.asList("value"),
                Arrays.asList("value"), "b6e704b6-96f8-4079-a88e-518293d0f39f")).thenReturn(0);
        when(mockPrintTypeTemplateMapper.queryRepeatTemplateCount("brandGuid", Arrays.asList("value"),
                "b6e704b6-96f8-4079-a88e-518293d0f39f")).thenReturn(0);
        when(mockPrintTypeTemplateMapper.checkTemplateName("brandGuid", "name",
                "b6e704b6-96f8-4079-a88e-518293d0f39f")).thenReturn(0);
        when(mockDistributedService.nextBatchTemplateRTypeGuid(0L)).thenReturn(Arrays.asList("value"));

        // Run the test
        printTypeTemplateServiceImplUnderTest.modify(modifyDTO);

        // Verify the results
        // Confirm PrintTypeTemplateMapper.updateById(...).
        final PrintTypeTemplateDO entity = new PrintTypeTemplateDO();
        entity.setId(0L);
        entity.setGuid("b6e704b6-96f8-4079-a88e-518293d0f39f");
        entity.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setIsDelete(false);
        entity.setIsEnable(false);
        entity.setBrandGuid("brandGuid");
        entity.setName("name");
        entity.setInvoiceType("invoiceType");
        entity.setIsAllStore(false);
        verify(mockPrintTypeTemplateMapper).updateById(entity);
        verify(mockTemplateRTypeService).removeByTemplateGuid("b6e704b6-96f8-4079-a88e-518293d0f39f");

        // Confirm PrintTypeTemplateRTypeService.saveBatch(...).
        final PrintTypeTemplateRTypeDO printTypeTemplateRTypeDO = new PrintTypeTemplateRTypeDO();
        printTypeTemplateRTypeDO.setId(0L);
        printTypeTemplateRTypeDO.setGuid("e9bc6879-934f-4b49-b475-9e924d48a5fe");
        printTypeTemplateRTypeDO.setTemplateGuid("b6e704b6-96f8-4079-a88e-518293d0f39f");
        printTypeTemplateRTypeDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        printTypeTemplateRTypeDO.setName("name");
        final List<PrintTypeTemplateRTypeDO> entityList = Arrays.asList(printTypeTemplateRTypeDO);
        verify(mockTemplateRTypeService).saveBatch(entityList);
        verify(mockTemplateRItemService).removeByTemplateGuid("b6e704b6-96f8-4079-a88e-518293d0f39f");

        // Confirm PrintTypeTemplateRItemService.saveBatch(...).
        final PrintTypeTemplateRItemDO printTypeTemplateRItemDO = new PrintTypeTemplateRItemDO();
        printTypeTemplateRItemDO.setId(0L);
        printTypeTemplateRItemDO.setTypeGuid("typeGuid");
        printTypeTemplateRItemDO.setItemGuid("itemGuid");
        printTypeTemplateRItemDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        printTypeTemplateRItemDO.setTemplateGuid("b6e704b6-96f8-4079-a88e-518293d0f39f");
        final List<PrintTypeTemplateRItemDO> entityList1 = Arrays.asList(printTypeTemplateRItemDO);
        verify(mockTemplateRItemService).saveBatch(entityList1);
        verify(mockTemplateRStoreService).removeByTemplateGuid("b6e704b6-96f8-4079-a88e-518293d0f39f");

        // Confirm PrintTypeTemplateRStoreService.saveBatch(...).
        final PrintTypeTemplateRStoreDO printTypeTemplateRStoreDO = new PrintTypeTemplateRStoreDO();
        printTypeTemplateRStoreDO.setId(0L);
        printTypeTemplateRStoreDO.setTemplateGuid("b6e704b6-96f8-4079-a88e-518293d0f39f");
        printTypeTemplateRStoreDO.setStoreGuid("storeGuid");
        printTypeTemplateRStoreDO.setIsDelete(false);
        printTypeTemplateRStoreDO.setBrandGuid("brandGuid");
        final List<PrintTypeTemplateRStoreDO> entityList2 = Arrays.asList(printTypeTemplateRStoreDO);
        verify(mockTemplateRStoreService).saveBatch(entityList2);
    }

    @Test
    public void testModify_DistributedServiceReturnsNoItems() {
        // Setup
        final PrintTypeTemplateDTO modifyDTO = new PrintTypeTemplateDTO();
        modifyDTO.setGuid("b6e704b6-96f8-4079-a88e-518293d0f39f");
        modifyDTO.setBrandGuid("brandGuid");
        modifyDTO.setName("name");
        final TemplateTypeDTO templateTypeDTO = new TemplateTypeDTO();
        templateTypeDTO.setName("name");
        templateTypeDTO.setItemGuidList(Arrays.asList(new TemplateItemQO()));
        modifyDTO.setTypeDTOList(Arrays.asList(templateTypeDTO));
        modifyDTO.setInvoiceType(Arrays.asList("value"));
        modifyDTO.setIsAllStore(false);
        modifyDTO.setStoreGuidList(Arrays.asList("value"));

        when(mockTemplateRStoreService.queryRepeatTemplateCount("brandGuid", Arrays.asList("value"),
                Arrays.asList("value"), "b6e704b6-96f8-4079-a88e-518293d0f39f")).thenReturn(0);
        when(mockPrintTypeTemplateMapper.queryRepeatTemplateCount("brandGuid", Arrays.asList("value"),
                "b6e704b6-96f8-4079-a88e-518293d0f39f")).thenReturn(0);
        when(mockPrintTypeTemplateMapper.checkTemplateName("brandGuid", "name",
                "b6e704b6-96f8-4079-a88e-518293d0f39f")).thenReturn(0);
        when(mockDistributedService.nextBatchTemplateRTypeGuid(0L)).thenReturn(Collections.emptyList());

        // Run the test
        printTypeTemplateServiceImplUnderTest.modify(modifyDTO);

        // Verify the results
        // Confirm PrintTypeTemplateMapper.updateById(...).
        final PrintTypeTemplateDO entity = new PrintTypeTemplateDO();
        entity.setId(0L);
        entity.setGuid("b6e704b6-96f8-4079-a88e-518293d0f39f");
        entity.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setIsDelete(false);
        entity.setIsEnable(false);
        entity.setBrandGuid("brandGuid");
        entity.setName("name");
        entity.setInvoiceType("invoiceType");
        entity.setIsAllStore(false);
        verify(mockPrintTypeTemplateMapper).updateById(entity);
        verify(mockTemplateRTypeService).removeByTemplateGuid("b6e704b6-96f8-4079-a88e-518293d0f39f");

        // Confirm PrintTypeTemplateRTypeService.saveBatch(...).
        final PrintTypeTemplateRTypeDO printTypeTemplateRTypeDO = new PrintTypeTemplateRTypeDO();
        printTypeTemplateRTypeDO.setId(0L);
        printTypeTemplateRTypeDO.setGuid("e9bc6879-934f-4b49-b475-9e924d48a5fe");
        printTypeTemplateRTypeDO.setTemplateGuid("b6e704b6-96f8-4079-a88e-518293d0f39f");
        printTypeTemplateRTypeDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        printTypeTemplateRTypeDO.setName("name");
        final List<PrintTypeTemplateRTypeDO> entityList = Arrays.asList(printTypeTemplateRTypeDO);
        verify(mockTemplateRTypeService).saveBatch(entityList);
        verify(mockTemplateRItemService).removeByTemplateGuid("b6e704b6-96f8-4079-a88e-518293d0f39f");

        // Confirm PrintTypeTemplateRItemService.saveBatch(...).
        final PrintTypeTemplateRItemDO printTypeTemplateRItemDO = new PrintTypeTemplateRItemDO();
        printTypeTemplateRItemDO.setId(0L);
        printTypeTemplateRItemDO.setTypeGuid("typeGuid");
        printTypeTemplateRItemDO.setItemGuid("itemGuid");
        printTypeTemplateRItemDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        printTypeTemplateRItemDO.setTemplateGuid("b6e704b6-96f8-4079-a88e-518293d0f39f");
        final List<PrintTypeTemplateRItemDO> entityList1 = Arrays.asList(printTypeTemplateRItemDO);
        verify(mockTemplateRItemService).saveBatch(entityList1);
        verify(mockTemplateRStoreService).removeByTemplateGuid("b6e704b6-96f8-4079-a88e-518293d0f39f");

        // Confirm PrintTypeTemplateRStoreService.saveBatch(...).
        final PrintTypeTemplateRStoreDO printTypeTemplateRStoreDO = new PrintTypeTemplateRStoreDO();
        printTypeTemplateRStoreDO.setId(0L);
        printTypeTemplateRStoreDO.setTemplateGuid("b6e704b6-96f8-4079-a88e-518293d0f39f");
        printTypeTemplateRStoreDO.setStoreGuid("storeGuid");
        printTypeTemplateRStoreDO.setIsDelete(false);
        printTypeTemplateRStoreDO.setBrandGuid("brandGuid");
        final List<PrintTypeTemplateRStoreDO> entityList2 = Arrays.asList(printTypeTemplateRStoreDO);
        verify(mockTemplateRStoreService).saveBatch(entityList2);
    }

    @Test
    public void testEnable() {
        // Setup
        final PrintTypeTemplateEnableDTO enableDTO = new PrintTypeTemplateEnableDTO();
        enableDTO.setTemplateGuid("templateGuid");
        enableDTO.setIsEnable(false);

        // Run the test
        final boolean result = printTypeTemplateServiceImplUnderTest.enable(enableDTO);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testDelete() {
        // Setup
        final SingleDataDTO deleteDTO = new SingleDataDTO("data", Arrays.asList("value"));

        // Run the test
        final boolean result = printTypeTemplateServiceImplUnderTest.delete(deleteDTO);

        // Verify the results
        assertThat(result).isTrue();
        verify(mockPrintTypeTemplateMapper).deleteById(0L);
        verify(mockTemplateRTypeService).removeByTemplateGuid("data");
        verify(mockTemplateRItemService).removeByTemplateGuid("data");
        verify(mockTemplateRStoreService).removeByTemplateGuid("data");
    }

    @Test
    public void testQueryByStoreAndInvoiceType() {
        // Setup
        final TemplateDetailQO query = new TemplateDetailQO();
        query.setStoreGuid("storeGuid");
        query.setInvoiceType(0);
        query.setItemGuidList(Arrays.asList("value"));

        final PrintTypeTemplateDetailDTO expectedResult = new PrintTypeTemplateDetailDTO();
        expectedResult.setGuid("b6e704b6-96f8-4079-a88e-518293d0f39f");
        expectedResult.setName("name");
        final TemplateTypeVO templateTypeVO = new TemplateTypeVO();
        expectedResult.setTypeList(Arrays.asList(templateTypeVO));
        expectedResult.setInvoiceType(Arrays.asList("value"));
        expectedResult.setIsAllStore(false);
        final TemplateStoreVO templateStoreVO = new TemplateStoreVO();
        expectedResult.setStoreList(Arrays.asList(templateStoreVO));

        // Configure StoreDeviceFeignService.queryBrandByStoreGuid(...).
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("d19b26a7-5622-4b41-940e-4691b028af7e");
        brandDTO.setUuid("449042fd-4134-4456-b7b6-8066560936e9");
        brandDTO.setName("name");
        brandDTO.setDescription("description");
        brandDTO.setLogoUrl("logoUrl");
        when(mockStoreDeviceFeignService.queryBrandByStoreGuid("storeGuid")).thenReturn(brandDTO);

        // Configure PrintTypeTemplateRStoreService.queryTemplateByInvoiceTypeAndStoreGuid(...).
        final PrintTypeTemplateRStoreDO printTypeTemplateRStoreDO = new PrintTypeTemplateRStoreDO();
        printTypeTemplateRStoreDO.setId(0L);
        printTypeTemplateRStoreDO.setTemplateGuid("b6e704b6-96f8-4079-a88e-518293d0f39f");
        printTypeTemplateRStoreDO.setStoreGuid("storeGuid");
        printTypeTemplateRStoreDO.setIsDelete(false);
        printTypeTemplateRStoreDO.setBrandGuid("brandGuid");
        when(mockTemplateRStoreService.queryTemplateByInvoiceTypeAndStoreGuid("invoiceType", "storeGuid"))
                .thenReturn(printTypeTemplateRStoreDO);

        // Configure PrintTypeTemplateMapper.selectList(...).
        final PrintTypeTemplateDO printTypeTemplateDO = new PrintTypeTemplateDO();
        printTypeTemplateDO.setId(0L);
        printTypeTemplateDO.setGuid("b6e704b6-96f8-4079-a88e-518293d0f39f");
        printTypeTemplateDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        printTypeTemplateDO.setIsDelete(false);
        printTypeTemplateDO.setIsEnable(false);
        printTypeTemplateDO.setBrandGuid("brandGuid");
        printTypeTemplateDO.setName("name");
        printTypeTemplateDO.setInvoiceType("invoiceType");
        printTypeTemplateDO.setIsAllStore(false);
        final List<PrintTypeTemplateDO> printTypeTemplateDOS = Arrays.asList(printTypeTemplateDO);
        when(mockPrintTypeTemplateMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(printTypeTemplateDOS);

        // Configure PrintTypeTemplateRTypeService.queryTemplateTypeVOList(...).
        final TemplateTypeVO templateTypeVO1 = new TemplateTypeVO();
        templateTypeVO1.setTypeGuid("typeGuid");
        templateTypeVO1.setTypeName("typeName");
        final TemplateItemVO templateItemVO = new TemplateItemVO();
        templateItemVO.setItemGuid("itemGuid");
        templateItemVO.setName("name");
        templateTypeVO1.setItemList(Arrays.asList(templateItemVO));
        final List<TemplateTypeVO> templateTypeVOS = Arrays.asList(templateTypeVO1);
        when(mockTemplateRTypeService.queryTemplateTypeVOList("b6e704b6-96f8-4079-a88e-518293d0f39f",
                Arrays.asList("value"))).thenReturn(templateTypeVOS);

        // Run the test
        final PrintTypeTemplateDetailDTO result = printTypeTemplateServiceImplUnderTest.queryByStoreAndInvoiceType(
                query);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryByStoreAndInvoiceType_PrintTypeTemplateMapperReturnsNoItems() {
        // Setup
        final TemplateDetailQO query = new TemplateDetailQO();
        query.setStoreGuid("storeGuid");
        query.setInvoiceType(0);
        query.setItemGuidList(Arrays.asList("value"));

        final PrintTypeTemplateDetailDTO expectedResult = new PrintTypeTemplateDetailDTO();
        expectedResult.setGuid("b6e704b6-96f8-4079-a88e-518293d0f39f");
        expectedResult.setName("name");
        final TemplateTypeVO templateTypeVO = new TemplateTypeVO();
        expectedResult.setTypeList(Arrays.asList(templateTypeVO));
        expectedResult.setInvoiceType(Arrays.asList("value"));
        expectedResult.setIsAllStore(false);
        final TemplateStoreVO templateStoreVO = new TemplateStoreVO();
        expectedResult.setStoreList(Arrays.asList(templateStoreVO));

        // Configure StoreDeviceFeignService.queryBrandByStoreGuid(...).
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("d19b26a7-5622-4b41-940e-4691b028af7e");
        brandDTO.setUuid("449042fd-4134-4456-b7b6-8066560936e9");
        brandDTO.setName("name");
        brandDTO.setDescription("description");
        brandDTO.setLogoUrl("logoUrl");
        when(mockStoreDeviceFeignService.queryBrandByStoreGuid("storeGuid")).thenReturn(brandDTO);

        // Configure PrintTypeTemplateRStoreService.queryTemplateByInvoiceTypeAndStoreGuid(...).
        final PrintTypeTemplateRStoreDO printTypeTemplateRStoreDO = new PrintTypeTemplateRStoreDO();
        printTypeTemplateRStoreDO.setId(0L);
        printTypeTemplateRStoreDO.setTemplateGuid("b6e704b6-96f8-4079-a88e-518293d0f39f");
        printTypeTemplateRStoreDO.setStoreGuid("storeGuid");
        printTypeTemplateRStoreDO.setIsDelete(false);
        printTypeTemplateRStoreDO.setBrandGuid("brandGuid");
        when(mockTemplateRStoreService.queryTemplateByInvoiceTypeAndStoreGuid("invoiceType", "storeGuid"))
                .thenReturn(printTypeTemplateRStoreDO);

        when(mockPrintTypeTemplateMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final PrintTypeTemplateDetailDTO result = printTypeTemplateServiceImplUnderTest.queryByStoreAndInvoiceType(
                query);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryByStoreAndInvoiceType_PrintTypeTemplateRTypeServiceReturnsNoItems() {
        // Setup
        final TemplateDetailQO query = new TemplateDetailQO();
        query.setStoreGuid("storeGuid");
        query.setInvoiceType(0);
        query.setItemGuidList(Arrays.asList("value"));

        final PrintTypeTemplateDetailDTO expectedResult = new PrintTypeTemplateDetailDTO();
        expectedResult.setGuid("b6e704b6-96f8-4079-a88e-518293d0f39f");
        expectedResult.setName("name");
        final TemplateTypeVO templateTypeVO = new TemplateTypeVO();
        expectedResult.setTypeList(Arrays.asList(templateTypeVO));
        expectedResult.setInvoiceType(Arrays.asList("value"));
        expectedResult.setIsAllStore(false);
        final TemplateStoreVO templateStoreVO = new TemplateStoreVO();
        expectedResult.setStoreList(Arrays.asList(templateStoreVO));

        // Configure StoreDeviceFeignService.queryBrandByStoreGuid(...).
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("d19b26a7-5622-4b41-940e-4691b028af7e");
        brandDTO.setUuid("449042fd-4134-4456-b7b6-8066560936e9");
        brandDTO.setName("name");
        brandDTO.setDescription("description");
        brandDTO.setLogoUrl("logoUrl");
        when(mockStoreDeviceFeignService.queryBrandByStoreGuid("storeGuid")).thenReturn(brandDTO);

        // Configure PrintTypeTemplateRStoreService.queryTemplateByInvoiceTypeAndStoreGuid(...).
        final PrintTypeTemplateRStoreDO printTypeTemplateRStoreDO = new PrintTypeTemplateRStoreDO();
        printTypeTemplateRStoreDO.setId(0L);
        printTypeTemplateRStoreDO.setTemplateGuid("b6e704b6-96f8-4079-a88e-518293d0f39f");
        printTypeTemplateRStoreDO.setStoreGuid("storeGuid");
        printTypeTemplateRStoreDO.setIsDelete(false);
        printTypeTemplateRStoreDO.setBrandGuid("brandGuid");
        when(mockTemplateRStoreService.queryTemplateByInvoiceTypeAndStoreGuid("invoiceType", "storeGuid"))
                .thenReturn(printTypeTemplateRStoreDO);

        // Configure PrintTypeTemplateMapper.selectList(...).
        final PrintTypeTemplateDO printTypeTemplateDO = new PrintTypeTemplateDO();
        printTypeTemplateDO.setId(0L);
        printTypeTemplateDO.setGuid("b6e704b6-96f8-4079-a88e-518293d0f39f");
        printTypeTemplateDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        printTypeTemplateDO.setIsDelete(false);
        printTypeTemplateDO.setIsEnable(false);
        printTypeTemplateDO.setBrandGuid("brandGuid");
        printTypeTemplateDO.setName("name");
        printTypeTemplateDO.setInvoiceType("invoiceType");
        printTypeTemplateDO.setIsAllStore(false);
        final List<PrintTypeTemplateDO> printTypeTemplateDOS = Arrays.asList(printTypeTemplateDO);
        when(mockPrintTypeTemplateMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(printTypeTemplateDOS);

        when(mockTemplateRTypeService.queryTemplateTypeVOList("b6e704b6-96f8-4079-a88e-518293d0f39f",
                Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        final PrintTypeTemplateDetailDTO result = printTypeTemplateServiceImplUnderTest.queryByStoreAndInvoiceType(
                query);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryStoreByBrand() {
        // Setup
        final TemplateStoreQO query = new TemplateStoreQO();
        query.setBrandGuid("brandGuid");
        query.setKeywords("keywords");
        query.setTemplateGuid("templateGuid");
        query.setInvoiceType(Arrays.asList("value"));

        // Configure PrintTypeTemplateRStoreService.queryTemplateStoreGuidList(...).
        final PrintTypeTemplateRStoreDO printTypeTemplateRStoreDO = new PrintTypeTemplateRStoreDO();
        printTypeTemplateRStoreDO.setId(0L);
        printTypeTemplateRStoreDO.setTemplateGuid("b6e704b6-96f8-4079-a88e-518293d0f39f");
        printTypeTemplateRStoreDO.setStoreGuid("storeGuid");
        printTypeTemplateRStoreDO.setIsDelete(false);
        printTypeTemplateRStoreDO.setBrandGuid("brandGuid");
        final List<PrintTypeTemplateRStoreDO> printTypeTemplateRStoreDOS = Arrays.asList(printTypeTemplateRStoreDO);
        when(mockTemplateRStoreService.queryTemplateStoreGuidList("brandGuid", Arrays.asList("value"),
                "templateGuid")).thenReturn(printTypeTemplateRStoreDOS);

        // Run the test
        final List<String> result = printTypeTemplateServiceImplUnderTest.queryStoreByBrand(query);

        // Verify the results
        assertThat(result).isEqualTo(Arrays.asList("value"));
    }

    @Test
    public void testQueryStoreByBrand_PrintTypeTemplateRStoreServiceReturnsNoItems() {
        // Setup
        final TemplateStoreQO query = new TemplateStoreQO();
        query.setBrandGuid("brandGuid");
        query.setKeywords("keywords");
        query.setTemplateGuid("templateGuid");
        query.setInvoiceType(Arrays.asList("value"));

        when(mockTemplateRStoreService.queryTemplateStoreGuidList("brandGuid", Arrays.asList("value"),
                "templateGuid")).thenReturn(Collections.emptyList());

        // Run the test
        final List<String> result = printTypeTemplateServiceImplUnderTest.queryStoreByBrand(query);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testCheckTemplateName() {
        // Setup
        final PrintTypeTemplateDTO query = new PrintTypeTemplateDTO();
        query.setGuid("b6e704b6-96f8-4079-a88e-518293d0f39f");
        query.setBrandGuid("brandGuid");
        query.setName("name");
        final TemplateTypeDTO templateTypeDTO = new TemplateTypeDTO();
        templateTypeDTO.setName("name");
        templateTypeDTO.setItemGuidList(Arrays.asList(new TemplateItemQO()));
        query.setTypeDTOList(Arrays.asList(templateTypeDTO));
        query.setInvoiceType(Arrays.asList("value"));
        query.setIsAllStore(false);
        query.setStoreGuidList(Arrays.asList("value"));

        when(mockPrintTypeTemplateMapper.checkTemplateName("brandGuid", "name",
                "b6e704b6-96f8-4079-a88e-518293d0f39f")).thenReturn(0);

        // Run the test
        final Boolean result = printTypeTemplateServiceImplUnderTest.checkTemplateName(query);

        // Verify the results
        assertThat(result).isFalse();
    }
}
