package com.holder.saas.print.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holder.saas.print.entity.domain.type.PrintTypeTemplateRItemDO;
import com.holder.saas.print.entity.domain.type.PrintTypeTemplateRTypeDO;
import com.holder.saas.print.mapper.PrintTypeTemplateRItemMapper;
import com.holder.saas.print.mapper.PrintTypeTemplateRTypeMapper;
import com.holder.saas.print.service.feign.ItemClientService;
import com.holder.saas.print.service.feign.StoreDeviceFeignService;
import com.holderzone.saas.store.dto.item.common.ItemStringListDTO;
import com.holderzone.saas.store.dto.item.resp.ItemInfoRespDTO;
import com.holderzone.saas.store.dto.print.type.TemplateItemVO;
import com.holderzone.saas.store.dto.print.type.TemplateTypeVO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PrintTypeTemplateRTypeServiceImplTest {

    @Mock
    private PrintTypeTemplateRTypeMapper mockRTypeMapper;
    @Mock
    private PrintTypeTemplateRItemMapper mockRItemMapper;
    @Mock
    private ItemClientService mockItemClientService;

    @Mock
    private StoreDeviceFeignService storeDeviceFeignService;

    private PrintTypeTemplateRTypeServiceImpl printTypeTemplateRTypeServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        printTypeTemplateRTypeServiceImplUnderTest = new PrintTypeTemplateRTypeServiceImpl(mockRTypeMapper,
                mockRItemMapper, mockItemClientService, storeDeviceFeignService);
    }

    @Test
    public void testRemoveByTemplateGuid() {
        // Setup
        // Run the test
        printTypeTemplateRTypeServiceImplUnderTest.removeByTemplateGuid("templateGuid");

        // Verify the results
        verify(mockRTypeMapper).delete(any(LambdaQueryWrapper.class));
    }

    @Test
    public void testQueryTemplateTypeVOList() {
        // Setup
        final TemplateTypeVO templateTypeVO = new TemplateTypeVO();
        templateTypeVO.setTypeGuid("guid");
        templateTypeVO.setTypeName("name");
        final TemplateItemVO templateItemVO = new TemplateItemVO();
        templateItemVO.setItemGuid("itemGuid");
        templateItemVO.setName("name");
        templateItemVO.setTypeGuid("typeGuid");
        templateTypeVO.setItemList(Arrays.asList(templateItemVO));
        final List<TemplateTypeVO> expectedResult = Arrays.asList(templateTypeVO);

        // Configure PrintTypeTemplateRTypeMapper.selectList(...).
        final PrintTypeTemplateRTypeDO printTypeTemplateRTypeDO = new PrintTypeTemplateRTypeDO();
        printTypeTemplateRTypeDO.setId(0L);
        printTypeTemplateRTypeDO.setGuid("guid");
        printTypeTemplateRTypeDO.setTemplateGuid("templateGuid");
        printTypeTemplateRTypeDO.setIsDelete(false);
        printTypeTemplateRTypeDO.setName("name");
        final List<PrintTypeTemplateRTypeDO> printTypeTemplateRTypeDOS = Arrays.asList(printTypeTemplateRTypeDO);
        when(mockRTypeMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(printTypeTemplateRTypeDOS);

        // Configure PrintTypeTemplateRItemMapper.selectList(...).
        final PrintTypeTemplateRItemDO printTypeTemplateRItemDO = new PrintTypeTemplateRItemDO();
        printTypeTemplateRItemDO.setId(0L);
        printTypeTemplateRItemDO.setTypeGuid("typeGuid");
        printTypeTemplateRItemDO.setItemGuid("itemGuid");
        printTypeTemplateRItemDO.setIsDelete(false);
        printTypeTemplateRItemDO.setTemplateGuid("templateGuid");
        final List<PrintTypeTemplateRItemDO> printTypeTemplateRItemDOS = Arrays.asList(printTypeTemplateRItemDO);
        when(mockRItemMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(printTypeTemplateRItemDOS);

        // Configure ItemClientService.getItemNameList(...).
        final ItemInfoRespDTO itemInfoRespDTO = new ItemInfoRespDTO();
        itemInfoRespDTO.setItemGuid("itemGuid");
        itemInfoRespDTO.setBrandGuid("brandGuid");
        itemInfoRespDTO.setStoreGuid("storeGuid");
        itemInfoRespDTO.setTypeGuid("typeGuid");
        itemInfoRespDTO.setName("name");
        final List<ItemInfoRespDTO> itemInfoRespDTOS = Arrays.asList(itemInfoRespDTO);
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        when(mockItemClientService.getItemNameList(itemStringListDTO)).thenReturn(itemInfoRespDTOS);

        // Run the test
        final List<TemplateTypeVO> result = printTypeTemplateRTypeServiceImplUnderTest.queryTemplateTypeVOList(
                "templateGuid", Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryTemplateTypeVOList_PrintTypeTemplateRTypeMapperReturnsNoItems() {
        // Setup
        final TemplateTypeVO templateTypeVO = new TemplateTypeVO();
        templateTypeVO.setTypeGuid("guid");
        templateTypeVO.setTypeName("name");
        final TemplateItemVO templateItemVO = new TemplateItemVO();
        templateItemVO.setItemGuid("itemGuid");
        templateItemVO.setName("name");
        templateItemVO.setTypeGuid("typeGuid");
        templateTypeVO.setItemList(Arrays.asList(templateItemVO));
        final List<TemplateTypeVO> expectedResult = Arrays.asList(templateTypeVO);
        when(mockRTypeMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure PrintTypeTemplateRItemMapper.selectList(...).
        final PrintTypeTemplateRItemDO printTypeTemplateRItemDO = new PrintTypeTemplateRItemDO();
        printTypeTemplateRItemDO.setId(0L);
        printTypeTemplateRItemDO.setTypeGuid("typeGuid");
        printTypeTemplateRItemDO.setItemGuid("itemGuid");
        printTypeTemplateRItemDO.setIsDelete(false);
        printTypeTemplateRItemDO.setTemplateGuid("templateGuid");
        final List<PrintTypeTemplateRItemDO> printTypeTemplateRItemDOS = Arrays.asList(printTypeTemplateRItemDO);
        when(mockRItemMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(printTypeTemplateRItemDOS);

        // Configure ItemClientService.getItemNameList(...).
        final ItemInfoRespDTO itemInfoRespDTO = new ItemInfoRespDTO();
        itemInfoRespDTO.setItemGuid("itemGuid");
        itemInfoRespDTO.setBrandGuid("brandGuid");
        itemInfoRespDTO.setStoreGuid("storeGuid");
        itemInfoRespDTO.setTypeGuid("typeGuid");
        itemInfoRespDTO.setName("name");
        final List<ItemInfoRespDTO> itemInfoRespDTOS = Arrays.asList(itemInfoRespDTO);
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        when(mockItemClientService.getItemNameList(itemStringListDTO)).thenReturn(itemInfoRespDTOS);

        // Run the test
        final List<TemplateTypeVO> result = printTypeTemplateRTypeServiceImplUnderTest.queryTemplateTypeVOList(
                "templateGuid", Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryTemplateTypeVOList_PrintTypeTemplateRItemMapperReturnsNoItems() {
        // Setup
        final TemplateTypeVO templateTypeVO = new TemplateTypeVO();
        templateTypeVO.setTypeGuid("guid");
        templateTypeVO.setTypeName("name");
        final TemplateItemVO templateItemVO = new TemplateItemVO();
        templateItemVO.setItemGuid("itemGuid");
        templateItemVO.setName("name");
        templateItemVO.setTypeGuid("typeGuid");
        templateTypeVO.setItemList(Arrays.asList(templateItemVO));
        final List<TemplateTypeVO> expectedResult = Arrays.asList(templateTypeVO);

        // Configure PrintTypeTemplateRTypeMapper.selectList(...).
        final PrintTypeTemplateRTypeDO printTypeTemplateRTypeDO = new PrintTypeTemplateRTypeDO();
        printTypeTemplateRTypeDO.setId(0L);
        printTypeTemplateRTypeDO.setGuid("guid");
        printTypeTemplateRTypeDO.setTemplateGuid("templateGuid");
        printTypeTemplateRTypeDO.setIsDelete(false);
        printTypeTemplateRTypeDO.setName("name");
        final List<PrintTypeTemplateRTypeDO> printTypeTemplateRTypeDOS = Arrays.asList(printTypeTemplateRTypeDO);
        when(mockRTypeMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(printTypeTemplateRTypeDOS);

        when(mockRItemMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure ItemClientService.getItemNameList(...).
        final ItemInfoRespDTO itemInfoRespDTO = new ItemInfoRespDTO();
        itemInfoRespDTO.setItemGuid("itemGuid");
        itemInfoRespDTO.setBrandGuid("brandGuid");
        itemInfoRespDTO.setStoreGuid("storeGuid");
        itemInfoRespDTO.setTypeGuid("typeGuid");
        itemInfoRespDTO.setName("name");
        final List<ItemInfoRespDTO> itemInfoRespDTOS = Arrays.asList(itemInfoRespDTO);
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        when(mockItemClientService.getItemNameList(itemStringListDTO)).thenReturn(itemInfoRespDTOS);

        // Run the test
        final List<TemplateTypeVO> result = printTypeTemplateRTypeServiceImplUnderTest.queryTemplateTypeVOList(
                "templateGuid", Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryTemplateTypeVOList_ItemClientServiceReturnsNoItems() {
        // Setup
        // Configure PrintTypeTemplateRTypeMapper.selectList(...).
        final PrintTypeTemplateRTypeDO printTypeTemplateRTypeDO = new PrintTypeTemplateRTypeDO();
        printTypeTemplateRTypeDO.setId(0L);
        printTypeTemplateRTypeDO.setGuid("guid");
        printTypeTemplateRTypeDO.setTemplateGuid("templateGuid");
        printTypeTemplateRTypeDO.setIsDelete(false);
        printTypeTemplateRTypeDO.setName("name");
        final List<PrintTypeTemplateRTypeDO> printTypeTemplateRTypeDOS = Arrays.asList(printTypeTemplateRTypeDO);
        when(mockRTypeMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(printTypeTemplateRTypeDOS);

        // Configure PrintTypeTemplateRItemMapper.selectList(...).
        final PrintTypeTemplateRItemDO printTypeTemplateRItemDO = new PrintTypeTemplateRItemDO();
        printTypeTemplateRItemDO.setId(0L);
        printTypeTemplateRItemDO.setTypeGuid("typeGuid");
        printTypeTemplateRItemDO.setItemGuid("itemGuid");
        printTypeTemplateRItemDO.setIsDelete(false);
        printTypeTemplateRItemDO.setTemplateGuid("templateGuid");
        final List<PrintTypeTemplateRItemDO> printTypeTemplateRItemDOS = Arrays.asList(printTypeTemplateRItemDO);
        when(mockRItemMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(printTypeTemplateRItemDOS);

        // Configure ItemClientService.getItemNameList(...).
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        when(mockItemClientService.getItemNameList(itemStringListDTO)).thenReturn(Collections.emptyList());

        // Run the test
        final List<TemplateTypeVO> result = printTypeTemplateRTypeServiceImplUnderTest.queryTemplateTypeVOList(
                "templateGuid", Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }
}
