package com.holder.saas.print.service.impl;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.Arrays;
import java.util.Collections;

import static org.assertj.core.api.Assertions.assertThat;

@RunWith(MockitoJUnitRunner.class)
public class DistributedServiceImplTest {

    @Mock
    private RedisTemplate mockRedisTemplate;

    private DistributedServiceImpl distributedServiceImplUnderTest;

    @Before
    public void setUp() {
        distributedServiceImplUnderTest = new DistributedServiceImpl(mockRedisTemplate);
    }

    @Test
    public void testRawId() {
        assertThat(distributedServiceImplUnderTest.rawId("tag")).isEqualTo(0L);
    }

    @Test
    public void testNextId() {
        assertThat(distributedServiceImplUnderTest.nextId("tag")).isEqualTo("result");
    }

    @Test
    public void testNextBatchId() {
        assertThat(distributedServiceImplUnderTest.nextBatchId("tag", 0L)).isEqualTo(Arrays.asList("value"));
        assertThat(distributedServiceImplUnderTest.nextBatchId("tag", 0L)).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testNextPrinterGuid() {
        assertThat(distributedServiceImplUnderTest.nextPrinterGuid()).isEqualTo("result");
    }

    @Test
    public void testNextBatchPrinterGuid() {
        assertThat(distributedServiceImplUnderTest.nextBatchPrinterGuid(0L)).isEqualTo(Arrays.asList("value"));
        assertThat(distributedServiceImplUnderTest.nextBatchPrinterGuid(0L)).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testNextPrinterInvoiceGuid() {
        assertThat(distributedServiceImplUnderTest.nextPrinterInvoiceGuid()).isEqualTo("result");
    }

    @Test
    public void testNextBatchPrinterInvoiceGuid() {
        assertThat(distributedServiceImplUnderTest.nextBatchPrinterInvoiceGuid(0L)).isEqualTo(Arrays.asList("value"));
        assertThat(distributedServiceImplUnderTest.nextBatchPrinterInvoiceGuid(0L)).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testNextPrinterAreaGuid() {
        assertThat(distributedServiceImplUnderTest.nextPrinterAreaGuid()).isEqualTo("result");
    }

    @Test
    public void testNextBatchPrinterAreaGuid() {
        assertThat(distributedServiceImplUnderTest.nextBatchPrinterAreaGuid(0L)).isEqualTo(Arrays.asList("value"));
        assertThat(distributedServiceImplUnderTest.nextBatchPrinterAreaGuid(0L)).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testNextPrinterItemGuid() {
        assertThat(distributedServiceImplUnderTest.nextPrinterItemGuid()).isEqualTo("result");
    }

    @Test
    public void testNextBatchPrinterItemGuid() {
        assertThat(distributedServiceImplUnderTest.nextBatchPrinterItemGuid(0L)).isEqualTo(Arrays.asList("value"));
        assertThat(distributedServiceImplUnderTest.nextBatchPrinterItemGuid(0L)).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testNextPrintRecordGuid() {
        assertThat(distributedServiceImplUnderTest.nextPrintRecordGuid()).isEqualTo("result");
    }

    @Test
    public void testNextBatchPrintRecordGuid() {
        assertThat(distributedServiceImplUnderTest.nextBatchPrintRecordGuid(0L)).isEqualTo(Arrays.asList("value"));
        assertThat(distributedServiceImplUnderTest.nextBatchPrintRecordGuid(0L)).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testNextInvoiceFormatId() {
        assertThat(distributedServiceImplUnderTest.nextInvoiceFormatId()).isEqualTo("result");
    }

    @Test
    public void testNextPrintTypeTemplateGuid() {
        assertThat(distributedServiceImplUnderTest.nextPrintTypeTemplateGuid()).isEqualTo("result");
    }

    @Test
    public void testNextBatchTemplateRTypeGuid() {
        assertThat(distributedServiceImplUnderTest.nextBatchTemplateRTypeGuid(0L)).isEqualTo(Arrays.asList("value"));
        assertThat(distributedServiceImplUnderTest.nextBatchTemplateRTypeGuid(0L)).isEqualTo(Collections.emptyList());
    }
}
