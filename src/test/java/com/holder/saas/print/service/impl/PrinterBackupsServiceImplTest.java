package com.holder.saas.print.service.impl;

import com.holder.saas.print.entity.domain.PrinterBackupsDO;
import com.holderzone.saas.store.dto.print.PrinterDTO;
import org.junit.Before;
import org.junit.Test;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

public class PrinterBackupsServiceImplTest {

    private PrinterBackupsServiceImpl printerBackupsServiceImplUnderTest;

    @Before
    public void setUp() {
        printerBackupsServiceImplUnderTest = new PrinterBackupsServiceImpl();
    }

    @Test
    public void testQueryPrinters() {
        // Setup
        final PrinterBackupsDO expectedResult = new PrinterBackupsDO();
        expectedResult.setId(0L);
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setDeviceId("deviceId");
        expectedResult.setPrintListJson("printListJson");
        expectedResult.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Run the test
        final PrinterBackupsDO result = printerBackupsServiceImplUnderTest.queryPrinters("storeGuid", "deviceId");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testBackupsPrinter() {
        // Setup
        final PrinterDTO printerDTO = new PrinterDTO();
        printerDTO.setStoreGuid("storeGuid");
        printerDTO.setStoreName("storeName");
        printerDTO.setDeviceId("deviceId");
        printerDTO.setPrinterGuid("printerGuid");
        printerDTO.setPrinterName("printerName");
        final List<PrinterDTO> printerBackupsDOS = Arrays.asList(printerDTO);

        // Run the test
        final boolean result = printerBackupsServiceImplUnderTest.backupsPrinter("storeGuid", "deviceId",
                printerBackupsDOS);

        // Verify the results
        assertThat(result).isFalse();
    }
}
