package com.holder.saas.print.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holder.saas.print.mapper.PrintTypeTemplateRItemMapper;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;

@RunWith(MockitoJUnitRunner.class)
public class PrintTypeTemplateRItemServiceImplTest {

    @Mock
    private PrintTypeTemplateRItemMapper mockRItemMapper;

    private PrintTypeTemplateRItemServiceImpl printTypeTemplateRItemServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        printTypeTemplateRItemServiceImplUnderTest = new PrintTypeTemplateRItemServiceImpl(mockRItemMapper);
    }

    @Test
    public void testRemoveByTemplateGuid() {
        // Setup
        // Run the test
        printTypeTemplateRItemServiceImplUnderTest.removeByTemplateGuid("templateGuid");

        // Verify the results
        verify(mockRItemMapper).delete(any(LambdaQueryWrapper.class));
    }
}
