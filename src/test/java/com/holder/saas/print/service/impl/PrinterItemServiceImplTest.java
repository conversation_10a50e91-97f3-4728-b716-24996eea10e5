package com.holder.saas.print.service.impl;

import com.holder.saas.print.entity.domain.PrinterItemDO;
import com.holder.saas.print.mapstruct.PrinterMapstruct;
import com.holder.saas.print.mapstruct.PrinterRawMaptstruct;
import com.holder.saas.print.service.DistributedService;
import com.holderzone.saas.store.dto.print.PrinterDTO;
import com.holderzone.saas.store.dto.print.PrinterItemDTO;
import com.holderzone.saas.store.dto.print.raw.PrinterItemRawDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PrinterItemServiceImplTest {

    @Mock
    private DistributedService mockDistributedService;
    @Mock
    private PrinterMapstruct mockPrinterMapstruct;
    @Mock
    private PrinterRawMaptstruct mockPrinterRawMaptstruct;

    private PrinterItemServiceImpl printerItemServiceImplUnderTest;

    @Before
    public void setUp() {
        printerItemServiceImplUnderTest = new PrinterItemServiceImpl(mockDistributedService, mockPrinterMapstruct,
                mockPrinterRawMaptstruct);
    }

    @Test
    public void testBindPrinterItem() {
        // Setup
        final PrinterDTO printerDTO = new PrinterDTO();
        printerDTO.setStoreGuid("storeGuid");
        printerDTO.setPrinterGuid("printerGuid");
        final PrinterItemDTO printerItemDTO = new PrinterItemDTO();
        printerItemDTO.setItemGuid("itemGuid");
        printerItemDTO.setItemName("itemName");
        printerDTO.setArrayOfItemDTO(Arrays.asList(printerItemDTO));

        when(mockDistributedService.nextBatchPrinterItemGuid(0L)).thenReturn(Arrays.asList("value"));

        // Run the test
        printerItemServiceImplUnderTest.bindPrinterItem(printerDTO);

        // Verify the results
    }

    @Test
    public void testBindPrinterItem_DistributedServiceReturnsNoItems() {
        // Setup
        final PrinterDTO printerDTO = new PrinterDTO();
        printerDTO.setStoreGuid("storeGuid");
        printerDTO.setPrinterGuid("printerGuid");
        final PrinterItemDTO printerItemDTO = new PrinterItemDTO();
        printerItemDTO.setItemGuid("itemGuid");
        printerItemDTO.setItemName("itemName");
        printerDTO.setArrayOfItemDTO(Arrays.asList(printerItemDTO));

        when(mockDistributedService.nextBatchPrinterItemGuid(0L)).thenReturn(Collections.emptyList());

        // Run the test
        printerItemServiceImplUnderTest.bindPrinterItem(printerDTO);

        // Verify the results
    }

    @Test
    public void testListPrinterItem() {
        // Setup
        final PrinterDTO printerDTO = new PrinterDTO();
        printerDTO.setStoreGuid("storeGuid");
        printerDTO.setPrinterGuid("printerGuid");
        final PrinterItemDTO printerItemDTO = new PrinterItemDTO();
        printerItemDTO.setItemGuid("itemGuid");
        printerItemDTO.setItemName("itemName");
        printerDTO.setArrayOfItemDTO(Arrays.asList(printerItemDTO));

        final List<PrinterItemDTO> expectedResult = Arrays.asList(new PrinterItemDTO("itemGuid", "itemName"));

        // Configure PrinterMapstruct.toPrinterItemDTO(...).
        final List<PrinterItemDTO> printerItemDTOS = Arrays.asList(new PrinterItemDTO("itemGuid", "itemName"));
        final PrinterItemDO printerItemDO = new PrinterItemDO();
        printerItemDO.setGuid("75cef318-0a1b-409a-8610-ef78715fdc1b");
        printerItemDO.setStoreGuid("storeGuid");
        printerItemDO.setPrinterGuid("printerGuid");
        printerItemDO.setItemGuid("itemGuid");
        printerItemDO.setItemName("itemName");
        final List<PrinterItemDO> list = Arrays.asList(printerItemDO);
        when(mockPrinterMapstruct.toPrinterItemDTO(list)).thenReturn(printerItemDTOS);

        // Run the test
        final List<PrinterItemDTO> result = printerItemServiceImplUnderTest.listPrinterItem(printerDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testListPrinterItem_PrinterMapstructReturnsNoItems() {
        // Setup
        final PrinterDTO printerDTO = new PrinterDTO();
        printerDTO.setStoreGuid("storeGuid");
        printerDTO.setPrinterGuid("printerGuid");
        final PrinterItemDTO printerItemDTO = new PrinterItemDTO();
        printerItemDTO.setItemGuid("itemGuid");
        printerItemDTO.setItemName("itemName");
        printerDTO.setArrayOfItemDTO(Arrays.asList(printerItemDTO));

        // Configure PrinterMapstruct.toPrinterItemDTO(...).
        final PrinterItemDO printerItemDO = new PrinterItemDO();
        printerItemDO.setGuid("75cef318-0a1b-409a-8610-ef78715fdc1b");
        printerItemDO.setStoreGuid("storeGuid");
        printerItemDO.setPrinterGuid("printerGuid");
        printerItemDO.setItemGuid("itemGuid");
        printerItemDO.setItemName("itemName");
        final List<PrinterItemDO> list = Arrays.asList(printerItemDO);
        when(mockPrinterMapstruct.toPrinterItemDTO(list)).thenReturn(Collections.emptyList());

        // Run the test
        final List<PrinterItemDTO> result = printerItemServiceImplUnderTest.listPrinterItem(printerDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testDeletePrinterItem() {
        // Setup
        // Run the test
        printerItemServiceImplUnderTest.deletePrinterItem("printerGuid");

        // Verify the results
    }

    @Test
    public void testBatchDeletePrinterItem() {
        // Setup
        // Run the test
        printerItemServiceImplUnderTest.batchDeletePrinterItem(Arrays.asList("value"));

        // Verify the results
    }

    @Test
    public void testDeleteStorePrinterItem() {
        // Setup
        // Run the test
        printerItemServiceImplUnderTest.deleteStorePrinterItem("storeGuid");

        // Verify the results
    }

    @Test
    public void testBatchDeleteStorePrinterItem() {
        // Setup
        // Run the test
        printerItemServiceImplUnderTest.batchDeleteStorePrinterItem(Arrays.asList("value"));

        // Verify the results
    }

    @Test
    public void testListRaw() {
        // Setup
        final PrinterItemRawDTO printerItemRawDTO = new PrinterItemRawDTO();
        printerItemRawDTO.setGuid("e12a8dcd-f19b-4c6d-bfd7-5c30268e93e9");
        printerItemRawDTO.setStoreGuid("storeGuid");
        printerItemRawDTO.setPrinterGuid("printerGuid");
        printerItemRawDTO.setItemGuid("itemGuid");
        printerItemRawDTO.setItemName("itemName");
        final List<PrinterItemRawDTO> expectedResult = Arrays.asList(printerItemRawDTO);

        // Configure PrinterRawMaptstruct.toItemRawDTO(...).
        final PrinterItemRawDTO printerItemRawDTO1 = new PrinterItemRawDTO();
        printerItemRawDTO1.setGuid("e12a8dcd-f19b-4c6d-bfd7-5c30268e93e9");
        printerItemRawDTO1.setStoreGuid("storeGuid");
        printerItemRawDTO1.setPrinterGuid("printerGuid");
        printerItemRawDTO1.setItemGuid("itemGuid");
        printerItemRawDTO1.setItemName("itemName");
        final List<PrinterItemRawDTO> printerItemRawDTOS = Arrays.asList(printerItemRawDTO1);
        final PrinterItemDO printerItemDO = new PrinterItemDO();
        printerItemDO.setGuid("75cef318-0a1b-409a-8610-ef78715fdc1b");
        printerItemDO.setStoreGuid("storeGuid");
        printerItemDO.setPrinterGuid("printerGuid");
        printerItemDO.setItemGuid("itemGuid");
        printerItemDO.setItemName("itemName");
        final List<PrinterItemDO> list = Arrays.asList(printerItemDO);
        when(mockPrinterRawMaptstruct.toItemRawDTO(list)).thenReturn(printerItemRawDTOS);

        // Run the test
        final List<PrinterItemRawDTO> result = printerItemServiceImplUnderTest.listRaw("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testListRaw_PrinterRawMaptstructReturnsNoItems() {
        // Setup
        // Configure PrinterRawMaptstruct.toItemRawDTO(...).
        final PrinterItemDO printerItemDO = new PrinterItemDO();
        printerItemDO.setGuid("75cef318-0a1b-409a-8610-ef78715fdc1b");
        printerItemDO.setStoreGuid("storeGuid");
        printerItemDO.setPrinterGuid("printerGuid");
        printerItemDO.setItemGuid("itemGuid");
        printerItemDO.setItemName("itemName");
        final List<PrinterItemDO> list = Arrays.asList(printerItemDO);
        when(mockPrinterRawMaptstruct.toItemRawDTO(list)).thenReturn(Collections.emptyList());

        // Run the test
        final List<PrinterItemRawDTO> result = printerItemServiceImplUnderTest.listRaw("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }
}
