package com.holder.saas.print.service.impl;

import com.holder.saas.print.config.PrinterConfig;
import com.holderzone.saas.store.dto.print.cloud.FeieRespDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class FeiePrinterServiceImplTest {

    @Mock
    private PrinterConfig mockPrinterConfig;

    private FeiePrinterServiceImpl feiePrinterServiceImplUnderTest;

    @Before
    public void setUp() {
        feiePrinterServiceImplUnderTest = new FeiePrinterServiceImpl(mockPrinterConfig);
    }

    @Test
    public void testAddPrinter() {
        // Setup
        when(mockPrinterConfig.getUser()).thenReturn("content");
        when(mockPrinterConfig.getUKey()).thenReturn("result");
        when(mockPrinterConfig.getUrl()).thenReturn("result");

        // Run the test
        feiePrinterServiceImplUnderTest.addPrinter("deviceNo", "deviceKey", "printerName");

        // Verify the results
    }

    @Test
    public void testDeletePrinter() {
        // Setup
        when(mockPrinterConfig.getUser()).thenReturn("content");
        when(mockPrinterConfig.getUKey()).thenReturn("result");
        when(mockPrinterConfig.getUrl()).thenReturn("result");

        // Run the test
        feiePrinterServiceImplUnderTest.deletePrinter("content");

        // Verify the results
    }

    @Test
    public void testQueryPrinterInfo() {
        // Setup
        final FeieRespDTO expectedResult = new FeieRespDTO();
        expectedResult.setMsg("msg");
        expectedResult.setRet(0);
        final FeieRespDTO.DataMsg data = new FeieRespDTO.DataMsg();
        data.setOk(Arrays.asList("value"));
        data.setNo(Arrays.asList("value"));
        expectedResult.setData(data);

        when(mockPrinterConfig.getUser()).thenReturn("content");
        when(mockPrinterConfig.getUKey()).thenReturn("result");
        when(mockPrinterConfig.getUrl()).thenReturn("result");

        // Run the test
        final FeieRespDTO result = feiePrinterServiceImplUnderTest.queryPrinterInfo("content");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testTestPrinter() {
        // Setup
        when(mockPrinterConfig.getUser()).thenReturn("content");
        when(mockPrinterConfig.getUKey()).thenReturn("result");
        when(mockPrinterConfig.getUrl()).thenReturn("result");

        // Run the test
        feiePrinterServiceImplUnderTest.testPrinter("content", 0);

        // Verify the results
    }

    @Test
    public void testPrint() {
        // Setup
        when(mockPrinterConfig.getUser()).thenReturn("content");
        when(mockPrinterConfig.getUKey()).thenReturn("result");
        when(mockPrinterConfig.getUrl()).thenReturn("result");

        // Run the test
        feiePrinterServiceImplUnderTest.print("content", "content", 0);

        // Verify the results
    }
}
