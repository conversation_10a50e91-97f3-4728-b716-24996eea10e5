package com.holder.saas.print.service.impl;

import com.google.common.util.concurrent.MoreExecutors;
import com.holder.saas.print.entity.domain.PrintRecordDO;
import com.holder.saas.print.entity.query.PrintRecordQuery;
import com.holder.saas.print.entity.read.PrintRecordReadDO;
import com.holder.saas.print.entity.read.PrinterReadDO;
import com.holder.saas.print.mapstruct.PrintRecordMapstruct;
import com.holder.saas.print.service.*;
import com.holderzone.framework.rocketmq.common.DefaultRocketMqProducer;
import com.holderzone.saas.store.dto.print.PrintOrderDTO;
import com.holderzone.saas.store.dto.print.PrintRecordDTO;
import com.holderzone.saas.store.dto.print.PrintRecordReqDTO;
import com.holderzone.saas.store.dto.print.PrinterDTO;
import com.holderzone.saas.store.dto.print.content.PrintDTO;
import com.holderzone.saas.store.dto.print.format.FormatDTO;
import org.apache.rocketmq.common.message.Message;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PrintRecordServiceImplTest {

    @Mock
    private PrinterService mockPrinterService;
    @Mock
    private PrinterRoutingService mockPrinterRoutingService;
    @Mock
    private PrintRecordCloneService mockPrintRecordCloneService;
    @Mock
    private PrintRecordRespService mockPrintRecordRespService;
    @Mock
    private PrintPushService mockPrintPushService;
    @Mock
    private ContentCacheService mockContentCacheService;
    @Mock
    private PrintRecordMapstruct mockPrintRecordMapstruct;
    @Mock
    private DefaultRocketMqProducer mockDefaultRocketMqProducer;
    @Mock
    private RedisTemplate mockRedisTemplate;
    @Mock
    private CloudPrinterService mockCloudPrinterService;

    private PrintRecordServiceImpl printRecordServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        printRecordServiceImplUnderTest = new PrintRecordServiceImpl(mockPrinterService, mockPrinterRoutingService,
                mockPrintRecordCloneService, mockPrintRecordRespService, mockPrintPushService, mockContentCacheService,
                mockPrintRecordMapstruct, mockDefaultRocketMqProducer, mockRedisTemplate, mockCloudPrinterService);
        ReflectionTestUtils.setField(printRecordServiceImplUnderTest, "printCloudInvoiceThreadPool",
                MoreExecutors.newDirectExecutorService());
    }

    @Test
    public void testInsertRecord() {
        // Setup
        final PrintDTO printDTO = new PrintDTO();
        printDTO.setInvoiceType(0);
        printDTO.setStoreGuid("storeGuid");
        printDTO.setTradeMode(0);
        printDTO.setOperatorStaffName("operatorStaffName");
        printDTO.setCreateTime(0L);
        printDTO.setDeviceId("masterDeviceId");
        printDTO.setAppointPrinterType(0);
        printDTO.setItemInvoiceType(0);

        // Configure PrinterRoutingService.findPrinterAvailable(...).
        final PrinterReadDO printerReadDO = new PrinterReadDO();
        printerReadDO.setPrinterGuid("printerGuid");
        printerReadDO.setPrinterType(0);
        printerReadDO.setPrintCount(0);
        printerReadDO.setIsPrintHangUp(false);
        printerReadDO.setDeviceNo("deviceNo");
        final List<PrinterReadDO> printerReadDOS = Arrays.asList(printerReadDO);
        final PrintDTO printDto = new PrintDTO();
        printDto.setInvoiceType(0);
        printDto.setStoreGuid("storeGuid");
        printDto.setTradeMode(0);
        printDto.setOperatorStaffName("operatorStaffName");
        printDto.setCreateTime(0L);
        printDto.setDeviceId("masterDeviceId");
        printDto.setAppointPrinterType(0);
        printDto.setItemInvoiceType(0);
        when(mockPrinterRoutingService.findPrinterAvailable(printDto)).thenReturn(printerReadDOS);

        // Configure PrinterRoutingService.findCloudPrinterAvailable(...).
        final PrinterReadDO printerReadDO1 = new PrinterReadDO();
        printerReadDO1.setPrinterGuid("printerGuid");
        printerReadDO1.setPrinterType(0);
        printerReadDO1.setPrintCount(0);
        printerReadDO1.setIsPrintHangUp(false);
        printerReadDO1.setDeviceNo("deviceNo");
        final List<PrinterReadDO> printerReadDOS1 = Arrays.asList(printerReadDO1);
        final PrintDTO printDTO1 = new PrintDTO();
        printDTO1.setInvoiceType(0);
        printDTO1.setStoreGuid("storeGuid");
        printDTO1.setTradeMode(0);
        printDTO1.setOperatorStaffName("operatorStaffName");
        printDTO1.setCreateTime(0L);
        printDTO1.setDeviceId("masterDeviceId");
        printDTO1.setAppointPrinterType(0);
        printDTO1.setItemInvoiceType(0);
        when(mockPrinterRoutingService.findCloudPrinterAvailable(printDTO1)).thenReturn(printerReadDOS1);

        // Run the test
        final String result = printRecordServiceImplUnderTest.insertRecord(printDTO);

        // Verify the results
        assertThat(result).isEqualTo("SUCCESS");

        // Confirm PrintRecordCloneService.cloneRecord(...).
        final PrintDTO printDTO2 = new PrintDTO();
        printDTO2.setInvoiceType(0);
        printDTO2.setStoreGuid("storeGuid");
        printDTO2.setTradeMode(0);
        printDTO2.setOperatorStaffName("operatorStaffName");
        printDTO2.setCreateTime(0L);
        printDTO2.setDeviceId("masterDeviceId");
        printDTO2.setAppointPrinterType(0);
        printDTO2.setItemInvoiceType(0);
        final PrinterReadDO printerReadDO2 = new PrinterReadDO();
        printerReadDO2.setPrinterGuid("printerGuid");
        printerReadDO2.setPrinterType(0);
        printerReadDO2.setPrintCount(0);
        printerReadDO2.setIsPrintHangUp(false);
        printerReadDO2.setDeviceNo("deviceNo");
        final List<PrinterReadDO> printers = Arrays.asList(printerReadDO2);
        final PrintRecordDO printRecordDO = new PrintRecordDO();
        printRecordDO.setRecordUid("recordUid");
        printRecordDO.setRecordGuid("recordGuid");
        printRecordDO.setStoreGuid("storeGuid");
        printRecordDO.setDeviceId("deviceId");
        printRecordDO.setInvoiceType(0);
        printRecordDO.setPrinterGuid("printerGuid");
        printRecordDO.setPrintStatus(0);
        printRecordDO.setPrintStatusMsg("客户端打印超时，系统默认失败");
        printRecordDO.setPrintContent("printContent");
        final List<PrintRecordDO> printRecordsToInsert = Arrays.asList(printRecordDO);
        final PrintRecordReadDO printRecordReadDO = new PrintRecordReadDO();
        printRecordReadDO.setId(0L);
        printRecordReadDO.setRecordUid("recordUid");
        printRecordReadDO.setRecordGuid("recordGuid");
        printRecordReadDO.setInvoiceType(0);
        printRecordReadDO.setPrintStatus(0);
        final List<PrintRecordReadDO> printRecordsToCache = Arrays.asList(printRecordReadDO);
        verify(mockPrintRecordCloneService).cloneRecord(printDTO2, printers, printRecordsToInsert, printRecordsToCache);
        verify(mockCloudPrinterService).print("", "deviceNo", 0);

        // Confirm ContentCacheService.save(...).
        final PrintRecordReadDO printRecordReadDO1 = new PrintRecordReadDO();
        printRecordReadDO1.setId(0L);
        printRecordReadDO1.setRecordUid("recordUid");
        printRecordReadDO1.setRecordGuid("recordGuid");
        printRecordReadDO1.setInvoiceType(0);
        printRecordReadDO1.setPrintStatus(0);
        final List<PrintRecordReadDO> arrayOfPrintRecordReadDO = Arrays.asList(printRecordReadDO1);
        verify(mockContentCacheService).save(arrayOfPrintRecordReadDO);
        verify(mockDefaultRocketMqProducer).sendMessage(any(Message.class));

        // Confirm PrintPushService.pushPrintTaskMsg(...).
        final PrintDTO printDTO3 = new PrintDTO();
        printDTO3.setInvoiceType(0);
        printDTO3.setStoreGuid("storeGuid");
        printDTO3.setTradeMode(0);
        printDTO3.setOperatorStaffName("operatorStaffName");
        printDTO3.setCreateTime(0L);
        printDTO3.setDeviceId("masterDeviceId");
        printDTO3.setAppointPrinterType(0);
        printDTO3.setItemInvoiceType(0);
        verify(mockPrintPushService).pushPrintTaskMsg(printDTO3, Arrays.asList("value"));
    }

    @Test
    public void testInsertRecord_PrinterRoutingServiceFindPrinterAvailableReturnsNoItems() {
        // Setup
        final PrintDTO printDTO = new PrintDTO();
        printDTO.setInvoiceType(0);
        printDTO.setStoreGuid("storeGuid");
        printDTO.setTradeMode(0);
        printDTO.setOperatorStaffName("operatorStaffName");
        printDTO.setCreateTime(0L);
        printDTO.setDeviceId("masterDeviceId");
        printDTO.setAppointPrinterType(0);
        printDTO.setItemInvoiceType(0);

        // Configure PrinterRoutingService.findPrinterAvailable(...).
        final PrintDTO printDto = new PrintDTO();
        printDto.setInvoiceType(0);
        printDto.setStoreGuid("storeGuid");
        printDto.setTradeMode(0);
        printDto.setOperatorStaffName("operatorStaffName");
        printDto.setCreateTime(0L);
        printDto.setDeviceId("masterDeviceId");
        printDto.setAppointPrinterType(0);
        printDto.setItemInvoiceType(0);
        when(mockPrinterRoutingService.findPrinterAvailable(printDto)).thenReturn(Collections.emptyList());

        // Configure PrinterRoutingService.findCloudPrinterAvailable(...).
        final PrinterReadDO printerReadDO = new PrinterReadDO();
        printerReadDO.setPrinterGuid("printerGuid");
        printerReadDO.setPrinterType(0);
        printerReadDO.setPrintCount(0);
        printerReadDO.setIsPrintHangUp(false);
        printerReadDO.setDeviceNo("deviceNo");
        final List<PrinterReadDO> printerReadDOS = Arrays.asList(printerReadDO);
        final PrintDTO printDTO1 = new PrintDTO();
        printDTO1.setInvoiceType(0);
        printDTO1.setStoreGuid("storeGuid");
        printDTO1.setTradeMode(0);
        printDTO1.setOperatorStaffName("operatorStaffName");
        printDTO1.setCreateTime(0L);
        printDTO1.setDeviceId("masterDeviceId");
        printDTO1.setAppointPrinterType(0);
        printDTO1.setItemInvoiceType(0);
        when(mockPrinterRoutingService.findCloudPrinterAvailable(printDTO1)).thenReturn(printerReadDOS);

        // Run the test
        final String result = printRecordServiceImplUnderTest.insertRecord(printDTO);

        // Verify the results
        assertThat(result).isEqualTo("SUCCESS");

        // Confirm PrintRecordCloneService.cloneRecord(...).
        final PrintDTO printDTO2 = new PrintDTO();
        printDTO2.setInvoiceType(0);
        printDTO2.setStoreGuid("storeGuid");
        printDTO2.setTradeMode(0);
        printDTO2.setOperatorStaffName("operatorStaffName");
        printDTO2.setCreateTime(0L);
        printDTO2.setDeviceId("masterDeviceId");
        printDTO2.setAppointPrinterType(0);
        printDTO2.setItemInvoiceType(0);
        final PrinterReadDO printerReadDO1 = new PrinterReadDO();
        printerReadDO1.setPrinterGuid("printerGuid");
        printerReadDO1.setPrinterType(0);
        printerReadDO1.setPrintCount(0);
        printerReadDO1.setIsPrintHangUp(false);
        printerReadDO1.setDeviceNo("deviceNo");
        final List<PrinterReadDO> printers = Arrays.asList(printerReadDO1);
        final PrintRecordDO printRecordDO = new PrintRecordDO();
        printRecordDO.setRecordUid("recordUid");
        printRecordDO.setRecordGuid("recordGuid");
        printRecordDO.setStoreGuid("storeGuid");
        printRecordDO.setDeviceId("deviceId");
        printRecordDO.setInvoiceType(0);
        printRecordDO.setPrinterGuid("printerGuid");
        printRecordDO.setPrintStatus(0);
        printRecordDO.setPrintStatusMsg("客户端打印超时，系统默认失败");
        printRecordDO.setPrintContent("printContent");
        final List<PrintRecordDO> printRecordsToInsert = Arrays.asList(printRecordDO);
        final PrintRecordReadDO printRecordReadDO = new PrintRecordReadDO();
        printRecordReadDO.setId(0L);
        printRecordReadDO.setRecordUid("recordUid");
        printRecordReadDO.setRecordGuid("recordGuid");
        printRecordReadDO.setInvoiceType(0);
        printRecordReadDO.setPrintStatus(0);
        final List<PrintRecordReadDO> printRecordsToCache = Arrays.asList(printRecordReadDO);
        verify(mockPrintRecordCloneService).cloneRecord(printDTO2, printers, printRecordsToInsert, printRecordsToCache);
        verify(mockCloudPrinterService).print("", "deviceNo", 0);

        // Confirm ContentCacheService.save(...).
        final PrintRecordReadDO printRecordReadDO1 = new PrintRecordReadDO();
        printRecordReadDO1.setId(0L);
        printRecordReadDO1.setRecordUid("recordUid");
        printRecordReadDO1.setRecordGuid("recordGuid");
        printRecordReadDO1.setInvoiceType(0);
        printRecordReadDO1.setPrintStatus(0);
        final List<PrintRecordReadDO> arrayOfPrintRecordReadDO = Arrays.asList(printRecordReadDO1);
        verify(mockContentCacheService).save(arrayOfPrintRecordReadDO);
        verify(mockDefaultRocketMqProducer).sendMessage(any(Message.class));
    }

    @Test
    public void testInsertRecord_PrinterRoutingServiceFindCloudPrinterAvailableReturnsNoItems() {
        // Setup
        final PrintDTO printDTO = new PrintDTO();
        printDTO.setInvoiceType(0);
        printDTO.setStoreGuid("storeGuid");
        printDTO.setTradeMode(0);
        printDTO.setOperatorStaffName("operatorStaffName");
        printDTO.setCreateTime(0L);
        printDTO.setDeviceId("masterDeviceId");
        printDTO.setAppointPrinterType(0);
        printDTO.setItemInvoiceType(0);

        // Configure PrinterRoutingService.findPrinterAvailable(...).
        final PrinterReadDO printerReadDO = new PrinterReadDO();
        printerReadDO.setPrinterGuid("printerGuid");
        printerReadDO.setPrinterType(0);
        printerReadDO.setPrintCount(0);
        printerReadDO.setIsPrintHangUp(false);
        printerReadDO.setDeviceNo("deviceNo");
        final List<PrinterReadDO> printerReadDOS = Arrays.asList(printerReadDO);
        final PrintDTO printDto = new PrintDTO();
        printDto.setInvoiceType(0);
        printDto.setStoreGuid("storeGuid");
        printDto.setTradeMode(0);
        printDto.setOperatorStaffName("operatorStaffName");
        printDto.setCreateTime(0L);
        printDto.setDeviceId("masterDeviceId");
        printDto.setAppointPrinterType(0);
        printDto.setItemInvoiceType(0);
        when(mockPrinterRoutingService.findPrinterAvailable(printDto)).thenReturn(printerReadDOS);

        // Configure PrinterRoutingService.findCloudPrinterAvailable(...).
        final PrintDTO printDTO1 = new PrintDTO();
        printDTO1.setInvoiceType(0);
        printDTO1.setStoreGuid("storeGuid");
        printDTO1.setTradeMode(0);
        printDTO1.setOperatorStaffName("operatorStaffName");
        printDTO1.setCreateTime(0L);
        printDTO1.setDeviceId("masterDeviceId");
        printDTO1.setAppointPrinterType(0);
        printDTO1.setItemInvoiceType(0);
        when(mockPrinterRoutingService.findCloudPrinterAvailable(printDTO1)).thenReturn(Collections.emptyList());

        // Run the test
        final String result = printRecordServiceImplUnderTest.insertRecord(printDTO);

        // Verify the results
        assertThat(result).isEqualTo("SUCCESS");

        // Confirm PrintRecordCloneService.cloneRecord(...).
        final PrintDTO printDTO2 = new PrintDTO();
        printDTO2.setInvoiceType(0);
        printDTO2.setStoreGuid("storeGuid");
        printDTO2.setTradeMode(0);
        printDTO2.setOperatorStaffName("operatorStaffName");
        printDTO2.setCreateTime(0L);
        printDTO2.setDeviceId("masterDeviceId");
        printDTO2.setAppointPrinterType(0);
        printDTO2.setItemInvoiceType(0);
        final PrinterReadDO printerReadDO1 = new PrinterReadDO();
        printerReadDO1.setPrinterGuid("printerGuid");
        printerReadDO1.setPrinterType(0);
        printerReadDO1.setPrintCount(0);
        printerReadDO1.setIsPrintHangUp(false);
        printerReadDO1.setDeviceNo("deviceNo");
        final List<PrinterReadDO> printers = Arrays.asList(printerReadDO1);
        final PrintRecordDO printRecordDO = new PrintRecordDO();
        printRecordDO.setRecordUid("recordUid");
        printRecordDO.setRecordGuid("recordGuid");
        printRecordDO.setStoreGuid("storeGuid");
        printRecordDO.setDeviceId("deviceId");
        printRecordDO.setInvoiceType(0);
        printRecordDO.setPrinterGuid("printerGuid");
        printRecordDO.setPrintStatus(0);
        printRecordDO.setPrintStatusMsg("客户端打印超时，系统默认失败");
        printRecordDO.setPrintContent("printContent");
        final List<PrintRecordDO> printRecordsToInsert = Arrays.asList(printRecordDO);
        final PrintRecordReadDO printRecordReadDO = new PrintRecordReadDO();
        printRecordReadDO.setId(0L);
        printRecordReadDO.setRecordUid("recordUid");
        printRecordReadDO.setRecordGuid("recordGuid");
        printRecordReadDO.setInvoiceType(0);
        printRecordReadDO.setPrintStatus(0);
        final List<PrintRecordReadDO> printRecordsToCache = Arrays.asList(printRecordReadDO);
        verify(mockPrintRecordCloneService).cloneRecord(printDTO2, printers, printRecordsToInsert, printRecordsToCache);
        verify(mockCloudPrinterService).print("", "deviceNo", 0);

        // Confirm ContentCacheService.save(...).
        final PrintRecordReadDO printRecordReadDO1 = new PrintRecordReadDO();
        printRecordReadDO1.setId(0L);
        printRecordReadDO1.setRecordUid("recordUid");
        printRecordReadDO1.setRecordGuid("recordGuid");
        printRecordReadDO1.setInvoiceType(0);
        printRecordReadDO1.setPrintStatus(0);
        final List<PrintRecordReadDO> arrayOfPrintRecordReadDO = Arrays.asList(printRecordReadDO1);
        verify(mockContentCacheService).save(arrayOfPrintRecordReadDO);
        verify(mockDefaultRocketMqProducer).sendMessage(any(Message.class));

        // Confirm PrintPushService.pushPrintTaskMsg(...).
        final PrintDTO printDTO3 = new PrintDTO();
        printDTO3.setInvoiceType(0);
        printDTO3.setStoreGuid("storeGuid");
        printDTO3.setTradeMode(0);
        printDTO3.setOperatorStaffName("operatorStaffName");
        printDTO3.setCreateTime(0L);
        printDTO3.setDeviceId("masterDeviceId");
        printDTO3.setAppointPrinterType(0);
        printDTO3.setItemInvoiceType(0);
        verify(mockPrintPushService).pushPrintTaskMsg(printDTO3, Arrays.asList("value"));
    }

    @Test
    public void testDeleteRecord() {
        // Setup
        final PrintRecordReqDTO printRecordReqDTO = new PrintRecordReqDTO();
        printRecordReqDTO.setStoreGuid("storeGuid");
        printRecordReqDTO.setMsgId("msgId");
        printRecordReqDTO.setDeviceId("deviceId");
        printRecordReqDTO.setRecordGuid("recordGuid");
        printRecordReqDTO.setPrintStatus(0);
        printRecordReqDTO.setPrintStatusMsg("printStatusMsg");
        printRecordReqDTO.setArrayOfRecordGuid(Arrays.asList("value"));

        when(mockRedisTemplate.opsForHash()).thenReturn(null);

        // Run the test
        printRecordServiceImplUnderTest.deleteRecord(printRecordReqDTO);

        // Verify the results
    }

    @Test
    public void testBatchDeleteRecord() {
        // Setup
        when(mockRedisTemplate.opsForHash()).thenReturn(null);

        // Run the test
        printRecordServiceImplUnderTest.batchDeleteRecord(Arrays.asList("value"));

        // Verify the results
    }

    @Test
    public void testDeletePrinterRecord() {
        // Setup
        // Run the test
        printRecordServiceImplUnderTest.deletePrinterRecord("printerGuid");

        // Verify the results
    }

    @Test
    public void testBatchDeletePrinterRecord() {
        // Setup
        // Run the test
        printRecordServiceImplUnderTest.batchDeletePrinterRecord(Arrays.asList("value"));

        // Verify the results
    }

    @Test
    public void testDeleteStorePrintRecord() {
        // Setup
        // Run the test
        printRecordServiceImplUnderTest.deleteStorePrintRecord("storeGuid");

        // Verify the results
    }

    @Test
    public void testDeleteHistoryRecord() {
        // Setup
        // Run the test
        printRecordServiceImplUnderTest.deleteHistoryRecord();

        // Verify the results
    }

    @Test
    public void testUpdatePrintResult() {
        // Setup
        final PrintRecordReqDTO printRecordReqDTO = new PrintRecordReqDTO();
        printRecordReqDTO.setStoreGuid("storeGuid");
        printRecordReqDTO.setMsgId("msgId");
        printRecordReqDTO.setDeviceId("deviceId");
        printRecordReqDTO.setRecordGuid("recordGuid");
        printRecordReqDTO.setPrintStatus(0);
        printRecordReqDTO.setPrintStatusMsg("printStatusMsg");
        printRecordReqDTO.setArrayOfRecordGuid(Arrays.asList("value"));

        // Configure PrintRecordMapstruct.fromPrintRecordReqDTO(...).
        final PrintRecordDO printRecordDO = new PrintRecordDO();
        printRecordDO.setRecordUid("recordUid");
        printRecordDO.setRecordGuid("recordGuid");
        printRecordDO.setStoreGuid("storeGuid");
        printRecordDO.setDeviceId("deviceId");
        printRecordDO.setInvoiceType(0);
        printRecordDO.setPrinterGuid("printerGuid");
        printRecordDO.setPrintStatus(0);
        printRecordDO.setPrintStatusMsg("客户端打印超时，系统默认失败");
        printRecordDO.setPrintContent("printContent");
        final PrintRecordReqDTO printRecordReqDTO1 = new PrintRecordReqDTO();
        printRecordReqDTO1.setStoreGuid("storeGuid");
        printRecordReqDTO1.setMsgId("msgId");
        printRecordReqDTO1.setDeviceId("deviceId");
        printRecordReqDTO1.setRecordGuid("recordGuid");
        printRecordReqDTO1.setPrintStatus(0);
        printRecordReqDTO1.setPrintStatusMsg("printStatusMsg");
        printRecordReqDTO1.setArrayOfRecordGuid(Arrays.asList("value"));
        when(mockPrintRecordMapstruct.fromPrintRecordReqDTO(printRecordReqDTO1)).thenReturn(printRecordDO);

        when(mockRedisTemplate.opsForHash()).thenReturn(null);

        // Run the test
        printRecordServiceImplUnderTest.updatePrintResult(printRecordReqDTO);

        // Verify the results
        // Confirm PrintPushService.pushPrintFailedMsg(...).
        final PrintRecordReqDTO printRecordReqDTO2 = new PrintRecordReqDTO();
        printRecordReqDTO2.setStoreGuid("storeGuid");
        printRecordReqDTO2.setMsgId("msgId");
        printRecordReqDTO2.setDeviceId("deviceId");
        printRecordReqDTO2.setRecordGuid("recordGuid");
        printRecordReqDTO2.setPrintStatus(0);
        printRecordReqDTO2.setPrintStatusMsg("printStatusMsg");
        printRecordReqDTO2.setArrayOfRecordGuid(Arrays.asList("value"));
        final PrintRecordDO printRecordDO1 = new PrintRecordDO();
        printRecordDO1.setRecordUid("recordUid");
        printRecordDO1.setRecordGuid("recordGuid");
        printRecordDO1.setStoreGuid("storeGuid");
        printRecordDO1.setDeviceId("deviceId");
        printRecordDO1.setInvoiceType(0);
        printRecordDO1.setPrinterGuid("printerGuid");
        printRecordDO1.setPrintStatus(0);
        printRecordDO1.setPrintStatusMsg("客户端打印超时，系统默认失败");
        printRecordDO1.setPrintContent("printContent");
        verify(mockPrintPushService).pushPrintFailedMsg(printRecordReqDTO2, printRecordDO1, 0);

        // Confirm PrintPushService.pushPrintSucceedMsg(...).
        final PrintRecordReqDTO printRecordReqDTO3 = new PrintRecordReqDTO();
        printRecordReqDTO3.setStoreGuid("storeGuid");
        printRecordReqDTO3.setMsgId("msgId");
        printRecordReqDTO3.setDeviceId("deviceId");
        printRecordReqDTO3.setRecordGuid("recordGuid");
        printRecordReqDTO3.setPrintStatus(0);
        printRecordReqDTO3.setPrintStatusMsg("printStatusMsg");
        printRecordReqDTO3.setArrayOfRecordGuid(Arrays.asList("value"));
        final PrintRecordDO printRecordDO2 = new PrintRecordDO();
        printRecordDO2.setRecordUid("recordUid");
        printRecordDO2.setRecordGuid("recordGuid");
        printRecordDO2.setStoreGuid("storeGuid");
        printRecordDO2.setDeviceId("deviceId");
        printRecordDO2.setInvoiceType(0);
        printRecordDO2.setPrinterGuid("printerGuid");
        printRecordDO2.setPrintStatus(0);
        printRecordDO2.setPrintStatusMsg("客户端打印超时，系统默认失败");
        printRecordDO2.setPrintContent("printContent");
        verify(mockPrintPushService).pushPrintSucceedMsg(printRecordReqDTO3, printRecordDO2, 0);
    }

    @Test
    public void testUpdatePendingResult() {
        // Setup
        when(mockRedisTemplate.opsForHash()).thenReturn(null);

        // Run the test
        printRecordServiceImplUnderTest.updatePendingResult("recordGuid");

        // Verify the results
        // Confirm PrintPushService.pushPrintFailedMsg(...).
        final PrintRecordReqDTO printRecordReqDTO = new PrintRecordReqDTO();
        printRecordReqDTO.setStoreGuid("storeGuid");
        printRecordReqDTO.setMsgId("msgId");
        printRecordReqDTO.setDeviceId("deviceId");
        printRecordReqDTO.setRecordGuid("recordGuid");
        printRecordReqDTO.setPrintStatus(0);
        printRecordReqDTO.setPrintStatusMsg("printStatusMsg");
        printRecordReqDTO.setArrayOfRecordGuid(Arrays.asList("value"));
        final PrintRecordDO printRecordDO = new PrintRecordDO();
        printRecordDO.setRecordUid("recordUid");
        printRecordDO.setRecordGuid("recordGuid");
        printRecordDO.setStoreGuid("storeGuid");
        printRecordDO.setDeviceId("deviceId");
        printRecordDO.setInvoiceType(0);
        printRecordDO.setPrinterGuid("printerGuid");
        printRecordDO.setPrintStatus(0);
        printRecordDO.setPrintStatusMsg("客户端打印超时，系统默认失败");
        printRecordDO.setPrintContent("printContent");
        verify(mockPrintPushService).pushPrintFailedMsg(printRecordReqDTO, printRecordDO, 0);
    }

    @Test
    public void testListRecord() {
        // Setup
        final PrintRecordReqDTO printRecordReqDTO = new PrintRecordReqDTO();
        printRecordReqDTO.setStoreGuid("storeGuid");
        printRecordReqDTO.setMsgId("msgId");
        printRecordReqDTO.setDeviceId("deviceId");
        printRecordReqDTO.setRecordGuid("recordGuid");
        printRecordReqDTO.setPrintStatus(0);
        printRecordReqDTO.setPrintStatusMsg("printStatusMsg");
        printRecordReqDTO.setArrayOfRecordGuid(Arrays.asList("value"));

        final PrintRecordDTO printRecordDTO = new PrintRecordDTO();
        printRecordDTO.setRecordUid("recordUid");
        printRecordDTO.setRecordGuid("recordGuid");
        printRecordDTO.setStoreGuid("storeGuid");
        printRecordDTO.setDeviceId("deviceId");
        printRecordDTO.setBeginTime("beginTime");
        final List<PrintRecordDTO> expectedResult = Arrays.asList(printRecordDTO);

        // Configure PrintRecordMapstruct.fromPrintRecordReqDTO(...).
        final PrintRecordDO printRecordDO = new PrintRecordDO();
        printRecordDO.setRecordUid("recordUid");
        printRecordDO.setRecordGuid("recordGuid");
        printRecordDO.setStoreGuid("storeGuid");
        printRecordDO.setDeviceId("deviceId");
        printRecordDO.setInvoiceType(0);
        printRecordDO.setPrinterGuid("printerGuid");
        printRecordDO.setPrintStatus(0);
        printRecordDO.setPrintStatusMsg("客户端打印超时，系统默认失败");
        printRecordDO.setPrintContent("printContent");
        final PrintRecordReqDTO printRecordReqDTO1 = new PrintRecordReqDTO();
        printRecordReqDTO1.setStoreGuid("storeGuid");
        printRecordReqDTO1.setMsgId("msgId");
        printRecordReqDTO1.setDeviceId("deviceId");
        printRecordReqDTO1.setRecordGuid("recordGuid");
        printRecordReqDTO1.setPrintStatus(0);
        printRecordReqDTO1.setPrintStatusMsg("printStatusMsg");
        printRecordReqDTO1.setArrayOfRecordGuid(Arrays.asList("value"));
        when(mockPrintRecordMapstruct.fromPrintRecordReqDTO(printRecordReqDTO1)).thenReturn(printRecordDO);

        when(mockPrinterService.isMasterPrinterDevice("storeGuid", "deviceId")).thenReturn(false);

        // Configure PrintRecordMapstruct.mapRecordDTO(...).
        final PrintRecordDTO printRecordDTO1 = new PrintRecordDTO();
        printRecordDTO1.setRecordUid("recordUid");
        printRecordDTO1.setRecordGuid("recordGuid");
        printRecordDTO1.setStoreGuid("storeGuid");
        printRecordDTO1.setDeviceId("deviceId");
        printRecordDTO1.setBeginTime("beginTime");
        final PrintRecordDO printRecordDo = new PrintRecordDO();
        printRecordDo.setRecordUid("recordUid");
        printRecordDo.setRecordGuid("recordGuid");
        printRecordDo.setStoreGuid("storeGuid");
        printRecordDo.setDeviceId("deviceId");
        printRecordDo.setInvoiceType(0);
        printRecordDo.setPrinterGuid("printerGuid");
        printRecordDo.setPrintStatus(0);
        printRecordDo.setPrintStatusMsg("客户端打印超时，系统默认失败");
        printRecordDo.setPrintContent("printContent");
        when(mockPrintRecordMapstruct.mapRecordDTO(printRecordDo)).thenReturn(printRecordDTO1);

        // Configure PrintRecordRespService.do2Dto(...).
        final PrintRecordDTO printRecordDTO2 = new PrintRecordDTO();
        printRecordDTO2.setRecordUid("recordUid");
        printRecordDTO2.setRecordGuid("recordGuid");
        printRecordDTO2.setStoreGuid("storeGuid");
        printRecordDTO2.setDeviceId("deviceId");
        printRecordDTO2.setBeginTime("beginTime");
        final PrintRecordReadDO printRecordReadDO = new PrintRecordReadDO();
        printRecordReadDO.setId(0L);
        printRecordReadDO.setRecordUid("recordUid");
        printRecordReadDO.setRecordGuid("recordGuid");
        printRecordReadDO.setInvoiceType(0);
        printRecordReadDO.setPrintStatus(0);
        when(mockPrintRecordRespService.do2Dto(printRecordReadDO)).thenReturn(printRecordDTO2);

        // Run the test
        final List<PrintRecordDTO> result = printRecordServiceImplUnderTest.listRecord(printRecordReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetPrintOrder() {
        // Setup
        final PrintRecordReqDTO printRecordReqDTO = new PrintRecordReqDTO();
        printRecordReqDTO.setStoreGuid("storeGuid");
        printRecordReqDTO.setMsgId("msgId");
        printRecordReqDTO.setDeviceId("deviceId");
        printRecordReqDTO.setRecordGuid("recordGuid");
        printRecordReqDTO.setPrintStatus(0);
        printRecordReqDTO.setPrintStatusMsg("printStatusMsg");
        printRecordReqDTO.setArrayOfRecordGuid(Arrays.asList("value"));

        final PrintOrderDTO printOrderDTO = new PrintOrderDTO();
        printOrderDTO.setPrintKey("printKey");
        printOrderDTO.setBusinessType(0);
        printOrderDTO.setPrinterType(0);
        printOrderDTO.setPrinterIp("printerIp");
        printOrderDTO.setPrinterPort(0);
        final List<PrintOrderDTO> expectedResult = Arrays.asList(printOrderDTO);
        when(mockContentCacheService.hasMsgId("msgId", "recordGuid", Arrays.asList("value"))).thenReturn(false);

        // Configure PrintRecordMapstruct.printRecordReqToQuery(...).
        final PrintRecordQuery printRecordQuery = new PrintRecordQuery();
        printRecordQuery.setRecordGuid("recordGuid");
        printRecordQuery.setArrayOfRecordGuid(Arrays.asList("value"));
        final PrintRecordReqDTO printRecordReqDTO1 = new PrintRecordReqDTO();
        printRecordReqDTO1.setStoreGuid("storeGuid");
        printRecordReqDTO1.setMsgId("msgId");
        printRecordReqDTO1.setDeviceId("deviceId");
        printRecordReqDTO1.setRecordGuid("recordGuid");
        printRecordReqDTO1.setPrintStatus(0);
        printRecordReqDTO1.setPrintStatusMsg("printStatusMsg");
        printRecordReqDTO1.setArrayOfRecordGuid(Arrays.asList("value"));
        when(mockPrintRecordMapstruct.printRecordReqToQuery(printRecordReqDTO1)).thenReturn(printRecordQuery);

        // Configure ContentCacheService.popSingle(...).
        final PrintRecordReadDO printRecordReadDO = new PrintRecordReadDO();
        printRecordReadDO.setId(0L);
        printRecordReadDO.setRecordUid("recordUid");
        printRecordReadDO.setRecordGuid("recordGuid");
        printRecordReadDO.setInvoiceType(0);
        printRecordReadDO.setPrintStatus(0);
        when(mockContentCacheService.popSingle("recordGuid")).thenReturn(printRecordReadDO);

        // Configure PrintRecordRespService.getOrderByPerDO(...).
        final PrintOrderDTO printOrderDTO1 = new PrintOrderDTO();
        printOrderDTO1.setPrintKey("printKey");
        printOrderDTO1.setBusinessType(0);
        printOrderDTO1.setPrinterType(0);
        printOrderDTO1.setPrinterIp("printerIp");
        printOrderDTO1.setPrinterPort(0);
        final PrintRecordReadDO printRecordReadDO1 = new PrintRecordReadDO();
        printRecordReadDO1.setId(0L);
        printRecordReadDO1.setRecordUid("recordUid");
        printRecordReadDO1.setRecordGuid("recordGuid");
        printRecordReadDO1.setInvoiceType(0);
        printRecordReadDO1.setPrintStatus(0);
        when(mockPrintRecordRespService.getOrderByPerDO(printRecordReadDO1)).thenReturn(printOrderDTO1);

        // Configure ContentCacheService.popBatch(...).
        final PrintRecordReadDO printRecordReadDO2 = new PrintRecordReadDO();
        printRecordReadDO2.setId(0L);
        printRecordReadDO2.setRecordUid("recordUid");
        printRecordReadDO2.setRecordGuid("recordGuid");
        printRecordReadDO2.setInvoiceType(0);
        printRecordReadDO2.setPrintStatus(0);
        final List<PrintRecordReadDO> printRecordReadDOS = Arrays.asList(printRecordReadDO2);
        when(mockContentCacheService.popBatch(Arrays.asList("value"))).thenReturn(printRecordReadDOS);

        // Run the test
        final List<PrintOrderDTO> result = printRecordServiceImplUnderTest.getPrintOrder(printRecordReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockContentCacheService).saveMsgId("msgId", "recordGuid", Arrays.asList("value"));
    }

    @Test
    public void testGetPrintOrder_ContentCacheServiceHasMsgIdReturnsTrue() {
        // Setup
        final PrintRecordReqDTO printRecordReqDTO = new PrintRecordReqDTO();
        printRecordReqDTO.setStoreGuid("storeGuid");
        printRecordReqDTO.setMsgId("msgId");
        printRecordReqDTO.setDeviceId("deviceId");
        printRecordReqDTO.setRecordGuid("recordGuid");
        printRecordReqDTO.setPrintStatus(0);
        printRecordReqDTO.setPrintStatusMsg("printStatusMsg");
        printRecordReqDTO.setArrayOfRecordGuid(Arrays.asList("value"));

        when(mockContentCacheService.hasMsgId("msgId", "recordGuid", Arrays.asList("value"))).thenReturn(true);

        // Run the test
        final List<PrintOrderDTO> result = printRecordServiceImplUnderTest.getPrintOrder(printRecordReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testGetPrintOrder_ContentCacheServicePopSingleReturnsNull() {
        // Setup
        final PrintRecordReqDTO printRecordReqDTO = new PrintRecordReqDTO();
        printRecordReqDTO.setStoreGuid("storeGuid");
        printRecordReqDTO.setMsgId("msgId");
        printRecordReqDTO.setDeviceId("deviceId");
        printRecordReqDTO.setRecordGuid("recordGuid");
        printRecordReqDTO.setPrintStatus(0);
        printRecordReqDTO.setPrintStatusMsg("printStatusMsg");
        printRecordReqDTO.setArrayOfRecordGuid(Arrays.asList("value"));

        when(mockContentCacheService.hasMsgId("msgId", "recordGuid", Arrays.asList("value"))).thenReturn(false);

        // Configure PrintRecordMapstruct.printRecordReqToQuery(...).
        final PrintRecordQuery printRecordQuery = new PrintRecordQuery();
        printRecordQuery.setRecordGuid("recordGuid");
        printRecordQuery.setArrayOfRecordGuid(Arrays.asList("value"));
        final PrintRecordReqDTO printRecordReqDTO1 = new PrintRecordReqDTO();
        printRecordReqDTO1.setStoreGuid("storeGuid");
        printRecordReqDTO1.setMsgId("msgId");
        printRecordReqDTO1.setDeviceId("deviceId");
        printRecordReqDTO1.setRecordGuid("recordGuid");
        printRecordReqDTO1.setPrintStatus(0);
        printRecordReqDTO1.setPrintStatusMsg("printStatusMsg");
        printRecordReqDTO1.setArrayOfRecordGuid(Arrays.asList("value"));
        when(mockPrintRecordMapstruct.printRecordReqToQuery(printRecordReqDTO1)).thenReturn(printRecordQuery);

        when(mockContentCacheService.popSingle("recordGuid")).thenReturn(null);

        // Run the test
        final List<PrintOrderDTO> result = printRecordServiceImplUnderTest.getPrintOrder(printRecordReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
        verify(mockContentCacheService).saveMsgId("msgId", "recordGuid", Arrays.asList("value"));
    }

    @Test
    public void testGetPrintOrder_ContentCacheServicePopBatchReturnsNoItems() {
        // Setup
        final PrintRecordReqDTO printRecordReqDTO = new PrintRecordReqDTO();
        printRecordReqDTO.setStoreGuid("storeGuid");
        printRecordReqDTO.setMsgId("msgId");
        printRecordReqDTO.setDeviceId("deviceId");
        printRecordReqDTO.setRecordGuid("recordGuid");
        printRecordReqDTO.setPrintStatus(0);
        printRecordReqDTO.setPrintStatusMsg("printStatusMsg");
        printRecordReqDTO.setArrayOfRecordGuid(Arrays.asList("value"));

        final PrintOrderDTO printOrderDTO = new PrintOrderDTO();
        printOrderDTO.setPrintKey("printKey");
        printOrderDTO.setBusinessType(0);
        printOrderDTO.setPrinterType(0);
        printOrderDTO.setPrinterIp("printerIp");
        printOrderDTO.setPrinterPort(0);
        final List<PrintOrderDTO> expectedResult = Arrays.asList(printOrderDTO);
        when(mockContentCacheService.hasMsgId("msgId", "recordGuid", Arrays.asList("value"))).thenReturn(false);

        // Configure PrintRecordMapstruct.printRecordReqToQuery(...).
        final PrintRecordQuery printRecordQuery = new PrintRecordQuery();
        printRecordQuery.setRecordGuid("recordGuid");
        printRecordQuery.setArrayOfRecordGuid(Arrays.asList("value"));
        final PrintRecordReqDTO printRecordReqDTO1 = new PrintRecordReqDTO();
        printRecordReqDTO1.setStoreGuid("storeGuid");
        printRecordReqDTO1.setMsgId("msgId");
        printRecordReqDTO1.setDeviceId("deviceId");
        printRecordReqDTO1.setRecordGuid("recordGuid");
        printRecordReqDTO1.setPrintStatus(0);
        printRecordReqDTO1.setPrintStatusMsg("printStatusMsg");
        printRecordReqDTO1.setArrayOfRecordGuid(Arrays.asList("value"));
        when(mockPrintRecordMapstruct.printRecordReqToQuery(printRecordReqDTO1)).thenReturn(printRecordQuery);

        when(mockContentCacheService.popBatch(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Configure PrintRecordRespService.getOrderByPerDO(...).
        final PrintOrderDTO printOrderDTO1 = new PrintOrderDTO();
        printOrderDTO1.setPrintKey("printKey");
        printOrderDTO1.setBusinessType(0);
        printOrderDTO1.setPrinterType(0);
        printOrderDTO1.setPrinterIp("printerIp");
        printOrderDTO1.setPrinterPort(0);
        final PrintRecordReadDO printRecordReadDO = new PrintRecordReadDO();
        printRecordReadDO.setId(0L);
        printRecordReadDO.setRecordUid("recordUid");
        printRecordReadDO.setRecordGuid("recordGuid");
        printRecordReadDO.setInvoiceType(0);
        printRecordReadDO.setPrintStatus(0);
        when(mockPrintRecordRespService.getOrderByPerDO(printRecordReadDO)).thenReturn(printOrderDTO1);

        // Run the test
        final List<PrintOrderDTO> result = printRecordServiceImplUnderTest.getPrintOrder(printRecordReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockContentCacheService).saveMsgId("msgId", "recordGuid", Arrays.asList("value"));
    }

    @Test
    public void testGetTestPrintOrder() {
        // Setup
        final FormatDTO formatDTO = new FormatDTO();
        formatDTO.setGuid("f175e299-bee7-4cd7-be31-a8ada6268690");
        formatDTO.setStoreGuid("storeGuid");
        formatDTO.setInvoiceType(0);
        formatDTO.setPageSize(0);
        formatDTO.setDeviceId("deviceId");

        final PrintOrderDTO expectedResult = new PrintOrderDTO();
        expectedResult.setPrintKey("printKey");
        expectedResult.setBusinessType(0);
        expectedResult.setPrinterType(0);
        expectedResult.setPrinterIp("printerIp");
        expectedResult.setPrinterPort(0);

        // Configure PrinterService.findTestOrderPrinter(...).
        final PrinterDTO printerDTO = new PrinterDTO();
        printerDTO.setStoreGuid("storeGuid");
        printerDTO.setStoreName("storeName");
        printerDTO.setDeviceId("deviceId");
        printerDTO.setPrinterGuid("printerGuid");
        printerDTO.setPrintPage("printPage");
        when(mockPrinterService.findTestOrderPrinter("storeGuid", "deviceId", 0, 0)).thenReturn(printerDTO);

        // Configure PrintRecordRespService.getOrderByPerMock(...).
        final PrintOrderDTO printOrderDTO = new PrintOrderDTO();
        printOrderDTO.setPrintKey("printKey");
        printOrderDTO.setBusinessType(0);
        printOrderDTO.setPrinterType(0);
        printOrderDTO.setPrinterIp("printerIp");
        printOrderDTO.setPrinterPort(0);
        final FormatDTO formatDTO1 = new FormatDTO();
        formatDTO1.setGuid("f175e299-bee7-4cd7-be31-a8ada6268690");
        formatDTO1.setStoreGuid("storeGuid");
        formatDTO1.setInvoiceType(0);
        formatDTO1.setPageSize(0);
        formatDTO1.setDeviceId("deviceId");
        final PrinterDTO printerDTO1 = new PrinterDTO();
        printerDTO1.setStoreGuid("storeGuid");
        printerDTO1.setStoreName("storeName");
        printerDTO1.setDeviceId("deviceId");
        printerDTO1.setPrinterGuid("printerGuid");
        printerDTO1.setPrintPage("printPage");
        when(mockPrintRecordRespService.getOrderByPerMock(0, 0, formatDTO1, printerDTO1)).thenReturn(printOrderDTO);

        // Run the test
        final PrintOrderDTO result = printRecordServiceImplUnderTest.getTestPrintOrder(formatDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetTestPrintOrders() {
        // Setup
        final FormatDTO formatDTO = new FormatDTO();
        formatDTO.setGuid("f175e299-bee7-4cd7-be31-a8ada6268690");
        formatDTO.setStoreGuid("storeGuid");
        formatDTO.setInvoiceType(0);
        formatDTO.setPageSize(0);
        formatDTO.setDeviceId("deviceId");

        final PrintOrderDTO printOrderDTO = new PrintOrderDTO();
        printOrderDTO.setPrintKey("printKey");
        printOrderDTO.setBusinessType(0);
        printOrderDTO.setPrinterType(0);
        printOrderDTO.setPrinterIp("printerIp");
        printOrderDTO.setPrinterPort(0);
        final List<PrintOrderDTO> expectedResult = Arrays.asList(printOrderDTO);

        // Configure PrinterService.findTestOrderPrinters(...).
        final PrinterDTO printerDTO = new PrinterDTO();
        printerDTO.setStoreGuid("storeGuid");
        printerDTO.setStoreName("storeName");
        printerDTO.setDeviceId("deviceId");
        printerDTO.setPrinterGuid("printerGuid");
        printerDTO.setPrintPage("printPage");
        final List<PrinterDTO> printerDTOS = Arrays.asList(printerDTO);
        when(mockPrinterService.findTestOrderPrinters("storeGuid", "deviceId", 0)).thenReturn(printerDTOS);

        // Configure PrintRecordRespService.getOrderByPerMock(...).
        final PrintOrderDTO printOrderDTO1 = new PrintOrderDTO();
        printOrderDTO1.setPrintKey("printKey");
        printOrderDTO1.setBusinessType(0);
        printOrderDTO1.setPrinterType(0);
        printOrderDTO1.setPrinterIp("printerIp");
        printOrderDTO1.setPrinterPort(0);
        final FormatDTO formatDTO1 = new FormatDTO();
        formatDTO1.setGuid("f175e299-bee7-4cd7-be31-a8ada6268690");
        formatDTO1.setStoreGuid("storeGuid");
        formatDTO1.setInvoiceType(0);
        formatDTO1.setPageSize(0);
        formatDTO1.setDeviceId("deviceId");
        final PrinterDTO printerDTO1 = new PrinterDTO();
        printerDTO1.setStoreGuid("storeGuid");
        printerDTO1.setStoreName("storeName");
        printerDTO1.setDeviceId("deviceId");
        printerDTO1.setPrinterGuid("printerGuid");
        printerDTO1.setPrintPage("printPage");
        when(mockPrintRecordRespService.getOrderByPerMock(0, 0, formatDTO1, printerDTO1)).thenReturn(printOrderDTO1);

        // Run the test
        final List<PrintOrderDTO> result = printRecordServiceImplUnderTest.getTestPrintOrders(formatDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetTestPrintOrders_PrinterServiceReturnsNoItems() {
        // Setup
        final FormatDTO formatDTO = new FormatDTO();
        formatDTO.setGuid("f175e299-bee7-4cd7-be31-a8ada6268690");
        formatDTO.setStoreGuid("storeGuid");
        formatDTO.setInvoiceType(0);
        formatDTO.setPageSize(0);
        formatDTO.setDeviceId("deviceId");

        when(mockPrinterService.findTestOrderPrinters("storeGuid", "deviceId", 0)).thenReturn(Collections.emptyList());

        // Run the test
        final List<PrintOrderDTO> result = printRecordServiceImplUnderTest.getTestPrintOrders(formatDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testReprintTakeaway() {
        // Setup
        when(mockRedisTemplate.opsForHash()).thenReturn(null);

        // Run the test
        printRecordServiceImplUnderTest.reprintTakeaway("storeGuid", "masterDeviceId");

        // Verify the results
        verify(mockDefaultRocketMqProducer).sendMessage(any(Message.class));

        // Confirm PrintPushService.pushPrintTaskMsg(...).
        final PrintDTO printDTO = new PrintDTO();
        printDTO.setInvoiceType(0);
        printDTO.setStoreGuid("storeGuid");
        printDTO.setTradeMode(0);
        printDTO.setOperatorStaffName("operatorStaffName");
        printDTO.setCreateTime(0L);
        printDTO.setDeviceId("masterDeviceId");
        printDTO.setAppointPrinterType(0);
        printDTO.setItemInvoiceType(0);
        verify(mockPrintPushService).pushPrintTaskMsg(printDTO, Arrays.asList("value"));
    }

    @Test
    public void testReprintTakeawayPrintOrderList() {
        // Setup
        final PrintOrderDTO printOrderDTO = new PrintOrderDTO();
        printOrderDTO.setPrintKey("printKey");
        printOrderDTO.setBusinessType(0);
        printOrderDTO.setPrinterType(0);
        printOrderDTO.setPrinterIp("printerIp");
        printOrderDTO.setPrinterPort(0);
        final List<PrintOrderDTO> expectedResult = Arrays.asList(printOrderDTO);
        when(mockRedisTemplate.opsForHash()).thenReturn(null);

        // Configure PrintRecordRespService.getOrderByPerDO(...).
        final PrintOrderDTO printOrderDTO1 = new PrintOrderDTO();
        printOrderDTO1.setPrintKey("printKey");
        printOrderDTO1.setBusinessType(0);
        printOrderDTO1.setPrinterType(0);
        printOrderDTO1.setPrinterIp("printerIp");
        printOrderDTO1.setPrinterPort(0);
        final PrintRecordReadDO printRecordReadDO = new PrintRecordReadDO();
        printRecordReadDO.setId(0L);
        printRecordReadDO.setRecordUid("recordUid");
        printRecordReadDO.setRecordGuid("recordGuid");
        printRecordReadDO.setInvoiceType(0);
        printRecordReadDO.setPrintStatus(0);
        when(mockPrintRecordRespService.getOrderByPerDO(printRecordReadDO)).thenReturn(printOrderDTO1);

        // Run the test
        final List<PrintOrderDTO> result = printRecordServiceImplUnderTest.reprintTakeawayPrintOrderList("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockRedisTemplate).delete("key");
    }
}
