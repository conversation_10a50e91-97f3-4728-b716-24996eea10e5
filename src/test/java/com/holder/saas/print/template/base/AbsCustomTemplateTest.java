package com.holder.saas.print.template.base;

import com.holderzone.saas.store.dto.print.content.PrintDTO;
import com.holderzone.saas.store.dto.print.format.FormatDTO;
import com.holderzone.saas.store.dto.print.template.PrintRow;
import com.holderzone.saas.store.dto.print.template.printable.BarCode;
import org.junit.Before;
import org.junit.Test;

import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

public class AbsCustomTemplateTest {

    private AbsCustomTemplate<PrintDTO, FormatDTO> absCustomTemplateUnderTest;

    @Before
    public void setUp() throws Exception {
        absCustomTemplateUnderTest = new AbsCustomTemplate<PrintDTO, FormatDTO>() {
            @Override
            public List<PrintRow> getContent() {
                return null;
            }

            @Override
            public String getFailedMsg() {
                return null;
            }
        };
    }

    @Test
    public void testGetHeader() {
        // Setup
        final PrintRow printRow = new PrintRow();
        printRow.setContentType("contentType");
        final BarCode barCode = new BarCode();
        barCode.setContent("content");
        barCode.setHeight(0);
        barCode.setMargin(0);
        printRow.setBarCode(barCode);
        final List<PrintRow> expectedResult = Arrays.asList(printRow);

        // Run the test
        final List<PrintRow> result = absCustomTemplateUnderTest.getHeader();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetFooter() {
        // Setup
        final PrintRow printRow = new PrintRow();
        printRow.setContentType("contentType");
        final BarCode barCode = new BarCode();
        barCode.setContent("content");
        barCode.setHeight(0);
        barCode.setMargin(0);
        printRow.setBarCode(barCode);
        final List<PrintRow> expectedResult = Arrays.asList(printRow);

        // Run the test
        final List<PrintRow> result = absCustomTemplateUnderTest.getFooter();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
