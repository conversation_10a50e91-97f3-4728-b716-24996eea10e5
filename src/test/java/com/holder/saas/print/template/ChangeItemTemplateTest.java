package com.holder.saas.print.template;

import com.holderzone.saas.store.dto.print.template.PrintRow;
import com.holderzone.saas.store.dto.print.template.printable.BarCode;
import com.holderzone.saas.store.dto.print.template.printable.KeyValue;
import com.holderzone.saas.store.dto.print.template.printable.Section;
import com.holderzone.saas.store.dto.print.template.printable.Table;
import org.junit.Before;
import org.junit.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.assertEquals;

public class ChangeItemTemplateTest {

    private ChangeItemTemplate changeItemTemplateUnderTest;

    @Before
    public void setUp() throws Exception {
        changeItemTemplateUnderTest = new ChangeItemTemplate();
    }

    @Test
    public void testGetContent() {
        // Setup
        final PrintRow printRow = new PrintRow();
        printRow.setContentType("type");
        final BarCode barCode = new BarCode();
        printRow.setBarCode(barCode);
        final KeyValue keyValue = new KeyValue();
        printRow.setKeyValue(keyValue);
        final Section section = new Section();
        printRow.setSection(section);
        final Table table = new Table();
        printRow.setTable(table);
        final List<PrintRow> expectedResult = Arrays.asList(printRow);

        // Run the test
        final List<PrintRow> result = changeItemTemplateUnderTest.getContent();

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetFailedMsg() {
        assertEquals("换菜单打印失败，请及时处理", changeItemTemplateUnderTest.getFailedMsg());
    }
}
