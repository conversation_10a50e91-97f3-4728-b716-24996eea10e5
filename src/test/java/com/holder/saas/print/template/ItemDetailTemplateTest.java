package com.holder.saas.print.template;

import com.holderzone.saas.store.dto.print.template.PrintRow;
import com.holderzone.saas.store.dto.print.template.printable.BarCode;
import org.junit.Before;
import org.junit.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.assertEquals;

public class ItemDetailTemplateTest {

    private ItemDetailTemplate itemDetailTemplateUnderTest;

    @Before
    public void setUp() throws Exception {
        itemDetailTemplateUnderTest = new ItemDetailTemplate();
    }

    @Test
    public void testGetContent() throws Exception {
        // Setup
        final PrintRow printRow = new PrintRow();
        printRow.setContentType("contentType");
        final BarCode barCode = new BarCode();
        barCode.setContent("content");
        barCode.setHeight(0);
        barCode.setMargin(0);
        printRow.setBarCode(barCode);
        final List<PrintRow> expectedResult = Arrays.asList(printRow);

        // Run the test
        final List<PrintRow> result = itemDetailTemplateUnderTest.getContent();

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetFailedMsg() {
        // Setup
        // Run the test
        final String result = itemDetailTemplateUnderTest.getFailedMsg();

        // Verify the results
        assertEquals("result", result);
    }
}
