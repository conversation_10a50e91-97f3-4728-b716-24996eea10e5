package com.holder.saas.print.template;

import com.holderzone.saas.store.dto.print.template.PrintRow;
import com.holderzone.saas.store.dto.print.template.printable.KeyValue;
import com.holderzone.saas.store.dto.print.template.printable.Section;
import com.holderzone.saas.store.dto.print.template.printable.Separator;
import org.junit.Before;
import org.junit.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.assertEquals;

public class MemConsuTemplateTest {

    private MemConsuTemplate memConsuTemplateUnderTest;

    @Before
    public void setUp() throws Exception {
        memConsuTemplateUnderTest = new MemConsuTemplate();
    }

    @Test
    public void testGetContent() throws Exception {
        // Setup
        final PrintRow printRow = new PrintRow();
        printRow.setContentType("type");
        final KeyValue keyValue = new KeyValue();
        keyValue.setAlignEdges(false);
        printRow.setKeyValue(keyValue);
        final Section section = new Section();
        printRow.setSection(section);
        final Separator separator = new Separator();
        printRow.setSeparator(separator);
        final List<PrintRow> expectedResult = Arrays.asList(printRow);

        // Run the test
        final List<PrintRow> result = memConsuTemplateUnderTest.getContent();

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetFailedMsg() throws Exception {
        assertEquals("会员消费统计单打印失败，请及时处理", memConsuTemplateUnderTest.getFailedMsg());
    }
}
