package com.holder.saas.print.template;

import com.holderzone.saas.store.dto.print.template.PrintRow;
import com.holderzone.saas.store.dto.print.template.convertable.Text;
import com.holderzone.saas.store.dto.print.template.printable.CoordinateRow;
import com.holderzone.saas.store.dto.print.template.printable.LabelBarCode;
import com.holderzone.saas.store.dto.print.template.printable.Section;
import org.junit.Before;
import org.junit.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.assertEquals;

public class ItemLabelTemplateTest {

    private ItemLabelTemplate itemLabelTemplateUnderTest;

    @Before
    public void setUp() throws Exception {
        itemLabelTemplateUnderTest = new ItemLabelTemplate();
    }

    @Test
    public void testGetContent() throws Exception {
        // Setup
        final PrintRow printRow = new PrintRow();
        printRow.setContentType("type");
        final LabelBarCode labelBarCode = new LabelBarCode();
        labelBarCode.setContent("content");
        printRow.setLabelBarCode(labelBarCode);
        final CoordinateRow coordinateRow = new CoordinateRow();
        printRow.setCoordinateRow(coordinateRow);
        final Section section = new Section();
        section.setAlign(Text.Align.Left);
        printRow.setSection(section);
        final List<PrintRow> expectedResult = Arrays.asList(printRow);

        // Run the test
        final List<PrintRow> result = itemLabelTemplateUnderTest.getContent();

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetFailedMsg() {
        assertEquals("商品标签单打印失败，请及时处理", itemLabelTemplateUnderTest.getFailedMsg());
    }
}
