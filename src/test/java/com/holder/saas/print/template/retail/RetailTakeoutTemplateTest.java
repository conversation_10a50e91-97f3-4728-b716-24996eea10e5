package com.holder.saas.print.template.retail;

import com.holderzone.saas.store.dto.print.template.PrintRow;
import com.holderzone.saas.store.dto.print.template.printable.*;
import org.junit.Before;
import org.junit.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.assertEquals;

public class RetailTakeoutTemplateTest {

    private RetailTakeoutTemplate retailTakeoutTemplateUnderTest;

    @Before
    public void setUp() throws Exception {
        retailTakeoutTemplateUnderTest = new RetailTakeoutTemplate();
    }

    @Test
    public void testGetContent() throws Exception {
        // Setup
        final PrintRow printRow = new PrintRow();
        printRow.setContentType("type");
        final BlankRow blankRow = new BlankRow();
        printRow.setBlankRow(blankRow);
        final KeyValue keyValue = new KeyValue();
        keyValue.setAlignEdges(false);
        printRow.setKeyValue(keyValue);
        final ReverseText reverseText = new ReverseText();
        printRow.setReverseText(reverseText);
        final Section section = new Section();
        printRow.setSection(section);
        final Separator separator = new Separator();
        printRow.setSeparator(separator);
        final List<PrintRow> expectedResult = Arrays.asList(printRow);

        // Run the test
        final List<PrintRow> result = retailTakeoutTemplateUnderTest.getContent();

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetFailedMsg() throws Exception {
        // Setup
        // Run the test
        final String result = retailTakeoutTemplateUnderTest.getFailedMsg();

        // Verify the results
        assertEquals("result", result);
    }
}
