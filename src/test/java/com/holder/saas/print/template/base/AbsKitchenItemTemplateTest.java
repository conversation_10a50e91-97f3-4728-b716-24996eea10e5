package com.holder.saas.print.template.base;

import com.holderzone.saas.store.dto.print.content.PrintBaseItemDTO;
import com.holderzone.saas.store.dto.print.format.FormatDTO;
import com.holderzone.saas.store.dto.print.template.PrintRow;
import com.holderzone.saas.store.dto.print.template.printable.BarCode;
import com.holderzone.saas.store.dto.print.template.printable.KeyValue;
import com.holderzone.saas.store.dto.print.template.printable.Section;
import com.holderzone.saas.store.dto.print.template.printable.Separator;
import org.junit.Before;
import org.junit.Test;

import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

public class AbsKitchenItemTemplateTest {

    private AbsKitchenItemTemplate<PrintBaseItemDTO, FormatDTO> absKitchenItemTemplateUnderTest;

    @Before
    public void setUp() throws Exception {
        absKitchenItemTemplateUnderTest = new AbsKitchenItemTemplate<PrintBaseItemDTO, FormatDTO>() {
            @Override
            protected boolean isCancel() {
                return false;
            }

            @Override
            public String getFailedMsg() {
                return null;
            }
        };
    }

    @Test
    public void testGetContent() {
        // Setup
        final PrintRow printRow = new PrintRow();
        printRow.setContentType("contentType");
        final BarCode barCode = new BarCode();
        printRow.setBarCode(barCode);
        final KeyValue keyValue = new KeyValue();
        printRow.setKeyValue(keyValue);
        final Section section = new Section();
        printRow.setSection(section);
        final Separator separator = new Separator();
        printRow.setSeparator(separator);
        final List<PrintRow> expectedResult = Arrays.asList(printRow);

        // Run the test
        final List<PrintRow> result = absKitchenItemTemplateUnderTest.getContent();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
