package com.holder.saas.print.template;

import com.holderzone.saas.store.dto.print.template.PrintRow;
import com.holderzone.saas.store.dto.print.template.printable.BarCode;
import com.holderzone.saas.store.dto.print.template.printable.KeyValue;
import com.holderzone.saas.store.dto.print.template.printable.Section;
import com.holderzone.saas.store.dto.print.template.printable.Separator;
import org.junit.Before;
import org.junit.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

public class RefundItemTemplateTest {

    private RefundItemTemplate refundItemTemplateUnderTest;

    @Before
    public void setUp() throws Exception {
        refundItemTemplateUnderTest = new RefundItemTemplate();
    }

    @Test
    public void testGetContent() throws Exception {
        // Setup
        final PrintRow printRow = new PrintRow();
        printRow.setContentType("type");
        final BarCode barCode = new BarCode();
        printRow.setBarCode(barCode);
        final KeyValue keyValue = new KeyValue();
        printRow.setKeyValue(keyValue);
        final Section section = new Section();
        printRow.setSection(section);
        final Separator separator = new Separator();
        printRow.setSeparator(separator);
        final List<PrintRow> expectedResult = Arrays.asList(printRow);

        // Run the test
        final List<PrintRow> result = refundItemTemplateUnderTest.getContent();

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testIsCancel() {
        assertTrue(refundItemTemplateUnderTest.isCancel());
    }

    @Test
    public void testGetFailedMsg() throws Exception {
        // Setup
        // Run the test
        final String result = refundItemTemplateUnderTest.getFailedMsg();

        // Verify the results
        assertEquals("result", result);
    }
}
