package com.holder.saas.print.template.base;

import com.holderzone.saas.store.dto.print.content.PrintDTO;
import com.holderzone.saas.store.dto.print.format.FormatDTO;
import com.holderzone.saas.store.dto.print.template.PrintRow;
import com.holderzone.saas.store.dto.print.template.printable.BarCode;
import org.junit.Before;
import org.junit.Test;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.function.Consumer;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;

public class AbsPrintTemplateTest {

    private AbsPrintTemplate<PrintDTO, FormatDTO> absPrintTemplateUnderTest;

    @Before
    public void setUp() throws Exception {
        absPrintTemplateUnderTest = new AbsPrintTemplate<PrintDTO, FormatDTO>() {
            @Override
            public List<PrintRow> getContent() {
                return null;
            }

            @Override
            public String getFailedMsg() {
                return null;
            }
        };
    }

    @Test
    public void testPageSizeGetterAndSetter() {
        final int pageSize = 0;
        absPrintTemplateUnderTest.setPageSize(pageSize);
        assertThat(absPrintTemplateUnderTest.getPageSize()).isEqualTo(pageSize);
    }

    @Test
    public void testPrintDTOGetterAndSetter() {
        final PrintDTO printDTO = new PrintDTO();
        absPrintTemplateUnderTest.setPrintDTO(printDTO);
        assertThat(absPrintTemplateUnderTest.getPrintDTO()).isEqualTo(printDTO);
    }

    @Test
    public void testFormatDTOGetterAndSetter() {
        final FormatDTO formatDTO = new FormatDTO();
        absPrintTemplateUnderTest.setFormatDTO(formatDTO);
        assertThat(absPrintTemplateUnderTest.getFormatDTO()).isEqualTo(formatDTO);
    }

    @Test
    public void testGetSucceedMsg() {
        assertThat(absPrintTemplateUnderTest.getSucceedMsg()).isNull();
    }

    @Test
    public void testGetPrintRows() {
        // Setup
        final PrintRow printRow = new PrintRow();
        printRow.setContentType("contentType");
        final BarCode barCode = new BarCode();
        barCode.setContent("content");
        barCode.setHeight(0);
        barCode.setMargin(0);
        printRow.setBarCode(barCode);
        final List<PrintRow> expectedResult = Arrays.asList(printRow);

        // Run the test
        final List<PrintRow> result = absPrintTemplateUnderTest.getPrintRows();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testSetPrintDTO2() {
        // Setup
        final PrintDTO t = new PrintDTO();
        t.setInvoiceType(0);
        t.setEnterpriseGuid("enterpriseGuid");
        t.setStoreGuid("storeGuid");
        t.setPrintUid("printUid");
        t.setAreaGuid("areaGuid");

        final Consumer<PrintDTO> mockConsumer = mock(Consumer.class);

        // Run the test
        absPrintTemplateUnderTest.setPrintDTO(t, mockConsumer);

        // Verify the results
    }

    @Test
    public void testSetFormatDTO2() {
        // Setup
        final FormatDTO f = new FormatDTO();
        f.setGuid("4d2018c6-7602-486f-a213-24681c57e121");
        f.setName("name");
        f.setStoreGuid("storeGuid");
        f.setInvoiceType(0);
        f.setPageSize(0);

        final Consumer<FormatDTO> mockConsumer = mock(Consumer.class);

        // Run the test
        absPrintTemplateUnderTest.setFormatDTO(f, mockConsumer);

        // Verify the results
    }

    @Test
    public void testBuild() {
        // Setup
        // Run the test
        final PrintTemplate<PrintDTO, FormatDTO> result = absPrintTemplateUnderTest.build();

        // Verify the results
    }

    @Test
    public void testBuildAware() {
        // Setup
        // Run the test
        final PrintTemplateAware<PrintDTO, FormatDTO> result = absPrintTemplateUnderTest.buildAware();

        // Verify the results
    }

    @Test
    public void testGetHeader() {
        assertThat(absPrintTemplateUnderTest.getHeader()).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testGetFooter() {
        assertThat(absPrintTemplateUnderTest.getFooter()).isEqualTo(Collections.emptyList());
    }
}
