package com.holder.saas.print.template.cloud;

import com.holder.saas.print.entity.domain.PrintRecordDO;
import org.junit.Test;

import static org.assertj.core.api.Assertions.assertThat;

public class CloudCheckOutTemplateTest {

    @Test
    public void testGetContent() {
        // Setup
        final PrintRecordDO printRecordDO = new PrintRecordDO();
        printRecordDO.setId(0L);
        printRecordDO.setRecordUid("recordUid");
        printRecordDO.setRecordGuid("recordGuid");
        printRecordDO.setStoreGuid("storeGuid");
        printRecordDO.setPrintContent("printContent");

        // Run the test
        final String result = CloudCheckOutTemplate.getContent(printRecordDO);

        // Verify the results
        assertThat(result).isEqualTo("result");
    }
}
