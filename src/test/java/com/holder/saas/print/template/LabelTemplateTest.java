package com.holder.saas.print.template;

import com.holderzone.saas.store.dto.print.template.PrintRow;
import com.holderzone.saas.store.dto.print.template.printable.BarCode;
import com.holderzone.saas.store.dto.print.template.printable.CoordinateRow;
import com.holderzone.saas.store.dto.print.template.printable.Section;
import org.junit.Before;
import org.junit.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.assertEquals;

public class LabelTemplateTest {

    private LabelTemplate labelTemplateUnderTest;

    @Before
    public void setUp() throws Exception {
        labelTemplateUnderTest = new LabelTemplate();
    }

    @Test
    public void testGetContent() throws Exception {
        // Setup
        final PrintRow printRow = new PrintRow();
        printRow.setContentType("type");
        final BarCode barCode = new BarCode();
        barCode.setContent("content");
        printRow.setBarCode(barCode);
        final CoordinateRow coordinateRow = new CoordinateRow();
        printRow.setCoordinateRow(coordinateRow);
        final Section section = new Section();
        printRow.setSection(section);
        final List<PrintRow> expectedResult = Arrays.asList(printRow);

        // Run the test
        final List<PrintRow> result = labelTemplateUnderTest.getContent();

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetFailedMsg() throws Exception {
        assertEquals("标签单打印失败，请及时处理", labelTemplateUnderTest.getFailedMsg());
    }
}
