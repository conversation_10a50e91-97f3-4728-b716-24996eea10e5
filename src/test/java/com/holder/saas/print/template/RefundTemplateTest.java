package com.holder.saas.print.template;

import com.holder.saas.print.utils.RefundPrintUtils;
import com.holderzone.saas.store.dto.print.content.PrintRefundDTO;
import com.holderzone.saas.store.dto.print.format.RefundFormatDTO;
import com.holderzone.saas.store.dto.print.template.PrintRow;
import lombok.var;
import org.junit.Before;
import org.junit.Test;

import java.util.List;

import static org.hibernate.validator.internal.util.Contracts.assertNotNull;
import static org.hibernate.validator.internal.util.Contracts.assertTrue;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;

/**
 * 退款单模板测试类
 *
 * <AUTHOR>
 * @date 2025/07/01
 */
public class RefundTemplateTest {

    private RefundTemplate refundTemplate;
    private PrintRefundDTO printRefundDTO;
    private RefundFormatDTO refundFormatDTO;

    @Before
    public void setUp() {
        refundTemplate = new RefundTemplate();
        
        // 创建测试数据
        printRefundDTO = RefundPrintUtils.createMockRefundData();
        
        // 创建格式配置
        refundFormatDTO = new RefundFormatDTO();
        refundFormatDTO.applyDefault(); // 应用默认格式
        
        // 设置到模板中
        refundTemplate.setPrintDTO(printRefundDTO);
        refundTemplate.setFormatDTO(refundFormatDTO);
    }

    @Test
    public void testGetContent() {
        // 测试获取打印内容
        List<PrintRow> content = refundTemplate.getContent();
        
        assertNotNull(content, "打印内容不应为空");
        assertFalse("打印内容不应为空列表", content.isEmpty());
        
        // 验证内容包含基本信息
        String contentStr = content.toString();
        assertTrue(contentStr.contains("花开仲夏"), "应包含门店名称");
        assertTrue(contentStr.contains("退款单"), "应包含票据类型");
        assertTrue(contentStr.contains("夫妻肺片"), "应包含商品名称");
        assertTrue(contentStr.contains("12.00"), "应包含金额信息");
    }

    @Test
    public void testGetFailedMsg() {
        // 测试失败消息
        String failedMsg = refundTemplate.getFailedMsg();
        
        assertNotNull(failedMsg, "失败消息不应为空");
        assertTrue(failedMsg.contains("退款单"), "失败消息应包含退款单字样");
        assertTrue(failedMsg.contains(printRefundDTO.getOrderNo()), "失败消息应包含订单号");
    }

    @Test
    public void testValidateRefundData() {
        // 测试数据验证
        boolean isValid = RefundPrintUtils.validateRefundData(printRefundDTO);
        assertTrue(isValid, "模拟数据应该是有效的");
        
        // 测试无效数据
        PrintRefundDTO invalidDTO = new PrintRefundDTO();
        boolean isInvalid = RefundPrintUtils.validateRefundData(invalidDTO);
        assertFalse("空数据应该是无效的", isInvalid );
    }

    @Test
    public void testFormatTime() {
        // 测试时间格式化
        Long timestamp = System.currentTimeMillis();
        String formattedTime = RefundPrintUtils.formatTime(timestamp);
        
        assertNotNull(formattedTime, "格式化时间不应为空");
        assertTrue(formattedTime.matches("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}"), 
                   "时间格式应为 yyyy-MM-dd HH:mm");
    }

    @Test
    public void testCalculateTotalRefundAmount() {
        // 测试计算退款总金额
        var totalAmount = RefundPrintUtils.calculateTotalRefundAmount(printRefundDTO.getItemRecordList());
        
        assertNotNull(totalAmount, "总金额不应为空");
        assertEquals( "计算的总金额应与设置的退款金额一致", 0, totalAmount.compareTo(printRefundDTO.getRefundAmount()));
    }

    @Test
    public void testGenerateRefundPrintUid() {
        // 测试生成打印标识
        String orderNo = "TEST_ORDER_001";
        String printUid = RefundPrintUtils.generateRefundPrintUid(orderNo);
        
        assertNotNull(printUid, "打印标识不应为空");
        assertTrue(printUid.startsWith("REFUND_"), "打印标识应以REFUND_开头");
        assertTrue(printUid.contains(orderNo), "打印标识应包含订单号");
    }
}
