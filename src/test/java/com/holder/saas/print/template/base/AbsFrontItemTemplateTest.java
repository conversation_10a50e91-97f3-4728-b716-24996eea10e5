package com.holder.saas.print.template.base;

import com.holderzone.saas.store.dto.print.content.PrintBaseItemDTO;
import com.holderzone.saas.store.dto.print.content.nested.PrintItemRecord;
import com.holderzone.saas.store.dto.print.format.FormatDTO;
import com.holderzone.saas.store.dto.print.format.metadata.CustomMetadata;
import com.holderzone.saas.store.dto.print.template.PrintRow;
import com.holderzone.saas.store.dto.print.template.printable.BarCode;
import org.junit.Before;
import org.junit.Test;

import java.util.Arrays;
import java.util.List;
import java.util.function.Consumer;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;

public class AbsFrontItemTemplateTest {

    private AbsFrontItemTemplate<PrintBaseItemDTO, FormatDTO> absFrontItemTemplateUnderTest;

    @Before
    public void setUp() throws Exception {
        absFrontItemTemplateUnderTest = new AbsFrontItemTemplate<PrintBaseItemDTO, FormatDTO>() {
            @Override
            public List<PrintRow> getContent() {
                return null;
            }

            @Override
            public String getFailedMsg() {
                return null;
            }
        };
    }

    @Test
    public void testGetHeader() {
        // Setup
        final PrintRow printRow = new PrintRow();
        printRow.setContentType("contentType");
        final BarCode barCode = new BarCode();
        barCode.setContent("content");
        barCode.setHeight(0);
        barCode.setMargin(0);
        printRow.setBarCode(barCode);
        final List<PrintRow> expectedResult = Arrays.asList(printRow);

        // Run the test
        final List<PrintRow> result = absFrontItemTemplateUnderTest.getHeader();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetFooter() {
        // Setup
        final PrintRow printRow = new PrintRow();
        printRow.setContentType("contentType");
        final BarCode barCode = new BarCode();
        barCode.setContent("content");
        barCode.setHeight(0);
        barCode.setMargin(0);
        printRow.setBarCode(barCode);
        final List<PrintRow> expectedResult = Arrays.asList(printRow);

        // Run the test
        final List<PrintRow> result = absFrontItemTemplateUnderTest.getFooter();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testPageSizeGetterAndSetter() {
        final int pageSize = 0;
        absFrontItemTemplateUnderTest.setPageSize(pageSize);
        assertThat(absFrontItemTemplateUnderTest.getPageSize()).isEqualTo(pageSize);
    }

    @Test
    public void testPrintDTOGetterAndSetter() {
        final PrintBaseItemDTO printDTO = new PrintBaseItemDTO();
        absFrontItemTemplateUnderTest.setPrintDTO(printDTO);
        assertThat(absFrontItemTemplateUnderTest.getPrintDTO()).isEqualTo(printDTO);
    }

    @Test
    public void testFormatDTOGetterAndSetter() {
        final FormatDTO formatDTO = new FormatDTO();
        absFrontItemTemplateUnderTest.setFormatDTO(formatDTO);
        assertThat(absFrontItemTemplateUnderTest.getFormatDTO()).isEqualTo(formatDTO);
    }

    @Test
    public void testGetSucceedMsg() {
        assertThat(absFrontItemTemplateUnderTest.getSucceedMsg()).isNull();
    }

    @Test
    public void testGetPrintRows() {
        // Setup
        final PrintRow printRow = new PrintRow();
        printRow.setContentType("contentType");
        final BarCode barCode = new BarCode();
        barCode.setContent("content");
        barCode.setHeight(0);
        barCode.setMargin(0);
        printRow.setBarCode(barCode);
        final List<PrintRow> expectedResult = Arrays.asList(printRow);

        // Run the test
        final List<PrintRow> result = absFrontItemTemplateUnderTest.getPrintRows();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testSetPrintDTO2() {
        // Setup
        final PrintBaseItemDTO t = new PrintBaseItemDTO();
        final PrintItemRecord printItemRecord = new PrintItemRecord();
        printItemRecord.setDaySn("daySn");
        printItemRecord.setOrderItemGuid("orderItemGuid");
        printItemRecord.setOriginalOrderItemGuid(0L);
        printItemRecord.setItemGuid("itemGuid");
        t.setItemRecordList(Arrays.asList(printItemRecord));

        final Consumer<PrintBaseItemDTO> mockConsumer = mock(Consumer.class);

        // Run the test
        absFrontItemTemplateUnderTest.setPrintDTO(t, mockConsumer);

        // Verify the results
    }

    @Test
    public void testSetFormatDTO2() {
        // Setup
        final FormatDTO f = new FormatDTO();
        f.setGuid("90bd8e62-2620-47ef-96c4-99865e9dc956");
        f.setName("name");
        f.setStoreGuid("storeGuid");
        final CustomMetadata customMetadata = new CustomMetadata();
        f.setHeaders(Arrays.asList(customMetadata));
        final CustomMetadata customMetadata1 = new CustomMetadata();
        f.setFooters(Arrays.asList(customMetadata1));

        final Consumer<FormatDTO> mockConsumer = mock(Consumer.class);

        // Run the test
        absFrontItemTemplateUnderTest.setFormatDTO(f, mockConsumer);

        // Verify the results
    }

    @Test
    public void testBuild() {
        // Setup
        // Run the test
        final PrintTemplate<PrintBaseItemDTO, FormatDTO> result = absFrontItemTemplateUnderTest.build();

        // Verify the results
    }

    @Test
    public void testBuildAware() {
        // Setup
        // Run the test
        final PrintTemplateAware<PrintBaseItemDTO, FormatDTO> result = absFrontItemTemplateUnderTest.buildAware();

        // Verify the results
    }
}
