package com.holder.saas.print.template;

import com.holderzone.saas.store.dto.print.template.PrintRow;
import com.holderzone.saas.store.dto.print.template.printable.BarCode;
import com.holderzone.saas.store.dto.print.template.printable.BlankRow;
import com.holderzone.saas.store.dto.print.template.printable.KeyValue;
import com.holderzone.saas.store.dto.print.template.printable.Section;
import org.junit.Before;
import org.junit.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.assertEquals;

public class DebtRepaymentTemplateTest {

    private DebtRepaymentTemplate debtRepaymentTemplateUnderTest;

    @Before
    public void setUp() throws Exception {
        debtRepaymentTemplateUnderTest = new DebtRepaymentTemplate();
    }

    @Test
    public void testGetContent() {
        // Setup
        final PrintRow printRow = new PrintRow();
        printRow.setContentType("type");
        final BarCode barCode = new BarCode();
        printRow.setBarCode(barCode);
        final BlankRow blankRow = new BlankRow();
        printRow.setBlankRow(blankRow);
        final KeyValue keyValue = new KeyValue();
        printRow.setKeyValue(keyValue);
        final Section section = new Section();
        printRow.setSection(section);
        final List<PrintRow> expectedResult = Arrays.asList(printRow);

        // Run the test
        final List<PrintRow> result = debtRepaymentTemplateUnderTest.getContent();

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetFailedMsg() {
        assertEquals("挂账还款单打印失败，请及时处理", debtRepaymentTemplateUnderTest.getFailedMsg());
    }
}
