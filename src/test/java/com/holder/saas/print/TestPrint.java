package com.holder.saas.print;

import org.junit.Assert;
import org.junit.Test;

import java.io.IOException;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.net.Socket;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TestBillSeriable
 * @date 2018/07/30 10:56
 * @description //TODO
 * @program holder-saas-store-print
 */
public class TestPrint {

    @Test
    public void tt() {
        Pattern pattern = Pattern.compile("([\\u4e00-\\u9fa5]+)");
        String name = "aa我们123是aha哈";
        Assert.assertNotNull(name);
        Matcher matcher = pattern.matcher(name);
        while (matcher.find()) {
            System.out.println(matcher.group());
        }
        System.out.println(name);
    }

    @Test
    public void testPrint() throws IOException {
        String PRINT_IP = "************";
        int PRINT_PORT = 9100;
        OutputStream socketOut;
        try (Socket socket = new Socket(PRINT_IP, PRINT_PORT)) {
            Assert.assertNotNull(socket.getOutputStream());
            socketOut = socket.getOutputStream();
        }
        OutputStreamWriter writer = new OutputStreamWriter(socketOut, "GBK");
        print(socketOut, writer);
    }

    public void print(OutputStream socketOut, OutputStreamWriter writer) {
        int column = 4;
        int pageSize = 80;
        int xm = 2;
        try {
            // 清除字体放大指令
            byte[] FD_FONT = new byte[3];
            FD_FONT[0] = 0x1c;
            FD_FONT[1] = 0x21;
            FD_FONT[2] = 4;
            // 字体加粗指令
            byte[] FONT_B = new byte[3];
            FONT_B[0] = 27;
            FONT_B[1] = 33;
            FONT_B[2] = 8;
            // 字体纵向放大一倍
            byte[] CLEAR_FONT = new byte[3];
            CLEAR_FONT[0] = 0x1c;
            CLEAR_FONT[1] = 0x21;
            CLEAR_FONT[2] = 0;
            // 计算合计金额
//            writer.write("  " + GS_INFO.get("GS_Name") + " \r\n");
//            writer.flush();// 关键,很重要,不然指令一次性输出,后面指令覆盖前面指令,导致取消放大指令无效
//            socketOut.write(CLEAR_FONT);
//            socketOut.write(10);
//            writer.write("NO:  " + CAIDAN_SN + " \r\n");
            socketOut.flush();
            socketOut.write(0x1B);
            socketOut.write(97);
            //设置条码居中
            socketOut.write(1);
            socketOut.write(0x1c);
            socketOut.write(0x21);
            socketOut.write(4);
            writer.write("综合测试");
            writer.write("\r\n");
            writer.flush();
            socketOut.write(0x1B);
            socketOut.write(97);
            //设置条码居中
            socketOut.write(0);
            printLine(pageSize, "菜品 单价 数量 小计", xm, column, writer, socketOut);
            printLine(pageSize, "测试菜品1 2.0 1 2.0", xm, column, writer, socketOut);
            printLine(pageSize, "测试菜品2 2.0 1 2.0", xm, column, writer, socketOut);
            printLine(pageSize, "测试菜品3 2.0 1 2.0", xm, column, writer, socketOut);
            printLine(pageSize, "测试菜品4 2.0 1 2.0", xm, column, writer, socketOut);
            // 下面指令为打印完成后自动走纸
            writer.write(27);
            writer.write(100);
            writer.write(4);
            writer.write(10);

            writer.write(0x1D);
            writer.write(86);
            writer.write(65);
            writer.write(50);
            writer.flush();
            writer.close();
            socketOut.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public void printLine(int pageSize, String text, int xm, int column, OutputStreamWriter writer, OutputStream socketOut) throws IOException {
        String[] args = text.split(" ");
        for (int i = 0; i < column; i++) {
            printContent(pageSize, i + 1, args[i], xm, column, writer, socketOut);
        }
        writer.write("\r\n");
        writer.flush();
    }

    /**
     * 计算每个字符所占的宽度mm
     *
     * @param pageSize 80 or 58 mm
     * @param xm       放大倍数
     * @return 单个字符所占的长度
     */
    private Double getSingleCharWidth(int pageSize, int xm) {
        if (pageSize == 80) {
            return 80d / 48 * xm;
        }
        return 58d / 32 * xm;
    }

    /**
     * @param part
     * @param text
     * @param xm
     * @param parts     总共有多少部分
     * @param writer
     * @param socketOut
     * @throws IOException
     */
    private void printContent(int pageSize, int part, String text, int xm, int parts, OutputStreamWriter writer, OutputStream socketOut) throws IOException {
        int[] strs = getPosition(part, parts, pageSize);
        socketOut.write(0x1B);
        socketOut.write(0x24);
        socketOut.write(strs[0]);
        socketOut.write(strs[1]);
        //横向放大1倍
        socketOut.write(0x1c);
        socketOut.write(0x21);
        socketOut.write(4);
        socketOut.write(new byte[]{0x1C, '&'});
        double width = (double) pageSize / parts;   //列宽
        Double singleCharWidth = getSingleCharWidth(pageSize, xm);
        double length = text.length() * singleCharWidth * 2;   //整个字符串应该占有的宽度
        if (length <= pageSize) {//小于列宽能够放下
            writer.write(text);
            if (width < length) {
                writer.write("\r\n");
            }
        } else {
            int index = (int) (pageSize / singleCharWidth / 2);     //列宽能够放下多少个字符
            writer.write(text.substring(0, index));
            writer.write("\r\n");
            printContent(pageSize, part, text.substring(index), xm, parts, writer, socketOut);
        }
        writer.flush();
    }

    /**
     * 计算每列的起始打印位置
     *
     * @param index
     * @param total
     * @param pageSize
     * @return
     */
    private int[] getPosition(int index, int total, int pageSize) {
        int peer = pageSize / total * 8;   //点位数
        if (index == 1) {
            return new int[]{0, 0};
        } else {
            peer = (index - 1) * peer;
            int nh = peer / 256 > 2 ? 2 : peer / 256;
            int nl = peer - nh * 256;
            return new int[]{nl, nh};
        }
    }
}
