package com.holder.saas.print.helper;

import org.junit.Before;
import org.junit.Test;

import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

public class PageAdapterTest {

    private PageAdapter<String> pageAdapterUnderTest;

    @Before
    public void setUp() throws Exception {
        pageAdapterUnderTest = new PageAdapter<>(0L, 0L, 0L);
    }

    @Test
    public void testHasPrevious() {
        assertThat(pageAdapterUnderTest.hasPrevious()).isFalse();
    }

    @Test
    public void testHasNext() {
        assertThat(pageAdapterUnderTest.hasNext()).isFalse();
    }

    @Test
    public void testGetPages() {
        assertThat(pageAdapterUnderTest.getPages()).isEqualTo(0L);
    }

    @Test
    public void testRecordsGetterAndSetter() {
        final List<String> records = Arrays.asList("value");
        pageAdapterUnderTest.setRecords(records);
        assertThat(pageAdapterUnderTest.getRecords()).isEqualTo(records);
    }

    @Test
    public void testTotalGetterAndSetter() {
        final long total = 0L;
        pageAdapterUnderTest.setTotal(total);
        assertThat(pageAdapterUnderTest.getTotal()).isEqualTo(total);
    }

    @Test
    public void testSizeGetterAndSetter() {
        final long size = 0L;
        pageAdapterUnderTest.setSize(size);
        assertThat(pageAdapterUnderTest.getSize()).isEqualTo(size);
    }

    @Test
    public void testCurrentGetterAndSetter() {
        final long current = 0L;
        pageAdapterUnderTest.setCurrent(current);
        assertThat(pageAdapterUnderTest.getCurrent()).isEqualTo(current);
    }

    @Test
    public void testAscsGetterAndSetter() {
        final String ascs = "ascs";
        pageAdapterUnderTest.setAsc(ascs);
        assertThat(pageAdapterUnderTest.ascs()).isEqualTo(ascs);
    }

    @Test
    public void testSetAscs() {
        // Setup
        // Run the test
        final PageAdapter<String> result = pageAdapterUnderTest.setAscs(Arrays.asList("value"));

        // Verify the results
    }

    @Test
    public void testDescsGetterAndSetter() {
        final String descs = "descs";
        pageAdapterUnderTest.setDesc(descs);
        assertThat(pageAdapterUnderTest.descs()).isEqualTo(descs);
    }

    @Test
    public void testSetDescs() {
        // Setup
        // Run the test
        final PageAdapter<String> result = pageAdapterUnderTest.setDescs(Arrays.asList("value"));

        // Verify the results
    }

    @Test
    public void testOptimizeCountSqlGetterAndSetter() {
        final boolean optimizeCountSql = false;
        pageAdapterUnderTest.setOptimizeCountSql(optimizeCountSql);
        assertThat(pageAdapterUnderTest.optimizeCountSql()).isFalse();
    }

    @Test
    public void testRecords1GetterAndSetter() {
        final List<String> records = Arrays.asList("value");
        pageAdapterUnderTest.setRecords(records);
        assertThat(pageAdapterUnderTest.getData()).isEqualTo(records);
    }

    @Test
    public void testRecords2GetterAndSetter() {
        final List<String> records = Arrays.asList("value");
        pageAdapterUnderTest.setData(records);
        assertThat(pageAdapterUnderTest.getRecords()).isEqualTo(records);
    }

    @Test
    public void testTotal1GetterAndSetter() {
        final long total = 0L;
        pageAdapterUnderTest.setTotal(total);
        assertThat(pageAdapterUnderTest.getTotalCount()).isEqualTo(total);
    }

    @Test
    public void testTotal2GetterAndSetter() {
        final long total = 0L;
        pageAdapterUnderTest.setTotalCount(total);
        assertThat(pageAdapterUnderTest.getTotal()).isEqualTo(total);
    }

    @Test
    public void testSize1GetterAndSetter() {
        final long size = 0L;
        pageAdapterUnderTest.setSize(size);
        assertThat(pageAdapterUnderTest.getPageSize()).isEqualTo(size);
    }

    @Test
    public void testSize2GetterAndSetter() {
        final long size = 0L;
        pageAdapterUnderTest.setPageSize(size);
        assertThat(pageAdapterUnderTest.getSize()).isEqualTo(size);
    }

    @Test
    public void testCurrent1GetterAndSetter() {
        final long current = 0L;
        pageAdapterUnderTest.setCurrent(current);
        assertThat(pageAdapterUnderTest.getCurrentPage()).isEqualTo(current);
    }

    @Test
    public void testCurrent2GetterAndSetter() {
        final long current = 0L;
        pageAdapterUnderTest.setCurrentPage(current);
        assertThat(pageAdapterUnderTest.getCurrent()).isEqualTo(current);
    }
}
