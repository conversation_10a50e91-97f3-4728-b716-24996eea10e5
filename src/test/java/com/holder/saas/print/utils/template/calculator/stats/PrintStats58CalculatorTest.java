package com.holder.saas.print.utils.template.calculator.stats;

import org.junit.Before;
import org.junit.Test;

import java.util.Arrays;

import static org.assertj.core.api.Assertions.assertThat;

public class PrintStats58CalculatorTest {

    private PrintStats58Calculator printStats58CalculatorUnderTest;

    @Before
    public void setUp() throws Exception {
        printStats58CalculatorUnderTest = new PrintStats58Calculator();
    }

    @Test
    public void testGetColumnWidthList() {
        assertThat(printStats58CalculatorUnderTest.getColumnWidthList()).isEqualTo(Arrays.asList(0));
    }
}
