package com.holder.saas.print.utils;

import org.junit.Test;

import java.math.BigDecimal;

import static org.assertj.core.api.Assertions.assertThat;

public class BigDecimalUtilsTest {

    @Test
    public void testQuantityTrimmed() {
        assertThat(BigDecimalUtils.quantityTrimmed(new BigDecimal("0.00"))).isEqualTo(new BigDecimal("0.00"));
    }

    @Test
    public void testQuantityTrimmedString() {
        assertThat(BigDecimalUtils.quantityTrimmedString(new BigDecimal("0.00"))).isEqualTo("result");
    }

    @Test
    public void testMoneyTrimmedString() {
        assertThat(BigDecimalUtils.moneyTrimmedString(new BigDecimal("0.00"))).isEqualTo("result");
    }

    @Test
    public void testNonNullValue() {
        assertThat(BigDecimalUtils.nonNullValue(new BigDecimal("0.00"))).isEqualTo(new BigDecimal("0.00"));
    }
}
