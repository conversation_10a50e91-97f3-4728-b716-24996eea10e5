package com.holder.saas.print.utils.template.label;

import org.junit.Test;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.within;

public class PrintCalcUtilsTest {

    @Test
    public void testGetCutOffText() {
        assertThat(PrintCalcUtils.getCutOffText("src", 0)).isEqualTo("result");
    }

    @Test
    public void testGetPointOfWidth() {
        assertThat(PrintCalcUtils.getPointOfWidth(0.0)).isEqualTo(0.0, within(0.0001));
    }

    @Test
    public void testGetPointOfText() {
        assertThat(PrintCalcUtils.getPointOfText("src", 0, 0)).isEqualTo(0.0, within(0.0001));
    }

    @Test
    public void testGetWidthOfText() {
        assertThat(PrintCalcUtils.getWidthOfText("src", 0, 0)).isEqualTo(0.0, within(0.0001));
    }

    @Test
    public void testGetCharCountOfText() {
        assertThat(PrintCalcUtils.getCharCountOfText("src")).isEqualTo(0);
    }

    @Test
    public void testGetByteCountOfText() {
        assertThat(PrintCalcUtils.getByteCountOfText("src", 0)).isEqualTo(0);
    }

    @Test
    public void testGetPointOfSingleByte() {
        assertThat(PrintCalcUtils.getPointOfSingleByte(0)).isEqualTo(0.0, within(0.0001));
    }

    @Test
    public void testGetWidthOfSingleByte() {
        assertThat(PrintCalcUtils.getWidthOfSingleByte(0)).isEqualTo(0.0, within(0.0001));
    }

    @Test
    public void testGetByteCountOfPage() {
        assertThat(PrintCalcUtils.getByteCountOfPage(0)).isEqualTo(0);
    }

    @Test
    public void testGetPointOfSingleChar() {
        assertThat(PrintCalcUtils.getPointOfSingleChar(0, 0)).isEqualTo(0.0, within(0.0001));
    }

    @Test
    public void testGetWidthOfSingleChar() {
        assertThat(PrintCalcUtils.getWidthOfSingleChar(0, 0)).isEqualTo(0.0, within(0.0001));
    }

    @Test
    public void testGetCharCountOfPage() {
        assertThat(PrintCalcUtils.getCharCountOfPage(0, 0)).isEqualTo(0);
    }
}
