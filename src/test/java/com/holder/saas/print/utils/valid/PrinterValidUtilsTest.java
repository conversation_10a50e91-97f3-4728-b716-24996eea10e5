package com.holder.saas.print.utils.valid;

import com.holderzone.saas.store.dto.print.PrinterDTO;
import com.holderzone.saas.store.dto.print.cloud.CloudPrinterDTO;
import org.junit.Test;

import java.util.Arrays;

public class PrinterValidUtilsTest {

    @Test
    public void testCreateAddValidate() {
        // Setup
        final PrinterDTO printerDTO = new PrinterDTO();
        printerDTO.setStoreGuid("storeGuid");
        printerDTO.setDeviceId("deviceId");
        printerDTO.setPrinterGuid("printerGuid");
        printerDTO.setPrinterName("printerName");
        printerDTO.setBusinessType(0);
        printerDTO.setPrinterType(0);
        printerDTO.setPrinterIp("printerIp");
        printerDTO.setPrinterPort(0);
        printerDTO.setPrintCount(0);
        printerDTO.setPrintPage("printPage");
        printerDTO.setPrintCut(0);
        printerDTO.setStaffGuid("staffGuid");
        printerDTO.setArrayOfInvoiceType(Arrays.asList(0));
        printerDTO.setDeviceNo("deviceNo");
        printerDTO.setDeviceKey("deviceKey");

        // Run the test
        PrinterValidUtils.createAddValidate(printerDTO);

        // Verify the results
    }

    @Test
    public void testCreateQueryValidate() {
        // Setup
        final PrinterDTO printerDTO = new PrinterDTO();
        printerDTO.setStoreGuid("storeGuid");
        printerDTO.setDeviceId("deviceId");
        printerDTO.setPrinterGuid("printerGuid");
        printerDTO.setPrinterName("printerName");
        printerDTO.setBusinessType(0);
        printerDTO.setPrinterType(0);
        printerDTO.setPrinterIp("printerIp");
        printerDTO.setPrinterPort(0);
        printerDTO.setPrintCount(0);
        printerDTO.setPrintPage("printPage");
        printerDTO.setPrintCut(0);
        printerDTO.setStaffGuid("staffGuid");
        printerDTO.setArrayOfInvoiceType(Arrays.asList(0));
        printerDTO.setDeviceNo("deviceNo");
        printerDTO.setDeviceKey("deviceKey");

        // Run the test
        PrinterValidUtils.createQueryValidate(printerDTO);

        // Verify the results
    }

    @Test
    public void testCreateListByBizValidate() {
        // Setup
        final PrinterDTO printerDTO = new PrinterDTO();
        printerDTO.setStoreGuid("storeGuid");
        printerDTO.setDeviceId("deviceId");
        printerDTO.setPrinterGuid("printerGuid");
        printerDTO.setPrinterName("printerName");
        printerDTO.setBusinessType(0);
        printerDTO.setPrinterType(0);
        printerDTO.setPrinterIp("printerIp");
        printerDTO.setPrinterPort(0);
        printerDTO.setPrintCount(0);
        printerDTO.setPrintPage("printPage");
        printerDTO.setPrintCut(0);
        printerDTO.setStaffGuid("staffGuid");
        printerDTO.setArrayOfInvoiceType(Arrays.asList(0));
        printerDTO.setDeviceNo("deviceNo");
        printerDTO.setDeviceKey("deviceKey");

        // Run the test
        PrinterValidUtils.createListByBizValidate(printerDTO);

        // Verify the results
    }

    @Test
    public void testCreateListByDeviceValidate() {
        // Setup
        final PrinterDTO printerDTO = new PrinterDTO();
        printerDTO.setStoreGuid("storeGuid");
        printerDTO.setDeviceId("deviceId");
        printerDTO.setPrinterGuid("printerGuid");
        printerDTO.setPrinterName("printerName");
        printerDTO.setBusinessType(0);
        printerDTO.setPrinterType(0);
        printerDTO.setPrinterIp("printerIp");
        printerDTO.setPrinterPort(0);
        printerDTO.setPrintCount(0);
        printerDTO.setPrintPage("printPage");
        printerDTO.setPrintCut(0);
        printerDTO.setStaffGuid("staffGuid");
        printerDTO.setArrayOfInvoiceType(Arrays.asList(0));
        printerDTO.setDeviceNo("deviceNo");
        printerDTO.setDeviceKey("deviceKey");

        // Run the test
        PrinterValidUtils.createListByDeviceValidate(printerDTO);

        // Verify the results
    }

    @Test
    public void testUpdatePrinterValidate() {
        // Setup
        final PrinterDTO printerDTO = new PrinterDTO();
        printerDTO.setStoreGuid("storeGuid");
        printerDTO.setDeviceId("deviceId");
        printerDTO.setPrinterGuid("printerGuid");
        printerDTO.setPrinterName("printerName");
        printerDTO.setBusinessType(0);
        printerDTO.setPrinterType(0);
        printerDTO.setPrinterIp("printerIp");
        printerDTO.setPrinterPort(0);
        printerDTO.setPrintCount(0);
        printerDTO.setPrintPage("printPage");
        printerDTO.setPrintCut(0);
        printerDTO.setStaffGuid("staffGuid");
        printerDTO.setArrayOfInvoiceType(Arrays.asList(0));
        printerDTO.setDeviceNo("deviceNo");
        printerDTO.setDeviceKey("deviceKey");

        // Run the test
        PrinterValidUtils.updatePrinterValidate(printerDTO);

        // Verify the results
    }

    @Test
    public void testCreateChangeMasterValidate() {
        // Setup
        final PrinterDTO printerDTO = new PrinterDTO();
        printerDTO.setStoreGuid("storeGuid");
        printerDTO.setDeviceId("deviceId");
        printerDTO.setPrinterGuid("printerGuid");
        printerDTO.setPrinterName("printerName");
        printerDTO.setBusinessType(0);
        printerDTO.setPrinterType(0);
        printerDTO.setPrinterIp("printerIp");
        printerDTO.setPrinterPort(0);
        printerDTO.setPrintCount(0);
        printerDTO.setPrintPage("printPage");
        printerDTO.setPrintCut(0);
        printerDTO.setStaffGuid("staffGuid");
        printerDTO.setArrayOfInvoiceType(Arrays.asList(0));
        printerDTO.setDeviceNo("deviceNo");
        printerDTO.setDeviceKey("deviceKey");

        // Run the test
        PrinterValidUtils.createChangeMasterValidate(printerDTO);

        // Verify the results
    }

    @Test
    public void testCreateDeleteValidate() {
        // Setup
        final PrinterDTO printerDTO = new PrinterDTO();
        printerDTO.setStoreGuid("storeGuid");
        printerDTO.setDeviceId("deviceId");
        printerDTO.setPrinterGuid("printerGuid");
        printerDTO.setPrinterName("printerName");
        printerDTO.setBusinessType(0);
        printerDTO.setPrinterType(0);
        printerDTO.setPrinterIp("printerIp");
        printerDTO.setPrinterPort(0);
        printerDTO.setPrintCount(0);
        printerDTO.setPrintPage("printPage");
        printerDTO.setPrintCut(0);
        printerDTO.setStaffGuid("staffGuid");
        printerDTO.setArrayOfInvoiceType(Arrays.asList(0));
        printerDTO.setDeviceNo("deviceNo");
        printerDTO.setDeviceKey("deviceKey");

        // Run the test
        PrinterValidUtils.createDeleteValidate(printerDTO);

        // Verify the results
    }

    @Test
    public void testCreateAddCloudValidate() {
        // Setup
        final CloudPrinterDTO cloudPrinterDTO = new CloudPrinterDTO();
        cloudPrinterDTO.setStoreGuid("storeGuid");
        cloudPrinterDTO.setDeviceId("deviceId");
        cloudPrinterDTO.setPrinterGuid("printerGuid");
        cloudPrinterDTO.setPrinterName("printerName");
        cloudPrinterDTO.setBusinessType(0);
        cloudPrinterDTO.setPrinterType(0);
        cloudPrinterDTO.setPrinterIp("printerIp");
        cloudPrinterDTO.setPrinterPort(0);
        cloudPrinterDTO.setPrintCount(0);
        cloudPrinterDTO.setPrintPage("printPage");
        cloudPrinterDTO.setPrintCut(0);
        cloudPrinterDTO.setStaffGuid("staffGuid");
        cloudPrinterDTO.setArrayOfInvoiceType(Arrays.asList(0));
        cloudPrinterDTO.setDeviceNo("deviceNo");
        cloudPrinterDTO.setDeviceKey("deviceKey");

        // Run the test
        PrinterValidUtils.createAddCloudValidate(cloudPrinterDTO);

        // Verify the results
    }

    @Test
    public void testCreateListCloudPrintersValidate() {
        // Setup
        final PrinterDTO printerDTO = new PrinterDTO();
        printerDTO.setStoreGuid("storeGuid");
        printerDTO.setDeviceId("deviceId");
        printerDTO.setPrinterGuid("printerGuid");
        printerDTO.setPrinterName("printerName");
        printerDTO.setBusinessType(0);
        printerDTO.setPrinterType(0);
        printerDTO.setPrinterIp("printerIp");
        printerDTO.setPrinterPort(0);
        printerDTO.setPrintCount(0);
        printerDTO.setPrintPage("printPage");
        printerDTO.setPrintCut(0);
        printerDTO.setStaffGuid("staffGuid");
        printerDTO.setArrayOfInvoiceType(Arrays.asList(0));
        printerDTO.setDeviceNo("deviceNo");
        printerDTO.setDeviceKey("deviceKey");

        // Run the test
        PrinterValidUtils.createListCloudPrintersValidate(printerDTO);

        // Verify the results
    }

    @Test
    public void testCreateCheckCloudValidate() {
        // Setup
        final CloudPrinterDTO cloudPrinterDTO = new CloudPrinterDTO();
        cloudPrinterDTO.setStoreGuid("storeGuid");
        cloudPrinterDTO.setDeviceId("deviceId");
        cloudPrinterDTO.setPrinterGuid("printerGuid");
        cloudPrinterDTO.setPrinterName("printerName");
        cloudPrinterDTO.setBusinessType(0);
        cloudPrinterDTO.setPrinterType(0);
        cloudPrinterDTO.setPrinterIp("printerIp");
        cloudPrinterDTO.setPrinterPort(0);
        cloudPrinterDTO.setPrintCount(0);
        cloudPrinterDTO.setPrintPage("printPage");
        cloudPrinterDTO.setPrintCut(0);
        cloudPrinterDTO.setStaffGuid("staffGuid");
        cloudPrinterDTO.setArrayOfInvoiceType(Arrays.asList(0));
        cloudPrinterDTO.setDeviceNo("deviceNo");
        cloudPrinterDTO.setDeviceKey("deviceKey");

        // Run the test
        PrinterValidUtils.createCheckCloudValidate(cloudPrinterDTO);

        // Verify the results
    }
}
