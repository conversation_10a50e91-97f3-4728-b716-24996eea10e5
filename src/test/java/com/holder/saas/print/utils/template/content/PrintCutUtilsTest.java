package com.holder.saas.print.utils.template.content;

import com.holder.saas.print.entity.read.PrinterReadDO;
import com.holderzone.saas.store.dto.print.content.PrintBaseItemDTO;
import com.holderzone.saas.store.dto.print.content.nested.PrintItemRecord;
import org.junit.Test;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

public class PrintCutUtilsTest {

    @Test
    public void testSplitItemByPrintCut() {
        // Setup
        final PrintItemRecord printItemRecord = new PrintItemRecord();
        printItemRecord.setItemGuid("itemGuid");
        printItemRecord.setItemName("itemName");
        printItemRecord.setItemTypeGuid("itemTypeGuid");
        printItemRecord.setPrice(new BigDecimal("0.00"));
        printItemRecord.setPkgCnt(new BigDecimal("0.00"));
        printItemRecord.setNumber(new BigDecimal("0.00"));
        printItemRecord.setUnit("unit");
        printItemRecord.setAsWeight(false);
        printItemRecord.setAsPackage(false);
        printItemRecord.setSubItemRecords(Arrays.asList(new PrintItemRecord()));
        printItemRecord.setPropertyPrice(new BigDecimal("0.00"));
        printItemRecord.setIngredientPrice(new BigDecimal("0.00"));
        final List<PrintItemRecord> printItemRecordsMatched = Arrays.asList(printItemRecord);
        final PrinterReadDO printerReadDO = new PrinterReadDO();
        printerReadDO.setId(0L);
        printerReadDO.setStoreGuid("storeGuid");
        printerReadDO.setStoreName("storeName");
        printerReadDO.setDeviceId("deviceId");
        printerReadDO.setPrintCut(0);

        final PrintBaseItemDTO printBaseItemDTO = new PrintBaseItemDTO();
        final PrintItemRecord printItemRecord1 = new PrintItemRecord();
        printItemRecord1.setDaySn("daySn");
        printItemRecord1.setOrderItemGuid("orderItemGuid");
        printItemRecord1.setOriginalOrderItemGuid(0L);
        printItemRecord1.setItemGuid("itemGuid");
        printBaseItemDTO.setItemRecordList(Arrays.asList(printItemRecord1));

        final PrintItemRecord printItemRecord2 = new PrintItemRecord();
        printItemRecord2.setItemGuid("itemGuid");
        printItemRecord2.setItemName("itemName");
        printItemRecord2.setItemTypeGuid("itemTypeGuid");
        printItemRecord2.setPrice(new BigDecimal("0.00"));
        printItemRecord2.setPkgCnt(new BigDecimal("0.00"));
        printItemRecord2.setNumber(new BigDecimal("0.00"));
        printItemRecord2.setUnit("unit");
        printItemRecord2.setAsWeight(false);
        printItemRecord2.setAsPackage(false);
        printItemRecord2.setSubItemRecords(Arrays.asList(new PrintItemRecord()));
        printItemRecord2.setPropertyPrice(new BigDecimal("0.00"));
        printItemRecord2.setIngredientPrice(new BigDecimal("0.00"));
        final List<List<PrintItemRecord>> expectedResult = Arrays.asList(Arrays.asList(printItemRecord2));

        // Run the test
        final List<List<PrintItemRecord>> result = PrintCutUtils.splitItemByPrintCut(printItemRecordsMatched,
                printerReadDO, printBaseItemDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
