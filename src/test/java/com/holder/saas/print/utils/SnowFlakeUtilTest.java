package com.holder.saas.print.utils;

import org.junit.Before;
import org.junit.Test;

import static org.assertj.core.api.Assertions.assertThat;

public class SnowFlakeUtilTest {

    private SnowFlakeUtil snowFlakeUtilUnderTest;

    @Before
    public void setUp() throws Exception {
        snowFlakeUtilUnderTest = new SnowFlakeUtil(0L, 0L);
    }

    @Test
    public void testGetInstance() {
        // Setup
        // Run the test
        final SnowFlakeUtil result = SnowFlakeUtil.getInstance();

        // Verify the results
    }

    @Test
    public void testNextId() {
        assertThat(snowFlakeUtilUnderTest.nextId()).isEqualTo(0L);
    }
}
