package com.holder.saas.print.utils;

import org.apache.http.NameValuePair;
import org.junit.Test;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

public class HttpRequestUtilsTest {

    @Test
    public void testGetRequestUri() {
        assertThat(HttpRequestUtils.getRequestUri()).isEqualTo("result");
    }

    @Test
    public void testGetRequest() {
        // Setup
        // Run the test
        final MockHttpServletRequest result = (MockHttpServletRequest) HttpRequestUtils.getRequest();

        // Verify the results
    }

    @Test
    public void testGetRespnse() {
        // Setup
        // Run the test
        final MockHttpServletResponse result = (MockHttpServletResponse) HttpRequestUtils.getRespnse();

        // Verify the results
    }

    @Test
    public void testRequestPost() {
        // Setup
        final List<NameValuePair> pairList = Arrays.asList();

        // Run the test
        final String result = HttpRequestUtils.requestPost("url", pairList);

        // Verify the results
        assertThat(result).isEqualTo("result");
    }
}
