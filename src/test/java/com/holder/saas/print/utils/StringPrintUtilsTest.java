package com.holder.saas.print.utils;

import org.junit.Test;

import java.io.UnsupportedEncodingException;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

public class StringPrintUtilsTest {

    @Test
    public void testSubstringByte() {
        assertThat(StringPrintUtils.substringByte("orignal", 0, 0)).isEqualTo("orignal");
    }

    @Test
    public void testSubStr() throws Exception {
        assertThat(StringPrintUtils.subStr("orignal", 0, 0)).isEqualTo("result");
        assertThatThrownBy(() -> StringPrintUtils.subStr("orignal", 0, 0))
                .isInstanceOf(UnsupportedEncodingException.class);
    }

    @Test
    public void testGetStringByteLengths() throws Exception {
        assertThat(StringPrintUtils.getStringByteLengths("args")).isEqualTo(0);
        assertThatThrownBy(() -> StringPrintUtils.getStringByteLengths("args"))
                .isInstanceOf(UnsupportedEncodingException.class);
    }

    @Test
    public void testGetLength() {
        assertThat(StringPrintUtils.getLength("s")).isEqualTo(0);
    }

    @Test
    public void testStrToCenter() {
        assertThat(StringPrintUtils.strToCenter("s", 0)).isEqualTo("s");
    }

    @Test
    public void testSupplyBlankSpace() {
        assertThat(StringPrintUtils.supplyBlankSpace(0)).isEqualTo("result");
    }

    @Test
    public void testProtectedName() {
        assertThat(StringPrintUtils.protectedName("userName")).isEqualTo("userName");
    }
}
