package com.holder.saas.print.utils.template;

import org.junit.Test;

import static org.assertj.core.api.Assertions.assertThat;

public class TradeModeUtilsTest {

    @Test
    public void testGetCoStartName() {
        assertThat(TradeModeUtils.getCoStartName(0)).isEqualTo("result");
    }

    @Test
    public void testGetMarkName1() {
        assertThat(TradeModeUtils.getMarkName(0)).isEqualTo("result");
    }

    @Test
    public void testGetMarkNameWithoutColon() {
        assertThat(TradeModeUtils.getMarkNameWithoutColon(0)).isEqualTo("result");
    }

    @Test
    public void testGetMarkName2() {
        assertThat(TradeModeUtils.getMarkName(0, "markName")).isEqualTo("markName");
    }
}
