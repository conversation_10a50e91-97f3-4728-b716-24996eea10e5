package com.holder.saas.print.utils.template.row.composite;

import com.holderzone.saas.store.dto.print.content.nested.PayRecord;
import com.holderzone.saas.store.dto.print.content.nested.PrintItemRecord;
import com.holderzone.saas.store.dto.print.content.nested.ReduceRecord;
import com.holderzone.saas.store.dto.print.template.PrintRow;
import com.holderzone.saas.store.dto.print.template.printable.BlankRow;
import com.holderzone.saas.store.dto.print.template.printable.KeyValue;
import com.holderzone.saas.store.dto.print.template.printable.Section;
import com.holderzone.saas.store.dto.print.template.printable.Separator;
import org.junit.Before;
import org.junit.Test;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.assertEquals;

public class PrintFixedLayoutContainerTest {

    private PrintFixedLayoutContainer printFixedLayoutContainerUnderTest;

    @Before
    public void setUp() throws Exception {
        printFixedLayoutContainerUnderTest = new PrintFixedLayoutContainer();
    }

    @Test
    public void testGetPrintRows() {
        final PrintRow printRow = new PrintRow();
        printRow.setContentType("type");
        final BlankRow blankRow = new BlankRow();
        printRow.setBlankRow(blankRow);
        final KeyValue keyValue = new KeyValue();
        printRow.setKeyValue(keyValue);
        final Section section = new Section();
        printRow.setSection(section);
        final Separator separator = new Separator();
        printRow.setSeparator(separator);
        final List<PrintRow> expectedResult = Arrays.asList(printRow);
        assertEquals(expectedResult, printFixedLayoutContainerUnderTest.getPrintRows());
    }

    @Test
    public void testAddSeparator() {
        // Setup
        // Run the test
        printFixedLayoutContainerUnderTest.addSeparator();

        // Verify the results
    }

    @Test
    public void testAddBlankRow() {
        // Setup
        // Run the test
        printFixedLayoutContainerUnderTest.addBlankRow();

        // Verify the results
    }

    @Test
    public void testAddStoreName() {
        // Setup
        // Run the test
        printFixedLayoutContainerUnderTest.addStoreName("storeName");

        // Verify the results
    }

    @Test
    public void testAddOrderNo() {
        // Setup
        // Run the test
        printFixedLayoutContainerUnderTest.addOrderNo("orderNo");

        // Verify the results
    }

    @Test
    public void testAddTableItem() {
        // Setup
        final PrintItemRecord printItemRecord = new PrintItemRecord();
        printItemRecord.setItemName("itemName");
        printItemRecord.setItemTypeName("itemTypeName");
        printItemRecord.setPrice(new BigDecimal("0.00"));
        printItemRecord.setPkgCnt(new BigDecimal("0.00"));
        printItemRecord.setNumber(new BigDecimal("0.00"));
        printItemRecord.setActualType(0);
        printItemRecord.setActualPrice(new BigDecimal("0.00"));
        printItemRecord.setAsWeight(false);
        printItemRecord.setAsPackage(false);
        printItemRecord.setAsGift(false);
        printItemRecord.setSubItemRecords(Arrays.asList(new PrintItemRecord()));
        printItemRecord.setRemark("remark");
        printItemRecord.setProperty("property");
        printItemRecord.setPropertyPrice(new BigDecimal("0.00"));
        printItemRecord.setIngredientPrice(new BigDecimal("0.00"));
        printItemRecord.setItemState(0);
        printItemRecord.setHasAttr(0);
        printItemRecord.setSingleItemAttrTotal(new BigDecimal("0.00"));
        printItemRecord.setSingleAddPriceTotal(new BigDecimal("0.00"));
        final List<PrintItemRecord> itemRecordList = Arrays.asList(printItemRecord);

        // Run the test
        printFixedLayoutContainerUnderTest.addTableItem(itemRecordList, new BigDecimal("0.00"), 0);

        // Verify the results
    }

    @Test
    public void testAddReduceRecordAndPayable() {
        // Setup
        final List<ReduceRecord> reduceRecordList = Arrays.asList(new ReduceRecord("name", new BigDecimal("0.00")));

        // Run the test
        printFixedLayoutContainerUnderTest.addReduceRecordAndPayable(reduceRecordList, new BigDecimal("0.00"));

        // Verify the results
    }

    @Test
    public void testAddPayRecordAndActuallyPay() {
        // Setup
        final PayRecord payRecord = new PayRecord();
        payRecord.setPayName("payName");
        payRecord.setAmount(new BigDecimal("0.00"));
        payRecord.setAmountStr("amountStr");
        payRecord.setPaySerialNumber("paySerialNumber");
        payRecord.setExcessAmount(new BigDecimal("0.00"));
        final List<PayRecord> payRecords = Arrays.asList(payRecord);

        // Run the test
        printFixedLayoutContainerUnderTest.addPayRecordAndActuallyPay(payRecords, new BigDecimal("0.00"),
                new BigDecimal("0.00"));

        // Verify the results
    }

    @Test
    public void testAddOpStaffAndOpenTableTimeAndCheckTimeAndPrintTime() {
        // Setup
        // Run the test
        printFixedLayoutContainerUnderTest.addOpStaffAndOpenTableTimeAndCheckTimeAndPrintTime(0, "operatorStaffName",
                0L, 0L, 0L);

        // Verify the results
    }
}
