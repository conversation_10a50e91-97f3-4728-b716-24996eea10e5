package com.holder.saas.print.utils.template.row.composite;

import com.holderzone.saas.store.dto.print.content.nested.AdditionalCharge;
import com.holderzone.saas.store.dto.print.content.nested.InOutRecord;
import com.holderzone.saas.store.dto.print.content.nested.ReduceRecord;
import com.holderzone.saas.store.dto.print.template.PrintRow;
import com.holderzone.saas.store.dto.print.template.printable.BarCode;
import com.holderzone.saas.store.dto.print.template.printable.KeyValue;
import com.holderzone.saas.store.dto.print.template.printable.Separator;
import org.junit.Test;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

public class PrintLayoutUtilsTest {

    @Test
    public void testAddReduceRecord() {
        // Setup
        final List<ReduceRecord> reduceRecordList = Arrays.asList(new ReduceRecord("name", new BigDecimal("0.00")));
        final PrintRow printRow = new PrintRow();
        printRow.setContentType("contentType");
        final BarCode barCode = new BarCode();
        barCode.setContent("content");
        printRow.setBarCode(barCode);
        final KeyValue keyValue = new KeyValue();
        printRow.setKeyValue(keyValue);
        final Separator separator = new Separator();
        printRow.setSeparator(separator);
        final List<PrintRow> printRows = Arrays.asList(printRow);

        // Run the test
        PrintLayoutUtils.addReduceRecord(reduceRecordList, printRows);

        // Verify the results
    }

    @Test
    public void testAddAdditionalCharge() {
        // Setup
        final List<AdditionalCharge> additionalChargeList = Arrays.asList(
                new AdditionalCharge("chargeName", new BigDecimal("0.00"), new BigDecimal("0.00")));
        final PrintRow printRow = new PrintRow();
        printRow.setContentType("contentType");
        final BarCode barCode = new BarCode();
        barCode.setContent("content");
        printRow.setBarCode(barCode);
        final KeyValue keyValue = new KeyValue();
        printRow.setKeyValue(keyValue);
        final Separator separator = new Separator();
        printRow.setSeparator(separator);
        final List<PrintRow> printRows = Arrays.asList(printRow);

        // Run the test
        PrintLayoutUtils.addAdditionalCharge(additionalChargeList, printRows);

        // Verify the results
    }

    @Test
    public void testAddOrderTimeAndPrintTime() {
        // Setup
        final PrintRow printRow = new PrintRow();
        printRow.setContentType("contentType");
        final BarCode barCode = new BarCode();
        barCode.setContent("content");
        printRow.setBarCode(barCode);
        final KeyValue keyValue = new KeyValue();
        printRow.setKeyValue(keyValue);
        final Separator separator = new Separator();
        printRow.setSeparator(separator);
        final List<PrintRow> printRows = Arrays.asList(printRow);

        // Run the test
        PrintLayoutUtils.addOrderTimeAndPrintTime(0, 0L, 0L, printRows);

        // Verify the results
    }

    @Test
    public void testAddOpStaffAndPrintTime() {
        // Setup
        final PrintRow printRow = new PrintRow();
        printRow.setContentType("contentType");
        final BarCode barCode = new BarCode();
        barCode.setContent("content");
        printRow.setBarCode(barCode);
        final KeyValue keyValue = new KeyValue();
        printRow.setKeyValue(keyValue);
        final Separator separator = new Separator();
        printRow.setSeparator(separator);
        final List<PrintRow> printRows = Arrays.asList(printRow);

        // Run the test
        PrintLayoutUtils.addOpStaffAndPrintTime(0, "operatorStaffName", 0L, printRows);

        // Verify the results
    }

    @Test
    public void testAddStats() {
        // Setup
        final List<InOutRecord> inOutRecordList = Arrays.asList(new InOutRecord("name", 0L, new BigDecimal("0.00")));
        final PrintRow printRow = new PrintRow();
        printRow.setContentType("contentType");
        final BarCode barCode = new BarCode();
        barCode.setContent("content");
        printRow.setBarCode(barCode);
        final KeyValue keyValue = new KeyValue();
        printRow.setKeyValue(keyValue);
        final Separator separator = new Separator();
        printRow.setSeparator(separator);
        final List<PrintRow> printRows = Arrays.asList(printRow);

        // Run the test
        PrintLayoutUtils.addStats(inOutRecordList, 0, printRows);

        // Verify the results
    }
}
