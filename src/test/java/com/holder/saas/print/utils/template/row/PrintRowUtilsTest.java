package com.holder.saas.print.utils.template.row;

import com.holderzone.saas.store.dto.print.template.PrintRow;
import com.holderzone.saas.store.dto.print.template.convertable.Font;
import com.holderzone.saas.store.dto.print.template.convertable.Text;
import com.holderzone.saas.store.dto.print.template.printable.*;
import org.junit.Test;

import java.util.Arrays;
import java.util.Collection;

public class PrintRowUtilsTest {

    @Test
    public void testAdd1() {
        // Setup
        final PrintRow printRow = new PrintRow();
        printRow.setContentType("contentType");
        final BarCode barCode = new BarCode();
        printRow.setBarCode(barCode);
        final LabelBarCode labelBarCode = new LabelBarCode();
        printRow.setLabelBarCode(labelBarCode);
        final BlankRow blankRow = new BlankRow();
        printRow.setBlankRow(blankRow);
        final CoordinateRow coordinateRow = new CoordinateRow();
        printRow.setCoordinateRow(coordinateRow);
        final Image image = new Image();
        printRow.setImage(image);
        final KeyValue keyValue = new KeyValue();
        printRow.setKeyValue(keyValue);
        final Line line = new Line();
        printRow.setLine(line);
        final QrCode qrCode = new QrCode();
        printRow.setQrCode(qrCode);
        final ReverseText reverseText = new ReverseText();
        printRow.setReverseText(reverseText);
        final Section section = new Section();
        printRow.setSection(section);
        final Separator separator = new Separator();
        printRow.setSeparator(separator);
        final Table table = new Table();
        printRow.setTable(table);
        final TableRow tableRow = new TableRow();
        printRow.setTableRow(tableRow);
        final Collection<PrintRow> printRows = Arrays.asList(printRow);
        final BarCode barCode1 = new BarCode("content", 0, 0);

        // Run the test
        PrintRowUtils.add(printRows, barCode1);

        // Verify the results
    }

    @Test
    public void testAdd2() {
        // Setup
        final PrintRow printRow = new PrintRow();
        printRow.setContentType("contentType");
        final BarCode barCode = new BarCode();
        printRow.setBarCode(barCode);
        final LabelBarCode labelBarCode = new LabelBarCode();
        printRow.setLabelBarCode(labelBarCode);
        final BlankRow blankRow = new BlankRow();
        printRow.setBlankRow(blankRow);
        final CoordinateRow coordinateRow = new CoordinateRow();
        printRow.setCoordinateRow(coordinateRow);
        final Image image = new Image();
        printRow.setImage(image);
        final KeyValue keyValue = new KeyValue();
        printRow.setKeyValue(keyValue);
        final Line line = new Line();
        printRow.setLine(line);
        final QrCode qrCode = new QrCode();
        printRow.setQrCode(qrCode);
        final ReverseText reverseText = new ReverseText();
        printRow.setReverseText(reverseText);
        final Section section = new Section();
        printRow.setSection(section);
        final Separator separator = new Separator();
        printRow.setSeparator(separator);
        final Table table = new Table();
        printRow.setTable(table);
        final TableRow tableRow = new TableRow();
        printRow.setTableRow(tableRow);
        final Collection<PrintRow> printRows = Arrays.asList(printRow);
        final LabelBarCode labelBarCode1 = new LabelBarCode("content", 0, 0);

        // Run the test
        PrintRowUtils.add(printRows, labelBarCode1);

        // Verify the results
    }

    @Test
    public void testAdd3() {
        // Setup
        final PrintRow printRow = new PrintRow();
        printRow.setContentType("contentType");
        final BarCode barCode = new BarCode();
        printRow.setBarCode(barCode);
        final LabelBarCode labelBarCode = new LabelBarCode();
        printRow.setLabelBarCode(labelBarCode);
        final BlankRow blankRow = new BlankRow();
        printRow.setBlankRow(blankRow);
        final CoordinateRow coordinateRow = new CoordinateRow();
        printRow.setCoordinateRow(coordinateRow);
        final Image image = new Image();
        printRow.setImage(image);
        final KeyValue keyValue = new KeyValue();
        printRow.setKeyValue(keyValue);
        final Line line = new Line();
        printRow.setLine(line);
        final QrCode qrCode = new QrCode();
        printRow.setQrCode(qrCode);
        final ReverseText reverseText = new ReverseText();
        printRow.setReverseText(reverseText);
        final Section section = new Section();
        printRow.setSection(section);
        final Separator separator = new Separator();
        printRow.setSeparator(separator);
        final Table table = new Table();
        printRow.setTable(table);
        final TableRow tableRow = new TableRow();
        printRow.setTableRow(tableRow);
        final Collection<PrintRow> printRows = Arrays.asList(printRow);
        final BlankRow blankRow1 = new BlankRow(0);

        // Run the test
        PrintRowUtils.add(printRows, blankRow1);

        // Verify the results
    }

    @Test
    public void testAdd4() {
        // Setup
        final PrintRow printRow = new PrintRow();
        printRow.setContentType("contentType");
        final BarCode barCode = new BarCode();
        printRow.setBarCode(barCode);
        final LabelBarCode labelBarCode = new LabelBarCode();
        printRow.setLabelBarCode(labelBarCode);
        final BlankRow blankRow = new BlankRow();
        printRow.setBlankRow(blankRow);
        final CoordinateRow coordinateRow = new CoordinateRow();
        printRow.setCoordinateRow(coordinateRow);
        final Image image = new Image();
        printRow.setImage(image);
        final KeyValue keyValue = new KeyValue();
        printRow.setKeyValue(keyValue);
        final Line line = new Line();
        printRow.setLine(line);
        final QrCode qrCode = new QrCode();
        printRow.setQrCode(qrCode);
        final ReverseText reverseText = new ReverseText();
        printRow.setReverseText(reverseText);
        final Section section = new Section();
        printRow.setSection(section);
        final Separator separator = new Separator();
        printRow.setSeparator(separator);
        final Table table = new Table();
        printRow.setTable(table);
        final TableRow tableRow = new TableRow();
        printRow.setTableRow(tableRow);
        final Collection<PrintRow> printRows = Arrays.asList(printRow);
        final CoordinateRow coordinateRow1 = new CoordinateRow();
        final CoordinateRow.CoordinateText coordinateText = new CoordinateRow.CoordinateText();
        final Text text = new Text();
        text.setText("text");
        final Font font = new Font();
        final Font.Size size = new Font.Size();
        font.setSize(size);
        text.setFont(font);
        coordinateText.setText(text);
        coordinateRow1.setCoordinateTextList(Arrays.asList(coordinateText));

        // Run the test
        PrintRowUtils.add(printRows, coordinateRow1);

        // Verify the results
    }

    @Test
    public void testAdd5() {
        // Setup
        final PrintRow printRow = new PrintRow();
        printRow.setContentType("contentType");
        final BarCode barCode = new BarCode();
        printRow.setBarCode(barCode);
        final LabelBarCode labelBarCode = new LabelBarCode();
        printRow.setLabelBarCode(labelBarCode);
        final BlankRow blankRow = new BlankRow();
        printRow.setBlankRow(blankRow);
        final CoordinateRow coordinateRow = new CoordinateRow();
        printRow.setCoordinateRow(coordinateRow);
        final Image image = new Image();
        printRow.setImage(image);
        final KeyValue keyValue = new KeyValue();
        printRow.setKeyValue(keyValue);
        final Line line = new Line();
        printRow.setLine(line);
        final QrCode qrCode = new QrCode();
        printRow.setQrCode(qrCode);
        final ReverseText reverseText = new ReverseText();
        printRow.setReverseText(reverseText);
        final Section section = new Section();
        printRow.setSection(section);
        final Separator separator = new Separator();
        printRow.setSeparator(separator);
        final Table table = new Table();
        printRow.setTable(table);
        final TableRow tableRow = new TableRow();
        printRow.setTableRow(tableRow);
        final Collection<PrintRow> printRows = Arrays.asList(printRow);
        final Image image1 = new Image(0, 0, new int[]{0}, 0, "url");

        // Run the test
        PrintRowUtils.add(printRows, image1);

        // Verify the results
    }

    @Test
    public void testAdd6() {
        // Setup
        final PrintRow printRow = new PrintRow();
        printRow.setContentType("contentType");
        final BarCode barCode = new BarCode();
        printRow.setBarCode(barCode);
        final LabelBarCode labelBarCode = new LabelBarCode();
        printRow.setLabelBarCode(labelBarCode);
        final BlankRow blankRow = new BlankRow();
        printRow.setBlankRow(blankRow);
        final CoordinateRow coordinateRow = new CoordinateRow();
        printRow.setCoordinateRow(coordinateRow);
        final Image image = new Image();
        printRow.setImage(image);
        final KeyValue keyValue = new KeyValue();
        printRow.setKeyValue(keyValue);
        final Line line = new Line();
        printRow.setLine(line);
        final QrCode qrCode = new QrCode();
        printRow.setQrCode(qrCode);
        final ReverseText reverseText = new ReverseText();
        printRow.setReverseText(reverseText);
        final Section section = new Section();
        printRow.setSection(section);
        final Separator separator = new Separator();
        printRow.setSeparator(separator);
        final Table table = new Table();
        printRow.setTable(table);
        final TableRow tableRow = new TableRow();
        printRow.setTableRow(tableRow);
        final Collection<PrintRow> printRows = Arrays.asList(printRow);
        final KeyValue keyValue1 = new KeyValue("key", "value", false);

        // Run the test
        PrintRowUtils.add(printRows, keyValue1);

        // Verify the results
    }

    @Test
    public void testAdd7() {
        // Setup
        final PrintRow printRow = new PrintRow();
        printRow.setContentType("contentType");
        final BarCode barCode = new BarCode();
        printRow.setBarCode(barCode);
        final LabelBarCode labelBarCode = new LabelBarCode();
        printRow.setLabelBarCode(labelBarCode);
        final BlankRow blankRow = new BlankRow();
        printRow.setBlankRow(blankRow);
        final CoordinateRow coordinateRow = new CoordinateRow();
        printRow.setCoordinateRow(coordinateRow);
        final Image image = new Image();
        printRow.setImage(image);
        final KeyValue keyValue = new KeyValue();
        printRow.setKeyValue(keyValue);
        final Line line = new Line();
        printRow.setLine(line);
        final QrCode qrCode = new QrCode();
        printRow.setQrCode(qrCode);
        final ReverseText reverseText = new ReverseText();
        printRow.setReverseText(reverseText);
        final Section section = new Section();
        printRow.setSection(section);
        final Separator separator = new Separator();
        printRow.setSeparator(separator);
        final Table table = new Table();
        printRow.setTable(table);
        final TableRow tableRow = new TableRow();
        printRow.setTableRow(tableRow);
        final Collection<PrintRow> printRows = Arrays.asList(printRow);
        final Line line1 = new Line();
        line1.setHeight(0);
        line1.setDotted(false);
        line1.setUseSymbol(false);
        final Text symbol = new Text();
        symbol.setText("text");
        line1.setSymbol(symbol);

        // Run the test
        PrintRowUtils.add(printRows, line1);

        // Verify the results
    }

    @Test
    public void testAdd8() {
        // Setup
        final PrintRow printRow = new PrintRow();
        printRow.setContentType("contentType");
        final BarCode barCode = new BarCode();
        printRow.setBarCode(barCode);
        final LabelBarCode labelBarCode = new LabelBarCode();
        printRow.setLabelBarCode(labelBarCode);
        final BlankRow blankRow = new BlankRow();
        printRow.setBlankRow(blankRow);
        final CoordinateRow coordinateRow = new CoordinateRow();
        printRow.setCoordinateRow(coordinateRow);
        final Image image = new Image();
        printRow.setImage(image);
        final KeyValue keyValue = new KeyValue();
        printRow.setKeyValue(keyValue);
        final Line line = new Line();
        printRow.setLine(line);
        final QrCode qrCode = new QrCode();
        printRow.setQrCode(qrCode);
        final ReverseText reverseText = new ReverseText();
        printRow.setReverseText(reverseText);
        final Section section = new Section();
        printRow.setSection(section);
        final Separator separator = new Separator();
        printRow.setSeparator(separator);
        final Table table = new Table();
        printRow.setTable(table);
        final TableRow tableRow = new TableRow();
        printRow.setTableRow(tableRow);
        final Collection<PrintRow> printRows = Arrays.asList(printRow);
        final QrCode qrCode1 = new QrCode("content", 0, 0);

        // Run the test
        PrintRowUtils.add(printRows, qrCode1);

        // Verify the results
    }

    @Test
    public void testAdd9() {
        // Setup
        final PrintRow printRow = new PrintRow();
        printRow.setContentType("contentType");
        final BarCode barCode = new BarCode();
        printRow.setBarCode(barCode);
        final LabelBarCode labelBarCode = new LabelBarCode();
        printRow.setLabelBarCode(labelBarCode);
        final BlankRow blankRow = new BlankRow();
        printRow.setBlankRow(blankRow);
        final CoordinateRow coordinateRow = new CoordinateRow();
        printRow.setCoordinateRow(coordinateRow);
        final Image image = new Image();
        printRow.setImage(image);
        final KeyValue keyValue = new KeyValue();
        printRow.setKeyValue(keyValue);
        final Line line = new Line();
        printRow.setLine(line);
        final QrCode qrCode = new QrCode();
        printRow.setQrCode(qrCode);
        final ReverseText reverseText = new ReverseText();
        printRow.setReverseText(reverseText);
        final Section section = new Section();
        printRow.setSection(section);
        final Separator separator = new Separator();
        printRow.setSeparator(separator);
        final Table table = new Table();
        printRow.setTable(table);
        final TableRow tableRow = new TableRow();
        printRow.setTableRow(tableRow);
        final Collection<PrintRow> printRows = Arrays.asList(printRow);
        final ReverseText reverseText1 = new ReverseText();
        final Text text = new Text();
        text.setText("text");
        final Font font = new Font();
        final Font.Size size = new Font.Size();
        size.setXm(0);
        font.setSize(size);
        text.setFont(font);
        reverseText1.setText(text);

        // Run the test
        PrintRowUtils.add(printRows, reverseText1);

        // Verify the results
    }

    @Test
    public void testAdd10() {
        // Setup
        final PrintRow printRow = new PrintRow();
        printRow.setContentType("contentType");
        final BarCode barCode = new BarCode();
        printRow.setBarCode(barCode);
        final LabelBarCode labelBarCode = new LabelBarCode();
        printRow.setLabelBarCode(labelBarCode);
        final BlankRow blankRow = new BlankRow();
        printRow.setBlankRow(blankRow);
        final CoordinateRow coordinateRow = new CoordinateRow();
        printRow.setCoordinateRow(coordinateRow);
        final Image image = new Image();
        printRow.setImage(image);
        final KeyValue keyValue = new KeyValue();
        printRow.setKeyValue(keyValue);
        final Line line = new Line();
        printRow.setLine(line);
        final QrCode qrCode = new QrCode();
        printRow.setQrCode(qrCode);
        final ReverseText reverseText = new ReverseText();
        printRow.setReverseText(reverseText);
        final Section section = new Section();
        printRow.setSection(section);
        final Separator separator = new Separator();
        printRow.setSeparator(separator);
        final Table table = new Table();
        printRow.setTable(table);
        final TableRow tableRow = new TableRow();
        printRow.setTableRow(tableRow);
        final Collection<PrintRow> printRows = Arrays.asList(printRow);
        final Section section1 = new Section();
        section1.setAlign(Text.Align.Left);
        final Text text = new Text();
        text.setText("text");
        final Font font = new Font();
        final Font.Size size = new Font.Size();
        font.setSize(size);
        text.setFont(font);
        section1.setTexts(Arrays.asList(text));

        // Run the test
        PrintRowUtils.add(printRows, section1);

        // Verify the results
    }

    @Test
    public void testAdd11() {
        // Setup
        final PrintRow printRow = new PrintRow();
        printRow.setContentType("contentType");
        final BarCode barCode = new BarCode();
        printRow.setBarCode(barCode);
        final LabelBarCode labelBarCode = new LabelBarCode();
        printRow.setLabelBarCode(labelBarCode);
        final BlankRow blankRow = new BlankRow();
        printRow.setBlankRow(blankRow);
        final CoordinateRow coordinateRow = new CoordinateRow();
        printRow.setCoordinateRow(coordinateRow);
        final Image image = new Image();
        printRow.setImage(image);
        final KeyValue keyValue = new KeyValue();
        printRow.setKeyValue(keyValue);
        final Line line = new Line();
        printRow.setLine(line);
        final QrCode qrCode = new QrCode();
        printRow.setQrCode(qrCode);
        final ReverseText reverseText = new ReverseText();
        printRow.setReverseText(reverseText);
        final Section section = new Section();
        printRow.setSection(section);
        final Separator separator = new Separator();
        printRow.setSeparator(separator);
        final Table table = new Table();
        printRow.setTable(table);
        final TableRow tableRow = new TableRow();
        printRow.setTableRow(tableRow);
        final Collection<PrintRow> printRows = Arrays.asList(printRow);
        final Separator separator1 = new Separator();
        final Text text = new Text();
        text.setText("text");
        final Font font = new Font();
        final Font.Size size = new Font.Size();
        size.setXm(0);
        font.setSize(size);
        text.setFont(font);
        separator1.setText(text);

        // Run the test
        PrintRowUtils.add(printRows, separator1);

        // Verify the results
    }

    @Test
    public void testAdd12() {
        // Setup
        final PrintRow printRow = new PrintRow();
        printRow.setContentType("contentType");
        final BarCode barCode = new BarCode();
        printRow.setBarCode(barCode);
        final LabelBarCode labelBarCode = new LabelBarCode();
        printRow.setLabelBarCode(labelBarCode);
        final BlankRow blankRow = new BlankRow();
        printRow.setBlankRow(blankRow);
        final CoordinateRow coordinateRow = new CoordinateRow();
        printRow.setCoordinateRow(coordinateRow);
        final Image image = new Image();
        printRow.setImage(image);
        final KeyValue keyValue = new KeyValue();
        printRow.setKeyValue(keyValue);
        final Line line = new Line();
        printRow.setLine(line);
        final QrCode qrCode = new QrCode();
        printRow.setQrCode(qrCode);
        final ReverseText reverseText = new ReverseText();
        printRow.setReverseText(reverseText);
        final Section section = new Section();
        printRow.setSection(section);
        final Separator separator = new Separator();
        printRow.setSeparator(separator);
        final Table table = new Table();
        printRow.setTable(table);
        final TableRow tableRow = new TableRow();
        printRow.setTableRow(tableRow);
        final Collection<PrintRow> printRows = Arrays.asList(printRow);
        final Table table1 = new Table();
        final Text text = new Text();
        text.setText("text");
        final Font font = new Font();
        final Font.Size size = new Font.Size();
        size.setXm(0);
        font.setSize(size);
        text.setFont(font);
        table1.setHeaders(Arrays.asList(text));

        // Run the test
        PrintRowUtils.add(printRows, table1);

        // Verify the results
    }

    @Test
    public void testAdd13() {
        // Setup
        final PrintRow printRow = new PrintRow();
        printRow.setContentType("contentType");
        final BarCode barCode = new BarCode();
        printRow.setBarCode(barCode);
        final LabelBarCode labelBarCode = new LabelBarCode();
        printRow.setLabelBarCode(labelBarCode);
        final BlankRow blankRow = new BlankRow();
        printRow.setBlankRow(blankRow);
        final CoordinateRow coordinateRow = new CoordinateRow();
        printRow.setCoordinateRow(coordinateRow);
        final Image image = new Image();
        printRow.setImage(image);
        final KeyValue keyValue = new KeyValue();
        printRow.setKeyValue(keyValue);
        final Line line = new Line();
        printRow.setLine(line);
        final QrCode qrCode = new QrCode();
        printRow.setQrCode(qrCode);
        final ReverseText reverseText = new ReverseText();
        printRow.setReverseText(reverseText);
        final Section section = new Section();
        printRow.setSection(section);
        final Separator separator = new Separator();
        printRow.setSeparator(separator);
        final Table table = new Table();
        printRow.setTable(table);
        final TableRow tableRow = new TableRow();
        printRow.setTableRow(tableRow);
        final Collection<PrintRow> printRows = Arrays.asList(printRow);
        final TableRow tableRow1 = new TableRow();
        final Text text = new Text();
        text.setText("text");
        final Font font = new Font();
        final Font.Size size = new Font.Size();
        size.setXm(0);
        font.setSize(size);
        text.setFont(font);
        tableRow1.setColumns(Arrays.asList(text));

        // Run the test
        PrintRowUtils.add(printRows, tableRow1);

        // Verify the results
    }
}
