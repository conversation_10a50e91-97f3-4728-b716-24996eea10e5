package com.holder.saas.print.utils.template.row.composite.table;

import com.holder.saas.print.entity.biz.ItemTableFormatBO;
import com.holder.saas.print.utils.template.calculator.item.PrintItemCalculator;
import com.holderzone.saas.store.dto.print.content.PrintBaseItemDTO;
import com.holderzone.saas.store.dto.print.content.nested.AdditionalCharge;
import com.holderzone.saas.store.dto.print.content.nested.PrintItemRecord;
import com.holderzone.saas.store.dto.print.format.metadata.FormatMetadata;
import com.holderzone.saas.store.dto.print.template.PrintRow;
import com.holderzone.saas.store.dto.print.template.convertable.Font;
import com.holderzone.saas.store.dto.print.template.convertable.Text;
import com.holderzone.saas.store.dto.print.template.printable.*;
import org.junit.Test;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

public class PrintTableUtilsTest {

    @Test
    public void testResolveTableItemRow() {
        // Setup
        final PrintItemRecord printItemRecord = new PrintItemRecord();
        printItemRecord.setItemName("itemName");
        printItemRecord.setItemTypeName("itemTypeName");
        printItemRecord.setPrice(new BigDecimal("0.00"));
        printItemRecord.setPkgCnt(new BigDecimal("0.00"));
        printItemRecord.setNumber(new BigDecimal("0.00"));
        printItemRecord.setUnit("unit");
        printItemRecord.setActualType(0);
        printItemRecord.setActualPrice(new BigDecimal("0.00"));
        printItemRecord.setAsWeight(false);
        printItemRecord.setAsPackage(false);
        printItemRecord.setAsGift(false);
        printItemRecord.setSubItemRecords(Arrays.asList(new PrintItemRecord()));
        printItemRecord.setRemark("remark");
        printItemRecord.setProperty("property");
        printItemRecord.setPropertyPrice(new BigDecimal("0.00"));
        printItemRecord.setIngredientPrice(new BigDecimal("0.00"));
        printItemRecord.setItemState(0);
        printItemRecord.setHasAttr(0);
        printItemRecord.setSingleItemAttrTotal(new BigDecimal("0.00"));
        printItemRecord.setSingleAddPriceTotal(new BigDecimal("0.00"));
        final List<PrintItemRecord> printItemRecordList = Arrays.asList(printItemRecord);
        final ItemTableFormatBO itemTableFormatBo = new ItemTableFormatBO();
        final FormatMetadata layout = new FormatMetadata();
        layout.setEnable(false);
        itemTableFormatBo.setLayout(layout);
        final FormatMetadata typeTotal = new FormatMetadata();
        typeTotal.setEnable(false);
        itemTableFormatBo.setTypeTotal(typeTotal);
        final FormatMetadata itemName = new FormatMetadata();
        itemName.setEnable(false);
        itemTableFormatBo.setItemName(itemName);
        final FormatMetadata itemPrice = new FormatMetadata();
        itemPrice.setEnable(false);
        itemTableFormatBo.setItemPrice(itemPrice);
        final FormatMetadata itemTotal = new FormatMetadata();
        itemTotal.setEnable(false);
        itemTableFormatBo.setItemTotal(itemTotal);
        final FormatMetadata itemUnit = new FormatMetadata();
        itemUnit.setEnable(false);
        itemTableFormatBo.setItemUnit(itemUnit);
        final FormatMetadata itemProperty = new FormatMetadata();
        itemProperty.setEnable(false);
        itemTableFormatBo.setItemProperty(itemProperty);
        final FormatMetadata itemRemark = new FormatMetadata();
        itemRemark.setEnable(false);
        itemTableFormatBo.setItemRemark(itemRemark);
        final FormatMetadata itemNumTotal = new FormatMetadata();
        itemNumTotal.setEnable(false);
        itemTableFormatBo.setItemNumTotal(itemNumTotal);
        final FormatMetadata itemSumTotal = new FormatMetadata();
        itemSumTotal.setEnable(false);
        itemTableFormatBo.setItemSumTotal(itemSumTotal);
        final FormatMetadata additionalCharge = new FormatMetadata();
        additionalCharge.setEnable(false);
        itemTableFormatBo.setAdditionalCharge(additionalCharge);
        itemTableFormatBo.setTakeOut(false);

        final PrintRow printRow = new PrintRow();
        printRow.setContentType("contentType");
        final BarCode barCode = new BarCode();
        printRow.setBarCode(barCode);
        final KeyValue keyValue = new KeyValue();
        printRow.setKeyValue(keyValue);
        final Section section = new Section();
        printRow.setSection(section);
        final Separator separator = new Separator();
        printRow.setSeparator(separator);
        final List<PrintRow> expectedResult = Arrays.asList(printRow);

        // Run the test
        final List<PrintRow> result = PrintTableUtils.resolveTableItemRow(printItemRecordList, new BigDecimal("0.00"),
                0, itemTableFormatBo, "cartSeparator");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testShowTypeHandler() {
        // Setup
        final PrintRow printRow = new PrintRow();
        printRow.setContentType("contentType");
        final BarCode barCode = new BarCode();
        printRow.setBarCode(barCode);
        final KeyValue keyValue = new KeyValue();
        printRow.setKeyValue(keyValue);
        final Section section = new Section();
        printRow.setSection(section);
        final Separator separator = new Separator();
        printRow.setSeparator(separator);
        final TableRowContext context = new TableRowContext(Arrays.asList(printRow), Arrays.asList(0),
                Arrays.asList(false), false);
        final PrintItemRecord printItemRecord = new PrintItemRecord();
        printItemRecord.setItemName("itemName");
        printItemRecord.setItemTypeName("itemTypeName");
        printItemRecord.setPrice(new BigDecimal("0.00"));
        printItemRecord.setPkgCnt(new BigDecimal("0.00"));
        printItemRecord.setNumber(new BigDecimal("0.00"));
        printItemRecord.setUnit("unit");
        printItemRecord.setActualType(0);
        printItemRecord.setActualPrice(new BigDecimal("0.00"));
        printItemRecord.setAsWeight(false);
        printItemRecord.setAsPackage(false);
        printItemRecord.setAsGift(false);
        printItemRecord.setSubItemRecords(Arrays.asList(new PrintItemRecord()));
        printItemRecord.setRemark("remark");
        printItemRecord.setProperty("property");
        printItemRecord.setPropertyPrice(new BigDecimal("0.00"));
        printItemRecord.setIngredientPrice(new BigDecimal("0.00"));
        printItemRecord.setItemState(0);
        printItemRecord.setHasAttr(0);
        printItemRecord.setSingleItemAttrTotal(new BigDecimal("0.00"));
        printItemRecord.setSingleAddPriceTotal(new BigDecimal("0.00"));
        final List<PrintItemRecord> printItemRecordList = Arrays.asList(printItemRecord);
        final PrintItemCalculator printItemCalculator = null;

        // Run the test

        // Verify the results
    }

    @Test
    public void testResolveKitchenItemRow() {
        // Setup
        final PrintBaseItemDTO printDTO = new PrintBaseItemDTO();
        final PrintItemRecord printItemRecord = new PrintItemRecord();
        printItemRecord.setItemName("itemName");
        printItemRecord.setItemTypeName("itemTypeName");
        printItemRecord.setPrice(new BigDecimal("0.00"));
        printItemRecord.setPkgCnt(new BigDecimal("0.00"));
        printItemRecord.setNumber(new BigDecimal("0.00"));
        printItemRecord.setUnit("unit");
        printItemRecord.setActualType(0);
        printItemRecord.setActualPrice(new BigDecimal("0.00"));
        printItemRecord.setAsWeight(false);
        printItemRecord.setAsPackage(false);
        printItemRecord.setAsGift(false);
        printItemRecord.setSubItemRecords(Arrays.asList(new PrintItemRecord()));
        printItemRecord.setRemark("remark");
        printItemRecord.setProperty("property");
        printItemRecord.setPropertyPrice(new BigDecimal("0.00"));
        printItemRecord.setIngredientPrice(new BigDecimal("0.00"));
        printItemRecord.setItemState(0);
        printItemRecord.setHasAttr(0);
        printItemRecord.setSingleItemAttrTotal(new BigDecimal("0.00"));
        printItemRecord.setSingleAddPriceTotal(new BigDecimal("0.00"));
        printDTO.setItemRecordList(Arrays.asList(printItemRecord));

        final ItemTableFormatBO itemTableFormatBo = new ItemTableFormatBO();
        final FormatMetadata layout = new FormatMetadata();
        layout.setEnable(false);
        itemTableFormatBo.setLayout(layout);
        final FormatMetadata typeTotal = new FormatMetadata();
        typeTotal.setEnable(false);
        itemTableFormatBo.setTypeTotal(typeTotal);
        final FormatMetadata itemName = new FormatMetadata();
        itemName.setEnable(false);
        itemTableFormatBo.setItemName(itemName);
        final FormatMetadata itemPrice = new FormatMetadata();
        itemPrice.setEnable(false);
        itemTableFormatBo.setItemPrice(itemPrice);
        final FormatMetadata itemTotal = new FormatMetadata();
        itemTotal.setEnable(false);
        itemTableFormatBo.setItemTotal(itemTotal);
        final FormatMetadata itemUnit = new FormatMetadata();
        itemUnit.setEnable(false);
        itemTableFormatBo.setItemUnit(itemUnit);
        final FormatMetadata itemProperty = new FormatMetadata();
        itemProperty.setEnable(false);
        itemTableFormatBo.setItemProperty(itemProperty);
        final FormatMetadata itemRemark = new FormatMetadata();
        itemRemark.setEnable(false);
        itemTableFormatBo.setItemRemark(itemRemark);
        final FormatMetadata itemNumTotal = new FormatMetadata();
        itemNumTotal.setEnable(false);
        itemTableFormatBo.setItemNumTotal(itemNumTotal);
        final FormatMetadata itemSumTotal = new FormatMetadata();
        itemSumTotal.setEnable(false);
        itemTableFormatBo.setItemSumTotal(itemSumTotal);
        final FormatMetadata additionalCharge = new FormatMetadata();
        additionalCharge.setEnable(false);
        itemTableFormatBo.setAdditionalCharge(additionalCharge);
        itemTableFormatBo.setTakeOut(false);

        final PrintRow printRow = new PrintRow();
        printRow.setContentType("contentType");
        final BarCode barCode = new BarCode();
        printRow.setBarCode(barCode);
        final KeyValue keyValue = new KeyValue();
        printRow.setKeyValue(keyValue);
        final Section section = new Section();
        printRow.setSection(section);
        final Separator separator = new Separator();
        printRow.setSeparator(separator);
        final List<PrintRow> expectedResult = Arrays.asList(printRow);

        // Run the test
        final List<PrintRow> result = PrintTableUtils.resolveKitchenItemRow(printDTO, false, itemTableFormatBo);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetStatsItemColWidthList() {
        assertThat(PrintTableUtils.getStatsItemColWidthList(0)).isEqualTo(Arrays.asList(0));
        assertThat(PrintTableUtils.getStatsItemColWidthList(0)).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testGetStatsPaymentColWidthList() {
        assertThat(PrintTableUtils.getStatsPaymentColWidthList(0)).isEqualTo(Arrays.asList(0));
        assertThat(PrintTableUtils.getStatsPaymentColWidthList(0)).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testResolveTableAdditionalChargeRow() {
        // Setup
        final List<AdditionalCharge> additionalChargeList = Arrays.asList(
                new AdditionalCharge("chargeName", new BigDecimal("0.00"), new BigDecimal("0.00")));
        final ItemTableFormatBO itemTableFormatBo = new ItemTableFormatBO();
        final FormatMetadata layout = new FormatMetadata();
        layout.setEnable(false);
        itemTableFormatBo.setLayout(layout);
        final FormatMetadata typeTotal = new FormatMetadata();
        typeTotal.setEnable(false);
        itemTableFormatBo.setTypeTotal(typeTotal);
        final FormatMetadata itemName = new FormatMetadata();
        itemName.setEnable(false);
        itemTableFormatBo.setItemName(itemName);
        final FormatMetadata itemPrice = new FormatMetadata();
        itemPrice.setEnable(false);
        itemTableFormatBo.setItemPrice(itemPrice);
        final FormatMetadata itemTotal = new FormatMetadata();
        itemTotal.setEnable(false);
        itemTableFormatBo.setItemTotal(itemTotal);
        final FormatMetadata itemUnit = new FormatMetadata();
        itemUnit.setEnable(false);
        itemTableFormatBo.setItemUnit(itemUnit);
        final FormatMetadata itemProperty = new FormatMetadata();
        itemProperty.setEnable(false);
        itemTableFormatBo.setItemProperty(itemProperty);
        final FormatMetadata itemRemark = new FormatMetadata();
        itemRemark.setEnable(false);
        itemTableFormatBo.setItemRemark(itemRemark);
        final FormatMetadata itemNumTotal = new FormatMetadata();
        itemNumTotal.setEnable(false);
        itemTableFormatBo.setItemNumTotal(itemNumTotal);
        final FormatMetadata itemSumTotal = new FormatMetadata();
        itemSumTotal.setEnable(false);
        itemTableFormatBo.setItemSumTotal(itemSumTotal);
        final FormatMetadata additionalCharge = new FormatMetadata();
        additionalCharge.setEnable(false);
        itemTableFormatBo.setAdditionalCharge(additionalCharge);
        itemTableFormatBo.setTakeOut(false);

        final PrintRow printRow = new PrintRow();
        printRow.setContentType("contentType");
        final BarCode barCode = new BarCode();
        printRow.setBarCode(barCode);
        final KeyValue keyValue = new KeyValue();
        printRow.setKeyValue(keyValue);
        final Section section = new Section();
        printRow.setSection(section);
        final Separator separator = new Separator();
        printRow.setSeparator(separator);
        final List<PrintRow> expectedResult = Arrays.asList(printRow);

        // Run the test
        final List<PrintRow> result = PrintTableUtils.resolveTableAdditionalChargeRow(additionalChargeList, 0,
                itemTableFormatBo);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCutOutTextLength() {
        assertThat(PrintTableUtils.cutOutTextLength("salesIncome", Arrays.asList("value"), 0)).isEqualTo("salesIncome");
    }

    @Test
    public void testAddNewLine() {
        // Setup
        final Table table = new Table();
        final Text text = new Text();
        text.setText("text");
        final Font font = new Font();
        final Font.Size size = new Font.Size();
        size.setXm(0);
        font.setSize(size);
        text.setFont(font);
        table.setHeaders(Arrays.asList(text));

        // Run the test
        PrintTableUtils.addNewLine(table, Arrays.asList("value"));

        // Verify the results
    }
}
