package com.holder.saas.print.utils.template.cloud;

import org.junit.Test;

import java.util.Arrays;
import java.util.Collections;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

public class CloudPrinterUtilsTest {

    @Test
    public void testAddSpaceAfter() {
        assertThat(CloudPrinterUtils.addSpaceAfter("content", 0)).isEqualTo("content");
    }

    @Test
    public void testAddSpaceAfterByLength() {
        assertThat(CloudPrinterUtils.addSpaceAfterByLength("content", 0)).isEqualTo("content");
    }

    @Test
    public void testAddSpaceAfterByOverLength() {
        assertThat(CloudPrinterUtils.addSpaceAfterByOverLength("content", 0)).isEqualTo("content");
    }

    @Test
    public void testHandleMixChar() {
        assertThat(CloudPrinterUtils.handleMixChar("content", 0)).isEqualTo("result");
    }

    @Test
    public void testGetContentLength() {
        assertThat(CloudPrinterUtils.getContentLength("content")).isEqualTo(0);
    }

    @Test
    public void testAddSpaceBefore() {
        assertThat(CloudPrinterUtils.addSpaceBefore("content", 0)).isEqualTo("content");
    }

    @Test
    public void testAddSpaceBeforeByLength() {
        assertThat(CloudPrinterUtils.addSpaceBeforeByLength("content", 0)).isEqualTo("content");
    }

    @Test
    public void testIsEn() {
        assertThat(CloudPrinterUtils.isEn("str")).isFalse();
    }

    @Test
    public void testGetStrList1() {
        assertThat(CloudPrinterUtils.getStrList("inputString", 0)).isEqualTo(Arrays.asList("value"));
        assertThat(CloudPrinterUtils.getStrList("inputString", 0)).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testGetStringByEnter() throws Exception {
        assertThat(CloudPrinterUtils.getStringByEnter(0, "string")).isEqualTo("string");
        assertThatThrownBy(() -> CloudPrinterUtils.getStringByEnter(0, "string")).isInstanceOf(Exception.class);
    }

    @Test
    public void testItemNameAddSpace() {
        assertThat(CloudPrinterUtils.itemNameAddSpace("content")).isEqualTo("result");
    }

    @Test
    public void testHandleInterval() {
        assertThat(CloudPrinterUtils.handleInterval("content", "str2", 0)).isEqualTo("result");
    }
}
