package com.holder.saas.print.utils.template.calculator.stats;

import org.junit.Before;
import org.junit.Test;

import java.util.Arrays;

import static org.assertj.core.api.Assertions.assertThat;

public class PrintStats80CalculatorTest {

    private PrintStats80Calculator printStats80CalculatorUnderTest;

    @Before
    public void setUp() throws Exception {
        printStats80CalculatorUnderTest = new PrintStats80Calculator();
    }

    @Test
    public void testGetColumnWidthList() throws Exception {
        assertThat(printStats80CalculatorUnderTest.getColumnWidthList()).isEqualTo(Arrays.asList(0));
    }
}
