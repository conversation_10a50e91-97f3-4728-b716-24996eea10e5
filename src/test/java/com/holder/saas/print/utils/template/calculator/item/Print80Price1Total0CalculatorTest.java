package com.holder.saas.print.utils.template.calculator.item;

import com.holderzone.saas.store.dto.print.template.convertable.Font;
import com.holderzone.saas.store.dto.print.template.convertable.Text;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

@RunWith(MockitoJUnitRunner.class)
public class Print80Price1Total0CalculatorTest {

    @Mock
    private Font mockComponentFont;

    private Print80Price1Total0Calculator print80Price1Total0CalculatorUnderTest;

    @Before
    public void setUp() throws Exception {
        print80Price1Total0CalculatorUnderTest = new Print80Price1Total0Calculator(mockComponentFont);
    }

    @Test
    public void testGetColumnHeaderList() {
        // Setup
        final Text text = new Text();
        text.setText("text");
        final Font font = new Font();
        final Font.Size size = new Font.Size();
        size.setXm(0);
        size.setYm(0);
        font.setSize(size);
        text.setFont(font);
        final List<Text> expectedResult = Arrays.asList(text);

        // Run the test
        final List<Text> result = print80Price1Total0CalculatorUnderTest.getColumnHeaderList();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetColumnWidthList() {
        assertThat(print80Price1Total0CalculatorUnderTest.getColumnWidthList()).isEqualTo(Arrays.asList(0));
    }

    @Test
    public void testGetAlignRights() {
        assertThat(print80Price1Total0CalculatorUnderTest.getAlignRights()).isEqualTo(Arrays.asList(false));
    }

    @Test
    public void testGetItem() {
        // Setup
        final Text text = new Text();
        text.setText("text");
        final Font font = new Font();
        final Font.Size size = new Font.Size();
        size.setXm(0);
        size.setYm(0);
        font.setSize(size);
        text.setFont(font);
        final List<Text> expectedResult = Arrays.asList(text);

        // Run the test
        final List<Text> result = print80Price1Total0CalculatorUnderTest.getItem("item", "price", "quantity",
                "subTotal");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetItemSingle() {
        // Setup
        final Text text = new Text();
        text.setText("text");
        final Font font = new Font();
        final Font.Size size = new Font.Size();
        size.setXm(0);
        size.setYm(0);
        font.setSize(size);
        text.setFont(font);
        final List<Text> expectedResult = Arrays.asList(text);

        // Run the test
        final List<Text> result = print80Price1Total0CalculatorUnderTest.getItemSingle("item");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetSubItem() {
        // Setup
        final Text text = new Text();
        text.setText("text");
        final Font font = new Font();
        final Font.Size size = new Font.Size();
        size.setXm(0);
        size.setYm(0);
        font.setSize(size);
        text.setFont(font);
        final List<Text> expectedResult = Arrays.asList(text);

        // Run the test
        final List<Text> result = print80Price1Total0CalculatorUnderTest.getSubItem("subItemName", "subItemNumber");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetTotal() {
        // Setup
        final Font font = new Font(new Font.Size(0, 0), false, false);
        final Text text = new Text();
        text.setText("text");
        final Font font1 = new Font();
        final Font.Size size = new Font.Size();
        size.setXm(0);
        size.setYm(0);
        font1.setSize(size);
        text.setFont(font1);
        final List<Text> expectedResult = Arrays.asList(text);

        // Run the test
        final List<Text> result = print80Price1Total0CalculatorUnderTest.getTotal("numTotal", "moneyTotal", font);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
