package com.holder.saas.print.utils.template.content;

import com.holderzone.saas.store.dto.print.content.PrintDTO;
import org.junit.Test;

public class PrintRepairUtilsTest {

    @Test
    public void testCorrectSubRecordItemNameAndUnit1() {
        // Setup
        final PrintDTO printDTO = new PrintDTO();
        printDTO.setInvoiceType(0);
        printDTO.setEnterpriseGuid("enterpriseGuid");
        printDTO.setStoreGuid("storeGuid");
        printDTO.setPrintUid("printUid");
        printDTO.setAreaGuid("areaGuid");

        // Run the test
        PrintRepairUtils.correctSubRecordItemNameAndUnit(printDTO);

        // Verify the results
    }
}
