package com.holder.saas.print.utils.template.row.composite;

import com.holder.saas.print.entity.biz.ItemTableFormatBO;
import com.holderzone.saas.store.dto.print.content.nested.*;
import com.holderzone.saas.store.dto.print.format.metadata.CustomMetadata;
import com.holderzone.saas.store.dto.print.format.metadata.FormatMetadata;
import com.holderzone.saas.store.dto.print.template.PrintRow;
import com.holderzone.saas.store.dto.print.template.convertable.Font;
import com.holderzone.saas.store.dto.print.template.convertable.Text;
import com.holderzone.saas.store.dto.print.template.printable.*;
import org.junit.Before;
import org.junit.Test;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.assertEquals;

public class PrintFormatLayoutContainerTest {

    private PrintFormatLayoutContainer printFormatLayoutContainerUnderTest;

    @Before
    public void setUp() throws Exception {
        printFormatLayoutContainerUnderTest = new PrintFormatLayoutContainer();
    }

    @Test
    public void testGetPrintRows() {
        final PrintRow printRow = new PrintRow();
        printRow.setContentType("type");
        final BarCode barCode = new BarCode();
        printRow.setBarCode(barCode);
        final BlankRow blankRow = new BlankRow();
        printRow.setBlankRow(blankRow);
        final Image image = new Image();
        printRow.setImage(image);
        final KeyValue keyValue = new KeyValue();
        printRow.setKeyValue(keyValue);
        final QrCode qrCode = new QrCode();
        printRow.setQrCode(qrCode);
        final ReverseText reverseText = new ReverseText();
        printRow.setReverseText(reverseText);
        final Section section = new Section();
        printRow.setSection(section);
        final Separator separator = new Separator();
        printRow.setSeparator(separator);
        final List<PrintRow> expectedResult = Arrays.asList(printRow);
        assertEquals(expectedResult, printFormatLayoutContainerUnderTest.getPrintRows());
    }

    @Test
    public void testAddHeader() {
        // Setup
        final CustomMetadata headerFormat = new CustomMetadata();
        headerFormat.setEnable(false);
        headerFormat.setType(0);
        headerFormat.setTitle("title");
        headerFormat.setText("foodFinishBarCode");

        // Run the test
        printFormatLayoutContainerUnderTest.addHeader(headerFormat);

        // Verify the results
    }

    @Test
    public void testAddFooter() {
        // Setup
        final CustomMetadata footerFormat = new CustomMetadata();
        footerFormat.setEnable(false);
        footerFormat.setType(0);
        footerFormat.setTitle("title");
        footerFormat.setText("foodFinishBarCode");

        // Run the test
        printFormatLayoutContainerUnderTest.addFooter(footerFormat);

        // Verify the results
    }

    @Test
    public void testAddStoreName() {
        // Setup
        final FormatMetadata formatMetadata = new FormatMetadata(0, 0, 0, false, false);

        // Run the test
        printFormatLayoutContainerUnderTest.addStoreName("storeName", formatMetadata);

        // Verify the results
    }

    @Test
    public void testAddInvoiceType() {
        // Setup
        final FormatMetadata formatMetadata = new FormatMetadata(0, 0, 0, false, false);

        // Run the test
        printFormatLayoutContainerUnderTest.addInvoiceType("invoiceType", formatMetadata);

        // Verify the results
    }

    @Test
    public void testAddOrderRemark() {
        // Setup
        final FormatMetadata formatMetadata = new FormatMetadata(0, 0, 0, false, false);

        // Run the test
        printFormatLayoutContainerUnderTest.addOrderRemark("orderRemark", formatMetadata);

        // Verify the results
    }

    @Test
    public void testAddOrderPrompt() {
        // Setup
        final FormatMetadata formatMetadata = new FormatMetadata(0, 0, 0, false, false);

        // Run the test
        printFormatLayoutContainerUnderTest.addOrderPrompt(formatMetadata);

        // Verify the results
    }

    @Test
    public void testAddTurnTableDetail() {
        // Setup
        final FormatMetadata oriTableFormat = new FormatMetadata(0, 0, 0, false, false);
        final FormatMetadata newTableFormat = new FormatMetadata(0, 0, 0, false, false);

        // Run the test
        printFormatLayoutContainerUnderTest.addTurnTableDetail("destTableName", "destTableName", oriTableFormat,
                newTableFormat);

        // Verify the results
    }

    @Test
    public void testAddMarkOrTableNoAndOrderAndGuest1() {
        // Setup
        final FormatMetadata markOrTableNoFormat = new FormatMetadata(0, 0, 0, false, false);
        final FormatMetadata orderNoFormat = new FormatMetadata(0, 0, 0, false, false);
        final FormatMetadata personNumberFormat = new FormatMetadata(0, 0, 0, false, false);

        // Run the test
        printFormatLayoutContainerUnderTest.addMarkOrTableNoAndOrderAndGuest(0, 0, "markOrTableNo", markOrTableNoFormat,
                "orderNo", orderNoFormat, 0, personNumberFormat);

        // Verify the results
    }

    @Test
    public void testAddOpenTableTime() {
        // Setup
        final FormatMetadata formatMetadata = new FormatMetadata(0, 0, 0, false, false);

        // Run the test
        printFormatLayoutContainerUnderTest.addOpenTableTime(0L, formatMetadata);

        // Verify the results
    }

    @Test
    public void testAddOpenAndCheckTime() {
        // Setup
        final FormatMetadata openTableFormat = new FormatMetadata(0, 0, 0, false, false);
        final FormatMetadata checkoutFormat = new FormatMetadata(0, 0, 0, false, false);

        // Run the test
        printFormatLayoutContainerUnderTest.addOpenAndCheckTime(0, 0, 0L, openTableFormat, 0L, checkoutFormat);

        // Verify the results
    }

    @Test
    public void testAddTotalMoney() {
        // Setup
        // Run the test
        printFormatLayoutContainerUnderTest.addTotalMoney(new BigDecimal("0.00"));

        // Verify the results
    }

    @Test
    public void testAddCombineTotalMoney() {
        // Setup
        // Run the test
        printFormatLayoutContainerUnderTest.addCombineTotalMoney(new BigDecimal("0.00"));

        // Verify the results
    }

    @Test
    public void testAddAdditionalCharge() {
        // Setup
        final List<AdditionalCharge> additionalChargeList = Arrays.asList(
                new AdditionalCharge("chargeName", new BigDecimal("0.00"), new BigDecimal("0.00")));
        final FormatMetadata additionalFeeDetail = new FormatMetadata(0, 0, 0, false, false);
        final FormatMetadata additionalFeeTotal = new FormatMetadata(0, 0, 0, false, false);

        // Run the test
        printFormatLayoutContainerUnderTest.addAdditionalCharge(additionalChargeList, additionalFeeDetail,
                additionalFeeTotal);

        // Verify the results
    }

    @Test
    public void testAddReduceRecordAndPayable() {
        // Setup
        final List<ReduceRecord> reduceRecordList = Arrays.asList(new ReduceRecord("name", new BigDecimal("0.00")));
        final FormatMetadata listFormat = new FormatMetadata(0, 0, 0, false, false);
        final FormatMetadata totalFormat = new FormatMetadata(0, 0, 0, false, false);
        final FormatMetadata payableFormat = new FormatMetadata(0, 0, 0, false, false);
        final FormatMetadata originalPriceFormat = new FormatMetadata(0, 0, 0, false, false);

        // Run the test
        printFormatLayoutContainerUnderTest.addReduceRecordAndPayable(reduceRecordList, listFormat, totalFormat,
                new BigDecimal("0.00"), payableFormat, new BigDecimal("0.00"), originalPriceFormat);

        // Verify the results
    }

    @Test
    public void testAddPayRecordAndActuallyPay() {
        // Setup
        final PayRecord payRecord = new PayRecord();
        payRecord.setPayName("payName");
        payRecord.setAmount(new BigDecimal("0.00"));
        payRecord.setAmountStr("amountStr");
        payRecord.setPaySerialNumber("paySerialNumber");
        payRecord.setExcessAmount(new BigDecimal("0.00"));
        final List<PayRecord> payRecords = Arrays.asList(payRecord);
        final FormatMetadata payRecordsFormat = new FormatMetadata(0, 0, 0, false, false);
        final FormatMetadata payChangedFormat = new FormatMetadata(0, 0, 0, false, false);
        final FormatMetadata actuallyPayFormat = new FormatMetadata(0, 0, 0, false, false);

        // Run the test
        printFormatLayoutContainerUnderTest.addPayRecordAndActuallyPay(payRecords, payRecordsFormat,
                new BigDecimal("0.00"), payChangedFormat, new BigDecimal("0.00"), actuallyPayFormat);

        // Verify the results
    }

    @Test
    public void testAddOpStaffAndPrintTime() {
        // Setup
        final FormatMetadata opStaffFormat = new FormatMetadata(0, 0, 0, false, false);
        final FormatMetadata createTimeFormat = new FormatMetadata(0, 0, 0, false, false);

        // Run the test
        printFormatLayoutContainerUnderTest.addOpStaffAndPrintTime(0, "operatorStaffName", opStaffFormat, 0L,
                createTimeFormat);

        // Verify the results
    }

    @Test
    public void testAddOpStaffAndOpenTableTimeAndPrintTime() {
        // Setup
        final FormatMetadata opStaffFormat = new FormatMetadata(0, 0, 0, false, false);
        final FormatMetadata openTableTimeFormat = new FormatMetadata(0, 0, 0, false, false);
        final FormatMetadata createTimeFormat = new FormatMetadata(0, 0, 0, false, false);
        final FormatMetadata yearMonthDayFormat = new FormatMetadata(0, 0, 0, false, false);

        // Run the test
        printFormatLayoutContainerUnderTest.addOpStaffAndOpenTableTimeAndPrintTime(0, "operatorStaffName",
                opStaffFormat, 0L, openTableTimeFormat, 0L, createTimeFormat, yearMonthDayFormat);

        // Verify the results
    }

    @Test
    public void testAddOpStaffAndOrderTimeAndPrintTime() {
        // Setup
        final FormatMetadata opStaffFormat = new FormatMetadata(0, 0, 0, false, false);
        final FormatMetadata orderTimeFormat = new FormatMetadata(0, 0, 0, false, false);
        final FormatMetadata printTimeFormat = new FormatMetadata(0, 0, 0, false, false);

        // Run the test
        printFormatLayoutContainerUnderTest.addOpStaffAndOrderTimeAndPrintTime(0, "operatorStaffName", opStaffFormat,
                0L, orderTimeFormat, 0L, printTimeFormat);

        // Verify the results
    }

    @Test
    public void testAddOpStaffAndTurnTimeAndPrintTime() {
        // Setup
        final FormatMetadata opStaffFormat = new FormatMetadata(0, 0, 0, false, false);
        final FormatMetadata turnTimeFormat = new FormatMetadata(0, 0, 0, false, false);
        final FormatMetadata createTimeFormat = new FormatMetadata(0, 0, 0, false, false);

        // Run the test
        printFormatLayoutContainerUnderTest.addOpStaffAndTurnTimeAndPrintTime(0, "operatorStaffName", opStaffFormat, 0L,
                turnTimeFormat, 0L, createTimeFormat);

        // Verify the results
    }

    @Test
    public void testAddOpStaffAndRechargeTimeAndPrintTime() {
        // Setup
        final FormatMetadata opStaffFormat = new FormatMetadata(0, 0, 0, false, false);
        final FormatMetadata rechargeTimeFormat = new FormatMetadata(0, 0, 0, false, false);
        final FormatMetadata createTimeFormat = new FormatMetadata(0, 0, 0, false, false);

        // Run the test
        printFormatLayoutContainerUnderTest.addOpStaffAndRechargeTimeAndPrintTime(0, "operatorStaffName", opStaffFormat,
                0L, rechargeTimeFormat, 0L, createTimeFormat);

        // Verify the results
    }

    @Test
    public void testAddOpStaffAndSomeTimeAndPrintTime() {
        // Setup
        final FormatMetadata opStaffFormat = new FormatMetadata(0, 0, 0, false, false);
        final FormatMetadata someTimeFormat = new FormatMetadata(0, 0, 0, false, false);
        final FormatMetadata createTimeFormat = new FormatMetadata(0, 0, 0, false, false);
        final FormatMetadata yearMonthDayFormat = new FormatMetadata(0, 0, 0, false, false);

        // Run the test
        printFormatLayoutContainerUnderTest.addOpStaffAndSomeTimeAndPrintTime(0, "operatorStaffName", opStaffFormat,
                "someTimeDesc", 0L, someTimeFormat, 0L, createTimeFormat, yearMonthDayFormat);

        // Verify the results
    }

    @Test
    public void testAddMemberName() {
        // Setup
        final FormatMetadata memberNameFormat = new FormatMetadata(0, 0, 0, false, false);

        // Run the test
        printFormatLayoutContainerUnderTest.addMemberName("memberName", memberNameFormat);

        // Verify the results
    }

    @Test
    public void testAddMemberPhone() {
        // Setup
        final FormatMetadata memberPhoneFormat = new FormatMetadata(0, 0, 0, false, false);

        // Run the test
        printFormatLayoutContainerUnderTest.addMemberPhone("memberPhone", memberPhoneFormat);

        // Verify the results
    }

    @Test
    public void testAddMemberCardBalance1() {
        // Setup
        final FormatMetadata memberCardBalanceFormat = new FormatMetadata(0, 0, 0, false, false);

        // Run the test
        printFormatLayoutContainerUnderTest.addMemberCardBalance(new BigDecimal("0.00"), memberCardBalanceFormat);

        // Verify the results
    }

    @Test
    public void testAddMemberCardBalance2() {
        // Setup
        final FormatMetadata memberCardBalanceType = new FormatMetadata(0, 0, 0, false, false);
        final FormatMetadata memberCardBalanceFormat = new FormatMetadata(0, 0, 0, false, false);
        final MultiMemberPayRecord multiMemberPayRecord = new MultiMemberPayRecord();
        multiMemberPayRecord.setMemberCardBalance(new BigDecimal("0.00"));
        multiMemberPayRecord.setRechargeAmount(new BigDecimal("0.00"));
        multiMemberPayRecord.setGiftAmount(new BigDecimal("0.00"));
        multiMemberPayRecord.setSubsidyAmount(new BigDecimal("0.00"));
        multiMemberPayRecord.setMemberCardNum("memberCardNum");
        final List<MultiMemberPayRecord> multiMemberPayRecords = Arrays.asList(multiMemberPayRecord);

        // Run the test
        printFormatLayoutContainerUnderTest.addMemberCardBalance(memberCardBalanceType, memberCardBalanceFormat,
                multiMemberPayRecords);

        // Verify the results
    }

    @Test
    public void testAddMemberCardBalanceType() {
        // Setup
        final MultiMemberPayRecord multiMemberPayRecord = new MultiMemberPayRecord();
        multiMemberPayRecord.setMemberCardBalance(new BigDecimal("0.00"));
        multiMemberPayRecord.setRechargeAmount(new BigDecimal("0.00"));
        multiMemberPayRecord.setGiftAmount(new BigDecimal("0.00"));
        multiMemberPayRecord.setSubsidyAmount(new BigDecimal("0.00"));
        multiMemberPayRecord.setMemberCardNum("memberCardNum");

        final FormatMetadata memberCardBalanceType = new FormatMetadata(0, 0, 0, false, false);

        // Run the test
        printFormatLayoutContainerUnderTest.addMemberCardBalanceType(multiMemberPayRecord, memberCardBalanceType);

        // Verify the results
    }

    @Test
    public void testAddMemberCardBalance3() {
        // Setup
        final MultiMemberPayRecord multiMemberPayRecord = new MultiMemberPayRecord();
        multiMemberPayRecord.setMemberCardBalance(new BigDecimal("0.00"));
        multiMemberPayRecord.setRechargeAmount(new BigDecimal("0.00"));
        multiMemberPayRecord.setGiftAmount(new BigDecimal("0.00"));
        multiMemberPayRecord.setSubsidyAmount(new BigDecimal("0.00"));
        multiMemberPayRecord.setMemberCardNum("memberCardNum");

        final FormatMetadata memberCardBalanceFormat = new FormatMetadata(0, 0, 0, false, false);

        // Run the test
        printFormatLayoutContainerUnderTest.addMemberCardBalance(multiMemberPayRecord, memberCardBalanceFormat);

        // Verify the results
    }

    @Test
    public void testAddDebtInfo() {
        // Setup
        final FormatMetadata debtUnitNameFormat = new FormatMetadata(0, 0, 0, false, false);
        final FormatMetadata debtContactNameFormat = new FormatMetadata(0, 0, 0, false, false);
        final FormatMetadata debtContactTelFormat = new FormatMetadata(0, 0, 0, false, false);

        // Run the test
        printFormatLayoutContainerUnderTest.addDebtInfo("debtUnitName", "debtContactName", "debtContactTel",
                debtUnitNameFormat, debtContactNameFormat, debtContactTelFormat);

        // Verify the results
    }

    @Test
    public void testAddInvoiceCodeName() {
        // Setup
        final FormatMetadata memberNameFormat = new FormatMetadata(0, 0, 0, false, false);

        // Run the test
        printFormatLayoutContainerUnderTest.addInvoiceCodeName(memberNameFormat);

        // Verify the results
    }

    @Test
    public void testAddOpStaffAndOpenTableTimeAndCheckTimeAndPrintTime() {
        // Setup
        final FormatMetadata opStaffFormat = new FormatMetadata(0, 0, 0, false, false);
        final FormatMetadata openTableTimeFormat = new FormatMetadata(0, 0, 0, false, false);
        final FormatMetadata checkOutTimeFormat = new FormatMetadata(0, 0, 0, false, false);
        final FormatMetadata createTimeFormat = new FormatMetadata(0, 0, 0, false, false);

    }

    @Test
    public void testAddPrintTime() {
        // Setup
        final FormatMetadata createTimeFormat = new FormatMetadata(0, 0, 0, false, false);

        // Run the test
        printFormatLayoutContainerUnderTest.addPrintTime(0, 0L, createTimeFormat);

        // Verify the results
    }

    @Test
    public void testAddOrderTimeAndPrintTime() {
        // Setup
        // Run the test
        printFormatLayoutContainerUnderTest.addOrderTimeAndPrintTime(0, 0L, 0L);

        // Verify the results
    }

    @Test
    public void testAddTurnTableTime() {
        // Setup
        final FormatMetadata formatMetadata = new FormatMetadata(0, 0, 0, false, false);

        // Run the test
        printFormatLayoutContainerUnderTest.addTurnTableTime(0L, formatMetadata);

        // Verify the results
    }

    @Test
    public void testAddMarkOrTableNo() {
        // Setup
        final FormatMetadata formatMetadata = new FormatMetadata(0, 0, 0, false, false);

        // Run the test
        printFormatLayoutContainerUnderTest.addMarkOrTableNo("markOrTableNo", formatMetadata);

        // Verify the results
    }

    @Test
    public void testAddPresentOrderAndOptionalGuest() {
        // Setup
        final FormatMetadata personNumberFormat = new FormatMetadata(0, 0, 0, false, false);

        // Run the test
        printFormatLayoutContainerUnderTest.addPresentOrderAndOptionalGuest(0, "orderNo", 0, personNumberFormat);

        // Verify the results
    }

    @Test
    public void testAddOptionalOrderAndOptionalGuest() {
        // Setup
        final FormatMetadata orderNoFormat = new FormatMetadata(0, 0, 0, false, false);
        final FormatMetadata personNumberFormat = new FormatMetadata(0, 0, 0, false, false);

        // Run the test
        printFormatLayoutContainerUnderTest.addOptionalOrderAndOptionalGuest(0, "orderNo", orderNoFormat, 0,
                personNumberFormat);

        // Verify the results
    }

    @Test
    public void testAddPayable() {
        // Setup
        final FormatMetadata formatMetadata = new FormatMetadata(0, 0, 0, false, false);

        // Run the test
        printFormatLayoutContainerUnderTest.addPayable(new BigDecimal("0.00"), formatMetadata);

        // Verify the results
    }

    @Test
    public void testAddActuallyPay() {
        // Setup
        final FormatMetadata formatMetadata = new FormatMetadata(0, 0, 0, false, false);

        // Run the test
        printFormatLayoutContainerUnderTest.addActuallyPay(new BigDecimal("0.00"), formatMetadata);

        // Verify the results
    }

    @Test
    public void testAddStoreAddressAndTel() {
        // Setup
        final FormatMetadata storeAddressFormat = new FormatMetadata(0, 0, 0, false, false);
        final FormatMetadata storeTelFormat = new FormatMetadata(0, 0, 0, false, false);

        // Run the test
        printFormatLayoutContainerUnderTest.addStoreAddressAndTel("storeAddress", storeAddressFormat, "storeTel",
                storeTelFormat);

        // Verify the results
    }

    @Test
    public void testAddSeparator() {
        // Setup
        // Run the test
        printFormatLayoutContainerUnderTest.addSeparator();

        // Verify the results
    }

    @Test
    public void testAddBlankRow() {
        // Setup
        // Run the test
        printFormatLayoutContainerUnderTest.addBlankRow();

        // Verify the results
    }

    @Test
    public void testAddSection() {
        // Setup
        final Section section = new Section();
        section.setAlign(Text.Align.Left);
        final Text text = new Text();
        text.setText("destTableName");
        final Font font = new Font();
        final Font.Size size = new Font.Size();
        font.setSize(size);
        text.setFont(font);
        section.setTexts(Arrays.asList(text));

        // Run the test
        printFormatLayoutContainerUnderTest.addSection(section);

        // Verify the results
    }

    @Test
    public void testAddKeyValue() {
        // Setup
        final KeyValue keyValue = new KeyValue("key", "value", false);

        // Run the test
        printFormatLayoutContainerUnderTest.addKeyValue(keyValue);

        // Verify the results
    }

    @Test
    public void testAddImage() {
        // Setup
        final Image image = new Image(0, 0, new int[]{0}, 0, "foodFinishBarCode");

        // Run the test
        printFormatLayoutContainerUnderTest.addImage(image);

        // Verify the results
    }

    @Test
    public void testAddAll() {
        // Setup
        final PrintRow printRow = new PrintRow();
        printRow.setContentType("type");
        final BarCode barCode = new BarCode();
        printRow.setBarCode(barCode);
        final BlankRow blankRow = new BlankRow();
        printRow.setBlankRow(blankRow);
        final Image image = new Image();
        printRow.setImage(image);
        final KeyValue keyValue = new KeyValue();
        printRow.setKeyValue(keyValue);
        final QrCode qrCode = new QrCode();
        printRow.setQrCode(qrCode);
        final ReverseText reverseText = new ReverseText();
        printRow.setReverseText(reverseText);
        final Section section = new Section();
        printRow.setSection(section);
        final Separator separator = new Separator();
        printRow.setSeparator(separator);
        final List<PrintRow> printRows = Arrays.asList(printRow);

        // Run the test
        printFormatLayoutContainerUnderTest.addAll(printRows);

        // Verify the results
    }

    @Test
    public void testAddTableItem() {
        // Setup
        final PrintItemRecord printItemRecord = new PrintItemRecord();
        printItemRecord.setItemName("itemName");
        printItemRecord.setItemTypeName("itemTypeName");
        printItemRecord.setPrice(new BigDecimal("0.00"));
        printItemRecord.setPkgCnt(new BigDecimal("0.00"));
        printItemRecord.setNumber(new BigDecimal("0.00"));
        printItemRecord.setActualType(0);
        printItemRecord.setActualPrice(new BigDecimal("0.00"));
        printItemRecord.setAsWeight(false);
        printItemRecord.setAsPackage(false);
        printItemRecord.setAsGift(false);
        printItemRecord.setSubItemRecords(Arrays.asList(new PrintItemRecord()));
        printItemRecord.setRemark("remark");
        printItemRecord.setProperty("property");
        printItemRecord.setPropertyPrice(new BigDecimal("0.00"));
        printItemRecord.setIngredientPrice(new BigDecimal("0.00"));
        printItemRecord.setItemState(0);
        printItemRecord.setCartId(0);
        printItemRecord.setHasAttr(0);
        printItemRecord.setSingleItemAttrTotal(new BigDecimal("0.00"));
        printItemRecord.setSingleAddPriceTotal(new BigDecimal("0.00"));
        final List<PrintItemRecord> itemRecordList = Arrays.asList(printItemRecord);
        final ItemTableFormatBO itemTableFormatBO = new ItemTableFormatBO();
        final FormatMetadata layout = new FormatMetadata();
        layout.setEnable(false);
        itemTableFormatBO.setLayout(layout);
        final FormatMetadata additionalCharge = new FormatMetadata();
        additionalCharge.setEnable(false);
        itemTableFormatBO.setAdditionalCharge(additionalCharge);
        final FormatMetadata additionalChargeTotal = new FormatMetadata();
        additionalChargeTotal.setEnable(false);
        itemTableFormatBO.setAdditionalChargeTotal(additionalChargeTotal);

        // Run the test
        printFormatLayoutContainerUnderTest.addTableItem(itemRecordList, new BigDecimal("0.00"), 0, itemTableFormatBO);

        // Verify the results
    }

    @Test
    public void testAddTableAdditionalCharge() {
        // Setup
        final List<AdditionalCharge> additionalChargeList = Arrays.asList(
                new AdditionalCharge("chargeName", new BigDecimal("0.00"), new BigDecimal("0.00")));
        final ItemTableFormatBO itemTableFormatBo = new ItemTableFormatBO();
        final FormatMetadata layout = new FormatMetadata();
        layout.setEnable(false);
        itemTableFormatBo.setLayout(layout);
        final FormatMetadata additionalCharge = new FormatMetadata();
        additionalCharge.setEnable(false);
        itemTableFormatBo.setAdditionalCharge(additionalCharge);
        final FormatMetadata additionalChargeTotal = new FormatMetadata();
        additionalChargeTotal.setEnable(false);
        itemTableFormatBo.setAdditionalChargeTotal(additionalChargeTotal);

        // Run the test
        printFormatLayoutContainerUnderTest.addTableAdditionalCharge(additionalChargeList, 0, itemTableFormatBo);

        // Verify the results
    }

    @Test
    public void testAddQrCode() {
        // Setup
        final FormatMetadata qrCodeFormat = new FormatMetadata(0, 0, 0, false, false);

        // Run the test
        printFormatLayoutContainerUnderTest.addQrCode("foodFinishBarCode", qrCodeFormat);

        // Verify the results
    }

    @Test
    public void testAddInvoiceQrCode() {
        // Setup
        final FormatMetadata invoiceAmountFormat = new FormatMetadata(0, 0, 0, false, false);
        final FormatMetadata qrCodeFormat = new FormatMetadata(0, 0, 0, false, false);
        final FormatMetadata invoiceCodeFormat = new FormatMetadata(0, 0, 0, false, false);
        final FormatMetadata invoiceMarkedFormat = new FormatMetadata(0, 0, 0, false, false);

        // Run the test
        printFormatLayoutContainerUnderTest.addInvoiceQrCode("foodFinishBarCode", "invoiceAmount", invoiceAmountFormat,
                qrCodeFormat, invoiceCodeFormat, invoiceMarkedFormat);

        // Verify the results
    }

    @Test
    public void testAppendTakeoutPlatform() {
        // Setup
        final FormatMetadata platformMetaData = new FormatMetadata(0, 0, 0, false, false);

        // Run the test
        printFormatLayoutContainerUnderTest.appendTakeoutPlatform("platform", "platformOrder", true, platformMetaData);

        // Verify the results
    }

    @Test
    public void testAppendTakeoutStoreName() {
        // Setup
        final FormatMetadata storeNameMetaData = new FormatMetadata(0, 0, 0, false, false);

        // Run the test
        printFormatLayoutContainerUnderTest.appendTakeoutStoreName("storeName", storeNameMetaData);

        // Verify the results
    }

    @Test
    public void testAppendTakeoutPayMsg() {
        // Setup
        final FormatMetadata payMsgMetaData = new FormatMetadata(0, 0, 0, false, false);

        // Run the test
        printFormatLayoutContainerUnderTest.appendTakeoutPayMsg("payMsg", payMsgMetaData);

        // Verify the results
    }

    @Test
    public void testAppendTakeoutAbnormal() {
        // Setup
        // Run the test
        printFormatLayoutContainerUnderTest.appendTakeoutAbnormal();

        // Verify the results
    }

    @Test
    public void testAppendTakeoutExpectedDeliveryTime() {
        // Setup
        final FormatMetadata expectTimeMetaData = new FormatMetadata(0, 0, 0, false, false);

        // Run the test
        printFormatLayoutContainerUnderTest.appendTakeoutExpectedDeliveryTime(true,"expectTime", expectTimeMetaData);

        // Verify the results
    }

    @Test
    public void testAppendTakeoutOrderTime() {
        // Setup
        final FormatMetadata orderTimeMetaData = new FormatMetadata(0, 0, 0, false, false);

        // Run the test
        printFormatLayoutContainerUnderTest.appendTakeoutOrderTime(0L, orderTimeMetaData);

        // Verify the results
    }

    @Test
    public void testAppendTakeoutInvoice() {
        // Setup
        // Run the test
        printFormatLayoutContainerUnderTest.appendTakeoutInvoice(false, "invoiceTitle", "taxpayerId");

        // Verify the results
    }

    @Test
    public void testAppendTakeoutRemark() {
        // Setup
        final FormatMetadata remarkMetadata = new FormatMetadata(0, 0, 0, false, false);

        // Run the test
        printFormatLayoutContainerUnderTest.appendTakeoutRemark("remark", "dinnersNumber", remarkMetadata);

        // Verify the results
    }

    @Test
    public void testAppendTakeoutItemList() {
        // Setup
        final PrintItemRecord printItemRecord = new PrintItemRecord();
        printItemRecord.setItemName("itemName");
        printItemRecord.setItemTypeName("itemTypeName");
        printItemRecord.setPrice(new BigDecimal("0.00"));
        printItemRecord.setPkgCnt(new BigDecimal("0.00"));
        printItemRecord.setNumber(new BigDecimal("0.00"));
        printItemRecord.setActualType(0);
        printItemRecord.setActualPrice(new BigDecimal("0.00"));
        printItemRecord.setAsWeight(false);
        printItemRecord.setAsPackage(false);
        printItemRecord.setAsGift(false);
        printItemRecord.setSubItemRecords(Arrays.asList(new PrintItemRecord()));
        printItemRecord.setRemark("remark");
        printItemRecord.setProperty("property");
        printItemRecord.setPropertyPrice(new BigDecimal("0.00"));
        printItemRecord.setIngredientPrice(new BigDecimal("0.00"));
        printItemRecord.setItemState(0);
        printItemRecord.setCartId(0);
        printItemRecord.setHasAttr(0);
        printItemRecord.setSingleItemAttrTotal(new BigDecimal("0.00"));
        printItemRecord.setSingleAddPriceTotal(new BigDecimal("0.00"));
        final List<PrintItemRecord> itemRecordList = Arrays.asList(printItemRecord);
        final ItemTableFormatBO itemTableFormatBO = new ItemTableFormatBO();
        final FormatMetadata layout = new FormatMetadata();
        layout.setEnable(false);
        itemTableFormatBO.setLayout(layout);
        final FormatMetadata additionalCharge = new FormatMetadata();
        additionalCharge.setEnable(false);
        itemTableFormatBO.setAdditionalCharge(additionalCharge);
        final FormatMetadata additionalChargeTotal = new FormatMetadata();
        additionalChargeTotal.setEnable(false);
        itemTableFormatBO.setAdditionalChargeTotal(additionalChargeTotal);

        // Run the test
        printFormatLayoutContainerUnderTest.appendTakeoutItemList(0, itemRecordList, itemTableFormatBO, "问问");

        // Verify the results
    }

    @Test
    public void testAppendTakeoutOrderTotalAmount() {
        // Setup
        final List<AdditionalCharge> additionalChargeList = Arrays.asList(
                new AdditionalCharge("chargeName", new BigDecimal("0.00"), new BigDecimal("0.00")));
        final FormatMetadata itemSumTotalMetadata = new FormatMetadata(0, 0, 0, false, false);
        final FormatMetadata packageTotalMetadata = new FormatMetadata(0, 0, 0, false, false);
        final FormatMetadata shipTotalMetadata = new FormatMetadata(0, 0, 0, false, false);

        // Run the test
        printFormatLayoutContainerUnderTest.appendTakeoutOrderTotalAmount(additionalChargeList, new BigDecimal("0.00"),
                itemSumTotalMetadata, packageTotalMetadata, shipTotalMetadata);

        // Verify the results
    }

    @Test
    public void testAppendTakeoutOrderAmount() {
        // Setup
        final List<ReduceRecord> reduceRecordList = Arrays.asList(new ReduceRecord("name", new BigDecimal("0.00")));
        final FormatMetadata originalPriceMetadata = new FormatMetadata(0, 0, 0, false, false);
        final FormatMetadata actuallyPayMetadata = new FormatMetadata(0, 0, 0, false, false);
        final FormatMetadata discountMetadata = new FormatMetadata(0, 0, 0, false, false);

        // Run the test
        printFormatLayoutContainerUnderTest.appendTakeoutOrderAmount(new BigDecimal("0.00"), new BigDecimal("0.00"),
                reduceRecordList, originalPriceMetadata, actuallyPayMetadata, discountMetadata);

        // Verify the results
    }

    @Test
    public void testAppendTakeoutCustomerInfo() {
        // Setup
        final FormatMetadata customerMetadata = new FormatMetadata(0, 0, 0, false, false);

        // Run the test
        printFormatLayoutContainerUnderTest.appendTakeoutCustomerInfo("platform", "receiverName", "receiverTel",
                "privacyPhone", "receiverAddress", "recipientAddressDesensitization", customerMetadata);

        // Verify the results
    }

    @Test
    public void testAppendTakeoutOrderNoBarCode() {
        // Setup
        final FormatMetadata orderNoMetadata = new FormatMetadata(0, 0, 0, false, false);
        final FormatMetadata orderNoBarCodeMetadata = new FormatMetadata(0, 0, 0, false, false);

        // Run the test
        printFormatLayoutContainerUnderTest.appendTakeoutOrderNoBarCode("orderNo", orderNoMetadata,
                orderNoBarCodeMetadata);

        // Verify the results
    }

    @Test
    public void testAppendTakeoutOpStaffAndPrintTime() {
        // Setup
        final FormatMetadata operatorMetadata = new FormatMetadata(0, 0, 0, false, false);
        final FormatMetadata printTimeMetadata = new FormatMetadata(0, 0, 0, false, false);

        // Run the test
        printFormatLayoutContainerUnderTest.appendTakeoutOpStaffAndPrintTime(0, "destTableName", 0L, operatorMetadata,
                printTimeMetadata);

        // Verify the results
    }

    @Test
    public void testAppendTakeoutAssuranceCard() {
        // Setup
        // Run the test
        printFormatLayoutContainerUnderTest.appendTakeoutAssuranceCard(0);

        // Verify the results
    }

    @Test
    public void testAppendFoodFinishBarCode() {
        // Setup
        final FormatMetadata foodFinishBarCodeFormat = new FormatMetadata(0, 0, 0, false, false);

        // Run the test
        printFormatLayoutContainerUnderTest.appendFoodFinishBarCode("orderNo", "foodFinishBarCode",
                foodFinishBarCodeFormat);

        // Verify the results
    }
}
