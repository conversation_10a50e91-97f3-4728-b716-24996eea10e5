package com.holder.saas.print.utils.template.cloud;

import org.junit.Test;

import static org.assertj.core.api.Assertions.assertThat;

public class FeiePrinterUtilsTest {

    @Test
    public void testAddLineBreak() {
        assertThat(FeiePrinterUtils.addLineBreak("content")).isEqualTo("result");
    }

    @Test
    public void testAddCenterMagnify() {
        assertThat(FeiePrinterUtils.addCenterMagnify("content")).isEqualTo("result");
    }

    @Test
    public void testAddMagnifyOnce() {
        assertThat(FeiePrinterUtils.addMagnifyOnce("content")).isEqualTo("result");
    }

    @Test
    public void testAddMagnifyTwice() {
        assertThat(FeiePrinterUtils.addMagnifyTwice("content")).isEqualTo("result");
    }

    @Test
    public void testAddHeightenOnce() {
        assertThat(FeiePrinterUtils.addHeightenOnce("content")).isEqualTo("result");
    }

    @Test
    public void testAddWidenOnce() {
        assertThat(FeiePrinterUtils.addWidenOnce("content")).isEqualTo("result");
    }

    @Test
    public void testAddBoldFont() {
        assertThat(FeiePrinterUtils.addBoldFont("content")).isEqualTo("result");
    }

    @Test
    public void testAddMagnifyOnceBold() {
        assertThat(FeiePrinterUtils.addMagnifyOnceBold("content")).isEqualTo("result");
    }

    @Test
    public void testAddHeightenOnceBold() {
        assertThat(FeiePrinterUtils.addHeightenOnceBold("content")).isEqualTo("result");
    }

    @Test
    public void testAddWidenOnceBold() {
        assertThat(FeiePrinterUtils.addWidenOnceBold("content")).isEqualTo("result");
    }

    @Test
    public void testAddDividingLine() {
        assertThat(FeiePrinterUtils.addDividingLine()).isEqualTo("--------------------------------<BR>");
    }

    @Test
    public void testAddRight() {
        assertThat(FeiePrinterUtils.addRight("content")).isEqualTo("result");
    }
}
