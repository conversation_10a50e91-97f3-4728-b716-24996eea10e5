package com.holder.saas.print.utils.valid;

import com.holderzone.saas.store.dto.print.PrintRecordReqDTO;
import com.holderzone.saas.store.dto.print.content.PrintDTO;
import org.junit.Test;

import java.util.Arrays;

public class RecordValidUtilsTest {

    @Test
    public void testCreatePrintTaskValidate() {
        // Setup
        final PrintDTO printDto = new PrintDTO();
        printDto.setInvoiceType(0);
        printDto.setEnterpriseGuid("enterpriseGuid");
        printDto.setStoreGuid("storeGuid");
        printDto.setPrintUid("printUid");
        printDto.setAreaGuid("areaGuid");

        // Run the test
        RecordValidUtils.createPrintTaskValidate(printDto);

        // Verify the results
    }

    @Test
    public void testCreateGetContentValidate() {
        // Setup
        final PrintRecordReqDTO printRecordReqDTO = new PrintRecordReqDTO();
        printRecordReqDTO.setMsgId("msgId");
        printRecordReqDTO.setDeviceId("deviceId");
        printRecordReqDTO.setRecordGuid("recordGuid");
        printRecordReqDTO.setPrintStatus(0);
        printRecordReqDTO.setArrayOfRecordGuid(Arrays.asList("value"));

        // Run the test
        RecordValidUtils.createGetContentValidate(printRecordReqDTO);

        // Verify the results
    }
}
