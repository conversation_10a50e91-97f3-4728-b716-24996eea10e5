package com.holder.saas.print.utils.template.calculator.item;

import com.holderzone.saas.store.dto.print.template.convertable.Font;
import org.junit.Test;

public class PrintCalculatorFactoryTest {

    @Test
    public void testCreate() {
        // Setup
        final Font componentFont = new Font(new Font.Size(0, 0), false, false);

        // Run the test
        final PrintItemCalculator result = PrintCalculatorFactory.create(0, false, false, false, componentFont);

        // Verify the results
    }
}
