package com.holder.saas.print.utils.template.row.composite.table;

import com.holderzone.saas.store.dto.print.template.PrintRow;
import com.holderzone.saas.store.dto.print.template.convertable.Font;
import com.holderzone.saas.store.dto.print.template.convertable.Text;
import com.holderzone.saas.store.dto.print.template.printable.KeyValue;
import com.holderzone.saas.store.dto.print.template.printable.Section;
import com.holderzone.saas.store.dto.print.template.printable.Separator;
import com.holderzone.saas.store.dto.print.template.printable.TableRow;
import org.junit.Before;
import org.junit.Test;

import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

public class TableRowContextTest {

    private TableRowContext tableRowContextUnderTest;

    @Before
    public void setUp() throws Exception {
        final PrintRow printRow = new PrintRow();
        printRow.setContentType("contentType");
        final KeyValue keyValue = new KeyValue();
        printRow.setKeyValue(keyValue);
        final Section section = new Section();
        printRow.setSection(section);
        final Separator separator = new Separator();
        printRow.setSeparator(separator);
        final TableRow tableRow = new TableRow();
        printRow.setTableRow(tableRow);
        final List<PrintRow> printRows = Arrays.asList(printRow);
        tableRowContextUnderTest = new TableRowContext(printRows, Arrays.asList(0), Arrays.asList(false), false);
    }

    @Test
    public void testAddHeaders() {
        // Setup
        final Text text = new Text();
        text.setText("text");
        final Font font = new Font();
        final Font.Size size = new Font.Size();
        size.setXm(0);
        size.setYm(0);
        font.setSize(size);
        text.setFont(font);
        final List<Text> headers = Arrays.asList(text);

        // Run the test
        tableRowContextUnderTest.addHeaders(headers);

        // Verify the results
    }

    @Test
    public void testAddRow() {
        // Setup
        final Text text = new Text();
        text.setText("text");
        final Font font = new Font();
        final Font.Size size = new Font.Size();
        size.setXm(0);
        size.setYm(0);
        font.setSize(size);
        text.setFont(font);
        final List<Text> rows = Arrays.asList(text);

        // Run the test
        tableRowContextUnderTest.addRow(rows);

        // Verify the results
    }

    @Test
    public void testAddType() {
        // Setup
        final Text type = new Text();
        type.setText("text");
        final Font font = new Font();
        final Font.Size size = new Font.Size();
        size.setXm(0);
        size.setYm(0);
        font.setSize(size);
        type.setFont(font);

        // Run the test
        tableRowContextUnderTest.addType(type);

        // Verify the results
    }

    @Test
    public void testAddItem() {
        // Setup
        final Text text = new Text();
        text.setText("text");
        final Font font = new Font();
        final Font.Size size = new Font.Size();
        size.setXm(0);
        size.setYm(0);
        font.setSize(size);
        text.setFont(font);
        final List<Text> items = Arrays.asList(text);

        // Run the test
        tableRowContextUnderTest.addItem(items);

        // Verify the results
    }

    @Test
    public void testAddPropRemark() {
        // Setup
        final Text text = new Text();
        text.setText("text");
        final Font font = new Font();
        final Font.Size size = new Font.Size();
        size.setXm(0);
        size.setYm(0);
        font.setSize(size);
        text.setFont(font);

        // Run the test
        tableRowContextUnderTest.addPropRemark(text);

        // Verify the results
    }

    @Test
    public void testAddKeyValue() {
        // Setup
        final KeyValue keyValue = new KeyValue("key", "value", false);

        // Run the test
        tableRowContextUnderTest.addKeyValue(keyValue);

        // Verify the results
    }

    @Test
    public void testAddKeyValueStashed() {
        // Setup
        final KeyValue keyValue = new KeyValue("key", "value", false);

        // Run the test
        tableRowContextUnderTest.addKeyValueStashed(keyValue);

        // Verify the results
    }

    @Test
    public void testApplyStashed() {
        // Setup
        // Run the test
        tableRowContextUnderTest.applyStashed();

        // Verify the results
    }

    @Test
    public void testAddItemSumTotal() {
        // Setup
        final Font itemSumTotalFont = new Font(new Font.Size(0, 0), false, false);

        // Run the test
        tableRowContextUnderTest.addItemSumTotal("separator", itemSumTotalFont);

        // Verify the results
    }

    @Test
    public void testAddItemSeparator() {
        // Setup
        // Run the test
        tableRowContextUnderTest.addItemSeparator();

        // Verify the results
    }

    @Test
    public void testAddSumTotal() {
        // Setup
        final Font itemSumTotalFont = new Font(new Font.Size(0, 0), false, false);

        // Run the test
        tableRowContextUnderTest.addSumTotal("separator", itemSumTotalFont);

        // Verify the results
    }

    @Test
    public void testAddSeparator1() {
        // Setup
        // Run the test
        tableRowContextUnderTest.addSeparator();

        // Verify the results
    }

    @Test
    public void testAddSeparator2() {
        // Setup
        // Run the test
        tableRowContextUnderTest.addSeparator("separator");

        // Verify the results
    }

    @Test
    public void testIsTakeOutGetterAndSetter() {
        final boolean isTakeOut = false;
        tableRowContextUnderTest.setTakeOut(isTakeOut);
        assertThat(tableRowContextUnderTest.isTakeOut()).isFalse();
    }
}
