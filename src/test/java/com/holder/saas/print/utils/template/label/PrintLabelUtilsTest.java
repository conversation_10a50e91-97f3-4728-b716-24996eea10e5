package com.holder.saas.print.utils.template.label;

import com.holderzone.saas.store.dto.print.template.PrintRow;
import com.holderzone.saas.store.dto.print.template.convertable.Text;
import com.holderzone.saas.store.dto.print.template.printable.BarCode;
import com.holderzone.saas.store.dto.print.template.printable.CoordinateRow;
import com.holderzone.saas.store.dto.print.template.printable.LabelBarCode;
import com.holderzone.saas.store.dto.print.template.printable.Section;
import org.junit.Test;

import java.util.Arrays;
import java.util.List;

public class PrintLabelUtilsTest {

    @Test
    public void testAddKeyValueAsCoordinateRow() {
        // Setup
        final PrintRow printRow = new PrintRow();
        printRow.setContentType("contentType");
        final BarCode barCode = new BarCode();
        printRow.setBarCode(barCode);
        final LabelBarCode labelBarCode = new LabelBarCode();
        printRow.setLabelBarCode(labelBarCode);
        final CoordinateRow coordinateRow = new CoordinateRow();
        printRow.setCoordinateRow(coordinateRow);
        final Section section = new Section();
        printRow.setSection(section);
        final List<PrintRow> printRows = Arrays.asList(printRow);

        // Run the test
        PrintLabelUtils.addKeyValueAsCoordinateRow(printRows, "key", "value", 0, 0);

        // Verify the results
    }

    @Test
    public void testAddSeparatorAsSection() {
        // Setup
        final PrintRow printRow = new PrintRow();
        printRow.setContentType("contentType");
        final BarCode barCode = new BarCode();
        printRow.setBarCode(barCode);
        final LabelBarCode labelBarCode = new LabelBarCode();
        printRow.setLabelBarCode(labelBarCode);
        final CoordinateRow coordinateRow = new CoordinateRow();
        printRow.setCoordinateRow(coordinateRow);
        final Section section = new Section();
        printRow.setSection(section);
        final List<PrintRow> printRows = Arrays.asList(printRow);

        // Run the test
        PrintLabelUtils.addSeparatorAsSection(printRows, "text", 0, 0);

        // Verify the results
    }

    @Test
    public void testAddSection1() {
        // Setup
        final PrintRow printRow = new PrintRow();
        printRow.setContentType("contentType");
        final BarCode barCode = new BarCode();
        printRow.setBarCode(barCode);
        final LabelBarCode labelBarCode = new LabelBarCode();
        printRow.setLabelBarCode(labelBarCode);
        final CoordinateRow coordinateRow = new CoordinateRow();
        printRow.setCoordinateRow(coordinateRow);
        final Section section = new Section();
        printRow.setSection(section);
        final List<PrintRow> printRows = Arrays.asList(printRow);

        // Run the test
        PrintLabelUtils.addSection(printRows, "text", 0, 0);

        // Verify the results
    }

    @Test
    public void testAddSection2() {
        // Setup
        final PrintRow printRow = new PrintRow();
        printRow.setContentType("contentType");
        final BarCode barCode = new BarCode();
        printRow.setBarCode(barCode);
        final LabelBarCode labelBarCode = new LabelBarCode();
        printRow.setLabelBarCode(labelBarCode);
        final CoordinateRow coordinateRow = new CoordinateRow();
        printRow.setCoordinateRow(coordinateRow);
        final Section section = new Section();
        printRow.setSection(section);
        final List<PrintRow> printRows = Arrays.asList(printRow);

        // Run the test
        PrintLabelUtils.addSection(printRows, "text", Text.Align.Left, 0, 0);

        // Verify the results
    }

    @Test
    public void testAddBarCode() {
        // Setup
        final PrintRow printRow = new PrintRow();
        printRow.setContentType("contentType");
        final BarCode barCode = new BarCode();
        printRow.setBarCode(barCode);
        final LabelBarCode labelBarCode = new LabelBarCode();
        printRow.setLabelBarCode(labelBarCode);
        final CoordinateRow coordinateRow = new CoordinateRow();
        printRow.setCoordinateRow(coordinateRow);
        final Section section = new Section();
        printRow.setSection(section);
        final List<PrintRow> printRows = Arrays.asList(printRow);

        // Run the test
        PrintLabelUtils.addBarCode(printRows, "serialNumber");

        // Verify the results
    }
}
