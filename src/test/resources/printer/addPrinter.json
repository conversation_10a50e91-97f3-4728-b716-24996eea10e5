{"deviceType": 3, "deviceId": "2306271940179100002", "userGuid": "2043", "userName": "赵亮", "enterpriseGuid": "4895", "enterpriseName": "销售报表测试企业1", "storeGuid": "4897", "storeName": "门店1", "requestTimestamp": 1711697141353, "printerName": "爱福窝", "printerType": 1, "printerIp": "***************", "printerPort": 9100, "staffGuid": "2043", "businessType": 1, "printCount": 1, "printPage": "80", "arrayOfInvoiceType": ["81", "80"], "arrayOfAreaGuid": ["赵江的区域,7082247575004250112", "包厢1,7064852582371426304"], "printerGuid": null, "printCut": 0, "arrayOfItemGuid": ["默认商品,6991311621872156672", "麻辣海鲈鱼,7075403092467908608", "红烧江团,7075403246994456576", "颜狗商品,7098137075475546112", "编码测试,7100414588583149568", "零时菜品,7107558433317978112", "默认商品,6977481832388362240", "my pro,7109839934311104512"], "recordGuid": null, "status": null, "recordGuidList": [], "printNull": false, "printType": null, "isPrintHangUp": false}