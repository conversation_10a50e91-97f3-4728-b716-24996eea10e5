package com.holderzone.saas.store.weixin.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.saas.store.dto.weixin.resp.WxOrderConfigDTO;
import com.holderzone.saas.store.weixin.service.WxStoreOrderConfigService;
import com.holderzone.saas.store.weixin.service.rpc.member.MemberClientService;
import lombok.extern.slf4j.Slf4j;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.TimeUnit;

/**
 * WxStoreOrderConfigServiceImpl Tester.
 *
 * <AUTHOR> name>
 * @version 1.0
 * @since <pre>12/29/2019</pre>
 */
@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
public class WxStoreOrderConfigServiceImplTest {

	@Resource
	private WxStoreOrderConfigService wxStoreOrderConfigService;
	@Resource
	private MemberClientService memberClientService;
	@Resource
	private RedisTemplate<String, Object> redisTemplate;


	@Before
	public void before() throws Exception {
		UserContext userContext = new UserContext();
		userContext.setAllianceId("1fb529b8da78459ca64187f94dc3ae3e");
		userContext.setEnterpriseGuid("6506431195651982337");
		userContext.setStoreGuid("6506453252643487745");
		userContext.setUserName("openId");
		userContext.setUserGuid("oUiAL08FWt7PUCwnIjLEiowCbizI");
		userContext.setSource("8");
		userContext.setStoreName("门店2337");
		String jsonString = JSONObject.toJSONString(userContext);
		System.out.println(jsonString);
		UserContextUtils.put(userContext);
		EnterpriseIdentifier.setEnterpriseGuid(userContext.getEnterpriseGuid());
	}


	@After
	public void after() throws Exception {

	}

	/**
	 * Method: pageOrderConfig(WxStorePageReqDTO wxStorePageReqDTO)
	 */
	@Test
	public void testPageOrderConfig() throws Exception {
//		lock();
	}



	private Boolean tryLock(String key, String value, long time, TimeUnit timeUnit) {
		long endTime = timeUnit.toMillis(time) + System.currentTimeMillis();
		while (endTime > System.currentTimeMillis()) {
			if (lock(key, value)) {
				return true;
			}
		}
		return false;
	}

	private Boolean lock(String key,String value) {
		RedisSerializer<String> stringSerializer = redisTemplate.getStringSerializer();
		return redisTemplate.execute((RedisCallback<Boolean>) connection -> {
			Object set = connection.execute("set"
					, stringSerializer.serialize(key)
					, stringSerializer.serialize(value)
					, "NX".getBytes(StandardCharsets.UTF_8)
					, "PX".getBytes(StandardCharsets.UTF_8)
					, String.valueOf(1000).getBytes(StandardCharsets.UTF_8));
			return set != null;
		});
	}

	/**
	 * Method: saveStoreConfig(WxStoreReqDTO wxStoreReqDTO)
	 */
	@Test
	public void testSaveStoreConfig() throws Exception {
		RedisSerializer<String> stringSerializer = redisTemplate.getStringSerializer();
		byte[] list = "list".getBytes(StandardCharsets.UTF_8);
//		List<String> objects = redisTemplate.executePipelined((RedisCallback<String>) connection -> {
//
//			connection.openPipeline();
//			for (int i = 0; i < 1000; i++) {
//				connection.zAdd(list, i, (i + "").getBytes());
//			}
//		}, redisTemplate.getStringSerializer());


	}

	/**
	 * Method: getDetailConfig(WxStoreReqDTO wxStoreReqDTO)
	 */
	@Test
	public void testGetDetailConfig() throws Exception {
		redisTemplate.opsForZSet().add("list", "tom", 1);
		redisTemplate.opsForZSet().add("list", "bug", 1);
		redisTemplate.opsForZSet().add("list", "mary", 2);
		redisTemplate.opsForZSet().add("list", "bob", 2);
		redisTemplate.opsForZSet().add("list", "alice", 3);
		redisTemplate.opsForZSet().add("list", "jerry", 1);
	}

	@Test
	public void test2(){
		Long remove = redisTemplate.opsForZSet().remove("list", "tom", "bug");
		log.error("remove:{}",remove);
	}

	/**
	 * Method: updateStoreConfig(WxOrderConfigDTO wxOrderConfigDTO)
	 */
	@Test
	public void testUpdateStoreConfig() throws Exception {
		log.info("zcard:{}",redisTemplate.opsForZSet().zCard("list"));
		log.error("zrange:{}",redisTemplate.opsForZSet().range("list", 0, -1));
		log.error("zrange:rever:{}",redisTemplate.opsForZSet().reverseRange("list",0,-1));
		log.error("zrange,score:{}",redisTemplate.opsForZSet().rangeByScore("list",1,1,0,-1));
		log.error("zrank:{}",redisTemplate.opsForZSet().rank("list", "bob"));
		log.error("zcount:{}",redisTemplate.opsForZSet().count("list", 1, 1));
		log.error("zscore:{}",redisTemplate.opsForZSet().score("list","bob"));
	}

	/**
	 * Method: updateBatchStoreConfig(WxOrderConfigUpdateBatchReqDTO wxOrderConfigUpdateBatchReqDTO)
	 */
	@Test
	public void testUpdateBatchStoreConfig() throws Exception {
		redisTemplate.opsForZSet().add("list", "jerry", 5);
	}

	/**
	 * Method: mapOrderConfigDO(List<String> storeGuidList)
	 */
	@Test
	public void testMapOrderConfigDO() throws Exception {
		redisTemplate.opsForZSet().incrementScore("list", "lucy", 1);
		log.error("lucy:{}",redisTemplate.opsForZSet().score("list","lucy"));

	}

	/**
	 * Method: getStoreConfig(String storeGuid)
	 */
	@Test
	public void testGetStoreConfig() throws Exception {
		WxOrderConfigDTO storeConfig = wxStoreOrderConfigService.getStoreConfig(UserContextUtils.getStoreGuid());
		log.error("storeConfig:{}", JSONObject.toJSONString(storeConfig));
	}


	/**
	 * Method: saveOrUpdateInRedis(WxOrderConfigDO wxOrderConfigDO)
	 */
	@Test
	public void testSaveOrUpdateInRedis() throws Exception {
//TODO: Test goes here... 
/* 
try { 
   Method method = WxStoreOrderConfigServiceImpl.getClass().getMethod("saveOrUpdateInRedis", WxOrderConfigDO.class); 
   method.setAccessible(true); 
   method.invoke(<Object>, <Parameters>); 
} catch(NoSuchMethodException e) { 
} catch(IllegalAccessException e) { 
} catch(InvocationTargetException e) { 
} 
*/
	}

	/**
	 * Method: couldEdit(StoreDTO storeDTO, Integer isOpened)
	 */
	@Test
	public void testCouldEdit() throws Exception {
//TODO: Test goes here... 
/* 
try { 
   Method method = WxStoreOrderConfigServiceImpl.getClass().getMethod("couldEdit", StoreDTO.class, Integer.class); 
   method.setAccessible(true); 
   method.invoke(<Object>, <Parameters>); 
} catch(NoSuchMethodException e) { 
} catch(IllegalAccessException e) { 
} catch(InvocationTargetException e) { 
} 
*/
	}

	/**
	 * Method: toOrderConfig(WxOrderConfigDTO wxOrderConfigDTO, Pair<WxStoreInfoDTO, Boolean> storeOpen)
	 */
	@Test
	public void testToOrderConfig() throws Exception {
//TODO: Test goes here... 
/* 
try { 
   Method method = WxStoreOrderConfigServiceImpl.getClass().getMethod("toOrderConfig", WxOrderConfigDTO.class, Pair<WxStoreInfoDTO,.class); 
   method.setAccessible(true); 
   method.invoke(<Object>, <Parameters>); 
} catch(NoSuchMethodException e) { 
} catch(IllegalAccessException e) { 
} catch(InvocationTargetException e) { 
} 
*/
	}

	/**
	 * Method: assertEnableMenuConfig(Pair<WxStoreInfoDTO, Boolean> storeOpen)
	 */
	@Test
	public void testAssertEnableMenuConfig() throws Exception {
//TODO: Test goes here... 
/* 
try { 
   Method method = WxStoreOrderConfigServiceImpl.getClass().getMethod("assertEnableMenuConfig", Pair<WxStoreInfoDTO,.class); 
   method.setAccessible(true); 
   method.invoke(<Object>, <Parameters>); 
} catch(NoSuchMethodException e) { 
} catch(IllegalAccessException e) { 
} catch(InvocationTargetException e) { 
} 
*/
	}

} 
