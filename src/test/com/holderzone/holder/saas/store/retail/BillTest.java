package com.holderzone.holder.saas.store.retail;

import com.alibaba.fastjson.JSON;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.business.manage.HandoverPayQueryDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.order.request.bill.RecoveryReqDTO;
import com.holderzone.saas.store.dto.retail.HangOrderDTO;
import com.holderzone.saas.store.dto.retail.bill.request.RetailCalculateReqDTO;
import com.holderzone.saas.store.dto.retail.dinein.RetailAddGoodsReqDTO;
import com.holderzone.saas.store.dto.retail.dinein.RetailOrderListReqDTO;
import com.holderzone.saas.store.dto.retail.dinein.RetailRemarkReqDTO;
import com.holderzone.saas.store.retail.HolderSaasStoreRetailApplication;
import com.holderzone.saas.store.retail.transform.OrderTransform;
import com.holderzone.saas.store.retail.utils.SpringContextUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import static com.holderzone.saas.store.dto.common.CommonConstant.USER_INFO;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DineInOrderDOTest
 * @date 2019/01/15 11:00
 * @description //TODO
 * @program holder-saas-store-dto
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = HolderSaasStoreRetailApplication.class)
@WebAppConfiguration
public class BillTest {

    private static final String encode = "%7B%22enterpriseGuid%22%3A%226506431195651982337%22%2C%22enterpriseName%22" +
            "%3A%22%E4%BC%81%E4%B8%9A0227%22%2C%22enterpriseNo%22%3A%22********%22%2C%22storeGuid%22%3A" +
            "%226506453252643487745%22%2C%22storeName%22%3A%22%E9%97%A8%E5%BA%970227_3%22%2C%22storeNo%22%3A" +
            "%*********%22%2C%22userGuid%22%3A%226507063794701697025%22%2C%22account%22%3A%********%22%2C%22tel%22%3A" +
            "%22***********%22%2C%22name%22%3A%22wg%22%7D";

    private static final String encode2="%7B%22enterpriseGuid%22%3A%221910251425490880009%22%2C%22enterpriseName%22%3A%22%E4%BC%81%E4%B8%9A0227%22%2C%22enterpriseNo%22%3A%22********%22%2C%22storeGuid%22%3A%221910251426505560008%22%2C%22storeName%22%3A%22%E9%97%A8%E5%BA%970227_3%22%2C%22storeNo%22%3A%*********%22%2C%22userGuid%22%3A%226507063794701697025%22%2C%22account%22%3A%********%22%2C%22tel%22%3A%22***********%22%2C%22name%22%3A%22wg%22%7D\n";
    @Autowired
    private WebApplicationContext wac;
    private MockMvc mockMvc;

    private OrderTransform orderTransform;

    @Autowired
    private ApplicationContext ap;

    @Before
    public void setupMockMvc() throws Exception {
        SpringContextUtil.getInstance().setCfgContext((ConfigurableApplicationContext) ap);
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();
        this.orderTransform = OrderTransform.INSTANCE;
    }

    @Test
    public void addItem() throws Exception {
        //已自测通过
        String str = "{\"deviceType\":3,\"deviceId\":\"1910171604264350001\",\"userGuid\":\"6519051470061436929\",\"userName\":\"lys\",\"enterpriseGuid\":\"6506431195651982337\",\"enterpriseName\":\"企业0227\",\"storeGuid\":\"6506453252643487745\",\"storeName\":\"门店2337\",\"guid\":\"\",\"guestCount\":1,\"diningTableGuid\":\"\",\"diningTableName\":\"\",\"dineInItemDTOS\":[{\"code\":\"6972029810059\",\"currentCount\":1.0,\"isMemberDiscount\":0,\"isPay\":0,\"isWholeDiscount\":0,\"itemGuid\":\"6561863446136771585\",\"itemName\":\"饿 去\",\"itemPrice\":10.0,\"itemState\":1,\"itemType\":4,\"itemTypeGuid\":\"6550654745529846785\",\"itemTypeName\":\"面食类\",\"price\":10.0,\"skuGuid\":\"6561863446182920193\",\"skuName\":\"\",\"unit\":\"份\",\"originalPrice\":10.0,\"memberPrice\":9.8,\"priceChangeType\":0,\"discountPercent\":null,\"freeCount\":0.0,\"freeItemDTOS\":[],\"guid\":null,\"remark\":\"\",\"itemAttrDTOS\":[],\"packageSubgroupDTOS\":[],\"returnCount\":null,\"singleItemAttrTotal\":null,\"urgeNum\":0},{\"code\":\"************\",\"currentCount\":1.0,\"isMemberDiscount\":0,\"isPay\":0,\"isWholeDiscount\":1,\"itemGuid\":\"6550655011292337153\",\"itemName\":\"牛肉面\",\"itemPrice\":10.0,\"itemState\":1,\"itemType\":2,\"itemTypeGuid\":\"6550654745529846785\",\"itemTypeName\":\"面食类\",\"price\":10.0,\"skuGuid\":\"6592675309386713089\",\"skuName\":\"大份\",\"unit\":\"份\",\"originalPrice\":10.0,\"memberPrice\":0.01,\"priceChangeType\":0,\"discountPercent\":null,\"freeCount\":0.0,\"freeItemDTOS\":[],\"guid\":null,\"remark\":\"\",\"itemAttrDTOS\":[],\"packageSubgroupDTOS\":[],\"returnCount\":null,\"singleItemAttrTotal\":null,\"urgeNum\":0},{\"code\":\"************\",\"currentCount\":1.0,\"isMemberDiscount\":0,\"isPay\":0,\"isWholeDiscount\":1,\"itemGuid\":\"6550655011292337153\",\"itemName\":\"牛肉面\",\"itemPrice\":0.01,\"itemState\":1,\"itemType\":2,\"itemTypeGuid\":\"6550654745529846785\",\"itemTypeName\":\"面食类\",\"price\":0.01,\"skuGuid\":\"6550655011342681089\",\"skuName\":\"小份\",\"unit\":\"份\",\"originalPrice\":0.01,\"memberPrice\":0.01,\"priceChangeType\":0,\"discountPercent\":null,\"freeCount\":0.0,\"freeItemDTOS\":[],\"guid\":null,\"remark\":\"\",\"itemAttrDTOS\":[],\"packageSubgroupDTOS\":[],\"returnCount\":null,\"singleItemAttrTotal\":null,\"urgeNum\":0}],\"remark\":\"\",\"print\":1,\"autoMark\":0,\"mark\":\"#888\"}";
        RetailAddGoodsReqDTO create = JacksonUtils.toObject(RetailAddGoodsReqDTO.class, str);
        MvcResult mvcResult = mockMvc.perform(post("/order_item/retail_add_item").header(USER_INFO, encode).accept
                (MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JSON.toJSONString(create)))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();

        System.out.println("sout:" + contentAsString);
    }


    @Test
    public void calculate() throws Exception {
        //已自测通过
        String str = "{\"deviceType\":3,\"deviceId\":\"1910171604264350001\",\"userGuid\":\"6519051470061436929\",\"userName\":\"lys\",\"enterpriseGuid\":\"6506431195651982337\",\"enterpriseName\":\"企业0227\",\"storeGuid\":\"6506453252643487745\",\"storeName\":\"门店2337\",\"concessional\":null,\"memberLogin\":-1,\"memberPhone\":null,\"orderGuid\":\"6594398975183290368\",\"wholeDiscount\":null,\"memberInfoCardGuid\":null,\"memberIntegral\":null,\"verify\":null,\"volumeCode\":null}";
        RetailCalculateReqDTO param = JacksonUtils.toObject(RetailCalculateReqDTO.class, str);

        MvcResult mvcResult = mockMvc.perform(post("/dine_in_bill/calculate").header(USER_INFO, encode).accept
                (MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JSON.toJSONString(param)))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();

        System.out.println("sout:" + contentAsString);
        //返回json：
        /**
         {"guid":"6594398975183290368","deviceType":3,"orderFee":20.01,"orderSurplusFee":20.00,"actuallyPayFee":20.00,"changeFee":0.00,"discountFee":0.01,"discountFeeDetailDTOS":[{"discountName":"赠送优惠","discountType":5,"discountFee":0},{"discountName":"单品折扣","discountType":10,"discountFee":0.00},{"discountName":"整单折扣","discountType":2,"discountFee":0.00},{"discountName":"积分抵扣","discountType":8,"discountFee":0},{"discountName":"系统省零","discountType":4,"discountFee":0.01},{"discountName":"整单让价","discountType":3,"discountFee":0}],"orderNo":"************","state":1,"stateName":"未结账","gmtCreate":"2019-10-28 09:47:13","memberGuid":"0","memberConsumptionGuid":"0","remark":"","retailItemDTOS":[],"memberCardGuid":"0"}
         */
    }

    @Test
    public void orderList() throws Exception {
//        String str = "{\"deviceType\":3,\"deviceId\":\"1910151126040120006\",\"enterpriseGuid\":\"1910251425490880009\",\"enterpriseName\":\"xh\",\"storeGuid\":\"1910251426505560008\",\"storeName\":\"企业0227\",\"userGuid\":\"6589714863180742656\",\"userName\":\"春林1号\",\"beginTime\":\"\",\"currentPage\":1,\"endTime\":\"\",\"goodsName\":\"\",\"pageSize\":2,\"state\":4,\"payWay\":null,\"staffGuid\":null,\"goodsName\":\"品\"}";
        String str = "{\"deviceType\":3,\"deviceId\":\"1910151126040120006\",\"userGuid\":\"1910251425513790006\",\"userName\":\"默认管理员\",\"enterpriseGuid\":\"1910251425490880009\",\"enterpriseName\":\"陈印的零售企业\",\"storeGuid\":\"1910251426505560008\",\"storeName\":\"默认门店\",\"beginTime\":null,\"currentPage\":1,\"endTime\":null,\"goodsName\":20191104000011,\"pageSize\":10,\"state\":null,\"payWay\":null,\"staffGuid\":null,\"orderType\":1}";
        String str1="{\"deviceType\":3,\"deviceId\":\"1910151126040120006\",\"userGuid\":\"1910251425513790006\",\"userName\":\"默认管理员\",\"enterpriseGuid\":\"1910251425490880009\",\"enterpriseName\":\"陈印的零售企业\",\"storeGuid\":\"1910251426505560008\",\"storeName\":\"默认门店\",\"beginTime\":\"2019-11-04 00:00:00\",\"currentPage\":1,\"endTime\":\"2019-11-04 23:59:59\",\"goodsName\":null,\"pageSize\":10,\"state\":null,\"payWay\":null,\"staffGuid\":[\"6596225600996769793\"]}";

        RetailOrderListReqDTO param = JacksonUtils.toObject(RetailOrderListReqDTO.class, str1);
        MvcResult mvcResult = mockMvc.perform(post("/retail_order/order_list").header(USER_INFO, encode2).accept
                (MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JSON.toJSONString(param)))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();

        System.out.println("sout:" + contentAsString);
        //返回json：
        /**
         * {"currentPage":1,"pageSize":10,"totalCount":3,"data":[{"deviceType":3,"storeGuid":"6506453252643487745","storeName":"门店0227_3","currentPage":1,"pageSize":20,"state":1,"stateName":"未结账","orderFee":20.01,"guid":"6594398975183290368","gmtCreate":"2019-10-28T09:47:13","orderNo":"************"},{"deviceType":3,"storeGuid":"6506453252643487745","storeName":"门店0227_3","currentPage":1,"pageSize":20,"state":1,"stateName":"未结账","orderFee":20.01,"guid":"6593095067001421824","gmtCreate":"2019-10-24T19:25:57","orderNo":"************"},{"deviceType":3,"storeGuid":"6506453252643487745","storeName":"门店0227_3","currentPage":1,"pageSize":20,"state":1,"stateName":"未结账","orderFee":19.92,"guid":"6593040474091552768","gmtCreate":"2019-10-24T15:49:01","orderNo":"************"}]}
         */
    }


    @Test
    public void orderDetail() throws Exception {
        //已自测通过
        String str = "{\"deviceType\":3,\"deviceId\":\"1910171604264350001\",\"userGuid\":\"6519051470061436929\",\"userName\":\"lys\",\"enterpriseGuid\":\"6506431195651982337\",\"enterpriseName\":\"企业0227\",\"storeGuid\":\"6506453252643487745\",\"storeName\":\"门店2337\",\"data\":\"6594398975183290368\"}";
        SingleDataDTO param = JacksonUtils.toObject(SingleDataDTO.class, str);
        MvcResult mvcResult = mockMvc.perform(post("/dine_in_order/get_order_detail").header(USER_INFO, encode).accept
                (MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JSON.toJSONString(param)))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();

        System.out.println("sout:" + contentAsString);
        //返回json：
        /**
         *{"guid":"6594398975183290368","deviceTypeName":"一体机","deviceType":3,"orderFee":20.01,"actuallyPayFee":0.00,"changeFee":0.00,"discountFee":20.01,"actuallyPayFeeDetailDTOS":[],"discountFeeDetailDTOS":[],"orderNo":"************","state":1,"stateName":"未结账","gmtCreate":"2019-10-28 09:47:13","memberGuid":"0","memberConsumptionGuid":"0","remark":"","returnItemDTOS":[],"retailItemDTOS":[{"guid":"6594398975992791040","gmtCreate":"2019-10-28T09:47:14","itemGuid":"6561863446136771585","itemName":"饿 去","code":"6972029810059","itemType":4,"itemState":1,"itemTypeGuid":"6550654745529846785","itemTypeName":"面食类","skuGuid":"6561863446182920193","skuName":"","price":10.00,"itemPrice":10.00,"totalDiscountFee":0.00,"currentCount":1.000,"freeCount":0.000,"returnCount":0.000,"unit":"份","isMemberDiscount":0,"isWholeDiscount":0,"priceChangeType":0,"originalPrice":10.00},{"guid":"6594398975996985344","gmtCreate":"2019-10-28T09:47:14","itemGuid":"6550655011292337153","itemName":"牛肉面","code":"************","itemType":2,"itemState":1,"itemTypeGuid":"6550654745529846785","itemTypeName":"面食类","skuGuid":"6592675309386713089","skuName":"大份","price":10.00,"itemPrice":10.00,"totalDiscountFee":0.00,"currentCount":1.000,"freeCount":0.000,"returnCount":0.000,"unit":"份","isMemberDiscount":0,"isWholeDiscount":1,"priceChangeType":0,"originalPrice":10.00},{"guid":"6594398975996985345","gmtCreate":"2019-10-28T09:47:14","itemGuid":"6550655011292337153","itemName":"牛肉面","code":"************","itemType":2,"itemState":1,"itemTypeGuid":"6550654745529846785","itemTypeName":"面食类","skuGuid":"6550655011342681089","skuName":"小份","price":0.01,"itemPrice":0.01,"totalDiscountFee":0.00,"currentCount":1.000,"freeCount":0.000,"returnCount":0.000,"unit":"份","isMemberDiscount":0,"isWholeDiscount":1,"priceChangeType":0,"originalPrice":0.01}],"memberCardGuid":"0"}
         */
    }

    @Test
    public void getOrder() throws Exception {
        String orderGuid = "6590058018853879808";
        MvcResult mvcResult = mockMvc.perform(post("/we_chat/get_order").header(USER_INFO, encode).accept
                (MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(orderGuid))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();

        System.out.println("sout:" + contentAsString);
    }


    @Test
    public void updateRemark() throws Exception {
        //已自测通过
        String str = "{\"deviceType\":3,\"deviceId\":\"1910171604264350001\",\"userGuid\":\"6519051470061436929\",\"userName\":\"lys\",\"enterpriseGuid\":\"6506431195651982337\",\"enterpriseName\":\"企业0227\",\"storeGuid\":\"6506453252643487745\",\"storeName\":\"门店2337\",\"guid\":\"6594398975183290368\",\"guestCount\":4,\"diningTableGuid\":\"6509685650860539905\",\"diningTableName\":\"一楼半滕承伟01\",\"dineInItemDTOS\":[],\"remark\":\"整单备注001\",\"print\":1,\"autoMark\":0,\"mark\":\"\"}";
        RetailRemarkReqDTO param = JacksonUtils.toObject(RetailRemarkReqDTO.class, str);
        MvcResult mvcResult = mockMvc.perform(post("/dine_in_order/update_remark").header(USER_INFO, encode).accept
                (MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JSON.toJSONString(param)))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();

        System.out.println("sout:" + contentAsString);
    }

    @Test
    public void orderHang() throws Exception {
        //已自测通过
        String str = "{\"deviceType\":3,\"deviceId\":\"1910171604264350001\",\"userGuid\":\"6519051470061436929\",\"userName\":\"lys\",\"enterpriseGuid\":\"6506431195651982337\",\"enterpriseName\":\"企业0227\",\"storeGuid\":\"6506453252643487745\",\"storeName\":\"门店2337\",\"order\":\"{\\\"id\\\":17467143331985,\\\"baseOrderAttr\\\":{\\\"guid\\\":\\\"\\\",\\\"guestCount\\\":1,\\\"autoMark\\\":0,\\\"mark\\\":\\\"\\\",\\\"remark\\\":\\\"\\\",\\\"createLocalOrderTime\\\":\\\"13:55:20\\\",\\\"orderOperatingType\\\":-1},\\\"datas\\\":[{\\\"code\\\":null,\\\"currentCount\\\":1.0,\\\"isMemberDiscount\\\":0,\\\"isPay\\\":0,\\\"isWholeDiscount\\\":1,\\\"itemGuid\\\":\\\"6593770058972605441\\\",\\\"itemName\\\":\\\"葡萄2\\\",\\\"itemPrice\\\":99.01,\\\"itemState\\\":1,\\\"itemType\\\":4,\\\"itemTypeGuid\\\":\\\"6550654253296175105\\\",\\\"itemTypeName\\\":\\\"单点类\\\",\\\"price\\\":0.01,\\\"skuGuid\\\":\\\"6593770059018754049\\\",\\\"skuName\\\":\\\"\\\",\\\"unit\\\":\\\"份\\\",\\\"originalPrice\\\":0.01,\\\"memberPrice\\\":null,\\\"priceChangeType\\\":0,\\\"discountPercent\\\":null,\\\"freeCount\\\":0.0,\\\"freeItemDTOS\\\":[],\\\"guid\\\":null,\\\"remark\\\":\\\"\\\",\\\"itemAttrDTOS\\\":[{\\\"attrGroupGuid\\\":\\\"6565113071892406273\\\",\\\"attrGroupName\\\":\\\"测试属性组002\\\",\\\"attrGuid\\\":\\\"6565114551709877249\\\",\\\"attrName\\\":\\\"属性加价99\\\",\\\"attrPrice\\\":99.0,\\\"guid\\\":null,\\\"num\\\":1}],\\\"packageSubgroupDTOS\\\":[],\\\"returnCount\\\":null,\\\"singleItemAttrTotal\\\":null,\\\"urgeNum\\\":0},{\\\"code\\\":\\\"6972029210059\\\",\\\"currentCount\\\":1.0,\\\"isMemberDiscount\\\":0,\\\"isPay\\\":0,\\\"isWholeDiscount\\\":1,\\\"itemGuid\\\":\\\"6550655727194710017\\\",\\\"itemName\\\":\\\"葡萄\\\",\\\"itemPrice\\\":99.01,\\\"itemState\\\":1,\\\"itemType\\\":3,\\\"itemTypeGuid\\\":\\\"6550654253296175105\\\",\\\"itemTypeName\\\":\\\"单点类\\\",\\\"price\\\":0.01,\\\"skuGuid\\\":\\\"6550655727236661249\\\",\\\"skuName\\\":\\\"\\\",\\\"unit\\\":\\\"kg\\\",\\\"originalPrice\\\":0.01,\\\"memberPrice\\\":0.01,\\\"priceChangeType\\\":0,\\\"discountPercent\\\":null,\\\"freeCount\\\":0.0,\\\"freeItemDTOS\\\":[],\\\"guid\\\":null,\\\"remark\\\":\\\"\\\",\\\"itemAttrDTOS\\\":[{\\\"attrGroupGuid\\\":\\\"6565113071892406273\\\",\\\"attrGroupName\\\":\\\"测试属性组002\\\",\\\"attrGuid\\\":\\\"6565114551709877249\\\",\\\"attrName\\\":\\\"属性加价99\\\",\\\"attrPrice\\\":99.0,\\\"guid\\\":null,\\\"num\\\":1}],\\\"packageSubgroupDTOS\\\":[],\\\"returnCount\\\":null,\\\"singleItemAttrTotal\\\":null,\\\"urgeNum\\\":0}],\\\"goodsInfo\\\":{\\\"6593770059018754049\\\":{\\\"itemGuid\\\":\\\"6593770058972605441\\\",\\\"itemType\\\":4,\\\"name\\\":\\\"葡萄2\\\",\\\"nameAbbr\\\":null,\\\"pictureUrl\\\":\\\"http://oss-sit.holderzone.cn/framework-dev/2019-10-26/1572077238508_eac2c.png\\\",\\\"pinyin\\\":\\\"PT2\\\",\\\"sort\\\":1,\\\"typeGuid\\\":\\\"6550654253296175105\\\",\\\"typeName\\\":\\\"单点类\\\",\\\"description\\\":\\\"jty\\\",\\\"tagList\\\":[{\\\"id\\\":\\\"isBestseller\\\",\\\"name\\\":\\\"热销\\\"}],\\\"hasAttr\\\":2,\\\"isFixPkg\\\":0,\\\"isSoldOut\\\":null,\\\"attrGroupList\\\":[{\\\"attrGroupGuid\\\":\\\"6565113071892406273\\\",\\\"name\\\":\\\"测试属性组002\\\",\\\"isMultiChoice\\\":0,\\\"isRequired\\\":1,\\\"attrList\\\":[{\\\"attrGuid\\\":\\\"6565114551709877249\\\",\\\"isDefault\\\":0,\\\"name\\\":\\\"属性加价99\\\",\\\"price\\\":99.0}],\\\"withDefault\\\":0,\\\"showPrice\\\":1,\\\"iconUrl\\\":null}],\\\"skuList\\\":[{\\\"isMemberDiscount\\\":0,\\\"isWholeDiscount\\\":1,\\\"name\\\":\\\"\\\",\\\"salePrice\\\":0.01,\\\"skuGuid\\\":\\\"6593770059018754049\\\",\\\"unit\\\":\\\"份\\\",\\\"memberPrice\\\":null,\\\"code\\\":null,\\\"minOrderNum\\\":1.0,\\\"stock\\\":null,\\\"upc\\\":null,\\\"sellOut\\\":null}],\\\"subgroupList\\\":[],\\\"pinYinAll\\\":\\\"putao2-\\\",\\\"isSellOut\\\":false,\\\"lastNum\\\":null},\\\"6550655727236661249\\\":{\\\"itemGuid\\\":\\\"6550655727194710017\\\",\\\"itemType\\\":3,\\\"name\\\":\\\"葡萄\\\",\\\"nameAbbr\\\":\\\"\\\",\\\"pictureUrl\\\":\\\"http://oss-sit.holderzone.cn/framework-dev/2019-06-29/1561798000761_ef7c3.jpg\\\",\\\"pinyin\\\":\\\"PT\\\",\\\"sort\\\":1,\\\"typeGuid\\\":\\\"6550654253296175105\\\",\\\"typeName\\\":\\\"单点类\\\",\\\"description\\\":\\\"\\\",\\\"tagList\\\":[{\\\"id\\\":\\\"isBestseller\\\",\\\"name\\\":\\\"热销\\\"},{\\\"id\\\":\\\"isNew\\\",\\\"name\\\":\\\"新品\\\"},{\\\"id\\\":\\\"isSign\\\",\\\"name\\\":\\\"招牌\\\"}],\\\"hasAttr\\\":2,\\\"isFixPkg\\\":0,\\\"isSoldOut\\\":null,\\\"attrGroupList\\\":[{\\\"attrGroupGuid\\\":\\\"6565113071892406273\\\",\\\"name\\\":\\\"测试属性组002\\\",\\\"isMultiChoice\\\":0,\\\"isRequired\\\":1,\\\"attrList\\\":[{\\\"attrGuid\\\":\\\"6565114551709877249\\\",\\\"isDefault\\\":0,\\\"name\\\":\\\"属性加价99\\\",\\\"price\\\":99.0}],\\\"withDefault\\\":0,\\\"showPrice\\\":1,\\\"iconUrl\\\":null}],\\\"skuList\\\":[{\\\"isMemberDiscount\\\":0,\\\"isWholeDiscount\\\":1,\\\"name\\\":\\\"\\\",\\\"salePrice\\\":0.01,\\\"skuGuid\\\":\\\"6550655727236661249\\\",\\\"unit\\\":\\\"kg\\\",\\\"memberPrice\\\":0.01,\\\"code\\\":\\\"6972029210059\\\",\\\"minOrderNum\\\":1.0,\\\"stock\\\":null,\\\"upc\\\":null,\\\"sellOut\\\":{\\\"isSoldOut\\\":1,\\\"reminderThreshold\\\":10.0,\\\"residueQuantity\\\":1000.0,\\\"skuGuid\\\":\\\"6550655727236661249\\\"}}],\\\"subgroupList\\\":[],\\\"pinYinAll\\\":\\\"putao-\\\",\\\"isSellOut\\\":false,\\\"lastNum\\\":null}},\\\"operator\\\":\\\"lys\\\"}\",\"hangOrderKey\":null}";
        HangOrderDTO param = JacksonUtils.toObject(HangOrderDTO.class, str);
        MvcResult mvcResult = mockMvc.perform(post("/dine_in_order/hang_order").header(USER_INFO, encode).accept
                (MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JSON.toJSONString(param)))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();

        System.out.println("sout:" + contentAsString);
        //sout:1910281357467050000
    }

    @Test
    public void orderHangList() throws Exception {
        //已自测通过
        String str = "{\"deviceType\":3,\"deviceId\":\"1910171604264350001\",\"userGuid\":\"6519051470061436929\",\"userName\":\"lys\",\"enterpriseGuid\":\"6506431195651982337\",\"enterpriseName\":\"企业0227\",\"storeGuid\":\"6506453252643487745\",\"storeName\":\"门店2337\",\"order\":\"\",\"hangOrderKey\":null}";
        HangOrderDTO param = JacksonUtils.toObject(HangOrderDTO.class, str);
        MvcResult mvcResult = mockMvc.perform(post("/dine_in_order/hang_order_list").header(USER_INFO, encode).accept
                (MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JSON.toJSONString(param)))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();

        System.out.println("sout:" + contentAsString);
        //json返回：
        /**
         * {"1910281357467050000":"{\"id\":17467143331985,\"baseOrderAttr\":{\"guid\":\"\",\"guestCount\":1,\"autoMark\":0,\"mark\":\"\",\"remark\":\"\",\"createLocalOrderTime\":\"13:55:20\",\"orderOperatingType\":-1},\"datas\":[{\"code\":null,\"currentCount\":1.0,\"isMemberDiscount\":0,\"isPay\":0,\"isWholeDiscount\":1,\"itemGuid\":\"6593770058972605441\",\"itemName\":\"葡萄2\",\"itemPrice\":99.01,\"itemState\":1,\"itemType\":4,\"itemTypeGuid\":\"6550654253296175105\",\"itemTypeName\":\"单点类\",\"price\":0.01,\"skuGuid\":\"6593770059018754049\",\"skuName\":\"\",\"unit\":\"份\",\"originalPrice\":0.01,\"memberPrice\":null,\"priceChangeType\":0,\"discountPercent\":null,\"freeCount\":0.0,\"freeItemDTOS\":[],\"guid\":null,\"remark\":\"\",\"itemAttrDTOS\":[{\"attrGroupGuid\":\"6565113071892406273\",\"attrGroupName\":\"测试属性组002\",\"attrGuid\":\"6565114551709877249\",\"attrName\":\"属性加价99\",\"attrPrice\":99.0,\"guid\":null,\"num\":1}],\"packageSubgroupDTOS\":[],\"returnCount\":null,\"singleItemAttrTotal\":null,\"urgeNum\":0},{\"code\":\"6972029210059\",\"currentCount\":1.0,\"isMemberDiscount\":0,\"isPay\":0,\"isWholeDiscount\":1,\"itemGuid\":\"6550655727194710017\",\"itemName\":\"葡萄\",\"itemPrice\":99.01,\"itemState\":1,\"itemType\":3,\"itemTypeGuid\":\"6550654253296175105\",\"itemTypeName\":\"单点类\",\"price\":0.01,\"skuGuid\":\"6550655727236661249\",\"skuName\":\"\",\"unit\":\"kg\",\"originalPrice\":0.01,\"memberPrice\":0.01,\"priceChangeType\":0,\"discountPercent\":null,\"freeCount\":0.0,\"freeItemDTOS\":[],\"guid\":null,\"remark\":\"\",\"itemAttrDTOS\":[{\"attrGroupGuid\":\"6565113071892406273\",\"attrGroupName\":\"测试属性组002\",\"attrGuid\":\"6565114551709877249\",\"attrName\":\"属性加价99\",\"attrPrice\":99.0,\"guid\":null,\"num\":1}],\"packageSubgroupDTOS\":[],\"returnCount\":null,\"singleItemAttrTotal\":null,\"urgeNum\":0}],\"goodsInfo\":{\"6593770059018754049\":{\"itemGuid\":\"6593770058972605441\",\"itemType\":4,\"name\":\"葡萄2\",\"nameAbbr\":null,\"pictureUrl\":\"http://oss-sit.holderzone.cn/framework-dev/2019-10-26/1572077238508_eac2c.png\",\"pinyin\":\"PT2\",\"sort\":1,\"typeGuid\":\"6550654253296175105\",\"typeName\":\"单点类\",\"description\":\"jty\",\"tagList\":[{\"id\":\"isBestseller\",\"name\":\"热销\"}],\"hasAttr\":2,\"isFixPkg\":0,\"isSoldOut\":null,\"attrGroupList\":[{\"attrGroupGuid\":\"6565113071892406273\",\"name\":\"测试属性组002\",\"isMultiChoice\":0,\"isRequired\":1,\"attrList\":[{\"attrGuid\":\"6565114551709877249\",\"isDefault\":0,\"name\":\"属性加价99\",\"price\":99.0}],\"withDefault\":0,\"showPrice\":1,\"iconUrl\":null}],\"skuList\":[{\"isMemberDiscount\":0,\"isWholeDiscount\":1,\"name\":\"\",\"salePrice\":0.01,\"skuGuid\":\"6593770059018754049\",\"unit\":\"份\",\"memberPrice\":null,\"code\":null,\"minOrderNum\":1.0,\"stock\":null,\"upc\":null,\"sellOut\":null}],\"subgroupList\":[],\"pinYinAll\":\"putao2-\",\"isSellOut\":false,\"lastNum\":null},\"6550655727236661249\":{\"itemGuid\":\"6550655727194710017\",\"itemType\":3,\"name\":\"葡萄\",\"nameAbbr\":\"\",\"pictureUrl\":\"http://oss-sit.holderzone.cn/framework-dev/2019-06-29/1561798000761_ef7c3.jpg\",\"pinyin\":\"PT\",\"sort\":1,\"typeGuid\":\"6550654253296175105\",\"typeName\":\"单点类\",\"description\":\"\",\"tagList\":[{\"id\":\"isBestseller\",\"name\":\"热销\"},{\"id\":\"isNew\",\"name\":\"新品\"},{\"id\":\"isSign\",\"name\":\"招牌\"}],\"hasAttr\":2,\"isFixPkg\":0,\"isSoldOut\":null,\"attrGroupList\":[{\"attrGroupGuid\":\"6565113071892406273\",\"name\":\"测试属性组002\",\"isMultiChoice\":0,\"isRequired\":1,\"attrList\":[{\"attrGuid\":\"6565114551709877249\",\"isDefault\":0,\"name\":\"属性加价99\",\"price\":99.0}],\"withDefault\":0,\"showPrice\":1,\"iconUrl\":null}],\"skuList\":[{\"isMemberDiscount\":0,\"isWholeDiscount\":1,\"name\":\"\",\"salePrice\":0.01,\"skuGuid\":\"6550655727236661249\",\"unit\":\"kg\",\"memberPrice\":0.01,\"code\":\"6972029210059\",\"minOrderNum\":1.0,\"stock\":null,\"upc\":null,\"sellOut\":{\"isSoldOut\":1,\"reminderThreshold\":10.0,\"residueQuantity\":1000.0,\"skuGuid\":\"6550655727236661249\"}}],\"subgroupList\":[],\"pinYinAll\":\"putao-\",\"isSellOut\":false,\"lastNum\":null}},\"operator\":\"lys\"}"}
         */
    }


    @Test
    public void gainOrder() throws Exception {
        //已自测通过
        String str = "{\"deviceType\":3,\"deviceId\":\"1910171604264350001\",\"userGuid\":\"6519051470061436929\",\"userName\":\"lys\",\"enterpriseGuid\":\"6506431195651982337\",\"enterpriseName\":\"企业0227\",\"storeGuid\":\"6506453252643487745\",\"storeName\":\"门店2337\",\"order\":\"\",\"hangOrderKey\":\"1910281414303110005\"}";
        HangOrderDTO param = JacksonUtils.toObject(HangOrderDTO.class, str);
        MvcResult mvcResult = mockMvc.perform(post("/dine_in_order/gain_order").header(USER_INFO, encode).accept
                (MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JSON.toJSONString(param)))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();

        System.out.println("sout:" + contentAsString);
    }


    @Test
    public void recovery() throws Exception {
        RecoveryReqDTO recoveryReqDTO = new RecoveryReqDTO();
        recoveryReqDTO.setOrderGuid("6508961594532167681");
        MvcResult mvcResult = mockMvc.perform(post("/dine_in_bill/recovery").header(USER_INFO, encode).accept
                (MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JSON.toJSONString(recoveryReqDTO)))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();

        System.out.println("sout:" + contentAsString);
    }


    @Test
    public void userInfo() throws Exception {
        String encode = URLEncoder.encode("{\"enterpriseGuid\":\"1910251425490880009\",\"enterpriseName\":\"企业0227\"," +
                "\"enterpriseNo\":\"********\",\"storeGuid\":\"1910251426505560008\",\"storeName\":\"门店0227_3\"," +
                "\"storeNo\":\"6148139\",\"userGuid\":\"6507063794701697025\",\"account\":\"200003\"," +
                "\"tel\":\"***********\",\"name\":\"wg\"}", "utf-8");
        System.out.println(encode);

    }


    @Test
    public void handover() throws Exception {
        HandoverPayQueryDTO s = new HandoverPayQueryDTO();
        s.setStoreGuid("6506453252643487745");
        s.setUserGuid("6516603599194161153");
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        s.setGmtCreate(LocalDateTime.parse("2019-04-04 00:00:00", df));
        s.setGmtModified(LocalDateTime.parse("2019-04-08 23:59:59", df));
        MvcResult mvcResult = mockMvc.perform(post("/handover_sheet/handover").header(USER_INFO, encode).accept
                (MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JSON.toJSONString(s)))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();

        System.out.println("sout:" + contentAsString);

    }

}
