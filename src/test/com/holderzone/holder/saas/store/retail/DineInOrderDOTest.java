package com.holderzone.holder.saas.store.retail;

import com.alibaba.fastjson.JSON;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.common.UserInfoDTO;
import com.holderzone.saas.store.dto.order.common.SingleListDTO;
import com.holderzone.saas.store.dto.order.inside.OrderGuidsDTO;
import com.holderzone.saas.store.dto.order.request.bill.BillPayReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CancelOrderReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CreateDineInOrderReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.DineInOrderListReqDTO;
import com.holderzone.saas.store.enums.BaseDeviceTypeEnum;
import com.holderzone.saas.store.retail.HolderSaasStoreRetailApplication;
import com.holderzone.saas.store.retail.utils.SpringContextUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static com.holderzone.saas.store.dto.common.CommonConstant.USER_INFO;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DineInOrderDOTest
 * @date 2019/01/15 11:00
 * @description //TODO
 * @program holder-saas-store-dto
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = HolderSaasStoreRetailApplication.class)
@WebAppConfiguration
public class DineInOrderDOTest {

    private static final String encode = "%7B%22enterpriseGuid%22%3A%226506431195651982337%22%2C%22enterpriseName%22" +
            "%3A%22%E4%BC%81%E4%B8%9A0227%22%2C%22enterpriseNo%22%3A%**********%22%2C%22storeGuid%22%3A" +
            "%226506453252643487745%22%2C%22storeName%22%3A%22%E9%97%A8%E5%BA%970227_3%22%2C%22storeNo%22%3A" +
            "%*********%22%2C%22userGuid%22%3A%226507063794701697025%22%2C%22account%22%3A%********%22%2C%22tel%22%3A" +
            "%*************%22%2C%22name%22%3A%22wg%22%7D";


    @Autowired
    private WebApplicationContext wac;
    private MockMvc mockMvc;

    @Autowired
    private ApplicationContext ap;

    @Before
    public void setupMockMvc() throws Exception {
        SpringContextUtil.getInstance().setCfgContext((ConfigurableApplicationContext) ap);
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();
    }

    /**
     * 1.开台（创建空订单）（内部调用）
     */
    @Test
    public void openTableOrder() throws Exception {
        for (int i = 0; i < 10; i++) {
            CreateDineInOrderReqDTO createDineInOrderReqDTO = new CreateDineInOrderReqDTO();
            createDineInOrderReqDTO.setDiningTableGuid("6493733889788346375");
            createDineInOrderReqDTO.setDiningTableName("C0001");
            createDineInOrderReqDTO.setAreaName("大厅");
            MvcResult mvcResult = mockMvc.perform(post("/dine_in_order/create").accept(MediaType.APPLICATION_JSON_VALUE)
                    .contentType(MediaType.APPLICATION_JSON).content(JSON.toJSONString(createDineInOrderReqDTO)))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
            String contentAsString = mvcResult.getResponse().getContentAsString();
            System.out.println("sout:" + contentAsString);

        }

    }

    /**
     * 2.查看订单详情
     */
    @Test
    public void getOrderDetail() throws Exception {
        SingleListDTO singleListDTO = new SingleListDTO();
        List<String> guidLists = new ArrayList<>();
        guidLists.add("6581784819091374080");
        singleListDTO.setList(guidLists);
        MvcResult mvcResult = mockMvc.perform(post("/we_chat/get_order_detail_list").header(USER_INFO, encode)
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JSON.toJSONString(singleListDTO)))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();

        System.out.println("sout:" + contentAsString);
    }

    /**
     * 3.修改整单备注
     * 4.修改就餐人数
     */
    @Test
    public void updateRemark() throws Exception {
        CreateDineInOrderReqDTO createDineInOrderReqDTO = new CreateDineInOrderReqDTO();
        createDineInOrderReqDTO.setGuid("6496926791473561601");
//        createDineInOrderReqDTO.setRemark("测试整单备注");
        createDineInOrderReqDTO.setGuestCount(1000);
//        MvcResult mvcResult = mockMvc.perform(post("/dine_in_order/update_remark").accept(MediaType
// .APPLICATION_JSON_VALUE)
//                .contentType(MediaType.APPLICATION_JSON).content(JSON.toJSONString(createDineInOrderReqDTO)))
//                .andExpect(status().isOk()).andDo(print()).andReturn();
        MvcResult mvcResult = mockMvc.perform(post("/dine_in_order/update_guest_count").accept(MediaType
                .APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JSON.toJSONString(createDineInOrderReqDTO)))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }

    /**
     * 5.批量获取桌台订单信息
     */
    @Test
    public void batchGetTableInfo() throws Exception {
        OrderGuidsDTO orderGuidsDTO = new OrderGuidsDTO();
        List<String> guids = new ArrayList<>();
        guids.add("6496936403715227649");
        guids.add("6496936404407287809");
        guids.add("6496936404503756801");

        orderGuidsDTO.setOrderGuids(guids);
        MvcResult mvcResult = mockMvc.perform(post("/dine_in_order/batch_get_table_info").accept(MediaType
                .APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JSON.toJSONString(orderGuidsDTO)))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }


    @Test
    public void testPay() throws Exception {
        BillPayReqDTO param = new BillPayReqDTO();
        param.setFastFood(false);
        MvcResult mvcResult = mockMvc.perform(post("/dine_in_bill/pay").accept(MediaType
                .APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JSON.toJSONString(param)))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }


    @Test
    public void updateRemarkOrGuest() throws Exception {
        CreateDineInOrderReqDTO createDineInOrderReqDTO = new CreateDineInOrderReqDTO();
        createDineInOrderReqDTO.setGuid("6493365710805270529");
        createDineInOrderReqDTO.setGuestCount(1111111);
        createDineInOrderReqDTO.setRemark("测试整单备注");
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setEnterpriseGuid("6490862829263766529");
        userInfoDTO.setUserName("zhyyyyyyyyyyyy");
        String encode = URLEncoder.encode(JacksonUtils.writeValueAsString(userInfoDTO), "utf-8");
        MvcResult mvcResult = mockMvc.perform(post("/dine_in_order/update_remark").accept(MediaType
                .APPLICATION_JSON_VALUE).header("userInfo", encode)
                .contentType(MediaType.APPLICATION_JSON).content(JSON.toJSONString(createDineInOrderReqDTO)))
                .andExpect(status().isOk()).andDo(print()).andReturn();
//        MvcResult mvcResult = mockMvc.perform(post("/dine_in_order/update_guest_count").accept(MediaType
//                .APPLICATION_JSON_VALUE).header("userInfo",encode)
//                .contentType(MediaType.APPLICATION_JSON).content(JSON.toJSONString(createDineInOrderReqDTO)))
//                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }

    /**
     * 6.订单列表
     */
    @Test
    public void orderList() throws Exception {
        DineInOrderListReqDTO dineInOrderListReqDTO = new DineInOrderListReqDTO();
        dineInOrderListReqDTO.setBeginTime(LocalDateTime.parse("2019-02-01T11:05:46"));
        dineInOrderListReqDTO.setEndTime(LocalDateTime.parse("2019-02-01T11:05:46"));
        dineInOrderListReqDTO.setState(0);
//        dineInOrderListReqDTO.setState(1);
//        dineInOrderListReqDTO.setState(2);
        dineInOrderListReqDTO.setSearchKey("大厅");
        dineInOrderListReqDTO.setCurrentPage(1);
        dineInOrderListReqDTO.setPageSize(5);
        MvcResult mvcResult = mockMvc.perform(post("/dine_in_order/order_list").accept(MediaType
                .APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JSON.toJSONString(dineInOrderListReqDTO)))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }

    /**
     * 7.作废订单
     */
    @Test
    public void cancelOrder() throws Exception {
        CancelOrderReqDTO cancelOrderReqDTO = new CancelOrderReqDTO();
        cancelOrderReqDTO.setReason("测试作废");
        cancelOrderReqDTO.setOrderGuid("6496936403715227649");
        cancelOrderReqDTO.setDeviceType(BaseDeviceTypeEnum.All_IN_ONE.getCode());
        MvcResult mvcResult = mockMvc.perform(post("/dine_in_order/cancel").accept(MediaType
                .APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JSON.toJSONString(cancelOrderReqDTO)))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }

}
