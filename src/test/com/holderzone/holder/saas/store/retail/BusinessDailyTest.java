package com.holderzone.holder.saas.store.retail;

import com.alibaba.fastjson.JSON;
import com.holderzone.saas.store.dto.business.manage.HandoverPayQueryDTO;
import com.holderzone.saas.store.dto.order.request.bill.BillPayReqDTO;
import com.holderzone.saas.store.dto.order.request.daily.DailyReqDTO;
import com.holderzone.saas.store.retail.HolderSaasStoreRetailApplication;
import com.holderzone.saas.store.retail.utils.SpringContextUtil;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BusinessDailyTest
 * @date 2019/02/13 16:01
 * @description 营业日报测试
 * @program holder-saas-store-trade
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = HolderSaasStoreRetailApplication.class)
@WebAppConfiguration
@Ignore
public class BusinessDailyTest {
    @Autowired
    private WebApplicationContext wac;
    private MockMvc mockMvc;

    @Autowired
    private ApplicationContext ap;

    @Before
    public void setupMockMvc() throws Exception {
        SpringContextUtil.getInstance().setCfgContext((ConfigurableApplicationContext) ap);
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();
    }

    /**
     * 分类销售统计
     * @throws Exception
     */
    @Test
    public void classify() throws Exception {
        DailyReqDTO param = new DailyReqDTO();
        param.setBeginTime("2019-02-01");
        param.setEndTime("2019-02-01");
        param.setOrderItem(3);
        param.setOrderType(2);
        String jsonString = JSON.toJSONString(param);
        MvcResult mvcResult = mockMvc.perform(post("/daily/classify").accept(MediaType
                .APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(jsonString))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }

    @Test
    public void testPay() throws Exception {
        BillPayReqDTO param = new BillPayReqDTO();
        param.setFastFood(true);
        String jsonString = JSON.toJSONString(param);
        MvcResult mvcResult = mockMvc.perform(post("/dine_in_bill/pay").accept(MediaType
                .APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(jsonString))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }

    @Test
    public void testHandoverRecord()  throws Exception {
        HandoverPayQueryDTO handoverPayQueryDTO = new HandoverPayQueryDTO();
        String jsonString = JSON.toJSONString(handoverPayQueryDTO);
        MvcResult mvcResult = mockMvc.perform(post("/daily/handover").accept(MediaType
                .APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(jsonString))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }
}