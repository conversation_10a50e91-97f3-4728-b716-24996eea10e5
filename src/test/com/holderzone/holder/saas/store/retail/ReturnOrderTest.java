package com.holderzone.holder.saas.store.retail;

import com.alibaba.fastjson.JSON;
import com.holderzone.saas.store.dto.retail.bill.request.ItemReqDTO;
import com.holderzone.saas.store.dto.retail.bill.request.ReturnItemReqDTO;
import com.holderzone.saas.store.retail.HolderSaasStoreRetailApplication;
import com.holderzone.saas.store.retail.utils.SpringContextUtil;
import holderzone.holder.saas.store.retail.listener.PersonIntegrationTestListener;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.http.MediaType;
import org.springframework.test.context.TestExecutionListeners;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.support.DependencyInjectionTestExecutionListener;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.math.BigDecimal;
import java.math.MathContext;
import java.util.ArrayList;
import java.util.List;

import static com.holderzone.saas.store.dto.common.CommonConstant.USER_INFO;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReturnOrderTest
 * @date 2019/10/28 10:42
 * @description //TODO
 * @program IdeaProjects
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = HolderSaasStoreRetailApplication.class)
@WebAppConfiguration
@TestExecutionListeners(listeners = {
        PersonIntegrationTestListener.class,
        DependencyInjectionTestExecutionListener.class
})

public class ReturnOrderTest {

    private static final String encode = "%7B%22enterpriseGuid%22%3A%226506431195651982337%22%2C%22enterpriseName%22" +
            "%3A%22%E4%BC%81%E4%B8%9A0227%22%2C%22enterpriseNo%22%3A%**********%22%2C%22storeGuid%22%3A" +
            "%226506453252643487745%22%2C%22storeName%22%3A%22%E9%97%A8%E5%BA%970227_3%22%2C%22storeNo%22%3A" +
            "%*********%22%2C%22userGuid%22%3A%226507063794701697025%22%2C%22account%22%3A%********%22%2C%22tel%22%3A" +
            "%*************%22%2C%22name%22%3A%22wg%22%7D";

    private MockMvc mockMvc;

    @Autowired
    private ApplicationContext ap;

    @Autowired
    private WebApplicationContext wac;

    @Before
    public void setupMockMvc() throws Exception {
        SpringContextUtil.getInstance().setCfgContext((ConfigurableApplicationContext) ap);
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();
    }

    /**
     * 7.作废订单
     */
    @Test
    public void testReturnGoods() throws Exception {
        ReturnItemReqDTO returnGoodReqDTO = new ReturnItemReqDTO();
        returnGoodReqDTO.setOrderGuid("6594572003217244160");
        returnGoodReqDTO.setShouldReturnAmount(new BigDecimal(9.90d,new MathContext(2)));
        returnGoodReqDTO.setActualReturnAmount(new BigDecimal(1_0));
        List<ItemReqDTO> list = new ArrayList<>();
        ItemReqDTO returnGoodsItemDto = new ItemReqDTO();
        returnGoodsItemDto.setItemGuid(6594572003280158720L);
        returnGoodsItemDto.setCount(new BigDecimal(1));
        list.add(returnGoodsItemDto);
        returnGoodReqDTO.setItemsReqDTOS(list);
        MvcResult mvcResult = mockMvc.perform(post("/order_item/return_items").accept(MediaType
                .APPLICATION_JSON_VALUE).header(USER_INFO, encode)
                .contentType(MediaType.APPLICATION_JSON).content(JSON.toJSONString(returnGoodReqDTO)))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }



}