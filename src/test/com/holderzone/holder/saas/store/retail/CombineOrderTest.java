package com.holderzone.holder.saas.store.retail;

import com.alibaba.fastjson.JSON;
import com.holderzone.saas.store.dto.order.request.dinein.CancelOrderReqDTO;
import com.holderzone.saas.store.dto.table.TableInfoDTO;
import com.holderzone.saas.store.dto.table.TableOrderCombineDTO;
import com.holderzone.saas.store.enums.BaseDeviceTypeEnum;
import com.holderzone.saas.store.retail.HolderSaasStoreRetailApplication;
import com.holderzone.saas.store.retail.utils.SpringContextUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.util.ArrayList;
import java.util.List;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DineInOrderDOTest
 * @date 2019/01/15 11:00
 * @description //并单拆单测试
 * @program holder-saas-store-dto
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = HolderSaasStoreRetailApplication.class)
@WebAppConfiguration
public class CombineOrderTest {

    @Autowired
    private WebApplicationContext wac;
    private MockMvc mockMvc;

    @Autowired
    private ApplicationContext ap;

    @Before
    public void setupMockMvc() throws Exception {
        SpringContextUtil.getInstance().setCfgContext((ConfigurableApplicationContext) ap);
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();
    }

    /**
     * 并单
     */
    @Test
    public void combine() throws Exception {
        TableOrderCombineDTO tableOrderCombineDTO = new TableOrderCombineDTO();
        tableOrderCombineDTO.setMainOrderGuid("6497035583150358529");
        List<TableInfoDTO> tableInfoDTOS = new ArrayList<>();
        TableInfoDTO tableInfoDTO = new TableInfoDTO();
        tableInfoDTO.setOrderGuid("6497035583896944641");
        TableInfoDTO tableInfoDTO2 = new TableInfoDTO();
        tableInfoDTO2.setOrderGuid("6497035584001802241");
        TableInfoDTO tableInfoDTO3 = new TableInfoDTO();
        tableInfoDTO3.setOrderGuid("6497035584085688321");
        tableInfoDTOS.add(tableInfoDTO);
        tableInfoDTOS.add(tableInfoDTO2);
        tableInfoDTOS.add(tableInfoDTO3);
        tableOrderCombineDTO.setTableInfoDTOS(tableInfoDTOS);
        MvcResult mvcResult = mockMvc.perform(post("/dine_in_order/combine").accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JSON.toJSONString(tableOrderCombineDTO)))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);


    }

    /**
     * 作废订单/拆单
     */
    @Test
    public void split() throws Exception {
        CancelOrderReqDTO cancelOrderReqDTO = new CancelOrderReqDTO();
        cancelOrderReqDTO.setReason("测试作废");
        cancelOrderReqDTO.setOrderGuid("6497035583150358529");
        cancelOrderReqDTO.setDeviceType(BaseDeviceTypeEnum.All_IN_ONE.getCode());
        MvcResult mvcResult = mockMvc.perform(post("/dine_in_order/cancel").accept(MediaType
                .APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JSON.toJSONString(cancelOrderReqDTO)))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);


    }




}
