package com.holderzone.holder.saas.store.retail;

import com.alibaba.fastjson.JSON;
import com.holderzone.saas.store.dto.order.common.*;
import com.holderzone.saas.store.dto.order.request.dinein.CancelFreeItemReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CreateDineInOrderReqDTO;
import com.holderzone.saas.store.dto.order.request.item.BatchItemReturnOrFreeReqDTO;
import com.holderzone.saas.store.dto.order.request.item.ItemReturnOrFreeReqDTO;
import com.holderzone.saas.store.dto.order.request.item.ServeItemReqDTO;
import com.holderzone.saas.store.retail.HolderSaasStoreRetailApplication;
import com.holderzone.saas.store.retail.utils.SpringContextUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DineInOrderDOTest
 * @date 2019/01/15 11:00
 * @description //TODO
 * @program holder-saas-store-dto
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = HolderSaasStoreRetailApplication.class)
@WebAppConfiguration
public class ItemTest {

    @Autowired
    private WebApplicationContext wac;
    private MockMvc mockMvc;

    @Autowired
    private ApplicationContext ap;

    @Before
    public void setupMockMvc() throws Exception {
        SpringContextUtil.getInstance().setCfgContext((ConfigurableApplicationContext) ap);
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();
    }

    /**
     * 1.加菜（同时赠菜）
     * @throws Exception
     */
    @Test
    public void addItem() throws Exception {
        CreateDineInOrderReqDTO createDineInOrderReqDTO = new CreateDineInOrderReqDTO();
        createDineInOrderReqDTO.setGuid("6503467413718171649");
        List<DineInItemDTO> dineInItemDTOS = new ArrayList<>();
        DineInItemDTO dineInItemDTO = new DineInItemDTO();
        DineInItemDTO dineInItemDTO2 = new DineInItemDTO();

        dineInItemDTOS.add(dineInItemDTO);
        dineInItemDTOS.add(dineInItemDTO2);
        createDineInOrderReqDTO.setDineInItemDTOS(dineInItemDTOS);
        dineInItemDTO.setCode("1");
        dineInItemDTO.setCurrentCount(new BigDecimal(2));
        dineInItemDTO.setIsMemberDiscount(1);
        dineInItemDTO.setIsWholeDiscount(1);
        dineInItemDTO.setItemGuid("111");
        dineInItemDTO.setItemName("可乐鸡翅");
        dineInItemDTO.setItemType(4);
        dineInItemDTO.setItemTypeGuid("1111");
        dineInItemDTO.setItemTypeName("鸡肉类");
        dineInItemDTO.setSkuGuid("11111");
        dineInItemDTO.setSkuName("");
        dineInItemDTO.setUnit("分");
        dineInItemDTO.setPrice(new BigDecimal(1));
        //赠送
        List<FreeItemDTO> freeItemDTOS = new ArrayList<>();
        FreeItemDTO freeItemDTO = new FreeItemDTO();
        FreeItemDTO freeItemDTO2 = new FreeItemDTO();
        freeItemDTOS.add(freeItemDTO);
        freeItemDTOS.add(freeItemDTO2);
        freeItemDTO.setCount(new BigDecimal(1));
        freeItemDTO.setReason("第一次赠送");
        freeItemDTO.setItemState(8);
        freeItemDTO2.setCount(new BigDecimal(2));
        freeItemDTO2.setReason("第2次赠送");
        freeItemDTO2.setItemState(8);
        dineInItemDTO.setFreeItemDTOS(freeItemDTOS);

        dineInItemDTO2.setCode("2");
        dineInItemDTO2.setCurrentCount(new BigDecimal(6));
        dineInItemDTO2.setIsMemberDiscount(1);
        dineInItemDTO2.setIsWholeDiscount(1);
        dineInItemDTO2.setItemGuid("222");
        dineInItemDTO2.setItemName("可乐鸭翅");
        dineInItemDTO2.setItemType(2);
        dineInItemDTO2.setItemTypeGuid("2222");
        dineInItemDTO2.setItemTypeName("鸭肉类");
        dineInItemDTO2.setSkuGuid("22222");
        dineInItemDTO2.setSkuName("中份");
        dineInItemDTO2.setUnit("fen");
        dineInItemDTO2.setPrice(new BigDecimal(2));
        List<ItemAttrDTO> itemAttrDTOS = new ArrayList<>();

        ItemAttrDTO itemAttrDTO = new ItemAttrDTO();
        ItemAttrDTO itemAttrDTO2 = new ItemAttrDTO();
        itemAttrDTOS.add(itemAttrDTO);
        itemAttrDTOS.add(itemAttrDTO2);
        dineInItemDTO2.setItemAttrDTOS(itemAttrDTOS);
        itemAttrDTO.setAttrGroupGuid("111");
        itemAttrDTO.setAttrGroupName("111属性组");
        itemAttrDTO.setAttrGuid("111");
        itemAttrDTO.setAttrName("加冰");
        itemAttrDTO.setAttrPrice(new BigDecimal(2));
        itemAttrDTO.setNum(1);
        itemAttrDTO2.setAttrGroupGuid("222");
        itemAttrDTO2.setAttrGroupName("222属性组");
        itemAttrDTO2.setAttrGuid("222");
        itemAttrDTO2.setAttrName("加肉");
        itemAttrDTO2.setAttrPrice(new BigDecimal(2));
        itemAttrDTO2.setNum(1);

        MvcResult mvcResult = mockMvc.perform(post("/order_item/add_item").accept(MediaType
                .APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JSON.toJSONString(createDineInOrderReqDTO)))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }

    /**
     * 2.批量赠送
     * @throws Exception
     */
    @Test
    public void free() throws Exception {
        BatchItemReturnOrFreeReqDTO batchItemReturnOrFreeReqDTO = new BatchItemReturnOrFreeReqDTO();
        List<BatchItemReturnOrFreeReqDTO.ItemReturnOrFreeReq> itemReturnOrFreeReqs = new ArrayList<>();
        batchItemReturnOrFreeReqDTO.setItemReturnOrFreeReqs(itemReturnOrFreeReqs);
        BatchItemReturnOrFreeReqDTO.ItemReturnOrFreeReq itemReturnOrFreeReq = new BatchItemReturnOrFreeReqDTO
                .ItemReturnOrFreeReq();
        BatchItemReturnOrFreeReqDTO.ItemReturnOrFreeReq itemReturnOrFreeReq2 = new BatchItemReturnOrFreeReqDTO
                .ItemReturnOrFreeReq();
        itemReturnOrFreeReqs.add(itemReturnOrFreeReq);
        itemReturnOrFreeReqs.add(itemReturnOrFreeReq2);
        itemReturnOrFreeReq.setOrderItemGuid("6496998128233742337");
        List<ItemReturnOrFreeReqDTO> itemReturnOrFreeReqDTOS = new ArrayList<>();
        ItemReturnOrFreeReqDTO itemReturnOrFreeReqDTO = new ItemReturnOrFreeReqDTO();
        itemReturnOrFreeReqDTOS.add(itemReturnOrFreeReqDTO);
        itemReturnOrFreeReqDTO.setCount(new BigDecimal(1));
        itemReturnOrFreeReqDTO.setReason("第一个菜赠送一个");
        itemReturnOrFreeReq.setItemReturnOrFreeReqDTOS(itemReturnOrFreeReqDTOS);
        itemReturnOrFreeReq2.setOrderItemGuid("6496998128279879681");
        List<ItemReturnOrFreeReqDTO> itemReturnOrFreeReqDTOS2 = new ArrayList<>();
        ItemReturnOrFreeReqDTO itemReturnOrFreeReqDTO2 = new ItemReturnOrFreeReqDTO();
        ItemReturnOrFreeReqDTO itemReturnOrFreeReqDTO22 = new ItemReturnOrFreeReqDTO();
        itemReturnOrFreeReqDTOS2.add(itemReturnOrFreeReqDTO2);
        itemReturnOrFreeReqDTO2.setCount(new BigDecimal(1));
        itemReturnOrFreeReqDTO2.setReason("第2个菜赠送一个1次");
        itemReturnOrFreeReqDTOS2.add(itemReturnOrFreeReqDTO22);
        itemReturnOrFreeReqDTO22.setCount(new BigDecimal(1));
        itemReturnOrFreeReqDTO22.setReason("第2个菜赠送一个2次");
        //一个划菜
        itemReturnOrFreeReqDTO22.setIsServe(1);
        itemReturnOrFreeReq2.setItemReturnOrFreeReqDTOS(itemReturnOrFreeReqDTOS2);
        String jsonString = JSON.toJSONString(batchItemReturnOrFreeReqDTO);
        MvcResult mvcResult = mockMvc.perform(post("/order_item/free").accept(MediaType
                .APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(jsonString))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }

    /**
     * 3.批量取消赠送
     * @throws Exception
     */
    @Test
    public void cacelFree() throws Exception {
        CancelFreeItemReqDTO cancelFreeItemReqDTO = new CancelFreeItemReqDTO();
        List<String> freeItemGuids = new ArrayList<>();
        freeItemGuids.add("6497006733951500289");
        freeItemGuids.add("6497006734534508545");
        freeItemGuids.add("6497006734635171841");
        cancelFreeItemReqDTO.setFreeItemGuids(freeItemGuids);
        String jsonString = JSON.toJSONString(cancelFreeItemReqDTO);
        MvcResult mvcResult = mockMvc.perform(post("/order_item/cancel_free").accept(MediaType
                .APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(jsonString))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }

    /**
     * 4.批量划菜
     * @throws Exception
     */
    @Test
    public void serveItem() throws Exception {
        ServeItemReqDTO serveItemReqDTO = new ServeItemReqDTO();
        List<ServeItemReqDTO.ServeItem> serveItems = new ArrayList<>();
        serveItemReqDTO.setServeItems(serveItems);
        ServeItemReqDTO.ServeItem serveItem = new ServeItemReqDTO.ServeItem();
        serveItem.setGuid("6496998128279879681");
        serveItem.setType(1);
        ServeItemReqDTO.ServeItem serveItem2 = new ServeItemReqDTO.ServeItem();
        serveItem2.setType(2);
        serveItem2.setGuid("6496998128263102465");
        serveItems.add(serveItem);
        serveItems.add(serveItem2);


        String jsonString = JSON.toJSONString(serveItemReqDTO);
        MvcResult mvcResult = mockMvc.perform(post("/order_item/serve_item").accept(MediaType
                .APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(jsonString))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }

    /**
     * 5.批量退菜
     * @throws Exception
     */
    @Test
    public void returnItem() throws Exception {
        BatchItemReturnOrFreeReqDTO batchItemReturnOrFreeReqDTO = new BatchItemReturnOrFreeReqDTO();
        List<BatchItemReturnOrFreeReqDTO.ItemReturnOrFreeReq> itemReturnOrFreeReqs = new ArrayList<>();
        batchItemReturnOrFreeReqDTO.setItemReturnOrFreeReqs(itemReturnOrFreeReqs);
        BatchItemReturnOrFreeReqDTO.ItemReturnOrFreeReq itemReturnOrFreeReq = new BatchItemReturnOrFreeReqDTO
                .ItemReturnOrFreeReq();
        BatchItemReturnOrFreeReqDTO.ItemReturnOrFreeReq itemReturnOrFreeReq2 = new BatchItemReturnOrFreeReqDTO
                .ItemReturnOrFreeReq();
        itemReturnOrFreeReqs.add(itemReturnOrFreeReq);
        itemReturnOrFreeReqs.add(itemReturnOrFreeReq2);
        itemReturnOrFreeReq.setOrderItemGuid("6497287508160675841");
        List<ItemReturnOrFreeReqDTO> itemReturnOrFreeReqDTOS = new ArrayList<>();
        ItemReturnOrFreeReqDTO itemReturnOrFreeReqDTO = new ItemReturnOrFreeReqDTO();
        itemReturnOrFreeReqDTOS.add(itemReturnOrFreeReqDTO);
        itemReturnOrFreeReqDTO.setCount(new BigDecimal(1));
        itemReturnOrFreeReqDTO.setReason("第一个菜退一个已经赠送的");
        itemReturnOrFreeReqDTO.setIsFree(1);
        itemReturnOrFreeReqDTO.setFreeGuid("6497287508185841665");

        itemReturnOrFreeReq.setItemReturnOrFreeReqDTOS(itemReturnOrFreeReqDTOS);
        itemReturnOrFreeReq2.setOrderItemGuid("6497287508202618881");
        List<ItemReturnOrFreeReqDTO> itemReturnOrFreeReqDTOS2 = new ArrayList<>();
        ItemReturnOrFreeReqDTO itemReturnOrFreeReqDTO2 = new ItemReturnOrFreeReqDTO();
        itemReturnOrFreeReqDTOS2.add(itemReturnOrFreeReqDTO2);
        itemReturnOrFreeReqDTO2.setCount(new BigDecimal(1));
        itemReturnOrFreeReqDTO2.setReason("第2个菜退一个");
        itemReturnOrFreeReqDTO2.setIsFree(0);
        itemReturnOrFreeReq2.setItemReturnOrFreeReqDTOS(itemReturnOrFreeReqDTOS2);
        String jsonString = JSON.toJSONString(batchItemReturnOrFreeReqDTO);
        System.out.println();
//        MvcResult mvcResult = mockMvc.perform(post("/order_item/return").accept(MediaType
//                .APPLICATION_JSON_VALUE)
//                .contentType(MediaType.APPLICATION_JSON).content(jsonString))
//                .andExpect(status().isOk()).andDo(print()).andReturn();
//        String contentAsString = mvcResult.getResponse().getContentAsString();
//        System.out.println("sout:" + contentAsString);
    }

    /**
     * 6.套餐
     * @throws Exception
     */
    @Test
    public void AddPackageItem() throws Exception {
        CreateDineInOrderReqDTO createDineInOrderReqDTO = new CreateDineInOrderReqDTO();
        createDineInOrderReqDTO.setGuid("6503467413718171649");
        List<DineInItemDTO> dineInItemDTOS = new ArrayList<>();
        DineInItemDTO dineInItemDTO = new DineInItemDTO();

        dineInItemDTOS.add(dineInItemDTO);
        createDineInOrderReqDTO.setDineInItemDTOS(dineInItemDTOS);
        dineInItemDTO.setCode("1");
        dineInItemDTO.setCurrentCount(new BigDecimal(2));
        dineInItemDTO.setIsMemberDiscount(1);
        dineInItemDTO.setIsWholeDiscount(1);
        dineInItemDTO.setItemGuid("111");
        dineInItemDTO.setItemName("套餐主项");
        dineInItemDTO.setItemType(1);
        dineInItemDTO.setItemTypeGuid("1111");
        dineInItemDTO.setItemTypeName("鸡肉类");
        dineInItemDTO.setSkuGuid("11111");
        dineInItemDTO.setSkuName("");
        dineInItemDTO.setUnit("分");
        dineInItemDTO.setPrice(new BigDecimal(1));

        List<PackageSubgroupDTO> packageSubgroupDTOS = new ArrayList<>();
        dineInItemDTO.setPackageSubgroupDTOS(packageSubgroupDTOS);
        PackageSubgroupDTO packageSubgroupDTO = new PackageSubgroupDTO();

        packageSubgroupDTOS.add(packageSubgroupDTO);
        packageSubgroupDTO.setSubgroupGuid("11111111");
        packageSubgroupDTO.setSubgroupName("主食分组");
        List<SubDineInItemDTO> subDineInItemDTOS = new ArrayList<>();
        SubDineInItemDTO subDineInItemDTO = new SubDineInItemDTO();
        subDineInItemDTOS.add(subDineInItemDTO);
        subDineInItemDTO.setCode("2");
        subDineInItemDTO.setCurrentCount(new BigDecimal(2));
        subDineInItemDTO.setPackageDefaultCount(new BigDecimal(2));
//        subDineInItemDTO.setIsMemberDiscount(1);
//        subDineInItemDTO.setIsWholeDiscount(1);
        subDineInItemDTO.setItemGuid("222");
        subDineInItemDTO.setItemName("可乐鸭翅");
        subDineInItemDTO.setItemType(2);
        subDineInItemDTO.setItemTypeGuid("2222");
        subDineInItemDTO.setItemTypeName("鸭肉类");
        subDineInItemDTO.setSkuGuid("22222");
        subDineInItemDTO.setSkuName("中份");
        subDineInItemDTO.setUnit("fen");
        subDineInItemDTO.setPrice(new BigDecimal(2));
        subDineInItemDTO.setAddPrice(new BigDecimal(5));
        List<ItemAttrDTO> itemAttrDTOS = new ArrayList<>();

        ItemAttrDTO itemAttrDTO = new ItemAttrDTO();
        ItemAttrDTO itemAttrDTO2 = new ItemAttrDTO();
        itemAttrDTOS.add(itemAttrDTO);
        itemAttrDTOS.add(itemAttrDTO2);
        subDineInItemDTO.setItemAttrDTOS(itemAttrDTOS);
        itemAttrDTO.setAttrGroupGuid("111");
        itemAttrDTO.setAttrGroupName("111属性组");
        itemAttrDTO.setAttrGuid("111");
        itemAttrDTO.setAttrName("加冰");
        itemAttrDTO.setAttrPrice(new BigDecimal(2));
        itemAttrDTO.setNum(1);
        itemAttrDTO2.setAttrGroupGuid("222");
        itemAttrDTO2.setAttrGroupName("222属性组");
        itemAttrDTO2.setAttrGuid("222");
        itemAttrDTO2.setAttrName("加肉");
        itemAttrDTO2.setAttrPrice(new BigDecimal(2));
        itemAttrDTO2.setNum(1);
        packageSubgroupDTO.setSubDineInItemDTOS(subDineInItemDTOS);


        MvcResult mvcResult = mockMvc.perform(post("/order_item/add_item").accept(MediaType
                .APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JSON.toJSONString(createDineInOrderReqDTO)))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }



}
