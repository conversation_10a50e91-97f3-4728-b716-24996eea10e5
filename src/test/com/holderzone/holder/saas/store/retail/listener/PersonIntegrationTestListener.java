package holderzone.holder.saas.store.retail.listener;

import org.springframework.test.context.TestContext;
import org.springframework.test.context.support.AbstractTestExecutionListener;


public class PersonIntegrationTestListener extends AbstractTestExecutionListener {

    private long beforeMill;

    private long afterMill;

    @Override
    public void prepareTestInstance(TestContext testContext) throws Exception {

//        System.err.println("prepare : " + testContext.getTestMethod());

    }


    @Override
    public void beforeTestMethod(TestContext testContext) throws Exception {

        System.err.println("before : " + testContext.getTestMethod());

    }

    @Override
    public void afterTestMethod(TestContext testContext) throws Exception {

        System.err.println("after : " + testContext.getTestMethod());
    }

    public final int getOrder(){
        return HIGHEST_PRECEDENCE;
    }

}
