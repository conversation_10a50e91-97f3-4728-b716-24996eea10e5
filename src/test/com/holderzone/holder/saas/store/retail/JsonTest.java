package com.holderzone.holder.saas.store.retail;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.retail.HolderSaasStoreRetailApplication;
import com.holderzone.saas.store.retail.utils.SpringContextUtil;
import lombok.Data;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DineInOrderDOTest
 * @date 2019/01/15 11:00
 * @description //TODO
 * @program holder-saas-store-dto
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = HolderSaasStoreRetailApplication.class)
@WebAppConfiguration
public class JsonTest {

    @Autowired
    private WebApplicationContext wac;
    private MockMvc mockMvc;

    @Autowired
    private ApplicationContext ap;

    @Before
    public void setupMockMvc() throws Exception {
        SpringContextUtil.getInstance().setCfgContext((ConfigurableApplicationContext) ap);
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();
    }

    @Test
    public void stackOverflowErrorTest() throws Exception {
        TestPojo testPojo = new TestPojo();
        testPojo.setName("test");
        List<TestPojo> testPojos = new ArrayList<>();
//        TestPojo testPojo1 = new TestPojo();
//        testPojo.setName("test");
        // testPojos.add(testPojo);
        testPojo.setTests(testPojos);
        System.out.println(JacksonUtils.writeValueAsString(testPojo));
    }

    @Data
    class TestPojo {
        private String name;
        private List<TestPojo> tests;
    }

}
