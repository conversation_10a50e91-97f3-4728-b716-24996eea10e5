[35m[2025-06-23 14:30:17:657][m[WARN ][main][org.springframework.web.servlet.handler.AbstractHandlerExceptionResolver.logException:198][][Resolved [org.springframework.web.bind.MethodArgumentNotValidException: Validation failed for argument [0] in public java.lang.Boolean com.holderzone.holder.saas.store.deposit.controller.DepositController.createDepositItem(com.holderzone.saas.store.dto.deposit.req.DepositCreateReqDTO): [Field error in object 'depositCreateReqDTO' on field 'goods': rejected value [[]]; codes [NotEmpty.depositCreateReqDTO.goods,NotEmpty.goods,NotEmpty.java.util.List,NotEmpty]; arguments [org.springframework.context.support.DefaultMessageSourceResolvable: codes [depositCreateReqDTO.goods,goods]; arguments []; default message [goods]]; default message [�Ĵ���Ʒ���ϲ���Ϊ��]] ]] 
[35m[2025-06-23 14:30:17:757][m[WARN ][main][org.springframework.web.util.UrlPathHelper.decodeInternal:472][][Could not decode request string [] with encoding 'Encoding': falling back to platform default encoding; exception message: Encoding] 
[35m[2025-06-23 14:30:17:758][m[WARN ][main][org.springframework.web.util.UrlPathHelper.decodeInternal:472][][Could not decode request string [/deposit/deposit_expire_remind] with encoding 'Encoding': falling back to platform default encoding; exception message: Encoding] 
[35m[2025-06-23 14:30:17:758][m[WARN ][main][org.springframework.web.util.UrlPathHelper.decodeInternal:472][][Could not decode request string [] with encoding 'Encoding': falling back to platform default encoding; exception message: Encoding] 
[35m[2025-06-23 14:30:17:758][m[WARN ][main][org.springframework.web.util.UrlPathHelper.decodeInternal:472][][Could not decode request string [/deposit/deposit_expire_remind] with encoding 'Encoding': falling back to platform default encoding; exception message: Encoding] 
[35m[2025-06-23 14:30:17:758][m[WARN ][main][org.springframework.web.util.UrlPathHelper.decodeInternal:472][][Could not decode request string [] with encoding 'Encoding': falling back to platform default encoding; exception message: Encoding] 
[35m[2025-06-23 14:30:17:760][m[WARN ][main][org.springframework.web.util.UrlPathHelper.decodeInternal:472][][Could not decode request string [/deposit/deposit_expire_remind] with encoding 'Encoding': falling back to platform default encoding; exception message: Encoding] 
[35m[2025-06-23 14:30:17:767][m[WARN ][main][org.springframework.web.util.UrlPathHelper.decodeInternal:472][][Could not decode request string [deposit_expire_remind] with encoding 'Encoding': falling back to platform default encoding; exception message: Encoding] 
[35m[2025-06-23 14:30:17:768][m[WARN ][main][org.springframework.web.util.UrlPathHelper.decodeInternal:472][][Could not decode request string [] with encoding 'Encoding': falling back to platform default encoding; exception message: Encoding] 
[35m[2025-06-23 14:30:18:008][m[WARN ][main][org.springframework.web.servlet.handler.AbstractHandlerExceptionResolver.logException:198][][Resolved [org.springframework.web.bind.MethodArgumentNotValidException: Validation failed for argument [0] in public java.lang.Boolean com.holderzone.holder.saas.store.deposit.controller.DepositController.createDepositItem(com.holderzone.saas.store.dto.deposit.req.DepositCreateReqDTO): [Field error in object 'depositCreateReqDTO' on field 'goods': rejected value [[]]; codes [NotEmpty.depositCreateReqDTO.goods,NotEmpty.goods,NotEmpty.java.util.List,NotEmpty]; arguments [org.springframework.context.support.DefaultMessageSourceResolvable: codes [depositCreateReqDTO.goods,goods]; arguments []; default message [goods]]; default message [�Ĵ���Ʒ���ϲ���Ϊ��]] ]] 
[35m[2025-06-23 14:30:21:066][m[WARN ][main][com.ctrip.framework.foundation.internals.provider.DefaultApplicationProvider.initAppId:106][][app.id is not available from System Property and /META-INF/app.properties. It is set to null] 
[35m[2025-06-23 14:30:21:274][m[WARN ][main][com.ctrip.framework.apollo.internals.DefaultMetaServerProvider.initMetaServerAddress:39][][Could not find meta server address, because it is not available in neither (1) JVM system property 'apollo.meta', (2) OS env variable 'APOLLO_META' (3) property 'apollo.meta' from server.properties nor (4) property 'apollo.meta' from app.properties] 
[35m[2025-06-23 14:30:21:281][m[WARN ][main][com.ctrip.framework.apollo.core.MetaDomainConsts.initMetaServerAddress:102][][Meta server address fallback to http://apollo.meta for env UNKNOWN, because it is not available in all MetaServerProviders] 
[35m[2025-06-23 14:30:21:283][m[WARN ][main][com.ctrip.framework.apollo.util.ConfigUtil.getAppId:62][][app.id is not set, please make sure it is set in classpath:/META-INF/app.properties, now apollo will only load public namespace configurations!] 
[35m[2025-06-23 14:30:25:672][m[WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-06-23 14:30:27:700][m[WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-06-23 14:30:29:728][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 1 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-06-23 14:30:31:742][m[WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-06-23 14:30:31:744][m[WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\ApolloNoAppIdPlaceHolder\config-cache\ApolloNoAppIdPlaceHolder+default+application.properties]] 
[35m[2025-06-23 14:30:33:765][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 2 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-06-23 14:30:35:782][m[WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-06-23 14:30:35:783][m[WARN ][main][com.ctrip.framework.apollo.internals.DefaultConfig.initialize:61][][Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\ApolloNoAppIdPlaceHolder\config-cache\ApolloNoAppIdPlaceHolder+default+application.properties].] 
[35m[2025-06-23 14:30:35:883][m[WARN ][main][com.ctrip.framework.apollo.internals.DefaultConfig.getProperty:95][][Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!] 
[35m[2025-06-23 14:30:36:527][m[WARN ][main][org.springframework.context.support.AbstractApplicationContext.refresh:557][][Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'hsdDepositMapper' defined in file [E:\store-saas-platform\holder-saas-store-deposit\target\classes\com\holderzone\holder\saas\store\deposit\mapper\HsdDepositMapper.class]: Invocation of init method failed; nested exception is java.lang.IllegalArgumentException: Property 'sqlSessionFactory' or 'sqlSessionTemplate' are required] 
[35m[2025-06-23 14:30:37:690][m[WARN ][main][org.springframework.context.support.AbstractApplicationContext.refresh:557][][Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'hsdDepositMapper' defined in file [E:\store-saas-platform\holder-saas-store-deposit\target\classes\com\holderzone\holder\saas\store\deposit\mapper\HsdDepositMapper.class]: Invocation of init method failed; nested exception is java.lang.IllegalArgumentException: Property 'sqlSessionFactory' or 'sqlSessionTemplate' are required] 
[35m[2025-06-23 14:30:38:686][m[WARN ][main][org.springframework.context.support.AbstractApplicationContext.refresh:557][][Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'hsdDepositMapper' defined in file [E:\store-saas-platform\holder-saas-store-deposit\target\classes\com\holderzone\holder\saas\store\deposit\mapper\HsdDepositMapper.class]: Invocation of init method failed; nested exception is java.lang.IllegalArgumentException: Property 'sqlSessionFactory' or 'sqlSessionTemplate' are required] 
[35m[2025-06-23 14:30:39:650][m[WARN ][main][org.springframework.context.support.AbstractApplicationContext.refresh:557][][Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'hsdDepositMapper' defined in file [E:\store-saas-platform\holder-saas-store-deposit\target\classes\com\holderzone\holder\saas\store\deposit\mapper\HsdDepositMapper.class]: Invocation of init method failed; nested exception is java.lang.IllegalArgumentException: Property 'sqlSessionFactory' or 'sqlSessionTemplate' are required] 
[35m[2025-06-23 14:30:40:649][m[WARN ][main][org.springframework.context.support.AbstractApplicationContext.refresh:557][][Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'hsdDepositMapper' defined in file [E:\store-saas-platform\holder-saas-store-deposit\target\classes\com\holderzone\holder\saas\store\deposit\mapper\HsdDepositMapper.class]: Invocation of init method failed; nested exception is java.lang.IllegalArgumentException: Property 'sqlSessionFactory' or 'sqlSessionTemplate' are required] 
[35m[2025-06-23 14:30:41:623][m[WARN ][main][org.springframework.context.support.AbstractApplicationContext.refresh:557][][Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'hsdDepositMapper' defined in file [E:\store-saas-platform\holder-saas-store-deposit\target\classes\com\holderzone\holder\saas\store\deposit\mapper\HsdDepositMapper.class]: Invocation of init method failed; nested exception is java.lang.IllegalArgumentException: Property 'sqlSessionFactory' or 'sqlSessionTemplate' are required] 
[35m[2025-06-23 14:30:42:547][m[WARN ][main][org.springframework.context.support.AbstractApplicationContext.refresh:557][][Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'hsdDepositMapper' defined in file [E:\store-saas-platform\holder-saas-store-deposit\target\classes\com\holderzone\holder\saas\store\deposit\mapper\HsdDepositMapper.class]: Invocation of init method failed; nested exception is java.lang.IllegalArgumentException: Property 'sqlSessionFactory' or 'sqlSessionTemplate' are required] 
[35m[2025-06-23 14:30:43:368][m[WARN ][main][org.springframework.context.support.AbstractApplicationContext.refresh:557][][Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'hsdDepositMapper' defined in file [E:\store-saas-platform\holder-saas-store-deposit\target\classes\com\holderzone\holder\saas\store\deposit\mapper\HsdDepositMapper.class]: Invocation of init method failed; nested exception is java.lang.IllegalArgumentException: Property 'sqlSessionFactory' or 'sqlSessionTemplate' are required] 
[35m[2025-06-23 14:30:43:831][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 8 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-06-23 14:30:44:163][m[WARN ][main][org.springframework.context.support.AbstractApplicationContext.refresh:557][][Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'hsdDepositMapper' defined in file [E:\store-saas-platform\holder-saas-store-deposit\target\classes\com\holderzone\holder\saas\store\deposit\mapper\HsdDepositMapper.class]: Invocation of init method failed; nested exception is java.lang.IllegalArgumentException: Property 'sqlSessionFactory' or 'sqlSessionTemplate' are required] 
[35m[2025-06-23 14:30:44:922][m[WARN ][main][org.springframework.context.support.AbstractApplicationContext.refresh:557][][Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'hsdDepositMapper' defined in file [E:\store-saas-platform\holder-saas-store-deposit\target\classes\com\holderzone\holder\saas\store\deposit\mapper\HsdDepositMapper.class]: Invocation of init method failed; nested exception is java.lang.IllegalArgumentException: Property 'sqlSessionFactory' or 'sqlSessionTemplate' are required] 
[35m[2025-06-23 14:30:45:649][m[WARN ][main][org.springframework.context.support.AbstractApplicationContext.refresh:557][][Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'hsdDepositMapper' defined in file [E:\store-saas-platform\holder-saas-store-deposit\target\classes\com\holderzone\holder\saas\store\deposit\mapper\HsdDepositMapper.class]: Invocation of init method failed; nested exception is java.lang.IllegalArgumentException: Property 'sqlSessionFactory' or 'sqlSessionTemplate' are required] 
[35m[2025-06-23 14:30:46:406][m[WARN ][main][org.springframework.context.support.AbstractApplicationContext.refresh:557][][Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'hsdDepositMapper' defined in file [E:\store-saas-platform\holder-saas-store-deposit\target\classes\com\holderzone\holder\saas\store\deposit\mapper\HsdDepositMapper.class]: Invocation of init method failed; nested exception is java.lang.IllegalArgumentException: Property 'sqlSessionFactory' or 'sqlSessionTemplate' are required] 
[35m[2025-06-23 14:30:47:123][m[WARN ][main][org.springframework.context.support.AbstractApplicationContext.refresh:557][][Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'hsdDepositMapper' defined in file [E:\store-saas-platform\holder-saas-store-deposit\target\classes\com\holderzone\holder\saas\store\deposit\mapper\HsdDepositMapper.class]: Invocation of init method failed; nested exception is java.lang.IllegalArgumentException: Property 'sqlSessionFactory' or 'sqlSessionTemplate' are required] 
[35m[2025-06-23 14:30:47:826][m[WARN ][main][org.springframework.context.support.AbstractApplicationContext.refresh:557][][Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'hsdDepositMapper' defined in file [E:\store-saas-platform\holder-saas-store-deposit\target\classes\com\holderzone\holder\saas\store\deposit\mapper\HsdDepositMapper.class]: Invocation of init method failed; nested exception is java.lang.IllegalArgumentException: Property 'sqlSessionFactory' or 'sqlSessionTemplate' are required] 
[35m[2025-06-23 14:30:48:780][m[WARN ][main][org.mybatis.logging.Logger.warn:44][][Skipping MapperFactoryBean with name 'hsdDepositMapper' and 'com.holderzone.holder.saas.store.deposit.mapper.HsdDepositMapper' mapperInterface. Bean already defined with the same name!] 
[35m[2025-06-23 14:30:48:780][m[WARN ][main][org.mybatis.logging.Logger.warn:44][][Skipping MapperFactoryBean with name 'hsdGoodsMapper' and 'com.holderzone.holder.saas.store.deposit.mapper.HsdGoodsMapper' mapperInterface. Bean already defined with the same name!] 
[35m[2025-06-23 14:30:48:780][m[WARN ][main][org.mybatis.logging.Logger.warn:44][][Skipping MapperFactoryBean with name 'hsdOperationGoodsMapper' and 'com.holderzone.holder.saas.store.deposit.mapper.HsdOperationGoodsMapper' mapperInterface. Bean already defined with the same name!] 
[35m[2025-06-23 14:30:48:780][m[WARN ][main][org.mybatis.logging.Logger.warn:44][][Skipping MapperFactoryBean with name 'hsdOperationMapper' and 'com.holderzone.holder.saas.store.deposit.mapper.HsdOperationMapper' mapperInterface. Bean already defined with the same name!] 
[35m[2025-06-23 14:30:48:781][m[WARN ][main][org.mybatis.logging.Logger.warn:44][][Skipping MapperFactoryBean with name 'hsdRemindMapper' and 'com.holderzone.holder.saas.store.deposit.mapper.HsdRemindMapper' mapperInterface. Bean already defined with the same name!] 
[35m[2025-06-23 14:30:48:781][m[WARN ][main][org.mybatis.logging.Logger.warn:44][][No MyBatis mapper was found in '[com.holderzone.holder.saas.store.deposit.mapper]' package. Please check your configuration.] 
[35m[2025-06-23 14:30:49:204][m[WARN ][main][org.springframework.boot.actuate.endpoint.EndpointId.logWarning:131][][Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.] 
[35m[2025-06-23 14:30:50:400][m[WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:68][][UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used] 
[35m[2025-06-23 14:30:50:626][m[WARN ][main][com.netflix.config.sources.URLConfigurationSource.<init>:121][][No URLs will be polled as dynamic configuration sources.] 
[35m[2025-06-23 14:30:53:851][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 16 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-06-23 14:30:54:252][m[WARN ][main][com.netflix.config.sources.URLConfigurationSource.<init>:121][][No URLs will be polled as dynamic configuration sources.] 
[35m[2025-06-23 14:30:55:373][m[WARN ][main][org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration.checkTemplateLocationExists:67][][Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)] 
[35m[2025-06-23 14:31:01:855][m[WARN ][main][com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute:130][][Request execution failed with message: org.apache.http.conn.ConnectTimeoutException: Connect to ***************:8141 timed out] 
[35m[2025-06-23 14:31:01:856][m[WARN ][main][com.netflix.discovery.DiscoveryClient$1.get:290][][Using default backup registry implementation which does not do anything.] 
[35m[2025-06-23 14:31:03:267][m[WARN ][main][org.springframework.context.support.AbstractApplicationContext.refresh:557][][Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'hsdDepositServiceImpl': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No bean named 'threadPool' available] 
[35m[2025-06-23 14:31:03:277][m[WARN ][main][org.springframework.context.support.AbstractApplicationContext.refresh:557][][Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'hsdDepositServiceImpl': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No bean named 'threadPool' available] 
[35m[2025-06-23 14:31:03:285][m[WARN ][main][org.springframework.context.support.AbstractApplicationContext.refresh:557][][Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'hsdDepositServiceImpl': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No bean named 'threadPool' available] 
[35m[2025-06-23 14:31:03:294][m[WARN ][main][org.springframework.context.support.AbstractApplicationContext.refresh:557][][Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'hsdDepositServiceImpl': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No bean named 'threadPool' available] 
[35m[2025-06-23 14:31:03:304][m[WARN ][main][org.springframework.context.support.AbstractApplicationContext.refresh:557][][Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'hsdDepositServiceImpl': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No bean named 'threadPool' available] 
[35m[2025-06-23 14:31:03:312][m[WARN ][main][org.springframework.context.support.AbstractApplicationContext.refresh:557][][Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'hsdDepositServiceImpl': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No bean named 'threadPool' available] 
[35m[2025-06-23 14:31:03:322][m[WARN ][main][org.springframework.context.support.AbstractApplicationContext.refresh:557][][Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'hsdDepositServiceImpl': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No bean named 'threadPool' available] 
[35m[2025-06-23 14:31:03:330][m[WARN ][main][org.springframework.context.support.AbstractApplicationContext.refresh:557][][Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'hsdDepositServiceImpl': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No bean named 'threadPool' available] 
[35m[2025-06-23 14:31:03:909][m[WARN ][Thread-50][com.netflix.discovery.DiscoveryClient$3.notify:1297][][Saw local status change event StatusChangeEvent [timestamp=1750660263909, current=DOWN, previous=UP]] 
[35m[2025-06-23 14:31:06:896][m[WARN ][DiscoveryClient-InstanceInfoReplicator-0][com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute:130][][Request execution failed with message: org.apache.http.conn.ConnectTimeoutException: Connect to ***************:8141 timed out] 
[35m[2025-06-23 14:31:06:896][m[WARN ][DiscoveryClient-InstanceInfoReplicator-0][com.netflix.discovery.DiscoveryClient.register:831][][DiscoveryClient_HOLDER-SAAS-STORE-DEPOSIT/**************:8924 - registration failed Cannot execute request on any known server] 
com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.DiscoveryClient.register(DiscoveryClient.java:829) [eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.InstanceInfoReplicator.run(InstanceInfoReplicator.java:121) [eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.InstanceInfoReplicator$1.run(InstanceInfoReplicator.java:101) [eureka-client-1.9.2.jar:1.9.2]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) [?:1.8.0_392]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) [?:1.8.0_392]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180) [?:1.8.0_392]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293) [?:1.8.0_392]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_392]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_392]
	at java.lang.Thread.run(Thread.java:750) [?:1.8.0_392]
[35m[2025-06-23 14:31:06:896][m[WARN ][DiscoveryClient-InstanceInfoReplicator-0][com.netflix.discovery.InstanceInfoReplicator.run:125][][There was a problem with the instance info replicator] 
com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.DiscoveryClient.register(DiscoveryClient.java:829) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.InstanceInfoReplicator.run(InstanceInfoReplicator.java:121) [eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.InstanceInfoReplicator$1.run(InstanceInfoReplicator.java:101) [eureka-client-1.9.2.jar:1.9.2]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) [?:1.8.0_392]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) [?:1.8.0_392]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180) [?:1.8.0_392]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293) [?:1.8.0_392]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_392]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_392]
	at java.lang.Thread.run(Thread.java:750) [?:1.8.0_392]
[35m[2025-06-23 14:31:07:161][m[WARN ][DiscoveryClient-0][com.netflix.discovery.TimedSupervisorTask.run:85][][task supervisor shutting down, can't accept the task] 
[35m[2025-06-23 14:31:11:875][m[WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 32 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[35m[2025-06-23 14:31:11:876][m[WARN ][DiscoveryClient-HeartbeatExecutor-0][com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute:130][][Request execution failed with message: org.apache.http.conn.ConnectTimeoutException: Connect to ***************:8141 timed out] 
[35m[2025-06-23 14:31:11:908][m[WARN ][DiscoveryClient-InstanceInfoReplicator-0][com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute:130][][Request execution failed with message: org.apache.http.conn.ConnectTimeoutException: Connect to ***************:8141 timed out] 
[35m[2025-06-23 14:31:11:908][m[WARN ][DiscoveryClient-InstanceInfoReplicator-0][com.netflix.discovery.DiscoveryClient.register:831][][DiscoveryClient_HOLDER-SAAS-STORE-DEPOSIT/**************:8924 - registration failed Cannot execute request on any known server] 
com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.DiscoveryClient.register(DiscoveryClient.java:829) [eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.InstanceInfoReplicator.run(InstanceInfoReplicator.java:121) [eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.InstanceInfoReplicator$1.run(InstanceInfoReplicator.java:101) [eureka-client-1.9.2.jar:1.9.2]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) [?:1.8.0_392]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) [?:1.8.0_392]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180) [?:1.8.0_392]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293) [?:1.8.0_392]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_392]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_392]
	at java.lang.Thread.run(Thread.java:750) [?:1.8.0_392]
[35m[2025-06-23 14:31:11:908][m[WARN ][DiscoveryClient-InstanceInfoReplicator-0][com.netflix.discovery.InstanceInfoReplicator.run:125][][There was a problem with the instance info replicator] 
com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.DiscoveryClient.register(DiscoveryClient.java:829) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.InstanceInfoReplicator.run(InstanceInfoReplicator.java:121) [eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.InstanceInfoReplicator$1.run(InstanceInfoReplicator.java:101) [eureka-client-1.9.2.jar:1.9.2]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) [?:1.8.0_392]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) [?:1.8.0_392]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180) [?:1.8.0_392]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293) [?:1.8.0_392]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_392]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_392]
	at java.lang.Thread.run(Thread.java:750) [?:1.8.0_392]
[35m[2025-06-23 14:31:12:173][m[WARN ][Thread-50][com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute:130][][Request execution failed with message: org.apache.http.conn.ConnectTimeoutException: Connect to ***************:8141 timed out] 
