[2025-06-23 15:42:40:323][WARN ][main][com.ctrip.framework.foundation.internals.provider.DefaultApplicationProvider.initAppId:106][][app.id is not available from System Property and /META-INF/app.properties. It is set to null] 
[2025-06-23 15:42:40:733][WARN ][main][com.ctrip.framework.apollo.internals.DefaultMetaServerProvider.initMetaServerAddress:39][][Could not find meta server address, because it is not available in neither (1) JVM system property 'apollo.meta', (2) OS env variable 'APOLLO_META' (3) property 'apollo.meta' from server.properties nor (4) property 'apollo.meta' from app.properties] 
[2025-06-23 15:42:40:744][WARN ][main][com.ctrip.framework.apollo.core.MetaDomainConsts.initMetaServerAddress:102][][Meta server address fallback to http://apollo.meta for env UNKNOWN, because it is not available in all MetaServerProviders] 
[2025-06-23 15:42:40:746][WARN ][main][com.ctrip.framework.apollo.util.ConfigUtil.getAppId:62][][app.id is not set, please make sure it is set in classpath:/META-INF/app.properties, now apollo will only load public namespace configurations!] 
[2025-06-23 15:42:44:982][WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 15:42:47:013][WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 15:42:49:036][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 1 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 15:42:51:059][WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 15:42:51:060][WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\ApolloNoAppIdPlaceHolder\config-cache\ApolloNoAppIdPlaceHolder+default+application.properties]] 
[2025-06-23 15:42:53:072][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 2 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 15:42:55:094][WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 15:42:55:095][WARN ][main][com.ctrip.framework.apollo.internals.DefaultConfig.initialize:61][][Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\ApolloNoAppIdPlaceHolder\config-cache\ApolloNoAppIdPlaceHolder+default+application.properties].] 
[2025-06-23 15:42:55:100][WARN ][main][com.ctrip.framework.apollo.internals.DefaultConfig.getProperty:95][][Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!] 
[2025-06-23 15:42:55:101][WARN ][main][org.springframework.context.support.AbstractApplicationContext.refresh:558][][Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanDefinitionStoreException: Invalid bean definition with name 'com.holderzone.holder.saas.aggregation.app.service.feign.MemberMarketingClientService' defined in null: Could not resolve placeholder 'member.marketing.host' in value "http://${member.marketing.host}"; nested exception is java.lang.IllegalArgumentException: Could not resolve placeholder 'member.marketing.host' in value "http://${member.marketing.host}"] 
[2025-06-23 16:31:46:440][WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://test-statefulset-apollo-config-server-0.test-service-apollo-meta-server.test.svc.cluster.local:8080/services/config?appId=holder-saas-aggregation-app&ip=************** [Cause: Could not complete get operation [Cause: test-statefulset-apollo-config-server-0.test-service-apollo-meta-server.test.svc.cluster.local]]] 
[2025-06-23 16:31:48:473][WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://test-statefulset-apollo-config-server-0.test-service-apollo-meta-server.test.svc.cluster.local:8080/services/config?appId=holder-saas-aggregation-app&ip=************** [Cause: Could not complete get operation [Cause: test-statefulset-apollo-config-server-0.test-service-apollo-meta-server.test.svc.cluster.local]]] 
[2025-06-23 16:31:50:491][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 1 seconds. appId: holder-saas-aggregation-app, cluster: default, namespaces: application-test.yml, long polling url: null, reason: Get config services failed from http://test-statefulset-apollo-config-server-0.test-service-apollo-meta-server.test.svc.cluster.local:8080/services/config?appId=holder-saas-aggregation-app&ip=************** [Cause: Could not complete get operation [Cause: test-statefulset-apollo-config-server-0.test-service-apollo-meta-server.test.svc.cluster.local]]] 
[2025-06-23 16:31:52:508][WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://test-statefulset-apollo-config-server-0.test-service-apollo-meta-server.test.svc.cluster.local:8080/services/config?appId=holder-saas-aggregation-app&ip=************** [Cause: Could not complete get operation [Cause: test-statefulset-apollo-config-server-0.test-service-apollo-meta-server.test.svc.cluster.local]]] 
[2025-06-23 16:31:52:509][WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\holder-saas-aggregation-app\config-cache\holder-saas-aggregation-app+default+application-test.yml.properties]] 
[2025-06-23 16:31:54:541][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 2 seconds. appId: holder-saas-aggregation-app, cluster: default, namespaces: application-test.yml, long polling url: null, reason: Get config services failed from http://test-statefulset-apollo-config-server-0.test-service-apollo-meta-server.test.svc.cluster.local:8080/services/config?appId=holder-saas-aggregation-app&ip=************** [Cause: Could not complete get operation [Cause: test-statefulset-apollo-config-server-0.test-service-apollo-meta-server.test.svc.cluster.local]]] 
[2025-06-23 16:31:56:561][WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://test-statefulset-apollo-config-server-0.test-service-apollo-meta-server.test.svc.cluster.local:8080/services/config?appId=holder-saas-aggregation-app&ip=************** [Cause: Could not complete get operation [Cause: test-statefulset-apollo-config-server-0.test-service-apollo-meta-server.test.svc.cluster.local]]] 
[2025-06-23 16:31:56:562][WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigFile.initialize:55][][Init Apollo Config File failed - namespace: application-test.yml, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\holder-saas-aggregation-app\config-cache\holder-saas-aggregation-app+default+application-test.yml.properties].] 
[2025-06-23 16:31:58:582][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 4 seconds. appId: holder-saas-aggregation-app, cluster: default, namespaces: application-test.yml, long polling url: null, reason: Get config services failed from http://test-statefulset-apollo-config-server-0.test-service-apollo-meta-server.test.svc.cluster.local:8080/services/config?appId=holder-saas-aggregation-app&ip=************** [Cause: Could not complete get operation [Cause: test-statefulset-apollo-config-server-0.test-service-apollo-meta-server.test.svc.cluster.local]]] 
[2025-06-23 16:32:02:218][WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://test-statefulset-apollo-config-server-0.test-service-apollo-meta-server.test.svc.cluster.local:8080/services/config?appId=holder-saas-aggregation-app&ip=************** [Cause: Could not complete get operation [Cause: test-statefulset-apollo-config-server-0.test-service-apollo-meta-server.test.svc.cluster.local]]] 
[2025-06-23 16:32:04:240][WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://test-statefulset-apollo-config-server-0.test-service-apollo-meta-server.test.svc.cluster.local:8080/services/config?appId=holder-saas-aggregation-app&ip=************** [Cause: Could not complete get operation [Cause: test-statefulset-apollo-config-server-0.test-service-apollo-meta-server.test.svc.cluster.local]]] 
[2025-06-23 16:32:06:251][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 8 seconds. appId: holder-saas-aggregation-app, cluster: default, namespaces: application+application-test.yml, long polling url: null, reason: Get config services failed from http://test-statefulset-apollo-config-server-0.test-service-apollo-meta-server.test.svc.cluster.local:8080/services/config?appId=holder-saas-aggregation-app&ip=************** [Cause: Could not complete get operation [Cause: test-statefulset-apollo-config-server-0.test-service-apollo-meta-server.test.svc.cluster.local]]] 
[2025-06-23 16:32:08:265][WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://test-statefulset-apollo-config-server-0.test-service-apollo-meta-server.test.svc.cluster.local:8080/services/config?appId=holder-saas-aggregation-app&ip=************** [Cause: Could not complete get operation [Cause: test-statefulset-apollo-config-server-0.test-service-apollo-meta-server.test.svc.cluster.local]]] 
[2025-06-23 16:32:08:267][WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\holder-saas-aggregation-app\config-cache\holder-saas-aggregation-app+default+application.properties]] 
[2025-06-23 16:32:10:289][WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://test-statefulset-apollo-config-server-0.test-service-apollo-meta-server.test.svc.cluster.local:8080/services/config?appId=holder-saas-aggregation-app&ip=************** [Cause: Could not complete get operation [Cause: test-statefulset-apollo-config-server-0.test-service-apollo-meta-server.test.svc.cluster.local]]] 
[2025-06-23 16:32:10:290][WARN ][main][com.ctrip.framework.apollo.internals.DefaultConfig.initialize:61][][Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\holder-saas-aggregation-app\config-cache\holder-saas-aggregation-app+default+application.properties].] 
[2025-06-23 16:32:10:293][WARN ][main][com.ctrip.framework.apollo.internals.DefaultConfig.getProperty:95][][Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!] 
[2025-06-23 16:32:10:294][WARN ][main][org.springframework.context.support.AbstractApplicationContext.refresh:558][][Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanDefinitionStoreException: Invalid bean definition with name 'com.holderzone.holder.saas.aggregation.app.service.feign.MemberMarketingClientService' defined in null: Could not resolve placeholder 'member.marketing.host' in value "http://${member.marketing.host}"; nested exception is java.lang.IllegalArgumentException: Could not resolve placeholder 'member.marketing.host' in value "http://${member.marketing.host}"] 
[2025-06-23 16:40:10:049][WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://apollo.holderzone.cn//services/config?appId=holder-saas-aggregation-app&ip=************** [Cause: Could not complete get operation [Cause: java.lang.IllegalStateException: Expected BEGIN_ARRAY but was STRING at line 1 column 1 path $ [Cause: Expected BEGIN_ARRAY but was STRING at line 1 column 1 path $]]]] 
[2025-06-23 16:40:12:117][WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.holderzone.cn//services/config?appId=holder-saas-aggregation-app&ip=************** [Cause: Could not complete get operation [Cause: java.lang.IllegalStateException: Expected BEGIN_ARRAY but was STRING at line 1 column 1 path $ [Cause: Expected BEGIN_ARRAY but was STRING at line 1 column 1 path $]]]] 
[2025-06-23 16:40:14:184][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 1 seconds. appId: holder-saas-aggregation-app, cluster: default, namespaces: application-test.yml, long polling url: null, reason: Get config services failed from http://apollo.holderzone.cn//services/config?appId=holder-saas-aggregation-app&ip=************** [Cause: Could not complete get operation [Cause: java.lang.IllegalStateException: Expected BEGIN_ARRAY but was STRING at line 1 column 1 path $ [Cause: Expected BEGIN_ARRAY but was STRING at line 1 column 1 path $]]]] 
[2025-06-23 16:40:16:225][WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.holderzone.cn//services/config?appId=holder-saas-aggregation-app&ip=************** [Cause: Could not complete get operation [Cause: java.lang.IllegalStateException: Expected BEGIN_ARRAY but was STRING at line 1 column 1 path $ [Cause: Expected BEGIN_ARRAY but was STRING at line 1 column 1 path $]]]] 
[2025-06-23 16:40:16:225][WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\holder-saas-aggregation-app\config-cache\holder-saas-aggregation-app+default+application-test.yml.properties]] 
[2025-06-23 16:40:18:269][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 2 seconds. appId: holder-saas-aggregation-app, cluster: default, namespaces: application-test.yml, long polling url: null, reason: Get config services failed from http://apollo.holderzone.cn//services/config?appId=holder-saas-aggregation-app&ip=************** [Cause: Could not complete get operation [Cause: java.lang.IllegalStateException: Expected BEGIN_ARRAY but was STRING at line 1 column 1 path $ [Cause: Expected BEGIN_ARRAY but was STRING at line 1 column 1 path $]]]] 
[2025-06-23 16:40:20:319][WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.holderzone.cn//services/config?appId=holder-saas-aggregation-app&ip=************** [Cause: Could not complete get operation [Cause: java.lang.IllegalStateException: Expected BEGIN_ARRAY but was STRING at line 1 column 1 path $ [Cause: Expected BEGIN_ARRAY but was STRING at line 1 column 1 path $]]]] 
[2025-06-23 16:40:20:320][WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigFile.initialize:55][][Init Apollo Config File failed - namespace: application-test.yml, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\holder-saas-aggregation-app\config-cache\holder-saas-aggregation-app+default+application-test.yml.properties].] 
[2025-06-23 16:40:22:363][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 4 seconds. appId: holder-saas-aggregation-app, cluster: default, namespaces: application-test.yml, long polling url: null, reason: Get config services failed from http://apollo.holderzone.cn//services/config?appId=holder-saas-aggregation-app&ip=************** [Cause: Could not complete get operation [Cause: java.lang.IllegalStateException: Expected BEGIN_ARRAY but was STRING at line 1 column 1 path $ [Cause: Expected BEGIN_ARRAY but was STRING at line 1 column 1 path $]]]] 
[2025-06-23 16:40:25:845][WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://apollo.holderzone.cn//services/config?appId=holder-saas-aggregation-app&ip=************** [Cause: Could not complete get operation [Cause: java.lang.IllegalStateException: Expected BEGIN_ARRAY but was STRING at line 1 column 1 path $ [Cause: Expected BEGIN_ARRAY but was STRING at line 1 column 1 path $]]]] 
[2025-06-23 16:40:27:898][WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.holderzone.cn//services/config?appId=holder-saas-aggregation-app&ip=************** [Cause: Could not complete get operation [Cause: java.lang.IllegalStateException: Expected BEGIN_ARRAY but was STRING at line 1 column 1 path $ [Cause: Expected BEGIN_ARRAY but was STRING at line 1 column 1 path $]]]] 
[2025-06-23 16:40:30:121][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 8 seconds. appId: holder-saas-aggregation-app, cluster: default, namespaces: application+application-test.yml, long polling url: null, reason: Get config services failed from http://apollo.holderzone.cn//services/config?appId=holder-saas-aggregation-app&ip=************** [Cause: Could not complete get operation [Cause: java.lang.IllegalStateException: Expected BEGIN_ARRAY but was STRING at line 1 column 1 path $ [Cause: Expected BEGIN_ARRAY but was STRING at line 1 column 1 path $]]]] 
[2025-06-23 16:40:32:171][WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.holderzone.cn//services/config?appId=holder-saas-aggregation-app&ip=************** [Cause: Could not complete get operation [Cause: java.lang.IllegalStateException: Expected BEGIN_ARRAY but was STRING at line 1 column 1 path $ [Cause: Expected BEGIN_ARRAY but was STRING at line 1 column 1 path $]]]] 
[2025-06-23 16:40:32:171][WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\holder-saas-aggregation-app\config-cache\holder-saas-aggregation-app+default+application.properties]] 
[2025-06-23 16:40:34:219][WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.holderzone.cn//services/config?appId=holder-saas-aggregation-app&ip=************** [Cause: Could not complete get operation [Cause: java.lang.IllegalStateException: Expected BEGIN_ARRAY but was STRING at line 1 column 1 path $ [Cause: Expected BEGIN_ARRAY but was STRING at line 1 column 1 path $]]]] 
[2025-06-23 16:40:34:219][WARN ][main][com.ctrip.framework.apollo.internals.DefaultConfig.initialize:61][][Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\holder-saas-aggregation-app\config-cache\holder-saas-aggregation-app+default+application.properties].] 
[2025-06-23 16:40:34:224][WARN ][main][com.ctrip.framework.apollo.internals.DefaultConfig.getProperty:95][][Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!] 
[2025-06-23 16:40:34:225][WARN ][main][org.springframework.context.support.AbstractApplicationContext.refresh:558][][Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanDefinitionStoreException: Invalid bean definition with name 'com.holderzone.holder.saas.aggregation.app.service.feign.MemberMarketingClientService' defined in null: Could not resolve placeholder 'member.marketing.host' in value "http://${member.marketing.host}"; nested exception is java.lang.IllegalArgumentException: Could not resolve placeholder 'member.marketing.host' in value "http://${member.marketing.host}"] 
[2025-06-23 16:44:25:087][WARN ][main][com.ctrip.framework.foundation.internals.provider.DefaultApplicationProvider.initAppId:106][][app.id is not available from System Property and /META-INF/app.properties. It is set to null] 
[2025-06-23 16:44:25:559][WARN ][main][com.ctrip.framework.apollo.internals.DefaultMetaServerProvider.initMetaServerAddress:39][][Could not find meta server address, because it is not available in neither (1) JVM system property 'apollo.meta', (2) OS env variable 'APOLLO_META' (3) property 'apollo.meta' from server.properties nor (4) property 'apollo.meta' from app.properties] 
[2025-06-23 16:44:25:564][WARN ][main][com.ctrip.framework.apollo.core.MetaDomainConsts.initMetaServerAddress:102][][Meta server address fallback to http://apollo.meta for env UNKNOWN, because it is not available in all MetaServerProviders] 
[2025-06-23 16:44:25:566][WARN ][main][com.ctrip.framework.apollo.util.ConfigUtil.getAppId:62][][app.id is not set, please make sure it is set in classpath:/META-INF/app.properties, now apollo will only load public namespace configurations!] 
[2025-06-23 16:44:29:830][WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 16:44:31:848][WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 16:44:33:863][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 1 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 16:44:35:872][WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 16:44:35:874][WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\ApolloNoAppIdPlaceHolder\config-cache\ApolloNoAppIdPlaceHolder+default+application.properties]] 
[2025-06-23 16:44:37:891][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 2 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 16:44:39:910][WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 16:44:39:910][WARN ][main][com.ctrip.framework.apollo.internals.DefaultConfig.initialize:61][][Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\ApolloNoAppIdPlaceHolder\config-cache\ApolloNoAppIdPlaceHolder+default+application.properties].] 
[2025-06-23 16:44:39:984][WARN ][main][com.ctrip.framework.apollo.internals.DefaultConfig.getProperty:95][][Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!] 
[2025-06-23 16:44:41:383][WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:67][][UT026009: XNIO worker was not set on WebSocketDeploymentInfo, the default worker will be used] 
[2025-06-23 16:44:41:384][WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:76][][UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used] 
[2025-06-23 16:44:41:841][WARN ][main][com.netflix.config.sources.URLConfigurationSource.<init>:121][][No URLs will be polled as dynamic configuration sources.] 
[2025-06-23 16:44:41:928][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 4 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 16:44:47:966][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 8 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 16:44:51:770][WARN ][main][org.springframework.context.support.AbstractApplicationContext.refresh:558][][Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'redissonSingleClient' defined in class path resource [com/holderzone/framework/redisson/sdk/starter/RedissonAutoConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'redissonSingleClient' threw exception; nested exception is java.net.UnknownHostException: failed to resolve 'test-holder-saas-redis-mch' after 5 queries ] 
[2025-06-23 16:44:51:798][WARN ][main][org.springframework.beans.factory.support.DisposableBeanAdapter.invokeCustomDestroyMethod:347][][Invocation of destroy method 'close' failed on bean with name 'eurekaRegistration': org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'org.springframework.cloud.netflix.eureka.EurekaClientAutoConfiguration$RefreshableEurekaClientConfiguration': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)] 
[2025-06-23 16:44:57:983][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 16 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 16:51:06:116][WARN ][main][com.ctrip.framework.foundation.internals.provider.DefaultApplicationProvider.initAppId:106][][app.id is not available from System Property and /META-INF/app.properties. It is set to null] 
[2025-06-23 16:51:06:538][WARN ][main][com.ctrip.framework.apollo.internals.DefaultMetaServerProvider.initMetaServerAddress:39][][Could not find meta server address, because it is not available in neither (1) JVM system property 'apollo.meta', (2) OS env variable 'APOLLO_META' (3) property 'apollo.meta' from server.properties nor (4) property 'apollo.meta' from app.properties] 
[2025-06-23 16:51:06:543][WARN ][main][com.ctrip.framework.apollo.core.MetaDomainConsts.initMetaServerAddress:102][][Meta server address fallback to http://apollo.meta for env UNKNOWN, because it is not available in all MetaServerProviders] 
[2025-06-23 16:51:06:544][WARN ][main][com.ctrip.framework.apollo.util.ConfigUtil.getAppId:62][][app.id is not set, please make sure it is set in classpath:/META-INF/app.properties, now apollo will only load public namespace configurations!] 
[2025-06-23 16:51:10:807][WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 16:51:12:830][WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 16:51:14:839][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 1 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 16:51:16:861][WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 16:51:16:861][WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\ApolloNoAppIdPlaceHolder\config-cache\ApolloNoAppIdPlaceHolder+default+application.properties]] 
[2025-06-23 16:51:18:875][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 2 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 16:51:20:897][WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 16:51:20:899][WARN ][main][com.ctrip.framework.apollo.internals.DefaultConfig.initialize:61][][Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\ApolloNoAppIdPlaceHolder\config-cache\ApolloNoAppIdPlaceHolder+default+application.properties].] 
[2025-06-23 16:51:20:968][WARN ][main][com.ctrip.framework.apollo.internals.DefaultConfig.getProperty:95][][Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!] 
[2025-06-23 16:51:22:191][WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:67][][UT026009: XNIO worker was not set on WebSocketDeploymentInfo, the default worker will be used] 
[2025-06-23 16:51:22:192][WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:76][][UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used] 
[2025-06-23 16:51:22:538][WARN ][main][com.netflix.config.sources.URLConfigurationSource.<init>:121][][No URLs will be polled as dynamic configuration sources.] 
[2025-06-23 16:51:22:916][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 4 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 16:51:28:938][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 8 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 16:51:31:838][WARN ][main][org.springframework.context.support.AbstractApplicationContext.refresh:558][][Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'redissonSingleClient' defined in class path resource [com/holderzone/framework/redisson/sdk/starter/RedissonAutoConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'redissonSingleClient' threw exception; nested exception is java.net.UnknownHostException: failed to resolve 'test-holder-saas-redis-mch' after 5 queries ] 
[2025-06-23 16:51:31:878][WARN ][main][org.springframework.beans.factory.support.DisposableBeanAdapter.invokeCustomDestroyMethod:347][][Invocation of destroy method 'close' failed on bean with name 'eurekaRegistration': org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'org.springframework.cloud.netflix.eureka.EurekaClientAutoConfiguration$RefreshableEurekaClientConfiguration': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)] 
[2025-06-23 16:51:38:973][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 16 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 16:51:57:006][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 32 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 16:52:29:010][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.util.ConfigUtil.getAppId:62][][app.id is not set, please make sure it is set in classpath:/META-INF/app.properties, now apollo will only load public namespace configurations!] 
[2025-06-23 16:52:31:038][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 64 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 17:24:18:631][WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:67][][UT026009: XNIO worker was not set on WebSocketDeploymentInfo, the default worker will be used] 
[2025-06-23 17:24:18:632][WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:76][][UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used] 
[2025-06-23 17:24:18:959][WARN ][main][com.netflix.config.sources.URLConfigurationSource.<init>:121][][No URLs will be polled as dynamic configuration sources.] 
[2025-06-23 17:24:27:257][WARN ][main][com.netflix.config.sources.URLConfigurationSource.<init>:121][][No URLs will be polled as dynamic configuration sources.] 
[2025-06-23 17:24:31:538][WARN ][main][com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute:130][][Request execution failed with message: java.net.ConnectException: Connection refused: connect] 
[2025-06-23 17:24:31:543][WARN ][main][com.netflix.discovery.DiscoveryClient$1.get:290][][Using default backup registry implementation which does not do anything.] 
[2025-06-23 17:24:33:640][WARN ][DiscoveryClient-InstanceInfoReplicator-0][com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute:130][][Request execution failed with message: java.net.ConnectException: Connection refused: connect] 
[2025-06-23 17:24:33:640][WARN ][DiscoveryClient-InstanceInfoReplicator-0][com.netflix.discovery.DiscoveryClient.register:831][][DiscoveryClient_HOLDER-SAAS-AGGREGATION-APP/**************:8161 - registration failed Cannot execute request on any known server] 
com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.DiscoveryClient.register(DiscoveryClient.java:829) [eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.InstanceInfoReplicator.run(InstanceInfoReplicator.java:121) [eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.InstanceInfoReplicator$1.run(InstanceInfoReplicator.java:101) [eureka-client-1.9.2.jar:1.9.2]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) [?:1.8.0_392]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) [?:1.8.0_392]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180) [?:1.8.0_392]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293) [?:1.8.0_392]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_392]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_392]
	at java.lang.Thread.run(Thread.java:750) [?:1.8.0_392]
[2025-06-23 17:24:33:641][WARN ][DiscoveryClient-InstanceInfoReplicator-0][com.netflix.discovery.InstanceInfoReplicator.run:125][][There was a problem with the instance info replicator] 
com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.DiscoveryClient.register(DiscoveryClient.java:829) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.InstanceInfoReplicator.run(InstanceInfoReplicator.java:121) [eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.InstanceInfoReplicator$1.run(InstanceInfoReplicator.java:101) [eureka-client-1.9.2.jar:1.9.2]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) [?:1.8.0_392]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) [?:1.8.0_392]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180) [?:1.8.0_392]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293) [?:1.8.0_392]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_392]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_392]
	at java.lang.Thread.run(Thread.java:750) [?:1.8.0_392]
[2025-06-23 17:24:38:608][WARN ][DiscoveryClient-HeartbeatExecutor-0][com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute:130][][Request execution failed with message: java.net.ConnectException: Connection refused: connect] 
[2025-06-23 17:24:45:667][WARN ][DiscoveryClient-HeartbeatExecutor-0][com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute:130][][Request execution failed with message: java.net.ConnectException: Connection refused: connect] 
[2025-06-23 17:24:52:747][WARN ][DiscoveryClient-HeartbeatExecutor-0][com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute:130][][Request execution failed with message: java.net.ConnectException: Connection refused: connect] 
[2025-06-23 17:24:59:830][WARN ][DiscoveryClient-HeartbeatExecutor-0][com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute:130][][Request execution failed with message: java.net.ConnectException: Connection refused: connect] 
[2025-06-23 17:25:03:610][WARN ][DiscoveryClient-CacheRefreshExecutor-0][com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute:130][][Request execution failed with message: java.net.ConnectException: Connection refused: connect] 
[2025-06-23 17:25:05:704][WARN ][DiscoveryClient-InstanceInfoReplicator-0][com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute:130][][Request execution failed with message: java.net.ConnectException: Connection refused: connect] 
[2025-06-23 17:25:05:704][WARN ][DiscoveryClient-InstanceInfoReplicator-0][com.netflix.discovery.DiscoveryClient.register:831][][DiscoveryClient_HOLDER-SAAS-AGGREGATION-APP/**************:8161 - registration failed Cannot execute request on any known server] 
com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.DiscoveryClient.register(DiscoveryClient.java:829) [eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.InstanceInfoReplicator.run(InstanceInfoReplicator.java:121) [eureka-client-1.9.2.jar:1.9.2]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) [?:1.8.0_392]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) [?:1.8.0_392]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180) [?:1.8.0_392]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293) [?:1.8.0_392]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_392]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_392]
	at java.lang.Thread.run(Thread.java:750) [?:1.8.0_392]
[2025-06-23 17:25:05:705][WARN ][DiscoveryClient-InstanceInfoReplicator-0][com.netflix.discovery.InstanceInfoReplicator.run:125][][There was a problem with the instance info replicator] 
com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.DiscoveryClient.register(DiscoveryClient.java:829) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.InstanceInfoReplicator.run(InstanceInfoReplicator.java:121) [eureka-client-1.9.2.jar:1.9.2]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) [?:1.8.0_392]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) [?:1.8.0_392]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180) [?:1.8.0_392]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293) [?:1.8.0_392]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_392]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_392]
	at java.lang.Thread.run(Thread.java:750) [?:1.8.0_392]
[2025-06-23 17:25:06:898][WARN ][DiscoveryClient-HeartbeatExecutor-0][com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute:130][][Request execution failed with message: java.net.ConnectException: Connection refused: connect] 
[2025-06-23 17:25:13:974][WARN ][DiscoveryClient-HeartbeatExecutor-0][com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute:130][][Request execution failed with message: java.net.ConnectException: Connection refused: connect] 
[2025-06-23 17:25:17:858][WARN ][timeoutChecker_1_1][com.netflix.discovery.internal.util.Archaius1Utils.initConfig:35][][Cannot find the properties specified : eureka-client. This may be okay if there are other environment specific properties or the configuration is installed with a different mechanism.] 
[2025-06-23 17:25:17:860][WARN ][timeoutChecker_1_1][com.netflix.discovery.internal.util.Archaius1Utils.initConfig:35][][Cannot find the properties specified : eureka-client. This may be okay if there are other environment specific properties or the configuration is installed with a different mechanism.] 
[2025-06-23 17:25:17:871][WARN ][timeoutChecker_1_1][com.netflix.discovery.internal.util.Archaius1Utils.initConfig:35][][Cannot find the properties specified : eureka-client. This may be okay if there are other environment specific properties or the configuration is installed with a different mechanism.] 
[2025-06-23 17:25:19:987][WARN ][timeoutChecker_1_1][com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute:130][][Request execution failed with message: java.net.ConnectException: Connection refused: connect] 
[2025-06-23 17:25:19:990][WARN ][timeoutChecker_1_1][com.netflix.discovery.DiscoveryClient$1.get:290][][Using default backup registry implementation which does not do anything.] 
[2025-06-23 17:25:21:038][WARN ][DiscoveryClient-HeartbeatExecutor-0][com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute:130][][Request execution failed with message: java.net.ConnectException: Connection refused: connect] 
[2025-06-23 17:25:27:046][WARN ][DiscoveryClient-CacheRefreshExecutor-0][com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute:130][][Request execution failed with message: java.net.ConnectException: Connection refused: connect] 
[2025-06-23 17:25:28:120][WARN ][DiscoveryClient-HeartbeatExecutor-0][com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute:130][][Request execution failed with message: java.net.ConnectException: Connection refused: connect] 
[2025-06-23 17:25:40:141][WARN ][main][com.ctrip.framework.foundation.internals.provider.DefaultApplicationProvider.initAppId:106][][app.id is not available from System Property and /META-INF/app.properties. It is set to null] 
[2025-06-23 17:25:40:523][WARN ][main][com.ctrip.framework.apollo.internals.DefaultMetaServerProvider.initMetaServerAddress:39][][Could not find meta server address, because it is not available in neither (1) JVM system property 'apollo.meta', (2) OS env variable 'APOLLO_META' (3) property 'apollo.meta' from server.properties nor (4) property 'apollo.meta' from app.properties] 
[2025-06-23 17:25:40:527][WARN ][main][com.ctrip.framework.apollo.core.MetaDomainConsts.initMetaServerAddress:102][][Meta server address fallback to http://apollo.meta for env UNKNOWN, because it is not available in all MetaServerProviders] 
[2025-06-23 17:25:40:528][WARN ][main][com.ctrip.framework.apollo.util.ConfigUtil.getAppId:62][][app.id is not set, please make sure it is set in classpath:/META-INF/app.properties, now apollo will only load public namespace configurations!] 
[2025-06-23 17:25:44:772][WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 17:25:46:787][WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 17:25:48:810][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 1 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 17:25:50:823][WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 17:25:50:824][WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\ApolloNoAppIdPlaceHolder\config-cache\ApolloNoAppIdPlaceHolder+default+application.properties]] 
[2025-06-23 17:25:52:842][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 2 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 17:25:54:861][WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 17:25:54:861][WARN ][main][com.ctrip.framework.apollo.internals.DefaultConfig.initialize:61][][Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\ApolloNoAppIdPlaceHolder\config-cache\ApolloNoAppIdPlaceHolder+default+application.properties].] 
[2025-06-23 17:25:54:925][WARN ][main][com.ctrip.framework.apollo.internals.DefaultConfig.getProperty:95][][Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!] 
[2025-06-23 17:25:56:133][WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:67][][UT026009: XNIO worker was not set on WebSocketDeploymentInfo, the default worker will be used] 
[2025-06-23 17:25:56:134][WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:76][][UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used] 
[2025-06-23 17:25:56:434][WARN ][main][com.netflix.config.sources.URLConfigurationSource.<init>:121][][No URLs will be polled as dynamic configuration sources.] 
[2025-06-23 17:25:56:879][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 4 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 17:26:02:908][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 8 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 17:26:06:564][WARN ][main][com.netflix.config.sources.URLConfigurationSource.<init>:121][][No URLs will be polled as dynamic configuration sources.] 
[2025-06-23 17:26:10:815][WARN ][main][com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute:130][][Request execution failed with message: java.net.ConnectException: Connection refused: connect] 
[2025-06-23 17:26:10:821][WARN ][main][com.netflix.discovery.DiscoveryClient$1.get:290][][Using default backup registry implementation which does not do anything.] 
[2025-06-23 17:26:12:905][WARN ][DiscoveryClient-InstanceInfoReplicator-0][com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute:130][][Request execution failed with message: java.net.ConnectException: Connection refused: connect] 
[2025-06-23 17:26:12:905][WARN ][DiscoveryClient-InstanceInfoReplicator-0][com.netflix.discovery.DiscoveryClient.register:831][][DiscoveryClient_HOLDER-SAAS-AGGREGATION-APP/**************:8161 - registration failed Cannot execute request on any known server] 
com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.DiscoveryClient.register(DiscoveryClient.java:829) [eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.InstanceInfoReplicator.run(InstanceInfoReplicator.java:121) [eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.InstanceInfoReplicator$1.run(InstanceInfoReplicator.java:101) [eureka-client-1.9.2.jar:1.9.2]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) [?:1.8.0_392]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) [?:1.8.0_392]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180) [?:1.8.0_392]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293) [?:1.8.0_392]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_392]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_392]
	at java.lang.Thread.run(Thread.java:750) [?:1.8.0_392]
[2025-06-23 17:26:12:906][WARN ][DiscoveryClient-InstanceInfoReplicator-0][com.netflix.discovery.InstanceInfoReplicator.run:125][][There was a problem with the instance info replicator] 
com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.DiscoveryClient.register(DiscoveryClient.java:829) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.InstanceInfoReplicator.run(InstanceInfoReplicator.java:121) [eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.InstanceInfoReplicator$1.run(InstanceInfoReplicator.java:101) [eureka-client-1.9.2.jar:1.9.2]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) [?:1.8.0_392]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) [?:1.8.0_392]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180) [?:1.8.0_392]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293) [?:1.8.0_392]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_392]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_392]
	at java.lang.Thread.run(Thread.java:750) [?:1.8.0_392]
[2025-06-23 17:26:12:932][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 16 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 17:26:17:879][WARN ][DiscoveryClient-HeartbeatExecutor-0][com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute:130][][Request execution failed with message: java.net.ConnectException: Connection refused: connect] 
[2025-06-23 17:26:24:939][WARN ][DiscoveryClient-HeartbeatExecutor-0][com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute:130][][Request execution failed with message: java.net.ConnectException: Connection refused: connect] 
[2025-06-23 17:26:30:946][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 32 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 17:26:31:991][WARN ][DiscoveryClient-HeartbeatExecutor-0][com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute:130][][Request execution failed with message: java.net.ConnectException: Connection refused: connect] 
[2025-06-23 17:26:39:051][WARN ][DiscoveryClient-HeartbeatExecutor-0][com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute:130][][Request execution failed with message: java.net.ConnectException: Connection refused: connect] 
[2025-06-23 17:26:42:901][WARN ][DiscoveryClient-CacheRefreshExecutor-0][com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute:130][][Request execution failed with message: java.net.ConnectException: Connection refused: connect] 
[2025-06-23 17:26:44:987][WARN ][DiscoveryClient-InstanceInfoReplicator-0][com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute:130][][Request execution failed with message: java.net.ConnectException: Connection refused: connect] 
[2025-06-23 17:26:44:987][WARN ][DiscoveryClient-InstanceInfoReplicator-0][com.netflix.discovery.DiscoveryClient.register:831][][DiscoveryClient_HOLDER-SAAS-AGGREGATION-APP/**************:8161 - registration failed Cannot execute request on any known server] 
com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.DiscoveryClient.register(DiscoveryClient.java:829) [eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.InstanceInfoReplicator.run(InstanceInfoReplicator.java:121) [eureka-client-1.9.2.jar:1.9.2]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) [?:1.8.0_392]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) [?:1.8.0_392]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180) [?:1.8.0_392]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293) [?:1.8.0_392]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_392]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_392]
	at java.lang.Thread.run(Thread.java:750) [?:1.8.0_392]
[2025-06-23 17:26:44:989][WARN ][DiscoveryClient-InstanceInfoReplicator-0][com.netflix.discovery.InstanceInfoReplicator.run:125][][There was a problem with the instance info replicator] 
com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.DiscoveryClient.register(DiscoveryClient.java:829) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.InstanceInfoReplicator.run(InstanceInfoReplicator.java:121) [eureka-client-1.9.2.jar:1.9.2]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) [?:1.8.0_392]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) [?:1.8.0_392]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180) [?:1.8.0_392]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293) [?:1.8.0_392]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_392]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_392]
	at java.lang.Thread.run(Thread.java:750) [?:1.8.0_392]
[2025-06-23 17:26:46:130][WARN ][DiscoveryClient-HeartbeatExecutor-0][com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute:130][][Request execution failed with message: java.net.ConnectException: Connection refused: connect] 
[2025-06-23 17:33:26:453][WARN ][main][com.ctrip.framework.foundation.internals.provider.DefaultApplicationProvider.initAppId:106][][app.id is not available from System Property and /META-INF/app.properties. It is set to null] 
[2025-06-23 17:33:26:934][WARN ][main][com.ctrip.framework.apollo.internals.DefaultMetaServerProvider.initMetaServerAddress:39][][Could not find meta server address, because it is not available in neither (1) JVM system property 'apollo.meta', (2) OS env variable 'APOLLO_META' (3) property 'apollo.meta' from server.properties nor (4) property 'apollo.meta' from app.properties] 
[2025-06-23 17:33:26:942][WARN ][main][com.ctrip.framework.apollo.core.MetaDomainConsts.initMetaServerAddress:102][][Meta server address fallback to http://apollo.meta for env UNKNOWN, because it is not available in all MetaServerProviders] 
[2025-06-23 17:33:26:944][WARN ][main][com.ctrip.framework.apollo.util.ConfigUtil.getAppId:62][][app.id is not set, please make sure it is set in classpath:/META-INF/app.properties, now apollo will only load public namespace configurations!] 
[2025-06-23 17:33:31:288][WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 17:33:33:300][WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 17:33:35:322][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 1 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 17:33:37:344][WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 17:33:37:346][WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\ApolloNoAppIdPlaceHolder\config-cache\ApolloNoAppIdPlaceHolder+default+application.properties]] 
[2025-06-23 17:33:39:364][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 2 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 17:33:41:388][WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 17:33:41:389][WARN ][main][com.ctrip.framework.apollo.internals.DefaultConfig.initialize:61][][Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\ApolloNoAppIdPlaceHolder\config-cache\ApolloNoAppIdPlaceHolder+default+application.properties].] 
[2025-06-23 17:33:41:460][WARN ][main][com.ctrip.framework.apollo.internals.DefaultConfig.getProperty:95][][Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!] 
[2025-06-23 17:33:43:107][WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:67][][UT026009: XNIO worker was not set on WebSocketDeploymentInfo, the default worker will be used] 
[2025-06-23 17:33:43:108][WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:76][][UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used] 
[2025-06-23 17:33:43:414][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 4 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 17:33:43:622][WARN ][main][com.netflix.config.sources.URLConfigurationSource.<init>:121][][No URLs will be polled as dynamic configuration sources.] 
[2025-06-23 17:33:49:453][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 8 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 17:33:59:488][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 16 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 17:33:59:760][WARN ][main][com.netflix.config.sources.URLConfigurationSource.<init>:121][][No URLs will be polled as dynamic configuration sources.] 
[2025-06-23 17:34:04:751][WARN ][main][com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute:130][][Request execution failed with message: java.net.ConnectException: Connection refused: connect] 
[2025-06-23 17:34:04:762][WARN ][main][com.netflix.discovery.DiscoveryClient$1.get:290][][Using default backup registry implementation which does not do anything.] 
[2025-06-23 17:34:06:939][WARN ][DiscoveryClient-InstanceInfoReplicator-0][com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute:130][][Request execution failed with message: java.net.ConnectException: Connection refused: connect] 
[2025-06-23 17:34:06:940][WARN ][DiscoveryClient-InstanceInfoReplicator-0][com.netflix.discovery.DiscoveryClient.register:831][][DiscoveryClient_HOLDER-SAAS-AGGREGATION-APP/**************:8161 - registration failed Cannot execute request on any known server] 
com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.DiscoveryClient.register(DiscoveryClient.java:829) [eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.InstanceInfoReplicator.run(InstanceInfoReplicator.java:121) [eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.InstanceInfoReplicator$1.run(InstanceInfoReplicator.java:101) [eureka-client-1.9.2.jar:1.9.2]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) [?:1.8.0_392]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) [?:1.8.0_392]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180) [?:1.8.0_392]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293) [?:1.8.0_392]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_392]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_392]
	at java.lang.Thread.run(Thread.java:750) [?:1.8.0_392]
[2025-06-23 17:34:06:941][WARN ][DiscoveryClient-InstanceInfoReplicator-0][com.netflix.discovery.InstanceInfoReplicator.run:125][][There was a problem with the instance info replicator] 
com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.DiscoveryClient.register(DiscoveryClient.java:829) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.InstanceInfoReplicator.run(InstanceInfoReplicator.java:121) [eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.InstanceInfoReplicator$1.run(InstanceInfoReplicator.java:101) [eureka-client-1.9.2.jar:1.9.2]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) [?:1.8.0_392]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) [?:1.8.0_392]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180) [?:1.8.0_392]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293) [?:1.8.0_392]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_392]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_392]
	at java.lang.Thread.run(Thread.java:750) [?:1.8.0_392]
[2025-06-23 17:34:11:836][WARN ][DiscoveryClient-HeartbeatExecutor-0][com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute:130][][Request execution failed with message: java.net.ConnectException: Connection refused: connect] 
[2025-06-23 17:34:17:550][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 32 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 17:34:18:940][WARN ][DiscoveryClient-HeartbeatExecutor-0][com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute:130][][Request execution failed with message: java.net.ConnectException: Connection refused: connect] 
[2025-06-23 17:34:26:087][WARN ][DiscoveryClient-HeartbeatExecutor-0][com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute:130][][Request execution failed with message: java.net.ConnectException: Connection refused: connect] 
[2025-06-23 17:34:33:225][WARN ][DiscoveryClient-HeartbeatExecutor-0][com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute:130][][Request execution failed with message: java.net.ConnectException: Connection refused: connect] 
[2025-06-23 17:34:36:855][WARN ][DiscoveryClient-CacheRefreshExecutor-0][com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute:130][][Request execution failed with message: java.net.ConnectException: Connection refused: connect] 
[2025-06-23 17:34:39:060][WARN ][DiscoveryClient-InstanceInfoReplicator-0][com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute:130][][Request execution failed with message: java.net.ConnectException: Connection refused: connect] 
[2025-06-23 17:34:39:061][WARN ][DiscoveryClient-InstanceInfoReplicator-0][com.netflix.discovery.DiscoveryClient.register:831][][DiscoveryClient_HOLDER-SAAS-AGGREGATION-APP/**************:8161 - registration failed Cannot execute request on any known server] 
com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.DiscoveryClient.register(DiscoveryClient.java:829) [eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.InstanceInfoReplicator.run(InstanceInfoReplicator.java:121) [eureka-client-1.9.2.jar:1.9.2]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) [?:1.8.0_392]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) [?:1.8.0_392]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180) [?:1.8.0_392]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293) [?:1.8.0_392]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_392]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_392]
	at java.lang.Thread.run(Thread.java:750) [?:1.8.0_392]
[2025-06-23 17:34:39:062][WARN ][DiscoveryClient-InstanceInfoReplicator-0][com.netflix.discovery.InstanceInfoReplicator.run:125][][There was a problem with the instance info replicator] 
com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.DiscoveryClient.register(DiscoveryClient.java:829) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.InstanceInfoReplicator.run(InstanceInfoReplicator.java:121) [eureka-client-1.9.2.jar:1.9.2]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) [?:1.8.0_392]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) [?:1.8.0_392]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180) [?:1.8.0_392]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293) [?:1.8.0_392]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_392]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_392]
	at java.lang.Thread.run(Thread.java:750) [?:1.8.0_392]
[2025-06-23 17:34:40:370][WARN ][DiscoveryClient-HeartbeatExecutor-0][com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute:130][][Request execution failed with message: java.net.ConnectException: Connection refused: connect] 
[2025-06-23 17:34:42:003][WARN ][timeoutChecker_1_1][com.ctrip.framework.apollo.internals.DefaultConfig.getProperty:95][][Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!] 
[2025-06-23 17:34:42:006][WARN ][timeoutChecker_1_1][com.netflix.discovery.internal.util.Archaius1Utils.initConfig:35][][Cannot find the properties specified : eureka-client. This may be okay if there are other environment specific properties or the configuration is installed with a different mechanism.] 
[2025-06-23 17:34:42:009][WARN ][timeoutChecker_1_1][com.netflix.discovery.internal.util.Archaius1Utils.initConfig:35][][Cannot find the properties specified : eureka-client. This may be okay if there are other environment specific properties or the configuration is installed with a different mechanism.] 
[2025-06-23 17:34:42:042][WARN ][timeoutChecker_1_1][com.netflix.discovery.internal.util.Archaius1Utils.initConfig:35][][Cannot find the properties specified : eureka-client. This may be okay if there are other environment specific properties or the configuration is installed with a different mechanism.] 
[2025-06-23 17:34:44:186][WARN ][timeoutChecker_1_1][com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute:130][][Request execution failed with message: java.net.ConnectException: Connection refused: connect] 
[2025-06-23 17:34:44:190][WARN ][timeoutChecker_1_1][com.netflix.discovery.DiscoveryClient$1.get:290][][Using default backup registry implementation which does not do anything.] 
[2025-06-23 17:34:47:496][WARN ][DiscoveryClient-HeartbeatExecutor-0][com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute:130][][Request execution failed with message: java.net.ConnectException: Connection refused: connect] 
[2025-06-23 17:34:49:557][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.util.ConfigUtil.getAppId:62][][app.id is not set, please make sure it is set in classpath:/META-INF/app.properties, now apollo will only load public namespace configurations!] 
[2025-06-23 17:34:51:322][WARN ][DiscoveryClient-CacheRefreshExecutor-0][com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute:130][][Request execution failed with message: java.net.ConnectException: Connection refused: connect] 
[2025-06-23 17:34:51:593][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 64 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 17:34:54:596][WARN ][DiscoveryClient-HeartbeatExecutor-0][com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute:130][][Request execution failed with message: java.net.ConnectException: Connection refused: connect] 
[2025-06-23 17:34:58:436][WARN ][DiscoveryClient-CacheRefreshExecutor-0][com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute:130][][Request execution failed with message: java.net.ConnectException: Connection refused: connect] 
[2025-06-23 17:35:01:706][WARN ][DiscoveryClient-HeartbeatExecutor-0][com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute:130][][Request execution failed with message: java.net.ConnectException: Connection refused: connect] 
[2025-06-23 17:35:05:550][WARN ][DiscoveryClient-CacheRefreshExecutor-0][com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute:130][][Request execution failed with message: java.net.ConnectException: Connection refused: connect] 
[2025-06-23 17:35:08:770][WARN ][DiscoveryClient-HeartbeatExecutor-0][com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute:130][][Request execution failed with message: java.net.ConnectException: Connection refused: connect] 
[2025-06-23 17:35:09:005][WARN ][DiscoveryClient-CacheRefreshExecutor-0][com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute:130][][Request execution failed with message: java.net.ConnectException: Connection refused: connect] 
[2025-06-23 17:35:11:138][WARN ][DiscoveryClient-InstanceInfoReplicator-0][com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute:130][][Request execution failed with message: java.net.ConnectException: Connection refused: connect] 
[2025-06-23 17:35:11:138][WARN ][DiscoveryClient-InstanceInfoReplicator-0][com.netflix.discovery.DiscoveryClient.register:831][][DiscoveryClient_HOLDER-SAAS-AGGREGATION-APP/**************:8161 - registration failed Cannot execute request on any known server] 
com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.DiscoveryClient.register(DiscoveryClient.java:829) [eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.InstanceInfoReplicator.run(InstanceInfoReplicator.java:121) [eureka-client-1.9.2.jar:1.9.2]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) [?:1.8.0_392]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) [?:1.8.0_392]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180) [?:1.8.0_392]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293) [?:1.8.0_392]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_392]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_392]
	at java.lang.Thread.run(Thread.java:750) [?:1.8.0_392]
[2025-06-23 17:35:11:139][WARN ][DiscoveryClient-InstanceInfoReplicator-0][com.netflix.discovery.InstanceInfoReplicator.run:125][][There was a problem with the instance info replicator] 
com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.DiscoveryClient.register(DiscoveryClient.java:829) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.InstanceInfoReplicator.run(InstanceInfoReplicator.java:121) [eureka-client-1.9.2.jar:1.9.2]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) [?:1.8.0_392]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) [?:1.8.0_392]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180) [?:1.8.0_392]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293) [?:1.8.0_392]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_392]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_392]
	at java.lang.Thread.run(Thread.java:750) [?:1.8.0_392]
[2025-06-23 17:35:12:682][WARN ][DiscoveryClient-CacheRefreshExecutor-0][com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute:130][][Request execution failed with message: java.net.ConnectException: Connection refused: connect] 
[2025-06-23 17:35:15:903][WARN ][DiscoveryClient-HeartbeatExecutor-0][com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute:130][][Request execution failed with message: java.net.ConnectException: Connection refused: connect] 
[2025-06-23 17:35:19:802][WARN ][DiscoveryClient-CacheRefreshExecutor-0][com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute:130][][Request execution failed with message: java.net.ConnectException: Connection refused: connect] 
[2025-06-23 17:35:22:991][WARN ][DiscoveryClient-HeartbeatExecutor-0][com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute:130][][Request execution failed with message: java.net.ConnectException: Connection refused: connect] 
[2025-06-23 17:50:38:478][WARN ][main][com.ctrip.framework.foundation.internals.provider.DefaultApplicationProvider.initAppId:106][][app.id is not available from System Property and /META-INF/app.properties. It is set to null] 
[2025-06-23 17:50:39:082][WARN ][main][com.ctrip.framework.apollo.internals.DefaultMetaServerProvider.initMetaServerAddress:39][][Could not find meta server address, because it is not available in neither (1) JVM system property 'apollo.meta', (2) OS env variable 'APOLLO_META' (3) property 'apollo.meta' from server.properties nor (4) property 'apollo.meta' from app.properties] 
[2025-06-23 17:50:39:093][WARN ][main][com.ctrip.framework.apollo.core.MetaDomainConsts.initMetaServerAddress:102][][Meta server address fallback to http://apollo.meta for env UNKNOWN, because it is not available in all MetaServerProviders] 
[2025-06-23 17:50:39:095][WARN ][main][com.ctrip.framework.apollo.util.ConfigUtil.getAppId:62][][app.id is not set, please make sure it is set in classpath:/META-INF/app.properties, now apollo will only load public namespace configurations!] 
[2025-06-23 17:50:43:417][WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 17:50:45:445][WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 17:50:47:495][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 1 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 17:50:49:546][WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 17:50:49:547][WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\ApolloNoAppIdPlaceHolder\config-cache\ApolloNoAppIdPlaceHolder+default+application.properties]] 
[2025-06-23 17:50:51:576][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 2 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 17:50:53:631][WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 17:50:53:632][WARN ][main][com.ctrip.framework.apollo.internals.DefaultConfig.initialize:61][][Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\ApolloNoAppIdPlaceHolder\config-cache\ApolloNoAppIdPlaceHolder+default+application.properties].] 
[2025-06-23 17:50:53:693][WARN ][main][com.ctrip.framework.apollo.internals.DefaultConfig.getProperty:95][][Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!] 
[2025-06-23 17:50:54:422][WARN ][main][org.springframework.context.support.AbstractApplicationContext.refresh:558][][Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'objectMapperConfigurer' defined in class path resource [springfox/documentation/spring/web/SpringfoxWebMvcConfiguration.class]: BeanPostProcessor before instantiation of bean failed; nested exception is java.lang.NoClassDefFoundError: com/holderzone/saas/store/dto/table/exception/TableBusinessLockException] 
[2025-06-23 18:08:13:591][WARN ][main][com.ctrip.framework.foundation.internals.provider.DefaultApplicationProvider.initAppId:106][][app.id is not available from System Property and /META-INF/app.properties. It is set to null] 
[2025-06-23 18:08:13:937][WARN ][main][com.ctrip.framework.apollo.internals.DefaultMetaServerProvider.initMetaServerAddress:39][][Could not find meta server address, because it is not available in neither (1) JVM system property 'apollo.meta', (2) OS env variable 'APOLLO_META' (3) property 'apollo.meta' from server.properties nor (4) property 'apollo.meta' from app.properties] 
[2025-06-23 18:08:13:943][WARN ][main][com.ctrip.framework.apollo.core.MetaDomainConsts.initMetaServerAddress:102][][Meta server address fallback to http://apollo.meta for env UNKNOWN, because it is not available in all MetaServerProviders] 
[2025-06-23 18:08:13:946][WARN ][main][com.ctrip.framework.apollo.util.ConfigUtil.getAppId:62][][app.id is not set, please make sure it is set in classpath:/META-INF/app.properties, now apollo will only load public namespace configurations!] 
[2025-06-23 18:08:18:166][WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 18:08:20:181][WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 18:08:22:207][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 1 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 18:08:24:226][WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 18:08:24:227][WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\ApolloNoAppIdPlaceHolder\config-cache\ApolloNoAppIdPlaceHolder+default+application.properties]] 
[2025-06-23 18:08:26:253][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 2 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 18:08:28:275][WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 18:08:28:277][WARN ][main][com.ctrip.framework.apollo.internals.DefaultConfig.initialize:61][][Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\ApolloNoAppIdPlaceHolder\config-cache\ApolloNoAppIdPlaceHolder+default+application.properties].] 
[2025-06-23 18:08:28:317][WARN ][main][com.ctrip.framework.apollo.internals.DefaultConfig.getProperty:95][][Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!] 
[2025-06-23 18:08:29:273][WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:67][][UT026009: XNIO worker was not set on WebSocketDeploymentInfo, the default worker will be used] 
[2025-06-23 18:08:29:273][WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:76][][UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used] 
[2025-06-23 18:08:29:546][WARN ][main][com.netflix.config.sources.URLConfigurationSource.<init>:121][][No URLs will be polled as dynamic configuration sources.] 
[2025-06-23 18:08:30:302][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 4 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 18:08:36:326][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 8 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 18:08:37:226][WARN ][main][com.netflix.config.sources.URLConfigurationSource.<init>:121][][No URLs will be polled as dynamic configuration sources.] 
[2025-06-23 18:08:46:358][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 16 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 18:09:04:392][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 32 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 18:09:28:662][WARN ][timeoutChecker_1_1][com.ctrip.framework.apollo.internals.DefaultConfig.getProperty:95][][Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!] 
[2025-06-23 18:09:28:663][WARN ][timeoutChecker_1_1][com.netflix.discovery.internal.util.Archaius1Utils.initConfig:35][][Cannot find the properties specified : eureka-client. This may be okay if there are other environment specific properties or the configuration is installed with a different mechanism.] 
[2025-06-23 18:09:28:663][WARN ][timeoutChecker_1_1][com.netflix.discovery.internal.util.Archaius1Utils.initConfig:35][][Cannot find the properties specified : eureka-client. This may be okay if there are other environment specific properties or the configuration is installed with a different mechanism.] 
[2025-06-23 18:09:28:674][WARN ][timeoutChecker_1_1][com.netflix.discovery.internal.util.Archaius1Utils.initConfig:35][][Cannot find the properties specified : eureka-client. This may be okay if there are other environment specific properties or the configuration is installed with a different mechanism.] 
[2025-06-23 18:09:29:052][WARN ][NettyClientSelector_RMROLE_1_1][io.seata.common.loader.EnhancedServiceLoader$InnerEnhancedServiceLoader.loadFile:482][][Load [io.seata.serializer.hessian.HessianSerializer] class fail. com/caucho/hessian/io/AbstractHessianOutput] 
[2025-06-23 18:09:36:397][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.util.ConfigUtil.getAppId:62][][app.id is not set, please make sure it is set in classpath:/META-INF/app.properties, now apollo will only load public namespace configurations!] 
[2025-06-23 18:09:38:415][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 64 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 18:10:42:421][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.util.ConfigUtil.getAppId:62][][app.id is not set, please make sure it is set in classpath:/META-INF/app.properties, now apollo will only load public namespace configurations!] 
[2025-06-23 18:10:44:438][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 120 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 18:11:51:710][WARN ][XNIO-2 task-20][com.holderzone.holder.saas.aggregation.app.controller.table.TableController.queryTable:80][f345001d-0035-452c-a264-4bf2c2254fed][��ҵ������������ҵ�ŵ꣺�ɶ�̫�����콢��������ʱ�䣺1750673511663�յ�����ʱ�䣺1750673511709������̺�ʱ��46] 
[2025-06-23 18:11:51:723][WARN ][XNIO-2 task-20][com.ctrip.framework.apollo.internals.DefaultConfig.getProperty:95][f345001d-0035-452c-a264-4bf2c2254fed][Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!] 
[2025-06-23 18:11:51:860][WARN ][XNIO-2 task-20][com.holderzone.holder.saas.aggregation.app.controller.table.TableController.queryTable:87][f345001d-0035-452c-a264-4bf2c2254fed][StopWatch '': running time (millis) = 145
-----------------------------------------
ms     %     Task name
-----------------------------------------
00141  097%  ��ҵ������������ҵ�ŵ꣺�ɶ�̫�����콢��TableGuidList:[]queryTable��ʱ
00004  003%  ��ҵ������������ҵ�ŵ꣺�ɶ�̫�����콢��TableGuidList:[]batchGetTableInfo��ʱ
] 
[2025-06-23 18:11:51:866][WARN ][XNIO-2 task-20][com.holderzone.holder.saas.aggregation.app.controller.table.TableController.queryTable:88][f345001d-0035-452c-a264-4bf2c2254fed][��ѯ��̨�б�-tableOrderReserveDTOS=[{"storeGuid":"2103241710239920006","storeName":"test�ŵ�1","tableGuid":"6780415479241506816","areaName":"����","areaGuid":"6780415478645915648","tableCode":"A01","seats":4,"status":0,"sort":1,"currentTime":"2025-06-23T18:11:51.154"},{"storeGuid":"2103241710239920006","storeName":"�ɶ�̫�����콢��","tableGuid":"6889823056290643968","areaName":"����1","areaGuid":"6889822970701676544","tableCode":"����01","seats":10,"status":0,"sort":1,"currentTime":"2025-06-23T18:11:51.154","associatedTimes":0},{"storeGuid":"2103241710239920006","storeName":"�ɶ�̫�����콢��","tableGuid":"7072115615875465216","areaName":"vip","areaGuid":"7072115593846980608","tableCode":"001","seats":40,"status":0,"sort":1,"currentTime":"2025-06-23T18:11:51.154"},{"storeGuid":"2103241710239920006","storeName":"test�ŵ�1","tableGuid":"6780415479241506817","areaName":"����","areaGuid":"6780415478645915648","tableCode":"A02","seats":4,"status":0,"sort":2,"currentTime":"2025-06-23T18:11:51.154"},{"storeGuid":"2103241710239920006","storeName":"test�ŵ�1","tableGuid":"6889823056290643969","areaName":"����1","areaGuid":"6889822970701676544","tableCode":"����02","seats":4,"status":0,"sort":2,"currentTime":"2025-06-23T18:11:51.154","associatedTimes":0},{"storeGuid":"2103241710239920006","storeName":"test�ŵ�1","tableGuid":"6780415479241506818","areaName":"����","areaGuid":"6780415478645915648","tableCode":"A03","seats":4,"status":0,"sort":3,"currentTime":"2025-06-23T18:11:51.154"},{"storeGuid":"2103241710239920006","storeName":"test�ŵ�1","tableGuid":"6889823056290643970","areaName":"����1","areaGuid":"6889822970701676544","tableCode":"����03","seats":4,"status":0,"sort":3,"currentTime":"2025-06-23T18:11:51.154","associatedTimes":0},{"storeGuid":"2103241710239920006","storeName":"test�ŵ�1","tableGuid":"6780415479241506819","areaName":"����","areaGuid":"6780415478645915648","tableCode":"A04","seats":4,"status":0,"sort":4,"currentTime":"2025-06-23T18:11:51.154","associatedTimes":0},{"storeGuid":"2103241710239920006","storeName":"test�ŵ�1","tableGuid":"6889823056290643971","areaName":"����1","areaGuid":"6889822970701676544","tableCode":"����04","seats":4,"status":0,"sort":4,"currentTime":"2025-06-23T18:11:51.154","associatedTimes":0},{"storeGuid":"2103241710239920006","storeName":"test�ŵ�1","tableGuid":"6780415479241506820","areaName":"����","areaGuid":"6780415478645915648","tableCode":"A05","seats":4,"status":0,"sort":5,"currentTime":"2025-06-23T18:11:51.154"},{"storeGuid":"2103241710239920006","storeName":"test�ŵ�1","tableGuid":"6889823056290643972","areaName":"����1","areaGuid":"6889822970701676544","tableCode":"����05","seats":4,"status":0,"sort":5,"currentTime":"2025-06-23T18:11:51.154","associatedTimes":0},{"storeGuid":"2103241710239920006","storeName":"test�ŵ�1","tableGuid":"6780415479241506821","areaName":"����","areaGuid":"6780415478645915648","tableCode":"A06","seats":4,"status":0,"sort":6,"subStatus":[],"currentTime":"2025-06-23T18:11:51.154","associatedTimes":0},{"storeGuid":"2103241710239920006","storeName":"test�ŵ�1","tableGuid":"6889823056290643973","areaName":"����1","areaGuid":"6889822970701676544","tableCode":"����06","seats":4,"status":0,"sort":6,"currentTime":"2025-06-23T18:11:51.154","associatedTimes":0},{"storeGuid":"2103241710239920006","storeName":"test�ŵ�1","tableGuid":"6780415479241506822","areaName":"����","areaGuid":"6780415478645915648","tableCode":"A07","seats":4,"status":0,"sort":7,"currentTime":"2025-06-23T18:11:51.154","associatedTimes":0},{"storeGuid":"2103241710239920006","storeName":"test�ŵ�1","tableGuid":"6889823056290643974","areaName":"����1","areaGuid":"6889822970701676544","tableCode":"����07","seats":4,"status":0,"sort":7,"currentTime":"2025-06-23T18:11:51.154","associatedTimes":0},{"storeGuid":"2103241710239920006","storeName":"test�ŵ�1","tableGuid":"6780415479241506823","areaName":"����","areaGuid":"6780415478645915648","tableCode":"A08","seats":4,"status":0,"sort":8,"currentTime":"2025-06-23T18:11:51.154","associatedTimes":0},{"storeGuid":"2103241710239920006","storeName":"test�ŵ�1","tableGuid":"6889823056290643975","areaName":"����1","areaGuid":"6889822970701676544","tableCode":"����08","seats":4,"status":0,"sort":8,"currentTime":"2025-06-23T18:11:51.154","associatedTimes":0},{"storeGuid":"2103241710239920006","storeName":"test�ŵ�1","tableGuid":"6780415479241506824","areaName":"����","areaGuid":"6780415478645915648","tableCode":"A09","seats":4,"status":0,"sort":9,"currentTime":"2025-06-23T18:11:51.154"},{"storeGuid":"2103241710239920006","storeName":"test�ŵ�1","tableGuid":"6889823056290643976","areaName":"����1","areaGuid":"6889822970701676544","tableCode":"����09","seats":4,"status":0,"sort":9,"currentTime":"2025-06-23T18:11:51.154","associatedTimes":0},{"storeGuid":"2103241710239920006","storeName":"test�ŵ�1","tableGuid":"6780415479241506825","areaName":"����","areaGuid":"6780415478645915648","tableCode":"A10","seats":4,"status":0,"sort":10,"currentTime":"2025-06-23T18:11:51.154","associatedTimes":0},{"storeGuid":"2103241710239920006","storeName":"test�ŵ�1","tableGuid":"6889823056290643977","areaName":"����1","areaGuid":"6889822970701676544","tableCode":"����10","seats":4,"status":0,"sort":10,"currentTime":"2025-06-23T18:11:51.154","associatedTimes":0}]] 
[2025-06-23 18:11:52:949][WARN ][pool-3-thread-1][org.apache.kafka.clients.ClientUtils.parseAndValidateAddresses:54][][Removing server test-holder-saas-kafka:9092 from bootstrap.servers as DNS resolution failed for test-holder-saas-kafka] 
[2025-06-23 18:12:22:798][WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:67][][UT026009: XNIO worker was not set on WebSocketDeploymentInfo, the default worker will be used] 
[2025-06-23 18:12:22:798][WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:76][][UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used] 
[2025-06-23 18:12:23:104][WARN ][main][com.netflix.config.sources.URLConfigurationSource.<init>:121][][No URLs will be polled as dynamic configuration sources.] 
[2025-06-23 18:12:32:058][WARN ][main][com.netflix.config.sources.URLConfigurationSource.<init>:121][][No URLs will be polled as dynamic configuration sources.] 
[2025-06-23 18:12:36:425][WARN ][main][com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute:130][][Request execution failed with message: java.net.ConnectException: Connection refused: connect] 
[2025-06-23 18:12:36:431][WARN ][main][com.netflix.discovery.DiscoveryClient$1.get:290][][Using default backup registry implementation which does not do anything.] 
[2025-06-23 18:12:38:516][WARN ][DiscoveryClient-InstanceInfoReplicator-0][com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute:130][][Request execution failed with message: java.net.ConnectException: Connection refused: connect] 
[2025-06-23 18:12:38:516][WARN ][DiscoveryClient-InstanceInfoReplicator-0][com.netflix.discovery.DiscoveryClient.register:831][][DiscoveryClient_HOLDER-SAAS-AGGREGATION-APP/**************:8161 - registration failed Cannot execute request on any known server] 
com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.DiscoveryClient.register(DiscoveryClient.java:829) [eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.InstanceInfoReplicator.run(InstanceInfoReplicator.java:121) [eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.InstanceInfoReplicator$1.run(InstanceInfoReplicator.java:101) [eureka-client-1.9.2.jar:1.9.2]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) [?:1.8.0_392]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) [?:1.8.0_392]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180) [?:1.8.0_392]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293) [?:1.8.0_392]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_392]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_392]
	at java.lang.Thread.run(Thread.java:750) [?:1.8.0_392]
[2025-06-23 18:12:38:517][WARN ][DiscoveryClient-InstanceInfoReplicator-0][com.netflix.discovery.InstanceInfoReplicator.run:125][][There was a problem with the instance info replicator] 
com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.DiscoveryClient.register(DiscoveryClient.java:829) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.InstanceInfoReplicator.run(InstanceInfoReplicator.java:121) [eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.InstanceInfoReplicator$1.run(InstanceInfoReplicator.java:101) [eureka-client-1.9.2.jar:1.9.2]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) [?:1.8.0_392]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) [?:1.8.0_392]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180) [?:1.8.0_392]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293) [?:1.8.0_392]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_392]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_392]
	at java.lang.Thread.run(Thread.java:750) [?:1.8.0_392]
[2025-06-23 18:12:43:494][WARN ][DiscoveryClient-HeartbeatExecutor-0][com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute:130][][Request execution failed with message: java.net.ConnectException: Connection refused: connect] 
[2025-06-23 18:12:50:573][WARN ][DiscoveryClient-HeartbeatExecutor-0][com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute:130][][Request execution failed with message: java.net.ConnectException: Connection refused: connect] 
[2025-06-23 18:12:57:637][WARN ][DiscoveryClient-HeartbeatExecutor-0][com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute:130][][Request execution failed with message: java.net.ConnectException: Connection refused: connect] 
[2025-06-23 18:13:04:693][WARN ][DiscoveryClient-HeartbeatExecutor-0][com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute:130][][Request execution failed with message: java.net.ConnectException: Connection refused: connect] 
[2025-06-23 18:13:08:502][WARN ][DiscoveryClient-CacheRefreshExecutor-0][com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute:130][][Request execution failed with message: java.net.ConnectException: Connection refused: connect] 
[2025-06-23 18:13:10:574][WARN ][DiscoveryClient-InstanceInfoReplicator-0][com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute:130][][Request execution failed with message: java.net.ConnectException: Connection refused: connect] 
[2025-06-23 18:13:10:574][WARN ][DiscoveryClient-InstanceInfoReplicator-0][com.netflix.discovery.DiscoveryClient.register:831][][DiscoveryClient_HOLDER-SAAS-AGGREGATION-APP/**************:8161 - registration failed Cannot execute request on any known server] 
com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.DiscoveryClient.register(DiscoveryClient.java:829) [eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.InstanceInfoReplicator.run(InstanceInfoReplicator.java:121) [eureka-client-1.9.2.jar:1.9.2]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) [?:1.8.0_392]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) [?:1.8.0_392]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180) [?:1.8.0_392]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293) [?:1.8.0_392]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_392]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_392]
	at java.lang.Thread.run(Thread.java:750) [?:1.8.0_392]
[2025-06-23 18:13:10:575][WARN ][DiscoveryClient-InstanceInfoReplicator-0][com.netflix.discovery.InstanceInfoReplicator.run:125][][There was a problem with the instance info replicator] 
com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.DiscoveryClient.register(DiscoveryClient.java:829) ~[eureka-client-1.9.2.jar:1.9.2]
	at com.netflix.discovery.InstanceInfoReplicator.run(InstanceInfoReplicator.java:121) [eureka-client-1.9.2.jar:1.9.2]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) [?:1.8.0_392]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) [?:1.8.0_392]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180) [?:1.8.0_392]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293) [?:1.8.0_392]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_392]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_392]
	at java.lang.Thread.run(Thread.java:750) [?:1.8.0_392]
[2025-06-23 18:13:11:751][WARN ][DiscoveryClient-HeartbeatExecutor-0][com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute:130][][Request execution failed with message: java.net.ConnectException: Connection refused: connect] 
[2025-06-23 18:13:18:822][WARN ][DiscoveryClient-HeartbeatExecutor-0][com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute:130][][Request execution failed with message: java.net.ConnectException: Connection refused: connect] 
[2025-06-23 18:13:22:048][WARN ][timeoutChecker_1_1][com.netflix.discovery.internal.util.Archaius1Utils.initConfig:35][][Cannot find the properties specified : eureka-client. This may be okay if there are other environment specific properties or the configuration is installed with a different mechanism.] 
[2025-06-23 18:13:22:049][WARN ][timeoutChecker_1_1][com.netflix.discovery.internal.util.Archaius1Utils.initConfig:35][][Cannot find the properties specified : eureka-client. This may be okay if there are other environment specific properties or the configuration is installed with a different mechanism.] 
[2025-06-23 18:13:22:058][WARN ][timeoutChecker_1_1][com.netflix.discovery.internal.util.Archaius1Utils.initConfig:35][][Cannot find the properties specified : eureka-client. This may be okay if there are other environment specific properties or the configuration is installed with a different mechanism.] 
[2025-06-23 18:13:29:382][WARN ][main][com.ctrip.framework.foundation.internals.provider.DefaultApplicationProvider.initAppId:106][][app.id is not available from System Property and /META-INF/app.properties. It is set to null] 
[2025-06-23 18:13:29:824][WARN ][main][com.ctrip.framework.apollo.internals.DefaultMetaServerProvider.initMetaServerAddress:39][][Could not find meta server address, because it is not available in neither (1) JVM system property 'apollo.meta', (2) OS env variable 'APOLLO_META' (3) property 'apollo.meta' from server.properties nor (4) property 'apollo.meta' from app.properties] 
[2025-06-23 18:13:29:831][WARN ][main][com.ctrip.framework.apollo.core.MetaDomainConsts.initMetaServerAddress:102][][Meta server address fallback to http://apollo.meta for env UNKNOWN, because it is not available in all MetaServerProviders] 
[2025-06-23 18:13:29:832][WARN ][main][com.ctrip.framework.apollo.util.ConfigUtil.getAppId:62][][app.id is not set, please make sure it is set in classpath:/META-INF/app.properties, now apollo will only load public namespace configurations!] 
[2025-06-23 18:13:34:113][WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 18:13:36:128][WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 18:13:38:150][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 1 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 18:13:40:162][WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 18:13:40:163][WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\ApolloNoAppIdPlaceHolder\config-cache\ApolloNoAppIdPlaceHolder+default+application.properties]] 
[2025-06-23 18:13:47:382][WARN ][main][com.ctrip.framework.foundation.internals.provider.DefaultApplicationProvider.initAppId:106][][app.id is not available from System Property and /META-INF/app.properties. It is set to null] 
[2025-06-23 18:13:47:851][WARN ][main][com.ctrip.framework.apollo.internals.DefaultMetaServerProvider.initMetaServerAddress:39][][Could not find meta server address, because it is not available in neither (1) JVM system property 'apollo.meta', (2) OS env variable 'APOLLO_META' (3) property 'apollo.meta' from server.properties nor (4) property 'apollo.meta' from app.properties] 
[2025-06-23 18:13:47:855][WARN ][main][com.ctrip.framework.apollo.core.MetaDomainConsts.initMetaServerAddress:102][][Meta server address fallback to http://apollo.meta for env UNKNOWN, because it is not available in all MetaServerProviders] 
[2025-06-23 18:13:47:857][WARN ][main][com.ctrip.framework.apollo.util.ConfigUtil.getAppId:62][][app.id is not set, please make sure it is set in classpath:/META-INF/app.properties, now apollo will only load public namespace configurations!] 
[2025-06-23 18:13:52:086][WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 18:13:54:119][WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 18:13:56:137][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 1 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 18:13:58:154][WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 18:13:58:155][WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\ApolloNoAppIdPlaceHolder\config-cache\ApolloNoAppIdPlaceHolder+default+application.properties]] 
[2025-06-23 18:14:00:173][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 2 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 18:14:02:193][WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 18:14:02:196][WARN ][main][com.ctrip.framework.apollo.internals.DefaultConfig.initialize:61][][Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\ApolloNoAppIdPlaceHolder\config-cache\ApolloNoAppIdPlaceHolder+default+application.properties].] 
[2025-06-23 18:14:02:260][WARN ][main][com.ctrip.framework.apollo.internals.DefaultConfig.getProperty:95][][Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!] 
[2025-06-23 18:14:03:532][WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:67][][UT026009: XNIO worker was not set on WebSocketDeploymentInfo, the default worker will be used] 
[2025-06-23 18:14:03:532][WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:76][][UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used] 
[2025-06-23 18:14:03:915][WARN ][main][com.netflix.config.sources.URLConfigurationSource.<init>:121][][No URLs will be polled as dynamic configuration sources.] 
[2025-06-23 18:14:04:216][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 4 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 18:14:10:230][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 8 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 18:14:12:839][WARN ][main][com.netflix.config.sources.URLConfigurationSource.<init>:121][][No URLs will be polled as dynamic configuration sources.] 
[2025-06-23 18:14:20:255][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 16 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 18:14:38:281][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 32 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 18:15:02:710][WARN ][timeoutChecker_1_1][com.ctrip.framework.apollo.internals.DefaultConfig.getProperty:95][][Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!] 
[2025-06-23 18:15:02:712][WARN ][timeoutChecker_1_1][com.netflix.discovery.internal.util.Archaius1Utils.initConfig:35][][Cannot find the properties specified : eureka-client. This may be okay if there are other environment specific properties or the configuration is installed with a different mechanism.] 
[2025-06-23 18:15:02:713][WARN ][timeoutChecker_1_1][com.netflix.discovery.internal.util.Archaius1Utils.initConfig:35][][Cannot find the properties specified : eureka-client. This may be okay if there are other environment specific properties or the configuration is installed with a different mechanism.] 
[2025-06-23 18:15:02:722][WARN ][timeoutChecker_1_1][com.netflix.discovery.internal.util.Archaius1Utils.initConfig:35][][Cannot find the properties specified : eureka-client. This may be okay if there are other environment specific properties or the configuration is installed with a different mechanism.] 
[2025-06-23 18:15:02:853][WARN ][NettyClientSelector_TMROLE_1_1][io.seata.common.loader.EnhancedServiceLoader$InnerEnhancedServiceLoader.loadFile:482][][Load [io.seata.serializer.hessian.HessianSerializer] class fail. com/caucho/hessian/io/AbstractHessianOutput] 
[2025-06-23 18:15:10:282][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.util.ConfigUtil.getAppId:62][][app.id is not set, please make sure it is set in classpath:/META-INF/app.properties, now apollo will only load public namespace configurations!] 
[2025-06-23 18:15:12:302][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 64 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 18:16:02:472][WARN ][XNIO-2 task-9][com.ctrip.framework.apollo.internals.DefaultConfig.getProperty:95][a590b82a-a3ba-434a-aaaf-7cc6be66fcaf][Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!] 
[2025-06-23 18:16:16:317][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.util.ConfigUtil.getAppId:62][][app.id is not set, please make sure it is set in classpath:/META-INF/app.properties, now apollo will only load public namespace configurations!] 
[2025-06-23 18:16:18:334][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 120 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 18:16:51:691][WARN ][XNIO-2 task-15][com.holderzone.holder.saas.aggregation.app.controller.table.TableController.queryTable:80][ce26107d-d94e-49b2-b7db-ac7acf83e037][��ҵ������������ҵ�ŵ꣺�ɶ�̫�����콢��������ʱ�䣺1750673811665�յ�����ʱ�䣺1750673811691������̺�ʱ��26] 
[2025-06-23 18:16:51:828][WARN ][XNIO-2 task-15][com.holderzone.holder.saas.aggregation.app.controller.table.TableController.queryTable:87][ce26107d-d94e-49b2-b7db-ac7acf83e037][StopWatch '': running time (millis) = 130
-----------------------------------------
ms     %     Task name
-----------------------------------------
00123  095%  ��ҵ������������ҵ�ŵ꣺�ɶ�̫�����콢��TableGuidList:[]queryTable��ʱ
00007  005%  ��ҵ������������ҵ�ŵ꣺�ɶ�̫�����콢��TableGuidList:[]batchGetTableInfo��ʱ
] 
[2025-06-23 18:16:51:834][WARN ][XNIO-2 task-15][com.holderzone.holder.saas.aggregation.app.controller.table.TableController.queryTable:88][ce26107d-d94e-49b2-b7db-ac7acf83e037][��ѯ��̨�б�-tableOrderReserveDTOS=[{"storeGuid":"2103241710239920006","storeName":"test�ŵ�1","tableGuid":"6780415479241506816","areaName":"����","areaGuid":"6780415478645915648","tableCode":"A01","seats":4,"status":0,"sort":1,"currentTime":"2025-06-23T18:16:51.116"},{"storeGuid":"2103241710239920006","storeName":"�ɶ�̫�����콢��","tableGuid":"6889823056290643968","areaName":"����1","areaGuid":"6889822970701676544","tableCode":"����01","seats":10,"status":0,"sort":1,"currentTime":"2025-06-23T18:16:51.116","associatedTimes":0},{"storeGuid":"2103241710239920006","storeName":"�ɶ�̫�����콢��","tableGuid":"7072115615875465216","areaName":"vip","areaGuid":"7072115593846980608","tableCode":"001","seats":40,"status":0,"sort":1,"currentTime":"2025-06-23T18:16:51.116"},{"storeGuid":"2103241710239920006","storeName":"test�ŵ�1","tableGuid":"6780415479241506817","areaName":"����","areaGuid":"6780415478645915648","tableCode":"A02","seats":4,"status":0,"sort":2,"currentTime":"2025-06-23T18:16:51.116"},{"storeGuid":"2103241710239920006","storeName":"test�ŵ�1","tableGuid":"6889823056290643969","areaName":"����1","areaGuid":"6889822970701676544","tableCode":"����02","seats":4,"status":0,"sort":2,"currentTime":"2025-06-23T18:16:51.116","associatedTimes":0},{"storeGuid":"2103241710239920006","storeName":"test�ŵ�1","tableGuid":"6780415479241506818","areaName":"����","areaGuid":"6780415478645915648","tableCode":"A03","seats":4,"status":0,"sort":3,"currentTime":"2025-06-23T18:16:51.116"},{"storeGuid":"2103241710239920006","storeName":"test�ŵ�1","tableGuid":"6889823056290643970","areaName":"����1","areaGuid":"6889822970701676544","tableCode":"����03","seats":4,"status":0,"sort":3,"currentTime":"2025-06-23T18:16:51.116","associatedTimes":0},{"storeGuid":"2103241710239920006","storeName":"test�ŵ�1","tableGuid":"6780415479241506819","areaName":"����","areaGuid":"6780415478645915648","tableCode":"A04","seats":4,"status":0,"sort":4,"currentTime":"2025-06-23T18:16:51.116","associatedTimes":0},{"storeGuid":"2103241710239920006","storeName":"test�ŵ�1","tableGuid":"6889823056290643971","areaName":"����1","areaGuid":"6889822970701676544","tableCode":"����04","seats":4,"status":0,"sort":4,"currentTime":"2025-06-23T18:16:51.116","associatedTimes":0},{"storeGuid":"2103241710239920006","storeName":"test�ŵ�1","tableGuid":"6780415479241506820","areaName":"����","areaGuid":"6780415478645915648","tableCode":"A05","seats":4,"status":0,"sort":5,"currentTime":"2025-06-23T18:16:51.116"},{"storeGuid":"2103241710239920006","storeName":"test�ŵ�1","tableGuid":"6889823056290643972","areaName":"����1","areaGuid":"6889822970701676544","tableCode":"����05","seats":4,"status":0,"sort":5,"currentTime":"2025-06-23T18:16:51.116","associatedTimes":0},{"storeGuid":"2103241710239920006","storeName":"test�ŵ�1","tableGuid":"6780415479241506821","areaName":"����","areaGuid":"6780415478645915648","tableCode":"A06","seats":4,"status":0,"sort":6,"subStatus":[],"currentTime":"2025-06-23T18:16:51.116","associatedTimes":0},{"storeGuid":"2103241710239920006","storeName":"test�ŵ�1","tableGuid":"6889823056290643973","areaName":"����1","areaGuid":"6889822970701676544","tableCode":"����06","seats":4,"status":0,"sort":6,"currentTime":"2025-06-23T18:16:51.116","associatedTimes":0},{"storeGuid":"2103241710239920006","storeName":"test�ŵ�1","tableGuid":"6780415479241506822","areaName":"����","areaGuid":"6780415478645915648","tableCode":"A07","seats":4,"status":0,"sort":7,"currentTime":"2025-06-23T18:16:51.116","associatedTimes":0},{"storeGuid":"2103241710239920006","storeName":"test�ŵ�1","tableGuid":"6889823056290643974","areaName":"����1","areaGuid":"6889822970701676544","tableCode":"����07","seats":4,"status":0,"sort":7,"currentTime":"2025-06-23T18:16:51.116","associatedTimes":0},{"storeGuid":"2103241710239920006","storeName":"test�ŵ�1","tableGuid":"6780415479241506823","areaName":"����","areaGuid":"6780415478645915648","tableCode":"A08","seats":4,"status":0,"sort":8,"currentTime":"2025-06-23T18:16:51.116","associatedTimes":0},{"storeGuid":"2103241710239920006","storeName":"test�ŵ�1","tableGuid":"6889823056290643975","areaName":"����1","areaGuid":"6889822970701676544","tableCode":"����08","seats":4,"status":0,"sort":8,"currentTime":"2025-06-23T18:16:51.116","associatedTimes":0},{"storeGuid":"2103241710239920006","storeName":"test�ŵ�1","tableGuid":"6780415479241506824","areaName":"����","areaGuid":"6780415478645915648","tableCode":"A09","seats":4,"status":0,"sort":9,"currentTime":"2025-06-23T18:16:51.116"},{"storeGuid":"2103241710239920006","storeName":"test�ŵ�1","tableGuid":"6889823056290643976","areaName":"����1","areaGuid":"6889822970701676544","tableCode":"����09","seats":4,"status":0,"sort":9,"currentTime":"2025-06-23T18:16:51.116","associatedTimes":0},{"storeGuid":"2103241710239920006","storeName":"test�ŵ�1","tableGuid":"6780415479241506825","areaName":"����","areaGuid":"6780415478645915648","tableCode":"A10","seats":4,"status":0,"sort":10,"currentTime":"2025-06-23T18:16:51.116","associatedTimes":0},{"storeGuid":"2103241710239920006","storeName":"test�ŵ�1","tableGuid":"6889823056290643977","areaName":"����1","areaGuid":"6889822970701676544","tableCode":"����10","seats":4,"status":0,"sort":10,"currentTime":"2025-06-23T18:16:51.116","associatedTimes":0}]] 
[2025-06-23 18:16:52:919][WARN ][pool-3-thread-1][org.apache.kafka.clients.ClientUtils.parseAndValidateAddresses:54][][Removing server test-holder-saas-kafka:9092 from bootstrap.servers as DNS resolution failed for test-holder-saas-kafka] 
[2025-06-23 18:18:18:337][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.util.ConfigUtil.getAppId:62][][app.id is not set, please make sure it is set in classpath:/META-INF/app.properties, now apollo will only load public namespace configurations!] 
[2025-06-23 18:18:20:354][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 120 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 18:18:54:116][WARN ][Apollo-RemoteConfigRepository-1][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 18:18:55:750][WARN ][XNIO-2 task-31][com.holderzone.holder.saas.aggregation.app.controller.table.TableController.queryTable:80][dfac451d-66bb-44c3-84cc-b3a6d9775683][��ҵ��������ҵ�ŵ꣺���Ӵ�������ŵ�������ʱ�䣺1750673931309�յ�����ʱ�䣺1750673935750������̺�ʱ��4441] 
[2025-06-23 18:18:55:916][WARN ][XNIO-2 task-31][com.ctrip.framework.apollo.internals.DefaultConfig.getProperty:95][dfac451d-66bb-44c3-84cc-b3a6d9775683][Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!] 
[2025-06-23 18:18:56:063][WARN ][XNIO-2 task-31][com.holderzone.holder.saas.aggregation.app.controller.table.TableController.queryTable:87][dfac451d-66bb-44c3-84cc-b3a6d9775683][StopWatch '': running time (millis) = 313
-----------------------------------------
ms     %     Task name
-----------------------------------------
00057  018%  ��ҵ��������ҵ�ŵ꣺���Ӵ�������ŵ�TableGuidList:[]queryTable��ʱ
00104  033%  ��ҵ��������ҵ�ŵ꣺���Ӵ�������ŵ�TableGuidList:[]batchGetTableInfo��ʱ
00152  049%  ��ҵ��������ҵ�ŵ꣺���Ӵ�������ŵ�TableGuidList:[]У���Ԥ����غ�ʱ
] 
[2025-06-23 18:18:56:067][WARN ][XNIO-2 task-31][com.holderzone.holder.saas.aggregation.app.controller.table.TableController.queryTable:88][dfac451d-66bb-44c3-84cc-b3a6d9775683][��ѯ��̨�б�-tableOrderReserveDTOS=[{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7204010224225615872","areaName":"�Զ���ר������","areaGuid":"7204010135465754624","tableCode":"C01","seats":4,"status":0,"sort":1,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7130783611803926528","areaName":"֧����ɨ����","areaGuid":"7130774054344065024","tableCode":"001","seats":4,"status":0,"sort":1,"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7182675879175127040","areaName":"�շ������շ������շ������շ������շ������շ������շ������շ������շ���","areaGuid":"6891990703988015104","orderAmount":5.00,"tableCode":"koko����","orderGuid":"842429948902961152","seats":4,"actualGuestsNo":4,"status":1,"sort":1,"subStatus":[10],"openTableTime":"2025-06-13T15:55:51","currentTime":"2025-06-23T18:18:55.083","printPreBillNum":0,"memberGuid":"0"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7182678489412141056","areaName":"����","areaGuid":"7182678425054740480","tableCode":"k1","seats":4,"status":0,"sort":1,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"6891990797743292416","areaName":"�շ������շ������շ������շ������շ������շ������շ������շ������շ���","areaGuid":"6891990703988015104","orderAmount":317.00,"tableCode":"��̨��01","orderGuid":"843822414910648320","seats":6,"actualGuestsNo":6,"status":1,"sort":1,"subStatus":[13],"openTableTime":"2025-06-17T12:09:01","currentTime":"2025-06-23T18:18:55.083","printPreBillNum":0,"memberGuid":"0"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7182678537474670592","areaName":"����","areaGuid":"7182678425054740480","tableCode":"k2","seats":4,"status":0,"sort":1,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7182678572425805824","areaName":"����","areaGuid":"7182678425054740480","tableCode":"k0","seats":10,"status":0,"sort":1,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"6909425176429985792","areaName":"vipר������Ҫ�ã�","areaGuid":"6909425080632082432","tableCode":"vip01","seats":4,"status":0,"sort":1,"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"6912937312783433728","areaName":"���","areaGuid":"6912937203584729088","orderAmount":215.00,"tableCode":"���01","orderGuid":"841341839662575616","seats":5,"actualGuestsNo":5,"status":1,"sort":1,"subStatus":[13],"openTableTime":"2025-06-10T15:52:06","currentTime":"2025-06-23T18:18:55.083","printPreBillNum":0,"memberGuid":"0"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913152","areaName":"����","areaGuid":"7080354482868977664","tableCode":"A01","seats":4,"status":0,"sort":1,"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"6813055631746924544","areaName":"��ʳ����","areaGuid":"6813055631675621376","orderAmount":11.02,"tableCode":"��ֵԽС����Խ��ǰ��ҷ��Ĵ������ŭ����","orderGuid":"7340999763950567440","seats":10,"actualGuestsNo":10,"status":1,"sort":1,"subStatus":[13],"openTableTime":"2025-06-18T15:12:15","currentTime":"2025-06-23T18:18:55.083","printPreBillNum":0,"memberGuid":"0"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"6914820933567905792","areaName":"vipר������Ҫ�ã�","areaGuid":"6909425080632082432","tableCode":"VIP11","seats":4,"status":0,"sort":1,"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"6914821039386001408","areaName":"vipר������Ҫ�ã�","areaGuid":"6909425080632082432","tableCode":"VIP12","seats":4,"status":0,"sort":1,"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"6914822562174205952","areaName":"vipר������Ҫ�ã�","areaGuid":"6909425080632082432","tableCode":"VIP13","seats":4,"status":0,"sort":1,"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"6914822607862759424","areaName":"vipר������Ҫ�ã�","areaGuid":"6909425080632082432","tableCode":"VIP14","seats":4,"status":0,"sort":1,"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7086521648584589312","areaName":"zpp","areaGuid":"7086521536433094656","tableCode":"zp01","seats":2,"status":0,"sort":1,"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7204010224225615873","areaName":"�Զ���ר������","areaGuid":"7204010135465754624","tableCode":"C02","seats":4,"status":0,"sort":2,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"6891990797743292417","areaName":"�շ������շ������շ������շ������շ������շ������շ������շ������շ���","areaGuid":"6891990703988015104","orderAmount":125.00,"tableCode":"��̨��02","orderGuid":"844166918536622081","seats":6,"actualGuestsNo":6,"status":1,"sort":2,"subStatus":[13],"openTableTime":"2025-06-18T10:57:57","currentTime":"2025-06-23T18:18:55.083","printPreBillNum":0,"memberGuid":"0"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"6909425176429985793","areaName":"vipר������Ҫ�ã�","areaGuid":"6909425080632082432","tableCode":"vip02","seats":4,"status":0,"sort":2,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"6912937312783433729","areaName":"���","areaGuid":"6912937203584729088","orderAmount":0.00,"tableCode":"���02","orderGuid":"841328992379400192","seats":5,"actualGuestsNo":5,"status":1,"sort":2,"subStatus":[10],"openTableTime":"2025-06-10T15:01:03","currentTime":"2025-06-23T18:18:55.083","printPreBillNum":0,"memberGuid":"0"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913153","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ02","seats":4,"status":0,"sort":2,"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"6813055631746924545","areaName":"��ʳ����","areaGuid":"6813055631675621376","orderAmount":32.00,"tableCode":"�����ί��ϯϰ��ƽ�μ������ڵ�ʮ�Ľ�ȫ��","orderGuid":"844248569832730625","seats":8,"actualGuestsNo":8,"status":1,"sort":2,"subStatus":[13],"openTableTime":"2025-06-18T16:22:24","currentTime":"2025-06-23T18:18:55.083","printPreBillNum":0,"memberGuid":"0"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7086521648584589313","areaName":"zpp","areaGuid":"7086521536433094656","tableCode":"zp02","seats":2,"status":0,"sort":2,"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7204010224225615874","areaName":"�Զ���ר������","areaGuid":"7204010135465754624","tableCode":"C03","seats":4,"status":0,"sort":3,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"6891990797743292418","areaName":"�շ������շ������շ������շ������շ������շ������շ������շ������շ���","areaGuid":"6891990703988015104","tableCode":"��̨��03","seats":6,"status":0,"sort":3,"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"6909425176429985794","areaName":"vipר������Ҫ�ã�","areaGuid":"6909425080632082432","tableCode":"vip03","seats":4,"status":0,"sort":3,"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"6912937312783433730","areaName":"���","areaGuid":"6912937203584729088","orderAmount":512.00,"tableCode":"���03","orderGuid":"843808611716231169","seats":5,"actualGuestsNo":5,"status":1,"sort":3,"subStatus":[13],"openTableTime":"2025-06-17T11:14:10","currentTime":"2025-06-23T18:18:55.083","printPreBillNum":0,"memberPhone":"15523437317","memberGuid":"7197173816387174400"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913154","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ03","seats":4,"status":0,"sort":3,"subStatus":[],"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"6813055631746924546","areaName":"��ʳ����","areaGuid":"6813055631675621376","orderAmount":0.00,"tableCode":"A03","orderGuid":"844599377656999937","seats":4,"actualGuestsNo":4,"status":1,"sort":3,"subStatus":[10],"openTableTime":"2025-06-19T15:36:24","currentTime":"2025-06-23T18:18:55.083","printPreBillNum":0,"memberGuid":"0"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7086521648584589314","areaName":"zpp","areaGuid":"7086521536433094656","tableCode":"zp03","seats":2,"status":0,"sort":3,"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7204010224225615875","areaName":"�Զ���ר������","areaGuid":"7204010135465754624","tableCode":"C04","seats":4,"status":0,"sort":4,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"6891990797743292419","areaName":"�շ������շ������շ������շ������շ������շ������շ������շ������շ���","areaGuid":"6891990703988015104","tableCode":"��̨��04","seats":6,"status":0,"sort":4,"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"6909425176429985795","areaName":"vipר������Ҫ�ã�","areaGuid":"6909425080632082432","tableCode":"vip04","seats":4,"status":0,"sort":4,"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"6912937312783433731","areaName":"���","areaGuid":"6912937203584729088","tableCode":"���04","seats":5,"status":0,"sort":4,"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"6813055631746924547","areaName":"��ʳ����","areaGuid":"6813055631675621376","orderAmount":0.00,"tableCode":"A04","orderGuid":"844989629609472001","seats":4,"actualGuestsNo":4,"status":1,"sort":4,"subStatus":[10],"openTableTime":"2025-06-20T17:27:07","currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"printPreBillNum":0,"memberGuid":"0"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7086521648584589315","areaName":"zpp","areaGuid":"7086521536433094656","tableCode":"zp04","seats":2,"status":0,"sort":4,"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7204010224225615876","areaName":"�Զ���ר������","areaGuid":"7204010135465754624","tableCode":"C05","seats":4,"status":0,"sort":5,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"6891990797743292420","areaName":"�շ������շ������շ������շ������շ������շ������շ������շ������շ���","areaGuid":"6891990703988015104","tableCode":"��̨��05","seats":6,"status":0,"sort":5,"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"6909425176429985796","areaName":"vipר������Ҫ�ã�","areaGuid":"6909425080632082432","tableCode":"vip05","seats":4,"status":0,"sort":5,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"6912937312783433732","areaName":"���","areaGuid":"6912937203584729088","tableCode":"���05","seats":5,"status":0,"sort":5,"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913156","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ05","seats":4,"status":0,"sort":5,"subStatus":[],"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"6813055631746924548","areaName":"��ʳ����","areaGuid":"6813055631675621376","tableCode":"A05�������ſ��ó���˭֪���ɲ������ֻ�","seats":4,"status":0,"sort":5,"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7086521648584589316","areaName":"zpp","areaGuid":"7086521536433094656","tableCode":"zp05","seats":2,"status":0,"sort":5,"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7204010224225615877","areaName":"�Զ���ר������","areaGuid":"7204010135465754624","tableCode":"C06","seats":4,"status":0,"sort":6,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"6891990797743292421","areaName":"�շ������շ������շ������շ������շ������շ������շ������շ������շ���","areaGuid":"6891990703988015104","tableCode":"��̨��06","seats":6,"status":0,"sort":6,"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"6909425176429985797","areaName":"vipר������Ҫ�ã�","areaGuid":"6909425080632082432","tableCode":"vip06","seats":4,"status":0,"sort":6,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"6912937312783433733","areaName":"���","areaGuid":"6912937203584729088","tableCode":"���06","seats":5,"status":0,"sort":6,"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913157","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ06","seats":4,"status":0,"sort":6,"subStatus":[],"currentTime":"2025-06-23T18:18:55.083","associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"6813055631746924549","areaName":"��ʳ����","areaGuid":"6813055631675621376","tableCode":"A06","seats":4,"status":0,"sort":6,"subStatus":[],"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7204010224225615878","areaName":"�Զ���ר������","areaGuid":"7204010135465754624","tableCode":"C07","seats":4,"status":0,"sort":7,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"6891990797743292422","areaName":"�շ������շ������շ������շ������շ������շ������շ������շ������շ���","areaGuid":"6891990703988015104","tableCode":"��̨��07","seats":6,"status":0,"sort":7,"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"6909425176429985798","areaName":"vipר������Ҫ�ã�","areaGuid":"6909425080632082432","tableCode":"vip07","seats":4,"status":0,"sort":7,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"6914142213769265152","areaName":"���","areaGuid":"6912937203584729088","orderAmount":85.00,"tableCode":"��07","orderGuid":"844504604430495744","seats":2,"actualGuestsNo":2,"status":1,"sort":7,"subStatus":[13],"openTableTime":"2025-06-19T09:19:48","currentTime":"2025-06-23T18:18:55.083","printPreBillNum":0,"memberGuid":"0"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913158","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ07","seats":4,"status":0,"sort":7,"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"6813055631746924550","areaName":"��ʳ����","areaGuid":"6813055631675621376","orderAmount":11.02,"tableCode":"A07","mainOrderGuid":"842428764347625473","orderGuid":"844227458944397313","seats":2,"actualGuestsNo":2,"status":1,"sort":7,"subStatus":[12,13],"openTableTime":"2025-06-18T14:58:31","currentTime":"2025-06-23T18:18:55.083","combineTimes":1,"printPreBillNum":0,"memberGuid":"0"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7204010224225615879","areaName":"�Զ���ר������","areaGuid":"7204010135465754624","tableCode":"C08","seats":4,"status":0,"sort":8,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"6891990797743292423","areaName":"�շ������շ������շ������շ������շ������շ������շ������շ������շ���","areaGuid":"6891990703988015104","tableCode":"��̨��08","seats":6,"status":0,"sort":8,"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"6909425176429985799","areaName":"vipר������Ҫ�ã�","areaGuid":"6909425080632082432","tableCode":"vip08","seats":4,"status":0,"sort":8,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"6914142213769265153","areaName":"���","areaGuid":"6912937203584729088","tableCode":"��08","seats":2,"status":0,"sort":8,"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913159","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ08","seats":4,"status":0,"sort":8,"subStatus":[],"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"6813055631746924551","areaName":"��ʳ����","areaGuid":"6813055631675621376","orderAmount":0.04,"tableCode":"A08","orderGuid":"841332252381212672","seats":2,"actualGuestsNo":2,"status":1,"sort":8,"subStatus":[13],"openTableTime":"2025-06-10T15:14:00","currentTime":"2025-06-23T18:18:55.083","printPreBillNum":0,"memberPhone":"18284563899","memberGuid":"6892042972167143424"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7204010224225615880","areaName":"�Զ���ר������","areaGuid":"7204010135465754624","tableCode":"C09","seats":4,"status":0,"sort":9,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"6891990797743292424","areaName":"�շ������շ������շ������շ������շ������շ������շ������շ������շ���","areaGuid":"6891990703988015104","tableCode":"��̨��09","seats":6,"status":0,"sort":9,"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"6909425176429985800","areaName":"vipר������Ҫ�ã�","areaGuid":"6909425080632082432","tableCode":"vip09","seats":4,"status":0,"sort":9,"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"6914142213769265154","areaName":"���","areaGuid":"6912937203584729088","tableCode":"��09","seats":2,"status":0,"sort":9,"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913160","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ09","seats":4,"status":0,"sort":9,"subStatus":[],"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"6813055631746924552","areaName":"��ʳ����","areaGuid":"6813055631675621376","orderAmount":15.00,"tableCode":"A09","orderGuid":"841964580161908736","seats":4,"actualGuestsNo":4,"status":1,"sort":9,"subStatus":[13],"openTableTime":"2025-06-12T09:06:39","currentTime":"2025-06-23T18:18:55.083","printPreBillNum":0,"memberGuid":"0"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7204010224225615881","areaName":"�Զ���ר������","areaGuid":"7204010135465754624","tableCode":"C10","seats":4,"status":0,"sort":10,"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"6891990797743292425","areaName":"�շ������շ������շ������շ������շ������շ������շ������շ������շ���","areaGuid":"6891990703988015104","tableCode":"��̨��10","seats":6,"status":0,"sort":10,"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"6909425176429985801","areaName":"vipר������Ҫ�ã�","areaGuid":"6909425080632082432","tableCode":"vip10","seats":4,"status":0,"sort":10,"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"6914142213769265155","areaName":"���","areaGuid":"6912937203584729088","tableCode":"��10","seats":2,"status":0,"sort":10,"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913161","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ10","seats":4,"status":0,"sort":10,"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"6813055631746924553","areaName":"��ʳ����","areaGuid":"6813055631675621376","orderAmount":10.00,"tableCode":"A10","mainOrderGuid":"842428764347625473","orderGuid":"842428764347625473","seats":15,"actualGuestsNo":15,"status":1,"sort":10,"subStatus":[12,13],"openTableTime":"2025-06-13T15:51:09","currentTime":"2025-06-23T18:18:55.083","combineTimes":1,"printPreBillNum":0,"memberGuid":"0"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"6914142213769265156","areaName":"���","areaGuid":"6912937203584729088","tableCode":"��11","seats":2,"status":0,"sort":11,"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913162","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ11","seats":4,"status":0,"sort":11,"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"6818138311375192064","areaName":"��ʳ����","areaGuid":"6813055631675621376","orderAmount":10.00,"tableCode":"A11","orderGuid":"842429429203529729","seats":4,"actualGuestsNo":4,"status":1,"sort":11,"subStatus":[13],"openTableTime":"2025-06-13T15:53:48","currentTime":"2025-06-23T18:18:55.083","printPreBillNum":0,"memberGuid":"0"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"6914142213769265157","areaName":"���","areaGuid":"6912937203584729088","tableCode":"��12","seats":2,"status":0,"sort":12,"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913163","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ12","seats":4,"status":0,"sort":12,"subStatus":[],"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"6818138343306428416","areaName":"��ʳ����","areaGuid":"6813055631675621376","tableCode":"A12","seats":4,"status":0,"sort":12,"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"6914142213769265158","areaName":"���","areaGuid":"6912937203584729088","orderAmount":0.00,"tableCode":"��13","orderGuid":"842343572715008001","seats":2,"actualGuestsNo":2,"status":1,"sort":13,"subStatus":[10],"openTableTime":"2025-06-13T10:12:38","currentTime":"2025-06-23T18:18:55.083","printPreBillNum":0,"memberGuid":"0"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913164","areaName":"����","areaGuid":"7080354482868977664","orderAmount":0.00,"tableCode":"ZJ13","orderGuid":"844218876215619585","seats":4,"actualGuestsNo":4,"status":1,"sort":13,"subStatus":[10],"openTableTime":"2025-06-18T14:24:25","currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0,"printPreBillNum":0,"memberGuid":"0"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"6818138364349251584","areaName":"��ʳ����","areaGuid":"6813055631675621376","orderAmount":11.02,"tableCode":"A13","orderGuid":"7340999578914652178","seats":6,"actualGuestsNo":6,"status":1,"sort":13,"subStatus":[13],"openTableTime":"2025-06-18T15:11:31","currentTime":"2025-06-23T18:18:55.083","printPreBillNum":0,"memberGuid":"0"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"6914142213769265159","areaName":"���","areaGuid":"6912937203584729088","tableCode":"��14","seats":2,"status":0,"sort":14,"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913165","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ14","seats":4,"status":0,"sort":14,"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"6818138393914900480","areaName":"��ʳ����","areaGuid":"6813055631675621376","orderAmount":11.00,"tableCode":"A14","orderGuid":"7341001261744914448","seats":4,"actualGuestsNo":4,"status":1,"sort":14,"subStatus":[13],"openTableTime":"2025-06-18T15:18:12","currentTime":"2025-06-23T18:18:55.083","printPreBillNum":0,"memberGuid":"0"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"6914142213769265160","areaName":"���","areaGuid":"6912937203584729088","tableCode":"��15","seats":2,"status":0,"sort":15,"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913166","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ15","seats":4,"status":0,"sort":15,"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"6914142213769265161","areaName":"���","areaGuid":"6912937203584729088","tableCode":"��16","seats":2,"status":0,"sort":16,"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913167","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ16","seats":4,"status":0,"sort":16,"subStatus":[],"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913168","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ17","seats":4,"status":0,"sort":17,"subStatus":[],"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913169","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ18","seats":4,"status":0,"sort":18,"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913170","areaName":"����","areaGuid":"7080354482868977664","orderAmount":0.00,"tableCode":"ZJ19","orderGuid":"844218574750019584","seats":4,"actualGuestsNo":4,"status":1,"sort":19,"subStatus":[10],"openTableTime":"2025-06-18T14:23:13","currentTime":"2025-06-23T18:18:55.083","printPreBillNum":0,"memberGuid":"0"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913171","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ20","seats":4,"status":0,"sort":20,"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913172","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ21","seats":4,"status":0,"sort":21,"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913173","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ22","seats":4,"status":0,"sort":22,"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913174","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ23","seats":4,"status":0,"sort":23,"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913175","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ24","seats":4,"status":0,"sort":24,"subStatus":[],"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913176","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ25","seats":4,"status":0,"sort":25,"subStatus":[],"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913177","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ26","seats":4,"status":0,"sort":26,"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913178","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ27","seats":4,"status":0,"sort":27,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913179","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ28","seats":4,"status":0,"sort":28,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913180","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ29","seats":4,"status":0,"sort":29,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913181","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ30","seats":4,"status":0,"sort":30,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913182","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ31","seats":4,"status":0,"sort":31,"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913183","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ32","seats":4,"status":0,"sort":32,"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913184","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ33","seats":4,"status":0,"sort":33,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913185","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ34","seats":4,"status":0,"sort":34,"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913186","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ35","seats":4,"status":0,"sort":35,"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913187","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ36","seats":4,"status":0,"sort":36,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913188","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ37","seats":4,"status":0,"sort":37,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913189","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ38","seats":4,"status":0,"sort":38,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913190","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ39","seats":4,"status":0,"sort":39,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913191","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ40","seats":4,"status":0,"sort":40,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913192","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ41","seats":4,"status":0,"sort":41,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913193","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ42","seats":4,"status":0,"sort":42,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913194","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ43","seats":4,"status":0,"sort":43,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913195","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ44","seats":4,"status":0,"sort":44,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913196","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ45","seats":4,"status":0,"sort":45,"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913197","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ46","seats":4,"status":0,"sort":46,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913198","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ47","seats":4,"status":0,"sort":47,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913199","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ48","seats":4,"status":0,"sort":48,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913200","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ49","seats":4,"status":0,"sort":49,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913201","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ50","seats":4,"status":0,"sort":50,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913202","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ51","seats":4,"status":0,"sort":51,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913203","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ52","seats":4,"status":0,"sort":52,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913204","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ53","seats":4,"status":0,"sort":53,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913205","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ54","seats":4,"status":0,"sort":54,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913206","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ55","seats":4,"status":0,"sort":55,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913207","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ56","seats":4,"status":0,"sort":56,"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913208","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ57","seats":4,"status":0,"sort":57,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913209","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ58","seats":4,"status":0,"sort":58,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913210","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ59","seats":4,"status":0,"sort":59,"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913211","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ60","seats":4,"status":0,"sort":60,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913212","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ61","seats":4,"status":0,"sort":61,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913213","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ62","seats":4,"status":0,"sort":62,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913214","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ63","seats":4,"status":0,"sort":63,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913215","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ64","seats":4,"status":0,"sort":64,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913216","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ65","seats":4,"status":0,"sort":65,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913217","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ66","seats":4,"status":0,"sort":66,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913218","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ67","seats":4,"status":0,"sort":67,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913219","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ68","seats":4,"status":0,"sort":68,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913220","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ69","seats":4,"status":0,"sort":69,"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913221","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ70","seats":4,"status":0,"sort":70,"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913222","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ71","seats":4,"status":0,"sort":71,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913223","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ72","seats":4,"status":0,"sort":72,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913224","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ73","seats":4,"status":0,"sort":73,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913225","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ74","seats":4,"status":0,"sort":74,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913226","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ75","seats":4,"status":0,"sort":75,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913227","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ76","seats":4,"status":0,"sort":76,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913228","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ77","seats":4,"status":0,"sort":77,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913229","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ78","seats":4,"status":0,"sort":78,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913230","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ79","seats":4,"status":0,"sort":79,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913231","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ80","seats":4,"status":0,"sort":80,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913232","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ81","seats":4,"status":0,"sort":81,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913233","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ82","seats":4,"status":0,"sort":82,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913234","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ83","seats":4,"status":0,"sort":83,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913235","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ84","seats":4,"status":0,"sort":84,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913236","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ85","seats":4,"status":0,"sort":85,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913237","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ86","seats":4,"status":0,"sort":86,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913238","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ87","seats":4,"status":0,"sort":87,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913239","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ88","seats":4,"status":0,"sort":88,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913240","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ89","seats":4,"status":0,"sort":89,"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913241","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ90","seats":4,"status":0,"sort":90,"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913242","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ91","seats":4,"status":0,"sort":91,"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913243","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ92","seats":4,"status":0,"sort":92,"currentTime":"2025-06-23T18:18:55.083","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913244","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ93","seats":4,"status":0,"sort":93,"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913245","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ94","seats":4,"status":0,"sort":94,"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913246","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ95","seats":4,"status":0,"sort":95,"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913247","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ96","seats":4,"status":0,"sort":96,"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913248","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ97","seats":4,"status":0,"sort":97,"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913249","areaName":"����","areaGuid":"7080354482868977664","tableCode":"ZJ98","seats":4,"status":0,"sort":98,"currentTime":"2025-06-23T18:18:55.083"},{"storeGuid":"2106221850429620006","storeName":"���Ӵ�������ŵ�","tableGuid":"7080354536228913250","areaName":"����","areaGuid":"7080354482868977664","orderAmount":15.00,"tableCode":"ZJ99","orderGuid":"845274597921320960","seats":4,"actualGuestsNo":4,"status":1,"sort":99,"subStatus":[13],"openTableTime":"2025-06-21T12:19:29","currentTime":"2025-06-23T18:18:55.083","printPreBillNum":0,"memberGuid":"0"}]] 
[2025-06-23 18:18:57:135][WARN ][pool-3-thread-2][org.apache.kafka.clients.ClientUtils.parseAndValidateAddresses:54][][Removing server test-holder-saas-kafka:9092 from bootstrap.servers as DNS resolution failed for test-holder-saas-kafka] 
[2025-06-23 18:20:20:361][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.util.ConfigUtil.getAppId:62][][app.id is not set, please make sure it is set in classpath:/META-INF/app.properties, now apollo will only load public namespace configurations!] 
[2025-06-23 18:20:22:385][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 120 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 18:22:22:391][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.util.ConfigUtil.getAppId:62][][app.id is not set, please make sure it is set in classpath:/META-INF/app.properties, now apollo will only load public namespace configurations!] 
[2025-06-23 18:22:24:410][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 120 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 18:23:50:077][WARN ][Apollo-ConfigServiceLocator-1][com.ctrip.framework.apollo.util.ConfigUtil.getAppId:62][][app.id is not set, please make sure it is set in classpath:/META-INF/app.properties, now apollo will only load public namespace configurations!] 
[2025-06-23 18:23:54:114][WARN ][Apollo-RemoteConfigRepository-1][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 18:24:26:441][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 120 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-23 18:26:26:451][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.util.ConfigUtil.getAppId:62][][app.id is not set, please make sure it is set in classpath:/META-INF/app.properties, now apollo will only load public namespace configurations!] 
[2025-06-23 18:26:28:477][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 120 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-26 15:46:01:874][WARN ][main][com.ctrip.framework.foundation.internals.provider.DefaultApplicationProvider.initAppId:106][][app.id is not available from System Property and /META-INF/app.properties. It is set to null] 
[2025-06-26 15:46:02:236][WARN ][main][com.ctrip.framework.apollo.internals.DefaultMetaServerProvider.initMetaServerAddress:39][][Could not find meta server address, because it is not available in neither (1) JVM system property 'apollo.meta', (2) OS env variable 'APOLLO_META' (3) property 'apollo.meta' from server.properties nor (4) property 'apollo.meta' from app.properties] 
[2025-06-26 15:46:02:241][WARN ][main][com.ctrip.framework.apollo.core.MetaDomainConsts.initMetaServerAddress:102][][Meta server address fallback to http://apollo.meta for env UNKNOWN, because it is not available in all MetaServerProviders] 
[2025-06-26 15:46:02:242][WARN ][main][com.ctrip.framework.apollo.util.ConfigUtil.getAppId:62][][app.id is not set, please make sure it is set in classpath:/META-INF/app.properties, now apollo will only load public namespace configurations!] 
[2025-06-26 15:46:06:375][WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-26 15:46:08:407][WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-26 15:46:10:433][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 1 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-26 15:46:12:457][WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-26 15:46:12:458][WARN ][main][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26][][Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\ApolloNoAppIdPlaceHolder\config-cache\ApolloNoAppIdPlaceHolder+default+application.properties]] 
[2025-06-26 15:46:14:471][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 2 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-26 15:46:16:486][WARN ][main][com.ctrip.framework.apollo.internals.LocalFileConfigRepository.trySyncFromUpstream:167][][Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-26 15:46:16:486][WARN ][main][com.ctrip.framework.apollo.internals.DefaultConfig.initialize:61][][Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\ApolloNoAppIdPlaceHolder\config-cache\ApolloNoAppIdPlaceHolder+default+application.properties].] 
[2025-06-26 15:46:16:538][WARN ][main][com.ctrip.framework.apollo.internals.DefaultConfig.getProperty:95][][Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!] 
[2025-06-26 15:46:17:891][WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:67][][UT026009: XNIO worker was not set on WebSocketDeploymentInfo, the default worker will be used] 
[2025-06-26 15:46:17:891][WARN ][main][io.undertow.websockets.jsr.Bootstrap.handleDeployment:76][][UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used] 
[2025-06-26 15:46:18:255][WARN ][main][com.netflix.config.sources.URLConfigurationSource.<init>:121][][No URLs will be polled as dynamic configuration sources.] 
[2025-06-26 15:46:18:503][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 4 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-26 15:46:24:532][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 8 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-26 15:46:27:867][WARN ][main][com.netflix.config.sources.URLConfigurationSource.<init>:121][][No URLs will be polled as dynamic configuration sources.] 
[2025-06-26 15:46:34:565][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 16 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-26 15:46:52:609][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 32 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-26 15:47:17:116][WARN ][timeoutChecker_1_1][com.ctrip.framework.apollo.internals.DefaultConfig.getProperty:95][][Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!] 
[2025-06-26 15:47:17:118][WARN ][timeoutChecker_1_1][com.netflix.discovery.internal.util.Archaius1Utils.initConfig:35][][Cannot find the properties specified : eureka-client. This may be okay if there are other environment specific properties or the configuration is installed with a different mechanism.] 
[2025-06-26 15:47:17:118][WARN ][timeoutChecker_1_1][com.netflix.discovery.internal.util.Archaius1Utils.initConfig:35][][Cannot find the properties specified : eureka-client. This may be okay if there are other environment specific properties or the configuration is installed with a different mechanism.] 
[2025-06-26 15:47:17:139][WARN ][timeoutChecker_1_1][com.netflix.discovery.internal.util.Archaius1Utils.initConfig:35][][Cannot find the properties specified : eureka-client. This may be okay if there are other environment specific properties or the configuration is installed with a different mechanism.] 
[2025-06-26 15:47:24:619][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.util.ConfigUtil.getAppId:62][][app.id is not set, please make sure it is set in classpath:/META-INF/app.properties, now apollo will only load public namespace configurations!] 
[2025-06-26 15:47:26:624][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 64 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-26 15:47:34:946][WARN ][XNIO-2 task-6][com.holderzone.holder.saas.aggregation.app.controller.table.TableController.queryTable:80][14e2085d-6b09-4ba3-a228-872840d99787][企业：赵氏企业门店：交子大道测试门店请求发起时间：1750924056223收到请求时间：1750924054945请求过程耗时：-1278] 
[2025-06-26 15:47:35:118][WARN ][XNIO-2 task-6][com.holderzone.holder.saas.aggregation.app.controller.table.TableController.queryTable:87][14e2085d-6b09-4ba3-a228-872840d99787][StopWatch '': running time (millis) = 167
-----------------------------------------
ms     %     Task name
-----------------------------------------
00157  094%  企业：赵氏企业门店：交子大道测试门店TableGuidList:nullqueryTable耗时
00010  006%  企业：赵氏企业门店：交子大道测试门店TableGuidList:nullbatchGetTableInfo耗时
] 
[2025-06-26 15:47:35:124][WARN ][XNIO-2 task-6][com.holderzone.holder.saas.aggregation.app.controller.table.TableController.queryTable:88][14e2085d-6b09-4ba3-a228-872840d99787][查询桌台列表-tableOrderReserveDTOS=[{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7204010224225615872","areaName":"自动化专用区域","areaGuid":"7204010135465754624","tableCode":"C01","seats":4,"status":0,"sort":1,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7130783611803926528","areaName":"支付宝扫码点餐","areaGuid":"7130774054344065024","tableCode":"001","seats":4,"status":0,"sort":1,"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7182675879175127040","areaName":"收费区域收费区域收费区域收费区域收费区域收费区域收费区域收费区域收费区","areaGuid":"6891990703988015104","tableCode":"koko的桌","orderGuid":"842429948902961152","seats":4,"status":1,"sort":1,"subStatus":[10],"openTableTime":"2025-06-13T15:55:51","currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7182678489412141056","areaName":"区域","areaGuid":"7182678425054740480","tableCode":"k1","seats":4,"status":0,"sort":1,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"6891990797743292416","areaName":"收费区域收费区域收费区域收费区域收费区域收费区域收费区域收费区域收费区","areaGuid":"6891990703988015104","tableCode":"桌台费01","orderGuid":"843822414910648320","seats":6,"status":1,"sort":1,"subStatus":[13],"openTableTime":"2025-06-17T12:09:01","currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7182678537474670592","areaName":"区域","areaGuid":"7182678425054740480","tableCode":"k2","seats":4,"status":0,"sort":1,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7182678572425805824","areaName":"区域","areaGuid":"7182678425054740480","tableCode":"k0","seats":10,"status":0,"sort":1,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"6909425176429985792","areaName":"vip专属（不要用）","areaGuid":"6909425080632082432","tableCode":"vip01","seats":4,"status":0,"sort":1,"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"6912937312783433728","areaName":"红包","areaGuid":"6912937203584729088","tableCode":"红包01","orderGuid":"841341839662575616","seats":5,"status":1,"sort":1,"subStatus":[13],"openTableTime":"2025-06-10T15:52:06","currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913152","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"A01","seats":4,"status":0,"sort":1,"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"6813055631746924544","areaName":"堂食大厅","areaGuid":"6813055631675621376","tableCode":"数值越小排序越靠前大家发的大哥呢无怒减肥","orderGuid":"7340999763950567440","seats":10,"status":1,"sort":1,"subStatus":[13],"openTableTime":"2025-06-18T15:12:15","currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"6914820933567905792","areaName":"vip专属（不要用）","areaGuid":"6909425080632082432","tableCode":"VIP11","seats":4,"status":0,"sort":1,"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"6914821039386001408","areaName":"vip专属（不要用）","areaGuid":"6909425080632082432","tableCode":"VIP12","seats":4,"status":0,"sort":1,"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"6914822562174205952","areaName":"vip专属（不要用）","areaGuid":"6909425080632082432","tableCode":"VIP13","seats":4,"status":0,"sort":1,"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"6914822607862759424","areaName":"vip专属（不要用）","areaGuid":"6909425080632082432","tableCode":"VIP14","seats":4,"status":0,"sort":1,"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7086521648584589312","areaName":"zpp","areaGuid":"7086521536433094656","tableCode":"zp01","seats":2,"status":0,"sort":1,"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7204010224225615873","areaName":"自动化专用区域","areaGuid":"7204010135465754624","tableCode":"C02","seats":4,"status":0,"sort":2,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"6891990797743292417","areaName":"收费区域收费区域收费区域收费区域收费区域收费区域收费区域收费区域收费区","areaGuid":"6891990703988015104","tableCode":"桌台费02","orderGuid":"846324900900827137","seats":6,"status":1,"sort":2,"subStatus":[10],"openTableTime":"2025-06-24T09:53:00","currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"6909425176429985793","areaName":"vip专属（不要用）","areaGuid":"6909425080632082432","tableCode":"vip02","seats":4,"status":0,"sort":2,"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"6912937312783433729","areaName":"红包","areaGuid":"6912937203584729088","tableCode":"红包02","orderGuid":"841328992379400192","seats":5,"status":1,"sort":2,"subStatus":[10],"openTableTime":"2025-06-10T15:01:03","currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913153","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ02","seats":4,"status":0,"sort":2,"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"6813055631746924545","areaName":"堂食大厅","areaGuid":"6813055631675621376","tableCode":"中央军委主席习近平参加他所在的十四届全国","orderGuid":"844248569832730625","seats":8,"status":1,"sort":2,"subStatus":[13],"openTableTime":"2025-06-18T16:22:24","currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7086521648584589313","areaName":"zpp","areaGuid":"7086521536433094656","tableCode":"zp02","seats":2,"status":0,"sort":2,"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7204010224225615874","areaName":"自动化专用区域","areaGuid":"7204010135465754624","tableCode":"C03","seats":4,"status":0,"sort":3,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"6891990797743292418","areaName":"收费区域收费区域收费区域收费区域收费区域收费区域收费区域收费区域收费区","areaGuid":"6891990703988015104","tableCode":"桌台费03","orderGuid":"846402082624958464","seats":6,"status":1,"sort":3,"subStatus":[13],"openTableTime":"2025-06-24T14:59:42","currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"6909425176429985794","areaName":"vip专属（不要用）","areaGuid":"6909425080632082432","tableCode":"vip03","seats":4,"status":0,"sort":3,"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"6912937312783433730","areaName":"红包","areaGuid":"6912937203584729088","tableCode":"红包03","orderGuid":"843808611716231169","seats":5,"status":1,"sort":3,"subStatus":[13],"openTableTime":"2025-06-17T11:14:10","currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913154","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ03","seats":4,"status":0,"sort":3,"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"6813055631746924546","areaName":"堂食大厅","areaGuid":"6813055631675621376","tableCode":"A03","orderGuid":"844599377656999937","seats":4,"status":1,"sort":3,"subStatus":[10],"openTableTime":"2025-06-19T15:36:24","currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7086521648584589314","areaName":"zpp","areaGuid":"7086521536433094656","tableCode":"zp03","seats":2,"status":0,"sort":3,"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7204010224225615875","areaName":"自动化专用区域","areaGuid":"7204010135465754624","tableCode":"C04","seats":4,"status":0,"sort":4,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"6891990797743292419","areaName":"收费区域收费区域收费区域收费区域收费区域收费区域收费区域收费区域收费区","areaGuid":"6891990703988015104","tableCode":"桌台费04","seats":6,"status":0,"sort":4,"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"6909425176429985795","areaName":"vip专属（不要用）","areaGuid":"6909425080632082432","tableCode":"vip04","seats":4,"status":0,"sort":4,"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"6912937312783433731","areaName":"红包","areaGuid":"6912937203584729088","tableCode":"红包04","seats":5,"status":0,"sort":4,"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"6813055631746924547","areaName":"堂食大厅","areaGuid":"6813055631675621376","tableCode":"A04","orderGuid":"844989629609472001","seats":4,"status":1,"sort":4,"subStatus":[10],"openTableTime":"2025-06-20T17:27:07","currentTime":"2025-06-26T15:47:35.668","combineTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7086521648584589315","areaName":"zpp","areaGuid":"7086521536433094656","tableCode":"zp04","seats":2,"status":0,"sort":4,"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7204010224225615876","areaName":"自动化专用区域","areaGuid":"7204010135465754624","tableCode":"C05","seats":4,"status":0,"sort":5,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"6891990797743292420","areaName":"收费区域收费区域收费区域收费区域收费区域收费区域收费区域收费区域收费区","areaGuid":"6891990703988015104","tableCode":"桌台费05","orderGuid":"846397607793324033","seats":6,"status":1,"sort":5,"subStatus":[13],"openTableTime":"2025-06-24T14:41:55","currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"6909425176429985796","areaName":"vip专属（不要用）","areaGuid":"6909425080632082432","tableCode":"vip05","seats":4,"status":0,"sort":5,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"6912937312783433732","areaName":"红包","areaGuid":"6912937203584729088","tableCode":"红包05","seats":5,"status":0,"sort":5,"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913156","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ05","seats":4,"status":0,"sort":5,"subStatus":[],"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"6813055631746924548","areaName":"堂食大厅","areaGuid":"6813055631675621376","tableCode":"A05测试桌号可用长度谁知道可不可以轮滑","seats":4,"status":0,"sort":5,"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7086521648584589316","areaName":"zpp","areaGuid":"7086521536433094656","tableCode":"zp05","seats":2,"status":0,"sort":5,"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7204010224225615877","areaName":"自动化专用区域","areaGuid":"7204010135465754624","tableCode":"C06","seats":4,"status":0,"sort":6,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"6891990797743292421","areaName":"收费区域收费区域收费区域收费区域收费区域收费区域收费区域收费区域收费区","areaGuid":"6891990703988015104","tableCode":"桌台费06","orderGuid":"846364197335003136","seats":6,"status":1,"sort":6,"subStatus":[13],"openTableTime":"2025-06-24T12:29:09","currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"6909425176429985797","areaName":"vip专属（不要用）","areaGuid":"6909425080632082432","tableCode":"vip06","seats":4,"status":0,"sort":6,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"6912937312783433733","areaName":"红包","areaGuid":"6912937203584729088","tableCode":"红包06","seats":5,"status":0,"sort":6,"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913157","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ06","seats":4,"status":0,"sort":6,"subStatus":[],"currentTime":"2025-06-26T15:47:35.668","associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"6813055631746924549","areaName":"堂食大厅","areaGuid":"6813055631675621376","tableCode":"A06","seats":4,"status":0,"sort":6,"subStatus":[],"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7204010224225615878","areaName":"自动化专用区域","areaGuid":"7204010135465754624","tableCode":"C07","seats":4,"status":0,"sort":7,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"6891990797743292422","areaName":"收费区域收费区域收费区域收费区域收费区域收费区域收费区域收费区域收费区","areaGuid":"6891990703988015104","tableCode":"桌台费07","seats":6,"status":0,"sort":7,"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"6909425176429985798","areaName":"vip专属（不要用）","areaGuid":"6909425080632082432","tableCode":"vip07","seats":4,"status":0,"sort":7,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"6914142213769265152","areaName":"红包","areaGuid":"6912937203584729088","tableCode":"包07","orderGuid":"844504604430495744","seats":2,"status":1,"sort":7,"subStatus":[13],"openTableTime":"2025-06-19T09:19:48","currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913158","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ07","seats":4,"status":0,"sort":7,"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"6813055631746924550","areaName":"堂食大厅","areaGuid":"6813055631675621376","tableCode":"A07","mainOrderGuid":"842428764347625473","orderGuid":"844227458944397313","seats":2,"status":1,"sort":7,"subStatus":[12,13],"openTableTime":"2025-06-18T14:58:31","currentTime":"2025-06-26T15:47:35.668","combineTimes":1},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7204010224225615879","areaName":"自动化专用区域","areaGuid":"7204010135465754624","tableCode":"C08","seats":4,"status":0,"sort":8,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"6891990797743292423","areaName":"收费区域收费区域收费区域收费区域收费区域收费区域收费区域收费区域收费区","areaGuid":"6891990703988015104","tableCode":"桌台费08","seats":6,"status":0,"sort":8,"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"6909425176429985799","areaName":"vip专属（不要用）","areaGuid":"6909425080632082432","tableCode":"vip08","seats":4,"status":0,"sort":8,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"6914142213769265153","areaName":"红包","areaGuid":"6912937203584729088","tableCode":"包08","seats":2,"status":0,"sort":8,"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913159","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ08","seats":4,"status":0,"sort":8,"subStatus":[],"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"6813055631746924551","areaName":"堂食大厅","areaGuid":"6813055631675621376","tableCode":"A08","orderGuid":"841332252381212672","seats":2,"status":1,"sort":8,"subStatus":[13],"openTableTime":"2025-06-10T15:14:00","currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7204010224225615880","areaName":"自动化专用区域","areaGuid":"7204010135465754624","tableCode":"C09","seats":4,"status":0,"sort":9,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"6891990797743292424","areaName":"收费区域收费区域收费区域收费区域收费区域收费区域收费区域收费区域收费区","areaGuid":"6891990703988015104","tableCode":"桌台费09","seats":6,"status":0,"sort":9,"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"6909425176429985800","areaName":"vip专属（不要用）","areaGuid":"6909425080632082432","tableCode":"vip09","seats":4,"status":0,"sort":9,"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"6914142213769265154","areaName":"红包","areaGuid":"6912937203584729088","tableCode":"包09","seats":2,"status":0,"sort":9,"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913160","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ09","seats":4,"status":0,"sort":9,"subStatus":[],"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"6813055631746924552","areaName":"堂食大厅","areaGuid":"6813055631675621376","tableCode":"A09","orderGuid":"841964580161908736","seats":4,"status":1,"sort":9,"subStatus":[13],"openTableTime":"2025-06-12T09:06:39","currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7204010224225615881","areaName":"自动化专用区域","areaGuid":"7204010135465754624","tableCode":"C10","seats":4,"status":0,"sort":10,"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"6891990797743292425","areaName":"收费区域收费区域收费区域收费区域收费区域收费区域收费区域收费区域收费区","areaGuid":"6891990703988015104","tableCode":"桌台费10","seats":6,"status":0,"sort":10,"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"6909425176429985801","areaName":"vip专属（不要用）","areaGuid":"6909425080632082432","tableCode":"vip10","seats":4,"status":0,"sort":10,"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"6914142213769265155","areaName":"红包","areaGuid":"6912937203584729088","tableCode":"包10","seats":2,"status":0,"sort":10,"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913161","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ10","seats":4,"status":0,"sort":10,"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"6813055631746924553","areaName":"堂食大厅","areaGuid":"6813055631675621376","tableCode":"A10","mainOrderGuid":"842428764347625473","orderGuid":"842428764347625473","seats":15,"status":1,"sort":10,"subStatus":[12,13],"openTableTime":"2025-06-13T15:51:09","currentTime":"2025-06-26T15:47:35.668","combineTimes":1},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"6914142213769265156","areaName":"红包","areaGuid":"6912937203584729088","tableCode":"包11","seats":2,"status":0,"sort":11,"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913162","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ11","seats":4,"status":0,"sort":11,"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"6818138311375192064","areaName":"堂食大厅","areaGuid":"6813055631675621376","tableCode":"A11","orderGuid":"842429429203529729","seats":4,"status":1,"sort":11,"subStatus":[13],"openTableTime":"2025-06-13T15:53:48","currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"6914142213769265157","areaName":"红包","areaGuid":"6912937203584729088","tableCode":"包12","seats":2,"status":0,"sort":12,"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913163","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ12","seats":4,"status":0,"sort":12,"subStatus":[],"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"6818138343306428416","areaName":"堂食大厅","areaGuid":"6813055631675621376","tableCode":"A12","orderGuid":"846339875467882496","seats":4,"status":1,"sort":12,"subStatus":[13],"openTableTime":"2025-06-24T10:52:31","currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"6914142213769265158","areaName":"红包","areaGuid":"6912937203584729088","tableCode":"包13","orderGuid":"842343572715008001","seats":2,"status":1,"sort":13,"subStatus":[10],"openTableTime":"2025-06-13T10:12:38","currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913164","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ13","orderGuid":"844218876215619585","seats":4,"status":1,"sort":13,"subStatus":[10],"openTableTime":"2025-06-18T14:24:25","currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"6818138364349251584","areaName":"堂食大厅","areaGuid":"6813055631675621376","tableCode":"A13","orderGuid":"7340999578914652178","seats":6,"status":1,"sort":13,"subStatus":[13],"openTableTime":"2025-06-18T15:11:31","currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"6914142213769265159","areaName":"红包","areaGuid":"6912937203584729088","tableCode":"包14","seats":2,"status":0,"sort":14,"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913165","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ14","seats":4,"status":0,"sort":14,"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"6818138393914900480","areaName":"堂食大厅","areaGuid":"6813055631675621376","tableCode":"A14","orderGuid":"7341001261744914448","seats":4,"status":1,"sort":14,"subStatus":[13],"openTableTime":"2025-06-18T15:18:12","currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"6914142213769265160","areaName":"红包","areaGuid":"6912937203584729088","tableCode":"包15","seats":2,"status":0,"sort":15,"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913166","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ15","seats":4,"status":0,"sort":15,"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"6914142213769265161","areaName":"红包","areaGuid":"6912937203584729088","tableCode":"包16","seats":2,"status":0,"sort":16,"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913167","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ16","seats":4,"status":0,"sort":16,"subStatus":[],"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913168","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ17","seats":4,"status":0,"sort":17,"subStatus":[],"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913169","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ18","seats":4,"status":0,"sort":18,"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913170","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ19","orderGuid":"844218574750019584","seats":4,"status":1,"sort":19,"subStatus":[10],"openTableTime":"2025-06-18T14:23:13","currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913171","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ20","seats":4,"status":0,"sort":20,"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913172","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ21","seats":4,"status":0,"sort":21,"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913173","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ22","seats":4,"status":0,"sort":22,"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913174","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ23","seats":4,"status":0,"sort":23,"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913175","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ24","seats":4,"status":0,"sort":24,"subStatus":[],"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913176","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ25","seats":4,"status":0,"sort":25,"subStatus":[],"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913177","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ26","seats":4,"status":0,"sort":26,"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913178","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ27","seats":4,"status":0,"sort":27,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913179","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ28","seats":4,"status":0,"sort":28,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913180","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ29","seats":4,"status":0,"sort":29,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913181","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ30","seats":4,"status":0,"sort":30,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913182","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ31","seats":4,"status":0,"sort":31,"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913183","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ32","seats":4,"status":0,"sort":32,"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913184","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ33","seats":4,"status":0,"sort":33,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913185","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ34","seats":4,"status":0,"sort":34,"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913186","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ35","seats":4,"status":0,"sort":35,"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913187","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ36","seats":4,"status":0,"sort":36,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913188","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ37","seats":4,"status":0,"sort":37,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913189","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ38","seats":4,"status":0,"sort":38,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913190","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ39","seats":4,"status":0,"sort":39,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913191","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ40","seats":4,"status":0,"sort":40,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913192","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ41","seats":4,"status":0,"sort":41,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913193","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ42","seats":4,"status":0,"sort":42,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913194","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ43","seats":4,"status":0,"sort":43,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913195","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ44","seats":4,"status":0,"sort":44,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913196","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ45","seats":4,"status":0,"sort":45,"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913197","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ46","seats":4,"status":0,"sort":46,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913198","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ47","seats":4,"status":0,"sort":47,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913199","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ48","seats":4,"status":0,"sort":48,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913200","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ49","seats":4,"status":0,"sort":49,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913201","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ50","seats":4,"status":0,"sort":50,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913202","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ51","seats":4,"status":0,"sort":51,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913203","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ52","seats":4,"status":0,"sort":52,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913204","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ53","seats":4,"status":0,"sort":53,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913205","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ54","seats":4,"status":0,"sort":54,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913206","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ55","seats":4,"status":0,"sort":55,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913207","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ56","seats":4,"status":0,"sort":56,"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913208","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ57","seats":4,"status":0,"sort":57,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913209","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ58","seats":4,"status":0,"sort":58,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913210","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ59","seats":4,"status":0,"sort":59,"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913211","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ60","seats":4,"status":0,"sort":60,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913212","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ61","seats":4,"status":0,"sort":61,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913213","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ62","seats":4,"status":0,"sort":62,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913214","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ63","seats":4,"status":0,"sort":63,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913215","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ64","seats":4,"status":0,"sort":64,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913216","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ65","seats":4,"status":0,"sort":65,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913217","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ66","seats":4,"status":0,"sort":66,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913218","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ67","seats":4,"status":0,"sort":67,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913219","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ68","seats":4,"status":0,"sort":68,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913220","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ69","seats":4,"status":0,"sort":69,"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913221","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ70","seats":4,"status":0,"sort":70,"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913222","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ71","seats":4,"status":0,"sort":71,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913223","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ72","seats":4,"status":0,"sort":72,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913224","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ73","seats":4,"status":0,"sort":73,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913225","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ74","seats":4,"status":0,"sort":74,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913226","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ75","seats":4,"status":0,"sort":75,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913227","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ76","seats":4,"status":0,"sort":76,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913228","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ77","seats":4,"status":0,"sort":77,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913229","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ78","seats":4,"status":0,"sort":78,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913230","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ79","seats":4,"status":0,"sort":79,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913231","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ80","seats":4,"status":0,"sort":80,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913232","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ81","seats":4,"status":0,"sort":81,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913233","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ82","seats":4,"status":0,"sort":82,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913234","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ83","seats":4,"status":0,"sort":83,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913235","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ84","seats":4,"status":0,"sort":84,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913236","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ85","seats":4,"status":0,"sort":85,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913237","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ86","seats":4,"status":0,"sort":86,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913238","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ87","seats":4,"status":0,"sort":87,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913239","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ88","seats":4,"status":0,"sort":88,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913240","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ89","seats":4,"status":0,"sort":89,"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913241","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ90","seats":4,"status":0,"sort":90,"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913242","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ91","seats":4,"status":0,"sort":91,"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913243","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ92","seats":4,"status":0,"sort":92,"currentTime":"2025-06-26T15:47:35.668","combineTimes":0,"associatedTimes":0},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913244","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ93","seats":4,"status":0,"sort":93,"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913245","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ94","seats":4,"status":0,"sort":94,"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913246","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ95","seats":4,"status":0,"sort":95,"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913247","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ96","seats":4,"status":0,"sort":96,"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913248","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ97","seats":4,"status":0,"sort":97,"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913249","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ98","seats":4,"status":0,"sort":98,"currentTime":"2025-06-26T15:47:35.668"},{"storeGuid":"2106221850429620006","storeName":"交子大道测试门店","tableGuid":"7080354536228913250","areaName":"大厅","areaGuid":"7080354482868977664","tableCode":"ZJ99","seats":4,"status":0,"sort":99,"currentTime":"2025-06-26T15:47:35.668"}]] 
[2025-06-26 15:47:36:285][WARN ][pool-3-thread-1][org.apache.kafka.clients.ClientUtils.parseAndValidateAddresses:54][][Removing server test-holder-saas-kafka:9092 from bootstrap.servers as DNS resolution failed for test-holder-saas-kafka] 
[2025-06-26 15:47:44:754][WARN ][pool-3-thread-2][org.apache.kafka.clients.ClientUtils.parseAndValidateAddresses:54][][Removing server test-holder-saas-kafka:9092 from bootstrap.servers as DNS resolution failed for test-holder-saas-kafka] 
[2025-06-26 15:48:30:633][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.util.ConfigUtil.getAppId:62][][app.id is not set, please make sure it is set in classpath:/META-INF/app.properties, now apollo will only load public namespace configurations!] 
[2025-06-26 15:48:32:659][WARN ][Apollo-RemoteConfigLongPollService-1][com.ctrip.framework.apollo.internals.RemoteConfigLongPollService.doLongPollingRefresh:193][][Long polling failed, will retry in 120 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=************** [Cause: Could not complete get operation [Cause: apollo.meta]]] 
[2025-06-26 15:48:39:181][WARN ][Thread-42][com.netflix.discovery.DiscoveryClient$3.notify:1297][][Saw local status change event StatusChangeEvent [timestamp=1750924119181, current=DOWN, previous=UP]] 
