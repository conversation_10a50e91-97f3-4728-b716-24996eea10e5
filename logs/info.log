[35m[2025-06-23 14:30:14:676][m[INFO ][main][org.springframework.test.context.support.AbstractTestContextBootstrapper.getDefaultTestExecutionListenerClassNames:248][][Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener]] 
[35m[2025-06-23 14:30:14:730][m[INFO ][main][org.springframework.test.context.support.AbstractTestContextBootstrapper.getTestExecutionListeners:177][][Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@280c3dc0, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@6f89ad03, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@f287a4e, org.springframework.test.context.support.DependencyInjectionTestExecutionListener@3879feec, org.springframework.test.context.support.DirtiesContextTestExecutionListener@71d2261e, org.springframework.test.context.transaction.TransactionalTestExecutionListener@53917c92, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@1fa796a4, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@68de8522, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@8dcacf1, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@50f13494, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@73c09a98, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@6f2bf657]] 
[35m[2025-06-23 14:30:17:034][m[INFO ][main][org.hibernate.validator.internal.util.Version.<clinit>:21][][HV000001: Hibernate Validator 6.0.17.Final] 
[35m[2025-06-23 14:30:17:339][m[INFO ][main][org.springframework.mock.web.MockServletContext.log:444][][Initializing Spring TestDispatcherServlet ''] 
[35m[2025-06-23 14:30:17:339][m[INFO ][main][org.springframework.web.servlet.FrameworkServlet.initServletBean:524][][Initializing Servlet ''] 
[35m[2025-06-23 14:30:17:346][m[INFO ][main][org.springframework.web.servlet.FrameworkServlet.initServletBean:546][][Completed initialization in 6 ms] 
[35m[2025-06-23 14:30:17:550][m[INFO ][main][com.holderzone.holder.saas.store.deposit.controller.DepositController.queryRemind:135][][��ȡ������ʾ����:ModifyRemindReqDTO(storeGuid=1234)] 
[35m[2025-06-23 14:30:17:619][m[INFO ][main][org.springframework.mock.web.MockServletContext.log:444][][Initializing Spring TestDispatcherServlet ''] 
[35m[2025-06-23 14:30:17:619][m[INFO ][main][org.springframework.web.servlet.FrameworkServlet.initServletBean:524][][Initializing Servlet ''] 
[35m[2025-06-23 14:30:17:620][m[INFO ][main][org.springframework.web.servlet.FrameworkServlet.initServletBean:546][][Completed initialization in 1 ms] 
[35m[2025-06-23 14:30:17:688][m[INFO ][main][org.springframework.mock.web.MockServletContext.log:444][][Initializing Spring TestDispatcherServlet ''] 
[35m[2025-06-23 14:30:17:688][m[INFO ][main][org.springframework.web.servlet.FrameworkServlet.initServletBean:524][][Initializing Servlet ''] 
[35m[2025-06-23 14:30:17:689][m[INFO ][main][org.springframework.web.servlet.FrameworkServlet.initServletBean:546][][Completed initialization in 1 ms] 
[35m[2025-06-23 14:30:17:732][m[INFO ][main][com.holderzone.holder.saas.store.deposit.controller.DepositController.queryOperationHistory:98][][��ѯ������ʷ��¼:{"deviceType":1,"deviceId":"42","enterpriseGuid":"1234","enterpriseName":"Enterprise Name","storeGuid":"1234","storeName":"Store Name","longitude":"Longitude","latitude":"Latitude","userGuid":"1234","userName":"janedoe","account":"3","requestTimestamp":1,"operSubjectGuid":"1234","actuallyPayFee":2.3,"isInvoiceCode":1,"invoicePhone":"**********","accountName":"Dr Jane Doe","invoiceCode":"Invoice Code","currentPage":1,"pageSize":3,"maxId":1,"depositGuid":"1234","elapsedTime":*************}] 
[35m[2025-06-23 14:30:17:755][m[INFO ][main][org.springframework.mock.web.MockServletContext.log:444][][Initializing Spring TestDispatcherServlet ''] 
[35m[2025-06-23 14:30:17:755][m[INFO ][main][org.springframework.web.servlet.FrameworkServlet.initServletBean:524][][Initializing Servlet ''] 
[35m[2025-06-23 14:30:17:757][m[INFO ][main][org.springframework.web.servlet.FrameworkServlet.initServletBean:546][][Completed initialization in 0 ms] 
[35m[2025-06-23 14:30:17:765][m[INFO ][main][com.holderzone.holder.saas.store.deposit.controller.DepositController.depositExpireRemind:116][][�Ĵ���Ʒ��������:2025-06-23 14:30:17] 
[35m[2025-06-23 14:30:17:791][m[INFO ][main][org.springframework.mock.web.MockServletContext.log:444][][Initializing Spring TestDispatcherServlet ''] 
[35m[2025-06-23 14:30:17:791][m[INFO ][main][org.springframework.web.servlet.FrameworkServlet.initServletBean:524][][Initializing Servlet ''] 
[35m[2025-06-23 14:30:17:791][m[INFO ][main][org.springframework.web.servlet.FrameworkServlet.initServletBean:546][][Completed initialization in 0 ms] 
[35m[2025-06-23 14:30:17:821][m[INFO ][main][com.holderzone.holder.saas.store.deposit.controller.DepositController.queryDepositItem:62][][��ѯ�Ĵ��¼���:{"deviceType":1,"deviceId":"42","enterpriseGuid":"1234","enterpriseName":"Enterprise Name","storeGuid":"1234","storeName":"Store Name","longitude":"Longitude","latitude":"Latitude","userGuid":"1234","userName":"janedoe","account":"3","requestTimestamp":1,"operSubjectGuid":"1234","actuallyPayFee":2.3,"isInvoiceCode":1,"invoicePhone":"**********","accountName":"Dr Jane Doe","invoiceCode":"Invoice Code","currentPage":1,"pageSize":3,"maxId":1,"condition":"Condition","phoneGuid":"**********","wxGuid":"1234","elapsedTime":*************}] 
[35m[2025-06-23 14:30:17:846][m[INFO ][main][org.springframework.mock.web.MockServletContext.log:444][][Initializing Spring TestDispatcherServlet ''] 
[35m[2025-06-23 14:30:17:847][m[INFO ][main][org.springframework.web.servlet.FrameworkServlet.initServletBean:524][][Initializing Servlet ''] 
[35m[2025-06-23 14:30:17:847][m[INFO ][main][org.springframework.web.servlet.FrameworkServlet.initServletBean:546][][Completed initialization in 0 ms] 
[35m[2025-06-23 14:30:17:852][m[INFO ][main][com.holderzone.holder.saas.store.deposit.controller.DepositController.queryDepositDetailForPos:80][][��ѯ�Ĵ���������:{"depositGuid":"1234"}] 
[35m[2025-06-23 14:30:17:872][m[INFO ][main][org.springframework.mock.web.MockServletContext.log:444][][Initializing Spring TestDispatcherServlet ''] 
[35m[2025-06-23 14:30:17:872][m[INFO ][main][org.springframework.web.servlet.FrameworkServlet.initServletBean:524][][Initializing Servlet ''] 
[35m[2025-06-23 14:30:17:873][m[INFO ][main][org.springframework.web.servlet.FrameworkServlet.initServletBean:546][][Completed initialization in 0 ms] 
[35m[2025-06-23 14:30:17:878][m[INFO ][main][com.holderzone.holder.saas.store.deposit.controller.DepositController.queryDepositDetailForPos:80][][��ѯ�Ĵ���������:{"depositGuid":"1234"}] 
[35m[2025-06-23 14:30:17:902][m[INFO ][main][org.springframework.mock.web.MockServletContext.log:444][][Initializing Spring TestDispatcherServlet ''] 
[35m[2025-06-23 14:30:17:902][m[INFO ][main][org.springframework.web.servlet.FrameworkServlet.initServletBean:524][][Initializing Servlet ''] 
[35m[2025-06-23 14:30:17:902][m[INFO ][main][org.springframework.web.servlet.FrameworkServlet.initServletBean:546][][Completed initialization in 0 ms] 
[35m[2025-06-23 14:30:17:903][m[INFO ][main][com.holderzone.holder.saas.store.deposit.controller.DepositController.depositExpireRemind:116][][�Ĵ���Ʒ��������:2025-06-23 14:30:17] 
[35m[2025-06-23 14:30:17:926][m[INFO ][main][org.springframework.mock.web.MockServletContext.log:444][][Initializing Spring TestDispatcherServlet ''] 
[35m[2025-06-23 14:30:17:927][m[INFO ][main][org.springframework.web.servlet.FrameworkServlet.initServletBean:524][][Initializing Servlet ''] 
[35m[2025-06-23 14:30:17:928][m[INFO ][main][org.springframework.web.servlet.FrameworkServlet.initServletBean:546][][Completed initialization in 0 ms] 
[35m[2025-06-23 14:30:17:948][m[INFO ][main][com.holderzone.holder.saas.store.deposit.controller.DepositController.queryGoodsSummary:107][][��ѯ��Ʒ�Ĵ�������:{"deviceType":1,"deviceId":"42","enterpriseGuid":"1234","enterpriseName":"Enterprise Name","storeGuid":"1234","storeName":"Store Name","longitude":"Longitude","latitude":"Latitude","userGuid":"1234","userName":"janedoe","account":"3","requestTimestamp":1,"operSubjectGuid":"1234","actuallyPayFee":2.3,"isInvoiceCode":1,"invoicePhone":"**********","accountName":"Dr Jane Doe","invoiceCode":"Invoice Code","currentPage":1,"pageSize":3,"maxId":1,"condition":"Condition","elapsedTime":*************}] 
[35m[2025-06-23 14:30:17:971][m[INFO ][main][org.springframework.mock.web.MockServletContext.log:444][][Initializing Spring TestDispatcherServlet ''] 
[35m[2025-06-23 14:30:17:972][m[INFO ][main][org.springframework.web.servlet.FrameworkServlet.initServletBean:524][][Initializing Servlet ''] 
[35m[2025-06-23 14:30:17:972][m[INFO ][main][org.springframework.web.servlet.FrameworkServlet.initServletBean:546][][Completed initialization in 0 ms] 
[35m[2025-06-23 14:30:17:976][m[INFO ][main][com.holderzone.holder.saas.store.deposit.controller.DepositController.queryDepositDetail:71][][��ѯ�Ĵ���������:{"depositGuid":"1234"}] 
[35m[2025-06-23 14:30:17:999][m[INFO ][main][org.springframework.mock.web.MockServletContext.log:444][][Initializing Spring TestDispatcherServlet ''] 
[35m[2025-06-23 14:30:17:999][m[INFO ][main][org.springframework.web.servlet.FrameworkServlet.initServletBean:524][][Initializing Servlet ''] 
[35m[2025-06-23 14:30:17:999][m[INFO ][main][org.springframework.web.servlet.FrameworkServlet.initServletBean:546][][Completed initialization in 0 ms] 
[35m[2025-06-23 14:30:18:027][m[INFO ][main][org.springframework.mock.web.MockServletContext.log:444][][Initializing Spring TestDispatcherServlet ''] 
[35m[2025-06-23 14:30:18:028][m[INFO ][main][org.springframework.web.servlet.FrameworkServlet.initServletBean:524][][Initializing Servlet ''] 
[35m[2025-06-23 14:30:18:028][m[INFO ][main][org.springframework.web.servlet.FrameworkServlet.initServletBean:546][][Completed initialization in 0 ms] 
[35m[2025-06-23 14:30:18:038][m[INFO ][main][com.holderzone.holder.saas.store.deposit.controller.DepositController.createDepositItem:53][][�½��Ĵ��¼���:{"storeGuid":"1234","memberGuid":"1234","headPortrait":"Head Portrait","goods":[{"guid":"1234","goodsName":"deposit","skuName":"deposit","skuGuid":"1234","goodsUnit":"deposit","storePosition":"deposit","expireTime":"deposit","residueDay":1,"depositNum":10,"residueNum":1,"takeOutNum":10}],"remark":"Remark","customerName":"Customer Name","phoneNum":"**********"}] 
[35m[2025-06-23 14:30:18:057][m[INFO ][main][org.springframework.mock.web.MockServletContext.log:444][][Initializing Spring TestDispatcherServlet ''] 
[35m[2025-06-23 14:30:18:058][m[INFO ][main][org.springframework.web.servlet.FrameworkServlet.initServletBean:524][][Initializing Servlet ''] 
[35m[2025-06-23 14:30:18:058][m[INFO ][main][org.springframework.web.servlet.FrameworkServlet.initServletBean:546][][Completed initialization in 0 ms] 
[35m[2025-06-23 14:30:18:064][m[INFO ][main][com.holderzone.holder.saas.store.deposit.controller.DepositController.queryDepositDetail:71][][��ѯ�Ĵ���������:{"depositGuid":"1234"}] 
[35m[2025-06-23 14:30:18:085][m[INFO ][main][org.springframework.mock.web.MockServletContext.log:444][][Initializing Spring TestDispatcherServlet ''] 
[35m[2025-06-23 14:30:18:086][m[INFO ][main][org.springframework.web.servlet.FrameworkServlet.initServletBean:524][][Initializing Servlet ''] 
[35m[2025-06-23 14:30:18:086][m[INFO ][main][org.springframework.web.servlet.FrameworkServlet.initServletBean:546][][Completed initialization in 0 ms] 
[35m[2025-06-23 14:30:18:093][m[INFO ][main][com.holderzone.holder.saas.store.deposit.controller.DepositController.getDepositGoods:89][][ȡ���Ĵ���Ʒ:{"depositGuid":"1234","userGuid":"1234","goodsList":[],"remark":"Remark"}] 
[35m[2025-06-23 14:30:18:113][m[INFO ][main][org.springframework.mock.web.MockServletContext.log:444][][Initializing Spring TestDispatcherServlet ''] 
[35m[2025-06-23 14:30:18:114][m[INFO ][main][org.springframework.web.servlet.FrameworkServlet.initServletBean:524][][Initializing Servlet ''] 
[35m[2025-06-23 14:30:18:114][m[INFO ][main][org.springframework.web.servlet.FrameworkServlet.initServletBean:546][][Completed initialization in 0 ms] 
[35m[2025-06-23 14:30:18:119][m[INFO ][main][com.holderzone.holder.saas.store.deposit.controller.DepositController.remindSet:126][][������ʾ����:MessageRemindReqDTO(storeGuid=1234, storeName=Store Name, depositRemind=1, getRemind=1, expireRemind=1, advanceDays=1)] 
[35m[2025-06-23 14:30:18:156][m[INFO ][main][org.springframework.test.context.support.AbstractTestContextBootstrapper.buildDefaultMergedContextConfiguration:308][][Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.holderzone.holder.saas.store.deposit.controller.DepositControllerTest], using SpringBootContextLoader] 
[35m[2025-06-23 14:30:18:156][m[INFO ][main][org.springframework.test.context.support.AbstractContextLoader.generateDefaultLocations:264][][Could not detect default resource locations for test class [com.holderzone.holder.saas.store.deposit.controller.DepositControllerTest]: no resource found for suffixes {-context.xml, Context.groovy}.] 
[35m[2025-06-23 14:30:18:158][m[INFO ][main][org.springframework.test.context.support.AnnotationConfigContextLoaderUtils.detectDefaultConfigurationClasses:83][][Could not detect default configuration classes for test class [com.holderzone.holder.saas.store.deposit.controller.DepositControllerTest]: DepositControllerTest does not declare any static, non-private, non-final, nested classes annotated with @Configuration.] 
[35m[2025-06-23 14:30:18:231][m[INFO ][main][org.springframework.boot.test.context.SpringBootTestContextBootstrapper.getOrFindConfigurationClasses:235][][Found @SpringBootConfiguration com.holderzone.holder.saas.store.deposit.HolderSaasStoreDepositApplication for test class com.holderzone.holder.saas.store.deposit.controller.DepositControllerTest] 
[35m[2025-06-23 14:30:18:233][m[INFO ][main][org.springframework.test.context.support.AbstractTestContextBootstrapper.getDefaultTestExecutionListenerClassNames:248][][Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener]] 
[35m[2025-06-23 14:30:18:237][m[INFO ][main][org.springframework.test.context.support.AbstractTestContextBootstrapper.getTestExecutionListeners:177][][Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@de5dd8c, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@24659777, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@38330440, org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@27a17581, org.springframework.test.context.support.DirtiesContextTestExecutionListener@40bd544, org.springframework.test.context.transaction.TransactionalTestExecutionListener@c5d249d, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@2cec2052, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@4d5533, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@5ab6118b, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@3faabf8c, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@7209f69b, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@1807c124]] 
[35m[2025-06-23 14:30:19:570][m[INFO ][main][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:330][][Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$175022fa] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)] 
[35m[2025-06-23 14:30:20:036][m[INFO ][main][org.springframework.boot.SpringApplication.logStartupProfileInfo:651][][The following profiles are active: dev] 
[35m[2025-06-23 14:30:21:069][m[INFO ][main][com.ctrip.framework.foundation.internals.provider.DefaultServerProvider.initEnvType:130][][Environment is set to null. Because it is not available in either (1) JVM system property 'env', (2) OS env variable 'ENV' nor (3) property 'env' from the properties InputStream.] 
[35m[2025-06-23 14:30:36:495][m[INFO ][main][springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping.initHandlerMethods:69][][Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]] 
[35m[2025-06-23 14:30:36:534][m[INFO ][main][org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener.logMessage:136][][

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.] 
[35m[2025-06-23 14:30:36:864][m[INFO ][main][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:330][][Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$175022fa] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)] 
[35m[2025-06-23 14:30:37:126][m[INFO ][main][org.springframework.boot.SpringApplication.logStartupProfileInfo:651][][The following profiles are active: dev] 
[35m[2025-06-23 14:30:37:682][m[INFO ][main][springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping.initHandlerMethods:69][][Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]] 
[35m[2025-06-23 14:30:37:693][m[INFO ][main][org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener.logMessage:136][][

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.] 
[35m[2025-06-23 14:30:38:006][m[INFO ][main][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:330][][Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$175022fa] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)] 
[35m[2025-06-23 14:30:38:289][m[INFO ][main][org.springframework.boot.SpringApplication.logStartupProfileInfo:651][][The following profiles are active: dev] 
[35m[2025-06-23 14:30:38:681][m[INFO ][main][springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping.initHandlerMethods:69][][Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]] 
[35m[2025-06-23 14:30:38:688][m[INFO ][main][org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener.logMessage:136][][

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.] 
[35m[2025-06-23 14:30:38:974][m[INFO ][main][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:330][][Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$175022fa] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)] 
[35m[2025-06-23 14:30:39:236][m[INFO ][main][org.springframework.boot.SpringApplication.logStartupProfileInfo:651][][The following profiles are active: dev] 
[35m[2025-06-23 14:30:39:645][m[INFO ][main][springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping.initHandlerMethods:69][][Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]] 
[35m[2025-06-23 14:30:39:652][m[INFO ][main][org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener.logMessage:136][][

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.] 
[35m[2025-06-23 14:30:39:917][m[INFO ][main][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:330][][Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$175022fa] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)] 
[35m[2025-06-23 14:30:40:180][m[INFO ][main][org.springframework.boot.SpringApplication.logStartupProfileInfo:651][][The following profiles are active: dev] 
[35m[2025-06-23 14:30:40:641][m[INFO ][main][springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping.initHandlerMethods:69][][Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]] 
[35m[2025-06-23 14:30:40:652][m[INFO ][main][org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener.logMessage:136][][

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.] 
[35m[2025-06-23 14:30:41:010][m[INFO ][main][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:330][][Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$175022fa] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)] 
[35m[2025-06-23 14:30:41:283][m[INFO ][main][org.springframework.boot.SpringApplication.logStartupProfileInfo:651][][The following profiles are active: dev] 
[35m[2025-06-23 14:30:41:620][m[INFO ][main][springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping.initHandlerMethods:69][][Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]] 
[35m[2025-06-23 14:30:41:624][m[INFO ][main][org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener.logMessage:136][][

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.] 
[35m[2025-06-23 14:30:41:902][m[INFO ][main][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:330][][Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$175022fa] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)] 
[35m[2025-06-23 14:30:42:164][m[INFO ][main][org.springframework.boot.SpringApplication.logStartupProfileInfo:651][][The following profiles are active: dev] 
[35m[2025-06-23 14:30:42:542][m[INFO ][main][springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping.initHandlerMethods:69][][Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]] 
[35m[2025-06-23 14:30:42:548][m[INFO ][main][org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener.logMessage:136][][

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.] 
[35m[2025-06-23 14:30:42:811][m[INFO ][main][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:330][][Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$175022fa] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)] 
[35m[2025-06-23 14:30:43:023][m[INFO ][main][org.springframework.boot.SpringApplication.logStartupProfileInfo:651][][The following profiles are active: dev] 
[35m[2025-06-23 14:30:43:363][m[INFO ][main][springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping.initHandlerMethods:69][][Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]] 
[35m[2025-06-23 14:30:43:370][m[INFO ][main][org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener.logMessage:136][][

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.] 
[35m[2025-06-23 14:30:43:592][m[INFO ][main][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:330][][Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$175022fa] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)] 
[35m[2025-06-23 14:30:43:804][m[INFO ][main][org.springframework.boot.SpringApplication.logStartupProfileInfo:651][][The following profiles are active: dev] 
[35m[2025-06-23 14:30:44:159][m[INFO ][main][springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping.initHandlerMethods:69][][Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]] 
[35m[2025-06-23 14:30:44:164][m[INFO ][main][org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener.logMessage:136][][

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.] 
[35m[2025-06-23 14:30:44:389][m[INFO ][main][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:330][][Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$175022fa] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)] 
[35m[2025-06-23 14:30:44:639][m[INFO ][main][org.springframework.boot.SpringApplication.logStartupProfileInfo:651][][The following profiles are active: dev] 
[35m[2025-06-23 14:30:44:918][m[INFO ][main][springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping.initHandlerMethods:69][][Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]] 
[35m[2025-06-23 14:30:44:924][m[INFO ][main][org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener.logMessage:136][][

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.] 
[35m[2025-06-23 14:30:45:123][m[INFO ][main][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:330][][Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$175022fa] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)] 
[35m[2025-06-23 14:30:45:337][m[INFO ][main][org.springframework.boot.SpringApplication.logStartupProfileInfo:651][][The following profiles are active: dev] 
[35m[2025-06-23 14:30:45:646][m[INFO ][main][springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping.initHandlerMethods:69][][Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]] 
[35m[2025-06-23 14:30:45:651][m[INFO ][main][org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener.logMessage:136][][

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.] 
[35m[2025-06-23 14:30:45:948][m[INFO ][main][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:330][][Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$175022fa] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)] 
[35m[2025-06-23 14:30:46:147][m[INFO ][main][org.springframework.boot.SpringApplication.logStartupProfileInfo:651][][The following profiles are active: dev] 
[35m[2025-06-23 14:30:46:404][m[INFO ][main][springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping.initHandlerMethods:69][][Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]] 
[35m[2025-06-23 14:30:46:407][m[INFO ][main][org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener.logMessage:136][][

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.] 
[35m[2025-06-23 14:30:46:620][m[INFO ][main][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:330][][Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$175022fa] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)] 
[35m[2025-06-23 14:30:46:842][m[INFO ][main][org.springframework.boot.SpringApplication.logStartupProfileInfo:651][][The following profiles are active: dev] 
[35m[2025-06-23 14:30:47:118][m[INFO ][main][springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping.initHandlerMethods:69][][Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]] 
[35m[2025-06-23 14:30:47:124][m[INFO ][main][org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener.logMessage:136][][

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.] 
[35m[2025-06-23 14:30:47:362][m[INFO ][main][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:330][][Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$175022fa] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)] 
[35m[2025-06-23 14:30:47:561][m[INFO ][main][org.springframework.boot.SpringApplication.logStartupProfileInfo:651][][The following profiles are active: dev] 
[35m[2025-06-23 14:30:47:822][m[INFO ][main][springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping.initHandlerMethods:69][][Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]] 
[35m[2025-06-23 14:30:47:827][m[INFO ][main][org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener.logMessage:136][][

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.] 
[35m[2025-06-23 14:30:48:046][m[INFO ][main][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:330][][Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$175022fa] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)] 
[35m[2025-06-23 14:30:48:266][m[INFO ][main][org.springframework.boot.SpringApplication.logStartupProfileInfo:651][][The following profiles are active: dev] 
[35m[2025-06-23 14:30:49:033][m[INFO ][main][org.springframework.data.repository.config.RepositoryConfigurationDelegate.multipleStoresDetected:244][][Multiple Spring Data modules found, entering strict repository configuration mode!] 
[35m[2025-06-23 14:30:49:038][m[INFO ][main][org.springframework.data.repository.config.RepositoryConfigurationDelegate.registerRepositoriesIn:126][][Bootstrapping Spring Data repositories in DEFAULT mode.] 
[35m[2025-06-23 14:30:49:074][m[INFO ][main][org.springframework.data.repository.config.RepositoryConfigurationDelegate.registerRepositoriesIn:182][][Finished Spring Data repository scanning in 17ms. Found 0 repository interfaces.] 
[35m[2025-06-23 14:30:49:518][m[INFO ][main][org.springframework.cloud.context.scope.GenericScope.setSerializationId:294][][BeanFactory id=e45d747c-3867-3b99-afbc-eff9f797c56b] 
[35m[2025-06-23 14:30:49:679][m[INFO ][main][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:330][][Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$fb361ffd] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)] 
[35m[2025-06-23 14:30:49:927][m[INFO ][main][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:330][][Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$175022fa] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)] 
[35m[2025-06-23 14:30:50:434][m[INFO ][main][io.undertow.servlet.spec.ServletContextImpl.log:364][][Initializing Spring embedded WebApplicationContext] 
[35m[2025-06-23 14:30:50:435][m[INFO ][main][org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.prepareWebApplicationContext:284][][Root WebApplicationContext: initialization completed in 2167 ms] 
[35m[2025-06-23 14:30:50:626][m[INFO ][main][com.netflix.config.sources.URLConfigurationSource.<init>:122][][To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.] 
[35m[2025-06-23 14:30:50:648][m[INFO ][main][com.netflix.config.DynamicPropertyFactory.getInstance:281][][DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@2b06454f] 
[35m[2025-06-23 14:30:52:236][m[INFO ][main][org.springframework.scheduling.concurrent.ExecutorConfigurationSupport.initialize:171][][Initializing ExecutorService] 
[35m[2025-06-23 14:30:52:237][m[INFO ][main][org.springframework.scheduling.concurrent.ExecutorConfigurationSupport.initialize:171][][Initializing ExecutorService 'asyncLogExecutor'] 
[35m[2025-06-23 14:30:53:796][m[INFO ][main][org.springframework.scheduling.concurrent.ExecutorConfigurationSupport.initialize:171][][Initializing ExecutorService 'threadPool'] 
[35m[2025-06-23 14:30:54:144][m[INFO ][main][org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver.<init>:58][][Exposing 20 endpoint(s) beneath base path '/actuator'] 
[35m[2025-06-23 14:30:54:239][m[INFO ][main][springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping.initHandlerMethods:69][][Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]] 
[35m[2025-06-23 14:30:54:253][m[INFO ][main][com.netflix.config.sources.URLConfigurationSource.<init>:122][][To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.] 
[35m[2025-06-23 14:30:55:487][m[INFO ][main][org.springframework.scheduling.concurrent.ExecutorConfigurationSupport.initialize:171][][Initializing ExecutorService 'taskScheduler'] 
[35m[2025-06-23 14:30:55:674][m[INFO ][main][org.springframework.cloud.netflix.eureka.InstanceInfoFactory.create:71][][Setting initial instance status as: STARTING] 
[35m[2025-06-23 14:30:55:740][m[INFO ][main][com.netflix.discovery.DiscoveryClient.<init>:349][][Initializing Eureka in region us-east-1] 
[35m[2025-06-23 14:30:55:823][m[INFO ][main][com.netflix.discovery.provider.DiscoveryJerseyProvider.<init>:70][][Using JSON encoding codec LegacyJacksonJson] 
[35m[2025-06-23 14:30:55:824][m[INFO ][main][com.netflix.discovery.provider.DiscoveryJerseyProvider.<init>:71][][Using JSON decoding codec LegacyJacksonJson] 
[35m[2025-06-23 14:30:56:001][m[INFO ][main][com.netflix.discovery.provider.DiscoveryJerseyProvider.<init>:80][][Using XML encoding codec XStreamXml] 
[35m[2025-06-23 14:30:56:003][m[INFO ][main][com.netflix.discovery.provider.DiscoveryJerseyProvider.<init>:81][][Using XML decoding codec XStreamXml] 
[35m[2025-06-23 14:30:56:597][m[INFO ][main][com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver.getClusterEndpoints:43][][Resolving eureka endpoints via configuration] 
[35m[2025-06-23 14:30:56:770][m[INFO ][main][com.netflix.discovery.DiscoveryClient.fetchRegistry:958][][Disable delta property : false] 
[35m[2025-06-23 14:30:56:771][m[INFO ][main][com.netflix.discovery.DiscoveryClient.fetchRegistry:959][][Single vip registry refresh property : null] 
[35m[2025-06-23 14:30:56:771][m[INFO ][main][com.netflix.discovery.DiscoveryClient.fetchRegistry:960][][Force full registry fetch : false] 
[35m[2025-06-23 14:30:56:771][m[INFO ][main][com.netflix.discovery.DiscoveryClient.fetchRegistry:961][][Application is null : false] 
[35m[2025-06-23 14:30:56:771][m[INFO ][main][com.netflix.discovery.DiscoveryClient.fetchRegistry:962][][Registered Applications size is zero : true] 
[35m[2025-06-23 14:30:56:771][m[INFO ][main][com.netflix.discovery.DiscoveryClient.fetchRegistry:964][][Application version is -1: true] 
[35m[2025-06-23 14:30:56:771][m[INFO ][main][com.netflix.discovery.DiscoveryClient.getAndStoreFullRegistry:1047][][Getting all instance registry info from the eureka server] 
[35m[2025-06-23 14:31:01:859][m[INFO ][main][com.netflix.discovery.DiscoveryClient.initScheduledTasks:1264][][Starting heartbeat executor: renew interval is: 5] 
[35m[2025-06-23 14:31:01:864][m[INFO ][main][com.netflix.discovery.InstanceInfoReplicator.<init>:60][][InstanceInfoReplicator onDemand update allowed rate per min is 4] 
[35m[2025-06-23 14:31:01:869][m[INFO ][main][com.netflix.discovery.DiscoveryClient.<init>:449][][Discovery Client initialized at timestamp 1750660261867 with initial instances count: 0] 
[35m[2025-06-23 14:31:01:876][m[INFO ][main][org.springframework.cloud.netflix.eureka.serviceregistry.EurekaServiceRegistry.register:42][][Registering application holder-saas-store-deposit with eureka with status UP] 
[35m[2025-06-23 14:31:01:878][m[INFO ][main][com.netflix.discovery.DiscoveryClient$3.notify:1299][][Saw local status change event StatusChangeEvent [timestamp=1750660261878, current=UP, previous=STARTING]] 
[35m[2025-06-23 14:31:01:881][m[INFO ][DiscoveryClient-InstanceInfoReplicator-0][com.netflix.discovery.DiscoveryClient.register:826][][DiscoveryClient_HOLDER-SAAS-STORE-DEPOSIT/192.168.102.11:8924: registering service...] 
[35m[2025-06-23 14:31:01:883][m[INFO ][main][springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper.start:160][][Context refreshed] 
[35m[2025-06-23 14:31:01:899][m[INFO ][main][springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper.start:163][][Found 1 custom documentation plugin(s)] 
[35m[2025-06-23 14:31:01:956][m[INFO ][main][springfox.documentation.spring.web.scanners.ApiListingReferenceScanner.scan:41][][Scanning for api listing references] 
[35m[2025-06-23 14:31:02:224][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingGET_1] 
[35m[2025-06-23 14:31:02:225][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingGET_2] 
[35m[2025-06-23 14:31:02:230][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingGET_3] 
[35m[2025-06-23 14:31:02:233][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingDELETE_1] 
[35m[2025-06-23 14:31:02:237][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingGET_4] 
[35m[2025-06-23 14:31:02:239][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingGET_5] 
[35m[2025-06-23 14:31:02:241][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingGET_6] 
[35m[2025-06-23 14:31:02:243][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingGET_7] 
[35m[2025-06-23 14:31:02:246][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingGET_8] 
[35m[2025-06-23 14:31:02:250][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingDELETE_2] 
[35m[2025-06-23 14:31:02:255][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingGET_9] 
[35m[2025-06-23 14:31:02:257][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingGET_10] 
[35m[2025-06-23 14:31:02:259][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingGET_11] 
[35m[2025-06-23 14:31:02:262][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingGET_12] 
[35m[2025-06-23 14:31:02:264][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingGET_13] 
[35m[2025-06-23 14:31:02:268][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingGET_14] 
[35m[2025-06-23 14:31:02:272][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingGET_15] 
[35m[2025-06-23 14:31:02:274][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingPOST_1] 
[35m[2025-06-23 14:31:02:275][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingGET_16] 
[35m[2025-06-23 14:31:02:279][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingGET_17] 
[35m[2025-06-23 14:31:02:281][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingGET_18] 
[35m[2025-06-23 14:31:02:283][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingGET_19] 
[35m[2025-06-23 14:31:02:286][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingGET_20] 
[35m[2025-06-23 14:31:02:289][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingPOST_2] 
[35m[2025-06-23 14:31:02:290][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingGET_21] 
[35m[2025-06-23 14:31:02:292][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingPOST_3] 
[35m[2025-06-23 14:31:02:295][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingGET_22] 
[35m[2025-06-23 14:31:02:297][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingPOST_4] 
[35m[2025-06-23 14:31:02:300][m[INFO ][main][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40][][Generating unique operation named: handleUsingGET_23] 
[35m[2025-06-23 14:31:02:364][m[INFO ][main][org.xnio.Xnio.<clinit>:104][][XNIO version 3.3.8.Final] 
[35m[2025-06-23 14:31:02:384][m[INFO ][main][org.xnio.nio.NioXnio.<clinit>:55][][XNIO NIO Implementation Version 3.3.8.Final] 
[35m[2025-06-23 14:31:02:494][m[INFO ][main][org.springframework.boot.web.embedded.undertow.UndertowServletWebServer.start:144][][Undertow started on port(s) 8924 (http) with context path ''] 
[35m[2025-06-23 14:31:02:497][m[INFO ][main][org.springframework.boot.StartupInfoLogger.logStarted:59][][Started HolderSaasStoreDepositApplication in 14.662 seconds (JVM running for 49.797)] 
[35m[2025-06-23 14:31:02:508][m[INFO ][main][org.springframework.test.context.support.AbstractTestContextBootstrapper.getDefaultTestExecutionListenerClassNames:248][][Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener]] 
[35m[2025-06-23 14:31:02:508][m[INFO ][main][org.springframework.test.context.support.AbstractTestContextBootstrapper.getTestExecutionListeners:177][][Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@5ad5e857, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@c36b8fd, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@9e925d7, org.springframework.test.context.support.DependencyInjectionTestExecutionListener@17560618, org.springframework.test.context.support.DirtiesContextTestExecutionListener@6234d3f5, org.springframework.test.context.transaction.TransactionalTestExecutionListener@5b7c7e1e, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@1bfb3f3b, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@3cba8c9a, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@78d54b83, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@40614609, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@221abd9, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@6a97295b]] 
[35m[2025-06-23 14:31:02:696][m[INFO ][main][org.springframework.test.context.support.AbstractTestContextBootstrapper.getDefaultTestExecutionListenerClassNames:248][][Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener]] 
[35m[2025-06-23 14:31:02:696][m[INFO ][main][org.springframework.test.context.support.AbstractTestContextBootstrapper.getTestExecutionListeners:177][][Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@6bdd520a, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@6f9a11a3, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@143e04c8, org.springframework.test.context.support.DependencyInjectionTestExecutionListener@2d41a1fd, org.springframework.test.context.support.DirtiesContextTestExecutionListener@2c8dfb5d, org.springframework.test.context.transaction.TransactionalTestExecutionListener@1325e066, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@77d9ae8e, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@5737a7c1, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@1423f969, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@28b91d5b, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@12ae6b1c, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@78cddf27]] 
[35m[2025-06-23 14:31:02:805][m[INFO ][main][org.springframework.test.context.support.AbstractTestContextBootstrapper.getDefaultTestExecutionListenerClassNames:248][][Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener]] 
[35m[2025-06-23 14:31:02:805][m[INFO ][main][org.springframework.test.context.support.AbstractTestContextBootstrapper.getTestExecutionListeners:177][][Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@38744567, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@35703950, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@2926a295, org.springframework.test.context.support.DependencyInjectionTestExecutionListener@d4dc3ad, org.springframework.test.context.support.DirtiesContextTestExecutionListener@1708e01e, org.springframework.test.context.transaction.TransactionalTestExecutionListener@2386e116, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@3f28ddf9, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@5c1b311a, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@c433297, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@18d9fc58, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@61e73daf, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@64714368]] 
[35m[2025-06-23 14:31:03:065][m[INFO ][main][org.springframework.test.context.support.AbstractTestContextBootstrapper.getDefaultTestExecutionListenerClassNames:248][][Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener]] 
[35m[2025-06-23 14:31:03:066][m[INFO ][main][org.springframework.test.context.support.AbstractTestContextBootstrapper.getTestExecutionListeners:177][][Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@48a4516f, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@24f1748a, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@210ec289, org.springframework.test.context.support.DependencyInjectionTestExecutionListener@7e4bb630, org.springframework.test.context.support.DirtiesContextTestExecutionListener@148ebfbf, org.springframework.test.context.transaction.TransactionalTestExecutionListener@3bf4dc80, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@6c395bfd, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@4fe09a7c, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@2e72a778, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@337f7eea, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@153f3dcb, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@5e47766a]] 
[35m[2025-06-23 14:31:03:512][m[INFO ][main][com.holderzone.holder.saas.store.deposit.service.impl.HsdDepositServiceImpl.queryDepositRecord:325][][��ѯ���� condition ��Ӧ�Ļ�Ա��Ϣ��[]] 
[35m[2025-06-23 14:31:03:553][m[INFO ][main][com.holderzone.holder.saas.store.deposit.service.impl.HsdDepositServiceImpl.queryDepositRecord:325][][��ѯ���� condition ��Ӧ�Ļ�Ա��Ϣ��[{"memberInfoGuid":"memberInfoGuid","phoneNum":"phoneNum","openId":"openId","nickName":"nickName"}]] 
[35m[2025-06-23 14:31:03:574][m[INFO ][main][com.holderzone.holder.saas.store.deposit.service.impl.HsdDepositServiceImpl.queryDepositRecord:325][][��ѯ���� condition ��Ӧ�Ļ�Ա��Ϣ��[{"memberInfoGuid":"memberInfoGuid","phoneNum":"phoneNum","openId":"openId","nickName":"nickName"}]] 
[35m[2025-06-23 14:31:03:607][m[INFO ][main][org.springframework.test.context.support.AbstractTestContextBootstrapper.getDefaultTestExecutionListenerClassNames:248][][Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener]] 
[35m[2025-06-23 14:31:03:609][m[INFO ][main][org.springframework.test.context.support.AbstractTestContextBootstrapper.getTestExecutionListeners:177][][Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@430a42f2, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@665fb2df, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@6b4e693c, org.springframework.test.context.support.DependencyInjectionTestExecutionListener@24cebdef, org.springframework.test.context.support.DirtiesContextTestExecutionListener@35c26656, org.springframework.test.context.transaction.TransactionalTestExecutionListener@2e10f37b, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@360f680c, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@67ff9ae7, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@41a9b098, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@680b08fd, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@258952f8, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@3e3adc0c]] 
[35m[2025-06-23 14:31:03:656][m[INFO ][main][org.springframework.test.context.support.AbstractTestContextBootstrapper.getDefaultTestExecutionListenerClassNames:248][][Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener]] 
[35m[2025-06-23 14:31:03:657][m[INFO ][main][org.springframework.test.context.support.AbstractTestContextBootstrapper.getTestExecutionListeners:177][][Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@43e9113c, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@1f71cd95, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@9e6b88a, org.springframework.test.context.support.DependencyInjectionTestExecutionListener@50dcd59, org.springframework.test.context.support.DirtiesContextTestExecutionListener@ee64c35, org.springframework.test.context.transaction.TransactionalTestExecutionListener@1f6a3519, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@2374ec96, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@48f89aa4, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@7443f06a, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@3a4fc106, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@3e5b9a85, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@32fa1a50]] 
[35m[2025-06-23 14:31:03:694][m[INFO ][main][org.springframework.test.context.support.AbstractTestContextBootstrapper.getDefaultTestExecutionListenerClassNames:248][][Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener]] 
[35m[2025-06-23 14:31:03:696][m[INFO ][main][org.springframework.test.context.support.AbstractTestContextBootstrapper.getTestExecutionListeners:177][][Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@315a9b59, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@f26fec5, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@77ca3497, org.springframework.test.context.support.DependencyInjectionTestExecutionListener@42be5190, org.springframework.test.context.support.DirtiesContextTestExecutionListener@27db9acd, org.springframework.test.context.transaction.TransactionalTestExecutionListener@1fb39825, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@39fb69c8, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@9f99db6, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@720f73b6, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@717ebf62, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@13a92dac, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@56f81642]] 
[35m[2025-06-23 14:31:03:737][m[INFO ][main][com.holderzone.holder.saas.store.deposit.service.impl.HsdRemindServiceImpl.queryRemindRecord:66][][��ѯ�������ü�¼:{"id":1,"guid":"1234","storeGuid":"1234","storeName":"Store Name","advanceDays":1,"depositRemind":1,"getRemind":1,"expireRemind":1,"gmtCreate":"1970-01-01T00:00:00","gmtModified":"1970-01-01T00:00:00"}] 
[35m[2025-06-23 14:31:03:750][m[INFO ][main][com.holderzone.holder.saas.store.deposit.service.impl.HsdRemindServiceImpl.queryRemindRecord:69][][�ɹ���ȡ���ŵ���Ϣ:{}] 
[35m[2025-06-23 14:31:03:758][m[INFO ][main][com.holderzone.holder.saas.store.deposit.service.impl.HsdRemindServiceImpl.queryRemindRecord:66][][��ѯ�������ü�¼:{"id":1,"guid":"1234","storeGuid":"1234","storeName":"Store Name","advanceDays":1,"depositRemind":1,"getRemind":1,"expireRemind":1,"gmtCreate":"1970-01-01T00:00:00","gmtModified":"1970-01-01T00:00:00"}] 
[35m[2025-06-23 14:31:03:758][m[INFO ][main][com.holderzone.holder.saas.store.deposit.service.impl.HsdRemindServiceImpl.queryRemindRecord:69][][�ɹ���ȡ���ŵ���Ϣ:{}] 
[35m[2025-06-23 14:31:03:764][m[INFO ][main][org.springframework.test.context.support.AbstractTestContextBootstrapper.getDefaultTestExecutionListenerClassNames:248][][Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener]] 
[35m[2025-06-23 14:31:03:764][m[INFO ][main][org.springframework.test.context.support.AbstractTestContextBootstrapper.getTestExecutionListeners:177][][Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@2a4299da, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@67f2ef7c, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@3092d1e, org.springframework.test.context.support.DependencyInjectionTestExecutionListener@4b6f49a7, org.springframework.test.context.support.DirtiesContextTestExecutionListener@75dd8559, org.springframework.test.context.transaction.TransactionalTestExecutionListener@5a40d91, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@4f0fa1e6, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@7e1cfbd1, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@67724e06, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@421f1ffc, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@702d86c8, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@6976396f]] 
[35m[2025-06-23 14:31:03:791][m[INFO ][main][com.holderzone.holder.saas.store.deposit.service.impl.SendMessageServiceImpl.sendMessage:45][][�Ĵ���ط��Ͷ��� ] 
[35m[2025-06-23 14:31:03:792][m[INFO ][main][com.holderzone.holder.saas.store.deposit.service.impl.SendMessageServiceImpl.sendMessage:57][][�Ĵ���ض��ŷ�������ʱ��:2025-06-23 14:31:03] 
[35m[2025-06-23 14:31:03:794][m[INFO ][main][com.holderzone.holder.saas.store.deposit.service.impl.SendMessageServiceImpl.sendMessage:45][][�Ĵ���ط��Ͷ��� ] 
[35m[2025-06-23 14:31:03:816][m[INFO ][main][com.holderzone.holder.saas.store.deposit.service.impl.SendMessageServiceImpl.sendMessage:45][][�Ĵ���ط��Ͷ��� ] 
[35m[2025-06-23 14:31:03:846][m[INFO ][main][com.holderzone.holder.saas.store.deposit.service.impl.SendMessageServiceImpl.sendMessage:45][][�Ĵ���ط��Ͷ��� ] 
[35m[2025-06-23 14:31:03:847][m[INFO ][main][com.holderzone.holder.saas.store.deposit.service.impl.SendMessageServiceImpl.sendMessage:45][][�Ĵ���ط��Ͷ��� ] 
[35m[2025-06-23 14:31:03:908][m[INFO ][Thread-50][org.springframework.cloud.netflix.eureka.serviceregistry.EurekaServiceRegistry.deregister:65][][Unregistering application holder-saas-store-deposit with eureka with status DOWN] 
[35m[2025-06-23 14:31:03:909][m[INFO ][Thread-50][org.springframework.scheduling.concurrent.ExecutorConfigurationSupport.shutdown:208][][Shutting down ExecutorService 'taskScheduler'] 
[35m[2025-06-23 14:31:04:143][m[INFO ][Thread-50][org.springframework.scheduling.concurrent.ExecutorConfigurationSupport.shutdown:208][][Shutting down ExecutorService 'threadPool'] 
[35m[2025-06-23 14:31:04:145][m[INFO ][Thread-50][org.springframework.scheduling.concurrent.ExecutorConfigurationSupport.shutdown:208][][Shutting down ExecutorService 'asyncLogExecutor'] 
[35m[2025-06-23 14:31:04:147][m[INFO ][Thread-50][com.netflix.discovery.DiscoveryClient.shutdown:888][][Shutting down DiscoveryClient ...] 
[35m[2025-06-23 14:31:06:897][m[INFO ][DiscoveryClient-InstanceInfoReplicator-0][com.netflix.discovery.DiscoveryClient.register:826][][DiscoveryClient_HOLDER-SAAS-STORE-DEPOSIT/192.168.102.11:8924: registering service...] 
[35m[2025-06-23 14:31:07:161][m[INFO ][Thread-50][com.netflix.discovery.DiscoveryClient.unregister:922][][Unregistering ...] 
[35m[2025-06-23 14:31:12:184][m[INFO ][Thread-50][com.netflix.discovery.DiscoveryClient.shutdown:911][][Completed shut down of DiscoveryClient] 
