## 微信服务

微信服务包括微信授权，获取用户信息，及微信H5所需接口

* doc文件夹下为当前设计的数据库结构sql

## 微信授权流程

### 1、接受微信推送的component_verify_ticket。

#### 接口地址：/wx_open/receive_ticket

       该ticket用于获取三方平台的component_access_token，有效期为10分钟，
       
       微信每10分钟会向服务器推送一次verify_ticket，验证无错后更新本地ticket
       
### 2、获取三方平台component_access_token

#### 当前component_verify_ticket,component_access_token等信息存储于内存及数据库中，使用时获取顺序 内存--> 数据库 --> 微信服务器拉取

    component_access_token有效时间为2小时，过期后将不能使用，需使用最新的verify_ticket重新拉取
    
    每次调用开放平台代公众号开发功能时，需先调用WxStoreComponentConfigService.getAccessToken()方法，
    
    该方法会判断当前内存中componentAccessToken是否有效，并会自动更新accessToken。若不调用该方法，可能会
    
    出现内存中找不到accessToken或accessToken失效的异常
    
### 3、获取预授权码，生成授权链接，并重定向至该链接

    授权链接为：https://mp.weixin.qq.com/safe/bindcomponent?action=bindcomponent&auth_type=3&no_scan=1&component_appid=xxxx&pre_auth_code=xxxxx&redirect_uri=xxxx&auth_type=xxx&biz_appid=xxxx#wechat_redirect
    
    参数说明：
    
            参数	     是否必填	     参数说明
            
       component_appid      是	    第三方平台方appid
       
       pre_auth_code        是	    预授权码
       
       redirect_uri	        是	    回调URI（回调url拼接了两个穿透参数：brandGuid（品牌guid），enterpriseGuid（企业guid））
       
       auth_type	        是	    要授权的帐号类型：1则商户点击链接后，手机端仅展示公众号、2表示仅展示小程序，3表示公众号和小程序都展示。如果为未指定，则默认小程序和公众号都展示。第三方平台开发者可以使用本字段来控制授权的帐号类型。
       
       biz_appid	        否	    指定授权唯一的小程序或公众号
       
       注：auth_type、biz_appid两个字段互斥。
       
#### 调用生成链接并重定向至该链接的接口必须从某个页面调用，且该页面的域名，必须与接口的域名，以及微信后台配置的域名相同，否则重定向的微信页面将会报错

#### 开发环境使用nginx+内网穿透实现。内网穿透到nginx上，nginx转发到后台服务器，入口页静态页面在nginx根目录下。

### 4、公众号管理员扫码授权后，微信服务器回调接口url，url参数中返回authorization_code授权码，过期时间（10分钟）

       回调地址：/wx_open/query_auth
       
       回调方法中解析微信返回的授权码，以及绑定在url中的穿透参数
       
       穿透参数用于手动切库以及公众号绑定
       
#### 使用授权码，获取公众号基本信息，以及authorizer_access_token和authorizer_refresh_token

        授权方令牌（authorizer_access_token）失效时，可用刷新令牌（authorizer_refresh_token）获取新的令牌。
        
        请注意，此处token过期时间为两小时，每日存在刷新次数上限（文档中未给出上限次数），故系统处理方式为：
        
        使用公众号功能，可调用WxSaasMpService.getWxMpService(String appId)得到公众号对应的mpService，使用该mpService调用
        
        公众号功能。
        
#### 注：当前微信公众号开发，微信三方平台开发均使用weixinSDK，第三方平台：weixin-java-open, 微信公众号：weixin-java-mp

#### 正式环境上，sdk版本尽量使用正式版本。第三方平台sdk调用需注入WxThirdOpenConfig，公众号开发需初始化WxMpService。

     WxMpService初始化方式有两种：
     
     1、第三方平台代公众号开发时，使用WxSaasMpService.getWxMpService(String appId)初始化
     
     2、使用默认公众号开发时，可直接注入WxMpService