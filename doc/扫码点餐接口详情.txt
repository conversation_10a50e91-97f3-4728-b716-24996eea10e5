扫码点餐接口详情

简介
	扫码点餐分为小程序扫码点餐和h5扫码点餐，其下又分为正餐和快餐。他们代码后端代码都是一套，前端针对不同的业务场景做了一些区分和处理。

	正餐通常为下单后在一体机上进行最终支付，而快餐则是点完餐后立即支付，带走餐品或者等候叫号通知。

	为了满足各类商户的需求，根据使用场景的不同做了一些功能上的细微区别。比如：
	    小程序正餐商品有单品和多规格，优惠只有会员价或会员折扣；小程序快餐的商品要多一类套餐商品，有优惠券、满减满折活动、限时特价等优惠方式。
	    而h5点餐，不管正餐快餐，都有所有类型商品，功能上多了一个切换会员卡的功能，优惠上只有会员价。

	扫码点餐的很多数据都是在缓存里处理的，因此对商品的下单有很多的校验，需要去数据库里查询这个商品此时真实的状态。

	商品搜索功能用拆分关键字的方式将搜索的关键字拆分开来，使得可以匹配到含这些字的所有商品。

	整体逻辑主要是加入购物车刷新缓存，校验门店配置，校验商品，然后下单，根据配置选择自动接单或者推送消息，然后走一体机的加菜逻辑，进行支付，查询订单等等。


1.扫描二维码

测试环境小程序需要用赚餐小程序的扫码工具去扫描对应的二维码，而h5直接用微信扫码就行，两种方式对应的二维码不一样，线上都可以直接用微信扫。

小程序二维码解析结果：https://cydl1000.holderzone.com/cydl-distributor/142/ordering?t=2009281531195930006,1199

h5二维码解析结果：https://mch-sit.holderzone.cn/gateway/weixin/wx_mp/qr?t=2009281531195930006,1128

h5跳转了两次，原因是有些商户没有自己的公众号，需要通过跳转去获取openid和微信token

支付的appid是写死的，用的掌控者的帐号


2.商品列表查询
URL：/deal/menu/item_config
使用@Cacheable来缓存参数相同的结果，避免相同参数多次调用，减轻压力
目前只有小程序快餐点餐对接了限时特价，用字段和来源做了区分
异步查询，等待所有异步线程都查询到结果后再进行下一步操作
    商品列表：查询全部商品（区分菜谱模式）和分类，过滤商品，不分页，因为前端滑动商品分页要同步变化以及点击分类商品要同步定位到对应商品，此时如果向上滑动不知道高度，无法准确加载出商品，会错乱
    门店配置：查询后台的微信门店配置
    商品估清：查询门店的估清数据（区分菜谱模式）
    满减满折：通过会员持卡id（memberInfoCardGuid）查询满足满减满折活动，活动时段过滤，h5不查询满减满折，然后为确保点餐和结算活动一致，此处查询并缓存一份，后面结算查询缓存
    限时特价：h5不查询限时活动，通过营销活动统一查询接口查询新会员的限时特价活动，然后进行校验（指定会员，会员等级，会员标签，活动时间），然后存入缓存，后面用到直接查询缓存
数据准备：将估清数据存入缓存，查询会员权益，然后存入缓存
转换门店配置信息返给前端，将分类列表转换为前端使用的数据结构
商品处理
    商品列表
        上下架过滤
            判断单品是否微信上架，商品只显示微信上架的sku， 若所有sku均未在微信上架， 则剔除掉该商品
            判断套餐子菜是否上架微信，当前分组下只要有一个sku未在微信上架，则这个套餐不上架
        估清过滤
            传入估清商品，对商品进行估清过滤，传入估清商品
    限时特价活动
    商品满减满折活动
    活动互斥展示
查询是否需要附加费
    查询是否需要按人收的附加费， 前端需要弹窗选择人数
    如果是加菜，则需要查询加菜前的附加费（从缓存里查询，没有就等着下一步查库）
    看附加费是否存在，不存在查询最新附加费，然后存入缓存
设置返给前端的售卖模式和菜谱id

3.门店活动列表
用于展示用户该次扫码进入后可享受的营销活动
URL：/deal/menu/query_store_activity

4.商品详情
URL：/deal/menu/get_item_info

5.购物车加减菜
URL：/deal/menu/item_modify

6.购物车查询
URL：/deal/menu/shop_cart

7.金额计算
URL：/deal/pay/calculate

8.优惠券查询
URL：/deal/menu/

9.商品下单
URL：/deal/menu/submit_order

10.支付
    0元支付
    URL：/deal/pay/applet/zero

    小程序支付 (收益余额支付/储值金额支付)
    URL：/deal/pay/applet/member

    小程序支付 (微信支付)
    URL：/deal/pay/applet/wechat

11.小程序取消订单
URL：/deal/pay/applet/cancel
