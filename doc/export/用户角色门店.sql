select ur.account as 帐号, ur.name as 姓名, ur.role_name as 角色, ud.user_store as 门店 from 
(
	SELECT u.guid, u.account, u.name, group_concat(distinct r.name separator '，') as role_name
    FROM hss_user u
	left join hss_r_user_role ur on ur.user_guid = u.guid
	left join hss_role r on r.guid = ur.role_guid
    where u.is_deleted = 0
    group by u.guid, u.account, u.name
) ur
left join
(
	SELECT u.guid, group_concat(distinct CONCAT(o.name, '[', o.code, ']') separator '，') as user_store
    FROM hss_user u
	left join hss_user_data ud on ud.user_guid = u.guid
	left join hso_organization o on o.guid = ud.store_guid
    where u.is_deleted = 0
	group by u.guid
) ud
on ur.guid = ud.guid;
