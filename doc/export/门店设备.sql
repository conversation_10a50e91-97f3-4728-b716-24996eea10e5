select * from
(
select o.code, o.name, d.device_no, '一体机' as type from hso_organization o
left join hso_r_store_device d on o.guid = d.store_guid
where o.type = 2 and o.is_deleted = 0 and d.device_type = 3
union all
select o.code, o.name, d.device_no, '非一体机' as type from hso_organization o
left join hso_r_store_device d on o.guid = d.store_guid
where o.type = 2 and o.is_deleted = 0 and d.device_type != 3
) a
order by a.code * 1