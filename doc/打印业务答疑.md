# 打印业务解释

## 打印规则

1. 单个设备创建打印机规则：
    前台打印机-本机：至多创建一个
    前台打印机-局域网：无上限
    后厨打印机-局域网：无上限
    标签打印机-USB：至多创建一个
    标签打印机-局域网：无上限

2. 类型打印机指令可达性：
    本机打印机：
        当前设备指令可达
        why：无法跨设备发送打印指令，安卓IPC机制无法跨设备
    局域网打印机：
        所有设备指令可达
        why：任何设备均可以使用Socket发送指令给局域网打印机
    US打印机：
        当前设备指令可达
        why：无法跨设备发送打印指令，安卓IPC机制无法跨设备

3. 类型打印机设备可见性：
    本机打印机：
        当前设备可见
        why：
            若遵循“谁提交推送给谁”的逻辑，那么，就存在指令无法发送到指定设备的情况；
            若不遵循“谁提交推送给谁”，而是遵循“推送给创建设备”的逻辑，那么，就存在让人很困扰的场景，设备A（自带打印驱动未配置打印机）提交结账单，结果是设备B出单；
    局域网打印机：
        所有设备可见
        why：局域网打印机，所有设备指令可达。因此，所有设备可见；
    US打印机：
        当前设备可见
        why：同本机打印机；
    结论：
        仅包含局域网、不包含USB、本机打印机的业务打印机才能够所有设备可见

4. 业务打印机设备可见性：    
    前台打印机当前设备可见
        前台打印机用于为客人提供各类单据、打印经营日报
        why：前台打印机包含本机和局域网，根据“类型打印机设备可见性”规则，推导出前台打印机为当前设备可见
        scene:
            如果前台打印机所有设备可见，那么对于设备A提交的结帐单，会根据设备A和设备B各自配置的前台打印机打印出2张结帐单。
            如果是一个设备创建所有的前台打印机呢？答案是不能。浪费了自带打印驱动的一体机，且灵活性不足
            前台-局域网打印机是否能所有设备可见呢？答案是不能。本机、局域网打印机并没有什么区别，在查询打印机路由的时候，只根据单据类型，不关心其是本机还是局域网，安卓端才关心。
    后厨打印机所有设备可见
        后厨打印机用于厨师制作菜品
        why：后厨打印机仅包含局域网，根据“类型打印机设备可见性”规则，推导出后厨打印机为所有设备可见
        scene：
            单个厨房：如果后厨打印机当前设备可见，那么所有设备都需要创建自己可见的后厨打印机，后厨才能正常出单，繁琐。且对于POS等没有打印设置模块的设备而言，后厨无法出单；
            多个厨房：单个厨房+打印区域，逻辑同单个厨房。
    标签打印机当前设备可见
        标签打印机用于为商品提供粘贴的商品标签
        why：标签打印机包含USB和局域网，根据“类型打印机设备可见性”规则，推导出标签打印机为当前设备可见
        scene：如果标签打印机所有设备可见，那么对于设备A提交的标签单，会根据设备A和设备B各自配置的标签打印机打印出2张标签单。

5. 打印记录设备可见性：
    主机：所有设备前台、后厨、标签打印记录
    非主机：当前设备提交的前台、标签打印记录，所有设备后厨打印记录

6. 打印来源（替打）：
    AIO、AIO_P的所有前台、后厨、标签由自己打印
    MOBILE_P(aka:POS)的前台由自己打印，后厨、标签由主机替打
    MOBILE的所有前台、后厨、标签由主机替打
    PAD的所有前台、后厨、表爱你由主机替打
    打印来源适配由PrintRecordServiceImpl#adaptPrintSource方法完成

7. 虚拟打印机：
    MOILE_P(aka:POS)没有打印功能管理模块，因此需要为POS创建虚拟前台打印机（全配置）。后厨、标签单则由主机替打。
    虚拟打印机由PrintRecordServiceImpl#findPrinterAvailable方法的第一段代码完成

8. 打印单据、打印范围、打印区域之间的推导组合：打印单据推导出是何种打印范围，继而推导出具体的打印区域
    堂食：
        桌台区域+菜品清单
        桌台区域+预结单
        桌台区域+结账单
        桌台区域+并台结帐单
        桌台区域+转台单
        桌台区域+点菜单
        桌台区域+退菜单
        桌台区域+后厨转台单（所有能管理转台桌台所在区域的后厨打印机均能打印后厨转台单）
        桌台区域+标签单
    快餐：
        结账单
        点菜单
        退菜单
        标签单
    外卖：
        外卖单
        点菜单
        退菜单
        标签单
    NULL：
        储值单
        交接单
        报表单

    桌台区域的Assert由以下两个方法完成
    PrintRecordServiceImpl#getTradeMode，需提取到TradeModeUtils工具类中，重命名为getTradeModeByInvoiceType()
    PrintRecordServiceImpl#assertAreaGuidByTradeMode，需提取到TradeModeUtils工具类中，重命名为correctAreaGuidByTradeMode()

9. assertDeviceId，需更名为correctDeviceIdByBizType
    前台单：
        deviceId不为空
    后厨单：
        deviceId为空
    标签单：
        deviceId不为空
    代码实现如下：
        //InvoiceTypeEnum新增方法getBusinessType()
        switch (InvoiceTypeEnum.ofType(printDto.getInvoiceType()).getBusinessType()) {
            case FRONT_PRINTER:
                Asserts.create().notEmpty(printDto.getDeviceId(), "deviceId");
                break
            case FRONT_PRINTER:
                printDto.setDeviceId(null);
                break
            case FRONT_PRINTER:
                Asserts.create().notEmpty(printDto.getDeviceId(), "deviceId");
                break
        }
10. assertArrayOfItemGuid，需更名为getArrayOfItemGuidByBizType
    后厨转台单：
        返回null
    前台单：
        返回null
    后厨单：
        返回非空列表
    标签单：
        返回非空列表
    代码实现如下：
        //InvoiceTypeEnum新增方法getBusinessType()
        InvoiceTypeEnum invoiceType = InvoiceTypeEnum.ofType(printDto.getInvoiceType());
        if (invoiceType == InvoiceTypeEnum.TURN_TABLE_ITEM) {
            // 后厨转台单：二期暂时无关菜品，后期需加入菜品条件判断
            return null;
        }
        BusinessTypeEnum businessType = invoiceType.getBusinessType();
        switch (businessType) {
            case FRONT_PRINTER: {
                return null;
            }
            case KITCHEN_PRINTER: 
            case LABEL_PRINTER: {
                PrintBaseItemDTO basePrintBaseItemDto = (PrintBaseItemDTO) printDto;
                List<ItemRecord> itemRecords = basePrintBaseItemDto.getItemRecordList();
                if (CollectionUtils.isEmpty(itemRecords)) throw new BusinessException("菜品列表为空，无法打印");
                List<String> arrayOfItemGuid = getAllItemGuidIncludeSubItemRecord(itemRecords);
                Asserts.create().notEmpty(arrayOfItemGuid, "arrayOfItemGuid");
                return arrayOfItemGuid;
            }
        }

11. assertDeviceAndItemByInvoiceTypeThenReturnPrinterQuery，需更名为getPrinterQueryByCorrectParam(PrinterDTO printerDto, List<String> arrayOfItemGuid)
    return new PrinterQuery()
            .setStoreGuid(printDto.getStoreGuid())
            .setInvoiceType(printDto.getInvoiceType())
            .setAreaGuid(printDto.getAreaGuid())
            .setDeviceId(printDto.getDeviceId())
            .setArrayOfItemGuid(arrayOfItemGuid);

## 打印模板


## TOOD

//某个桌台的转台单需要打印到能管理该区域的后厨打印机出，所以是需要areaGuid的，所以，下面注释掉的TODO是不合理的。待删除。
//PrintRecordServiceImpl#getTradeMode方法中代码块'case TURN_TABLE_ITEM: return TradeModeEnum.DINE;'改为'case TURN_TABLE_ITEM: return TradeModeEnum.NULL;'

done PrintRecordServiceImpl#getTradeMode提取到TradeModeUtils工具类中，重命名为getTradeModeByInvoiceType()

done PrintRecordServiceImpl#assertAreaGuidByTradeMode提取到TradeModeUtils工具类中，重命名为correctAreaGuidByTradeMode()

done
// 下面这段代码出现了两次，需要提取方法，入参是printRecordDO和printerReadDO,返回值printRecordReadDO
// 添加打印记录缓存
PrintRecordReadDO printRecordReadDO = new PrintRecordReadDO();
printRecordReadDO.setRecordGuid(printRecordDO.getRecordGuid());
printRecordReadDO.setInvoiceType(printRecordDO.getInvoiceType());
printRecordReadDO.setPrintContent(printRecordDO.getPrintContent());
PrinterDO printerDO = new PrinterDO();
printerDO.setPrintPage(printerReadDO.getPrintPage());
printerDO.setPrintCount(printerReadDO.getPrintCount());
printerDO.setPrinterType(printerReadDO.getPrinterType());
printerDO.setPrinterIp(printerReadDO.getPrinterIp());
printerDO.setPrinterPort(printerReadDO.getPrinterPort());
printRecordReadDO.setPrinterDO(printerDO);
return printRecordReadDO;


done 将copyItemThenSaveBatch方法里面的部分代码提取到PrintRecordCloneUtils中，入参PrintBaseItemDTO basePrintBaseItemDto, List<PrinterReadDO> printers, List<PrintRecordDO> printRecordsToInsert, List<PrintRecordReadDO> printRecordsToCache，返回值void

        List<PrintRecordDO> printRecordsToInsert = new ArrayList<>();
        List<PrintRecordReadDO> printRecordsToCache = new ArrayList<>();

        PrintRecordCloneUtils.splitAndClone(basePrintBaseItemDto, printers, printRecordsToInsert, printRecordsToCache);

        // 保存PrintRecords
        saveBatch(printRecordsToInsert);
        contentCacheService.save(printRecordsToCache);

        return printRecordsToInsert.stream().map(PrintRecordDO::getRecordGuid).collect(Collectors.toList());


done 将splitItemByPrintCut及其子方法splitItemByAll、splitItemByItem、splitItemByItemType、splitItemByCountOne提取到PrintCutUtils中    

done 将buildPrintRecord方法提取到PrintRecordCloneUtils中

done 将do2Dto及其自方法handlerRecordDtoList提取到PrintResponseUtils中

done 将getOrderByPerDO提取到PrintResponseUtils中，入参PrintRecordReadDO printRecordReadDO， PrintComponentFactory printComponentFactory

done 将getContentByPerDO提取到PrintResponseUtils中，入参PrintRecordReadDO printRecordReadDO， PrintTemplateFactory printTemplateFactory3，同时将其子方法getPrintContent一并提取过去，最后为这两个方法添加Depre注解

done 将adaptPrintSource, correctAreaGuid, correctDeviceId, correctArrayOfItemGuid, removeItemThatNumberIsZeroIfNecessary, findPrinterAvailable, getPrinterQueryByCorrectParam提取到PrinterRoutingService、printerRoutingServiceImpl中

问冬梅：？？？
deletePrinterOfTheDevice是否需要删除该设备创建的后厨打印机，目前是不删除，只有当删除最后一个门店设备时，会调用deletePrinterOfTheStore删除所有打印机（包括之前其他设备未删除的后厨打印机）。