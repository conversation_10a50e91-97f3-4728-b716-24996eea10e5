<?xml version="1.0" encoding="UTF-8"?>
<jmeterTestPlan version="1.2" properties="2.6" jmeter="2.11.20151206">
  <hashTree>
    <TestPlan guiclass="TestPlanGui" testclass="TestPlan" testname="测试计划" enabled="true">
      <stringProp name="TestPlan.comments"></stringProp>
      <boolProp name="TestPlan.functional_mode">false</boolProp>
      <boolProp name="TestPlan.serialize_threadgroups">false</boolProp>
      <elementProp name="TestPlan.user_defined_variables" elementType="Arguments" guiclass="ArgumentsPanel" testclass="Arguments" testname="用户定义的变量" enabled="true">
        <collectionProp name="Arguments.arguments"/>
      </elementProp>
      <stringProp name="TestPlan.user_define_classpath"></stringProp>
    </TestPlan>
    <hashTree>
      <ThreadGroup guiclass="ThreadGroupGui" testclass="ThreadGroup" testname="线程组" enabled="true">
        <stringProp name="ThreadGroup.on_sample_error">continue</stringProp>
        <elementProp name="ThreadGroup.main_controller" elementType="LoopController" guiclass="LoopControlPanel" testclass="LoopController" testname="循环控制器" enabled="true">
          <boolProp name="LoopController.continue_forever">false</boolProp>
          <intProp name="LoopController.loops">-1</intProp>
        </elementProp>
        <stringProp name="ThreadGroup.num_threads">200</stringProp>
        <stringProp name="ThreadGroup.ramp_time">5</stringProp>
        <longProp name="ThreadGroup.start_time">1546061976000</longProp>
        <longProp name="ThreadGroup.end_time">1546061976000</longProp>
        <boolProp name="ThreadGroup.scheduler">true</boolProp>
        <stringProp name="ThreadGroup.duration">30</stringProp>
        <stringProp name="ThreadGroup.delay"></stringProp>
      </ThreadGroup>
      <hashTree>
        <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP信息头管理器" enabled="true">
          <collectionProp name="HeaderManager.headers">
            <elementProp name="" elementType="Header">
              <stringProp name="Header.name">Content-Type</stringProp>
              <stringProp name="Header.value">application/json</stringProp>
            </elementProp>
            <elementProp name="" elementType="Header">
              <stringProp name="Header.name">userInfo</stringProp>
              <stringProp name="Header.value">%7B%22userGuid%22%3A%226480756476603191298%22%2C%22enterpriseGuid%22%3A%226480755310876715009%22%2C%22enterpriseName%22%3A%22%E6%89%93%E5%8D%B0%E4%BC%81%E4%B8%9A1218%22%2C%22name%22%3A%22tcw%22%2C%22tel%22%3A%*************%22%2C%22storeGuid%22%3A%226480756384945597441%22%2C%22storeName%22%3A%22%E6%89%93%E5%8D%B0%E9%97%A8%E5%BA%971218%22%2C%22account%22%3A%********%22%2C%22storeNo%22%3A%*********%22%7D</stringProp>
            </elementProp>
          </collectionProp>
        </HeaderManager>
        <hashTree/>
        <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="HTTP请求" enabled="true">
          <boolProp name="HTTPSampler.postBodyRaw">true</boolProp>
          <elementProp name="HTTPsampler.Arguments" elementType="Arguments">
            <collectionProp name="Arguments.arguments">
              <elementProp name="" elementType="HTTPArgument">
                <boolProp name="HTTPArgument.always_encode">false</boolProp>
                <stringProp name="Argument.value">{&quot;invoiceType&quot;:&quot;3&quot;,&quot;enterpriseGuid&quot;:&quot;6480755310876715009&quot;,&quot;storeGuid&quot;:&quot;6480756384945597441&quot;,&quot;storeName&quot;:&quot;打印门店1218&quot;,&quot;printUid&quot;:&quot;T6484653701184094209&quot;,&quot;operatorStaffGuid&quot;:&quot;6480756476603191298&quot;,&quot;operatorStaffName&quot;:&quot;tcw&quot;,&quot;createTime&quot;:0,&quot;deviceId&quot;:&quot;6481150069296058369&quot;,&quot;printSource&quot;:&quot;AIO&quot;,&quot;itemRecordList&quot;:[{&quot;itemGuid&quot;:&quot;6480757087524632577&quot;,&quot;itemName&quot;:&quot;分类1单品1&quot;,&quot;itemTypeGuid&quot;:&quot;6480756808346515457&quot;,&quot;itemTypeName&quot;:&quot;分类1&quot;,&quot;price&quot;:0.01,&quot;number&quot;:1.0,&quot;unit&quot;:&quot;份&quot;,&quot;subtotal&quot;:0.01,&quot;asWeight&quot;:false,&quot;remark&quot;:&quot;&quot;,&quot;subList&quot;:[],&quot;propertyPrice&quot;:0,&quot;packageTotal&quot;:0.0}],&quot;markName&quot;:&quot;牌号&quot;,&quot;markNo&quot;:&quot;#9&quot;,&quot;orderNo&quot;:&quot;T6484653701184094209&quot;,&quot;personNumber&quot;:1,&quot;cashier&quot;:&quot;tcw&quot;,&quot;checkOutTime&quot;:1546061924000,&quot;total&quot;:0.01,&quot;reduceList&quot;:[],&quot;payAble&quot;:0.01,&quot;actuallyPay&quot;:0.01,&quot;payRecordList&quot;:[{&quot;payName&quot;:&quot;现金支付&quot;,&quot;amount&quot;:&quot;0.01&quot;}],&quot;storeAddress&quot;:&quot;高新区&quot;,&quot;tel&quot;:&quot;17302864792&quot;,&quot;tradeMode&quot;:1}</stringProp>
                <stringProp name="Argument.metadata">=</stringProp>
              </elementProp>
            </collectionProp>
          </elementProp>
          <stringProp name="HTTPSampler.domain">*************</stringProp>
          <stringProp name="HTTPSampler.port">8917</stringProp>
          <stringProp name="HTTPSampler.connect_timeout"></stringProp>
          <stringProp name="HTTPSampler.response_timeout"></stringProp>
          <stringProp name="HTTPSampler.protocol"></stringProp>
          <stringProp name="HTTPSampler.contentEncoding">UTF-8</stringProp>
          <stringProp name="HTTPSampler.path">/print/send</stringProp>
          <stringProp name="HTTPSampler.method">POST</stringProp>
          <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
          <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
          <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
          <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
          <stringProp name="HTTPSampler.implementation">HttpClient4</stringProp>
          <boolProp name="HTTPSampler.monitor">false</boolProp>
          <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
        </HTTPSamplerProxy>
        <hashTree/>
        <ResultCollector guiclass="ViewResultsFullVisualizer" testclass="ResultCollector" testname="察看结果树" enabled="true">
          <boolProp name="ResultCollector.error_logging">false</boolProp>
          <objProp>
            <name>saveConfig</name>
            <value class="SampleSaveConfiguration">
              <time>true</time>
              <latency>true</latency>
              <timestamp>true</timestamp>
              <success>true</success>
              <label>true</label>
              <code>true</code>
              <message>true</message>
              <threadName>true</threadName>
              <dataType>true</dataType>
              <encoding>false</encoding>
              <assertions>true</assertions>
              <subresults>true</subresults>
              <responseData>false</responseData>
              <samplerData>false</samplerData>
              <xml>false</xml>
              <fieldNames>false</fieldNames>
              <responseHeaders>false</responseHeaders>
              <requestHeaders>false</requestHeaders>
              <responseDataOnError>false</responseDataOnError>
              <saveAssertionResultsFailureMessage>false</saveAssertionResultsFailureMessage>
              <assertionsResultsToSave>0</assertionsResultsToSave>
              <bytes>true</bytes>
            </value>
          </objProp>
          <stringProp name="filename"></stringProp>
        </ResultCollector>
        <hashTree/>
        <ResultCollector guiclass="StatVisualizer" testclass="ResultCollector" testname="聚合报告" enabled="true">
          <boolProp name="ResultCollector.error_logging">false</boolProp>
          <objProp>
            <name>saveConfig</name>
            <value class="SampleSaveConfiguration">
              <time>true</time>
              <latency>true</latency>
              <timestamp>true</timestamp>
              <success>true</success>
              <label>true</label>
              <code>true</code>
              <message>true</message>
              <threadName>true</threadName>
              <dataType>true</dataType>
              <encoding>false</encoding>
              <assertions>true</assertions>
              <subresults>true</subresults>
              <responseData>false</responseData>
              <samplerData>false</samplerData>
              <xml>false</xml>
              <fieldNames>false</fieldNames>
              <responseHeaders>false</responseHeaders>
              <requestHeaders>false</requestHeaders>
              <responseDataOnError>false</responseDataOnError>
              <saveAssertionResultsFailureMessage>false</saveAssertionResultsFailureMessage>
              <assertionsResultsToSave>0</assertionsResultsToSave>
              <bytes>true</bytes>
            </value>
          </objProp>
          <stringProp name="filename"></stringProp>
        </ResultCollector>
        <hashTree/>
      </hashTree>
    </hashTree>
  </hashTree>
</jmeterTestPlan>
