# SpringBoot 快速支持国际化i18n

[传送门](https://www.jianshu.com/p/e2eae08f3255)，很不错的教程

## 语言切换

很多新人配置好之后不懂得如何切换国际化语言，其实很简单，由于在前面已经配置了拦截器LocaleChangeInterceptor ，此时我们只需在任意请求中附上语言参数lang即可，当然也通过AJAX来快速切换。
例如：
默认英语：http://127.0.0.1:8080?lang=en_US
中文简体：http://127.0.0.1:8080?lang=zh_CN
中文繁体：http://127.0.0.1:8080?lang=zh_TW

注意：单次生效，并不是切换了后，就一直是切换后的语言；所以需要在每个请求都加上?lang=xxx

## 巴塞罗那解决办法：

1. 设置
    localeResolver.setDefaultLocale(Locale.US);
    
2. 且
    spring:
      messages:
        basename: static/i18n/messages # 不要写成messages_en_US