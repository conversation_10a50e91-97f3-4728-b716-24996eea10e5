CREATE TABLE `hsp_printer_format` (
  `id` bigint(64) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `store_guid` varchar(50) NOT NULL COMMENT '门店GUID',
  `invoice_type` tinyint(8) NOT NULL COMMENT '单据类型',
  `format_json_string` text NOT NULL COMMENT '格式Json串',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_store_invoice` (`store_guid`,`invoice_type`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COMMENT='单据格式'