SELECT * FROM hsp_print_6480755310876715009_db.hsp_print_record order by gmt_create desc limit 10;

show index from hsp_print_6480755310876715009_db.hsp_print_record;
show index from hsp_print_6480755310876715009_db.hsp_printer;

        explain select count(*)
        from hsp_print_6480755310876715009_db.hsp_print_record r
        inner join hsp_print_6480755310876715009_db.hsp_printer p on p.guid=r.printer_guid
        where r.print_status=2 and r.is_deleted = 0 and p.device_id='6481150069296058369';

        explain select count(*)
        from hsp_print_6480755310876715009_db.hsp_print_record r
        inner join hsp_print_6480755310876715009_db.hsp_printer p on p.guid=r.printer_guid
        and r.print_status=2 and r.is_deleted = 0 and p.device_id='6481150069296058369';

        explain select count(*)
        from hsp_print_6480755310876715009_db.hsp_printer p
        inner join hsp_print_6480755310876715009_db.hsp_print_record r on p.guid=r.printer_guid
        and r.print_status=2 and r.is_deleted = 0 and p.device_id='6481150069296058369';

        explain select count(*)
        from hsp_print_6480755310876715009_db.hsp_printer p
        inner join hsp_print_6480755310876715009_db.hsp_print_record r on p.guid=r.printer_guid
        where r.print_status=2 and r.is_deleted = 0 and p.device_id='6481150069296058369';

alter table hsp_print_6480755310876715009_db.hsp_print_record add index idx_printer_guid(printer_guid);
alter table hsp_print_6480755310876715009_db.hsp_printer add index idx_guid(guid);
alter table hsp_print_6480755310876715009_db.hsp_print_record drop index idx_printer_guid;
alter table hsp_print_6480755310876715009_db.hsp_printer drop index idx_guid;

alter table hsp_print_6480755310876715009_db.hsp_printer add index idx_device_id(device_id);
alter table hsp_print_6480755310876715009_db.hsp_printer drop index idx_device_id;

alter table hsp_print_6480755310876715009_db.hsp_print_record add index idx_print_status(print_status);
alter table hsp_print_6480755310876715009_db.hsp_printer drop index idx_print_status;

alter table hsp_print_6480755310876715009_db.hsp_print_record add index idx_print_status_is_deleted(print_status, is_deleted);
alter table hsp_print_6480755310876715009_db.hsp_print_record drop index idx_print_status_is_deleted;

alter table hsp_print_6480755310876715009_db.hsp_print_record add index idx_printer_guid_print_status_is_deleted(printer_guid, print_status, is_deleted);
alter table hsp_print_6480755310876715009_db.hsp_printer add index idx_guid_device_id(guid, device_id);
alter table hsp_print_6480755310876715009_db.hsp_print_record drop index idx_printer_guid_print_status_is_deleted;
alter table hsp_print_6480755310876715009_db.hsp_printer drop index idx_guid_device_id;

alter table hsp_print_6480755310876715009_db.hsp_print_record add index idx_print_status_is_deleted_printer_guid(print_status, is_deleted, printer_guid);
alter table hsp_print_6480755310876715009_db.hsp_printer add index idx_guid_device_id(guid, device_id);
alter table hsp_print_6480755310876715009_db.hsp_print_record drop index idx_print_status_is_deleted_printer_guid;
alter table hsp_print_6480755310876715009_db.hsp_printer drop index idx_guid_device_id;

alter table hsp_print_6480755310876715009_db.hsp_print_record add index idx_printer_guid(printer_guid);
alter table hsp_print_6480755310876715009_db.hsp_print_record add index idx_print_status_is_deleted(print_status, is_deleted);
alter table hsp_print_6480755310876715009_db.hsp_printer add index idx_guid_device_id(guid, device_id);
alter table hsp_print_6480755310876715009_db.hsp_print_record drop index idx_printer_guid;
alter table hsp_print_6480755310876715009_db.hsp_print_record drop index idx_print_status_is_deleted;
alter table hsp_print_6480755310876715009_db.hsp_printer drop index idx_guid_device_id;