/*
##########################order_record1.5#####################################
*/

ALTER TABLE `hsw_weixin_order_record`
ADD COLUMN `item_name`  text NULL COMMENT '商品名称' AFTER `actually_pay_fee`,
ADD COLUMN `brand_name`  varchar(255) NULL AFTER `order_guid`,
ADD COLUMN `table_code`  varchar(50) NULL AFTER `user_record_guid`,
ADD COLUMN `area_name`  varchar(50) NULL AFTER `table_code`,
ADD COLUMN `store_name`  varchar(50) NULL AFTER `area_name`,
ADD COLUMN `un_member_price`  decimal(10,2) NULL AFTER `gmt_modified`,
ADD COLUMN `is_login`  tinyint(1) NULL COMMENT '是否登录会员' AFTER `item_name`,
ADD COLUMN `member_info_card_guid`  varchar(50) NULL COMMENT '会员持卡id，有可能为null' AFTER `is_login`,
ADD COLUMN `volume_code`  varchar(50) NULL COMMENT '优惠券券码' AFTER `member_info_card_guid`,
ADD COLUMN `user_count`  int(3) NULL DEFAULT 0 COMMENT '就餐人数' AFTER `volume_code`;


/*
###########################order_item1.5#############################################
*/

DROP TABLE IF EXISTS `hsw_weixin_order_item`;
CREATE TABLE `hsw_weixin_order_item` (
  `guid` char(50) NOT NULL COMMENT '全局唯一主键',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 0：false,1:true',
  `order_guid` char(50) NOT NULL COMMENT '订单guid',
  `open_id` varchar(128) NOT NULL DEFAULT '0' COMMENT '用户微信公众号openId',
  `merchant_guid` char(50) DEFAULT NULL COMMENT '订单批次id',
  `order_record_guid` char(50) DEFAULT NULL COMMENT '用户订单记录id',
  `item_guid` varchar(50) NOT NULL COMMENT '商品guid',
  `item_name` varchar(50) NOT NULL COMMENT '商品名称',
  `picture_url` varchar(255) DEFAULT NULL COMMENT '商品图片',
  `code` varchar(50) DEFAULT NULL COMMENT '商品编号',
  `item_type_guid` varchar(50) NOT NULL COMMENT '商品类别guid',
  `item_type_name` varchar(50) DEFAULT NULL COMMENT '商品类别名称',
  `item_type` int(11) DEFAULT '0' COMMENT '商品类型(1.套餐主项，2.规格，3.称重，4.单品 )',
  `subgroup` text COMMENT '套餐分组json',
  `sku_guid` varchar(50) NOT NULL DEFAULT '' COMMENT '商品的规格',
  `sku_name` varchar(20) NOT NULL DEFAULT '' COMMENT '规格名称',
  `sku_price` decimal(15,2) NOT NULL COMMENT 'sku价格',
  `sku_unit` varchar(50) NOT NULL DEFAULT '' COMMENT '计数单位',
  `original_price` decimal(15,2) DEFAULT NULL COMMENT '商品小计原价',
  `member_price` decimal(15,2) DEFAULT NULL COMMENT '商品小计会员价格',
  `has_attr` int(11) unsigned DEFAULT NULL COMMENT '是否有属性（0：否，1：是）',
  `attr_total` decimal(15,2) DEFAULT '0.00' COMMENT '属性总价',
  `current_count` decimal(12,3) DEFAULT '0.000' COMMENT '当前数量',
  `is_whole_discount` int(11) unsigned DEFAULT NULL COMMENT '是否参与整单折扣(0：否，1：是)',
  `is_member_discount` int(11) unsigned DEFAULT NULL COMMENT '是否参与会员折扣（0：否，1：是）',
  `remark` varchar(255) DEFAULT NULL COMMENT '商品备注',
  `create_staff_guid` varchar(50) DEFAULT NULL COMMENT '创建操作人guid',
  `create_staff_name` varchar(50) DEFAULT NULL COMMENT '创建操作人guid',
  `item_attr` text COMMENT '属性jsonarray',
  `min_order_num` decimal(15,2) DEFAULT NULL COMMENT '起卖数',
  PRIMARY KEY (`guid`),
  KEY `idx_order_guid` (`order_guid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单商品';


ALTER TABLE `hsw_weixin_order_item`
MODIFY COLUMN `order_guid`  char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '订单guid' AFTER `is_del`,
MODIFY COLUMN `item_name`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品名称' AFTER `item_guid`;
/*
##########################merchant1.5#############################################
*/

ALTER TABLE `hsw_weixin_merchant_order`
MODIFY COLUMN `total_price`  decimal(10,2) NULL DEFAULT NULL COMMENT '商品小计' AFTER `is_del`,
MODIFY COLUMN `open_id`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户id' AFTER `total_price`,
MODIFY COLUMN `nick_name`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户昵称' AFTER `open_id`,
MODIFY COLUMN `actual_guests_no`  int(11) NULL DEFAULT NULL COMMENT '就餐人数' AFTER `nick_name`,
MODIFY COLUMN `area_guid`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL AFTER `actual_guests_no`,
MODIFY COLUMN `area_name`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '区域名称' AFTER `area_guid`,
MODIFY COLUMN `dining_table_guid`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '桌台id' AFTER `area_name`,
MODIFY COLUMN `table_code`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '桌台号' AFTER `dining_table_guid`,
MODIFY COLUMN `order_state`  tinyint(2) NULL DEFAULT NULL COMMENT '0:待处理，1：接单，2：拒单，3：未结帐，4：已结账,5:已做废, 8订单已失效,9.已退菜' AFTER `table_code`,
MODIFY COLUMN `trade_mode`  tinyint(2) NULL DEFAULT NULL COMMENT '用餐类型，0：正餐：1：快餐' AFTER `denial_reason`,
MODIFY COLUMN `item_count`  int(11) NULL DEFAULT NULL COMMENT '商品数量' AFTER `checkout_time`,
ADD COLUMN `store_name`  varchar(50) NULL AFTER `store_guid`,
ADD COLUMN `brand_guid`  char(50) NULL COMMENT '品牌id' AFTER `store_name`,
ADD COLUMN `brand_name`  char(50) NULL COMMENT '品牌名称' AFTER `brand_guid`,
ADD COLUMN `order_no`  varchar(50) NULL COMMENT '订单号' AFTER `order_guid`,
ADD COLUMN `order_record_guid`  varchar(50) NULL COMMENT '订单号' AFTER `order_guid`;

ALTER TABLE `hsw_weixin_order_record`
MODIFY COLUMN `merchant_guid`  varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '订单批次id' AFTER `guid`;

ALTER TABLE `hsw_weixin_order_item`
ADD COLUMN `sku_member_price`  decimal(10,2) NULL COMMENT '会员价' AFTER `sku_price`;
