alter table hst_takeout_dish RENAME TO hst_takeout_item;
alter table hst_takeout_item change column dish_sku item_sku VARCHAR(50) NULL DEFAULT NULL COMMENT '商品SKU';
alter table hst_takeout_item change column dish_guid item_guid VARCHAR(50) NULL DEFAULT NULL COMMENT '商品GUID';
alter table hst_takeout_item change column dish_name item_name VARCHAR(50) NULL DEFAULT NULL COMMENT '商品名称';
alter table hst_takeout_item change column dish_name_platform item_name_platform VARCHAR(50) NULL DEFAULT NULL COMMENT '商品名称+(平台)';
alter table hst_takeout_item change column dish_code item_code VARCHAR(50) NULL DEFAULT NULL COMMENT '商品编码';
alter table hst_takeout_item change column dish_unit item_unit VARCHAR(50) NULL DEFAULT NULL COMMENT '商品单位';
alter table hst_takeout_item change column dish_price item_price DECIMAL(10,2) NULL DEFAULT NULL COMMENT '商品单价';
alter table hst_takeout_item change column dish_count item_count DECIMAL(10,2) NULL DEFAULT NULL COMMENT '商品数量';
alter table hst_takeout_item change column dish_total item_total DECIMAL(10,2) NULL DEFAULT NULL COMMENT '商品总额';
alter table hst_takeout_item change column dish_spec item_spec VARCHAR(50) NULL DEFAULT NULL COMMENT '商品规格';
alter table hst_takeout_item change column dish_property item_property VARCHAR(50) NULL DEFAULT NULL COMMENT '商品属性';

alter table hst_takeout_package change column dish_guid item_guid VARCHAR(50) NULL DEFAULT NULL COMMENT '套餐子菜品GUID';
alter table hst_takeout_package change column dish_count item_count DECIMAL(10,2) NULL DEFAULT NULL COMMENT '套餐子菜品数量';
alter table hst_takeout_package change column dish_unit_count item_unit_count DECIMAL(10,2) NULL DEFAULT NULL COMMENT '套餐子菜品单位数量';

alter table hst_takeout_practice change column dish_guid item_guid VARCHAR(50) NULL DEFAULT NULL COMMENT '菜品GUID';

alter table hst_takeout_order change column dish_count item_count DECIMAL(10,2) NULL DEFAULT NULL COMMENT '菜品数量';
alter table hst_takeout_order change column dish_total item_total DECIMAL(10,2) NULL DEFAULT NULL COMMENT '菜品消费合计';
alter table hst_takeout_order change column customer_refund_dish customer_refund_item VARCHAR(50) NULL DEFAULT NULL COMMENT '顾客退菜字符串拼接，有时间时将该字段提取为退菜表';

alter table hst_takeout_item drop index uk_dish_guid;
alter table hst_takeout_item add unique uk_item_guid(item_guid);
alter table hst_takeout_item drop index idx_dish_sku;
alter table hst_takeout_item add index idx_item_sku(item_sku);

alter table hst_takeout_practice drop index idx_dish_guid;
alter table hst_takeout_practice add index idx_item_guid(item_guid);

drop table if exists hst_ele_auth;
drop table if exists hst_mt_auth;
drop table if exists hst_mt_privacy;
