-- MySQL dump 10.13  Distrib 5.7.20, for Linux (x86_64)
--
-- Host: ***************    Database: hst_takeaway_db
-- ------------------------------------------------------
-- Server version	5.7.20-log

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `hst_ele_shop_auth`
--

DROP TABLE IF EXISTS `hst_ele_shop_auth`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `hst_ele_shop_auth` (
  `id` bigint(11) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(11) DEFAULT NULL COMMENT '饿了么店铺所在商户的主键',
  `ele_shop_id` bigint(20) DEFAULT NULL COMMENT '饿了么店铺Id',
  `ele_shop_name` varchar(100) DEFAULT NULL COMMENT '饿了么店铺名称',
  `ele_open_id` varchar(50) DEFAULT NULL COMMENT '饿了么店铺openId',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '店铺创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '店铺更新时间',
  `enterprise_guid` varchar(50) DEFAULT NULL COMMENT 'erp商户id',
  `store_guid` varchar(50) DEFAULT NULL COMMENT 'erp门店id',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`) USING BTREE,
  KEY `idx_store_guid` (`store_guid`) USING BTREE,
  KEY `idx_ele_open_id` (`ele_open_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8 COMMENT='饿了么店铺表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `hst_ele_token_auth`
--

DROP TABLE IF EXISTS `hst_ele_token_auth`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `hst_ele_token_auth` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `access_token` varchar(50) DEFAULT NULL COMMENT '饿了么token的访问token',
  `refresh_token` varchar(50) DEFAULT NULL COMMENT '商户token刷新token时用',
  `token_type` varchar(20) DEFAULT NULL COMMENT 'token类型',
  `expires` bigint(20) DEFAULT NULL COMMENT 'token有效时间，以秒为单位',
  `expire_time` datetime DEFAULT NULL COMMENT 'token过期具体时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'token创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'token更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_access_token` (`access_token`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8 COMMENT='饿了么token表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `hst_ele_user_auth`
--

DROP TABLE IF EXISTS `hst_ele_user_auth`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `hst_ele_user_auth` (
  `id` bigint(11) NOT NULL AUTO_INCREMENT,
  `ele_user_id` bigint(20) DEFAULT NULL COMMENT '饿了么商户Id',
  `ele_user_name` varchar(50) DEFAULT NULL COMMENT '商户账号名称',
  `enterprise_guid` varchar(50) DEFAULT NULL COMMENT '智慧门店企业id',
  `token_id` bigint(11) DEFAULT NULL COMMENT 'token主键id',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '商户更新时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `store_guid` varchar(36) DEFAULT NULL COMMENT 'erp门店id',
  PRIMARY KEY (`id`),
  KEY `idx_ele_user_id` (`ele_user_id`) USING BTREE,
  KEY `idx_store_guid` (`store_guid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8 COMMENT='饿了么平台user商户表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `hst_meituan_epoi_auth`
--

DROP TABLE IF EXISTS `hst_meituan_epoi_auth`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `hst_meituan_epoi_auth` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `epoi_id` varchar(50) DEFAULT NULL COMMENT 'erp门店ID',
  `auth_token` varchar(200) DEFAULT NULL COMMENT '平台认证erp门店的token',
  `refresh_token` varchar(200) DEFAULT NULL COMMENT '刷新的token',
  `business_id` tinyint(2) DEFAULT NULL COMMENT 'erp门店开通的业务类型',
  `expire_time` datetime DEFAULT NULL COMMENT 'token时效',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '修改时间',
  `state` tinyint(2) DEFAULT NULL COMMENT '数据状态',
  `enterprise_guid` varchar(50) DEFAULT NULL COMMENT '商户id',
  `meituan_store_guid` varchar(50) DEFAULT NULL COMMENT '美团门店id',
  `meituan_store_name` varchar(36) DEFAULT NULL COMMENT '美团门店名称',
  PRIMARY KEY (`id`),
  KEY `idx_epoi_id` (`epoi_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=82 DEFAULT CHARSET=utf8 COMMENT='外卖平台门店认证信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `hst_meituan_privacy_phone`
--

DROP TABLE IF EXISTS `hst_meituan_privacy_phone`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `hst_meituan_privacy_phone` (
  `id` bigint(64) NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `order_id` varchar(32) DEFAULT NULL COMMENT '操作结果',
  `order_id_view` varchar(32) DEFAULT NULL COMMENT '订单展示号',
  `day_seq` varchar(32) DEFAULT NULL COMMENT '门店下的订单流水号',
  `real_phone_number` varchar(12) DEFAULT NULL COMMENT '真实手机号',
  `e_poi_id` varchar(32) DEFAULT NULL COMMENT 'ERP门店id',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_id` (`order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='美团隐私号拉取结果表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `hst_order`
--

DROP TABLE IF EXISTS `hst_order`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `hst_order` (
  `id` bigint(30) NOT NULL AUTO_INCREMENT,
  `msg_type` varchar(20) DEFAULT NULL COMMENT '2010:拒单，2011:确认接单， 2030:不同意退单，2031:同意退单 ',
  `takeaway_order_guid` varchar(36) DEFAULT NULL COMMENT 'oder主键',
  `enterprise_id` varchar(36) DEFAULT NULL COMMENT '商家标识',
  `store_id` varchar(36) DEFAULT NULL COMMENT '门店标识',
  `order_id` varchar(36) DEFAULT NULL COMMENT '外卖订单ID',
  `order_sn` varchar(36) DEFAULT NULL COMMENT '外卖订单流水号',
  `day_sn` varchar(36) DEFAULT NULL COMMENT '日流水',
  `create_order_time_stamp` datetime DEFAULT NULL COMMENT '订单创建（下单）时间戳',
  `is_reserve` tinyint(4) DEFAULT NULL COMMENT '是否是预订单 0=否 1=是',
  `activation_time_stamp` datetime DEFAULT NULL COMMENT '预订单生效/激活时间,非预订单，同下单时间',
  `order_type` tinyint(4) DEFAULT NULL COMMENT '订单类型,0=外卖订单  1=微信订单  2=其他订单',
  `order_sub_type` tinyint(10) DEFAULT NULL COMMENT '订单子类.OrderType=0：0=美团  1=饿了么  2=百度  3=京东 ;OrderType=1：0=扫码订单  1=微信预订单',
  `update_time_stamp` datetime DEFAULT NULL COMMENT '订单状态最后更新时间',
  `order_status` int(5) DEFAULT NULL COMMENT '订单状态',
  `first_order` tinyint(4) DEFAULT NULL COMMENT '首单用户 0=否 1=是 -1=未知"',
  `customer_number` int(10) DEFAULT NULL COMMENT '用餐人数',
  `customer_name` varchar(20) DEFAULT NULL COMMENT '顾客姓名',
  `customer_address` varchar(50) DEFAULT NULL COMMENT '顾客地址',
  `customer_phone` varchar(50) DEFAULT NULL COMMENT '顾客手机号',
  `latitude` varchar(30) DEFAULT NULL COMMENT '配送地址纬度',
  `longitude` varchar(30) DEFAULT NULL COMMENT '配送地址经度',
  `shipper_name` varchar(20) DEFAULT NULL COMMENT '配送员姓名',
  `shipper_phone` varchar(50) DEFAULT NULL COMMENT '配送员手机号',
  `is_third_shipper` tinyint(4) DEFAULT NULL COMMENT '是否三方托运  0=否  1=是  -1=未知',
  `delivery_time_stamp` datetime DEFAULT NULL COMMENT '配送时间要求  0=无时间要求  >0=具体要求的配送时间',
  `has_invoiced` tinyint(4) DEFAULT NULL COMMENT '是否有发票 0=无发票  1=有发票',
  `invoice_type` tinyint(4) DEFAULT NULL COMMENT '发票类型 0=个人 1=企业  -1=未知',
  `invoice_title` varchar(100) DEFAULT NULL COMMENT '发票抬头',
  `total` decimal(10,2) DEFAULT NULL COMMENT '总价,包括：菜品(原价)+餐盒+配送',
  `shipping_fee_total` decimal(10,2) DEFAULT NULL COMMENT '配送费',
  `dishes_total` decimal(10,2) DEFAULT NULL COMMENT '菜品消费合计(不含餐盒费)',
  `package_fee_total` decimal(10,2) DEFAULT NULL COMMENT '餐盒费',
  `service_fee_rate` decimal(10,2) DEFAULT NULL COMMENT '服务费率(平台抽成比例)0.15=15%"',
  `service_fee` decimal(10,2) DEFAULT NULL COMMENT '服务费(平台抽成费用)',
  `discount_total` decimal(10,2) DEFAULT NULL COMMENT '折扣合计 EnterpriseDiscount+PlatformDiscount+OtherDiscount',
  `enterprise_discount` decimal(10,2) DEFAULT NULL COMMENT '商家承担的折扣部分',
  `platform_discount` decimal(10,2) DEFAULT NULL COMMENT '外卖平台承担的折扣部分',
  `other_discount` decimal(10,2) DEFAULT NULL COMMENT '其他折扣',
  `customer_actual_pay` decimal(10,2) DEFAULT NULL COMMENT '顾客实际支付金额',
  `shop_total` decimal(10,2) DEFAULT NULL COMMENT '门店收款,顾客实际支付的金额扣除服务费(平台抽成)考虑扣除配送费?',
  `is_online_pay` tinyint(4) DEFAULT NULL COMMENT '是否在线支付  1=在线支付  0=线下付款',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `api_ver` varchar(50) DEFAULT NULL COMMENT 'api版本',
  `shop_id` bigint(36) DEFAULT NULL COMMENT '店铺id',
  `shop_name` varchar(100) DEFAULT NULL COMMENT '店铺名称',
  `update_order_time_stamp` datetime DEFAULT NULL COMMENT '改单时间',
  `delivering_time_stamp` datetime DEFAULT NULL COMMENT '配送时间',
  `delivered_time_stamp` datetime DEFAULT NULL COMMENT '配送完成时间',
  `refused_time_stamp` datetime DEFAULT NULL COMMENT '拒单时间',
  `original_order_status` varchar(36) DEFAULT NULL COMMENT '外卖平台原始的状态值',
  `order_guid` varchar(36) DEFAULT NULL COMMENT '订单服务中的订单guid',
  `staf_guid` varchar(36) DEFAULT NULL COMMENT '操作员guid',
  `staf_name` varchar(36) DEFAULT NULL COMMENT '操作员姓名',
  `accept_order_time_stamp` datetime DEFAULT NULL COMMENT '接单时间',
  `reject_staf_guid` varchar(36) DEFAULT NULL COMMENT '取消订单操作人guid',
  `reject_staf_name` varchar(36) DEFAULT NULL COMMENT '取消订单操作人名字',
  `reject_remark` varchar(36) DEFAULT NULL COMMENT '取消原因',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_guid` (`takeaway_order_guid`) USING BTREE,
  KEY `idx_order_id` (`order_id`) USING BTREE,
  KEY `idx_store_id` (`store_id`) USING BTREE,
  KEY `idx_enterprise_id` (`enterprise_id`) USING BTREE,
  KEY `idx_order_status` (`order_status`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=619 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `hst_order_discount`
--

DROP TABLE IF EXISTS `hst_order_discount`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `hst_order_discount` (
  `id` bigint(30) NOT NULL AUTO_INCREMENT,
  `order_discount_guid` varchar(36) DEFAULT NULL,
  `takeaway_order_guid` varchar(36) DEFAULT NULL,
  `discount_name` varchar(200) DEFAULT NULL COMMENT '优惠活动名称',
  `total` decimal(10,2) DEFAULT NULL COMMENT '此优惠活动总金额 EnterpriseDiscount+PlatformTotal+OtherTotal',
  `enterprise_discount` decimal(10,2) DEFAULT NULL COMMENT '商家承担的折扣部分',
  `platform_total` decimal(10,2) DEFAULT NULL COMMENT '外卖平台承担的折扣部分',
  `other_total` decimal(10,2) DEFAULT NULL COMMENT '第三方承担的部分',
  `remark` varchar(200) DEFAULT NULL COMMENT '备注说明',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_extras_guid` (`order_discount_guid`) USING BTREE,
  KEY `idx_order_guid` (`takeaway_order_guid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `hst_order_dishes`
--

DROP TABLE IF EXISTS `hst_order_dishes`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `hst_order_dishes` (
  `id` bigint(30) NOT NULL AUTO_INCREMENT,
  `dishes_code` varchar(30) DEFAULT NULL COMMENT '菜品code',
  `dishes_name` varchar(30) DEFAULT NULL COMMENT '菜品名称',
  `dishes_sku` varchar(30) DEFAULT NULL COMMENT '菜品SKU',
  `dishes_unit` varchar(20) DEFAULT NULL COMMENT '单位',
  `dishes_quantity` decimal(10,2) DEFAULT NULL COMMENT '菜品数量',
  `dishes_price` decimal(10,2) DEFAULT NULL COMMENT '单价',
  `dishes_sub_total` decimal(10,2) DEFAULT NULL COMMENT '菜品小计',
  `box_quantity` decimal(10,2) DEFAULT NULL COMMENT '餐盒数量',
  `box_price` decimal(10,2) DEFAULT NULL COMMENT '餐盒单价',
  `box_sub_total` decimal(10,2) DEFAULT NULL COMMENT '餐盒小计',
  `dishes_discount_ratio` decimal(10,2) DEFAULT NULL COMMENT '菜品折扣比例 1=无折扣，0.85=8.5折扣',
  `dishes_discount_amount` decimal(10,2) DEFAULT NULL COMMENT '折扣掉的金额单价',
  `dishes_discount_total` decimal(10,2) DEFAULT NULL COMMENT '折扣掉的金额合计',
  `dishes_specs` varchar(50) DEFAULT NULL COMMENT '菜品规格 多元素使用"",""分割开',
  `dishes_property` varchar(50) DEFAULT NULL COMMENT '特殊属性 多元素使用"",""分割开',
  `cart_id` tinyint(4) DEFAULT NULL COMMENT '所属口袋 0=1号口袋  1=2号口袋',
  `settlement_type` tinyint(4) DEFAULT NULL COMMENT '结算类型 0=普通消费菜品  1=赠送菜品',
  `order_dishes_guid` varchar(36) DEFAULT NULL,
  `takeaway_order_guid` varchar(36) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_detail_guid` (`order_dishes_guid`) USING BTREE,
  KEY `idx_order_guid` (`takeaway_order_guid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1021 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `hst_order_dishes_package`
--

DROP TABLE IF EXISTS `hst_order_dishes_package`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `hst_order_dishes_package` (
  `id` bigint(30) NOT NULL AUTO_INCREMENT,
  `order_dishes_package_guid` varchar(36) DEFAULT NULL,
  `order_dishes_guid` varchar(36) DEFAULT NULL COMMENT '订单明细外键',
  `dishes_guid` varchar(36) DEFAULT NULL COMMENT '菜品外键',
  `package_dishes_order_count` int(20) DEFAULT NULL COMMENT '套餐菜品数',
  `package_dishes_unit_count` decimal(10,2) DEFAULT NULL COMMENT '套餐单位数',
  `rise_amount` decimal(10,2) DEFAULT NULL COMMENT '增加数量',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `hst_order_dishes_practice`
--

DROP TABLE IF EXISTS `hst_order_dishes_practice`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `hst_order_dishes_practice` (
  `id` bigint(30) NOT NULL AUTO_INCREMENT,
  `order_dishes_practice_guid` varchar(36) DEFAULT NULL,
  `order_dishes_guid` varchar(36) DEFAULT NULL,
  `fees` decimal(10,2) DEFAULT NULL COMMENT '费用',
  `fees_count` int(10) DEFAULT NULL COMMENT '个数',
  `sub_total` decimal(10,2) DEFAULT NULL COMMENT '合计',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `hst_order_receive_msg`
--

DROP TABLE IF EXISTS `hst_order_receive_msg`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `hst_order_receive_msg` (
  `id` bigint(30) NOT NULL AUTO_INCREMENT,
  `order_receive_msg_guid` varchar(36) DEFAULT NULL,
  `takeaway_order_guid` varchar(36) DEFAULT NULL,
  `msg_type` varchar(30) DEFAULT NULL,
  `crete_time_stamp` datetime DEFAULT NULL COMMENT '创建时间',
  `remind_id` varchar(30) DEFAULT NULL,
  `source_data` varchar(200) DEFAULT NULL,
  `store_guid` varchar(36) DEFAULT NULL,
  `is_notice` tinyint(4) DEFAULT NULL COMMENT '是否看到',
  `is_handle` tinyint(4) DEFAULT NULL COMMENT '是否处理',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `hst_order_response_msg`
--

DROP TABLE IF EXISTS `hst_order_response_msg`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `hst_order_response_msg` (
  `id` bigint(30) NOT NULL AUTO_INCREMENT,
  `order_response_msg_guid` varchar(36) DEFAULT NULL,
  `takeaway_order_guid` varchar(36) DEFAULT NULL,
  `order_receive_msg_guid` varchar(36) DEFAULT NULL,
  `msg_type` varchar(50) DEFAULT NULL,
  `response_time_stamp` datetime DEFAULT NULL COMMENT '回复消息时间',
  `remind_id` varchar(20) DEFAULT NULL,
  `handle_user_guid` varchar(36) DEFAULT NULL,
  `content` varchar(200) DEFAULT NULL COMMENT '回复内容',
  `store_guid` varchar(36) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `hst_store_config`
--

DROP TABLE IF EXISTS `hst_store_config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `hst_store_config` (
  `id` bigint(30) NOT NULL AUTO_INCREMENT,
  `store_config_guid` varchar(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_automatic_accept_order` tinyint(4) DEFAULT NULL COMMENT '是否为自动接单，1：是，0：否',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `store_guid` varchar(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '门店guid',
  `staf_guid` varchar(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '操作员guid',
  `staf_name` varchar(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '操作员name',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=76 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2018-10-26 16:04:31
