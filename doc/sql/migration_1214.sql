alter table hst_ele_auth add column guid varchar(50) comment '唯一标识' after id;
alter table hst_mt_auth add column guid varchar(50) comment '唯一标识' after id;
alter table hst_mt_privacy add column guid varchar(50) comment '唯一标识' after id;
alter table hst_takeout_log add column guid varchar(50) comment '唯一标识' after id;
update hst_ele_auth set guid = (select uuid()) where id > 0;
update hst_mt_auth set guid = (select uuid()) where id > 0;
update hst_mt_privacy set guid = (select uuid()) where id > 0;
update hst_takeout_log set guid = (select uuid()) where id > 0;
update hst_takeout_config set config_guid = (select uuid()) where id > 0;

alter table hst_takeout_discount drop index idx_discount_guid;
alter table hst_takeout_dish drop index idx_dish_guid;
alter table hst_takeout_order drop index idx_order_guid;
alter table hst_takeout_package drop index idx_package_guid;

alter table hst_takeout_config add unique uk_config_guid(config_guid);
alter table hst_takeout_discount add unique uk_discount_guid(discount_guid);
alter table hst_takeout_dish add unique uk_dish_guid(dish_guid);
alter table hst_takeout_order add unique uk_order_guid(order_guid);
alter table hst_takeout_package add unique uk_package_guid(package_guid);
alter table hst_takeout_practice add unique uk_practice_guid(practice_guid);

alter table hst_ele_auth add unique uk_guid(guid);
alter table hst_mt_auth add unique uk_guid(guid);
alter table hst_mt_privacy add unique uk_guid(guid);
alter table hst_takeout_log add unique uk_guid(guid);