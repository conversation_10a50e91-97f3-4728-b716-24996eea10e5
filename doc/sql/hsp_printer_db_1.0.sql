drop table hsp_printer;
CREATE TABLE `hsp_printer` (
  `id` bigint(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `printer_guid` varchar(50) NOT NULL COMMENT '打印机guid',
  `printer_type` char(3) DEFAULT NULL COMMENT '打印机类型; 可选参数: 0/网络打印机; 1/本机; 2/usb打印机',
  `printer_name` varchar(50) DEFAULT NULL COMMENT '打印机名称',
  `printer_ip` varchar(20) DEFAULT NULL COMMENT '打印机ip',
  `printer_port` char(6) DEFAULT NULL COMMENT '打印端口',
  `business_type` char(3) DEFAULT NULL COMMENT '打印业务类型; 参数: 1/前台打印; 2后厨打印; 3/标签打印',
  `print_count` int(11) DEFAULT NULL COMMENT '打印次数',
  `print_page` char(10) DEFAULT NULL COMMENT '打印纸张类型; 参数: 1/80; 2/58; 3/40*30; 4/30*20',
  `print_cut` char(10) NOT NULL DEFAULT '1' COMMENT '打印方式(切纸方式);  参数: 1/整单; 2/一菜一单; 3/一种类型一单; 4/一份数量一单; 默认1/整单',
  `invoice_type` char(3) DEFAULT NULL COMMENT '打印单据; 参数打印单据: 1/点菜单; 2/预接单; 3/结账单; 4/储值单; 5/菜品清单; 6/退菜单; 7/标签单; 88:/外卖单; 99/交接单',
  `store_guid` varchar(40) NOT NULL COMMENT '门店guid',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '数据更新时间',
  `create_staff_guid` varchar(50) NOT NULL COMMENT '创建该条记录的员工id',
  `update_staff_guid` varchar(50) NOT NULL COMMENT '更新该条记录的员工id',
  `is_master` tinyint(1) unsigned NOT NULL COMMENT '主',
  `device_id` varchar(20) NOT NULL DEFAULT '' COMMENT '设备编号',
  `guid` varchar(20) NOT NULL DEFAULT '' COMMENT 'guid',
  `store_name` varchar(50) NOT NULL DEFAULT '' COMMENT '门店名称',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_guid` (`guid`) USING BTREE,
  KEY `idx_create_time` (`create_time`) USING BTREE,
  KEY `idx_store_guid` (`store_guid`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='打印机配置表 - 多个记录对一个打印机';

drop table hsp_printer_area;
CREATE TABLE `hsp_printer_area` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `guid` varchar(50) DEFAULT NULL,
  `printer_guid` varchar(50) DEFAULT NULL COMMENT '打印机guid',
  `area_guid` varchar(50) DEFAULT NULL COMMENT '区域guid',
  `store_guid` varchar(50) DEFAULT NULL COMMENT '门店guid',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP,
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `area_name` varchar(50) NOT NULL DEFAULT '' COMMENT '区域名称',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_guid` (`guid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

drop table hsp_printer_dish;
CREATE TABLE `hsp_printer_dish` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `link_guid` varchar(50) DEFAULT NULL,
  `dish_guid` varchar(50) NOT NULL COMMENT '菜品guid',
  `store_guid` varchar(50) NOT NULL COMMENT '门店guid',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP,
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `printer_guid` varchar(50) NOT NULL COMMENT '打印机guid',
  `dish_name` varchar(50) NOT NULL DEFAULT '' COMMENT '菜品名称',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_link_guid` (`link_guid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

drop table hsp_print_record;
CREATE TABLE `hsp_print_record` (
  `id` bigint(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `record_uid` varchar(50) NOT NULL DEFAULT '' COMMENT '打印UID',
  `record_guid` varchar(40) NOT NULL COMMENT '打印记录guid',
  `type_code` char(3) NOT NULL DEFAULT '' COMMENT '打印类型代码',
  `printer_guid` varchar(50) NOT NULL DEFAULT '' COMMENT '打印机guid',
  `print_status` char(3) NOT NULL DEFAULT '0' COMMENT '0/打印中;1/打印成功;2/打印失败',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '数据更新时间',
  `create_staff_guid` char(32) NOT NULL DEFAULT '' COMMENT '创建该条记录的员工id',
  `update_staff_guid` char(32) NOT NULL DEFAULT '' COMMENT '更新该条记录的员工id',
  `print_content` text NOT NULL,
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_record_guid` (`record_guid`) USING BTREE,
  KEY `idx_create_time` (`create_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='打印记录表';

