-- -----------------------------------------------------
-- Schema hst_takeout_db
-- -----------------------------------------------------
--   外卖服务

-- -----------------------------------------------------
-- Table `hst_ele_auth`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `hst_ele_auth` ;

CREATE TABLE IF NOT EXISTS `hst_ele_auth` (
  `id` BIGINT(64) UNSIGNED NOT NULL AUTO_INCREMENT,
  `store_guid` VARCHAR(50) NOT NULL COMMENT ''门店guid'',
  `enterprise_guid` VARCHAR(50) NOT NULL,
  `user_id` BIGINT(64) UNSIGNED NOT NULL COMMENT ''商户id'',
  `user_name` VARCHAR(50) NOT NULL COMMENT ''商户名'',
  `shop_id` BIGINT(64) NOT NULL COMMENT ''店铺ID'',
  `shop_name` VARCHAR(50) NOT NULL COMMENT ''店铺名称'',
  `access_token` VARCHAR(50) NOT NULL COMMENT ''访问token'',
  `refresh_token` VARCHAR(50) NOT NULL COMMENT ''刷新token'',
  `token_type` VARCHAR(50) NOT NULL COMMENT ''token类型'',
  `active_time` DATETIME NOT NULL COMMENT ''Token生效时间'',
  `expires` BIGINT(64) UNSIGNED NOT NULL COMMENT ''Token有效时长，秒'',
  `expire_time` DATETIME NOT NULL COMMENT ''Token过期时间'',
  `refresh_active_time` DATETIME NOT NULL COMMENT ''刷新Token生效时间'',
  `refresh_expires` BIGINT(64) NOT NULL COMMENT ''刷新Token有效时长，秒'',
  `refresh_expire_time` DATETIME NOT NULL COMMENT ''刷新Token过期时间'',
  `is_deleted` TINYINT(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT ''是否已删除：0 未删除 1 已删除'',
  `gmt_create` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT ''创建时间'',
  `gmt_modified` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT ''修改时间'',
  PRIMARY KEY (`id`),
  INDEX `idx_store_guid` (`store_guid` ASC),
  INDEX `idx_user_id` (`user_id` ASC),
  INDEX `idx_expire_time` (`expire_time` ASC),
  INDEX `idx_is_deleted` (`is_deleted` ASC))
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8mb4
COMMENT = ''饿了么授权'';


-- -----------------------------------------------------
-- Table `hst_takeout_order`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `hst_takeout_order` ;

CREATE TABLE IF NOT EXISTS `hst_takeout_order` (
  `id` BIGINT(64) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT ''主键ID'',
  `shop_id` VARCHAR(50) NULL COMMENT ''外卖店铺ID'',
  `shop_name` VARCHAR(50) NULL COMMENT ''外卖店铺名字'',
  `order_guid` VARCHAR(50) NULL COMMENT ''订单GUID'',
  `order_status` TINYINT(5) NULL COMMENT ''订单状态：0=，1=，2=，3=，4=，5=，6=，7='',
  `order_tag_status` TINYINT(5) NULL COMMENT ''订单附加状态'',
  `order_type` TINYINT(5) NULL COMMENT ''订单类型：0=外卖订单，1=微信订单，2=其他订单'',
  `order_sub_type` TINYINT(5) NULL COMMENT ''订单子类型：OrderType=0时：0=美团，1=饿了么，2=百度，3=京东; OrderType=1时：0=扫码订单，1=微信预订单'',
  `enterprise_name` VARCHAR(50) NULL COMMENT ''企业名称'',
  `store_name` VARCHAR(50) NULL COMMENT ''门店名称'',
  `enterprise_guid` VARCHAR(50) NULL COMMENT ''企业GUID'',
  `store_guid` VARCHAR(50) NULL COMMENT ''门店GUID'',
  `order_id` VARCHAR(50) NULL COMMENT ''外卖订单ID'',
  `order_view_id` VARCHAR(50) NULL COMMENT ''外卖订单SN'',
  `order_day_sn` VARCHAR(50) NULL COMMENT ''平台订单日流水 #3'',
  `order_remark` VARCHAR(200) NULL COMMENT ''外卖单备注'',
  `is_reserve` TINYINT(1) NULL COMMENT ''是否是预订单：0=否，1=是'',
  `dish_count` DECIMAL(10,2) NULL COMMENT ''菜品数量'',
  `customer_name` VARCHAR(50) NULL COMMENT ''顾客姓名'',
  `customer_phone` VARCHAR(50) NULL,
  `customer_address` VARCHAR(50) NULL COMMENT ''顾客地址'',
  `customer_number` INT(32) NULL COMMENT ''用餐人数'',
  `is_first_order` TINYINT(1) NULL COMMENT ''首单用户：0=否，1=是'',
  `ship_latitude` VARCHAR(50) NULL COMMENT ''配送地址纬度'',
  `ship_longitude` VARCHAR(50) NULL,
  `shipper_name` VARCHAR(50) NULL COMMENT ''配送员姓名'',
  `shipper_phone` VARCHAR(50) NULL COMMENT ''配送员手机号'',
  `is_third_shipper` TINYINT(1) NULL COMMENT ''是否三方托运：0=否，1=是'',
  `is_invoiced` TINYINT(1) NULL COMMENT ''是否有发票：0=无发票，1=有发票'',
  `invoice_type` TINYINT(5) NULL COMMENT ''发票类型：-1=未知，0=个人，1=企业'',
  `invoice_title` VARCHAR(50) NULL COMMENT ''发票抬头'',
  `taxpayer_id` VARCHAR(50) NULL COMMENT ''纳税人身份证明'',
  `total` DECIMAL(10,2) NULL COMMENT ''总价，包括：菜品 + 餐盒 + 配送'',
  `ship_total` DECIMAL(10,2) NULL COMMENT ''配送费合计'',
  `dish_total` DECIMAL(10,2) NULL COMMENT ''菜品消费合计'',
  `package_total` DECIMAL(10,2) NULL COMMENT ''餐盒费合计'',
  `discount_total` DECIMAL(10,2) NULL COMMENT ''折扣合计，enterpriseDiscount + platformDiscount + otherDiscount'',
  `enterprise_discount` DECIMAL(10,2) NULL COMMENT ''商家承担的折扣部分'',
  `platform_discount` DECIMAL(10,2) NULL,
  `other_discount` DECIMAL(10,2) NULL COMMENT ''其他折扣'',
  `service_fee_rate` DECIMAL(10,2) NULL COMMENT ''服务费率(平台抽成比例)0.15=15%'',
  `service_fee` DECIMAL(10,2) NULL,
  `customer_actual_pay` DECIMAL(10,2) NULL COMMENT ''顾客实际支付金额'',
  `customer_refund` DECIMAL(10,2) NULL COMMENT ''顾客退款金额'',
  `customer_refund_dish` VARCHAR(500) NULL COMMENT ''顾客退菜字符串拼接，有时间时将该字段提取为退菜表'',
  `shop_total` DECIMAL(10,2) NULL COMMENT ''门店收款：顾客实际支付的金额 - 服务费 - 三方配送费'',
  `is_online_pay` TINYINT(1) NULL COMMENT ''是否在线支付：0=否，1=是'',
  `create_time` DATETIME NULL COMMENT ''订单创建时间'',
  `active_time` DATETIME NULL COMMENT ''订单激活时间：非预订单，激活时间与下单时间一致；预订单，激活时间指实际生效时间；'',
  `accept_time` DATETIME NULL COMMENT ''接单时间'',
  `accept_staff_guid` VARCHAR(50) NULL COMMENT ''接单员工GUID'',
  `accept_staff_name` VARCHAR(50) NULL COMMENT ''接单员工名字'',
  `accept_device_id` VARCHAR(50) NULL COMMENT ''接单设备ID'',
  `accept_device_type` TINYINT(5) NULL COMMENT ''接单设备类型'',
  `estimate_delivered_time` DATETIME NULL COMMENT ''预计送达时间：转时间戳后，若0=立即配送，若大于0=具体要求的送达时间'',
  `delivery_time` DATETIME NULL COMMENT ''配送时间，同取餐时间'',
  `delivered_time` DATETIME NULL COMMENT ''实际送达时间'',
  `cancel_req_time` DATETIME NULL COMMENT ''取消订单请求时间'',
  `cancel_req_reason` VARCHAR(50) NULL COMMENT ''取消订单请求原因'',
  `cancel_req_expire_time` DATETIME NULL COMMENT ''取消订单请求超时时间'',
  `cancel_reply_time` DATETIME NULL COMMENT ''取消订单请求回复时间'',
  `cancel_reply_message` VARCHAR(50) NULL COMMENT ''取消订单请求回复消息体'',
  `cancel_reply_staff_guid` VARCHAR(50) NULL COMMENT ''取消订单请求回复员工GUID'',
  `cancel_reply_staff_name` VARCHAR(50) NULL COMMENT ''取消订单请求回复员工名字'',
  `refund_req_time` DATETIME NULL COMMENT ''退单请求时间'',
  `refund_req_reason` VARCHAR(50) NULL,
  `refund_req_expire_time` DATETIME NULL COMMENT ''退单请求超时时间'',
  `refund_reply_time` DATETIME NULL COMMENT ''退单请求回复时间'',
  `refund_reply_message` VARCHAR(50) NULL COMMENT ''退单请求回复消息体'',
  `refund_reply_staff_guid` VARCHAR(50) NULL COMMENT ''退单请求回复员工GUID'',
  `refund_reply_staff_name` VARCHAR(50) NULL COMMENT ''退单请求回复员工名字'',
  `cancel_time` DATETIME NULL COMMENT ''取消订单时间'',
  `cancel_reason` VARCHAR(50) NULL COMMENT ''取消订单原因'',
  `cancel_staff_guid` VARCHAR(50) NULL COMMENT ''取消订单员工GUID'',
  `cancel_staff_name` VARCHAR(50) NULL COMMENT ''取消订单员工名字'',
  `is_cancel_as_reject` TINYINT(1) UNSIGNED NULL DEFAULT 0 COMMENT ''订单取消类型是否是拒单：0=否，1=是'',
  `complete_time` DATETIME NULL COMMENT ''订单完成时间订单完成时间'',
  `gmt_create` DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT ''创建时间'',
  `gmt_modified` DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT ''更新时间'',
  PRIMARY KEY (`id`),
  INDEX `idx_order_guid` (`order_guid` ASC),
  INDEX `idx_order_id` (`order_id` ASC),
  INDEX `idx_gmt_create` (`gmt_create` ASC))
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8mb4
COMMENT = ''外卖订单'';


-- -----------------------------------------------------
-- Table `hst_takeout_discount`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `hst_takeout_discount` ;

CREATE TABLE IF NOT EXISTS `hst_takeout_discount` (
  `id` BIGINT(64) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT ''主键ID'',
  `discount_guid` VARCHAR(50) NULL COMMENT ''订单折扣GUID'',
  `order_guid` VARCHAR(50) NULL COMMENT ''订单GUID'',
  `discount_name` VARCHAR(50) NULL COMMENT ''折扣名称'',
  `discount_remark` VARCHAR(200) NULL COMMENT ''备注'',
  `total_discount` DECIMAL(10,2) NULL COMMENT ''折扣总额，等于enterpriseDiscount+platformDiscount+otherDiscount'',
  `enterprise_discount` DECIMAL(10,2) NULL COMMENT ''ERP承担折扣总额'',
  `platform_discount` DECIMAL(10,2) NULL COMMENT ''平台承担折扣总额'',
  `other_discount` DECIMAL(10,2) NULL COMMENT ''其他折扣总额'',
  `gmt_create` DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT ''创建时间'',
  `gmt_modified` DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT ''修改时间'',
  PRIMARY KEY (`id`),
  INDEX `idx_order_guid` (`order_guid` ASC),
  INDEX `idx_discount_guid` (`discount_guid` ASC))
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8mb4
COMMENT = ''外卖菜品折扣'';


-- -----------------------------------------------------
-- Table `hst_takeout_dish`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `hst_takeout_dish` ;

CREATE TABLE IF NOT EXISTS `hst_takeout_dish` (
  `id` BIGINT(64) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT ''主键ID'',
  `order_guid` VARCHAR(50) NULL COMMENT ''订单GUID'',
  `dish_sku` VARCHAR(50) NULL COMMENT ''菜品SKU'',
  `dish_guid` VARCHAR(50) NULL COMMENT ''菜品GUID'',
  `dish_name` VARCHAR(50) NULL COMMENT ''菜品名称'',
  `dish_code` VARCHAR(50) NULL COMMENT ''菜品编码'',
  `dish_unit` VARCHAR(50) NULL COMMENT ''菜品单位'',
  `dish_price` DECIMAL(10,2) NULL COMMENT ''菜品单价'',
  `dish_count` DECIMAL(10,2) NULL COMMENT ''菜品数量'',
  `dish_total` DECIMAL(10,2) NULL COMMENT ''菜品总额'',
  `dish_spec` VARCHAR(50) NULL COMMENT ''菜品规格'',
  `dish_property` VARCHAR(50) NULL COMMENT ''菜品属性'',
  `box_price` DECIMAL(10,2) NULL COMMENT ''餐盒单价'',
  `box_count` DECIMAL(10,2) NULL COMMENT ''餐盒数量'',
  `box_total` DECIMAL(10,2) NULL COMMENT ''餐盒总额'',
  `cart_id` TINYINT(5) NULL COMMENT ''口袋ID'',
  `discount_price` DECIMAL(10,2) NULL COMMENT ''折扣单价'',
  `discount_ratio` DECIMAL(10,2) NULL COMMENT ''折扣比例，1=无折扣，0.85=8.5折'',
  `discount_total` DECIMAL(10,2) NULL COMMENT ''折扣总额'',
  `settle_type` TINYINT(5) NULL COMMENT ''结算类型，0=普通消费菜品，1=赠送菜品'',
  `gmt_create` DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT ''创建时间'',
  `gmt_modified` DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT ''修改时间'',
  PRIMARY KEY (`id`),
  INDEX `idx_dish_sku` (`dish_sku` ASC),
  INDEX `idx_dish_guid` (`dish_guid` ASC),
  INDEX `idx_order_guid` (`order_guid` ASC))
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8mb4
COMMENT = ''外卖菜品'';


-- -----------------------------------------------------
-- Table `hst_takeout_package`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `hst_takeout_package` ;

CREATE TABLE IF NOT EXISTS `hst_takeout_package` (
  `id` BIGINT(64) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT ''主键ID'',
  `package_guid` VARCHAR(50) NULL COMMENT ''套餐菜品GUID'',
  `dish_guid` VARCHAR(50) NULL COMMENT ''套餐子菜品GUID'',
  `dish_count` DECIMAL(10,2) NULL COMMENT ''套餐子菜品数量'',
  `dish_unit_count` DECIMAL(10,2) NULL COMMENT ''套餐子菜品单位数量'',
  `rise_amount` DECIMAL(10,2) NULL COMMENT ''增加数量'',
  `gmt_create` DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT ''创建时间'',
  `gmt_modified` DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT ''修改时间'',
  PRIMARY KEY (`id`),
  INDEX `idx_gmt_create` (`gmt_create` ASC),
  INDEX `idx_package_guid` (`package_guid` ASC))
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8mb4
COMMENT = ''外卖菜品套餐'';


-- -----------------------------------------------------
-- Table `hst_takeout_practice`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `hst_takeout_practice` ;

CREATE TABLE IF NOT EXISTS `hst_takeout_practice` (
  `id` BIGINT(64) UNSIGNED NOT NULL AUTO_INCREMENT,
  `practice_guid` VARCHAR(50) NULL,
  `dish_guid` VARCHAR(50) NULL,
  `fee` DECIMAL(10,2) NULL,
  `fee_count` DECIMAL(10,2) NULL,
  `fee_total` DECIMAL(10,2) NULL,
  `gmt_create` DATETIME NULL DEFAULT CURRENT_TIMESTAMP,
  `gmt_modified` DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  INDEX `idx_dish_guid` (`dish_guid` ASC),
  INDEX `idx_gmt_create` (`gmt_create` ASC))
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8mb4
COMMENT = ''外卖菜品做法'';


-- -----------------------------------------------------
-- Table `hst_takeout_remind`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `hst_takeout_remind` ;

CREATE TABLE IF NOT EXISTS `hst_takeout_remind` (
  `id` BIGINT(64) UNSIGNED NOT NULL AUTO_INCREMENT,
  `remind_guid` VARCHAR(50) NULL COMMENT ''催单GUID'',
  `remind_time` DATETIME NULL COMMENT ''催单时间'',
  `order_guid` VARCHAR(50) NULL COMMENT ''订单GUID'',
  `remind_id` VARCHAR(50) NULL COMMENT ''催单ID，向平台回复催单时所用唯一标识'',
  `is_replied` TINYINT(1) NULL COMMENT ''是否已回复'',
  `reply_time` DATETIME NULL COMMENT ''催单回复时间'',
  `reply_content` VARCHAR(200) NULL COMMENT ''催单回复内容'',
  `reply_staff_guid` VARCHAR(50) NULL COMMENT ''催单回复员工GUID'',
  `reply_staff_name` VARCHAR(50) NULL COMMENT ''催单回复员工名字'',
  `gmt_create` DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT ''记录创建时间'',
  `gmt_modified` DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT ''记录修改时间'',
  PRIMARY KEY (`id`),
  UNIQUE INDEX `urge_guid_UNIQUE` (`remind_guid` ASC),
  UNIQUE INDEX `order_guid_UNIQUE` (`order_guid` ASC))
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8mb4
COMMENT = ''催单记录'';


-- -----------------------------------------------------
-- Table `hst_mt_auth`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `hst_mt_auth` ;

CREATE TABLE IF NOT EXISTS `hst_mt_auth` (
  `id` BIGINT(64) UNSIGNED NOT NULL AUTO_INCREMENT,
  `e_poi_id` VARCHAR(50) NOT NULL COMMENT ''Erp方门店GUID，即storeGuid'',
  `enterprise_guid` VARCHAR(50) NOT NULL COMMENT ''Erp方企业GUID'',
  `mt_store_guid` VARCHAR(50) NOT NULL COMMENT ''美团门店GUID'',
  `mt_store_name` VARCHAR(50) NOT NULL COMMENT ''美团门店名字'',
  `access_token` VARCHAR(200) NOT NULL COMMENT ''授权Token'',
  `business_id` TINYINT(5) UNSIGNED NOT NULL COMMENT ''业务ID'',
  `active_time` DATETIME NOT NULL COMMENT ''token激活时间'',
  `expire_time` DATETIME NOT NULL COMMENT ''token过期时间'',
  `is_deleted` TINYINT(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT ''是否已删除：0=未删除，1=已删除'',
  `gmt_create` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT ''创建时间'',
  `gmt_modified` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT ''修改时间'',
  PRIMARY KEY (`id`),
  INDEX `idx_e_poi_id` (`e_poi_id` ASC),
  INDEX `idx_mt_store_guid` (`mt_store_guid` ASC),
  INDEX `idx_expire_time` (`expire_time` ASC),
  INDEX `idx_is_deleted` (`is_deleted` ASC))
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8mb4
COMMENT = ''美团授权'';


-- -----------------------------------------------------
-- Table `hst_mt_privacy`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `hst_mt_privacy` ;

CREATE TABLE IF NOT EXISTS `hst_mt_privacy` (
  `id` BIGINT(64) NOT NULL,
  `e_poi_id` VARCHAR(50) NULL COMMENT ''Erp方门店GUID，即storeGuid'',
  `order_id` VARCHAR(50) NULL COMMENT ''操作结果？'',
  `order_id_view` VARCHAR(50) NULL COMMENT ''订单展示号？'',
  `day_seq` VARCHAR(50) NULL COMMENT ''订单流水号？'',
  `real_phone_number` VARCHAR(50) NULL COMMENT ''真实手机号'',
  `gmt_create` DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT ''创建时间'',
  `gmt_modified` DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT ''修改时间'',
  PRIMARY KEY (`id`),
  INDEX `idx_e_poi_id` (`e_poi_id` ASC))
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8mb4
COMMENT = ''美团隐私号'';


-- -----------------------------------------------------
-- Table `hst_takeout_config`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `hst_takeout_config` ;

CREATE TABLE IF NOT EXISTS `hst_takeout_config` (
  `id` BIGINT(64) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT ''主键ID'',
  `config_guid` VARCHAR(50) NULL COMMENT ''配置GUID'',
  `store_guid` VARCHAR(50) NULL COMMENT ''门店GUID'',
  `is_auto_order` TINYINT(1) UNSIGNED NULL COMMENT ''是否自动接单：0=否，1=是'',
  `staff_guid` VARCHAR(50) NULL COMMENT ''员工GUID'',
  `staff_name` VARCHAR(50) NULL COMMENT ''员工名字'',
  `gmt_create` DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT ''创建时间'',
  `gmt_modified` DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT ''修改时间'',
  PRIMARY KEY (`id`),
  INDEX `idx_store_guid` (`store_guid` ASC))
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8mb4
COMMENT = ''外卖配置'';