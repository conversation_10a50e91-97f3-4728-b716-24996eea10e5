SELECT
temp.营业日,
temp.创建时间,
temp.门店名称,
temp.order_id "外卖订单ID",
temp.订单状态,
temp.订单子类型,
temp.third_sku_id "外卖商品SKU",
temp.商品名称,
temp.商品规格,
temp.商品属性,
temp.商品单位,
temp.商品单价,
temp.商品数量,
temp.商品消费合计,
temp.餐盒单价,
temp.餐盒数量,
temp.餐盒金额,
temp.餐盒总额,
temp.门店商品名称,
temp.门店商品单价,
temp.门店商品数量,
temp.门店商品总价
from (
SELECT
	o.order_guid,
	i.item_guid as order_item_guid, 
	o.business_day::text "营业日",
	i.gmt_create "创建时间",
	o.store_guid "门店GUID",
	o.store_name "门店名称",
	o.order_id,
    CASE o.order_status 
		WHEN -1 THEN '已取消' 
		WHEN 0 THEN '待处理' 
		WHEN 10 THEN '已接单' 
		WHEN 20 THEN '配送中' 
		WHEN 30 THEN '配送完成' 
		WHEN 100 THEN '已完成'
	END AS "订单状态",
    CASE o.order_sub_type 
		WHEN 0 THEN '美团' 
		WHEN 1 THEN	'饿了么' 
		WHEN 6 THEN	'赚餐外卖' 
	END AS "订单子类型",
	i.item_guid "商品GUID",
	-- i.item_sku "商品SKU",
	i.third_sku_id,
	i.item_name "商品名称",
	i.item_spec "商品规格",
	i.item_property "商品属性",
	i.item_unit "商品单位",
	i.item_price "商品单价",
	i.item_count "商品数量",
	i.item_total "商品消费合计",
	i.box_price "餐盒单价",
	i.box_count "餐盒数量",
	i.box_total "餐盒金额",
	o.package_total "餐盒总额",
	i.erp_item_name "门店商品名称",
	i.erp_item_price "门店商品单价",
	case when i.erp_item_name is not null then i.actual_item_count else null end "门店商品数量",
	i.erp_item_price * i.actual_item_count "门店商品总价"
FROM
	"hst_takeaway_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_takeout_item i
	LEFT JOIN "hst_takeaway_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_takeout_order o ON i.order_guid = o.order_guid,
    LATERAL (
        select
            public.getlist(
                public.getacl(
                    $privileges_flag,
                    ($power_list)::text
                ),
                ($power_list)::text,
                array[
                    [[ {{STORE_GUID_MULTI}} -- ]] '-1'
                    ,
                    [[ {{GROUP_STORE_GUID}} -- ]] '-1'
                ]
            ) list,
            public.getacl(
                $privileges_flag,
                ($power_list)::text
            ) chmod
    ) acl
WHERE
    acl.chmod
	[[and o.business_day between {{START_DATE}} AND {{END_DATE}}]]
	[[and i.business_day between {{START_DATE}} AND {{END_DATE}}]]
    and case acl.list
        when 'true' then true
        when 'false' then false
        -- when 'false' then true  --debug
        else (o.store_guid = any(acl.list::text[]))
    end
	[[and o.order_status in ({{ORDER_STATUS_MULTI}})]]
	[[and o.order_sub_type in ({{ORDER_SUB_TYPE_MULTI}})]]
	[[and i.item_name ~* ({{ITEM_NAME_MULTI}})]]
    [[and o.order_id ~* {{SEARCH}}]]
		
union all

SELECT
	o.order_guid,
	i.item_guid as order_item_guid, 
	o.business_day::text "营业日",
	i.gmt_create "创建时间",
	o.store_guid "门店GUID",
	o.store_name "门店名称",
	o.order_id,
   '已退款' AS "订单状态",
    CASE o.order_sub_type 
		WHEN 0 THEN '美团' 
		WHEN 1 THEN	'饿了么' 
		WHEN 6 THEN	'赚餐外卖' 
	END AS "订单子类型",
	i.item_guid "商品GUID",
	-- i.item_sku "商品SKU",
	i.third_sku_id,
	i.item_name "商品名称",
	i.item_spec "商品规格",
	i.item_property "商品属性",
	i.item_unit "商品单位",
	i.item_price "商品单价",
	case when o.order_status = -1
	then - i.item_count else - i.refund_count end "商品数量",
	case when o.order_status = -1
	then - i.item_count * i.item_price  else - i.refund_count * i.item_price end "商品消费合计",
	i.box_price "餐盒单价",
	i.box_count "餐盒数量",
	i.box_total "餐盒金额",
	o.package_total "餐盒总额",
    i.erp_item_name "门店商品名称",
    i.erp_item_price "门店商品单价",
    - case when i.erp_item_name is not null then i.actual_item_count else null end "门店商品数量",
    - i.erp_item_price * i.actual_item_count "门店商品总价"
FROM
	"hst_takeaway_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_takeout_item i
	LEFT JOIN "hst_takeaway_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_takeout_order o ON i.order_guid = o.order_guid,
    LATERAL (
        select
            public.getlist(
                public.getacl(
                    $privileges_flag,
                    ($power_list)::text
                ),
                ($power_list)::text,
                array[
                    [[ {{STORE_GUID_MULTI}} -- ]] '-1'
                    ,
                    [[ {{GROUP_STORE_GUID}} -- ]] '-1'
                ]
            ) list,
            public.getacl(
                $privileges_flag,
                ($power_list)::text
            ) chmod
    ) acl
WHERE
    acl.chmod
    and (i.refund_count > 0 or (o.order_status = '-1' and o.is_refund_success = 1 ))
	[[and o.business_day between {{START_DATE}} AND {{END_DATE}}]]
	[[and i.business_day between {{START_DATE}} AND {{END_DATE}}]]
    and case acl.list
        when 'true' then true
        when 'false' then false
        -- when 'false' then true  --debug
        else (o.store_guid = any(acl.list::text[]))
    end
	[[and o.order_status in ({{ORDER_STATUS_MULTI}})]]
	[[and o.order_sub_type in ({{ORDER_SUB_TYPE_MULTI}})]]
	[[and i.item_name ~* ({{ITEM_NAME_MULTI}})]]
    [[and o.order_id ~* {{SEARCH}}]]
) temp
order by temp.order_guid desc, temp.order_item_guid asc, temp.商品消费合计 desc