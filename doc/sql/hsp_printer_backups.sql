CREATE TABLE `hsp_printer_backups` (
  `id` bigint(64) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `store_guid` varchar(50) NOT NULL COMMENT '门店GUID',
  `device_id` varchar(20) NOT NULL COMMENT '设备编号',
  `print_list_json` text NOT NULL COMMENT '打印机列表的Json字符串',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '数据更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=134 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='打印机备份表';

