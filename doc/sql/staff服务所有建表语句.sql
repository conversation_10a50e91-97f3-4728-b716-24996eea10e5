-- -----------------------------------------------------
-- Schema hss_staff
-- -----------------------------------------------------
--   员工服务

-- -----------------------------------------------------
-- Table `hss_authorization_record`
-- -----------------------------------------------------
CREATE TABLE `hss_authorization_record` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `guid` varchar(50) NOT NULL COMMENT '全局唯一主键',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间：必填',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间：必填',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除（默认为0-未删除）',
  `authorization_code` varchar(50) DEFAULT NULL COMMENT '授权号',
  `source_guid` varchar(50) DEFAULT NULL COMMENT '资源guid,唯一',
  `source_name` varchar(50) DEFAULT NULL COMMENT '资源名称',
  `source_code` varchar(50) DEFAULT NULL COMMENT '资源code',
  `authorization_staff_guid` varchar(50) DEFAULT NULL COMMENT '授权人guid',
  `authorization_staff_name` varchar(50) DEFAULT NULL COMMENT '授权人name',
  `store_name` varchar(50) DEFAULT NULL COMMENT '门店名',
  `store_guid` varchar(50) NOT NULL DEFAULT '0' COMMENT '门店guid',
  `operator_guid` varchar(50) DEFAULT NULL COMMENT '操作人员guid',
  `operator_name` varchar(50) DEFAULT NULL COMMENT '操作人名字',
  `authorization_time` datetime DEFAULT NULL COMMENT '授权时间',
  `last_use_time` datetime DEFAULT NULL COMMENT '最后使用时间',
  `use_count` int(11) DEFAULT '0' COMMENT '使用次数',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_guid` (`guid`)
) ENGINE=InnoDB AUTO_INCREMENT=113 DEFAULT CHARSET=utf8mb4 COMMENT='授权记录表';

-- -----------------------------------------------------
-- Table `hss_data_dictionary`
-- -----------------------------------------------------
CREATE TABLE `hss_data_dictionary` (
  `id` bigint(64) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `guid` varchar(50) DEFAULT NULL COMMENT '唯一标识',
  `type_code` varchar(50) DEFAULT NULL COMMENT '字典类型编码',
  `type_name` varchar(50) DEFAULT NULL COMMENT '字典类型名称',
  `item_code` int(32) unsigned DEFAULT NULL COMMENT '字典项目编码',
  `item_name` varchar(50) DEFAULT NULL COMMENT '字典项目名称',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_guid` (`guid`),
  KEY `idx_type_code_item_code` (`type_code`,`item_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据字典表';

-- -----------------------------------------------------
-- Table `hss_menu`
-- -----------------------------------------------------
CREATE TABLE `hss_menu` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `menu_guid` varchar(50) NOT NULL COMMENT '菜单guid',
  `menu_name` varchar(50) NOT NULL COMMENT '菜单名称',
  `menu_url` varchar(100) DEFAULT NULL COMMENT '菜单地址',
  `parent_ids` varchar(200) NOT NULL COMMENT '上级菜单id，逗号分割，最顶级为模块guid',
  `menu_icon` varchar(100) DEFAULT NULL COMMENT '菜单图标',
  `menu_sort` int(11) NOT NULL COMMENT '菜单排序，默认为1',
  `module_guid` varchar(50) DEFAULT NULL COMMENT '模块guid',
  `terminal_guid` varchar(50) NOT NULL COMMENT '终端guid',
  `is_enable` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '是否启用，默认为1-已启用',
  `create_staff_guid` varchar(50) NOT NULL COMMENT '创建人guid',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_sync` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '同步时间',
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_menu` (`menu_guid`)
) ENGINE=InnoDB AUTO_INCREMENT=17600 DEFAULT CHARSET=utf8mb4 COMMENT='菜单表';


-- -----------------------------------------------------
-- Table `hss_product`
-- -----------------------------------------------------
CREATE TABLE `hss_product` (
  `id` bigint(64) unsigned NOT NULL AUTO_INCREMENT,
  `store_guid` varchar(50) DEFAULT NULL COMMENT '门店GUID',
  `product_guid` varchar(30) NOT NULL COMMENT '产品GUID',
  `product_name` varchar(255) DEFAULT '' COMMENT '产品名字',
  `product_type` tinyint(8) NOT NULL DEFAULT '0' COMMENT '产品类型：0=时间，1=数量',
  `charge_guid` varchar(50) DEFAULT NULL,
  `mchnt_type_code` varchar(50) NOT NULL DEFAULT '' COMMENT '商家经营类型，对于于 hsp_product 的 available_merchant',
  `gmt_product_start` datetime DEFAULT NULL COMMENT '产品授权起始日期',
  `gmt_product_end` datetime DEFAULT NULL COMMENT '产品授权截止日期',
  `is_enable` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用:0/禁用,1/启用',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除:0/删除,1/正常',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=58 DEFAULT CHARSET=utf8mb4;

-- -----------------------------------------------------
-- Table `hss_product`
-- -----------------------------------------------------
CREATE TABLE `hss_product` (
  `id` bigint(64) unsigned NOT NULL AUTO_INCREMENT,
  `store_guid` varchar(50) DEFAULT NULL COMMENT '门店GUID',
  `product_guid` varchar(30) NOT NULL COMMENT '产品GUID',
  `product_name` varchar(255) DEFAULT '' COMMENT '产品名字',
  `product_type` tinyint(8) NOT NULL DEFAULT '0' COMMENT '产品类型：0=时间，1=数量',
  `charge_guid` varchar(50) DEFAULT NULL,
  `mchnt_type_code` varchar(50) NOT NULL DEFAULT '' COMMENT '商家经营类型，对于于 hsp_product 的 available_merchant',
  `gmt_product_start` datetime DEFAULT NULL COMMENT '产品授权起始日期',
  `gmt_product_end` datetime DEFAULT NULL COMMENT '产品授权截止日期',
  `is_enable` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用:0/禁用,1/启用',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除:0/删除,1/正常',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=58 DEFAULT CHARSET=utf8mb4;

-- -----------------------------------------------------
-- Table `hss_product_theme`
-- -----------------------------------------------------
CREATE TABLE `hss_product_theme` (
  `id` bigint(64) unsigned NOT NULL AUTO_INCREMENT,
  `store_guid` varchar(50) DEFAULT NULL,
  `product_guid` varchar(30) NOT NULL COMMENT '产品GUID',
  `product_name` varchar(50) DEFAULT NULL COMMENT '产品名称',
  `terminal_guid` varchar(50) NOT NULL COMMENT '终端GUID',
  `terminal_name` varchar(50) DEFAULT NULL COMMENT '终端名称',
  `terminal_code` varchar(50) NOT NULL,
  `theme_guid` varchar(45) NOT NULL COMMENT '主题GUID',
  `theme_name` varchar(45) DEFAULT NULL COMMENT '主题名称',
  `theme_code` varchar(45) NOT NULL COMMENT '主题编码，对应于 hse_theme_db 的 type',
  `theme_status` tinyint(2) NOT NULL DEFAULT '0' COMMENT '主题状态：0：上架，1：下架',
  `theme_expire_time` datetime NOT NULL COMMENT '到期时间',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_theme` (`store_guid`,`product_guid`,`terminal_guid`,`theme_guid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- -----------------------------------------------------
-- Table `hss_r_user_role`
-- -----------------------------------------------------
CREATE TABLE `hss_r_user_role` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `guid` varchar(50) NOT NULL COMMENT 'guid',
  `user_guid` varchar(50) NOT NULL COMMENT '员工guid',
  `role_guid` varchar(50) NOT NULL COMMENT '角色guid',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_role` (`user_guid`,`role_guid`),
  UNIQUE KEY `uk_guid` (`guid`)
) ENGINE=InnoDB AUTO_INCREMENT=671 DEFAULT CHARSET=utf8mb4 COMMENT='用户-角色表';

-- -----------------------------------------------------
-- Table `hss_role`
-- -----------------------------------------------------
CREATE TABLE `hss_role` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `guid` varchar(50) NOT NULL COMMENT 'guid',
  `name` varchar(50) NOT NULL COMMENT '角色名称：必填，1-16个字',
  `is_enable` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用（默认为1-已启用）',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除（默认为0-未删除）',
  `create_staff_guid` varchar(50) NOT NULL COMMENT '创建人guid：必填',
  `modified_staff_guid` varchar(50) NOT NULL COMMENT '更新人guid：必填',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间：必填',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间：必填',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_role_guid` (`guid`)
) ENGINE=InnoDB AUTO_INCREMENT=113 DEFAULT CHARSET=utf8mb4 COMMENT='角色表';


-- -----------------------------------------------------
-- Table `hss_role_source`
-- -----------------------------------------------------
CREATE TABLE `hss_role_source` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `role_guid` varchar(50) NOT NULL COMMENT '角色guid',
  `terminal_guid` varchar(50) NOT NULL COMMENT '终端guid',
  `terminal_code` varchar(50) NOT NULL COMMENT '终端code',
  `terminal_name` varchar(50) NOT NULL COMMENT '终端名称',
  `menu_guid` varchar(50) NOT NULL COMMENT '菜单guid',
  `source_guid` varchar(50) NOT NULL COMMENT '资源guid',
  `source_code` varchar(50) NOT NULL COMMENT '服务资源编码',
  `source_url` text NOT NULL COMMENT '服务资源url',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `idx_rmt` (`role_guid`,`menu_guid`,`terminal_guid`)
) ENGINE=InnoDB AUTO_INCREMENT=93720 DEFAULT CHARSET=utf8mb4 COMMENT='角色-资源表';


-- -----------------------------------------------------
-- Table `hss_store_source`
-- -----------------------------------------------------
CREATE TABLE `hss_store_source` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `store_guid` varchar(50) DEFAULT NULL COMMENT '门店guid',
  `product_guid` varchar(50) DEFAULT NULL COMMENT '产品guid',
  `charge_guid` varchar(50) DEFAULT NULL,
  `terminal_guid` varchar(50) DEFAULT NULL COMMENT '终端guid',
  `terminal_name` varchar(50) DEFAULT NULL COMMENT '终端名称',
  `terminal_code` varchar(50) DEFAULT NULL COMMENT '终端code',
  `module_guid` varchar(50) DEFAULT NULL COMMENT '模块guid（存云端最底级模块guid）',
  `module_name` varchar(50) DEFAULT NULL COMMENT '模块名称（存云端最底级模块名）',
  `module_type` char(1) DEFAULT NULL COMMENT '模块类型（0-基础类业务，1-权限类业务）',
  `page_title` varchar(50) DEFAULT NULL COMMENT '跳转页标题',
  `page_url` varchar(100) DEFAULT NULL COMMENT '跳转页url',
  `source_guid` varchar(50) DEFAULT NULL COMMENT '资源guid,唯一',
  `source_name` varchar(50) DEFAULT NULL COMMENT '资源名称',
  `source_code` varchar(50) DEFAULT NULL COMMENT '资源code',
  `source_url` text COMMENT '资源url',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据创建时间',
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `idx_mg_tc` (`module_guid`,`terminal_code`),
  KEY `idx_tc` (`terminal_code`),
  KEY `idx_sg` (`source_guid`)
) ENGINE=InnoDB AUTO_INCREMENT=3603194 DEFAULT CHARSET=utf8mb4 COMMENT='企业/门店关联资源表';


-- -----------------------------------------------------
-- Table `hss_user`
-- -----------------------------------------------------
CREATE TABLE `hss_user` (
  `id` bigint(64) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `guid` varchar(50) NOT NULL COMMENT '用户GUID',
  `enterprise_no` char(8) NOT NULL COMMENT '企业编码：必填 8个数字',
  `account` char(6) NOT NULL COMMENT '员工账号：必填，3-6个数字',
  `password` varchar(50) NOT NULL COMMENT '员工密码：必填，不超过50个数字(25个中文)',
  `auth_code` char(6) NOT NULL COMMENT '授权码：必填，6位',
  `name` varchar(50) NOT NULL COMMENT '姓名：必填，2-20字',
  `phone` varchar(20) NOT NULL COMMENT '手机号：必填，11位手机号',
  `org_guid` varchar(50) DEFAULT NULL COMMENT '用户所属组织GUID',
  `office_code` varchar(50) DEFAULT NULL COMMENT '用户职位编码',
  `office_name` varchar(50) DEFAULT NULL COMMENT '用户职位名称',
  `id_card_no` varchar(20) DEFAULT NULL COMMENT '员工身份证号码：选填，18位',
  `id_card_address` varchar(200) DEFAULT NULL COMMENT '员工身份证地址：选填，不超过200个数字(100个中文)',
  `address` varchar(200) DEFAULT NULL COMMENT '员工居住地址：选填，不超过200个数字(100个中文)',
  `birthday` datetime DEFAULT NULL COMMENT '员工生日：选填',
  `on_boarding_time` datetime DEFAULT NULL COMMENT '员工入职时间：选填',
  `discount_threshold` decimal(10,2) DEFAULT NULL COMMENT '整单折扣阈值\n',
  `allowance_threshold` decimal(10,2) DEFAULT NULL COMMENT '整单让价阈值',
  `product_discount_threshold` decimal(10,2) DEFAULT NULL COMMENT '单品折扣阈值',
  `refund_threshold` decimal(10,2) DEFAULT NULL COMMENT '退款金额差值阈值',
  `roles_distributable` text COMMENT '用户可分配的角色id，逗号分割',
  `create_staff_guid` varchar(50) NOT NULL COMMENT '创建人GUID：必填',
  `update_staff_guid` varchar(50) NOT NULL COMMENT '更新人GUID：必填',
  `is_enable` tinyint(1) unsigned NOT NULL COMMENT '是否启用：0=禁用，1=启用',
  `is_deleted` tinyint(1) unsigned NOT NULL COMMENT '是否删除：0=未删除，1=已删除',
  `external_version` int(32) unsigned NOT NULL DEFAULT '0' COMMENT '外部版本号：每次外部操作版本号+1，内部操作不处理',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_guid` (`guid`)
) ENGINE=InnoDB AUTO_INCREMENT=168 DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- -----------------------------------------------------
-- Table `hss_user_data`
-- -----------------------------------------------------
CREATE TABLE `hss_user_data` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `rule_guid` varchar(50) NOT NULL COMMENT '规则GUID,唯一',
  `user_guid` varchar(50) NOT NULL COMMENT '用户GUID',
  `store_guid` varchar(50) DEFAULT NULL COMMENT '门店guid',
  `store_name` varchar(50) DEFAULT NULL COMMENT '门店名称',
  `organization_guid` varchar(50) DEFAULT NULL COMMENT '组织guid',
  `organization_name` varchar(50) DEFAULT NULL COMMENT '组织名称',
  `region_code` varchar(50) DEFAULT NULL COMMENT '区域code',
  `region_name` varchar(50) DEFAULT NULL COMMENT '区域名称\n',
  `brand_guid` varchar(50) DEFAULT NULL COMMENT '品牌guid',
  `brand_name` varchar(50) DEFAULT NULL,
  `is_deleted` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2110 DEFAULT CHARSET=utf8mb4 COMMENT='用户管理的组织-区域-品牌表';