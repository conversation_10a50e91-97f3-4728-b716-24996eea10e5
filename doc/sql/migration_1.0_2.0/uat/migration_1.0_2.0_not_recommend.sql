#hsp_printer
alter table hsp_printer change column store_guid store_guid VARCHAR(50) NOT NULL COMMENT '门店GUID';
alter table hsp_printer change column store_name store_name VARCHAR(50) NULL COMMENT '门店名称';
alter table hsp_printer change column device_id device_id VARCHAR(20) NOT NULL COMMENT '设备编号';
alter table hsp_printer change column printer_name printer_name VARCHAR(50) NOT NULL COMMENT '打印机名称';
alter table hsp_printer change column business_type business_type TINYINT(8) NOT NULL COMMENT '打印业务类型; 参数: 1/前台打印; 2后厨打印; 3/标签打印';
alter table hsp_printer change column printer_type printer_type TINYINT(8) NOT NULL COMMENT '打印机类型; 可选参数: 0/网络打印机; 1/本机; 2/usb打印机';
alter table hsp_printer change column printer_ip printer_ip VARCHAR(20) NULL COMMENT '打印机ip';
alter table hsp_printer change column printer_port printer_port INT(32) NULL COMMENT '打印端口';
alter table hsp_printer change column print_count print_count TINYINT(8) NOT NULL COMMENT '打印次数';
alter table hsp_printer change column print_page print_page CHAR(10) NOT NULL COMMENT '打印纸张类型';
alter table hsp_printer change column print_cut print_cut TINYINT(8) NOT NULL COMMENT '打印方式(切纸方式);  参数: 1/整单; 2/一菜一单; 3/一种类型一单; 4/一份数量一单; 默认1/整单';
alter table hsp_printer change column create_time gmt_create DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间';
alter table hsp_printer change column update_time gmt_modified DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录修改时间';
alter table hsp_printer drop column guid;
alter table hsp_printer drop column invoice_type;
alter table hsp_printer drop column create_staff_guid;
alter table hsp_printer drop column update_staff_guid;
alter table hsp_printer drop index uk_guid;
alter table hsp_printer drop index idx_create_time;
alter table hsp_printer drop index idx_store_guid;
alter table hsp_printer add unique uk_printer_guid(printer_guid);高频,已定
alter table hsp_printer add index idx_query_printer(store_guid, invoice_type, device_id); 高频,已定
alter table hsp_printer add index idx_store_master(store_guid, is_master); 低频,暂定
alter table hsp_printer add index idx_device_biz(device_id, business_type); 低频,暂定
alter table hsp_printer add index idx_store_biz(store_guid, business_type); 低频,暂定

#hsp_printer_area
alter table hsp_printer_area change column id id BIGINT(64) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID';
alter table hsp_printer_area change column guid guid VARCHAR(50) NOT NULL COMMENT '唯一标识';
alter table hsp_printer_area change column store_guid store_guid VARCHAR(50) NOT NULL COMMENT '门店GUID';
alter table hsp_printer_area change column printer_guid printer_guid VARCHAR(50) NOT NULL COMMENT '打印机GUID';
alter table hsp_printer_area change column area_guid area_guid VARCHAR(50) NOT NULL COMMENT '区域GUID';
alter table hsp_printer_area change column area_name area_name VARCHAR(50) NULL COMMENT '区域名称';
alter table hsp_printer_area change column gmt_create gmt_create DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间';
alter table hsp_printer_area change column gmt_modified gmt_modified DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间';
alter table hsp_printer_area drop index uk_guid;
alter table hsp_printer_area add unique uk_guid(guid);
alter table hsp_printer_area add index idx_printer_area(printer_guid, area_guid);

#hsp_printer_item
alter table hsp_printer_dish RENAME TO hsp_printer_item;
alter table hsp_printer_item change column id id BIGINT(64) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID';
alter table hsp_printer_item change column link_guid guid VARCHAR(50) NOT NULL COMMENT '唯一标识';
alter table hsp_printer_item change column dish_guid item_guid VARCHAR(50) NULL DEFAULT NULL COMMENT '商品GUID';
alter table hsp_printer_item change column dish_name item_name VARCHAR(50) NULL DEFAULT NULL COMMENT '商品名称';
alter table hsp_printer_item change column gmt_create gmt_create DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间';
alter table hsp_printer_item change column gmt_modified gmt_modified DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间';
alter table hsp_printer_item drop index uk_link_guid;
alter table hsp_printer_item add unique uk_guid(guid);
alter table hsp_printer_item add index idx_printer_item(printer_guid, item_guid);

#hsp_printer_invoice
DROP TABLE IF EXISTS hsp_printer_invoice;
CREATE TABLE IF NOT EXISTS `default_schema`.`hsp_printer_invoice` (
  `id` BIGINT(64) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `guid` VARCHAR(50) NOT NULL COMMENT '唯一标识',
  `store_guid` VARCHAR(50) NOT NULL COMMENT '门店GUID',
  `printer_guid` VARCHAR(50) NOT NULL COMMENT '打印机GUID',
  `invoice_type` TINYINT(8) UNSIGNED NOT NULL COMMENT '票据类型',
  `invoice_name` VARCHAR(50) NULL COMMENT '票据名称',
  `gmt_create` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `gmt_modified` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  PRIMARY KEY (`id`),
  UNIQUE INDEX `uk_guid` (`guid` ASC),
  INDEX `idx_printer_invoice` (`printer_guid` ASC, `invoice_type` ASC))
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8
COMMENT = '打印机票据'

#hsp_print_record
alter table hsp_print_record change column id id BIGINT(64) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID';
alter table hsp_print_record add column store_guid VARCHAR(50) NOT NULL COMMENT '门店GUID' after record_guid;
alter table hsp_print_record add column device_id VARCHAR(50) NOT NULL COMMENT '生成该打印记录的设备ID' after store_guid;
alter table hsp_print_record change column type_code invoice_type TINYINT(8) NOT NULL COMMENT '打印票据类型';
alter table hsp_print_record change column printer_guid printer_guid VARCHAR(50) NOT NULL COMMENT '打印机guid';
alter table hsp_print_record change column print_status print_status TINYINT(8) NOT NULL DEFAULT 0 COMMENT '0/打印中;1/打印成功;2/打印失败';
alter table hsp_print_record change column create_staff_guid create_staff_guid VARCHAR(50) NOT NULL COMMENT '创建该条记录的员工id';
alter table hsp_print_record drop column update_staff_guid;
alter table hsp_print_record change column create_time gmt_create DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间';
alter table hsp_print_record change column update_time gmt_modified DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '数据更新时间';
alter table hsp_print_record drop index idx_create_time;
alter table hsp_print_record add unique uk_record_guid(record_guid);
alter table hsp_print_record add index idx_list_record(printer_guid, device_id, print_status, is_deleted);



