-- My<PERSON><PERSON> Script generated by <PERSON><PERSON><PERSON> Workbench
-- 2019年02月12日 星期二 09时55分22秒
-- Model: New Model    Version: 1.0
-- MySQL Workbench Forward Engineering

SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0;
SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0;
SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='TRADITIONAL,ALLOW_INVALID_DATES';

-- -----------------------------------------------------
-- Table `hsp_printer`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `hsp_printer` ;

CREATE TABLE IF NOT EXISTS `hsp_printer` (
  `id` BIGINT(64) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `store_guid` VARCHAR(50) NOT NULL COMMENT '门店GUID',
  `store_name` VARCHAR(50) NULL COMMENT '门店名称',
  `device_id` VARCHAR(20) NOT NULL COMMENT '设备编号',
  `printer_guid` VARCHAR(50) NOT NULL COMMENT '打印机GUID',
  `printer_name` VARCHAR(50) NOT NULL COMMENT '打印机名称',
  `business_type` TINYINT(8) NOT NULL COMMENT '打印业务类型; 参数: 0/前台打印; 1后厨打印; 2/标签打印',
  `printer_type` TINYINT(8) NOT NULL COMMENT '打印机类型; 可选参数: 0/本机; 1/网络打印机; 2/usb打印机',
  `printer_ip` VARCHAR(20) NULL COMMENT '打印机ip',
  `printer_port` INT(32) NULL COMMENT '打印端口',
  `print_count` TINYINT(8) NOT NULL COMMENT '打印次数',
  `print_page` CHAR(10) NOT NULL COMMENT '打印纸张类型; 参数: 80; 58; 40*30; 30*20',
  `print_cut` TINYINT(8) NOT NULL DEFAULT 0 COMMENT '打印方式(切纸方式);  参数: 0/整单; 1/一菜一单; 2/一种类型一单; 3/一份数量一单; 默认0/整单',
  `is_master` TINYINT(1) UNSIGNED NOT NULL COMMENT '主',
  `gmt_create` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `gmt_modified` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '数据更新时间',
  PRIMARY KEY USING BTREE (`id`),
  UNIQUE INDEX `uk_printer_guid` (`printer_guid` ASC),
  INDEX `idx_query_printer` (`store_guid` ASC, `device_id` ASC),
  INDEX `idx_store_master` (`store_guid` ASC, `is_master` ASC),
  INDEX `idx_device_biz` (`device_id` ASC, `business_type` ASC),
  INDEX `idx_store_biz` (`store_guid` ASC, `business_type` ASC))
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8mb4
COMMENT = '打印机'
ROW_FORMAT = DYNAMIC;


-- -----------------------------------------------------
-- Table `hsp_printer_area`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `hsp_printer_area` ;

CREATE TABLE IF NOT EXISTS `hsp_printer_area` (
  `id` BIGINT(64) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `guid` VARCHAR(50) NOT NULL COMMENT '唯一标识',
  `store_guid` VARCHAR(50) NOT NULL COMMENT '门店GUID',
  `printer_guid` VARCHAR(50) NOT NULL COMMENT '打印机GUID',
  `area_guid` VARCHAR(50) NOT NULL COMMENT '区域GUID',
  `area_name` VARCHAR(50) NULL COMMENT '区域名称',
  `gmt_create` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `gmt_modified` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  PRIMARY KEY USING BTREE (`id`),
  UNIQUE INDEX `uk_guid` (`guid` ASC),
  INDEX `idx_printer_area` (`printer_guid` ASC, `area_guid` ASC))
ENGINE = InnoDB
AUTO_INCREMENT = 37
DEFAULT CHARACTER SET = utf8mb4
COMMENT = '打印机区域'
ROW_FORMAT = DYNAMIC;


-- -----------------------------------------------------
-- Table `hsp_printer_item`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `hsp_printer_item` ;

CREATE TABLE IF NOT EXISTS `hsp_printer_item` (
  `id` BIGINT(64) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `guid` VARCHAR(50) NOT NULL COMMENT '唯一标识',
  `store_guid` VARCHAR(50) NOT NULL COMMENT '门店GUID',
  `printer_guid` VARCHAR(50) NOT NULL COMMENT '打印机GUID',
  `item_guid` VARCHAR(50) NOT NULL COMMENT '商品GUID',
  `item_name` VARCHAR(50) NULL DEFAULT NULL COMMENT '商品名称',
  `gmt_create` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `gmt_modified` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  PRIMARY KEY USING BTREE (`id`),
  UNIQUE INDEX `uk_guid` (`guid` ASC),
  INDEX `idx_printer_item` (`printer_guid` ASC, `item_guid` ASC))
ENGINE = InnoDB
AUTO_INCREMENT = 488
DEFAULT CHARACTER SET = utf8mb4
COMMENT = '打印机商品'
ROW_FORMAT = DYNAMIC;


-- -----------------------------------------------------
-- Table `hsp_print_record`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `hsp_print_record` ;

CREATE TABLE IF NOT EXISTS `hsp_print_record` (
  `id` BIGINT(64) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `record_uid` VARCHAR(50) NOT NULL COMMENT '打印UID',
  `record_guid` VARCHAR(40) NOT NULL COMMENT '打印记录guid',
  `store_guid` VARCHAR(50) NOT NULL COMMENT '门店GUID',
  `device_id` VARCHAR(50) NOT NULL COMMENT '生成该打印记录的设备ID',
  `invoice_type` TINYINT(8) NOT NULL COMMENT '打印单据类型',
  `printer_guid` VARCHAR(50) NOT NULL COMMENT '打印机guid',
  `print_status` TINYINT(8) NOT NULL DEFAULT 0 COMMENT '0/打印中;1/打印成功;2/打印失败',
  `print_content` TEXT NOT NULL,
  `create_staff_guid` VARCHAR(50) NOT NULL COMMENT '创建该条记录的员工id',
  `is_deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否删除：0=未删除，1=已删除',
  `gmt_create` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `gmt_modified` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '数据更新时间',
  PRIMARY KEY USING BTREE (`id`),
  UNIQUE INDEX `uk_record_guid` USING BTREE (`record_guid` ASC),
  INDEX `idx_list_record` (`printer_guid` ASC, `device_id` ASC, `print_status` ASC, `is_deleted` ASC))
ENGINE = InnoDB
AUTO_INCREMENT = 161
DEFAULT CHARACTER SET = utf8mb4
COMMENT = '打印记录'
ROW_FORMAT = DYNAMIC;


-- -----------------------------------------------------
-- Table `hsp_printer_invoice`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `hsp_printer_invoice` ;

CREATE TABLE IF NOT EXISTS `hsp_printer_invoice` (
  `id` BIGINT(64) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `guid` VARCHAR(50) NOT NULL COMMENT '唯一标识',
  `store_guid` VARCHAR(50) NOT NULL COMMENT '门店GUID',
  `printer_guid` VARCHAR(50) NOT NULL COMMENT '打印机GUID',
  `invoice_type` TINYINT(8) UNSIGNED NOT NULL COMMENT '票据类型',
  `invoice_name` VARCHAR(50) NULL COMMENT '票据名称',
  `gmt_create` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `gmt_modified` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  PRIMARY KEY (`id`),
  UNIQUE INDEX `uk_guid` (`guid` ASC),
  INDEX `idx_printer_invoice` (`printer_guid` ASC, `invoice_type` ASC))
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8
COMMENT = '打印机票据'


SET SQL_MODE=@OLD_SQL_MODE;
SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS;
SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS;
