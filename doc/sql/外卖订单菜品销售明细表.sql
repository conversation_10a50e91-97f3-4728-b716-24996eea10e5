with takeaway as (
    select
        o.order_guid,
        i.item_guid as "order_item_guid",
        o.business_day,
        i.gmt_create,
        case o.brand_guid
            when '11d2895a-ad2e-4283-ba69-69d98bd1d0a5' then '何师烧烤'
            when '95a8319b-8297-4f06-a39f-a3c08799dad6' then '玉米熊'
            when '6919138141420912640' then '家婆抄手'
        end as "brand_name",
        o.store_guid,
        o.store_name,
        o.order_id,
        o.order_status,
        o.order_sub_type,
        o.is_refund_success,
        o.package_total,
        o.refund_status,
        i.third_sku_id,
        i.item_name,
        i.item_spec,
        i.item_property,
        i.item_unit,
        i.refund_count,
        case {{VIEW_TYPE}}
            when '0' then i.item_price
            when '1' then (
                case
                    when p.takeout_item_guid isnull then i.item_price
                    else p.sale_price
                end
            )
        end as "item_price",
        case {{VIEW_TYPE}}
            when '0' then i.item_count
            when '1' then (
                case
                    when p.takeout_item_guid isnull then i.item_count
                    else p.item_count * i.actual_item_count
                end
            )
        end as "item_count",
        case {{VIEW_TYPE}}
            when '0' then ( case when o.order_status = -1 then - i.item_count else - i.refund_count end)
            when '1' then (
                case
                    when p.takeout_item_guid isnull then ( case when o.order_status = -1 then - i.item_count else - i.refund_count end)
                    else p.item_count * ( case when o.order_status = -1 then - i.item_count else - i.refund_count end) * ( i.actual_item_count / i.item_count)
                end
            )
        end as "item_refund_count",
        case {{VIEW_TYPE}}
            when '0' then i.item_total
            when '1' then (
                case
                    when p.takeout_item_guid isnull then i.item_total
                    else p.item_count * i.actual_item_count * p.sale_price
                end
            )
        end as "item_total",
        case {{VIEW_TYPE}}
            when '0' then ( case when o.order_status = -1 then - i.item_count else - i.refund_count end) * i.item_price
            when '1' then (
                case
                    when p.takeout_item_guid isnull then ( case when o.order_status = -1 then - i.item_count else - i.refund_count end) * i.item_price
                    else p.item_count * ( case when o.order_status = -1 then - i.item_count else - i.refund_count end) * ( i.actual_item_count / i.item_count) * p.sale_price
                end
            )
        end as "item_refund_total",
        i.box_price,
        i.box_count,
        i.box_total,
        case {{VIEW_TYPE}}
            when '0' then hs.code
            when '1' then (
                case
                    when p.takeout_item_guid isnull then hs.code
                    else p.code
                end
            )
        end as "code",
        case {{VIEW_TYPE}}
            when '0' then i.erp_item_name
            when '1' then (
                case
                    when p.takeout_item_guid isnull then i.erp_item_name
                    else p.item_name
                end
            )
        end erp_item_name,
        case {{VIEW_TYPE}}
            when '0' then i.erp_item_price
            when '1' then (
                case
                    when p.takeout_item_guid isnull then i.erp_item_price
                    else p.sale_price
                end
            )
        end as "erp_item_price",
        case {{VIEW_TYPE}}
            when '0' then i.actual_item_count
            when '1' then (
                case
                    when p.takeout_item_guid isnull then i.actual_item_count
                    else p.item_count * i.actual_item_count
                end
            )
        end as "actual_item_count",
        case {{VIEW_TYPE}}
            when '0' then ( case when o.order_status = -1 then - i.item_count else - i.refund_count end) * ( i.actual_item_count / i.item_count)
            when '1' then (
                case
                    when p.takeout_item_guid isnull then ( case when o.order_status = -1 then - i.item_count else - i.refund_count end) * ( i.actual_item_count / i.item_count)
                    else p.item_count * ( case when o.order_status = -1 then - i.item_count else - i.refund_count end) * ( i.actual_item_count / i.item_count)
                end
            )
        end as "actual_refund_item_count",
        case {{VIEW_TYPE}}
            when '0' then i.takeaway_accounting_price
            when '1' then (
                case
                    when p.takeout_item_guid isnull then i.takeaway_accounting_price
                    else p.accounting_price
                end
            )
        end as "takeaway_accounting_price"
    from
        "hst_takeaway_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_takeout_item i
        LEFT JOIN "hst_takeaway_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_takeout_order o ON i.order_guid = o.order_guid
        LEFT JOIN "hsi_item_887f2181-eb06-4d77-b914-7c37c884952c_db".hsi_sku hs on i.erp_item_sku_guid = hs.guid
        LEFT JOIN (
            select
                rowid,
                takeout_item_guid,
                item_count,
                item_name,
                sku_guid,
                sale_price,
                accounting_price,
                code
            from (
                select
                    row_number() over(partition by takeout_item_guid) rowid,
                    takeout_item_guid,
                    item_count,
                    item_name,
                    sku_guid,
                    sale_price,
                    accounting_price,
                    code
                from "hst_takeaway_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_takeout_package
                where
                    is_delete = 0
                    and ods_delete_time isnull
            ) x
            where
                case {{VIEW_TYPE}}
                    when '0' then rowid = 1
                    when '1' then true
                end
        ) p on p.takeout_item_guid = i.item_guid,
        LATERAL (
            select
                public.getlist(
                    public.getacl(
                        $privileges_flag,
                        ($power_list)::text
                    ),
                    ($power_list)::text,
                    array[
                        [[ {{STORE_GUID_MULTI}} -- ]] '-1'
                        ,
                        [[ {{GROUP_STORE_GUID}} -- ]] '-1'
                    ]
                ) list,
                public.getacl(
                    $privileges_flag,
                    ($power_list)::text
                ) chmod
        ) acl
     where
        acl.chmod
        and case acl.list
            when 'true' then true
            when 'false' then false
            -- when 'false' then true  --debug
            else (o.store_guid = any(acl.list::text[]))
        end
     	[[and o.business_day between {{START_DATE}} AND {{END_DATE}}]]
     	[[and i.business_day between {{START_DATE}} AND {{END_DATE}}]]
     	[[and o.brand_guid in ({{BRAND_MULTI}}) ]]
     	[[and case when {{ORDER_STATUS_MULTI}} = 99 then 1=1 else o.order_status in ({{ORDER_STATUS_MULTI}}) end ]]
     	[[and o.refund_status in ({{REFUND_STATUS_MULTI}})]]
        [[and o.order_id = {{SEARCH}}::text]]
)


select
    temp.营业日,
    temp.创建时间,
    temp.品牌,
    temp.门店名称,
    temp.order_id "外卖订单ID",
    temp.订单状态,
    temp.订单子类型,
    temp.third_sku_id "外卖商品SKU",
    temp.商品名称,
    temp.商品规格,
    temp.商品属性,
    temp.商品单位,
    temp.商品单价,
    temp.商品数量,
    temp.商品消费合计,
    temp.餐盒单价,
    temp.餐盒数量,
    temp.餐盒金额,
    temp.门店商品编码,
    temp.门店商品名称,
    temp.门店商品单价,
    temp.门店商品数量,
    temp.门店商品金额合计,
    temp.核算单价,
    temp.核算商品总额
from
(
    -- 外卖订单菜品销售明细表
    SELECT
        t.order_guid,
        t.order_item_guid,
        t.business_day::text "营业日",
        t.gmt_create::text "创建时间",
        t.brand_name "品牌",
        t.store_guid "门店GUID",
        t.store_name "门店名称",
        t.order_id,
        CASE t.order_status
            WHEN -1 THEN '已取消'
            WHEN 0 THEN '待处理'
            WHEN 10 THEN '已接单'
            WHEN 20 THEN '配送中'
            WHEN 30 THEN '配送完成'
            WHEN 100 THEN '已完成'
        END AS "订单状态",
        CASE t.order_sub_type
            WHEN 0 THEN '美团'
            WHEN 1 THEN	'饿了么'
            WHEN 6 THEN	'赚餐外卖'
        END AS "订单子类型",
        t.third_sku_id,
        t.item_name "商品名称",
        t.item_spec "商品规格",
        t.item_property "商品属性",
        t.item_unit "商品单位",
        t.item_price "商品单价",
        t.item_count "商品数量",
        t.item_price * t.item_count "商品消费合计",
        t.box_price "餐盒单价",
        t.box_count "餐盒数量",
        t.box_total "餐盒金额",
        t.code "门店商品编码",
        t.erp_item_name "门店商品名称",
        t.erp_item_price "门店商品单价",
        t.actual_item_count "门店商品数量",
        t.erp_item_price * t.actual_item_count "门店商品金额合计",
        t.takeaway_accounting_price "核算单价",
        t.actual_item_count * t.takeaway_accounting_price "核算商品总额"
    FROM
        takeaway t

    union all

    -- 退款记录
    SELECT
        t.order_guid,
        t.order_item_guid,
        t.business_day::text "营业日",
        t.gmt_create::text "创建时间",
        t.brand_name "品牌",
        t.store_guid "门店GUID",
        t.store_name "门店名称",
        t.order_id,
        '已退款' AS "订单状态",
        CASE t.order_sub_type
            WHEN 0 THEN '美团'
            WHEN 1 THEN	'饿了么'
            WHEN 6 THEN	'赚餐外卖'
        END AS "订单子类型",
        t.third_sku_id,
        t.item_name "商品名称",
        t.item_spec "商品规格",
        t.item_property "商品属性",
        t.item_unit "商品单位",
        t.item_price "商品单价",
        t.item_count "商品数量",
        t.item_price * t.item_count "商品消费合计",
        t.box_price "餐盒单价",
        t.box_count "餐盒数量",
        t.box_total "餐盒金额",
        t.code "门店商品编码",
        t.erp_item_name "门店商品名称",
        t.erp_item_price "门店商品单价",
        t.actual_refund_item_count "门店商品数量",
        t.actual_refund_item_count * t.erp_item_price "门店商品金额合计",
        t.takeaway_accounting_price "核算单价",
        t.actual_refund_item_count * t.takeaway_accounting_price "核算商品总额"
    FROM
        takeaway t
    WHERE
        (t.refund_count > 0 or (t.order_status = '-1' and t.is_refund_success = 1 ))

    union all

    -- 堂食订单支付方式为 '美团生食闪购','抖音外卖'的订单
    SELECT
        append.order_guid,
        append.order_item_guid,
        append.营业日,
        append.创建时间,
        append.品牌,
        append.store_guid as "门店GUID",
        append.门店名称,
        append.order_id,
        append.订单状态,
        append.订单子类型,
        append.third_sku_id,
        append.商品名称,
        append.商品规格,
        append.商品属性,
        append.商品单位,
        append.商品单价,
        append.商品数量,
        append.商品消费合计,
        append.餐盒单价,
        append.餐盒数量,
        append.餐盒金额,
        append.门店商品编码,
        append.门店商品名称,
        append.门店商品单价,
        append.门店商品数量,
        append.门店商品金额合计,
        append.核算单价,
        append.核算商品总额
    FROM (
        SELECT
            o.guid::text as "order_guid",
            i.item_guid::text as order_item_guid,
            o.business_day::text "营业日",
            i.gmt_create::text "创建时间",
            case
                when o.store_name ~~* '%He%'  then '何师烧烤'
                when o.store_name ~~* '%YM%' then '玉米熊'
                when o.store_name ~~* '%家婆%' then '家婆抄手'
            end as "品牌",
            o.store_guid,
            o.store_name "门店名称",
            o.guid::text as "order_id",
            '已完成' AS "订单状态",
            r.payment_type_name AS "订单子类型",
            i.sku_guid,
            i.sku_guid as "third_sku_id",
            i.item_name "商品名称",
            i.sku_name "商品规格",
            '' "商品属性",
            i.unit "商品单位",
            i.price "商品单价",
            case i.parent_item_guid
                when '0' then i.current_count
                else i.current_count * i.package_default_count * ii.current_count
            end "商品数量",
            case i.parent_item_guid
                when '0' then i.price * (i.current_count + i.free_count)
                else i.price * (ii.current_count + ii.free_count)
            end "商品消费合计",
            0 "餐盒单价",
            0 "餐盒数量",
            0 "餐盒金额",
            case i.parent_item_guid
                when '0' then ii.code
                else i.code
            end "门店商品编码",
            i.item_name "门店商品名称",
            i.price "门店商品单价",
            case i.parent_item_guid
                when '0' then i.current_count
                else i.current_count * i.package_default_count * ii.current_count
            end "门店商品数量",
            case i.parent_item_guid
                when '0' then i.price * (i.current_count + i.free_count)
                else i.price * (ii.current_count + ii.free_count)
            end "门店商品金额合计",
            i.accounting_price 核算单价,
            case i.parent_item_guid
                when '0' then coalesce(i.accounting_price,0) * (i.current_count + i.free_count)
                else coalesce(i.accounting_price,0) * ((i.current_count + i.free_count) * i.package_default_count * ii.current_count)
            end 核算商品总额,
            acl.list
        FROM
            "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_transaction_record r
            LEFT JOIN "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_order o ON r.order_guid = o.guid and o.is_delete = 0
            LEFT join "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_order_item i on i.order_guid = o.guid and i.is_delete = 0
            LEFT join "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_order_item ii on i.parent_item_guid = ii.guid and i.is_delete = 0,
            LATERAL (
                select
                    public.getlist(
                        public.getacl(
                            $privileges_flag,
                            ($power_list)::text
                        ),
                        ($power_list)::text,
                        array[
                            [[ {{STORE_GUID_MULTI}} -- ]] '-1'
                            ,
                            [[ {{GROUP_STORE_GUID}} -- ]] '-1'
                        ]
                    ) list,
                    public.getacl(
                        $privileges_flag,
                        ($power_list)::text
                    ) chmod
            ) acl
        WHERE
            acl.chmod
            and r.payment_type = 10
            and r.payment_type_name in ('美团生食闪购','抖音外卖')
            and r.is_delete = 0
            and o.state = 4
            and case {{VIEW_TYPE}}
                when '0' then i.parent_item_guid = 0
                when '1' then i.item_type != 1
            end
            [[and r.is_delete in ({{REFUND_STATUS_MULTI}}) and r.is_delete = 2]]
            [[and r.business_day between {{START_DATE}} AND {{END_DATE}}]]
            [[and o.business_day between {{START_DATE}} AND {{END_DATE}}]]
            [[and i.business_day between {{START_DATE}} AND {{END_DATE}}]]
            [[
             and case
                when '11d2895a-ad2e-4283-ba69-69d98bd1d0a5' in({{BRAND_MULTI}}) and '95a8319b-8297-4f06-a39f-a3c08799dad6' in({{BRAND_MULTI}}) and '6919138141420912640' in({{BRAND_MULTI}}) then (o.store_name ~~* '%He%' or o.store_name ~~* '%YM%' or o.store_name ~~* '%家婆%' )
                when '11d2895a-ad2e-4283-ba69-69d98bd1d0a5' in({{BRAND_MULTI}}) and '95a8319b-8297-4f06-a39f-a3c08799dad6' in({{BRAND_MULTI}}) then (o.store_name ~~* '%He%' or o.store_name ~~* '%YM%')
                when '11d2895a-ad2e-4283-ba69-69d98bd1d0a5' in({{BRAND_MULTI}}) and '6919138141420912640' in({{BRAND_MULTI}}) then (o.store_name ~~* '%He%' or o.store_name ~~* '%家婆%')
                when '95a8319b-8297-4f06-a39f-a3c08799dad6' in({{BRAND_MULTI}}) and '6919138141420912640' in({{BRAND_MULTI}}) then (o.store_name ~~* '%YM%' or o.store_name ~~* '%家婆%')
                when '11d2895a-ad2e-4283-ba69-69d98bd1d0a5' in({{BRAND_MULTI}}) then o.store_name ~~* '%He%'
                when '95a8319b-8297-4f06-a39f-a3c08799dad6' in({{BRAND_MULTI}}) then o.store_name ~~* '%YM%'
                when '6919138141420912640' in({{BRAND_MULTI}}) then o.store_name ~~* '%家婆%'
                else 1=1 end
             ]]
            [[
            and case when {{ORDER_STATUS_MULTI}} = 99 then 1=1 else
                    case
                        when 100 in ({{ORDER_STATUS_MULTI}}) then o.state = 4
                        else o.state = -100
                    end
                end
            ]]
            [[and o.guid = {{SEARCH}}::bigint]]
            [[and o.store_guid in ({{STORE_GUID_MULTI}})]]
    ) append
    where
        append.商品数量 > 0

    union all

    -- 餐盒费 转菜品
    select
        (sec.js->>'order_guid') "order_guid",
        (sec.js->>'order_item_guid') "order_item_guid",
        (sec.js->>'business_day') "营业日",
        (sec.js->>'创建时间') "创建时间",
        (sec.js->>'brand_name') "品牌",
        sec.js->>'store_guid' "门店GUID",
        sec.js->>'store_name' "门店名称",
        sec.js->>'order_id' "order_id",
        sec.js->>'订单状态' "订单状态",
        sec.js->>'订单子类型' "订单子类型",
        null "third_sku_id",
        '餐盒费' "商品名称",
        '' "商品规格",
        '' "商品属性",
        '' "商品单位",
        null "商品单价",
        null "商品数量",
        null "商品消费合计",
        null "餐盒单价",
        null "餐盒数量",
        null "餐盒金额",
        '' "门店商品编码",
        '' "门店商品名称",
        null "门店商品单价",
        null "门店商品数量",
        null "门店商品金额合计",
        null "核算单价",
        (sec.jsx).value::numeric 核算商品总额
    from (
        select
            row_to_json(row) js,
            jsonb_each(
                row_to_json(row)::jsonb
                    -'rowid'
                    -'order_guid'
                    -'order_item_guid'
                    -'business_day'
                    -'创建时间'
                    -'brand_name'
                    -'store_guid'
                    -'store_name'
                    -'order_id'
                    -'订单状态'
                    -'订单子类型'
            ) jsx
        from (
            select
                row_number() over(partition by max(t.order_item_guid)) rowid,
                t.order_guid,
                '0' as order_item_guid,
                max(t.business_day) "business_day",
                max(t.gmt_create::text) "创建时间",
                max(t.brand_name) "brand_name",
                max(t.store_guid) "store_guid",
                max(t.store_name) "store_name",
                max(t.order_id) as "order_id",
                CASE max(t.order_status)
                    WHEN -1 THEN '已取消'
                    WHEN 0 THEN '待处理'
                    WHEN 10 THEN '已接单'
                    WHEN 20 THEN '配送中'
                    WHEN 30 THEN '配送完成'
                    WHEN 100 THEN '已完成'
                END AS "订单状态",
                CASE max(t.order_sub_type)
                    WHEN 0 THEN '美团'
                    WHEN 1 THEN	'饿了么'
                    WHEN 6 THEN	'赚餐外卖'
                END AS "订单子类型",
                max(t.package_total) 核算商品总额
            FROM
                takeaway t
            where
                t.package_total > 0
            group by t.order_guid
        ) row
        where
            row.rowid = 1
    ) sec

    union all

    -- 调整单菜品更换需要先扣减数量
    SELECT
        adjust.order_guid::text as "order_guid",
        adjust.order_item_guid,
        adjust.营业日,
        adjust.创建时间,
        adjust.品牌 ,
        adjust.store_guid "门店GUID",
        adjust.store_name "门店名称",
        adjust.order_id,
        adjust.订单状态,
        adjust.订单子类型,
        adjust.third_sku_id,
        adjust.商品名称,
        adjust.商品规格,
        adjust.商品属性,
        adjust.商品单位,
        adjust.商品单价,
        -adjust.商品数量,
        -adjust.商品消费合计,
        adjust.餐盒单价,
        adjust.餐盒数量,
        adjust.餐盒金额,
        adjust.门店商品编码,
        adjust.门店商品名称,
        adjust.门店商品单价,
        -adjust.门店商品数量,
        -adjust.门店商品金额合计,
        adjust.核算单价,
        -adjust.核算商品总额
    from (
        SELECT
            t.order_guid,
            t.order_item_guid,
            t.business_day::text "营业日",
            a.gmt_create::text "创建时间",
            t.brand_name "品牌",
            t.store_guid,
            t.store_name,
            t.order_id,
            '已完成' AS "订单状态",
            '调整单' AS "订单子类型",
            t.third_sku_id,
            t.item_name "商品名称",
            t.item_spec "商品规格",
            t.item_property "商品属性",
            t.item_unit AS "商品单位",
            t.item_price "商品单价",
            t.item_count "商品数量",
            t.item_price * t.item_count "商品消费合计",
            t.box_price "餐盒单价",
            t.box_count "餐盒数量",
            t.box_total "餐盒金额",
            t.code "门店商品编码",
            t.erp_item_name "门店商品名称",
            t.erp_item_price "门店商品单价",
            t.actual_item_count "门店商品数量",
            t.erp_item_price * t.actual_item_count "门店商品金额合计",
            t.takeaway_accounting_price "核算单价",
            t.actual_item_count * t.takeaway_accounting_price "核算商品总额",
            acl.list
        from
            (
                SELECT
                    DISTINCT ad.order_item_guid,
                    ad.gmt_create,
                    ad.adjust_type
                from
                    "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_adjust_order_details ad
                left join "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_adjust_order ao on ao.guid = ad.adjust_guid
                where
                    ad.is_delete = 0 and ad.adjust_type = 1 and ao.trade_mode = 2
            ) a
          left join takeaway t on t.order_item_guid::bigint = a.order_item_guid,
            LATERAL (
                select
                    public.getlist(
                        public.getacl(
                            $privileges_flag,
                            ($power_list)::text
                        ),
                        ($power_list)::text,
                        array[
                            [[ {{STORE_GUID_MULTI}} -- ]] '-1'
                            ,
                            [[ {{GROUP_STORE_GUID}} -- ]] '-1'
                        ]
                    ) list,
                    public.getacl(
                        $privileges_flag,
                        ($power_list)::text
                    ) chmod
            ) acl
        WHERE
            acl.chmod
            [[and t.business_day between {{START_DATE}} AND {{END_DATE}}]]
            [[
             and case
                when '11d2895a-ad2e-4283-ba69-69d98bd1d0a5' in({{BRAND_MULTI}}) and '95a8319b-8297-4f06-a39f-a3c08799dad6' in({{BRAND_MULTI}}) and '6919138141420912640' in({{BRAND_MULTI}}) then (a.store_name ~~* '%He%' or a.store_name ~~* '%YM%' or a.store_name ~~* '%家婆%' )
                when '11d2895a-ad2e-4283-ba69-69d98bd1d0a5' in({{BRAND_MULTI}}) and '95a8319b-8297-4f06-a39f-a3c08799dad6' in({{BRAND_MULTI}}) then (a.store_name ~~* '%He%' or a.store_name ~~* '%YM%')
                when '11d2895a-ad2e-4283-ba69-69d98bd1d0a5' in({{BRAND_MULTI}}) and '6919138141420912640' in({{BRAND_MULTI}}) then (a.store_name ~~* '%He%' or a.store_name ~~* '%家婆%')
                when '95a8319b-8297-4f06-a39f-a3c08799dad6' in({{BRAND_MULTI}}) and '6919138141420912640' in({{BRAND_MULTI}}) then (a.store_name ~~* '%YM%' or a.store_name ~~* '%家婆%')
                when '11d2895a-ad2e-4283-ba69-69d98bd1d0a5' in({{BRAND_MULTI}}) then a.store_name ~~* '%He%'
                when '95a8319b-8297-4f06-a39f-a3c08799dad6' in({{BRAND_MULTI}}) then a.store_name ~~* '%YM%'
                when '6919138141420912640' in({{BRAND_MULTI}}) then a.store_name ~~* '%家婆%'
                else 1=1 end
             ]]
            [[and case when {{ORDER_STATUS_MULTI}} = 99 then 1=1 else t.order_status in ({{ORDER_STATUS_MULTI}}) end ]]
            [[and t.order_id = {{SEARCH}}]]
            [[and a.store_guid in ({{STORE_GUID_MULTI}})]]
    ) adjust

    union all

    -- 调整单明细
    SELECT
        adjust.order_guid::text as "order_guid",
        adjust.order_item_guid,
        adjust.营业日,
        adjust.创建时间,
        adjust.品牌 ,
        adjust.store_guid "门店GUID",
        adjust.store_name "门店名称",
        adjust.order_id,
        adjust.订单状态,
        adjust.订单子类型,
        adjust.third_sku_id,
        adjust.商品名称,
        adjust.商品规格,
        adjust.商品属性,
        adjust.商品单位,
        adjust.商品单价,
        adjust.current_count "商品数量",
        adjust.商品单价 * adjust.current_count "商品消费合计",
        0 "餐盒单价",
        0 "餐盒数量",
        0 "餐盒金额",
        sku.code "门店商品编码",
        adjust.商品名称 门店商品名称,
        adjust.商品单价 门店商品单价,
        adjust.current_count "门店商品数量",
        adjust.商品单价 * adjust.current_count "门店商品金额合计",
        adjust.核算单价,
        adjust.核算单价 * adjust.current_count "核算商品总额"
    from (
        SELECT
            t.order_guid,
            t.order_item_guid,
            t.business_day::text "营业日",
            a.gmt_create::text "创建时间",
            t.brand_name "品牌",
            t.store_guid,
            t.store_name,
            t.order_id,
            '已完成' AS "订单状态",
            '调整单' AS "订单子类型",
            a.sku_guid,
            a.sku_guid as "third_sku_id",
            a.item_name "商品名称",
            a.sku_name "商品规格",
            '' "商品属性",
            a.unit AS "商品单位",
            a.price "商品单价",
            case when a.adjust_type = 1 and a.parent_item_guid = 0 then a.current_count
            when a.adjust_type = 1 and a.parent_item_guid != 0 then a.current_count * a.package_default_count * aa.current_count
            when a.adjust_type = 0 and a.parent_item_guid = 0 then a.current_count - t.item_count
            when a.adjust_type = 0 and a.parent_item_guid != 0 then a.current_count * a.package_default_count * aa.current_count - t.item_count
            end current_count,
            a.takeaway_accounting_price 核算单价,
            acl.list
        from
          "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_adjust_order_details a
          left join "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_adjust_order ao on ao.guid = a.adjust_guid
          left join "hst_trade_887f2181-eb06-4d77-b914-7c37c884952c_db".hst_adjust_order_details aa on a.parent_item_guid = aa.guid and aa.is_delete = 0
          left join takeaway t on t.order_item_guid::bigint = a.order_item_guid,
            LATERAL (
                select
                    public.getlist(
                        public.getacl(
                            $privileges_flag,
                            ($power_list)::text
                        ),
                        ($power_list)::text,
                        array[
                            [[ {{STORE_GUID_MULTI}} -- ]] '-1'
                            ,
                            [[ {{GROUP_STORE_GUID}} -- ]] '-1'
                        ]
                    ) list,
                    public.getacl(
                        $privileges_flag,
                        ($power_list)::text
                    ) chmod
            ) acl
        WHERE
            acl.chmod
            and a.is_delete = 0
            and ao.trade_mode = 2
            and case {{VIEW_TYPE}}
                when '0' then a.parent_item_guid = 0
                when '1' then a.item_type != 1
            end
            [[and t.business_day between {{START_DATE}} AND {{END_DATE}}]]
            [[
             and case
                when '11d2895a-ad2e-4283-ba69-69d98bd1d0a5' in({{BRAND_MULTI}}) and '95a8319b-8297-4f06-a39f-a3c08799dad6' in({{BRAND_MULTI}}) and '6919138141420912640' in({{BRAND_MULTI}}) then (a.store_name ~~* '%He%' or a.store_name ~~* '%YM%' orao.store_name ~~* '%家婆%' )
                when '11d2895a-ad2e-4283-ba69-69d98bd1d0a5' in({{BRAND_MULTI}}) and '95a8319b-8297-4f06-a39f-a3c08799dad6' in({{BRAND_MULTI}}) then (a.store_name ~~* '%He%' or a.store_name ~~* '%YM%')
                when '11d2895a-ad2e-4283-ba69-69d98bd1d0a5' in({{BRAND_MULTI}}) and '6919138141420912640' in({{BRAND_MULTI}}) then (a.store_name ~~* '%He%' or a.store_name ~~* '%家婆%')
                when '95a8319b-8297-4f06-a39f-a3c08799dad6' in({{BRAND_MULTI}}) and '6919138141420912640' in({{BRAND_MULTI}}) then (a.store_name ~~* '%YM%' or a.store_name ~~* '%家婆%')
                when '11d2895a-ad2e-4283-ba69-69d98bd1d0a5' in({{BRAND_MULTI}}) then a.store_name ~~* '%He%'
                when '95a8319b-8297-4f06-a39f-a3c08799dad6' in({{BRAND_MULTI}}) then a.store_name ~~* '%YM%'
                when '6919138141420912640' in({{BRAND_MULTI}}) then a.store_name ~~* '%家婆%'
                else 1=1 end
             ]]
            [[and case when {{ORDER_STATUS_MULTI}} = 99 then 1=1 else t.order_status in ({{ORDER_STATUS_MULTI}}) end ]]
            [[and t.order_id = {{SEARCH}}]]
            [[and a.store_guid in ({{STORE_GUID_MULTI}})]]

    ) adjust
    left join "hsi_item_887f2181-eb06-4d77-b914-7c37c884952c_db".hsi_sku sku on sku.guid = adjust.sku_guid

    union all

    -- 整单退款 退餐盒费 转菜品
    select
        (sec.js->>'order_guid') "order_guid",
        (sec.js->>'order_item_guid') "order_item_guid",
        (sec.js->>'business_day') "营业日",
        (sec.js->>'创建时间') "创建时间",
        (sec.js->>'brand_name') "品牌",
        sec.js->>'store_guid' "门店GUID",
        sec.js->>'store_name' "门店名称",
        sec.js->>'order_id' "order_id",
        sec.js->>'订单状态' "订单状态",
        sec.js->>'订单子类型' "订单子类型",
        null "third_sku_id",
        '餐盒费' "商品名称",
        '' "商品规格",
        '' "商品属性",
        '' "商品单位",
        null "商品单价",
        null "商品数量",
        null "商品消费合计",
        null "餐盒单价",
        null "餐盒数量",
        null "餐盒金额",
        '' "门店商品编码",
        '' "门店商品名称",
        null "门店商品单价",
        null "门店商品数量",
        null "门店商品金额合计",
        null "核算单价",
        (sec.jsx).value::numeric 核算商品总额
    from (
        select
            row_to_json(row) js,
            jsonb_each(
                row_to_json(row)::jsonb
                    -'rowid'
                    -'order_guid'
                    -'order_item_guid'
                    -'business_day'
                    -'创建时间'
                    -'brand_name'
                    -'store_guid'
                    -'store_name'
                    -'order_id'
                    -'订单状态'
                    -'订单子类型'
            ) jsx
        from (
            select
                row_number() over(partition by max(t.order_item_guid)) rowid,
                t.order_guid,
                '0' as order_item_guid,
                max(t.business_day) "business_day",
                max(t.gmt_create::text) "创建时间",
                max(t.brand_name) "brand_name",
                max(t.store_guid) "store_guid",
                max(t.store_name) "store_name",
                max(t.order_id) as "order_id",
                '已退款' "订单状态",
                CASE max(t.order_sub_type)
                    WHEN 0 THEN '美团'
                    WHEN 1 THEN	'饿了么'
                    WHEN 6 THEN	'赚餐外卖'
                END AS "订单子类型",
                max( -t.package_total) 核算商品总额
            FROM
                takeaway t
            where
                t.package_total > 0 and (t.refund_status = 2 or t.order_status = -1)
            group by t.order_guid
        ) row
        where
            row.rowid = 1
    ) sec

) temp
order by temp.order_guid desc, temp.order_item_guid desc, temp.商品消费合计 desc