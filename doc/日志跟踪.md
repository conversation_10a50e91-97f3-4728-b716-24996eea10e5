# 日志跟踪

## 节点

1. 11个重要日志节点

    - 打印消息推送入参
    
    - 打印消息推送消费
    
    - 打印任务消息推送，处理中
    
    - 打印任务消息推送，处理完毕
    
    - Client端拉取打印单
    
    - Client端更新打印结果：打印失败
    
    - Client端更新打印结果：打印成功
    
    - 打印成功消息推送，处理中
    
    - 打印成功消息推送，处理成功
    
    - 打印失败消息推送，处理中
    
    - 打印失败消息推送，处理成功

2. 3个业务异常（业务终结）节点

    - 未匹配到打印机
    
    - 商品列表为空
    
    - 打印机所有商品匹配时均发生错误

3. 2个代码异常节点

    - 套餐商品深拷贝时发生错误
    
    - 打印机商品匹配时发生错误

## 场景

1. 参数错误（开发环境）

    打印消息推送入参

    打印消息参数异常

2. 参数错误（非开发环境）

    打印消息推送入参
    
    打印消息推送消费

    打印消息参数异常

3. 未找到匹配的打印机

    打印消息推送入参
    
    打印消息推送消费
    
    未找到匹配的打印机

4. 打印成功

    打印消息推送入参
    
    打印消息推送消费
    
    打印任务消息推送，处理中
    
    打印任务消息推送，处理完毕
    
    Client端拉取打印单
    
    Client端更新打印结果：打印成功

5. 打印失败

    打印消息推送入参
    
    打印消息推送消费
    
    打印任务消息推送，处理中
    
    打印任务消息推送，处理完毕
    
    Client端拉取打印单
    
    Client端更新打印结果：打印失败
    
    打印失败消息推送，处理中
    
    打印失败消息推送，处理成功
    
6. 发送任务消息失败

    打印消息推送入参
    
    打印消息推送消费
    
    打印任务消息推送，处理中
    
7. Client未拉取数据

    打印消息推送入参
    
    打印消息推送消费
    
    打印任务消息推送，处理中
    
    打印任务消息推送，处理完毕
    
8. 未知错误
    
    打印消息推送入参
    
    打印消息推送消费
    
    打印消息消费异常

## 业务涉及的单据

searchKey组成规则：printUid(recordUid)_invoiceType (eg: 73806613685792701_0)

### 正餐

1. 点菜

    201904020053_0 菜品清单

    201904020053_80 点菜单

    201904020053_100 标签单
    

2. 退菜
    
    201904020053_81 退菜单

3. 打印预结单

    201904020053_5 预结单
    
    201904020053_6 并台预结单

4. 结帐

    201904020053_7 结账单

    201904020053_8 并台结账单
    
### 快餐

1. 点菜

    201904020053_80 点菜单

    201904020053_100 标签单

2. 退菜
    
    201904020053_81 退菜单

3. 结账

    201904020053_7 结账单
    
### 外卖

1. 新订单

    201904020053_20 外卖单

## 线上错误场景

### 外卖自动接单成功，打印失败

```
grep <erpName> info.log // 得到erpGuid和storeGuid
grep <storeGuid> error.log // 得到该门店的error信息

grep <erpName> warn.log | grep 打印失败 // 得到search_key
grep <search_key> info.log // 可以得到整体的打印流程
grep <print_uid(record_uid)> info.log // info.log 可以得到更详细的打印流程
```

### 语音播报打印失败，需要向客户解释具体失败原因

```bash
grep '\"status\":2' info.log
grep '\"status\":2}] ' info.log
```