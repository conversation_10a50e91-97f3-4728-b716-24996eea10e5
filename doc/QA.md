- Q：已知 storeName

1. 查找 storeGuid, storeName, enterpriseGuid, enterpriseName

    ```bash
    grep 'storeName' info.log
    ```

2. 记录 storeGuid, storeName, enterpriseGuid, enterpriseName，然后参见《已知 Erp 和 Store》

- Q：已知 storeGuid

1. 查找 storeGuid, storeName, enterpriseGuid, enterpriseName

    ```bash
    grep 'storeGuid' info.log 
    ```

2. 记录 storeGuid, storeName, enterpriseGuid, enterpriseName，然后参见《已知 Erp 和 Store》

- Q：已知 enterpriseName

1. 查找 storeGuid, storeName, enterpriseGuid, enterpriseName

    ```bash
    grep 'enterpriseName' info.log
    ```

2. 记录 storeGuid, storeName, enterpriseGuid, enterpriseName，然后参见《已知 Erp 和 Store》

- Q：已知 enterpriseGuid

1. 查找 storeGuid, storeName, enterpriseGuid, enterpriseName

    ```bash
    grep 'enterpriseGuid' info.log
    ```

2. 记录 storeGuid, storeName, enterpriseGuid, enterpriseName，然后参见《已知 Erp 和 Store》

- Q：已知 Erp 和 Store

略

- Q：已知printUid：订单的流水号、等

1. 拼接 searchKey

    `printUid_invoiceType`

2. 暴力搜索 searchKey

    ```bash
    grep searchKey error.log
    grep searchKey warn.log
    grep searchKey info.log
    ```

- Q：已知有打印单不出

1. 定位searchKey

    ```bash
    tail -200f error.log
    tail -200f warn.log
    ```

2. 根据时间筛选出异常日志对应的searchKey，然后

    ```bash
    grep searchKey info.log
    ```

- Q：已知device_no
1. 去 hsd_device_db.hsd_device 查找 device_guid

- Q：已知device_guid

1. 拼接 clientId

    `store-deviceTypeName-deviceGuid`

2. 拼接 topics

    `erpGuid/storeGuid/print:deviceGuid`

3. 前往 EMQ 控制台查询 clientId 和 topics