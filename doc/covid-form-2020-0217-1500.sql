DROP TABLE IF EXISTS `hsc_form_question`;
CREATE TABLE `hsc_form_question` (
  `guid` bigint(64) unsigned NOT NULL COMMENT '主键',
  `uid` varchar(45) NOT NULL COMMENT '持卷人标识',
  `name` varchar(45) DEFAULT NULL COMMENT '调查问卷名称',
  `city` varchar(45) DEFAULT NULL COMMENT '调查问卷城市',
  `description` text COMMENT '调查问卷目的',
  `communities` text COMMENT '社区内的小区，列表JSON',
  `questions` text COMMENT '保留字段，题目详情，列表JSON',
  `h5_url` varchar(200) DEFAULT NULL COMMENT '微信二维码链接',
  `mp_url` varchar(200) DEFAULT NULL COMMENT '小程序二维码链接',
  PRIMARY KEY (`guid`),
  KEY `idx_uid` (`uid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

DROP TABLE IF EXISTS `hsc_form_answer`;
CREATE TABLE `hsc_form_answer` (
  `guid` bigint(64) unsigned NOT NULL,
  `uid` varchar(45) NOT NULL,
  `question_guid` bigint(64) NOT NULL,
  `name` varchar(45) NOT NULL COMMENT '姓名',
  `gender` varchar(45) NOT NULL COMMENT '性别',
  `age` tinyint(8) unsigned NOT NULL COMMENT '年龄',
  `id_card` varchar(45) NOT NULL COMMENT '身份证号',
  `phone` varchar(45) NOT NULL COMMENT '联系方式',
  `community` varchar(100) NOT NULL COMMENT '所在小区',
  `building` varchar(100) NOT NULL COMMENT '楼栋信息',
  `house_type` varchar(45) NOT NULL COMMENT '房屋性质',
  `resident_num` tinyint(8) unsigned NOT NULL COMMENT '家中常住人数',
  `contact_with_infected_area` varchar(4) NOT NULL COMMENT '近期(1月8日)以来接触过从湖北、重庆等疫情高发地区来访人员',
  `contact_with_out_city` varchar(4) NOT NULL COMMENT '近期(1月29日)以来接触过外市的来访人员',
  `travel_outside` varchar(4) NOT NULL COMMENT '1月8日后是否去过市外',
  `travel_city` varchar(45) DEFAULT NULL COMMENT '所到城市',
  `travel_date` date DEFAULT NULL COMMENT '外出时间',
  `travel_mode` varchar(45) DEFAULT NULL COMMENT '出行方式',
  `travel_detail` varchar(45) DEFAULT NULL COMMENT '车牌或班次',
  `health` varchar(100) NOT NULL COMMENT '您及家人目前的身体状况',
  `temperature` decimal(5,2) unsigned NOT NULL COMMENT '今日测量体温(℃)',
  `symptom` varchar(100) NOT NULL COMMENT '以下症状',
  `treatment_date` date DEFAULT NULL COMMENT '诊疗时间',
  `treatment_agency` varchar(45) DEFAULT NULL COMMENT '诊疗机构',
  `treatment_result` varchar(45) DEFAULT NULL COMMENT '诊疗结果：确诊、留观、疑似、解除观察',
  `contact_people` text COMMENT '返(赴)所在区后接触的人员信息',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '填卷日期',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改日期',
  PRIMARY KEY (`guid`),
  KEY `idx_uid` (`uid`,`question_guid`),
  KEY `idx_question` (`question_guid`,`gmt_modified`),
  KEY `idx_community` (`question_guid`,`community`,`gmt_modified`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

DROP TABLE IF EXISTS `hsc_form_statistic`;
CREATE TABLE `hsc_form_statistic` (
  `guid` bigint(64) unsigned NOT NULL,
  `uid` varchar(45) NOT NULL,
  `question_guid` bigint(64) NOT NULL,
  `answer_guid` bigint(64) NOT NULL,
  `community` varchar(100) NOT NULL COMMENT '所在小区',
  `old_three_category` tinyint(1) NOT NULL DEFAULT '0' COMMENT '老三类小计',
  `hu_bei_native` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否湖北籍人员，根据idCard规则来判断',
  `live_travel_infected_area` tinyint(1) NOT NULL DEFAULT '0' COMMENT '近期有疫情居住或旅行史',
  `contact_with_infected_area` tinyint(1) NOT NULL DEFAULT '0' COMMENT '近期与疫区外来人员密切接触式',
  `contact_with_suspect_or_infected` tinyint(1) NOT NULL DEFAULT '0' COMMENT '与疑似和确诊病人有接触',
  `new_three_category` tinyint(1) NOT NULL DEFAULT '0' COMMENT '新三类小计',
  `contact_with_wu_han` tinyint(1) NOT NULL DEFAULT '0' COMMENT '与到过武汉的人有接触史',
  `travel_out_side` tinyint(1) NOT NULL DEFAULT '0' COMMENT '近期去过疫情或者外地旅游',
  `travel_in_chong_qing` tinyint(1) NOT NULL DEFAULT '0' COMMENT '重庆地区往(返)人员',
  `travel_in_zhe_guang_hu` tinyint(1) NOT NULL DEFAULT '0' COMMENT '浙江、广东、湖南地区往(返)人员',
  `observe_category` tinyint(1) NOT NULL DEFAULT '0' COMMENT '医学观察类小计',
  `under_observation` tinyint(1) NOT NULL DEFAULT '0' COMMENT '居家医学观察人员',
  `observe_fourteen` tinyint(1) NOT NULL DEFAULT '0' COMMENT '当日满14天人数',
  `observe_terminate` tinyint(1) NOT NULL DEFAULT '0' COMMENT '已解除观察人数',
  `travel_outside_city` tinyint(1) NOT NULL DEFAULT '0' COMMENT '市外往(返)人员',
  `travel_outside_province` tinyint(1) NOT NULL DEFAULT '0' COMMENT '省外往(返)人员',
  `travel_non_city_in_province` tinyint(1) NOT NULL DEFAULT '0' COMMENT '省内非本市往(返)人员',
  `car` tinyint(1) NOT NULL DEFAULT '0' COMMENT '外登记车辆',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '填卷日期',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '填卷修改日期',
  PRIMARY KEY (`guid`),
  KEY `idx_uid` (`uid`,`question_guid`),
  KEY `idx_question` (`question_guid`,`gmt_create`),
  KEY `idx_answer` (`answer_guid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;