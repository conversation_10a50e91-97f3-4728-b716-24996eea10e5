# 云呼对接文档

<!--### 1. 签名 <a id="signature"></a>-->
<!--> 1. 参数按字典顺序排序-->
<!-->>-->
<!-->> 第一步，设所有发送或者接收到的数据为集合M，将集合M内非空参数值的参数按照参数名ASCII码从小到大排序（字典序）-->
<!--````java-->
<!--    param.entrySet().stream()-->
<!--        .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue,-->
<!--                (u, v) -> {-->
<!--                    throw new IllegalStateException(String.format("Duplicate key %s", u));-->
<!--                }, TreeMap::new)-->
<!--        );-->
<!--````-->
<!--> 2. 格式拼接-->
<!-->> 第二步，使用URL键值对的格式（即key1=value1&key2=value2…）拼接成字符串stringA。-->
<!--````java-->
<!--    String pramStr = sortedParam.entrySet().stream()-->
<!--        .map(stringObjectEntry -> String.format("%s=%s", stringObjectEntry.getKey(), stringObjectEntry.getValue()))-->
<!--        .collect(Collectors.joining("&"));-->
<!--````-->

<!--> 3. 拼接AppSecret-->
<!-->> 第三步，拼接AppSecret（即key1=value1&key2=value2…appSecret=[appSecret]）拼接成字符串stringB。-->
<!--````java-->
<!--    String secretStr =String.format("%s&%s=%s",pramStr, KEY_APP_SECRET, appSecret);-->
<!--````-->
<!--> 4. 散列-->
<!-->> 第四步，对拼接好的字符串散列并用16进制编码(sha1Hex)-->
<!--````java-->
<!--    String signature = DigestUtils.sha1Hex(secretStr.getBytes());-->
<!--````-->


## 1、数据结构

### 参数

名称|类型|描述
|---|---|---|
params|Object|参数

### 参数示例

```json
{
    "params": {
        // business fields here
    }
}
```

### 返回值

名称|类型|描述
| --- | --- | --- |
returnCode | String | 响应码（0：成功，非0：失败）
returnMsg | String | 响应消息（具体业务，具体消息）
params | Object | 响应实体（详细的业务实体）

### 返回值示例

```json
{
    "returnCode": 0,
    "returnMsg": "成功",
}
{
    "returnCode": 1,
    "returnMsg": "意外错误"
}
```

## 2、接口协议

### Protocal: HTTP 1.1

### Host: https://mch-sit.holderzone.cn/gateway

### Method: POST

### Content-Type: application/json

## 3、验证商户是否开通预定服务接口

### Url: /hw/reserve/validate

### 参数

名称 | 类型 | 描述
| --- | --- | --- |
merchantPhone | String | 商家电话号码

### 参数示例

```json
{
    "params":{
	    "merchantPhone":"***********"
	}
}
```

### 返回值示例

```json
{
    "returnCode": 0,
    "returnMsg": "商户已开通此服务",
}
{
    "returnCode": 1,
    "returnMsg": "商户未开通此服务"
}
```

## 4、验证指定日期是否可预订，并返回该日期的可预订区间

### Url: /hw/reserve/validate_date

### 参数

名称 | 类型 | 描述
| --- | --- | --- |
merchantPhone | String | 商家电话号码
reserveDate | String | 预订日期：20191011
tableType | Integer | 桌台类型：0=大厅，1=包房（可选）
peopleTotal | Integer | 就餐人数范围（可选）

### 参数示例

```json
{
    "params":{
        "merchantPhone": "***********",
        "reserveDate": "20191011",
        "tableType": 0,
        "peopleTotal": 4
	}
}
```

### 返回值示例

```json
{
    "returnCode": 0,
    "returnMsg": "_BLANK_",
}
{
    "returnCode": 0,
    "returnMsg": "中午吗"
}
{
    "returnCode": 0,
    "returnMsg": "晚上吗"
}
{
    "returnCode": 0,
    "returnMsg": "中午还是晚上呢"
}
```

## 5、验证指定日期、区间是否可预订，并返回该日期区间的可预订时段

### Url: /hw/reserve/validate_interval

### 参数

名称 | 类型 | 描述
| --- | --- | --- |
merchantPhone | String | 商家电话号码
reserveDate | String | 预订日期：20191011
reserveInterval | Integer | 预定区间：0=中午，1=晚上
tableType | Integer | 桌台类型：0=大厅，1=包房（可选）
peopleTotal | Integer | 就餐人数范围（可选）

### 参数示例

````json
{
    "params":{
        "merchantPhone": "***********",
        "reserveDate": "20191011",
        "reserveInterval": 0,
        "tableType": 0,
        "peopleTotal": 4
	}
}
````

### 返回值示例

```json
{
    "returnCode": 0,
    "returnMsg": "_BLANK_",
}
{
    "returnCode": 0,
    "returnMsg": "7点"
}
{
    "returnCode": 0,
    "returnMsg": "7点、7点半"
}
```

## 6、验证指定日期时间是否可预订

### Url: /hw/reserve/validate_date_time

### 参数

名称 | 类型 | 描述
| --- | --- | --- |
merchantPhone | String | 商家电话号码
reserveTime | String | 预订时间日期：2019101112:00
tableType | Integer | 桌台类型：0=大厅，1=包房（可选）
peopleTotal | Integer | 就餐人数范围（可选）

### 参数示例

````json
{
    "params":{
        "merchantPhone": "***********",
        "reserveTime": "2019101112:00",
        "tableType": 0,
        "peopleTotal": 4
	}
}
````

### 返回值示例

```json
{
    "returnCode": 0,
    "returnMsg": "_BLANK_",
}
{
    "returnCode": 0,
    "returnMsg": "_SUCCESS_"
}
```

## 7、验证是否有满足人数的桌台可预订

### Url: /hw/reserve/validate_people

### 参数

名称 | 类型 | 描述
| --- | --- | --- |
merchantPhone | String | 商家电话号码
reserveTime | String | 预订时间日期：2019101112:00
peopleTotal | Integer | 就餐人数范围
tableType | Integer | 桌台类型：0=大厅，1=包房（可选）

### 参数示例

````json
{
    "params":{
        "merchantPhone": "***********",
        "reserveTime": "2019101112:00",
        "peopleTotal": 4,
        "tableType": 0
	}
}
````

### 返回值示例

```json
{
    "returnCode": 0,
    "returnMsg": "_BLANK_",
}
{
    "returnCode": 0,
    "returnMsg": "_SUCCESS_"
}
```

## 8、验证包房是否可提供、是否可预订

### Url: /hw/reserve/validate_room

### 参数

名称 | 类型 | 描述
| --- | --- | --- |
merchantPhone | String | 商家电话号码
reserveTime | String | 预订时间日期：2019101112:00（可选，不可与reserveDate同时为空）
reserveDate | String | 预订时间：20191011（可选，不可与reserveTime同时为空）
reserveInterval | Integer | 预定区间：0=中午，1=晚上（可选）
peopleTotal | Integer | 就餐人数范围（可选）

### 参数示例

````json
{
    "params":{
        "merchantPhone": "***********",
        "reserveTime": "2019101112:00",
        "reserveDate": "20191011",
        "reserveInterval": 0,
        "peopleTotal": 4
	}
}
````

### 返回值示例

```json
{
    "returnCode": 0,
    "returnMsg": "_BLANK_",
}
{
    "returnCode": 0,
    "returnMsg": "有包房"
}
{
    "returnCode": 0,
    "returnMsg": "包房已满"
}
```

## 9、根据所有信息验证是否可预订

### Url: /hw/reserve/validate_all

### 参数

名称 | 类型 | 描述
| --- | --- | --- |
merchantPhone | String | 商家电话号码
reserveTime | String | 预订时间日期：2019101112:00
peopleTotal | Integer | 就餐人数范围
tableType | Integer | 桌台类型：0=大厅，1=包房

### 参数示例

````json
{
    "params":{
        "merchantPhone": "***********",
        "reserveTime": "2019101112:00",
        "peopleTotal": 4,
        "tableType": 0
	}
}
````

### 返回值示例

```json
{
    "returnCode": 0,
    "returnMsg": "_BLANK_",
}
{
    "returnCode": 0,
    "returnMsg": "_SUCCESS_"
}
```

## 10、执行预订请求

### Url: /hw/reserve/launch

### 参数

名称 | 类型 | 描述
| --- | --- | --- |
merchantPhone | String | 商户电话号码
customerPhone | String | 客户手机号
reserveTime | String | 预订时间日期：2019101112:00
peopleTotal | Integer | 就餐人数
tableType | Integer | 桌台类型：0=大厅，1=包房

### 参数示例

````json
{
    "params":{
        {
          "merchantPhone": "***********",
          "customerPhone": "***********",
          "reserveTime": "2019101112:00",
          "peopleTotal": 5,
          "tableType":0
        }
    }
}
````

### 返回值示例

````json
{
    "returnCode": 0,
    "returnMsg": "成功",
    "params": {
        // reserving details
    }
}
{
    "returnCode": 1,
    "returnMsg": "超出预定时间段"
}
````

<p style="text-align:right">@Version 2.00</p>


