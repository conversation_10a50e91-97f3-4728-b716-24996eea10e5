
日期
S：请问是想预定今天、明天还是后天呢？
C：今天

区间
Holder： 
    ->  逻辑：3点以前任意一个时段的桌台没有全部被预定，则上午可用；3点以后任意一个时段的桌台没有全部被预定，则晚上可用
    -> _BLANK_/中午、晚上
S：很抱歉我们今天已经没法预订了，能改订其他日期吗？
S：请问是想预定中午还是晚上呢?
C：晚上

时段
Holder：
    ->  逻辑：3点以后，对于每一个时段，判断桌台是否全部预定，若全部预定，则排除。最后得到结果列表。
    -> _BLANK_/6点、6点半
S：很抱歉我们今天晚上时段已满，能改订其他时段吗？
S：目前有的时段是 6点、6点半，请问是想预定几点呢？
C：6点半

包房（可选）
C：你们有包房吗？
Holder：
    -> 逻辑：查询包房是否开启，并且包房下的桌台是否全部预订，若全部预订，者排除。最后得到结果列表
    -> _BLANK_/有包房/包房已满
S：很抱歉我们未提供包房，能改订大厅吗？
S：很抱歉我们包房已满，能改订大厅吗？
S：我们目前 有包房，请问是想预订包房吗？
C：是

人数
S：好的，一共有几位客人呢？
C：4人
Holder：
    -> 逻辑：根据 dateTime, roomType, peopleTotal 筛选出是否有可用的桌台
    -> _BLANK_/_SUCCESS_
S：很抱歉目前餐台已满，能改其他日期吗？
S：给您确认一下，您预订的是今天晚上6点半的包房，共4位客人，对吗？