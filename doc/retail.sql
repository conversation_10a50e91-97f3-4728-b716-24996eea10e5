# 修改表结构

CREATE TABLE `hss_product` (
    `id` bigint(64) unsigned NOT NULL AUTO_INCREMENT,
    `store_guid` varchar(50) DEFAULT NULL COMMENT '门店GUID',
    `product_guid` varchar(30) NOT NULL COMMENT '产品GUID',
    `product_name` varchar(255) DEFAULT '' COMMENT '产品名字',
    `charge_guid` varchar(50) DEFAULT NULL,
    `mchnt_type_code` varchar(50) NOT NULL DEFAULT '' COMMENT '商家经营类型，对于于 hsp_product 的 available_merchant',
    `gmt_product_start` datetime NOT NULL COMMENT '产品授权起始日期',
    `gmt_product_end` datetime DEFAULT NULL COMMENT '产品授权截止日期',
    `is_enable` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用:0/禁用,1/启用',
    `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除:0/删除,1/正常',
    `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_product` (`store_guid`, `product_guid`, `charge_guid`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4;

CREATE TABLE `hss_product_theme` (
    `id` bigint(64) unsigned NOT NULL AUTO_INCREMENT,
    `store_guid` varchar(50) DEFAULT NULL,
    `product_guid` varchar(30) NOT NULL COMMENT '产品GUID',
    `product_name` varchar(50) DEFAULT NULL COMMENT '产品名称',
    `terminal_guid` varchar(50) NOT NULL COMMENT '终端GUID',
    `terminal_name` varchar(50) DEFAULT NULL COMMENT '终端名称',
    `theme_guid` varchar(45) NOT NULL COMMENT '主题GUID',
    `theme_name` varchar(45) DEFAULT NULL COMMENT '主题名称',
    `theme_code` varchar(45) NOT NULL COMMENT '主题编码，对应于 hse_theme_db 的 type',
    `theme_status` tinyint(2) NOT NULL DEFAULT '0' COMMENT '主题状态：0：上架，1：下架',
    `theme_expire_time` datetime NOT NULL COMMENT '到期时间',
    `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_theme` (`store_guid`,`product_guid`,`terminal_guid`,`theme_guid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

# 迁移产品数据

insert into hss_product
    (store_guid, product_guid, product_name, charge_guid, mchnt_type_code, gmt_product_start, gmt_product_end, is_enable, is_deleted)
select
    store_guid, product_guid, product_name, '', '10001', gmt_product_start, gmt_product_end, 1, 0
from
    hss_store_source
group by
    store_guid, product_guid, product_name, gmt_product_start, gmt_product_end;

# 修改表结构

# ALTER TABLE `hss_store_source`
#     DROP COLUMN `enterprise_guid`,
#     DROP COLUMN `product_name`,
#     DROP COLUMN `gmt_product_end`,
#     DROP COLUMN `gmt_product_start`,
#     DROP COLUMN `gmt_sync`;

ALTER TABLE `hss_store_source`
    DROP COLUMN `enterprise_guid`,
    DROP COLUMN `gmt_sync`;

ALTER TABLE `hss_user`
    ADD COLUMN `product_discount_threshold` decimal(10, 2) NULL DEFAULT NULL COMMENT '单品折扣阈值' AFTER `allowance_threshold`;

ALTER TABLE `hss_user` ADD COLUMN `refund_threshold` decimal(10, 0) NULL DEFAULT NULL AFTER `gmt_modified`;

# 分割线---------------------------------

ALTER TABLE `hss_store_source`
    ADD COLUMN `charge_guid` VARCHAR(50) NULL DEFAULT NULL AFTER `product_guid`;

ALTER TABLE `hss_store_source`
    CHANGE COLUMN `source_url` `source_url` TEXT NULL DEFAULT NULL COMMENT '资源url' ;

ALTER TABLE `hss_role_source`
    CHANGE COLUMN `source_url` `source_url` TEXT NOT NULL COMMENT '服务资源url' ;

ALTER TABLE `hss_product_theme`
    ADD COLUMN `terminal_code` VARCHAR(50) NOT NULL AFTER `terminal_name`;

# 分割线---------------------------------

ALTER TABLE `hss_product` DROP INDEX `uk_product` ;

ALTER TABLE `hss_product`
    ADD COLUMN `product_type` TINYINT(8) NOT NULL DEFAULT 0 COMMENT '产品类型：0=时间，1=数量' AFTER `product_name`;

ALTER TABLE `hss_product`
    CHANGE COLUMN `gmt_product_start` `gmt_product_start` DATETIME NULL DEFAULT NULL COMMENT '产品授权起始日期' ;

# 分割线---------------------------------

ALTER TABLE `hss_menu`
    ADD UNIQUE INDEX `uk_menu` (`menu_guid` ASC),
    DROP INDEX `menu_guid_UNIQUE` ;

ALTER TABLE `hss_role`
    ADD UNIQUE INDEX `uk_role_guid` (`guid` ASC),
    DROP INDEX `guid_UNIQUE` ;

ALTER TABLE `hss_role_source`
    ADD INDEX `idx_rmt` (`role_guid` ASC, `menu_guid` ASC, `terminal_guid` ASC);

ALTER TABLE `hss_r_user_role`
    ADD UNIQUE INDEX `uk_guid` (`guid` ASC),
    ADD UNIQUE INDEX `uk_user_role` (`user_guid` ASC, `role_guid` ASC),
    DROP INDEX `idx_user_guid` ,
    DROP INDEX `guid_UNIQUE` ;

ALTER TABLE `hss_store_source`
    ADD INDEX `idx_mg_tc` (`module_guid` ASC, `terminal_code` ASC),
    ADD INDEX `idx_tc` (`terminal_code` ASC),
    ADD INDEX `idx_sg` (`source_guid` ASC);

ALTER TABLE `hss_user`
    ADD UNIQUE INDEX `uk_user_guid` (`guid` ASC),
    DROP INDEX `uk_guid` ;

ALTER TABLE `hss_user_data`
    ADD INDEX `idx_user_guid` (`user_guid` ASC);