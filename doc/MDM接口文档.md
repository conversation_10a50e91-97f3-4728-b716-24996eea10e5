#服务调用说明

##1.服务请求

    地址：
        http://${host}:${port}/mdm/${path}  或 http://${domain}/gateway/mdm/${path} 
        根据实际环境选择使用 http 或 https
        path = 接口地址
    
    签名规则：
        为保证数据传输过程中的数据真实性，完整性和不可抵赖，商户需要对数据进行数字签名，我司在接收签名数据之后进行签名校验。
        签名算法为MD5(32位)。签名是为了防止从商户系统提交的支付请求以及通知数据被非法篡改。根据接口报文内容，把除签名(signature)
        字段以外的所有非空字段参数按照ASCII先后顺序，依次按照“字段名=字段值”的方式用“&”符号连接，最后再拼接上“&developerKey=merKey”，
        merKey为MDM系统给平台分配的MD5key。使用MD5算法计算数字签名，密文值填入签名字段signature。
        签名样式如下：
            amount=1&body=test_body&clientIp=***********&mchntId=0000000001&mchntOrderNo=ceshi1502616056874&
            notifyUrl=http://www.baidu.com&payPowerId=1&subject=test_subject&version=api_1.0&developerKey=06762e4c71485ef49c8098b987ba891d
        将上面拼接的字符串进行md5签名得到签名密文值：57232c9fb90add19b914f674de244b84
        注意事项：
            1.没有值的参数无需传递，也无需包含到待签名数据中，否则下单请求时会报验签失败。签名时将字符转变成字节流时统一使用utf-8；
        对于所有我方接口响应给第三方调用者的结果，请不要写死，以免服务器增加返回字段，导致调用者接收失败。 
            2.如果请求类中包含map、list或其他实体类时，将该部分参数直接转为json字符串后参与签名

    参数要求：
        所有请求中必须有
        signature（签名） 和 developerId（开发者账号）
        
    请求头中必须携带商户标识（enterpriseGuid）
        
##2.api调用
###2.1 商品
**2.1.1 添加商品（post）**

    路径：/item/addItem 

| 参数 | 参数类型 | 是否必选 | 说明 |
|:----|:--------:|:-------:|:-----------:|
| developerId | String | true | 开发者账号 |
| signature | String | true | 签名 |
| request |  |  |  |
| request.guid | String | false | 平台主键 |
| request.thirdNo | String | true | 第三方唯一标识 |
| request.name | String | true | 商品名称 |
| request.typeGuid | String | true | 商品关联的分类GUID |
| request.storeGuid | String | false | 门店标识（门店和品牌标识不能同时为空, itemFrom = 0 时，不能为空）|
| request.brandGuid | String | false | 品牌标识（门店和品牌标识不能同时为空，itemFrom = 1 时，不能为空）|
| request.itemFrom | Integer | true | 商品来源（0：门店，1：品牌） |
| request.itemType | Integer | true | 商品类型：1.套餐（不称重，无规格），2多规格商品（多商品，不称重），3.称重商品（单商品，称重），4.单品。 |
| request.pinyin | String | true | 拼音简码 |
| request.pictureUrl | String | false | 图片路径数组json |

**2.1.2 批量添加商品（post）**

    路径：/item/addItemList (request为 list)
    参数详见2.1.1
    
**2.1.3 删除商品（post）**

    路径：/item/deleteItem   （guid 和 thirdNo 必填其一）

| 参数 | 参数类型 | 是否必选 | 说明 |
|:----|:--------:|:-------:|:-----------:|
| developerId | String | true | 开发者账号 |
| signature | String | true | 签名 |
| request |  |  |  |
| request.guid | String | false | 平台主键（guid） |
| request.thirdNo | String | false | 商品主键（thirdNo） |

**2.1.4 批量删除商品（post）**

    路径：/item/deleteItem  
    参数详见2.1.1  （request 为 list）

**2.1.5 查询商品（post）**

    路径：/item/selectItemList 

| 参数 | 参数类型 | 是否必选 | 说明 |
|:----|:--------:|:-------:|:-----------:|
| developerId | String | true | 开发者账号 |
| signature | String | true | 签名 |
| request |  |  |  |
| request.startTime | 开始时间 | true | yyyy-MM-dd HH:mm:ss |
| request.endTime | 结束时间 | true | yyyy-MM-dd HH:mm:ss |
| request.storeGuid | String | false | 门店标识 |
| request.brandGuid | String | false | 品牌标识 |

**2.1.6 更新商品（post）**

    路径：/item/updateItem  （guid 和 thirdNo 必填其一）

| 参数 | 参数类型 | 是否必选 | 说明 |
|:----|:--------:|:-------:|:-----------:|
| developerId | String | true | 开发者账号 |
| signature | String | true | 签名 |
| request |  |  |  |
| request.guid | String | false | 平台主键 |
| request.thirdNo | String | true | 第三方唯一标识 |
| request.name | String | true | 商品名称 |
| request.typeGuid | String | true | 商品关联的分类GUID |
| request.storeGuid | String | false | 门店标识（门店和品牌标识不能同时为空, itemFrom = 0 时，不能为空）|
| request.brandGuid | String | false | 品牌标识（门店和品牌标识不能同时为空，itemFrom = 1 时，不能为空）|
| request.itemFrom | Integer | true | 商品来源（0：门店，1：品牌） |
| request.itemType | Integer | true | 商品类型：1.套餐（不称重，无规格），2多规格商品（多商品，不称重），3.称重商品（单商品，称重），4.单品。 |
| request.pinyin | String | true | 拼音简码 |
| request.pictureUrl | String | false | 图片路径数组json |

**2.1.7 添加商品类型（post）**

    路径：/item_type/add （request 为 list）
    
| 参数 | 参数类型 | 是否必选 | 说明 |
|:----|:--------:|:-------:|:-----------:|
| developerId | String | true | 开发者账号 |
| signature | String | true | 签名 |
| request |  |  |  |
| request.guid | String | false | 主键 |
| request.thirdNo | String | true | 第三方唯一标识 |
| request.storeGuid | String | true | 门店GUID(typeFrom = 0时必填) |
| request.brandGuid | String | true | 品牌GUID(typeFrom = 1时必填) |
| request.name | String | true | 名称 |
| request.description | String | false | 分类描述,可为空 |
| request.typeFrom | Integer | true | 分类来源（0：门店，1：品牌） |

**2.1.8 更新商品类型（put）**

    路径：/item_type/update  （guid 和 thirdNo 任意传一个值）
    
| 参数 | 参数类型 | 是否必选 | 说明 |
|:----|:--------:|:-------:|:-----------:|
| developerId | String | true | 开发者账号 |
| signature | String | true | 签名 |5
| request |  |  |  |
| request.guid | String | false | 平台主键(guid) |
| request.thirdNo | String | true | 主键(thirdNo) |
| request.storeGuid | String | true | 门店GUID(typeFrom = 0时必填) |
| request.brandGuid | String | true | 品牌GUID(typeFrom = 1时必填) |
| request.name | String | true | 名称 |
| request.description | String | false | 分类描述,可为空 |
| request.typeFrom | Integer | true | 分类来源（0：门店，1：品牌） |

**2.1.9 删除商品类型（delete）**

    路径：/item_type/delete   （request 为 list）
    
| 参数 | 参数类型 | 是否必选 | 说明 |
|:----|:--------:|:-------:|:-----------:|
| developerId | String | true | 开发者账号 |
| signature | String | true | 签名 |
| request |  |  |  |
| request.guid | String | false | 平台主键(guid) |

**2.1.10 商品规格(SKU)添加（post）**

    路径：/item_sku/add  （request 为 list）
    
| 参数 | 参数类型 | 是否必选 | 说明 |
|:----|:--------:|:-------:|:-----------:|
| developerId | String | true | 开发者账号 |
| signature | String | true | 签名 |
| request |  |  |  |
| request.guid | String | false | 平台主键(guid) |
| request.thirdNo | String | true | 主键(thirdNo) |
| request.storeGuid | String | false | 门店GUID(skuFrom = 0时必填) |
| request.brandGuid | String | false | 品牌GUID(skuFrom = 1时必填) |
| request.itemGuid | String | true | 商品标识(商品表中 thirdNo) |
| request.skuFrom | Integer | true | 规格来源（0：门店，1：品牌） |
| request.upc | String | false | upc商品条码,可为空 |
| request.name | String | false | 规格名称(单品规格名称为空字符串) |
| request.code | String | false | 编号,可为空 |
| request.salePrice | BigDecimal | true | 售卖价格（参考价） |
| request.unit | String | true | 商品规格单位 |

**2.1.11 商品规格(SKU)更新（post）**

    路径：/item_sku/update  （guid 和 thirdNo 任意传一个值）
    参数详见2.1.1  

**2.1.12 商品规格(SKU)删除（post）**

    路径：/item_sku/update  （request 为 list）（guid 和 thirdNo 任意传一个值）
    
| 参数 | 参数类型 | 是否必选 | 说明 |
|:----|:--------:|:-------:|:-----------:|
| developerId | String | true | 开发者账号 |
| signature | String | true | 签名 |
| request |  |  |  |
| request.guid | String | false | 平台主键(guid) |
| request.thirdNo | String | false | 主键(thirdNo) |

###2.2 物料
**2.2.1 添加物料（post）**

    路径：/material

| 参数 | 参数类型 | 是否必选 | 说明 |
|:----|:--------:|:-------:|:-----------:|
| developerId | String | true | 开发者账号 |
| signature | String | true | 签名 |
| request |  |  |  |
| request.guid | String | false | 平台主键（guid） |
| request.thirdNo | String | true | 第三方唯一标识 |
| request.property | String | true | 物品性质 |
| request.name | String | true | 物品名称 |
| request.simpleName | String | false | 物品简称 |
| request.unit | String | true | 库存单位 |
| request.category | String | true | 存货类别（系统默认类别：原材料、半成品、在制品、产成品、替换件、低值易耗品） |
| request.financialCode | String | false | 财务编码 |
| request.salesPrice | BigDecimal | true | 销售价 |
| request.costPrice | BigDecimal | true | 成本价 |
| request.effectiveDate | Integer | false | 保质期 |
| request.storageMethod | String | false | 贮藏方式 |
| request.image | String | false | 图片 |

**2.2.2 批量添加物料（post）**

    路径：/material/batch (request为 list)
    参数详见2.2.1

**2.2.3 修改物料信息（put）**

    路径：/material  （guid 和 thirdNo 任意传一个值）

| 参数 | 参数类型 | 是否必选 | 说明 |
|:----|:--------:|:-------:|:-----------:|
| developerId | String | true | 开发者账号 |
| signature | String | true | 签名 |
| request |  |  |  |
| request.guid | String | false | 平台主键（guid） |
| request.thirdNo | String | false | 第三方唯一标识 |
| request.property | String | true | 物品性质 |
| request.name | String | true | 物品名称 |
| request.simpleName | String | false | 物品简称 |
| request.unit | String | true | 库存单位 |
| request.category | String | true | 存货类别（系统默认类别：原材料、半成品、在制品、产成品、替换件、低值易耗品） |
| request.financialCode | String | false | 财务编码 |
| request.salesPrice | BigDecimal | true | 销售价 |
| request.costPrice | BigDecimal | true | 成本价 |
| request.effectiveDate | Integer | false | 保质期 |
| request.storageMethod | String | false | 贮藏方式 |
| request.image | String | false | 图片 |

**2.2.4 删除物料（delete）**

    路径：/material （request 为 list） （guid 和 thirdNo 任意传一个值）

| 参数 | 参数类型 | 是否必选 | 说明 |
|:----|:--------:|:-------:|:-----------:|
| developerId | String | true | 开发者账号 |
| signature | String | true | 签名 |
| request |  |  |  |
| request.guid | String | false | 平台主键(guid) |
| request.thirdNo | String | false | 主键(thirdNo) |

**2.2.5 根据GUID查询物料信息（post）**

    路径：/material/info

| 参数 | 参数类型 | 是否必选 | 说明 |
|:----|:--------:|:-------:|:-----------:|
| developerId | String | true | 开发者账号 |
| signature | String | true | 签名 |
| request |  |  |  |
| request.guid | String | false | 平台主键(guid) |
| request.thirdNo | String | false | 主键(thirdNo) |

**2.2.6 根据条件查询物料信息（post）**

    路径：/material/query
    毫秒时间戳（startDate、endDate）和 时间格式（startTime、endTime）使用时任选其一即可

| 参数 | 参数类型 | 是否必选 | 说明 |
|:----|:--------:|:-------:|:-----------:|
| developerId | String | true | 开发者账号 |
| signature | String | true | 签名 |
| request |  |  |  |
| request.startDate | Long | false | 开始时间（毫秒时间戳） |
| request.endDate | Long | false | 结束时间（毫秒时间戳） |
| request.startTime | Date | false | 开始时间（yyyy-MM-dd HH:mm:ss）|
| request.endTime | Date | false | 结束时间（yyyy-MM-dd HH:mm:ss）|

**2.2.7 添加物料规格（post）**

    路径：/materialSpec

| 参数 | 参数类型 | 是否必选 | 说明 |
|:----|:--------:|:-------:|:-----------:|
| developerId | String | true | 开发者账号 |
| signature | String | true | 签名 |
| request |  |  |  |
| request.guid | String | false | 平台主键 |
| request.thirdNo | String | true | 第三方唯一标识 |
| request.name | String | true | 规格名称 |
| request.unit | String | true | 计量单位 |
| request.materialGuid | String | true | 关联的物料GUID |
| request.conversionRatio | BigDecimal | false | 换算比率 |
| request.barCode | String | false | 条码 |

**2.2.8 批量添加物料规格（post）**

    路径：/materialSpec/batch (request为 list)
    参数详见2.2.7
    
**2.2.9 更新物料规格（put）**

    路径：/materialSpec  （guid 和 thirdNo 任意传一个值）

| 参数 | 参数类型 | 是否必选 | 说明 |
|:----|:--------:|:-------:|:-----------:|
| developerId | String | true | 开发者账号 |
| signature | String | true | 签名 |
| request |  |  |  |
| request.guid | String | false | 主键 |
| request.thirdNo | String | false | 第三方唯一标识 |
| request.name | String | true | 规格名称 |
| request.unit | String | true | 计量单位 |
| request.materialGuid | String | true | 关联的物料GUID |
| request.conversionRatio | BigDecimal | false | 换算比率 |
| request.barCode | String | false | 条码 |
    
**2.2.10 删除物料规格（delete）**

    路径：/materialSpec （guid 和 thirdNo 任意传一个值） （request 为 list）

| 参数 | 参数类型 | 是否必选 | 说明 |
|:----|:--------:|:-------:|:-----------:|
| developerId | String | true | 开发者账号 |
| signature | String | true | 签名 |
| request |  |  |  |
| request.guid | String | false | 平台主键(guid) |
| request.thirdNo | String | false | 主键(thirdNo) |
    
**2.2.11 根据GUID查询物料规格信息（post）**

    路径：/materialSpec/findByGuid

| 参数 | 参数类型 | 是否必选 | 说明 |
|:----|:--------:|:-------:|:-----------:|
| developerId | String | true | 开发者账号 |
| signature | String | true | 签名 |
| request |  |  |  |
| request.guid | String | true | 主键（thirdNo） |
    
**2.2.12 根据GUID查询物料规格信息（post）**

    路径：/materialSpec/query

| 参数 | 参数类型 | 是否必选 | 说明 |
|:----|:--------:|:-------:|:-----------:|
| developerId | String | true | 开发者账号 |
| signature | String | true | 签名 |
| request |  |  |  |
| request.startDate | Long | false | 开始时间（毫秒时间戳） |
| request.endDate | Long | false | 结束时间（毫秒时间戳） |

##2.3 用户
**2.3.1 添加用户（post）**

    路径：/user

| 参数 | 参数类型 | 是否必选 | 说明 |
|:----|:--------:|:-------:|:-----------:|
| developerId | String | true | 开发者账号 |
| signature | String | true | 签名 |
| request |  |  |  |
| request.thirdNo | String | true | 第三方唯一标识 |
| request.account | String | true | 账号 |
| request.password | String | true | 密码 |
| request.name | String | true | 名字 |
| request.merchantNo | String | true | 商户号 |
| request.tel | String | true | 电话号码 |
| request.email | String | false | 邮箱 |
| request.enabled | String | true | 是否可用（0/禁用,1/启用） |

**2.3.2 批量添加用户（post）**

    路径：/user/batch (request为 list)
    参数详见 2.3.1

**2.3.3 更新用户（put）**

    路径：/user （guid 和 thirdNo 任意传一个值）

| 参数 | 参数类型 | 是否必选 | 说明 |
|:----|:--------:|:-------:|:-----------:|
| developerId | String | true | 开发者账号 |
| signature | String | true | 签名 |
| request |  |  |  |
| request.guid | String | false | 平台主键 |
| request.thirdNo | String | true | 第三方唯一标识 |
| request.account | String | true | 账号 |
| request.password | String | true | 密码 |
| request.name | String | true | 名字 |
| request.merchantNo | String | true | 商户号 |
| request.tel | String | true | 电话号码 |
| request.email | String | false | 邮箱 |
| request.enabled | String | true | 是否可用（0/禁用,1/启用） |

**2.3.4 删除用户（delete）**

    路径：/user （guid 和 thirdNo 任意传一个值）(request为 list)

| 参数 | 参数类型 | 是否必选 | 说明 |
|:----|:--------:|:-------:|:-----------:|
| developerId | String | true | 开发者账号 |
| signature | String | true | 签名 |
| request |  |  |  |
| request.guid | String | false | 平台主键(guid) |
| request.thirdNo | String | false | 主键(thirdNo) |

**2.3.5 根据GUID查询用户（post）**

    路径：/user/find （guid 和 thirdNo 任意传一个值）

| 参数 | 参数类型 | 是否必选 | 说明 |
|:----|:--------:|:-------:|:-----------:|
| developerId | String | true | 开发者账号 |
| signature | String | true | 签名 |
| request |  |  |  |
| request.guid | String | false | 平台主键(guid) |
| request.thirdNo | String | false | 主键(thirdNo) |

**2.3.6 根据条件查询用户（post）**

    路径：/user/query
    毫秒时间戳（startDate、endDate）和 时间格式（startTime、endTime）使用时任选其一即可

| 参数 | 参数类型 | 是否必选 | 说明 |
|:----|:--------:|:-------:|:-----------:|
| developerId | String | true | 开发者账号 |
| signature | String | true | 签名 |
| request |  |  |  |
| request.startDate | Long | false | 开始时间（毫秒时间戳） |
| request.endDate | Long | false | 结束时间（毫秒时间戳） |
| request.startTime | Date | false | 开始时间（yyyy-MM-dd HH:mm:ss）|
| request.endTime | Date | false | 结束时间（yyyy-MM-dd HH:mm:ss）|

## 2.4 组织
**2.4.1 创建组织（post）**

    路径：/organization/create  (request为 list)

| 参数 | 参数类型 | 是否必选 | 说明 |
|:----|:--------:|:-------:|:-----------:|
| developerId | String | true | 开发者账号 |
| signature | String | true | 签名 |
| request |  |  |  |
| request.thirdNo | String | true | 第三方唯一标识 |
| request.code | String | false | 编号 |
| request.name | String | true | 名称 |
| request.businessTime | String | false | 营业时间(yyyy-MM-dd HH:mm:ss) |
| request.brandGuid | String | false | 所属品牌（type=2时，必填） |
| request.fid | String | true | 所属组织（默认为企业,type=0时，不填） |
| request.contactName | String | false | 联系人 |
| request.contactTel | String | false | 联系电话 |
| request.province | String | false | 省(使用高德code码) |
| request.city | String | false | 市(使用高德code码) |
| request.district | String | false | 区(使用高德code码) |
| request.address | String | false | 详细地址 |
| request.type | String | true | 0：企业，1：组织，2：门店 |
| request.remark | String | true | 组织描述 |

**2.4.2 更新组织（put）**

    路径：/organization/update（guid 和 thirdNo 任意传入一个即可）

| 参数 | 参数类型 | 是否必选 | 说明 |
|:----|:--------:|:-------:|:-----------:|
| developerId | String | true | 开发者账号 |
| signature | String | true | 签名 |
| request |  |  |  |
| request.guid | String | false | 主键 |
| request.thirdNo | String | true | 第三方唯一标识 |
| request.code | String | false | 编号 |
| request.name | String | true | 名称 |
| request.businessTime | String | false | 营业时间 |
| request.brandGuid | String | false | 所属品牌（type=2时，必填） |
| request.fid | String | true | 所属组织（默认为企业） |
| request.contactName | String | false | 联系人 |
| request.contactTel | String | false | 联系电话 |
| request.province | String | false | 省 |
| request.city | String | false | 市 |
| request.district | String | false | 区 |
| request.address | String | false | 详细地址 |
| request.type | String | true | 0：企业，1：组织，2：门店 |
| request.remark | String | true | 组织描述 |

**2.4.3 删除组织（delete）**

    路径：/organization/delete

| 参数 | 参数类型 | 是否必选 | 说明 |
|:----|:--------:|:-------:|:-----------:|
| developerId | String | true | 开发者账号 |
| signature | String | true | 签名 |
| request |  |  |  |
| request.list | List&lt;String&gt; | true | 主键(thirdNo) |
| request.list.guid | String | false | 平台主键(guid) |
| request.list.thirdNo | String | false | 主键(thirdNo) |
| request.type | Integer | true | 删除类型（0：企业，1：组织，2：门店） |

**2.4.4 查询组织列表（post）**

    路径：/organization/list

| 参数 | 参数类型 | 是否必选 | 说明 |
|:----|:--------:|:-------:|:-----------:|
| developerId | String | true | 开发者账号 |
| signature | String | true | 签名 |
| request |  |  |  |
| request.type | Integer | true | 0：企业，1：组织，2：门店 |
| request.startTime | Date | false | yyyy-MM-dd HH:mm:ss |
| request.endTime | Date | false | yyyy-MM-dd HH:mm:ss |

**2.4.5 查询单个组织（post）**

    路径：/organization/query

| 参数 | 参数类型 | 是否必选 | 说明 |
|:----|:--------:|:-------:|:-----------:|
| developerId | String | true | 开发者账号 |
| signature | String | true | 签名 |
| request |  |  |  |
| request.guid | String | false | 平台主键(guid) |
| request.thirdNo | String | false | 主键(thirdNo) |

**2.4.6 创建品牌（post）**

    路径：/brand/create (request为 list)

| 参数 | 参数类型 | 是否必选 | 说明 |
|:----|:--------:|:-------:|:-----------:|
| developerId | String | true | 开发者账号 |
| signature | String | true | 签名 |
| request |  |  |  |
| request.thirdNo | String | true | 第三方唯一标识 |
| request.name | String | true | 品牌名称 |
| request.description | String | false | 品牌介绍 |
| request.logo | String | false | 图标 |
| request.organizationGuid | String | true | 上级组织(默认为企业) |

**2.4.7 更新品牌（put）**

    路径：/brand/update（guid 和 thirdNo 任意传入一个即可）

| 参数 | 参数类型 | 是否必选 | 说明 |
|:----|:--------:|:-------:|:-----------:|
| developerId | String | true | 开发者账号 |
| signature | String | true | 签名 |
| request |  |  |  |
| request.guid | String | false | 主键 |
| request.thirdNo | String | true | 第三方唯一标识 |
| request.name | String | true | 品牌名称 |
| request.description | String | false | 品牌介绍 |
| request.logo | String | false | 图标 |
| request.organizationGuid | String | true | 上级组织(默认为企业) |

**2.4.8 删除品牌（delete）**

    路径：/brand/delete (request为 list)（guid 和 thirdNo 任意传入一个即可）

| 参数 | 参数类型 | 是否必选 | 说明 |
|:----|:--------:|:-------:|:-----------:|
| developerId | String | true | 开发者账号 |
| signature | String | true | 签名 |
| request |  |  |  |
| request.guid | String | false | 平台主键(guid) |
| request.thirdNo | String | false | 主键(thirdNo) |

**2.4.9 查询单个品牌（post）**

    路径：/brand/query (request为 list)（guid 和 thirdNo 任意传入一个即可）

| 参数 | 参数类型 | 是否必选 | 说明 |
|:----|:--------:|:-------:|:-----------:|
| developerId | String | true | 开发者账号 |
| signature | String | true | 签名 |
| request |  |  |  |
| request.guid | String | false | 平台主键(guid) |
| request.thirdNo | String | false | 主键(thirdNo) |

**2.4.9 查询品牌列表（post）**

    路径：/brand/list

| 参数 | 参数类型 | 是否必选 | 说明 |
|:----|:--------:|:-------:|:-----------:|
| developerId | String | true | 开发者账号 |
| signature | String | true | 签名 |
| request |  |  |  |
| request.startTime | Date | false | yyyy-MM-dd HH:mm:ss |
| request.endTime | Date | false | yyyy-MM-dd HH:mm:ss |

## 2.5 会员

**2.5.1 添加会员信息（post）**

    路径：/memberInfo/addMemberInfo
    
| 参数 | 参数类型 | 是否必选 | 说明 |
|:----|:--------:|:-------:|:-----------:|
| developerId | String | true | 开发者账号 |
| signature | String | true | 签名 |
| request |  |  |  |
| request.thirdNo | String | true | 第三方唯一标识 |
| request.brandThirdNo | String | false | 品牌guid |
| request.storeThirdNo | String | false | 门店guid |
| request.account | String | true | 账户 |
| request.openId | String | false | 微信openId |
| request.sourceType | Integer | true | 会员注册来源 0后台添加,1POS机注册,2一体机注册,3后台导入，20微信关注，21微信扫码点餐，22预定，23排队，24微信注册.25微信C端后台注册 |
| request.accountState | Integer | true | 账号状态(0:正常 ， 1：冻结) |
| request.delete | Boolean | true | 是否删除(0: 未删除， 1： 已删除) |
| request.memberInfoThirdNo | String | true | 会员基本信息表关联标识 |
| request.memberSerialId | String | false |  |
| request.phoneNum | String | true | 手机号码 |
| request.unionId | String | false |  |
| request.nickName | String | false | 用户昵称 |
| request.sex | Integer | true | 性别（1 男性，2 女性，0 未知） |
| request.birthday | Date | false | 时间pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai" |
| request.provinceCode | String | false | 省编码 |
| request.cityCode | String | false | 市编码 |
| request.areaCode | String | false | 区编码 |
| request.provinceName | String | false | 省名称 |
| request.cityName | String | false | 市名称 |
| request.areaName | String | false | 区名称 |
| request.address | String | false | 用户详细地址 |
| request.headImgUrl | String | true | 头像url地址 |
| request.occupation | String | false | 职业 |
| request.signature | String | false | 个性签名 |
| request.password | String | true | 用户密码 |
| request.payPassword | String | false | 支付密码 |


**2.5.2 更新会员信息（post）**

    路径：/memberInfo/updateMemberInfo （guid 和 thirdNo 任意传入一个即可）
    
    参数详见2.5.1
    
**2.5.3 删除会员信息（post）**

    路径：/memberInfo/deleteMemberInfo
    
| 参数 | 参数类型 | 是否必选 | 说明 |
|:----|:--------:|:-------:|:-----------:|
| developerId | String | true | 开发者账号 |
| signature | String | true | 签名 |
| request |  |  |  |
| request.guid | String | false | 平台主键(guid) |
| request.thirdNo | String | false | 主键(thirdNo) |

**2.5.4 查询会员信息（post）**

    路径：/memberInfo/selectMemberInfoList
    
| 参数 | 参数类型 | 是否必选 | 说明 |
|:----|:--------:|:-------:|:-----------:|
| developerId | String | true | 开发者账号 |
| signature | String | true | 签名 |
| request |  |  |  |
| request.startTime | String | true | 开始时间 |
| request.endTime | String | true | 结束时间 |

#3. 同步

    1.同步数据新增、更新操作的所有实体类的字段都和各平台向MDM平台发起相应请求时字段相同，唯一区别在于同步时所有数据都以集合形式传递
    2.删除操作统一为 list集合，list中为主键（guid）的字符串
    
    例如：

    商品新增 / 更新有效实体字段为:  
        {
            "thirdNo":"主键",
            "itemCode":"商品编码",
            "itemBar":"商品条形码",
            "itemName":"商品名称",
            "pinyin":"拼音简码",
            "alias":"别名",
            "picture":"商品图片",
            "type":"商品类型",
            "description":"商品描述",
            "price":"参考价",
            "unit":"计价单位",
            "isWeighing":"是否是称重商品(0:标准，1：称重)",
            "enterpriseGuid":"企业Guid",
            "skuGuidList":[
              "skuGuid的集合"  
            ]
        }
    
    同步时的数据为：
        {
          "developerId":"开发者账号ID",
          "signature":"签名",
          "source":"数据来源（即数据来源方的developerId）",
          "data":[
              {
                "guid":"mdm平台主键",
                "thirdNo":"主键",
                "itemCode":"商品编码",
                "itemBar":"商品条形码",
                "itemName":"商品名称",
                "pinyin":"拼音简码",
                "alias":"别名",
                "picture":"商品图片",
                "type":"商品类型",
                "description":"商品描述",
                "price":"参考价",
                "unit":"计价单位",
                "isWeighing":"是否是称重商品(0:标准，1：称重)",
                "enterpriseGuid":"企业Guid",
                "skuGuidList":[
                  "skuGuid的集合"  
                ]
              }
            ]
        }
    
#4. 密码加密  

    DIGITS_LOWER = new char[]{'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};
    DIGITS_UPPER = new char[]{'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F'};

    /**
     * Md5方式加密，生成随机的 16 位salt并经过1024次 md5 hash
     *
     * @return 加密后的字符串
     * @see Digests
     */
    public static String entryptMd5(String plainText, boolean toLowerCase) {
        byte[] salt = Digests.generateSalt(8);
        byte[] hashText = Digests.md5(plainText.getBytes(), salt, 1024);
        return Encodes.encodeHex(salt, toLowerCase) + Encodes.encodeHex(hashText, toLowerCase);
    }
    
    /**
     * 对输入字符串进行md5散列.
     */
    public static byte[] md5(byte[] input, byte[] salt, int iterations) {
        return digest(input, MD5, salt, iterations);
    }
    
    /**
     * 对字符串进行散列, 支持md5与sha1算法.
     */
    private static byte[] digest(byte[] input, String algorithm, byte[] salt, int iterations) {
        try {
            MessageDigest digest = MessageDigest.getInstance(algorithm);

            if (salt != null) {
                digest.update(salt);
            }

            byte[] result = digest.digest(input);

            for (int i = 1; i < iterations; i++) {
                digest.reset();
                result = digest.digest(result);
            }
            return result;
        } catch (GeneralSecurityException e) {
            throw new RuntimeException(e);
        }
    }
    
    /**
     * 生成随机的Byte[]作为salt.
     *
     * @param numBytes byte数组的大小
     */
    public static byte[] generateSalt(int numBytes) {
        if (numBytes <= 0) {
            throw new RuntimeException("numBytes argument must be a positive integer (1 or larger)");
        }
        byte[] bytes = new byte[numBytes];
        random.nextBytes(bytes);
        return bytes;
    }
    
    /**
     * Hex编码.
     */
    public static String encodeHex(byte[] input, boolean toLowerCase) {
        return new String(toLowerCase ? encodeHexText(input, DIGITS_LOWER) : encodeHexText(input, DIGITS_UPPER));
    }

    /**
     * Hex编码
     */
    protected static char[] encodeHexText(byte[] data, char[] toDigits) {
        int l = data.length;
        char[] out = new char[l << 1];
        int i = 0;

        for (int var5 = 0; i < l; ++i) {
            out[var5++] = toDigits[(240 & data[i]) >>> 4];
            out[var5++] = toDigits[15 & data[i]];
        }
        return out;
    }

























