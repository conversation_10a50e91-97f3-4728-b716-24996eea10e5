# 饿了么常见场景及其消息推送


## 场景1： tested 
- 用户下单-接单前用户取消
    
消息推送：
- 10 订单生效
- 14 订单被取消（接单前），且message中role=1
- 结束

## 场景： tested
- 用户下单-超时未接单取消

消息推送：
- 10 订单生效
- 14 订单被取消（接单前），且message中role=2
- 结束

## 场景： tested
- 用户下单-接单前商户取消

消息推送：
- 10 订单生效
- 14 订单被取消（接单前），且message中role=3
- 结束

## 场景： tested
- 用户下单-商户接单(商户在开放平台接单)

消息推送：
- 10 订单生效
- 12 商户已经接单，且role=3

## 场景： tested
- 用户下单-商户在饿了么后台接单

消息推送：
- 10 订单生效
- 12 商户已经接单，且role=3；是的，你没看错，开放平台接单或饿了么后台接单时，接单推送是一样的

## 场景： to be test
- 用户下单-商户接单-1分钟内用户取消

消息推送：
- 10 订单生效
- 17 订单强制无效（商家主动取消已接订单、用户1分钟内取消），且role=1
- 结束

## 场景： to be test
- 用户下单-商户接单-商家取消订单

消息推送：
- 10 订单生效
- 17 订单强制无效（商家主动取消已接订单、用户1分钟内取消），且role=3
- 结束

## 场景： tested
- 用户下单-商户接单-1分钟后用户取消(未收到货)-商户同意

消息推送：
- 10 订单生效
- 12 商户已经接单
- 20 下单用户申请取消
- 23 商户同意取消订单
- 15 订单置为无效（接单后）
- 结束

## 场景：
- 用户下单-商户接单-1分钟后用户取消(未收到货)-商户不同意-仲裁有效
消息推送：
- 10 订单生效
- 12 商户已经接单
- 20 下单用户申请取消
- 22 商户不同意取消订单
- 25 客服仲裁取消单申请有效
- ? 有后续否
结束

## 场景：
- 用户下单-商户接单-1分钟后用户取消(未收到货)-商户不同意-仲裁无效

消息推送：
- 10 订单生效
- 12 商户已经接单
- 20 下单用户申请取消
- 22 商户不同意取消订单
- 26 客服仲裁取消单申请无效
- ? 有后续否
- 结束

## 场景：
- 用户下单-商户接单-1分钟后用户取消(已收到货，退全部)-商户同意

消息推送：
- 10 订单生效
- 12 商户已经接单
- 30 用户申请退单，且message中refundType=normal
- 33 商户同意退单
- 结束

## 场景：
- 用户下单-商户接单-1分钟后用户取消(已收到货，退部分)-商户同意

消息推送：
- 10 订单生效
- 12 商户已经接单
- 30 用户申请退单，且message中refundType=part
- 33 商户同意退单
- 结束

## 场景：
- 用户下单-商户接单-用户催单-略

消息推送：
- 10 订单生效
- 12 商户已经接单
- 45 用户催单
- 略
- 结束

## 场景：
- 用户下单-商户接单-订单完成-用户申请退全款-商户同意

消息推送：
- 10 订单生效
- 12 商户已经接单
- 18 订单已完成
- 30 用户申请退单，且message中refundType=normal
- 33 商户同意退单
- 结束

## 场景：
- 用户下单-商户接单-订单完成-用户申请退部分-商户同意

消息推送：
- 10 订单生效
- 12 商户已经接单
- 18 订单已完成
- 30 用户申请退单，且message中refundType=part
- 33 商户同意退单
- 结束

## 场景：
- 用户下单-商户接单-订单完成-用户申请退全款-商户不同意-仲裁有效

消息推送：
- 10 订单生效
- 12 商户已经接单
- 18 订单已完成
- 30 用户申请退单，且message中refundType=normal
- 32 商户不同意退单
- 35 客服仲裁退单有效
- ? 有后续否
- 结束

## 场景：
- 用户下单-商户接单-订单完成-用户申请退全款-商户不同意-仲裁无效

消息推送：
- 10 订单生效
- 12 商户已经接单
- 18 订单已完成
- 30 用户申请退单，且message中refundType=normal
- 32 商户不同意退单
- 36 客服仲裁退单无效
- ? 有后续否
- 结束