# 说明

# 前台打印机

可选打印设备：
- 本机
- 网络打印机

可选营业模式：
- 堂食
- 快餐
- 快餐（微信点餐）
- 外卖

可选票据类型:
- 菜品清单
- 预结单
- 结帐单
- 储值单
- 外卖单
- 交接单

# 后厨打印机

可选打印设备：
- 网络打印机

可选营业模式：
- 堂食
- 快餐
- 快餐（微信点餐）
- 外卖

内置票据类型：
- 点菜单
- 退菜单

可选打印方式：
- 整单切纸
- 商品切纸
- 分类切纸
- 数量切纸

可选打印商品：
- 所有菜品

# 标签打印机

可选打印设备：
- USB打印机
- 网络打印机

可选营业模式：
- 堂食
- 快餐
- 快餐（微信点餐）
- 外卖

内置票据类型：
- 标签单

可选标签大小：
- 40X30
- 30X20

可选打印商品：
- 所有菜品

# 逻辑

| 打印机类型 | (堂食、快餐、快餐(微信)、外卖)与桌台区域是否相关 | 与DeviceId是否相关 | 与菜品是否相关 | 与切纸方式是否相关 |
| :-----: | :-----: | :-----: | :-----: | :-----: | :-----: |
| 前台打印机 | 是、否、是、否 | 是 | 否 | 否 |
| 后厨打印机 | 是、否、是、否 | 否 | 是 | 是 |
| 标签打印机 | 是、否、是、否 | 是 | 是 | 否 |

从数据库查出可用打印机列表

使用 PrinterQuery {
    String storeGuid;
    String invoiceType;
    String deviceId;
    String areaGuid;
    List<String> itemGuidList;
}


0. 打印来源

    AIO：DeviceId对应的设备打印
    
    POS：主机替打，设为主机DeviceId
    
    MBP：主机替打，设为主机DeviceId
    
    PAD：主机替打，设为主机DeviceId
    
    TAKEOUT：主机替打，设为主机DeviceId
    
    WECHAT：主机替打，设为主机DeviceId
    
    其他第三方来源：主机替打，设为主机DeviceId
    
1. 票据类型、交易模式、打印区域

    前台打印机
    菜品清单：堂食
    预结单：堂食
    结帐单：堂食、快餐、微信、外卖
    储值单：无模式
    外卖单：外卖
    交接单：无模式
    
    后厨打印机
    点菜单：堂食、快餐、微信、外卖
    退菜单：堂食、快餐、微信、外卖
    
    标签打印机
    标签单：快餐、外卖

    堂食：断言DTO的areaGuid不为null，areaGuid!=null

    快餐：断言DTO的areaGuid为null，areaGuid==null

    微信：断言DTO的areaGuid不为null，areaGuid!=null

    外卖：断言DTO的areaGuid为null，areaGuid==null
    
    无：断言DTO的areaGuid为null，areaGuid==null

2. 票据类型、设备ID、打印商品

    因为一次打印肯定是确定的票据类型
  
    前台打印机的"菜品清单","预结单","结帐单","储值单","外卖单","交接单"
    需构造deviceId作为查询条件，deviceId!=null
    无需构造itemGuidList作为查询条件，itemGuidList==null
    
    后厨打印机的"点菜单","退菜单"
    无需构造deviceId作为查询条件，deviceId==null
    需构造itemGuidList作为查询条件，itemGuidList!=null
    
    标签打印机的"标签单"
    需构造deviceId作为查询条件，deviceId!=null
    需构造itemGuidList作为查询条件，itemGuidList!=null
    
    得到通用查询Sql
    select * from Printer
    inner join PrinterArea a on a.printer_guid = p.printer_guid (if areaGuid not null) 
    inner join PrinterItem d on d.printer_guid = p.printer_guid (if itemGuidList not null)
    where p.store_guid = #{storeGuid}
    and p.invoice_type = #{invoiceType}
    and p.device_id = #{deviceId} (if deviceId not null)
    and a.area_guid = #{areaGuid} (if areaGuid not null)
    and d.item_guid in #{itemGuidList} (if itemGuidList not null)

3. 得到的Printer列表，与

    若前台打印机，直接存入数据库
    
    若后厨打印机：
        打印商品匹配
        打印方式拆分
        存入数据库    
    
    若标签打印机：
        打印商品匹配
        打印方式，商品切纸、数量切纸结合
        存入数据库
        
## 打印性能优化

1. 添加索引

- findPrinterByQuery方法

    查询需要建立索引：
    联合索引 idx_store_guid_invoice_type_device_id(store_guid, invoice_type, device_id, printer_guid)
    join的hsp_printer_item表需要建立普通索引idx_printer_guid_item_guid(printer_guid, item_guid)
    join的hsp_printer_area表需要建立普通索引idx_printer_guid_area_guid(printer_guid, area_guid)
    
    需要返回结果集：
    guid, 
    printCut, 
    arrayOfItemGuid
    需查询这些，冗余进printRecord


- queryByRecordGuid方法：

    查询需建立索引：
    hsp_print_record 需要建立联合索引 idx_record_guid_is_deleted_printer_guid(record_guid, is_deleted, printer_guid)
    join的hsp_printer表需要建立唯一索引uk_guid(guid)
    
    需要返回结果集合：
    recordGuid(入参就有，可省略),
    invoiceType, 
    printContent, 
    printer.printPage, 
    printer.printCount, 
    printer.printerType, 
    printer.printerId, 
    printer.printerPort

- countFailedByDeviceAndStatus方法

    查询需要建立索引：
    hsp_printer表行数小于hsp_print_record表，所以MySql优化器会优化为 hsp_printer left join hsp_print_record，
    因此 hsp_print_record 需要建立普通索引 idx_printer_guid，
    join的hsp_printer表需要建立联合索引 idx_guid_device_id(guid, device_id)。
    
    需要返回结果集：
    所有列
    
- listByDeviceAndStatus方法

    查询需要建立索引：
    hsp_print_record 需要建立联合索引 idx_print_status_is_deleted_printer_guid(print_status, is_deleted, printer_guid)
    join的hsp_printer表需要建立联合索引 idx_guid_device_id(guid, device_id)
    
    需要返回结果集：
    count(*)
    
2. mqtt批量推送printRecordGuid