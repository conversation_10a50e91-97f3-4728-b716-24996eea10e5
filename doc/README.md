uuid存储到mdm

mdm不分库

服务内部落库 source=0 掌控者操作

StoreMdm落库 source=1 三方系统操作

删除使用逻辑删除

新增
我方 insertNullUuidWithGuid -> rpcNullUuidWithGuid 
三方 empty
-> receive -> findRowByEqGuidNullUuid
    找到 -> 我方 -> updateUuidByGuid
    未找到 -> 三方 -> genereteGuid  ->  insertItemWithGuid -> insertUuidAndGuid;

修改
findUuidByGuid -> rpcByUuid
findGuidByUuid -> updateItemByGuid

删除
findUuidByGuid -> rpcByUuid -> deleteUuid
findGuidByUuid -> deleteItemByGuid -> deleteUuid


------------

待解决：
运行storeMdm，canal-bin从0开始同步，storeMdm开始batch同步

查询
订单查询，
-方案一：提供 uuid_mapping查询接口
-方案二：提供 order 消费接口，内部去mapping