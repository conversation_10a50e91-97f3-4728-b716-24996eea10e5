# 外卖交接文档

## 项目

1. 业务模块

Producer服务

Consumer服务

Agg-App服务

Agg-Mchnt服务

2. 业务逻辑

客人触发

    新订单

    取消订单（待明确）

服务员触发

    订单列表

    订单详情

    接单（打印外卖单、外卖后厨单）

    拒单

    接单后取消订单

平台触发向我们推送(确认含义：平台确认我方操作，并予以异步ack)

    新订单 （新订单消息，向内部Message服务）

    确认接单

    确认订单取消

    订单配送

    确认订单完成

    确认退款

3. 业务系统接口

Consumer内部Restful接口：

    POST /takeaway/acceptOrder
    店家接单/拒单

    POST /takeaway/agreeCancelReq
    店家同意退单/拒绝退单

    POST /takeaway/shopBindingUrl
    门店授权页面绑定url拼接

    POST /takeaway/printBill
    打印账单

    POST /takeaway/printKitchen
    打印后厨菜单

    POST /takeaway/queryMoneyAndDealNumber
    查询交易金额和交易数量

    POST /takeaway/queryStoreAuthorization
    门店授权页面

    POST /takeaway/queryAutoReceive
    商家查询是否自动接单

    POST /takeaway/listOrder
    查询外卖订单

    POST /takeaway/getOrderDetail
    查询外卖订单详情

    POST /takeaway/setAutoReceive
    商家操作是否自动接单

    POST /takeaway/test

Producer外部Restful接口: 

    饿了么相关接口 ele-controller

    GET /ele/bind/callback
    testEleBindCallback

    GET /ele/order/callback
    testget

    POST /ele/order/callback
    testEleOrderCallback

    POST /ele/listEleAuth
    listEleAuth

Producer外部Restful接口: 

    美团相关接口 mei-tuan-controller
    
    POST /mt/bind/callback
    门店映射回调

    POST /mt/queryMtToken
    queryMtToken

    GET /mt/group-buying/token/{ePoiId}
    getAuthToken

    POST /mt/heartbeat/callback
    heartbeat

    POST /mt/order/callback/{flag}
    回调推送

    POST /mt/phonenumber/callback
    realPhoneNumberReback

    POST /mt/listMtAuth
    listMtAuth

    POST /mt/unbind/callback
    门店自助解绑回调

Consumer消息接口 

    消费者 package/event/ConsumersRocketListener, source: Producer服务

    新订单
    订单取消
    确认接单
    订单配送
    订单完成
    确认退款

    生产者 package/service/RocketMQ/RocketMqService#mtTakeawayOrder发给Producer服务

    生产者 package/service/RocketMQ/RocketMqService#orderLog发给内部订单服务

Producer消息接口

    消费者 package/event/ProducersRocketListener，source: ConsumerModule 根据外卖类型使用不同Handler处理业务

    生产者 package/service/RocketMQ/RocketMqService#mtTakeawayOrder发给Consumer服务

4. 源代码相关

最后修改的代码提交

源代码地址: [holder-saas-store-takeaway 
](http://101.37.252.104/holderRD/holder-java/saas-platform/holder-saas-store-takeaway)

子模块地址: 

[holder-saas-store-takeaway-consumers](http://101.37.252.104/holderRD/holder-java/saas-platform/holder-saas-store-takeaway/tree/master/holder-saas-store-takeaway-consumers)

[holder-saas-store-takeaway-producers](http://101.37.252.104/holderRD/holder-java/saas-platform/holder-saas-store-takeaway/tree/master/holder-saas-store-takeaway-producers)

聚合层地址：

[holder-saas-aggregation-app](http://101.37.252.104/holderRD/holder-java/saas-platform-enter/holder-saas-aggregation-app)TakeawayController 和 TakeawayPrintController

[holder-saas-aggregation-merchant](http://101.37.252.104/holderRD/holder-java/saas-platform-enter/holder-saas-aggregation-merchant)TakeawayController

5. 数据库相关

相关数据库与数据表结构，未注释的库名、表名、字段名，将其确定

Producer服务表结构

`hst_ele_user_auth` 在饿了么注册的商户
`hst_ele_shop_auth` 在饿了么注册的门店
`hst_ele_token_auth` 在饿了么注册的门店token(一个门店一个token)

`hst_meituan_epoi_auth` 在美团注册的门店（无商户概念，含token字段）

`hst_meituan_privacy_phone` 美团门店隐私号（必接接口）

Consumer服务表结构

`hst_order` 订单、账单
`hst_order_discount` 内部优惠、平台优惠
`hst_order_dishes` 订单菜品详情
`hst_store_config` 门店配置（如是否自动接单）

`hst_order_dishes_package` 订单套餐（未使用）
`hst_order_dishes_practice` 订单菜品做法（未使用）
`hst_order_receive_msg` （未使用）
`hst_order_response_msg`（未使用）

每一个数据库、表、字段的意义，更新到文档 

足够详细

每一个表涉及到哪一个模块进行确认，更新到文档



6. 对接人

    确定产品对接人

    Web端：周萤
    Android端：郑冬梅

    测试对接人

    Web端：兰小梅
    Android端：兰小梅

## 项目资料

1. 原型文档

[后台重构原型设计](http://101.37.252.104/holderRD/holder-java/saas-platform/holder-saas-store-docs/tree/master/version-1.0/2.%E5%8E%9F%E5%9E%8B%E6%96%87%E6%A1%A3/%E5%90%8E%E5%8F%B0%E9%87%8D%E6%9E%84%E5%8E%9F%E5%9E%8B%E8%AE%BE%E8%AE%A1)

[客户端重构原型设计](http://101.37.252.104/holderRD/holder-java/saas-platform/holder-saas-store-docs/tree/master/version-1.0/2.%E5%8E%9F%E5%9E%8B%E6%96%87%E6%A1%A3/%E5%AE%A2%E6%88%B7%E7%AB%AF%E9%87%8D%E6%9E%84%E5%8E%9F%E5%9E%8B%E8%AE%BE%E8%AE%A1)

2. 开发文档

美团

[美团官网](https://developer.meituan.com/home)

开发者帐号：18190813619 (开发使用)
密码： zkz123456
注：使用普通登录方式

开发者帐号：13551185710 （未使用）
密码： zkz181818
注：使用普通登录方式

开发认证信息： 正式帐号

测试账号信息： 测试帐号
测试门店名称：t_Hm91c4ZY    
商家版本，可登录[商家Web后台](http://e.waimai.meituan.com/#/v2/order/pre)
只使用了外卖模块

回调接口设置，回调的聚合层，gateway放行

对接：微信群: 官方微信

[文档中心](https://developer.meituan.com/openapi)

[开发者中心](https://developer.meituan.com/admin#/?_k=8r53lu)

饿了么

[饿了么官网](https://nest-fe.faas.ele.me/openapi/help-center/index)

文档：[接入指南](https://nest-fe.faas.ele.me/openapi/documents/startguide)

开发者帐号：成都掌控者网络科技有限公司(18190813619) 
密码： zkz19412942
注意： 只能使用公司名登录

java组在使用：

saas开发测试
智慧门店外卖

原门店在使用：

智慧门店系统
智慧门店测试

每一应用都有沙箱授权和正式授权

注意项：
沙箱环境配置
    ngrok内网穿透：
        每次启动url都与上次不一样，所以每次启动需要配置沙箱环境的回调、推送Url
        并且项目yml中 属性 ele:REDIRECT_URL 也需要配置与上述url一致
    目前在用 智慧门店外卖 的 沙箱 Key Secret
环境授权配置

回调设置：回调的聚合层，gateway放行

小工具tips:
    测试小工具->追踪订单
    生成签名工具->测试签名

对接： 使用“我要提问”功能

遗留：饿了么解绑，平台未提供接口。所以需要进入管理中心，手动解绑，如果配置了回调，会回调我方服务“解绑状态”

## 项目测试

1. 测试数据

常用帐号、数据库Mock数据 等

2. 测试用例

单元测试、Postman请求 等

Consumer#HolderSaasStoreTakeawayConsumersApplicationTests的单元测试

3. 沙箱订单测试

4. Ngrox使用

5. 菜品映射问题

## UAT环境

1. 现状

2. 遗留问题

本期业务遗留问题：

    并没有遗留问题

未来业务遗留问题：

    三方配送：无法测试

    饿了么解绑：无法测试（沙箱不推送，只有正式环境才推送）

代码遗留问题：

    Bug已解决

## 对接三方服务商

1. 服务概念

2. 服务地址

3. 服务帐号

4. 服务配置
## 京东外卖
1. 地址：https://opendj.jd.com/isv/isvindex.jsp
2. 开放平台账号：zkztakeout 密码：19412942 手机：18190813619

