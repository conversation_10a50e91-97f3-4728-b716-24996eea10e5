# 外卖服务

## 概述

Takeaway-Producers和Takeaway-Consumer之间大部分通过Mq传输数据，少部分通过restful接口传输数据。
Consumer和Producers之间实现的是第三方对接和订单数据流转解耦的设计。
Producers负责和具体的第三方外卖平台打交道，如美团、饿了么、自营外卖、赚餐外卖。
Consumer负责控制订单的【待处理】【待配送】【配送中】【已完成】【异常单】【已取消】的相关通用状态的流转，发送相关emq消息给安卓端执行订单的刷新操作。


## Takeaway-Producers
外卖Producers，负责跟第三方服务的对接。也就是具体负责和美团、饿了么、自营外卖、赚餐外卖打交道的。
一般情况下，美团、饿了么会提供自己的开发者工具包，称为SDK.
在涉及到相关的菜品绑定、菜品查询、菜品修改、订单接单、订单拒单的时候，只要将相关的参数设置进去即可。
当订单的相关状态发生改变时，美团、饿了么、赚餐会通过约定好的回调地址，将相关的订单推送到我方，进行业务流转。



### 业务逻辑

- 订单完整流程（美团专送）

用户下单
-->美团订单回调推送（订单生成）-->Producers接收回调-->Consumer订单首次保存（待处理）-->发送EMQ消息给安卓端-->安卓端刷新页面、语音提示-->自动接单or手动接单


-->美团订单回调推送（接单成功）-->Producers接收回调-->Consumer更新订单状态（待配送）-->发送EMQ消息给安卓端-->安卓端刷新页面、语音提示-->美团专送骑手取餐-->上报美团


-->美团订单回调推送（配送中）-->Producers接收回调-->Consumer更新订单状态（配送中）-->发送EMQ消息给安卓端-->安卓端刷新页面、语音提示-->美团专送骑手送达-->上报美团


-->美团订单回调推送（配送完成）-->Producers接收回调-->Consumer更新订单状态（配送完成）

注意：

A.美团配送完成后，需要客户确认收货后，我方才会收到美团方回调订单完成。若客户未确认收货，则需要等待最多24小时候，由美团方批量处理订单的确认收货。

B.饿了么流程和上述几乎一致，只是饿了么骑手确认送达之后，无需客户确认收货，我方就可以收到订单完结的回调请求。


- 一体机手动接单流程

商户手动接单
-->APP聚合层-->Consumer通用接单流程-->Producers调用美团方SDK-->美团处理完毕


-->美团订单回调推送-->Consumer更新订单状态（待配送）-->发送EMQ消息给安卓端-->安卓端刷新页面、语音提示


- 一体机自动接单流程

商户设置了自动接单：

-->美团订单回调推送（订单生成）-->Consumer订单首次保存（待处理）-->发送消息到message服务-->安卓端接到消息后-->安卓端刷新页面、语音提示


-->若autoRcv为true-->安卓端调用APP聚合层-->APP聚合层-->Consumer执行通用接单流程-->Producers调用美团方SDK-->美团处理完毕


-->美团回调接单成功（接单成功）-->Consumer更新订单状态（待配送）-->发送EMQ消息给安卓端-->安卓端接收后刷新页面、语音提示


-->后续执行相关配送流程

-  一城飞客配送流程
同订单完整流程（美团专送），只是在接单完毕之后，通过门店绑定表保存的是否一城飞客配送，来进行是否发起一城飞客的配送。


### 三方Config

1. 饿了么Config

    - gateway 放行

        聚合层回调接口放行

    - dynamic 放行
    
        待添加


2. 美团Config

    - gateway 放行

        聚合层回调接口放行

    - dynamic 放行
    
        待添加

### 三方Callback

1. 饿了么Callback

    GET /ele/bind/callback 绑定结果回调

    POST /ele/order/callback 订单回调

    GET /ele/order/callback 订单回调(绑定时饿了么验证回调，业务中不使用)

2. 美团Callback

    POST /mt/bind/callback 门店映射回调

    POST /mt/heartbeat/callback 心跳保持回调

    POST /mt/order/callback/{flag} 订单回调

    POST /mt/phonenumber/callback 隐私号回调

    POST /mt/unbind/callback 门店解绑回调

3. 饿了么Callback测试

    “绑定回调”测试： 商户后台跳转至饿了么绑定页面，执行绑定操作
    
    “订单回调”测试： 手机端安装饿了么App，选择测试门店下单即可
    
    注意项： 
    
    - 需要使用ngrok，内网穿透；
    
    启动aggregation-app服务 -> 启动Ngrok(./ngrok http 8161) -> 得到映射地址(如 )：
    
        -> 外卖producers的yml中更新回调地址 
        
        -> 平台上更新回调地址
        
        -> 启动外卖producers服务
        
    - 搜索“饿了么首次下单测试”可了解如何测试
    
    - 手机微信扫描“美团外卖商家中心-门店-门店信息-门店二维码”，进入测试门店，（如果有必要，更换配送地址），下单即可。

4. 美团Callback测试

    “门店映射回调”测试： 商户后台转至美团绑定页面，执行绑定操作
    
    “心跳保持回调”测试：空包检测，返回OK即可
    
    “订单回调”测试：手机端安装美团App，选择测试门店下单即可
    
    “隐私号回调”测试：暂未测试，因为未使用
    
    “门店解绑回调”测试：商户后台转至美团绑定页面，执行解绑操作
    
    注意项： 
    
    - 需要使用ngrok，内网穿透；
    
    启动aggregation-app服务 -> 启动Ngrok(./ngrok http 8161) -> 得到映射地址(如 )：
    
        -> 外卖producers的yml中更新回调地址 
        
        -> 平台上更新回调地址
        
        -> 启动外卖producers服务
    
    - 搜索“美团首次下单测试”可了解如何测试
    
    - 手机微信扫描“饿了么商家版-店铺设置-门店管理-店铺小程序二维码”，进入测试门店，（如果有必要，更换配送地址），下单即可。

### 内部Controller

1. 饿了么 EleController

    POST /ele/listEleAuth 根据我方门店id (从DB中)查询饿了么门店授权（Consumer调用）

2. 美团 MeiTuanController

    POST /mt/queryMtToken 根据我方门店id (从DB中)查询美团门店授权（Consumer调用）

    POST /mt/listMtAuth 根据我方门店id (从DB中)查询美团门店id（Consumer调用）
    
3. 饿了么Controller测试

    单元测试即可

4. 美团Controller测试

    单元测试即可
     
### 内部伪Controller，需要移到Service中

1. 美团 MeiTuanController

    GET /mt/group-buying/token/{ePoiId} 根据美团门店id (从DB中)获取美团门店授权

### 内部MessageQueue

1. 消费者 package/event/ProducersRocketListener, source: Consumer服务
 
    根据外卖类型使用不同Handler处理业务

2. 生产者 package/service/RocketMQ/RocketMqService#mtTakeawayOrder, target: Consumer服务

### 数据库设计

`hst_ele_user_auth` 在饿了么注册的商户

`hst_ele_shop_auth` 在饿了么注册的门店

`hst_ele_token_auth` 在饿了么注册的门店token(一个门店一个token)

`hst_meituan_epoi_auth` 在美团注册的门店（无商户概念，含token字段）

`hst_meituan_privacy_phone` 美团门店隐私号（必接接口）

### 遗留问题

    菜品映射问题：据说已解决

    三方配送：无法测试

    饿了么解绑：无法测试（沙箱不推送，只有正式环境才推送）

## Takeaway-Consumers

外卖Consumers，负责桥接外卖Producers和Saas-Store内部服务

### 业务逻辑

服务员触发

    订单列表

    订单详情

    接单（打印外卖单、外卖后厨单）

    拒单

    接单后取消订单

### 伪三方config

    yml配置了一个回调地址，需要将其移植到Producers中，提供restful接口供Consumers查询使用

### 内部Controller

1. 外卖Consumers TakeawayController，Saas内部服务调用

    POST /takeaway/acceptOrder
    店家接单/拒单

    POST /takeaway/agreeCancelReq
    店家同意退单/拒绝退单

    POST /takeaway/shopBindingUrl
    门店授权页面绑定url拼接

    POST /takeaway/printBill
    打印账单

    POST /takeaway/printKitchen
    打印后厨菜单

    POST /takeaway/queryMoneyAndDealNumber
    查询交易金额和交易数量

    POST /takeaway/queryStoreAuthorization
    门店授权页面

    POST /takeaway/queryAutoReceive
    商家查询是否自动接单

    POST /takeaway/listOrder
    查询外卖订单

    POST /takeaway/getOrderDetail
    查询外卖订单详情

    POST /takeaway/setAutoReceive
    商家操作是否自动接单

    POST /takeaway/test
    
2. 外卖Consumers TakeawayController，Producers调用

    无
    
3. TakeawayController Saas内部服务调用测试 

    单元测试即可
    
4. TakeawayController Producers调用测试

    无

### 内部MessageQueue

1. 消费者 package/event/ConsumersRocketListener, source: Producer服务

    新订单
    订单取消
    确认接单
    订单配送
    订单完成
    确认退款

2. 生产者 package/service/RocketMQ/RocketMqService#mtTakeawayOrder, target: Producer服务

3. 生产者 package/service/RocketMQ/RocketMqService#orderLog, target: Order服务

### 数据库设计

`hst_order` 订单、账单

`hst_order_discount` 内部优惠、平台优惠

`hst_order_dishes` 订单菜品详情

`hst_store_config` 门店配置（如是否自动接单）

`hst_order_dishes_package` 订单套餐（未使用）

`hst_order_dishes_practice` 订单菜品做法（未使用）

`hst_order_receive_msg` （未使用）

`hst_order_response_msg`（未使用）

## 三方对接-美团

[美团官网](https://developer.meituan.com/home)

[文档中心](https://developer.meituan.com/openapi)

[开发者中心](https://developer.meituan.com/admin#/?_k=8r53lu)

[商家Web后台](http://e.waimai.meituan.com/#/v2/order/pre)

1. 开发者帐号

    - 普通登录方式
    
        开发环境：
        
        开发者帐号：无 
        
        密码： 无

    - 普通登录方式
    
        测试环境：
           
        开发者帐号：xxx 
    
        密码： xxx
        
        开发者id： 105325

    - 普通登录方式

        测试环境：
    
        开发者帐号：17358597917 (慎用，公司座机)
    
        密码：zkz1234567
        
        开发者id： 104795
    
    - 普通登录方式
        
        天翼云线上环境：
        
        开发者帐号：18190945269 (慎用)
      
        密码：zkz123456
            
        开发者id： 105325    
    
    - 普通登录方式
    
        阿里环境线上环境：
    
        开发者帐号：18190813619 （慎用，李运强）
    
        密码： zkz123456
        
        开发者id： 104664
    
2. 门店帐号
        
    - 开发环境

        测试账号信息： ?
        
        测试门店名称： ?   
        
        测试门店模块： 外卖模块
        
        回调地址域名端口：https://mch-dev.holderzone.cn/gateway/merchant/takeout/callback/mt/bind

    - 测试环境

        测试账号信息： 测试帐号
        
        测试门店名称： t_Hm91c4ZY   
        
        测试门店模块： 外卖模块
        
        回调地址域名端口：https://mch-sit.holderzone.cn/gateway/merchant/takeout/callback/mt/bind

3. 如何对接

    略，看官方文档

4. 如何寻求帮助

    除了帮助文档外，只支持微信群（网页底部官方微信）

## 三方对接-饿了么

[饿了么官网](https://nest-fe.faas.ele.me/openapi/help-center/index)

[接入指南](https://nest-fe.faas.ele.me/openapi/documents/startguide)

1. 开发者帐号

    开发者帐号：成都掌控者网络科技有限公司(13551185710) 
    
    密码： zkz19412942
    
    注意： 只能使用公司名登录

2. 测试帐号

    Java项目组使用了如下模块
    
    - saas开发环境
        
        回调地址URL: https://mch-dev.holderzone.cn/gateway/merchant/takeout/callback/ele/bind
        
        推送URL: https://mch-dev.holderzone.cn/gateway/merchant/takeout/callback/ele/order
    
    - saas正式环境(天翼云)
    
        回调地址URL: https://mch-re.holderzone.cn/gateway/merchant/takeout/callback/ele/bind
        
        推送URL: https://mch-re.holderzone.cn/gateway/merchant/takeout/callback/ele/order
         - 智慧门店外卖
    
        同saas开发环境一样的配置，和智慧门店交替着在用，考虑换成saas开发环境
    
    原智慧门店项目组使用了如下模块
    
    - 智慧门店系统新
    
    - 智慧门店外卖
    
    - 智慧门店系统
    
    - 智慧门店测试

3. 如何对接

    每一应用都有沙箱授权和正式授权
    
    注意项：
    沙箱环境配置
        ngrok内网穿透：
            每次启动url都与上次不一样，所以每次启动需要配置沙箱环境的回调、推送Url
            并且项目yml中 属性 ele:REDIRECT_URL 也需要配置与上述url一致
        目前在用 智慧门店外卖 的 沙箱 Key Secret
    环境授权配置
    
    回调设置：回调的聚合层，gateway放行
    
    小工具tips:
        测试小工具->追踪订单
        生成签名工具->测试签名
    
    其余暂时略
    
4. 如何寻求帮助

    除了官方文档外，只支持“我要提问”功能
    
5. 推送

    饿了么沙箱环境不推送解除绑定通知，
    
        跨企业解绑、绑定时，无法做到上一个企业的解绑操作；
        
        企业内解绑、绑定时，可检测是否已存在相同userId的绑定数据，有则更新，无则插入。
        
    饿了么正式环境会推送解除绑定通知
    
        跨企业解绑、绑定时，可正确做到上一个企业解绑，这一个企业绑定；
        
        企业内解绑时，无法确定解绑回调、绑定回调的先后顺序，无法使用一个逻辑做到在正确解绑时还能正确插入。
        唯一解决办法是绑定成功后更新ErpStoreGuid到饿了么，解绑时匹配ErpStoreGuid是否一致，一致才删除，否则，更新。
    
## 内部对接

### 后端

源代码地址: [holder-saas-store-takeaway](http://101.37.252.104/holderRD/holder-java/saas-platform/holder-saas-store-takeaway)

子模块地址: 

[holder-saas-store-takeaway-consumers](http://101.37.252.104/holderRD/holder-java/saas-platform/holder-saas-store-takeaway/tree/master/holder-saas-store-takeaway-consumers)

[holder-saas-store-takeaway-producers](http://101.37.252.104/holderRD/holder-java/saas-platform/holder-saas-store-takeaway/tree/master/holder-saas-store-takeaway-producers)

聚合层地址：

[holder-saas-aggregation-app](http://101.37.252.104/holderRD/holder-java/saas-platform-enter/holder-saas-aggregation-app)TakeawayController 和 TakeawayPrintController

[holder-saas-aggregation-merchant](http://101.37.252.104/holderRD/holder-java/saas-platform-enter/holder-saas-aggregation-merchant)TakeawayController


### 产品

1. 负责人

    产品前端：周萤
    
    产品移动端：郑冬梅
    
2. 原型文档
    
    [后台重构原型设计](http://101.37.252.104/holderRD/holder-java/saas-platform/holder-saas-store-docs/tree/master/version-1.0/2.%E5%8E%9F%E5%9E%8B%E6%96%87%E6%A1%A3/%E5%90%8E%E5%8F%B0%E9%87%8D%E6%9E%84%E5%8E%9F%E5%9E%8B%E8%AE%BE%E8%AE%A1)
    
    [客户端重构原型设计](http://101.37.252.104/holderRD/holder-java/saas-platform/holder-saas-store-docs/tree/master/version-1.0/2.%E5%8E%9F%E5%9E%8B%E6%96%87%E6%A1%A3/%E5%AE%A2%E6%88%B7%E7%AB%AF%E9%87%8D%E6%9E%84%E5%8E%9F%E5%9E%8B%E8%AE%BE%E8%AE%A1)

### 前端

1. 负责人

    who? todo

### 移动端

1. 负责人

    夏欢

### 测试

1. 负责人

    兰小梅

