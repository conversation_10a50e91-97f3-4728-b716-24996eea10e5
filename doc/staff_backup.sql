-- MySQL dump 10.16  Distrib 10.1.29-MariaDB, for debian-linux-gnu (x86_64)
--
-- Host: ***************    Database: hss_staff_6501005887110999041_db
-- ------------------------------------------------------
-- Server version	5.7.20-log

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `hss_data_dictionary`
--

DROP TABLE IF EXISTS `hss_data_dictionary`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `hss_data_dictionary` (
  `id` bigint(64) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `guid` varchar(50) DEFAULT NULL COMMENT '唯一标识',
  `type_code` varchar(50) DEFAULT NULL COMMENT '字典类型编码',
  `type_name` varchar(50) DEFAULT NULL COMMENT '字典类型名称',
  `item_code` int(32) unsigned DEFAULT NULL COMMENT '字典项目编码',
  `item_name` varchar(50) DEFAULT NULL COMMENT '字典项目名称',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_guid` (`guid`),
  KEY `idx_type_code_item_code` (`type_code`,`item_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据字典表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `hss_menu`
--

DROP TABLE IF EXISTS `hss_menu`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `hss_menu` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `menu_guid` varchar(50) NOT NULL COMMENT '菜单guid',
  `menu_name` varchar(50) NOT NULL COMMENT '菜单名称',
  `menu_url` varchar(100) DEFAULT NULL COMMENT '菜单地址',
  `parent_ids` varchar(200) NOT NULL COMMENT '上级菜单id，逗号分割，最顶级为模块guid',
  `menu_icon` varchar(100) DEFAULT NULL COMMENT '菜单图标',
  `menu_sort` int(11) NOT NULL COMMENT '菜单排序，默认为1',
  `module_guid` varchar(50) DEFAULT NULL COMMENT '模块guid',
  `terminal_guid` varchar(50) NOT NULL COMMENT '终端guid',
  `is_enable` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '是否启用，默认为1-已启用',
  `create_staff_guid` varchar(50) NOT NULL COMMENT '创建人guid',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_sync` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '同步时间',
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `menu_guid_UNIQUE` (`menu_guid`)
) ENGINE=InnoDB AUTO_INCREMENT=93 DEFAULT CHARSET=utf8mb4 COMMENT='菜单表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `hss_r_user_role`
--

DROP TABLE IF EXISTS `hss_r_user_role`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `hss_r_user_role` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `guid` varchar(50) NOT NULL COMMENT 'guid',
  `user_guid` varchar(50) NOT NULL COMMENT '员工guid',
  `role_guid` varchar(50) NOT NULL COMMENT '角色guid',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `guid_UNIQUE` (`guid`),
  KEY `idx_user_guid` (`user_guid`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COMMENT='用户-角色表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `hss_role`
--

DROP TABLE IF EXISTS `hss_role`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `hss_role` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `guid` varchar(50) NOT NULL COMMENT 'guid',
  `name` varchar(50) NOT NULL COMMENT '角色名称：必填，1-16个字',
  `is_enable` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用（默认为1-已启用）',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除（默认为0-未删除）',
  `create_staff_guid` varchar(50) NOT NULL COMMENT '创建人guid：必填',
  `modified_staff_guid` varchar(50) NOT NULL COMMENT '更新人guid：必填',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间：必填',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间：必填',
  PRIMARY KEY (`id`),
  UNIQUE KEY `guid_UNIQUE` (`guid`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COMMENT='角色表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `hss_role_source`
--

DROP TABLE IF EXISTS `hss_role_source`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `hss_role_source` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `role_guid` varchar(50) NOT NULL COMMENT '角色guid',
  `terminal_guid` varchar(50) NOT NULL COMMENT '终端guid',
  `terminal_code` varchar(50) NOT NULL COMMENT '终端code',
  `terminal_name` varchar(50) NOT NULL COMMENT '终端名称',
  `menu_guid` varchar(50) NOT NULL COMMENT '菜单guid',
  `source_guid` varchar(50) NOT NULL COMMENT '资源guid',
  `source_code` varchar(50) NOT NULL COMMENT '服务资源编码',
  `source_url` varchar(200) NOT NULL COMMENT '服务资源url',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=77 DEFAULT CHARSET=utf8mb4 COMMENT='角色-资源表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `hss_store_source`
--

DROP TABLE IF EXISTS `hss_store_source`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `hss_store_source` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `enterprise_guid` varchar(50) DEFAULT NULL COMMENT '企业guid',
  `store_guid` varchar(50) DEFAULT NULL COMMENT '门店guid',
  `product_guid` varchar(50) DEFAULT NULL COMMENT '产品guid',
  `product_name` varchar(50) DEFAULT NULL COMMENT '产品名称',
  `terminal_guid` varchar(50) DEFAULT NULL COMMENT '终端guid',
  `terminal_name` varchar(50) DEFAULT NULL COMMENT '终端名称',
  `terminal_code` varchar(50) DEFAULT NULL COMMENT '终端code',
  `module_guid` varchar(50) DEFAULT NULL COMMENT '模块guid（存云端最底级模块guid）',
  `module_name` varchar(50) DEFAULT NULL COMMENT '模块名称（存云端最底级模块名）',
  `module_type` char(1) DEFAULT NULL COMMENT '模块类型（0-基础类业务，1-权限类业务）',
  `page_title` varchar(50) DEFAULT NULL COMMENT '跳转页标题',
  `page_url` varchar(100) DEFAULT NULL COMMENT '跳转页url',
  `source_guid` varchar(50) DEFAULT NULL COMMENT '资源guid,唯一',
  `source_name` varchar(50) DEFAULT NULL COMMENT '资源名称',
  `source_code` varchar(50) DEFAULT NULL COMMENT '资源code',
  `source_url` varchar(200) DEFAULT NULL COMMENT '资源url',
  `gmt_product_start` date DEFAULT NULL COMMENT '产品授权起始日期',
  `gmt_product_end` date DEFAULT NULL COMMENT '产品授权截止日期',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据创建时间',
  `gmt_sync` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '数据同步时间',
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=319 DEFAULT CHARSET=utf8mb4 COMMENT='企业/门店关联资源表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `hss_user`
--

DROP TABLE IF EXISTS `hss_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `hss_user` (
  `id` bigint(64) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `guid` varchar(50) NOT NULL COMMENT '用户GUID',
  `enterprise_no` char(8) NOT NULL COMMENT '企业编码：必填 8个数字',
  `account` char(6) NOT NULL COMMENT '员工账号：必填，3-6个数字',
  `password` varchar(50) NOT NULL COMMENT '员工密码：必填，不超过50个数字(25个中文)',
  `auth_code` char(6) NOT NULL COMMENT '授权码：必填，6位',
  `name` varchar(50) NOT NULL COMMENT '姓名：必填，2-20字',
  `phone` varchar(20) NOT NULL COMMENT '手机号：必填，11位手机号',
  `org_guid` varchar(50) DEFAULT NULL COMMENT '用户所属组织GUID',
  `office_code` varchar(50) DEFAULT NULL COMMENT '用户职位编码',
  `office_name` varchar(50) DEFAULT NULL COMMENT '用户职位名称',
  `id_card_no` varchar(20) DEFAULT NULL COMMENT '员工身份证号码：选填，18位',
  `id_card_address` varchar(200) DEFAULT NULL COMMENT '员工身份证地址：选填，不超过200个数字(100个中文)',
  `address` varchar(200) DEFAULT NULL COMMENT '员工居住地址：选填，不超过200个数字(100个中文)',
  `birthday` datetime DEFAULT NULL COMMENT '员工生日：选填',
  `on_boarding_time` datetime DEFAULT NULL COMMENT '员工入职时间：选填',
  `discount_threshold` decimal(10,2) DEFAULT NULL COMMENT '整单折扣阈值\n',
  `allowance_threshold` decimal(10,2) DEFAULT NULL COMMENT '整单让价阈值',
  `roles_distributable` varchar(300) DEFAULT NULL COMMENT '用户可分配的角色id，逗号分割',
  `create_staff_guid` varchar(50) NOT NULL COMMENT '创建人GUID：必填',
  `update_staff_guid` varchar(50) NOT NULL COMMENT '更新人GUID：必填',
  `is_enable` tinyint(1) unsigned NOT NULL COMMENT '是否启用：0=禁用，1=启用',
  `is_deleted` tinyint(1) unsigned NOT NULL COMMENT '是否删除：0=未删除，1=已删除',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_guid` (`guid`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='用户表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `hss_user_data`
--

DROP TABLE IF EXISTS `hss_user_data`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `hss_user_data` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `rule_guid` varchar(50) NOT NULL COMMENT '规则GUID,唯一',
  `user_guid` varchar(50) NOT NULL COMMENT '用户GUID',
  `store_guid` varchar(50) DEFAULT NULL COMMENT '门店guid',
  `store_name` varchar(50) DEFAULT NULL COMMENT '门店名称',
  `organization_guid` varchar(50) DEFAULT NULL COMMENT '组织guid',
  `organization_name` varchar(50) DEFAULT NULL COMMENT '组织名称',
  `region_code` varchar(50) DEFAULT NULL COMMENT '区域code',
  `region_name` varchar(50) DEFAULT NULL COMMENT '区域名称\n',
  `brand_guid` varchar(50) DEFAULT NULL COMMENT '品牌guid',
  `brand_name` varchar(50) DEFAULT NULL,
  `is_deleted` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=139 DEFAULT CHARSET=utf8mb4 COMMENT='用户管理的组织-区域-品牌表';
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2019-02-22 16:56:49
