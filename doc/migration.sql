CREATE TABLE `hss_private_room` (
    `id` bigint(64) unsigned NOT NULL AUTO_INCREMENT,
    `guid` varchar(50) NOT NULL,
    `store_guid` varchar(50) NOT NULL,
    `table_guid` varchar(50) NOT NULL,
    `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

ALTER TABLE `hss_reserve_config`
    ADD COLUMN `is_enable_private_room` BIT(1) NOT NULL DEFAULT b'0' AFTER `is_enable_warn_message`;


# 以下是插入模拟数据，生产环境不要执行

INSERT INTO `hsc_common_db`.`hsc_common_config` (
    `guid`, `enterprise_guid`, `store_guid`,
    `dic_code`, `dic_name`, `dict_value`,
    `gmt_create`, `gmt_modified`, `is_enable`
)
VALUES (
    '6561875779676086274', '6506431195651982337', '6506453252643487745',
    '102', '云呼商户预订电话', '17302864356',
    '2019-10-11 11:16:28', '2019-10-11 11:16:28', '1'
);

UPDATE
    `hsr_reserve_6506431195651982337_db`.`hss_reserve_config`
SET
    `is_enable_private_room` = 0b1
WHERE
    (`store_guid` = '6506453252643487745');

INSERT INTO `hsr_reserve_6506431195651982337_db`.`hss_private_room` (
    `guid`,
    `store_guid`, `table_guid`,
    `gmt_create`, `gmt_modified`
)
VALUES (
    '6561875779676086275',
    '6506453252643487745', '6506433795862822913',
    '2019-10-11 13:46:30', '2019-10-11 13:46:30'
);

