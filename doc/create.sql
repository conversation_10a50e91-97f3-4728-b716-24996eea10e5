DROP TABLE IF EXISTS `hsb_surcharge`;
CREATE TABLE `hsb_surcharge` (
  `id` bigint(64) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `surcharge_guid` varchar(45) DEFAULT NULL COMMENT '附加费Guid',
  `store_guid` varchar(45) DEFAULT NULL COMMENT '门店Guid',
  `name` varchar(45) DEFAULT NULL COMMENT '附加费名称',
  `amount` decimal(10,2) unsigned DEFAULT NULL COMMENT '附加费金额',
  `type` tinyint(1) unsigned DEFAULT NULL COMMENT '附加费类型：0=按人，1=按桌',
  `is_enable` tinyint(1) unsigned DEFAULT '1' COMMENT '是否已启用：0=未启用，1=已启用',
  `is_deleted` tinyint(1) unsigned DEFAULT '0' COMMENT '是否已删除：0=未删除，1=已删除',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_surcharge_guid` (`surcharge_guid`),
  INDEX `idx_store_type` (`store_guid` ASC, `type` ASC)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='营业中心-附加费';

DROP TABLE IF EXISTS `hsb_surcharge_area`;
CREATE TABLE `hsb_surcharge_area` (
  `id` bigint(64) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `guid` varchar(45) DEFAULT NULL COMMENT '唯一Guid',
  `surcharge_guid` varchar(45) DEFAULT NULL COMMENT '附加费Guid',
  `area_guid` varchar(45) NOT NULL COMMENT '区域Guid',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_guid` (`guid`),
  INDEX `idx_surcharge_guid` (`surcharge_guid`),
  INDEX `idx_area_guid` (`area_guid`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='营业中心-附加费关联区域';