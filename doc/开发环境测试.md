# DEV环境测试流程

## 基础环境

1. 商户环境

    商户号：58522925
    
    管理员帐号：100000
    
    管理员密码：865344
    
    门店号：9045464
    
    员工帐号：100001
    
    员工密码：111111

## Ngrok配置

```bash
cd /opt/ngrok
./ngrok http ***************:8151

拷贝ngrok映射地址
```

## 饿了么配置

1. 登录饿了么开发者中心，修改回调URL

    [饿了么-我的应用](https://nest-fe.faas.ele.me/openapi/manage-center) 

    帐号：成都掌控者网络科技有限公司
    
    密码：zkz121212
    
    选择：saas开发环境-沙箱环境
    
    更换“回调地址URL”为：上述拷贝的ngrok映射地址/merchant/waimai/ele/bind/callback
    
    更换“推送URL”为：上述拷贝的ngrok映射地址/merchant/waimai/ele/order/callback
    
    保存，查看ngrok是否由相应的输出。如果保存失败，多保存几次即可。
    
    拷贝 clientKey, secret 于下一步使用（第一次配置后就不需要再拷贝了了）

2. 更改Producers和Consumers的application-dev.yml，设置其clientKey, secret, redirect_url，注意这三者是同一个应用的参数（第一次配置后就不需要修改了）

    ```yml
    ele:
      ISSANDBOX: true
      REDIRECT_URL: 上述拷贝的ngrok映射地址/merchant/waimai/ele/bind/callback
      CLIENT_KEY: pwQhokx8Q4
      SECRET: 5adb17934eef02569db21197e9062e03326b9c17
    ```

3. 更改Producers和Consumers的application-dev.yml，正确配置其redirect_url

    ```yml
    ele:
      REDIRECT_URL: 上述拷贝的ngrok映射地址/merchant/waimai/ele/bind/callback
    ```

4. 登录SAAS商家后台，复制菜品skuGuid（第一次绑定成功就不需要修改了）

    [商家后台登录](http://***************:71/login#/)

    商户号: 58522925
    
    员工帐号：100001
    
    员工密码：111111
    
    选择“销售管理”
    
    选择“门店菜品”
    
    查询“目标门店”
    
    点击“添加菜品”
    
    点击“上架菜品”
    
    拷贝其 skuGuid 于下一步使用（第一次绑定成功就不需要拷贝了）
    
5. 登录饿了么商家客户端，绑定菜品（第一次绑定成功就不需要修改了）

    [饿了么商家登录](https://melody.shop.ele.me/login)
    
    帐号：sandbox_15289259
    
    密码：xaCMzQwg67a8

    选择“商品栏目”
    
    选择一个编辑
    
    商品规格，填入SKU码(上一步复制的Saas菜品skuGuid)，以建立与Sass菜品的映射，完善其他信息，保存

6. 登录SAAS商家后台，绑定门店（第一次绑定成功就不需要修改了）

    [商家后台登录](http://***************:71/login#/)

    商户号: 58522925
    
    员工帐号：100001
    
    员工密码：111111
    
    选择“销售管理”
    
    选择“外卖管理”
    
    点击“饿了么外卖”
    
    选择一个门店点击绑定
    
    授权帐号：sandbox_15289259
        
    授权密码：xaCMzQwg67a8
    
    同意授权
    
7. 再次进入饿了么商家客户端，扫描门店二维码，下单

    [饿了么商家登录](https://melody.shop.ele.me/login)
    
    帐号：sandbox_15289259
    
    密码：xaCMzQwg67a8

    选择“门店”栏目
    
    选择“门店信息”
    
    扫描门店二维码，下单
    
8. DEV环境无Canal同步，所以饿了么无法切库！！！

## 美团外卖配置

1. 登录美团外卖开发者中心，修改回调URL

    [美团外卖-开发者中心](https://passport.meituan.com/account/unitivelogin) 

    帐号：***********
    
    密码：zkz123456
    
    回调接口设置
    
    推送订单URL                 上述拷贝的ngrok映射地址/merchant/waimai/mt/order/callback/first
       
    美团用户或客服取消URL       上述拷贝的ngrok映射地址/merchant/waimai/mt/order/callback/cancel
       
    美团用户或客服退款流程URL   上述拷贝的ngrok映射地址/merchant/waimai/mt/order/callback/refund
       
    已完成订单推送回调URL       上述拷贝的ngrok映射地址/merchant/waimai/mt/order/callback/over
       
    订单已确认的回调URL         上述拷贝的ngrok映射地址/merchant/waimai/mt/order/callback/confirm
       
    订单配送状态的回调URL       上述拷贝的ngrok映射地址/merchant/waimai/mt/order/callback/shipping
       
    隐私号降级URL               上述拷贝的ngrok映射地址/merchant/waimai/mt/order/phoneNumber/callback
       
    门店映射回调地址            上述拷贝的ngrok映射地址/merchant/waimai/mt/bind/callback
       
    门店映射解绑回调地址        上述拷贝的ngrok映射地址/merchant/waimai/mt/unbind/callback
    
    拷贝其 developerId, signKey 于下一步使用（第一次配置后就不需要拷贝了）
    
    拷贝其"t_9FwNEdZS"门店的 “测试账号”, “密码” 于下下一步使用（第一次配置后就不需要拷贝了）
    
2. 更改Producers和Consumers的application-dev.yml，设置其 developerId, signKey（第一次配置后就不需要修改了）

    ```yml
    mt:
      DEVELOPER_ID: 104664
      SIGN_KEY: 9cs57yydie0wsfj2

3. 登录SAAS商家后台，创建菜品（第一次创建成功就不需要修改了）

    [商家后台登录](http://***************:71/login#/)

    商户号: 58522925
    
    员工帐号：100001
    
    员工密码：111111
    
    选择“销售管理”
    
    选择“菜品管理”
    
    选择“门店菜品”
    
    查询“目标门店”
    
    创建“大于等于美团菜品数量的菜品，因为SAAS端的自动创建菜品接口出错”

4. 登录SAAS商家后台，绑定菜品（第一次绑定成功就不需要修改了）

    [商家后台登录](http://***************:71/login#/)

    商户号: 58522925
    
    员工帐号：100001
    
    员工密码：111111
    
    选择“销售管理”
    
    选择“外卖管理”
    
    选择“美团外卖”
    
    选择“要绑定的门店”，点击绑定，
    
    授权帐号：1212zw_19900003291
    
    授权密码：zw19900003291
    
    然后，在绑定页面绑定菜品。绑定页面左侧是美团菜品，右侧是SAAS端菜品，对每一个美团菜品绑定一个不同的SAAS端菜品。完成即可。
    
5. 登录美团外卖商家中心，下单
    
    [美团外卖-商家中心](http://e.waimai.meituan.com/logon)
    
    帐号：1212zw_19900003291

    密码：zw19900003291
    
    选择“店铺设置”
    
    选择“门店管理”
    
    扫描店铺二维码，下单

## 一体机

1. 登录一体机

    门店号: 9045464
    
    员工帐号：100001
    
    员工密码：111111
    
2. 确认打印机已配置

    为下单的菜品配置一个打印机

3. 确认一体机声音已开启

    略
    
4. 观察是否有新订单语音提示，是否有新订单记录