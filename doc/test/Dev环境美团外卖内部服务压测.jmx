<?xml version="1.0" encoding="UTF-8"?>
<jmeterTestPlan version="1.2" properties="2.6" jmeter="2.11.20151206">
  <hashTree>
    <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="开发环境美团测试" enabled="true">
      <boolProp name="HTTPSampler.postBodyRaw">true</boolProp>
      <elementProp name="HTTPsampler.Arguments" elementType="Arguments">
        <collectionProp name="Arguments.arguments">
          <elementProp name="" elementType="HTTPArgument">
            <boolProp name="HTTPArgument.always_encode">false</boolProp>
            <stringProp name="Argument.value">{&#xd;
  &quot;developerId&quot;: 104664,&#xd;
  &quot;sign&quot;: &quot;2709668ff6c0ef1163252e04611074146e10f273&quot;,&#xd;
  &quot;order&quot;: &quot;{\&quot;avgSendTime\&quot;:4200.0,\&quot;caution\&quot;:\&quot;\&quot;,\&quot;cityId\&quot;:999999,\&quot;ctime\&quot;:1541994484,\&quot;daySeq\&quot;:\&quot;18\&quot;,\&quot;deliveryTime\&quot;:0,\&quot;detail\&quot;:\&quot;[{\\\&quot;app_food_code\\\&quot;:\\\&quot;6465540744079668225\\\&quot;,\\\&quot;box_num\\\&quot;:1,\\\&quot;box_price\\\&quot;:0,\\\&quot;cart_id\\\&quot;:0,\\\&quot;food_discount\\\&quot;:1,\\\&quot;food_name\\\&quot;:\\\&quot;西瓜\\\&quot;,\\\&quot;food_property\\\&quot;:\\\&quot;\\\&quot;,\\\&quot;price\\\&quot;:0.01,\\\&quot;quantity\\\&quot;:1,\\\&quot;sku_id\\\&quot;:\\\&quot;6465540744092254209\\\&quot;,\\\&quot;spec\\\&quot;:\\\&quot;\\\&quot;,\\\&quot;unit\\\&quot;:\\\&quot;\\\&quot;}]\&quot;,\&quot;dinnersNumber\&quot;:0,\&quot;ePoiId\&quot;:\&quot;6465539874333666305\&quot;,\&quot;extras\&quot;:\&quot;[{}]\&quot;,\&quot;hasInvoiced\&quot;:0,\&quot;invoiceTitle\&quot;:\&quot;\&quot;,\&quot;isFavorites\&quot;:false,\&quot;isPoiFirstOrder\&quot;:false,\&quot;isThirdShipping\&quot;:0,\&quot;latitude\&quot;:29.77389,\&quot;logisticsCode\&quot;:\&quot;0000\&quot;,\&quot;longitude\&quot;:95.36876,\&quot;orderId\&quot;:32856432201175871,\&quot;orderIdView\&quot;:32856432201175871,\&quot;originalPrice\&quot;:0.02,\&quot;payType\&quot;:1,\&quot;poiAddress\&quot;:\&quot;南极洲04号站\&quot;,\&quot;poiFirstOrder\&quot;:false,\&quot;poiId\&quot;:3285643,\&quot;poiName\&quot;:\&quot;t_9FwNEdZS\&quot;,\&quot;poiPhone\&quot;:\&quot;4009208801\&quot;,\&quot;poiReceiveDetail\&quot;:\&quot;{\\\&quot;actOrderChargeByMt\\\&quot;:[{\\\&quot;comment\\\&quot;:\\\&quot;活动款\\\&quot;,\\\&quot;feeTypeDesc\\\&quot;:\\\&quot;活动款\\\&quot;,\\\&quot;feeTypeId\\\&quot;:10019,\\\&quot;moneyCent\\\&quot;:0}],\\\&quot;actOrderChargeByPoi\\\&quot;:[],\\\&quot;foodShareFeeChargeByPoi\\\&quot;:0,\\\&quot;logisticsFee\\\&quot;:1,\\\&quot;onlinePayment\\\&quot;:2,\\\&quot;wmPoiReceiveCent\\\&quot;:2}\&quot;,\&quot;recipientAddress\&quot;:\&quot;色金拉@#西藏自治区林芝市墨脱县色金拉\&quot;,\&quot;recipientName\&quot;:\&quot;滕(先生)\&quot;,\&quot;recipientPhone\&quot;:\&quot;17302864356\&quot;,\&quot;shipperPhone\&quot;:\&quot;\&quot;,\&quot;shippingFee\&quot;:0.01,\&quot;status\&quot;:2,\&quot;taxpayerId\&quot;:\&quot;\&quot;,\&quot;total\&quot;:0.02,\&quot;utime\&quot;:1541994484}&quot;,&#xd;
  &quot;epoiId&quot;: &quot;6465539874333666305&quot;&#xd;
}</stringProp>
            <stringProp name="Argument.metadata">=</stringProp>
          </elementProp>
        </collectionProp>
      </elementProp>
      <stringProp name="HTTPSampler.domain">localhost</stringProp>
      <stringProp name="HTTPSampler.port">8918</stringProp>
      <stringProp name="HTTPSampler.connect_timeout"></stringProp>
      <stringProp name="HTTPSampler.response_timeout"></stringProp>
      <stringProp name="HTTPSampler.protocol"></stringProp>
      <stringProp name="HTTPSampler.contentEncoding"></stringProp>
      <stringProp name="HTTPSampler.path">/mt/callback/order/first</stringProp>
      <stringProp name="HTTPSampler.method">POST</stringProp>
      <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
      <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
      <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
      <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
      <stringProp name="HTTPSampler.implementation">HttpClient4</stringProp>
      <boolProp name="HTTPSampler.monitor">false</boolProp>
      <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
    </HTTPSamplerProxy>
    <hashTree>
      <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP信息头管理器" enabled="true">
        <collectionProp name="HeaderManager.headers">
          <elementProp name="" elementType="Header">
            <stringProp name="Header.name">Accept</stringProp>
            <stringProp name="Header.value">*/*</stringProp>
          </elementProp>
          <elementProp name="" elementType="Header">
            <stringProp name="Header.name">Cache-Control</stringProp>
            <stringProp name="Header.value">no-cache</stringProp>
          </elementProp>
          <elementProp name="" elementType="Header">
            <stringProp name="Header.name">content-type</stringProp>
            <stringProp name="Header.value">application/json</stringProp>
          </elementProp>
        </collectionProp>
      </HeaderManager>
      <hashTree/>
      <ResultCollector guiclass="ViewResultsFullVisualizer" testclass="ResultCollector" testname="察看结果树" enabled="true">
        <boolProp name="ResultCollector.error_logging">false</boolProp>
        <objProp>
          <name>saveConfig</name>
          <value class="SampleSaveConfiguration">
            <time>true</time>
            <latency>true</latency>
            <timestamp>true</timestamp>
            <success>true</success>
            <label>true</label>
            <code>true</code>
            <message>true</message>
            <threadName>true</threadName>
            <dataType>true</dataType>
            <encoding>false</encoding>
            <assertions>true</assertions>
            <subresults>true</subresults>
            <responseData>false</responseData>
            <samplerData>false</samplerData>
            <xml>false</xml>
            <fieldNames>false</fieldNames>
            <responseHeaders>false</responseHeaders>
            <requestHeaders>false</requestHeaders>
            <responseDataOnError>false</responseDataOnError>
            <saveAssertionResultsFailureMessage>false</saveAssertionResultsFailureMessage>
            <assertionsResultsToSave>0</assertionsResultsToSave>
            <bytes>true</bytes>
          </value>
        </objProp>
        <stringProp name="filename"></stringProp>
      </ResultCollector>
      <hashTree/>
      <ResultCollector guiclass="StatVisualizer" testclass="ResultCollector" testname="聚合报告" enabled="true">
        <boolProp name="ResultCollector.error_logging">false</boolProp>
        <objProp>
          <name>saveConfig</name>
          <value class="SampleSaveConfiguration">
            <time>true</time>
            <latency>true</latency>
            <timestamp>true</timestamp>
            <success>true</success>
            <label>true</label>
            <code>true</code>
            <message>true</message>
            <threadName>true</threadName>
            <dataType>true</dataType>
            <encoding>false</encoding>
            <assertions>true</assertions>
            <subresults>true</subresults>
            <responseData>false</responseData>
            <samplerData>false</samplerData>
            <xml>false</xml>
            <fieldNames>false</fieldNames>
            <responseHeaders>false</responseHeaders>
            <requestHeaders>false</requestHeaders>
            <responseDataOnError>false</responseDataOnError>
            <saveAssertionResultsFailureMessage>false</saveAssertionResultsFailureMessage>
            <assertionsResultsToSave>0</assertionsResultsToSave>
            <bytes>true</bytes>
          </value>
        </objProp>
        <stringProp name="filename"></stringProp>
      </ResultCollector>
      <hashTree/>
    </hashTree>
  </hashTree>
</jmeterTestPlan>
