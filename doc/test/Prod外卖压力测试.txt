美团
请求方式：Post
参数方式：RequestParam（跟在URL的?后）
URL地址：https://mch.holderzone.net/gateway/merchant/takeout/callback/mt/order/first
请求Param如下

developerId: 104664

sign: 2709668ff6c0ef1163252e04611074146e10f273

order: {"avgSendTime":4200.0,"caution":"","cityId":999999,"ctime":1541994484,"daySeq":"18","deliveryTime":0,"detail":"[{\"app_food_code\":\"6465540744079668225\",\"box_num\":1,\"box_price\":0,\"cart_id\":0,\"food_discount\":1,\"food_name\":\"西瓜\",\"food_property\":\"\",\"price\":0.01,\"quantity\":1,\"sku_id\":\"6465540744092254209\",\"spec\":\"\",\"unit\":\"\"}]","dinnersNumber":0,"ePoiId":"6478829386148714497","extras":"[{}]","hasInvoiced":0,"invoiceTitle":"","isFavorites":false,"isPoiFirstOrder":false,"isThirdShipping":0,"latitude":29.77389,"logisticsCode":"0000","longitude":95.36876,"orderId":32856432201175871,"orderIdView":32856432201175871,"originalPrice":0.02,"payType":1,"poiAddress":"南极洲04号站","poiFirstOrder":false,"poiId":3285643,"poiName":"t_9FwNEdZS","poiPhone":"4009208801","poiReceiveDetail":"{\"actOrderChargeByMt\":[{\"comment\":\"活动款\",\"feeTypeDesc\":\"活动款\",\"feeTypeId\":10019,\"moneyCent\":0}],\"actOrderChargeByPoi\":[],\"foodShareFeeChargeByPoi\":0,\"logisticsFee\":1,\"onlinePayment\":2,\"wmPoiReceiveCent\":2}","recipientAddress":"色金拉@#西藏自治区林芝市墨脱县色金拉","recipientName":"滕(先生)","recipientPhone":"17302864356","shipperPhone":"","shippingFee":0.01,"status":2,"taxpayerId":"","total":0.02,"utime":1541994484}

ePoiId: 6478829386148714497

其中orderId需要随机，ePoiId需要匹配，order中的ePoiId需要匹配

Jmeter参数如下
host: takeout.easy.echosite.cn
path: /merchant/takeout/callback/mt/order/first










饿了么
请求方式：Post
参数方式：RequestBody（请求体Json）
URL地址：https://mch.holderzone.net/gateway/merchant/takeout/callback/ele/order
请求体：
{
  "requestId": "200023832433446625",
  "type": 10,
  "appId": 41193985,
  "message": "{\"id\":\"3032956277608090808\",\"orderId\":\"3032956277608090808\",\"address\":\"东平国家森林公园(1号门)\",\"createdAt\":\"2018-11-30T15:25:19\",\"activeAt\":\"2018-11-30T15:25:19\",\"deliverFee\":0.01,\"merchantDeliverySubsidy\":0.0,\"deliverTime\":null,\"description\":\"\",\"groups\":[{\"name\":\"1号篮子\",\"type\":\"normal\",\"items\":[{\"id\":1538909403,\"skuId\":200000347034886881,\"name\":\"特色烧鸡公[少冰+重辣]\",\"categoryId\":1,\"price\":0.01,\"quantity\":1,\"total\":0.01,\"additions\":[],\"newSpecs\":[{\"name\":\"规格\",\"value\":\"特色烧鸡公\"}],\"attributes\":[{\"name\":\"温度\",\"value\":\"少冰\"},{\"name\":\"辣度\",\"value\":\"重辣\"}],\"extendCode\":\"6473127185224395778\",\"barCode\":\"\",\"weight\":1.0,\"userPrice\":0.0,\"shopPrice\":0.0,\"vfoodId\":1489168570,\"ingredients\":[]},{\"id\":1538898148,\"skuId\":200000347030263521,\"name\":\"红烧狮子头\",\"categoryId\":1,\"price\":0.02,\"quantity\":1,\"total\":0.02,\"additions\":[],\"newSpecs\":[],\"attributes\":[],\"extendCode\":\"6468356238810100737\",\"barCode\":\"\",\"weight\":1.0,\"userPrice\":0.0,\"shopPrice\":0.0,\"vfoodId\":1489172425,\"ingredients\":[]}],\"relatedItems\":[]}],\"invoice\":null,\"book\":false,\"onlinePaid\":true,\"railwayAddress\":null,\"phoneList\":[\"17091956873,57\"],\"shopId\":*********,\"shopName\":\"saas测试i环测试店铺\",\"daySn\":6,\"status\":\"unprocessed\",\"refundStatus\":\"noRefund\",\"userId\":187118524,\"userIdStr\":\"187118524\",\"totalPrice\":0.04,\"originalPrice\":0.04,\"consignee\":\"滕**\",\"deliveryGeo\":\"121.48156917,31.67690196\",\"deliveryPoiAddress\":\"东平国家森林公园(1号门)\",\"invoiced\":false,\"income\":0.04,\"serviceRate\":0.08,\"serviceFee\":-0.0,\"hongbao\":0.0,\"packageFee\":0.0,\"activityTotal\":-0.0,\"shopPart\":-0.0,\"elemePart\":-0.0,\"downgraded\":false,\"vipDeliveryFeeDiscount\":0.0,\"openId\":\"6473126470256594946\",\"secretPhoneExpireTime\":\"2018-11-30T21:25:15\",\"orderActivities\":[],\"invoiceType\":null,\"taxpayerId\":\"\",\"coldBoxFee\":0.0,\"cancelOrderDescription\":null,\"cancelOrderCreatedAt\":null,\"orderCommissions\":[],\"baiduWaimai\":false,\"userExtraInfo\":{\"giverPhone\":\"\",\"greeting\":\"\"},\"consigneePhones\":[\"173****4356\"],\"superVip\":\"ELEME_SUPER_VIP\",\"confirmCookingTime\":null,\"orderActivityParts\":[{\"partName\":\"优惠总计\",\"partValue\":-0.0,\"weight\":10}],\"orderBusinessType\":0,\"pickUpTime\":\"1970-01-01T08:00:00\",\"pickUpNumber\":0,\"umpOrder\":0,\"tianmaoPart\":-0.0,\"shopBrandId\":0,\"userPart\":0.0,\"specUserPart\":0.0,\"isBusinessOrder\":false,\"pinTuanOrder\":false}",
  "shopId": *********,
  "timestamp": 1543562719138,
  "signature": "9816D040812B713CBFA4927A814601FF",
  "userId": 176926821992027911
}
其中orderId需要随机
