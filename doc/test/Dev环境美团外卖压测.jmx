<?xml version="1.0" encoding="UTF-8"?>
<jmeterTestPlan version="1.2" properties="2.6" jmeter="2.11.20151206">
  <hashTree>
    <TestPlan guiclass="TestPlanGui" testclass="TestPlan" testname="测试计划" enabled="true">
      <stringProp name="TestPlan.comments"></stringProp>
      <boolProp name="TestPlan.functional_mode">false</boolProp>
      <boolProp name="TestPlan.serialize_threadgroups">false</boolProp>
      <elementProp name="TestPlan.user_defined_variables" elementType="Arguments" guiclass="ArgumentsPanel" testclass="Arguments" testname="用户定义的变量" enabled="true">
        <collectionProp name="Arguments.arguments"/>
      </elementProp>
      <stringProp name="TestPlan.user_define_classpath"></stringProp>
    </TestPlan>
    <hashTree>
      <ThreadGroup guiclass="ThreadGroupGui" testclass="ThreadGroup" testname="线程组" enabled="true">
        <stringProp name="ThreadGroup.on_sample_error">continue</stringProp>
        <elementProp name="ThreadGroup.main_controller" elementType="LoopController" guiclass="LoopControlPanel" testclass="LoopController" testname="循环控制器" enabled="true">
          <boolProp name="LoopController.continue_forever">false</boolProp>
          <stringProp name="LoopController.loops">1</stringProp>
        </elementProp>
        <stringProp name="ThreadGroup.num_threads">1</stringProp>
        <stringProp name="ThreadGroup.ramp_time">1</stringProp>
        <longProp name="ThreadGroup.start_time">1544688260000</longProp>
        <longProp name="ThreadGroup.end_time">1544688260000</longProp>
        <boolProp name="ThreadGroup.scheduler">false</boolProp>
        <stringProp name="ThreadGroup.duration"></stringProp>
        <stringProp name="ThreadGroup.delay"></stringProp>
      </ThreadGroup>
      <hashTree>
        <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="开发环境美团测试" enabled="false">
          <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" testname="用户定义的变量" enabled="true">
            <collectionProp name="Arguments.arguments">
              <elementProp name="developerId" elementType="HTTPArgument">
                <boolProp name="HTTPArgument.always_encode">false</boolProp>
                <stringProp name="Argument.value">104664</stringProp>
                <stringProp name="Argument.metadata">=</stringProp>
                <boolProp name="HTTPArgument.use_equals">true</boolProp>
                <stringProp name="Argument.name">developerId</stringProp>
              </elementProp>
              <elementProp name="sign" elementType="HTTPArgument">
                <boolProp name="HTTPArgument.always_encode">false</boolProp>
                <stringProp name="Argument.value">2709668ff6c0ef1163252e04611074146e10f273</stringProp>
                <stringProp name="Argument.metadata">=</stringProp>
                <boolProp name="HTTPArgument.use_equals">true</boolProp>
                <stringProp name="Argument.name">sign</stringProp>
              </elementProp>
              <elementProp name="ePoiId" elementType="HTTPArgument">
                <boolProp name="HTTPArgument.always_encode">false</boolProp>
                <stringProp name="Argument.value">6477064113400228865</stringProp>
                <stringProp name="Argument.metadata">=</stringProp>
                <boolProp name="HTTPArgument.use_equals">true</boolProp>
                <stringProp name="Argument.name">ePoiId</stringProp>
              </elementProp>
              <elementProp name="order" elementType="HTTPArgument">
                <boolProp name="HTTPArgument.always_encode">false</boolProp>
                <stringProp name="Argument.value">{&quot;avgSendTime&quot;:4200.0,&quot;caution&quot;:&quot;&quot;,&quot;cityId&quot;:999999,&quot;ctime&quot;:1541994484,&quot;daySeq&quot;:&quot;18&quot;,&quot;deliveryTime&quot;:0,&quot;detail&quot;:&quot;[{\&quot;app_food_code\&quot;:\&quot;6465540744079668225\&quot;,\&quot;box_num\&quot;:1,\&quot;box_price\&quot;:0,\&quot;cart_id\&quot;:0,\&quot;food_discount\&quot;:1,\&quot;food_name\&quot;:\&quot;西瓜\&quot;,\&quot;food_property\&quot;:\&quot;\&quot;,\&quot;price\&quot;:0.01,\&quot;quantity\&quot;:1,\&quot;sku_id\&quot;:\&quot;6465540744092254209\&quot;,\&quot;spec\&quot;:\&quot;\&quot;,\&quot;unit\&quot;:\&quot;\&quot;}]&quot;,&quot;dinnersNumber&quot;:0,&quot;ePoiId&quot;:&quot;6477064113400228865&quot;,&quot;extras&quot;:&quot;[{}]&quot;,&quot;hasInvoiced&quot;:0,&quot;invoiceTitle&quot;:&quot;&quot;,&quot;isFavorites&quot;:false,&quot;isPoiFirstOrder&quot;:false,&quot;isThirdShipping&quot;:0,&quot;latitude&quot;:29.77389,&quot;logisticsCode&quot;:&quot;0000&quot;,&quot;longitude&quot;:95.36876,&quot;orderId&quot;:32856432201175871,&quot;orderIdView&quot;:32856432201175871,&quot;originalPrice&quot;:0.02,&quot;payType&quot;:1,&quot;poiAddress&quot;:&quot;南极洲04号站&quot;,&quot;poiFirstOrder&quot;:false,&quot;poiId&quot;:3285643,&quot;poiName&quot;:&quot;t_9FwNEdZS&quot;,&quot;poiPhone&quot;:&quot;4009208801&quot;,&quot;poiReceiveDetail&quot;:&quot;{\&quot;actOrderChargeByMt\&quot;:[{\&quot;comment\&quot;:\&quot;活动款\&quot;,\&quot;feeTypeDesc\&quot;:\&quot;活动款\&quot;,\&quot;feeTypeId\&quot;:10019,\&quot;moneyCent\&quot;:0}],\&quot;actOrderChargeByPoi\&quot;:[],\&quot;foodShareFeeChargeByPoi\&quot;:0,\&quot;logisticsFee\&quot;:1,\&quot;onlinePayment\&quot;:2,\&quot;wmPoiReceiveCent\&quot;:2}&quot;,&quot;recipientAddress&quot;:&quot;色金拉@#西藏自治区林芝市墨脱县色金拉&quot;,&quot;recipientName&quot;:&quot;滕(先生)&quot;,&quot;recipientPhone&quot;:&quot;17302864356&quot;,&quot;shipperPhone&quot;:&quot;&quot;,&quot;shippingFee&quot;:0.01,&quot;status&quot;:2,&quot;taxpayerId&quot;:&quot;&quot;,&quot;total&quot;:0.02,&quot;utime&quot;:1541994484}</stringProp>
                <stringProp name="Argument.metadata">=</stringProp>
                <boolProp name="HTTPArgument.use_equals">true</boolProp>
                <stringProp name="Argument.name">order</stringProp>
              </elementProp>
            </collectionProp>
          </elementProp>
          <stringProp name="HTTPSampler.domain">takeout.easy.echosite.cn</stringProp>
          <stringProp name="HTTPSampler.port"></stringProp>
          <stringProp name="HTTPSampler.connect_timeout"></stringProp>
          <stringProp name="HTTPSampler.response_timeout"></stringProp>
          <stringProp name="HTTPSampler.protocol"></stringProp>
          <stringProp name="HTTPSampler.contentEncoding"></stringProp>
          <stringProp name="HTTPSampler.path">/merchant/takeout/callback/mt/order/first</stringProp>
          <stringProp name="HTTPSampler.method">POST</stringProp>
          <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
          <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
          <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
          <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
          <stringProp name="HTTPSampler.implementation">HttpClient4</stringProp>
          <boolProp name="HTTPSampler.monitor">false</boolProp>
          <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
        </HTTPSamplerProxy>
        <hashTree>
          <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP信息头管理器" enabled="true">
            <collectionProp name="HeaderManager.headers">
              <elementProp name="" elementType="Header">
                <stringProp name="Header.name">Accept</stringProp>
                <stringProp name="Header.value">*/*</stringProp>
              </elementProp>
              <elementProp name="" elementType="Header">
                <stringProp name="Header.name">Cache-Control</stringProp>
                <stringProp name="Header.value">no-cache</stringProp>
              </elementProp>
            </collectionProp>
          </HeaderManager>
          <hashTree/>
          <ResultCollector guiclass="ViewResultsFullVisualizer" testclass="ResultCollector" testname="察看结果树" enabled="true">
            <boolProp name="ResultCollector.error_logging">false</boolProp>
            <objProp>
              <name>saveConfig</name>
              <value class="SampleSaveConfiguration">
                <time>true</time>
                <latency>true</latency>
                <timestamp>true</timestamp>
                <success>true</success>
                <label>true</label>
                <code>true</code>
                <message>true</message>
                <threadName>true</threadName>
                <dataType>true</dataType>
                <encoding>false</encoding>
                <assertions>true</assertions>
                <subresults>true</subresults>
                <responseData>false</responseData>
                <samplerData>false</samplerData>
                <xml>false</xml>
                <fieldNames>false</fieldNames>
                <responseHeaders>false</responseHeaders>
                <requestHeaders>false</requestHeaders>
                <responseDataOnError>false</responseDataOnError>
                <saveAssertionResultsFailureMessage>false</saveAssertionResultsFailureMessage>
                <assertionsResultsToSave>0</assertionsResultsToSave>
                <bytes>true</bytes>
              </value>
            </objProp>
            <stringProp name="filename"></stringProp>
          </ResultCollector>
          <hashTree/>
          <ResultCollector guiclass="StatVisualizer" testclass="ResultCollector" testname="聚合报告" enabled="true">
            <boolProp name="ResultCollector.error_logging">false</boolProp>
            <objProp>
              <name>saveConfig</name>
              <value class="SampleSaveConfiguration">
                <time>true</time>
                <latency>true</latency>
                <timestamp>true</timestamp>
                <success>true</success>
                <label>true</label>
                <code>true</code>
                <message>true</message>
                <threadName>true</threadName>
                <dataType>true</dataType>
                <encoding>false</encoding>
                <assertions>true</assertions>
                <subresults>true</subresults>
                <responseData>false</responseData>
                <samplerData>false</samplerData>
                <xml>false</xml>
                <fieldNames>false</fieldNames>
                <responseHeaders>false</responseHeaders>
                <requestHeaders>false</requestHeaders>
                <responseDataOnError>false</responseDataOnError>
                <saveAssertionResultsFailureMessage>false</saveAssertionResultsFailureMessage>
                <assertionsResultsToSave>0</assertionsResultsToSave>
                <bytes>true</bytes>
              </value>
            </objProp>
            <stringProp name="filename"></stringProp>
          </ResultCollector>
          <hashTree/>
        </hashTree>
        <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="开发环境饿了么测试" enabled="true">
          <boolProp name="HTTPSampler.postBodyRaw">true</boolProp>
          <elementProp name="HTTPsampler.Arguments" elementType="Arguments">
            <collectionProp name="Arguments.arguments">
              <elementProp name="" elementType="HTTPArgument">
                <boolProp name="HTTPArgument.always_encode">false</boolProp>
                <stringProp name="Argument.value">{&#xd;
  &quot;requestId&quot;: &quot;200023832433446625&quot;,&#xd;
  &quot;type&quot;: 10,&#xd;
  &quot;appId&quot;: 41193985,&#xd;
  &quot;message&quot;: &quot;{\&quot;id\&quot;:\&quot;3032956277608090808\&quot;,\&quot;orderId\&quot;:\&quot;3032956277608090808\&quot;,\&quot;address\&quot;:\&quot;东平国家森林公园(1号门)\&quot;,\&quot;createdAt\&quot;:\&quot;2018-11-30T15:25:19\&quot;,\&quot;activeAt\&quot;:\&quot;2018-11-30T15:25:19\&quot;,\&quot;deliverFee\&quot;:0.01,\&quot;merchantDeliverySubsidy\&quot;:0.0,\&quot;deliverTime\&quot;:null,\&quot;description\&quot;:\&quot;\&quot;,\&quot;groups\&quot;:[{\&quot;name\&quot;:\&quot;1号篮子\&quot;,\&quot;type\&quot;:\&quot;normal\&quot;,\&quot;items\&quot;:[{\&quot;id\&quot;:1538909403,\&quot;skuId\&quot;:200000347034886881,\&quot;name\&quot;:\&quot;特色烧鸡公[少冰+重辣]\&quot;,\&quot;categoryId\&quot;:1,\&quot;price\&quot;:0.01,\&quot;quantity\&quot;:1,\&quot;total\&quot;:0.01,\&quot;additions\&quot;:[],\&quot;newSpecs\&quot;:[{\&quot;name\&quot;:\&quot;规格\&quot;,\&quot;value\&quot;:\&quot;特色烧鸡公\&quot;}],\&quot;attributes\&quot;:[{\&quot;name\&quot;:\&quot;温度\&quot;,\&quot;value\&quot;:\&quot;少冰\&quot;},{\&quot;name\&quot;:\&quot;辣度\&quot;,\&quot;value\&quot;:\&quot;重辣\&quot;}],\&quot;extendCode\&quot;:\&quot;6473127185224395778\&quot;,\&quot;barCode\&quot;:\&quot;\&quot;,\&quot;weight\&quot;:1.0,\&quot;userPrice\&quot;:0.0,\&quot;shopPrice\&quot;:0.0,\&quot;vfoodId\&quot;:1489168570,\&quot;ingredients\&quot;:[]},{\&quot;id\&quot;:1538898148,\&quot;skuId\&quot;:200000347030263521,\&quot;name\&quot;:\&quot;红烧狮子头\&quot;,\&quot;categoryId\&quot;:1,\&quot;price\&quot;:0.02,\&quot;quantity\&quot;:1,\&quot;total\&quot;:0.02,\&quot;additions\&quot;:[],\&quot;newSpecs\&quot;:[],\&quot;attributes\&quot;:[],\&quot;extendCode\&quot;:\&quot;6468356238810100737\&quot;,\&quot;barCode\&quot;:\&quot;\&quot;,\&quot;weight\&quot;:1.0,\&quot;userPrice\&quot;:0.0,\&quot;shopPrice\&quot;:0.0,\&quot;vfoodId\&quot;:1489172425,\&quot;ingredients\&quot;:[]}],\&quot;relatedItems\&quot;:[]}],\&quot;invoice\&quot;:null,\&quot;book\&quot;:false,\&quot;onlinePaid\&quot;:true,\&quot;railwayAddress\&quot;:null,\&quot;phoneList\&quot;:[\&quot;17091956873,57\&quot;],\&quot;shopId\&quot;:*********,\&quot;shopName\&quot;:\&quot;saas测试i环测试店铺\&quot;,\&quot;daySn\&quot;:6,\&quot;status\&quot;:\&quot;unprocessed\&quot;,\&quot;refundStatus\&quot;:\&quot;noRefund\&quot;,\&quot;userId\&quot;:187118524,\&quot;userIdStr\&quot;:\&quot;187118524\&quot;,\&quot;totalPrice\&quot;:0.04,\&quot;originalPrice\&quot;:0.04,\&quot;consignee\&quot;:\&quot;滕**\&quot;,\&quot;deliveryGeo\&quot;:\&quot;121.48156917,31.67690196\&quot;,\&quot;deliveryPoiAddress\&quot;:\&quot;东平国家森林公园(1号门)\&quot;,\&quot;invoiced\&quot;:false,\&quot;income\&quot;:0.04,\&quot;serviceRate\&quot;:0.08,\&quot;serviceFee\&quot;:-0.0,\&quot;hongbao\&quot;:0.0,\&quot;packageFee\&quot;:0.0,\&quot;activityTotal\&quot;:-0.0,\&quot;shopPart\&quot;:-0.0,\&quot;elemePart\&quot;:-0.0,\&quot;downgraded\&quot;:false,\&quot;vipDeliveryFeeDiscount\&quot;:0.0,\&quot;openId\&quot;:\&quot;6473126470256594946\&quot;,\&quot;secretPhoneExpireTime\&quot;:\&quot;2018-11-30T21:25:15\&quot;,\&quot;orderActivities\&quot;:[],\&quot;invoiceType\&quot;:null,\&quot;taxpayerId\&quot;:\&quot;\&quot;,\&quot;coldBoxFee\&quot;:0.0,\&quot;cancelOrderDescription\&quot;:null,\&quot;cancelOrderCreatedAt\&quot;:null,\&quot;orderCommissions\&quot;:[],\&quot;baiduWaimai\&quot;:false,\&quot;userExtraInfo\&quot;:{\&quot;giverPhone\&quot;:\&quot;\&quot;,\&quot;greeting\&quot;:\&quot;\&quot;},\&quot;consigneePhones\&quot;:[\&quot;173****4356\&quot;],\&quot;superVip\&quot;:\&quot;ELEME_SUPER_VIP\&quot;,\&quot;confirmCookingTime\&quot;:null,\&quot;orderActivityParts\&quot;:[{\&quot;partName\&quot;:\&quot;优惠总计\&quot;,\&quot;partValue\&quot;:-0.0,\&quot;weight\&quot;:10}],\&quot;orderBusinessType\&quot;:0,\&quot;pickUpTime\&quot;:\&quot;1970-01-01T08:00:00\&quot;,\&quot;pickUpNumber\&quot;:0,\&quot;umpOrder\&quot;:0,\&quot;tianmaoPart\&quot;:-0.0,\&quot;shopBrandId\&quot;:0,\&quot;userPart\&quot;:0.0,\&quot;specUserPart\&quot;:0.0,\&quot;isBusinessOrder\&quot;:false,\&quot;pinTuanOrder\&quot;:false}&quot;,&#xd;
  &quot;shopId&quot;: *********,&#xd;
  &quot;timestamp&quot;: 1543562719138,&#xd;
  &quot;signature&quot;: &quot;9816D040812B713CBFA4927A814601FF&quot;,&#xd;
  &quot;userId&quot;: 176926821992027911&#xd;
}</stringProp>
                <stringProp name="Argument.metadata">=</stringProp>
              </elementProp>
            </collectionProp>
          </elementProp>
          <stringProp name="HTTPSampler.domain">takeout.easy.echosite.cn</stringProp>
          <stringProp name="HTTPSampler.port"></stringProp>
          <stringProp name="HTTPSampler.connect_timeout"></stringProp>
          <stringProp name="HTTPSampler.response_timeout"></stringProp>
          <stringProp name="HTTPSampler.protocol"></stringProp>
          <stringProp name="HTTPSampler.contentEncoding"></stringProp>
          <stringProp name="HTTPSampler.path">/merchant/takeout/callback/ele/order</stringProp>
          <stringProp name="HTTPSampler.method">POST</stringProp>
          <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
          <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
          <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
          <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
          <stringProp name="HTTPSampler.implementation">HttpClient4</stringProp>
          <boolProp name="HTTPSampler.monitor">false</boolProp>
          <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
        </HTTPSamplerProxy>
        <hashTree>
          <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP信息头管理器" enabled="true">
            <collectionProp name="HeaderManager.headers">
              <elementProp name="" elementType="Header">
                <stringProp name="Header.name">Accept</stringProp>
                <stringProp name="Header.value">*/*</stringProp>
              </elementProp>
              <elementProp name="" elementType="Header">
                <stringProp name="Header.name">Cache-Control</stringProp>
                <stringProp name="Header.value">no-cache</stringProp>
              </elementProp>
              <elementProp name="" elementType="Header">
                <stringProp name="Header.name">content-type</stringProp>
                <stringProp name="Header.value">application/json</stringProp>
              </elementProp>
            </collectionProp>
          </HeaderManager>
          <hashTree/>
          <ResultCollector guiclass="ViewResultsFullVisualizer" testclass="ResultCollector" testname="察看结果树" enabled="true">
            <boolProp name="ResultCollector.error_logging">false</boolProp>
            <objProp>
              <name>saveConfig</name>
              <value class="SampleSaveConfiguration">
                <time>true</time>
                <latency>true</latency>
                <timestamp>true</timestamp>
                <success>true</success>
                <label>true</label>
                <code>true</code>
                <message>true</message>
                <threadName>true</threadName>
                <dataType>true</dataType>
                <encoding>false</encoding>
                <assertions>true</assertions>
                <subresults>true</subresults>
                <responseData>false</responseData>
                <samplerData>false</samplerData>
                <xml>false</xml>
                <fieldNames>false</fieldNames>
                <responseHeaders>false</responseHeaders>
                <requestHeaders>false</requestHeaders>
                <responseDataOnError>false</responseDataOnError>
                <saveAssertionResultsFailureMessage>false</saveAssertionResultsFailureMessage>
                <assertionsResultsToSave>0</assertionsResultsToSave>
                <bytes>true</bytes>
              </value>
            </objProp>
            <stringProp name="filename"></stringProp>
          </ResultCollector>
          <hashTree/>
          <ResultCollector guiclass="StatVisualizer" testclass="ResultCollector" testname="聚合报告" enabled="true">
            <boolProp name="ResultCollector.error_logging">false</boolProp>
            <objProp>
              <name>saveConfig</name>
              <value class="SampleSaveConfiguration">
                <time>true</time>
                <latency>true</latency>
                <timestamp>true</timestamp>
                <success>true</success>
                <label>true</label>
                <code>true</code>
                <message>true</message>
                <threadName>true</threadName>
                <dataType>true</dataType>
                <encoding>false</encoding>
                <assertions>true</assertions>
                <subresults>true</subresults>
                <responseData>false</responseData>
                <samplerData>false</samplerData>
                <xml>false</xml>
                <fieldNames>false</fieldNames>
                <responseHeaders>false</responseHeaders>
                <requestHeaders>false</requestHeaders>
                <responseDataOnError>false</responseDataOnError>
                <saveAssertionResultsFailureMessage>false</saveAssertionResultsFailureMessage>
                <assertionsResultsToSave>0</assertionsResultsToSave>
                <bytes>true</bytes>
              </value>
            </objProp>
            <stringProp name="filename"></stringProp>
          </ResultCollector>
          <hashTree/>
        </hashTree>
      </hashTree>
    </hashTree>
  </hashTree>
</jmeterTestPlan>
