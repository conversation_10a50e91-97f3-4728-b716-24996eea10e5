export default [{
    menuGuid: 'home',
    menuName: '首页',
    pageUrl: '/home',
    parentId: '终端ID',
    menus: []
}, {
    menuGuid: 'storesAndEmployees',
    menuName: '门店及员工',
    pageUrl: '/storesAndEmployees',
    parentId: '终端ID',
    isEnabled: true,
    menus: [{
        menuGuid: '员工管理',
        menuName: '员工管理',
        pageUrl: null,
        isEnabled: true,
        menus: [{
            menuGuid: '员工列表',
            menuName: '员工列表',
            pageUrl: '/storesAndEmployees/staffList',
            isEnabled: true,
            menus: []
        }, {
            menuGuid: '角色列表',
            menuName: '角色列表',
            pageUrl: '/storesAndEmployees/roleList',
            isEnabled: true,
            menus: []
        }, {
            menuGuid: '角色授权',
            menuName: '角色授权',
            pageUrl: '/storesAndEmployees/roleAuthorization',
            isEnabled: false,
            menus: []
        }, {
            menuGuid: '新建账号',
            menuName: '新建账号',
            pageUrl: '/storesAndEmployees/createStaff',
            isEnabled: false,
            menus: []
        }, {
            menuGuid: '编辑账号',
            menuName: '编辑账号',
            pageUrl: '/storesAndEmployees/editStaff',
            isEnabled: false,
            menus: []
        }]
    }, {
        menuGuid: '门店管理',
        menuName: '门店管理',
        pageUrl: null,
        isEnabled: true,
        menus: [{
            menuGuid: 'storeList',
            menuName: '门店列表',
            parentId: '终端ID,storesAndEmployees',
            pageUrl: '/storesAndEmployees/storeList',
            isEnabled: true,
        },
            {
                menuGuid: 'newStore',
                menuName: '新建门店',
                parentId: '终端ID,storesAndEmployees',
                pageUrl: '/storesAndEmployees/newStore',
                isEnabled: false,
            }, {
                menuGuid: 'reservations',
                menuName: '桌位管理',
                parentId: '终端ID,storesAndEmployees',
                pageUrl: '/storesAndEmployees/reservations',
                isEnabled: true,
            }]
    }, {
        menuGuid: 'organization',
        menuName: '组织机构',
        parentId: '终端ID,storesAndEmployees',
        pageUrl: '/storesAndEmployees/organization',
        isEnabled: true,
    },
        {
            menuGuid: 'enterpriseBrand',
            menuName: '企业品牌',
            pageUrl: '/storesAndEmployees/enterpriseBrand',
            parentId: '终端ID,storesAndEmployees',
            isEnabled: true,
            menus: [],
        }, {
            menuGuid: 'weChatPublic',
            menuName: '微信公众号',
            pageUrl: null,
            parentId: '终端ID,storesAndEmployees',
            isEnabled: true,
            menus: [{
                menuGuid: 'publicAuth',
                menuName: '公众号授权',
                pageUrl: '/storesAndEmployees/publicAuth',
                isEnabled: true,
            }]
        },
        {
            menuGuid: 'weChatStores',
            menuName: '微信门店',
            pageUrl: null,
            parentId: '终端ID,storesAndEmployees',
            isEnabled: true,
            menus: [{
                menuGuid: 'serviceConfiguration',
                menuName: '业务配置',
                pageUrl: '/storesAndEmployees/serviceConfig',
                isEnabled: true,
            }, {
                menuGuid: 'wxConfigStore',
                menuName: '点餐批量配置',
                pageUrl: '/storesAndEmployees/batchWxConfigStore',
                isEnabled: false,
                menus: []
            }, {
                menuGuid: 'wxConfigStore',
                menuName: '编辑点餐配置',
                pageUrl: '/storesAndEmployees/wxConfigStore',
                isEnabled: false,
                menus: []
            }]
        }]
},
    {
        menuGuid: 'sjhdjskahdjashdjkahda',
        menuName: '顾客',
        pageUrl: '顾客',
        parentId: '终端ID',
        menus: []
    }, {
        menuGuid: 'commodityListAnd',
        menuName: '商品销售',
        pageUrl: '/merchandiseSales',
        parentId: '终端ID',
        menus: [
            {
                menuGuid: '品牌库',
                menuName: '品牌库',
                pageUrl: null,
                isEnabled: true,
                menus: [
                    {
                        menuGuid: 'commodityBank',
                        menuName: '商品库',
                        parentId: '终端ID,brandLibrary',
                        pageUrl: '/brandLibrary/commodityBank',
                        isEnabled: true,
                    },
                    {
                        menuGuid: 'attributeLibrary',
                        menuName: '属性库',
                        parentId: '终端ID,brandLibrary',
                        pageUrl: '/brandLibrary/attributeLibrary',
                        isEnabled: true,
                    },
                    {
                        menuGuid: 'newSingleProdouct',
                        menuName: '新建单品',
                        parentId: '终端ID,storeGoods',
                        pageUrl: '/brandLibrary/newSingleProdouct',
                        isEnabled: false,
                    },
                    {
                        menuGuid: 'editSingleProdouct',
                        menuName: '编辑单品',
                        parentId: '终端ID,brandLibrary',
                        menuUrl: '/brandLibrary/editSingleProdouct',
                        isEnabled: false,
                    },
                    {
                        menuGuid: 'newPackage',
                        menuName: '新建套餐',
                        parentId: '终端ID,storeGoods',
                        pageUrl: '/brandLibrary/newPackage',
                        isEnabled: false,
                    },
                    {
                        menuGuid: 'editPackage',
                        menuName: '编辑套餐',
                        parentId: '终端ID,brandLibrary',
                        menuUrl: '/brandLibrary/editPackage',
                        isEnabled: false,
                    }
                ]
            },
            {
                menuGuid: '门店商品',
                menuName: '门店商品',
                pageUrl: null,
                isEnabled: true,
                menus: [
                    {
                        menuGuid: 'commodityList',
                        menuName: '商品列表',
                        parentId: '终端ID,storeGoods',
                        pageUrl: '/storeGoods/commodityList',
                        isEnabled: true,
                    },
                    {
                        menuGuid: 'attributeList',
                        menuName: '属性列表',
                        parentId: '终端ID,storeGoods',
                        pageUrl: '/storeGoods/attributeList',
                        isEnabled: true,
                    },
                    {
                        menuGuid: 'newSingleProdouct',
                        menuName: '新建单品',
                        parentId: '终端ID,storeGoods',
                        pageUrl: '/storeGoods/newSingleProdouct',
                        isEnabled: false,
                    },
                    {
                        menuGuid: 'editSingleProdouct',
                        menuName: '编辑单品',
                        parentId: '终端ID,storeGoods',
                        menuUrl: '/storeGoods/editSingleProdouct',
                        isEnabled: false,
                    },
                    {
                        menuGuid: 'newPackage',
                        menuName: '新建套餐',
                        parentId: '终端ID,storeGoods',
                        pageUrl: '/storeGoods/newPackage',
                        isEnabled: false,
                    },
                    {
                        menuGuid: 'editPackage',
                        menuName: '编辑套餐',
                        parentId: '终端ID,storeGoods',
                        menuUrl: '/storeGoods/editPackage',
                        isEnabled: false,
                    }
                ]
            }
        ]
    }, {
        menuGuid: 'sjhdjskahdjashdjkahda',
        menuName: '营销活动',
        pageUrl: '营销活动',
        parentId: '',
        menus: []
    }, {
        menuGuid: 'sjhdjskahdjashdjkahda',
        menuName: '库存',
        pageUrl: '库存',
        parentId: '终端ID',
        menus: []
    }, {
        menuGuid: 'reportCenter',
        menuName: '报表中心',
        pageUrl: '/reportCenter',
        parentId: '终端ID',
        isEnabled: true,
        menus: [
            {
                menuGuid: 'foodReportBack',
                menuName: '退菜报表',
                parentId: '终端ID,reportCenter',
                pageUrl: '/reportCenter/foodReportBack',
                isEnabled: true,
            },
            {
                menuGuid: 'giveReport',
                menuName: '赠送报表',
                parentId: '终端ID,giveReport',
                pageUrl: '/reportCenter/giveReport',
                isEnabled: true,
            },
            {
                menuGuid: 'checkoutOrder',
                menuName: '订单反结账记录',
                parentId: '终端ID,checkoutOrder',
                pageUrl: '/reportCenter/checkoutOrder',
                isEnabled: true,
            },
            {
                menuGuid: 'paymentMethod',
                menuName: '支付方式',
                parentId: '终端ID,paymentMethod',
                pageUrl: '/reportCenter/paymentMethod',
                isEnabled: true,
            },
            {
                menuGuid: 'payMethodDetail',
                menuName: '支付方式明细',
                parentId: '终端ID,payMethodDetail',
                pageUrl: '/reportCenter/payMethodDetail',
                isEnabled: true,
            },
            {
                menuGuid: 'preferential',
                menuName: '优惠方式',
                parentId: '终端ID,preferential',
                pageUrl: '/reportCenter/preferential',
                isEnabled: true,
            },
            {
                menuGuid: 'preferentialDetail',
                menuName: '优惠方式详情',
                parentId: '终端ID,preferentialDetail',
                pageUrl: '/reportCenter/preferentialDetail',
                isEnabled: true,
            },
            {
                menuGuid: 'salesReport',
                menuName: '销售报表',
                parentId: '终端ID,salesReport',
                pageUrl: '/reportCenter/salesReport',
                isEnabled: true,
            },
            {
                menuGuid: 'scrapOrder',
                menuName: '订单作废记录',
                parentId: '终端ID,scrapOrder',
                pageUrl: '/reportCenter/scrapOrder',
                isEnabled: true,
            },
            {
                menuGuid: 'shiftingDuty',
                menuName: '交接班',
                parentId: '终端ID,shiftingDuty',
                pageUrl: '/reportCenter/shiftingDuty',
                isEnabled: true,
            }, {
                menuGuid: 'shiftingDutyDetail',
                menuName: '交接班详情',
                parentId: '终端ID,shiftingDutyDetail',
                pageUrl: '/reportCenter/shiftingDutyDetail',
                isEnabled: false,
            }, {
                menuGuid: 'transactionRecord',
                menuName: '订单交易记录',
                parentId: '终端ID,transactionRecord',
                pageUrl: '/reorderRecordDetailportCenter/transactionRecord',
                isEnabled: true,
            }, {
                menuGuid: 'orderRecordDetail',
                menuName: '订单记录详情',
                parentId: '终端ID,orderRecordDetail',
                pageUrl: '/reportCenter/orderRecordDetail',
                isEnabled: false,
            }, {
                menuGuid: 'takeawayOrder',
                menuName: '外卖交易',
                parentId: '终端ID,takeawayOrder',
                pageUrl: '/reportCenter/takeawayOrder',
                isEnabled: true,
            }, {
                menuGuid: 'businessSituation',
                menuName: '营业概况',
                parentId: '终端ID,businessSituation',
                pageUrl: '/reportCenter/businessSituation',
                isEnabled: true,
            }
        ]
    }, {
        menuGuid: 'menberMarket',
        menuName: '会员营销',
        pageUrl: '/menberMarket',
        parentId: '终端ID',
        isEnabled: true,
        menus: [
            {
                menuGuid: 'ruleSet',
                menuName: '规则设置',
                parentId: '终端ID,menberMarket',
                pageUrl: '/menberMarket/ruleSet',
                pageTitle: '规则设置',
                isEnabled: true,
                menus: [
                    {
                        menuGuid: 'ruleDetail',
                        menuName: '规则详情',
                        parentId: '终端ID,menberMarket',
                        pageUrl: '/menberMarket/ruleSet/ruleDetail',
                        pageTitle: '规则详情',
                        isEnabled: true,
                    }
                ]
            },
            {
                menuGuid: 'membershipData',
                menuName: '会员数据',
                parentId: '终端ID,membershipData',
                pageUrl: '/menberMarket/membershipData',
                isEnabled: true,
                menus: [
                    {
                        menuGuid: 'membershiHome',
                        menuName: '会员主界面',
                        parentId: '终端ID,membershipData',
                        pageUrl: '/menberMarket/membershipData/membershiHome',
                        pageTitle: '会员主界面',
                        isEnabled: true,
                    },
                    {
                        menuGuid: 'membershiDetail',
                        menuName: '会员详情',
                        parentId: '终端ID,membershipData',
                        pageUrl: '/menberMarket/membershipData/membershiDetail',
                        pageTitle: '会员详情',
                        isEnabled: true,
                    }
                ]
            }
        ]
    }, {
        menuGuid: 'log',
        menuName: '日志管理',
        pageUrl: '/log',
        parentId: '终端ID',
        isEnabled: true,
        menus: [
            {
                menuGuid: 'bgOperationLog',
                menuName: '后台系统日志',
                parentId: '终端ID,log',
                pageUrl: '/log',
                pageTitle: '后台系统日志',
                isEnabled: true,
                menus: [
                    {
                        menuGuid: 'bgOperationLog',
                        menuName: '操作日志',
                        parentId: '终端ID,log',
                        pageUrl: '/log/bgOperationLog',
                        pageTitle: '操作日志',
                        isEnabled: false,
                    }
                ]
            }, {
                menuGuid: 'orderLog',
                menuName: '收银系统日志',
                parentId: '终端ID,log',
                pageUrl: '',
                pageTitle: '收银系统日志',
                isEnabled: true,
                menus: [
                    {
                        menuGuid: 'orderLog',
                        menuName: '操作日志',
                        parentId: '终端ID,log',
                        pageUrl: '/log/orderOperationLog',
                        pageTitle: '操作日志',
                        isEnabled: true,
                    },
                    {
                        menuGuid: 'orderLog',
                        menuName: '订单日志',
                        parentId: '终端ID,log',
                        pageUrl: '/log/orderLog',
                        pageTitle: '订单日志',
                        isEnabled: true,
                    }
                ]
            }

        ]
    }, {
        menuGuid: 'sjhdjskahdjashdjkahda',
        menuName: '基础配置',
        pageUrl: '基础配置',
        parentId: '终端ID',
        menus: []
    }, {
        menuGuid: 'sjhdjskahdjashdjkahda',
        menuName: '生产管理',
        pageUrl: '生产管理',
        parentId: '终端ID',
        menus: []
    }]