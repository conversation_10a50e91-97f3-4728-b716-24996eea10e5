
## 饿了么

新订单原始参数

```
{"requestId":"200047615198985467","type":10,"appId":28778374,"message":"{\"id\":\"3058136247998670910\",\"orderId\":\"3058136247998670910\",\"address\":\"天盛·壹中心-2单元四川省成都市青羊区北大街88号1 楼2单元1106\",\"createdAt\":\"2020-01-02T14:12:53\",\"activeAt\":\"2020-01-02T14:12:53\",\"deliverFee\":1.5,\"merchantDeliverySubsidy\":-2.5,\"deliverTime\":null,\"description\":\"\",\"groups\":[{\"name\":\"1号篮子\",\"type\":\"normal\",\"items\":[{\"id\":************,\"skuId\":200000471279755515,\"name\":\"铁板烤藕片【有折耳根，可备注不加！】\",\"categoryId\":1,\"price\":12.0,\"quantity\":1,\"total\":12.0,\"additions\":[],\"newSpecs\":[],\"attributes\":[],\"extendCode\":\"945c5a46e7924d8292f42c62a4df09b6\",\"barCode\":\"\",\"weight\":1.0,\"userPrice\":0.0,\"shopPrice\":0.0,\"vfoodId\":1580106656,\"ingredients\":[]},{\"id\":1533223716,\"skuId\":200000341228617979,\"name\":\"烤藤椒牛肉（5串）\",\"categoryId\":1,\"price\":13.8,\"quantity\":1,\"total\":13.8,\"additions\":[],\"newSpecs\":[],\"attributes\":[],\"extendCode\":\"6617792755408044032\",\"barCode\":\"\",\"weight\":1.0,\"userPrice\":0.0,\"shopPrice\":0.0,\"vfoodId\":1486136414,\"ingredients\":[]},{\"id\":************,\"skuId\":200000471256779003,\"name\":\"烤鸡尖（4串/每串两个）\",\"categoryId\":1,\"price\":11.8,\"quantity\":1,\"total\":11.8,\"additions\":[],\"newSpecs\":[],\"attributes\":[],\"extendCode\":\"6617793368917278720\",\"barCode\":\"\",\"weight\":1.0,\"userPrice\":0.0,\"shopPrice\":0.0,\"vfoodId\":1580098898,\"ingredients\":[]}],\"relatedItems\":[]},{\"name\":\"其它费用\",\"type\":\"extra\",\"items\":[{\"id\":-70000,\"skuId\":-1,\"name\":\"餐盒\",\"categoryId\":102,\"price\":1.7,\"quantity\":1,\"total\":1.7,\"additions\":[],\"newSpecs\":null,\"attributes\":null,\"extendCode\":\"\",\"barCode\":\"\",\"weight\":null,\"userPrice\":0.0,\"shopPrice\":0.0,\"vfoodId\":0,\"ingredients\":[]}],\"relatedItems\":[]}],\"invoice\":null,\"book\":false,\"onlinePaid\":true,\"railwayAddress\":null,\"phoneList\":[\"17088174872,985\"],\"shopId\":658620,\"shopName\":\"何师烧烤(王府井后街梓潼桥概念店)\",\"daySn\":17,\"status\":\"unprocessed\",\"refundStatus\":\"noRefund\",\"userId\":151829505,\"userIdStr\":\"151829505\",\"totalPrice\":30.8,\"originalPrice\":40.8,\"consignee\":\"蒋**\",\"deliveryGeo\":\"104.0771809,30.67444495\",\"deliveryPoiAddress\":\"天盛·壹中心-2单元四川省成都市青羊区北大街88号1 楼2单元1106\",\"invoiced\":false,\"income\":22.8,\"serviceRate\":0.13,\"serviceFee\":-4.0,\"allowanceServiceFee\":0.0,\"baseLogisticsServiceFee\":0.0,\"timeIntervalMarkUpFee\":0.0,\"hongbao\":0.0,\"packageFee\":1.7,\"activityTotal\":-10.0,\"shopPart\":-10.0,\"elemePart\":-0.0,\"downgraded\":false,\"vipDeliveryFeeDiscount\":0.0,\"openId\":\"cb6e8158-0104-452a-ac58-2b5139ef3f5e\",\"secretPhoneExpireTime\":\"2020-01-02T20:12:43\",\"orderActivities\":[{\"categoryId\":12,\"name\":\"店铺满减优惠\",\"amount\":-10.0,\"elemePart\":0.0,\"restaurantPart\":-10.0,\"familyPart\":0.0,\"userPart\":0.0,\"id\":22495616123,\"metaId\":1269608723,\"orderAllPartiesPartList\":[{\"partName\":\"商家补贴\",\"partAmount\":\"10.0\"}]}],\"invoiceType\":null,\"taxpayerId\":\"\",\"coldBoxFee\":0.0,\"cancelOrderDescription\":null,\"cancelOrderCreatedAt\":null,\"orderCommissions\":[],\"baiduWaimai\":false,\"userExtraInfo\":{\"giverPhone\":\"\",\"greeting\":\"\"},\"consigneePhones\":[\"134****1699\"],\"superVip\":\"NOT_VIP\",\"confirmCookingTime\":null,\"orderActivityParts\":[{\"partName\":\"商户承担\",\"partValue\":-10.0,\"weight\":2},{\"partName\":\"优惠总计\",\"partValue\":-10.0,\"weight\":10}],\"orderBusinessType\":0,\"pickUpTime\":\"1970-01-01T08:00:00\",\"pickUpNumber\":0,\"umpOrder\":0,\"tianmaoPart\":-0.0,\"shopBrandId\":0,\"userPart\":0.0,\"specUserPart\":0.0,\"isBusinessOrder\":false,\"pinTuanOrder\":false,\"extraJson\":\"{\\\"originalDeliverFee\\\":\\\"4.0\\\",\\\"deliveryActivityFee\\\":\\\"0.0\\\"}\",\"svcPart\":-0.0,\"orderSourceTag\":\"ELEME\"}","shopId":658620,"timestamp":1577945573953,"signature":"303D0250FA503595535DC02D598BBAEA","userId":123602175228883104}
```

新订单中的订单字段的格式化参数

```
{
	"id": "3058136247998670910",
	"orderId": "3058136247998670910",
	"address": "天盛·壹中心-2单元四川省成都市青羊区北大街88号1 楼2单元1106",
	"createdAt": "2020-01-02T14:12:53",
	"activeAt": "2020-01-02T14:12:53",
	"deliverFee": 1.5,
	"merchantDeliverySubsidy": -2.5,
	"deliverTime": null,
	"description": "",
	"groups": [{
		"name": "1号篮子",
		"type": "normal",
		"items": [{
			"id": ************,
			"skuId": 200000471279755515,
			"name": "铁板烤藕片【有折耳根，可备注不加！】",
			"categoryId": 1,
			"price": 12.0,
			"quantity": 1,
			"total": 12.0,
			"additions": [],
			"newSpecs": [],
			"attributes": [],
			"extendCode": "945c5a46e7924d8292f42c62a4df09b6",
			"barCode": "",
			"weight": 1.0,
			"userPrice": 0.0,
			"shopPrice": 0.0,
			"vfoodId": 1580106656,
			"ingredients": []
		}, {
			"id": 1533223716,
			"skuId": 200000341228617979,
			"name": "烤藤椒牛肉（5串）",
			"categoryId": 1,
			"price": 13.8,
			"quantity": 1,
			"total": 13.8,
			"additions": [],
			"newSpecs": [],
			"attributes": [],
			"extendCode": "6617792755408044032",
			"barCode": "",
			"weight": 1.0,
			"userPrice": 0.0,
			"shopPrice": 0.0,
			"vfoodId": 1486136414,
			"ingredients": []
		}, {
			"id": ************,
			"skuId": 200000471256779003,
			"name": "烤鸡尖（4串/每串两个）",
			"categoryId": 1,
			"price": 11.8,
			"quantity": 1,
			"total": 11.8,
			"additions": [],
			"newSpecs": [],
			"attributes": [],
			"extendCode": "6617793368917278720",
			"barCode": "",
			"weight": 1.0,
			"userPrice": 0.0,
			"shopPrice": 0.0,
			"vfoodId": 1580098898,
			"ingredients": []
		}],
		"relatedItems": []
	}, {
		"name": "其它费用",
		"type": "extra",
		"items": [{
			"id": -70000,
			"skuId": -1,
			"name": "餐盒",
			"categoryId": 102,
			"price": 1.7,
			"quantity": 1,
			"total": 1.7,
			"additions": [],
			"newSpecs": null,
			"attributes": null,
			"extendCode": "",
			"barCode": "",
			"weight": null,
			"userPrice": 0.0,
			"shopPrice": 0.0,
			"vfoodId": 0,
			"ingredients": []
		}],
		"relatedItems": []
	}],
	"invoice": null,
	"book": false,
	"onlinePaid": true,
	"railwayAddress": null,
	"phoneList": ["17088174872,985"],
	"shopId": 658620,
	"shopName": "何师烧烤(王府井后街梓潼桥概念店)",
	"daySn": 17,
	"status": "unprocessed",
	"refundStatus": "noRefund",
	"userId": 151829505,
	"userIdStr": "151829505",
	"totalPrice": 30.8,
	"originalPrice": 40.8,
	"consignee": "蒋**",
	"deliveryGeo": "104.0771809,30.67444495",
	"deliveryPoiAddress": "天盛·壹中心-2单元四川省成都市青羊区北大街88号1 楼2单元1106",
	"invoiced": false,
	"income": 22.8,
	"serviceRate": 0.13,
	"serviceFee": -4.0,
	"allowanceServiceFee": 0.0,
	"baseLogisticsServiceFee": 0.0,
	"timeIntervalMarkUpFee": 0.0,
	"hongbao": 0.0,
	"packageFee": 1.7,
	"activityTotal": -10.0,
	"shopPart": -10.0,
	"elemePart": -0.0,
	"downgraded": false,
	"vipDeliveryFeeDiscount": 0.0,
	"openId": "cb6e8158-0104-452a-ac58-2b5139ef3f5e",
	"secretPhoneExpireTime": "2020-01-02T20:12:43",
	"orderActivities": [{
		"categoryId": 12,
		"name": "店铺满减优惠",
		"amount": -10.0,
		"elemePart": 0.0,
		"restaurantPart": -10.0,
		"familyPart": 0.0,
		"userPart": 0.0,
		"id": 22495616123,
		"metaId": 1269608723,
		"orderAllPartiesPartList": [{
			"partName": "商家补贴",
			"partAmount": "10.0"
		}]
	}],
	"invoiceType": null,
	"taxpayerId": "",
	"coldBoxFee": 0.0,
	"cancelOrderDescription": null,
	"cancelOrderCreatedAt": null,
	"orderCommissions": [],
	"baiduWaimai": false,
	"userExtraInfo": {
		"giverPhone": "",
		"greeting": ""
	},
	"consigneePhones": ["134****1699"],
	"superVip": "NOT_VIP",
	"confirmCookingTime": null,
	"orderActivityParts": [{
		"partName": "商户承担",
		"partValue": -10.0,
		"weight": 2
	}, {
		"partName": "优惠总计",
		"partValue": -10.0,
		"weight": 10
	}],
	"orderBusinessType": 0,
	"pickUpTime": "1970-01-01T08:00:00",
	"pickUpNumber": 0,
	"umpOrder": 0,
	"tianmaoPart": -0.0,
	"shopBrandId": 0,
	"userPart": 0.0,
	"specUserPart": 0.0,
	"isBusinessOrder": false,
	"pinTuanOrder": false,
	"extraJson": "{\"originalDeliverFee\":\"4.0\",\"deliveryActivityFee\":\"0.0\"}",
	"svcPart": -0.0,
	"orderSourceTag": "ELEME"
}
```

接单原始参数

```
{"requestId":"200047615363481920","type":12,"appId":28778374,"message":"{\"orderId\":\"3058136737130051664\",\"state\":\"valid\",\"shopId\":732336,\"updateTime\":1577945771,\"role\":3}","shopId":732336,"timestamp":1577945771190,"signature":"35FB9425EC7EF48A8481A89C001CA6D1","userId":123602175212393843}
```

接单中的订单字段的格式化参数

```
{
	"orderId": "3058136737130051664",
	"state": "valid",
	"shopId": 732336,
	"updateTime": 1577945771,
	"role": 3
}
```

## 美团

新订单原始参数

```
{"developerId":105325,"sign":"2c82c7ea0821140b48065b25d9be04cf83bbf994","ePoiId":"f92e6389-be05-484e-af5d-b06eb85906c6","order":"{\"avgSendTime\":2740.0,\"caution\":\"收餐人隐私号 15682116412_2066，手机号 138****7499 \",\"cityId\":510100,\"ctime\":1577944851,\"daySeq\":\"10\",\"deliveryTime\":0,\"detail\":\"[{\\\"app_food_code\\\":\\\"1e4ccaf621d541f084e0a6b06672b221\\\",\\\"box_num\\\":1.0,\\\"box_price\\\":0.1,\\\"cart_id\\\":0,\\\"food_discount\\\":1.0,\\\"food_name\\\":\\\"【有料】烤烟熏豆腐干\\\",\\\"food_property\\\":\\\"\\\",\\\"price\\\":1.8,\\\"quantity\\\":2,\\\"sku_id\\\":\\\"1e4ccaf621d541f084e0a6b06672b221\\\",\\\"spec\\\":\\\"\\\",\\\"unit\\\":\\\"1串\\\"},{\\\"app_food_code\\\":\\\"6617793368720146433\\\",\\\"box_num\\\":2.0,\\\"box_price\\\":1.0,\\\"cart_id\\\":0,\\\"food_discount\\\":1.0,\\\"food_name\\\":\\\"招牌鸭脑壳（2个）\\\",\\\"food_property\\\":\\\"\\\",\\\"price\\\":16.0,\\\"quantity\\\":2,\\\"sku_id\\\":\\\"6617793368720146433\\\",\\\"spec\\\":\\\"\\\",\\\"unit\\\":\\\"2个\\\"}]\",\"dinnersNumber\":0,\"ePoiId\":\"f92e6389-be05-484e-af5d-b06eb85906c6\",\"extras\":\"[{\\\"act_detail_id\\\":1599623673,\\\"mt_charge\\\":7.0,\\\"poi_charge\\\":8.0,\\\"reduce_fee\\\":15.0,\\\"remark\\\":\\\"满35.0元减15.0元\\\",\\\"type\\\":2},{}]\",\"favorites\":true,\"hasInvoiced\":0,\"invoiceTitle\":\"\",\"isFavorites\":true,\"isPoiFirstOrder\":false,\"isThirdShipping\":0,\"latitude\":30.649727,\"logisticsCode\":\"1003\",\"longitude\":104.118622,\"orderId\":63911213061779108,\"orderIdView\":63911213061779108,\"originalPrice\":42.7,\"payType\":2,\"poiAddress\":\"成都市成华区双成三路16号附67号1层\",\"poiFirstOrder\":false,\"poiId\":6391121,\"poiName\":\"何师烧烤（华润24城店）\",\"poiPhone\":\"17729828480\",\"poiReceiveDetail\":\"{\\\"actOrderChargeByMt\\\":[{\\\"comment\\\":\\\"活动款\\\",\\\"feeTypeDesc\\\":\\\"活动款\\\",\\\"feeTypeId\\\":10019,\\\"moneyCent\\\":700}],\\\"actOrderChargeByPoi\\\":[{\\\"comment\\\":\\\"满35.0元减15.0元\\\",\\\"feeTypeDesc\\\":\\\"活动款\\\",\\\"feeTypeId\\\":10019,\\\"moneyCent\\\":800}],\\\"foodShareFeeChargeByPoi\\\":174,\\\"logisticsFee\\\":500,\\\"onlinePayment\\\":2770,\\\"reconciliationExtras\\\":\\\"{\\\\\\\"performanceServiceFee\\\\\\\":0.0}\\\",\\\"wmPoiReceiveCent\\\":3296}\",\"recipientAddress\":\"华润·二十四城一期 (4号楼1单元3101)@#四川省成都市成华区双庆路双庆路6号华润·二十四城一期\",\"recipientName\":\"陈女士(女士)\",\"recipientPhone\":\"15682116412_2066\",\"shipperPhone\":\"\",\"shippingFee\":5.0,\"status\":2,\"taxpayerId\":\"\",\"total\":27.7,\"utime\":1577944851}"}
```

新订单中的订单字段的格式化参数

```
{
	"avgSendTime": 2740.0,
	"caution": "收餐人隐私号 15682116412_2066，手机号 138****7499 ",
	"cityId": 510100,
	"ctime": 1577944851,
	"daySeq": "10",
	"deliveryTime": 0,
	"detail": "[{\"app_food_code\":\"1e4ccaf621d541f084e0a6b06672b221\",\"box_num\":1.0,\"box_price\":0.1,\"cart_id\":0,\"food_discount\":1.0,\"food_name\":\"【有料】烤烟熏豆腐干\",\"food_property\":\"\",\"price\":1.8,\"quantity\":2,\"sku_id\":\"1e4ccaf621d541f084e0a6b06672b221\",\"spec\":\"\",\"unit\":\"1串\"},{\"app_food_code\":\"6617793368720146433\",\"box_num\":2.0,\"box_price\":1.0,\"cart_id\":0,\"food_discount\":1.0,\"food_name\":\"招牌鸭脑壳（2个）\",\"food_property\":\"\",\"price\":16.0,\"quantity\":2,\"sku_id\":\"6617793368720146433\",\"spec\":\"\",\"unit\":\"2个\"}]",
	"dinnersNumber": 0,
	"ePoiId": "f92e6389-be05-484e-af5d-b06eb85906c6",
	"extras": "[{\"act_detail_id\":1599623673,\"mt_charge\":7.0,\"poi_charge\":8.0,\"reduce_fee\":15.0,\"remark\":\"满35.0元减15.0元\",\"type\":2},{}]",
	"favorites": true,
	"hasInvoiced": 0,
	"invoiceTitle": "",
	"isFavorites": true,
	"isPoiFirstOrder": false,
	"isThirdShipping": 0,
	"latitude": 30.649727,
	"logisticsCode": "1003",
	"longitude": 104.118622,
	"orderId": 63911213061779108,
	"orderIdView": 63911213061779108,
	"originalPrice": 42.7,
	"payType": 2,
	"poiAddress": "成都市成华区双成三路16号附67号1层",
	"poiFirstOrder": false,
	"poiId": 6391121,
	"poiName": "何师烧烤（华润24城店）",
	"poiPhone": "17729828480",
	"poiReceiveDetail": "{\"actOrderChargeByMt\":[{\"comment\":\"活动款\",\"feeTypeDesc\":\"活动款\",\"feeTypeId\":10019,\"moneyCent\":700}],\"actOrderChargeByPoi\":[{\"comment\":\"满35.0元减15.0元\",\"feeTypeDesc\":\"活动款\",\"feeTypeId\":10019,\"moneyCent\":800}],\"foodShareFeeChargeByPoi\":174,\"logisticsFee\":500,\"onlinePayment\":2770,\"reconciliationExtras\":\"{\\\"performanceServiceFee\\\":0.0}\",\"wmPoiReceiveCent\":3296}",
	"recipientAddress": "华润·二十四城一期 (4号楼1单元3101)@#四川省成都市成华区双庆路双庆路6号华润·二十四城一期",
	"recipientName": "陈女士(女士)",
	"recipientPhone": "15682116412_2066",
	"shipperPhone": "",
	"shippingFee": 5.0,
	"status": 2,
	"taxpayerId": "",
	"total": 27.7,
	"utime": 1577944851
}
```

接单原始参数

```
{"developerId":105325,"sign":"ed59de0f64cd0db095c170d8f2d08a5a93808892","ePoiId":"a3ee7d39-d626-47e4-9c58-ba004ba90324","order":"{\"avgSendTime\":2583.0,\"caution\":\"收餐人隐私号 17882960258_9810，手机号 180****6697 \",\"cityId\":510100,\"ctime\":1577940684,\"daySeq\":\"13\",\"deliveryTime\":0,\"detail\":\"[{\\\"app_food_code\\\":\\\"bc3450badc584d759a2aa6a09b146ad5\\\",\\\"box_num\\\":2.0,\\\"box_price\\\":1.0,\\\"cart_id\\\":0,\\\"food_discount\\\":1.0,\\\"food_name\\\":\\\"卤烤鲜猪蹄（半根）\\\",\\\"food_property\\\":\\\"\\\",\\\"price\\\":22.0,\\\"quantity\\\":2,\\\"sku_id\\\":\\\"bc3450badc584d759a2aa6a09b146ad5\\\",\\\"spec\\\":\\\"\\\",\\\"unit\\\":\\\"\\\"}]\",\"dinnersNumber\":0,\"ePoiId\":\"a3ee7d39-d626-47e4-9c58-ba004ba90324\",\"extras\":\"[{\\\"act_detail_id\\\":1599623673,\\\"mt_charge\\\":7.0,\\\"poi_charge\\\":8.0,\\\"reduce_fee\\\":15.0,\\\"remark\\\":\\\"满35.0元减15.0元\\\",\\\"type\\\":2},{\\\"act_detail_id\\\":*********,\\\"mt_charge\\\":0.0,\\\"poi_charge\\\":2.0,\\\"reduce_fee\\\":2.0,\\\"remark\\\":\\\"用户使用联盟津贴减2元\\\",\\\"type\\\":305},{\\\"act_detail_id\\\":440039387,\\\"mt_charge\\\":0.0,\\\"poi_charge\\\":3.5,\\\"reduce_fee\\\":3.5,\\\"remark\\\":\\\"减配送费3.5元\\\",\\\"type\\\":25},{}]\",\"favorites\":false,\"hasInvoiced\":0,\"invoiceTitle\":\"\",\"isFavorites\":false,\"isPoiFirstOrder\":true,\"isThirdShipping\":0,\"latitude\":30.690355,\"logisticsCode\":\"1003\",\"longitude\":104.100386,\"orderId\":65038103208869099,\"orderIdView\":65038103208869099,\"originalPrice\":51.0,\"payType\":2,\"poiAddress\":\"成都市成华区高车三路69号4栋1楼8、9、11号\",\"poiFirstOrder\":true,\"poiId\":6503810,\"poiName\":\"何师烧烤（永立星城都店）\",\"poiPhone\":\"028-83520158\",\"poiReceiveDetail\":\"{\\\"actOrderChargeByMt\\\":[{\\\"comment\\\":\\\"活动款\\\",\\\"feeTypeDesc\\\":\\\"活动款\\\",\\\"feeTypeId\\\":10019,\\\"moneyCent\\\":700}],\\\"actOrderChargeByPoi\\\":[{\\\"comment\\\":\\\"满35.0元减15.0元\\\",\\\"feeTypeDesc\\\":\\\"活动款\\\",\\\"feeTypeId\\\":10019,\\\"moneyCent\\\":800},{\\\"comment\\\":\\\"用户使用联盟津贴减2元\\\",\\\"feeTypeDesc\\\":\\\"活动款\\\",\\\"feeTypeId\\\":10019,\\\"moneyCent\\\":200},{\\\"comment\\\":\\\"减配送费3.5元\\\",\\\"feeTypeDesc\\\":\\\"活动款\\\",\\\"feeTypeId\\\":10019,\\\"moneyCent\\\":350}],\\\"foodShareFeeChargeByPoi\\\":188,\\\"logisticsFee\\\":500,\\\"onlinePayment\\\":3050,\\\"reconciliationExtras\\\":\\\"{\\\\\\\"performanceServiceFee\\\\\\\":0.0}\\\",\\\"wmPoiReceiveCent\\\":3562}\",\"recipientAddress\":\"永立龙寓 (二环路北四段3号永立龙寓)@#四川省成都市成华区瑞丰巷瑞丰巷永立·星城都\",\"recipientName\":\"彭绍文(女士)\",\"recipientPhone\":\"17882960258_9810\",\"shipperPhone\":\"\",\"shippingFee\":5.0,\"status\":4,\"taxpayerId\":\"\",\"total\":30.5,\"utime\":1577940686}"}
```

接单中的订单字段的格式化参数

```
{
	"avgSendTime": 2583.0,
	"caution": "收餐人隐私号 17882960258_9810，手机号 180****6697 ",
	"cityId": 510100,
	"ctime": 1577940684,
	"daySeq": "13",
	"deliveryTime": 0,
	"detail": "[{\"app_food_code\":\"bc3450badc584d759a2aa6a09b146ad5\",\"box_num\":2.0,\"box_price\":1.0,\"cart_id\":0,\"food_discount\":1.0,\"food_name\":\"卤烤鲜猪蹄（半根）\",\"food_property\":\"\",\"price\":22.0,\"quantity\":2,\"sku_id\":\"bc3450badc584d759a2aa6a09b146ad5\",\"spec\":\"\",\"unit\":\"\"}]",
	"dinnersNumber": 0,
	"ePoiId": "a3ee7d39-d626-47e4-9c58-ba004ba90324",
	"extras": "[{\"act_detail_id\":1599623673,\"mt_charge\":7.0,\"poi_charge\":8.0,\"reduce_fee\":15.0,\"remark\":\"满35.0元减15.0元\",\"type\":2},{\"act_detail_id\":*********,\"mt_charge\":0.0,\"poi_charge\":2.0,\"reduce_fee\":2.0,\"remark\":\"用户使用联盟津贴减2元\",\"type\":305},{\"act_detail_id\":440039387,\"mt_charge\":0.0,\"poi_charge\":3.5,\"reduce_fee\":3.5,\"remark\":\"减配送费3.5元\",\"type\":25},{}]",
	"favorites": false,
	"hasInvoiced": 0,
	"invoiceTitle": "",
	"isFavorites": false,
	"isPoiFirstOrder": true,
	"isThirdShipping": 0,
	"latitude": 30.690355,
	"logisticsCode": "1003",
	"longitude": 104.100386,
	"orderId": 65038103208869099,
	"orderIdView": 65038103208869099,
	"originalPrice": 51.0,
	"payType": 2,
	"poiAddress": "成都市成华区高车三路69号4栋1楼8、9、11号",
	"poiFirstOrder": true,
	"poiId": 6503810,
	"poiName": "何师烧烤（永立星城都店）",
	"poiPhone": "028-83520158",
	"poiReceiveDetail": "{\"actOrderChargeByMt\":[{\"comment\":\"活动款\",\"feeTypeDesc\":\"活动款\",\"feeTypeId\":10019,\"moneyCent\":700}],\"actOrderChargeByPoi\":[{\"comment\":\"满35.0元减15.0元\",\"feeTypeDesc\":\"活动款\",\"feeTypeId\":10019,\"moneyCent\":800},{\"comment\":\"用户使用联盟津贴减2元\",\"feeTypeDesc\":\"活动款\",\"feeTypeId\":10019,\"moneyCent\":200},{\"comment\":\"减配送费3.5元\",\"feeTypeDesc\":\"活动款\",\"feeTypeId\":10019,\"moneyCent\":350}],\"foodShareFeeChargeByPoi\":188,\"logisticsFee\":500,\"onlinePayment\":3050,\"reconciliationExtras\":\"{\\\"performanceServiceFee\\\":0.0}\",\"wmPoiReceiveCent\":3562}",
	"recipientAddress": "永立龙寓 (二环路北四段3号永立龙寓)@#四川省成都市成华区瑞丰巷瑞丰巷永立·星城都",
	"recipientName": "彭绍文(女士)",
	"recipientPhone": "17882960258_9810",
	"shipperPhone": "",
	"shippingFee": 5.0,
	"status": 4,
	"taxpayerId": "",
	"total": 30.5,
	"utime": 1577940686
}
```

综上所述：

1、饿了么接单时不能获取到完整数据，不可直接当作新订单入库。

2、美团接单时能获取到订单的完整数据，可作为新订单直接入库，之后，对于新订单消息则忽略即可。（发生这种情况一般是商家在美团商家客户端开启了自动接单）