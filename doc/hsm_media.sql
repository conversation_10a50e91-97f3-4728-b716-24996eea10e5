CREATE TABLE `hsm_media` (
  `id` bigint(8) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `guid` varchar(50) NOT NULL COMMENT '文件GUID',
  `brand_guid` varchar(50) DEFAULT NULL COMMENT '品牌GUID',
  `store_guid` varchar(50) DEFAULT NULL COMMENT '门店guid',
  `name` varchar(500) NOT NULL COMMENT '名称',
  `file_url` varchar(260) DEFAULT NULL COMMENT '文件路径，文件夹为空',
  `parent_guid` varchar(50) DEFAULT NULL COMMENT '父目录guid，根目录下的文件或文件夹为空',
  `is_folder` tinyint(1) unsigned NOT NULL COMMENT '是否文件夹',
  `is_delete` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除',
  `is_brand` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否品牌图库',
  `file_size` bigint(10) unsigned DEFAULT NULL COMMENT '文件大小（单位：KB）',
  `file_type` int(10) unsigned NOT NULL COMMENT '文件类型：0-文件夹 1-图片 2-音乐 3-视频 4-其它格式文件',
  `remark` varchar(50) DEFAULT NULL COMMENT '备注',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_user_guid` varchar(50) DEFAULT NULL COMMENT '创建人guid',
  `create_user_name` varchar(50) DEFAULT NULL COMMENT '创建人名称',
  `modify_user_guid` varchar(50) DEFAULT NULL COMMENT '修改人guid',
  `modify_user_name` varchar(50) DEFAULT NULL COMMENT '修改人名称',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_guid` (`guid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='媒体文件表';