# 附加费技术方案

## 一. 组件
## 二. 服务拓扑
1. 结构

    略
    
2. 数据流

```
graph LR
安卓端-->Nginx
商户后台-->Nginx
微信端-->Nginx
Nginx-->Gateway
Gateway-->Merchant
Merchant-->Business
```

3. 服务依赖

    - holder-saas-store-staff 员工服务
    - holder-saas-store-table 桌台服务
    


## 三. 服务设计
1. 业务设计



- 元素定义
    
    SurchargeDTO: 基础元素，包括门店GUID、附加费guid、附加费名、附加费金额、收费方式、关联区域

    SurchargeCreateDTO: 基础元素

    SurchargeQueryDTO: 基础元素

    SurchargeUpdateDTO: 基础元素

    SurchargeEnableDTO: 基础元素

    SurchargeDeleteDTO: 基础元素

    SurchargeBatchEnableDTO: 基础元素

    SurchargeListDTO  : 基础元素

2. 表结构设计

    （1）hsb_surcharge表
    
    name | type | comment
    ---|---|---
    id | bigint(64) | 自增id
    surcharge_guid | varchar(45) | 门店guid
    store_guid | varchar(45) | 附加费guid
    name | varchar(45) | 附加费名
    amount | decimal(7,2) | 附加费金额
    type|tinyint(1)|附加费类型
    is_enable|tinyint(1)|是否已启用
    is_deleted|tinyint(1)|是否已删除
    gmt_create|datetime|创建时间
    gmt_modified|datetime|修改时间
    
    （2）hsb_surcharge_area表
    
       name | type | comment
    ---|---|---
    id | bigint(64) | 自增id
    guid | varchar(45) | 唯一guid
    surcharge_guid | varchar(45) | 附加费guid
    area_guid | varchar(45) | 区域guid

    gmt_create|datetime|创建时间
    gmt_modified|datetime|修改时间
    
   
    
3. 主要流程

- 设置模板
 
(1)新增附加费


```
graph LR
开始-->创建附加费
创建附加费-->选择收费方式
选择收费方式-->按人
选择收费方式-->按桌
按人-->填写名称,金额
按桌-->填写名称,金额
填写名称,金额-->选择适用区域
选择使用区域-->成功
选择使用区域-->失败
成功-->结束
失败-->通知提示
通知提示-->结束

```
（2）查询附加费

```
graph TB
开始-->查询附加费
查询附加费-->选择门店
选择门店-->选择收费方式
选择收费方式-->查询数据总条数
查询数据总条数-->分页查询
分页查询-->返回结果

```



4. 功能设计

- 新增、修改附加费
    1. 选择不同收费方式
    2. 查询该门店下是否已经存在同样名字的附加费
    3. 保存/修改附加费

- 查询附加费
    1. 根据附加费类型查询
    2. 根据区域查询附加费


- 启用/停用/删除附加费
    1. 启用附加费
    2. 停用附加费
    
- 批量操作附加费
    1. 批量删除附加费
    2. 批量启用附加费
    3. 批量停用附加费
    

## 四. 其他方案
## 五. 服务参数
## 六. 补充说明