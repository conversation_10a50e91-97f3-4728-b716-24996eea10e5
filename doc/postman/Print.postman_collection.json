{"variables": [], "info": {"name": "Print", "_postman_id": "629e9ac6-8747-e44a-08b8-022d04c77944", "description": "", "schema": "https://schema.getpostman.com/json/collection/v2.0.0/collection.json"}, "item": [{"name": "************:8681/test/order", "request": {"url": "http://*************:8162", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "description": ""}], "body": {"mode": "raw", "raw": "{\n\t\"printBusiness\": \"ORDER\",\n\t\"storeName\": \"测试门店\",\n\t\"storeGuid\": \"123456\",\n\t\"enterpriseGuid\": \"12344\",\n\t\"printUid\": \"100\",\n\t\"operatorStaffGuid\": \"123456\",\n\t\"operatorStaffName\": \"hsx\",\n\t\"createTime\": \"1538036100\",\n\t\"markName\": \"牌号\",\n\t\"markNo\": \"#1\",\n\t\"orderTime\": \"1538035281000\",\n\t\"orderNo\": \"1234\",\n\t\"personNumber\": \"2\",\n\t\"remark\": \"测试整单备注\",\n\t\"totalNumber\": \"1\",\n\t\"itemRecordList\": [\n\t\t{\n\t\t\"itemGuid\":\"1\",\n\t\t\"daySerialNumber\": \"001\",\n\t\t\"itemTypeName\": \"热菜\",\n\t\t\"itemTypeGuid\": \"12345\",\n\t\t\"itemTypeTotal\": \"2\",\n\t\t\"itemName\": \"炒白菜\",\n\t\t\"price\": \"2\",\n\t\t\"number\": \"2\",\n\t\t\"subtotal\": \"2\",\n\t\t\"remark\": \"不要辣椒\",\n\t\t\"practice\": [\n\t\t\t\"做法\"\n\t\t]\n    \t},\n    \t{\n\t\t\"itemGuid\":\"2\",\n\t\t\"daySerialNumber\": \"001\",\n\t\t\"itemTypeName\": \"热菜\",\n\t\t\"itemTypeGuid\": \"123451\",\n\t\t\"itemTypeTotal\": \"2\",\n\t\t\"itemName\": \"炒清菜\",\n\t\t\"price\": \"2\",\n\t\t\"number\": \"2\",\n\t\t\"subtotal\": \"2\",\n\t\t\"remark\": \"不要辣椒\",\n\t\t\"practice\": [\n\t\t\t\"做法\"\n\t\t]\n\t    }\n\t]\n}"}, "description": "TestPrintOrder"}, "response": []}, {"name": "添加前台打印机", "request": {"url": "localhost:8917/printer/add", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json;charset=utf-8", "description": ""}, {"key": "userInfo", "value": "%7B%22userGuid%22%3A%226480756476603191298%22%2C%22enterpriseGuid%22%3A%226490862829263766529%22%2C%22enterpriseName%22%3A%22%E6%89%93%E5%8D%B0%E4%BC%81%E4%B8%9A1218%22%2C%22name%22%3A%22tcw%22%2C%22tel%22%3A%*************%22%2C%22storeGuid%22%3A%226480756384945597441%22%2C%22storeName%22%3A%22%E6%89%93%E5%8D%B0%E9%97%A8%E5%BA%971218%22%2C%22account%22%3A%********%22%2C%22storeNo%22%3A%*********%22%7D", "description": ""}], "body": {"mode": "raw", "raw": "{\n\t\"printerName\": \"前台\",\n\t\"storeGuid\": \"mock_store_guid\",\n\t\"storeName\": \"Mock门店\",\n\t\"printerType\": 0,\n\t\"printerIp\": \"\",\n\t\"deviceId\": \"mock_device_id\",\n\t\"printerPort\": 9100,\n\t\"staffGuid\": \"mock_staff_guid\",\n\t\"businessType\": 0,\n\t\"printCount\": 1,\n\t\"printPage\": \"80\",\n\t\"arrayOfInvoiceType\": [0, 5, 6, 7, 10, 15, 20, 25, 40, 41, 42, 43, 44, 45, 46, 47, 48],\n\t\"arrayOfAreaGuid\": [\"mock_area_name_1,mock_area_guid_1\", \"mock_area_name_2,mock_area_guid_2\"]\n}"}, "description": "添加打印机"}, "response": []}, {"name": "添加前台打印机-58", "request": {"url": "localhost:8917/printer/add", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json;charset=utf-8", "description": ""}, {"key": "userInfo", "value": "%7B%22userGuid%22%3A%226480756476603191298%22%2C%22enterpriseGuid%22%3A%226490862829263766529%22%2C%22enterpriseName%22%3A%22%E6%89%93%E5%8D%B0%E4%BC%81%E4%B8%9A1218%22%2C%22name%22%3A%22tcw%22%2C%22tel%22%3A%*************%22%2C%22storeGuid%22%3A%226480756384945597441%22%2C%22storeName%22%3A%22%E6%89%93%E5%8D%B0%E9%97%A8%E5%BA%971218%22%2C%22account%22%3A%********%22%2C%22storeNo%22%3A%*********%22%7D", "description": ""}], "body": {"mode": "raw", "raw": "{\n\t\"printerName\": \"前台-wlan-58\",\n\t\"storeGuid\": \"mock_store_guid\",\n\t\"storeName\": \"Mock门店\",\n\t\"printerType\": 1,\n\t\"printerIp\": \"**************\",\n\t\"deviceId\": \"mock_device_id\",\n\t\"printerPort\": 9100,\n\t\"staffGuid\": \"mock_staff_guid\",\n\t\"businessType\": 0,\n\t\"printCount\": 1,\n\t\"printPage\": \"58\",\n\t\"arrayOfInvoiceType\": [0, 5, 6, 7, 10, 15, 20, 25, 40, 41, 42, 43, 44, 45, 46, 47, 48],\n\t\"arrayOfAreaGuid\": [\"mock_area_name_1,mock_area_guid_1\", \"mock_area_name_2,mock_area_guid_2\"]\n}"}, "description": "添加打印机"}, "response": []}, {"name": "添加后厨打印机", "request": {"url": "localhost:8917/printer/add", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json;charset=utf-8", "description": ""}, {"key": "userInfo", "value": "%7B%22userGuid%22%3A%226480756476603191298%22%2C%22enterpriseGuid%22%3A%226490862829263766529%22%2C%22enterpriseName%22%3A%22%E6%89%93%E5%8D%B0%E4%BC%81%E4%B8%9A1218%22%2C%22name%22%3A%22tcw%22%2C%22tel%22%3A%*************%22%2C%22storeGuid%22%3A%226480756384945597441%22%2C%22storeName%22%3A%22%E6%89%93%E5%8D%B0%E9%97%A8%E5%BA%971218%22%2C%22account%22%3A%********%22%2C%22storeNo%22%3A%*********%22%7D", "description": ""}], "body": {"mode": "raw", "raw": "{\n\t\"printerName\": \"后厨\",\n\t\"storeGuid\": \"mock_store_guid\",\n\t\"storeName\": \"Mock门店\",\n\t\"printerType\": 1,\n\t\"printerIp\": \"************\",\n\t\"deviceId\": \"mock_device_id\",\n\t\"printerPort\": 9100,\n\t\"staffGuid\": \"mock_staff_guid\",\n\t\"businessType\": 1,\n\t\"printCount\": 1,\n\t\"printPage\": \"80\",\n\t\"printCut\": 1,\n\t\"arrayOfInvoiceType\": [80, 81, 85],\n\t\"arrayOfItemGuid\": [\n\t\t\"mock菜品1,mock_item_guid_1\", \n\t\t\"mock菜品2,mock_item_guid_2\"\n\t],\n\t\"arrayOfAreaGuid\": [\"mock_area_name_1,mock_area_guid_1\", \"mock_area_name_2,mock_area_guid_2\"]\n}\n"}, "description": "添加打印机"}, "response": []}, {"name": "添加后厨打印机-58", "request": {"url": "localhost:8917/printer/add", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json;charset=utf-8", "description": ""}, {"key": "userInfo", "value": "%7B%22userGuid%22%3A%226480756476603191298%22%2C%22enterpriseGuid%22%3A%226490862829263766529%22%2C%22enterpriseName%22%3A%22%E6%89%93%E5%8D%B0%E4%BC%81%E4%B8%9A1218%22%2C%22name%22%3A%22tcw%22%2C%22tel%22%3A%*************%22%2C%22storeGuid%22%3A%226480756384945597441%22%2C%22storeName%22%3A%22%E6%89%93%E5%8D%B0%E9%97%A8%E5%BA%971218%22%2C%22account%22%3A%********%22%2C%22storeNo%22%3A%*********%22%7D", "description": ""}], "body": {"mode": "raw", "raw": "{\n\t\"printerName\": \"后厨-wlan-58\",\n\t\"storeGuid\": \"mock_store_guid\",\n\t\"storeName\": \"Mock门店\",\n\t\"printerType\": 1,\n\t\"printerIp\": \"**************\",\n\t\"deviceId\": \"mock_device_id\",\n\t\"printerPort\": 9100,\n\t\"staffGuid\": \"mock_staff_guid\",\n\t\"businessType\": 1,\n\t\"printCount\": 1,\n\t\"printPage\": \"58\",\n\t\"printCut\": 1,\n\t\"arrayOfInvoiceType\": [80, 81, 85],\n\t\"arrayOfItemGuid\": [\n\t\t\"mock菜品1,mock_item_guid_1\", \n\t\t\"mock菜品2,mock_item_guid_2\"\n\t],\n\t\"arrayOfAreaGuid\": [\"mock_area_name_1,mock_area_guid_1\", \"mock_area_name_2,mock_area_guid_2\"]\n}\n"}, "description": "添加打印机"}, "response": []}, {"name": "查询单个打印机详情", "request": {"url": "localhost:8917/printer/query", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json;charset=utf-8", "description": ""}, {"key": "userInfo", "value": "%7B%22userGuid%22%3A%226480756476603191298%22%2C%22enterpriseGuid%22%3A%226490862829263766529%22%2C%22enterpriseName%22%3A%22%E6%89%93%E5%8D%B0%E4%BC%81%E4%B8%9A1218%22%2C%22name%22%3A%22tcw%22%2C%22tel%22%3A%*************%22%2C%22storeGuid%22%3A%226480756384945597441%22%2C%22storeName%22%3A%22%E6%89%93%E5%8D%B0%E9%97%A8%E5%BA%971218%22%2C%22account%22%3A%********%22%2C%22storeNo%22%3A%*********%22%7D", "description": ""}], "body": {"mode": "raw", "raw": "{\r\n  \"printerGuid\": \"6487941645491765249\"\r\n}"}, "description": "Query"}, "response": []}, {"name": "查询当前设备的打印机", "request": {"url": "localhost:8917/printer/list_by_device", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json;charset=utf-8", "description": ""}, {"key": "userInfo", "value": "%7B%22userGuid%22%3A%226480756476603191298%22%2C%22enterpriseGuid%22%3A%226490862829263766529%22%2C%22enterpriseName%22%3A%22%E6%89%93%E5%8D%B0%E4%BC%81%E4%B8%9A1218%22%2C%22name%22%3A%22tcw%22%2C%22tel%22%3A%*************%22%2C%22storeGuid%22%3A%226480756384945597441%22%2C%22storeName%22%3A%22%E6%89%93%E5%8D%B0%E9%97%A8%E5%BA%971218%22%2C%22account%22%3A%********%22%2C%22storeNo%22%3A%*********%22%7D", "description": ""}], "body": {"mode": "raw", "raw": "{\n\t\"storeGuid\": \"mock_store_guid\",\n\t\"deviceId\": \"mock_device_id\"\n}"}, "description": "QueryOfBizType"}, "response": []}, {"name": "查询前台打印机", "request": {"url": "localhost:8917/printer/list_by_biz", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json;charset=utf-8", "description": ""}, {"key": "userInfo", "value": "%7B%22userGuid%22%3A%226480756476603191298%22%2C%22enterpriseGuid%22%3A%226490862829263766529%22%2C%22enterpriseName%22%3A%22%E6%89%93%E5%8D%B0%E4%BC%81%E4%B8%9A1218%22%2C%22name%22%3A%22tcw%22%2C%22tel%22%3A%*************%22%2C%22storeGuid%22%3A%226480756384945597441%22%2C%22storeName%22%3A%22%E6%89%93%E5%8D%B0%E9%97%A8%E5%BA%971218%22%2C%22account%22%3A%********%22%2C%22storeNo%22%3A%*********%22%7D", "description": ""}], "body": {"mode": "raw", "raw": "{\n\t\"storeGuid\": \"6480756384945597441\",\n\t\"businessType\": 0,\n\t\"deviceId\": \"6481150069296058369\"\n}"}, "description": "修改打印机"}, "response": []}, {"name": "查询后厨打印机", "request": {"url": "localhost:8917/printer/list_by_biz", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json;charset=utf-8", "description": ""}, {"key": "userInfo", "value": "%7B%22userGuid%22%3A%226480756476603191298%22%2C%22enterpriseGuid%22%3A%226490862829263766529%22%2C%22enterpriseName%22%3A%22%E6%89%93%E5%8D%B0%E4%BC%81%E4%B8%9A1218%22%2C%22name%22%3A%22tcw%22%2C%22tel%22%3A%*************%22%2C%22storeGuid%22%3A%226480756384945597441%22%2C%22storeName%22%3A%22%E6%89%93%E5%8D%B0%E9%97%A8%E5%BA%971218%22%2C%22account%22%3A%********%22%2C%22storeNo%22%3A%*********%22%7D", "description": ""}], "body": {"mode": "raw", "raw": "{\n\t\"storeGuid\": \"6480756384945597441\",\n\t\"businessType\": 1,\n\t\"deviceId\": \"6481150069296058369\"\n}"}, "description": "修改打印机"}, "response": []}, {"name": "查询标签打印机", "request": {"url": "localhost:8917/printer/list_by_biz", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json;charset=utf-8", "description": ""}, {"key": "userInfo", "value": "%7B%22userGuid%22%3A%226480756476603191298%22%2C%22enterpriseGuid%22%3A%226490862829263766529%22%2C%22enterpriseName%22%3A%22%E6%89%93%E5%8D%B0%E4%BC%81%E4%B8%9A1218%22%2C%22name%22%3A%22tcw%22%2C%22tel%22%3A%*************%22%2C%22storeGuid%22%3A%226480756384945597441%22%2C%22storeName%22%3A%22%E6%89%93%E5%8D%B0%E9%97%A8%E5%BA%971218%22%2C%22account%22%3A%********%22%2C%22storeNo%22%3A%*********%22%7D", "description": ""}], "body": {"mode": "raw", "raw": "{\n\t\"storeGuid\": \"6480756384945597441\",\n\t\"businessType\": 2,\n\t\"deviceId\": \"6481150069296058369\"\n}"}, "description": "修改打印机"}, "response": []}, {"name": "修改打印机", "request": {"url": "localhost:8917/printer/update", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json;charset=utf-8", "description": ""}, {"key": "userInfo", "value": "%7B%22userGuid%22%3A%226480756476603191298%22%2C%22enterpriseGuid%22%3A%226490862829263766529%22%2C%22enterpriseName%22%3A%22%E6%89%93%E5%8D%B0%E4%BC%81%E4%B8%9A1218%22%2C%22name%22%3A%22tcw%22%2C%22tel%22%3A%*************%22%2C%22storeGuid%22%3A%226480756384945597441%22%2C%22storeName%22%3A%22%E6%89%93%E5%8D%B0%E9%97%A8%E5%BA%971218%22%2C%22account%22%3A%********%22%2C%22storeNo%22%3A%*********%22%7D", "description": ""}], "body": {"mode": "raw", "raw": "{\n\t\"printerGuid\": \"6487941645491765249\",\n\t\"printerName\": \"前台（修）\",\n\t\"storeGuid\": \"6480756384945597441\",\n\t\"storeName\": \"打印门店1218\",\n\t\"printerType\": 0,\n\t\"printerIp\": \"\",\n\t\"deviceId\": \"6481150069296058369\",\n\t\"printerPort\": \"9100\",\n\t\"staffGuid\": \"6480756476603191298\",\n\t\"businessType\": 0,\n\t\"printCount\": 1,\n\t\"printPage\": \"80\",\n\t\"arrayOfInvoiceType\": [0, 5, 6, 7, 10, 15, 20, 25, 40, 41, 42, 43, 44, 45, 46, 47, 48],\n\t\"arrayOfItemGuid\": [],\n\t\"arrayOfAreaGuid\": []\n}"}, "description": "修改打印机"}, "response": []}, {"name": "修改主机", "request": {"url": "localhost:8917/printer/change_master", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json;charset=utf-8", "description": ""}, {"key": "userInfo", "value": "%7B%22userGuid%22%3A%226480756476603191298%22%2C%22enterpriseGuid%22%3A%226490862829263766529%22%2C%22enterpriseName%22%3A%22%E6%89%93%E5%8D%B0%E4%BC%81%E4%B8%9A1218%22%2C%22name%22%3A%22tcw%22%2C%22tel%22%3A%*************%22%2C%22storeGuid%22%3A%226480756384945597441%22%2C%22storeName%22%3A%22%E6%89%93%E5%8D%B0%E9%97%A8%E5%BA%971218%22%2C%22account%22%3A%********%22%2C%22storeNo%22%3A%*********%22%7D", "description": ""}], "body": {"mode": "raw", "raw": "{\n\t\"storeGuid\": \"6480756384945597441\",\n\t\"businessType\": 0,\n\t\"deviceId\": \"6481150069296058369\"\n}"}, "description": "修改打印机"}, "response": []}, {"name": "删除打印机", "request": {"url": "localhost:8917/printer/delete", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json;charset=utf-8", "description": ""}, {"key": "userInfo", "value": "%7B%22userGuid%22%3A%226480756476603191298%22%2C%22enterpriseGuid%22%3A%226490862829263766529%22%2C%22enterpriseName%22%3A%22%E6%89%93%E5%8D%B0%E4%BC%81%E4%B8%9A1218%22%2C%22name%22%3A%22tcw%22%2C%22tel%22%3A%*************%22%2C%22storeGuid%22%3A%226480756384945597441%22%2C%22storeName%22%3A%22%E6%89%93%E5%8D%B0%E9%97%A8%E5%BA%971218%22%2C%22account%22%3A%********%22%2C%22storeNo%22%3A%*********%22%7D", "description": ""}], "body": {"mode": "raw", "raw": "{\n\t\"storeGuid\": \"6480756384945597441\",\n\t\"businessType\": 0,\n\t\"deviceId\": \"6481150069296058369\"\n}"}, "description": "修改打印机"}, "response": []}, {"name": "删除当前设备的打印机", "request": {"url": "localhost:8917/printer/delete_by_device", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json;charset=utf-8", "description": ""}, {"key": "userInfo", "value": "%7B%22userGuid%22%3A%226480756476603191298%22%2C%22enterpriseGuid%22%3A%226490862829263766529%22%2C%22enterpriseName%22%3A%22%E6%89%93%E5%8D%B0%E4%BC%81%E4%B8%9A1218%22%2C%22name%22%3A%22tcw%22%2C%22tel%22%3A%*************%22%2C%22storeGuid%22%3A%226480756384945597441%22%2C%22storeName%22%3A%22%E6%89%93%E5%8D%B0%E9%97%A8%E5%BA%971218%22%2C%22account%22%3A%********%22%2C%22storeNo%22%3A%*********%22%7D", "description": ""}], "body": {"mode": "raw", "raw": "{\n\t\"storeGuid\": \"6480756384945597441\",\n\t\"businessType\": 0,\n\t\"deviceId\": \"6481150069296058369\"\n}"}, "description": "修改打印机"}, "response": []}, {"name": "获取打印内容", "request": {"url": "localhost:8917/print_record/get_content", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json;charset=utf-8", "description": ""}, {"key": "userInfo", "value": "%7B%22userGuid%22%3A%226480756476603191298%22%2C%22enterpriseGuid%22%3A%226490862829263766529%22%2C%22enterpriseName%22%3A%22%E6%89%93%E5%8D%B0%E4%BC%81%E4%B8%9A1218%22%2C%22name%22%3A%22tcw%22%2C%22tel%22%3A%*************%22%2C%22storeGuid%22%3A%226480756384945597441%22%2C%22storeName%22%3A%22%E6%89%93%E5%8D%B0%E9%97%A8%E5%BA%971218%22%2C%22account%22%3A%********%22%2C%22storeNo%22%3A%*********%22%7D", "description": ""}], "body": {"mode": "raw", "raw": "{\n\t\"recordGuid\": \"6489415094575824897\"\n}"}, "description": "修改打印机"}, "response": []}, {"name": "获取打印内容 新", "request": {"url": "localhost:8917/print_record/get_order", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json;charset=utf-8", "description": ""}, {"key": "userInfo", "value": "%7B%22userGuid%22%3A%226480756476603191298%22%2C%22enterpriseGuid%22%3A%226490862829263766529%22%2C%22enterpriseName%22%3A%22%E6%89%93%E5%8D%B0%E4%BC%81%E4%B8%9A1218%22%2C%22name%22%3A%22tcw%22%2C%22tel%22%3A%*************%22%2C%22storeGuid%22%3A%226480756384945597441%22%2C%22storeName%22%3A%22%E6%89%93%E5%8D%B0%E9%97%A8%E5%BA%971218%22%2C%22account%22%3A%********%22%2C%22storeNo%22%3A%*********%22%7D", "description": ""}], "body": {"mode": "raw", "raw": "{\n\t\"recordGuid\": \"6489415094575824897\"\n}"}, "description": "修改打印机"}, "response": []}, {"name": "查询打印失败记录-AIO-2个前台2个后厨记录", "request": {"url": "localhost:8917/print_record/list", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json;charset=utf-8", "description": ""}, {"key": "userInfo", "value": "%7B%22userGuid%22%3A%226480756476603191298%22%2C%22enterpriseGuid%22%3A%226490862829263766529%22%2C%22enterpriseName%22%3A%22%E6%89%93%E5%8D%B0%E4%BC%81%E4%B8%9A1218%22%2C%22name%22%3A%22tcw%22%2C%22tel%22%3A%*************%22%2C%22storeGuid%22%3A%226480756384945597441%22%2C%22storeName%22%3A%22%E6%89%93%E5%8D%B0%E9%97%A8%E5%BA%971218%22%2C%22account%22%3A%********%22%2C%22storeNo%22%3A%*********%22%7D", "description": ""}], "body": {"mode": "raw", "raw": "{\n\t\"deviceType\": 3,\n\t\"storeGuid\": \"mock_store_guid\",\n\t\"deviceId\": \"mock_device_id\",\n\t\"status\": 2\n}"}, "description": "修改打印机"}, "response": []}, {"name": "查询打印失败记录-POS-2个前台记录", "request": {"url": "localhost:8917/print_record/list", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json;charset=utf-8", "description": ""}, {"key": "userInfo", "value": "%7B%22userGuid%22%3A%226480756476603191298%22%2C%22enterpriseGuid%22%3A%226490862829263766529%22%2C%22enterpriseName%22%3A%22%E6%89%93%E5%8D%B0%E4%BC%81%E4%B8%9A1218%22%2C%22name%22%3A%22tcw%22%2C%22tel%22%3A%*************%22%2C%22storeGuid%22%3A%226480756384945597441%22%2C%22storeName%22%3A%22%E6%89%93%E5%8D%B0%E9%97%A8%E5%BA%971218%22%2C%22account%22%3A%********%22%2C%22storeNo%22%3A%*********%22%7D", "description": ""}], "body": {"mode": "raw", "raw": "{\n\t\"deviceType\": 4,\n\t\"storeGuid\": \"mock_store_guid\",\n\t\"deviceId\": \"mock_device_id_2\",\n\t\"status\": 2\n}"}, "description": "修改打印机"}, "response": []}, {"name": "打印结帐单", "request": {"url": "localhost:8917/print_record/send", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json;charset=utf-8", "description": ""}, {"key": "userInfo", "value": "%7B%22userGuid%22%3A%226480756476603191298%22%2C%22enterpriseGuid%22%3A%226490862829263766529%22%2C%22enterpriseName%22%3A%22%E6%89%93%E5%8D%B0%E4%BC%81%E4%B8%9A1218%22%2C%22name%22%3A%22tcw%22%2C%22tel%22%3A%*************%22%2C%22storeGuid%22%3A%226480756384945597441%22%2C%22storeName%22%3A%22%E6%89%93%E5%8D%B0%E9%97%A8%E5%BA%971218%22%2C%22account%22%3A%********%22%2C%22storeNo%22%3A%*********%22%7D", "description": ""}], "body": {"mode": "raw", "raw": "{\n\t\"invoiceType\": 1,\n\t\"enterpriseGuid\": \"6480755310876715009\",\n\t\"storeGuid\": \"6480756384945597441\",\n\t\"storeName\": \"打印门店1218\",\n\t\"printUid\": \"T6487995551596937217\",\n\t\"operatorStaffGuid\": \"6480756476603191298\",\n\t\"operatorStaffName\": \"tcw\",\n\t\"createTime\": 0,\n\t\"deviceId\": \"6481150069296058369\",\n\t\"printSource\": \"AIO\",\n\t\"itemRecordList\": [{\n\t\t\"itemGuid\": \"6480757087524632577\",\n\t\t\"itemName\": \"分类1单品1\",\n\t\t\"itemTypeGuid\": \"6480756808346515457\",\n\t\t\"itemTypeName\": \"分类1\",\n\t\t\"price\": 0.01,\n\t\t\"number\": 1.0,\n\t\t\"unit\": \"份\",\n\t\t\"subtotal\": 0.01,\n\t\t\"asWeight\": false,\n\t\t\"remark\": \"\",\n\t\t\"subList\": [],\n\t\t\"propertyPrice\": 0,\n\t\t\"packageTotal\": 0.0\n\t}],\n\t\"markName\": \"牌号\",\n\t\"markNo\": \"#6\",\n\t\"orderTime\": *************,\n\t\"orderNo\": \"T6487995551596937217\",\n\t\"personNumber\": 1,\n\t\"remark\": \"\",\n\t\"totalNumber\": 1,\n\t\"tradeMode\": 1\n}"}, "description": "新增打印记录"}, "response": []}]}