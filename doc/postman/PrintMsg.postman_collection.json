{"variables": [], "info": {"name": "PrintMsg", "_postman_id": "dabecdbd-858d-0565-77a4-700759ffea96", "description": "", "schema": "https://schema.getpostman.com/json/collection/v2.0.0/collection.json"}, "item": [{"name": "获取打印数据", "request": {"url": "localhost:8917/print_record/get_order", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json;charset=utf-8", "description": ""}, {"key": "userInfo", "value": "%7B%22userGuid%22%3A%226480756476603191298%22%2C%22enterpriseGuid%22%3A%226490862829263766529%22%2C%22enterpriseName%22%3A%22%E6%89%93%E5%8D%B0%E4%BC%81%E4%B8%9A1218%22%2C%22name%22%3A%22tcw%22%2C%22tel%22%3A%*************%22%2C%22storeGuid%22%3A%226480756384945597441%22%2C%22storeName%22%3A%22%E6%89%93%E5%8D%B0%E9%97%A8%E5%BA%971218%22%2C%22account%22%3A%********%22%2C%22storeNo%22%3A%*********%22%7D", "description": ""}], "body": {"mode": "raw", "raw": "{\n    \"recordGuid\": \"6500667094177153025\"\n}"}, "description": "添加打印机"}, "response": []}, {"name": "菜品清单", "request": {"url": "localhost:8917/print_record/send", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json;charset=utf-8", "description": ""}, {"key": "userInfo", "value": "%7B%22userGuid%22%3A%226480756476603191298%22%2C%22enterpriseGuid%22%3A%226490862829263766529%22%2C%22enterpriseName%22%3A%22%E6%89%93%E5%8D%B0%E4%BC%81%E4%B8%9A1218%22%2C%22name%22%3A%22tcw%22%2C%22tel%22%3A%*************%22%2C%22storeGuid%22%3A%226480756384945597441%22%2C%22storeName%22%3A%22%E6%89%93%E5%8D%B0%E9%97%A8%E5%BA%971218%22%2C%22account%22%3A%********%22%2C%22storeNo%22%3A%*********%22%7D", "description": ""}], "body": {"mode": "raw", "raw": "{\n    \"invoiceType\": 0,\n    \"enterpriseGuid\": \"mock_erp_guid\",\n    \"storeGuid\": \"mock_store_guid\",\n    \"printUid\": \"mock_print_uid\",\n    \"areaGuid\": \"mock_area_guid_1\",\n    \"operatorStaffGuid\": \"mock_staff_guid\",\n    \"operatorStaffName\": \"mock_staff_name\",\n    \"createTime\": *************,\n    \"deviceId\": \"mock_device_id\",\n    \"printSourceEnum\": \"AIO\",\n    \"itemRecordList\": [\n        {\n            \"itemGuid\": \"mock_item_guid\",\n            \"itemName\": \"mock_item_name\",\n            \"itemTypeGuid\": \"mock_item_type_guid\",\n            \"itemTypeName\": \"mock_item_type_name\",\n            \"price\": 0.01,\n            \"number\": 1.00,\n            \"asWeight\": false,\n            \"asPackage\": false,\n            \"asGift\": false,\n            \"property\": \"mock_property\",\n            \"propertyPrice\": 0.01,\n            \"ingredientPrice\": 0.01\n        }\n    ],\n    \"storeName\": \"mock_store_name\",\n    \"markNo\": \"mock_mark_no\",\n    \"orderNo\": \"mock_order_no\",\n    \"personNumber\": 1,\n    \"openTableTime\": *************,\n    \"total\": 0.01\n}"}, "description": "添加打印机"}, "response": []}, {"name": "菜品清单-称重商品-赠送商品", "request": {"url": "localhost:8917/print_record/send", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json;charset=utf-8", "description": ""}, {"key": "userInfo", "value": "%7B%22userGuid%22%3A%226480756476603191298%22%2C%22enterpriseGuid%22%3A%226490862829263766529%22%2C%22enterpriseName%22%3A%22%E6%89%93%E5%8D%B0%E4%BC%81%E4%B8%9A1218%22%2C%22name%22%3A%22tcw%22%2C%22tel%22%3A%*************%22%2C%22storeGuid%22%3A%226480756384945597441%22%2C%22storeName%22%3A%22%E6%89%93%E5%8D%B0%E9%97%A8%E5%BA%971218%22%2C%22account%22%3A%********%22%2C%22storeNo%22%3A%*********%22%7D", "description": ""}], "body": {"mode": "raw", "raw": "{\n    \"invoiceType\": 0,\n    \"enterpriseGuid\": \"mock_erp_guid\",\n    \"storeGuid\": \"mock_store_guid\",\n    \"printUid\": \"mock_print_uid\",\n    \"areaGuid\": \"mock_area_guid_1\",\n    \"operatorStaffGuid\": \"mock_staff_guid\",\n    \"operatorStaffName\": \"mock_staff_name\",\n    \"createTime\": *************,\n    \"deviceId\": \"mock_device_id\",\n    \"printSourceEnum\": \"AIO\",\n    \"itemRecordList\": [\n        {\n            \"itemGuid\": \"mock_item_guid\",\n            \"itemName\": \"mock_item_name\",\n            \"itemTypeGuid\": \"mock_item_type_guid\",\n            \"itemTypeName\": \"mock_item_type_name\",\n            \"price\": 0.01,\n            \"number\": 1.00,\n            \"asWeight\": true,\n            \"asPackage\": false,\n            \"asGift\": true,\n            \"property\": \"mock_property\",\n            \"propertyPrice\": 0.01,\n            \"ingredientPrice\": 0.01\n        }\n    ],\n    \"storeName\": \"mock_store_name\",\n    \"markNo\": \"mock_mark_no\",\n    \"orderNo\": \"mock_order_no\",\n    \"personNumber\": 1,\n    \"openTableTime\": *************,\n    \"total\": 0.01\n}"}, "description": "添加打印机"}, "response": []}, {"name": "菜品清单-区域测试-未匹配到打印机", "request": {"url": "localhost:8917/print_record/send", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json;charset=utf-8", "description": ""}, {"key": "userInfo", "value": "%7B%22userGuid%22%3A%226480756476603191298%22%2C%22enterpriseGuid%22%3A%226490862829263766529%22%2C%22enterpriseName%22%3A%22%E6%89%93%E5%8D%B0%E4%BC%81%E4%B8%9A1218%22%2C%22name%22%3A%22tcw%22%2C%22tel%22%3A%*************%22%2C%22storeGuid%22%3A%226480756384945597441%22%2C%22storeName%22%3A%22%E6%89%93%E5%8D%B0%E9%97%A8%E5%BA%971218%22%2C%22account%22%3A%********%22%2C%22storeNo%22%3A%*********%22%7D", "description": ""}], "body": {"mode": "raw", "raw": "{\n    \"invoiceType\": 0,\n    \"enterpriseGuid\": \"mock_erp_guid\",\n    \"storeGuid\": \"mock_store_guid\",\n    \"printUid\": \"mock_print_uid\",\n    \"areaGuid\": \"mock_area_guid_3\",\n    \"operatorStaffGuid\": \"mock_staff_guid\",\n    \"operatorStaffName\": \"mock_staff_name\",\n    \"createTime\": *************,\n    \"deviceId\": \"mock_device_id\",\n    \"printSourceEnum\": \"AIO\",\n    \"itemRecordList\": [\n        {\n            \"itemGuid\": \"mock_item_guid\",\n            \"itemName\": \"mock_item_name\",\n            \"itemTypeGuid\": \"mock_item_type_guid\",\n            \"itemTypeName\": \"mock_item_type_name\",\n            \"price\": 0.01,\n            \"number\": 1.00,\n            \"asWeight\": false,\n            \"asPackage\": false,\n            \"asGift\": false,\n            \"property\": \"mock_property\",\n            \"propertyPrice\": 0.01,\n            \"ingredientPrice\": 0.01\n        }\n    ],\n    \"storeName\": \"mock_store_name\",\n    \"markNo\": \"mock_mark_no\",\n    \"orderNo\": \"mock_order_no\",\n    \"personNumber\": 1,\n    \"openTableTime\": *************,\n    \"total\": 0.01\n}"}, "description": "添加打印机"}, "response": []}, {"name": "菜品清单-POS测试-SUCCESS", "request": {"url": "localhost:8917/print_record/send", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json;charset=utf-8", "description": ""}, {"key": "userInfo", "value": "%7B%22userGuid%22%3A%226480756476603191298%22%2C%22enterpriseGuid%22%3A%226490862829263766529%22%2C%22enterpriseName%22%3A%22%E6%89%93%E5%8D%B0%E4%BC%81%E4%B8%9A1218%22%2C%22name%22%3A%22tcw%22%2C%22tel%22%3A%*************%22%2C%22storeGuid%22%3A%226480756384945597441%22%2C%22storeName%22%3A%22%E6%89%93%E5%8D%B0%E9%97%A8%E5%BA%971218%22%2C%22account%22%3A%********%22%2C%22storeNo%22%3A%*********%22%7D", "description": ""}], "body": {"mode": "raw", "raw": "{\n    \"invoiceType\": 0,\n    \"enterpriseGuid\": \"mock_erp_guid\",\n    \"storeGuid\": \"mock_store_guid\",\n    \"printUid\": \"mock_print_uid\",\n    \"areaGuid\": \"mock_area_guid_1\",\n    \"operatorStaffGuid\": \"mock_staff_guid\",\n    \"operatorStaffName\": \"mock_staff_name\",\n    \"createTime\": *************,\n    \"deviceId\": \"mock_device_id_2\",\n    \"printSourceEnum\": \"POS\",\n    \"itemRecordList\": [\n        {\n            \"itemGuid\": \"mock_item_guid\",\n            \"itemName\": \"mock_item_name\",\n            \"itemTypeGuid\": \"mock_item_type_guid\",\n            \"itemTypeName\": \"mock_item_type_name\",\n            \"price\": 0.01,\n            \"number\": 1.00,\n            \"asWeight\": false,\n            \"asPackage\": false,\n            \"asGift\": false,\n            \"property\": \"mock_property\",\n            \"propertyPrice\": 0.01,\n            \"ingredientPrice\": 0.01\n        }\n    ],\n    \"storeName\": \"mock_store_name\",\n    \"markNo\": \"mock_mark_no\",\n    \"orderNo\": \"mock_order_no\",\n    \"personNumber\": 1,\n    \"openTableTime\": *************,\n    \"total\": 0.01\n}"}, "description": "添加打印机"}, "response": []}, {"name": "预结单", "request": {"url": "localhost:8917/print_record/send", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json;charset=utf-8", "description": ""}, {"key": "userInfo", "value": "%7B%22userGuid%22%3A%226480756476603191298%22%2C%22enterpriseGuid%22%3A%226490862829263766529%22%2C%22enterpriseName%22%3A%22%E6%89%93%E5%8D%B0%E4%BC%81%E4%B8%9A1218%22%2C%22name%22%3A%22tcw%22%2C%22tel%22%3A%*************%22%2C%22storeGuid%22%3A%226480756384945597441%22%2C%22storeName%22%3A%22%E6%89%93%E5%8D%B0%E9%97%A8%E5%BA%971218%22%2C%22account%22%3A%********%22%2C%22storeNo%22%3A%*********%22%7D", "description": ""}], "body": {"mode": "raw", "raw": "{\n    \"invoiceType\": 5,\n    \"enterpriseGuid\": \"mock_erp_guid\",\n    \"storeGuid\": \"mock_store_guid\",\n    \"printUid\": \"mock_print_uid\",\n    \"areaGuid\": \"mock_area_guid_1\",\n    \"operatorStaffGuid\": \"mock_staff_guid\",\n    \"operatorStaffName\": \"mock_staff_name\",\n    \"createTime\": *************,\n    \"deviceId\": \"mock_device_id\",\n    \"printSourceEnum\": \"AIO\",\n    \"itemRecordList\": [\n        {\n            \"itemGuid\": \"mock_item_guid\",\n            \"itemName\": \"mock_item_name\",\n            \"itemTypeGuid\": \"mock_item_type_guid\",\n            \"itemTypeName\": \"mock_item_type_name\",\n            \"price\": 0.01,\n            \"number\": 1.00,\n            \"asWeight\": false,\n            \"asPackage\": false,\n            \"asGift\": false,\n            \"property\": \"mock_property\",\n            \"propertyPrice\": 0.01,\n            \"ingredientPrice\": 0.01\n        }\n    ],\n    \"storeName\": \"mock_store_name\",\n    \"markNo\": \"mock_mark_no\",\n    \"orderNo\": \"mock_order_no\",\n    \"personNumber\": 1,\n    \"openTableTime\": *************,\n    \"total\": 0.01,\n    \"additionalChargeList\": [\n    \t{\n    \t\t\"chargeName\": \"mock_charge_name\",\n    \t\t\"chargeValue\": 0.01\n    \t}\n    ],\n    \"reduceRecordList\": [\n    \t{\n    \t\t\"name\": \"mock_reduce_name\",\n    \t\t\"amount\": 0.01\n    \t}\n    ],\n    \"payAble\": 0.01,\n    \"storeAddress\": \"mock_address\",\n    \"tel\": \"mock_tel\"\n}"}, "description": "添加打印机"}, "response": []}, {"name": "预结单-区域测试-未匹配到打印机", "request": {"url": "localhost:8917/print_record/send", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json;charset=utf-8", "description": ""}, {"key": "userInfo", "value": "%7B%22userGuid%22%3A%226480756476603191298%22%2C%22enterpriseGuid%22%3A%226490862829263766529%22%2C%22enterpriseName%22%3A%22%E6%89%93%E5%8D%B0%E4%BC%81%E4%B8%9A1218%22%2C%22name%22%3A%22tcw%22%2C%22tel%22%3A%*************%22%2C%22storeGuid%22%3A%226480756384945597441%22%2C%22storeName%22%3A%22%E6%89%93%E5%8D%B0%E9%97%A8%E5%BA%971218%22%2C%22account%22%3A%********%22%2C%22storeNo%22%3A%*********%22%7D", "description": ""}], "body": {"mode": "raw", "raw": "{\n    \"invoiceType\": 5,\n    \"enterpriseGuid\": \"mock_erp_guid\",\n    \"storeGuid\": \"mock_store_guid\",\n    \"printUid\": \"mock_print_uid\",\n    \"areaGuid\": \"mock_area_guid_3\",\n    \"operatorStaffGuid\": \"mock_staff_guid\",\n    \"operatorStaffName\": \"mock_staff_name\",\n    \"createTime\": *************,\n    \"deviceId\": \"mock_device_id\",\n    \"printSourceEnum\": \"AIO\",\n    \"itemRecordList\": [\n        {\n            \"itemGuid\": \"mock_item_guid\",\n            \"itemName\": \"mock_item_name\",\n            \"itemTypeGuid\": \"mock_item_type_guid\",\n            \"itemTypeName\": \"mock_item_type_name\",\n            \"price\": 0.01,\n            \"number\": 1.00,\n            \"asWeight\": false,\n            \"asPackage\": false,\n            \"asGift\": false,\n            \"property\": \"mock_property\",\n            \"propertyPrice\": 0.01,\n            \"ingredientPrice\": 0.01\n        }\n    ],\n    \"storeName\": \"mock_store_name\",\n    \"markNo\": \"mock_mark_no\",\n    \"orderNo\": \"mock_order_no\",\n    \"personNumber\": 1,\n    \"openTableTime\": *************,\n    \"total\": 0.01,\n    \"additionalChargeList\": [\n    \t{\n    \t\t\"chargeName\": \"mock_charge_name\",\n    \t\t\"chargeValue\": 0.01\n    \t}\n    ],\n    \"reduceRecordList\": [\n    \t{\n    \t\t\"name\": \"mock_reduce_name\",\n    \t\t\"amount\": 0.01\n    \t}\n    ],\n    \"payAble\": 0.01,\n    \"storeAddress\": \"mock_address\",\n    \"tel\": \"mock_tel\"\n}"}, "description": "添加打印机"}, "response": []}, {"name": "结帐单", "request": {"url": "localhost:8917/print_record/send", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json;charset=utf-8", "description": ""}, {"key": "userInfo", "value": "%7B%22userGuid%22%3A%226480756476603191298%22%2C%22enterpriseGuid%22%3A%226490862829263766529%22%2C%22enterpriseName%22%3A%22%E6%89%93%E5%8D%B0%E4%BC%81%E4%B8%9A1218%22%2C%22name%22%3A%22tcw%22%2C%22tel%22%3A%*************%22%2C%22storeGuid%22%3A%226480756384945597441%22%2C%22storeName%22%3A%22%E6%89%93%E5%8D%B0%E9%97%A8%E5%BA%971218%22%2C%22account%22%3A%********%22%2C%22storeNo%22%3A%*********%22%7D", "description": ""}], "body": {"mode": "raw", "raw": "{\n    \"invoiceType\": 6,\n    \"enterpriseGuid\": \"mock_erp_guid\",\n    \"storeGuid\": \"mock_store_guid\",\n    \"printUid\": \"mock_print_uid\",\n    \"areaGuid\": \"mock_area_guid_1\",\n    \"operatorStaffGuid\": \"mock_staff_guid\",\n    \"operatorStaffName\": \"mock_staff_name\",\n    \"createTime\": *************,\n    \"deviceId\": \"mock_device_id\",\n    \"printSourceEnum\": \"AIO\",\n    \"itemRecordList\": [\n        {\n            \"itemGuid\": \"mock_item_guid\",\n            \"itemName\": \"mock_item_name\",\n            \"itemTypeGuid\": \"mock_item_type_guid\",\n            \"itemTypeName\": \"mock_item_type_name\",\n            \"price\": 0.01,\n            \"number\": 1.00,\n            \"asWeight\": false,\n            \"asPackage\": false,\n            \"asGift\": false,\n            \"property\": \"mock_property\",\n            \"propertyPrice\": 0.01,\n            \"ingredientPrice\": 0.01\n        }\n    ],\n    \"storeName\": \"mock_store_name\",\n    \"markNo\": \"mock_mark_no\",\n    \"orderNo\": \"mock_order_no\",\n    \"personNumber\": 1,\n    \"openTableTime\": *************,\n    \"total\": 0.01,\n    \"additionalChargeList\": [\n    \t{\n    \t\t\"chargeName\": \"mock_charge_name\",\n    \t\t\"chargeValue\": 0.01\n    \t}\n    ],\n    \"reduceRecordList\": [\n    \t{\n    \t\t\"name\": \"mock_reduce_name\",\n    \t\t\"amount\": 0.01\n    \t}\n    ],\n    \"payAble\": 0.01,\n    \"storeAddress\": \"mock_address\",\n    \"tel\": \"mock_tel\",\n    \"checkOutTime\": *************,\n    \"actuallyPay\": 0.01,\n    \"payRecordList\": [\n    \t{\n    \t\t\"payName\": \"mock_pay_name\",\n    \t\t\"amount\": 0.01\n    \t}\n    ],\n    \"tradeMode\": 0\n}"}, "description": "添加打印机"}, "response": []}, {"name": "结帐单-区域测试-未匹配到打印机", "request": {"url": "localhost:8917/print_record/send", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json;charset=utf-8", "description": ""}, {"key": "userInfo", "value": "%7B%22userGuid%22%3A%226480756476603191298%22%2C%22enterpriseGuid%22%3A%226490862829263766529%22%2C%22enterpriseName%22%3A%22%E6%89%93%E5%8D%B0%E4%BC%81%E4%B8%9A1218%22%2C%22name%22%3A%22tcw%22%2C%22tel%22%3A%*************%22%2C%22storeGuid%22%3A%226480756384945597441%22%2C%22storeName%22%3A%22%E6%89%93%E5%8D%B0%E9%97%A8%E5%BA%971218%22%2C%22account%22%3A%********%22%2C%22storeNo%22%3A%*********%22%7D", "description": ""}], "body": {"mode": "raw", "raw": "{\n    \"invoiceType\": 6,\n    \"enterpriseGuid\": \"mock_erp_guid\",\n    \"storeGuid\": \"mock_store_guid\",\n    \"printUid\": \"mock_print_uid\",\n    \"areaGuid\": \"mock_area_guid_3\",\n    \"operatorStaffGuid\": \"mock_staff_guid\",\n    \"operatorStaffName\": \"mock_staff_name\",\n    \"createTime\": *************,\n    \"deviceId\": \"mock_device_id\",\n    \"printSourceEnum\": \"AIO\",\n    \"itemRecordList\": [\n        {\n            \"itemGuid\": \"mock_item_guid\",\n            \"itemName\": \"mock_item_name\",\n            \"itemTypeGuid\": \"mock_item_type_guid\",\n            \"itemTypeName\": \"mock_item_type_name\",\n            \"price\": 0.01,\n            \"number\": 1.00,\n            \"asWeight\": false,\n            \"asPackage\": false,\n            \"asGift\": false,\n            \"property\": \"mock_property\",\n            \"propertyPrice\": 0.01,\n            \"ingredientPrice\": 0.01\n        }\n    ],\n    \"storeName\": \"mock_store_name\",\n    \"markNo\": \"mock_mark_no\",\n    \"orderNo\": \"mock_order_no\",\n    \"personNumber\": 1,\n    \"openTableTime\": *************,\n    \"total\": 0.01,\n    \"additionalChargeList\": [\n    \t{\n    \t\t\"chargeName\": \"mock_charge_name\",\n    \t\t\"chargeValue\": 0.01\n    \t}\n    ],\n    \"reduceRecordList\": [\n    \t{\n    \t\t\"name\": \"mock_reduce_name\",\n    \t\t\"amount\": 0.01\n    \t}\n    ],\n    \"payAble\": 0.01,\n    \"storeAddress\": \"mock_address\",\n    \"tel\": \"mock_tel\",\n    \"checkOutTime\": *************,\n    \"actuallyPay\": 0.01,\n    \"payRecordList\": [\n    \t{\n    \t\t\"payName\": \"mock_pay_name\",\n    \t\t\"amount\": 0.01\n    \t}\n    ],\n    \"tradeMode\": 0\n}"}, "description": "添加打印机"}, "response": []}, {"name": "并台结帐单", "request": {"url": "localhost:8917/print_record/send", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json;charset=utf-8", "description": ""}, {"key": "userInfo", "value": "%7B%22userGuid%22%3A%226480756476603191298%22%2C%22enterpriseGuid%22%3A%226490862829263766529%22%2C%22enterpriseName%22%3A%22%E6%89%93%E5%8D%B0%E4%BC%81%E4%B8%9A1218%22%2C%22name%22%3A%22tcw%22%2C%22tel%22%3A%*************%22%2C%22storeGuid%22%3A%226480756384945597441%22%2C%22storeName%22%3A%22%E6%89%93%E5%8D%B0%E9%97%A8%E5%BA%971218%22%2C%22account%22%3A%********%22%2C%22storeNo%22%3A%*********%22%7D", "description": ""}], "body": {"mode": "raw", "raw": "{\n    \"invoiceType\": 7,\n    \"enterpriseGuid\": \"mock_erp_guid\",\n    \"storeGuid\": \"mock_store_guid\",\n    \"printUid\": \"mock_print_uid\",\n    \"areaGuid\": \"mock_area_guid_1\",\n    \"operatorStaffGuid\": \"mock_staff_guid\",\n    \"operatorStaffName\": \"mock_staff_name\",\n    \"createTime\": *************,\n    \"deviceId\": \"mock_device_id\",\n    \"printSourceEnum\": \"AIO\",\n    \"storeName\": \"mock_store_name\",\n    \"tableOrderList\": [\n    \t{\n\t\t    \"itemRecordList\": [\n\t\t        {\n\t\t            \"itemGuid\": \"mock_item_guid\",\n\t\t            \"itemName\": \"mock_item_name\",\n\t\t            \"itemTypeGuid\": \"mock_item_type_guid\",\n\t\t            \"itemTypeName\": \"mock_item_type_name\",\n\t\t            \"price\": 0.01,\n\t\t            \"number\": 1.00,\n\t\t            \"asWeight\": false,\n\t\t            \"asPackage\": false,\n\t\t            \"asGift\": false,\n\t\t            \"property\": \"mock_property\",\n\t\t            \"propertyPrice\": 0.01,\n\t\t            \"ingredientPrice\": 0.01\n\t\t        }\n\t\t    ],\n\t\t    \"markNo\": \"mock_mark_no\",\n\t\t    \"orderNo\": \"mock_order_no\",\n\t\t    \"personNumber\": 1,\n\t\t    \"openTableTime\": *************,\n\t\t    \"total\": 0.01,\n\t\t    \"checkOutTime\": *************\n    \t},\n    \t{\n\t\t    \"itemRecordList\": [\n\t\t        {\n\t\t            \"itemGuid\": \"mock_item_guid\",\n\t\t            \"itemName\": \"mock_item_name\",\n\t\t            \"itemTypeGuid\": \"mock_item_type_guid\",\n\t\t            \"itemTypeName\": \"mock_item_type_name\",\n\t\t            \"price\": 0.01,\n\t\t            \"number\": 1.00,\n\t\t            \"asWeight\": false,\n\t\t            \"asPackage\": false,\n\t\t            \"asGift\": false,\n\t\t            \"property\": \"mock_property\",\n\t\t            \"propertyPrice\": 0.01,\n\t\t            \"ingredientPrice\": 0.01\n\t\t        }\n\t\t    ],\n\t\t    \"markNo\": \"mock_mark_no\",\n\t\t    \"orderNo\": \"mock_order_no\",\n\t\t    \"personNumber\": 1,\n\t\t    \"openTableTime\": *************,\n\t\t    \"total\": 0.01,\n\t\t    \"checkOutTime\": *************\n    \t}\n    ],\n    \"tableTotal\": 0.02,\n    \"additionalChargeList\": [\n    \t{\n    \t\t\"chargeName\": \"mock_charge_name\",\n    \t\t\"chargeValue\": 0.01\n    \t}\n    ],\n    \"reduceRecordList\": [\n    \t{\n    \t\t\"name\": \"mock_reduce_name\",\n    \t\t\"amount\": 0.01\n    \t}\n    ],\n    \"payAble\": 0.01,\n    \"actuallyPay\": 0.01,\n    \"payRecordList\": [\n    \t{\n    \t\t\"payName\": \"mock_pay_name\",\n    \t\t\"amount\": 0.01\n    \t}\n    ],\n    \"storeAddress\": \"mock_address\",\n    \"tel\": \"mock_tel\"\n}"}, "description": "添加打印机"}, "response": []}, {"name": "并台结帐单-区域测试-未匹配到打印机", "request": {"url": "localhost:8917/print_record/send", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json;charset=utf-8", "description": ""}, {"key": "userInfo", "value": "%7B%22userGuid%22%3A%226480756476603191298%22%2C%22enterpriseGuid%22%3A%226490862829263766529%22%2C%22enterpriseName%22%3A%22%E6%89%93%E5%8D%B0%E4%BC%81%E4%B8%9A1218%22%2C%22name%22%3A%22tcw%22%2C%22tel%22%3A%*************%22%2C%22storeGuid%22%3A%226480756384945597441%22%2C%22storeName%22%3A%22%E6%89%93%E5%8D%B0%E9%97%A8%E5%BA%971218%22%2C%22account%22%3A%********%22%2C%22storeNo%22%3A%*********%22%7D", "description": ""}], "body": {"mode": "raw", "raw": "{\n    \"invoiceType\": 7,\n    \"enterpriseGuid\": \"mock_erp_guid\",\n    \"storeGuid\": \"mock_store_guid\",\n    \"printUid\": \"mock_print_uid\",\n    \"areaGuid\": \"mock_area_guid_3\",\n    \"operatorStaffGuid\": \"mock_staff_guid\",\n    \"operatorStaffName\": \"mock_staff_name\",\n    \"createTime\": *************,\n    \"deviceId\": \"mock_device_id\",\n    \"printSourceEnum\": \"AIO\",\n    \"storeName\": \"mock_store_name\",\n    \"tableOrderList\": [\n    \t{\n\t\t    \"itemRecordList\": [\n\t\t        {\n\t\t            \"itemGuid\": \"mock_item_guid\",\n\t\t            \"itemName\": \"mock_item_name\",\n\t\t            \"itemTypeGuid\": \"mock_item_type_guid\",\n\t\t            \"itemTypeName\": \"mock_item_type_name\",\n\t\t            \"price\": 0.01,\n\t\t            \"number\": 1.00,\n\t\t            \"asWeight\": false,\n\t\t            \"asPackage\": false,\n\t\t            \"asGift\": false,\n\t\t            \"property\": \"mock_property\",\n\t\t            \"propertyPrice\": 0.01,\n\t\t            \"ingredientPrice\": 0.01\n\t\t        }\n\t\t    ],\n\t\t    \"markNo\": \"mock_mark_no\",\n\t\t    \"orderNo\": \"mock_order_no\",\n\t\t    \"personNumber\": 1,\n\t\t    \"openTableTime\": *************,\n\t\t    \"total\": 0.01,\n\t\t    \"checkOutTime\": *************\n    \t},\n    \t{\n\t\t    \"itemRecordList\": [\n\t\t        {\n\t\t            \"itemGuid\": \"mock_item_guid\",\n\t\t            \"itemName\": \"mock_item_name\",\n\t\t            \"itemTypeGuid\": \"mock_item_type_guid\",\n\t\t            \"itemTypeName\": \"mock_item_type_name\",\n\t\t            \"price\": 0.01,\n\t\t            \"number\": 1.00,\n\t\t            \"asWeight\": false,\n\t\t            \"asPackage\": false,\n\t\t            \"asGift\": false,\n\t\t            \"property\": \"mock_property\",\n\t\t            \"propertyPrice\": 0.01,\n\t\t            \"ingredientPrice\": 0.01\n\t\t        }\n\t\t    ],\n\t\t    \"markNo\": \"mock_mark_no\",\n\t\t    \"orderNo\": \"mock_order_no\",\n\t\t    \"personNumber\": 1,\n\t\t    \"openTableTime\": *************,\n\t\t    \"total\": 0.01,\n\t\t    \"checkOutTime\": *************\n    \t}\n    ],\n    \"tableTotal\": 0.02,\n    \"additionalChargeList\": [\n    \t{\n    \t\t\"chargeName\": \"mock_charge_name\",\n    \t\t\"chargeValue\": 0.01\n    \t}\n    ],\n    \"reduceRecordList\": [\n    \t{\n    \t\t\"name\": \"mock_reduce_name\",\n    \t\t\"amount\": 0.01\n    \t}\n    ],\n    \"payAble\": 0.01,\n    \"actuallyPay\": 0.01,\n    \"payRecordList\": [\n    \t{\n    \t\t\"payName\": \"mock_pay_name\",\n    \t\t\"amount\": 0.01\n    \t}\n    ],\n    \"storeAddress\": \"mock_address\",\n    \"tel\": \"mock_tel\"\n}"}, "description": "添加打印机"}, "response": []}, {"name": "储值单", "request": {"url": "localhost:8917/print_record/send", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json;charset=utf-8", "description": ""}, {"key": "userInfo", "value": "%7B%22userGuid%22%3A%226480756476603191298%22%2C%22enterpriseGuid%22%3A%226490862829263766529%22%2C%22enterpriseName%22%3A%22%E6%89%93%E5%8D%B0%E4%BC%81%E4%B8%9A1218%22%2C%22name%22%3A%22tcw%22%2C%22tel%22%3A%*************%22%2C%22storeGuid%22%3A%226480756384945597441%22%2C%22storeName%22%3A%22%E6%89%93%E5%8D%B0%E9%97%A8%E5%BA%971218%22%2C%22account%22%3A%********%22%2C%22storeNo%22%3A%*********%22%7D", "description": ""}], "body": {"mode": "raw", "raw": "{\n    \"invoiceType\": 10,\n    \"enterpriseGuid\": \"mock_erp_guid\",\n    \"storeGuid\": \"mock_store_guid\",\n    \"printUid\": \"mock_print_uid\",\n    \"areaGuid\": \"mock_area_guid_1\",\n    \"operatorStaffGuid\": \"mock_staff_guid\",\n    \"operatorStaffName\": \"mock_staff_name\",\n    \"createTime\": *************,\n    \"deviceId\": \"mock_device_id\",\n    \"printSourceEnum\": \"AIO\",\n    \"storeName\": \"mock_store_name\",\n    \"serialNumber\": \"mock_serial_number\",\n    \"recharge\": 0.01,\n    \"presented\": 0.00,\n    \"arrival\": 0.01,\n    \"payWay\": \"现金\",\n    \"cardNo\": \"mock_card_no\",\n    \"currentCash\": 0.01,\n    \"integration\": \"1\",\n    \"rechargeTime\": *************,\n    \"storeAddress\": \"mock_address\",\n    \"tel\": \"mock_tel\"\n}"}, "description": "添加打印机"}, "response": []}, {"name": "储值单-区域测试-SUCCESS", "request": {"url": "localhost:8917/print_record/send", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json;charset=utf-8", "description": ""}, {"key": "userInfo", "value": "%7B%22userGuid%22%3A%226480756476603191298%22%2C%22enterpriseGuid%22%3A%226490862829263766529%22%2C%22enterpriseName%22%3A%22%E6%89%93%E5%8D%B0%E4%BC%81%E4%B8%9A1218%22%2C%22name%22%3A%22tcw%22%2C%22tel%22%3A%*************%22%2C%22storeGuid%22%3A%226480756384945597441%22%2C%22storeName%22%3A%22%E6%89%93%E5%8D%B0%E9%97%A8%E5%BA%971218%22%2C%22account%22%3A%********%22%2C%22storeNo%22%3A%*********%22%7D", "description": ""}], "body": {"mode": "raw", "raw": "{\n    \"invoiceType\": 10,\n    \"enterpriseGuid\": \"mock_erp_guid\",\n    \"storeGuid\": \"mock_store_guid\",\n    \"printUid\": \"mock_print_uid\",\n    \"areaGuid\": \"mock_area_guid_3\",\n    \"operatorStaffGuid\": \"mock_staff_guid\",\n    \"operatorStaffName\": \"mock_staff_name\",\n    \"createTime\": *************,\n    \"deviceId\": \"mock_device_id\",\n    \"printSourceEnum\": \"AIO\",\n    \"storeName\": \"mock_store_name\",\n    \"serialNumber\": \"mock_serial_number\",\n    \"recharge\": 0.01,\n    \"presented\": 0.00,\n    \"arrival\": 0.01,\n    \"payWay\": \"现金\",\n    \"cardNo\": \"mock_card_no\",\n    \"currentCash\": 0.01,\n    \"integration\": \"1\",\n    \"rechargeTime\": *************,\n    \"storeAddress\": \"mock_address\",\n    \"tel\": \"mock_tel\"\n}"}, "description": "添加打印机"}, "response": []}, {"name": "转台单", "request": {"url": "localhost:8917/print_record/send", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json;charset=utf-8", "description": ""}, {"key": "userInfo", "value": "%7B%22userGuid%22%3A%226480756476603191298%22%2C%22enterpriseGuid%22%3A%226490862829263766529%22%2C%22enterpriseName%22%3A%22%E6%89%93%E5%8D%B0%E4%BC%81%E4%B8%9A1218%22%2C%22name%22%3A%22tcw%22%2C%22tel%22%3A%*************%22%2C%22storeGuid%22%3A%226480756384945597441%22%2C%22storeName%22%3A%22%E6%89%93%E5%8D%B0%E9%97%A8%E5%BA%971218%22%2C%22account%22%3A%********%22%2C%22storeNo%22%3A%*********%22%7D", "description": ""}], "body": {"mode": "raw", "raw": "{\n    \"invoiceType\": 15,\n    \"enterpriseGuid\": \"mock_erp_guid\",\n    \"storeGuid\": \"mock_store_guid\",\n    \"printUid\": \"mock_print_uid\",\n    \"areaGuid\": \"mock_area_guid_1\",\n    \"operatorStaffGuid\": \"mock_staff_guid\",\n    \"operatorStaffName\": \"mock_staff_name\",\n    \"createTime\": *************,\n    \"deviceId\": \"mock_device_id\",\n    \"printSourceEnum\": \"AIO\",\n    \"storeName\": \"mock_store_name\",\n    \"srcTableName\": \"mock_src_name\",\n    \"destTableName\": \"mock_dest_name\",\n    \"turnTime\": *************\n}"}, "description": "添加打印机"}, "response": []}, {"name": "转台单-区域测试-未匹配到打印机", "request": {"url": "localhost:8917/print_record/send", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json;charset=utf-8", "description": ""}, {"key": "userInfo", "value": "%7B%22userGuid%22%3A%226480756476603191298%22%2C%22enterpriseGuid%22%3A%226490862829263766529%22%2C%22enterpriseName%22%3A%22%E6%89%93%E5%8D%B0%E4%BC%81%E4%B8%9A1218%22%2C%22name%22%3A%22tcw%22%2C%22tel%22%3A%*************%22%2C%22storeGuid%22%3A%226480756384945597441%22%2C%22storeName%22%3A%22%E6%89%93%E5%8D%B0%E9%97%A8%E5%BA%971218%22%2C%22account%22%3A%********%22%2C%22storeNo%22%3A%*********%22%7D", "description": ""}], "body": {"mode": "raw", "raw": "{\n    \"invoiceType\": 15,\n    \"enterpriseGuid\": \"mock_erp_guid\",\n    \"storeGuid\": \"mock_store_guid\",\n    \"printUid\": \"mock_print_uid\",\n    \"areaGuid\": \"mock_area_guid_3\",\n    \"operatorStaffGuid\": \"mock_staff_guid\",\n    \"operatorStaffName\": \"mock_staff_name\",\n    \"createTime\": *************,\n    \"deviceId\": \"mock_device_id\",\n    \"printSourceEnum\": \"AIO\",\n    \"storeName\": \"mock_store_name\",\n    \"srcTableName\": \"mock_src_name\",\n    \"destTableName\": \"mock_dest_name\",\n    \"turnTime\": *************\n}"}, "description": "添加打印机"}, "response": []}, {"name": "外卖单", "request": {"url": "localhost:8917/print_record/send", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json;charset=utf-8", "description": ""}, {"key": "userInfo", "value": "%7B%22userGuid%22%3A%226480756476603191298%22%2C%22enterpriseGuid%22%3A%226490862829263766529%22%2C%22enterpriseName%22%3A%22%E6%89%93%E5%8D%B0%E4%BC%81%E4%B8%9A1218%22%2C%22name%22%3A%22tcw%22%2C%22tel%22%3A%*************%22%2C%22storeGuid%22%3A%226480756384945597441%22%2C%22storeName%22%3A%22%E6%89%93%E5%8D%B0%E9%97%A8%E5%BA%971218%22%2C%22account%22%3A%********%22%2C%22storeNo%22%3A%*********%22%7D", "description": ""}], "body": {"mode": "raw", "raw": "{\n    \"invoiceType\": 20,\n    \"enterpriseGuid\": \"mock_erp_guid\",\n    \"storeGuid\": \"mock_store_guid\",\n    \"printUid\": \"mock_print_uid\",\n    \"areaGuid\": \"mock_area_guid_1\",\n    \"operatorStaffGuid\": \"mock_staff_guid\",\n    \"operatorStaffName\": \"mock_staff_name\",\n    \"createTime\": *************,\n    \"deviceId\": \"mock_device_id\",\n    \"printSourceEnum\": \"AIO\",\n    \"itemRecordList\": [\n        {\n            \"itemGuid\": \"mock_item_guid\",\n            \"itemName\": \"mock_item_name\",\n            \"itemTypeGuid\": \"mock_item_type_guid\",\n            \"itemTypeName\": \"mock_item_type_name\",\n            \"price\": 0.01,\n            \"number\": 1.00,\n            \"asWeight\": false,\n            \"asPackage\": false,\n            \"asGift\": false,\n            \"property\": \"mock_property\",\n            \"propertyPrice\": 0.01,\n            \"ingredientPrice\": 0.01\n        }\n    ],\n    \"storeName\": \"mock_store_name\",\n    \"platform\": \"mock_platform\",\n    \"platformOrder\": \"mock_p_order\",\n    \"payMsg\": \"已在线支付\",\n    \"expectTime\": \"立即送达\",\n    \"orderTime\": *************,\n    \"orderNo\": \"mock_order_no\",\n    \"itemTotalPrice\": 0.01,\n        \"additionalChargeList\": [\n    \t{\n    \t\t\"chargeName\": \"mock_charge_name\",\n    \t\t\"chargeValue\": 0.01\n    \t}\n    ],\n    \"reduceRecordList\": [\n    \t{\n    \t\t\"name\": \"mock_reduce_name\",\n    \t\t\"amount\": 0.01\n    \t}\n    ],\n    \"originalPrice\": 0.01,\n    \"actuallyPay\": 0.01,\n    \"receiverName\": \"mock_name\",\n    \"receiverTel\": \"mock_tel\",\n    \"receiverAddress\": \"mock_address\",\n    \"abnormal\": false\n}"}, "description": "添加打印机"}, "response": []}, {"name": "外卖单-区域测试-SUCCESS", "request": {"url": "localhost:8917/print_record/send", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json;charset=utf-8", "description": ""}, {"key": "userInfo", "value": "%7B%22userGuid%22%3A%226480756476603191298%22%2C%22enterpriseGuid%22%3A%226490862829263766529%22%2C%22enterpriseName%22%3A%22%E6%89%93%E5%8D%B0%E4%BC%81%E4%B8%9A1218%22%2C%22name%22%3A%22tcw%22%2C%22tel%22%3A%*************%22%2C%22storeGuid%22%3A%226480756384945597441%22%2C%22storeName%22%3A%22%E6%89%93%E5%8D%B0%E9%97%A8%E5%BA%971218%22%2C%22account%22%3A%********%22%2C%22storeNo%22%3A%*********%22%7D", "description": ""}], "body": {"mode": "raw", "raw": "{\n    \"invoiceType\": 20,\n    \"enterpriseGuid\": \"mock_erp_guid\",\n    \"storeGuid\": \"mock_store_guid\",\n    \"printUid\": \"mock_print_uid\",\n    \"areaGuid\": \"mock_area_guid_3\",\n    \"operatorStaffGuid\": \"mock_staff_guid\",\n    \"operatorStaffName\": \"mock_staff_name\",\n    \"createTime\": *************,\n    \"deviceId\": \"mock_device_id\",\n    \"printSourceEnum\": \"AIO\",\n    \"itemRecordList\": [\n        {\n            \"itemGuid\": \"mock_item_guid\",\n            \"itemName\": \"mock_item_name\",\n            \"itemTypeGuid\": \"mock_item_type_guid\",\n            \"itemTypeName\": \"mock_item_type_name\",\n            \"price\": 0.01,\n            \"number\": 1.00,\n            \"asWeight\": false,\n            \"asPackage\": false,\n            \"asGift\": false,\n            \"property\": \"mock_property\",\n            \"propertyPrice\": 0.01,\n            \"ingredientPrice\": 0.01\n        }\n    ],\n    \"storeName\": \"mock_store_name\",\n    \"platform\": \"mock_platform\",\n    \"platformOrder\": \"mock_p_order\",\n    \"payMsg\": \"已在线支付\",\n    \"expectTime\": \"立即送达\",\n    \"orderTime\": *************,\n    \"orderNo\": \"mock_order_no\",\n    \"itemTotalPrice\": 0.01,\n        \"additionalChargeList\": [\n    \t{\n    \t\t\"chargeName\": \"mock_charge_name\",\n    \t\t\"chargeValue\": 0.01\n    \t}\n    ],\n    \"reduceRecordList\": [\n    \t{\n    \t\t\"name\": \"mock_reduce_name\",\n    \t\t\"amount\": 0.01\n    \t}\n    ],\n    \"originalPrice\": 0.01,\n    \"actuallyPay\": 0.01,\n    \"receiverName\": \"mock_name\",\n    \"receiverTel\": \"mock_tel\",\n    \"receiverAddress\": \"mock_address\",\n    \"abnormal\": false\n}"}, "description": "添加打印机"}, "response": []}, {"name": "交接单", "request": {"url": "localhost:8917/print_record/send", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json;charset=utf-8", "description": ""}, {"key": "userInfo", "value": "%7B%22userGuid%22%3A%226480756476603191298%22%2C%22enterpriseGuid%22%3A%226490862829263766529%22%2C%22enterpriseName%22%3A%22%E6%89%93%E5%8D%B0%E4%BC%81%E4%B8%9A1218%22%2C%22name%22%3A%22tcw%22%2C%22tel%22%3A%*************%22%2C%22storeGuid%22%3A%226480756384945597441%22%2C%22storeName%22%3A%22%E6%89%93%E5%8D%B0%E9%97%A8%E5%BA%971218%22%2C%22account%22%3A%********%22%2C%22storeNo%22%3A%*********%22%7D", "description": ""}], "body": {"mode": "raw", "raw": "{\n    \"invoiceType\": 25,\n    \"enterpriseGuid\": \"mock_erp_guid\",\n    \"storeGuid\": \"mock_store_guid\",\n    \"printUid\": \"mock_print_uid\",\n    \"areaGuid\": \"mock_area_guid_1\",\n    \"operatorStaffGuid\": \"mock_staff_guid\",\n    \"operatorStaffName\": \"mock_staff_name\",\n    \"createTime\": *************,\n    \"deviceId\": \"mock_device_id\",\n    \"printSourceEnum\": \"AIO\",\n    \"storeName\": \"mock_store_name\",\n    \"payRecordList\": [\n    \t{\n    \t\t\"payName\": \"mock_pay_name\",\n    \t\t\"amount\": 0.01\n    \t}\n    ],\n    \"staffName\": \"mock_name\",\n    \"duration\": \"4h\",\n    \"beginTime\": \"08:00\",\n    \"overTime\": \"20:00\",\n    \"dutyAmount\": 0.01,\n    \"saleIncome\": 0.01,\n    \"rechargeIncome\": 0.01\n}"}, "description": "添加打印机"}, "response": []}, {"name": "交接单-区域测试-SUCCESS", "request": {"url": "localhost:8917/print_record/send", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json;charset=utf-8", "description": ""}, {"key": "userInfo", "value": "%7B%22userGuid%22%3A%226480756476603191298%22%2C%22enterpriseGuid%22%3A%226490862829263766529%22%2C%22enterpriseName%22%3A%22%E6%89%93%E5%8D%B0%E4%BC%81%E4%B8%9A1218%22%2C%22name%22%3A%22tcw%22%2C%22tel%22%3A%*************%22%2C%22storeGuid%22%3A%226480756384945597441%22%2C%22storeName%22%3A%22%E6%89%93%E5%8D%B0%E9%97%A8%E5%BA%971218%22%2C%22account%22%3A%********%22%2C%22storeNo%22%3A%*********%22%7D", "description": ""}], "body": {"mode": "raw", "raw": "{\n    \"invoiceType\": 25,\n    \"enterpriseGuid\": \"mock_erp_guid\",\n    \"storeGuid\": \"mock_store_guid\",\n    \"printUid\": \"mock_print_uid\",\n    \"areaGuid\": \"mock_area_guid_3\",\n    \"operatorStaffGuid\": \"mock_staff_guid\",\n    \"operatorStaffName\": \"mock_staff_name\",\n    \"createTime\": *************,\n    \"deviceId\": \"mock_device_id\",\n    \"printSourceEnum\": \"AIO\",\n    \"storeName\": \"mock_store_name\",\n    \"payRecordList\": [\n    \t{\n    \t\t\"payName\": \"mock_pay_name\",\n    \t\t\"amount\": 0.01\n    \t}\n    ],\n    \"staffName\": \"mock_name\",\n    \"duration\": \"4h\",\n    \"beginTime\": \"08:00\",\n    \"overTime\": \"20:00\",\n    \"dutyAmount\": 0.01,\n    \"saleIncome\": 0.01,\n    \"rechargeIncome\": 0.01\n}"}, "description": "添加打印机"}, "response": []}, {"name": "营业概况单", "request": {"url": "localhost:8917/print_record/send", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json;charset=utf-8", "description": ""}, {"key": "userInfo", "value": "%7B%22userGuid%22%3A%226480756476603191298%22%2C%22enterpriseGuid%22%3A%226490862829263766529%22%2C%22enterpriseName%22%3A%22%E6%89%93%E5%8D%B0%E4%BC%81%E4%B8%9A1218%22%2C%22name%22%3A%22tcw%22%2C%22tel%22%3A%*************%22%2C%22storeGuid%22%3A%226480756384945597441%22%2C%22storeName%22%3A%22%E6%89%93%E5%8D%B0%E9%97%A8%E5%BA%971218%22%2C%22account%22%3A%********%22%2C%22storeNo%22%3A%*********%22%7D", "description": ""}], "body": {"mode": "raw", "raw": "{\n    \"invoiceType\": 40,\n    \"enterpriseGuid\": \"mock_erp_guid\",\n    \"storeGuid\": \"mock_store_guid\",\n    \"printUid\": \"mock_print_uid\",\n    \"areaGuid\": \"mock_area_guid_1\",\n    \"operatorStaffGuid\": \"mock_staff_guid\",\n    \"operatorStaffName\": \"mock_staff_name\",\n    \"createTime\": *************,\n    \"deviceId\": \"mock_device_id\",\n    \"printSourceEnum\": \"AIO\",\n    \"storeName\": \"mock_store_name\",\n    \"startTime\": *************,\n    \"endTime\": *************,\n    \"orderQuantity\": 1,\n    \"customerNumber\": 1,\n    \"salesTotal\": 0.01,\n    \"salesIncome\": 0.01,\n    \"salesDetail\": [\n    \t{\n    \t\t\"payName\": \"mock_pay_name\",\n    \t\t\"amount\": 0.01\n    \t}\n    ],\n    \"reduceTotal\": 0.01,\n    \"reduceDetail\": [\n    \t{\n    \t\t\"name\": \"mock_reduce_name\",\n    \t\t\"amount\": 0.01\n    \t}\n    ]\n}"}, "description": "添加打印机"}, "response": []}, {"name": "营业概况单-区域测试-SUCCESS", "request": {"url": "localhost:8917/print_record/send", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json;charset=utf-8", "description": ""}, {"key": "userInfo", "value": "%7B%22userGuid%22%3A%226480756476603191298%22%2C%22enterpriseGuid%22%3A%226490862829263766529%22%2C%22enterpriseName%22%3A%22%E6%89%93%E5%8D%B0%E4%BC%81%E4%B8%9A1218%22%2C%22name%22%3A%22tcw%22%2C%22tel%22%3A%*************%22%2C%22storeGuid%22%3A%226480756384945597441%22%2C%22storeName%22%3A%22%E6%89%93%E5%8D%B0%E9%97%A8%E5%BA%971218%22%2C%22account%22%3A%********%22%2C%22storeNo%22%3A%*********%22%7D", "description": ""}], "body": {"mode": "raw", "raw": "{\n    \"invoiceType\": 40,\n    \"enterpriseGuid\": \"mock_erp_guid\",\n    \"storeGuid\": \"mock_store_guid\",\n    \"printUid\": \"mock_print_uid\",\n    \"areaGuid\": \"mock_area_guid_3\",\n    \"operatorStaffGuid\": \"mock_staff_guid\",\n    \"operatorStaffName\": \"mock_staff_name\",\n    \"createTime\": *************,\n    \"deviceId\": \"mock_device_id\",\n    \"printSourceEnum\": \"AIO\",\n    \"storeName\": \"mock_store_name\",\n    \"startTime\": *************,\n    \"endTime\": *************,\n    \"orderQuantity\": 1,\n    \"customerNumber\": 1,\n    \"salesTotal\": 0.01,\n    \"salesIncome\": 0.01,\n    \"salesDetail\": [\n    \t{\n    \t\t\"payName\": \"mock_pay_name\",\n    \t\t\"amount\": 0.01\n    \t}\n    ],\n    \"reduceTotal\": 0.01,\n    \"reduceDetail\": [\n    \t{\n    \t\t\"name\": \"mock_reduce_name\",\n    \t\t\"amount\": 0.01\n    \t}\n    ]\n}"}, "description": "添加打印机"}, "response": []}, {"name": "收款统计单", "request": {"url": "localhost:8917/print_record/send", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json;charset=utf-8", "description": ""}, {"key": "userInfo", "value": "%7B%22userGuid%22%3A%226480756476603191298%22%2C%22enterpriseGuid%22%3A%226490862829263766529%22%2C%22enterpriseName%22%3A%22%E6%89%93%E5%8D%B0%E4%BC%81%E4%B8%9A1218%22%2C%22name%22%3A%22tcw%22%2C%22tel%22%3A%*************%22%2C%22storeGuid%22%3A%226480756384945597441%22%2C%22storeName%22%3A%22%E6%89%93%E5%8D%B0%E9%97%A8%E5%BA%971218%22%2C%22account%22%3A%********%22%2C%22storeNo%22%3A%*********%22%7D", "description": ""}], "body": {"mode": "raw", "raw": "{\n    \"invoiceType\": 41,\n    \"enterpriseGuid\": \"mock_erp_guid\",\n    \"storeGuid\": \"mock_store_guid\",\n    \"printUid\": \"mock_print_uid\",\n    \"areaGuid\": \"mock_area_guid_1\",\n    \"operatorStaffGuid\": \"mock_staff_guid\",\n    \"operatorStaffName\": \"mock_staff_name\",\n    \"createTime\": *************,\n    \"deviceId\": \"mock_device_id\",\n    \"printSourceEnum\": \"AIO\",\n    \"storeName\": \"mock_store_name\",\n    \"startTime\": *************,\n    \"endTime\": *************,\n    \"receiptTotal\": 1,\n    \"receiptDetail\": [\n    \t{\n    \t\t\"payName\": \"mock_pay_name\",\n    \t\t\"amount\": 0.01\n    \t}\n    ],\n    \"receiptStatsDetail\": [\n    \t{\n    \t\t\"payName\": \"mock_reduce_name\",\n    \t\t\"salesIncome\": 0.01,\n    \t\t\"rechargeIncome\": 0.01\n    \t}\n    ]\n}"}, "description": "添加打印机"}, "response": []}, {"name": "会员消费统计单", "request": {"url": "localhost:8917/print_record/send", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json;charset=utf-8", "description": ""}, {"key": "userInfo", "value": "%7B%22userGuid%22%3A%226480756476603191298%22%2C%22enterpriseGuid%22%3A%226490862829263766529%22%2C%22enterpriseName%22%3A%22%E6%89%93%E5%8D%B0%E4%BC%81%E4%B8%9A1218%22%2C%22name%22%3A%22tcw%22%2C%22tel%22%3A%*************%22%2C%22storeGuid%22%3A%226480756384945597441%22%2C%22storeName%22%3A%22%E6%89%93%E5%8D%B0%E9%97%A8%E5%BA%971218%22%2C%22account%22%3A%********%22%2C%22storeNo%22%3A%*********%22%7D", "description": ""}], "body": {"mode": "raw", "raw": "{\n    \"invoiceType\": 42,\n    \"enterpriseGuid\": \"mock_erp_guid\",\n    \"storeGuid\": \"mock_store_guid\",\n    \"printUid\": \"mock_print_uid\",\n    \"areaGuid\": \"mock_area_guid_1\",\n    \"operatorStaffGuid\": \"mock_staff_guid\",\n    \"operatorStaffName\": \"mock_staff_name\",\n    \"createTime\": *************,\n    \"deviceId\": \"mock_device_id\",\n    \"printSourceEnum\": \"AIO\",\n    \"storeName\": \"mock_store_name\",\n    \"startTime\": *************,\n    \"endTime\": *************,\n    \"consumeQuantity\": 1,\n    \"consumeTotal\": 1,\n    \"rechargeQuantity\": 1,\n    \"rechargeTotal\": 1,\n    \"rechargeGift\": 1,\n    \"rechargeIncome\": 1,\n    \"rechargeDetail\": [\n    \t{\n    \t\t\"payName\": \"mock_pay_name\",\n    \t\t\"amount\": 0.01\n    \t}\n    ]\n}"}, "description": "添加打印机"}, "response": []}, {"name": "用餐类型统计单", "request": {"url": "localhost:8917/print_record/send", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json;charset=utf-8", "description": ""}, {"key": "userInfo", "value": "%7B%22userGuid%22%3A%226480756476603191298%22%2C%22enterpriseGuid%22%3A%226490862829263766529%22%2C%22enterpriseName%22%3A%22%E6%89%93%E5%8D%B0%E4%BC%81%E4%B8%9A1218%22%2C%22name%22%3A%22tcw%22%2C%22tel%22%3A%*************%22%2C%22storeGuid%22%3A%226480756384945597441%22%2C%22storeName%22%3A%22%E6%89%93%E5%8D%B0%E9%97%A8%E5%BA%971218%22%2C%22account%22%3A%********%22%2C%22storeNo%22%3A%*********%22%7D", "description": ""}], "body": {"mode": "raw", "raw": "{\n    \"invoiceType\": 43,\n    \"enterpriseGuid\": \"mock_erp_guid\",\n    \"storeGuid\": \"mock_store_guid\",\n    \"printUid\": \"mock_print_uid\",\n    \"areaGuid\": \"mock_area_guid_1\",\n    \"operatorStaffGuid\": \"mock_staff_guid\",\n    \"operatorStaffName\": \"mock_staff_name\",\n    \"createTime\": *************,\n    \"deviceId\": \"mock_device_id\",\n    \"printSourceEnum\": \"AIO\",\n    \"storeName\": \"mock_store_name\",\n    \"startTime\": *************,\n    \"endTime\": *************,\n    \"orderQuantity\": 1,\n    \"customerNumber\": 1,\n    \"salesIncome\": 1,\n    \"dineMode\": {\n    \t\t\"orderQuantity\": 1,\n    \t\t\"salesIncome\": 0.01,\n    \t\t\"orderAvgConsume\": 0.01,\n    \t\t\"customerNumber\": 1,\n    \t\t\"customerAvgConsume\": 0.01\n    },\n    \"snackMode\": {\n    \t\t\"orderQuantity\": 1,\n    \t\t\"salesIncome\": 0.01,\n    \t\t\"orderAvgConsume\": 0.01,\n    \t\t\"customerNumber\": 1,\n    \t\t\"customerAvgConsume\": 0.01\n    },\n    \"takeoutMode\": [\n\t    {\n    \t\t\"orderQuantity\": 1,\n    \t\t\"salesIncome\": 0.01,\n    \t\t\"orderAvgConsume\": 0.01,\n    \t\t\"platformName\": \"mock_platform\"\n\t    }\n    ]\n}"}, "description": "添加打印机"}, "response": []}, {"name": "分类销售统计单", "request": {"url": "localhost:8917/print_record/send", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json;charset=utf-8", "description": ""}, {"key": "userInfo", "value": "%7B%22userGuid%22%3A%226480756476603191298%22%2C%22enterpriseGuid%22%3A%226490862829263766529%22%2C%22enterpriseName%22%3A%22%E6%89%93%E5%8D%B0%E4%BC%81%E4%B8%9A1218%22%2C%22name%22%3A%22tcw%22%2C%22tel%22%3A%*************%22%2C%22storeGuid%22%3A%226480756384945597441%22%2C%22storeName%22%3A%22%E6%89%93%E5%8D%B0%E9%97%A8%E5%BA%971218%22%2C%22account%22%3A%********%22%2C%22storeNo%22%3A%*********%22%7D", "description": ""}], "body": {"mode": "raw", "raw": "{\n    \"invoiceType\": 44,\n    \"enterpriseGuid\": \"mock_erp_guid\",\n    \"storeGuid\": \"mock_store_guid\",\n    \"printUid\": \"mock_print_uid\",\n    \"areaGuid\": \"mock_area_guid_1\",\n    \"operatorStaffGuid\": \"mock_staff_guid\",\n    \"operatorStaffName\": \"mock_staff_name\",\n    \"createTime\": *************,\n    \"deviceId\": \"mock_device_id\",\n    \"printSourceEnum\": \"AIO\",\n    \"storeName\": \"mock_store_name\",\n    \"startTime\": *************,\n    \"endTime\": *************,\n    \"itemTypeList\": [\n\t    {\n    \t\t\"name\": \"mock_name_1\",\n    \t\t\"quantity\": 0.01,\n    \t\t\"money\": 0.01\n\t    },\n\t    {\n    \t\t\"name\": \"mock_name_2\",\n    \t\t\"quantity\": 0.01,\n    \t\t\"money\": 0.01\n\t    }\n    ]\n}"}, "description": "添加打印机"}, "response": []}, {"name": "商品销售统计单", "request": {"url": "localhost:8917/print_record/send", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json;charset=utf-8", "description": ""}, {"key": "userInfo", "value": "%7B%22userGuid%22%3A%226480756476603191298%22%2C%22enterpriseGuid%22%3A%226490862829263766529%22%2C%22enterpriseName%22%3A%22%E6%89%93%E5%8D%B0%E4%BC%81%E4%B8%9A1218%22%2C%22name%22%3A%22tcw%22%2C%22tel%22%3A%*************%22%2C%22storeGuid%22%3A%226480756384945597441%22%2C%22storeName%22%3A%22%E6%89%93%E5%8D%B0%E9%97%A8%E5%BA%971218%22%2C%22account%22%3A%********%22%2C%22storeNo%22%3A%*********%22%7D", "description": ""}], "body": {"mode": "raw", "raw": "{\n    \"invoiceType\": 45,\n    \"enterpriseGuid\": \"mock_erp_guid\",\n    \"storeGuid\": \"mock_store_guid\",\n    \"printUid\": \"mock_print_uid\",\n    \"areaGuid\": \"mock_area_guid_1\",\n    \"operatorStaffGuid\": \"mock_staff_guid\",\n    \"operatorStaffName\": \"mock_staff_name\",\n    \"createTime\": *************,\n    \"deviceId\": \"mock_device_id\",\n    \"printSourceEnum\": \"AIO\",\n    \"storeName\": \"mock_store_name\",\n    \"startTime\": *************,\n    \"endTime\": *************,\n    \"itemList\": [\n\t    {\n    \t\t\"name\": \"mock_name_1\",\n    \t\t\"quantity\": 0.01,\n    \t\t\"money\": 0.01,\n    \t\t\"isWeight\": false,\n    \t\t\"isPackage\": false\n\t    },\n\t    {\n    \t\t\"name\": \"mock_name_2\",\n    \t\t\"quantity\": 0.01,\n    \t\t\"money\": 0.01,\n    \t\t\"isWeight\": false,\n    \t\t\"isPackage\": false\n\t    }\n    ]\n}"}, "description": "添加打印机"}, "response": []}, {"name": "退菜统计单", "request": {"url": "localhost:8917/print_record/send", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json;charset=utf-8", "description": ""}, {"key": "userInfo", "value": "%7B%22userGuid%22%3A%226480756476603191298%22%2C%22enterpriseGuid%22%3A%226490862829263766529%22%2C%22enterpriseName%22%3A%22%E6%89%93%E5%8D%B0%E4%BC%81%E4%B8%9A1218%22%2C%22name%22%3A%22tcw%22%2C%22tel%22%3A%*************%22%2C%22storeGuid%22%3A%226480756384945597441%22%2C%22storeName%22%3A%22%E6%89%93%E5%8D%B0%E9%97%A8%E5%BA%971218%22%2C%22account%22%3A%********%22%2C%22storeNo%22%3A%*********%22%7D", "description": ""}], "body": {"mode": "raw", "raw": "{\n    \"invoiceType\": 47,\n    \"enterpriseGuid\": \"mock_erp_guid\",\n    \"storeGuid\": \"mock_store_guid\",\n    \"printUid\": \"mock_print_uid\",\n    \"areaGuid\": \"mock_area_guid_1\",\n    \"operatorStaffGuid\": \"mock_staff_guid\",\n    \"operatorStaffName\": \"mock_staff_name\",\n    \"createTime\": *************,\n    \"deviceId\": \"mock_device_id\",\n    \"printSourceEnum\": \"AIO\",\n    \"storeName\": \"mock_store_name\",\n    \"startTime\": *************,\n    \"endTime\": *************,\n    \"itemList\": [\n\t    {\n    \t\t\"name\": \"mock_name_1\",\n    \t\t\"quantity\": 0.01,\n    \t\t\"money\": 0.01,\n    \t\t\"isWeight\": false,\n    \t\t\"isPackage\": false\n\t    },\n\t    {\n    \t\t\"name\": \"mock_name_2\",\n    \t\t\"quantity\": 0.01,\n    \t\t\"money\": 0.01,\n    \t\t\"isWeight\": false,\n    \t\t\"isPackage\": false\n\t    }\n    ]\n}"}, "description": "添加打印机"}, "response": []}, {"name": "赠菜统计单", "request": {"url": "localhost:8917/print_record/send", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json;charset=utf-8", "description": ""}, {"key": "userInfo", "value": "%7B%22userGuid%22%3A%226480756476603191298%22%2C%22enterpriseGuid%22%3A%226490862829263766529%22%2C%22enterpriseName%22%3A%22%E6%89%93%E5%8D%B0%E4%BC%81%E4%B8%9A1218%22%2C%22name%22%3A%22tcw%22%2C%22tel%22%3A%*************%22%2C%22storeGuid%22%3A%226480756384945597441%22%2C%22storeName%22%3A%22%E6%89%93%E5%8D%B0%E9%97%A8%E5%BA%971218%22%2C%22account%22%3A%********%22%2C%22storeNo%22%3A%*********%22%7D", "description": ""}], "body": {"mode": "raw", "raw": "{\n    \"invoiceType\": 48,\n    \"enterpriseGuid\": \"mock_erp_guid\",\n    \"storeGuid\": \"mock_store_guid\",\n    \"printUid\": \"mock_print_uid\",\n    \"areaGuid\": \"mock_area_guid_1\",\n    \"operatorStaffGuid\": \"mock_staff_guid\",\n    \"operatorStaffName\": \"mock_staff_name\",\n    \"createTime\": *************,\n    \"deviceId\": \"mock_device_id\",\n    \"printSourceEnum\": \"AIO\",\n    \"storeName\": \"mock_store_name\",\n    \"startTime\": *************,\n    \"endTime\": *************,\n    \"itemList\": [\n\t    {\n    \t\t\"name\": \"mock_name_1\",\n    \t\t\"quantity\": 0.01,\n    \t\t\"money\": 0.01,\n    \t\t\"isWeight\": false,\n    \t\t\"isPackage\": false\n\t    },\n\t    {\n    \t\t\"name\": \"mock_name_2\",\n    \t\t\"quantity\": 0.01,\n    \t\t\"money\": 0.01,\n    \t\t\"isWeight\": false,\n    \t\t\"isPackage\": false\n\t    }\n    ]\n}"}, "description": "添加打印机"}, "response": []}, {"name": "属性销售统计单", "request": {"url": "localhost:8917/print_record/send", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json;charset=utf-8", "description": ""}, {"key": "userInfo", "value": "%7B%22userGuid%22%3A%226480756476603191298%22%2C%22enterpriseGuid%22%3A%226490862829263766529%22%2C%22enterpriseName%22%3A%22%E6%89%93%E5%8D%B0%E4%BC%81%E4%B8%9A1218%22%2C%22name%22%3A%22tcw%22%2C%22tel%22%3A%*************%22%2C%22storeGuid%22%3A%226480756384945597441%22%2C%22storeName%22%3A%22%E6%89%93%E5%8D%B0%E9%97%A8%E5%BA%971218%22%2C%22account%22%3A%********%22%2C%22storeNo%22%3A%*********%22%7D", "description": ""}], "body": {"mode": "raw", "raw": "{\n    \"invoiceType\": 46,\n    \"enterpriseGuid\": \"mock_erp_guid\",\n    \"storeGuid\": \"mock_store_guid\",\n    \"printUid\": \"mock_print_uid\",\n    \"areaGuid\": \"mock_area_guid_1\",\n    \"operatorStaffGuid\": \"mock_staff_guid\",\n    \"operatorStaffName\": \"mock_staff_name\",\n    \"createTime\": *************,\n    \"deviceId\": \"mock_device_id\",\n    \"printSourceEnum\": \"AIO\",\n    \"storeName\": \"mock_store_name\",\n    \"startTime\": *************,\n    \"endTime\": *************,\n    \"propGroupList\": [\n\t    {\n    \t\t\"name\": \"mock_name_1\",\n    \t\t\"propList\": [\n    \t\t\t{\n    \t\t\t\t\"name\": \"mock_name_11\",\n    \t\t\t\t\"quantity\": 1,\n    \t\t\t\t\"money\": 0.01\n    \t\t\t},\n    \t\t\t{\n    \t\t\t\t\"name\": \"mock_name_12\",\n    \t\t\t\t\"quantity\": 1,\n    \t\t\t\t\"money\": 0.01\n    \t\t\t}\n    \t\t]\n\t    },\n\t    {\n    \t\t\"name\": \"mock_name_2\",\n    \t\t\"propList\": [\n    \t\t\t{\n    \t\t\t\t\"name\": \"mock_name_21\",\n    \t\t\t\t\"quantity\": 1,\n    \t\t\t\t\"money\": 0.01\n    \t\t\t},\n    \t\t\t{\n    \t\t\t\t\"name\": \"mock_name_22\",\n    \t\t\t\t\"quantity\": 1,\n    \t\t\t\t\"money\": 0.01\n    \t\t\t}\n    \t\t]\n\t    }\n    ]\n}"}, "description": "添加打印机"}, "response": []}, {"name": "点菜单", "request": {"url": "localhost:8917/print_record/send", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json;charset=utf-8", "description": ""}, {"key": "userInfo", "value": "%7B%22userGuid%22%3A%226480756476603191298%22%2C%22enterpriseGuid%22%3A%226490862829263766529%22%2C%22enterpriseName%22%3A%22%E6%89%93%E5%8D%B0%E4%BC%81%E4%B8%9A1218%22%2C%22name%22%3A%22tcw%22%2C%22tel%22%3A%*************%22%2C%22storeGuid%22%3A%226480756384945597441%22%2C%22storeName%22%3A%22%E6%89%93%E5%8D%B0%E9%97%A8%E5%BA%971218%22%2C%22account%22%3A%********%22%2C%22storeNo%22%3A%*********%22%7D", "description": ""}], "body": {"mode": "raw", "raw": "{\n    \"invoiceType\": 80,\n    \"enterpriseGuid\": \"mock_erp_guid\",\n    \"storeGuid\": \"mock_store_guid\",\n    \"printUid\": \"mock_print_uid\",\n    \"areaGuid\": \"mock_area_guid_1\",\n    \"operatorStaffGuid\": \"mock_staff_guid\",\n    \"operatorStaffName\": \"mock_staff_name\",\n    \"createTime\": *************,\n    \"deviceId\": \"mock_device_id\",\n    \"printSourceEnum\": \"AIO\",\n    \"itemRecordList\": [\n        {\n            \"itemGuid\": \"mock_item_guid_1\",\n            \"itemName\": \"mock_item_name_1\",\n            \"itemTypeGuid\": \"mock_item_type_guid\",\n            \"itemTypeName\": \"mock_item_type_name\",\n            \"price\": 0.01,\n            \"number\": 1.00,\n            \"asWeight\": false,\n            \"asPackage\": false,\n            \"asGift\": false,\n            \"property\": \"mock_property\",\n            \"propertyPrice\": 0.01,\n            \"ingredientPrice\": 0.01\n        },\n        {\n            \"itemGuid\": \"mock_item_guid_2\",\n            \"itemName\": \"mock_item_name_2\",\n            \"itemTypeGuid\": \"mock_item_type_guid\",\n            \"itemTypeName\": \"mock_item_type_name\",\n            \"price\": 0.01,\n            \"number\": 1.00,\n            \"asWeight\": false,\n            \"asPackage\": false,\n            \"asGift\": false,\n            \"property\": \"mock_property\",\n            \"propertyPrice\": 0.01,\n            \"ingredientPrice\": 0.01\n        }\n    ],\n    \"markName\": \"mock_mark_name\",\n    \"markNo\": \"mock_mark_no\",\n    \"orderNo\": \"mock_order_no\",\n    \"personNumber\": 1,\n    \"orderTime\": *************,\n    \"tradeMode\": 0,\n    \"remark\": \"mock_remark\"\n}"}, "description": "添加打印机"}, "response": []}, {"name": "点菜单-称重商品", "request": {"url": "localhost:8917/print_record/send", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json;charset=utf-8", "description": ""}, {"key": "userInfo", "value": "%7B%22userGuid%22%3A%226480756476603191298%22%2C%22enterpriseGuid%22%3A%226490862829263766529%22%2C%22enterpriseName%22%3A%22%E6%89%93%E5%8D%B0%E4%BC%81%E4%B8%9A1218%22%2C%22name%22%3A%22tcw%22%2C%22tel%22%3A%*************%22%2C%22storeGuid%22%3A%226480756384945597441%22%2C%22storeName%22%3A%22%E6%89%93%E5%8D%B0%E9%97%A8%E5%BA%971218%22%2C%22account%22%3A%********%22%2C%22storeNo%22%3A%*********%22%7D", "description": ""}], "body": {"mode": "raw", "raw": "{\n    \"invoiceType\": 80,\n    \"enterpriseGuid\": \"mock_erp_guid\",\n    \"storeGuid\": \"mock_store_guid\",\n    \"printUid\": \"mock_print_uid\",\n    \"areaGuid\": \"mock_area_guid_1\",\n    \"operatorStaffGuid\": \"mock_staff_guid\",\n    \"operatorStaffName\": \"mock_staff_name\",\n    \"createTime\": *************,\n    \"deviceId\": \"mock_device_id\",\n    \"printSourceEnum\": \"AIO\",\n    \"itemRecordList\": [\n        {\n            \"itemGuid\": \"mock_item_guid_1\",\n            \"itemName\": \"mock_item_name_1\",\n            \"itemTypeGuid\": \"mock_item_type_guid\",\n            \"itemTypeName\": \"mock_item_type_name\",\n            \"price\": 0.01,\n            \"number\": 1.00,\n            \"asWeight\": false,\n            \"asPackage\": false,\n            \"asGift\": false,\n            \"property\": \"mock_property\",\n            \"propertyPrice\": 0.01,\n            \"ingredientPrice\": 0.01\n        },\n        {\n            \"itemGuid\": \"mock_item_guid_2\",\n            \"itemName\": \"mock_item_name_2\",\n            \"itemTypeGuid\": \"mock_item_type_guid\",\n            \"itemTypeName\": \"mock_item_type_name\",\n            \"price\": 0.01,\n            \"number\": 1.00,\n            \"asWeight\": true,\n            \"asPackage\": false,\n            \"asGift\": false,\n            \"property\": \"mock_property\",\n            \"propertyPrice\": 0.01,\n            \"ingredientPrice\": 0.01\n        }\n    ],\n    \"markName\": \"mock_mark_name\",\n    \"markNo\": \"mock_mark_no\",\n    \"orderNo\": \"mock_order_no\",\n    \"personNumber\": 1,\n    \"orderTime\": *************,\n    \"tradeMode\": 0,\n    \"remark\": \"mock_remark\"\n}"}, "description": "添加打印机"}, "response": []}, {"name": "点菜单-区域测试-未匹配到打印机", "request": {"url": "localhost:8917/print_record/send", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json;charset=utf-8", "description": ""}, {"key": "userInfo", "value": "%7B%22userGuid%22%3A%226480756476603191298%22%2C%22enterpriseGuid%22%3A%226490862829263766529%22%2C%22enterpriseName%22%3A%22%E6%89%93%E5%8D%B0%E4%BC%81%E4%B8%9A1218%22%2C%22name%22%3A%22tcw%22%2C%22tel%22%3A%*************%22%2C%22storeGuid%22%3A%226480756384945597441%22%2C%22storeName%22%3A%22%E6%89%93%E5%8D%B0%E9%97%A8%E5%BA%971218%22%2C%22account%22%3A%********%22%2C%22storeNo%22%3A%*********%22%7D", "description": ""}], "body": {"mode": "raw", "raw": "{\n    \"invoiceType\": 80,\n    \"enterpriseGuid\": \"mock_erp_guid\",\n    \"storeGuid\": \"mock_store_guid\",\n    \"printUid\": \"mock_print_uid\",\n    \"areaGuid\": \"mock_area_guid_3\",\n    \"operatorStaffGuid\": \"mock_staff_guid\",\n    \"operatorStaffName\": \"mock_staff_name\",\n    \"createTime\": *************,\n    \"deviceId\": \"mock_device_id\",\n    \"printSourceEnum\": \"AIO\",\n    \"itemRecordList\": [\n        {\n            \"itemGuid\": \"mock_item_guid_1\",\n            \"itemName\": \"mock_item_name_1\",\n            \"itemTypeGuid\": \"mock_item_type_guid\",\n            \"itemTypeName\": \"mock_item_type_name\",\n            \"price\": 0.01,\n            \"number\": 1.00,\n            \"asWeight\": false,\n            \"asPackage\": false,\n            \"asGift\": false,\n            \"property\": \"mock_property\",\n            \"propertyPrice\": 0.01,\n            \"ingredientPrice\": 0.01\n        },\n        {\n            \"itemGuid\": \"mock_item_guid_2\",\n            \"itemName\": \"mock_item_name_2\",\n            \"itemTypeGuid\": \"mock_item_type_guid\",\n            \"itemTypeName\": \"mock_item_type_name\",\n            \"price\": 0.01,\n            \"number\": 1.00,\n            \"asWeight\": false,\n            \"asPackage\": false,\n            \"asGift\": false,\n            \"property\": \"mock_property\",\n            \"propertyPrice\": 0.01,\n            \"ingredientPrice\": 0.01\n        }\n    ],\n    \"markName\": \"mock_mark_name\",\n    \"markNo\": \"mock_mark_no\",\n    \"orderNo\": \"mock_order_no\",\n    \"personNumber\": 1,\n    \"orderTime\": *************,\n    \"tradeMode\": 0,\n    \"remark\": \"mock_remark\"\n}"}, "description": "添加打印机"}, "response": []}, {"name": "点菜单-POS测试-SUCCESS", "request": {"url": "localhost:8917/print_record/send", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json;charset=utf-8", "description": ""}, {"key": "userInfo", "value": "%7B%22userGuid%22%3A%226480756476603191298%22%2C%22enterpriseGuid%22%3A%226490862829263766529%22%2C%22enterpriseName%22%3A%22%E6%89%93%E5%8D%B0%E4%BC%81%E4%B8%9A1218%22%2C%22name%22%3A%22tcw%22%2C%22tel%22%3A%*************%22%2C%22storeGuid%22%3A%226480756384945597441%22%2C%22storeName%22%3A%22%E6%89%93%E5%8D%B0%E9%97%A8%E5%BA%971218%22%2C%22account%22%3A%********%22%2C%22storeNo%22%3A%*********%22%7D", "description": ""}], "body": {"mode": "raw", "raw": "{\n    \"invoiceType\": 80,\n    \"enterpriseGuid\": \"mock_erp_guid\",\n    \"storeGuid\": \"mock_store_guid\",\n    \"printUid\": \"mock_print_uid\",\n    \"areaGuid\": \"mock_area_guid_1\",\n    \"operatorStaffGuid\": \"mock_staff_guid\",\n    \"operatorStaffName\": \"mock_staff_name\",\n    \"createTime\": *************,\n    \"deviceId\": \"mock_device_id_2\",\n    \"printSourceEnum\": \"POS\",\n    \"itemRecordList\": [\n        {\n            \"itemGuid\": \"mock_item_guid_1\",\n            \"itemName\": \"mock_item_name_1\",\n            \"itemTypeGuid\": \"mock_item_type_guid\",\n            \"itemTypeName\": \"mock_item_type_name\",\n            \"price\": 0.01,\n            \"number\": 1.00,\n            \"asWeight\": false,\n            \"asPackage\": false,\n            \"asGift\": false,\n            \"property\": \"mock_property\",\n            \"propertyPrice\": 0.01,\n            \"ingredientPrice\": 0.01\n        },\n        {\n            \"itemGuid\": \"mock_item_guid_2\",\n            \"itemName\": \"mock_item_name_2\",\n            \"itemTypeGuid\": \"mock_item_type_guid\",\n            \"itemTypeName\": \"mock_item_type_name\",\n            \"price\": 0.01,\n            \"number\": 1.00,\n            \"asWeight\": false,\n            \"asPackage\": false,\n            \"asGift\": false,\n            \"property\": \"mock_property\",\n            \"propertyPrice\": 0.01,\n            \"ingredientPrice\": 0.01\n        }\n    ],\n    \"markName\": \"mock_mark_name\",\n    \"markNo\": \"mock_mark_no\",\n    \"orderNo\": \"mock_order_no\",\n    \"personNumber\": 1,\n    \"orderTime\": *************,\n    \"tradeMode\": 0,\n    \"remark\": \"mock_remark\"\n}"}, "description": "添加打印机"}, "response": []}, {"name": "退菜单", "request": {"url": "localhost:8917/print_record/send", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json;charset=utf-8", "description": ""}, {"key": "userInfo", "value": "%7B%22userGuid%22%3A%226480756476603191298%22%2C%22enterpriseGuid%22%3A%226490862829263766529%22%2C%22enterpriseName%22%3A%22%E6%89%93%E5%8D%B0%E4%BC%81%E4%B8%9A1218%22%2C%22name%22%3A%22tcw%22%2C%22tel%22%3A%*************%22%2C%22storeGuid%22%3A%226480756384945597441%22%2C%22storeName%22%3A%22%E6%89%93%E5%8D%B0%E9%97%A8%E5%BA%971218%22%2C%22account%22%3A%********%22%2C%22storeNo%22%3A%*********%22%7D", "description": ""}], "body": {"mode": "raw", "raw": "{\n    \"invoiceType\": 81,\n    \"enterpriseGuid\": \"mock_erp_guid\",\n    \"storeGuid\": \"mock_store_guid\",\n    \"printUid\": \"mock_print_uid\",\n    \"areaGuid\": \"mock_area_guid_1\",\n    \"operatorStaffGuid\": \"mock_staff_guid\",\n    \"operatorStaffName\": \"mock_staff_name\",\n    \"createTime\": *************,\n    \"deviceId\": \"mock_device_id\",\n    \"printSourceEnum\": \"AIO\",\n    \"itemRecordList\": [\n        {\n            \"itemGuid\": \"mock_item_guid_1\",\n            \"itemName\": \"mock_item_name_1\",\n            \"itemTypeGuid\": \"mock_item_type_guid\",\n            \"itemTypeName\": \"mock_item_type_name\",\n            \"price\": 0.01,\n            \"number\": 1.00,\n            \"asWeight\": false,\n            \"asPackage\": false,\n            \"asGift\": false,\n            \"property\": \"mock_property\",\n            \"propertyPrice\": 0.01,\n            \"ingredientPrice\": 0.01,\n            \"unit\": \"份\"\n        },\n        {\n            \"itemGuid\": \"mock_item_guid_2\",\n            \"itemName\": \"mock_item_name_2\",\n            \"itemTypeGuid\": \"mock_item_type_guid\",\n            \"itemTypeName\": \"mock_item_type_name\",\n            \"price\": 0.01,\n            \"number\": 1.00,\n            \"asWeight\": false,\n            \"asPackage\": false,\n            \"asGift\": false,\n            \"property\": \"mock_property\",\n            \"propertyPrice\": 0.01,\n            \"ingredientPrice\": 0.01,\n            \"unit\": \"份\"\n        }\n    ],\n    \"markName\": \"mock_mark_name\",\n    \"markNo\": \"mock_mark_no\",\n    \"orderNo\": \"mock_order_no\",\n    \"personNumber\": 1,\n    \"orderTime\": *************,\n    \"tradeMode\": 0\n}"}, "description": "添加打印机"}, "response": []}, {"name": "退菜单-称重商品", "request": {"url": "localhost:8917/print_record/send", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json;charset=utf-8", "description": ""}, {"key": "userInfo", "value": "%7B%22userGuid%22%3A%226480756476603191298%22%2C%22enterpriseGuid%22%3A%226490862829263766529%22%2C%22enterpriseName%22%3A%22%E6%89%93%E5%8D%B0%E4%BC%81%E4%B8%9A1218%22%2C%22name%22%3A%22tcw%22%2C%22tel%22%3A%*************%22%2C%22storeGuid%22%3A%226480756384945597441%22%2C%22storeName%22%3A%22%E6%89%93%E5%8D%B0%E9%97%A8%E5%BA%971218%22%2C%22account%22%3A%********%22%2C%22storeNo%22%3A%*********%22%7D", "description": ""}], "body": {"mode": "raw", "raw": "{\n    \"invoiceType\": 81,\n    \"enterpriseGuid\": \"mock_erp_guid\",\n    \"storeGuid\": \"mock_store_guid\",\n    \"printUid\": \"mock_print_uid\",\n    \"areaGuid\": \"mock_area_guid_1\",\n    \"operatorStaffGuid\": \"mock_staff_guid\",\n    \"operatorStaffName\": \"mock_staff_name\",\n    \"createTime\": *************,\n    \"deviceId\": \"mock_device_id\",\n    \"printSourceEnum\": \"AIO\",\n    \"itemRecordList\": [\n        {\n            \"itemGuid\": \"mock_item_guid_1\",\n            \"itemName\": \"mock_item_name_1\",\n            \"itemTypeGuid\": \"mock_item_type_guid\",\n            \"itemTypeName\": \"mock_item_type_name\",\n            \"price\": 0.01,\n            \"number\": 1.00,\n            \"asWeight\": false,\n            \"asPackage\": false,\n            \"asGift\": false,\n            \"property\": \"mock_property\",\n            \"propertyPrice\": 0.01,\n            \"ingredientPrice\": 0.01,\n            \"unit\": \"份\"\n        },\n        {\n            \"itemGuid\": \"mock_item_guid_2\",\n            \"itemName\": \"mock_item_name_2\",\n            \"itemTypeGuid\": \"mock_item_type_guid\",\n            \"itemTypeName\": \"mock_item_type_name\",\n            \"price\": 0.01,\n            \"number\": 1.00,\n            \"asWeight\": true,\n            \"asPackage\": false,\n            \"asGift\": false,\n            \"property\": \"mock_property\",\n            \"propertyPrice\": 0.01,\n            \"ingredientPrice\": 0.01,\n            \"unit\": \"斤\"\n        }\n    ],\n    \"markName\": \"mock_mark_name\",\n    \"markNo\": \"mock_mark_no\",\n    \"orderNo\": \"mock_order_no\",\n    \"personNumber\": 1,\n    \"orderTime\": *************,\n    \"tradeMode\": 0\n}"}, "description": "添加打印机"}, "response": []}, {"name": "退菜单-区域测试-未匹配到打印机", "request": {"url": "localhost:8917/print_record/send", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json;charset=utf-8", "description": ""}, {"key": "userInfo", "value": "%7B%22userGuid%22%3A%226480756476603191298%22%2C%22enterpriseGuid%22%3A%226490862829263766529%22%2C%22enterpriseName%22%3A%22%E6%89%93%E5%8D%B0%E4%BC%81%E4%B8%9A1218%22%2C%22name%22%3A%22tcw%22%2C%22tel%22%3A%*************%22%2C%22storeGuid%22%3A%226480756384945597441%22%2C%22storeName%22%3A%22%E6%89%93%E5%8D%B0%E9%97%A8%E5%BA%971218%22%2C%22account%22%3A%********%22%2C%22storeNo%22%3A%*********%22%7D", "description": ""}], "body": {"mode": "raw", "raw": "{\n    \"invoiceType\": 81,\n    \"enterpriseGuid\": \"mock_erp_guid\",\n    \"storeGuid\": \"mock_store_guid\",\n    \"printUid\": \"mock_print_uid\",\n    \"areaGuid\": \"mock_area_guid_3\",\n    \"operatorStaffGuid\": \"mock_staff_guid\",\n    \"operatorStaffName\": \"mock_staff_name\",\n    \"createTime\": *************,\n    \"deviceId\": \"mock_device_id\",\n    \"printSourceEnum\": \"AIO\",\n    \"itemRecordList\": [\n        {\n            \"itemGuid\": \"mock_item_guid_1\",\n            \"itemName\": \"mock_item_name_1\",\n            \"itemTypeGuid\": \"mock_item_type_guid\",\n            \"itemTypeName\": \"mock_item_type_name\",\n            \"price\": 0.01,\n            \"number\": 1.00,\n            \"asWeight\": false,\n            \"asPackage\": false,\n            \"asGift\": false,\n            \"property\": \"mock_property\",\n            \"propertyPrice\": 0.01,\n            \"ingredientPrice\": 0.01,\n            \"unit\": \"份\"\n        },\n        {\n            \"itemGuid\": \"mock_item_guid_2\",\n            \"itemName\": \"mock_item_name_2\",\n            \"itemTypeGuid\": \"mock_item_type_guid\",\n            \"itemTypeName\": \"mock_item_type_name\",\n            \"price\": 0.01,\n            \"number\": 1.00,\n            \"asWeight\": false,\n            \"asPackage\": false,\n            \"asGift\": false,\n            \"property\": \"mock_property\",\n            \"propertyPrice\": 0.01,\n            \"ingredientPrice\": 0.01,\n            \"unit\": \"份\"\n        }\n    ],\n    \"markName\": \"mock_mark_name\",\n    \"markNo\": \"mock_mark_no\",\n    \"orderNo\": \"mock_order_no\",\n    \"personNumber\": 1,\n    \"orderTime\": *************,\n    \"tradeMode\": 0\n}"}, "description": "添加打印机"}, "response": []}, {"name": "后厨转台单", "request": {"url": "localhost:8917/print_record/send", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json;charset=utf-8", "description": ""}, {"key": "userInfo", "value": "%7B%22userGuid%22%3A%226480756476603191298%22%2C%22enterpriseGuid%22%3A%226490862829263766529%22%2C%22enterpriseName%22%3A%22%E6%89%93%E5%8D%B0%E4%BC%81%E4%B8%9A1218%22%2C%22name%22%3A%22tcw%22%2C%22tel%22%3A%*************%22%2C%22storeGuid%22%3A%226480756384945597441%22%2C%22storeName%22%3A%22%E6%89%93%E5%8D%B0%E9%97%A8%E5%BA%971218%22%2C%22account%22%3A%********%22%2C%22storeNo%22%3A%*********%22%7D", "description": ""}], "body": {"mode": "raw", "raw": "{\n    \"invoiceType\": 85,\n    \"enterpriseGuid\": \"mock_erp_guid\",\n    \"storeGuid\": \"mock_store_guid\",\n    \"printUid\": \"mock_print_uid\",\n    \"areaGuid\": \"mock_area_guid_1\",\n    \"operatorStaffGuid\": \"mock_staff_guid\",\n    \"operatorStaffName\": \"mock_staff_name\",\n    \"createTime\": *************,\n    \"deviceId\": \"mock_device_id\",\n    \"printSourceEnum\": \"AIO\",\n    \"storeName\": \"mock_store_name\",\n    \"srcTableName\": \"mock_src_name\",\n    \"destTableName\": \"mock_dest_name\",\n    \"turnTime\": *************\n}"}, "description": "添加打印机"}, "response": []}, {"name": "后厨转台单-区域测试-未匹配到打印机", "request": {"url": "localhost:8917/print_record/send", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json;charset=utf-8", "description": ""}, {"key": "userInfo", "value": "%7B%22userGuid%22%3A%226480756476603191298%22%2C%22enterpriseGuid%22%3A%226490862829263766529%22%2C%22enterpriseName%22%3A%22%E6%89%93%E5%8D%B0%E4%BC%81%E4%B8%9A1218%22%2C%22name%22%3A%22tcw%22%2C%22tel%22%3A%*************%22%2C%22storeGuid%22%3A%226480756384945597441%22%2C%22storeName%22%3A%22%E6%89%93%E5%8D%B0%E9%97%A8%E5%BA%971218%22%2C%22account%22%3A%********%22%2C%22storeNo%22%3A%*********%22%7D", "description": ""}], "body": {"mode": "raw", "raw": "{\n    \"invoiceType\": 85,\n    \"enterpriseGuid\": \"mock_erp_guid\",\n    \"storeGuid\": \"mock_store_guid\",\n    \"printUid\": \"mock_print_uid\",\n    \"areaGuid\": \"mock_area_guid_3\",\n    \"operatorStaffGuid\": \"mock_staff_guid\",\n    \"operatorStaffName\": \"mock_staff_name\",\n    \"createTime\": *************,\n    \"deviceId\": \"mock_device_id\",\n    \"printSourceEnum\": \"AIO\",\n    \"storeName\": \"mock_store_name\",\n    \"srcTableName\": \"mock_src_name\",\n    \"destTableName\": \"mock_dest_name\",\n    \"turnTime\": *************\n}"}, "description": "添加打印机"}, "response": []}]}