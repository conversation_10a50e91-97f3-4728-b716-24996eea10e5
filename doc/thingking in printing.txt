整体设计基调：
能复用Template就复用，不能复用(如结构性变化)就新建template
但是Template强依赖于PrintDTO何FormatDTO了，不好复用


单据类型

业态类型

营业类型
页宽类型

餐饮
    - 结帐单
        - 正餐
            58
            80
        - 快餐
            58
            80  
    - 外卖
        -外卖
    - 交接班
        58
        80

商超
    - 结帐单
        - 正餐
            58
            80
        - 快餐
            58
            80  
    - 交接班
        58
        80


美页
    - 结帐单
        - 正餐
            58
            80
        - 快餐
            58
            80  
    - 交接班
        58
        80


invoiceType
tradeMode
template
format
name
title
bizType
printDTO
printFormat

通用模板
指定业态的模板

模板
模板数据注入：页宽、单据；   还缺少 业态、营业类型 （用一个）
模板建造者
模板接口隔离
模板接口单一职责
模板的模板方法应用

模板工厂

AbsKitchenItemTemplate提供dto转bo对象的接口，让子类实现。

元素
元素格式
元素辅助类

PrintEscUtils：resolveTableItemRow，resolveKitchenItemRow

PrintLabelUtils、PrintCalcUtils：只有LabelTemplate在用
    

元素复合容器
PrintModuleUtils：一个最常用的staffAndPrintTime样式，一个3列排版的table样式
PrintFixedContainer：提供对AddRow的原生支持；固定的一套元素容器；
PrintElementContainer：提供对AddRow的原生支持；支持模板的一套元素容器；

元素工具类
PrintRowUtils：元素行工具
PrintTableUtils：仅仅给部分单据提供了columnWidth，建议定义 headerFactory 来做（划掉）。
TableRowContext：对 Table 格式提供了一个支持

做一个calculator的factory何children何templateMethod

PrintItemCalculator
PrintStatsCalculator
用抽象工厂？？？抽象一个Calculator，里面包含3元素+1Header+1Body，body可以由子类实现并提供新的模板方法
FormatCalculator
HeaderCalculator
PrintItemCalculator继承自FormatCalculator、HeaderCalculator，扩展type、item、subItem、prop、remark、total
PrintStatCalculator继承自FormatCalculator、HeaderCalculator，实现默认header，扩展normalRow

SubItem
Prop
Remark
Total

非元素工具类
PrintCutUtils：只用PrintRecordCloneService在用
PrintMockUtils：只用PrintRecordRespService在用
PrintRepairUtils：只用PrintRecordCloneService在用

营业类型工具类
TradeModeUtils

