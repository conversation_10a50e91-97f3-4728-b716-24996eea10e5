# 扫码点餐优化变更文档

## 1.商品查询接口

**URL：/deal/menu/item_config**

1.查询用户是否登录，统一使用 `isMemberLogin()`从缓存里获取

从该接口开始缓存会员优惠信息、满减满折活动信息、限时特价活动，会员优惠信息重新扫码会重新查询


2.会员权益返回增加字段

```
@ApiModelProperty("会员权益类型 0无权益 1会员价 2会员折扣")
private Integer memberRightsType;

@ApiModelProperty("会员折扣比例")
private BigDecimal discountValue;
```

原来的字段弃用：

```
@ApiModelProperty("是否支持会员价")
private boolean memberPrice;

@ApiModelProperty("折扣描述及支持菜品的key")
private List<ResponseDiscount> discountRespDTOList;
```

现在的会员折扣在商品上没有限制，因此不需要特殊返回

并且在商品列表查询会员权益后会缓存到redis，后面直接查redis，缓存时间2小时

```
/**
 * 会员权益
 */
public static final String MEMBER_RIGHTS = "MemberRights:%s";
```

获取会员权益公共方法

```
ResponseProductDiscount productDiscountRespDTO = queryMemberRights(WeixinUserThreadLocal.getWxtoken());
```



3.商品价格字段整理为一下字段

```
@ApiModelProperty(value = "划线价")
private BigDecimal linePrice;

@ApiModelProperty("原价")
private BigDecimal originalPrice;

@ApiModelProperty("最低价")
private BigDecimal minPrice;

/**
 * 最低价类型
 *
 * @see MinPriceTypeEnum
 */
@ApiModelProperty("最低价类型 0原价 1会员价 2会员折扣 3限时特价")
private Integer minPriceType;

/**
 * 会员折扣比例
 */
@ApiModelProperty("会员折扣比例")
private BigDecimal discountValue;
```

根据`minPriceType`区分，为0时不展示`minPrice`，为2时用`discountValue`展示打*折，其余情况展示`minPrice`

除了以上字段，以前的众多价格相关字段都不再使用，精简之后需要前端处理好小程序和h5

以下字段为摒弃的字段

```
@ApiModelProperty(value = "商品原价")
private BigDecimal showPrice = BigDecimal.ZERO;

@ApiModelProperty(value = "true：有会员价,false：没有")
private Boolean enablePreferentialPrice = false;

@ApiModelProperty(value = "商品会员价")
private BigDecimal showMemberPrice = BigDecimal.ZERO;

@ApiModelProperty(value = "划线价")
private BigDecimal showLinePrice = BigDecimal.ZERO;

@ApiModelProperty(value = "是否参与会员折扣（0：否，1：是）")
private Integer isMemberDiscount;

@ApiModelProperty("满减活动规则描述")
private List<String> fullReductionStrList;

@ApiModelProperty("满折活动规则描述")
private List<String> fullDiscountStrList;

```



4.商品参与的活动标签原来由分开的几个字段分别展示，整理为一个字段展示

以下为`活动规则描述`字段

```
/**
 * 活动规则描述
 * 顺序：1限时特价 2满减 3满折
 */
@ApiModelProperty("活动规则描述")
private List<ActivityRuleDescDTO> activityRuleDescList;

@ApiModel(value = "活动规则描述")
public class ActivityRuleDescDTO implements Serializable {

    private static final long serialVersionUID = 1802511816052648651L;

    /**
     * @see ActivityTypeEnum
     */
    @ApiModelProperty("活动类型 1限时特价 2满减 3满折")
    private Integer activityType;

    @ApiModelProperty(value = "活动规则描述")
    private String activityRuleDesc;

}
```

以下为摒弃的字段

```
@ApiModelProperty(value = "会员折扣规则")
private String showMemberRule = StringUtils.EMPTY;

@ApiModelProperty("满减活动规则描述")
private List<String> fullReductionStrList;

@ApiModelProperty("满折活动规则描述")
private List<String> fullDiscountStrList;
```



5.限时特价活动相关字段不再返回给前端，金额由后端计算

以下为摒弃的字段

```
@ApiModelProperty(value = "最终优惠价")
private BigDecimal preferentialPrice;

@ApiModelProperty(value = "折扣价")
private BigDecimal discountPrice;

/**
 * 特价类型 1打折 2减价 3指定价格
 */
private Integer specialsType;

/**
 * 特价数额
 */
private BigDecimal specialsNumber;

/**
 * 活动规则
 * 共享互斥关系 0-互斥 1-共享
 */
@ApiModelProperty(value = "活动规则 共享互斥关系 0-互斥 1-共享")
private Integer relationRule;

/**
 * 商品特价优惠后金额
 */
private BigDecimal specialsPrice;

/**
 * 商品特价优惠折扣
 */
private BigDecimal specialsDiscount;

/**
 * 优惠类型 1会员价 2会员折扣 3限时特价
 *
 * @see PreferentialTypeEnum
 */
@ApiModelProperty(value = "优惠类型 1会员价 2会员折扣 3限时特价")
private Integer preferentialType;
```



7.规格增加`活动规则描述`字段

`如商品存在限时特价活动，并与满减满折活动互斥，则满减满折活动信息不提示`

```
/**
 * 活动规则描述
 * 顺序：1限时特价 2满减 3满折
 */
@ApiModelProperty("活动规则描述")
private List<ActivityRuleDescDTO> activityRuleDescList;

@ApiModel(value = "活动规则描述")
public class ActivityRuleDescDTO implements Serializable {

    private static final long serialVersionUID = 1802511816052648651L;

    /**
     * @see ActivityTypeEnum
     */
    @ApiModelProperty("活动类型 1限时特价 2满减 3满折")
    private Integer activityType;

    @ApiModelProperty(value = "活动规则描述")
    private String activityRuleDesc;

}
```

摒弃以下字段

```
@ApiModelProperty("满减活动规则描述")
private List<String> fullReductionStrList;

@ApiModelProperty("满折活动规则描述")
private List<String> fullDiscountStrList;
```



## 2.购物车查询接口

**URL：/deal/menu/shop_cart**

1.最外层的折扣价摒弃

```
@ApiModelProperty(value = "折扣价")
private BigDecimal discountPrice;
```

2.会员权益查询修改，改为从缓存中直接取权益信息

```
queryMemberRights(WeixinUserThreadLocal.getWxtoken());
```

3.修改了一些计算方式，优化了部分冗余逻辑，限时特价计算优化

4.增加以下字段给前端展示

```
@ApiModelProperty(value = "商品种数")
private Integer speciesCount;

@ApiModelProperty(value = "商品件数")
private BigDecimal pieceCount;
```

5.字段`limitNumber`表示限购的提示

```
/**
 * 优惠限购
 * 为空表示不限制
 */
private Integer limitNumber;
```

6.增加h5flag字段用于控制是否使用限时特价

```
该字段最初是定义给h5点餐不参与限时特价使用的，后来小程序正餐点餐也需要，也就复用了
是否h5：0否 1是
默认值为0，表示非h5，可以使用限时特价
Integer h5flag
```

## 3.计算接口

**URL：/deal/pay/calculate**

## 4.下单接口

**URL：/deal/menu/shop_cart**

## 5.支付接口

0元支付

**URL：/deal/pay/applet/zero**

收益余额支付/储值金额支付

**URL：/deal/pay/applet/member**

微信支付

**URL：/deal/pay/applet/wechat**

