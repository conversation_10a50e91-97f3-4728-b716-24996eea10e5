CREATE TABLE `hsp_pay_record` (
  `id` bigint(64) unsigned NOT NULL AUTO_INCREMENT,
  `store_guid` varchar(45) DEFAULT NULL,
  `pay_power_id` varchar(45) DEFAULT NULL,
  `pay_power_name` varchar(45) DEFAULT NULL,
  `pay_channel_id` varchar(45) DEFAULT NULL,
  `order_guid` varchar(45) DEFAULT NULL,
  `pay_guid` varchar(45) DEFAULT NULL,
  `amount` decimal(8,2) DEFAULT NULL,
  `order_holder_no` varchar(45) DEFAULT NULL,
  `order_no` varchar(45) DEFAULT NULL,
  `pay_st` varchar(45) DEFAULT NULL,
  `bank_transaction_id` varchar(45) DEFAULT NULL,
  `gmt_time_paid` datetime DEFAULT NULL,
  `ref_order_no` varchar(45) DEFAULT NULL,
  `gmt_refund` datetime DEFAULT NULL,
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP,
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `id_UNIQUE` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=72 DEFAULT CHARSET=utf8mb4;