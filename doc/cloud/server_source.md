## 营业服务 holder_saas_store_business
### Business
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 查看副屏图片 | ^(/business/get/time&#124;/business/query/web)$ | screen_img_query | |
| 新增副屏图片 | ^(/business/time&#124;/business/save)$ | screen_img_create | |
| 删除副屏图片 | ^(/business/delete/[-_a-zA-Z0-9]+)$ | screen_img_delete | |
| 查看副屏图片 | ^(/business/get_config)$ | screen_config_query | |
| 保存副屏图片 | ^(/business/save_config)$ | screen_config_save | |
### PayType
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 查看收款方式 | ^(/payType/getAll&#124;/payType/detail)$ | pay_type_list | |
| 新增收款方式 | ^(/payType/add)$ | pay_type_create | |
| 修改收款方式 | ^(/payType/update&#124;/payType/sort&#124;/payType/config)$ | pay_type_update | |
| 删除收款方式 | ^(/payType/delete)$ | pay_type_delete | |
| 解绑聚合支付 | ^(/payType/unbind)$ | pay_type_unbind | |
### SysDisc
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 查看省零规则 | ^(/system/getAll/[-_a-zA-Z0-9]+)$ | system_discount_query ||
| 新建省零规则 | ^(/system/add)$ | system_discount_create ||
| 修改省零规则 | ^(/system/update)$ | system_discount_update ||
| 启用省零规则 | ^(/system/enable)$ | system_discount_enable ||
| 禁用省零规则 | ^(/system/disable)$ | system_discount_disable ||
| 删除省零规则 | ^(/system/delete/[-_a-zA-Z0-9]+/[-_a-zA-Z0-9]+)$ | system_discount_delete ||
### BizApp
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 快餐设置 | /1 | setting_snack | |
| 语言设置 | /1 | setting_language | |
| 钱箱操作 | /1 | op_cash_box | |
### HomeController
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 正餐主页 | /1 | menu_dine_in_home | |
| 快餐主页 | /1 | menu_snack_home | |
| 外卖主页 | /1 | menu_takeout_home | |
| 订单主页 | /1 | menu_order_home | |
| 会员主页 | /1 | menu_member_home | |
| 设置主页 | /1 | menu_setting_home | |
| 打印设置主页 | /1 | print_setting_home | |
| 收银设置主页 | /1 | cash_setting_home | |
| 操作设置主页 | /1 | operation_setting_home | |
| 排队主页 | /1 | menu_queue_home | |
| 预定主页 | /1 | menu_reserve_home | |
| 寄存主页 | /1 | menu_deposit_home ||
### StoreDeviceController
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 查看设备 | ^(/storeDevice/findAllTerminalType&#124;/storeDevice/findStoreDevice)$ | store_device_query | |
| 设备解绑 | ^(/storeDevice/unbind)$ | store_device_unbind | |
| 设备排序 | ^(/storeDevice/sort)$ | store_device_sort | |
### SmsController
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 获取充值列表 | ^(/shortMsg/index)$ | shortMsg_index | | 
| 充值 | ^(/shortMsg/charge)$ | shortMsg_charge | |
### SurchargeController
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 新建附加费 | ^(/area/query/all&#124;/surcharge/create)$ | surcharge_create | |
| 查看附加费 | ^(/surcharge/list_by_type)$ | surcharge_query | |
| 编辑附加费 | ^(/surcharge/query&#124;/surcharge/update)$ | surcharge_update | |
| 启用、停用附加费 | ^(/surcharge/enable)$ | surcharge_enable | |
| 删除附加费 | ^(/surcharge/delete)$ | surcharge_delete | |
| 批量启用、停用附加费 | ^(/surcharge/batch_enable)$ | surcharge_batch_enable | |
| 批量删除附加费 | ^(/surcharge/batch_delete)$ | surcharge_batch_delete | |
### reasonDictionary
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 删除'原因列表' | ^(/reason/deleteReason)$ | delet_reason | | 
| 查询'原因列表' | ^(/reason/findReason)$ | find_reason | | 
| 查询原因类型 | ^(/reason/findReasonType)$ | find_reason_type | | 
| 新增'原因列表' | ^(/reason/insertReason)$ | insert_reason | | 
| 修改'原因列表' | ^(/reason/updateReason)$ | update_reason | |

## 商品服务 holder_saas_store_item
### Brand
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 查看商品 | ^(/item/get_item_info&#124;/item/brand/select_item_for_web)$ | item_brand_query | |
| 修改商品 | ^(/item/brand/update_item&#124;/item_pkg/brand/update_pkg)$ | item_brand_update | |
| 上架商品 | ^(/item/brand/rack)$ | item_brand_rack | |
| 下架商品 | ^(/item/brand/un_rack)$ | item_brand_un_rack | |
| 删除商品 | ^(/item/brand/delete_item)$ | item_brand_delete | |
| 批量上架 | ^(/item/brand/batch_rack)$ | item_brand_batch_rack | |
| 批量下架 | ^(/item/brand/batch_un_rack)$ | item_brand_batch_un_rack | |
| 批量删除商品 | ^(/item/brand/batch_delete)$ | item_brand_batch_delete | |
| 批量导入商品 | ^(/item_file/brand/batch_import)$ | item_brand_batch_import | |
| 批量同步门店 | ^(/item/select_sku_list_from_push&#124;/item/brand/push_item)$ | item_brand_batch_push | |
| 查看分类 | ^(/type/brand/query_type)$ | type_brand_query | |
| 新建分类 | ^(/type/brand/save&#124;/type/brand/quick_save)$ | type_brand_create | |
| 修改分类 | ^(/type/brand/update)$ | type_brand_update | |
| 删除分类 | ^(/type/brand/delete)$ | type_brand_delete | |
| excel批量导入 | ^(/item_file/brand/batch_import_item)$ | brand_batch_import_from_excel | |
| 批量保存商品 | ^(/item/brand/batch_import_item)$ | brand_batch_save_item | |
### BrandItem
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 新建单品 | ^(/attr/brand/select_attr_list_for_save_item&#124;/item/brand/save)$ | item_brand_create | |
### BrandPkg
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 新建套餐 | ^(/item_pkg/brand/save_pkg&#124;/item_pkg/brand/select_sku_list)$ | item_pkg_brand_create | |
### Store
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 查看商品 | ^(/item/get_item_info&#124;/item/store/select_item_for_web)$ | item_store_query | |
| 修改商品 | ^(/item/store/update_item&#124;/item_pkg/store/update_pkg)$ | item_store_update | |
| 上架商品 | ^(/item/store/rack)$ | item_store_rack | |
| 下架商品 | ^(/item/store/un_rack)$ | item_store_un_rack | |
| 删除商品 | ^(/item/store/delete_item)$ | item_store_delete | |
| 批量上架 | ^(/item/store/batch_rack)$ | item_store_batch_rack | |
| 批量下架 | ^(/item/store/batch_un_rack)$ | item_store_batch_un_rack | |
| 批量删除商品 | ^(/item/store/batch_delete)$ | item_store_batch_delete | |
| 批量导入商品 | ^(/item_file/store/batch_import)$ | item_store_batch_import | |
| 批量导出商品 | ^(/item_file/store/download_item)$ | item_store_batch_export | |
| 查看分类 | ^(/type/store/query_type)$ | type_store_query | |
| 新建分类 | ^(/type/store/save&#124;/type/store/quick_save)$ | type_store_create | |
| 修改分类 | ^(/type/store/update)$ | type_store_update | |
| 删除分类 | ^(/type/store/delete)$ | type_store_delete | |
| excel批量导入 | ^(/item_file/store/batch_import_item)$ | store_batch_import_from_excel | |
| 批量保存商品 | ^(/item/store/batch_import_item)$ | store_batch_save_item | |
### StoreItem
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 新建单品 | ^(/attr/store/select_attr_list_for_save_item&#124;/item/store/save)$ | item_store_create | |
### StorePkg
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 新建套餐 | ^(/item_pkg/store/save_pkg&#124;/item_pkg/store/select_sku_list)$ | item_pkg_store_create | |
### BrandAttr
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 新建属性组 | ^(/attr/brand/save_attr_group)$ | attr_group_brand_create | |
| 修改属性组 | ^(/attr/brand/set_attr_group)$ | attr_group_brand_update | |
| 查看属性组 | ^(/type/brand/query_type&#124;/attr/brand/list_attr_group)$ | attr_group_brand_list | |
| 删除属性组 | ^(/attr/brand/delete_attr_group)$ | attr_group_brand_delete | |
| 新建属性 | ^(/attr/brand/save_attr)$ | attr_brand_create | |
| 查看属性 | ^(/attr/brand/list_attr_by_group)$ | attr_brand_list | |
| 修改属性 | ^(/attr/brand/update_attr)$ | attr_brand_update | |
| 删除属性 | ^(/attr/brand/delete_attr)$ | attr_brand_delete | |
### StoreAttr
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 新建属性组 | ^(/attr/store/save_attr_group)$ | attr_group_store_create | |
| 修改属性组 | ^(/attr/store/set_attr_group)$ | attr_group_store_update | |
| 查看属性组 | ^(/type/store/query_type&#124;/attr/store/list_attr_group)$ | attr_group_store_list | |
| 删除属性组 | ^(/attr/store/delete_attr_group)$ | attr_group_store_delete | |
| 新建属性 | ^(/attr/store/save_attr)$ | attr_store_create | |
| 查看属性 | ^(/attr/store/list_attr_by_group)$ | attr_store_list | |
| 修改属性 | ^(/attr/store/update_attr)$ | attr_store_update | |
| 删除属性 | ^(/attr/store/delete_attr)$ | attr_store_delete | |
### EstimateController
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 查询沽清 | ^(/type/store/query_type&#124;estimate/select_estimate_list_store)$ | estimate_query | |
| 查询剩余量 | ^(/type/store/query_type&#124;estimate/select_estimate_itme_residue_store)$ | estimate_remain | |
| 设置是否限量、次日置满 | ^(/estimate/save)$ | estimate_strategy_config | |
| 设置置满时间 | ^(/config/select_estimate_reset_time&#124;config/save_estimate_reset_time)$ | estimate_reset_time | |
### item_template
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 销售模板 | (^/item_template/select_store_item_templates&#124;/user_data/query_store_spinner &#124;/item_template/save$) | sale_template | |
| 模板菜单 | (^/item_template/select_item_template_menus&#124;/item_template/save_menu$) | template_menu | |
| 添加商品 | (^/type/query_type_by_stores&#124;/item/select_sku_item_list&#124;/item_template/save_menu$) | add_goods | |
| 编辑商品 | (^/api/type/query_type_by_stores&#124;/item_template/select_item_template_menu_detail/item/select_sku_item_list&#124;/item_template/batch_remove&#124;/item_template/save_menu$) | edit_goods | |
### ItemTemplate
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 查询模板 | ^(/item_template/select_store_item_templates)$ | query_template | |
| 新建模板 | ^(/item_template/save)$ | create_template | |
| 编辑模板 | ^(/item_template/save)$ | update_template | |
| 删除模板 | ^(/item_template/save)$ | delete_template | |
| 模板菜单 | ^(/item_template/select_item_template_menus&#124;/item_template/select_item_template_menu_detail&#124;/item_template/batch_remove&#124;/item_template/save_menu)$ | template_menu | |

## 会员服务 holder_saas_store_member
### Member
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 查看会员 | ^(/member/memberGrades&#124;/member/member_list)$ | member_list |
| 查看会员规则 | ^(/grade/get_account_validity&#124;/grade/member_grade_list&#124;/grade/member_grade_detail)$ | member_rule |
| 设置账号有效期 | ^(/grade/update_account_validity)$ | member_account_validity_update |
| 添加等级 | ^(/grade/add_member_grade)$ | member_grade_create |
| 编辑等级规则 | ^(/grade/update_member_grade)$ | member_grade_update |
| 删除等级 | ^(/grade/delete_member_grade)$ | member_grade_delete |
| 查看会员详情 | ^(/member/memberGrades&#124;/member/memberDetail&#124;/member/member_data_download&#124;/member/memberCards&#124;/member/memberConsumeRecords&#124;/member/member_transactions/pay&#124;/member/member_transactions/charge&#124;/member/getMemberIntegralType&#124;/member/getMemberIntegralRecords)$ | member_query |
### MemberAppController
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 查询会员 | /1 | member_query | |
| 注册会员 | /1 | member_registry | |
| 修改会员 | /1 | member_update | |
| 会员充值 | /1 | member_recharge | |
| 重置密码 | /1 | member_pwd_reset | |
| 修改密码 | /1 | member_pwd_update | |
| 查询消费记录 | /1 | member_consume_record_list | |
 
## 打印服务 holder_saas_store_print
### PrinterController
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 添加打印机 | /1 | printer_front_create | |
| 修改打印机 | /1 | printer_front_update | |
| 删除打印机 | /1 | printer_front_delete | |
| 添加打印机 | /1 | printer_kitchen_create | |
| 修改打印机 | /1 | printer_kitchen_update | |
| 删除打印机 | /1 | printer_kitchen_delete | |
| 添加打印机 | /1 | printer_label_create | |
| 修改打印机 | /1 | printer_label_update | |
| 删除打印机 | /1 | printer_label_delete | |
### PrinterMchController
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 查询打印模板 | ^(/format/list)$ | format_list ||
| 添加打印模板 | ^(/format/add)$ | format_add ||
| 删除打印模板 | ^(/format/delete)$ | format_delete ||


## 员工服务 holder_saas_store_staff
### User
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 查看帐号 | ^(/user/query&#124;/user/page_query&#124;/user_data/query&#124;/organization/query_all_organization)$ | employee_query | |
| 新建帐号 | ^(/store/query_all_store&#124;/organization/query_all_organization&#124;/brand/query_list&#124;/user/create&#124;/user_data/save)$ | employee_create | | 
| 修改帐号 | ^(/store/query_all_store&#124;/organization/query_all_organization&#124;/brand/query_list&#124;/user/update&#124;/user_data/update)$ | employee_update | | 
| 启用帐号 | ^(/user/enable)$ | employee_enable | | 
| 禁用帐号 | ^(/user/disable)$ | employee_disable | | 
| 删除帐号 | ^(/user/delete)$ | employee_delete | | 
| 修改密码 | ^(/user/update_pwd&#124;/user/reset_pwd)$ | employee_change_pwd | | 
### Role
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 查看角色 | ^(/role/query_page_by_name)$ | role_query | |
| 新建角色 | ^(/role/create)$ | role_create | |
| 修改角色 | ^(/role/update)$ | role_update | |
| 复制角色 | ^(/role/copy_role)$ | role_copy | |
| 删除角色 | ^(/role/delete&#124;/role/query_exist_user)$ | role_delete | |
| 设置权限 | ^(/role_data/save&#124;/role_data/query_role_terminal&#124;/role_data/query_role_terminal_data)$ | role_data_save | |

## 桌台服务 holder_saas_store_table
### Area
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 查看区域 | ^(/area/query/all)$ | area_list | |
| 新建区域 | ^(/area/add)$ | area_create | |
| 修改区域 | ^(/area/update)$ | area_update | |
| 删除区域 | ^(/area/delete)$ | area_delete | |

### Table
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 查看桌位 | ^(/table/web/query)$ | table_list | |
| 新建桌位 | ^(/table/add)$ | table_create | |
| 批量新建桌位 | ^(/table/batch/create)$ | table_batch_create | |
| 修改桌位 | ^(/table/update)$ | table_update | |
| 删除桌位 | ^(/table/delete)$ | table_delete | |
| 批量删除桌位 | ^(/table/deleteAll)$ | table_batch_delete | |

## 外卖服务 holder_saas_store_takeaway
### MeiTuan
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 查看授权门店 | ^(/takeout/query_store_auth)$ | meituan_store_list | |
| 帐号绑定 | ^(/takeout/shop_binding_url)$ | meituan_store_bind_unbind | |
| 菜品绑定 | ^(/item/mapping&#124;/takeout/item_binding_url)$ | meituan_item_bind_unbind | |
### EleMe
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 查看授权门店 | ^(/takeout/query_store_auth)$ | eleme_store_list | |
| 帐号绑定 | ^(/takeout/shop_binding_url)$ | eleme_store_bind_unbind | |
| 菜品绑定 | ^(/takeout/item_binding_url)$ | eleme_item_bind_unbind | |
### TakeoutController1
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 取消订单 | /1 | takeout_cancel_order | |
| 作废订单 | /1 | order_takeout_invalid | |
| 外卖设置 | /1 | setting_takeout | |
### GroupByController
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 查看 | ^(/takeout/query_tuangou_auth_by_store)$ | group_by_store_list | |
| 绑定 | ^(/takeout/group_buy_binding_url)$ | group_by_store_bind | |
| 解绑 | ^(/takeout/group_buy_binding_url)$ | group_by_store_unbind | |
### TakeoutController
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 查看 | ^(/takeout/query_takeout_auth_by_store)$ | takeout_store_list | |
| 绑定 | ^(/takeout/shop_binding_url)$ | takeout_store_bind | |
| 解绑 | ^(/takeout/shop_binding_url)$ | takeout_store_unbind | |
| 关联商品 | ^(/item_mapping/query&#124;/item_mapping/bind&#124;/item_mapping/unbind&#124;/item_mapping/batch_unbind)$ | takeout_item_bind_unbind | |
### PosController
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 主页 | /pos | pos_takeaway_index | |

## 交易服务 holder_saas_store_trade
### NewOrderController
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 赠送菜品 | /1 | dine_in_dish_gift |  |
| 退菜 | /1 | dine_in_dish_refund |  |
| 结账 | /1 | dine_in_checkout |  |
| 整单折扣 | /1 | dine_in_discount |  |
| 整单让价 | /1 | dine_in_allowance |  |
| 现金支付 | /1 | dine_in_cash_pay |  |
| 聚合支付 | /1 | dine_in_jh_pay |  |
| 银行卡支付 | /1 | dine_in_bank_card_pay |  |
| 会员余额支付 | /1 | dine_in_member_card_pay |  |
| 自定义支付 | /1 | dine_in_self_defined_pay |  |
| 结账 | /1 | order_dine_in_checkout |  |
| 反结账 | /1 | order_dine_in_recovery |  |
| 作废订单 | /1 | order_dine_in_invalid |  |
| 人脸支付 | /1 | dine_in_face_pay |  |
### SnackOrderController
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 赠送菜品 | /1 | snack_dish_gift |  |
| 退菜 | /1 | snack_dish_refund |  |
| 结账 | /1 | order_snack_checkout |  |
| 反结账 | /1 | order_snack_recovery |  |
| 作废订单 | /1 | order_snack_invalid |  |
| 结账 | /1 | snack_checkout |  |
| 整单折扣 | /1 | snack_discount |  |
| 整单让价 | /1 | snack_allowance |  |
| 现金支付 | /1 | snack_cash_pay |  |
| 聚合支付 | /1 | snack_jh_pay |  |
| 银行卡支付 | /1 | snack_bank_card_pay |  |
| 会员余额支付 | /1 | snack_member_card_pay |  |
| 人脸支付 | /1 | snack_face_pay |  |
| 自定义支付 | /1 | snack_self_defined_pay |  |
### PosNewOrderController
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 赠菜 | /pos | pos_dine_in_dish_gift |  |
| 退菜 | /pos | pos_dine_in_dish_refund |  |
| 结账 | /pos | pos_dine_in_checkout |  |
| 现金 | /pos | pos_dine_in_cash_pay |  |
| 聚合支付 | /pos | pos_dine_in_jh_pay |  |
| 会员支付 | /pos | pos_dine_in_member_card_pay |  |
| 更多 | /pos | pos_dine_in_last_all_pay |  |
| 验券 | /pos | pos_check_ticket |  |
| 主页 | /pos | pos_dine_in_index |  |
### posOrderReceiveController
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 主页 | /pos | pos_order_receive_index |  |


## 组织服务 holder_saas_store_organization
### Store
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 查看门店 | ^(/store/query_by_condition&#124;/store/query_all_store&#124;/store/query_store_by_guid)$ | store_list |  |
| 创建门店 | ^(/brand/query_list&#124;/organization/query_enterprise_and_organization&#124;/store/create)$ | store_create |  |
| 修改门店 | ^(/brand/query_list&#124;/organization/query_enterprise_and_organization&#124;/store/update)$ | store_update |  |
| 启用门店 | ^(/store/enable)$ | store_enable |  |
| 禁用门店 | ^(/store/disable)$ | store_disable |  |
| 删除门店 | ^(/store/delete)$ | store_delete |  |
### Brand
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 查看品牌 | ^(/brand/query_list)$ | brand_list |  |
| 新建品牌 | ^(/brand/create)$ | brand_create |  |
| 修改品牌 | ^(/brand/update)$ | brand_update |  |
| 删除品牌 | ^(/brand/query_exist_store_account&#124;/brand/delete)$ | brand_delete |  |
### Org
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 查看组织 | ^(/organization/query_enterprise_and_organization&#124;/organization/query_all_organization)$ | organization_list |  |
| 新建组织 | ^(/organization/create)$ | organization_create |  |
| 编辑组织 | ^(/organization/get_optional_organization&#124;/organization/update)$ | organization_update |  |
| 删除组织 | ^(/organization/delete)$ | organization_delete |  |


## 微信服务 holder_saas_store_weixin
### WxController
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 获取业务配置 | ^(/wx_store_order_config/get_detail_config)$ | get_detail_config |  |
| 批量配置| ^(/wx_store_order_config/list_could_edit_store)&#124;(/wx_store_order_config/update_batch_store_config)&#124;(/wx_store_order_config/get_detail_config)&#124;(/wx_store_order_config/list_could_edit_store)$ | batch_update_store_config |  |
| 编辑 | ^(/wx_store_order_config/get_detail_config)&#124;(/wx_store_order_config/update_store_config)$ | list_could_edit_store |  |
| 复制 | ^(/wx_store_order_config/update_batch_store_config)&#124;(/wx_store_order_config/get_detail_config)&#124;(/wx_store_order_config/list_could_edit_store)$| update_batch_store_config |  |
| 主页 | ^(/wx_brand_auth/list_brand_auth)$ | list_brand_auth |  |
| 立即绑定 | ^(/wx_open/get_pre_code)&#124;(/wx_brand_auth/get_by_brand_guid)$ | get_pre_code |  |
| 订单配置列表 | ^(/wx_store_order_config/list_order_config)&#124;(/wx_store_status/page_wx_store_status)$ | list_order_config |  |
| 解除绑定 | ^(/wx_brand_auth/send_message)&#124;(/wx_brand_auth/un_band_brand)$ | un_band_brand |  |
| 修改概览配置 | ^(/wx_store_status/update_status_by_guid)$ | wx_store_status_update |  |
| 查询排队配置 | ^(/wx_queue/page_config)$ | wx_queue_config_page |  |
| 修改排队配置 | ^(/wx_queue/get_by_guid)&#124;(/wx_queue/update_by_guid)$ | wx_queue_config_update |  |
| 批量修改排队配置 | ^(/wx_queue/batch_update)$ | wx_queue_config_batch_update |  |
### TableTipsController
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 新建桌贴 | ^(/wx_table_stick/find_by_guid)&#124;(/wx_table_stick/save_my_stick)$ | create_stick_zip |  |
| 下载桌贴 | ^(/wx_table_stick/create_stick_zip)&#124;(/wx_table_stick/download_stick_zip)&#124;(/wx_table_stick/list_model_and_ticket)&#124;(/wx_brand_auth/query_by_store_guid)$ | download_stick_zip |  |
| 删除模版 | ^(/wx_table_stick/delete_my_model)$ | delete_my_model |  |
| 编辑桌贴 | ^(/wx_table_stick/update_my_stick)&#124;(/wx_table_stick/find_by_guid)$ | edit_stick |  |
| ----- | ^(/wx_table_stick/save_my_stick)$ | save_my_stick |  |
| 桌贴删除 | ^(/wx_table_stick/delete_my_stick)$ | delete_my_stick |  |
| 主页 | ^(/wx_stick_shop_cart/list_shop_cart)&#124;(/wx_stick_shop_cart/add_models)&#124;(/wx_table_stick/list_stick_category)&#124;(/wx_table_stick/list_stick_model)&#124;(/wx_table_stick/list_model_and_ticket)&#124;(/wx_stick_order/order)&#124;(/wx_stick_order/polling)$ | table_tips_index |  |
| 购物车 | ^(/wx_stick_shop_cart/list_shop_cart)&#124;(/wx_stick_shop_cart/add_models)&#124;(/wx_stick_shop_cart/remove_models)$ | models |  |
## 日志服务 holder_saas_store_log
### Mchnt
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 查看 | ^(/log/module&#124;/log/list)$ | report_mchnt_op_log |  |
### CashOp
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 查看 | ^(/log/list)$ | report_cashier_op_log |  |
### CashOrder
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 查看 | ^(/order/getOrderSource&#124;/order/getOrderLog&#124;/order/getNormalOrderLogDetail&#124;/order/getTakeAwayOrderLogDetail&#124;/order/order_detail)$ | report_cashier_order_log |  |

## 排队服务 holder_saas_store_queue
### QueueController
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
|创建、编辑队列 | ^(/queue/save)$ | queue_create |  |
|查看队列 | ^(/queue/query)$ | queue_query |  |
|编辑桌位 | ^(/queue/table/all&#124;/queue/table/save)$ | queue_table |  |
|删除队列 | ^(/queue/delete)$ | queue_delete |  |
|队列设置 | ^(/queue/config)$ | queue_config |  |
### PosController
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 主页 | /pos | pos_queue_index |  |

## 预定服务 holder_saas_store_reserve
### ReserveController
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 新增、修改时段 | ^(/reserve/config/update)$ | reserve_period_create |  |
| 查看时段 | ^(/reserve/config)$ | reserve_period_query |  |
### PosController
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 主页 | /pos | pos_reserve_index |  |
## 进销存服务 holder_saas_store_erp
### BomController
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 添加bom配置 | ^(/bom/add)$ | /bom/add |  |
| 查询商品bom配置 | ^(/bom/findByGoods)$ | /bom/findByGoods |  |
| 查询商品分类列表 | ^(/bom/findTypeByStore)$ | /bom/findTypeByStore |  |
| 根据分类查询商品列表 | ^(/bom/findGoodsListByType)$ | /bom/findGoodsListByType |  |
| 根据名称查询商品列表 | ^(/bom/findGoodsListByName)$ | /bom/findGoodsListByName |  |
### CheckoutDocumentController
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 添加或者更新盘点单 | ^(/checkoutDocument/addOrUpdateCheckoutDocument)$ | /checkoutDocument/addOrUpdateCheckoutDocument | |
| 查询物料信息新增物料时使用 | ^(/checkoutDocument/selectDocumentDetailForAdd)$ | /checkoutDocument/selectDocumentDetailForAdd | |
| 查询盘点单及其明细编辑时使用 | ^(/checkoutDocument/selectDocumentAndDetailForUpdate)$ | /checkoutDocument/selectDocumentAndDetailForUpdate | |
| 查询盘点单及其明细 | ^(/checkoutDocument/selectDocumentAndDetailForSelect)$ | /checkoutDocument/selectDocumentAndDetailForSelect | |
| 删除盘点单 | ^(/checkoutDocument/deleteDocument)$ | /checkoutDocument/deleteDocument | |
| 提交盘点单 | ^(/checkoutDocument/submitCheckoutDocument)$ | /checkoutDocument/submitCheckoutDocument | |
| 分页查询盘点单列表 | ^(/checkoutDocument/selectCheckoutDocumentForPage)$ | /checkoutDocument/selectCheckoutDocumentForPage | |
### InOutDocumentController
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 插入或者更新入库单据及其明细 | ^(/inOutDocument/insertOrUpdate)$ | /inOutDocument/insertOrUpdate |  |
| 查询出入库单据中的物料信息 | ^(/inOutDocument/selectMaterialListForAdd)$ | /inOutDocument/selectMaterialListForAdd |  |
| 查询对应入库单的退货数量小于入库数量的物 | ^(/inOutDocument/selectMaterialListForReturn)$ | /inOutDocument/selectMaterialListForReturn |  |
| 提交出入库单 | ^(/inOutDocument/submitInOutDocument)$ | /inOutDocument/submitInOutDocument |  |
| 删除出入库单 | ^(/inOutDocument/deleteDocument)$ | /inOutDocument/deleteDocument |  |
| 查询关联单据 | ^(/inOutDocument/selectDocumentGuidList)$ | /inOutDocument/selectDocumentGuidList |  |
| 查询出入库单据及其明细编辑时使用 | ^(/inOutDocument/selectDocumentAndDetailForUpdate)$ | /inOutDocument/selectDocumentAndDetailForUpdate |  |
| 查询出入库单据及其明细仅查看时使用 | ^(/inOutDocument/selectDocumentAndDetailForSelect)$ | /inOutDocument/selectDocumentAndDetailForSelect |  |
| 查询出入库列表 | ^(/inOutDocument/selectDocumentListForPage)$ | /inOutDocument/selectDocumentListForPage |  |
| 供应商对账表 | ^(/inOutDocument/reconciliation)$ | /inOutDocument/reconciliation |  |
| 结算总金额 | ^(/inOutDocument/reconciliation/total)$ | /inOutDocument/reconciliation/total |  |
| 结算 | ^(/inOutDocument/reconciliation/settle)$ | /inOutDocument/reconciliation/settl |  |
| 出入库流水明细查看 | ^(/inOutDocument/selectFlowDetailListForPage)$ | /inOutDocument/selectFlowDetailListForPage |  |
### materialCategory
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 添加物料分类 | ^(/materialCategory/add)$ | /materialCategory/add |  |
| 修改物料分类 | ^(/materialCategory/update)$ | /materialCategory/update |  |
| 根据分类GUID查询分类信息 | ^(/materialCategory/findByGuid)$ | /materialCategory/findByGuid |  |
| 条件查询分类信息列表 | ^(/materialCategory/findByCondition)$ | /materialCategory/findByCondition |  |
| 查询所有的分类信息,不包括物料 | ^(/materialCategory/list)$ | /materialCategory/list |  |
| 根据分类GUID统计某分类下的物料使用数 | ^(/materialCategory/countCategory)$ | /materialCategory/countCategory |  |
| 查询所有的分类物料信息,包括物料 | ^(/materialCategory/listCategory)$ | /materialCategory/listCategory |  |
| 删除物料分类 | ^(/materialCategory/delete)$ | /materialCategory/delete |  |
### MaterialController
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 添加物料信息 | ^(/material/add)$ | /material/add |  |
| 根据GUID删除物料信息 | ^(/material/delete)$ | /material/delete |  |
| 修改物料信息 | ^(/material/update)$ | /material/update |  |
| 启用禁用物料信息 | ^(/material/changeStatus)$ | /material/changeStatus |  |
| 条件查询物料信息 | ^(/material/findByCondition)$ | /material/findByCondition |  |
| 根据GUID查询物料信息 | ^(/material/findByGuid)$ | /material/findByGuid |  |
| 根据GUID查询物料配置的bom数量 | ^(/material/countBom)$ | /material/countBom |  |
| 根据物料GUID列表查询物料信息 | ^(/material/findByGuidList)$ | /material/findByGuidList |  |
| 导入物料信息 | ^(/material/import)$ | /material/import |  |
| 下载物料模板 | ^(/material/template)$ | /material/template |  |
| 导出物料信息 | ^(/material/export)$ | /material/export |  |
| 生成物料code | ^(/material/generatorCode)$ | /material/generatorCode |  |
| 条件查询库存列表 | ^(/material/findStockByCondition)$ | /material/findStockByCondition |  |
### MaterialUnitController
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 增加单位 | ^(/materialUnit)$ | /materialUnit |  |
| 获取单位列表 | ^(/materialUnit/list)$ | /materialUnit/list |  |
### PricingSchemesController
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 保存报价方案 | ^(/pricing)$ | /pricing |  |
| 报价方案列表 | ^(/pricing/query/[-_a-zA-Z0-9]+)$ | /pricing/query |  |
| 删除物料报价信息 | ^(/pricing/delete/[-_a-zA-Z0-9]+)$ | /pricing/delete |  |
| 启禁用物料报价 | ^(/pricing/enable/[-_a-zA-Z0-9]+)$ | /pricing/enable |  |
| 批量查询物料协议单价 | ^(/pricing/batch)$ | /pricing/batch |  |
| 根据物料GUID列表查询物料信息 | ^(/pricing/findByGuidList)$ | /pricing/findByGuidList |  |
### StockController
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| ERP库存 | ^(/stock)$ | /stock |  | 
### SuppliersController
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 新建供应商 | ^(/suppliers/create)$ | /suppliers/create |  |
| 更新供应商 | ^(/suppliers/update)$ | /suppliers/update |  |
| 查询供应商信息 | ^(/suppliers/query/one/[-_a-zA-Z0-9]+)$ | /suppliers/query/one |  |
| 启禁用供应商 | ^(/suppliers/enable/[-_a-zA-Z0-9]+)$ | /suppliers/enable |  |
| 删除供应商 | ^(/suppliers/delete/[-_a-zA-Z0-9]+)$ | /suppliers/delete |  |
| 供应商列表 | ^(/suppliers/query/list)$ | /suppliers/query/list |  |
| 供应商下拉列表 | ^(/suppliers/query/all)$ | /suppliers/query/all |  |
### WarehouseController
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 创建仓库 | ^(/warehouse/create)$ | /warehouse/create |  |
| 更新仓库 | ^(/warehouse/update)$ | /warehouse/update |  |
| 查询仓库信息 | ^(/warehouse/query/one/[-_a-zA-Z0-9]+)$ | /warehouse/query/one |  |
| 查询仓库列表 | ^(/warehouse/query/list)$ | /warehouse/query/list |  |
| 仓库下拉列表 | ^(/warehouse/name)$ | /warehouse/name |  |
| 启禁用仓库 | ^(/warehouse/enable/[-_a-zA-Z0-9]+)$ | /warehouse/enable |  |
| 仓库解锁或锁定 | ^(/warehouse/lock/[-_a-zA-Z0-9]+)$ | /warehouse/lock |  |
| 删除仓库 | ^(/warehouse/delete/[-_a-zA-Z0-9]+)$ | /warehouse/delete |  |
| 生产仓库编号 | ^(/warehouse/code)$ | /warehouse/code |  |

## 支付服务 holder_saas_store_pay
### posFastCheckOut
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 扫码收款 | /pos | pos_fast_pay |  |
| 快速收款记录 | /pos | pos_fast_pay_record |  |
| 退款 | /pos | pos_fast_refund |  |
| 主页 | /pos | pos_fast_pay_index |  |
 
## 报表服务 holder_saas_store_report
### BusController
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 营业概况 | ^(/busSituation/businessData&#124;/busSituation/busiHisTrend)$ | business_data |  |
### storeGatherController
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 门店汇总 | ^(/storeGather/storeGatherList&#124;/journal/export/0)$ | store_gather |  |
### orderDetailController
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 订单明细 | ^(/order_detail/page&#124;/journal/export/1)$ | order_detail |  |
### saleDetailController
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 销售明细 | ^(/sale_detail/item_type&#124;/sale_detail/page&#124;/journal/export/2)$ | sale_detail |  |
### ScreenController
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 实时营业统计 | ^(/screen/business_data)$ | current_business_data |  |
| 实时商品统计 | ^(/screen/sale_count)$ | current_sale_count |  |
| 实时门店统计 | ^(/screen/store)$ | current_business_store |  |
| 实时折线图统计 | ^(/screen/sale_by_hour/statistics)$ | current_statistics |  |
| 会员年龄分布 | ^(/screen/hsm-member-statistics/ageDistribution)$ | member_age_distribution |  |
| 会员增长值 | ^(/screen/hsm-member-statistics/memberGrowth)$ | member_growth |  |
| 查询会员消费信息 | ^(/screen/hsm-member-statistics/querySexAndConsume)$ | sex_and_consume |  |

## 会员账户服务 holder_saas_store_member_account
### HsmMemberController
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 保存会员信息-dep | ^(/api/hsm_member_list_new/addMember)$ | todo | |
| 会员列表展示-dep | ^(/api/hsm_member_list_new/memberPageList)$ | todo | |
| 精准营销-dep | ^(/hsm-member-data-statistics/query_by_condition)$ | todo | |
| 会员渠道-dep | ^(/hsm-become-member-condition/getByEnterpriseGuid)$ | todo | |
| 获取会员渠道 | ^(/hsm-become-member-condition/getByEnterpriseGuid)$ | todo | |
| 更新会员渠道 | ^(/hsm-become-member-condition/update)$ | todo | |
| 精准营销 | ^(/hsm-member-data-statistics/query_by_condition&#124;/hsm-volume-info/queryList&#124;/hsm-member-info-volume/platform-save&#124;/hsm-member-info-volume/platform-withdraw)$ | todo ||
| 结算规则 | ^(/hsm_settlement_rule/getDetails)$ | todo | |
| 会员详情 | ^(/hsm_member/getOne&#124;/hsm/system/management/card/query&#124;/hsm-card-level/list/[_a-zA-Z0-9]+&#124;/hsm/system/management/guid/[_a-zA-Z0-9]+)$ | todo | |
| 更新会员 | ^(/hsm_member/update)$ | todo | |
| 新增会员 | ^(/hsm/system/management/card/query&#124;/hsm-card-level/list/[_a-zA-Z0-9]+&#124;/hsm/system/management/guid/[_a-zA-Z0-9]+&#124;/api/hsm_member_list_new/addMember)$ | todo | |
| 会员列表 | ^(/api/hsm_member_list_new/memberPageList)&#124;(/hsm/member/enterprise/have/system)$ | todo | |
| 会员启用 | ^(/api/hsm_member_list_new/enableMember)$ | todo | |
| 会员禁用 | ^(/api/hsm_member_list_new/disableMember)$ | todo | |
| 会员删除 | ^(/api/hsm_member_list_new/deleteMember)$ | todo | |
| 会员详情 | ^(/hsm_member/getOne&#124;/hsm_member/statisticsBaseData)$ | todo | |
| 赠送卡 | ^(/hsm_member/giftCard)$ | todo | |
| 查询体系卡 | ^(/hsm_member/listCardBySystemGuid)$ | todo | |
| 查询体系 | ^(/hsm_member/listMemberSystemByMemberGuid)$ | todo | |
| 启用账号 | ^(/api/hsm_member_list_new/enableMember)$ | todo | |
| 禁用账号 | ^(/api/hsm_member_list_new/disableMember)$ | todo | |
| 删除账号 | ^(/api/hsm_member_list_new/deleteMember)$ | todo | |
| 修改信息 | ^(/hsm_member/update)$ | todo | |
| 查询优惠券 | ^(/hsm_member_coupon/listByCondition)$ | todo | |
| 查询消费 | ^(/hsm_member_consume/listByCondition&#124;/hsm_member_consume/statisticsConsumeBaseData&#124;/hsm_member/getCardNameByMember&#124;/hsm_member_consume/syncConsumeRecord)$ | todo | |
| 查询积分 | ^(/hsm_member_integral/listByCondition&#124;/hsm_member_integral/listSourceType&#124;/hsm_member_integral/statisticsConvertibleIntegral)$ | todo | |
| 调整积分 | ^(/hsm_member_integral/changeIntegral)$ | todo | |
| 查询成长值 | ^(/hsm_member_growth_value/listByCondition&#124;/hsm_member_growth_value/listSourceType)$ | todo | |
| 调整成长值 | ^(/hsm_member_growth_value/changeGrowthValue)$ | todo | |
| 查询余额 | ^(/hsm_member_balance/listByCondition&#124;/hsm_member_balance/listSourceType&#124;/hsm_member_balance/statistical)$ | todo | |
| 调整余额 | ^(/hsm_member_balance/changeBalance)$ | todo | |
| 赠送记录获取 | ^(/hsm-member-info-volume/platform-list)$ | todo | |
| 重置密码 | ^(/hsm_member/resetPassword)$ | todo | |
| 新增标签分组 | ^(/hsm-label-group/insert_group_label)$ | todo | |
| 查询所有标签分组 | ^(/hsm-label-group/query_group_label)$ | todo | |
| 编辑标签分组 | ^(/hsm-label-group/update_group_label)$ | todo | |
| 删除标签分组 | ^(/hsm-label-group/delete_group_label)$ | todo | |
| 保存标签 | ^(/hsm-label-setting/save)$ | todo | |
| 更新标签 | ^(/hsm-label-setting/update)$ | todo | |
| 标签列表 | ^(/hsm-label-setting/listByCondition)$ | todo | |
| 体系下所有卡的列表 | ^(/hsm/system/management/card/queryCardAll)$ | todo | |
| 标签详情 | ^(/hsm-label-setting/getOne)$ | todo | |
| 标签删除 | ^(/hsm-label-setting/delete)$ | todo | |
| 精准营销标签列表查询 | ^(/hsm-member-data-statistics/query_by_label)$ | todo | |
| 会员详情标签查询 | ^(/hsm-member-data-statistics/get_member_label)$ | todo |
### HsmSystemManagementController
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 系统体系列表-dep | ^(/hsm/system/management/query)$ | hsm_system_management_list | |
| 新建体系-dep | ^(/hsm/system/management/save)$ |  hsm_system_management_save | |
| 体系列表 | ^(/hsm/system/management/query)$ | todo | |
| 删除体系 | ^(/hsm/system/management/[_a-zA-Z0-9]+)$ | todo | |
| 修改门店校验 | ^(/hsm/system/management/check/modify/shops)$ | todo | |
| 确认保存 | ^(/hsm/system/management/modify/shops)$ | todo | |
| 修改体系名称保存 | ^(/hsm/system/management/modify/name)$ | todo | |
| 体系设置校验保存 | ^(/hsm/system/management/check/save)$ | todo | |
| 体系设置门店异常仍保存 | ^(/hsm/system/management/save)$ | todo | |
| 主卡设置会员等级删除 | ^(/hsm-card-level/[_a-zA-Z0-9]+)$ | todo | |
| 主卡设置会员等级复制 | ^(/hsm-card-level/copy/[_a-zA-Z0-9]+)$ | todo | |
| 主卡设置会员等级保存 | ^(/hsm-card-level/save)$ | todo | |
| 体系成长值设置编辑 | ^(/hsm-system-management-growth-set/update)$ | todo | |
| 体系积分设置编辑 | ^(/hsm-system-management-integral-set/update)$ | todo | |
| 体系充值设置编辑 | ^(/hsm-system-management-recharge-set/update)$ | todo | |
| 权益列表 | ^(/hsm-card-level/right/[_a-zA-Z0-9]+)$ | todo | |
| 权益删除 | ^(/hsm/card/level/rights/[_a-zA-Z0-9]+/[_a-zA-Z0-9]+)$ | todo | |
| 生日权益 | ^(/hsm/card/level/rights/birth&#124;/hsm/card/level/rights/[_a-zA-Z0-9]+/[_a-zA-Z0-9]+)$ | todo | |
| 会员折扣 | ^(/hsm/card/level/rights/discount&#124;/hsm-system-management-recharge-set/querySelectStoreInfo&#124;/hsm/card/level/rights/[_a-zA-Z0-9]+/[_a-zA-Z0-9]+)$ | todo | |
| 升级奖励 | ^(/hsm/card/level/rights/upgrade&#124;/hsm/card/level/rights/[_a-zA-Z0-9]+/[_a-zA-Z0-9]+)$ | todo | |
| 会员列表 | ^(/hsm/member/enterprise/have/system)$ | todo | |
| 停止发放 | ^(/hsm/system/management/change/system/[_a-zA-Z0-9]+)$ | todo | 
### HsmVolumeInfoController
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 优惠券列表-dep| ^(/hsm-volume-info/queryList)$ | hsm_volume_info_list | |
| 添加优惠券-dep| ^(/hsm-volume-info/save)$ | hsm_volume_info_save | |
| 查看优惠券-dep| ^(/hsm-volume-info/[_a-zA-Z0-9]+)$ | hsm_volume_info_detail | |
| 编辑优惠券-dep| ^(/hsm-volume-info/update)$ | hsm_volume_info_update | |
| 删除优惠券-dep| ^(/hsm-volume-info/[_a-zA-Z0-9]+/[_a-zA-Z0-9]+)$ | hsm_volume_info_delete | |
| 停止发送优惠券-dep| ^(/hsm-volume-info/[_a-zA-Z0-9]+/[_a-zA-Z0-9]+)$ | hsm_volume_info_stop | |
| 作废优惠券-dep| ^(/hsm-volume-info/[_a-zA-Z0-9]+/[_a-zA-Z0-9]+)$ | hsm_volume_info_cancel | |
| 优惠券查询| ^(/hsm-volume-info/queryList)$ | todo | |
| 优惠券操作| ^(/hsm-volume-info)$ | todo | |
| 优惠券详情| ^(/hsm-volume-info/[_a-zA-Z0-9]+&#124;/hsm-volume-info/volume/detail)$ | todo | |
| 添加优惠券| ^(/hsm-volume-info/save&#124;/hsm-volume-info/[_a-zA-Z0-9]+)$ | todo | |
| 编辑优惠券| ^(/hsm-volume-info/update/hsm-volume-info/[_a-zA-Z0-9]+)$ | todo | |
| 删除优惠券| ^(/hsm-volume-info/[_a-zA-Z0-9]+/[_a-zA-Z0-9]+)$ | todo | |
| 发放明细| ^(/hsm-volume-info/volume/sendDetail)$ | send_detail | |
| 核销明细| ^(/hsm-volume-info/volume/useDetail)$ | use_detail | |
| 金额合计| ^(/hsm-volume-info/volume/useDetailAmount)$ | use_detail_amount | |
| 优惠券加券| ^(/hsm-volume-info/plus/[_a-zA-Z0-9]+)$ | hsm_volume_info_plus | |
| 查询商品券| ^(/hsm-volume-info/product/[_a-zA-Z0-9]+)$ | hsm_volume_product_query | |
| 商品券保存| ^(/hsm-volume-info/product/save)$ | hsm_volume_product_save | |
| 商品券更新| ^(/hsm-volume-info/product/update)$ | hsm_volume_product_update | |
| 添加商品券| ^(/marketing/createProductCoupon)$ | hsm_volume_product_update | |
### PosController
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 新会员注册 | /pos | pos_member_register | |
| 充值 | /pos | pos_member_charge | |
| 主页 | /pos | pos_member_index | |

### HsmMemberCardController
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
|品牌列表查询|^(/hsm/member/brand/enterprise)$|member_enterprise_brand | |
|品牌体系卡查询|^(/hsm-default-card/brand/query_by_condition)$|card_brand_query_by_condition | |
|门店体系卡查询|^(/hsm-default-card/store/query_by_condition)$|card_store_query_by_condition | |
|品牌默认卡修改|^(/hsm-default-card/brand/saveOrUpdateBrandDefaultCard)$|save_update_brand_default_card | |
|门店默认卡修改|^(/hsm-default-card/store/saveOrUpdateStoreDefaultCard)$|save_update_store_default_card | |
|门店列表查询|^(/hsm/member/store/findListByBrandGuid)$|find_list_by_brand | |
|重置密码|^(/hsm_member/resetPassword)$|member_reset_pwd | |
|等级明细列表|^(/hsm_member_level_detail/listByCondition)$|level_detail_list | |
|会员模板下载|^(/file/member/downloadExcelUrl)$|member_download_excel | |
|会员导入|^(/file/member/memberUploadExcelUrl)$|member_upload_execel | |
|会员上传|^(/file/member/memberUploadExcel)$|member_upload | |

### HsmMemberLableController
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 新增标签分组 | ^(/hsm-label-group/insert_group_label)$ | hsm_insert_group_lable||
| 查询所有标签分组 | ^(/hsm-label-group/query_group_label)$ | hsm_query_group_label | |
| 编辑标签分组 | ^(/hsm-label-group/update_group_label)$ | hsm_update_group_label | |
| 删除标签分组 | ^(/hsm-label-group/delete_group_label)$ | hsm_delete_group_label | |
| 保存标签 | ^(/hsm-label-setting/save)$ | hsm_label_setting_save | |
| 更新标签 | ^(/hsm-label-setting/update)$ | hsm_label_setting_update | |
| 标签列表 | ^(/hsm-label-setting/listByCondition)$ | hsm_label_setting_list | |
| 体系下所有卡的列表 | ^(/hsm/system/management/card/queryCardAll)$ | hsm_label_setting_query | |
| 标签详情 | ^(/hsm-label-setting/getOne)$ | hsm_label_setting_get_one | |
| 标签删除 | ^(/hsm-label-setting/delete)$ | hsm_label_setting_delete | |
| 精准营销标签列表查询 | ^(/hsm-member-data-statistics/query_by_label)$ | hsm_query_by_label | |
| 会员详情标签查询 | ^(/hsm-member-data-statistics/get_member_label)$ | hsm_get_member_label | |
| 标签列表点击刷新 | ^(/hsm-label-setting/refresh)$ | hsm_label_setting_refresh | |

## 厨显服务 holder_saas_store_kds
### WelcomeController
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 引导配置 | /1 | kds_user_guide |  | 
### PrdOrderController
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 查看 | /1 | kds_prd_order_query |  |
| 操作 | /1 | kds_prd_order_operate |  |
### PrdHistoryController
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 查看 | /1 | kds_prd_history_query |  |
| 打印 | /1 | kds_prd_history_print |  |
### PrdBindingController
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 查看 | /1 | kds_prd_query_point |  |
| 添加堂口 | /1 | kds_prd_create_point |  |
| 编辑堂口 | /1 | kds_prd_update_point |  |
| 删除堂口 | /1 | kds_prd_delete_point |  |
| 编辑绑定菜品 | /1 | kds_prd_bind_item |  |
| 单菜品/整组解绑 | /1 | kds_prd_unbind_item |  |
### PrdDisplayController
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 查看 | /1 | kds_prd_query_display |  |
| 编辑 | /1 | kds_prd_update_display |  |
### PrdAdvancedController
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 查看 | /1 | kds_prd_query_advanced_config |  | 
| 编辑 | /1 | kds_prd_update_advanced_config |  | 
### PrdPrinterController
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 查看 | /1 | kds_prd_query_printer |  |
| 添加打印机 | /1 | kds_prd_create_printer |  |
| 编辑打印机 | /1 | kds_prd_update_printer |  |
| 删除打印机 | /1 | kds_prd_delete_printer |  |
| 启用打印机 | /1 | kds_prd_bind_printer |  |
### PrdAboutController
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 查看 | /1 | kds_prd_query_about |  |
| 编辑设备名称 | /1 | kds_prd_update_device |  |
| 检查更新 | /1 | kds_prd_check_update |  |
| 初始化配置 | /1 | kds_prd_initial_again |  |
### DstOrderController
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 查看 | /1 | kds_dst_order_query |  |
| 操作 | /1 | kds_dst_order_operate |  |
### DstHistoryController
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 查看 | /1 | kds_dst_history_query |  |
| 打印 | /1 | kds_dst_history_print |  |
| 撤回出堂单 | /1 | kds_dst_cancel_dstribution |  |
### DstBindingController
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 查看 | /1 | kds_dst_query_binding |  |
| 类型/区域修改 | /1 | kds_dst_bind_area |  |
| 编辑绑定菜品 | /1 | kds_dst_bind_item |  |
| 单菜品/整组解绑 | /1 | kds_dst_unbind_item |  |
### DstDisplayController
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 查看 | /1 | kds_dst_query_display |  |  
| 编辑 | /1 | kds_dst_update_display |  |  
### DstAdvancedController
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 查看 | /1 | kds_dst_query_advanced_config |  |
| 编辑 | /1 | kds_dst_update_advanced_config |  |
### DstPrinterController
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 查看 | /1 | kds_dst_query_printer |  |
| 添加打印机 | /1 | kds_dst_create_printer |  |
| 编辑打印机 | /1 | kds_dst_update_printer |  |
| 删除打印机 | /1 | kds_dst_delete_printer |  |
| 启用打印机 | /1 | kds_dst_bind_printer |  |
### DstAboutController
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 查看 | /1 | kds_dst_query_about |  |
| 编辑设备名称 | /1 | kds_dst_update_device |  |
| 检查更新 | /1 | kds_dst_check_update |  |
| 初始化配置 | /1 | kds_dst_initial_again |  |

## 寄存服务 holder_saas_store_deposit
### MchController
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 新建寄存记录 | ^(/deposit/create_deposit_item)$ | create_deposit_item |  | 
| 查询寄存记录 | ^(/deposit/query_deposit_item)$ | query_deposit_item |  | 
| 查询寄存记录详情 | ^(/deposit/query_deposit_detail)$ | query_deposit_detail |  | 
| 取出寄存商品 | ^(/deposit/get_deposit_goods)$ | get_deposit_goods |  | 
| 商品寄存汇总 | ^(/deposit/query_goods_summary)$ | query_goods_summary |  | 
| 查询操作历史记录 | ^(/deposit/query_operation_history)$ | query_operation_history |  | 
| 短信提示设置 | ^(/deposit/remind_set)$ | remind_set |  | 
| 获取短信提示设置 | ^(/deposit/query_remind)$ | query_remind |  | 
### AIODepositController
| 资源名  | 资源URL | 资源CODE | 白名单URL |
| :------: | :------: | :------: | :------: |
| 主页 | /1 | aio_deposit_index |  | 
| 新增寄存 | /1 | aio_deposit_add |  | 
| 取出寄存 | /1 | aio_deposite_take_out |  | 