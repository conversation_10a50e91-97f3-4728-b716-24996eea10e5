# 鉴权逻辑

## 表说明

hss_user
- guid

hss_r_user_role
- user_guid
- role_guid

hss_role_source
- role_guid
- terminal_code
- menu_guid
- source_guid
- source_code
- source_url

hss_store_source
- module_guid
- module_type
- source_guid
- source_code
- source_url

hss_menu
- menu_guid
- module_guid

## 具体逻辑

1. WhitelistValidator命中，返回true

2. 基础类权限页面，返回true

    select
      s.module_type
    from hss_menu m
    inner join hss_store_source s on s.module_guid = m.module_guid
    where m.menu_guid = #{menuGuid}
    limit 1
        
    如果0==moduleType，返回true

3. SourceCode命中，返回true

    select
      count(*)
    from hss_user u
    inner join hss_r_user_role r on r.user_guid = u.guid
    inner join hss_role_source rs on (
      rs.role_guid = r.role_guid
      and rs.terminal_code = #{terminalCode}
      and rs.menu_guid = #{menuGuid}
      and #{sourceUrl} like concat('', rs.source_url,'%')
    )
    where u.guid = #{userGuid} and u.is_deleted = 0
        
    如果count>0，返回true，否则，返回false