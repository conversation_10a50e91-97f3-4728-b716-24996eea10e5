export default [{
  menuGuid: 'home',
  menuName: '首页',
  pageUrl: '/home',
  parentId: '终端ID',
  menus: []
}, {
  menuGuid: 'storesAndEmployees',
  menuName: '门店及员工',
  pageUrl: '/storesAndEmployees',
  parentId: '终端ID',
  isEnabled: true,
  menus: [{
    menuGuid: '门店管理',
    menuName: '门店管理',
    pageUrl: null,
    isEnabled: true,
    menus: [
      // 2.1 新建
      {
        menuGuid: 'storeList',
        menuName: '队列设置',
        pageUrl: '/storesAndEmployees/queueManage',
        isEnabled: true,
      }, {
        menuGuid: 'storeList',
        menuName: '排号设置',
        pageUrl: '/storesAndEmployees/surchargeManage',
        isEnabled: true,
      }, {
        menuGuid: 'bookSet',
        menuName: '预定设置',
        pageUrl: '/storesAndEmployees/bookSet',
        isEnabled: true,
      }, {
        menuGuid: 'groupBuy',
        menuName: '团购管理',
        pageUrl: '/storesAndEmployees/groupBuy',
        isEnabled: true,
      }, {
        menuGuid: 'takeawayManagement',
        menuName: '外卖管理列表',
        pageUrl: '/storesAndEmployees/takeawayManagement',
        isEnabled: true,
      }, {
        menuGuid: 'americanRegiment',
        menuName: '关联商品（美团）',
        pageUrl: '/storesAndEmployees/takeawayManagement/americanRegiment',
        isEnabled: false,
      }, {
        menuGuid: 'hungry',
        menuName: '关联商品（饿了么）',
        pageUrl: '/storesAndEmployees/takeawayManagement/hungry',
        isEnabled: false,
      }

      // 2.1 新建 结束
    ]
  }]
  },{
    menuGuid: 'commodityListAnd',
    menuName: '商品销售',
    pageUrl: '/merchandiseSales',
    parentId: '终端ID',
    menus: [
      {
        menuGuid: '门店商品',
        menuName: '门店商品',
        pageUrl: null,
        isEnabled: true,
        menus: [
          
          // 2.1 新建
          {
            menuGuid: 'estimateClearly',
            menuName: '估清设置',
            parentId: '终端ID,storeGoods',
            menuUrl: '/storeGoods/estimateClearly',
            isEnabled: true,
          }
          // 2.1 结束
        ]
      }
    ]
  },
  // 2.1的库存管理--start
  {
    menuGuid: '库存报表',
    menuName: '库存报表',
    pageUrl: null,
    isEnabled: true,
    menus: [{
      menuGuid: 'warehouseDetails',
      menuName: '出入库流水明细',
      parentId: '终端ID,inventoryManagement',
      pageUrl: '/inventoryManagement/warehouseDetails',
      isEnabled: true,
    },
    {
      menuGuid: 'supplierReconciliation',
      menuName: '供应商对账表',
      parentId: '终端ID,inventoryManagement',
      pageUrl: '/inventoryManagement/supplierReconciliation',
      isEnabled: true,
    }, {
      menuGuid: 'reportedLossManage',
      menuName: '库存查询',
      parentId: '终端ID,inventoryManagement',
      pageUrl: '/inventoryManagement/inventoryQuery',
      isEnabled: true,
    }]
  }, {
    menuGuid: '建资料',
    menuName: '建资料',
    pageUrl: null,
    isEnabled: true,
    menus: [{
      menuGuid: 'rawMaterialList',
      menuName: '物料列表',
      parentId: '终端ID,inventoryManagement',
      pageUrl: '/inventoryManagement/rawMaterialList',
      isEnabled: true,
    },
    {
      menuGuid: 'rawMaterialClassy',
      menuName: '物料分类',
      parentId: '终端ID,inventoryManagement',
      pageUrl: '/inventoryManagement/rawMaterialClassy',
      isEnabled: true,
    }, {
      menuGuid: 'rawMaterialUnit',
      menuName: '物料单位',
      parentId: '终端ID,inventoryManagement',
      pageUrl: '/inventoryManagement/rawMaterialUnit',
      isEnabled: true,
    },
    {
      menuGuid: 'rawMaterialRatio',
      menuName: '商品物料配比',
      parentId: '终端ID,inventoryManagement',
      pageUrl: '/inventoryManagement/rawMaterialRatio',
      isEnabled: true,
    }, {
      menuGuid: 'supplierList',
      menuName: '供应商列表',
      parentId: '终端ID,inventoryManagement',
      pageUrl: '/inventoryManagement/supplierList',
      isEnabled: true,
      }, {
        menuGuid: '',
        menuName: '新建物料',
        parentId: '终端ID,inventoryManagement',
        pageUrl: '/inventoryManagement/newRawMaterialList',
        isEnabled: false,
      }, {
        menuGuid: '',
        menuName: '编辑物料',
        parentId: '终端ID,inventoryManagement',
        pageUrl: '/inventoryManagement/editRawMaterialList',
        isEnabled: false,
      }, {
        menuGuid: '',
        menuName: '新建供应商列表',
        parentId: '终端ID,inventoryManagement',
        pageUrl: '/inventoryManagement/newSupplier',
        isEnabled: false,
      }, {
        menuGuid: '',
        menuName: '编辑供应商列表',
        parentId: '终端ID,inventoryManagement',
        pageUrl: '/inventoryManagement/editSupplier',
        isEnabled: false,
      }, {
        menuGuid: '',
        menuName: '配置商品',
        parentId: '终端ID,inventoryManagement',
        pageUrl: '/inventoryManagement/configuration',
        isEnabled: false,
      }, {
        menuGuid: '',
        menuName: '修改供应商报价方案',
        parentId: '终端ID,inventoryManagement',
        pageUrl: '/inventoryManagement/updateSupplier',
        isEnabled: false,
      }
    ]
  }, {
    menuGuid: '入库管理',
    menuName: '入库管理',
    pageUrl: null,
    isEnabled: true,
    menus: [
      {
      menuGuid: 'invoicingList',
      menuName: '入库列表',
      parentId: '终端ID,inventoryManagement',
      pageUrl: '/inventoryManagement/invoicingList',
      isEnabled: true,
    },
    {
      menuGuid: 'createEntrybound',
      menuName: '新建入库单',
      parentId: '终端ID,inventoryManagement',
      pageUrl: '/inventoryManagement/createEntrybound',
      isEnabled: false,
    },
    {
      menuGuid: 'editEntrybound',
      menuName: '编辑入库单',
      parentId: '终端ID,inventoryManagement',
      pageUrl: '/inventoryManagement/editEntrybound',
      isEnabled: false,
    },
    {
      menuGuid: 'viewDetailsEntrybound',
      menuName: '查看入库单',
      parentId: '终端ID,inventoryManagement',
      pageUrl: '/inventoryManagement/viewDetailsEntrybound',
      isEnabled: false,
    }
  ]
  }, {
    menuGuid: '出库管理',
    menuName: '出库管理',
    pageUrl: null,
    isEnabled: true,
    menus: [
      {
      menuGuid: 'outboundList',
      menuName: '入库列表',
      parentId: '终端ID,inventoryManagement',
      pageUrl: '/inventoryManagement/outboundList',
      isEnabled: true,
    },
    {
      menuGuid: 'createOutbound',
      menuName: '新建出库单',
      parentId: '终端ID,inventoryManagement',
      pageUrl: '/inventoryManagement/createOutbound',
      isEnabled: false,
    },
    {
      menuGuid: 'editOutbound',
      menuName: '编辑出库单',
      parentId: '终端ID,inventoryManagement',
      pageUrl: '/inventoryManagement/editOutbound',
      isEnabled: false,
    },
    {
      menuGuid: 'viewDetailsOutbound',
      menuName: '查看出库单',
      parentId: '终端ID,inventoryManagement',
      pageUrl: '/inventoryManagement/viewDetailsOutbound',
      isEnabled: false,
    }
  ]
  }, {
    menuGuid: '盘点管理',
    menuName: '盘点管理',
    pageUrl: null,
    isEnabled: true,
    menus: [{
      menuGuid: 'inventorySheet',
      menuName: '盘点单列表',
      parentId: '终端ID,inventoryManagement',
      pageUrl: '/inventoryManagement/inventorySheet',
      isEnabled: true,
    }, {
      menuGuid: 'inventory',
      menuName: '新建盘点单',
      parentId: '终端ID,inventoryManagement',
      pageUrl: '/inventoryManagement/inventory',
      isEnabled: false,
    }, {
      menuGuid: 'inventoryDetail',
      menuName: '查看盘点单',
      parentId: '终端ID,inventoryManagement',
      pageUrl: '/inventoryManagement/inventoryDetail',
      isEnabled: false,
    }]
  },
  {
    menuGuid: '仓库列表',
    menuName: '仓库列表',
    pageUrl: null,
    isEnabled: true,
    menus: [{
      menuGuid: 'warehouseList',
      menuName: '仓库列表',
      parentId: '终端ID,inventoryManagement',
      pageUrl: '/inventoryManagement/warehouseList',
      isEnabled: true,
    }]
  }
  // 2.1的库存管理--end
]
