
// 获取排队列表
export async function getLineUp(data: any) {
  return service({
    url: `/wx_queue/page_config`,
    method: 'post',
    data
  })
}
// 修改排队配置
export async function updateQueueConfig(data: any) {
  return service({
    url: `/wx_queue/update_by_guid`,
    method: 'post',
    data
  })
}
// 获取排队配置详情
export async function getDetailQueueConfig(data: any) {
  return service({
    url: `/wx_queue/get_by_guid`,
    method: 'post',
    data
  })
}
// 批量保存排队配置
export async function batchQueueConfig(data: any) {
  return service({
    url: `/wx_queue/batch_update`,
    method: 'post',
    data
  })
}
