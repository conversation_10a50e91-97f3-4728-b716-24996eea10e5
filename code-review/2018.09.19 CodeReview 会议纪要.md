# 会议纪要

1. （必须）Swagger的@ApiOperation里不要显示添加response，于此，Swagger就能展示出返回值的详细定义（包含泛型类型）

2. （必须）聚合层返回值使用Result<SomeGeneric>，不要使用Result<?>或未经包装的SomeClass

3. （必须）使用@FeignClient实现服务间调用时需要配置FallbackFactory，使用静态内部类形式来定义即可

4. （推荐）请求DTO命名：XxxReqDTO extends BaseDTO，响应DTO命名：XxxRespDTO

5. （必须）Redis缓存Key命名规则：模块名:业务名:具体业务唯一值（原则是前缀必须使用模块名做区分，后面的可根据业务定义）

6. （必须）分页使用统一地Page(SDK中，内部采用Mybatis拦截器拼接Sql)，详询冯超

7. （必须）暂时不用、或不推荐使用、或废弃的类使用@Deprecated注解标注，并在类注释上注明原因

8. （必须）DateTimeUtils的使用（此处引申含义是：尽量了解sdk中的一些常用功能）

9. （必须）批量插入、批量更新不得使用 for each 的Java代码，请使用batchInsertOrUpdate，基于 On Duplicated Key 原理，详询李杰

10. （必须）所有Guid统一定义为长度50

11. （必须）所有金额统一定义为Decimal(10, 2)

12. （待定）web前端操作日志记录

13. （待定）收银系统操作日志记录

14. （必须）RokectMQ 及 RokectMQ Stater 的正确使用，详询李杰

15. （必须）RokectMQ中命名规则：

    ProduceGroup: 模块

    ConsumeGroup: 模块-业务-consume-group

    Topic: 模块-业务-topic

    Tag: 模块-业务-tag

16. （必须）RokectMQ回调中若需处理本地数据(db, cache)，一定得手动切换数据源，详询李杰（将来会做一次封装）

17. （必须）数据库库名，表名规则，详见原文档

18. （推荐）枚举定义规则，一般包含一个id和value

    id: 字符串(或数值)唯一标识，代码中使用该id判断枚举相等性

    value: 字符串，表示该枚举值地详细含义，前端显示可用

    序列化时使用id

19. （必须）枚举一定以Enum结尾

20. （实验）列表页查询时，使用 BaseQueryDTO extends BaseDTO，或 PageDTO extends BaseDTO，均添加字段queryKey(aka: 查询关键字)

21. （必须）Mapper文件命名：一定以Mapper.xml结尾，如：resources/mapper/XxxMapper.xml

22. （推荐）Mapper文件方法命名：find表示“找一个”，query表示“找多个”

23. （必须）定时任务要在容易失败的地方tryCatch，避免一次失败导致该次任务整体失败

24. （推荐）定时任务需记录详细执行日志

25. （必须）接口测试可采用：Postman，需保存接口文件到项目中resources/postman；MockMVC，需在Test中保存单元测试方法，注意单元测试不通过对打包的影响（可使用-Dmaven.test.skip=true解决）

26.  (必须)各项目中的pom.xml文件必须引入135私服仓库，节点添加参考《Saas平台微服务工程结构规范说明.pdf》