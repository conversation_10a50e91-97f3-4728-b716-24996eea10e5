# Kotlin官方代码规范文档

代码风格请遵守官方[Coding Conventions](https://kotlinlang.org/docs/reference/coding-conventions.html)

Applying the style guide
To configure the IntelliJ formatter according to this style guide, please install Kotlin plugin version 1.2.20 or newer, go to Settings | Editor | Code Style | Kotlin, click on "Set from…" link in the upper right corner, and select "Predefined style / Kotlin style guide" from the menu.

To verify that your code is formatted according to the style guide, go to the inspection settings and enable the "Kotlin | Style issues | File is not formatted according to project settings" inspection. Additional inspections that verify other issues described in the style guide (such as naming conventions) are enabled by default.