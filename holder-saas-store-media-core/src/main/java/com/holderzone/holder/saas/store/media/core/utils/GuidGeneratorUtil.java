package com.holderzone.holder.saas.store.media.core.utils;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.sdk.util.BatchIdGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@Component
public class GuidGeneratorUtil {

    @Autowired
    private RedisTemplate redisTemplate;

    public String getGuid(String tab) {
        try {
            return String.valueOf(BatchIdGenerator.getGuid(redisTemplate, tab));
        } catch (IOException e) {
            throw new BusinessException("BatchIdGenerator生成【" + tab + "】对应guid失败");
        }
    }

    public List<String> getGuids(String tab, int count) {
        try {
            List<String> guids = new ArrayList<>();
            for (int i = 0; i < count; i++) {
                String guid = String.valueOf(BatchIdGenerator.getGuid(redisTemplate, tab));
                guids.add(guid);
            }
            return guids;
        } catch (IOException e) {
            throw new BusinessException("BatchIdGenerator生成【" + tab + "】对应guid失败");
        }
    }
}
