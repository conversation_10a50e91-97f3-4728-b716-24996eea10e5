package com.holderzone.holder.saas.store.media.core.service.impl;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.store.media.core.entity.GuidKeyConstant;
import com.holderzone.holder.saas.store.media.core.entity.HsmMedia;
import com.holderzone.holder.saas.store.media.core.mapper.HsmMediaMapper;
import com.holderzone.holder.saas.store.media.core.mapstruct.MediaDTOMap;
import com.holderzone.holder.saas.store.media.core.service.HsmMediaService;
import com.holderzone.holder.saas.store.media.core.utils.GuidGeneratorUtil;
import com.holderzone.holder.saas.store.media.dto.entity.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;


@Service
@Transactional
@Slf4j
public class HsmMediaServiceImpl implements HsmMediaService {

    private final HsmMediaMapper hsmMediaMapper;

    private final GuidGeneratorUtil guidGeneratorUtil;

    private final MediaDTOMap mediaDTOMap;


    public HsmMediaServiceImpl(HsmMediaMapper hsmMediaMapper, GuidGeneratorUtil guidGeneratorUtil,
                               MediaDTOMap mediaDTOMap) {
        this.hsmMediaMapper = hsmMediaMapper;
        this.guidGeneratorUtil = guidGeneratorUtil;
        this.mediaDTOMap = mediaDTOMap;
    }

    @Override
    public Page<MediaDTO> queryMediaByName(PageMedia pageMedia) {
        try {
            Integer count = hsmMediaMapper.queryCountMediaByName(pageMedia);
            if (count == 0) {
                return new Page<>();
            }
            List<HsmMedia> data = hsmMediaMapper.queryMediaByName(pageMedia);
            List<MediaDTO> mediaDTOList = mediaDTOMap.media2dtoList(data);
            Page<MediaDTO> pageInfo = new Page<>(pageMedia.getCurrentPage(), pageMedia.getPageSize(), count);
            pageInfo.setData(mediaDTOList);
            return pageInfo;
        } catch (Exception e) {
            log.info("根据文件名查询异常:{}", e.getMessage());
            e.printStackTrace();
            return new Page<>();
        }
    }

    @Override
    public List<FileCreateRespDTO> insertHsmMedia(List<MediaDTO> mediaDTOList) {
        List<FileCreateRespDTO> data = new ArrayList<>();
        try {
            List<HsmMedia> hsmMediaList = mediaDTOMap.dto2MediaList(mediaDTOList);
            for (HsmMedia media : hsmMediaList) {
                String guid = guidGeneratorUtil.getGuid(GuidKeyConstant.HOLDER_STORE_MEDIA);
                media.setCreateUserGuid(UserContextUtils.getUserGuid());
                media.setCreateUserName(UserContextUtils.getUserName());
                media.setGuid(guid);
                if (StringUtils.isEmpty(media.getName())) {
                    media.setName(UUID.randomUUID().toString());
                }
                log.info("name={}", media.getName());
                //检查重名
                List<String> nameList = hsmMediaMapper.queryExistByName(media);
                if (nameList.size() == 0 || ObjectUtils.isEmpty(nameList)) {
                    hsmMediaMapper.insertHsmMedia(media);
                } else {
                    String newName = isSameName(nameList, media.getName());
                    log.info("递归结束，name={}", newName);
                    media.setName(newName);
                    hsmMediaMapper.insertHsmMedia(media);
                }
                FileCreateRespDTO respDTO = new FileCreateRespDTO();
                respDTO.setGuid(guid);
                respDTO.setName(media.getName());
                respDTO.setUrl(media.getFileUrl());
                data.add(respDTO);
            }
        } catch (Exception e) {
            log.info("批量插入文件或文件夹异常:{}", e.getMessage());
            e.printStackTrace();
            return Collections.emptyList();
        }
        return data;
    }

    @Override
    public FileCreateRespDTO insertHsmMedia(MediaDTO mediaDTO) {
        try {
            String guid = guidGeneratorUtil.getGuid(GuidKeyConstant.HOLDER_STORE_MEDIA);
            HsmMedia hsmMedia = mediaDTOMap.dto2Media(mediaDTO);
            hsmMedia.setCreateUserGuid(UserContextUtils.getUserGuid());
            hsmMedia.setCreateUserName(UserContextUtils.getUserName());
            hsmMedia.setGuid(guid);
            //检查重名
            List<String> nameList = hsmMediaMapper.queryExistByName(hsmMedia);
            //无重名，直接插入
            if (ObjectUtils.isEmpty(nameList)) {
                hsmMediaMapper.insertHsmMedia(hsmMedia);
            } else {
                String newName = isSameName(nameList, hsmMedia.getName());
                hsmMedia.setName(newName);
                hsmMediaMapper.insertHsmMedia(hsmMedia);
            }
            FileCreateRespDTO respDTO = new FileCreateRespDTO();
            respDTO.setGuid(guid);
            respDTO.setName(hsmMedia.getName());
            respDTO.setUrl(hsmMedia.getFileUrl());
            return respDTO;
        } catch (Exception e) {
            log.info("插入文件或文件夹异常:{}", e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    //递归判断是否重名
    private String isSameName(List<String> nameList, String mediaName) {
        for (String name : nameList) {
            if (name.equals(mediaName)) {
                mediaName = mediaName + "(1)";
                return isSameName(nameList, mediaName);
            }
        }
        return mediaName;
    }


    @Override
    public Page<MediaDTO> queryMediaList(PageMedia pageMedia) {
        try {
            Integer count = hsmMediaMapper.queryCountMediaList(pageMedia);
            if (count == 0) {
                return new Page<>();
            }
            List<HsmMedia> data = hsmMediaMapper.queryMediaList(pageMedia);
            List<MediaDTO> mediaDTOList = mediaDTOMap.media2dtoList(data);
            Page<MediaDTO> pageInfo = new Page<>(pageMedia.getCurrentPage(), pageMedia.getPageSize(), count);
            pageInfo.setData(mediaDTOList);
            return pageInfo;
        } catch (Exception e) {
            log.info("加载文件夹列表异常:{}", e.getMessage());
            e.printStackTrace();
            return new Page<>();
        }
    }

    @Override
    public Boolean moveFile(FileMoveDTO fileMoveDTO) {
        try {
            List<String> moveFileGuidList = fileMoveDTO.getMoveFileGuidList();
            List<HsmMedia> mediaList = hsmMediaMapper.selectHsmMediaInGuids(moveFileGuidList);
            for (HsmMedia media : mediaList) {
                //检查重名
                media.setParentGuid(fileMoveDTO.getTargetFolderGuid());
                List<String> nameList = hsmMediaMapper.queryExistByName(media);
                if (CollectionUtils.isNotEmpty(nameList)) {
                    String newName = isSameName(nameList, media.getName());
                    //有同名的改名
                    FileRenameDTO fileRenameDTO = new FileRenameDTO();
                    fileRenameDTO.setFileGuid(media.getGuid());
                    fileRenameDTO.setNewName(newName);
                    hsmMediaMapper.renameFile(fileRenameDTO);
                }
            }
            fileMoveDTO.setModifyUserGuid(UserContextUtils.getUserGuid());
            fileMoveDTO.setModifyUserName(UserContextUtils.getUserName());
            return hsmMediaMapper.moveFile(fileMoveDTO);
        } catch (Exception e) {
            log.info("移动文件异常:{}", e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    @Override
    public Boolean renameFile(FileRenameDTO fileRenameDTO) {
        try {
            //查询要重命名文件的同级目录下的所有文件名
            List<String> nameList = hsmMediaMapper.checkRepeatFile(fileRenameDTO);
            if (CollectionUtils.isNotEmpty(nameList)) {
                throw new BusinessException("已存在同名文件或文件夹");
            } else {
                HsmMedia hsmMedia = hsmMediaMapper.selectHsmMediaByGuid(fileRenameDTO.getFileGuid());
                hsmMedia.setName(fileRenameDTO.getNewName())
                        .setModifyUserGuid(UserContextUtils.getUserGuid())
                        .setModifyUserName(UserContextUtils.getUserName());
                return hsmMediaMapper.updateHsmMedia(hsmMedia);
            }
        } catch (Exception e) {
            log.info("文件重命名异常:{}", e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
}
