package com.holderzone.holder.saas.store.media.core.utils;

import lombok.extern.slf4j.Slf4j;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className UploadValidateUtil
 * @date 2019/07/30 14:07
 * @description 商户后台图片上传验证工具类
 * @program holder-saas-store
 */
@Slf4j
public class UploadValidateUtil {

    private static final List<String> correctTypeList = Arrays.asList("jpg", "jpeg", "bmp", "png", "gif", "zip", "rar");

    /**
     * 验证图片类型是否不合法
     *
     * @param type
     * @return true 不合法 false 合法
     */
    public static Boolean typeNotValidate(String type) {
        return correctTypeList.stream().noneMatch(s -> s.equalsIgnoreCase(type));
    }
}
