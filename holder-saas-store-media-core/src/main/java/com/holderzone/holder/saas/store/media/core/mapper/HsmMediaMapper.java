package com.holderzone.holder.saas.store.media.core.mapper;

import com.holderzone.holder.saas.store.media.core.entity.HsmMedia;
import com.holderzone.holder.saas.store.media.dto.entity.FileMoveDTO;
import com.holderzone.holder.saas.store.media.dto.entity.FileRenameDTO;
import com.holderzone.holder.saas.store.media.dto.entity.PageMedia;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public interface HsmMediaMapper {

    void insertHsmMedia(HsmMedia hsmMedia);

    void deleteHsmMediaByGuid(String guid);

    List<String> queryExistByName(HsmMedia hsmMedia);

    List<HsmMedia> queryMediaByName(PageMedia hsmMedia);

    Boolean moveFile(@Param("dto") FileMoveDTO fileMoveDTO);

    Boolean renameFile(@Param("dto") FileRenameDTO fileRenameDTO);

    List<String> queryFileName(@Param("dto") FileRenameDTO fileRenameDTO);

    void deleteHsmMediaByGuids(List<String> guid);

    HsmMedia selectHsmMediaByGuid(String guid);

    List<HsmMedia> selectHsmMediaByParentGuids(List<String> guid);

    List<HsmMedia> selectHsmMediaInGuids(@Param("guids") List<String> guids);

    Integer queryCountMediaByName(PageMedia hsmMedia);

    List<HsmMedia> queryMediaList(PageMedia pageMedia);

    Integer queryCountMediaList(PageMedia pageMedia);

    List<String> checkRepeatFile(@Param("dto") FileRenameDTO fileRenameDTO);

    Boolean updateHsmMedia(HsmMedia hsmMedia);
}
