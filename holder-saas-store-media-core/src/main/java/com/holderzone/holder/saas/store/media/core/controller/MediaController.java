package com.holderzone.holder.saas.store.media.core.controller;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.store.media.core.service.FileUploadService;
import com.holderzone.holder.saas.store.media.core.service.HsmMediaService;
import com.holderzone.holder.saas.store.media.dto.entity.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/file")
@Api("文件接口")
@Slf4j
public class MediaController {

    private final FileUploadService fileUploadService;

    private final HsmMediaService hsmMediaService;


    public MediaController(FileUploadService fileUploadService, HsmMediaService hsmMediaService) {
        this.fileUploadService = fileUploadService;
        this.hsmMediaService = hsmMediaService;
    }

    @ApiOperation(value = "批量新建接口")
    @PostMapping("batch/create_file")
    public List<FileCreateRespDTO> batchCreateFile(@RequestBody List<MediaDTO> hsmMediaList) {
        log.info("批量新建接口入参:{}", JacksonUtils.writeValueAsString(hsmMediaList));
        return hsmMediaService.insertHsmMedia(hsmMediaList);
    }

    @ApiOperation(value = "新建接口")
    @PostMapping("/create_file")
    public FileCreateRespDTO createFile(@RequestBody MediaDTO hsmMedia) {
        log.info("新建接口入参:{}", JacksonUtils.writeValueAsString(hsmMedia));
        return hsmMediaService.insertHsmMedia(hsmMedia);
    }

    @ApiOperation(value = "删除文件")
    @PostMapping("/delete")
    public void delete(@RequestBody List<FileDeleteDTO> fileDeleteDTOList) {
        log.info("删除文件入参:{}", JacksonUtils.writeValueAsString(fileDeleteDTOList));
        for (FileDeleteDTO fileDeleteDTO : fileDeleteDTOList) {
            fileUploadService.deleteFile(fileDeleteDTO);
        }
    }

    @ApiOperation(value = "根据名称查询文件或文件夹")
    @PostMapping("/queryMediaByName")
    public Page<MediaDTO> queryMediaByName(@RequestBody PageMedia pageMedia) {
        log.info("根据名称查询文件或文件夹入参:{}", JacksonUtils.writeValueAsString(pageMedia));
        return hsmMediaService.queryMediaByName(pageMedia);
    }

    @ApiOperation(value = "查询根目录文件和文件夹")
    @PostMapping("/queryMediaList")
    public Page<MediaDTO> queryMediaList(@RequestBody PageMedia pageMedia) {
        log.info("查询根目录文件和文件夹入参:{}", JacksonUtils.writeValueAsString(pageMedia));
        return hsmMediaService.queryMediaList(pageMedia);
    }


    /**
     * 移动文件（可批量）
     *
     * @param fileMoveDTO fileMoveDTO
     * @return sof
     */
    @ApiOperation(value = "移动文件")
    @PostMapping("/move")
    public Boolean moveFile(@RequestBody FileMoveDTO fileMoveDTO) {
        log.info("移动文件入参:{}", JacksonUtils.writeValueAsString(fileMoveDTO));
        return hsmMediaService.moveFile(fileMoveDTO);
    }

    /**
     * 文件或目录重命名
     *
     * @param fileRenameDTO fileRenameDTO
     * @return sof
     */
    @ApiOperation(value = "文件重命名")
    @PostMapping("/rename")
    public Boolean renameFile(@RequestBody FileRenameDTO fileRenameDTO) {
        log.info("文件重命名入参:{}", JacksonUtils.writeValueAsString(fileRenameDTO));
        return hsmMediaService.renameFile(fileRenameDTO);
    }
}
