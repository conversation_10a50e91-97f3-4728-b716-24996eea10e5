package com.holderzone.holder.saas.store.media.core.service.impl;

import com.holderzone.framework.base.dto.file.FileDto;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.security.SecurityManager;
import com.holderzone.holder.saas.store.media.core.entity.HsmMedia;
import com.holderzone.holder.saas.store.media.core.mapper.HsmMediaMapper;
import com.holderzone.holder.saas.store.media.core.service.BaseService;
import com.holderzone.holder.saas.store.media.core.service.FileUploadService;
import com.holderzone.holder.saas.store.media.core.utils.GuidGeneratorUtil;
import com.holderzone.holder.saas.store.media.core.utils.UploadValidateUtil;
import com.holderzone.holder.saas.store.media.dto.entity.FileDeleteDTO;
import com.holderzone.holder.saas.store.media.dto.entity.FileUploadDTO;
import com.holderzone.holder.saas.store.media.dto.entity.MediaDTO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className FileUploadServiceImpl
 * @date 2018/09/13 11:12
 * @description
 * @program holder-saas-aggregation-merchant
 */
@Service
public class FileUploadServiceImpl implements FileUploadService {

    private static final Logger logger = LoggerFactory.getLogger(FileUploadServiceImpl.class);

    private final BaseService baseService;

    private final GuidGeneratorUtil guidGeneratorUtil;

    private final HsmMediaMapper hsmMediaMapper;


    public FileUploadServiceImpl(BaseService baseService, GuidGeneratorUtil guidGeneratorUtil,
                                 HsmMediaMapper hsmMediaMapper) {
        this.baseService = baseService;
        this.guidGeneratorUtil = guidGeneratorUtil;
        this.hsmMediaMapper = hsmMediaMapper;
    }

    public static String getRandomFileName() {
        return UUID.randomUUID().toString().replace("-", "").substring(0, 5);
    }

    private static double getAccuracy(long size) {
        double accuracy;
        if (size < 900) {
            accuracy = 0.85;
        } else if (size < 2047) {
            accuracy = 0.6;
        } else if (size < 3275) {
            accuracy = 0.44;
        } else {
            accuracy = 0.4;
        }
        return accuracy;
    }

    @Override
    public void deleteFile(FileDeleteDTO fileDeleteDTO) {
        Map<String, List<String>> subData = new HashMap<>();
        List<String> guids = new ArrayList<>();
        List<String> urls = new ArrayList<>();
        List<String> param = new ArrayList<>();
        param.add(fileDeleteDTO.getGuid());
        //递归查询出所有子文件、文件夹
        Map<String, List<String>> data = this.selectAllSub(subData, param, guids, urls);
        List<String> deleteGuids;
        List<String> deleteUrls;
        if (!ObjectUtils.isEmpty(data)) {
            deleteGuids = data.get("guids");
            deleteUrls = data.get("urls");

            //删除子类文件或文件夹
            hsmMediaMapper.deleteHsmMediaByGuids(deleteGuids);

            //删除子类图片或文件
            for (String url : deleteUrls) {
                baseService.delete(url);
            }
        }
        //删除父类
        if (!StringUtils.isEmpty(fileDeleteDTO.getFileUrl())) {
            baseService.delete(fileDeleteDTO.getFileUrl());
        }
        //删除父类
        if (!StringUtils.isEmpty(fileDeleteDTO.getGuid())) {
            hsmMediaMapper.deleteHsmMediaByGuid(fileDeleteDTO.getGuid());
        }
    }

    private Map<String, List<String>> selectAllSub(Map<String, List<String>> subData, List<String> parentIds,
                                                   List<String> guids, List<String> urls) {
        List<HsmMedia> hsmMediaList = hsmMediaMapper.selectHsmMediaByParentGuids(parentIds);
        if (ObjectUtils.isEmpty(hsmMediaList) || ObjectUtils.isEmpty(parentIds)) {
            return subData;
        }
        guids.addAll(hsmMediaList.stream().map(HsmMedia::getGuid).collect(Collectors.toList()));
        List<String> currentGuids = hsmMediaList.stream().map(HsmMedia::getGuid).collect(Collectors.toList());
        urls.addAll(hsmMediaList.stream().filter((HsmMedia) -> !StringUtils.isEmpty(HsmMedia.getFileUrl()))
                .map(HsmMedia::getFileUrl).collect(Collectors.toList()));
        subData.put("guids", guids);
        subData.put("urls", urls);
        return this.selectAllSub(subData, currentGuids, guids, urls);
    }

}
