package com.holderzone.holder.saas.store.media.core.mapstruct;

import com.holderzone.holder.saas.store.media.core.entity.HsmMedia;
import com.holderzone.holder.saas.store.media.dto.entity.MediaDTO;
import org.mapstruct.Mapper;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * MediaDto与HsmMedia转换类
 */
@Component
@Mapper(componentModel = "spring")
public interface MediaDTOMap {

    HsmMedia dto2Media(MediaDTO mediaDTO);

    List<HsmMedia> dto2MediaList(List<MediaDTO> mediaDTOList);

    List<MediaDTO> media2dtoList(List<HsmMedia> hsmMediaList);
}
