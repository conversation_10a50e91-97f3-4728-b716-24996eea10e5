package com.holderzone.holder.saas.store.media.core.service;

import com.holderzone.framework.base.dto.file.FileDto;
import com.holderzone.framework.base.dto.region.RegionDTO;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.ThrowableUtils;
import com.netflix.hystrix.exception.HystrixBadRequestException;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className RegionService
 * @date 18-9-17 下午4:03
 * @description 服务间调用-base服务
 * @program holder-saas-aggregation-merchant
 */
@Component
@FeignClient(name = "base-service", fallbackFactory = BaseService.ServiceFallBack.class)
public interface BaseService {

    @GetMapping("/region/province")
    List<RegionDTO> listProvince();

    @GetMapping("/region/city")
    List<RegionDTO> listCity(@RequestParam(name = "pCode") String pCode);

    @GetMapping("/region/district")
    List<RegionDTO> listDistrict(@RequestParam(name = "pCode") String pCode);

    @GetMapping("/verifycode")
    byte[] getVerifyCodeImg(@RequestParam(name = "vc_id") String id);

    @PostMapping("/verifycode")
    Boolean validateVerifyCodeImg(@RequestParam("vc_id") String id, @RequestParam("vc_code") String vcCode);

    @PostMapping("/file")
    String upload(@RequestBody FileDto fileDto);

    @DeleteMapping("/file")
    void delete(@RequestParam("fileUrl") String fileUrl);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<BaseService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public BaseService create(Throwable cause) {
            return new BaseService() {
                @Override
                public List<RegionDTO> listProvince() {
                    log.error(HYSTRIX_PATTERN, "listProvince", null, ThrowableUtils.asString(cause));
                    return null;
                }

                @Override
                public List<RegionDTO> listCity(String pCode) {
                    log.error(HYSTRIX_PATTERN, "listCity", null, ThrowableUtils.asString(cause));
                    return null;
                }

                @Override
                public List<RegionDTO> listDistrict(String pCode) {
                    log.error(HYSTRIX_PATTERN, "listDistrict", null, ThrowableUtils.asString(cause));
                    return null;
                }

                @Override
                public byte[] getVerifyCodeImg(String id) {
                    throw new BusinessException("获取验证码失败");
                }

                @Override
                public Boolean validateVerifyCodeImg(String id, String vcCode) {
                    log.error("调用base-service服务验证验证码失败");
                    return false;
                }

                @Override
                public String upload(FileDto fileDto) {
                    log.error("e={}", cause.getMessage());
                    throw new HystrixBadRequestException("调用oss文件上传异常! " + cause.getMessage());
                }

                @Override
                public void delete(String fileUrl) {
                    log.error("e={}", cause.getMessage());
                    throw new HystrixBadRequestException("调用oss文件删除异常! " + cause.getMessage());
                }
            };
        }
    }
}
