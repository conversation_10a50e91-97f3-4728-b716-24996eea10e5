package com.holderzone.holder.saas.store.media.core.utils;

import org.springframework.context.ConfigurableApplicationContext;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2018/10/25 14:55
 * @description
 */
public class SpringContextUtil {

    private ConfigurableApplicationContext cfgContext;

    /**
     * 实体对象
     */
    private static final SpringContextUtil INSTANCE = new SpringContextUtil();

    private SpringContextUtil() {
        if (INSTANCE != null) {
            throw new Error("error");
        }
    }

    public static SpringContextUtil getInstance() {
        return INSTANCE;
    }


    /**
     * 根据名称获取Bean
     */
    @SuppressWarnings("unchecked")
    public <T> T getBean(String name) {
        return (T) cfgContext.getBean(name);
    }


    /**
     * @date 2018-07-09 15:47:56
     */
    public void setCfgContext(ConfigurableApplicationContext cfgContext) {
        this.cfgContext = cfgContext;
    }

}
