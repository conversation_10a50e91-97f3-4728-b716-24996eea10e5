package com.holderzone.holder.saas.store.media.core;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

@SpringBootApplication(exclude = DruidDataSourceAutoConfigure.class)
@EnableFeignClients(basePackages = "com.holderzone")
@EnableSwagger2
@EnableEurekaClient
@MapperScan(basePackages = {"com.holderzone.holder.saas.store.media.core.mapper"})
public class HolderSaasStoreMediaCoreApplication {

    public static void main(String[] args) {
        SpringApplication.run(HolderSaasStoreMediaCoreApplication.class, args);
    }

}
