package com.holderzone.holder.saas.store.media.core.service;

import com.holderzone.holder.saas.store.media.dto.entity.FileDeleteDTO;
import com.holderzone.holder.saas.store.media.dto.entity.FileUploadDTO;
import com.holderzone.holder.saas.store.media.dto.entity.MediaDTO;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;

/**
 * <AUTHOR>
 * @version 1.0
 * @className FileUploadService
 * @date 2018/09/13 11:12
 * @description
 * @program holder-saas-aggregation-merchant
 */
public interface FileUploadService {

    void deleteFile(FileDeleteDTO fileDeleteDTO);
}
