package com.holderzone.holder.saas.store.media.core.service;

import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.store.media.dto.entity.*;

import java.util.List;

public interface HsmMediaService {

    List<FileCreateRespDTO> insertHsmMedia(List<MediaDTO> hsmMediaList);

    FileCreateRespDTO insertHsmMedia(MediaDTO hsmMedia);

    Page<MediaDTO> queryMediaByName(PageMedia pageMedia);

    Page<MediaDTO> queryMediaList(PageMedia pageMedia);

    Boolean moveFile(FileMoveDTO fileMoveDTO);

    Boolean renameFile(FileRenameDTO fileRenameDTO);
}
