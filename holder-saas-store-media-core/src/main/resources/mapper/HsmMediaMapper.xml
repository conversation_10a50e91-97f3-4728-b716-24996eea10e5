<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.holder.saas.store.media.core.mapper.HsmMediaMapper">

    <resultMap type="HsmMedia" id="HsmMediaResult">
        <result property="id" column="id"/>
        <result property="guid" column="guid"/>
        <result property="brandGuid" column="brand_guid"/>
        <result property="name" column="name"/>
        <result property="fileUrl" column="file_url"/>
        <result property="parentGuid" column="parent_guid"/>
        <result property="isFolder" column="is_folder"/>
        <result property="isDelete" column="is_delete"/>
        <result property="isBrand" column="is_brand"/>
        <result property="fileSize" column="file_size"/>
        <result property="fileType" column="file_type"/>
        <result property="remark" column="remark"/>
        <result property="createUserGuid" column="create_user_guid"/>
        <result property="createUserName" column="create_user_name"/>
        <result property="modifyUserGuid" column="modify_user_guid"/>
        <result property="modifyUserName" column="modify_user_name"/>
    </resultMap>

    <sql id="selectHsmMediaVo">
        select id, guid, brand_guid, name, file_url, parent_guid, is_folder, is_delete, is_brand, file_size, file_type, remark,create_user_guid,create_user_name,modify_user_guid,modify_user_name from hsm_media
    </sql>

    <select id="selectHsmMediaInGuids" parameterType="list" resultMap="HsmMediaResult">
        <include refid="selectHsmMediaVo"/>
        where is_delete= 0 and guid in
        <foreach collection="guids" item="guid" open="(" close=")" separator=",">
            #{guid}
        </foreach>
    </select>

    <select id="queryMediaByName" parameterType="PageMedia" resultMap="HsmMediaResult">
        <include refid="selectHsmMediaVo"/>
        where
        is_delete= 0
        <if test="name != null and name != ''">
            and name like concat('%',#{name},'%')
        </if>
        <if test="fileType != null">
            and file_type = #{fileType}
        </if>
        <if test="brandGuid != null and brandGuid != ''">
            and brand_guid = #{brandGuid}
        </if>
        <if test="storeGuid != null and storeGuid != ''">
            and store_guid = #{storeGuid}
        </if>
        limit #{index},#{pageSize}
    </select>

    <select id="queryCountMediaByName" parameterType="PageMedia" resultType="int">
        select count(*) from hsm_media
        where is_delete= 0
        <if test="name != null and name != ''">
            and name like concat('%',#{name},'%')
        </if>
        <if test="fileType != null">
            and file_type = #{fileType}
        </if>
        <if test="brandGuid != null and brandGuid != ''">
            and brand_guid = #{brandGuid}
        </if>
        <if test="storeGuid != null and storeGuid != ''">
            and store_guid = #{storeGuid}
        </if>
    </select>

    <select id="queryExistByName" parameterType="HsmMedia" resultType="string">
        select name from hsm_media
        where is_delete= 0
        and name like concat(#{name},'%')
        AND is_folder = #{isFolder}
        and
        <choose>
            <when test="parentGuid != null and parentGuid != ''">
                parent_guid = #{parentGuid}
            </when>
            <otherwise>
                parent_guid is null
            </otherwise>
        </choose>
        AND
        <choose>
            <when test="brandGuid != null and brandGuid != ''">
                brand_guid = #{brandGuid}
            </when>
            <otherwise>
                store_guid = #{storeGuid}
            </otherwise>
        </choose>
    </select>

    <select id="queryMediaList" parameterType="PageMedia" resultMap="HsmMediaResult">
        <include refid="selectHsmMediaVo"/>
        <where>
            is_delete = 0
            <if test="isFolder != null">and is_folder = #{isFolder}</if>
            <if test="parentGuid != null and parentGuid != ''">and parent_guid = #{parentGuid}</if>
            <if test="parentGuid == null or parentGuid == ''">and parent_guid is null</if>
            <if test="brandGuid != null and brandGuid != ''">and brand_guid = #{brandGuid}</if>
            <if test="storeGuid != null and storeGuid != ''">and store_guid = #{storeGuid}</if>
        </where>
        limit #{index},#{pageSize}
    </select>

    <select id="queryCountMediaList" parameterType="PageMedia" resultType="int">
        select count(*) from hsm_media

        <where>
            is_delete = 0
            <if test="isFolder != null">and is_folder = #{isFolder}</if>
            <if test="parentGuid != null and parentGuid != ''">and parent_guid = #{parentGuid}</if>
            <if test="parentGuid == null or parentGuid == ''">and parent_guid is null</if>
            <if test="brandGuid != null and brandGuid != ''">and brand_guid = #{brandGuid}</if>
            <if test="storeGuid != null and storeGuid != ''">and store_guid = #{storeGuid}</if>
        </where>
    </select>

    <insert id="insertHsmMedia" parameterType="HsmMedia" useGeneratedKeys="true" keyProperty="id">
        insert into hsm_media
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="guid != null  and guid != ''">guid,</if>
            <if test="brandGuid != null  and brandGuid != ''">brand_guid,</if>
            <if test="storeGuid != null  and storeGuid != ''">store_guid,</if>
            <if test="name != null  and name != ''">name,</if>
            <if test="fileUrl != null  and fileUrl != ''">file_url,</if>
            <if test="parentGuid != null  and parentGuid != ''">parent_guid,</if>
            <if test="isFolder != null ">is_folder,</if>
            <if test="isDelete != null ">is_delete,</if>
            <if test="isBrand != null">is_brand,</if>
            <if test="fileSize != null ">file_size,</if>
            <if test="fileType != null">file_type,</if>
            <if test="remark != null  and remark != ''">remark,</if>
            <if test="createUserGuid != null and createUserGuid != ''">create_user_guid,</if>
            <if test="createUserName != null and createUserName != ''">create_user_name,</if>
            <if test="modifyUserGuid != null and modifyUserGuid != ''">modify_user_guid,</if>
            <if test="modifyUserName != null and modifyUserName != ''">modify_user_name</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="guid != null  and guid != ''">#{guid},</if>
            <if test="brandGuid != null  and brandGuid != ''">#{brandGuid},</if>
            <if test="storeGuid != null  and storeGuid != ''">#{storeGuid},</if>
            <if test="name != null  and name != ''">#{name},</if>
            <if test="fileUrl != null  and fileUrl != ''">#{fileUrl},</if>
            <if test="parentGuid != null  and parentGuid != ''">#{parentGuid},</if>
            <if test="isFolder != null ">#{isFolder},</if>
            <if test="isDelete != null ">#{isDelete},</if>
            <if test="isBrand != null">#{isBrand},</if>
            <if test="fileSize != null ">#{fileSize},</if>
            <if test="fileType != null">#{fileType},</if>
            <if test="remark != null  and remark != ''">#{remark},</if>
            <if test="createUserGuid != null and createUserGuid != ''">#{createUserGuid},</if>
            <if test="createUserName != null and createUserName != ''">#{createUserName},</if>
            <if test="modifyUserGuid != null and modifyUserGuid != ''">#{modifyUserGuid},</if>
            <if test="modifyUserName != null and modifyUserName != ''">#{modifyUserName}</if>
        </trim>
    </insert>

    <update id="updateHsmMedia" parameterType="HsmMedia">
        update hsm_media
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="parentGuid != null and parentGuid != ''">parent_guid = #{parentGuid},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
            <if test="modifyUserGuid != null and modifyUserGuid != ''">modify_user_guid = #{modifyUserGuid},</if>
            <if test="modifyUserName != null and modifyUserName != ''">modify_user_name = #{modifyUserName},</if>
        </trim>
        where guid = #{guid}
    </update>

    <delete id="deleteHsmMediaById" parameterType="Long">
        delete from hsm_media where id = #{id}
    </delete>

    <update id="deleteHsmMediaByGuid" parameterType="String">
        UPDATE hsm_media m
         SET m.is_delete = 1
         WHERE m.guid = #{guid}
    </update>

    <delete id="deleteHsmMediaByIds" parameterType="String">
        delete from hsm_media where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="moveFile" parameterType="FileMoveDTO">
        UPDATE hsm_media m
        SET
            <choose>
                <when test="dto.targetFolderGuid != null and dto.targetFolderGuid != ''">
                    m.parent_guid = #{dto.targetFolderGuid},
                </when>
                <otherwise>
                    m.parent_guid = null,
                </otherwise>
            </choose>
            m.modify_user_guid = #{dto.modifyUserGuid}, m.modify_user_name = #{dto.modifyUserName}
        WHERE m.guid IN
        <foreach collection="dto.moveFileGuidList" item="guid" open="(" close=")" separator=",">
            #{guid}
        </foreach>
    </update>

    <update id="renameFile" parameterType="FileRenameDTO">
        UPDATE hsm_media m
        SET m.name = #{dto.newName}
        WHERE m.guid = #{dto.fileGuid}
    </update>

    <!-- 查询某个目录下的所有文件或文件名，用于判断是否有重名 -->
    <select id="queryFileName" resultType="string">
        SELECT m.NAME
        FROM hsm_media m
        WHERE m.is_folder = #{dto.isFolder}
        and is_delete= 0
        AND
            <choose>
                <when test="dto.parentGuid != null and dto.parentGuid != ''">
                    m.parent_guid = #{dto.parentGuid}
                </when>
                <otherwise>
                    m.parent_guid is null
                </otherwise>
            </choose>
        AND
            <choose>
                <when test="dto.brandGuid != null and dto.brandGuid != ''">
                    m.brand_guid = #{dto.brandGuid}
                </when>
                <otherwise>
                    m.store_guid = #{dto.storeGuid}
                </otherwise>
            </choose>
    </select>

    <select id="selectHsmMediaByGuid" parameterType="String" resultMap="HsmMediaResult">
        <include refid="selectHsmMediaVo"/>
        where is_delete=0 and guid = #{guid}
    </select>

    <select id="selectHsmMediaByParentGuids" parameterType="list" resultMap="HsmMediaResult">
        <include refid="selectHsmMediaVo"/>
        where is_delete=0 and parent_guid in
        <foreach collection="list" item="media" open="(" close=")" separator=",">
            (
            #{media}
            )
        </foreach>
    </select>

    <update id="deleteHsmMediaByGuids" parameterType="list">
        UPDATE hsm_media m
        SET m.is_delete = 1
        WHERE m.guid in
        <foreach collection="list" item="guid" open="(" close=")" separator=",">
            (
            #{guid}
            )
        </foreach>
    </update>

    <select id="checkRepeatFile" resultType="string">
        SELECT m.name
        FROM hsm_media m
        WHERE m.is_folder = #{dto.isFolder}
        and is_delete=0
        AND
        <choose>
            <when test="dto.parentGuid != null and dto.parentGuid != ''">
                m.parent_guid = #{dto.parentGuid}
            </when>
            <otherwise>
                m.parent_guid is null
            </otherwise>
        </choose>
        AND
        <choose>
            <when test="dto.brandGuid != null and dto.brandGuid != ''">
                m.brand_guid = #{dto.brandGuid}
            </when>
            <otherwise>
                m.store_guid = #{dto.storeGuid}
            </otherwise>
        </choose>
        AND
        m.name = #{dto.newName}
    </select>

</mapper>