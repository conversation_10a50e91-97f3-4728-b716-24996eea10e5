dev:
  hostname: ***************
  eureka:
    hostname: ***************
  redis:
    hostname: **************
  log:
    hostname: ${dev.hostname}

eureka:
  instance:
    lease-expiration-duration-in-seconds: 10
    lease-renewal-interval-in-seconds: 5
    prefer-ip-address: true
    instance-id: ${spring.cloud.client.ip-address}:${server.port}
  client:
    serviceUrl:
      defaultZone: http://${dev.eureka.hostname}:8141/eureka/

server:
  port: 8928

spring:
  application:
    name: holder-saas-store-media

  redis:
    database: 0
    host: ${dev.redis.hostname}
    port: 36380
    password: eIx6TynJq
    timeout: 5000ms
    jedis:
      pool:
        max-active: 50
        max-idle: 50
        min-idle: 20
        max-wait: -1ms

#自定义配置：用来判断是否执行手动切换数据源的方法
self:
  open-dynamic-datasource: true

# 动态数据源
dynamic:
  intercept:
    datasource:
      include-url: /**
      exclude-url:
  server:
    #如果本服务需要对表分库，那么需要配置本服务的所有表名,以逗号隔开
    #sharding-tables: person
    #必须制定当前服务的名称
    server-code: holder_saas_store_media

mybatis:
  type-aliases-package: com.holderzone.holder.saas.store.media.core.entity,com.holderzone.holder.saas.store.media.dto.entity
  mapper-locations: classpath:mapper/*.xml