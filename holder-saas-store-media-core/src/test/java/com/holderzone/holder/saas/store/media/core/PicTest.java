package com.holderzone.holder.saas.store.media.core;

import com.alibaba.fastjson.JSON;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.store.media.core.entity.HsmMedia;
import com.holderzone.holder.saas.store.media.core.utils.SpringContextUtil;
import com.holderzone.holder.saas.store.media.dto.entity.MediaDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;


import java.util.List;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DineInOrderDOTest
 * @date 2019/01/15 11:00
 * @description //TODO
 * @program holder-saas-store-dto
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = HolderSaasStoreMediaCoreApplication.class)
@WebAppConfiguration
public class PicTest {

    private static final String encode = "%7B%22enterpriseGuid%22%3A%226506431195651982337%22%2C%22enterpriseName%22" +
            "%3A%22%E4%BC%81%E4%B8%9A0227%22%2C%22enterpriseNo%22%3A%**********%22%2C%22storeGuid%22%3A" +
            "%226506453252643487745%22%2C%22storeName%22%3A%22%E9%97%A8%E5%BA%970227_3%22%2C%22storeNo%22%3A" +
            "%*********%22%2C%22userGuid%22%3A%226507063794701697025%22%2C%22account%22%3A%********%22%2C%22tel%22%3A" +
            "%*************%22%2C%22name%22%3A%22wg%22%7D";
    @Autowired
    private WebApplicationContext wac;
    private MockMvc mockMvc;

    @Autowired
    private ApplicationContext ap;

    @Before
    public void setupMockMvc() throws Exception {
        SpringContextUtil.getInstance().setCfgContext((ConfigurableApplicationContext) ap);
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();
    }

    @Test
    public void saveOrder() throws Exception {
        String str = "[{\"brandGuid\":\"6564767921501896704\",\"storeGuid\":\"\",\"name\":\"\",\"fileUrl\":\"https://holder-dev.oss-cn-hangzhou.aliyuncs.com/framework-dev/2020-04-03/1585926018681_4be96.jpg\",\"parentGuid\":\"\",\"isFolder\":false,\"isDelete\":false,\"isBrand\":true,\"fileType\":0,\"remark\":\"\"},{\"brandGuid\":\"6564767921501896704\",\"storeGuid\":\"\",\"name\":\"\",\"fileUrl\":\"http://oss-sit.holderzone.cn/HesStoreWeChatFile/unifyhome/important/2019-09-23/201909231521401029.jpg\",\"parentGuid\":\"\",\"isFolder\":false,\"isDelete\":false,\"isBrand\":true,\"fileType\":0,\"remark\":\"\"}]";

        List<MediaDTO> param = JacksonUtils.toObjectList(MediaDTO.class, str);
        MvcResult mvcResult = mockMvc.perform(post("/file/batch/create_file").header("userInfo", encode).accept
                (MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JSON.toJSONString(param)))
                .andExpect(status().isOk()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();

        System.out.println("sout:" + contentAsString);
    }



}
