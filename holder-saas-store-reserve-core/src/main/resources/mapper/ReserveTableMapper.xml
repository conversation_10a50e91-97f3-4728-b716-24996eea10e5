<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.reserve.core.dal.mapper.ReserveRecordTableRelationDoMapper">

    <sql id="field">
       `guid`, `table_guid`,`table_name`,`area_guid`,`area_name`,`state`,`reserve_record_guid`
    </sql>
    <sql id="t_field">
   	t.`guid`, t.`table_guid`, t.`table_name`, t.`area_guid`, t.`area_name`, t.`state`, t.`reserve_record_guid`
    </sql>
    <insert id="batchInsert">
        <foreach collection="tables" item="item" separator=";">
            insert into hss_r_reserve_record_table (
            <include refid="field"/>
            )
            select
            #{item.guid},#{item.tableGuid},#{item.tableName},#{item.areaGuid},#{item.areaName},#{item.state},#{item.reserveRecordGuid}
            from dual
            <if test="removeSame">
                where not exists (
                select 1 from
                hss_r_reserve_record_table t
                inner join hss_reserve_record r on r.guid = t.reserve_record_guid and t.is_deleted = 0 and r.is_deleted
                = 0
                and t.table_guid = #{item.tableGuid}
                and r.state in (32,38)
                and
                r.reserve_start_time >= #{dto.startTime} and r.reserve_start_time &lt; #{dto.endTime}
                )
            </if>
        </foreach>
    </insert>
    <insert id="saveBatch">
        <foreach collection="tables" item="item" separator=";">
            insert into hss_r_reserve_record_table (
            <include refid="field"/>
            )
            values (
            #{item.guid},
            #{item.tableGuid},
            #{item.tableName},
            #{item.areaGuid},
            #{item.areaName},
            #{item.state},
            #{item.reserveRecordGuid}
            )
        </foreach>
    </insert>
    <delete id="deleteByReserveGuid">
            delete from hss_r_reserve_record_table
            where reserve_record_guid = #{guid}
    </delete>
    <select id="queryReserveGuidByTableAndState"
            resultType="com.holderzone.saas.store.reserve.core.dal.ReserveRecordTableRelationDo">
        select t.reserve_record_guid,t.table_guid
        from hss_r_reserve_record_table t
        inner join hss_reserve_record r on r.guid = t.reserve_record_guid and t.is_deleted = 0 and r.is_deleted = 0
        where
        <if test="tableGuids != null and tableGuids.size() >0">
            t.table_guid in
            <foreach collection="tableGuids" item="tableGuid" open="(" close=")" separator=",">
                #{tableGuid}
            </foreach>
        </if>
        <if test="tableGuids == null or tableGuids.size() == 0">
            1!=1
        </if>
        <if test="type != null">
            <choose>
                <when test="type == 1">
                    and t.state in (1,0)
                    and r.state in (62,54,38)
                </when>
                <otherwise>
                    and t.state = 1
                    and r.state in (38)
                </otherwise>
            </choose>
        </if>
    </select>
    <select id="queryTableAfterAndIn" resultType="java.lang.String">
        select
        t.table_guid
        from hss_r_reserve_record_table t
        inner join hss_reserve_record r on r.guid = t.reserve_record_guid and t.is_deleted = 0 and r.is_deleted = 0
        where 1 = 1
        <if test="tableGuids == null">
            and 1 != 1
        </if>
        <if test="tableGuids != null">
            and t.table_guid in
            <foreach collection="tableGuids" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        and r.guid != #{record}
        and t.state = 0
        and r.state in (38)
        and r.reserve_start_time > #{time}
    </select>
    <select id="queryTablesByTimeSegment" resultType="com.holderzone.saas.store.reserve.core.dal.TableTimeDo">
        select
        <include refid="t_field"/>,r.reserve_start_time
        from hss_r_reserve_record_table t
        inner join hss_reserve_record r on r.guid = t.reserve_record_guid and t.is_deleted = 0 and r.is_deleted = 0
        where
        r.store_guid = #{storeGuid}
        and r.reserve_start_time between #{startTime} and #{endTime}
        and r.state in (32,38)
        and (r.is_delay is null or r.is_delay = false)
        <if test="areaGuid != null and areaGuid != &quot;&quot;">
            and t.area_guid = #{areaGuid}
        </if>
        <if test="recordGuid != null and recordGuid != &quot;&quot;">
            and r.guid != #{recordGuid}
        </if>
    </select>
    <select id="queryTableGuidBeReseved" resultType="java.lang.String">
        select t.table_name table_name from
        hss_r_reserve_record_table t
        inner join hss_reserve_record r on r.guid = t.reserve_record_guid and t.is_deleted = 0 and r.is_deleted = 0
        and t.table_guid in
        <foreach open="(" close=")" collection="tables" item="item" separator=",">
            #{item.tableGuid}
        </foreach>
        and r.state in (32,38)
        and r.guid &lt;&gt; #{dto.guid}
        and r.reserve_start_time >= #{dto.startTime} and r.reserve_start_time &lt; #{dto.endTime}
    </select>


    <select id="queryTablesByReserveStartTime"
            resultType="com.holderzone.saas.store.reserve.core.dal.TableTimeDo">
        select
            <include refid="t_field"/>,
            r.reserve_start_time
        from
            hss_r_reserve_record_table t
        join hss_reserve_record r on r.guid = t.reserve_record_guid and t.is_deleted = 0 and r.is_deleted = 0
        where
            r.store_guid = #{storeGuid}
            and r.reserves_end_time >= #{reserveStartTime} and r.reserve_lock_time &lt;= #{reserveStartTime}
            and r.state in (32,38)
            and (r.is_delay is null or r.is_delay = false)
            <if test="areaGuid != null and areaGuid != &quot;&quot;">
                and t.area_guid = #{areaGuid}
            </if>
            <if test="recordGuid != null and recordGuid != &quot;&quot;">
                and r.guid != #{recordGuid}
            </if>
    </select>


</mapper>