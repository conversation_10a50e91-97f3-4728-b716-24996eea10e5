<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.reserve.core.dal.mapper.ReserveItemDOMapper">

    <select id="itemCount" resultType="com.holderzone.saas.store.dto.reserve.ReserveItemDTO">
        SELECT ri.item_type_name, ri.item_name, sum( ri.num ) num, sum( ri.item_price ) item_price
        FROM hss_reserve_item ri, hss_reserve_record rr
        WHERE
            ri.record_guid = rr.guid
          AND rr.store_guid = #{storeGuid}
          <if test="type == 1">
            AND ri.item_type IN ( 2, 3, 4 )
          </if>
          <if test="type == 2">
            AND ri.item_type IN ( 1, 5 )
          </if>
          AND rr.reserve_start_time BETWEEN #{startTime} AND #{endTime}
          AND rr.state NOT IN ( 35, 47, 62 )
        GROUP BY
            ri.item_type_name, ri.item_name
    </select>
</mapper>