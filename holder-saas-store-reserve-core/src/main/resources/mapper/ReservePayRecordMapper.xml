<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.reserve.core.dal.mapper.ReservePayRecordDoMapper">


    <select id="getReserveStatistic" resultType="com.holderzone.saas.store.dto.reserve.ReservePayDTO">
        select count(*) payCount,sum(reserve_amount) payAmount,pay_type payType,pay_type_name payName
        from hss_reserve_pay_record
        where state in( 4,5)
        and
        store_guid
        in
        <foreach item="storeGuid" collection="storeGuids" open="(" separator="," close=")">
            #{storeGuid}
        </foreach>
        <if test="startTime != null">
            and gmt_modified >= #{startTime}
        </if>
        <if test="endTime != null">
            and gmt_modified &lt;= #{endTime}
        </if>
        group by pay_type_name,pay_type

    </select>

</mapper>