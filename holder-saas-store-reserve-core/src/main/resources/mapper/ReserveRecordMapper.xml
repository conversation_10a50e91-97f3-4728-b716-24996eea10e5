<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.saas.store.reserve.core.dal.mapper.ReserveRecordDoMapper">

    <sql id="field">
        `id`,
        `guid`,
        `store_guid`,
        `number`,
        `state`,
        `is_locked`,
        `phone`,
        `gender`,
        `name`,
        `device_type`,
        `is_delay`,
        `device_id`,
        `main_order_guid`,
        `payment_type`,
        `payment_type_name`,
        `reserve_amount`,
        `tag`,
        `remark`,
        `items_str`,
        `reserve_start_time`,
        `reserves_end_time`,
        `confirm_user_guid`,
        `confirm_user_name`,
        `confirm_time`,
        `cancel_user_guid`,
        `cancel_user_name`,
        `cancle_time`,
        `cancle_reason`,
        `arrive_time`,
        `arrive_user_guid`,
        `arrive_user_name`,
        `is_enable`,
        `is_deleted`,
        `create_staff_guid`,
        `create_staff_name`,
        `modified_staff_guid`,
        `modified_staff_name`,
        `gmt_create`,
        `gmt_modified`,
        `order_no`,
        `area`,
        `create_user_phone`,
        `max_cancelable_time`,
        `cancel_role`,
        `reserve_lock_time`,
        `share_url`,
        `payment_time`,
        `oper_subject_guid`,
        `member_info_guid`,
        `member_info_card_guid`,
        `order_type`,
        `refund_amount`
    </sql>

    <select id="statistics" resultType="com.holderzone.saas.store.reserve.api.dto.GuidStateMappingDTO">
        select
        `guid`,
        `state`,
        `is_delay` as isDelay
        from hss_reserve_record
        where store_guid = #{storeGuid}
        and phone = #{phone}
        and is_deleted = 0
    </select>

    <select id="reserveAmountQuery" resultType="com.holderzone.saas.store.reserve.api.dto.ReserveAmountPaymentDTO">
       SELECT aa.payment_type, aa.payment_type_name,
              sum(aa.reserve_count) reserve_count, sum(aa.reserve_amount) reserve_amount FROM
       (SELECT rr.payment_type, rr.payment_type_name, count(1) reserve_count, sum(rr.reserve_amount - rr.refund_amount) reserve_amount
        FROM hss_reserve_record rr
        WHERE rr.store_guid = #{dto.storeGuid}
          AND rr.state IN (32, 38, 62, 54, 56)
          AND rr.confirm_time BETWEEN #{dto.gmtCreate} AND #{dto.gmtModified}
          AND rr.confirm_user_guid = #{dto.userGuid}
        GROUP BY rr.payment_type, rr.payment_type_name
        UNION ALL
        SELECT rr.payment_type, rr.payment_type_name, 0 reserve_count, sum(-rr.reserve_amount) reserve_amount
        FROM hss_reserve_record rr
        WHERE rr.store_guid = #{dto.storeGuid}
          AND rr.state IN (3, 35)
          AND rr.cancle_time BETWEEN #{dto.gmtCreate} AND #{dto.gmtModified}
          AND #{dto.gmtCreate} > rr.confirm_time
          AND rr.confirm_user_guid = #{dto.userGuid}
        GROUP BY rr.payment_type, rr.payment_type_name) aa
        GROUP BY aa.payment_type, aa.payment_type_name
    </select>

    <select id="gather" resultType="com.holderzone.saas.store.dto.order.response.daily.GatherRespDTO">
        SELECT
            pr.pay_type gather_code,
            pr.pay_type_name gather_name,
            sum( pr.reserve_amount ) reserve_amount
        FROM
            hss_reserve_record rr,
            hss_reserve_pay_record pr
        WHERE
            rr.guid = pr.reserve_guid
          AND rr.store_guid = #{storeGuid}
          AND rr.reserve_amount > 0
          AND rr.confirm_time BETWEEN #{beginTime} AND #{endTime}
        GROUP BY
            pr.pay_type,
            pr.pay_type_name
    </select>


    <select id="querySameTimeRecord"
            resultType="com.holderzone.saas.store.reserve.core.dal.ReserveRecordDo">
        select
            *
        from
            hss_reserve_record
        where
            reserve_start_time = #{query.reserveStartTime}
            and phone = #{query.phone}
            and state in (32, 38)
            and is_deleted = 0
        order by id desc
        limit 1
    </select>


    <select id="obtainRecordList"
            resultType="com.holderzone.saas.store.reserve.core.dal.ReserveRecordDo">
        select
            guid,
            store_guid,
            number,
            state,
            reserve_start_time,
            gmt_create,
            area,
            reserve_amount,
            refund_amount,
            is_delay,
            cancle_reason
        from
            hss_reserve_record
        <where>
            create_staff_guid = #{query.createUserId}
            and is_deleted = 0
            <if test="query.getStates != null and query.getStates().size()>0">
                and state in
                <foreach collection="query.getStates" item="state" open="(" separator="," close=")">
                    #{state}
                </foreach>
            </if>
            <if test="query.isDelay != null and query.isDelay">
                or ( is_delay = true and state = 38 and create_staff_guid = #{query.createUserId} and is_deleted = 0 )
            </if>
            <if test="query.isDelay != null and !query.isDelay">
                and is_delay = false
            </if>
        </where>
        order by gmt_create desc, id desc
    </select>


    <select id="queryLastTimeRecord" resultType="com.holderzone.saas.store.reserve.core.dal.ReserveRecordDo">
        select
            guid,
            store_guid,
            state,
            reserve_start_time
        from
            hss_reserve_record
        <where>
            create_staff_guid = #{query.createUserId}
            and is_deleted = 0
            <if test="query.getStates != null and query.getStates().size()>0">
                and state in
                <foreach collection="query.getStates" item="state" open="(" separator="," close=")">
                    #{state}
                </foreach>
            </if>
            <if test="query.isDelay != null">
                and is_delay = #{query.isDelay}
            </if>
        </where>
        order by reserve_start_time asc, id asc
        limit 1
    </select>


    <select id="commitStatistics" resultType="java.lang.Long">
        select
            IFNULL(count(1), 0)
        from
            hss_reserve_record
        where
            store_guid = #{storeGuid}
            and is_deleted = 0
            and state = 32
    </select>

    <select id="commitStatisticsByDay"
            resultType="com.holderzone.saas.store.dto.reserve.ReserveCommitStaticsDTO$InnerDay">
        select
            DATE_FORMAT(reserve_start_time, '%Y-%m-%d') as time,
            IFNULL(count(1), 0) as total
        from
            hss_reserve_record
        where
            store_guid = #{storeGuid}
            and is_deleted = 0
            and state = 32
        group by
            DATE_FORMAT(reserve_start_time, '%Y-%m-%d')
    </select>

    <select id="commitStatisticsTotalByScope" resultType="java.lang.Long">
        select
            IFNULL(count(1), 0)
        from
            hss_reserve_record
        where
            store_guid = #{dto.storeGuid}
            and DATE_FORMAT(reserve_start_time, '%Y-%m-%d') BETWEEN #{dto.startDate} AND #{dto.endDate}
            and is_deleted = 0
            and state = 38
            and order_type = 0
            and is_delay = 0
    </select>

    <select id="commitStatisticsByScope"
            resultType="com.holderzone.saas.store.dto.reserve.ReserveCommitStaticsDTO$InnerDay">
         select
            DATE_FORMAT(reserve_start_time, '%Y-%m-%d') as time,
            IFNULL(count(1), 0) as total
        from
            hss_reserve_record
        where
            store_guid = #{dto.storeGuid}
            and is_deleted = 0
            and state = 38
            and DATE_FORMAT(reserve_start_time, '%Y-%m-%d') BETWEEN #{dto.startDate} AND #{dto.endDate}
            and order_type = 0
            and is_delay = 0
        group by
            DATE_FORMAT(reserve_start_time, '%Y-%m-%d')
    </select>

    <select id="queryReserveAmount" resultType="java.math.BigDecimal">
        SELECT
            reserve_amount - refund_amount
        FROM
            hss_reserve_record
        WHERE
            is_deleted = 0
            AND main_order_guid = #{orderGuid}
    </select>

    <select id="queryByGuid" resultType="com.holderzone.saas.store.reserve.core.dal.ReserveRecordDo">
        SELECT
            <include refid="field"/>
        FROM
            hss_reserve_record
        WHERE
            guid = #{guid}
    </select>

    <select id="queryByOrderGuid"
            resultType="com.holderzone.saas.store.reserve.core.dal.ReserveRecordDo">
        SELECT
            <include refid="field"/>
        FROM
            hss_reserve_record
        WHERE
            main_order_guid = #{orderGuid}
        ORDER BY
            gmt_create DESC
        limit 1
    </select>

    <select id="queryReserveTable" resultType="com.holderzone.saas.store.dto.reserve.TableDTO">
        SELECT
            guid,
            table_guid AS guid,
            table_name AS `name`,
            area_guid AS areaGuid,
            area_name AS areaName,
            reserve_record_guid
        FROM
            `hss_r_reserve_record_table`
        WHERE
            is_deleted = 0
            AND reserve_record_guid = #{guid}
    </select>

</mapper>