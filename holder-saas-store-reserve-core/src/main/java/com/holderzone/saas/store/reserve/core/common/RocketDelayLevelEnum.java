package com.holderzone.saas.store.reserve.core.common;

import com.holderzone.framework.util.DateTimeUtils;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Comparator;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @className RocketDelayLevelEnum
 * @date 2019/05/30 16:39
 * @description //TODO
 * @program holder-saas-store-reserve
 */
public enum RocketDelayLevelEnum {

    _1(1 ,1, TimeUnit.SECONDS),
    _2(2 ,5, TimeUnit.SECONDS),
    _3(3 ,10, TimeUnit.SECONDS),
    _4(4 ,30, TimeUnit.SECONDS),
    _5(5 ,1, TimeUnit.MINUTES),
    _6(6 ,2, TimeUnit.MINUTES),
    _7(7 ,3, TimeUnit.MINUTES),
    _8(8 ,4, TimeUnit.MINUTES),
    _9(9 ,5, TimeUnit.MINUTES),
    _10(10 ,6, TimeUnit.MINUTES),
    _11(11 ,7, TimeUnit.MINUTES),
    _12(12 ,8, TimeUnit.MINUTES),
    _13(13 ,9, TimeUnit.MINUTES),
    _14(14 ,10, TimeUnit.MINUTES),
    _15(15 ,20, TimeUnit.MINUTES),
    _16(16 ,30, TimeUnit.MINUTES),
    _17(17 ,1, TimeUnit.HOURS),
    _18(18 ,2, TimeUnit.HOURS),
    ;
    private Integer level;
    private Integer time;
    private TimeUnit timeUnit;

    RocketDelayLevelEnum(Integer level, Integer time, TimeUnit timeUnit) {
        this.level = level;
        this.time = time;
        this.timeUnit = timeUnit;
    }

    public Integer getLevel() {
        return level;
    }

    public Integer getTime() {
        return time;
    }

    public TimeUnit getTimeUnit() {
        return timeUnit;
    }

    public static RocketDelayLevelEnum fetchMatched(LocalDateTime target){
        return fetchMatched(target,LocalDateTime.now());
    }
    public static RocketDelayLevelEnum fetchMatched(LocalDateTime target, LocalDateTime ref){
        Long mil = DateTimeUtils.localDateTime2Mills(target) - DateTimeUtils.localDateTime2Mills(ref);
        return Arrays.stream(values())
                .sorted(Comparator.comparingInt(RocketDelayLevelEnum::getLevel).reversed())
                .filter(e->mil>=e.getTimeUnit().toMillis(e.getTime()))
                .findFirst()
                .orElse(null);
    }
}