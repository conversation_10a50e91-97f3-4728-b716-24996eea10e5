package com.holderzone.saas.store.reserve.core.controller;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.reserve.TableDTO;
import com.holderzone.saas.store.reserve.api.ReserveSmallQueryApi;
import com.holderzone.saas.store.reserve.core.service.ReserveRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/28
 * @description
 */
@Slf4j
@Primary
@RestController
public class ReserveSmallQueryControllerImpl implements ReserveSmallQueryApi {

    @Autowired
    private ReserveRecordService reserveRecordService;

    /**
     * 查询订单当前实际预定金额
     */
    @Override
    public BigDecimal queryReserveAmount(SingleDataDTO query) {
        log.info("[查询订单当前实际预定金额]query={}", JacksonUtils.writeValueAsString(query));
        return reserveRecordService.queryReserveAmount(query);
    }

    /**
     * 查询订单所属桌台
     */
    @Override
    public List<TableDTO> queryReserveTable(SingleDataDTO query) {
        log.info("[查询订单所属桌台]query={}", JacksonUtils.writeValueAsString(query));
        return reserveRecordService.queryReserveTable(query);
    }

}
