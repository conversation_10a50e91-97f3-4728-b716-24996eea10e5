package com.holderzone.saas.store.reserve.core.common;

import com.holderzone.saas.store.dto.common.BaseDTO;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BaseDTOThreadLocal
 * @date 2019/05/30 18:57
 * @description //TODO
 * @program holder-saas-store-reserve
 */
public abstract class BaseDTOThreadLocal {
    private static final ThreadLocal<BaseDTO> threadLocal = new ThreadLocal<>();


    public static void set(BaseDTO baseDTO){
        threadLocal.set(baseDTO);
    }
    public static BaseDTO get(){
        return threadLocal.get();
    }
}