package com.holderzone.saas.store.reserve.core.event.impl.handler;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.anno.CustomerRegister;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.event.CustomerEvent;
import com.holderzone.framework.event.observer.CustomerObserver;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.redisson.sdk.lock.RedissonLockUtil;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.enums.PaymentTypeEnum;
import com.holderzone.saas.store.reserve.api.enums.OrderTypeEnum;
import com.holderzone.saas.store.reserve.api.enums.PayStateEnum;
import com.holderzone.saas.store.reserve.core.dal.ReserveRecordDo;
import com.holderzone.saas.store.reserve.core.dal.mapper.ReserveRecordDoMapper;
import com.holderzone.saas.store.reserve.core.domain.ReserveRecord;
import com.holderzone.saas.store.reserve.core.domain.ReserveRecordStateEnum;
import com.holderzone.saas.store.reserve.core.event.BaseEventHandler;
import com.holderzone.saas.store.reserve.core.event.impl.event.AggPayEvent;
import com.holderzone.saas.store.reserve.core.event.impl.event.DelayEvent;
import com.holderzone.saas.store.reserve.core.event.impl.event.SystemMessageEvent;
import com.holderzone.saas.store.reserve.core.rocket.Consume;
import com.holderzone.saas.store.reserve.core.service.ReservePayRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Objects;
import java.util.concurrent.ExecutorService;


/**
 * 微信支付回调通知成功处理
 */
@Slf4j
@Component
@RequiredArgsConstructor
@CustomerRegister(isRegister = true)
public class AggPayEventHandler extends BaseEventHandler<AggPayEvent> implements CustomerObserver<AggPayEvent> {

    private final ReserveRecordDoMapper reserveRecordDoMapper;

    private final ReservePayRecordService reservePayRecordService;

    private final ExecutorService payAfterExecutor;

    @Override
    public void execute(AggPayEvent baseEvent) {
        ReserveRecord reserveRecord = baseEvent.getReserveRecord();
        if (ReserveRecordStateEnum.COMMIT.getCode() == reserveRecord.getState()) {
            log.warn("订单已支付, 跳过处理, reserveRecord:{}", JacksonUtils.writeValueAsString(reserveRecord));
            return;
        }
        // 修改预定单信息
        updateReserveRecordDO(baseEvent);
        // 修改聚合支付记录
        savePayRecordDO(baseEvent);
        // 聚合支付后置处理
        aggPayAfterHandler(baseEvent);
    }

    /**
     * 修改预定单信息
     */
    private void updateReserveRecordDO(AggPayEvent baseEvent) {
        ReserveRecord reserveRecord = baseEvent.getReserveRecord();
        // update
        ReserveRecordDo recordDO = new ReserveRecordDo();
        recordDO.setGuid(reserveRecord.getGuid());
        recordDO.setState(ReserveRecordStateEnum.COMMIT.getCode());
        recordDO.setPaymentType(PaymentTypeEnum.AGG.getCode());
        recordDO.setPaymentTypeName(PaymentTypeEnum.AGG.getDesc());
        recordDO.setPaymentTime(LocalDateTime.now());
        reserveRecordDoMapper.update(recordDO,
                new LambdaQueryWrapper<ReserveRecordDo>()
                        .eq(ReserveRecordDo::getGuid, reserveRecord.getGuid()));
        // copy
        reserveRecord.setState(ReserveRecordStateEnum.COMMIT.getCode());
        reserveRecord.setPaymentType(recordDO.getPaymentType());
        reserveRecord.setPaymentTypeName(recordDO.getPaymentTypeName());
        reserveRecord.setPaymentTime(recordDO.getPaymentTime());
    }

    /**
     * 保存支付记录
     */
    private void savePayRecordDO(AggPayEvent baseEvent) {
        ReserveRecord reserveRecord = baseEvent.getReserveRecord();
        // 更改预定支付记录状态
        reservePayRecordService.updatePayRecordStateAndInfo(reserveRecord.getPayGuid(), PayStateEnum.SUCCESS, null);
    }

    /**
     * 聚合支付后置处理
     */
    private void aggPayAfterHandler(AggPayEvent baseEvent) {
        ReserveRecord reserveRecord = baseEvent.getReserveRecord();
        if (Objects.equals(reserveRecord.getOrderType(), OrderTypeEnum.RESERVE_PAY.getCode())) {
            return;
        }
        UserContext userContext = UserContextUtils.get();
        payAfterExecutor.execute(() -> {
            UserContextUtils.put(userContext);
            EnterpriseIdentifier.setEnterpriseGuid(userContext.getEnterpriseGuid());
            SystemMessageEvent systemMessageEvent = new SystemMessageEvent(reserveRecord);
            customerPublish.publish(new CustomerEvent<>(systemMessageEvent));
        });
        // 商家未接单2小时自动取消
        payAfterExecutor.execute(() -> {
            UserContextUtils.put(userContext);
            EnterpriseIdentifier.setEnterpriseGuid(userContext.getEnterpriseGuid());
            DelayEvent delayEvent = new DelayEvent(reserveRecord, Consume.ACCEPT_DELAY_TAG);
            customerPublish.publish(new CustomerEvent<>(delayEvent));
        });
    }


    @Override
    protected void pre(AggPayEvent baseEvent) {
        ReserveRecord reserveRecord = baseEvent.getReserveRecord();
        boolean result = RedissonLockUtil.tryLock(reserveRecord.getGuid(), 3, 10);
        if (!result) {
            throw new BusinessException("当前预定记录正被其他人操作,请稍候重试!!");
        }
        super.pre(baseEvent);
    }

    @Override
    protected void after(AggPayEvent baseEvent) {
        ReserveRecord reserveRecord = baseEvent.getReserveRecord();
        RedissonLockUtil.unlock(reserveRecord.getGuid());
        super.after(baseEvent);
    }
}