package com.holderzone.saas.store.reserve.core.event.impl.handler;

import com.holderzone.framework.anno.CustomerRegister;
import com.holderzone.framework.event.observer.CustomerObserver;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.message.BusinessMessageDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.takeaway.OrderType;
import com.holderzone.saas.store.enums.BaseDeviceTypeEnum;
import com.holderzone.saas.store.enums.msg.BusinessMsgTypeEnum;
import com.holderzone.saas.store.reserve.api.dto.ReserveSystemMsgDTO;
import com.holderzone.saas.store.reserve.core.domain.ReserveRecord;
import com.holderzone.saas.store.reserve.core.domain.ReserveRecordStateEnum;
import com.holderzone.saas.store.reserve.core.event.BaseEventHandler;
import com.holderzone.saas.store.reserve.core.event.impl.event.SystemMessageEvent;
import com.holderzone.saas.store.reserve.intergration.table.MessageClientService;
import com.holderzone.saas.store.reserve.intergration.table.OrganizationClientService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Objects;


/**
 * 系统消息通知
 */
@Slf4j
@Component
@CustomerRegister(isRegister = true)
public class SystemMessageEventHandler extends BaseEventHandler<SystemMessageEvent> implements CustomerObserver<SystemMessageEvent> {

    @Autowired
    private OrganizationClientService organizationClientService;

    @Autowired
    private MessageClientService messageClientService;

    @Override
    public void execute(SystemMessageEvent baseEvent) {
        ReserveRecord reserveRecord = baseEvent.getReserveRecord();
        if (!BaseDeviceTypeEnum.isApplet(reserveRecord.getDeviceType())) {
            sendMsgByPos(reserveRecord);
            return;
        }
        if (ReserveRecordStateEnum.COMMIT.getCode() != reserveRecord.getState()
                && ReserveRecordStateEnum.SYSTEM_CANCLE.getCode() != reserveRecord.getState()
                && ReserveRecordStateEnum.CANCLE.getCode() != reserveRecord.getState()
                && ReserveRecordStateEnum.PASS.getCode() != reserveRecord.getState()) {
            log.warn("当前预定单流程无需通知, state:{}, reserveRecord:{}", reserveRecord.getState(), JacksonUtils.writeValueAsString(reserveRecord));
            return;
        }
        // 发送系统消息通知
        // 查询门店名称
        StoreDTO storeDTO = organizationClientService.queryStoreByGuid(reserveRecord.getStoreGuid());
        BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setMessageType(1);
        businessMessageDTO.setPlatform(String.valueOf(OrderType.OTHER_ORDER.getType()));
        businessMessageDTO.setStoreGuid(reserveRecord.getStoreGuid());
        businessMessageDTO.setStoreName(storeDTO.getName());
        if (ReserveRecordStateEnum.COMMIT.getCode() == reserveRecord.getState()) {
            businessMessageDTO.setDetailMessageType(BusinessMsgTypeEnum.RESERVE_RECORD_SUBMIT.getId());
            businessMessageDTO.setSubject("您有一笔新的预定单需要处理。");
        } else if (Objects.equals(BaseDeviceTypeEnum.All_IN_ONE.getCode(), reserveRecord.getOperateDeviceType())) {
            businessMessageDTO.setDetailMessageType(BusinessMsgTypeEnum.RESERVE_RECORD_STATE_CHANGE.getId());
            businessMessageDTO.setSubject("您有一笔预定单状态发生变化。");
        } else if (ReserveRecordStateEnum.SYSTEM_CANCLE.getCode() == reserveRecord.getState()
                || ReserveRecordStateEnum.CANCLE.getCode() == reserveRecord.getState()) {
            businessMessageDTO.setDetailMessageType(BusinessMsgTypeEnum.RESERVE_RECORD_CANCEL.getId());
            businessMessageDTO.setSubject("您有一笔预定单已取消。");
        }
        ReserveSystemMsgDTO reserveSystemMsgDTO = new ReserveSystemMsgDTO();
        reserveSystemMsgDTO.setGuid(reserveRecord.getGuid());
        reserveSystemMsgDTO.setAutoRev(0);
        businessMessageDTO.setContent(JacksonUtils.writeValueAsString(reserveSystemMsgDTO));
        businessMessageDTO.setCreateTime(LocalDateTime.now());
        businessMessageDTO.setPushTime(LocalDateTime.now());
        log.info("预定消息推送入参:{}", JacksonUtils.writeValueAsString(businessMessageDTO));
        messageClientService.msg(businessMessageDTO);
    }

    private void sendMsgByPos(ReserveRecord reserveRecord) {
        // 发送系统消息通知
        // 查询门店名称
        StoreDTO storeDTO = organizationClientService.queryStoreByGuid(reserveRecord.getStoreGuid());
        BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setMessageType(BusinessMsgTypeEnum.TABLE_STATE_MSG_TYPE.getId());
        businessMessageDTO.setPlatform(String.valueOf(OrderType.OTHER_ORDER.getType()));
        businessMessageDTO.setStoreGuid(reserveRecord.getStoreGuid());
        businessMessageDTO.setStoreName(storeDTO.getName());
        businessMessageDTO.setDetailMessageType(BusinessMsgTypeEnum.RESERVE_RECORD_STATE_CHANGE_POS.getId());
        businessMessageDTO.setSubject("");
        ReserveSystemMsgDTO reserveSystemMsgDTO = new ReserveSystemMsgDTO();
        reserveSystemMsgDTO.setGuid(reserveRecord.getGuid());
        reserveSystemMsgDTO.setAutoRev(0);
        businessMessageDTO.setContent(JacksonUtils.writeValueAsString(reserveSystemMsgDTO));
        businessMessageDTO.setCreateTime(LocalDateTime.now());
        businessMessageDTO.setPushTime(LocalDateTime.now());
        log.info("[SystemMessageEvent][POS]预定消息推送入参:{}", JacksonUtils.writeValueAsString(businessMessageDTO));
        messageClientService.msg(businessMessageDTO);
    }

}
