package com.holderzone.saas.store.reserve.core.event;

import com.holderzone.saas.store.reserve.core.domain.ReserveRecord;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BaseEvent
 * @date 2019/04/23 17:22
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Data
@AllArgsConstructor
public abstract class BaseEvent{
    private ReserveRecord reserveRecord;

    public abstract String getName();
}