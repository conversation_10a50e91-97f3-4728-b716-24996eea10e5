package com.holderzone.saas.store.reserve.core.event.impl.event;

import com.holderzone.saas.store.reserve.core.domain.ReserveRecord;
import com.holderzone.saas.store.reserve.core.event.BaseEvent;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DelayEvent
 * @date 2019/04/26 16:46
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Getter
public class DelayEvent extends BaseEvent {
    private String tag;

    public DelayEvent(ReserveRecord reserveRecord, String tag) {
        super(reserveRecord);
        this.tag = tag;
    }

    @Override
    public String getName() {
        return "DelayEvent";
    }
}