package com.holderzone.saas.store.reserve.core.event.impl.handler;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.anno.CustomerRegister;
import com.holderzone.framework.event.CustomerEvent;
import com.holderzone.framework.event.observer.CustomerObserver;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.redisson.sdk.lock.RedissonLockUtil;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.reserve.core.common.ReserveRoleEnum;
import com.holderzone.saas.store.reserve.core.domain.ReserveRecord;
import com.holderzone.saas.store.reserve.core.domain.ReserveRecordStateEnum;
import com.holderzone.saas.store.reserve.core.event.BaseEventHandler;
import com.holderzone.saas.store.reserve.core.event.impl.event.CancleEvent;
import com.holderzone.saas.store.reserve.core.event.impl.event.PayTimeoutEvent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;


/**
 * 小程序用户支付超时
 */
@Slf4j
@Component
@CustomerRegister(isRegister = true)
public class PayTimeoutEventHandler extends BaseEventHandler<PayTimeoutEvent> implements CustomerObserver<PayTimeoutEvent> {

    @Override
    public void execute(PayTimeoutEvent baseEvent) {
        ReserveRecord reserveRecord = baseEvent.getReserveRecord();
        if (reserveRecord.getState() != ReserveRecordStateEnum.NO_PAY.getCode()) {
            log.warn("预定单状态已发生变化,无法自动取消, reserveRecord:{}", JacksonUtils.writeValueAsString(reserveRecord));
            return;
        }
        // put
        UserContext userContext = UserContextUtils.get();
        if (StringUtils.isEmpty(userContext.getStoreGuid())) {
            userContext.setStoreGuid(reserveRecord.getStoreGuid());
            UserContextUtils.put(userContext);
        }
        // 取消预定单
        reserveRecord.setCancelRole(ReserveRoleEnum.USER.getRole());
        reserveRecord.setCancleReason("顾客超时未支付");
        CancleEvent cancleEvent = new CancleEvent(reserveRecord, true);
        customerPublish.publish(new CustomerEvent<>(cancleEvent));
    }

    @Override
    protected void after(PayTimeoutEvent baseEvent) {
        ReserveRecord reserveRecord = baseEvent.getReserveRecord();
        RedissonLockUtil.unlock(reserveRecord.getGuid());
        super.after(baseEvent);
    }

    @Override
    protected void pre(PayTimeoutEvent baseEvent) {
        ReserveRecord reserveRecord = baseEvent.getReserveRecord();
        boolean result = RedissonLockUtil.tryLock(reserveRecord.getGuid(), 3, 10);
        if (!result) {
            throw new BusinessException("当前预定记录正被其他人操作,请稍候重试!!");
        }
        super.pre(baseEvent);
    }
}