package com.holderzone.saas.store.reserve.core;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.holderzone.saas.store.reserve.core.common.SpringContextUtils;
import org.apache.curator.framework.recipes.queue.DistributedDelayQueue;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

@EnableAsync
@EnableSwagger2
@EnableDiscoveryClient
@EnableFeignClients(basePackages = "com.holderzone.saas.store.reserve")
@ComponentScan(basePackages = "com.holderzone.saas.store.reserve")
@SpringBootApplication
@MapperScan("com.holderzone.saas.store.reserve.core.dal.mapper")
@EnableApolloConfig
public class HolderSaasStoreReserveApplication implements CommandLineRunner {
    @Autowired
    private DistributedDelayQueue delayQueue;

    public static void main(String[] args) {
        try {
            ConfigurableApplicationContext context = SpringApplication.run(HolderSaasStoreReserveApplication.class, args);
            SpringContextUtils.getInstance().setCfgContext(context);
        }catch (Exception e){
            System.out.println(e.getMessage());
            e.printStackTrace();
        }
    }

    @Override
    public void run(String... args) throws Exception {
        delayQueue.start();
    }
}
