package com.holderzone.saas.store.reserve.core.rocket;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.base.dto.message.ShortMessageType;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.event.CustomerEvent;
import com.holderzone.framework.event.publish.impl.CustomerPublishImpl;
import com.holderzone.resource.common.dto.mq.UnMessage;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.reserve.api.dto.ReserveRecordTransferDTO;
import com.holderzone.saas.store.reserve.core.dal.ReserveRecordDo;
import com.holderzone.saas.store.reserve.core.dal.mapper.ReserveRecordDoMapper;
import com.holderzone.saas.store.reserve.core.domain.ReserveRecord;
import com.holderzone.saas.store.reserve.core.event.BaseEvent;
import com.holderzone.saas.store.reserve.core.event.impl.event.*;
import com.holderzone.saas.store.reserve.core.mapstruct.ReserveRecordMapstruct;
import com.holderzone.saas.store.reserve.intergration.table.OrganizationClientService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

public interface Consume {
    Logger log = LoggerFactory.getLogger(Consume.class);
    String LOCK_TAG = "reserve-delay-lock-table-tag";
    String BE_DELAY_TAG = "reserve-delay-be-delay-tag";
    String ACCEPT_DELAY_TAG = "reserve-delay-accept-delay-tag";
    String PAY_TIMEOUT_TAG = "reserve-delay-pay-timeout-tag";
    String WARN_MESSAGE_TAG = "reserve-delay-warn-tag";
    ThreadLocal<LocalDateTime> THREAD_LOCAL = new ThreadLocal<>();

    default void doConsume(UnMessage unMessage) {
        JSONObject jsonObject = (JSONObject) unMessage.getMessage();
        ReserveRecordTransferDTO dto = jsonObject.toJavaObject(ReserveRecordTransferDTO.class);
        log.info("zk messsage:{},enterpriseGuid:{}", dto,unMessage.getEnterpriseGuid());
        log.info(String.format("RESERVE GUID : %S ---- %S EVENT EXECUTE AT:-----------------------:%S", dto.getGuid(), dto.getTag(), System.currentTimeMillis()));
        THREAD_LOCAL.set(dto.getStartTime());
        EnterpriseIdentifier.setEnterpriseGuid(unMessage.getEnterpriseGuid());

        UserContextUtils.putErpAndStore(unMessage.getEnterpriseGuid(), unMessage.getStoreGuid());
        ReserveRecordDo recordDo = getReserveRecordDoMapper().selectOne(new LambdaQueryWrapper<ReserveRecordDo>().eq(ReserveRecordDo::getGuid, dto.getGuid()));
        if (recordDo == null) {
            log.error("未找到对应的预订信息：reserveGuid:{}", dto.getGuid());
            return;
        }
        ReserveRecord reserveRecord = ReserveRecordMapstruct.MAPSTRUCT.toDomain(recordDo);
        String tag = dto.getTag();
//        LocalDateTime dif = null;
        BaseEvent baseEvent = null;
        switch (tag) {
            case LOCK_TAG:
//                dif = BaseEventHandler.getStartDateTime(reserveRecord);
                baseEvent = new LockTableEvent(reserveRecord);
                break;
            case BE_DELAY_TAG:
//                dif = BaseEventHandler.getEndDateTime(reserveRecord);
                baseEvent = new BeDelayEvent(reserveRecord);
                break;
            case ACCEPT_DELAY_TAG:
                baseEvent = new AcceptDelayEvent(reserveRecord);
                break;
            case PAY_TIMEOUT_TAG:
                baseEvent = new PayTimeoutEvent(reserveRecord);
                break;
            case WARN_MESSAGE_TAG:
                baseEvent = new ShortMessageEvent(reserveRecord, (e) -> {
                    Map<String, String> params = new HashMap<>();
                    StoreDTO storeDTO = getOrganizationClientService().queryStoreByGuid(e.getStoreGuid());
                    params.put("storeName", storeDTO.getName());
                    return params;
                }, () -> ShortMessageType.RESERVE_EXPIRE);
//                dif = BaseEventHandler.getWarnDateTime(reserveRecord);
                break;
            default:
        }
//        if (!dif.isBefore(LocalDateTime.now())) {
//            baseEvent = new DelayEvent(reserveRecord, tag);
//        }
        try {
            getCustomerPublish().publish(new CustomerEvent<>(baseEvent));
        } catch (Exception e) {
            log.error("consumeMsg fail" +
                    "----------------tag: {}," +
                    "----------------guid:{}," +
                    "----------------record:{}", tag, dto, reserveRecord);
            log.error("consumeMsg fail", e);
        }
    }

    ReserveRecordDoMapper getReserveRecordDoMapper();

    CustomerPublishImpl getCustomerPublish();

    OrganizationClientService getOrganizationClientService();
}
