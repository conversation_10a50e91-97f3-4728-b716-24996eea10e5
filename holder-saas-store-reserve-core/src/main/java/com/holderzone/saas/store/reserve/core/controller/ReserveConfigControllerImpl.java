package com.holderzone.saas.store.reserve.core.controller;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.resource.common.dto.validate.Add;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.table.AreaDTO;
import com.holderzone.saas.store.reserve.api.ReserveConfigApi;
import com.holderzone.saas.store.reserve.api.dto.*;
import com.holderzone.saas.store.reserve.core.dal.ReserveConfigDo;
import com.holderzone.saas.store.reserve.core.service.PrivateRoomService;
import com.holderzone.saas.store.reserve.core.service.ReserveConfigService;
import com.holderzone.saas.store.reserve.core.support.ReserveConfigSupport;
import com.holderzone.saas.store.reserve.intergration.table.TableClientService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2019/05/05 11:05
 */
@Slf4j
@Primary
@RestController
public class ReserveConfigControllerImpl implements ReserveConfigApi {

    private final ReserveConfigService reserveConfigService;

    private final PrivateRoomService privateRoomService;

    private final TableClientService tableClientService;

    private final ReserveConfigSupport reserveConfigSupport;

    @Autowired
    public ReserveConfigControllerImpl(ReserveConfigService reserveConfigService, PrivateRoomService privateRoomService,
                                       TableClientService tableClientService, ReserveConfigSupport reserveConfigSupport) {
        this.reserveConfigService = reserveConfigService;
        this.privateRoomService = privateRoomService;
        this.tableClientService = tableClientService;
        this.reserveConfigSupport = reserveConfigSupport;
    }

    @Override
    public ReserveConfigDTO insert(@Validated(Add.class) @RequestBody ReserveConfigDTO dto) {
        return reserveConfigService.insert(dto);
    }

    @Override
    public ReserveConfigDTO update(@Validated(Add.class) @RequestBody ReserveConfigDTO dto) {
        ReserveConfigDTO update = reserveConfigService.update(dto);
        // cache
        reserveConfigSupport.remove(dto.getStoreGuid());
        return update;
    }

    @Override
    public ReserveConfigDTO query(@Validated @RequestBody StoreGuidDTO storeGuid) {
        return reserveConfigService.obtain(storeGuid.getStoreGuid());
    }

    @Override
    public List<String> queryRoom(@Validated @RequestBody StoreGuidDTO storeGuid) {
        return privateRoomService.query(storeGuid.getStoreGuid());
    }

    @Override
    public void saveRoom(@Validated @RequestBody StoreRoomDTO storeRoom) {
        privateRoomService.save(storeRoom.getStoreGuid(), storeRoom.getTableGuids());
    }

    @Override
    public void syncConfig(@Validated @RequestBody ReserveConfigSyncDTO reserveConfigSyncDTO) {
        log.info("同步门店配置入参:{}", JacksonUtils.writeValueAsString(reserveConfigSyncDTO));
        // 查询门店配置
        ReserveConfigDo reserveConfigDO = reserveConfigService.obtainByStoreGuid(reserveConfigSyncDTO.getStoreGuid());
        if (Objects.isNull(reserveConfigDO)) {
            throw new BusinessException("请先配置规则");
        }
        reserveConfigSyncDTO.getSyncStoreGuids().removeIf(e -> e.equals(reserveConfigSyncDTO.getStoreGuid()));
        if (CollectionUtils.isEmpty(reserveConfigSyncDTO.getSyncStoreGuids())) {
            log.warn("同步当前门店配置，直接跳过,入参:{}", JacksonUtils.writeValueAsString(reserveConfigSyncDTO));
            return;
        }
        // 批量查询门店区域
        SingleDataDTO singleDataDTO = new SingleDataDTO();
        List<String> dataList = new ArrayList<>(reserveConfigSyncDTO.getSyncStoreGuids());
        dataList.add(reserveConfigSyncDTO.getStoreGuid());
        singleDataDTO.setDatas(dataList);
        List<AreaDTO> areaList = tableClientService.batchQueryArea(singleDataDTO);
        reserveConfigService.sync(reserveConfigSyncDTO, reserveConfigDO, areaList);
        // cache
        reserveConfigSupport.removeBatch(reserveConfigSyncDTO.getSyncStoreGuids());
    }

}