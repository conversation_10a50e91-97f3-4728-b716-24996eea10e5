package com.holderzone.saas.store.reserve.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.business.manage.HandoverPayQueryDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.request.daily.DailyReqDTO;
import com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO;
import com.holderzone.saas.store.dto.order.response.daily.GatherRespDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.reserve.*;
import com.holderzone.saas.store.dto.table.TableInfoDTO;
import com.holderzone.saas.store.dto.table.TableOrderCombineDTO;
import com.holderzone.saas.store.dto.table.trade.TradeTableDTO;
import com.holderzone.saas.store.dto.trade.OrderDTO;
import com.holderzone.saas.store.enums.BaseDeviceTypeEnum;
import com.holderzone.saas.store.enums.common.BooleanEnum;
import com.holderzone.saas.store.reserve.api.dto.*;
import com.holderzone.saas.store.reserve.api.enums.AppletStateEnum;
import com.holderzone.saas.store.reserve.api.enums.ClientStateEnum;
import com.holderzone.saas.store.reserve.api.enums.OrderTypeEnum;
import com.holderzone.saas.store.reserve.core.common.BaseDTOThreadLocal;
import com.holderzone.saas.store.reserve.core.common.PageAdapter;
import com.holderzone.saas.store.reserve.core.dal.ReserveRecordDo;
import com.holderzone.saas.store.reserve.core.dal.ReserveRecordTableRelationDo;
import com.holderzone.saas.store.reserve.core.dal.mapper.ReserveItemDOMapper;
import com.holderzone.saas.store.reserve.core.dal.mapper.ReservePayRecordDoMapper;
import com.holderzone.saas.store.reserve.core.dal.mapper.ReserveRecordDoMapper;
import com.holderzone.saas.store.reserve.core.dal.mapper.ReserveRecordTableRelationDoMapper;
import com.holderzone.saas.store.reserve.core.domain.ReserveRecord;
import com.holderzone.saas.store.reserve.core.domain.ReserveRecordStateEnum;
import com.holderzone.saas.store.reserve.core.domain.Table;
import com.holderzone.saas.store.reserve.core.domain.standar.DelegateReserveRecord;
import com.holderzone.saas.store.reserve.core.event.impl.event.CompensateEvent;
import com.holderzone.saas.store.reserve.core.event.impl.event.EffectiveEvent;
import com.holderzone.saas.store.reserve.core.mapstruct.ReserveRecordMapstruct;
import com.holderzone.saas.store.reserve.core.mapstruct.TableMapstruct;
import com.holderzone.saas.store.reserve.core.service.PrintService;
import com.holderzone.saas.store.reserve.core.service.ReserveRecordService;
import com.holderzone.saas.store.reserve.intergration.table.OrganizationClientService;
import com.holderzone.saas.store.reserve.intergration.table.TradeClientService;
import com.holderzone.saas.store.util.BigDecimalUtil;
import com.holderzone.sdk.util.BatchIdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.holderzone.saas.store.reserve.core.mapstruct.ReserveRecordMapstruct.DelegateInfrastructure.toDelegate;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2019/04/26 19:03
 */
@Service
@Slf4j
public class ReserveRecordServiceImpl implements ReserveRecordService {
    @Autowired
    ReserveRecordDoMapper reserveRecordDoMapper;
    @Autowired
    ReserveRecordTableRelationDoMapper tableRelationDoMapper;
    @Autowired
    ReserveItemDOMapper itemDOMapper;

    @Autowired
    ReservePayRecordDoMapper reservePayRecordDoMapper;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private PrintService printService;

    @Autowired
    private OrganizationClientService organizationClientService;

    @Autowired
    private TradeClientService tradeClientService;

    private static final int SUB_ORDER_CODE = 2;

    @Override
    public ReserveRecord translate(ReserveRecordDTO dto) {
        return toDelegate(ReserveRecordMapstruct.MAPSTRUCT.dtotoDomain(dto));
    }

    @Override
    public Collection<Table> getTables(ReserveRecord reserveRecord) {
        return TableMapstruct.TABLE_MAPSTRUCT.toDomains(
                tableRelationDoMapper.selectList(
                        new LambdaQueryWrapper<ReserveRecordTableRelationDo>()
                                .eq(ReserveRecordTableRelationDo::getReserveRecordGuid, reserveRecord.getGuid())
                )
        );
    }

    @Override
    public Collection<Table> getTables(List<ReserveRecord> reserveRecords) {
        List<String> reserveRecordGuids = reserveRecords.stream().map(ReserveRecord::getGuid).distinct().collect(Collectors.toList());
        return TableMapstruct.TABLE_MAPSTRUCT.toDomains(
                tableRelationDoMapper.selectList(
                        new LambdaQueryWrapper<ReserveRecordTableRelationDo>()
                                .in(ReserveRecordTableRelationDo::getReserveRecordGuid, reserveRecordGuids)
                )
        );
    }

    @Override
    public ReserveRecordDetailDTO obtainDetail(ReserveRecordGuidDTO guidDTO) {
        ReserveRecord reserveRecord = obtain(guidDTO);
        return ReserveRecordMapstruct.MAPSTRUCT.toDetailDTO(reserveRecord);
    }

    @Override
    public ReserveRecordDetailDTO obtainStaticsDetail(ReserveRecordGuidDTO guidDTO) {
        ReserveRecordDetailDTO detailDTO = obtainDetail(guidDTO);
        detailDTO.setStatistics(statistics(new PhoneDTO(detailDTO.getPhone())));
        return detailDTO;
    }

    @Override
    public Collection<ReserveRecordLessDTO> query(ReserveRecordQueryDTO queryDTO) {
        LambdaQueryWrapper<ReserveRecordDo> queryWrapper = new LambdaQueryWrapper<ReserveRecordDo>()
                .eq(ReserveRecordDo::getStoreGuid, UserContextUtils.getStoreGuid())
                .between(queryDTO.getStartTime() != null, ReserveRecordDo::getReserveStartTime, queryDTO.getStartTime(), queryDTO.fetchEnd())
                .nested(
                        StringUtils.hasText(queryDTO.getKeyword()),
                        (e) -> e.or()
                                .like(ReserveRecordDo::getPhone, queryDTO.getKeyword())
                                .or()
                                .like(ReserveRecordDo::getName, queryDTO.getKeyword())
                );
        if (queryDTO.getStateEnum() != null) {

            ClientStateEnum reserveRecordStateEnum = queryDTO.getStateEnum();
            switch (reserveRecordStateEnum) {
                case COMMIT:
                case PASS:
                    queryWrapper.eq(ReserveRecordDo::getState, reserveRecordStateEnum.getCode());
                    break;
                case CANCLE:
                    break;
                case PICK_TABLE:
                    queryWrapper.apply("state & " + reserveRecordStateEnum.getCode() + "=0");
                    break;
                case DELAY:
                    queryWrapper.apply("state & " + reserveRecordStateEnum.getCode() + "=0");
                    queryWrapper.eq(ReserveRecordDo::getIsDelay, 1);
                    break;
                default:
                    break;
            }
        } else {
            queryWrapper.notIn(ReserveRecordDo::getState, Arrays.asList(ReserveRecordStateEnum.NO_PAY.getCode(),
                    ReserveRecordStateEnum.NO_PAY_CANCEL.getCode()));
        }
        queryWrapper.eq(ReserveRecordDo::getOrderType, OrderTypeEnum.RESERVE);
        queryWrapper.orderByDesc(ReserveRecordDo::getState, ReserveRecordDo::getReserveStartTime);
        List<ReserveRecordDo> reserveRecordDoList = reserveRecordDoMapper.selectList(
                queryWrapper
        );
        return ReserveRecordMapstruct.MAPSTRUCT.toLessDTOs(
                ReserveRecordMapstruct.MAPSTRUCT.toDomainss(
                        ReserveRecordMapstruct.MAPSTRUCT.toDomains(
                                reserveRecordDoList
                        )
                )
        );
    }

    @Override
    public Page<ReserveRecordAppletPageDTO> obtainRecordList(ReserveAppletQueryDTO queryDTO) {
        if (!StringUtils.isEmpty(queryDTO.getAppletState())) {
            queryDTO.setIsDelay(false);
        }
        if (AppletStateEnum.INVALID.name().equals(queryDTO.getAppletState())) {
            queryDTO.setIsDelay(true);
        }
        if (AppletStateEnum.ING.name().equals(queryDTO.getAppletState())) {
            queryDTO.setIsDelay(false);
        }
        queryDTO.setStates(ReserveRecordStateEnum.transferReserveRecordStateEnums(queryDTO.getAppletState()));
        PageAdapter<ReserveRecordDo> pageAdapter = new PageAdapter<>(queryDTO);
        IPage<ReserveRecordDo> page = reserveRecordDoMapper.obtainRecordList(pageAdapter, queryDTO);
        List<ReserveRecordDo> records = page.getRecords();
        List<DelegateReserveRecord> reserveRecords = new ArrayList<>(ReserveRecordMapstruct.MAPSTRUCT.toDomains(records));
        List<ReserveRecordAppletPageDTO> reserveRecordPages = reserveRecords.stream().map(e -> {
            ReserveRecordAppletPageDTO appletPageDTO = ReserveRecordMapstruct.MAPSTRUCT.toAppletPageDTO(e);
            // 安排桌台
            if (CollectionUtils.isNotEmpty(e.getTables())) {
                List<String> tableNames = e.getTables().stream()
                        .map(table -> table.getAreaName() + "-" + table.getName())
                        .collect(Collectors.toList());
                appletPageDTO.setAreaName(String.join(",", tableNames));
            }
            return appletPageDTO;
        }).collect(Collectors.toList());
        return new PageAdapter<>(page, reserveRecordPages);
    }

    @Override
    public StatisticsDTO statistics(PhoneDTO queryDTO) {
        Collection<GuidStateMappingDTO> guidStateMappingDTOS = reserveRecordDoMapper.statistics(UserContextUtils.getStoreGuid(), queryDTO.getPhone());
        Map<Integer, Long> ref = guidStateMappingDTOS.stream().collect(Collectors.groupingBy(GuidStateMappingDTO::getState, Collectors.counting()));
        Long total = ref.values().stream().reduce(0L, Long::sum);
        // 预定次数 - 未支付次数
        total = total - Optional.ofNullable(ref.get(ReserveRecordStateEnum.NO_PAY.getCode())).orElse(0L);
        // 预定次数 - 未支付取消次数
        total = total - Optional.ofNullable(ref.get(ReserveRecordStateEnum.NO_PAY_CANCEL.getCode())).orElse(0L);
        Long delay = guidStateMappingDTOS.stream().filter(GuidStateMappingDTO::getIsDelay).count();
        return new StatisticsDTO(
                Optional.of(total).orElse(0L),
                Optional.of(delay).orElse(0L),
                Optional.ofNullable(ref.get(ReserveRecordStateEnum.CANCLE.getCode())).orElse(0L)
                        + Optional.ofNullable(ref.get(ReserveRecordStateEnum.SYSTEM_CANCLE.getCode())).orElse(0L)
        );
    }

    @Override
    public List<DineInItemDTO> getItems(ReserveRecordGuidDTO guidDTO) {
        Integer tableCount = tableRelationDoMapper.selectCount(new QueryWrapper<ReserveRecordTableRelationDo>().lambda()
                .eq(ReserveRecordTableRelationDo::getReserveRecordGuid, guidDTO.getGuid()));
        if (tableCount <= 0) {
            throw new BusinessException("预订GUID有误");
        }
        List<DineInItemDTO> dineInItemDTOS = Optional.of(reserveRecordDoMapper.selectOne(new QueryWrapper<ReserveRecordDo>().lambda()
                        .eq(ReserveRecordDo::getGuid, guidDTO.getGuid())
                        .select(ReserveRecordDo::getItemsStr)))
                .map(reserveRecord -> JacksonUtils.toObjectList(DineInItemDTO.class, reserveRecord.getItemsStr()))
                .orElse(Collections.emptyList());
        dineInItemDTOS.forEach(dineInItemDTO -> dineInItemDTO.setCurrentCount(dineInItemDTO.getCurrentCount().multiply(BigDecimal.valueOf(tableCount))));
        return dineInItemDTOS;
    }

    @Override
    public ReserveReportTotalDataDTO itemCount(ReserveReportParamDTO paramDTO) {
        //格式化称重数量
        DecimalFormat df = new DecimalFormat("#.###");
        //查询分类明细
        List<ReserveItemDTO> allItemDTOList = itemDOMapper.itemCount(paramDTO);
        if (CollectionUtils.isEmpty(allItemDTOList)) {
            return null;
        }
        //将分类名筛选出来
        List<String> typeNameList = allItemDTOList.stream().map(ReserveItemDTO::getItemTypeName)
                .distinct().collect(Collectors.toList());
        List<ReserveReportDataDTO> dataDTOList = new ArrayList<>();
        for (String typeName : typeNameList) {
            ReserveReportDataDTO dataDTO = new ReserveReportDataDTO();
            dataDTO.setItemTypeName(typeName);
            List<ReserveItemDTO> itemDTOList = allItemDTOList.stream().filter(
                    v -> v.getItemTypeName().equals(typeName)).collect(Collectors.toList());
            dataDTO.setReserveItemDTOList(itemDTOList);
            dataDTO.setItemTypeNum(Double.parseDouble(df.format(itemDTOList.stream().mapToDouble(ReserveItemDTO::getNum).sum())));
            dataDTO.setItemTypePrice(itemDTOList.stream().map(ReserveItemDTO::getItemPrice)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
            dataDTOList.add(dataDTO);
        }
        ReserveReportTotalDataDTO totalDataDTO = new ReserveReportTotalDataDTO();
        totalDataDTO.setReportDataList(dataDTOList);
        totalDataDTO.setTotalNum(Double.parseDouble(df.format(dataDTOList.stream().mapToDouble(ReserveReportDataDTO::getItemTypeNum).sum())));
        totalDataDTO.setTotalItemPrice(dataDTOList.stream().map(ReserveReportDataDTO::getItemTypePrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add));
        log.info("预订商品统计返回结果：{}", JacksonUtils.writeValueAsString(totalDataDTO));
        return totalDataDTO;
    }

    @Override
    public ReserveHandoverDTO handover(HandoverPayQueryDTO reservePayReqDTO) {
        if (reservePayReqDTO.getGmtModified() == null) {
            reservePayReqDTO.setGmtModified(LocalDateTime.now());
        }
        List<ReserveAmountPaymentDTO> paymentDTOList = reserveRecordDoMapper.reserveAmountQuery(reservePayReqDTO);
        ReserveHandoverDTO reserveHandoverDTO = new ReserveHandoverDTO();
        //每种支付方式的订单数累加为总预订数（包含未交预订金）
        reserveHandoverDTO.setReserveCount(paymentDTOList.stream().mapToInt(ReserveAmountPaymentDTO::getReserveCount).sum());
        //去除未交预订金的
        paymentDTOList = paymentDTOList.stream().filter(
                v -> v.getReserveAmount().compareTo(BigDecimal.ZERO) != 0).collect(Collectors.toList());
        //预订金总额
        reserveHandoverDTO.setReserveAmount(paymentDTOList.stream().map(
                ReserveAmountPaymentDTO::getReserveAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        //预订金中现金支付金额（现金支付paymentType=1)
        reserveHandoverDTO.setReserveCashAmount(paymentDTOList.stream().filter(v -> v.getPaymentType() == 1)
                .map(ReserveAmountPaymentDTO::getReserveAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        //支付方式名称为key，金额为value
        Map<String, BigDecimal> payDetailMap = new HashMap<>();
        paymentDTOList.forEach(v -> payDetailMap.put(v.getPaymentTypeName(), v.getReserveAmount()));
        reserveHandoverDTO.setReservePayDetailMap(payDetailMap);
        List<AmountItemDTO> payDetailList = new ArrayList<>();
        paymentDTOList.forEach(v -> {
            AmountItemDTO amountItemDTO = new AmountItemDTO();
            amountItemDTO.setCode(v.getPaymentType());
            amountItemDTO.setName(v.getPaymentTypeName());
            amountItemDTO.setAmount(v.getReserveAmount());
            payDetailList.add(amountItemDTO);
        });
        reserveHandoverDTO.setReservePayDetailList(payDetailList);
        return reserveHandoverDTO;
    }

    @Override
    public List<GatherRespDTO> gather(DailyReqDTO dailyReqDTO) {
        List<GatherRespDTO> reserveGather = reserveRecordDoMapper.gather(dailyReqDTO);
        reserveGather.forEach(v -> v.setTotalAmount(v.getReserveAmount()));
        log.info("营业报表 收款方式统计 预订统计结果 = {}", JacksonUtils.writeValueAsString(reserveGather));
        return reserveGather;
    }

    @Override
    public ReserveRecordDetailDTO querySameTimeRecord(ReserveAppletQueryDTO queryDTO) {
        ReserveRecordDo recordDo = reserveRecordDoMapper.querySameTimeRecord(queryDTO);
        return ReserveRecordMapstruct.MAPSTRUCT.toDetailDTO(ReserveRecordMapstruct.MAPSTRUCT.toDomain(recordDo));
    }

    @Override
    public ReserveRecordAppletPageDTO queryLastTimeRecord(ReserveAppletQueryDTO queryDTO) {
        ReserveRecordDo recordDo = reserveRecordDoMapper.queryLastTimeRecord(queryDTO);
        return ReserveRecordMapstruct.MAPSTRUCT.toAppletPageDTO(ReserveRecordMapstruct.MAPSTRUCT.toDomain(recordDo));
    }

    @Override
    public ReserveCommitStaticsDTO commitStatistics() {
        Long total = reserveRecordDoMapper.commitStatistics(UserContextUtils.getStoreGuid());
        ReserveCommitStaticsDTO commitStaticsDTO = new ReserveCommitStaticsDTO();
        commitStaticsDTO.setTotal(total);
        return commitStaticsDTO;
    }

    @Override
    public ReserveCommitStaticsDTO commitStatisticsByDay() {
        List<ReserveCommitStaticsDTO.InnerDay> staticsDays = reserveRecordDoMapper.commitStatisticsByDay(UserContextUtils.getStoreGuid());
        ReserveCommitStaticsDTO commitStaticsDTO = new ReserveCommitStaticsDTO();
        commitStaticsDTO.setDays(staticsDays);
        return commitStaticsDTO;
    }

    @Override
    public ReserveRecordDTO queryByOrderGuid(String orderGuid) {
        ReserveRecordDo reserveRecordDo = reserveRecordDoMapper.selectOne(new LambdaQueryWrapper<ReserveRecordDo>()
                .eq(ReserveRecordDo::getMainOrderGuid, orderGuid)
                .orderByDesc(ReserveRecordDo::getGmtCreate)
                .last("limit 1")
        );
        if (Objects.isNull(reserveRecordDo)) {
            return null;
        }

        return getReserveRecordDTO(reserveRecordDo);
    }

    @Override
    public void separate(TableOrderCombineDTO tableOrderCombineDTO) {
        String mainOrderGuid = tableOrderCombineDTO.getMainOrderGuid();
        ReserveRecordDo reserveRecordDo = reserveRecordDoMapper.selectOne(new LambdaQueryWrapper<ReserveRecordDo>()
                .eq(ReserveRecordDo::getMainOrderGuid, mainOrderGuid)
                .in(ReserveRecordDo::getState, ReserveRecordStateEnum.getOpenedList())
                .orderByDesc(ReserveRecordDo::getGmtCreate)
                .last("limit 1")
        );
        if (Objects.isNull(reserveRecordDo)) {
            log.warn("订单没有预定信息[separate]，mainOrderGuid={}", JacksonUtils.writeValueAsString(mainOrderGuid));
            return;
        }
        String mainTableGuid = tableOrderCombineDTO.getMainTableGuid();
        LambdaQueryWrapper<ReserveRecordTableRelationDo> queryWrapper = new LambdaQueryWrapper<ReserveRecordTableRelationDo>()
                .eq(ReserveRecordTableRelationDo::getReserveRecordGuid, reserveRecordDo.getGuid());

        List<TableInfoDTO> tableInfoDTOS = tableOrderCombineDTO.getTableInfoDTOS();
        if (org.springframework.util.CollectionUtils.isEmpty(tableInfoDTOS)) {
            // 为空时拆主单
            queryWrapper.ne(ReserveRecordTableRelationDo::getTableGuid, mainTableGuid);
        } else {
            // 不为空拆自己
            List<String> tableGuidList = tableInfoDTOS.stream()
                    .map(TableInfoDTO::getTableGuid)
                    .distinct()
                    .collect(Collectors.toList());
            queryWrapper.in(ReserveRecordTableRelationDo::getTableGuid, tableGuidList);
        }
        tableRelationDoMapper.delete(queryWrapper);
    }

    @Override
    public ReserveRecordDTO queryByGuid(String guid) {
        ReserveRecordDo reserveRecordDo = reserveRecordDoMapper.queryByGuid(guid);
        if (Objects.isNull(reserveRecordDo)) {
            return null;
        }

        return getReserveRecordDTO(reserveRecordDo);
    }

    @Override
    public void notifyTurn(TradeTableDTO tradeTableDTO) {
        ReserveRecordDo reserveRecordDo = reserveRecordDoMapper.selectOne(new LambdaQueryWrapper<ReserveRecordDo>()
                .eq(ReserveRecordDo::getMainOrderGuid, tradeTableDTO.getOrderGuid())
                .in(ReserveRecordDo::getState, ReserveRecordStateEnum.getOpenedList())
                .orderByDesc(ReserveRecordDo::getGmtCreate)
                .last("limit 1")
        );
        if (Objects.isNull(reserveRecordDo)) {
            log.warn("订单没有预定信息[notifyTurn]，mainOrderGuid={}", JacksonUtils.writeValueAsString(tradeTableDTO.getOrderGuid()));
            return;
        }
        ReserveRecordTableRelationDo tableRelationDo = tableRelationDoMapper.selectOne(
                new LambdaQueryWrapper<ReserveRecordTableRelationDo>()
                        .eq(ReserveRecordTableRelationDo::getReserveRecordGuid, reserveRecordDo.getGuid())
                        .orderByDesc(ReserveRecordTableRelationDo::getGmtCreate)
                        .last("limit 1")
        );
        if (Objects.nonNull(tableRelationDo)) {
            tableRelationDo.setTableGuid(tradeTableDTO.getTableGuid());
            tableRelationDo.setTableName(tradeTableDTO.getTableName());
            tableRelationDo.setAreaGuid(tradeTableDTO.getAreaGuid());
            tableRelationDo.setAreaName(tradeTableDTO.getAreaName());
            tableRelationDoMapper.updateById(tableRelationDo);
        }
    }

    @Override
    public void notifyPay(NotifyPayReqDTO reqDTO) {
        ReserveRecordDo recordDo = new ReserveRecordDo();
        recordDo.setPhone(reqDTO.getMemberPhone());
        recordDo.setState(ReserveRecordStateEnum.FINISH.getCode());
        reserveRecordDoMapper.update(recordDo, new LambdaQueryWrapper<ReserveRecordDo>()
                .eq(ReserveRecordDo::getGuid, reqDTO.getReserveGuid())
        );
    }

    @Override
    public void combine(TableOrderCombineDTO tableOrderCombineDTO) {
        String mainOrderGuid = tableOrderCombineDTO.getMainOrderGuid();
        ReserveRecordDo reserveRecordDo = reserveRecordDoMapper.selectOne(new LambdaQueryWrapper<ReserveRecordDo>()
                .eq(ReserveRecordDo::getMainOrderGuid, mainOrderGuid)
                .in(ReserveRecordDo::getState, ReserveRecordStateEnum.getOpenedList())
                .orderByDesc(ReserveRecordDo::getGmtCreate)
                .last("limit 1")
        );
        if (Objects.isNull(reserveRecordDo)) {
            log.warn("订单没有预定信息[combine]，mainOrderGuid={}", JacksonUtils.writeValueAsString(mainOrderGuid));
            return;
        }
        List<TableInfoDTO> tableInfoDTOS = tableOrderCombineDTO.getTableInfoDTOS();
        LambdaQueryWrapper<ReserveRecordTableRelationDo> queryWrapper = new LambdaQueryWrapper<ReserveRecordTableRelationDo>()
                .eq(ReserveRecordTableRelationDo::getReserveRecordGuid, reserveRecordDo.getGuid());
        List<ReserveRecordTableRelationDo> tableRelationDoList = tableRelationDoMapper.selectList(queryWrapper);
        if (CollectionUtils.isNotEmpty(tableRelationDoList)) {
            List<String> reservedTableGuidList = tableRelationDoList.stream()
                    .map(ReserveRecordTableRelationDo::getTableGuid)
                    .distinct()
                    .collect(Collectors.toList());
            tableInfoDTOS.removeIf(t -> reservedTableGuidList.contains(t.getTableGuid()));
        }
        if (CollectionUtils.isNotEmpty(tableInfoDTOS)) {
            List<ReserveRecordTableRelationDo> relationDoList = new ArrayList<>();
            List<Long> guids = BatchIdGenerator.batchGetGuids(redisTemplate, ReserveRecordTableRelationDo.class.getSimpleName(), tableInfoDTOS.size());
            for (int i = 0; i < tableInfoDTOS.size(); i++) {
                TableInfoDTO tableInfoDTO = tableInfoDTOS.get(i);
                ReserveRecordTableRelationDo relationDo = new ReserveRecordTableRelationDo();
                relationDo.setGuid(String.valueOf(guids.get(i)));
                relationDo.setTableGuid(tableInfoDTO.getTableGuid());
                relationDo.setTableName(tableInfoDTO.getTableName());
                relationDo.setAreaGuid(tableInfoDTO.getAreaGuid());
                relationDo.setAreaName(tableInfoDTO.getAreaName());
                relationDo.setReserveRecordGuid(reserveRecordDo.getGuid());
                relationDo.setState(BooleanEnum.FALSE.getCode());
                relationDo.setIsDeleted(Boolean.FALSE);
                relationDo.setGmtCreate(LocalDateTime.now());

                relationDoList.add(relationDo);
            }
            tableRelationDoMapper.saveBatch(relationDoList);
        }
    }

    @Override
    public void recovery(ReserveRecoveryDTO recoveryDTO) {
        // 新增预定表
        String orderGuid = recoveryDTO.getOrderGuid();
        ReserveRecordDo reserveRecordDo = reserveRecordDoMapper.selectOne(new LambdaQueryWrapper<ReserveRecordDo>()
                .eq(ReserveRecordDo::getMainOrderGuid, orderGuid)
                .in(ReserveRecordDo::getState, ReserveRecordStateEnum.FINISH.getCode())
                .orderByDesc(ReserveRecordDo::getGmtCreate)
                .last("limit 1")
        );
        if (Objects.isNull(reserveRecordDo)) {
            log.warn("订单没有预定信息[recovery]，orderGuid={}", JacksonUtils.writeValueAsString(orderGuid));
            return;
        }
        reserveRecordDo.setMainOrderGuid(recoveryDTO.getNewOrderGuid());
        reserveRecordDo.setState(ReserveRecordStateEnum.OPEN_TABLE.getCode());
        reserveRecordDoMapper.updateById(reserveRecordDo);
    }

    @Override
    public ReserveCommitStaticsDTO commitStatisticsByScope(CommitStatisticsReqDTO commitStatisticsByScope) {
        log.info("[查询时间范围内待处理订单]query={}", JacksonUtils.writeValueAsString(commitStatisticsByScope));
        Long total = reserveRecordDoMapper.commitStatisticsTotalByScope(commitStatisticsByScope);
        ReserveCommitStaticsDTO commitStaticsDTO = new ReserveCommitStaticsDTO();
        commitStaticsDTO.setTotal(total);
        List<ReserveCommitStaticsDTO.InnerDay> staticsDays = reserveRecordDoMapper.commitStatisticsByScope(commitStatisticsByScope);
        commitStaticsDTO.setDays(staticsDays);
        return commitStaticsDTO;
    }

    @Override
    public BigDecimal queryReserveAmount(SingleDataDTO query) {
        return reserveRecordDoMapper.queryReserveAmount(query.getData());
    }

    @Override
    public List<TableDTO> queryReserveTable(SingleDataDTO query) {
        return reserveRecordDoMapper.queryReserveTable(query.getData());
    }

    @Override
    public void updateShareUrl(String guid, String shareUrl) {
        ReserveRecordDo recordDo = new ReserveRecordDo();
        recordDo.setGuid(guid);
        recordDo.setShareUrl(shareUrl);
        reserveRecordDoMapper.update(recordDo, new LambdaQueryWrapper<ReserveRecordDo>()
                .eq(ReserveRecordDo::getGuid, guid));
    }

    @NotNull
    private ReserveRecordDTO getReserveRecordDTO(ReserveRecordDo reserveRecordDo) {
        ReserveRecordDTO recordDTO = new ReserveRecordDTO();
        recordDTO.setGuid(reserveRecordDo.getGuid());
        recordDTO.setStoreGuid(reserveRecordDo.getStoreGuid());
        recordDTO.setNumber(reserveRecordDo.getNumber());
        recordDTO.setGender(reserveRecordDo.getGender());
        recordDTO.setName(reserveRecordDo.getName());
        recordDTO.setPhone(reserveRecordDo.getPhone());
        recordDTO.setPaymentType(reserveRecordDo.getPaymentType());
        recordDTO.setPaymentTypeName(reserveRecordDo.getPaymentTypeName());
        recordDTO.setReserveAmount(reserveRecordDo.getReserveAmount());
        if (BigDecimalUtil.greaterThanZero(reserveRecordDo.getReserveAmount())) {
            recordDTO.setReserveAmount(reserveRecordDo.getReserveAmount().subtract(reserveRecordDo.getRefundAmount()));
        }
        recordDTO.setRemark(reserveRecordDo.getRemark());
        recordDTO.setOrderType(reserveRecordDo.getOrderType());
        recordDTO.setReserveRefundAmount(reserveRecordDo.getRefundAmount());
        recordDTO.setConfirmUserGuid(reserveRecordDo.getConfirmUserGuid());
        recordDTO.setConfirmUserName(reserveRecordDo.getConfirmUserName());
        recordDTO.setDeviceId(reserveRecordDo.getDeviceId());
        recordDTO.setDeviceType(reserveRecordDo.getDeviceType());
        recordDTO.setPaymentTime(reserveRecordDo.getPaymentTime());
        return recordDTO;
    }

    @Override
    public ReserveRecordDetailDTO launch(ReserveRecordDTO dto, ReserveRecordStateEnum stateEnum) {
        dto.setState(stateEnum.getCode() + "");
        ReserveRecord reserveRecord = translate(dto);
        reserveRecord.lauch();
        dto.setGuid(reserveRecord.getGuid());
        return ReserveRecordMapstruct.MAPSTRUCT.toDetailDTO(reserveRecord);
    }

    @Override
    public ReserveRecordDetailDTO modify(ReserveRecordDTO dto) {
        ReserveRecord reserveRecord = translate(dto);
        if (dto.getTables() == null || dto.getTables().isEmpty()) {
            reserveRecord.setTables(Collections.emptyList());
        }
        reserveRecord.modify();
        ReserveRecordDetailDTO detailDTO = ReserveRecordMapstruct.MAPSTRUCT.toDetailDTO(reserveRecord);
        if (Objects.nonNull(dto.getDeviceId())) {
            detailDTO.setDeviceId(dto.getDeviceId());
        }
        if (Objects.nonNull(dto.getDeviceType())) {
            detailDTO.setDeviceType(dto.getDeviceType());
        }
        printService.printReservePay(detailDTO);
        return detailDTO;
    }

    @Override
    public ReserveRecordDetailDTO memberPay(ReserveAppletPayDTO payDTO) {
        ReserveRecordGuidDTO reserveRecordGuidDTO = new ReserveRecordGuidDTO();
        reserveRecordGuidDTO.setGuid(payDTO.getRecordGuid());
        ReserveRecord reserveRecord = obtain(reserveRecordGuidDTO);
        reserveRecord.setMemberInfoGuid(payDTO.getMemberInfoGuid());
        reserveRecord.setMemberInfoCardGuid(payDTO.getOperationMemberInfoCardGuid());
        reserveRecord.setPayType(payDTO.getPayType());
        reserveRecord.setMemberPassword(payDTO.getMemberPassword());
        reserveRecord.setMemberName(payDTO.getMemberName());
        reserveRecord.memberPay();
        return ReserveRecordMapstruct.MAPSTRUCT.toDetailDTO(reserveRecord);
    }

    @Override
    public void aggPay(ReserveRecordGuidDTO guidDTO) {
        ReserveRecord reserveRecord = obtain(guidDTO);
        reserveRecord.setPayGuid(guidDTO.getPayGuid());
        reserveRecord.aggPay();
    }

    @Override
    public ReserveRecordDetailDTO pass(ReserveRecordGuidDTO guidDTO) {
        ReserveRecord reserveRecord = obtain(guidDTO);
        if (Objects.nonNull(guidDTO.getReserveAmount())) {
            reserveRecord.setReserveAmount(guidDTO.getReserveAmount());
        }
        if (CollectionUtils.isNotEmpty(guidDTO.getTables())) {
            reserveRecord.setTables(TableMapstruct.TABLE_MAPSTRUCT.tableToDomain(guidDTO.getTables()));
        }
        if (CollectionUtils.isNotEmpty(guidDTO.getItems())) {
            reserveRecord.setItems(ReserveRecordMapstruct.MAPSTRUCT.dineInItemDTOToItem(guidDTO.getItems()));
        }
        if (Objects.nonNull(guidDTO.getDeviceId())) {
            reserveRecord.setDeviceId(guidDTO.getDeviceId());
        }
        reserveRecord.setOperateDeviceType(BaseDeviceTypeEnum.All_IN_ONE.getCode());
        if (Objects.nonNull(guidDTO.getOrderType())) {
            reserveRecord.setOrderType(guidDTO.getOrderType());
        }
        if (Objects.nonNull(guidDTO.getOrderGuid())) {
            reserveRecord.setOrderGuid(guidDTO.getOrderGuid());
        }
        if (Objects.nonNull(guidDTO.getStoreGuid())) {
            reserveRecord.setStoreGuid(guidDTO.getStoreGuid());
        }
        if (!StringUtils.hasText(guidDTO.getOrderGuid())) {
            ReserveRecordDo recordDo = reserveRecordDoMapper.selectOne(
                    new LambdaQueryWrapper<ReserveRecordDo>().eq(ReserveRecordDo::getGuid, guidDTO.getGuid())
            );
            log.info("recordDo={}", JacksonUtils.writeValueAsString(recordDo));
            if (Objects.nonNull(recordDo)) {
                reserveRecord.setOrderGuid(recordDo.getMainOrderGuid());
                reserveRecord.setStoreGuid(recordDo.getStoreGuid());
                reserveRecord.setOrderType(recordDo.getOrderType());
            }
        }
        reserveRecord.accept();
        ReserveRecordDetailDTO detailDTO = ReserveRecordMapstruct.MAPSTRUCT.toDetailDTO(reserveRecord);
        if (Objects.nonNull(guidDTO.getDeviceId())) {
            detailDTO.setDeviceId(guidDTO.getDeviceId());
        }
        if (Objects.nonNull(guidDTO.getDeviceType())) {
            detailDTO.setDeviceType(guidDTO.getDeviceType());
        }
        printService.printReservePay(detailDTO);
        return detailDTO;
    }

    @Override
    public ReserveRecordDetailDTO cancle(ReserveRecordGuidDTO guidDTO) {
        ReserveRecord reserveRecord = obtain(guidDTO);
        reserveRecord.setOperateDeviceType(guidDTO.getDeviceType());
        reserveRecord.setCancleReason(guidDTO.getReason());
        reserveRecord.setOrderType(guidDTO.getOrderType());
        reserveRecord.setOrderGuid(guidDTO.getOrderGuid());
        if (Objects.isNull(guidDTO.getReserveRefundAmount())) {
            reserveRecord.setReserveRefundAmount(reserveRecord.getReserveAmount());
        } else {
            reserveRecord.setReserveRefundAmount(guidDTO.getReserveRefundAmount());
        }
        reserveRecord.cancle();
        return ReserveRecordMapstruct.MAPSTRUCT.toDetailDTO(reserveRecord);
    }

    @Override
    public BigDecimal partRefund(ReserveRecordDTO dto) {
        ReserveRecordGuidDTO reserveRecordGuidDTO = new ReserveRecordGuidDTO();
        reserveRecordGuidDTO.setGuid(dto.getGuid());
        ReserveRecord reserveRecord = obtain(reserveRecordGuidDTO);
        reserveRecord.setReserveRefundAmount(dto.getReserveAmount());
        reserveRecord.partRefund();
        return dto.getReserveAmount();
    }

    @Override
    public ReserveRecordDetailDTO open(ReserveRecordGuidDTO guidDTO) {
        ReserveRecord reserveRecord = obtain(guidDTO);
        BaseDTOThreadLocal.set(guidDTO);
        EffectiveEvent effect = reserveRecord.effect();
        ReserveRecordDetailDTO reserveRecordDetailDTO = ReserveRecordMapstruct.MAPSTRUCT.toDetailDTO(reserveRecord);
        reserveRecordDetailDTO.setOrderGuid(effect.getOrderGuid());
        reserveRecordDetailDTO.setTableGuid(effect.getTableGuid());
        return reserveRecordDetailDTO;
    }

    @Override
    public ReserveRecordDetailDTO compensate(CompensateDTO compensateDTO) {
        ReserveRecord reserveRecord = obtain(compensateDTO);
        BaseDTOThreadLocal.set(compensateDTO);
        reserveRecord.setTables(compensateDTO.getTableDTOS().stream().map(TableMapstruct.TABLE_MAPSTRUCT::dtotoDomain).collect(Collectors.toList()));
        CompensateEvent compensate = reserveRecord.compensate();
        ReserveRecordDetailDTO reserveRecordDetailDTO = ReserveRecordMapstruct.MAPSTRUCT.toDetailDTO(reserveRecord);
        reserveRecordDetailDTO.setOrderGuid(compensate.getOrderGuid());
        reserveRecordDetailDTO.setTableGuid(compensate.getTableGuid());
        return reserveRecordDetailDTO;
    }

    public ReserveRecord obtain(ReserveRecordGuidDTO dto) {
        ReserveRecordDo recordDo = reserveRecordDoMapper.selectOne(
                new LambdaQueryWrapper<ReserveRecordDo>().eq(ReserveRecordDo::getGuid, dto.getGuid())
        );
        return ReserveRecordMapstruct.MAPSTRUCT.toDomain(recordDo);
    }

    @Override
    public void printByOrderGuid(SingleDataDTO query) {
        if (query == null || StringUtils.isEmpty(query.getData())) {
            log.warn("请求参数为空，无法执行打印");
            return;
        }

        log.info("[根据guid打印预付金信息]query={}", JacksonUtils.writeValueAsString(query));

        // 获取订单 GUID, 如果是子单，取主单 GUID
        String orderGuid = query.getData();
        OrderDTO orderDTO = tradeClientService.findByOrderGuid(orderGuid);
        if (Objects.nonNull(orderDTO) && Objects.equals(orderDTO.getUpperState(), SUB_ORDER_CODE)) {
            orderGuid = String.valueOf(orderDTO.getMainOrderGuid());
        }

        ReserveRecordDetailDTO reserveRecordDetailDTO = obtainByOrderGuid(orderGuid);

        if (reserveRecordDetailDTO == null) {
            throw new BusinessException("未查询到预订记录");
        }

        // 查询并设置门店名称
        StoreDTO storeDTO = organizationClientService.queryStoreByGuid(reserveRecordDetailDTO.getStoreGuid());
        reserveRecordDetailDTO.setStoreName(Objects.nonNull(storeDTO) ? storeDTO.getName() : "");
        if (Objects.nonNull(query.getDeviceId())) {
            reserveRecordDetailDTO.setDeviceId(query.getDeviceId());
        }
        if (Objects.nonNull(query.getDeviceType())) {
            reserveRecordDetailDTO.setDeviceType(query.getDeviceType());
        }
        // 执行打印操作
        printService.printReservePay(reserveRecordDetailDTO);
    }

    /**
     * 根据订单 GUID 获取最新的预约记录详情 DTO
     *
     * @param orderGuid 订单 GUID
     * @return 最新的预约记录详情 DTO，如果没有找到则返回 null
     */
    @SuppressWarnings("unchecked")
    private ReserveRecordDetailDTO obtainByOrderGuid(String orderGuid) {
        try {
            // 查询数据库获取最新的预约记录
            ReserveRecordDo reserveRecordDo = reserveRecordDoMapper.selectOne(
                    new LambdaQueryWrapper<ReserveRecordDo>()
                            .eq(ReserveRecordDo::getMainOrderGuid, orderGuid)
                            .orderByDesc(ReserveRecordDo::getGmtCreate)
                            .last("LIMIT 1")
            );

            if (Objects.isNull(reserveRecordDo)) {
                log.warn("未找到与订单 GUID 相关的预约记录: {}", orderGuid);
                return null;
            }

            // 转换为领域对象
            ReserveRecord reserveRecord = ReserveRecordMapstruct.MAPSTRUCT.toDomain(reserveRecordDo);

            // 转换为 DTO 并返回
            return ReserveRecordMapstruct.MAPSTRUCT.toDetailDTO(reserveRecord);
        } catch (Exception e) {
            log.error("获取预约记录时发生错误，订单 GUID: {}", orderGuid, e);
            throw new BusinessException("获取预约记录失败，请稍后再试。");
        }
    }
}
