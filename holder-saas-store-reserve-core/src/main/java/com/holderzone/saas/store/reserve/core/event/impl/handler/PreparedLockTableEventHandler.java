package com.holderzone.saas.store.reserve.core.event.impl.handler;

import com.holderzone.framework.anno.CustomerRegister;
import com.holderzone.framework.event.observer.CustomerObserver;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.redisson.sdk.lock.RedissonLockUtil;
import com.holderzone.saas.store.dto.table.ReservePreparedDTO;
import com.holderzone.saas.store.reserve.core.dal.mapper.ReserveRecordTableRelationDoMapper;
import com.holderzone.saas.store.reserve.core.domain.ReserveRecord;
import com.holderzone.saas.store.reserve.core.event.BaseEventHandler;
import com.holderzone.saas.store.reserve.core.event.impl.event.PreparedLockTableEvent;
import com.holderzone.saas.store.reserve.intergration.table.TableClientService;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className LockTableEventHandler
 * @date 2019/04/26 16:59
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Component
@CustomerRegister(isRegister = true)
public class PreparedLockTableEventHandler extends BaseEventHandler<PreparedLockTableEvent> implements CustomerObserver<PreparedLockTableEvent> {
    @Autowired
    TableClientService tableServiceService;
    @Autowired
    protected ReserveRecordTableRelationDoMapper tableRelationDoMapper;
    @Override
    public void execute(PreparedLockTableEvent baseEvent) {
        ReserveRecord record = baseEvent.getReserveRecord();
        List<String> old = baseEvent.getOld();

        List<String> neo = baseEvent.getNeo();
        Pair<List<String>, List<String>> pair = removeSame(old,neo);
        List<String> del = pair.getRight();
        List<String> add = pair.getLeft();

        if(!del.isEmpty()){
            List<String> tables = tableRelationDoMapper.queryTableAfterAndIn(del, LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),record.getGuid());
            tables.forEach(del::remove);
        }
        if(add.isEmpty() && del.isEmpty()){
            return;
        }
        tableServiceService.prepare(new ReservePreparedDTO(add,del));
    }
    @Override
    protected void pre(PreparedLockTableEvent baseEvent) {
        ReserveRecord record = baseEvent.getReserveRecord();
        boolean result = RedissonLockUtil.tryLock(record.getGuid(),3,10);
        if(!result){
            throw new BusinessException("当前预定记录正被其他人操作,请稍候重试!!");
        }
        super.pre(baseEvent);
    }

    @Override
    protected void after(PreparedLockTableEvent baseEvent) {
        ReserveRecord record = baseEvent.getReserveRecord();
        RedissonLockUtil.unlock(record.getGuid());
        super.after(baseEvent);
    }
}