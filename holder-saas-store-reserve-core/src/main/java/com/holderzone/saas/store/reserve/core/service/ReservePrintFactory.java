package com.holderzone.saas.store.reserve.core.service;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.common.UserInfoDTO;
import com.holderzone.saas.store.dto.print.content.PrintDTO;
import com.holderzone.saas.store.dto.print.content.PrintReserveDTO;
import com.holderzone.saas.store.dto.print.content.PrintReservePayDTO;
import com.holderzone.saas.store.dto.print.content.nested.PrintReserveItem;
import com.holderzone.saas.store.dto.print.content.nested.PrintReserveType;
import com.holderzone.saas.store.dto.reserve.ReserveItemDTO;
import com.holderzone.saas.store.dto.reserve.ReserveReportDataDTO;
import com.holderzone.saas.store.dto.reserve.ReserveReportTotalDataDTO;
import com.holderzone.saas.store.dto.reserve.TableDTO;
import com.holderzone.saas.store.enums.print.InvoiceTypeEnum;
import com.holderzone.saas.store.enums.print.PrintSourceEnum;
import com.holderzone.saas.store.reserve.api.dto.ReservePrintDTO;
import com.holderzone.saas.store.reserve.api.dto.ReserveRecordDetailDTO;
import com.holderzone.saas.store.util.BigDecimalUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;


@Slf4j
@Component
public class ReservePrintFactory {

    private static final String LINE = "-";

    private static final String CHINESE_COMMA = "，";

    // 5亿纳秒 = 0.5秒
    private static final long FIVE_HUNDRED_MILLION_NANOSECONDS = 500_000_000L;

    private static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    public PrintReserveDTO createBillReserveDtO(ReservePrintDTO reservePrintDTO) {
        PrintReserveDTO printReserveDTO = new PrintReserveDTO();
        printReserveDTO.setStoreName(reservePrintDTO.getStoreName());
        printReserveDTO.setStartTime(reservePrintDTO.getReserveReportParamDTO().getStartTime());
        printReserveDTO.setEndTime(reservePrintDTO.getReserveReportParamDTO().getEndTime());
        printReserveDTO.setInvoiceType(InvoiceTypeEnum.RESERVE_ITEM_STATS.getType());
        printReserveDTO.setType(reservePrintDTO.getReserveReportParamDTO().getType());
        printReserveDTO.setEnterpriseGuid(reservePrintDTO.getEnterpriseGuid());
        printReserveDTO.setStoreGuid(reservePrintDTO.getStoreGuid());
        printReserveDTO.setOperatorStaffGuid(reservePrintDTO.getUserGuid());
        printReserveDTO.setOperatorStaffName(reservePrintDTO.getUserName());
        printReserveDTO.setCreateTime(DateTimeUtils.nowMillis());
        printReserveDTO.setDeviceId(reservePrintDTO.getDeviceId());
        printReserveDTO.setPrintSourceEnum(PrintSourceEnum.AIO);
        printReserveDTO.setPrintUid(DateTimeUtils.nowString());
        UserContextUtils.put(JacksonUtils.writeValueAsString(UserInfoDTO.builder()
                .enterpriseGuid(reservePrintDTO.getEnterpriseGuid())
                .enterpriseName(reservePrintDTO.getEnterpriseName())
                .storeGuid(reservePrintDTO.getStoreGuid())
                .storeName(reservePrintDTO.getStoreName())
                .build()));
        ReserveReportTotalDataDTO reserveReportTotalDataDTO = reservePrintDTO.getReserveReportTotalDataDTO();
        printReserveDTO.setTotalNum(reserveReportTotalDataDTO.getTotalNum());
        List<ReserveReportDataDTO> reportDataList = reserveReportTotalDataDTO.getReportDataList();
        List<PrintReserveType> printReserveTypes = new ArrayList<>();
        if (ObjectUtils.isEmpty(reportDataList)) {
            throw new BusinessException("无报表数据");
        }
        for (ReserveReportDataDTO reserveReportDataDTO : reportDataList) {
            PrintReserveType printReserveType = new PrintReserveType();
            List<PrintReserveItem> printReserveItems = new ArrayList<>();
            printReserveType.setItemTypeGuid(reserveReportDataDTO.getItemTypeGuid());
            printReserveType.setItemTypeName(reserveReportDataDTO.getItemTypeName());
            printReserveType.setItemTypeNum(reserveReportDataDTO.getItemTypeNum());
            List<ReserveItemDTO> reserveItemDTOList = reserveReportDataDTO.getReserveItemDTOList();
            if (!ObjectUtils.isEmpty(reserveItemDTOList)) {
                for (ReserveItemDTO reserveItemDTO : reserveItemDTOList) {
                    PrintReserveItem printReserveItem = new PrintReserveItem();
                    printReserveItem.setItemGuid(reserveItemDTO.getItemGuid());
                    printReserveItem.setItemName(reserveItemDTO.getItemName());
                    printReserveItem.setNumber(reserveItemDTO.getNum());
                    printReserveItems.add(printReserveItem);
                }
            }
            printReserveType.setItemRecordList(printReserveItems);
            printReserveTypes.add(printReserveType);
        }
        printReserveDTO.setTypeRecordList(printReserveTypes);
        return printReserveDTO;
    }

    /**
     * 创建并填充 PrintReservePayDTO 对象，用于预付金打印。
     *
     * @param reserveRecordDetailDTO 预定记录详情 DTO
     * @return 构建好的 PrintReservePayDTO 对象，若入参为空则返回 null
     */
    public PrintReservePayDTO createReservePayDtO(ReserveRecordDetailDTO reserveRecordDetailDTO) {
        // 入参校验
        if (Objects.isNull(reserveRecordDetailDTO)) {
            log.warn("构建 PrintReservePayDTO 失败：入参为空");
            return null;
        }

        // 获取PrintReservePayDTO对象，并填充相关字段
        PrintReservePayDTO printReservePayDTO = getPrintReservePayDTO(reserveRecordDetailDTO);

        // 时间字段格式化处理
        formatAndSetTimeFields(reserveRecordDetailDTO, printReservePayDTO);

        // 桌台信息拼接
        buildAndSetTableName(reserveRecordDetailDTO, printReservePayDTO);

        // 设置基础打印信息（设备、门店、操作人等）
        UserContext userInfo = UserContextUtils.get();
        setBasePrintInfo(reserveRecordDetailDTO, userInfo, printReservePayDTO, InvoiceTypeEnum.RESERVE_PAY_STATS.getType());

        log.info("构建 PrintReservePayDTO 成功：{}", JacksonUtils.writeValueAsString(printReservePayDTO));
        return printReservePayDTO;
    }

    /**
     * 获取 PrintReservePayDTO 对象，并填充相关字段。
     *
     * @param reserveRecordDetailDTO 预定记录详情 DTO
     * @return 构建好的 PrintReservePayDTO 对象
     */
    private PrintReservePayDTO getPrintReservePayDTO(ReserveRecordDetailDTO reserveRecordDetailDTO) {
        // 创建 PrintReservePayDTO 对象
        PrintReservePayDTO printReservePayDTO = new PrintReservePayDTO();

        // 基础信息设置
        printReservePayDTO.setStoreName(reserveRecordDetailDTO.getStoreName());
        if (BigDecimalUtil.greaterThanZero(reserveRecordDetailDTO.getReserveAmount())) {
            printReservePayDTO.setCheckoutStaffs(reserveRecordDetailDTO.getConfirmUserName());
        }
        printReservePayDTO.setName(reserveRecordDetailDTO.getName());
        printReservePayDTO.setGender(reserveRecordDetailDTO.getGender());
        printReservePayDTO.setPhone(reserveRecordDetailDTO.getPhone());
        printReservePayDTO.setNumber(reserveRecordDetailDTO.getNumber());
        printReservePayDTO.setReserveAmount(reserveRecordDetailDTO.getReserveAmount());
        printReservePayDTO.setRemark(reserveRecordDetailDTO.getRemark());
        printReservePayDTO.setPaymentTypeName(reserveRecordDetailDTO.getPaymentTypeName());
        return printReservePayDTO;
    }

    /**
     * 格式化并设置时间字段（预订时间、就餐时间、支付时间）。添加0.5秒四舍五入和存入数据库的记录保持一致
     */
    private void formatAndSetTimeFields(ReserveRecordDetailDTO dto, PrintReservePayDTO printDTO) {
        // 预订时间
        Optional.ofNullable(dto.getConfirmTime())
                .map(time -> time.plusNanos(FIVE_HUNDRED_MILLION_NANOSECONDS).withNano(0))
                .map(DATETIME_FORMATTER::format)
                .ifPresent(printDTO::setConfirmTime);

        // 就餐时间
        Optional.ofNullable(dto.getReserveStartTime())
                .map(time -> time.plusNanos(FIVE_HUNDRED_MILLION_NANOSECONDS).withNano(0))
                .map(DATETIME_FORMATTER::format)
                .ifPresent(printDTO::setReserveStartTime);

        // 支付时间
        Optional.ofNullable(dto.getPaymentTime())
                .map(time -> time.plusNanos(FIVE_HUNDRED_MILLION_NANOSECONDS).withNano(0))
                .map(DATETIME_FORMATTER::format)
                .ifPresent(printDTO::setPaymentTime);
    }

    /**
     * 拼接桌台信息（区域名-桌台名），并设置到打印 DTO 中。
     */
    private void buildAndSetTableName(ReserveRecordDetailDTO dto, PrintReservePayDTO printDTO) {
        Collection<TableDTO> tables = dto.getTables();
        String tableName = Optional.ofNullable(tables)
                .orElse(Collections.emptyList()).stream()
                .map(table -> table.getAreaName() + LINE + table.getName())
                .collect(Collectors.joining(CHINESE_COMMA));
        printDTO.setTableName(tableName);
    }

    /**
     * 设置基础打印信息，包括票据类型、设备信息、企业门店信息、操作人及时间等。
     *
     * @param reserveRecordDetailDTO 请求参数，用于获取设备相关信息
     * @param userInfo               用户上下文信息，不可为空
     * @param printDTO               打印数据载体，需填充基础信息
     * @param invoiceType            票据类型枚举值
     */
    private void setBasePrintInfo(ReserveRecordDetailDTO reserveRecordDetailDTO,
                                  UserContext userInfo,
                                  PrintDTO printDTO,
                                  Integer invoiceType) {
        // 参数校验
        if (Objects.isNull(reserveRecordDetailDTO) || Objects.isNull(userInfo) || Objects.isNull(printDTO)) {
            log.warn("设置基础打印信息失败：参数为空");
            return;
        }

        // 设置票据类型
        printDTO.setInvoiceType(invoiceType);

        // 设备编号与打印来源
        String deviceId = reserveRecordDetailDTO.getDeviceId();
        Integer deviceType = reserveRecordDetailDTO.getDeviceType();
        printDTO.setDeviceId(deviceId);
        if (Objects.nonNull(deviceType)) {
            printDTO.setPrintSourceEnum(PrintSourceEnum.getPrintSourceByDeviceType(deviceType));
        } else {
            log.warn("打印来源失败：设备类型为空");
        }

        // 打印唯一标识
        String printUid = String.valueOf(System.currentTimeMillis());
        printDTO.setPrintUid(printUid);

        // 企业与门店信息
        String enterpriseGuid = StringUtils.isEmpty(reserveRecordDetailDTO.getEnterpriseGuid()) ?
                userInfo.getEnterpriseGuid() :
                reserveRecordDetailDTO.getEnterpriseGuid();
        String storeGuid = StringUtils.isEmpty(reserveRecordDetailDTO.getStoreGuid()) ?
                userInfo.getStoreGuid() :
                reserveRecordDetailDTO.getStoreGuid();
        printDTO.setEnterpriseGuid(enterpriseGuid);
        printDTO.setStoreGuid(storeGuid);

        // 打印区域 GUID（由调用方决定是否赋值）
        printDTO.setAreaGuid(null);

        // 操作人员信息
        String staffGuid = StringUtils.isEmpty(userInfo.getUserGuid()) ?
                reserveRecordDetailDTO.getConfirmUserGuid() :
                userInfo.getUserGuid();
        String staffName = StringUtils.isEmpty(userInfo.getUserName()) ?
                reserveRecordDetailDTO.getConfirmUserName() :
                userInfo.getUserName();

        printDTO.setOperatorStaffGuid(staffGuid);
        printDTO.setOperatorStaffName(staffName);

        // 打印时间
        Long createTime = DateTimeUtils.nowMillis();
        printDTO.setCreateTime(createTime);
    }

}
