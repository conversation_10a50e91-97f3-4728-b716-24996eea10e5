package com.holderzone.saas.store.reserve.core.event.impl.handler;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.framework.anno.CustomerRegister;
import com.holderzone.framework.event.CustomerEvent;
import com.holderzone.framework.event.observer.CustomerObserver;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.redisson.sdk.lock.RedissonLockUtil;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.message.BusinessMessageDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.takeaway.OrderType;
import com.holderzone.saas.store.enums.BaseDeviceTypeEnum;
import com.holderzone.saas.store.enums.msg.BusinessMsgTypeEnum;
import com.holderzone.saas.store.reserve.api.dto.ReserveSystemMsgDTO;
import com.holderzone.saas.store.reserve.api.enums.ClientStateEnum;
import com.holderzone.saas.store.reserve.api.enums.OrderTypeEnum;
import com.holderzone.saas.store.reserve.core.common.ReserveRoleEnum;
import com.holderzone.saas.store.reserve.core.dal.ReserveRecordDo;
import com.holderzone.saas.store.reserve.core.dal.mapper.ReserveRecordDoMapper;
import com.holderzone.saas.store.reserve.core.domain.ReserveRecord;
import com.holderzone.saas.store.reserve.core.domain.ReserveRecordStateEnum;
import com.holderzone.saas.store.reserve.core.domain.Table;
import com.holderzone.saas.store.reserve.core.event.BaseEventHandler;
import com.holderzone.saas.store.reserve.core.event.impl.event.BeDelayEvent;
import com.holderzone.saas.store.reserve.core.event.impl.event.PreparedLockTableEvent;
import com.holderzone.saas.store.reserve.core.event.impl.event.UnLockTableEvent;
import com.holderzone.saas.store.reserve.core.rocket.Consume;
import com.holderzone.saas.store.reserve.intergration.table.MessageClientService;
import com.holderzone.saas.store.reserve.intergration.table.OrganizationClientService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 自动预期
 */
@Slf4j
@Component
@CustomerRegister(isRegister = true)
public class BeDelayHandler extends BaseEventHandler<BeDelayEvent> implements CustomerObserver<BeDelayEvent> {

    @Autowired
    ReserveRecordDoMapper reserveRecordDoMapper;

    @Autowired
    private OrganizationClientService organizationClientService;

    @Autowired
    private MessageClientService messageClientService;

    @Override
    public void execute(BeDelayEvent baseEvent) {
        LocalDateTime ref = Consume.THREAD_LOCAL.get();
        ReserveRecord reserveRecord = baseEvent.getReserveRecord();
        Assert.isTrue(reserveRecord.getState() == ReserveRecordStateEnum.COMMIT.getCode()
                || reserveRecord.getState() == ReserveRecordStateEnum.PASS.getCode(), "只能逾期发起,和审核通过状态的预定申请");
        if (ref != null && !reserveRecord.getReserveStartTime().equals(ref)) {
            throw new BusinessException("系统自动逾期失败,时间被修改");
        }
        if (((reserveRecord.getState() & ReserveRecordStateEnum.SYSTEM_CANCLE.getCode()) == ReserveRecordStateEnum.SYSTEM_CANCLE.getCode()
                || (reserveRecord.getState() & ClientStateEnum.PICK_TABLE.getCode()) == ClientStateEnum.PICK_TABLE.getCode())) {
            throw new BusinessException("系统自动逾期失败,状态变更");
        }
        // 修改预定单数据
        updateReserveRecord(baseEvent);
        // 自动预期后置处理
        beDelayAfterHandler(baseEvent);
    }


    /**
     * 修改预定单数据
     */
    private void updateReserveRecord(BeDelayEvent baseEvent) {
        ReserveRecord reserveRecord = baseEvent.getReserveRecord();
        // 是否预期
        Boolean isDelay = isDelay(reserveRecord);
        ReserveRecordDo recordDo = new ReserveRecordDo();
        recordDo.setIsDelay(isDelay);
        recordDo.setGuid(reserveRecord.getGuid());
        recordDo.setModifiedStaffGuid(ReserveRoleEnum.SYSTEM.getRole());
        recordDo.setModifiedStaffName(ReserveRoleEnum.SYSTEM.getRole());
        recordDo.setGmtModified(LocalDateTime.now());
        // copy
        reserveRecord.setIsDelay(isDelay);
        reserveRecord.setModifiedStaffGuid(recordDo.getModifiedStaffGuid());
        reserveRecord.setModifiedStaffName(recordDo.getModifiedStaffName());
        reserveRecord.setGmtModified(recordDo.getGmtModified());
        reserveRecordDoMapper.update(recordDo, new LambdaQueryWrapper<ReserveRecordDo>().eq(ReserveRecordDo::getGuid, recordDo.getGuid()));
    }

    /**
     * 自动预期后置处理
     */
    private void beDelayAfterHandler(BeDelayEvent baseEvent) {
        ReserveRecord reserveRecord = baseEvent.getReserveRecord();
        // 预定已锁桌 接触锁定
        if (Boolean.TRUE.equals(reserveRecord.getIsLocked())) {
            UnLockTableEvent unLockTableEvent = new UnLockTableEvent(reserveRecord);
            customerPublish.publish(new CustomerEvent<>(unLockTableEvent));
        } else {
            //预定未锁定
            List<String> old = reserveRecord.getTables().stream().map(Table::getGuid).collect(Collectors.toList());
            List<String> neo = Collections.emptyList();
            PreparedLockTableEvent preparedLockTableEvent = new PreparedLockTableEvent(reserveRecord, old, neo);
            customerPublish.publish(new CustomerEvent<>(preparedLockTableEvent));
        }
        // 发送消息到pos
        sendMsgByPos(reserveRecord);
    }

    /**
     * 发送消息到pos
     */
    private void sendMsgByPos(ReserveRecord reserveRecord) {
        if (!Objects.equals(reserveRecord.getOrderType(), OrderTypeEnum.RESERVE.getCode())
                || BaseDeviceTypeEnum.isApplet(reserveRecord.getDeviceType())) {
            return;
        }
        // 查询门店名称
        StoreDTO storeDTO = organizationClientService.queryStoreByGuid(reserveRecord.getStoreGuid());
        BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setMessageType(BusinessMsgTypeEnum.TABLE_STATE_MSG_TYPE.getId());
        businessMessageDTO.setPlatform(String.valueOf(OrderType.OTHER_ORDER.getType()));
        businessMessageDTO.setStoreGuid(reserveRecord.getStoreGuid());
        businessMessageDTO.setStoreName(storeDTO.getName());
        businessMessageDTO.setDetailMessageType(BusinessMsgTypeEnum.RESERVE_RECORD_STATE_CHANGE_POS.getId());
        businessMessageDTO.setSubject("");
        ReserveSystemMsgDTO reserveSystemMsgDTO = new ReserveSystemMsgDTO();
        reserveSystemMsgDTO.setGuid(reserveRecord.getGuid());
        reserveSystemMsgDTO.setAutoRev(0);
        businessMessageDTO.setContent(JacksonUtils.writeValueAsString(reserveSystemMsgDTO));
        businessMessageDTO.setCreateTime(LocalDateTime.now());
        businessMessageDTO.setPushTime(LocalDateTime.now());
        log.info("[BeDelayEvent][POS]预定消息推送入参:{}", JacksonUtils.writeValueAsString(businessMessageDTO));
        messageClientService.msg(businessMessageDTO);
    }

    @Override
    protected void pre(BeDelayEvent baseEvent) {
        ReserveRecord reserveRecord = baseEvent.getReserveRecord();
        boolean result = RedissonLockUtil.tryLock(reserveRecord.getGuid(), 3, 10);
        if (!result) {
            throw new BusinessException("当前预定记录正被其他人操作,请稍候重试!!");
        }
        super.pre(baseEvent);
    }

    @Override
    protected void after(BeDelayEvent baseEvent) {
        ReserveRecord reserveRecord = baseEvent.getReserveRecord();
        RedissonLockUtil.unlock(reserveRecord.getGuid());
        super.after(baseEvent);
    }
}