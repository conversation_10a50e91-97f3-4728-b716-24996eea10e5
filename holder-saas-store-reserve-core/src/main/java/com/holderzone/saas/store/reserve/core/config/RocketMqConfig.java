package com.holderzone.saas.store.reserve.core.config;

/**
 * <AUTHOR>
 * @version 1.0
 * @className RocketMqConfig
 * @date 2018/09/18 17:46
 * @description
 * @program holder-saas-store-takeaway
 */
public class RocketMqConfig {

    public static final String TAKEAWAY_PRODUCERS_ORDER_TOPIC = "takeaway-producers-order-topic";

    public static final String TAKEAWAY_PRODUCERS_ORDER_TAG = "takeaway-producers-order-tag";

    public static final String TAKEAWAY_ORDER_PRODUCERS_GROUP = "takeaway-order-producers-group";

    public static final String TAKEAWAY_CONSUMERS_ORDER_TOPIC = "takeaway-consumers-order-topic";

    public static final String TAKEAWAY_CONSUMERS_ORDER_TAG = "takeaway-consumers-order-tag";

    public static final String TAKEAWAY_ORDER_CONSUMERS_GROUP = "takeaway-order-consumers-group";

    public static final String PRINT_MESSAGE_TOPIC = "print-message-topic";

    public static final String PRINT_MESSAGE_TAG = "print-message-tag";

    public static final String MESSAGE_CONTEXT = "message-context";

    public static final String KDS_MESSAGE_TOPIC = "kds-message-topic";

    public static final String KDS_PREPARE_TAG = "kds-prepare-tag";

    public static final String ERP_MESSAGE_TOPIC = "erp-message-topic";

    public static final String ERP_REDUCE_TAG = "erp-reduce-tag";

    public static final String ERP_MESSAGE_GROUP = "erp-message-group";

    public static final String MDM_MESSAGE_TOPIC = "mdm-message-topic";

    public static final String MDM_REDUCE_TAG = "mdm-reduce-tag";

    public static final String MDM_MESSAGE_GROUP = "mdm-message-group";

    public static final String DISTRIBUTION_MESSAGE_TOPIC = "delivery-message-topic";

    public static final String DISTRIBUTION_START_TAG = "delivery-start-tag";

    public static final String DISTRIBUTION_MESSAGE_GROUP = "delivery-message-group";

    public static final String KDS_URGE_TAG = "kds-urge-tag";

    public static final String KDS_REFUND_TAG = "kds-refund-tag";

    public static final String USER_INFO = "userInfo";
}
