package com.holderzone.saas.store.reserve.core.dal;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReserveConfig
 * @date 2019/04/23 10:24
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Data
@Accessors(chain = true)
@TableName("hss_private_room")
public class PrivateRoomDo {

    private Long id;

    private String guid;

    private String storeGuid;

    private String tableGuid;

    private LocalDateTime gmtCreate;

    private LocalDateTime gmtModified;
}