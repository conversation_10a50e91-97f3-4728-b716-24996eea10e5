package com.holderzone.saas.store.reserve.core.event.impl.handler;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.anno.CustomerRegister;
import com.holderzone.framework.base.dto.message.MessageDTO;
import com.holderzone.framework.base.dto.message.MessageType;
import com.holderzone.framework.base.dto.message.ShortMessageDTO;
import com.holderzone.framework.event.observer.CustomerObserver;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.redisson.sdk.lock.RedissonLockUtil;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.resource.common.dto.enterprise.DeductShortMessageDTO;
import com.holderzone.resource.common.dto.enterprise.MessageConfigDTO;
import com.holderzone.saas.store.reserve.api.enums.ClientStateEnum;
import com.holderzone.saas.store.reserve.core.domain.ReserveRecord;
import com.holderzone.saas.store.reserve.core.domain.ReserveRecordStateEnum;
import com.holderzone.saas.store.reserve.core.event.BaseEventHandler;
import com.holderzone.saas.store.reserve.core.event.impl.event.ShortMessageEvent;
import com.holderzone.saas.store.reserve.core.rocket.DelayLockTableListener;
import com.holderzone.saas.store.reserve.intergration.table.EntServiceClient;
import com.holderzone.saas.store.reserve.intergration.table.SmsClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className LockTableEventHandler
 * @date 2019/04/26 16:59
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Slf4j
@Component
@CustomerRegister(isRegister = true)
public class ShortMessageEventHandler extends BaseEventHandler<ShortMessageEvent> implements CustomerObserver<ShortMessageEvent> {
    @Autowired
    SmsClient smsClient;
    @Autowired
    EntServiceClient es;

    @Override
    public void execute(ShortMessageEvent baseEvent) {
        LocalDateTime ref = DelayLockTableListener.THREAD_LOCAL.get();
        ReserveRecord record = baseEvent.getReserveRecord();
        if (ref != null && !ref.equals(record.getReserveStartTime())) {
            throw new BusinessException("发送提示短信失败,时间被修改");
        }
        if ((record.getState() & ReserveRecordStateEnum.SYSTEM_CANCLE.getCode()) == ReserveRecordStateEnum.SYSTEM_CANCLE.getCode()
                || (record.getState() & ClientStateEnum.PICK_TABLE.getCode()) == ClientStateEnum.PICK_TABLE.getCode()) {
            throw new BusinessException("发送提示短信失败,状态变更");
        }


        try {
            String entGuid = UserContextUtils.getEnterpriseGuid();

            MessageConfigDTO messageInfo = es.getMessageInfo(entGuid);
            //会员充值
            if (messageInfo != null) {
                if (messageInfo.getResidueCount() == -1 || messageInfo.getResidueCount() >= 1) {
                    log.info("send message before request: entry {} " + JacksonUtils.writeValueAsString(messageInfo));
                    ShortMessageDTO shortMessageDTO = new ShortMessageDTO();
                    shortMessageDTO.setPhoneNumber(record.getCustomer().getPhone());
                    shortMessageDTO.setParams(baseEvent.getMessageFunction().apply(record));
                    shortMessageDTO.setShortMessageType(baseEvent.getMessageTypeSupplier().get());
                    MessageDTO messageDTO = new MessageDTO();
                    messageDTO.setMessageType(MessageType.SHORT_MESSAGE);
                    messageDTO.setShortMessage(shortMessageDTO);
                    smsClient.sendMessage(messageDTO);
                    log.info("send message success ! ");
                    List<DeductShortMessageDTO> ds = new ArrayList<>();
                    DeductShortMessageDTO d = new DeductShortMessageDTO();
                    d.setDeductCount(1);
                    d.setEnterpriseGuid(entGuid);
                    ds.add(d);
                    es.deductShortMessage(ds);
                }
            }
        } catch (Exception e) {
            log.error("send message fail: ", e);
        }

    }

    @Override
    protected void pre(ShortMessageEvent baseEvent) {
        ReserveRecord record = baseEvent.getReserveRecord();
        boolean result = RedissonLockUtil.tryLock(record.getGuid(), 3, 10);
        if (!result) {
            throw new BusinessException("当前预定记录正被其他人操作,请稍候重试!!");
        }
        super.pre(baseEvent);
    }

    @Override
    protected void after(ShortMessageEvent baseEvent) {
        ReserveRecord record = baseEvent.getReserveRecord();
        RedissonLockUtil.unlock(record.getGuid());
        super.after(baseEvent);
    }
}