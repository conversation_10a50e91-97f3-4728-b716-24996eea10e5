package com.holderzone.saas.store.reserve.core.event.impl.event;

import com.holderzone.saas.store.reserve.core.domain.ReserveRecord;
import com.holderzone.saas.store.reserve.core.event.BaseEvent;
import lombok.Getter;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className LaunchEvent
 * @date 2019/04/23 17:24
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Getter
public class PreparedLockTableEvent extends BaseEvent {
    private List<String> old;
    private List<String> neo;

    public PreparedLockTableEvent(ReserveRecord reserveRecord, List<String> old, List<String> neo) {
        super(reserveRecord);
        this.old = old;
        this.neo = neo;
    }

    private static final String NAME= PreparedLockTableEvent.class.getSimpleName();

    @Override
    public String getName() {
        return NAME;
    }
}