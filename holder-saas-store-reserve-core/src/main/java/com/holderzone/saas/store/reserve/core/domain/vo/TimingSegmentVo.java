package com.holderzone.saas.store.reserve.core.domain.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.holderzone.saas.store.reserve.api.dto.TimingSegment;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TimingSegmentVo
 * @date 2019/04/30 10:05
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Data
public class TimingSegmentVo implements TimingSegment {

    private static LocalTime _24H = LocalTime.of(0, 0);

    private LocalTime start;

    private LocalTime end;

    private Integer period;

    public TimingSegmentVo(LocalTime start, LocalTime end) {
        this.start = start;
        this.end = end;
    }

    public TimingSegmentVo(LocalTime start, LocalTime end, Integer period) {
        this.start = start;
        this.end = end;
        this.period = period;
    }

    public TimingSegmentVo() {
    }

    @JsonIgnore
    public List<TimingSegmentVo> getSub() {
        LocalDate now = LocalDate.now();
        LocalDateTime start = startOfRef(now);
        LocalDateTime end = endOfRef(now);
        if (!end.minusMinutes(period()).isAfter(start)) {
            return Arrays.asList(new TimingSegmentVo(getStart(), getEnd()));
        }
        if (end.toLocalTime().equals(_24H)) {
            end = end.minusSeconds(1);
        }
        List<TimingSegmentVo> sub = new ArrayList<>();
        LocalDateTime subStart = start;
        while (!subStart.isAfter(end)) {
            LocalDateTime subEnd = subStart.plus(period(), ChronoUnit.MINUTES);
            sub.add(new TimingSegmentVo(subStart.toLocalTime(), subEnd.toLocalTime()));
            subStart = subEnd;
        }
        return sub;
    }
}