package com.holderzone.saas.store.reserve.core.event.impl.event;

import com.holderzone.saas.store.reserve.core.domain.ReserveRecord;
import com.holderzone.saas.store.reserve.core.event.BaseEvent;


public class PayTimeoutEvent extends BaseEvent {

    public PayTimeoutEvent(ReserveRecord reserveRecord) {
        super(reserveRecord);
    }

    private static final String NAME = PayTimeoutEvent.class.getSimpleName();

    @Override
    public String getName() {
        return NAME;
    }
}