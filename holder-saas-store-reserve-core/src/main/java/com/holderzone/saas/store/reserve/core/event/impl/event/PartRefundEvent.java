package com.holderzone.saas.store.reserve.core.event.impl.event;

import com.holderzone.saas.store.reserve.core.domain.ReserveRecord;
import com.holderzone.saas.store.reserve.core.event.BaseEvent;
import lombok.Getter;


/**
 * 部分退款
 */
@Getter
public class PartRefundEvent extends BaseEvent {

    public PartRefundEvent(ReserveRecord reserveRecord) {
        super(reserveRecord);
    }

    private static final String NAME = PartRefundEvent.class.getSimpleName();

    @Override
    public String getName() {
        return NAME;
    }
}