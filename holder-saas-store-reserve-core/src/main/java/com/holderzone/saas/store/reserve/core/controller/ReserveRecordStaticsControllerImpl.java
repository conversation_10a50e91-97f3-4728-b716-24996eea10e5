package com.holderzone.saas.store.reserve.core.controller;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.reserve.CommitStatisticsReqDTO;
import com.holderzone.saas.store.dto.reserve.ReserveCommitStaticsDTO;
import com.holderzone.saas.store.reserve.api.ReserveRecordStaticsApi;
import com.holderzone.saas.store.reserve.core.service.ReserveRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Primary;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;

@Slf4j
@Primary
@RequiredArgsConstructor
@RestController
public class ReserveRecordStaticsControllerImpl implements ReserveRecordStaticsApi {

    private final ReserveRecordService reserveRecordService;

    @Override
    public ReserveCommitStaticsDTO commitStatistics() {
        return reserveRecordService.commitStatistics();
    }

    @Override
    public ReserveCommitStaticsDTO commitStatisticsByDay() {
        return reserveRecordService.commitStatisticsByDay();
    }

    @Override
    public ReserveCommitStaticsDTO commitStatisticsByScope(@RequestBody CommitStatisticsReqDTO commitStatisticsByScope) {
        log.info("[查询时间范围内待处理订单]query={}", JacksonUtils.writeValueAsString(commitStatisticsByScope));
        if (StringUtils.isEmpty(commitStatisticsByScope.getStoreGuid())) {
            commitStatisticsByScope.setStoreGuid(UserContextUtils.getStoreGuid());
        }
        // 查询近30天待处理订单总数,经前端讨论为后30天
        if (ObjectUtils.isEmpty(commitStatisticsByScope.getStartDate())
                || ObjectUtils.isEmpty(commitStatisticsByScope.getEndDate())) {
            commitStatisticsByScope.setStartDate(LocalDate.now());
            commitStatisticsByScope.setEndDate(LocalDate.now().plusDays(30));
        }
        return reserveRecordService.commitStatisticsByScope(commitStatisticsByScope);
    }
}