package com.holderzone.saas.store.reserve.core.event.impl.handler;

import com.holderzone.framework.anno.CustomerRegister;
import com.holderzone.framework.event.CustomerEvent;
import com.holderzone.framework.event.observer.CustomerObserver;
import com.holderzone.saas.store.dto.reserve.TableDTO;
import com.holderzone.saas.store.reserve.core.domain.ReserveRecord;
import com.holderzone.saas.store.reserve.core.domain.Table;
import com.holderzone.saas.store.reserve.core.event.impl.event.EffectiveEvent;
import com.holderzone.saas.store.reserve.core.event.impl.event.PreparedLockTableEvent;
import com.holderzone.saas.store.reserve.core.mapstruct.TableMapstruct;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className LaunchEventHandler
 * @date 2019/04/23 17:34
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Component
@CustomerRegister(isRegister = true)
public class EffectiveEventHandler extends TableOpenTableEventHandler<EffectiveEvent> implements CustomerObserver<EffectiveEvent> {

    @Override
    protected List<TableDTO> fetchTables(EffectiveEvent baseEvent) {
        ReserveRecord record = baseEvent.getReserveRecord();
        List<Table> tableRelationDos =
                record.getTables().stream().filter(e -> e.getState() == 1).collect(Collectors.toList());

        Assert.isTrue(tableRelationDos != null && !tableRelationDos.isEmpty(), "没有可用桌台(或桌台被占用),请重新选取桌台");
        return tableRelationDos.stream().map(TableMapstruct.TABLE_MAPSTRUCT::domaintoDto).collect(Collectors.toList());
    }

    @Override
    public void execute(EffectiveEvent baseEvent) {
        super.execute(baseEvent);
        ReserveRecord record = baseEvent.getReserveRecord();
        if (!record.getIsLocked()) {
            List<Table> tableRelationDos =
                    record.getTables().stream().collect(Collectors.toList());
            if (tableRelationDos != null && !tableRelationDos.isEmpty()) {
                List<String> all = tableRelationDos.stream().map(Table::getGuid).collect(Collectors.toList());
                //预定未锁定
                List<String> neo = Collections.emptyList();
                PreparedLockTableEvent preparedLockTableEvent = new PreparedLockTableEvent(record, all, neo);
                customerPublish.publish(new CustomerEvent(preparedLockTableEvent));
            }
        }
    }
}