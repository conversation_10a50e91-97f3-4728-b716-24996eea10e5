package com.holderzone.saas.store.reserve.core.rocket;

import com.holderzone.framework.event.publish.impl.CustomerPublishImpl;
import com.holderzone.framework.rocketmq.anno.RocketListenerHandler;
import com.holderzone.framework.rocketmq.common.AbstractRocketMqConsumer;
import com.holderzone.framework.rocketmq.constants.RocketMqTopic;
import com.holderzone.resource.common.dto.mq.UnMessage;
import com.holderzone.saas.store.reserve.core.dal.mapper.ReserveRecordDoMapper;
import com.holderzone.saas.store.reserve.intergration.table.OrganizationClientService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className InitEnterpriseProductListener
 * @date 19-2-22 上午9:58
 * @description 订阅云端产品基本信息消息（同步场景：新建企业时对产品授权、叠加产品时对产品授权）
 * @program holder-saas-store-staff
 */
@Slf4j
@Component
@RocketListenerHandler(topic = "reserve-delay-lock-table-topic",
        tags = {Consume.LOCK_TAG, Consume.BE_DELAY_TAG, Consume.ACCEPT_DELAY_TAG, Consume.PAY_TIMEOUT_TAG, Consume.WARN_MESSAGE_TAG},
        consumerGroup = "reserve-delay-lock-table-group")
public class DelayLockTableListener extends AbstractRocketMqConsumer<RocketMqTopic, UnMessage> implements Consume {

    @Autowired
    ReserveRecordDoMapper reserveRecordDoMapper;
    @Lazy
    @Autowired
    CustomerPublishImpl customerPublish;
    @Autowired
    OrganizationClientService organizationClientService;
    @Override
    public boolean consumeMsg(UnMessage unMessage, MessageExt messageExt) {

        try {
            doConsume(unMessage);
        } catch (Exception e) {
            log.error("consumeMsg fail" +
                    "----------------unMessage: {}", unMessage);
        }
        return true;
    }

    @Override
    public ReserveRecordDoMapper getReserveRecordDoMapper() {
        return reserveRecordDoMapper;
    }

    @Override
    public CustomerPublishImpl getCustomerPublish() {
        return customerPublish;
    }
    @Override
    public OrganizationClientService getOrganizationClientService() {
        return organizationClientService;
    }
}
