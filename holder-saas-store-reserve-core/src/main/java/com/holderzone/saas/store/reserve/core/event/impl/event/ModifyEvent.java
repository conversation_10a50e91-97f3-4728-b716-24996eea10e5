package com.holderzone.saas.store.reserve.core.event.impl.event;

import com.holderzone.saas.store.reserve.core.domain.ReserveRecord;

/**
 * <AUTHOR>
 * @version 1.0
 * @className LaunchEvent
 * @date 2019/04/23 17:24
 * @description //TODO
 * @program holder-saas-store-reserve
 */

public class ModifyEvent extends LaunchEvent {

    public ModifyEvent(ReserveRecord reserveRecord, Boolean saveTable) {
        super(reserveRecord, saveTable);
    }

    private static final String NAME= ModifyEvent.class.getSimpleName();

    @Override
    public String getName() {
        return NAME;
    }
}