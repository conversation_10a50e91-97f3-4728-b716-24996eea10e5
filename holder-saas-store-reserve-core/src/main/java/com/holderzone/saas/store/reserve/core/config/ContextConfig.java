package com.holderzone.saas.store.reserve.core.config;

import com.holderzone.framework.event.publish.impl.CustomerPublishImpl;
import com.holderzone.saas.store.reserve.core.domain.standar.EventReserveRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ContextConfig
 * @date 2019/04/27 11:29
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Configuration
public class ContextConfig {
    @Autowired
    private CustomerPublishImpl customerPublish;

    @PostConstruct
    public void init(){
        EventReserveRecord.setCustomerPublish(customerPublish);
    }

}