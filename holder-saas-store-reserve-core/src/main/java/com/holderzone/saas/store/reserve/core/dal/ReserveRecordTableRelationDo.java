package com.holderzone.saas.store.reserve.core.dal;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReserveRecordTableRelationDo
 * @date 2019/04/23 15:03
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Data
@TableName("hss_r_reserve_record_table")
public class ReserveRecordTableRelationDo {
    @TableId
    private String guid;
    private String tableGuid;
    private String tableName;
    private String areaGuid;
    private String areaName;
    private String reserveRecordGuid;
    private Integer state = 0;
    @TableLogic
    private Boolean isDeleted;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;
}