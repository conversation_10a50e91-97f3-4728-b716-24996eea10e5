package com.holderzone.saas.store.reserve.core.domain;

import com.holderzone.saas.store.reserve.api.common.Utils;
import com.holderzone.saas.store.reserve.core.domain.vo.AreaVO;
import com.holderzone.saas.store.reserve.core.domain.vo.TimingSegmentVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReserveConfig
 * @date 2019/04/23 10:24
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Data
@Accessors(chain = true)
public class ReserveConfig {

    private String guid;

    private String storeGuid;

    private Collection<TimingSegmentVo> segments;

    private Float lockTableTiming = 0.5F;

    private Float unLockTableTiming = 0.5F;

    private Float sendWarnMessageTiming = 0F;

    private Boolean isEnableSuccessMessage = true;

    private Boolean isEnableWarnMessage = true;

    private Boolean isEnablePrivateRoom = false;

    /**
     * 适用终端 ,隔开
     *
     * @see com.holderzone.saas.store.enums.BaseDeviceTypeEnum
     */
    @ApiModelProperty(value = "适用终端 ,隔开")
    private String deviceType;

    @ApiModelProperty(value = "可预订人数 ,隔开")
    private String crowdRange;

    @ApiModelProperty(value = "需求类型")
    private List<String> requirementType;

    @ApiModelProperty(value = "可预订日期")
    private Integer reserveDay;

    @ApiModelProperty(value = "可预订区域")
    private List<AreaVO> reserveArea;

    @ApiModelProperty(value = "是否开启预订定金")
    private Boolean depositFlag;

    @ApiModelProperty(value = "收取类型 0:按人数收取 1：按区域收取")
    private Integer depositType;

    @ApiModelProperty(value = "预订超x人数收取定金")
    private String exceedsPeopleDeposit;

    @ApiModelProperty(value = "按桌台区域收取定金")
    private List<AreaVO> areaDeposit;

    @ApiModelProperty(value = "可取消订单时间 单位小时")
    private Integer cancelableTime;

    @ApiModelProperty(value = "邀请函图片")
    private List<String> invitationImages;

    public TimingSegmentVo fetchCurrent(LocalDateTime localDateTime) {
        return Utils.fetchCurrentSeg(localDateTime, getSegments());
    }

    public Collection<TimingSegmentVo> getSegments() {
        if (segments == null) {
            segments = Arrays.asList(new TimingSegmentVo(LocalTime.of(0, 0), LocalTime.of(23, 59), 30));
        }
        return segments;
    }

    public List<TimingSegmentVo> getSub() {
        return getSegments().stream().flatMap(vo -> vo.getSub().stream()).collect(Collectors.toList());
    }
}