package com.holderzone.saas.store.reserve.core.dal;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2019/04/30 10:12
 */
@Data
@Accessors(chain = true)
@TableName("hss_reserve_config")
public class ReserveConfigDo {

    private Long id;
    private String guid;

    private String storeGuid;
    private String segmentsStr;
    private Float lockTableTiming;
    private Float unLockTableTiming;
    private Float sendWarnMessageTiming;
    private Boolean isEnableSuccessMessage;
    private Boolean isEnableWarnMessage;
    private Boolean isEnablePrivateRoom;

    /**
     * 适用终端
     *
     * @see com.holderzone.saas.store.enums.BaseDeviceTypeEnum
     */
    private String deviceType;

    /**
     * 可预订人数 ,隔开
     */
    private String crowdRange;

    /**
     * 需求类型
     */
    private String requirementType;

    /**
     * 可预订日期
     */
    private Integer reserveDay;

    /**
     * 可预订区域
     */
    private String reserveArea;

    /**
     * 是否开启预订定金
     */
    private Boolean depositFlag;

    /**
     * 收取类型 0:按人数收取 1：按区域收取
     */
    private Integer depositType;

    /**
     * 预订超x人数收取定金
     */
    @TableField(strategy = FieldStrategy.NOT_NULL)
    private String exceedsPeopleDeposit;

    /**
     * 按桌台区域收取定金
     */
    private String areaDeposit;

    /**
     * 可取消订单时间 单位小时
     */
    private Integer cancelableTime;

    /**
     * 是否启用，1-已启用，0-未启用
     */
    private Boolean isEnable;

    /**
     * 预定海报内容
     */
    private String invitationImages;

    /**
     * 是否删除，1-已删除，0-未删除
     */
    @TableLogic
    private Boolean isDeleted;

    /**
     * 创建人guid
     */
    private String createStaffGuid;

    /**
     * 更新人guid
     */
    private String modifiedStaffGuid;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    private LocalDateTime gmtModified;

    public void setSegmentsStr(String segmentsStr) {
        if (StringUtils.hasText(segmentsStr) && !"null".equals(segmentsStr)) {
            this.segmentsStr = segmentsStr;
        }
    }
}