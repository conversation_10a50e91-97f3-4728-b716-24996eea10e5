package com.holderzone.saas.store.reserve.core.event.impl.event;

import com.holderzone.saas.store.reserve.core.domain.ReserveRecord;
import com.holderzone.saas.store.reserve.core.event.BaseEvent;
import lombok.Getter;

import java.math.BigDecimal;

@Getter
public class MemberPayEvent extends BaseEvent {

    /**
     * 会员guid
     */
    private String memberInfoGuid;

    /**
     * 会员主卡持卡guid
     */
    private String memberInfoCardGuid;

    /**
     * 会员支付密码
     */
    private String memberPassword;

    /**
     * 预定金
     */
    private BigDecimal reserveAmount;

    /**
     * 支付方式
     *
     * @see com.holderzone.saas.store.enums.weixin.WxAppletMemberPayTypeEnum
     */
    private Integer payType;


    public MemberPayEvent(ReserveRecord reserveRecord, String memberInfoGuid, String memberInfoCardGuid,
                          String memberPassword, Integer payType) {
        super(reserveRecord);
        this.memberInfoGuid = memberInfoGuid;
        this.memberInfoCardGuid = memberInfoCardGuid;
        this.memberPassword = memberPassword;
        this.payType = payType;
        this.reserveAmount = reserveRecord.getReserveAmount();
    }

    private static final String NAME = MemberPayEvent.class.getSimpleName();

    @Override
    public String getName() {
        return NAME;
    }
}