package com.holderzone.saas.store.reserve.core.event;

import com.holderzone.framework.event.CustomerEvent;
import com.holderzone.framework.event.observer.CustomerObserver;
import com.holderzone.framework.event.publish.impl.CustomerPublishImpl;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.saas.store.reserve.core.domain.ReserveRecord;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.transaction.support.TransactionTemplate;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BaseEventHandler
 * @date 2019/04/23 17:27
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Slf4j
public abstract class BaseEventHandler<T extends BaseEvent> implements CustomerObserver<T> {
    @Lazy
    @Autowired
    protected CustomerPublishImpl customerPublish;
    @Autowired
    protected TransactionTemplate transactionTemplate;

    public abstract void execute(T baseEvent);

    @Override
    public void notify(CustomerEvent<T> customerEvent) {
        T baseEvent = (T) customerEvent.getSource();
        log.info("{} event start, param {}", baseEvent.getName(), baseEvent.getReserveRecord());
        try {
            pre(baseEvent);
            transactionTemplate.execute((transactionStatus) -> {
                try {
                    execute(baseEvent);
                } catch (Exception e) {
                    log.error("event execute fail", e);
                    transactionStatus.setRollbackOnly();
                    throw e;
                }
                return null;
            });
        } catch (Exception e) {
            throw e;
        } finally {
            after(baseEvent);
        }
    }

    protected Pair<List<String>, List<String>> removeSame(List<String> old, List<String> neo) {
        List<String> del = new ArrayList<>(old);
        List<String> add = new ArrayList<>(neo);

        neo.forEach(del::remove);
        old.forEach(add::remove);
        return Pair.of(add,del);
    }

    protected void pre(T baseEvent) {

    }

    protected void after(T baseEvent) {

    }

    public static Long getStart(ReserveRecord record) {
        return DateTimeUtils.localDateTime2Mills(
                getStartDateTime(record));
    }

    public static Long getEnd(ReserveRecord record) {
        return DateTimeUtils.localDateTime2Mills(getEndDateTime(record));
    }

    public static Long getWarn(ReserveRecord record) {
        return DateTimeUtils.localDateTime2Mills(getWarnDateTime(record)
        );
    }

    public static Boolean isDelay(ReserveRecord record) {
        return getEndDateTime(record).isBefore(DateTimeUtils.now());
    }

    public static LocalDateTime getStartDateTime(ReserveRecord record) {
        return record.getReserveStartTime()
                .minus((long)
                                (record.getConfig().getLockTableTiming() * TimeUnit.HOURS.toMinutes(1)),
                        ChronoUnit.MINUTES);
    }

    public static LocalDateTime getEndDateTime(ReserveRecord record) {
        return record.getReservesEndTime();
    }

    public static LocalDateTime getAcceptDelayDateTime(ReserveRecord record) {
        return record.getGmtCreate().plusHours(2L);
    }

    public static LocalDateTime getPayTimeoutDateTime(ReserveRecord record) {
        return record.getGmtCreate().plusMinutes(15L).plusSeconds(-1L);
    }

    public static LocalDateTime getWarnDateTime(ReserveRecord record) {
        return record.getReserveStartTime()
                .minus((long)
                                (record.getConfig().getSendWarnMessageTiming() * TimeUnit.HOURS.toMinutes(1)),
                        ChronoUnit.MINUTES);
    }
}