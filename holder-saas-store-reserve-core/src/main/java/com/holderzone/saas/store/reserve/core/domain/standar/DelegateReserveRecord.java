package com.holderzone.saas.store.reserve.core.domain.standar;

import com.holderzone.saas.store.reserve.core.domain.Item;
import com.holderzone.saas.store.reserve.core.domain.ReserveConfig;
import com.holderzone.saas.store.reserve.core.domain.ReserveRecord;
import com.holderzone.saas.store.reserve.core.domain.Table;
import lombok.Getter;
import lombok.Setter;

import java.util.Collection;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DelegateReserveRecord
 * @date 2019/04/23 14:31
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Setter
@Getter
public class DelegateReserveRecord extends EventReserveRecord {
    private Function<ReserveRecord, Collection<Item>> itemSupplier;
    private Function<ReserveRecord, Collection<Table>> tableFunction;
    private Function<ReserveRecord, ReserveConfig> configFunction;

    @Override
    public ReserveConfig getConfig() {
        return super.getConfig() == null?configFunction.apply(this):super.getConfig();
    }

    @Override
    public Collection<Table> getTables() {
        return super.getTables() == null?tableFunction.apply(this):super.getTables();
    }

    @Override
    public Collection<Item> getItems() {
        return super.getItems() == null?itemSupplier.apply(this):super.getItems();
    }
}