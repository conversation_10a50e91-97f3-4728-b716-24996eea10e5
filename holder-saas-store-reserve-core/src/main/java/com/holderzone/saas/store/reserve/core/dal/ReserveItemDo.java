package com.holderzone.saas.store.reserve.core.dal;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
@TableName("hss_reserve_item")
public class ReserveItemDo {
    @TableId
    private Long id;

    private String guid;

    private LocalDateTime gmtCreate;

    private LocalDateTime gmtModified;

    /**
     * 关联预订记录guid
     */
    private String recordGuid;

    /**
     * 商品guid
     */
    private String itemGuid;

    /**
     * 商品名称
     */
    private String itemName;

    /**
     * 商品编码
     */
    private String code;

    /**
     * 商品类型：1.套餐（不称重，无规格），2多规格商品（多商品，不称重），3.称重商品（单商品，称重），4.单品， 5.团餐
     */
    private Integer itemType;

    /**
     * 商品状态
     */
    private Integer itemState;

    /**
     * 商品分类guid
     */
    private String itemTypeGuid;

    /**
     * 商品分类名称
     */
    private String itemTypeName;

    /**
     * 规格guid
     */
    private String skuGuid;

    /**
     * 规格名称
     */
    private String skuName;

    /**
     * 商品单价
     */
    private BigDecimal price;

    /**
     * 商品数量
     */
    private Double num;

    /**
     * 商品小计（价格*数量）
     */
    private BigDecimal itemPrice;

    /**
     * 是否下单
     */
    private Integer isPay;

    /**
     * 父商品guid
     */
    private String parentGuid;

}