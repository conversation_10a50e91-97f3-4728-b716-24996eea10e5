package com.holderzone.saas.store.reserve.core.config;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Configuration
public class ThreadPoolConfig {

    @Bean(value = "launchAfterExecutor")
    public ExecutorService launchAfterExecutor() {
        return new ThreadPoolExecutor(5, 10, 30L, TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(500), new ThreadFactoryBuilder().setNameFormat("launch-after-pool-%d").build());
    }

    @Bean(value = "cancelAfterExecutor")
    public ExecutorService cancelAfterExecutor() {
        return new ThreadPoolExecutor(5, 10, 30L, TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(500), new ThreadFactoryBuilder().setNameFormat("cancel-after-pool-%d").build());
    }

    @Bean(value = "acceptAfterExecutor")
    public ExecutorService acceptAfterExecutor() {
        return new ThreadPoolExecutor(5, 10, 30L, TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(500), new ThreadFactoryBuilder().setNameFormat("accept-after-pool-%d").build());
    }

    @Bean(value = "payAfterExecutor")
    public ExecutorService payAfterExecutor() {
        return new ThreadPoolExecutor(5, 10, 30L, TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(500), new ThreadFactoryBuilder().setNameFormat("pay-after-pool-%d").build());
    }
}