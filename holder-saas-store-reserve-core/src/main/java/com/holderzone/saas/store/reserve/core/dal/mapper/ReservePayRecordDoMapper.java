package com.holderzone.saas.store.reserve.core.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.saas.store.dto.reserve.ReservePayDTO;
import com.holderzone.saas.store.reserve.core.dal.ReservePayRecordDO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReservePayRecordDoMapper
 * @date 2019/12/06 18:10
 * @description //TODO
 * @program IdeaProjects
 */
@Repository
public interface ReservePayRecordDoMapper extends BaseMapper<ReservePayRecordDO> {

     List<ReservePayDTO> getReserveStatistic(@Param("storeGuids")List<String> storeGuids, @Param("startTime")LocalDateTime startTime, @Param("endTime")LocalDateTime endTime);

}
