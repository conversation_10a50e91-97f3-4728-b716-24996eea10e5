package com.holderzone.saas.store.reserve.core.event.impl.handler;

import com.holderzone.framework.event.observer.CustomerObserver;
import com.holderzone.framework.rocketmq.common.DefaultRocketMqProducer;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.resource.common.dto.mq.UnMessage;
import com.holderzone.resource.common.util.MessageType;
import com.holderzone.saas.store.reserve.api.dto.ReserveRecordTransferDTO;
import com.holderzone.saas.store.reserve.core.common.RocketDelayLevelEnum;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.saas.store.reserve.core.dal.mapper.ReserveRecordDoMapper;
import com.holderzone.saas.store.reserve.core.domain.ReserveRecord;
import com.holderzone.saas.store.reserve.core.event.BaseEventHandler;
import com.holderzone.saas.store.reserve.core.event.impl.event.DelayEvent;
import com.holderzone.saas.store.reserve.core.rocket.DelayLockTableListener;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.ThreadFactory;

/**
 * <AUTHOR>
 * @version 1.0
 * @className LaunchEventHandler
 * @date 2019/04/23 17:34
 * @description //TODO
 * @program holder-saas-store-reserve
 */
public class ToDelayHandler extends BaseEventHandler<DelayEvent> implements CustomerObserver<DelayEvent> {
    public static final String LOCK_TAG="reserve-delay-lock-table-tag";
    public static final String BE_DELAY_TAG = "reserve-delay-be-delay-tag";
    public static final String ACCEPT_DELAY_TAG = "reserve-delay-accept-delay-tag";
    public static final String PAY_TIMEOUT_TAG = "reserve-delay-pay-timeout-tag";
    public static final String WARN_MESSAGE_TAG="reserve-delay-warn-tag";
    @Autowired
    ReserveRecordDoMapper reserveRecordDoMapper;
    @Autowired
    DefaultRocketMqProducer producer;
    @Autowired
    DelayLockTableListener lockTableListener;
    private static final ScheduledThreadPoolExecutor SCHEDULED_THREAD_POOL_EXECUTOR =  new ScheduledThreadPoolExecutor(1, new ThreadFactory() {
        @Override
        public Thread newThread(Runnable r) {
            return new Thread(r,"ToDelayHandler");
        }
    });
    @Override
    public void execute(DelayEvent baseEvent) {
        //预定许可
        ReserveRecord record = baseEvent.getReserveRecord();
        String tag = baseEvent.getTag();
        Long now = DateTimeUtils.localDateTime2Mills(LocalDateTime.now());
        //延时消息
        LocalDateTime dif = null;
        switch (tag){
            case LOCK_TAG:
                dif = getStartDateTime(record);
                break;
            case BE_DELAY_TAG:
                dif = getEndDateTime(record);
                break;
            case ACCEPT_DELAY_TAG:
                dif = getAcceptDelayDateTime(record);
                break;
            case PAY_TIMEOUT_TAG:
                dif = getPayTimeoutDateTime(record);
                break;
            case WARN_MESSAGE_TAG:
                dif = getWarnDateTime(record);
                break;
                default:
        }
        UnMessage<ReserveRecordTransferDTO> unMessage = new UnMessage<>();
        unMessage.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        unMessage.setStoreGuid(UserContextUtils.getStoreGuid());
        unMessage.setMessageType(MessageType.OTHER.code());
        unMessage.setMessage(new ReserveRecordTransferDTO(record.getGuid(),record.getReserveStartTime(),tag));
        MessageExt message = new MessageExt();
        message.setTopic("reserve-delay-lock-table-topic");
        message.setTags(baseEvent.getTag());
        message.setBody(JacksonUtils.toJsonByte(unMessage));
        RocketDelayLevelEnum levelEnum = RocketDelayLevelEnum.fetchMatched(dif);
        if(levelEnum.getLevel() < RocketDelayLevelEnum._5.getLevel()){
            SCHEDULED_THREAD_POOL_EXECUTOR.schedule(()->{
                lockTableListener.consumeMsg(unMessage,message);
            },levelEnum.getTime(),levelEnum.getTimeUnit());
            return;
        }
        message.setDelayTimeLevel(levelEnum.getLevel());
        producer.sendMessage(message);
    }

}