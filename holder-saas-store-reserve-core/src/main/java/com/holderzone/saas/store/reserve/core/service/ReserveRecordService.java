package com.holderzone.saas.store.reserve.core.service;

import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.business.manage.HandoverPayQueryDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.request.daily.DailyReqDTO;
import com.holderzone.saas.store.dto.order.response.daily.GatherRespDTO;
import com.holderzone.saas.store.dto.reserve.*;
import com.holderzone.saas.store.dto.table.TableOrderCombineDTO;
import com.holderzone.saas.store.dto.table.trade.TradeTableDTO;
import com.holderzone.saas.store.reserve.api.dto.*;
import com.holderzone.saas.store.reserve.core.domain.ReserveRecord;
import com.holderzone.saas.store.reserve.core.domain.ReserveRecordStateEnum;
import com.holderzone.saas.store.reserve.core.domain.Table;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReserveRecordService
 * @date 2019/04/23 15:29
 * @description //TODO
 * @program holder-saas-store-reserve
 */
public interface ReserveRecordService {
    ReserveRecord translate(ReserveRecordDTO dto);

    Collection<Table> getTables(ReserveRecord reserveRecord);

    Collection<Table> getTables(List<ReserveRecord> reserveRecords);

    ReserveRecordDetailDTO launch(ReserveRecordDTO dto,ReserveRecordStateEnum stateEnum);

    ReserveRecordDetailDTO modify(ReserveRecordDTO dto);

    ReserveRecordDetailDTO memberPay(ReserveAppletPayDTO payDTO);

    void aggPay(ReserveRecordGuidDTO guidDTO);

    ReserveRecordDetailDTO pass (ReserveRecordGuidDTO guidDTO);

    ReserveRecordDetailDTO cancle (ReserveRecordGuidDTO guidDTO);

    BigDecimal partRefund(ReserveRecordDTO dto);
    ReserveRecordDetailDTO open (ReserveRecordGuidDTO guidDTO);
    ReserveRecordDetailDTO compensate(CompensateDTO guidDTO);

    Collection<ReserveRecordLessDTO> query(ReserveRecordQueryDTO queryDTO);

    /**
     * 预定分页列表
     */
    Page<ReserveRecordAppletPageDTO> obtainRecordList(ReserveAppletQueryDTO queryDTO);

    ReserveRecordDetailDTO obtainDetail(ReserveRecordGuidDTO guidDTO);

    ReserveRecordDetailDTO obtainStaticsDetail(ReserveRecordGuidDTO guidDTO);
    StatisticsDTO statistics(PhoneDTO queryDTO);

    List<DineInItemDTO> getItems(ReserveRecordGuidDTO guidDTO);

    ReserveReportTotalDataDTO itemCount(ReserveReportParamDTO paramDTO);

    ReserveHandoverDTO handover(HandoverPayQueryDTO reservePayReqDTO);

    List<GatherRespDTO> gather(DailyReqDTO dailyReqDTO);

    ReserveRecordDetailDTO querySameTimeRecord(ReserveAppletQueryDTO queryDTO);

    ReserveRecordAppletPageDTO queryLastTimeRecord(ReserveAppletQueryDTO queryDTO);

    ReserveCommitStaticsDTO commitStatistics();

    ReserveCommitStaticsDTO commitStatisticsByDay();

    ReserveRecordDTO queryByOrderGuid(String orderGuid);

    void separate(TableOrderCombineDTO tableOrderCombineDTO);

    ReserveRecordDTO queryByGuid(String guid);

    void notifyTurn(TradeTableDTO tradeTableDTO);

    void notifyPay(NotifyPayReqDTO reqDTO);

    /**
     * 预订并台
     */
    void combine(TableOrderCombineDTO tableOrderCombineDTO);

    /**
     * 预付金反结账
     */
    void recovery(ReserveRecoveryDTO recoveryDTO);

    /**
     * 查询时间范围内待处理订单
     */
    ReserveCommitStaticsDTO commitStatisticsByScope(CommitStatisticsReqDTO commitStatisticsByScope);

    BigDecimal queryReserveAmount(SingleDataDTO query);

    List<TableDTO> queryReserveTable(SingleDataDTO query);

    void updateShareUrl(String guid, String shareUrl);

    /**
     * 根据订单打印预付金信息
     *
     * @param query 打印请求参数
     */
    void printByOrderGuid(SingleDataDTO query);
}