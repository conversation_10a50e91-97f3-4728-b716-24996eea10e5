package com.holderzone.saas.store.reserve.core.event.impl.handler;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CancelOrderReqDTO;
import com.holderzone.saas.store.dto.table.OpenTableDTO;
import com.holderzone.saas.store.dto.table.TableBasicQueryDTO;
import com.holderzone.saas.store.dto.table.TableCombineDTO;
import com.holderzone.saas.store.dto.table.TableOrderDTO;
import com.holderzone.saas.store.enums.table.TableStatusEnum;
import com.holderzone.saas.store.dto.reserve.TableDTO;
import com.holderzone.saas.store.reserve.api.dto.TableQueryResultDTO;
import com.holderzone.saas.store.reserve.core.dal.ReserveRecordDo;
import com.holderzone.saas.store.reserve.core.dal.mapper.ReserveRecordDoMapper;
import com.holderzone.saas.store.reserve.core.dal.mapper.ReserveRecordTableRelationDoMapper;
import com.holderzone.saas.store.reserve.core.domain.ReserveRecord;
import com.holderzone.saas.store.reserve.core.domain.ReserveRecordStateEnum;
import com.holderzone.saas.store.reserve.core.event.BaseEventHandler;
import com.holderzone.saas.store.reserve.core.event.impl.event.EffectiveEvent;
import com.holderzone.saas.store.reserve.core.lock.BatchRedisLock;
import com.holderzone.saas.store.reserve.core.lock.LockContext;
import com.holderzone.saas.store.reserve.core.lock.LockFailStrategy;
import com.holderzone.saas.store.reserve.core.lock.LockModeEnum;
import com.holderzone.saas.store.reserve.core.mapstruct.TableMapstruct;
import com.holderzone.saas.store.reserve.intergration.table.TableClientService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OpenTableEventHandler
 * @date 2019/05/30 15:25
 * @description //TODO
 * @program holder-saas-store-reserve
 */
public abstract class OpenTableEventHandler<T extends EffectiveEvent> extends BaseEventHandler<T>{
    @Autowired
    ReserveRecordDoMapper reserveRecordDoMapper;
    @Autowired
    ReserveRecordTableRelationDoMapper tableRelationDoMapper;
    @Autowired
    TableClientService tableServiceService;

    protected void doOpenTable(ReserveRecord record, List<TableDTO> tables, EffectiveEvent baseEvent){
        Map<String,TableDTO> tableRef = tables.stream().collect(Collectors.toMap(TableDTO::getGuid, Function.identity()));
        LockContext context = new LockContext(LockModeEnum.WHOLE, LockFailStrategy.FAIL_FAST);
        BatchRedisLock batchRedisLock = new BatchRedisLock(UserContextUtils.getUserGuid(),tableRef.keySet());
        batchRedisLock.lock(context);
        List<String> fails= Optional.ofNullable((List<String>)context.getOther().get(BatchRedisLock.RESULT_KEY))
                .orElse(Collections.emptyList());
        if(!fails.isEmpty()){
            throw new BusinessException("请稍候重试");
        }
        try {
            TableBasicQueryDTO query = new TableBasicQueryDTO();
            String storeGuid = UserContextUtils.getStoreGuid();
            query.setStoreGuid(storeGuid);
            query.setTableGuidList(new ArrayList<>(tableRef.keySet()));
            List<TableOrderDTO> tableOrderDTOS = tableServiceService.listByAndroid(query);
            List<TableQueryResultDTO> fail = tableOrderDTOS.stream().filter((e) ->
                    e.getStatus() == TableStatusEnum.FREE.getStatus().intValue()
                            || e.getStatus() == TableStatusEnum.RESERVATION_LOCK.getStatus().intValue()).map(TableMapstruct.TABLE_MAPSTRUCT::toQueryResult).collect(Collectors.toList());

            if (fail != null || fail.isEmpty()) {
                throw new BusinessException(fail.toString());
            }
            int mainIndex = 0;
            Integer peopleNum = record.getNumber();
            int tableNumber = peopleNum / tables.size();
            int mode = peopleNum % tables.size();

            List<OpenTableDTO> list = new ArrayList(tables);

            //开主桌
            TableDTO main = null;
            String orderGuid = null;
            for (; mainIndex < tables.size(); ) {
                main = tables.stream().filter((e) -> 1 == Optional.ofNullable(e.getState()).orElse(0)).findFirst().orElse(tables.get(mainIndex));
                tables.remove(main);
                OpenTableDTO openTableDTO = new OpenTableDTO();
                BeanUtils.copyProperties(baseEvent.getBaseDTO(), openTableDTO);
                openTableDTO.setTableCode(main.getName());
                openTableDTO.setTableGuid(main.getGuid());
                openTableDTO.setAreaName(main.getAreaName());
                openTableDTO.setActualGuestsNo(mode > 0 ? tableNumber + 1 : tableNumber);
                try {
                    orderGuid = tableServiceService.open(openTableDTO);
                    break;
                } catch (Exception e) {
                    throw new BusinessException(Arrays.asList(main).toString());
                }
            }
            mode -= 1;
            //并台
            if (!tables.isEmpty()) {
                List<String> tableGuids = new ArrayList<>(tables.size());
                List<String> orderGuids = new ArrayList<>(tables.size());
                for (int i = 0; i < tables.size(); i++) {
                    TableDTO e = tables.get(i);
                    OpenTableDTO dto = new OpenTableDTO();
                    BeanUtils.copyProperties(baseEvent.getBaseDTO(), dto);
                    dto.setTableCode(e.getName());
                    dto.setTableGuid(e.getGuid());
                    dto.setAreaName(e.getAreaName());
                    dto.setActualGuestsNo(mode - 1 < i ? tableNumber + 1 : tableNumber);
                    try {
                        String order = tableServiceService.open(dto);
                        tableGuids.add(e.getGuid());
                        orderGuids.add(order);
                    } catch (Exception a) {
                        closeTable(main.getGuid(),orderGuid,baseEvent.getBaseDTO());
                        closeTable(tableGuids,orderGuids,baseEvent.getBaseDTO());
                        throw new BusinessException(Arrays.asList(main).toString());
                    }
                }

                TableCombineDTO combineDTO = new TableCombineDTO();
                BeanUtils.copyProperties(baseEvent.getBaseDTO(), combineDTO);
                combineDTO.setMainOrderGuid(orderGuid);
                combineDTO.setMainTableGuid(main.getGuid());
                combineDTO.setTableGuidList(tableGuids);
                tableServiceService.combine(combineDTO);
            }
            //写库 记录 主订单guid
            ReserveRecordDo recordDo = new ReserveRecordDo();
            Boolean isDelay = isDelay(record);
            recordDo.setIsDelay(isDelay);
            recordDo.setState(ReserveRecordStateEnum.OPEN_TABLE.getCode());
            record.setState(recordDo.getState());
            recordDo.setGuid(record.getGuid());
            recordDo.setMainOrderGuid(orderGuid);
            recordDo.setModifiedStaffGuid(UserContextUtils.getUserGuid());
            recordDo.setGmtModified(LocalDateTime.now());
            reserveRecordDoMapper.update(recordDo, new LambdaQueryWrapper<ReserveRecordDo>().eq(ReserveRecordDo::getGuid, recordDo.getGuid()));
            //TODO 调用trade 服务开台
        }finally {
            batchRedisLock.unLock(context);
        }
    }
    private void closeTable(List<String> tableGuid,List<String> orderGuid,BaseDTO baseDTO){
        for(int i =0; i<tableGuid.size();i++){
            closeTable(tableGuid.get(i),orderGuid.get(i),baseDTO);
        }
    }

    private void closeTable(String tableGuid, String orderGuid, BaseDTO baseDTO){
        CancelOrderReqDTO cancelOrderReqDTO = new CancelOrderReqDTO();
        BeanUtils.copyProperties(baseDTO, cancelOrderReqDTO);
        cancelOrderReqDTO.setReason("预定失败");
        cancelOrderReqDTO.setTable(true);
        cancelOrderReqDTO.setFastFood(false);
        cancelOrderReqDTO.setOrderGuid(orderGuid);
        cancelOrderReqDTO.setTableGuid(tableGuid);
        tableServiceService.close(cancelOrderReqDTO);
    }
}