package com.holderzone.saas.store.reserve.core.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.order.common.SingleListDTO;
import com.holderzone.saas.store.dto.table.TableBasicDTO;
import com.holderzone.saas.store.dto.table.TableBasicQueryDTO;
import com.holderzone.saas.store.dto.table.TableOrderDTO;
import com.holderzone.saas.store.reserve.api.ReserveTableApi;
import com.holderzone.saas.store.reserve.api.dto.*;
import com.holderzone.saas.store.reserve.core.dal.ReserveRecordDo;
import com.holderzone.saas.store.reserve.core.dal.ReserveRecordTableRelationDo;
import com.holderzone.saas.store.reserve.core.dal.TableTimeDo;
import com.holderzone.saas.store.reserve.core.dal.mapper.ReserveRecordDoMapper;
import com.holderzone.saas.store.reserve.core.dal.mapper.ReserveRecordTableRelationDoMapper;
import com.holderzone.saas.store.reserve.core.domain.ReserveConfig;
import com.holderzone.saas.store.reserve.core.domain.ReserveRecord;
import com.holderzone.saas.store.reserve.core.domain.Table;
import com.holderzone.saas.store.reserve.core.domain.vo.TimingSegmentVo;
import com.holderzone.saas.store.reserve.core.mapstruct.ReserveRecordMapstruct;
import com.holderzone.saas.store.reserve.core.mapstruct.TableMapstruct;
import com.holderzone.saas.store.reserve.core.service.PrivateRoomService;
import com.holderzone.saas.store.reserve.core.service.ReserveConfigService;
import com.holderzone.saas.store.reserve.intergration.table.TableClientService;
import com.holderzone.saas.store.reserve.intergration.table.TradeClientService;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collector;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReserveRecordControllerImpl
 * @date 2019/04/27 14:06
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Slf4j
@Primary
@RestController
public class ReserveTableControllerImpl implements ReserveTableApi {

    @Autowired
    private ReserveConfigService reserveConfigService;

    @Autowired
    private TableClientService tableClientService;

    @Autowired
    private PrivateRoomService privateRoomService;

    @Autowired
    private ReserveRecordTableRelationDoMapper tableRelationDoMapper;

    @Autowired
    private ReserveRecordDoMapper reserveRecordDoMapper;

    @Autowired
    private TradeClientService tradeClientService;

    @Override
    public Collection<TableQueryResultDTO> query(@RequestBody TableQueryDTO queryDTO) {
        try {
            // 查询区域对应的桌台
            List<TableOrderDTO> tables = queryTables(queryDTO);
            // 查询是否编辑预定单
            ReserveRecord reserveRecord = queryReserveRecord(queryDTO);
            // 查询 对应门店、对应区域、对应时间段 的 桌台预定信息
            List<TableTimeDo> tableTimeDOList = tableRelationDoMapper.queryTablesByReserveStartTime(
                    UserContextUtils.getStoreGuid(), queryDTO.getRecordGuid(), queryDTO.getAreaGuid(), queryDTO.getTime());
            // 相同时间的桌台的guid
            List<String> sameTimeTables = tableTimeDOList.stream()
                    .map(ReserveRecordTableRelationDo::getTableGuid)
                    .collect(Collectors.toList());
            List<String> ownTable = Optional.ofNullable(reserveRecord)
                    .map(e -> e.getTables().stream().map(Table::getGuid).collect(Collectors.toList()))
                    .orElse(Collections.emptyList());
            // 去除所有状态为 预定锁定和预定保留的桌台
            List<TableOrderDTO> afterFilter = tables.stream()
                    .filter(e -> ownTable.contains(e.getTableGuid()) || !sameTimeTables.contains(e.getTableGuid()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(afterFilter)) {
                return Lists.newArrayList();
            }
            // 如果预定的时间 已不足后台设定的锁定时间, 则需要过滤已开台的桌台
            ReserveConfig reserveConfig = reserveConfigService.obtainDomain(UserContextUtils.getStoreGuid());
            LocalDateTime lockTableTime = queryDTO.getTime()
                    .plusMinutes(-(long) (reserveConfig.getLockTableTiming() * TimeUnit.HOURS.toMinutes(1)));
            if (!lockTableTime.isAfter(LocalDateTime.now())) {
                afterFilter = afterFilter.stream()
                        .filter(e -> (e.getStatus() == 0 || e.getStatus() == 2))
                        .collect(Collectors.toList());
            }
            Collection<TableQueryResultDTO> result = TableMapstruct.TABLE_MAPSTRUCT.toQueryResults(afterFilter);
            if (CollectionUtils.isEmpty(result)) {
                return result;
            }
            Map<String, List<TableTimeDo>> ref = tableTimeDOList.stream()
                    .collect(Collectors.groupingBy(ReserveRecordTableRelationDo::getTableGuid));
            // 打tag
            result.forEach(e -> {
                List<TableTimeDo> his = ref.get(e.getGuid());
                Optional.ofNullable(his).ifPresent(d -> d.forEach(timeDo -> {
                    e.setStatus("2");
                    if (e.getTag() == null) {
                        e.setTag(new ArrayList<>());
                    }
                    e.getTag().add(DateTimeUtils.localDateTime2String(timeDo.getReserveStartTime(), "MM:dd HH:mm"));
                }));
            });
            return TableMapstruct.TABLE_MAPSTRUCT.toQueryResults(afterFilter);
        } catch (Exception e) {
            log.error("query table fail:", e);
            throw e;
        }
    }

    /**
     * 查询门店桌台列表
     */
    private List<TableOrderDTO> queryTables(TableQueryDTO queryDTO) {
        TableBasicQueryDTO query = new TableBasicQueryDTO();
        query.setStoreGuid(UserContextUtils.getStoreGuid());
        query.setAreaGuid(queryDTO.getAreaGuid());
        // 查询区域对应的桌台
        return tableClientService.listByAndroid(query);
    }

    /**
     * 查询预定单记录
     */
    private ReserveRecord queryReserveRecord(TableQueryDTO queryDTO) {
        // 查询是否编辑预定单
        ReserveRecord reserveRecord = null;
        if (StringUtils.hasText(queryDTO.getRecordGuid())) {
            ReserveRecordDo recordDo = reserveRecordDoMapper.selectOne(
                    new LambdaQueryWrapper<ReserveRecordDo>().eq(ReserveRecordDo::getGuid, queryDTO.getRecordGuid())
            );
            reserveRecord = ReserveRecordMapstruct.MAPSTRUCT.toDomain(recordDo);
        }
        return reserveRecord;
    }


    @Override
    public List<TableReserveRecordRef> query(@RequestBody TableGuidsDTO guidsDTO) {
        List<ReserveRecordTableRelationDo> refs = tableRelationDoMapper.queryReserveGuidByTableAndState(guidsDTO.getTableGuids(), 0);
        List<ReserveRecordTableRelationDo> reservePayList = tableRelationDoMapper.queryReserveGuidByTableAndState(guidsDTO.getTableGuids(), 1);
        Collection<String> reserveGuids = refs.stream()
                .map(ReserveRecordTableRelationDo::getReserveRecordGuid)
                .collect(Collectors.toSet());
        Collection<String> reserveGuidList = reservePayList.stream()
                .map(ReserveRecordTableRelationDo::getReserveRecordGuid)
                .collect(Collectors.toSet());
        Map<String, List<String>> ref = reservePayList.stream().collect(
                Collectors.groupingBy(
                        ReserveRecordTableRelationDo::getReserveRecordGuid,
                        Collector.of(
                                ArrayList::new,
                                (e, a) -> e.add(a.getTableGuid()),
                                (e, a) -> e
                        )
                )
        );
        if (CollectionUtils.isEmpty(reservePayList)) {
            return Collections.emptyList();
        }
        List<ReserveRecordDo> reserveRecordDoList = reserveRecordDoMapper.selectList(
                new LambdaQueryWrapper<ReserveRecordDo>().in(ReserveRecordDo::getGuid, reserveGuidList)
        );
        List<String> mainOrderGuidList = reserveRecordDoList.stream()
                .map(ReserveRecordDo::getMainOrderGuid)
                .distinct()
                .collect(Collectors.toList());
        SingleListDTO listDTO = new SingleListDTO();
        listDTO.setList(mainOrderGuidList);
        List<String> orderGuidList = tradeClientService.queryReadyOrder(listDTO);
        reserveRecordDoList.removeIf(r -> !reserveGuids.contains(r.getGuid()) && !orderGuidList.contains(r.getMainOrderGuid()));

        Map<String, ReserveRecordLessDTO> lessDTOS = reserveRecordDoList.stream()
                .map(ReserveRecordMapstruct.MAPSTRUCT::toDomain)
                .map(ReserveRecordMapstruct.MAPSTRUCT::toLessDTO)
                .collect(
                        Collectors.toMap(ReserveRecordLessDTO::getGuid, Function.identity())
                );

        List<TableReserveRecordRef> recordRefList = ref.entrySet().stream().flatMap((e) -> {
            String reserveGuid = e.getKey();
            ReserveRecordLessDTO dto = lessDTOS.get(reserveGuid);
            if (dto != null) {
                return e.getValue().stream().map(a -> new TableReserveRecordRef(a, dto));
            } else {
                return new ArrayList<TableReserveRecordRef>(0).stream();
            }
        }).collect(Collectors.toList());

        return handleDuplicateData(recordRefList);
    }

    /**
     * 处理重复数据
     */
    @NotNull
    private List<TableReserveRecordRef> handleDuplicateData(List<TableReserveRecordRef> recordRefList) {
        List<TableReserveRecordRef> responseList = new ArrayList<>();
        Map<String, List<TableReserveRecordRef>> groupingByTableGuidMap = recordRefList.stream()
                .collect(Collectors.groupingBy(TableReserveRecordRef::getTableGuid));
        groupingByTableGuidMap.forEach((tableGuid, duplicateDTOList) -> {
            if (duplicateDTOList.size() > 1) {
                List<ReserveRecordLessDTO> lessDTOList = duplicateDTOList.stream()
                        .map(TableReserveRecordRef::getLessDTO)
                        .sorted(Comparator.comparing(ReserveRecordLessDTO::getGuid).reversed())
                        .collect(Collectors.toList());
                responseList.add(new TableReserveRecordRef(tableGuid, lessDTOList.get(0)));
                return;
            }
            responseList.add(duplicateDTOList.get(0));
        });
        return responseList;
    }

    @Override
    public VoiceQueryResultDTO queryDate(@RequestBody VoiceQueryDTO voiceQueryDTO) {
        String storeGuid = voiceQueryDTO.getStoreGuid();
        LocalDate reserveDate = Objects.requireNonNull(voiceQueryDTO.getReserveDate());
        Optional<Integer> tableType = Optional.ofNullable(voiceQueryDTO.getRoomType());
        Optional<Integer> peopleTotal = Optional.ofNullable(voiceQueryDTO.getPeopleTotal());
        LocalDateTime start = reserveDate.atStartOfDay();
        LocalDateTime end = DateTimeUtils.endTimeOfDay(start);
        ReserveConfig reserveConfig = reserveConfigService.obtainDomain(storeGuid);
        List<TableOrderDTO> tables = getTableOrderBoundary(storeGuid, tableType, reserveConfig);
        if (CollectionUtils.isEmpty(tables)) {
            return VoiceQueryResultDTO.unavailableInterval();
        }
        Map<LocalTime, List<TableTimeDo>> recordsMap = getReservedRecordsMap(storeGuid, start, end);
        List<TimingSegmentVo> segments = getTimingSegmentConfig(storeGuid);
        if (CollectionUtils.isEmpty(segments)) {
            return VoiceQueryResultDTO.unavailableInterval();
        }
        List<TimingSegmentVo> segmentsAvailable = filterAvailableSegment(reserveDate, peopleTotal, tables, recordsMap, segments);
        List<TimingSegmentVo> subSegmentAvailable = segmentsAvailable.stream()
                .flatMap(timingSegmentVo -> timingSegmentVo.getSub().stream()).collect(Collectors.toList());
        LocalDateTime nowDateTime = LocalDateTime.now();
        boolean isNoonAvailable = subSegmentAvailable.stream()
                .anyMatch(timingSegmentVo ->
                        timingSegmentVo.getStart().compareTo(LocalTime.of(15, 0)) < 0
                                && LocalDateTime.of(reserveDate, timingSegmentVo.getStart()).compareTo(nowDateTime) >= 0
                );
        boolean isNightAvailable = subSegmentAvailable.stream()
                .anyMatch(timingSegmentVo ->
                        timingSegmentVo.getStart().compareTo(LocalTime.of(15, 0)) >= 0
                                && LocalDateTime.of(reserveDate, timingSegmentVo.getStart()).compareTo(nowDateTime) >= 0
                );
        return VoiceQueryResultDTO.availableInterval(isNoonAvailable, isNightAvailable);
    }

    @Override
    public VoiceQueryResultDTO queryInterval(@RequestBody VoiceQueryDTO voiceQueryDTO) {
        String storeGuid = voiceQueryDTO.getStoreGuid();
        LocalDate reserveDate = Objects.requireNonNull(voiceQueryDTO.getReserveDate());
        Integer reserveInterval = Objects.requireNonNull(voiceQueryDTO.getReserveInterval());
        Optional<Integer> tableType = Optional.ofNullable(voiceQueryDTO.getRoomType());
        Optional<Integer> peopleTotal = Optional.ofNullable(voiceQueryDTO.getPeopleTotal());
        LocalDateTime start = reserveDate.atStartOfDay();
        LocalDateTime end = DateTimeUtils.endTimeOfDay(start);
        ReserveConfig reserveConfig = reserveConfigService.obtainDomain(storeGuid);
        List<TableOrderDTO> tables = getTableOrderBoundary(storeGuid, tableType, reserveConfig);
        if (CollectionUtils.isEmpty(tables)) {
            return VoiceQueryResultDTO.unavailableSegments();
        }
        Map<LocalTime, List<TableTimeDo>> recordsMap = getReservedRecordsMap(storeGuid, start, end);
        List<TimingSegmentVo> segments = getTimingSegmentConfig(storeGuid);
        if (CollectionUtils.isEmpty(segments)) {
            return VoiceQueryResultDTO.unavailableSegments();
        }
        List<TimingSegmentVo> segmentsAvailable = filterAvailableSegment(
                reserveDate, peopleTotal, tables, recordsMap, segments);
        LocalTime boundary = LocalTime.of(15, 0);
        List<TimingSegmentVo> subSegmentsAvailable = segmentsAvailable.stream()
                .flatMap(segment -> segment.getSub().stream())
                .collect(Collectors.toList());
        LocalDateTime nowDateTime = LocalDateTime.now();
        List<TimingSegmentDTO> result = subSegmentsAvailable.stream()
                .filter(timingSegmentVo -> {
                    if (reserveInterval > 0) {
                        return timingSegmentVo.getStart().compareTo(boundary) >= 0;
                    }
                    return timingSegmentVo.getStart().compareTo(boundary) < 0;
                })
                .filter(timingSegmentVo ->
                        LocalDateTime.of(reserveDate, timingSegmentVo.getStart()).compareTo(nowDateTime) >= 0)
                .map(timingSegmentVo -> {
                    TimingSegmentDTO timingSegmentDTO = new TimingSegmentDTO();
                    timingSegmentDTO.setStart(timingSegmentVo.getStart());
                    timingSegmentDTO.setEnd(timingSegmentVo.getEnd());
                    timingSegmentDTO.setPeriod(timingSegmentVo.getPeriod());
                    return timingSegmentDTO;
                })
                .collect(Collectors.toList());
        return VoiceQueryResultDTO.availableSegments(result);
    }

    @Override
    public VoiceQueryResultDTO queryDateTime(@RequestBody VoiceQueryDTO voiceQueryDTO) {
        String storeGuid = voiceQueryDTO.getStoreGuid();
        LocalDateTime reserveTime = Objects.requireNonNull(voiceQueryDTO.getReserveTime());
        Optional<Integer> tableType = Optional.ofNullable(voiceQueryDTO.getRoomType());
        Optional<Integer> peopleTotal = Optional.ofNullable(voiceQueryDTO.getPeopleTotal());
        LocalDate reserveDate = reserveTime.toLocalDate();
        LocalDateTime start = reserveDate.atStartOfDay();
        LocalDateTime end = DateTimeUtils.endTimeOfDay(start);
        ReserveConfig reserveConfig = reserveConfigService.obtainDomain(storeGuid);
        List<TableOrderDTO> tables = getTableOrderBoundary(storeGuid, tableType, reserveConfig);
        if (CollectionUtils.isEmpty(tables)) {
            return VoiceQueryResultDTO.unavailableDateTime();
        }
        Map<LocalTime, List<TableTimeDo>> recordsMap = getReservedRecordsMap(storeGuid, start, end);
        List<TimingSegmentVo> segments = getTimingSegmentConfig(storeGuid);
        if (CollectionUtils.isEmpty(segments)) {
            return VoiceQueryResultDTO.unavailableDateTime();
        }
        List<TimingSegmentVo> segmentsAvailable = filterAvailableSegment(
                reserveDate, peopleTotal, tables, recordsMap, segments);
        LocalTime boundary = reserveTime.toLocalTime();
        LocalDateTime nowDateTime = LocalDateTime.now();
        List<TimingSegmentVo> subSegmentsAvailable = segmentsAvailable.stream()
                .flatMap(segment -> segment.getSub().stream())
                .collect(Collectors.toList());
        boolean matched = subSegmentsAvailable.stream().anyMatch(timingSegmentVo ->
                timingSegmentVo.getStart().compareTo(boundary) <= 0
                        && timingSegmentVo.getEnd().compareTo(boundary) > 0
                        && LocalDateTime.of(reserveDate, timingSegmentVo.getStart()).compareTo(nowDateTime) >= 0);
        return matched ? VoiceQueryResultDTO.availableDateTime() : VoiceQueryResultDTO.unavailableDateTime();
    }

    @Override
    public VoiceQueryResultDTO queryPeople(@RequestBody VoiceQueryDTO voiceQueryDTO) {
        String storeGuid = voiceQueryDTO.getStoreGuid();
        LocalDateTime reserveTime = Objects.requireNonNull(voiceQueryDTO.getReserveTime());
        Optional<Integer> peopleTotal = Optional.of(Objects.requireNonNull(voiceQueryDTO.getPeopleTotal()));
        Optional<Integer> tableType = Optional.ofNullable(voiceQueryDTO.getRoomType());
        LocalDate reserveDate = reserveTime.toLocalDate();
        LocalDateTime start = reserveDate.atStartOfDay();
        LocalDateTime end = DateTimeUtils.endTimeOfDay(start);
        ReserveConfig reserveConfig = reserveConfigService.obtainDomain(storeGuid);
        List<TableOrderDTO> tables = getTableOrderBoundary(storeGuid, tableType, reserveConfig);
        if (CollectionUtils.isEmpty(tables)) {
            return VoiceQueryResultDTO.unavailablePeopleTotal();
        }
        Map<LocalTime, List<TableTimeDo>> recordsMap = getReservedRecordsMap(storeGuid, start, end);
        List<TimingSegmentVo> sub = getTimingSegmentConfig(storeGuid);
        if (CollectionUtils.isEmpty(sub)) {
            return VoiceQueryResultDTO.unavailablePeopleTotal();
        }
        List<TimingSegmentVo> segmentsAvailable = filterAvailableSegment(reserveDate, peopleTotal, tables, recordsMap, sub);
        LocalTime boundary = reserveTime.toLocalTime();
        List<TimingSegmentVo> subSegmentsAvailable = segmentsAvailable.stream()
                .flatMap(segment -> segment.getSub().stream())
                .collect(Collectors.toList());
        LocalDateTime nowDateTime = LocalDateTime.now();
        boolean matched = subSegmentsAvailable.stream().anyMatch(timingSegmentVo ->
                timingSegmentVo.getStart().compareTo(boundary) <= 0
                        && timingSegmentVo.getEnd().compareTo(boundary) > 0
                        && LocalDateTime.of(reserveDate, timingSegmentVo.getStart()).compareTo(nowDateTime) >= 0);
        return matched ? VoiceQueryResultDTO.availablePeopleTotal() : VoiceQueryResultDTO.unavailablePeopleTotal();
    }

    @Override
    public VoiceQueryResultDTO queryRoomType(@RequestBody VoiceQueryDTO voiceQueryDTO) {
        String storeGuid = voiceQueryDTO.getStoreGuid();
        LocalDateTime reserveTime = voiceQueryDTO.getReserveTime();
        Optional<Integer> tableType = Optional.empty();
        Optional<Integer> peopleTotal = Optional.ofNullable(voiceQueryDTO.getPeopleTotal());
        ReserveConfig reserveConfig = reserveConfigService.obtainDomain(storeGuid);
        if (reserveTime == null && voiceQueryDTO.getReserveDate() == null) {
            if (!reserveConfig.getIsEnablePrivateRoom()) {
                return VoiceQueryResultDTO.disabledTableType();
            } else {
                return VoiceQueryResultDTO.availableTableType();
            }
        }
        LocalDate reserveDate = reserveTime != null ? reserveTime.toLocalDate()
                : Objects.requireNonNull(voiceQueryDTO.getReserveDate());
        LocalDateTime start = reserveDate.atStartOfDay();
        LocalDateTime end = DateTimeUtils.endTimeOfDay(start);
        if (!reserveConfig.getIsEnablePrivateRoom()) {
            return VoiceQueryResultDTO.disabledTableType();
        }
        List<TableOrderDTO> tables = getTableOrderBoundary(storeGuid, tableType, reserveConfig);
        if (CollectionUtils.isEmpty(tables)) {
            return VoiceQueryResultDTO.unavailableTableType();
        }
        Map<LocalTime, List<TableTimeDo>> recordsMap = getReservedRecordsMap(storeGuid, start, end);
        List<TimingSegmentVo> sub = getTimingSegmentConfig(storeGuid);
        if (CollectionUtils.isEmpty(sub)) {
            return VoiceQueryResultDTO.unavailableTableType();
        }
        List<TimingSegmentVo> segmentsAvailable = filterAvailableSegment(
                reserveDate, peopleTotal, tables, recordsMap, sub);
        List<TimingSegmentVo> subSegmentsAvailable = segmentsAvailable.stream()
                .flatMap(segment -> segment.getSub().stream())
                .collect(Collectors.toList());
        LocalDateTime nowDateTime = LocalDateTime.now();
        if (reserveTime != null) {
            LocalTime boundary = reserveTime.toLocalTime();
            boolean matched = subSegmentsAvailable.stream().anyMatch(timingSegmentVo ->
                    timingSegmentVo.getStart().compareTo(boundary) <= 0
                            && timingSegmentVo.getEnd().compareTo(boundary) > 0
                            && LocalDateTime.of(reserveDate, timingSegmentVo.getStart()).compareTo(nowDateTime) >= 0);
            return matched ? VoiceQueryResultDTO.availableTableType() : VoiceQueryResultDTO.unavailableTableType();
        }
        Integer reserveInterval = voiceQueryDTO.getReserveInterval();
        if (reserveInterval != null) {
            LocalTime boundary = LocalTime.of(15, 0);
            List<TimingSegmentVo> result = subSegmentsAvailable.stream()
                    .filter(timingSegmentVo -> {
                        if (reserveInterval > 0) {
                            return timingSegmentVo.getStart().compareTo(boundary) >= 0;
                        }
                        return timingSegmentVo.getStart().compareTo(boundary) < 0;
                    })
                    .filter(timingSegmentVo ->
                            LocalDateTime.of(reserveDate, timingSegmentVo.getStart()).compareTo(nowDateTime) >= 0)
                    .collect(Collectors.toList());
            return result.size() > 0 ? VoiceQueryResultDTO.availableTableType() : VoiceQueryResultDTO.unavailableTableType();
        }
        return segmentsAvailable.size() > 0 ? VoiceQueryResultDTO.availableTableType() : VoiceQueryResultDTO.unavailableTableType();
    }

    @Override
    public VoiceQueryResultDTO queryAllCond(@RequestBody VoiceQueryDTO voiceQueryDTO) {
        String storeGuid = voiceQueryDTO.getStoreGuid();
        LocalDateTime reserveTime = Objects.requireNonNull(voiceQueryDTO.getReserveTime());
        Optional<Integer> peopleTotal = Optional.of(Objects.requireNonNull(voiceQueryDTO.getPeopleTotal()));
        Optional<Integer> tableType = Optional.of(Objects.requireNonNull(voiceQueryDTO.getRoomType()));
        LocalDate reserveDate = reserveTime.toLocalDate();
        LocalDateTime start = reserveDate.atStartOfDay();
        LocalDateTime end = DateTimeUtils.endTimeOfDay(start);
        ReserveConfig reserveConfig = reserveConfigService.obtainDomain(storeGuid);
        List<TableOrderDTO> tables = getTableOrderBoundary(storeGuid, tableType, reserveConfig);
        if (CollectionUtils.isEmpty(tables)) {
            return VoiceQueryResultDTO.unavailableReserve();
        }
        Map<LocalTime, List<TableTimeDo>> recordsMap = getReservedRecordsMap(storeGuid, start, end);
        List<TimingSegmentVo> sub = getTimingSegmentConfig(storeGuid);
        if (CollectionUtils.isEmpty(sub)) {
            return VoiceQueryResultDTO.unavailableReserve();
        }
        List<TimingSegmentVo> segmentsAvailable = filterAvailableSegment(
                reserveDate, peopleTotal, tables, recordsMap, sub
        );
        List<TimingSegmentVo> subSegmentsAvailable = segmentsAvailable.stream()
                .flatMap(segment -> segment.getSub().stream())
                .collect(Collectors.toList());
        LocalTime boundary = reserveTime.toLocalTime();
        LocalDateTime nowDateTime = LocalDateTime.now();
        boolean matched = subSegmentsAvailable.stream().anyMatch(timingSegmentVo ->
                timingSegmentVo.getStart().compareTo(boundary) <= 0
                        && timingSegmentVo.getEnd().compareTo(boundary) > 0
                        && LocalDateTime.of(reserveDate, timingSegmentVo.getStart()).compareTo(nowDateTime) >= 0);
        return matched ? VoiceQueryResultDTO.availableReserve() : VoiceQueryResultDTO.unavailableReserve();
    }

    @Override
    public VoiceQueryResultDTO fetchMatchedTable(@RequestBody VoiceQueryDTO voiceQueryDTO) {
        String storeGuid = voiceQueryDTO.getStoreGuid();
        LocalDateTime reserveTime = Objects.requireNonNull(voiceQueryDTO.getReserveTime());
        Optional<Integer> peopleTotal = Optional.of(Objects.requireNonNull(voiceQueryDTO.getPeopleTotal()));
        Optional<Integer> tableType = Optional.of(Objects.requireNonNull(voiceQueryDTO.getRoomType()));
        LocalDate reserveDate = reserveTime.toLocalDate();
        LocalDateTime start = reserveDate.atStartOfDay();
        LocalDateTime end = DateTimeUtils.endTimeOfDay(start);
        ReserveConfig reserveConfig = reserveConfigService.obtainDomain(storeGuid);
        List<TableOrderDTO> tables = getTableOrderBoundary(storeGuid, tableType, reserveConfig);
        if (CollectionUtils.isEmpty(tables)) {
            return VoiceQueryResultDTO.unavailableTable();
        }
        Map<LocalTime, List<TableTimeDo>> recordsMap = getReservedRecordsMap(storeGuid, start, end);
        List<TimingSegmentVo> segments = getTimingSegmentConfig(storeGuid);
        if (CollectionUtils.isEmpty(segments)) {
            return VoiceQueryResultDTO.unavailableTable();
        }
        List<TimingSegmentVo> segmentsAvailable = filterAvailableSegment(
                reserveDate, peopleTotal, tables, recordsMap, segments
        );
        LocalTime boundary = reserveTime.toLocalTime();
        List<TimingSegmentVo> subSegmentsAvailable = segmentsAvailable.stream()
                .flatMap(segment -> segment.getSub().stream())
                .collect(Collectors.toList());
        LocalDateTime nowDateTime = LocalDateTime.now();
        Optional<TimingSegmentVo> first = subSegmentsAvailable.stream()
                .filter(timingSegmentVo ->
                        timingSegmentVo.getStart().compareTo(boundary) <= 0
                                && timingSegmentVo.getEnd().compareTo(boundary) > 0
                )
                .filter(timingSegmentVo ->
                        LocalDateTime.of(reserveDate, timingSegmentVo.getStart()).compareTo(nowDateTime) >= 0)
                .findFirst();
        if (!first.isPresent()) {
            return VoiceQueryResultDTO.unavailableTable();
        }
        TimingSegmentVo timingSegmentVo = first.get();
        Optional<TimingSegmentVo> firstParent = segmentsAvailable.stream()
                .filter(segment -> segment.getStart().compareTo(timingSegmentVo.getStart()) <= 0
                        && segment.getEnd().compareTo(timingSegmentVo.getStart()) >= 0)
                .findFirst();
        if (!firstParent.isPresent()) {
            return VoiceQueryResultDTO.unavailableTable();
        }
        TimingSegmentVo parentSegment = firstParent.get();
        LocalDateTime now = LocalDateTime.now();
        List<TableOrderDTO> available = filterAvailableTableOfCurSegment(
                peopleTotal, reserveDate, tables, recordsMap, parentSegment, now);
        if (available.isEmpty()) {
            return VoiceQueryResultDTO.unavailableTable();
        }
        TableOrderDTO tableOrderDTO = fetchBestTable(available);
        TableBasicDTO tableBasicDTO = fetchBasicTable(storeGuid, tableOrderDTO.getTableGuid());
        fetchBasicTable(storeGuid, tableOrderDTO.getTableGuid());
        if (tableBasicDTO == null) {
            return VoiceQueryResultDTO.unavailableTable();
        }
        return VoiceQueryResultDTO.availableTable(tableBasicDTO);
    }

    @Override
    public void printPreOrdering(@RequestBody ReservePrintDTO reservePrintDTO) {
        log.info("打印入参:{}", JacksonUtils.writeValueAsString(reservePrintDTO));
        privateRoomService.print(reservePrintDTO);
    }

    private List<TableOrderDTO> filterAvailableTableOfCurSegment(Optional<Integer> peopleTotal,
                                                                 LocalDate reserveDate, List<TableOrderDTO> tables,
                                                                 Map<LocalTime, List<TableTimeDo>> recordsMap,
                                                                 TimingSegmentVo timingSegmentVo, LocalDateTime now) {
        List<TableOrderDTO> available = new ArrayList<>(tables);
        List<TableTimeDo> tableTimeDos1 = timingSegmentVo.getSub().stream()
                .flatMap(timingSegmentVo1 -> recordsMap.getOrDefault(
                        timingSegmentVo1.getStart(), Collections.emptyList()).stream())
                .collect(Collectors.toList());
        List<String> occupied = tableTimeDos1.stream()
                .map(ReserveRecordTableRelationDo::getTableGuid)
                .collect(Collectors.toList());
        if (reserveDate.atStartOfDay().compareTo(now) <= 0) {
            LocalDateTime start = LocalDateTime.of(reserveDate, timingSegmentVo.getStart());
            LocalDateTime end = LocalDateTime.of(reserveDate, timingSegmentVo.getEnd());
            if (start.isBefore(now) && end.isAfter(now)) {
                available.removeIf(table -> table.getStatus() != 0);
            }
        }
        available.removeIf(tableOrderDTO -> {
            if (occupied.contains(tableOrderDTO.getTableGuid())) {
                return true;
            }
            return peopleTotal.filter(integer -> tableOrderDTO.getSeats() < integer).isPresent();
        });
        return available;
    }

    private LocalTime segmentBegin(LocalDateTime reserveStartTime) {
        LocalTime time = reserveStartTime.toLocalTime();
        if (time.getMinute() < 30) {
            return time.withMinute(0);
        }
        return time.withMinute(30);
    }

    private List<TimingSegmentVo> filterAvailableSegment(LocalDate reserveDate,
                                                         Optional<Integer> peopleTotal,
                                                         List<TableOrderDTO> tables,
                                                         Map<LocalTime, List<TableTimeDo>> recordsMap,
                                                         List<TimingSegmentVo> segments) {
        List<TableOrderDTO> finalTables = new ArrayList<>(tables);
        LocalDateTime now = LocalDateTime.now();
        return segments.stream().filter(segment -> {
            List<TableOrderDTO> available = filterAvailableTableOfCurSegment(
                    peopleTotal, reserveDate,
                    finalTables, recordsMap, segment, now);
            return !available.isEmpty();
        }).collect(Collectors.toList());
    }

    private List<TimingSegmentVo> getTimingSegmentConfig(String storeGuid) {
        // 从数据库查询门店的Segment配置
        ReserveConfig config = reserveConfigService.obtainDomain(storeGuid);
        return new ArrayList<>(config.getSegments());
    }

    private Map<LocalTime, List<TableTimeDo>> getReservedRecordsMap(String storeGuid, LocalDateTime start, LocalDateTime end) {
        // 从数据库 查询 某天的所有预订记录
        List<TableTimeDo> tableTimeDos = tableRelationDoMapper.queryTablesByTimeSegment(
                storeGuid, null, null, start, end);
        return tableTimeDos.stream()
                .collect(Collectors.groupingBy(o -> segmentBegin(o.getReserveStartTime())));
    }

    private List<TableOrderDTO> getTableOrderBoundary(String storeGuid,
                                                      Optional<Integer> tableType,
                                                      ReserveConfig reserveConfig) {
        // 从数据库和桌台服务 查询 包房、非包房的桌台
        Boolean isEnablePrivateRoom = reserveConfig.getIsEnablePrivateRoom();
        List<TableOrderDTO> tables = Collections.emptyList();
        if (Optional.ofNullable(isEnablePrivateRoom).orElse(false)
                && tableType.map(aByte -> aByte > 0).orElse(false)) {
            List<String> tableGuidList = privateRoomService.query(storeGuid);
            if (!CollectionUtils.isEmpty(tableGuidList)) {
                tables = fetchTables(storeGuid, tableGuidList);
            }
        } else {
            tables = fetchTables(storeGuid, null);
        }
        return tables;
    }

    private List<TableOrderDTO> fetchTables(String storeGuid, List<String> tableGuidList) {
        TableBasicQueryDTO query = new TableBasicQueryDTO();
        query.setStoreGuid(storeGuid);
        query.setTableGuidList(tableGuidList);
        return tableClientService.listByAndroid(query);
    }

    private TableOrderDTO fetchBestTable(List<TableOrderDTO> available) {
        available.sort(Comparator.comparing(TableOrderDTO::getSeats));
        return available.get(0);
    }

    private TableBasicDTO fetchBasicTable(String storeGuid, String tableGuid) {
        TableBasicQueryDTO query = new TableBasicQueryDTO();
        query.setStoreGuid(storeGuid);
        query.setTableGuidList(Collections.singletonList(tableGuid));
        List<TableBasicDTO> tableBasicDTOS = tableClientService.listByWeb(query);
        if (CollectionUtils.isEmpty(tableBasicDTOS)) {
            return null;
        }
        return tableBasicDTOS.get(0);
    }
}