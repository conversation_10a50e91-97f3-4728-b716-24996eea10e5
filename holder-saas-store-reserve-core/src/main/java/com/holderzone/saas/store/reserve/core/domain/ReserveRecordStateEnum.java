package com.holderzone.saas.store.reserve.core.domain;

import com.google.common.collect.Lists;
import com.holderzone.saas.store.reserve.api.enums.AppletStateEnum;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ClientStateEnum
 * @date 2019/04/24 18:43
 * @description //TODO
 * @program holder-saas-store-reserve
 */
public enum  ReserveRecordStateEnum {
    /**
     * 32
     */
    COMMIT(0b100000,"提交"),
    /**
     * 35
     */
    CANCLE(0b100011,"取消"),
    /**
     * 39
     */
    NO_PAY(0b100111,"未付定金"),
    /**
     * 47
     */
    NO_PAY_CANCEL(0b101111,"支付失敗或者未付定金而取消"),
    /**
     * 3
     */
    SYSTEM_CANCLE(0b000011,"系统自动取消"),
    /**
     *38
     */
    PASS(0b100110,"通过"),
    /**
     * 62
     */
    OPEN_TABLE(0b111110,"到店开台"),
    /**
     *54
     */
    PICK_TABLE(0b110110,"到店选台"),
    /**
     * 56
     */
    FINISH(0b111000, "已结账"),
    ;
    private int code;
    private String message;

    ReserveRecordStateEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public static ReserveRecordStateEnum getByCode(Integer code){
        return Arrays.stream(values()).filter((e)->code==e.getCode()).findAny().get();
    }

    public static List<Integer> transferReserveRecordStateEnums(String state) {
        if (StringUtils.isEmpty(state)) {
            return Lists.newArrayList();
        }
        AppletStateEnum appletStateEnum = AppletStateEnum.valueOf(state);
        switch (appletStateEnum) {
            case ING:
                return Lists.newArrayList(COMMIT.getCode(), PASS.getCode());
            case COMPLETE:
                return Lists.newArrayList(OPEN_TABLE.getCode(), PICK_TABLE.getCode());
            case INVALID:
                return Lists.newArrayList(CANCLE.getCode(), SYSTEM_CANCLE.getCode(), NO_PAY_CANCEL.getCode());
            default:
                return Lists.newArrayList();
        }
    }

    public static List<Integer> getOpenedList() {
        return Lists.newArrayList(PASS.getCode(), OPEN_TABLE.getCode(), PICK_TABLE.getCode());
    }

}