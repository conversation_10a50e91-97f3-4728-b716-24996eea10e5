package com.holderzone.saas.store.reserve.core.event.impl.handler;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.anno.CustomerRegister;
import com.holderzone.framework.event.observer.CustomerObserver;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.redisson.sdk.lock.RedissonLockUtil;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.member.terminal.dto.order.RequestRefundPay;
import com.holderzone.saas.store.dto.order.ShopOrderIslandUserAmountDTO;
import com.holderzone.saas.store.dto.pay.AggRefundReqDTO;
import com.holderzone.saas.store.dto.pay.AggRefundRespDTO;
import com.holderzone.saas.store.dto.pay.SaasAggRefundDTO;
import com.holderzone.saas.store.dto.pay.constant.AggRefundStateEnum;
import com.holderzone.saas.store.enums.PaymentTypeEnum;
import com.holderzone.saas.store.enums.weixin.WxAppletMemberPayTypeEnum;
import com.holderzone.saas.store.reserve.api.enums.PayStateEnum;
import com.holderzone.saas.store.reserve.core.common.ReserveUtils;
import com.holderzone.saas.store.reserve.core.dal.ReservePayRecordDO;
import com.holderzone.saas.store.reserve.core.dal.ReserveRecordDo;
import com.holderzone.saas.store.reserve.core.dal.mapper.ReservePayRecordDoMapper;
import com.holderzone.saas.store.reserve.core.dal.mapper.ReserveRecordDoMapper;
import com.holderzone.saas.store.reserve.core.domain.ReserveRecord;
import com.holderzone.saas.store.reserve.core.domain.ReserveRecordStateEnum;
import com.holderzone.saas.store.reserve.core.event.BaseEventHandler;
import com.holderzone.saas.store.reserve.core.event.impl.event.PartRefundEvent;
import com.holderzone.saas.store.reserve.core.service.ReservePayRecordService;
import com.holderzone.saas.store.reserve.intergration.table.AggPayClientService;
import com.holderzone.saas.store.reserve.intergration.table.MemberTerminalClientService;
import com.holderzone.saas.store.reserve.intergration.table.TcdClientService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.Objects;


/**
 * 部分退款
 */
@Slf4j
@Component
@RequiredArgsConstructor
@CustomerRegister(isRegister = true)
public class PartRefundEventHandler extends BaseEventHandler<PartRefundEvent> implements CustomerObserver<PartRefundEvent> {

    private final ReservePayRecordService reservePayRecordService;

    private final MemberTerminalClientService memberTerminalClientService;

    private final TcdClientService tcdClientService;

    private final AggPayClientService aggPayClientService;

    private final ReserveRecordDoMapper reserveRecordDoMapper;

    private final ReservePayRecordDoMapper reservePayRecordDoMapper;

    @Override
    public void execute(PartRefundEvent baseEvent) {
        ReserveRecord reserveRecord = baseEvent.getReserveRecord();
        // put
        UserContext userContext = UserContextUtils.get();
        if (StringUtils.isEmpty(userContext.getStoreGuid())) {
            userContext.setStoreGuid(reserveRecord.getStoreGuid());
            UserContextUtils.put(userContext);
        }
        // 取消预定单 退预定金处理
        cancelReserveAmountHandler(baseEvent);
    }


    /**
     * 取消预定单 退预定金处理
     */
    private void cancelReserveAmountHandler(PartRefundEvent baseEvent) {
        ReserveRecord reserveRecord = baseEvent.getReserveRecord();
        // 有支付预订金时，增加一条预订金退款记录
        if (Objects.isNull(reserveRecord.getReserveAmount()) || reserveRecord.getReserveAmount().compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }
        if (Objects.equals(ReserveRecordStateEnum.NO_PAY_CANCEL.getCode(), reserveRecord.getState())) {
            // 未支付定金
            return;
        }
        // 原路退回
        refundForBacktrack(reserveRecord);
    }

    /**
     * 小程序下的预定单
     * 原路返回
     */
    private void refundForBacktrack(ReserveRecord reserveRecord) {
        log.info("[原路返回]reserveGuid={}", reserveRecord.getGuid());
        ReservePayRecordDO reservePayRecordDO = reservePayRecordService.selectOneByReserveGuid(reserveRecord.getGuid());
        if (Objects.isNull(reservePayRecordDO)) {
            log.info("支付记录不存在, reserveGuid:{}", reserveRecord.getGuid());
            return;
        }
        Integer payType = reservePayRecordDO.getPayType();
        String payTypeName = reservePayRecordDO.getPayTypeName();
        if (PaymentTypeEnum.AGG.getCode() == payType) {
            // 微信支付退款
            wechatRefund(reserveRecord, reservePayRecordDO);
        } else if (PaymentTypeEnum.MEMBER.getCode() == payType) {
            // 会员储值余额退款
            memberStoredBalanceRefund(reserveRecord, reservePayRecordDO);
        } else if (PaymentTypeEnum.OTHER.getCode() == payType && WxAppletMemberPayTypeEnum.INCOME_AMOUNT.getMessage().equals(payTypeName)) {
            // 会员收益余额退款
            memberIncomeBalanceRefund(reserveRecord);
        }
        // 更新退款金额
        ReserveRecordDo recordDo = new ReserveRecordDo();
        recordDo.setRefundAmount(reserveRecord.getReserveRefundAmount());
        reserveRecordDoMapper.update(recordDo, new LambdaQueryWrapper<ReserveRecordDo>()
                .eq(ReserveRecordDo::getGuid, reserveRecord.getGuid())
        );
        ReservePayRecordDO payRecordDO = new ReservePayRecordDO();
        BeanUtils.copyProperties(reservePayRecordDO, payRecordDO);
        payRecordDO.setGuid(ReserveUtils.reserveGuid());
        payRecordDO.setReserveAmount(reserveRecord.getReserveRefundAmount().negate());
        payRecordDO.setState(PayStateEnum.REFOUNDED.getCode());
        payRecordDO.setGmtCreate(LocalDateTime.now());
        payRecordDO.setGmtModified(LocalDateTime.now());
        reservePayRecordDoMapper.insert(payRecordDO);
    }

    /**
     * 会员收益余额退款
     */
    private void memberIncomeBalanceRefund(ReserveRecord reserveRecord) {
        try {
            ShopOrderIslandUserAmountDTO shopOrderIslandUserAmountDTO = new ShopOrderIslandUserAmountDTO();
            shopOrderIslandUserAmountDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
            shopOrderIslandUserAmountDTO.setOperSubjectGuid(reserveRecord.getOperSubjectGuid());
            shopOrderIslandUserAmountDTO.setMemberInfoCardGuid(reserveRecord.getMemberInfoCardGuid());
            shopOrderIslandUserAmountDTO.setMemberInfoGuid(reserveRecord.getMemberInfoGuid());
            shopOrderIslandUserAmountDTO.setStoreGuid(reserveRecord.getStoreGuid());
            shopOrderIslandUserAmountDTO.setAmount(reserveRecord.getReserveRefundAmount());
            shopOrderIslandUserAmountDTO.setOrderNo(reserveRecord.getOrderNo());
            shopOrderIslandUserAmountDTO.setOrderGuid(reserveRecord.getGuid());
            shopOrderIslandUserAmountDTO.setWeAppUserId(Long.valueOf(reserveRecord.getCreateStaffGuid()));
            log.info("用户收益余额部分退款请求参数:{}", JacksonUtils.writeValueAsString(shopOrderIslandUserAmountDTO));
            Map<String, Object> resultMap = tcdClientService.islandUserAmountRefund(shopOrderIslandUserAmountDTO);
            log.info("用户收益余额部分退款返回参数:{}", resultMap);
        } catch (Exception e) {
            log.error("用户收益余额部分退款失败,e:{}", e.getMessage());
            throw new BusinessException("用户收益余额部分退款失败");
        }
    }

    /**
     * 微信支付退款
     */
    private void wechatRefund(ReserveRecord reserveRecord, ReservePayRecordDO reservePayRecordDO) {
        // 构建退款请求参数
        AggRefundReqDTO aggRefundReqDTO = new AggRefundReqDTO();
        aggRefundReqDTO.setOrderGUID(reserveRecord.getGuid());
        aggRefundReqDTO.setRefundType(1);
        aggRefundReqDTO.setRefundFee(reserveRecord.getReserveRefundAmount().abs());
        aggRefundReqDTO.setReason("聚合支付部分退款");
        aggRefundReqDTO.setPayGUID(String.valueOf(reservePayRecordDO.getGuid()));
        SaasAggRefundDTO saasAggRefundDTO = new SaasAggRefundDTO();
        saasAggRefundDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        saasAggRefundDTO.setStoreGuid(reserveRecord.getStoreGuid());
        saasAggRefundDTO.setAggRefundReqDTO(aggRefundReqDTO);
        AggRefundRespDTO refund = aggPayClientService.refund(saasAggRefundDTO);
        if (refundFailure(refund)) {
            log.warn("预定单微信部分退款失败,orderGuid:{},payGuid:{},amount{},code{},message{}", reserveRecord.getGuid(),
                    reservePayRecordDO.getGuid(), aggRefundReqDTO.getRefundType(), refund.getCode(), refund.getMsg());
            throw new BusinessException(refund.getMsg());
        } else {
            log.warn("预定单微信部分退款,orderGuid:{},payGuid:{},amount{},code{},message{}", reserveRecord.getGuid(),
                    reservePayRecordDO.getGuid(), aggRefundReqDTO.getRefundFee(), refund.getCode(),
                    refund.getMsg());
        }
    }

    private boolean refundFailure(AggRefundRespDTO refund) {
        return !("10000".equals(refund.getCode()) || "20045".equals(refund.getCode()))
                || refund.getMsg().contains("失败")
                || refund.getState().equals(AggRefundStateEnum.REFUND_FAILURE.getState());
    }


    /**
     * 会员储值余额退款
     */
    private void memberStoredBalanceRefund(ReserveRecord reserveRecord, ReservePayRecordDO reservePayRecordDO) {
        RequestRefundPay requestRefundPay = new RequestRefundPay();
        requestRefundPay.setMemberFundingDetailGuid(reservePayRecordDO.getMemberFundingDetailGuid());
        requestRefundPay.setRefundAmount(reserveRecord.getReserveRefundAmount());
        memberTerminalClientService.reserveRefundPay(requestRefundPay);
    }

    @Override
    protected void pre(PartRefundEvent baseEvent) {
        ReserveRecord reserveRecord = baseEvent.getReserveRecord();
        boolean result = RedissonLockUtil.tryLock(reserveRecord.getGuid(), 3, 10);
        if (!result) {
            throw new BusinessException("当前预定记录正被其他人操作,请稍候重试!!");
        }
        super.pre(baseEvent);
    }

    @Override
    protected void after(PartRefundEvent baseEvent) {
        ReserveRecord reserveRecord = baseEvent.getReserveRecord();
        RedissonLockUtil.unlock(reserveRecord.getGuid());
        super.after(baseEvent);
    }
}