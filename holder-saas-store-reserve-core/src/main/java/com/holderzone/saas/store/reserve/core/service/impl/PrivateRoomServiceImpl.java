package com.holderzone.saas.store.reserve.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.saas.store.reserve.api.dto.ReservePrintDTO;
import com.holderzone.saas.store.reserve.core.common.ReserveUtils;
import com.holderzone.saas.store.reserve.core.dal.PrivateRoomDo;
import com.holderzone.saas.store.reserve.core.dal.mapper.PrivateRoomDoMapper;
import com.holderzone.saas.store.reserve.core.service.PrintService;
import com.holderzone.saas.store.reserve.core.service.PrivateRoomService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class PrivateRoomServiceImpl extends ServiceImpl<PrivateRoomDoMapper, PrivateRoomDo> implements PrivateRoomService {

    private final PrintService printService;

    public PrivateRoomServiceImpl(PrintService printService) {
        this.printService = printService;

    }

    @Override
    public List<String> query(String storeGuid) {
        List<PrivateRoomDo> list = list(new LambdaQueryWrapper<PrivateRoomDo>()
                .eq(PrivateRoomDo::getStoreGuid, storeGuid));
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list.stream().map(PrivateRoomDo::getTableGuid).collect(Collectors.toList());
    }

    @Override
    public void save(String storeGuid, List<String> tableGuids) {
        remove(new LambdaQueryWrapper<PrivateRoomDo>().eq(PrivateRoomDo::getStoreGuid, storeGuid));
        if (!CollectionUtils.isEmpty(tableGuids)) {
            List<PrivateRoomDo> collect = tableGuids.stream()
                    .map(tableGuid -> {
                        PrivateRoomDo privateRoomDo = new PrivateRoomDo();
                        privateRoomDo.setGuid(ReserveUtils.privateRoomGuid());
                        privateRoomDo.setStoreGuid(storeGuid);
                        privateRoomDo.setTableGuid(tableGuid);
                        return privateRoomDo;
                    }).collect(Collectors.toList());
            saveBatch(collect);
        }
    }

    @Override
    public void print(ReservePrintDTO reservePrintDTO) {
        printService.printPreOrdering(reservePrintDTO);
    }
}
