package com.holderzone.saas.store.reserve.core.event.impl.event;

import com.holderzone.framework.base.dto.message.ShortMessageType;
import com.holderzone.saas.store.reserve.core.domain.ReserveRecord;
import com.holderzone.saas.store.reserve.core.event.BaseEvent;
import lombok.Getter;

import java.util.Map;
import java.util.function.Function;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @version 1.0
 * @className LaunchEvent
 * @date 2019/04/23 17:24
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Getter
public class ShortMessageEvent extends BaseEvent {
    private Function<ReserveRecord, Map<String, String>> messageFunction;
    private Supplier<ShortMessageType> messageTypeSupplier;

    public ShortMessageEvent(ReserveRecord reserveRecord, Function<ReserveRecord, Map<String, String>> messageFunction, Supplier<ShortMessageType> messageTypeSupplier) {
        super(reserveRecord);
        this.messageFunction = messageFunction;
        this.messageTypeSupplier = messageTypeSupplier;
    }

    private static final String NAME= ShortMessageEvent.class.getSimpleName();

    @Override
    public String getName() {
        return NAME;
    }
}