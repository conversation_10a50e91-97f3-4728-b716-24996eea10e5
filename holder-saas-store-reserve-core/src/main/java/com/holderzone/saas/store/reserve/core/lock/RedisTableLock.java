package com.holderzone.saas.store.reserve.core.lock;

import com.holderzone.framework.exception.unchecked.BusinessException;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 1.0
 * @className RedisTableLock
 * @date 2019/04/24 14:30
 * @description //TODO
 * @program holder-saas-store-reserve
 */
public class RedisTableLock extends TableLock{
    protected static final String PREFIX="redis:table:lock:";

    private static final String U_SCRIPT = "local segment = ARGV[1];  " +
            "local owner = ARGV[2];  " +
            "local segmentKey = KEYS[1]..':segment';  " +
            "local modeKey = KEYS[1]..':mode';  " +
            "local ownerKey = KEYS[1]..':owner';  " +
            "local redisOwner = redis.call('HGET',segmentKey,segment);  " +
            "if(redisOwner == owner) then  " +
            "  redis.call('HDEL',segmentKey,segment);   " +
            "  local size = redis.call('HLEN',segmentKey); " +
            "  if(size == 0) then  " +
            "    redis.call('del',modeKey);  " +
            "    redis.call('del',ownerKey);  " +
            "  end  " +
            "  return 1;  " +
            "end  " +
            "return 0; ";
    private static final String L_SCRIPT = "local segment = ARGV[1]; " +
            "local owner = ARGV[2]; " +
            "local segmentKey = KEYS[1]..':segment'; " +
            "local modeKey = KEYS[1]..':mode'; " +
            "local ownerKey = KEYS[1]..':owner'; " +
            "local currentSegment = {};  " +
            "string.gsub(segment,'[^-]+',function ( w )  " +
            "    table.insert(currentSegment,w)  " +
            "end); " +
            "local modeExist = redis.call('EXISTS',modeKey); " +
            "local success = 1; " +
            "if(modeExist == 0) then  " +
            "  redis.call('set',ownerKey,1); " +
            "  redis.call('set',modeKey,1); " +
            "  success = 1; " +
            "else " +
            "  local mode = redis.call('get',modeKey); " +
            "  if(mode == 0) then  " +
            "    success = 0; " +
            "  else " +
            "    local data = redis.call('HGETALL',segmentKey);  " +
            "    for i, segment in ipairs(data) do  " +
            "      if(i % 2 == 1) then " +
            "        local redisOwner = data[i+1]; " +
            "        local old = {}  " +
            "        string.gsub(segment,'[^-]+',function ( w )  " +
            "            table.insert(old,w)  " +
            "        end);  " +
            "        if(old[1]==currentSegment[1] and old[2]==currentSegment[2]) then   " +
            "          success = 0;  " +
            "          break; " +
            "        end  " +
            "        if(old[1]<currentSegment[1] and old[2]>=currentSegment[1]) then   " +
            "          success = 0;  " +
            "          break; " +
            "        end  " +
            "        if(old[1]<=currentSegment[2] and old[2]>currentSegment[2]) then   " +
            "          success = 0;  " +
            "          break; " +
            "        end  " +
            "      end " +
            "    end  " +
            "  end  " +
            "end " +
            "if(success == 1) then   " +
            "  redis.call('hset',segmentKey,segment,owner);  " +
            "end  " +
            "return success;";
    private static final DefaultRedisScript LOCK_SCRIPT = new DefaultRedisScript(L_SCRIPT,Integer.class);
    private static final DefaultRedisScript UNLOCK_SCRIPT = new DefaultRedisScript(U_SCRIPT,Integer.class);
    public static RedisTemplate redisTemplate;

    public RedisTableLock(String tableGuid, String owner) {
        super(tableGuid, owner);
    }

    @Override
    public void addSegment(String segment) {
        Integer result = (Integer) redisTemplate.execute(LOCK_SCRIPT, Arrays.asList(PREFIX+getTableGuid()),getSegment(),getOwner());
        if(result != 1){
            throw new BusinessException("lock fail");
        }
    }

    @Override
    public void tryWholeLock() {
        redisTemplate.opsForValue().setIfAbsent(PREFIX+getTableGuid()+":owner",getOwner());
        redisTemplate.opsForValue().set(PREFIX+getTableGuid()+":mode",LockModeEnum.WHOLE.ordinal());
    }

    @Override
    public void removeSegment(String segment) {
        redisTemplate.execute(UNLOCK_SCRIPT, Arrays.asList(PREFIX+getTableGuid()),getSegment(),getOwner());
    }

    @Override
    public void removeAllSegment() {
        redisTemplate.delete(PREFIX+getTableGuid()+":segment");
        redisTemplate.delete(PREFIX+getTableGuid()+":mode");
        redisTemplate.delete(PREFIX+getTableGuid()+":owner");
    }

    @Override
    public void tryWholeUnLock() {
        String owner = (String) redisTemplate.opsForValue().get(PREFIX+getTableGuid()+":owner");
        if(getOwner().equals(owner)){
            redisTemplate.delete(PREFIX+getTableGuid()+":mode");
            redisTemplate.delete(PREFIX+getTableGuid()+":owner");
        }
    }

    @Override
    public void lock(LockContext context) {
        switch (context.getLockModeEnum()) {
            case WHOLE:
                redisTemplate.opsForValue().setIfAbsent(PREFIX+getTableGuid()+":mode",LockModeEnum.WHOLE.ordinal());
                break;
            case SEGMENT:
                redisTemplate.execute(LOCK_SCRIPT, Arrays.asList(PREFIX+getTableGuid()),context.getSegment());
                break;
            default:
                throw new UnsupportedOperationException();
        }
    }

    @Override
    public void unLock(LockContext context) {
        switch (context.getLockModeEnum()) {
            case WHOLE:
                redisTemplate.delete(PREFIX+getTableGuid()+":mode");
                break;
            case SEGMENT:
                redisTemplate.execute(UNLOCK_SCRIPT, Arrays.asList(PREFIX+getTableGuid()),context.getSegment());
                break;
            default:
                throw new UnsupportedOperationException();
        }
    }
}