package com.holderzone.saas.store.reserve.core.lock;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TableLock
 * @date 2019/04/24 12:00
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Data
public abstract class TableLock {
    private String tableGuid;
    private String mode;
    private String owner;
    private List<String> segment;

    public TableLock(String tableGuid, String owner) {
        this.tableGuid = tableGuid;
        this.owner = owner;
    }


    public abstract void addSegment(String segment);
    public abstract void removeSegment(String segment);

    public abstract void removeAllSegment();
    public abstract void tryWholeLock();

    public abstract void tryWholeUnLock();




    public abstract void lock(LockContext context);

    public  void lock(){
         this.lock(new LockContext(LockModeEnum.WHOLE));
    }
    public abstract void unLock(LockContext context);

    public  void unLock(){
         this.lock(new LockContext(LockModeEnum.WHOLE));
    }
}