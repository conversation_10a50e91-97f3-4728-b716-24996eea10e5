package com.holderzone.saas.store.reserve.core.event.impl.handler;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.anno.CustomerRegister;
import com.holderzone.framework.base.dto.message.ShortMessageType;
import com.holderzone.framework.event.CustomerEvent;
import com.holderzone.framework.event.observer.CustomerObserver;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.redisson.sdk.lock.RedissonLockUtil;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.message.BusinessMessageDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.takeaway.OrderType;
import com.holderzone.saas.store.enums.BaseDeviceTypeEnum;
import com.holderzone.saas.store.enums.msg.BusinessMsgTypeEnum;
import com.holderzone.saas.store.reserve.api.dto.ReserveSystemMsgDTO;
import com.holderzone.saas.store.reserve.core.common.ReserveConstant;
import com.holderzone.saas.store.reserve.core.common.ReserveExceptionEnum;
import com.holderzone.saas.store.reserve.core.dal.ReserveRecordDo;
import com.holderzone.saas.store.reserve.core.domain.ReserveRecord;
import com.holderzone.saas.store.reserve.core.domain.ReserveRecordStateEnum;
import com.holderzone.saas.store.reserve.core.domain.Table;
import com.holderzone.saas.store.reserve.core.event.impl.event.*;
import com.holderzone.saas.store.reserve.core.mapstruct.ReserveRecordMapstruct;
import com.holderzone.saas.store.reserve.core.rocket.Consume;
import com.holderzone.saas.store.reserve.intergration.table.MessageClientService;
import com.holderzone.saas.store.reserve.intergration.table.OrganizationClientService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className LaunchEventHandler
 * @date 2019/04/23 17:34
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Slf4j
@Component
@CustomerRegister(isRegister = true)
public class ModifyEventHandler extends SaveEventHanlder<ModifyEvent> implements CustomerObserver<ModifyEvent> {
    @Autowired
    OrganizationClientService organizationClientService;

    @Autowired
    private MessageClientService messageClientService;

    @Override
    public void execute(ModifyEvent baseEvent) {
        ReserveRecord reserveRecord = baseEvent.getReserveRecord();
        ReserveRecordDo db = reserveRecordDoMapper.selectOne(
                new LambdaQueryWrapper<ReserveRecordDo>().eq(ReserveRecordDo::getGuid, reserveRecord.getGuid())
        );
        /*
        if (Objects.equals(db.getDeviceType(), BaseDeviceTypeEnum.TCD.getCode())) {
            throw new BusinessException(ReserveExceptionEnum.APPLET_RESERVE_RECORD_NOT_UPDATE.getMsg());
        }*/
        ReserveRecord dbDomain = ReserveRecordMapstruct.MAPSTRUCT.toDomain(db);

        ReserveRecordDo d = ReserveRecordMapstruct.MAPSTRUCT.toDo(reserveRecord);
        d.setModifiedStaffGuid(UserContextUtils.getUserGuid());
        d.setModifiedStaffName(UserContextUtils.getUserName());
        d.setDeviceType(db.getDeviceType());
        d.setGmtModified(LocalDateTime.now());

        reserveRecord.setDeviceType(db.getDeviceType());
        reserveRecord.setIsDelay(dbDomain.getIsDelay());
        reserveRecord.setModifiedStaffGuid(d.getModifiedStaffGuid());
        reserveRecord.setModifiedStaffName(d.getModifiedStaffName());
        reserveRecord.setGmtModified(d.getGmtModified());
        reserveRecord.setConfirmTime(db.getConfirmTime());
        reserveRecord.setConfirmUserName(db.getConfirmUserName());
        reserveRecord.setPaymentTypeName(db.getPaymentTypeName());
        reserveRecord.setPaymentTime(db.getPaymentTime());

        LocalDateTime now = LocalDateTime.now().truncatedTo(ChronoUnit.MINUTES);
        if (!dbDomain.getReserveStartTime().equals(reserveRecord.getReserveStartTime()) && reserveRecord.getReserveStartTime().isBefore(now)) {
            throw new BusinessException(ReserveExceptionEnum.LAUNCH_OVERTIME.getMsg());
        }
        if (!dbDomain.getReserveStartTime().isEqual(reserveRecord.getReserveStartTime())) {
            d.setIsDelay(false);
            reserveRecord.setIsDelay(false);
        }
        reserveRecord.setState(dbDomain.getState());
        reserveRecordDoMapper.update(d, new LambdaQueryWrapper<ReserveRecordDo>().eq(ReserveRecordDo::getGuid, reserveRecord.getGuid()));

        //删除原预订关联的商品并插入新的
        reserveItemService.deleteReserveItems(reserveRecord.getGuid());
        reserveItemService.saveReserveItems(reserveRecord);

        List<String> old = dbDomain.getTables().stream().map(Table::getGuid).collect(Collectors.toList());
        List<String> neo = reserveRecord.getTables().stream().map(Table::getGuid).collect(Collectors.toList());

        if (reserveRecord.getState() == ReserveRecordStateEnum.COMMIT.getCode()) {
            if (Boolean.TRUE.equals(baseEvent.getSaveTable()
                    && (!new HashSet<>(old).containsAll(neo))
                    || !new HashSet<>(neo).containsAll(old))) {
                saveTable(reserveRecord);
            } else if (Boolean.FALSE.equals(baseEvent.getSaveTable())) {
                tableRelationDoMapper.deleteByReserveGuid(reserveRecord.getGuid());
            }
            return;
        }
        // 修改预定 后置处理
        afterEventHandler(baseEvent, db, dbDomain);
    }

    /**
     * 修改预定 后置处理
     */
    private void afterEventHandler(ModifyEvent baseEvent, ReserveRecordDo db, ReserveRecord dbDomain) {
        ReserveRecord reserveRecord = baseEvent.getReserveRecord();
        List<String> old = dbDomain.getTables().stream().map(Table::getGuid).collect(Collectors.toList());
        List<String> neo = reserveRecord.getTables().stream().map(Table::getGuid).collect(Collectors.toList());
        boolean unlocked = false;
        boolean locked = false;
        boolean messaged = false;
        if (Boolean.TRUE.equals(baseEvent.getSaveTable()) &&
                (!new HashSet<>(old).containsAll(neo) || !new HashSet<>(neo).containsAll(old))) {
            if (Boolean.TRUE.equals(db.getIsLocked())) {
                UnLockTableEvent unLockTableEvent = new UnLockTableEvent(dbDomain);
                customerPublish.publish(new CustomerEvent<>(unLockTableEvent));
                unlocked = true;
            }
            saveTable(reserveRecord);
            PreparedLockTableEvent preparedLockTableEvent = new PreparedLockTableEvent(reserveRecord, old, neo);
            customerPublish.publish(new CustomerEvent<>(preparedLockTableEvent));
            if (Boolean.TRUE.equals(db.getIsLocked())) {
                DelayEvent delayEvent = new DelayEvent(reserveRecord, Consume.LOCK_TAG);
                customerPublish.publish(new CustomerEvent<>(delayEvent));
                locked = true;
            }
        } else if (Boolean.FALSE.equals(baseEvent.getSaveTable())) {
            if (Boolean.TRUE.equals(db.getIsLocked())) {
                UnLockTableEvent unLockTableEvent = new UnLockTableEvent(dbDomain);
                customerPublish.publish(new CustomerEvent<>(unLockTableEvent));
                unlocked = true;
            } else {
                PreparedLockTableEvent preparedLockTableEvent = new PreparedLockTableEvent(reserveRecord, old, neo);
                customerPublish.publish(new CustomerEvent<>(preparedLockTableEvent));
            }
            tableRelationDoMapper.deleteByReserveGuid(reserveRecord.getGuid());
            if (Boolean.TRUE.equals(db.getIsLocked())) {
                DelayEvent delayEvent = new DelayEvent(reserveRecord, Consume.LOCK_TAG);
                customerPublish.publish(new CustomerEvent<>(delayEvent));
                locked = true;
            }
        }
        if (!dbDomain.getCustomer().getPhone().equals(reserveRecord.getCustomer().getPhone())) {
            messaged = publishMessageEvent(reserveRecord, false);
        }
        if (!dbDomain.getReserveStartTime().isEqual(reserveRecord.getReserveStartTime())) {
            // 修改预定时间 后置处理
            modifyReserveStartTimeAfterHandler(baseEvent, db, dbDomain, unlocked, locked, messaged);
        }
        // 发送消息到pos
        sendMsgByPos(reserveRecord);
    }


    /**
     * 修改预定时间 后置处理
     */
    private void modifyReserveStartTimeAfterHandler(ModifyEvent baseEvent, ReserveRecordDo db, ReserveRecord dbDomain,
                                                    boolean unlocked, boolean locked, boolean messaged) {
        ReserveRecord reserveRecord = baseEvent.getReserveRecord();
        List<String> neo = reserveRecord.getTables().stream().map(Table::getGuid).collect(Collectors.toList());
        if (Boolean.TRUE.equals(db.getIsLocked())) {
            PreparedLockTableEvent preparedLockTableEvent = new PreparedLockTableEvent(reserveRecord, Collections.emptyList(), neo);
            customerPublish.publish(new CustomerEvent<>(preparedLockTableEvent));
        }
        publishMessageEvent(reserveRecord, messaged);
        if (Boolean.TRUE.equals(reserveRecord.getConfig().getIsEnableWarnMessage())) {
            DelayEvent warnEvent = new DelayEvent(reserveRecord, Consume.WARN_MESSAGE_TAG);
            customerPublish.publish(new CustomerEvent<>(warnEvent));
        }
        if (!unlocked) {
            UnLockTableEvent unLockTableEvent = new UnLockTableEvent(dbDomain);
            customerPublish.publish(new CustomerEvent<>(unLockTableEvent));
        }
        if (!locked) {
            DelayEvent delayEvent = new DelayEvent(reserveRecord, Consume.LOCK_TAG);
            customerPublish.publish(new CustomerEvent<>(delayEvent));
        }
        //自动取消
        DelayEvent cancelEvent = new DelayEvent(reserveRecord, Consume.BE_DELAY_TAG);
        customerPublish.publish(new CustomerEvent<>(cancelEvent));
    }

    /**
     * 发送消息到pos
     */
    private void sendMsgByPos(ReserveRecord reserveRecord) {
        if (BaseDeviceTypeEnum.isApplet(reserveRecord.getDeviceType())) {
            return;
        }
        // 查询门店名称
        StoreDTO storeDTO = organizationClientService.queryStoreByGuid(reserveRecord.getStoreGuid());
        BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setMessageType(BusinessMsgTypeEnum.TABLE_STATE_MSG_TYPE.getId());
        businessMessageDTO.setPlatform(String.valueOf(OrderType.OTHER_ORDER.getType()));
        businessMessageDTO.setStoreGuid(reserveRecord.getStoreGuid());
        businessMessageDTO.setStoreName(storeDTO.getName());
        businessMessageDTO.setDetailMessageType(BusinessMsgTypeEnum.RESERVE_RECORD_STATE_CHANGE_POS.getId());
        businessMessageDTO.setSubject("");
        ReserveSystemMsgDTO reserveSystemMsgDTO = new ReserveSystemMsgDTO();
        reserveSystemMsgDTO.setGuid(reserveRecord.getGuid());
        reserveSystemMsgDTO.setAutoRev(0);
        businessMessageDTO.setContent(JacksonUtils.writeValueAsString(reserveSystemMsgDTO));
        businessMessageDTO.setCreateTime(LocalDateTime.now());
        businessMessageDTO.setPushTime(LocalDateTime.now());
        log.info("[ModifyEvent][POS]预定消息推送入参:{}", JacksonUtils.writeValueAsString(businessMessageDTO));
        messageClientService.msg(businessMessageDTO);
    }

    /**
     * 发送预定短信通知
     */
    private boolean publishMessageEvent(ReserveRecord reserveRecord, boolean messaged) {
        if (Boolean.TRUE.equals(reserveRecord.getConfig().getIsEnableSuccessMessage()) && !messaged) {
            ShortMessageEvent warnShortMessageEvent = new ShortMessageEvent(reserveRecord, e -> {
                Map<String, String> params = new HashMap<>();
                // 门店名称(区域名称-桌台名称) 如果没有桌台，则只展示门店名称
                String storeNameAndAreaName = getStoreNameAndTableName(reserveRecord);
                params.put("storeName", storeNameAndAreaName);
                params.put("reserveT", e.getReserveStartTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")));
                params.put("totalPeople", e.getNumber() + "");
                return params;
            }, () -> ShortMessageType.RESERVE_NOTIFY);
            customerPublish.publish(new CustomerEvent<>(warnShortMessageEvent));
            return true;
        }
        return false;
    }

    @Override
    protected void pre(ModifyEvent baseEvent) {
        ReserveRecord reserveRecord = baseEvent.getReserveRecord();
        boolean result = RedissonLockUtil.tryLock(reserveRecord.getGuid(), 3, 10);
        if (!result) {
            throw new BusinessException("当前预定记录正被其他人操作,请稍候重试!!");
        }
        super.pre(baseEvent);
    }

    @Override
    protected void after(ModifyEvent baseEvent) {
        ReserveRecord reserveRecord = baseEvent.getReserveRecord();
        RedissonLockUtil.unlock(reserveRecord.getGuid());
        super.after(baseEvent);
    }

    /**
     * 获取门店名称和桌台名称
     */
    private String getStoreNameAndTableName(ReserveRecord modifyReserveRecord) {
        StoreDTO storeDTO = organizationClientService.queryStoreByGuid(modifyReserveRecord.getStoreGuid());
        String name = storeDTO.getName();
        Collection<Table> tables = modifyReserveRecord.getTables();
        if (CollectionUtils.isEmpty(tables)) {
            return name;
        }
        List<String> tableNameList = tables.stream()
                .map(e -> e.getAreaName() + "-" + e.getName())
                .collect(Collectors.toList());
        return name + "(" + generateTableName(tableNameList) + ")";
    }

    /**
     * 构建桌台列表
     */
    private String generateTableName(List<String> tableNameList) {
        int originalSize = tableNameList.size();
        StringBuilder modifyTableStr = new StringBuilder();
        if (tableNameList.size() >= ReserveConstant.RESERVE_SMS_MULTI_TABLE_COUNT) {
            tableNameList = tableNameList.subList(0, ReserveConstant.RESERVE_SMS_MULTI_TABLE_COUNT);
        }
        for (int i = 0; i < tableNameList.size(); i++) {
            modifyTableStr.append(tableNameList.get(i));
            if (i != tableNameList.size() - 1) {
                modifyTableStr.append("、");
            }
        }
        if (originalSize >= ReserveConstant.RESERVE_SMS_MULTI_TABLE_COUNT) {
            modifyTableStr.append(String.format(ReserveConstant.RESERVE_SMS_MULTI_TABLE_TEMPLATE, originalSize));
        }
        return modifyTableStr.toString();
    }
}