package com.holderzone.saas.store.reserve.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.common.PackageSubgroupDTO;
import com.holderzone.saas.store.dto.order.common.SubDineInItemDTO;
import com.holderzone.saas.store.reserve.core.common.ReserveUtils;
import com.holderzone.saas.store.reserve.core.dal.ReserveItemDo;
import com.holderzone.saas.store.reserve.core.dal.mapper.ReserveItemDOMapper;
import com.holderzone.saas.store.reserve.core.domain.Item;
import com.holderzone.saas.store.reserve.core.domain.ReserveRecord;
import com.holderzone.saas.store.reserve.core.mapstruct.ReserveItemMapstruct;
import com.holderzone.saas.store.reserve.core.service.ReserveItemService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

@Service
public class ReserveItemServiceImpl implements ReserveItemService {

    @Autowired
    protected ReserveItemDOMapper reserveItemDOMapper;

    @Autowired
    private ReserveItemMapstruct reserveItemMapstruct;

    @Override
    public void saveReserveItems(ReserveRecord record) {
        Collection<Item> items = record.getItems();
        //声明ReserveItem集合，后面批量保存
        List<ReserveItemDo> itemDoList = new ArrayList<>();
        //桌位数（总份数需要乘以桌位数）
        int tableNum = record.getTables().size();
        //若未选择桌台，则默认1
        if (tableNum == 0) {
            tableNum = 1;
        }
        if (CollectionUtils.isNotEmpty(items)) {
            for (Item item : items) {
                DineInItemDTO itemDTO = (DineInItemDTO) item.getDetail();
                ReserveItemDo itemDo = reserveItemMapstruct.dine2ReserveItem(itemDTO);
                itemDo.setGuid(ReserveUtils.reserveGuid()).setRecordGuid(record.getGuid())
                        .setNum(itemDTO.getCurrentCount().doubleValue() * tableNum)
                        .setItemPrice(itemDTO.getItemPrice().multiply(new BigDecimal(tableNum)));
                if (StringUtils.isNotEmpty(itemDo.getSkuName())) {
                    //如果有规格将规格加在名称后
                    itemDo.setItemName(itemDo.getItemName() + "(" + itemDo.getSkuName() + ")");
                }
                itemDoList.add(itemDo);
                //如果是套餐或宴会餐，需保存子商品
                List<PackageSubgroupDTO> subgroupDTOList = itemDTO.getPackageSubgroupDTOS();
                if (CollectionUtils.isNotEmpty(subgroupDTOList)) {
                    for (PackageSubgroupDTO subgroupDTO : subgroupDTOList) {
                        List<SubDineInItemDTO> subItems = subgroupDTO.getSubDineInItemDTOS();
                        if (CollectionUtils.isNotEmpty(subItems)) {
                            for (SubDineInItemDTO subItemDTO : subItems) {
                                ReserveItemDo subItemDo = reserveItemMapstruct.subdine2ReserveItem(subItemDTO);
                                subItemDo.setGuid(ReserveUtils.reserveGuid()).setRecordGuid(record.getGuid())
                                        .setParentGuid(itemDo.getGuid()).setNum(subItemDTO.getPackageDefaultCount().doubleValue() * tableNum)
                                        .setItemPrice(subItemDTO.getPrice().multiply(subItemDTO.getPackageDefaultCount()).multiply(new BigDecimal(tableNum)));
                                if (StringUtils.isNotEmpty(subItemDo.getSkuName())) {
                                    //如果有规格将规格加在名称后
                                    subItemDo.setItemName(subItemDo.getItemName() + "(" + subItemDo.getSkuName() + ")");
                                }
                                itemDoList.add(subItemDo);
                            }
                        }
                    }
                }
            }
        }
        //插入数据
        itemDoList.forEach(item -> reserveItemDOMapper.insert(item));
    }

    @Override
    public void deleteReserveItems(String recordGuid) {
        reserveItemDOMapper.delete(
                new LambdaQueryWrapper<ReserveItemDo>().eq(ReserveItemDo::getRecordGuid, recordGuid));
    }
}
