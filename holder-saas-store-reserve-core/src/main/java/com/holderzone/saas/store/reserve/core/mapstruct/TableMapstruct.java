package com.holderzone.saas.store.reserve.core.mapstruct;

import com.holderzone.saas.store.dto.table.TableOrderDTO;
import com.holderzone.saas.store.dto.reserve.TableDTO;
import com.holderzone.saas.store.reserve.api.dto.TableQueryResultDTO;
import com.holderzone.saas.store.reserve.core.dal.ReserveRecordTableRelationDo;
import com.holderzone.saas.store.reserve.core.domain.Table;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.Collection;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReserveRecordMapstruct
 * @date 2019/04/23 18:09
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Mapper
public interface TableMapstruct {
    TableMapstruct TABLE_MAPSTRUCT = Mappers.getMapper(TableMapstruct.class);

    @Mappings({
            @Mapping(source = "table.guid",target = "tableGuid"),
            @Mapping(source = "table.name",target = "tableName"),
            @Mapping(source = "reserveGuid",target = "reserveRecordGuid"),

    })
    ReserveRecordTableRelationDo toDo(Table table,String reserveGuid);

    @Mappings({
            @Mapping(target = "guid",source = "tableGuid"),
            @Mapping(target = "name",source = "tableName")

    })
    Table toDomain(ReserveRecordTableRelationDo table);

    Collection<Table> toDomains(Collection<ReserveRecordTableRelationDo> table);
    @Mappings({
            @Mapping(target = "guid",source = "tableGuid"),
            @Mapping(target = "name",source = "tableName")

    })
    TableDTO dotoDto(ReserveRecordTableRelationDo table);


    TableDTO domaintoDto(Table table);

    Collection<Table> tableToDomain(Collection<TableDTO> tables);


    @Mappings({
            @Mapping(target = "tableGuid",source = "guid"),
            @Mapping(target = "tableName",source = "name")
    })
    ReserveRecordTableRelationDo dtotoDo(TableDTO table);
    @Mappings({
            @Mapping(target = "guid",source = "tableGuid"),
            @Mapping(target = "name",source = "tableCode"),
            @Mapping(target = "status",expression = "java(null)")

    })
    TableQueryResultDTO toQueryResult(TableOrderDTO dto);
    Collection<TableQueryResultDTO> toQueryResults(Collection<TableOrderDTO> dto);
    Table dtotoDomain(TableDTO table);
}