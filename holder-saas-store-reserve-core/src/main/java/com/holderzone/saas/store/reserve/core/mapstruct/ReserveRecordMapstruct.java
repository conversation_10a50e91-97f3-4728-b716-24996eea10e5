package com.holderzone.saas.store.reserve.core.mapstruct;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.reserve.api.dto.*;
import com.holderzone.saas.store.reserve.api.enums.ClientStateEnum;
import com.holderzone.saas.store.reserve.core.common.SpringContextUtils;
import com.holderzone.saas.store.reserve.core.dal.ReserveConfigDo;
import com.holderzone.saas.store.reserve.core.dal.ReserveRecordDo;
import com.holderzone.saas.store.reserve.core.domain.Item;
import com.holderzone.saas.store.reserve.core.domain.ReserveConfig;
import com.holderzone.saas.store.reserve.core.domain.ReserveRecord;
import com.holderzone.saas.store.reserve.core.domain.standar.DelegateReserveRecord;
import com.holderzone.saas.store.reserve.core.domain.vo.CustomerVo;
import com.holderzone.saas.store.reserve.core.domain.vo.TimingSegmentVo;
import com.holderzone.saas.store.reserve.core.service.ReserveConfigService;
import com.holderzone.saas.store.reserve.core.service.ReserveRecordService;
import com.holderzone.saas.store.reserve.core.service.ReserveToItemsBridge;
import com.holderzone.saas.store.reserve.intergration.order.ReserveOrderQueryDTO;
import com.holderzone.saas.store.util.BigDecimalUtil;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReserveRecordMapstruct
 * @date 2019/04/23 18:09
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Mapper(imports = {TableMapstruct.class, CustomerVo.class,JacksonUtils.class,TimingSegmentVo.class, UserContextUtils.class, ClientStateEnum.class})
public interface ReserveRecordMapstruct {
    ReserveRecordMapstruct MAPSTRUCT = Mappers.getMapper(ReserveRecordMapstruct.class);
    @Mappings({
            @Mapping(target = "name", expression = "java(reserveRecord.getCustomer().getName())"),
            @Mapping(target = "phone", expression = "java(reserveRecord.getCustomer().getPhone())"),
            @Mapping(target = "gender", expression = "java(reserveRecord.getCustomer().getGender())")
    })
    ReserveRecordDo toDo(ReserveRecord reserveRecord);

    @Mappings({
            @Mapping(target = "name", expression = "java(reserveRecord.getCustomer().getName())"),
            @Mapping(target = "phone", expression = "java(reserveRecord.getCustomer().getPhone())"),
            @Mapping(target = "gender", expression = "java(reserveRecord.getCustomer().getGender())"),
            @Mapping(target = "state" ,expression = "java(ClientStateEnum.getByState(reserveRecord).name())"),
            @Mapping(target = "area", expression = "java(reserveRecord.getArea()!=null?JacksonUtils.toObject(com.holderzone.saas.store.reserve.api.dto.AreaDTO.class,reserveRecord.getArea()):null)"),
//            @Mapping(target = "items" ,expression = "java(JacksonUtils.toObjectList(DineInItemDTO.class,reserveRecord.getItemsStr()))")
    })
    ReserveRecordDetailDTO toDetailDTO(ReserveRecord reserveRecord);

    @Mappings({
            @Mapping(target = "recordGuid", source = "guid")
    })
    ReserveRecordAppletDetailDTO toAppletDetailDTO(ReserveRecordDetailDTO detailDTO);

    Collection<DelegateReserveRecord> toDomains(Collection<ReserveRecordDo> reserveRecordDo);


    default DelegateReserveRecord toDomain(ReserveRecordDo reserveRecordDo) {

        if ( reserveRecordDo == null ) {
            return null;
        }

        DelegateReserveRecord delegateReserveRecord = new DelegateReserveRecord();

        delegateReserveRecord.setId( reserveRecordDo.getId() );
        delegateReserveRecord.setGuid( reserveRecordDo.getGuid() );
        delegateReserveRecord.setStoreGuid( reserveRecordDo.getStoreGuid() );
        delegateReserveRecord.setOrderNo( reserveRecordDo.getOrderNo() );
        delegateReserveRecord.setNumber( reserveRecordDo.getNumber() );
        delegateReserveRecord.setState( reserveRecordDo.getState() );
        delegateReserveRecord.setIsLocked( reserveRecordDo.getIsLocked() );
        delegateReserveRecord.setDeviceType( reserveRecordDo.getDeviceType() );
        delegateReserveRecord.setDeviceId( reserveRecordDo.getDeviceId() );
        delegateReserveRecord.setIsDelay( reserveRecordDo.getIsDelay() );
        delegateReserveRecord.setPaymentType( reserveRecordDo.getPaymentType() );
        delegateReserveRecord.setPaymentTypeName( reserveRecordDo.getPaymentTypeName() );
        delegateReserveRecord.setReserveAmount(reserveRecordDo.getReserveAmount());
        if (BigDecimalUtil.greaterThanZero(reserveRecordDo.getReserveAmount())) {
            delegateReserveRecord.setReserveAmount(reserveRecordDo.getReserveAmount().subtract(reserveRecordDo.getRefundAmount()));
        }
        delegateReserveRecord.setReserveRefundAmount(reserveRecordDo.getRefundAmount());
        delegateReserveRecord.setTag( reserveRecordDo.getTag() );
        delegateReserveRecord.setRemark( reserveRecordDo.getRemark() );
        delegateReserveRecord.setItemsStr( reserveRecordDo.getItemsStr() );
        delegateReserveRecord.setReserveStartTime( reserveRecordDo.getReserveStartTime() );
        delegateReserveRecord.setReservesEndTime( reserveRecordDo.getReservesEndTime() );
        delegateReserveRecord.setGender( reserveRecordDo.getGender() );
        delegateReserveRecord.setArea( reserveRecordDo.getArea() );

        delegateReserveRecord.setConfirmUserGuid( reserveRecordDo.getConfirmUserGuid() );
        delegateReserveRecord.setConfirmUserName( reserveRecordDo.getConfirmUserName() );
        delegateReserveRecord.setConfirmTime( reserveRecordDo.getConfirmTime() );
        delegateReserveRecord.setArriveUserGuid( reserveRecordDo.getArriveUserGuid() );
        delegateReserveRecord.setArriveUserName( reserveRecordDo.getArriveUserName() );
        delegateReserveRecord.setArriveTime( reserveRecordDo.getArriveTime() );
        delegateReserveRecord.setCancelUserGuid( reserveRecordDo.getCancelUserGuid() );
        delegateReserveRecord.setCancelUserName( reserveRecordDo.getCancelUserName() );
        delegateReserveRecord.setCancleTime( reserveRecordDo.getCancleTime() );
        delegateReserveRecord.setCancleReason( reserveRecordDo.getCancleReason() );
        delegateReserveRecord.setCancelRole( reserveRecordDo.getCancelRole() );
        delegateReserveRecord.setCreateStaffGuid( reserveRecordDo.getCreateStaffGuid() );
        delegateReserveRecord.setCreateStaffName( reserveRecordDo.getCreateStaffName() );
        delegateReserveRecord.setShareUrl( reserveRecordDo.getShareUrl() );
        delegateReserveRecord.setPaymentTime( reserveRecordDo.getPaymentTime() );
        delegateReserveRecord.setMaxCancelableTime( reserveRecordDo.getMaxCancelableTime() );
        delegateReserveRecord.setOperSubjectGuid( reserveRecordDo.getOperSubjectGuid() );
        delegateReserveRecord.setMemberInfoGuid( reserveRecordDo.getMemberInfoGuid() );
        delegateReserveRecord.setMemberInfoCardGuid( reserveRecordDo.getMemberInfoCardGuid() );
        delegateReserveRecord.setModifiedStaffGuid( reserveRecordDo.getModifiedStaffGuid() );
        delegateReserveRecord.setModifiedStaffName( reserveRecordDo.getModifiedStaffName() );
        delegateReserveRecord.setGmtCreate( reserveRecordDo.getGmtCreate() );
        delegateReserveRecord.setGmtModified( reserveRecordDo.getGmtModified() );
        delegateReserveRecord.setCustomer( new CustomerVo(reserveRecordDo.getName(),reserveRecordDo.getPhone(),reserveRecordDo.getGender()) );
        delegateReserveRecord.setOrderType(reserveRecordDo.getOrderType());
        if (StringUtils.hasText(reserveRecordDo.getMainOrderGuid())) {
            delegateReserveRecord.setOrderGuid(reserveRecordDo.getMainOrderGuid());
        }

        toDelegate(delegateReserveRecord);
        return delegateReserveRecord;
    }

    Collection<DelegateReserveRecord> toDomainss(Collection<DelegateReserveRecord> reserveRecords);

    ReserveOrderQueryDTO toQueryDTO(ReserveRecord reserveRecord);

    @Mappings({
            @Mapping(target = "customer", expression = "java(new CustomerVo(reserveRecord.getName(),reserveRecord.getPhone(),reserveRecord.getGender()))"),
            @Mapping(target = "itemsStr", expression = "java(reserveRecord.getItems()!=null?JacksonUtils.writeValueAsString(reserveRecord.getItems()):null)"),
            @Mapping(target = "area", expression = "java(reserveRecord.getArea()!=null?JacksonUtils.writeValueAsString(reserveRecord.getArea()):null)")
    })
    DelegateReserveRecord dtotoDomain(ReserveRecordDTO reserveRecord);

    @Mappings({
            @Mapping(target = "detail", expression = "java(dineInItemDTO)"),
    })
    Item dineInItemDTOToItem(DineInItemDTO dineInItemDTO);

    Collection<Item> dineInItemDTOToItem(Collection<DineInItemDTO> dineInItemDTO);

    @Mappings({
            @Mapping(target = "name", expression = "java(reserveRecord.getCustomer().getName())"),
            @Mapping(target = "phone", expression = "java(reserveRecord.getCustomer().getPhone())"),
            @Mapping(target = "gender", expression = "java(reserveRecord.getCustomer().getGender())"),
            @Mapping(target = "state" ,expression = "java(ClientStateEnum.getByState(reserveRecord).name())"),
            @Mapping(target = "area", expression = "java(reserveRecord.getArea()!=null?JacksonUtils.toObject(com.holderzone.saas.store.reserve.api.dto.AreaDTO.class,reserveRecord.getArea()):null)"),
    })
    ReserveRecordDTO domainToDto(ReserveRecord reserveRecord);

    @Mappings({
            @Mapping(target = "name", expression = "java(reserveRecord.getCustomer().getName())"),
            @Mapping(target = "phone", expression = "java(reserveRecord.getCustomer().getPhone())"),
            @Mapping(target = "state" ,expression = "java(ClientStateEnum.getByState(reserveRecord).name())"),
            @Mapping(target = "hasItem",expression = "java(reserveRecord.hasItem())"),
            @Mapping(target = "area", expression = "java(reserveRecord.getArea()!=null?JacksonUtils.toObject(com.holderzone.saas.store.reserve.api.dto.AreaDTO.class,reserveRecord.getArea()):null)"),
    })
    ReserveRecordLessDTO toLessDTO(ReserveRecord reserveRecord);

    Collection<ReserveRecordLessDTO> toLessDTOs(Collection<? extends ReserveRecord> reserveRecords);

    @Mappings({
            @Mapping(target = "state", expression = "java(ClientStateEnum.getByState(reserveRecord).name())"),
            @Mapping(target = "recordGuid", source = "guid"),
            @Mapping(target = "areaName", expression = "java(reserveRecord.getArea()!=null?JacksonUtils.toObject(com.holderzone.saas.store.reserve.api.dto.AreaDTO.class,reserveRecord.getArea()).getName():null)")
    })
    ReserveRecordAppletPageDTO toAppletPageDTO(DelegateReserveRecord reserveRecord);

    List<ReserveRecordAppletPageDTO> toAppletPageDTOs(List<DelegateReserveRecord> reserveRecords);


    @Mappings({
            @Mapping(target = "segments", expression = "java(reserveConfigDo.getSegmentsStr()!=null?JacksonUtils.toObjectList(TimingSegmentVo.class,reserveConfigDo.getSegmentsStr()):null)"),
            @Mapping(target = "requirementType", expression = "java(reserveConfigDo.getRequirementType()!=null?JacksonUtils.toObjectList(String.class,reserveConfigDo.getRequirementType()):null)"),
            @Mapping(target = "reserveArea", expression = "java(reserveConfigDo.getReserveArea()!=null?JacksonUtils.toObjectList(AreaVO.class,reserveConfigDo.getReserveArea()):null)"),
            @Mapping(target = "areaDeposit", expression = "java(reserveConfigDo.getAreaDeposit()!=null?JacksonUtils.toObjectList(AreaVO.class,reserveConfigDo.getAreaDeposit()):null)"),
            @Mapping(target = "invitationImages", expression = "java(reserveConfigDo.getInvitationImages()!=null?JacksonUtils.toObjectList(String.class,reserveConfigDo.getInvitationImages()):null)"),
    })
    ReserveConfig doToDomain(ReserveConfigDo reserveConfigDo);

    List<ReserveConfig> dosToDomains(List<ReserveConfigDo> reserveConfigDOList);

    @Mappings({
            @Mapping(target = "segmentsStr", expression = "java(reserveConfig.getSegments()!=null?JacksonUtils.writeValueAsString(reserveConfig.getSegments()):null)"),
            @Mapping(target = "requirementType", expression = "java(reserveConfig.getRequirementType()!=null?JacksonUtils.writeValueAsString(reserveConfig.getRequirementType()):null)"),
            @Mapping(target = "reserveArea", expression = "java(reserveConfig.getReserveArea()!=null?JacksonUtils.writeValueAsString(reserveConfig.getReserveArea()):null)"),
            @Mapping(target = "areaDeposit", expression = "java(reserveConfig.getAreaDeposit()!=null?JacksonUtils.writeValueAsString(reserveConfig.getAreaDeposit()):null)"),
            @Mapping(target = "invitationImages", expression = "java(reserveConfig.getInvitationImages()!=null?JacksonUtils.writeValueAsString(reserveConfig.getInvitationImages()):null)"),
    })
    ReserveConfigDo domainToDo(ReserveConfig reserveConfig);

    @Mappings(@Mapping(target = "sub" ,ignore = true))
    TimingSegmentDTO voToDto(TimingSegmentVo vo);
    @Mappings(@Mapping(target = "sub" ,ignore = true))
    TimingSegmentVo dtoToVo(TimingSegmentDTO vo);

    ReserveAvailableStoreConfigDTO configDTOToAvailableStoreConfigDTO(ReserveConfigDTO reserveConfigDTO);

    List<ReserveAvailableStoreConfigDTO> configDTOListToAvailableStoreConfigDTOList(List<ReserveConfigDTO> reserveConfigDTOList);

    ReserveConfig dtoToDomain(ReserveConfigDTO reserveConfig);

    ReserveConfigDTO domainToDto(ReserveConfig reserveConfig);

    List<ReserveConfigDTO> domainsToDtoList(List<ReserveConfig> reserveConfigList);

    default DineInItemDTO dineInItemDTOToItem(Item item){
        return (DineInItemDTO) item.getDetail();
    }


    default DelegateReserveRecord toDelegate(ReserveRecord reserveRecord){
        return DelegateInfrastructure.toDelegate(reserveRecord);
    }
    @Component
    class DelegateInfrastructure{
        private static ReserveToItemsBridge reserveToItemsBridge ;
        private static ReserveRecordService reserveRecordService ;
        private static ReserveConfigService reserveConfigService ;
        public static void init(){
            if(reserveToItemsBridge == null){
                reserveToItemsBridge = SpringContextUtils.getInstance().getBean(ReserveToItemsBridge.class);
                reserveRecordService = SpringContextUtils.getInstance().getBean(ReserveRecordService.class);
                reserveConfigService = SpringContextUtils.getInstance().getBean(ReserveConfigService.class);
            }
        }
        public static DelegateReserveRecord toDelegate(ReserveRecord reserveRecord){
            init();
            if(reserveRecord instanceof DelegateReserveRecord){
                ((DelegateReserveRecord) reserveRecord).setItemSupplier(reserveToItemsBridge);
                ((DelegateReserveRecord) reserveRecord).setTableFunction(reserveRecordService::getTables);
                ((DelegateReserveRecord) reserveRecord).setConfigFunction(reserveConfigService::obtain);
                return (DelegateReserveRecord) reserveRecord;
            }
            return null;
        }
    }


}