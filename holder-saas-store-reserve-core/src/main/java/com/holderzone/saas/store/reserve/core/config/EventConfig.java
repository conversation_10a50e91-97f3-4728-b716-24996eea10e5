package com.holderzone.saas.store.reserve.core.config;

import com.holderzone.framework.event.publish.impl.CustomerPublishImpl;
import com.holderzone.saas.store.reserve.core.common.SyncPublishImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Scope;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2018/5/17
 */
@Configuration
public class EventConfig {

    @Autowired
    private ApplicationContext applicationContext;
    @Bean
    @Scope("singleton")
    public CustomerPublishImpl customerPublish() {
        CustomerPublishImpl customerPublish = new SyncPublishImpl();
        String packageName = "com.holderzone.saas.store.reserve.core.event.impl.handler";
        customerPublish.setPackName(packageName);
        customerPublish.addObserver();
        customerPublish.getListsForSpring()
                .forEach(customerObserverClass -> Optional.ofNullable(applicationContext.getBean(customerObserverClass)).ifPresent(e->customerPublish.getLists().add(e)));
        return customerPublish;
    }

}
