package com.holderzone.saas.store.reserve.core.event.impl.handler;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.anno.CustomerRegister;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.event.CustomerEvent;
import com.holderzone.framework.event.observer.CustomerObserver;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.redisson.sdk.lock.RedissonLockUtil;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.member.terminal.dto.common.RequestBaseInfo;
import com.holderzone.holder.saas.member.terminal.dto.order.RequestConfirmPay;
import com.holderzone.holder.saas.member.terminal.dto.order.ResponseConfirmPay;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.enums.PaymentTypeEnum;
import com.holderzone.saas.store.enums.weixin.WxAppletMemberPayTypeEnum;
import com.holderzone.saas.store.reserve.api.dto.ReservePayReqDTO;
import com.holderzone.saas.store.reserve.api.enums.PayStateEnum;
import com.holderzone.saas.store.reserve.core.common.ReserveUtils;
import com.holderzone.saas.store.reserve.core.dal.ReserveRecordDo;
import com.holderzone.saas.store.reserve.core.dal.mapper.ReserveRecordDoMapper;
import com.holderzone.saas.store.reserve.core.domain.ReserveRecord;
import com.holderzone.saas.store.reserve.core.domain.ReserveRecordStateEnum;
import com.holderzone.saas.store.reserve.core.event.BaseEventHandler;
import com.holderzone.saas.store.reserve.core.event.impl.event.DelayEvent;
import com.holderzone.saas.store.reserve.core.event.impl.event.MemberPayEvent;
import com.holderzone.saas.store.reserve.core.event.impl.event.SystemMessageEvent;
import com.holderzone.saas.store.reserve.core.rocket.Consume;
import com.holderzone.saas.store.reserve.core.service.ReservePayRecordService;
import com.holderzone.saas.store.reserve.intergration.table.MemberTerminalClientService;
import com.holderzone.saas.store.reserve.intergration.table.OrganizationClientService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.util.Objects;
import java.util.concurrent.ExecutorService;


/**
 * 会员余额支付
 */
@Slf4j
@Component
@RequiredArgsConstructor
@CustomerRegister(isRegister = true)
public class MemberPayEventHandler extends BaseEventHandler<MemberPayEvent> implements CustomerObserver<MemberPayEvent> {

    private final ReserveRecordDoMapper reserveRecordDoMapper;

    private final ReservePayRecordService reservePayRecordService;

    private final MemberTerminalClientService memberTerminalClientService;

    private final OrganizationClientService organizationClientService;

    private final ExecutorService payAfterExecutor;

    @Override
    public void execute(MemberPayEvent baseEvent) {
        ReserveRecord reserveRecord = baseEvent.getReserveRecord();
        Assert.isTrue(reserveRecord.getState() == ReserveRecordStateEnum.NO_PAY.getCode(), "该订单无法支付, 请刷新后重试");
        // put
        UserContext userContext = UserContextUtils.get();
        if (StringUtils.isEmpty(userContext.getStoreGuid())) {
            userContext.setStoreGuid(reserveRecord.getStoreGuid());
        }
        if (StringUtils.isNotEmpty(reserveRecord.getMemberName())) {
            userContext.setUserName(reserveRecord.getMemberName());
        }
        UserContextUtils.put(userContext);
        if (Objects.equals(WxAppletMemberPayTypeEnum.STORED_AMOUNT.getType(), baseEvent.getPayType())) {
            // 会员储值余额支付 调用会员服务
            RequestConfirmPay confirmPay = new RequestConfirmPay();
            confirmPay.setPayPassword(reserveRecord.getMemberPassword());
            confirmPay.setNeedPassword(true);
            confirmPay.setCardBalancePayAmount(reserveRecord.getReserveAmount());
            RequestBaseInfo requestBaseInfo = new RequestBaseInfo();
            requestBaseInfo.setMemberInfoGuid(baseEvent.getMemberInfoGuid());
            requestBaseInfo.setMemberInfoCardGuid(baseEvent.getMemberInfoCardGuid());
            requestBaseInfo.setStoreGuid(reserveRecord.getStoreGuid());
            StoreDTO storeDTO = organizationClientService.queryStoreByGuid(reserveRecord.getStoreGuid());
            if (Objects.nonNull(storeDTO)) {
                requestBaseInfo.setStoreName(storeDTO.getName());
                userContext.setStoreName(storeDTO.getName());
                UserContextUtils.put(userContext);
            }
            requestBaseInfo.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
            confirmPay.setRequestBaseInfo(requestBaseInfo);
            ResponseConfirmPay responseConfirmPay = memberTerminalClientService.reservePay(confirmPay);
            log.info("会员储值余额支付返回参数,responseConfirmPay:{}", JacksonUtils.writeValueAsString(responseConfirmPay));
            reserveRecord.setMemberFundingDetailGuid(responseConfirmPay.getFundingDetailGuid());
        }
        // 使用收益余额 直接支付成功
        // 修改预定单信息
        updateReserveRecordDO(baseEvent);
        // 记录会员余额支付记录
        savePayRecordDO(baseEvent);
        // 会员支付后置处理
        memberPayAfterHandler(baseEvent);
    }

    /**
     * 修改预定单信息
     */
    private void updateReserveRecordDO(MemberPayEvent baseEvent) {
        ReserveRecord reserveRecord = baseEvent.getReserveRecord();
        // update
        ReserveRecordDo recordDO = new ReserveRecordDo();
        recordDO.setGuid(reserveRecord.getGuid());
        recordDO.setState(ReserveRecordStateEnum.COMMIT.getCode());
        recordDO.setPaymentType(PaymentTypeEnum.MEMBER.getCode());
        recordDO.setPaymentTypeName(PaymentTypeEnum.MEMBER.getDesc());
        if (Objects.equals(WxAppletMemberPayTypeEnum.INCOME_AMOUNT.getType(), baseEvent.getPayType())) {
            recordDO.setPaymentType(PaymentTypeEnum.OTHER.getCode());
            recordDO.setPaymentTypeName(WxAppletMemberPayTypeEnum.INCOME_AMOUNT.getMessage());
        }
        recordDO.setPaymentTime(LocalDateTime.now());
        recordDO.setMemberInfoGuid(reserveRecord.getMemberInfoGuid());
        recordDO.setMemberInfoCardGuid(reserveRecord.getMemberInfoCardGuid());
        reserveRecordDoMapper.update(recordDO,
                new LambdaQueryWrapper<ReserveRecordDo>()
                        .eq(ReserveRecordDo::getGuid, reserveRecord.getGuid()));
        // copy
        reserveRecord.setState(ReserveRecordStateEnum.COMMIT.getCode());
        reserveRecord.setPaymentType(recordDO.getPaymentType());
        reserveRecord.setPaymentTypeName(recordDO.getPaymentTypeName());
        reserveRecord.setPaymentTime(recordDO.getPaymentTime());
        reserveRecord.setMemberInfoGuid(recordDO.getMemberInfoGuid());
        reserveRecord.setMemberInfoCardGuid(recordDO.getMemberInfoCardGuid());
    }

    /**
     * 保存支付记录
     */
    private void savePayRecordDO(MemberPayEvent baseEvent) {
        ReserveRecord reserveRecord = baseEvent.getReserveRecord();
        ReservePayReqDTO reservePayReqDTO = new ReservePayReqDTO();
        reservePayReqDTO.setPaymentType(PaymentTypeEnum.MEMBER.getCode());
        reservePayReqDTO.setPaymentTypeName(PaymentTypeEnum.MEMBER.getDesc());
        if (Objects.equals(WxAppletMemberPayTypeEnum.INCOME_AMOUNT.getType(), baseEvent.getPayType())) {
            reservePayReqDTO.setPaymentType(PaymentTypeEnum.OTHER.getCode());
            reservePayReqDTO.setPaymentTypeName(WxAppletMemberPayTypeEnum.INCOME_AMOUNT.getMessage());
        }
        reservePayReqDTO.setGuid(reserveRecord.getGuid());
        reservePayReqDTO.setReserveAmount(reserveRecord.getReserveAmount());
        reservePayReqDTO.setMemberFundingDetailGuid(reserveRecord.getMemberFundingDetailGuid());
        reservePayRecordService.savePayRecordDO(reservePayReqDTO, PayStateEnum.SUCCESS, ReserveUtils.reservePayGuid());
    }

    /**
     * 会员支付后置处理
     */
    private void memberPayAfterHandler(MemberPayEvent baseEvent) {
        ReserveRecord reserveRecord = baseEvent.getReserveRecord();
        UserContext userContext = UserContextUtils.get();
        payAfterExecutor.execute(() -> {
            UserContextUtils.put(userContext);
            EnterpriseIdentifier.setEnterpriseGuid(userContext.getEnterpriseGuid());
            SystemMessageEvent systemMessageEvent = new SystemMessageEvent(reserveRecord);
            customerPublish.publish(new CustomerEvent<>(systemMessageEvent));
        });
        // 商家未接单2小时自动取消
        payAfterExecutor.execute(() -> {
            UserContextUtils.put(userContext);
            EnterpriseIdentifier.setEnterpriseGuid(userContext.getEnterpriseGuid());
            DelayEvent delayEvent = new DelayEvent(reserveRecord, Consume.ACCEPT_DELAY_TAG);
            customerPublish.publish(new CustomerEvent<>(delayEvent));
        });
    }


    @Override
    protected void pre(MemberPayEvent baseEvent) {
        ReserveRecord reserveRecord = baseEvent.getReserveRecord();
        boolean result = RedissonLockUtil.tryLock(reserveRecord.getGuid(), 3, 10);
        if (!result) {
            throw new BusinessException("当前预定记录正被其他人操作,请稍候重试!!");
        }
        super.pre(baseEvent);
    }

    @Override
    protected void after(MemberPayEvent baseEvent) {
        ReserveRecord reserveRecord = baseEvent.getReserveRecord();
        RedissonLockUtil.unlock(reserveRecord.getGuid());
        super.after(baseEvent);
    }
}