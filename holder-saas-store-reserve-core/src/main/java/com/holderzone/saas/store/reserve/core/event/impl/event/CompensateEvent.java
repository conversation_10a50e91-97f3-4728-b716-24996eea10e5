package com.holderzone.saas.store.reserve.core.event.impl.event;

import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.reserve.TableDTO;
import com.holderzone.saas.store.reserve.core.domain.ReserveRecord;
import lombok.Getter;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className LaunchEvent
 * @date 2019/04/23 17:24
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Getter
public class CompensateEvent extends EffectiveEvent {
    private List<TableDTO> tableDTOS;

    public CompensateEvent(ReserveRecord reserveRecord, BaseDTO baseDTO, List<TableDTO> tableDTOS) {
        super(reserveRecord, baseDTO);
        this.tableDTOS = tableDTOS;
    }

    private static final String NAME=CompensateEvent.class.getSimpleName();


    @Override
    public String getName() {
        return NAME;
    }
}