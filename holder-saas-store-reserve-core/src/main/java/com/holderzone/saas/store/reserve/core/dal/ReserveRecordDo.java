package com.holderzone.saas.store.reserve.core.dal;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReserveRecordDo
 * @date 2019/04/23 14:21
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Data
@Accessors(chain = true)
@TableName("hss_reserve_record")
public class ReserveRecordDo {
    @TableId
    private Long id;
    private String guid;

    /**
     * 订单编号
     */
    private String orderNo;

    private String storeGuid;
    private Integer number;
    private Integer state;
    private Boolean isLocked = false;
    private String name;
    private String phone;
    /**
     * 1. 男, 0. 女
     */
    private Byte gender;
    /**
     * value = "设备类型:PC服务端- 0,PC平板- 1,小店通- 2,一体机- 3,POS机- 4,云平板- 5,点菜宝(M1)- 6,PV1(带刷卡的点菜宝)- 7,微信- 8；",
     */
    private Integer deviceType;
    /**
     * value = "设备id",
     */
    private Boolean isDelay;

    /**
     * 预定区域
     */
    private String area;

    private String deviceId;
    private String mainOrderGuid;

    private Integer paymentType;
    private String paymentTypeName;
    private LocalDateTime paymentTime;
    private BigDecimal reserveAmount;

    private String tag;
    private String remark;
    private String itemsStr;
    /**
     * 预定锁开始时间
     */
    private LocalDateTime reserveLockTime;
    private LocalDateTime reserveStartTime;
    private LocalDateTime reservesEndTime;

    private String confirmUserGuid;
    private String confirmUserName;
    private LocalDateTime confirmTime;


    private String arriveUserGuid;
    private String arriveUserName;
    private LocalDateTime arriveTime;


    private String cancelUserGuid;
    private String cancelUserName;
    private LocalDateTime cancleTime;
    private String cancleReason;

    /**
     * 最大可退款时间
     */
    private LocalDateTime maxCancelableTime;

    /**
     * 取消角色
     */
    private String cancelRole;

    /**
     * 是否启用，1-已启用，0-未启用
     */
    private Boolean isEnable;

    /**
     * 小程序分享地址
     */
    private String shareUrl;

    /**
     * 运营主体guid
     */
    private String operSubjectGuid;

    /**
     * 会员guid
     */
    private String memberInfoGuid;

    /**
     * 会员持卡guid (支付)
     */
    private String memberInfoCardGuid;

    /**
     * 是否删除，1-已删除，0-未删除
     */
    @TableLogic
    private Boolean isDeleted;

    /**
     * 创建人guid
     */
    private String createStaffGuid;

    private String createStaffName;

    /**
     * 下单人手机号 (小程序端)
     */
    private String createUserPhone;

    /**
     * 更新人guid
     */
    private String modifiedStaffGuid;
    private String modifiedStaffName;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    private LocalDateTime gmtModified;

    /**
     * 订单类型 0预订 1预付金
     */
    private Integer orderType;

    /**
     * 回退金额
     */
    private BigDecimal refundAmount;

}