package com.holderzone.saas.store.reserve.core.event.impl.event;

import com.holderzone.saas.store.reserve.core.domain.ReserveRecord;
import com.holderzone.saas.store.reserve.core.event.BaseEvent;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BeDelayEvent
 * @date 2019/06/12 14:09
 * @description //TODO
 * @program holder-saas-store-reserve
 */
public class BeDelayEvent extends BaseEvent {

    private static final String NAME=CompensateEvent.class.getSimpleName();

    public BeDelayEvent(ReserveRecord reserveRecord) {
        super(reserveRecord);
    }

    @Override
    public String getName() {
        return NAME;
    }
}