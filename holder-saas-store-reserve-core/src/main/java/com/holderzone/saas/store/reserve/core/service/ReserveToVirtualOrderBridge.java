package com.holderzone.saas.store.reserve.core.service;

import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.reserve.intergration.order.ReserveOrderQueryDTO;
import com.holderzone.saas.store.reserve.intergration.order.VirtualOrderService;

import java.util.function.Function;

/**
 * <AUTHOR>
 * @version 1.0
 * @className VirtualOrderToItemsBridge
 * @date 2019/04/23 14:39
 * @description //TODO
 * @program holder-saas-store-reserve
 */
public class ReserveToVirtualOrderBridge implements Function<ReserveOrderQueryDTO,DineinOrderDetailRespDTO> {

    private VirtualOrderService virtualOrderService;

    public ReserveToVirtualOrderBridge(VirtualOrderService virtualOrderService) {
        this.virtualOrderService = virtualOrderService;
    }

    @Override
    public DineinOrderDetailRespDTO apply(ReserveOrderQueryDTO dto) {
        return virtualOrderService.getByReserveRecord(dto);
    }
}