package com.holderzone.saas.store.reserve.core.service.impl;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.rocketmq.common.DefaultRocketMqProducer;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.print.content.PrintReserveDTO;
import com.holderzone.saas.store.dto.print.content.PrintReservePayDTO;
import com.holderzone.saas.store.reserve.api.dto.ReservePrintDTO;
import com.holderzone.saas.store.reserve.api.dto.ReserveRecordDetailDTO;
import com.holderzone.saas.store.reserve.core.config.RocketMqConfig;
import com.holderzone.saas.store.reserve.core.service.PrintService;
import com.holderzone.saas.store.reserve.core.service.ReservePrintFactory;
import com.holderzone.saas.store.reserve.intergration.table.OrganizationClientService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.Message;
import org.springframework.stereotype.Service;

import java.util.Objects;


@Service
@Slf4j
public class PrintServiceIml implements PrintService {

    private final ReservePrintFactory reservePrintFactory;

    private final DefaultRocketMqProducer defaultRocketMqProducer;

    private final OrganizationClientService organizationClientService;

    public PrintServiceIml(ReservePrintFactory reservePrintFactory,
                           DefaultRocketMqProducer defaultRocketMqProducer,
                           OrganizationClientService organizationClientService) {
        this.reservePrintFactory = reservePrintFactory;
        this.defaultRocketMqProducer = defaultRocketMqProducer;
        this.organizationClientService = organizationClientService;
    }

    @Override
    public void printPreOrdering(ReservePrintDTO reservePrintDTO) {
        PrintReserveDTO printReserveDTO = reservePrintFactory.createBillReserveDtO(reservePrintDTO);
        doPrintAsync(printReserveDTO);
    }

    /**
     * 预付金单打印
     *
     * @param reserveRecordDetailDTO 预约记录详情 DTO
     */
    @Override
    public void printReservePay(ReserveRecordDetailDTO reserveRecordDetailDTO) {
        // 记录入参用于调试
        log.info("预付金打印入参: {}", JacksonUtils.writeValueAsString(reserveRecordDetailDTO));

        // 查询门店信息
        StoreDTO storeDTO = organizationClientService.queryStoreByGuid(reserveRecordDetailDTO.getStoreGuid());
        if (Objects.isNull(storeDTO)
            || StringUtils.isEmpty(storeDTO.getName())) {
            throw new RuntimeException("门店信息不存在");
        }
        reserveRecordDetailDTO.setStoreName(storeDTO.getName());

        // 参数校验
        if (!validateReserveRecordDetailDTO(reserveRecordDetailDTO)) {
            return;
        }

        try {
            // 构建打印数据
            PrintReservePayDTO printReservePayDTO = reservePrintFactory.createReservePayDtO(reserveRecordDetailDTO);
            if (Objects.isNull(printReservePayDTO)) {
                log.warn("预付金打印参数为空");
                return;
            }

            // 异步发送打印消息
            doPrintAsync(printReservePayDTO);
        } catch (Exception e) {
            // 捕获并记录异常信息
            log.error("预付金单打印消息发送失败，参数：{}", JacksonUtils.writeValueAsString(reserveRecordDetailDTO), e);
        }
    }

    /**
     * 校验预约记录详情 DTO 是否有效
     *
     * @param dto 预约记录详情 DTO
     * @return 是否有效
     */
    private boolean validateReserveRecordDetailDTO(ReserveRecordDetailDTO dto) {
        if (Objects.isNull(dto)) {
            log.warn("预付金打印请求参数为空");
            return false;
        }
        return true;
    }

    private String doPrintAsync(Object object) {
        Message message = new Message(
                RocketMqConfig.PRINT_MESSAGE_TOPIC,
                RocketMqConfig.PRINT_MESSAGE_TAG,
                JacksonUtils.toJsonByte(object)
        );
        message.getProperties().put(
                RocketMqConfig.MESSAGE_CONTEXT,
                UserContextUtils.getJsonStr()
        );
        defaultRocketMqProducer.sendMessage(message);
        return "SUCCESS";
    }
}
