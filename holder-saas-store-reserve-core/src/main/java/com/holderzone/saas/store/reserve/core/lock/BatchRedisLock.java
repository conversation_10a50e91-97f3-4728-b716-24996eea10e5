package com.holderzone.saas.store.reserve.core.lock;

import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.script.DefaultRedisScript;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BatchRedisLock
 * @date 2019/04/24 16:10
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Slf4j
public class BatchRedisLock extends RedisTableLock {
    public static final String RESULT_KEY = "batchResult";

    public void setTableGuids(Collection<String> tableGuids) {
        this.tableGuids = tableGuids;
    }

    private static final String BATCH_SEGMENT_LOCK_STR ="local segment = ARGV[1]; " +
                                                        "local owner = ARGV[2]; " +
                                                        "local failStrategy = tonumber(ARGV[3]); " +
                                                        "local fail={}; " +
                                                        "for i,key in ipairs(KEYS) do " +
                                                        "  local segmentKey = key..':segment'; " +
                                                        "  local modeKey = key..':mode'; " +
                                                        "  local ownerKey = key..':owner'; " +
                                                        "  local currentSegment = {};  " +
                                                        "  string.gsub(segment,'[^-]+',function ( w )  " +
                                                        "      table.insert(currentSegment,w)  " +
                                                        "  end); " +
                                                        "  local modeExist = redis.call('EXISTS',modeKey); " +
                                                        "  local success = 1; " +
                                                        "  if(modeExist == 0) then  " +
                                                        "    redis.call('set',ownerKey,1); " +
                                                        "    redis.call('set',modeKey,1); " +
                                                        "    success = 1; " +
                                                        "  else " +
                                                        "    local mode = redis.call('get',modeKey); " +
                                                        "    if(mode == 0) then  " +
                                                        "      success = 0; " +
                                                        "    else " +
                                                        "      local data = redis.call('HGETALL',segmentKey);  " +
                                                        "      for i, segment in ipairs(data) do  " +
                                                        "        if(i % 2 == 1) then " +
                                                        "          local old = {}  " +
                                                        "          string.gsub(segment,'[^-]+',function ( w )  " +
                                                        "              table.insert(old,w)  " +
                                                        "          end);  " +
                                                        "          if(old[1]==currentSegment[1] and old[2]==currentSegment[2]) then   " +
                                                        "            success = 0;  " +
                                                        "            break; " +
                                                        "          end  " +
                                                        "          if(old[1]<currentSegment[1] and old[2]>=currentSegment[1]) then   " +
                                                        "            success = 0;  " +
                                                        "            break; " +
                                                        "          end  " +
                                                        "          if(old[1]<=currentSegment[2] and old[2]>currentSegment[2]) then   " +
                                                        "            success = 0;  " +
                                                        "            break; " +
                                                        "          end  " +
                                                        "        end  " +
                                                        "      end  " +
                                                        "    end  " +
                                                        "  end " +
                                                        "  if(success == 1) then  " +
                                                        "    redis.call('hset',segmentKey,segment,owner);  " +
                                                        "  else " +
                                                        "    table.insert(fail,key); " +
                                                        "    if(failStrategy == 0) then  " +
                                                        "      break; " +
                                                        "    end  " +
                                                        "  end " +
                                                        "end " +
                                                        "return fail;";
    private static final String BATCH_SEGMENT_UNLOCK_STR = "local segment = ARGV[1];  " +
                                                            "local owner = ARGV[2];  " +
                                                            "local failStrategy = tonumber(ARGV[3]); " +
                                                            "local fail={}; " +
                                                            "for i,key in ipairs(KEYS) do " +
                                                            "  local segmentKey = key..':segment';  " +
                                                            "  local modeKey = key..':mode';  " +
                                                            "  local ownerKey = key..':owner'; " +
                                                            "  local redisOwner = redis.call('HGET',segmentKey,segment);  " +
                                                            "  if(redisOwner == owner) then  " +
                                                            "    redis.call('HDEL',segmentKey,segment);   " +
                                                            "    local size = redis.call('HLEN',segmentKey); " +
                                                            "    if(size == 0) then  " +
                                                            "      redis.call('del',modeKey);  " +
                                                            "      redis.call('del',ownerKey); " +
                                                            "    end " +
                                                            "  else  " +
                                                            "    table.insert(fail,key); " +
                                                            "  end " +
                                                            "end " +
                                                            "return fail;";

    private static final String BATCH_WHOLE_LOCK_STR = "local fail={}; " +
                                                        "local failStrategy = ARGV[1]; " +
                                                        "local owner = ARGV[2]; " +
                                                        "for i,key in ipairs(KEYS) do " +
                                                        "  local modeKey = key..':mode';  " +
                                                        "  local ownerKey = key..':owner'; " +
                                                        "  local success = redis.call('SETNX',ownerKey,owner); " +
                                                        "  if(success == 0) then  " +
                                                        "    local redisOwner = redis.call('get',ownerKey); " +
                                                        "    if(redisOwner ~= owner) then  " +
                                                        "      table.insert(fail,key); " +
                                                        "      if(failStrategy == 0) then  " +
                                                        "        break; " +
                                                        "      end  " +
                                                        "    end " +
                                                        "  else  " +
                                                        "    redis.call('set',modeKey,0); " +
                                                        "  end  " +
                                                        "end  " +
                                                        "return fail;";

    private static final String BATCH_WHOLE_UNLOCK_STR = "local failStrategy = ARGV[1]; " +
                                                        "local owner = ARGV[2]; " +
                                                        "local fail={}; " +
                                                        "for i,key in ipairs(KEYS) do " +
                                                        " local modeKey = key..':mode';  " +
                                                        " local ownerKey = key..':owner'; " +
                                                        " local redisOwner = redis.call('get',ownerKey); " +
                                                        " if(redisOwner == owner) then " +
                                                        "  redis.call('del',modeKey);  " +
                                                        "  redis.call('del',ownerKey); " +
                                                        " else " +
                                                        "  table.insert(fail,key); " +
                                                        "  if(failStrategy == 0) then  " +
                                                        "   break; " +
                                                        "  end  " +
                                                        " end " +
                                                        "end  " +
                                                        "return fail; ";
    private static final DefaultRedisScript SEGMENT_LOCK_SCRIPT = new DefaultRedisScript(BATCH_SEGMENT_LOCK_STR, List.class);
    private static final DefaultRedisScript SEGMENT_UNLOCK_SCRIPT = new DefaultRedisScript(BATCH_SEGMENT_UNLOCK_STR,List.class);
    private static final DefaultRedisScript WHOLE_LOCK_SCRIPT = new DefaultRedisScript(BATCH_WHOLE_LOCK_STR,Integer.class);
    private static final DefaultRedisScript WHOLE_UNLOCK_SCRIPT = new DefaultRedisScript(BATCH_WHOLE_UNLOCK_STR,Integer.class);
    private Collection<String> tableGuids;

    public BatchRedisLock(String owner, Collection<String> tableGuids) {
        super(null, owner);
        this.tableGuids = tableGuids;
    }

    public Collection<String> getTableGuids() {
        return tableGuids;
    }

    @Override
    public void lock(LockContext context) {
        List<String> fail = null;
        List<String> keys = Optional.ofNullable(tableGuids)
                .orElse(Collections.emptyList())
                .stream()
                .map((e)->PREFIX+e)
                .collect(Collectors.toList());
        if(keys.isEmpty()){
            return;
        }
        try {
            switch (context.getLockModeEnum()) {
                case WHOLE:
                    fail = (List<String>) redisTemplate.execute(WHOLE_LOCK_SCRIPT, keys(),getOwner(),context.getLockFailStrategy().ordinal()+"");
                    break;
                case SEGMENT:
                    fail = (List<String>) redisTemplate.execute(SEGMENT_LOCK_SCRIPT, keys(),context.getSegment(),getOwner(),context.getLockFailStrategy().ordinal()+"");
                    break;
                default:
                    throw new UnsupportedOperationException();
            }
        }catch (Exception e){
            log.error("加锁异常",e);
        }


        context.getOther().put(RESULT_KEY,
                Optional.ofNullable(fail).orElse(Collections.emptyList())
                        .stream()
                        .map((e)->e.replaceAll(PREFIX,""))
                        .collect(Collectors.toList())
        );
    }

    @Override
    public void unLock(LockContext context) {
        if(tableGuids == null || tableGuids.isEmpty()){
            return;
        }
        try {
            switch (context.getLockModeEnum()) {
                case WHOLE:
                    redisTemplate.execute(WHOLE_UNLOCK_SCRIPT, keys(),getOwner());
                    break;
                case SEGMENT:
                    redisTemplate.execute(SEGMENT_UNLOCK_SCRIPT, keys(),context.getSegment(),getOwner());
                    break;
                default:
                    throw new UnsupportedOperationException();
            }
        }catch (Exception e){
            log.error("解锁异常",e);
        }

    }
    public List<String> keys(){
        return tableGuids.stream().map((e)->PREFIX+e).collect(Collectors.toList());
    }
}