package com.holderzone.saas.store.reserve.core.event.impl.handler;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.anno.CustomerRegister;
import com.holderzone.framework.base.dto.message.ShortMessageType;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.event.CustomerEvent;
import com.holderzone.framework.event.observer.CustomerObserver;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.redisson.sdk.lock.RedissonLockUtil;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.message.BusinessMessageDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.reserve.UpdateOrderReserveReqDTO;
import com.holderzone.saas.store.dto.takeaway.OrderType;
import com.holderzone.saas.store.enums.BaseDeviceTypeEnum;
import com.holderzone.saas.store.enums.msg.BusinessMsgTypeEnum;
import com.holderzone.saas.store.reserve.api.dto.ReserveSystemMsgDTO;
import com.holderzone.saas.store.reserve.api.enums.OrderTypeEnum;
import com.holderzone.saas.store.reserve.core.common.ReserveConstant;
import com.holderzone.saas.store.reserve.core.dal.ReserveRecordDo;
import com.holderzone.saas.store.reserve.core.domain.Item;
import com.holderzone.saas.store.reserve.core.domain.ReserveRecord;
import com.holderzone.saas.store.reserve.core.domain.ReserveRecordStateEnum;
import com.holderzone.saas.store.reserve.core.domain.Table;
import com.holderzone.saas.store.reserve.core.event.impl.event.*;
import com.holderzone.saas.store.reserve.core.rocket.Consume;
import com.holderzone.saas.store.reserve.intergration.table.MessageClientService;
import com.holderzone.saas.store.reserve.intergration.table.OrganizationClientService;
import com.holderzone.saas.store.reserve.intergration.table.TradeClientService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

/**
 * 商家接受预定
 */
@Slf4j
@Component
@RequiredArgsConstructor
@CustomerRegister(isRegister = true)
public class AcceptEventHandler extends SaveEventHanlder<AcceptEvent> implements CustomerObserver<AcceptEvent> {

    private final OrganizationClientService organizationClientService;

    private final MessageClientService messageClientService;

    private final ExecutorService acceptAfterExecutor;

    private final TradeClientService tradeClientService;

    @Override
    public void execute(AcceptEvent baseEvent) {
        // 预定许可
        ReserveRecord reserveRecord = baseEvent.getReserveRecord();
        if (!Objects.equals(ReserveRecordStateEnum.NO_PAY.getCode(), reserveRecord.getState())
                && !Objects.equals(ReserveRecordStateEnum.COMMIT.getCode(), reserveRecord.getState())) {
            throw new BusinessException("该订单状态发生变化，请刷新重试");
        }
        // 修改预定单预点餐商品
        saveReserveItems(baseEvent);
        // 修改预定单
        updateRecordDO(baseEvent);
        // 保存预定桌台
        if (Boolean.TRUE.equals(baseEvent.getSaveTable())) {
            saveTable(reserveRecord);
        }
        // 接单后置处理
        acceptAfterHandler(baseEvent);
    }


    /**
     * 修改预定单
     */
    private void updateRecordDO(AcceptEvent baseEvent) {
        ReserveRecord reserveRecord = baseEvent.getReserveRecord();
        // 修改数据
        ReserveRecordDo reserveRecordDO = new ReserveRecordDo();
        reserveRecordDO.setReserveAmount(reserveRecord.getReserveAmount());
        reserveRecordDO.setState(ReserveRecordStateEnum.PASS.getCode());
        reserveRecordDO.setGuid(reserveRecord.getGuid());
        reserveRecordDO.setConfirmUserGuid(UserContextUtils.getUserGuid());
        reserveRecordDO.setConfirmUserName(UserContextUtils.getUserName());
        reserveRecordDO.setConfirmTime(LocalDateTime.now());
        reserveRecordDO.setItemsStr(reserveRecord.getItemsStr());
        reserveRecordDO.setDeviceId(reserveRecord.getDeviceId());
        reserveRecordDO.setModifiedStaffGuid(UserContextUtils.getUserGuid());
        reserveRecordDO.setModifiedStaffName(UserContextUtils.getUserName());
        reserveRecordDO.setGmtModified(LocalDateTime.now());
        reserveRecordDO.setOrderType(reserveRecord.getOrderType());
        if (!com.holderzone.framework.util.StringUtils.isEmpty(reserveRecord.getOrderGuid())) {
            reserveRecordDO.setMainOrderGuid(reserveRecord.getOrderGuid());
        }
        if (Objects.nonNull(reserveRecord.getReserveAmount())
                && reserveRecord.getReserveAmount().compareTo(BigDecimal.ZERO) > 0
                && BaseDeviceTypeEnum.TCD.getCode() != reserveRecord.getState()) {
            // 一体机有预订金，通过表示支付成功
            reserveRecordDO.setPaymentTime(LocalDateTime.now());
        }
        // copy
        reserveRecord.setState(reserveRecordDO.getState());
        reserveRecord.setConfirmUserName(reserveRecordDO.getConfirmUserName());
        reserveRecord.setConfirmUserGuid(reserveRecordDO.getConfirmUserGuid());
        reserveRecord.setConfirmTime(reserveRecordDO.getConfirmTime());
        reserveRecord.setModifiedStaffGuid(reserveRecordDO.getModifiedStaffGuid());
        reserveRecord.setModifiedStaffName(reserveRecordDO.getModifiedStaffName());
        reserveRecord.setPaymentTime(reserveRecordDO.getPaymentTime());
        if (Objects.equals(reserveRecord.getOrderType(), OrderTypeEnum.RESERVE_PAY.getCode())) {
            reserveRecord.setState(ReserveRecordStateEnum.OPEN_TABLE.getCode());
            reserveRecord.setOrderType(reserveRecord.getOrderType());
        }
        // update
        reserveRecordDoMapper.update(reserveRecordDO,
                new LambdaQueryWrapper<ReserveRecordDo>()
                        .eq(ReserveRecordDo::getGuid, reserveRecordDO.getGuid()));
    }

    /**
     * 修改预定单预点餐商品
     */
    private void saveReserveItems(AcceptEvent baseEvent) {
        ReserveRecord reserveRecord = baseEvent.getReserveRecord();
        if (CollectionUtils.isEmpty(reserveRecord.getItems())) {
            return;
        }
        // 删除原预订关联的商品并插入新的
        reserveItemService.deleteReserveItems(reserveRecord.getGuid());
        reserveItemService.saveReserveItems(reserveRecord);
        reserveRecord.setItemsStr(JacksonUtils.writeValueAsString(
                reserveRecord.getItems().stream().map(Item::getDetail).collect(Collectors.toList())));
    }

    /**
     * 接单后置处理
     */
    private void acceptAfterHandler(AcceptEvent baseEvent) {
        ReserveRecord reserveRecord = baseEvent.getReserveRecord();
        if (Objects.equals(reserveRecord.getOrderType(), OrderTypeEnum.RESERVE_PAY.getCode())) {
            UpdateOrderReserveReqDTO reqDTO = new UpdateOrderReserveReqDTO();
            reqDTO.setOrderGuid(reserveRecord.getOrderGuid());
            reqDTO.setReserveAmount(reserveRecord.getReserveAmount());
            reqDTO.setReserveGuid(reserveRecord.getGuid());
            reqDTO.setStoreGuid(reserveRecord.getStoreGuid());
            tradeClientService.updateOrderReserve(reqDTO);
            return;
        }
        // 预定未锁定
        List<String> old = Collections.emptyList();
        List<String> neo = reserveRecord.getTables().stream().map(Table::getGuid).collect(Collectors.toList());
        PreparedLockTableEvent preparedLockTableEvent = new PreparedLockTableEvent(reserveRecord, old, neo);
        customerPublish.publish(new CustomerEvent<>(preparedLockTableEvent));
        // 延时锁桌
        DelayEvent delayEvent = new DelayEvent(reserveRecord, Consume.LOCK_TAG);
        customerPublish.publish(new CustomerEvent<>(delayEvent));
        DelayEvent cancelEvent = new DelayEvent(reserveRecord, Consume.BE_DELAY_TAG);
        customerPublish.publish(new CustomerEvent<>(cancelEvent));
        if (Boolean.TRUE.equals(reserveRecord.getConfig().getIsEnableSuccessMessage())) {
            ShortMessageEvent warnShortMessageEvent = new ShortMessageEvent(reserveRecord, e -> {
                Map<String, String> params = new HashMap<>();
                // 门店名称(区域名称-桌台名称) 如果没有桌台，则只展示门店名称
                String storeNameAndAreaName = getStoreNameAndTableName(reserveRecord);
                params.put("storeName", storeNameAndAreaName);
                params.put("reserveT", e.getReserveStartTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")));
                params.put("totalPeople", e.getNumber() + "");
                return params;
            }, () -> ShortMessageType.RESERVE_NOTIFY);
            customerPublish.publish(new CustomerEvent<>(warnShortMessageEvent));
        }
        // 延时发送短信
        if (Boolean.TRUE.equals(reserveRecord.getConfig().getIsEnableWarnMessage())) {
            DelayEvent warnEvent = new DelayEvent(reserveRecord, Consume.WARN_MESSAGE_TAG);
            customerPublish.publish(new CustomerEvent<>(warnEvent));
        }
        if (BaseDeviceTypeEnum.isApplet(reserveRecord.getDeviceType())) {
            // 小程序预定单接单推送
            UserContext userContext = UserContextUtils.get();
            acceptAfterExecutor.execute(() -> {
                UserContextUtils.put(userContext);
                EnterpriseIdentifier.setEnterpriseGuid(userContext.getEnterpriseGuid());
                SystemMessageEvent systemMessageEvent = new SystemMessageEvent(reserveRecord);
                customerPublish.publish(new CustomerEvent<>(systemMessageEvent));
            });
        }
        if (!BaseDeviceTypeEnum.isApplet(reserveRecord.getDeviceType())) {
            // 发送消息到pos
            sendMsgByPos(reserveRecord);
        }
    }

    /**
     * 发送消息到pos
     */
    private void sendMsgByPos(ReserveRecord reserveRecord) {
        // 查询门店名称
        StoreDTO storeDTO = organizationClientService.queryStoreByGuid(reserveRecord.getStoreGuid());
        BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setMessageType(BusinessMsgTypeEnum.TABLE_STATE_MSG_TYPE.getId());
        businessMessageDTO.setPlatform(String.valueOf(OrderType.OTHER_ORDER.getType()));
        businessMessageDTO.setStoreGuid(reserveRecord.getStoreGuid());
        businessMessageDTO.setStoreName(storeDTO.getName());
        businessMessageDTO.setDetailMessageType(BusinessMsgTypeEnum.RESERVE_RECORD_STATE_CHANGE_POS.getId());
        businessMessageDTO.setSubject("");
        ReserveSystemMsgDTO reserveSystemMsgDTO = new ReserveSystemMsgDTO();
        reserveSystemMsgDTO.setGuid(reserveRecord.getGuid());
        reserveSystemMsgDTO.setAutoRev(0);
        businessMessageDTO.setContent(JacksonUtils.writeValueAsString(reserveSystemMsgDTO));
        businessMessageDTO.setCreateTime(LocalDateTime.now());
        businessMessageDTO.setPushTime(LocalDateTime.now());
        log.info("[AcceptEvent][POS]预定消息推送入参:{}", JacksonUtils.writeValueAsString(businessMessageDTO));
        messageClientService.msg(businessMessageDTO);
    }

    @Override
    protected void pre(AcceptEvent baseEvent) {
        ReserveRecord reserveRecord = baseEvent.getReserveRecord();
        boolean result = RedissonLockUtil.tryLock(reserveRecord.getGuid(), 3, 10);
        if (!result) {
            throw new BusinessException("当前预定记录正被其他人操作,请稍候重试!!");
        }
        super.pre(baseEvent);
    }

    @Override
    protected void after(AcceptEvent baseEvent) {
        ReserveRecord reserveRecord = baseEvent.getReserveRecord();
        RedissonLockUtil.unlock(reserveRecord.getGuid());
        super.after(baseEvent);
    }


    /**
     * 获取门店名称和桌台名称
     */
    private String getStoreNameAndTableName(ReserveRecord acceptReserveRecord) {
        StoreDTO storeDTO = organizationClientService.queryStoreByGuid(acceptReserveRecord.getStoreGuid());
        String name = storeDTO.getName();
        Collection<Table> tables = acceptReserveRecord.getTables();
        if (CollectionUtils.isEmpty(tables)) {
            return name;
        }
        List<String> tableNameList = tables.stream()
                .map(e -> e.getAreaName() + "-" + e.getName())
                .collect(Collectors.toList());
        return name + "(" + generateTableName(tableNameList) + ")";
    }

    /**
     * 构建桌台列表
     */
    private String generateTableName(List<String> tableNameList) {
        int originalSize = tableNameList.size();
        StringBuilder acceptTableStr = new StringBuilder();
        if (tableNameList.size() >= ReserveConstant.RESERVE_SMS_MULTI_TABLE_COUNT) {
            tableNameList = tableNameList.subList(0, ReserveConstant.RESERVE_SMS_MULTI_TABLE_COUNT);
        }
        for (int i = 0; i < tableNameList.size(); i++) {
            acceptTableStr.append(tableNameList.get(i));
            if (i != tableNameList.size() - 1) {
                acceptTableStr.append("、");
            }
        }
        if (originalSize >= ReserveConstant.RESERVE_SMS_MULTI_TABLE_COUNT) {
            acceptTableStr.append(String.format(ReserveConstant.RESERVE_SMS_MULTI_TABLE_TEMPLATE, originalSize));
        }
        return acceptTableStr.toString();
    }
}