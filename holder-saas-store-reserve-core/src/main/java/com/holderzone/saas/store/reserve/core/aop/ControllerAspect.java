package com.holderzone.saas.store.reserve.core.aop;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.common.BaseDTO;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ControllerAspect
 * @date 2018/10/16 上午10:56
 * @description //controller切面
 * @program holder-saas-aggregation-merchant
 */
@Slf4j
@Aspect
@Component
@SuppressWarnings("Duplicates")
public class ControllerAspect {

    @Pointcut("execution(* com.holderzone.saas.store.reserve.core.controller.*.*(..))")
    public void pointCut() {
    }

    @Before("pointCut()")
    public void doBefore(JoinPoint joinPoint) {
        Object[] args = joinPoint.getArgs();

        if (args != null && args.length > 0) {
            UserContext userContext = UserContextUtils.get();
            if (userContext != null) {
                // UserInfo 日志
                log.info("userInfo: {}", UserContextUtils.getJsonStr());
                // BaseDTO 填充
                for (Object arg : args) {
                    if (arg instanceof BaseDTO) {
                        BaseDTO baseDTO = (BaseDTO) arg;
                        Optional.ofNullable(userContext.getEnterpriseGuid()).ifPresent(baseDTO::setEnterpriseGuid);
                        Optional.ofNullable(userContext.getEnterpriseName()).ifPresent(baseDTO::setEnterpriseName);
                        Optional.ofNullable(userContext.getStoreGuid()).ifPresent(baseDTO::setStoreGuid);
                        Optional.ofNullable(userContext.getStoreName()).ifPresent(baseDTO::setStoreName);
                        Optional.ofNullable(userContext.getUserGuid()).ifPresent(baseDTO::setUserGuid);
                        Optional.ofNullable(userContext.getUserName()).ifPresent(baseDTO::setUserName);
                        Optional.ofNullable(userContext.getAccount()).ifPresent(baseDTO::setAccount);
                    }
                }
            }
        }

        String params = "";
        if (args != null && args.length > 0) {
            StringBuilder stringBuilder = new StringBuilder();
            for (Object object : args) {
                // HttpServletRequest 和 HttpServletResponse 作为 controller 参数
                if (object instanceof HttpServletRequest) {
                    HttpServletRequest request = (HttpServletRequest) object;
                    stringBuilder.append(JacksonUtils.writeValueAsString(request.getParameterMap())).append(",");
                    continue;
                }
                if (object instanceof HttpServletResponse) {
                    continue;
                }
                stringBuilder.append(JacksonUtils.writeValueAsString(object)).append(",");
            }
            params = stringBuilder.toString();
        }
        if (params.length() > 0) {
            params = params.substring(0, params.length() - 1);
        }
        String controllerName = joinPoint.getTarget().getClass().getName();
        int lastIndexOfPoint = controllerName.lastIndexOf(".");
        if (lastIndexOfPoint > -1) {
            controllerName = controllerName.substring(lastIndexOfPoint + 1);
        }
        String methodName = joinPoint.getSignature().getName();
        log.info("{}#{}接口入参: {}", controllerName, methodName, params);
    }
}
