package com.holderzone.saas.store.reserve.core.config;

import com.alibaba.fastjson.support.spring.FastJsonRedisSerializer;
import com.holderzone.resource.common.dto.mq.UnMessage;
import com.holderzone.saas.store.reserve.core.rocket.DelayTaskConsumer;
import org.apache.curator.framework.CuratorFramework;
import org.apache.curator.framework.CuratorFrameworkFactory;
import org.apache.curator.framework.recipes.queue.DistributedDelayQueue;
import org.apache.curator.framework.recipes.queue.QueueBuilder;
import org.apache.curator.framework.recipes.queue.QueueSerializer;
import org.apache.curator.retry.ExponentialBackoffRetry;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ZKConfig
 * @date 2019/05/30 17:22
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Configuration
public class ZKConfig {
    @Value("${zk.hosts}")
    private String hosts;
    private static final String QUEUE_PATH = "/reserve/delay/queue";
    private static final String LOCK_PATH = "/reserve/delay/look";

    @Bean
    @ConditionalOnMissingBean
    public CuratorFramework curatorFramework() {
        CuratorFrameworkFactory.Builder builder = CuratorFrameworkFactory.builder().connectString(hosts).retryPolicy(new ExponentialBackoffRetry(1000, 3, 3000)).namespace("reserve");
            builder.sessionTimeoutMs(60000);
        builder.connectionTimeoutMs(15000);
        CuratorFramework curatorFramework = builder.build();
        curatorFramework.start();
        try {
            if (!curatorFramework.blockUntilConnected(3000 * 3, TimeUnit.MILLISECONDS)) {
                curatorFramework.close();
                throw new RuntimeException("connectionTimeOut");
            }
        } catch (InterruptedException e) {
            throw new RuntimeException(e.getMessage());
        }

        return curatorFramework;
    }

    @Bean
    @Primary
    public DelayTaskConsumer delayTaskConsumer() {
        return new DelayTaskConsumer();
    }

    @Bean
    public DistributedDelayQueue delayQueue(CuratorFramework curatorFramework, DelayTaskConsumer delayTaskConsumer) throws Exception {
        FastJsonRedisSerializer<UnMessage> jackson2JsonRedisSerializer = new FastJsonRedisSerializer(UnMessage.class);
        QueueSerializer queueSerializer = new QueueSerializer<UnMessage>() {
            @Override
            public byte[] serialize(UnMessage o) {
                return jackson2JsonRedisSerializer.serialize(o);
            }

            @Override
            public UnMessage deserialize(byte[] bytes) {
                return jackson2JsonRedisSerializer.deserialize(bytes);
            }
        };
        DistributedDelayQueue queue = QueueBuilder.builder(curatorFramework, delayTaskConsumer,queueSerializer, QUEUE_PATH)
                .executor(
                        new ThreadPoolExecutor(
                                4, 10, 1, TimeUnit.MINUTES,
                                new LinkedBlockingQueue<>(), (e) -> new Thread(e, "reserve_delay_executor")
                        )
                ).lockPath(LOCK_PATH)
//                .putInBackground(false)
                .buildDelayQueue();
        return queue;
    }
}