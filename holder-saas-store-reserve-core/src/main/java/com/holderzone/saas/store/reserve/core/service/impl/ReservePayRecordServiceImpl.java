package com.holderzone.saas.store.reserve.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.saas.store.reserve.api.dto.ReservePayReqDTO;
import com.holderzone.saas.store.reserve.api.enums.PayStateEnum;
import com.holderzone.saas.store.reserve.core.dal.ReservePayRecordDO;
import com.holderzone.saas.store.reserve.core.dal.mapper.ReservePayRecordDoMapper;
import com.holderzone.saas.store.reserve.core.service.ReservePayRecordService;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReservePayRecordServiceImpl
 * @date 2019/12/06 18:12
 * @description //TODO
 * @program IdeaProjects
 */
@Service
public class ReservePayRecordServiceImpl extends ServiceImpl<ReservePayRecordDoMapper, ReservePayRecordDO> implements ReservePayRecordService {

    /**
     * 转换支付DO
     * @param reservePayReqDTO
     * @param stateEnum
     * @return
     */
    @Override
    public ReservePayRecordDO savePayRecordDO(ReservePayReqDTO reservePayReqDTO, PayStateEnum stateEnum, String payGuid){
        ReservePayRecordDO reservePayRecordDO = makePayRecordDO(reservePayReqDTO, stateEnum,payGuid);
        save(reservePayRecordDO);
        return reservePayRecordDO;
    }

    /**
     * 更换状态和msg
     * @param payGuid
     * @param stateEnum
     * @param msg
     * @return
     */
    @Override
    public boolean updatePayRecordStateAndInfo(String payGuid, PayStateEnum stateEnum,String msg){
        ReservePayRecordDO reservePayRecordDO = new ReservePayRecordDO();
        reservePayRecordDO.setGuid(payGuid);
        reservePayRecordDO.setState(stateEnum.getCode());
        reservePayRecordDO.setFailMsg(msg);
        return update(reservePayRecordDO,new QueryWrapper<ReservePayRecordDO>().lambda().eq(ReservePayRecordDO::getGuid, payGuid));
    }


    @Override
    public ReservePayRecordDO selectOneByGuid(String guid){
        return getOne(new QueryWrapper<ReservePayRecordDO>().lambda().eq(ReservePayRecordDO::getGuid, guid));
    }

    @Override
    public ReservePayRecordDO selectOneByReserveGuid(String reserveGuid) {
        return getOne(new QueryWrapper<ReservePayRecordDO>().lambda()
                .eq(ReservePayRecordDO::getReserveGuid, reserveGuid)
                .eq(ReservePayRecordDO::getState, PayStateEnum.SUCCESS.getCode()));
    }

    @Override
    public boolean updatePayStateByReserveGuid(String reserveGuid,Integer beforeState,Integer afterState) {
        return update(new ReservePayRecordDO(), new UpdateWrapper<ReservePayRecordDO>().lambda().set(ReservePayRecordDO::getState, afterState)
        .eq(ReservePayRecordDO::getReserveGuid, reserveGuid).eq(ReservePayRecordDO::getState, beforeState));
    }

    //  =================================  private  ==================================
    private ReservePayRecordDO makePayRecordDO(ReservePayReqDTO reservePayReqDTO, PayStateEnum stateEnum, String payGuid) {
        ReservePayRecordDO reservePayRecordDO = new ReservePayRecordDO();
        reservePayRecordDO.setGuid(payGuid);
        reservePayRecordDO.setStoreGuid(StringUtils.isEmpty(UserContextUtils.getStoreGuid()) ? reservePayReqDTO.getStoreGuid() : UserContextUtils.getStoreGuid());
        reservePayRecordDO.setReserveGuid(reservePayReqDTO.getGuid());
        reservePayRecordDO.setPayType(reservePayReqDTO.getPaymentType());
        reservePayRecordDO.setReserveAmount(reservePayReqDTO.getReserveAmount());
        reservePayRecordDO.setState(stateEnum.getCode());
        reservePayRecordDO.setPayTypeName(reservePayReqDTO.getPaymentTypeName());
        reservePayRecordDO.setMemberFundingDetailGuid(reservePayReqDTO.getMemberFundingDetailGuid());
        return reservePayRecordDO;
    }
}