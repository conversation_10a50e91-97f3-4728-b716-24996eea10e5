package com.holderzone.saas.store.reserve.core.common;

import com.holderzone.framework.event.CustomerEvent;
import com.holderzone.framework.event.observer.CustomerObserver;
import com.holderzone.framework.event.publish.impl.CustomerPublishImpl;
import sun.reflect.generics.reflectiveObjects.ParameterizedTypeImpl;

import java.lang.reflect.Type;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SyncPublishImpl
 * @date 2019/05/05 15:42
 * @description //TODO
 * @program holder-saas-store-reserve
 */
public class SyncPublishImpl extends CustomerPublishImpl {
    @Override
    public void publish(CustomerEvent customerEvent) {
        if (customerEvent != null) {
            get().stream().filter((customerObserver) -> {
                Type[] genericInterfaces = customerObserver.getClass().getGenericInterfaces();
                return Arrays.stream(genericInterfaces).map((type) -> {
                    ParameterizedTypeImpl actuallyType = (ParameterizedTypeImpl)type;
                    return actuallyType.getRawType().getName().equals(CustomerObserver.class.getName()) ? type : null;
                }).anyMatch((observer) -> {
                    return null != observer && null != customerEvent.getSource() ? ((ParameterizedTypeImpl)observer).getActualTypeArguments()[0].getTypeName().equals(customerEvent.getSource().getClass().getName()) : false;
                });
            }).forEach((customerObserver) -> {
                customerObserver.notify(customerEvent);
            });
        }
    }
}