package com.holderzone.saas.store.reserve.core.event.impl.event;

import com.holderzone.saas.store.reserve.core.domain.ReserveRecord;
import com.holderzone.saas.store.reserve.core.event.BaseEvent;
import lombok.Getter;


@Getter
public class AggPayEvent extends BaseEvent {


    public AggPayEvent(ReserveRecord reserveRecord) {
        super(reserveRecord);
    }

    private static final String NAME = AggPayEvent.class.getSimpleName();

    @Override
    public String getName() {
        return NAME;
    }
}