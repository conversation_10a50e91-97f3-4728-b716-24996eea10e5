package com.holderzone.saas.store.reserve.core.service;

import com.holderzone.saas.store.reserve.api.dto.ReservePayReqDTO;
import com.holderzone.saas.store.reserve.api.enums.PayStateEnum;
import com.holderzone.saas.store.reserve.core.dal.ReservePayRecordDO;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReservePayRecordService
 * @date 2019/12/06 18:11
 * @description //TODO
 * @program IdeaProjects
 */
public interface ReservePayRecordService {
    ReservePayRecordDO savePayRecordDO(ReservePayReqDTO reservePayReqDTO, PayStateEnum stateEnum, String payGuid);

    boolean updatePayRecordStateAndInfo(String payGuid, PayStateEnum stateEnum, String msg);

    ReservePayRecordDO selectOneByGuid(String guid);

    ReservePayRecordDO selectOneByReserveGuid(String reserveGuid);

    boolean updatePayStateByReserveGuid(String reserveGuid, Integer beforeState, Integer afterState);
}