package com.holderzone.saas.store.reserve.core.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.message.BusinessMessageDTO;
import com.holderzone.saas.store.dto.pay.*;
import com.holderzone.saas.store.dto.pay.constant.AggPayStateEnum;
import com.holderzone.saas.store.dto.reserve.ReservePayDTO;
import com.holderzone.saas.store.dto.reserve.ReservePayStatisticReqDTO;
import com.holderzone.saas.store.dto.reserve.ReserveRecordGuidDTO;
import com.holderzone.saas.store.dto.reserve.TableDTO;
import com.holderzone.saas.store.enums.BaseDeviceTypeEnum;
import com.holderzone.saas.store.enums.PaymentTypeEnum;
import com.holderzone.saas.store.enums.msg.BusinessMsgTypeEnum;
import com.holderzone.saas.store.reserve.api.ReservePaymentApi;
import com.holderzone.saas.store.reserve.api.dto.ReservePayReqDTO;
import com.holderzone.saas.store.reserve.api.dto.ReservePayRespDTO;
import com.holderzone.saas.store.reserve.api.dto.ReserveRecordDTO;
import com.holderzone.saas.store.reserve.api.dto.ReserveRecordDetailDTO;
import com.holderzone.saas.store.reserve.api.enums.OrderTypeEnum;
import com.holderzone.saas.store.reserve.api.enums.PayStateEnum;
import com.holderzone.saas.store.reserve.core.common.ReserveConstant;
import com.holderzone.saas.store.reserve.core.common.ReserveUtils;
import com.holderzone.saas.store.reserve.core.dal.ReservePayRecordDO;
import com.holderzone.saas.store.reserve.core.dal.ReserveRecordDo;
import com.holderzone.saas.store.reserve.core.dal.ReserveRecordTableRelationDo;
import com.holderzone.saas.store.reserve.core.dal.mapper.ReservePayRecordDoMapper;
import com.holderzone.saas.store.reserve.core.dal.mapper.ReserveRecordDoMapper;
import com.holderzone.saas.store.reserve.core.dal.mapper.ReserveRecordTableRelationDoMapper;
import com.holderzone.saas.store.reserve.core.domain.ReserveRecord;
import com.holderzone.saas.store.reserve.core.domain.ReserveRecordStateEnum;
import com.holderzone.saas.store.reserve.core.mapstruct.ReserveRecordMapstruct;
import com.holderzone.saas.store.reserve.core.service.PrintService;
import com.holderzone.saas.store.reserve.core.service.ReservePayRecordService;
import com.holderzone.saas.store.reserve.core.service.ReserveRecordService;
import com.holderzone.saas.store.reserve.intergration.table.AggPayClientService;
import com.holderzone.saas.store.reserve.intergration.table.MessageClientService;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.annotation.Primary;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2019/12/06 16:38
 */
@Slf4j
@Primary
@RestController
public class ReservePayControllerImpl implements ReservePaymentApi {

    private final AggPayClientService aggPayClientService;

    private final ReserveRecordService reserveRecordService;

    private final ReservePayRecordService reservePayRecordService;

    private final ReservePayRecordDoMapper reservePayRecordDoMapper;

    private final MessageClientService messageClientService;

    private final ReserveRecordDoMapper reserveRecordDoMapper;

    private final ReserveRecordTableRelationDoMapper tableRelationDoMapper;

    private final PrintService printService;

    public ReservePayControllerImpl(AggPayClientService aggPayClientService,
                                    ReserveRecordService reserveRecordService,
                                    ReservePayRecordService reservePayRecordService,
                                    ReservePayRecordDoMapper reservePayRecordDoMapper,
                                    MessageClientService messageClientService,
                                    ReserveRecordDoMapper reserveRecordDoMapper,
                                    ReserveRecordTableRelationDoMapper tableRelationDoMapper,
                                    PrintService printService) {
        this.aggPayClientService = aggPayClientService;
        this.reserveRecordService = reserveRecordService;
        this.reservePayRecordService = reservePayRecordService;
        this.reservePayRecordDoMapper = reservePayRecordDoMapper;
        this.messageClientService = messageClientService;
        this.reserveRecordDoMapper = reserveRecordDoMapper;
        this.tableRelationDoMapper = tableRelationDoMapper;
        this.printService = printService;
    }

    /**
     * 支付呀
     * attention: 事务这个地方暂不生效
     */
    @Override
    public ReservePayRespDTO pay(@RequestBody ReservePayReqDTO reservePayReqDTO) {
        Assert.notNull(reservePayReqDTO.getPaymentType(), "请传入支付方式");
        cancelReserveOrder(reservePayReqDTO);

        String payGuid = ReserveUtils.reservePayGuid();
        //预订正常流程
        ReserveRecordDetailDTO launch = reserveRecordService.launch(reservePayReqDTO, ReserveRecordStateEnum.NO_PAY);
        if (reservePayReqDTO.getPaymentType() == PaymentTypeEnum.AGG.getCode()) {
            return aggPay(reservePayReqDTO, payGuid, launch.getGuid());
        }
        //通过
        ReserveRecordGuidDTO guidDTO = new ReserveRecordGuidDTO(launch.getGuid());
        guidDTO.setOrderType(reservePayReqDTO.getOrderType());
        guidDTO.setOrderGuid(reservePayReqDTO.getOrderGuid());
        guidDTO.setStoreGuid(reservePayReqDTO.getStoreGuid());
        guidDTO.setReserveAmount(reservePayReqDTO.getReserveAmount());
        reserveRecordService.pass(guidDTO);
        //存支付记录
        reservePayRecordService.savePayRecordDO(reservePayReqDTO, PayStateEnum.SUCCESS, payGuid);
        // 发送桌位状态变化通知
        sendTableMsg(reservePayReqDTO);
        return new ReservePayRespDTO(payGuid, launch.getGuid());
    }

    private void cancelReserveOrder(ReservePayReqDTO reservePayReqDTO) {
        // 预付金，先查询该桌台前面是否有预定单，有则先撤销（针对预定金为0，然后支付的情况）
        if (Objects.equals(reservePayReqDTO.getOrderType(), OrderTypeEnum.RESERVE_PAY.getCode())
                && StringUtils.hasText(reservePayReqDTO.getGuid())) {
            ReserveRecordDTO recordDTO = reserveRecordService.queryByGuid(reservePayReqDTO.getGuid());
            log.info("[支付前撤销预定单]recordDTO={}", JacksonUtils.writeValueAsString(recordDTO));
            if (Objects.isNull(recordDTO) || recordDTO.getOrderType().equals(OrderTypeEnum.RESERVE_PAY.getCode())) {
                log.warn("数据异常不进入撤销流程");
                return;
            }
            ReserveRecordGuidDTO guidDTO = buildCancelReq(reservePayReqDTO, recordDTO);
            reserveRecordService.cancle(guidDTO);
        }
    }

    @NotNull
    private ReserveRecordGuidDTO buildCancelReq(ReservePayReqDTO reservePayReqDTO,
                                                ReserveRecordDTO recordDTO) {
        ReserveRecordGuidDTO guidDTO = new ReserveRecordGuidDTO();
        guidDTO.setGuid(reservePayReqDTO.getGuid());
        guidDTO.setReason("");
        guidDTO.setTables(reservePayReqDTO.getTables());
        guidDTO.setOrderType(reservePayReqDTO.getOrderType());
        guidDTO.setOrderGuid(reservePayReqDTO.getOrderGuid());
        guidDTO.setReserveRefundAmount(recordDTO.getReserveAmount());
        guidDTO.setDeviceId(reservePayReqDTO.getDeviceId());
        guidDTO.setEnterpriseGuid(reservePayReqDTO.getEnterpriseGuid());
        guidDTO.setEnterpriseName(reservePayReqDTO.getEnterpriseName());
        guidDTO.setStoreGuid(reservePayReqDTO.getStoreGuid());
        guidDTO.setStoreName(reservePayReqDTO.getStoreName());
        guidDTO.setUserGuid(reservePayReqDTO.getUserGuid());
        guidDTO.setUserName(reservePayReqDTO.getUserName());
        guidDTO.setRequestTimestamp(reservePayReqDTO.getRequestTimestamp());
        guidDTO.setDeviceType(BaseDeviceTypeEnum.All_IN_ONE.getCode());
        return guidDTO;
    }

    private void sendTableMsg(ReservePayReqDTO reservePayReqDTO) {
        if (Objects.equals(reservePayReqDTO.getOrderType(), OrderTypeEnum.RESERVE.getCode())) {
            return;
        }
        List<String> tableGuidList = reservePayReqDTO.getTables().stream()
                .map(TableDTO::getGuid)
                .distinct()
                .collect(Collectors.toList());
        BusinessMessageDTO messageDTO = BusinessMessageDTO.builder()
                .subject("桌位状态变化通知")
                .messageType(BusinessMsgTypeEnum.TABLE_STATE_MSG_TYPE.getId())
                .detailMessageType(BusinessMsgTypeEnum.TABLE_CHANGED.getId())
                .content(JacksonUtils.writeValueAsString(tableGuidList))
                .platform("2")
                .storeGuid(reservePayReqDTO.getStoreGuid())
                .storeName(reservePayReqDTO.getStoreName())
                .build();
        messageClientService.msg(messageDTO);
    }

    /**
     * 回调
     */
    @Override
    public String callback(@RequestBody SaasNotifyDTO saasNotifyDTO) {
        log.info("聚合支付回调，saasNotifyDTO：{}", JacksonUtils.writeValueAsString(saasNotifyDTO));
        AggPayPollingRespDTO aggPayPollingRespDTO = saasNotifyDTO.getAggPayPollingRespDTO();
        if (aggPayPollingRespDTO.getPayGUID() == null) {
            log.error("数据有误");
            return ReserveConstant.SUCCESS_MSG;
        }
        ReservePayRecordDO reservePayRecordDO = reservePayRecordService.selectOneByGuid(aggPayPollingRespDTO.getPayGUID());
        if (ReserveConstant.SUCCESS_CODE.equals(aggPayPollingRespDTO.getCode()) && aggPayPollingRespDTO.getPaySt()
                .equals(AggPayStateEnum.SUCCESS.getId())) {
            // 支付成功处理
            successPayHandler(saasNotifyDTO, reservePayRecordDO);
        } else {
            // 支付失败处理
            failurePayHandler(saasNotifyDTO, reservePayRecordDO);
        }
        return ReserveConstant.SUCCESS_MSG;
    }

    /**
     * 支付成功处理
     */
    private void successPayHandler(SaasNotifyDTO saasNotifyDTO, ReservePayRecordDO reservePayRecordDO) {
        AggPayPollingRespDTO aggPayPollingRespDTO = saasNotifyDTO.getAggPayPollingRespDTO();
        // 校验不通过 直接不处理
        if (!validatAggBackData(reservePayRecordDO, aggPayPollingRespDTO)) {
            return;
        }
        if (BaseDeviceTypeEnum.isApplet(saasNotifyDTO.getBaseInfo().getDeviceType())) {
            // 更新预定单状态为支付成功
            ReserveRecordGuidDTO reserveRecordGuidDTO = new ReserveRecordGuidDTO();
            reserveRecordGuidDTO.setGuid(reservePayRecordDO.getReserveGuid());
            reserveRecordGuidDTO.setPayGuid(reservePayRecordDO.getGuid());
            reserveRecordService.aggPay(reserveRecordGuidDTO);
        } else {
            // 更改预定支付记录状态
            reservePayRecordService.updatePayRecordStateAndInfo(reservePayRecordDO.getGuid(), PayStateEnum.SUCCESS, null);
            // 如果一体机 则直接预定通过
            ReserveRecordGuidDTO guidDTO = new ReserveRecordGuidDTO(reservePayRecordDO.getReserveGuid());
            guidDTO.setReserveAmount(aggPayPollingRespDTO.getAmount());
            reserveRecordService.pass(guidDTO);
            // 1.发送桌位状态变化通知 2.打印预定单
            handleAggPaySuccess(reservePayRecordDO);
        }
    }

    /**
     * 支付成功处理
     * 1.发送桌位状态变化通知 2.打印预定单
     *
     * @param reservePayRecordDO 预订单
     */
    @SuppressWarnings("unchecked")
    private void handleAggPaySuccess(ReservePayRecordDO reservePayRecordDO) {
        ReserveRecordDo recordDo = reserveRecordDoMapper.selectOne(
                new LambdaQueryWrapper<ReserveRecordDo>()
                        .eq(ReserveRecordDo::getGuid, reservePayRecordDO.getReserveGuid())
        );
        if (Objects.nonNull(recordDo)) {
            List<ReserveRecordTableRelationDo> tableRelationDoList = tableRelationDoMapper.selectList(
                    new LambdaQueryWrapper<ReserveRecordTableRelationDo>()
                            .eq(ReserveRecordTableRelationDo::getReserveRecordGuid, reservePayRecordDO.getReserveGuid())
                            .orderByDesc(ReserveRecordTableRelationDo::getGuid)
            );
            // 发送桌位状态变化通知
            aggPaySendTableMsg(tableRelationDoList, recordDo);
            // 打印预定单
            aggPrintReservePay(recordDo);
        }
    }

    /**
     * 发送桌位状态变化通知
     * @param tableRelationDoList 桌位关系
     * @param recordDo 预定单
     */
    private void aggPaySendTableMsg(List<ReserveRecordTableRelationDo> tableRelationDoList, ReserveRecordDo recordDo) {
        List<TableDTO> tableDTOList = tableRelationDoList.stream().map(tr -> {
            TableDTO tableDTO = new TableDTO();
            tableDTO.setGuid(tr.getTableGuid());
            return tableDTO;
        }).collect(Collectors.toList());
        ReservePayReqDTO reqDTO = new ReservePayReqDTO();
        reqDTO.setStoreGuid(recordDo.getStoreGuid());
        reqDTO.setOrderType(recordDo.getOrderType());
        reqDTO.setTables(tableDTOList);
        sendTableMsg(reqDTO);
    }

    /**
     * 打印预定单
     * @param recordDo  预定单
     */
    private void aggPrintReservePay(ReserveRecordDo recordDo) {
        // 转换为领域对象
        ReserveRecord reserveRecord = ReserveRecordMapstruct.MAPSTRUCT.toDomain(recordDo);

        // 转换为 DTO 并返回
        ReserveRecordDetailDTO detailDTO = ReserveRecordMapstruct.MAPSTRUCT.toDetailDTO(reserveRecord);

        // 执行打印操作
        printService.printReservePay(detailDTO);
    }

    /**
     * 支付失败处理
     */
    private void failurePayHandler(SaasNotifyDTO saasNotifyDTO, ReservePayRecordDO reservePayRecordDO) {
        AggPayPollingRespDTO aggPayPollingRespDTO = saasNotifyDTO.getAggPayPollingRespDTO();
        if (PayStateEnum.FAILURE.getCode() == reservePayRecordDO.getState()) {
            //去重复提醒
            log.info("重复回调：{}", aggPayPollingRespDTO.getOrderGUID());
            return;
        }
        // 更改订单状态
        reservePayRecordService.updatePayRecordStateAndInfo(reservePayRecordDO.getGuid(), PayStateEnum.FAILURE, aggPayPollingRespDTO.getMsg());
        if (!BaseDeviceTypeEnum.isApplet(saasNotifyDTO.getBaseInfo().getDeviceType())) {
            log.warn("一体机预定聚合支付失败取消预定, saasNotifyDTO:{}", JacksonUtils.writeValueAsString(saasNotifyDTO));
            reserveRecordService.cancle(new ReserveRecordGuidDTO(reservePayRecordDO.getReserveGuid(), "定金支付失败"));
        }
    }


    @Override
    public String refund(SingleDataDTO singleDataDTO) {
        log.info("退款：{}", singleDataDTO.getData());
        boolean updateResult = reservePayRecordService.updatePayStateByReserveGuid(singleDataDTO.getData(),
                PayStateEnum.SUCCESS.getCode(), PayStateEnum.REFOUNDED.getCode());
        return updateResult ? ReserveConstant.SUCCESS_MSG : ReserveConstant.FAIL_MSG;
    }

    @Override
    public BigDecimal partRefund(@RequestBody ReservePayReqDTO reservePayReqDTO) {
        log.info("部分退款入参：{}", JacksonUtils.writeValueAsString(reservePayReqDTO));
        return reserveRecordService.partRefund(reservePayReqDTO);
    }

    /**
     * 校验聚合支付回调值，一般情况下不可能发生，但是要排查
     */
    private boolean validatAggBackData(ReservePayRecordDO reservePayRecordDO, AggPayPollingRespDTO aggPayPollingRespDTO) {
        if (reservePayRecordDO == null) {
            //不可能为空啊
            log.error("为什么为空?预定id为{}", aggPayPollingRespDTO.getOrderGUID());
            return false;
        }
        if (PayStateEnum.SUCCESS.getCode() == reservePayRecordDO.getState()) {
            //去重复提醒
            log.info("重复回调：{}", aggPayPollingRespDTO.getOrderGUID());
            return false;
        }
        if (aggPayPollingRespDTO.getAmount().compareTo(reservePayRecordDO.getReserveAmount()) != 0) {
            //金额有错误
            log.error(String.format("预定的金额有误,回调的金额为%.2f,但是预定的时候金额为%.2f,预定id为%s",
                    aggPayPollingRespDTO.getAmount().floatValue(),
                    reservePayRecordDO.getReserveAmount().floatValue(),
                    reservePayRecordDO.getReserveGuid()));
            return false;
        }
        return true;
    }

    /**
     * 聚合支付
     */
    private ReservePayRespDTO aggPay(ReservePayReqDTO reservePayReqDTO, String payGuid, String reserveGuid) {
        //支付记录
        SaasAggPayDTO aggPayDTO = makeAggPayData(reservePayReqDTO, payGuid);
        log.info("发起聚合支付：{}", aggPayDTO);
        AggPayRespDTO payRespDTO = aggPayClientService.pay(aggPayDTO);
        log.info("聚合支付返回结果：{}", payRespDTO);
        if (ReserveConstant.SUCCESS_CODE.equals(payRespDTO.getCode())) {
            reservePayRecordService.savePayRecordDO(reservePayReqDTO, PayStateEnum.PENDING, payGuid);
            return new ReservePayRespDTO(payRespDTO.getPayGuid(), reserveGuid);
        } else {
            reservePayRecordService.savePayRecordDO(reservePayReqDTO, PayStateEnum.FAILURE, payGuid);
            throw new RuntimeException(payRespDTO.getMsg());
        }
    }


    /**
     * 聚合支付data
     */
    private SaasAggPayDTO makeAggPayData(ReservePayReqDTO reservePayReqDTO, String payGuid) {
        //makeAggData
        SaasAggPayDTO aggPayDTO = new SaasAggPayDTO();
        AggPayPreTradingReqDTO payPreTradingReqDTO = AggPayPreTradingReqDTO.builder()
                .timestamp(System.currentTimeMillis()).storeName(UserContextUtils.getStoreName())
                .amount(reservePayReqDTO.getReserveAmount()).authCode(reservePayReqDTO.getAuthCode())
                .terminalId(reservePayReqDTO.getDeviceId()).goodsName("预订支付")
                .body("预订支付订单：" + reservePayReqDTO.getGuid()).orderGUID(reservePayReqDTO.getGuid())
                .payGUID(payGuid).build();
        aggPayDTO.setReqDTO(payPreTradingReqDTO);
        aggPayDTO.setSaasCallBackUrl(ReserveConstant.CALLBACK_URL);
        aggPayDTO.setIsQuickReceipt(Boolean.FALSE);
        aggPayDTO.setStoreGuid(reservePayReqDTO.getStoreGuid());
        aggPayDTO.setEnterpriseGuid(reservePayReqDTO.getEnterpriseGuid());
        return aggPayDTO;
    }

    @Override
    public List<ReservePayDTO> getPayStatics(@RequestBody ReservePayStatisticReqDTO reservePayStatisticReqDTO) {
        if (CollectionUtils.isEmpty(reservePayStatisticReqDTO.getStoreGuids())) {
            return Collections.emptyList();
        }
        return reservePayRecordDoMapper.getReserveStatistic(reservePayStatisticReqDTO.getStoreGuids(),
                reservePayStatisticReqDTO.getStartTime(),
                reservePayStatisticReqDTO.getEndTime());
    }
}