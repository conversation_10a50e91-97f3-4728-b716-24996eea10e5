package com.holderzone.saas.store.reserve.core.event.impl.handler;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.message.BusinessMessageDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.reserve.TableDTO;
import com.holderzone.saas.store.dto.table.BatchOpenTableDTO;
import com.holderzone.saas.store.dto.table.OpenTableDTO;
import com.holderzone.saas.store.dto.table.ReserveOpenTableDTO;
import com.holderzone.saas.store.dto.takeaway.OrderType;
import com.holderzone.saas.store.enums.BaseDeviceTypeEnum;
import com.holderzone.saas.store.enums.msg.BusinessMsgTypeEnum;
import com.holderzone.saas.store.reserve.api.dto.ReserveSystemMsgDTO;
import com.holderzone.saas.store.reserve.api.enums.OrderTypeEnum;
import com.holderzone.saas.store.reserve.core.common.TableNotAllSuccessException;
import com.holderzone.saas.store.reserve.core.dal.ReserveRecordDo;
import com.holderzone.saas.store.reserve.core.dal.mapper.ReserveRecordDoMapper;
import com.holderzone.saas.store.reserve.core.dal.mapper.ReserveRecordTableRelationDoMapper;
import com.holderzone.saas.store.reserve.core.domain.ReserveRecord;
import com.holderzone.saas.store.reserve.core.domain.ReserveRecordStateEnum;
import com.holderzone.saas.store.reserve.core.event.BaseEventHandler;
import com.holderzone.saas.store.reserve.core.event.impl.event.EffectiveEvent;
import com.holderzone.saas.store.reserve.core.lock.BatchRedisLock;
import com.holderzone.saas.store.reserve.core.lock.LockContext;
import com.holderzone.saas.store.reserve.core.lock.LockFailStrategy;
import com.holderzone.saas.store.reserve.core.lock.LockModeEnum;
import com.holderzone.saas.store.reserve.intergration.table.MessageClientService;
import com.holderzone.saas.store.reserve.intergration.table.OrganizationClientService;
import com.holderzone.saas.store.reserve.intergration.table.TableClientService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OpenTableEventHandler
 * @date 2019/05/30 15:25
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Slf4j
public abstract class TableOpenTableEventHandler<T extends EffectiveEvent> extends BaseEventHandler<T> {
    @Autowired
    ReserveRecordDoMapper reserveRecordDoMapper;
    @Autowired
    ReserveRecordTableRelationDoMapper tableRelationDoMapper;
    @Autowired
    TableClientService tableServiceService;
    @Autowired
    private OrganizationClientService organizationClientService;

    @Autowired
    private MessageClientService messageClientService;

    @Override
    protected void pre(T baseEvent) {
        Collection<TableDTO> tables = fetchTables(baseEvent);
        Map<String, TableDTO> tableRef = tables.stream().collect(Collectors.toMap(TableDTO::getGuid, Function.identity()));
        LockContext context = new LockContext(LockModeEnum.WHOLE, LockFailStrategy.FAIL_FAST);
        BatchRedisLock batchRedisLock = new BatchRedisLock(UserContextUtils.getUserGuid(), tableRef.keySet());
        batchRedisLock.lock(context);
        List<String> fails = Optional.ofNullable((List<String>) context.getOther().get(BatchRedisLock.RESULT_KEY))
                .orElse(Collections.emptyList());
        if (!fails.isEmpty()) {
            throw new BusinessException("请稍候重试");
        }
        baseEvent.setUnlock((e) -> batchRedisLock.unLock(context));
        super.pre(baseEvent);
    }

    protected abstract List<TableDTO> fetchTables(T baseEvent);

    @Override
    protected void after(T baseEvent) {
        ReserveRecord record = baseEvent.getReserveRecord();
        Optional.ofNullable(baseEvent.getUnlock()).orElse((e) -> {
        }).accept(record);
        super.pre(baseEvent);
    }

    @Override
    public void execute(T baseEvent) {
        ReserveRecord reserveRecord = baseEvent.getReserveRecord();
        List<TableDTO> tableDTOS = fetchTables(baseEvent);
        if (reserveRecord.getState() == ReserveRecordStateEnum.OPEN_TABLE.getCode()
                || reserveRecord.getState() == ReserveRecordStateEnum.PICK_TABLE.getCode()) {
            throw new BusinessException("订单状态发生变化，请刷新后重试");
        }
        Assert.isTrue(reserveRecord.getState() == ReserveRecordStateEnum.PASS.getCode(), "预定已取消不能开台");
        Assert.isTrue(tableDTOS != null && !tableDTOS.isEmpty(), "请选择桌台");
        ReserveOpenTableDTO reserveOpenTableDTO = doOpenTable(reserveRecord, tableDTOS, baseEvent);
        baseEvent.setOrderGuid(reserveOpenTableDTO.getMainOrderGuid());
        baseEvent.setTableGuid(reserveOpenTableDTO.getMainTableGuid());
        doOpenTableAfterHandler(reserveRecord);
    }

    protected ReserveOpenTableDTO doOpenTable(ReserveRecord record, List<TableDTO> tables, T baseEvent) {
        String orderGuid = null;
        Integer peopleNum = record.getNumber();
        int tableNumber = peopleNum / tables.size();
        int mode = peopleNum % tables.size();
        BatchOpenTableDTO batchOpenTableDTO = new BatchOpenTableDTO();
        List<OpenTableDTO> list = new ArrayList(tables.size());
        batchOpenTableDTO.setReserveGuid(record.getGuid());
        batchOpenTableDTO.setOpenTableDTOS(list);
        batchOpenTableDTO.setReserveAmount(record.getReserveAmount());
        batchOpenTableDTO.setContainDish(record.hasItem());
        ReserveOpenTableDTO reserveOpenTableDTO = null;
        if (!tables.isEmpty()) {
            for (int i = 0; i < tables.size(); i++) {
                TableDTO e = tables.get(i);
                OpenTableDTO dto = new OpenTableDTO();
                BeanUtils.copyProperties(baseEvent.getBaseDTO(), dto);
                dto.setTableCode(e.getName());
                dto.setTableGuid(e.getGuid());
                dto.setAreaName(e.getAreaName());
                dto.setAreaGuid(e.getAreaGuid());
                int num = mode - 1 < i ? tableNumber : tableNumber + 1;
                dto.setActualGuestsNo(num <= 0 ? 1 : num);
                list.add(dto);
            }
            reserveOpenTableDTO = tableServiceService.batch(batchOpenTableDTO);
            if (Boolean.FALSE.equals(reserveOpenTableDTO.getSuccess())) {
                if (CollectionUtils.isNotEmpty(reserveOpenTableDTO.getNoExistTables())) {
                    throw new TableNotAllSuccessException("桌台不存在, 请重新选择桌台", reserveOpenTableDTO);
                }
                throw new TableNotAllSuccessException("桌台占用, 请重新选择桌台", reserveOpenTableDTO);
            }
            orderGuid = reserveOpenTableDTO.getOrderGuids().get(0);
        }
        //写库 记录 主订单guid
        ReserveRecordDo recordDo = new ReserveRecordDo();
        Boolean isDelay = isDelay(record);
        recordDo.setIsDelay(isDelay);
        recordDo.setState(ReserveRecordStateEnum.OPEN_TABLE.getCode());
        record.setState(recordDo.getState());
        recordDo.setGuid(record.getGuid());
        recordDo.setArriveTime(LocalDateTime.now());
        recordDo.setArriveUserGuid(UserContextUtils.getUserGuid());
        recordDo.setArriveUserName(UserContextUtils.getUserName());
        recordDo.setMainOrderGuid(orderGuid);
        recordDo.setModifiedStaffGuid(UserContextUtils.getUserGuid());
        recordDo.setModifiedStaffName(UserContextUtils.getUserName());
        recordDo.setGmtModified(LocalDateTime.now());

        record.setArriveTime(recordDo.getArriveTime());
        record.setArriveUserGuid(recordDo.getArriveUserGuid());
        record.setArriveUserName(recordDo.getArriveUserName());
        record.setModifiedStaffGuid(recordDo.getModifiedStaffGuid());
        record.setModifiedStaffName(recordDo.getModifiedStaffName());
        record.setGmtModified(recordDo.getGmtModified());
        reserveRecordDoMapper.update(recordDo, new LambdaQueryWrapper<ReserveRecordDo>().eq(ReserveRecordDo::getGuid, recordDo.getGuid()));
        return reserveOpenTableDTO;
    }

    /**
     * 到店开台后置处理
     */
    private void doOpenTableAfterHandler(ReserveRecord reserveRecord) {
        if (Objects.equals(reserveRecord.getOrderType(), OrderTypeEnum.RESERVE.getCode())
                && !BaseDeviceTypeEnum.isApplet(reserveRecord.getDeviceType())) {
            // 发送消息到pos
            sendMsgByPos(reserveRecord);
        }
    }

    /**
     * 发送消息到pos
     */
    private void sendMsgByPos(ReserveRecord reserveRecord) {
        // 查询门店名称
        StoreDTO storeDTO = organizationClientService.queryStoreByGuid(reserveRecord.getStoreGuid());
        BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setMessageType(BusinessMsgTypeEnum.TABLE_STATE_MSG_TYPE.getId());
        businessMessageDTO.setPlatform(String.valueOf(OrderType.OTHER_ORDER.getType()));
        businessMessageDTO.setStoreGuid(reserveRecord.getStoreGuid());
        businessMessageDTO.setStoreName(storeDTO.getName());
        businessMessageDTO.setDetailMessageType(BusinessMsgTypeEnum.RESERVE_RECORD_STATE_CHANGE_POS.getId());
        businessMessageDTO.setSubject("");
        ReserveSystemMsgDTO reserveSystemMsgDTO = new ReserveSystemMsgDTO();
        reserveSystemMsgDTO.setGuid(reserveRecord.getGuid());
        reserveSystemMsgDTO.setAutoRev(0);
        businessMessageDTO.setContent(JacksonUtils.writeValueAsString(reserveSystemMsgDTO));
        businessMessageDTO.setCreateTime(LocalDateTime.now());
        businessMessageDTO.setPushTime(LocalDateTime.now());
        log.info("[TableOpenTableEvent][POS]预定消息推送入参:{}", JacksonUtils.writeValueAsString(businessMessageDTO));
        messageClientService.msg(businessMessageDTO);
    }

}