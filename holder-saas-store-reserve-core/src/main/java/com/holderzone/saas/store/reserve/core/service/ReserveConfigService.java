package com.holderzone.saas.store.reserve.core.service;

import com.holderzone.saas.store.dto.table.AreaDTO;
import com.holderzone.saas.store.reserve.api.dto.ReserveConfigDTO;
import com.holderzone.saas.store.reserve.api.dto.ReserveConfigSyncDTO;
import com.holderzone.saas.store.reserve.core.dal.ReserveConfigDo;
import com.holderzone.saas.store.reserve.core.domain.ReserveConfig;
import com.holderzone.saas.store.reserve.core.domain.ReserveRecord;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReserveRecordService
 * @date 2019/04/23 15:29
 * @description //TODO
 * @program holder-saas-store-reserve
 */
public interface ReserveConfigService {

    ReserveConfig obtain(ReserveRecord reserveRecord);

    ReserveConfigDo obtainByStoreGuid(String storeGuid);

    List<ReserveConfigDo> obtainByStoreGuids(List<String> storeGuids);

    ReserveConfigDTO obtain(String storeGuid);

    ReserveConfigDTO insert(ReserveConfigDTO config);

    ReserveConfigDTO update(ReserveConfigDTO config);

    void sync(ReserveConfigSyncDTO reserveConfigSyncDTO, ReserveConfigDo reserveConfigDO, List<AreaDTO> areaLis);

    ReserveConfig obtainDomain(String storeGuid);
}