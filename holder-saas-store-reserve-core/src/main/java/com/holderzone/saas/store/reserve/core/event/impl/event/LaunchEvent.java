package com.holderzone.saas.store.reserve.core.event.impl.event;

import com.holderzone.saas.store.reserve.core.domain.ReserveRecord;
import com.holderzone.saas.store.reserve.core.event.BaseEvent;
import lombok.Getter;

import java.util.function.Consumer;
import java.util.function.Predicate;

/**
 * <AUTHOR>
 * @version 1.0
 * @className LaunchEvent
 * @date 2019/04/23 17:24
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Getter
public class LaunchEvent extends BaseEvent {
    private Boolean saveTable;

    public LaunchEvent(ReserveRecord reserveRecord, Boolean saveTable) {
        super(reserveRecord);
        this.saveTable = saveTable;
    }

    private static final String NAME= LaunchEvent.class.getSimpleName();
    private Consumer<ReserveRecord> unlock;
    private Predicate<Void> predicate = (e)->true;

    public Predicate<Void> getPredicate() {
        return predicate;
    }

    public void setPredicate(Predicate<Void> predicate) {
        this.predicate = predicate;
    }

    public Consumer<ReserveRecord> getUnlock() {
        return unlock;
    }

    public void setUnlock(Consumer<ReserveRecord> unlock) {
        this.unlock = unlock;
    }

    @Override
    public String getName() {
        return NAME;
    }
}