package com.holderzone.saas.store.reserve.core.event.impl.event;

import com.holderzone.saas.store.reserve.core.domain.ReserveRecord;
import com.holderzone.saas.store.reserve.core.event.BaseEvent;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className LaunchEvent
 * @date 2019/04/23 17:24
 * @description //TODO
 * @program holder-saas-store-reserve
 */

public class UnLockTableEvent extends BaseEvent {
    private List<String> tableGuids;
    public UnLockTableEvent(ReserveRecord reserveRecord) {
        super(reserveRecord);
    }

    public UnLockTableEvent(ReserveRecord reserveRecord, List<String> tableGuids) {
        super(reserveRecord);
        this.tableGuids = tableGuids;
    }

    public List<String> getTableGuids() {
        return tableGuids;
    }

    private static final String NAME= UnLockTableEvent.class.getSimpleName();

    @Override
    public String getName() {
        return NAME;
    }
}