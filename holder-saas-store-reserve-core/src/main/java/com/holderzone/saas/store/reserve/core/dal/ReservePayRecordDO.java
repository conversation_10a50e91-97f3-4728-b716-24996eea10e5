package com.holderzone.saas.store.reserve.core.dal;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReservePayRecordDO
 * @date 2019/12/06 18:05
 * @description //TODO
 * @program IdeaProjects
 */

@Data
@EqualsAndHashCode
@Accessors(chain = true)
@TableName("hss_reserve_pay_record")
public class ReservePayRecordDO {

    private String guid;

    private String reserveGuid;

    private String failMsg;

    private String payPowerName;

    private Integer payPowerId;

    private String appId;

    private String storeGuid;

    private BigDecimal reserveAmount;

    private Integer payType;

    private Integer state;

    private LocalDateTime gmtCreate;

    private LocalDateTime gmtModified;

    private String payTypeName;

    /**
     * 会员余额资金变动明细guid
     */
    private String memberFundingDetailGuid;
}