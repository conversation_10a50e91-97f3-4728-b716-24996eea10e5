package com.holderzone.saas.store.reserve.core.event.impl.event;

import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.reserve.core.domain.ReserveRecord;
import com.holderzone.saas.store.reserve.core.event.BaseEvent;
import lombok.Getter;

import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @version 1.0
 * @className LaunchEvent
 * @date 2019/04/23 17:24
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Getter
public class EffectiveEvent extends BaseEvent {
    private BaseDTO baseDTO;

    private String orderGuid;

    private String tableGuid;

    public EffectiveEvent(ReserveRecord reserveRecord, BaseDTO baseDTO) {
        super(reserveRecord);
        this.baseDTO = baseDTO;
    }

    private static final String NAME=EffectiveEvent.class.getSimpleName();
    private Consumer<ReserveRecord> unlock;
    public void setUnlock(Consumer<ReserveRecord> unlock) {
        this.unlock = unlock;
    }
    @Override
    public String getName() {
        return NAME;
    }

    public void setOrderGuid(String orderGuid) {
        this.orderGuid = orderGuid;
    }

    public void setTableGuid(String tableGuid) {
        this.tableGuid = tableGuid;
    }
}