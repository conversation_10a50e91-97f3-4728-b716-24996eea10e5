package com.holderzone.saas.store.reserve.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.redisson.sdk.lock.RedissonLockUtil;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.table.AreaDTO;
import com.holderzone.saas.store.enums.BaseDeviceTypeEnum;
import com.holderzone.saas.store.enums.reserve.ReserveDepoistTypeEnum;
import com.holderzone.saas.store.reserve.api.dto.ReserveConfigDTO;
import com.holderzone.saas.store.reserve.api.dto.ReserveConfigSyncDTO;
import com.holderzone.saas.store.reserve.api.dto.TimingSegmentDTO;
import com.holderzone.saas.store.reserve.core.common.ReserveUtils;
import com.holderzone.saas.store.reserve.core.common.SnowFlakeUtil;
import com.holderzone.saas.store.reserve.core.dal.ReserveConfigDo;
import com.holderzone.saas.store.reserve.core.dal.mapper.ReserveConfigDoMapper;
import com.holderzone.saas.store.reserve.core.domain.ReserveConfig;
import com.holderzone.saas.store.reserve.core.domain.ReserveRecord;
import com.holderzone.saas.store.reserve.core.domain.vo.AreaVO;
import com.holderzone.saas.store.reserve.core.mapstruct.ReserveRecordMapstruct;
import com.holderzone.saas.store.reserve.core.service.ReserveConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReserveConfigServiceImpl
 * @date 2019/05/05 10:05
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ReserveConfigServiceImpl extends ServiceImpl<ReserveConfigDoMapper, ReserveConfigDo> implements ReserveConfigService {
    private static final ReserveConfigDTO DEFAULT_CONFIG;
    private static final TimingSegmentDTO TIME_10_12 = new TimingSegmentDTO(LocalTime.of(10, 0), LocalTime.of(12, 0));
    private static final TimingSegmentDTO TIME_17_19 = new TimingSegmentDTO(LocalTime.of(17, 0), LocalTime.of(19, 0));
    private static final List<TimingSegmentDTO> DEFAULT_SEGMENTS = Arrays.asList(TIME_10_12, TIME_17_19);
    private static final List<String> DEFAULT_REQUIREMENT_TYPE = Lists.newArrayList("常规用餐", "生日聚会");

    static {
        DEFAULT_CONFIG = new ReserveConfigDTO();
        DEFAULT_CONFIG.setSegments(DEFAULT_SEGMENTS);
        DEFAULT_CONFIG.setIsEnableSuccessMessage(true);
        DEFAULT_CONFIG.setIsEnableWarnMessage(true);
        DEFAULT_CONFIG.setIsEnablePrivateRoom(false);
        DEFAULT_CONFIG.setLockTableTiming(0.5F);
        DEFAULT_CONFIG.setUnLockTableTiming(0.5F);
        DEFAULT_CONFIG.setSendWarnMessageTiming(2F);
        DEFAULT_CONFIG.setDeviceType(String.valueOf(BaseDeviceTypeEnum.All_IN_ONE.getCode()));
        DEFAULT_CONFIG.setRequirementType(DEFAULT_REQUIREMENT_TYPE);
        DEFAULT_CONFIG.setDepositFlag(false);
        DEFAULT_CONFIG.setDepositType(ReserveDepoistTypeEnum.BY_NUM.getCode());
    }

    @Override
    public ReserveConfig obtain(ReserveRecord reserveRecord) {
        return ReserveRecordMapstruct.MAPSTRUCT.doToDomain(
                obtainByStoreGuid(reserveRecord.getStoreGuid())
        );
    }

    @Override
    public ReserveConfig obtainDomain(String storeGuid) {
        return ReserveRecordMapstruct.MAPSTRUCT.doToDomain(
                obtainByStoreGuid(storeGuid)
        );
    }

    @Override
    public ReserveConfigDTO obtain(String storeGuid) {
        ReserveConfigDo db = obtainByStoreGuid(storeGuid);
        if (db == null) {
            if (!RedissonLockUtil.tryLock(storeGuid, 5, 15)) {
                throw new BusinessException("系统繁忙请稍候重试");
            }
            try {
                db = obtainByStoreGuid(storeGuid);
                if (db == null) {
                    ReserveConfigDTO config = (ReserveConfigDTO) DEFAULT_CONFIG.clone();
                    config.setStoreGuid(storeGuid);
                    return insert(config);
                }
            } catch (CloneNotSupportedException e) {
                e.printStackTrace();
            } finally {
                RedissonLockUtil.unlock(storeGuid);
            }
        }
        return ReserveRecordMapstruct.MAPSTRUCT.domainToDto(ReserveRecordMapstruct.MAPSTRUCT.doToDomain(db));
    }

    @Override
    public ReserveConfigDo obtainByStoreGuid(String storeGuid) {
        return baseMapper.selectOne(new LambdaQueryWrapper<ReserveConfigDo>().eq(ReserveConfigDo::getStoreGuid, storeGuid));
    }

    @Override
    public List<ReserveConfigDo> obtainByStoreGuids(List<String> storeGuids) {
        if (CollectionUtils.isEmpty(storeGuids)) {
            return Lists.newArrayList();
        }
        return baseMapper.selectList(new LambdaQueryWrapper<ReserveConfigDo>()
                .in(ReserveConfigDo::getStoreGuid, storeGuids));
    }

    @Override
    public ReserveConfigDTO insert(ReserveConfigDTO config) {
        log.info("保存预定配置, config:{}", JacksonUtils.writeValueAsString(config));
        ReserveConfigDo configDo = ReserveRecordMapstruct.MAPSTRUCT.domainToDo(
                ReserveRecordMapstruct.MAPSTRUCT.dtoToDomain(config)
        );
        // String configGuid = ReserveUtils.reserveConfigGuid();
        String configGuid = String.valueOf(SnowFlakeUtil.getInstance().nextId());
        log.info("生成configGuid, configGuid:{}", configGuid);
        List<TimingSegmentDTO> segments = config.getSegments();
        segmentsCheck(segments);
        configDo.setCreateStaffGuid(UserContextUtils.getUserGuid());
        configDo.setModifiedStaffGuid(UserContextUtils.getUserGuid());
        configDo.setGuid(configGuid);
        config.setGuid(configGuid);
        Integer count = baseMapper.selectCount(new LambdaQueryWrapper<ReserveConfigDo>().eq(ReserveConfigDo::getStoreGuid, config.getStoreGuid()));
        if (count > 0) {
            throw new BusinessException("重复的预定配置");
        }
        save(configDo);
        log.info("保存预定配置完成, config:{}", JacksonUtils.writeValueAsString(configDo));
        return config;
    }

    private void segmentsCheck(List<TimingSegmentDTO> segments) {
        for (int i = 0; i < segments.size(); i++) {
            TimingSegmentDTO template = segments.get(i);
            for (int j = 0; j < segments.size(); j++) {
                if (i == j) {
                    continue;
                }
                TimingSegmentDTO ref = segments.get(j);
                if (ref.getStart().isBefore(template.getStart()) && ref.getEnd().isAfter(template.getStart())) {
                    throw new BusinessException("预定时间段不能交错");
                }
                if (ref.getStart().isBefore(template.getEnd()) && ref.getEnd().isAfter(template.getEnd())) {
                    throw new BusinessException("预定时间段不能交错");
                }
            }
        }
    }

    @Override
    public ReserveConfigDTO update(ReserveConfigDTO config) {
        ReserveConfigDo configDo = ReserveRecordMapstruct.MAPSTRUCT.domainToDo(
                ReserveRecordMapstruct.MAPSTRUCT.dtoToDomain(config)
        );
        if (StringUtils.isEmpty(config.getGuid())) {
            throw new IllegalArgumentException("缺少guid");
        }
        Integer count = baseMapper.selectCount(
                new LambdaQueryWrapper<ReserveConfigDo>()
                        .eq(ReserveConfigDo::getStoreGuid, config.getStoreGuid())
                        .ne(ReserveConfigDo::getGuid, config.getGuid())
        );
        if (count > 0) {
            throw new BusinessException("重复的预定配置");
        }
        if (config.getSegments() != null) {
            List<TimingSegmentDTO> segments = config.getSegments();
            segmentsCheck(segments);
        }
        configDo.setCreateStaffGuid(UserContextUtils.getUserGuid());
        configDo.setModifiedStaffGuid(UserContextUtils.getUserGuid());
        configDo.setGmtModified(LocalDateTime.now());
        baseMapper.update(configDo, new LambdaQueryWrapper<ReserveConfigDo>().eq(ReserveConfigDo::getGuid, config.getGuid()));
        return config;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sync(ReserveConfigSyncDTO reserveConfigSyncDTO, ReserveConfigDo reserveConfigDO, List<AreaDTO> areaLis) {
        List<ReserveConfigDo> reserveConfigDOList = baseMapper.selectList(
                new LambdaQueryWrapper<ReserveConfigDo>()
                        .in(ReserveConfigDo::getStoreGuid, reserveConfigSyncDTO.getSyncStoreGuids())
        );
        Map<String, ReserveConfigDo> reserveConfigDOMap = reserveConfigDOList.stream()
                .collect(Collectors.toMap(ReserveConfigDo::getStoreGuid, Function.identity(), (key1, key2) -> key1));
        Map<String, List<AreaDTO>> areaByStoreGuidMap = areaLis.stream().collect(Collectors.groupingBy(AreaDTO::getStoreGuid));
        List<ReserveConfigDo> syncReserveConfigDOList = reserveConfigSyncDTO.getSyncStoreGuids().stream().map(syncStoreGuid -> {
            ReserveConfigDo reserveConfigDo = reserveConfigDOMap.getOrDefault(syncStoreGuid, new ReserveConfigDo());
            BeanUtils.copyProperties(reserveConfigDO, reserveConfigDo, "id", "guid", "storeGuid");
            if (StringUtils.isEmpty(reserveConfigDo.getGuid())) {
                reserveConfigDo.setGuid(String.valueOf(SnowFlakeUtil.getInstance().nextId()));
                reserveConfigDo.setGmtCreate(LocalDateTime.now());
                reserveConfigDo.setCreateStaffGuid(UserContextUtils.getUserGuid());
                reserveConfigDo.setStoreGuid(syncStoreGuid);
            }
            reserveConfigDo.setGmtModified(LocalDateTime.now());
            reserveConfigDo.setModifiedStaffGuid(UserContextUtils.getUserGuid());
            // 过滤门店不存在的区域名称
            filterAreaName(reserveConfigDo, reserveConfigDO, areaByStoreGuidMap);
            return reserveConfigDo;
        }).collect(Collectors.toList());
        saveOrUpdateBatch(syncReserveConfigDOList);
    }


    /**
     * 过滤门店不存在的区域名称
     */
    private void filterAreaName(ReserveConfigDo otherReserveConfigDo, ReserveConfigDo originalReserveConfigDO,
                                Map<String, List<AreaDTO>> areaByStoreGuidMap) {
        // 原门店的区域
        String originalReserveArea = originalReserveConfigDO.getReserveArea();
        if (StringUtils.isEmpty(originalReserveArea)) {
            otherReserveConfigDo.setReserveArea(JacksonUtils.writeValueAsString(Lists.newArrayList()));
            otherReserveConfigDo.setAreaDeposit(JacksonUtils.writeValueAsString(Lists.newArrayList()));
            return;
        }
        List<AreaVO> originalAreaVOList = JacksonUtils.toObjectList(AreaVO.class, originalReserveArea);
        List<AreaDTO> originalAreaDTOList = areaByStoreGuidMap.getOrDefault(originalReserveConfigDO.getStoreGuid(), Lists.newArrayList());
        Map<String, String> originalAreaDTOMap = originalAreaDTOList.stream()
                .collect(Collectors.toMap(AreaDTO::getGuid, AreaDTO::getAreaName, (key1, key2) -> key1));
        // 过滤不存在的区域
        originalAreaVOList = originalAreaVOList.stream()
                .filter(e -> originalAreaDTOMap.containsKey(e.getGuid()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(originalAreaVOList)) {
            otherReserveConfigDo.setReserveArea(JacksonUtils.writeValueAsString(Lists.newArrayList()));
            otherReserveConfigDo.setAreaDeposit(JacksonUtils.writeValueAsString(Lists.newArrayList()));
            return;
        }
        // 查询目标门店的区域
        List<AreaDTO> otherAreaDTOList = areaByStoreGuidMap.get(otherReserveConfigDo.getStoreGuid());
        Map<String, AreaDTO> otherAreaNameMap = otherAreaDTOList.stream()
                .collect(Collectors.toMap(AreaDTO::getAreaName, Function.identity(), (key1, key2) -> key1));
        List<AreaVO> otherAreaVOList = Lists.newArrayList();
        for (AreaVO areaVO : originalAreaVOList) {
            String areaName = originalAreaDTOMap.get(areaVO.getGuid());
            if (StringUtils.isEmpty(areaName)) {
                continue;
            }
            if (otherAreaNameMap.containsKey(areaName)) {
                AreaVO otherAreaVO = new AreaVO();
                BeanUtils.copyProperties(areaVO, otherAreaVO);
                otherAreaVO.setGuid(otherAreaNameMap.get(areaName).getGuid());
                otherAreaVOList.add(otherAreaVO);
            }
        }
        otherReserveConfigDo.setReserveArea(JacksonUtils.writeValueAsString(otherAreaVOList));
        if (Boolean.TRUE.equals(otherReserveConfigDo.getDepositFlag())
                && Objects.equals(otherReserveConfigDo.getDepositType(), ReserveDepoistTypeEnum.BY_AREA.getCode())) {
            otherReserveConfigDo.setAreaDeposit(JacksonUtils.writeValueAsString(otherAreaVOList));
        } else {
            otherReserveConfigDo.setAreaDeposit(JacksonUtils.writeValueAsString(Lists.newArrayList()));
        }
    }

}