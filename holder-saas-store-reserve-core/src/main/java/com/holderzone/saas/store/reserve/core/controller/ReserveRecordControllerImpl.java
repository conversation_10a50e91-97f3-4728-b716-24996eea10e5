package com.holderzone.saas.store.reserve.core.controller;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.business.manage.HandoverPayQueryDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.request.daily.DailyReqDTO;
import com.holderzone.saas.store.dto.order.response.daily.GatherRespDTO;
import com.holderzone.saas.store.dto.reserve.*;
import com.holderzone.saas.store.dto.table.TableOrderCombineDTO;
import com.holderzone.saas.store.dto.table.trade.TradeTableDTO;
import com.holderzone.saas.store.dto.zhuancan.ZhuanCanReserveMerchantDTO;
import com.holderzone.saas.store.enums.BaseDeviceTypeEnum;
import com.holderzone.saas.store.reserve.api.ReserveRecordApi;
import com.holderzone.saas.store.reserve.api.common.Utils;
import com.holderzone.saas.store.reserve.api.dto.*;
import com.holderzone.saas.store.reserve.api.enums.OrderTypeEnum;
import com.holderzone.saas.store.reserve.core.domain.ReserveRecordStateEnum;
import com.holderzone.saas.store.reserve.core.service.ReserveRecordService;
import com.holderzone.saas.store.reserve.intergration.table.MessageClientService;
import com.holderzone.saas.store.reserve.intergration.table.TableClientService;
import com.holderzone.saas.store.reserve.intergration.table.TcdClientService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReserveRecordControllerImpl
 * @date 2019/04/27 14:06
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Slf4j
@Primary
@RestController
public class ReserveRecordControllerImpl implements ReserveRecordApi {
    @Autowired
    private ReserveRecordService reserveRecordService;

    @Resource
    private MessageClientService messageClientService;

    @Resource
    private TableClientService tableClientService;

    @Resource
    private TcdClientService tcdClientService;

    @Override
    public ReserveRecordDetailDTO launch(@Valid @RequestBody ReserveRecordDTO reserveRecordDTO) {
        Assert.isTrue(!Utils.greatThanZeroConsume().test(reserveRecordDTO.getReserveAmount()), "请调支付接口预订");
        return reserveRecordService.launch(reserveRecordDTO,ReserveRecordStateEnum.COMMIT);
    }

    @Override
    public ReserveRecordDetailDTO obtain(@RequestBody ReserveRecordGuidDTO guidDTO) {
        return reserveRecordService.obtainStaticsDetail(guidDTO);
    }

    @Override
    public ReserveRecordDetailDTO obtainApplet(String guid) {
        ReserveRecordGuidDTO reserveRecordGuidDTO = new ReserveRecordGuidDTO(guid);
        ReserveRecordDetailDTO detailDTO = reserveRecordService.obtainDetail(reserveRecordGuidDTO);
        if (Objects.isNull(detailDTO)) {
            log.error("预定单不存在, guid:{}", guid);
            throw new BusinessException("预定单不存在");
        }
        try {
            ZhuanCanReserveMerchantDTO merchantByThirdNo = tcdClientService.getReserveMerchantByThirdNo(detailDTO.getStoreGuid());
            detailDTO.setZhuanCanMerchantDTO(merchantByThirdNo);
            if (Objects.nonNull(merchantByThirdNo) && !StringUtils.isEmpty(merchantByThirdNo.getShareUrl())) {
                String oldShareUrl = detailDTO.getShareUrl();
                detailDTO.setShareUrl(merchantByThirdNo.getShareUrl() + "&guid=" + detailDTO.getGuid());
                if (!Objects.equals(detailDTO.getShareUrl(), oldShareUrl)) {
                    // 更新shareUrl
                    reserveRecordService.updateShareUrl(detailDTO.getGuid(), detailDTO.getShareUrl());
                }
            }
        } catch (Exception e) {
            log.error("查询小程序门店信息失败, e:", e);
        }
        return detailDTO;
    }

    @Override
    public Integer obtainDeviceType(@RequestBody ReserveRecordGuidDTO guidDTO) {
        ReserveRecordDetailDTO detailDTO = reserveRecordService.obtainDetail(guidDTO);
        if (Objects.isNull(detailDTO)) {
            log.error("预定单不存在, guid:{}", guidDTO.getGuid());
            throw new BusinessException("预定单不存在");
        }
        return detailDTO.getDeviceType();
    }

    @Override
    public ReserveReportTotalDataDTO itemCount(@RequestBody ReserveReportParamDTO paramDTO) {
        return reserveRecordService.itemCount(paramDTO);
    }

    @Override
    public ReserveHandoverDTO handover(@RequestBody HandoverPayQueryDTO handoverPayQueryDTO) {
        return reserveRecordService.handover(handoverPayQueryDTO);
    }

    @Override
    public List<GatherRespDTO> gather(@RequestBody DailyReqDTO dailyReqDTO) {
        return reserveRecordService.gather(dailyReqDTO);
    }

    @Override
    public ReserveRecordDTO queryByOrderGuid(@RequestBody SingleDataDTO query) {
        log.info("[根据订单查询预付金信息]query={}", JacksonUtils.writeValueAsString(query));
        return reserveRecordService.queryByOrderGuid(query.getData());
    }

    @Override
    public ReserveRecordDTO queryByGuid(@RequestBody SingleDataDTO query) {
        log.info("[根据guid查询预付金信息]query={}", JacksonUtils.writeValueAsString(query));
        return reserveRecordService.queryByGuid(query.getData());
    }

    /**
     * 预定拆台
     */
    @Override
    public void separate(@RequestBody TableOrderCombineDTO tableOrderCombineDTO) {
        log.info("[预定拆台][separate]tableOrderCombineDTO={}", JacksonUtils.writeValueAsString(tableOrderCombineDTO));
        reserveRecordService.separate(tableOrderCombineDTO);
    }

    /**
     * 通知转台
     */
    @Override
    public void notifyTurn(@RequestBody TradeTableDTO tradeTableDTO) {
        log.info("[通知转台][notifyTurn]tradeTableDTO={}", JacksonUtils.writeValueAsString(tradeTableDTO));
        reserveRecordService.notifyTurn(tradeTableDTO);
    }

    /**
     * 支付成功通知
     */
    @Override
    public void notifyPay(@RequestBody NotifyPayReqDTO reqDTO) {
        log.info("[支付成功通知][notifyPay]reqDTO={}", JacksonUtils.writeValueAsString(reqDTO));
        reserveRecordService.notifyPay(reqDTO);
    }

    /**
     * 预订并台
     */
    @Override
    public void combine(@RequestBody TableOrderCombineDTO tableOrderCombineDTO) {
        log.info("[预订并台][combine]tableOrderCombineDTO={}", JacksonUtils.writeValueAsString(tableOrderCombineDTO));
        reserveRecordService.combine(tableOrderCombineDTO);
    }

    /**
     * 预付金反结账
     */
    @Override
    public void recovery(@RequestBody ReserveRecoveryDTO recoveryDTO) {
        log.info("[预付金反结账][recovery]recoveryDTO={}", JacksonUtils.writeValueAsString(recoveryDTO));
        reserveRecordService.recovery(recoveryDTO);
    }

    /**
     * 预订单打印
     * @param query 请求参数
     */
    @Override
    public void printByOrderGuid(@RequestBody SingleDataDTO query) {
        reserveRecordService.printByOrderGuid(query);
    }

    @Override
    public List<DineInItemDTO> getItems(@RequestBody ReserveRecordGuidDTO guidDTO) {
        return reserveRecordService.getItems(guidDTO);
    }

    @Override
    public Collection<ReserveRecordLessDTO> query(@RequestBody ReserveRecordQueryDTO queryDTO) {
        return reserveRecordService.query(queryDTO);
    }

    @Override
    public StatisticsDTO statistics(@RequestBody PhoneDTO queryDTO) {
        return reserveRecordService.statistics(queryDTO);
    }

    @Override
    public ReserveRecordDetailDTO modify(@Valid @RequestBody ReserveRecordDTO reserveRecordDTO) {
        return reserveRecordService.modify(reserveRecordDTO);
    }

    @Override
    public ReserveRecordDetailDTO pass(@RequestBody ReserveRecordGuidDTO guidDTO) {
        return reserveRecordService.pass(guidDTO);
    }

    @Override
    public ReserveRecordDetailDTO cancle(@RequestBody ReserveRecordGuidDTO guidDTO) {
        guidDTO.setDeviceType(BaseDeviceTypeEnum.All_IN_ONE.getCode());
        ReserveRecordDetailDTO cancelDetailDTO = reserveRecordService.cancle(guidDTO);
        cancelDetailDTO.setStatistics(statistics(new PhoneDTO(cancelDetailDTO.getPhone())));
        // 发送桌位状态变化通知
        sendTableMsg(guidDTO);
        return cancelDetailDTO;
    }

    private void sendTableMsg(ReserveRecordGuidDTO guidDTO) {
        if (Objects.equals(guidDTO.getOrderType(), OrderTypeEnum.RESERVE.getCode())
                || CollectionUtils.isEmpty(guidDTO.getTables())) {
            return;
        }
        List<String> tableGuidList = guidDTO.getTables().stream()
                .map(TableDTO::getGuid)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(tableGuidList)) {
            return;
        }
        SingleDataDTO singleDataDTO = new SingleDataDTO();
        BeanUtils.copyProperties(guidDTO, singleDataDTO);
        singleDataDTO.setDatas(tableGuidList);
        tableClientService.sendTableChangeMsg(singleDataDTO);
    }

    @Override
    public ReserveRecordDetailDTO open(@RequestBody ReserveRecordGuidDTO guidDTO) {
        return reserveRecordService.open(guidDTO);
    }

    @Override
    public ReserveRecordDetailDTO compensate(@RequestBody CompensateDTO compensateDTO) {
        return reserveRecordService.compensate(compensateDTO);
    }
}