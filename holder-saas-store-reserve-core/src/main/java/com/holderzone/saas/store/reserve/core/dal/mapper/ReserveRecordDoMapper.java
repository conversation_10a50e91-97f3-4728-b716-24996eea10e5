package com.holderzone.saas.store.reserve.core.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.holderzone.saas.store.dto.business.manage.HandoverPayQueryDTO;
import com.holderzone.saas.store.dto.order.request.daily.DailyReqDTO;
import com.holderzone.saas.store.dto.order.response.daily.GatherRespDTO;
import com.holderzone.saas.store.dto.reserve.CommitStatisticsReqDTO;
import com.holderzone.saas.store.dto.reserve.ReserveCommitStaticsDTO;
import com.holderzone.saas.store.dto.reserve.TableDTO;
import com.holderzone.saas.store.reserve.api.dto.GuidStateMappingDTO;
import com.holderzone.saas.store.reserve.api.dto.ReserveAmountPaymentDTO;
import com.holderzone.saas.store.reserve.api.dto.ReserveAppletQueryDTO;
import com.holderzone.saas.store.reserve.core.dal.ReserveRecordDo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReserveRecordDoMapper
 * @date 2019/04/23 19:09
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Repository
public interface ReserveRecordDoMapper extends BaseMapper<ReserveRecordDo> {

    IPage<ReserveRecordDo> obtainRecordList(IPage<ReserveRecordDo> page, @Param("query") ReserveAppletQueryDTO queryDTO);

    Collection<GuidStateMappingDTO> statistics(@Param("storeGuid")String storeGuid,@Param("phone")String phone);

    List<ReserveAmountPaymentDTO> reserveAmountQuery(@Param("dto") HandoverPayQueryDTO handoverPayQueryDTO);

    List<GatherRespDTO> gather(DailyReqDTO dailyReqDTO);

    /**
     * 查询同时间相同联系人的预定单
     */
    ReserveRecordDo querySameTimeRecord(@Param("query") ReserveAppletQueryDTO queryDTO);

    /**
     * 查询同时间相同联系人的预定单
     */
    ReserveRecordDo queryLastTimeRecord(@Param("query") ReserveAppletQueryDTO queryDTO);

    /**
     * 查询待处理订单总数
     */
    Long commitStatistics(@Param("storeGuid") String storeGuid);

    /**
     * 查询近30天待处理订单总数 按每天统计
     */
    List<ReserveCommitStaticsDTO.InnerDay> commitStatisticsByDay(@Param("storeGuid") String storeGuid);

    /**
     * 查询时间范围内待处理订单总数
     */
    Long commitStatisticsTotalByScope(@Param("dto") CommitStatisticsReqDTO commitStatisticsByScope);

    /**
     * 查询时间范围内待处理订单总数
     * 按每天统计
     */
    List<ReserveCommitStaticsDTO.InnerDay> commitStatisticsByScope(@Param("dto") CommitStatisticsReqDTO commitStatisticsByScope);

    BigDecimal queryReserveAmount(@Param("orderGuid") String orderGuid);

    ReserveRecordDo queryByGuid(@Param("guid") String guid);

    ReserveRecordDo queryByOrderGuid(@Param("orderGuid") String orderGuid);

    List<TableDTO> queryReserveTable(@Param("guid") String guid);

}