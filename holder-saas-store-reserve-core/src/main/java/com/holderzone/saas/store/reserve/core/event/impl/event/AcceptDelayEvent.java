package com.holderzone.saas.store.reserve.core.event.impl.event;

import com.holderzone.saas.store.reserve.core.domain.ReserveRecord;
import com.holderzone.saas.store.reserve.core.event.BaseEvent;


public class AcceptDelayEvent extends BaseEvent {

    public AcceptDelayEvent(ReserveRecord reserveRecord) {
        super(reserveRecord);
    }

    private static final String NAME = AcceptDelayEvent.class.getSimpleName();

    @Override
    public String getName() {
        return NAME;
    }
}