package com.holderzone.saas.store.reserve.core.common;

import java.util.UUID;

/**
 * <AUTHOR>
 * @version 1.0
 * @className GuidUtils
 * @date 2018/07/28 下午6:42
 * @description GUID生成工具
 * @program holder-saas-store-business-center
 */
public final class GuidUtils {

    private GuidUtils() {
    }

    /**
     * 生成营业日唯一guid
     *
     * @return
     */
    public static String nextAccountRecordGuid() {
        return UUID.randomUUID().toString();
    }

    public static String nextScreenPicGuid() {
        return UUID.randomUUID().toString();
    }

    public static String getRandomFileName(){
        return UUID.randomUUID().toString().replace("-","").substring(0,5);
    }

    /**
     * 生成交接班记录唯一guid
     *
     * @return
     */
    public static String nextHandoverRecordGuid() {
        return UUID.randomUUID().toString();
    }

    /**
     * 生成钱箱记录唯一guid
     *
     * @return
     */
    public static String nextCashboxRecordGuid() {
        return UUID.randomUUID().toString();
    }

    /**
     * 生成附加费唯一guid
     *
     * @return
     */
    public static String nextAdditionalFeeGuid() {
        return UUID.randomUUID().toString();
    }

    /**
     * 生成估清记录唯一guid
     *
     * @return
     */
    public static String nextEstimateRecordGuid() {
        return UUID.randomUUID().toString();
    }
}
