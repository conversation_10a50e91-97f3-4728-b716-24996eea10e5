package com.holderzone.saas.store.reserve.core.domain;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReserveConfig
 * @date 2019/04/23 10:24
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Data
@Accessors(chain = true)
public class PrivateRoom {

    private String guid;

    private String storeGuid;

    private String tableGuid;
}