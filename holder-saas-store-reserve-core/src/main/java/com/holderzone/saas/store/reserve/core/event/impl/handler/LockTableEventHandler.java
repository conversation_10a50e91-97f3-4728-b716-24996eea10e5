package com.holderzone.saas.store.reserve.core.event.impl.handler;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.framework.anno.CustomerRegister;
import com.holderzone.framework.event.CustomerEvent;
import com.holderzone.framework.event.observer.CustomerObserver;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.redisson.sdk.lock.RedissonLockUtil;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.reserve.api.enums.ClientStateEnum;
import com.holderzone.saas.store.reserve.core.dal.ReserveRecordDo;
import com.holderzone.saas.store.reserve.core.dal.ReserveRecordTableRelationDo;
import com.holderzone.saas.store.reserve.core.dal.mapper.ReserveRecordDoMapper;
import com.holderzone.saas.store.reserve.core.dal.mapper.ReserveRecordTableRelationDoMapper;
import com.holderzone.saas.store.reserve.core.domain.ReserveRecord;
import com.holderzone.saas.store.reserve.core.domain.ReserveRecordStateEnum;
import com.holderzone.saas.store.reserve.core.event.BaseEventHandler;
import com.holderzone.saas.store.reserve.core.event.impl.event.LockTableEvent;
import com.holderzone.saas.store.reserve.core.event.impl.event.PreparedLockTableEvent;
import com.holderzone.saas.store.reserve.core.mapstruct.ReserveRecordMapstruct;
import com.holderzone.saas.store.reserve.core.rocket.DelayLockTableListener;
import com.holderzone.saas.store.reserve.intergration.table.TableClientService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className LockTableEventHandler
 * @date 2019/04/26 16:59
 * @description 预定锁桌
 * @program holder-saas-store-reserve
 */
@Component
@Slf4j
@CustomerRegister(isRegister = true)
public class LockTableEventHandler extends BaseEventHandler<LockTableEvent> implements CustomerObserver<LockTableEvent> {
    @Autowired
    ReserveRecordDoMapper reserveRecordDoMapper;
    @Autowired
    ReserveRecordTableRelationDoMapper tableRelationDoMapper;
    @Autowired
    TableClientService tableServiceService;
    @Override
    public void execute(LockTableEvent baseEvent) {
        LocalDateTime ref = DelayLockTableListener.THREAD_LOCAL.get();
        ReserveRecord record = baseEvent.getReserveRecord();
        ReserveRecordDo db = reserveRecordDoMapper.selectOne(
                new LambdaQueryWrapper<ReserveRecordDo>().eq(ReserveRecordDo::getGuid,record.getGuid())
        );
        if (ReserveRecordStateEnum.FINISH.getCode() == db.getState()) {
            log.warn("订单已完成, 忽略锁台, reserveRecord:{}", JacksonUtils.writeValueAsString(db));
            return;
        }

        record = ReserveRecordMapstruct.MAPSTRUCT.toDomain(db);
        if(ref != null && !ref.equals(record.getReserveStartTime())){

            throw new BusinessException("锁台失败,时间被修改");
        }
        if((record.getState()& ReserveRecordStateEnum.SYSTEM_CANCLE.getCode())== ReserveRecordStateEnum.SYSTEM_CANCLE.getCode()
                || (record.getState() & ClientStateEnum.PICK_TABLE.getCode())==ClientStateEnum.PICK_TABLE.getCode()){
            throw new BusinessException("锁台失败,状态变更");
        }


        //修改预定记录
        ReserveRecordDo recordDo = new ReserveRecordDo();
        recordDo.setGuid(record.getGuid());
        recordDo.setIsLocked(true);
        reserveRecordDoMapper.update(recordDo,new LambdaQueryWrapper<ReserveRecordDo>().eq(ReserveRecordDo::getGuid,record.getGuid()));
        List<String> tableGuids = baseEvent.getTableGuids();
        if(tableGuids == null || tableGuids.isEmpty()) {
            List<ReserveRecordTableRelationDo> tableRelationDos = tableRelationDoMapper.selectList(
                    new LambdaQueryWrapper<ReserveRecordTableRelationDo>()
                            .eq(ReserveRecordTableRelationDo::getReserveRecordGuid, record.getGuid())
                            //state 1 标识 被预定 锁住的桌台
                            .eq(ReserveRecordTableRelationDo::getState, 0)
            );
            if (tableRelationDos == null || tableRelationDos.isEmpty()) {
                return;
            }
            tableGuids = tableRelationDos.stream().map(ReserveRecordTableRelationDo::getTableGuid).collect(Collectors.toList());
        }
        //TODO
        List<String> all = new ArrayList<>(tableGuids);

        //预定未锁定
        List<String> old = all;
        List<String> neo = Collections.emptyList();
        PreparedLockTableEvent preparedLockTableEvent = new PreparedLockTableEvent(record,old,neo);
        log.info("预订未锁定:{},old:{},new:{}",preparedLockTableEvent.getReserveRecord().getGuid(),old,neo);
        customerPublish.publish(new CustomerEvent(preparedLockTableEvent));
        // 锁桌
        log.info("预订锁定:{}，tables:{}",preparedLockTableEvent.getReserveRecord().getGuid(),tableGuids);
        List<String> failtGuid = tableServiceService.reserveLock(tableGuids);
        tableGuids.removeAll(failtGuid);
        if(tableGuids.isEmpty()){
            return;
        }

        // 反写 锁定成功的 桌台
        ReserveRecordTableRelationDo reserveRecordTableRelationDo = new ReserveRecordTableRelationDo();
        reserveRecordTableRelationDo.setState(1);
        tableRelationDoMapper.update(reserveRecordTableRelationDo,
                new LambdaQueryWrapper<ReserveRecordTableRelationDo>()
                        .in(ReserveRecordTableRelationDo::getTableGuid,tableGuids)
                        .eq(ReserveRecordTableRelationDo::getReserveRecordGuid,record.getGuid()));
    }
    @Override
    protected void pre(LockTableEvent baseEvent) {
        ReserveRecord record = baseEvent.getReserveRecord();
        boolean result = RedissonLockUtil.tryLock(record.getGuid(),3,10);
        if(!result){
            throw new BusinessException("当前预定记录正被其他人操作,请稍候重试!!");
        }
        super.pre(baseEvent);
    }

    @Override
    protected void after(LockTableEvent baseEvent) {
        ReserveRecord record = baseEvent.getReserveRecord();
        RedissonLockUtil.unlock(record.getGuid());
        super.after(baseEvent);
    }
}