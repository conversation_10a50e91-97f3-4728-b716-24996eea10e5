package com.holderzone.saas.store.reserve.core.common;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.table.ReserveOpenTableDTO;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TableNotAllSuccessException
 * @date 2019/05/31 14:57
 * @description //TODO
 * @program holder-saas-store-reserve
 */
public class TableNotAllSuccessException extends BusinessException {
    private ReserveOpenTableDTO reserveOpenTableDTO;

    public TableNotAllSuccessException(String message, ReserveOpenTableDTO reserveOpenTableDTO) {
        super(message);
        this.reserveOpenTableDTO = reserveOpenTableDTO;
    }

    public TableNotAllSuccessException(String message, Throwable cause, ReserveOpenTableDTO reserveOpenTableDTO) {
        super(message, cause);
        this.reserveOpenTableDTO = reserveOpenTableDTO;
    }
}