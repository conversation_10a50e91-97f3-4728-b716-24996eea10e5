package com.holderzone.saas.store.reserve.core.service;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.reserve.core.domain.Item;
import com.holderzone.saas.store.reserve.core.domain.ReserveRecord;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className VirtualOrderToItemsBridge
 * @date 2019/04/23 14:39
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Component
public class ReserveToItemsBridge implements Function<ReserveRecord, Collection<Item>> {
    @Override
    public Collection<Item> apply(ReserveRecord reserveRecord) {
        return Optional.ofNullable(reserveRecord.getItemsStr()).map((s)->
                  JacksonUtils.toObjectList(DineInItemDTO.class,s).stream().map((e)->new Item(e.getGuid(),e)).collect(Collectors.toList())
        ).orElse(null);
    }
}