package com.holderzone.saas.store.reserve.core.event.impl.handler;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.anno.CustomerRegister;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.event.CustomerEvent;
import com.holderzone.framework.event.observer.CustomerObserver;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.enums.BaseDeviceTypeEnum;
import com.holderzone.saas.store.enums.PaymentTypeEnum;
import com.holderzone.saas.store.reserve.api.enums.OrderTypeEnum;
import com.holderzone.saas.store.reserve.core.common.ReserveExceptionEnum;
import com.holderzone.saas.store.reserve.core.common.ReserveUtils;
import com.holderzone.saas.store.reserve.core.dal.ReserveRecordDo;
import com.holderzone.saas.store.reserve.core.domain.ReserveRecord;
import com.holderzone.saas.store.reserve.core.domain.ReserveRecordStateEnum;
import com.holderzone.saas.store.reserve.core.event.impl.event.DelayEvent;
import com.holderzone.saas.store.reserve.core.event.impl.event.LaunchEvent;
import com.holderzone.saas.store.reserve.core.event.impl.event.SystemMessageEvent;
import com.holderzone.saas.store.reserve.core.mapstruct.ReserveRecordMapstruct;
import com.holderzone.saas.store.reserve.core.rocket.Consume;
import com.holderzone.saas.store.util.BigDecimalUtil;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Objects;
import java.util.concurrent.ExecutorService;

/**
 * <AUTHOR>
 * @version 1.0
 * @className LaunchEventHandler
 * @date 2019/04/23 17:34
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Component
@RequiredArgsConstructor
@CustomerRegister(isRegister = true)
public class LaunchEventHandler extends SaveEventHanlder<LaunchEvent> implements CustomerObserver<LaunchEvent> {

    private final ExecutorService launchAfterExecutor;

    @Override
    public void execute(LaunchEvent baseEvent) {
        ReserveRecordDo reserveRecordDO = buildReserveRecordDO(baseEvent);
        reserveRecordDoMapper.insert(reserveRecordDO);
        // 保存预订商品
        reserveItemService.saveReserveItems(baseEvent.getReserveRecord());
        // 发起预定后置处理
        launchAfterHandler(baseEvent);
    }

    /**
     * 构建预定单实体
     */
    private ReserveRecordDo buildReserveRecordDO(LaunchEvent baseEvent) {
        ReserveRecord reserveRecord = baseEvent.getReserveRecord();
        ReserveRecordDo d = ReserveRecordMapstruct.MAPSTRUCT.toDo(reserveRecord);
        if (!com.holderzone.framework.util.StringUtils.isEmpty(reserveRecord.getOrderGuid())) {
            d.setMainOrderGuid(reserveRecord.getOrderGuid());
        }
        d.setGuid(ReserveUtils.reserveGuid());
        reserveRecord.setGuid(d.getGuid());
        if (!reserveRecord.getOrderType().equals(OrderTypeEnum.RESERVE_PAY.getCode())) {
            LocalDateTime now = LocalDateTime.now().truncatedTo(ChronoUnit.MINUTES);
            if (reserveRecord.getReserveStartTime().isBefore(now)) {
                throw new BusinessException(ReserveExceptionEnum.LAUNCH_OVERTIME.getMsg());
            }
        }
        d.setGmtCreate(LocalDateTime.now());
        d.setOperSubjectGuid(reserveRecord.getOperSubjectGuid());
        reserveRecord.setGmtCreate(d.getGmtCreate());
        if (BaseDeviceTypeEnum.isApplet(reserveRecord.getDeviceType())) {
            // put
            UserContext userContext = UserContextUtils.get();
            if (StringUtils.isEmpty(userContext.getStoreGuid())) {
                userContext.setStoreGuid(reserveRecord.getStoreGuid());
                UserContextUtils.put(userContext);
            }
            // 小程序
            d.setCreateStaffGuid(reserveRecord.getCreateUserId());
            d.setCreateStaffName(reserveRecord.getCreateUserName());
            d.setCreateUserPhone(reserveRecord.getCreateUserPhone());
            d.setArea(reserveRecord.getArea());
            d.setOrderNo("Y" + d.getGuid());
            // 最大退款时间
            if (BigDecimalUtil.greaterThanZero(reserveRecord.getReserveAmount())) {
                d.setMaxCancelableTime(reserveRecord.getReserveStartTime().plusHours(-reserveRecord.getCancelableTime()));
            }
            // 小程序分享地址
            d.setShareUrl(d.getShareUrl() + "&guid=" + d.getGuid());
        } else {
            // 一体机
            if (reserveRecord.getPaymentType() != null && reserveRecord.getPaymentType() == PaymentTypeEnum.AGG.getCode()) {
                d.setConfirmUserName(UserContextUtils.getUserName());
                d.setConfirmUserGuid(UserContextUtils.getUserGuid());
            }
            d.setConfirmUserName(UserContextUtils.getUserName());
            d.setCreateStaffGuid(UserContextUtils.getUserGuid());
            d.setModifiedStaffGuid(UserContextUtils.getUserGuid());
            d.setCreateStaffName(UserContextUtils.getUserName());
            d.setModifiedStaffName(UserContextUtils.getUserName());
            if (Boolean.TRUE.equals(baseEvent.getSaveTable())) {
                saveTable(reserveRecord);
            }
        }
        return d;
    }

    /**
     * 发起预定后置处理
     */
    private void launchAfterHandler(LaunchEvent baseEvent) {
        ReserveRecord reserveRecord = baseEvent.getReserveRecord();
        UserContext userContext = UserContextUtils.get();
        if (!BaseDeviceTypeEnum.isApplet(reserveRecord.getDeviceType())) {
            return;
        }
        if (Objects.equals(reserveRecord.getOrderType(), OrderTypeEnum.RESERVE.getCode())) {
            if (ReserveRecordStateEnum.NO_PAY.getCode() == reserveRecord.getState()) {
                // 15分钟支付超时
                launchAfterExecutor.execute(() -> {
                    UserContextUtils.put(userContext);
                    EnterpriseIdentifier.setEnterpriseGuid(userContext.getEnterpriseGuid());
                    DelayEvent delayEvent = new DelayEvent(reserveRecord, Consume.PAY_TIMEOUT_TAG);
                    customerPublish.publish(new CustomerEvent<>(delayEvent));
                });
                return;
            }
            // 商家未接单2小时自动取消
            launchAfterExecutor.execute(() -> {
                UserContextUtils.put(userContext);
                EnterpriseIdentifier.setEnterpriseGuid(userContext.getEnterpriseGuid());
                DelayEvent delayEvent = new DelayEvent(reserveRecord, Consume.ACCEPT_DELAY_TAG);
                customerPublish.publish(new CustomerEvent<>(delayEvent));
            });
        }
        // 发送消息到一体机
        launchAfterExecutor.execute(() -> {
            UserContextUtils.put(userContext);
            EnterpriseIdentifier.setEnterpriseGuid(userContext.getEnterpriseGuid());
            SystemMessageEvent systemMessageEvent = new SystemMessageEvent(reserveRecord);
            customerPublish.publish(new CustomerEvent<>(systemMessageEvent));
        });
    }

}