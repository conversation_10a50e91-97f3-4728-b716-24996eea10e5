package com.holderzone.saas.store.reserve.core.rocket;

import com.holderzone.framework.event.publish.impl.CustomerPublishImpl;
import com.holderzone.resource.common.dto.mq.UnMessage;
import com.holderzone.saas.store.reserve.core.dal.mapper.ReserveRecordDoMapper;
import com.holderzone.saas.store.reserve.intergration.table.OrganizationClientService;
import lombok.extern.slf4j.Slf4j;
import org.apache.curator.framework.CuratorFramework;
import org.apache.curator.framework.recipes.queue.QueueConsumer;
import org.apache.curator.framework.state.ConnectionState;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;

import java.text.MessageFormat;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DelayTaskConsumer
 * @date 2019/05/30 17:23
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Slf4j
public class DelayTaskConsumer implements QueueConsumer<UnMessage> ,Consume{

    @Autowired
    ReserveRecordDoMapper reserveRecordDoMapper;
    @Lazy
    @Autowired
    CustomerPublishImpl customerPublish;

    @Autowired
    OrganizationClientService organizationClientService;
    @Override
    public void consumeMessage(UnMessage s){
        try {
            doConsume(s);
        }catch (Exception e){
            log.error("consume fail by:{}",e);
        }
    }

    @Override
    public void stateChanged(CuratorFramework curatorFramework, ConnectionState connectionState) {
       log.warn(MessageFormat.format("State change . New State is - {0}",connectionState));
    }

    @Override
    public ReserveRecordDoMapper getReserveRecordDoMapper() {
        return reserveRecordDoMapper;
    }

    @Override
    public CustomerPublishImpl getCustomerPublish() {
        return customerPublish;
    }
    @Override
    public OrganizationClientService getOrganizationClientService() {
        return organizationClientService;
    }
}