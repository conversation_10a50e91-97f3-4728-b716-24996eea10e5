package com.holderzone.saas.store.reserve.core.domain;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.reserve.api.common.Utils;
import com.holderzone.saas.store.reserve.api.dto.ClientStateAble;
import com.holderzone.saas.store.reserve.core.domain.vo.CustomerVo;
import com.holderzone.saas.store.reserve.core.event.impl.event.CompensateEvent;
import com.holderzone.saas.store.reserve.core.event.impl.event.EffectiveEvent;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Collection;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReserveRecord
 * @date 2019/04/23 10:25
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Data
public abstract class ReserveRecord implements ClientStateAble {

    private Long id;
    private String guid;
    private String storeGuid;
    /**
     * 订单编号
     */
    private String orderNo;
    private Integer number;
    private Integer state;
    private Boolean isLocked;

    /**
     * value = "设备类型:PC服务端- 0,PC平板- 1,小店通- 2,一体机- 3,POS机- 4,云平板- 5,点菜宝(M1)- 6,PV1(带刷卡的点菜宝)- 7,微信- 8；",
     */
    private Integer deviceType;
    /**
     * 当前操作的设备类型
     */
    private Integer operateDeviceType;
    /**
     * value = "设备id",
     */
    private String deviceId;
    private Boolean isDelay;

    /**
     * 预定区域
     */
    private String area;

    private CustomerVo customer;
    private transient ReserveConfig config;
    private Collection<Table> tables;
    private Collection<Item> items;

    private Integer paymentType;
    private String paymentTypeName;
    private LocalDateTime paymentTime;
    private BigDecimal reserveAmount;
    /**
     * 退款金额
     */
    private BigDecimal reserveRefundAmount;


    private Byte gender;


    private String tag;
    private String des;
    private String remark;

    private String itemsStr;

    private LocalDateTime reserveLockTime;
    private LocalDateTime reserveStartTime;
    private LocalDateTime reservesEndTime;


    private String confirmUserGuid;
    private String confirmUserName;
    private LocalDateTime confirmTime;


    private String arriveUserGuid;
    private String arriveUserName;
    private LocalDateTime arriveTime;

    private String cancelUserGuid;
    private String cancelUserName;
    private LocalDateTime cancleTime;
    private String cancleReason;

    /**
     * 可取消订单时间 单位小时
     */
    private Integer cancelableTime;

    /**
     * 最大可退款时间
     */
    private LocalDateTime maxCancelableTime;

    /**
     * 取消角色
     */
    private String cancelRole;

    /**
     * 会员guid
     */
    private String memberInfoGuid;

    /**
     * 会员主卡持卡guid
     */
    private String memberInfoCardGuid;

    /**
     * 会员余额资金变动明细guid
     */
    private String memberFundingDetailGuid;

    /**
     * 运营主体guid
     */
    private String operSubjectGuid;

    /**
     * 小程序会员名称
     */
    private String memberName;

    /**
     * 会员支付密码
     */
    private String memberPassword;

    /**
     * 支付方式
     */
    private Integer payType;

    /**
     * 创建人guid
     */
    private String createStaffGuid;

    private String createStaffName;

    /**
     * 更新人guid
     */
    private String modifiedStaffGuid;
    private String modifiedStaffName;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    private LocalDateTime gmtModified;

    /**
     * 下单人id
     */
    private String createUserId;

    /**
     * 下单人手机号
     */
    private String createUserPhone;

    /**
     * 下单人名称
     */
    private String createUserName;

    /**
     * 小程序分享地址
     */
    private String shareUrl;

    /**
     * 支付guid
     */
    private String payGuid;

    /**
     * 订单类型 0预订 1预付金
     */
    private Integer orderType;

    @ApiModelProperty("订单guid")
    private String orderGuid;

    public LocalDateTime getReservesEndTime() {
        if (Objects.nonNull(config)) {
            setReservesEndTime(reserveStartTime.plus((long) (getConfig().getUnLockTableTiming() * TimeUnit.HOURS.toMinutes(1)), ChronoUnit.MINUTES));
        } else {
            setReservesEndTime(reserveStartTime.plusMinutes(30));
        }
        return reservesEndTime;
    }

    public LocalDateTime getReserveLockTime() {
        setReserveLockTime(reserveStartTime.plusMinutes(-(long) (getConfig().getLockTableTiming() * TimeUnit.HOURS.toMinutes(1))));
        return reserveLockTime;
    }

    /**
     * 取消
     *
     * @return
     */
    public abstract ReserveRecord cancle();

    /**
     * 会员支付
     */
    public abstract ReserveRecord memberPay();


    /**
     * 聚合支付
     */
    public abstract ReserveRecord aggPay();

    /**
     * 部分退款
     */
    public abstract ReserveRecord partRefund();

    /**
     * 接受
     *
     * @return
     */
    public abstract ReserveRecord accept();

    /**
     * 提交
     *
     * @return
     */
    public abstract ReserveRecord lauch();


    public abstract ReserveRecord modify();

    /**
     * 到店开单
     *
     * @return
     */
    public abstract EffectiveEvent effect();

    /**
     * 到店选桌
     *
     * @return
     */
    public abstract CompensateEvent compensate();

    /**
     * 返回true  说明不对
     *
     * @return true:不對的狀態    false：對的狀態
     */
    public boolean unSatisfyState() {
        //需要支付 但是不是未支付状态
        return isNeedPay() && state.intValue() != ReserveRecordStateEnum.NO_PAY.getCode();
    }

    /**
     * 是否需要支付
     *
     * @return true:需要支付    false：不需要支付的預定
     */
    public boolean isNeedPay() {
        return Utils.greatThanZeroConsume().test(reserveAmount);
    }

    /**
     * 是否需要支付
     *
     * @return true:需要支付    false：不需要支付的預定
     */
    public boolean hasItem() {
        return !(itemsStr == null || JacksonUtils.toObjectList(Object.class, itemsStr).size() == 0);
    }
}