package com.holderzone.saas.store.reserve.core.mapstruct;

import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.common.SubDineInItemDTO;
import com.holderzone.saas.store.reserve.core.dal.ReserveItemDo;
import org.mapstruct.Mapper;
import org.springframework.stereotype.Component;

@Component
@Mapper(componentModel = "spring")
public interface ReserveItemMapstruct {

    ReserveItemDo dine2ReserveItem(DineInItemDTO dineInItemDTO);

    ReserveItemDo subdine2ReserveItem(SubDineInItemDTO subDineInItemDTO);
}
