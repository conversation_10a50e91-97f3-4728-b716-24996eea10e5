package com.holderzone.saas.store.reserve.core.event.impl.handler;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.saas.store.dto.table.TableBasicQueryDTO;
import com.holderzone.saas.store.dto.table.TableOrderDTO;
import com.holderzone.saas.store.reserve.api.dto.ReserveRecordQueryDTO;
import com.holderzone.saas.store.reserve.api.enums.OrderTypeEnum;
import com.holderzone.saas.store.reserve.core.dal.ReserveRecordTableRelationDo;
import com.holderzone.saas.store.reserve.core.dal.TableTimeDo;
import com.holderzone.saas.store.reserve.core.dal.mapper.ReserveRecordDoMapper;
import com.holderzone.saas.store.reserve.core.dal.mapper.ReserveRecordTableRelationDoMapper;
import com.holderzone.saas.store.reserve.core.domain.ReserveConfig;
import com.holderzone.saas.store.reserve.core.domain.ReserveRecord;
import com.holderzone.saas.store.reserve.core.domain.Table;
import com.holderzone.saas.store.reserve.core.event.BaseEventHandler;
import com.holderzone.saas.store.reserve.core.event.impl.event.LaunchEvent;
import com.holderzone.saas.store.reserve.core.lock.*;
import com.holderzone.saas.store.reserve.core.mapstruct.TableMapstruct;
import com.holderzone.saas.store.reserve.core.service.ReserveItemService;
import com.holderzone.saas.store.reserve.intergration.table.TableClientService;
import com.holderzone.sdk.util.BatchIdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SaveEventHanlder
 * @date 2019/04/26 19:11
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Slf4j
public abstract class SaveEventHanlder<T extends LaunchEvent> extends BaseEventHandler<T> {
    @Autowired
    protected ReserveRecordDoMapper reserveRecordDoMapper;
    @Autowired
    protected ReserveRecordTableRelationDoMapper tableRelationDoMapper;

    @Autowired
    protected ReserveItemService reserveItemService;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private TableClientService tableClientService;


    public void saveTable(ReserveRecord reserveRecord) {
        saveTable(reserveRecord, true);
    }

    public void saveTable(ReserveRecord reserveRecord, Boolean removeSame) {
        List<ReserveRecordTableRelationDo> tables = reserveRecord.getTables().stream()
                .map(e -> TableMapstruct.TABLE_MAPSTRUCT.toDo(e, reserveRecord.getGuid()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(tables)) {
            throw new BusinessException("所选桌台都不可用");
        }
        if (Objects.equals(reserveRecord.getOrderType(), OrderTypeEnum.RESERVE.getCode())) {
            if (Boolean.TRUE.equals(removeSame) && !checkAvailableTables(reserveRecord, tables)) {
                throw new BusinessException("桌台占用请刷新后重试");
            }
            if (Boolean.TRUE.equals(removeSame)) {
                List<String> tableGuidBeReseved = tableRelationDoMapper.queryTableGuidBeReseved(tables, new ReserveRecordQueryDTO(reserveRecord.getGuid(),
                        reserveRecord.getReserveStartTime(), reserveRecord.getReservesEndTime(), null, null));
                if (!CollectionUtils.isEmpty(tableGuidBeReseved)) {
                    throw new BusinessException(String.format("所选桌台[%s]都不可用", StringUtils.join(tableGuidBeReseved, ",")));
                }
            }
        }
        List<Long> guids = BatchIdGenerator.batchGetGuids(redisTemplate, "reserve_table", tables.size());
        tables.forEach(e -> e.setGuid(guids.get((tables).indexOf(e)).toString()));
        tableRelationDoMapper.deleteByReserveGuid(reserveRecord.getGuid());
        log.info("reserveStartTime={}", reserveRecord.getReserveStartTime());
        tableRelationDoMapper.batchInsert(tables, new ReserveRecordQueryDTO(reserveRecord.getGuid(), reserveRecord.getReserveStartTime(),
                reserveRecord.getReservesEndTime(), null, null), removeSame);
    }


    /**
     * 校验可预定的桌台列表
     */
    private boolean checkAvailableTables(ReserveRecord reserveRecord, List<ReserveRecordTableRelationDo> tableRelationDos) {
        try {
            // 查询区域对应的桌台
            List<String> tableGuids = tableRelationDos.stream().map(ReserveRecordTableRelationDo::getTableGuid)
                    .collect(Collectors.toList());
            List<TableOrderDTO> tables = queryTables(reserveRecord.getStoreGuid(), tableGuids);
            // 查询 对应门店、对应区域、对应时间段 的 桌台预定信息
            List<TableTimeDo> tableTimeDOList = tableRelationDoMapper.queryTablesByReserveStartTime(
                    reserveRecord.getStoreGuid(), reserveRecord.getGuid(), null, reserveRecord.getReserveStartTime());
            // 相同时间的桌台的guid
            List<String> sameTimeTables = tableTimeDOList.stream()
                    .map(ReserveRecordTableRelationDo::getTableGuid)
                    .collect(Collectors.toList());
            List<String> ownTable = Optional.of(reserveRecord)
                    .map(e -> e.getTables().stream().map(Table::getGuid).collect(Collectors.toList()))
                    .orElse(Collections.emptyList());
            // 去除所有状态为 预定锁定和预定保留的桌台
            List<TableOrderDTO> afterFilter = tables.stream()
                    .filter(e -> ownTable.contains(e.getTableGuid()) || !sameTimeTables.contains(e.getTableGuid()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(afterFilter)) {
                return false;
            }
            // 如果预定的时间 已不足后台设定的锁定时间, 则需要过滤已开台的桌台
            ReserveConfig reserveConfig = reserveRecord.getConfig();
            LocalDateTime lockTableTime = reserveRecord.getReserveStartTime()
                    .plusMinutes(-(long) (reserveConfig.getLockTableTiming() * TimeUnit.HOURS.toMinutes(1)));
            if (!lockTableTime.isAfter(LocalDateTime.now())) {
                afterFilter = afterFilter.stream()
                        .filter(e -> (e.getStatus() == 0 || e.getStatus() == 2))
                        .collect(Collectors.toList());
            }
            return afterFilter.size() == tableGuids.size();
        } catch (Exception e) {
            log.error("check available table fail:", e);
            return false;
        }
    }

    /**
     * 查询指定桌台
     */
    private List<TableOrderDTO> queryTables(String storeGuid, List<String> tableGuids) {
        TableBasicQueryDTO query = new TableBasicQueryDTO();
        query.setStoreGuid(storeGuid);
        query.setTableGuidList(tableGuids);
        // 查询区域对应的桌台
        return tableClientService.listByAndroid(query);
    }


    @PostConstruct
    public void init(){
        RedisTableLock.redisTemplate = redisTemplate;
    }
    @Override
    protected void pre(T baseEvent) {
        if(!baseEvent.getSaveTable()){
            super.pre((T) baseEvent);
            return;
        }
        ReserveRecord record = baseEvent.getReserveRecord();
        if (record.getOrderType().equals(OrderTypeEnum.RESERVE_PAY.getCode())) {
            return;
        }
        Consumer<ReserveRecord> unlock = tryLock(record,record.getTables());
        baseEvent.setUnlock(unlock);
        super.pre((T) baseEvent);
    }

    @Override
    protected void after(T baseEvent) {
        if(!baseEvent.getSaveTable()){
            super.after((T) baseEvent);
            return;
        }
        ReserveRecord record = baseEvent.getReserveRecord();
        Optional.ofNullable(baseEvent.getUnlock()).orElse((e)->{}).accept(record);
    }

    public Consumer<ReserveRecord> tryLock(ReserveRecord reserveRecord, Collection<Table> tables) {
        String segment = DateTimeUtils.localDateTime2Mills(reserveRecord.getReserveStartTime())
                    + "-"
                    + DateTimeUtils.localDateTime2Mills(reserveRecord.getReservesEndTime());

        Collection<String> tableGuids = tables.stream().map(Table::getGuid).collect(Collectors.toSet());
        LockContext lockContext = new LockContext(LockModeEnum.SEGMENT, LockFailStrategy.IGONRE, segment);
        BatchRedisLock batchRedisLock = new BatchRedisLock(UserContextUtils.getUserGuid(), tableGuids);
        batchRedisLock.lock(lockContext);
        List<String> fail = Optional.ofNullable((List<String>) lockContext.getOther().get(BatchRedisLock.RESULT_KEY))
                .orElse(Collections.emptyList());
        reserveRecord.setTables(reserveRecord.getTables().stream().filter(e -> !fail.contains(e.getGuid())).collect(Collectors.toList()));
        batchRedisLock.setTableGuids(batchRedisLock.getTableGuids().stream().filter(e -> !fail.contains(e)).collect(Collectors.toList()));
        return e -> batchRedisLock.unLock(lockContext);
    }
}