package com.holderzone.saas.store.reserve.core.support;

import com.google.common.collect.Lists;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.reserve.api.dto.ReserveConfigDTO;
import com.holderzone.saas.store.reserve.core.dal.ReserveConfigDo;
import com.holderzone.saas.store.reserve.core.mapstruct.ReserveRecordMapstruct;
import com.holderzone.saas.store.reserve.core.service.ReserveConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.core.RedisOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.SessionCallback;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * 预订配置 缓存
 */
@Slf4j
@SuppressWarnings("all")
@Component("reserveConfigSupport")
@RequiredArgsConstructor
public class ReserveConfigSupport {


    private final RedisTemplate<String, String> redisTemplate;

    private final ReserveConfigService reserveConfigService;

    /**
     * 预订配置
     */
    private static final String RESERVE_CONFIG_KEY = "RESERVE_CONFIG_KEY:%s";

    /**
     * 查询门店预订配置
     */
    public ReserveConfigDTO get(String storeGuid) {
        String redisKey = String.format(RESERVE_CONFIG_KEY, storeGuid);
        if (redisTemplate.hasKey(redisKey)) {
            String reserveConfigRedisObj = redisTemplate.opsForValue().get(redisKey);
            if (StringUtils.isEmpty(reserveConfigRedisObj)) {
                return null;
            }
            return JacksonUtils.toObject(ReserveConfigDTO.class, reserveConfigRedisObj);
        }
        // 查询db
        ReserveConfigDo reserveConfigDO = reserveConfigService.obtainByStoreGuid(storeGuid);
        ReserveConfigDTO reserveConfigDTO = ReserveRecordMapstruct.MAPSTRUCT.domainToDto(
                ReserveRecordMapstruct.MAPSTRUCT.doToDomain(reserveConfigDO));
        // cache
        putConfigRedis(Lists.newArrayList(reserveConfigDTO));
        return reserveConfigDTO;
    }


    /**
     * 批量查询门店预订配置
     */
    public List<ReserveConfigDTO> list(List<String> storeGuids) {
        if (CollectionUtils.isEmpty(storeGuids)) {
            return Lists.newArrayList();
        }
        List<String> redisKeys = storeGuids.stream().map(e -> String.format(RESERVE_CONFIG_KEY, e)).collect(Collectors.toList());
        List<String> configStrList = redisTemplate.opsForValue().multiGet(redisKeys);
        if (CollectionUtils.isEmpty(configStrList)) {
            return Lists.newArrayList();
        }
        List<ReserveConfigDTO> configList = configStrList.stream()
                .filter(StringUtils::isNotEmpty)
                .map(e -> JacksonUtils.toObject(ReserveConfigDTO.class, e))
                .collect(Collectors.toList());
        List<String> noRedisList = configStrList.stream().filter(StringUtils::isEmpty).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(noRedisList)) {
            return configList;
        }
        List<String> noRedisConfigList = Lists.newArrayList();
        for (int i = 0; i < configStrList.size(); i++) {
            String configStr = configStrList.get(i);
            if (Objects.isNull(configStr)) {
                noRedisConfigList.add(storeGuids.get(i));
            }
        }
        // 查询db
        List<ReserveConfigDo> configDbList = reserveConfigService.obtainByStoreGuids(noRedisConfigList);
        List<ReserveConfigDTO> reserveConfigDTOList = ReserveRecordMapstruct.MAPSTRUCT.domainsToDtoList(
                ReserveRecordMapstruct.MAPSTRUCT.dosToDomains(configDbList));
        if (CollectionUtils.isNotEmpty(reserveConfigDTOList)) {
            // cache
            putConfigRedis(reserveConfigDTOList);
            configList.addAll(reserveConfigDTOList);
        }
        return configList;
    }


    /**
     * 删除门店预订配置
     */
    public void remove(String storeGuid) {
        String redisKey = String.format(RESERVE_CONFIG_KEY, storeGuid);
        redisTemplate.delete(redisKey);
    }


    /**
     * 批量删除门店预订配置
     */
    public void removeBatch(List<String> storeGuids) {
        List<String> redisKeyList = storeGuids.stream()
                .map(e -> String.format(RESERVE_CONFIG_KEY, e))
                .collect(Collectors.toList());
        redisTemplate.delete(redisKeyList);
    }


    /**
     * 存入redis
     */
    public void putConfigRedis(List<ReserveConfigDTO> reserveConfigList) {
        redisTemplate.executePipelined(new SessionCallback<Object>() {
            @Override
            public Object execute(RedisOperations operations) throws DataAccessException {
                for (ReserveConfigDTO reserveConfigDTO : reserveConfigList) {
                    String redisKey = String.format(RESERVE_CONFIG_KEY, reserveConfigDTO.getStoreGuid());
                    if (Objects.isNull(reserveConfigDTO)) {
                        operations.opsForValue().set(redisKey, Strings.EMPTY, 7, TimeUnit.DAYS);
                    } else {
                        operations.opsForValue().set(redisKey,
                                JacksonUtils.writeValueAsString(reserveConfigDTO), 7, TimeUnit.DAYS);
                    }
                }
                return null;
            }
        });
    }


}