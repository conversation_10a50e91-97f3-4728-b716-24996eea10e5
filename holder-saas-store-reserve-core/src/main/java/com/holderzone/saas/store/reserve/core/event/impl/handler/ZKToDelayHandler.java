package com.holderzone.saas.store.reserve.core.event.impl.handler;

import com.holderzone.framework.anno.CustomerRegister;
import com.holderzone.framework.event.observer.CustomerObserver;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.resource.common.dto.mq.UnMessage;
import com.holderzone.resource.common.util.MessageType;
import com.holderzone.saas.store.reserve.api.dto.ReserveRecordTransferDTO;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.saas.store.reserve.core.domain.ReserveRecord;
import com.holderzone.saas.store.reserve.core.event.impl.event.DelayEvent;
import com.holderzone.saas.store.reserve.core.rocket.Consume;
import lombok.extern.slf4j.Slf4j;
import org.apache.curator.framework.recipes.queue.DistributedDelayQueue;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ZKToDelayHandler
 * @date 2019/05/30 17:42
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Slf4j
@Component
@CustomerRegister(isRegister = true)
public class ZKToDelayHandler extends ToDelayHandler implements CustomerObserver<DelayEvent> {
    @Autowired
    private DistributedDelayQueue delayQueue;
    @Autowired
    private Consume consume;
    @Override
    public void execute(DelayEvent baseEvent) {
        ReserveRecord record = baseEvent.getReserveRecord();
        String tag = baseEvent.getTag();
        //延时消息
        LocalDateTime dif = null;
        switch (tag){
            case LOCK_TAG:
                dif = getStartDateTime(record);
                break;
            case BE_DELAY_TAG:
                dif = getEndDateTime(record);
                break;
            case ACCEPT_DELAY_TAG:
                dif = getAcceptDelayDateTime(record);
                break;
            case PAY_TIMEOUT_TAG:
                dif = getPayTimeoutDateTime(record);
                break;
            case WARN_MESSAGE_TAG:
                dif = getWarnDateTime(record);
                break;
            default:
        }
        UnMessage<ReserveRecordTransferDTO> unMessage = new UnMessage<>();
        unMessage.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        unMessage.setStoreGuid(UserContextUtils.getStoreGuid());
        unMessage.setMessageType(MessageType.OTHER.code());
        unMessage.setMessage(new ReserveRecordTransferDTO(record.getGuid(),record.getReserveStartTime(),tag));
        try {
            delayQueue.put(unMessage,DateTimeUtils.localDateTime2Mills(dif));
            log.info(String.format("RESERVE GUID : %S ---- %S EVENT START AT:-----------------------:%S",unMessage.getMessage().getGuid(),unMessage.getMessage().getTag(),System.currentTimeMillis()));
        } catch (Exception e) {
           log.error("ZKToDelayHandler execute fail: ",e);
           throw new BusinessException(e.getMessage());
        }
    }
}