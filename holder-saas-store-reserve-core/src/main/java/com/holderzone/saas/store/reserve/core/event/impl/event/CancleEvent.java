package com.holderzone.saas.store.reserve.core.event.impl.event;

import com.holderzone.saas.store.reserve.core.domain.ReserveRecord;
import com.holderzone.saas.store.reserve.core.event.BaseEvent;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @className LaunchEvent
 * @date 2019/04/23 17:24
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Getter
public class CancleEvent extends BaseEvent {
    private Boolean system;

    public CancleEvent(ReserveRecord reserveRecord, Boolean system) {
        super(reserveRecord);
        this.system = system;
    }

    public CancleEvent(ReserveRecord reserveRecord) {
        this(reserveRecord,false);
    }

    private static final String NAME=CancleEvent.class.getSimpleName();

    @Override
    public String getName() {
        return NAME;
    }
}