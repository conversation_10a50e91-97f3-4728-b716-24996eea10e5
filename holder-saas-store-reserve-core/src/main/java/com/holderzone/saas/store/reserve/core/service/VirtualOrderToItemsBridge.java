package com.holderzone.saas.store.reserve.core.service;

import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.reserve.core.domain.Item;

import java.util.Collection;
import java.util.Collections;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className VirtualOrderToItemsBridge
 * @date 2019/04/23 14:39
 * @description //TODO
 * @program holder-saas-store-reserve
 */
public class VirtualOrderToItemsBridge implements Function<DineinOrderDetailRespDTO, Collection<Item>> {

    @Override
    public Collection<Item> apply(DineinOrderDetailRespDTO dineinOrderDetailRespDTO) {
        return Optional.ofNullable(dineinOrderDetailRespDTO.getDineInItemDTOS())
                .orElse(Collections.emptyList())
                .stream().map(
                        (e) -> new Item(e.getGuid(), e)
                ).collect(Collectors.toList());
    }
}