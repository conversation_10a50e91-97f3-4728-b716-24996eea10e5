package com.holderzone.saas.store.reserve.core.lock;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @className LockContext
 * @date 2019/04/24 16:27
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Data
public class LockContext {
    private LockModeEnum lockModeEnum;
    private LockFailStrategy lockFailStrategy;
    private String segment;
    private Map<String,Object> other = new HashMap<>();
    public LockContext(LockModeEnum lockModeEnum) {
        this(lockModeEnum,LockFailStrategy.FAIL_FAST);
    }
    public LockContext(LockModeEnum lockModeEnum, LockFailStrategy lockFailStrategy) {
        this(lockModeEnum,lockFailStrategy,null);
    }
    public LockContext(LockModeEnum lockModeEnum, LockFailStrategy lockFailStrategy, String segment) {
        this.lockModeEnum = lockModeEnum;
        this.lockFailStrategy = lockFailStrategy;
        this.segment = segment;
    }
}