package com.holderzone.saas.store.reserve.core.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ReserveExceptionEnum {

    LAUNCH_OVERTIME("预定时间已过请重新选择"),
    RESERVE_START_TIME_NOT_NULL("预定时间不能为空"),
    PHONE_NOT_NULL("联系人电话不能为空"),
    CREATE_STAFF_GUID_NOT_NULL("预定下单人不能为空"),
    APPLET_RESERVE_RECORD_NOT_UPDATE("小程序预定单不能修改,请更新最新包后重试"),
    FAIL("立即定座失败,请稍后重试"),
    ;

    private final String msg;
}
