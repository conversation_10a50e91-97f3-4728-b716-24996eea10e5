package com.holderzone.saas.store.reserve.core.event.impl.handler;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.framework.anno.CustomerRegister;
import com.holderzone.framework.event.CustomerEvent;
import com.holderzone.framework.event.observer.CustomerObserver;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.reserve.TableDTO;
import com.holderzone.saas.store.reserve.core.dal.ReserveRecordTableRelationDo;
import com.holderzone.saas.store.reserve.core.dal.mapper.ReserveRecordTableRelationDoMapper;
import com.holderzone.saas.store.reserve.core.domain.ReserveRecord;
import com.holderzone.saas.store.reserve.core.event.impl.event.CompensateEvent;
import com.holderzone.saas.store.reserve.core.event.impl.event.PreparedLockTableEvent;
import com.holderzone.saas.store.reserve.core.mapstruct.TableMapstruct;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 到店开台
 */
@Component
@CustomerRegister(isRegister = true)
public class CompensateEventHandler extends TableOpenTableEventHandler<CompensateEvent> implements CustomerObserver<CompensateEvent> {
    @Resource(name = "modifyEventHandler")
    private SaveEventHanlder saveEventHanlder;
    @Autowired
    ReserveRecordTableRelationDoMapper reserveRecordTableRelationDoMapper;

    @Override
    protected List<TableDTO> fetchTables(CompensateEvent baseEvent) {
        return baseEvent.getTableDTOS();
    }

    @Override
    public void execute(CompensateEvent baseEvent) {
        ReserveRecord reserveRecord = baseEvent.getReserveRecord();
        List<TableDTO> tables = fetchTables(baseEvent);
        if (CollectionUtils.isEmpty(tables)) {
            throw new BusinessException("请选择桌台");
        }
        List<String> tableGuids = tables.stream().map(TableDTO::getGuid).collect(Collectors.toList());
        List<ReserveRecordTableRelationDo> relationTables = reserveRecordTableRelationDoMapper.queryReserveGuidByTableAndState(tableGuids, 0);
        if (CollectionUtils.isNotEmpty(relationTables)) {
            relationTables.stream().filter(e -> !e.getReserveRecordGuid().equals(reserveRecord.getGuid())).findAny().ifPresent(s -> {
                throw new BusinessException("已被其他用户预订!!!");
            });
        }
        super.execute(baseEvent);
        List<ReserveRecordTableRelationDo> tableRelationDos = tableRelationDoMapper.selectList(
                new LambdaQueryWrapper<ReserveRecordTableRelationDo>()
                        .eq(ReserveRecordTableRelationDo::getReserveRecordGuid, reserveRecord.getGuid())
        );
        if (CollectionUtils.isNotEmpty(tableRelationDos)) {
            if (Boolean.TRUE.equals(reserveRecord.getIsLocked())) {
                List<String> origin = tableRelationDos.stream()
                        .filter(e -> e.getState() == 1)
                        .map(ReserveRecordTableRelationDo::getTableGuid)
                        .collect(Collectors.toList());
                fetchTables(baseEvent).stream().map(TableDTO::getGuid).forEach(origin::remove);
                if (!origin.isEmpty()) {
                    tableServiceService.cancleReserveLock(origin);
                }
            } else {
                List<String> all = tableRelationDos.stream().map(ReserveRecordTableRelationDo::getTableGuid).collect(Collectors.toList());
                //预定未锁定
                List<String> neo = Collections.emptyList();
                PreparedLockTableEvent preparedLockTableEvent = new PreparedLockTableEvent(reserveRecord, all, neo);
                customerPublish.publish(new CustomerEvent<>(preparedLockTableEvent));
            }
        }
        reserveRecord.setTables(fetchTables(baseEvent).stream().map(TableMapstruct.TABLE_MAPSTRUCT::dtotoDomain).collect(Collectors.toList()));
        saveEventHanlder.saveTable(reserveRecord, false);
    }

}