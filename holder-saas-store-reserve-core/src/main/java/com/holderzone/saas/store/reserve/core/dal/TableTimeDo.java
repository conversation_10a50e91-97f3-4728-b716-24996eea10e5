package com.holderzone.saas.store.reserve.core.dal;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TableTimeDo
 * @date 2019/05/06 17:39
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Getter
@Setter
public class TableTimeDo extends ReserveRecordTableRelationDo {
    private LocalDateTime reserveStartTime;
}