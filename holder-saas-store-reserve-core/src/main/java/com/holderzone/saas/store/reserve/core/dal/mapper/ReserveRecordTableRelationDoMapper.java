package com.holderzone.saas.store.reserve.core.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.saas.store.reserve.api.dto.ReserveRecordQueryDTO;
import com.holderzone.saas.store.reserve.core.dal.ReserveRecordTableRelationDo;
import com.holderzone.saas.store.reserve.core.dal.TableTimeDo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReserveRecordDoMapper
 * @date 2019/04/23 19:09
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Repository
public interface ReserveRecordTableRelationDoMapper extends BaseMapper<ReserveRecordTableRelationDo> {
    void batchInsert(@Param("tables") Collection<ReserveRecordTableRelationDo> tables,
                     @Param("dto") ReserveRecordQueryDTO dto,
                     @Param("removeSame") Boolean removeSame
    );

    void saveBatch(@Param("tables") Collection<ReserveRecordTableRelationDo> tables);

    void deleteByReserveGuid(@Param("guid") String guid);

    List<ReserveRecordTableRelationDo> queryReserveGuidByTableAndState(@Param("tableGuids") List<String> tableGuids, @Param("type") Integer type);
    List<String> queryTableAfterAndIn(@Param("tableGuids") List<String> tableGuids, @Param("time") String time,@Param("record")String guid);

    List<TableTimeDo> queryTablesByTimeSegment(@Param("storeGuid") String storeGuid,
                                               @Param("recordGuid") String recordGuid,
                                               @Param("areaGuid") String areaGuid,
                                               @Param("startTime") LocalDateTime startTime,
                                               @Param("endTime") LocalDateTime endTime);

    List<TableTimeDo> queryTablesByReserveStartTime(@Param("storeGuid") String storeGuid,
                                                    @Param("recordGuid") String recordGuid,
                                                    @Param("areaGuid") String areaGuid,
                                                    @Param("reserveStartTime") LocalDateTime reserveStartTime);

    List<String> queryTableGuidBeReseved(@Param("tables") List<ReserveRecordTableRelationDo> tables,@Param("dto") ReserveRecordQueryDTO dto);
}