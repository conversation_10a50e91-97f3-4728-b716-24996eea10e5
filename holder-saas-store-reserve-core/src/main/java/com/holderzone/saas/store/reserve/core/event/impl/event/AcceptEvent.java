package com.holderzone.saas.store.reserve.core.event.impl.event;

import com.holderzone.saas.store.reserve.core.domain.ReserveRecord;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @className LaunchEvent
 * @date 2019/04/23 17:24
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Getter
public class AcceptEvent extends LaunchEvent {

    public AcceptEvent(ReserveRecord reserveRecord) {
        super(reserveRecord, false);
    }

    public AcceptEvent(ReserveRecord reserveRecord, Boolean saveTable) {
        super(reserveRecord, saveTable);
    }

    private static final String NAME=AcceptEvent.class.getSimpleName();

    @Override
    public String getName() {
        return NAME;
    }
}