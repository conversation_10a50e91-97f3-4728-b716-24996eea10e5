package com.holderzone.saas.store.reserve.core.controller;

import com.google.common.collect.Lists;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.pay.AggPayPreTradingReqDTO;
import com.holderzone.saas.store.dto.pay.AggPayRespDTO;
import com.holderzone.saas.store.dto.pay.SaasAggPayDTO;
import com.holderzone.saas.store.dto.reserve.ReserveRecordGuidDTO;
import com.holderzone.saas.store.dto.table.AreaDTO;
import com.holderzone.saas.store.dto.trade.constant.PayPowerId;
import com.holderzone.saas.store.dto.weixin.resp.WxPayRespDTO;
import com.holderzone.saas.store.enums.BaseDeviceTypeEnum;
import com.holderzone.saas.store.enums.PaymentTypeEnum;
import com.holderzone.saas.store.enums.reserve.ReserveDepoistTypeEnum;
import com.holderzone.saas.store.reserve.api.ReserveAppletApi;
import com.holderzone.saas.store.reserve.api.dto.*;
import com.holderzone.saas.store.reserve.api.enums.ClientStateEnum;
import com.holderzone.saas.store.reserve.api.enums.PayStateEnum;
import com.holderzone.saas.store.reserve.core.common.ReserveConstant;
import com.holderzone.saas.store.reserve.core.common.ReserveExceptionEnum;
import com.holderzone.saas.store.reserve.core.common.ReserveUtils;
import com.holderzone.saas.store.reserve.core.domain.ReserveRecordStateEnum;
import com.holderzone.saas.store.reserve.core.mapstruct.ReserveRecordMapstruct;
import com.holderzone.saas.store.reserve.core.service.ReservePayRecordService;
import com.holderzone.saas.store.reserve.core.service.ReserveRecordService;
import com.holderzone.saas.store.reserve.core.support.ReserveConfigSupport;
import com.holderzone.saas.store.reserve.intergration.table.AggPayClientService;
import com.holderzone.saas.store.reserve.intergration.table.OrganizationClientService;
import com.holderzone.saas.store.reserve.intergration.table.TableClientService;
import com.holderzone.saas.store.util.BigDecimalUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 预订配置
 * 小程序端
 */
@Slf4j
@Primary
@RestController
@RequiredArgsConstructor
public class ReserveAppletControllerImpl implements ReserveAppletApi {

    private final TableClientService tableClientService;

    private final ReserveRecordService reserveRecordService;

    private final ReservePayRecordService reservePayRecordService;

    private final AggPayClientService aggPayClientService;

    private final OrganizationClientService organizationClientService;

    private final ReserveConfigSupport reserveConfigSupport;

    @Override
    public List<ReserveAvailableStoreDTO> getAvailableStoreList(@RequestBody ReserveAppletQueryDTO queryDTO) {
        List<ReserveConfigDTO> configList = getAvailableReserveConfig(queryDTO);
        if (CollectionUtils.isEmpty(configList)) {
            return Lists.newArrayList();
        }
        // 查询门店区域名称
        List<String> availableStoreGuidList = configList.stream()
                .map(ReserveConfigDTO::getStoreGuid)
                .distinct()
                .collect(Collectors.toList());
        SingleDataDTO singleDataDTO = new SingleDataDTO();
        singleDataDTO.setDatas(availableStoreGuidList);
        List<AreaDTO> areaList = tableClientService.batchQueryArea(singleDataDTO);
        return transferReserveAvailableStoreDTOList(configList, areaList);
    }

    @Override
    public ReserveAvailableStoreConfigDTO getAvailableStore(@RequestBody ReserveAppletQueryDTO queryDTO) {
        List<ReserveConfigDTO> configList = getAvailableReserveConfig(queryDTO);
        if (CollectionUtils.isEmpty(configList)) {
            log.error("该门店不可发起小程序预订,storeGuids:{}", JacksonUtils.writeValueAsString(queryDTO.getStoreGuids()));
            return null;
        }
        // 查询门店区域名称
        List<String> availableStoreGuidList = configList.stream()
                .map(ReserveConfigDTO::getStoreGuid)
                .distinct()
                .collect(Collectors.toList());
        SingleDataDTO singleDataDTO = new SingleDataDTO();
        singleDataDTO.setDatas(availableStoreGuidList);
        List<AreaDTO> areaList = tableClientService.batchQueryArea(singleDataDTO);
        List<ReserveAvailableStoreConfigDTO> reserveAvailableStoreConfigList = transferReserveAvailableStoreConfigDTOList(configList, areaList);
        if (CollectionUtils.isEmpty(reserveAvailableStoreConfigList)) {
            log.error("该门店无小程序预订规则, configList:{}", JacksonUtils.writeValueAsString(configList));
            return null;
        }
        return reserveAvailableStoreConfigList.get(0);
    }

    @Override
    public String launch(@RequestBody ReserveRecordDTO reserveRecordDTO) {
        log.info("小程序发起预定入参:{}", JacksonUtils.writeValueAsString(reserveRecordDTO));
        reserveRecordDTO.setDeviceType(BaseDeviceTypeEnum.TCD.getCode());
        ReserveRecordStateEnum reserveRecordStateEnum = ReserveRecordStateEnum.COMMIT;
        if (BigDecimalUtil.greaterThanZero(reserveRecordDTO.getReserveAmount())) {
            reserveRecordStateEnum = ReserveRecordStateEnum.NO_PAY;
        }
        return reserveRecordService.launch(reserveRecordDTO, reserveRecordStateEnum).getGuid();
    }

    @Override
    public Page<ReserveRecordAppletPageDTO> obtainRecordList(@RequestBody ReserveAppletQueryDTO queryDTO) {
        return reserveRecordService.obtainRecordList(queryDTO);
    }

    @Override
    public ReserveRecordAppletDetailDTO obtainRecordDetail(@RequestParam("recordGuid") String recordGuid) {
        ReserveRecordDetailDTO detailDTO = reserveRecordService.obtainDetail(new ReserveRecordGuidDTO(recordGuid));
        if (Objects.isNull(detailDTO)) {
            return null;
        }
        // 查询门店预定配置
        String storeGuid = detailDTO.getStoreGuid();
        ReserveConfigDTO reserveConfigDTO = reserveConfigSupport.get(storeGuid);
        ReserveRecordAppletDetailDTO appletDetailDTO = ReserveRecordMapstruct.MAPSTRUCT.toAppletDetailDTO(detailDTO);
        appletDetailDTO.setInvitationImages(reserveConfigDTO.getInvitationImages());
        return appletDetailDTO;
    }

    @Override
    public ReserveRecordAppletDetailDTO querySameTimeRecord(@RequestBody ReserveAppletQueryDTO queryDTO) {
        LocalDateTime reserveStartTime = queryDTO.getReserveStartTime();
        if (Objects.isNull(reserveStartTime)) {
            throw new BusinessException(ReserveExceptionEnum.RESERVE_START_TIME_NOT_NULL.getMsg());
        }
        String phone = queryDTO.getPhone();
        if (StringUtils.isEmpty(phone)) {
            throw new BusinessException(ReserveExceptionEnum.PHONE_NOT_NULL.getMsg());
        }
        // 查询同时间的预定单
        ReserveRecordDetailDTO detailDTO = reserveRecordService.querySameTimeRecord(queryDTO);
        return ReserveRecordMapstruct.MAPSTRUCT.toAppletDetailDTO(detailDTO);
    }

    @Override
    public ReserveRecordAppletPageDTO queryLastTimeRecord(@RequestBody ReserveAppletQueryDTO queryDTO) {
        if (StringUtils.isEmpty(queryDTO.getCreateUserId())) {
            throw new BusinessException(ReserveExceptionEnum.CREATE_STAFF_GUID_NOT_NULL.getMsg());
        }
        queryDTO.setStates(Lists.newArrayList(ClientStateEnum.PASS.getCode()));
        queryDTO.setIsDelay(false);
        return reserveRecordService.queryLastTimeRecord(queryDTO);
    }

    @Override
    public void cancel(@RequestBody ReserveRecordGuidDTO reserveRecordGuidDTO) {
        log.info("小程序预定取消入参:{}", JacksonUtils.writeValueAsString(reserveRecordGuidDTO));
        reserveRecordGuidDTO.setDeviceType(BaseDeviceTypeEnum.TCD.getCode());
        reserveRecordService.cancle(reserveRecordGuidDTO);
    }

    @Override
    public ReserveRecordDetailDTO memberPay(@RequestBody ReserveAppletPayDTO reserveAppletPayDTO) {
        log.info("小程序预定会员支付入参:{}", JacksonUtils.writeValueAsString(reserveAppletPayDTO));
        return reserveRecordService.memberPay(reserveAppletPayDTO);
    }

    @Override
    public WxPayRespDTO wechatPay(@RequestBody ReserveAppletPayDTO reserveAppletPayDTO) {
        log.info("小程序预定微信支付入参:{}", JacksonUtils.writeValueAsString(reserveAppletPayDTO));
        String payGuid = ReserveUtils.reservePayGuid();
        ReserveRecordDetailDTO detailDTO = reserveRecordService.obtainDetail(new ReserveRecordGuidDTO(reserveAppletPayDTO.getRecordGuid()));
        log.info("预定单详情:{}", JacksonUtils.writeValueAsString(detailDTO));
        if (!Objects.equals(ReserveRecordStateEnum.NO_PAY.name(), detailDTO.getState())) {
            throw new BusinessException("该订单状态发生变化, 请刷新后重试");
        }
        SaasAggPayDTO saasAggPayDTO = buildSaasAggPayDTO(detailDTO, reserveAppletPayDTO, payGuid);
        log.info("小程序预定发起聚合支付：{}", JacksonUtils.writeValueAsString(saasAggPayDTO));
        AggPayRespDTO payRespDTO = aggPayClientService.pay(saasAggPayDTO);
        log.info("小程序预定聚合支付返回结果：{}", payRespDTO);
        // 构建预定使用微信支付明细请求
        ReservePayReqDTO reservePayReqDTO = buildReserveAggPayReqDTO(detailDTO);
        if (ReserveConstant.SUCCESS_CODE.equals(payRespDTO.getCode())) {
            reservePayRecordService.savePayRecordDO(reservePayReqDTO, PayStateEnum.PENDING, payGuid);
            WxPayRespDTO wxPayRespDTO = new WxPayRespDTO();
            wxPayRespDTO.setCouldPay(1);
            wxPayRespDTO.setResult(payRespDTO);
            return wxPayRespDTO;
        } else {
            reservePayRecordService.savePayRecordDO(reservePayReqDTO, PayStateEnum.FAILURE, payGuid);
            throw new BusinessException(payRespDTO.getMsg());
        }
    }

    /**
     * 构建微信支付请求参数
     */
    private SaasAggPayDTO buildSaasAggPayDTO(ReserveRecordDetailDTO detailDTO, ReserveAppletPayDTO reserveAppletPayDTO, String payGuid) {
        AggPayPreTradingReqDTO aggWeChatPayDTO = new AggPayPreTradingReqDTO();
        StoreDTO storeDTO = organizationClientService.queryStoreByGuid(detailDTO.getStoreGuid());
        if (Objects.isNull(storeDTO)) {
            log.error("门店信息不存在, storeGuid:{}", detailDTO.getStoreGuid());
            throw new BusinessException("门店信息不存在");
        }
        aggWeChatPayDTO.setStoreName(storeDTO.getName());
        aggWeChatPayDTO.setGoodsName(detailDTO.getOrderNo());
        aggWeChatPayDTO.setBody(detailDTO.getOrderNo());
        aggWeChatPayDTO.setPayPowerId(PayPowerId.WX_MINI_PROGRAM_ONLINE.getId());
        aggWeChatPayDTO.setTerminalId(payGuid);
        aggWeChatPayDTO.setAmount(reserveAppletPayDTO.getReserveAmount());
        aggWeChatPayDTO.setOrderGUID(detailDTO.getGuid());
        aggWeChatPayDTO.setPayGUID(payGuid);
        aggWeChatPayDTO.setSubAppId(reserveAppletPayDTO.getAppId());
        aggWeChatPayDTO.setSubOpenId(reserveAppletPayDTO.getOpenId());
        aggWeChatPayDTO.setClientIp(reserveAppletPayDTO.getClientIp());
        SaasAggPayDTO saasAggPayDTO = new SaasAggPayDTO();
        saasAggPayDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        saasAggPayDTO.setStoreGuid(detailDTO.getStoreGuid());
        saasAggPayDTO.setDeviceType(BaseDeviceTypeEnum.TCD.getCode());
        saasAggPayDTO.setIsLast(true);
        saasAggPayDTO.setSaasCallBackUrl(ReserveConstant.CALLBACK_URL);
        saasAggPayDTO.setReqDTO(aggWeChatPayDTO);
        return saasAggPayDTO;
    }

    /**
     * 构建预定使用微信支付明细请求
     */
    private ReservePayReqDTO buildReserveAggPayReqDTO(ReserveRecordDetailDTO detailDTO) {
        ReservePayReqDTO reservePayReqDTO = new ReservePayReqDTO();
        reservePayReqDTO.setGuid(detailDTO.getGuid());
        reservePayReqDTO.setStoreGuid(detailDTO.getStoreGuid());
        reservePayReqDTO.setPaymentType(PaymentTypeEnum.AGG.getCode());
        reservePayReqDTO.setPaymentTypeName(PaymentTypeEnum.AGG.getDesc());
        reservePayReqDTO.setReserveAmount(detailDTO.getReserveAmount());
        return reservePayReqDTO;
    }

    /**
     * 获取可预订的门店
     */
    private List<ReserveConfigDTO> getAvailableReserveConfig(ReserveAppletQueryDTO queryDTO) {
        if (CollectionUtils.isEmpty(queryDTO.getStoreGuids()) || Objects.isNull(queryDTO.getReserveDate())) {
            return Lists.newArrayList();
        }
        List<ReserveConfigDTO> configList = reserveConfigSupport.list(queryDTO.getStoreGuids());
        if (CollectionUtils.isEmpty(configList)) {
            return Lists.newArrayList();
        }
        configList = configList.stream()
                .filter(e -> isAvailableReserve(e, queryDTO))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(configList)) {
            return Lists.newArrayList();
        }
        return configList;
    }


    /**
     * ReserveConfigDTO -> ReserveAvailableStoreConfigDTO
     */
    private List<ReserveAvailableStoreConfigDTO> transferReserveAvailableStoreConfigDTOList(List<ReserveConfigDTO> configList,
                                                                                            List<AreaDTO> areaList) {
        // 门店区域名称替换最新
        filterArea(configList, areaList);
        // 排除没有区域的
        configList = configList.stream()
                .filter(e -> CollectionUtils.isNotEmpty(e.getReserveArea()))
                .collect(Collectors.toList());
        return ReserveRecordMapstruct.MAPSTRUCT
                .configDTOListToAvailableStoreConfigDTOList(configList);
    }

    /**
     * ReserveConfigDTO -> ReserveAvailableStoreDTO
     */
    private List<ReserveAvailableStoreDTO> transferReserveAvailableStoreDTOList(List<ReserveConfigDTO> configList,
                                                                                List<AreaDTO> areaList) {
        // 门店区域名称替换最新
        filterArea(configList, areaList);
        // 排除没有区域的
        configList = configList.stream()
                .filter(e -> CollectionUtils.isNotEmpty(e.getReserveArea()))
                .collect(Collectors.toList());
        List<ReserveAvailableStoreDTO> availableStoreList = Lists.newArrayList();
        for (ReserveConfigDTO configDTO : configList) {
            ReserveAvailableStoreDTO reserveAvailableStoreDTO = new ReserveAvailableStoreDTO();
            reserveAvailableStoreDTO.setStoreGuid(configDTO.getStoreGuid());
            reserveAvailableStoreDTO.setReserveAreaNames(configDTO.getReserveArea());
            availableStoreList.add(reserveAvailableStoreDTO);
        }
        return availableStoreList;
    }

    /**
     * 门店区域名称替换最新
     */
    private void filterArea(List<ReserveConfigDTO> configList, List<AreaDTO> areaList) {
        Map<String, String> areaMap = areaList.stream()
                .collect(Collectors.toMap(AreaDTO::getGuid, AreaDTO::getAreaName, (key1, key2) -> key1));
        for (ReserveConfigDTO configDTO : configList) {
            List<com.holderzone.saas.store.reserve.api.dto.AreaDTO> reserveAreaList = configDTO.getReserveArea();
            Iterator<com.holderzone.saas.store.reserve.api.dto.AreaDTO> iterator = reserveAreaList.iterator();
            while (iterator.hasNext()) {
                com.holderzone.saas.store.reserve.api.dto.AreaDTO areaDTO = iterator.next();
                String areaName = areaMap.get(areaDTO.getGuid());
                if (StringUtils.isEmpty(areaName)) {
                    iterator.remove();
                    continue;
                }
                areaDTO.setName(areaName);
            }
            configDTO.setReserveArea(reserveAreaList);
            if (Boolean.TRUE.equals(configDTO.getDepositFlag()) && Objects.equals(configDTO.getDepositType(), ReserveDepoistTypeEnum.BY_AREA.getCode())) {
                configDTO.setAreaDeposit(reserveAreaList);
            }
        }
    }

    /**
     * 判断门店是否符合预订规则
     */
    private boolean isAvailableReserve(ReserveConfigDTO reserveConfigDTO, ReserveAppletQueryDTO queryDTO) {
        // 小程序是否可预订
        String deviceType = reserveConfigDTO.getDeviceType();
        List<String> deviceTypeList = Arrays.stream(deviceType.split(",")).collect(Collectors.toList());
        if (!deviceTypeList.contains(String.valueOf(BaseDeviceTypeEnum.TCD.getCode()))) {
            return false;
        }
        // 是否有配置区域
        if (CollectionUtils.isEmpty(reserveConfigDTO.getReserveArea())) {
            return false;
        }
        Integer reserveDay = reserveConfigDTO.getReserveDay();
        LocalDate maxDate = LocalDate.now().plusDays(reserveDay);
        return queryDTO.getReserveDate().isBefore(maxDate);
    }
}