package com.holderzone.saas.store.reserve.core.event.impl.event;

import com.holderzone.saas.store.reserve.core.domain.ReserveRecord;
import com.holderzone.saas.store.reserve.core.event.BaseEvent;
import lombok.Getter;

@Getter
public class SystemMessageEvent extends BaseEvent {

    private static final String NAME = SystemMessageEvent.class.getSimpleName();

    public SystemMessageEvent(ReserveRecord reserveRecord) {
        super(reserveRecord);
    }

    @Override
    public String getName() {
        return NAME;
    }
}
