package com.holderzone.saas.store.reserve.core.domain.standar;

import com.holderzone.framework.event.CustomerEvent;
import com.holderzone.framework.event.publish.impl.CustomerPublishImpl;
import com.holderzone.saas.store.dto.reserve.TableDTO;
import com.holderzone.saas.store.reserve.core.common.BaseDTOThreadLocal;
import com.holderzone.saas.store.reserve.core.domain.ReserveRecord;
import com.holderzone.saas.store.reserve.core.event.impl.event.*;
import com.holderzone.saas.store.reserve.core.mapstruct.TableMapstruct;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className EventReserveRecord
 * @date 2019/04/23 15:45
 * @description //TODO
 * @program holder-saas-store-reserve
 */
public class EventReserveRecord extends ReserveRecord {
    private static CustomerPublishImpl customerPublish;

    @Override
    public ReserveRecord cancle() {
        customerPublish.publish(new CustomerEvent<>(new CancleEvent(this)));
        return this;
    }

    @Override
    public ReserveRecord memberPay() {
        customerPublish.publish(new CustomerEvent<>(new MemberPayEvent(this, this.getMemberInfoGuid(),
                this.getMemberInfoCardGuid(), this.getMemberPassword(), this.getPayType())));
        return this;
    }

    @Override
    public ReserveRecord aggPay() {
        customerPublish.publish(new CustomerEvent<>(new AggPayEvent(this)));
        return this;
    }

    @Override
    public ReserveRecord partRefund() {
        customerPublish.publish(new CustomerEvent<>(new PartRefundEvent(this)));
        return this;
    }

    @Override
    public ReserveRecord accept() {
        customerPublish.publish(new CustomerEvent<>(new AcceptEvent(this, this.getTables() != null && !this.getTables().isEmpty())));
        return this;
    }


    @Override
    public ReserveRecord lauch() {
        customerPublish.publish(new CustomerEvent<>(new LaunchEvent(this, this.getTables() != null && !this.getTables().isEmpty())));
        return this;
    }

    @Override
    public ReserveRecord modify() {
        customerPublish.publish(new CustomerEvent<>(new ModifyEvent(this, this.getTables() != null && !this.getTables().isEmpty())));
        return this;
    }

    @Override
    public EffectiveEvent effect() {
        EffectiveEvent effectiveEvent = new EffectiveEvent(this, BaseDTOThreadLocal.get());
        customerPublish.publish(new CustomerEvent<>(effectiveEvent));
        return effectiveEvent;
    }

    @Override
    public CompensateEvent compensate() {
        List<TableDTO> tableDTOS = this.getTables().stream().map(TableMapstruct.TABLE_MAPSTRUCT::domaintoDto).collect(Collectors.toList());
        CompensateEvent compensateEvent = new CompensateEvent(this, BaseDTOThreadLocal.get(), tableDTOS);
        customerPublish.publish(new CustomerEvent<>(compensateEvent));
        return compensateEvent;
    }

    public static void setCustomerPublish(CustomerPublishImpl customerPublish) {
        EventReserveRecord.customerPublish = customerPublish;
    }
}