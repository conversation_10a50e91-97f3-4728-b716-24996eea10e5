package com.holderzone.saas.store.reserve.core.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.saas.store.dto.reserve.ReserveItemDTO;
import com.holderzone.saas.store.dto.reserve.ReserveReportParamDTO;
import com.holderzone.saas.store.reserve.core.dal.ReserveItemDo;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ReserveItemDOMapper extends BaseMapper<ReserveItemDo> {

    List<ReserveItemDTO> itemCount(ReserveReportParamDTO paramDTO);
}
