package com.holderzone.saas.store.reserve.core.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.saas.store.reserve.core.dal.ReserveConfigDo;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReserveConfigDoMapper
 * @date 2019/04/23 19:09
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Repository
public interface ReserveConfigDoMapper extends BaseMapper<ReserveConfigDo> {
}