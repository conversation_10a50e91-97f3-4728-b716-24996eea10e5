package com.holderzone.saas.store.reserve.core.event.impl.handler;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.anno.CustomerRegister;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.event.CustomerEvent;
import com.holderzone.framework.event.observer.CustomerObserver;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.redisson.sdk.lock.RedissonLockUtil;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.member.terminal.dto.order.RequestRefundPay;
import com.holderzone.saas.store.dto.order.ShopOrderIslandUserAmountDTO;
import com.holderzone.saas.store.dto.pay.AggRefundReqDTO;
import com.holderzone.saas.store.dto.pay.AggRefundRespDTO;
import com.holderzone.saas.store.dto.pay.SaasAggRefundDTO;
import com.holderzone.saas.store.dto.pay.constant.AggRefundStateEnum;
import com.holderzone.saas.store.dto.reserve.UpdateOrderReserveReqDTO;
import com.holderzone.saas.store.enums.BaseDeviceTypeEnum;
import com.holderzone.saas.store.enums.PaymentTypeEnum;
import com.holderzone.saas.store.enums.weixin.WxAppletMemberPayTypeEnum;
import com.holderzone.saas.store.reserve.api.enums.ClientStateEnum;
import com.holderzone.saas.store.reserve.api.enums.OrderTypeEnum;
import com.holderzone.saas.store.reserve.api.enums.PayStateEnum;
import com.holderzone.saas.store.reserve.core.common.ReserveRoleEnum;
import com.holderzone.saas.store.reserve.core.common.ReserveUtils;
import com.holderzone.saas.store.reserve.core.dal.ReservePayRecordDO;
import com.holderzone.saas.store.reserve.core.dal.ReserveRecordDo;
import com.holderzone.saas.store.reserve.core.dal.mapper.ReservePayRecordDoMapper;
import com.holderzone.saas.store.reserve.core.dal.mapper.ReserveRecordDoMapper;
import com.holderzone.saas.store.reserve.core.domain.ReserveRecord;
import com.holderzone.saas.store.reserve.core.domain.ReserveRecordStateEnum;
import com.holderzone.saas.store.reserve.core.domain.Table;
import com.holderzone.saas.store.reserve.core.event.BaseEventHandler;
import com.holderzone.saas.store.reserve.core.event.impl.event.CancleEvent;
import com.holderzone.saas.store.reserve.core.event.impl.event.PreparedLockTableEvent;
import com.holderzone.saas.store.reserve.core.event.impl.event.SystemMessageEvent;
import com.holderzone.saas.store.reserve.core.event.impl.event.UnLockTableEvent;
import com.holderzone.saas.store.reserve.core.rocket.Consume;
import com.holderzone.saas.store.reserve.core.service.ReservePayRecordService;
import com.holderzone.saas.store.reserve.intergration.table.AggPayClientService;
import com.holderzone.saas.store.reserve.intergration.table.MemberTerminalClientService;
import com.holderzone.saas.store.reserve.intergration.table.TcdClientService;
import com.holderzone.saas.store.reserve.intergration.table.TradeClientService;
import com.holderzone.saas.store.util.BigDecimalUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className LaunchEventHandler
 * @date 2019/04/23 17:34
 * @description 取消预定
 * @program holder-saas-store-reserve
 */
@Slf4j
@Component
@RequiredArgsConstructor
@CustomerRegister(isRegister = true)
public class CancleEventHandler extends BaseEventHandler<CancleEvent> implements CustomerObserver<CancleEvent> {

    private final ReserveRecordDoMapper reserveRecordDoMapper;

    private final ReservePayRecordDoMapper reservePayRecordDoMapper;

    private final ReservePayRecordService reservePayRecordService;

    private final MemberTerminalClientService memberTerminalClientService;

    private final TcdClientService tcdClientService;

    private final AggPayClientService aggPayClientService;

    private final ExecutorService cancelAfterExecutor;

    private final TradeClientService tradeClientService;

    @Override
    public void execute(CancleEvent baseEvent) {
        // 校验是否可退款
        checkCancel(baseEvent);
        ReserveRecord reserveRecord = baseEvent.getReserveRecord();
        // put
        UserContext userContext = UserContextUtils.get();
        if (StringUtils.isEmpty(userContext.getStoreGuid())) {
            userContext.setStoreGuid(reserveRecord.getStoreGuid());
            UserContextUtils.put(userContext);
        }
        // 修改预定单数据
        updateReserveRecord(baseEvent);
        // 取消预定单 退预定金处理
        cancelReserveAmountHandler(reserveRecord);
        // 取消预定单后置处理
        cancelAfterHandler(reserveRecord);
    }

    /**
     * 校验是否可退款
     */
    private void checkCancel(CancleEvent baseEvent) {
        ReserveRecord reserveRecord = baseEvent.getReserveRecord();
        log.info("[checkCancel]reserveRecord={}", JacksonUtils.writeValueAsString(reserveRecord));
        if (Objects.equals(reserveRecord.getOrderType(), OrderTypeEnum.RESERVE_PAY.getCode())) {
            return;
        }
        LocalDateTime ref = Consume.THREAD_LOCAL.get();
        Assert.isTrue(reserveRecord.getState() == ReserveRecordStateEnum.NO_PAY.getCode()
                || reserveRecord.getState() == ReserveRecordStateEnum.COMMIT.getCode()
                || reserveRecord.getState() == ReserveRecordStateEnum.PASS.getCode(), "该订单无法取消, 请刷新后重试");
        if (Boolean.TRUE.equals(baseEvent.getSystem()) && ref != null && !reserveRecord.getReserveStartTime().equals(ref)) {
            throw new BusinessException("系统自动取消失败,时间被修改");
        }
        if (Boolean.TRUE.equals(baseEvent.getSystem()) && reserveRecord.getState() != ReserveRecordStateEnum.NO_PAY.getCode()
                && ((reserveRecord.getState() & ReserveRecordStateEnum.SYSTEM_CANCLE.getCode()) == ReserveRecordStateEnum.SYSTEM_CANCLE.getCode()
                || (reserveRecord.getState() & ClientStateEnum.PICK_TABLE.getCode()) == ClientStateEnum.PICK_TABLE.getCode())) {
            throw new BusinessException("系统自动取消失败,状态变更");
        }
        if (BaseDeviceTypeEnum.isApplet(reserveRecord.getOperateDeviceType())
                && BigDecimalUtil.greaterThanZero(reserveRecord.getReserveAmount())
                && Objects.equals(ReserveRecordStateEnum.PASS.getCode(), reserveRecord.getState())
                && Objects.nonNull(reserveRecord.getMaxCancelableTime())
                && reserveRecord.getMaxCancelableTime().isBefore(LocalDateTime.now())) {
            // 小程序发起取消 并且支付了预定金, 并且商家接单 需要判断是否超过可退款时间
            throw new BusinessException("已过取消时间");
        }
    }

    /**
     * 修改预定单数据
     */
    private void updateReserveRecord(CancleEvent baseEvent) {
        ReserveRecord reserveRecord = baseEvent.getReserveRecord();
        ReserveRecordDo recordDO = new ReserveRecordDo();
        ReserveRecordDo reserveRecordDo = reserveRecordDoMapper.selectOne(new LambdaQueryWrapper<ReserveRecordDo>()
                .eq(ReserveRecordDo::getGuid, reserveRecord.getGuid()));
        if (Objects.isNull(reserveRecordDo)) {
            log.warn("[修改预定单数据]预订单不存在，reserveGuid={}", reserveRecord.getGuid());
            return;
        }
        setRefundAmount(reserveRecordDo, recordDO, reserveRecord);
        recordDO.setGuid(reserveRecord.getGuid());
        // 产品需求：只撤销金额，不变更状态，因为已经到店了
        setState(baseEvent, reserveRecord, recordDO);
        recordDO.setCancleTime(LocalDateTime.now());
        recordDO.setGmtModified(LocalDateTime.now());
        if (BaseDeviceTypeEnum.isApplet(reserveRecord.getDeviceType())) {
            // 小程序取消
            recordDO.setCancelRole(ReserveRoleEnum.USER.getRole());
            recordDO.setCancleReason(reserveRecord.getCancleReason());
            recordDO.setCancelUserGuid(recordDO.getCreateStaffGuid());
            recordDO.setCancelUserName(ReserveRoleEnum.USER.getMsg());
        } else {
            // 一体机取消
            recordDO.setCancelRole(ReserveRoleEnum.MERCHANT.getRole());
            recordDO.setCancleReason(StringUtils.isEmpty(reserveRecord.getCancleReason()) ? "拒单" : reserveRecord.getCancleReason());
            recordDO.setCancelUserGuid(UserContextUtils.getUserGuid());
            recordDO.setCancelUserName(UserContextUtils.getUserName());
        }
        if (Boolean.TRUE.equals(baseEvent.getSystem())) {
            // 系统取消/自动取消
            recordDO.setCancelRole(reserveRecord.getCancelRole());
            if (StringUtils.isEmpty(reserveRecord.getCancelRole())) {
                recordDO.setCancelRole(ReserveRoleEnum.SYSTEM.getRole());
            }
            recordDO.setCancleReason(StringUtils.isEmpty(reserveRecord.getCancleReason())
                    ? ReserveRoleEnum.SYSTEM.getMsg() : reserveRecord.getCancleReason());
            recordDO.setCancelUserGuid(ReserveRoleEnum.SYSTEM.getRole());
            recordDO.setCancelUserName(ReserveRoleEnum.SYSTEM.getRole());
        }
        recordDO.setModifiedStaffGuid(recordDO.getCancelUserGuid());
        recordDO.setModifiedStaffName(recordDO.getCancelUserName());
        // copy
        reserveRecord.setState(recordDO.getState());
        reserveRecord.setCancelUserName(recordDO.getCancelUserName());
        reserveRecord.setCancelUserGuid(recordDO.getCancelUserGuid());
        reserveRecord.setCancleTime(recordDO.getCancleTime());
        reserveRecord.setCancleReason(recordDO.getCancleReason());
        reserveRecord.setModifiedStaffGuid(recordDO.getModifiedStaffGuid());
        reserveRecord.setModifiedStaffName(recordDO.getModifiedStaffName());
        reserveRecord.setGmtModified(recordDO.getGmtModified());
        reserveRecordDoMapper.update(recordDO,
                new LambdaQueryWrapper<ReserveRecordDo>().eq(ReserveRecordDo::getGuid, recordDO.getGuid()));
    }

    private void setRefundAmount(ReserveRecordDo reserveRecordDo, ReserveRecordDo recordDO, ReserveRecord reserveRecord) {
        if (BigDecimalUtil.greaterThanZero(reserveRecordDo.getRefundAmount())) {
            recordDO.setRefundAmount(reserveRecordDo.getRefundAmount().add(reserveRecord.getReserveRefundAmount()));
        } else {
            recordDO.setRefundAmount(reserveRecord.getReserveRefundAmount());
        }
    }

    private void setState(CancleEvent baseEvent, ReserveRecord reserveRecord, ReserveRecordDo recordDO) {
        if (reserveRecord.getState() == ReserveRecordStateEnum.OPEN_TABLE.getCode()
                || reserveRecord.getState() == ReserveRecordStateEnum.PICK_TABLE.getCode()
                || reserveRecord.getState() == ReserveRecordStateEnum.FINISH.getCode()) {
            recordDO.setState(reserveRecord.getState());
        } else {
            // 默认系统取消
            int cancelState = Boolean.TRUE.equals(baseEvent.getSystem()) ? ReserveRecordStateEnum.SYSTEM_CANCLE.getCode() : ReserveRecordStateEnum.CANCLE.getCode();
            recordDO.setState(reserveRecord.isNeedPay() && reserveRecord.getState() == ReserveRecordStateEnum.NO_PAY.getCode()
                    ? ReserveRecordStateEnum.NO_PAY_CANCEL.getCode() : cancelState);
        }
    }


    /**
     * 取消预定单 退预定金处理
     */
    private void cancelReserveAmountHandler(ReserveRecord reserveRecord) {
        // 有支付预订金时，增加一条预订金退款记录
        if (Objects.isNull(reserveRecord.getReserveAmount()) || reserveRecord.getReserveAmount().compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }
        if (Objects.equals(ReserveRecordStateEnum.NO_PAY_CANCEL.getCode(), reserveRecord.getState())) {
            // 未支付定金
            return;
        }
        // 原路退回
        refundForBacktrack(reserveRecord);
    }

    /**
     * 一体机下的预定单
     * 现金退款
     */
    private void refundForCash(ReserveRecord reserveRecord) {
        ReservePayRecordDO reservePayRecordDO = new ReservePayRecordDO();
        reservePayRecordDO.setGuid(ReserveUtils.reservePayGuid())
                .setReserveGuid(reserveRecord.getGuid())
                .setStoreGuid(reserveRecord.getStoreGuid())
                // 金额为负表示退款
                .setReserveAmount(reserveRecord.getReserveAmount().negate())
                // 预订退款方式为现金
                .setPayType(PaymentTypeEnum.CASH.getCode())
                .setPayTypeName(PaymentTypeEnum.CASH.getDesc())
                // 新增类型5表示退款
                .setState(5)
                .setGmtCreate(LocalDateTime.now()).setGmtModified(LocalDateTime.now());
        reservePayRecordDoMapper.insert(reservePayRecordDO);
    }

    /**
     * 小程序下的预定单
     * 原路返回
     */
    private void refundForBacktrack(ReserveRecord reserveRecord) {
        ReservePayRecordDO reservePayRecordDO = reservePayRecordService.selectOneByReserveGuid(reserveRecord.getGuid());
        if (Objects.isNull(reservePayRecordDO)) {
            log.info("未支付直接取消, reserveGuid:{}", reserveRecord.getGuid());
            return;
        }
        Integer payType = reservePayRecordDO.getPayType();
        String payTypeName = reservePayRecordDO.getPayTypeName();
        if (PaymentTypeEnum.AGG.getCode() == payType) {
            // 微信支付退款
            wechatRefund(reserveRecord, reservePayRecordDO);
        } else if (PaymentTypeEnum.MEMBER.getCode() == payType) {
            // 会员储值余额退款
            memberStoredBalanceRefund(reservePayRecordDO);
        } else if (PaymentTypeEnum.OTHER.getCode() == payType && WxAppletMemberPayTypeEnum.INCOME_AMOUNT.getMessage().equals(payTypeName)) {
            // 会员收益余额退款
            memberIncomeBalanceRefund(reserveRecord, reservePayRecordDO);
        }
        ReservePayRecordDO refundPayRecordDO = new ReservePayRecordDO();
        refundPayRecordDO.setGuid(ReserveUtils.reservePayGuid())
                .setReserveGuid(reserveRecord.getGuid())
                .setStoreGuid(reserveRecord.getStoreGuid())
                // 金额为负表示退款
                .setReserveAmount(reserveRecord.getReserveAmount().negate())
                .setPayType(reservePayRecordDO.getPayType())
                .setPayTypeName(reservePayRecordDO.getPayTypeName())
                .setState(PayStateEnum.REFOUNDED.getCode())
                .setGmtCreate(LocalDateTime.now())
                .setGmtModified(LocalDateTime.now());
        reservePayRecordDoMapper.insert(refundPayRecordDO);
    }

    /**
     * 会员收益余额退款
     */
    private void memberIncomeBalanceRefund(ReserveRecord reserveRecord, ReservePayRecordDO reservePayRecordDO) {
        try {
            ShopOrderIslandUserAmountDTO shopOrderIslandUserAmountDTO = new ShopOrderIslandUserAmountDTO();
            shopOrderIslandUserAmountDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
            shopOrderIslandUserAmountDTO.setOperSubjectGuid(reserveRecord.getOperSubjectGuid());
            shopOrderIslandUserAmountDTO.setMemberInfoCardGuid(reserveRecord.getMemberInfoCardGuid());
            shopOrderIslandUserAmountDTO.setMemberInfoGuid(reserveRecord.getMemberInfoGuid());
            shopOrderIslandUserAmountDTO.setStoreGuid(reserveRecord.getStoreGuid());
            shopOrderIslandUserAmountDTO.setAmount(reservePayRecordDO.getReserveAmount());
            shopOrderIslandUserAmountDTO.setOrderNo(reserveRecord.getOrderNo());
            shopOrderIslandUserAmountDTO.setOrderGuid(reserveRecord.getGuid());
            shopOrderIslandUserAmountDTO.setWeAppUserId(Long.valueOf(reserveRecord.getCreateStaffGuid()));
            log.info("用户收益余额退款请求参数:{}", JacksonUtils.writeValueAsString(shopOrderIslandUserAmountDTO));
            Map<String, Object> resultMap = tcdClientService.islandUserAmountRefund(shopOrderIslandUserAmountDTO);
            log.info("用户收益余额退款返回参数:{}", resultMap);
        } catch (Exception e) {
            log.error("用户收益余额退款失败,e:{}", e.getMessage());
            throw new BusinessException("用户收益余额退款失败");
        }
    }


    /**
     * 会员储值余额退款
     */
    private void memberStoredBalanceRefund(ReservePayRecordDO reservePayRecordDO) {
        RequestRefundPay requestRefundPay = new RequestRefundPay();
        requestRefundPay.setMemberFundingDetailGuid(reservePayRecordDO.getMemberFundingDetailGuid());
        requestRefundPay.setRefundAmount(reservePayRecordDO.getReserveAmount());
        memberTerminalClientService.reserveRefundPay(requestRefundPay);
    }

    /**
     * 微信支付退款
     */
    private void wechatRefund(ReserveRecord reserveRecord, ReservePayRecordDO reservePayRecordDO) {
        // 构建退款请求参数
        AggRefundReqDTO aggRefundReqDTO = new AggRefundReqDTO();
        aggRefundReqDTO.setOrderGUID(reserveRecord.getGuid());
        aggRefundReqDTO.setRefundType(1);
        aggRefundReqDTO.setRefundFee(reserveRecord.getReserveAmount().abs());
        aggRefundReqDTO.setReason("聚合支付退款");
        aggRefundReqDTO.setPayGUID(String.valueOf(reservePayRecordDO.getGuid()));
        SaasAggRefundDTO saasAggRefundDTO = new SaasAggRefundDTO();
        saasAggRefundDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        saasAggRefundDTO.setStoreGuid(reserveRecord.getStoreGuid());
        saasAggRefundDTO.setAggRefundReqDTO(aggRefundReqDTO);
        AggRefundRespDTO refund = aggPayClientService.refund(saasAggRefundDTO);
        if (refundFailure(refund)) {
            log.warn("预定单微信退款失败,orderGuid:{},payGuid:{},amount{},code{},message{}", reserveRecord.getGuid(),
                    reservePayRecordDO.getGuid(), aggRefundReqDTO.getRefundType(), refund.getCode(), refund.getMsg());
            throw new BusinessException(refund.getMsg());
        } else {
            log.warn("预定单微信退款,orderGuid:{},payGuid:{},amount{},code{},message{}", reserveRecord.getGuid(),
                    reservePayRecordDO.getGuid(), aggRefundReqDTO.getRefundFee(), refund.getCode(),
                    refund.getMsg());
        }
    }

    private boolean refundFailure(AggRefundRespDTO refund) {
        return !("10000".equals(refund.getCode()) || "20045".equals(refund.getCode()))
                || refund.getMsg().contains("失败")
                || refund.getState().equals(AggRefundStateEnum.REFUND_FAILURE.getState());
    }

    /**
     * 取消预定单后置处理
     */
    private void cancelAfterHandler(ReserveRecord reserveRecord) {
        if (Objects.equals(reserveRecord.getOrderType(), OrderTypeEnum.RESERVE_PAY.getCode())
                && BigDecimalUtil.greaterThanZero(reserveRecord.getReserveAmount())) {
            // 预付金更新订单表数据
            UpdateOrderReserveReqDTO reqDTO = new UpdateOrderReserveReqDTO();
            reqDTO.setOrderGuid(reserveRecord.getOrderGuid());
            reqDTO.setReserveAmount(reserveRecord.getReserveAmount().subtract(reserveRecord.getReserveRefundAmount()));
            if (BigDecimalUtil.greaterThanZero(reqDTO.getReserveAmount())) {
                reqDTO.setReserveGuid(reserveRecord.getGuid());
            }
            tradeClientService.updateOrderReserve(reqDTO);
        }
        //预定已锁桌 接触锁定
        if (Boolean.TRUE.equals(reserveRecord.getIsLocked()) && Boolean.FALSE.equals(reserveRecord.getIsDelay())) {
            UnLockTableEvent unLockTableEvent = new UnLockTableEvent(reserveRecord);
            customerPublish.publish(new CustomerEvent<>(unLockTableEvent));
        } else {
            //预定未锁定
            List<String> old = reserveRecord.getTables().stream().map(Table::getGuid).collect(Collectors.toList());
            List<String> neo = Collections.emptyList();
            PreparedLockTableEvent preparedLockTableEvent = new PreparedLockTableEvent(reserveRecord, old, neo);
            customerPublish.publish(new CustomerEvent<>(preparedLockTableEvent));
        }
        // 发送消息到一体机
        if (!Objects.equals(ReserveRecordStateEnum.NO_PAY_CANCEL.getCode(), reserveRecord.getState())) {
            UserContext userContext = UserContextUtils.get();
            cancelAfterExecutor.execute(() -> {
                UserContextUtils.put(userContext);
                EnterpriseIdentifier.setEnterpriseGuid(userContext.getEnterpriseGuid());
                SystemMessageEvent systemMessageEvent = new SystemMessageEvent(reserveRecord);
                customerPublish.publish(new CustomerEvent<>(systemMessageEvent));
            });
        }
    }

    @Override
    protected void pre(CancleEvent baseEvent) {
        ReserveRecord reserveRecord = baseEvent.getReserveRecord();
        boolean result = RedissonLockUtil.tryLock(reserveRecord.getGuid(), 3, 10);
        if (!result) {
            throw new BusinessException("当前预定记录正被其他人操作,请稍候重试!!");
        }
        super.pre(baseEvent);
    }

    @Override
    protected void after(CancleEvent baseEvent) {
        ReserveRecord reserveRecord = baseEvent.getReserveRecord();
        RedissonLockUtil.unlock(reserveRecord.getGuid());
        super.after(baseEvent);
    }
}