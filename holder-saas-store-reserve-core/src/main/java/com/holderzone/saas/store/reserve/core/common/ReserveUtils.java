package com.holderzone.saas.store.reserve.core.common;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReserveUtils
 * @date 2018/07/27 下午5:55
 * @description guid
 * @program holder-saas-store-business-center
 */
@Slf4j
public final class ReserveUtils {
    private static DynamicHelper dynamicHelper;
    private static final String RESERVE = "reserve";
    private static final String RESERVE_TABLE = "reserve_table";
    private static final String RESERVE_ITEM = "reserve_item";
    private static final String RESERVE_CONFIG = "reserve_config";
    private static final String PRIVATE_ROOM = "private_room";
    private static final String RESERVE_PAY_RECORD = "reserve_pay_record";

    private ReserveUtils() {
    }

    /**
     * 生成唯一队列类型guid
     *
     * @return
     */
    public static String reserveGuid() {
        long startTime = System.currentTimeMillis();
        String guid = dynamicHelper().generateGuid(RESERVE);
        long endTime = System.currentTimeMillis();
        if (endTime - startTime > 1000) {
            log.warn("guid生成大于1s");
        }
        return guid;
    }

    public static String reserveTableGuid() {
        return dynamicHelper().generateGuid(RESERVE_TABLE);
    }

    public static String reserveItemGuid() {
        return dynamicHelper().generateGuid(RESERVE_ITEM);
    }

    /**
     * 生成唯一队列类型guid
     *
     * @return
     */
    public static String reservePayGuid() {
        return dynamicHelper().generateGuid(RESERVE_PAY_RECORD);
    }

    /**
     * 生成唯一队列记录guid
     *
     * @return
     */
    public static String reserveConfigGuid() {
        return dynamicHelper().generateGuid(RESERVE_CONFIG);
    }

    public static String privateRoomGuid() {
        return dynamicHelper().generateGuid(PRIVATE_ROOM);
    }

    public static DynamicHelper dynamicHelper() {
        if (dynamicHelper == null) {
            dynamicHelper = SpringContextUtils.getInstance().getBean("dynamicHelper");
        }
        return dynamicHelper;
    }
}
