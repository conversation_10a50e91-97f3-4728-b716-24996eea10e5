package com.holderzone.saas.store.reserve.core.event.impl.handler;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.framework.anno.CustomerRegister;
import com.holderzone.framework.event.observer.CustomerObserver;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.redisson.sdk.lock.RedissonLockUtil;
import com.holderzone.saas.store.reserve.core.dal.ReserveRecordDo;
import com.holderzone.saas.store.reserve.core.dal.ReserveRecordTableRelationDo;
import com.holderzone.saas.store.reserve.core.dal.mapper.ReserveRecordDoMapper;
import com.holderzone.saas.store.reserve.core.dal.mapper.ReserveRecordTableRelationDoMapper;
import com.holderzone.saas.store.reserve.core.domain.ReserveRecord;
import com.holderzone.saas.store.reserve.core.event.BaseEventHandler;
import com.holderzone.saas.store.reserve.core.event.impl.event.UnLockTableEvent;
import com.holderzone.saas.store.reserve.intergration.table.TableClientService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className LockTableEventHandler
 * @date 2019/04/26 16:59
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Component
@CustomerRegister(isRegister = true)
public class UnLockTableEventHandler extends BaseEventHandler<UnLockTableEvent> implements CustomerObserver<UnLockTableEvent> {
    @Autowired
    ReserveRecordDoMapper reserveRecordDoMapper;
    @Autowired
    ReserveRecordTableRelationDoMapper tableRelationDoMapper;
    @Autowired
    TableClientService tableServiceService;
    @Override
    public void execute(UnLockTableEvent baseEvent) {
        ReserveRecord record = baseEvent.getReserveRecord();
        List<String> tableGuids = baseEvent.getTableGuids();
        if(tableGuids == null || tableGuids.isEmpty()){
            List<ReserveRecordTableRelationDo> tableRelationDos =  tableRelationDoMapper.selectList(
                    new LambdaQueryWrapper<ReserveRecordTableRelationDo>()
                            .eq(ReserveRecordTableRelationDo::getReserveRecordGuid,record.getGuid())
                            //state 1 标识 被预定 锁住的桌台
                            .eq(ReserveRecordTableRelationDo::getState,1)
            );
            if(tableRelationDos == null || tableRelationDos.isEmpty()){
                return;
            }
            tableGuids = tableRelationDos.stream().map(ReserveRecordTableRelationDo::getTableGuid).collect(Collectors.toList());
        }
        List<String> failList = tableServiceService.cancleReserveLock(tableGuids);
        tableGuids.removeAll(failList);
        ReserveRecordTableRelationDo reserveRecordTableRelationDo = new ReserveRecordTableRelationDo();
        reserveRecordTableRelationDo.setState(0);
        tableRelationDoMapper.update(reserveRecordTableRelationDo,
                new LambdaQueryWrapper<ReserveRecordTableRelationDo>()
                        .in(ReserveRecordTableRelationDo::getTableGuid,tableGuids)
                        .eq(ReserveRecordTableRelationDo::getReserveRecordGuid,record.getGuid()));

        ReserveRecordDo recordDo = new ReserveRecordDo();
        recordDo.setGuid(record.getGuid());
        recordDo.setIsLocked(false);
        reserveRecordDoMapper.update(recordDo,new LambdaQueryWrapper<ReserveRecordDo>().eq(ReserveRecordDo::getGuid,record.getGuid()));
        record.setIsLocked(false);

    }
    @Override
    protected void pre(UnLockTableEvent baseEvent) {
        ReserveRecord record = baseEvent.getReserveRecord();
        boolean result = RedissonLockUtil.tryLock(record.getGuid(),3,10);
        if(!result){
            throw new BusinessException("当前预定记录正被其他人操作,请稍候重试!!");
        }
        super.pre(baseEvent);
    }

    @Override
    protected void after(UnLockTableEvent baseEvent) {
        ReserveRecord record = baseEvent.getReserveRecord();
        RedissonLockUtil.unlock(record.getGuid());
        super.after(baseEvent);
    }
}