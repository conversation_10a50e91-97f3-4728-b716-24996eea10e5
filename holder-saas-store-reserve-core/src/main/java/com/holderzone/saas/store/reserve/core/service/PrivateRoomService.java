package com.holderzone.saas.store.reserve.core.service;

import com.holderzone.saas.store.reserve.api.dto.ReservePrintDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReserveRecordService
 * @date 2019/04/23 15:29
 * @description //TODO
 * @program holder-saas-store-reserve
 */
public interface PrivateRoomService {

    List<String> query(String storeGuid);

    void save(String storeGuid, List<String> tableGuids);

    void print(ReservePrintDTO reservePrintDTO);
}