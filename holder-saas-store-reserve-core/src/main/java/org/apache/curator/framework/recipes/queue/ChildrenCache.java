package org.apache.curator.framework.recipes.queue;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import java.io.Closeable;
import java.io.IOException;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;

import com.holderzone.framework.util.JacksonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.curator.framework.CuratorFramework;
import org.apache.curator.framework.api.BackgroundCallback;
import org.apache.curator.framework.api.BackgroundPathable;
import org.apache.curator.framework.api.CuratorEvent;
import org.apache.curator.framework.api.CuratorWatcher;
import org.apache.curator.framework.api.Pathable;
import org.apache.curator.utils.PathUtils;
import org.apache.zookeeper.WatchedEvent;
import org.apache.zookeeper.KeeperException.Code;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ChildrenCache
 * @date 2019/06/17 10:51
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Slf4j
class ChildrenCache implements Closeable {
    private final CuratorFramework client;
    private final String path;
    private final AtomicReference<ChildrenCache.Data> children = new AtomicReference(new ChildrenCache.Data(Lists.newArrayList(), 0L));
    private final AtomicBoolean isClosed = new AtomicBoolean(false);
    private final CuratorWatcher watcher = new CuratorWatcher() {
        @Override
        public void process(WatchedEvent event) throws Exception {
            if (!ChildrenCache.this.isClosed.get()) {
                ChildrenCache.this.sync(true);
            }

        }
    };
    private final BackgroundCallback callback = new BackgroundCallback() {
        @Override
        public void processResult(CuratorFramework client, CuratorEvent event) throws Exception {
            if (event.getResultCode() == Code.OK.intValue()) {
                ChildrenCache.this.setNewChildren(event.getChildren());
            }

        }
    };

    ChildrenCache(CuratorFramework client, String path) {
        this.client = client;
        this.path = PathUtils.validatePath(path);
    }

    void start() throws Exception {
        this.sync(true);
    }

    @Override
    public void close() throws IOException {
        this.isClosed.set(true);
        this.notifyFromCallback();
    }

    ChildrenCache.Data getData() {
        return (ChildrenCache.Data)this.children.get();
    }

    ChildrenCache.Data blockingNextGetData(long startVersion) throws InterruptedException {
        return this.blockingNextGetData(startVersion, 0L, (TimeUnit)null);
    }

    synchronized ChildrenCache.Data blockingNextGetData(long startVersion, long maxWait, TimeUnit unit) throws InterruptedException {
        long startMs = System.currentTimeMillis();
        boolean hasMaxWait = unit != null;
        long maxWaitMs = hasMaxWait ? unit.toMillis(maxWait) : -1L;

        while(startVersion == ((ChildrenCache.Data)this.children.get()).version) {
            if (hasMaxWait) {
                long elapsedMs = System.currentTimeMillis() - startMs;
                long thisWaitMs = maxWaitMs - elapsedMs;
                if (thisWaitMs <= 0L) {
                    break;
                }
                log.info("-------ChildrenCache-----Wait---{}", System.currentTimeMillis());
                this.wait(thisWaitMs);
                log.info("-------ChildrenCache-----Notify---{}", System.currentTimeMillis());
            } else {
                log.info("-------ChildrenCache-----Wait---{}", System.currentTimeMillis());
                this.wait();
                log.info("-------ChildrenCache-----Notify---{}", System.currentTimeMillis());
            }
        }

        return (ChildrenCache.Data)this.children.get();
    }

    private synchronized void notifyFromCallback() {
        this.notifyAll();
    }

    private synchronized void sync(boolean watched) throws Exception {
        if (watched) {
            ((Pathable)((BackgroundPathable)this.client.getChildren().usingWatcher(this.watcher)).inBackground(this.callback)).forPath(this.path);
        } else {
            ((Pathable)this.client.getChildren().inBackground(this.callback)).forPath(this.path);
        }

    }

    private synchronized void setNewChildren(List<String> newChildren) {
        if (newChildren != null) {
            log.info("-------ChildrenCache-----Added---{}", JacksonUtils.writeValueAsString(newChildren));
            ChildrenCache.Data currentData = (ChildrenCache.Data)this.children.get();
            this.children.set(new ChildrenCache.Data(newChildren, currentData.version + 1L));
            this.notifyFromCallback();
        }

    }

    static class Data {
        final List<String> children;
        final long version;

        private Data(List<String> children, long version) {
            this.children = ImmutableList.copyOf(children);
            this.version = version;
        }
    }
}
