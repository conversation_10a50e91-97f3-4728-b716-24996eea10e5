package com.holderzone.saas.store.reserve.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalTimeSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TimingSegmentVo
 * @date 2019/04/30 10:05
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Data
public class TimingSegmentDTO implements TimingSegment {

    private static LocalTime _24H = LocalTime.of(0, 0);

    @ApiModelProperty("开始时间:HH:mm")
    @JsonFormat(pattern = "HH:mm")
    @JsonSerialize(using = LocalTimeSerializer.class)
    @JsonDeserialize(using = LocalTimeDeserializer.class)
    private LocalTime start;

    @ApiModelProperty("结束时间:HH:mm")
    @JsonFormat(pattern = "HH:mm")
    @JsonSerialize(using = LocalTimeSerializer.class)
    @JsonDeserialize(using = LocalTimeDeserializer.class)
    private LocalTime end;

    @ApiModelProperty("多少分钟一个时间点")
    private Integer period;

    public TimingSegmentDTO() {
    }

    public TimingSegmentDTO(LocalTime start, LocalTime end) {
        this.start = start;
        this.end = end;
    }

    @JsonIgnore
    public List<TimingSegmentDTO> getSub() {
        LocalDate now = LocalDate.now();
        LocalDateTime start = startOfRef(now);
        LocalDateTime end = endOfRef(now);
        if (end.toLocalTime().equals(_24H)) {
            end = end.minusSeconds(1);
        }
        List<TimingSegmentDTO> subList = new ArrayList<>();
        LocalDateTime subStart = start;
        while (!subStart.isAfter(end)) {
            LocalDateTime subEnd = subStart.plus(period(), ChronoUnit.MINUTES);
            subList.add(new TimingSegmentDTO(subStart.toLocalTime(), subEnd.toLocalTime()));
            subStart = subEnd;
        }
        return subList;
    }
}