package com.holderzone.saas.store.reserve.api;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.reserve.ReserveRecordGuidDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxPayRespDTO;
import com.holderzone.saas.store.reserve.api.dto.*;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;


/**
 * 预订
 * 小程序端
 */
@Api("预定-小程序端")
@FeignClient(value = "holder-saas-store-reserve", fallbackFactory = ReserveAppletApi.ReserveWechatControllerFallBack.class)
public interface ReserveAppletApi {

    @ApiOperation("查询可预订的门店列表")
    @PostMapping("/reserve/applet/available_store_list")
    List<ReserveAvailableStoreDTO> getAvailableStoreList(@RequestBody ReserveAppletQueryDTO queryDTO);

    @ApiOperation("查询可预订的门店详情")
    @PostMapping("/reserve/applet/available_store")
    ReserveAvailableStoreConfigDTO getAvailableStore(@RequestBody ReserveAppletQueryDTO queryDTO);

    @ApiOperation("发起预定")
    @PostMapping("/reserve/applet/launch")
    String launch(@Valid @RequestBody ReserveRecordDTO reserveRecordDTO);

    @ApiOperation("预订单列表")
    @PostMapping("/reserve/applet/record/list")
    Page<ReserveRecordAppletPageDTO> obtainRecordList(@RequestBody ReserveAppletQueryDTO queryDTO);

    @ApiOperation("预订单详情")
    @GetMapping("/reserve/applet/record/detail")
    ReserveRecordAppletDetailDTO obtainRecordDetail(@RequestParam("recordGuid") String recordGuid);

    @ApiOperation("查询同时间的预定单")
    @PostMapping("/reserve/applet/query_same_time_record")
    ReserveRecordAppletDetailDTO querySameTimeRecord(@RequestBody ReserveAppletQueryDTO queryDTO);

    @ApiOperation("查询最近一笔待预定的预定单")
    @PostMapping("/reserve/applet/query_last_time_record")
    ReserveRecordAppletPageDTO queryLastTimeRecord(@RequestBody ReserveAppletQueryDTO queryDTO);

    @ApiOperation("取消订单")
    @PostMapping("/reserve/applet/cancel")
    void cancel(@RequestBody ReserveRecordGuidDTO reserveRecordGuidDTO);

    @ApiOperation("小程序支付-会员支付")
    @PostMapping("/reserve/applet/member/pay")
    ReserveRecordDetailDTO memberPay(@RequestBody ReserveAppletPayDTO reserveAppletPayDTO);

    @ApiOperation("小程序支付-微信支付")
    @PostMapping("/reserve/applet/wechat/pay")
    WxPayRespDTO wechatPay(@RequestBody ReserveAppletPayDTO reserveAppletPayDTO);

    @Slf4j
    @Component
    class ReserveWechatControllerFallBack implements FallbackFactory<ReserveAppletApi> {

        @Override
        public ReserveAppletApi create(Throwable throwable) {
            return new ReserveAppletApi() {
                @Override
                public List<ReserveAvailableStoreDTO> getAvailableStoreList(ReserveAppletQueryDTO queryDTO) {
                    log.error("reserve get available store list fail with param: {}", JacksonUtils.writeValueAsString(queryDTO));
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ReserveAvailableStoreConfigDTO getAvailableStore(ReserveAppletQueryDTO queryDTO) {
                    log.error("reserve get available store fail with param: {}", JacksonUtils.writeValueAsString(queryDTO));
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public String launch(ReserveRecordDTO reserveRecordDTO) {
                    log.error("reserve launch fail with param: {}", JacksonUtils.writeValueAsString(reserveRecordDTO));
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public Page<ReserveRecordAppletPageDTO> obtainRecordList(ReserveAppletQueryDTO queryDTO) {
                    log.error("reserve obtain record page fail with param: {}", JacksonUtils.writeValueAsString(queryDTO));
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ReserveRecordAppletDetailDTO obtainRecordDetail(String recordGuid) {
                    log.error("reserve obtain record detail fail with param: {}", recordGuid);
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ReserveRecordAppletDetailDTO querySameTimeRecord(ReserveAppletQueryDTO queryDTO) {
                    log.error("reserve query same time record fail with param: {}", JacksonUtils.writeValueAsString(queryDTO));
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ReserveRecordAppletPageDTO queryLastTimeRecord(ReserveAppletQueryDTO queryDTO) {
                    log.error("reserve query last time record fail with param: {}", JacksonUtils.writeValueAsString(queryDTO));
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public void cancel(ReserveRecordGuidDTO reserveRecordGuidDTO) {
                    log.error("reserve cancel fail with param: {}", JacksonUtils.writeValueAsString(reserveRecordGuidDTO));
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ReserveRecordDetailDTO memberPay(ReserveAppletPayDTO reserveAppletPayDTO) {
                    log.error("reserve member pay fail with param: {}", JacksonUtils.writeValueAsString(reserveAppletPayDTO));
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public WxPayRespDTO wechatPay(ReserveAppletPayDTO reserveAppletPayDTO) {
                    log.error("reserve wechat pay fail with param: {}", JacksonUtils.writeValueAsString(reserveAppletPayDTO));
                    throw new BusinessException(throwable.getMessage());
                }

            };
        }
    }
}