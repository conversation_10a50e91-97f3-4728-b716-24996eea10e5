package com.holderzone.saas.store.reserve.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.reserve.TableDTO;
import com.holderzone.saas.store.enums.PaymentTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collection;

/**
 * <AUTHOR>
 * @version 1.0
 * @className HWReserveRecordDTO
 * @date 2019/04/26 18:29
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Data
@ApiModel
public class ReserveRecordDTO extends BaseDTO {

    @ApiModelProperty("业务主键")
    private String guid;

    @NotEmpty
    @ApiModelProperty(value = "门店guid", required = true)
    private String storeGuid;

    @NotNull
    @Max(value = 999, message = "请输入正确人数")
    @Min(value = 1, message = "请输入正确人数")
    @ApiModelProperty(value = "预定人数", required = true)
    private Integer number;

    private String state;

    @ApiModelProperty(value = "预订人姓名", required = true)
    private String name;

    @ApiModelProperty(value = "预订人手机号", required = true)
    @NotEmpty
    private String phone;

    @ApiModelProperty("桌台信息")
    private Collection<TableDTO> tables;

    @ApiModelProperty("菜品信息")
    private Collection<DineInItemDTO> items;

    /**
     * 预定区域
     */
    private AreaDTO area;

    /**
     * {@link PaymentTypeEnum}
     */
    @ApiModelProperty("预定金支付方式,1-现金，2-聚合支付，2银行卡支付，10，其他支付方式")
    private Integer paymentType;

    @ApiModelProperty("预定金支付方式名称")
    private String paymentTypeName;

    @ApiModelProperty("预定金支付时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime paymentTime;

    @ApiModelProperty("预定金金额")
    private BigDecimal reserveAmount;

    /**
     * 回退金额
     */
    @ApiModelProperty("预定金退款金额")
    private BigDecimal reserveRefundAmount;

    @ApiModelProperty("可取消订单时间 单位小时")
    private Integer cancelableTime;

    /**
     * 0. 女
     */
    @NotNull
    @ApiModelProperty("0. 女,1. 男")
    private Byte gender;

    @ApiModelProperty("预定备注")
    private String remark;

    @ApiModelProperty("预定开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime reserveStartTime;

    @ApiModelProperty("预定结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime reservesEndTime;

    @ApiModelProperty("下单人id")
    private String createUserId;

    @ApiModelProperty("下单人手机号")
    private String createUserPhone;

    @ApiModelProperty("下单人名称")
    private String createUserName;

    @ApiModelProperty("小程序分享地址")
    private String shareUrl;

    /**
     * 订单类型 0预订 1预付金
     */
    @ApiModelProperty("订单类型 0预订 1预付金")
    private Integer orderType = 0;

    @ApiModelProperty("订单guid")
    private String orderGuid;

    @ApiModelProperty("确认人id")
    private String confirmUserGuid;

    @ApiModelProperty("确认人")
    private String confirmUserName;

}