package com.holderzone.saas.store.reserve.api.dto;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PhoneDTO
 * @date 2019/04/29 17:35
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PhoneDTO extends BaseDTO {

    @ApiModelProperty("手机号")
    private String phone;
}