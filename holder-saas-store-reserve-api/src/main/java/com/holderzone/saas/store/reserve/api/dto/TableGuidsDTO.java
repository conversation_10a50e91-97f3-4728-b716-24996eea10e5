package com.holderzone.saas.store.reserve.api.dto;

import com.holderzone.saas.store.dto.common.BaseDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TableGuidDTO
 * @date 2019/06/11 14:24
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TableGuidsDTO extends BaseDTO {

     private List<String> tableGuids;
}