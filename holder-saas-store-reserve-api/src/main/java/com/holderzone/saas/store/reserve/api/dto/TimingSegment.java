package com.holderzone.saas.store.reserve.api.dto;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TimingSegment
 * @date 2019/05/06 15:27
 * @description //TODO
 * @program holder-saas-store-reserve
 */
public interface TimingSegment {

    LocalTime getStart();

    void setStart(LocalTime time);

    LocalTime getEnd();

    void setEnd(LocalTime time);

    default boolean isReversed() {
        return !this.getEnd().isAfter(this.getStart());
    }

    default LocalDateTime startOfRef(LocalDate localDate) {
        return LocalDateTime.of(localDate, getStart());
    }

    default LocalDateTime endOfRef(LocalDate localDate) {
        return isReversed() ? LocalDateTime.of(localDate, getEnd()).plusDays(1) : LocalDateTime.of(localDate, getEnd());
    }

    default Integer getPeriod() {
        return 30;
    }

    default Integer period() {
        Integer period = getPeriod();
        if (period == null) {
            return 30;
        }
        return period;
    }
}