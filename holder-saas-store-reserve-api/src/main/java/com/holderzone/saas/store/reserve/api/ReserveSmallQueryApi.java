package com.holderzone.saas.store.reserve.api;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.reserve.TableDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/28
 * @description 小众特殊查询api
 */
@Api("小众特殊查询api")
@FeignClient(value = "holder-saas-store-reserve", fallbackFactory = ReserveSmallQueryApi.ReserveSmallQueryControllerFallback.class)
public interface ReserveSmallQueryApi {

    @ApiOperation("查询订单当前实际预定金额")
    @PostMapping("/reserveSmall/queryReserveAmount")
    BigDecimal queryReserveAmount(@RequestBody SingleDataDTO query);

    @ApiOperation("查询订单所属桌台")
    @PostMapping("/reserveSmall/queryReserveTable")
    List<TableDTO> queryReserveTable(@RequestBody SingleDataDTO query);

    @Slf4j
    @Component
    class ReserveSmallQueryControllerFallback implements FallbackFactory<ReserveSmallQueryApi> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public ReserveSmallQueryApi create(Throwable cause) {
            return new ReserveSmallQueryApi() {

                @Override
                public BigDecimal queryReserveAmount(SingleDataDTO query) {
                    log.error(HYSTRIX_PATTERN, "queryReserveAmount", JacksonUtils.writeValueAsString(query),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public List<TableDTO> queryReserveTable(SingleDataDTO query) {
                    log.error(HYSTRIX_PATTERN, "queryReserveTable", JacksonUtils.writeValueAsString(query),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

            };
        }
    }
}
