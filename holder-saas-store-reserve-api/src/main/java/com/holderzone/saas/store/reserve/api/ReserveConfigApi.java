package com.holderzone.saas.store.reserve.api;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.reserve.api.dto.ReserveConfigDTO;
import com.holderzone.saas.store.reserve.api.dto.ReserveConfigSyncDTO;
import com.holderzone.saas.store.reserve.api.dto.StoreGuidDTO;
import com.holderzone.saas.store.reserve.api.dto.StoreRoomDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReserveRecordApi
 * @date 2019/04/26 18:24
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Api("预定-门店配置")
@FeignClient(value = "holder-saas-store-reserve", fallbackFactory = ReserveConfigApi.ReserveConfigControllerFallBack.class)
public interface ReserveConfigApi {

    @ApiOperation("保存配置")
    @PostMapping("/reserve/config/insert")
    ReserveConfigDTO insert(@RequestBody ReserveConfigDTO dto);

    @ApiOperation("修改配置")
    @PostMapping("/reserve/config/update")
    ReserveConfigDTO update(@RequestBody ReserveConfigDTO dto);

    @ApiOperation("查询配置")
    @PostMapping("/reserve/config")
    ReserveConfigDTO query(@RequestBody StoreGuidDTO storeGuid);

    @ApiOperation("查询包间")
    @PostMapping("/reserve/room/query")
    List<String> queryRoom(@RequestBody StoreGuidDTO storeGuid);

    @ApiOperation("保存包间")
    @PostMapping("/reserve/room/save")
    void saveRoom(@RequestBody StoreRoomDTO storeRoom);

    @ApiOperation("同步门店配置")
    @PostMapping("/reserve/config/sync")
    void syncConfig(@RequestBody ReserveConfigSyncDTO reserveConfigSyncDTO);

    @Slf4j
    @Component
    class ReserveConfigControllerFallBack implements FallbackFactory<ReserveConfigApi> {

        @Override
        public ReserveConfigApi create(Throwable throwable) {
            return new ReserveConfigApi() {
                @Override
                public ReserveConfigDTO insert(ReserveConfigDTO dto) {
                    log.error("reserve config insert fail with param: {}", dto);
                    throw new RuntimeException(throwable);
                }

                @Override
                public ReserveConfigDTO update(ReserveConfigDTO dto) {
                    log.error("reserve config update fail with param: {}", dto);
                    throw new RuntimeException(throwable);
                }

                @Override
                public ReserveConfigDTO query(StoreGuidDTO storeGuid) {
                    log.error("reserve config query fail with param: {}", storeGuid);
                    throw new RuntimeException(throwable);
                }

                @Override
                public List<String> queryRoom(StoreGuidDTO storeGuid) {
                    log.error("reserve room query fail with param: {}", storeGuid);
                    throw new RuntimeException(throwable);
                }

                @Override
                public void saveRoom(StoreRoomDTO storeRoom) {
                    log.error("reserve room save fail with param: {}", storeRoom);
                    throw new RuntimeException(throwable);
                }

                @Override
                public void syncConfig(ReserveConfigSyncDTO reserveConfigSyncDTO) {
                    log.error("reserve config sync fail with param: {}", reserveConfigSyncDTO);
                    throw new BusinessException(throwable.getMessage());
                }
            };
        }
    }
}