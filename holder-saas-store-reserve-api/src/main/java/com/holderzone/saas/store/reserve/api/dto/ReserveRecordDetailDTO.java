package com.holderzone.saas.store.reserve.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.holderzone.saas.store.dto.reserve.StatisticsDTO;
import com.holderzone.saas.store.dto.zhuancan.ZhuanCanReserveMerchantDTO;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReserveRecordDetailDTO
 * @date 2019/05/28 16:57
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Data
public class ReserveRecordDetailDTO extends ReserveRecordDTO {

    private String remark;

    /**
     * 订单编号
     */
    private String orderNo;

    private String confirmUserGuid;

    private String confirmUserName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime confirmTime;

    private String arriveUserGuid;

    private String arriveUserName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime arriveTime;

    private Integer paymentType;
    private String paymentTypeName;
    private LocalDateTime paymentTime;
    private BigDecimal reserveAmount;



    private String cancelUserGuid;

    private String cancelUserName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime cancleTime;

    private String cancleReason;

    /**
     * 最大可退款时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime maxCancelableTime;

    /**
     * 取消角色
     */
    private String cancelRole;

    /**
     * 创建人guid
     */
    private String createStaffGuid;

    private String createStaffName;

    /**
     * 更新人guid
     */
    private String modifiedStaffGuid;

    private String modifiedStaffName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtModified;

    private StatisticsDTO statistics;

    private String orderGuid;

    private String tableGuid;

    /**
     * 关联赚餐门店信息
     */
    private ZhuanCanReserveMerchantDTO zhuanCanMerchantDTO;
}