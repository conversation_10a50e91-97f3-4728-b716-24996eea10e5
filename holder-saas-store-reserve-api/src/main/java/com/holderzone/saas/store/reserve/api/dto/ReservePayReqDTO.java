package com.holderzone.saas.store.reserve.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReservePayDTO
 * @date 2019/12/06 17:02
 * @description //TODO
 * @program IdeaProjects
 */
@ApiModel(description = "支付DTO")
@Data
public class ReservePayReqDTO extends ReserveRecordDTO {

    private static final long serialVersionUID = -7032610169896840116L;

    @ApiModelProperty(value = "发起条码支付的授权码，当微信、支付宝条码支付时候，必填！！")
    private String authCode;

    /**
     * 会员余额资金变动明细guid
     */
    private String memberFundingDetailGuid;

    @ApiModelProperty(value = "主单guid")
    private String mainOrderGuid;

}