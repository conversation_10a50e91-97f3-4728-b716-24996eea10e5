package com.holderzone.saas.store.reserve.api.dto;

import com.holderzone.saas.store.dto.reserve.ReserveRecordGuidDTO;
import com.holderzone.saas.store.dto.reserve.TableDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className CompensateDTO
 * @date 2019/05/30 19:06
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CompensateDTO extends ReserveRecordGuidDTO {

    private List<TableDTO> tableDTOS;
}