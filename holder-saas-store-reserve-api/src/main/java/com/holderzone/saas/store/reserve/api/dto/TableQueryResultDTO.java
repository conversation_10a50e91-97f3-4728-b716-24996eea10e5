package com.holderzone.saas.store.reserve.api.dto;

import com.holderzone.saas.store.dto.reserve.TableDTO;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TableQueryResultDTO
 * @date 2019/05/06 17:22
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Setter
@Getter
public class TableQueryResultDTO extends TableDTO {

    private String status;

    private Integer seats;

    private List<String> tag;
}