package com.holderzone.saas.store.reserve.api.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 交接班时查询预订金支付方式统计对象
 */
@Data
public class ReserveAmountPaymentDTO {

    /**
     * 支付方式
     */
    private Integer paymentType;

    /**
     * 支付方式名称
     */
    private String paymentTypeName;

    /**
     * 该支付方式的预订单数
     */
    private Integer reserveCount;

    /**
     * 该支付方式的预订金额合计
     */
    private BigDecimal reserveAmount;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

}
