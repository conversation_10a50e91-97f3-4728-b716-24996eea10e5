package com.holderzone.saas.store.reserve.api.dto;

import com.holderzone.saas.store.dto.table.TableOrderDTO;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TableOrderReserveDTO
 * @date 2019/06/11 14:43
 * @description //坑！！！！  主要set get方法
 * @program holder-saas-store-reserve
 */
@AllArgsConstructor
@NoArgsConstructor
public class TableOrderReserveDTO extends TableOrderDTO {

    private ReserveRecordLessDTO record;

    private transient TableOrderDTO tableOrderDTO;

    public ReserveRecordLessDTO getRecord() {
        return record;
    }

    public void setRecord(ReserveRecordLessDTO record) {
        this.record = record;
    }

    @Override
    public String getTableGuid() {
        return tableOrderDTO.getTableGuid();
    }

    @Override
    public String getAreaGuid() {
        return tableOrderDTO.getAreaGuid();
    }

    @Override
    public String getAreaName() {
        return tableOrderDTO.getAreaName();
    }

    @Override
    public BigDecimal getOrderAmount() {
        return tableOrderDTO.getOrderAmount();
    }

    @Override
    public String getTableCode() {
        return tableOrderDTO.getTableCode();
    }

    @Override
    public String getMainOrderGuid() {
        return tableOrderDTO.getMainOrderGuid();
    }

    @Override
    public String getOrderGuid() {
        return tableOrderDTO.getOrderGuid();
    }

    @Override
    public Integer getSeats() {
        return tableOrderDTO.getSeats();
    }

    @Override
    public Integer getActualGuestsNo() {
        return tableOrderDTO.getActualGuestsNo();
    }

    @Override
    public Integer getStatus() {
        return tableOrderDTO.getStatus();
    }

    @Override
    public Set<Integer> getSubStatus() {
        return tableOrderDTO.getSubStatus();
    }

    @Override
    public LocalDateTime getOpenTableTime() {
        return tableOrderDTO.getOpenTableTime();
    }

    @Override
    public LocalDateTime getCurrentTime() {
        return tableOrderDTO.getCurrentTime();
    }

    @Override
    public Integer getCombineTimes() {
        return tableOrderDTO.getCombineTimes();
    }

    @Override
    public Integer getAssociatedTimes() {
        return tableOrderDTO.getAssociatedTimes();
    }

    @Override
    public Integer getPrintPreBillNum() {
        return tableOrderDTO.getPrintPreBillNum();
    }

    @Override
    public String getLockDeviceId() {
        return tableOrderDTO.getLockDeviceId();
    }

    @Override
    public void setTableGuid(String tableGuid) {
        tableOrderDTO.setTableGuid(tableGuid);
    }

    @Override
    public void setAreaName(String areaName) {
        tableOrderDTO.setAreaName(areaName);
    }

    @Override
    public void setAreaGuid(String areaGuid) {
        tableOrderDTO.setAreaGuid(areaGuid);
    }

    @Override
    public void setOrderAmount(BigDecimal orderAmount) {
        tableOrderDTO.setOrderAmount(orderAmount);
    }

    @Override
    public void setTableCode(String tableCode) {
        tableOrderDTO.setTableCode(tableCode);
    }

    @Override
    public void setMainOrderGuid(String mainOrderGuid) {
        tableOrderDTO.setMainOrderGuid(mainOrderGuid);
    }

    @Override
    public void setOrderGuid(String orderGuid) {
        tableOrderDTO.setOrderGuid(orderGuid);
    }

    @Override
    public void setSeats(Integer seats) {
        tableOrderDTO.setSeats(seats);
    }

    @Override
    public void setActualGuestsNo(Integer actualGuestsNo) {
        tableOrderDTO.setActualGuestsNo(actualGuestsNo);
    }

    @Override
    public void setStatus(Integer status) {
        tableOrderDTO.setStatus(status);
    }

    @Override
    public void setSort(Integer sort){
        tableOrderDTO.setSort(sort);
    };

    @Override
    public Integer getSort(){
        return tableOrderDTO.getSort();
    };

    @Override
    public void setSubStatus(Set<Integer> subStatus) {
        tableOrderDTO.setSubStatus(subStatus);
    }

    @Override
    public void setOpenTableTime(LocalDateTime openTableTime) {
        tableOrderDTO.setOpenTableTime(openTableTime);
    }

    @Override
    public void setCurrentTime(LocalDateTime currentTime) {
        tableOrderDTO.setCurrentTime(currentTime);
    }

    @Override
    public void setCombineTimes(Integer combineTimes) {
        tableOrderDTO.setCombineTimes(combineTimes);
    }

    @Override
    public void setAssociatedTimes(Integer associatedTimes) {
        tableOrderDTO.setAssociatedTimes(associatedTimes);
    }

    @Override
    public void setPrintPreBillNum(Integer printPreBillNum) {
        tableOrderDTO.setPrintPreBillNum(printPreBillNum);
    }

    @Override
    public void setLockDeviceId(String lockDeviceId) {
        tableOrderDTO.setLockDeviceId(lockDeviceId);
    }

    @Override
    public Integer getDeviceType() {
        return tableOrderDTO.getDeviceType();
    }

    @Override
    public String getDeviceId() {
        return tableOrderDTO.getDeviceId();
    }

    @Override
    public String getEnterpriseGuid() {
        return tableOrderDTO.getEnterpriseGuid();
    }

    @Override
    public String getEnterpriseName() {
        return tableOrderDTO.getEnterpriseName();
    }

    @Override
    public String getStoreGuid() {
        return tableOrderDTO.getStoreGuid();
    }

    @Override
    public String getStoreName() {
        return tableOrderDTO.getStoreName();
    }

    @Override
    public String getUserGuid() {
        return tableOrderDTO.getUserGuid();
    }

    @Override
    public String getUserName() {
        return tableOrderDTO.getUserName();
    }

    @Override
    public String getAccount() {
        return tableOrderDTO.getAccount();
    }

    @Override
    public void setDeviceType(Integer deviceType) {
        tableOrderDTO.setDeviceType(deviceType);
    }

    @Override
    public void setDeviceId(String deviceId) {
        tableOrderDTO.setDeviceId(deviceId);
    }

    @Override
    public void setEnterpriseGuid(String enterpriseGuid) {
        tableOrderDTO.setEnterpriseGuid(enterpriseGuid);
    }

    @Override
    public void setEnterpriseName(String enterpriseName) {
        tableOrderDTO.setEnterpriseName(enterpriseName);
    }

    @Override
    public void setStoreGuid(String storeGuid) {
        tableOrderDTO.setStoreGuid(storeGuid);
    }

    @Override
    public void setStoreName(String storeName) {
        tableOrderDTO.setStoreName(storeName);
    }

    @Override
    public void setUserGuid(String userGuid) {
        tableOrderDTO.setUserGuid(userGuid);
    }

    @Override
    public void setUserName(String userName) {
        tableOrderDTO.setUserName(userName);
    }

    @Override
    public void setAccount(String account) {
        tableOrderDTO.setAccount(account);
    }

    @Override
    public String getMemberPhone() {
        return tableOrderDTO.getMemberPhone();
    }

    @Override
    public void setMemberPhone(String memberPhone) {
        tableOrderDTO.setMemberPhone(memberPhone);
    }

    @Override
    public String getMemberGuid() {
        return tableOrderDTO.getMemberGuid();
    }

    @Override
    public void setMemberGuid(String memberGuid) {
        tableOrderDTO.setMemberGuid(memberGuid);
    }

}