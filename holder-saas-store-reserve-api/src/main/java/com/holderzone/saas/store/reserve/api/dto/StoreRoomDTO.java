package com.holderzone.saas.store.reserve.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.lang.Nullable;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Null;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className StoreGuidDTO
 * @date 2019/05/13 10:26
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Data
@ApiModel
public class StoreRoomDTO extends StoreGuidDTO {

    @Nullable
    @ApiModelProperty("tableGuids")
    private List<String> tableGuids;
}