package com.holderzone.saas.store.reserve.api.enums;

/**
 * <AUTHOR>
 * @date 2024/9/4
 * @description 订单类型
 */
public enum OrderTypeEnum {

    RESERVE(0, "预订"),

    RESERVE_PAY(1, "预付金"),
    ;

    private int code;
    private String desc;

    OrderTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(int code) {
        for (OrderTypeEnum c : OrderTypeEnum.values()) {
            if (c.getCode() == code) {
                return c.desc;
            }
        }
        return null;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
