package com.holderzone.saas.store.reserve.api;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.pay.SaasNotifyDTO;
import com.holderzone.saas.store.dto.reserve.ReservePayDTO;
import com.holderzone.saas.store.dto.reserve.ReservePayStatisticReqDTO;
import com.holderzone.saas.store.reserve.api.dto.ReservePayReqDTO;
import com.holderzone.saas.store.reserve.api.dto.ReservePayRespDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReservePaymentController
 * @date 2019/12/06 16:23
 * @description //TODO
 * @program IdeaProjects
 */
@Api("预定-预定金支付")
@FeignClient(value = "holder-saas-store-reserve", fallbackFactory = ReservePaymentApi.ReservePaymentControllerFallback.class)
public interface ReservePaymentApi {

    @ApiOperation("支付")
    @PostMapping("/pay")
    ReservePayRespDTO pay(@RequestBody ReservePayReqDTO reservePayReqDTO);

    @ApiOperation("聚合支付回调")
    @PostMapping("/callback")
    String callback(@RequestBody SaasNotifyDTO saasNotifyDTO);

    @ApiOperation("退款")
    @PostMapping("/refund")
    String refund(@RequestBody SingleDataDTO singleDataDTO);

    @ApiOperation("部分退款")
    @PostMapping("/part_refund")
    BigDecimal partRefund(@RequestBody ReservePayReqDTO reservePayReqDTO);

    @ApiOperation("查詢統計")
    @PostMapping("/queryStatictis")
    List<ReservePayDTO> getPayStatics(@RequestBody ReservePayStatisticReqDTO reservePayStatisticReqDTO);

    @Slf4j
    @Component
    class ReservePaymentControllerFallback implements FallbackFactory<ReservePaymentApi> {

        @Override
        public ReservePaymentApi create(Throwable throwable) {
            return new ReservePaymentApi() {

                @Override
                public ReservePayRespDTO pay(ReservePayReqDTO reservePayReqDTO) {
                    log.error("reserve pay fail with param: {}", reservePayReqDTO);
                    throw new RuntimeException(throwable);
                }

                @Override
                public String callback(SaasNotifyDTO saasNotifyDTO) {
                    log.error("reserve callback fail with param: {}", saasNotifyDTO);
                    throw new RuntimeException(throwable);
                }

                @Override
                public String refund(SingleDataDTO singleDataDTO) {
                    log.error("reserve refund fail with param: {}", singleDataDTO);
                    throw new RuntimeException(throwable);
                }

                @Override
                public BigDecimal partRefund(ReservePayReqDTO reservePayReqDTO) {
                    log.error("reserve part refund fail with param: {}", reservePayReqDTO);
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public List<ReservePayDTO> getPayStatics(ReservePayStatisticReqDTO reservePayStatisticReqDTO) {
                    log.error("reserve statistic fail with param: {}", reservePayStatisticReqDTO);
                    throw new RuntimeException(throwable);
                }
            };
        }
    }
}