package com.holderzone.saas.store.reserve.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;


/**
 * 预订
 * 小程序 支付
 */
@Data
public class ReserveAppletPayDTO implements Serializable {

    private static final long serialVersionUID = -5114921693627974439L;

    @ApiModelProperty("预定单guid")
    private String recordGuid;

    @ApiModelProperty("会员guid")
    private String memberInfoGuid;

    @ApiModelProperty("会员主卡持卡guid")
    private String operationMemberInfoCardGuid;

    @ApiModelProperty("预定金")
    private BigDecimal reserveAmount;

    @ApiModelProperty(value = "会员密码")
    private String memberPassword;

    /**
     * @see com.holderzone.saas.store.enums.weixin.WxAppletMemberPayTypeEnum
     */
    @ApiModelProperty(value = "支付方式 6:储值金额 4:收益余额")
    private Integer payType;

    @ApiModelProperty(value = "小程序openId")
    private String openId;

    @ApiModelProperty(value = "小程序会员名称")
    private String memberName;

    @ApiModelProperty(value = "小程序appId(小程序支付需要appId)")
    private String appId;

    @ApiModelProperty(value = "客户端ip(农行渠道需要参数)(后端获取)")
    private String clientIp;
}