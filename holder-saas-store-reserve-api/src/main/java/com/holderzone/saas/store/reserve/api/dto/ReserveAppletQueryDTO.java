package com.holderzone.saas.store.reserve.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.holderzone.saas.store.dto.common.PageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;


/**
 * 预订
 * 小程序 查询
 */
@Data
public class ReserveAppletQueryDTO extends PageDTO {

    private static final long serialVersionUID = -5114921693627974439L;

    /**
     * 门店列表
     */
    private List<String> storeGuids;

    /**
     * 预订时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate reserveDate;

    /**
     * 小程序查询状态
     */
    private String appletState;

    /**
     * 查询状态
     */
    private List<Integer> states;

    /**
     * 小程序预定下单人id
     */
    private String createUserId;

    /**
     * 预定人手机号
     */
    private String phone;

    /**
     * 是否逾期
     */
    private Boolean isDelay;

    /**
     * 预订时间 - 时段
     */
    @ApiModelProperty("预定开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime reserveStartTime;
}