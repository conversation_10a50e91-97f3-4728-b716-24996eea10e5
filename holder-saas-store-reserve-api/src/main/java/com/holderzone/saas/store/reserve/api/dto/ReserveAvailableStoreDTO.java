package com.holderzone.saas.store.reserve.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


/**
 * 预订
 * 可预订门店列表信息
 */
@Data
public class ReserveAvailableStoreDTO implements Cloneable {

    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    @ApiModelProperty(value = "可预订区域")
    private List<AreaDTO> reserveAreaNames;

    @Override
    public Object clone() throws CloneNotSupportedException {
        return super.clone();
    }
}