package com.holderzone.saas.store.reserve.api;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.business.manage.HandoverPayQueryDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.request.daily.DailyReqDTO;
import com.holderzone.saas.store.dto.order.response.daily.GatherRespDTO;
import com.holderzone.saas.store.dto.reserve.*;
import com.holderzone.saas.store.dto.table.TableOrderCombineDTO;
import com.holderzone.saas.store.dto.table.trade.TradeTableDTO;
import com.holderzone.saas.store.reserve.api.dto.*;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReserveRecordApi
 * @date 2019/04/26 18:24
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Api("预定")
@FeignClient(value = "holder-saas-store-reserve", fallbackFactory = ReserveRecordApi.ReserveRecordControllerFallback.class)
public interface ReserveRecordApi {

    @ApiOperation("查詢菜品列表")
    @PostMapping("/reserve/getItems")
    List<DineInItemDTO> getItems(@RequestBody ReserveRecordGuidDTO guidDTO);

    @ApiOperation("到店开台")
    @PostMapping("/reserve/query")
    Collection<ReserveRecordLessDTO> query(@RequestBody ReserveRecordQueryDTO queryDTO);

    @ApiOperation("统计信息")
    @PostMapping("/reserve/statistics")
    StatisticsDTO statistics(@RequestBody PhoneDTO queryDTO);

    @ApiOperation("发起预定")
    @PostMapping("/reserve/launch")
    ReserveRecordDetailDTO launch(@Valid @RequestBody ReserveRecordDTO reserveRecordDTO);

    @ApiOperation("修改预定")
    @PostMapping("/reserve/modify")
    ReserveRecordDetailDTO modify(@Valid @RequestBody ReserveRecordDTO reserveRecordDTO);

    @ApiOperation("审核通过")
    @PostMapping("/reserve/pass")
    ReserveRecordDetailDTO pass(@RequestBody ReserveRecordGuidDTO guidDTO);

    @ApiOperation("取消预定")
    @PostMapping("/reserve/cancle")
    ReserveRecordDetailDTO cancle(@RequestBody ReserveRecordGuidDTO guidDTO);

    @ApiOperation("到店开台")
    @PostMapping("/reserve/open")
    ReserveRecordDetailDTO open(@RequestBody ReserveRecordGuidDTO guidDTO);

    @ApiOperation("到店选台")
    @PostMapping("/reserve/compensate")
    ReserveRecordDetailDTO compensate(@RequestBody CompensateDTO guidDTO);

    @ApiOperation("查询指定预订记录详细信息")
    @PostMapping("/reserve/obtain")
    ReserveRecordDetailDTO obtain(@RequestBody ReserveRecordGuidDTO guidDTO);

    @ApiOperation("查询指定预订记录详细信息(包含小程序字段信息)")
    @GetMapping("/reserve/obtain/applet")
    ReserveRecordDetailDTO obtainApplet(@RequestParam("guid") String guid);

    @ApiOperation("查询指定预订记录来源")
    @PostMapping("/reserve/obtainDeviceType")
    Integer obtainDeviceType(@RequestBody ReserveRecordGuidDTO guidDTO);

    @ApiOperation("查询预订菜品统计")
    @PostMapping("/reserve/itemStatistics")
    ReserveReportTotalDataDTO itemCount(@RequestBody ReserveReportParamDTO paramDTO);

    @ApiOperation("查询交接班预订金统计")
    @PostMapping("/reserve/handover")
    ReserveHandoverDTO handover(@RequestBody HandoverPayQueryDTO handoverPayQueryDTO);

    @ApiOperation("/预订收入统计")
    @PostMapping("/reserve/gather")
    List<GatherRespDTO> gather(@RequestBody DailyReqDTO dailyReqDTO);

    @ApiOperation("根据订单查询预付金信息")
    @PostMapping("/reserve/queryByOrderGuid")
    ReserveRecordDTO queryByOrderGuid(@RequestBody SingleDataDTO query);

    @ApiOperation("根据guid查询预付金信息")
    @PostMapping("/reserve/queryByGuid")
    ReserveRecordDTO queryByGuid(@RequestBody SingleDataDTO query);

    @ApiOperation("预定拆台")
    @PostMapping("/reserve/separate")
    void separate(@RequestBody TableOrderCombineDTO tableOrderCombineDTO);

    @ApiOperation("通知转台")
    @PostMapping("/reserve/notifyTurn")
    void notifyTurn(@RequestBody TradeTableDTO tradeTableDTO);

    @ApiOperation("支付成功通知")
    @PostMapping("/reserve/notifyPay")
    void notifyPay(NotifyPayReqDTO reqDTO);

    @ApiOperation("预订并台")
    @PostMapping("/reserve/combine")
    void combine(@RequestBody TableOrderCombineDTO tableOrderCombineDTO);

    @ApiOperation("预付金反结账")
    @PostMapping("/reserve/recovery")
    void recovery(@RequestBody ReserveRecoveryDTO recoveryDTO);

    @ApiOperation("根据订单打印预付金信息")
    @PostMapping("/reserve/printByOrderGuid")
    void printByOrderGuid(@RequestBody SingleDataDTO query);

    @Slf4j
    @Component
    class ReserveRecordControllerFallback implements FallbackFactory<ReserveRecordApi> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public ReserveRecordApi create(Throwable throwable) {
            return new ReserveRecordApi() {
                @Override
                public ReserveRecordDetailDTO obtain(ReserveRecordGuidDTO guidDTO) {
                    log.error("reserve obtain fail with param: {}", guidDTO);
                    throw new RuntimeException(throwable);
                }

                @Override
                public ReserveRecordDetailDTO obtainApplet(String guid) {
                    log.error("reserve obtain applet fail with param: {}", guid);
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public Integer obtainDeviceType(ReserveRecordGuidDTO guidDTO) {
                    log.error("reserve obtain deviceType fail with param: {}", guidDTO);
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ReserveReportTotalDataDTO itemCount(ReserveReportParamDTO paramDTO) {
                    log.error("reserve item statistics fail with param: {}", paramDTO);
                    throw new RuntimeException(throwable);
                }

                @Override
                public ReserveHandoverDTO handover(HandoverPayQueryDTO handoverPayQueryDTO) {
                    log.error("reserve handover fail with param: {}", handoverPayQueryDTO);
                    throw new RuntimeException(throwable);
                }

                @Override
                public List<GatherRespDTO> gather(DailyReqDTO dailyReqDTO) {
                    log.error("reserve gather fail with param: {}", dailyReqDTO);
                    throw new RuntimeException(throwable);
                }

                @Override
                public ReserveRecordDTO queryByOrderGuid(SingleDataDTO query) {
                    log.error(HYSTRIX_PATTERN, "queryByOrderGuid", JacksonUtils.writeValueAsString(query),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public ReserveRecordDTO queryByGuid(SingleDataDTO query) {
                    log.error(HYSTRIX_PATTERN, "queryByGuid", JacksonUtils.writeValueAsString(query),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void separate(TableOrderCombineDTO tableOrderCombineDTO) {
                    log.error(HYSTRIX_PATTERN, "separate", JacksonUtils.writeValueAsString(tableOrderCombineDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void notifyTurn(TradeTableDTO tradeTableDTO) {
                    log.error(HYSTRIX_PATTERN, "notifyTurn", JacksonUtils.writeValueAsString(tradeTableDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void notifyPay(NotifyPayReqDTO reqDTO) {
                    log.error(HYSTRIX_PATTERN, "notifyPay", JacksonUtils.writeValueAsString(reqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void combine(TableOrderCombineDTO tableOrderCombineDTO) {
                    log.error(HYSTRIX_PATTERN, "combine", JacksonUtils.writeValueAsString(tableOrderCombineDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void recovery(ReserveRecoveryDTO recoveryDTO) {
                    log.error(HYSTRIX_PATTERN, "recovery", JacksonUtils.writeValueAsString(recoveryDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public ReserveRecordDetailDTO compensate(CompensateDTO guidDTO) {
                    log.error("reserve compensate fail with param: {}", guidDTO);
                    throw new RuntimeException(throwable);
                }

                @Override
                public ReserveRecordDetailDTO launch(ReserveRecordDTO reserveRecordDTO) {
                    log.error("reserve launch fail with param: {}", reserveRecordDTO);
                    throw new RuntimeException(throwable);
                }

                @Override
                public StatisticsDTO statistics(PhoneDTO queryDTO) {
                    log.error("reserve launch fail with param: {}", queryDTO);
                    throw new RuntimeException(throwable);
                }

                @Override
                public List<DineInItemDTO> getItems(ReserveRecordGuidDTO guidDTO) {
                    return Collections.emptyList();
                }

                @Override
                public Collection<ReserveRecordLessDTO> query(ReserveRecordQueryDTO queryDTO) {
                    log.error("reserve query fail with param: {}", queryDTO);
                    throw new RuntimeException(throwable);
                }

                @Override
                public ReserveRecordDetailDTO modify(ReserveRecordDTO reserveRecordDTO) {
                    log.error("reserve modify fail with param: {}", reserveRecordDTO);
                    throw new RuntimeException(throwable);
                }

                @Override
                public ReserveRecordDetailDTO pass(ReserveRecordGuidDTO guidDTO) {
                    log.error("reserve pass fail with param: {}", guidDTO);
                    throw new RuntimeException(throwable);
                }

                @Override
                public ReserveRecordDetailDTO cancle(ReserveRecordGuidDTO guidDTO) {
                    log.error("reserve cancle fail with param: {}", guidDTO);
                    throw new RuntimeException(throwable);
                }

                @Override
                public ReserveRecordDetailDTO open(ReserveRecordGuidDTO guidDTO) {
                    log.error("reserve open fail with param: {}", guidDTO);
                    throw new RuntimeException(throwable);
                }

                @Override
                public void printByOrderGuid(SingleDataDTO query) {
                    log.error(HYSTRIX_PATTERN, "printByOrderGuid", JacksonUtils.writeValueAsString(query),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }
            };
        }
    }
}