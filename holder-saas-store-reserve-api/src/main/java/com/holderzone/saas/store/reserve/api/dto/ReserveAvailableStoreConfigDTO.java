package com.holderzone.saas.store.reserve.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


/**
 * 预订
 * 可预订门店列表信息
 */
@Data
public class ReserveAvailableStoreConfigDTO implements Cloneable {

    @ApiModelProperty(value = "门店guid", required = true)
    private String storeGuid;

    @ApiModelProperty(value = "时段")
    private List<TimingSegmentDTO> segments;

    @ApiModelProperty(value = "锁桌的时间")
    private Float lockTableTiming;

    @ApiModelProperty(value = "解锁桌的时间")
    private Float unLockTableTiming;

    /**
     * 适用终端 ,隔开
     *
     * @see com.holderzone.saas.store.enums.BaseDeviceTypeEnum
     */
    @ApiModelProperty(value = "适用终端 ,隔开")
    private String deviceType;

    @ApiModelProperty(value = "可预订人数 ,隔开")
    private String crowdRange;

    @ApiModelProperty(value = "需求类型")
    private List<String> requirementType;

    @ApiModelProperty(value = "可预订日期")
    private Integer reserveDay;

    @ApiModelProperty(value = "可预订区域")
    private List<AreaDTO> reserveArea;

    @ApiModelProperty(value = "是否开启预订定金")
    private Boolean depositFlag;

    @ApiModelProperty(value = "收取类型 0:按人数收取 1：按区域收取")
    private Integer depositType;

    @ApiModelProperty(value = "预订超x人数收取定金")
    private String exceedsPeopleDeposit;

    @ApiModelProperty(value = "按桌台区域收取定金")
    private List<AreaDTO> areaDeposit;

    @ApiModelProperty(value = "可取消订单时间 单位小时")
    private Integer cancelableTime;


    @Override
    public Object clone() throws CloneNotSupportedException {
        return super.clone();
    }
}