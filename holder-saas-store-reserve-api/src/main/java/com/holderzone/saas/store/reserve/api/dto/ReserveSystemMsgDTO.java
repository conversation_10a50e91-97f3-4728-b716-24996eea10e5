package com.holderzone.saas.store.reserve.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@NoArgsConstructor
@Accessors(chain = true)
public class ReserveSystemMsgDTO implements Serializable {

    private static final long serialVersionUID = 116087722670628322L;

    @ApiModelProperty("预定单guid")
    private String guid;

    @ApiModelProperty("是否自动接单，0是，1否")
    private Integer autoRev;

    /**
     * 系统设备编号
     */
    @ApiModelProperty(value = "系统设备编号（云端生成）")
    private String deviceGuid;
}
