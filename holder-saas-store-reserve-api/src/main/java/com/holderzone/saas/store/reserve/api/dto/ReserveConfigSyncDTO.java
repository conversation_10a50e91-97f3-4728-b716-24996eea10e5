package com.holderzone.saas.store.reserve.api.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 预订同步配置
 */
@Data
public class ReserveConfigSyncDTO implements Cloneable {

    /**
     * 被同步的门店
     */
    @NotBlank(message = "门店guid不能为空")
    private String storeGuid;

    /**
     * 同步门店
     */
    @NotEmpty(message = "同步门店guid不能为空")
    private List<String> syncStoreGuids;


    @Override
    public Object clone() throws CloneNotSupportedException {
        return super.clone();
    }
}