package com.holderzone.saas.store.reserve.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.holderzone.saas.store.reserve.api.enums.ClientStateEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReserveRecordQueryDTO
 * @date 2019/04/24 11:13
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Data
@ApiModel
@AllArgsConstructor
@NoArgsConstructor
public class ReserveRecordQueryDTO {

    private static final String TIME_STR = " 00:00:00";

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private String guid;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime startTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime endTime;

    @ApiModelProperty("查询关键字")
    private String keyword;

    @ApiModelProperty("预订状态 \r\n" +
            "COMMIT 待审核" +
            "PASS 预定中" +
            "CANCLE 已取消" +
            "DELAY 已逾期" +
            "PICK_TABLE 到店选台"
    )
    private String stateEnum;


    public LocalDateTime fetchEnd() {
        if (endTime == null) {
            return null;
        }
        return endTime.plus(TimeUnit.DAYS.toMillis(1) - 1, ChronoUnit.MILLIS);
    }

    public ClientStateEnum getStateEnum() {
        return ClientStateEnum.getByName(this.stateEnum);
    }
}