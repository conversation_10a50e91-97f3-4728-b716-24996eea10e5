package com.holderzone.saas.store.reserve.api.dto;

import com.holderzone.saas.store.dto.reserve.TableDTO;
import com.holderzone.saas.store.dto.table.TableBasicDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TableQueryResultDTO
 * @date 2019/05/06 17:22
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Data
public class VoiceQueryResultDTO {

    @ApiModelProperty(value = "中午是否可用")
    private Boolean isNoonAvailable;

    @ApiModelProperty(value = "晚上是否可用")
    private Boolean isNightAvailable;

    @ApiModelProperty(value = "确定的时间是否可用")
    private Boolean isDateTimeAvailable;

    @ApiModelProperty(value = "确定的人数是否可用")
    private Boolean isPeopleTotalAvailable;

    @ApiModelProperty(value = "是否开通了包间")
    private Boolean isEnablePrivateRoom;

    @ApiModelProperty(value = "确定的房型是否可用")
    private Boolean isTableTypeAvailable;

    @ApiModelProperty(value = "根据所有预订信息，桌台是否可用")
    private Boolean isReserveAvailable;

    @ApiModelProperty(value = "根据所有预订信息，桌台是否可用")
    private TableDTO matchedTable;

    @ApiModelProperty(value = "可用的时段")
    private List<TimingSegmentDTO> timingSegments;

    public static VoiceQueryResultDTO unavailableInterval() {
        VoiceQueryResultDTO voiceQueryResultDTO = new VoiceQueryResultDTO();
        voiceQueryResultDTO.setIsNoonAvailable(false);
        voiceQueryResultDTO.setIsNightAvailable(false);
        return voiceQueryResultDTO;
    }

    public static VoiceQueryResultDTO availableInterval(Boolean isNoonAvailable, Boolean isNightAvailable) {
        VoiceQueryResultDTO voiceQueryResultDTO = new VoiceQueryResultDTO();
        voiceQueryResultDTO.setIsNoonAvailable(isNoonAvailable);
        voiceQueryResultDTO.setIsNightAvailable(isNightAvailable);
        return voiceQueryResultDTO;
    }

    public static VoiceQueryResultDTO unavailableSegments() {
        VoiceQueryResultDTO voiceQueryResultDTO = new VoiceQueryResultDTO();
        voiceQueryResultDTO.setTimingSegments(Collections.emptyList());
        return voiceQueryResultDTO;
    }

    public static VoiceQueryResultDTO availableSegments(List<TimingSegmentDTO> timingSegments) {
        VoiceQueryResultDTO voiceQueryResultDTO = new VoiceQueryResultDTO();
        voiceQueryResultDTO.setTimingSegments(timingSegments);
        return voiceQueryResultDTO;
    }

    public static VoiceQueryResultDTO unavailableDateTime() {
        VoiceQueryResultDTO voiceQueryResultDTO = new VoiceQueryResultDTO();
        voiceQueryResultDTO.setIsDateTimeAvailable(false);
        return voiceQueryResultDTO;
    }

    public static VoiceQueryResultDTO availableDateTime() {
        VoiceQueryResultDTO voiceQueryResultDTO = new VoiceQueryResultDTO();
        voiceQueryResultDTO.setIsDateTimeAvailable(true);
        return voiceQueryResultDTO;
    }

    public static VoiceQueryResultDTO unavailablePeopleTotal() {
        VoiceQueryResultDTO voiceQueryResultDTO = new VoiceQueryResultDTO();
        voiceQueryResultDTO.setIsPeopleTotalAvailable(false);
        return voiceQueryResultDTO;
    }

    public static VoiceQueryResultDTO availablePeopleTotal() {
        VoiceQueryResultDTO voiceQueryResultDTO = new VoiceQueryResultDTO();
        voiceQueryResultDTO.setIsPeopleTotalAvailable(true);
        return voiceQueryResultDTO;
    }

    public static VoiceQueryResultDTO disabledTableType() {
        VoiceQueryResultDTO voiceQueryResultDTO = new VoiceQueryResultDTO();
        voiceQueryResultDTO.setIsEnablePrivateRoom(false);
        voiceQueryResultDTO.setIsTableTypeAvailable(false);
        return voiceQueryResultDTO;
    }

    public static VoiceQueryResultDTO unavailableTableType() {
        VoiceQueryResultDTO voiceQueryResultDTO = new VoiceQueryResultDTO();
        voiceQueryResultDTO.setIsEnablePrivateRoom(true);
        voiceQueryResultDTO.setIsTableTypeAvailable(false);
        return voiceQueryResultDTO;
    }

    public static VoiceQueryResultDTO availableTableType() {
        VoiceQueryResultDTO voiceQueryResultDTO = new VoiceQueryResultDTO();
        voiceQueryResultDTO.setIsEnablePrivateRoom(true);
        voiceQueryResultDTO.setIsTableTypeAvailable(true);
        return voiceQueryResultDTO;
    }

    public static VoiceQueryResultDTO unavailableReserve() {
        VoiceQueryResultDTO voiceQueryResultDTO = new VoiceQueryResultDTO();
        voiceQueryResultDTO.setIsReserveAvailable(false);
        return voiceQueryResultDTO;
    }

    public static VoiceQueryResultDTO availableReserve() {
        VoiceQueryResultDTO voiceQueryResultDTO = new VoiceQueryResultDTO();
        voiceQueryResultDTO.setIsReserveAvailable(true);
        return voiceQueryResultDTO;
    }

    public static VoiceQueryResultDTO unavailableTable() {
        VoiceQueryResultDTO voiceQueryResultDTO = new VoiceQueryResultDTO();
        voiceQueryResultDTO.setMatchedTable(null);
        return voiceQueryResultDTO;
    }

    public static VoiceQueryResultDTO availableTable(TableBasicDTO tableBasicDTO) {
        VoiceQueryResultDTO voiceQueryResultDTO = new VoiceQueryResultDTO();
        TableDTO tableDTO = new TableDTO();
        tableDTO.setGuid(tableBasicDTO.getGuid());
        tableDTO.setName(tableBasicDTO.getTableCode());
        tableDTO.setAreaGuid(tableBasicDTO.getAreaGuid());
        tableDTO.setAreaName(tableBasicDTO.getAreaName());
        voiceQueryResultDTO.setMatchedTable(tableDTO);
        return voiceQueryResultDTO;
    }
}