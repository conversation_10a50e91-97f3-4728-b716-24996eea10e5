package com.holderzone.saas.store.reserve.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
 * 预定小程序端 订单列表
 */
@Data
public class ReserveRecordAppletPageDTO implements Serializable {

    private static final long serialVersionUID = 1902053170959717213L;

    /**
     * 订单guid
     */
    private String recordGuid;

    private String storeGuid;

    private String state;

    @ApiModelProperty("预定开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime reserveStartTime;

    /**
     * 下单时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "预定人数")
    private Integer number;

    /**
     * 预定金额
     */
    private BigDecimal reserveAmount;

    /**
     * 预定区域
     */
    private String areaName;

    /**
     * 取消原因
     */
    private String cancleReason;
}