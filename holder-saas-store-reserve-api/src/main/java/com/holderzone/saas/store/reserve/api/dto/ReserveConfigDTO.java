package com.holderzone.saas.store.reserve.api.dto;

import com.holderzone.resource.common.dto.validate.Update;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReserveConfigDTO
 * @date 2019/05/05 10:09
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Data
@ApiModel
public class ReserveConfigDTO implements Cloneable {

    @ApiModelProperty("guid")
    @NotBlank(message = "预定guid不能为空", groups = Update.class)
    private String guid;

    @ApiModelProperty(value = "门店guid", required = true)
    @NotBlank
    private String storeGuid;

    @ApiModelProperty(value = "时段", required = false)
    private List<TimingSegmentDTO> segments;

    @ApiModelProperty(value = "锁桌的时间", required = false)
    private Float lockTableTiming;

    @ApiModelProperty(value = "解锁桌的时间", required = false)
    private Float unLockTableTiming;

    @ApiModelProperty(value = "是否发送预定成功短信", required = false)
    private Boolean isEnableSuccessMessage;

    @ApiModelProperty(value = "发送预期提示的时间", required = false)
    private Float sendWarnMessageTiming;

    @ApiModelProperty(value = "是否发送预期提示短信", required = false)
    private Boolean isEnableWarnMessage;

    @ApiModelProperty(value = "是否启用包房", required = false)
    private Boolean isEnablePrivateRoom;

    /**
     * 适用终端 ,隔开
     *
     * @see com.holderzone.saas.store.enums.BaseDeviceTypeEnum
     */
    @ApiModelProperty(value = "适用终端 ,隔开")
    private String deviceType;

    @ApiModelProperty(value = "可预订人数 ,隔开")
    private String crowdRange;

    @ApiModelProperty(value = "需求类型")
    private List<String> requirementType;

    @ApiModelProperty(value = "可预订日期")
    private Integer reserveDay;

    @ApiModelProperty(value = "可预订区域")
    private List<AreaDTO> reserveArea;

    @ApiModelProperty(value = "是否开启预订定金")
    private Boolean depositFlag;

    @ApiModelProperty(value = "收取类型 0:按人数收取 1：按区域收取")
    private Integer depositType;

    @ApiModelProperty(value = "预订超x人数收取定金")
    private String exceedsPeopleDeposit;

    @ApiModelProperty(value = "按桌台区域收取定金")
    private List<AreaDTO> areaDeposit;

    @ApiModelProperty(value = "可取消订单时间 单位小时")
    private Integer cancelableTime;

    @ApiModelProperty(value = "邀请函图片")
    private List<String> invitationImages;

    @Override
    public Object clone() throws CloneNotSupportedException {
        return super.clone();
    }

    public List<TimingSegmentDTO> getSub() {
        return segments.stream().flatMap(dto -> dto.getSub().stream()).collect(Collectors.toList());
    }
}