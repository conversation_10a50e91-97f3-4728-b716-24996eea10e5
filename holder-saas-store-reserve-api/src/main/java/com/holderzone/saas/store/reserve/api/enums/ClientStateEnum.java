package com.holderzone.saas.store.reserve.api.enums;

import com.holderzone.saas.store.reserve.api.dto.ClientStateAble;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ClientStateEnum
 * @date 2019/05/06 15:51
 * @description //TODO
 * @program holder-saas-store-reserve
 */
public enum ClientStateEnum {

    //=
    COMMIT(0b100000, "待审核"),

    // =
    PASS(0b100110, "预定中"),

    // &
    CANCLE(0b000011, "已取消"),
    /**
     * 39
     */
    NO_PAY(0b100111,"未付定金"),
    /**
     * 47
     */
    NO_PAY_CANCEL(0b101111,"支付失敗或者未付定金而取消"),

    //& delay
    DELAY(0b100110, "已逾期"),

    //&
    OPEN_TABLE(0b111110, "到店开台"),

    /**
     * 54
     */
    PICK_TABLE(0b110110, "到店选台"),

    /**
     * 56
     */
    FINISH(0b111000, "已结账"),
    ;

    private int code;

    private String message;

    ClientStateEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public static ClientStateEnum getByState(ClientStateAble state) {
        if (state.getState() == PASS.getCode() && state.getIsDelay() != null && state.getIsDelay()) {
            return DELAY;
        }
        if (state.getState() == COMMIT.getCode()) {
            return COMMIT;
        }
        if (state.getState() == PASS.getCode()) {
            return PASS;
        }
        if (state.getState() == NO_PAY.getCode()) {
            return NO_PAY;
        }
        if (state.getState() == NO_PAY_CANCEL.getCode()) {
            return NO_PAY_CANCEL;
        }
        if ((state.getState() & CANCLE.getCode()) == CANCLE.getCode()) {
            return CANCLE;
        }
        if (state.getState() == OPEN_TABLE.getCode() || state.getState() == PICK_TABLE.getCode()
                || state.getState() == FINISH.getCode()) {
            return PICK_TABLE;
        }
        if (state.getState() == FINISH.getCode()) {
            return FINISH;
        }
        return null;
    }

    public static ClientStateEnum getByName(String state) {
        return Arrays.stream(values()).filter(e -> e.name().equals(state)).findFirst().orElse(null);
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}