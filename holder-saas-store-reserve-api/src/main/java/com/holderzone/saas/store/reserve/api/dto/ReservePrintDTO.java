package com.holderzone.saas.store.reserve.api.dto;

import com.holderzone.saas.store.dto.reserve.ReserveReportParamDTO;
import com.holderzone.saas.store.dto.reserve.ReserveReportTotalDataDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TableQueryResultDTO
 * @date 2019/05/06 17:22
 * @description //TODO
 * @program holder-saas-store-reserve
 */

@Data
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(description = "菜品记录")
public class ReservePrintDTO {

    @NotBlank(message = "设备号不能为空")
    @ApiModelProperty(value = "设备号", required = true)
    private String deviceId;

    @NotBlank(message = "企业guid不能为空")
    @ApiModelProperty(value = "企业guid", required = true)
    private String enterpriseGuid;

    @NotBlank(message = "企业不能为空")
    @ApiModelProperty(value = "企业", required = true)
    private String enterpriseName;

    @NotBlank(message = "门店guid不能为空")
    @ApiModelProperty(value = "门店guid", required = true)
    private String storeGuid;

    @NotBlank(message = "门店名称不能为空")
    @ApiModelProperty(value = "门店名称", required = true)
    private String storeName;

    @NotBlank(message = "用户guid不能为空")
    @ApiModelProperty(value = "门店guid", required = true)
    private String userGuid;

    @NotBlank(message = "用户名称不能为空")
    @ApiModelProperty(value = "用户名称", required = true)
    private String userName;

    @NotEmpty(message = "报表数据不得为空")
    @ApiModelProperty(value = "报表数据", required = true)
    private ReserveReportTotalDataDTO reserveReportTotalDataDTO;

    @NotEmpty(message = "数据范围不得为空")
    @ApiModelProperty(value = "数据范围", required = true)
    private ReserveReportParamDTO reserveReportParamDTO;


}