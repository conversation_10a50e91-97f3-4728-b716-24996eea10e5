package com.holderzone.saas.store.reserve.api.common;

import com.holderzone.saas.store.reserve.api.dto.TimingSegment;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.function.Predicate;

/**
 * <AUTHOR>
 * @version 1.0
 * @className Utils
 * @date 2019/05/06 15:24
 * @description //TODO
 * @program holder-saas-store-reserve
 */
public class Utils {

    public static <T extends TimingSegment> T fetchCurrent(LocalDateTime localDateTime, Collection<T> segments) {
//        Map<Long,T> ref = new HashMap<>();
        LocalDate refDate = localDateTime.toLocalDate();
        for (T vo : segments) {
            LocalDateTime start = vo.startOfRef(refDate);
            LocalDateTime end = vo.endOfRef(refDate);
            if (!start.isAfter(localDateTime) && end.isAfter(localDateTime)) {
                return vo;
            }
        }
        return null;
    }

    public static <T extends TimingSegment> T fetchCurrentSeg(LocalDateTime localDateTime, Collection<T> segments) {
//        Map<Long,T> ref = new HashMap<>();
        LocalDate refDate = localDateTime.toLocalDate();
        for (T vo : segments) {
            LocalDateTime start = vo.startOfRef(refDate);
            LocalDateTime end = vo.endOfRef(refDate);
            if (!start.isAfter(localDateTime) && !end.isBefore(localDateTime)) {
                return vo;
            }
        }
        return null;
    }


    public static Predicate<BigDecimal> greatThanZeroConsume(){
        return bigDecimal -> bigDecimal != null && bigDecimal.compareTo(BigDecimal.ZERO) > 0;
    }

//    public static copyBaseDTO()
}