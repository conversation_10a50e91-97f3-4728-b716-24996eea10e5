package com.holderzone.saas.store.reserve.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.reserve.TableDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collection;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReserveRecordLessDTO
 * @date 2019/04/29 17:22
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Data
public class ReserveRecordLessDTO extends BaseDTO {

    private String guid;

    private String storeGuid;

    private Integer number;

    private String state;

    private String name;

    private String phone;

    private Byte gender;

    private BigDecimal reserveAmount;

    /**
     * 退款金额
     */
    private BigDecimal reserveRefundAmount;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime reserveStartTime;

    /**
     * 期望区域
     */
    private AreaDTO area;

    @ApiModelProperty("桌台信息")
    private Collection<TableDTO> tables;

    private String remark;

    private Boolean hasItem;
}