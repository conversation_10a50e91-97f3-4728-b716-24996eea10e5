package com.holderzone.saas.store.reserve.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.lang.Nullable;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TableQueryResultDTO
 * @date 2019/05/06 17:22
 * @description //TODO
 * @program holder-saas-store-reserve
 */

@Data
@NoArgsConstructor
public class VoiceQueryDTO extends BaseDTO {

    @NotEmpty(message = "门店Guid不得为空")
    @ApiModelProperty(value = "门店Guid", required = true)
    private String storeGuid;

    @Nullable
    @JsonFormat(pattern = "yyyyMMdd")
    @JsonSerialize(using = LocalDateSerializer.class)
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @ApiModelProperty(value = "预定日期：如 20190516")
    private LocalDate reserveDate;

    @Nullable
    @ApiModelProperty(value = "预定区间：如 0表示中午，1表示下午/晚上")
    private Integer reserveInterval;

    @Nullable
    @JsonFormat(pattern = "yyyyMMddHH:mm")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @ApiModelProperty(value = "预定时间：如 2019051619:00")
    private LocalDateTime reserveTime;

    @Nullable
    @Min(value = 1, message = "就餐人数范围1-99")
    @Max(value = 99, message = "就餐人数范围1-99")
    @ApiModelProperty(value = "就餐人数")
    private Integer peopleTotal;

    @Nullable
    @Min(value = 0, message = "桌台类型：0=大厅，1=包房")
    @Max(value = 1, message = "桌台类型：0=大厅，1=包房")
    @ApiModelProperty(value = "桌台类型：0=大厅，1=包房")
    private Integer roomType;
}