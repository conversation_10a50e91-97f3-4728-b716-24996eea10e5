package com.holderzone.saas.store.reserve.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version 1.0
 * @className StoreGuidDTO
 * @date 2019/05/13 10:26
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Data
@ApiModel
public class StoreGuidDTO {

    @ApiModelProperty("storeGuid")
    @NotBlank
    private String storeGuid;
}