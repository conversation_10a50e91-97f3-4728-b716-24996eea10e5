package com.holderzone.saas.store.reserve.api;

import com.holderzone.saas.store.reserve.api.dto.*;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReserveRecordApi
 * @date 2019/04/26 18:24
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Api("预定")
@FeignClient(value = "holder-saas-store-reserve", fallbackFactory = ReserveTableApi.ReserveTableControllerFallback.class)
public interface ReserveTableApi {

    @ApiOperation("查询桌台")
    @PostMapping("/reserve/table/query")
    Collection<TableQueryResultDTO> query(@RequestBody TableQueryDTO queryDTO);

    @ApiOperation("根据桌台查询对应的预定记录")
    @PostMapping("/reserve/table/reserve")
    List<TableReserveRecordRef> query(@RequestBody TableGuidsDTO guidsDTO);

    @ApiOperation("根据条件查询可用桌台，")
    @PostMapping("/reserve/date/query")
    VoiceQueryResultDTO queryDate(@RequestBody VoiceQueryDTO voiceQueryDTO);

    @ApiOperation("根据条件查询可用桌台，")
    @PostMapping("/reserve/interval/query")
    VoiceQueryResultDTO queryInterval(@RequestBody VoiceQueryDTO voiceQueryDTO);

    @ApiOperation("根据条件查询可用桌台，")
    @PostMapping("/reserve/date_time/query")
    VoiceQueryResultDTO queryDateTime(@RequestBody VoiceQueryDTO voiceQueryDTO);

    @ApiOperation("根据条件查询可用桌台，")
    @PostMapping("/reserve/people/query")
    VoiceQueryResultDTO queryPeople(@RequestBody VoiceQueryDTO voiceQueryDTO);

    @ApiOperation("根据条件查询可用桌台，")
    @PostMapping("/reserve/room_type/query")
    VoiceQueryResultDTO queryRoomType(@RequestBody VoiceQueryDTO voiceQueryDTO);

    @ApiOperation("根据条件查询可用桌台，")
    @PostMapping("/reserve/all_cond/query")
    VoiceQueryResultDTO queryAllCond(@RequestBody VoiceQueryDTO voiceQueryDTO);

    @ApiOperation("根据条件查询可用桌台，")
    @PostMapping("/reserve/matched_table/fetch")
    VoiceQueryResultDTO fetchMatchedTable(@RequestBody VoiceQueryDTO voiceQueryDTO);

    @ApiOperation("打印预定菜品统计单")
    @PostMapping("/reserve/pre_ordering/print")
    void printPreOrdering(@RequestBody ReservePrintDTO reservePrintDTO);

    @Slf4j
    @Component
    class ReserveTableControllerFallback implements FallbackFactory<ReserveTableApi> {

        @Override
        public ReserveTableApi create(Throwable throwable) {
            return new ReserveTableApi() {
                @Override
                public Collection<TableQueryResultDTO> query(TableQueryDTO queryDTO) {
                    log.error("reserve table query fail with param: {}", queryDTO);
                    throw new RuntimeException(throwable);
                }

                @Override
                public List<TableReserveRecordRef> query(TableGuidsDTO guidsDTO) {
                    log.error("reserve table query fail with param: {}", guidsDTO);
                    throw new RuntimeException(throwable);
                }

                @Override
                public VoiceQueryResultDTO queryDate(VoiceQueryDTO voiceQueryDTO) {
                    log.error("reserve query by condition fail with param: {}", voiceQueryDTO);
                    throw new RuntimeException(throwable);
                }

                @Override
                public VoiceQueryResultDTO queryInterval(VoiceQueryDTO voiceQueryDTO) {
                    log.error("reserve query by condition fail with param: {}", voiceQueryDTO);
                    throw new RuntimeException(throwable);
                }

                @Override
                public VoiceQueryResultDTO queryDateTime(VoiceQueryDTO voiceQueryDTO) {
                    log.error("reserve query by condition fail with param: {}", voiceQueryDTO);
                    throw new RuntimeException(throwable);
                }

                @Override
                public VoiceQueryResultDTO queryRoomType(VoiceQueryDTO voiceQueryDTO) {
                    log.error("reserve query by condition fail with param: {}", voiceQueryDTO);
                    throw new RuntimeException(throwable);
                }

                @Override
                public VoiceQueryResultDTO queryPeople(VoiceQueryDTO voiceQueryDTO) {
                    log.error("reserve query by condition fail with param: {}", voiceQueryDTO);
                    throw new RuntimeException(throwable);
                }

                @Override
                public VoiceQueryResultDTO queryAllCond(VoiceQueryDTO voiceQueryDTO) {
                    log.error("reserve query by condition fail with param: {}", voiceQueryDTO);
                    throw new RuntimeException(throwable);
                }

                @Override
                public VoiceQueryResultDTO fetchMatchedTable(VoiceQueryDTO voiceQueryDTO) {
                    log.error("reserve query by condition fail with param: {}", voiceQueryDTO);
                    throw new RuntimeException(throwable);
                }

                @Override
                public void printPreOrdering(ReservePrintDTO reservePrintDTO) {
                    log.error("reserve query by condition fail with param: {}", reservePrintDTO);
                    throw new RuntimeException(throwable);
                }
            };
        }
    }
}