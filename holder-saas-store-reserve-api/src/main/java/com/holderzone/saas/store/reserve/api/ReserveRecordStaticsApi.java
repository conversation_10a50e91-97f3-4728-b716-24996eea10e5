package com.holderzone.saas.store.reserve.api;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.reserve.CommitStatisticsReqDTO;
import com.holderzone.saas.store.dto.reserve.ReserveCommitStaticsDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


/**
 * 预定 统计
 */
@FeignClient(value = "holder-saas-store-reserve", fallbackFactory = ReserveRecordStaticsApi.ReserveRecordStaticsControllerFallback.class)
public interface ReserveRecordStaticsApi {

    @ApiOperation("查询预定总待处理订单数")
    @GetMapping("/reserve/statistics/commit")
    ReserveCommitStaticsDTO commitStatistics();

    @ApiOperation("查询近30天预定待处理订单数")
    @GetMapping("/reserve/statistics/by_day/commit")
    ReserveCommitStaticsDTO commitStatisticsByDay();

    @ApiOperation("查询时间范围内待处理订单")
    @PostMapping("/statistics/commit/by_scope")
    ReserveCommitStaticsDTO commitStatisticsByScope(@RequestBody CommitStatisticsReqDTO reqDTO);

    @Slf4j
    @Component
    class ReserveRecordStaticsControllerFallback implements FallbackFactory<ReserveRecordStaticsApi> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public ReserveRecordStaticsApi create(Throwable throwable) {
            return new ReserveRecordStaticsApi() {
                @Override
                public ReserveCommitStaticsDTO commitStatistics() {
                    log.error("reserve commit statistics fail");
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ReserveCommitStaticsDTO commitStatisticsByDay() {
                    log.error("reserve commit statistics by day fail");
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ReserveCommitStaticsDTO commitStatisticsByScope(CommitStatisticsReqDTO reqDTO) {
                    log.error(HYSTRIX_PATTERN, "commitStatisticsByScope", JacksonUtils.writeValueAsString(reqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

            };
        }
    }
}