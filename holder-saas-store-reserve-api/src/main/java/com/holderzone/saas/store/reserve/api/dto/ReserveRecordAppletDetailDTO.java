package com.holderzone.saas.store.reserve.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;


/**
 * 预定小程序端 订单详情
 */
@Data
public class ReserveRecordAppletDetailDTO extends ReserveRecordDTO {

    private static final long serialVersionUID = 1902053170959717213L;

    /**
     * 订单guid
     */
    private String recordGuid;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 下单时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtCreate;

    /**
     * 取消时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime cancleTime;

    @ApiModelProperty(value = "可取消订单时间 单位小时")
    private Integer cancelableTime;

    @ApiModelProperty(value = "取消原因")
    private String cancleReason;

    /**
     * 取消角色
     */
    private String cancelRole;

    /**
     * 最大可退款时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime maxCancelableTime;

    @ApiModelProperty(value = "邀请函图片")
    private List<String> invitationImages;
}