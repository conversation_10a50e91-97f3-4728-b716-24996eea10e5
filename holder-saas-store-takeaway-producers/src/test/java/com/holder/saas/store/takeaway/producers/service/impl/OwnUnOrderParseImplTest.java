package com.holder.saas.store.takeaway.producers.service.impl;

import com.holderzone.saas.store.dto.takeaway.UnDiscount;
import com.holderzone.saas.store.dto.takeaway.UnItem;
import com.holderzone.saas.store.dto.takeaway.UnOrder;
import com.holderzone.saas.store.dto.takeaway.request.CustomersDTO;
import com.holderzone.saas.store.dto.takeaway.request.SalesOrderConstituteDTO;
import com.holderzone.saas.store.dto.takeaway.request.SalesOrderDTO;
import com.holderzone.saas.store.dto.takeaway.request.SalesOrderGoodsDTO;
import org.junit.Before;
import org.junit.Test;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;

import static org.junit.Assert.assertEquals;

public class OwnUnOrderParseImplTest {

    private OwnUnOrderParseImpl ownUnOrderParseImplUnderTest;

    @Before
    public void setUp() throws Exception {
        ownUnOrderParseImplUnderTest = new OwnUnOrderParseImpl();
    }

    @Test
    public void testFromOrderCreated() {
        // Setup
        final SalesOrderDTO salesOrderDTO = new SalesOrderDTO();
        salesOrderDTO.setOrderId(0L);
        salesOrderDTO.setSerialNumber("orderViewId");
        salesOrderDTO.setTakeAwayNum("orderDaySn");
        salesOrderDTO.setGuestCount(0);
        salesOrderDTO.setDeliverAmount(new BigDecimal("0.00"));
        salesOrderDTO.setPayAmount(new BigDecimal("0.00"));
        salesOrderDTO.setGoodsAmount(new BigDecimal("0.00"));
        salesOrderDTO.setDiscountAmount(new BigDecimal("0.00"));
        salesOrderDTO.setRemark("orderRemark");
        salesOrderDTO.setOrderStatus(0);
        salesOrderDTO.setCreateTime("CreateTime");
        final CustomersDTO customersDTO = new CustomersDTO();
        customersDTO.setName("customerName");
        customersDTO.setPhone("Phone");
        customersDTO.setAddress("Address");
        customersDTO.setTakeMealTime("TakeMealTime");
        customersDTO.setLongitude(new BigDecimal("0.00"));
        customersDTO.setLatitude(new BigDecimal("0.00"));
        salesOrderDTO.setCustomersDTO(customersDTO);
        final SalesOrderGoodsDTO salesOrderGoodsDTO = new SalesOrderGoodsDTO();
        salesOrderGoodsDTO.setGoodsName("GoodsName");
        salesOrderGoodsDTO.setNumber(0);
        salesOrderGoodsDTO.setPrice(new BigDecimal("0.00"));
        salesOrderGoodsDTO.setAmount(new BigDecimal("0.00"));
        salesOrderGoodsDTO.setRemark("itemProperty");
        salesOrderGoodsDTO.setThirdSkuId("itemSku");
        salesOrderDTO.setSalesOrderGoods(Arrays.asList(salesOrderGoodsDTO));
        final SalesOrderConstituteDTO salesOrderConstituteDTO = new SalesOrderConstituteDTO();
        salesOrderConstituteDTO.setDiscountType(0);
        salesOrderConstituteDTO.setDiscountAmount(new BigDecimal("0.00"));
        salesOrderConstituteDTO.setDiscountName("discountName");
        salesOrderDTO.setSalesOrderConstituteDTOs(Arrays.asList(salesOrderConstituteDTO));

        final UnOrder expectedResult = new UnOrder();
        expectedResult.setOrderId("orderId");
        expectedResult.setOrderViewId("orderViewId");
        expectedResult.setOrderDaySn("orderDaySn");
        expectedResult.setOrderRemark("orderRemark");
        expectedResult.setReserve(false);
        expectedResult.setCustomerName("customerName");
        expectedResult.setCustomerPhone("customerPhone");
        expectedResult.setCustomerAddress("Address");
        expectedResult.setCustomerNumber(0);
        expectedResult.setFirstOrder(false);
        expectedResult.setShipLatitude("shipLatitude");
        expectedResult.setShipLongitude("shipLongitude");
        expectedResult.setTotal(new BigDecimal("0.00"));
        expectedResult.setShipTotal(new BigDecimal("0.00"));
        expectedResult.setItemTotal(new BigDecimal("0.00"));
        expectedResult.setPackageTotal(new BigDecimal("0.00"));
        expectedResult.setDiscountTotal(new BigDecimal("0.00"));
        expectedResult.setEnterpriseDiscount(new BigDecimal("0.00"));
        expectedResult.setPlatformDiscount(new BigDecimal("0.00"));
        expectedResult.setCustomerActualPay(new BigDecimal("0.00"));
        expectedResult.setShopTotal(new BigDecimal("0.00"));
        expectedResult.setOnlinePay(false);
        expectedResult.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final UnItem unItem = new UnItem();
        unItem.setItemSku("itemSku");
        unItem.setItemName("GoodsName");
        unItem.setItemPrice(new BigDecimal("0.00"));
        unItem.setItemCount(new BigDecimal("0.00"));
        unItem.setItemTotal(new BigDecimal("0.00"));
        unItem.setItemProperty("itemProperty");
        expectedResult.setArrayOfUnItem(Arrays.asList(unItem));
        final UnDiscount unDiscount = new UnDiscount();
        unDiscount.setDiscountName("discountName");
        unDiscount.setTotalDiscount(new BigDecimal("0.00"));
        expectedResult.setArrayOfUnDiscount(Arrays.asList(unDiscount));

        // Run the test
        final UnOrder result = ownUnOrderParseImplUnderTest.fromOrderCreated(salesOrderDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }
}
