package com.holder.saas.store.takeaway.producers.service.impl;

import com.holder.saas.store.takeaway.producers.service.TcdUnOrderParser;
import com.holder.saas.store.takeaway.producers.service.UnOrderMqService;
import com.holderzone.saas.store.dto.takeaway.UnOrder;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutTcdOrderReqDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class TcdCallbackServiceImplTest {

    @Mock
    private TcdUnOrderParser mockTcdUnOrderParser;
    @Mock
    private UnOrderMqService mockUnOrderMqService;

    private TcdCallbackServiceImpl tcdCallbackServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        tcdCallbackServiceImplUnderTest = new TcdCallbackServiceImpl(mockTcdUnOrderParser, mockUnOrderMqService);
    }

    @Test
    public void testOrderCallback() {
        // Setup
        final TakeoutTcdOrderReqDTO takeoutTcdOrderReqDTO = new TakeoutTcdOrderReqDTO();
        takeoutTcdOrderReqDTO.setMerchantId(0L);
        takeoutTcdOrderReqDTO.setMerchantNo(0L);
        takeoutTcdOrderReqDTO.setMerchantUserId(0L);
        takeoutTcdOrderReqDTO.setBrandId(0L);
        takeoutTcdOrderReqDTO.setOrderState(TakeoutTcdOrderReqDTO.OrderState.PENDING);

        // Configure TcdUnOrderParser.fromOrderCreate(...).
        final UnOrder unOrder = new UnOrder();
        unOrder.setOrderStatus(0);
        unOrder.setShopId(0L);
        unOrder.setShopName("shopName");
        unOrder.setCbMsgType(0);
        unOrder.setReplyMsgType(0);
        final TakeoutTcdOrderReqDTO req = new TakeoutTcdOrderReqDTO();
        req.setMerchantId(0L);
        req.setMerchantNo(0L);
        req.setMerchantUserId(0L);
        req.setBrandId(0L);
        req.setOrderState(TakeoutTcdOrderReqDTO.OrderState.PENDING);
        when(mockTcdUnOrderParser.fromOrderCreate(req)).thenReturn(unOrder);

        // Configure TcdUnOrderParser.fromOrderCreatePersonPending(...).
        final UnOrder unOrder1 = new UnOrder();
        unOrder1.setOrderStatus(0);
        unOrder1.setShopId(0L);
        unOrder1.setShopName("shopName");
        unOrder1.setCbMsgType(0);
        unOrder1.setReplyMsgType(0);
        final TakeoutTcdOrderReqDTO req1 = new TakeoutTcdOrderReqDTO();
        req1.setMerchantId(0L);
        req1.setMerchantNo(0L);
        req1.setMerchantUserId(0L);
        req1.setBrandId(0L);
        req1.setOrderState(TakeoutTcdOrderReqDTO.OrderState.PENDING);
        when(mockTcdUnOrderParser.fromOrderCreatePersonPending(req1)).thenReturn(unOrder1);

        // Configure TcdUnOrderParser.fromOrderConfirmed(...).
        final UnOrder unOrder2 = new UnOrder();
        unOrder2.setOrderStatus(0);
        unOrder2.setShopId(0L);
        unOrder2.setShopName("shopName");
        unOrder2.setCbMsgType(0);
        unOrder2.setReplyMsgType(0);
        final TakeoutTcdOrderReqDTO req2 = new TakeoutTcdOrderReqDTO();
        req2.setMerchantId(0L);
        req2.setMerchantNo(0L);
        req2.setMerchantUserId(0L);
        req2.setBrandId(0L);
        req2.setOrderState(TakeoutTcdOrderReqDTO.OrderState.PENDING);
        when(mockTcdUnOrderParser.fromOrderConfirmed(req2)).thenReturn(unOrder2);

        // Configure TcdUnOrderParser.fromOrderCanceled(...).
        final UnOrder unOrder3 = new UnOrder();
        unOrder3.setOrderStatus(0);
        unOrder3.setShopId(0L);
        unOrder3.setShopName("shopName");
        unOrder3.setCbMsgType(0);
        unOrder3.setReplyMsgType(0);
        final TakeoutTcdOrderReqDTO req3 = new TakeoutTcdOrderReqDTO();
        req3.setMerchantId(0L);
        req3.setMerchantNo(0L);
        req3.setMerchantUserId(0L);
        req3.setBrandId(0L);
        req3.setOrderState(TakeoutTcdOrderReqDTO.OrderState.PENDING);
        when(mockTcdUnOrderParser.fromOrderCanceled(req3)).thenReturn(unOrder3);

        // Configure TcdUnOrderParser.fromOrderRefunded(...).
        final UnOrder unOrder4 = new UnOrder();
        unOrder4.setOrderStatus(0);
        unOrder4.setShopId(0L);
        unOrder4.setShopName("shopName");
        unOrder4.setCbMsgType(0);
        unOrder4.setReplyMsgType(0);
        final TakeoutTcdOrderReqDTO req4 = new TakeoutTcdOrderReqDTO();
        req4.setMerchantId(0L);
        req4.setMerchantNo(0L);
        req4.setMerchantUserId(0L);
        req4.setBrandId(0L);
        req4.setOrderState(TakeoutTcdOrderReqDTO.OrderState.PENDING);
        when(mockTcdUnOrderParser.fromOrderRefunded(req4)).thenReturn(unOrder4);

        // Configure TcdUnOrderParser.fromOrderShipping(...).
        final UnOrder unOrder5 = new UnOrder();
        unOrder5.setOrderStatus(0);
        unOrder5.setShopId(0L);
        unOrder5.setShopName("shopName");
        unOrder5.setCbMsgType(0);
        unOrder5.setReplyMsgType(0);
        final TakeoutTcdOrderReqDTO req5 = new TakeoutTcdOrderReqDTO();
        req5.setMerchantId(0L);
        req5.setMerchantNo(0L);
        req5.setMerchantUserId(0L);
        req5.setBrandId(0L);
        req5.setOrderState(TakeoutTcdOrderReqDTO.OrderState.PENDING);
        when(mockTcdUnOrderParser.fromOrderShipping(req5)).thenReturn(unOrder5);

        // Configure TcdUnOrderParser.fromOrderFinished(...).
        final UnOrder unOrder6 = new UnOrder();
        unOrder6.setOrderStatus(0);
        unOrder6.setShopId(0L);
        unOrder6.setShopName("shopName");
        unOrder6.setCbMsgType(0);
        unOrder6.setReplyMsgType(0);
        final TakeoutTcdOrderReqDTO req6 = new TakeoutTcdOrderReqDTO();
        req6.setMerchantId(0L);
        req6.setMerchantNo(0L);
        req6.setMerchantUserId(0L);
        req6.setBrandId(0L);
        req6.setOrderState(TakeoutTcdOrderReqDTO.OrderState.PENDING);
        when(mockTcdUnOrderParser.fromOrderFinished(req6)).thenReturn(unOrder6);

        // Run the test
        tcdCallbackServiceImplUnderTest.orderCallback(takeoutTcdOrderReqDTO);

        // Verify the results
        // Confirm UnOrderMqService.sendUnOrder(...).
        final UnOrder unorder = new UnOrder();
        unorder.setOrderStatus(0);
        unorder.setShopId(0L);
        unorder.setShopName("shopName");
        unorder.setCbMsgType(0);
        unorder.setReplyMsgType(0);
        verify(mockUnOrderMqService).sendUnOrder(unorder);
    }
}
