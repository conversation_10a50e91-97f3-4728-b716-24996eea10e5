package com.holder.saas.store.takeaway.producers.service.job;

import com.holder.saas.store.takeaway.producers.entity.domain.MtAuthDO;
import com.holder.saas.store.takeaway.producers.service.MtAuthService;
import com.holder.saas.store.takeaway.producers.service.MtUnOrderParser;
import com.holder.saas.store.takeaway.producers.service.UnOrderMqService;
import com.holderzone.saas.store.dto.takeaway.MtQueryOrderDetail;
import com.holderzone.saas.store.dto.takeaway.UnOrder;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class MtOrderJobTest {

    @Mock
    private MtAuthService mockMtAuthService;
    @Mock
    private MtUnOrderParser mockMtUnOrderParser;
    @Mock
    private UnOrderMqService mockUnOrderMqService;

    private MtOrderJob mtOrderJobUnderTest;

    @Before
    public void setUp() throws Exception {
        mtOrderJobUnderTest = new MtOrderJob(mockMtAuthService, mockMtUnOrderParser, mockUnOrderMqService);
        ReflectionTestUtils.setField(mtOrderJobUnderTest, "developerId", 0);
        ReflectionTestUtils.setField(mtOrderJobUnderTest, "mtSignKey", "mtSignKey");
    }

    @Test
    public void testQueryNewOrdersByDevId() {
        // Setup
        // Configure MtAuthService.getAuth(...).
        final MtAuthDO mtAuthDO = new MtAuthDO();
        mtAuthDO.setId(0);
        mtAuthDO.setGuid("55847f57-6c33-4225-9f87-d7be665ecabe");
        mtAuthDO.setEPoiId("ePoiId");
        mtAuthDO.setEnterpriseGuid("enterpriseGuid");
        mtAuthDO.setAccessToken("accessToken");
        when(mockMtAuthService.getAuth("epoiId", 0)).thenReturn(mtAuthDO);

        // Configure MtUnOrderParser.fromMtQueryOrderDetail(...).
        final UnOrder unOrder = new UnOrder();
        unOrder.setOrderStatus(0);
        unOrder.setShopId(0L);
        unOrder.setSubStoreId(0L);
        unOrder.setShopName("shopName");
        unOrder.setCbMsgType(0);
        final MtQueryOrderDetail mtQueryOrderDetail = new MtQueryOrderDetail();
        mtQueryOrderDetail.setCtime(0L);
        mtQueryOrderDetail.setCaution("caution");
        mtQueryOrderDetail.setCityId(0L);
        mtQueryOrderDetail.setDeliveryTime(0L);
        mtQueryOrderDetail.setEPoiId("epoiId");
        when(mockMtUnOrderParser.fromMtQueryOrderDetail(mtQueryOrderDetail)).thenReturn(unOrder);

        // Run the test
        mtOrderJobUnderTest.queryNewOrdersByDevId();

        // Verify the results
        verify(mockMtAuthService).correctAuth("epoiId", 0, "errorCode");

        // Confirm UnOrderMqService.sendUnOrder(...).
        final UnOrder unorder = new UnOrder();
        unorder.setOrderStatus(0);
        unorder.setShopId(0L);
        unorder.setSubStoreId(0L);
        unorder.setShopName("shopName");
        unorder.setCbMsgType(0);
        verify(mockUnOrderMqService).sendUnOrder(unorder);
    }

    @Test
    public void testQueryNewOrdersByDevId_MtAuthServiceGetAuthReturnsNull() {
        // Setup
        when(mockMtAuthService.getAuth("epoiId", 0)).thenReturn(null);

        // Run the test
        mtOrderJobUnderTest.queryNewOrdersByDevId();

        // Verify the results
    }

    @Test
    public void testRecursion() {
        // Setup
        // Configure MtAuthService.getAuth(...).
        final MtAuthDO mtAuthDO = new MtAuthDO();
        mtAuthDO.setId(0);
        mtAuthDO.setGuid("55847f57-6c33-4225-9f87-d7be665ecabe");
        mtAuthDO.setEPoiId("ePoiId");
        mtAuthDO.setEnterpriseGuid("enterpriseGuid");
        mtAuthDO.setAccessToken("accessToken");
        when(mockMtAuthService.getAuth("epoiId", 0)).thenReturn(mtAuthDO);

        // Configure MtUnOrderParser.fromMtQueryOrderDetail(...).
        final UnOrder unOrder = new UnOrder();
        unOrder.setOrderStatus(0);
        unOrder.setShopId(0L);
        unOrder.setSubStoreId(0L);
        unOrder.setShopName("shopName");
        unOrder.setCbMsgType(0);
        final MtQueryOrderDetail mtQueryOrderDetail = new MtQueryOrderDetail();
        mtQueryOrderDetail.setCtime(0L);
        mtQueryOrderDetail.setCaution("caution");
        mtQueryOrderDetail.setCityId(0L);
        mtQueryOrderDetail.setDeliveryTime(0L);
        mtQueryOrderDetail.setEPoiId("epoiId");
        when(mockMtUnOrderParser.fromMtQueryOrderDetail(mtQueryOrderDetail)).thenReturn(unOrder);

        // Run the test
        mtOrderJobUnderTest.recursion(0L, 0);

        // Verify the results
        verify(mockMtAuthService).correctAuth("epoiId", 0, "errorCode");

        // Confirm UnOrderMqService.sendUnOrder(...).
        final UnOrder unorder = new UnOrder();
        unorder.setOrderStatus(0);
        unorder.setShopId(0L);
        unorder.setSubStoreId(0L);
        unorder.setShopName("shopName");
        unorder.setCbMsgType(0);
        verify(mockUnOrderMqService).sendUnOrder(unorder);
    }

    @Test
    public void testRecursion_MtAuthServiceGetAuthReturnsNull() {
        // Setup
        when(mockMtAuthService.getAuth("epoiId", 0)).thenReturn(null);

        // Run the test
        mtOrderJobUnderTest.recursion(0L, 0);

        // Verify the results
    }
}
