package com.holder.saas.store.takeaway.producers.controller;

import com.alibaba.fastjson.JSON;
import com.holder.saas.store.takeaway.producers.HolderSaasStoreTakeawayProducersApplication;
import com.holder.saas.store.takeaway.producers.controller.util.JsonFileUtil;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.takeaway.UnItemBatchUnbindReq;
import com.holderzone.saas.store.dto.takeaway.UnItemBindUnbindReq;
import com.holderzone.saas.store.dto.takeaway.UnItemQueryReq;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.io.UnsupportedEncodingException;

import static com.holderzone.saas.store.dto.common.CommonConstant.USER_INFO;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * <AUTHOR>
 * @date 2023/11/9
 * @description
 */
@Slf4j
@WebAppConfiguration
@ContextConfiguration
@AutoConfigureMockMvc
@RunWith(SpringRunner.class)
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
@SpringBootTest(classes = HolderSaasStoreTakeawayProducersApplication.class)
public class MappingControllerTest {

    private static final String USERINFO = "{\"operSubjectGuid\": \"2010121440477930009\"," +
            "\"enterpriseGuid\":" + " \"2009281531195930006\"," +
            "\"enterpriseName\": \"赵氏企业\"," +
            "\"enterpriseNo\": \"********\"," +
            "\"storeGuid\":" + " \"2106221850429620006\"," +
            "\"storeName\": \"交子大道测试门店\"," +
            "\"storeNo\": \"5796807\",\"userGuid\": " +
            "\"6787561298847596545\"," +
            "\"account\": \"196504\"," +
            "\"tel\": \"***********\"," +
            "\"name\": \"靓亮仔\"}\n";

    private static final String ITEM_MAPPING = "/item_mapping";

    private static final String RESPONSE = "response:";

    @Autowired
    private WebApplicationContext wac;

    private MockMvc mockMvc;

    @Before
    public void setupMockMvc() {
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();
    }

    @Test
    public void getType() throws UnsupportedEncodingException {
        MvcResult getTypeMvcResult = null;
        try {
            getTypeMvcResult = mockMvc.perform(get(ITEM_MAPPING + "/query_type/2106221850429620006/1")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        String contentAsString = getTypeMvcResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    @Test
    public void getItem() throws UnsupportedEncodingException {
        MvcResult getItemMvcResult = null;
        try {
            getItemMvcResult = mockMvc.perform(get(ITEM_MAPPING + "/query_item/2106221850429620006/2")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        String contentAsString = getItemMvcResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    @Test
    public void getItems() throws UnsupportedEncodingException {
        UnItemQueryReq getItemsReqDTO = JSON.parseObject(JsonFileUtil.read("item_mapping/getItems.json"),
                UnItemQueryReq.class);
        String getItemsJsonString = JSON.toJSONString(getItemsReqDTO);
        MvcResult getItemsMvcResult = null;
        try {
            getItemsMvcResult = mockMvc.perform(post(ITEM_MAPPING + "/query_items")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(getItemsJsonString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        String contentAsString = getItemsMvcResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    @Test
    public void bindItem() throws UnsupportedEncodingException {
        UnItemBindUnbindReq bindItemReqDTO = JSON.parseObject(JsonFileUtil.read("item_mapping/bindItem.json"),
                UnItemBindUnbindReq.class);
        String bindItemJsonString = JSON.toJSONString(bindItemReqDTO);
        MvcResult bindItemMvcResult = null;
        try {
            bindItemMvcResult = mockMvc.perform(post(ITEM_MAPPING + "/bind")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(bindItemJsonString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        String contentAsString = bindItemMvcResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    @Test
    public void unbindItem() throws UnsupportedEncodingException {
        UnItemBindUnbindReq unbindItemReqDTO = JSON.parseObject(JsonFileUtil.read("item_mapping/unbindItem.json"),
                UnItemBindUnbindReq.class);
        String unbindItemJsonString = JSON.toJSONString(unbindItemReqDTO);
        MvcResult unbindItemMvcResult = null;
        try {
            unbindItemMvcResult = mockMvc.perform(post(ITEM_MAPPING + "/unbind")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(unbindItemJsonString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        String contentAsString = unbindItemMvcResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    /**
     * 批量解绑
     * 性能测试
     */
    @Test
    public void batchUnbindItem() throws UnsupportedEncodingException {
        UnItemBatchUnbindReq batchUnbindItemReqDTO = JSON.parseObject(JsonFileUtil.read("item_mapping/batchUnbindItem.json"),
                UnItemBatchUnbindReq.class);
        String batchUnbindItemJsonString = JSON.toJSONString(batchUnbindItemReqDTO);
        MvcResult batchUnbindItemMvcResult = null;
        try {
            batchUnbindItemMvcResult = mockMvc.perform(post(ITEM_MAPPING + "/batch_unbind")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(batchUnbindItemJsonString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        String contentAsString = batchUnbindItemMvcResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    /**
     * 批量绑定
     * 性能测试
     */
    @Test
    public void batchBindItem() throws UnsupportedEncodingException {
        UnItemBatchUnbindReq batchBindItemReqDTO = JSON.parseObject(JsonFileUtil.read("item_mapping/batchBindItem.json"),
                UnItemBatchUnbindReq.class);
        String batchBindItemJsonString = JSON.toJSONString(batchBindItemReqDTO);
        MvcResult batchBindItemMvcResult = null;
        try {
            batchBindItemMvcResult = mockMvc.perform(post(ITEM_MAPPING + "/batch_bind")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(batchBindItemJsonString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        String contentAsString = batchBindItemMvcResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }
}