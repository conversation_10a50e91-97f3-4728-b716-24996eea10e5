package com.holder.saas.store.takeaway.producers.service.impl;

import com.holder.saas.store.takeaway.producers.service.rpc.ConsumersFeignService;
import com.holderzone.saas.store.dto.takeaway.SendOrderCallReq;
import com.holderzone.saas.store.dto.takeaway.request.OrderCallDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.LocalDateTime;

import static org.mockito.Mockito.verify;

@RunWith(MockitoJUnitRunner.class)
public class DianXinServiceImplTest {

    @Mock
    private ConsumersFeignService mockConsumersFeignService;

    private DianXinServiceImpl dianXinServiceImplUnderTest;

    @Before
    public void setUp() {
        dianXinServiceImplUnderTest = new DianXinServiceImpl(mockConsumersFeignService);
        ReflectionTestUtils.setField(dianXinServiceImplUnderTest, "DIAN_XIN_TOKEN_URL", "DIAN_XIN_TOKEN_URL");
        ReflectionTestUtils.setField(dianXinServiceImplUnderTest, "DIAN_XIN_CALL_URL", "DIAN_XIN_CALL_URL");
        ReflectionTestUtils.setField(dianXinServiceImplUnderTest, "APPID", "APPID");
        ReflectionTestUtils.setField(dianXinServiceImplUnderTest, "SECRET", "SECRET");
        ReflectionTestUtils.setField(dianXinServiceImplUnderTest, "TASK_ID", "TASK_ID");
        ReflectionTestUtils.setField(dianXinServiceImplUnderTest, "PROJECT_ID", "PROJECT_ID");
    }

    @Test
    public void testOutbound() {
        // Setup
        final SendOrderCallReq sendOrderCallReq = new SendOrderCallReq();
        sendOrderCallReq.setLongTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        sendOrderCallReq.setOrderGuid("orderGuid");
        sendOrderCallReq.setPhone("phone");
        sendOrderCallReq.setBatch("batch");
        sendOrderCallReq.setEnterpriseGuid("enterpriseGuid");

        // Run the test
        dianXinServiceImplUnderTest.outbound(sendOrderCallReq);

        // Verify the results
        // Confirm ConsumersFeignService.updateCallOrder(...).
        final OrderCallDTO orderCallDTO = new OrderCallDTO();
        orderCallDTO.setId(0L);
        orderCallDTO.setGuid("850b9a9c-ac03-402d-9e82-b5cbf9b19332");
        orderCallDTO.setCallStatus(0);
        orderCallDTO.setMessage("Success");
        orderCallDTO.setEnterpriseGuid("enterpriseGuid");
        verify(mockConsumersFeignService).updateCallOrder(orderCallDTO);
    }
}
