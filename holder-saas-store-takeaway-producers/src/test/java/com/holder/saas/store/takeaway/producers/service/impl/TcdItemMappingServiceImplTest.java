package com.holder.saas.store.takeaway.producers.service.impl;

import com.google.common.util.concurrent.MoreExecutors;
import com.holder.saas.store.takeaway.producers.entity.domain.TcdAuthDO;
import com.holder.saas.store.takeaway.producers.service.TcdAuthService;
import com.holderzone.saas.store.dto.takeaway.*;
import com.holderzone.saas.store.dto.takeaway.request.TCDItemBindingReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.TcdDish;
import com.holderzone.saas.store.dto.takeaway.response.TcdDishSku;
import com.holderzone.saas.store.dto.takeaway.response.TcdItemMappingRespDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class TcdItemMappingServiceImplTest {

    @Mock
    private TcdAuthService mockTcdAuthService;

    @InjectMocks
    private TcdItemMappingServiceImpl tcdItemMappingServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        ReflectionTestUtils.setField(tcdItemMappingServiceImplUnderTest, "tcdProductQueryThreadPool",
                MoreExecutors.newDirectExecutorService());
    }

    @Test
    public void testGetType() {
        // Setup
        final UnMappedType unMappedType = new UnMappedType();
        unMappedType.setUnItemTypeId("unItemTypeId");
        unMappedType.setUnItemTypeName("name");
        final UnMappedItem unMappedItem = new UnMappedItem();
        unMappedItem.setUnItemId("unItemId");
        unMappedItem.setUnItemName("name");
        unMappedType.setUnItemList(Arrays.asList(unMappedItem));
        final List<UnMappedType> expectedResult = Arrays.asList(unMappedType);
        when(mockTcdAuthService.checkToken("storeGuid")).thenReturn("token");

        // Configure TcdAuthService.getType(...).
        final TcdItemMappingRespDTO tcdItemMappingRespDTO = new TcdItemMappingRespDTO();
        tcdItemMappingRespDTO.setId(0L);
        tcdItemMappingRespDTO.setName("name");
        final TcdDish tcdDish = new TcdDish();
        tcdDish.setId(0L);
        tcdDish.setName("name");
        final TcdDishSku tcdDishSku = new TcdDishSku();
        tcdDishSku.setId(0L);
        tcdDishSku.setDishId("dishId");
        tcdDishSku.setHolderDishSkuId("erpItemSkuId");
        tcdDishSku.setHolderActualDishSkuId("actualErpItemSkuId");
        tcdDishSku.setHolderDishId("erpItemGuid");
        tcdDishSku.setSpec("");
        tcdDishSku.setPrice(0.0f);
        tcdDish.setSkus(Arrays.asList(tcdDishSku));
        tcdItemMappingRespDTO.setDishes(Arrays.asList(tcdDish));
        final List<TcdItemMappingRespDTO> tcdItemMappingRespDTOS = Arrays.asList(tcdItemMappingRespDTO);
        when(mockTcdAuthService.getType("token")).thenReturn(tcdItemMappingRespDTOS);

        // Run the test
        final List<UnMappedType> result = tcdItemMappingServiceImplUnderTest.getType("storeGuid");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetType_TcdAuthServiceGetTypeReturnsNoItems() {
        // Setup
        when(mockTcdAuthService.checkToken("storeGuid")).thenReturn("token");
        when(mockTcdAuthService.getType("token")).thenReturn(Collections.emptyList());

        // Run the test
        final List<UnMappedType> result = tcdItemMappingServiceImplUnderTest.getType("storeGuid");

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testGetItem() {
        // Setup
        final UnMappedItem unMappedItem = new UnMappedItem();
        unMappedItem.setUnItemId("unItemId");
        unMappedItem.setUnItemName("name");
        unMappedItem.setUnItemSkuId("unItemSkuId");
        unMappedItem.setUnItemSkuName("");
        unMappedItem.setUnItemNameWithSku("name");
        unMappedItem.setUnItemTypeId("unItemTypeId");
        unMappedItem.setUnItemTypeName("name");
        unMappedItem.setErpItemId("");
        unMappedItem.setErpItemSkuId("erpItemSkuId");
        unMappedItem.setErpStoreGuid("storeGuid");
        unMappedItem.setExtendValue("extendValue");
        final List<UnMappedItem> expectedResult = Arrays.asList(unMappedItem);
        when(mockTcdAuthService.checkToken("storeGuid")).thenReturn("token");

        // Configure TcdAuthService.getItem(...).
        final TcdItemMappingRespDTO tcdItemMappingRespDTO = new TcdItemMappingRespDTO();
        tcdItemMappingRespDTO.setId(0L);
        tcdItemMappingRespDTO.setName("name");
        final TcdDish tcdDish = new TcdDish();
        tcdDish.setId(0L);
        tcdDish.setName("name");
        final TcdDishSku tcdDishSku = new TcdDishSku();
        tcdDishSku.setId(0L);
        tcdDishSku.setDishId("dishId");
        tcdDishSku.setHolderDishSkuId("erpItemSkuId");
        tcdDishSku.setHolderActualDishSkuId("actualErpItemSkuId");
        tcdDishSku.setHolderDishId("erpItemGuid");
        tcdDishSku.setSpec("");
        tcdDishSku.setPrice(0.0f);
        tcdDish.setSkus(Arrays.asList(tcdDishSku));
        tcdItemMappingRespDTO.setDishes(Arrays.asList(tcdDish));
        final List<TcdItemMappingRespDTO> tcdItemMappingRespDTOS = Arrays.asList(tcdItemMappingRespDTO);
        when(mockTcdAuthService.getItem("token")).thenReturn(tcdItemMappingRespDTOS);

        // Run the test
        final List<UnMappedItem> result = tcdItemMappingServiceImplUnderTest.getItem("storeGuid");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetItem_TcdAuthServiceGetItemReturnsNoItems() {
        // Setup
        when(mockTcdAuthService.checkToken("storeGuid")).thenReturn("token");
        when(mockTcdAuthService.getItem("token")).thenReturn(Collections.emptyList());

        // Run the test
        final List<UnMappedItem> result = tcdItemMappingServiceImplUnderTest.getItem("storeGuid");

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testGetItems() {
        // Setup
        final UnItemQueryReq unItemQueryReq = new UnItemQueryReq();
        unItemQueryReq.setKeywords("keywords");
        unItemQueryReq.setStoreGuids(Arrays.asList("value"));
        unItemQueryReq.setTakeoutType(0);
        unItemQueryReq.setBindingFlag(0);

        final UnMappedItem unMappedItem = new UnMappedItem();
        unMappedItem.setUnItemId("unItemId");
        unMappedItem.setUnItemName("name");
        unMappedItem.setUnItemSkuId("unItemSkuId");
        unMappedItem.setUnItemSkuName("");
        unMappedItem.setUnItemNameWithSku("name");
        unMappedItem.setUnItemTypeId("unItemTypeId");
        unMappedItem.setUnItemTypeName("name");
        unMappedItem.setErpItemId("");
        unMappedItem.setErpItemSkuId("erpItemSkuId");
        unMappedItem.setErpStoreGuid("storeGuid");
        unMappedItem.setExtendValue("extendValue");
        final List<UnMappedItem> expectedResult = Arrays.asList(unMappedItem);

        // Configure TcdAuthService.getTokens(...).
        final TcdAuthDO tcdAuthDO = new TcdAuthDO();
        tcdAuthDO.setId(0L);
        tcdAuthDO.setGuid("3eab11d6-32c0-4605-ad78-b06b6f5baa00");
        tcdAuthDO.setEnterpriseGuid("enterpriseGuid");
        tcdAuthDO.setStoreGuid("storeGuid");
        tcdAuthDO.setAccessToken("authToken");
        final List<TcdAuthDO> tcdAuthDOS = Arrays.asList(tcdAuthDO);
        when(mockTcdAuthService.getTokens(Arrays.asList("value"))).thenReturn(tcdAuthDOS);

        // Configure TcdAuthService.getItem(...).
        final TcdItemMappingRespDTO tcdItemMappingRespDTO = new TcdItemMappingRespDTO();
        tcdItemMappingRespDTO.setId(0L);
        tcdItemMappingRespDTO.setName("name");
        final TcdDish tcdDish = new TcdDish();
        tcdDish.setId(0L);
        tcdDish.setName("name");
        final TcdDishSku tcdDishSku = new TcdDishSku();
        tcdDishSku.setId(0L);
        tcdDishSku.setDishId("dishId");
        tcdDishSku.setHolderDishSkuId("erpItemSkuId");
        tcdDishSku.setHolderActualDishSkuId("actualErpItemSkuId");
        tcdDishSku.setHolderDishId("erpItemGuid");
        tcdDishSku.setSpec("");
        tcdDishSku.setPrice(0.0f);
        tcdDish.setSkus(Arrays.asList(tcdDishSku));
        tcdItemMappingRespDTO.setDishes(Arrays.asList(tcdDish));
        final List<TcdItemMappingRespDTO> tcdItemMappingRespDTOS = Arrays.asList(tcdItemMappingRespDTO);
        when(mockTcdAuthService.getItem("authToken")).thenReturn(tcdItemMappingRespDTOS);

        // Run the test
        final List<UnMappedItem> result = tcdItemMappingServiceImplUnderTest.getItems(unItemQueryReq);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetItems_TcdAuthServiceGetTokensReturnsNoItems() {
        // Setup
        final UnItemQueryReq unItemQueryReq = new UnItemQueryReq();
        unItemQueryReq.setKeywords("keywords");
        unItemQueryReq.setStoreGuids(Arrays.asList("value"));
        unItemQueryReq.setTakeoutType(0);
        unItemQueryReq.setBindingFlag(0);

        when(mockTcdAuthService.getTokens(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        final List<UnMappedItem> result = tcdItemMappingServiceImplUnderTest.getItems(unItemQueryReq);

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testGetItems_TcdAuthServiceGetItemReturnsNoItems() {
        // Setup
        final UnItemQueryReq unItemQueryReq = new UnItemQueryReq();
        unItemQueryReq.setKeywords("keywords");
        unItemQueryReq.setStoreGuids(Arrays.asList("value"));
        unItemQueryReq.setTakeoutType(0);
        unItemQueryReq.setBindingFlag(0);

        final UnMappedItem unMappedItem = new UnMappedItem();
        unMappedItem.setUnItemId("unItemId");
        unMappedItem.setUnItemName("name");
        unMappedItem.setUnItemSkuId("unItemSkuId");
        unMappedItem.setUnItemSkuName("");
        unMappedItem.setUnItemNameWithSku("name");
        unMappedItem.setUnItemTypeId("unItemTypeId");
        unMappedItem.setUnItemTypeName("name");
        unMappedItem.setErpItemId("");
        unMappedItem.setErpItemSkuId("erpItemSkuId");
        unMappedItem.setErpStoreGuid("storeGuid");
        unMappedItem.setExtendValue("extendValue");
        final List<UnMappedItem> expectedResult = Arrays.asList(unMappedItem);

        // Configure TcdAuthService.getTokens(...).
        final TcdAuthDO tcdAuthDO = new TcdAuthDO();
        tcdAuthDO.setId(0L);
        tcdAuthDO.setGuid("3eab11d6-32c0-4605-ad78-b06b6f5baa00");
        tcdAuthDO.setEnterpriseGuid("enterpriseGuid");
        tcdAuthDO.setStoreGuid("storeGuid");
        tcdAuthDO.setAccessToken("authToken");
        final List<TcdAuthDO> tcdAuthDOS = Arrays.asList(tcdAuthDO);
        when(mockTcdAuthService.getTokens(Arrays.asList("value"))).thenReturn(tcdAuthDOS);

        when(mockTcdAuthService.getItem("authToken")).thenReturn(Collections.emptyList());

        // Run the test
        final List<UnMappedItem> result = tcdItemMappingServiceImplUnderTest.getItems(unItemQueryReq);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testChangeData() {
        // Setup
        final TcdItemMappingRespDTO tcdItemMappingRespDTO = new TcdItemMappingRespDTO();
        tcdItemMappingRespDTO.setId(0L);
        tcdItemMappingRespDTO.setName("name");
        final TcdDish tcdDish = new TcdDish();
        tcdDish.setId(0L);
        tcdDish.setName("name");
        final TcdDishSku tcdDishSku = new TcdDishSku();
        tcdDishSku.setId(0L);
        tcdDishSku.setDishId("dishId");
        tcdDishSku.setHolderDishSkuId("erpItemSkuId");
        tcdDishSku.setHolderActualDishSkuId("actualErpItemSkuId");
        tcdDishSku.setHolderDishId("erpItemGuid");
        tcdDishSku.setSpec("");
        tcdDishSku.setPrice(0.0f);
        tcdDish.setSkus(Arrays.asList(tcdDishSku));
        tcdItemMappingRespDTO.setDishes(Arrays.asList(tcdDish));
        final List<TcdItemMappingRespDTO> list = Arrays.asList(tcdItemMappingRespDTO);
        final UnMappedItem unMappedItem = new UnMappedItem();
        unMappedItem.setUnItemId("unItemId");
        unMappedItem.setUnItemName("name");
        unMappedItem.setUnItemSkuId("unItemSkuId");
        unMappedItem.setUnItemSkuName("");
        unMappedItem.setUnItemNameWithSku("name");
        unMappedItem.setUnItemTypeId("unItemTypeId");
        unMappedItem.setUnItemTypeName("name");
        unMappedItem.setErpItemId("");
        unMappedItem.setErpItemSkuId("erpItemSkuId");
        unMappedItem.setErpStoreGuid("storeGuid");
        unMappedItem.setExtendValue("extendValue");
        final List<UnMappedItem> expectedResult = Arrays.asList(unMappedItem);

        // Run the test
        final List<UnMappedItem> result = tcdItemMappingServiceImplUnderTest.changeData(list);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testBindMapping() {
        // Setup
        final UnItemBindUnbindReq unItemBindUnbindReq = new UnItemBindUnbindReq();
        unItemBindUnbindReq.setUnItemId("dishId");
        unItemBindUnbindReq.setUnItemSkuId("unItemSkuId");
        unItemBindUnbindReq.setErpItemGuid("erpItemGuid");
        unItemBindUnbindReq.setErpItemSkuId("erpItemSkuId");
        unItemBindUnbindReq.setActualErpItemSkuId("actualErpItemSkuId");
        unItemBindUnbindReq.setStoreGuid("storeGuid");

        when(mockTcdAuthService.getToken("storeGuid")).thenReturn("token");

        // Configure TcdAuthService.doTcdItemBinding(...).
        final TCDItemBindingReqDTO TCDItemBindingReqDTO = new TCDItemBindingReqDTO();
        TCDItemBindingReqDTO.setToken("token");
        TCDItemBindingReqDTO.setOperateType(0);
        final TcdDishSku tcdDishSku = new TcdDishSku();
        tcdDishSku.setId(0L);
        tcdDishSku.setDishId("dishId");
        tcdDishSku.setHolderDishSkuId("erpItemSkuId");
        tcdDishSku.setHolderActualDishSkuId("actualErpItemSkuId");
        tcdDishSku.setHolderDishId("erpItemGuid");
        tcdDishSku.setSpec("");
        tcdDishSku.setPrice(0.0f);
        TCDItemBindingReqDTO.setDishSkus(Arrays.asList(tcdDishSku));
        when(mockTcdAuthService.doTcdItemBinding(TCDItemBindingReqDTO)).thenReturn("result");

        // Run the test
        tcdItemMappingServiceImplUnderTest.bindMapping(unItemBindUnbindReq);

        // Verify the results
    }

    @Test
    public void testUnbindMapping() {
        // Setup
        final UnItemBindUnbindReq unItemBindUnbindReq = new UnItemBindUnbindReq();
        unItemBindUnbindReq.setUnItemId("dishId");
        unItemBindUnbindReq.setUnItemSkuId("unItemSkuId");
        unItemBindUnbindReq.setErpItemGuid("erpItemGuid");
        unItemBindUnbindReq.setErpItemSkuId("erpItemSkuId");
        unItemBindUnbindReq.setActualErpItemSkuId("actualErpItemSkuId");
        unItemBindUnbindReq.setStoreGuid("storeGuid");

        when(mockTcdAuthService.getToken("storeGuid")).thenReturn("token");

        // Configure TcdAuthService.doTcdItemBinding(...).
        final TCDItemBindingReqDTO TCDItemBindingReqDTO = new TCDItemBindingReqDTO();
        TCDItemBindingReqDTO.setToken("token");
        TCDItemBindingReqDTO.setOperateType(0);
        final TcdDishSku tcdDishSku = new TcdDishSku();
        tcdDishSku.setId(0L);
        tcdDishSku.setDishId("dishId");
        tcdDishSku.setHolderDishSkuId("erpItemSkuId");
        tcdDishSku.setHolderActualDishSkuId("actualErpItemSkuId");
        tcdDishSku.setHolderDishId("erpItemGuid");
        tcdDishSku.setSpec("");
        tcdDishSku.setPrice(0.0f);
        TCDItemBindingReqDTO.setDishSkus(Arrays.asList(tcdDishSku));
        when(mockTcdAuthService.doTcdItemBinding(TCDItemBindingReqDTO)).thenReturn("result");

        // Run the test
        tcdItemMappingServiceImplUnderTest.unbindMapping(unItemBindUnbindReq);

        // Verify the results
    }

    @Test
    public void testBatchUnbindMapping() {
        // Setup
        final UnItemBatchUnbindReq unItemBatchUnbindReq = new UnItemBatchUnbindReq();
        unItemBatchUnbindReq.setStoreGuid("storeGuid");
        final UnItemBaseMapReq unItemBaseMapReq = new UnItemBaseMapReq();
        unItemBaseMapReq.setUnItemId("dishId");
        unItemBaseMapReq.setUnItemSkuId("unItemSkuId");
        unItemBaseMapReq.setErpItemGuid("erpItemGuid");
        unItemBaseMapReq.setErpItemSkuId("erpItemSkuId");
        unItemBaseMapReq.setActualErpItemSkuId("actualErpItemSkuId");
        unItemBatchUnbindReq.setUnItemUnbindList(Arrays.asList(unItemBaseMapReq));
        unItemBatchUnbindReq.setBindFlag(false);

        when(mockTcdAuthService.getToken("storeGuid")).thenReturn("token");

        // Configure TcdAuthService.doTcdItemBinding(...).
        final TCDItemBindingReqDTO TCDItemBindingReqDTO = new TCDItemBindingReqDTO();
        TCDItemBindingReqDTO.setToken("token");
        TCDItemBindingReqDTO.setOperateType(0);
        final TcdDishSku tcdDishSku = new TcdDishSku();
        tcdDishSku.setId(0L);
        tcdDishSku.setDishId("dishId");
        tcdDishSku.setHolderDishSkuId("erpItemSkuId");
        tcdDishSku.setHolderActualDishSkuId("actualErpItemSkuId");
        tcdDishSku.setHolderDishId("erpItemGuid");
        tcdDishSku.setSpec("");
        tcdDishSku.setPrice(0.0f);
        TCDItemBindingReqDTO.setDishSkus(Arrays.asList(tcdDishSku));
        when(mockTcdAuthService.doTcdItemBinding(TCDItemBindingReqDTO)).thenReturn("result");

        // Run the test
        tcdItemMappingServiceImplUnderTest.batchUnbindMapping(unItemBatchUnbindReq);

        // Verify the results
    }
}
