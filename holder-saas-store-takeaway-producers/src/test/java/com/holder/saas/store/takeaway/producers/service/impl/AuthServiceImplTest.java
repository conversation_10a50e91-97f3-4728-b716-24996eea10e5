package com.holder.saas.store.takeaway.producers.service.impl;

import com.holder.saas.store.takeaway.producers.entity.domain.MtAuthDO;
import com.holder.saas.store.takeaway.producers.entity.domain.TcdAuthDO;
import com.holder.saas.store.takeaway.producers.service.EleAuthService;
import com.holder.saas.store.takeaway.producers.service.JdStoreMappingService;
import com.holder.saas.store.takeaway.producers.service.MtAuthService;
import com.holder.saas.store.takeaway.producers.service.TcdAuthService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class AuthServiceImplTest {

    @Mock
    private MtAuthService mockMtAuthService;
    @Mock
    private EleAuthService mockEleAuthService;
    @Mock
    private TcdAuthService mockTcdAuthService;

    private AuthServiceImpl authServiceImplUnderTest;

    private JdStoreMappingService jdStoreMappingService;

    @Before
    public void setUp() {
        authServiceImplUnderTest = new AuthServiceImpl(mockMtAuthService, mockEleAuthService, mockTcdAuthService,jdStoreMappingService);
    }

    @Test
    public void testAuthStoreGuids() {
        // Setup
        // Configure MtAuthService.getAuths(...).
        final MtAuthDO mtAuthDO = new MtAuthDO();
        mtAuthDO.setId(0);
        mtAuthDO.setGuid("1fb4d964-ac2a-4a5f-b003-a304e5064f68");
        mtAuthDO.setEPoiId("ePoiId");
        mtAuthDO.setEnterpriseGuid("enterpriseGuid");
        mtAuthDO.setMtStoreGuid("mtStoreGuid");
        final List<MtAuthDO> mtAuthDOS = Arrays.asList(mtAuthDO);
        when(mockMtAuthService.getAuths(Arrays.asList("value"), 0)).thenReturn(mtAuthDOS);

        when(mockEleAuthService.getTokens(Arrays.asList("value"))).thenReturn(new HashMap<>());

        // Configure TcdAuthService.getTokens(...).
        final TcdAuthDO tcdAuthDO = new TcdAuthDO();
        tcdAuthDO.setId(0L);
        tcdAuthDO.setGuid("c18de7a9-2745-4f7b-8c85-d6850fa60fe9");
        tcdAuthDO.setEnterpriseGuid("enterpriseGuid");
        tcdAuthDO.setStoreGuid("storeGuid");
        tcdAuthDO.setDeliveryType(0);
        final List<TcdAuthDO> tcdAuthDOS = Arrays.asList(tcdAuthDO);
        when(mockTcdAuthService.getTokens(Arrays.asList("value"))).thenReturn(tcdAuthDOS);

        // Run the test
        final List<String> result = authServiceImplUnderTest.authStoreGuids(Arrays.asList("value"), 0);

        // Verify the results
        assertEquals(Arrays.asList("value"), result);
    }

    @Test
    public void testAuthStoreGuids_MtAuthServiceReturnsNoItems() {
        // Setup
        when(mockMtAuthService.getAuths(Arrays.asList("value"), 0)).thenReturn(Collections.emptyList());

        // Run the test
        final List<String> result = authServiceImplUnderTest.authStoreGuids(Arrays.asList("value"), 0);

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testAuthStoreGuids_TcdAuthServiceReturnsNoItems() {
        // Setup
        when(mockTcdAuthService.getTokens(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        final List<String> result = authServiceImplUnderTest.authStoreGuids(Arrays.asList("value"), 0);

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }
}
