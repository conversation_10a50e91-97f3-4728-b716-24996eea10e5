package com.holder.saas.store.takeaway.producers.service.converter;

import com.holder.saas.store.takeaway.producers.entity.domain.MtAuthDO;
import com.holder.saas.store.takeaway.producers.entity.dto.MtMemberParamsDTO;
import com.holderzone.saas.store.dto.member.PlatformMemberDTO;
import org.junit.Test;

import static org.junit.Assert.assertEquals;

public class PlatformMemberConverterTest {

    @Test
    public void testFromMtMemberParamsAndAuth() {
        // Setup
        final MtMemberParamsDTO mtMemberParams = new MtMemberParamsDTO();
        mtMemberParams.setPhoneNo("phone");
        mtMemberParams.setPlatformType("platformType");

        final MtAuthDO auth = new MtAuthDO();
        auth.setId(0);
        auth.setGuid("bd97c6d5-b86e-4b32-9a71-7b67808c262d");
        auth.setEPoiId("ePoiId");
        auth.setEnterpriseGuid("enterpriseGuid");
        auth.setMtStoreGuid("mtStoreGuid");

        final PlatformMemberDTO expectedResult = new PlatformMemberDTO();
        expectedResult.setPhone("phone");
        expectedResult.setSourceName("platformType");
        expectedResult.setEnterpriseGuid("enterpriseGuid");
        expectedResult.setOperSubjectGuid("ePoiId");
        expectedResult.setType(0);

        // Run the test
        final PlatformMemberDTO result = PlatformMemberConverter.fromMtMemberParamsAndAuth(mtMemberParams, auth);

        // Verify the results
        assertEquals(expectedResult, result);
    }
}
