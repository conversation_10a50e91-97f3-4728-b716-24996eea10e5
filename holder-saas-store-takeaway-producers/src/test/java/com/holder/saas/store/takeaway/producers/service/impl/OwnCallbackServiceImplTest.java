package com.holder.saas.store.takeaway.producers.service.impl;

import com.holder.saas.store.takeaway.producers.service.OwnUnOrderParser;
import com.holder.saas.store.takeaway.producers.service.UnOrderMqService;
import com.holderzone.saas.store.dto.takeaway.UnOrder;
import com.holderzone.saas.store.dto.takeaway.request.SalesOrderDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class OwnCallbackServiceImplTest {

    @Mock
    private UnOrderMqService mockUnOrderMqService;
    @Mock
    private OwnUnOrderParser mockOwnUnOrderParse;

    private OwnCallbackServiceImpl ownCallbackServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        ownCallbackServiceImplUnderTest = new OwnCallbackServiceImpl(mockUnOrderMqService, mockOwnUnOrderParse);
    }

    @Test
    public void testOrderCallback() {
        // Setup
        final SalesOrderDTO salesOrderDTO = new SalesOrderDTO();
        salesOrderDTO.setUserName("UserName");
        salesOrderDTO.setStoreId(0);
        salesOrderDTO.setOrderId(0L);
        salesOrderDTO.setStoreGuid("StoreGuid");
        salesOrderDTO.setOrderStatus(0);

        // Configure OwnUnOrderParser.fromOrderCreated(...).
        final UnOrder unOrder = new UnOrder();
        unOrder.setCbMsgType(0);
        unOrder.setOrderType(0);
        unOrder.setOrderSubType(0);
        unOrder.setEnterpriseGuid("enterpriseGuid");
        unOrder.setStoreGuid("storeGuid");
        final SalesOrderDTO salesOrderDTO1 = new SalesOrderDTO();
        salesOrderDTO1.setUserName("UserName");
        salesOrderDTO1.setStoreId(0);
        salesOrderDTO1.setOrderId(0L);
        salesOrderDTO1.setStoreGuid("StoreGuid");
        salesOrderDTO1.setOrderStatus(0);
        when(mockOwnUnOrderParse.fromOrderCreated(salesOrderDTO1)).thenReturn(unOrder);

        // Run the test
        ownCallbackServiceImplUnderTest.orderCallback(salesOrderDTO);

        // Verify the results
        // Confirm UnOrderMqService.sendUnOrder(...).
        final UnOrder unorder = new UnOrder();
        unorder.setCbMsgType(0);
        unorder.setOrderType(0);
        unorder.setOrderSubType(0);
        unorder.setEnterpriseGuid("enterpriseGuid");
        unorder.setStoreGuid("storeGuid");
        verify(mockUnOrderMqService).sendUnOrder(unorder);
    }
}
