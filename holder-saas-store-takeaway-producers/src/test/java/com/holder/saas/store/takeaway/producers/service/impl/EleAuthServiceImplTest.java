package com.holder.saas.store.takeaway.producers.service.impl;

import com.holder.saas.store.takeaway.producers.entity.domain.EleAuthDO;
import com.holder.saas.store.takeaway.producers.mapper.EleAuthMapper;
import com.holder.saas.store.takeaway.producers.service.DistributedService;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.takeaway.EleCallbackBindDTO;
import com.holderzone.saas.store.dto.takeaway.UnOrder;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutShopBindReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.StoreAuthDTO;
import com.holderzone.saas.store.dto.takeaway.response.TakeoutShopBindRespDTO;
import eleme.openapi.sdk.api.entity.other.OMessage;
import eleme.openapi.sdk.api.entity.packs.ShopContract;
import eleme.openapi.sdk.api.exception.ServiceException;
import eleme.openapi.sdk.config.Config;
import eleme.openapi.sdk.oauth.OAuthClient;
import eleme.openapi.sdk.oauth.response.Token;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.util.Pair;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class EleAuthServiceImplTest {

    @Mock
    private Config mockConfig;
    @Mock
    private OAuthClient mockOAuthClient;
    @Mock
    private DistributedService mockDistributedService;
    @Mock
    private EleAuthMapper mockEleAuthMapper;

    private EleAuthServiceImpl eleAuthServiceImplUnderTest;

    @Before
    public void setUp() {
        eleAuthServiceImplUnderTest = new EleAuthServiceImpl(mockConfig, mockOAuthClient, mockDistributedService);
        ReflectionTestUtils.setField(eleAuthServiceImplUnderTest, "bindingUrl", "bindingUrl");
        ReflectionTestUtils.setField(eleAuthServiceImplUnderTest, "redirectUrl", "redirectUrl");
        ReflectionTestUtils.setField(eleAuthServiceImplUnderTest, "eleClientKey", "eleClientKey");
        ReflectionTestUtils.setField(eleAuthServiceImplUnderTest, "tokenRefreshAheadHours", 0);
        ReflectionTestUtils.setField(eleAuthServiceImplUnderTest, "refreshTokenValidityPeriodDays", 0);
        ReflectionTestUtils.setField(eleAuthServiceImplUnderTest, "eleAuthMapper", mockEleAuthMapper);
    }

    @Test
    public void testTakeOutBindingUrl() {
        // Setup
        final TakeoutShopBindReqDTO takeoutShopBindReqDTO = new TakeoutShopBindReqDTO();
        takeoutShopBindReqDTO.setTakeoutType(0);
        takeoutShopBindReqDTO.setBindingStatus(0);

        final TakeoutShopBindRespDTO expectedResult = new TakeoutShopBindRespDTO();
        expectedResult.setUrl("url");

        // Run the test
        final TakeoutShopBindRespDTO result = eleAuthServiceImplUnderTest.takeOutBindingUrl(takeoutShopBindReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testBindCallback() {
        // Setup
        final EleCallbackBindDTO eleCallbackBindDTO = new EleCallbackBindDTO();
        eleCallbackBindDTO.setCode("autoCode");
        eleCallbackBindDTO.setState("state");
        eleCallbackBindDTO.setError("error");
        eleCallbackBindDTO.setErrorDescription("errorDescription");

        // Configure OAuthClient.getTokenByCode(...).
        final Token token = new Token();
        token.setError("error");
        token.setError_description("error_description");
        token.setAccessToken("accessToken");
        token.setTokenType("tokenType");
        token.setExpires(0L);
        token.setRefreshToken("refreshToken");
        when(mockOAuthClient.getTokenByCode("autoCode", "redirectUrl")).thenReturn(token);

        when(mockDistributedService.nextEleGuid()).thenReturn("ae0f4c3c-013f-4fa8-a035-ed677df47a1d");

        // Configure EleAuthMapper.eleAuthList(...).
        final EleAuthDO eleAuthDO = new EleAuthDO();
        eleAuthDO.setId(0L);
        eleAuthDO.setGuid("ae0f4c3c-013f-4fa8-a035-ed677df47a1d");
        eleAuthDO.setEnterpriseGuid("enterpriseGuid");
        eleAuthDO.setStoreGuid("storeGuid");
        eleAuthDO.setUserId(0L);
        eleAuthDO.setUserName("userName");
        eleAuthDO.setShopId(0L);
        eleAuthDO.setShopName("name");
        eleAuthDO.setDeliveryType(0);
        eleAuthDO.setAccessToken("accessToken");
        eleAuthDO.setRefreshToken("refreshToken");
        eleAuthDO.setTokenType("tokenType");
        eleAuthDO.setActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        eleAuthDO.setExpires(0L);
        eleAuthDO.setExpireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        eleAuthDO.setRefreshActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        eleAuthDO.setRefreshExpires(0L);
        eleAuthDO.setRefreshExpireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        eleAuthDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        eleAuthDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<EleAuthDO> eleAuthDOS = Arrays.asList(eleAuthDO);
        when(mockEleAuthMapper.eleAuthList(0L)).thenReturn(eleAuthDOS);

        // Run the test
        eleAuthServiceImplUnderTest.bindCallback(eleCallbackBindDTO);

        // Verify the results
    }

    @Test
    public void testBindCallback_OAuthClientReturnsNull() {
        // Setup
        final EleCallbackBindDTO eleCallbackBindDTO = new EleCallbackBindDTO();
        eleCallbackBindDTO.setCode("autoCode");
        eleCallbackBindDTO.setState("state");
        eleCallbackBindDTO.setError("error");
        eleCallbackBindDTO.setErrorDescription("errorDescription");

        when(mockOAuthClient.getTokenByCode("autoCode", "redirectUrl")).thenReturn(null);

        // Run the test
        eleAuthServiceImplUnderTest.bindCallback(eleCallbackBindDTO);

        // Verify the results
    }

    @Test
    public void testBindCallback_EleAuthMapperReturnsNoItems() {
        // Setup
        final EleCallbackBindDTO eleCallbackBindDTO = new EleCallbackBindDTO();
        eleCallbackBindDTO.setCode("autoCode");
        eleCallbackBindDTO.setState("state");
        eleCallbackBindDTO.setError("error");
        eleCallbackBindDTO.setErrorDescription("errorDescription");

        // Configure OAuthClient.getTokenByCode(...).
        final Token token = new Token();
        token.setError("error");
        token.setError_description("error_description");
        token.setAccessToken("accessToken");
        token.setTokenType("tokenType");
        token.setExpires(0L);
        token.setRefreshToken("refreshToken");
        when(mockOAuthClient.getTokenByCode("autoCode", "redirectUrl")).thenReturn(token);

        when(mockDistributedService.nextEleGuid()).thenReturn("ae0f4c3c-013f-4fa8-a035-ed677df47a1d");
        when(mockEleAuthMapper.eleAuthList(0L)).thenReturn(Collections.emptyList());

        // Run the test
        eleAuthServiceImplUnderTest.bindCallback(eleCallbackBindDTO);

        // Verify the results
    }

    @Test
    public void testUnbindCallback() {
        // Setup
        final OMessage oMessage = new OMessage();
        oMessage.setRequestId("requestId");
        oMessage.setType(0);
        oMessage.setAppId(0);
        oMessage.setMessage("message");
        oMessage.setShopId(0L);

        // Run the test
        eleAuthServiceImplUnderTest.unbindCallback(oMessage);

        // Verify the results
    }

    @Test
    public void testSaveToken() throws Exception {
        // Setup
        // Configure OAuthClient.getTokenByCode(...).
        final Token token = new Token();
        token.setError("error");
        token.setError_description("error_description");
        token.setAccessToken("accessToken");
        token.setTokenType("tokenType");
        token.setExpires(0L);
        token.setRefreshToken("refreshToken");
        when(mockOAuthClient.getTokenByCode("autoCode", "redirectUrl")).thenReturn(token);

        when(mockDistributedService.nextEleGuid()).thenReturn("ae0f4c3c-013f-4fa8-a035-ed677df47a1d");

        // Configure EleAuthMapper.eleAuthList(...).
        final EleAuthDO eleAuthDO = new EleAuthDO();
        eleAuthDO.setId(0L);
        eleAuthDO.setGuid("ae0f4c3c-013f-4fa8-a035-ed677df47a1d");
        eleAuthDO.setEnterpriseGuid("enterpriseGuid");
        eleAuthDO.setStoreGuid("storeGuid");
        eleAuthDO.setUserId(0L);
        eleAuthDO.setUserName("userName");
        eleAuthDO.setShopId(0L);
        eleAuthDO.setShopName("name");
        eleAuthDO.setDeliveryType(0);
        eleAuthDO.setAccessToken("accessToken");
        eleAuthDO.setRefreshToken("refreshToken");
        eleAuthDO.setTokenType("tokenType");
        eleAuthDO.setActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        eleAuthDO.setExpires(0L);
        eleAuthDO.setExpireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        eleAuthDO.setRefreshActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        eleAuthDO.setRefreshExpires(0L);
        eleAuthDO.setRefreshExpireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        eleAuthDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        eleAuthDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<EleAuthDO> eleAuthDOS = Arrays.asList(eleAuthDO);
        when(mockEleAuthMapper.eleAuthList(0L)).thenReturn(eleAuthDOS);

        // Run the test
        eleAuthServiceImplUnderTest.saveToken("autoCode", "enterpriseGuid", "storeGuid");

        // Verify the results
    }

    @Test(expected = BusinessException.class)
    public void testSaveToken_OAuthClientReturnsNull() throws Exception {
        // Setup
        when(mockOAuthClient.getTokenByCode("autoCode", "redirectUrl")).thenReturn(null);

        // Run the test
        eleAuthServiceImplUnderTest.saveToken("autoCode", "enterpriseGuid", "storeGuid");
    }

    @Test
    public void testSaveToken_EleAuthMapperReturnsNoItems() throws Exception {
        // Setup
        // Configure OAuthClient.getTokenByCode(...).
        final Token token = new Token();
        token.setError("error");
        token.setError_description("error_description");
        token.setAccessToken("accessToken");
        token.setTokenType("tokenType");
        token.setExpires(0L);
        token.setRefreshToken("refreshToken");
        when(mockOAuthClient.getTokenByCode("autoCode", "redirectUrl")).thenReturn(token);

        when(mockDistributedService.nextEleGuid()).thenReturn("ae0f4c3c-013f-4fa8-a035-ed677df47a1d");
        when(mockEleAuthMapper.eleAuthList(0L)).thenReturn(Collections.emptyList());

        // Run the test
        eleAuthServiceImplUnderTest.saveToken("autoCode", "enterpriseGuid", "storeGuid");

        // Verify the results
    }

    @Test
    public void testGetTokenByShopId() {
        // Setup
        // Configure OAuthClient.getTokenByRefreshToken(...).
        final Token token = new Token();
        token.setError("error");
        token.setError_description("error_description");
        token.setAccessToken("accessToken");
        token.setTokenType("tokenType");
        token.setExpires(0L);
        token.setRefreshToken("refreshToken");
        when(mockOAuthClient.getTokenByRefreshToken("refreshToken")).thenReturn(token);

        when(mockDistributedService.nextEleGuid()).thenReturn("ae0f4c3c-013f-4fa8-a035-ed677df47a1d");

        // Run the test
        final Token result = eleAuthServiceImplUnderTest.getTokenByShopId(0L);

        // Verify the results
    }

    @Test
    public void testGetTokenByShopId_OAuthClientReturnsNull() {
        // Setup
        when(mockOAuthClient.getTokenByRefreshToken("refreshToken")).thenReturn(null);

        // Run the test
        final Token result = eleAuthServiceImplUnderTest.getTokenByShopId(0L);

        // Verify the results
        assertNull(result);
    }

    @Test
    public void testRefreshToken1() {
        // Setup
        // Configure OAuthClient.getTokenByRefreshToken(...).
        final Token token = new Token();
        token.setError("error");
        token.setError_description("error_description");
        token.setAccessToken("accessToken");
        token.setTokenType("tokenType");
        token.setExpires(0L);
        token.setRefreshToken("refreshToken");
        when(mockOAuthClient.getTokenByRefreshToken("refreshToken")).thenReturn(token);

        when(mockDistributedService.nextEleGuid()).thenReturn("ae0f4c3c-013f-4fa8-a035-ed677df47a1d");

        // Run the test
        eleAuthServiceImplUnderTest.refreshToken(0L);

        // Verify the results
    }

    @Test
    public void testRefreshToken1_OAuthClientReturnsNull() {
        // Setup
        when(mockOAuthClient.getTokenByRefreshToken("refreshToken")).thenReturn(null);

        // Run the test
        eleAuthServiceImplUnderTest.refreshToken(0L);

        // Verify the results
    }

    @Test
    public void testRefreshToken2() {
        // Setup
        final EleAuthDO eleAuthDO = new EleAuthDO();
        eleAuthDO.setId(0L);
        eleAuthDO.setGuid("ae0f4c3c-013f-4fa8-a035-ed677df47a1d");
        eleAuthDO.setEnterpriseGuid("enterpriseGuid");
        eleAuthDO.setStoreGuid("storeGuid");
        eleAuthDO.setUserId(0L);
        eleAuthDO.setUserName("userName");
        eleAuthDO.setShopId(0L);
        eleAuthDO.setShopName("name");
        eleAuthDO.setDeliveryType(0);
        eleAuthDO.setAccessToken("accessToken");
        eleAuthDO.setRefreshToken("refreshToken");
        eleAuthDO.setTokenType("tokenType");
        eleAuthDO.setActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        eleAuthDO.setExpires(0L);
        eleAuthDO.setExpireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        eleAuthDO.setRefreshActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        eleAuthDO.setRefreshExpires(0L);
        eleAuthDO.setRefreshExpireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        eleAuthDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        eleAuthDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Configure OAuthClient.getTokenByRefreshToken(...).
        final Token token = new Token();
        token.setError("error");
        token.setError_description("error_description");
        token.setAccessToken("accessToken");
        token.setTokenType("tokenType");
        token.setExpires(0L);
        token.setRefreshToken("refreshToken");
        when(mockOAuthClient.getTokenByRefreshToken("refreshToken")).thenReturn(token);

        when(mockDistributedService.nextEleGuid()).thenReturn("ae0f4c3c-013f-4fa8-a035-ed677df47a1d");

        // Run the test
        final Token result = eleAuthServiceImplUnderTest.refreshToken(eleAuthDO);

        // Verify the results
    }

    @Test
    public void testRefreshToken2_OAuthClientReturnsNull() {
        // Setup
        final EleAuthDO eleAuthDO = new EleAuthDO();
        eleAuthDO.setId(0L);
        eleAuthDO.setGuid("ae0f4c3c-013f-4fa8-a035-ed677df47a1d");
        eleAuthDO.setEnterpriseGuid("enterpriseGuid");
        eleAuthDO.setStoreGuid("storeGuid");
        eleAuthDO.setUserId(0L);
        eleAuthDO.setUserName("userName");
        eleAuthDO.setShopId(0L);
        eleAuthDO.setShopName("name");
        eleAuthDO.setDeliveryType(0);
        eleAuthDO.setAccessToken("accessToken");
        eleAuthDO.setRefreshToken("refreshToken");
        eleAuthDO.setTokenType("tokenType");
        eleAuthDO.setActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        eleAuthDO.setExpires(0L);
        eleAuthDO.setExpireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        eleAuthDO.setRefreshActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        eleAuthDO.setRefreshExpires(0L);
        eleAuthDO.setRefreshExpireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        eleAuthDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        eleAuthDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        when(mockOAuthClient.getTokenByRefreshToken("refreshToken")).thenReturn(null);

        // Run the test
        final Token result = eleAuthServiceImplUnderTest.refreshToken(eleAuthDO);

        // Verify the results
        assertNull(result);
    }

    @Test
    public void testGetToken1() {
        // Setup
        // Configure OAuthClient.getTokenByRefreshToken(...).
        final Token token = new Token();
        token.setError("error");
        token.setError_description("error_description");
        token.setAccessToken("accessToken");
        token.setTokenType("tokenType");
        token.setExpires(0L);
        token.setRefreshToken("refreshToken");
        when(mockOAuthClient.getTokenByRefreshToken("refreshToken")).thenReturn(token);

        when(mockDistributedService.nextEleGuid()).thenReturn("ae0f4c3c-013f-4fa8-a035-ed677df47a1d");

        // Run the test
        final Token result = eleAuthServiceImplUnderTest.getToken(0L);

        // Verify the results
    }

    @Test
    public void testGetToken1_OAuthClientReturnsNull() {
        // Setup
        when(mockOAuthClient.getTokenByRefreshToken("refreshToken")).thenReturn(null);

        // Run the test
        final Token result = eleAuthServiceImplUnderTest.getToken(0L);

        // Verify the results
        assertNull(result);
    }

    @Test
    public void testGetToken2() {
        // Setup
        // Configure OAuthClient.getTokenByRefreshToken(...).
        final Token token = new Token();
        token.setError("error");
        token.setError_description("error_description");
        token.setAccessToken("accessToken");
        token.setTokenType("tokenType");
        token.setExpires(0L);
        token.setRefreshToken("refreshToken");
        when(mockOAuthClient.getTokenByRefreshToken("refreshToken")).thenReturn(token);

        when(mockDistributedService.nextEleGuid()).thenReturn("ae0f4c3c-013f-4fa8-a035-ed677df47a1d");

        // Run the test
        final Token result = eleAuthServiceImplUnderTest.getToken("storeGuid");

        // Verify the results
    }

    @Test
    public void testGetToken2_OAuthClientReturnsNull() {
        // Setup
        when(mockOAuthClient.getTokenByRefreshToken("refreshToken")).thenReturn(null);

        // Run the test
        final Token result = eleAuthServiceImplUnderTest.getToken("storeGuid");

        // Verify the results
        assertNull(result);
    }

    @Test
    public void testGetToken3() {
        // Setup
        final EleAuthDO eleAuthDO = new EleAuthDO();
        eleAuthDO.setId(0L);
        eleAuthDO.setGuid("ae0f4c3c-013f-4fa8-a035-ed677df47a1d");
        eleAuthDO.setEnterpriseGuid("enterpriseGuid");
        eleAuthDO.setStoreGuid("storeGuid");
        eleAuthDO.setUserId(0L);
        eleAuthDO.setUserName("userName");
        eleAuthDO.setShopId(0L);
        eleAuthDO.setShopName("name");
        eleAuthDO.setDeliveryType(0);
        eleAuthDO.setAccessToken("accessToken");
        eleAuthDO.setRefreshToken("refreshToken");
        eleAuthDO.setTokenType("tokenType");
        eleAuthDO.setActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        eleAuthDO.setExpires(0L);
        eleAuthDO.setExpireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        eleAuthDO.setRefreshActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        eleAuthDO.setRefreshExpires(0L);
        eleAuthDO.setRefreshExpireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        eleAuthDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        eleAuthDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Configure OAuthClient.getTokenByRefreshToken(...).
        final Token token = new Token();
        token.setError("error");
        token.setError_description("error_description");
        token.setAccessToken("accessToken");
        token.setTokenType("tokenType");
        token.setExpires(0L);
        token.setRefreshToken("refreshToken");
        when(mockOAuthClient.getTokenByRefreshToken("refreshToken")).thenReturn(token);

        when(mockDistributedService.nextEleGuid()).thenReturn("ae0f4c3c-013f-4fa8-a035-ed677df47a1d");

        // Run the test
        final Token result = eleAuthServiceImplUnderTest.getToken(eleAuthDO);

        // Verify the results
    }

    @Test
    public void testGetToken3_OAuthClientReturnsNull() {
        // Setup
        final EleAuthDO eleAuthDO = new EleAuthDO();
        eleAuthDO.setId(0L);
        eleAuthDO.setGuid("ae0f4c3c-013f-4fa8-a035-ed677df47a1d");
        eleAuthDO.setEnterpriseGuid("enterpriseGuid");
        eleAuthDO.setStoreGuid("storeGuid");
        eleAuthDO.setUserId(0L);
        eleAuthDO.setUserName("userName");
        eleAuthDO.setShopId(0L);
        eleAuthDO.setShopName("name");
        eleAuthDO.setDeliveryType(0);
        eleAuthDO.setAccessToken("accessToken");
        eleAuthDO.setRefreshToken("refreshToken");
        eleAuthDO.setTokenType("tokenType");
        eleAuthDO.setActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        eleAuthDO.setExpires(0L);
        eleAuthDO.setExpireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        eleAuthDO.setRefreshActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        eleAuthDO.setRefreshExpires(0L);
        eleAuthDO.setRefreshExpireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        eleAuthDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        eleAuthDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        when(mockOAuthClient.getTokenByRefreshToken("refreshToken")).thenReturn(null);

        // Run the test
        final Token result = eleAuthServiceImplUnderTest.getToken(eleAuthDO);

        // Verify the results
        assertNull(result);
    }

    @Test
    public void testGetTokens() {
        // Setup
        // Configure OAuthClient.getTokenByRefreshToken(...).
        final Token token = new Token();
        token.setError("error");
        token.setError_description("error_description");
        token.setAccessToken("accessToken");
        token.setTokenType("tokenType");
        token.setExpires(0L);
        token.setRefreshToken("refreshToken");
        when(mockOAuthClient.getTokenByRefreshToken("refreshToken")).thenReturn(token);

        when(mockDistributedService.nextEleGuid()).thenReturn("ae0f4c3c-013f-4fa8-a035-ed677df47a1d");

        // Run the test
        final Map<String, Pair<EleAuthDO, Token>> result = eleAuthServiceImplUnderTest.getTokens(
                Arrays.asList("value"));

        // Verify the results
    }

    @Test
    public void testGetTokens_OAuthClientReturnsNull() {
        // Setup
        when(mockOAuthClient.getTokenByRefreshToken("refreshToken")).thenReturn(null);

        // Run the test
        final Map<String, Pair<EleAuthDO, Token>> result = eleAuthServiceImplUnderTest.getTokens(
                Arrays.asList("value"));

        // Verify the results
    }

    @Test
    public void testListAuth() {
        // Setup
        final StoreAuthDTO storeAuthDTO = new StoreAuthDTO();
        storeAuthDTO.setStoreGuid("storeGuid");
        storeAuthDTO.setShopId("shopId");
        storeAuthDTO.setShopName("name");
        storeAuthDTO.setBindingStatus(0);
        storeAuthDTO.setDeliveryType(0);
        final List<StoreAuthDTO> storeAuthorizationDTOList = Arrays.asList(storeAuthDTO);
        final StoreAuthDTO storeAuthDTO1 = new StoreAuthDTO();
        storeAuthDTO1.setStoreGuid("storeGuid");
        storeAuthDTO1.setShopId("shopId");
        storeAuthDTO1.setShopName("name");
        storeAuthDTO1.setBindingStatus(0);
        storeAuthDTO1.setDeliveryType(0);
        final List<StoreAuthDTO> expectedResult = Arrays.asList(storeAuthDTO1);

        // Run the test
        final List<StoreAuthDTO> result = eleAuthServiceImplUnderTest.listAuth(storeAuthorizationDTOList);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetTakeoutAuth() {
        // Setup
        final StoreAuthDTO storeAuthDTO = new StoreAuthDTO();
        storeAuthDTO.setStoreGuid("storeGuid");
        storeAuthDTO.setShopId("shopId");
        storeAuthDTO.setShopName("name");
        storeAuthDTO.setBindingStatus(0);
        storeAuthDTO.setDeliveryType(0);

        final StoreAuthDTO expectedResult = new StoreAuthDTO();
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setShopId("shopId");
        expectedResult.setShopName("name");
        expectedResult.setBindingStatus(0);
        expectedResult.setDeliveryType(0);

        // Run the test
        final StoreAuthDTO result = eleAuthServiceImplUnderTest.getTakeoutAuth(storeAuthDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testUpdateDelivery() {
        // Setup
        final StoreAuthDTO storeAuthDTO = new StoreAuthDTO();
        storeAuthDTO.setStoreGuid("storeGuid");
        storeAuthDTO.setShopId("shopId");
        storeAuthDTO.setShopName("name");
        storeAuthDTO.setBindingStatus(0);
        storeAuthDTO.setDeliveryType(0);

        // Run the test
        final Boolean result = eleAuthServiceImplUnderTest.updateDelivery(storeAuthDTO);

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testCorrectAuth() {
        // Setup
        final ServiceException e = new ServiceException("code", "message", new Exception("message"));
        final EleAuthDO eleAuthDO = new EleAuthDO();
        eleAuthDO.setId(0L);
        eleAuthDO.setGuid("ae0f4c3c-013f-4fa8-a035-ed677df47a1d");
        eleAuthDO.setEnterpriseGuid("enterpriseGuid");
        eleAuthDO.setStoreGuid("storeGuid");
        eleAuthDO.setUserId(0L);
        eleAuthDO.setUserName("userName");
        eleAuthDO.setShopId(0L);
        eleAuthDO.setShopName("name");
        eleAuthDO.setDeliveryType(0);
        eleAuthDO.setAccessToken("accessToken");
        eleAuthDO.setRefreshToken("refreshToken");
        eleAuthDO.setTokenType("tokenType");
        eleAuthDO.setActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        eleAuthDO.setExpires(0L);
        eleAuthDO.setExpireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        eleAuthDO.setRefreshActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        eleAuthDO.setRefreshExpires(0L);
        eleAuthDO.setRefreshExpireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        eleAuthDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        eleAuthDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Configure OAuthClient.getTokenByRefreshToken(...).
        final Token token = new Token();
        token.setError("error");
        token.setError_description("error_description");
        token.setAccessToken("accessToken");
        token.setTokenType("tokenType");
        token.setExpires(0L);
        token.setRefreshToken("refreshToken");
        when(mockOAuthClient.getTokenByRefreshToken("refreshToken")).thenReturn(token);

        when(mockDistributedService.nextEleGuid()).thenReturn("ae0f4c3c-013f-4fa8-a035-ed677df47a1d");

        // Run the test
        eleAuthServiceImplUnderTest.correctAuth(e, eleAuthDO);

        // Verify the results
    }

    @Test
    public void testCorrectAuth_OAuthClientReturnsNull() {
        // Setup
        final ServiceException e = new ServiceException("code", "message", new Exception("message"));
        final EleAuthDO eleAuthDO = new EleAuthDO();
        eleAuthDO.setId(0L);
        eleAuthDO.setGuid("ae0f4c3c-013f-4fa8-a035-ed677df47a1d");
        eleAuthDO.setEnterpriseGuid("enterpriseGuid");
        eleAuthDO.setStoreGuid("storeGuid");
        eleAuthDO.setUserId(0L);
        eleAuthDO.setUserName("userName");
        eleAuthDO.setShopId(0L);
        eleAuthDO.setShopName("name");
        eleAuthDO.setDeliveryType(0);
        eleAuthDO.setAccessToken("accessToken");
        eleAuthDO.setRefreshToken("refreshToken");
        eleAuthDO.setTokenType("tokenType");
        eleAuthDO.setActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        eleAuthDO.setExpires(0L);
        eleAuthDO.setExpireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        eleAuthDO.setRefreshActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        eleAuthDO.setRefreshExpires(0L);
        eleAuthDO.setRefreshExpireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        eleAuthDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        eleAuthDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        when(mockOAuthClient.getTokenByRefreshToken("refreshToken")).thenReturn(null);

        // Run the test
        eleAuthServiceImplUnderTest.correctAuth(e, eleAuthDO);

        // Verify the results
    }

    @Test
    public void testGetEffectServicePackContract() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setOrderStatus(0);
        unOrder.setShopId(0L);
        unOrder.setShopName("shopName");
        unOrder.setEnterpriseGuid("enterpriseGuid");
        unOrder.setStoreGuid("storeGuid");

        // Configure OAuthClient.getTokenByRefreshToken(...).
        final Token token = new Token();
        token.setError("error");
        token.setError_description("error_description");
        token.setAccessToken("accessToken");
        token.setTokenType("tokenType");
        token.setExpires(0L);
        token.setRefreshToken("refreshToken");
        when(mockOAuthClient.getTokenByRefreshToken("refreshToken")).thenReturn(token);

        when(mockDistributedService.nextEleGuid()).thenReturn("ae0f4c3c-013f-4fa8-a035-ed677df47a1d");

        // Run the test
        final ShopContract result = eleAuthServiceImplUnderTest.getEffectServicePackContract(unOrder);

        // Verify the results
    }

    @Test
    public void testGetEffectServicePackContract_OAuthClientReturnsNull() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setOrderStatus(0);
        unOrder.setShopId(0L);
        unOrder.setShopName("shopName");
        unOrder.setEnterpriseGuid("enterpriseGuid");
        unOrder.setStoreGuid("storeGuid");

        when(mockOAuthClient.getTokenByRefreshToken("refreshToken")).thenReturn(null);

        // Run the test
        final ShopContract result = eleAuthServiceImplUnderTest.getEffectServicePackContract(unOrder);

        // Verify the results
    }

    @Test
    public void testCorrectAuthThenThrow() {
        // Setup
        final ServiceException e = new ServiceException("code", "message", new Exception("message"));
        final UnOrder unOrder = new UnOrder();
        unOrder.setOrderStatus(0);
        unOrder.setShopId(0L);
        unOrder.setShopName("shopName");
        unOrder.setEnterpriseGuid("enterpriseGuid");
        unOrder.setStoreGuid("storeGuid");

        // Configure OAuthClient.getTokenByRefreshToken(...).
        final Token token = new Token();
        token.setError("error");
        token.setError_description("error_description");
        token.setAccessToken("accessToken");
        token.setTokenType("tokenType");
        token.setExpires(0L);
        token.setRefreshToken("refreshToken");
        when(mockOAuthClient.getTokenByRefreshToken("refreshToken")).thenReturn(token);

        when(mockDistributedService.nextEleGuid()).thenReturn("ae0f4c3c-013f-4fa8-a035-ed677df47a1d");

        // Run the test
        eleAuthServiceImplUnderTest.correctAuthThenThrow(e, unOrder);

        // Verify the results
    }

    @Test
    public void testCorrectAuthThenThrow_OAuthClientReturnsNull() {
        // Setup
        final ServiceException e = new ServiceException("code", "message", new Exception("message"));
        final UnOrder unOrder = new UnOrder();
        unOrder.setOrderStatus(0);
        unOrder.setShopId(0L);
        unOrder.setShopName("shopName");
        unOrder.setEnterpriseGuid("enterpriseGuid");
        unOrder.setStoreGuid("storeGuid");

        when(mockOAuthClient.getTokenByRefreshToken("refreshToken")).thenReturn(null);

        // Run the test
        eleAuthServiceImplUnderTest.correctAuthThenThrow(e, unOrder);

        // Verify the results
    }
}
