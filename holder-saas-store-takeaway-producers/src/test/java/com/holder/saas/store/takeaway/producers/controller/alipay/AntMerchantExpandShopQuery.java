package com.holder.saas.store.takeaway.producers.controller.alipay;

import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.AlipayConfig;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.diagnosis.DiagnosisUtils;
import com.alipay.api.domain.AntMerchantExpandShopQueryModel;
import com.alipay.api.request.AntMerchantExpandShopQueryRequest;
import com.alipay.api.response.AntMerchantExpandShopQueryResponse;
import lombok.extern.slf4j.Slf4j;

/**
 * ant.merchant.expand.shop.query(店铺查询接口)
 */
@Slf4j
public class AntMerchantExpandShopQuery {

    public static void main(String[] args) throws AlipayApiException {
        String privateKey = "MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQC1K3hBze+5OYZDUSMSyaDrCf/s+U93SS0vEPf3Om+w5+U6OG1/RJnf30rqsSeRwXX+oR90bJdKT4W3Paf56N1hsEqu4UebOEHerIHzTc2Zn4stz2UISs3bZeI1CXLdr0eGsL84YT7skmHdBMs4qnt9vIgnM3n8yFkxIWQtNgIy5fDbYNsTPsWuaqwlG1g1htisthfApOgB7z7XiGUFRoQYDqSsvWRtuHdPx/vqQw1aNTRYSsY+BdbOV0YB1cgurRzWKshg/644KU8yUWyo6qiaKeNMaudwECTLYFYUmP5LpuXsx5i2O9UeGoZuP+PteEosCn6yQO6PO3v41HHSQtAfAgMBAAECggEAaR5h1nyxHZ39A04utZX8IqRYtJ0ruKDi8K490ls7tz+2R4O6NOAPEFC1XVuRBAdmeWnvoFPprJpbkajasCynVyk9DB92tt97iMI7XHrGUt65mMj/bEJLE4QYLoHe/jXXoJUpPoNeSVHeKqm/aI+yu5AKyAQcQcnVxqYDRCBUQkci/+WgOFT79EbgSo/95kyOfOxmKl5X43fp/7EKYzvq8rcbA/MB1Jz/pOB1QVpSadvhfRynMIZVUmGjqBizjUX7K6miykuzD2aYHfJarTKOSEAoY+vDS5CNFvEKb6d55ekGTZjFOweD7tge1BXtoqp4ezxJnzUSUtMrDQj4FclVoQKBgQDYrxczEcLIvJ8MTnM1rw2YhfJ85THNfGoRs+IwmAM5rEtePnNQC3JEkVDkd1SdGUWeIoJpjLBPTWeRA4NiOYbhMn9c+WIX/JBdISU9jaALkhxoKhMI3zJvrhuSZhApIFMqSs6+TIX0uK182qm/RzIFRQlaOZU0gHdODiff6jLINwKBgQDWCsHJFpobLV6/MATA/Jgo42iolfgyu2EisYflTsb41eA9L+UVh+pQn1yQ0agkXd56UYgwOZ/r6rhhmKY0GU36IxeJRPfbZHNzRL0EYwkLgIQt8RpGU99+bNDaWeyiZhtULlT8J/+yHctpfvsTINPQPBTO6+iW4C7D1ClPjGnzWQKBgDMDPZ37T3U82uDl0z/PP48p8cWvm8L5yGr0g4rXLuM0CeZdI36zwyobYAT5hwUZPweUrkk1Iwj4eyprvlGwo21/GvcDizGEd2RFemP8ZLJ0qpVnWmXbTh92yOz1dFaKUqoBRJ0OnPbZR5a+T/pFQ6Rz9Vi1d8gNeoj40jBoDZ6PAoGAcGN9lYUUriiIk4M3O85mZzHhhoQYqyrQulLVtIZE2+wRro8zjrarRmInTLOJwm63nyqohAn9+vZoknaA7uJKm4lJln5sVvrL1D7HsUH/QdbdkPIEheEMfWnN0JlfM3bB6tXZMf4euPwEDwTp+QVHsxOZKX2dx3s3kwQYHPjxuSECgYBHltHZmrsWqqtfzy+3v1JJxUtQK1dc1AlN0d1EgcZLVDJzjDV0cCFoVgRW8XE9Ylmes5AmGzVDH9kXcanWdpdmw0hpzwZADGxVEBdHLFWpJs5fPIg6/Z5X4+YgTPrZqGDFCypLumCB1tyVWcEQL8gi2jBmo5pvmy5qa0cwt5o/6g==";
        String alipayPublicKey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAmnQSbptztvH+VXPAR1afZU9keFgzrv42a7015dRZk9PVCchvX6BuUzz17WZ1fTTYyGZa2q1tF1hoyHiGLojOdun9IQVK+16kVWKK+t3+tt9eT4f/nkqmvj1Y4/l2vDSxQFchph76gbgz+Hw8eTEAYAmsMdriqOfE/0TUUWjHQKJrwCRmJIOhWJefVrXEkjPuULSnt/RiBT+zJAbaU2sQ08Udiey3iYacehHoEebSMv9vdbG/SDukqnOZVvHKA6m5sGkLVZwwJUB0CTuiJ2SscTfenAu/7k7Puxih2XAu0q7raHpR3nM35HLAbAAtcpps3fqe/yQwYi2U6qD/xqT1FQIDAQAB\n";
        AlipayConfig alipayConfig = new AlipayConfig();
        alipayConfig.setServerUrl("https://openapi.alipay.com/gateway.do");
        alipayConfig.setAppId("2021004125681141");
        alipayConfig.setPrivateKey(privateKey);
        alipayConfig.setFormat("json");
        alipayConfig.setAlipayPublicKey(alipayPublicKey);
        alipayConfig.setCharset("UTF-8");
        alipayConfig.setSignType("RSA2");
        AlipayClient alipayClient = new DefaultAlipayClient(alipayConfig);
        AntMerchantExpandShopQueryRequest request = new AntMerchantExpandShopQueryRequest();
        AntMerchantExpandShopQueryModel model = new AntMerchantExpandShopQueryModel();
        model.setNeedRecommend("0");
        model.setAddressVersion("UPTODATE");
        // 蚂蚁店铺id。填写本参数的话，store_id和ip_role_id可以不填
        model.setShopId("2018101900077000000064375816");
//        model.setStoreId("XJ202206142394560987");
//        model.setIpRoleId("2088621980811097");
        request.setBizModel(model);
        AntMerchantExpandShopQueryResponse response = alipayClient.execute(request);
        log.info("Body={}", response.getBody());
        if (response.isSuccess()) {
            log.info("调用成功");
        } else {
            log.info("调用失败");
            // sdk版本是"4.38.0.ALL"及以上,可以参考下面的示例获取诊断链接
            String diagnosisUrl = DiagnosisUtils.getDiagnosisUrl(response);
            log.info(diagnosisUrl);
        }
    }
}
