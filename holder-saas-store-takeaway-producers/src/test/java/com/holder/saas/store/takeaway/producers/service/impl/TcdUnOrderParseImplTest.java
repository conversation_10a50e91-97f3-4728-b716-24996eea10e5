package com.holder.saas.store.takeaway.producers.service.impl;

import com.holderzone.saas.store.dto.takeaway.UnItem;
import com.holderzone.saas.store.dto.takeaway.UnOrder;
import com.holderzone.saas.store.dto.takeaway.request.SelfSupportDishPropertyTemp;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutTcdOrderReqDTO;
import org.junit.Before;
import org.junit.Test;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;

import static org.junit.Assert.assertEquals;

public class TcdUnOrderParseImplTest {

    private TcdUnOrderParseImpl tcdUnOrderParseImplUnderTest;

    @Before
    public void setUp() throws Exception {
        tcdUnOrderParseImplUnderTest = new TcdUnOrderParseImpl();
    }

    @Test
    public void testFromOrderCreate() {
        // Setup
        final TakeoutTcdOrderReqDTO req = new TakeoutTcdOrderReqDTO();
        req.setMerchantId(0L);
        req.setEnterpriseGuid("enterpriseGuid");
        req.setPrint(false);
        req.setStoreGuid("storeGuid");
        req.setWriteOffCode("writeOffCode");
        req.setStoreName("shopName");
        req.setOrderSn("orderId");
        req.setDayCount(1);
        req.setOrderType("orderType");
        req.setTotalAmount(new BigDecimal("0.00"));
        req.setProductFee(new BigDecimal("0.00"));
        req.setPackFee(new BigDecimal("0.00"));
        req.setShippingFee(new BigDecimal("0.00"));
        req.setAmount(new BigDecimal("0.00"));
        req.setFullReduction(new BigDecimal("0.00"));
        req.setCouponFee(new BigDecimal("0.00"));
        req.setDiscountFee(new BigDecimal("0.00"));
        req.setOtherDiscount(new BigDecimal("0.00"));
        req.setPersonName("personName");
        req.setPersonPhone("personPhone");
        req.setWhoRefunds(0);
        req.setRequestRefundsReason("refundReqReason");
        final TakeoutTcdOrderReqDTO.OrderExtended orderExtended = new TakeoutTcdOrderReqDTO.OrderExtended();
        orderExtended.setCustomerAddress("customerAddress");
        orderExtended.setCustomerName("customerName");
        orderExtended.setCustomerPhone("customerPhone");
        orderExtended.setCustomerCoordinate("customerCoordinate");
        orderExtended.setOrderSend("orderSend");
        orderExtended.setOrderMark("到店取餐");
        req.setOrderExtended(orderExtended);
        final TakeoutTcdOrderReqDTO.ReduceActivity reduceActivity = new TakeoutTcdOrderReqDTO.ReduceActivity();
        final TakeoutTcdOrderReqDTO.Reduction reduction = new TakeoutTcdOrderReqDTO.Reduction();
        reduction.setCutMoney(new BigDecimal("0.00"));
        reduceActivity.setReductionList(Arrays.asList(reduction));
        req.setReduceActivity(reduceActivity);
        final TakeoutTcdOrderReqDTO.OrderProduct orderProduct = new TakeoutTcdOrderReqDTO.OrderProduct();
        orderProduct.setSkuId("skuId");
        orderProduct.setProductNum(0);
        orderProduct.setProductPrice(new BigDecimal("0.00"));
        orderProduct.setItemName("itemName");
        final TakeoutTcdOrderReqDTO.ProductSpec spec = new TakeoutTcdOrderReqDTO.ProductSpec();
        spec.setSkuGuid("itemSku");
        spec.setSkuName("skuName");
        spec.setName("itemSpec");
        spec.setUnit("itemUnit");
        spec.setPackNum(0);
        spec.setPackPrice(new BigDecimal("0.00"));
        spec.setPrice(new BigDecimal("0.00"));
        spec.setBoxPrice(new BigDecimal("0.00"));
        orderProduct.setSpec(spec);
        orderProduct.setMemberPrice(new BigDecimal("0.00"));
        final SelfSupportDishPropertyTemp selfSupportDishPropertyTemp = new SelfSupportDishPropertyTemp();
        selfSupportDishPropertyTemp.setDetails("details");
        orderProduct.setSelfSupportDishPropertyTempList(Arrays.asList(selfSupportDishPropertyTemp));
        req.setOrderProductList(Arrays.asList(orderProduct));

        final UnOrder expectedResult = new UnOrder();
        expectedResult.setShopId(0L);
        expectedResult.setShopName("shopName");
        expectedResult.setCbMsgType(0);
        expectedResult.setOrderType(0);
        expectedResult.setOrderSubType(0);
        expectedResult.setOrderStatusTcd("CANCELLED");
        expectedResult.setWriteOffCode("writeOffCode");
        expectedResult.setOrderTypeTcd("orderType");
        expectedResult.setEnterpriseGuid("enterpriseGuid");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setPrint(false);
        expectedResult.setStoreName("shopName");
        expectedResult.setOrderId("orderId");
        expectedResult.setOrderViewId("orderId");
        expectedResult.setOrderDaySn("orderDaySn");
        expectedResult.setOrderRemark("到店取餐");
        expectedResult.setReserve(false);
        expectedResult.setCustomerName("customerName");
        expectedResult.setCustomerPhone("customerPhone");
        expectedResult.setPrivacyPhone("privacyPhone");
        expectedResult.setCustomerAddress("customerAddress");
        expectedResult.setCustomerNumber(0);
        expectedResult.setFirstOrder(false);
        expectedResult.setShipLatitude("shipLatitude");
        expectedResult.setShipLongitude("shipLongitude");
        expectedResult.setShipperName("personName");
        expectedResult.setShipperPhone("personPhone");
        expectedResult.setThirdShipper(false);
        expectedResult.setInvoiced(false);
        expectedResult.setInvoiceType(0);
        expectedResult.setInvoiceTitle("invoiceTitle");
        expectedResult.setTaxpayerId("taxpayerId");
        expectedResult.setTotal(new BigDecimal("0.00"));
        expectedResult.setShipTotal(new BigDecimal("0.00"));
        expectedResult.setItemTotal(new BigDecimal("0.00"));
        expectedResult.setPackageTotal(new BigDecimal("0.00"));
        expectedResult.setDiscountTotal(new BigDecimal("0.00"));
        expectedResult.setEnterpriseDiscount(new BigDecimal("0.00"));
        expectedResult.setPlatformDiscount(new BigDecimal("0.00"));
        expectedResult.setOtherDiscount(new BigDecimal("0.00"));
        expectedResult.setServiceFeeRate(new BigDecimal("0.00"));
        expectedResult.setServiceFee(new BigDecimal("0.00"));
        expectedResult.setCustomerActualPay(new BigDecimal("0.00"));
        expectedResult.setPart(false);
        expectedResult.setCustomerRefund(new BigDecimal("0.00"));
        expectedResult.setShopTotal(new BigDecimal("0.00"));
        expectedResult.setOnlinePay(false);
        expectedResult.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setAcceptTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setDeliveryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRefundReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRefundReqReason("refundReqReason");
        expectedResult.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReason("商户取消订单");
        expectedResult.setCancelRoleName("商户");
        expectedResult.setCompleteTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final UnItem unItem = new UnItem();
        unItem.setItemSku("itemSku");
        unItem.setItemCode("itemCode");
        unItem.setItemName("itemName");
        unItem.setItemUnit("itemUnit");
        unItem.setItemPrice(new BigDecimal("0.00"));
        unItem.setItemCount(new BigDecimal("0.00"));
        unItem.setItemTotal(new BigDecimal("0.00"));
        unItem.setItemSpec("itemSpec");
        unItem.setItemProperty("itemProperty");
        unItem.setBoxPrice(new BigDecimal("0.00"));
        unItem.setBoxCount(new BigDecimal("0.00"));
        expectedResult.setArrayOfUnItem(Arrays.asList(unItem));

        // Run the test
        final UnOrder result = tcdUnOrderParseImplUnderTest.fromOrderCreate(req);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testFromOrderCreatePersonPending() {
        // Setup
        final TakeoutTcdOrderReqDTO req = new TakeoutTcdOrderReqDTO();
        req.setMerchantId(0L);
        req.setEnterpriseGuid("enterpriseGuid");
        req.setPrint(false);
        req.setStoreGuid("storeGuid");
        req.setWriteOffCode("writeOffCode");
        req.setStoreName("shopName");
        req.setOrderSn("orderId");
        req.setDayCount(1);
        req.setOrderType("orderType");
        req.setTotalAmount(new BigDecimal("0.00"));
        req.setProductFee(new BigDecimal("0.00"));
        req.setPackFee(new BigDecimal("0.00"));
        req.setShippingFee(new BigDecimal("0.00"));
        req.setAmount(new BigDecimal("0.00"));
        req.setFullReduction(new BigDecimal("0.00"));
        req.setCouponFee(new BigDecimal("0.00"));
        req.setDiscountFee(new BigDecimal("0.00"));
        req.setOtherDiscount(new BigDecimal("0.00"));
        req.setPersonName("personName");
        req.setPersonPhone("personPhone");
        req.setWhoRefunds(0);
        req.setRequestRefundsReason("refundReqReason");
        final TakeoutTcdOrderReqDTO.OrderExtended orderExtended = new TakeoutTcdOrderReqDTO.OrderExtended();
        orderExtended.setCustomerAddress("customerAddress");
        orderExtended.setCustomerName("customerName");
        orderExtended.setCustomerPhone("customerPhone");
        orderExtended.setCustomerCoordinate("customerCoordinate");
        orderExtended.setOrderSend("orderSend");
        orderExtended.setOrderMark("到店取餐");
        req.setOrderExtended(orderExtended);
        final TakeoutTcdOrderReqDTO.ReduceActivity reduceActivity = new TakeoutTcdOrderReqDTO.ReduceActivity();
        final TakeoutTcdOrderReqDTO.Reduction reduction = new TakeoutTcdOrderReqDTO.Reduction();
        reduction.setCutMoney(new BigDecimal("0.00"));
        reduceActivity.setReductionList(Arrays.asList(reduction));
        req.setReduceActivity(reduceActivity);
        final TakeoutTcdOrderReqDTO.OrderProduct orderProduct = new TakeoutTcdOrderReqDTO.OrderProduct();
        orderProduct.setSkuId("skuId");
        orderProduct.setProductNum(0);
        orderProduct.setProductPrice(new BigDecimal("0.00"));
        orderProduct.setItemName("itemName");
        final TakeoutTcdOrderReqDTO.ProductSpec spec = new TakeoutTcdOrderReqDTO.ProductSpec();
        spec.setSkuGuid("itemSku");
        spec.setSkuName("skuName");
        spec.setName("itemSpec");
        spec.setUnit("itemUnit");
        spec.setPackNum(0);
        spec.setPackPrice(new BigDecimal("0.00"));
        spec.setPrice(new BigDecimal("0.00"));
        spec.setBoxPrice(new BigDecimal("0.00"));
        orderProduct.setSpec(spec);
        orderProduct.setMemberPrice(new BigDecimal("0.00"));
        final SelfSupportDishPropertyTemp selfSupportDishPropertyTemp = new SelfSupportDishPropertyTemp();
        selfSupportDishPropertyTemp.setDetails("details");
        orderProduct.setSelfSupportDishPropertyTempList(Arrays.asList(selfSupportDishPropertyTemp));
        req.setOrderProductList(Arrays.asList(orderProduct));

        final UnOrder expectedResult = new UnOrder();
        expectedResult.setShopId(0L);
        expectedResult.setShopName("shopName");
        expectedResult.setCbMsgType(0);
        expectedResult.setOrderType(0);
        expectedResult.setOrderSubType(0);
        expectedResult.setOrderStatusTcd("CANCELLED");
        expectedResult.setWriteOffCode("writeOffCode");
        expectedResult.setOrderTypeTcd("orderType");
        expectedResult.setEnterpriseGuid("enterpriseGuid");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setPrint(false);
        expectedResult.setStoreName("shopName");
        expectedResult.setOrderId("orderId");
        expectedResult.setOrderViewId("orderId");
        expectedResult.setOrderDaySn("orderDaySn");
        expectedResult.setOrderRemark("到店取餐");
        expectedResult.setReserve(false);
        expectedResult.setCustomerName("customerName");
        expectedResult.setCustomerPhone("customerPhone");
        expectedResult.setPrivacyPhone("privacyPhone");
        expectedResult.setCustomerAddress("customerAddress");
        expectedResult.setCustomerNumber(0);
        expectedResult.setFirstOrder(false);
        expectedResult.setShipLatitude("shipLatitude");
        expectedResult.setShipLongitude("shipLongitude");
        expectedResult.setShipperName("personName");
        expectedResult.setShipperPhone("personPhone");
        expectedResult.setThirdShipper(false);
        expectedResult.setInvoiced(false);
        expectedResult.setInvoiceType(0);
        expectedResult.setInvoiceTitle("invoiceTitle");
        expectedResult.setTaxpayerId("taxpayerId");
        expectedResult.setTotal(new BigDecimal("0.00"));
        expectedResult.setShipTotal(new BigDecimal("0.00"));
        expectedResult.setItemTotal(new BigDecimal("0.00"));
        expectedResult.setPackageTotal(new BigDecimal("0.00"));
        expectedResult.setDiscountTotal(new BigDecimal("0.00"));
        expectedResult.setEnterpriseDiscount(new BigDecimal("0.00"));
        expectedResult.setPlatformDiscount(new BigDecimal("0.00"));
        expectedResult.setOtherDiscount(new BigDecimal("0.00"));
        expectedResult.setServiceFeeRate(new BigDecimal("0.00"));
        expectedResult.setServiceFee(new BigDecimal("0.00"));
        expectedResult.setCustomerActualPay(new BigDecimal("0.00"));
        expectedResult.setPart(false);
        expectedResult.setCustomerRefund(new BigDecimal("0.00"));
        expectedResult.setShopTotal(new BigDecimal("0.00"));
        expectedResult.setOnlinePay(false);
        expectedResult.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setAcceptTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setDeliveryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRefundReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRefundReqReason("refundReqReason");
        expectedResult.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReason("商户取消订单");
        expectedResult.setCancelRoleName("商户");
        expectedResult.setCompleteTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final UnItem unItem = new UnItem();
        unItem.setItemSku("itemSku");
        unItem.setItemCode("itemCode");
        unItem.setItemName("itemName");
        unItem.setItemUnit("itemUnit");
        unItem.setItemPrice(new BigDecimal("0.00"));
        unItem.setItemCount(new BigDecimal("0.00"));
        unItem.setItemTotal(new BigDecimal("0.00"));
        unItem.setItemSpec("itemSpec");
        unItem.setItemProperty("itemProperty");
        unItem.setBoxPrice(new BigDecimal("0.00"));
        unItem.setBoxCount(new BigDecimal("0.00"));
        expectedResult.setArrayOfUnItem(Arrays.asList(unItem));

        // Run the test
        final UnOrder result = tcdUnOrderParseImplUnderTest.fromOrderCreatePersonPending(req);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testFromOrderConfirmed() {
        // Setup
        final TakeoutTcdOrderReqDTO req = new TakeoutTcdOrderReqDTO();
        req.setMerchantId(0L);
        req.setEnterpriseGuid("enterpriseGuid");
        req.setPrint(false);
        req.setStoreGuid("storeGuid");
        req.setWriteOffCode("writeOffCode");
        req.setStoreName("shopName");
        req.setOrderSn("orderId");
        req.setDayCount(1);
        req.setOrderType("orderType");
        req.setTotalAmount(new BigDecimal("0.00"));
        req.setProductFee(new BigDecimal("0.00"));
        req.setPackFee(new BigDecimal("0.00"));
        req.setShippingFee(new BigDecimal("0.00"));
        req.setAmount(new BigDecimal("0.00"));
        req.setFullReduction(new BigDecimal("0.00"));
        req.setCouponFee(new BigDecimal("0.00"));
        req.setDiscountFee(new BigDecimal("0.00"));
        req.setOtherDiscount(new BigDecimal("0.00"));
        req.setPersonName("personName");
        req.setPersonPhone("personPhone");
        req.setWhoRefunds(0);
        req.setRequestRefundsReason("refundReqReason");
        final TakeoutTcdOrderReqDTO.OrderExtended orderExtended = new TakeoutTcdOrderReqDTO.OrderExtended();
        orderExtended.setCustomerAddress("customerAddress");
        orderExtended.setCustomerName("customerName");
        orderExtended.setCustomerPhone("customerPhone");
        orderExtended.setCustomerCoordinate("customerCoordinate");
        orderExtended.setOrderSend("orderSend");
        orderExtended.setOrderMark("到店取餐");
        req.setOrderExtended(orderExtended);
        final TakeoutTcdOrderReqDTO.ReduceActivity reduceActivity = new TakeoutTcdOrderReqDTO.ReduceActivity();
        final TakeoutTcdOrderReqDTO.Reduction reduction = new TakeoutTcdOrderReqDTO.Reduction();
        reduction.setCutMoney(new BigDecimal("0.00"));
        reduceActivity.setReductionList(Arrays.asList(reduction));
        req.setReduceActivity(reduceActivity);
        final TakeoutTcdOrderReqDTO.OrderProduct orderProduct = new TakeoutTcdOrderReqDTO.OrderProduct();
        orderProduct.setSkuId("skuId");
        orderProduct.setProductNum(0);
        orderProduct.setProductPrice(new BigDecimal("0.00"));
        orderProduct.setItemName("itemName");
        final TakeoutTcdOrderReqDTO.ProductSpec spec = new TakeoutTcdOrderReqDTO.ProductSpec();
        spec.setSkuGuid("itemSku");
        spec.setSkuName("skuName");
        spec.setName("itemSpec");
        spec.setUnit("itemUnit");
        spec.setPackNum(0);
        spec.setPackPrice(new BigDecimal("0.00"));
        spec.setPrice(new BigDecimal("0.00"));
        spec.setBoxPrice(new BigDecimal("0.00"));
        orderProduct.setSpec(spec);
        orderProduct.setMemberPrice(new BigDecimal("0.00"));
        final SelfSupportDishPropertyTemp selfSupportDishPropertyTemp = new SelfSupportDishPropertyTemp();
        selfSupportDishPropertyTemp.setDetails("details");
        orderProduct.setSelfSupportDishPropertyTempList(Arrays.asList(selfSupportDishPropertyTemp));
        req.setOrderProductList(Arrays.asList(orderProduct));

        final UnOrder expectedResult = new UnOrder();
        expectedResult.setShopId(0L);
        expectedResult.setShopName("shopName");
        expectedResult.setCbMsgType(0);
        expectedResult.setOrderType(0);
        expectedResult.setOrderSubType(0);
        expectedResult.setOrderStatusTcd("CANCELLED");
        expectedResult.setWriteOffCode("writeOffCode");
        expectedResult.setOrderTypeTcd("orderType");
        expectedResult.setEnterpriseGuid("enterpriseGuid");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setPrint(false);
        expectedResult.setStoreName("shopName");
        expectedResult.setOrderId("orderId");
        expectedResult.setOrderViewId("orderId");
        expectedResult.setOrderDaySn("orderDaySn");
        expectedResult.setOrderRemark("到店取餐");
        expectedResult.setReserve(false);
        expectedResult.setCustomerName("customerName");
        expectedResult.setCustomerPhone("customerPhone");
        expectedResult.setPrivacyPhone("privacyPhone");
        expectedResult.setCustomerAddress("customerAddress");
        expectedResult.setCustomerNumber(0);
        expectedResult.setFirstOrder(false);
        expectedResult.setShipLatitude("shipLatitude");
        expectedResult.setShipLongitude("shipLongitude");
        expectedResult.setShipperName("personName");
        expectedResult.setShipperPhone("personPhone");
        expectedResult.setThirdShipper(false);
        expectedResult.setInvoiced(false);
        expectedResult.setInvoiceType(0);
        expectedResult.setInvoiceTitle("invoiceTitle");
        expectedResult.setTaxpayerId("taxpayerId");
        expectedResult.setTotal(new BigDecimal("0.00"));
        expectedResult.setShipTotal(new BigDecimal("0.00"));
        expectedResult.setItemTotal(new BigDecimal("0.00"));
        expectedResult.setPackageTotal(new BigDecimal("0.00"));
        expectedResult.setDiscountTotal(new BigDecimal("0.00"));
        expectedResult.setEnterpriseDiscount(new BigDecimal("0.00"));
        expectedResult.setPlatformDiscount(new BigDecimal("0.00"));
        expectedResult.setOtherDiscount(new BigDecimal("0.00"));
        expectedResult.setServiceFeeRate(new BigDecimal("0.00"));
        expectedResult.setServiceFee(new BigDecimal("0.00"));
        expectedResult.setCustomerActualPay(new BigDecimal("0.00"));
        expectedResult.setPart(false);
        expectedResult.setCustomerRefund(new BigDecimal("0.00"));
        expectedResult.setShopTotal(new BigDecimal("0.00"));
        expectedResult.setOnlinePay(false);
        expectedResult.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setAcceptTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setDeliveryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRefundReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRefundReqReason("refundReqReason");
        expectedResult.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReason("商户取消订单");
        expectedResult.setCancelRoleName("商户");
        expectedResult.setCompleteTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final UnItem unItem = new UnItem();
        unItem.setItemSku("itemSku");
        unItem.setItemCode("itemCode");
        unItem.setItemName("itemName");
        unItem.setItemUnit("itemUnit");
        unItem.setItemPrice(new BigDecimal("0.00"));
        unItem.setItemCount(new BigDecimal("0.00"));
        unItem.setItemTotal(new BigDecimal("0.00"));
        unItem.setItemSpec("itemSpec");
        unItem.setItemProperty("itemProperty");
        unItem.setBoxPrice(new BigDecimal("0.00"));
        unItem.setBoxCount(new BigDecimal("0.00"));
        expectedResult.setArrayOfUnItem(Arrays.asList(unItem));

        // Run the test
        final UnOrder result = tcdUnOrderParseImplUnderTest.fromOrderConfirmed(req);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testFromOrderCanceled() {
        // Setup
        final TakeoutTcdOrderReqDTO takeoutTcdOrderReqDTO = new TakeoutTcdOrderReqDTO();
        takeoutTcdOrderReqDTO.setMerchantId(0L);
        takeoutTcdOrderReqDTO.setEnterpriseGuid("enterpriseGuid");
        takeoutTcdOrderReqDTO.setPrint(false);
        takeoutTcdOrderReqDTO.setStoreGuid("storeGuid");
        takeoutTcdOrderReqDTO.setWriteOffCode("writeOffCode");
        takeoutTcdOrderReqDTO.setStoreName("shopName");
        takeoutTcdOrderReqDTO.setOrderSn("orderId");
        takeoutTcdOrderReqDTO.setDayCount(1);
        takeoutTcdOrderReqDTO.setOrderType("orderType");
        takeoutTcdOrderReqDTO.setTotalAmount(new BigDecimal("0.00"));
        takeoutTcdOrderReqDTO.setProductFee(new BigDecimal("0.00"));
        takeoutTcdOrderReqDTO.setPackFee(new BigDecimal("0.00"));
        takeoutTcdOrderReqDTO.setShippingFee(new BigDecimal("0.00"));
        takeoutTcdOrderReqDTO.setAmount(new BigDecimal("0.00"));
        takeoutTcdOrderReqDTO.setFullReduction(new BigDecimal("0.00"));
        takeoutTcdOrderReqDTO.setCouponFee(new BigDecimal("0.00"));
        takeoutTcdOrderReqDTO.setDiscountFee(new BigDecimal("0.00"));
        takeoutTcdOrderReqDTO.setOtherDiscount(new BigDecimal("0.00"));
        takeoutTcdOrderReqDTO.setPersonName("personName");
        takeoutTcdOrderReqDTO.setPersonPhone("personPhone");
        takeoutTcdOrderReqDTO.setWhoRefunds(0);
        takeoutTcdOrderReqDTO.setRequestRefundsReason("refundReqReason");
        final TakeoutTcdOrderReqDTO.OrderExtended orderExtended = new TakeoutTcdOrderReqDTO.OrderExtended();
        orderExtended.setCustomerAddress("customerAddress");
        orderExtended.setCustomerName("customerName");
        orderExtended.setCustomerPhone("customerPhone");
        orderExtended.setCustomerCoordinate("customerCoordinate");
        orderExtended.setOrderSend("orderSend");
        orderExtended.setOrderMark("到店取餐");
        takeoutTcdOrderReqDTO.setOrderExtended(orderExtended);
        final TakeoutTcdOrderReqDTO.ReduceActivity reduceActivity = new TakeoutTcdOrderReqDTO.ReduceActivity();
        final TakeoutTcdOrderReqDTO.Reduction reduction = new TakeoutTcdOrderReqDTO.Reduction();
        reduction.setCutMoney(new BigDecimal("0.00"));
        reduceActivity.setReductionList(Arrays.asList(reduction));
        takeoutTcdOrderReqDTO.setReduceActivity(reduceActivity);
        final TakeoutTcdOrderReqDTO.OrderProduct orderProduct = new TakeoutTcdOrderReqDTO.OrderProduct();
        orderProduct.setSkuId("skuId");
        orderProduct.setProductNum(0);
        orderProduct.setProductPrice(new BigDecimal("0.00"));
        orderProduct.setItemName("itemName");
        final TakeoutTcdOrderReqDTO.ProductSpec spec = new TakeoutTcdOrderReqDTO.ProductSpec();
        spec.setSkuGuid("itemSku");
        spec.setSkuName("skuName");
        spec.setName("itemSpec");
        spec.setUnit("itemUnit");
        spec.setPackNum(0);
        spec.setPackPrice(new BigDecimal("0.00"));
        spec.setPrice(new BigDecimal("0.00"));
        spec.setBoxPrice(new BigDecimal("0.00"));
        orderProduct.setSpec(spec);
        orderProduct.setMemberPrice(new BigDecimal("0.00"));
        final SelfSupportDishPropertyTemp selfSupportDishPropertyTemp = new SelfSupportDishPropertyTemp();
        selfSupportDishPropertyTemp.setDetails("details");
        orderProduct.setSelfSupportDishPropertyTempList(Arrays.asList(selfSupportDishPropertyTemp));
        takeoutTcdOrderReqDTO.setOrderProductList(Arrays.asList(orderProduct));

        final UnOrder expectedResult = new UnOrder();
        expectedResult.setShopId(0L);
        expectedResult.setShopName("shopName");
        expectedResult.setCbMsgType(0);
        expectedResult.setOrderType(0);
        expectedResult.setOrderSubType(0);
        expectedResult.setOrderStatusTcd("CANCELLED");
        expectedResult.setWriteOffCode("writeOffCode");
        expectedResult.setOrderTypeTcd("orderType");
        expectedResult.setEnterpriseGuid("enterpriseGuid");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setPrint(false);
        expectedResult.setStoreName("shopName");
        expectedResult.setOrderId("orderId");
        expectedResult.setOrderViewId("orderId");
        expectedResult.setOrderDaySn("orderDaySn");
        expectedResult.setOrderRemark("到店取餐");
        expectedResult.setReserve(false);
        expectedResult.setCustomerName("customerName");
        expectedResult.setCustomerPhone("customerPhone");
        expectedResult.setPrivacyPhone("privacyPhone");
        expectedResult.setCustomerAddress("customerAddress");
        expectedResult.setCustomerNumber(0);
        expectedResult.setFirstOrder(false);
        expectedResult.setShipLatitude("shipLatitude");
        expectedResult.setShipLongitude("shipLongitude");
        expectedResult.setShipperName("personName");
        expectedResult.setShipperPhone("personPhone");
        expectedResult.setThirdShipper(false);
        expectedResult.setInvoiced(false);
        expectedResult.setInvoiceType(0);
        expectedResult.setInvoiceTitle("invoiceTitle");
        expectedResult.setTaxpayerId("taxpayerId");
        expectedResult.setTotal(new BigDecimal("0.00"));
        expectedResult.setShipTotal(new BigDecimal("0.00"));
        expectedResult.setItemTotal(new BigDecimal("0.00"));
        expectedResult.setPackageTotal(new BigDecimal("0.00"));
        expectedResult.setDiscountTotal(new BigDecimal("0.00"));
        expectedResult.setEnterpriseDiscount(new BigDecimal("0.00"));
        expectedResult.setPlatformDiscount(new BigDecimal("0.00"));
        expectedResult.setOtherDiscount(new BigDecimal("0.00"));
        expectedResult.setServiceFeeRate(new BigDecimal("0.00"));
        expectedResult.setServiceFee(new BigDecimal("0.00"));
        expectedResult.setCustomerActualPay(new BigDecimal("0.00"));
        expectedResult.setPart(false);
        expectedResult.setCustomerRefund(new BigDecimal("0.00"));
        expectedResult.setShopTotal(new BigDecimal("0.00"));
        expectedResult.setOnlinePay(false);
        expectedResult.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setAcceptTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setDeliveryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRefundReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRefundReqReason("refundReqReason");
        expectedResult.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReason("商户取消订单");
        expectedResult.setCancelRoleName("商户");
        expectedResult.setCompleteTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final UnItem unItem = new UnItem();
        unItem.setItemSku("itemSku");
        unItem.setItemCode("itemCode");
        unItem.setItemName("itemName");
        unItem.setItemUnit("itemUnit");
        unItem.setItemPrice(new BigDecimal("0.00"));
        unItem.setItemCount(new BigDecimal("0.00"));
        unItem.setItemTotal(new BigDecimal("0.00"));
        unItem.setItemSpec("itemSpec");
        unItem.setItemProperty("itemProperty");
        unItem.setBoxPrice(new BigDecimal("0.00"));
        unItem.setBoxCount(new BigDecimal("0.00"));
        expectedResult.setArrayOfUnItem(Arrays.asList(unItem));

        // Run the test
        final UnOrder result = tcdUnOrderParseImplUnderTest.fromOrderCanceled(takeoutTcdOrderReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testFromOrderRefunded() {
        // Setup
        final TakeoutTcdOrderReqDTO req = new TakeoutTcdOrderReqDTO();
        req.setMerchantId(0L);
        req.setEnterpriseGuid("enterpriseGuid");
        req.setPrint(false);
        req.setStoreGuid("storeGuid");
        req.setWriteOffCode("writeOffCode");
        req.setStoreName("shopName");
        req.setOrderSn("orderId");
        req.setDayCount(1);
        req.setOrderType("orderType");
        req.setTotalAmount(new BigDecimal("0.00"));
        req.setProductFee(new BigDecimal("0.00"));
        req.setPackFee(new BigDecimal("0.00"));
        req.setShippingFee(new BigDecimal("0.00"));
        req.setAmount(new BigDecimal("0.00"));
        req.setFullReduction(new BigDecimal("0.00"));
        req.setCouponFee(new BigDecimal("0.00"));
        req.setDiscountFee(new BigDecimal("0.00"));
        req.setOtherDiscount(new BigDecimal("0.00"));
        req.setPersonName("personName");
        req.setPersonPhone("personPhone");
        req.setWhoRefunds(0);
        req.setRequestRefundsReason("refundReqReason");
        final TakeoutTcdOrderReqDTO.OrderExtended orderExtended = new TakeoutTcdOrderReqDTO.OrderExtended();
        orderExtended.setCustomerAddress("customerAddress");
        orderExtended.setCustomerName("customerName");
        orderExtended.setCustomerPhone("customerPhone");
        orderExtended.setCustomerCoordinate("customerCoordinate");
        orderExtended.setOrderSend("orderSend");
        orderExtended.setOrderMark("到店取餐");
        req.setOrderExtended(orderExtended);
        final TakeoutTcdOrderReqDTO.ReduceActivity reduceActivity = new TakeoutTcdOrderReqDTO.ReduceActivity();
        final TakeoutTcdOrderReqDTO.Reduction reduction = new TakeoutTcdOrderReqDTO.Reduction();
        reduction.setCutMoney(new BigDecimal("0.00"));
        reduceActivity.setReductionList(Arrays.asList(reduction));
        req.setReduceActivity(reduceActivity);
        final TakeoutTcdOrderReqDTO.OrderProduct orderProduct = new TakeoutTcdOrderReqDTO.OrderProduct();
        orderProduct.setSkuId("skuId");
        orderProduct.setProductNum(0);
        orderProduct.setProductPrice(new BigDecimal("0.00"));
        orderProduct.setItemName("itemName");
        final TakeoutTcdOrderReqDTO.ProductSpec spec = new TakeoutTcdOrderReqDTO.ProductSpec();
        spec.setSkuGuid("itemSku");
        spec.setSkuName("skuName");
        spec.setName("itemSpec");
        spec.setUnit("itemUnit");
        spec.setPackNum(0);
        spec.setPackPrice(new BigDecimal("0.00"));
        spec.setPrice(new BigDecimal("0.00"));
        spec.setBoxPrice(new BigDecimal("0.00"));
        orderProduct.setSpec(spec);
        orderProduct.setMemberPrice(new BigDecimal("0.00"));
        final SelfSupportDishPropertyTemp selfSupportDishPropertyTemp = new SelfSupportDishPropertyTemp();
        selfSupportDishPropertyTemp.setDetails("details");
        orderProduct.setSelfSupportDishPropertyTempList(Arrays.asList(selfSupportDishPropertyTemp));
        req.setOrderProductList(Arrays.asList(orderProduct));

        final UnOrder expectedResult = new UnOrder();
        expectedResult.setShopId(0L);
        expectedResult.setShopName("shopName");
        expectedResult.setCbMsgType(0);
        expectedResult.setOrderType(0);
        expectedResult.setOrderSubType(0);
        expectedResult.setOrderStatusTcd("CANCELLED");
        expectedResult.setWriteOffCode("writeOffCode");
        expectedResult.setOrderTypeTcd("orderType");
        expectedResult.setEnterpriseGuid("enterpriseGuid");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setPrint(false);
        expectedResult.setStoreName("shopName");
        expectedResult.setOrderId("orderId");
        expectedResult.setOrderViewId("orderId");
        expectedResult.setOrderDaySn("orderDaySn");
        expectedResult.setOrderRemark("到店取餐");
        expectedResult.setReserve(false);
        expectedResult.setCustomerName("customerName");
        expectedResult.setCustomerPhone("customerPhone");
        expectedResult.setPrivacyPhone("privacyPhone");
        expectedResult.setCustomerAddress("customerAddress");
        expectedResult.setCustomerNumber(0);
        expectedResult.setFirstOrder(false);
        expectedResult.setShipLatitude("shipLatitude");
        expectedResult.setShipLongitude("shipLongitude");
        expectedResult.setShipperName("personName");
        expectedResult.setShipperPhone("personPhone");
        expectedResult.setThirdShipper(false);
        expectedResult.setInvoiced(false);
        expectedResult.setInvoiceType(0);
        expectedResult.setInvoiceTitle("invoiceTitle");
        expectedResult.setTaxpayerId("taxpayerId");
        expectedResult.setTotal(new BigDecimal("0.00"));
        expectedResult.setShipTotal(new BigDecimal("0.00"));
        expectedResult.setItemTotal(new BigDecimal("0.00"));
        expectedResult.setPackageTotal(new BigDecimal("0.00"));
        expectedResult.setDiscountTotal(new BigDecimal("0.00"));
        expectedResult.setEnterpriseDiscount(new BigDecimal("0.00"));
        expectedResult.setPlatformDiscount(new BigDecimal("0.00"));
        expectedResult.setOtherDiscount(new BigDecimal("0.00"));
        expectedResult.setServiceFeeRate(new BigDecimal("0.00"));
        expectedResult.setServiceFee(new BigDecimal("0.00"));
        expectedResult.setCustomerActualPay(new BigDecimal("0.00"));
        expectedResult.setPart(false);
        expectedResult.setCustomerRefund(new BigDecimal("0.00"));
        expectedResult.setShopTotal(new BigDecimal("0.00"));
        expectedResult.setOnlinePay(false);
        expectedResult.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setAcceptTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setDeliveryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRefundReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRefundReqReason("refundReqReason");
        expectedResult.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReason("商户取消订单");
        expectedResult.setCancelRoleName("商户");
        expectedResult.setCompleteTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final UnItem unItem = new UnItem();
        unItem.setItemSku("itemSku");
        unItem.setItemCode("itemCode");
        unItem.setItemName("itemName");
        unItem.setItemUnit("itemUnit");
        unItem.setItemPrice(new BigDecimal("0.00"));
        unItem.setItemCount(new BigDecimal("0.00"));
        unItem.setItemTotal(new BigDecimal("0.00"));
        unItem.setItemSpec("itemSpec");
        unItem.setItemProperty("itemProperty");
        unItem.setBoxPrice(new BigDecimal("0.00"));
        unItem.setBoxCount(new BigDecimal("0.00"));
        expectedResult.setArrayOfUnItem(Arrays.asList(unItem));

        // Run the test
        final UnOrder result = tcdUnOrderParseImplUnderTest.fromOrderRefunded(req);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testFromOrderShipping() {
        // Setup
        final TakeoutTcdOrderReqDTO req = new TakeoutTcdOrderReqDTO();
        req.setMerchantId(0L);
        req.setEnterpriseGuid("enterpriseGuid");
        req.setPrint(false);
        req.setStoreGuid("storeGuid");
        req.setWriteOffCode("writeOffCode");
        req.setStoreName("shopName");
        req.setOrderSn("orderId");
        req.setDayCount(1);
        req.setOrderType("orderType");
        req.setTotalAmount(new BigDecimal("0.00"));
        req.setProductFee(new BigDecimal("0.00"));
        req.setPackFee(new BigDecimal("0.00"));
        req.setShippingFee(new BigDecimal("0.00"));
        req.setAmount(new BigDecimal("0.00"));
        req.setFullReduction(new BigDecimal("0.00"));
        req.setCouponFee(new BigDecimal("0.00"));
        req.setDiscountFee(new BigDecimal("0.00"));
        req.setOtherDiscount(new BigDecimal("0.00"));
        req.setPersonName("personName");
        req.setPersonPhone("personPhone");
        req.setWhoRefunds(0);
        req.setRequestRefundsReason("refundReqReason");
        final TakeoutTcdOrderReqDTO.OrderExtended orderExtended = new TakeoutTcdOrderReqDTO.OrderExtended();
        orderExtended.setCustomerAddress("customerAddress");
        orderExtended.setCustomerName("customerName");
        orderExtended.setCustomerPhone("customerPhone");
        orderExtended.setCustomerCoordinate("customerCoordinate");
        orderExtended.setOrderSend("orderSend");
        orderExtended.setOrderMark("到店取餐");
        req.setOrderExtended(orderExtended);
        final TakeoutTcdOrderReqDTO.ReduceActivity reduceActivity = new TakeoutTcdOrderReqDTO.ReduceActivity();
        final TakeoutTcdOrderReqDTO.Reduction reduction = new TakeoutTcdOrderReqDTO.Reduction();
        reduction.setCutMoney(new BigDecimal("0.00"));
        reduceActivity.setReductionList(Arrays.asList(reduction));
        req.setReduceActivity(reduceActivity);
        final TakeoutTcdOrderReqDTO.OrderProduct orderProduct = new TakeoutTcdOrderReqDTO.OrderProduct();
        orderProduct.setSkuId("skuId");
        orderProduct.setProductNum(0);
        orderProduct.setProductPrice(new BigDecimal("0.00"));
        orderProduct.setItemName("itemName");
        final TakeoutTcdOrderReqDTO.ProductSpec spec = new TakeoutTcdOrderReqDTO.ProductSpec();
        spec.setSkuGuid("itemSku");
        spec.setSkuName("skuName");
        spec.setName("itemSpec");
        spec.setUnit("itemUnit");
        spec.setPackNum(0);
        spec.setPackPrice(new BigDecimal("0.00"));
        spec.setPrice(new BigDecimal("0.00"));
        spec.setBoxPrice(new BigDecimal("0.00"));
        orderProduct.setSpec(spec);
        orderProduct.setMemberPrice(new BigDecimal("0.00"));
        final SelfSupportDishPropertyTemp selfSupportDishPropertyTemp = new SelfSupportDishPropertyTemp();
        selfSupportDishPropertyTemp.setDetails("details");
        orderProduct.setSelfSupportDishPropertyTempList(Arrays.asList(selfSupportDishPropertyTemp));
        req.setOrderProductList(Arrays.asList(orderProduct));

        final UnOrder expectedResult = new UnOrder();
        expectedResult.setShopId(0L);
        expectedResult.setShopName("shopName");
        expectedResult.setCbMsgType(0);
        expectedResult.setOrderType(0);
        expectedResult.setOrderSubType(0);
        expectedResult.setOrderStatusTcd("CANCELLED");
        expectedResult.setWriteOffCode("writeOffCode");
        expectedResult.setOrderTypeTcd("orderType");
        expectedResult.setEnterpriseGuid("enterpriseGuid");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setPrint(false);
        expectedResult.setStoreName("shopName");
        expectedResult.setOrderId("orderId");
        expectedResult.setOrderViewId("orderId");
        expectedResult.setOrderDaySn("orderDaySn");
        expectedResult.setOrderRemark("到店取餐");
        expectedResult.setReserve(false);
        expectedResult.setCustomerName("customerName");
        expectedResult.setCustomerPhone("customerPhone");
        expectedResult.setPrivacyPhone("privacyPhone");
        expectedResult.setCustomerAddress("customerAddress");
        expectedResult.setCustomerNumber(0);
        expectedResult.setFirstOrder(false);
        expectedResult.setShipLatitude("shipLatitude");
        expectedResult.setShipLongitude("shipLongitude");
        expectedResult.setShipperName("personName");
        expectedResult.setShipperPhone("personPhone");
        expectedResult.setThirdShipper(false);
        expectedResult.setInvoiced(false);
        expectedResult.setInvoiceType(0);
        expectedResult.setInvoiceTitle("invoiceTitle");
        expectedResult.setTaxpayerId("taxpayerId");
        expectedResult.setTotal(new BigDecimal("0.00"));
        expectedResult.setShipTotal(new BigDecimal("0.00"));
        expectedResult.setItemTotal(new BigDecimal("0.00"));
        expectedResult.setPackageTotal(new BigDecimal("0.00"));
        expectedResult.setDiscountTotal(new BigDecimal("0.00"));
        expectedResult.setEnterpriseDiscount(new BigDecimal("0.00"));
        expectedResult.setPlatformDiscount(new BigDecimal("0.00"));
        expectedResult.setOtherDiscount(new BigDecimal("0.00"));
        expectedResult.setServiceFeeRate(new BigDecimal("0.00"));
        expectedResult.setServiceFee(new BigDecimal("0.00"));
        expectedResult.setCustomerActualPay(new BigDecimal("0.00"));
        expectedResult.setPart(false);
        expectedResult.setCustomerRefund(new BigDecimal("0.00"));
        expectedResult.setShopTotal(new BigDecimal("0.00"));
        expectedResult.setOnlinePay(false);
        expectedResult.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setAcceptTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setDeliveryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRefundReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRefundReqReason("refundReqReason");
        expectedResult.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReason("商户取消订单");
        expectedResult.setCancelRoleName("商户");
        expectedResult.setCompleteTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final UnItem unItem = new UnItem();
        unItem.setItemSku("itemSku");
        unItem.setItemCode("itemCode");
        unItem.setItemName("itemName");
        unItem.setItemUnit("itemUnit");
        unItem.setItemPrice(new BigDecimal("0.00"));
        unItem.setItemCount(new BigDecimal("0.00"));
        unItem.setItemTotal(new BigDecimal("0.00"));
        unItem.setItemSpec("itemSpec");
        unItem.setItemProperty("itemProperty");
        unItem.setBoxPrice(new BigDecimal("0.00"));
        unItem.setBoxCount(new BigDecimal("0.00"));
        expectedResult.setArrayOfUnItem(Arrays.asList(unItem));

        // Run the test
        final UnOrder result = tcdUnOrderParseImplUnderTest.fromOrderShipping(req);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testFromOrderFinished() {
        // Setup
        final TakeoutTcdOrderReqDTO req = new TakeoutTcdOrderReqDTO();
        req.setMerchantId(0L);
        req.setEnterpriseGuid("enterpriseGuid");
        req.setPrint(false);
        req.setStoreGuid("storeGuid");
        req.setWriteOffCode("writeOffCode");
        req.setStoreName("shopName");
        req.setOrderSn("orderId");
        req.setDayCount(1);
        req.setOrderType("orderType");
        req.setTotalAmount(new BigDecimal("0.00"));
        req.setProductFee(new BigDecimal("0.00"));
        req.setPackFee(new BigDecimal("0.00"));
        req.setShippingFee(new BigDecimal("0.00"));
        req.setAmount(new BigDecimal("0.00"));
        req.setFullReduction(new BigDecimal("0.00"));
        req.setCouponFee(new BigDecimal("0.00"));
        req.setDiscountFee(new BigDecimal("0.00"));
        req.setOtherDiscount(new BigDecimal("0.00"));
        req.setPersonName("personName");
        req.setPersonPhone("personPhone");
        req.setWhoRefunds(0);
        req.setRequestRefundsReason("refundReqReason");
        final TakeoutTcdOrderReqDTO.OrderExtended orderExtended = new TakeoutTcdOrderReqDTO.OrderExtended();
        orderExtended.setCustomerAddress("customerAddress");
        orderExtended.setCustomerName("customerName");
        orderExtended.setCustomerPhone("customerPhone");
        orderExtended.setCustomerCoordinate("customerCoordinate");
        orderExtended.setOrderSend("orderSend");
        orderExtended.setOrderMark("到店取餐");
        req.setOrderExtended(orderExtended);
        final TakeoutTcdOrderReqDTO.ReduceActivity reduceActivity = new TakeoutTcdOrderReqDTO.ReduceActivity();
        final TakeoutTcdOrderReqDTO.Reduction reduction = new TakeoutTcdOrderReqDTO.Reduction();
        reduction.setCutMoney(new BigDecimal("0.00"));
        reduceActivity.setReductionList(Arrays.asList(reduction));
        req.setReduceActivity(reduceActivity);
        final TakeoutTcdOrderReqDTO.OrderProduct orderProduct = new TakeoutTcdOrderReqDTO.OrderProduct();
        orderProduct.setSkuId("skuId");
        orderProduct.setProductNum(0);
        orderProduct.setProductPrice(new BigDecimal("0.00"));
        orderProduct.setItemName("itemName");
        final TakeoutTcdOrderReqDTO.ProductSpec spec = new TakeoutTcdOrderReqDTO.ProductSpec();
        spec.setSkuGuid("itemSku");
        spec.setSkuName("skuName");
        spec.setName("itemSpec");
        spec.setUnit("itemUnit");
        spec.setPackNum(0);
        spec.setPackPrice(new BigDecimal("0.00"));
        spec.setPrice(new BigDecimal("0.00"));
        spec.setBoxPrice(new BigDecimal("0.00"));
        orderProduct.setSpec(spec);
        orderProduct.setMemberPrice(new BigDecimal("0.00"));
        final SelfSupportDishPropertyTemp selfSupportDishPropertyTemp = new SelfSupportDishPropertyTemp();
        selfSupportDishPropertyTemp.setDetails("details");
        orderProduct.setSelfSupportDishPropertyTempList(Arrays.asList(selfSupportDishPropertyTemp));
        req.setOrderProductList(Arrays.asList(orderProduct));

        final UnOrder expectedResult = new UnOrder();
        expectedResult.setShopId(0L);
        expectedResult.setShopName("shopName");
        expectedResult.setCbMsgType(0);
        expectedResult.setOrderType(0);
        expectedResult.setOrderSubType(0);
        expectedResult.setOrderStatusTcd("CANCELLED");
        expectedResult.setWriteOffCode("writeOffCode");
        expectedResult.setOrderTypeTcd("orderType");
        expectedResult.setEnterpriseGuid("enterpriseGuid");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setPrint(false);
        expectedResult.setStoreName("shopName");
        expectedResult.setOrderId("orderId");
        expectedResult.setOrderViewId("orderId");
        expectedResult.setOrderDaySn("orderDaySn");
        expectedResult.setOrderRemark("到店取餐");
        expectedResult.setReserve(false);
        expectedResult.setCustomerName("customerName");
        expectedResult.setCustomerPhone("customerPhone");
        expectedResult.setPrivacyPhone("privacyPhone");
        expectedResult.setCustomerAddress("customerAddress");
        expectedResult.setCustomerNumber(0);
        expectedResult.setFirstOrder(false);
        expectedResult.setShipLatitude("shipLatitude");
        expectedResult.setShipLongitude("shipLongitude");
        expectedResult.setShipperName("personName");
        expectedResult.setShipperPhone("personPhone");
        expectedResult.setThirdShipper(false);
        expectedResult.setInvoiced(false);
        expectedResult.setInvoiceType(0);
        expectedResult.setInvoiceTitle("invoiceTitle");
        expectedResult.setTaxpayerId("taxpayerId");
        expectedResult.setTotal(new BigDecimal("0.00"));
        expectedResult.setShipTotal(new BigDecimal("0.00"));
        expectedResult.setItemTotal(new BigDecimal("0.00"));
        expectedResult.setPackageTotal(new BigDecimal("0.00"));
        expectedResult.setDiscountTotal(new BigDecimal("0.00"));
        expectedResult.setEnterpriseDiscount(new BigDecimal("0.00"));
        expectedResult.setPlatformDiscount(new BigDecimal("0.00"));
        expectedResult.setOtherDiscount(new BigDecimal("0.00"));
        expectedResult.setServiceFeeRate(new BigDecimal("0.00"));
        expectedResult.setServiceFee(new BigDecimal("0.00"));
        expectedResult.setCustomerActualPay(new BigDecimal("0.00"));
        expectedResult.setPart(false);
        expectedResult.setCustomerRefund(new BigDecimal("0.00"));
        expectedResult.setShopTotal(new BigDecimal("0.00"));
        expectedResult.setOnlinePay(false);
        expectedResult.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setAcceptTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setDeliveryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRefundReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRefundReqReason("refundReqReason");
        expectedResult.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReason("商户取消订单");
        expectedResult.setCancelRoleName("商户");
        expectedResult.setCompleteTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final UnItem unItem = new UnItem();
        unItem.setItemSku("itemSku");
        unItem.setItemCode("itemCode");
        unItem.setItemName("itemName");
        unItem.setItemUnit("itemUnit");
        unItem.setItemPrice(new BigDecimal("0.00"));
        unItem.setItemCount(new BigDecimal("0.00"));
        unItem.setItemTotal(new BigDecimal("0.00"));
        unItem.setItemSpec("itemSpec");
        unItem.setItemProperty("itemProperty");
        unItem.setBoxPrice(new BigDecimal("0.00"));
        unItem.setBoxCount(new BigDecimal("0.00"));
        expectedResult.setArrayOfUnItem(Arrays.asList(unItem));

        // Run the test
        final UnOrder result = tcdUnOrderParseImplUnderTest.fromOrderFinished(req);

        // Verify the results
        assertEquals(expectedResult, result);
    }
}
