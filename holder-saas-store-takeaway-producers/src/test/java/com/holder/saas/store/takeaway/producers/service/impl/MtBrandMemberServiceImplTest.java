package com.holder.saas.store.takeaway.producers.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holder.saas.store.takeaway.producers.config.MeiTuanConfig;
import com.holder.saas.store.takeaway.producers.entity.domain.MtAuthDO;
import com.holder.saas.store.takeaway.producers.entity.dto.MtMemberRefreshTokenRespDTO;
import com.holder.saas.store.takeaway.producers.service.MtAuthService;
import com.holder.saas.store.takeaway.producers.service.rpc.CloudEnterpriseService;
import com.holder.saas.store.takeaway.producers.service.rpc.MemberFeignService;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.rocketmq.common.DefaultRocketMqProducer;
import com.holderzone.saas.store.dto.takeaway.MtCallbackConsumeDTO;
import com.holderzone.saas.store.dto.takeaway.MtCallbackMemberDTO;
import com.holderzone.saas.store.dto.takeaway.RefreshTokenMemberDTO;
import org.apache.rocketmq.common.message.Message;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class MtBrandMemberServiceImplTest {

    @Mock
    private MtAuthService mockMtAuthService;
    @Mock
    private MemberFeignService mockMemberFeignService;
    @Mock
    private CloudEnterpriseService mockCloudEnterpriseService;
    @Mock
    private MeiTuanConfig mockMeiTuanConfig;
    @Mock
    private DefaultRocketMqProducer mockDefaultRocketMqProducer;

    private MtBrandMemberServiceImpl mtBrandMemberServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        mtBrandMemberServiceImplUnderTest = new MtBrandMemberServiceImpl(mockMtAuthService, mockMemberFeignService,
                mockCloudEnterpriseService, mockMeiTuanConfig, mockDefaultRocketMqProducer);
    }

    @Test
    public void testJudgeAndMember() {
        // Setup
        final MtCallbackMemberDTO mtCallbackDTO = new MtCallbackMemberDTO();
        mtCallbackDTO.setDeveloperId(0L);
        mtCallbackDTO.setBusinessId(0);
        mtCallbackDTO.setOpBizCode("opBizCode");
        mtCallbackDTO.setInvokeType(0);
        mtCallbackDTO.setParam("param");

        // Configure MtAuthService.getAuthByBizCode(...).
        final MtAuthDO mtAuthDO = new MtAuthDO();
        mtAuthDO.setEPoiId("ePoiId");
        mtAuthDO.setEnterpriseGuid("enterpriseGuid");
        mtAuthDO.setAccessToken("appAuthToken");
        mtAuthDO.setBusinessId((byte) 0b0);
        mtAuthDO.setActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        mtAuthDO.setExpireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        mtAuthDO.setDeleted(false);
        mtAuthDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        mtAuthDO.setRefreshToken("refreshToken");
        mtAuthDO.setRefreshTokenExpire(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockMtAuthService.getAuthByBizCode("opBizCode", 0)).thenReturn(mtAuthDO);

        // Run the test
        final boolean result = mtBrandMemberServiceImplUnderTest.judgeAndMember(mtCallbackDTO);

        // Verify the results
        assertTrue(result);
        verify(mockDefaultRocketMqProducer).sendMessage(any(Message.class));
    }

    @Test(expected = BusinessException.class)
    public void testJudgeAndMember_MtAuthServiceReturnsNull() {
        // Setup
        final MtCallbackMemberDTO mtCallbackDTO = new MtCallbackMemberDTO();
        mtCallbackDTO.setDeveloperId(0L);
        mtCallbackDTO.setBusinessId(0);
        mtCallbackDTO.setOpBizCode("opBizCode");
        mtCallbackDTO.setInvokeType(0);
        mtCallbackDTO.setParam("param");

        when(mockMtAuthService.getAuthByBizCode("opBizCode", 0)).thenReturn(null);

        // Run the test
        mtBrandMemberServiceImplUnderTest.judgeAndMember(mtCallbackDTO);
    }

    @Test
    public void testSaveConsumeRecord() {
        // Setup
        final MtCallbackConsumeDTO mtCallbackDTO = new MtCallbackConsumeDTO();
        mtCallbackDTO.setDeveloperId(0L);
        mtCallbackDTO.setBusinessId(0);
        mtCallbackDTO.setOpBizCode("opBizCode");
        mtCallbackDTO.setMsgType(0);
        mtCallbackDTO.setMessage("message");

        // Configure MtAuthService.getAuthByBizCode(...).
        final MtAuthDO mtAuthDO = new MtAuthDO();
        mtAuthDO.setEPoiId("ePoiId");
        mtAuthDO.setEnterpriseGuid("enterpriseGuid");
        mtAuthDO.setAccessToken("appAuthToken");
        mtAuthDO.setBusinessId((byte) 0b0);
        mtAuthDO.setActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        mtAuthDO.setExpireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        mtAuthDO.setDeleted(false);
        mtAuthDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        mtAuthDO.setRefreshToken("refreshToken");
        mtAuthDO.setRefreshTokenExpire(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockMtAuthService.getAuthByBizCode("opBizCode", 0)).thenReturn(mtAuthDO);

        when(mockMeiTuanConfig.getSignKey()).thenReturn("signKey");

        // Run the test
        mtBrandMemberServiceImplUnderTest.saveConsumeRecord(mtCallbackDTO);

        // Verify the results
        verify(mockDefaultRocketMqProducer).sendMessage(any(Message.class));
    }

    @Test
    public void testMemberRefreshToken() {
        // Setup
        // Configure MtAuthService.list(...).
        final MtAuthDO mtAuthDO = new MtAuthDO();
        mtAuthDO.setEPoiId("ePoiId");
        mtAuthDO.setEnterpriseGuid("enterpriseGuid");
        mtAuthDO.setAccessToken("appAuthToken");
        mtAuthDO.setBusinessId((byte) 0b0);
        mtAuthDO.setActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        mtAuthDO.setExpireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        mtAuthDO.setDeleted(false);
        mtAuthDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        mtAuthDO.setRefreshToken("refreshToken");
        mtAuthDO.setRefreshTokenExpire(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<MtAuthDO> mtAuthDOS = Arrays.asList(mtAuthDO);
        when(mockMtAuthService.list(any(LambdaQueryWrapper.class))).thenReturn(mtAuthDOS);

        when(mockMeiTuanConfig.getDeveloperId()).thenReturn(0);
        when(mockMeiTuanConfig.getSignKey()).thenReturn("signKey");

        // Run the test
        mtBrandMemberServiceImplUnderTest.memberRefreshToken();

        // Verify the results
        // Confirm MtAuthService.updateBatchById(...).
        final MtAuthDO mtAuthDO1 = new MtAuthDO();
        mtAuthDO1.setEPoiId("ePoiId");
        mtAuthDO1.setEnterpriseGuid("enterpriseGuid");
        mtAuthDO1.setAccessToken("appAuthToken");
        mtAuthDO1.setBusinessId((byte) 0b0);
        mtAuthDO1.setActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        mtAuthDO1.setExpireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        mtAuthDO1.setDeleted(false);
        mtAuthDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        mtAuthDO1.setRefreshToken("refreshToken");
        mtAuthDO1.setRefreshTokenExpire(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<MtAuthDO> entityList = Arrays.asList(mtAuthDO1);
        verify(mockMtAuthService).updateBatchById(entityList);
    }

    @Test
    public void testMemberRefreshToken_MtAuthServiceListReturnsNoItems() {
        // Setup
        when(mockMtAuthService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        mtBrandMemberServiceImplUnderTest.memberRefreshToken();

        // Verify the results
    }

    @Test
    public void testRequestRefreshToken() {
        // Setup
        final RefreshTokenMemberDTO refreshTokenMemberDTO = new RefreshTokenMemberDTO();
        refreshTokenMemberDTO.setBusinessId("businessId");
        refreshTokenMemberDTO.setRefreshToken("refreshToken");

        final MtMemberRefreshTokenRespDTO.MemberRefreshTokenResp expectedResult = new MtMemberRefreshTokenRespDTO.MemberRefreshTokenResp();
        expectedResult.setAccessToken("appAuthToken");
        expectedResult.setExpireIn(0L);
        expectedResult.setOpBizCode("opBizCode");
        expectedResult.setRefreshToken("refreshToken");
        expectedResult.setScope("scope");

        when(mockMeiTuanConfig.getDeveloperId()).thenReturn(0);
        when(mockMeiTuanConfig.getSignKey()).thenReturn("signKey");

        // Run the test
        final MtMemberRefreshTokenRespDTO.MemberRefreshTokenResp result = mtBrandMemberServiceImplUnderTest.requestRefreshToken(
                refreshTokenMemberDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }
}
