package com.holder.saas.store.takeaway.producers.service.impl;

import com.holder.saas.store.takeaway.producers.service.MtAuthService;
import com.holderzone.saas.store.dto.takeaway.request.CouponDelReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.MtCouponReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtCouponDoCheckRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtCouponPreRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtDealRespMenu;
import com.holderzone.saas.store.dto.takeaway.response.MtDelCouponRespDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;

@RunWith(MockitoJUnitRunner.class)
public class MtDevCouponServiceImplTest {

    @Mock
    private MtAuthService mockAuthService;

    private MtDevCouponServiceImpl mtDevCouponServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        mtDevCouponServiceImplUnderTest = new MtDevCouponServiceImpl(mockAuthService);
        ReflectionTestUtils.setField(mtDevCouponServiceImplUnderTest, "mtSignKey", "mtSignKey");
    }

    @Test
    public void testQueryById() {
        assertNull(mtDevCouponServiceImplUnderTest.queryById(new MtCouponReqDTO()));
    }

    @Test
    public void testCheckTicket() {
        // Setup
        final MtCouponReqDTO mtCouponReqDTO = new MtCouponReqDTO();
        mtCouponReqDTO.setStoreGuid("storeGuid");
        mtCouponReqDTO.setCouponCode("couponCode");
        mtCouponReqDTO.setCount(0);
        mtCouponReqDTO.setErpId("erpId");
        mtCouponReqDTO.setErpName("eName");
        mtCouponReqDTO.setErpOrderId("erpOrderId");

        final MtCouponDoCheckRespDTO expectedResult = new MtCouponDoCheckRespDTO();
        expectedResult.setCouponCodes(Arrays.asList("value"));
        expectedResult.setDealTitle("kfpttest_zl5_02人餐");
        expectedResult.setDealValue(0.0);
        expectedResult.setDealId(0);
        expectedResult.setMessage("");
        expectedResult.setPoiid("159869278");
        expectedResult.setResult(0);

        // Run the test
        final MtCouponDoCheckRespDTO result = mtDevCouponServiceImplUnderTest.checkTicket(mtCouponReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testDoCheck() {
        // Setup
        final MtCouponReqDTO mtCouponReqDTO = new MtCouponReqDTO();
        mtCouponReqDTO.setStoreGuid("storeGuid");
        mtCouponReqDTO.setCouponCode("couponCode");
        mtCouponReqDTO.setCount(0);
        mtCouponReqDTO.setErpId("erpId");
        mtCouponReqDTO.setErpName("eName");
        mtCouponReqDTO.setErpOrderId("erpOrderId");

        final MtCouponDoCheckRespDTO expectedResult = new MtCouponDoCheckRespDTO();
        expectedResult.setCouponCodes(Arrays.asList("value"));
        expectedResult.setDealTitle("kfpttest_zl5_02人餐");
        expectedResult.setDealValue(0.0);
        expectedResult.setDealId(0);
        expectedResult.setMessage("");
        expectedResult.setPoiid("159869278");
        expectedResult.setResult(0);

        // Run the test
        final MtCouponDoCheckRespDTO result = mtDevCouponServiceImplUnderTest.doCheck(mtCouponReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testPreCheck() {
        // Setup
        final MtCouponReqDTO mtCouponReqDTO = new MtCouponReqDTO();
        mtCouponReqDTO.setStoreGuid("storeGuid");
        mtCouponReqDTO.setCouponCode("couponCode");
        mtCouponReqDTO.setCount(0);
        mtCouponReqDTO.setErpId("erpId");
        mtCouponReqDTO.setErpName("eName");
        mtCouponReqDTO.setErpOrderId("erpOrderId");

        final MtCouponPreRespDTO expectedResult = new MtCouponPreRespDTO();
        expectedResult.setCount(0);
        expectedResult.setCouponBuyPrice(0.0);
        expectedResult.setCouponCode("131489950071");
        expectedResult.setIsVoucher(false);
        expectedResult.setCouponEndTime("2019-12-31");
        expectedResult.setDealBeginTime("2019-03-18");
        expectedResult.setDealId(0);
        final MtDealRespMenu mtDealRespMenu = new MtDealRespMenu();
        mtDealRespMenu.setContent("小吃1");
        mtDealRespMenu.setPrice("0.01");
        mtDealRespMenu.setSpecification("1 份");
        mtDealRespMenu.setTotal("0.01");
        mtDealRespMenu.setType("0");
        mtDealRespMenu.setNotDishes("false");
        mtDealRespMenu.setImages("[]");
        mtDealRespMenu.setDesc("");
        expectedResult.setDealPrice(0.0);
        expectedResult.setDealTitle("kfpttest_zl5_02人餐");
        expectedResult.setDealValue(0.0);
        expectedResult.setMessage("");
        expectedResult.setMinConsume(0);
        expectedResult.setResult(0);

        // Run the test
        final MtCouponPreRespDTO result = mtDevCouponServiceImplUnderTest.preCheck(mtCouponReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testCancelTicket() {
        // Setup
        final CouponDelReqDTO couponDelReqDTO = new CouponDelReqDTO();
        couponDelReqDTO.setCouponCode("couponCode");
        couponDelReqDTO.setErpId("erpId");
        couponDelReqDTO.setErpName("erpName");

        final MtDelCouponRespDTO expectedResult = new MtDelCouponRespDTO(0, "message");

        // Run the test
        final MtDelCouponRespDTO result = mtDevCouponServiceImplUnderTest.cancelTicket(couponDelReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testQueryGroupTradeDetail() {
        assertNull(mtDevCouponServiceImplUnderTest.queryGroupTradeDetail(new MtCouponReqDTO()));
    }
}
