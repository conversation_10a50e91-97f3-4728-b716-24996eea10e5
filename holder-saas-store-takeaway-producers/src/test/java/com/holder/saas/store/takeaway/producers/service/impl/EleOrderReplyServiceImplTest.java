package com.holder.saas.store.takeaway.producers.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holder.saas.store.takeaway.producers.entity.domain.EleAuthDO;
import com.holder.saas.store.takeaway.producers.service.EleAuthService;
import com.holder.saas.store.takeaway.producers.service.UnOrderMqService;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.takeaway.OwnApiResult;
import com.holderzone.saas.store.dto.takeaway.UnOrder;
import com.holderzone.saas.store.dto.takeaway.UnRemind;
import com.holderzone.saas.store.dto.takeaway.request.FoodListDTO;
import eleme.openapi.sdk.config.Config;
import eleme.openapi.sdk.oauth.response.Token;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class EleOrderReplyServiceImplTest {

    @Mock
    private EleAuthService mockEleAuthService;
    @Mock
    private Config mockConfig;
    @Mock
    private UnOrderMqService mockUnOrderMqService;

    private EleOrderReplyServiceImpl eleOrderReplyServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        eleOrderReplyServiceImplUnderTest = new EleOrderReplyServiceImpl(mockEleAuthService, mockConfig,
                mockUnOrderMqService);
        ReflectionTestUtils.setField(eleOrderReplyServiceImplUnderTest, "deliveryUrl", "deliveryUrl");
        ReflectionTestUtils.setField(eleOrderReplyServiceImplUnderTest, "cancelDeliveryUrl", "cancelDeliveryUrl");
    }

    @Test
    public void testReplyCancelOrder() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setShopName("ShopName");
        unOrder.setCbMsgType(0);
        unOrder.setReplyMsgType(0);
        unOrder.setOrderSubType(0);
        unOrder.setStoreGuid("ShopNo");
        unOrder.setOrderId("orderId");
        unOrder.setOrderDaySn("orderDaySn");
        unOrder.setOrderRemark("orderRemark");
        unOrder.setReserve(false);
        unOrder.setCustomerName("customerName");
        unOrder.setCustomerPhone("customerPhone");
        unOrder.setPrivacyPhone("privacyPhone");
        unOrder.setCustomerAddress("customerAddress");
        unOrder.setShipLatitude("ReceiverLat");
        unOrder.setShipLongitude("ReceiverLng");
        unOrder.setShipperId("id");
        unOrder.setShipperName("name");
        unOrder.setShipperPhone("phone");
        unOrder.setInvoiced(false);
        unOrder.setInvoiceType(0);
        unOrder.setInvoiceTitle("invoiceTitle");
        unOrder.setTaxpayerId("taxpayerId");
        unOrder.setTotal(new BigDecimal("0.00"));
        unOrder.setShipTotal(new BigDecimal("0.00"));
        unOrder.setPackageTotal(new BigDecimal("0.00"));
        unOrder.setDiscountTotal(new BigDecimal("0.00"));
        unOrder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unOrder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setCancelReplyMessage("cancelReplyMessage");
        unOrder.setRefundReplyMessage("refundReplyMessage");
        unOrder.setCancelReason("cancelReason");
        final UnRemind unRemind = new UnRemind();
        unRemind.setRemindId("remindId");
        unRemind.setReplyContent("replyContent");
        unOrder.setArrayOfUnRemind(Arrays.asList(unRemind));
        final FoodListDTO foodListDTO = new FoodListDTO();
        unOrder.setFoodLists(Arrays.asList(foodListDTO));

        // Configure EleAuthService.getToken(...).
        final Token token = new Token();
        token.setAccessToken("accessToken");
        token.setTokenType("tokenType");
        token.setExpires(0L);
        token.setRefreshToken("refreshToken");
        when(mockEleAuthService.getToken("ShopNo")).thenReturn(token);

        // Run the test
        eleOrderReplyServiceImplUnderTest.replyCancelOrder(unOrder);

        // Verify the results
    }

    @Test(expected = BusinessException.class)
    public void testReplyCancelOrder_EleAuthServiceGetTokenReturnsNull() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setShopName("ShopName");
        unOrder.setCbMsgType(0);
        unOrder.setReplyMsgType(0);
        unOrder.setOrderSubType(0);
        unOrder.setStoreGuid("ShopNo");
        unOrder.setOrderId("orderId");
        unOrder.setOrderDaySn("orderDaySn");
        unOrder.setOrderRemark("orderRemark");
        unOrder.setReserve(false);
        unOrder.setCustomerName("customerName");
        unOrder.setCustomerPhone("customerPhone");
        unOrder.setPrivacyPhone("privacyPhone");
        unOrder.setCustomerAddress("customerAddress");
        unOrder.setShipLatitude("ReceiverLat");
        unOrder.setShipLongitude("ReceiverLng");
        unOrder.setShipperId("id");
        unOrder.setShipperName("name");
        unOrder.setShipperPhone("phone");
        unOrder.setInvoiced(false);
        unOrder.setInvoiceType(0);
        unOrder.setInvoiceTitle("invoiceTitle");
        unOrder.setTaxpayerId("taxpayerId");
        unOrder.setTotal(new BigDecimal("0.00"));
        unOrder.setShipTotal(new BigDecimal("0.00"));
        unOrder.setPackageTotal(new BigDecimal("0.00"));
        unOrder.setDiscountTotal(new BigDecimal("0.00"));
        unOrder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unOrder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setCancelReplyMessage("cancelReplyMessage");
        unOrder.setRefundReplyMessage("refundReplyMessage");
        unOrder.setCancelReason("cancelReason");
        final UnRemind unRemind = new UnRemind();
        unRemind.setRemindId("remindId");
        unRemind.setReplyContent("replyContent");
        unOrder.setArrayOfUnRemind(Arrays.asList(unRemind));
        final FoodListDTO foodListDTO = new FoodListDTO();
        unOrder.setFoodLists(Arrays.asList(foodListDTO));

        when(mockEleAuthService.getToken("ShopNo")).thenReturn(null);

        // Run the test
        eleOrderReplyServiceImplUnderTest.replyCancelOrder(unOrder);
    }

    @Test
    public void testReplyConfirmOrder() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setShopName("ShopName");
        unOrder.setCbMsgType(0);
        unOrder.setReplyMsgType(0);
        unOrder.setOrderSubType(0);
        unOrder.setStoreGuid("ShopNo");
        unOrder.setOrderId("orderId");
        unOrder.setOrderDaySn("orderDaySn");
        unOrder.setOrderRemark("orderRemark");
        unOrder.setReserve(false);
        unOrder.setCustomerName("customerName");
        unOrder.setCustomerPhone("customerPhone");
        unOrder.setPrivacyPhone("privacyPhone");
        unOrder.setCustomerAddress("customerAddress");
        unOrder.setShipLatitude("ReceiverLat");
        unOrder.setShipLongitude("ReceiverLng");
        unOrder.setShipperId("id");
        unOrder.setShipperName("name");
        unOrder.setShipperPhone("phone");
        unOrder.setInvoiced(false);
        unOrder.setInvoiceType(0);
        unOrder.setInvoiceTitle("invoiceTitle");
        unOrder.setTaxpayerId("taxpayerId");
        unOrder.setTotal(new BigDecimal("0.00"));
        unOrder.setShipTotal(new BigDecimal("0.00"));
        unOrder.setPackageTotal(new BigDecimal("0.00"));
        unOrder.setDiscountTotal(new BigDecimal("0.00"));
        unOrder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unOrder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setCancelReplyMessage("cancelReplyMessage");
        unOrder.setRefundReplyMessage("refundReplyMessage");
        unOrder.setCancelReason("cancelReason");
        final UnRemind unRemind = new UnRemind();
        unRemind.setRemindId("remindId");
        unRemind.setReplyContent("replyContent");
        unOrder.setArrayOfUnRemind(Arrays.asList(unRemind));
        final FoodListDTO foodListDTO = new FoodListDTO();
        unOrder.setFoodLists(Arrays.asList(foodListDTO));

        // Configure EleAuthService.getOne(...).
        final EleAuthDO eleAuthDO = new EleAuthDO();
        eleAuthDO.setId(0L);
        eleAuthDO.setStoreGuid("storeGuid");
        eleAuthDO.setUserId(0L);
        eleAuthDO.setShopId(0L);
        eleAuthDO.setAccessToken("accessToken");
        when(mockEleAuthService.getOne(any(LambdaQueryWrapper.class))).thenReturn(eleAuthDO);

        // Configure EleAuthService.getToken(...).
        final Token token = new Token();
        token.setAccessToken("accessToken");
        token.setTokenType("tokenType");
        token.setExpires(0L);
        token.setRefreshToken("refreshToken");
        when(mockEleAuthService.getToken(0L)).thenReturn(token);

        // Run the test
        eleOrderReplyServiceImplUnderTest.replyConfirmOrder(unOrder);

        // Verify the results
    }

    @Test
    public void testReplyConfirmOrder_EleAuthServiceGetTokenReturnsNull() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setShopName("ShopName");
        unOrder.setCbMsgType(0);
        unOrder.setReplyMsgType(0);
        unOrder.setOrderSubType(0);
        unOrder.setStoreGuid("ShopNo");
        unOrder.setOrderId("orderId");
        unOrder.setOrderDaySn("orderDaySn");
        unOrder.setOrderRemark("orderRemark");
        unOrder.setReserve(false);
        unOrder.setCustomerName("customerName");
        unOrder.setCustomerPhone("customerPhone");
        unOrder.setPrivacyPhone("privacyPhone");
        unOrder.setCustomerAddress("customerAddress");
        unOrder.setShipLatitude("ReceiverLat");
        unOrder.setShipLongitude("ReceiverLng");
        unOrder.setShipperId("id");
        unOrder.setShipperName("name");
        unOrder.setShipperPhone("phone");
        unOrder.setInvoiced(false);
        unOrder.setInvoiceType(0);
        unOrder.setInvoiceTitle("invoiceTitle");
        unOrder.setTaxpayerId("taxpayerId");
        unOrder.setTotal(new BigDecimal("0.00"));
        unOrder.setShipTotal(new BigDecimal("0.00"));
        unOrder.setPackageTotal(new BigDecimal("0.00"));
        unOrder.setDiscountTotal(new BigDecimal("0.00"));
        unOrder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unOrder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setCancelReplyMessage("cancelReplyMessage");
        unOrder.setRefundReplyMessage("refundReplyMessage");
        unOrder.setCancelReason("cancelReason");
        final UnRemind unRemind = new UnRemind();
        unRemind.setRemindId("remindId");
        unRemind.setReplyContent("replyContent");
        unOrder.setArrayOfUnRemind(Arrays.asList(unRemind));
        final FoodListDTO foodListDTO = new FoodListDTO();
        unOrder.setFoodLists(Arrays.asList(foodListDTO));

        // Configure EleAuthService.getOne(...).
        final EleAuthDO eleAuthDO = new EleAuthDO();
        eleAuthDO.setId(0L);
        eleAuthDO.setStoreGuid("storeGuid");
        eleAuthDO.setUserId(0L);
        eleAuthDO.setShopId(0L);
        eleAuthDO.setAccessToken("accessToken");
        when(mockEleAuthService.getOne(any(LambdaQueryWrapper.class))).thenReturn(eleAuthDO);

        when(mockEleAuthService.getToken(0L)).thenReturn(null);

        // Run the test
        eleOrderReplyServiceImplUnderTest.replyConfirmOrder(unOrder);

        // Verify the results
    }

    @Test
    public void testReplyAgreeCancelOrder() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setShopName("ShopName");
        unOrder.setCbMsgType(0);
        unOrder.setReplyMsgType(0);
        unOrder.setOrderSubType(0);
        unOrder.setStoreGuid("ShopNo");
        unOrder.setOrderId("orderId");
        unOrder.setOrderDaySn("orderDaySn");
        unOrder.setOrderRemark("orderRemark");
        unOrder.setReserve(false);
        unOrder.setCustomerName("customerName");
        unOrder.setCustomerPhone("customerPhone");
        unOrder.setPrivacyPhone("privacyPhone");
        unOrder.setCustomerAddress("customerAddress");
        unOrder.setShipLatitude("ReceiverLat");
        unOrder.setShipLongitude("ReceiverLng");
        unOrder.setShipperId("id");
        unOrder.setShipperName("name");
        unOrder.setShipperPhone("phone");
        unOrder.setInvoiced(false);
        unOrder.setInvoiceType(0);
        unOrder.setInvoiceTitle("invoiceTitle");
        unOrder.setTaxpayerId("taxpayerId");
        unOrder.setTotal(new BigDecimal("0.00"));
        unOrder.setShipTotal(new BigDecimal("0.00"));
        unOrder.setPackageTotal(new BigDecimal("0.00"));
        unOrder.setDiscountTotal(new BigDecimal("0.00"));
        unOrder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unOrder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setCancelReplyMessage("cancelReplyMessage");
        unOrder.setRefundReplyMessage("refundReplyMessage");
        unOrder.setCancelReason("cancelReason");
        final UnRemind unRemind = new UnRemind();
        unRemind.setRemindId("remindId");
        unRemind.setReplyContent("replyContent");
        unOrder.setArrayOfUnRemind(Arrays.asList(unRemind));
        final FoodListDTO foodListDTO = new FoodListDTO();
        unOrder.setFoodLists(Arrays.asList(foodListDTO));

        // Configure EleAuthService.getOne(...).
        final EleAuthDO eleAuthDO = new EleAuthDO();
        eleAuthDO.setId(0L);
        eleAuthDO.setStoreGuid("storeGuid");
        eleAuthDO.setUserId(0L);
        eleAuthDO.setShopId(0L);
        eleAuthDO.setAccessToken("accessToken");
        when(mockEleAuthService.getOne(any(LambdaQueryWrapper.class))).thenReturn(eleAuthDO);

        // Configure EleAuthService.getToken(...).
        final Token token = new Token();
        token.setAccessToken("accessToken");
        token.setTokenType("tokenType");
        token.setExpires(0L);
        token.setRefreshToken("refreshToken");
        when(mockEleAuthService.getToken(0L)).thenReturn(token);

        // Run the test
        eleOrderReplyServiceImplUnderTest.replyAgreeCancelOrder(unOrder);

        // Verify the results
    }

    @Test
    public void testReplyAgreeCancelOrder_EleAuthServiceGetTokenReturnsNull() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setShopName("ShopName");
        unOrder.setCbMsgType(0);
        unOrder.setReplyMsgType(0);
        unOrder.setOrderSubType(0);
        unOrder.setStoreGuid("ShopNo");
        unOrder.setOrderId("orderId");
        unOrder.setOrderDaySn("orderDaySn");
        unOrder.setOrderRemark("orderRemark");
        unOrder.setReserve(false);
        unOrder.setCustomerName("customerName");
        unOrder.setCustomerPhone("customerPhone");
        unOrder.setPrivacyPhone("privacyPhone");
        unOrder.setCustomerAddress("customerAddress");
        unOrder.setShipLatitude("ReceiverLat");
        unOrder.setShipLongitude("ReceiverLng");
        unOrder.setShipperId("id");
        unOrder.setShipperName("name");
        unOrder.setShipperPhone("phone");
        unOrder.setInvoiced(false);
        unOrder.setInvoiceType(0);
        unOrder.setInvoiceTitle("invoiceTitle");
        unOrder.setTaxpayerId("taxpayerId");
        unOrder.setTotal(new BigDecimal("0.00"));
        unOrder.setShipTotal(new BigDecimal("0.00"));
        unOrder.setPackageTotal(new BigDecimal("0.00"));
        unOrder.setDiscountTotal(new BigDecimal("0.00"));
        unOrder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unOrder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setCancelReplyMessage("cancelReplyMessage");
        unOrder.setRefundReplyMessage("refundReplyMessage");
        unOrder.setCancelReason("cancelReason");
        final UnRemind unRemind = new UnRemind();
        unRemind.setRemindId("remindId");
        unRemind.setReplyContent("replyContent");
        unOrder.setArrayOfUnRemind(Arrays.asList(unRemind));
        final FoodListDTO foodListDTO = new FoodListDTO();
        unOrder.setFoodLists(Arrays.asList(foodListDTO));

        // Configure EleAuthService.getOne(...).
        final EleAuthDO eleAuthDO = new EleAuthDO();
        eleAuthDO.setId(0L);
        eleAuthDO.setStoreGuid("storeGuid");
        eleAuthDO.setUserId(0L);
        eleAuthDO.setShopId(0L);
        eleAuthDO.setAccessToken("accessToken");
        when(mockEleAuthService.getOne(any(LambdaQueryWrapper.class))).thenReturn(eleAuthDO);

        when(mockEleAuthService.getToken(0L)).thenReturn(null);

        // Run the test
        eleOrderReplyServiceImplUnderTest.replyAgreeCancelOrder(unOrder);

        // Verify the results
    }

    @Test
    public void testReplyDisagreeCancelOrder() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setShopName("ShopName");
        unOrder.setCbMsgType(0);
        unOrder.setReplyMsgType(0);
        unOrder.setOrderSubType(0);
        unOrder.setStoreGuid("ShopNo");
        unOrder.setOrderId("orderId");
        unOrder.setOrderDaySn("orderDaySn");
        unOrder.setOrderRemark("orderRemark");
        unOrder.setReserve(false);
        unOrder.setCustomerName("customerName");
        unOrder.setCustomerPhone("customerPhone");
        unOrder.setPrivacyPhone("privacyPhone");
        unOrder.setCustomerAddress("customerAddress");
        unOrder.setShipLatitude("ReceiverLat");
        unOrder.setShipLongitude("ReceiverLng");
        unOrder.setShipperId("id");
        unOrder.setShipperName("name");
        unOrder.setShipperPhone("phone");
        unOrder.setInvoiced(false);
        unOrder.setInvoiceType(0);
        unOrder.setInvoiceTitle("invoiceTitle");
        unOrder.setTaxpayerId("taxpayerId");
        unOrder.setTotal(new BigDecimal("0.00"));
        unOrder.setShipTotal(new BigDecimal("0.00"));
        unOrder.setPackageTotal(new BigDecimal("0.00"));
        unOrder.setDiscountTotal(new BigDecimal("0.00"));
        unOrder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unOrder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setCancelReplyMessage("cancelReplyMessage");
        unOrder.setRefundReplyMessage("refundReplyMessage");
        unOrder.setCancelReason("cancelReason");
        final UnRemind unRemind = new UnRemind();
        unRemind.setRemindId("remindId");
        unRemind.setReplyContent("replyContent");
        unOrder.setArrayOfUnRemind(Arrays.asList(unRemind));
        final FoodListDTO foodListDTO = new FoodListDTO();
        unOrder.setFoodLists(Arrays.asList(foodListDTO));

        // Configure EleAuthService.getOne(...).
        final EleAuthDO eleAuthDO = new EleAuthDO();
        eleAuthDO.setId(0L);
        eleAuthDO.setStoreGuid("storeGuid");
        eleAuthDO.setUserId(0L);
        eleAuthDO.setShopId(0L);
        eleAuthDO.setAccessToken("accessToken");
        when(mockEleAuthService.getOne(any(LambdaQueryWrapper.class))).thenReturn(eleAuthDO);

        // Configure EleAuthService.getToken(...).
        final Token token = new Token();
        token.setAccessToken("accessToken");
        token.setTokenType("tokenType");
        token.setExpires(0L);
        token.setRefreshToken("refreshToken");
        when(mockEleAuthService.getToken(0L)).thenReturn(token);

        // Run the test
        eleOrderReplyServiceImplUnderTest.replyDisagreeCancelOrder(unOrder);

        // Verify the results
    }

    @Test
    public void testReplyDisagreeCancelOrder_EleAuthServiceGetTokenReturnsNull() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setShopName("ShopName");
        unOrder.setCbMsgType(0);
        unOrder.setReplyMsgType(0);
        unOrder.setOrderSubType(0);
        unOrder.setStoreGuid("ShopNo");
        unOrder.setOrderId("orderId");
        unOrder.setOrderDaySn("orderDaySn");
        unOrder.setOrderRemark("orderRemark");
        unOrder.setReserve(false);
        unOrder.setCustomerName("customerName");
        unOrder.setCustomerPhone("customerPhone");
        unOrder.setPrivacyPhone("privacyPhone");
        unOrder.setCustomerAddress("customerAddress");
        unOrder.setShipLatitude("ReceiverLat");
        unOrder.setShipLongitude("ReceiverLng");
        unOrder.setShipperId("id");
        unOrder.setShipperName("name");
        unOrder.setShipperPhone("phone");
        unOrder.setInvoiced(false);
        unOrder.setInvoiceType(0);
        unOrder.setInvoiceTitle("invoiceTitle");
        unOrder.setTaxpayerId("taxpayerId");
        unOrder.setTotal(new BigDecimal("0.00"));
        unOrder.setShipTotal(new BigDecimal("0.00"));
        unOrder.setPackageTotal(new BigDecimal("0.00"));
        unOrder.setDiscountTotal(new BigDecimal("0.00"));
        unOrder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unOrder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setCancelReplyMessage("cancelReplyMessage");
        unOrder.setRefundReplyMessage("refundReplyMessage");
        unOrder.setCancelReason("cancelReason");
        final UnRemind unRemind = new UnRemind();
        unRemind.setRemindId("remindId");
        unRemind.setReplyContent("replyContent");
        unOrder.setArrayOfUnRemind(Arrays.asList(unRemind));
        final FoodListDTO foodListDTO = new FoodListDTO();
        unOrder.setFoodLists(Arrays.asList(foodListDTO));

        // Configure EleAuthService.getOne(...).
        final EleAuthDO eleAuthDO = new EleAuthDO();
        eleAuthDO.setId(0L);
        eleAuthDO.setStoreGuid("storeGuid");
        eleAuthDO.setUserId(0L);
        eleAuthDO.setShopId(0L);
        eleAuthDO.setAccessToken("accessToken");
        when(mockEleAuthService.getOne(any(LambdaQueryWrapper.class))).thenReturn(eleAuthDO);

        when(mockEleAuthService.getToken(0L)).thenReturn(null);

        // Run the test
        eleOrderReplyServiceImplUnderTest.replyDisagreeCancelOrder(unOrder);

        // Verify the results
    }

    @Test
    public void testReplyAgreeRefundOrder() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setShopName("ShopName");
        unOrder.setCbMsgType(0);
        unOrder.setReplyMsgType(0);
        unOrder.setOrderSubType(0);
        unOrder.setStoreGuid("ShopNo");
        unOrder.setOrderId("orderId");
        unOrder.setOrderDaySn("orderDaySn");
        unOrder.setOrderRemark("orderRemark");
        unOrder.setReserve(false);
        unOrder.setCustomerName("customerName");
        unOrder.setCustomerPhone("customerPhone");
        unOrder.setPrivacyPhone("privacyPhone");
        unOrder.setCustomerAddress("customerAddress");
        unOrder.setShipLatitude("ReceiverLat");
        unOrder.setShipLongitude("ReceiverLng");
        unOrder.setShipperId("id");
        unOrder.setShipperName("name");
        unOrder.setShipperPhone("phone");
        unOrder.setInvoiced(false);
        unOrder.setInvoiceType(0);
        unOrder.setInvoiceTitle("invoiceTitle");
        unOrder.setTaxpayerId("taxpayerId");
        unOrder.setTotal(new BigDecimal("0.00"));
        unOrder.setShipTotal(new BigDecimal("0.00"));
        unOrder.setPackageTotal(new BigDecimal("0.00"));
        unOrder.setDiscountTotal(new BigDecimal("0.00"));
        unOrder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unOrder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setCancelReplyMessage("cancelReplyMessage");
        unOrder.setRefundReplyMessage("refundReplyMessage");
        unOrder.setCancelReason("cancelReason");
        final UnRemind unRemind = new UnRemind();
        unRemind.setRemindId("remindId");
        unRemind.setReplyContent("replyContent");
        unOrder.setArrayOfUnRemind(Arrays.asList(unRemind));
        final FoodListDTO foodListDTO = new FoodListDTO();
        unOrder.setFoodLists(Arrays.asList(foodListDTO));

        // Configure EleAuthService.getOne(...).
        final EleAuthDO eleAuthDO = new EleAuthDO();
        eleAuthDO.setId(0L);
        eleAuthDO.setStoreGuid("storeGuid");
        eleAuthDO.setUserId(0L);
        eleAuthDO.setShopId(0L);
        eleAuthDO.setAccessToken("accessToken");
        when(mockEleAuthService.getOne(any(LambdaQueryWrapper.class))).thenReturn(eleAuthDO);

        // Configure EleAuthService.getToken(...).
        final Token token = new Token();
        token.setAccessToken("accessToken");
        token.setTokenType("tokenType");
        token.setExpires(0L);
        token.setRefreshToken("refreshToken");
        when(mockEleAuthService.getToken(0L)).thenReturn(token);

        // Run the test
        eleOrderReplyServiceImplUnderTest.replyAgreeRefundOrder(unOrder);

        // Verify the results
    }

    @Test
    public void testReplyAgreeRefundOrder_EleAuthServiceGetTokenReturnsNull() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setShopName("ShopName");
        unOrder.setCbMsgType(0);
        unOrder.setReplyMsgType(0);
        unOrder.setOrderSubType(0);
        unOrder.setStoreGuid("ShopNo");
        unOrder.setOrderId("orderId");
        unOrder.setOrderDaySn("orderDaySn");
        unOrder.setOrderRemark("orderRemark");
        unOrder.setReserve(false);
        unOrder.setCustomerName("customerName");
        unOrder.setCustomerPhone("customerPhone");
        unOrder.setPrivacyPhone("privacyPhone");
        unOrder.setCustomerAddress("customerAddress");
        unOrder.setShipLatitude("ReceiverLat");
        unOrder.setShipLongitude("ReceiverLng");
        unOrder.setShipperId("id");
        unOrder.setShipperName("name");
        unOrder.setShipperPhone("phone");
        unOrder.setInvoiced(false);
        unOrder.setInvoiceType(0);
        unOrder.setInvoiceTitle("invoiceTitle");
        unOrder.setTaxpayerId("taxpayerId");
        unOrder.setTotal(new BigDecimal("0.00"));
        unOrder.setShipTotal(new BigDecimal("0.00"));
        unOrder.setPackageTotal(new BigDecimal("0.00"));
        unOrder.setDiscountTotal(new BigDecimal("0.00"));
        unOrder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unOrder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setCancelReplyMessage("cancelReplyMessage");
        unOrder.setRefundReplyMessage("refundReplyMessage");
        unOrder.setCancelReason("cancelReason");
        final UnRemind unRemind = new UnRemind();
        unRemind.setRemindId("remindId");
        unRemind.setReplyContent("replyContent");
        unOrder.setArrayOfUnRemind(Arrays.asList(unRemind));
        final FoodListDTO foodListDTO = new FoodListDTO();
        unOrder.setFoodLists(Arrays.asList(foodListDTO));

        // Configure EleAuthService.getOne(...).
        final EleAuthDO eleAuthDO = new EleAuthDO();
        eleAuthDO.setId(0L);
        eleAuthDO.setStoreGuid("storeGuid");
        eleAuthDO.setUserId(0L);
        eleAuthDO.setShopId(0L);
        eleAuthDO.setAccessToken("accessToken");
        when(mockEleAuthService.getOne(any(LambdaQueryWrapper.class))).thenReturn(eleAuthDO);

        when(mockEleAuthService.getToken(0L)).thenReturn(null);

        // Run the test
        eleOrderReplyServiceImplUnderTest.replyAgreeRefundOrder(unOrder);

        // Verify the results
    }

    @Test
    public void testReplyDisagreeRefundOrder() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setShopName("ShopName");
        unOrder.setCbMsgType(0);
        unOrder.setReplyMsgType(0);
        unOrder.setOrderSubType(0);
        unOrder.setStoreGuid("ShopNo");
        unOrder.setOrderId("orderId");
        unOrder.setOrderDaySn("orderDaySn");
        unOrder.setOrderRemark("orderRemark");
        unOrder.setReserve(false);
        unOrder.setCustomerName("customerName");
        unOrder.setCustomerPhone("customerPhone");
        unOrder.setPrivacyPhone("privacyPhone");
        unOrder.setCustomerAddress("customerAddress");
        unOrder.setShipLatitude("ReceiverLat");
        unOrder.setShipLongitude("ReceiverLng");
        unOrder.setShipperId("id");
        unOrder.setShipperName("name");
        unOrder.setShipperPhone("phone");
        unOrder.setInvoiced(false);
        unOrder.setInvoiceType(0);
        unOrder.setInvoiceTitle("invoiceTitle");
        unOrder.setTaxpayerId("taxpayerId");
        unOrder.setTotal(new BigDecimal("0.00"));
        unOrder.setShipTotal(new BigDecimal("0.00"));
        unOrder.setPackageTotal(new BigDecimal("0.00"));
        unOrder.setDiscountTotal(new BigDecimal("0.00"));
        unOrder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unOrder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setCancelReplyMessage("cancelReplyMessage");
        unOrder.setRefundReplyMessage("refundReplyMessage");
        unOrder.setCancelReason("cancelReason");
        final UnRemind unRemind = new UnRemind();
        unRemind.setRemindId("remindId");
        unRemind.setReplyContent("replyContent");
        unOrder.setArrayOfUnRemind(Arrays.asList(unRemind));
        final FoodListDTO foodListDTO = new FoodListDTO();
        unOrder.setFoodLists(Arrays.asList(foodListDTO));

        // Configure EleAuthService.getOne(...).
        final EleAuthDO eleAuthDO = new EleAuthDO();
        eleAuthDO.setId(0L);
        eleAuthDO.setStoreGuid("storeGuid");
        eleAuthDO.setUserId(0L);
        eleAuthDO.setShopId(0L);
        eleAuthDO.setAccessToken("accessToken");
        when(mockEleAuthService.getOne(any(LambdaQueryWrapper.class))).thenReturn(eleAuthDO);

        // Configure EleAuthService.getToken(...).
        final Token token = new Token();
        token.setAccessToken("accessToken");
        token.setTokenType("tokenType");
        token.setExpires(0L);
        token.setRefreshToken("refreshToken");
        when(mockEleAuthService.getToken(0L)).thenReturn(token);

        // Run the test
        eleOrderReplyServiceImplUnderTest.replyDisagreeRefundOrder(unOrder);

        // Verify the results
    }

    @Test
    public void testReplyDisagreeRefundOrder_EleAuthServiceGetTokenReturnsNull() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setShopName("ShopName");
        unOrder.setCbMsgType(0);
        unOrder.setReplyMsgType(0);
        unOrder.setOrderSubType(0);
        unOrder.setStoreGuid("ShopNo");
        unOrder.setOrderId("orderId");
        unOrder.setOrderDaySn("orderDaySn");
        unOrder.setOrderRemark("orderRemark");
        unOrder.setReserve(false);
        unOrder.setCustomerName("customerName");
        unOrder.setCustomerPhone("customerPhone");
        unOrder.setPrivacyPhone("privacyPhone");
        unOrder.setCustomerAddress("customerAddress");
        unOrder.setShipLatitude("ReceiverLat");
        unOrder.setShipLongitude("ReceiverLng");
        unOrder.setShipperId("id");
        unOrder.setShipperName("name");
        unOrder.setShipperPhone("phone");
        unOrder.setInvoiced(false);
        unOrder.setInvoiceType(0);
        unOrder.setInvoiceTitle("invoiceTitle");
        unOrder.setTaxpayerId("taxpayerId");
        unOrder.setTotal(new BigDecimal("0.00"));
        unOrder.setShipTotal(new BigDecimal("0.00"));
        unOrder.setPackageTotal(new BigDecimal("0.00"));
        unOrder.setDiscountTotal(new BigDecimal("0.00"));
        unOrder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unOrder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setCancelReplyMessage("cancelReplyMessage");
        unOrder.setRefundReplyMessage("refundReplyMessage");
        unOrder.setCancelReason("cancelReason");
        final UnRemind unRemind = new UnRemind();
        unRemind.setRemindId("remindId");
        unRemind.setReplyContent("replyContent");
        unOrder.setArrayOfUnRemind(Arrays.asList(unRemind));
        final FoodListDTO foodListDTO = new FoodListDTO();
        unOrder.setFoodLists(Arrays.asList(foodListDTO));

        // Configure EleAuthService.getOne(...).
        final EleAuthDO eleAuthDO = new EleAuthDO();
        eleAuthDO.setId(0L);
        eleAuthDO.setStoreGuid("storeGuid");
        eleAuthDO.setUserId(0L);
        eleAuthDO.setShopId(0L);
        eleAuthDO.setAccessToken("accessToken");
        when(mockEleAuthService.getOne(any(LambdaQueryWrapper.class))).thenReturn(eleAuthDO);

        when(mockEleAuthService.getToken(0L)).thenReturn(null);

        // Run the test
        eleOrderReplyServiceImplUnderTest.replyDisagreeRefundOrder(unOrder);

        // Verify the results
    }

    @Test
    public void testReplyUrgeOrder() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setShopName("ShopName");
        unOrder.setCbMsgType(0);
        unOrder.setReplyMsgType(0);
        unOrder.setOrderSubType(0);
        unOrder.setStoreGuid("ShopNo");
        unOrder.setOrderId("orderId");
        unOrder.setOrderDaySn("orderDaySn");
        unOrder.setOrderRemark("orderRemark");
        unOrder.setReserve(false);
        unOrder.setCustomerName("customerName");
        unOrder.setCustomerPhone("customerPhone");
        unOrder.setPrivacyPhone("privacyPhone");
        unOrder.setCustomerAddress("customerAddress");
        unOrder.setShipLatitude("ReceiverLat");
        unOrder.setShipLongitude("ReceiverLng");
        unOrder.setShipperId("id");
        unOrder.setShipperName("name");
        unOrder.setShipperPhone("phone");
        unOrder.setInvoiced(false);
        unOrder.setInvoiceType(0);
        unOrder.setInvoiceTitle("invoiceTitle");
        unOrder.setTaxpayerId("taxpayerId");
        unOrder.setTotal(new BigDecimal("0.00"));
        unOrder.setShipTotal(new BigDecimal("0.00"));
        unOrder.setPackageTotal(new BigDecimal("0.00"));
        unOrder.setDiscountTotal(new BigDecimal("0.00"));
        unOrder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unOrder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setCancelReplyMessage("cancelReplyMessage");
        unOrder.setRefundReplyMessage("refundReplyMessage");
        unOrder.setCancelReason("cancelReason");
        final UnRemind unRemind = new UnRemind();
        unRemind.setRemindId("remindId");
        unRemind.setReplyContent("replyContent");
        unOrder.setArrayOfUnRemind(Arrays.asList(unRemind));
        final FoodListDTO foodListDTO = new FoodListDTO();
        unOrder.setFoodLists(Arrays.asList(foodListDTO));

        // Configure EleAuthService.getOne(...).
        final EleAuthDO eleAuthDO = new EleAuthDO();
        eleAuthDO.setId(0L);
        eleAuthDO.setStoreGuid("storeGuid");
        eleAuthDO.setUserId(0L);
        eleAuthDO.setShopId(0L);
        eleAuthDO.setAccessToken("accessToken");
        when(mockEleAuthService.getOne(any(LambdaQueryWrapper.class))).thenReturn(eleAuthDO);

        // Configure EleAuthService.getToken(...).
        final Token token = new Token();
        token.setAccessToken("accessToken");
        token.setTokenType("tokenType");
        token.setExpires(0L);
        token.setRefreshToken("refreshToken");
        when(mockEleAuthService.getToken(0L)).thenReturn(token);

        // Run the test
        eleOrderReplyServiceImplUnderTest.replyUrgeOrder(unOrder);

        // Verify the results
    }

    @Test
    public void testReplyUrgeOrder_EleAuthServiceGetTokenReturnsNull() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setShopName("ShopName");
        unOrder.setCbMsgType(0);
        unOrder.setReplyMsgType(0);
        unOrder.setOrderSubType(0);
        unOrder.setStoreGuid("ShopNo");
        unOrder.setOrderId("orderId");
        unOrder.setOrderDaySn("orderDaySn");
        unOrder.setOrderRemark("orderRemark");
        unOrder.setReserve(false);
        unOrder.setCustomerName("customerName");
        unOrder.setCustomerPhone("customerPhone");
        unOrder.setPrivacyPhone("privacyPhone");
        unOrder.setCustomerAddress("customerAddress");
        unOrder.setShipLatitude("ReceiverLat");
        unOrder.setShipLongitude("ReceiverLng");
        unOrder.setShipperId("id");
        unOrder.setShipperName("name");
        unOrder.setShipperPhone("phone");
        unOrder.setInvoiced(false);
        unOrder.setInvoiceType(0);
        unOrder.setInvoiceTitle("invoiceTitle");
        unOrder.setTaxpayerId("taxpayerId");
        unOrder.setTotal(new BigDecimal("0.00"));
        unOrder.setShipTotal(new BigDecimal("0.00"));
        unOrder.setPackageTotal(new BigDecimal("0.00"));
        unOrder.setDiscountTotal(new BigDecimal("0.00"));
        unOrder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unOrder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setCancelReplyMessage("cancelReplyMessage");
        unOrder.setRefundReplyMessage("refundReplyMessage");
        unOrder.setCancelReason("cancelReason");
        final UnRemind unRemind = new UnRemind();
        unRemind.setRemindId("remindId");
        unRemind.setReplyContent("replyContent");
        unOrder.setArrayOfUnRemind(Arrays.asList(unRemind));
        final FoodListDTO foodListDTO = new FoodListDTO();
        unOrder.setFoodLists(Arrays.asList(foodListDTO));

        // Configure EleAuthService.getOne(...).
        final EleAuthDO eleAuthDO = new EleAuthDO();
        eleAuthDO.setId(0L);
        eleAuthDO.setStoreGuid("storeGuid");
        eleAuthDO.setUserId(0L);
        eleAuthDO.setShopId(0L);
        eleAuthDO.setAccessToken("accessToken");
        when(mockEleAuthService.getOne(any(LambdaQueryWrapper.class))).thenReturn(eleAuthDO);

        when(mockEleAuthService.getToken(0L)).thenReturn(null);

        // Run the test
        eleOrderReplyServiceImplUnderTest.replyUrgeOrder(unOrder);

        // Verify the results
    }

    @Test
    public void testReplyDeliveryAccept() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setShopName("ShopName");
        unOrder.setCbMsgType(0);
        unOrder.setReplyMsgType(0);
        unOrder.setOrderSubType(0);
        unOrder.setStoreGuid("ShopNo");
        unOrder.setOrderId("orderId");
        unOrder.setOrderDaySn("orderDaySn");
        unOrder.setOrderRemark("orderRemark");
        unOrder.setReserve(false);
        unOrder.setCustomerName("customerName");
        unOrder.setCustomerPhone("customerPhone");
        unOrder.setPrivacyPhone("privacyPhone");
        unOrder.setCustomerAddress("customerAddress");
        unOrder.setShipLatitude("ReceiverLat");
        unOrder.setShipLongitude("ReceiverLng");
        unOrder.setShipperId("id");
        unOrder.setShipperName("name");
        unOrder.setShipperPhone("phone");
        unOrder.setInvoiced(false);
        unOrder.setInvoiceType(0);
        unOrder.setInvoiceTitle("invoiceTitle");
        unOrder.setTaxpayerId("taxpayerId");
        unOrder.setTotal(new BigDecimal("0.00"));
        unOrder.setShipTotal(new BigDecimal("0.00"));
        unOrder.setPackageTotal(new BigDecimal("0.00"));
        unOrder.setDiscountTotal(new BigDecimal("0.00"));
        unOrder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unOrder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setCancelReplyMessage("cancelReplyMessage");
        unOrder.setRefundReplyMessage("refundReplyMessage");
        unOrder.setCancelReason("cancelReason");
        final UnRemind unRemind = new UnRemind();
        unRemind.setRemindId("remindId");
        unRemind.setReplyContent("replyContent");
        unOrder.setArrayOfUnRemind(Arrays.asList(unRemind));
        final FoodListDTO foodListDTO = new FoodListDTO();
        unOrder.setFoodLists(Arrays.asList(foodListDTO));

        // Configure EleAuthService.getOne(...).
        final EleAuthDO eleAuthDO = new EleAuthDO();
        eleAuthDO.setId(0L);
        eleAuthDO.setStoreGuid("storeGuid");
        eleAuthDO.setUserId(0L);
        eleAuthDO.setShopId(0L);
        eleAuthDO.setAccessToken("accessToken");
        when(mockEleAuthService.getOne(any(LambdaQueryWrapper.class))).thenReturn(eleAuthDO);

        // Configure EleAuthService.getToken(...).
        final Token token = new Token();
        token.setAccessToken("accessToken");
        token.setTokenType("tokenType");
        token.setExpires(0L);
        token.setRefreshToken("refreshToken");
        when(mockEleAuthService.getToken(0L)).thenReturn(token);

        // Run the test
        eleOrderReplyServiceImplUnderTest.replyDeliveryAccept(unOrder);

        // Verify the results
    }

    @Test
    public void testReplyDeliveryAccept_EleAuthServiceGetTokenReturnsNull() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setShopName("ShopName");
        unOrder.setCbMsgType(0);
        unOrder.setReplyMsgType(0);
        unOrder.setOrderSubType(0);
        unOrder.setStoreGuid("ShopNo");
        unOrder.setOrderId("orderId");
        unOrder.setOrderDaySn("orderDaySn");
        unOrder.setOrderRemark("orderRemark");
        unOrder.setReserve(false);
        unOrder.setCustomerName("customerName");
        unOrder.setCustomerPhone("customerPhone");
        unOrder.setPrivacyPhone("privacyPhone");
        unOrder.setCustomerAddress("customerAddress");
        unOrder.setShipLatitude("ReceiverLat");
        unOrder.setShipLongitude("ReceiverLng");
        unOrder.setShipperId("id");
        unOrder.setShipperName("name");
        unOrder.setShipperPhone("phone");
        unOrder.setInvoiced(false);
        unOrder.setInvoiceType(0);
        unOrder.setInvoiceTitle("invoiceTitle");
        unOrder.setTaxpayerId("taxpayerId");
        unOrder.setTotal(new BigDecimal("0.00"));
        unOrder.setShipTotal(new BigDecimal("0.00"));
        unOrder.setPackageTotal(new BigDecimal("0.00"));
        unOrder.setDiscountTotal(new BigDecimal("0.00"));
        unOrder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unOrder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setCancelReplyMessage("cancelReplyMessage");
        unOrder.setRefundReplyMessage("refundReplyMessage");
        unOrder.setCancelReason("cancelReason");
        final UnRemind unRemind = new UnRemind();
        unRemind.setRemindId("remindId");
        unRemind.setReplyContent("replyContent");
        unOrder.setArrayOfUnRemind(Arrays.asList(unRemind));
        final FoodListDTO foodListDTO = new FoodListDTO();
        unOrder.setFoodLists(Arrays.asList(foodListDTO));

        // Configure EleAuthService.getOne(...).
        final EleAuthDO eleAuthDO = new EleAuthDO();
        eleAuthDO.setId(0L);
        eleAuthDO.setStoreGuid("storeGuid");
        eleAuthDO.setUserId(0L);
        eleAuthDO.setShopId(0L);
        eleAuthDO.setAccessToken("accessToken");
        when(mockEleAuthService.getOne(any(LambdaQueryWrapper.class))).thenReturn(eleAuthDO);

        when(mockEleAuthService.getToken(0L)).thenReturn(null);

        // Run the test
        eleOrderReplyServiceImplUnderTest.replyDeliveryAccept(unOrder);

        // Verify the results
    }

    @Test
    public void testReplyDeliveryStart() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setShopName("ShopName");
        unOrder.setCbMsgType(0);
        unOrder.setReplyMsgType(0);
        unOrder.setOrderSubType(0);
        unOrder.setStoreGuid("ShopNo");
        unOrder.setOrderId("orderId");
        unOrder.setOrderDaySn("orderDaySn");
        unOrder.setOrderRemark("orderRemark");
        unOrder.setReserve(false);
        unOrder.setCustomerName("customerName");
        unOrder.setCustomerPhone("customerPhone");
        unOrder.setPrivacyPhone("privacyPhone");
        unOrder.setCustomerAddress("customerAddress");
        unOrder.setShipLatitude("ReceiverLat");
        unOrder.setShipLongitude("ReceiverLng");
        unOrder.setShipperId("id");
        unOrder.setShipperName("name");
        unOrder.setShipperPhone("phone");
        unOrder.setInvoiced(false);
        unOrder.setInvoiceType(0);
        unOrder.setInvoiceTitle("invoiceTitle");
        unOrder.setTaxpayerId("taxpayerId");
        unOrder.setTotal(new BigDecimal("0.00"));
        unOrder.setShipTotal(new BigDecimal("0.00"));
        unOrder.setPackageTotal(new BigDecimal("0.00"));
        unOrder.setDiscountTotal(new BigDecimal("0.00"));
        unOrder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unOrder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setCancelReplyMessage("cancelReplyMessage");
        unOrder.setRefundReplyMessage("refundReplyMessage");
        unOrder.setCancelReason("cancelReason");
        final UnRemind unRemind = new UnRemind();
        unRemind.setRemindId("remindId");
        unRemind.setReplyContent("replyContent");
        unOrder.setArrayOfUnRemind(Arrays.asList(unRemind));
        final FoodListDTO foodListDTO = new FoodListDTO();
        unOrder.setFoodLists(Arrays.asList(foodListDTO));

        // Configure EleAuthService.getOne(...).
        final EleAuthDO eleAuthDO = new EleAuthDO();
        eleAuthDO.setId(0L);
        eleAuthDO.setStoreGuid("storeGuid");
        eleAuthDO.setUserId(0L);
        eleAuthDO.setShopId(0L);
        eleAuthDO.setAccessToken("accessToken");
        when(mockEleAuthService.getOne(any(LambdaQueryWrapper.class))).thenReturn(eleAuthDO);

        // Configure EleAuthService.getToken(...).
        final Token token = new Token();
        token.setAccessToken("accessToken");
        token.setTokenType("tokenType");
        token.setExpires(0L);
        token.setRefreshToken("refreshToken");
        when(mockEleAuthService.getToken(0L)).thenReturn(token);

        // Run the test
        eleOrderReplyServiceImplUnderTest.replyDeliveryStart(unOrder);

        // Verify the results
        // Confirm UnOrderMqService.sendUnOrder(...).
        final UnOrder unorder = new UnOrder();
        unorder.setShopName("ShopName");
        unorder.setCbMsgType(0);
        unorder.setReplyMsgType(0);
        unorder.setOrderSubType(0);
        unorder.setStoreGuid("ShopNo");
        unorder.setOrderId("orderId");
        unorder.setOrderDaySn("orderDaySn");
        unorder.setOrderRemark("orderRemark");
        unorder.setReserve(false);
        unorder.setCustomerName("customerName");
        unorder.setCustomerPhone("customerPhone");
        unorder.setPrivacyPhone("privacyPhone");
        unorder.setCustomerAddress("customerAddress");
        unorder.setShipLatitude("ReceiverLat");
        unorder.setShipLongitude("ReceiverLng");
        unorder.setShipperId("id");
        unorder.setShipperName("name");
        unorder.setShipperPhone("phone");
        unorder.setInvoiced(false);
        unorder.setInvoiceType(0);
        unorder.setInvoiceTitle("invoiceTitle");
        unorder.setTaxpayerId("taxpayerId");
        unorder.setTotal(new BigDecimal("0.00"));
        unorder.setShipTotal(new BigDecimal("0.00"));
        unorder.setPackageTotal(new BigDecimal("0.00"));
        unorder.setDiscountTotal(new BigDecimal("0.00"));
        unorder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unorder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unorder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unorder.setCancelReplyMessage("cancelReplyMessage");
        unorder.setRefundReplyMessage("refundReplyMessage");
        unorder.setCancelReason("cancelReason");
        final UnRemind unRemind1 = new UnRemind();
        unRemind1.setRemindId("remindId");
        unRemind1.setReplyContent("replyContent");
        unorder.setArrayOfUnRemind(Arrays.asList(unRemind1));
        final FoodListDTO foodListDTO1 = new FoodListDTO();
        unorder.setFoodLists(Arrays.asList(foodListDTO1));
        verify(mockUnOrderMqService).sendUnOrder(unorder);
    }

    @Test
    public void testReplyDeliveryStart_EleAuthServiceGetTokenReturnsNull() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setShopName("ShopName");
        unOrder.setCbMsgType(0);
        unOrder.setReplyMsgType(0);
        unOrder.setOrderSubType(0);
        unOrder.setStoreGuid("ShopNo");
        unOrder.setOrderId("orderId");
        unOrder.setOrderDaySn("orderDaySn");
        unOrder.setOrderRemark("orderRemark");
        unOrder.setReserve(false);
        unOrder.setCustomerName("customerName");
        unOrder.setCustomerPhone("customerPhone");
        unOrder.setPrivacyPhone("privacyPhone");
        unOrder.setCustomerAddress("customerAddress");
        unOrder.setShipLatitude("ReceiverLat");
        unOrder.setShipLongitude("ReceiverLng");
        unOrder.setShipperId("id");
        unOrder.setShipperName("name");
        unOrder.setShipperPhone("phone");
        unOrder.setInvoiced(false);
        unOrder.setInvoiceType(0);
        unOrder.setInvoiceTitle("invoiceTitle");
        unOrder.setTaxpayerId("taxpayerId");
        unOrder.setTotal(new BigDecimal("0.00"));
        unOrder.setShipTotal(new BigDecimal("0.00"));
        unOrder.setPackageTotal(new BigDecimal("0.00"));
        unOrder.setDiscountTotal(new BigDecimal("0.00"));
        unOrder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unOrder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setCancelReplyMessage("cancelReplyMessage");
        unOrder.setRefundReplyMessage("refundReplyMessage");
        unOrder.setCancelReason("cancelReason");
        final UnRemind unRemind = new UnRemind();
        unRemind.setRemindId("remindId");
        unRemind.setReplyContent("replyContent");
        unOrder.setArrayOfUnRemind(Arrays.asList(unRemind));
        final FoodListDTO foodListDTO = new FoodListDTO();
        unOrder.setFoodLists(Arrays.asList(foodListDTO));

        // Configure EleAuthService.getOne(...).
        final EleAuthDO eleAuthDO = new EleAuthDO();
        eleAuthDO.setId(0L);
        eleAuthDO.setStoreGuid("storeGuid");
        eleAuthDO.setUserId(0L);
        eleAuthDO.setShopId(0L);
        eleAuthDO.setAccessToken("accessToken");
        when(mockEleAuthService.getOne(any(LambdaQueryWrapper.class))).thenReturn(eleAuthDO);

        when(mockEleAuthService.getToken(0L)).thenReturn(null);

        // Run the test
        eleOrderReplyServiceImplUnderTest.replyDeliveryStart(unOrder);

        // Verify the results
        // Confirm UnOrderMqService.sendUnOrder(...).
        final UnOrder unorder = new UnOrder();
        unorder.setShopName("ShopName");
        unorder.setCbMsgType(0);
        unorder.setReplyMsgType(0);
        unorder.setOrderSubType(0);
        unorder.setStoreGuid("ShopNo");
        unorder.setOrderId("orderId");
        unorder.setOrderDaySn("orderDaySn");
        unorder.setOrderRemark("orderRemark");
        unorder.setReserve(false);
        unorder.setCustomerName("customerName");
        unorder.setCustomerPhone("customerPhone");
        unorder.setPrivacyPhone("privacyPhone");
        unorder.setCustomerAddress("customerAddress");
        unorder.setShipLatitude("ReceiverLat");
        unorder.setShipLongitude("ReceiverLng");
        unorder.setShipperId("id");
        unorder.setShipperName("name");
        unorder.setShipperPhone("phone");
        unorder.setInvoiced(false);
        unorder.setInvoiceType(0);
        unorder.setInvoiceTitle("invoiceTitle");
        unorder.setTaxpayerId("taxpayerId");
        unorder.setTotal(new BigDecimal("0.00"));
        unorder.setShipTotal(new BigDecimal("0.00"));
        unorder.setPackageTotal(new BigDecimal("0.00"));
        unorder.setDiscountTotal(new BigDecimal("0.00"));
        unorder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unorder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unorder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unorder.setCancelReplyMessage("cancelReplyMessage");
        unorder.setRefundReplyMessage("refundReplyMessage");
        unorder.setCancelReason("cancelReason");
        final UnRemind unRemind1 = new UnRemind();
        unRemind1.setRemindId("remindId");
        unRemind1.setReplyContent("replyContent");
        unorder.setArrayOfUnRemind(Arrays.asList(unRemind1));
        final FoodListDTO foodListDTO1 = new FoodListDTO();
        unorder.setFoodLists(Arrays.asList(foodListDTO1));
        verify(mockUnOrderMqService).sendUnOrder(unorder);
    }

    @Test
    public void testReplyDeliveryCancel() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setShopName("ShopName");
        unOrder.setCbMsgType(0);
        unOrder.setReplyMsgType(0);
        unOrder.setOrderSubType(0);
        unOrder.setStoreGuid("ShopNo");
        unOrder.setOrderId("orderId");
        unOrder.setOrderDaySn("orderDaySn");
        unOrder.setOrderRemark("orderRemark");
        unOrder.setReserve(false);
        unOrder.setCustomerName("customerName");
        unOrder.setCustomerPhone("customerPhone");
        unOrder.setPrivacyPhone("privacyPhone");
        unOrder.setCustomerAddress("customerAddress");
        unOrder.setShipLatitude("ReceiverLat");
        unOrder.setShipLongitude("ReceiverLng");
        unOrder.setShipperId("id");
        unOrder.setShipperName("name");
        unOrder.setShipperPhone("phone");
        unOrder.setInvoiced(false);
        unOrder.setInvoiceType(0);
        unOrder.setInvoiceTitle("invoiceTitle");
        unOrder.setTaxpayerId("taxpayerId");
        unOrder.setTotal(new BigDecimal("0.00"));
        unOrder.setShipTotal(new BigDecimal("0.00"));
        unOrder.setPackageTotal(new BigDecimal("0.00"));
        unOrder.setDiscountTotal(new BigDecimal("0.00"));
        unOrder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unOrder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setCancelReplyMessage("cancelReplyMessage");
        unOrder.setRefundReplyMessage("refundReplyMessage");
        unOrder.setCancelReason("cancelReason");
        final UnRemind unRemind = new UnRemind();
        unRemind.setRemindId("remindId");
        unRemind.setReplyContent("replyContent");
        unOrder.setArrayOfUnRemind(Arrays.asList(unRemind));
        final FoodListDTO foodListDTO = new FoodListDTO();
        unOrder.setFoodLists(Arrays.asList(foodListDTO));

        // Configure EleAuthService.getOne(...).
        final EleAuthDO eleAuthDO = new EleAuthDO();
        eleAuthDO.setId(0L);
        eleAuthDO.setStoreGuid("storeGuid");
        eleAuthDO.setUserId(0L);
        eleAuthDO.setShopId(0L);
        eleAuthDO.setAccessToken("accessToken");
        when(mockEleAuthService.getOne(any(LambdaQueryWrapper.class))).thenReturn(eleAuthDO);

        // Configure EleAuthService.getToken(...).
        final Token token = new Token();
        token.setAccessToken("accessToken");
        token.setTokenType("tokenType");
        token.setExpires(0L);
        token.setRefreshToken("refreshToken");
        when(mockEleAuthService.getToken(0L)).thenReturn(token);

        // Run the test
        eleOrderReplyServiceImplUnderTest.replyDeliveryCancel(unOrder);

        // Verify the results
        // Confirm UnOrderMqService.sendUnOrder(...).
        final UnOrder unorder = new UnOrder();
        unorder.setShopName("ShopName");
        unorder.setCbMsgType(0);
        unorder.setReplyMsgType(0);
        unorder.setOrderSubType(0);
        unorder.setStoreGuid("ShopNo");
        unorder.setOrderId("orderId");
        unorder.setOrderDaySn("orderDaySn");
        unorder.setOrderRemark("orderRemark");
        unorder.setReserve(false);
        unorder.setCustomerName("customerName");
        unorder.setCustomerPhone("customerPhone");
        unorder.setPrivacyPhone("privacyPhone");
        unorder.setCustomerAddress("customerAddress");
        unorder.setShipLatitude("ReceiverLat");
        unorder.setShipLongitude("ReceiverLng");
        unorder.setShipperId("id");
        unorder.setShipperName("name");
        unorder.setShipperPhone("phone");
        unorder.setInvoiced(false);
        unorder.setInvoiceType(0);
        unorder.setInvoiceTitle("invoiceTitle");
        unorder.setTaxpayerId("taxpayerId");
        unorder.setTotal(new BigDecimal("0.00"));
        unorder.setShipTotal(new BigDecimal("0.00"));
        unorder.setPackageTotal(new BigDecimal("0.00"));
        unorder.setDiscountTotal(new BigDecimal("0.00"));
        unorder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unorder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unorder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unorder.setCancelReplyMessage("cancelReplyMessage");
        unorder.setRefundReplyMessage("refundReplyMessage");
        unorder.setCancelReason("cancelReason");
        final UnRemind unRemind1 = new UnRemind();
        unRemind1.setRemindId("remindId");
        unRemind1.setReplyContent("replyContent");
        unorder.setArrayOfUnRemind(Arrays.asList(unRemind1));
        final FoodListDTO foodListDTO1 = new FoodListDTO();
        unorder.setFoodLists(Arrays.asList(foodListDTO1));
        verify(mockUnOrderMqService).sendUnOrder(unorder);
    }

    @Test
    public void testReplyDeliveryCancel_EleAuthServiceGetTokenReturnsNull() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setShopName("ShopName");
        unOrder.setCbMsgType(0);
        unOrder.setReplyMsgType(0);
        unOrder.setOrderSubType(0);
        unOrder.setStoreGuid("ShopNo");
        unOrder.setOrderId("orderId");
        unOrder.setOrderDaySn("orderDaySn");
        unOrder.setOrderRemark("orderRemark");
        unOrder.setReserve(false);
        unOrder.setCustomerName("customerName");
        unOrder.setCustomerPhone("customerPhone");
        unOrder.setPrivacyPhone("privacyPhone");
        unOrder.setCustomerAddress("customerAddress");
        unOrder.setShipLatitude("ReceiverLat");
        unOrder.setShipLongitude("ReceiverLng");
        unOrder.setShipperId("id");
        unOrder.setShipperName("name");
        unOrder.setShipperPhone("phone");
        unOrder.setInvoiced(false);
        unOrder.setInvoiceType(0);
        unOrder.setInvoiceTitle("invoiceTitle");
        unOrder.setTaxpayerId("taxpayerId");
        unOrder.setTotal(new BigDecimal("0.00"));
        unOrder.setShipTotal(new BigDecimal("0.00"));
        unOrder.setPackageTotal(new BigDecimal("0.00"));
        unOrder.setDiscountTotal(new BigDecimal("0.00"));
        unOrder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unOrder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setCancelReplyMessage("cancelReplyMessage");
        unOrder.setRefundReplyMessage("refundReplyMessage");
        unOrder.setCancelReason("cancelReason");
        final UnRemind unRemind = new UnRemind();
        unRemind.setRemindId("remindId");
        unRemind.setReplyContent("replyContent");
        unOrder.setArrayOfUnRemind(Arrays.asList(unRemind));
        final FoodListDTO foodListDTO = new FoodListDTO();
        unOrder.setFoodLists(Arrays.asList(foodListDTO));

        // Configure EleAuthService.getOne(...).
        final EleAuthDO eleAuthDO = new EleAuthDO();
        eleAuthDO.setId(0L);
        eleAuthDO.setStoreGuid("storeGuid");
        eleAuthDO.setUserId(0L);
        eleAuthDO.setShopId(0L);
        eleAuthDO.setAccessToken("accessToken");
        when(mockEleAuthService.getOne(any(LambdaQueryWrapper.class))).thenReturn(eleAuthDO);

        when(mockEleAuthService.getToken(0L)).thenReturn(null);

        // Run the test
        eleOrderReplyServiceImplUnderTest.replyDeliveryCancel(unOrder);

        // Verify the results
        // Confirm UnOrderMqService.sendUnOrder(...).
        final UnOrder unorder = new UnOrder();
        unorder.setShopName("ShopName");
        unorder.setCbMsgType(0);
        unorder.setReplyMsgType(0);
        unorder.setOrderSubType(0);
        unorder.setStoreGuid("ShopNo");
        unorder.setOrderId("orderId");
        unorder.setOrderDaySn("orderDaySn");
        unorder.setOrderRemark("orderRemark");
        unorder.setReserve(false);
        unorder.setCustomerName("customerName");
        unorder.setCustomerPhone("customerPhone");
        unorder.setPrivacyPhone("privacyPhone");
        unorder.setCustomerAddress("customerAddress");
        unorder.setShipLatitude("ReceiverLat");
        unorder.setShipLongitude("ReceiverLng");
        unorder.setShipperId("id");
        unorder.setShipperName("name");
        unorder.setShipperPhone("phone");
        unorder.setInvoiced(false);
        unorder.setInvoiceType(0);
        unorder.setInvoiceTitle("invoiceTitle");
        unorder.setTaxpayerId("taxpayerId");
        unorder.setTotal(new BigDecimal("0.00"));
        unorder.setShipTotal(new BigDecimal("0.00"));
        unorder.setPackageTotal(new BigDecimal("0.00"));
        unorder.setDiscountTotal(new BigDecimal("0.00"));
        unorder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unorder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unorder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unorder.setCancelReplyMessage("cancelReplyMessage");
        unorder.setRefundReplyMessage("refundReplyMessage");
        unorder.setCancelReason("cancelReason");
        final UnRemind unRemind1 = new UnRemind();
        unRemind1.setRemindId("remindId");
        unRemind1.setReplyContent("replyContent");
        unorder.setArrayOfUnRemind(Arrays.asList(unRemind1));
        final FoodListDTO foodListDTO1 = new FoodListDTO();
        unorder.setFoodLists(Arrays.asList(foodListDTO1));
        verify(mockUnOrderMqService).sendUnOrder(unorder);
    }

    @Test
    public void testReplyDeliveryComplete() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setShopName("ShopName");
        unOrder.setCbMsgType(0);
        unOrder.setReplyMsgType(0);
        unOrder.setOrderSubType(0);
        unOrder.setStoreGuid("ShopNo");
        unOrder.setOrderId("orderId");
        unOrder.setOrderDaySn("orderDaySn");
        unOrder.setOrderRemark("orderRemark");
        unOrder.setReserve(false);
        unOrder.setCustomerName("customerName");
        unOrder.setCustomerPhone("customerPhone");
        unOrder.setPrivacyPhone("privacyPhone");
        unOrder.setCustomerAddress("customerAddress");
        unOrder.setShipLatitude("ReceiverLat");
        unOrder.setShipLongitude("ReceiverLng");
        unOrder.setShipperId("id");
        unOrder.setShipperName("name");
        unOrder.setShipperPhone("phone");
        unOrder.setInvoiced(false);
        unOrder.setInvoiceType(0);
        unOrder.setInvoiceTitle("invoiceTitle");
        unOrder.setTaxpayerId("taxpayerId");
        unOrder.setTotal(new BigDecimal("0.00"));
        unOrder.setShipTotal(new BigDecimal("0.00"));
        unOrder.setPackageTotal(new BigDecimal("0.00"));
        unOrder.setDiscountTotal(new BigDecimal("0.00"));
        unOrder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unOrder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setCancelReplyMessage("cancelReplyMessage");
        unOrder.setRefundReplyMessage("refundReplyMessage");
        unOrder.setCancelReason("cancelReason");
        final UnRemind unRemind = new UnRemind();
        unRemind.setRemindId("remindId");
        unRemind.setReplyContent("replyContent");
        unOrder.setArrayOfUnRemind(Arrays.asList(unRemind));
        final FoodListDTO foodListDTO = new FoodListDTO();
        unOrder.setFoodLists(Arrays.asList(foodListDTO));

        // Configure EleAuthService.getOne(...).
        final EleAuthDO eleAuthDO = new EleAuthDO();
        eleAuthDO.setId(0L);
        eleAuthDO.setStoreGuid("storeGuid");
        eleAuthDO.setUserId(0L);
        eleAuthDO.setShopId(0L);
        eleAuthDO.setAccessToken("accessToken");
        when(mockEleAuthService.getOne(any(LambdaQueryWrapper.class))).thenReturn(eleAuthDO);

        // Configure EleAuthService.getToken(...).
        final Token token = new Token();
        token.setAccessToken("accessToken");
        token.setTokenType("tokenType");
        token.setExpires(0L);
        token.setRefreshToken("refreshToken");
        when(mockEleAuthService.getToken(0L)).thenReturn(token);

        // Run the test
        eleOrderReplyServiceImplUnderTest.replyDeliveryComplete(unOrder);

        // Verify the results
        // Confirm UnOrderMqService.sendUnOrder(...).
        final UnOrder unorder = new UnOrder();
        unorder.setShopName("ShopName");
        unorder.setCbMsgType(0);
        unorder.setReplyMsgType(0);
        unorder.setOrderSubType(0);
        unorder.setStoreGuid("ShopNo");
        unorder.setOrderId("orderId");
        unorder.setOrderDaySn("orderDaySn");
        unorder.setOrderRemark("orderRemark");
        unorder.setReserve(false);
        unorder.setCustomerName("customerName");
        unorder.setCustomerPhone("customerPhone");
        unorder.setPrivacyPhone("privacyPhone");
        unorder.setCustomerAddress("customerAddress");
        unorder.setShipLatitude("ReceiverLat");
        unorder.setShipLongitude("ReceiverLng");
        unorder.setShipperId("id");
        unorder.setShipperName("name");
        unorder.setShipperPhone("phone");
        unorder.setInvoiced(false);
        unorder.setInvoiceType(0);
        unorder.setInvoiceTitle("invoiceTitle");
        unorder.setTaxpayerId("taxpayerId");
        unorder.setTotal(new BigDecimal("0.00"));
        unorder.setShipTotal(new BigDecimal("0.00"));
        unorder.setPackageTotal(new BigDecimal("0.00"));
        unorder.setDiscountTotal(new BigDecimal("0.00"));
        unorder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unorder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unorder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unorder.setCancelReplyMessage("cancelReplyMessage");
        unorder.setRefundReplyMessage("refundReplyMessage");
        unorder.setCancelReason("cancelReason");
        final UnRemind unRemind1 = new UnRemind();
        unRemind1.setRemindId("remindId");
        unRemind1.setReplyContent("replyContent");
        unorder.setArrayOfUnRemind(Arrays.asList(unRemind1));
        final FoodListDTO foodListDTO1 = new FoodListDTO();
        unorder.setFoodLists(Arrays.asList(foodListDTO1));
        verify(mockUnOrderMqService).sendUnOrder(unorder);
    }

    @Test
    public void testReplyDeliveryComplete_EleAuthServiceGetTokenReturnsNull() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setShopName("ShopName");
        unOrder.setCbMsgType(0);
        unOrder.setReplyMsgType(0);
        unOrder.setOrderSubType(0);
        unOrder.setStoreGuid("ShopNo");
        unOrder.setOrderId("orderId");
        unOrder.setOrderDaySn("orderDaySn");
        unOrder.setOrderRemark("orderRemark");
        unOrder.setReserve(false);
        unOrder.setCustomerName("customerName");
        unOrder.setCustomerPhone("customerPhone");
        unOrder.setPrivacyPhone("privacyPhone");
        unOrder.setCustomerAddress("customerAddress");
        unOrder.setShipLatitude("ReceiverLat");
        unOrder.setShipLongitude("ReceiverLng");
        unOrder.setShipperId("id");
        unOrder.setShipperName("name");
        unOrder.setShipperPhone("phone");
        unOrder.setInvoiced(false);
        unOrder.setInvoiceType(0);
        unOrder.setInvoiceTitle("invoiceTitle");
        unOrder.setTaxpayerId("taxpayerId");
        unOrder.setTotal(new BigDecimal("0.00"));
        unOrder.setShipTotal(new BigDecimal("0.00"));
        unOrder.setPackageTotal(new BigDecimal("0.00"));
        unOrder.setDiscountTotal(new BigDecimal("0.00"));
        unOrder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unOrder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setCancelReplyMessage("cancelReplyMessage");
        unOrder.setRefundReplyMessage("refundReplyMessage");
        unOrder.setCancelReason("cancelReason");
        final UnRemind unRemind = new UnRemind();
        unRemind.setRemindId("remindId");
        unRemind.setReplyContent("replyContent");
        unOrder.setArrayOfUnRemind(Arrays.asList(unRemind));
        final FoodListDTO foodListDTO = new FoodListDTO();
        unOrder.setFoodLists(Arrays.asList(foodListDTO));

        // Configure EleAuthService.getOne(...).
        final EleAuthDO eleAuthDO = new EleAuthDO();
        eleAuthDO.setId(0L);
        eleAuthDO.setStoreGuid("storeGuid");
        eleAuthDO.setUserId(0L);
        eleAuthDO.setShopId(0L);
        eleAuthDO.setAccessToken("accessToken");
        when(mockEleAuthService.getOne(any(LambdaQueryWrapper.class))).thenReturn(eleAuthDO);

        when(mockEleAuthService.getToken(0L)).thenReturn(null);

        // Run the test
        eleOrderReplyServiceImplUnderTest.replyDeliveryComplete(unOrder);

        // Verify the results
        // Confirm UnOrderMqService.sendUnOrder(...).
        final UnOrder unorder = new UnOrder();
        unorder.setShopName("ShopName");
        unorder.setCbMsgType(0);
        unorder.setReplyMsgType(0);
        unorder.setOrderSubType(0);
        unorder.setStoreGuid("ShopNo");
        unorder.setOrderId("orderId");
        unorder.setOrderDaySn("orderDaySn");
        unorder.setOrderRemark("orderRemark");
        unorder.setReserve(false);
        unorder.setCustomerName("customerName");
        unorder.setCustomerPhone("customerPhone");
        unorder.setPrivacyPhone("privacyPhone");
        unorder.setCustomerAddress("customerAddress");
        unorder.setShipLatitude("ReceiverLat");
        unorder.setShipLongitude("ReceiverLng");
        unorder.setShipperId("id");
        unorder.setShipperName("name");
        unorder.setShipperPhone("phone");
        unorder.setInvoiced(false);
        unorder.setInvoiceType(0);
        unorder.setInvoiceTitle("invoiceTitle");
        unorder.setTaxpayerId("taxpayerId");
        unorder.setTotal(new BigDecimal("0.00"));
        unorder.setShipTotal(new BigDecimal("0.00"));
        unorder.setPackageTotal(new BigDecimal("0.00"));
        unorder.setDiscountTotal(new BigDecimal("0.00"));
        unorder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unorder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unorder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unorder.setCancelReplyMessage("cancelReplyMessage");
        unorder.setRefundReplyMessage("refundReplyMessage");
        unorder.setCancelReason("cancelReason");
        final UnRemind unRemind1 = new UnRemind();
        unRemind1.setRemindId("remindId");
        unRemind1.setReplyContent("replyContent");
        unorder.setArrayOfUnRemind(Arrays.asList(unRemind1));
        final FoodListDTO foodListDTO1 = new FoodListDTO();
        unorder.setFoodLists(Arrays.asList(foodListDTO1));
        verify(mockUnOrderMqService).sendUnOrder(unorder);
    }

    @Test
    public void testReplyRiderPosition() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setShopName("ShopName");
        unOrder.setCbMsgType(0);
        unOrder.setReplyMsgType(0);
        unOrder.setOrderSubType(0);
        unOrder.setStoreGuid("ShopNo");
        unOrder.setOrderId("orderId");
        unOrder.setOrderDaySn("orderDaySn");
        unOrder.setOrderRemark("orderRemark");
        unOrder.setReserve(false);
        unOrder.setCustomerName("customerName");
        unOrder.setCustomerPhone("customerPhone");
        unOrder.setPrivacyPhone("privacyPhone");
        unOrder.setCustomerAddress("customerAddress");
        unOrder.setShipLatitude("ReceiverLat");
        unOrder.setShipLongitude("ReceiverLng");
        unOrder.setShipperId("id");
        unOrder.setShipperName("name");
        unOrder.setShipperPhone("phone");
        unOrder.setInvoiced(false);
        unOrder.setInvoiceType(0);
        unOrder.setInvoiceTitle("invoiceTitle");
        unOrder.setTaxpayerId("taxpayerId");
        unOrder.setTotal(new BigDecimal("0.00"));
        unOrder.setShipTotal(new BigDecimal("0.00"));
        unOrder.setPackageTotal(new BigDecimal("0.00"));
        unOrder.setDiscountTotal(new BigDecimal("0.00"));
        unOrder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unOrder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setCancelReplyMessage("cancelReplyMessage");
        unOrder.setRefundReplyMessage("refundReplyMessage");
        unOrder.setCancelReason("cancelReason");
        final UnRemind unRemind = new UnRemind();
        unRemind.setRemindId("remindId");
        unRemind.setReplyContent("replyContent");
        unOrder.setArrayOfUnRemind(Arrays.asList(unRemind));
        final FoodListDTO foodListDTO = new FoodListDTO();
        unOrder.setFoodLists(Arrays.asList(foodListDTO));

        // Configure EleAuthService.getOne(...).
        final EleAuthDO eleAuthDO = new EleAuthDO();
        eleAuthDO.setId(0L);
        eleAuthDO.setStoreGuid("storeGuid");
        eleAuthDO.setUserId(0L);
        eleAuthDO.setShopId(0L);
        eleAuthDO.setAccessToken("accessToken");
        when(mockEleAuthService.getOne(any(LambdaQueryWrapper.class))).thenReturn(eleAuthDO);

        // Configure EleAuthService.getToken(...).
        final Token token = new Token();
        token.setAccessToken("accessToken");
        token.setTokenType("tokenType");
        token.setExpires(0L);
        token.setRefreshToken("refreshToken");
        when(mockEleAuthService.getToken(0L)).thenReturn(token);

        // Run the test
        eleOrderReplyServiceImplUnderTest.replyRiderPosition(unOrder);

        // Verify the results
    }

    @Test
    public void testReplyRiderPosition_EleAuthServiceGetTokenReturnsNull() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setShopName("ShopName");
        unOrder.setCbMsgType(0);
        unOrder.setReplyMsgType(0);
        unOrder.setOrderSubType(0);
        unOrder.setStoreGuid("ShopNo");
        unOrder.setOrderId("orderId");
        unOrder.setOrderDaySn("orderDaySn");
        unOrder.setOrderRemark("orderRemark");
        unOrder.setReserve(false);
        unOrder.setCustomerName("customerName");
        unOrder.setCustomerPhone("customerPhone");
        unOrder.setPrivacyPhone("privacyPhone");
        unOrder.setCustomerAddress("customerAddress");
        unOrder.setShipLatitude("ReceiverLat");
        unOrder.setShipLongitude("ReceiverLng");
        unOrder.setShipperId("id");
        unOrder.setShipperName("name");
        unOrder.setShipperPhone("phone");
        unOrder.setInvoiced(false);
        unOrder.setInvoiceType(0);
        unOrder.setInvoiceTitle("invoiceTitle");
        unOrder.setTaxpayerId("taxpayerId");
        unOrder.setTotal(new BigDecimal("0.00"));
        unOrder.setShipTotal(new BigDecimal("0.00"));
        unOrder.setPackageTotal(new BigDecimal("0.00"));
        unOrder.setDiscountTotal(new BigDecimal("0.00"));
        unOrder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unOrder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setCancelReplyMessage("cancelReplyMessage");
        unOrder.setRefundReplyMessage("refundReplyMessage");
        unOrder.setCancelReason("cancelReason");
        final UnRemind unRemind = new UnRemind();
        unRemind.setRemindId("remindId");
        unRemind.setReplyContent("replyContent");
        unOrder.setArrayOfUnRemind(Arrays.asList(unRemind));
        final FoodListDTO foodListDTO = new FoodListDTO();
        unOrder.setFoodLists(Arrays.asList(foodListDTO));

        // Configure EleAuthService.getOne(...).
        final EleAuthDO eleAuthDO = new EleAuthDO();
        eleAuthDO.setId(0L);
        eleAuthDO.setStoreGuid("storeGuid");
        eleAuthDO.setUserId(0L);
        eleAuthDO.setShopId(0L);
        eleAuthDO.setAccessToken("accessToken");
        when(mockEleAuthService.getOne(any(LambdaQueryWrapper.class))).thenReturn(eleAuthDO);

        when(mockEleAuthService.getToken(0L)).thenReturn(null);

        // Run the test
        eleOrderReplyServiceImplUnderTest.replyRiderPosition(unOrder);

        // Verify the results
    }

    @Test
    public void testStartDelivery() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setShopName("ShopName");
        unOrder.setCbMsgType(0);
        unOrder.setReplyMsgType(0);
        unOrder.setOrderSubType(0);
        unOrder.setStoreGuid("ShopNo");
        unOrder.setOrderId("orderId");
        unOrder.setOrderDaySn("orderDaySn");
        unOrder.setOrderRemark("orderRemark");
        unOrder.setReserve(false);
        unOrder.setCustomerName("customerName");
        unOrder.setCustomerPhone("customerPhone");
        unOrder.setPrivacyPhone("privacyPhone");
        unOrder.setCustomerAddress("customerAddress");
        unOrder.setShipLatitude("ReceiverLat");
        unOrder.setShipLongitude("ReceiverLng");
        unOrder.setShipperId("id");
        unOrder.setShipperName("name");
        unOrder.setShipperPhone("phone");
        unOrder.setInvoiced(false);
        unOrder.setInvoiceType(0);
        unOrder.setInvoiceTitle("invoiceTitle");
        unOrder.setTaxpayerId("taxpayerId");
        unOrder.setTotal(new BigDecimal("0.00"));
        unOrder.setShipTotal(new BigDecimal("0.00"));
        unOrder.setPackageTotal(new BigDecimal("0.00"));
        unOrder.setDiscountTotal(new BigDecimal("0.00"));
        unOrder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unOrder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setCancelReplyMessage("cancelReplyMessage");
        unOrder.setRefundReplyMessage("refundReplyMessage");
        unOrder.setCancelReason("cancelReason");
        final UnRemind unRemind = new UnRemind();
        unRemind.setRemindId("remindId");
        unRemind.setReplyContent("replyContent");
        unOrder.setArrayOfUnRemind(Arrays.asList(unRemind));
        final FoodListDTO foodListDTO = new FoodListDTO();
        unOrder.setFoodLists(Arrays.asList(foodListDTO));

        // Run the test
        eleOrderReplyServiceImplUnderTest.startDelivery(unOrder);

        // Verify the results
    }

    @Test
    public void testStartDeliveryMQ() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setShopName("ShopName");
        unOrder.setCbMsgType(0);
        unOrder.setReplyMsgType(0);
        unOrder.setOrderSubType(0);
        unOrder.setStoreGuid("ShopNo");
        unOrder.setOrderId("orderId");
        unOrder.setOrderDaySn("orderDaySn");
        unOrder.setOrderRemark("orderRemark");
        unOrder.setReserve(false);
        unOrder.setCustomerName("customerName");
        unOrder.setCustomerPhone("customerPhone");
        unOrder.setPrivacyPhone("privacyPhone");
        unOrder.setCustomerAddress("customerAddress");
        unOrder.setShipLatitude("ReceiverLat");
        unOrder.setShipLongitude("ReceiverLng");
        unOrder.setShipperId("id");
        unOrder.setShipperName("name");
        unOrder.setShipperPhone("phone");
        unOrder.setInvoiced(false);
        unOrder.setInvoiceType(0);
        unOrder.setInvoiceTitle("invoiceTitle");
        unOrder.setTaxpayerId("taxpayerId");
        unOrder.setTotal(new BigDecimal("0.00"));
        unOrder.setShipTotal(new BigDecimal("0.00"));
        unOrder.setPackageTotal(new BigDecimal("0.00"));
        unOrder.setDiscountTotal(new BigDecimal("0.00"));
        unOrder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unOrder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setCancelReplyMessage("cancelReplyMessage");
        unOrder.setRefundReplyMessage("refundReplyMessage");
        unOrder.setCancelReason("cancelReason");
        final UnRemind unRemind = new UnRemind();
        unRemind.setRemindId("remindId");
        unRemind.setReplyContent("replyContent");
        unOrder.setArrayOfUnRemind(Arrays.asList(unRemind));
        final FoodListDTO foodListDTO = new FoodListDTO();
        unOrder.setFoodLists(Arrays.asList(foodListDTO));

        final OwnApiResult expectedResult = new OwnApiResult();
        expectedResult.setCode(0);
        expectedResult.setStatus("status");
        expectedResult.setMsg("message");
        expectedResult.setResult("result");
        expectedResult.setErrorCode(0);

        // Run the test
        final OwnApiResult result = eleOrderReplyServiceImplUnderTest.startDeliveryMQ(unOrder);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testCancelDelivery() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setShopName("ShopName");
        unOrder.setCbMsgType(0);
        unOrder.setReplyMsgType(0);
        unOrder.setOrderSubType(0);
        unOrder.setStoreGuid("ShopNo");
        unOrder.setOrderId("orderId");
        unOrder.setOrderDaySn("orderDaySn");
        unOrder.setOrderRemark("orderRemark");
        unOrder.setReserve(false);
        unOrder.setCustomerName("customerName");
        unOrder.setCustomerPhone("customerPhone");
        unOrder.setPrivacyPhone("privacyPhone");
        unOrder.setCustomerAddress("customerAddress");
        unOrder.setShipLatitude("ReceiverLat");
        unOrder.setShipLongitude("ReceiverLng");
        unOrder.setShipperId("id");
        unOrder.setShipperName("name");
        unOrder.setShipperPhone("phone");
        unOrder.setInvoiced(false);
        unOrder.setInvoiceType(0);
        unOrder.setInvoiceTitle("invoiceTitle");
        unOrder.setTaxpayerId("taxpayerId");
        unOrder.setTotal(new BigDecimal("0.00"));
        unOrder.setShipTotal(new BigDecimal("0.00"));
        unOrder.setPackageTotal(new BigDecimal("0.00"));
        unOrder.setDiscountTotal(new BigDecimal("0.00"));
        unOrder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unOrder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setCancelReplyMessage("cancelReplyMessage");
        unOrder.setRefundReplyMessage("refundReplyMessage");
        unOrder.setCancelReason("cancelReason");
        final UnRemind unRemind = new UnRemind();
        unRemind.setRemindId("remindId");
        unRemind.setReplyContent("replyContent");
        unOrder.setArrayOfUnRemind(Arrays.asList(unRemind));
        final FoodListDTO foodListDTO = new FoodListDTO();
        unOrder.setFoodLists(Arrays.asList(foodListDTO));

        // Run the test
        eleOrderReplyServiceImplUnderTest.cancelDelivery(unOrder);

        // Verify the results
    }
}
