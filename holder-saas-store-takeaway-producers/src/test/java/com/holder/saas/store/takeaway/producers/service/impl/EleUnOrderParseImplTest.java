package com.holder.saas.store.takeaway.producers.service.impl;

import com.holder.saas.store.takeaway.producers.service.EleAuthService;
import com.holderzone.saas.store.dto.takeaway.UnDiscount;
import com.holderzone.saas.store.dto.takeaway.UnItem;
import com.holderzone.saas.store.dto.takeaway.UnOrder;
import com.holderzone.saas.store.dto.takeaway.UnRemind;
import eleme.openapi.sdk.api.entity.order.*;
import eleme.openapi.sdk.api.entity.other.OMessage;
import eleme.openapi.sdk.api.enumeration.order.InvoiceType;
import eleme.openapi.sdk.api.enumeration.order.OOrderDetailGroupType;
import eleme.openapi.sdk.api.enumeration.order.OOrderStatus;
import eleme.openapi.sdk.config.Config;
import eleme.openapi.sdk.oauth.response.Token;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Calendar;
import java.util.GregorianCalendar;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class EleUnOrderParseImplTest {

    @Mock
    private EleAuthService mockEleAuthService;
    @Mock
    private Config mockConfig;
    @Mock
    private StringRedisTemplate mockRedisTemplate;

    private EleUnOrderParseImpl eleUnOrderParseImplUnderTest;

    @Before
    public void setUp() throws Exception {
        eleUnOrderParseImplUnderTest = new EleUnOrderParseImpl(mockEleAuthService, mockConfig, mockRedisTemplate);
    }

    @Test
    public void testFromOOrder() {
        // Setup
        final OOrder oOrder = new OOrder();
        oOrder.setCreatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        oOrder.setActiveAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        oOrder.setDeliverFee(0.0);
        oOrder.setDeliverTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        oOrder.setDescription("description");
        final OGoodsGroup oGoodsGroup = new OGoodsGroup();
        oGoodsGroup.setType(OOrderDetailGroupType.normal);
        final OGoodsItem oGoodsItem = new OGoodsItem();
        oGoodsItem.setId(0L);
        oGoodsItem.setName("itemName");
        oGoodsItem.setCategoryId(0L);
        oGoodsItem.setPrice(0.0);
        oGoodsItem.setQuantity(0);
        oGoodsItem.setTotal(0.0);
        final OGroupItemSpec oGroupItemSpec = new OGroupItemSpec();
        oGroupItemSpec.setValue("value");
        oGoodsItem.setNewSpecs(Arrays.asList(oGroupItemSpec));
        final OGroupItemAttribute oGroupItemAttribute = new OGroupItemAttribute();
        oGroupItemAttribute.setValue("value");
        oGoodsItem.setAttributes(Arrays.asList(oGroupItemAttribute));
        oGoodsItem.setExtendCode("extendCode");
        oGoodsGroup.setItems(Arrays.asList(oGoodsItem));
        oOrder.setGroups(Arrays.asList(oGoodsGroup));
        oOrder.setInvoice("invoice");
        oOrder.setBook(false);
        oOrder.setOnlinePaid(false);
        oOrder.setId("id");
        oOrder.setPhoneList(Arrays.asList("value"));
        oOrder.setShopId(0L);
        oOrder.setShopName("shopName");
        oOrder.setDaySn(1);
        oOrder.setStatus(OOrderStatus.pending);
        oOrder.setTotalPrice(0.0);
        oOrder.setOriginalPrice(0.0);
        oOrder.setConsignee("consignee");
        oOrder.setDeliveryGeo("deliveryGeo");
        oOrder.setDeliveryPoiAddress("customerAddress");
        oOrder.setInvoiced(false);
        oOrder.setIncome(0.0);
        oOrder.setServiceRate(0.0);
        oOrder.setServiceFee(0.0);
        oOrder.setPackageFee(0.0);
        oOrder.setShopPart(0.0);
        oOrder.setElemePart(0.0);
        final OActivity oActivity = new OActivity();
        oActivity.setName("discountName");
        oActivity.setAmount(0.0);
        oOrder.setOrderActivities(Arrays.asList(oActivity));
        oOrder.setInvoiceType(InvoiceType.personal);
        oOrder.setTaxpayerId("taxpayerId");
        oOrder.setConsigneePhones(Arrays.asList("value"));

        final UnOrder expectedResult = new UnOrder();
        expectedResult.setShopId(0L);
        expectedResult.setShopName("shopName");
        expectedResult.setCbMsgType(0);
        expectedResult.setOrderId("id");
        expectedResult.setOrderViewId("id");
        expectedResult.setOrderDaySn("orderDaySn");
        expectedResult.setOrderRemark("description");
        expectedResult.setReserve(false);
        expectedResult.setCustomerName("consignee");
        expectedResult.setCustomerPhone("customerPhone");
        expectedResult.setPrivacyPhone("privacyPhone");
        expectedResult.setCustomerAddress("customerAddress");
        expectedResult.setCustomerNumber(0);
        expectedResult.setFirstOrder(false);
        expectedResult.setShipLatitude("shipLatitude");
        expectedResult.setShipLongitude("shipLongitude");
        expectedResult.setShipperName("name");
        expectedResult.setShipperPhone("phone");
        expectedResult.setThirdShipper(false);
        expectedResult.setInvoiced(false);
        expectedResult.setInvoiceType(0);
        expectedResult.setInvoiceTitle("invoice");
        expectedResult.setTaxpayerId("taxpayerId");
        expectedResult.setTotal(new BigDecimal("0.00"));
        expectedResult.setShipTotal(new BigDecimal("0.00"));
        expectedResult.setItemTotal(new BigDecimal("0.00"));
        expectedResult.setPackageTotal(new BigDecimal("0.00"));
        expectedResult.setDiscountTotal(new BigDecimal("0.00"));
        expectedResult.setEnterpriseDiscount(new BigDecimal("0.00"));
        expectedResult.setPlatformDiscount(new BigDecimal("0.00"));
        expectedResult.setServiceFeeRate(new BigDecimal("0.00"));
        expectedResult.setServiceFee(new BigDecimal("0.00"));
        expectedResult.setCustomerActualPay(new BigDecimal("0.00"));
        expectedResult.setPart(false);
        expectedResult.setCustomerRefund(new BigDecimal("0.00"));
        expectedResult.setCustomerRefundItem("customerRefundItem");
        expectedResult.setShopTotal(new BigDecimal("0.00"));
        expectedResult.setOnlinePay(false);
        expectedResult.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setAcceptTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setDeliveryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReqReason("cancelReqReason");
        expectedResult.setRefundReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRefundReqReason("cancelReqReason");
        expectedResult.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReason("下单超时未确认，系统自动取消");
        expectedResult.setCancelRoleName("desc");
        expectedResult.setCompleteTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final UnItem unItem = new UnItem();
        unItem.setItemSku("itemSku");
        unItem.setItemName("itemName");
        unItem.setItemPrice(new BigDecimal("0.00"));
        unItem.setItemCount(new BigDecimal("0.00"));
        unItem.setItemTotal(new BigDecimal("0.00"));
        unItem.setItemSpec("itemName");
        unItem.setItemProperty("itemProperty");
        unItem.setCartId(0);
        unItem.setActualPrice(new BigDecimal("0.00"));
        unItem.setSettleType(0);
        unItem.setUnItemSkuId("unItemSkuId");
        unItem.setThirdSkuId("thirdSkuId");
        expectedResult.setArrayOfUnItem(Arrays.asList(unItem));
        final UnRemind unRemind = new UnRemind();
        unRemind.setOrderId("id");
        unRemind.setRemindId("remindId");
        unRemind.setRemindTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setArrayOfUnRemind(Arrays.asList(unRemind));
        final UnDiscount unDiscount = new UnDiscount();
        expectedResult.setArrayOfUnDiscount(Arrays.asList(unDiscount));

        // Configure EleAuthService.getTokenByShopId(...).
        final Token token = new Token();
        token.setAccessToken("accessToken");
        token.setTokenType("tokenType");
        token.setExpires(0L);
        token.setRefreshToken("refreshToken");
        when(mockEleAuthService.getTokenByShopId(0L)).thenReturn(token);

        // Run the test
        final UnOrder result = eleUnOrderParseImplUnderTest.fromOOrder(oOrder);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testFromOrderCreated() {
        // Setup
        final OMessage oMessage = new OMessage();
        oMessage.setRequestId("requestId");
        oMessage.setType(0);
        oMessage.setAppId(0);
        oMessage.setMessage("message");
        oMessage.setShopId(0L);

        final UnOrder expectedResult = new UnOrder();
        expectedResult.setShopId(0L);
        expectedResult.setShopName("shopName");
        expectedResult.setCbMsgType(0);
        expectedResult.setOrderId("id");
        expectedResult.setOrderViewId("id");
        expectedResult.setOrderDaySn("orderDaySn");
        expectedResult.setOrderRemark("description");
        expectedResult.setReserve(false);
        expectedResult.setCustomerName("consignee");
        expectedResult.setCustomerPhone("customerPhone");
        expectedResult.setPrivacyPhone("privacyPhone");
        expectedResult.setCustomerAddress("customerAddress");
        expectedResult.setCustomerNumber(0);
        expectedResult.setFirstOrder(false);
        expectedResult.setShipLatitude("shipLatitude");
        expectedResult.setShipLongitude("shipLongitude");
        expectedResult.setShipperName("name");
        expectedResult.setShipperPhone("phone");
        expectedResult.setThirdShipper(false);
        expectedResult.setInvoiced(false);
        expectedResult.setInvoiceType(0);
        expectedResult.setInvoiceTitle("invoice");
        expectedResult.setTaxpayerId("taxpayerId");
        expectedResult.setTotal(new BigDecimal("0.00"));
        expectedResult.setShipTotal(new BigDecimal("0.00"));
        expectedResult.setItemTotal(new BigDecimal("0.00"));
        expectedResult.setPackageTotal(new BigDecimal("0.00"));
        expectedResult.setDiscountTotal(new BigDecimal("0.00"));
        expectedResult.setEnterpriseDiscount(new BigDecimal("0.00"));
        expectedResult.setPlatformDiscount(new BigDecimal("0.00"));
        expectedResult.setServiceFeeRate(new BigDecimal("0.00"));
        expectedResult.setServiceFee(new BigDecimal("0.00"));
        expectedResult.setCustomerActualPay(new BigDecimal("0.00"));
        expectedResult.setPart(false);
        expectedResult.setCustomerRefund(new BigDecimal("0.00"));
        expectedResult.setCustomerRefundItem("customerRefundItem");
        expectedResult.setShopTotal(new BigDecimal("0.00"));
        expectedResult.setOnlinePay(false);
        expectedResult.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setAcceptTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setDeliveryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReqReason("cancelReqReason");
        expectedResult.setRefundReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRefundReqReason("cancelReqReason");
        expectedResult.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReason("下单超时未确认，系统自动取消");
        expectedResult.setCancelRoleName("desc");
        expectedResult.setCompleteTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final UnItem unItem = new UnItem();
        unItem.setItemSku("itemSku");
        unItem.setItemName("itemName");
        unItem.setItemPrice(new BigDecimal("0.00"));
        unItem.setItemCount(new BigDecimal("0.00"));
        unItem.setItemTotal(new BigDecimal("0.00"));
        unItem.setItemSpec("itemName");
        unItem.setItemProperty("itemProperty");
        unItem.setCartId(0);
        unItem.setActualPrice(new BigDecimal("0.00"));
        unItem.setSettleType(0);
        unItem.setUnItemSkuId("unItemSkuId");
        unItem.setThirdSkuId("thirdSkuId");
        expectedResult.setArrayOfUnItem(Arrays.asList(unItem));
        final UnRemind unRemind = new UnRemind();
        unRemind.setOrderId("id");
        unRemind.setRemindId("remindId");
        unRemind.setRemindTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setArrayOfUnRemind(Arrays.asList(unRemind));
        final UnDiscount unDiscount = new UnDiscount();
        expectedResult.setArrayOfUnDiscount(Arrays.asList(unDiscount));

        when(mockRedisTemplate.opsForZSet()).thenReturn(null);

        // Configure EleAuthService.getTokenByShopId(...).
        final Token token = new Token();
        token.setAccessToken("accessToken");
        token.setTokenType("tokenType");
        token.setExpires(0L);
        token.setRefreshToken("refreshToken");
        when(mockEleAuthService.getTokenByShopId(0L)).thenReturn(token);

        // Run the test
        final UnOrder result = eleUnOrderParseImplUnderTest.fromOrderCreated(oMessage);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testFromOrderConfirmed() {
        // Setup
        final OMessage oMessage = new OMessage();
        oMessage.setRequestId("requestId");
        oMessage.setType(0);
        oMessage.setAppId(0);
        oMessage.setMessage("message");
        oMessage.setShopId(0L);

        final UnOrder expectedResult = new UnOrder();
        expectedResult.setShopId(0L);
        expectedResult.setShopName("shopName");
        expectedResult.setCbMsgType(0);
        expectedResult.setOrderId("id");
        expectedResult.setOrderViewId("id");
        expectedResult.setOrderDaySn("orderDaySn");
        expectedResult.setOrderRemark("description");
        expectedResult.setReserve(false);
        expectedResult.setCustomerName("consignee");
        expectedResult.setCustomerPhone("customerPhone");
        expectedResult.setPrivacyPhone("privacyPhone");
        expectedResult.setCustomerAddress("customerAddress");
        expectedResult.setCustomerNumber(0);
        expectedResult.setFirstOrder(false);
        expectedResult.setShipLatitude("shipLatitude");
        expectedResult.setShipLongitude("shipLongitude");
        expectedResult.setShipperName("name");
        expectedResult.setShipperPhone("phone");
        expectedResult.setThirdShipper(false);
        expectedResult.setInvoiced(false);
        expectedResult.setInvoiceType(0);
        expectedResult.setInvoiceTitle("invoice");
        expectedResult.setTaxpayerId("taxpayerId");
        expectedResult.setTotal(new BigDecimal("0.00"));
        expectedResult.setShipTotal(new BigDecimal("0.00"));
        expectedResult.setItemTotal(new BigDecimal("0.00"));
        expectedResult.setPackageTotal(new BigDecimal("0.00"));
        expectedResult.setDiscountTotal(new BigDecimal("0.00"));
        expectedResult.setEnterpriseDiscount(new BigDecimal("0.00"));
        expectedResult.setPlatformDiscount(new BigDecimal("0.00"));
        expectedResult.setServiceFeeRate(new BigDecimal("0.00"));
        expectedResult.setServiceFee(new BigDecimal("0.00"));
        expectedResult.setCustomerActualPay(new BigDecimal("0.00"));
        expectedResult.setPart(false);
        expectedResult.setCustomerRefund(new BigDecimal("0.00"));
        expectedResult.setCustomerRefundItem("customerRefundItem");
        expectedResult.setShopTotal(new BigDecimal("0.00"));
        expectedResult.setOnlinePay(false);
        expectedResult.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setAcceptTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setDeliveryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReqReason("cancelReqReason");
        expectedResult.setRefundReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRefundReqReason("cancelReqReason");
        expectedResult.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReason("下单超时未确认，系统自动取消");
        expectedResult.setCancelRoleName("desc");
        expectedResult.setCompleteTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final UnItem unItem = new UnItem();
        unItem.setItemSku("itemSku");
        unItem.setItemName("itemName");
        unItem.setItemPrice(new BigDecimal("0.00"));
        unItem.setItemCount(new BigDecimal("0.00"));
        unItem.setItemTotal(new BigDecimal("0.00"));
        unItem.setItemSpec("itemName");
        unItem.setItemProperty("itemProperty");
        unItem.setCartId(0);
        unItem.setActualPrice(new BigDecimal("0.00"));
        unItem.setSettleType(0);
        unItem.setUnItemSkuId("unItemSkuId");
        unItem.setThirdSkuId("thirdSkuId");
        expectedResult.setArrayOfUnItem(Arrays.asList(unItem));
        final UnRemind unRemind = new UnRemind();
        unRemind.setOrderId("id");
        unRemind.setRemindId("remindId");
        unRemind.setRemindTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setArrayOfUnRemind(Arrays.asList(unRemind));
        final UnDiscount unDiscount = new UnDiscount();
        expectedResult.setArrayOfUnDiscount(Arrays.asList(unDiscount));

        // Run the test
        final UnOrder result = eleUnOrderParseImplUnderTest.fromOrderConfirmed(oMessage);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testFromOrderShippingDistribute() {
        // Setup
        final OMessage oMessage = new OMessage();
        oMessage.setRequestId("requestId");
        oMessage.setType(0);
        oMessage.setAppId(0);
        oMessage.setMessage("message");
        oMessage.setShopId(0L);

        final UnOrder expectedResult = new UnOrder();
        expectedResult.setShopId(0L);
        expectedResult.setShopName("shopName");
        expectedResult.setCbMsgType(0);
        expectedResult.setOrderId("id");
        expectedResult.setOrderViewId("id");
        expectedResult.setOrderDaySn("orderDaySn");
        expectedResult.setOrderRemark("description");
        expectedResult.setReserve(false);
        expectedResult.setCustomerName("consignee");
        expectedResult.setCustomerPhone("customerPhone");
        expectedResult.setPrivacyPhone("privacyPhone");
        expectedResult.setCustomerAddress("customerAddress");
        expectedResult.setCustomerNumber(0);
        expectedResult.setFirstOrder(false);
        expectedResult.setShipLatitude("shipLatitude");
        expectedResult.setShipLongitude("shipLongitude");
        expectedResult.setShipperName("name");
        expectedResult.setShipperPhone("phone");
        expectedResult.setThirdShipper(false);
        expectedResult.setInvoiced(false);
        expectedResult.setInvoiceType(0);
        expectedResult.setInvoiceTitle("invoice");
        expectedResult.setTaxpayerId("taxpayerId");
        expectedResult.setTotal(new BigDecimal("0.00"));
        expectedResult.setShipTotal(new BigDecimal("0.00"));
        expectedResult.setItemTotal(new BigDecimal("0.00"));
        expectedResult.setPackageTotal(new BigDecimal("0.00"));
        expectedResult.setDiscountTotal(new BigDecimal("0.00"));
        expectedResult.setEnterpriseDiscount(new BigDecimal("0.00"));
        expectedResult.setPlatformDiscount(new BigDecimal("0.00"));
        expectedResult.setServiceFeeRate(new BigDecimal("0.00"));
        expectedResult.setServiceFee(new BigDecimal("0.00"));
        expectedResult.setCustomerActualPay(new BigDecimal("0.00"));
        expectedResult.setPart(false);
        expectedResult.setCustomerRefund(new BigDecimal("0.00"));
        expectedResult.setCustomerRefundItem("customerRefundItem");
        expectedResult.setShopTotal(new BigDecimal("0.00"));
        expectedResult.setOnlinePay(false);
        expectedResult.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setAcceptTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setDeliveryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReqReason("cancelReqReason");
        expectedResult.setRefundReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRefundReqReason("cancelReqReason");
        expectedResult.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReason("下单超时未确认，系统自动取消");
        expectedResult.setCancelRoleName("desc");
        expectedResult.setCompleteTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final UnItem unItem = new UnItem();
        unItem.setItemSku("itemSku");
        unItem.setItemName("itemName");
        unItem.setItemPrice(new BigDecimal("0.00"));
        unItem.setItemCount(new BigDecimal("0.00"));
        unItem.setItemTotal(new BigDecimal("0.00"));
        unItem.setItemSpec("itemName");
        unItem.setItemProperty("itemProperty");
        unItem.setCartId(0);
        unItem.setActualPrice(new BigDecimal("0.00"));
        unItem.setSettleType(0);
        unItem.setUnItemSkuId("unItemSkuId");
        unItem.setThirdSkuId("thirdSkuId");
        expectedResult.setArrayOfUnItem(Arrays.asList(unItem));
        final UnRemind unRemind = new UnRemind();
        unRemind.setOrderId("id");
        unRemind.setRemindId("remindId");
        unRemind.setRemindTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setArrayOfUnRemind(Arrays.asList(unRemind));
        final UnDiscount unDiscount = new UnDiscount();
        expectedResult.setArrayOfUnDiscount(Arrays.asList(unDiscount));

        // Run the test
        final UnOrder result = eleUnOrderParseImplUnderTest.fromOrderShippingDistribute(oMessage);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testFromOrderShipping() {
        // Setup
        final OMessage oMessage = new OMessage();
        oMessage.setRequestId("requestId");
        oMessage.setType(0);
        oMessage.setAppId(0);
        oMessage.setMessage("message");
        oMessage.setShopId(0L);

        final UnOrder expectedResult = new UnOrder();
        expectedResult.setShopId(0L);
        expectedResult.setShopName("shopName");
        expectedResult.setCbMsgType(0);
        expectedResult.setOrderId("id");
        expectedResult.setOrderViewId("id");
        expectedResult.setOrderDaySn("orderDaySn");
        expectedResult.setOrderRemark("description");
        expectedResult.setReserve(false);
        expectedResult.setCustomerName("consignee");
        expectedResult.setCustomerPhone("customerPhone");
        expectedResult.setPrivacyPhone("privacyPhone");
        expectedResult.setCustomerAddress("customerAddress");
        expectedResult.setCustomerNumber(0);
        expectedResult.setFirstOrder(false);
        expectedResult.setShipLatitude("shipLatitude");
        expectedResult.setShipLongitude("shipLongitude");
        expectedResult.setShipperName("name");
        expectedResult.setShipperPhone("phone");
        expectedResult.setThirdShipper(false);
        expectedResult.setInvoiced(false);
        expectedResult.setInvoiceType(0);
        expectedResult.setInvoiceTitle("invoice");
        expectedResult.setTaxpayerId("taxpayerId");
        expectedResult.setTotal(new BigDecimal("0.00"));
        expectedResult.setShipTotal(new BigDecimal("0.00"));
        expectedResult.setItemTotal(new BigDecimal("0.00"));
        expectedResult.setPackageTotal(new BigDecimal("0.00"));
        expectedResult.setDiscountTotal(new BigDecimal("0.00"));
        expectedResult.setEnterpriseDiscount(new BigDecimal("0.00"));
        expectedResult.setPlatformDiscount(new BigDecimal("0.00"));
        expectedResult.setServiceFeeRate(new BigDecimal("0.00"));
        expectedResult.setServiceFee(new BigDecimal("0.00"));
        expectedResult.setCustomerActualPay(new BigDecimal("0.00"));
        expectedResult.setPart(false);
        expectedResult.setCustomerRefund(new BigDecimal("0.00"));
        expectedResult.setCustomerRefundItem("customerRefundItem");
        expectedResult.setShopTotal(new BigDecimal("0.00"));
        expectedResult.setOnlinePay(false);
        expectedResult.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setAcceptTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setDeliveryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReqReason("cancelReqReason");
        expectedResult.setRefundReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRefundReqReason("cancelReqReason");
        expectedResult.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReason("下单超时未确认，系统自动取消");
        expectedResult.setCancelRoleName("desc");
        expectedResult.setCompleteTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final UnItem unItem = new UnItem();
        unItem.setItemSku("itemSku");
        unItem.setItemName("itemName");
        unItem.setItemPrice(new BigDecimal("0.00"));
        unItem.setItemCount(new BigDecimal("0.00"));
        unItem.setItemTotal(new BigDecimal("0.00"));
        unItem.setItemSpec("itemName");
        unItem.setItemProperty("itemProperty");
        unItem.setCartId(0);
        unItem.setActualPrice(new BigDecimal("0.00"));
        unItem.setSettleType(0);
        unItem.setUnItemSkuId("unItemSkuId");
        unItem.setThirdSkuId("thirdSkuId");
        expectedResult.setArrayOfUnItem(Arrays.asList(unItem));
        final UnRemind unRemind = new UnRemind();
        unRemind.setOrderId("id");
        unRemind.setRemindId("remindId");
        unRemind.setRemindTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setArrayOfUnRemind(Arrays.asList(unRemind));
        final UnDiscount unDiscount = new UnDiscount();
        expectedResult.setArrayOfUnDiscount(Arrays.asList(unDiscount));

        // Run the test
        final UnOrder result = eleUnOrderParseImplUnderTest.fromOrderShipping(oMessage);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testFromOrderShipSuccessful() {
        // Setup
        final OMessage oMessage = new OMessage();
        oMessage.setRequestId("requestId");
        oMessage.setType(0);
        oMessage.setAppId(0);
        oMessage.setMessage("message");
        oMessage.setShopId(0L);

        final UnOrder expectedResult = new UnOrder();
        expectedResult.setShopId(0L);
        expectedResult.setShopName("shopName");
        expectedResult.setCbMsgType(0);
        expectedResult.setOrderId("id");
        expectedResult.setOrderViewId("id");
        expectedResult.setOrderDaySn("orderDaySn");
        expectedResult.setOrderRemark("description");
        expectedResult.setReserve(false);
        expectedResult.setCustomerName("consignee");
        expectedResult.setCustomerPhone("customerPhone");
        expectedResult.setPrivacyPhone("privacyPhone");
        expectedResult.setCustomerAddress("customerAddress");
        expectedResult.setCustomerNumber(0);
        expectedResult.setFirstOrder(false);
        expectedResult.setShipLatitude("shipLatitude");
        expectedResult.setShipLongitude("shipLongitude");
        expectedResult.setShipperName("name");
        expectedResult.setShipperPhone("phone");
        expectedResult.setThirdShipper(false);
        expectedResult.setInvoiced(false);
        expectedResult.setInvoiceType(0);
        expectedResult.setInvoiceTitle("invoice");
        expectedResult.setTaxpayerId("taxpayerId");
        expectedResult.setTotal(new BigDecimal("0.00"));
        expectedResult.setShipTotal(new BigDecimal("0.00"));
        expectedResult.setItemTotal(new BigDecimal("0.00"));
        expectedResult.setPackageTotal(new BigDecimal("0.00"));
        expectedResult.setDiscountTotal(new BigDecimal("0.00"));
        expectedResult.setEnterpriseDiscount(new BigDecimal("0.00"));
        expectedResult.setPlatformDiscount(new BigDecimal("0.00"));
        expectedResult.setServiceFeeRate(new BigDecimal("0.00"));
        expectedResult.setServiceFee(new BigDecimal("0.00"));
        expectedResult.setCustomerActualPay(new BigDecimal("0.00"));
        expectedResult.setPart(false);
        expectedResult.setCustomerRefund(new BigDecimal("0.00"));
        expectedResult.setCustomerRefundItem("customerRefundItem");
        expectedResult.setShopTotal(new BigDecimal("0.00"));
        expectedResult.setOnlinePay(false);
        expectedResult.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setAcceptTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setDeliveryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReqReason("cancelReqReason");
        expectedResult.setRefundReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRefundReqReason("cancelReqReason");
        expectedResult.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReason("下单超时未确认，系统自动取消");
        expectedResult.setCancelRoleName("desc");
        expectedResult.setCompleteTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final UnItem unItem = new UnItem();
        unItem.setItemSku("itemSku");
        unItem.setItemName("itemName");
        unItem.setItemPrice(new BigDecimal("0.00"));
        unItem.setItemCount(new BigDecimal("0.00"));
        unItem.setItemTotal(new BigDecimal("0.00"));
        unItem.setItemSpec("itemName");
        unItem.setItemProperty("itemProperty");
        unItem.setCartId(0);
        unItem.setActualPrice(new BigDecimal("0.00"));
        unItem.setSettleType(0);
        unItem.setUnItemSkuId("unItemSkuId");
        unItem.setThirdSkuId("thirdSkuId");
        expectedResult.setArrayOfUnItem(Arrays.asList(unItem));
        final UnRemind unRemind = new UnRemind();
        unRemind.setOrderId("id");
        unRemind.setRemindId("remindId");
        unRemind.setRemindTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setArrayOfUnRemind(Arrays.asList(unRemind));
        final UnDiscount unDiscount = new UnDiscount();
        expectedResult.setArrayOfUnDiscount(Arrays.asList(unDiscount));

        // Run the test
        final UnOrder result = eleUnOrderParseImplUnderTest.fromOrderShipSuccessful(oMessage);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testFromOrderFinished() {
        // Setup
        final OMessage oMessage = new OMessage();
        oMessage.setRequestId("requestId");
        oMessage.setType(0);
        oMessage.setAppId(0);
        oMessage.setMessage("message");
        oMessage.setShopId(0L);

        final UnOrder expectedResult = new UnOrder();
        expectedResult.setShopId(0L);
        expectedResult.setShopName("shopName");
        expectedResult.setCbMsgType(0);
        expectedResult.setOrderId("id");
        expectedResult.setOrderViewId("id");
        expectedResult.setOrderDaySn("orderDaySn");
        expectedResult.setOrderRemark("description");
        expectedResult.setReserve(false);
        expectedResult.setCustomerName("consignee");
        expectedResult.setCustomerPhone("customerPhone");
        expectedResult.setPrivacyPhone("privacyPhone");
        expectedResult.setCustomerAddress("customerAddress");
        expectedResult.setCustomerNumber(0);
        expectedResult.setFirstOrder(false);
        expectedResult.setShipLatitude("shipLatitude");
        expectedResult.setShipLongitude("shipLongitude");
        expectedResult.setShipperName("name");
        expectedResult.setShipperPhone("phone");
        expectedResult.setThirdShipper(false);
        expectedResult.setInvoiced(false);
        expectedResult.setInvoiceType(0);
        expectedResult.setInvoiceTitle("invoice");
        expectedResult.setTaxpayerId("taxpayerId");
        expectedResult.setTotal(new BigDecimal("0.00"));
        expectedResult.setShipTotal(new BigDecimal("0.00"));
        expectedResult.setItemTotal(new BigDecimal("0.00"));
        expectedResult.setPackageTotal(new BigDecimal("0.00"));
        expectedResult.setDiscountTotal(new BigDecimal("0.00"));
        expectedResult.setEnterpriseDiscount(new BigDecimal("0.00"));
        expectedResult.setPlatformDiscount(new BigDecimal("0.00"));
        expectedResult.setServiceFeeRate(new BigDecimal("0.00"));
        expectedResult.setServiceFee(new BigDecimal("0.00"));
        expectedResult.setCustomerActualPay(new BigDecimal("0.00"));
        expectedResult.setPart(false);
        expectedResult.setCustomerRefund(new BigDecimal("0.00"));
        expectedResult.setCustomerRefundItem("customerRefundItem");
        expectedResult.setShopTotal(new BigDecimal("0.00"));
        expectedResult.setOnlinePay(false);
        expectedResult.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setAcceptTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setDeliveryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReqReason("cancelReqReason");
        expectedResult.setRefundReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRefundReqReason("cancelReqReason");
        expectedResult.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReason("下单超时未确认，系统自动取消");
        expectedResult.setCancelRoleName("desc");
        expectedResult.setCompleteTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final UnItem unItem = new UnItem();
        unItem.setItemSku("itemSku");
        unItem.setItemName("itemName");
        unItem.setItemPrice(new BigDecimal("0.00"));
        unItem.setItemCount(new BigDecimal("0.00"));
        unItem.setItemTotal(new BigDecimal("0.00"));
        unItem.setItemSpec("itemName");
        unItem.setItemProperty("itemProperty");
        unItem.setCartId(0);
        unItem.setActualPrice(new BigDecimal("0.00"));
        unItem.setSettleType(0);
        unItem.setUnItemSkuId("unItemSkuId");
        unItem.setThirdSkuId("thirdSkuId");
        expectedResult.setArrayOfUnItem(Arrays.asList(unItem));
        final UnRemind unRemind = new UnRemind();
        unRemind.setOrderId("id");
        unRemind.setRemindId("remindId");
        unRemind.setRemindTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setArrayOfUnRemind(Arrays.asList(unRemind));
        final UnDiscount unDiscount = new UnDiscount();
        expectedResult.setArrayOfUnDiscount(Arrays.asList(unDiscount));

        // Run the test
        final UnOrder result = eleUnOrderParseImplUnderTest.fromOrderFinished(oMessage);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testFromOrderCanceled() {
        // Setup
        final OMessage oMessage = new OMessage();
        oMessage.setRequestId("requestId");
        oMessage.setType(0);
        oMessage.setAppId(0);
        oMessage.setMessage("message");
        oMessage.setShopId(0L);

        final UnOrder expectedResult = new UnOrder();
        expectedResult.setShopId(0L);
        expectedResult.setShopName("shopName");
        expectedResult.setCbMsgType(0);
        expectedResult.setOrderId("id");
        expectedResult.setOrderViewId("id");
        expectedResult.setOrderDaySn("orderDaySn");
        expectedResult.setOrderRemark("description");
        expectedResult.setReserve(false);
        expectedResult.setCustomerName("consignee");
        expectedResult.setCustomerPhone("customerPhone");
        expectedResult.setPrivacyPhone("privacyPhone");
        expectedResult.setCustomerAddress("customerAddress");
        expectedResult.setCustomerNumber(0);
        expectedResult.setFirstOrder(false);
        expectedResult.setShipLatitude("shipLatitude");
        expectedResult.setShipLongitude("shipLongitude");
        expectedResult.setShipperName("name");
        expectedResult.setShipperPhone("phone");
        expectedResult.setThirdShipper(false);
        expectedResult.setInvoiced(false);
        expectedResult.setInvoiceType(0);
        expectedResult.setInvoiceTitle("invoice");
        expectedResult.setTaxpayerId("taxpayerId");
        expectedResult.setTotal(new BigDecimal("0.00"));
        expectedResult.setShipTotal(new BigDecimal("0.00"));
        expectedResult.setItemTotal(new BigDecimal("0.00"));
        expectedResult.setPackageTotal(new BigDecimal("0.00"));
        expectedResult.setDiscountTotal(new BigDecimal("0.00"));
        expectedResult.setEnterpriseDiscount(new BigDecimal("0.00"));
        expectedResult.setPlatformDiscount(new BigDecimal("0.00"));
        expectedResult.setServiceFeeRate(new BigDecimal("0.00"));
        expectedResult.setServiceFee(new BigDecimal("0.00"));
        expectedResult.setCustomerActualPay(new BigDecimal("0.00"));
        expectedResult.setPart(false);
        expectedResult.setCustomerRefund(new BigDecimal("0.00"));
        expectedResult.setCustomerRefundItem("customerRefundItem");
        expectedResult.setShopTotal(new BigDecimal("0.00"));
        expectedResult.setOnlinePay(false);
        expectedResult.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setAcceptTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setDeliveryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReqReason("cancelReqReason");
        expectedResult.setRefundReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRefundReqReason("cancelReqReason");
        expectedResult.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReason("下单超时未确认，系统自动取消");
        expectedResult.setCancelRoleName("desc");
        expectedResult.setCompleteTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final UnItem unItem = new UnItem();
        unItem.setItemSku("itemSku");
        unItem.setItemName("itemName");
        unItem.setItemPrice(new BigDecimal("0.00"));
        unItem.setItemCount(new BigDecimal("0.00"));
        unItem.setItemTotal(new BigDecimal("0.00"));
        unItem.setItemSpec("itemName");
        unItem.setItemProperty("itemProperty");
        unItem.setCartId(0);
        unItem.setActualPrice(new BigDecimal("0.00"));
        unItem.setSettleType(0);
        unItem.setUnItemSkuId("unItemSkuId");
        unItem.setThirdSkuId("thirdSkuId");
        expectedResult.setArrayOfUnItem(Arrays.asList(unItem));
        final UnRemind unRemind = new UnRemind();
        unRemind.setOrderId("id");
        unRemind.setRemindId("remindId");
        unRemind.setRemindTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setArrayOfUnRemind(Arrays.asList(unRemind));
        final UnDiscount unDiscount = new UnDiscount();
        expectedResult.setArrayOfUnDiscount(Arrays.asList(unDiscount));

        // Run the test
        final UnOrder result = eleUnOrderParseImplUnderTest.fromOrderCanceled(oMessage);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testFromOrderReminded() {
        // Setup
        final OMessage oMessage = new OMessage();
        oMessage.setRequestId("requestId");
        oMessage.setType(0);
        oMessage.setAppId(0);
        oMessage.setMessage("message");
        oMessage.setShopId(0L);

        final UnOrder expectedResult = new UnOrder();
        expectedResult.setShopId(0L);
        expectedResult.setShopName("shopName");
        expectedResult.setCbMsgType(0);
        expectedResult.setOrderId("id");
        expectedResult.setOrderViewId("id");
        expectedResult.setOrderDaySn("orderDaySn");
        expectedResult.setOrderRemark("description");
        expectedResult.setReserve(false);
        expectedResult.setCustomerName("consignee");
        expectedResult.setCustomerPhone("customerPhone");
        expectedResult.setPrivacyPhone("privacyPhone");
        expectedResult.setCustomerAddress("customerAddress");
        expectedResult.setCustomerNumber(0);
        expectedResult.setFirstOrder(false);
        expectedResult.setShipLatitude("shipLatitude");
        expectedResult.setShipLongitude("shipLongitude");
        expectedResult.setShipperName("name");
        expectedResult.setShipperPhone("phone");
        expectedResult.setThirdShipper(false);
        expectedResult.setInvoiced(false);
        expectedResult.setInvoiceType(0);
        expectedResult.setInvoiceTitle("invoice");
        expectedResult.setTaxpayerId("taxpayerId");
        expectedResult.setTotal(new BigDecimal("0.00"));
        expectedResult.setShipTotal(new BigDecimal("0.00"));
        expectedResult.setItemTotal(new BigDecimal("0.00"));
        expectedResult.setPackageTotal(new BigDecimal("0.00"));
        expectedResult.setDiscountTotal(new BigDecimal("0.00"));
        expectedResult.setEnterpriseDiscount(new BigDecimal("0.00"));
        expectedResult.setPlatformDiscount(new BigDecimal("0.00"));
        expectedResult.setServiceFeeRate(new BigDecimal("0.00"));
        expectedResult.setServiceFee(new BigDecimal("0.00"));
        expectedResult.setCustomerActualPay(new BigDecimal("0.00"));
        expectedResult.setPart(false);
        expectedResult.setCustomerRefund(new BigDecimal("0.00"));
        expectedResult.setCustomerRefundItem("customerRefundItem");
        expectedResult.setShopTotal(new BigDecimal("0.00"));
        expectedResult.setOnlinePay(false);
        expectedResult.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setAcceptTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setDeliveryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReqReason("cancelReqReason");
        expectedResult.setRefundReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRefundReqReason("cancelReqReason");
        expectedResult.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReason("下单超时未确认，系统自动取消");
        expectedResult.setCancelRoleName("desc");
        expectedResult.setCompleteTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final UnItem unItem = new UnItem();
        unItem.setItemSku("itemSku");
        unItem.setItemName("itemName");
        unItem.setItemPrice(new BigDecimal("0.00"));
        unItem.setItemCount(new BigDecimal("0.00"));
        unItem.setItemTotal(new BigDecimal("0.00"));
        unItem.setItemSpec("itemName");
        unItem.setItemProperty("itemProperty");
        unItem.setCartId(0);
        unItem.setActualPrice(new BigDecimal("0.00"));
        unItem.setSettleType(0);
        unItem.setUnItemSkuId("unItemSkuId");
        unItem.setThirdSkuId("thirdSkuId");
        expectedResult.setArrayOfUnItem(Arrays.asList(unItem));
        final UnRemind unRemind = new UnRemind();
        unRemind.setOrderId("id");
        unRemind.setRemindId("remindId");
        unRemind.setRemindTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setArrayOfUnRemind(Arrays.asList(unRemind));
        final UnDiscount unDiscount = new UnDiscount();
        expectedResult.setArrayOfUnDiscount(Arrays.asList(unDiscount));

        // Run the test
        final UnOrder result = eleUnOrderParseImplUnderTest.fromOrderReminded(oMessage);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testFromOrderCancelReq() {
        // Setup
        final OMessage oMessage = new OMessage();
        oMessage.setRequestId("requestId");
        oMessage.setType(0);
        oMessage.setAppId(0);
        oMessage.setMessage("message");
        oMessage.setShopId(0L);

        final UnOrder expectedResult = new UnOrder();
        expectedResult.setShopId(0L);
        expectedResult.setShopName("shopName");
        expectedResult.setCbMsgType(0);
        expectedResult.setOrderId("id");
        expectedResult.setOrderViewId("id");
        expectedResult.setOrderDaySn("orderDaySn");
        expectedResult.setOrderRemark("description");
        expectedResult.setReserve(false);
        expectedResult.setCustomerName("consignee");
        expectedResult.setCustomerPhone("customerPhone");
        expectedResult.setPrivacyPhone("privacyPhone");
        expectedResult.setCustomerAddress("customerAddress");
        expectedResult.setCustomerNumber(0);
        expectedResult.setFirstOrder(false);
        expectedResult.setShipLatitude("shipLatitude");
        expectedResult.setShipLongitude("shipLongitude");
        expectedResult.setShipperName("name");
        expectedResult.setShipperPhone("phone");
        expectedResult.setThirdShipper(false);
        expectedResult.setInvoiced(false);
        expectedResult.setInvoiceType(0);
        expectedResult.setInvoiceTitle("invoice");
        expectedResult.setTaxpayerId("taxpayerId");
        expectedResult.setTotal(new BigDecimal("0.00"));
        expectedResult.setShipTotal(new BigDecimal("0.00"));
        expectedResult.setItemTotal(new BigDecimal("0.00"));
        expectedResult.setPackageTotal(new BigDecimal("0.00"));
        expectedResult.setDiscountTotal(new BigDecimal("0.00"));
        expectedResult.setEnterpriseDiscount(new BigDecimal("0.00"));
        expectedResult.setPlatformDiscount(new BigDecimal("0.00"));
        expectedResult.setServiceFeeRate(new BigDecimal("0.00"));
        expectedResult.setServiceFee(new BigDecimal("0.00"));
        expectedResult.setCustomerActualPay(new BigDecimal("0.00"));
        expectedResult.setPart(false);
        expectedResult.setCustomerRefund(new BigDecimal("0.00"));
        expectedResult.setCustomerRefundItem("customerRefundItem");
        expectedResult.setShopTotal(new BigDecimal("0.00"));
        expectedResult.setOnlinePay(false);
        expectedResult.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setAcceptTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setDeliveryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReqReason("cancelReqReason");
        expectedResult.setRefundReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRefundReqReason("cancelReqReason");
        expectedResult.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReason("下单超时未确认，系统自动取消");
        expectedResult.setCancelRoleName("desc");
        expectedResult.setCompleteTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final UnItem unItem = new UnItem();
        unItem.setItemSku("itemSku");
        unItem.setItemName("itemName");
        unItem.setItemPrice(new BigDecimal("0.00"));
        unItem.setItemCount(new BigDecimal("0.00"));
        unItem.setItemTotal(new BigDecimal("0.00"));
        unItem.setItemSpec("itemName");
        unItem.setItemProperty("itemProperty");
        unItem.setCartId(0);
        unItem.setActualPrice(new BigDecimal("0.00"));
        unItem.setSettleType(0);
        unItem.setUnItemSkuId("unItemSkuId");
        unItem.setThirdSkuId("thirdSkuId");
        expectedResult.setArrayOfUnItem(Arrays.asList(unItem));
        final UnRemind unRemind = new UnRemind();
        unRemind.setOrderId("id");
        unRemind.setRemindId("remindId");
        unRemind.setRemindTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setArrayOfUnRemind(Arrays.asList(unRemind));
        final UnDiscount unDiscount = new UnDiscount();
        expectedResult.setArrayOfUnDiscount(Arrays.asList(unDiscount));

        // Run the test
        final UnOrder result = eleUnOrderParseImplUnderTest.fromOrderCancelReq(oMessage);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testFromOrderCancelCancelReq() {
        // Setup
        final OMessage oMessage = new OMessage();
        oMessage.setRequestId("requestId");
        oMessage.setType(0);
        oMessage.setAppId(0);
        oMessage.setMessage("message");
        oMessage.setShopId(0L);

        final UnOrder expectedResult = new UnOrder();
        expectedResult.setShopId(0L);
        expectedResult.setShopName("shopName");
        expectedResult.setCbMsgType(0);
        expectedResult.setOrderId("id");
        expectedResult.setOrderViewId("id");
        expectedResult.setOrderDaySn("orderDaySn");
        expectedResult.setOrderRemark("description");
        expectedResult.setReserve(false);
        expectedResult.setCustomerName("consignee");
        expectedResult.setCustomerPhone("customerPhone");
        expectedResult.setPrivacyPhone("privacyPhone");
        expectedResult.setCustomerAddress("customerAddress");
        expectedResult.setCustomerNumber(0);
        expectedResult.setFirstOrder(false);
        expectedResult.setShipLatitude("shipLatitude");
        expectedResult.setShipLongitude("shipLongitude");
        expectedResult.setShipperName("name");
        expectedResult.setShipperPhone("phone");
        expectedResult.setThirdShipper(false);
        expectedResult.setInvoiced(false);
        expectedResult.setInvoiceType(0);
        expectedResult.setInvoiceTitle("invoice");
        expectedResult.setTaxpayerId("taxpayerId");
        expectedResult.setTotal(new BigDecimal("0.00"));
        expectedResult.setShipTotal(new BigDecimal("0.00"));
        expectedResult.setItemTotal(new BigDecimal("0.00"));
        expectedResult.setPackageTotal(new BigDecimal("0.00"));
        expectedResult.setDiscountTotal(new BigDecimal("0.00"));
        expectedResult.setEnterpriseDiscount(new BigDecimal("0.00"));
        expectedResult.setPlatformDiscount(new BigDecimal("0.00"));
        expectedResult.setServiceFeeRate(new BigDecimal("0.00"));
        expectedResult.setServiceFee(new BigDecimal("0.00"));
        expectedResult.setCustomerActualPay(new BigDecimal("0.00"));
        expectedResult.setPart(false);
        expectedResult.setCustomerRefund(new BigDecimal("0.00"));
        expectedResult.setCustomerRefundItem("customerRefundItem");
        expectedResult.setShopTotal(new BigDecimal("0.00"));
        expectedResult.setOnlinePay(false);
        expectedResult.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setAcceptTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setDeliveryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReqReason("cancelReqReason");
        expectedResult.setRefundReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRefundReqReason("cancelReqReason");
        expectedResult.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReason("下单超时未确认，系统自动取消");
        expectedResult.setCancelRoleName("desc");
        expectedResult.setCompleteTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final UnItem unItem = new UnItem();
        unItem.setItemSku("itemSku");
        unItem.setItemName("itemName");
        unItem.setItemPrice(new BigDecimal("0.00"));
        unItem.setItemCount(new BigDecimal("0.00"));
        unItem.setItemTotal(new BigDecimal("0.00"));
        unItem.setItemSpec("itemName");
        unItem.setItemProperty("itemProperty");
        unItem.setCartId(0);
        unItem.setActualPrice(new BigDecimal("0.00"));
        unItem.setSettleType(0);
        unItem.setUnItemSkuId("unItemSkuId");
        unItem.setThirdSkuId("thirdSkuId");
        expectedResult.setArrayOfUnItem(Arrays.asList(unItem));
        final UnRemind unRemind = new UnRemind();
        unRemind.setOrderId("id");
        unRemind.setRemindId("remindId");
        unRemind.setRemindTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setArrayOfUnRemind(Arrays.asList(unRemind));
        final UnDiscount unDiscount = new UnDiscount();
        expectedResult.setArrayOfUnDiscount(Arrays.asList(unDiscount));

        // Run the test
        final UnOrder result = eleUnOrderParseImplUnderTest.fromOrderCancelCancelReq(oMessage);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testFromOrderCancelAgreed() {
        // Setup
        final OMessage oMessage = new OMessage();
        oMessage.setRequestId("requestId");
        oMessage.setType(0);
        oMessage.setAppId(0);
        oMessage.setMessage("message");
        oMessage.setShopId(0L);

        final UnOrder expectedResult = new UnOrder();
        expectedResult.setShopId(0L);
        expectedResult.setShopName("shopName");
        expectedResult.setCbMsgType(0);
        expectedResult.setOrderId("id");
        expectedResult.setOrderViewId("id");
        expectedResult.setOrderDaySn("orderDaySn");
        expectedResult.setOrderRemark("description");
        expectedResult.setReserve(false);
        expectedResult.setCustomerName("consignee");
        expectedResult.setCustomerPhone("customerPhone");
        expectedResult.setPrivacyPhone("privacyPhone");
        expectedResult.setCustomerAddress("customerAddress");
        expectedResult.setCustomerNumber(0);
        expectedResult.setFirstOrder(false);
        expectedResult.setShipLatitude("shipLatitude");
        expectedResult.setShipLongitude("shipLongitude");
        expectedResult.setShipperName("name");
        expectedResult.setShipperPhone("phone");
        expectedResult.setThirdShipper(false);
        expectedResult.setInvoiced(false);
        expectedResult.setInvoiceType(0);
        expectedResult.setInvoiceTitle("invoice");
        expectedResult.setTaxpayerId("taxpayerId");
        expectedResult.setTotal(new BigDecimal("0.00"));
        expectedResult.setShipTotal(new BigDecimal("0.00"));
        expectedResult.setItemTotal(new BigDecimal("0.00"));
        expectedResult.setPackageTotal(new BigDecimal("0.00"));
        expectedResult.setDiscountTotal(new BigDecimal("0.00"));
        expectedResult.setEnterpriseDiscount(new BigDecimal("0.00"));
        expectedResult.setPlatformDiscount(new BigDecimal("0.00"));
        expectedResult.setServiceFeeRate(new BigDecimal("0.00"));
        expectedResult.setServiceFee(new BigDecimal("0.00"));
        expectedResult.setCustomerActualPay(new BigDecimal("0.00"));
        expectedResult.setPart(false);
        expectedResult.setCustomerRefund(new BigDecimal("0.00"));
        expectedResult.setCustomerRefundItem("customerRefundItem");
        expectedResult.setShopTotal(new BigDecimal("0.00"));
        expectedResult.setOnlinePay(false);
        expectedResult.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setAcceptTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setDeliveryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReqReason("cancelReqReason");
        expectedResult.setRefundReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRefundReqReason("cancelReqReason");
        expectedResult.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReason("下单超时未确认，系统自动取消");
        expectedResult.setCancelRoleName("desc");
        expectedResult.setCompleteTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final UnItem unItem = new UnItem();
        unItem.setItemSku("itemSku");
        unItem.setItemName("itemName");
        unItem.setItemPrice(new BigDecimal("0.00"));
        unItem.setItemCount(new BigDecimal("0.00"));
        unItem.setItemTotal(new BigDecimal("0.00"));
        unItem.setItemSpec("itemName");
        unItem.setItemProperty("itemProperty");
        unItem.setCartId(0);
        unItem.setActualPrice(new BigDecimal("0.00"));
        unItem.setSettleType(0);
        unItem.setUnItemSkuId("unItemSkuId");
        unItem.setThirdSkuId("thirdSkuId");
        expectedResult.setArrayOfUnItem(Arrays.asList(unItem));
        final UnRemind unRemind = new UnRemind();
        unRemind.setOrderId("id");
        unRemind.setRemindId("remindId");
        unRemind.setRemindTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setArrayOfUnRemind(Arrays.asList(unRemind));
        final UnDiscount unDiscount = new UnDiscount();
        expectedResult.setArrayOfUnDiscount(Arrays.asList(unDiscount));

        // Run the test
        final UnOrder result = eleUnOrderParseImplUnderTest.fromOrderCancelAgreed(oMessage);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testFromOrderCancelDisagreed() {
        // Setup
        final OMessage oMessage = new OMessage();
        oMessage.setRequestId("requestId");
        oMessage.setType(0);
        oMessage.setAppId(0);
        oMessage.setMessage("message");
        oMessage.setShopId(0L);

        final UnOrder expectedResult = new UnOrder();
        expectedResult.setShopId(0L);
        expectedResult.setShopName("shopName");
        expectedResult.setCbMsgType(0);
        expectedResult.setOrderId("id");
        expectedResult.setOrderViewId("id");
        expectedResult.setOrderDaySn("orderDaySn");
        expectedResult.setOrderRemark("description");
        expectedResult.setReserve(false);
        expectedResult.setCustomerName("consignee");
        expectedResult.setCustomerPhone("customerPhone");
        expectedResult.setPrivacyPhone("privacyPhone");
        expectedResult.setCustomerAddress("customerAddress");
        expectedResult.setCustomerNumber(0);
        expectedResult.setFirstOrder(false);
        expectedResult.setShipLatitude("shipLatitude");
        expectedResult.setShipLongitude("shipLongitude");
        expectedResult.setShipperName("name");
        expectedResult.setShipperPhone("phone");
        expectedResult.setThirdShipper(false);
        expectedResult.setInvoiced(false);
        expectedResult.setInvoiceType(0);
        expectedResult.setInvoiceTitle("invoice");
        expectedResult.setTaxpayerId("taxpayerId");
        expectedResult.setTotal(new BigDecimal("0.00"));
        expectedResult.setShipTotal(new BigDecimal("0.00"));
        expectedResult.setItemTotal(new BigDecimal("0.00"));
        expectedResult.setPackageTotal(new BigDecimal("0.00"));
        expectedResult.setDiscountTotal(new BigDecimal("0.00"));
        expectedResult.setEnterpriseDiscount(new BigDecimal("0.00"));
        expectedResult.setPlatformDiscount(new BigDecimal("0.00"));
        expectedResult.setServiceFeeRate(new BigDecimal("0.00"));
        expectedResult.setServiceFee(new BigDecimal("0.00"));
        expectedResult.setCustomerActualPay(new BigDecimal("0.00"));
        expectedResult.setPart(false);
        expectedResult.setCustomerRefund(new BigDecimal("0.00"));
        expectedResult.setCustomerRefundItem("customerRefundItem");
        expectedResult.setShopTotal(new BigDecimal("0.00"));
        expectedResult.setOnlinePay(false);
        expectedResult.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setAcceptTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setDeliveryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReqReason("cancelReqReason");
        expectedResult.setRefundReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRefundReqReason("cancelReqReason");
        expectedResult.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReason("下单超时未确认，系统自动取消");
        expectedResult.setCancelRoleName("desc");
        expectedResult.setCompleteTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final UnItem unItem = new UnItem();
        unItem.setItemSku("itemSku");
        unItem.setItemName("itemName");
        unItem.setItemPrice(new BigDecimal("0.00"));
        unItem.setItemCount(new BigDecimal("0.00"));
        unItem.setItemTotal(new BigDecimal("0.00"));
        unItem.setItemSpec("itemName");
        unItem.setItemProperty("itemProperty");
        unItem.setCartId(0);
        unItem.setActualPrice(new BigDecimal("0.00"));
        unItem.setSettleType(0);
        unItem.setUnItemSkuId("unItemSkuId");
        unItem.setThirdSkuId("thirdSkuId");
        expectedResult.setArrayOfUnItem(Arrays.asList(unItem));
        final UnRemind unRemind = new UnRemind();
        unRemind.setOrderId("id");
        unRemind.setRemindId("remindId");
        unRemind.setRemindTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setArrayOfUnRemind(Arrays.asList(unRemind));
        final UnDiscount unDiscount = new UnDiscount();
        expectedResult.setArrayOfUnDiscount(Arrays.asList(unDiscount));

        // Run the test
        final UnOrder result = eleUnOrderParseImplUnderTest.fromOrderCancelDisagreed(oMessage);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testFromOrderCancelArbitrationEffective() {
        // Setup
        final OMessage oMessage = new OMessage();
        oMessage.setRequestId("requestId");
        oMessage.setType(0);
        oMessage.setAppId(0);
        oMessage.setMessage("message");
        oMessage.setShopId(0L);

        final UnOrder expectedResult = new UnOrder();
        expectedResult.setShopId(0L);
        expectedResult.setShopName("shopName");
        expectedResult.setCbMsgType(0);
        expectedResult.setOrderId("id");
        expectedResult.setOrderViewId("id");
        expectedResult.setOrderDaySn("orderDaySn");
        expectedResult.setOrderRemark("description");
        expectedResult.setReserve(false);
        expectedResult.setCustomerName("consignee");
        expectedResult.setCustomerPhone("customerPhone");
        expectedResult.setPrivacyPhone("privacyPhone");
        expectedResult.setCustomerAddress("customerAddress");
        expectedResult.setCustomerNumber(0);
        expectedResult.setFirstOrder(false);
        expectedResult.setShipLatitude("shipLatitude");
        expectedResult.setShipLongitude("shipLongitude");
        expectedResult.setShipperName("name");
        expectedResult.setShipperPhone("phone");
        expectedResult.setThirdShipper(false);
        expectedResult.setInvoiced(false);
        expectedResult.setInvoiceType(0);
        expectedResult.setInvoiceTitle("invoice");
        expectedResult.setTaxpayerId("taxpayerId");
        expectedResult.setTotal(new BigDecimal("0.00"));
        expectedResult.setShipTotal(new BigDecimal("0.00"));
        expectedResult.setItemTotal(new BigDecimal("0.00"));
        expectedResult.setPackageTotal(new BigDecimal("0.00"));
        expectedResult.setDiscountTotal(new BigDecimal("0.00"));
        expectedResult.setEnterpriseDiscount(new BigDecimal("0.00"));
        expectedResult.setPlatformDiscount(new BigDecimal("0.00"));
        expectedResult.setServiceFeeRate(new BigDecimal("0.00"));
        expectedResult.setServiceFee(new BigDecimal("0.00"));
        expectedResult.setCustomerActualPay(new BigDecimal("0.00"));
        expectedResult.setPart(false);
        expectedResult.setCustomerRefund(new BigDecimal("0.00"));
        expectedResult.setCustomerRefundItem("customerRefundItem");
        expectedResult.setShopTotal(new BigDecimal("0.00"));
        expectedResult.setOnlinePay(false);
        expectedResult.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setAcceptTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setDeliveryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReqReason("cancelReqReason");
        expectedResult.setRefundReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRefundReqReason("cancelReqReason");
        expectedResult.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReason("下单超时未确认，系统自动取消");
        expectedResult.setCancelRoleName("desc");
        expectedResult.setCompleteTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final UnItem unItem = new UnItem();
        unItem.setItemSku("itemSku");
        unItem.setItemName("itemName");
        unItem.setItemPrice(new BigDecimal("0.00"));
        unItem.setItemCount(new BigDecimal("0.00"));
        unItem.setItemTotal(new BigDecimal("0.00"));
        unItem.setItemSpec("itemName");
        unItem.setItemProperty("itemProperty");
        unItem.setCartId(0);
        unItem.setActualPrice(new BigDecimal("0.00"));
        unItem.setSettleType(0);
        unItem.setUnItemSkuId("unItemSkuId");
        unItem.setThirdSkuId("thirdSkuId");
        expectedResult.setArrayOfUnItem(Arrays.asList(unItem));
        final UnRemind unRemind = new UnRemind();
        unRemind.setOrderId("id");
        unRemind.setRemindId("remindId");
        unRemind.setRemindTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setArrayOfUnRemind(Arrays.asList(unRemind));
        final UnDiscount unDiscount = new UnDiscount();
        expectedResult.setArrayOfUnDiscount(Arrays.asList(unDiscount));

        // Run the test
        final UnOrder result = eleUnOrderParseImplUnderTest.fromOrderCancelArbitrationEffective(oMessage);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testFromOrderRefundReq() {
        // Setup
        final OMessage oMessage = new OMessage();
        oMessage.setRequestId("requestId");
        oMessage.setType(0);
        oMessage.setAppId(0);
        oMessage.setMessage("message");
        oMessage.setShopId(0L);

        final UnOrder expectedResult = new UnOrder();
        expectedResult.setShopId(0L);
        expectedResult.setShopName("shopName");
        expectedResult.setCbMsgType(0);
        expectedResult.setOrderId("id");
        expectedResult.setOrderViewId("id");
        expectedResult.setOrderDaySn("orderDaySn");
        expectedResult.setOrderRemark("description");
        expectedResult.setReserve(false);
        expectedResult.setCustomerName("consignee");
        expectedResult.setCustomerPhone("customerPhone");
        expectedResult.setPrivacyPhone("privacyPhone");
        expectedResult.setCustomerAddress("customerAddress");
        expectedResult.setCustomerNumber(0);
        expectedResult.setFirstOrder(false);
        expectedResult.setShipLatitude("shipLatitude");
        expectedResult.setShipLongitude("shipLongitude");
        expectedResult.setShipperName("name");
        expectedResult.setShipperPhone("phone");
        expectedResult.setThirdShipper(false);
        expectedResult.setInvoiced(false);
        expectedResult.setInvoiceType(0);
        expectedResult.setInvoiceTitle("invoice");
        expectedResult.setTaxpayerId("taxpayerId");
        expectedResult.setTotal(new BigDecimal("0.00"));
        expectedResult.setShipTotal(new BigDecimal("0.00"));
        expectedResult.setItemTotal(new BigDecimal("0.00"));
        expectedResult.setPackageTotal(new BigDecimal("0.00"));
        expectedResult.setDiscountTotal(new BigDecimal("0.00"));
        expectedResult.setEnterpriseDiscount(new BigDecimal("0.00"));
        expectedResult.setPlatformDiscount(new BigDecimal("0.00"));
        expectedResult.setServiceFeeRate(new BigDecimal("0.00"));
        expectedResult.setServiceFee(new BigDecimal("0.00"));
        expectedResult.setCustomerActualPay(new BigDecimal("0.00"));
        expectedResult.setPart(false);
        expectedResult.setCustomerRefund(new BigDecimal("0.00"));
        expectedResult.setCustomerRefundItem("customerRefundItem");
        expectedResult.setShopTotal(new BigDecimal("0.00"));
        expectedResult.setOnlinePay(false);
        expectedResult.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setAcceptTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setDeliveryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReqReason("cancelReqReason");
        expectedResult.setRefundReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRefundReqReason("cancelReqReason");
        expectedResult.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReason("下单超时未确认，系统自动取消");
        expectedResult.setCancelRoleName("desc");
        expectedResult.setCompleteTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final UnItem unItem = new UnItem();
        unItem.setItemSku("itemSku");
        unItem.setItemName("itemName");
        unItem.setItemPrice(new BigDecimal("0.00"));
        unItem.setItemCount(new BigDecimal("0.00"));
        unItem.setItemTotal(new BigDecimal("0.00"));
        unItem.setItemSpec("itemName");
        unItem.setItemProperty("itemProperty");
        unItem.setCartId(0);
        unItem.setActualPrice(new BigDecimal("0.00"));
        unItem.setSettleType(0);
        unItem.setUnItemSkuId("unItemSkuId");
        unItem.setThirdSkuId("thirdSkuId");
        expectedResult.setArrayOfUnItem(Arrays.asList(unItem));
        final UnRemind unRemind = new UnRemind();
        unRemind.setOrderId("id");
        unRemind.setRemindId("remindId");
        unRemind.setRemindTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setArrayOfUnRemind(Arrays.asList(unRemind));
        final UnDiscount unDiscount = new UnDiscount();
        expectedResult.setArrayOfUnDiscount(Arrays.asList(unDiscount));

        // Run the test
        final UnOrder result = eleUnOrderParseImplUnderTest.fromOrderRefundReq(oMessage);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testFromOrderCancelRefundReq() {
        // Setup
        final OMessage oMessage = new OMessage();
        oMessage.setRequestId("requestId");
        oMessage.setType(0);
        oMessage.setAppId(0);
        oMessage.setMessage("message");
        oMessage.setShopId(0L);

        final UnOrder expectedResult = new UnOrder();
        expectedResult.setShopId(0L);
        expectedResult.setShopName("shopName");
        expectedResult.setCbMsgType(0);
        expectedResult.setOrderId("id");
        expectedResult.setOrderViewId("id");
        expectedResult.setOrderDaySn("orderDaySn");
        expectedResult.setOrderRemark("description");
        expectedResult.setReserve(false);
        expectedResult.setCustomerName("consignee");
        expectedResult.setCustomerPhone("customerPhone");
        expectedResult.setPrivacyPhone("privacyPhone");
        expectedResult.setCustomerAddress("customerAddress");
        expectedResult.setCustomerNumber(0);
        expectedResult.setFirstOrder(false);
        expectedResult.setShipLatitude("shipLatitude");
        expectedResult.setShipLongitude("shipLongitude");
        expectedResult.setShipperName("name");
        expectedResult.setShipperPhone("phone");
        expectedResult.setThirdShipper(false);
        expectedResult.setInvoiced(false);
        expectedResult.setInvoiceType(0);
        expectedResult.setInvoiceTitle("invoice");
        expectedResult.setTaxpayerId("taxpayerId");
        expectedResult.setTotal(new BigDecimal("0.00"));
        expectedResult.setShipTotal(new BigDecimal("0.00"));
        expectedResult.setItemTotal(new BigDecimal("0.00"));
        expectedResult.setPackageTotal(new BigDecimal("0.00"));
        expectedResult.setDiscountTotal(new BigDecimal("0.00"));
        expectedResult.setEnterpriseDiscount(new BigDecimal("0.00"));
        expectedResult.setPlatformDiscount(new BigDecimal("0.00"));
        expectedResult.setServiceFeeRate(new BigDecimal("0.00"));
        expectedResult.setServiceFee(new BigDecimal("0.00"));
        expectedResult.setCustomerActualPay(new BigDecimal("0.00"));
        expectedResult.setPart(false);
        expectedResult.setCustomerRefund(new BigDecimal("0.00"));
        expectedResult.setCustomerRefundItem("customerRefundItem");
        expectedResult.setShopTotal(new BigDecimal("0.00"));
        expectedResult.setOnlinePay(false);
        expectedResult.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setAcceptTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setDeliveryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReqReason("cancelReqReason");
        expectedResult.setRefundReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRefundReqReason("cancelReqReason");
        expectedResult.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReason("下单超时未确认，系统自动取消");
        expectedResult.setCancelRoleName("desc");
        expectedResult.setCompleteTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final UnItem unItem = new UnItem();
        unItem.setItemSku("itemSku");
        unItem.setItemName("itemName");
        unItem.setItemPrice(new BigDecimal("0.00"));
        unItem.setItemCount(new BigDecimal("0.00"));
        unItem.setItemTotal(new BigDecimal("0.00"));
        unItem.setItemSpec("itemName");
        unItem.setItemProperty("itemProperty");
        unItem.setCartId(0);
        unItem.setActualPrice(new BigDecimal("0.00"));
        unItem.setSettleType(0);
        unItem.setUnItemSkuId("unItemSkuId");
        unItem.setThirdSkuId("thirdSkuId");
        expectedResult.setArrayOfUnItem(Arrays.asList(unItem));
        final UnRemind unRemind = new UnRemind();
        unRemind.setOrderId("id");
        unRemind.setRemindId("remindId");
        unRemind.setRemindTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setArrayOfUnRemind(Arrays.asList(unRemind));
        final UnDiscount unDiscount = new UnDiscount();
        expectedResult.setArrayOfUnDiscount(Arrays.asList(unDiscount));

        // Run the test
        final UnOrder result = eleUnOrderParseImplUnderTest.fromOrderCancelRefundReq(oMessage);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testFromOrderRefundAgreed() {
        // Setup
        final OMessage oMessage = new OMessage();
        oMessage.setRequestId("requestId");
        oMessage.setType(0);
        oMessage.setAppId(0);
        oMessage.setMessage("message");
        oMessage.setShopId(0L);

        final UnOrder expectedResult = new UnOrder();
        expectedResult.setShopId(0L);
        expectedResult.setShopName("shopName");
        expectedResult.setCbMsgType(0);
        expectedResult.setOrderId("id");
        expectedResult.setOrderViewId("id");
        expectedResult.setOrderDaySn("orderDaySn");
        expectedResult.setOrderRemark("description");
        expectedResult.setReserve(false);
        expectedResult.setCustomerName("consignee");
        expectedResult.setCustomerPhone("customerPhone");
        expectedResult.setPrivacyPhone("privacyPhone");
        expectedResult.setCustomerAddress("customerAddress");
        expectedResult.setCustomerNumber(0);
        expectedResult.setFirstOrder(false);
        expectedResult.setShipLatitude("shipLatitude");
        expectedResult.setShipLongitude("shipLongitude");
        expectedResult.setShipperName("name");
        expectedResult.setShipperPhone("phone");
        expectedResult.setThirdShipper(false);
        expectedResult.setInvoiced(false);
        expectedResult.setInvoiceType(0);
        expectedResult.setInvoiceTitle("invoice");
        expectedResult.setTaxpayerId("taxpayerId");
        expectedResult.setTotal(new BigDecimal("0.00"));
        expectedResult.setShipTotal(new BigDecimal("0.00"));
        expectedResult.setItemTotal(new BigDecimal("0.00"));
        expectedResult.setPackageTotal(new BigDecimal("0.00"));
        expectedResult.setDiscountTotal(new BigDecimal("0.00"));
        expectedResult.setEnterpriseDiscount(new BigDecimal("0.00"));
        expectedResult.setPlatformDiscount(new BigDecimal("0.00"));
        expectedResult.setServiceFeeRate(new BigDecimal("0.00"));
        expectedResult.setServiceFee(new BigDecimal("0.00"));
        expectedResult.setCustomerActualPay(new BigDecimal("0.00"));
        expectedResult.setPart(false);
        expectedResult.setCustomerRefund(new BigDecimal("0.00"));
        expectedResult.setCustomerRefundItem("customerRefundItem");
        expectedResult.setShopTotal(new BigDecimal("0.00"));
        expectedResult.setOnlinePay(false);
        expectedResult.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setAcceptTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setDeliveryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReqReason("cancelReqReason");
        expectedResult.setRefundReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRefundReqReason("cancelReqReason");
        expectedResult.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReason("下单超时未确认，系统自动取消");
        expectedResult.setCancelRoleName("desc");
        expectedResult.setCompleteTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final UnItem unItem = new UnItem();
        unItem.setItemSku("itemSku");
        unItem.setItemName("itemName");
        unItem.setItemPrice(new BigDecimal("0.00"));
        unItem.setItemCount(new BigDecimal("0.00"));
        unItem.setItemTotal(new BigDecimal("0.00"));
        unItem.setItemSpec("itemName");
        unItem.setItemProperty("itemProperty");
        unItem.setCartId(0);
        unItem.setActualPrice(new BigDecimal("0.00"));
        unItem.setSettleType(0);
        unItem.setUnItemSkuId("unItemSkuId");
        unItem.setThirdSkuId("thirdSkuId");
        expectedResult.setArrayOfUnItem(Arrays.asList(unItem));
        final UnRemind unRemind = new UnRemind();
        unRemind.setOrderId("id");
        unRemind.setRemindId("remindId");
        unRemind.setRemindTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setArrayOfUnRemind(Arrays.asList(unRemind));
        final UnDiscount unDiscount = new UnDiscount();
        expectedResult.setArrayOfUnDiscount(Arrays.asList(unDiscount));

        // Run the test
        final UnOrder result = eleUnOrderParseImplUnderTest.fromOrderRefundAgreed(oMessage);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testFromOrderRefundDisagreed() {
        // Setup
        final OMessage oMessage = new OMessage();
        oMessage.setRequestId("requestId");
        oMessage.setType(0);
        oMessage.setAppId(0);
        oMessage.setMessage("message");
        oMessage.setShopId(0L);

        final UnOrder expectedResult = new UnOrder();
        expectedResult.setShopId(0L);
        expectedResult.setShopName("shopName");
        expectedResult.setCbMsgType(0);
        expectedResult.setOrderId("id");
        expectedResult.setOrderViewId("id");
        expectedResult.setOrderDaySn("orderDaySn");
        expectedResult.setOrderRemark("description");
        expectedResult.setReserve(false);
        expectedResult.setCustomerName("consignee");
        expectedResult.setCustomerPhone("customerPhone");
        expectedResult.setPrivacyPhone("privacyPhone");
        expectedResult.setCustomerAddress("customerAddress");
        expectedResult.setCustomerNumber(0);
        expectedResult.setFirstOrder(false);
        expectedResult.setShipLatitude("shipLatitude");
        expectedResult.setShipLongitude("shipLongitude");
        expectedResult.setShipperName("name");
        expectedResult.setShipperPhone("phone");
        expectedResult.setThirdShipper(false);
        expectedResult.setInvoiced(false);
        expectedResult.setInvoiceType(0);
        expectedResult.setInvoiceTitle("invoice");
        expectedResult.setTaxpayerId("taxpayerId");
        expectedResult.setTotal(new BigDecimal("0.00"));
        expectedResult.setShipTotal(new BigDecimal("0.00"));
        expectedResult.setItemTotal(new BigDecimal("0.00"));
        expectedResult.setPackageTotal(new BigDecimal("0.00"));
        expectedResult.setDiscountTotal(new BigDecimal("0.00"));
        expectedResult.setEnterpriseDiscount(new BigDecimal("0.00"));
        expectedResult.setPlatformDiscount(new BigDecimal("0.00"));
        expectedResult.setServiceFeeRate(new BigDecimal("0.00"));
        expectedResult.setServiceFee(new BigDecimal("0.00"));
        expectedResult.setCustomerActualPay(new BigDecimal("0.00"));
        expectedResult.setPart(false);
        expectedResult.setCustomerRefund(new BigDecimal("0.00"));
        expectedResult.setCustomerRefundItem("customerRefundItem");
        expectedResult.setShopTotal(new BigDecimal("0.00"));
        expectedResult.setOnlinePay(false);
        expectedResult.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setAcceptTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setDeliveryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReqReason("cancelReqReason");
        expectedResult.setRefundReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRefundReqReason("cancelReqReason");
        expectedResult.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReason("下单超时未确认，系统自动取消");
        expectedResult.setCancelRoleName("desc");
        expectedResult.setCompleteTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final UnItem unItem = new UnItem();
        unItem.setItemSku("itemSku");
        unItem.setItemName("itemName");
        unItem.setItemPrice(new BigDecimal("0.00"));
        unItem.setItemCount(new BigDecimal("0.00"));
        unItem.setItemTotal(new BigDecimal("0.00"));
        unItem.setItemSpec("itemName");
        unItem.setItemProperty("itemProperty");
        unItem.setCartId(0);
        unItem.setActualPrice(new BigDecimal("0.00"));
        unItem.setSettleType(0);
        unItem.setUnItemSkuId("unItemSkuId");
        unItem.setThirdSkuId("thirdSkuId");
        expectedResult.setArrayOfUnItem(Arrays.asList(unItem));
        final UnRemind unRemind = new UnRemind();
        unRemind.setOrderId("id");
        unRemind.setRemindId("remindId");
        unRemind.setRemindTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setArrayOfUnRemind(Arrays.asList(unRemind));
        final UnDiscount unDiscount = new UnDiscount();
        expectedResult.setArrayOfUnDiscount(Arrays.asList(unDiscount));

        // Run the test
        final UnOrder result = eleUnOrderParseImplUnderTest.fromOrderRefundDisagreed(oMessage);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testFromOrderRefundArbitrationEffective() {
        // Setup
        final OMessage oMessage = new OMessage();
        oMessage.setRequestId("requestId");
        oMessage.setType(0);
        oMessage.setAppId(0);
        oMessage.setMessage("message");
        oMessage.setShopId(0L);

        final UnOrder expectedResult = new UnOrder();
        expectedResult.setShopId(0L);
        expectedResult.setShopName("shopName");
        expectedResult.setCbMsgType(0);
        expectedResult.setOrderId("id");
        expectedResult.setOrderViewId("id");
        expectedResult.setOrderDaySn("orderDaySn");
        expectedResult.setOrderRemark("description");
        expectedResult.setReserve(false);
        expectedResult.setCustomerName("consignee");
        expectedResult.setCustomerPhone("customerPhone");
        expectedResult.setPrivacyPhone("privacyPhone");
        expectedResult.setCustomerAddress("customerAddress");
        expectedResult.setCustomerNumber(0);
        expectedResult.setFirstOrder(false);
        expectedResult.setShipLatitude("shipLatitude");
        expectedResult.setShipLongitude("shipLongitude");
        expectedResult.setShipperName("name");
        expectedResult.setShipperPhone("phone");
        expectedResult.setThirdShipper(false);
        expectedResult.setInvoiced(false);
        expectedResult.setInvoiceType(0);
        expectedResult.setInvoiceTitle("invoice");
        expectedResult.setTaxpayerId("taxpayerId");
        expectedResult.setTotal(new BigDecimal("0.00"));
        expectedResult.setShipTotal(new BigDecimal("0.00"));
        expectedResult.setItemTotal(new BigDecimal("0.00"));
        expectedResult.setPackageTotal(new BigDecimal("0.00"));
        expectedResult.setDiscountTotal(new BigDecimal("0.00"));
        expectedResult.setEnterpriseDiscount(new BigDecimal("0.00"));
        expectedResult.setPlatformDiscount(new BigDecimal("0.00"));
        expectedResult.setServiceFeeRate(new BigDecimal("0.00"));
        expectedResult.setServiceFee(new BigDecimal("0.00"));
        expectedResult.setCustomerActualPay(new BigDecimal("0.00"));
        expectedResult.setPart(false);
        expectedResult.setCustomerRefund(new BigDecimal("0.00"));
        expectedResult.setCustomerRefundItem("customerRefundItem");
        expectedResult.setShopTotal(new BigDecimal("0.00"));
        expectedResult.setOnlinePay(false);
        expectedResult.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setAcceptTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setDeliveryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReqReason("cancelReqReason");
        expectedResult.setRefundReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRefundReqReason("cancelReqReason");
        expectedResult.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReason("下单超时未确认，系统自动取消");
        expectedResult.setCancelRoleName("desc");
        expectedResult.setCompleteTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final UnItem unItem = new UnItem();
        unItem.setItemSku("itemSku");
        unItem.setItemName("itemName");
        unItem.setItemPrice(new BigDecimal("0.00"));
        unItem.setItemCount(new BigDecimal("0.00"));
        unItem.setItemTotal(new BigDecimal("0.00"));
        unItem.setItemSpec("itemName");
        unItem.setItemProperty("itemProperty");
        unItem.setCartId(0);
        unItem.setActualPrice(new BigDecimal("0.00"));
        unItem.setSettleType(0);
        unItem.setUnItemSkuId("unItemSkuId");
        unItem.setThirdSkuId("thirdSkuId");
        expectedResult.setArrayOfUnItem(Arrays.asList(unItem));
        final UnRemind unRemind = new UnRemind();
        unRemind.setOrderId("id");
        unRemind.setRemindId("remindId");
        unRemind.setRemindTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setArrayOfUnRemind(Arrays.asList(unRemind));
        final UnDiscount unDiscount = new UnDiscount();
        expectedResult.setArrayOfUnDiscount(Arrays.asList(unDiscount));

        // Run the test
        final UnOrder result = eleUnOrderParseImplUnderTest.fromOrderRefundArbitrationEffective(oMessage);

        // Verify the results
        assertEquals(expectedResult, result);
    }
}
