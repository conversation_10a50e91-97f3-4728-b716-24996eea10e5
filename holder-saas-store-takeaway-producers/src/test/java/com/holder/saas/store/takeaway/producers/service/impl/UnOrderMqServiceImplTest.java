package com.holder.saas.store.takeaway.producers.service.impl;

import com.holderzone.framework.rocketmq.common.DefaultRocketMqProducer;
import com.holderzone.saas.store.dto.takeaway.UnOrder;
import org.apache.rocketmq.client.producer.MessageQueueSelector;
import org.apache.rocketmq.common.message.Message;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;

@RunWith(MockitoJUnitRunner.class)
public class UnOrderMqServiceImplTest {

    @Mock
    private DefaultRocketMqProducer mockDefaultRocketMqProducer;

    private UnOrderMqServiceImpl unOrderMqServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        unOrderMqServiceImplUnderTest = new UnOrderMqServiceImpl(mockDefaultRocketMqProducer);
    }

    @Test
    public void testSendUnOrder() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setOrderStatus(0);
        unOrder.setShopId(0L);
        unOrder.setShopName("shopName");
        unOrder.setOrderSubType(0);
        unOrder.setOrderId("orderId");

        // Run the test
        unOrderMqServiceImplUnderTest.sendUnOrder(unOrder);

        // Verify the results
        verify(mockDefaultRocketMqProducer).sendMessage(any(Message.class), any(MessageQueueSelector.class),
                eq("orderId"));
    }
}
