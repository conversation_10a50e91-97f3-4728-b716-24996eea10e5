package com.holder.saas.store.takeaway.producers.service.impl;

import com.holder.saas.store.takeaway.producers.entity.domain.MtAuthDO;
import com.holder.saas.store.takeaway.producers.service.MtAuthService;
import com.holderzone.saas.store.dto.business.group.StoreBindDTO;
import com.holderzone.saas.store.dto.takeaway.response.StoreAuthDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class GroupStoreBindServiceImplTest {

    @Mock
    private MtAuthService mockTakeawayInfoAuth;

    private GroupStoreBindServiceImpl groupStoreBindServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        groupStoreBindServiceImplUnderTest = new GroupStoreBindServiceImpl(mockTakeawayInfoAuth);
    }

    @Test
    public void testUnbindStore() {
        // Setup
        final StoreBindDTO storeBind = new StoreBindDTO();
        storeBind.setGroupBuyType(0);
        storeBind.setAccountId("accountId");
        storeBind.setPoiId("poiId");
        storeBind.setStoreGuid("storeGuid");

        // Run the test
        groupStoreBindServiceImplUnderTest.unbindStore(storeBind);

        // Verify the results
    }

    @Test
    public void testListAllByStoreGuid() {
        // Setup
        final StoreAuthDTO storeAuthDTO = new StoreAuthDTO();
        storeAuthDTO.setTakeoutType(0);
        storeAuthDTO.setPlatformName("desc");
        storeAuthDTO.setStoreGuid("storeGuid");
        storeAuthDTO.setShopName("poiName");
        storeAuthDTO.setBindingStatus(0);
        final List<StoreAuthDTO> expectedResult = Arrays.asList(storeAuthDTO);

        // Configure MtAuthService.getAuth(...).
        final MtAuthDO mtAuthDO = new MtAuthDO();
        mtAuthDO.setId(0);
        mtAuthDO.setGuid("fda25a1f-4e00-4b79-b976-dacd651f851f");
        mtAuthDO.setEPoiId("ePoiId");
        mtAuthDO.setMtStoreGuid("storeGuid");
        mtAuthDO.setMtStoreName("poiName");
        when(mockTakeawayInfoAuth.getAuth("storeGuid", 0)).thenReturn(mtAuthDO);

        // Run the test
        final List<StoreAuthDTO> result = groupStoreBindServiceImplUnderTest.listAllByStoreGuid("storeGuid");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testListAllByStoreGuid_MtAuthServiceReturnsNull() {
        // Setup
        final StoreAuthDTO storeAuthDTO = new StoreAuthDTO();
        storeAuthDTO.setTakeoutType(0);
        storeAuthDTO.setPlatformName("desc");
        storeAuthDTO.setStoreGuid("storeGuid");
        storeAuthDTO.setShopName("poiName");
        storeAuthDTO.setBindingStatus(0);
        final List<StoreAuthDTO> expectedResult = Arrays.asList(storeAuthDTO);
        when(mockTakeawayInfoAuth.getAuth("storeGuid", 0)).thenReturn(null);

        // Run the test
        final List<StoreAuthDTO> result = groupStoreBindServiceImplUnderTest.listAllByStoreGuid("storeGuid");

        // Verify the results
        assertEquals(expectedResult, result);
    }
}
