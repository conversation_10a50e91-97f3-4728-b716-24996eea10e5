package com.holder.saas.store.takeaway.producers;

import com.holder.saas.store.takeaway.producers.service.job.MtOrderJob;
import com.holder.saas.store.takeaway.producers.utils.sdk.jd.*;
import com.holder.saas.store.takeaway.producers.utils.sdk.jd.dto.*;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import java.time.LocalDateTime;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = HolderSaasStoreTakeawayProducersApplication.class)
@WebAppConfiguration
@Slf4j
public class MtTest{

    @Autowired
    private MtOrderJob mtOrderJob;

    /**
     * 测试接口轮训
     *
     * @throws Exception
     */
    @Test
    public void testRecursion() {
        log.info("<==========(美团)订单轮询，最近5分钟内未被商家确认的美团订单号列表刷新开始==========");
        final LocalDateTime endTime = LocalDateTime.now();
        final LocalDateTime startTime = endTime.minusMinutes(5);
        mtOrderJob.recursion(0, 0);
        log.info("<==========(美团)订单轮询，最近5分钟内未被商家确认的美团订单号列表刷新结束==========>");
    }

    @Test
    public void testJd() {
//        QuerySkuInfoRequest querySkuInfoRequest = new QuerySkuInfoRequest("de3b91647ad84fdf8ba9ddc5786f9d9c","a973d7ae8cfb4b30bc0be4af05ec4bda", "dc7a4b2b-0246-451a-a63b-7d607f891259");
        //查询所有商品列表
//        List<SkuMain> skuMainList = querySkuInfoRequest.execute();
//        System.out.println(skuMainList);
        //需查询所有分类
//        QueryCategoriesRequest queryCategoriesRequest = new QueryCategoriesRequest("de3b91647ad84fdf8ba9ddc5786f9d9c", "a973d7ae8cfb4b30bc0be4af05ec4bda", "dc7a4b2b-0246-451a-a63b-7d607f891259");
//        List<ShopCategory> categories = queryCategoriesRequest.execute();
//        System.out.println(categories);
        GetStationsByVenderIdRequest getStationsByVenderIdRequest = new GetStationsByVenderIdRequest("de3b91647ad84fdf8ba9ddc5786f9d9c", "a973d7ae8cfb4b30bc0be4af05ec4bda","dc7a4b2b-0246-451a-a63b-7d607f891259");
        List<String> storeNoList = getStationsByVenderIdRequest.execute();
//        GetStoreInfoByStationNoRequest request = new GetStoreInfoByStationNoRequest("de3b91647ad84fdf8ba9ddc5786f9d9c", "a973d7ae8cfb4b30bc0be4af05ec4bda", "dc7a4b2b-0246-451a-a63b-7d607f891259");
//        StoreInfo storeInfo = request.execute("15052266");
//        log.info(String.valueOf(storeInfo));
//
//        GetStationsByVenderIdRequest getStationsByVenderIdRequest = new GetStationsByVenderIdRequest("de3b91647ad84fdf8ba9ddc5786f9d9c","a973d7ae8cfb4b30bc0be4af05ec4bda", "dc7a4b2b-0246-451a-a63b-7d607f891259");
//        List<String> storeNoList = getStationsByVenderIdRequest.execute();
//
//        log.info(storeNoList.toString());
    }
}
