package com.holder.saas.store.takeaway.producers.service.impl;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.business.group.StoreBindDTO;
import com.holderzone.saas.store.dto.order.response.groupon.GroupVerifyDTO;
import com.holderzone.saas.store.dto.takeaway.request.CouPonPreReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.CouPonReqDTO;
import org.junit.Before;
import org.junit.Test;

import java.util.Collections;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;

public class DaZhongGroupBuyServiceImplTest {

    private DaZhongGroupBuyServiceImpl daZhongGroupBuyServiceImplUnderTest;

    @Before
    public void setUp() {
        daZhongGroupBuyServiceImplUnderTest = new DaZhongGroupBuyServiceImpl();
    }

    @Test(expected = BusinessException.class)
    public void testBindStore() {
        daZhongGroupBuyServiceImplUnderTest.bindStore(new StoreBindDTO());
    }

    @Test
    public void testGetToken() {
        assertNull(daZhongGroupBuyServiceImplUnderTest.getToken());
    }

    @Test
    public void testCouponPrepare() {
        assertEquals(Collections.emptyList(), daZhongGroupBuyServiceImplUnderTest.couponPrepare(new CouPonPreReqDTO()));
    }

    @Test
    public void testVerifyCoupon() {
        assertEquals(Collections.emptyList(), daZhongGroupBuyServiceImplUnderTest.verifyCoupon(new CouPonReqDTO()));
    }

    @Test
    public void testRevokeCoupon() {
        assertNull(daZhongGroupBuyServiceImplUnderTest.revokeCoupon(new GroupVerifyDTO()));
    }
}
