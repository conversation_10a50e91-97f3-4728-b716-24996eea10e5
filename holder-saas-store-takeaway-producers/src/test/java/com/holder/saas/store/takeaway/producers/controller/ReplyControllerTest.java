package com.holder.saas.store.takeaway.producers.controller;

import com.holder.saas.store.takeaway.producers.service.UnOrderReplyServiceFactory;
import com.holderzone.saas.store.dto.takeaway.OrderType;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(ReplyController.class)
public class ReplyControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private UnOrderReplyServiceFactory mockUnOrderReplyServiceFactory;

    @Test
    public void testReply() throws Exception {
        // Setup
        when(mockUnOrderReplyServiceFactory.create(OrderType.TakeoutSubType.MT_TAKEOUT)).thenReturn(null);

        // Run the test and verify the results
        mockMvc.perform(post("/reply")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }
}
