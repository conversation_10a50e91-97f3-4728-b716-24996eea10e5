package com.holder.saas.store.takeaway.producers.service.impl;

import com.google.common.util.concurrent.MoreExecutors;
import com.holder.saas.store.takeaway.producers.config.MeiTuanConfig;
import com.holder.saas.store.takeaway.producers.entity.domain.MtAuthDO;
import com.holder.saas.store.takeaway.producers.entity.domain.MtPrivacyDO;
import com.holder.saas.store.takeaway.producers.entity.dto.MtAuthDTO;
import com.holder.saas.store.takeaway.producers.entity.dto.MtCbPrivacyDTO;
import com.holder.saas.store.takeaway.producers.mapstruct.MtPrivacyMapstruct;
import com.holder.saas.store.takeaway.producers.service.*;
import com.holder.saas.store.takeaway.producers.service.rpc.ErpFeignService;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.resource.common.dto.enterprise.MultiMemberDTO;
import com.holderzone.saas.store.dto.takeaway.*;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class MtCallbackServiceImplTest {

    @Mock
    private MtUnOrderParser mockMtUnOrderParser;
    @Mock
    private UnOrderMqService mockUnOrderMqService;
    @Mock
    private MtPrivacyService mockMtPrivacyService;
    @Mock
    private MtPrivacyMapstruct mockMtPrivacyMapstruct;
    @Mock
    private DistributedService mockDistributedService;
    @Mock
    private OrderTradeDetailMqService mockOrderTradeDetailMqService;
    @Mock
    private MtAuthService mockMtAuthService;
    @Mock
    private ErpFeignService mockErpFeignService;
    @Mock
    private MeiTuanConfig mockMeiTuanConfig;

    private MtCallbackServiceImpl mtCallbackServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        mtCallbackServiceImplUnderTest = new MtCallbackServiceImpl(mockMtUnOrderParser, mockUnOrderMqService,
                MoreExecutors.newDirectExecutorService(), mockMtPrivacyService, mockMtPrivacyMapstruct,
                mockDistributedService, mockOrderTradeDetailMqService, mockMtAuthService, mockErpFeignService,
                mockMeiTuanConfig);
    }

    @Test
    public void testOrderCallback() {
        // Setup
        final MtCallbackDTO mtCallbackDTO = new MtCallbackDTO();
        mtCallbackDTO.setDeveloperId(0);
        mtCallbackDTO.setSign("sign");
        mtCallbackDTO.setAppAuthToken("appAuthToken");
        mtCallbackDTO.setBusinessId("businessId");
        mtCallbackDTO.setPoiId("poiId");

        // Configure MtUnOrderParser.fromMtCbOrderCreated(...).
        final UnOrder unOrder = new UnOrder();
        unOrder.setOrderStatus(0);
        unOrder.setShopId(0L);
        unOrder.setShopName("shopName");
        unOrder.setCbMsgType(0);
        unOrder.setReplyMsgType(0);
        final MtCallbackDTO mtCallbackDTO1 = new MtCallbackDTO();
        mtCallbackDTO1.setDeveloperId(0);
        mtCallbackDTO1.setSign("sign");
        mtCallbackDTO1.setAppAuthToken("appAuthToken");
        mtCallbackDTO1.setBusinessId("businessId");
        mtCallbackDTO1.setPoiId("poiId");
        when(mockMtUnOrderParser.fromMtCbOrderCreated(mtCallbackDTO1)).thenReturn(unOrder);

        // Configure MtUnOrderParser.fromMtCbOrderCanceled(...).
        final UnOrder unOrder1 = new UnOrder();
        unOrder1.setOrderStatus(0);
        unOrder1.setShopId(0L);
        unOrder1.setShopName("shopName");
        unOrder1.setCbMsgType(0);
        unOrder1.setReplyMsgType(0);
        final MtCallbackDTO mtCallbackDTO2 = new MtCallbackDTO();
        mtCallbackDTO2.setDeveloperId(0);
        mtCallbackDTO2.setSign("sign");
        mtCallbackDTO2.setAppAuthToken("appAuthToken");
        mtCallbackDTO2.setBusinessId("businessId");
        mtCallbackDTO2.setPoiId("poiId");
        when(mockMtUnOrderParser.fromMtCbOrderCanceled(mtCallbackDTO2)).thenReturn(unOrder1);

        // Configure MtUnOrderParser.fromMtCbOrderRefund(...).
        final UnOrder unOrder2 = new UnOrder();
        unOrder2.setOrderStatus(0);
        unOrder2.setShopId(0L);
        unOrder2.setShopName("shopName");
        unOrder2.setCbMsgType(0);
        unOrder2.setReplyMsgType(0);
        final MtCallbackDTO mtCallbackDTO3 = new MtCallbackDTO();
        mtCallbackDTO3.setDeveloperId(0);
        mtCallbackDTO3.setSign("sign");
        mtCallbackDTO3.setAppAuthToken("appAuthToken");
        mtCallbackDTO3.setBusinessId("businessId");
        mtCallbackDTO3.setPoiId("poiId");
        when(mockMtUnOrderParser.fromMtCbOrderRefund(mtCallbackDTO3)).thenReturn(unOrder2);

        // Configure MtUnOrderParser.fromMtCbOrderPartRefund(...).
        final UnOrder unOrder3 = new UnOrder();
        unOrder3.setOrderStatus(0);
        unOrder3.setShopId(0L);
        unOrder3.setShopName("shopName");
        unOrder3.setCbMsgType(0);
        unOrder3.setReplyMsgType(0);
        final MtCallbackDTO mtCallbackDTO4 = new MtCallbackDTO();
        mtCallbackDTO4.setDeveloperId(0);
        mtCallbackDTO4.setSign("sign");
        mtCallbackDTO4.setAppAuthToken("appAuthToken");
        mtCallbackDTO4.setBusinessId("businessId");
        mtCallbackDTO4.setPoiId("poiId");
        when(mockMtUnOrderParser.fromMtCbOrderPartRefund(mtCallbackDTO4)).thenReturn(unOrder3);

        // Configure MtUnOrderParser.fromMtCbOrderConfirmed(...).
        final UnOrder unOrder4 = new UnOrder();
        unOrder4.setOrderStatus(0);
        unOrder4.setShopId(0L);
        unOrder4.setShopName("shopName");
        unOrder4.setCbMsgType(0);
        unOrder4.setReplyMsgType(0);
        final MtCallbackDTO mtCallbackDTO5 = new MtCallbackDTO();
        mtCallbackDTO5.setDeveloperId(0);
        mtCallbackDTO5.setSign("sign");
        mtCallbackDTO5.setAppAuthToken("appAuthToken");
        mtCallbackDTO5.setBusinessId("businessId");
        mtCallbackDTO5.setPoiId("poiId");
        when(mockMtUnOrderParser.fromMtCbOrderConfirmed(mtCallbackDTO5)).thenReturn(unOrder4);

        // Configure MtUnOrderParser.fromMtCbOrderFinished(...).
        final UnOrder unOrder5 = new UnOrder();
        unOrder5.setOrderStatus(0);
        unOrder5.setShopId(0L);
        unOrder5.setShopName("shopName");
        unOrder5.setCbMsgType(0);
        unOrder5.setReplyMsgType(0);
        final MtCallbackDTO mtCallbackDTO6 = new MtCallbackDTO();
        mtCallbackDTO6.setDeveloperId(0);
        mtCallbackDTO6.setSign("sign");
        mtCallbackDTO6.setAppAuthToken("appAuthToken");
        mtCallbackDTO6.setBusinessId("businessId");
        mtCallbackDTO6.setPoiId("poiId");
        when(mockMtUnOrderParser.fromMtCbOrderFinished(mtCallbackDTO6)).thenReturn(unOrder5);

        // Configure MtUnOrderParser.fromMtCbShippingStatus(...).
        final UnOrder unOrder6 = new UnOrder();
        unOrder6.setOrderStatus(0);
        unOrder6.setShopId(0L);
        unOrder6.setShopName("shopName");
        unOrder6.setCbMsgType(0);
        unOrder6.setReplyMsgType(0);
        final MtCallbackDTO mtCallbackDTO7 = new MtCallbackDTO();
        mtCallbackDTO7.setDeveloperId(0);
        mtCallbackDTO7.setSign("sign");
        mtCallbackDTO7.setAppAuthToken("appAuthToken");
        mtCallbackDTO7.setBusinessId("businessId");
        mtCallbackDTO7.setPoiId("poiId");
        when(mockMtUnOrderParser.fromMtCbShippingStatus(mtCallbackDTO7)).thenReturn(unOrder6);

        // Run the test
        mtCallbackServiceImplUnderTest.orderCallback(mtCallbackDTO, "path");

        // Verify the results
        // Confirm UnOrderMqService.sendUnOrder(...).
        final UnOrder unorder = new UnOrder();
        unorder.setOrderStatus(0);
        unorder.setShopId(0L);
        unorder.setShopName("shopName");
        unorder.setCbMsgType(0);
        unorder.setReplyMsgType(0);
        verify(mockUnOrderMqService).sendUnOrder(unorder);
    }

    @Test
    public void testOrderPrivacyDegrade() {
        // Setup
        final MtCallbackDTO mtCallbackDTO = new MtCallbackDTO();
        mtCallbackDTO.setDeveloperId(0);
        mtCallbackDTO.setSign("sign");
        mtCallbackDTO.setAppAuthToken("appAuthToken");
        mtCallbackDTO.setBusinessId("businessId");
        mtCallbackDTO.setPoiId("poiId");

        when(mockMeiTuanConfig.getDeveloperId()).thenReturn(0);
        when(mockMeiTuanConfig.getSignKey()).thenReturn("result");

        // Configure MtPrivacyMapstruct.fromMtCbPrivacyDetail(...).
        final MtPrivacyDO mtPrivacyDO = new MtPrivacyDO();
        mtPrivacyDO.setId(0L);
        mtPrivacyDO.setGuid("************************************");
        mtPrivacyDO.setEPoiId("ePoiId");
        mtPrivacyDO.setOrderId("orderId");
        mtPrivacyDO.setOrderIdView("orderIdView");
        final List<MtPrivacyDO> mtPrivacyDOS = Arrays.asList(mtPrivacyDO);
        final MtCbPrivacyDTO.MtCbPrivacyDetail mtCbPrivacyDetail = new MtCbPrivacyDTO.MtCbPrivacyDetail();
        mtCbPrivacyDetail.setDaySeq(1L);
        mtCbPrivacyDetail.setEPoiId("ePoiId");
        mtCbPrivacyDetail.setOrderId(0L);
        mtCbPrivacyDetail.setOrderIdView(0L);
        mtCbPrivacyDetail.setRealPhoneNumber("realPhoneNumber");
        final List<MtCbPrivacyDTO.MtCbPrivacyDetail> mtCbPrivacyDetails = Arrays.asList(mtCbPrivacyDetail);
        when(mockMtPrivacyMapstruct.fromMtCbPrivacyDetail(mtCbPrivacyDetails)).thenReturn(mtPrivacyDOS);

        when(mockDistributedService.nextMtGuid()).thenReturn("************************************");

        // Run the test
        mtCallbackServiceImplUnderTest.orderPrivacyDegrade(mtCallbackDTO);

        // Verify the results
        // Confirm MtPrivacyService.saveBatch(...).
        final MtPrivacyDO mtPrivacyDO1 = new MtPrivacyDO();
        mtPrivacyDO1.setId(0L);
        mtPrivacyDO1.setGuid("************************************");
        mtPrivacyDO1.setEPoiId("ePoiId");
        mtPrivacyDO1.setOrderId("orderId");
        mtPrivacyDO1.setOrderIdView("orderIdView");
        final List<MtPrivacyDO> entityList = Arrays.asList(mtPrivacyDO1);
        verify(mockMtPrivacyService).saveBatch(entityList);
    }

    @Test
    public void testOrderPrivacyDegrade_MtPrivacyMapstructReturnsNoItems() {
        // Setup
        final MtCallbackDTO mtCallbackDTO = new MtCallbackDTO();
        mtCallbackDTO.setDeveloperId(0);
        mtCallbackDTO.setSign("sign");
        mtCallbackDTO.setAppAuthToken("appAuthToken");
        mtCallbackDTO.setBusinessId("businessId");
        mtCallbackDTO.setPoiId("poiId");

        when(mockMeiTuanConfig.getDeveloperId()).thenReturn(0);
        when(mockMeiTuanConfig.getSignKey()).thenReturn("result");

        // Configure MtPrivacyMapstruct.fromMtCbPrivacyDetail(...).
        final MtCbPrivacyDTO.MtCbPrivacyDetail mtCbPrivacyDetail = new MtCbPrivacyDTO.MtCbPrivacyDetail();
        mtCbPrivacyDetail.setDaySeq(1L);
        mtCbPrivacyDetail.setEPoiId("ePoiId");
        mtCbPrivacyDetail.setOrderId(0L);
        mtCbPrivacyDetail.setOrderIdView(0L);
        mtCbPrivacyDetail.setRealPhoneNumber("realPhoneNumber");
        final List<MtCbPrivacyDTO.MtCbPrivacyDetail> mtCbPrivacyDetails = Arrays.asList(mtCbPrivacyDetail);
        when(mockMtPrivacyMapstruct.fromMtCbPrivacyDetail(mtCbPrivacyDetails)).thenReturn(Collections.emptyList());

        when(mockDistributedService.nextMtGuid()).thenReturn("************************************");

        // Run the test
        mtCallbackServiceImplUnderTest.orderPrivacyDegrade(mtCallbackDTO);

        // Verify the results
        // Confirm MtPrivacyService.saveBatch(...).
        final MtPrivacyDO mtPrivacyDO = new MtPrivacyDO();
        mtPrivacyDO.setId(0L);
        mtPrivacyDO.setGuid("************************************");
        mtPrivacyDO.setEPoiId("ePoiId");
        mtPrivacyDO.setOrderId("orderId");
        mtPrivacyDO.setOrderIdView("orderIdView");
        final List<MtPrivacyDO> entityList = Arrays.asList(mtPrivacyDO);
        verify(mockMtPrivacyService).saveBatch(entityList);
    }

    @Test
    public void testOrderTradeDetailCallback() {
        // Setup
        final MtCallbackSettlementDTO mtCallbackSettlementDTO = new MtCallbackSettlementDTO();
        mtCallbackSettlementDTO.setDeveloperId(0);
        mtCallbackSettlementDTO.setSign("sign");
        mtCallbackSettlementDTO.setEpoiId("epoiId");
        mtCallbackSettlementDTO.setTradeDetail("tradeDetail");

        // Configure MtAuthService.getAuth(...).
        final MtAuthDO mtAuthDO = new MtAuthDO();
        mtAuthDO.setId(0);
        mtAuthDO.setGuid("cd4a6d5c-ecb5-4a77-95af-cf2457b216ef");
        mtAuthDO.setEPoiId("ePoiId");
        mtAuthDO.setEnterpriseGuid("enterpriseGuid");
        mtAuthDO.setMtStoreGuid("mtStoreGuid");
        when(mockMtAuthService.getAuth("epoiId", 0)).thenReturn(mtAuthDO);

        // Run the test
        mtCallbackServiceImplUnderTest.orderTradeDetailCallback(mtCallbackSettlementDTO);

        // Verify the results
        // Confirm OrderTradeDetailMqService.sendOrderTradeDetail(...).
        final TakeoutOrderTradeDetailDTO tradeDetailDTO = new TakeoutOrderTradeDetailDTO();
        tradeDetailDTO.setEnterpriseGuid("enterpriseGuid");
        tradeDetailDTO.setActivityDetails("activityDetails");
        tradeDetailDTO.setCommisionAmount("commisionAmount");
        tradeDetailDTO.setFoodAmount("foodAmount");
        tradeDetailDTO.setOfflineOrderSkPayAmount("offlineOrderSkPayAmount");
        verify(mockOrderTradeDetailMqService).sendOrderTradeDetail(tradeDetailDTO);
    }

    @Test
    public void testAuthorizationCallback() {
        // Setup
        final MtCallbackAuthorizationDTO mtCallbackDTO = new MtCallbackAuthorizationDTO();
        mtCallbackDTO.setCode("code");
        mtCallbackDTO.setSign("sign");
        mtCallbackDTO.setDeveloperId(0L);
        mtCallbackDTO.setBusinessId(0);
        mtCallbackDTO.setState("state");

        when(mockMeiTuanConfig.getSignKey()).thenReturn("signKey");
        when(mockMeiTuanConfig.getDeveloperId()).thenReturn(0);

        // Configure ErpFeignService.getMultiMemberByGuid(...).
        final MultiMemberDTO multiMemberDTO = new MultiMemberDTO();
        multiMemberDTO.setId(0L);
        multiMemberDTO.setEnterpriseGuid("enterpriseGuid");
        multiMemberDTO.setMultiMemberGuid("ePoiId");
        multiMemberDTO.setMultiMemberName("multiMemberName");
        multiMemberDTO.setEnabled(false);
        when(mockErpFeignService.getMultiMemberByGuid("state")).thenReturn(multiMemberDTO);

        // Run the test
        mtCallbackServiceImplUnderTest.authorizationCallback(mtCallbackDTO);

        // Verify the results
        // Confirm MtAuthService.saveCallbackAuth(...).
        final MtAuthDTO mtAuthDTO = new MtAuthDTO();
        mtAuthDTO.setMtStoreGuid("mtStoreGuid");
        mtAuthDTO.setMtStoreName("mtStoreGuid");
        mtAuthDTO.setEPoiId("ePoiId");
        mtAuthDTO.setEnterpriseGuid("enterpriseGuid");
        mtAuthDTO.setAccessToken("accessToken");
        mtAuthDTO.setRefreshToken("refreshToken");
        mtAuthDTO.setBusinessId((byte) 0b0);
        mtAuthDTO.setActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        mtAuthDTO.setExpireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        mtAuthDTO.setRefreshTokenExpire(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        mtAuthDTO.setDeleted(false);
        verify(mockMtAuthService).saveCallbackAuth(mtAuthDTO);
    }

    @Test(expected = BusinessException.class)
    public void testAuthorizationCallback_ErpFeignServiceReturnsNull() {
        // Setup
        final MtCallbackAuthorizationDTO mtCallbackDTO = new MtCallbackAuthorizationDTO();
        mtCallbackDTO.setCode("code");
        mtCallbackDTO.setSign("sign");
        mtCallbackDTO.setDeveloperId(0L);
        mtCallbackDTO.setBusinessId(0);
        mtCallbackDTO.setState("state");

        when(mockMeiTuanConfig.getSignKey()).thenReturn("signKey");
        when(mockMeiTuanConfig.getDeveloperId()).thenReturn(0);
        when(mockErpFeignService.getMultiMemberByGuid("state")).thenReturn(null);

        // Run the test
        mtCallbackServiceImplUnderTest.authorizationCallback(mtCallbackDTO);
    }

    @Test
    public void testUnAuthorizationCallback() {
        // Setup
        final MtCallbackUnbindAuthorizationDTO mtCallbackDTO = new MtCallbackUnbindAuthorizationDTO();
        mtCallbackDTO.setMsgType("msgType");
        mtCallbackDTO.setSign("sign");
        mtCallbackDTO.setDeveloperId("developerId");
        mtCallbackDTO.setMessage("message");
        mtCallbackDTO.setMsgId("msgId");

        // Run the test
        mtCallbackServiceImplUnderTest.unAuthorizationCallback(mtCallbackDTO);

        // Verify the results
        verify(mockMtAuthService).deleteAuthByMt("opBizCode", 0);
    }
}
