package com.holder.saas.store.takeaway.producers.event;

import com.holder.saas.store.takeaway.producers.service.StarDeliveryService;
import com.holder.saas.store.takeaway.producers.service.UnOrderDeliveryService;
import com.holderzone.saas.store.dto.takeaway.UnOrder;
import org.apache.rocketmq.common.message.MessageExt;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.net.InetSocketAddress;

import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;

@RunWith(MockitoJUnitRunner.class)
public class DistributionListenerTest {

    @Mock
    private UnOrderDeliveryService mockMtOrderReplyService;
    @Mock
    private UnOrderDeliveryService mockEleOrderReplyService;
    @Mock
    private UnOrderDeliveryService mockOwnOrderReplyService;
    @Mock
    private UnOrderDeliveryService mockTcdOrderReplyServiceImpl;
    @Mock
    private StarDeliveryService mockStarDeliveryService;

    private DistributionListener distributionListenerUnderTest;

    @Before
    public void setUp() throws Exception {
        distributionListenerUnderTest = new DistributionListener(mockMtOrderReplyService, mockEleOrderReplyService,
                mockOwnOrderReplyService, mockTcdOrderReplyServiceImpl, mockStarDeliveryService);
    }

    @Test
    public void testConsumeMsg() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setOrderStatus(0);
        unOrder.setShopId(0L);
        unOrder.setSubStoreId(0L);
        unOrder.setOrderSubType(0);
        unOrder.setOrderId("orderId");

        final MessageExt messageExt = new MessageExt();
        messageExt.setQueueId(0);
        messageExt.setBornTimestamp(0L);
        messageExt.setBornHost(new InetSocketAddress("localhost", 80));
        messageExt.setStoreTimestamp(0L);
        messageExt.setStoreHost(new InetSocketAddress("localhost", 80));

        // Run the test
        final boolean result = distributionListenerUnderTest.consumeMsg(unOrder, messageExt);

        // Verify the results
        assertTrue(result);

        // Confirm StarDeliveryService.starDelivery(...).
        final UnOrder unOrder1 = new UnOrder();
        unOrder1.setOrderStatus(0);
        unOrder1.setShopId(0L);
        unOrder1.setSubStoreId(0L);
        unOrder1.setOrderSubType(0);
        unOrder1.setOrderId("orderId");
        verify(mockStarDeliveryService).starDelivery(any(UnOrderDeliveryService.class), eq(unOrder1));
    }

    @Test
    public void testCreate() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setOrderStatus(0);
        unOrder.setShopId(0L);
        unOrder.setSubStoreId(0L);
        unOrder.setOrderSubType(0);
        unOrder.setOrderId("orderId");

        // Run the test
        final UnOrderDeliveryService result = distributionListenerUnderTest.create(unOrder);

        // Verify the results
    }
}
