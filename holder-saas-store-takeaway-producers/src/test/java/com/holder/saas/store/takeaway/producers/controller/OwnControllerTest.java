package com.holder.saas.store.takeaway.producers.controller;

import com.holder.saas.store.takeaway.producers.entity.domain.HolderAuthDO;
import com.holder.saas.store.takeaway.producers.mapstruct.HolderMapstruct;
import com.holder.saas.store.takeaway.producers.service.HolderAuthService;
import com.holder.saas.store.takeaway.producers.service.OwnCallbackService;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.takeaway.OwnCallbackResponse;
import com.holderzone.saas.store.dto.takeaway.TakeoutOrderDTO;
import com.holderzone.saas.store.dto.takeaway.request.SalesOrderDTO;
import com.holderzone.saas.store.dto.takeaway.response.HolderAuthDTO;
import com.holderzone.saas.store.dto.takeaway.response.OwnDistributionDTO;
import com.holderzone.saas.store.dto.takeaway.response.StoreAuthDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(OwnController.class)
public class OwnControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private OwnCallbackService mockOwnCallbackService;
    @MockBean
    private HolderAuthService mockHolderAuthService;
    @MockBean
    private HolderMapstruct mockHolderMapstruct;

    @Test
    public void testOrderCallback() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(post("/own/callback/order")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));

        // Confirm OwnCallbackService.orderCallback(...).
        final SalesOrderDTO salesOrderDTO = new SalesOrderDTO();
        salesOrderDTO.setUserName("UserName");
        salesOrderDTO.setStoreId(0);
        salesOrderDTO.setOrderId(0L);
        salesOrderDTO.setStoreGuid("StoreGuid");
        salesOrderDTO.setCode("Code");
        verify(mockOwnCallbackService).orderCallback(salesOrderDTO);
    }

    @Test
    public void testGetDistribution() throws Exception {
        // Setup
        // Configure HolderAuthService.getDistribution(...).
        final OwnDistributionDTO ownDistributionDTO = new OwnDistributionDTO();
        ownDistributionDTO.setType(0);
        ownDistributionDTO.setTypeName("TypeName");
        final List<OwnDistributionDTO> ownDistributionDTOS = Arrays.asList(ownDistributionDTO);
        final BaseDTO baseDTO = new BaseDTO();
        baseDTO.setDeviceType(0);
        baseDTO.setDeviceId("deviceId");
        baseDTO.setEnterpriseGuid("enterpriseGuid");
        baseDTO.setEnterpriseName("enterpriseName");
        baseDTO.setStoreGuid("storeGuid");
        when(mockHolderAuthService.getDistribution(baseDTO)).thenReturn(ownDistributionDTOS);

        // Run the test and verify the results
        mockMvc.perform(post("/own/get_distribution")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGetDistribution_HolderAuthServiceReturnsNoItems() throws Exception {
        // Setup
        // Configure HolderAuthService.getDistribution(...).
        final BaseDTO baseDTO = new BaseDTO();
        baseDTO.setDeviceType(0);
        baseDTO.setDeviceId("deviceId");
        baseDTO.setEnterpriseGuid("enterpriseGuid");
        baseDTO.setEnterpriseName("enterpriseName");
        baseDTO.setStoreGuid("storeGuid");
        when(mockHolderAuthService.getDistribution(baseDTO)).thenReturn(Collections.emptyList());

        // Run the test and verify the results
        mockMvc.perform(post("/own/get_distribution")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("[]", true));
    }

    @Test
    public void testListAuth() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(post("/own/list_auth")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("[]", true));
    }

    @Test
    public void testGetTakeoutAuth() throws Exception {
        // Setup
        // Configure HolderAuthService.getTakeoutAuth(...).
        final StoreAuthDTO storeAuthDTO = new StoreAuthDTO();
        storeAuthDTO.setTakeoutType(0);
        storeAuthDTO.setPlatformName("platformName");
        storeAuthDTO.setStoreNumber("storeNumber");
        storeAuthDTO.setStoreGuid("storeGuid");
        storeAuthDTO.setStoreName("storeName");
        final StoreAuthDTO storeAuthDTO1 = new StoreAuthDTO();
        storeAuthDTO1.setTakeoutType(0);
        storeAuthDTO1.setPlatformName("platformName");
        storeAuthDTO1.setStoreNumber("storeNumber");
        storeAuthDTO1.setStoreGuid("storeGuid");
        storeAuthDTO1.setStoreName("storeName");
        when(mockHolderAuthService.getTakeoutAuth(storeAuthDTO1)).thenReturn(storeAuthDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/own/get_takeout_auth")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGetHolder() throws Exception {
        // Setup
        // Configure HolderAuthService.getHolderAuth(...).
        final HolderAuthDO holderAuthDO = new HolderAuthDO();
        holderAuthDO.setId(0L);
        holderAuthDO.setGuid("b42739a0-f988-4103-8df8-6bcdaae5ae6d");
        holderAuthDO.setEnterpriseGuid("enterpriseGuid");
        holderAuthDO.setStoreGuid("storeGuid");
        holderAuthDO.setUserId(0L);
        when(mockHolderAuthService.getHolderAuth("storeGuid")).thenReturn(holderAuthDO);

        // Configure HolderMapstruct.toHolderAuthDTO(...).
        final HolderAuthDTO holderAuthDTO = new HolderAuthDTO();
        holderAuthDTO.setId(0L);
        holderAuthDTO.setGuid("ae2103c5-4ed6-4dd5-844b-f5f2726b3f0e");
        holderAuthDTO.setEnterpriseGuid("enterpriseGuid");
        holderAuthDTO.setStoreGuid("storeGuid");
        holderAuthDTO.setUserId(0L);
        final HolderAuthDO holderAuthDO1 = new HolderAuthDO();
        holderAuthDO1.setId(0L);
        holderAuthDO1.setGuid("b42739a0-f988-4103-8df8-6bcdaae5ae6d");
        holderAuthDO1.setEnterpriseGuid("enterpriseGuid");
        holderAuthDO1.setStoreGuid("storeGuid");
        holderAuthDO1.setUserId(0L);
        when(mockHolderMapstruct.toHolderAuthDTO(holderAuthDO1)).thenReturn(holderAuthDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/own/get_holder_auth")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGetToken() throws Exception {
        // Setup
        // Configure HolderAuthService.getHolderAuth(...).
        final HolderAuthDO holderAuthDO = new HolderAuthDO();
        holderAuthDO.setId(0L);
        holderAuthDO.setGuid("b42739a0-f988-4103-8df8-6bcdaae5ae6d");
        holderAuthDO.setEnterpriseGuid("enterpriseGuid");
        holderAuthDO.setStoreGuid("storeGuid");
        holderAuthDO.setUserId(0L);
        when(mockHolderAuthService.getHolderAuth("storeGuid")).thenReturn(holderAuthDO);

        // Configure HolderAuthService.getToken(...).
        final HolderAuthDO holderAuthDO1 = new HolderAuthDO();
        holderAuthDO1.setId(0L);
        holderAuthDO1.setGuid("b42739a0-f988-4103-8df8-6bcdaae5ae6d");
        holderAuthDO1.setEnterpriseGuid("enterpriseGuid");
        holderAuthDO1.setStoreGuid("storeGuid");
        holderAuthDO1.setUserId(0L);
        when(mockHolderAuthService.getToken(holderAuthDO1)).thenReturn("result");

        // Run the test and verify the results
        mockMvc.perform(post("/own/get_token")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGetToken_HolderAuthServiceGetTokenReturnsNull() throws Exception {
        // Setup
        // Configure HolderAuthService.getHolderAuth(...).
        final HolderAuthDO holderAuthDO = new HolderAuthDO();
        holderAuthDO.setId(0L);
        holderAuthDO.setGuid("b42739a0-f988-4103-8df8-6bcdaae5ae6d");
        holderAuthDO.setEnterpriseGuid("enterpriseGuid");
        holderAuthDO.setStoreGuid("storeGuid");
        holderAuthDO.setUserId(0L);
        when(mockHolderAuthService.getHolderAuth("storeGuid")).thenReturn(holderAuthDO);

        // Configure HolderAuthService.getToken(...).
        final HolderAuthDO holderAuthDO1 = new HolderAuthDO();
        holderAuthDO1.setId(0L);
        holderAuthDO1.setGuid("b42739a0-f988-4103-8df8-6bcdaae5ae6d");
        holderAuthDO1.setEnterpriseGuid("enterpriseGuid");
        holderAuthDO1.setStoreGuid("storeGuid");
        holderAuthDO1.setUserId(0L);
        when(mockHolderAuthService.getToken(holderAuthDO1)).thenReturn(null);

        // Run the test and verify the results
        mockMvc.perform(post("/own/get_token")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("", true));
    }

    @Test
    public void testGoShipping() throws Exception {
        // Setup
        // Configure HolderAuthService.goShipping(...).
        final TakeoutOrderDTO takeoutOrderDTO = new TakeoutOrderDTO();
        takeoutOrderDTO.setShopId(0L);
        takeoutOrderDTO.setShopName("shopName");
        takeoutOrderDTO.setOrderGuid("orderGuid");
        takeoutOrderDTO.setOrderStatus(0);
        takeoutOrderDTO.setOrderTagStatus(0);
        when(mockHolderAuthService.goShipping(takeoutOrderDTO)).thenReturn(new OwnCallbackResponse(0, "message"));

        // Run the test and verify the results
        mockMvc.perform(post("/own/go_shipping")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testDoneShipping() throws Exception {
        // Setup
        // Configure HolderAuthService.doneShipping(...).
        final TakeoutOrderDTO takeoutOrderDTO = new TakeoutOrderDTO();
        takeoutOrderDTO.setShopId(0L);
        takeoutOrderDTO.setShopName("shopName");
        takeoutOrderDTO.setOrderGuid("orderGuid");
        takeoutOrderDTO.setOrderStatus(0);
        takeoutOrderDTO.setOrderTagStatus(0);
        when(mockHolderAuthService.doneShipping(takeoutOrderDTO)).thenReturn(new OwnCallbackResponse(0, "message"));

        // Run the test and verify the results
        mockMvc.perform(post("/own/done_shipping")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testCancelShipping() throws Exception {
        // Setup
        // Configure HolderAuthService.cancelShipping(...).
        final TakeoutOrderDTO takeoutOrderDTO = new TakeoutOrderDTO();
        takeoutOrderDTO.setShopId(0L);
        takeoutOrderDTO.setShopName("shopName");
        takeoutOrderDTO.setOrderGuid("orderGuid");
        takeoutOrderDTO.setOrderStatus(0);
        takeoutOrderDTO.setOrderTagStatus(0);
        when(mockHolderAuthService.cancelShipping(takeoutOrderDTO)).thenReturn(new OwnCallbackResponse(0, "message"));

        // Run the test and verify the results
        mockMvc.perform(post("/own/cancel_shipping")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }
}
