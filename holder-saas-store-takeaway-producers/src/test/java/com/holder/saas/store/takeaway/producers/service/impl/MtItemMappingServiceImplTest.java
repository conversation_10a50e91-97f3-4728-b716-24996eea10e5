package com.holder.saas.store.takeaway.producers.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.MoreExecutors;
import com.google.gson.Gson;
import com.holder.saas.store.takeaway.producers.constant.TakeoutConstant;
import com.holder.saas.store.takeaway.producers.entity.domain.MtAuthDO;
import com.holder.saas.store.takeaway.producers.entity.dto.MtFoodInfo;
import com.holder.saas.store.takeaway.producers.entity.dto.MtFoodListAllRequest;
import com.holder.saas.store.takeaway.producers.entity.dto.MtRspDTO;
import com.holder.saas.store.takeaway.producers.entity.dto.MtSpecialFoodPageDTO;
import com.holder.saas.store.takeaway.producers.service.MtAuthService;
import com.holder.saas.store.takeaway.producers.utils.HttpsClientUtils;
import com.holder.saas.store.takeaway.producers.utils.SignUtil;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.takeaway.*;
import com.meituan.sdk.DefaultMeituanClient;
import com.meituan.sdk.MeituanClient;
import com.meituan.sdk.MeituanResponse;
import com.meituan.sdk.internal.exceptions.MtSdkException;
import com.meituan.sdk.internal.utils.JsonUtil;
import com.meituan.sdk.model.ddzh.ugc.ugcQueryShopReview.ReviewInfoDTO;
import com.meituan.sdk.model.waimaiNg.dish.dishFoodListAll.DishFoodListAllRequest;
import com.meituan.sdk.model.waimaiNg.dish.dishFoodListAll.FoodInfo;
import com.meituan.sdk.model.waimaiNg.special.specialFoodBindSpuAndSkuCode.SpecialFoodBindSpuAndSkuCodeRequest;
import com.sankuai.sjst.platform.developer.domain.RequestSysParams;
import com.sankuai.sjst.platform.developer.request.CipCaterTakeoutDishBaseQueryByEPoiIdRequest;
import lombok.Data;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.IOException;
import java.net.URISyntaxException;
import java.util.*;
import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class MtItemMappingServiceImplTest {

    @Mock
    private MtAuthService mockAuthService;

    private MtItemMappingServiceImpl mtItemMappingServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        mtItemMappingServiceImplUnderTest = new MtItemMappingServiceImpl(mockAuthService);
        ReflectionTestUtils.setField(mtItemMappingServiceImplUnderTest, "mtSignKey", "mtSignKey");
        ReflectionTestUtils.setField(mtItemMappingServiceImplUnderTest, "mtProductQueryThreadPool",
                MoreExecutors.newDirectExecutorService());
    }

    @Test
    public void testSpecial() throws MtSdkException {
        String appAuthToken = "74cb3d445a7878e256c167a81bedea52b871050042846357dc622abc51c343c7bbadffa7e1270933abfb23d8ce45d4dd5d46eb94c939669740b5690966189c87";

        MeituanClient meituanClient = DefaultMeituanClient.builder(104795L, "5jydvc7w8ee8fx7w").build();

        MtFoodListAllRequest dishFoodListAllRequest = new MtFoodListAllRequest();

        dishFoodListAllRequest.setOffset(1);
        dishFoodListAllRequest.setLimit(199);
        dishFoodListAllRequest.setNeedTopping(true);

        MeituanResponse<List<MtFoodInfo>> response = meituanClient.invokeApi(dishFoodListAllRequest, appAuthToken);
        if (response.isSuccess()) {
            List<MtFoodInfo> resp = response.getData();
            System.out.println(resp);
        } else {
            System.out.println("调用失败");
        }
    }

    @Test
    public void testMtFoodList() throws MtSdkException {
        String appAuthToken = "74cb3d445a7878e256c167a81bedea52b871050042846357dc622abc51c343c7bbadffa7e1270933abfb23d8ce45d4dd5d46eb94c939669740b5690966189c87";

        // 查询商品列表
        CipCaterTakeoutDishBaseQueryByEPoiIdRequest itemRequest = new CipCaterTakeoutDishBaseQueryByEPoiIdRequest();
        RequestSysParams itemRequestSysParams = new RequestSysParams("5jydvc7w8ee8fx7w", appAuthToken, TakeoutConstant.CHARSET_UTF_8);
        itemRequest.setRequestSysParams(itemRequestSysParams);
        itemRequest.setePoiId("2106221850429620006");
        try {
            String itemResult = itemRequest.doRequest();
            System.out.println(itemResult);
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    @Test
    public void testSpecialBind() throws MtSdkException, IOException, URISyntaxException {
        String appAuthToken = "089ea297bc6f5bf14870426419997f8f359c9f90b8a99f9bfdecb3c499677449be253762cb3ea05cc083b7918af58296098dba335a7ef947c4e02ce8738c5192";
        Map<String, String> params = Maps.newHashMap();
        params.put("appAuthToken", appAuthToken);
        params.put("charset", "UTF-8");
        params.put("timestamp", String.valueOf(System.currentTimeMillis() / 1000));
        params.put("developerId", "105325");
        Map<String, Object> map = Maps.newHashMap();
        map.put("offset", 1);
        map.put("limit", 199);
        params.put("biz", JsonUtil.toJson(map));
        params.put("version", "2");
        params.put("businessId", "2");
        String sign = SignUtil.getSign("ns7v29225tbu8jfv", params);
        params.put("sign", sign);
        String result = HttpsClientUtils.doPost(MT_LIST_QUERY, params);
        System.out.println(result);
    }
    private static final String MT_BATCH_QUERY = "https://api-open-cater.meituan.com/waimai/ng/special/food/batchQuery";

    private static final String MT_LIST_QUERY = "https://api-open-cater.meituan.com/waimai/ng/dish/food/listAll";


    @Data
    public static class Biz{
        private String ePoiId;
        private Integer businessIdentify;
        private Integer pageSize;
        private Integer pageNum;
    }

    @Test
    public void testGetType() {
        // Setup
        final UnMappedType unMappedType = new UnMappedType();
        unMappedType.setUnItemTypeId("name");
        unMappedType.setUnItemTypeName("name");
        final UnMappedItem unMappedItem = new UnMappedItem();
        unMappedItem.setUnItemId("dishId");
        unMappedItem.setUnItemName("dishName");
        unMappedType.setUnItemList(Arrays.asList(unMappedItem));
        final List<UnMappedType> expectedResult = Arrays.asList(unMappedType);

        // Configure MtAuthService.getAuth(...).
        final MtAuthDO mtAuthDO = new MtAuthDO();
        mtAuthDO.setId(0);
        mtAuthDO.setGuid("0353c687-9f24-46f7-b136-99843dd2e5f0");
        mtAuthDO.setEPoiId("storeGuid");
        mtAuthDO.setEnterpriseGuid("enterpriseGuid");
        mtAuthDO.setAccessToken("authToken");
        when(mockAuthService.getAuth("storeGuid", 0)).thenReturn(mtAuthDO);

        // Run the test
        final List<UnMappedType> result = mtItemMappingServiceImplUnderTest.getType("storeGuid");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test(expected = BusinessException.class)
    public void testGetType_MtAuthServiceGetAuthReturnsNull() {
        // Setup
        when(mockAuthService.getAuth("storeGuid", 0)).thenReturn(null);

        // Run the test
        mtItemMappingServiceImplUnderTest.getType("storeGuid");
    }

    @Test
    public void testGetItem() {
        // Setup
        final UnMappedItem unMappedItem = new UnMappedItem();
        unMappedItem.setUnItemId("dishId");
        unMappedItem.setUnItemName("dishName");
        unMappedItem.setUnItemSkuId("dishSkuId");
        unMappedItem.setUnItemSkuName("spec");
        unMappedItem.setUnItemNameWithSku("dishSkuName");
        unMappedItem.setUnItemTypeId("categoryName");
        unMappedItem.setUnItemTypeName("categoryName");
        unMappedItem.setErpItemSkuId("eDishSkuCode");
        unMappedItem.setErpStoreGuid("storeGuid");
        unMappedItem.setExtendValue("extendValue");
        unMappedItem.setMtSkuId("mtSkuId");
        final List<UnMappedItem> expectedResult = Arrays.asList(unMappedItem);

        // Configure MtAuthService.getAuth(...).
        final MtAuthDO mtAuthDO = new MtAuthDO();
        mtAuthDO.setId(0);
        mtAuthDO.setGuid("0353c687-9f24-46f7-b136-99843dd2e5f0");
        mtAuthDO.setEPoiId("storeGuid");
        mtAuthDO.setEnterpriseGuid("enterpriseGuid");
        mtAuthDO.setAccessToken("authToken");
        when(mockAuthService.getAuth("storeGuid", 0)).thenReturn(mtAuthDO);

        // Run the test
        final List<UnMappedItem> result = mtItemMappingServiceImplUnderTest.getItem("storeGuid");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test(expected = BusinessException.class)
    public void testGetItem_MtAuthServiceGetAuthReturnsNull() {
        // Setup
        when(mockAuthService.getAuth("storeGuid", 0)).thenReturn(null);

        // Run the test
        mtItemMappingServiceImplUnderTest.getItem("storeGuid");
    }

    @Test
    public void testGetItems() {
        // Setup
        final UnItemQueryReq unItemQueryReq = new UnItemQueryReq();
        unItemQueryReq.setKeywords("keywords");
        unItemQueryReq.setStoreGuids(Arrays.asList("value"));
        unItemQueryReq.setTakeoutType(0);
        unItemQueryReq.setBindingFlag(0);

        final UnMappedItem unMappedItem = new UnMappedItem();
        unMappedItem.setUnItemId("dishId");
        unMappedItem.setUnItemName("dishName");
        unMappedItem.setUnItemSkuId("dishSkuId");
        unMappedItem.setUnItemSkuName("spec");
        unMappedItem.setUnItemNameWithSku("dishSkuName");
        unMappedItem.setUnItemTypeId("categoryName");
        unMappedItem.setUnItemTypeName("categoryName");
        unMappedItem.setErpItemSkuId("eDishSkuCode");
        unMappedItem.setErpStoreGuid("storeGuid");
        unMappedItem.setExtendValue("extendValue");
        unMappedItem.setMtSkuId("mtSkuId");
        final List<UnMappedItem> expectedResult = Arrays.asList(unMappedItem);

        // Configure MtAuthService.getAuths(...).
        final MtAuthDO mtAuthDO = new MtAuthDO();
        mtAuthDO.setId(0);
        mtAuthDO.setGuid("0353c687-9f24-46f7-b136-99843dd2e5f0");
        mtAuthDO.setEPoiId("storeGuid");
        mtAuthDO.setEnterpriseGuid("enterpriseGuid");
        mtAuthDO.setAccessToken("authToken");
        final List<MtAuthDO> mtAuthDOS = Arrays.asList(mtAuthDO);
        when(mockAuthService.getAuths(Arrays.asList("value"), 0)).thenReturn(mtAuthDOS);

        // Run the test
        final List<UnMappedItem> result = mtItemMappingServiceImplUnderTest.getItems(unItemQueryReq);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockAuthService).correctAuth("storeGuid", 0, "error_type");
    }

    @Test
    public void testGetItems_MtAuthServiceGetAuthsReturnsNoItems() {
        // Setup
        final UnItemQueryReq unItemQueryReq = new UnItemQueryReq();
        unItemQueryReq.setKeywords("keywords");
        unItemQueryReq.setStoreGuids(Arrays.asList("value"));
        unItemQueryReq.setTakeoutType(0);
        unItemQueryReq.setBindingFlag(0);

        when(mockAuthService.getAuths(Arrays.asList("value"), 0)).thenReturn(Collections.emptyList());

        // Run the test
        final List<UnMappedItem> result = mtItemMappingServiceImplUnderTest.getItems(unItemQueryReq);

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testBindMapping() {
        // Setup
        final UnItemBindUnbindReq unItemBindUnbindReq = new UnItemBindUnbindReq();
        unItemBindUnbindReq.setUnItemId("dishId");
        unItemBindUnbindReq.setUnItemSkuId("dishId");
        unItemBindUnbindReq.setExtendValue("extendValue");
        unItemBindUnbindReq.setErpItemGuid("dishId");
        unItemBindUnbindReq.setErpItemSkuId("dishId");
        unItemBindUnbindReq.setStoreGuid("storeGuid");

        // Configure MtAuthService.getAuth(...).
        final MtAuthDO mtAuthDO = new MtAuthDO();
        mtAuthDO.setId(0);
        mtAuthDO.setGuid("0353c687-9f24-46f7-b136-99843dd2e5f0");
        mtAuthDO.setEPoiId("storeGuid");
        mtAuthDO.setEnterpriseGuid("enterpriseGuid");
        mtAuthDO.setAccessToken("authToken");
        when(mockAuthService.getAuth("storeGuid", 0)).thenReturn(mtAuthDO);

        // Run the test
        mtItemMappingServiceImplUnderTest.bindMapping(unItemBindUnbindReq);

        // Verify the results
    }

    @Test(expected = BusinessException.class)
    public void testBindMapping_MtAuthServiceGetAuthReturnsNull() {
        // Setup
        final UnItemBindUnbindReq unItemBindUnbindReq = new UnItemBindUnbindReq();
        unItemBindUnbindReq.setUnItemId("dishId");
        unItemBindUnbindReq.setUnItemSkuId("dishId");
        unItemBindUnbindReq.setExtendValue("extendValue");
        unItemBindUnbindReq.setErpItemGuid("dishId");
        unItemBindUnbindReq.setErpItemSkuId("dishId");
        unItemBindUnbindReq.setStoreGuid("storeGuid");

        when(mockAuthService.getAuth("storeGuid", 0)).thenReturn(null);

        // Run the test
        mtItemMappingServiceImplUnderTest.bindMapping(unItemBindUnbindReq);
    }

    @Test
    public void testUnbindMapping() {
        // Setup
        final UnItemBindUnbindReq unItemBindUnbindReq = new UnItemBindUnbindReq();
        unItemBindUnbindReq.setUnItemId("dishId");
        unItemBindUnbindReq.setUnItemSkuId("dishId");
        unItemBindUnbindReq.setExtendValue("extendValue");
        unItemBindUnbindReq.setErpItemGuid("dishId");
        unItemBindUnbindReq.setErpItemSkuId("dishId");
        unItemBindUnbindReq.setStoreGuid("storeGuid");

        // Configure MtAuthService.getAuth(...).
        final MtAuthDO mtAuthDO = new MtAuthDO();
        mtAuthDO.setId(0);
        mtAuthDO.setGuid("0353c687-9f24-46f7-b136-99843dd2e5f0");
        mtAuthDO.setEPoiId("storeGuid");
        mtAuthDO.setEnterpriseGuid("enterpriseGuid");
        mtAuthDO.setAccessToken("authToken");
        when(mockAuthService.getAuth("storeGuid", 0)).thenReturn(mtAuthDO);

        // Run the test
        mtItemMappingServiceImplUnderTest.unbindMapping(unItemBindUnbindReq);

        // Verify the results
    }

    @Test(expected = BusinessException.class)
    public void testUnbindMapping_MtAuthServiceGetAuthReturnsNull() {
        // Setup
        final UnItemBindUnbindReq unItemBindUnbindReq = new UnItemBindUnbindReq();
        unItemBindUnbindReq.setUnItemId("dishId");
        unItemBindUnbindReq.setUnItemSkuId("dishId");
        unItemBindUnbindReq.setExtendValue("extendValue");
        unItemBindUnbindReq.setErpItemGuid("dishId");
        unItemBindUnbindReq.setErpItemSkuId("dishId");
        unItemBindUnbindReq.setStoreGuid("storeGuid");

        when(mockAuthService.getAuth("storeGuid", 0)).thenReturn(null);

        // Run the test
        mtItemMappingServiceImplUnderTest.unbindMapping(unItemBindUnbindReq);
    }

    @Test
    public void testBatchUnbindMapping() {
        // Setup
        final UnItemBatchUnbindReq unItemBatchUnbindReq = new UnItemBatchUnbindReq();
        unItemBatchUnbindReq.setStoreGuid("storeGuid");
        final UnItemBaseMapReq unItemBaseMapReq = new UnItemBaseMapReq();
        unItemBaseMapReq.setUnItemId("dishId");
        unItemBaseMapReq.setUnItemSkuId("dishId");
        unItemBaseMapReq.setExtendValue("extendValue");
        unItemBaseMapReq.setErpItemGuid("dishId");
        unItemBaseMapReq.setErpItemSkuId("dishId");
        unItemBatchUnbindReq.setUnItemUnbindList(Arrays.asList(unItemBaseMapReq));
        unItemBatchUnbindReq.setBindFlag(false);

        // Configure MtAuthService.getAuth(...).
        final MtAuthDO mtAuthDO = new MtAuthDO();
        mtAuthDO.setId(0);
        mtAuthDO.setGuid("0353c687-9f24-46f7-b136-99843dd2e5f0");
        mtAuthDO.setEPoiId("storeGuid");
        mtAuthDO.setEnterpriseGuid("enterpriseGuid");
        mtAuthDO.setAccessToken("authToken");
        when(mockAuthService.getAuth("storeGuid", 0)).thenReturn(mtAuthDO);

        // Run the test
        mtItemMappingServiceImplUnderTest.batchUnbindMapping(unItemBatchUnbindReq);

        // Verify the results
    }

    @Test(expected = BusinessException.class)
    public void testBatchUnbindMapping_MtAuthServiceGetAuthReturnsNull() {
        // Setup
        final UnItemBatchUnbindReq unItemBatchUnbindReq = new UnItemBatchUnbindReq();
        unItemBatchUnbindReq.setStoreGuid("storeGuid");
        final UnItemBaseMapReq unItemBaseMapReq = new UnItemBaseMapReq();
        unItemBaseMapReq.setUnItemId("dishId");
        unItemBaseMapReq.setUnItemSkuId("dishId");
        unItemBaseMapReq.setExtendValue("extendValue");
        unItemBaseMapReq.setErpItemGuid("dishId");
        unItemBaseMapReq.setErpItemSkuId("dishId");
        unItemBatchUnbindReq.setUnItemUnbindList(Arrays.asList(unItemBaseMapReq));
        unItemBatchUnbindReq.setBindFlag(false);

        when(mockAuthService.getAuth("storeGuid", 0)).thenReturn(null);

        // Run the test
        mtItemMappingServiceImplUnderTest.batchUnbindMapping(unItemBatchUnbindReq);
    }
}
