package com.holder.saas.store.takeaway.producers.service.converter;

import com.holder.saas.store.takeaway.producers.entity.dto.MtAuthDTO;
import com.holder.saas.store.takeaway.producers.entity.dto.MtOauthRspDTO;
import com.holderzone.resource.common.dto.enterprise.MultiMemberDTO;
import org.junit.Test;

import java.time.LocalDateTime;

import static org.junit.Assert.assertEquals;

public class MtCallbackConverterTest {

    @Test
    public void testFromMultiMemberAndRsp() {
        // Setup
        final MultiMemberDTO multiMember = new MultiMemberDTO();
        multiMember.setId(0L);
        multiMember.setEnterpriseGuid("enterpriseGuid");
        multiMember.setMultiMemberGuid("ePoiId");
        multiMember.setMultiMemberName("multiMemberName");
        multiMember.setEnabled(false);

        final MtOauthRspDTO mtOauthRsp = new MtOauthRspDTO();
        final MtOauthRspDTO.Data data = new MtOauthRspDTO.Data();
        data.setAccessToken("accessToken");
        data.setExpireIn(0L);
        data.setRefreshToken("refreshToken");
        data.setOpBizCode("mtStoreGuid");
        mtOauthRsp.setData(data);

        final MtAuthDTO expectedResult = new MtAuthDTO();
        expectedResult.setMtStoreGuid("mtStoreGuid");
        expectedResult.setMtStoreName("mtStoreGuid");
        expectedResult.setEPoiId("ePoiId");
        expectedResult.setEnterpriseGuid("enterpriseGuid");
        expectedResult.setAccessToken("accessToken");
        expectedResult.setRefreshToken("refreshToken");
        expectedResult.setBusinessId((byte) 0b0);
        expectedResult.setActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setExpireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRefreshTokenExpire(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setDeleted(false);

        // Run the test
        final MtAuthDTO result = MtCallbackConverter.fromMultiMemberAndRsp(multiMember, mtOauthRsp);

        // Verify the results
        assertEquals(expectedResult, result);
    }
}
