package com.holder.saas.store.takeaway.producers.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holder.saas.store.takeaway.producers.config.GroupBuyDouYinConfig;
import com.holder.saas.store.takeaway.producers.entity.domain.GroupStoreBindDO;
import com.holder.saas.store.takeaway.producers.service.GroupStoreBindService;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.business.group.StoreBindDTO;
import com.holderzone.saas.store.dto.order.response.groupon.GroupVerifyDTO;
import com.holderzone.saas.store.dto.takeaway.request.CouPonPreReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.CouPonReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtCouponPreRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtDelCouponRespDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.*;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class DouYinGroupBuyServiceImplTest {

    @Mock
    private GroupBuyDouYinConfig mockGroupBuyDouYinConfig;
    @Mock
    private StringRedisTemplate mockRedisTemplate;
    @Mock
    private RestTemplate mockGroupBuyRestTemplate;
    @Mock
    private GroupStoreBindService mockGroupStoreBindService;

    private DouYinGroupBuyServiceImpl douYinGroupBuyServiceImplUnderTest;

    @Before
    public void setUp() {
        douYinGroupBuyServiceImplUnderTest = new DouYinGroupBuyServiceImpl(mockGroupBuyDouYinConfig, mockRedisTemplate,
                mockGroupBuyRestTemplate, mockGroupStoreBindService);
    }

    @Test
    public void testBindStore() {
        // Setup
        final StoreBindDTO storeBind = new StoreBindDTO();
        storeBind.setGroupBuyType(0);
        storeBind.setAccountId("accountId");
        storeBind.setPoiId("poiId");
        storeBind.setStoreGuid("storeGuid");

        when(mockGroupStoreBindService.count(any(LambdaQueryWrapper.class))).thenReturn(0);
        when(mockGroupBuyDouYinConfig.isSandbox()).thenReturn(false);
        when(mockRedisTemplate.opsForValue()).thenReturn(null);
        when(mockGroupBuyDouYinConfig.getAppId()).thenReturn("result");
        when(mockGroupBuyDouYinConfig.getAppSecret()).thenReturn("result");
        when(mockGroupBuyRestTemplate.postForEntity("url", new HttpEntity<>("value", new HttpHeaders()),
                String.class)).thenReturn(new ResponseEntity<>("body", HttpStatus.OK));
        when(mockGroupBuyRestTemplate.exchange("url", HttpMethod.GET,
                new HttpEntity<>(new JSONObject(0, false), new HttpHeaders()), String.class))
                .thenReturn(new ResponseEntity<>("body", HttpStatus.OK));

        // Run the test
        douYinGroupBuyServiceImplUnderTest.bindStore(storeBind);

        // Verify the results
        verify(mockGroupStoreBindService).remove(any(LambdaQueryWrapper.class));

        // Confirm GroupStoreBindService.save(...).
        final GroupStoreBindDO entity = new GroupStoreBindDO();
        entity.setStoreGuid("storeGuid");
        entity.setPoiId("poiId");
        entity.setPoiName("poiName");
        entity.setType(0);
        entity.setTaskId("taskId");
        verify(mockGroupStoreBindService).save(entity);
    }

    @Test
    public void testBindStore_GroupBuyDouYinConfigIsSandboxReturnsTrue() {
        // Setup
        final StoreBindDTO storeBind = new StoreBindDTO();
        storeBind.setGroupBuyType(0);
        storeBind.setAccountId("accountId");
        storeBind.setPoiId("poiId");
        storeBind.setStoreGuid("storeGuid");

        when(mockGroupStoreBindService.count(any(LambdaQueryWrapper.class))).thenReturn(0);
        when(mockGroupBuyDouYinConfig.isSandbox()).thenReturn(true);
        when(mockRedisTemplate.opsForValue()).thenReturn(null);
        when(mockGroupBuyDouYinConfig.getAppId()).thenReturn("result");
        when(mockGroupBuyDouYinConfig.getAppSecret()).thenReturn("result");
        when(mockGroupBuyRestTemplate.postForEntity("url", new HttpEntity<>("value", new HttpHeaders()),
                String.class)).thenReturn(new ResponseEntity<>("body", HttpStatus.OK));
        when(mockGroupBuyRestTemplate.exchange("url", HttpMethod.GET,
                new HttpEntity<>(new JSONObject(0, false), new HttpHeaders()), String.class))
                .thenReturn(new ResponseEntity<>("body", HttpStatus.OK));

        // Run the test
        douYinGroupBuyServiceImplUnderTest.bindStore(storeBind);

        // Verify the results
        verify(mockGroupStoreBindService).remove(any(LambdaQueryWrapper.class));

        // Confirm GroupStoreBindService.save(...).
        final GroupStoreBindDO entity = new GroupStoreBindDO();
        entity.setStoreGuid("storeGuid");
        entity.setPoiId("poiId");
        entity.setPoiName("poiName");
        entity.setType(0);
        entity.setTaskId("taskId");
        verify(mockGroupStoreBindService).save(entity);
    }

    @Test(expected = RestClientException.class)
    public void testBindStore_RestTemplatePostForEntityThrowsRestClientException() {
        // Setup
        final StoreBindDTO storeBind = new StoreBindDTO();
        storeBind.setGroupBuyType(0);
        storeBind.setAccountId("accountId");
        storeBind.setPoiId("poiId");
        storeBind.setStoreGuid("storeGuid");

        when(mockGroupStoreBindService.count(any(LambdaQueryWrapper.class))).thenReturn(0);
        when(mockGroupBuyDouYinConfig.isSandbox()).thenReturn(false);
        when(mockRedisTemplate.opsForValue()).thenReturn(null);
        when(mockGroupBuyDouYinConfig.getAppId()).thenReturn("result");
        when(mockGroupBuyDouYinConfig.getAppSecret()).thenReturn("result");
        when(mockGroupBuyRestTemplate.postForEntity("url", new HttpEntity<>("value", new HttpHeaders()),
                String.class)).thenThrow(RestClientException.class);

        // Run the test
        douYinGroupBuyServiceImplUnderTest.bindStore(storeBind);
    }

    @Test(expected = RestClientException.class)
    public void testBindStore_RestTemplateExchangeThrowsRestClientException() {
        // Setup
        final StoreBindDTO storeBind = new StoreBindDTO();
        storeBind.setGroupBuyType(0);
        storeBind.setAccountId("accountId");
        storeBind.setPoiId("poiId");
        storeBind.setStoreGuid("storeGuid");

        when(mockGroupStoreBindService.count(any(LambdaQueryWrapper.class))).thenReturn(0);
        when(mockGroupBuyDouYinConfig.isSandbox()).thenReturn(false);
        when(mockRedisTemplate.opsForValue()).thenReturn(null);
        when(mockGroupBuyDouYinConfig.getAppId()).thenReturn("result");
        when(mockGroupBuyDouYinConfig.getAppSecret()).thenReturn("result");
        when(mockGroupBuyRestTemplate.postForEntity("url", new HttpEntity<>("value", new HttpHeaders()),
                String.class)).thenReturn(new ResponseEntity<>("body", HttpStatus.OK));
        when(mockGroupBuyRestTemplate.exchange("url", HttpMethod.GET,
                new HttpEntity<>(new JSONObject(0, false), new HttpHeaders()), String.class))
                .thenThrow(RestClientException.class);

        // Run the test
        douYinGroupBuyServiceImplUnderTest.bindStore(storeBind);
    }

    @Test
    public void testCouponPrepare() {
        // Setup
        final CouPonPreReqDTO couPonPreReqDTO = new CouPonPreReqDTO();
        couPonPreReqDTO.setStoreGuid("storeGuid");
        couPonPreReqDTO.setCouponCode("encryptedData");
        couPonPreReqDTO.setErpOrderId("erpOrderId");
        couPonPreReqDTO.setGroupBuyType(0);
        couPonPreReqDTO.setEncryptedData("encryptedData");

        final MtCouponPreRespDTO mtCouponPreRespDTO = new MtCouponPreRespDTO();
        mtCouponPreRespDTO.setCount(0);
        mtCouponPreRespDTO.setCouponBuyPrice(0.0);
        mtCouponPreRespDTO.setCouponCode("couponCode");
        mtCouponPreRespDTO.setCouponEndTime("couponEndTime");
        mtCouponPreRespDTO.setDealTitle("dealTitle");
        mtCouponPreRespDTO.setDealValue(0.0);
        mtCouponPreRespDTO.setCouponType(0);
        mtCouponPreRespDTO.setSkuId("skuId");
        final List<MtCouponPreRespDTO> expectedResult = Arrays.asList(mtCouponPreRespDTO);
        when(mockGroupBuyRestTemplate.headForHeaders("encryptedData")).thenReturn(new HttpHeaders());
        when(mockGroupStoreBindService.count(any(LambdaQueryWrapper.class))).thenReturn(0);
        when(mockGroupBuyDouYinConfig.isSandbox()).thenReturn(false);
        when(mockRedisTemplate.opsForValue()).thenReturn(null);
        when(mockGroupBuyDouYinConfig.getAppId()).thenReturn("result");
        when(mockGroupBuyDouYinConfig.getAppSecret()).thenReturn("result");
        when(mockGroupBuyRestTemplate.postForEntity("url", new HttpEntity<>("value", new HttpHeaders()),
                String.class)).thenReturn(new ResponseEntity<>("body", HttpStatus.OK));
        when(mockGroupBuyRestTemplate.exchange("url", HttpMethod.GET,
                new HttpEntity<>(new JSONObject(0, false), new HttpHeaders()), String.class))
                .thenReturn(new ResponseEntity<>("body", HttpStatus.OK));

        // Run the test
        final List<MtCouponPreRespDTO> result = douYinGroupBuyServiceImplUnderTest.couponPrepare(couPonPreReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test(expected = RestClientException.class)
    public void testCouponPrepare_RestTemplateHeadForHeadersThrowsRestClientException() {
        // Setup
        final CouPonPreReqDTO couPonPreReqDTO = new CouPonPreReqDTO();
        couPonPreReqDTO.setStoreGuid("storeGuid");
        couPonPreReqDTO.setCouponCode("encryptedData");
        couPonPreReqDTO.setErpOrderId("erpOrderId");
        couPonPreReqDTO.setGroupBuyType(0);
        couPonPreReqDTO.setEncryptedData("encryptedData");

        when(mockGroupBuyRestTemplate.headForHeaders("encryptedData")).thenThrow(RestClientException.class);

        // Run the test
        douYinGroupBuyServiceImplUnderTest.couponPrepare(couPonPreReqDTO);
    }

    @Test
    public void testCouponPrepare_GroupBuyDouYinConfigIsSandboxReturnsTrue() {
        // Setup
        final CouPonPreReqDTO couPonPreReqDTO = new CouPonPreReqDTO();
        couPonPreReqDTO.setStoreGuid("storeGuid");
        couPonPreReqDTO.setCouponCode("encryptedData");
        couPonPreReqDTO.setErpOrderId("erpOrderId");
        couPonPreReqDTO.setGroupBuyType(0);
        couPonPreReqDTO.setEncryptedData("encryptedData");

        final MtCouponPreRespDTO mtCouponPreRespDTO = new MtCouponPreRespDTO();
        mtCouponPreRespDTO.setCount(0);
        mtCouponPreRespDTO.setCouponBuyPrice(0.0);
        mtCouponPreRespDTO.setCouponCode("couponCode");
        mtCouponPreRespDTO.setCouponEndTime("couponEndTime");
        mtCouponPreRespDTO.setDealTitle("dealTitle");
        mtCouponPreRespDTO.setDealValue(0.0);
        mtCouponPreRespDTO.setCouponType(0);
        mtCouponPreRespDTO.setSkuId("skuId");
        final List<MtCouponPreRespDTO> expectedResult = Arrays.asList(mtCouponPreRespDTO);
        when(mockGroupBuyRestTemplate.headForHeaders("encryptedData")).thenReturn(new HttpHeaders());
        when(mockGroupStoreBindService.count(any(LambdaQueryWrapper.class))).thenReturn(0);
        when(mockGroupBuyDouYinConfig.isSandbox()).thenReturn(true);
        when(mockGroupBuyRestTemplate.exchange("url", HttpMethod.GET,
                new HttpEntity<>(new JSONObject(0, false), new HttpHeaders()), String.class))
                .thenReturn(new ResponseEntity<>("body", HttpStatus.OK));

        // Run the test
        final List<MtCouponPreRespDTO> result = douYinGroupBuyServiceImplUnderTest.couponPrepare(couPonPreReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test(expected = RestClientException.class)
    public void testCouponPrepare_RestTemplatePostForEntityThrowsRestClientException() {
        // Setup
        final CouPonPreReqDTO couPonPreReqDTO = new CouPonPreReqDTO();
        couPonPreReqDTO.setStoreGuid("storeGuid");
        couPonPreReqDTO.setCouponCode("encryptedData");
        couPonPreReqDTO.setErpOrderId("erpOrderId");
        couPonPreReqDTO.setGroupBuyType(0);
        couPonPreReqDTO.setEncryptedData("encryptedData");

        when(mockGroupBuyRestTemplate.headForHeaders("encryptedData")).thenReturn(new HttpHeaders());
        when(mockGroupStoreBindService.count(any(LambdaQueryWrapper.class))).thenReturn(0);
        when(mockGroupBuyDouYinConfig.isSandbox()).thenReturn(false);
        when(mockRedisTemplate.opsForValue()).thenReturn(null);
        when(mockGroupBuyDouYinConfig.getAppId()).thenReturn("result");
        when(mockGroupBuyDouYinConfig.getAppSecret()).thenReturn("result");
        when(mockGroupBuyRestTemplate.postForEntity("url", new HttpEntity<>("value", new HttpHeaders()),
                String.class)).thenThrow(RestClientException.class);

        // Run the test
        douYinGroupBuyServiceImplUnderTest.couponPrepare(couPonPreReqDTO);
    }

    @Test(expected = RestClientException.class)
    public void testCouponPrepare_RestTemplateExchangeThrowsRestClientException() {
        // Setup
        final CouPonPreReqDTO couPonPreReqDTO = new CouPonPreReqDTO();
        couPonPreReqDTO.setStoreGuid("storeGuid");
        couPonPreReqDTO.setCouponCode("encryptedData");
        couPonPreReqDTO.setErpOrderId("erpOrderId");
        couPonPreReqDTO.setGroupBuyType(0);
        couPonPreReqDTO.setEncryptedData("encryptedData");

        when(mockGroupBuyRestTemplate.headForHeaders("encryptedData")).thenReturn(new HttpHeaders());
        when(mockGroupStoreBindService.count(any(LambdaQueryWrapper.class))).thenReturn(0);
        when(mockGroupBuyDouYinConfig.isSandbox()).thenReturn(false);
        when(mockRedisTemplate.opsForValue()).thenReturn(null);
        when(mockGroupBuyDouYinConfig.getAppId()).thenReturn("result");
        when(mockGroupBuyDouYinConfig.getAppSecret()).thenReturn("result");
        when(mockGroupBuyRestTemplate.postForEntity("url", new HttpEntity<>("value", new HttpHeaders()),
                String.class)).thenReturn(new ResponseEntity<>("body", HttpStatus.OK));
        when(mockGroupBuyRestTemplate.exchange("url", HttpMethod.GET,
                new HttpEntity<>(new JSONObject(0, false), new HttpHeaders()), String.class))
                .thenThrow(RestClientException.class);

        // Run the test
        douYinGroupBuyServiceImplUnderTest.couponPrepare(couPonPreReqDTO);
    }

    @Test
    public void testVerifyCoupon() {
        // Setup
        final CouPonReqDTO couPonReq = new CouPonReqDTO();
        couPonReq.setStoreGuid("storeGuid");
        couPonReq.setErpId("erpId");
        couPonReq.setErpOrderId("erpOrderId");
        couPonReq.setCouponCodeList(Arrays.asList("value"));
        couPonReq.setGroupBuyType(0);

        final GroupVerifyDTO groupVerifyDTO = new GroupVerifyDTO();
        groupVerifyDTO.setGroupBuyType(0);
        groupVerifyDTO.setCode("code");
        groupVerifyDTO.setVerifyId("verifyId");
        groupVerifyDTO.setCertificateId("certificateId");
        groupVerifyDTO.setErpId("erpId");
        final List<GroupVerifyDTO> expectedResult = Arrays.asList(groupVerifyDTO);

        // Configure GroupStoreBindService.getOne(...).
        final GroupStoreBindDO groupStoreBindDO = new GroupStoreBindDO();
        groupStoreBindDO.setStoreGuid("storeGuid");
        groupStoreBindDO.setPoiId("poiId");
        groupStoreBindDO.setPoiName("poiName");
        groupStoreBindDO.setType(0);
        groupStoreBindDO.setTaskId("taskId");
        when(mockGroupStoreBindService.getOne(any(LambdaQueryWrapper.class))).thenReturn(groupStoreBindDO);

        when(mockGroupBuyDouYinConfig.isSandbox()).thenReturn(false);
        when(mockRedisTemplate.opsForValue()).thenReturn(null);
        when(mockGroupBuyDouYinConfig.getAppId()).thenReturn("result");
        when(mockGroupBuyDouYinConfig.getAppSecret()).thenReturn("result");
        when(mockGroupBuyRestTemplate.postForEntity("url", new HttpEntity<>("value", new HttpHeaders()),
                String.class)).thenReturn(new ResponseEntity<>("body", HttpStatus.OK));

        // Run the test
        final List<GroupVerifyDTO> result = douYinGroupBuyServiceImplUnderTest.verifyCoupon(couPonReq);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test(expected = BusinessException.class)
    public void testVerifyCoupon_GroupStoreBindServiceReturnsNull() {
        // Setup
        final CouPonReqDTO couPonReq = new CouPonReqDTO();
        couPonReq.setStoreGuid("storeGuid");
        couPonReq.setErpId("erpId");
        couPonReq.setErpOrderId("erpOrderId");
        couPonReq.setCouponCodeList(Arrays.asList("value"));
        couPonReq.setGroupBuyType(0);

        when(mockGroupStoreBindService.getOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Run the test
        douYinGroupBuyServiceImplUnderTest.verifyCoupon(couPonReq);
    }

    @Test
    public void testVerifyCoupon_GroupBuyDouYinConfigIsSandboxReturnsTrue() {
        // Setup
        final CouPonReqDTO couPonReq = new CouPonReqDTO();
        couPonReq.setStoreGuid("storeGuid");
        couPonReq.setErpId("erpId");
        couPonReq.setErpOrderId("erpOrderId");
        couPonReq.setCouponCodeList(Arrays.asList("value"));
        couPonReq.setGroupBuyType(0);

        final GroupVerifyDTO groupVerifyDTO = new GroupVerifyDTO();
        groupVerifyDTO.setGroupBuyType(0);
        groupVerifyDTO.setCode("code");
        groupVerifyDTO.setVerifyId("verifyId");
        groupVerifyDTO.setCertificateId("certificateId");
        groupVerifyDTO.setErpId("erpId");
        final List<GroupVerifyDTO> expectedResult = Arrays.asList(groupVerifyDTO);

        // Configure GroupStoreBindService.getOne(...).
        final GroupStoreBindDO groupStoreBindDO = new GroupStoreBindDO();
        groupStoreBindDO.setStoreGuid("storeGuid");
        groupStoreBindDO.setPoiId("poiId");
        groupStoreBindDO.setPoiName("poiName");
        groupStoreBindDO.setType(0);
        groupStoreBindDO.setTaskId("taskId");
        when(mockGroupStoreBindService.getOne(any(LambdaQueryWrapper.class))).thenReturn(groupStoreBindDO);

        when(mockGroupBuyDouYinConfig.isSandbox()).thenReturn(true);
        when(mockGroupBuyRestTemplate.postForEntity("url", new HttpEntity<>("value", new HttpHeaders()),
                String.class)).thenReturn(new ResponseEntity<>("body", HttpStatus.OK));

        // Run the test
        final List<GroupVerifyDTO> result = douYinGroupBuyServiceImplUnderTest.verifyCoupon(couPonReq);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test(expected = RestClientException.class)
    public void testVerifyCoupon_RestTemplateThrowsRestClientException() {
        // Setup
        final CouPonReqDTO couPonReq = new CouPonReqDTO();
        couPonReq.setStoreGuid("storeGuid");
        couPonReq.setErpId("erpId");
        couPonReq.setErpOrderId("erpOrderId");
        couPonReq.setCouponCodeList(Arrays.asList("value"));
        couPonReq.setGroupBuyType(0);

        // Configure GroupStoreBindService.getOne(...).
        final GroupStoreBindDO groupStoreBindDO = new GroupStoreBindDO();
        groupStoreBindDO.setStoreGuid("storeGuid");
        groupStoreBindDO.setPoiId("poiId");
        groupStoreBindDO.setPoiName("poiName");
        groupStoreBindDO.setType(0);
        groupStoreBindDO.setTaskId("taskId");
        when(mockGroupStoreBindService.getOne(any(LambdaQueryWrapper.class))).thenReturn(groupStoreBindDO);

        when(mockGroupBuyDouYinConfig.isSandbox()).thenReturn(false);
        when(mockRedisTemplate.opsForValue()).thenReturn(null);
        when(mockGroupBuyDouYinConfig.getAppId()).thenReturn("result");
        when(mockGroupBuyDouYinConfig.getAppSecret()).thenReturn("result");
        when(mockGroupBuyRestTemplate.postForEntity("url", new HttpEntity<>("value", new HttpHeaders()),
                String.class)).thenThrow(RestClientException.class);

        // Run the test
        douYinGroupBuyServiceImplUnderTest.verifyCoupon(couPonReq);
    }

    @Test
    public void testRevokeCoupon() {
        // Setup
        final GroupVerifyDTO revokeReq = new GroupVerifyDTO();
        revokeReq.setGroupBuyType(0);
        revokeReq.setCode("code");
        revokeReq.setVerifyId("verifyId");
        revokeReq.setCertificateId("certificateId");
        revokeReq.setErpId("erpId");

        final MtDelCouponRespDTO expectedResult = new MtDelCouponRespDTO(0, "message");
        when(mockGroupBuyDouYinConfig.isSandbox()).thenReturn(false);
        when(mockRedisTemplate.opsForValue()).thenReturn(null);
        when(mockGroupBuyDouYinConfig.getAppId()).thenReturn("result");
        when(mockGroupBuyDouYinConfig.getAppSecret()).thenReturn("result");
        when(mockGroupBuyRestTemplate.postForEntity("url", new HttpEntity<>("value", new HttpHeaders()),
                String.class)).thenReturn(new ResponseEntity<>("body", HttpStatus.OK));

        // Run the test
        final MtDelCouponRespDTO result = douYinGroupBuyServiceImplUnderTest.revokeCoupon(revokeReq);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testRevokeCoupon_GroupBuyDouYinConfigIsSandboxReturnsTrue() {
        // Setup
        final GroupVerifyDTO revokeReq = new GroupVerifyDTO();
        revokeReq.setGroupBuyType(0);
        revokeReq.setCode("code");
        revokeReq.setVerifyId("verifyId");
        revokeReq.setCertificateId("certificateId");
        revokeReq.setErpId("erpId");

        final MtDelCouponRespDTO expectedResult = new MtDelCouponRespDTO(0, "message");
        when(mockGroupBuyDouYinConfig.isSandbox()).thenReturn(true);
        when(mockGroupBuyRestTemplate.postForEntity("url", new HttpEntity<>("value", new HttpHeaders()),
                String.class)).thenReturn(new ResponseEntity<>("body", HttpStatus.OK));

        // Run the test
        final MtDelCouponRespDTO result = douYinGroupBuyServiceImplUnderTest.revokeCoupon(revokeReq);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test(expected = RestClientException.class)
    public void testRevokeCoupon_RestTemplateThrowsRestClientException() {
        // Setup
        final GroupVerifyDTO revokeReq = new GroupVerifyDTO();
        revokeReq.setGroupBuyType(0);
        revokeReq.setCode("code");
        revokeReq.setVerifyId("verifyId");
        revokeReq.setCertificateId("certificateId");
        revokeReq.setErpId("erpId");

        when(mockGroupBuyDouYinConfig.isSandbox()).thenReturn(false);
        when(mockRedisTemplate.opsForValue()).thenReturn(null);
        when(mockGroupBuyDouYinConfig.getAppId()).thenReturn("result");
        when(mockGroupBuyDouYinConfig.getAppSecret()).thenReturn("result");
        when(mockGroupBuyRestTemplate.postForEntity("url", new HttpEntity<>("value", new HttpHeaders()),
                String.class)).thenThrow(RestClientException.class);

        // Run the test
        douYinGroupBuyServiceImplUnderTest.revokeCoupon(revokeReq);
    }

    @Test
    public void testQueryTask() {
        // Setup
        when(mockGroupBuyDouYinConfig.isSandbox()).thenReturn(false);
        when(mockRedisTemplate.opsForValue()).thenReturn(null);
        when(mockGroupBuyDouYinConfig.getAppId()).thenReturn("result");
        when(mockGroupBuyDouYinConfig.getAppSecret()).thenReturn("result");
        when(mockGroupBuyRestTemplate.postForEntity("url", new HttpEntity<>("value", new HttpHeaders()),
                String.class)).thenReturn(new ResponseEntity<>("body", HttpStatus.OK));
        when(mockGroupBuyRestTemplate.exchange("url", HttpMethod.GET,
                new HttpEntity<>(new JSONObject(0, false), new HttpHeaders()), String.class))
                .thenReturn(new ResponseEntity<>("body", HttpStatus.OK));

        // Run the test
        douYinGroupBuyServiceImplUnderTest.queryTask("taskId");

        // Verify the results
    }

    @Test
    public void testQueryTask_GroupBuyDouYinConfigIsSandboxReturnsTrue() {
        // Setup
        when(mockGroupBuyDouYinConfig.isSandbox()).thenReturn(true);
        when(mockGroupBuyRestTemplate.exchange("url", HttpMethod.GET,
                new HttpEntity<>(new JSONObject(0, false), new HttpHeaders()), String.class))
                .thenReturn(new ResponseEntity<>("body", HttpStatus.OK));

        // Run the test
        douYinGroupBuyServiceImplUnderTest.queryTask("taskId");

        // Verify the results
    }

    @Test(expected = RestClientException.class)
    public void testQueryTask_RestTemplatePostForEntityThrowsRestClientException() {
        // Setup
        when(mockGroupBuyDouYinConfig.isSandbox()).thenReturn(false);
        when(mockRedisTemplate.opsForValue()).thenReturn(null);
        when(mockGroupBuyDouYinConfig.getAppId()).thenReturn("result");
        when(mockGroupBuyDouYinConfig.getAppSecret()).thenReturn("result");
        when(mockGroupBuyRestTemplate.postForEntity("url", new HttpEntity<>("value", new HttpHeaders()),
                String.class)).thenThrow(RestClientException.class);

        // Run the test
        douYinGroupBuyServiceImplUnderTest.queryTask("taskId");
    }

    @Test(expected = RestClientException.class)
    public void testQueryTask_RestTemplateExchangeThrowsRestClientException() {
        // Setup
        when(mockGroupBuyDouYinConfig.isSandbox()).thenReturn(false);
        when(mockRedisTemplate.opsForValue()).thenReturn(null);
        when(mockGroupBuyDouYinConfig.getAppId()).thenReturn("result");
        when(mockGroupBuyDouYinConfig.getAppSecret()).thenReturn("result");
        when(mockGroupBuyRestTemplate.postForEntity("url", new HttpEntity<>("value", new HttpHeaders()),
                String.class)).thenReturn(new ResponseEntity<>("body", HttpStatus.OK));
        when(mockGroupBuyRestTemplate.exchange("url", HttpMethod.GET,
                new HttpEntity<>(new JSONObject(0, false), new HttpHeaders()), String.class))
                .thenThrow(RestClientException.class);

        // Run the test
        douYinGroupBuyServiceImplUnderTest.queryTask("taskId");
    }

    @Test
    public void testGetToken() {
        // Setup
        when(mockGroupBuyDouYinConfig.isSandbox()).thenReturn(false);
        when(mockRedisTemplate.opsForValue()).thenReturn(null);
        when(mockGroupBuyDouYinConfig.getAppId()).thenReturn("result");
        when(mockGroupBuyDouYinConfig.getAppSecret()).thenReturn("result");
        when(mockGroupBuyRestTemplate.postForEntity("url", new HttpEntity<>("value", new HttpHeaders()),
                String.class)).thenReturn(new ResponseEntity<>("body", HttpStatus.OK));

        // Run the test
        final String result = douYinGroupBuyServiceImplUnderTest.getToken();

        // Verify the results
        assertEquals("accessToken", result);
    }

    @Test
    public void testGetToken_GroupBuyDouYinConfigIsSandboxReturnsTrue() {
        // Setup
        when(mockGroupBuyDouYinConfig.isSandbox()).thenReturn(true);

        // Run the test
        final String result = douYinGroupBuyServiceImplUnderTest.getToken();

        // Verify the results
        assertEquals("accessToken", result);
    }

    @Test(expected = RestClientException.class)
    public void testGetToken_RestTemplateThrowsRestClientException() {
        // Setup
        when(mockGroupBuyDouYinConfig.isSandbox()).thenReturn(false);
        when(mockRedisTemplate.opsForValue()).thenReturn(null);
        when(mockGroupBuyDouYinConfig.getAppId()).thenReturn("result");
        when(mockGroupBuyDouYinConfig.getAppSecret()).thenReturn("result");
        when(mockGroupBuyRestTemplate.postForEntity("url", new HttpEntity<>("value", new HttpHeaders()),
                String.class)).thenThrow(RestClientException.class);

        // Run the test
        douYinGroupBuyServiceImplUnderTest.getToken();
    }
}
