package com.holder.saas.store.takeaway.producers.service;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class UnItemMappingServiceFactoryTest {

    @Mock
    private UnItemMappingService mockMtItemMappingService;
    @Mock
    private UnItemMappingService mockEleItemMappingService;
    @Mock
    private UnItemMappingService mockOwnItemMappingService;
    @Mock
    private UnItemMappingService mockZcItemMappingService;

    @Mock
    private UnItemMappingService jdItemMappingServiceImpl;

    private UnItemMappingServiceFactory unItemMappingServiceFactoryUnderTest;

    @Before
    public void setUp() throws Exception {
        unItemMappingServiceFactoryUnderTest = new UnItemMappingServiceFactory(mockMtItemMappingService,
                mockEleItemMappingService, mockOwnItemMappingService, mockZcItemMappingService,jdItemMappingServiceImpl);
    }

    @Test
    public void testCreate() {
        // Setup
        // Run the test
        final UnItemMappingService result = unItemMappingServiceFactoryUnderTest.create(0);

        // Verify the results
    }
}
