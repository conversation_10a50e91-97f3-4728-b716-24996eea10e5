package com.holder.saas.store.takeaway.producers.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holder.saas.store.takeaway.producers.entity.domain.AlipayAuthDO;
import com.holder.saas.store.takeaway.producers.mapper.AlipayAuthMapper;
import com.holder.saas.store.takeaway.producers.service.DistributedService;
import com.holder.saas.store.takeaway.producers.service.rpc.CloudEnterpriseService;
import com.holderzone.resource.common.dto.enterprise.MultiMemberDTO;
import com.holderzone.saas.store.dto.takeaway.request.AlipayAuthQO;
import com.holderzone.saas.store.dto.takeaway.request.NotifyAliPayAuthReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.AlipayAuthRespDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class AlipayAuthServiceImplTest {

    @Mock
    private AlipayAuthMapper mockAlipayAuthMapper;
    @Mock
    private DistributedService mockDistributedService;
    @Mock
    private CloudEnterpriseService mockCloudService;

    private AlipayAuthServiceImpl alipayAuthServiceImplUnderTest;

    @Before
    public void setUp() {
        alipayAuthServiceImplUnderTest = new AlipayAuthServiceImpl(mockAlipayAuthMapper, mockDistributedService,
                mockCloudService);
    }

    @Test
    public void testQueryAuthInfo() {
        // Setup
        final AlipayAuthQO authQO = new AlipayAuthQO();
        authQO.setEnterpriseGuid("enterpriseGuid");
        authQO.setStoreGuid("storeGuid");

        final AlipayAuthRespDTO expectedResult = new AlipayAuthRespDTO();
        expectedResult.setEnterpriseGuid("enterpriseGuid");
        expectedResult.setOperSubjectGuid("operSubjectGuid");
        expectedResult.setAppId("appId");
        expectedResult.setAppAuthToken("appAuthToken");
        expectedResult.setApplyPublicKey("applyPublicKey");
        expectedResult.setApplyPrivateKey("applyPrivateKey");
        expectedResult.setAliPublicKey("aliPublicKey");
        expectedResult.setAes("aes");

        // Configure CloudEnterpriseService.findMemberInfoByOrganizationGuid(...).
        final MultiMemberDTO multiMemberDTO = new MultiMemberDTO();
        multiMemberDTO.setId(0L);
        multiMemberDTO.setEnterpriseGuid("enterpriseGuid");
        multiMemberDTO.setMultiMemberGuid("multiMemberGuid");
        multiMemberDTO.setMultiMemberName("multiMemberName");
        multiMemberDTO.setEnabled(false);
        when(mockCloudService.findMemberInfoByOrganizationGuid("storeGuid")).thenReturn(multiMemberDTO);

        // Configure AlipayAuthMapper.selectOne(...).
        final AlipayAuthDO alipayAuthDO = new AlipayAuthDO();
        alipayAuthDO.setGuid("8b9c4d3a-6dff-4cb9-b610-bedefe988484");
        alipayAuthDO.setIsDeleted(false);
        alipayAuthDO.setEnterpriseGuid("enterpriseGuid");
        alipayAuthDO.setOperSubjectGuid("operSubjectGuid");
        alipayAuthDO.setAppAuthToken("appAuthToken");
        alipayAuthDO.setAppId("appId");
        alipayAuthDO.setApplyPublicKey("applyPublicKey");
        alipayAuthDO.setApplyPrivateKey("applyPrivateKey");
        alipayAuthDO.setAliPublicKey("aliPublicKey");
        alipayAuthDO.setAes("aes");
        when(mockAlipayAuthMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(alipayAuthDO);

        // Run the test
        final AlipayAuthRespDTO result = alipayAuthServiceImplUnderTest.queryAuthInfo(authQO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testNotifyAliPayAuth() {
        // Setup
        final NotifyAliPayAuthReqDTO notifyDTO = new NotifyAliPayAuthReqDTO();
        notifyDTO.setEnterpriseGuid("enterpriseGuid");
        notifyDTO.setOperSubjectGuid("operSubjectGuid");
        notifyDTO.setAppId("appId");
        notifyDTO.setAppAuthToken("appAuthToken");
        notifyDTO.setNotifyType(0);

        when(mockDistributedService.nextAliPayGuid()).thenReturn("8b9c4d3a-6dff-4cb9-b610-bedefe988484");

        // Run the test
        alipayAuthServiceImplUnderTest.notifyAliPayAuth(notifyDTO);

        // Verify the results
        // Confirm AlipayAuthMapper.insert(...).
        final AlipayAuthDO entity = new AlipayAuthDO();
        entity.setGuid("8b9c4d3a-6dff-4cb9-b610-bedefe988484");
        entity.setIsDeleted(false);
        entity.setEnterpriseGuid("enterpriseGuid");
        entity.setOperSubjectGuid("operSubjectGuid");
        entity.setAppAuthToken("appAuthToken");
        entity.setAppId("appId");
        entity.setApplyPublicKey("applyPublicKey");
        entity.setApplyPrivateKey("applyPrivateKey");
        entity.setAliPublicKey("aliPublicKey");
        entity.setAes("aes");
        verify(mockAlipayAuthMapper).insert(entity);

        // Confirm AlipayAuthMapper.updateById(...).
        final AlipayAuthDO entity1 = new AlipayAuthDO();
        entity1.setGuid("8b9c4d3a-6dff-4cb9-b610-bedefe988484");
        entity1.setIsDeleted(false);
        entity1.setEnterpriseGuid("enterpriseGuid");
        entity1.setOperSubjectGuid("operSubjectGuid");
        entity1.setAppAuthToken("appAuthToken");
        entity1.setAppId("appId");
        entity1.setApplyPublicKey("applyPublicKey");
        entity1.setApplyPrivateKey("applyPrivateKey");
        entity1.setAliPublicKey("aliPublicKey");
        entity1.setAes("aes");
        verify(mockAlipayAuthMapper).updateById(entity1);
    }
}
