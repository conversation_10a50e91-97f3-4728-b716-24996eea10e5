package com.holder.saas.store.takeaway.producers.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.util.concurrent.MoreExecutors;
import com.holder.saas.store.takeaway.producers.entity.domain.EleAuthDO;
import com.holder.saas.store.takeaway.producers.service.EleAuthService;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.takeaway.*;
import eleme.openapi.sdk.api.entity.product.OCategory;
import eleme.openapi.sdk.api.entity.product.OItem;
import eleme.openapi.sdk.api.entity.product.QueryPage;
import eleme.openapi.sdk.api.exception.ServiceException;
import eleme.openapi.sdk.api.service.ProductService;
import eleme.openapi.sdk.config.Config;
import eleme.openapi.sdk.oauth.response.Token;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.class)
public class EleItemMappingServiceImplTest {

    @Mock
    private EleAuthService mockEleAuthService;
    @Mock
    private Config mockConfig;

    private EleItemMappingServiceImpl eleItemMappingServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        eleItemMappingServiceImplUnderTest = new EleItemMappingServiceImpl(mockEleAuthService, mockConfig);
        ReflectionTestUtils.setField(eleItemMappingServiceImplUnderTest, "eleProductQueryThreadPool",
                MoreExecutors.newDirectExecutorService());
        ReflectionTestUtils.setField(eleItemMappingServiceImplUnderTest, "batchBindProductThreadPool",
                MoreExecutors.newDirectExecutorService());
    }

    @Test
    public void testElemeItemMapping() {
        Config config = new Config(false, "CV6sxR4uxo", "0247107a498ffbe988f32a75619093cd10698cc1");
        Token token = new Token();
        token.setAccessToken("ec00069d35f41c90ddce62547c1f8b60");
        token.setRefreshToken("e45de96eebfce84d20f9c341fe378a20");
        token.setExpires(2592000L);
        token.setTokenType("Bearer");
        ProductService productService = new ProductService(config, token);
        try {

            QueryPage queryPage = new QueryPage();
            queryPage.setShopId(658604L);
            queryPage.setOffset(0L);
            queryPage.setLimit(300L);
            List<OItem> shopItem = productService.queryItemByPage(queryPage);
            log.info(JacksonUtils.writeValueAsString(shopItem));
        } catch (ServiceException e) {
            throw new RuntimeException(e);
        }
        try {
            List<OCategory> shopCategories = productService.getShopCategories(658604L);
            log.info(JacksonUtils.writeValueAsString(shopCategories));
        } catch (ServiceException e) {
        }
    }

    @Test
    public void testGetType() {
        // Setup
        final UnMappedType unMappedType = new UnMappedType();
        unMappedType.setUnItemTypeId("unItemTypeId");
        unMappedType.setUnItemTypeName("name");
        final UnMappedItem unMappedItem = new UnMappedItem();
        unMappedItem.setUnItemId("unItemId");
        unMappedItem.setUnItemName("name");
        unMappedType.setUnItemList(Arrays.asList(unMappedItem));
        final List<UnMappedType> expectedResult = Arrays.asList(unMappedType);

        // Configure EleAuthService.getToken(...).
        final Token token = new Token();
        token.setAccessToken("accessToken");
        token.setTokenType("tokenType");
        token.setExpires(0L);
        token.setRefreshToken("refreshToken");
        when(mockEleAuthService.getToken("storeGuid")).thenReturn(token);

        // Configure EleAuthService.getOne(...).
        final EleAuthDO eleAuthDO = new EleAuthDO();
        eleAuthDO.setId(0L);
        eleAuthDO.setGuid("6eef1bff-a8b5-41b8-9696-7c0de131ca59");
        eleAuthDO.setEnterpriseGuid("enterpriseGuid");
        eleAuthDO.setStoreGuid("storeGuid");
        eleAuthDO.setShopId(0L);
        when(mockEleAuthService.getOne(any(LambdaQueryWrapper.class))).thenReturn(eleAuthDO);

        // Run the test
        final List<UnMappedType> result = eleItemMappingServiceImplUnderTest.getType("storeGuid");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test(expected = BusinessException.class)
    public void testGetType_EleAuthServiceGetTokenReturnsNull() {
        // Setup
        when(mockEleAuthService.getToken("storeGuid")).thenReturn(null);

        // Run the test
        eleItemMappingServiceImplUnderTest.getType("storeGuid");
    }

    @Test
    public void testGetItem() {
        // Setup
        final UnMappedItem unMappedItem = new UnMappedItem();
        unMappedItem.setUnItemId("unItemId");
        unMappedItem.setUnItemName("name");
        unMappedItem.setUnItemSkuId("unItemSkuId");
        unMappedItem.setUnItemSkuName("name");
        unMappedItem.setUnItemNameWithSku("name");
        unMappedItem.setUnItemSkuUnit("unit");
        unMappedItem.setUnItemTypeId("unItemTypeId");
        unMappedItem.setUnItemTypeName("name");
        unMappedItem.setErpItemSkuId("");
        unMappedItem.setErpStoreGuid("storeGuid");
        unMappedItem.setExtendValue("extendValue");
        final List<UnMappedItem> expectedResult = Arrays.asList(unMappedItem);

        // Configure EleAuthService.getToken(...).
        final Token token = new Token();
        token.setAccessToken("accessToken");
        token.setTokenType("tokenType");
        token.setExpires(0L);
        token.setRefreshToken("refreshToken");
        when(mockEleAuthService.getToken("storeGuid")).thenReturn(token);

        // Configure EleAuthService.getOne(...).
        final EleAuthDO eleAuthDO = new EleAuthDO();
        eleAuthDO.setId(0L);
        eleAuthDO.setGuid("6eef1bff-a8b5-41b8-9696-7c0de131ca59");
        eleAuthDO.setEnterpriseGuid("enterpriseGuid");
        eleAuthDO.setStoreGuid("storeGuid");
        eleAuthDO.setShopId(0L);
        when(mockEleAuthService.getOne(any(LambdaQueryWrapper.class))).thenReturn(eleAuthDO);

        // Run the test
        final List<UnMappedItem> result = eleItemMappingServiceImplUnderTest.getItem("storeGuid");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test(expected = BusinessException.class)
    public void testGetItem_EleAuthServiceGetTokenReturnsNull() {
        // Setup
        when(mockEleAuthService.getToken("storeGuid")).thenReturn(null);

        // Run the test
        eleItemMappingServiceImplUnderTest.getItem("storeGuid");
    }

    @Test
    public void testGetItems() {
        // Setup
        final UnItemQueryReq unItemQueryReq = new UnItemQueryReq();
        unItemQueryReq.setKeywords("keywords");
        unItemQueryReq.setStoreGuids(Arrays.asList("value"));
        unItemQueryReq.setTakeoutType(0);
        unItemQueryReq.setBindingFlag(0);

        final UnMappedItem unMappedItem = new UnMappedItem();
        unMappedItem.setUnItemId("unItemId");
        unMappedItem.setUnItemName("name");
        unMappedItem.setUnItemSkuId("unItemSkuId");
        unMappedItem.setUnItemSkuName("name");
        unMappedItem.setUnItemNameWithSku("name");
        unMappedItem.setUnItemSkuUnit("unit");
        unMappedItem.setUnItemTypeId("unItemTypeId");
        unMappedItem.setUnItemTypeName("name");
        unMappedItem.setErpItemSkuId("");
        unMappedItem.setErpStoreGuid("storeGuid");
        unMappedItem.setExtendValue("extendValue");
        final List<UnMappedItem> expectedResult = Arrays.asList(unMappedItem);
        when(mockEleAuthService.getTokens(Arrays.asList("value"))).thenReturn(new HashMap<>());

        // Run the test
        final List<UnMappedItem> result = eleItemMappingServiceImplUnderTest.getItems(unItemQueryReq);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testBindMapping() {
        // Setup
        final UnItemBindUnbindReq unItemBindUnbindReq = new UnItemBindUnbindReq();
        unItemBindUnbindReq.setUnItemId("unItemId");
        unItemBindUnbindReq.setUnItemSkuId("unItemSkuId");
        unItemBindUnbindReq.setUnItemTypeId("unItemTypeId");
        unItemBindUnbindReq.setExtendValue("extendValue");
        unItemBindUnbindReq.setErpItemGuid("erpItemGuid");
        unItemBindUnbindReq.setErpItemSkuId("");
        unItemBindUnbindReq.setStoreGuid("storeGuid");

        // Configure EleAuthService.getToken(...).
        final Token token = new Token();
        token.setAccessToken("accessToken");
        token.setTokenType("tokenType");
        token.setExpires(0L);
        token.setRefreshToken("refreshToken");
        when(mockEleAuthService.getToken("storeGuid")).thenReturn(token);

        // Run the test
        eleItemMappingServiceImplUnderTest.bindMapping(unItemBindUnbindReq);

        // Verify the results
    }

    @Test(expected = BusinessException.class)
    public void testBindMapping_EleAuthServiceReturnsNull() {
        // Setup
        final UnItemBindUnbindReq unItemBindUnbindReq = new UnItemBindUnbindReq();
        unItemBindUnbindReq.setUnItemId("unItemId");
        unItemBindUnbindReq.setUnItemSkuId("unItemSkuId");
        unItemBindUnbindReq.setUnItemTypeId("unItemTypeId");
        unItemBindUnbindReq.setExtendValue("extendValue");
        unItemBindUnbindReq.setErpItemGuid("erpItemGuid");
        unItemBindUnbindReq.setErpItemSkuId("");
        unItemBindUnbindReq.setStoreGuid("storeGuid");

        when(mockEleAuthService.getToken("storeGuid")).thenReturn(null);

        // Run the test
        eleItemMappingServiceImplUnderTest.bindMapping(unItemBindUnbindReq);
    }

    @Test
    public void testUnbindMapping() {
        // Setup
        final UnItemBindUnbindReq unItemBindUnbindReq = new UnItemBindUnbindReq();
        unItemBindUnbindReq.setUnItemId("unItemId");
        unItemBindUnbindReq.setUnItemSkuId("unItemSkuId");
        unItemBindUnbindReq.setUnItemTypeId("unItemTypeId");
        unItemBindUnbindReq.setExtendValue("extendValue");
        unItemBindUnbindReq.setErpItemGuid("erpItemGuid");
        unItemBindUnbindReq.setErpItemSkuId("");
        unItemBindUnbindReq.setStoreGuid("storeGuid");

        // Configure EleAuthService.getToken(...).
        final Token token = new Token();
        token.setAccessToken("accessToken");
        token.setTokenType("tokenType");
        token.setExpires(0L);
        token.setRefreshToken("refreshToken");
        when(mockEleAuthService.getToken("storeGuid")).thenReturn(token);

        // Run the test
        eleItemMappingServiceImplUnderTest.unbindMapping(unItemBindUnbindReq);

        // Verify the results
    }

    @Test(expected = BusinessException.class)
    public void testUnbindMapping_EleAuthServiceReturnsNull() {
        // Setup
        final UnItemBindUnbindReq unItemBindUnbindReq = new UnItemBindUnbindReq();
        unItemBindUnbindReq.setUnItemId("unItemId");
        unItemBindUnbindReq.setUnItemSkuId("unItemSkuId");
        unItemBindUnbindReq.setUnItemTypeId("unItemTypeId");
        unItemBindUnbindReq.setExtendValue("extendValue");
        unItemBindUnbindReq.setErpItemGuid("erpItemGuid");
        unItemBindUnbindReq.setErpItemSkuId("");
        unItemBindUnbindReq.setStoreGuid("storeGuid");

        when(mockEleAuthService.getToken("storeGuid")).thenReturn(null);

        // Run the test
        eleItemMappingServiceImplUnderTest.unbindMapping(unItemBindUnbindReq);
    }

    @Test
    public void testBatchUnbindMapping() {
        // Setup
        final UnItemBatchUnbindReq unItemBatchUnbindReq = new UnItemBatchUnbindReq();
        unItemBatchUnbindReq.setStoreGuid("storeGuid");
        final UnItemBaseMapReq unItemBaseMapReq = new UnItemBaseMapReq();
        unItemBaseMapReq.setUnItemId("unItemId");
        unItemBaseMapReq.setUnItemSkuId("unItemSkuId");
        unItemBaseMapReq.setUnItemTypeId("unItemTypeId");
        unItemBaseMapReq.setExtendValue("extendValue");
        unItemBaseMapReq.setErpItemGuid("erpItemGuid");
        unItemBaseMapReq.setErpItemSkuId("");
        unItemBatchUnbindReq.setUnItemUnbindList(Arrays.asList(unItemBaseMapReq));
        unItemBatchUnbindReq.setBindFlag(false);

        // Configure EleAuthService.getToken(...).
        final Token token = new Token();
        token.setAccessToken("accessToken");
        token.setTokenType("tokenType");
        token.setExpires(0L);
        token.setRefreshToken("refreshToken");
        when(mockEleAuthService.getToken("storeGuid")).thenReturn(token);

        // Run the test
        eleItemMappingServiceImplUnderTest.batchUnbindMapping(unItemBatchUnbindReq);

        // Verify the results
    }

    @Test(expected = BusinessException.class)
    public void testBatchUnbindMapping_EleAuthServiceReturnsNull() {
        // Setup
        final UnItemBatchUnbindReq unItemBatchUnbindReq = new UnItemBatchUnbindReq();
        unItemBatchUnbindReq.setStoreGuid("storeGuid");
        final UnItemBaseMapReq unItemBaseMapReq = new UnItemBaseMapReq();
        unItemBaseMapReq.setUnItemId("unItemId");
        unItemBaseMapReq.setUnItemSkuId("unItemSkuId");
        unItemBaseMapReq.setUnItemTypeId("unItemTypeId");
        unItemBaseMapReq.setExtendValue("extendValue");
        unItemBaseMapReq.setErpItemGuid("erpItemGuid");
        unItemBaseMapReq.setErpItemSkuId("");
        unItemBatchUnbindReq.setUnItemUnbindList(Arrays.asList(unItemBaseMapReq));
        unItemBatchUnbindReq.setBindFlag(false);

        when(mockEleAuthService.getToken("storeGuid")).thenReturn(null);

        // Run the test
        eleItemMappingServiceImplUnderTest.batchUnbindMapping(unItemBatchUnbindReq);
    }
}
