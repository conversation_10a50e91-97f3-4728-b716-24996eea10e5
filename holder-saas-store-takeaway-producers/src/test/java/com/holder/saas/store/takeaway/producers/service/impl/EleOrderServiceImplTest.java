package com.holder.saas.store.takeaway.producers.service.impl;

import com.holder.saas.store.takeaway.producers.service.EleAuthService;
import com.holderzone.framework.exception.unchecked.BusinessException;
import eleme.openapi.sdk.api.entity.order.OOrder;
import eleme.openapi.sdk.config.Config;
import eleme.openapi.sdk.oauth.response.Token;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class EleOrderServiceImplTest {

    @Mock
    private EleAuthService mockEleAuthService;
    @Mock
    private Config mockConfig;

    @InjectMocks
    private EleOrderServiceImpl eleOrderServiceImplUnderTest;

    @Test
    public void testGetOrder() {
        // Setup
        // Configure EleAuthService.getToken(...).
        final Token token = new Token();
        token.setAccessToken("accessToken");
        token.setTokenType("tokenType");
        token.setExpires(0L);
        token.setRefreshToken("refreshToken");
        when(mockEleAuthService.getToken("storeGuid")).thenReturn(token);

        // Run the test
        final OOrder result = eleOrderServiceImplUnderTest.getOrder("storeGuid", "orderId");

        // Verify the results
    }

    @Test(expected = BusinessException.class)
    public void testGetOrder_EleAuthServiceReturnsNull() {
        // Setup
        when(mockEleAuthService.getToken("storeGuid")).thenReturn(null);

        // Run the test
        eleOrderServiceImplUnderTest.getOrder("storeGuid", "orderId");
    }
}
