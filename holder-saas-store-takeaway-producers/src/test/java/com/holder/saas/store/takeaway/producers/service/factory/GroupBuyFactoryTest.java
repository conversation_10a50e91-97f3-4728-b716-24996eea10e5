package com.holder.saas.store.takeaway.producers.service.factory;

import com.holder.saas.store.takeaway.producers.service.GroupBuyService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class GroupBuyFactoryTest {

    @Mock
    private GroupBuyService mockDouYinGroupBuyServiceImpl;
    @Mock
    private GroupBuyService mockDaZhongGroupBuyServiceImpl;
    @Mock
    private GroupBuyService mockAliPayGroupBuyServiceImpl;
    @Mock
    private GroupBuyService mockAbcGroupBuyServiceImpl;
    @Mock
    private GroupBuyService mockMeiTuanGroupBuyServiceImpl;

    private GroupBuyFactory groupBuyFactoryUnderTest;

    @Before
    public void setUp() throws Exception {
        groupBuyFactoryUnderTest = new GroupBuyFactory(mockDouYinGroupBuyServiceImpl, mockDaZhongGroupBuyServiceImpl,
                mockAliPayGroupBuyServiceImpl, mockAbcGroupBuyServiceImpl, mockMeiTuanGroupBuyServiceImpl);
    }

    @Test
    public void testBuild() {
        // Setup
        // Run the test
        final GroupBuyService result = groupBuyFactoryUnderTest.build(0);

        // Verify the results
    }
}
