package com.holder.saas.store.takeaway.producers.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holder.saas.store.takeaway.producers.entity.domain.GroupStoreBindDO;
import com.holder.saas.store.takeaway.producers.service.AlipayAuthService;
import com.holder.saas.store.takeaway.producers.service.DistributedService;
import com.holder.saas.store.takeaway.producers.service.GroupStoreBindService;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.business.group.StoreBindDTO;
import com.holderzone.saas.store.dto.order.response.groupon.GroupVerifyDTO;
import com.holderzone.saas.store.dto.takeaway.request.AlipayAuthQO;
import com.holderzone.saas.store.dto.takeaway.request.CouPonPreReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.CouPonReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.AlipayAuthRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtCouponPreRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtDelCouponRespDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class AliPayGroupBuyServiceImplTest {

    @Mock
    private GroupStoreBindService mockGroupStoreBindService;
    @Mock
    private AlipayAuthService mockAlipayAuthService;
    @Mock
    private StringRedisTemplate mockStringRedisTemplate;
    @Mock
    private DistributedService mockDistributedService;

    private AliPayGroupBuyServiceImpl aliPayGroupBuyServiceImplUnderTest;

    @Before
    public void setUp() {
        aliPayGroupBuyServiceImplUnderTest = new AliPayGroupBuyServiceImpl(mockGroupStoreBindService,
                mockAlipayAuthService, mockStringRedisTemplate, mockDistributedService);
    }

    @Test
    public void testBindStore() {
        // Setup
        final StoreBindDTO storeBind = new StoreBindDTO();
        storeBind.setGroupBuyType(0);
        storeBind.setAccountId("accountId");
        storeBind.setPoiId("poiId");
        storeBind.setStoreGuid("storeGuid");

        when(mockGroupStoreBindService.count(any(LambdaQueryWrapper.class))).thenReturn(0);
        when(mockStringRedisTemplate.opsForValue()).thenReturn(null);

        // Configure AlipayAuthService.queryAuthInfo(...).
        final AlipayAuthRespDTO alipayAuthRespDTO = new AlipayAuthRespDTO();
        alipayAuthRespDTO.setAppId("appId");
        alipayAuthRespDTO.setAppAuthToken("appAuthToken");
        alipayAuthRespDTO.setApplyPrivateKey("applyPrivateKey");
        alipayAuthRespDTO.setAliPublicKey("alipayPublicKey");
        alipayAuthRespDTO.setAes("aes");
        final AlipayAuthQO authQO = new AlipayAuthQO();
        authQO.setEnterpriseGuid("enterpriseGuid");
        authQO.setStoreGuid("storeGuid");
        when(mockAlipayAuthService.queryAuthInfo(authQO)).thenReturn(alipayAuthRespDTO);

        // Run the test
        aliPayGroupBuyServiceImplUnderTest.bindStore(storeBind);

        // Verify the results
        verify(mockGroupStoreBindService).remove(any(LambdaQueryWrapper.class));

        // Confirm GroupStoreBindService.save(...).
        final GroupStoreBindDO entity = new GroupStoreBindDO();
        entity.setId(0L);
        entity.setStoreGuid("storeGuid");
        entity.setPoiId("poiId");
        entity.setPoiName("poiName");
        entity.setType(0);
        verify(mockGroupStoreBindService).save(entity);
    }

    @Test
    public void testGetToken() {
        // Setup
        when(mockStringRedisTemplate.opsForValue()).thenReturn(null);

        // Configure AlipayAuthService.queryAuthInfo(...).
        final AlipayAuthRespDTO alipayAuthRespDTO = new AlipayAuthRespDTO();
        alipayAuthRespDTO.setAppId("appId");
        alipayAuthRespDTO.setAppAuthToken("appAuthToken");
        alipayAuthRespDTO.setApplyPrivateKey("applyPrivateKey");
        alipayAuthRespDTO.setAliPublicKey("alipayPublicKey");
        alipayAuthRespDTO.setAes("aes");
        final AlipayAuthQO authQO = new AlipayAuthQO();
        authQO.setEnterpriseGuid("enterpriseGuid");
        authQO.setStoreGuid("storeGuid");
        when(mockAlipayAuthService.queryAuthInfo(authQO)).thenReturn(alipayAuthRespDTO);

        // Run the test
        final String result = aliPayGroupBuyServiceImplUnderTest.getToken();

        // Verify the results
        assertEquals("appAuthToken", result);
    }

    @Test
    public void testGetAppId() {
        // Setup
        when(mockStringRedisTemplate.opsForValue()).thenReturn(null);

        // Configure AlipayAuthService.queryAuthInfo(...).
        final AlipayAuthRespDTO alipayAuthRespDTO = new AlipayAuthRespDTO();
        alipayAuthRespDTO.setAppId("appId");
        alipayAuthRespDTO.setAppAuthToken("appAuthToken");
        alipayAuthRespDTO.setApplyPrivateKey("applyPrivateKey");
        alipayAuthRespDTO.setAliPublicKey("alipayPublicKey");
        alipayAuthRespDTO.setAes("aes");
        final AlipayAuthQO authQO = new AlipayAuthQO();
        authQO.setEnterpriseGuid("enterpriseGuid");
        authQO.setStoreGuid("storeGuid");
        when(mockAlipayAuthService.queryAuthInfo(authQO)).thenReturn(alipayAuthRespDTO);

        // Run the test
        final String result = aliPayGroupBuyServiceImplUnderTest.getAppId();

        // Verify the results
        assertEquals("appId", result);
    }

    @Test
    public void testCouponPrepare() {
        // Setup
        final CouPonPreReqDTO couPonPreReqDTO = new CouPonPreReqDTO();
        couPonPreReqDTO.setStoreGuid("storeGuid");
        couPonPreReqDTO.setCouponCode("code");
        couPonPreReqDTO.setGroupBuyType(0);
        couPonPreReqDTO.setEncryptedData("encryptedData");
        couPonPreReqDTO.setCrossOrder(false);

        final MtCouponPreRespDTO mtCouponPreRespDTO = new MtCouponPreRespDTO();
        mtCouponPreRespDTO.setCount(0);
        mtCouponPreRespDTO.setCouponBuyPrice(0.0);
        mtCouponPreRespDTO.setCouponCode("couponCode");
        mtCouponPreRespDTO.setIsVoucher(false);
        mtCouponPreRespDTO.setCouponEndTime("couponEndTime");
        mtCouponPreRespDTO.setDealBeginTime("dealBeginTime");
        mtCouponPreRespDTO.setDealTitle("dealTitle");
        mtCouponPreRespDTO.setDealValue(0.0);
        mtCouponPreRespDTO.setCouponType(0);
        mtCouponPreRespDTO.setItemId("itemId");
        mtCouponPreRespDTO.setSkuId("skuId");
        mtCouponPreRespDTO.setUserId("userId");
        mtCouponPreRespDTO.setOrderId("orderId");
        final List<MtCouponPreRespDTO> expectedResult = Arrays.asList(mtCouponPreRespDTO);
        when(mockGroupStoreBindService.count(any(LambdaQueryWrapper.class))).thenReturn(0);
        when(mockStringRedisTemplate.opsForValue()).thenReturn(null);

        // Configure AlipayAuthService.queryAuthInfo(...).
        final AlipayAuthRespDTO alipayAuthRespDTO = new AlipayAuthRespDTO();
        alipayAuthRespDTO.setAppId("appId");
        alipayAuthRespDTO.setAppAuthToken("appAuthToken");
        alipayAuthRespDTO.setApplyPrivateKey("applyPrivateKey");
        alipayAuthRespDTO.setAliPublicKey("alipayPublicKey");
        alipayAuthRespDTO.setAes("aes");
        final AlipayAuthQO authQO = new AlipayAuthQO();
        authQO.setEnterpriseGuid("enterpriseGuid");
        authQO.setStoreGuid("storeGuid");
        when(mockAlipayAuthService.queryAuthInfo(authQO)).thenReturn(alipayAuthRespDTO);

        // Run the test
        final List<MtCouponPreRespDTO> result = aliPayGroupBuyServiceImplUnderTest.couponPrepare(couPonPreReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testVerifyCoupon() {
        // Setup
        final CouPonReqDTO couPonReq = new CouPonReqDTO();
        couPonReq.setStoreGuid("storeGuid");
        couPonReq.setErpId("erpId");
        couPonReq.setErpOrderId("erpOrderId");
        couPonReq.setCouponCodeList(Arrays.asList("value"));
        couPonReq.setGroupBuyType(0);
        couPonReq.setUserId("userId");
        couPonReq.setOrderId("orderId");
        couPonReq.setShopId("poiId");

        final GroupVerifyDTO groupVerifyDTO = new GroupVerifyDTO();
        groupVerifyDTO.setGroupBuyType(0);
        groupVerifyDTO.setCode("certificateId");
        groupVerifyDTO.setVerifyId("useOrderNo");
        groupVerifyDTO.setCertificateId("certificateId");
        groupVerifyDTO.setUserId("userId");
        final List<GroupVerifyDTO> expectedResult = Arrays.asList(groupVerifyDTO);

        // Configure GroupStoreBindService.getOne(...).
        final GroupStoreBindDO groupStoreBindDO = new GroupStoreBindDO();
        groupStoreBindDO.setId(0L);
        groupStoreBindDO.setStoreGuid("storeGuid");
        groupStoreBindDO.setPoiId("poiId");
        groupStoreBindDO.setPoiName("poiName");
        groupStoreBindDO.setType(0);
        when(mockGroupStoreBindService.getOne(any(LambdaQueryWrapper.class))).thenReturn(groupStoreBindDO);

        when(mockDistributedService.nextId("OutBizNo")).thenReturn("outBizNo");
        when(mockStringRedisTemplate.opsForValue()).thenReturn(null);

        // Configure AlipayAuthService.queryAuthInfo(...).
        final AlipayAuthRespDTO alipayAuthRespDTO = new AlipayAuthRespDTO();
        alipayAuthRespDTO.setAppId("appId");
        alipayAuthRespDTO.setAppAuthToken("appAuthToken");
        alipayAuthRespDTO.setApplyPrivateKey("applyPrivateKey");
        alipayAuthRespDTO.setAliPublicKey("alipayPublicKey");
        alipayAuthRespDTO.setAes("aes");
        final AlipayAuthQO authQO = new AlipayAuthQO();
        authQO.setEnterpriseGuid("enterpriseGuid");
        authQO.setStoreGuid("storeGuid");
        when(mockAlipayAuthService.queryAuthInfo(authQO)).thenReturn(alipayAuthRespDTO);

        // Run the test
        final List<GroupVerifyDTO> result = aliPayGroupBuyServiceImplUnderTest.verifyCoupon(couPonReq);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test(expected = BusinessException.class)
    public void testVerifyCoupon_GroupStoreBindServiceReturnsNull() {
        // Setup
        final CouPonReqDTO couPonReq = new CouPonReqDTO();
        couPonReq.setStoreGuid("storeGuid");
        couPonReq.setErpId("erpId");
        couPonReq.setErpOrderId("erpOrderId");
        couPonReq.setCouponCodeList(Arrays.asList("value"));
        couPonReq.setGroupBuyType(0);
        couPonReq.setUserId("userId");
        couPonReq.setOrderId("orderId");
        couPonReq.setShopId("poiId");

        when(mockGroupStoreBindService.getOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Run the test
        aliPayGroupBuyServiceImplUnderTest.verifyCoupon(couPonReq);
    }

    @Test
    public void testRevokeCoupon() {
        // Setup
        final GroupVerifyDTO revokeReq = new GroupVerifyDTO();
        revokeReq.setGroupBuyType(0);
        revokeReq.setCode("certificateId");
        revokeReq.setVerifyId("useOrderNo");
        revokeReq.setCertificateId("certificateId");
        revokeReq.setUserId("userId");

        final MtDelCouponRespDTO expectedResult = new MtDelCouponRespDTO(0, "message");
        when(mockDistributedService.nextId("OutBizNo")).thenReturn("outBizNo");
        when(mockStringRedisTemplate.opsForValue()).thenReturn(null);

        // Configure AlipayAuthService.queryAuthInfo(...).
        final AlipayAuthRespDTO alipayAuthRespDTO = new AlipayAuthRespDTO();
        alipayAuthRespDTO.setAppId("appId");
        alipayAuthRespDTO.setAppAuthToken("appAuthToken");
        alipayAuthRespDTO.setApplyPrivateKey("applyPrivateKey");
        alipayAuthRespDTO.setAliPublicKey("alipayPublicKey");
        alipayAuthRespDTO.setAes("aes");
        final AlipayAuthQO authQO = new AlipayAuthQO();
        authQO.setEnterpriseGuid("enterpriseGuid");
        authQO.setStoreGuid("storeGuid");
        when(mockAlipayAuthService.queryAuthInfo(authQO)).thenReturn(alipayAuthRespDTO);

        // Run the test
        final MtDelCouponRespDTO result = aliPayGroupBuyServiceImplUnderTest.revokeCoupon(revokeReq);

        // Verify the results
        assertEquals(expectedResult, result);
    }
}
