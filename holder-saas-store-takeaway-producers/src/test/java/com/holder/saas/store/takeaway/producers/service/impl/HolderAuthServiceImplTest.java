package com.holder.saas.store.takeaway.producers.service.impl;

import com.holder.saas.store.takeaway.producers.entity.domain.HolderAuthDO;
import com.holder.saas.store.takeaway.producers.service.DistributedService;
import com.holder.saas.store.takeaway.producers.service.UnOrderMqService;
import com.holder.saas.store.takeaway.producers.service.rpc.ConsumersFeignService;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.takeaway.OwnCallbackResponse;
import com.holderzone.saas.store.dto.takeaway.TakeoutOrderDTO;
import com.holderzone.saas.store.dto.takeaway.request.SalesUpdateDTO;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutShopOwnBindReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutShopOwnItemBindReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutShopOwnUnBindReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.*;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class HolderAuthServiceImplTest {

    @Mock
    private DistributedService mockDistributedService;
    @Mock
    private ConsumersFeignService mockConsumersFeignService;
    @Mock
    private UnOrderMqService mockUnOrderMqService;

    private HolderAuthServiceImpl holderAuthServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        holderAuthServiceImplUnderTest = new HolderAuthServiceImpl(mockDistributedService, mockConsumersFeignService,
                mockUnOrderMqService);
        ReflectionTestUtils.setField(holderAuthServiceImplUnderTest, "url", "url");
        ReflectionTestUtils.setField(holderAuthServiceImplUnderTest, "itemUrl", "itemUrl");
        ReflectionTestUtils.setField(holderAuthServiceImplUnderTest, "itemQuery", "itemQuery");
        ReflectionTestUtils.setField(holderAuthServiceImplUnderTest, "itemBinding", "itemBinding");
        ReflectionTestUtils.setField(holderAuthServiceImplUnderTest, "itemUnBinding", "itemUnBinding");
        ReflectionTestUtils.setField(holderAuthServiceImplUnderTest, "distributionQuery", "distributionQuery");
        ReflectionTestUtils.setField(holderAuthServiceImplUnderTest, "orderUpdate", "orderUpdate");
        ReflectionTestUtils.setField(holderAuthServiceImplUnderTest, "bindingUrl", "bindingUrl");
        ReflectionTestUtils.setField(holderAuthServiceImplUnderTest, "unBindingUrl", "unBindingUrl");
        ReflectionTestUtils.setField(holderAuthServiceImplUnderTest, "refreshUrl", "refreshUrl");
        ReflectionTestUtils.setField(holderAuthServiceImplUnderTest, "appId", "appId");
        ReflectionTestUtils.setField(holderAuthServiceImplUnderTest, "appSecret", "appSecret");
        ReflectionTestUtils.setField(holderAuthServiceImplUnderTest, "tokenRefreshAheadHours", 0);
        ReflectionTestUtils.setField(holderAuthServiceImplUnderTest, "refreshTokenValidityPeriodDays", 0);
    }

    @Test
    public void testDoTakeOutBind() {
        // Setup
        final TakeoutShopOwnBindReqDTO takeoutShopOwnBindReqDTO = new TakeoutShopOwnBindReqDTO();
        takeoutShopOwnBindReqDTO.setEnterpriseGuid("enterpriseGuid");
        takeoutShopOwnBindReqDTO.setStoreGuid("storeGuid");
        takeoutShopOwnBindReqDTO.setUserGuid("userGuid");
        takeoutShopOwnBindReqDTO.setUserName("userName");
        takeoutShopOwnBindReqDTO.setTel("tel");
        takeoutShopOwnBindReqDTO.setStoreCode("storeCode");
        takeoutShopOwnBindReqDTO.setPassWord("passWord");

        final TakeoutOwnRespDTO expectedResult = new TakeoutOwnRespDTO();
        expectedResult.setCode("code");
        final TakeoutOwnBindRespDTO data = new TakeoutOwnBindRespDTO();
        data.setToken("token");
        data.setEnterpriseCode("-1");
        data.setEnterpriseName("EnterpriseName");
        expectedResult.setData(data);

        when(mockDistributedService.nextOwnGuid()).thenReturn("b82dd416-ec25-4fd2-8c89-a1e477061a17");

        // Run the test
        final TakeoutOwnRespDTO result = holderAuthServiceImplUnderTest.doTakeOutBind(takeoutShopOwnBindReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testDoTakeOutUnBind() {
        // Setup
        final TakeoutShopOwnUnBindReqDTO takeoutShopOwnUnBindReqDTO = new TakeoutShopOwnUnBindReqDTO();
        takeoutShopOwnUnBindReqDTO.setEnterpriseGuid("enterpriseGuid");
        takeoutShopOwnUnBindReqDTO.setStoreGuid("storeGuid");
        takeoutShopOwnUnBindReqDTO.setUserGuid("userGuid");
        takeoutShopOwnUnBindReqDTO.setUserName("userName");
        takeoutShopOwnUnBindReqDTO.setTel("tel");
        takeoutShopOwnUnBindReqDTO.setStoreCode("storeCode");
        takeoutShopOwnUnBindReqDTO.setPassWord("passWord");

        final TakeoutOwnRespDTO expectedResult = new TakeoutOwnRespDTO();
        expectedResult.setCode("code");
        final TakeoutOwnBindRespDTO data = new TakeoutOwnBindRespDTO();
        data.setToken("token");
        data.setEnterpriseCode("-1");
        data.setEnterpriseName("EnterpriseName");
        expectedResult.setData(data);

        // Run the test
        final TakeoutOwnRespDTO result = holderAuthServiceImplUnderTest.doTakeOutUnBind(takeoutShopOwnUnBindReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetItem() {
        // Setup
        final TakeoutOwnItemMappingRespDTO takeoutOwnItemMappingRespDTO = new TakeoutOwnItemMappingRespDTO();
        takeoutOwnItemMappingRespDTO.setId(0L);
        takeoutOwnItemMappingRespDTO.setName("name");
        takeoutOwnItemMappingRespDTO.setPrice(new BigDecimal("0.00"));
        takeoutOwnItemMappingRespDTO.setGoodsId(0L);
        takeoutOwnItemMappingRespDTO.setUnit("unit");
        final List<TakeoutOwnItemMappingRespDTO> expectedResult = Arrays.asList(takeoutOwnItemMappingRespDTO);

        // Run the test
        final List<TakeoutOwnItemMappingRespDTO> result = holderAuthServiceImplUnderTest.getItem("storeGuid");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testItemBind() {
        // Setup
        final TakeoutShopOwnItemBindReqDTO takeoutShopOwnItemBindReqDTO = new TakeoutShopOwnItemBindReqDTO();
        takeoutShopOwnItemBindReqDTO.setEnterpriseGuid("enterpriseGuid");
        takeoutShopOwnItemBindReqDTO.setStoreGuid("storeGuid");
        takeoutShopOwnItemBindReqDTO.setUserGuid("userGuid");
        takeoutShopOwnItemBindReqDTO.setUserName("userName");
        takeoutShopOwnItemBindReqDTO.setStoreCode("code");

        // Run the test
        final String result = holderAuthServiceImplUnderTest.itemBind(takeoutShopOwnItemBindReqDTO, "code", "token");

        // Verify the results
        assertEquals("FAILURE", result);
    }

    @Test
    public void testGetHolder() {
        // Setup
        final HolderAuthDO expectedResult = new HolderAuthDO();
        expectedResult.setId(0L);
        expectedResult.setGuid("b82dd416-ec25-4fd2-8c89-a1e477061a17");
        expectedResult.setEnterpriseGuid("enterpriseGuid");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setUserId(0L);
        expectedResult.setUserName("userName");
        expectedResult.setCode("-1");
        expectedResult.setShopName("EnterpriseName");
        expectedResult.setAccessToken("accessToken");
        expectedResult.setRefreshToken("refreshToken");
        expectedResult.setActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setExpires(0L);
        expectedResult.setExpireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRefreshActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRefreshExpires(0L);
        expectedResult.setRefreshExpireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setDeleted(false);

        // Run the test
        final HolderAuthDO result = holderAuthServiceImplUnderTest.getHolder("storeGuid");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testItemUnBind() {
        // Setup
        final TakeoutShopOwnItemBindReqDTO takeoutShopOwnItemBindReqDTO = new TakeoutShopOwnItemBindReqDTO();
        takeoutShopOwnItemBindReqDTO.setEnterpriseGuid("enterpriseGuid");
        takeoutShopOwnItemBindReqDTO.setStoreGuid("storeGuid");
        takeoutShopOwnItemBindReqDTO.setUserGuid("userGuid");
        takeoutShopOwnItemBindReqDTO.setUserName("userName");
        takeoutShopOwnItemBindReqDTO.setStoreCode("code");

        // Run the test
        final String result = holderAuthServiceImplUnderTest.itemUnBind(takeoutShopOwnItemBindReqDTO, "code", "token");

        // Verify the results
        assertEquals("FAILURE", result);
    }

    @Test
    public void testGetCode() {
        assertEquals("-1", holderAuthServiceImplUnderTest.getCode("storeGuid"));
    }

    @Test
    public void testGetDistribution() {
        // Setup
        final BaseDTO baseDTO = new BaseDTO();
        baseDTO.setDeviceType(0);
        baseDTO.setEnterpriseGuid("enterpriseGuid");
        baseDTO.setStoreGuid("storeGuid");
        baseDTO.setUserGuid("userGuid");
        baseDTO.setUserName("userName");

        final OwnDistributionDTO ownDistributionDTO = new OwnDistributionDTO();
        ownDistributionDTO.setType(0);
        ownDistributionDTO.setTypeName("TypeName");
        final List<OwnDistributionDTO> expectedResult = Arrays.asList(ownDistributionDTO);

        // Run the test
        final List<OwnDistributionDTO> result = holderAuthServiceImplUnderTest.getDistribution(baseDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGoShipping() {
        // Setup
        final TakeoutOrderDTO takeoutOrderDTO = new TakeoutOrderDTO();
        takeoutOrderDTO.setUserGuid("userGuid");
        takeoutOrderDTO.setUserName("userName");
        takeoutOrderDTO.setStoreGuid("storeGuid");
        takeoutOrderDTO.setOrderId("orderId");
        takeoutOrderDTO.setDistributionType(0);

        final OwnCallbackResponse expectedResult = new OwnCallbackResponse(0, "message");

        // Configure ConsumersFeignService.orderUpdate(...).
        final SalesUpdateDTO salesUpdateDTO = new SalesUpdateDTO();
        salesUpdateDTO.setOrderID(0L);
        salesUpdateDTO.setOrderStatus(0);
        salesUpdateDTO.setDistributionType(0);
        salesUpdateDTO.setStoreGuid("storeGuid");
        when(mockConsumersFeignService.orderUpdate(salesUpdateDTO)).thenReturn(new OwnCallbackResponse(0, "message"));

        // Run the test
        final OwnCallbackResponse result = holderAuthServiceImplUnderTest.goShipping(takeoutOrderDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testDoneShipping() {
        // Setup
        final TakeoutOrderDTO takeoutOrderDTO = new TakeoutOrderDTO();
        takeoutOrderDTO.setUserGuid("userGuid");
        takeoutOrderDTO.setUserName("userName");
        takeoutOrderDTO.setStoreGuid("storeGuid");
        takeoutOrderDTO.setOrderId("orderId");
        takeoutOrderDTO.setDistributionType(0);

        final OwnCallbackResponse expectedResult = new OwnCallbackResponse(0, "message");

        // Configure ConsumersFeignService.orderUpdate(...).
        final SalesUpdateDTO salesUpdateDTO = new SalesUpdateDTO();
        salesUpdateDTO.setOrderID(0L);
        salesUpdateDTO.setOrderStatus(0);
        salesUpdateDTO.setDistributionType(0);
        salesUpdateDTO.setStoreGuid("storeGuid");
        when(mockConsumersFeignService.orderUpdate(salesUpdateDTO)).thenReturn(new OwnCallbackResponse(0, "message"));

        // Run the test
        final OwnCallbackResponse result = holderAuthServiceImplUnderTest.doneShipping(takeoutOrderDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testCancelShipping() {
        // Setup
        final TakeoutOrderDTO takeoutOrderDTO = new TakeoutOrderDTO();
        takeoutOrderDTO.setUserGuid("userGuid");
        takeoutOrderDTO.setUserName("userName");
        takeoutOrderDTO.setStoreGuid("storeGuid");
        takeoutOrderDTO.setOrderId("orderId");
        takeoutOrderDTO.setDistributionType(0);

        final OwnCallbackResponse expectedResult = new OwnCallbackResponse(0, "message");

        // Configure ConsumersFeignService.orderUpdate(...).
        final SalesUpdateDTO salesUpdateDTO = new SalesUpdateDTO();
        salesUpdateDTO.setOrderID(0L);
        salesUpdateDTO.setOrderStatus(0);
        salesUpdateDTO.setDistributionType(0);
        salesUpdateDTO.setStoreGuid("storeGuid");
        when(mockConsumersFeignService.orderUpdate(salesUpdateDTO)).thenReturn(new OwnCallbackResponse(0, "message"));

        // Run the test
        final OwnCallbackResponse result = holderAuthServiceImplUnderTest.cancelShipping(takeoutOrderDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testRefreshToken1() {
        // Setup
        when(mockDistributedService.nextEleGuid()).thenReturn("b82dd416-ec25-4fd2-8c89-a1e477061a17");

        // Run the test
        holderAuthServiceImplUnderTest.refreshToken(0L);

        // Verify the results
    }

    @Test
    public void testRefreshToken2() {
        // Setup
        final HolderAuthDO holderAuthDO = new HolderAuthDO();
        holderAuthDO.setId(0L);
        holderAuthDO.setGuid("b82dd416-ec25-4fd2-8c89-a1e477061a17");
        holderAuthDO.setEnterpriseGuid("enterpriseGuid");
        holderAuthDO.setStoreGuid("storeGuid");
        holderAuthDO.setUserId(0L);
        holderAuthDO.setUserName("userName");
        holderAuthDO.setCode("-1");
        holderAuthDO.setShopName("EnterpriseName");
        holderAuthDO.setAccessToken("accessToken");
        holderAuthDO.setRefreshToken("refreshToken");
        holderAuthDO.setActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        holderAuthDO.setExpires(0L);
        holderAuthDO.setExpireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        holderAuthDO.setRefreshActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        holderAuthDO.setRefreshExpires(0L);
        holderAuthDO.setRefreshExpireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        holderAuthDO.setDeleted(false);

        when(mockDistributedService.nextEleGuid()).thenReturn("b82dd416-ec25-4fd2-8c89-a1e477061a17");

        // Run the test
        final String result = holderAuthServiceImplUnderTest.refreshToken(holderAuthDO);

        // Verify the results
        assertEquals("-1", result);
    }

    @Test
    public void testGetToken() {
        // Setup
        final HolderAuthDO holderAuthDO = new HolderAuthDO();
        holderAuthDO.setId(0L);
        holderAuthDO.setGuid("b82dd416-ec25-4fd2-8c89-a1e477061a17");
        holderAuthDO.setEnterpriseGuid("enterpriseGuid");
        holderAuthDO.setStoreGuid("storeGuid");
        holderAuthDO.setUserId(0L);
        holderAuthDO.setUserName("userName");
        holderAuthDO.setCode("-1");
        holderAuthDO.setShopName("EnterpriseName");
        holderAuthDO.setAccessToken("accessToken");
        holderAuthDO.setRefreshToken("refreshToken");
        holderAuthDO.setActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        holderAuthDO.setExpires(0L);
        holderAuthDO.setExpireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        holderAuthDO.setRefreshActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        holderAuthDO.setRefreshExpires(0L);
        holderAuthDO.setRefreshExpireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        holderAuthDO.setDeleted(false);

        // Run the test
        final String result = holderAuthServiceImplUnderTest.getToken(holderAuthDO);

        // Verify the results
        assertEquals("accessToken", result);
    }

    @Test
    public void testGetTakeoutAuth() {
        // Setup
        final StoreAuthDTO storeAuthDTO = new StoreAuthDTO();
        storeAuthDTO.setTakeoutType(0);
        storeAuthDTO.setPlatformName("platformName");
        storeAuthDTO.setStoreGuid("storeGuid");
        storeAuthDTO.setShopName("EnterpriseName");
        storeAuthDTO.setBindingStatus(0);

        final StoreAuthDTO expectedResult = new StoreAuthDTO();
        expectedResult.setTakeoutType(0);
        expectedResult.setPlatformName("platformName");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setShopName("EnterpriseName");
        expectedResult.setBindingStatus(0);

        // Run the test
        final StoreAuthDTO result = holderAuthServiceImplUnderTest.getTakeoutAuth(storeAuthDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetHolderAuth() {
        // Setup
        final HolderAuthDO expectedResult = new HolderAuthDO();
        expectedResult.setId(0L);
        expectedResult.setGuid("b82dd416-ec25-4fd2-8c89-a1e477061a17");
        expectedResult.setEnterpriseGuid("enterpriseGuid");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setUserId(0L);
        expectedResult.setUserName("userName");
        expectedResult.setCode("-1");
        expectedResult.setShopName("EnterpriseName");
        expectedResult.setAccessToken("accessToken");
        expectedResult.setRefreshToken("refreshToken");
        expectedResult.setActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setExpires(0L);
        expectedResult.setExpireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRefreshActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRefreshExpires(0L);
        expectedResult.setRefreshExpireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setDeleted(false);

        // Run the test
        final HolderAuthDO result = holderAuthServiceImplUnderTest.getHolderAuth("storeGuid");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testDeleteAuth() {
        // Setup
        // Run the test
        holderAuthServiceImplUnderTest.deleteAuth("storeGuid");

        // Verify the results
    }
}
