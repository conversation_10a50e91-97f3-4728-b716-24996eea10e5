package com.holder.saas.store.takeaway.producers.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holder.saas.store.takeaway.producers.config.GroupBuyAbcConfig;
import com.holder.saas.store.takeaway.producers.entity.domain.GroupStoreBindDO;
import com.holder.saas.store.takeaway.producers.service.GroupStoreBindService;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.business.group.StoreBindDTO;
import com.holderzone.saas.store.dto.order.response.groupon.GroupVerifyDTO;
import com.holderzone.saas.store.dto.takeaway.request.CouPonPreReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.CouPonReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtCouponPreRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtDelCouponRespDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class AbcGroupBuyServiceImplTest {

    @Mock
    private GroupBuyAbcConfig mockGroupBuyAbcConfig;
    @Mock
    private RestTemplate mockGroupBuyRestTemplate;
    @Mock
    private GroupStoreBindService mockGroupStoreBindService;

    private AbcGroupBuyServiceImpl abcGroupBuyServiceImplUnderTest;

    @Before
    public void setUp() {
        abcGroupBuyServiceImplUnderTest = new AbcGroupBuyServiceImpl(mockGroupBuyAbcConfig, mockGroupBuyRestTemplate,
                mockGroupStoreBindService);
    }

    @Test
    public void testBindStore() {
        // Setup
        final StoreBindDTO storeBind = new StoreBindDTO();
        storeBind.setGroupBuyType(0);
        storeBind.setAccountId("accountId");
        storeBind.setPoiId("poiId");
        storeBind.setStoreGuid("storeGuid");

        when(mockGroupStoreBindService.count(any(LambdaQueryWrapper.class))).thenReturn(0);
        when(mockGroupBuyRestTemplate.postForEntity("url", new HttpEntity<>("value", new HttpHeaders()),
                String.class)).thenReturn(new ResponseEntity<>("body", HttpStatus.OK));

        // Run the test
        abcGroupBuyServiceImplUnderTest.bindStore(storeBind);

        // Verify the results
        verify(mockGroupStoreBindService).remove(any(LambdaQueryWrapper.class));

        // Confirm GroupStoreBindService.save(...).
        final GroupStoreBindDO entity = new GroupStoreBindDO();
        entity.setId(0L);
        entity.setStoreGuid("storeGuid");
        entity.setPoiId("poiId");
        entity.setPoiName("poiName");
        entity.setType(0);
        verify(mockGroupStoreBindService).save(entity);
    }

    @Test(expected = BusinessException.class)
    public void testBindStore_RestTemplateThrowsRestClientException() {
        // Setup
        final StoreBindDTO storeBind = new StoreBindDTO();
        storeBind.setGroupBuyType(0);
        storeBind.setAccountId("accountId");
        storeBind.setPoiId("poiId");
        storeBind.setStoreGuid("storeGuid");

        when(mockGroupStoreBindService.count(any(LambdaQueryWrapper.class))).thenReturn(0);
        when(mockGroupBuyRestTemplate.postForEntity("url", new HttpEntity<>("value", new HttpHeaders()),
                String.class)).thenThrow(RestClientException.class);

        // Run the test
        abcGroupBuyServiceImplUnderTest.bindStore(storeBind);
    }

    @Test
    public void testCouponPrepare() {
        // Setup
        final CouPonPreReqDTO couPonPreReqDTO = new CouPonPreReqDTO();
        couPonPreReqDTO.setStoreGuid("storeGuid");
        couPonPreReqDTO.setCouponCode("code");
        couPonPreReqDTO.setErpOrderId("erpOrderId");
        couPonPreReqDTO.setActivityGuid("activityGuid");
        couPonPreReqDTO.setGroupBuyType(0);

        final MtCouponPreRespDTO mtCouponPreRespDTO = new MtCouponPreRespDTO();
        mtCouponPreRespDTO.setCount(0);
        mtCouponPreRespDTO.setCouponBuyPrice(0.0);
        mtCouponPreRespDTO.setCouponCode("couponCode");
        mtCouponPreRespDTO.setIsVoucher(false);
        mtCouponPreRespDTO.setCouponEndTime("couponEndTime");
        mtCouponPreRespDTO.setDealTitle("couponName");
        mtCouponPreRespDTO.setDealValue(0.0);
        mtCouponPreRespDTO.setCouponType(0);
        mtCouponPreRespDTO.setSkuId("skuId");
        final List<MtCouponPreRespDTO> expectedResult = Arrays.asList(mtCouponPreRespDTO);
        when(mockGroupStoreBindService.count(any(LambdaQueryWrapper.class))).thenReturn(0);
        when(mockGroupBuyRestTemplate.postForEntity("url", new HttpEntity<>("value", new HttpHeaders()),
                String.class)).thenReturn(new ResponseEntity<>("body", HttpStatus.OK));

        // Run the test
        final List<MtCouponPreRespDTO> result = abcGroupBuyServiceImplUnderTest.couponPrepare(couPonPreReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test(expected = RestClientException.class)
    public void testCouponPrepare_RestTemplateThrowsRestClientException() {
        // Setup
        final CouPonPreReqDTO couPonPreReqDTO = new CouPonPreReqDTO();
        couPonPreReqDTO.setStoreGuid("storeGuid");
        couPonPreReqDTO.setCouponCode("code");
        couPonPreReqDTO.setErpOrderId("erpOrderId");
        couPonPreReqDTO.setActivityGuid("activityGuid");
        couPonPreReqDTO.setGroupBuyType(0);

        when(mockGroupStoreBindService.count(any(LambdaQueryWrapper.class))).thenReturn(0);
        when(mockGroupBuyRestTemplate.postForEntity("url", new HttpEntity<>("value", new HttpHeaders()),
                String.class)).thenThrow(RestClientException.class);

        // Run the test
        abcGroupBuyServiceImplUnderTest.couponPrepare(couPonPreReqDTO);
    }

    @Test
    public void testVerifyCoupon() {
        // Setup
        final CouPonReqDTO couPonReq = new CouPonReqDTO();
        couPonReq.setStoreGuid("storeGuid");
        couPonReq.setErpId("erpId");
        couPonReq.setErpOrderId("erpOrderId");
        couPonReq.setCouponCodeList(Arrays.asList("value"));
        couPonReq.setGroupBuyType(0);
        couPonReq.setOrderFee(new BigDecimal("0.00"));

        final GroupVerifyDTO groupVerifyDTO = new GroupVerifyDTO();
        groupVerifyDTO.setGroupBuyType(0);
        groupVerifyDTO.setCode("code");
        groupVerifyDTO.setVerifyId("verifyId");
        groupVerifyDTO.setCertificateId("certificateId");
        groupVerifyDTO.setErpId("erpId");
        final List<GroupVerifyDTO> expectedResult = Arrays.asList(groupVerifyDTO);

        // Configure GroupStoreBindService.getOne(...).
        final GroupStoreBindDO groupStoreBindDO = new GroupStoreBindDO();
        groupStoreBindDO.setId(0L);
        groupStoreBindDO.setStoreGuid("storeGuid");
        groupStoreBindDO.setPoiId("poiId");
        groupStoreBindDO.setPoiName("poiName");
        groupStoreBindDO.setType(0);
        when(mockGroupStoreBindService.getOne(any(LambdaQueryWrapper.class))).thenReturn(groupStoreBindDO);

        when(mockGroupBuyRestTemplate.postForEntity("url", new HttpEntity<>("value", new HttpHeaders()),
                String.class)).thenReturn(new ResponseEntity<>("body", HttpStatus.OK));

        // Run the test
        final List<GroupVerifyDTO> result = abcGroupBuyServiceImplUnderTest.verifyCoupon(couPonReq);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test(expected = BusinessException.class)
    public void testVerifyCoupon_GroupStoreBindServiceReturnsNull() {
        // Setup
        final CouPonReqDTO couPonReq = new CouPonReqDTO();
        couPonReq.setStoreGuid("storeGuid");
        couPonReq.setErpId("erpId");
        couPonReq.setErpOrderId("erpOrderId");
        couPonReq.setCouponCodeList(Arrays.asList("value"));
        couPonReq.setGroupBuyType(0);
        couPonReq.setOrderFee(new BigDecimal("0.00"));

        when(mockGroupStoreBindService.getOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Run the test
        abcGroupBuyServiceImplUnderTest.verifyCoupon(couPonReq);
    }

    @Test(expected = RestClientException.class)
    public void testVerifyCoupon_RestTemplateThrowsRestClientException() {
        // Setup
        final CouPonReqDTO couPonReq = new CouPonReqDTO();
        couPonReq.setStoreGuid("storeGuid");
        couPonReq.setErpId("erpId");
        couPonReq.setErpOrderId("erpOrderId");
        couPonReq.setCouponCodeList(Arrays.asList("value"));
        couPonReq.setGroupBuyType(0);
        couPonReq.setOrderFee(new BigDecimal("0.00"));

        // Configure GroupStoreBindService.getOne(...).
        final GroupStoreBindDO groupStoreBindDO = new GroupStoreBindDO();
        groupStoreBindDO.setId(0L);
        groupStoreBindDO.setStoreGuid("storeGuid");
        groupStoreBindDO.setPoiId("poiId");
        groupStoreBindDO.setPoiName("poiName");
        groupStoreBindDO.setType(0);
        when(mockGroupStoreBindService.getOne(any(LambdaQueryWrapper.class))).thenReturn(groupStoreBindDO);

        when(mockGroupBuyRestTemplate.postForEntity("url", new HttpEntity<>("value", new HttpHeaders()),
                String.class)).thenThrow(RestClientException.class);

        // Run the test
        abcGroupBuyServiceImplUnderTest.verifyCoupon(couPonReq);
    }

    @Test
    public void testRevokeCoupon() {
        // Setup
        final GroupVerifyDTO revokeReq = new GroupVerifyDTO();
        revokeReq.setGroupBuyType(0);
        revokeReq.setCode("code");
        revokeReq.setVerifyId("verifyId");
        revokeReq.setCertificateId("certificateId");
        revokeReq.setErpId("erpId");

        final MtDelCouponRespDTO expectedResult = new MtDelCouponRespDTO(0, "message");
        when(mockGroupBuyRestTemplate.postForEntity("url", new HttpEntity<>("value", new HttpHeaders()),
                String.class)).thenReturn(new ResponseEntity<>("body", HttpStatus.OK));

        // Run the test
        final MtDelCouponRespDTO result = abcGroupBuyServiceImplUnderTest.revokeCoupon(revokeReq);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test(expected = RestClientException.class)
    public void testRevokeCoupon_RestTemplateThrowsRestClientException() {
        // Setup
        final GroupVerifyDTO revokeReq = new GroupVerifyDTO();
        revokeReq.setGroupBuyType(0);
        revokeReq.setCode("code");
        revokeReq.setVerifyId("verifyId");
        revokeReq.setCertificateId("certificateId");
        revokeReq.setErpId("erpId");

        when(mockGroupBuyRestTemplate.postForEntity("url", new HttpEntity<>("value", new HttpHeaders()),
                String.class)).thenThrow(RestClientException.class);

        // Run the test
        abcGroupBuyServiceImplUnderTest.revokeCoupon(revokeReq);
    }

    @Test
    public void testGetToken() {
        assertNull(abcGroupBuyServiceImplUnderTest.getToken());
    }
}
