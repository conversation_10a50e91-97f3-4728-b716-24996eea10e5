package com.holder.saas.store.takeaway.producers.service.impl;

import com.holder.saas.store.takeaway.producers.mapstruct.MtOrderMapstruct;
import com.holder.saas.store.takeaway.producers.service.ErpGuidCacheService;
import com.holder.saas.store.takeaway.producers.service.MtOrderService;
import com.holderzone.saas.store.dto.takeaway.*;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class MtUnOrderParserImplTest {

    @Mock
    private MtOrderMapstruct mockMtOrderMapstruct;
    @Mock
    private ErpGuidCacheService mockErpGuidCacheService;
    @Mock
    private MtOrderService mockMtOrderService;

    private MtUnOrderParserImpl mtUnOrderParserImplUnderTest;

    @Before
    public void setUp() throws Exception {
        mtUnOrderParserImplUnderTest = new MtUnOrderParserImpl(mockMtOrderMapstruct, mockErpGuidCacheService,
                mockMtOrderService);
    }

    @Test
    public void testFromMtQueryOrderDetail() {
        // Setup
        final MtQueryOrderDetail mtQueryOrderDetail = new MtQueryOrderDetail();
        mtQueryOrderDetail.setCtime(0L);
        mtQueryOrderDetail.setEPoiId("ePoiId");
        mtQueryOrderDetail.setOrderId(0L);
        mtQueryOrderDetail.setPoiId(0L);
        mtQueryOrderDetail.setPoiName("poiName");

        final UnOrder expectedResult = new UnOrder();
        expectedResult.setShopId(0L);
        expectedResult.setShopName("poiName");
        expectedResult.setCbMsgType(0);
        expectedResult.setOrderType(0);
        expectedResult.setOrderSubType(0);
        expectedResult.setEnterpriseGuid("enterpriseGuid");
        expectedResult.setStoreGuid("ePoiId");
        expectedResult.setOrderId("orderId");
        expectedResult.setOrderViewId("orderViewId");
        expectedResult.setOrderDaySn("daySeq");
        expectedResult.setOrderRemark("caution");
        expectedResult.setReserve(false);
        expectedResult.setCustomerName("customerName");
        expectedResult.setCustomerPhone("customerPhone");
        expectedResult.setPrivacyPhone("privacyPhone");
        expectedResult.setCustomerAddress("customerAddress");
        expectedResult.setRecipientAddressDesensitization("recipientAddressDesensitization");
        expectedResult.setRealRecipientAddress("realRecipientAddress");
        expectedResult.setCustomerNumber(0);
        expectedResult.setFirstOrder(false);
        expectedResult.setShipLatitude("shipLatitude");
        expectedResult.setShipLongitude("shipLongitude");
        expectedResult.setShipperName("dispatcherName");
        expectedResult.setShipperPhone("dispatcherMobile");
        expectedResult.setThirdShipper(false);
        expectedResult.setInvoiced(false);
        expectedResult.setInvoiceTitle("invoiceTitle");
        expectedResult.setTaxpayerId("taxpayerId");
        expectedResult.setTotal(new BigDecimal("0.00"));
        expectedResult.setShipTotal(new BigDecimal("0.00"));
        expectedResult.setItemTotal(new BigDecimal("0.00"));
        expectedResult.setPackageTotal(new BigDecimal("0.00"));
        expectedResult.setDinnersNumber(0);
        expectedResult.setDiscountTotal(new BigDecimal("0.00"));
        expectedResult.setEnterpriseDiscount(new BigDecimal("0.00"));
        expectedResult.setPlatformDiscount(new BigDecimal("0.00"));
        expectedResult.setServiceFeeRate(new BigDecimal("0.00"));
        expectedResult.setServiceFee(new BigDecimal("0.00"));
        expectedResult.setCustomerActualPay(new BigDecimal("0.00"));
        expectedResult.setPart(false);
        expectedResult.setCustomerRefund(new BigDecimal("0.00"));
        expectedResult.setCustomerRefundItem("customerRefundItem");
        expectedResult.setShopTotal(new BigDecimal("0.00"));
        expectedResult.setOnlinePay(false);
        expectedResult.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setAcceptTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setDeliveryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRefundReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRefundReqReason("reason");
        expectedResult.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReason("用户取消");
        expectedResult.setCancelRoleName("美团客服");
        expectedResult.setCompleteTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final UnItem unItem = new UnItem();
        unItem.setItemSku("itemSku");
        unItem.setItemCode("app_food_code");
        unItem.setItemName("itemName");
        unItem.setItemUnit("itemUnit");
        unItem.setItemPrice(new BigDecimal("0.00"));
        unItem.setItemCount(new BigDecimal("0.00"));
        unItem.setItemTotal(new BigDecimal("0.00"));
        unItem.setItemSpec("itemSpec");
        unItem.setItemProperty("food_property");
        unItem.setBoxPrice(new BigDecimal("0.00"));
        unItem.setBoxCount(new BigDecimal("0.00"));
        unItem.setBoxTotal(new BigDecimal("0.00"));
        unItem.setCartId(0);
        expectedResult.setArrayOfUnItem(Arrays.asList(unItem));

        when(mockErpGuidCacheService.getEnterpriseGuid("ePoiId")).thenReturn("enterpriseGuid");

        // Configure MtOrderMapstruct.fromMtQueryOrderDetail(...).
        final MtCbOrderDetail mtCbOrderDetail = new MtCbOrderDetail();
        mtCbOrderDetail.setCaution("caution");
        mtCbOrderDetail.setCtime(0L);
        mtCbOrderDetail.setDaySeq("daySeq");
        mtCbOrderDetail.setDeliveryTime(0L);
        mtCbOrderDetail.setDinnersNumber(0);
        mtCbOrderDetail.setHasInvoiced(0);
        mtCbOrderDetail.setInvoiceTitle("invoiceTitle");
        mtCbOrderDetail.setTaxpayerId("taxpayerId");
        mtCbOrderDetail.setLatitude(0.0);
        mtCbOrderDetail.setLongitude(0.0);
        mtCbOrderDetail.setLogisticsCode("logisticsCode");
        mtCbOrderDetail.setOrderId(0L);
        mtCbOrderDetail.setOrderIdView(0L);
        mtCbOrderDetail.setOriginalPrice(0.0);
        mtCbOrderDetail.setPayType(0);
        mtCbOrderDetail.setPoiId(0L);
        mtCbOrderDetail.setPoiName("poiName");
        mtCbOrderDetail.setPoiReceiveDetail("poiReceiveDetail");
        mtCbOrderDetail.setRecipientAddress("customerAddress");
        mtCbOrderDetail.setRecipientAddressDesensitization("recipientAddressDesensitization");
        mtCbOrderDetail.setRecipientName("customerName");
        mtCbOrderDetail.setRecipientPhone("recipientPhone");
        mtCbOrderDetail.setBackupRecipientPhone("backupRecipientPhone");
        mtCbOrderDetail.setShippingFee(0.0);
        mtCbOrderDetail.setStatus(0);
        mtCbOrderDetail.setTotal(0.0);
        mtCbOrderDetail.setUtime(0L);
        mtCbOrderDetail.setDetail("detail");
        mtCbOrderDetail.setExtras("extras");
        final MtQueryOrderDetail mtQueryOrderDetail1 = new MtQueryOrderDetail();
        mtQueryOrderDetail1.setCtime(0L);
        mtQueryOrderDetail1.setEPoiId("ePoiId");
        mtQueryOrderDetail1.setOrderId(0L);
        mtQueryOrderDetail1.setPoiId(0L);
        mtQueryOrderDetail1.setPoiName("poiName");
        when(mockMtOrderMapstruct.fromMtQueryOrderDetail(mtQueryOrderDetail1)).thenReturn(mtCbOrderDetail);

        // Run the test
        final UnOrder result = mtUnOrderParserImplUnderTest.fromMtQueryOrderDetail(mtQueryOrderDetail);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testFromMtCbOrderCreated() {
        // Setup
        final MtCallbackDTO mtCallbackDTO = new MtCallbackDTO();
        mtCallbackDTO.setTimestamp(0L);
        mtCallbackDTO.setEPoiId("ePoiId");
        mtCallbackDTO.setOrder("order");
        mtCallbackDTO.setOrderCancel("orderCancel");
        mtCallbackDTO.setOrderRefund("orderRefund");
        mtCallbackDTO.setShippingStatus("shippingStatus");
        mtCallbackDTO.setPartOrderRefund("partOrderRefund");

        final UnOrder expectedResult = new UnOrder();
        expectedResult.setShopId(0L);
        expectedResult.setShopName("poiName");
        expectedResult.setCbMsgType(0);
        expectedResult.setOrderType(0);
        expectedResult.setOrderSubType(0);
        expectedResult.setEnterpriseGuid("enterpriseGuid");
        expectedResult.setStoreGuid("ePoiId");
        expectedResult.setOrderId("orderId");
        expectedResult.setOrderViewId("orderViewId");
        expectedResult.setOrderDaySn("daySeq");
        expectedResult.setOrderRemark("caution");
        expectedResult.setReserve(false);
        expectedResult.setCustomerName("customerName");
        expectedResult.setCustomerPhone("customerPhone");
        expectedResult.setPrivacyPhone("privacyPhone");
        expectedResult.setCustomerAddress("customerAddress");
        expectedResult.setRecipientAddressDesensitization("recipientAddressDesensitization");
        expectedResult.setRealRecipientAddress("realRecipientAddress");
        expectedResult.setCustomerNumber(0);
        expectedResult.setFirstOrder(false);
        expectedResult.setShipLatitude("shipLatitude");
        expectedResult.setShipLongitude("shipLongitude");
        expectedResult.setShipperName("dispatcherName");
        expectedResult.setShipperPhone("dispatcherMobile");
        expectedResult.setThirdShipper(false);
        expectedResult.setInvoiced(false);
        expectedResult.setInvoiceTitle("invoiceTitle");
        expectedResult.setTaxpayerId("taxpayerId");
        expectedResult.setTotal(new BigDecimal("0.00"));
        expectedResult.setShipTotal(new BigDecimal("0.00"));
        expectedResult.setItemTotal(new BigDecimal("0.00"));
        expectedResult.setPackageTotal(new BigDecimal("0.00"));
        expectedResult.setDinnersNumber(0);
        expectedResult.setDiscountTotal(new BigDecimal("0.00"));
        expectedResult.setEnterpriseDiscount(new BigDecimal("0.00"));
        expectedResult.setPlatformDiscount(new BigDecimal("0.00"));
        expectedResult.setServiceFeeRate(new BigDecimal("0.00"));
        expectedResult.setServiceFee(new BigDecimal("0.00"));
        expectedResult.setCustomerActualPay(new BigDecimal("0.00"));
        expectedResult.setPart(false);
        expectedResult.setCustomerRefund(new BigDecimal("0.00"));
        expectedResult.setCustomerRefundItem("customerRefundItem");
        expectedResult.setShopTotal(new BigDecimal("0.00"));
        expectedResult.setOnlinePay(false);
        expectedResult.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setAcceptTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setDeliveryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRefundReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRefundReqReason("reason");
        expectedResult.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReason("用户取消");
        expectedResult.setCancelRoleName("美团客服");
        expectedResult.setCompleteTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final UnItem unItem = new UnItem();
        unItem.setItemSku("itemSku");
        unItem.setItemCode("app_food_code");
        unItem.setItemName("itemName");
        unItem.setItemUnit("itemUnit");
        unItem.setItemPrice(new BigDecimal("0.00"));
        unItem.setItemCount(new BigDecimal("0.00"));
        unItem.setItemTotal(new BigDecimal("0.00"));
        unItem.setItemSpec("itemSpec");
        unItem.setItemProperty("food_property");
        unItem.setBoxPrice(new BigDecimal("0.00"));
        unItem.setBoxCount(new BigDecimal("0.00"));
        unItem.setBoxTotal(new BigDecimal("0.00"));
        unItem.setCartId(0);
        expectedResult.setArrayOfUnItem(Arrays.asList(unItem));

        when(mockErpGuidCacheService.getEnterpriseGuid("ePoiId")).thenReturn("enterpriseGuid");

        // Run the test
        final UnOrder result = mtUnOrderParserImplUnderTest.fromMtCbOrderCreated(mtCallbackDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testFromMtCbOrderCanceled() {
        // Setup
        final MtCallbackDTO mtCallbackDTO = new MtCallbackDTO();
        mtCallbackDTO.setTimestamp(0L);
        mtCallbackDTO.setEPoiId("ePoiId");
        mtCallbackDTO.setOrder("order");
        mtCallbackDTO.setOrderCancel("orderCancel");
        mtCallbackDTO.setOrderRefund("orderRefund");
        mtCallbackDTO.setShippingStatus("shippingStatus");
        mtCallbackDTO.setPartOrderRefund("partOrderRefund");

        final UnOrder expectedResult = new UnOrder();
        expectedResult.setShopId(0L);
        expectedResult.setShopName("poiName");
        expectedResult.setCbMsgType(0);
        expectedResult.setOrderType(0);
        expectedResult.setOrderSubType(0);
        expectedResult.setEnterpriseGuid("enterpriseGuid");
        expectedResult.setStoreGuid("ePoiId");
        expectedResult.setOrderId("orderId");
        expectedResult.setOrderViewId("orderViewId");
        expectedResult.setOrderDaySn("daySeq");
        expectedResult.setOrderRemark("caution");
        expectedResult.setReserve(false);
        expectedResult.setCustomerName("customerName");
        expectedResult.setCustomerPhone("customerPhone");
        expectedResult.setPrivacyPhone("privacyPhone");
        expectedResult.setCustomerAddress("customerAddress");
        expectedResult.setRecipientAddressDesensitization("recipientAddressDesensitization");
        expectedResult.setRealRecipientAddress("realRecipientAddress");
        expectedResult.setCustomerNumber(0);
        expectedResult.setFirstOrder(false);
        expectedResult.setShipLatitude("shipLatitude");
        expectedResult.setShipLongitude("shipLongitude");
        expectedResult.setShipperName("dispatcherName");
        expectedResult.setShipperPhone("dispatcherMobile");
        expectedResult.setThirdShipper(false);
        expectedResult.setInvoiced(false);
        expectedResult.setInvoiceTitle("invoiceTitle");
        expectedResult.setTaxpayerId("taxpayerId");
        expectedResult.setTotal(new BigDecimal("0.00"));
        expectedResult.setShipTotal(new BigDecimal("0.00"));
        expectedResult.setItemTotal(new BigDecimal("0.00"));
        expectedResult.setPackageTotal(new BigDecimal("0.00"));
        expectedResult.setDinnersNumber(0);
        expectedResult.setDiscountTotal(new BigDecimal("0.00"));
        expectedResult.setEnterpriseDiscount(new BigDecimal("0.00"));
        expectedResult.setPlatformDiscount(new BigDecimal("0.00"));
        expectedResult.setServiceFeeRate(new BigDecimal("0.00"));
        expectedResult.setServiceFee(new BigDecimal("0.00"));
        expectedResult.setCustomerActualPay(new BigDecimal("0.00"));
        expectedResult.setPart(false);
        expectedResult.setCustomerRefund(new BigDecimal("0.00"));
        expectedResult.setCustomerRefundItem("customerRefundItem");
        expectedResult.setShopTotal(new BigDecimal("0.00"));
        expectedResult.setOnlinePay(false);
        expectedResult.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setAcceptTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setDeliveryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRefundReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRefundReqReason("reason");
        expectedResult.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReason("用户取消");
        expectedResult.setCancelRoleName("美团客服");
        expectedResult.setCompleteTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final UnItem unItem = new UnItem();
        unItem.setItemSku("itemSku");
        unItem.setItemCode("app_food_code");
        unItem.setItemName("itemName");
        unItem.setItemUnit("itemUnit");
        unItem.setItemPrice(new BigDecimal("0.00"));
        unItem.setItemCount(new BigDecimal("0.00"));
        unItem.setItemTotal(new BigDecimal("0.00"));
        unItem.setItemSpec("itemSpec");
        unItem.setItemProperty("food_property");
        unItem.setBoxPrice(new BigDecimal("0.00"));
        unItem.setBoxCount(new BigDecimal("0.00"));
        unItem.setBoxTotal(new BigDecimal("0.00"));
        unItem.setCartId(0);
        expectedResult.setArrayOfUnItem(Arrays.asList(unItem));

        when(mockErpGuidCacheService.getEnterpriseGuid("ePoiId")).thenReturn("enterpriseGuid");

        // Run the test
        final UnOrder result = mtUnOrderParserImplUnderTest.fromMtCbOrderCanceled(mtCallbackDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testFromMtCbOrderRefund() {
        // Setup
        final MtCallbackDTO mtCallbackDTO = new MtCallbackDTO();
        mtCallbackDTO.setTimestamp(0L);
        mtCallbackDTO.setEPoiId("ePoiId");
        mtCallbackDTO.setOrder("order");
        mtCallbackDTO.setOrderCancel("orderCancel");
        mtCallbackDTO.setOrderRefund("orderRefund");
        mtCallbackDTO.setShippingStatus("shippingStatus");
        mtCallbackDTO.setPartOrderRefund("partOrderRefund");

        final UnOrder expectedResult = new UnOrder();
        expectedResult.setShopId(0L);
        expectedResult.setShopName("poiName");
        expectedResult.setCbMsgType(0);
        expectedResult.setOrderType(0);
        expectedResult.setOrderSubType(0);
        expectedResult.setEnterpriseGuid("enterpriseGuid");
        expectedResult.setStoreGuid("ePoiId");
        expectedResult.setOrderId("orderId");
        expectedResult.setOrderViewId("orderViewId");
        expectedResult.setOrderDaySn("daySeq");
        expectedResult.setOrderRemark("caution");
        expectedResult.setReserve(false);
        expectedResult.setCustomerName("customerName");
        expectedResult.setCustomerPhone("customerPhone");
        expectedResult.setPrivacyPhone("privacyPhone");
        expectedResult.setCustomerAddress("customerAddress");
        expectedResult.setRecipientAddressDesensitization("recipientAddressDesensitization");
        expectedResult.setRealRecipientAddress("realRecipientAddress");
        expectedResult.setCustomerNumber(0);
        expectedResult.setFirstOrder(false);
        expectedResult.setShipLatitude("shipLatitude");
        expectedResult.setShipLongitude("shipLongitude");
        expectedResult.setShipperName("dispatcherName");
        expectedResult.setShipperPhone("dispatcherMobile");
        expectedResult.setThirdShipper(false);
        expectedResult.setInvoiced(false);
        expectedResult.setInvoiceTitle("invoiceTitle");
        expectedResult.setTaxpayerId("taxpayerId");
        expectedResult.setTotal(new BigDecimal("0.00"));
        expectedResult.setShipTotal(new BigDecimal("0.00"));
        expectedResult.setItemTotal(new BigDecimal("0.00"));
        expectedResult.setPackageTotal(new BigDecimal("0.00"));
        expectedResult.setDinnersNumber(0);
        expectedResult.setDiscountTotal(new BigDecimal("0.00"));
        expectedResult.setEnterpriseDiscount(new BigDecimal("0.00"));
        expectedResult.setPlatformDiscount(new BigDecimal("0.00"));
        expectedResult.setServiceFeeRate(new BigDecimal("0.00"));
        expectedResult.setServiceFee(new BigDecimal("0.00"));
        expectedResult.setCustomerActualPay(new BigDecimal("0.00"));
        expectedResult.setPart(false);
        expectedResult.setCustomerRefund(new BigDecimal("0.00"));
        expectedResult.setCustomerRefundItem("customerRefundItem");
        expectedResult.setShopTotal(new BigDecimal("0.00"));
        expectedResult.setOnlinePay(false);
        expectedResult.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setAcceptTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setDeliveryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRefundReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRefundReqReason("reason");
        expectedResult.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReason("用户取消");
        expectedResult.setCancelRoleName("美团客服");
        expectedResult.setCompleteTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final UnItem unItem = new UnItem();
        unItem.setItemSku("itemSku");
        unItem.setItemCode("app_food_code");
        unItem.setItemName("itemName");
        unItem.setItemUnit("itemUnit");
        unItem.setItemPrice(new BigDecimal("0.00"));
        unItem.setItemCount(new BigDecimal("0.00"));
        unItem.setItemTotal(new BigDecimal("0.00"));
        unItem.setItemSpec("itemSpec");
        unItem.setItemProperty("food_property");
        unItem.setBoxPrice(new BigDecimal("0.00"));
        unItem.setBoxCount(new BigDecimal("0.00"));
        unItem.setBoxTotal(new BigDecimal("0.00"));
        unItem.setCartId(0);
        expectedResult.setArrayOfUnItem(Arrays.asList(unItem));

        when(mockErpGuidCacheService.getEnterpriseGuid("ePoiId")).thenReturn("enterpriseGuid");

        // Run the test
        final UnOrder result = mtUnOrderParserImplUnderTest.fromMtCbOrderRefund(mtCallbackDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testFromMtCbOrderPartRefund() {
        // Setup
        final MtCallbackDTO mtCallbackDTO = new MtCallbackDTO();
        mtCallbackDTO.setTimestamp(0L);
        mtCallbackDTO.setEPoiId("ePoiId");
        mtCallbackDTO.setOrder("order");
        mtCallbackDTO.setOrderCancel("orderCancel");
        mtCallbackDTO.setOrderRefund("orderRefund");
        mtCallbackDTO.setShippingStatus("shippingStatus");
        mtCallbackDTO.setPartOrderRefund("partOrderRefund");

        final UnOrder expectedResult = new UnOrder();
        expectedResult.setShopId(0L);
        expectedResult.setShopName("poiName");
        expectedResult.setCbMsgType(0);
        expectedResult.setOrderType(0);
        expectedResult.setOrderSubType(0);
        expectedResult.setEnterpriseGuid("enterpriseGuid");
        expectedResult.setStoreGuid("ePoiId");
        expectedResult.setOrderId("orderId");
        expectedResult.setOrderViewId("orderViewId");
        expectedResult.setOrderDaySn("daySeq");
        expectedResult.setOrderRemark("caution");
        expectedResult.setReserve(false);
        expectedResult.setCustomerName("customerName");
        expectedResult.setCustomerPhone("customerPhone");
        expectedResult.setPrivacyPhone("privacyPhone");
        expectedResult.setCustomerAddress("customerAddress");
        expectedResult.setRecipientAddressDesensitization("recipientAddressDesensitization");
        expectedResult.setRealRecipientAddress("realRecipientAddress");
        expectedResult.setCustomerNumber(0);
        expectedResult.setFirstOrder(false);
        expectedResult.setShipLatitude("shipLatitude");
        expectedResult.setShipLongitude("shipLongitude");
        expectedResult.setShipperName("dispatcherName");
        expectedResult.setShipperPhone("dispatcherMobile");
        expectedResult.setThirdShipper(false);
        expectedResult.setInvoiced(false);
        expectedResult.setInvoiceTitle("invoiceTitle");
        expectedResult.setTaxpayerId("taxpayerId");
        expectedResult.setTotal(new BigDecimal("0.00"));
        expectedResult.setShipTotal(new BigDecimal("0.00"));
        expectedResult.setItemTotal(new BigDecimal("0.00"));
        expectedResult.setPackageTotal(new BigDecimal("0.00"));
        expectedResult.setDinnersNumber(0);
        expectedResult.setDiscountTotal(new BigDecimal("0.00"));
        expectedResult.setEnterpriseDiscount(new BigDecimal("0.00"));
        expectedResult.setPlatformDiscount(new BigDecimal("0.00"));
        expectedResult.setServiceFeeRate(new BigDecimal("0.00"));
        expectedResult.setServiceFee(new BigDecimal("0.00"));
        expectedResult.setCustomerActualPay(new BigDecimal("0.00"));
        expectedResult.setPart(false);
        expectedResult.setCustomerRefund(new BigDecimal("0.00"));
        expectedResult.setCustomerRefundItem("customerRefundItem");
        expectedResult.setShopTotal(new BigDecimal("0.00"));
        expectedResult.setOnlinePay(false);
        expectedResult.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setAcceptTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setDeliveryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRefundReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRefundReqReason("reason");
        expectedResult.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReason("用户取消");
        expectedResult.setCancelRoleName("美团客服");
        expectedResult.setCompleteTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final UnItem unItem = new UnItem();
        unItem.setItemSku("itemSku");
        unItem.setItemCode("app_food_code");
        unItem.setItemName("itemName");
        unItem.setItemUnit("itemUnit");
        unItem.setItemPrice(new BigDecimal("0.00"));
        unItem.setItemCount(new BigDecimal("0.00"));
        unItem.setItemTotal(new BigDecimal("0.00"));
        unItem.setItemSpec("itemSpec");
        unItem.setItemProperty("food_property");
        unItem.setBoxPrice(new BigDecimal("0.00"));
        unItem.setBoxCount(new BigDecimal("0.00"));
        unItem.setBoxTotal(new BigDecimal("0.00"));
        unItem.setCartId(0);
        expectedResult.setArrayOfUnItem(Arrays.asList(unItem));

        when(mockErpGuidCacheService.getEnterpriseGuid("ePoiId")).thenReturn("enterpriseGuid");

        // Run the test
        final UnOrder result = mtUnOrderParserImplUnderTest.fromMtCbOrderPartRefund(mtCallbackDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testFromMtCbOrderConfirmed() {
        // Setup
        final MtCallbackDTO mtCallbackDTO = new MtCallbackDTO();
        mtCallbackDTO.setTimestamp(0L);
        mtCallbackDTO.setEPoiId("ePoiId");
        mtCallbackDTO.setOrder("order");
        mtCallbackDTO.setOrderCancel("orderCancel");
        mtCallbackDTO.setOrderRefund("orderRefund");
        mtCallbackDTO.setShippingStatus("shippingStatus");
        mtCallbackDTO.setPartOrderRefund("partOrderRefund");

        final UnOrder expectedResult = new UnOrder();
        expectedResult.setShopId(0L);
        expectedResult.setShopName("poiName");
        expectedResult.setCbMsgType(0);
        expectedResult.setOrderType(0);
        expectedResult.setOrderSubType(0);
        expectedResult.setEnterpriseGuid("enterpriseGuid");
        expectedResult.setStoreGuid("ePoiId");
        expectedResult.setOrderId("orderId");
        expectedResult.setOrderViewId("orderViewId");
        expectedResult.setOrderDaySn("daySeq");
        expectedResult.setOrderRemark("caution");
        expectedResult.setReserve(false);
        expectedResult.setCustomerName("customerName");
        expectedResult.setCustomerPhone("customerPhone");
        expectedResult.setPrivacyPhone("privacyPhone");
        expectedResult.setCustomerAddress("customerAddress");
        expectedResult.setRecipientAddressDesensitization("recipientAddressDesensitization");
        expectedResult.setRealRecipientAddress("realRecipientAddress");
        expectedResult.setCustomerNumber(0);
        expectedResult.setFirstOrder(false);
        expectedResult.setShipLatitude("shipLatitude");
        expectedResult.setShipLongitude("shipLongitude");
        expectedResult.setShipperName("dispatcherName");
        expectedResult.setShipperPhone("dispatcherMobile");
        expectedResult.setThirdShipper(false);
        expectedResult.setInvoiced(false);
        expectedResult.setInvoiceTitle("invoiceTitle");
        expectedResult.setTaxpayerId("taxpayerId");
        expectedResult.setTotal(new BigDecimal("0.00"));
        expectedResult.setShipTotal(new BigDecimal("0.00"));
        expectedResult.setItemTotal(new BigDecimal("0.00"));
        expectedResult.setPackageTotal(new BigDecimal("0.00"));
        expectedResult.setDinnersNumber(0);
        expectedResult.setDiscountTotal(new BigDecimal("0.00"));
        expectedResult.setEnterpriseDiscount(new BigDecimal("0.00"));
        expectedResult.setPlatformDiscount(new BigDecimal("0.00"));
        expectedResult.setServiceFeeRate(new BigDecimal("0.00"));
        expectedResult.setServiceFee(new BigDecimal("0.00"));
        expectedResult.setCustomerActualPay(new BigDecimal("0.00"));
        expectedResult.setPart(false);
        expectedResult.setCustomerRefund(new BigDecimal("0.00"));
        expectedResult.setCustomerRefundItem("customerRefundItem");
        expectedResult.setShopTotal(new BigDecimal("0.00"));
        expectedResult.setOnlinePay(false);
        expectedResult.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setAcceptTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setDeliveryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRefundReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRefundReqReason("reason");
        expectedResult.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReason("用户取消");
        expectedResult.setCancelRoleName("美团客服");
        expectedResult.setCompleteTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final UnItem unItem = new UnItem();
        unItem.setItemSku("itemSku");
        unItem.setItemCode("app_food_code");
        unItem.setItemName("itemName");
        unItem.setItemUnit("itemUnit");
        unItem.setItemPrice(new BigDecimal("0.00"));
        unItem.setItemCount(new BigDecimal("0.00"));
        unItem.setItemTotal(new BigDecimal("0.00"));
        unItem.setItemSpec("itemSpec");
        unItem.setItemProperty("food_property");
        unItem.setBoxPrice(new BigDecimal("0.00"));
        unItem.setBoxCount(new BigDecimal("0.00"));
        unItem.setBoxTotal(new BigDecimal("0.00"));
        unItem.setCartId(0);
        expectedResult.setArrayOfUnItem(Arrays.asList(unItem));

        when(mockErpGuidCacheService.getEnterpriseGuid("ePoiId")).thenReturn("enterpriseGuid");

        // Run the test
        final UnOrder result = mtUnOrderParserImplUnderTest.fromMtCbOrderConfirmed(mtCallbackDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testFromMtCbOrderFinished() {
        // Setup
        final MtCallbackDTO mtCallbackDTO = new MtCallbackDTO();
        mtCallbackDTO.setTimestamp(0L);
        mtCallbackDTO.setEPoiId("ePoiId");
        mtCallbackDTO.setOrder("order");
        mtCallbackDTO.setOrderCancel("orderCancel");
        mtCallbackDTO.setOrderRefund("orderRefund");
        mtCallbackDTO.setShippingStatus("shippingStatus");
        mtCallbackDTO.setPartOrderRefund("partOrderRefund");

        final UnOrder expectedResult = new UnOrder();
        expectedResult.setShopId(0L);
        expectedResult.setShopName("poiName");
        expectedResult.setCbMsgType(0);
        expectedResult.setOrderType(0);
        expectedResult.setOrderSubType(0);
        expectedResult.setEnterpriseGuid("enterpriseGuid");
        expectedResult.setStoreGuid("ePoiId");
        expectedResult.setOrderId("orderId");
        expectedResult.setOrderViewId("orderViewId");
        expectedResult.setOrderDaySn("daySeq");
        expectedResult.setOrderRemark("caution");
        expectedResult.setReserve(false);
        expectedResult.setCustomerName("customerName");
        expectedResult.setCustomerPhone("customerPhone");
        expectedResult.setPrivacyPhone("privacyPhone");
        expectedResult.setCustomerAddress("customerAddress");
        expectedResult.setRecipientAddressDesensitization("recipientAddressDesensitization");
        expectedResult.setRealRecipientAddress("realRecipientAddress");
        expectedResult.setCustomerNumber(0);
        expectedResult.setFirstOrder(false);
        expectedResult.setShipLatitude("shipLatitude");
        expectedResult.setShipLongitude("shipLongitude");
        expectedResult.setShipperName("dispatcherName");
        expectedResult.setShipperPhone("dispatcherMobile");
        expectedResult.setThirdShipper(false);
        expectedResult.setInvoiced(false);
        expectedResult.setInvoiceTitle("invoiceTitle");
        expectedResult.setTaxpayerId("taxpayerId");
        expectedResult.setTotal(new BigDecimal("0.00"));
        expectedResult.setShipTotal(new BigDecimal("0.00"));
        expectedResult.setItemTotal(new BigDecimal("0.00"));
        expectedResult.setPackageTotal(new BigDecimal("0.00"));
        expectedResult.setDinnersNumber(0);
        expectedResult.setDiscountTotal(new BigDecimal("0.00"));
        expectedResult.setEnterpriseDiscount(new BigDecimal("0.00"));
        expectedResult.setPlatformDiscount(new BigDecimal("0.00"));
        expectedResult.setServiceFeeRate(new BigDecimal("0.00"));
        expectedResult.setServiceFee(new BigDecimal("0.00"));
        expectedResult.setCustomerActualPay(new BigDecimal("0.00"));
        expectedResult.setPart(false);
        expectedResult.setCustomerRefund(new BigDecimal("0.00"));
        expectedResult.setCustomerRefundItem("customerRefundItem");
        expectedResult.setShopTotal(new BigDecimal("0.00"));
        expectedResult.setOnlinePay(false);
        expectedResult.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setAcceptTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setDeliveryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRefundReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRefundReqReason("reason");
        expectedResult.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReason("用户取消");
        expectedResult.setCancelRoleName("美团客服");
        expectedResult.setCompleteTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final UnItem unItem = new UnItem();
        unItem.setItemSku("itemSku");
        unItem.setItemCode("app_food_code");
        unItem.setItemName("itemName");
        unItem.setItemUnit("itemUnit");
        unItem.setItemPrice(new BigDecimal("0.00"));
        unItem.setItemCount(new BigDecimal("0.00"));
        unItem.setItemTotal(new BigDecimal("0.00"));
        unItem.setItemSpec("itemSpec");
        unItem.setItemProperty("food_property");
        unItem.setBoxPrice(new BigDecimal("0.00"));
        unItem.setBoxCount(new BigDecimal("0.00"));
        unItem.setBoxTotal(new BigDecimal("0.00"));
        unItem.setCartId(0);
        expectedResult.setArrayOfUnItem(Arrays.asList(unItem));

        when(mockErpGuidCacheService.getEnterpriseGuid("ePoiId")).thenReturn("enterpriseGuid");

        // Run the test
        final UnOrder result = mtUnOrderParserImplUnderTest.fromMtCbOrderFinished(mtCallbackDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testFromMtCbShippingStatus() {
        // Setup
        final MtCallbackDTO mtCallbackDTO = new MtCallbackDTO();
        mtCallbackDTO.setTimestamp(0L);
        mtCallbackDTO.setEPoiId("ePoiId");
        mtCallbackDTO.setOrder("order");
        mtCallbackDTO.setOrderCancel("orderCancel");
        mtCallbackDTO.setOrderRefund("orderRefund");
        mtCallbackDTO.setShippingStatus("shippingStatus");
        mtCallbackDTO.setPartOrderRefund("partOrderRefund");

        final UnOrder expectedResult = new UnOrder();
        expectedResult.setShopId(0L);
        expectedResult.setShopName("poiName");
        expectedResult.setCbMsgType(0);
        expectedResult.setOrderType(0);
        expectedResult.setOrderSubType(0);
        expectedResult.setEnterpriseGuid("enterpriseGuid");
        expectedResult.setStoreGuid("ePoiId");
        expectedResult.setOrderId("orderId");
        expectedResult.setOrderViewId("orderViewId");
        expectedResult.setOrderDaySn("daySeq");
        expectedResult.setOrderRemark("caution");
        expectedResult.setReserve(false);
        expectedResult.setCustomerName("customerName");
        expectedResult.setCustomerPhone("customerPhone");
        expectedResult.setPrivacyPhone("privacyPhone");
        expectedResult.setCustomerAddress("customerAddress");
        expectedResult.setRecipientAddressDesensitization("recipientAddressDesensitization");
        expectedResult.setRealRecipientAddress("realRecipientAddress");
        expectedResult.setCustomerNumber(0);
        expectedResult.setFirstOrder(false);
        expectedResult.setShipLatitude("shipLatitude");
        expectedResult.setShipLongitude("shipLongitude");
        expectedResult.setShipperName("dispatcherName");
        expectedResult.setShipperPhone("dispatcherMobile");
        expectedResult.setThirdShipper(false);
        expectedResult.setInvoiced(false);
        expectedResult.setInvoiceTitle("invoiceTitle");
        expectedResult.setTaxpayerId("taxpayerId");
        expectedResult.setTotal(new BigDecimal("0.00"));
        expectedResult.setShipTotal(new BigDecimal("0.00"));
        expectedResult.setItemTotal(new BigDecimal("0.00"));
        expectedResult.setPackageTotal(new BigDecimal("0.00"));
        expectedResult.setDinnersNumber(0);
        expectedResult.setDiscountTotal(new BigDecimal("0.00"));
        expectedResult.setEnterpriseDiscount(new BigDecimal("0.00"));
        expectedResult.setPlatformDiscount(new BigDecimal("0.00"));
        expectedResult.setServiceFeeRate(new BigDecimal("0.00"));
        expectedResult.setServiceFee(new BigDecimal("0.00"));
        expectedResult.setCustomerActualPay(new BigDecimal("0.00"));
        expectedResult.setPart(false);
        expectedResult.setCustomerRefund(new BigDecimal("0.00"));
        expectedResult.setCustomerRefundItem("customerRefundItem");
        expectedResult.setShopTotal(new BigDecimal("0.00"));
        expectedResult.setOnlinePay(false);
        expectedResult.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setAcceptTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setDeliveryTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRefundReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRefundReqReason("reason");
        expectedResult.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancelReason("用户取消");
        expectedResult.setCancelRoleName("美团客服");
        expectedResult.setCompleteTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final UnItem unItem = new UnItem();
        unItem.setItemSku("itemSku");
        unItem.setItemCode("app_food_code");
        unItem.setItemName("itemName");
        unItem.setItemUnit("itemUnit");
        unItem.setItemPrice(new BigDecimal("0.00"));
        unItem.setItemCount(new BigDecimal("0.00"));
        unItem.setItemTotal(new BigDecimal("0.00"));
        unItem.setItemSpec("itemSpec");
        unItem.setItemProperty("food_property");
        unItem.setBoxPrice(new BigDecimal("0.00"));
        unItem.setBoxCount(new BigDecimal("0.00"));
        unItem.setBoxTotal(new BigDecimal("0.00"));
        unItem.setCartId(0);
        expectedResult.setArrayOfUnItem(Arrays.asList(unItem));

        when(mockErpGuidCacheService.getEnterpriseGuid("ePoiId")).thenReturn("enterpriseGuid");

        // Run the test
        final UnOrder result = mtUnOrderParserImplUnderTest.fromMtCbShippingStatus(mtCallbackDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }
}
