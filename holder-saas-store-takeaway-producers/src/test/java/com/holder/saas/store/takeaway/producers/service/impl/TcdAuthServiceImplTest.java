package com.holder.saas.store.takeaway.producers.service.impl;

import com.holder.saas.store.takeaway.producers.entity.domain.TcdAuthDO;
import com.holder.saas.store.takeaway.producers.service.DistributedService;
import com.holderzone.saas.store.dto.takeaway.request.*;
import com.holderzone.saas.store.dto.takeaway.response.*;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class TcdAuthServiceImplTest {

    @Mock
    private DistributedService mockDistributedService;

    private TcdAuthServiceImpl tcdAuthServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        tcdAuthServiceImplUnderTest = new TcdAuthServiceImpl(mockDistributedService);
        ReflectionTestUtils.setField(tcdAuthServiceImplUnderTest, "zcUrl", "zcUrl");
        ReflectionTestUtils.setField(tcdAuthServiceImplUnderTest, "shopBindingUrl", "shopBindingUrl");
        ReflectionTestUtils.setField(tcdAuthServiceImplUnderTest, "shopDeliverySyncUrl", "shopDeliverySyncUrl");
        ReflectionTestUtils.setField(tcdAuthServiceImplUnderTest, "typeQueryUrl", "typeQueryUrl");
        ReflectionTestUtils.setField(tcdAuthServiceImplUnderTest, "itemQueryUrl", "itemQueryUrl");
        ReflectionTestUtils.setField(tcdAuthServiceImplUnderTest, "itemBindingUrl", "itemBindingUrl");
        ReflectionTestUtils.setField(tcdAuthServiceImplUnderTest, "orderDiningOutUrl", "orderDiningOutUrl");
        ReflectionTestUtils.setField(tcdAuthServiceImplUnderTest, "orderConfirmTheMealUrl", "orderConfirmTheMealUrl");
    }

    @Test
    public void testDoShopBindingTcd() {
        // Setup
        final TCDBindReqDTO tcdBindReqDTO = new TCDBindReqDTO();
        tcdBindReqDTO.setEnterpriseGuid("enterpriseGuid");
        tcdBindReqDTO.setStoreGuid("storeGuid");
        tcdBindReqDTO.setId("id");
        tcdBindReqDTO.setPlatformId("platformId");
        tcdBindReqDTO.setToken("token");

        final TcdCommonRespDTO expectedResult = new TcdCommonRespDTO();
        expectedResult.setCode(0);
        expectedResult.setMessage("FAILURE");

        when(mockDistributedService.nextZcGuid()).thenReturn("e39d6a16-a888-400b-9391-fe156dc1966c");

        // Run the test
        final TcdCommonRespDTO result = tcdAuthServiceImplUnderTest.doShopBindingTcd(tcdBindReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testDoShopUnBindingTcd() {
        // Setup
        final TCDBindReqDTO TCDBindReqDTO = new TCDBindReqDTO();
        TCDBindReqDTO.setEnterpriseGuid("enterpriseGuid");
        TCDBindReqDTO.setStoreGuid("storeGuid");
        TCDBindReqDTO.setId("id");
        TCDBindReqDTO.setPlatformId("platformId");
        TCDBindReqDTO.setToken("token");

        final TcdCommonRespDTO expectedResult = new TcdCommonRespDTO();
        expectedResult.setCode(0);
        expectedResult.setMessage("FAILURE");

        // Run the test
        final TcdCommonRespDTO result = tcdAuthServiceImplUnderTest.doShopUnBindingTcd(TCDBindReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetItem() {
        // Setup
        final TcdItemMappingRespDTO tcdItemMappingRespDTO = new TcdItemMappingRespDTO();
        tcdItemMappingRespDTO.setId(0L);
        tcdItemMappingRespDTO.setName("name");
        final TcdDish tcdDish = new TcdDish();
        tcdDish.setId(0L);
        tcdDish.setName("name");
        tcdItemMappingRespDTO.setDishes(Arrays.asList(tcdDish));
        final List<TcdItemMappingRespDTO> expectedResult = Arrays.asList(tcdItemMappingRespDTO);

        // Run the test
        final List<TcdItemMappingRespDTO> result = tcdAuthServiceImplUnderTest.getItem("token");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testDoTcdItemBinding() {
        // Setup
        final TCDItemBindingReqDTO tcdItemBindingReqDTO = new TCDItemBindingReqDTO();
        tcdItemBindingReqDTO.setEnterpriseGuid("enterpriseGuid");
        tcdItemBindingReqDTO.setStoreGuid("storeGuid");
        tcdItemBindingReqDTO.setToken("token");
        tcdItemBindingReqDTO.setOperateType(0);
        final TcdDishSku tcdDishSku = new TcdDishSku();
        tcdItemBindingReqDTO.setDishSkus(Arrays.asList(tcdDishSku));

        // Run the test
        final String result = tcdAuthServiceImplUnderTest.doTcdItemBinding(tcdItemBindingReqDTO);

        // Verify the results
        assertEquals("FAILURE", result);
    }

    @Test
    public void testGetType() {
        // Setup
        final TcdItemMappingRespDTO tcdItemMappingRespDTO = new TcdItemMappingRespDTO();
        tcdItemMappingRespDTO.setId(0L);
        tcdItemMappingRespDTO.setName("name");
        final TcdDish tcdDish = new TcdDish();
        tcdDish.setId(0L);
        tcdDish.setName("name");
        tcdItemMappingRespDTO.setDishes(Arrays.asList(tcdDish));
        final List<TcdItemMappingRespDTO> expectedResult = Arrays.asList(tcdItemMappingRespDTO);

        // Run the test
        final List<TcdItemMappingRespDTO> result = tcdAuthServiceImplUnderTest.getType("token");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testUpdateDelivery() {
        // Setup
        final StoreAuthDTO storeAuthDTO = new StoreAuthDTO();
        storeAuthDTO.setTakeoutType(0);
        storeAuthDTO.setStoreGuid("storeGuid");
        storeAuthDTO.setShopName("name");
        storeAuthDTO.setBindingStatus(0);
        storeAuthDTO.setDeliveryType(0);

        // Run the test
        final Boolean result = tcdAuthServiceImplUnderTest.updateDelivery(storeAuthDTO);

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testGetTakeoutAuth() {
        // Setup
        final StoreAuthDTO storeAuthDTO = new StoreAuthDTO();
        storeAuthDTO.setTakeoutType(0);
        storeAuthDTO.setStoreGuid("storeGuid");
        storeAuthDTO.setShopName("name");
        storeAuthDTO.setBindingStatus(0);
        storeAuthDTO.setDeliveryType(0);

        final StoreAuthDTO expectedResult = new StoreAuthDTO();
        expectedResult.setTakeoutType(0);
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setShopName("name");
        expectedResult.setBindingStatus(0);
        expectedResult.setDeliveryType(0);

        // Run the test
        final StoreAuthDTO result = tcdAuthServiceImplUnderTest.getTakeoutAuth(storeAuthDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetToken() {
        assertEquals("-1", tcdAuthServiceImplUnderTest.getToken("storeGuid"));
    }

    @Test
    public void testGetTcdAuth() {
        // Setup
        final TcdAuthDO expectedResult = new TcdAuthDO();
        expectedResult.setId(0L);
        expectedResult.setGuid("e39d6a16-a888-400b-9391-fe156dc1966c");
        expectedResult.setEnterpriseGuid("enterpriseGuid");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setDeliveryType(0);
        expectedResult.setZcStoreId("id");
        expectedResult.setZcPlatformId("platformId");
        expectedResult.setShopName("name");
        expectedResult.setAccessToken("-1");
        expectedResult.setRefreshToken("-1");
        expectedResult.setActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setExpireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setDeleted(false);

        // Run the test
        final TcdAuthDO result = tcdAuthServiceImplUnderTest.getTcdAuth("storeGuid");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testCheckToken() {
        assertEquals("-1", tcdAuthServiceImplUnderTest.checkToken("storeGuid"));
    }

    @Test
    public void testGetTokens() {
        // Setup
        final TcdAuthDO tcdAuthDO = new TcdAuthDO();
        tcdAuthDO.setId(0L);
        tcdAuthDO.setGuid("e39d6a16-a888-400b-9391-fe156dc1966c");
        tcdAuthDO.setEnterpriseGuid("enterpriseGuid");
        tcdAuthDO.setStoreGuid("storeGuid");
        tcdAuthDO.setDeliveryType(0);
        tcdAuthDO.setZcStoreId("id");
        tcdAuthDO.setZcPlatformId("platformId");
        tcdAuthDO.setShopName("name");
        tcdAuthDO.setAccessToken("-1");
        tcdAuthDO.setRefreshToken("-1");
        tcdAuthDO.setActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tcdAuthDO.setExpireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tcdAuthDO.setDeleted(false);
        final List<TcdAuthDO> expectedResult = Arrays.asList(tcdAuthDO);

        // Run the test
        final List<TcdAuthDO> result = tcdAuthServiceImplUnderTest.getTokens(Arrays.asList("value"));

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testDiningOutTcd() {
        // Setup
        final TakeoutTCDDiningOutDTO takeoutTCDDiningOutDTO = new TakeoutTCDDiningOutDTO();
        takeoutTCDDiningOutDTO.setOrderSn("orderSn");
        takeoutTCDDiningOutDTO.setStoreGuid("storeGuid");
        takeoutTCDDiningOutDTO.setOrderState("orderState");
        takeoutTCDDiningOutDTO.setToken("token");

        final TcdCommonRespDTO expectedResult = new TcdCommonRespDTO();
        expectedResult.setCode(0);
        expectedResult.setMessage("FAILURE");

        // Run the test
        final TcdCommonRespDTO result = tcdAuthServiceImplUnderTest.diningOutTcd(takeoutTCDDiningOutDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testConfirmTheMealTcd() {
        // Setup
        final TakeoutTCDConfirmTheMealDTO takeoutTCDConfirmTheMealDTO = new TakeoutTCDConfirmTheMealDTO();
        takeoutTCDConfirmTheMealDTO.setOrderSn("orderSn");
        takeoutTCDConfirmTheMealDTO.setStoreGuid("storeGuid");
        takeoutTCDConfirmTheMealDTO.setWriteOffCode("writeOffCode");
        takeoutTCDConfirmTheMealDTO.setOrderState("FINISH");
        takeoutTCDConfirmTheMealDTO.setToken("token");

        final TcdCommonRespDTO expectedResult = new TcdCommonRespDTO();
        expectedResult.setCode(0);
        expectedResult.setMessage("FAILURE");

        // Run the test
        final TcdCommonRespDTO result = tcdAuthServiceImplUnderTest.confirmTheMealTcd(takeoutTCDConfirmTheMealDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testPickUpTcd() {
        // Setup
        final TakeoutTCDPickUpDTO takeoutTCDPickUpDTO = new TakeoutTCDPickUpDTO();
        takeoutTCDPickUpDTO.setOrderSn("orderSn");
        takeoutTCDPickUpDTO.setStoreGuid("storeGuid");
        takeoutTCDPickUpDTO.setWriteOffCode("writeOffCode");
        takeoutTCDPickUpDTO.setOrderState("FINISH");
        takeoutTCDPickUpDTO.setToken("token");

        final TcdCommonRespDTO expectedResult = new TcdCommonRespDTO();
        expectedResult.setCode(0);
        expectedResult.setMessage("FAILURE");

        // Run the test
        final TcdCommonRespDTO result = tcdAuthServiceImplUnderTest.pickUpTcd(takeoutTCDPickUpDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testRemoveByZcStoreId() {
        // Setup
        // Run the test
        tcdAuthServiceImplUnderTest.removeByZcStoreId("zcStoreId");

        // Verify the results
    }
}
