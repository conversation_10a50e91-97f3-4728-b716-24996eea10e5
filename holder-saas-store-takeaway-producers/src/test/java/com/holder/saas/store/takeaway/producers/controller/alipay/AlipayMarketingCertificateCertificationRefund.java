package com.holder.saas.store.takeaway.producers.controller.alipay;

import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.AlipayConfig;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.domain.AlipayMarketingCertificateCertificationRefundModel;
import com.alipay.api.request.AlipayMarketingCertificateCertificationRefundRequest;
import com.alipay.api.response.AlipayMarketingCertificateCertificationRefundResponse;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class AlipayMarketingCertificateCertificationRefund {

    public static void main(String[] args) throws AlipayApiException {
        String privateKey = "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCUzvsNaYrGrEQxjvJM7m3iIfyQoDcuivBbtSPbmAW0/scya6vOy95P7empqJZZkF9x4SFOkdr6Ckv7l5WCR6t6I2i7MmfXhkILXSHojuOjz6jOnQ1qKT65/O5YWQ5NLi7kUWQTcMJDKKiuYAlBC/mpXCJBU/mgfDXgvmcDRUjk0xppwrYuXxoRD37S9ZLWnYl3yqOe7aw4Ioop4eMRggu9LsKCBIbsNuXjDO6OkDuq3swEAU/EGlCw4jGjv62FY8F8a3GS+gl4qrap7L5DDce6vnTY1TO87wwLFl+8VNNqmWmA++k16N/Lveagu8a7NFL5Lr7kc1pRy886+1fhYIZhAgMBAAECggEAZOoo3uOgNTNF28XxE7Lt2djqirncMjgO4GJrOtRRqnjNZagXD1q6HMJfH6swqvR5haUDDWFkewTYmnol7f/kjiPNNoXsXgCycTprcGbWgZSmCdf/7Oqjm0Wn7UoXpMeZDUUU9QoW86xzGC4QmIgie3P71wm309noP81f1pv53xMxpWDLfv8R4gew6xCf3bxH3NEjH5pXGlHwT1vuk0pv71ELJQ7mGO3PH7pgTWjLT5TABdEBd9O3BjZa+DllCDrFL+u9FNf6lA1b4JzZkbWkKTc2i4Ad+8dtRSW3HpFu102gP/Jo8SC56+ehaxOGpsFdmyQoPJLAJY5yWGTgibWtAQKBgQDGvO44FYlGXpe3IfssNrcS672EjdpYov2S+RwQWw6KgvSWednBhn7TsS1e84dFm/G97xrGsFr6C5wi6/ZWwm+gFSnuxh4nnFaeG68r//Ol01XjI2VMK+QyMh7t9myK5ri10sjxUS0TpBPu1tzxwHzKidxWvBOMpu+lb6Ewe038qQKBgQC/rzZQ/EYNA9BaTbBbEV9edrXSf+/IanHhu4rrzthPbWq/6j/D/i1A3Y95EtCZwcl1y0IOLP4hNEbD0oLUHTQPGAnLPECYLYXLSlY2OYWateAmO8T15fAAyD7Gg27gTMkuLSKsfEhZLjixzL66Ge+z9HYTpFYlN/GFqZRk7J1W+QKBgQCZ+p3zlDQU8otH16s82XPE1CO1RRbsX/Rp4FpvDa3ZgZcx01z1BVjUec02aSbgtu95An3TfaYvX7lskTf+ho1oEZ24o4WPmSC/RaHWTGwhiflcj/sXJlaa0ZyHMMtuX06ziYoo9oUfV5weDBmJsWK+pkuyY+wdqOW6XyAbaR3GaQKBgE7P+aM+toavZMFcZABtmiq64HNWSv66VWycsbfW6jVuJZAW/nTVU0HyVwVO6RnvBag7FEPD2BFK7zWgnk4cW8VA+vXnJh/rx7EYNW5CJF7CHRFbTdZU3mNT8gupCrOKnsUvpawJxa11RbZFGr5l35q8Drhwv0K6R8HPQcZlSyPxAoGAWRqqYGYl0kx2YARfIKeKG8tqwEAp/NYzHpyNn5Vk8PajMshCDxb7tZ1a+4o7Fftr0USJGbK5eGxXynQc8ni3hL7hLebcYaM/dREmMb6H8TByv0oHfGz9BjEl5m7AQeuSRYFuEayAgQFr9Q/3FWimmQCEXCCQX/Z7+YegaMzzhds=";
        String alipayPublicKey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAhTIRtHd73U4kcNVrbiLtN8/BWfNMJFHyHg0gf7k4BUW9RgfZ2rslu48BhwLWpNLMtxwKgUErteEHLZE3DNv0fhvj3d2I/ZSpY26rKb5qnAOBe4j9cQG+fU8gS01A4B8wwkzzStpZhY6Hf6gZD/IyixZ4nC2u4ZI7p9KRDa92bkXpnKDGBpddFW+hYQVrR4q+5RiiLU/IBRvQumCwlfBE0N4FVRYwp5ggMY8nQNZfAv6vK2wOBc+9gZYaxXy2eXqhZmw13p/aQYJEGN1Hgem0OXCON+LC3e93SrevAczGsHccxFpi+IRK+D3AQbUILMwnXCpIcm12pkS07sG9SABTtwIDAQAB";
        AlipayConfig alipayConfig = new AlipayConfig();
        alipayConfig.setServerUrl("https://openapi.alipay.com/gateway.do");
        alipayConfig.setAppId("2021004125681141");
        alipayConfig.setPrivateKey(privateKey);
        alipayConfig.setFormat("json");
        alipayConfig.setAlipayPublicKey(alipayPublicKey);
        alipayConfig.setCharset("UTF-8");
        alipayConfig.setSignType("RSA2");
        AlipayClient alipayClient = new DefaultAlipayClient(alipayConfig);
        AlipayMarketingCertificateCertificationRefundRequest request = new AlipayMarketingCertificateCertificationRefundRequest();
        AlipayMarketingCertificateCertificationRefundModel model = new AlipayMarketingCertificateCertificationRefundModel();
        model.setOutBizNo("1002600620019090123143254436");
        model.setUserId("2088412993570073");
        List<String> useOrderNoList = new ArrayList<String>();
        useOrderNoList.add("2023052100445005835800000045");
        model.setUseOrderNoList(useOrderNoList);
        request.setBizModel(model);
        AlipayMarketingCertificateCertificationRefundResponse response = alipayClient.execute(request);
        log.info("Body={}", response.getBody());
        if (response.isSuccess()) {
            log.info("调用成功");
        } else {
            log.info("调用失败");
            // sdk版本是"4.38.0.ALL"及以上,可以参考下面的示例获取诊断链接
            // String diagnosisUrl = DiagnosisUtils.getDiagnosisUrl(response);
            // System.out.println(diagnosisUrl);
        }
    }
}
