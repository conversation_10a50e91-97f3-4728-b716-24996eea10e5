package com.holder.saas.store.takeaway.producers.service.job;

import com.holder.saas.store.takeaway.producers.service.EleAuthService;
import com.holder.saas.store.takeaway.producers.service.HolderAuthService;
import com.holder.saas.store.takeaway.producers.service.MtHeartbeatService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import static org.mockito.Mockito.verify;

@RunWith(MockitoJUnitRunner.class)
public class RefreshTokenJobTest {

    @Mock
    private EleAuthService mockEleAuthService;
    @Mock
    private MtHeartbeatService mockMtHeartbeatService;
    @Mock
    private HolderAuthService mockHolderAuthService;

    private RefreshTokenJob refreshTokenJobUnderTest;

    @Before
    public void setUp() throws Exception {
        refreshTokenJobUnderTest = new RefreshTokenJob(mockEleAuthService, mockMtHeartbeatService,
                mockHolderAuthService);
        ReflectionTestUtils.setField(refreshTokenJobUnderTest, "tokenRefreshAheadHours", 0);
        ReflectionTestUtils.setField(refreshTokenJobUnderTest, "ownTokenRefreshAheadHours", 0);
    }

    @Test
    public void testRefreshEleToken() {
        // Setup
        // Run the test
        refreshTokenJobUnderTest.refreshEleToken();

        // Verify the results
        verify(mockEleAuthService).refreshToken(0L);
    }

    @Test
    public void testRefreshOwnToken() {
        // Setup
        // Run the test
        refreshTokenJobUnderTest.refreshOwnToken();

        // Verify the results
        verify(mockHolderAuthService).refreshToken(0L);
    }

    @Test
    public void testHeartbeatReport() throws Exception {
        // Setup
        // Run the test
        refreshTokenJobUnderTest.heartbeatReport();

        // Verify the results
        verify(mockMtHeartbeatService).heartbeatReport();
    }
}
