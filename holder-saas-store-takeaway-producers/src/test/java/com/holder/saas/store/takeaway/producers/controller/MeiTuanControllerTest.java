package com.holder.saas.store.takeaway.producers.controller;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.holder.saas.store.takeaway.producers.HolderSaasStoreTakeawayProducersApplication;
import com.holder.saas.store.takeaway.producers.constant.TakeoutConstant;
import com.holder.saas.store.takeaway.producers.controller.util.JsonFileUtil;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.exception.GroupBuyException;
import com.holderzone.saas.store.dto.takeaway.MtReqItemMapping;
import com.holderzone.saas.store.dto.takeaway.MtReqSkuMapping;
import com.holderzone.saas.store.dto.takeaway.UnItemBindUnbindReq;
import com.holderzone.saas.store.dto.trade.req.RecordThirdActivityInfoReqDTO;
import com.sankuai.sjst.platform.developer.domain.RequestSysParams;
import com.sankuai.sjst.platform.developer.request.CipCaterTakeoutDishMapRequest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URISyntaxException;
import java.util.Collections;
import java.util.List;

import static com.holderzone.saas.store.dto.common.CommonConstant.USER_INFO;
import static org.junit.Assert.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * <AUTHOR>
 * @date 2023年09月11日 10:08
 * @description
 */
@Slf4j
@WebAppConfiguration
@ContextConfiguration
@AutoConfigureMockMvc
@RunWith(SpringRunner.class)
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
@SpringBootTest(classes = HolderSaasStoreTakeawayProducersApplication.class)
public class MeiTuanControllerTest {

    private static final String USERINFO = "{\"operSubjectGuid\": \"2010121440477930009\",\"enterpriseGuid\":" +
            " \"2009281531195930006\",\"enterpriseName\": \"赵氏企业\",\"enterpriseNo\": \"********\",\"storeGuid\":" +
            " \"2106221850429620006\",\"storeName\": \"交子大道测试门店\",\"storeNo\": \"5796807\",\"userGuid\": " +
            "\"6787561298847596545\",\"account\": \"196504\",\"tel\": \"***********\",\"name\": \"靓亮仔\"}\n";

    private static final String MT = "/mt";

    private static final String RESPONSE = "response:";

    @Autowired
    private WebApplicationContext wac;

    private MockMvc mockMvc;

    @Before
    public void setupMockMvc() {
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();
    }

    @Test
    public void memberConsumeCallback() throws UnsupportedEncodingException {
        RecordThirdActivityInfoReqDTO reqMemberConsumeCallbackDTO = JSON.parseObject(JsonFileUtil.read("mt/memberConsumeCallback.json"),
                RecordThirdActivityInfoReqDTO.class);
        String jsonMemberConsumeCallback = JSON.toJSONString(reqMemberConsumeCallbackDTO);
        MvcResult mvcMemberConsumeCallbackResult = null;
        try {
            mvcMemberConsumeCallbackResult = mockMvc.perform(post(MT + "/callback/member/consume")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(jsonMemberConsumeCallback))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new GroupBuyException(e.getMessage());
        }
        String contentAsString = mvcMemberConsumeCallbackResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    @Test
    public void memberNewCallback() throws UnsupportedEncodingException {
        RecordThirdActivityInfoReqDTO reqMemberNewCallbackDTO = JSON.parseObject(JsonFileUtil.read("mt/memberNewCallback.json"),
                RecordThirdActivityInfoReqDTO.class);
        String jsonMemberNewCallback = JSON.toJSONString(reqMemberNewCallbackDTO);
        MvcResult mvcMemberNewCallbackResult = null;
        try {
            mvcMemberNewCallbackResult = mockMvc.perform(post(MT + "/callback/member/new")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(jsonMemberNewCallback))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new GroupBuyException(e.getMessage());
        }
        String contentAsString = mvcMemberNewCallbackResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    @Test
    public void mtWmBind() {
        String authToken = "1ecc8991c48f94b9e6cd6a1966dee2d2888f6cdb4f863191ee94c118d1839a9b0a0891c1f1bdcf216981e811932c6cc6";
        String mtSignKey = "5jydvc7w8ee8fx7w";
        String storeGuid = "4897";
        UnItemBindUnbindReq unItemBindUnbindReq = new UnItemBindUnbindReq();
        unItemBindUnbindReq.setUnItemId("9776982644");
        unItemBindUnbindReq.setUnItemSkuId("13480993024");
        unItemBindUnbindReq.setUnItemTypeId("饮品");
        unItemBindUnbindReq.setErpItemGuid("6977553257207955458");
        unItemBindUnbindReq.setErpItemSkuId("2309220932547680001");

        List<MtReqSkuMapping> waiMaiDishSkuMappings = Lists.newArrayList();

        MtReqSkuMapping mtReqSkuMapping1 = new MtReqSkuMapping();
        // AppFoodCode -> unItemBindUnbindReq.getErpItemGuid()
        mtReqSkuMapping1.setDishSkuId(unItemBindUnbindReq.getUnItemSkuId());
        mtReqSkuMapping1.setEDishSkuCode(unItemBindUnbindReq.getErpItemSkuId());
        waiMaiDishSkuMappings.add(mtReqSkuMapping1);


        CipCaterTakeoutDishMapRequest request = new CipCaterTakeoutDishMapRequest();
        RequestSysParams requestSysParams = new RequestSysParams(mtSignKey, authToken, TakeoutConstant.CHARSET_UTF_8);
        request.setRequestSysParams(requestSysParams);
        request.setePoiId(storeGuid);
        MtReqItemMapping mtReqItemMapping = new MtReqItemMapping();
        // AppFoodCode -> unItemBindUnbindReq.getErpItemGuid()
        mtReqItemMapping.setDishId(unItemBindUnbindReq.getUnItemId());

        mtReqItemMapping.setWaiMaiDishSkuMappings(waiMaiDishSkuMappings);
        List<MtReqItemMapping> mtReqItemMappings = Collections.singletonList(mtReqItemMapping);
        request.setDishMappings(JacksonUtils.writeValueAsString(mtReqItemMappings));

        log.info("request={}", JacksonUtils.writeValueAsString(request));
        try {
            String result = request.doRequest();
            log.info("result={}", JacksonUtils.writeValueAsString(result));
        } catch (IOException | URISyntaxException e) {
            log.info("error={}", e.getMessage());
        }
    }

}