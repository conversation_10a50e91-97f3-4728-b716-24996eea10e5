package com.holder.saas.store.takeaway.producers.service.impl;

import com.holder.saas.store.takeaway.producers.service.HolderAuthService;
import com.holder.saas.store.takeaway.producers.service.UnOrderMqService;
import com.holder.saas.store.takeaway.producers.service.rpc.ConsumersFeignService;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.takeaway.OwnApiResult;
import com.holderzone.saas.store.dto.takeaway.OwnCallbackResponse;
import com.holderzone.saas.store.dto.takeaway.UnOrder;
import com.holderzone.saas.store.dto.takeaway.request.SalesUpdateDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.LocalDateTime;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class OwnOrderReplyServiceImplTest {

    @Mock
    private HolderAuthService mockHolderAuthService;
    @Mock
    private UnOrderMqService mockUnOrderMqService;
    @Mock
    private ConsumersFeignService mockConsumersFeignService;

    private OwnOrderReplyServiceImpl ownOrderReplyServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        ownOrderReplyServiceImplUnderTest = new OwnOrderReplyServiceImpl(mockHolderAuthService, mockUnOrderMqService,
                mockConsumersFeignService);
        ReflectionTestUtils.setField(ownOrderReplyServiceImplUnderTest, "orderUpdate", "orderUpdate");
        ReflectionTestUtils.setField(ownOrderReplyServiceImplUnderTest, "url", "url");
        ReflectionTestUtils.setField(ownOrderReplyServiceImplUnderTest, "itemurl", "itemurl");
        ReflectionTestUtils.setField(ownOrderReplyServiceImplUnderTest, "appId", "appId");
        ReflectionTestUtils.setField(ownOrderReplyServiceImplUnderTest, "mtSignKey", "mtSignKey");
    }

    @Test
    public void testReplyCancelOrder() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setCbMsgType(0);
        unOrder.setReplyMsgType(0);
        unOrder.setOrderType(0);
        unOrder.setOrderSubType(0);
        unOrder.setEnterpriseGuid("enterpriseGuid");
        unOrder.setStoreGuid("storeGuid");
        unOrder.setOrderId("orderId");
        unOrder.setAcceptTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setDistributionType(0);

        // Configure ConsumersFeignService.orderUpdate(...).
        final SalesUpdateDTO salesUpdateDTO = new SalesUpdateDTO();
        salesUpdateDTO.setOrderID(0L);
        salesUpdateDTO.setOrderStatus(0);
        salesUpdateDTO.setDistributionType(0);
        salesUpdateDTO.setStoreGuid("storeGuid");
        when(mockConsumersFeignService.orderUpdate(salesUpdateDTO)).thenReturn(new OwnCallbackResponse(0, "message"));

        // Run the test
        ownOrderReplyServiceImplUnderTest.replyCancelOrder(unOrder);

        // Verify the results
        // Confirm UnOrderMqService.sendUnOrder(...).
        final UnOrder unorder = new UnOrder();
        unorder.setCbMsgType(0);
        unorder.setReplyMsgType(0);
        unorder.setOrderType(0);
        unorder.setOrderSubType(0);
        unorder.setEnterpriseGuid("enterpriseGuid");
        unorder.setStoreGuid("storeGuid");
        unorder.setOrderId("orderId");
        unorder.setAcceptTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unorder.setDistributionType(0);
        verify(mockUnOrderMqService).sendUnOrder(unorder);
    }

    @Test
    public void testReplyConfirmOrder() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setCbMsgType(0);
        unOrder.setReplyMsgType(0);
        unOrder.setOrderType(0);
        unOrder.setOrderSubType(0);
        unOrder.setEnterpriseGuid("enterpriseGuid");
        unOrder.setStoreGuid("storeGuid");
        unOrder.setOrderId("orderId");
        unOrder.setAcceptTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setDistributionType(0);

        // Configure ConsumersFeignService.orderUpdate(...).
        final SalesUpdateDTO salesUpdateDTO = new SalesUpdateDTO();
        salesUpdateDTO.setOrderID(0L);
        salesUpdateDTO.setOrderStatus(0);
        salesUpdateDTO.setDistributionType(0);
        salesUpdateDTO.setStoreGuid("storeGuid");
        when(mockConsumersFeignService.orderUpdate(salesUpdateDTO)).thenReturn(new OwnCallbackResponse(0, "message"));

        // Run the test
        ownOrderReplyServiceImplUnderTest.replyConfirmOrder(unOrder);

        // Verify the results
        // Confirm UnOrderMqService.sendUnOrder(...).
        final UnOrder unorder = new UnOrder();
        unorder.setCbMsgType(0);
        unorder.setReplyMsgType(0);
        unorder.setOrderType(0);
        unorder.setOrderSubType(0);
        unorder.setEnterpriseGuid("enterpriseGuid");
        unorder.setStoreGuid("storeGuid");
        unorder.setOrderId("orderId");
        unorder.setAcceptTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unorder.setDistributionType(0);
        verify(mockUnOrderMqService).sendUnOrder(unorder);
    }

    @Test
    public void testReplyAgreeCancelOrder() {
        ownOrderReplyServiceImplUnderTest.replyAgreeCancelOrder(new UnOrder());
    }

    @Test
    public void testReplyDisagreeCancelOrder() {
        ownOrderReplyServiceImplUnderTest.replyDisagreeCancelOrder(new UnOrder());
    }

    @Test
    public void testReplyAgreeRefundOrder() {
        ownOrderReplyServiceImplUnderTest.replyAgreeRefundOrder(new UnOrder());
    }

    @Test
    public void testReplyDisagreeRefundOrder() {
        ownOrderReplyServiceImplUnderTest.replyDisagreeRefundOrder(new UnOrder());
    }

    @Test
    public void testStartDelivery() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setCbMsgType(0);
        unOrder.setReplyMsgType(0);
        unOrder.setOrderType(0);
        unOrder.setOrderSubType(0);
        unOrder.setEnterpriseGuid("enterpriseGuid");
        unOrder.setStoreGuid("storeGuid");
        unOrder.setOrderId("orderId");
        unOrder.setAcceptTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setDistributionType(0);

        // Run the test
        ownOrderReplyServiceImplUnderTest.startDelivery(unOrder);

        // Verify the results
    }

    @Test
    public void testStartDeliveryMQ() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setCbMsgType(0);
        unOrder.setReplyMsgType(0);
        unOrder.setOrderType(0);
        unOrder.setOrderSubType(0);
        unOrder.setEnterpriseGuid("enterpriseGuid");
        unOrder.setStoreGuid("storeGuid");
        unOrder.setOrderId("orderId");
        unOrder.setAcceptTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setDistributionType(0);

        final OwnApiResult expectedResult = new OwnApiResult();
        expectedResult.setCode(0);
        expectedResult.setStatus("status");
        expectedResult.setMsg("自营外卖平台暂未实现一城飞客配送");
        expectedResult.setResult("result");
        expectedResult.setErrorCode(0);

        // Run the test
        final OwnApiResult result = ownOrderReplyServiceImplUnderTest.startDeliveryMQ(unOrder);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test(expected = BusinessException.class)
    public void testCancelDelivery() {
        ownOrderReplyServiceImplUnderTest.cancelDelivery(new UnOrder());
    }

    @Test(expected = BusinessException.class)
    public void testReplyUrgeOrder() {
        ownOrderReplyServiceImplUnderTest.replyUrgeOrder(new UnOrder());
    }

    @Test(expected = BusinessException.class)
    public void testReplyDeliveryAccept() {
        ownOrderReplyServiceImplUnderTest.replyDeliveryAccept(new UnOrder());
    }

    @Test(expected = BusinessException.class)
    public void testReplyDeliveryStart() {
        ownOrderReplyServiceImplUnderTest.replyDeliveryStart(new UnOrder());
    }

    @Test(expected = BusinessException.class)
    public void testReplyDeliveryCancel() {
        ownOrderReplyServiceImplUnderTest.replyDeliveryCancel(new UnOrder());
    }

    @Test(expected = BusinessException.class)
    public void testReplyDeliveryComplete() {
        ownOrderReplyServiceImplUnderTest.replyDeliveryComplete(new UnOrder());
    }

    @Test(expected = BusinessException.class)
    public void testReplyRiderPosition() {
        ownOrderReplyServiceImplUnderTest.replyRiderPosition(new UnOrder());
    }
}
