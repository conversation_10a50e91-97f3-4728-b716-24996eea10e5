package com.holder.saas.store.takeaway.producers.controller;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(DevController.class)
public class DevControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Test
    public void testEleOrderCheck() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(get("/ele/callback/order")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testMtOrderCheck() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(get("/mt/callback/order/{path}", "path")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testOrderPrivacyCheck() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(get("/mt/callback/privacy_degrade")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testBindCheck() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(get("/mt/callback/bind")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testUnbindCheck() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(get("/mt/callback/unbind")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testHeartbeatCheck() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(get("/mt/callback/heartbeat")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }
}
