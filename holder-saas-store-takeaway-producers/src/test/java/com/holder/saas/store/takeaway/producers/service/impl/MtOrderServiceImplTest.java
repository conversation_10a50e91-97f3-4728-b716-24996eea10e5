package com.holder.saas.store.takeaway.producers.service.impl;

import com.holder.saas.store.takeaway.producers.entity.domain.MtAuthDO;
import com.holder.saas.store.takeaway.producers.service.MtAuthService;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.takeaway.UnOrder;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class MtOrderServiceImplTest {

    @Mock
    private MtAuthService mockAuthService;

    @InjectMocks
    private MtOrderServiceImpl mtOrderServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        ReflectionTestUtils.setField(mtOrderServiceImplUnderTest, "mtDeveloperId", 0L);
        ReflectionTestUtils.setField(mtOrderServiceImplUnderTest, "mtSignKey", "mtSignKey");
    }

    @Test
    public void testGetRealRecipientAddress() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setOrderStatus(0);
        unOrder.setShopId(0L);
        unOrder.setShopName("shopName");
        unOrder.setStoreGuid("storeGuid");
        unOrder.setOrderId("orderId");

        // Configure MtAuthService.getAuth(...).
        final MtAuthDO mtAuthDO = new MtAuthDO();
        mtAuthDO.setId(0);
        mtAuthDO.setGuid("1047c60b-9d8c-4c2e-80a5-a0f75a46a522");
        mtAuthDO.setEPoiId("ePoiId");
        mtAuthDO.setEnterpriseGuid("enterpriseGuid");
        mtAuthDO.setAccessToken("accessToken");
        when(mockAuthService.getAuth("storeGuid", 0)).thenReturn(mtAuthDO);

        // Run the test
        final String result = mtOrderServiceImplUnderTest.getRealRecipientAddress(unOrder);

        // Verify the results
        assertEquals("result", result);
    }

    @Test(expected = BusinessException.class)
    public void testGetRealRecipientAddress_MtAuthServiceReturnsNull() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setOrderStatus(0);
        unOrder.setShopId(0L);
        unOrder.setShopName("shopName");
        unOrder.setStoreGuid("storeGuid");
        unOrder.setOrderId("orderId");

        when(mockAuthService.getAuth("storeGuid", 0)).thenReturn(null);

        // Run the test
        mtOrderServiceImplUnderTest.getRealRecipientAddress(unOrder);
    }
}
