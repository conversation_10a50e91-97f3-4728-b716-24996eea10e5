package com.holder.saas.store.takeaway.producers.service.impl;

import com.holderzone.saas.store.dto.takeaway.OwnApiResult;
import com.holderzone.saas.store.dto.takeaway.UnOrder;
import com.holderzone.saas.store.dto.takeaway.request.FoodListDTO;
import org.junit.Before;
import org.junit.Test;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;

import static org.junit.Assert.assertEquals;

public class TcdOrderReplyServiceImplTest {

    private TcdOrderReplyServiceImpl tcdOrderReplyServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        tcdOrderReplyServiceImplUnderTest = new TcdOrderReplyServiceImpl();
        ReflectionTestUtils.setField(tcdOrderReplyServiceImplUnderTest, "zcUrl", "zcUrl");
        ReflectionTestUtils.setField(tcdOrderReplyServiceImplUnderTest, "orderAcceptOrRefuseUrl",
                "orderAcceptOrRefuseUrl");
        ReflectionTestUtils.setField(tcdOrderReplyServiceImplUnderTest, "orderAgreeOrDisagreeUrl",
                "orderAgreeOrDisagreeUrl");
        ReflectionTestUtils.setField(tcdOrderReplyServiceImplUnderTest, "deliveryUrl", "deliveryUrl");
        ReflectionTestUtils.setField(tcdOrderReplyServiceImplUnderTest, "deliveryAcceptUrl", "deliveryAcceptUrl");
        ReflectionTestUtils.setField(tcdOrderReplyServiceImplUnderTest, "updatePersonUrl", "updatePersonUrl");
        ReflectionTestUtils.setField(tcdOrderReplyServiceImplUnderTest, "deliveryFinishUrl", "deliveryFinishUrl");
        ReflectionTestUtils.setField(tcdOrderReplyServiceImplUnderTest, "knightAcceptUrl", "knightAcceptUrl");
        ReflectionTestUtils.setField(tcdOrderReplyServiceImplUnderTest, "cancelDeliveryUrl", "cancelDeliveryUrl");
    }

    @Test
    public void testReplyCancelOrder() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setShopName("ShopName");
        unOrder.setReplyMsgType(0);
        unOrder.setOrderSubType(0);
        unOrder.setStoreGuid("ShopNo");
        unOrder.setOrderId("orderId");
        unOrder.setOrderDaySn("orderDaySn");
        unOrder.setOrderRemark("orderRemark");
        unOrder.setReserve(false);
        unOrder.setCustomerName("customerName");
        unOrder.setCustomerPhone("customerPhone");
        unOrder.setPrivacyPhone("privacyPhone");
        unOrder.setCustomerAddress("customerAddress");
        unOrder.setShipLatitude("ReceiverLat");
        unOrder.setShipLongitude("ReceiverLng");
        unOrder.setShipperName("deliveryPersonName");
        unOrder.setShipperPhone("deliveryPersonPhone");
        unOrder.setInvoiced(false);
        unOrder.setInvoiceType(0);
        unOrder.setInvoiceTitle("invoiceTitle");
        unOrder.setTaxpayerId("taxpayerId");
        unOrder.setTotal(new BigDecimal("0.00"));
        unOrder.setShipTotal(new BigDecimal("0.00"));
        unOrder.setPackageTotal(new BigDecimal("0.00"));
        unOrder.setDiscountTotal(new BigDecimal("0.00"));
        unOrder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unOrder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setRefundReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setCancelReason("cancelReason");
        final FoodListDTO foodListDTO = new FoodListDTO();
        unOrder.setFoodLists(Arrays.asList(foodListDTO));

        // Run the test
        tcdOrderReplyServiceImplUnderTest.replyCancelOrder(unOrder);

        // Verify the results
    }

    @Test
    public void testReplyConfirmOrder() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setShopName("ShopName");
        unOrder.setReplyMsgType(0);
        unOrder.setOrderSubType(0);
        unOrder.setStoreGuid("ShopNo");
        unOrder.setOrderId("orderId");
        unOrder.setOrderDaySn("orderDaySn");
        unOrder.setOrderRemark("orderRemark");
        unOrder.setReserve(false);
        unOrder.setCustomerName("customerName");
        unOrder.setCustomerPhone("customerPhone");
        unOrder.setPrivacyPhone("privacyPhone");
        unOrder.setCustomerAddress("customerAddress");
        unOrder.setShipLatitude("ReceiverLat");
        unOrder.setShipLongitude("ReceiverLng");
        unOrder.setShipperName("deliveryPersonName");
        unOrder.setShipperPhone("deliveryPersonPhone");
        unOrder.setInvoiced(false);
        unOrder.setInvoiceType(0);
        unOrder.setInvoiceTitle("invoiceTitle");
        unOrder.setTaxpayerId("taxpayerId");
        unOrder.setTotal(new BigDecimal("0.00"));
        unOrder.setShipTotal(new BigDecimal("0.00"));
        unOrder.setPackageTotal(new BigDecimal("0.00"));
        unOrder.setDiscountTotal(new BigDecimal("0.00"));
        unOrder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unOrder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setRefundReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setCancelReason("cancelReason");
        final FoodListDTO foodListDTO = new FoodListDTO();
        unOrder.setFoodLists(Arrays.asList(foodListDTO));

        // Run the test
        tcdOrderReplyServiceImplUnderTest.replyConfirmOrder(unOrder);

        // Verify the results
    }

    @Test
    public void testReplyAgreeCancelOrder() {
        tcdOrderReplyServiceImplUnderTest.replyAgreeCancelOrder(new UnOrder());
    }

    @Test
    public void testReplyDisagreeCancelOrder() {
        tcdOrderReplyServiceImplUnderTest.replyDisagreeCancelOrder(new UnOrder());
    }

    @Test
    public void testReplyAgreeRefundOrder() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setShopName("ShopName");
        unOrder.setReplyMsgType(0);
        unOrder.setOrderSubType(0);
        unOrder.setStoreGuid("ShopNo");
        unOrder.setOrderId("orderId");
        unOrder.setOrderDaySn("orderDaySn");
        unOrder.setOrderRemark("orderRemark");
        unOrder.setReserve(false);
        unOrder.setCustomerName("customerName");
        unOrder.setCustomerPhone("customerPhone");
        unOrder.setPrivacyPhone("privacyPhone");
        unOrder.setCustomerAddress("customerAddress");
        unOrder.setShipLatitude("ReceiverLat");
        unOrder.setShipLongitude("ReceiverLng");
        unOrder.setShipperName("deliveryPersonName");
        unOrder.setShipperPhone("deliveryPersonPhone");
        unOrder.setInvoiced(false);
        unOrder.setInvoiceType(0);
        unOrder.setInvoiceTitle("invoiceTitle");
        unOrder.setTaxpayerId("taxpayerId");
        unOrder.setTotal(new BigDecimal("0.00"));
        unOrder.setShipTotal(new BigDecimal("0.00"));
        unOrder.setPackageTotal(new BigDecimal("0.00"));
        unOrder.setDiscountTotal(new BigDecimal("0.00"));
        unOrder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unOrder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setRefundReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setCancelReason("cancelReason");
        final FoodListDTO foodListDTO = new FoodListDTO();
        unOrder.setFoodLists(Arrays.asList(foodListDTO));

        // Run the test
        tcdOrderReplyServiceImplUnderTest.replyAgreeRefundOrder(unOrder);

        // Verify the results
    }

    @Test
    public void testReplyDisagreeRefundOrder() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setShopName("ShopName");
        unOrder.setReplyMsgType(0);
        unOrder.setOrderSubType(0);
        unOrder.setStoreGuid("ShopNo");
        unOrder.setOrderId("orderId");
        unOrder.setOrderDaySn("orderDaySn");
        unOrder.setOrderRemark("orderRemark");
        unOrder.setReserve(false);
        unOrder.setCustomerName("customerName");
        unOrder.setCustomerPhone("customerPhone");
        unOrder.setPrivacyPhone("privacyPhone");
        unOrder.setCustomerAddress("customerAddress");
        unOrder.setShipLatitude("ReceiverLat");
        unOrder.setShipLongitude("ReceiverLng");
        unOrder.setShipperName("deliveryPersonName");
        unOrder.setShipperPhone("deliveryPersonPhone");
        unOrder.setInvoiced(false);
        unOrder.setInvoiceType(0);
        unOrder.setInvoiceTitle("invoiceTitle");
        unOrder.setTaxpayerId("taxpayerId");
        unOrder.setTotal(new BigDecimal("0.00"));
        unOrder.setShipTotal(new BigDecimal("0.00"));
        unOrder.setPackageTotal(new BigDecimal("0.00"));
        unOrder.setDiscountTotal(new BigDecimal("0.00"));
        unOrder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unOrder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setRefundReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setCancelReason("cancelReason");
        final FoodListDTO foodListDTO = new FoodListDTO();
        unOrder.setFoodLists(Arrays.asList(foodListDTO));

        // Run the test
        tcdOrderReplyServiceImplUnderTest.replyDisagreeRefundOrder(unOrder);

        // Verify the results
    }

    @Test
    public void testReplyUrgeOrder() {
        tcdOrderReplyServiceImplUnderTest.replyUrgeOrder(new UnOrder());
    }

    @Test
    public void testStartDelivery() {
        tcdOrderReplyServiceImplUnderTest.startDelivery(new UnOrder());
    }

    @Test
    public void testStartDeliveryMQ() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setShopName("ShopName");
        unOrder.setReplyMsgType(0);
        unOrder.setOrderSubType(0);
        unOrder.setStoreGuid("ShopNo");
        unOrder.setOrderId("orderId");
        unOrder.setOrderDaySn("orderDaySn");
        unOrder.setOrderRemark("orderRemark");
        unOrder.setReserve(false);
        unOrder.setCustomerName("customerName");
        unOrder.setCustomerPhone("customerPhone");
        unOrder.setPrivacyPhone("privacyPhone");
        unOrder.setCustomerAddress("customerAddress");
        unOrder.setShipLatitude("ReceiverLat");
        unOrder.setShipLongitude("ReceiverLng");
        unOrder.setShipperName("deliveryPersonName");
        unOrder.setShipperPhone("deliveryPersonPhone");
        unOrder.setInvoiced(false);
        unOrder.setInvoiceType(0);
        unOrder.setInvoiceTitle("invoiceTitle");
        unOrder.setTaxpayerId("taxpayerId");
        unOrder.setTotal(new BigDecimal("0.00"));
        unOrder.setShipTotal(new BigDecimal("0.00"));
        unOrder.setPackageTotal(new BigDecimal("0.00"));
        unOrder.setDiscountTotal(new BigDecimal("0.00"));
        unOrder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unOrder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setRefundReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setCancelReason("cancelReason");
        final FoodListDTO foodListDTO = new FoodListDTO();
        unOrder.setFoodLists(Arrays.asList(foodListDTO));

        final OwnApiResult expectedResult = new OwnApiResult();
        expectedResult.setCode(0);
        expectedResult.setStatus("status");
        expectedResult.setMsg("message");
        expectedResult.setResult("result");
        expectedResult.setErrorCode(0);

        // Run the test
        final OwnApiResult result = tcdOrderReplyServiceImplUnderTest.startDeliveryMQ(unOrder);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testCancelDelivery() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setShopName("ShopName");
        unOrder.setReplyMsgType(0);
        unOrder.setOrderSubType(0);
        unOrder.setStoreGuid("ShopNo");
        unOrder.setOrderId("orderId");
        unOrder.setOrderDaySn("orderDaySn");
        unOrder.setOrderRemark("orderRemark");
        unOrder.setReserve(false);
        unOrder.setCustomerName("customerName");
        unOrder.setCustomerPhone("customerPhone");
        unOrder.setPrivacyPhone("privacyPhone");
        unOrder.setCustomerAddress("customerAddress");
        unOrder.setShipLatitude("ReceiverLat");
        unOrder.setShipLongitude("ReceiverLng");
        unOrder.setShipperName("deliveryPersonName");
        unOrder.setShipperPhone("deliveryPersonPhone");
        unOrder.setInvoiced(false);
        unOrder.setInvoiceType(0);
        unOrder.setInvoiceTitle("invoiceTitle");
        unOrder.setTaxpayerId("taxpayerId");
        unOrder.setTotal(new BigDecimal("0.00"));
        unOrder.setShipTotal(new BigDecimal("0.00"));
        unOrder.setPackageTotal(new BigDecimal("0.00"));
        unOrder.setDiscountTotal(new BigDecimal("0.00"));
        unOrder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unOrder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setRefundReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setCancelReason("cancelReason");
        final FoodListDTO foodListDTO = new FoodListDTO();
        unOrder.setFoodLists(Arrays.asList(foodListDTO));

        // Run the test
        tcdOrderReplyServiceImplUnderTest.cancelDelivery(unOrder);

        // Verify the results
    }

    @Test
    public void testReplyDeliveryAccept() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setShopName("ShopName");
        unOrder.setReplyMsgType(0);
        unOrder.setOrderSubType(0);
        unOrder.setStoreGuid("ShopNo");
        unOrder.setOrderId("orderId");
        unOrder.setOrderDaySn("orderDaySn");
        unOrder.setOrderRemark("orderRemark");
        unOrder.setReserve(false);
        unOrder.setCustomerName("customerName");
        unOrder.setCustomerPhone("customerPhone");
        unOrder.setPrivacyPhone("privacyPhone");
        unOrder.setCustomerAddress("customerAddress");
        unOrder.setShipLatitude("ReceiverLat");
        unOrder.setShipLongitude("ReceiverLng");
        unOrder.setShipperName("deliveryPersonName");
        unOrder.setShipperPhone("deliveryPersonPhone");
        unOrder.setInvoiced(false);
        unOrder.setInvoiceType(0);
        unOrder.setInvoiceTitle("invoiceTitle");
        unOrder.setTaxpayerId("taxpayerId");
        unOrder.setTotal(new BigDecimal("0.00"));
        unOrder.setShipTotal(new BigDecimal("0.00"));
        unOrder.setPackageTotal(new BigDecimal("0.00"));
        unOrder.setDiscountTotal(new BigDecimal("0.00"));
        unOrder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unOrder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setRefundReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setCancelReason("cancelReason");
        final FoodListDTO foodListDTO = new FoodListDTO();
        unOrder.setFoodLists(Arrays.asList(foodListDTO));

        // Run the test
        tcdOrderReplyServiceImplUnderTest.replyDeliveryAccept(unOrder);

        // Verify the results
    }

    @Test
    public void testReplyDeliveryStart() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setShopName("ShopName");
        unOrder.setReplyMsgType(0);
        unOrder.setOrderSubType(0);
        unOrder.setStoreGuid("ShopNo");
        unOrder.setOrderId("orderId");
        unOrder.setOrderDaySn("orderDaySn");
        unOrder.setOrderRemark("orderRemark");
        unOrder.setReserve(false);
        unOrder.setCustomerName("customerName");
        unOrder.setCustomerPhone("customerPhone");
        unOrder.setPrivacyPhone("privacyPhone");
        unOrder.setCustomerAddress("customerAddress");
        unOrder.setShipLatitude("ReceiverLat");
        unOrder.setShipLongitude("ReceiverLng");
        unOrder.setShipperName("deliveryPersonName");
        unOrder.setShipperPhone("deliveryPersonPhone");
        unOrder.setInvoiced(false);
        unOrder.setInvoiceType(0);
        unOrder.setInvoiceTitle("invoiceTitle");
        unOrder.setTaxpayerId("taxpayerId");
        unOrder.setTotal(new BigDecimal("0.00"));
        unOrder.setShipTotal(new BigDecimal("0.00"));
        unOrder.setPackageTotal(new BigDecimal("0.00"));
        unOrder.setDiscountTotal(new BigDecimal("0.00"));
        unOrder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unOrder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setRefundReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setCancelReason("cancelReason");
        final FoodListDTO foodListDTO = new FoodListDTO();
        unOrder.setFoodLists(Arrays.asList(foodListDTO));

        // Run the test
        tcdOrderReplyServiceImplUnderTest.replyDeliveryStart(unOrder);

        // Verify the results
    }

    @Test
    public void testReplyDeliveryCancel() {
        tcdOrderReplyServiceImplUnderTest.replyDeliveryCancel(new UnOrder());
    }

    @Test
    public void testReplyDeliveryComplete() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setShopName("ShopName");
        unOrder.setReplyMsgType(0);
        unOrder.setOrderSubType(0);
        unOrder.setStoreGuid("ShopNo");
        unOrder.setOrderId("orderId");
        unOrder.setOrderDaySn("orderDaySn");
        unOrder.setOrderRemark("orderRemark");
        unOrder.setReserve(false);
        unOrder.setCustomerName("customerName");
        unOrder.setCustomerPhone("customerPhone");
        unOrder.setPrivacyPhone("privacyPhone");
        unOrder.setCustomerAddress("customerAddress");
        unOrder.setShipLatitude("ReceiverLat");
        unOrder.setShipLongitude("ReceiverLng");
        unOrder.setShipperName("deliveryPersonName");
        unOrder.setShipperPhone("deliveryPersonPhone");
        unOrder.setInvoiced(false);
        unOrder.setInvoiceType(0);
        unOrder.setInvoiceTitle("invoiceTitle");
        unOrder.setTaxpayerId("taxpayerId");
        unOrder.setTotal(new BigDecimal("0.00"));
        unOrder.setShipTotal(new BigDecimal("0.00"));
        unOrder.setPackageTotal(new BigDecimal("0.00"));
        unOrder.setDiscountTotal(new BigDecimal("0.00"));
        unOrder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unOrder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setRefundReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setCancelReason("cancelReason");
        final FoodListDTO foodListDTO = new FoodListDTO();
        unOrder.setFoodLists(Arrays.asList(foodListDTO));

        // Run the test
        tcdOrderReplyServiceImplUnderTest.replyDeliveryComplete(unOrder);

        // Verify the results
    }

    @Test
    public void testReplyRiderPosition() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setShopName("ShopName");
        unOrder.setReplyMsgType(0);
        unOrder.setOrderSubType(0);
        unOrder.setStoreGuid("ShopNo");
        unOrder.setOrderId("orderId");
        unOrder.setOrderDaySn("orderDaySn");
        unOrder.setOrderRemark("orderRemark");
        unOrder.setReserve(false);
        unOrder.setCustomerName("customerName");
        unOrder.setCustomerPhone("customerPhone");
        unOrder.setPrivacyPhone("privacyPhone");
        unOrder.setCustomerAddress("customerAddress");
        unOrder.setShipLatitude("ReceiverLat");
        unOrder.setShipLongitude("ReceiverLng");
        unOrder.setShipperName("deliveryPersonName");
        unOrder.setShipperPhone("deliveryPersonPhone");
        unOrder.setInvoiced(false);
        unOrder.setInvoiceType(0);
        unOrder.setInvoiceTitle("invoiceTitle");
        unOrder.setTaxpayerId("taxpayerId");
        unOrder.setTotal(new BigDecimal("0.00"));
        unOrder.setShipTotal(new BigDecimal("0.00"));
        unOrder.setPackageTotal(new BigDecimal("0.00"));
        unOrder.setDiscountTotal(new BigDecimal("0.00"));
        unOrder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unOrder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setRefundReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setCancelReason("cancelReason");
        final FoodListDTO foodListDTO = new FoodListDTO();
        unOrder.setFoodLists(Arrays.asList(foodListDTO));

        // Run the test
        tcdOrderReplyServiceImplUnderTest.replyRiderPosition(unOrder);

        // Verify the results
    }
}
