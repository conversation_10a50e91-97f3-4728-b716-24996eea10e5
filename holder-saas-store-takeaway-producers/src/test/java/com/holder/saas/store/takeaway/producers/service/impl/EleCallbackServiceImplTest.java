package com.holder.saas.store.takeaway.producers.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.holder.saas.store.takeaway.producers.entity.domain.EleAuthDO;
import com.holder.saas.store.takeaway.producers.service.EleAuthService;
import com.holder.saas.store.takeaway.producers.service.EleUnOrderParser;
import com.holder.saas.store.takeaway.producers.service.UnOrderMqService;
import com.holder.saas.store.takeaway.producers.service.rpc.OrganizationService;
import com.holderzone.saas.store.dto.takeaway.UnOrder;
import eleme.openapi.sdk.api.entity.other.OMessage;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class EleCallbackServiceImplTest {

    @Mock
    private EleAuthService mockEleAuthService;
    @Mock
    private EleUnOrderParser mockEleUnOrderParse;
    @Mock
    private UnOrderMqService mockUnOrderMqService;
    @Mock
    private OrganizationService mockOrganizationService;

    private EleCallbackServiceImpl eleCallbackServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        eleCallbackServiceImplUnderTest = new EleCallbackServiceImpl(mockEleAuthService, mockEleUnOrderParse,
                mockUnOrderMqService, mockOrganizationService);
    }

    @Test
    public void testOrderCallback() {
        // Setup
        final OMessage oMessage = new OMessage();
        oMessage.setRequestId("requestId");
        oMessage.setType(0);
        oMessage.setAppId(0);
        oMessage.setShopId(0L);
        oMessage.setUserId(0L);

        // Configure EleAuthService.getOne(...).
        final EleAuthDO eleAuthDO = new EleAuthDO();
        eleAuthDO.setId(0L);
        eleAuthDO.setGuid("*************-4f91-a663-2f1fcb4d134a");
        eleAuthDO.setEnterpriseGuid("enterpriseGuid");
        eleAuthDO.setStoreGuid("storeGuid");
        eleAuthDO.setUserId(0L);
        when(mockEleAuthService.getOne(any(Wrapper.class))).thenReturn(eleAuthDO);

        // Configure EleUnOrderParser.fromOrderCreated(...).
        final UnOrder unOrder = new UnOrder();
        unOrder.setOrderStatus(0);
        unOrder.setOrderType(0);
        unOrder.setOrderSubType(0);
        unOrder.setEnterpriseGuid("enterpriseGuid");
        unOrder.setStoreGuid("storeGuid");
        when(mockEleUnOrderParse.fromOrderCreated(any(OMessage.class))).thenReturn(unOrder);

        // Configure EleUnOrderParser.fromOrderConfirmed(...).
        final UnOrder unOrder1 = new UnOrder();
        unOrder1.setOrderStatus(0);
        unOrder1.setOrderType(0);
        unOrder1.setOrderSubType(0);
        unOrder1.setEnterpriseGuid("enterpriseGuid");
        unOrder1.setStoreGuid("storeGuid");
        when(mockEleUnOrderParse.fromOrderConfirmed(any(OMessage.class))).thenReturn(unOrder1);

        // Configure EleUnOrderParser.fromOrderShippingDistribute(...).
        final UnOrder unOrder2 = new UnOrder();
        unOrder2.setOrderStatus(0);
        unOrder2.setOrderType(0);
        unOrder2.setOrderSubType(0);
        unOrder2.setEnterpriseGuid("enterpriseGuid");
        unOrder2.setStoreGuid("storeGuid");
        when(mockEleUnOrderParse.fromOrderShippingDistribute(any(OMessage.class))).thenReturn(unOrder2);

        // Configure EleUnOrderParser.fromOrderShipping(...).
        final UnOrder unOrder3 = new UnOrder();
        unOrder3.setOrderStatus(0);
        unOrder3.setOrderType(0);
        unOrder3.setOrderSubType(0);
        unOrder3.setEnterpriseGuid("enterpriseGuid");
        unOrder3.setStoreGuid("storeGuid");
        when(mockEleUnOrderParse.fromOrderShipping(any(OMessage.class))).thenReturn(unOrder3);

        // Configure EleUnOrderParser.fromOrderShipSuccessful(...).
        final UnOrder unOrder4 = new UnOrder();
        unOrder4.setOrderStatus(0);
        unOrder4.setOrderType(0);
        unOrder4.setOrderSubType(0);
        unOrder4.setEnterpriseGuid("enterpriseGuid");
        unOrder4.setStoreGuid("storeGuid");
        when(mockEleUnOrderParse.fromOrderShipSuccessful(any(OMessage.class))).thenReturn(unOrder4);

        // Configure EleUnOrderParser.fromOrderFinished(...).
        final UnOrder unOrder5 = new UnOrder();
        unOrder5.setOrderStatus(0);
        unOrder5.setOrderType(0);
        unOrder5.setOrderSubType(0);
        unOrder5.setEnterpriseGuid("enterpriseGuid");
        unOrder5.setStoreGuid("storeGuid");
        when(mockEleUnOrderParse.fromOrderFinished(any(OMessage.class))).thenReturn(unOrder5);

        // Configure EleUnOrderParser.fromOrderCanceled(...).
        final UnOrder unOrder6 = new UnOrder();
        unOrder6.setOrderStatus(0);
        unOrder6.setOrderType(0);
        unOrder6.setOrderSubType(0);
        unOrder6.setEnterpriseGuid("enterpriseGuid");
        unOrder6.setStoreGuid("storeGuid");
        when(mockEleUnOrderParse.fromOrderCanceled(any(OMessage.class))).thenReturn(unOrder6);

        // Configure EleUnOrderParser.fromOrderReminded(...).
        final UnOrder unOrder7 = new UnOrder();
        unOrder7.setOrderStatus(0);
        unOrder7.setOrderType(0);
        unOrder7.setOrderSubType(0);
        unOrder7.setEnterpriseGuid("enterpriseGuid");
        unOrder7.setStoreGuid("storeGuid");
        when(mockEleUnOrderParse.fromOrderReminded(any(OMessage.class))).thenReturn(unOrder7);

        // Configure EleUnOrderParser.fromOrderCancelReq(...).
        final UnOrder unOrder8 = new UnOrder();
        unOrder8.setOrderStatus(0);
        unOrder8.setOrderType(0);
        unOrder8.setOrderSubType(0);
        unOrder8.setEnterpriseGuid("enterpriseGuid");
        unOrder8.setStoreGuid("storeGuid");
        when(mockEleUnOrderParse.fromOrderCancelReq(any(OMessage.class))).thenReturn(unOrder8);

        // Configure EleUnOrderParser.fromOrderCancelCancelReq(...).
        final UnOrder unOrder9 = new UnOrder();
        unOrder9.setOrderStatus(0);
        unOrder9.setOrderType(0);
        unOrder9.setOrderSubType(0);
        unOrder9.setEnterpriseGuid("enterpriseGuid");
        unOrder9.setStoreGuid("storeGuid");
        when(mockEleUnOrderParse.fromOrderCancelCancelReq(any(OMessage.class))).thenReturn(unOrder9);

        // Configure EleUnOrderParser.fromOrderCancelDisagreed(...).
        final UnOrder unOrder10 = new UnOrder();
        unOrder10.setOrderStatus(0);
        unOrder10.setOrderType(0);
        unOrder10.setOrderSubType(0);
        unOrder10.setEnterpriseGuid("enterpriseGuid");
        unOrder10.setStoreGuid("storeGuid");
        when(mockEleUnOrderParse.fromOrderCancelDisagreed(any(OMessage.class))).thenReturn(unOrder10);

        // Configure EleUnOrderParser.fromOrderCancelArbitrationEffective(...).
        final UnOrder unOrder11 = new UnOrder();
        unOrder11.setOrderStatus(0);
        unOrder11.setOrderType(0);
        unOrder11.setOrderSubType(0);
        unOrder11.setEnterpriseGuid("enterpriseGuid");
        unOrder11.setStoreGuid("storeGuid");
        when(mockEleUnOrderParse.fromOrderCancelArbitrationEffective(any(OMessage.class))).thenReturn(unOrder11);

        // Configure EleUnOrderParser.fromOrderCancelAgreed(...).
        final UnOrder unOrder12 = new UnOrder();
        unOrder12.setOrderStatus(0);
        unOrder12.setOrderType(0);
        unOrder12.setOrderSubType(0);
        unOrder12.setEnterpriseGuid("enterpriseGuid");
        unOrder12.setStoreGuid("storeGuid");
        when(mockEleUnOrderParse.fromOrderCancelAgreed(any(OMessage.class))).thenReturn(unOrder12);

        // Configure EleUnOrderParser.fromOrderRefundReq(...).
        final UnOrder unOrder13 = new UnOrder();
        unOrder13.setOrderStatus(0);
        unOrder13.setOrderType(0);
        unOrder13.setOrderSubType(0);
        unOrder13.setEnterpriseGuid("enterpriseGuid");
        unOrder13.setStoreGuid("storeGuid");
        when(mockEleUnOrderParse.fromOrderRefundReq(any(OMessage.class))).thenReturn(unOrder13);

        // Configure EleUnOrderParser.fromOrderCancelRefundReq(...).
        final UnOrder unOrder14 = new UnOrder();
        unOrder14.setOrderStatus(0);
        unOrder14.setOrderType(0);
        unOrder14.setOrderSubType(0);
        unOrder14.setEnterpriseGuid("enterpriseGuid");
        unOrder14.setStoreGuid("storeGuid");
        when(mockEleUnOrderParse.fromOrderCancelRefundReq(any(OMessage.class))).thenReturn(unOrder14);

        // Configure EleUnOrderParser.fromOrderRefundDisagreed(...).
        final UnOrder unOrder15 = new UnOrder();
        unOrder15.setOrderStatus(0);
        unOrder15.setOrderType(0);
        unOrder15.setOrderSubType(0);
        unOrder15.setEnterpriseGuid("enterpriseGuid");
        unOrder15.setStoreGuid("storeGuid");
        when(mockEleUnOrderParse.fromOrderRefundDisagreed(any(OMessage.class))).thenReturn(unOrder15);

        // Configure EleUnOrderParser.fromOrderRefundArbitrationEffective(...).
        final UnOrder unOrder16 = new UnOrder();
        unOrder16.setOrderStatus(0);
        unOrder16.setOrderType(0);
        unOrder16.setOrderSubType(0);
        unOrder16.setEnterpriseGuid("enterpriseGuid");
        unOrder16.setStoreGuid("storeGuid");
        when(mockEleUnOrderParse.fromOrderRefundArbitrationEffective(any(OMessage.class))).thenReturn(unOrder16);

        // Configure EleUnOrderParser.fromOrderRefundAgreed(...).
        final UnOrder unOrder17 = new UnOrder();
        unOrder17.setOrderStatus(0);
        unOrder17.setOrderType(0);
        unOrder17.setOrderSubType(0);
        unOrder17.setEnterpriseGuid("enterpriseGuid");
        unOrder17.setStoreGuid("storeGuid");
        when(mockEleUnOrderParse.fromOrderRefundAgreed(any(OMessage.class))).thenReturn(unOrder17);

        // Run the test
        eleCallbackServiceImplUnderTest.orderCallback(oMessage);

        // Verify the results
        verify(mockEleAuthService).unbindCallback(any(OMessage.class));

        // Confirm UnOrderMqService.sendUnOrder(...).
        final UnOrder unorder = new UnOrder();
        unorder.setOrderStatus(0);
        unorder.setOrderType(0);
        unorder.setOrderSubType(0);
        unorder.setEnterpriseGuid("enterpriseGuid");
        unorder.setStoreGuid("storeGuid");
        verify(mockUnOrderMqService).sendUnOrder(unorder);
    }
}
