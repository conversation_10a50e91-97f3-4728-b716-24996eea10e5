package com.holder.saas.store.takeaway.producers.controller;

import com.alibaba.fastjson.JSON;
import com.holder.saas.store.takeaway.producers.HolderSaasStoreTakeawayProducersApplication;
import com.holder.saas.store.takeaway.producers.controller.util.JsonFileUtil;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.business.group.StoreBindDTO;
import com.holderzone.saas.store.dto.order.response.groupon.GroupVerifyDTO;
import com.holderzone.saas.store.dto.takeaway.request.CouPonPreReqDTO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.io.UnsupportedEncodingException;

import static com.holderzone.saas.store.dto.common.CommonConstant.USER_INFO;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * <AUTHOR>
 * @date 2023/11/20
 * @description
 */
@Slf4j
@WebAppConfiguration
@ContextConfiguration
@AutoConfigureMockMvc
@RunWith(SpringRunner.class)
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
@SpringBootTest(classes = HolderSaasStoreTakeawayProducersApplication.class)
public class GroupBuyControllerTest {

    private static final String USERINFO = "{\"operSubjectGuid\": \"2010121440477930009\"," +
            "\"enterpriseGuid\":" + " \"2009281531195930006\"," +
            "\"enterpriseName\": \"赵氏企业\"," +
            "\"enterpriseNo\": \"********\"," +
            "\"storeGuid\":" + " \"2106221850429620006\"," +
            "\"storeName\": \"交子大道测试门店\"," +
            "\"storeNo\": \"5796807\",\"userGuid\": " +
            "\"6787561298847596545\"," +
            "\"account\": \"196504\"," +
            "\"tel\": \"***********\"," +
            "\"name\": \"靓亮仔\"}\n";

    private static final String GROUP = "/group";

    private static final String RESPONSE = "response:";

    @Autowired
    private WebApplicationContext wac;

    private MockMvc mockMvc;

    @Before
    public void setupMockMvc() {
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();
    }

    @Test
    public void bindStore() throws UnsupportedEncodingException {
        StoreBindDTO bindStoreReqDTO = JSON.parseObject(JsonFileUtil.read("group/bindStore.json"),
                StoreBindDTO.class);
        String bindStoreJsonString = JSON.toJSONString(bindStoreReqDTO);
        MvcResult bindStoreMvcResult = null;
        try {
            bindStoreMvcResult = mockMvc.perform(post(GROUP + "/store/bind")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(bindStoreJsonString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        String contentAsString = bindStoreMvcResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    @Test
    public void unbindStore() throws UnsupportedEncodingException {
        StoreBindDTO unbindStoreReqDTO = JSON.parseObject(JsonFileUtil.read("group/unbindStore.json"),
                StoreBindDTO.class);
        String unbindStoreJsonString = JSON.toJSONString(unbindStoreReqDTO);
        MvcResult unbindStoreMvcResult = null;
        try {
            unbindStoreMvcResult = mockMvc.perform(post(GROUP + "/store/unbind")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(unbindStoreJsonString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        String contentAsString = unbindStoreMvcResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    @Test
    public void query() throws UnsupportedEncodingException {
        MvcResult queryMvcResult = null;
        try {
            queryMvcResult = mockMvc.perform(get(GROUP + "/list/2106221850429620006")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        String contentAsString = queryMvcResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    @Test
    public void couponPrepare() throws UnsupportedEncodingException {
        CouPonPreReqDTO couponPrepareReqDTO = JSON.parseObject(JsonFileUtil.read("group/couponPrepare.json"),
                CouPonPreReqDTO.class);
        String couponPrepareJsonString = JSON.toJSONString(couponPrepareReqDTO);
        MvcResult couponPrepareMvcResult = null;
        try {
            couponPrepareMvcResult = mockMvc.perform(post(GROUP + "/coupon/prepare")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(couponPrepareJsonString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        String contentAsString = couponPrepareMvcResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    @Test
    public void verifyCoupon() throws UnsupportedEncodingException {
        StoreBindDTO verifyCouponReqDTO = JSON.parseObject(JsonFileUtil.read("group/verifyCoupon.json"),
                StoreBindDTO.class);
        String verifyCouponJsonString = JSON.toJSONString(verifyCouponReqDTO);
        MvcResult verifyCouponMvcResult = null;
        try {
            verifyCouponMvcResult = mockMvc.perform(post(GROUP + "/coupon/verify")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(verifyCouponJsonString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        String contentAsString = verifyCouponMvcResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    @Test
    public void revokeCoupon() throws UnsupportedEncodingException {
        GroupVerifyDTO revokeCouponReqDTO = JSON.parseObject(JsonFileUtil.read("group/revokeCoupon.json"),
                GroupVerifyDTO.class);
        String revokeCouponJsonString = JSON.toJSONString(revokeCouponReqDTO);
        MvcResult revokeCouponMvcResult = null;
        try {
            revokeCouponMvcResult = mockMvc.perform(post(GROUP + "/coupon/revoke")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(revokeCouponJsonString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        String contentAsString = revokeCouponMvcResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }
}