package com.holder.saas.store.takeaway.producers.service.converter;

import com.holder.saas.store.takeaway.producers.entity.domain.AlipayAuthDO;
import com.holderzone.saas.store.dto.takeaway.request.NotifyAliPayAuthReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.AlipayAuthRespDTO;
import org.junit.Test;

import static org.junit.Assert.assertEquals;

public class AuthConverterTest {

    @Test
    public void testAlipayAuthDO2AlipayAuthDTO() {
        // Setup
        final AlipayAuthDO alipayAuthDO = new AlipayAuthDO();
        alipayAuthDO.setEnterpriseGuid("enterpriseGuid");
        alipayAuthDO.setOperSubjectGuid("operSubjectGuid");
        alipayAuthDO.setAppAuthToken("appAuthToken");
        alipayAuthDO.setAppId("appId");
        alipayAuthDO.setApplyPublicKey("applyPublicKey");
        alipayAuthDO.setApplyPrivateKey("applyPrivateKey");
        alipayAuthDO.setAliPublicKey("aliPublicKey");
        alipayAuthDO.setAes("aes");

        final AlipayAuthRespDTO expectedResult = new AlipayAuthRespDTO();
        expectedResult.setEnterpriseGuid("enterpriseGuid");
        expectedResult.setOperSubjectGuid("operSubjectGuid");
        expectedResult.setAppId("appId");
        expectedResult.setAppAuthToken("appAuthToken");
        expectedResult.setApplyPublicKey("applyPublicKey");
        expectedResult.setApplyPrivateKey("applyPrivateKey");
        expectedResult.setAliPublicKey("aliPublicKey");
        expectedResult.setAes("aes");

        // Run the test
        final AlipayAuthRespDTO result = AuthConverter.alipayAuthDO2AlipayAuthDTO(alipayAuthDO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testNotifyDTO2AlipayAuthDO() {
        // Setup
        final NotifyAliPayAuthReqDTO notifyDTO = new NotifyAliPayAuthReqDTO();
        notifyDTO.setEnterpriseGuid("enterpriseGuid");
        notifyDTO.setOperSubjectGuid("operSubjectGuid");
        notifyDTO.setAppId("appId");
        notifyDTO.setAppAuthToken("appAuthToken");
        notifyDTO.setNotifyType(0);

        final AlipayAuthDO expectedResult = new AlipayAuthDO();
        expectedResult.setEnterpriseGuid("enterpriseGuid");
        expectedResult.setOperSubjectGuid("operSubjectGuid");
        expectedResult.setAppAuthToken("appAuthToken");
        expectedResult.setAppId("appId");
        expectedResult.setApplyPublicKey("applyPublicKey");
        expectedResult.setApplyPrivateKey("applyPrivateKey");
        expectedResult.setAliPublicKey("aliPublicKey");
        expectedResult.setAes("aes");

        // Run the test
        final AlipayAuthDO result = AuthConverter.notifyDTO2AlipayAuthDO(notifyDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }
}
