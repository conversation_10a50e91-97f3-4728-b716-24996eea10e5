package com.holder.saas.store.takeaway.producers.service.impl;

import com.holder.saas.store.takeaway.producers.service.MtCouponService;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.business.group.StoreBindDTO;
import com.holderzone.saas.store.dto.order.response.groupon.GroupVerifyDTO;
import com.holderzone.saas.store.dto.takeaway.request.CouPonPreReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.CouPonReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.CouponDelReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.MtCouponReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtCouponDoCheckRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtCouponPreRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtDelCouponRespDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class MeiTuanGroupBuyServiceImplTest {

    @Mock
    private MtCouponService mockMtProdCouponServiceImpl;

    private MeiTuanGroupBuyServiceImpl meiTuanGroupBuyServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        meiTuanGroupBuyServiceImplUnderTest = new MeiTuanGroupBuyServiceImpl(mockMtProdCouponServiceImpl);
    }

    @Test(expected = BusinessException.class)
    public void testBindStore() {
        meiTuanGroupBuyServiceImplUnderTest.bindStore(new StoreBindDTO());
    }

    @Test
    public void testGetToken() {
        assertNull(meiTuanGroupBuyServiceImplUnderTest.getToken());
    }

    @Test
    public void testCouponPrepare() {
        // Setup
        final CouPonPreReqDTO couPonPreReqDTO = new CouPonPreReqDTO();
        couPonPreReqDTO.setStoreGuid("erpId");
        couPonPreReqDTO.setCouponCode("couponCode");
        couPonPreReqDTO.setErpOrderId("erpOrderId");
        couPonPreReqDTO.setActivityGuid("activityGuid");
        couPonPreReqDTO.setUsedGuidList(Arrays.asList("value"));

        final MtCouponPreRespDTO mtCouponPreRespDTO = new MtCouponPreRespDTO();
        mtCouponPreRespDTO.setCount(0);
        mtCouponPreRespDTO.setCouponBuyPrice(0.0);
        mtCouponPreRespDTO.setCouponCode("couponCode");
        mtCouponPreRespDTO.setIsVoucher(false);
        mtCouponPreRespDTO.setCouponEndTime("couponEndTime");
        mtCouponPreRespDTO.setDealId(0);
        mtCouponPreRespDTO.setDealPrice(0.0);
        mtCouponPreRespDTO.setDealTitle("dealTitle");
        mtCouponPreRespDTO.setDealValue(0.0);
        mtCouponPreRespDTO.setRawTitle("rawTitle");
        mtCouponPreRespDTO.setCouponType(0);
        final List<MtCouponPreRespDTO> expectedResult = Arrays.asList(mtCouponPreRespDTO);

        // Configure MtCouponService.preCheck(...).
        final MtCouponPreRespDTO mtCouponPreRespDTO1 = new MtCouponPreRespDTO();
        mtCouponPreRespDTO1.setCount(0);
        mtCouponPreRespDTO1.setCouponBuyPrice(0.0);
        mtCouponPreRespDTO1.setCouponCode("couponCode");
        mtCouponPreRespDTO1.setIsVoucher(false);
        mtCouponPreRespDTO1.setCouponEndTime("couponEndTime");
        mtCouponPreRespDTO1.setDealId(0);
        mtCouponPreRespDTO1.setDealPrice(0.0);
        mtCouponPreRespDTO1.setDealTitle("dealTitle");
        mtCouponPreRespDTO1.setDealValue(0.0);
        mtCouponPreRespDTO1.setRawTitle("rawTitle");
        mtCouponPreRespDTO1.setCouponType(0);
        final MtCouponReqDTO mtCouponReqDTO = new MtCouponReqDTO();
        mtCouponReqDTO.setStoreGuid("erpId");
        mtCouponReqDTO.setCouponCode("couponCode");
        mtCouponReqDTO.setCount(0);
        mtCouponReqDTO.setErpId("erpId");
        mtCouponReqDTO.setErpName("erpName");
        mtCouponReqDTO.setErpOrderId("erpOrderId");
        when(mockMtProdCouponServiceImpl.preCheck(mtCouponReqDTO)).thenReturn(mtCouponPreRespDTO1);

        // Run the test
        final List<MtCouponPreRespDTO> result = meiTuanGroupBuyServiceImplUnderTest.couponPrepare(couPonPreReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testVerifyCoupon() {
        // Setup
        final CouPonReqDTO couPonReq = new CouPonReqDTO();
        couPonReq.setStoreGuid("erpId");
        couPonReq.setCouponCode("couponCode");
        couPonReq.setCount(0);
        couPonReq.setErpId("erpId");
        couPonReq.setErpName("erpName");
        couPonReq.setErpOrderId("erpOrderId");
        couPonReq.setCouponCodeList(Arrays.asList("value"));

        final GroupVerifyDTO groupVerifyDTO = new GroupVerifyDTO();
        groupVerifyDTO.setGroupBuyType(0);
        groupVerifyDTO.setCode("code");
        groupVerifyDTO.setErpId("erpId");
        groupVerifyDTO.setErpName("erpName");
        groupVerifyDTO.setStoreGuid("erpId");
        final List<GroupVerifyDTO> expectedResult = Arrays.asList(groupVerifyDTO);

        // Configure MtCouponService.doCheck(...).
        final MtCouponDoCheckRespDTO mtCouponDoCheckRespDTO = new MtCouponDoCheckRespDTO();
        mtCouponDoCheckRespDTO.setOrderId("orderId");
        mtCouponDoCheckRespDTO.setCouponCodes(Arrays.asList("value"));
        mtCouponDoCheckRespDTO.setDealTitle("dealTitle");
        mtCouponDoCheckRespDTO.setDealValue(0.0);
        mtCouponDoCheckRespDTO.setDealId(0);
        final MtCouponReqDTO mtCouponReqDTO = new MtCouponReqDTO();
        mtCouponReqDTO.setStoreGuid("erpId");
        mtCouponReqDTO.setCouponCode("couponCode");
        mtCouponReqDTO.setCount(0);
        mtCouponReqDTO.setErpId("erpId");
        mtCouponReqDTO.setErpName("erpName");
        mtCouponReqDTO.setErpOrderId("erpOrderId");
        when(mockMtProdCouponServiceImpl.doCheck(mtCouponReqDTO)).thenReturn(mtCouponDoCheckRespDTO);

        // Run the test
        final List<GroupVerifyDTO> result = meiTuanGroupBuyServiceImplUnderTest.verifyCoupon(couPonReq);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testRevokeCoupon() {
        // Setup
        final GroupVerifyDTO revokeReq = new GroupVerifyDTO();
        revokeReq.setGroupBuyType(0);
        revokeReq.setCode("code");
        revokeReq.setErpId("erpId");
        revokeReq.setErpName("erpName");
        revokeReq.setStoreGuid("erpId");

        final MtDelCouponRespDTO expectedResult = new MtDelCouponRespDTO(0, "message");

        // Configure MtCouponService.cancelTicket(...).
        final CouponDelReqDTO couponDelReqDTO = new CouponDelReqDTO();
        couponDelReqDTO.setStoreGuid("erpId");
        couponDelReqDTO.setCouponCode("code");
        couponDelReqDTO.setErpId("erpId");
        couponDelReqDTO.setErpName("erpName");
        when(mockMtProdCouponServiceImpl.cancelTicket(couponDelReqDTO))
                .thenReturn(new MtDelCouponRespDTO(0, "message"));

        // Run the test
        final MtDelCouponRespDTO result = meiTuanGroupBuyServiceImplUnderTest.revokeCoupon(revokeReq);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test(expected = BusinessException.class)
    public void testRevokeCoupon_MtCouponServiceReturnsNull() {
        // Setup
        final GroupVerifyDTO revokeReq = new GroupVerifyDTO();
        revokeReq.setGroupBuyType(0);
        revokeReq.setCode("code");
        revokeReq.setErpId("erpId");
        revokeReq.setErpName("erpName");
        revokeReq.setStoreGuid("erpId");

        // Configure MtCouponService.cancelTicket(...).
        final CouponDelReqDTO couponDelReqDTO = new CouponDelReqDTO();
        couponDelReqDTO.setStoreGuid("erpId");
        couponDelReqDTO.setCouponCode("code");
        couponDelReqDTO.setErpId("erpId");
        couponDelReqDTO.setErpName("erpName");
        when(mockMtProdCouponServiceImpl.cancelTicket(couponDelReqDTO)).thenReturn(null);

        // Run the test
        meiTuanGroupBuyServiceImplUnderTest.revokeCoupon(revokeReq);
    }

    @Test
    public void testRevokeCoupon_MtCouponServiceReturnsError() {
        // Setup
        final GroupVerifyDTO revokeReq = new GroupVerifyDTO();
        revokeReq.setGroupBuyType(0);
        revokeReq.setCode("code");
        revokeReq.setErpId("erpId");
        revokeReq.setErpName("erpName");
        revokeReq.setStoreGuid("erpId");

        final MtDelCouponRespDTO expectedResult = new MtDelCouponRespDTO(0, "message");

        // Configure MtCouponService.cancelTicket(...).
        final CouponDelReqDTO couponDelReqDTO = new CouponDelReqDTO();
        couponDelReqDTO.setStoreGuid("erpId");
        couponDelReqDTO.setCouponCode("code");
        couponDelReqDTO.setErpId("erpId");
        couponDelReqDTO.setErpName("erpName");
        when(mockMtProdCouponServiceImpl.cancelTicket(couponDelReqDTO))
                .thenReturn(MtDelCouponRespDTO.buildError("message"));

        // Run the test
        final MtDelCouponRespDTO result = meiTuanGroupBuyServiceImplUnderTest.revokeCoupon(revokeReq);

        // Verify the results
        assertEquals(expectedResult, result);
    }
}
