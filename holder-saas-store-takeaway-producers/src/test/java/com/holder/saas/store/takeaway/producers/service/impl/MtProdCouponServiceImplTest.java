package com.holder.saas.store.takeaway.producers.service.impl;

import com.holder.saas.store.takeaway.producers.entity.domain.MtAuthDO;
import com.holder.saas.store.takeaway.producers.mapstruct.MtCouponMapstruct;
import com.holder.saas.store.takeaway.producers.service.MtAuthService;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.takeaway.request.CouponDelReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.MtCouponReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.*;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class MtProdCouponServiceImplTest {

    @Mock
    private MtAuthService mockAuthService;
    @Mock
    private MtCouponMapstruct mtCouponMapstruct;

    private MtProdCouponServiceImpl mtProdCouponServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        mtProdCouponServiceImplUnderTest = new MtProdCouponServiceImpl(mockAuthService, mtCouponMapstruct);
        ReflectionTestUtils.setField(mtProdCouponServiceImplUnderTest, "mtSignKey", "mtSignKey");
        ReflectionTestUtils.setField(mtProdCouponServiceImplUnderTest, "developerId", "developerId");
    }

    @Test
    public void testQueryById() {
        // Setup
        final MtCouponReqDTO mtCouponReqDTO = new MtCouponReqDTO();
        mtCouponReqDTO.setStoreGuid("storeGuid");
        mtCouponReqDTO.setAppAuthToken("accessToken");
        mtCouponReqDTO.setCouponCode("couponCode");
        mtCouponReqDTO.setCount(0);
        mtCouponReqDTO.setErpId("erpId");
        mtCouponReqDTO.setErpName("eName");
        mtCouponReqDTO.setErpOrderId("erpOrderId");

        final MtCouponDetailRespDTO expectedResult = new MtCouponDetailRespDTO();
        expectedResult.setCouponBuyPrice(0.0);
        expectedResult.setCouponCancelStatus(0);
        expectedResult.setCouponCode("couponCode");
        expectedResult.setCouponStatusDesc("couponStatusDesc");
        expectedResult.setCouponUseTime("couponUseTime");

        // Configure MtAuthService.getAuth(...).
        final MtAuthDO mtAuthDO = new MtAuthDO();
        mtAuthDO.setId(0);
        mtAuthDO.setEPoiId("ePoiId");
        mtAuthDO.setMtStoreGuid("mtStoreGuid");
        mtAuthDO.setMtStoreName("mtStoreName");
        mtAuthDO.setAccessToken("accessToken");
        when(mockAuthService.getAuth("storeGuid", 0)).thenReturn(mtAuthDO);

        // Run the test
        final MtCouponDetailRespDTO result = mtProdCouponServiceImplUnderTest.queryById(mtCouponReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test(expected = BusinessException.class)
    public void testQueryById_MtAuthServiceReturnsNull() {
        // Setup
        final MtCouponReqDTO mtCouponReqDTO = new MtCouponReqDTO();
        mtCouponReqDTO.setStoreGuid("storeGuid");
        mtCouponReqDTO.setAppAuthToken("accessToken");
        mtCouponReqDTO.setCouponCode("couponCode");
        mtCouponReqDTO.setCount(0);
        mtCouponReqDTO.setErpId("erpId");
        mtCouponReqDTO.setErpName("eName");
        mtCouponReqDTO.setErpOrderId("erpOrderId");

        when(mockAuthService.getAuth("storeGuid", 0)).thenReturn(null);

        // Run the test
        mtProdCouponServiceImplUnderTest.queryById(mtCouponReqDTO);
    }

    @Test
    public void testCheckTicket() {
        // Setup
        final MtCouponReqDTO mtCouponReqDTO = new MtCouponReqDTO();
        mtCouponReqDTO.setStoreGuid("storeGuid");
        mtCouponReqDTO.setAppAuthToken("accessToken");
        mtCouponReqDTO.setCouponCode("couponCode");
        mtCouponReqDTO.setCount(0);
        mtCouponReqDTO.setErpId("erpId");
        mtCouponReqDTO.setErpName("eName");
        mtCouponReqDTO.setErpOrderId("erpOrderId");

        final MtCouponDoCheckRespDTO expectedResult = new MtCouponDoCheckRespDTO();
        expectedResult.setOrderId("erpOrderId");
        expectedResult.setCouponCodes(Arrays.asList("value"));
        expectedResult.setDealTitle("dealTitle");
        expectedResult.setMessage("message");
        expectedResult.setResult(0);

        // Run the test
        final MtCouponDoCheckRespDTO result = mtProdCouponServiceImplUnderTest.checkTicket(mtCouponReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testDoCheck() {
        // Setup
        final MtCouponReqDTO mtCouponReqDTO = new MtCouponReqDTO();
        mtCouponReqDTO.setStoreGuid("storeGuid");
        mtCouponReqDTO.setAppAuthToken("accessToken");
        mtCouponReqDTO.setCouponCode("couponCode");
        mtCouponReqDTO.setCount(0);
        mtCouponReqDTO.setErpId("erpId");
        mtCouponReqDTO.setErpName("eName");
        mtCouponReqDTO.setErpOrderId("erpOrderId");

        final MtCouponDoCheckRespDTO expectedResult = new MtCouponDoCheckRespDTO();
        expectedResult.setOrderId("erpOrderId");
        expectedResult.setCouponCodes(Arrays.asList("value"));
        expectedResult.setDealTitle("dealTitle");
        expectedResult.setMessage("message");
        expectedResult.setResult(0);

        // Configure MtAuthService.getAuth(...).
        final MtAuthDO mtAuthDO = new MtAuthDO();
        mtAuthDO.setId(0);
        mtAuthDO.setEPoiId("ePoiId");
        mtAuthDO.setMtStoreGuid("mtStoreGuid");
        mtAuthDO.setMtStoreName("mtStoreName");
        mtAuthDO.setAccessToken("accessToken");
        when(mockAuthService.getAuth("storeGuid", 0)).thenReturn(mtAuthDO);

        // Run the test
        final MtCouponDoCheckRespDTO result = mtProdCouponServiceImplUnderTest.doCheck(mtCouponReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test(expected = BusinessException.class)
    public void testDoCheck_MtAuthServiceReturnsNull() {
        // Setup
        final MtCouponReqDTO mtCouponReqDTO = new MtCouponReqDTO();
        mtCouponReqDTO.setStoreGuid("storeGuid");
        mtCouponReqDTO.setAppAuthToken("accessToken");
        mtCouponReqDTO.setCouponCode("couponCode");
        mtCouponReqDTO.setCount(0);
        mtCouponReqDTO.setErpId("erpId");
        mtCouponReqDTO.setErpName("eName");
        mtCouponReqDTO.setErpOrderId("erpOrderId");

        when(mockAuthService.getAuth("storeGuid", 0)).thenReturn(null);

        // Run the test
        mtProdCouponServiceImplUnderTest.doCheck(mtCouponReqDTO);
    }

    @Test
    public void testPreCheck() {
        // Setup
        final MtCouponReqDTO mtCouponReqDTO = new MtCouponReqDTO();
        mtCouponReqDTO.setStoreGuid("storeGuid");
        mtCouponReqDTO.setAppAuthToken("accessToken");
        mtCouponReqDTO.setCouponCode("couponCode");
        mtCouponReqDTO.setCount(0);
        mtCouponReqDTO.setErpId("erpId");
        mtCouponReqDTO.setErpName("eName");
        mtCouponReqDTO.setErpOrderId("erpOrderId");

        final MtCouponPreRespDTO expectedResult = new MtCouponPreRespDTO();
        expectedResult.setCount(0);
        expectedResult.setCouponBuyPrice(0.0);
        expectedResult.setCouponCode("couponCode");
        expectedResult.setIsVoucher(false);
        expectedResult.setMinConsume(0);

        // Configure MtAuthService.getAuth(...).
        final MtAuthDO mtAuthDO = new MtAuthDO();
        mtAuthDO.setId(0);
        mtAuthDO.setEPoiId("ePoiId");
        mtAuthDO.setMtStoreGuid("mtStoreGuid");
        mtAuthDO.setMtStoreName("mtStoreName");
        mtAuthDO.setAccessToken("accessToken");
        when(mockAuthService.getAuth("storeGuid", 0)).thenReturn(mtAuthDO);

        // Run the test
        final MtCouponPreRespDTO result = mtProdCouponServiceImplUnderTest.preCheck(mtCouponReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test(expected = BusinessException.class)
    public void testPreCheck_MtAuthServiceReturnsNull() {
        // Setup
        final MtCouponReqDTO mtCouponReqDTO = new MtCouponReqDTO();
        mtCouponReqDTO.setStoreGuid("storeGuid");
        mtCouponReqDTO.setAppAuthToken("accessToken");
        mtCouponReqDTO.setCouponCode("couponCode");
        mtCouponReqDTO.setCount(0);
        mtCouponReqDTO.setErpId("erpId");
        mtCouponReqDTO.setErpName("eName");
        mtCouponReqDTO.setErpOrderId("erpOrderId");

        when(mockAuthService.getAuth("storeGuid", 0)).thenReturn(null);

        // Run the test
        mtProdCouponServiceImplUnderTest.preCheck(mtCouponReqDTO);
    }

    @Test
    public void testCancelTicket() {
        // Setup
        final CouponDelReqDTO couponDelReqDTO = new CouponDelReqDTO();
        couponDelReqDTO.setStoreGuid("storeGuid");
        couponDelReqDTO.setCouponCode("couponCode");
        couponDelReqDTO.setErpId("erpId");
        couponDelReqDTO.setErpName("eName");

        final MtDelCouponRespDTO expectedResult = new MtDelCouponRespDTO(0, "message");

        // Configure MtAuthService.getAuth(...).
        final MtAuthDO mtAuthDO = new MtAuthDO();
        mtAuthDO.setId(0);
        mtAuthDO.setEPoiId("ePoiId");
        mtAuthDO.setMtStoreGuid("mtStoreGuid");
        mtAuthDO.setMtStoreName("mtStoreName");
        mtAuthDO.setAccessToken("accessToken");
        when(mockAuthService.getAuth("storeGuid", 0)).thenReturn(mtAuthDO);

        // Run the test
        final MtDelCouponRespDTO result = mtProdCouponServiceImplUnderTest.cancelTicket(couponDelReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test(expected = BusinessException.class)
    public void testCancelTicket_MtAuthServiceReturnsNull() {
        // Setup
        final CouponDelReqDTO couponDelReqDTO = new CouponDelReqDTO();
        couponDelReqDTO.setStoreGuid("storeGuid");
        couponDelReqDTO.setCouponCode("couponCode");
        couponDelReqDTO.setErpId("erpId");
        couponDelReqDTO.setErpName("eName");

        when(mockAuthService.getAuth("storeGuid", 0)).thenReturn(null);

        // Run the test
        mtProdCouponServiceImplUnderTest.cancelTicket(couponDelReqDTO);
    }

    @Test
    public void testQueryGroupTradeDetail() {
        // Setup
        final MtCouponReqDTO mtCouponReqDTO = new MtCouponReqDTO();
        mtCouponReqDTO.setStoreGuid("storeGuid");
        mtCouponReqDTO.setAppAuthToken("accessToken");
        mtCouponReqDTO.setCouponCode("couponCode");
        mtCouponReqDTO.setCount(0);
        mtCouponReqDTO.setErpId("erpId");
        mtCouponReqDTO.setErpName("eName");
        mtCouponReqDTO.setErpOrderId("erpOrderId");

        final MtCouponTradeDetailRespDTO expectedResult = new MtCouponTradeDetailRespDTO();
        expectedResult.setStoreGuid("ePoiId");
        expectedResult.setMtStoreGuid("mtStoreGuid");
        expectedResult.setMtStoreName("mtStoreName");
        expectedResult.setBizCost(0.0);
        final MtCouponDetailRespDTO couponDetail = new MtCouponDetailRespDTO();
        expectedResult.setCouponDetail(couponDetail);

        // Configure MtAuthService.getAuth(...).
        final MtAuthDO mtAuthDO = new MtAuthDO();
        mtAuthDO.setId(0);
        mtAuthDO.setEPoiId("ePoiId");
        mtAuthDO.setMtStoreGuid("mtStoreGuid");
        mtAuthDO.setMtStoreName("mtStoreName");
        mtAuthDO.setAccessToken("accessToken");
        when(mockAuthService.getAuth("storeGuid", 0)).thenReturn(mtAuthDO);

        // Run the test
        final MtCouponTradeDetailRespDTO result = mtProdCouponServiceImplUnderTest.queryGroupTradeDetail(
                mtCouponReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test(expected = BusinessException.class)
    public void testQueryGroupTradeDetail_MtAuthServiceReturnsNull() {
        // Setup
        final MtCouponReqDTO mtCouponReqDTO = new MtCouponReqDTO();
        mtCouponReqDTO.setStoreGuid("storeGuid");
        mtCouponReqDTO.setAppAuthToken("accessToken");
        mtCouponReqDTO.setCouponCode("couponCode");
        mtCouponReqDTO.setCount(0);
        mtCouponReqDTO.setErpId("erpId");
        mtCouponReqDTO.setErpName("eName");
        mtCouponReqDTO.setErpOrderId("erpOrderId");

        when(mockAuthService.getAuth("storeGuid", 0)).thenReturn(null);

        // Run the test
        mtProdCouponServiceImplUnderTest.queryGroupTradeDetail(mtCouponReqDTO);
    }
}
