package com.holder.saas.store.takeaway.producers.controller;

import com.holder.saas.store.takeaway.producers.service.DianXinService;
import com.holderzone.saas.store.dto.takeaway.SendOrderCallReq;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDateTime;

import static org.mockito.Mockito.verify;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(DianXinController.class)
public class DianXinControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private DianXinService mockDianXinService;

    @Test
    public void testSendCall() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(post("/dx/send_call")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));

        // Confirm DianXinService.outbound(...).
        final SendOrderCallReq sendOrderCallReq = new SendOrderCallReq();
        sendOrderCallReq.setGuid("d7d0e99a-d71b-4b3d-b821-c889cd757897");
        sendOrderCallReq.setLongTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        sendOrderCallReq.setOrderGuid("orderGuid");
        sendOrderCallReq.setPhone("phone");
        sendOrderCallReq.setBatch("batch");
        verify(mockDianXinService).outbound(sendOrderCallReq);
    }
}
