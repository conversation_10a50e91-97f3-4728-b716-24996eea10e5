package com.holder.saas.store.takeaway.producers.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.holder.saas.store.takeaway.producers.entity.domain.MtAuthDO;
import com.holder.saas.store.takeaway.producers.mapper.MtAuthMapper;
import com.holder.saas.store.takeaway.producers.service.MtHbRedisService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class MtHeartbeatServiceImplTest {

    @Mock
    private MtAuthMapper mockMtAuthMapper;
    @Mock
    private MtHbRedisService mockMtHbRedisService;

    @InjectMocks
    private MtHeartbeatServiceImpl mtHeartbeatServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        ReflectionTestUtils.setField(mtHeartbeatServiceImplUnderTest, "mtDeveloperId", "mtDeveloperId");
        ReflectionTestUtils.setField(mtHeartbeatServiceImplUnderTest, "mtSignKey", "mtSignKey");
    }

    @Test
    public void testHeartbeatReport() {
        // Setup
        when(mockMtHbRedisService.getHeartbeat()).thenReturn("result");

        // Configure MtAuthMapper.selectList(...).
        final MtAuthDO mtAuthDO = new MtAuthDO();
        mtAuthDO.setId(0);
        mtAuthDO.setGuid("6269ae4d-16ab-4915-a3ad-93f994dff42e");
        mtAuthDO.setEPoiId("ePoiId");
        mtAuthDO.setEnterpriseGuid("enterpriseGuid");
        mtAuthDO.setMtStoreGuid("mtStoreGuid");
        final List<MtAuthDO> mtAuthDOS = Arrays.asList(mtAuthDO);
        when(mockMtAuthMapper.selectList(any(Wrapper.class))).thenReturn(mtAuthDOS);

        // Run the test
        mtHeartbeatServiceImplUnderTest.heartbeatReport();

        // Verify the results
        verify(mockMtHbRedisService).saveHeartbeat("heartbeatParameter");
    }

    @Test
    public void testHeartbeatReport_MtHbRedisServiceGetHeartbeatReturnsNull() {
        // Setup
        when(mockMtHbRedisService.getHeartbeat()).thenReturn(null);

        // Configure MtAuthMapper.selectList(...).
        final MtAuthDO mtAuthDO = new MtAuthDO();
        mtAuthDO.setId(0);
        mtAuthDO.setGuid("6269ae4d-16ab-4915-a3ad-93f994dff42e");
        mtAuthDO.setEPoiId("ePoiId");
        mtAuthDO.setEnterpriseGuid("enterpriseGuid");
        mtAuthDO.setMtStoreGuid("mtStoreGuid");
        final List<MtAuthDO> mtAuthDOS = Arrays.asList(mtAuthDO);
        when(mockMtAuthMapper.selectList(any(Wrapper.class))).thenReturn(mtAuthDOS);

        // Run the test
        mtHeartbeatServiceImplUnderTest.heartbeatReport();

        // Verify the results
        verify(mockMtHbRedisService).saveHeartbeat("heartbeatParameter");
    }

    @Test
    public void testHeartbeatReport_MtAuthMapperReturnsNull() {
        // Setup
        when(mockMtHbRedisService.getHeartbeat()).thenReturn("result");
        when(mockMtAuthMapper.selectList(any(Wrapper.class))).thenReturn(null);

        // Run the test
        mtHeartbeatServiceImplUnderTest.heartbeatReport();

        // Verify the results
    }

    @Test
    public void testHeartbeatReport_MtAuthMapperReturnsNoItems() {
        // Setup
        when(mockMtHbRedisService.getHeartbeat()).thenReturn("result");
        when(mockMtAuthMapper.selectList(any(Wrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        mtHeartbeatServiceImplUnderTest.heartbeatReport();

        // Verify the results
        verify(mockMtHbRedisService).saveHeartbeat("heartbeatParameter");
    }

    @Test
    public void testDetectionHeartbeat() {
        assertEquals("result", mtHeartbeatServiceImplUnderTest.detectionHeartbeat("url", "params"));
    }
}
