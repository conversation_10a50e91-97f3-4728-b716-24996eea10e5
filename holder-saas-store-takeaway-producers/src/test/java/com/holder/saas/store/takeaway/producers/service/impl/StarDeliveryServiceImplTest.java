package com.holder.saas.store.takeaway.producers.service.impl;

import com.holder.saas.store.takeaway.producers.service.UnOrderDeliveryService;
import com.holder.saas.store.takeaway.producers.service.UnOrderMqService;
import com.holderzone.framework.rocketmq.common.DefaultRocketMqProducer;
import com.holderzone.saas.store.dto.takeaway.UnOrder;
import org.apache.rocketmq.common.message.Message;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;

import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;

@RunWith(MockitoJUnitRunner.class)
public class StarDeliveryServiceImplTest {

    @Mock
    private DefaultRocketMqProducer mockDefaultRocketMqProducer;
    @Mock
    private UnOrderMqService mockUnOrderMqService;

    private StarDeliveryServiceImpl starDeliveryServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        starDeliveryServiceImplUnderTest = new StarDeliveryServiceImpl(mockDefaultRocketMqProducer,
                mockUnOrderMqService);
    }

    @Test
    public void testStarDelivery() {
        // Setup
        final UnOrderDeliveryService unOrderDeliveryService = null;
        final UnOrder unOrder = new UnOrder();
        unOrder.setCbMsgType(0);
        unOrder.setCancelReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setRefundReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setCancelReason("发起一城飞客配送异常，请主动联系一城飞客人员发配送单");
        unOrder.setCurRetryTime(0);
        unOrder.setMaxRetryTimes(0);
        unOrder.setDelayTimeLevel(0);
        unOrder.setIsEnableBackOff(false);

        // Run the test
        starDeliveryServiceImplUnderTest.starDelivery(unOrderDeliveryService, unOrder);

        // Verify the results
        verify(mockDefaultRocketMqProducer).sendMessage(any(Message.class));

        // Confirm UnOrderMqService.sendUnOrder(...).
        final UnOrder unorder = new UnOrder();
        unorder.setCbMsgType(0);
        unorder.setCancelReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unorder.setRefundReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unorder.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unorder.setCancelReason("发起一城飞客配送异常，请主动联系一城飞客人员发配送单");
        unorder.setCurRetryTime(0);
        unorder.setMaxRetryTimes(0);
        unorder.setDelayTimeLevel(0);
        unorder.setIsEnableBackOff(false);
        verify(mockUnOrderMqService).sendUnOrder(unorder);
    }

    @Test
    public void testJudgeDeliveryRetry() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setCbMsgType(0);
        unOrder.setCancelReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setRefundReqTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setCancelReason("发起一城飞客配送异常，请主动联系一城飞客人员发配送单");
        unOrder.setCurRetryTime(0);
        unOrder.setMaxRetryTimes(0);
        unOrder.setDelayTimeLevel(0);
        unOrder.setIsEnableBackOff(false);

        // Run the test
        final boolean result = starDeliveryServiceImplUnderTest.judgeDeliveryRetry(null, unOrder, false);

        // Verify the results
        assertTrue(result);
        verify(mockDefaultRocketMqProducer).sendMessage(any(Message.class));
    }
}
