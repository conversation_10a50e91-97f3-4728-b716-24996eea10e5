package com.holder.saas.store.takeaway.producers.service.job;

import org.junit.Before;
import org.junit.Test;
import org.springframework.test.util.ReflectionTestUtils;

public class OwnOrderJobTest {

    private OwnOrderJob ownOrderJobUnderTest;

    @Before
    public void setUp() throws Exception {
        ownOrderJobUnderTest = new OwnOrderJob();
        ReflectionTestUtils.setField(ownOrderJobUnderTest, "appId", 0);
    }

    @Test
    public void testQueryUnProcessOrders() {
        // Setup
        // Run the test
        ownOrderJobUnderTest.queryUnProcessOrders();

        // Verify the results
    }
}
