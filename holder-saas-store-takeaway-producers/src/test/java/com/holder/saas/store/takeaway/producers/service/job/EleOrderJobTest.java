package com.holder.saas.store.takeaway.producers.service.job;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.holder.saas.store.takeaway.producers.entity.domain.EleAuthDO;
import com.holder.saas.store.takeaway.producers.mapstruct.EleMessageMapstruct;
import com.holder.saas.store.takeaway.producers.service.EleAuthService;
import com.holder.saas.store.takeaway.producers.service.EleUnOrderParser;
import com.holder.saas.store.takeaway.producers.service.UnOrderMqService;
import com.holderzone.saas.store.dto.takeaway.UnOrder;
import eleme.openapi.sdk.api.entity.order.OOrder;
import eleme.openapi.sdk.api.entity.other.OMessage;
import eleme.openapi.sdk.config.Config;
import eleme.openapi.sdk.oauth.response.Token;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class EleOrderJobTest {

    @Mock
    private Config mockConfig;
    @Mock
    private EleAuthService mockEleAuthService;
    @Mock
    private EleUnOrderParser mockEleUnOrderParser;
    @Mock
    private UnOrderMqService mockUnOrderMqService;
    @Mock
    private EleMessageMapstruct mockEleMessageMapstruct;
    @Mock
    private StringRedisTemplate mockRedisTemplate;

    private EleOrderJob eleOrderJobUnderTest;

    @Before
    public void setUp() throws Exception {
        eleOrderJobUnderTest = new EleOrderJob(mockConfig, mockEleAuthService, mockEleUnOrderParser,
                mockUnOrderMqService, mockEleMessageMapstruct, mockRedisTemplate);
        ReflectionTestUtils.setField(eleOrderJobUnderTest, "appId", 0);
    }

    @Test
    public void testQueryUnProcessOrders1() {
        // Setup
        // Configure EleAuthService.list(...).
        final EleAuthDO eleAuthDO = new EleAuthDO();
        eleAuthDO.setId(0L);
        eleAuthDO.setGuid("6fd70509-a8a7-4fba-b54d-44e138a9e806");
        eleAuthDO.setEnterpriseGuid("enterpriseGuid");
        eleAuthDO.setStoreGuid("storeGuid");
        eleAuthDO.setShopId(0L);
        final List<EleAuthDO> eleAuthDOS = Arrays.asList(eleAuthDO);
        when(mockEleAuthService.list(any(Wrapper.class))).thenReturn(eleAuthDOS);

        // Configure EleAuthService.getToken(...).
        final Token token = new Token();
        token.setAccessToken("accessToken");
        token.setTokenType("tokenType");
        token.setExpires(0L);
        token.setRefreshToken("refreshToken");
        final EleAuthDO eleAuthDO1 = new EleAuthDO();
        eleAuthDO1.setId(0L);
        eleAuthDO1.setGuid("6fd70509-a8a7-4fba-b54d-44e138a9e806");
        eleAuthDO1.setEnterpriseGuid("enterpriseGuid");
        eleAuthDO1.setStoreGuid("storeGuid");
        eleAuthDO1.setShopId(0L);
        when(mockEleAuthService.getToken(eleAuthDO1)).thenReturn(token);

        when(mockRedisTemplate.opsForZSet()).thenReturn(null);

        // Configure EleUnOrderParser.fromOOrder(...).
        final UnOrder unOrder = new UnOrder();
        unOrder.setOrderStatus(0);
        unOrder.setOrderType(0);
        unOrder.setOrderSubType(0);
        unOrder.setEnterpriseGuid("enterpriseGuid");
        unOrder.setStoreGuid("storeGuid");
        when(mockEleUnOrderParser.fromOOrder(any(OOrder.class))).thenReturn(unOrder);

        // Run the test
        eleOrderJobUnderTest.queryUnProcessOrders();

        // Verify the results
        // Confirm UnOrderMqService.sendUnOrder(...).
        final UnOrder unorder = new UnOrder();
        unorder.setOrderStatus(0);
        unorder.setOrderType(0);
        unorder.setOrderSubType(0);
        unorder.setEnterpriseGuid("enterpriseGuid");
        unorder.setStoreGuid("storeGuid");
        verify(mockUnOrderMqService).sendUnOrder(unorder);
    }

    @Test
    public void testQueryUnProcessOrders1_EleAuthServiceListReturnsNoItems() {
        // Setup
        when(mockEleAuthService.list(any(Wrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        eleOrderJobUnderTest.queryUnProcessOrders();

        // Verify the results
    }

    @Test
    public void testQueryUnProcessOrders1_EleAuthServiceGetTokenReturnsNull() {
        // Setup
        // Configure EleAuthService.list(...).
        final EleAuthDO eleAuthDO = new EleAuthDO();
        eleAuthDO.setId(0L);
        eleAuthDO.setGuid("6fd70509-a8a7-4fba-b54d-44e138a9e806");
        eleAuthDO.setEnterpriseGuid("enterpriseGuid");
        eleAuthDO.setStoreGuid("storeGuid");
        eleAuthDO.setShopId(0L);
        final List<EleAuthDO> eleAuthDOS = Arrays.asList(eleAuthDO);
        when(mockEleAuthService.list(any(Wrapper.class))).thenReturn(eleAuthDOS);

        // Configure EleAuthService.getToken(...).
        final EleAuthDO eleAuthDO1 = new EleAuthDO();
        eleAuthDO1.setId(0L);
        eleAuthDO1.setGuid("6fd70509-a8a7-4fba-b54d-44e138a9e806");
        eleAuthDO1.setEnterpriseGuid("enterpriseGuid");
        eleAuthDO1.setStoreGuid("storeGuid");
        eleAuthDO1.setShopId(0L);
        when(mockEleAuthService.getToken(eleAuthDO1)).thenReturn(null);

        when(mockRedisTemplate.opsForZSet()).thenReturn(null);

        // Configure EleUnOrderParser.fromOOrder(...).
        final UnOrder unOrder = new UnOrder();
        unOrder.setOrderStatus(0);
        unOrder.setOrderType(0);
        unOrder.setOrderSubType(0);
        unOrder.setEnterpriseGuid("enterpriseGuid");
        unOrder.setStoreGuid("storeGuid");
        when(mockEleUnOrderParser.fromOOrder(any(OOrder.class))).thenReturn(unOrder);

        // Run the test
        eleOrderJobUnderTest.queryUnProcessOrders();

        // Verify the results
        // Confirm UnOrderMqService.sendUnOrder(...).
        final UnOrder unorder = new UnOrder();
        unorder.setOrderStatus(0);
        unorder.setOrderType(0);
        unorder.setOrderSubType(0);
        unorder.setEnterpriseGuid("enterpriseGuid");
        unorder.setStoreGuid("storeGuid");
        verify(mockUnOrderMqService).sendUnOrder(unorder);
    }

    @Test
    public void testHandleUnProcessOrders() {
        // Setup
        // Configure EleAuthService.list(...).
        final EleAuthDO eleAuthDO = new EleAuthDO();
        eleAuthDO.setId(0L);
        eleAuthDO.setGuid("6fd70509-a8a7-4fba-b54d-44e138a9e806");
        eleAuthDO.setEnterpriseGuid("enterpriseGuid");
        eleAuthDO.setStoreGuid("storeGuid");
        eleAuthDO.setShopId(0L);
        final List<EleAuthDO> eleAuthDOS = Arrays.asList(eleAuthDO);
        when(mockEleAuthService.list(any(Wrapper.class))).thenReturn(eleAuthDOS);

        // Configure EleAuthService.getToken(...).
        final Token token = new Token();
        token.setAccessToken("accessToken");
        token.setTokenType("tokenType");
        token.setExpires(0L);
        token.setRefreshToken("refreshToken");
        final EleAuthDO eleAuthDO1 = new EleAuthDO();
        eleAuthDO1.setId(0L);
        eleAuthDO1.setGuid("6fd70509-a8a7-4fba-b54d-44e138a9e806");
        eleAuthDO1.setEnterpriseGuid("enterpriseGuid");
        eleAuthDO1.setStoreGuid("storeGuid");
        eleAuthDO1.setShopId(0L);
        when(mockEleAuthService.getToken(eleAuthDO1)).thenReturn(token);

        when(mockRedisTemplate.opsForZSet()).thenReturn(null);

        // Configure EleUnOrderParser.fromOOrder(...).
        final UnOrder unOrder = new UnOrder();
        unOrder.setOrderStatus(0);
        unOrder.setOrderType(0);
        unOrder.setOrderSubType(0);
        unOrder.setEnterpriseGuid("enterpriseGuid");
        unOrder.setStoreGuid("storeGuid");
        when(mockEleUnOrderParser.fromOOrder(any(OOrder.class))).thenReturn(unOrder);

        // Run the test
        eleOrderJobUnderTest.handleUnProcessOrders();

        // Verify the results
        // Confirm UnOrderMqService.sendUnOrder(...).
        final UnOrder unorder = new UnOrder();
        unorder.setOrderStatus(0);
        unorder.setOrderType(0);
        unorder.setOrderSubType(0);
        unorder.setEnterpriseGuid("enterpriseGuid");
        unorder.setStoreGuid("storeGuid");
        verify(mockUnOrderMqService).sendUnOrder(unorder);
    }

    @Test
    public void testHandleUnProcessOrders_EleAuthServiceListReturnsNoItems() {
        // Setup
        when(mockEleAuthService.list(any(Wrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        eleOrderJobUnderTest.handleUnProcessOrders();

        // Verify the results
    }

    @Test
    public void testHandleUnProcessOrders_EleAuthServiceGetTokenReturnsNull() {
        // Setup
        // Configure EleAuthService.list(...).
        final EleAuthDO eleAuthDO = new EleAuthDO();
        eleAuthDO.setId(0L);
        eleAuthDO.setGuid("6fd70509-a8a7-4fba-b54d-44e138a9e806");
        eleAuthDO.setEnterpriseGuid("enterpriseGuid");
        eleAuthDO.setStoreGuid("storeGuid");
        eleAuthDO.setShopId(0L);
        final List<EleAuthDO> eleAuthDOS = Arrays.asList(eleAuthDO);
        when(mockEleAuthService.list(any(Wrapper.class))).thenReturn(eleAuthDOS);

        // Configure EleAuthService.getToken(...).
        final EleAuthDO eleAuthDO1 = new EleAuthDO();
        eleAuthDO1.setId(0L);
        eleAuthDO1.setGuid("6fd70509-a8a7-4fba-b54d-44e138a9e806");
        eleAuthDO1.setEnterpriseGuid("enterpriseGuid");
        eleAuthDO1.setStoreGuid("storeGuid");
        eleAuthDO1.setShopId(0L);
        when(mockEleAuthService.getToken(eleAuthDO1)).thenReturn(null);

        when(mockRedisTemplate.opsForZSet()).thenReturn(null);

        // Configure EleUnOrderParser.fromOOrder(...).
        final UnOrder unOrder = new UnOrder();
        unOrder.setOrderStatus(0);
        unOrder.setOrderType(0);
        unOrder.setOrderSubType(0);
        unOrder.setEnterpriseGuid("enterpriseGuid");
        unOrder.setStoreGuid("storeGuid");
        when(mockEleUnOrderParser.fromOOrder(any(OOrder.class))).thenReturn(unOrder);

        // Run the test
        eleOrderJobUnderTest.handleUnProcessOrders();

        // Verify the results
        // Confirm UnOrderMqService.sendUnOrder(...).
        final UnOrder unorder = new UnOrder();
        unorder.setOrderStatus(0);
        unorder.setOrderType(0);
        unorder.setOrderSubType(0);
        unorder.setEnterpriseGuid("enterpriseGuid");
        unorder.setStoreGuid("storeGuid");
        verify(mockUnOrderMqService).sendUnOrder(unorder);
    }

    @Test
    public void testHandleUnProcessOrdersByNonReachedOMessages() {
        // Setup
        // Configure EleAuthService.list(...).
        final EleAuthDO eleAuthDO = new EleAuthDO();
        eleAuthDO.setId(0L);
        eleAuthDO.setGuid("6fd70509-a8a7-4fba-b54d-44e138a9e806");
        eleAuthDO.setEnterpriseGuid("enterpriseGuid");
        eleAuthDO.setStoreGuid("storeGuid");
        eleAuthDO.setShopId(0L);
        final List<EleAuthDO> eleAuthDOS = Arrays.asList(eleAuthDO);
        when(mockEleAuthService.list(any(Wrapper.class))).thenReturn(eleAuthDOS);

        // Configure EleAuthService.getToken(...).
        final Token token = new Token();
        token.setAccessToken("accessToken");
        token.setTokenType("tokenType");
        token.setExpires(0L);
        token.setRefreshToken("refreshToken");
        final EleAuthDO eleAuthDO1 = new EleAuthDO();
        eleAuthDO1.setId(0L);
        eleAuthDO1.setGuid("6fd70509-a8a7-4fba-b54d-44e138a9e806");
        eleAuthDO1.setEnterpriseGuid("enterpriseGuid");
        eleAuthDO1.setStoreGuid("storeGuid");
        eleAuthDO1.setShopId(0L);
        when(mockEleAuthService.getToken(eleAuthDO1)).thenReturn(token);

        // Configure EleMessageMapstruct.fromOMessage(...).
        final OMessage oMessage = new OMessage();
        oMessage.setRequestId("requestId");
        oMessage.setType(0);
        oMessage.setAppId(0);
        oMessage.setMessage("message");
        oMessage.setShopId(0L);
        when(mockEleMessageMapstruct.fromOMessage(any(eleme.openapi.sdk.api.entity.message.OMessage.class)))
                .thenReturn(oMessage);

        // Configure EleUnOrderParser.fromOrderCreated(...).
        final UnOrder unOrder = new UnOrder();
        unOrder.setOrderStatus(0);
        unOrder.setOrderType(0);
        unOrder.setOrderSubType(0);
        unOrder.setEnterpriseGuid("enterpriseGuid");
        unOrder.setStoreGuid("storeGuid");
        when(mockEleUnOrderParser.fromOrderCreated(any(OMessage.class))).thenReturn(unOrder);

        // Run the test
        eleOrderJobUnderTest.handleUnProcessOrdersByNonReachedOMessages();

        // Verify the results
        // Confirm UnOrderMqService.sendUnOrder(...).
        final UnOrder unorder = new UnOrder();
        unorder.setOrderStatus(0);
        unorder.setOrderType(0);
        unorder.setOrderSubType(0);
        unorder.setEnterpriseGuid("enterpriseGuid");
        unorder.setStoreGuid("storeGuid");
        verify(mockUnOrderMqService).sendUnOrder(unorder);
    }

    @Test
    public void testHandleUnProcessOrdersByNonReachedOMessages_EleAuthServiceListReturnsNoItems() {
        // Setup
        when(mockEleAuthService.list(any(Wrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        eleOrderJobUnderTest.handleUnProcessOrdersByNonReachedOMessages();

        // Verify the results
    }

    @Test
    public void testHandleUnProcessOrdersByNonReachedOMessages_EleAuthServiceGetTokenReturnsNull() {
        // Setup
        // Configure EleAuthService.list(...).
        final EleAuthDO eleAuthDO = new EleAuthDO();
        eleAuthDO.setId(0L);
        eleAuthDO.setGuid("6fd70509-a8a7-4fba-b54d-44e138a9e806");
        eleAuthDO.setEnterpriseGuid("enterpriseGuid");
        eleAuthDO.setStoreGuid("storeGuid");
        eleAuthDO.setShopId(0L);
        final List<EleAuthDO> eleAuthDOS = Arrays.asList(eleAuthDO);
        when(mockEleAuthService.list(any(Wrapper.class))).thenReturn(eleAuthDOS);

        // Configure EleAuthService.getToken(...).
        final EleAuthDO eleAuthDO1 = new EleAuthDO();
        eleAuthDO1.setId(0L);
        eleAuthDO1.setGuid("6fd70509-a8a7-4fba-b54d-44e138a9e806");
        eleAuthDO1.setEnterpriseGuid("enterpriseGuid");
        eleAuthDO1.setStoreGuid("storeGuid");
        eleAuthDO1.setShopId(0L);
        when(mockEleAuthService.getToken(eleAuthDO1)).thenReturn(null);

        // Run the test
        eleOrderJobUnderTest.handleUnProcessOrdersByNonReachedOMessages();

        // Verify the results
    }
}
