package com.holder.saas.store.takeaway.producers.service.impl;

import com.holder.saas.store.takeaway.producers.entity.domain.MtAuthDO;
import com.holder.saas.store.takeaway.producers.service.MtAuthService;
import com.holder.saas.store.takeaway.producers.service.UnOrderMqService;
import com.holder.saas.store.takeaway.producers.service.rpc.ConsumersFeignService;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.takeaway.OwnApiResult;
import com.holderzone.saas.store.dto.takeaway.ThirdCarrierUpdateDTO;
import com.holderzone.saas.store.dto.takeaway.UnOrder;
import com.holderzone.saas.store.dto.takeaway.request.FoodListDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class MtOrderReplyServiceImplTest {

    @Mock
    private MtAuthService mockAuthService;
    @Mock
    private UnOrderMqService mockUnOrderMqService;
    @Mock
    private ConsumersFeignService mockConsumersFeignService;

    private MtOrderReplyServiceImpl mtOrderReplyServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        mtOrderReplyServiceImplUnderTest = new MtOrderReplyServiceImpl(mockAuthService, mockUnOrderMqService,
                mockConsumersFeignService);
        ReflectionTestUtils.setField(mtOrderReplyServiceImplUnderTest, "mtSignKey", "mtSignKey");
        ReflectionTestUtils.setField(mtOrderReplyServiceImplUnderTest, "deliveryUrl", "deliveryUrl");
        ReflectionTestUtils.setField(mtOrderReplyServiceImplUnderTest, "cancelDeliveryUrl", "cancelDeliveryUrl");
    }

    @Test
    public void testReplyCancelOrder() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setOrderStatus(0);
        unOrder.setShopName("ShopName");
        unOrder.setCbMsgType(0);
        unOrder.setReplyMsgType(0);
        unOrder.setOrderSubType(0);
        unOrder.setEnterpriseGuid("enterpriseGuid");
        unOrder.setStoreGuid("ShopNo");
        unOrder.setOrderId("orderId");
        unOrder.setOrderDaySn("orderDaySn");
        unOrder.setOrderRemark("orderRemark");
        unOrder.setReserve(false);
        unOrder.setCustomerName("customerName");
        unOrder.setCustomerPhone("customerPhone");
        unOrder.setPrivacyPhone("privacyPhone");
        unOrder.setCustomerAddress("customerAddress");
        unOrder.setShipLatitude("latitude");
        unOrder.setShipLongitude("longitude");
        unOrder.setShipperName("courierName");
        unOrder.setShipperPhone("courierPhone");
        unOrder.setInvoiced(false);
        unOrder.setInvoiceType(0);
        unOrder.setInvoiceTitle("invoiceTitle");
        unOrder.setTaxpayerId("taxpayerId");
        unOrder.setTotal(new BigDecimal("0.00"));
        unOrder.setShipTotal(new BigDecimal("0.00"));
        unOrder.setPackageTotal(new BigDecimal("0.00"));
        unOrder.setDiscountTotal(new BigDecimal("0.00"));
        unOrder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unOrder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setCancelReplyMessage("cancelReplyMessage");
        unOrder.setRefundReplyMessage("refundReplyMessage");
        unOrder.setCancelReason("cancelReason");
        final FoodListDTO foodListDTO = new FoodListDTO();
        unOrder.setFoodLists(Arrays.asList(foodListDTO));
        unOrder.setThirdCarrierId("thirdCarrierId");

        // Configure MtAuthService.getAuth(...).
        final MtAuthDO mtAuthDO = new MtAuthDO();
        mtAuthDO.setId(0);
        mtAuthDO.setGuid("7adec2bf-7244-4a9b-a438-614af94ac6e8");
        mtAuthDO.setEPoiId("ePoiId");
        mtAuthDO.setEnterpriseGuid("enterpriseGuid");
        mtAuthDO.setAccessToken("accessToken");
        when(mockAuthService.getAuth("ShopNo", 0)).thenReturn(mtAuthDO);

        // Run the test
        mtOrderReplyServiceImplUnderTest.replyCancelOrder(unOrder);

        // Verify the results
        // Confirm UnOrderMqService.sendUnOrder(...).
        final UnOrder unorder = new UnOrder();
        unorder.setOrderStatus(0);
        unorder.setShopName("ShopName");
        unorder.setCbMsgType(0);
        unorder.setReplyMsgType(0);
        unorder.setOrderSubType(0);
        unorder.setEnterpriseGuid("enterpriseGuid");
        unorder.setStoreGuid("ShopNo");
        unorder.setOrderId("orderId");
        unorder.setOrderDaySn("orderDaySn");
        unorder.setOrderRemark("orderRemark");
        unorder.setReserve(false);
        unorder.setCustomerName("customerName");
        unorder.setCustomerPhone("customerPhone");
        unorder.setPrivacyPhone("privacyPhone");
        unorder.setCustomerAddress("customerAddress");
        unorder.setShipLatitude("latitude");
        unorder.setShipLongitude("longitude");
        unorder.setShipperName("courierName");
        unorder.setShipperPhone("courierPhone");
        unorder.setInvoiced(false);
        unorder.setInvoiceType(0);
        unorder.setInvoiceTitle("invoiceTitle");
        unorder.setTaxpayerId("taxpayerId");
        unorder.setTotal(new BigDecimal("0.00"));
        unorder.setShipTotal(new BigDecimal("0.00"));
        unorder.setPackageTotal(new BigDecimal("0.00"));
        unorder.setDiscountTotal(new BigDecimal("0.00"));
        unorder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unorder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unorder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unorder.setCancelReplyMessage("cancelReplyMessage");
        unorder.setRefundReplyMessage("refundReplyMessage");
        unorder.setCancelReason("cancelReason");
        final FoodListDTO foodListDTO1 = new FoodListDTO();
        unorder.setFoodLists(Arrays.asList(foodListDTO1));
        unorder.setThirdCarrierId("thirdCarrierId");
        verify(mockUnOrderMqService).sendUnOrder(unorder);
    }

    @Test(expected = BusinessException.class)
    public void testReplyCancelOrder_MtAuthServiceGetAuthReturnsNull() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setOrderStatus(0);
        unOrder.setShopName("ShopName");
        unOrder.setCbMsgType(0);
        unOrder.setReplyMsgType(0);
        unOrder.setOrderSubType(0);
        unOrder.setEnterpriseGuid("enterpriseGuid");
        unOrder.setStoreGuid("ShopNo");
        unOrder.setOrderId("orderId");
        unOrder.setOrderDaySn("orderDaySn");
        unOrder.setOrderRemark("orderRemark");
        unOrder.setReserve(false);
        unOrder.setCustomerName("customerName");
        unOrder.setCustomerPhone("customerPhone");
        unOrder.setPrivacyPhone("privacyPhone");
        unOrder.setCustomerAddress("customerAddress");
        unOrder.setShipLatitude("latitude");
        unOrder.setShipLongitude("longitude");
        unOrder.setShipperName("courierName");
        unOrder.setShipperPhone("courierPhone");
        unOrder.setInvoiced(false);
        unOrder.setInvoiceType(0);
        unOrder.setInvoiceTitle("invoiceTitle");
        unOrder.setTaxpayerId("taxpayerId");
        unOrder.setTotal(new BigDecimal("0.00"));
        unOrder.setShipTotal(new BigDecimal("0.00"));
        unOrder.setPackageTotal(new BigDecimal("0.00"));
        unOrder.setDiscountTotal(new BigDecimal("0.00"));
        unOrder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unOrder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setCancelReplyMessage("cancelReplyMessage");
        unOrder.setRefundReplyMessage("refundReplyMessage");
        unOrder.setCancelReason("cancelReason");
        final FoodListDTO foodListDTO = new FoodListDTO();
        unOrder.setFoodLists(Arrays.asList(foodListDTO));
        unOrder.setThirdCarrierId("thirdCarrierId");

        when(mockAuthService.getAuth("ShopNo", 0)).thenReturn(null);

        // Run the test
        mtOrderReplyServiceImplUnderTest.replyCancelOrder(unOrder);
    }

    @Test
    public void testReplyConfirmOrder() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setOrderStatus(0);
        unOrder.setShopName("ShopName");
        unOrder.setCbMsgType(0);
        unOrder.setReplyMsgType(0);
        unOrder.setOrderSubType(0);
        unOrder.setEnterpriseGuid("enterpriseGuid");
        unOrder.setStoreGuid("ShopNo");
        unOrder.setOrderId("orderId");
        unOrder.setOrderDaySn("orderDaySn");
        unOrder.setOrderRemark("orderRemark");
        unOrder.setReserve(false);
        unOrder.setCustomerName("customerName");
        unOrder.setCustomerPhone("customerPhone");
        unOrder.setPrivacyPhone("privacyPhone");
        unOrder.setCustomerAddress("customerAddress");
        unOrder.setShipLatitude("latitude");
        unOrder.setShipLongitude("longitude");
        unOrder.setShipperName("courierName");
        unOrder.setShipperPhone("courierPhone");
        unOrder.setInvoiced(false);
        unOrder.setInvoiceType(0);
        unOrder.setInvoiceTitle("invoiceTitle");
        unOrder.setTaxpayerId("taxpayerId");
        unOrder.setTotal(new BigDecimal("0.00"));
        unOrder.setShipTotal(new BigDecimal("0.00"));
        unOrder.setPackageTotal(new BigDecimal("0.00"));
        unOrder.setDiscountTotal(new BigDecimal("0.00"));
        unOrder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unOrder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setCancelReplyMessage("cancelReplyMessage");
        unOrder.setRefundReplyMessage("refundReplyMessage");
        unOrder.setCancelReason("cancelReason");
        final FoodListDTO foodListDTO = new FoodListDTO();
        unOrder.setFoodLists(Arrays.asList(foodListDTO));
        unOrder.setThirdCarrierId("thirdCarrierId");

        // Configure MtAuthService.getAuth(...).
        final MtAuthDO mtAuthDO = new MtAuthDO();
        mtAuthDO.setId(0);
        mtAuthDO.setGuid("7adec2bf-7244-4a9b-a438-614af94ac6e8");
        mtAuthDO.setEPoiId("ePoiId");
        mtAuthDO.setEnterpriseGuid("enterpriseGuid");
        mtAuthDO.setAccessToken("accessToken");
        when(mockAuthService.getAuth("ShopNo", 0)).thenReturn(mtAuthDO);

        // Run the test
        mtOrderReplyServiceImplUnderTest.replyConfirmOrder(unOrder);

        // Verify the results
    }

    @Test(expected = BusinessException.class)
    public void testReplyConfirmOrder_MtAuthServiceGetAuthReturnsNull() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setOrderStatus(0);
        unOrder.setShopName("ShopName");
        unOrder.setCbMsgType(0);
        unOrder.setReplyMsgType(0);
        unOrder.setOrderSubType(0);
        unOrder.setEnterpriseGuid("enterpriseGuid");
        unOrder.setStoreGuid("ShopNo");
        unOrder.setOrderId("orderId");
        unOrder.setOrderDaySn("orderDaySn");
        unOrder.setOrderRemark("orderRemark");
        unOrder.setReserve(false);
        unOrder.setCustomerName("customerName");
        unOrder.setCustomerPhone("customerPhone");
        unOrder.setPrivacyPhone("privacyPhone");
        unOrder.setCustomerAddress("customerAddress");
        unOrder.setShipLatitude("latitude");
        unOrder.setShipLongitude("longitude");
        unOrder.setShipperName("courierName");
        unOrder.setShipperPhone("courierPhone");
        unOrder.setInvoiced(false);
        unOrder.setInvoiceType(0);
        unOrder.setInvoiceTitle("invoiceTitle");
        unOrder.setTaxpayerId("taxpayerId");
        unOrder.setTotal(new BigDecimal("0.00"));
        unOrder.setShipTotal(new BigDecimal("0.00"));
        unOrder.setPackageTotal(new BigDecimal("0.00"));
        unOrder.setDiscountTotal(new BigDecimal("0.00"));
        unOrder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unOrder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setCancelReplyMessage("cancelReplyMessage");
        unOrder.setRefundReplyMessage("refundReplyMessage");
        unOrder.setCancelReason("cancelReason");
        final FoodListDTO foodListDTO = new FoodListDTO();
        unOrder.setFoodLists(Arrays.asList(foodListDTO));
        unOrder.setThirdCarrierId("thirdCarrierId");

        when(mockAuthService.getAuth("ShopNo", 0)).thenReturn(null);

        // Run the test
        mtOrderReplyServiceImplUnderTest.replyConfirmOrder(unOrder);
    }

    @Test
    public void testReplyAgreeCancelOrder() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setOrderStatus(0);
        unOrder.setShopName("ShopName");
        unOrder.setCbMsgType(0);
        unOrder.setReplyMsgType(0);
        unOrder.setOrderSubType(0);
        unOrder.setEnterpriseGuid("enterpriseGuid");
        unOrder.setStoreGuid("ShopNo");
        unOrder.setOrderId("orderId");
        unOrder.setOrderDaySn("orderDaySn");
        unOrder.setOrderRemark("orderRemark");
        unOrder.setReserve(false);
        unOrder.setCustomerName("customerName");
        unOrder.setCustomerPhone("customerPhone");
        unOrder.setPrivacyPhone("privacyPhone");
        unOrder.setCustomerAddress("customerAddress");
        unOrder.setShipLatitude("latitude");
        unOrder.setShipLongitude("longitude");
        unOrder.setShipperName("courierName");
        unOrder.setShipperPhone("courierPhone");
        unOrder.setInvoiced(false);
        unOrder.setInvoiceType(0);
        unOrder.setInvoiceTitle("invoiceTitle");
        unOrder.setTaxpayerId("taxpayerId");
        unOrder.setTotal(new BigDecimal("0.00"));
        unOrder.setShipTotal(new BigDecimal("0.00"));
        unOrder.setPackageTotal(new BigDecimal("0.00"));
        unOrder.setDiscountTotal(new BigDecimal("0.00"));
        unOrder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unOrder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setCancelReplyMessage("cancelReplyMessage");
        unOrder.setRefundReplyMessage("refundReplyMessage");
        unOrder.setCancelReason("cancelReason");
        final FoodListDTO foodListDTO = new FoodListDTO();
        unOrder.setFoodLists(Arrays.asList(foodListDTO));
        unOrder.setThirdCarrierId("thirdCarrierId");

        // Configure MtAuthService.getAuth(...).
        final MtAuthDO mtAuthDO = new MtAuthDO();
        mtAuthDO.setId(0);
        mtAuthDO.setGuid("7adec2bf-7244-4a9b-a438-614af94ac6e8");
        mtAuthDO.setEPoiId("ePoiId");
        mtAuthDO.setEnterpriseGuid("enterpriseGuid");
        mtAuthDO.setAccessToken("accessToken");
        when(mockAuthService.getAuth("ShopNo", 0)).thenReturn(mtAuthDO);

        // Run the test
        mtOrderReplyServiceImplUnderTest.replyAgreeCancelOrder(unOrder);

        // Verify the results
    }

    @Test(expected = BusinessException.class)
    public void testReplyAgreeCancelOrder_MtAuthServiceGetAuthReturnsNull() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setOrderStatus(0);
        unOrder.setShopName("ShopName");
        unOrder.setCbMsgType(0);
        unOrder.setReplyMsgType(0);
        unOrder.setOrderSubType(0);
        unOrder.setEnterpriseGuid("enterpriseGuid");
        unOrder.setStoreGuid("ShopNo");
        unOrder.setOrderId("orderId");
        unOrder.setOrderDaySn("orderDaySn");
        unOrder.setOrderRemark("orderRemark");
        unOrder.setReserve(false);
        unOrder.setCustomerName("customerName");
        unOrder.setCustomerPhone("customerPhone");
        unOrder.setPrivacyPhone("privacyPhone");
        unOrder.setCustomerAddress("customerAddress");
        unOrder.setShipLatitude("latitude");
        unOrder.setShipLongitude("longitude");
        unOrder.setShipperName("courierName");
        unOrder.setShipperPhone("courierPhone");
        unOrder.setInvoiced(false);
        unOrder.setInvoiceType(0);
        unOrder.setInvoiceTitle("invoiceTitle");
        unOrder.setTaxpayerId("taxpayerId");
        unOrder.setTotal(new BigDecimal("0.00"));
        unOrder.setShipTotal(new BigDecimal("0.00"));
        unOrder.setPackageTotal(new BigDecimal("0.00"));
        unOrder.setDiscountTotal(new BigDecimal("0.00"));
        unOrder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unOrder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setCancelReplyMessage("cancelReplyMessage");
        unOrder.setRefundReplyMessage("refundReplyMessage");
        unOrder.setCancelReason("cancelReason");
        final FoodListDTO foodListDTO = new FoodListDTO();
        unOrder.setFoodLists(Arrays.asList(foodListDTO));
        unOrder.setThirdCarrierId("thirdCarrierId");

        when(mockAuthService.getAuth("ShopNo", 0)).thenReturn(null);

        // Run the test
        mtOrderReplyServiceImplUnderTest.replyAgreeCancelOrder(unOrder);
    }

    @Test
    public void testReplyDisagreeCancelOrder() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setOrderStatus(0);
        unOrder.setShopName("ShopName");
        unOrder.setCbMsgType(0);
        unOrder.setReplyMsgType(0);
        unOrder.setOrderSubType(0);
        unOrder.setEnterpriseGuid("enterpriseGuid");
        unOrder.setStoreGuid("ShopNo");
        unOrder.setOrderId("orderId");
        unOrder.setOrderDaySn("orderDaySn");
        unOrder.setOrderRemark("orderRemark");
        unOrder.setReserve(false);
        unOrder.setCustomerName("customerName");
        unOrder.setCustomerPhone("customerPhone");
        unOrder.setPrivacyPhone("privacyPhone");
        unOrder.setCustomerAddress("customerAddress");
        unOrder.setShipLatitude("latitude");
        unOrder.setShipLongitude("longitude");
        unOrder.setShipperName("courierName");
        unOrder.setShipperPhone("courierPhone");
        unOrder.setInvoiced(false);
        unOrder.setInvoiceType(0);
        unOrder.setInvoiceTitle("invoiceTitle");
        unOrder.setTaxpayerId("taxpayerId");
        unOrder.setTotal(new BigDecimal("0.00"));
        unOrder.setShipTotal(new BigDecimal("0.00"));
        unOrder.setPackageTotal(new BigDecimal("0.00"));
        unOrder.setDiscountTotal(new BigDecimal("0.00"));
        unOrder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unOrder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setCancelReplyMessage("cancelReplyMessage");
        unOrder.setRefundReplyMessage("refundReplyMessage");
        unOrder.setCancelReason("cancelReason");
        final FoodListDTO foodListDTO = new FoodListDTO();
        unOrder.setFoodLists(Arrays.asList(foodListDTO));
        unOrder.setThirdCarrierId("thirdCarrierId");

        // Configure MtAuthService.getAuth(...).
        final MtAuthDO mtAuthDO = new MtAuthDO();
        mtAuthDO.setId(0);
        mtAuthDO.setGuid("7adec2bf-7244-4a9b-a438-614af94ac6e8");
        mtAuthDO.setEPoiId("ePoiId");
        mtAuthDO.setEnterpriseGuid("enterpriseGuid");
        mtAuthDO.setAccessToken("accessToken");
        when(mockAuthService.getAuth("ShopNo", 0)).thenReturn(mtAuthDO);

        // Run the test
        mtOrderReplyServiceImplUnderTest.replyDisagreeCancelOrder(unOrder);

        // Verify the results
    }

    @Test(expected = BusinessException.class)
    public void testReplyDisagreeCancelOrder_MtAuthServiceGetAuthReturnsNull() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setOrderStatus(0);
        unOrder.setShopName("ShopName");
        unOrder.setCbMsgType(0);
        unOrder.setReplyMsgType(0);
        unOrder.setOrderSubType(0);
        unOrder.setEnterpriseGuid("enterpriseGuid");
        unOrder.setStoreGuid("ShopNo");
        unOrder.setOrderId("orderId");
        unOrder.setOrderDaySn("orderDaySn");
        unOrder.setOrderRemark("orderRemark");
        unOrder.setReserve(false);
        unOrder.setCustomerName("customerName");
        unOrder.setCustomerPhone("customerPhone");
        unOrder.setPrivacyPhone("privacyPhone");
        unOrder.setCustomerAddress("customerAddress");
        unOrder.setShipLatitude("latitude");
        unOrder.setShipLongitude("longitude");
        unOrder.setShipperName("courierName");
        unOrder.setShipperPhone("courierPhone");
        unOrder.setInvoiced(false);
        unOrder.setInvoiceType(0);
        unOrder.setInvoiceTitle("invoiceTitle");
        unOrder.setTaxpayerId("taxpayerId");
        unOrder.setTotal(new BigDecimal("0.00"));
        unOrder.setShipTotal(new BigDecimal("0.00"));
        unOrder.setPackageTotal(new BigDecimal("0.00"));
        unOrder.setDiscountTotal(new BigDecimal("0.00"));
        unOrder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unOrder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setCancelReplyMessage("cancelReplyMessage");
        unOrder.setRefundReplyMessage("refundReplyMessage");
        unOrder.setCancelReason("cancelReason");
        final FoodListDTO foodListDTO = new FoodListDTO();
        unOrder.setFoodLists(Arrays.asList(foodListDTO));
        unOrder.setThirdCarrierId("thirdCarrierId");

        when(mockAuthService.getAuth("ShopNo", 0)).thenReturn(null);

        // Run the test
        mtOrderReplyServiceImplUnderTest.replyDisagreeCancelOrder(unOrder);
    }

    @Test
    public void testReplyAgreeRefundOrder() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setOrderStatus(0);
        unOrder.setShopName("ShopName");
        unOrder.setCbMsgType(0);
        unOrder.setReplyMsgType(0);
        unOrder.setOrderSubType(0);
        unOrder.setEnterpriseGuid("enterpriseGuid");
        unOrder.setStoreGuid("ShopNo");
        unOrder.setOrderId("orderId");
        unOrder.setOrderDaySn("orderDaySn");
        unOrder.setOrderRemark("orderRemark");
        unOrder.setReserve(false);
        unOrder.setCustomerName("customerName");
        unOrder.setCustomerPhone("customerPhone");
        unOrder.setPrivacyPhone("privacyPhone");
        unOrder.setCustomerAddress("customerAddress");
        unOrder.setShipLatitude("latitude");
        unOrder.setShipLongitude("longitude");
        unOrder.setShipperName("courierName");
        unOrder.setShipperPhone("courierPhone");
        unOrder.setInvoiced(false);
        unOrder.setInvoiceType(0);
        unOrder.setInvoiceTitle("invoiceTitle");
        unOrder.setTaxpayerId("taxpayerId");
        unOrder.setTotal(new BigDecimal("0.00"));
        unOrder.setShipTotal(new BigDecimal("0.00"));
        unOrder.setPackageTotal(new BigDecimal("0.00"));
        unOrder.setDiscountTotal(new BigDecimal("0.00"));
        unOrder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unOrder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setCancelReplyMessage("cancelReplyMessage");
        unOrder.setRefundReplyMessage("refundReplyMessage");
        unOrder.setCancelReason("cancelReason");
        final FoodListDTO foodListDTO = new FoodListDTO();
        unOrder.setFoodLists(Arrays.asList(foodListDTO));
        unOrder.setThirdCarrierId("thirdCarrierId");

        // Configure MtAuthService.getAuth(...).
        final MtAuthDO mtAuthDO = new MtAuthDO();
        mtAuthDO.setId(0);
        mtAuthDO.setGuid("7adec2bf-7244-4a9b-a438-614af94ac6e8");
        mtAuthDO.setEPoiId("ePoiId");
        mtAuthDO.setEnterpriseGuid("enterpriseGuid");
        mtAuthDO.setAccessToken("accessToken");
        when(mockAuthService.getAuth("ShopNo", 0)).thenReturn(mtAuthDO);

        // Run the test
        mtOrderReplyServiceImplUnderTest.replyAgreeRefundOrder(unOrder);

        // Verify the results
    }

    @Test(expected = BusinessException.class)
    public void testReplyAgreeRefundOrder_MtAuthServiceGetAuthReturnsNull() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setOrderStatus(0);
        unOrder.setShopName("ShopName");
        unOrder.setCbMsgType(0);
        unOrder.setReplyMsgType(0);
        unOrder.setOrderSubType(0);
        unOrder.setEnterpriseGuid("enterpriseGuid");
        unOrder.setStoreGuid("ShopNo");
        unOrder.setOrderId("orderId");
        unOrder.setOrderDaySn("orderDaySn");
        unOrder.setOrderRemark("orderRemark");
        unOrder.setReserve(false);
        unOrder.setCustomerName("customerName");
        unOrder.setCustomerPhone("customerPhone");
        unOrder.setPrivacyPhone("privacyPhone");
        unOrder.setCustomerAddress("customerAddress");
        unOrder.setShipLatitude("latitude");
        unOrder.setShipLongitude("longitude");
        unOrder.setShipperName("courierName");
        unOrder.setShipperPhone("courierPhone");
        unOrder.setInvoiced(false);
        unOrder.setInvoiceType(0);
        unOrder.setInvoiceTitle("invoiceTitle");
        unOrder.setTaxpayerId("taxpayerId");
        unOrder.setTotal(new BigDecimal("0.00"));
        unOrder.setShipTotal(new BigDecimal("0.00"));
        unOrder.setPackageTotal(new BigDecimal("0.00"));
        unOrder.setDiscountTotal(new BigDecimal("0.00"));
        unOrder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unOrder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setCancelReplyMessage("cancelReplyMessage");
        unOrder.setRefundReplyMessage("refundReplyMessage");
        unOrder.setCancelReason("cancelReason");
        final FoodListDTO foodListDTO = new FoodListDTO();
        unOrder.setFoodLists(Arrays.asList(foodListDTO));
        unOrder.setThirdCarrierId("thirdCarrierId");

        when(mockAuthService.getAuth("ShopNo", 0)).thenReturn(null);

        // Run the test
        mtOrderReplyServiceImplUnderTest.replyAgreeRefundOrder(unOrder);
    }

    @Test
    public void testReplyDisagreeRefundOrder() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setOrderStatus(0);
        unOrder.setShopName("ShopName");
        unOrder.setCbMsgType(0);
        unOrder.setReplyMsgType(0);
        unOrder.setOrderSubType(0);
        unOrder.setEnterpriseGuid("enterpriseGuid");
        unOrder.setStoreGuid("ShopNo");
        unOrder.setOrderId("orderId");
        unOrder.setOrderDaySn("orderDaySn");
        unOrder.setOrderRemark("orderRemark");
        unOrder.setReserve(false);
        unOrder.setCustomerName("customerName");
        unOrder.setCustomerPhone("customerPhone");
        unOrder.setPrivacyPhone("privacyPhone");
        unOrder.setCustomerAddress("customerAddress");
        unOrder.setShipLatitude("latitude");
        unOrder.setShipLongitude("longitude");
        unOrder.setShipperName("courierName");
        unOrder.setShipperPhone("courierPhone");
        unOrder.setInvoiced(false);
        unOrder.setInvoiceType(0);
        unOrder.setInvoiceTitle("invoiceTitle");
        unOrder.setTaxpayerId("taxpayerId");
        unOrder.setTotal(new BigDecimal("0.00"));
        unOrder.setShipTotal(new BigDecimal("0.00"));
        unOrder.setPackageTotal(new BigDecimal("0.00"));
        unOrder.setDiscountTotal(new BigDecimal("0.00"));
        unOrder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unOrder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setCancelReplyMessage("cancelReplyMessage");
        unOrder.setRefundReplyMessage("refundReplyMessage");
        unOrder.setCancelReason("cancelReason");
        final FoodListDTO foodListDTO = new FoodListDTO();
        unOrder.setFoodLists(Arrays.asList(foodListDTO));
        unOrder.setThirdCarrierId("thirdCarrierId");

        // Configure MtAuthService.getAuth(...).
        final MtAuthDO mtAuthDO = new MtAuthDO();
        mtAuthDO.setId(0);
        mtAuthDO.setGuid("7adec2bf-7244-4a9b-a438-614af94ac6e8");
        mtAuthDO.setEPoiId("ePoiId");
        mtAuthDO.setEnterpriseGuid("enterpriseGuid");
        mtAuthDO.setAccessToken("accessToken");
        when(mockAuthService.getAuth("ShopNo", 0)).thenReturn(mtAuthDO);

        // Run the test
        mtOrderReplyServiceImplUnderTest.replyDisagreeRefundOrder(unOrder);

        // Verify the results
    }

    @Test(expected = BusinessException.class)
    public void testReplyDisagreeRefundOrder_MtAuthServiceGetAuthReturnsNull() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setOrderStatus(0);
        unOrder.setShopName("ShopName");
        unOrder.setCbMsgType(0);
        unOrder.setReplyMsgType(0);
        unOrder.setOrderSubType(0);
        unOrder.setEnterpriseGuid("enterpriseGuid");
        unOrder.setStoreGuid("ShopNo");
        unOrder.setOrderId("orderId");
        unOrder.setOrderDaySn("orderDaySn");
        unOrder.setOrderRemark("orderRemark");
        unOrder.setReserve(false);
        unOrder.setCustomerName("customerName");
        unOrder.setCustomerPhone("customerPhone");
        unOrder.setPrivacyPhone("privacyPhone");
        unOrder.setCustomerAddress("customerAddress");
        unOrder.setShipLatitude("latitude");
        unOrder.setShipLongitude("longitude");
        unOrder.setShipperName("courierName");
        unOrder.setShipperPhone("courierPhone");
        unOrder.setInvoiced(false);
        unOrder.setInvoiceType(0);
        unOrder.setInvoiceTitle("invoiceTitle");
        unOrder.setTaxpayerId("taxpayerId");
        unOrder.setTotal(new BigDecimal("0.00"));
        unOrder.setShipTotal(new BigDecimal("0.00"));
        unOrder.setPackageTotal(new BigDecimal("0.00"));
        unOrder.setDiscountTotal(new BigDecimal("0.00"));
        unOrder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unOrder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setCancelReplyMessage("cancelReplyMessage");
        unOrder.setRefundReplyMessage("refundReplyMessage");
        unOrder.setCancelReason("cancelReason");
        final FoodListDTO foodListDTO = new FoodListDTO();
        unOrder.setFoodLists(Arrays.asList(foodListDTO));
        unOrder.setThirdCarrierId("thirdCarrierId");

        when(mockAuthService.getAuth("ShopNo", 0)).thenReturn(null);

        // Run the test
        mtOrderReplyServiceImplUnderTest.replyDisagreeRefundOrder(unOrder);
    }

    @Test
    public void testStartDelivery() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setOrderStatus(0);
        unOrder.setShopName("ShopName");
        unOrder.setCbMsgType(0);
        unOrder.setReplyMsgType(0);
        unOrder.setOrderSubType(0);
        unOrder.setEnterpriseGuid("enterpriseGuid");
        unOrder.setStoreGuid("ShopNo");
        unOrder.setOrderId("orderId");
        unOrder.setOrderDaySn("orderDaySn");
        unOrder.setOrderRemark("orderRemark");
        unOrder.setReserve(false);
        unOrder.setCustomerName("customerName");
        unOrder.setCustomerPhone("customerPhone");
        unOrder.setPrivacyPhone("privacyPhone");
        unOrder.setCustomerAddress("customerAddress");
        unOrder.setShipLatitude("latitude");
        unOrder.setShipLongitude("longitude");
        unOrder.setShipperName("courierName");
        unOrder.setShipperPhone("courierPhone");
        unOrder.setInvoiced(false);
        unOrder.setInvoiceType(0);
        unOrder.setInvoiceTitle("invoiceTitle");
        unOrder.setTaxpayerId("taxpayerId");
        unOrder.setTotal(new BigDecimal("0.00"));
        unOrder.setShipTotal(new BigDecimal("0.00"));
        unOrder.setPackageTotal(new BigDecimal("0.00"));
        unOrder.setDiscountTotal(new BigDecimal("0.00"));
        unOrder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unOrder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setCancelReplyMessage("cancelReplyMessage");
        unOrder.setRefundReplyMessage("refundReplyMessage");
        unOrder.setCancelReason("cancelReason");
        final FoodListDTO foodListDTO = new FoodListDTO();
        unOrder.setFoodLists(Arrays.asList(foodListDTO));
        unOrder.setThirdCarrierId("thirdCarrierId");

        // Run the test
        mtOrderReplyServiceImplUnderTest.startDelivery(unOrder);

        // Verify the results
        // Confirm ConsumersFeignService.updateThirdCarrierId(...).
        final ThirdCarrierUpdateDTO carrierUpdate = new ThirdCarrierUpdateDTO();
        carrierUpdate.setThirdCarrierId("thirdCarrierId");
        carrierUpdate.setOrderId("orderId");
        carrierUpdate.setEnterpriseGuid("enterpriseGuid");
        verify(mockConsumersFeignService).updateThirdCarrierId(carrierUpdate);
    }

    @Test
    public void testStartDeliveryMQ() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setOrderStatus(0);
        unOrder.setShopName("ShopName");
        unOrder.setCbMsgType(0);
        unOrder.setReplyMsgType(0);
        unOrder.setOrderSubType(0);
        unOrder.setEnterpriseGuid("enterpriseGuid");
        unOrder.setStoreGuid("ShopNo");
        unOrder.setOrderId("orderId");
        unOrder.setOrderDaySn("orderDaySn");
        unOrder.setOrderRemark("orderRemark");
        unOrder.setReserve(false);
        unOrder.setCustomerName("customerName");
        unOrder.setCustomerPhone("customerPhone");
        unOrder.setPrivacyPhone("privacyPhone");
        unOrder.setCustomerAddress("customerAddress");
        unOrder.setShipLatitude("latitude");
        unOrder.setShipLongitude("longitude");
        unOrder.setShipperName("courierName");
        unOrder.setShipperPhone("courierPhone");
        unOrder.setInvoiced(false);
        unOrder.setInvoiceType(0);
        unOrder.setInvoiceTitle("invoiceTitle");
        unOrder.setTaxpayerId("taxpayerId");
        unOrder.setTotal(new BigDecimal("0.00"));
        unOrder.setShipTotal(new BigDecimal("0.00"));
        unOrder.setPackageTotal(new BigDecimal("0.00"));
        unOrder.setDiscountTotal(new BigDecimal("0.00"));
        unOrder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unOrder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setCancelReplyMessage("cancelReplyMessage");
        unOrder.setRefundReplyMessage("refundReplyMessage");
        unOrder.setCancelReason("cancelReason");
        final FoodListDTO foodListDTO = new FoodListDTO();
        unOrder.setFoodLists(Arrays.asList(foodListDTO));
        unOrder.setThirdCarrierId("thirdCarrierId");

        final OwnApiResult expectedResult = new OwnApiResult();
        expectedResult.setCode(0);
        expectedResult.setStatus("status");
        expectedResult.setMsg("message");
        expectedResult.setResult("result");
        expectedResult.setErrorCode(0);

        // Run the test
        final OwnApiResult result = mtOrderReplyServiceImplUnderTest.startDeliveryMQ(unOrder);

        // Verify the results
        assertEquals(expectedResult, result);

        // Confirm ConsumersFeignService.updateThirdCarrierId(...).
        final ThirdCarrierUpdateDTO carrierUpdate = new ThirdCarrierUpdateDTO();
        carrierUpdate.setThirdCarrierId("thirdCarrierId");
        carrierUpdate.setOrderId("orderId");
        carrierUpdate.setEnterpriseGuid("enterpriseGuid");
        verify(mockConsumersFeignService).updateThirdCarrierId(carrierUpdate);
    }

    @Test
    public void testCancelDelivery() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setOrderStatus(0);
        unOrder.setShopName("ShopName");
        unOrder.setCbMsgType(0);
        unOrder.setReplyMsgType(0);
        unOrder.setOrderSubType(0);
        unOrder.setEnterpriseGuid("enterpriseGuid");
        unOrder.setStoreGuid("ShopNo");
        unOrder.setOrderId("orderId");
        unOrder.setOrderDaySn("orderDaySn");
        unOrder.setOrderRemark("orderRemark");
        unOrder.setReserve(false);
        unOrder.setCustomerName("customerName");
        unOrder.setCustomerPhone("customerPhone");
        unOrder.setPrivacyPhone("privacyPhone");
        unOrder.setCustomerAddress("customerAddress");
        unOrder.setShipLatitude("latitude");
        unOrder.setShipLongitude("longitude");
        unOrder.setShipperName("courierName");
        unOrder.setShipperPhone("courierPhone");
        unOrder.setInvoiced(false);
        unOrder.setInvoiceType(0);
        unOrder.setInvoiceTitle("invoiceTitle");
        unOrder.setTaxpayerId("taxpayerId");
        unOrder.setTotal(new BigDecimal("0.00"));
        unOrder.setShipTotal(new BigDecimal("0.00"));
        unOrder.setPackageTotal(new BigDecimal("0.00"));
        unOrder.setDiscountTotal(new BigDecimal("0.00"));
        unOrder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unOrder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setCancelReplyMessage("cancelReplyMessage");
        unOrder.setRefundReplyMessage("refundReplyMessage");
        unOrder.setCancelReason("cancelReason");
        final FoodListDTO foodListDTO = new FoodListDTO();
        unOrder.setFoodLists(Arrays.asList(foodListDTO));
        unOrder.setThirdCarrierId("thirdCarrierId");

        // Run the test
        mtOrderReplyServiceImplUnderTest.cancelDelivery(unOrder);

        // Verify the results
    }

    @Test(expected = BusinessException.class)
    public void testReplyUrgeOrder() {
        mtOrderReplyServiceImplUnderTest.replyUrgeOrder(new UnOrder());
    }

    @Test
    public void testReplyDeliveryAccept() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setOrderStatus(0);
        unOrder.setShopName("ShopName");
        unOrder.setCbMsgType(0);
        unOrder.setReplyMsgType(0);
        unOrder.setOrderSubType(0);
        unOrder.setEnterpriseGuid("enterpriseGuid");
        unOrder.setStoreGuid("ShopNo");
        unOrder.setOrderId("orderId");
        unOrder.setOrderDaySn("orderDaySn");
        unOrder.setOrderRemark("orderRemark");
        unOrder.setReserve(false);
        unOrder.setCustomerName("customerName");
        unOrder.setCustomerPhone("customerPhone");
        unOrder.setPrivacyPhone("privacyPhone");
        unOrder.setCustomerAddress("customerAddress");
        unOrder.setShipLatitude("latitude");
        unOrder.setShipLongitude("longitude");
        unOrder.setShipperName("courierName");
        unOrder.setShipperPhone("courierPhone");
        unOrder.setInvoiced(false);
        unOrder.setInvoiceType(0);
        unOrder.setInvoiceTitle("invoiceTitle");
        unOrder.setTaxpayerId("taxpayerId");
        unOrder.setTotal(new BigDecimal("0.00"));
        unOrder.setShipTotal(new BigDecimal("0.00"));
        unOrder.setPackageTotal(new BigDecimal("0.00"));
        unOrder.setDiscountTotal(new BigDecimal("0.00"));
        unOrder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unOrder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setCancelReplyMessage("cancelReplyMessage");
        unOrder.setRefundReplyMessage("refundReplyMessage");
        unOrder.setCancelReason("cancelReason");
        final FoodListDTO foodListDTO = new FoodListDTO();
        unOrder.setFoodLists(Arrays.asList(foodListDTO));
        unOrder.setThirdCarrierId("thirdCarrierId");

        // Configure MtAuthService.getAuth(...).
        final MtAuthDO mtAuthDO = new MtAuthDO();
        mtAuthDO.setId(0);
        mtAuthDO.setGuid("7adec2bf-7244-4a9b-a438-614af94ac6e8");
        mtAuthDO.setEPoiId("ePoiId");
        mtAuthDO.setEnterpriseGuid("enterpriseGuid");
        mtAuthDO.setAccessToken("accessToken");
        when(mockAuthService.getAuth("ShopNo", 0)).thenReturn(mtAuthDO);

        // Run the test
        mtOrderReplyServiceImplUnderTest.replyDeliveryAccept(unOrder);

        // Verify the results
    }

    @Test(expected = BusinessException.class)
    public void testReplyDeliveryAccept_MtAuthServiceGetAuthReturnsNull() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setOrderStatus(0);
        unOrder.setShopName("ShopName");
        unOrder.setCbMsgType(0);
        unOrder.setReplyMsgType(0);
        unOrder.setOrderSubType(0);
        unOrder.setEnterpriseGuid("enterpriseGuid");
        unOrder.setStoreGuid("ShopNo");
        unOrder.setOrderId("orderId");
        unOrder.setOrderDaySn("orderDaySn");
        unOrder.setOrderRemark("orderRemark");
        unOrder.setReserve(false);
        unOrder.setCustomerName("customerName");
        unOrder.setCustomerPhone("customerPhone");
        unOrder.setPrivacyPhone("privacyPhone");
        unOrder.setCustomerAddress("customerAddress");
        unOrder.setShipLatitude("latitude");
        unOrder.setShipLongitude("longitude");
        unOrder.setShipperName("courierName");
        unOrder.setShipperPhone("courierPhone");
        unOrder.setInvoiced(false);
        unOrder.setInvoiceType(0);
        unOrder.setInvoiceTitle("invoiceTitle");
        unOrder.setTaxpayerId("taxpayerId");
        unOrder.setTotal(new BigDecimal("0.00"));
        unOrder.setShipTotal(new BigDecimal("0.00"));
        unOrder.setPackageTotal(new BigDecimal("0.00"));
        unOrder.setDiscountTotal(new BigDecimal("0.00"));
        unOrder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unOrder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setCancelReplyMessage("cancelReplyMessage");
        unOrder.setRefundReplyMessage("refundReplyMessage");
        unOrder.setCancelReason("cancelReason");
        final FoodListDTO foodListDTO = new FoodListDTO();
        unOrder.setFoodLists(Arrays.asList(foodListDTO));
        unOrder.setThirdCarrierId("thirdCarrierId");

        when(mockAuthService.getAuth("ShopNo", 0)).thenReturn(null);

        // Run the test
        mtOrderReplyServiceImplUnderTest.replyDeliveryAccept(unOrder);
    }

    @Test
    public void testReplyDeliveryStart() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setOrderStatus(0);
        unOrder.setShopName("ShopName");
        unOrder.setCbMsgType(0);
        unOrder.setReplyMsgType(0);
        unOrder.setOrderSubType(0);
        unOrder.setEnterpriseGuid("enterpriseGuid");
        unOrder.setStoreGuid("ShopNo");
        unOrder.setOrderId("orderId");
        unOrder.setOrderDaySn("orderDaySn");
        unOrder.setOrderRemark("orderRemark");
        unOrder.setReserve(false);
        unOrder.setCustomerName("customerName");
        unOrder.setCustomerPhone("customerPhone");
        unOrder.setPrivacyPhone("privacyPhone");
        unOrder.setCustomerAddress("customerAddress");
        unOrder.setShipLatitude("latitude");
        unOrder.setShipLongitude("longitude");
        unOrder.setShipperName("courierName");
        unOrder.setShipperPhone("courierPhone");
        unOrder.setInvoiced(false);
        unOrder.setInvoiceType(0);
        unOrder.setInvoiceTitle("invoiceTitle");
        unOrder.setTaxpayerId("taxpayerId");
        unOrder.setTotal(new BigDecimal("0.00"));
        unOrder.setShipTotal(new BigDecimal("0.00"));
        unOrder.setPackageTotal(new BigDecimal("0.00"));
        unOrder.setDiscountTotal(new BigDecimal("0.00"));
        unOrder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unOrder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setCancelReplyMessage("cancelReplyMessage");
        unOrder.setRefundReplyMessage("refundReplyMessage");
        unOrder.setCancelReason("cancelReason");
        final FoodListDTO foodListDTO = new FoodListDTO();
        unOrder.setFoodLists(Arrays.asList(foodListDTO));
        unOrder.setThirdCarrierId("thirdCarrierId");

        // Configure MtAuthService.getAuth(...).
        final MtAuthDO mtAuthDO = new MtAuthDO();
        mtAuthDO.setId(0);
        mtAuthDO.setGuid("7adec2bf-7244-4a9b-a438-614af94ac6e8");
        mtAuthDO.setEPoiId("ePoiId");
        mtAuthDO.setEnterpriseGuid("enterpriseGuid");
        mtAuthDO.setAccessToken("accessToken");
        when(mockAuthService.getAuth("ShopNo", 0)).thenReturn(mtAuthDO);

        // Run the test
        mtOrderReplyServiceImplUnderTest.replyDeliveryStart(unOrder);

        // Verify the results
        verify(mockAuthService).deleteAuth("ShopNo", 0);

        // Confirm UnOrderMqService.sendUnOrder(...).
        final UnOrder unorder = new UnOrder();
        unorder.setOrderStatus(0);
        unorder.setShopName("ShopName");
        unorder.setCbMsgType(0);
        unorder.setReplyMsgType(0);
        unorder.setOrderSubType(0);
        unorder.setEnterpriseGuid("enterpriseGuid");
        unorder.setStoreGuid("ShopNo");
        unorder.setOrderId("orderId");
        unorder.setOrderDaySn("orderDaySn");
        unorder.setOrderRemark("orderRemark");
        unorder.setReserve(false);
        unorder.setCustomerName("customerName");
        unorder.setCustomerPhone("customerPhone");
        unorder.setPrivacyPhone("privacyPhone");
        unorder.setCustomerAddress("customerAddress");
        unorder.setShipLatitude("latitude");
        unorder.setShipLongitude("longitude");
        unorder.setShipperName("courierName");
        unorder.setShipperPhone("courierPhone");
        unorder.setInvoiced(false);
        unorder.setInvoiceType(0);
        unorder.setInvoiceTitle("invoiceTitle");
        unorder.setTaxpayerId("taxpayerId");
        unorder.setTotal(new BigDecimal("0.00"));
        unorder.setShipTotal(new BigDecimal("0.00"));
        unorder.setPackageTotal(new BigDecimal("0.00"));
        unorder.setDiscountTotal(new BigDecimal("0.00"));
        unorder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unorder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unorder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unorder.setCancelReplyMessage("cancelReplyMessage");
        unorder.setRefundReplyMessage("refundReplyMessage");
        unorder.setCancelReason("cancelReason");
        final FoodListDTO foodListDTO1 = new FoodListDTO();
        unorder.setFoodLists(Arrays.asList(foodListDTO1));
        unorder.setThirdCarrierId("thirdCarrierId");
        verify(mockUnOrderMqService).sendUnOrder(unorder);
    }

    @Test
    public void testReplyDeliveryStart_MtAuthServiceGetAuthReturnsNull() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setOrderStatus(0);
        unOrder.setShopName("ShopName");
        unOrder.setCbMsgType(0);
        unOrder.setReplyMsgType(0);
        unOrder.setOrderSubType(0);
        unOrder.setEnterpriseGuid("enterpriseGuid");
        unOrder.setStoreGuid("ShopNo");
        unOrder.setOrderId("orderId");
        unOrder.setOrderDaySn("orderDaySn");
        unOrder.setOrderRemark("orderRemark");
        unOrder.setReserve(false);
        unOrder.setCustomerName("customerName");
        unOrder.setCustomerPhone("customerPhone");
        unOrder.setPrivacyPhone("privacyPhone");
        unOrder.setCustomerAddress("customerAddress");
        unOrder.setShipLatitude("latitude");
        unOrder.setShipLongitude("longitude");
        unOrder.setShipperName("courierName");
        unOrder.setShipperPhone("courierPhone");
        unOrder.setInvoiced(false);
        unOrder.setInvoiceType(0);
        unOrder.setInvoiceTitle("invoiceTitle");
        unOrder.setTaxpayerId("taxpayerId");
        unOrder.setTotal(new BigDecimal("0.00"));
        unOrder.setShipTotal(new BigDecimal("0.00"));
        unOrder.setPackageTotal(new BigDecimal("0.00"));
        unOrder.setDiscountTotal(new BigDecimal("0.00"));
        unOrder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unOrder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setCancelReplyMessage("cancelReplyMessage");
        unOrder.setRefundReplyMessage("refundReplyMessage");
        unOrder.setCancelReason("cancelReason");
        final FoodListDTO foodListDTO = new FoodListDTO();
        unOrder.setFoodLists(Arrays.asList(foodListDTO));
        unOrder.setThirdCarrierId("thirdCarrierId");

        when(mockAuthService.getAuth("ShopNo", 0)).thenReturn(null);

        // Run the test
        mtOrderReplyServiceImplUnderTest.replyDeliveryStart(unOrder);

        // Verify the results
    }

    @Test
    public void testReplyDeliveryCancel() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setOrderStatus(0);
        unOrder.setShopName("ShopName");
        unOrder.setCbMsgType(0);
        unOrder.setReplyMsgType(0);
        unOrder.setOrderSubType(0);
        unOrder.setEnterpriseGuid("enterpriseGuid");
        unOrder.setStoreGuid("ShopNo");
        unOrder.setOrderId("orderId");
        unOrder.setOrderDaySn("orderDaySn");
        unOrder.setOrderRemark("orderRemark");
        unOrder.setReserve(false);
        unOrder.setCustomerName("customerName");
        unOrder.setCustomerPhone("customerPhone");
        unOrder.setPrivacyPhone("privacyPhone");
        unOrder.setCustomerAddress("customerAddress");
        unOrder.setShipLatitude("latitude");
        unOrder.setShipLongitude("longitude");
        unOrder.setShipperName("courierName");
        unOrder.setShipperPhone("courierPhone");
        unOrder.setInvoiced(false);
        unOrder.setInvoiceType(0);
        unOrder.setInvoiceTitle("invoiceTitle");
        unOrder.setTaxpayerId("taxpayerId");
        unOrder.setTotal(new BigDecimal("0.00"));
        unOrder.setShipTotal(new BigDecimal("0.00"));
        unOrder.setPackageTotal(new BigDecimal("0.00"));
        unOrder.setDiscountTotal(new BigDecimal("0.00"));
        unOrder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unOrder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setCancelReplyMessage("cancelReplyMessage");
        unOrder.setRefundReplyMessage("refundReplyMessage");
        unOrder.setCancelReason("cancelReason");
        final FoodListDTO foodListDTO = new FoodListDTO();
        unOrder.setFoodLists(Arrays.asList(foodListDTO));
        unOrder.setThirdCarrierId("thirdCarrierId");

        // Configure MtAuthService.getAuth(...).
        final MtAuthDO mtAuthDO = new MtAuthDO();
        mtAuthDO.setId(0);
        mtAuthDO.setGuid("7adec2bf-7244-4a9b-a438-614af94ac6e8");
        mtAuthDO.setEPoiId("ePoiId");
        mtAuthDO.setEnterpriseGuid("enterpriseGuid");
        mtAuthDO.setAccessToken("accessToken");
        when(mockAuthService.getAuth("ShopNo", 0)).thenReturn(mtAuthDO);

        // Run the test
        mtOrderReplyServiceImplUnderTest.replyDeliveryCancel(unOrder);

        // Verify the results
    }

    @Test(expected = BusinessException.class)
    public void testReplyDeliveryCancel_MtAuthServiceGetAuthReturnsNull() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setOrderStatus(0);
        unOrder.setShopName("ShopName");
        unOrder.setCbMsgType(0);
        unOrder.setReplyMsgType(0);
        unOrder.setOrderSubType(0);
        unOrder.setEnterpriseGuid("enterpriseGuid");
        unOrder.setStoreGuid("ShopNo");
        unOrder.setOrderId("orderId");
        unOrder.setOrderDaySn("orderDaySn");
        unOrder.setOrderRemark("orderRemark");
        unOrder.setReserve(false);
        unOrder.setCustomerName("customerName");
        unOrder.setCustomerPhone("customerPhone");
        unOrder.setPrivacyPhone("privacyPhone");
        unOrder.setCustomerAddress("customerAddress");
        unOrder.setShipLatitude("latitude");
        unOrder.setShipLongitude("longitude");
        unOrder.setShipperName("courierName");
        unOrder.setShipperPhone("courierPhone");
        unOrder.setInvoiced(false);
        unOrder.setInvoiceType(0);
        unOrder.setInvoiceTitle("invoiceTitle");
        unOrder.setTaxpayerId("taxpayerId");
        unOrder.setTotal(new BigDecimal("0.00"));
        unOrder.setShipTotal(new BigDecimal("0.00"));
        unOrder.setPackageTotal(new BigDecimal("0.00"));
        unOrder.setDiscountTotal(new BigDecimal("0.00"));
        unOrder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unOrder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setCancelReplyMessage("cancelReplyMessage");
        unOrder.setRefundReplyMessage("refundReplyMessage");
        unOrder.setCancelReason("cancelReason");
        final FoodListDTO foodListDTO = new FoodListDTO();
        unOrder.setFoodLists(Arrays.asList(foodListDTO));
        unOrder.setThirdCarrierId("thirdCarrierId");

        when(mockAuthService.getAuth("ShopNo", 0)).thenReturn(null);

        // Run the test
        mtOrderReplyServiceImplUnderTest.replyDeliveryCancel(unOrder);
    }

    @Test
    public void testReplyDeliveryComplete() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setOrderStatus(0);
        unOrder.setShopName("ShopName");
        unOrder.setCbMsgType(0);
        unOrder.setReplyMsgType(0);
        unOrder.setOrderSubType(0);
        unOrder.setEnterpriseGuid("enterpriseGuid");
        unOrder.setStoreGuid("ShopNo");
        unOrder.setOrderId("orderId");
        unOrder.setOrderDaySn("orderDaySn");
        unOrder.setOrderRemark("orderRemark");
        unOrder.setReserve(false);
        unOrder.setCustomerName("customerName");
        unOrder.setCustomerPhone("customerPhone");
        unOrder.setPrivacyPhone("privacyPhone");
        unOrder.setCustomerAddress("customerAddress");
        unOrder.setShipLatitude("latitude");
        unOrder.setShipLongitude("longitude");
        unOrder.setShipperName("courierName");
        unOrder.setShipperPhone("courierPhone");
        unOrder.setInvoiced(false);
        unOrder.setInvoiceType(0);
        unOrder.setInvoiceTitle("invoiceTitle");
        unOrder.setTaxpayerId("taxpayerId");
        unOrder.setTotal(new BigDecimal("0.00"));
        unOrder.setShipTotal(new BigDecimal("0.00"));
        unOrder.setPackageTotal(new BigDecimal("0.00"));
        unOrder.setDiscountTotal(new BigDecimal("0.00"));
        unOrder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unOrder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setCancelReplyMessage("cancelReplyMessage");
        unOrder.setRefundReplyMessage("refundReplyMessage");
        unOrder.setCancelReason("cancelReason");
        final FoodListDTO foodListDTO = new FoodListDTO();
        unOrder.setFoodLists(Arrays.asList(foodListDTO));
        unOrder.setThirdCarrierId("thirdCarrierId");

        // Configure MtAuthService.getAuth(...).
        final MtAuthDO mtAuthDO = new MtAuthDO();
        mtAuthDO.setId(0);
        mtAuthDO.setGuid("7adec2bf-7244-4a9b-a438-614af94ac6e8");
        mtAuthDO.setEPoiId("ePoiId");
        mtAuthDO.setEnterpriseGuid("enterpriseGuid");
        mtAuthDO.setAccessToken("accessToken");
        when(mockAuthService.getAuth("ShopNo", 0)).thenReturn(mtAuthDO);

        // Run the test
        mtOrderReplyServiceImplUnderTest.replyDeliveryComplete(unOrder);

        // Verify the results
        // Confirm UnOrderMqService.sendUnOrder(...).
        final UnOrder unorder = new UnOrder();
        unorder.setOrderStatus(0);
        unorder.setShopName("ShopName");
        unorder.setCbMsgType(0);
        unorder.setReplyMsgType(0);
        unorder.setOrderSubType(0);
        unorder.setEnterpriseGuid("enterpriseGuid");
        unorder.setStoreGuid("ShopNo");
        unorder.setOrderId("orderId");
        unorder.setOrderDaySn("orderDaySn");
        unorder.setOrderRemark("orderRemark");
        unorder.setReserve(false);
        unorder.setCustomerName("customerName");
        unorder.setCustomerPhone("customerPhone");
        unorder.setPrivacyPhone("privacyPhone");
        unorder.setCustomerAddress("customerAddress");
        unorder.setShipLatitude("latitude");
        unorder.setShipLongitude("longitude");
        unorder.setShipperName("courierName");
        unorder.setShipperPhone("courierPhone");
        unorder.setInvoiced(false);
        unorder.setInvoiceType(0);
        unorder.setInvoiceTitle("invoiceTitle");
        unorder.setTaxpayerId("taxpayerId");
        unorder.setTotal(new BigDecimal("0.00"));
        unorder.setShipTotal(new BigDecimal("0.00"));
        unorder.setPackageTotal(new BigDecimal("0.00"));
        unorder.setDiscountTotal(new BigDecimal("0.00"));
        unorder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unorder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unorder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unorder.setCancelReplyMessage("cancelReplyMessage");
        unorder.setRefundReplyMessage("refundReplyMessage");
        unorder.setCancelReason("cancelReason");
        final FoodListDTO foodListDTO1 = new FoodListDTO();
        unorder.setFoodLists(Arrays.asList(foodListDTO1));
        unorder.setThirdCarrierId("thirdCarrierId");
        verify(mockUnOrderMqService).sendUnOrder(unorder);
        verify(mockAuthService).deleteAuth("ShopNo", 0);
    }

    @Test
    public void testReplyDeliveryComplete_MtAuthServiceGetAuthReturnsNull() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setOrderStatus(0);
        unOrder.setShopName("ShopName");
        unOrder.setCbMsgType(0);
        unOrder.setReplyMsgType(0);
        unOrder.setOrderSubType(0);
        unOrder.setEnterpriseGuid("enterpriseGuid");
        unOrder.setStoreGuid("ShopNo");
        unOrder.setOrderId("orderId");
        unOrder.setOrderDaySn("orderDaySn");
        unOrder.setOrderRemark("orderRemark");
        unOrder.setReserve(false);
        unOrder.setCustomerName("customerName");
        unOrder.setCustomerPhone("customerPhone");
        unOrder.setPrivacyPhone("privacyPhone");
        unOrder.setCustomerAddress("customerAddress");
        unOrder.setShipLatitude("latitude");
        unOrder.setShipLongitude("longitude");
        unOrder.setShipperName("courierName");
        unOrder.setShipperPhone("courierPhone");
        unOrder.setInvoiced(false);
        unOrder.setInvoiceType(0);
        unOrder.setInvoiceTitle("invoiceTitle");
        unOrder.setTaxpayerId("taxpayerId");
        unOrder.setTotal(new BigDecimal("0.00"));
        unOrder.setShipTotal(new BigDecimal("0.00"));
        unOrder.setPackageTotal(new BigDecimal("0.00"));
        unOrder.setDiscountTotal(new BigDecimal("0.00"));
        unOrder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unOrder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setCancelReplyMessage("cancelReplyMessage");
        unOrder.setRefundReplyMessage("refundReplyMessage");
        unOrder.setCancelReason("cancelReason");
        final FoodListDTO foodListDTO = new FoodListDTO();
        unOrder.setFoodLists(Arrays.asList(foodListDTO));
        unOrder.setThirdCarrierId("thirdCarrierId");

        when(mockAuthService.getAuth("ShopNo", 0)).thenReturn(null);

        // Run the test
        mtOrderReplyServiceImplUnderTest.replyDeliveryComplete(unOrder);

        // Verify the results
        // Confirm UnOrderMqService.sendUnOrder(...).
        final UnOrder unorder = new UnOrder();
        unorder.setOrderStatus(0);
        unorder.setShopName("ShopName");
        unorder.setCbMsgType(0);
        unorder.setReplyMsgType(0);
        unorder.setOrderSubType(0);
        unorder.setEnterpriseGuid("enterpriseGuid");
        unorder.setStoreGuid("ShopNo");
        unorder.setOrderId("orderId");
        unorder.setOrderDaySn("orderDaySn");
        unorder.setOrderRemark("orderRemark");
        unorder.setReserve(false);
        unorder.setCustomerName("customerName");
        unorder.setCustomerPhone("customerPhone");
        unorder.setPrivacyPhone("privacyPhone");
        unorder.setCustomerAddress("customerAddress");
        unorder.setShipLatitude("latitude");
        unorder.setShipLongitude("longitude");
        unorder.setShipperName("courierName");
        unorder.setShipperPhone("courierPhone");
        unorder.setInvoiced(false);
        unorder.setInvoiceType(0);
        unorder.setInvoiceTitle("invoiceTitle");
        unorder.setTaxpayerId("taxpayerId");
        unorder.setTotal(new BigDecimal("0.00"));
        unorder.setShipTotal(new BigDecimal("0.00"));
        unorder.setPackageTotal(new BigDecimal("0.00"));
        unorder.setDiscountTotal(new BigDecimal("0.00"));
        unorder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unorder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unorder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unorder.setCancelReplyMessage("cancelReplyMessage");
        unorder.setRefundReplyMessage("refundReplyMessage");
        unorder.setCancelReason("cancelReason");
        final FoodListDTO foodListDTO1 = new FoodListDTO();
        unorder.setFoodLists(Arrays.asList(foodListDTO1));
        unorder.setThirdCarrierId("thirdCarrierId");
        verify(mockUnOrderMqService).sendUnOrder(unorder);
    }

    @Test
    public void testReplyRiderPosition() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setOrderStatus(0);
        unOrder.setShopName("ShopName");
        unOrder.setCbMsgType(0);
        unOrder.setReplyMsgType(0);
        unOrder.setOrderSubType(0);
        unOrder.setEnterpriseGuid("enterpriseGuid");
        unOrder.setStoreGuid("ShopNo");
        unOrder.setOrderId("orderId");
        unOrder.setOrderDaySn("orderDaySn");
        unOrder.setOrderRemark("orderRemark");
        unOrder.setReserve(false);
        unOrder.setCustomerName("customerName");
        unOrder.setCustomerPhone("customerPhone");
        unOrder.setPrivacyPhone("privacyPhone");
        unOrder.setCustomerAddress("customerAddress");
        unOrder.setShipLatitude("latitude");
        unOrder.setShipLongitude("longitude");
        unOrder.setShipperName("courierName");
        unOrder.setShipperPhone("courierPhone");
        unOrder.setInvoiced(false);
        unOrder.setInvoiceType(0);
        unOrder.setInvoiceTitle("invoiceTitle");
        unOrder.setTaxpayerId("taxpayerId");
        unOrder.setTotal(new BigDecimal("0.00"));
        unOrder.setShipTotal(new BigDecimal("0.00"));
        unOrder.setPackageTotal(new BigDecimal("0.00"));
        unOrder.setDiscountTotal(new BigDecimal("0.00"));
        unOrder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unOrder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setCancelReplyMessage("cancelReplyMessage");
        unOrder.setRefundReplyMessage("refundReplyMessage");
        unOrder.setCancelReason("cancelReason");
        final FoodListDTO foodListDTO = new FoodListDTO();
        unOrder.setFoodLists(Arrays.asList(foodListDTO));
        unOrder.setThirdCarrierId("thirdCarrierId");

        // Configure MtAuthService.getAuth(...).
        final MtAuthDO mtAuthDO = new MtAuthDO();
        mtAuthDO.setId(0);
        mtAuthDO.setGuid("7adec2bf-7244-4a9b-a438-614af94ac6e8");
        mtAuthDO.setEPoiId("ePoiId");
        mtAuthDO.setEnterpriseGuid("enterpriseGuid");
        mtAuthDO.setAccessToken("accessToken");
        when(mockAuthService.getAuth("ShopNo", 0)).thenReturn(mtAuthDO);

        // Run the test
        mtOrderReplyServiceImplUnderTest.replyRiderPosition(unOrder);

        // Verify the results
    }

    @Test(expected = BusinessException.class)
    public void testReplyRiderPosition_MtAuthServiceGetAuthReturnsNull() {
        // Setup
        final UnOrder unOrder = new UnOrder();
        unOrder.setOrderStatus(0);
        unOrder.setShopName("ShopName");
        unOrder.setCbMsgType(0);
        unOrder.setReplyMsgType(0);
        unOrder.setOrderSubType(0);
        unOrder.setEnterpriseGuid("enterpriseGuid");
        unOrder.setStoreGuid("ShopNo");
        unOrder.setOrderId("orderId");
        unOrder.setOrderDaySn("orderDaySn");
        unOrder.setOrderRemark("orderRemark");
        unOrder.setReserve(false);
        unOrder.setCustomerName("customerName");
        unOrder.setCustomerPhone("customerPhone");
        unOrder.setPrivacyPhone("privacyPhone");
        unOrder.setCustomerAddress("customerAddress");
        unOrder.setShipLatitude("latitude");
        unOrder.setShipLongitude("longitude");
        unOrder.setShipperName("courierName");
        unOrder.setShipperPhone("courierPhone");
        unOrder.setInvoiced(false);
        unOrder.setInvoiceType(0);
        unOrder.setInvoiceTitle("invoiceTitle");
        unOrder.setTaxpayerId("taxpayerId");
        unOrder.setTotal(new BigDecimal("0.00"));
        unOrder.setShipTotal(new BigDecimal("0.00"));
        unOrder.setPackageTotal(new BigDecimal("0.00"));
        unOrder.setDiscountTotal(new BigDecimal("0.00"));
        unOrder.setEnterpriseDiscount(new BigDecimal("0.00"));
        unOrder.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setEstimateDeliveredTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        unOrder.setCancelReplyMessage("cancelReplyMessage");
        unOrder.setRefundReplyMessage("refundReplyMessage");
        unOrder.setCancelReason("cancelReason");
        final FoodListDTO foodListDTO = new FoodListDTO();
        unOrder.setFoodLists(Arrays.asList(foodListDTO));
        unOrder.setThirdCarrierId("thirdCarrierId");

        when(mockAuthService.getAuth("ShopNo", 0)).thenReturn(null);

        // Run the test
        mtOrderReplyServiceImplUnderTest.replyRiderPosition(unOrder);
    }
}
