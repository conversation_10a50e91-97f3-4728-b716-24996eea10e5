package com.holder.saas.store.takeaway.producers.service.impl;

import com.holder.saas.store.takeaway.producers.entity.domain.MtAuthDO;
import com.holder.saas.store.takeaway.producers.entity.dto.MtAuthDTO;
import com.holder.saas.store.takeaway.producers.mapper.MtAuthMapper;
import com.holder.saas.store.takeaway.producers.mapstruct.MtAuthMapstruct;
import com.holder.saas.store.takeaway.producers.service.DistributedService;
import com.holder.saas.store.takeaway.producers.service.ErpGuidCacheService;
import com.holderzone.saas.store.dto.takeaway.MtAuthBindUrlDTO;
import com.holderzone.saas.store.dto.takeaway.MtCallbackDTO;
import com.holderzone.saas.store.dto.takeaway.TokenDTO;
import com.holderzone.saas.store.dto.takeaway.request.GroupBuyShopBindReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutItemBindReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutShopBindReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.GroupBuyShopBindRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.StoreAuthDTO;
import com.holderzone.saas.store.dto.takeaway.response.TakeoutItemBindRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.TakeoutShopBindRespDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class MtAuthServiceImplTest {

    @Mock
    private ErpGuidCacheService mockErpGuidCacheService;
    @Mock
    private DistributedService mockDistributedService;
    @Mock
    private MtAuthMapstruct mockMtAuthMapstruct;
    @Mock
    private MtAuthMapper mockMtAuthMapper;

    private MtAuthServiceImpl mtAuthServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        mtAuthServiceImplUnderTest = new MtAuthServiceImpl(mockErpGuidCacheService, mockDistributedService,
                mockMtAuthMapstruct);
        ReflectionTestUtils.setField(mtAuthServiceImplUnderTest, "mtDeveloperId", 0);
        ReflectionTestUtils.setField(mtAuthServiceImplUnderTest, "mtSignKey", "mtSignKey");
        ReflectionTestUtils.setField(mtAuthServiceImplUnderTest, "mtAuthMapper", mockMtAuthMapper);
    }

    @Test
    public void testTakeOutBindingUrl() {
        // Setup
        final TakeoutShopBindReqDTO takeoutShopBindReqDTO = new TakeoutShopBindReqDTO();
        takeoutShopBindReqDTO.setStoreGuid("storeGuid");
        takeoutShopBindReqDTO.setTakeoutType(0);
        takeoutShopBindReqDTO.setBindingStatus(0);

        final TakeoutShopBindRespDTO expectedResult = new TakeoutShopBindRespDTO();
        expectedResult.setUrl("url");

        // Run the test
        final TakeoutShopBindRespDTO result = mtAuthServiceImplUnderTest.takeOutBindingUrl(takeoutShopBindReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGroupBuyBindingUrl() {
        // Setup
        final GroupBuyShopBindReqDTO groupBuyShopBindReqDTO = new GroupBuyShopBindReqDTO();
        groupBuyShopBindReqDTO.setTakeoutType(0);
        groupBuyShopBindReqDTO.setBindingStatus(0);
        groupBuyShopBindReqDTO.setStoreGuid("storeGuid");

        final GroupBuyShopBindRespDTO expectedResult = new GroupBuyShopBindRespDTO();
        expectedResult.setUrl("url");

        // Run the test
        final GroupBuyShopBindRespDTO result = mtAuthServiceImplUnderTest.groupBuyBindingUrl(groupBuyShopBindReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testItemBindingUrl() {
        // Setup
        final TakeoutItemBindReqDTO takeoutItemBindReqDTO = new TakeoutItemBindReqDTO();
        takeoutItemBindReqDTO.setStoreGuid("storeGuid");
        takeoutItemBindReqDTO.setTakeoutType(0);

        final TakeoutItemBindRespDTO expectedResult = new TakeoutItemBindRespDTO();
        expectedResult.setUrl("url");

        // Run the test
        final TakeoutItemBindRespDTO result = mtAuthServiceImplUnderTest.itemBindingUrl(takeoutItemBindReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testBind() {
        // Setup
        final MtCallbackDTO mtCallbackDTO = new MtCallbackDTO();
        mtCallbackDTO.setAppAuthToken("accessToken");
        mtCallbackDTO.setBusinessId("businessId");
        mtCallbackDTO.setPoiId("mtStoreGuid");
        mtCallbackDTO.setPoiName("mtStoreName");
        mtCallbackDTO.setTimestamp(0L);
        mtCallbackDTO.setEPoiId("ePoiId");

        when(mockErpGuidCacheService.getEnterpriseGuid("ePoiId")).thenReturn("enterpriseGuid");
        when(mockDistributedService.nextMtGuid()).thenReturn("9f29b259-3798-49cc-bf35-6da6f7f2e079");

        // Run the test
        mtAuthServiceImplUnderTest.bind(mtCallbackDTO);

        // Verify the results
    }

    @Test
    public void testUnbind() {
        // Setup
        final MtCallbackDTO mtCallbackDTO = new MtCallbackDTO();
        mtCallbackDTO.setAppAuthToken("accessToken");
        mtCallbackDTO.setBusinessId("businessId");
        mtCallbackDTO.setPoiId("mtStoreGuid");
        mtCallbackDTO.setPoiName("mtStoreName");
        mtCallbackDTO.setTimestamp(0L);
        mtCallbackDTO.setEPoiId("ePoiId");

        // Run the test
        mtAuthServiceImplUnderTest.unbind(mtCallbackDTO);

        // Verify the results
    }

    @Test
    public void testGetAuth() {
        // Setup
        final MtAuthDO expectedResult = new MtAuthDO();
        expectedResult.setId(0);
        expectedResult.setGuid("9f29b259-3798-49cc-bf35-6da6f7f2e079");
        expectedResult.setEPoiId("ePoiId");
        expectedResult.setEnterpriseGuid("enterpriseGuid");
        expectedResult.setMtStoreGuid("mtStoreGuid");
        expectedResult.setMtStoreName("mtStoreName");
        expectedResult.setDeliveryType(0);
        expectedResult.setAccessToken("accessToken");
        expectedResult.setBusinessId((byte) 0b0);
        expectedResult.setActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setExpireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Run the test
        final MtAuthDO result = mtAuthServiceImplUnderTest.getAuth("ePoiId", 0);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetAuths() {
        // Setup
        final MtAuthDO mtAuthDO = new MtAuthDO();
        mtAuthDO.setId(0);
        mtAuthDO.setGuid("9f29b259-3798-49cc-bf35-6da6f7f2e079");
        mtAuthDO.setEPoiId("ePoiId");
        mtAuthDO.setEnterpriseGuid("enterpriseGuid");
        mtAuthDO.setMtStoreGuid("mtStoreGuid");
        mtAuthDO.setMtStoreName("mtStoreName");
        mtAuthDO.setDeliveryType(0);
        mtAuthDO.setAccessToken("accessToken");
        mtAuthDO.setBusinessId((byte) 0b0);
        mtAuthDO.setActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        mtAuthDO.setExpireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<MtAuthDO> expectedResult = Arrays.asList(mtAuthDO);

        // Run the test
        final List<MtAuthDO> result = mtAuthServiceImplUnderTest.getAuths(Arrays.asList("value"), 0);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetAuthByBizCode() {
        // Setup
        final MtAuthDO expectedResult = new MtAuthDO();
        expectedResult.setId(0);
        expectedResult.setGuid("9f29b259-3798-49cc-bf35-6da6f7f2e079");
        expectedResult.setEPoiId("ePoiId");
        expectedResult.setEnterpriseGuid("enterpriseGuid");
        expectedResult.setMtStoreGuid("mtStoreGuid");
        expectedResult.setMtStoreName("mtStoreName");
        expectedResult.setDeliveryType(0);
        expectedResult.setAccessToken("accessToken");
        expectedResult.setBusinessId((byte) 0b0);
        expectedResult.setActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setExpireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Run the test
        final MtAuthDO result = mtAuthServiceImplUnderTest.getAuthByBizCode("opBizCode", 0);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testDeleteAuth() {
        // Setup
        // Run the test
        mtAuthServiceImplUnderTest.deleteAuth("ePoiId", 0);

        // Verify the results
    }

    @Test
    public void testDeleteAuthByMt() {
        // Setup
        // Run the test
        mtAuthServiceImplUnderTest.deleteAuthByMt("bizCode", 0);

        // Verify the results
    }

    @Test
    public void testCorrectAuth() {
        // Setup
        // Run the test
        mtAuthServiceImplUnderTest.correctAuth("storeGuid", 0, "errorCode");

        // Verify the results
    }

    @Test
    public void testListAuth() {
        // Setup
        final StoreAuthDTO storeAuthDTO = new StoreAuthDTO();
        storeAuthDTO.setStoreGuid("storeGuid");
        storeAuthDTO.setShopId("mtStoreGuid");
        storeAuthDTO.setShopName("mtStoreName");
        storeAuthDTO.setBindingStatus(0);
        storeAuthDTO.setDeliveryType(0);
        final List<StoreAuthDTO> storeAuthorizationDTOList = Arrays.asList(storeAuthDTO);
        final StoreAuthDTO storeAuthDTO1 = new StoreAuthDTO();
        storeAuthDTO1.setStoreGuid("storeGuid");
        storeAuthDTO1.setShopId("mtStoreGuid");
        storeAuthDTO1.setShopName("mtStoreName");
        storeAuthDTO1.setBindingStatus(0);
        storeAuthDTO1.setDeliveryType(0);
        final List<StoreAuthDTO> expectedResult = Arrays.asList(storeAuthDTO1);

        // Run the test
        final List<StoreAuthDTO> result = mtAuthServiceImplUnderTest.listAuth(storeAuthorizationDTOList);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetTakeoutAuth() {
        // Setup
        final StoreAuthDTO storeAuthDTO = new StoreAuthDTO();
        storeAuthDTO.setStoreGuid("storeGuid");
        storeAuthDTO.setShopId("mtStoreGuid");
        storeAuthDTO.setShopName("mtStoreName");
        storeAuthDTO.setBindingStatus(0);
        storeAuthDTO.setDeliveryType(0);

        final StoreAuthDTO expectedResult = new StoreAuthDTO();
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setShopId("mtStoreGuid");
        expectedResult.setShopName("mtStoreName");
        expectedResult.setBindingStatus(0);
        expectedResult.setDeliveryType(0);

        // Run the test
        final StoreAuthDTO result = mtAuthServiceImplUnderTest.getTakeoutAuth(storeAuthDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testUpdateDelivery() {
        // Setup
        final StoreAuthDTO storeAuthDTO = new StoreAuthDTO();
        storeAuthDTO.setStoreGuid("storeGuid");
        storeAuthDTO.setShopId("mtStoreGuid");
        storeAuthDTO.setShopName("mtStoreName");
        storeAuthDTO.setBindingStatus(0);
        storeAuthDTO.setDeliveryType(0);

        // Run the test
        final Boolean result = mtAuthServiceImplUnderTest.updateDelivery(storeAuthDTO);

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testGetTuanGouAuth() {
        // Setup
        final StoreAuthDTO storeAuthDTO = new StoreAuthDTO();
        storeAuthDTO.setStoreGuid("storeGuid");
        storeAuthDTO.setShopId("mtStoreGuid");
        storeAuthDTO.setShopName("mtStoreName");
        storeAuthDTO.setBindingStatus(0);
        storeAuthDTO.setDeliveryType(0);

        final StoreAuthDTO expectedResult = new StoreAuthDTO();
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setShopId("mtStoreGuid");
        expectedResult.setShopName("mtStoreName");
        expectedResult.setBindingStatus(0);
        expectedResult.setDeliveryType(0);

        // Run the test
        final StoreAuthDTO result = mtAuthServiceImplUnderTest.getTuanGouAuth(storeAuthDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetToken1() {
        // Setup
        final TokenDTO expectedResult = new TokenDTO();
        expectedResult.setToken("accessToken");

        // Run the test
        final TokenDTO result = mtAuthServiceImplUnderTest.getToken("ePoiId", 0);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetToken2() {
        // Setup
        final GroupBuyShopBindReqDTO groupBuyShopBindReqDTO = new GroupBuyShopBindReqDTO();
        groupBuyShopBindReqDTO.setTakeoutType(0);
        groupBuyShopBindReqDTO.setBindingStatus(0);
        groupBuyShopBindReqDTO.setStoreGuid("storeGuid");

        final TokenDTO expectedResult = new TokenDTO();
        expectedResult.setToken("accessToken");

        // Run the test
        final TokenDTO result = mtAuthServiceImplUnderTest.getToken(groupBuyShopBindReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetToken3() {
        // Setup
        final TakeoutShopBindReqDTO takeoutShopBindReqDTO = new TakeoutShopBindReqDTO();
        takeoutShopBindReqDTO.setStoreGuid("storeGuid");
        takeoutShopBindReqDTO.setTakeoutType(0);
        takeoutShopBindReqDTO.setBindingStatus(0);

        final TokenDTO expectedResult = new TokenDTO();
        expectedResult.setToken("accessToken");

        // Run the test
        final TokenDTO result = mtAuthServiceImplUnderTest.getToken(takeoutShopBindReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testSaveCallbackAuth() {
        // Setup
        final MtAuthDTO mtAuthDTO = new MtAuthDTO();
        mtAuthDTO.setMtStoreGuid("mtStoreGuid");
        mtAuthDTO.setMtStoreName("mtStoreName");
        mtAuthDTO.setEPoiId("ePoiId");
        mtAuthDTO.setEnterpriseGuid("enterpriseGuid");
        mtAuthDTO.setDeliveryType(0);

        // Configure MtAuthMapstruct.fromMtAuth(...).
        final MtAuthDO mtAuthDO = new MtAuthDO();
        mtAuthDO.setId(0);
        mtAuthDO.setGuid("9f29b259-3798-49cc-bf35-6da6f7f2e079");
        mtAuthDO.setEPoiId("ePoiId");
        mtAuthDO.setEnterpriseGuid("enterpriseGuid");
        mtAuthDO.setMtStoreGuid("mtStoreGuid");
        mtAuthDO.setMtStoreName("mtStoreName");
        mtAuthDO.setDeliveryType(0);
        mtAuthDO.setAccessToken("accessToken");
        mtAuthDO.setBusinessId((byte) 0b0);
        mtAuthDO.setActiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        mtAuthDO.setExpireTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final MtAuthDTO mtAuthDTO1 = new MtAuthDTO();
        mtAuthDTO1.setMtStoreGuid("mtStoreGuid");
        mtAuthDTO1.setMtStoreName("mtStoreName");
        mtAuthDTO1.setEPoiId("ePoiId");
        mtAuthDTO1.setEnterpriseGuid("enterpriseGuid");
        mtAuthDTO1.setDeliveryType(0);
        when(mockMtAuthMapstruct.fromMtAuth(mtAuthDTO1)).thenReturn(mtAuthDO);

        when(mockDistributedService.nextMtGuid()).thenReturn("9f29b259-3798-49cc-bf35-6da6f7f2e079");

        // Run the test
        mtAuthServiceImplUnderTest.saveCallbackAuth(mtAuthDTO);

        // Verify the results
    }

    @Test
    public void testGetMtAuthBindUrl() {
        // Setup
        final MtAuthBindUrlDTO authBindUrl = new MtAuthBindUrlDTO();
        authBindUrl.setBusinessId(0);
        authBindUrl.setBusinessName("businessName");
        authBindUrl.setOperSubjectGuid("data");
        authBindUrl.setStatus(0);

        // Run the test
        final String result = mtAuthServiceImplUnderTest.getMtAuthBindUrl(authBindUrl);

        // Verify the results
        assertEquals("result", result);
    }

    @Test
    public void testGetMtBindAuth() {
        // Setup
        final MtAuthBindUrlDTO mtAuthBindUrlDTO = new MtAuthBindUrlDTO();
        mtAuthBindUrlDTO.setBusinessId(0);
        mtAuthBindUrlDTO.setBusinessName("businessName");
        mtAuthBindUrlDTO.setOperSubjectGuid("data");
        mtAuthBindUrlDTO.setStatus(0);
        final List<MtAuthBindUrlDTO> expectedResult = Arrays.asList(mtAuthBindUrlDTO);

        // Run the test
        final List<MtAuthBindUrlDTO> result = mtAuthServiceImplUnderTest.getMtBindAuth("data");

        // Verify the results
        assertEquals(expectedResult, result);
    }
}
