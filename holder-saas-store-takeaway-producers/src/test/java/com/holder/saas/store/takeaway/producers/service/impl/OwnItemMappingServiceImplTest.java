package com.holder.saas.store.takeaway.producers.service.impl;

import com.holder.saas.store.takeaway.producers.entity.domain.HolderAuthDO;
import com.holder.saas.store.takeaway.producers.service.HolderAuthService;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.takeaway.*;
import com.holderzone.saas.store.dto.takeaway.request.BindInfo;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutShopOwnItemBindReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.TakeoutOwnItemMappingRespDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class OwnItemMappingServiceImplTest {

    @Mock
    private HolderAuthService mockHolderAuthService;

    private OwnItemMappingServiceImpl ownItemMappingServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        ownItemMappingServiceImplUnderTest = new OwnItemMappingServiceImpl(mockHolderAuthService);
    }

    @Test
    public void testGetType() {
        assertNull(ownItemMappingServiceImplUnderTest.getType("storeGuid"));
    }

    @Test
    public void testGetItem() {
        // Setup
        final UnMappedItem unMappedItem = new UnMappedItem();
        unMappedItem.setUnItemId("unItemId");
        unMappedItem.setUnItemName("name");
        unMappedItem.setUnItemSkuId("unItemSkuId");
        unMappedItem.setUnItemSkuName("name");
        unMappedItem.setUnItemTypeId("unItemTypeId");
        unMappedItem.setUnItemTypeName("goodsTypeName");
        unMappedItem.setErpItemId("erpItemId");
        unMappedItem.setErpItemSkuId("erpItemId");
        unMappedItem.setExtendValue("extendValue");
        final List<UnMappedItem> expectedResult = Arrays.asList(unMappedItem);
        when(mockHolderAuthService.getCode("storeGuid")).thenReturn("result");

        // Configure HolderAuthService.getItem(...).
        final TakeoutOwnItemMappingRespDTO takeoutOwnItemMappingRespDTO = new TakeoutOwnItemMappingRespDTO();
        takeoutOwnItemMappingRespDTO.setId(0L);
        takeoutOwnItemMappingRespDTO.setName("name");
        takeoutOwnItemMappingRespDTO.setPrice(new BigDecimal("0.00"));
        takeoutOwnItemMappingRespDTO.setGoodsId(0L);
        takeoutOwnItemMappingRespDTO.setUnit("name");
        takeoutOwnItemMappingRespDTO.setThirdSkuId("erpItemId");
        takeoutOwnItemMappingRespDTO.setGoodsTypeId(0L);
        takeoutOwnItemMappingRespDTO.setGoodsTypeName("goodsTypeName");
        final List<TakeoutOwnItemMappingRespDTO> takeoutOwnItemMappingRespDTOS = Arrays.asList(
                takeoutOwnItemMappingRespDTO);
        when(mockHolderAuthService.getItem("storeGuid")).thenReturn(takeoutOwnItemMappingRespDTOS);

        // Run the test
        final List<UnMappedItem> result = ownItemMappingServiceImplUnderTest.getItem("storeGuid");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test(expected = BusinessException.class)
    public void testGetItem_HolderAuthServiceGetCodeReturnsNull() {
        // Setup
        when(mockHolderAuthService.getCode("storeGuid")).thenReturn(null);

        // Run the test
        ownItemMappingServiceImplUnderTest.getItem("storeGuid");
    }

    @Test
    public void testGetItem_HolderAuthServiceGetItemReturnsNoItems() {
        // Setup
        when(mockHolderAuthService.getCode("storeGuid")).thenReturn("result");
        when(mockHolderAuthService.getItem("storeGuid")).thenReturn(Collections.emptyList());

        // Run the test
        final List<UnMappedItem> result = ownItemMappingServiceImplUnderTest.getItem("storeGuid");

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testGetItems() {
        assertEquals(Collections.emptyList(), ownItemMappingServiceImplUnderTest.getItems(new UnItemQueryReq()));
    }

    @Test
    public void testChangeData1() {
        // Setup
        final TakeoutOwnItemMappingRespDTO takeoutOwnItemMappingRespDTO = new TakeoutOwnItemMappingRespDTO();
        takeoutOwnItemMappingRespDTO.setId(0L);
        takeoutOwnItemMappingRespDTO.setName("name");
        takeoutOwnItemMappingRespDTO.setPrice(new BigDecimal("0.00"));
        takeoutOwnItemMappingRespDTO.setGoodsId(0L);
        takeoutOwnItemMappingRespDTO.setUnit("name");
        takeoutOwnItemMappingRespDTO.setThirdSkuId("erpItemId");
        takeoutOwnItemMappingRespDTO.setGoodsTypeId(0L);
        takeoutOwnItemMappingRespDTO.setGoodsTypeName("goodsTypeName");
        final List<TakeoutOwnItemMappingRespDTO> list = Arrays.asList(takeoutOwnItemMappingRespDTO);
        final UnMappedItem unMappedItem = new UnMappedItem();
        unMappedItem.setUnItemId("unItemId");
        unMappedItem.setUnItemName("name");
        unMappedItem.setUnItemSkuId("unItemSkuId");
        unMappedItem.setUnItemSkuName("name");
        unMappedItem.setUnItemTypeId("unItemTypeId");
        unMappedItem.setUnItemTypeName("goodsTypeName");
        unMappedItem.setErpItemId("erpItemId");
        unMappedItem.setErpItemSkuId("erpItemId");
        unMappedItem.setExtendValue("extendValue");
        final List<UnMappedItem> expectedResult = Arrays.asList(unMappedItem);

        // Run the test
        final List<UnMappedItem> result = ownItemMappingServiceImplUnderTest.changeData(list);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testBindMapping() {
        // Setup
        final UnItemBindUnbindReq unItemBindUnbindReq = new UnItemBindUnbindReq();
        unItemBindUnbindReq.setUnItemSkuId("unItemSkuId");
        unItemBindUnbindReq.setErpItemSkuId("erpItemSkuId");
        unItemBindUnbindReq.setStoreGuid("storeGuid");
        unItemBindUnbindReq.setTakeoutType(0);
        unItemBindUnbindReq.setMtSkuId("mtSkuId");

        // Configure HolderAuthService.getHolder(...).
        final HolderAuthDO holderAuthDO = new HolderAuthDO();
        holderAuthDO.setId(0L);
        holderAuthDO.setGuid("57773cc1-2b6d-4e22-ad58-3a811afdc9f1");
        holderAuthDO.setEnterpriseGuid("enterpriseGuid");
        holderAuthDO.setStoreGuid("storeGuid");
        holderAuthDO.setUserId(0L);
        when(mockHolderAuthService.getHolder("storeGuid")).thenReturn(holderAuthDO);

        // Configure HolderAuthService.getToken(...).
        final HolderAuthDO holderAuthDO1 = new HolderAuthDO();
        holderAuthDO1.setId(0L);
        holderAuthDO1.setGuid("57773cc1-2b6d-4e22-ad58-3a811afdc9f1");
        holderAuthDO1.setEnterpriseGuid("enterpriseGuid");
        holderAuthDO1.setStoreGuid("storeGuid");
        holderAuthDO1.setUserId(0L);
        when(mockHolderAuthService.getToken(holderAuthDO1)).thenReturn("token");

        when(mockHolderAuthService.getCode("storeGuid")).thenReturn("code");

        // Configure HolderAuthService.itemBind(...).
        final TakeoutShopOwnItemBindReqDTO takeoutShopOwnItemBindReqDTO = new TakeoutShopOwnItemBindReqDTO();
        takeoutShopOwnItemBindReqDTO.setStoreGuid("storeGuid");
        final BindInfo bindInfo = new BindInfo();
        bindInfo.setGoodsSpecId(0L);
        bindInfo.setThirdSkuId("erpItemSkuId");
        takeoutShopOwnItemBindReqDTO.setGoodsList(Arrays.asList(bindInfo));
        takeoutShopOwnItemBindReqDTO.setStoreCode("StoreCode");
        when(mockHolderAuthService.itemBind(takeoutShopOwnItemBindReqDTO, "code", "token")).thenReturn("result");

        // Configure HolderAuthService.itemUnBind(...).
        final TakeoutShopOwnItemBindReqDTO takeoutShopOwnItemBindReqDTO1 = new TakeoutShopOwnItemBindReqDTO();
        takeoutShopOwnItemBindReqDTO1.setStoreGuid("storeGuid");
        final BindInfo bindInfo1 = new BindInfo();
        bindInfo1.setGoodsSpecId(0L);
        bindInfo1.setThirdSkuId("erpItemSkuId");
        takeoutShopOwnItemBindReqDTO1.setGoodsList(Arrays.asList(bindInfo1));
        takeoutShopOwnItemBindReqDTO1.setStoreCode("StoreCode");
        when(mockHolderAuthService.itemUnBind(takeoutShopOwnItemBindReqDTO1, "code", "token")).thenReturn("result");

        // Run the test
        ownItemMappingServiceImplUnderTest.bindMapping(unItemBindUnbindReq);

        // Verify the results
    }

    @Test
    public void testBindMapping_HolderAuthServiceGetTokenReturnsNull() {
        // Setup
        final UnItemBindUnbindReq unItemBindUnbindReq = new UnItemBindUnbindReq();
        unItemBindUnbindReq.setUnItemSkuId("unItemSkuId");
        unItemBindUnbindReq.setErpItemSkuId("erpItemSkuId");
        unItemBindUnbindReq.setStoreGuid("storeGuid");
        unItemBindUnbindReq.setTakeoutType(0);
        unItemBindUnbindReq.setMtSkuId("mtSkuId");

        // Configure HolderAuthService.getHolder(...).
        final HolderAuthDO holderAuthDO = new HolderAuthDO();
        holderAuthDO.setId(0L);
        holderAuthDO.setGuid("57773cc1-2b6d-4e22-ad58-3a811afdc9f1");
        holderAuthDO.setEnterpriseGuid("enterpriseGuid");
        holderAuthDO.setStoreGuid("storeGuid");
        holderAuthDO.setUserId(0L);
        when(mockHolderAuthService.getHolder("storeGuid")).thenReturn(holderAuthDO);

        // Configure HolderAuthService.getToken(...).
        final HolderAuthDO holderAuthDO1 = new HolderAuthDO();
        holderAuthDO1.setId(0L);
        holderAuthDO1.setGuid("57773cc1-2b6d-4e22-ad58-3a811afdc9f1");
        holderAuthDO1.setEnterpriseGuid("enterpriseGuid");
        holderAuthDO1.setStoreGuid("storeGuid");
        holderAuthDO1.setUserId(0L);
        when(mockHolderAuthService.getToken(holderAuthDO1)).thenReturn(null);

        when(mockHolderAuthService.getCode("storeGuid")).thenReturn("code");

        // Configure HolderAuthService.itemBind(...).
        final TakeoutShopOwnItemBindReqDTO takeoutShopOwnItemBindReqDTO = new TakeoutShopOwnItemBindReqDTO();
        takeoutShopOwnItemBindReqDTO.setStoreGuid("storeGuid");
        final BindInfo bindInfo = new BindInfo();
        bindInfo.setGoodsSpecId(0L);
        bindInfo.setThirdSkuId("erpItemSkuId");
        takeoutShopOwnItemBindReqDTO.setGoodsList(Arrays.asList(bindInfo));
        takeoutShopOwnItemBindReqDTO.setStoreCode("StoreCode");
        when(mockHolderAuthService.itemBind(takeoutShopOwnItemBindReqDTO, "code", "token")).thenReturn("result");

        // Configure HolderAuthService.itemUnBind(...).
        final TakeoutShopOwnItemBindReqDTO takeoutShopOwnItemBindReqDTO1 = new TakeoutShopOwnItemBindReqDTO();
        takeoutShopOwnItemBindReqDTO1.setStoreGuid("storeGuid");
        final BindInfo bindInfo1 = new BindInfo();
        bindInfo1.setGoodsSpecId(0L);
        bindInfo1.setThirdSkuId("erpItemSkuId");
        takeoutShopOwnItemBindReqDTO1.setGoodsList(Arrays.asList(bindInfo1));
        takeoutShopOwnItemBindReqDTO1.setStoreCode("StoreCode");
        when(mockHolderAuthService.itemUnBind(takeoutShopOwnItemBindReqDTO1, "code", "token")).thenReturn("result");

        // Run the test
        ownItemMappingServiceImplUnderTest.bindMapping(unItemBindUnbindReq);

        // Verify the results
    }

    @Test(expected = BusinessException.class)
    public void testBindMapping_HolderAuthServiceGetCodeReturnsNull() {
        // Setup
        final UnItemBindUnbindReq unItemBindUnbindReq = new UnItemBindUnbindReq();
        unItemBindUnbindReq.setUnItemSkuId("unItemSkuId");
        unItemBindUnbindReq.setErpItemSkuId("erpItemSkuId");
        unItemBindUnbindReq.setStoreGuid("storeGuid");
        unItemBindUnbindReq.setTakeoutType(0);
        unItemBindUnbindReq.setMtSkuId("mtSkuId");

        // Configure HolderAuthService.getHolder(...).
        final HolderAuthDO holderAuthDO = new HolderAuthDO();
        holderAuthDO.setId(0L);
        holderAuthDO.setGuid("57773cc1-2b6d-4e22-ad58-3a811afdc9f1");
        holderAuthDO.setEnterpriseGuid("enterpriseGuid");
        holderAuthDO.setStoreGuid("storeGuid");
        holderAuthDO.setUserId(0L);
        when(mockHolderAuthService.getHolder("storeGuid")).thenReturn(holderAuthDO);

        // Configure HolderAuthService.getToken(...).
        final HolderAuthDO holderAuthDO1 = new HolderAuthDO();
        holderAuthDO1.setId(0L);
        holderAuthDO1.setGuid("57773cc1-2b6d-4e22-ad58-3a811afdc9f1");
        holderAuthDO1.setEnterpriseGuid("enterpriseGuid");
        holderAuthDO1.setStoreGuid("storeGuid");
        holderAuthDO1.setUserId(0L);
        when(mockHolderAuthService.getToken(holderAuthDO1)).thenReturn("token");

        when(mockHolderAuthService.getCode("storeGuid")).thenReturn(null);

        // Run the test
        ownItemMappingServiceImplUnderTest.bindMapping(unItemBindUnbindReq);
    }

    @Test
    public void testUnbindMapping() {
        // Setup
        final UnItemBindUnbindReq unItemBindUnbindReq = new UnItemBindUnbindReq();
        unItemBindUnbindReq.setUnItemSkuId("unItemSkuId");
        unItemBindUnbindReq.setErpItemSkuId("erpItemSkuId");
        unItemBindUnbindReq.setStoreGuid("storeGuid");
        unItemBindUnbindReq.setTakeoutType(0);
        unItemBindUnbindReq.setMtSkuId("mtSkuId");

        // Configure HolderAuthService.getHolder(...).
        final HolderAuthDO holderAuthDO = new HolderAuthDO();
        holderAuthDO.setId(0L);
        holderAuthDO.setGuid("57773cc1-2b6d-4e22-ad58-3a811afdc9f1");
        holderAuthDO.setEnterpriseGuid("enterpriseGuid");
        holderAuthDO.setStoreGuid("storeGuid");
        holderAuthDO.setUserId(0L);
        when(mockHolderAuthService.getHolder("storeGuid")).thenReturn(holderAuthDO);

        // Configure HolderAuthService.getToken(...).
        final HolderAuthDO holderAuthDO1 = new HolderAuthDO();
        holderAuthDO1.setId(0L);
        holderAuthDO1.setGuid("57773cc1-2b6d-4e22-ad58-3a811afdc9f1");
        holderAuthDO1.setEnterpriseGuid("enterpriseGuid");
        holderAuthDO1.setStoreGuid("storeGuid");
        holderAuthDO1.setUserId(0L);
        when(mockHolderAuthService.getToken(holderAuthDO1)).thenReturn("token");

        when(mockHolderAuthService.getCode("storeGuid")).thenReturn("code");

        // Configure HolderAuthService.itemBind(...).
        final TakeoutShopOwnItemBindReqDTO takeoutShopOwnItemBindReqDTO = new TakeoutShopOwnItemBindReqDTO();
        takeoutShopOwnItemBindReqDTO.setStoreGuid("storeGuid");
        final BindInfo bindInfo = new BindInfo();
        bindInfo.setGoodsSpecId(0L);
        bindInfo.setThirdSkuId("erpItemSkuId");
        takeoutShopOwnItemBindReqDTO.setGoodsList(Arrays.asList(bindInfo));
        takeoutShopOwnItemBindReqDTO.setStoreCode("StoreCode");
        when(mockHolderAuthService.itemBind(takeoutShopOwnItemBindReqDTO, "code", "token")).thenReturn("result");

        // Configure HolderAuthService.itemUnBind(...).
        final TakeoutShopOwnItemBindReqDTO takeoutShopOwnItemBindReqDTO1 = new TakeoutShopOwnItemBindReqDTO();
        takeoutShopOwnItemBindReqDTO1.setStoreGuid("storeGuid");
        final BindInfo bindInfo1 = new BindInfo();
        bindInfo1.setGoodsSpecId(0L);
        bindInfo1.setThirdSkuId("erpItemSkuId");
        takeoutShopOwnItemBindReqDTO1.setGoodsList(Arrays.asList(bindInfo1));
        takeoutShopOwnItemBindReqDTO1.setStoreCode("StoreCode");
        when(mockHolderAuthService.itemUnBind(takeoutShopOwnItemBindReqDTO1, "code", "token")).thenReturn("result");

        // Run the test
        ownItemMappingServiceImplUnderTest.unbindMapping(unItemBindUnbindReq);

        // Verify the results
    }

    @Test
    public void testUnbindMapping_HolderAuthServiceGetTokenReturnsNull() {
        // Setup
        final UnItemBindUnbindReq unItemBindUnbindReq = new UnItemBindUnbindReq();
        unItemBindUnbindReq.setUnItemSkuId("unItemSkuId");
        unItemBindUnbindReq.setErpItemSkuId("erpItemSkuId");
        unItemBindUnbindReq.setStoreGuid("storeGuid");
        unItemBindUnbindReq.setTakeoutType(0);
        unItemBindUnbindReq.setMtSkuId("mtSkuId");

        // Configure HolderAuthService.getHolder(...).
        final HolderAuthDO holderAuthDO = new HolderAuthDO();
        holderAuthDO.setId(0L);
        holderAuthDO.setGuid("57773cc1-2b6d-4e22-ad58-3a811afdc9f1");
        holderAuthDO.setEnterpriseGuid("enterpriseGuid");
        holderAuthDO.setStoreGuid("storeGuid");
        holderAuthDO.setUserId(0L);
        when(mockHolderAuthService.getHolder("storeGuid")).thenReturn(holderAuthDO);

        // Configure HolderAuthService.getToken(...).
        final HolderAuthDO holderAuthDO1 = new HolderAuthDO();
        holderAuthDO1.setId(0L);
        holderAuthDO1.setGuid("57773cc1-2b6d-4e22-ad58-3a811afdc9f1");
        holderAuthDO1.setEnterpriseGuid("enterpriseGuid");
        holderAuthDO1.setStoreGuid("storeGuid");
        holderAuthDO1.setUserId(0L);
        when(mockHolderAuthService.getToken(holderAuthDO1)).thenReturn(null);

        when(mockHolderAuthService.getCode("storeGuid")).thenReturn("code");

        // Configure HolderAuthService.itemBind(...).
        final TakeoutShopOwnItemBindReqDTO takeoutShopOwnItemBindReqDTO = new TakeoutShopOwnItemBindReqDTO();
        takeoutShopOwnItemBindReqDTO.setStoreGuid("storeGuid");
        final BindInfo bindInfo = new BindInfo();
        bindInfo.setGoodsSpecId(0L);
        bindInfo.setThirdSkuId("erpItemSkuId");
        takeoutShopOwnItemBindReqDTO.setGoodsList(Arrays.asList(bindInfo));
        takeoutShopOwnItemBindReqDTO.setStoreCode("StoreCode");
        when(mockHolderAuthService.itemBind(takeoutShopOwnItemBindReqDTO, "code", "token")).thenReturn("result");

        // Configure HolderAuthService.itemUnBind(...).
        final TakeoutShopOwnItemBindReqDTO takeoutShopOwnItemBindReqDTO1 = new TakeoutShopOwnItemBindReqDTO();
        takeoutShopOwnItemBindReqDTO1.setStoreGuid("storeGuid");
        final BindInfo bindInfo1 = new BindInfo();
        bindInfo1.setGoodsSpecId(0L);
        bindInfo1.setThirdSkuId("erpItemSkuId");
        takeoutShopOwnItemBindReqDTO1.setGoodsList(Arrays.asList(bindInfo1));
        takeoutShopOwnItemBindReqDTO1.setStoreCode("StoreCode");
        when(mockHolderAuthService.itemUnBind(takeoutShopOwnItemBindReqDTO1, "code", "token")).thenReturn("result");

        // Run the test
        ownItemMappingServiceImplUnderTest.unbindMapping(unItemBindUnbindReq);

        // Verify the results
    }

    @Test(expected = BusinessException.class)
    public void testUnbindMapping_HolderAuthServiceGetCodeReturnsNull() {
        // Setup
        final UnItemBindUnbindReq unItemBindUnbindReq = new UnItemBindUnbindReq();
        unItemBindUnbindReq.setUnItemSkuId("unItemSkuId");
        unItemBindUnbindReq.setErpItemSkuId("erpItemSkuId");
        unItemBindUnbindReq.setStoreGuid("storeGuid");
        unItemBindUnbindReq.setTakeoutType(0);
        unItemBindUnbindReq.setMtSkuId("mtSkuId");

        // Configure HolderAuthService.getHolder(...).
        final HolderAuthDO holderAuthDO = new HolderAuthDO();
        holderAuthDO.setId(0L);
        holderAuthDO.setGuid("57773cc1-2b6d-4e22-ad58-3a811afdc9f1");
        holderAuthDO.setEnterpriseGuid("enterpriseGuid");
        holderAuthDO.setStoreGuid("storeGuid");
        holderAuthDO.setUserId(0L);
        when(mockHolderAuthService.getHolder("storeGuid")).thenReturn(holderAuthDO);

        // Configure HolderAuthService.getToken(...).
        final HolderAuthDO holderAuthDO1 = new HolderAuthDO();
        holderAuthDO1.setId(0L);
        holderAuthDO1.setGuid("57773cc1-2b6d-4e22-ad58-3a811afdc9f1");
        holderAuthDO1.setEnterpriseGuid("enterpriseGuid");
        holderAuthDO1.setStoreGuid("storeGuid");
        holderAuthDO1.setUserId(0L);
        when(mockHolderAuthService.getToken(holderAuthDO1)).thenReturn("token");

        when(mockHolderAuthService.getCode("storeGuid")).thenReturn(null);

        // Run the test
        ownItemMappingServiceImplUnderTest.unbindMapping(unItemBindUnbindReq);
    }

    @Test
    public void testBatchUnbindMapping() {
        // Setup
        final UnItemBatchUnbindReq unItemBatchUnbindReq = new UnItemBatchUnbindReq();
        unItemBatchUnbindReq.setStoreGuid("storeGuid");
        final UnItemBaseMapReq unItemBaseMapReq = new UnItemBaseMapReq();
        unItemBaseMapReq.setUnItemSkuId("unItemSkuId");
        unItemBaseMapReq.setErpItemSkuId("erpItemSkuId");
        unItemBatchUnbindReq.setUnItemUnbindList(Arrays.asList(unItemBaseMapReq));
        unItemBatchUnbindReq.setBindFlag(false);

        // Configure HolderAuthService.getHolder(...).
        final HolderAuthDO holderAuthDO = new HolderAuthDO();
        holderAuthDO.setId(0L);
        holderAuthDO.setGuid("57773cc1-2b6d-4e22-ad58-3a811afdc9f1");
        holderAuthDO.setEnterpriseGuid("enterpriseGuid");
        holderAuthDO.setStoreGuid("storeGuid");
        holderAuthDO.setUserId(0L);
        when(mockHolderAuthService.getHolder("storeGuid")).thenReturn(holderAuthDO);

        // Configure HolderAuthService.getToken(...).
        final HolderAuthDO holderAuthDO1 = new HolderAuthDO();
        holderAuthDO1.setId(0L);
        holderAuthDO1.setGuid("57773cc1-2b6d-4e22-ad58-3a811afdc9f1");
        holderAuthDO1.setEnterpriseGuid("enterpriseGuid");
        holderAuthDO1.setStoreGuid("storeGuid");
        holderAuthDO1.setUserId(0L);
        when(mockHolderAuthService.getToken(holderAuthDO1)).thenReturn("token");

        when(mockHolderAuthService.getCode("storeGuid")).thenReturn("code");

        // Configure HolderAuthService.itemBind(...).
        final TakeoutShopOwnItemBindReqDTO takeoutShopOwnItemBindReqDTO = new TakeoutShopOwnItemBindReqDTO();
        takeoutShopOwnItemBindReqDTO.setStoreGuid("storeGuid");
        final BindInfo bindInfo = new BindInfo();
        bindInfo.setGoodsSpecId(0L);
        bindInfo.setThirdSkuId("erpItemSkuId");
        takeoutShopOwnItemBindReqDTO.setGoodsList(Arrays.asList(bindInfo));
        takeoutShopOwnItemBindReqDTO.setStoreCode("StoreCode");
        when(mockHolderAuthService.itemBind(takeoutShopOwnItemBindReqDTO, "code", "token")).thenReturn("result");

        // Configure HolderAuthService.itemUnBind(...).
        final TakeoutShopOwnItemBindReqDTO takeoutShopOwnItemBindReqDTO1 = new TakeoutShopOwnItemBindReqDTO();
        takeoutShopOwnItemBindReqDTO1.setStoreGuid("storeGuid");
        final BindInfo bindInfo1 = new BindInfo();
        bindInfo1.setGoodsSpecId(0L);
        bindInfo1.setThirdSkuId("erpItemSkuId");
        takeoutShopOwnItemBindReqDTO1.setGoodsList(Arrays.asList(bindInfo1));
        takeoutShopOwnItemBindReqDTO1.setStoreCode("StoreCode");
        when(mockHolderAuthService.itemUnBind(takeoutShopOwnItemBindReqDTO1, "code", "token")).thenReturn("result");

        // Run the test
        ownItemMappingServiceImplUnderTest.batchUnbindMapping(unItemBatchUnbindReq);

        // Verify the results
    }

    @Test
    public void testBatchUnbindMapping_HolderAuthServiceGetTokenReturnsNull() {
        // Setup
        final UnItemBatchUnbindReq unItemBatchUnbindReq = new UnItemBatchUnbindReq();
        unItemBatchUnbindReq.setStoreGuid("storeGuid");
        final UnItemBaseMapReq unItemBaseMapReq = new UnItemBaseMapReq();
        unItemBaseMapReq.setUnItemSkuId("unItemSkuId");
        unItemBaseMapReq.setErpItemSkuId("erpItemSkuId");
        unItemBatchUnbindReq.setUnItemUnbindList(Arrays.asList(unItemBaseMapReq));
        unItemBatchUnbindReq.setBindFlag(false);

        // Configure HolderAuthService.getHolder(...).
        final HolderAuthDO holderAuthDO = new HolderAuthDO();
        holderAuthDO.setId(0L);
        holderAuthDO.setGuid("57773cc1-2b6d-4e22-ad58-3a811afdc9f1");
        holderAuthDO.setEnterpriseGuid("enterpriseGuid");
        holderAuthDO.setStoreGuid("storeGuid");
        holderAuthDO.setUserId(0L);
        when(mockHolderAuthService.getHolder("storeGuid")).thenReturn(holderAuthDO);

        // Configure HolderAuthService.getToken(...).
        final HolderAuthDO holderAuthDO1 = new HolderAuthDO();
        holderAuthDO1.setId(0L);
        holderAuthDO1.setGuid("57773cc1-2b6d-4e22-ad58-3a811afdc9f1");
        holderAuthDO1.setEnterpriseGuid("enterpriseGuid");
        holderAuthDO1.setStoreGuid("storeGuid");
        holderAuthDO1.setUserId(0L);
        when(mockHolderAuthService.getToken(holderAuthDO1)).thenReturn(null);

        when(mockHolderAuthService.getCode("storeGuid")).thenReturn("code");

        // Configure HolderAuthService.itemBind(...).
        final TakeoutShopOwnItemBindReqDTO takeoutShopOwnItemBindReqDTO = new TakeoutShopOwnItemBindReqDTO();
        takeoutShopOwnItemBindReqDTO.setStoreGuid("storeGuid");
        final BindInfo bindInfo = new BindInfo();
        bindInfo.setGoodsSpecId(0L);
        bindInfo.setThirdSkuId("erpItemSkuId");
        takeoutShopOwnItemBindReqDTO.setGoodsList(Arrays.asList(bindInfo));
        takeoutShopOwnItemBindReqDTO.setStoreCode("StoreCode");
        when(mockHolderAuthService.itemBind(takeoutShopOwnItemBindReqDTO, "code", "token")).thenReturn("result");

        // Configure HolderAuthService.itemUnBind(...).
        final TakeoutShopOwnItemBindReqDTO takeoutShopOwnItemBindReqDTO1 = new TakeoutShopOwnItemBindReqDTO();
        takeoutShopOwnItemBindReqDTO1.setStoreGuid("storeGuid");
        final BindInfo bindInfo1 = new BindInfo();
        bindInfo1.setGoodsSpecId(0L);
        bindInfo1.setThirdSkuId("erpItemSkuId");
        takeoutShopOwnItemBindReqDTO1.setGoodsList(Arrays.asList(bindInfo1));
        takeoutShopOwnItemBindReqDTO1.setStoreCode("StoreCode");
        when(mockHolderAuthService.itemUnBind(takeoutShopOwnItemBindReqDTO1, "code", "token")).thenReturn("result");

        // Run the test
        ownItemMappingServiceImplUnderTest.batchUnbindMapping(unItemBatchUnbindReq);

        // Verify the results
    }

    @Test(expected = BusinessException.class)
    public void testBatchUnbindMapping_HolderAuthServiceGetCodeReturnsNull() {
        // Setup
        final UnItemBatchUnbindReq unItemBatchUnbindReq = new UnItemBatchUnbindReq();
        unItemBatchUnbindReq.setStoreGuid("storeGuid");
        final UnItemBaseMapReq unItemBaseMapReq = new UnItemBaseMapReq();
        unItemBaseMapReq.setUnItemSkuId("unItemSkuId");
        unItemBaseMapReq.setErpItemSkuId("erpItemSkuId");
        unItemBatchUnbindReq.setUnItemUnbindList(Arrays.asList(unItemBaseMapReq));
        unItemBatchUnbindReq.setBindFlag(false);

        // Configure HolderAuthService.getHolder(...).
        final HolderAuthDO holderAuthDO = new HolderAuthDO();
        holderAuthDO.setId(0L);
        holderAuthDO.setGuid("57773cc1-2b6d-4e22-ad58-3a811afdc9f1");
        holderAuthDO.setEnterpriseGuid("enterpriseGuid");
        holderAuthDO.setStoreGuid("storeGuid");
        holderAuthDO.setUserId(0L);
        when(mockHolderAuthService.getHolder("storeGuid")).thenReturn(holderAuthDO);

        // Configure HolderAuthService.getToken(...).
        final HolderAuthDO holderAuthDO1 = new HolderAuthDO();
        holderAuthDO1.setId(0L);
        holderAuthDO1.setGuid("57773cc1-2b6d-4e22-ad58-3a811afdc9f1");
        holderAuthDO1.setEnterpriseGuid("enterpriseGuid");
        holderAuthDO1.setStoreGuid("storeGuid");
        holderAuthDO1.setUserId(0L);
        when(mockHolderAuthService.getToken(holderAuthDO1)).thenReturn("token");

        when(mockHolderAuthService.getCode("storeGuid")).thenReturn(null);

        // Run the test
        ownItemMappingServiceImplUnderTest.batchUnbindMapping(unItemBatchUnbindReq);
    }

    @Test
    public void testChangeData2() {
        // Setup
        final UnItemBatchUnbindReq unItemBatchUnbindReq = new UnItemBatchUnbindReq();
        unItemBatchUnbindReq.setStoreGuid("storeGuid");
        final UnItemBaseMapReq unItemBaseMapReq = new UnItemBaseMapReq();
        unItemBaseMapReq.setUnItemSkuId("unItemSkuId");
        unItemBaseMapReq.setErpItemSkuId("erpItemSkuId");
        unItemBatchUnbindReq.setUnItemUnbindList(Arrays.asList(unItemBaseMapReq));
        unItemBatchUnbindReq.setBindFlag(false);

        final BindInfo bindInfo = new BindInfo();
        bindInfo.setGoodsSpecId(0L);
        bindInfo.setThirdSkuId("erpItemSkuId");
        final List<BindInfo> expectedResult = Arrays.asList(bindInfo);

        // Run the test
        final List<BindInfo> result = ownItemMappingServiceImplUnderTest.changeData(unItemBatchUnbindReq);

        // Verify the results
        assertEquals(expectedResult, result);
    }
}
