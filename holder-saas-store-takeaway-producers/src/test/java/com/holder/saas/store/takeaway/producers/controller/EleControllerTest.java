package com.holder.saas.store.takeaway.producers.controller;

import com.holder.saas.store.takeaway.producers.config.EleCallbackValidator;
import com.holder.saas.store.takeaway.producers.mapstruct.EleOrderMapstruct;
import com.holder.saas.store.takeaway.producers.service.EleAuthService;
import com.holder.saas.store.takeaway.producers.service.EleCallbackService;
import com.holder.saas.store.takeaway.producers.service.EleOrderService;
import com.holderzone.saas.store.dto.takeaway.EleCallbackBindDTO;
import com.holderzone.saas.store.dto.takeaway.EleOrderItemPriceDTO;
import com.holderzone.saas.store.dto.takeaway.UnOrder;
import com.holderzone.saas.store.dto.takeaway.response.StoreAuthDTO;
import eleme.openapi.sdk.api.entity.order.OGoodsGroup;
import eleme.openapi.sdk.api.entity.order.OGoodsItem;
import eleme.openapi.sdk.api.entity.order.OOrder;
import eleme.openapi.sdk.api.entity.other.OMessage;
import eleme.openapi.sdk.api.entity.packs.ShopContract;
import javafx.util.Pair;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.*;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(EleController.class)
public class EleControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private EleCallbackValidator mockEleCallbackValidator;
    @MockBean
    private EleCallbackService mockEleCallbackService;
    @MockBean
    private EleAuthService mockEleAuthService;
    @MockBean
    private EleOrderService mockEleOrderService;
    @MockBean
    private EleOrderMapstruct mockEleOrderMapstruct;

    @Test
    public void testBindCallback() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(get("/ele/callback/bind")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));

        // Confirm EleAuthService.bindCallback(...).
        final EleCallbackBindDTO eleCallbackBindDTO = new EleCallbackBindDTO();
        eleCallbackBindDTO.setCode("code");
        eleCallbackBindDTO.setState("state");
        eleCallbackBindDTO.setError("error");
        eleCallbackBindDTO.setErrorDescription("errorDescription");
        verify(mockEleAuthService).bindCallback(eleCallbackBindDTO);
    }

    @Test
    public void testOrderCallback() throws Exception {
        // Setup
        when(mockEleCallbackValidator.checkParameter(any(OMessage.class))).thenReturn(new Pair<>(false, "value"));
        when(mockEleCallbackValidator.checkSignature(any(OMessage.class))).thenReturn(false);

        // Run the test and verify the results
        mockMvc.perform(post("/ele/callback/order")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testOrderCallback_EleCallbackValidatorCheckSignatureReturnsTrue() throws Exception {
        // Setup
        when(mockEleCallbackValidator.checkParameter(any(OMessage.class))).thenReturn(new Pair<>(false, "value"));
        when(mockEleCallbackValidator.checkSignature(any(OMessage.class))).thenReturn(true);

        // Run the test and verify the results
        mockMvc.perform(post("/ele/callback/order")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
        verify(mockEleCallbackService).orderCallback(any(OMessage.class));
    }

    @Test
    public void testListAuth() throws Exception {
        // Setup
        // Configure EleAuthService.listAuth(...).
        final StoreAuthDTO storeAuthDTO = new StoreAuthDTO();
        storeAuthDTO.setTakeoutType(0);
        storeAuthDTO.setPlatformName("platformName");
        storeAuthDTO.setStoreNumber("storeNumber");
        storeAuthDTO.setStoreGuid("storeGuid");
        storeAuthDTO.setStoreName("storeName");
        final List<StoreAuthDTO> storeAuthDTOS = Arrays.asList(storeAuthDTO);
        final StoreAuthDTO storeAuthDTO1 = new StoreAuthDTO();
        storeAuthDTO1.setTakeoutType(0);
        storeAuthDTO1.setPlatformName("platformName");
        storeAuthDTO1.setStoreNumber("storeNumber");
        storeAuthDTO1.setStoreGuid("storeGuid");
        storeAuthDTO1.setStoreName("storeName");
        final List<StoreAuthDTO> storeAuthorizationDTOList = Arrays.asList(storeAuthDTO1);
        when(mockEleAuthService.listAuth(storeAuthorizationDTOList)).thenReturn(storeAuthDTOS);

        // Run the test and verify the results
        mockMvc.perform(post("/ele/list_auth")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testListAuth_EleAuthServiceReturnsNoItems() throws Exception {
        // Setup
        // Configure EleAuthService.listAuth(...).
        final StoreAuthDTO storeAuthDTO = new StoreAuthDTO();
        storeAuthDTO.setTakeoutType(0);
        storeAuthDTO.setPlatformName("platformName");
        storeAuthDTO.setStoreNumber("storeNumber");
        storeAuthDTO.setStoreGuid("storeGuid");
        storeAuthDTO.setStoreName("storeName");
        final List<StoreAuthDTO> storeAuthorizationDTOList = Arrays.asList(storeAuthDTO);
        when(mockEleAuthService.listAuth(storeAuthorizationDTOList)).thenReturn(Collections.emptyList());

        // Run the test and verify the results
        mockMvc.perform(post("/ele/list_auth")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("[]", true));
    }

    @Test
    public void testGetTakeoutAuth() throws Exception {
        // Setup
        // Configure EleAuthService.getTakeoutAuth(...).
        final StoreAuthDTO storeAuthDTO = new StoreAuthDTO();
        storeAuthDTO.setTakeoutType(0);
        storeAuthDTO.setPlatformName("platformName");
        storeAuthDTO.setStoreNumber("storeNumber");
        storeAuthDTO.setStoreGuid("storeGuid");
        storeAuthDTO.setStoreName("storeName");
        final StoreAuthDTO storeAuthDTO1 = new StoreAuthDTO();
        storeAuthDTO1.setTakeoutType(0);
        storeAuthDTO1.setPlatformName("platformName");
        storeAuthDTO1.setStoreNumber("storeNumber");
        storeAuthDTO1.setStoreGuid("storeGuid");
        storeAuthDTO1.setStoreName("storeName");
        when(mockEleAuthService.getTakeoutAuth(storeAuthDTO1)).thenReturn(storeAuthDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/ele/get_takeout_auth")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testUpdateDelivery() throws Exception {
        // Setup
        // Configure EleAuthService.updateDelivery(...).
        final StoreAuthDTO storeAuthDTO = new StoreAuthDTO();
        storeAuthDTO.setTakeoutType(0);
        storeAuthDTO.setPlatformName("platformName");
        storeAuthDTO.setStoreNumber("storeNumber");
        storeAuthDTO.setStoreGuid("storeGuid");
        storeAuthDTO.setStoreName("storeName");
        when(mockEleAuthService.updateDelivery(storeAuthDTO)).thenReturn(false);

        // Run the test and verify the results
        mockMvc.perform(post("/ele/update_delivery")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testUpdateDelivery_EleAuthServiceReturnsTrue() throws Exception {
        // Setup
        // Configure EleAuthService.updateDelivery(...).
        final StoreAuthDTO storeAuthDTO = new StoreAuthDTO();
        storeAuthDTO.setTakeoutType(0);
        storeAuthDTO.setPlatformName("platformName");
        storeAuthDTO.setStoreNumber("storeNumber");
        storeAuthDTO.setStoreGuid("storeGuid");
        storeAuthDTO.setStoreName("storeName");
        when(mockEleAuthService.updateDelivery(storeAuthDTO)).thenReturn(true);

        // Run the test and verify the results
        mockMvc.perform(post("/ele/update_delivery")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGetEffectServicePackContract() throws Exception {
        // Setup
        // Configure EleAuthService.getEffectServicePackContract(...).
        final ShopContract shopContract = new ShopContract();
        shopContract.setContractTypeName("-1");
        final UnOrder unOrder = new UnOrder();
        unOrder.setOrderStatus(0);
        unOrder.setShopId(0L);
        unOrder.setSubStoreId(0L);
        unOrder.setShopName("shopName");
        unOrder.setCbMsgType(0);
        when(mockEleAuthService.getEffectServicePackContract(unOrder)).thenReturn(shopContract);

        // Run the test and verify the results
        mockMvc.perform(post("/ele/get_effect_service_packContract")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGetOrderItemPrice() throws Exception {
        // Setup
        // Configure EleOrderService.getOrder(...).
        final OOrder oOrder = new OOrder();
        oOrder.setAddress("address");
        oOrder.setCreatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        oOrder.setActiveAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final OGoodsGroup oGoodsGroup = new OGoodsGroup();
        final OGoodsItem oGoodsItem = new OGoodsItem();
        oGoodsGroup.setItems(Arrays.asList(oGoodsItem));
        oOrder.setGroups(Arrays.asList(oGoodsGroup));
        when(mockEleOrderService.getOrder("storeGuid", "orderId")).thenReturn(oOrder);

        // Configure EleOrderMapstruct.toEleOrderItemPriceDTOS(...).
        final EleOrderItemPriceDTO eleOrderItemPriceDTO = new EleOrderItemPriceDTO();
        eleOrderItemPriceDTO.setExtendCode("extendCode");
        eleOrderItemPriceDTO.setTotal(0.0);
        eleOrderItemPriceDTO.setQuantity(0);
        eleOrderItemPriceDTO.setUserPrice(0.0);
        final List<EleOrderItemPriceDTO> eleOrderItemPriceDTOS = Arrays.asList(eleOrderItemPriceDTO);
        final OGoodsItem oGoodsItem1 = new OGoodsItem();
        oGoodsItem1.setId(0L);
        oGoodsItem1.setSkuId(0L);
        oGoodsItem1.setName("name");
        oGoodsItem1.setOriginalName("originalName");
        oGoodsItem1.setMealPreparation("mealPreparation");
        final List<OGoodsItem> items = Arrays.asList(oGoodsItem1);
        when(mockEleOrderMapstruct.toEleOrderItemPriceDTOS(items)).thenReturn(eleOrderItemPriceDTOS);

        // Run the test and verify the results
        mockMvc.perform(get("/ele/get_order_item_price/{storeGuid}/{orderId}", "storeGuid", "orderId")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGetOrderItemPrice_EleOrderMapstructReturnsNoItems() throws Exception {
        // Setup
        // Configure EleOrderService.getOrder(...).
        final OOrder oOrder = new OOrder();
        oOrder.setAddress("address");
        oOrder.setCreatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        oOrder.setActiveAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final OGoodsGroup oGoodsGroup = new OGoodsGroup();
        final OGoodsItem oGoodsItem = new OGoodsItem();
        oGoodsGroup.setItems(Arrays.asList(oGoodsItem));
        oOrder.setGroups(Arrays.asList(oGoodsGroup));
        when(mockEleOrderService.getOrder("storeGuid", "orderId")).thenReturn(oOrder);

        // Configure EleOrderMapstruct.toEleOrderItemPriceDTOS(...).
        final OGoodsItem oGoodsItem1 = new OGoodsItem();
        oGoodsItem1.setId(0L);
        oGoodsItem1.setSkuId(0L);
        oGoodsItem1.setName("name");
        oGoodsItem1.setOriginalName("originalName");
        oGoodsItem1.setMealPreparation("mealPreparation");
        final List<OGoodsItem> items = Arrays.asList(oGoodsItem1);
        when(mockEleOrderMapstruct.toEleOrderItemPriceDTOS(items)).thenReturn(Collections.emptyList());

        // Run the test and verify the results
        mockMvc.perform(get("/ele/get_order_item_price/{storeGuid}/{orderId}", "storeGuid", "orderId")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("[]", true));
    }

    @Test
    public void testGetOrderItemOOrder() throws Exception {
        // Setup
        // Configure EleOrderService.getOrder(...).
        final OOrder oOrder = new OOrder();
        oOrder.setAddress("address");
        oOrder.setCreatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        oOrder.setActiveAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final OGoodsGroup oGoodsGroup = new OGoodsGroup();
        final OGoodsItem oGoodsItem = new OGoodsItem();
        oGoodsGroup.setItems(Arrays.asList(oGoodsItem));
        oOrder.setGroups(Arrays.asList(oGoodsGroup));
        when(mockEleOrderService.getOrder("storeGuid", "orderId")).thenReturn(oOrder);

        // Run the test and verify the results
        mockMvc.perform(get("/ele/get_order/{storeGuid}/{orderId}", "storeGuid", "orderId")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }
}
