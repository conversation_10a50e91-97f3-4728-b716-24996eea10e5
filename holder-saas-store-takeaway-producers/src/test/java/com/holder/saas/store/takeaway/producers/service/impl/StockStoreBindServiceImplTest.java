package com.holder.saas.store.takeaway.producers.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holder.saas.store.takeaway.producers.entity.domain.StoreBindOrderDO;
import com.holder.saas.store.takeaway.producers.mapper.StockStoreBindOrderMapper;
import com.holder.saas.store.takeaway.producers.service.rpc.OrganizationService;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.takeaway.request.StockStoreBindReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.StockStoreBindReqOrderDTO;
import com.holderzone.saas.store.dto.takeaway.response.StockStoreBindResqDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class StockStoreBindServiceImplTest {

    @Mock
    private StockStoreBindOrderMapper mockOrderMapper;
    @Mock
    private OrganizationService mockOrganizationService;

    private StockStoreBindServiceImpl stockStoreBindServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        stockStoreBindServiceImplUnderTest = new StockStoreBindServiceImpl(mockOrderMapper, mockOrganizationService);
    }

    @Test
    public void testBindStockStore() {
        // Setup
        final StockStoreBindReqDTO req = new StockStoreBindReqDTO("storeGuid", "branchStoreGuid", "branchStoreName");
        final StockStoreBindResqDTO expectedResult = new StockStoreBindResqDTO("storeGuid", "branchStoreGuid",
                "branchStoreName");

        // Run the test
        final StockStoreBindResqDTO result = stockStoreBindServiceImplUnderTest.bindStockStore(req);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetBindStockStore() {
        // Setup
        final StockStoreBindResqDTO expectedResult = new StockStoreBindResqDTO("storeGuid", "branchStoreGuid",
                "branchStoreName");

        // Configure OrganizationService.queryStoreByGuid(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("3dbf413d-49cb-4348-829e-cc79ffd763d8");
        storeDTO.setCode("code");
        storeDTO.setName("branchStoreName");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        when(mockOrganizationService.queryStoreByGuid("storeGuid")).thenReturn(storeDTO);

        // Run the test
        final StockStoreBindResqDTO result = stockStoreBindServiceImplUnderTest.getBindStockStore("storeGuid");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testSaveBindStockStoreOrder() {
        // Setup
        final StockStoreBindReqOrderDTO reqOrderDTO = new StockStoreBindReqOrderDTO("storeGuid", "branchStoreGuid",
                "orderId");

        // Run the test
        stockStoreBindServiceImplUnderTest.saveBindStockStoreOrder(reqOrderDTO);

        // Verify the results
        // Confirm StockStoreBindOrderMapper.insert(...).
        final StoreBindOrderDO entity = new StoreBindOrderDO();
        entity.setId(0L);
        entity.setStoreGuid("storeGuid");
        entity.setBranchStoreGuid("branchStoreGuid");
        entity.setOrderId("orderId");
        entity.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        verify(mockOrderMapper).insert(entity);
    }

    @Test
    public void testGetBindStockStoreOrder() {
        // Setup
        final StoreBindOrderDO expectedResult = new StoreBindOrderDO();
        expectedResult.setId(0L);
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setBranchStoreGuid("branchStoreGuid");
        expectedResult.setOrderId("orderId");
        expectedResult.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Configure StockStoreBindOrderMapper.selectOne(...).
        final StoreBindOrderDO storeBindOrderDO = new StoreBindOrderDO();
        storeBindOrderDO.setId(0L);
        storeBindOrderDO.setStoreGuid("storeGuid");
        storeBindOrderDO.setBranchStoreGuid("branchStoreGuid");
        storeBindOrderDO.setOrderId("orderId");
        storeBindOrderDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockOrderMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(storeBindOrderDO);

        // Run the test
        final StoreBindOrderDO result = stockStoreBindServiceImplUnderTest.getBindStockStoreOrder("orderId",
                "branchStoreGuid");

        // Verify the results
        assertEquals(expectedResult, result);
    }
}
