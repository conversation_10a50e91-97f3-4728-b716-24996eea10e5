package com.holder.saas.store.takeaway.producers.service.converter;

import com.alipay.api.domain.*;
import com.alipay.api.response.AlipayMarketingCertificateCertificationPrepareuseResponse;
import com.alipay.api.response.AntMerchantExpandShopQueryResponse;
import com.holder.saas.store.takeaway.producers.entity.domain.GroupStoreBindDO;
import com.holder.saas.store.takeaway.producers.entity.domain.MtAuthDO;
import com.holder.saas.store.takeaway.producers.entity.dto.group.AbcCouponRspDTO;
import com.holder.saas.store.takeaway.producers.entity.dto.group.DouYinCertificate;
import com.holder.saas.store.takeaway.producers.entity.dto.group.DouYinStoreBindRspDTO;
import com.holder.saas.store.takeaway.producers.entity.dto.group.DouYinVerifyResult;
import com.holderzone.saas.store.dto.business.group.StoreBindDTO;
import com.holderzone.saas.store.dto.order.response.groupon.GroupVerifyDTO;
import com.holderzone.saas.store.dto.takeaway.request.CouPonPreReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.CouPonReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.CouponDelReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.MtCouponReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtCouponDoCheckRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtCouponPreRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.StoreAuthDTO;
import org.junit.Test;

import java.util.Arrays;
import java.util.Calendar;
import java.util.GregorianCalendar;
import java.util.List;

import static org.junit.Assert.assertEquals;

public class GroupBuyConverterTest {

    @Test
    public void testFromNameAndStoreBind() {
        // Setup
        final DouYinStoreBindRspDTO poiRsp = new DouYinStoreBindRspDTO();
        poiRsp.setTaskId("taskId");
        poiRsp.setPoiName("poiName");

        final StoreBindDTO storeBindDTO = new StoreBindDTO();
        storeBindDTO.setGroupBuyType(0);
        storeBindDTO.setAccountId("accountId");
        storeBindDTO.setPoiId("poiId");
        storeBindDTO.setStoreGuid("storeGuid");

        final GroupStoreBindDO expectedResult = new GroupStoreBindDO();
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setPoiId("poiId");
        expectedResult.setPoiName("poiName");
        expectedResult.setType(0);
        expectedResult.setTaskId("taskId");

        // Run the test
        final GroupStoreBindDO result = GroupBuyConverter.fromNameAndStoreBind(poiRsp, storeBindDTO, 0);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testToStoreAuthList() {
        // Setup
        final GroupStoreBindDO groupStoreBindDO = new GroupStoreBindDO();
        groupStoreBindDO.setStoreGuid("storeGuid");
        groupStoreBindDO.setPoiId("poiId");
        groupStoreBindDO.setPoiName("poiName");
        groupStoreBindDO.setType(0);
        groupStoreBindDO.setTaskId("taskId");
        final List<GroupStoreBindDO> bindList = Arrays.asList(groupStoreBindDO);
        final MtAuthDO auth = new MtAuthDO();
        auth.setId(0);
        auth.setGuid("95fb466e-07ed-49b2-a50f-c1be5baba55d");
        auth.setEPoiId("ePoiId");
        auth.setMtStoreGuid("storeGuid");
        auth.setMtStoreName("poiName");

        final StoreAuthDTO storeAuthDTO = new StoreAuthDTO();
        storeAuthDTO.setTakeoutType(0);
        storeAuthDTO.setPlatformName("desc");
        storeAuthDTO.setStoreGuid("storeGuid");
        storeAuthDTO.setShopName("poiName");
        storeAuthDTO.setBindingStatus(0);
        final List<StoreAuthDTO> expectedResult = Arrays.asList(storeAuthDTO);

        // Run the test
        final List<StoreAuthDTO> result = GroupBuyConverter.toStoreAuthList(bindList, auth);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testToDouYinCouponPreRespList() {
        // Setup
        final DouYinCertificate douYinCertificate = new DouYinCertificate();
        douYinCertificate.setEncryptedCode("couponCode");
        douYinCertificate.setExpireTime(0);
        final DouYinCertificate.Amount amount = new DouYinCertificate.Amount();
        amount.setCouponPayAmount(0);
        douYinCertificate.setAmount(amount);
        final DouYinCertificate.Sku sku = new DouYinCertificate.Sku();
        sku.setSkuId("skuId");
        sku.setTitle("couponName");
        sku.setGrouponType(0);
        sku.setMarketPrice(0);
        douYinCertificate.setSku(sku);
        final List<DouYinCertificate> certificateList = Arrays.asList(douYinCertificate);
        final MtCouponPreRespDTO mtCouponPreRespDTO = new MtCouponPreRespDTO();
        mtCouponPreRespDTO.setCount(0);
        mtCouponPreRespDTO.setCouponBuyPrice(0.0);
        mtCouponPreRespDTO.setCouponCode("couponCode");
        mtCouponPreRespDTO.setIsVoucher(false);
        mtCouponPreRespDTO.setCouponEndTime("couponEndTime");
        mtCouponPreRespDTO.setDealBeginTime("dealBeginTime");
        mtCouponPreRespDTO.setDealId(0);
        mtCouponPreRespDTO.setDealPrice(0.0);
        mtCouponPreRespDTO.setDealTitle("couponName");
        mtCouponPreRespDTO.setDealValue(0.0);
        mtCouponPreRespDTO.setRawTitle("rawTitle");
        mtCouponPreRespDTO.setCouponType(0);
        mtCouponPreRespDTO.setItemId("itemId");
        mtCouponPreRespDTO.setSkuId("skuId");
        mtCouponPreRespDTO.setUserId("userId");
        mtCouponPreRespDTO.setOrderId("orderId");
        final List<MtCouponPreRespDTO> expectedResult = Arrays.asList(mtCouponPreRespDTO);

        // Run the test
        final List<MtCouponPreRespDTO> result = GroupBuyConverter.toDouYinCouponPreRespList(certificateList);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testToDouYinCouponVerifyRespList() {
        // Setup
        final DouYinVerifyResult douYinVerifyResult = new DouYinVerifyResult();
        douYinVerifyResult.setResult(0);
        douYinVerifyResult.setMsg("msg");
        douYinVerifyResult.setVerifyId("useOrderNo");
        douYinVerifyResult.setCertificateId("certificateId");
        douYinVerifyResult.setOriginCode("certificateId");
        final List<DouYinVerifyResult> verifyResultList = Arrays.asList(douYinVerifyResult);
        final GroupVerifyDTO groupVerifyDTO = new GroupVerifyDTO();
        groupVerifyDTO.setGroupBuyType(0);
        groupVerifyDTO.setCode("certificateId");
        groupVerifyDTO.setVerifyId("useOrderNo");
        groupVerifyDTO.setCertificateId("certificateId");
        groupVerifyDTO.setErpId("erpId");
        groupVerifyDTO.setErpName("erpName");
        groupVerifyDTO.setStoreGuid("erpId");
        groupVerifyDTO.setUserId("userId");
        final List<GroupVerifyDTO> expectedResult = Arrays.asList(groupVerifyDTO);

        // Run the test
        final List<GroupVerifyDTO> result = GroupBuyConverter.toDouYinCouponVerifyRespList(verifyResultList);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testMtPreReqFromPreReq() {
        // Setup
        final CouPonPreReqDTO couPonPreReqDTO = new CouPonPreReqDTO();
        couPonPreReqDTO.setStoreGuid("erpId");
        couPonPreReqDTO.setCouponCode("couponCode");
        couPonPreReqDTO.setErpOrderId("erpOrderId");
        couPonPreReqDTO.setActivityGuid("activityGuid");
        couPonPreReqDTO.setUsedGuidList(Arrays.asList("value"));

        final MtCouponReqDTO expectedResult = new MtCouponReqDTO();
        expectedResult.setStoreGuid("erpId");
        expectedResult.setCouponCode("couponCode");
        expectedResult.setCount(0);
        expectedResult.setErpId("erpId");
        expectedResult.setErpName("erpName");
        expectedResult.setErpOrderId("erpOrderId");

        // Run the test
        final MtCouponReqDTO result = GroupBuyConverter.mtPreReqFromPreReq(couPonPreReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testMtCouPonReqFromCouPonReq() {
        // Setup
        final CouPonReqDTO couPonReq = new CouPonReqDTO();
        couPonReq.setStoreGuid("erpId");
        couPonReq.setCouponCode("couponCode");
        couPonReq.setCount(0);
        couPonReq.setErpId("erpId");
        couPonReq.setErpName("erpName");
        couPonReq.setErpOrderId("erpOrderId");
        couPonReq.setCouponCodeList(Arrays.asList("value"));

        final MtCouponReqDTO expectedResult = new MtCouponReqDTO();
        expectedResult.setStoreGuid("erpId");
        expectedResult.setCouponCode("couponCode");
        expectedResult.setCount(0);
        expectedResult.setErpId("erpId");
        expectedResult.setErpName("erpName");
        expectedResult.setErpOrderId("erpOrderId");

        // Run the test
        final MtCouponReqDTO result = GroupBuyConverter.mtCouPonReqFromCouPonReq(couPonReq);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testToMtCouponPreRespList() {
        // Setup
        final MtCouponPreRespDTO mtCouponPreRespDTO = new MtCouponPreRespDTO();
        mtCouponPreRespDTO.setCount(0);
        mtCouponPreRespDTO.setCouponBuyPrice(0.0);
        mtCouponPreRespDTO.setCouponCode("couponCode");
        mtCouponPreRespDTO.setIsVoucher(false);
        mtCouponPreRespDTO.setCouponEndTime("couponEndTime");
        mtCouponPreRespDTO.setDealBeginTime("dealBeginTime");
        mtCouponPreRespDTO.setDealId(0);
        mtCouponPreRespDTO.setDealPrice(0.0);
        mtCouponPreRespDTO.setDealTitle("couponName");
        mtCouponPreRespDTO.setDealValue(0.0);
        mtCouponPreRespDTO.setRawTitle("rawTitle");
        mtCouponPreRespDTO.setCouponType(0);
        mtCouponPreRespDTO.setItemId("itemId");
        mtCouponPreRespDTO.setSkuId("skuId");
        mtCouponPreRespDTO.setUserId("userId");
        mtCouponPreRespDTO.setOrderId("orderId");

        final MtCouponPreRespDTO mtCouponPreRespDTO1 = new MtCouponPreRespDTO();
        mtCouponPreRespDTO1.setCount(0);
        mtCouponPreRespDTO1.setCouponBuyPrice(0.0);
        mtCouponPreRespDTO1.setCouponCode("couponCode");
        mtCouponPreRespDTO1.setIsVoucher(false);
        mtCouponPreRespDTO1.setCouponEndTime("couponEndTime");
        mtCouponPreRespDTO1.setDealBeginTime("dealBeginTime");
        mtCouponPreRespDTO1.setDealId(0);
        mtCouponPreRespDTO1.setDealPrice(0.0);
        mtCouponPreRespDTO1.setDealTitle("couponName");
        mtCouponPreRespDTO1.setDealValue(0.0);
        mtCouponPreRespDTO1.setRawTitle("rawTitle");
        mtCouponPreRespDTO1.setCouponType(0);
        mtCouponPreRespDTO1.setItemId("itemId");
        mtCouponPreRespDTO1.setSkuId("skuId");
        mtCouponPreRespDTO1.setUserId("userId");
        mtCouponPreRespDTO1.setOrderId("orderId");
        final List<MtCouponPreRespDTO> expectedResult = Arrays.asList(mtCouponPreRespDTO1);

        // Run the test
        final List<MtCouponPreRespDTO> result = GroupBuyConverter.toMtCouponPreRespList(new CouPonPreReqDTO(),mtCouponPreRespDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testToMtCouponVerifyRespList() {
        // Setup
        final MtCouponDoCheckRespDTO mtCouponDoCheckResp = new MtCouponDoCheckRespDTO();
        mtCouponDoCheckResp.setOrderId("orderId");
        mtCouponDoCheckResp.setCouponCodes(Arrays.asList("value"));
        mtCouponDoCheckResp.setDealTitle("dealTitle");
        mtCouponDoCheckResp.setDealValue(0.0);
        mtCouponDoCheckResp.setDealId(0);

        final GroupVerifyDTO groupVerifyDTO = new GroupVerifyDTO();
        groupVerifyDTO.setGroupBuyType(0);
        groupVerifyDTO.setCode("certificateId");
        groupVerifyDTO.setVerifyId("useOrderNo");
        groupVerifyDTO.setCertificateId("certificateId");
        groupVerifyDTO.setErpId("erpId");
        groupVerifyDTO.setErpName("erpName");
        groupVerifyDTO.setStoreGuid("erpId");
        groupVerifyDTO.setUserId("userId");
        final List<GroupVerifyDTO> expectedResult = Arrays.asList(groupVerifyDTO);

        // Run the test
        final List<GroupVerifyDTO> result = GroupBuyConverter.toMtCouponVerifyRespList(new CouPonReqDTO(), mtCouponDoCheckResp);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testMtRevokeFromRevokeReq() {
        // Setup
        final GroupVerifyDTO revokeReq = new GroupVerifyDTO();
        revokeReq.setGroupBuyType(0);
        revokeReq.setCode("certificateId");
        revokeReq.setVerifyId("useOrderNo");
        revokeReq.setCertificateId("certificateId");
        revokeReq.setErpId("erpId");
        revokeReq.setErpName("erpName");
        revokeReq.setStoreGuid("erpId");
        revokeReq.setUserId("userId");

        final CouponDelReqDTO expectedResult = new CouponDelReqDTO();
        expectedResult.setStoreGuid("erpId");
        expectedResult.setCouponCode("certificateId");
        expectedResult.setErpId("erpId");
        expectedResult.setErpName("erpName");

        // Run the test
        final CouponDelReqDTO result = GroupBuyConverter.mtRevokeFromRevokeReq(revokeReq);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testFromAliPayStoreBind() {
        // Setup
        final AntMerchantExpandShopQueryResponse response = new AntMerchantExpandShopQueryResponse();
        response.setBrandId("brandId");
        final AddressInfo businessAddress = new AddressInfo();
        businessAddress.setAddress("address");
        businessAddress.setCityCode("cityCode");
        response.setBusinessAddress(businessAddress);
        response.setShopName("poiName");

        final StoreBindDTO storeBind = new StoreBindDTO();
        storeBind.setGroupBuyType(0);
        storeBind.setAccountId("accountId");
        storeBind.setPoiId("poiId");
        storeBind.setStoreGuid("storeGuid");

        final GroupStoreBindDO expectedResult = new GroupStoreBindDO();
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setPoiId("poiId");
        expectedResult.setPoiName("poiName");
        expectedResult.setType(0);
        expectedResult.setTaskId("taskId");

        // Run the test
        final GroupStoreBindDO result = GroupBuyConverter.fromAliPayStoreBind(response, storeBind);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testToAliPayCouponPreRespList() {
        // Setup
        final AlipayMarketingCertificateCertificationPrepareuseResponse prepareUseResponse = new AlipayMarketingCertificateCertificationPrepareuseResponse();
        final CertificatePrepareInfo certificatePrepareInfo = new CertificatePrepareInfo();
        final CertificateInstanceAmountInfo amountInfo = new CertificateInstanceAmountInfo();
        amountInfo.setOriginalPrice("originalPrice");
        amountInfo.setSalePrice("salePrice");
        certificatePrepareInfo.setAmountInfo(amountInfo);
        certificatePrepareInfo.setEncryptedCode("couponCode");
        final CertificateSkuInfo skuInfo = new CertificateSkuInfo();
        skuInfo.setItemId("itemId");
        skuInfo.setItemType("itemType");
        skuInfo.setSkuId("skuId");
        skuInfo.setTitle("couponName");
        certificatePrepareInfo.setSkuInfo(skuInfo);
        certificatePrepareInfo.setValidBeginTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        certificatePrepareInfo.setValidEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        prepareUseResponse.setCertificatePrepareInfoList(Arrays.asList(certificatePrepareInfo));
        prepareUseResponse.setOrderId("orderId");
        prepareUseResponse.setUserId("userId");

        final MtCouponPreRespDTO mtCouponPreRespDTO = new MtCouponPreRespDTO();
        mtCouponPreRespDTO.setCount(0);
        mtCouponPreRespDTO.setCouponBuyPrice(0.0);
        mtCouponPreRespDTO.setCouponCode("couponCode");
        mtCouponPreRespDTO.setIsVoucher(false);
        mtCouponPreRespDTO.setCouponEndTime("couponEndTime");
        mtCouponPreRespDTO.setDealBeginTime("dealBeginTime");
        mtCouponPreRespDTO.setDealId(0);
        mtCouponPreRespDTO.setDealPrice(0.0);
        mtCouponPreRespDTO.setDealTitle("couponName");
        mtCouponPreRespDTO.setDealValue(0.0);
        mtCouponPreRespDTO.setRawTitle("rawTitle");
        mtCouponPreRespDTO.setCouponType(0);
        mtCouponPreRespDTO.setItemId("itemId");
        mtCouponPreRespDTO.setSkuId("skuId");
        mtCouponPreRespDTO.setUserId("userId");
        mtCouponPreRespDTO.setOrderId("orderId");
        final List<MtCouponPreRespDTO> expectedResult = Arrays.asList(mtCouponPreRespDTO);

        // Run the test
        final List<MtCouponPreRespDTO> result = GroupBuyConverter.toAliPayCouponPreRespList(prepareUseResponse);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testToAliPayCouponVerifyRespList() {
        // Setup
        final CertificateUseResult certificateUseResult = new CertificateUseResult();
        final CertificateInstanceAmountInfo amountInfo = new CertificateInstanceAmountInfo();
        amountInfo.setOriginalPrice("originalPrice");
        amountInfo.setReceiptAmount("receiptAmount");
        certificateUseResult.setAmountInfo(amountInfo);
        certificateUseResult.setCertificateId("certificateId");
        certificateUseResult.setUseOrderNo("useOrderNo");
        final List<CertificateUseResult> useResultList = Arrays.asList(certificateUseResult);
        final GroupVerifyDTO groupVerifyDTO = new GroupVerifyDTO();
        groupVerifyDTO.setGroupBuyType(0);
        groupVerifyDTO.setCode("certificateId");
        groupVerifyDTO.setVerifyId("useOrderNo");
        groupVerifyDTO.setCertificateId("certificateId");
        groupVerifyDTO.setErpId("erpId");
        groupVerifyDTO.setErpName("erpName");
        groupVerifyDTO.setStoreGuid("erpId");
        groupVerifyDTO.setUserId("userId");
        final List<GroupVerifyDTO> expectedResult = Arrays.asList(groupVerifyDTO);

        // Run the test
        final List<GroupVerifyDTO> result = GroupBuyConverter.toAliPayCouponVerifyRespList(useResultList, "userId");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testToAbcCouponPreRespList() {
        // Setup
        final AbcCouponRspDTO couponRsp = new AbcCouponRspDTO();
        couponRsp.setCouponName("couponName");
        couponRsp.setValue("value");
        couponRsp.setPayAmount(0.0);
        couponRsp.setCouponId(0L);
        couponRsp.setMerchantCouponType("merchantCouponType");
        couponRsp.setExpiredTime(0L);

        final MtCouponPreRespDTO mtCouponPreRespDTO = new MtCouponPreRespDTO();
        mtCouponPreRespDTO.setCount(0);
        mtCouponPreRespDTO.setCouponBuyPrice(0.0);
        mtCouponPreRespDTO.setCouponCode("couponCode");
        mtCouponPreRespDTO.setIsVoucher(false);
        mtCouponPreRespDTO.setCouponEndTime("couponEndTime");
        mtCouponPreRespDTO.setDealBeginTime("dealBeginTime");
        mtCouponPreRespDTO.setDealId(0);
        mtCouponPreRespDTO.setDealPrice(0.0);
        mtCouponPreRespDTO.setDealTitle("couponName");
        mtCouponPreRespDTO.setDealValue(0.0);
        mtCouponPreRespDTO.setRawTitle("rawTitle");
        mtCouponPreRespDTO.setCouponType(0);
        mtCouponPreRespDTO.setItemId("itemId");
        mtCouponPreRespDTO.setSkuId("skuId");
        mtCouponPreRespDTO.setUserId("userId");
        mtCouponPreRespDTO.setOrderId("orderId");
        final List<MtCouponPreRespDTO> expectedResult = Arrays.asList(mtCouponPreRespDTO);

        // Run the test
        final List<MtCouponPreRespDTO> result = GroupBuyConverter.toAbcCouponPreRespList(couponRsp, "couponCode");

        // Verify the results
        assertEquals(expectedResult, result);
    }
}
