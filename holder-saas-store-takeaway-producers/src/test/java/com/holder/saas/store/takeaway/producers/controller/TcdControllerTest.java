package com.holder.saas.store.takeaway.producers.controller;

import com.holder.saas.store.takeaway.producers.service.TcdAuthService;
import com.holder.saas.store.takeaway.producers.service.TcdCallbackService;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutTCDConfirmTheMealDTO;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutTCDDiningOutDTO;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutTCDPickUpDTO;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutTcdOrderReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.StoreAuthDTO;
import com.holderzone.saas.store.dto.takeaway.response.TcdCommonRespDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(TcdController.class)
public class TcdControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private TcdAuthService mockTcdAuthService;
    @MockBean
    private TcdCallbackService mockTcdCallbackService;

    @Test
    public void testOrderCallback() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(post("/tcd/callback/order")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));

        // Confirm TcdCallbackService.orderCallback(...).
        final TakeoutTcdOrderReqDTO takeoutTcdOrderReqDTO = new TakeoutTcdOrderReqDTO();
        takeoutTcdOrderReqDTO.setMerchantId(0L);
        takeoutTcdOrderReqDTO.setMerchantNo(0L);
        takeoutTcdOrderReqDTO.setMerchantUserId(0L);
        takeoutTcdOrderReqDTO.setBrandId(0L);
        takeoutTcdOrderReqDTO.setEnterpriseGuid("enterpriseGuid");
        verify(mockTcdCallbackService).orderCallback(takeoutTcdOrderReqDTO);
    }

    @Test
    public void testGetTakeoutAuth() throws Exception {
        // Setup
        // Configure TcdAuthService.getTakeoutAuth(...).
        final StoreAuthDTO storeAuthDTO = new StoreAuthDTO();
        storeAuthDTO.setTakeoutType(0);
        storeAuthDTO.setPlatformName("platformName");
        storeAuthDTO.setStoreNumber("storeNumber");
        storeAuthDTO.setStoreGuid("storeGuid");
        storeAuthDTO.setStoreName("storeName");
        final StoreAuthDTO storeAuthDTO1 = new StoreAuthDTO();
        storeAuthDTO1.setTakeoutType(0);
        storeAuthDTO1.setPlatformName("platformName");
        storeAuthDTO1.setStoreNumber("storeNumber");
        storeAuthDTO1.setStoreGuid("storeGuid");
        storeAuthDTO1.setStoreName("storeName");
        when(mockTcdAuthService.getTakeoutAuth(storeAuthDTO1)).thenReturn(storeAuthDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/tcd/get_takeout_auth")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testUpdateDelivery() throws Exception {
        // Setup
        // Configure TcdAuthService.updateDelivery(...).
        final StoreAuthDTO storeAuthDTO = new StoreAuthDTO();
        storeAuthDTO.setTakeoutType(0);
        storeAuthDTO.setPlatformName("platformName");
        storeAuthDTO.setStoreNumber("storeNumber");
        storeAuthDTO.setStoreGuid("storeGuid");
        storeAuthDTO.setStoreName("storeName");
        when(mockTcdAuthService.updateDelivery(storeAuthDTO)).thenReturn(false);

        // Run the test and verify the results
        mockMvc.perform(post("/tcd/update_delivery")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testUpdateDelivery_TcdAuthServiceReturnsTrue() throws Exception {
        // Setup
        // Configure TcdAuthService.updateDelivery(...).
        final StoreAuthDTO storeAuthDTO = new StoreAuthDTO();
        storeAuthDTO.setTakeoutType(0);
        storeAuthDTO.setPlatformName("platformName");
        storeAuthDTO.setStoreNumber("storeNumber");
        storeAuthDTO.setStoreGuid("storeGuid");
        storeAuthDTO.setStoreName("storeName");
        when(mockTcdAuthService.updateDelivery(storeAuthDTO)).thenReturn(true);

        // Run the test and verify the results
        mockMvc.perform(post("/tcd/update_delivery")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testDiningOutTcd() throws Exception {
        // Setup
        // Configure TcdAuthService.diningOutTcd(...).
        final TcdCommonRespDTO tcdCommonRespDTO = new TcdCommonRespDTO();
        tcdCommonRespDTO.setCode(0);
        tcdCommonRespDTO.setMessage("message");
        final TakeoutTCDDiningOutDTO takeoutTCDDiningOutDTO = new TakeoutTCDDiningOutDTO();
        takeoutTCDDiningOutDTO.setOrderSn("orderSn");
        takeoutTCDDiningOutDTO.setStoreGuid("storeGuid");
        takeoutTCDDiningOutDTO.setOrderState("orderState");
        takeoutTCDDiningOutDTO.setToken("token");
        when(mockTcdAuthService.diningOutTcd(takeoutTCDDiningOutDTO)).thenReturn(tcdCommonRespDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/tcd/dining_out_tcd")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testConfirmTheMealTcd() throws Exception {
        // Setup
        // Configure TcdAuthService.confirmTheMealTcd(...).
        final TcdCommonRespDTO tcdCommonRespDTO = new TcdCommonRespDTO();
        tcdCommonRespDTO.setCode(0);
        tcdCommonRespDTO.setMessage("message");
        final TakeoutTCDConfirmTheMealDTO takeoutTCDConfirmTheMealDTO = new TakeoutTCDConfirmTheMealDTO();
        takeoutTCDConfirmTheMealDTO.setOrderSn("orderSn");
        takeoutTCDConfirmTheMealDTO.setStoreGuid("storeGuid");
        takeoutTCDConfirmTheMealDTO.setWriteOffCode("writeOffCode");
        takeoutTCDConfirmTheMealDTO.setOrderState("orderState");
        takeoutTCDConfirmTheMealDTO.setToken("token");
        when(mockTcdAuthService.confirmTheMealTcd(takeoutTCDConfirmTheMealDTO)).thenReturn(tcdCommonRespDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/tcd/confirm_the_meal_tcd")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testPickUpTcd() throws Exception {
        // Setup
        // Configure TcdAuthService.pickUpTcd(...).
        final TcdCommonRespDTO tcdCommonRespDTO = new TcdCommonRespDTO();
        tcdCommonRespDTO.setCode(0);
        tcdCommonRespDTO.setMessage("message");
        final TakeoutTCDPickUpDTO takeoutTCDPickUpDTO = new TakeoutTCDPickUpDTO();
        takeoutTCDPickUpDTO.setOrderSn("orderSn");
        takeoutTCDPickUpDTO.setStoreGuid("storeGuid");
        takeoutTCDPickUpDTO.setWriteOffCode("writeOffCode");
        takeoutTCDPickUpDTO.setOrderState("orderState");
        takeoutTCDPickUpDTO.setToken("token");
        when(mockTcdAuthService.pickUpTcd(takeoutTCDPickUpDTO)).thenReturn(tcdCommonRespDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/tcd/pick_up_tcd")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }
}
