ref:
  eureka:
    ip-address: ***************
    port: 8141
  zipkin:
    ip-address: **************
    port: 9411
  rocketmq:
    ip-address: ***************
    port: 9876
server:
  port: 8918
  undertow:
    max-http-post-size: 0
    # 设置 IO 线程数, 它主要执行非阻塞的任务,它们会负责多个连接, 默认设置每个 CPU 核 心一个线程,数量和 CPU 内核数目一样即可
    io-threads: 4
    # 阻塞任务线程池, 当执行类似 servlet 请求阻塞操作, undertow 会从这个线程池中取得 线程,它的值设置取决于系统的负载  io-threads*8
    worker-threads: 32
    # 以下的配置会影响 buffer,这些 buffer 会用于服务器连接的 IO 操作,有点类似 netty 的 池化内存管理
    # 每块 buffer 的空间大小,越小的空间被利用越充分
    buffer-size: 1024
    # 每个区分配的 buffer 数量 , 所以 pool 的大小是 buffer-size * buffers-per-region
    #buffers-per-region: 1024 # 这个参数不需要写了
    # 是否分配的直接内存
    direct-buffers: true

eureka:
  instance:
    lease-renewal-interval-in-seconds: 5
    lease-expiration-duration-in-seconds: 10
    instance-id: ${spring.cloud.client.ip-address}:${server.port}
    prefer-ip-address: true
  client:
    service-url:
      defaultZone: http://${ref.eureka.ip-address}:${ref.eureka.port}/eureka/

spring:
  application:
    name: holder-saas-takeaway-producer
  zipkin:
    base-url: http://${ref.zipkin.ip-address}:${ref.zipkin.port}/
    service:
      name: holder-saas-takeaway-producer
    sender:
      type: web
    enabled: true
  sleuth:
    sampler:
      probability: 1.0
    enabled: true
  datasource:
    url: jdbc:mysql://${spring.datasource.host}:3306/hst_takeaway_db?autoReconnect=true&failOverReadOnly=false&useUnicode=true&characterEncoding=utf8&allowMultiQueries=true&useSSL=false&useJDBCCompliantTimezoneShift=true&useLegacyDatetimeCode=false&serverTimezone=Asia/Shanghai
    password: mysqlHolder
    username: root
    host: ***************
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      connection-error-retry-attempts: 2
      break-after-acquire-failure: true
      min-idle: 10
      max-active: 500
      max-wait: 60000
      initial-size: 10
  redis:
    host: ***************
    database: 1
    password: eIx6TynJq
    port: 6379
  rocketmq:
    namesrv-addr: ${ref.rocketmq.ip-address}:${ref.rocketmq.port}
    producer-group-name: takeOrderProducer
    retry-times-when-send-async-failed: 4
    retry-times-when-send-failed: 4
    compress-msg-body-over-how-much: 5120
#    没有个性化的redis可以去掉
# feign配置，使用appach client 替代默认urlConnection
feign:
  hystrix:
    enabled: true
  httpclient:
    enabled: true
    connection-timeout: 30000
    max-connections: 5000
    time-to-live: 30
    time-to-live-unit: seconds
    max-connections-per-route: 1000
  okhttp:
    enabled: false
  client:
    config:
      default:
        connectTimeout: 10000
        readTimeout: 10000
#hystrix 配置
hystrix:
  threadpool:
    default:
      coreSize: 50
      allowMaximumSizeToDivergeFromCoreSize: true
      maxQueueSize: -1
      maximumSize: 100
      queueSizeRejectionThreshold: -1
      keepAliveTimeMinutes: 10
      metrics:
        rollingStats: 10
        numBuckets: 50
  command:
    default:
      fallback:
        isolation:
          semaphore:
            maxConcurrentRequests: 100
      circuitBreaker:
        requestVolumeThreshold: 1000
      execution:
        timeout:
          enable: true
        isolation:
          strategy: SEMAPHORE
          semaphore:
            maxConcurrentRequests: 3000
          thread:
            timeoutInMilliseconds: 30000

#dev
#ele:
#  IS_SAND_BOX: true
#  REDIRECT_URL: http://takeout.easy.echosite.cn/merchant/waimai/ele/bind/callback
#  CLIENT_KEY: pwQhokx8Q4
#  SECRET: 5adb17934eef02569db21197e9062e03326b9c17

#uat
ele:
  APP_ID: 28778374
  IS_SAND_BOX: true
  BINDING_URL: https://open-api-sandbox.shop.ele.me/authorize
  REDIRECT_URL: https://erpbs01.holderzone.com/merchant/takeout/callback/ele/bind
  CLIENT_KEY: pwQhokx8Q4
  SECRET: 5adb17934eef02569db21197e9062e03326b9c17
  TOKEN_REFRESH_AHEAD_HOURS: 12
  REFRESH_TOKEN_VALIDITY_PERIOD_DAYS: 15

mt:
  DEVELOPER_ID: 104795
  SIGN_KEY: 5jydvc7w8ee8fx7w


logging:
  level:
    com.holder.saas.store.takeaway.producers: debug

mybatis-plus:
  mapper-locations: classpath*:/mapper/**Mapper.xml
  typeAliasesPackage: com.holder.saas.store.takeaway.producers.entity.domain
  configuration:
    map-underscore-to-camel-case: true
    aggressive-lazy-loading: true
    auto-mapping-behavior: partial
    auto-mapping-unknown-column-behavior: warning
    cache-enabled: false
    call-setters-on-nulls: false
  global-config:
    refresh: false
    db-config:
      db-type: mysql
      id-type: auto
      field-strategy: not_empty
      logic-delete-value: 1
      logic-not-delete-value: 0