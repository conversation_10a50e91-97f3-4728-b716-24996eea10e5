<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holder.saas.store.takeaway.producers.mapper.EleAuthMapper">

    <select id="eleAuthList" resultType="com.holder.saas.store.takeaway.producers.entity.domain.EleAuthDO">
        SELECT * FROM hst_ele_auth a
        WHERE a.user_id = #{userId}
        ORDER BY id desc
        limit 0,1
    </select>

</mapper>
