value:
  eureka:
    hostname: ***************
  rocketmq:
    hostname: ***************
  redis:
    hostname: test-holder-saas-rocketmq

server:
  port: 8918
  undertow:
    max-http-post-size: 0
    # 设置 IO 线程数, 它主要执行非阻塞的任务,它们会负责多个连接, 默认设置每个 CPU 核 心一个线程,数量和 CPU 内核数目一样即可
    io-threads: 4
    # 阻塞任务线程池, 当执行类似 servlet 请求阻塞操作, undertow 会从这个线程池中取得 线程,它的值设置取决于系统的负载  io-threads*8
    worker-threads: 32
    # 以下的配置会影响 buffer,这些 buffer 会用于服务器连接的 IO 操作,有点类似 netty 的 池化内存管理
    # 每块 buffer 的空间大小,越小的空间被利用越充分
    buffer-size: 1024
    # 每个区分配的 buffer 数量 , 所以 pool 的大小是 buffer-size * buffers-per-region
    #buffers-per-region: 1024 # 这个参数不需要写了
    # 是否分配的直接内存
    direct-buffers: true

eureka:
  instance:
    lease-renewal-interval-in-seconds: 5
    lease-expiration-duration-in-seconds: 10
    instance-id: ${spring.cloud.client.ip-address}:${server.port}
    prefer-ip-address: true
    metadata-map:
      cluster: default
  client:
    service-url:
      defaultZone: http://${value.eureka.hostname}:48141/eureka/

spring:
  application:
    name: holder-saas-takeaway-producer
  datasource:
    url: jdbc:mysql://${spring.datasource.host}:3306/hst_takeaway_db?autoReconnect=true&failOverReadOnly=false&useUnicode=true&characterEncoding=utf8&allowMultiQueries=true&useSSL=false&useJDBCCompliantTimezoneShift=true&useLegacyDatetimeCode=false&serverTimezone=Asia/Shanghai
    password: mysqlHolder
    username: root
    host: ***************
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      minimum-idle: 10
      max-lifetime: 60000
      maximum-pool-size: 500
  redis:
    host: ${value.redis.hostname}
    database: 1
    password: eIx6TynJq
    port: 56380
  zipkin:
    base-url: http://***************:9411/
    enabled: false
  sleuth:
    sampler:
      probability: 1.0
  enabled: false
  rocketmq:
    namesrv-addr: ${value.rocketmq.hostname}:18080
    producer-group-name: takeOrderProducer
    retry-times-when-send-async-failed: 4
    retry-times-when-send-failed: 4
    compress-msg-body-over-how-much: 5120

# feign配置，使用appach client 替代默认urlConnection
feign:
  hystrix:
    enabled: true
  httpclient:
    enabled: true
    connection-timeout: 30000
    max-connections: 5000
    time-to-live: 30
    time-to-live-unit: seconds
    max-connections-per-route: 1000
  okhttp:
    enabled: false
  client:
    config:
      default:
        connectTimeout: 20000
        readTimeout: 20000

# hystrix 配置
hystrix:
  threadpool:
    default:
      coreSize: 50
      allowMaximumSizeToDivergeFromCoreSize: true
      maxQueueSize: -1
      maximumSize: 100
      queueSizeRejectionThreshold: -1
      keepAliveTimeMinutes: 10
      metrics:
        rollingStats: 10
        numBuckets: 50
  command:
    default:
      fallback:
        isolation:
          semaphore:
            maxConcurrentRequests: 100
      circuitBreaker:
        requestVolumeThreshold: 1000
      execution:
        timeout:
          enable: true
        isolation:
          strategy: SEMAPHORE
          semaphore:
            maxConcurrentRequests: 3000
          thread:
            timeoutInMilliseconds: 30000

# ele:
#  IS_SAND_BOX: true
#  REDIRECT_URL: http://holder.f3322.net:1080/gateway/merchant/waimai/ele/bind/callback
#  CLIENT_KEY: CrugsQVT6i
#  SECRET: 456461ad73a4f9491b3845d2269e2f9d4311acd1
ele:
  APP_ID: 157095134
  IS_SAND_BOX: true
  BINDING_URL: https://open-api-sandbox.shop.ele.me/authorize
  REDIRECT_URL: https://mch-sit.holderzone.cn/gateway/merchant/takeout/callback/ele/bind
  CLIENT_KEY: jkLrSxuQPm
  SECRET: 3640fba0fa7bf8a374892902bd81c2c80d82d6df
  TOKEN_REFRESH_AHEAD_HOURS: 12
  REFRESH_TOKEN_VALIDITY_PERIOD_DAYS: 15

mt:
  DEVELOPER_ID: 104795
  SIGN_KEY: 5jydvc7w8ee8fx7w

tiktok:
  groupon:
    appId: awsudbjxs509m86s
    appSecret: 487b3142a403dcdafe9b308541c0e0d9
    host: https://open.douyin.com/goodlife/
    tokenUrl: https://open.douyin.com/oauth/client_token/
    # 查询抖音门店信息
    shopQuery: v1/shop/poi/query/
    # 提交门店绑定
    storeMatchSubmitUrl: v1/poi/match/task/submit/
    # 门店绑定结果查询
    storeMatchQueryUrl: v1/poi/match/task/query/
    # 是否是沙箱模式1开启0关闭
    sandbox: 0
    # 沙箱环境使用的token
    sandboxToken: douyin.iWdOKQDCzdkAXvXL6ojFiVSGrelxAmlD3mIraMMCGyr7Ln2NoxWkH0OfFrRG
  takeaway:
    appKey: 7395783961827477001
    appSecret: 6e442052-b5df-49c5-ac4b-7514b2bf3de7
    serviceId: 12321
    token_refresh_scan_hours: 12
    refresh_token_expires_in_days: 15
    storeAuthorizeUrl: https://fuwu.jinritemai.com/authorize?service_id=%s&state=%s

own:
  APP_ID: 100875
  APP_SECRET: 5uu2gnd8h
  URL: https://miniapp-sit.holderzone.cn/api/
  ITEM_URL: https://miniapp-sit.holderzone.cn/api/
  ITEM_QUERY: goods/ThirdBindGoods/QueryBaseList
  ITEM_BINGING: goods/ThirdBindGoods/goodsBind
  ITEM_UNBINGING: goods/ThirdBindGoods/CancelBind
  BINDING_URL: cloud/StoreWmBind/StoreBind
  UNBINDING_URL: cloud/StoreWmBind/CancelBind
  REFRESH_URL: StoreWmBind/RefreshToken
  ITEM_QUERY_URL: WaimaiDishes/QueryBaseList
  ORDER_UPDATE: order/SalesOrderManage/UpdateStatus
  DISTRIBUTION_QUERY: WaiMaiPlatformInfo/GetDeliveryType
  TOKEN_REFRESH_AHEAD_HOURS: 1
  REFRESH_TOKEN_VALIDITY_PERIOD_DAYS: 10

small:
  ORDER_DELIVERY: https://wechat-sit.holderzone.cn/distribution/api/Order/AddOrder
  ORDER_CANCEL_DELIVERY: https://wechat-sit.holderzone.cn/distribution/api/Order/OrderCancel
  #  ENTERPRISE_FOR_HESHI: 2105121457004450005 食堂
  ENTERPRISE_FOR_HESHI: 2103221820543700008


zc:
  URL: https://zhuancan-sit.holderzone.cn
  SHOP_BINDING_URL: /merchant/api/holder-bind-merchant
  SHOP_DELIVERY_SYNC: /merchant/api/sync-holder-merchant-info
  TYPE_QUERY_URL: /shop/api/holder-query-dishes-group
  ITEM_QUERY_URL: /shop/api/holder-query-dishes
  ITEM_BINDING_URL: /shop/api/holder-bind-dishes
  ORDER_ACCEPT_OR_REFUSE_URL: /shop/api/holder-pending-order-resolve
  ORDER_AGREE_OR_DISAGREE_URL: /shop/api/holder-cooking-order-user-cancel-resolve
  ORDER_DELIVERY_ACCEPT_URL: /shop/api/holder-cooking-order-ready
  ORDER_DELIVERY_FINISH_URL: /shop/api/holder-ready-order-delivery
  ORDER_DINING_OUT_URL: /shop/api/syn-order-by-write-off-code
  ORDER_CONFIRM_THE_MEAL_URL: /shop/api/syn-order-by-write-off-code
  ORDER_PICK_UP_URL: /shop/api/syn-order-by-write-off-code
  ORDER_KNIGHT_ACCEPT: /shop/api/holder-ready-order-delivery-detail
  SYNC_ITEM_URL: /shop/api/update-store-dish-name
  ORDER_UPDATE_PERSON_URL: /shop/api/holder-ready-order-update-person
logging:
  level:
    com.holder.saas.store.takeaway.producers: info

mybatis-plus:
  mapper-locations: classpath*:/mapper/**Mapper.xml
  typeAliasesPackage: com.holder.saas.store.takeaway.producers.entity.domain
  configuration:
    map-underscore-to-camel-case: true
    aggressive-lazy-loading: true
    auto-mapping-behavior: partial
    auto-mapping-unknown-column-behavior: warning
    cache-enabled: false
    call-setters-on-nulls: false
  global-config:
    refresh: false
    db-config:
      db-type: mysql
      id-type: auto
      field-strategy: not_empty
      logic-delete-value: 1
      logic-not-delete-value: 0

dx:
  DIAN_XIN_TOKEN_URL: http://**************:3140/openapi/getToken?
  DIAN_XIN_CALL_URL: http://**************:3140/openapi/v1/trigger_phone_import
  APPID: jHed9BtiEoENxJi7&
  SECRET: A80247ED9AB8DAF35E7CE4059ED88A41EC239814F8867EA3E9CD84A4A91C9243
  TASK_ID: null
  PROJECT_ID: HSSK
# 团购验券配置类
group:
  dou-yin:
    appId: awsudbjxs509m86s
    appSecret: 487b3142a403dcdafe9b308541c0e0d9
    host: https://open.douyin.com/goodlife/v1/
    tokenUrl: https://open.douyin.com/oauth/client_token/
    # 查询抖音门店信息
    shopQuery: shop/poi/query/
    # 提交门店绑定
    storeMatchSubmitUrl: poi/match/task/submit/
    # 门店绑定结果查询
    storeMatchQueryUrl: poi/match/task/query/
    # 查询券列表
    certificatePrepare: fulfilment/certificate/prepare/
    # 验券
    certificateVerify: fulfilment/certificate/verify/
    # 撤销验券
    certificateCancel: fulfilment/certificate/cancel/
    # 是否是沙箱模式1开启0关闭
    sandbox: 0
    # 沙箱环境使用的token
    sandboxToken: douyin.iWdOKQDCzdkAXvXL6ojFiVSGrelxAmlD3mIraMMCGyr7Ln2NoxWkH0OfFrRG
  alipay:
    privateKey: MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCUzvsNaYrGrEQxjvJM7m3iIfyQoDcuivBbtSPbmAW0/scya6vOy95P7empqJZZkF9x4SFOkdr6Ckv7l5WCR6t6I2i7MmfXhkILXSHojuOjz6jOnQ1qKT65/O5YWQ5NLi7kUWQTcMJDKKiuYAlBC/mpXCJBU/mgfDXgvmcDRUjk0xppwrYuXxoRD37S9ZLWnYl3yqOe7aw4Ioop4eMRggu9LsKCBIbsNuXjDO6OkDuq3swEAU/EGlCw4jGjv62FY8F8a3GS+gl4qrap7L5DDce6vnTY1TO87wwLFl+8VNNqmWmA++k16N/Lveagu8a7NFL5Lr7kc1pRy886+1fhYIZhAgMBAAECggEAZOoo3uOgNTNF28XxE7Lt2djqirncMjgO4GJrOtRRqnjNZagXD1q6HMJfH6swqvR5haUDDWFkewTYmnol7f/kjiPNNoXsXgCycTprcGbWgZSmCdf/7Oqjm0Wn7UoXpMeZDUUU9QoW86xzGC4QmIgie3P71wm309noP81f1pv53xMxpWDLfv8R4gew6xCf3bxH3NEjH5pXGlHwT1vuk0pv71ELJQ7mGO3PH7pgTWjLT5TABdEBd9O3BjZa+DllCDrFL+u9FNf6lA1b4JzZkbWkKTc2i4Ad+8dtRSW3HpFu102gP/Jo8SC56+ehaxOGpsFdmyQoPJLAJY5yWGTgibWtAQKBgQDGvO44FYlGXpe3IfssNrcS672EjdpYov2S+RwQWw6KgvSWednBhn7TsS1e84dFm/G97xrGsFr6C5wi6/ZWwm+gFSnuxh4nnFaeG68r//Ol01XjI2VMK+QyMh7t9myK5ri10sjxUS0TpBPu1tzxwHzKidxWvBOMpu+lb6Ewe038qQKBgQC/rzZQ/EYNA9BaTbBbEV9edrXSf+/IanHhu4rrzthPbWq/6j/D/i1A3Y95EtCZwcl1y0IOLP4hNEbD0oLUHTQPGAnLPECYLYXLSlY2OYWateAmO8T15fAAyD7Gg27gTMkuLSKsfEhZLjixzL66Ge+z9HYTpFYlN/GFqZRk7J1W+QKBgQCZ+p3zlDQU8otH16s82XPE1CO1RRbsX/Rp4FpvDa3ZgZcx01z1BVjUec02aSbgtu95An3TfaYvX7lskTf+ho1oEZ24o4WPmSC/RaHWTGwhiflcj/sXJlaa0ZyHMMtuX06ziYoo9oUfV5weDBmJsWK+pkuyY+wdqOW6XyAbaR3GaQKBgE7P+aM+toavZMFcZABtmiq64HNWSv66VWycsbfW6jVuJZAW/nTVU0HyVwVO6RnvBag7FEPD2BFK7zWgnk4cW8VA+vXnJh/rx7EYNW5CJF7CHRFbTdZU3mNT8gupCrOKnsUvpawJxa11RbZFGr5l35q8Drhwv0K6R8HPQcZlSyPxAoGAWRqqYGYl0kx2YARfIKeKG8tqwEAp/NYzHpyNn5Vk8PajMshCDxb7tZ1a+4o7Fftr0USJGbK5eGxXynQc8ni3hL7hLebcYaM/dREmMb6H8TByv0oHfGz9BjEl5m7AQeuSRYFuEayAgQFr9Q/3FWimmQCEXCCQX/Z7+YegaMzzhds=
    alipayPublicKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAhTIRtHd73U4kcNVrbiLtN8/BWfNMJFHyHg0gf7k4BUW9RgfZ2rslu48BhwLWpNLMtxwKgUErteEHLZE3DNv0fhvj3d2I/ZSpY26rKb5qnAOBe4j9cQG+fU8gS01A4B8wwkzzStpZhY6Hf6gZD/IyixZ4nC2u4ZI7p9KRDa92bkXpnKDGBpddFW+hYQVrR4q+5RiiLU/IBRvQumCwlfBE0N4FVRYwp5ggMY8nQNZfAv6vK2wOBc+9gZYaxXy2eXqhZmw13p/aQYJEGN1Hgem0OXCON+LC3e93SrevAczGsHccxFpi+IRK+D3AQbUILMwnXCpIcm12pkS07sG9SABTtwIDAQAB
    appId: 2021004124678357
  abc:
    appId: ZKZ2023120658769
    appSecret: v1N+zS22r9FGaJvhZuQNlg==
    host: https://plat-api.1kahui.cn/api/v1/third/
    # 查询门店信息
    shopQuery: store/id
    # 查询券列表
    couponQuery: coupon/by/code
    # 验券
    couponVerify: coupon/writeOff
    # 撤销验券
    couponCancel: coupon/revoke