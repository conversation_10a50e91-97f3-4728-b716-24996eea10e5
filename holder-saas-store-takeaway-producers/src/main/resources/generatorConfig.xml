<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<generatorConfiguration>
	<!-- 如果是在eclipse 中，选择pom.xml文件，击右键先择Run AS——>Maven Build… ——>在Goals框中输入：mybatis-generator:generate
		如果在命令行输入Maven命令即可，注意：一定是当前项目目录下运行该命令： mvn mybatis-generator:generate -->

	<!-- 你的本地数据库连接jar绝对路径 -->
	<!--<classPathEntry-->
		<!--location="/Users/<USER>/.m2/repository/mysql/mysql-connector-java-5.1.6.jar" />-->


	<!--<context id="Mysql" targetRuntime="MyBatis3Simple"-->
		<!--defaultModelType="flat">-->

		<!--<commentGenerator>-->
			<!--<property name="suppressDate" value="false" />-->
			<!--<property name="suppressAllComments" value="true" />-->
		<!--</commentGenerator>-->

		<!--<jdbcConnection driverClass="com.mysql.jdbc.Driver"-->
			<!--connectionURL="******************************************************************"-->
			<!--userId="root" password="root" />-->
		<!--&lt;!&ndash; 你本地的绝对路径 &ndash;&gt;-->
		<!--<javaModelGenerator targetPackage="cn.zkz.takeaway.domain.model"-->
			<!--targetProject="/Users/<USER>/Documents/IdeaProjects/takeaway/takeaway-domain/src/main/java">-->
		<!--</javaModelGenerator>-->
		<!--&lt;!&ndash; 你本地的绝对路径 &ndash;&gt;-->
		<!--<sqlMapGenerator targetPackage="cn.zkz.takeaway.dao.mapperxml"-->
			<!--targetProject="/Users/<USER>/Documents/IdeaProjects/takeaway/takeaway-dao/src/main/java">-->
		<!--</sqlMapGenerator>-->
		<!--&lt;!&ndash; 你本地的绝对路径 &ndash;&gt;-->
		<!--<javaClientGenerator targetPackage="cn.zkz.takeaway.dao.mapper"-->
			<!--targetProject="/Users/<USER>/Documents/IdeaProjects/takeaway/takeaway-dao/src/main/java" type="XMLMAPPER">-->
		<!--</javaClientGenerator>-->

		<!--&lt;!&ndash; 根据表明生成, 全部用 % &ndash;&gt;-->
		<!--<table tableName="platform_epoi_auth">-->
			<!--<generatedKey column="id" sqlStatement="Mysql" identity="true" />-->
		<!--</table>-->

	<!--</context>-->


    <properties resource="application.properties"/>

    <context id="Mysql" targetRuntime="MyBatis3Simple" defaultModelType="flat">
        <property name="beginningDelimiter" value="`"/>
        <property name="endingDelimiter" value="`"/>

        <plugin type="tk.mybatis.mapper.generator.MapperPlugin">
            <property name="mappers" value="com.holder.saas.store.takeaway.mapper"/>
        </plugin>

        <jdbcConnection driverClass="${druid.driver-class-name}"
                        connectionURL="${druid.url}"
                        userId="${druid.username}"
                        password="${druid.password}">
        </jdbcConnection>

        <!--&lt;!&ndash; 你本地的绝对路径 &ndash;&gt;-->
        <javaModelGenerator targetPackage="com.holder.saas.store.takeaway.domain.model"
        targetProject="/Project/takeaway/takeaway-domain/src/main/java">
        </javaModelGenerator>
        <!--&lt;!&ndash; 你本地的绝对路径 &ndash;&gt;-->
        <sqlMapGenerator targetPackage="cn.zkz.takeaway.dao.mapperxml"
        targetProject="/Project/takeaway/takeaway-dao/src/main/java">
        </sqlMapGenerator>
        <!--&lt;!&ndash; 你本地的绝对路径 &ndash;&gt;-->
        <javaClientGenerator targetPackage="com.holder.saas.store.takeaway.mapper"
        targetProject="/Project/takeaway/takeaway-dao/src/main/java" type="XMLMAPPER">
        </javaClientGenerator>
        <table tableName="mt_secret"  domainObjectName="MtSecret" />
        <!--<table tableName="platform_epoi_auth">
            &lt;!&ndash;mysql 配置&ndash;&gt;
            <generatedKey column="id" sqlStatement="Mysql" identity="true"/>
            &lt;!&ndash;oracle 配置&ndash;&gt;
            &lt;!&ndash;<generatedKey column="id" sqlStatement="select SEQ_{1}.nextval from dual" identity="false" type="pre"/>&ndash;&gt;
        </table>-->
    </context>
</generatorConfiguration>