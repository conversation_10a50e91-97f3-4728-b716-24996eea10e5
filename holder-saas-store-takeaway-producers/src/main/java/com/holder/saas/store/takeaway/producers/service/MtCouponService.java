package com.holder.saas.store.takeaway.producers.service;

import com.holderzone.saas.store.dto.takeaway.request.CouponDelReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.MtCouponReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.*;

public interface MtCouponService {

    /**
     * 已验券码查询
     */
    MtCouponDetailRespDTO queryById(MtCouponReqDTO mtCouponReqDTO);

    /**
     * 执行验券
     */
    MtCouponDoCheckRespDTO checkTicket(MtCouponReqDTO mtCouponReqDTO);

    /**
     * 预验券
     */
    MtCouponPreRespDTO preCheck(MtCouponReqDTO mtCouponReqDTO);

    /**
     * 执行验券
     */
    MtCouponDoCheckRespDTO doCheck(MtCouponReqDTO mtCouponReqDTO);

    /**
     * 撤销验券
     */
    MtDelCouponRespDTO cancelTicket(CouponDelReqDTO couponDelReqDTO);

    /**
     * 查询团购订单结算明细
     */
    MtCouponTradeDetailRespDTO queryGroupTradeDetail(MtCouponReqDTO mtCouponReqDTO);
}
