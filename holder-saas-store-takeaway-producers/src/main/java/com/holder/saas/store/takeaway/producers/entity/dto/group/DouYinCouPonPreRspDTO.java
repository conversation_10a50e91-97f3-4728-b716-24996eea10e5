package com.holder.saas.store.takeaway.producers.entity.dto.group;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.exception.GroupBuyException;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-06-20
 * @description
 */
@Data
public class DouYinCouPonPreRspDTO extends DouYinCommonDTO{

    private List<DouYinCertificate> certificates;

    public static List<DouYinCertificate> parseJsonAndCheck(String rsp) {
        DouYinRspDTO<DouYinCouPonPreRspDTO> douYinCouPonPreRsp = JSONObject.parseObject(rsp,new TypeReference<DouYinRspDTO<DouYinCouPonPreRspDTO>>(){}.getType());
        if (douYinCouPonPreRsp == null || douYinCouPonPreRsp.getData() == null){
            throw new BusinessException("抖音预验券返回为空");
        }
        if(douYinCouPonPreRsp.getData().getErrorCode() != 0){
            throw new BusinessException(douYinCouPonPreRsp.getData().getDescription());
        }
        DouYinCouPonPreRspDTO douYinCouPonPre = douYinCouPonPreRsp.getData();
        return douYinCouPonPre.getCertificates();
    }
}
