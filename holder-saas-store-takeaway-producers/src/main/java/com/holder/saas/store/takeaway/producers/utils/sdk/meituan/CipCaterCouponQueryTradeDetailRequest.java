package com.holder.saas.store.takeaway.producers.utils.sdk.meituan;


import com.google.common.collect.Maps;
import com.sankuai.sjst.platform.developer.domain.RequestDomain;
import com.sankuai.sjst.platform.developer.domain.RequestMethod;
import com.sankuai.sjst.platform.developer.request.CipCaterStringPairRequest;

import java.util.Map;


/**
 * 查询团购订单结算明细
 */
public class CipCaterCouponQueryTradeDetailRequest extends CipCaterStringPairRequest {

    private String couponCode;

    public CipCaterCouponQueryTradeDetailRequest() {
        this.url = RequestDomain.preUrl.getValue() + "/tuangou/coupon/queryTradeDetail";
        this.requestMethod = RequestMethod.POST;
    }

    @Override
    public Map<String, String> getParams() {
        Map<String, String> params = Maps.newHashMap();
        params.put("couponCode", CipCaterCouponQueryTradeDetailRequest.this.couponCode);
        return params;
    }

    @Override
    public boolean paramsAbsent() {
        return this.couponCode == null || this.couponCode.trim().isEmpty();
    }

    public String getCouponCode() {
        return this.couponCode;
    }

    public void setCouponCode(String couponCode) {
        this.couponCode = couponCode;
    }
}
