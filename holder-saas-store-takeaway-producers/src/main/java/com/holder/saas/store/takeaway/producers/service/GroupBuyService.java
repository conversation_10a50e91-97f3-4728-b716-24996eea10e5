package com.holder.saas.store.takeaway.producers.service;

import com.holderzone.saas.store.dto.business.group.StoreBindDTO;
import com.holderzone.saas.store.dto.order.response.groupon.GroupVerifyDTO;
import com.holderzone.saas.store.dto.takeaway.request.CouPonPreReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.CouPonReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtCouponPreRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtDelCouponRespDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-06-16
 * @description 团购服务
 */
public interface GroupBuyService {

    String GROUP_BUY_TOKEN = "GROUP_BUY_TOKEN:";
    void bindStore(StoreBindDTO storeBind);

    String getToken();

    List<MtCouponPreRespDTO> couponPrepare(CouPonPreReqDTO couPonPreReqDTO);

    List<GroupVerifyDTO> verifyCoupon(CouPonReqDTO couPonReq);

    MtDelCouponRespDTO revokeCoupon(GroupVerifyDTO revokeReq);
}
