package com.holder.saas.store.takeaway.producers.mapstruct;

import com.holder.saas.store.takeaway.producers.entity.domain.MtPrivacyDO;
import com.holder.saas.store.takeaway.producers.entity.dto.MtCbPrivacyDTO;
import org.mapstruct.Mapper;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Mapper(componentModel = "spring")
public interface MtPrivacyMapstruct {

    MtPrivacyDO fromMtCbPrivacyDetail(MtCbPrivacyDTO.MtCbPrivacyDetail mtCbPrivacyDetail);

    List<MtPrivacyDO> fromMtCbPrivacyDetail(List<MtCbPrivacyDTO.MtCbPrivacyDetail> mtCbPrivacyDetails);
}
