package com.holder.saas.store.takeaway.producers.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holder.saas.store.takeaway.producers.entity.domain.JdStoreMappingDO;
import com.holder.saas.store.takeaway.producers.mapper.JdStoreMappingMapper;
import com.holder.saas.store.takeaway.producers.service.JdStoreMappingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class JdStoreMappingServiceImpl extends ServiceImpl<JdStoreMappingMapper, JdStoreMappingDO> implements JdStoreMappingService {


    @Override
    public JdStoreMappingDO getByVenderStoreAndToken(String storeId) {
        return getOne(new LambdaQueryWrapper<JdStoreMappingDO>()
                .eq(JdStoreMappingDO::getVenderStoreId, storeId)
                .last(" limit 1"));
    }

    @Override
    public JdStoreMappingDO getByStoreGuid(String storeGuid) {
        return getOne(new LambdaQueryWrapper<JdStoreMappingDO>()
                .eq(JdStoreMappingDO::getStoreGuid, storeGuid)
                .last(" limit 1"));
    }

    @Override
    public List<JdStoreMappingDO> listByStoreGuids(List<String> storeGuids) {
        return list(new LambdaQueryWrapper<JdStoreMappingDO>()
                .in(JdStoreMappingDO::getStoreGuid, storeGuids));
    }
}
