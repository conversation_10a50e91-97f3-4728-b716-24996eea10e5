package com.holder.saas.store.takeaway.producers.config;


import com.alibaba.fastjson.JSON;
import com.holderzone.framework.exception.unchecked.BusinessException;
import eleme.openapi.sdk.api.exception.ServiceException;
import org.apache.commons.lang3.ArrayUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * todo 删除该aspect
 * <AUTHOR> controller请求日志
 */
//@Aspect
//@Component
public class AopAspect {

    private static final Logger LOGGER = LoggerFactory.getLogger(AopAspect.class);

    public AopAspect() {
    }

    @Pointcut("execution(* com.holder.saas.store.takeaway.producers.controller.*.*(..))")
    private void controllerMethod() {
    }

    @Around("controllerMethod()")
    public Object invoke(ProceedingJoinPoint joinPoint) throws Throwable {
        // 请求参数
        Object[] args = joinPoint.getArgs();
        String params = "";
        if (ArrayUtils.isNotEmpty(args)) {
            StringBuilder stringBuilder = new StringBuilder();
            for (Object object : args) {
                //HttpServletRequest和HttpServletResponse作为controller参数
                if (object instanceof HttpServletRequest) {
                    HttpServletRequest request = (HttpServletRequest) object;
                    stringBuilder.append(JSON.toJSONString(request.getParameterMap())).append(",");
                    continue;
                }
                if (object instanceof HttpServletResponse) {
                    continue;
                }
                stringBuilder.append(JSON.toJSONString(object)).append(",");
            }
            params = stringBuilder.toString();
        }
        // 控制器名
        String controllerName = joinPoint.getTarget().getClass().getName();
        // 方法名
        String methodName = joinPoint.getSignature().getName();
        try {
            LOGGER.info("controller:{} # method:{} # 入参:{}", controllerName, methodName, params);
            return joinPoint.proceed();
        } catch (BusinessException e) {
            LOGGER.error("调用 controller: [" + controllerName + "], method: [" + methodName + "] 出错", e);
            return null;
        } catch (ServiceException e) {
            LOGGER.error("调用 controller: [" + controllerName + "], method: [" + methodName + "] 出错", e);
            return null;
        } catch (Exception e) {
            LOGGER.error("调用 controller: [" + controllerName + "], method: [" + methodName + "] 出错", e);
            return null;
        }
    }
}
