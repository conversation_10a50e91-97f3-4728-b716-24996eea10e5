package com.holder.saas.store.takeaway.producers.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.AlipayConfig;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.domain.*;
import com.alipay.api.request.AlipayMarketingCertificateCertificationPrepareuseRequest;
import com.alipay.api.request.AlipayMarketingCertificateCertificationRefundRequest;
import com.alipay.api.request.AlipayMarketingCertificateCertificationUseRequest;
import com.alipay.api.request.AntMerchantExpandShopQueryRequest;
import com.alipay.api.response.AlipayMarketingCertificateCertificationPrepareuseResponse;
import com.alipay.api.response.AlipayMarketingCertificateCertificationRefundResponse;
import com.alipay.api.response.AlipayMarketingCertificateCertificationUseResponse;
import com.alipay.api.response.AntMerchantExpandShopQueryResponse;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holder.saas.store.takeaway.producers.entity.domain.GroupStoreBindDO;
import com.holder.saas.store.takeaway.producers.entity.enums.AlipayResultEnum;
import com.holder.saas.store.takeaway.producers.service.AlipayAuthService;
import com.holder.saas.store.takeaway.producers.service.DistributedService;
import com.holder.saas.store.takeaway.producers.service.GroupBuyService;
import com.holder.saas.store.takeaway.producers.service.GroupStoreBindService;
import com.holder.saas.store.takeaway.producers.service.converter.GroupBuyConverter;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.constant.Constant;
import com.holderzone.saas.store.dto.business.group.StoreBindDTO;
import com.holderzone.saas.store.dto.exception.GroupBuyException;
import com.holderzone.saas.store.dto.order.response.groupon.GroupVerifyDTO;
import com.holderzone.saas.store.dto.takeaway.request.AlipayAuthQO;
import com.holderzone.saas.store.dto.takeaway.request.CouPonPreReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.CouPonReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.AlipayAuthRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtCouponPreRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtDelCouponRespDTO;
import com.holderzone.saas.store.enums.GroupBuyTypeEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023/11/13
 * @description 支付宝团购服务
 */
@Slf4j
@AllArgsConstructor
@Service("aliPayGroupBuyServiceImpl")
public class AliPayGroupBuyServiceImpl implements GroupBuyService {

    private static final int ALIPAY_CODE_LENGTH = 12;

    private static final String APP_AUTH_TOKEN = "app_auth_token";

    private final GroupStoreBindService groupStoreBindService;

    private final AlipayAuthService alipayAuthService;

    private final StringRedisTemplate stringRedisTemplate;

    private final DistributedService distributedService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void bindStore(StoreBindDTO storeBind) {
        storeBind.verify();
        // 查询门店是否绑定
        int count = groupStoreBindService.count(new LambdaQueryWrapper<GroupStoreBindDO>()
                .eq(GroupStoreBindDO::getStoreGuid, storeBind.getStoreGuid())
                .eq(GroupStoreBindDO::getType, storeBind.getGroupBuyType()));
        if (count > 0) {
            throw new GroupBuyException("此门店已绑定支付宝团购");
        }

        // 查询支付宝门店信息
        AntMerchantExpandShopQueryResponse response = getAliPayShopResponse(storeBind);

        // 清除绑定此支付宝门店的绑定信息
        groupStoreBindService.remove(new LambdaQueryWrapper<GroupStoreBindDO>()
                .eq(GroupStoreBindDO::getPoiId, storeBind.getPoiId())
                .eq(GroupStoreBindDO::getType, storeBind.getGroupBuyType()));

        // 保存绑定关系
        groupStoreBindService.save(GroupBuyConverter.fromAliPayStoreBind(response, storeBind));
    }

    /**
     * 查询支付宝门店信息
     */
    private AntMerchantExpandShopQueryResponse getAliPayShopResponse(StoreBindDTO storeBind) {
        AlipayAuthRespDTO authDTO = getAlipayAuthDTO(storeBind.getStoreGuid());
        AlipayConfig alipayConfig = new AlipayConfig();
        alipayConfig.setAppId(authDTO.getAppId());
        alipayConfig.setPrivateKey(authDTO.getApplyPrivateKey());
        alipayConfig.setAlipayPublicKey(authDTO.getAliPublicKey());
        alipayConfig.setEncryptKey(authDTO.getAes());
        AntMerchantExpandShopQueryResponse response;
        try {
            AlipayClient alipayClient = new DefaultAlipayClient(alipayConfig);
            AntMerchantExpandShopQueryRequest request = new AntMerchantExpandShopQueryRequest();
            AntMerchantExpandShopQueryModel model = new AntMerchantExpandShopQueryModel();
            model.setShopId(storeBind.getPoiId());
            request.setBizModel(model);
            response = alipayClient.execute(request);
        } catch (AlipayApiException apiException) {
            log.error("[支付宝店铺查询]异常,apiException=", apiException);
            throw new BusinessException(apiException.getMessage());
        }
        if (!response.isSuccess()) {
            throw new BusinessException(response.getSubMsg());
        }
        return response;
    }

    @Override
    public String getToken() {
        AlipayAuthRespDTO authDTO = getAlipayAuthDTO(UserContextUtils.getStoreGuid());
        return authDTO.getAppAuthToken();
    }

    public String getAppId() {
        AlipayAuthRespDTO authDTO = getAlipayAuthDTO(UserContextUtils.getStoreGuid());
        return authDTO.getAppId();
    }

    private AlipayAuthRespDTO getAlipayAuthDTO(String storeGuid) {
        // 先从redis取 如果不存在则查库
        String cacheJson = stringRedisTemplate.opsForValue().get(GROUP_BUY_TOKEN + GroupBuyTypeEnum.ALIPAY);
        if (StringUtils.isNotEmpty(cacheJson)) {
            return JacksonUtils.toObject(AlipayAuthRespDTO.class, cacheJson);
        }

        AlipayAuthQO authQO = new AlipayAuthQO();
        authQO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        authQO.setStoreGuid(storeGuid);
        AlipayAuthRespDTO authDTO = alipayAuthService.queryAuthInfo(authQO);
        if (ObjectUtils.isEmpty(authDTO)) {
            throw new BusinessException("请授权支付宝小程序");
        }

        stringRedisTemplate.opsForValue().set(GROUP_BUY_TOKEN + GroupBuyTypeEnum.ALIPAY, JacksonUtils.writeValueAsString(authDTO), 6, TimeUnit.HOURS);
        return authDTO;
    }

    /**
     * 支付宝预验券
     */
    @Override
    public List<MtCouponPreRespDTO> couponPrepare(CouPonPreReqDTO couPonPreReqDTO) {
        if (StringUtils.isEmpty(couPonPreReqDTO.getCouponCode()) && StringUtils.isEmpty(couPonPreReqDTO.getEncryptedData())) {
            throw new BusinessException("券码入参为空");
        }
        // 查询门店是否绑定
        int count = groupStoreBindService.count(new LambdaQueryWrapper<GroupStoreBindDO>()
                .eq(GroupStoreBindDO::getStoreGuid, couPonPreReqDTO.getStoreGuid())
                .eq(GroupStoreBindDO::getType, couPonPreReqDTO.getGroupBuyType()));
        if (count == 0) {
            throw new BusinessException(Constant.COUPON_UN_BIND_STORE);
        }

        // 凭证核销准备
        AlipayMarketingCertificateCertificationPrepareuseResponse prepareUseResponse = prepareUse(couPonPreReqDTO);
        log.info("[凭证核销准备]返回,prepareUseResponse={}", JacksonUtils.writeValueAsString(prepareUseResponse));
        return GroupBuyConverter.toAliPayCouponPreRespList(prepareUseResponse);
    }

    /**
     * alipay.marketing.certificate.certification.prepareuse(凭证核销准备)
     */
    private AlipayMarketingCertificateCertificationPrepareuseResponse prepareUse(CouPonPreReqDTO couPonPreReqDTO) {
        AlipayAuthRespDTO authDTO = getAlipayAuthDTO(UserContextUtils.getStoreGuid());
        AlipayConfig alipayConfig = new AlipayConfig();
        alipayConfig.setPrivateKey(authDTO.getApplyPrivateKey());
        alipayConfig.setAlipayPublicKey(authDTO.getAliPublicKey());
        alipayConfig.setAppId(authDTO.getAppId());

        // 构造请求参数以调用接口
        AlipayMarketingCertificateCertificationPrepareuseRequest request = new AlipayMarketingCertificateCertificationPrepareuseRequest();
        AlipayMarketingCertificateCertificationPrepareuseModel model = new AlipayMarketingCertificateCertificationPrepareuseModel();

        // 设置加密数据 参数有误code长度需要在0和12之间
        if (couPonPreReqDTO.getCouponCode().length() > ALIPAY_CODE_LENGTH) {
            model.setEncryptedData(couPonPreReqDTO.getCouponCode());
        } else {
            model.setCode(couPonPreReqDTO.getCouponCode());
        }
        if (!ObjectUtils.isEmpty(couPonPreReqDTO.getCrossOrder())) {
            model.setCrossOrder(couPonPreReqDTO.getCrossOrder());
        }
        request.setBizModel(model);

        AlipayMarketingCertificateCertificationPrepareuseResponse response;
        try {
            AlipayClient alipayClient = new DefaultAlipayClient(alipayConfig);
            response = alipayClient.execute(request);
        } catch (AlipayApiException apiException) {
            log.error("[凭证核销准备]异常,apiException=", apiException);
            throw new BusinessException(apiException.getMessage());
        }
        if (!response.isSuccess()) {
            throw new BusinessException(response.getSubMsg());
        }
        return response;
    }

    /**
     * 支付宝核销券码
     */
    @Override
    public List<GroupVerifyDTO> verifyCoupon(CouPonReqDTO couPonReq) {
        if (CollUtil.isEmpty(couPonReq.getCouponCodeList())) {
            throw new BusinessException("券码为空");
        }
        if (StringUtils.isEmpty(couPonReq.getErpId())) {
            throw new BusinessException("验券门店不能为空");
        }

        // 校验门店是否绑定
        GroupStoreBindDO bindStore = groupStoreBindService.getOne(new LambdaQueryWrapper<GroupStoreBindDO>()
                .eq(GroupStoreBindDO::getStoreGuid, couPonReq.getErpId())
                .eq(GroupStoreBindDO::getType, couPonReq.getGroupBuyType()));
        if (bindStore == null) {
            throw new BusinessException(Constant.COUPON_UN_BIND_STORE);
        }

        couPonReq.setShopId(bindStore.getPoiId());
        AlipayMarketingCertificateCertificationUseResponse response = verifyCouponByAlipay(couPonReq);
        log.info("[支付宝核销券码]返回,response={}", JacksonUtils.writeValueAsString(response));
        List<CertificateUseResult> useResultList = response.getCertificateUseResultList();
        if (CollectionUtils.isEmpty(useResultList)) {
            throw new BusinessException("支付宝核销券码失败");
        }
        // 过滤失败的
        String msg = useResultList.get(0).getMsg();
        useResultList.removeIf(useResult -> !AlipayResultEnum.SUCCESS.name().equals(useResult.getResult()));
        if (CollectionUtils.isEmpty(useResultList)) {
            throw new BusinessException(msg);
        }
        /*List<String> certificateIdList = useResultList.stream()
                .map(CertificateUseResult::getCertificateId)
                .collect(Collectors.toList());
        AlipayMarketingCertificateCertificationBatchqueryResponse queryResponse = batchQueryCoupon(couPonReq.getOrderId(),
                couPonReq.getUserId(), certificateIdList);
        log.info("[查询凭证信息]返回,response={}", JacksonUtils.writeValueAsString(queryResponse));*/

        return GroupBuyConverter.toAliPayCouponVerifyRespList(useResultList, couPonReq.getUserId());
    }

    /**
     * alipay.marketing.certificate.certification.batchquery(查询凭证信息)
     */
    /*private AlipayMarketingCertificateCertificationBatchqueryResponse batchQueryCoupon(String orderId, String userId, List<String> certificateIdList) {
        AlipayMarketingCertificateCertificationBatchqueryRequest request = new AlipayMarketingCertificateCertificationBatchqueryRequest();
        AlipayMarketingCertificateCertificationBatchqueryModel model = new AlipayMarketingCertificateCertificationBatchqueryModel();
        model.setOrderId(orderId);
        model.setUserId(userId);
        model.setCertificateIdList(certificateIdList);
        request.setBizModel(model);

        AlipayAuthRespDTO authDTO = getAlipayAuthDTO(UserContextUtils.getStoreGuid());
        AlipayConfig alipayConfig = new AlipayConfig();
        alipayConfig.setAppId(authDTO.getAppId());
        alipayConfig.setPrivateKey(authDTO.getApplyPrivateKey());
        alipayConfig.setAlipayPublicKey(authDTO.getAliPublicKey());
        AlipayMarketingCertificateCertificationBatchqueryResponse response;
        try {
            AlipayClient alipayClient = new DefaultAlipayClient(alipayConfig);
            response = alipayClient.execute(request);
        } catch (AlipayApiException apiException) {
            log.error("[查询凭证信息]异常,apiException=", apiException);
            throw new BusinessException(apiException.getMessage());
        }
        if (!response.isSuccess()) {
            throw new BusinessException(response.getSubMsg());
        }
        return response;
    }*/

    /**
     * alipay.marketing.certificate.certification.use(同步凭证核销状态)
     */
    private AlipayMarketingCertificateCertificationUseResponse verifyCouponByAlipay(CouPonReqDTO couPonReq) {
        // 构造请求参数以调用接口
        AlipayMarketingCertificateCertificationUseRequest request = new AlipayMarketingCertificateCertificationUseRequest();
        AlipayMarketingCertificateCertificationUseModel model = new AlipayMarketingCertificateCertificationUseModel();

        // 设置核销时间
//        Date date = new Date();
//        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//        String dateStr = format.format(date);
//        Date bizDt;
//        try {
//            bizDt = format.parse(dateStr);
//        } catch (ParseException e) {
//            log.error("[同步凭证核销状态]时间解析错误", e);
//            throw new BusinessException(e.getMessage());
//        }
        model.setBizDt(new Date());

        // 设置凭证核销详情,都取加密code
        List<CertificateUseInfo> certificateUseInfoList = new ArrayList<>();
        couPonReq.getCouponCodeList().forEach(couponCode -> {
            CertificateUseInfo certificateUseInfo = new CertificateUseInfo();
            certificateUseInfo.setEncryptedCode(couponCode);
            certificateUseInfoList.add(certificateUseInfo);
        });
        model.setCertificateUseInfoList(certificateUseInfoList);

        // 设置购买商品的订单id
        model.setOrderId(couPonReq.getOrderId());

        // 设置外部业务单号
        model.setOutBizNo(distributedService.nextId("OutBizNo"));
        model.setOutOrderId(couPonReq.getErpOrderId());

        // 设置核销门店id
        model.setShopId(couPonReq.getShopId());

        // 设置凭证归属支付宝用户id
        model.setUserId(couPonReq.getUserId());

        request.setBizModel(model);

        AlipayAuthRespDTO authDTO = getAlipayAuthDTO(UserContextUtils.getStoreGuid());
        AlipayConfig alipayConfig = new AlipayConfig();
        alipayConfig.setPrivateKey(authDTO.getApplyPrivateKey());
        alipayConfig.setAlipayPublicKey(authDTO.getAliPublicKey());
        alipayConfig.setAppId(authDTO.getAppId());

        AlipayMarketingCertificateCertificationUseResponse response;
        try {
            AlipayClient alipayClient = new DefaultAlipayClient(alipayConfig);
            response = alipayClient.execute(request);
        } catch (AlipayApiException apiException) {
            log.error("[支付宝凭证核销]异常,apiException=", apiException);
            throw new BusinessException(apiException.getMessage());
        }
        if (!response.isSuccess()) {
            throw new BusinessException(response.getSubMsg());
        }
        return response;
    }

    /**
     * 支付宝撤销验券
     */
    @Override
    public MtDelCouponRespDTO revokeCoupon(GroupVerifyDTO revokeReq) {
        log.info("[支付宝撤销验券]入参,revokeReq:{}", JacksonUtils.writeValueAsString(revokeReq));
        if (StringUtils.isEmpty(revokeReq.getUserId()) || StringUtils.isEmpty(revokeReq.getVerifyId())) {
            throw new BusinessException("撤销验券信息有误");
        }

        AlipayMarketingCertificateCertificationRefundResponse response = refundAlipayCoupon(revokeReq);
        log.info("[支付宝撤销验券]返回,response={}", JacksonUtils.writeValueAsString(response));
        List<CertificateReverseResult> reverseResultList = response.getCertificateReverseResultList();
        reverseResultList.removeIf(result -> AlipayResultEnum.SUCCESS.name().equals(result.getResult()));
        if (!CollectionUtils.isEmpty(reverseResultList)) {
            return MtDelCouponRespDTO.buildError(reverseResultList.get(0).getMsg());
        }
        return MtDelCouponRespDTO.buildSuccess();
    }

    /**
     * alipay.marketing.certificate.certification.refund(撤销凭证核销状态)
     */
    private AlipayMarketingCertificateCertificationRefundResponse refundAlipayCoupon(GroupVerifyDTO revokeReq) {
        AlipayMarketingCertificateCertificationRefundRequest request = getAlipayMarketingCertificateCertificationRefundRequest(revokeReq);

        AlipayAuthRespDTO authDTO = getAlipayAuthDTO(UserContextUtils.getStoreGuid());
        AlipayConfig alipayConfig = new AlipayConfig();
        alipayConfig.setPrivateKey(authDTO.getApplyPrivateKey());
        alipayConfig.setAlipayPublicKey(authDTO.getAliPublicKey());
        alipayConfig.setAppId(authDTO.getAppId());

        AlipayMarketingCertificateCertificationRefundResponse response;
        try {
            AlipayClient alipayClient = new DefaultAlipayClient(alipayConfig);
            response = alipayClient.execute(request);
        } catch (AlipayApiException apiException) {
            log.error("[撤销凭证核销状态]异常,apiException=", apiException);
            throw new BusinessException(apiException.getMessage());
        }
        if (!response.isSuccess()) {
            throw new BusinessException(response.getSubMsg());
        }
        return response;
    }

    private AlipayMarketingCertificateCertificationRefundRequest getAlipayMarketingCertificateCertificationRefundRequest(GroupVerifyDTO revokeReq) {
        AlipayMarketingCertificateCertificationRefundModel model = new AlipayMarketingCertificateCertificationRefundModel();
        model.setOutBizNo(distributedService.nextId("OutBizNo"));
        model.setUserId(revokeReq.getUserId());
        List<String> useOrderNoList = new ArrayList<>();
        useOrderNoList.add(revokeReq.getVerifyId());
        model.setUseOrderNoList(useOrderNoList);

        AlipayMarketingCertificateCertificationRefundRequest request = new AlipayMarketingCertificateCertificationRefundRequest();
        request.setBizModel(model);
//        request.putOtherTextParam(APP_AUTH_TOKEN, getToken());
        return request;
    }
}
