package com.holder.saas.store.takeaway.producers.utils.sdk.meituan;


import com.sankuai.sjst.platform.developer.domain.RequestDomain;
import com.sankuai.sjst.platform.developer.domain.RequestMethod;
import com.sankuai.sjst.platform.developer.request.CipCaterStringPairRequest;

import java.util.HashMap;
import java.util.Map;

public class CipCaterTakeoutOrderRiderPositionRequest extends CipCaterStringPairRequest {
    private Long orderId;
    private Integer logisticsStatus;
    private String courierName;
    private String courierPhone;
    private Integer thirdLogisticsId;
    private String latitude;
    private String longitude;
    private String thirdCarrierId;

    public CipCaterTakeoutOrderRiderPositionRequest() {
        this.url = RequestDomain.preUrl.getValue() + "/waimai/order/riderPosition";
        this.requestMethod = RequestMethod.POST;
    }

    @Override
    public Map<String, String> getParams() {
        Map<String, String>  param = new HashMap<>(8);
        param.put("orderId", CipCaterTakeoutOrderRiderPositionRequest.this.orderId.toString());
        param.put("logisticsStatus", CipCaterTakeoutOrderRiderPositionRequest.this.logisticsStatus.toString());
        param.put("courierName", CipCaterTakeoutOrderRiderPositionRequest.this.courierName);
        param.put("courierPhone", CipCaterTakeoutOrderRiderPositionRequest.this.courierPhone);
        param.put("thirdLogisticsId",CipCaterTakeoutOrderRiderPositionRequest.this.thirdLogisticsId.toString());
        param.put("latitude", CipCaterTakeoutOrderRiderPositionRequest.this.latitude);
        param.put("longitude", CipCaterTakeoutOrderRiderPositionRequest.this.longitude);
        param.put("thirdCarrierId", CipCaterTakeoutOrderRiderPositionRequest.this.thirdCarrierId);
        return param;
    }


    public String getThirdCarrierId() {
        return thirdCarrierId;
    }

    public void setThirdCarrierId(String thirdCarrierId) {
        this.thirdCarrierId = thirdCarrierId;
    }

    public Long getOrderId() {
        return this.orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getCourierName() {
        return this.courierName;
    }

    public void setCourierName(String courierName) {
        this.courierName = courierName;
    }

    public String getCourierPhone() {
        return this.courierPhone;
    }

    public void setCourierPhone(String courierPhone) {
        this.courierPhone = courierPhone;
    }

    public int getLogisticsStatus() {
        return logisticsStatus;
    }

    public void setLogisticsStatus(Integer logisticsStatus) {
        this.logisticsStatus = logisticsStatus;
    }

    public int getThirdLogisticsId() {
        return thirdLogisticsId;
    }

    public void setThirdLogisticsId(int thirdLogisticsId) {
        this.thirdLogisticsId = thirdLogisticsId;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

}

