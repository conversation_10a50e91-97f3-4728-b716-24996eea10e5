package com.holder.saas.store.takeaway.producers.entity.domain;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @create 2023-06-19
 * @description
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "hst_group_store_bind")
public class GroupStoreBindDO {

    private Long id;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 团购平台门店id
     */
    private String poiId;

    /**
     * 团购平台门店名称
     */
    private String poiName;

    /**
     * 团购类型GroupBuyTypeEnum
     */
    private Integer type;

    /**
     * 抖音绑定门店返回的任务id
     */
    private String taskId;

    @TableLogic
    private Integer isDelete;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;


}
