package com.holder.saas.store.takeaway.producers.mapstruct;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.takeaway.response.MtCouponPreRespDTO;
import com.meituan.sdk.model.tuangouNg.coupon.msSuperPrepare.MsSuperPrepareResponse;
import org.mapstruct.Mapper;
import org.springframework.stereotype.Component;

@Component
@Mapper(componentModel = "spring", imports = JacksonUtils.class)
public interface MtCouponMapstruct {

    MtCouponPreRespDTO msSuperPrepareResponsetoMtCouponPreRespDTO(MsSuperPrepareResponse response);
}
