package com.holder.saas.store.takeaway.producers.utils.sdk.jd;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.holder.saas.store.takeaway.producers.utils.AESUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import lombok.extern.slf4j.Slf4j;
import o2o.openplatform.sdk.dto.WebRequestDTO;
import o2o.openplatform.sdk.dto.WebResponseDTO;
import o2o.openplatform.sdk.util.HttpUtil;
import o2o.openplatform.sdk.util.SignUtils;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public abstract class AbstractJdRequest implements JdRequest {

    public String appKey;

    public String appSecret;

    public String token;

    public String execute(String json,String url) throws Exception {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String timestamp = LocalDateTime.now().format(formatter);
        WebRequestDTO webReqeustDTO = new WebRequestDTO();
        webReqeustDTO.setApp_key(appKey);
        webReqeustDTO.setFormat(format);
        webReqeustDTO.setJd_param_json(json);
        webReqeustDTO.setTimestamp(timestamp);
        webReqeustDTO.setToken(token);
        webReqeustDTO.setV(v);
        String sign = SignUtils.getSignByMD5(webReqeustDTO, appSecret);
        Map<String, Object> param = new HashMap<>();
        param.put("token", token);
        param.put("app_key", appKey);
        param.put("timestamp", timestamp);
        param.put("sign", sign);
        param.put("format", format);
        param.put("v", v);
        param.put("jd_param_json", json);
        String rsp = HttpUtil.sendSimplePostRequest(preUrl + url, param);
        WebResponseDTO<String> response = JSON.parseObject(rsp, new TypeReference<WebResponseDTO<String>>() {
        });
        if(!response.isSuccess()){
            log.info("请求京东秒送服务返回失败：{}",rsp);
            throw new BusinessException("京东秒送服务返回失败");
        }
        if(StringUtils.isNotEmpty(response.getEncryptData()) && StringUtils.isEmpty(response.getData())){
            String decryptData = AESUtils.decryptAES(response.getEncryptData(), appSecret.substring(0, 16), appSecret.substring(16, 32));
            log.info("请求京东秒送服务成功解密返回数据：{}",decryptData);
            return decryptData;
        }
        log.info("请求京东秒送服务成功返回数据：{}",response.getData());
        return response.getData();
    }

}
