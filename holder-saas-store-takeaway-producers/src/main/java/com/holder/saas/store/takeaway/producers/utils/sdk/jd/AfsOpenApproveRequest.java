package com.holder.saas.store.takeaway.producers.utils.sdk.jd;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.holder.saas.store.takeaway.producers.utils.sdk.jd.dto.AfsOpenApproveDTO;
import com.holder.saas.store.takeaway.producers.utils.sdk.jd.dto.CommonRspDTO;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @description  申请售后单审核接口
 */
@Slf4j
public class AfsOpenApproveRequest extends AbstractJdRequest {


    public AfsOpenApproveRequest() {
    }

    public AfsOpenApproveRequest(String appKey, String appSecret, String token) {
       super.appKey = appKey;
       super.appSecret = appSecret;
       super.token = token;
    }

    public boolean execute(AfsOpenApproveDTO afsOpenApproveDTO){
        try {
            String response = super.execute(JSON.toJSONString(afsOpenApproveDTO), "/afs/afsOpenApprove");

            CommonRspDTO<String> afsOpenApproveRsp = JSON.parseObject(response, new TypeReference<CommonRspDTO<String>>(){});
            return afsOpenApproveRsp.isSuccess();
        }catch (Exception e){
            log.error("售后单审核失败",e);
        }
        return false;
    }
}
