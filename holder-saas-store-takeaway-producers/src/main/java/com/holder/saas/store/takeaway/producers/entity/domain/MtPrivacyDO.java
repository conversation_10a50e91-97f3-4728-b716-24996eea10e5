package com.holder.saas.store.takeaway.producers.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 美团隐私号
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("hst_mt_privacy")
public class MtPrivacyDO {

    /**
     * 自增id
     */
    @TableId
    private Long id;

    /**
     * 唯一标识
     */
    private String guid;

    /**
     * ERP门店id
     */
    private String ePoiId;

    /**
     * 操作结果
     */
    private String orderId;

    /**
     * 订单展示号
     */
    private String orderIdView;

    /**
     * 门店下的订单流水号
     */
    private String daySeq;

    /**
     * 真实手机号
     */
    private String realPhoneNumber;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;
}