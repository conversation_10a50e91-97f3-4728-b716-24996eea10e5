package com.holder.saas.store.takeaway.producers.service.converter;

import com.holder.saas.store.takeaway.producers.entity.dto.MtAuthDTO;
import com.holder.saas.store.takeaway.producers.entity.dto.MtOauthRspDTO;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.resource.common.dto.enterprise.MultiMemberDTO;
import com.holderzone.saas.store.dto.takeaway.MtBusinessIdEnum;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @create 2023-08-25
 * @description 美团回调实体转换
 */
public class MtCallbackConverter {

    private  MtCallbackConverter(){
    }

    public static MtAuthDTO fromMultiMemberAndRsp(MultiMemberDTO multiMember, MtOauthRspDTO mtOauthRsp) {
        MtAuthDTO mtAuthDTO = new MtAuthDTO();
        mtAuthDTO.setBusinessId((byte) MtBusinessIdEnum.BRAND_MEMBER.getType());
        mtAuthDTO.setDeleted(Boolean.FALSE);

        mtAuthDTO.setEnterpriseGuid(multiMember.getEnterpriseGuid());
        mtAuthDTO.setMtStoreGuid(mtOauthRsp.getData().getOpBizCode());
        mtAuthDTO.setMtStoreName(mtOauthRsp.getData().getOpBizCode());

        mtAuthDTO.setEPoiId(multiMember.getMultiMemberGuid());
        mtAuthDTO.setAccessToken(mtOauthRsp.getData().getAccessToken());
        mtAuthDTO.setRefreshToken(mtOauthRsp.getData().getRefreshToken());
        LocalDateTime now = LocalDateTime.now();
        mtAuthDTO.setActiveTime(now);
        mtAuthDTO.setExpireTime(now.plusSeconds(mtOauthRsp.getData().getExpireIn()));
        mtAuthDTO.setRefreshTokenExpire(now.plusDays(35));
        return mtAuthDTO;
    }
}
