package com.holder.saas.store.takeaway.producers.utils;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.common.UserInfoDTO;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ThreadCacheUserInfo
 * @date 2018/09/13 16:17
 * @description 线程级别的缓存userInfo，用于动态数据源切库，获取当前用户信息，适当的时候，清除，防止内存泄漏
 * @program holder-saas-aggregation-merchant
 */
@Deprecated
public final class ThreadCacheUserInfo {

    private static final ThreadLocal<String> THREAD_LOCAL = new ThreadLocal<>();

    public static void put(String str) {
        THREAD_LOCAL.set(str);
    }

    public static UserInfoDTO get() {
        String result = THREAD_LOCAL.get();
        return JacksonUtils.toObject(UserInfoDTO.class, result);
    }

    public static String getJsonStr() {
        return THREAD_LOCAL.get();
    }

    public static String getEnterpriseGuid() {
        UserInfoDTO userInfoDTO = get();
        if (null != userInfoDTO) {
            return userInfoDTO.getEnterpriseGuid();
        }
        return null;
    }

    public static String getUserGuid() {
        String result = THREAD_LOCAL.get();
        UserInfoDTO userInfoDTO = JacksonUtils.toObject(UserInfoDTO.class, result);
        if (null != userInfoDTO) {
            return userInfoDTO.getUserGuid();
        }
        return null;
    }

    public static String getUserAccount() {
        UserInfoDTO userInfoDTO = get();
        if (null != userInfoDTO) {
            return userInfoDTO.getAccount();
        }
        return null;
    }

    public static String getUserName() {
        UserInfoDTO userInfoDTO = get();
        if (null != userInfoDTO) {
            return userInfoDTO.getUserName();
        }
        return null;
    }

    public static String getStoreGuid() {
        UserInfoDTO userInfoDTO = get();
        if (null != userInfoDTO) {
            return userInfoDTO.getStoreGuid();
        }
        return null;
    }

    public static void remove() {
        THREAD_LOCAL.remove();
    }
}
