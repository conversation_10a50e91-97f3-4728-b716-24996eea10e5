package com.holder.saas.store.takeaway.producers.service.rpc;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.resource.common.dto.enterprise.MultiMemberDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderRocketListener
 * @date 2018/11/15 12:00
 * @description 组织RPC服务
 * @program holder-saas-store-takeaway
 */
@Component
@FeignClient(value = "holder-saas-cloud-enterprise", fallbackFactory = ErpFeignService.ServiceFallBack.class)
public interface ErpFeignService {

    /**
     * 根据门店guid查询企业guid
     *
     * @param storeGuid
     * @return
     */
    @GetMapping("/organization/store/guid")
    String getEnterpriseGuidByStoreGuid(@RequestParam("storeGuid") String storeGuid);

    @GetMapping("/multi/member/get/{multiMemberGuid}")
    MultiMemberDTO getMultiMemberByGuid(@PathVariable("multiMemberGuid") String multiMemberGuid);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<ErpFeignService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public ErpFeignService create(Throwable cause) {
            return new ErpFeignService() {

                @Override
                public String getEnterpriseGuidByStoreGuid(String storeGuid) {
                    log.error(HYSTRIX_PATTERN, "getEnterpriseGuidByStoreGuid",
                            JacksonUtils.writeValueAsString(storeGuid),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public MultiMemberDTO getMultiMemberByGuid(String multiMemberGuid) {
                    log.error(HYSTRIX_PATTERN, "getMultiMemberByGuid",
                            JacksonUtils.writeValueAsString(multiMemberGuid),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }
            };
        }

    }
}
