package com.holder.saas.store.takeaway.producers.utils.sdk.jd;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.holder.saas.store.takeaway.producers.utils.sdk.jd.dto.CommonRspDTO;
import com.holder.saas.store.takeaway.producers.utils.sdk.jd.dto.OrderDeliveryOperateDTO;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @description 拣货完成且众包配送接口
 */
@Slf4j
public class OrderDeliveryOperateRequest extends AbstractJdRequest {


    public OrderDeliveryOperateRequest() {
    }

    public OrderDeliveryOperateRequest(String appKey, String appSecret, String token) {
       super.appKey = appKey;
       super.appSecret = appSecret;
       super.token = token;
    }

    public boolean execute(OrderDeliveryOperateDTO orderDeliveryOperate){
        try {
            String response = super.execute(JSON.toJSONString(orderDeliveryOperate), "/bm/open/api/order/OrderJDZBDelivery");

            CommonRspDTO<String> orderDeliveryOperateRsp = JSON.parseObject(response, new TypeReference<CommonRspDTO<String>>(){});
            return orderDeliveryOperateRsp.isSuccess();
        }catch (Exception e){
            log.error("商家出餐失败",e);
        }
        return false;
    }
}
