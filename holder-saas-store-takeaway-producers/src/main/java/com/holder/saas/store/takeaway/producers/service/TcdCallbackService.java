/*
 * Copyright (c) 2018-2028 成都掌控者科技有限公司 All Rights Reserved.
 * ProjectName:saas-platform
 * FileName:TcdCallbackService.java
 * Date:2020-3-2
 * Author:terry
 */

package com.holder.saas.store.takeaway.producers.service;

import com.holderzone.saas.store.dto.takeaway.request.TakeoutTcdOrderReqDTO;

/**
 * <AUTHOR>
 * @date 2020-03-02 下午2:00
 */
public interface TcdCallbackService {

    void orderCallback(TakeoutTcdOrderReqDTO takeoutTcdOrderReqDTO);
}
