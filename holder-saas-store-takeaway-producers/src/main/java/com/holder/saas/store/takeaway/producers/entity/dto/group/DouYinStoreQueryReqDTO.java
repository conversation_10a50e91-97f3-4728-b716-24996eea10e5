package com.holder.saas.store.takeaway.producers.entity.dto.group;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023-06-19
 * @description
 */
@Data
public class DouYinStoreQueryReqDTO {

    @JSONField(name = "account_id")
    private String accountId;

    @JSONField(name = "third_id")
    private String thirdId;

    @JSONField(name = "poi_id")
    private String poiId;

    private Integer page;

    private Integer size;

    public static String buildUrlParams(String poiId){
        return "?poi_id=" + poiId + "&page=" + 1;
    }
}
