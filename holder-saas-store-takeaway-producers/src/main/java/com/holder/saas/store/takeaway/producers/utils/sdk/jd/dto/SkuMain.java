package com.holder.saas.store.takeaway.producers.utils.sdk.jd.dto;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class SkuMain {
    /**
     * 到家商品编码
     */
    private Long skuId;

    /**
     * 商家商品编码
     */
    private String outSkuId;

    /**
     * 到家类目编码; 到家类目分三级，该字段展示最末级类目编码
     */
    private Long categoryId;

    /**
     * 到家品牌编码
     */
    private Long brandId;

    /**
     * 商家店内分类编码,店内分类分两级，该字段展示最末级分类编码；当一个商品绑定了多个店的分类时，该字段展示用逗号分隔的各个分类编码。
     */
    private Long[] shopCategories;

    /**
     * 商品名称
     */
    private String skuName;

    /**
     * 广告词
     */
    private String slogan;

    /**
     * 长(mm)
     */
    private Integer length;

    /**
     * 宽(mm)
     */
    private Integer wide;

    /**
     * 高(mm)
     */
    private Integer high;

    /**
     * 重量(公斤) 单位:KG
     */
    private Float weight;

    /**
     * 商品UPC编码
     */
    private String upcCode;

    /**
     * 商家商品上下架状态(1:上架;2:下架;4:删除;)
     */
    private Integer fixedStatus;

    /**
     * 下架时间
     */
    private LocalDateTime fixedDownTime;

    /**
     * 商品前缀
     */
    private String prefixKey;

    /**
     * 前缀开始时间
     */
    private LocalDateTime prefixKeyStartTime;

    /**
     * 前缀结束时间
     */
    private LocalDateTime prefixKeyEndTime;

    /**
     * 广告词生效时间
     */
    private LocalDateTime sloganStartTime;

    /**
     * 广告词失效时间
     */
    private LocalDateTime sloganEndTime;

    /**
     * 是否易碎品(0易碎，1非易碎)
     */
    private Integer isBreakable;

    /**
     * 储藏方式(0常温,1冷藏,2冷冻)
     */
    private String transportAttribute;

    /**
     * 是否液体(0是,1否)
     */
    private String liquidStatus;

    /**
     * 是否处方药(1是,2否)
     */
    private String prescription;

    /**
     * 是否高单值(1否,2是)
     */
    private String highSingularValue;

    /**
     * 城市ID，0为全国，其他城市ID需调用获取所有城市信息列表接口查询，如果不传该参数默认为全国
     */
    private Long[] sellCities;

    /**
     * SPUID（到家多规格商品）
     */
    private Long superId;

    /**
     * 包括销售属性值ID的id的组合
     */
    private String saleAttrValueId;

    /**
     * 包括销售属性值ID的id的名称组合 例如: 红色;XL;
     */
    private String saleAttrValueName;

    /**
     * 勋章，包含1001代表支持称重
     */
    private Integer[] businessMark;
}
