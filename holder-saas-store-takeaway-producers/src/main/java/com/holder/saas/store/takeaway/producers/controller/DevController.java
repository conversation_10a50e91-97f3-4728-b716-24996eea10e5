package com.holder.saas.store.takeaway.producers.controller;


import com.holderzone.saas.store.dto.takeaway.EleCallbackResponse;
import com.holderzone.saas.store.dto.takeaway.MtCallbackResponse;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping
public class DevController {

    /**
     * 饿了么绑定回调是Get方法，所以这里不能再定义测试方法
     */
    public EleCallbackResponse eleBindCheck() {
        return EleCallbackResponse.SUCCESS;
    }

    @GetMapping("/ele/callback/order")
    public EleCallbackResponse eleOrderCheck() {
        return EleCallbackResponse.SUCCESS;
    }

    @GetMapping("/mt/callback/order/{path}")
    public MtCallbackResponse mtOrderCheck(@PathVariable("path") String path) {
        return MtCallbackResponse.OK;
    }

    @GetMapping("/mt/callback/privacy_degrade")
    public MtCallbackResponse orderPrivacyCheck() {
        return MtCallbackResponse.OK;
    }

    @GetMapping("/mt/callback/bind")
    public MtCallbackResponse bindCheck() {
        return MtCallbackResponse.OK;
    }

    @GetMapping("/mt/callback/unbind")
    public MtCallbackResponse unbindCheck() {
        return MtCallbackResponse.OK;
    }

    @GetMapping("/mt/callback/heartbeat")
    public MtCallbackResponse heartbeatCheck() {
        return MtCallbackResponse.OK;
    }
}
