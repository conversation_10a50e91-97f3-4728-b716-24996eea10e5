package com.holder.saas.store.takeaway.producers.service;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DistributedService
 * @date 2018/02/14 09:00
 * @description
 * @program holder-saas-store-print
 */
public interface DistributedService {

    String nextId(String tag);

    List<String> nextBatchId(String tag, long count);

    String nextEleGuid();

    String nextMtGuid();

    String nextOwnGuid();

    String nextZcGuid();

    String nextAliPayGuid();
}
