package com.holder.saas.store.takeaway.producers.entity.dto.group;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2023-12-06
 * @description 农行聚卡慧验券请求参数
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AbcCouponVerifyReqDTO extends AbcReqDTO{

    /**
     * 第三方上送券码
     */
    private String couponCode;

    /**
     * 店铺id
     */
    private Long storeId;

    /**
     * 当前订单金额
     */
    private BigDecimal orderAmount;

    /**
     * 第三方核销订单号
     */
    private String thirdPayNum;

}
