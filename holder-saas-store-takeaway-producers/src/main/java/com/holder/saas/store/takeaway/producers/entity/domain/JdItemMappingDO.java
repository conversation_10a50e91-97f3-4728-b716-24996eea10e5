package com.holder.saas.store.takeaway.producers.entity.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 京东商品映射表
 */
@Data
@TableName("hst_jd_item_mapping")
public class JdItemMappingDO {
    @TableId
    private Integer id;


    /**
     * 商户门店guid
     */
    private String storeGuid;

    /**
     * 商户商品itemguid
     */
    private String itemGuid;

    /**
     * 商户商品skuguid
     */
    private String skuGuid;

    /**
     * 京东平台商户商品spu
     */
    private String spuId;

    /**
     * 京东平台商户商品sku
     */
    private String skuId;

    /**
     * 逻辑删除：0=未删除，1=已删除
     */
    @TableLogic
    @TableField("is_deleted")
    private Boolean deleted;

    /**
     * 记录创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 记录修改时间
     */
    private LocalDateTime gmtModified;
}
