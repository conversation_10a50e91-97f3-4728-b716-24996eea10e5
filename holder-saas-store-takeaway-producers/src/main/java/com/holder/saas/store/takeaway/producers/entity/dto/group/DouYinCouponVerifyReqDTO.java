package com.holder.saas.store.takeaway.producers.entity.dto.group;

import cn.hutool.core.lang.UUID;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-06-19
 * @description
 */
@Data
public class DouYinCouponVerifyReqDTO {

    /**
     * 一次验券的标识 (用于短时间内的幂等)
     */
    @JSONField(name = "verify_token")
    private String verifyToken;

    /**
     * 验券准备接口返回的加密抖音券码
     */
    @JSONField(name = "encrypted_codes")
    private List<String> encryptedCodes;

    /**
     * 核销的抖音门店id
     */
    @JSONField(name = "poi_id")
    private String poiId;

    @JSONField(name = "order_id")
    private String orderId;

    public static String buildJsonString(List<String> codes, String poiId,String orderId) {
        DouYinCouponVerifyReqDTO verifyReq = new DouYinCouponVerifyReqDTO();
        verifyReq.setEncryptedCodes(codes);
        verifyReq.setPoiId(poiId);
        verifyReq.setVerifyToken(UUID.fastUUID().toString());
        if(StringUtils.isNotEmpty(orderId)){
            verifyReq.setOrderId(orderId);
        }
        return JSON.toJSONString(verifyReq);
    }
}
