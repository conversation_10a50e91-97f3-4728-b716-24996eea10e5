package com.holder.saas.store.takeaway.producers.controller;

import com.holder.saas.store.takeaway.producers.entity.domain.StoreBindOrderDO;
import com.holder.saas.store.takeaway.producers.entity.enums.TakeoutShopBindEnum;
import com.holder.saas.store.takeaway.producers.service.*;
import com.holder.saas.store.takeaway.producers.service.factory.TakeoutOrderOperateFactory;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.takeaway.MtAuthBindUrlDTO;
import com.holderzone.saas.store.dto.takeaway.TakeoutOrderOperateDTO;
import com.holderzone.saas.store.dto.takeaway.UnItemQueryReq;
import com.holderzone.saas.store.dto.takeaway.request.*;
import com.holderzone.saas.store.dto.takeaway.response.*;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 拼接门店授权地址
 * 美团菜品绑定
 */
@Slf4j
@RestController
@RequestMapping("/takeout")
public class TakeoutController {

    private final MtAuthService mtAuthService;

    private final EleAuthService eleAuthService;

    private final JdAuthService jdAuthService;

    private final AlipayAuthService alipayAuthService;

    private final HolderAuthService holderAuthService;

    private final TcdAuthService tcdAuthService;

    private final StockStoreBindService stockStoreBindService;

    private final TakeoutOrderOperateFactory orderOperateFactory;

    @Resource
    private AuthService authService;

    @Autowired
    public TakeoutController(MtAuthService mtAuthService, EleAuthService eleAuthService,JdAuthService jdAuthService,
                             AlipayAuthService alipayAuthService, HolderAuthService holderAuthService,
                             TcdAuthService tcdAuthService, StockStoreBindService stockStoreBindService, TakeoutOrderOperateFactory orderOperateFactory) {
        this.mtAuthService = mtAuthService;
        this.eleAuthService = eleAuthService;
        this.alipayAuthService = alipayAuthService;
        this.holderAuthService = holderAuthService;
        this.tcdAuthService = tcdAuthService;
        this.stockStoreBindService = stockStoreBindService;
        this.orderOperateFactory = orderOperateFactory;
        this.jdAuthService = jdAuthService;
    }


    /**
     * 外卖拼接授权地址
     *
     * @param takeoutShopBindReqDTO 外卖类型(1：美团，2：饿了么)  操作绑定的状态(1：绑定操作，0，解除绑定操作)
     * @return 返回值TakeoutShopBindRespDTO  门店解绑/绑定地址
     */
    @ApiOperation(value = "外卖门店授权绑定页面url", notes = "外卖门店授权绑定页面url")
    @PostMapping(value = "/shop_binding_url", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public TakeoutShopBindRespDTO takeOutBindingUrl(@RequestBody @Validated TakeoutShopBindReqDTO takeoutShopBindReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("外卖门店授权绑定页面url入参：{}", JacksonUtils.writeValueAsString(takeoutShopBindReqDTO));
        }
        if (TakeoutShopBindEnum.MT_TAKEOUT.getType() == takeoutShopBindReqDTO.getTakeoutType()) {
            return mtAuthService.takeOutBindingUrl(takeoutShopBindReqDTO);
        }else if(TakeoutShopBindEnum.JD_TAKEOUT.getType() == takeoutShopBindReqDTO.getTakeoutType()){
            return jdAuthService.takeOutBindingUrl(takeoutShopBindReqDTO);
        } else {
            return eleAuthService.takeOutBindingUrl(takeoutShopBindReqDTO);
        }
    }

    @ApiOperation(value = "绑定自营外卖门店", notes = "绑定自营外卖门店")
    @PostMapping(value = "/do_shop_binding", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public TakeoutOwnRespDTO doTakeOutBinding(@RequestBody @Validated TakeoutShopOwnBindReqDTO takeoutShopOwnBindReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("绑定自营外卖门店入参：{}", JacksonUtils.writeValueAsString(takeoutShopOwnBindReqDTO));
        }
        return holderAuthService.doTakeOutBind(takeoutShopOwnBindReqDTO);
    }

    @ApiOperation(value = "解绑自营外卖门店", notes = "解绑自营外卖门店")
    @PostMapping(value = "/do_shop_unbinding", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public TakeoutOwnRespDTO doTakeOutUnBinding(@RequestBody @Validated TakeoutShopOwnUnBindReqDTO takeoutShopOwnUnBindReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("解绑自营外卖门店入参：{}", JacksonUtils.writeValueAsString(takeoutShopOwnUnBindReqDTO));
        }
        return holderAuthService.doTakeOutUnBind(takeoutShopOwnUnBindReqDTO);
    }

    @ApiOperation(value = "绑定赚餐外卖门店")
    @PostMapping(value = "/do_shop_binding_tcd")
    public TcdCommonRespDTO doShopBindingTcd(@RequestBody @Validated TCDBindReqDTO tcdBindReqDTO) {
        log.info("绑定赚餐外卖门店入参：{}", JacksonUtils.writeValueAsString(tcdBindReqDTO));
        //绑定状态：未绑定
        tcdBindReqDTO.setBindStatus(0);
        return tcdAuthService.doShopBindingTcd(tcdBindReqDTO);
    }

    @ApiOperation(value = "解绑赚餐外卖门店")
    @PostMapping(value = "/do_shop_unbinding_tcd")
    public TcdCommonRespDTO doShopUnBindingTcd(@RequestBody @Validated TCDBindReqDTO tcdBindReqDTO) {
        log.info("解绑赚餐外卖门店入参：{}", JacksonUtils.writeValueAsString(tcdBindReqDTO));
        //绑定状态：已绑定
        tcdBindReqDTO.setBindStatus(1);
        return tcdAuthService.doShopUnBindingTcd(tcdBindReqDTO);
    }

    /**
     * 团购拼接授权地址
     *
     * @param groupBuyShopBindReqDTO 团购类型(1：美团，2：饿了么)  操作绑定的状态(1：绑定操作，0，解除绑定操作) 门店guid
     * @return 返回值GroupBuyShopBindRespDTO  门店解绑/绑定地址
     */
    @ApiOperation(value = "团购门店授权绑定页面url", notes = "团购门店授权绑定页面url")
    @PostMapping(value = "/group_buy_binding_url", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public GroupBuyShopBindRespDTO groupBuyBindingUrl(@RequestBody @Validated GroupBuyShopBindReqDTO groupBuyShopBindReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("团购门店授权绑定页面url入参：{}", JacksonUtils.writeValueAsString(groupBuyShopBindReqDTO));
        }
        return mtAuthService.groupBuyBindingUrl(groupBuyShopBindReqDTO);
    }


    /**
     * 美团菜品绑定
     *
     * @param takeoutItemBindReqDTO 外卖类型(1：美团，2：饿了么)
     * @return 返回值TakeoutItemBindRespDTO  菜品绑定地址
     */
    @ApiOperation(value = "外卖门店菜品绑定页面url", notes = "外卖门店菜品绑定页面url")
    @PostMapping(value = "/item_binding_url", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public TakeoutItemBindRespDTO itemBindingUrl(@RequestBody @Validated TakeoutItemBindReqDTO takeoutItemBindReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("外卖门店菜品绑定页面url入参：{}", JacksonUtils.writeValueAsString(takeoutItemBindReqDTO));
        }
        if (1 != takeoutItemBindReqDTO.getTakeoutType()) {
            throw new BusinessException("暂不支持除美团以外的菜品绑定");
        }
        return mtAuthService.itemBindingUrl(takeoutItemBindReqDTO);
    }

    @ApiOperation(value = "绑定库存门店", notes = "绑定库存门店")
    @PostMapping(value = "/bind_stock_store", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public StockStoreBindResqDTO bindStockStore(@RequestBody @Validated StockStoreBindReqDTO req) {
        if (log.isInfoEnabled()) {
            log.info("绑定库存门店入参：{}", JacksonUtils.writeValueAsString(req));
        }
        return stockStoreBindService.bindStockStore(req);
    }

    @ApiOperation(value = "查询绑定库存门店", notes = "查询绑定库存门店")
    @GetMapping(value = "/get_bind_stock_store", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public StockStoreBindResqDTO getBindStockStore(@RequestParam("storeGuid") String storeGuid) {
        if (log.isInfoEnabled()) {
            log.info("查询绑定库存门店：{}", storeGuid);
        }
        return stockStoreBindService.getBindStockStore(storeGuid);
    }

    @ApiOperation(value = "绑定库存门店订单", notes = "绑定库存门店订单")
    @PostMapping(value = "/bind_stock_store_order", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void bindStockStoreOrder(@RequestBody @Validated StockStoreBindReqOrderDTO req) {
        if (log.isInfoEnabled()) {
            log.info("绑定库存门店订单：{}", JacksonUtils.writeValueAsString(req));
        }
        stockStoreBindService.saveBindStockStoreOrder(req);
    }

    @ApiOperation(value = "查询绑定库存门店订单", notes = "查询绑定库存门店订单")
    @GetMapping(value = "/get_bind_stock_store_order", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public StoreBindOrderDO getBindStockStoreOrder(@RequestParam("branchStoreGuid") String branchStoreGuid, @RequestParam("orderId") String orderId) {
        if (log.isInfoEnabled()) {
            log.info("查询绑定库存门店订单：{},{}", orderId, branchStoreGuid);
        }
        return stockStoreBindService.getBindStockStoreOrder(orderId, branchStoreGuid);
    }

    @ApiOperation(value = "第三方业务授权地址", notes = "第三方业务授权地址")
    @PostMapping(value = "/get_mt_auth_url", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public String getMtAuthUrl(@RequestBody @Validated MtAuthBindUrlDTO authBindUrl) {
        if (log.isInfoEnabled()) {
            log.info("获取第三方业务授权地址请求参数：{}", JacksonUtils.writeValueAsString(authBindUrl));
        }
        return mtAuthService.getMtAuthBindUrl(authBindUrl);
    }

    @ApiOperation(value = "查询业务授权绑定信息", notes = "第三方业务授权地址")
    @GetMapping(value = "/get_mt_bind_auth/{data}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public List<MtAuthBindUrlDTO> getMtBindAuth(@PathVariable("data") String data) {
        if (log.isInfoEnabled()) {
            log.info("查询业务授权绑定信息：{}", data);
        }
        return mtAuthService.getMtBindAuth(data);
    }


    @ApiOperation(value = "查询已授权外卖平台的门店列表", notes = "查询已授权外卖平台的门店列表")
    @PostMapping(value = "/get_auth_stores")
    public List<String> getAuthStores(@RequestBody UnItemQueryReq unItemQueryReq) {
        if (log.isInfoEnabled()) {
            log.info("查询已授权外卖平台的门店列表：入参:{}", JacksonUtils.writeValueAsString(unItemQueryReq));
        }
        return authService.authStoreGuids(unItemQueryReq.getStoreGuids(), unItemQueryReq.getTakeoutType());
    }

    @PostMapping(value = "/notify_alipay_auth")
    @ApiOperation(value = "通知支付宝授权变更", notes = "通知支付宝授权变更")
    public void notifyAliPayAuth(@RequestBody @Validated NotifyAliPayAuthReqDTO notifyDTO) {
        log.info("[通知支付宝授权变更],入参{}", JacksonUtils.writeValueAsString(notifyDTO));
        alipayAuthService.notifyAliPayAuth(notifyDTO);
    }

    /**
     * 订单出餐
     */
    @PostMapping(value = "/order_operate/{type}")
    public void orderOperate(@PathVariable("type") Integer type, @RequestBody TakeoutOrderOperateDTO orderOperateDTO) {
        log.info("外卖订单出餐,入参{}", JacksonUtils.writeValueAsString(orderOperateDTO));
        orderOperateFactory.type(type).orderPrepared(orderOperateDTO);
    }

}
