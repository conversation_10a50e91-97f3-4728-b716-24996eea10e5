package com.holder.saas.store.takeaway.producers.service.converter;

import com.holder.saas.store.takeaway.producers.entity.domain.MtAuthDO;
import com.holder.saas.store.takeaway.producers.entity.dto.MtAuthDTO;
import com.holder.saas.store.takeaway.producers.entity.dto.MtMemberParamsDTO;
import com.holder.saas.store.takeaway.producers.entity.dto.MtOauthRspDTO;
import com.holderzone.resource.common.dto.enterprise.MultiMemberDTO;
import com.holderzone.saas.store.dto.member.PlatformMemberDTO;
import com.holderzone.saas.store.dto.takeaway.MtBusinessIdEnum;
import com.holderzone.saas.store.dto.takeaway.MtCallbackMemberDTO;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @create 2023-08-25
 * @description 美团会员实体转换
 */
public class PlatformMemberConverter {

    private PlatformMemberConverter() {
    }

    public static PlatformMemberDTO fromMtMemberParamsAndAuth(MtMemberParamsDTO mtMemberParams, MtAuthDO auth) {
        PlatformMemberDTO platformMember = new PlatformMemberDTO();
        platformMember.setType(0);
        platformMember.setEnterpriseGuid(auth.getEnterpriseGuid());
        platformMember.setOperSubjectGuid(auth.getEPoiId());
        platformMember.setPhone(mtMemberParams.getPhoneNo());
        platformMember.setSourceName(mtMemberParams.getPlatformType());
        return platformMember;
    }
}
