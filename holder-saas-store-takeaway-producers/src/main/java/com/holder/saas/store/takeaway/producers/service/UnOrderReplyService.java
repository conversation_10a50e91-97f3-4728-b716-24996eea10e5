package com.holder.saas.store.takeaway.producers.service;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.takeaway.UnOrder;
import com.holderzone.saas.store.dto.takeaway.UnOrderReplyMsgType;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderRocketListener
 * @date 2018/11/15 12:00
 * @description UnOrder回复平台服务
 * @program holder-saas-store-takeaway
 */
public interface UnOrderReplyService extends UnOrderDeliveryService {

    /**
     * 商家拒单
     *
     * @param unOrder
     */
    void replyCancelOrder(UnOrder unOrder);

    /**
     * 商家接单
     *
     * @param unOrder
     */
    void replyConfirmOrder(UnOrder unOrder);

    /**
     * 商家同意取消订单
     *
     * @param unOrder
     */
    void replyAgreeCancelOrder(UnOrder unOrder);

    /**
     * 商家不同意取消订单
     *
     * @param unOrder
     */
    void replyDisagreeCancelOrder(UnOrder unOrder);

    /**
     * 商家同意退单
     *
     * @param unOrder
     */
    void replyAgreeRefundOrder(UnOrder unOrder);

    /**
     * 商家不同意退单
     *
     * @param unOrder
     */
    void replyDisagreeRefundOrder(UnOrder unOrder);

    /**
     * 商家回复催单
     *
     * @param unOrder
     */
    void replyUrgeOrder(UnOrder unOrder);

    /**
     * 根据msgType选择处理逻辑
     *
     * @param unOrder
     */
    default void doReply(UnOrder unOrder) {
        if (null == unOrder || -1 == unOrder.getReplyMsgType()) {
            throw new BusinessException("UnOrder消息类型ReplyMsgType未设置");
        }
        switch (unOrder.getReplyMsgType()) {
            case UnOrderReplyMsgType.CONFIRM_ORDER:
                this.replyConfirmOrder(unOrder);
                break;
            case UnOrderReplyMsgType.CANCEL_ORDER:
                this.replyCancelOrder(unOrder);
                break;
            case UnOrderReplyMsgType.AGREE_CANCEL_ORDER:
                this.replyAgreeCancelOrder(unOrder);
                break;
            case UnOrderReplyMsgType.DISAGREE_CANCEL_ORDER:
                this.replyDisagreeCancelOrder(unOrder);
                break;
            case UnOrderReplyMsgType.AGREE_REFUND_ORDER:
                this.replyAgreeRefundOrder(unOrder);
                break;
            case UnOrderReplyMsgType.DISAGREE_REFUND_ORDER:
                this.replyDisagreeRefundOrder(unOrder);
                break;
            case UnOrderReplyMsgType.REPLY_URGE_ORDER:
                this.replyUrgeOrder(unOrder);
                break;
            case UnOrderReplyMsgType.START_DELIVERY_ORDER:
                this.startDelivery(unOrder);
                break;
            case UnOrderReplyMsgType.CANCEL_DELIVERY_ORDER:
                this.cancelDelivery(unOrder);
                break;
            case UnOrderReplyMsgType.DELIVERY_KNIGHT_ACCEPT:
                this.replyDeliveryAccept(unOrder);
                break;
            case UnOrderReplyMsgType.DELIVERY_START:
                this.replyDeliveryStart(unOrder);
                break;
            case UnOrderReplyMsgType.DELIVERY_CANCEL:
                this.replyDeliveryCancel(unOrder);
                break;
            case UnOrderReplyMsgType.DELIVERY_COMPLETE:
                this.replyDeliveryComplete(unOrder);
                break;
            case UnOrderReplyMsgType.DELIVERY_KNIGHT_LOCATION:
                this.replyRiderPosition(unOrder);
                break;
            default:
                throw new BusinessException("invalid MsgType");
        }
    }
}
