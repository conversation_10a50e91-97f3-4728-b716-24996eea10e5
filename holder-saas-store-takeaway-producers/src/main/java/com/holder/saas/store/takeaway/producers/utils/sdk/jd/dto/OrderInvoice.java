package com.holder.saas.store.takeaway.producers.utils.sdk.jd.dto;

import lombok.Data;

@Data
public class OrderInvoice {

    /**
     * 发票类型：0.纸质发票1.电子发票
     */
    private Integer invoiceFormType;

    /**
     * 发票抬头
     */
    private String invoiceTitle;

    /**
     * 发票税号
     */
    private String invoiceDutyNo;

    /**
     * 发票邮箱地址
     */
    private String invoiceMail;

    /**
     * 发票金额
     */
    private Long invoiceMoney;

    /**
     * 发票抬头类型(0：个人、1：企业普票、2：企业专票)
     */
    private Integer invoiceType;

    /**
     * 发票金额描述
     */
    private String invoiceMoneyDetail;

    /**
     * 公司注册地址
     */
    private String invoiceAddress;

    /**
     * 公司注册电话
     */
    private String invoiceTelNo;

    /**
     * 公司开户银行名称
     */
    private String invoiceBankName;

    /**
     * 公司开户银行账户
     */
    private String invoiceAccountNo;

    /**
     * 发票内容
     */
    private String invoiceContent;
}
