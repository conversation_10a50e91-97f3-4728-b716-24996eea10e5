package com.holder.saas.store.takeaway.producers.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.holder.saas.store.takeaway.producers.entity.domain.JdAuthDO;
import com.holder.saas.store.takeaway.producers.entity.domain.JdItemMappingDO;
import com.holder.saas.store.takeaway.producers.entity.domain.JdStoreMappingDO;
import com.holder.saas.store.takeaway.producers.service.*;
import com.holder.saas.store.takeaway.producers.utils.BigDecimalUtil;
import com.holder.saas.store.takeaway.producers.utils.sdk.jd.OrderAfsApplyInfoRequest;
import com.holder.saas.store.takeaway.producers.utils.sdk.jd.OrderQueryRequest;
import com.holder.saas.store.takeaway.producers.utils.sdk.jd.dto.*;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.takeaway.*;
import com.holderzone.saas.store.dto.takeaway.jd.BusinessOrderDTO;
import com.holderzone.saas.store.dto.takeaway.jd.OrderStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class JdOrderServiceImpl implements JdOrderService {

    private final JdAuthService jdAuthService;

    private final UnOrderMqService unOrderMqService;

    private final JdStoreMappingService jdStoreMappingService;

    private final JdItemMappingService jdItemMappingService;

    @Override
    public void orderCallback(BusinessOrderDTO businessOrder) {
        if(businessOrder.getStatus() == null){
            log.error("京东外卖订单回调类型解析错误：{}", JacksonUtils.writeValueAsString(businessOrder));
            return;
        }
        UnOrder unOrder = queryAndTransfer(businessOrder);
        if(unOrder == null){
            return;
        }
        if(businessOrder.getStatus() == OrderStatusEnum.ACTIVATED){
            unOrder.setActiveTime(DateTimeUtils.string2LocalDateTime(businessOrder.getTimestamp()));
        }
        if(businessOrder.getStatus() == OrderStatusEnum.CONFIRMED){
            unOrder.setAcceptTime(DateTimeUtils.string2LocalDateTime(businessOrder.getTimestamp()));
        }
        if(businessOrder.getStatus() == OrderStatusEnum.FINISHED){
            unOrder.setCompleteTime(DateTimeUtils.string2LocalDateTime(businessOrder.getTimestamp()));
        }
        if(businessOrder.getStatus() == OrderStatusEnum.CANCEL_REQ || businessOrder.getStatus() == OrderStatusEnum.CANCELED_BEFORE_CONFIRMED){
            unOrder.setCancelReqTime(DateTimeUtils.string2LocalDateTime(businessOrder.getTimestamp()));
        }
        //设置业务标识
        unOrder.setCbMsgType(businessOrder.getStatus().getSystemCode());
        unOrderMqService.sendUnOrder(unOrder);
    }

    private UnOrder queryAndTransfer(BusinessOrderDTO businessOrder){
        //查询授权信息
        JdAuthDO jdAuthByToken = jdAuthService.getJdAuthByToken(businessOrder.getToken());
        if (jdAuthByToken == null) {
            log.error("授权信息不存在，{}", JacksonUtils.writeValueAsString(businessOrder));
            return null;
        }
        //先根据回调消息订单号查询订单详情
        OrderQueryRequest orderQueryRequest = new OrderQueryRequest(jdAuthByToken.getAppKey(), jdAuthByToken.getAppSecret(), businessOrder.getToken());
        OrderQueryReqDTO reqDTO = OrderQueryReqDTO.builder().orderId(Long.valueOf(businessOrder.getOrderId())).pageNo(1L).pageSize(1).build();
        List<OrderInfoDTO> list = orderQueryRequest.execute(reqDTO);
        if(list.isEmpty()){
            return null;
        }
        OrderInfoDTO orderInfoDTO = list.get(0);

        //根据门店编码以及token查询授权信息
        JdStoreMappingDO jdStoreMappingDO = jdStoreMappingService.getByVenderStoreAndToken(orderInfoDTO.getDeliveryStationNo());
        if(jdStoreMappingDO == null){
            log.error("授权信息为空，商家门店为：{}",orderInfoDTO.getDeliveryStationNo());
            return null;
        }
        UnOrder unOrder = new UnOrder();
        unOrder.setShopId(Long.valueOf(orderInfoDTO.getDeliveryStationNo()));
        unOrder.setShopName(orderInfoDTO.getDeliveryStationName());
        unOrder.setOrderId(String.valueOf(orderInfoDTO.getOrderId()));
        unOrder.setStoreGuid(jdStoreMappingDO.getStoreGuid());
        unOrder.setEnterpriseGuid(jdStoreMappingDO.getEnterpriseGuid());
        unOrder.setOrderType(OrderType.TAKEOUT_ORDER.getType());
        unOrder.setOrderSubType(OrderType.TakeoutSubType.JD_TAKEOUT.getType());
        //解析订单数据
        transferUnOrder(unOrder,orderInfoDTO,jdStoreMappingDO.getStoreGuid());
        //售后申请订单
        if(businessOrder.getStatus() == OrderStatusEnum.REFUND_REQ){
            unOrder.setRefundReqTime(DateTimeUtils.string2LocalDateTime(businessOrder.getTimestamp()));
            //查询售后单详情
            OrderAfsApplyInfoRequest applyInfoRequest = new OrderAfsApplyInfoRequest(jdAuthByToken.getAppKey(), jdAuthByToken.getAppSecret(), businessOrder.getToken());
            OrderAfsApplyInfoResponse orderAfsApplyInfoResponse = applyInfoRequest.execute(businessOrder.getOrderId());
            if(orderAfsApplyInfoResponse == null){
                return null;
            }
            unOrder.setRefundReqReason(orderAfsApplyInfoResponse.getAfsServiceQuestionVenderList().stream().map(OrderAfsApplyInfoResponse.AfsServiceQuestionVender::getQuestionTypeName).collect(Collectors.joining(",")));
        }
        return unOrder;
    }

    private void transferUnOrder( UnOrder unOrder,OrderInfoDTO orderInfoDTO,String storeGuid) {

        unOrder.setOrderViewId(String.valueOf(orderInfoDTO.getOrderId()));
        unOrder.setOrderDaySn(String.valueOf(orderInfoDTO.getOrderNum()));
        unOrder.setOrderRemark(orderInfoDTO.getOrderBuyerRemark());
        //支付类型为4则是在线支付
        unOrder.setOnlinePay(orderInfoDTO.getOrderPayType() == 4);
        unOrder.setFirstOrder(false);
        unOrder.setCreateTime(orderInfoDTO.getOrderStartTime());
        unOrder.setActiveTime(orderInfoDTO.getOrderPreStartDeliveryTime());
        unOrder.setCustomerNumber(1);
        unOrder.setCustomerName(orderInfoDTO.getBuyerFullName());
        unOrder.setCustomerAddress(orderInfoDTO.getBuyerFullAddress());

        //顾客真实手机号
        unOrder.setCustomerPhone(Collections.singletonList("\"尾号" + orderInfoDTO.getLastFourDigitsOfBuyerMobile() + "\"" ).toString());
        unOrder.setPrivacyPhone(Collections.singletonList("\""  + orderInfoDTO.getBuyerMobile().replaceAll(",","&") + "\"").toString());

        // 配送
        unOrder.setShipLongitude(String.valueOf(orderInfoDTO.getBuyerLng()));
        unOrder.setShipLatitude(String.valueOf(orderInfoDTO.getBuyerLat()));
        unOrder.setEstimateDeliveredTime(orderInfoDTO.getOrderPreStartDeliveryTime());

        //是否是预订单：京东外卖businessTag字段包含：one_dingshida，dj_aging_nextday
        unOrder.setReserve(orderInfoDTO.getBusinessTag().contains("one_dingshida")
                || orderInfoDTO.getBusinessTag().contains("dj_aging_nextday") );
        unOrder.setInvoiced(orderInfoDTO.getOrderInvoiceOpenMark() == 1);
        if(unOrder.getInvoiced() && orderInfoDTO.getOrderInvoice() != null){

            unOrder.setInvoiceTitle(orderInfoDTO.getOrderInvoice().getInvoiceTitle());
            unOrder.setTaxpayerId(orderInfoDTO.getOrderInvoice().getInvoiceDutyNo());
            unOrder.setInvoiceType(orderInfoDTO.getOrderInvoice().getInvoiceType());
        }

        // 订单原始价格=餐盒费+配送费+菜品费用
        BigDecimal originalPrice = BigDecimalUtil.nonNullValue(orderInfoDTO.getOrderBuyerPayableMoney()).divide(new BigDecimal(100),2,RoundingMode.HALF_UP);
        BigDecimal deliverFee = BigDecimalUtil.nonNullValue(orderInfoDTO.getOrderReceivableFreight()).divide(new BigDecimal(100),2,RoundingMode.HALF_UP);
        BigDecimal packageFee = BigDecimalUtil.nonNullValue(orderInfoDTO.getPackagingMoney()).divide(new BigDecimal(100),2,RoundingMode.HALF_UP);
        unOrder.setCustomerActualPay(originalPrice);
        unOrder.setShipTotal(deliverFee);
        unOrder.setItemTotal(BigDecimalUtil.nonNullValue(orderInfoDTO.getOrderTotalMoney()).divide(new BigDecimal(100),2,RoundingMode.HALF_UP));

        // 京东服务费未找到
        unOrder.setShopTotal(originalPrice);

        // 第三方配送
        unOrder.setThirdShipper(false);

        // 填充UnOrderDetail
        unOrder.setArrayOfUnItem(parseUnOrderItems(orderInfoDTO.getProduct(),storeGuid));

        //打包费中还需加上商品的包装费
        BigDecimal itemPackageFee = unOrder.getArrayOfUnItem().stream().map(UnItem::getBoxPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
        unOrder.setPackageTotal(packageFee.add(itemPackageFee));

        //总价等于商品+运费+打包费
        unOrder.setTotal(unOrder.getItemTotal().add(unOrder.getPackageTotal()).add(unOrder.getShipTotal()));

        // 填充UnOrderDiscount
        List<UnDiscount> unDiscounts = parseUnOrderDiscount(orderInfoDTO.getDiscount());
        BigDecimal discountTotal = unDiscounts.isEmpty() ? BigDecimal.ZERO : unDiscounts.stream().map(UnDiscount::getTotalDiscount).reduce(BigDecimal.ZERO, BigDecimal::add);
        // 优惠金额
        unOrder.setDiscountTotal(discountTotal);
        unOrder.setPlatformDiscount(BigDecimal.ZERO);
        unOrder.setEnterpriseDiscount(discountTotal);
        unOrder.setArrayOfUnDiscount(unDiscounts);

        //订单取消时间
        unOrder.setCancelTime(orderInfoDTO.getOrderCancelTime());
        unOrder.setCancelReason(orderInfoDTO.getOrderCancelRemark());

        //订单完成参数
        unOrder.setDeliveredTime(orderInfoDTO.getDeliveryConfirmTime());
    }

    private List<UnDiscount> parseUnOrderDiscount(List<OrderDiscountDTO> orderDiscountDTOList) {
        List<UnDiscount> discountList = Lists.newArrayList();
        if(CollUtil.isEmpty(orderDiscountDTOList)){
            return discountList;
        }
        for (OrderDiscountDTO discount : orderDiscountDTOList) {
            UnDiscount unDiscount = new UnDiscount();
            unDiscount.setDiscountName(discount.getDiscountName());
            unDiscount.setTotalDiscount(BigDecimal.valueOf(discount.getDiscountPrice()).divide(new BigDecimal(100),2,RoundingMode.HALF_UP));
            unDiscount.setEnterpriseDiscount(unDiscount.getTotalDiscount());
            //暂时未区分平台和企业优惠
            unDiscount.setPlatformDiscount(BigDecimal.ZERO);
            discountList.add(unDiscount);
        }
        return discountList;
    }

    private List<UnItem> parseUnOrderItems(List<OrderProductDTO> productList,String storeGuid) {
        List<UnItem> unItems = Lists.newArrayList();
        if(CollUtil.isEmpty(productList)){
            return unItems;
        }
        //平台映射关系
        List<JdItemMappingDO> itemMapping = jdItemMappingService.listItemMappingByStore(storeGuid, productList.stream().map(e -> String.valueOf(e.getSkuId())).collect(Collectors.toList()));
        Map<String,JdItemMappingDO> itemMappingMap = Maps.newHashMap();
        if(CollUtil.isNotEmpty(itemMapping)){
            itemMappingMap = itemMapping.stream().collect(Collectors.toMap(JdItemMappingDO::getSkuId,  Function.identity(), (key1, key2) -> key1));
        }
        for (OrderProductDTO product : productList) {
            UnItem unItem = new UnItem();
            //设置默认值
            unItem.setItemSku("");
            //餐饮系统商品编码
            JdItemMappingDO jdItemMappingDO = itemMappingMap.get(String.valueOf(product.getSkuId()));
            if(jdItemMappingDO != null){
                unItem.setItemCode(jdItemMappingDO.getItemGuid());
                unItem.setItemSku(jdItemMappingDO.getSkuGuid());
                unItem.setUnItemSkuId(String.valueOf(product.getSkuId()));
            }
            unItem.setItemName(product.getSkuName());

            //无单位字段
            unItem.setItemUnit("份");
            unItem.setItemCount(BigDecimal.valueOf(product.getSkuCount()));
            unItem.setItemPrice(BigDecimal.valueOf(product.getSkuJdPrice()).divide(new BigDecimal(100),2,RoundingMode.HALF_UP));
            unItem.setItemTotal(unItem.getItemCount().multiply(unItem.getItemPrice()));
            //无数量
            unItem.setBoxCount(BigDecimal.ONE);
            unItem.setBoxPrice(BigDecimalUtil.nonNullValue(product.getCanteenMoney()).divide(new BigDecimal(100),2,RoundingMode.HALF_UP));
            unItem.setBoxTotal(unItem.getBoxPrice());
            unItem.setItemSpec("");
            unItem.setItemProperty(StringUtils.isEmpty(product.getSkuCostumeProperty()) ? "" : product.getSkuCostumeProperty().replaceAll(";",",").replaceAll(",$", ""));
            unItem.setActualPrice(unItem.getItemCount().multiply(product.getSkuCostPrice() == null ? unItem.getItemPrice() : BigDecimal.valueOf(product.getSkuCostPrice())).setScale(2, RoundingMode.HALF_UP));

            unItem.setThirdSkuId(String.valueOf(product.getSkuId()));
            unItems.add(unItem);
        }
        return unItems;
    }

}
