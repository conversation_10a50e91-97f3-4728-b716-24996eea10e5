package com.holder.saas.store.takeaway.producers.utils.sdk.meituan;

import com.google.common.collect.Maps;
import com.holder.saas.store.takeaway.producers.utils.HttpsClientUtils;
import com.holder.saas.store.takeaway.producers.utils.SignUtil;
import com.meituan.sdk.internal.utils.JsonUtil;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import java.util.Map;

/**
 * 批量查询商品(拼好饭)
 */
@Setter
@Getter
@NoArgsConstructor
public class CipCaterTakeoutSpecialFoodBatchQueryRequest{

    private static final String MT_BATCH_QUERY = "https://api-open-cater.meituan.com/waimai/ng/special/food/batchQuery";

    private String mtSignKey;

    private String accessToken;

    private Integer developerId;

    private String ePoiId;

    private Integer pageSize;

    private Integer pageNum;

    public CipCaterTakeoutSpecialFoodBatchQueryRequest(String mtSignKey, String accessToken,Integer developerId) {
        this.mtSignKey = mtSignKey;
        this.accessToken = accessToken;
        this.developerId = developerId;
    }

    public Map<String, Object> getParams() {
        Map<String, Object> map = Maps.newHashMap();
        map.put("ePoiId", CipCaterTakeoutSpecialFoodBatchQueryRequest.this.ePoiId);
        map.put("businessIdentify", 1);
        if(CipCaterTakeoutSpecialFoodBatchQueryRequest.this.pageNum != null){
            map.put("pageNum", CipCaterTakeoutSpecialFoodBatchQueryRequest.this.pageNum);
        }else {
            map.put("pageNum",1);
        }
        if(CipCaterTakeoutSpecialFoodBatchQueryRequest.this.pageSize != null){
            map.put("pageSize", CipCaterTakeoutSpecialFoodBatchQueryRequest.this.pageSize);
        }else {
            map.put("pageSize",20);
        }
        return map;
    }

    public String doRequest() {
        Map<String, String> params = Maps.newHashMap();
        params.put("appAuthToken", this.getAccessToken());
        params.put("charset", "UTF-8");
        params.put("timestamp", String.valueOf(System.currentTimeMillis() / 1000));
        params.put("developerId", String.valueOf(this.getDeveloperId()));
        params.put("biz", JsonUtil.toJson(getParams()));
        params.put("version", "2");
        params.put("businessId", "2");
        String sign = SignUtil.getSign(this.getMtSignKey(), params);
        params.put("sign", sign);
        return HttpsClientUtils.doPost(MT_BATCH_QUERY, params);
    }
}
