package com.holder.saas.store.takeaway.producers.event;

import com.holder.saas.store.takeaway.producers.config.RocketMqConfig;
import com.holder.saas.store.takeaway.producers.service.UnOrderReplyService;
import com.holder.saas.store.takeaway.producers.service.UnOrderReplyServiceFactory;
import com.holderzone.framework.rocketmq.anno.RocketListenerHandler;
import com.holderzone.framework.rocketmq.common.AbstractRocketMqConsumer;
import com.holderzone.framework.rocketmq.constants.RocketMqTopic;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.takeaway.OrderType.TakeoutSubType;
import com.holderzone.saas.store.dto.takeaway.UnOrder;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderRocketListener
 * @date 2018/11/15 12:00
 * @description
 * @program holder-saas-store-takeaway
 */
@Deprecated
@Slf4j
@Component
@RocketListenerHandler(
        topic = RocketMqConfig.TAKEAWAY_PRODUCERS_ORDER_TOPIC,
        tags = RocketMqConfig.TAKEAWAY_PRODUCERS_ORDER_TAG,
        consumerGroup = RocketMqConfig.TAKEAWAY_ORDER_PRODUCERS_GROUP)
public class ProducerUnOrderListener extends AbstractRocketMqConsumer<RocketMqTopic, UnOrder> {

    private final UnOrderReplyServiceFactory unOrderReplyServiceFactory;

    @Autowired
    public ProducerUnOrderListener(UnOrderReplyServiceFactory unOrderReplyServiceFactory) {
        this.unOrderReplyServiceFactory = unOrderReplyServiceFactory;
    }

    @Override
    public boolean consumeMsg(UnOrder unOrder, MessageExt messageExt) {
        if (log.isInfoEnabled()) {
            log.info("收到ERP发送的Reply消息，unOrder: {}", JacksonUtils.writeValueAsString(unOrder));
        }

        try {
            TakeoutSubType takeoutSubType = TakeoutSubType.ofType(unOrder.getOrderSubType());
            UnOrderReplyService unOrderReplyService = unOrderReplyServiceFactory.create(takeoutSubType);
            unOrderReplyService.doReply(unOrder);
        } catch (Exception e) {
            if (log.isErrorEnabled()) {
                log.error("unOrder消费发生异常：{}", ThrowableUtils.asString(e));
            }
        }

        return true;
    }
}
