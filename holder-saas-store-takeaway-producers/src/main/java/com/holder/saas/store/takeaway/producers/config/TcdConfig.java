package com.holder.saas.store.takeaway.producers.config;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = "zc")
@Data
public class TcdConfig {

    @JSONField(name = "URL")
    public String url;

    public String orderPrepared = "";
}
