package com.holder.saas.store.takeaway.producers.controller;

import com.holder.saas.store.takeaway.producers.service.MtBrandMemberService;
import com.holder.saas.store.takeaway.producers.service.job.EleOrderJob;
import com.holder.saas.store.takeaway.producers.service.job.MtOrderJob;
import com.holder.saas.store.takeaway.producers.service.job.RefreshTokenJob;
import com.holder.saas.store.takeaway.producers.utils.ThrowableExtUtils;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/job")
public class JobController {

    private final EleOrderJob eleOrderJob;

    private final MtOrderJob mtOrderJob;

    private final RefreshTokenJob refreshTokenJob;

    private MtBrandMemberService mtBrandMemberService;

    @Autowired
    public JobController(EleOrderJob eleOrderJob, MtOrderJob mtOrderJob, RefreshTokenJob refreshTokenJob,
                         MtBrandMemberService mtBrandMemberService) {
        this.eleOrderJob = eleOrderJob;
        this.mtOrderJob = mtOrderJob;
        this.refreshTokenJob = refreshTokenJob;
        this.mtBrandMemberService = mtBrandMemberService;
    }

    @PostMapping("/ele_order")
    public void eleOrder() {
        eleOrderJob.queryUnProcessOrders();
    }

    @PostMapping("/mt_order")
    public void mtOrder() {
        log.info("根据开发者id批量查询美团订单接口已失效");
//        mtOrderJob.queryNewOrdersByDevId();
    }

    @PostMapping("/ele_refresh_token")
    public void eleRefreshToken() {
        refreshTokenJob.refreshEleToken();
    }

    @PostMapping("/own_refresh_token")
    public void ownOrder() {
        refreshTokenJob.refreshOwnToken();
    }

    @PostMapping("/member/refresh_token")
    @ApiOperation(value = "更新授权令牌", notes = "更新授权令牌")
    public void memberRefreshToken() {
        if (log.isInfoEnabled()) {
            log.info("===更新授权令牌===");
        }
        try {
            mtBrandMemberService.memberRefreshToken();
        } catch (Exception e) {
            log.error("[更新授权令牌]异常，{}", ThrowableExtUtils.asStringIfAbsent(e));
        }
    }
}
