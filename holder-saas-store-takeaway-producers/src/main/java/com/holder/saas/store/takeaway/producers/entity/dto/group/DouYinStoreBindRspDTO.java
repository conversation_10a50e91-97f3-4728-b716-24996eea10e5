package com.holder.saas.store.takeaway.producers.entity.dto.group;


import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.annotation.JSONField;
import com.holderzone.saas.store.dto.exception.GroupBuyException;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023-06-19
 * @description
 */
@Data
public class DouYinStoreBindRspDTO extends DouYinCommonDTO{

    /**
     * 任务id
     */
    @JSONField(name = "task_id")
    private String taskId;

    private String poiName;

    public static DouYinStoreBindRspDTO parseJsonAndCheck(String rsp) {
        DouYinRspDTO<DouYinStoreBindRspDTO> douYinStoreBindRsp = JSONObject.parseObject(rsp,new TypeReference<DouYinRspDTO<DouYinStoreBindRspDTO>>(){}.getType());
        if (douYinStoreBindRsp == null || douYinStoreBindRsp.getData() == null){
            throw new GroupBuyException("绑定抖音门店返回为空");
        }
        if(douYinStoreBindRsp.getData().getErrorCode() != 0){
            throw new GroupBuyException(douYinStoreBindRsp.getData().getDescription());
        }
        if("0".equals(douYinStoreBindRsp.getData().getTaskId())){
            throw new GroupBuyException("绑定抖音门店失败");
        }
        return douYinStoreBindRsp.getData();
    }

}
