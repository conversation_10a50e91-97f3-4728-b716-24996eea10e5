package com.holder.saas.store.takeaway.producers.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.holder.saas.store.takeaway.producers.config.GroupBuyAbcConfig;
import com.holder.saas.store.takeaway.producers.entity.domain.GroupStoreBindDO;
import com.holder.saas.store.takeaway.producers.entity.dto.group.*;
import com.holder.saas.store.takeaway.producers.service.GroupBuyService;
import com.holder.saas.store.takeaway.producers.service.GroupStoreBindService;
import com.holder.saas.store.takeaway.producers.service.converter.GroupBuyConverter;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.constant.Constant;
import com.holderzone.saas.store.dto.business.group.StoreBindDTO;
import com.holderzone.saas.store.dto.order.response.groupon.GroupVerifyDTO;
import com.holderzone.saas.store.dto.takeaway.request.CouPonPreReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.CouPonReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtCouponPreRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtDelCouponRespDTO;
import com.holderzone.saas.store.enums.GroupBuyTypeEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.List;
import java.util.Locale;

/**
 * <AUTHOR>
 * @create 2023-12-06
 * @description 农行聚卡慧团购服务类
 * 测试门店id：8374597659335462912
 * 测试券码：EAcEC8pzjd
 */
@Service("abcGroupBuyServiceImpl")
@Slf4j
@AllArgsConstructor
public class AbcGroupBuyServiceImpl implements GroupBuyService {

    private final GroupBuyAbcConfig groupBuyAbcConfig;

    private final RestTemplate groupBuyRestTemplate;

    private final GroupStoreBindService groupStoreBindService;

    private static final String REMOTE_ERROR = "请求农行聚卡慧服务异常";

    private static final String STORE_DISABLED = "农行聚卡慧门店已禁用";

    private static final String COUPON_NOT_AVAILABLE = "优惠券不可用";

    private static final String STORE_ERROR = "请输入正确的门店ID";

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void bindStore(StoreBindDTO storeBind) {
        storeBind.verify();
        //查询门店是否绑定
        int count = groupStoreBindService.count(new LambdaQueryWrapper<GroupStoreBindDO>().eq(GroupStoreBindDO::getStoreGuid, storeBind.getStoreGuid())
                .eq(GroupStoreBindDO::getType, storeBind.getGroupBuyType()));
        if(count > 0){
            throw new BusinessException("此门店已绑定聚卡汇门店");
        }
        try {
            //清除绑定此聚卡慧门店的信息
            groupStoreBindService.remove(new LambdaQueryWrapper<GroupStoreBindDO>().eq(GroupStoreBindDO::getPoiId, storeBind.getPoiId())
                    .eq(GroupStoreBindDO::getType, storeBind.getGroupBuyType()));
            //远程请求绑定门店
            AbcCouponStoreReqDTO abcCouponStoreReqDTO = new AbcCouponStoreReqDTO();
            abcCouponStoreReqDTO.setStoreId(Long.parseLong(storeBind.getPoiId()));
            abcCouponStoreReqDTO.setTimestamp(System.currentTimeMillis());
            abcCouponStoreReqDTO.setSign(getSign(abcCouponStoreReqDTO.getTimestamp()));
            String rsp = postRequest(JSON.toJSONString(abcCouponStoreReqDTO), groupBuyAbcConfig.host +groupBuyAbcConfig.shopQuery);
            //解析返回结果
            AbcRspDTO<AbcStoreRspDTO> storeRsp = JSON.parseObject(rsp,new TypeReference<AbcRspDTO<AbcStoreRspDTO>>(){}.getType());
            if(!storeRsp.isSuccess()){
                throw new BusinessException(storeRsp.getMessage());
            }
            AbcStoreRspDTO abcStoreRsp = storeRsp.getData();
            if(abcStoreRsp.getEnabled() == null || !abcStoreRsp.getEnabled()){
                throw new BusinessException(STORE_DISABLED);
            }
            //保存门店绑定关系
            GroupStoreBindDO groupStoreBindDO = new GroupStoreBindDO();
            groupStoreBindDO.setStoreGuid(storeBind.getStoreGuid());
            groupStoreBindDO.setPoiId(storeBind.getPoiId());
            groupStoreBindDO.setPoiName(abcStoreRsp.getName());
            groupStoreBindDO.setType(GroupBuyTypeEnum.ABC.getCode());
            groupStoreBindService.save(groupStoreBindDO);
        }catch (Exception e){
            log.error("绑定门店失败",e);
            throw new BusinessException(STORE_ERROR);
        }

    }

    @Override
    public List<MtCouponPreRespDTO> couponPrepare(CouPonPreReqDTO couPonPreReqDTO) {
        if(StringUtils.isEmpty(couPonPreReqDTO.getCouponCode())){
           throw new BusinessException("验券入参为空");
        }
        //查询门店是否绑定
        int count = groupStoreBindService.count(new LambdaQueryWrapper<GroupStoreBindDO>().eq(GroupStoreBindDO::getStoreGuid, couPonPreReqDTO.getStoreGuid())
                .eq(GroupStoreBindDO::getType, couPonPreReqDTO.getGroupBuyType()));
        if(count == 0){
            throw new BusinessException(Constant.COUPON_UN_BIND_STORE);
        }
        AbcCouponQueryReqDTO abcCouponQueryReq = new AbcCouponQueryReqDTO();
        abcCouponQueryReq.setCouponCode(couPonPreReqDTO.getCouponCode());
        abcCouponQueryReq.setTimestamp(System.currentTimeMillis());
        abcCouponQueryReq.setSign(getSign(abcCouponQueryReq.getTimestamp()));
        String rsp = postRequest(JSON.toJSONString(abcCouponQueryReq), groupBuyAbcConfig.host +groupBuyAbcConfig.couponQuery);
        AbcRspDTO<AbcCouponRspDTO> storeRsp = JSON.parseObject(rsp,new TypeReference<AbcRspDTO<AbcCouponRspDTO>>(){}.getType());
        if(!storeRsp.isSuccess()){
            throw new BusinessException(storeRsp.getMessage());
        }
        AbcCouponRspDTO couponRsp = storeRsp.getData();
        //判断券状态
        if(!couponRsp.isAvailable()){
            throw new BusinessException(COUPON_NOT_AVAILABLE);
        }
        //转化农行聚卡慧优惠券
        return GroupBuyConverter.toAbcCouponPreRespList(couponRsp,couPonPreReqDTO.getCouponCode());
    }

    @Override
    public List<GroupVerifyDTO> verifyCoupon(CouPonReqDTO couPonReq) {
        log.info("农行聚卡慧验券入参：{}",JacksonUtils.writeValueAsString(couPonReq));
        //需要判断验券信息是否为空
        if(CollUtil.isEmpty(couPonReq.getCouponCodeList())){
            throw new BusinessException("券码为空");
        }
        if(StringUtils.isEmpty(couPonReq.getErpId())){
            throw new BusinessException("验券门店不能为空");
        }
        //根据业务门店查询门店信息
        GroupStoreBindDO bindStore = groupStoreBindService.getOne(new LambdaQueryWrapper<GroupStoreBindDO>().eq(GroupStoreBindDO::getStoreGuid, couPonReq.getErpId())
                .eq(GroupStoreBindDO::getType, couPonReq.getGroupBuyType()));
        if(bindStore == null){
            throw new BusinessException(Constant.COUPON_UN_BIND_STORE);
        }
        AbcCouponVerifyReqDTO abcCouponVerifyReq = new AbcCouponVerifyReqDTO();
        abcCouponVerifyReq.setCouponCode(couPonReq.getCouponCodeList().get(0));
        abcCouponVerifyReq.setStoreId(Long.parseLong(bindStore.getPoiId()));
        abcCouponVerifyReq.setTimestamp(System.currentTimeMillis());
        abcCouponVerifyReq.setThirdPayNum(couPonReq.getErpOrderId());
        abcCouponVerifyReq.setOrderAmount(couPonReq.getOrderFee());
        abcCouponVerifyReq.setSign(getSign(abcCouponVerifyReq.getTimestamp()));
        String rsp = postRequest(JSON.toJSONString(abcCouponVerifyReq), groupBuyAbcConfig.host + groupBuyAbcConfig.couponVerify);
        log.info("农行聚卡慧验券返回信息：{}",rsp);
        //验券返回处理
        AbcRspDTO<String> storeRsp = JSON.parseObject(rsp,new TypeReference<AbcRspDTO<String>>(){}.getType());
        if(!storeRsp.isSuccess()){
            throw new BusinessException(storeRsp.getMessage());
        }
        List<GroupVerifyDTO> groupVerifylist = Lists.newArrayList();
        GroupVerifyDTO groupVerifyRsp = new GroupVerifyDTO();
        groupVerifyRsp.setCode(couPonReq.getCouponCodeList().get(0));
        groupVerifyRsp.setGroupBuyType(GroupBuyTypeEnum.ABC.getCode());
        groupVerifylist.add(groupVerifyRsp);
        return groupVerifylist;
    }

    @Override
    public MtDelCouponRespDTO revokeCoupon(GroupVerifyDTO revokeReq) {

        log.info("农行聚卡慧撤销验券入参,revokeReq:{}", JacksonUtils.writeValueAsString(revokeReq));
        if(StringUtils.isEmpty(revokeReq.getCode())){
            throw new BusinessException("撤销验券信息有误");
        }
        if(revokeReq.getGroupBuyType() == GroupBuyTypeEnum.ABC.getCode()){
            return MtDelCouponRespDTO.buildError("农行聚卡惠团购暂不支持撤销");
        }
        AbcCouponQueryReqDTO abcCouponRevokeReq = new AbcCouponQueryReqDTO();
        abcCouponRevokeReq.setCouponCode(revokeReq.getCode());
        abcCouponRevokeReq.setTimestamp(System.currentTimeMillis());
        abcCouponRevokeReq.setSign(getSign(abcCouponRevokeReq.getTimestamp()));
        String rsp = postRequest(JSON.toJSONString(abcCouponRevokeReq),groupBuyAbcConfig.host + groupBuyAbcConfig.couponCancel);
        AbcRspDTO<String> couponCancelRsp = JSON.parseObject(rsp,new TypeReference<AbcRspDTO<String>>(){}.getType());

        if(!couponCancelRsp.isSuccess()){
            return MtDelCouponRespDTO.buildError(couponCancelRsp.getMessage());
        }
        return MtDelCouponRespDTO.buildSuccess();
    }

    @Override
    public String getToken() {
       return null;
    }

    private String getSign(Long timeStamp){
        return DigestUtils.md5Hex("appId=" + groupBuyAbcConfig.appId + "&Param=" + timeStamp
                + "&AppSecretKey=" + DigestUtils.md5Hex(groupBuyAbcConfig.appSecret));
    }

    private String postRequest(String body,String url){
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
        headers.setAcceptCharset(Collections.singletonList(StandardCharsets.UTF_8));
        HttpEntity<String> entity = new HttpEntity<>(body, headers);
        ResponseEntity<String> stringResponseEntity;
        try {
            stringResponseEntity = groupBuyRestTemplate.postForEntity(url, entity, String.class);
        }catch (HttpClientErrorException e){
            String exceptionRsp = new String(e.getResponseBodyAsByteArray(), StandardCharsets.UTF_8);
            log.info("请求农行聚卡慧异常：{}",exceptionRsp);
            AbcRspDTO<String> abcRsp = JSON.parseObject(exceptionRsp,new TypeReference<AbcRspDTO<String>>(){}.getType());
            throw new BusinessException(abcRsp == null || abcRsp.getMessage() == null ? REMOTE_ERROR : abcRsp.getMessage());
        }
        return getRspBody(stringResponseEntity);

    }

    private String getRspBody(ResponseEntity<String> stringResponseEntity) {
        log.info("请求聚卡慧服务返回参数信息：{}",stringResponseEntity);
        if(ObjectUtil.isNull(stringResponseEntity)){
            throw new BusinessException(REMOTE_ERROR);
        }
        //响应状态
        HttpStatus statusCode = stringResponseEntity.getStatusCode();
        if(statusCode != HttpStatus.OK){
            throw new BusinessException(REMOTE_ERROR);
        }
        return stringResponseEntity.getBody();
    }


}
