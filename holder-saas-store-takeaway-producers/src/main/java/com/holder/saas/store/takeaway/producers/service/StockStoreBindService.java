package com.holder.saas.store.takeaway.producers.service;

import com.holder.saas.store.takeaway.producers.entity.domain.StoreBindOrderDO;
import com.holderzone.saas.store.dto.takeaway.request.StockStoreBindReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.StockStoreBindReqOrderDTO;
import com.holderzone.saas.store.dto.takeaway.response.StockStoreBindResqDTO;

/**
 * <AUTHOR>
 * @description 库存门店绑定
 * @date 2021/9/16
 */
public interface StockStoreBindService {
    
    StockStoreBindResqDTO bindStockStore(StockStoreBindReqDTO req);

    StockStoreBindResqDTO getBindStockStore(String storeGuid);

    void saveBindStockStoreOrder(StockStoreBindReqOrderDTO reqOrderDTO);

    StoreBindOrderDO getBindStockStoreOrder(String orderId, String branchStoreGuid);
}
