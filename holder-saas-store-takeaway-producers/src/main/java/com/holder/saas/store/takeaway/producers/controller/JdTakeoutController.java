package com.holder.saas.store.takeaway.producers.controller;

import com.holder.saas.store.takeaway.producers.service.JdAuthService;
import com.holder.saas.store.takeaway.producers.service.JdOrderService;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.takeaway.jd.BusinessOrderDTO;
import com.holderzone.saas.store.dto.takeaway.jd.CallBackDTO;
import com.holderzone.saas.store.dto.takeaway.request.StoreAuthByStoreReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.StoreAuthDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.w3c.dom.stylesheets.LinkStyle;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 京东秒送接口
 */
@Slf4j
@RestController
@RequestMapping("/jd")
@RequiredArgsConstructor
public class JdTakeoutController {

    private final JdAuthService jdAuthService;

    private final JdOrderService jdOrderService;

    @PostMapping("/token")
    public void callback(@RequestBody CallBackDTO callBackDTO) {
        log.info("京东秒送回调：{}", JacksonUtils.writeValueAsString(callBackDTO));
        jdAuthService.tokenSetting(callBackDTO);
    }

    @PostMapping(value = "/store/bind", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void doShopBindingJd(@RequestBody StoreAuthByStoreReqDTO storeAuthByStoreReqDTO) {
        jdAuthService.doBindStore(storeAuthByStoreReqDTO);
    }

    @PostMapping(value = "/store/unbind", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void doShopUnBindingJd(@RequestBody StoreAuthByStoreReqDTO storeAuthByStoreReqDTO) {
        jdAuthService.doUnbindStore(storeAuthByStoreReqDTO);
    }

    @PostMapping("/get_takeout_auth")
    public StoreAuthDTO getTakeoutAuth(@RequestBody StoreAuthDTO storeAuthDTO) {
        return jdAuthService.getTakeoutAuth(storeAuthDTO);
    }

    @PostMapping("/order_callback")
    public void orderCallback(@RequestBody BusinessOrderDTO businessOrder) {
        jdOrderService.orderCallback(businessOrder);
    }

    @PostMapping("/auth_brand")
    public List<String> authBrand(@RequestBody List<String> brandGuidList) {
        return jdAuthService.authBrand(brandGuidList);
    }

}
