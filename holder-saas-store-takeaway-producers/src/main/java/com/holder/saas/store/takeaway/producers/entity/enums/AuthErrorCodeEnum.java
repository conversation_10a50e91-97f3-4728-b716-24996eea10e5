package com.holder.saas.store.takeaway.producers.entity.enums;

import com.google.common.collect.Lists;
import lombok.Getter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023年09月08日 18:21
 * @description 授权错误码
 */
@Getter
public enum AuthErrorCodeEnum {

    NOT_EXIST_OR_EXPIRE("10009","授权不存在或已过期, 请联系商家进行授权"),

    ILLEGAL("30001","非法更新令牌"),

    UPDATE_TOKEN_EXPIRE("30002","更新令牌已过期"),
    ;

    private final String code;

    private final String desc;

    AuthErrorCodeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static List<String> allError(){
        List<String> list = Lists.newArrayList();
        list.add(NOT_EXIST_OR_EXPIRE.code);
        list.add(ILLEGAL.code);
        list.add(UPDATE_TOKEN_EXPIRE.code);
        return list;
    }

}
