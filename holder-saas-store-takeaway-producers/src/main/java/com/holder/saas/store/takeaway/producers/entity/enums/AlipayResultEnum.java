package com.holder.saas.store.takeaway.producers.entity.enums;

/**
 * <AUTHOR>
 * @date 2023/11/16
 * @description 支付宝核销返回结果枚举
 */
public enum AlipayResultEnum {

    /**
     * 核销成功
     */
    SUCCESS,

    /**
     * 凭证不是可核销状态
     */
    CERTIFICATE_CANNOT_USE,

    /**
     * 商品不在可用商品范围内
     */
    NOT_IN_AVAILABLE_ITEM,

    /**
     * 核销门店不在可用门店中
     */
    NOT_IN_AVAILABLE_SHOP,

    /**
     * 核销时间不在可用时间区间范围内
     */
    NOT_IN_AVAILABLE_TIME,

    /**
     * 剩余可核销次数不足
     */
    AVAILABLE_COUNT_NOT_ENOUGH,

    /**
     * 核销次数超过限制
     */
    USE_COUNT_EXCEED_LIMIT,

    /**
     * 凭证不归属于该商户
     */
    CERTIFICATE_MERCHANT_NOT_PERMISSION,;
}
