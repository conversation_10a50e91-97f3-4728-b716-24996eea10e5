package com.holder.saas.store.takeaway.producers.service;

import com.holderzone.saas.store.dto.takeaway.MtCallbackConsumeDTO;
import com.holderzone.saas.store.dto.takeaway.MtCallbackMemberDTO;
import com.holderzone.saas.store.dto.takeaway.RefreshTokenMemberDTO;

/**
 * <AUTHOR>
 * @create 2023-08-29
 * @description 美团到餐品牌会员对接服务
 */
public interface MtBrandMemberService {

    boolean judgeAndMember(MtCallbackMemberDTO mtCallbackDTO);

    void saveConsumeRecord(MtCallbackConsumeDTO mtCallbackDTO);

    void memberRefreshToken();

}
