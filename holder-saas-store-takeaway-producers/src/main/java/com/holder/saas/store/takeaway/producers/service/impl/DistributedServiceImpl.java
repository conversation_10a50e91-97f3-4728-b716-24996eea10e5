package com.holder.saas.store.takeaway.producers.service.impl;

import com.holder.saas.store.takeaway.producers.service.DistributedService;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.sdk.util.BatchIdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DistributedServiceImpl
 * @date 2018/02/14 09:00
 * @description 分布式id服务实现类
 * @program holder-saas-store-print
 */
@Slf4j
@Service
public class DistributedServiceImpl implements DistributedService {

    private static final String TAG_TAKEOUT_ELE = "takeout/ele";

    private static final String TAG_TAKEOUT_MT = "takeout/mt";

    private static final String TAG_TAKEOUT_OWN = "takeout/own";

    private static final String TAG_TAKEOUT_ZC = "takeout/zc";

    private static final String TAG_TAKEOUT_ALIPAY = "takeout/alipay";

    private final RedisTemplate redisTemplate;

    @Autowired
    public DistributedServiceImpl(RedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    @Override
    public String nextId(String tag) {
        try {
            return String.valueOf(BatchIdGenerator.getGuid(redisTemplate, tag));
        } catch (IOException e) {
            throw new BusinessException("生成Guid失败：" + e.getMessage());
        }
    }

    @Override
    public List<String> nextBatchId(String tag, long count) {
        return BatchIdGenerator.batchGetGuids(redisTemplate, tag, count)
                .stream().map(String::valueOf).collect(Collectors.toList());
    }

    @Override
    public String nextEleGuid() {
        return nextId(TAG_TAKEOUT_ELE);
    }

    @Override
    public String nextMtGuid() {
        return nextId(TAG_TAKEOUT_MT);
    }

    @Override
    public String nextOwnGuid() {
        return nextId(TAG_TAKEOUT_OWN);
    }

    @Override
    public String nextZcGuid() {
        return nextId(TAG_TAKEOUT_ZC);
    }

    @Override
    public String nextAliPayGuid() {
        return nextId(TAG_TAKEOUT_ALIPAY);
    }
}
