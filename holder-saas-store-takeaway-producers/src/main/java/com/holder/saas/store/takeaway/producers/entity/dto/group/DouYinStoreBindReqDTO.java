package com.holder.saas.store.takeaway.producers.entity.dto.group;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.google.common.collect.Lists;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-06-19
 * @description
 */
@Data
public class DouYinStoreBindReqDTO {

    private List<DouYinStoreBindReq> datas;

    @Data
    static
    class DouYinStoreBindReq{
        /**
         * 商家自己的门店id
         */
        @JSONField(name = "ext_id")
        private String extId;

        @JSONField(name = "poi_id")
        private String poiId;

        @JSONField(name = "poi_name")
        private String poiName;

        private String address;

        private String latitude;

        private String longitude;
    }




    public static String buildJsonString(DouYinPoi.Poi poi, String storeGuid) {
        DouYinStoreBindReqDTO douYinStoreBindReq = new DouYinStoreBindReqDTO();
        DouYinStoreBindReq storeBindReq = new DouYinStoreBindReq();
        storeBindReq.setExtId(storeGuid);
        storeBindReq.setPoiId(poi.getPoiId());
        storeBindReq.setPoiName(poi.getPoiName());
        storeBindReq.setAddress(poi.getAddress());
        storeBindReq.setLatitude(poi.getLatitude().toString());
        storeBindReq.setLongitude(poi.getLongitude().toString());
        douYinStoreBindReq.setDatas(Lists.newArrayList(storeBindReq));
        return JSON.toJSONString(douYinStoreBindReq);
    }
}
