package com.holder.saas.store.takeaway.producers.utils.sdk.meituan;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.holder.saas.store.takeaway.producers.constant.TakeoutConstant;
import com.sankuai.sjst.platform.developer.domain.RequestDomain;
import com.sankuai.sjst.platform.developer.utils.SignUtils;
import com.sankuai.sjst.platform.developer.utils.WebUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.NameValuePair;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.message.BasicNameValuePair;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @date 2023/9/8 16:33
 * @description 更新授权令牌
 */
@Data
@Slf4j
public class CipCaterRefreshTokenRequest {

    private String timestamp;

    private String url;

    private String businessId;

    private String signKey;

    private String developerId;

    private String refreshToken;

    /**
     * 固定为 "refresh_token"
     */
    private static final String GRANT_TYPE = "refresh_token";

    /**
     * 固定为 "all"
     */
    private static final String SCOPE = "all";

    public CipCaterRefreshTokenRequest() {

    }

    public CipCaterRefreshTokenRequest(String businessId, String developerId, String signKey, String refreshToken) {
        this.url = RequestDomain.preUrl.getValue() + "/oauth/refresh";
        this.timestamp = (new Timestamp((new Date()).getTime())).toString();
        this.signKey = signKey;
        this.businessId = businessId;
        this.developerId = developerId;
        this.refreshToken = refreshToken;
    }

    public String doPost() {
        List<NameValuePair> paramsInUrl = Lists.newArrayList();
        paramsInUrl.add(new BasicNameValuePair("timestamp", this.timestamp));
        paramsInUrl.add(new BasicNameValuePair("scope", SCOPE));
        paramsInUrl.add(new BasicNameValuePair("refreshToken", this.refreshToken));
        paramsInUrl.add(new BasicNameValuePair("grantType", GRANT_TYPE));
        paramsInUrl.add(new BasicNameValuePair("developerId", this.developerId));
        paramsInUrl.add(new BasicNameValuePair("charset", TakeoutConstant.CHARSET_UTF_8));
        paramsInUrl.add(new BasicNameValuePair("businessId", this.businessId));
        paramsInUrl.add(new BasicNameValuePair("sign", getSign()));
        try {
            String finalUrl = (new URIBuilder()).setParameters(paramsInUrl).setPath(this.url).build().toString();
            return WebUtils.post(finalUrl, null);
        } catch (Exception e) {
            log.error("[更新授权令牌]失败", e);
        }
        return null;
    }

    public String getSign() {
        Map<String, String> params = Maps.newHashMap();
        params.put("businessId", this.businessId);
        params.put("charset", TakeoutConstant.CHARSET_UTF_8);
        params.put("developerId", this.developerId);
        params.put("timestamp", this.timestamp);
        params.put("refreshToken", this.refreshToken);
        params.put("scope", SCOPE);
        params.put("grantType", GRANT_TYPE);
        return SignUtils.createSign(this.signKey, params);
    }

}
