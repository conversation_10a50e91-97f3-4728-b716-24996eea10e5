package com.holder.saas.store.takeaway.producers.entity.enums;

import com.holderzone.framework.exception.unchecked.BusinessException;

public enum MtPathEnum {

    /**
     * 美团新订单
     */
    FIRST("first", "美团新订单"),

    /**
     * 美团取消订单
     */
    CANCEL("cancel", "美团取消订单"),

    /**
     * 美团退单/退款
     */
    REFUND("refund", "美团退单/退款"),

    /**
     * 美团部分退单/退款
     */
    PART_REFUND("part_refund", "美团部分退单/退款"),

    /**
     * 美团接单
     */
    CONFIRM("confirm", "美团接单"),

    // todo 将 over 改为 finish：dev, uat美团回调需同步修改
    FINISH("over", "美团订单完成"),

    /**
     * 美团订单配送
     */
    SHIPPING("shipping", "美团订单配送");

    private String path;

    private String desc;

    MtPathEnum(String path, String desc) {
        this.path = path;
        this.desc = desc;
    }

    public String getPath() {
        return path;
    }

    public String getDesc() {
        return desc;
    }

    public static MtPathEnum ofPath(String path) {
        for (MtPathEnum mtPathEnum : values()) {
            if (mtPathEnum.getPath().equalsIgnoreCase(path)) {
                return mtPathEnum;
            }
        }
        throw new BusinessException("无效的美团回调Path: " + path);
    }
}
