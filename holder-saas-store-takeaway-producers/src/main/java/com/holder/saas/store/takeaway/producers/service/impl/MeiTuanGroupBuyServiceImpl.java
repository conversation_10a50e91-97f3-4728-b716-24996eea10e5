package com.holder.saas.store.takeaway.producers.service.impl;

import com.holder.saas.store.takeaway.producers.service.GroupBuyService;
import com.holder.saas.store.takeaway.producers.service.MtCouponService;
import com.holder.saas.store.takeaway.producers.service.converter.GroupBuyConverter;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.business.group.StoreBindDTO;
import com.holderzone.saas.store.dto.order.response.groupon.GroupVerifyDTO;
import com.holderzone.saas.store.dto.takeaway.request.CouPonPreReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.CouPonReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.CouponDelReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.MtCouponReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtCouponDoCheckRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtCouponPreRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtDelCouponRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import java.util.List;


/**
 * <AUTHOR>

 */
@Service("meiTuanGroupBuyServiceImpl")
@Slf4j
public class MeiTuanGroupBuyServiceImpl implements GroupBuyService {

    private final MtCouponService mtProdCouponServiceImpl;

    @Autowired
    public MeiTuanGroupBuyServiceImpl(@Qualifier("mtProdCouponServiceImpl")MtCouponService mtProdCouponServiceImpl) {
        this.mtProdCouponServiceImpl = mtProdCouponServiceImpl;
    }


    @Override
    public void bindStore(StoreBindDTO storeBind) {
        throw new BusinessException("不存在绑定门店服务");
    }

    @Override
    public String getToken() {
        return null;
    }

    @Override
    public List<MtCouponPreRespDTO> couponPrepare(CouPonPreReqDTO couPonPreReqDTO) {
        MtCouponReqDTO mtCouponReqDTO = GroupBuyConverter.mtPreReqFromPreReq(couPonPreReqDTO);
        MtCouponPreRespDTO mtCouponPreRespDTO = mtProdCouponServiceImpl.preCheck(mtCouponReqDTO);
        return GroupBuyConverter.toMtCouponPreRespList(couPonPreReqDTO, mtCouponPreRespDTO);
    }

    @Override
    public List<GroupVerifyDTO> verifyCoupon(CouPonReqDTO couPonReq) {
        log.info("美团验券入参:{}", JacksonUtils.writeValueAsString(couPonReq));
        MtCouponReqDTO mtCouponReqDTO = GroupBuyConverter.mtCouPonReqFromCouPonReq(couPonReq);
        MtCouponDoCheckRespDTO mtCouponDoCheckResp = mtProdCouponServiceImpl.doCheck(mtCouponReqDTO);
        return GroupBuyConverter.toMtCouponVerifyRespList(couPonReq, mtCouponDoCheckResp);
    }

    @Override
    public MtDelCouponRespDTO revokeCoupon(GroupVerifyDTO revokeReq) {
        log.info("美团撤销券入参:{}", JacksonUtils.writeValueAsString(revokeReq));
        CouponDelReqDTO couponDelReqDTO = GroupBuyConverter.mtRevokeFromRevokeReq(revokeReq);
        MtDelCouponRespDTO mtDelCouponRespDTO = mtProdCouponServiceImpl.cancelTicket(couponDelReqDTO);
        if(mtDelCouponRespDTO == null || mtDelCouponRespDTO.getResult() != 0){
            throw new BusinessException("撤销美团券失败");
        }
        return MtDelCouponRespDTO.buildSuccess();
    }

}
