package com.holder.saas.store.takeaway.producers.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * https://developer.meituan.com/openapi#7.3.18
 */
@Data
@ApiModel
@NoArgsConstructor
public class MtCbPrivacyDTO implements Serializable {

    private static final long serialVersionUID = -1829724116405943472L;

    @ApiModelProperty(value = "数据，字符串的Json列表")
    private String data;

    @ApiModelProperty(value = "错误，字符串的Json对象")
    private String error;

    @Data
    @ApiModel
    @NoArgsConstructor
    public static class MtCbErrorDetail implements Serializable {

        private static final long serialVersionUID = -2089792546832677598L;

        @ApiModelProperty(value = "错误码")
        private String code;

        @ApiModelProperty(value = "错误类型")
        private String error_type;

        @ApiModelProperty(value = "错误消息")
        private String message;

        @ApiModelProperty(value = "错误消息")
        private String msg;
    }

    @Data
    @ApiModel
    @NoArgsConstructor
    public static class MtCbPrivacyDetail implements Serializable {

        private static final long serialVersionUID = 6425774236919906089L;

        @ApiModelProperty(value = "门店下的订单流水号")
        private long daySeq;

        @ApiModelProperty(value = "ERP门店ID")
        private String ePoiId;

        @ApiModelProperty(value = "操作结果")
        private long orderId;

        @ApiModelProperty(value = "订单展示号")
        private long orderIdView;

        @ApiModelProperty(value = "真实手机号")
        private String realPhoneNumber;
    }
}
