package com.holder.saas.store.takeaway.producers.mapstruct;

import com.holder.saas.store.takeaway.producers.entity.domain.HolderAuthDO;
import com.holderzone.saas.store.dto.takeaway.response.HolderAuthDTO;
import org.mapstruct.Mapper;
import org.springframework.stereotype.Component;

@Component
@Mapper(componentModel = "spring")
public interface HolderMapstruct {

    HolderAuthDTO toHolderAuthDTO(HolderAuthDO holderAuthDO);
}
