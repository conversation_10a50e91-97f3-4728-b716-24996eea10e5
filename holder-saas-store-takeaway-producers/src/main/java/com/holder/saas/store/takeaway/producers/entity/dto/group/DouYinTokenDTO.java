package com.holder.saas.store.takeaway.producers.entity.dto.group;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.annotation.JSONField;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.exception.GroupBuyException;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023-06-19
 * @description
 */
@Data
public class DouYinTokenDTO extends DouYinCommonDTO{

    /**
     * token
     */
    @JSONField(name = "access_token")
    private String accessToken;

    /**
     * token过期时间单位S
     */
    @JSONField(name = "expires_in")
    private Integer expiresIn;

    public static DouYinTokenDTO parseJson(String rsp) {
        DouYinRspDTO<DouYinTokenDTO> douYinToken = JSONObject.parseObject(rsp,new TypeReference<DouYinRspDTO<DouYinTokenDTO>>(){}.getType());
        if (douYinToken == null || douYinToken.getData() == null){
            throw new GroupBuyException("抖音请求token返回为null");
        }
        if(douYinToken.getData().getErrorCode() != 0){
            throw new GroupBuyException(douYinToken.getData().getDescription());
        }
        return douYinToken.getData();
    }
}
