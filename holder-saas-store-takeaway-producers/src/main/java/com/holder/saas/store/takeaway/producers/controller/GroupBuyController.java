package com.holder.saas.store.takeaway.producers.controller;


import com.holder.saas.store.takeaway.producers.service.GroupStoreBindService;
import com.holder.saas.store.takeaway.producers.service.factory.GroupBuyFactory;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.business.group.StoreBindDTO;
import com.holderzone.saas.store.dto.exception.GroupBuyException;
import com.holderzone.saas.store.dto.order.response.groupon.GroupVerifyDTO;
import com.holderzone.saas.store.dto.takeaway.request.CouPonPreReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.CouPonReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtCouponPreRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtDelCouponRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.StoreAuthDTO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-06-16
 * @description 团购管理
 */
@Slf4j
@RestController
@RequestMapping("/group")
@AllArgsConstructor
public class GroupBuyController {

    private final GroupBuyFactory groupBuyFactory;

    private final GroupStoreBindService groupStoreBindService;

    @PostMapping("/store/bind")
    public Result<?> bindStore(@RequestBody StoreBindDTO storeBind){
        try {
            groupBuyFactory.build((storeBind.getGroupBuyType())).bindStore(storeBind);
            return Result.buildEmptySuccess();
        }catch (Exception e){
            log.error("绑定团购门店异常，入参：{}",JacksonUtils.writeValueAsString(storeBind),e);
            return Result.buildFailResult(-1,e.getMessage());
        }

    }

    @PostMapping("/store/unbind")
    public Result<?> unbindStore(@RequestBody StoreBindDTO storeBind){
        try {
            groupStoreBindService.unbindStore(storeBind);
            return Result.buildEmptySuccess();
        }catch (GroupBuyException e){
            return Result.buildFailResult(-1,e.getMessage());
        }
    }

    @GetMapping("/list/{storeGuid}")
    public List<StoreAuthDTO> query(@PathVariable("storeGuid") String storeGuid){
       return groupStoreBindService.listAllByStoreGuid(storeGuid);
    }

    /**
     * 预验券
     * @param couPonPreReqDTO 预验券入参
     */
    @PostMapping("/coupon/prepare")
    public List<MtCouponPreRespDTO> couponPrepare(@RequestBody CouPonPreReqDTO couPonPreReqDTO){
        return groupBuyFactory.build((couPonPreReqDTO.getGroupBuyType())).couponPrepare(couPonPreReqDTO);
    }

    /**
     * 验券
     */
    @PostMapping("/coupon/verify")
    public List<GroupVerifyDTO> verifyCoupon(@RequestBody CouPonReqDTO couPonReq){
        return groupBuyFactory.build(couPonReq.getGroupBuyType()).verifyCoupon(couPonReq);
    }

    /**
     * 撤销验券
     */
    @PostMapping("/coupon/revoke")
    public MtDelCouponRespDTO revokeCoupon(@RequestBody GroupVerifyDTO revokeReq) {
        try {
            return groupBuyFactory.build(revokeReq.getGroupBuyType()).revokeCoupon(revokeReq);
        } catch (Exception e) {
            log.error("团购撤销验券失败：revokeReq={}", JacksonUtils.writeValueAsString(revokeReq), e);
            return MtDelCouponRespDTO.buildError(e.getMessage());
        }
    }
}
