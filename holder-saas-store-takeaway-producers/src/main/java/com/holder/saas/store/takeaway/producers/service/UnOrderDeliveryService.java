package com.holder.saas.store.takeaway.producers.service;

import com.holderzone.saas.store.dto.takeaway.OwnApiResult;
import com.holderzone.saas.store.dto.takeaway.UnOrder;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderRocketListener
 * @date 2018/11/15 12:00
 * @description UnOrder回复平台服务
 * @program holder-saas-store-takeaway
 */
public interface UnOrderDeliveryService {


    /**
     * 一城飞客发起配送
     *
     * @param unOrder
     */
    void startDelivery(UnOrder unOrder);

    /**
     * 一城飞客发起配送
     *
     * @param unOrder
     */
    OwnApiResult startDeliveryMQ(UnOrder unOrder);

    /**
     * 一城飞客取消配送
     *
     * @param unOrder
     */
    void cancelDelivery(UnOrder unOrder);

    /**
     * 商家回复已接单
     *
     * @param unOrder
     */
    void replyDeliveryAccept(UnOrder unOrder);

    /**
     * 商家回复已送出
     *
     * @param unOrder
     */
    void replyDeliveryStart(UnOrder unOrder);

    /**
     * 商家回复已取消
     *
     * @param unOrder
     */
    void replyDeliveryCancel(UnOrder unOrder);

    /**
     * 商家回复已送达
     *
     * @param unOrder
     */
    void replyDeliveryComplete(UnOrder unOrder);


    /**
     * 更新骑手位置信息
     *
     * @param unOrder
     */
   void replyRiderPosition(UnOrder unOrder);

}
