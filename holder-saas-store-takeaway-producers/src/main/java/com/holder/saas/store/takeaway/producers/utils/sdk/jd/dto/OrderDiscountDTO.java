package com.holder.saas.store.takeaway.producers.utils.sdk.jd.dto;

import lombok.Data;
import lombok.Getter;

@Data
public class OrderDiscountDTO {

    /**
     * 订单主表订单id
     */
    private Long orderId;

    /**
     * 优惠类型(1:优惠码;2：单品促销；3:优惠劵;4:满减;5:满折;6:首单优惠;7：VIP会员免基础运费；8:商家满免运费;10:满件减;11:满件折;
     * 12:首单地推满免运费;15:运费券;16:单品免运;17：合约机优惠；18:满免基础运费;19:包装费优惠;20:餐盒费优惠)
     */
    private Integer discountType;

    /**
     * 优惠金额
     */
    private Long discountPrice;

    public String getDiscountName(){
        return Discount.typName(this.discountType);
    }

    @Getter
    public enum Discount{
        COUPON_CODE(1, "优惠码"),
        SINGLE_ITEM_PROMOTION(2, "单品促销"),
        COUPON(3, "优惠券"),
        FULL_REDUCTION(4, "满减"),
        FULL_DISCOUNT(5, "满折"),
        FIRST_ORDER_DISCOUNT(6, "首单优惠"),
        VIP_FREE_BASIC_DELIVERY(7, "VIP会员免基础运费"),
        MERCHANT_FULL_FREE_DELIVERY(8, "商家满免运费"),
        FULL_QUANTITY_REDUCTION(10, "满件减"),
        FULL_QUANTITY_DISCOUNT(11, "满件折"),
        FIRST_ORDER_PROMOTION_FREE_DELIVERY(12, "首单地推满免运费"),
        DELIVERY_COUPON(15, "运费券"),
        SINGLE_ITEM_FREE_DELIVERY(16, "单品免运"),
        CONTRACT_MACHINE_DISCOUNT(17, "合约机优惠"),
        FULL_FREE_BASIC_DELIVERY(18, "满免基础运费"),
        PACKAGING_FEE_DISCOUNT(19, "包装费优惠"),
        MEAL_BOX_FEE_DISCOUNT(20, "餐盒费优惠");

        final int code;
        final String name;

        Discount(int code, String name) {
            this.code = code;
            this.name = name;
        }

        public static String typName(Integer code){
            for(Discount discount : Discount.values()){
                if(discount.code == code){
                    return discount.name;
                }
            }
            return null;
        }
    }
}
