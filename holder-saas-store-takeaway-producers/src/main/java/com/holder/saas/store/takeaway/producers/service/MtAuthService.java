package com.holder.saas.store.takeaway.producers.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.holder.saas.store.takeaway.producers.entity.domain.MtAuthDO;
import com.holder.saas.store.takeaway.producers.entity.dto.MtAuthDTO;
import com.holderzone.saas.store.dto.takeaway.MtAuthBindUrlDTO;
import com.holderzone.saas.store.dto.takeaway.MtCallbackDTO;
import com.holderzone.saas.store.dto.takeaway.TokenDTO;
import com.holderzone.saas.store.dto.takeaway.request.GroupBuyShopBindReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutItemBindReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutShopBindReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.GroupBuyShopBindRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.StoreAuthDTO;
import com.holderzone.saas.store.dto.takeaway.response.TakeoutItemBindRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.TakeoutShopBindRespDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderRocketListener
 * @date 2018/11/15 12:00
 * @description 美团身份认证服务
 * @program holder-saas-store-takeaway
 */
public interface MtAuthService extends IService<MtAuthDO> {

    TakeoutShopBindRespDTO takeOutBindingUrl(TakeoutShopBindReqDTO takeoutShopBindReqDTO);

    GroupBuyShopBindRespDTO groupBuyBindingUrl(GroupBuyShopBindReqDTO groupBuyShopBindReqDTO);

    TakeoutItemBindRespDTO itemBindingUrl(TakeoutItemBindReqDTO takeoutItemBindReqDTO);

    void bind(MtCallbackDTO mtCallbackDTO);

    void unbind(MtCallbackDTO mtCallbackDTO);

    MtAuthDO getAuth(String ePoiId, int businessId);

    List<MtAuthDO> getAuths(List<String> ePoiIds, int businessId);

    TokenDTO getToken(String ePoiId, int businessId);

    TokenDTO getToken(GroupBuyShopBindReqDTO groupBuyShopBindReqDTO);

    TokenDTO getToken(TakeoutShopBindReqDTO takeoutShopBindReqDTO);

    MtAuthDO getAuthByBizCode(String opBizCode, int businessId);

    void deleteAuth(String ePoiId, int businessId);

    void deleteAuthByMt(String bizCode, int businessId);

    void correctAuth(String storeGuid, int businessId, String errorCode);

    List<StoreAuthDTO> listAuth(List<StoreAuthDTO> storeAuthorizationDTOList);

    StoreAuthDTO getTakeoutAuth(StoreAuthDTO storeAuthDTO);

    Boolean updateDelivery(StoreAuthDTO storeAuthDTO);

    StoreAuthDTO getTuanGouAuth(StoreAuthDTO storeAuthDTO);

    void saveCallbackAuth(MtAuthDTO mtAuthDTO);

    String getMtAuthBindUrl(MtAuthBindUrlDTO authBindUrl);

    List<MtAuthBindUrlDTO> getMtBindAuth(String data);
}
