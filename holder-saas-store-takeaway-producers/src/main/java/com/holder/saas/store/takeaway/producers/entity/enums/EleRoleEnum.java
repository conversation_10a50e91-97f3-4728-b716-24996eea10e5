package com.holder.saas.store.takeaway.producers.entity.enums;

/**
 * role角色定义如下
 * <p>
 * https://nest-fe.faas.ele.me/openapi/documents/callback
 */
public enum EleRoleEnum {

    /**
     * 下单用户
     */
    下单用户(1, "下单用户"),

    饿了么系统(2, "饿了么系统"),

    饿了么商户(3, "饿了么商户"),

    饿了么客服(4, "饿了么客服"),

    饿了么开放平台系统(5, "饿了么开放平台系统"),

    饿了么短信系统(6, "饿了么短信系统"),

    饿了么无线打印机系统(7, "饿了么无线打印机系统"),

    饿了么风控系统(8, "饿了么风控系统"),

    饿了么订单完结(9, "饿了么订单完结");

    private int role;

    private String desc;

    EleRoleEnum(int role, String desc) {
        this.role = role;
        this.desc = desc;
    }

    public int getRole() {
        return role;
    }

    public String getDesc() {
        return desc;
    }

    public static EleRoleEnum byRole(int role) {
        for (EleRoleEnum value : values()) {
            if (value.role == role) {
                return value;
            }
        }
        throw new IllegalArgumentException("无效的饿了么角色role[" + role + "]");
    }
}
