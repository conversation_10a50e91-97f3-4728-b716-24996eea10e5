package com.holder.saas.store.takeaway.producers.controller;

import com.alibaba.fastjson.JSONObject;
import com.google.gson.JsonObject;
import com.holder.saas.store.takeaway.producers.config.MtCallbackValidator;
import com.holder.saas.store.takeaway.producers.service.MtAuthService;
import com.holder.saas.store.takeaway.producers.service.MtBrandMemberService;
import com.holder.saas.store.takeaway.producers.service.MtCallbackService;
import com.holder.saas.store.takeaway.producers.service.MtCouponService;
import com.holder.saas.store.takeaway.producers.utils.SpringContextUtil;
import com.holder.saas.store.takeaway.producers.utils.ThrowableExtUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.takeaway.*;
import com.holderzone.saas.store.dto.takeaway.request.CouponDelReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.MtCouponReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutShopBindReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.*;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 根据官方文档，回调Method是Post的表单提交
 * 然而该Controller却使用RequestBody，这是因为在聚合层做了表单提交的解析，然后使用RequestBody的Feign调用
 * <p>
 * 美团
 * 订单回调（订单处理）
 * 隐私号降级回调
 * 绑定回调
 * 解绑回调
 * 心跳回调
 * 查询美团授权列表（一个，批量，团购）
 * 查询美团Token
 * 验券相关操作
 */
@Slf4j
@RestController
@RequestMapping("/mt")
public class MeiTuanController {

    private final MtCallbackValidator mtCallbackValidator;

    private final MtCallbackService mtCallbackService;

    private final MtAuthService mtAuthService;

    private final MtCouponService mtDevCouponServiceImpl;

    private final MtCouponService mtProdCouponServiceImpl;

    private final MtBrandMemberService mtBrandMemberService;

    @Autowired
    public MeiTuanController(MtCallbackValidator mtCallbackValidator, MtCallbackService mtCallbackService,
                             MtAuthService mtAuthService,
                             @Qualifier("mtDevCouponServiceImpl") MtCouponService mtDevCouponServiceImpl,
                             @Qualifier("mtProdCouponServiceImpl") MtCouponService mtProdCouponServiceImpl, MtBrandMemberService mtBrandMemberService) {
        this.mtCallbackValidator = mtCallbackValidator;
        this.mtCallbackService = mtCallbackService;
        this.mtAuthService = mtAuthService;
        this.mtDevCouponServiceImpl = mtDevCouponServiceImpl;
        this.mtProdCouponServiceImpl = mtProdCouponServiceImpl;
        this.mtBrandMemberService = mtBrandMemberService;
    }

    @PostMapping("/callback/order/{path}")
    @ApiOperation(value = "美团订单回调", notes = "回调推送")
    public MtCallbackResponse orderCallback(@RequestBody MtCallbackDTO mtCallbackDTO,
                                            @PathVariable("path") String path) {
        if (log.isInfoEnabled()) {
            log.info("(美团)订单回调，path：{}, 入参：{}", path, JacksonUtils.writeValueAsString(mtCallbackDTO));
        }


        if (!mtCallbackValidator.checkSignature(mtCallbackDTO)) {
            log.info("(美团)订单回调，签名不匹配");
            return MtCallbackResponse.SIGNATURE_ERROR;
        }

        try {
            mtCallbackService.orderCallback(mtCallbackDTO, path);
        } catch (Exception e) {
            e.printStackTrace();
            if (log.isErrorEnabled()) {
                log.error("(美团)订单回调，处理异常：{}", ThrowableExtUtils.asStringIfAbsent(e));
            }

            return MtCallbackResponse.UNKNOWN_ERROR;
        }

        return MtCallbackResponse.OK;
    }

    @PostMapping("/callback/privacy_degrade")
    @ApiOperation(value = "隐私号降级回调", notes = "隐私号降级回调")
    public MtCallbackResponse privacyDegradeCallback(@RequestBody @Validated(MtCallbackDTO.PrivacyDegrade.class) MtCallbackDTO mtCallbackDTO) {
        if (log.isInfoEnabled()) {
            log.info("隐私号降级回调入参：{}", JacksonUtils.writeValueAsString(mtCallbackDTO));
        }
        if (!mtCallbackValidator.checkSignature(mtCallbackDTO)) {
            log.info("(美团)回调签名不匹配");
            return MtCallbackResponse.SIGNATURE_ERROR;
        }
        mtCallbackService.orderPrivacyDegrade(mtCallbackDTO);

        return MtCallbackResponse.OK;
    }

    @PostMapping("/callback/bind")
    @ApiOperation(value = "门店绑定回调", notes = "门店绑定回调")
    public MtCallbackResponse bind(@RequestBody @Validated(MtCallbackDTO.Bind.class) MtCallbackDTO mtCallbackDTO) {
        if (log.isInfoEnabled()) {
            log.info("(美团)绑定回调，入参：{}", JacksonUtils.writeValueAsString(mtCallbackDTO));
        }

        // 绑定回调，美团没有返回sign签名字段，无需签名验证

        try {
            mtAuthService.bind(mtCallbackDTO);
        } catch (Exception e) {
            if (log.isErrorEnabled()) {
                log.error("(美团)绑定回调，处理异常：{}", ThrowableExtUtils.asStringIfAbsent(e));
            }
            return MtCallbackResponse.UNKNOWN_ERROR;
        }

        return MtCallbackResponse.SUCCESS;
    }

    @PostMapping("/callback/unbind")
    @ApiOperation(value = "门店解绑回调", notes = "门店解绑回调")
    public MtCallbackResponse unbind(@RequestBody @Validated(MtCallbackDTO.Unbind.class) MtCallbackDTO mtCallbackDTO) {
        if (log.isInfoEnabled()) {
            log.info("(美团)解绑回调，入参：{}", JacksonUtils.writeValueAsString(mtCallbackDTO));
        }

        // 解绑回调，美团没有返回sign签名字段，无需签名验证

        try {
            mtAuthService.unbind(mtCallbackDTO);
        } catch (Exception e) {
            if (log.isErrorEnabled()) {
                log.error("(美团)解绑回调，处理异常：{}", ThrowableExtUtils.asStringIfAbsent(e));
            }
            return MtCallbackResponse.UNKNOWN_ERROR;
        }

        return MtCallbackResponse.SUCCESS;
    }

    @PostMapping("/callback/heartbeat")
    @ApiOperation(value = "心跳回调", notes = "心跳回调")
    public MtCallbackResponse heartbeatCallback() {
        if (log.isInfoEnabled()) {
            log.info("----------心跳回调----------");
        }

        return MtCallbackResponse.OK;
    }

    @PostMapping("/list_auth")
    @ApiOperation(value = "查询美团授权列表", notes = "查询美团授权列表")
    public List<StoreAuthDTO> listAuth(
            @RequestBody @Validated List<StoreAuthDTO> storeAuthorizationDTOList) {
        if (log.isInfoEnabled()) {
            log.info("查询美团授权列表入参：{}", JacksonUtils.writeValueAsString(storeAuthorizationDTOList));
        }
        return mtAuthService.listAuth(storeAuthorizationDTOList);
    }

    @PostMapping("/get_takeout_auth")
    @ApiOperation(value = "查询美团外卖授权列表", notes = "查询美团外卖授权列表")
    public StoreAuthDTO getTakeoutAuth(@RequestBody @Validated StoreAuthDTO storeAuthDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询美团外卖授权列表入参：{}", JacksonUtils.writeValueAsString(storeAuthDTO));
        }
        return mtAuthService.getTakeoutAuth(storeAuthDTO);
    }

    @PostMapping("/get_tuangou_auth")
    @ApiOperation(value = "查询美团外卖授权列表", notes = "查询美团外卖授权列表")
    public StoreAuthDTO getTuanGouAuth(@RequestBody @Validated StoreAuthDTO storeAuthDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询美团外卖授权列表入参：{}", JacksonUtils.writeValueAsString(storeAuthDTO));
        }
        return mtAuthService.getTuanGouAuth(storeAuthDTO);
    }

    @PostMapping("/query_token")
    @ApiOperation(value = "查询美团Token", notes = "查询美团Token")
    public TokenDTO queryToken(@RequestBody TakeoutShopBindReqDTO takeoutShopBindReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询美团Token入参：{}", JacksonUtils.writeValueAsString(takeoutShopBindReqDTO));
        }

        return mtAuthService.getToken(takeoutShopBindReqDTO);
    }
//
//    @GetMapping("/group-buying/token/{ePoiId}")
//    @ApiOperation(value = "TODO", notes = "TODO")
//    public Object getAccessToken(@PathVariable String ePoiId, @RequestParam String secret) {
//        if (log.isInfoEnabled()) {
//            log.info("TODO入参：{}", "ePoiId=" + ePoiId + ", secret=" + secret);
//        }
//        Response response = new Response();
//        if (!"zkzAuthTokenMtSecret".equals(secret))
//            throw new BusinessException("秘钥错误");
//        MtAuthDO platformEpoiAuth = mtAuthService.getAuth(ePoiId, 1);
//        if (platformEpoiAuth != null) {
//            response.setData(platformEpoiAuth);
//        } else {
//            response.setStatus(ResponseCode.NOT_FOUND.getCode());
//            response.setMessage(ResponseCode.NOT_FOUND.getCodeName());
//        }
//        return response;
//    }

    @PostMapping("/check_ticket")
    @ApiOperation(value = "美团验券", notes = "美团验券")
    public MtCouponDoCheckRespDTO checkTicket(@RequestBody MtCouponReqDTO mtCouponReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("美团验券入参：{}", JacksonUtils.writeValueAsString(mtCouponReqDTO));
        }

        if (SpringContextUtil.isDevEnv()) {
            log.info("美团验券：加载环境，开发环境：{}", SpringContextUtil.isDevEnv());
            return mtDevCouponServiceImpl.checkTicket(mtCouponReqDTO);
        } else {
            log.info("美团验券：加载环境，线上环境：{}", SpringContextUtil.isDevEnv());
            return mtProdCouponServiceImpl.checkTicket(mtCouponReqDTO);
        }
    }

    @PostMapping("/pre_check")
    @ApiOperation(value = "预验券", notes = "预验券")
    public MtCouponPreRespDTO preCheck(@RequestBody MtCouponReqDTO mtCouponReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("预验券入参：{}", JacksonUtils.writeValueAsString(mtCouponReqDTO));
        }

        if (SpringContextUtil.isDevEnv()) {
            log.info("预验券：加载环境，开发环境");
            return mtDevCouponServiceImpl.preCheck(mtCouponReqDTO);
        } else {
            log.info("预验券：加载环境，线上环境");
            return mtProdCouponServiceImpl.preCheck(mtCouponReqDTO);
        }
    }


    @PostMapping("/do_check")
    @ApiOperation(value = "执行验券", notes = "执行验券")
    public MtCouponDoCheckRespDTO doCheck(@RequestBody MtCouponReqDTO mtCouponReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("执行验券：{}", JacksonUtils.writeValueAsString(mtCouponReqDTO));
        }

        if (SpringContextUtil.isDevEnv()) {
            log.info("执行验券：加载环境，开发环境");
            return mtDevCouponServiceImpl.doCheck(mtCouponReqDTO);
        } else {
            log.info("执行验券：加载环境，线上环境");
            return mtProdCouponServiceImpl.doCheck(mtCouponReqDTO);
        }
    }


    @PostMapping("/cancel_ticket")
    @ApiOperation(value = "撤销验券", notes = "撤销验券")
    public MtDelCouponRespDTO cancelTicket(@RequestBody CouponDelReqDTO couponDelReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("撤销验券入参：{}", JacksonUtils.writeValueAsString(couponDelReqDTO));
        }
        if (SpringContextUtil.isDevEnv()) {
            log.info("撤销验券：加载环境，开发环境");
            return mtDevCouponServiceImpl.cancelTicket(couponDelReqDTO);
        } else {
            log.info("撤销验券：加载环境，线上环境");
            return mtProdCouponServiceImpl.cancelTicket(couponDelReqDTO);
        }
    }

    @PostMapping("/group/trade/detail")
    @ApiOperation(value = "查询团购订单结算明细", notes = "查询团购订单结算明细")
    public MtCouponTradeDetailRespDTO queryGroupTradeDetail(@RequestBody MtCouponReqDTO mtCouponReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询团购订单结算明细入参：{}", JacksonUtils.writeValueAsString(mtCouponReqDTO));
        }
        if (SpringContextUtil.isDevEnv()) {
            log.info("查询团购订单结算明细：加载环境，开发环境");
            return mtDevCouponServiceImpl.queryGroupTradeDetail(mtCouponReqDTO);
        } else {
            log.info("查询团购订单结算明细：加载环境，线上环境");
            return mtProdCouponServiceImpl.queryGroupTradeDetail(mtCouponReqDTO);
        }
    }


    @PostMapping("/update_delivery")
    @ApiOperation(value = "修改门店配送方式")
    public Boolean updateDelivery(@RequestBody StoreAuthDTO storeAuthDTO) {
        return mtAuthService.updateDelivery(storeAuthDTO);
    }


    @PostMapping("/callback/settlement/order")
    @ApiOperation(value = "接收订单结算明细回调接口", notes = "回调推送")
    public MtCallbackResponse orderSettlementCallback(@RequestBody MtCallbackSettlementDTO mtCallbackDTO) {
        if (log.isInfoEnabled()) {
            log.info("(美团)接收订单结算明细回调， 入参：{}", JacksonUtils.writeValueAsString(mtCallbackDTO));
        }
        if (!mtCallbackValidator.checkSignature(mtCallbackDTO)) {
            log.info("(美团)接收订单结算明细回调，签名不匹配");
            return MtCallbackResponse.SIGNATURE_ERROR;
        }

        try {
            mtCallbackService.orderTradeDetailCallback(mtCallbackDTO);
        } catch (Exception e) {
            if (log.isErrorEnabled()) {
                log.error("(美团)接收订单结算明细回调，处理异常：{}", ThrowableExtUtils.asStringIfAbsent(e));
            }
            return MtCallbackResponse.UNKNOWN_ERROR;
        }
        return MtCallbackResponse.OK;
    }

    @PostMapping("/callback/authorization")
    @ApiOperation(value = "美团业务授权码回调", notes = "回调推送")
    public MtCallbackResponse authorizationCallback(@RequestBody MtCallbackAuthorizationDTO mtCallbackDTO) {
        if (log.isInfoEnabled()) {
            log.info("美团业务授权码回调，参数：{}", JacksonUtils.writeValueAsString(mtCallbackDTO));
        }

        if (!mtCallbackValidator.checkSignature(mtCallbackDTO)) {
            log.info("美团业务授权码回调地址，签名不匹配");
            return MtCallbackResponse.SIGNATURE_ERROR;
        }

        try {
            mtCallbackService.authorizationCallback(mtCallbackDTO);
        } catch (Exception e) {
            if (log.isErrorEnabled()) {
                log.error("美团业务授权码回调地址，处理异常：{}", ThrowableExtUtils.asStringIfAbsent(e));
            }
            return MtCallbackResponse.UNKNOWN_ERROR;
        }
        return MtCallbackResponse.OK;
    }

    @PostMapping("/callback/authorization/unbind")
    @ApiOperation(value = "美团业务授权解除回调", notes = "回调推送")
    public MtCallbackResponse authorizationUnbindCallback(@RequestBody MtCallbackUnbindAuthorizationDTO mtCallbackDTO) {
        if (log.isInfoEnabled()) {
            log.info("美团业务授权解除回调，参数：{}", JacksonUtils.writeValueAsString(mtCallbackDTO));
        }

        if (!mtCallbackValidator.checkSignature(mtCallbackDTO)) {
            log.info("美团业务授权解除回调，签名不匹配");
            return MtCallbackResponse.UNBIND_AUTHORIZATION_FAIL;
        }

        try {
            mtCallbackService.unAuthorizationCallback(mtCallbackDTO);
        } catch (Exception e) {
            if (log.isErrorEnabled()) {
                log.error("美团业务授权解除回调，处理异常：{}", ThrowableExtUtils.asStringIfAbsent(e));
            }
            return MtCallbackResponse.UNBIND_AUTHORIZATION_FAIL;
        }

        return MtCallbackResponse.UNBIND_AUTHORIZATION_SUCCESS;
    }

    @PostMapping("/callback/member/consume")
    @ApiOperation(value = "会员卡积分推送回调", notes = "回调推送")
    public MtCallbackResponse memberConsumeCallback(@RequestBody MtCallbackConsumeDTO mtCallbackDTO) {
        if (log.isInfoEnabled()) {
            log.info("会员卡积分推送回调，参数：{}", JacksonUtils.writeValueAsString(mtCallbackDTO));
        }

        if (!mtCallbackValidator.checkSignature(mtCallbackDTO)) {
            log.info("会员卡积分推送，签名不匹配");
            return MtCallbackResponse.SIGNATURE_ERROR;
        }

        try {
            mtBrandMemberService.saveConsumeRecord(mtCallbackDTO);
        } catch (Exception e) {
            e.printStackTrace();
            if (log.isErrorEnabled()) {
                log.error("会员卡积分推送，处理异常：{}", ThrowableExtUtils.asStringIfAbsent(e));
            }

            return MtCallbackResponse.UNKNOWN_ERROR;
        }

        return MtCallbackResponse.OK;
    }

    @PostMapping("/callback/member/new")
    @ApiOperation(value = "会员联名卡查询新客", notes = "回调推送")
    public MtCallbackResponse memberNewCallback(@RequestBody MtCallbackMemberDTO mtCallbackDTO) {
        if (log.isInfoEnabled()) {
            log.info("会员联名卡查询新客，参数：{}", JacksonUtils.writeValueAsString(mtCallbackDTO));
        }

        if (!mtCallbackValidator.checkSignature(mtCallbackDTO)) {
            log.info("会员联名卡查询新客，签名不匹配");
            return MtCallbackResponse.buildBusinessFail("签名不匹配");
        }
        boolean isPureNewUser;
        try {
            isPureNewUser = mtBrandMemberService.judgeAndMember(mtCallbackDTO);
        } catch (Exception e) {
            if (log.isErrorEnabled()) {
                log.error("会员联名卡查询新客异常，{}", ThrowableExtUtils.asStringIfAbsent(e));
            }

            return MtCallbackResponse.buildBusinessFail("业务异常");
        }
        JSONObject json = new JSONObject();
        json.put("isPureNewUser",isPureNewUser);
        log.info("会员联名卡查询新客回调返回：{}",json);
        return MtCallbackResponse.buildBusinessSuccess(json.toJSONString());
    }

}
