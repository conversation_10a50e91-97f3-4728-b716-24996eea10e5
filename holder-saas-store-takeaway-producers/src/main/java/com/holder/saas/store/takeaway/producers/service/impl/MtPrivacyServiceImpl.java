package com.holder.saas.store.takeaway.producers.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holder.saas.store.takeaway.producers.entity.domain.MtPrivacyDO;
import com.holder.saas.store.takeaway.producers.mapper.MtPrivacyMapper;
import com.holder.saas.store.takeaway.producers.service.MtPrivacyService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2018-05-23
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
public class MtPrivacyServiceImpl extends ServiceImpl<MtPrivacyMapper, MtPrivacyDO> implements MtPrivacyService {

}
