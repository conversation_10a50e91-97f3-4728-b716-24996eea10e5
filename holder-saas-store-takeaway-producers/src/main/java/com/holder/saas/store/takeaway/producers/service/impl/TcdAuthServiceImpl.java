package com.holder.saas.store.takeaway.producers.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holder.saas.store.takeaway.producers.entity.domain.TcdAuthDO;
import com.holder.saas.store.takeaway.producers.mapper.ZcAuthMapper;
import com.holder.saas.store.takeaway.producers.service.DistributedService;
import com.holder.saas.store.takeaway.producers.service.TcdAuthService;
import com.holder.saas.store.takeaway.producers.utils.HttpRequestUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.takeaway.request.*;
import com.holderzone.saas.store.dto.takeaway.response.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Slf4j
@Service
public class TcdAuthServiceImpl extends ServiceImpl<ZcAuthMapper, TcdAuthDO> implements TcdAuthService {

    @Value("${zc.URL}")
    private String zcUrl;

    @Value("${zc.SHOP_BINDING_URL}")
    private String shopBindingUrl;

    @Value("${zc.SHOP_DELIVERY_SYNC}")
    private String shopDeliverySyncUrl;

    @Value("${zc.TYPE_QUERY_URL}")
    private String typeQueryUrl;

    @Value("${zc.ITEM_QUERY_URL}")
    private String itemQueryUrl;

    @Value("${zc.ITEM_BINDING_URL}")
    private String itemBindingUrl;

    @Value("${zc.ORDER_DINING_OUT_URL}")
    private String orderDiningOutUrl;

    @Value("${zc.ORDER_CONFIRM_THE_MEAL_URL}")
    private String orderConfirmTheMealUrl;

    private final DistributedService distributedService;

    @Autowired
    public TcdAuthServiceImpl(DistributedService distributedService) {
        this.distributedService = distributedService;
    }

    @Override
    public TcdCommonRespDTO doShopBindingTcd(TCDBindReqDTO tcdBindReqDTO) {
        TcdCommonRespDTO tcdCommonRespDTO = new TcdCommonRespDTO();
        String paramJson = JacksonUtils.writeValueAsString(tcdBindReqDTO);
        log.info("【赚餐外卖平台】门店绑定参数，url={},param={}", zcUrl + shopBindingUrl, paramJson);
        String httpResult = HttpRequestUtils.sendHttpRequest(zcUrl + shopBindingUrl, paramJson, "-1");
        log.info("【赚餐外卖平台】门店绑定结果，httpResult={}", httpResult);
        TcdAuthDO tcdAuthDO = new TcdAuthDO();
        if (StringUtils.isNotEmpty(httpResult)) {
            TcdBindRespDTO respDTO = JacksonUtils.toObject(TcdBindRespDTO.class, httpResult);
            if (respDTO.getCode() == 200) {
                TcdBindResp tcdBindResp = respDTO.getData();
                // 删除其他绑定关系
                removeByZcStoreId(tcdBindReqDTO.getId());
                tcdAuthDO.setGuid(distributedService.nextZcGuid())
                        .setStoreGuid(tcdBindReqDTO.getStoreGuid())
                        .setEnterpriseGuid(tcdBindReqDTO.getEnterpriseGuid())
                        .setAccessToken(tcdBindResp.getToken())
                        .setRefreshToken(tcdBindResp.getToken())
                        .setActiveTime(DateTimeUtils.now())
                        .setExpireTime(DateTimeUtils.now().plusYears(100))
                        .setZcStoreId(tcdBindReqDTO.getId())
                        .setZcPlatformId(tcdBindReqDTO.getPlatformId())
                        .setShopName(tcdBindResp.getName())
                        .setDeleted(Boolean.FALSE);
                boolean result = this.save(tcdAuthDO);
                if (result) {
                    tcdCommonRespDTO.setCode(200);
                    tcdCommonRespDTO.setMessage("操作成功!");
                    return tcdCommonRespDTO;
                } else {
                    tcdCommonRespDTO.setCode(201);
                    tcdCommonRespDTO.setMessage("操作失败!");
                    return tcdCommonRespDTO;
                }
            } else {
                tcdCommonRespDTO.setCode(respDTO.getCode());
                tcdCommonRespDTO.setMessage("绑定失败：" + respDTO.getMessage());
                return tcdCommonRespDTO;
            }
        } else {
            tcdCommonRespDTO.setCode(203);
            tcdCommonRespDTO.setMessage("绑定失败：绑定结果无响应！");
            return tcdCommonRespDTO;
        }
    }

    @Override
    public TcdCommonRespDTO doShopUnBindingTcd(TCDBindReqDTO TCDBindReqDTO) {
        TcdCommonRespDTO tcdCommonRespDTO = new TcdCommonRespDTO();
        String token = checkToken(TCDBindReqDTO.getStoreGuid());
        TcdAuthDO tcdAuthDO = getTcdAuth(TCDBindReqDTO.getStoreGuid());
        TCDBindReqDTO.setToken(token);
        String paramJson = JacksonUtils.writeValueAsString(TCDBindReqDTO);
        log.info("【赚餐外卖平台】门店解绑参数，url={},param={}", zcUrl + shopBindingUrl, paramJson);
        String httpResult = HttpRequestUtils.sendHttpRequest(zcUrl + shopBindingUrl, paramJson, tcdAuthDO.getAccessToken());
        log.info("【赚餐外卖平台】门店解绑结果，httpResult={}", httpResult);
        if (StringUtils.isNotEmpty(httpResult)) {
            TcdBindRespDTO tcdBindRespDTO = JacksonUtils.toObject(TcdBindRespDTO.class, httpResult);
            if (tcdBindRespDTO.getCode() == 200) {
                boolean result = this.removeById(tcdAuthDO.getId());
                if (result) {
                    tcdCommonRespDTO.setCode(200);
                    tcdCommonRespDTO.setMessage("操作成功！");
                } else {
                    tcdCommonRespDTO.setCode(201);
                    tcdCommonRespDTO.setMessage("操作失败！");
                }
                return tcdCommonRespDTO;
            } else {
                tcdCommonRespDTO.setCode(tcdBindRespDTO.getCode());
                tcdCommonRespDTO.setMessage("解绑失败：" + tcdBindRespDTO.getMessage());
                return tcdCommonRespDTO;
            }
        } else {
            tcdCommonRespDTO.setCode(202);
            tcdCommonRespDTO.setMessage("解绑失败：解绑请求未响应！");
            return tcdCommonRespDTO;
        }
    }

    @Override
    public List<TcdItemMappingRespDTO> getItem(String token) {
        String itemQuery = zcUrl + itemQueryUrl + "?token=" + token;
        log.info("【赚餐外卖平台】查询菜品参数，url={}", itemQuery);
        String httpResult = HttpRequestUtils.sendHttpRequestGet(itemQuery);
        log.info("【赚餐外卖平台】查询菜品结果，httpResult={}", httpResult);
        TcdItemMappingQueryRespDTO tcdItemMappingQueryRespDTO = new TcdItemMappingQueryRespDTO();
        List<TcdItemMappingRespDTO> itemData = new ArrayList<>();
        if (StringUtils.isNotEmpty(httpResult)) {
            tcdItemMappingQueryRespDTO = JacksonUtils.toObject(TcdItemMappingQueryRespDTO.class, httpResult);
        } else {
            return Collections.emptyList();
        }
        if (tcdItemMappingQueryRespDTO.getCode() == 200) {
            itemData = tcdItemMappingQueryRespDTO.getData();
            return itemData;
        } else {
            log.info("【赚餐外卖平台】查询菜品失败:code={},message={}",
                    tcdItemMappingQueryRespDTO.getCode(), tcdItemMappingQueryRespDTO.getMessage());
            return Collections.emptyList();
        }
    }

    @Override
    public String doTcdItemBinding(TCDItemBindingReqDTO tcdItemBindingReqDTO) {
        String jsonParam = JacksonUtils.writeValueAsString(tcdItemBindingReqDTO);
        String httpUrl = zcUrl + itemBindingUrl;
        if (tcdItemBindingReqDTO.getOperateType() == 0) {
            log.info("【赚餐外卖平台】绑定菜品入参：httpUrl={},param={}", httpUrl, jsonParam);
        } else if (tcdItemBindingReqDTO.getOperateType() == 1) {
            log.info("【赚餐外卖平台】解绑菜品入参：httpUrl={},param={}", httpUrl, jsonParam);
        }
        String httpResult = HttpRequestUtils.sendHttpRequestPost(httpUrl, jsonParam);
        if (tcdItemBindingReqDTO.getOperateType() == 0) {
            log.info("【赚餐外卖平台】绑定菜品结果：httpResult={}", httpResult);
        } else if (tcdItemBindingReqDTO.getOperateType() == 1) {
            log.info("【赚餐外卖平台】解绑菜品结果：httpResult={}", httpResult);
        }
        if (httpResult == null) {
            return "FAILURE";
        }
        TcdCommonRespDTO tcdCommonRespDTO = JacksonUtils.toObject(TcdCommonRespDTO.class, httpResult);
        if (tcdCommonRespDTO.getCode() == 200) {
            return "SUCCESS";
        } else {
            return tcdCommonRespDTO.getMessage();
        }
    }

    @Override
    public List<TcdItemMappingRespDTO> getType(String token) {
        String typeQuery = zcUrl + typeQueryUrl + "?token=" + token;
        log.info("【赚餐外卖平台】查询分类参数，url={}", typeQuery);
        String httpResult = HttpRequestUtils.sendHttpRequestGet(typeQuery);
        log.info("【赚餐外卖平台】查询分类结果，httpResult={}", httpResult);
        TcdItemMappingQueryRespDTO tcdItemMappingQueryRespDTO = new TcdItemMappingQueryRespDTO();
        List<TcdItemMappingRespDTO> tcdItemMappingRespDTOS = new ArrayList<>();
        if (!StringUtils.isEmpty(httpResult)) {
            tcdItemMappingQueryRespDTO = JacksonUtils.toObject(TcdItemMappingQueryRespDTO.class, httpResult);
            if (tcdItemMappingQueryRespDTO.getCode() == 200) {
                tcdItemMappingRespDTOS = tcdItemMappingQueryRespDTO.getData();
            } else {
                log.info("【赚餐外卖平台】查询分类结果失败,code={},message={}",
                        tcdItemMappingQueryRespDTO.getCode(), tcdItemMappingQueryRespDTO.getMessage());
                tcdItemMappingRespDTOS = new ArrayList<>();
            }
        } else {
            log.info("【赚餐外卖平台】查询分类结果无响应");
        }
        return tcdItemMappingRespDTOS;
    }

    @Override
    public Boolean updateDelivery(StoreAuthDTO storeAuthDTO) {
        TcdAuthDO tcdAuthDO = getOne(new LambdaQueryWrapper<TcdAuthDO>()
                .eq(TcdAuthDO::getStoreGuid, storeAuthDTO.getStoreGuid())
                .eq(TcdAuthDO::getDeleted, Boolean.FALSE));
        tcdAuthDO.setDeliveryType(storeAuthDTO.getDeliveryType());
        //通吃岛特殊流程：在更改自配送设置时，调用赚餐方同步门店当前的配送状态。后续赚餐方通过该项数据做配送业务的选择
        if (updateById(tcdAuthDO)) {
            TCDDeliverySyncReqDTO tcdDeliverySyncReqDTO = new TCDDeliverySyncReqDTO();
            TcdCommonRespDTO tcdCommonRespDTO = new TcdCommonRespDTO();
            String token = tcdAuthDO.getAccessToken();
            tcdDeliverySyncReqDTO.setToken(token);
            tcdDeliverySyncReqDTO.setDeliveryType(tcdAuthDO.getDeliveryType());
            String paramJson = JacksonUtils.writeValueAsString(tcdDeliverySyncReqDTO);
            log.info("【赚餐外卖平台】同步自配送方式参数，url={},param={}", zcUrl + shopDeliverySyncUrl, paramJson);
            String httpResult = HttpRequestUtils.sendHttpRequest(zcUrl + shopDeliverySyncUrl, paramJson, tcdAuthDO.getAccessToken());
            log.info("【赚餐外卖平台】同步自配送方式结果，httpResult={}", httpResult);
            if (StringUtils.isNotEmpty(httpResult)) {
                TcdBindRespDTO tcdBindRespDTO = JacksonUtils.toObject(TcdBindRespDTO.class, httpResult);
                if (tcdBindRespDTO.getCode() == 200) {
                    log.info("【赚餐外卖平台】同步自配送方式成功");
                    return Boolean.TRUE;
                } else {
                    log.info("【赚餐外卖平台】同步自配送方式失败:code={},message={}", tcdBindRespDTO.getCode(), tcdBindRespDTO.getMessage());
                    return Boolean.FALSE;
                }
            } else {
                log.info("【赚餐外卖平台】同步自配送方式无响应");
                return Boolean.FALSE;
            }
        } else {
            log.info("【赚餐外卖平台】同步自配送方式修改失败");
            return Boolean.FALSE;
        }
    }

    @Override
    public StoreAuthDTO getTakeoutAuth(StoreAuthDTO storeAuthDTO) {
        TcdAuthDO tcdAuthDO = getOne(new LambdaQueryWrapper<TcdAuthDO>()
                .eq(TcdAuthDO::getStoreGuid, storeAuthDTO.getStoreGuid())
                .eq(TcdAuthDO::getDeleted, Boolean.FALSE));
        if (tcdAuthDO == null) {
            return storeAuthDTO.setBindingStatus(0);
        }
        return storeAuthDTO.setBindingStatus(1).setShopName(tcdAuthDO.getShopName()).setDeliveryType(tcdAuthDO.getDeliveryType());
    }

    @Override
    public String getToken(String storeGuid) {
        TcdAuthDO tcdAuthDO = getOne(new LambdaQueryWrapper<TcdAuthDO>()
                .eq(TcdAuthDO::getStoreGuid, storeGuid)
                .eq(TcdAuthDO::getDeleted, Boolean.FALSE));
        if (!ObjectUtils.isEmpty(tcdAuthDO)) {
            return tcdAuthDO.getAccessToken();
        } else {
            return "-1";
        }
    }

    public TcdAuthDO getTcdAuth(String storeGuid) {
        TcdAuthDO tcdAuthDO = getOne(new LambdaQueryWrapper<TcdAuthDO>()
                .eq(TcdAuthDO::getStoreGuid, storeGuid)
                .eq(TcdAuthDO::getDeleted, Boolean.FALSE));
        if (!ObjectUtils.isEmpty(tcdAuthDO)) {
            return tcdAuthDO;
        } else {
            return null;
        }
    }

    @Override
    public String checkToken(String storeGuid) {
        TcdAuthDO tcdAuthDO = null;
        if (!StringUtils.isEmpty(storeGuid)) {
            tcdAuthDO = getOne(new LambdaQueryWrapper<TcdAuthDO>()
                    .eq(TcdAuthDO::getStoreGuid, storeGuid));
            if (tcdAuthDO == null) {
                throw new BusinessException("门店未绑定!请绑定门店后重试!");
            } else {
                return tcdAuthDO.getAccessToken();
            }
        } else {
            throw new BusinessException("storeGuid为空！");
        }
    }

    @Override
    public List<TcdAuthDO> getTokens(List<String> storeGuids) {
        if (CollectionUtils.isEmpty(storeGuids)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<TcdAuthDO>()
                .in(TcdAuthDO::getStoreGuid, storeGuids));
    }

    @Override
    public TcdCommonRespDTO diningOutTcd(TakeoutTCDDiningOutDTO takeoutTCDDiningOutDTO) {
        String msgType = "商家出餐";
        String storeGuid = takeoutTCDDiningOutDTO.getStoreGuid();
        String orderId = takeoutTCDDiningOutDTO.getOrderSn();
        log.info("(赚餐外卖平台){}，orderId: {}，处理中", msgType, takeoutTCDDiningOutDTO.getOrderSn());
        String token = checkToken(takeoutTCDDiningOutDTO.getStoreGuid());
        takeoutTCDDiningOutDTO.setOrderState(TakeoutTcdOrderReqDTO.OrderState.PERSON_PENDING.toString());
        takeoutTCDDiningOutDTO.setToken(token);
        //调用赚餐出餐接口
        String jsonParam = JacksonUtils.writeValueAsString(takeoutTCDDiningOutDTO);
        log.info("【赚餐外卖平台】商家出餐参数：url={},param={}", zcUrl + orderDiningOutUrl, jsonParam);
        String result = HttpRequestUtils.sendHttpRequestPost(zcUrl + orderDiningOutUrl, jsonParam);
        log.info("【赚餐外卖平台】商家出餐结果：result={}", result);
        if (result == null) {
            log.error("【赚餐外卖平台】商家出餐失败，storeGuid={},orderId={}", storeGuid, orderId);
            TcdCommonRespDTO tcdCommonRespDTO = new TcdCommonRespDTO();
            tcdCommonRespDTO.setCode(202);
            tcdCommonRespDTO.setMessage("商家出餐失败");
            return tcdCommonRespDTO;
        }
        TcdCommonRespDTO tcdCommonRespDTO = JacksonUtils.toObject(TcdCommonRespDTO.class, result);
        if (tcdCommonRespDTO.getCode() != 200) {
            log.error("【赚餐外卖平台】商家出餐失败，message={}", tcdCommonRespDTO.getMessage());
        } else {
            log.error("【赚餐外卖平台】商家出餐成功，orderId={}", takeoutTCDDiningOutDTO.getOrderSn());
        }
        return tcdCommonRespDTO;
    }


    @Override
    public TcdCommonRespDTO confirmTheMealTcd(TakeoutTCDConfirmTheMealDTO takeoutTCDConfirmTheMealDTO) {
        String msgType = "确认取餐";
        String storeGuid = takeoutTCDConfirmTheMealDTO.getStoreGuid();
        String orderId = takeoutTCDConfirmTheMealDTO.getOrderSn();
        log.info("(赚餐外卖平台){}，orderId: {}，处理中", msgType, takeoutTCDConfirmTheMealDTO.getOrderSn());
        String token = checkToken(takeoutTCDConfirmTheMealDTO.getStoreGuid());
        takeoutTCDConfirmTheMealDTO.setOrderState(TakeoutTcdOrderReqDTO.OrderState.FINISH.toString());
        takeoutTCDConfirmTheMealDTO.setToken(token);
        //调用赚餐出餐接口
        String jsonParam = JacksonUtils.writeValueAsString(takeoutTCDConfirmTheMealDTO);
        log.info("【赚餐外卖平台】确认取餐参数：url={},param={}", zcUrl + orderConfirmTheMealUrl, jsonParam);
        String result = HttpRequestUtils.sendHttpRequestPost(zcUrl + orderConfirmTheMealUrl, jsonParam);
        log.info("【赚餐外卖平台】确认取餐结果：result={}", result);
        if (result == null) {
            log.error("【赚餐外卖平台】商家出餐失败，storeGuid={},orderId={}", storeGuid, orderId);
            TcdCommonRespDTO tcdCommonRespDTO = new TcdCommonRespDTO();
            tcdCommonRespDTO.setCode(202);
            tcdCommonRespDTO.setMessage("确认取餐失败");
            return tcdCommonRespDTO;
        }
        TcdCommonRespDTO tcdCommonRespDTO = JacksonUtils.toObject(TcdCommonRespDTO.class, result);
        if (tcdCommonRespDTO.getCode() != 200) {
            log.error("【赚餐外卖平台】确认取餐失败，message={}", tcdCommonRespDTO.getMessage());
        } else {
            log.error("【赚餐外卖平台】确认取餐成功，orderId={}", takeoutTCDConfirmTheMealDTO.getOrderSn());
        }
        return tcdCommonRespDTO;
    }

    @Override
    public TcdCommonRespDTO pickUpTcd(TakeoutTCDPickUpDTO takeoutTCDPickUpDTO) {
        String msgType = "商家自提扫码";
        String storeGuid = takeoutTCDPickUpDTO.getStoreGuid();
        String orderId = takeoutTCDPickUpDTO.getOrderSn();
        log.info("(赚餐外卖平台){}，orderId: {}，处理中", msgType, takeoutTCDPickUpDTO.getOrderSn());
        String token = checkToken(takeoutTCDPickUpDTO.getStoreGuid());
        takeoutTCDPickUpDTO.setOrderState(TakeoutTcdOrderReqDTO.OrderState.FINISH.toString());
        takeoutTCDPickUpDTO.setToken(token);
        //调用赚餐出餐接口
        String jsonParam = JacksonUtils.writeValueAsString(takeoutTCDPickUpDTO);
        log.info("【赚餐外卖平台】商家自提扫码参数：url={},param={}", zcUrl + orderDiningOutUrl, jsonParam);
        String result = HttpRequestUtils.sendHttpRequestPost(zcUrl + orderDiningOutUrl, jsonParam);
        log.info("【赚餐外卖平台】商家自提扫码结果：result={}", result);
        if (result == null) {
            log.error("【赚餐外卖平台】商家自提扫码失败，storeGuid={},orderId={}", storeGuid, orderId);
            TcdCommonRespDTO tcdCommonRespDTO = new TcdCommonRespDTO();
            tcdCommonRespDTO.setCode(202);
            tcdCommonRespDTO.setMessage("商家出餐失败");
            return tcdCommonRespDTO;
        }
        TcdCommonRespDTO tcdCommonRespDTO = JacksonUtils.toObject(TcdCommonRespDTO.class, result);
        if (tcdCommonRespDTO.getCode() != 200) {
            log.error("【赚餐外卖平台】商家自提扫码失败，message={}", tcdCommonRespDTO.getMessage());
        } else {
            log.error("【赚餐外卖平台】商家自提扫码成功，orderId={}", takeoutTCDPickUpDTO.getOrderSn());
        }
        //fixme 1.定义具体的错误码给赵平，三方规定清楚。成功，自动到已完成，会自动刷新（原有流程）；失败，则返回错误码，平总去弄刷新
        return tcdCommonRespDTO;
    }

    @Override
    public void removeByZcStoreId(String zcStoreId) {
        UpdateWrapper<TcdAuthDO> uw = new UpdateWrapper<>();
        uw.lambda().eq(TcdAuthDO::getZcStoreId, zcStoreId);
        remove(uw);
    }

}
