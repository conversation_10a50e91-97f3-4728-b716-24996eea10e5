package com.holder.saas.store.takeaway.producers.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.holder.saas.store.takeaway.producers.entity.domain.EleAuthDO;
import com.holder.saas.store.takeaway.producers.service.EleAuthService;
import com.holder.saas.store.takeaway.producers.service.UnItemMappingService;
import com.holder.saas.store.takeaway.producers.utils.JacksonExtUtils;
import com.holder.saas.store.takeaway.producers.utils.ThrowableExtUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.bo.ExecuteRequestBO;
import com.holderzone.saas.store.constant.Constant;
import com.holderzone.saas.store.constant.takeaway.TakeawayConstants;
import com.holderzone.saas.store.dto.takeaway.*;
import eleme.openapi.sdk.api.entity.product.OCategory;
import eleme.openapi.sdk.api.entity.product.OItem;
import eleme.openapi.sdk.api.entity.product.OSpec;
import eleme.openapi.sdk.api.entity.product.QueryPage;
import eleme.openapi.sdk.api.enumeration.product.OItemUpdateProperty;
import eleme.openapi.sdk.api.exception.ServiceException;
import eleme.openapi.sdk.api.exception.UnauthorizedException;
import eleme.openapi.sdk.api.service.ProductService;
import eleme.openapi.sdk.config.Config;
import eleme.openapi.sdk.oauth.response.Token;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.ExecutorService;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service("eleItemMappingServiceImpl")
public class EleItemMappingServiceImpl implements UnItemMappingService {

    private final EleAuthService eleAuthService;

    private final Config config;

    @Qualifier("eleProductQueryThreadPool")
    @Autowired
    private ExecutorService eleProductQueryThreadPool;

    @Qualifier("batchBindProductThreadPool")
    @Autowired
    private ExecutorService batchBindProductThreadPool;

    @Autowired
    public EleItemMappingServiceImpl(EleAuthService eleAuthService, Config config) {
        this.eleAuthService = eleAuthService;
        this.config = config;
    }

    @Override
    public List<UnMappedType> getType(String storeGuid) {
        String replyType = "查询菜品分类";
        logRequestProcessing(replyType, storeGuid);

        Token token = eleAuthService.getToken(storeGuid);
        if (token != null) {
            ProductService productService = new ProductService(config, token);
            try {
                Long shopId = getShopIdByStoreGuid(storeGuid);
                if (shopId == null) {
                    logDirtyAuthFailureThenThrow(replyType, storeGuid);
                }
                List<OCategory> shopCategories = productService.getShopCategories(shopId);
                logRequestDataSucceed(replyType, storeGuid, shopCategories);
                return eleType2ErpType(shopCategories);
            } catch (ServiceException e) {
                logExceptionThenThrow(replyType, storeGuid, e);
            }
        } else {
            logAuthFailureThenThrow(replyType, storeGuid);
        }
        return null;
    }

    @Override
    public List<UnMappedItem> getItem(String storeGuid) {
        String replyType = "查询菜品";
        logRequestProcessing(replyType, storeGuid);

        Token token = eleAuthService.getToken(storeGuid);
        if (token != null) {
            ProductService productService = new ProductService(config, token);
            try {
                Long shopId = getShopIdByStoreGuid(storeGuid);
                if (shopId == null) {
                    logDirtyAuthFailureThenThrow(replyType, storeGuid);
                }
                QueryPage queryPage = new QueryPage();
                queryPage.setShopId(shopId);
                queryPage.setOffset(0L);
                queryPage.setLimit(300L);
                List<OItem> shopItem = productService.queryItemByPage(queryPage);
                logRequestDataSucceed(replyType, storeGuid, shopItem);
                return eleItemErpItem(shopItem);
            } catch (ServiceException e) {
                logExceptionThenThrow(replyType, storeGuid, e);
            }
        } else {
            logAuthFailureThenThrow(replyType, storeGuid);
        }
        return null;
    }

    @Override
    public List<UnMappedItem> getItems(UnItemQueryReq unItemQueryReq) {
        List<String> storeGuids = unItemQueryReq.getStoreGuids();
        String storeGuidStr = String.join(",", storeGuids);
        logRequestProcessing(TakeawayConstants.BATCH_QUERY_ITEM_OPERATE + "(饿了么)", storeGuidStr);
        // 查询各门店的token
        Map<String, Pair<EleAuthDO, Token>> tokenMap = eleAuthService.getTokens(storeGuids);
        if (MapUtils.isEmpty(tokenMap)) {
            log.warn("不存在token, storeGuids:{}", JacksonUtils.writeValueAsString(storeGuids));
            return Collections.emptyList();
        }
        if (storeGuids.size() != tokenMap.keySet().size()) {
            // 部分门店没有绑定
            storeGuids.removeAll(tokenMap.keySet());
            log.warn("部分门店没有token, storeGuids:{}", JacksonUtils.writeValueAsString(storeGuids));
        }
        Set<String> authStoreGuids = tokenMap.keySet();
        List<List<String>> storePartition = Lists.partition(new ArrayList<>(authStoreGuids), TakeawayConstants.BATCH_QUERY_ITEM_PARTITION);
        List<List<UnMappedItem>> eleResultList = Collections.synchronizedList(new ArrayList<>());
        CompletableFuture.allOf(storePartition.stream()
                .map(storeGuidList ->
                        CompletableFuture.supplyAsync(() -> storeGuidList.stream()
                                        .map(storeGuid -> queryMergeItem(storeGuid, tokenMap.get(storeGuid).getFirst(), tokenMap.get(storeGuid).getSecond()))
                                        .collect(Collectors.toList()), eleProductQueryThreadPool)
                                .whenComplete((result, throwable) -> {
                                    if (!CollectionUtils.isEmpty(result)) {
                                        eleResultList.addAll(result);
                                    }
                                    if (throwable != null) {
                                        log.error("completableFuture error:{}", throwable.getMessage());
                                    }
                                })).toArray(CompletableFuture[]::new)).whenComplete((v, th) -> {
        }).join();
        List<UnMappedItem> unMappedItemList = eleResultList.stream().flatMap(Collection::stream).collect(Collectors.toList());
        if (!StringUtils.isEmpty(unItemQueryReq.getKeywords())) {
            unMappedItemList.removeIf(e -> !e.getUnItemNameWithSku().contains(unItemQueryReq.getKeywords()));
        }
        log.info("批量查询门店商品列表返回:{}", JacksonUtils.writeValueAsString(unMappedItemList));
        return unMappedItemList;
    }


    /**
     * 查询饿了么 商品分类 + 商品列表
     *
     * @param eleAuth 门店授权
     * @param token   门店授权token
     * @return 商品列表
     */
    private List<UnMappedItem> queryMergeItem(String storeGuid, EleAuthDO eleAuth, Token token) {
        List<UnMappedType> itemTypeList;
        // 先查询分类
        ProductService productService = new ProductService(config, token);
        try {
            List<OCategory> shopCategories = productService.getShopCategories(eleAuth.getShopId());
            log.info("查询饿了么菜品分类,门店guid:{}, 分类返回：{}", storeGuid, JacksonUtils.writeValueAsString(shopCategories));
            itemTypeList = eleType2ErpType(shopCategories);
        } catch (ServiceException e) {
            logExceptionThenThrow(TakeawayConstants.BATCH_QUERY_ITEM_OPERATE, storeGuid, e);
            return Collections.emptyList();
        }
        if (CollectionUtils.isEmpty(itemTypeList)) {
            return Collections.emptyList();
        }
        Map<String, UnMappedType> itemTypeMap = itemTypeList.stream()
                .collect(Collectors.toMap(UnMappedType::getUnItemTypeId, Function.identity(), (key1, key2) -> key1));
        // 再查询商品
        try {
            QueryPage queryPage = new QueryPage();
            queryPage.setShopId(eleAuth.getShopId());
            queryPage.setOffset(0L);
            queryPage.setLimit(300L);
            List<OItem> shopItem = productService.queryItemByPage(queryPage);
            logRequestDataSucceed(TakeawayConstants.BATCH_QUERY_ITEM_OPERATE, storeGuid, JacksonUtils.writeValueAsString(shopItem));
            List<UnMappedItem> unMappedItems = eleItemErpItem(shopItem);
            for (UnMappedItem unMappedItem : unMappedItems) {
                UnMappedType unMappedType = itemTypeMap.get(unMappedItem.getUnItemTypeId());
                if (Objects.nonNull(unMappedType)) {
                    unMappedItem.setUnItemTypeName(unMappedType.getUnItemTypeName());
                }
                unMappedItem.setErpStoreGuid(storeGuid);
            }
            return unMappedItems;
        } catch (ServiceException e) {
            logExceptionThenThrow(TakeawayConstants.BATCH_QUERY_ITEM_OPERATE, storeGuid, e);
            return Collections.emptyList();
        }
    }


    @Override
    public void bindMapping(UnItemBindUnbindReq unItemBindUnbindReq) {
        handleMapping(unItemBindUnbindReq, true);
    }

    @Override
    public void unbindMapping(UnItemBindUnbindReq unItemBindUnbindReq) {
        handleMapping(unItemBindUnbindReq, false);
    }

    @Override
    public void batchUnbindMapping(UnItemBatchUnbindReq unItemBatchUnbindReq) {
        String replyType = unItemBatchUnbindReq.getBindFlag() ? "批量绑定商品映射" : "批量删除商品映射";
        String storeGuid = unItemBatchUnbindReq.getStoreGuid();
        logRequestProcessing(replyType, storeGuid);
        // 查询门店授权token
        Token token = eleAuthService.getToken(storeGuid);
        if (token == null) {
            logAuthFailureThenThrow(replyType, storeGuid);
            return;
        }
        // 构建批量解绑DTO
        List<UnItemBindUnbindReq> unItemBindUnbindReqs = unItemBatchUnbindReq.getUnItemUnbindList().stream()
                .map(unItemBaseMapReq -> {
                    UnItemBindUnbindReq unItemBindUnbindReq = new UnItemBindUnbindReq();
                    unItemBindUnbindReq.setUnItemId(unItemBaseMapReq.getUnItemId());
                    unItemBindUnbindReq.setUnItemSkuId(unItemBaseMapReq.getUnItemSkuId());
                    unItemBindUnbindReq.setUnItemTypeId(unItemBaseMapReq.getUnItemTypeId());
                    unItemBindUnbindReq.setExtendValue(unItemBaseMapReq.getExtendValue());
                    unItemBindUnbindReq.setErpItemGuid(unItemBaseMapReq.getErpItemGuid());
                    unItemBindUnbindReq.setErpItemSkuId(unItemBaseMapReq.getErpItemSkuId());
                    return unItemBindUnbindReq;
                }).collect(Collectors.toList());
        // 根据外卖平台商品itemId分组
        Map<String, List<UnItemBindUnbindReq>> unItemBindUnbindGroupByItemIdMap = unItemBindUnbindReqs.stream()
                .collect(Collectors.groupingBy(UnItemBindUnbindReq::getUnItemId));
        List<List<UnItemBindUnbindReq>> unItemBindUnbindGroupByItemIdList = new ArrayList<>(unItemBindUnbindGroupByItemIdMap.values());
        // 再根据商品数量分批
        List<List<List<UnItemBindUnbindReq>>> partitionList = Lists.partition(unItemBindUnbindGroupByItemIdList,
                TakeawayConstants.BATCH_QUERY_ITEM_PARTITION);
        ConcurrentMap<String, String> extendValueMap = Maps.newConcurrentMap();
        ConcurrentMap<Long, OSpec> skuHandleMap = Maps.newConcurrentMap();
        CompletableFuture.allOf(partitionList.stream()
                .map(partition ->
                        CompletableFuture.runAsync(() -> partition.stream()
                                .flatMap(Collection::stream)
                                .forEach(bindUnbindReq -> {
                                    ExecuteRequestBO requestBO = new ExecuteRequestBO();
                                    requestBO.setUnItemBindUnbindReq(bindUnbindReq);
                                    requestBO.setBinding(unItemBatchUnbindReq.getBindFlag());
                                    requestBO.setReplyType(replyType);
                                    requestBO.setStoreGuid(storeGuid);
                                    requestBO.setExtendValueMap(extendValueMap);
                                    executeRequest(token, requestBO, skuHandleMap);
                                }), eleProductQueryThreadPool).exceptionally(e -> {
                            log.error("[{}]失败,e=", replyType, e);
                            throw new BusinessException(e.getMessage());
                        })
                ).toArray(CompletableFuture[]::new)).join();
    }

    @SuppressWarnings("unchecked")
    private void handleMapping(UnItemBindUnbindReq unItemBindUnbindReq, boolean isBinding) {
        String replyType = isBinding ? "设置商品映射" : "删除商品映射";
        String storeGuid = unItemBindUnbindReq.getStoreGuid();
        logRequestProcessing(replyType, storeGuid);

        Token token = eleAuthService.getToken(storeGuid);
        if (token != null) {
            ExecuteRequestBO requestBO = new ExecuteRequestBO();
            requestBO.setUnItemBindUnbindReq(unItemBindUnbindReq);
            requestBO.setBinding(isBinding);
            requestBO.setReplyType(replyType);
            requestBO.setStoreGuid(storeGuid);
            requestBO.setExtendValueMap(null);
            executeRequest(token, requestBO, null);
        } else {
            logAuthFailureThenThrow(replyType, storeGuid);
        }
    }

    private void executeRequest(Token token, ExecuteRequestBO requestBO, ConcurrentMap<Long, OSpec> skuHandleMap) {
        UnItemBindUnbindReq unItemBindUnbindReq = requestBO.getUnItemBindUnbindReq();
        ConcurrentMap<String, String> extendValueMap = requestBO.getExtendValueMap();
        String replyType = requestBO.getReplyType();
        String storeGuid = requestBO.getStoreGuid();
        boolean isBinding = requestBO.isBinding();
        ProductService productService = new ProductService(config, token);
        try {
            String extendValue = getExtendValue(isBinding, extendValueMap, unItemBindUnbindReq);
            Map<OItemUpdateProperty, Object> properties = JacksonExtUtils.toObject(
                    new TypeReference<Map<OItemUpdateProperty, Object>>() {
                    }, extendValue);
            if (properties == null) {
                throw new BusinessException("商品源信息不得为空");
            }
            List<OSpec> oSpecs = JacksonUtils.toObjectList(OSpec.class,
                    JacksonUtils.writeValueAsString(properties.get(OItemUpdateProperty.specs)));
            handleExtendCode(isBinding, skuHandleMap, oSpecs, unItemBindUnbindReq);
            List<OSpec> targetSpecs = oSpecs.stream()
                    .filter(oSpec -> Long.valueOf(unItemBindUnbindReq.getUnItemSkuId())
                            .equals(oSpec.getSpecId()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(targetSpecs)) {
                throw new BusinessException("未找到平台方规格：" + unItemBindUnbindReq.getUnItemSkuId());
            }
            if (isBinding) {
                List<String> extendCodes = oSpecs.stream().map(OSpec::getExtendCode)
                        .filter(Objects::nonNull).collect(Collectors.toList());
                if (extendCodes.contains(unItemBindUnbindReq.getErpItemSkuId())) {
                    throw new BusinessException("已存在该规格的绑定信息，请重新绑定有效规格");
                }
                targetSpecs.get(0).setExtendCode(unItemBindUnbindReq.getErpItemSkuId());
            } else {
                targetSpecs.get(0).setExtendCode("");
            }
            for (OSpec oSpec : oSpecs) {
                if (oSpec.getStock() > 10000) {
                    oSpec.setStock(10000);
                }
                if (oSpec.getMaxStock() > 10000) {
                    oSpec.setMaxStock(10000);
                }
                //设置默认分量
                defaultWeightSetting(oSpec);
            }
            properties.put(OItemUpdateProperty.specs, oSpecs);
            OItem oItem = productService.updateItem(Long.parseLong(unItemBindUnbindReq.getUnItemId()),
                    Long.parseLong(unItemBindUnbindReq.getUnItemTypeId()), properties);
            if (!isBinding && extendValueMap != null) {
                extendValueMap.put(unItemBindUnbindReq.getUnItemId(), JacksonUtils.writeValueAsString(properties));
            }
            logReplyDataSucceed(replyType, storeGuid, oItem);
        } catch (ServiceException e) {
            logExceptionThenThrow(replyType, storeGuid, e);
        }
    }

    private void defaultWeightSetting(OSpec oSpec) {
        if(oSpec.getWeight() == null || oSpec.getWeight() <= 0){
            oSpec.setWeight(1);
        }
        boolean zeroWeight = oSpec.getSpecAttribute() != null && !StringUtils.isEmpty(oSpec.getSpecAttribute().getWeight()) && oSpec.getSpecAttribute().getWeight().equals("0");
        if(zeroWeight){
            oSpec.getSpecAttribute().setWeight("1");
        }
    }

    private void handleExtendCode(boolean isBinding, ConcurrentMap<Long, OSpec> skuHandleMap, List<OSpec> oSpecs,
                                  UnItemBindUnbindReq unItemBindUnbindReq) {
        if (oSpecs.size() > Constant.NUMBER_ONE && null != skuHandleMap) {
            Map<Long, OSpec> specMappingMap = oSpecs.stream()
                    .collect(Collectors.toMap(OSpec::getSpecId, Function.identity(), (v1, v2) -> v1));
            specMappingMap.forEach((specId, other) -> {
                OSpec skuHandle = skuHandleMap.get(specId);
                if (!ObjectUtils.isEmpty(skuHandle)) {
                    other.setExtendCode(skuHandle.getExtendCode());
                }
            });

            OSpec thisBind = specMappingMap.get(Long.valueOf(unItemBindUnbindReq.getUnItemSkuId()));
            if (!ObjectUtils.isEmpty(thisBind)) {
                OSpec oSpec = new OSpec();
                BeanUtils.copyProperties(thisBind, oSpec);
                if (isBinding) {
                    oSpec.setExtendCode(unItemBindUnbindReq.getErpItemSkuId());
                } else {
                    oSpec.setExtendCode(Strings.EMPTY);
                }
                skuHandleMap.put(thisBind.getSpecId(), oSpec);
            }
        }
    }

    private String getExtendValue(boolean isBinding,
                                  ConcurrentMap<String, String> extendValueMap,
                                  UnItemBindUnbindReq unItemBindUnbindReq) {
        String extendValue = null;
        if (!isBinding && extendValueMap != null) {
            extendValue = extendValueMap.get(unItemBindUnbindReq.getUnItemId());
        }
        if (!StringUtils.hasText(extendValue)) {
            extendValue = unItemBindUnbindReq.getExtendValue();
        }
        return extendValue;
    }

    private void logRequestProcessing(String msg, String storeGuid) {
        log.info("Request(饿了么){}，storeGuid: {}，处理中", msg, storeGuid);
    }

    private void logRequestDataSucceed(String msg, String storeGuid, Object obj) {
        log.info("Request(饿了么){}，storeGuid: {}，查询成功：{}",
                msg, storeGuid, JacksonUtils.writeValueAsString(obj));
    }

    private void logReplyDataSucceed(String msg, String storeGuid, Object obj) {
        log.info("Request(饿了么){}，storeGuid: {}，处理成功，处理结果：{}"
                , msg, storeGuid, JacksonUtils.writeValueAsString(obj));
    }

    private void logExceptionThenThrow(String msg, String storeGuid, ServiceException e) {
        if (log.isErrorEnabled()) {
            log.error("Request(饿了么){}，storeGuid: {}，处理失败：{}",
                    msg, storeGuid, ThrowableExtUtils.asStringIfAbsent(e));
        }
        if (e instanceof UnauthorizedException) {
            throw new BusinessException("操作失败，您的饿了么外卖已与门店解绑，请重新登录商户后台绑定");
        }
        throw new BusinessException(ThrowableExtUtils.asStringIfAbsent(e));
    }

    private void logAuthFailureThenThrow(String msg, String storeGuid) {
        log.error("Request(饿了么){}，处理失败，通过storeGuid[{}]未查询到token", msg, storeGuid);
        throw new BusinessException("操作失败，您的饿了么外卖已与门店解绑，请重新登录商户后台绑定");
    }

    private void logDirtyAuthFailureThenThrow(String msg, String storeGuid) {
        log.error("Request(饿了么){}，处理失败，通过storeGuid[{}]未查询到shopId", msg, storeGuid);
        throw new BusinessException("操作失败，您的饿了么外卖已与门店解绑，请重新登录商户后台绑定");
    }

    private Long getShopIdByStoreGuid(String storeGuid) {
        return eleAuthService.getOne(new LambdaQueryWrapper<EleAuthDO>()
                .select(EleAuthDO::getStoreGuid, EleAuthDO::getShopId)
                .eq(EleAuthDO::getStoreGuid, storeGuid)).getShopId();
    }

    private List<UnMappedType> eleType2ErpType(List<OCategory> shopCategories) {
        if (CollectionUtils.isEmpty(shopCategories)) {
            return Collections.emptyList();
        }
        return shopCategories.stream()
                .map(oShopCategory -> {
                    UnMappedType unMappedType = new UnMappedType();
                    unMappedType.setUnItemTypeId(String.valueOf(oShopCategory.getId()));
                    unMappedType.setUnItemTypeName(oShopCategory.getName());
                    return unMappedType;
                })
                .collect(Collectors.toList());
    }

    private List<UnMappedItem> eleItemErpItem(List<OItem> backItem) {
        if (CollectionUtils.isEmpty(backItem)) {
            return Collections.emptyList();
        }
        return backItem.stream()
                .flatMap(oItem -> {
                    List<UnMappedItem> unMappedItems = new ArrayList<>();
                    Map<OItemUpdateProperty, Object> properties = new HashMap<>();
                    properties.put(OItemUpdateProperty.name, oItem.getName());
                    properties.put(OItemUpdateProperty.specs, oItem.getSpecs());
                    properties.put(OItemUpdateProperty.materials, oItem.getMaterials());
                    String extendValue = JacksonUtils.writeValueAsString(properties);
                    for (OSpec spec : oItem.getSpecs()) {
                        UnMappedItem unMappedItem = new UnMappedItem();
                        unMappedItem.setUnItemId(String.valueOf(oItem.getId()));
                        unMappedItem.setUnItemName(oItem.getName());
                        unMappedItem.setUnItemSkuId(String.valueOf(spec.getSpecId()));
                        unMappedItem.setUnItemSkuName(spec.getName());
                        unMappedItem.setUnItemSkuUnit(oItem.getUnit());
                        if (StringUtils.hasText(spec.getName())) {
                            unMappedItem.setUnItemNameWithSku(oItem.getName() + "（" + spec.getName() + "）");
                        } else {
                            unMappedItem.setUnItemNameWithSku(oItem.getName());
                        }
                        unMappedItem.setUnItemTypeId(String.valueOf(oItem.getCategoryId()));
                        unMappedItem.setErpItemSkuId(spec.getExtendCode());
                        unMappedItem.setExtendValue(extendValue);
                        unMappedItems.add(unMappedItem);
                    }
                    return unMappedItems.stream();
                })
                .collect(Collectors.toList());
    }
}
