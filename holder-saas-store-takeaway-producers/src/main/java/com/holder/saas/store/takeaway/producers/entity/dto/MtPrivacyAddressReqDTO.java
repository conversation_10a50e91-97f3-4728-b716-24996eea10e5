package com.holder.saas.store.takeaway.producers.entity.dto;

import com.sankuai.sjst.platform.developer.utils.SignUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
public class MtPrivacyAddressReqDTO {

    @ApiModelProperty(value = "认领门店返回的token【一店一token】，非必须")
    private String appAuthToken;

    @ApiModelProperty(value = "交互数据的编码【建议UTF-8】，必须")
    private String charset;

    @ApiModelProperty(value = "当前请求的时间戳【单位是秒】，必须")
    private long timestamp;

    @ApiModelProperty(value = "接口版本【默认是1】，非必须")
    private String version;

    @ApiModelProperty(value = "请求的数字签名，必须")
    private String sign;

    @ApiModelProperty(value = "开发者id，必须")
    private Long developerId;

    @ApiModelProperty(value = "订单ID，必须")
    private long orderId;

    @ApiModelProperty(value = "查询原因类型枚举值：1：商家自有运力 2：美团运力转商家自有运力 0：其他，必须")
    private int queryReasonType;

    @ApiModelProperty(value = "查询原因补充说明：当【查询原因类型=其他】时，必须填写原因，query_reason_type=0时，必填，非必须")
    private String addOtherReason;


    public List<NameValuePair> nameValuePairs(String mtSignKey) {
        String charsetStr = charset;
        String addOtherReasonStr = addOtherReason;
        String appAuthTokenStr = appAuthToken;
        String versionStr = version;
        String timestampStr = String.valueOf(timestamp);
        String developerIdStr = String.valueOf(developerId);
        String orderIdStr = String.valueOf(orderId);
        String queryReasonTypeStr = String.valueOf(queryReasonType);

        Map<String, String> params = new HashMap<>();
        params.put("charset", charsetStr);
        params.put("timestamp", timestampStr);
        params.put("developerId", developerIdStr);
        params.put("orderId", orderIdStr);
        params.put("queryReasonType", queryReasonTypeStr);
        params.put("addOtherReason", addOtherReasonStr);

        String signStr = SignUtils.createSign(mtSignKey, params);

        List<NameValuePair> paramsInUrl = new ArrayList<>();
        paramsInUrl.add(new BasicNameValuePair("charset", charsetStr));
        paramsInUrl.add(new BasicNameValuePair("timestamp", timestampStr));
        paramsInUrl.add(new BasicNameValuePair("sign", signStr));
        paramsInUrl.add(new BasicNameValuePair("developerId", developerIdStr));
        paramsInUrl.add(new BasicNameValuePair("orderId", orderIdStr));
        paramsInUrl.add(new BasicNameValuePair("queryReasonType", queryReasonTypeStr));
        paramsInUrl.add(new BasicNameValuePair("addOtherReason", addOtherReasonStr));
        paramsInUrl.add(new BasicNameValuePair("appAuthToken", appAuthTokenStr));
        paramsInUrl.add(new BasicNameValuePair("version", versionStr));

        return paramsInUrl;
    }
}
