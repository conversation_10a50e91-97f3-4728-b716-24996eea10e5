package com.holder.saas.store.takeaway.producers.utils.sdk.jd;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.holder.saas.store.takeaway.producers.utils.sdk.jd.dto.CommonRspDTO;
import com.holder.saas.store.takeaway.producers.utils.sdk.jd.dto.OrderInfoDTO;
import com.holder.saas.store.takeaway.producers.utils.sdk.jd.dto.OrderQueryReqDTO;
import com.holder.saas.store.takeaway.producers.utils.sdk.jd.dto.OrderQueryRspDTO;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @description 查询订单列表
 */
@Slf4j
public class OrderQueryRequest extends AbstractJdRequest {


    public OrderQueryRequest() {
    }

    public OrderQueryRequest(String appKey, String appSecret, String token) {
       super.appKey = appKey;
       super.appSecret = appSecret;
       super.token = token;
    }

    public List<OrderInfoDTO> execute(OrderQueryReqDTO orderQueryReq){
        try {
            String response = super.execute(JSON.toJSONString(orderQueryReq), "/order/es/query");

            CommonRspDTO<String> storeNoRsp = JSON.parseObject(response, new TypeReference<CommonRspDTO<String>>(){});
            if(!storeNoRsp.isSuccess() || storeNoRsp.getResult() == null){
                return Collections.emptyList();
            }
            OrderQueryRspDTO orderQueryRspDTO = JSON.parseObject(storeNoRsp.getResult(), OrderQueryRspDTO.class);
            return orderQueryRspDTO.getResultList();
        }catch (Exception e){
            log.error("查询商家订单列表失败",e);
        }
        return Collections.emptyList();
    }
}
