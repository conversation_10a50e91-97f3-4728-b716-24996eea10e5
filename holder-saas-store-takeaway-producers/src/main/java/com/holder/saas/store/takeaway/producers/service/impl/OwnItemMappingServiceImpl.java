package com.holder.saas.store.takeaway.producers.service.impl;

import com.holder.saas.store.takeaway.producers.service.HolderAuthService;
import com.holder.saas.store.takeaway.producers.service.UnItemMappingService;
import com.holder.saas.store.takeaway.producers.utils.ThrowableExtUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.takeaway.*;
import com.holderzone.saas.store.dto.takeaway.request.BindInfo;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutShopOwnItemBindReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.TakeoutOwnItemMappingRespDTO;
import eleme.openapi.sdk.api.entity.product.OCategory;
import eleme.openapi.sdk.api.entity.product.OItem;
import eleme.openapi.sdk.api.entity.product.OSpec;
import eleme.openapi.sdk.api.enumeration.product.OItemUpdateProperty;
import eleme.openapi.sdk.api.exception.ServiceException;
import eleme.openapi.sdk.api.exception.UnauthorizedException;
import eleme.openapi.sdk.oauth.response.Token;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service("ownItemMappingServiceImpl")
public class OwnItemMappingServiceImpl implements UnItemMappingService {

    private final HolderAuthService holderAuthService;


    @Autowired
    public OwnItemMappingServiceImpl(HolderAuthService holderAuthService) {
        this.holderAuthService = holderAuthService;
    }

    @Override
    public List<UnMappedType> getType(String storeGuid) {
        return null;
    }

    @Override
    public List<UnMappedItem> getItem(String storeGuid) {
        String replyType = "查询菜品";
        logRequestProcessing(replyType, storeGuid);
        List<UnMappedItem> data = new ArrayList<>();
        String code = holderAuthService.getCode(storeGuid);
        if (code != null) {
            //查询掌控者下所有菜品信息
            List<TakeoutOwnItemMappingRespDTO> list = holderAuthService.getItem(storeGuid);
            data = changeData(list);
        } else {
            logAuthFailureThenThrow(replyType, storeGuid);
        }
        log.info("查询菜品返回：data={}", data);
        return data;
    }

    @Override
    public List<UnMappedItem> getItems(UnItemQueryReq unItemQueryReq) {
        return Collections.emptyList();
    }


    public List<UnMappedItem> changeData(List<TakeoutOwnItemMappingRespDTO> list) {
        List<UnMappedItem> data = new ArrayList<>();
        if (ObjectUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        for (TakeoutOwnItemMappingRespDTO take : list) {
            UnMappedItem unItem = new UnMappedItem();
            //规格Id
            unItem.setUnItemSkuId(String.valueOf(take.getId()));
            //规格名称
            unItem.setUnItemSkuName(take.getName());
            //价格：放入透传字段
            unItem.setExtendValue(String.valueOf(take.getPrice()));
            //菜品ID
            unItem.setUnItemId(String.valueOf(take.getGoodsId()));
            //菜品名称
            unItem.setUnItemName(take.getName());
            //菜品类型ID
            unItem.setUnItemTypeId(String.valueOf(take.getGoodsTypeId()));
            //菜品类型名称
            unItem.setUnItemTypeName(take.getGoodsTypeName());
            //已绑定菜品sku
            unItem.setErpItemSkuId(take.getThirdSkuId());
            //已绑定菜品id
            unItem.setErpItemId(take.getThirdSkuId());
            //平台方单位名称
            unItem.setUnItemSkuName(take.getUnit());

            data.add(unItem);
        }
        return data;
    }


    @Override
    public void bindMapping(UnItemBindUnbindReq unItemBindUnbindReq) {
        handleMapping(unItemBindUnbindReq, true);
    }

    @Override
    public void unbindMapping(UnItemBindUnbindReq unItemBindUnbindReq) {
        handleMapping(unItemBindUnbindReq, false);
    }

    @Override
    public void batchUnbindMapping(UnItemBatchUnbindReq unItemBatchUnbindReq) {
        handleMapping(unItemBatchUnbindReq);
    }

    @SuppressWarnings("unchecked")
    private void handleMapping(UnItemBindUnbindReq unItemBindUnbindReq, boolean isBinding) {
        String storeGuid = unItemBindUnbindReq.getStoreGuid();
        String token = holderAuthService.getToken(holderAuthService.getHolder(storeGuid));
        if ("-1".equals(token)) {
            throw new BusinessException("门店未绑定!请绑定门店后重试!");
        }
        String code = holderAuthService.getCode(storeGuid);
        String replyType = isBinding ? "设置商品映射" : "删除商品映射";
        logRequestProcessing(replyType, storeGuid);
        if (code != null) {
            //实现菜品绑定逻辑
            TakeoutShopOwnItemBindReqDTO takeoutShopOwnItemBindReqDTO = new TakeoutShopOwnItemBindReqDTO();
            List<BindInfo> list = new ArrayList<>();
            BindInfo bind = new BindInfo();

            bind.setGoodsSpecId(Long.parseLong(unItemBindUnbindReq.getUnItemSkuId()));
            bind.setThirdSkuId(unItemBindUnbindReq.getErpItemSkuId());
            list.add(bind);
            takeoutShopOwnItemBindReqDTO.setGoodsList(list);
            takeoutShopOwnItemBindReqDTO.setStoreGuid(storeGuid);
            if (isBinding) {
                //菜品绑定
                String result = holderAuthService.itemBind(takeoutShopOwnItemBindReqDTO, code, token);
                log.info("菜品绑定,result={}", result);
            } else {
                //菜品解绑
                String result = holderAuthService.itemUnBind(takeoutShopOwnItemBindReqDTO, code, token);
                log.info("菜品解绑,result={}", result);
            }
        } else {
            logAuthFailureThenThrow(replyType, storeGuid);
        }
    }

    private void handleMapping(UnItemBatchUnbindReq unItemBatchUnbindReq) {
        String storeGuid = unItemBatchUnbindReq.getStoreGuid();
        String token = holderAuthService.getToken(holderAuthService.getHolder(storeGuid));
        if ("-1".equals(token)) {
            throw new BusinessException("门店未绑定!请绑定门店后重试!");
        }
        String code = holderAuthService.getCode(storeGuid);
        String replyType = unItemBatchUnbindReq.getBindFlag() ? "批量绑定商品映射" : "批量删除商品映射";
        logRequestProcessing(replyType, storeGuid);
        if (code != null) {
            //实现菜品批量解绑逻辑
            TakeoutShopOwnItemBindReqDTO takeoutShopOwnItemBindReqDTO = new TakeoutShopOwnItemBindReqDTO();
            List<BindInfo> list = this.changeData(unItemBatchUnbindReq);
            takeoutShopOwnItemBindReqDTO.setGoodsList(list);
            takeoutShopOwnItemBindReqDTO.setStoreGuid(storeGuid);
            //菜品批量解绑
            if (unItemBatchUnbindReq.getBindFlag()) {
                String result = holderAuthService.itemBind(takeoutShopOwnItemBindReqDTO, code, token);
                log.info("菜品批量绑定,result={}", result);
            } else {
                String result = holderAuthService.itemUnBind(takeoutShopOwnItemBindReqDTO, code, token);
                log.info("菜品批量解绑,result={}", result);
            }
        } else {
            logAuthFailureThenThrow(replyType, storeGuid);
        }
    }

    public List<BindInfo> changeData(UnItemBatchUnbindReq unItemBatchUnbindReq) {
        List<BindInfo> data = new ArrayList<>();
        List<UnItemBaseMapReq> itemList = unItemBatchUnbindReq.getUnItemUnbindList();
        for (UnItemBaseMapReq unItem : itemList) {
            BindInfo bindInfo = new BindInfo();
            bindInfo.setGoodsSpecId(Long.parseLong(unItem.getUnItemSkuId()));
            bindInfo.setThirdSkuId(unItem.getErpItemSkuId());
            data.add(bindInfo);
        }
        return data;
    }

    private void executeRequest(Token token, UnItemBindUnbindReq unItemBindUnbindReq,
                                boolean isBinding, String replyType, String storeGuid, Map<String, String> extendValueMap) {

    }

    private void logRequestProcessing(String msg, String storeGuid) {
        log.info("Request(自营外卖平台){}，storeGuid: {}，处理中", msg, storeGuid);
    }

    private void logRequestDataSucceed(String msg, String storeGuid, Object obj) {
        log.info("Request(自营外卖平台){}，storeGuid: {}，查询成功：{}",
                msg, storeGuid, JacksonUtils.writeValueAsString(obj));
    }

    private void logReplyDataSucceed(String msg, String storeGuid, Object obj) {
        log.info("Request(自营外卖平台){}，storeGuid: {}，处理成功，处理结果：{}"
                , msg, storeGuid, JacksonUtils.writeValueAsString(obj));
    }

    private void logExceptionThenThrow(String msg, String storeGuid, ServiceException e) {
        if (log.isErrorEnabled()) {
            log.error("Request(自营外卖平台){}，storeGuid: {}，处理失败：{}",
                    msg, storeGuid, ThrowableExtUtils.asStringIfAbsent(e));
        }
        if (e instanceof UnauthorizedException) {
            throw new BusinessException("操作失败，您的自营外卖已与门店解绑，请重新登录商户后台绑定");
        }
        throw new BusinessException(ThrowableExtUtils.asStringIfAbsent(e));
    }

    private void logAuthFailureThenThrow(String msg, String storeGuid) {
        log.error("Request(自营外卖平台){}，处理失败，通过storeGuid[{}]未查询到token", msg, storeGuid);
        throw new BusinessException("操作失败，您的自营外卖已与门店解绑，请重新登录商户后台绑定");
    }

    private void logDirtyAuthFailureThenThrow(String msg, String storeGuid) {
        log.error("Request(自营外卖平台){}，处理失败，通过storeGuid[{}]未查询到shopId", msg, storeGuid);
        throw new BusinessException("操作失败，您的自营外卖已与门店解绑，请重新登录商户后台绑定");
    }

    private Long getShopIdByStoreGuid(String storeGuid) {
        return 1L;
    }

    private List<UnMappedType> eleType2ErpType(List<OCategory> shopCategories) {
        if (CollectionUtils.isEmpty(shopCategories)) {
            return Collections.emptyList();
        }
        return shopCategories.stream()
                .map(oShopCategory -> {
                    UnMappedType unMappedType = new UnMappedType();
                    unMappedType.setUnItemTypeId(String.valueOf(oShopCategory.getId()));
                    unMappedType.setUnItemTypeName(oShopCategory.getName());
                    return unMappedType;
                })
                .collect(Collectors.toList());
    }


}
