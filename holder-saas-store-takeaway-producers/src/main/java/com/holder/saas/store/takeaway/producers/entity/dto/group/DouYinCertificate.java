package com.holder.saas.store.takeaway.producers.entity.dto.group;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-06-20
 * @description
 */
@Data
public class DouYinCertificate {

    /**
     * 加密券码, 在验券接口传入
     */
    @JSONField(name = "encrypted_code")
    private String encryptedCode;

    /**
     * 券码有效期，截至时间，时间戳，单位秒
     */
    @JSONField(name = "expire_time")
    private Integer expireTime;

    /**
     * 次卡次数
     */
    @JSONField(name = "time_card")
    private TimeCard timeCard;

    /**
     * 券维度金额信息
     */
    private Amount amount;

    /**
     * 团购信息
     */
    private Sku sku;

    @Data
    public static class Sku{
        /**
         * 团购SKU ID
         */
        @JSONField(name = "sku_id")
        private String skuId;

        /**
         * 团购名称
         */
        private String title;

        /**
         * 团购类型（type=1团餐券; type=2代金券; type=3次卡）
         */
        @JSONField(name = "groupon_type")
        private Integer grouponType;

        /**
         * 团购市场价，单位分
         */
        @JSONField(name = "market_price")
        private Integer marketPrice;

        /**
         * 团购售卖开始时间，时间戳，单位秒
         */
        @JSONField(name = "sku_id")
        private Integer soldStartTime;

        /**
         * 商家系统（第三方）团购id
         */
        @JSONField(name = "third_sku_id")
        private String thirdSkuId;

        /**
         * 商家团购账号id
         */
        @JSONField(name = "account_id")
        private String accountId;
    }

    @Data
    public static class Amount{

        /**
         * 券原始金额，单位分
         */
        @JSONField(name = "original_amount")
        private Integer originalAmount;

        /**
         * 券划线价金额，单位分
         */
        @JSONField(name = "list_market_amount")
        private Integer listMarketAmount;

        /**
         * 用户实付金额，单位分
         */
        @JSONField(name = "pay_amount")
        private Integer payAmount;

        /**
         * 商家营销金额，单位分
         */
        @JSONField(name = "merchant_ticket_amount")
        private Integer merchantTicketAmount;

        /**
         * 支付优惠金额，单位分
         */
        @JSONField(name = "payment_discount_amount")
        private Integer paymentDiscountAmount;

        /**
         * 平台优惠金额，单位分
         */
        @JSONField(name = "platform_discount_amount")
        private Integer platformDiscountAmount;

        /**
         * 券实付金额（=用户实付金额+支付优惠金额），单位分
         */
        @JSONField(name = "coupon_pay_amount")
        private Integer couponPayAmount;

    }

    @Data
    public static class TimeCard{

        /**
         * 次卡总次数
         */
        @JSONField(name = "times_count")
        private Integer timesCount;

        /**
         * 次卡已使用次数
         */
        @JSONField(name = "times_used")
        private Integer timesUsed;

        /**
         * 次卡次序号维度的 金额明细
         */
        @JSONField(name = "serial_amount_list")
        private List<SerialAmount> serialAmountList;

    }

    @Data
    public static class SerialAmount{
        /**
         * 次序号
         */
        @JSONField(name = "serial_numb")
        private Integer serialNumb;

        private Amount amount;

    }
}
