package com.holder.saas.store.takeaway.producers.utils.sdk.jd;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.holder.saas.store.takeaway.producers.utils.sdk.jd.dto.CommonRspDTO;
import lombok.extern.slf4j.Slf4j;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @description 根据到家门店编码查询门店基本信息接
 */
@Slf4j
public class GetStationsByVenderIdRequest extends AbstractJdRequest{


    public GetStationsByVenderIdRequest() {
    }

    public GetStationsByVenderIdRequest(String appKey, String appSecret, String token) {
       super.appKey = appKey;
       super.appSecret = appSecret;
       super.token = token;
    }

    public List<String> execute(){
        try {
            String response = super.execute(null, "/store/getStationsByVenderId");

            CommonRspDTO<List<String>> storeNoRsp = JSON.parseObject(response, new TypeReference<CommonRspDTO<List<String>>>(){});
            if(!storeNoRsp.isSuccess()){
                return Collections.emptyList();
            }
            return storeNoRsp.getResult();
        }catch (Exception e){
            log.error("查询商家商品列表失败",e);
        }
        return Collections.emptyList();
    }
}
