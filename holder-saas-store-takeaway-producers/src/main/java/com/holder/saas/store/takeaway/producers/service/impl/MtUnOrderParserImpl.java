package com.holder.saas.store.takeaway.producers.service.impl;

import com.holder.saas.store.takeaway.producers.entity.dto.MtOrderStatus;
import com.holder.saas.store.takeaway.producers.mapstruct.MtOrderMapstruct;
import com.holder.saas.store.takeaway.producers.service.ErpGuidCacheService;
import com.holder.saas.store.takeaway.producers.service.MtOrderService;
import com.holder.saas.store.takeaway.producers.service.MtUnOrderParser;
import com.holder.saas.store.takeaway.producers.utils.ThrowableExtUtils;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.takeaway.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class MtUnOrderParserImpl implements MtUnOrderParser {

    private final MtOrderMapstruct mtOrderMapstruct;

    private final ErpGuidCacheService erpGuidCacheService;

    private final MtOrderService mtOrderService;

    @Autowired
    public MtUnOrderParserImpl(MtOrderMapstruct mtOrderMapstruct, ErpGuidCacheService erpGuidCacheService, MtOrderService mtOrderService) {
        this.mtOrderMapstruct = mtOrderMapstruct;
        this.erpGuidCacheService = erpGuidCacheService;
        this.mtOrderService = mtOrderService;
    }

    @Override
    public UnOrder fromMtQueryOrderDetail(MtQueryOrderDetail mtQueryOrderDetail) {
        UnOrder unOrder = buildUnOrderFromMtQueryOrderDetail(mtQueryOrderDetail);
        log.info("(美团)新订单，orderId: {}", mtQueryOrderDetail.getOrderId());
        return convertOrderToUnOrder(unOrder, mtOrderMapstruct.fromMtQueryOrderDetail(mtQueryOrderDetail));
    }

    @Override
    public UnOrder fromMtCbOrderCreated(MtCallbackDTO mtCallbackDTO) {
        UnOrder unOrder = buildUnOrderFromMtCallbackDTO(mtCallbackDTO);

        // 设置订单详情
        MtCbOrderDetail order = JacksonUtils.toObject(MtCbOrderDetail.class, mtCallbackDTO.getOrder());
        unOrder.setShopId(order.getPoiId());
        unOrder.setShopName(order.getPoiName());
        unOrder.setOrderId(String.valueOf(order.getOrderId()));
        log.info("(美团)新订单，orderId: {}", order.getOrderId());
        int orderStatus = order.getStatus();
        if (orderStatus == MtOrderStatus.ORDER_SUBMIT || orderStatus == MtOrderStatus.ORDER_PUSH_APP_MCHNT) {
            return convertOrderToUnOrder(unOrder, order);
        }
        if (orderStatus == MtOrderStatus.ORDER_CONFIRMED) {
            unOrder.setOrderId(String.valueOf(order.getOrderId()));
            unOrder.setCbMsgType(UnOrderCbMsgType.ORDER_CONFIRMED);
            return unOrder;
        }
        // 只处理“用户已提交订单”、“商家已确认”两种情况
        return null;
    }

    @Override
    public UnOrder fromMtCbOrderCanceled(MtCallbackDTO mtCallbackDTO) {
        UnOrder unOrder = buildUnOrderFromMtCallbackDTO(mtCallbackDTO);

        MtCbOrderCancelDetail orderCancel = JacksonUtils.toObject(MtCbOrderCancelDetail.class, mtCallbackDTO.getOrderCancel());
        log.info("(美团)取消订单，orderId: {}", orderCancel.getOrderId());
        unOrder.setOrderId(orderCancel.getOrderId());
        LocalDateTime cancelTime = mtCallbackDTO.getTimestamp() != null
                ? DateTimeUtils.mills2LocalDateTime(mtCallbackDTO.getTimestamp())
                : DateTimeUtils.now();
        unOrder.setCancelTime(cancelTime);
        unOrder.setPart(false);
        unOrder.setCancelReason(orderCancel.getReason());
        switch (MtCbOcReasonCodeEnum.ofCode(orderCancel.getReasonCode())) {
            case 超时未确认:
            case 在线支付订单30分钟未支付:
                unOrder.setCancelRoleName("美团系统");
                break;
            case 在线支付中取消:
            case 商家确认前取消:
            case 用户退款取消:
                unOrder.setCancelRoleName("美团用户");
                break;
            case 未知原因:
                unOrder.setCancelReason("用户取消");
                unOrder.setCancelRoleName("美团用户");
                break;
            default:
                unOrder.setCancelRoleName("美团客服");
                break;
        }

        // 美团的取消指“接单1分钟前”，与饿了么的“订单完结前不同”
        // https://developer.meituan.com/openapi#7.1.2
        // 三、订单退款
        // 1、外卖退款全流程
        unOrder.setCbMsgType(UnOrderCbMsgType.ORDER_CANCELED);

        return unOrder;
    }

    @Override
    public UnOrder fromMtCbOrderRefund(MtCallbackDTO mtCallbackDTO) {
        UnOrder unOrder = buildUnOrderFromMtCallbackDTO(mtCallbackDTO);

        MtCbOrderRefundDetail orderRefund = JacksonUtils.toObject(
                MtCbOrderRefundDetail.class, mtCallbackDTO.getOrderRefund());
        log.info("(美团)退款，orderId: {}", orderRefund.getOrderId());
        unOrder.setOrderId(orderRefund.getOrderId());
        unOrder.setPart(false);

        // 设置消息类型
        switch (MtCbOrNotifyTypeEnum.ofType(orderRefund.getNotifyType())) {
            case APPLY:
                unOrder.setCbMsgType(UnOrderCbMsgType.ORDER_REFUND_REQ);
                LocalDateTime refundReqTime = mtCallbackDTO.getTimestamp() != null
                        ? DateTimeUtils.mills2LocalDateTime(mtCallbackDTO.getTimestamp())
                        : DateTimeUtils.now();
                unOrder.setRefundReqTime(refundReqTime);
                unOrder.setRefundReqReason(orderRefund.getReason());
                break;
            case AGREE:
                unOrder.setCbMsgType(UnOrderCbMsgType.ORDER_REFUND_AGREED);
                break;
            case REJECT:
                unOrder.setCbMsgType(UnOrderCbMsgType.ORDER_REFUND_DISAGREED);
                break;
            case CANCEL_REFUND:
                unOrder.setCbMsgType(UnOrderCbMsgType.ORDER_CANCEL_REFUND_REQ);
                break;
            default:
                unOrder.setCbMsgType(UnOrderCbMsgType.ORDER_CANCEL_REFUND_REQ);
                break;
        }

        return unOrder;
    }

    @Override
    public UnOrder fromMtCbOrderPartRefund(MtCallbackDTO mtCallbackDTO) {
        UnOrder unOrder = buildUnOrderFromMtCallbackDTO(mtCallbackDTO);

        MtCbOrderPartRefundDetail partOrderRefund = JacksonUtils.toObject(
                MtCbOrderPartRefundDetail.class, mtCallbackDTO.getPartOrderRefund());
        log.info("(美团)部分退款，orderId: {}", partOrderRefund.getOrderId());
        unOrder.setOrderId(partOrderRefund.getOrderId());
        unOrder.setPart(true);

        // 设置消息类型
        switch (MtCbOprNotifyTypeEnum.ofType(partOrderRefund.getNotifyType())) {
            case PART:
                unOrder.setCbMsgType(UnOrderCbMsgType.ORDER_REFUND_REQ);
                LocalDateTime cancelReqTime = mtCallbackDTO.getTimestamp() != null
                        ? DateTimeUtils.mills2LocalDateTime(mtCallbackDTO.getTimestamp())
                        : DateTimeUtils.now();
                unOrder.setRefundReqTime(cancelReqTime);
                unOrder.setRefundReqReason(partOrderRefund.getReason());
                unOrder.setCustomerRefund(new BigDecimal(partOrderRefund.getMoney()));
                List<MtCbOrderPartRefundDetail.FoodBean> foodBeans = JacksonUtils.toObjectList(
                        MtCbOrderPartRefundDetail.FoodBean.class, partOrderRefund.getFood());
                unOrder.setCustomerRefundItem(foodBeans.stream()
                        .map(foodBean -> foodBean.getFood_name() + "*" + foodBean.getCount())
                        .collect(Collectors.joining("，")));
                // 退菜明细
                unOrder.setArrayOfUnRefundItem(parseUnRefundItems(foodBeans));
                break;
            case AGREE:
                unOrder.setCbMsgType(UnOrderCbMsgType.ORDER_REFUND_AGREED);
                break;
            case REJECT:
                unOrder.setCbMsgType(UnOrderCbMsgType.ORDER_REFUND_DISAGREED);
                break;
            default:
                unOrder.setCbMsgType(UnOrderCbMsgType.ORDER_CANCEL_REFUND_REQ);
                break;
        }

        return unOrder;
    }

    @Override
    public UnOrder fromMtCbOrderConfirmed(MtCallbackDTO mtCallbackDTO) {
        UnOrder unOrder = buildUnOrderFromMtCallbackDTO(mtCallbackDTO);

        MtCbOrderDetail order = JacksonUtils.toObject(MtCbOrderDetail.class, mtCallbackDTO.getOrder());
        log.info("(美团)接单，orderId: {}", order.getOrderId());
        unOrder = convertOrderToUnOrder(unOrder, order);
        unOrder.setShopId(order.getPoiId());
        unOrder.setShopName(order.getPoiName());
        unOrder.setOrderId(String.valueOf(order.getOrderId()));
        unOrder.setAcceptTime(DateTimeUtils.mills2LocalDateTime(order.getUtime() * 1000));
        unOrder.setCbMsgType(UnOrderCbMsgType.ORDER_CONFIRMED);

        return unOrder;
    }

    @Override
    public UnOrder fromMtCbOrderFinished(MtCallbackDTO mtCallbackDTO) {
        UnOrder unOrder = buildUnOrderFromMtCallbackDTO(mtCallbackDTO);

        MtCbOrderDetail order = JacksonUtils.toObject(MtCbOrderDetail.class, mtCallbackDTO.getOrder());
        log.info("(美团)订单完成，orderId: {}", order.getOrderId());
        unOrder.setShopId(order.getPoiId());
        unOrder.setShopName(order.getPoiName());
        unOrder.setOrderId(String.valueOf(order.getOrderId()));
        unOrder.setCompleteTime(DateTimeUtils.mills2LocalDateTime(order.getUtime() * 1000));
        unOrder.setCbMsgType(UnOrderCbMsgType.ORDER_FINISHED);

        return unOrder;
    }

    @Override
    public UnOrder fromMtCbShippingStatus(MtCallbackDTO mtCallbackDTO) {
        MtCbShippingStatusDetail shippingStatus = JacksonUtils.toObject(
                MtCbShippingStatusDetail.class, mtCallbackDTO.getShippingStatus()
        );
        MtCbShippingStatusEnum mtCbShippingStatusEnum =
                MtCbShippingStatusEnum.ofStatus(shippingStatus.getShippingStatus());
        if (!MtCbShippingStatusEnum.骑手已取餐.equals(mtCbShippingStatusEnum)
                && !MtCbShippingStatusEnum.骑手已送达.equals(mtCbShippingStatusEnum)) {
            return null;
        }

        UnOrder unOrder = buildUnOrderFromMtCallbackDTO(mtCallbackDTO);

        unOrder.setOrderId(shippingStatus.getOrderId());
        unOrder.setShipperName(shippingStatus.getDispatcherName());
        unOrder.setShipperPhone(shippingStatus.getDispatcherMobile());

        if (MtCbShippingStatusEnum.骑手已取餐.equals(mtCbShippingStatusEnum)) {
            log.info("(美团)订单配送中，orderId: {}", shippingStatus.getOrderId());
            unOrder.setCbMsgType(UnOrderCbMsgType.ORDER_SHIPPING);
            unOrder.setDeliveryTime(DateTimeUtils.mills2LocalDateTime(shippingStatus.getTime() * 1000));
            return unOrder;
        }

        log.info("(美团)订单配送完成，orderId: {}", shippingStatus.getOrderId());
        unOrder.setCbMsgType(UnOrderCbMsgType.ORDER_SHIP_SUCCEED);
        unOrder.setDeliveredTime(DateTimeUtils.mills2LocalDateTime(shippingStatus.getTime() * 1000));
        return unOrder;
    }

    private UnOrder buildUnOrderFromMtCallbackDTO(MtCallbackDTO mtCallbackDTO) {
        return buildUnOrderFromEPoiId(mtCallbackDTO.getEPoiId(), null, null);
    }

    private UnOrder buildUnOrderFromMtQueryOrderDetail(MtQueryOrderDetail mtQueryOrderDetail) {
        return buildUnOrderFromEPoiId(mtQueryOrderDetail.getEPoiId(),
                mtQueryOrderDetail.getPoiId(), mtQueryOrderDetail.getPoiName());
    }

    private UnOrder buildUnOrderFromEPoiId(String ePoiId, Long poiId, String poiName) {
        String enterpriseGuid = erpGuidCacheService.getEnterpriseGuid(ePoiId);

        UnOrder unOrder = new UnOrder();

        // 设置企业Guid、门店Guid
        unOrder.setShopId(poiId);
        unOrder.setShopName(poiName);
        unOrder.setStoreGuid(ePoiId);
        unOrder.setEnterpriseGuid(enterpriseGuid);

        // 设置订单类型
        unOrder.setOrderType(OrderType.TAKEOUT_ORDER.getType());
        unOrder.setOrderSubType(OrderType.TakeoutSubType.MT_TAKEOUT.getType());

        return unOrder;
    }

    private UnOrder convertOrderToUnOrder(UnOrder unOrder, MtCbOrderDetail mtCbOrderDetail) {
        unOrder.setOrderId(String.valueOf(mtCbOrderDetail.getOrderId()));
        unOrder.setShopName(mtCbOrderDetail.getPoiName());
        unOrder.setTotal(BigDecimal.valueOf(mtCbOrderDetail.getOriginalPrice()));
        unOrder.setOrderViewId(String.valueOf(mtCbOrderDetail.getOrderIdView()));
        unOrder.setOrderDaySn(mtCbOrderDetail.getDaySeq());
        long createTime = mtCbOrderDetail.getCtime() * 1000;
        unOrder.setCreateTime(DateTimeUtils.mills2LocalDateTime(createTime));
        long deliveryTime = mtCbOrderDetail.getDeliveryTime() * 1000;
        //是否是预订单：美团外卖：立即送达delivery=0;    饿了么外卖：立即送达delivery=null
        log.info("(美团)订单[{}]deliveryTime={}", mtCbOrderDetail.getOrderId(), mtCbOrderDetail.getDeliveryTime());
        if (mtCbOrderDetail.getDeliveryTime() == 0) {
            log.info("(美团)订单[{}]非预定单", mtCbOrderDetail.getOrderId());
            unOrder.setReserve(Boolean.FALSE);
        } else {
            log.info("(美团)订单[{}]是预定单", mtCbOrderDetail.getOrderId());
            unOrder.setReserve(Boolean.TRUE);
        }
        unOrder.setDinnersNumber(mtCbOrderDetail.getDinnersNumber());
        unOrder.setEstimateDeliveredTime(DateTimeUtils.mills2LocalDateTime(deliveryTime));
        unOrder.setCreateTime(DateTimeUtils.mills2LocalDateTime(mtCbOrderDetail.getUtime() * 1000));
        unOrder.setActiveTime(DateTimeUtils.mills2LocalDateTime(deliveryTime == 0 ? createTime : deliveryTime));
        unOrder.setFirstOrder(mtCbOrderDetail.isPoiFirstOrder());
        unOrder.setCustomerName(mtCbOrderDetail.getRecipientName());
        unOrder.setCustomerAddress(mtCbOrderDetail.getRecipientAddress());
        unOrder.setRecipientAddressDesensitization(mtCbOrderDetail.getRecipientAddressDesensitization());
        // 处理customerAddress,用真实地址替换脱敏地址
        // replaceCustomerAddress(unOrder);
        String backupRecipientPhone = mtCbOrderDetail.getBackupRecipientPhone();
        if (!StringUtils.hasText(backupRecipientPhone) && !StringUtils.isEmpty(mtCbOrderDetail.getRecipientPhone())) {
            List<String> customerS = new ArrayList<>();
            String phone = mtCbOrderDetail.getRecipientPhone();
            phone = phone.replaceAll("_", "&");
            customerS.add(phone);
            unOrder.setCustomerPhone(JacksonUtils.writeValueAsString(customerS));
        } else {
            try {
                List<String> backupRecipientPhones = JacksonUtils.toObjectList(String.class, backupRecipientPhone);
                if (!ObjectUtils.isEmpty(backupRecipientPhones)) {
                    List<String> data = new ArrayList<>();
                    for (String phone : backupRecipientPhones) {
                        phone = phone.replaceAll("_", "&");
                        data.add(phone);
                    }
                    unOrder.setPrivacyPhone(JacksonUtils.writeValueAsString(data));
                } else {
                    List<String> customerS = new ArrayList<>();
                    customerS.add(mtCbOrderDetail.getRecipientPhone());
                    String phone = mtCbOrderDetail.getRecipientPhone();
                    phone = phone.replaceAll("_", "&");
                    customerS.add(phone);
                    unOrder.setCustomerPhone(JacksonUtils.writeValueAsString(customerS));
                }
            } catch (Exception e) {
                log.error("隐私号反序列化出错，backupRecipientPhone: {}，e: {}",
                        backupRecipientPhone, ThrowableExtUtils.asStringIfAbsent(e));
                List<String> customerS = new ArrayList<>();
                customerS.add(mtCbOrderDetail.getRecipientPhone());
                unOrder.setCustomerPhone(JacksonUtils.writeValueAsString(customerS));
            }
        }
        unOrder.setCustomerNumber(1);
        unOrder.setShipLatitude(String.valueOf(mtCbOrderDetail.getLatitude()));
        unOrder.setShipLongitude(String.valueOf(mtCbOrderDetail.getLongitude()));
        unOrder.setThirdShipper(!MtCbLogisticsCodeEnum.商家自配送.equals(MtCbLogisticsCodeEnum.ofCode(mtCbOrderDetail.getLogisticsCode())));
        if (mtCbOrderDetail.getHasInvoiced() == 1) {
            unOrder.setInvoiced(Boolean.TRUE);
        } else {
            unOrder.setInvoiced(Boolean.FALSE);
        }
        unOrder.setInvoiceTitle(mtCbOrderDetail.getInvoiceTitle());
        unOrder.setTaxpayerId(mtCbOrderDetail.getTaxpayerId());
        unOrder.setShipTotal(BigDecimal.valueOf(mtCbOrderDetail.getShippingFee()).setScale(2, BigDecimal.ROUND_HALF_UP));
        BigDecimal packageMoney = new BigDecimal(0);
        BigDecimal foodTotal = new BigDecimal(0);
        // 订单商品明细
        List<UnItem> unItemList = new ArrayList<>();
        List<MtCbOrderDetail.DetailBean> detailBeans = JacksonUtils.toObjectList(
                MtCbOrderDetail.DetailBean.class, mtCbOrderDetail.getDetail());
        for (MtCbOrderDetail.DetailBean detailBean : detailBeans) {
            //
            BigDecimal boxNum = BigDecimal.valueOf(detailBean.getBox_num());
            BigDecimal boxPrice = BigDecimal.valueOf(detailBean.getBox_price());
            BigDecimal money = boxNum.multiply(boxPrice);
            packageMoney = packageMoney.add(money);
            //
            BigDecimal quantity = BigDecimal.valueOf(detailBean.getQuantity());
            BigDecimal price = BigDecimal.valueOf(detailBean.getPrice());
            BigDecimal total = price.multiply(quantity);
            foodTotal = foodTotal.add(total);

            UnItem unItem = new UnItem();
            unItem.setItemCode(detailBean.getApp_food_code());

            // 平台             菜品名    规格    属性
            // 美团单品无属性   小火锅    null    null
            // 美团规格有属性   西瓜      小份    热
            // 这里需要把规格append到菜品名称里
            String spec = !StringUtils.isEmpty(detailBean.getSpec()) ? "(" + detailBean.getSpec() + ")" : "";
            unItem.setItemName(detailBean.getFood_name() + spec);
            unItem.setItemSku(detailBean.getSku_id());
            unItem.setItemUnit(detailBean.getUnit());
            unItem.setItemCount(quantity);
            unItem.setItemPrice(price);
            unItem.setItemTotal(total);
            unItem.setBoxCount(boxNum);
            unItem.setBoxPrice(boxPrice);
            unItem.setBoxTotal(money);
            BigDecimal foodDiscount = BigDecimal.valueOf(detailBean.getFood_discount());
            unItem.setDiscountRatio(foodDiscount);
            unItem.setDiscountPrice(price.multiply(foodDiscount));
            unItem.setDiscountTotal(total.multiply(foodDiscount));
            unItem.setItemSpec(detailBean.getSpec());
            unItem.setItemProperty(detailBean.getFood_property());
            unItem.setCartId(detailBean.getCart_id());
            unItem.setActualPrice(quantity.multiply(BigDecimal.valueOf(detailBean.getActual_price())));
            unItem.setUnItemSkuId(detailBean.getSku_id());
            unItem.setThirdSkuId(detailBean.getMt_sku_id());
            List<UnItem> subItemList = getSubItemList(detailBean.getSub_detail_list(), detailBean);
            unItem.setSubItemList(subItemList);
            unItemList.add(unItem);
        }
        unOrder.setItemTotal(foodTotal);
        unOrder.setPackageTotal(packageMoney);
        unOrder.setArrayOfUnItem(unItemList);

        BigDecimal reduceFeeCount = new BigDecimal(0);
        BigDecimal poiChargeCount = new BigDecimal(0);
        BigDecimal mtChargeCount = new BigDecimal(0);
        List<UnDiscount> unDiscountList = new ArrayList<>();
        List<MtCbOrderDetail.ExtraBean> extraBeans = JacksonUtils.toObjectList(
                MtCbOrderDetail.ExtraBean.class, mtCbOrderDetail.getExtras());
        for (MtCbOrderDetail.ExtraBean extraBean : extraBeans) {
            BigDecimal reduceFee = BigDecimal.valueOf(extraBean.getReduce_fee());
            BigDecimal poiCharge = BigDecimal.valueOf(extraBean.getPoi_charge());
            BigDecimal mtCharge = BigDecimal.valueOf(extraBean.getMt_charge());
            reduceFeeCount = reduceFeeCount.add(reduceFee);
            poiChargeCount = poiChargeCount.add(poiCharge);
            mtChargeCount = mtChargeCount.add(mtCharge);
            UnDiscount orderDiscount = new UnDiscount();
            orderDiscount.setDiscountName(extraBean.getRemark());
            orderDiscount.setDiscountRemark(extraBean.getRemark());
            orderDiscount.setType(extraBean.getType());
            orderDiscount.setTotalDiscount(reduceFee);
            orderDiscount.setEnterpriseDiscount(poiCharge);
            orderDiscount.setPlatformDiscount(mtCharge);
            unDiscountList.add(orderDiscount);
            //美团拼好饭标识
            if (extraBean.getType() == 62){
                unOrder.setBusinessType(1);
            }
        }
        unOrder.setDiscountTotal(reduceFeeCount);
        unOrder.setPlatformDiscount(mtChargeCount);
        unOrder.setEnterpriseDiscount(poiChargeCount);
        unOrder.setArrayOfUnDiscount(unDiscountList);

        unOrder.setCustomerActualPay(BigDecimal.valueOf(mtCbOrderDetail.getTotal()));
        // 门店收款,顾客实际支付的金额扣除服务费(平台抽成)考虑扣除配送费?

        MtCbOrderDetail.PoiReceiveDetailBean poiReceiveDetail = JacksonUtils.toObject(
                MtCbOrderDetail.PoiReceiveDetailBean.class, mtCbOrderDetail.getPoiReceiveDetail());
        unOrder.setShopTotal(BigDecimal.valueOf(poiReceiveDetail.getWmPoiReceiveCent())
                .setScale(2, BigDecimal.ROUND_HALF_UP)
                .divide(BigDecimal.valueOf(100.0), BigDecimal.ROUND_HALF_UP)
                .setScale(2, BigDecimal.ROUND_HALF_UP));
        // 平台抽成
        BigDecimal foodShareFeeChargeByPoi = BigDecimal.valueOf(poiReceiveDetail.getFoodShareFeeChargeByPoi())
                .setScale(2, BigDecimal.ROUND_HALF_UP);
        unOrder.setServiceFeeRate(foodShareFeeChargeByPoi);
        unOrder.setServiceFee(foodShareFeeChargeByPoi
                .divide(BigDecimal.valueOf(100), BigDecimal.ROUND_HALF_UP)
                .setScale(2, BigDecimal.ROUND_HALF_UP));
        // 支付方式
        unOrder.setOnlinePay(2 == mtCbOrderDetail.getPayType());
        // 备注
        unOrder.setOrderRemark(mtCbOrderDetail.getCaution());

        unOrder.setCbMsgType(UnOrderCbMsgType.ORDER_ACTIVATED);

        return unOrder;
    }

    private List<UnItem> getSubItemList(List<MtCbOrderDetail.SubDetailBean> subDetailList,
                                        MtCbOrderDetail.DetailBean detailBean) {
        List<UnItem> subItemList = new ArrayList<>();
        if (org.springframework.util.CollectionUtils.isEmpty(subDetailList)) {
            return subItemList;
        }
        for (MtCbOrderDetail.SubDetailBean subDetailBean : subDetailList) {
            UnItem unSubItem = new UnItem();
            unSubItem.setItemCode(subDetailBean.getApp_food_code());
            BigDecimal quantity = BigDecimal.valueOf(subDetailBean.getQuantity());
            BigDecimal boxNum = BigDecimal.valueOf(subDetailBean.getBox_num());
            BigDecimal boxPrice = BigDecimal.valueOf(subDetailBean.getBox_price());
            BigDecimal money = boxNum.multiply(boxPrice);
            BigDecimal price = BigDecimal.valueOf(subDetailBean.getPrice());
            BigDecimal total = price.multiply(quantity);

            // 平台             菜品名    规格    属性
            // 美团单品无属性   小火锅    null    null
            // 美团规格有属性   西瓜      小份    热
            // 这里需要把规格append到菜品名称里
            String spec = !StringUtils.isEmpty(subDetailBean.getSpec()) ? "(" + subDetailBean.getSpec() + ")" : "";
            unSubItem.setItemName(subDetailBean.getFood_name() + spec);
            unSubItem.setItemSku(subDetailBean.getSku_id());
            unSubItem.setItemUnit(subDetailBean.getUnit());
            unSubItem.setItemCount(quantity);
            unSubItem.setItemPrice(price);
            unSubItem.setItemTotal(total);
            unSubItem.setBoxCount(boxNum);
            unSubItem.setBoxPrice(boxPrice);
            unSubItem.setBoxTotal(money);
            unSubItem.setItemSpec(subDetailBean.getSpec());
            unSubItem.setItemProperty(subDetailBean.getFood_property());
            unSubItem.setCartId(detailBean.getCart_id());
            unSubItem.setActualPrice(quantity.multiply(BigDecimal.valueOf(subDetailBean.getActual_price())));
            unSubItem.setUnItemSkuId(subDetailBean.getSku_id());
            unSubItem.setThirdSkuId(subDetailBean.getMt_sku_id());
            unSubItem.setParentItemCode(detailBean.getApp_food_code());
            unSubItem.setParentItemSku(detailBean.getSku_id());
            subItemList.add(unSubItem);
        }
        return subItemList;
    }


    private void replaceCustomerAddress(UnOrder unOrder) {
        String customerAddress = unOrder.getCustomerAddress();
        if (StringUtils.isEmpty(customerAddress) || !customerAddress.contains("@#")) {
            return;
        }
        // 查询真实地址
        try {
            String realRecipientAddress = mtOrderService.getRealRecipientAddress(unOrder);
            unOrder.setRealRecipientAddress(realRecipientAddress);
        } catch (Exception e) {
            log.error("查询真实地址异常:{}", e.getMessage());
        }
        String realRecipientAddress = unOrder.getRealRecipientAddress();
        if (StringUtils.isEmpty(realRecipientAddress) || realRecipientAddress.contains("为保护顾客隐私，地址已隐藏")) {
            return;
        }
        // 替换
        String[] split = customerAddress.split("@#");
        unOrder.setCustomerAddress(realRecipientAddress + "@#" + split[1]);
    }


    private List<UnRefundItem> parseUnRefundItems(List<MtCbOrderPartRefundDetail.FoodBean> foodBeans) {
        if (CollectionUtils.isEmpty(foodBeans)) {
            return null;
        }
        return foodBeans.stream()
                .map(item -> {
                    BigDecimal boxNum = BigDecimal.valueOf(item.getBox_num());
                    BigDecimal boxPrice = BigDecimal.valueOf(item.getBox_price());
                    BigDecimal money = boxNum.multiply(boxPrice);
                    BigDecimal quantity = BigDecimal.valueOf(item.getCount());
                    BigDecimal price = BigDecimal.valueOf(item.getOrigin_food_price());
                    BigDecimal total = price.multiply(quantity);
                    BigDecimal refundPrice = BigDecimal.valueOf(item.getRefund_price());

                    UnRefundItem refundItem = new UnRefundItem();

                    // 平台             菜品名    规格    属性
                    // 美团单品无属性   小火锅    null    null
                    // 美团规格有属性   西瓜      小份    热
                    // 这里需要把规格append到菜品名称里
                    String spec = !StringUtils.isEmpty(item.getSpec()) ? "(" + item.getSpec() + ")" : "";
                    refundItem.setItemName(item.getFood_name() + spec);
                    refundItem.setItemCount(quantity);
                    refundItem.setItemPrice(price);
                    refundItem.setItemTotal(total);
                    refundItem.setBoxCount(boxNum);
                    refundItem.setBoxPrice(boxPrice);
                    refundItem.setBoxTotal(money);
                    refundItem.setItemSpec(item.getSpec());
                    refundItem.setItemProperty(item.getFood_property());
                    refundItem.setThirdSkuId(item.getMt_sku_id());
                    refundItem.setRefundPrice(refundPrice);
                    return refundItem;
                })
                .collect(Collectors.toList());
    }
}
