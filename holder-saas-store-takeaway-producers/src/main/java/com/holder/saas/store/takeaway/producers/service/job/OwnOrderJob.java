package com.holder.saas.store.takeaway.producers.service.job;

import com.holder.saas.store.takeaway.producers.utils.ThrowableExtUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;


@Slf4j
@Component
public class OwnOrderJob {

    @Value("${own.APP_ID}")
    private Integer appId;

    @Async
    public void queryUnProcessOrders() {
        log.info("<==========(自营外卖平台)订单轮询，最近10分钟内未被商家确认的饿了么订单列表刷新开始==========>");
        try {
            for (int i = 0; i < 20; i++) {
                log.info("测试：自营外卖平台订单轮询第{}次", i);
            }
        } catch (Exception e) {
            log.error("(自营外卖平台)订单轮询，发生异常：{}", ThrowableExtUtils.asStringIfAbsent(e));
        }
        log.info("<==========(自营外卖平台)订单轮询，最近10分钟内未被商家确认的饿了么订单列表刷新结束==========>");
    }
}
