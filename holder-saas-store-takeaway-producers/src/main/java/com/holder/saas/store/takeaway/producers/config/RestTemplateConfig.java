package com.holder.saas.store.takeaway.producers.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.web.client.RestTemplate;
import java.nio.charset.StandardCharsets;
import java.util.List;


/**
 * <AUTHOR>
 */
@Configuration
public class RestTemplateConfig {

    private static final int CONNECT_TIME_OUT = 10000;

    private static final int READ_TIME_OUT = 90000;

    @Bean(name = "remoteRestTemplate")
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }

    @Bean(name = "groupBuyRestTemplate")
    public RestTemplate groupBuyRestTemplate(){
        RestTemplate restTemplate = new RestTemplate();
        //解决restTemplate发送string参数乱码的问题 设置编码 UTF-8
        List<HttpMessageConverter<?>> list = restTemplate.getMessageConverters();
        for (HttpMessageConverter<?> httpMessageConverter : list) {
            if (httpMessageConverter instanceof StringHttpMessageConverter) {
                ((StringHttpMessageConverter) httpMessageConverter).setDefaultCharset(StandardCharsets.UTF_8);
            }
        }
        SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
        //设置连接超时时间 10s
        requestFactory.setConnectTimeout(CONNECT_TIME_OUT);
        //设置服务端读取数据超时时间 30s
        requestFactory.setReadTimeout(READ_TIME_OUT);
        restTemplate.setRequestFactory(requestFactory);
        return restTemplate;
    }
}
