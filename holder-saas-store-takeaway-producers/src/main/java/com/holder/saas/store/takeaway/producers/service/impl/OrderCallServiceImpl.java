package com.holder.saas.store.takeaway.producers.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holder.saas.store.takeaway.producers.entity.domain.OrderCallDO;
import com.holder.saas.store.takeaway.producers.mapper.OrderCallMapper;
import com.holder.saas.store.takeaway.producers.service.OrderCallService;
import org.springframework.stereotype.Service;

@Service
public class OrderCallServiceImpl extends ServiceImpl<OrderCallMapper, OrderCallDO> implements OrderCallService {
}
