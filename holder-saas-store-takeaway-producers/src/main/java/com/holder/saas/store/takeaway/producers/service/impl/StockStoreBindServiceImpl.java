package com.holder.saas.store.takeaway.producers.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holder.saas.store.takeaway.producers.entity.domain.StoreBindDO;
import com.holder.saas.store.takeaway.producers.entity.domain.StoreBindOrderDO;
import com.holder.saas.store.takeaway.producers.mapper.StockStoreBindMapper;
import com.holder.saas.store.takeaway.producers.mapper.StockStoreBindOrderMapper;
import com.holder.saas.store.takeaway.producers.service.StockStoreBindService;
import com.holder.saas.store.takeaway.producers.service.rpc.OrganizationService;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.takeaway.request.StockStoreBindReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.StockStoreBindReqOrderDTO;
import com.holderzone.saas.store.dto.takeaway.response.StockStoreBindResqDTO;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 库存门店绑定
 * @date 2021/9/16
 */
@Service
@AllArgsConstructor
public class StockStoreBindServiceImpl  extends ServiceImpl<StockStoreBindMapper, StoreBindDO> implements StockStoreBindService {

    private final StockStoreBindOrderMapper orderMapper;

    private final OrganizationService organizationService;

    @Override
    public StockStoreBindResqDTO bindStockStore(StockStoreBindReqDTO req) {
        StoreBindDO one = getOne(new LambdaQueryWrapper<StoreBindDO>().eq(StoreBindDO::getStoreGuid, req.getStoreGuid()));
        if(ObjectUtil.isNull(one)){
            StoreBindDO storeBindDO = new StoreBindDO();
            storeBindDO.setStoreGuid(req.getStoreGuid());
            storeBindDO.setBranchStoreGuid(req.getBranchStoreGuid());
            storeBindDO.setBranchStoreName(req.getBranchStoreName());
            save(storeBindDO);
        }else {
            one.setBranchStoreGuid(req.getBranchStoreGuid());
            one.setBranchStoreName(req.getBranchStoreName());
            updateById(one);
        }
        StockStoreBindResqDTO resqDTO = new StockStoreBindResqDTO();
        resqDTO.setStoreGuid(req.getStoreGuid());
        return resqDTO;
    }

    @Override
    public StockStoreBindResqDTO getBindStockStore(String storeGuid) {
        StoreBindDO one = getOne(new LambdaQueryWrapper<StoreBindDO>().eq(StoreBindDO::getStoreGuid, storeGuid));
        StockStoreBindResqDTO resqDTO = new StockStoreBindResqDTO();
        if(ObjectUtil.isNotNull(one)){
            resqDTO.setStoreGuid(one.getStoreGuid());
            resqDTO.setBranchStoreGuid(one.getBranchStoreGuid());
            resqDTO.setBranchStoreName(one.getBranchStoreName());
            return resqDTO;
        }
        StoreDTO storeDTO = organizationService.queryStoreByGuid(storeGuid);
        resqDTO.setStoreGuid(storeGuid);
        resqDTO.setBranchStoreName(storeDTO.getName());
        return resqDTO;
    }

    @Override
    public void saveBindStockStoreOrder(StockStoreBindReqOrderDTO reqOrderDTO) {
        StoreBindOrderDO orderDO = new StoreBindOrderDO();
        orderDO.setBranchStoreGuid(reqOrderDTO.getBranchStoreGuid());
        orderDO.setOrderId(reqOrderDTO.getOrderId());
        orderDO.setStoreGuid(reqOrderDTO.getStoreGuid());
        orderMapper.insert(orderDO);
    }

    @Override
    public StoreBindOrderDO getBindStockStoreOrder(String orderId, String branchStoreGuid) {
        return orderMapper.selectOne(new LambdaQueryWrapper<StoreBindOrderDO>().eq(StoreBindOrderDO::getOrderId,orderId)
        .eq(StoreBindOrderDO::getBranchStoreGuid,branchStoreGuid));
    }
}
