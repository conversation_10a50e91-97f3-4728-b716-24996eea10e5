package com.holder.saas.store.takeaway.producers.constant;

public interface TakeoutConstant {

    interface Ele {

    }

    String CHARSET_UTF_8 = "UTF-8";

    String REAL_PHONE_NUMBER_URL = "https://api-open-cater.meituan.com/waimai/order/batchPullPhoneNumber";

    String REAL_RECIPIENT_ADDRESS_URL = "https://api-open-cater.meituan.com/waimai/ng/order/getRealRecipientAddress";

    // 美团绑定url
    String MEI_TUAN_BINDING_URL = "https://open-erp.meituan.com/storemap";

    String MEI_TUAN_LOGIN_BINDING_URL = "https://open-erp.meituan.com/login";

    // 美团解绑url
    String MEI_TUAN_UN_BINDING_URL = "https://open-erp.meituan.com/releasebinding";

    // 美团菜品映射url
    String MEI_TUAN_ITEM_BINDING_URL = "https://open-erp.meituan.com/waimai-dish-mapping";

    String MEI_TUAN_AUTH_BINDING_URL = "https://open-erp.meituan.com/general/auth";

    String MEI_TUAN_AUTH_UN_BINDING_URL = "https://open-erp.meituan.com/general/unauth";

    // 饿了么解绑url
    String ELE_UNLOCK_BINDING_URL = "";

    // 1：绑定操作，0，解除绑定操作
    int BINDING = 1;
    int REMOVE_BINDING = 0;

    // 当前状态  0：未绑定，1:绑定，2：全部
    int NOT_BINDING = 0;
    int ALREADY_BINDING = 1;
    int ALL_SATUS = 2;

    String PARAM_DEVELOPER_ID = "developerId";

    String ELE_ME_COMPENSATE = "ELE_ME_COMPENSATE";

    int MT_BATCH_QUERY_ITEM_PARTITION = 20;

    String MSG_EDIT_ITEM_MAPPING="修改菜品映射";

    String MSG_DELETE_ITEM_MAPPING="删除菜品映射";

    String BRACKET = "%s（%s）";

    String JD_NOT_AUTH = "京东外卖未授权";
}
