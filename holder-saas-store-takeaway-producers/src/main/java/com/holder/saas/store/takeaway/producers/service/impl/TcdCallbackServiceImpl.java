/*
 * Copyright (c) 2018-2028 成都掌控者科技有限公司 All Rights Reserved.
 * ProjectName:saas-platform
 * FileName:TcdCallbackServiceImpl.java
 * Date:2020-3-2
 * Author:terry
 */

package com.holder.saas.store.takeaway.producers.service.impl;

import com.holder.saas.store.takeaway.producers.service.TcdCallbackService;
import com.holder.saas.store.takeaway.producers.service.TcdUnOrderParser;
import com.holder.saas.store.takeaway.producers.service.UnOrderMqService;
import com.holder.saas.store.takeaway.producers.utils.ManualValidUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.takeaway.UnOrder;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutTcdOrderReqDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2020-03-02 下午2:01
 */
@Slf4j
@Service
public class TcdCallbackServiceImpl implements TcdCallbackService {

    private final TcdUnOrderParser tcdUnOrderParser;

    private final UnOrderMqService unOrderMqService;

    @Autowired
    public TcdCallbackServiceImpl(TcdUnOrderParser tcdUnOrderParser, UnOrderMqService unOrderMqService) {
        this.tcdUnOrderParser = tcdUnOrderParser;
        this.unOrderMqService = unOrderMqService;
    }

    @Override
    public void orderCallback(TakeoutTcdOrderReqDTO takeoutTcdOrderReqDTO) {
        UnOrder unOrder = parseToUnOrder(takeoutTcdOrderReqDTO);
        if (unOrder == null) {
            log.error("(赚餐)回调类型({})未定义具体逻辑", takeoutTcdOrderReqDTO.getOrderState());
        } else {
            unOrderMqService.sendUnOrder(unOrder);
        }
    }

    private UnOrder parseToUnOrder(TakeoutTcdOrderReqDTO takeoutTcdOrderReqDTO) {
        UnOrder unOrder = null;
        switch (takeoutTcdOrderReqDTO.getOrderState()) {
            case PENDING:
                ManualValidUtils.validate(takeoutTcdOrderReqDTO, TakeoutTcdOrderReqDTO.Created.class);
                unOrder = tcdUnOrderParser.fromOrderCreate(takeoutTcdOrderReqDTO);
                if (log.isInfoEnabled()) {
                    log.info("(赚餐)订单回调，向ERP发送(通吃岛)新订单，unOrder: {}", JacksonUtils.writeValueAsString(unOrder));
                }
                break;
            case PERSON_PENDING:
                unOrder = tcdUnOrderParser.fromOrderCreatePersonPending(takeoutTcdOrderReqDTO);
                if (log.isInfoEnabled()) {
                    log.info("(赚餐)订单回调，向ERP发送(通吃岛)出餐订单，unOrder: {}", JacksonUtils.writeValueAsString(unOrder));
                }
                break;
            case COOKING:
                ManualValidUtils.validate(takeoutTcdOrderReqDTO, TakeoutTcdOrderReqDTO.Confirmed.class);
                unOrder = tcdUnOrderParser.fromOrderConfirmed(takeoutTcdOrderReqDTO);
                if (log.isInfoEnabled()) {
                    log.info("(赚餐)订单回调，向ERP发送(通吃岛)接单，unOrder: {}", JacksonUtils.writeValueAsString(unOrder));
                }
                break;
            case CANCELLED:
                ManualValidUtils.validate(takeoutTcdOrderReqDTO, TakeoutTcdOrderReqDTO.Canceled.class);
                unOrder = tcdUnOrderParser.fromOrderCanceled(takeoutTcdOrderReqDTO);
                if (log.isInfoEnabled()) {
                    log.info("(赚餐)订单回调，向ERP发送(通吃岛)取消订单，unOrder: {}", JacksonUtils.writeValueAsString(unOrder));
                }
                break;
            case REFUND:
                ManualValidUtils.validate(takeoutTcdOrderReqDTO, TakeoutTcdOrderReqDTO.Canceled.class);
                unOrder = tcdUnOrderParser.fromOrderRefunded(takeoutTcdOrderReqDTO);
                if (log.isInfoEnabled()) {
                    log.info("(赚餐)订单回调，向ERP发送(通吃岛)用户申请取消订单，unOrder: {}", JacksonUtils.writeValueAsString(unOrder));
                }
                break;
            case DISTRIBUTION:
                ManualValidUtils.validate(takeoutTcdOrderReqDTO, TakeoutTcdOrderReqDTO.Shipping.class);
                unOrder = tcdUnOrderParser.fromOrderShipping(takeoutTcdOrderReqDTO);
                if (log.isInfoEnabled()) {
                    log.info("(赚餐)订单回调，向ERP发送(通吃岛)订单配送，unOrder: {}", JacksonUtils.writeValueAsString(unOrder));
                }
                break;
            case FINISH:
                ManualValidUtils.validate(takeoutTcdOrderReqDTO, TakeoutTcdOrderReqDTO.Finished.class);
                unOrder = tcdUnOrderParser.fromOrderFinished(takeoutTcdOrderReqDTO);
                if (log.isInfoEnabled()) {
                    log.info("(赚餐)订单回调，向ERP发送(通吃岛)订单完成，unOrder: {}", JacksonUtils.writeValueAsString(unOrder));
                }
                break;
            default:
                break;
        }
        return unOrder;
    }

}
