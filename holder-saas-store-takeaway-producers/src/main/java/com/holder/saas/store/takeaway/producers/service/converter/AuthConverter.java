package com.holder.saas.store.takeaway.producers.service.converter;

import com.holder.saas.store.takeaway.producers.entity.domain.AlipayAuthDO;
import com.holder.saas.store.takeaway.producers.service.DistributedService;
import com.holderzone.saas.store.dto.takeaway.request.NotifyAliPayAuthReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.AlipayAuthRespDTO;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/11/15
 * @description 授权类转换
 */
public class AuthConverter {

    @Autowired
    private DistributedService distributedService;

    public static AlipayAuthRespDTO alipayAuthDO2AlipayAuthDTO(AlipayAuthDO alipayAuthDO) {
        if (Objects.isNull(alipayAuthDO)) {
            return null;
        }
        AlipayAuthRespDTO authDTO = new AlipayAuthRespDTO();
        authDTO.setApplyPublicKey(alipayAuthDO.getApplyPublicKey());
        authDTO.setApplyPrivateKey(alipayAuthDO.getApplyPrivateKey());
        authDTO.setAliPublicKey(alipayAuthDO.getAliPublicKey());
        authDTO.setAes(alipayAuthDO.getAes());

        authDTO.setEnterpriseGuid(alipayAuthDO.getEnterpriseGuid());
        authDTO.setOperSubjectGuid(alipayAuthDO.getOperSubjectGuid());
        authDTO.setAppId(alipayAuthDO.getAppId());
        authDTO.setAppAuthToken(alipayAuthDO.getAppAuthToken());

        return authDTO;
    }

    public static AlipayAuthDO notifyDTO2AlipayAuthDO(NotifyAliPayAuthReqDTO notifyDTO) {
        AlipayAuthDO authDO = new AlipayAuthDO();
        authDO.setEnterpriseGuid(notifyDTO.getEnterpriseGuid());
        authDO.setOperSubjectGuid(notifyDTO.getOperSubjectGuid());
        authDO.setAppAuthToken(notifyDTO.getAppAuthToken());
        authDO.setAppId(notifyDTO.getAppId());

        return authDO;
    }
}
