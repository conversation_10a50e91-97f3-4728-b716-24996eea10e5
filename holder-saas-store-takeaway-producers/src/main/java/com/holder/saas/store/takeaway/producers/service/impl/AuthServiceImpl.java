package com.holder.saas.store.takeaway.producers.service.impl;

import com.holder.saas.store.takeaway.producers.entity.domain.EleAuthDO;
import com.holder.saas.store.takeaway.producers.entity.domain.JdStoreMappingDO;
import com.holder.saas.store.takeaway.producers.entity.domain.MtAuthDO;
import com.holder.saas.store.takeaway.producers.entity.domain.TcdAuthDO;
import com.holder.saas.store.takeaway.producers.service.*;
import com.holderzone.saas.store.dto.takeaway.MtBusinessIdEnum;
import com.holderzone.saas.store.enums.takeaway.TakeawayQueryTypeEnum;
import eleme.openapi.sdk.oauth.response.Token;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Slf4j
@Service
public class AuthServiceImpl implements AuthService {

    private final MtAuthService mtAuthService;

    private final EleAuthService eleAuthService;

    private final TcdAuthService tcdAuthService;

    private final JdStoreMappingService jdStoreMappingService;

    @Override
    public List<String> authStoreGuids(List<String> storeGuids, Integer takeoutType) {
        if (TakeawayQueryTypeEnum.mt.getCode().equals(takeoutType)) {
            // 美团
            List<MtAuthDO> mtAuthList = mtAuthService.getAuths(storeGuids, MtBusinessIdEnum.TAKEOUT.getType());
            return mtAuthList.stream().map(MtAuthDO::getEPoiId).collect(Collectors.toList());
        } else if (TakeawayQueryTypeEnum.ele.getCode().equals(takeoutType)) {
            // 饿了么
            Map<String, Pair<EleAuthDO, Token>> eleTokenList = eleAuthService.getTokens(storeGuids);
            return new ArrayList<>(eleTokenList.keySet());
        } else if (TakeawayQueryTypeEnum.tcd.getCode().equals(takeoutType)) {
            // 赚餐
            List<TcdAuthDO> tcdTokenList = tcdAuthService.getTokens(storeGuids);
            return tcdTokenList.stream().map(TcdAuthDO::getStoreGuid).collect(Collectors.toList());
        } else if (TakeawayQueryTypeEnum.JD.getCode().equals(takeoutType)) {
            List<JdStoreMappingDO> jdStoreMappingList = jdStoreMappingService.listByStoreGuids(storeGuids);
            return jdStoreMappingList.stream().map(JdStoreMappingDO::getStoreGuid).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }
}
