package com.holder.saas.store.takeaway.producers.utils;

import java.io.UnsupportedEncodingException;

/**
 * <AUTHOR>
 * @version 1.0
 * @className UrlCodecUtils  url encode
 * @date 2018/09/20 16:55
 * @description
 * @program holder-saas-store-takeaway
 */
public class UrlCodecUtils {

    private final static String ENCODE = "GBK";

    /**
     * URL 解码
     *
     * @return String
     * <AUTHOR>
     * @date 2015-3-17 下午04:09:51
     */
    public static String getURLDecoderString(String str) {
        String result = "";
        if (null == str) {
            return "";
        }
        try {
            result = java.net.URLDecoder.decode(str, ENCODE);
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return result;
    }
    /**
     * URL 转码
     *
     * @return String
     * <AUTHOR>
     * @date 2015-3-17 下午04:10:28
     */
    public static String getURLEncoderString(String str) {
        String result = "";
        if (null == str) {
            return "";
        }
        try {
            result = java.net.URLEncoder.encode(str, ENCODE);
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return result;
    }
}
