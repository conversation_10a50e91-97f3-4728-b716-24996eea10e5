package com.holder.saas.store.takeaway.producers.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.holder.saas.store.takeaway.producers.constant.TakeoutConstant;
import com.holder.saas.store.takeaway.producers.entity.domain.MtAuthDO;
import com.holder.saas.store.takeaway.producers.service.MtAuthService;
import com.holder.saas.store.takeaway.producers.service.UnOrderMqService;
import com.holder.saas.store.takeaway.producers.service.UnOrderReplyService;
import com.holder.saas.store.takeaway.producers.service.rpc.ConsumersFeignService;
import com.holder.saas.store.takeaway.producers.utils.*;
import com.holder.saas.store.takeaway.producers.utils.sdk.meituan.CipCaterTakeoutOrderRiderPositionRequest;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.common.enums.BooleanEnum;
import com.holderzone.saas.store.dto.takeaway.*;
import com.sankuai.sjst.platform.developer.domain.RequestSysParams;
import com.sankuai.sjst.platform.developer.request.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.net.URISyntaxException;
import java.util.Map;


/**
 * <AUTHOR>
 * @Description 接收erp推送的的消息处理后发送给平台处理
 * @time 2017年7月25日 下午6:30:32
 */
@Slf4j
@Service("mtOrderReplyServiceImpl")
public class MtOrderReplyServiceImpl implements UnOrderReplyService {

    private final MtAuthService authService;

    private final UnOrderMqService unOrderMqService;

    private final ConsumersFeignService consumersFeignService;

    private final String platformName = "美团";

    @Value("${mt.SIGN_KEY}")
    private String mtSignKey;

    @Value("${small.ORDER_DELIVERY}")
    private String deliveryUrl;

    @Value("${small.ORDER_CANCEL_DELIVERY}")
    private String cancelDeliveryUrl;

    @Autowired
    public MtOrderReplyServiceImpl(MtAuthService authService, UnOrderMqService unOrderMqService, ConsumersFeignService consumersFeignService) {
        this.authService = authService;
        this.unOrderMqService = unOrderMqService;
        this.consumersFeignService = consumersFeignService;
    }

    @Override
    public void replyCancelOrder(UnOrder unOrder) {
        String msgType = "商家取消订单";
        logReplyProcessing(unOrder, msgType);

        // 根据门店ID查询外卖平台对应的门店token (首先去缓存找，缓存没有去数据库找)
        MtAuthDO mtAuthDO = authService.getAuth(unOrder.getStoreGuid(), MtBusinessIdEnum.TAKEOUT.getType());
        if (mtAuthDO == null) {
            logTokenUnavailableThenThrow(unOrder, msgType);
        } else {
            String authToken = mtAuthDO.getAccessToken();

            CipCaterTakeoutOrderCancelRequest request = new CipCaterTakeoutOrderCancelRequest();
            RequestSysParams requestSysParams = new RequestSysParams(mtSignKey, authToken, TakeoutConstant.CHARSET_UTF_8);
            request.setRequestSysParams(requestSysParams);
            request.setOrderId(Long.valueOf(unOrder.getOrderId()));
            request.setReasonCode("2007"); // 默认其它原因
            request.setReason(unOrder.getCancelReason() != null ? unOrder.getCancelReason() : "其他原因");

            try {
                String result = request.doRequest();
                MtResponseDTO mtResponseDTO = JacksonUtils.toObject(MtResponseDTO.class, result);
                if (mtResponseDTO.isOK()) {
                    logReplySucceed(unOrder, msgType);
                    //由于美团外卖平台不推送拒单消息，此处逻辑自己处理
                    unOrder.setCbMsgType(UnOrderCbMsgType.ORDER_CANCELED);
                    unOrderMqService.sendUnOrder(unOrder);
                } else {
                    logReplyFailedThenThrow(unOrder, mtResponseDTO.getError(), msgType);
                }
            } catch (IOException | URISyntaxException e) {
                logExceptionThenThrow(unOrder, e, msgType);
            }
        }
    }

    @Override
    public void replyConfirmOrder(UnOrder unOrder) {
        String msgType = "商家接单";
        logReplyProcessing(unOrder, msgType);

        MtAuthDO mtAuthDO = authService.getAuth(unOrder.getStoreGuid(), MtBusinessIdEnum.TAKEOUT.getType());
        if (mtAuthDO == null) {
            logTokenUnavailableThenThrow(unOrder, msgType);
        } else {
            String authToken = mtAuthDO.getAccessToken();

            CipCaterTakeoutOrderConfirmRequest request = new CipCaterTakeoutOrderConfirmRequest();
            RequestSysParams requestSysParams = new RequestSysParams(mtSignKey, authToken, TakeoutConstant.CHARSET_UTF_8);
            request.setRequestSysParams(requestSysParams);
            request.setOrderId(Long.valueOf(unOrder.getOrderId()));

            doReply(request, unOrder, msgType);
        }
    }

    @Override
    public void replyAgreeCancelOrder(UnOrder unOrder) {
        String msgType = "商家同意取消订单";
        logReplyProcessing(unOrder, msgType);

        MtAuthDO mtAuthDO = authService.getAuth(unOrder.getStoreGuid(), MtBusinessIdEnum.TAKEOUT.getType());
        if (mtAuthDO == null) {
            logTokenUnavailableThenThrow(unOrder, msgType);
        } else {
            String authToken = mtAuthDO.getAccessToken();
            CipCaterTakeoutOrderRefundAcceptRequest request = new CipCaterTakeoutOrderRefundAcceptRequest();
            RequestSysParams requestSysParams = new RequestSysParams(mtSignKey, authToken, TakeoutConstant.CHARSET_UTF_8);
            request.setRequestSysParams(requestSysParams);
            request.setOrderId(Long.valueOf(unOrder.getOrderId()));
            request.setReason("同意取消订单");

            doReply(request, unOrder, msgType);
        }
    }

    @Override
    public void replyDisagreeCancelOrder(UnOrder unOrder) {
        String msgType = "不同意取消订单";
        logReplyProcessing(unOrder, msgType);

        MtAuthDO mtAuthDO = authService.getAuth(unOrder.getStoreGuid(), MtBusinessIdEnum.TAKEOUT.getType());
        if (mtAuthDO == null) {
            logTokenUnavailableThenThrow(unOrder, msgType);
        } else {
            String authToken = mtAuthDO.getAccessToken();

            CipCaterTakeoutOrderRefundRejectRequest request = new CipCaterTakeoutOrderRefundRejectRequest();
            RequestSysParams requestSysParams = new RequestSysParams(mtSignKey, authToken, TakeoutConstant.CHARSET_UTF_8);
            request.setRequestSysParams(requestSysParams);
            request.setOrderId(Long.valueOf(unOrder.getOrderId()));
            request.setReason(unOrder.getCancelReplyMessage() != null ? unOrder.getCancelReplyMessage() : "不同意取消订单");

            doReply(request, unOrder, msgType);
        }
    }

    @Override
    public void replyAgreeRefundOrder(UnOrder unOrder) {
        String msgType = "同意退单";
        logReplyProcessing(unOrder, msgType);

        MtAuthDO mtAuthDO = authService.getAuth(unOrder.getStoreGuid(), MtBusinessIdEnum.TAKEOUT.getType());
        if (mtAuthDO == null) {
            logTokenUnavailableThenThrow(unOrder, msgType);
        } else {
            String authToken = mtAuthDO.getAccessToken();

            CipCaterTakeoutOrderRefundAcceptRequest request = new CipCaterTakeoutOrderRefundAcceptRequest();
            RequestSysParams requestSysParams = new RequestSysParams(mtSignKey, authToken, TakeoutConstant.CHARSET_UTF_8);
            request.setRequestSysParams(requestSysParams);
            request.setOrderId(Long.valueOf(unOrder.getOrderId()));
            request.setReason("同意退单");

            doReply(request, unOrder, msgType);
        }
    }

    @Override
    public void replyDisagreeRefundOrder(UnOrder unOrder) {
        String msgType = "商家不同意退单";
        logReplyProcessing(unOrder, msgType);

        MtAuthDO mtAuthDO = authService.getAuth(unOrder.getStoreGuid(), MtBusinessIdEnum.TAKEOUT.getType());
        if (mtAuthDO == null) {
            logTokenUnavailableThenThrow(unOrder, msgType);
        } else {
            String authToken = mtAuthDO.getAccessToken();

            CipCaterTakeoutOrderRefundRejectRequest request = new CipCaterTakeoutOrderRefundRejectRequest();
            RequestSysParams requestSysParams = new RequestSysParams(mtSignKey, authToken, TakeoutConstant.CHARSET_UTF_8);
            request.setRequestSysParams(requestSysParams);
            request.setOrderId(Long.valueOf(unOrder.getOrderId()));
            request.setReason(unOrder.getRefundReplyMessage() != null ? unOrder.getRefundReplyMessage() : "不同意退单");

            doReply(request, unOrder, msgType);
        }
    }

    @Override
    public void startDelivery(UnOrder unOrder) {
        startDeliveryMQ(unOrder);
    }


    @Override
    public OwnApiResult startDeliveryMQ(UnOrder unOrder) {
        OwnApiResult ownApiResult = DeliveryUtils.startDelivery(unOrder, platformName, deliveryUrl);
        //判断返回值
        boolean haveResult = ObjectUtil.equal(ownApiResult.getCode(), BooleanEnum.FALSE.getCode())
                && ObjectUtil.isNotNull(ownApiResult.getResult());
        if(haveResult){
            Map<String, String> thirdCarrierIdMap  = (Map<String, String>) ownApiResult.getResult();
            String thirdCarrierId = thirdCarrierIdMap.get("OrderId");
            if(ObjectUtil.isNotNull(thirdCarrierId)){
                try {
                    ThirdCarrierUpdateDTO carrierUpdate = new ThirdCarrierUpdateDTO();
                    carrierUpdate.setEnterpriseGuid(unOrder.getEnterpriseGuid());
                    carrierUpdate.setOrderId(unOrder.getOrderId());
                    carrierUpdate.setThirdCarrierId(thirdCarrierId);
                    log.info("更新订单的物流单号，订单id:{},物流单号:{}",unOrder.getOrderId(),thirdCarrierId);
                    consumersFeignService.updateThirdCarrierId(carrierUpdate);
                }catch (Exception e){
                    log.warn("更新订单的物流单号失败");
                    e.printStackTrace();
                }

            }
        }
        return ownApiResult;
    }


    @Override
    public void cancelDelivery(UnOrder unOrder) {
        DeliveryUtils.cancelDelivery(unOrder,platformName,cancelDeliveryUrl);
    }


    @Override
    public void replyUrgeOrder(UnOrder unOrder) {
        throw new BusinessException("美团暂未实现催单");
    }

    private void logReplyProcessing(UnOrder unOrder, String msgType) {
        log.info("Reply(美团){}，orderId: {}，处理中", msgType, unOrder.getOrderId());
    }

    private void logReplySucceed(UnOrder unOrder, String msgType) {
        log.info("Reply(美团){}，orderId: {}，处理成功", msgType, unOrder.getOrderId());
    }

    private void logTokenUnavailableThenThrow(UnOrder unOrder, String msgType) {
        log.error("Reply(美团){}，orderId: {}，处理失败: 根据storeGuid: {} 未查询到Token",
                msgType, unOrder.getOrderId(), unOrder.getStoreGuid());
        throw new BusinessException("业务失败：" + "门店未绑定");
    }

    private void logReplyFailedThenThrow(UnOrder unOrder, MtResponseDTO.ErrorDetail errorDetail, String msgType) {
        log.error("Reply(美团){}，orderId: {}，处理失败，code: {}, message: {}",
                msgType, unOrder.getOrderId(), errorDetail.getCode(), errorDetail.getMessage());
        if ("808".equals(errorDetail.getCode())) {
            //1.英文为空，就是失败，中文就用它的。
            throw new BusinessException("用户申诉退款只能由客服进行操作");
        }
        // https://developer.meituan.com/openapi#3.3
        // 4 authority_error 权限验证失败，请检查外卖或者团购门店是否绑定
        // 5 app_auth_token_error 令牌错误
        if ("authority_error".equalsIgnoreCase(errorDetail.getError_type())
                || "app_auth_token_error".equalsIgnoreCase(errorDetail.getError_type())) {
            try {
                authService.deleteAuth(unOrder.getStoreGuid(), MtBusinessIdEnum.TAKEOUT.getType());
                log.info("storeGuid: {}，(美团)移除失效Token成功", unOrder.getStoreGuid());
            } catch (Throwable throwable) {
                log.error("storeGuid: {}，(美团)移除失效Token失败，失败原因：{}",
                        unOrder.getStoreGuid(), ThrowableExtUtils.asStringIfAbsent(throwable));
            }
        }
        throw new BusinessException("业务失败：" + errorDetail.getMessage());
    }

    private void logExceptionThenThrow(UnOrder unOrder, Exception e, String msgType) {
        if (log.isErrorEnabled()) {
            log.error("Reply(美团){}，orderId: {}，处理失败: {}", msgType, unOrder.getOrderId(),
                    ThrowableExtUtils.asStringIfAbsent(e));
        }
        throw new BusinessException("业务失败：" + ThrowableExtUtils.asStringIfAbsent(e));
    }

    private void doReply(CipCaterRequest request, UnOrder unOrder, String msgType) {
        try {
            log.info("调用（美团参数）：{}",JacksonUtils.writeValueAsString(request));
            String result = request.doRequest();
            log.info("返回（美团参数）：{}",result);
            MtResponseDTO mtResponseDTO = JacksonUtils.toObject(MtResponseDTO.class, result);
            if (mtResponseDTO.isOK()) {
                logReplySucceed(unOrder, msgType);
            } else {
                logReplyFailedThenThrow(unOrder, mtResponseDTO.getError(), msgType);
            }
        } catch (IOException | URISyntaxException e) {
            logExceptionThenThrow(unOrder, e, msgType);
        }
    }

    @Override
    public void replyDeliveryAccept(UnOrder unOrder) {
        log.info("(美团)订单[{}]骑手接单,unOrder={}", unOrder.getOrderId(), JacksonUtils.writeValueAsString(unOrder));
        replyDeliveryStateNew(unOrder, "骑手接单",10);
    }

    @Override
    public void replyDeliveryStart(UnOrder unOrder) {
        //https://developer.meituan.com/openapi#7.3.5
        try {
            log.info("(美团)订单[{}]商家已送出(骑手已取餐),unOrder={}", unOrder.getOrderId(), JacksonUtils.writeValueAsString(unOrder));
            replyDeliveryStateNew(unOrder, "商家已送出", 20);
            unOrder.setCbMsgType(UnOrderCbMsgType.ORDER_SHIPPING);
            unOrderMqService.sendUnOrder(unOrder);
        } catch (Exception e) {
            e.printStackTrace();
            log.info("(美团)订单[{}]商家已送出异常,异常消息:{}", unOrder.getOrderId(), e.getMessage());
        }


    }

    @Override
    public void replyDeliveryCancel(UnOrder unOrder) {
        log.info("(美团)订单[{}]配送取消,unOrder={}", unOrder.getOrderId(), JacksonUtils.writeValueAsString(unOrder));
        replyDeliveryStateNew(unOrder, "配送取消",100);
    }

    @Override
    public void replyDeliveryComplete(UnOrder unOrder) {
        // https://developer.meituan.com/openapi#7.3.6
        try {
            unOrder.setCbMsgType(UnOrderCbMsgType.ORDER_SHIP_SUCCEED);
            unOrderMqService.sendUnOrder(unOrder);
            log.info("(美团)订单[{}]商家已送达,unOrder={}", unOrder.getOrderId(), JacksonUtils.writeValueAsString(unOrder));
            replyDeliveryStateNew(unOrder, "商家已送达", 40);
        } catch (Exception e) {
            e.printStackTrace();
            log.info("(美团)订单[{}]已送达异常,异常消息:{}", unOrder.getOrderId(), e.getMessage());
        }
    }

    /**
     * 自配送订单同步配送信息到 美团
     *
     * @param unOrder
     * @param msgType
     * @param logisticsStatus ： 0 配送单发往配送 10 配送单已确认 15 骑手已到店 20 骑手已取餐 40 骑手已送达 100 配送单已取消
     */
    private void replyDeliveryStateNew(UnOrder unOrder, String msgType,int logisticsStatus) {
        logReplyProcessing(unOrder, msgType);

        MtAuthDO mtAuthDO = authService.getAuth(unOrder.getStoreGuid(), MtBusinessIdEnum.TAKEOUT.getType());
        if (mtAuthDO == null) {
            logTokenUnavailableThenThrow(unOrder, msgType);
        } else {
            String authToken = mtAuthDO.getAccessToken();

            //新接口 https://developer.meituan.com/openapi#7.3.19 推荐替换之前的 7.3.5，7.3.6
            CipCaterTakeoutOrderRiderPositionRequest request = new CipCaterTakeoutOrderRiderPositionRequest();

            RequestSysParams requestSysParams = new RequestSysParams(mtSignKey, authToken, TakeoutConstant.CHARSET_UTF_8);
            request.setRequestSysParams(requestSysParams);
            request.setOrderId(Long.valueOf(unOrder.getOrderId()));
            //自配送状态：code 说明 0 配送单发往配送 10 配送单已确认 15 骑手已到店 20 骑手已取餐 40 骑手已送达 100 配送单已取消
            request.setThirdCarrierId(unOrder.getThirdCarrierId());
            request.setLogisticsStatus(logisticsStatus);
            //== 以下参数可为空
            request.setCourierName(unOrder.getShipperName());
            request.setCourierPhone(unOrder.getShipperPhone());
            //配送方式：其他
            request.setThirdLogisticsId(10017);
            request.setLatitude(unOrder.getShipLatitude());
            request.setLongitude(unOrder.getShipLongitude());
            doReply(request, unOrder, msgType);
        }
    }

    @Deprecated
    private void replyDeliveryState(UnOrder unOrder, String msgType, int deliveryType) {
        MtAuthDO mtAuthDO = authService.getAuth(unOrder.getStoreGuid(), MtBusinessIdEnum.TAKEOUT.getType());
        if (mtAuthDO == null) {
            logTokenUnavailableThenThrow(unOrder, msgType);
        } else {
            String authToken = mtAuthDO.getAccessToken();

            //订单已送出
            if (deliveryType == 1) {
                log.info("(美团)订单[{}]已送出,mtSignKey={}，authToken={}", unOrder.getOrderId(), mtSignKey, authToken);
                // https://developer.meituan.com/openapi#7.3.5
                CipCaterTakeoutOrderDeliveringRequest request = new CipCaterTakeoutOrderDeliveringRequest();
                RequestSysParams requestSysParams = new RequestSysParams(mtSignKey, authToken, TakeoutConstant.CHARSET_UTF_8);
                request.setRequestSysParams(requestSysParams);
                request.setOrderId(Long.valueOf(unOrder.getOrderId()));

                if (StringUtils.isEmpty(unOrder.getShipperPhone())) {
                    request.setCourierName("测试配送员");
                }

                if (StringUtils.isEmpty(unOrder.getShipperPhone())) {
                    request.setCourierPhone("13322222222");
                }

                log.info("(美团)订单[{}]已送出,request={}", unOrder.getOrderId(), JacksonUtils.writeValueAsString(request));
                doReply(request, unOrder, msgType);

                //订单已送达
            } else if (deliveryType == 2) {
                // https://developer.meituan.com/openapi#7.3.6
                log.info("(美团)订单[{}]已送达,mtSignKey={}，authToken={}", unOrder.getOrderId(), mtSignKey, authToken);
                CipCaterTakeoutOrderDeliveredRequest request = new CipCaterTakeoutOrderDeliveredRequest();
                RequestSysParams requestSysParams = new RequestSysParams(mtSignKey, authToken, TakeoutConstant.CHARSET_UTF_8);
                request.setRequestSysParams(requestSysParams);
                request.setOrderId(Long.valueOf(unOrder.getOrderId()));
                log.info("(美团)订单[{}]已送达,request={}", unOrder.getOrderId(), JacksonUtils.writeValueAsString(request));
                doReply(request, unOrder, msgType);
            }
        }
    }

    @Override
    public void replyRiderPosition(UnOrder unOrder) {
        String msgType = "推送骑手定位";
        int logisticsStatus = 10;
        if(unOrder.getOrderStatus() != null && unOrder.getOrderStatus() >= OrderStatus.SHIPPING){
            logisticsStatus = 20;
        }
        replyDeliveryStateNew(unOrder, msgType,logisticsStatus);
    }
}
