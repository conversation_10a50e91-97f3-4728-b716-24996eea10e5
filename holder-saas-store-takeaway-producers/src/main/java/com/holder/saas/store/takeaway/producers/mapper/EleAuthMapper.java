package com.holder.saas.store.takeaway.producers.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holder.saas.store.takeaway.producers.entity.domain.EleAuthDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface EleAuthMapper extends BaseMapper<EleAuthDO> {

    /**
     * 查询最新的认证数据，包括删除的
     *
     * @param userId user
     * @return List<EleAuthDO>
     */
    List<EleAuthDO> eleAuthList(@Param("userId") long userId);
}
