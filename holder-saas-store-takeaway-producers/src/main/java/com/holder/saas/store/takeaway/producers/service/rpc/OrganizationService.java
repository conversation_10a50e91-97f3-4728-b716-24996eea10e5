package com.holder.saas.store.takeaway.producers.service.rpc;

import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.saas.store.dto.organization.ItemUploadUpdateReq;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@Component
@FeignClient(name = "holder-saas-store-organization", fallbackFactory = OrganizationService.ServiceFallBack.class)
public interface OrganizationService {

    /**
     * 根据门店guid查询门店详细信息
     *
     * @param storeGuid 门店guid
     * @return StoreDTO
     */
    @ApiOperation("根据门店guid查询门店详细信息")
    @PostMapping("/store/query_store_by_guid")
    StoreDTO queryStoreByGuid(@RequestParam("storeGuid") String storeGuid);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<OrganizationService> {

        @Override
        public OrganizationService create(Throwable cause) {
            return new OrganizationService() {

                @Override
                public StoreDTO queryStoreByGuid(String storeGuid) {
                    log.error("根据门店guid查询门店详细信息 storeGuid={}, e={}",storeGuid, cause.getMessage());
                    throw new ParameterException("根据门店guid查询门店详细信息 失败!");
                }

            };
        }
    }
}
