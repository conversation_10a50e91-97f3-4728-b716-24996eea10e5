package com.holder.saas.store.takeaway.producers.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holder.saas.store.takeaway.producers.entity.domain.MtPrivacyDO;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderRocketListener
 * @date 2018/11/15 12:00
 * @description 美团隐私号服务
 * @program holder-saas-store-takeaway
 */
public interface MtPrivacyService extends IService<MtPrivacyDO> {
}
