package com.holder.saas.store.takeaway.producers.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @create 2023-06-16
 * @description 抖音团购配置信息
 */
@Component
@ConfigurationProperties(prefix = "group.dou-yin")
@Data
public class GroupBuyDouYinConfig {

    public String appId;

    public String appSecret;

    public String tokenUrl;

    public String host;

    public String shopQuery;

    public String storeMatchSubmitUrl;

    public String storeMatchQueryUrl;

    public String certificatePrepare;

    public String certificateVerify;

    public String certificateCancel;

    public Integer sandbox;

    public String sandboxToken;

    public boolean isSandbox(){
        return sandbox != null && sandbox == 1;
    }
}
