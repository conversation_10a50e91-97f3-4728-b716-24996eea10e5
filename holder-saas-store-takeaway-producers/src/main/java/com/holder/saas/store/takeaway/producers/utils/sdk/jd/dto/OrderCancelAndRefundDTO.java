package com.holder.saas.store.takeaway.producers.utils.sdk.jd.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 订单取消且退款接口
 */
@Data
public class OrderCancelAndRefundDTO {

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 操作人
     */
    private String operPin;

    /**
     * 备注信息
     */
    private String operRemark;

    /**
     * 操作时间
     */
    private LocalDateTime operTime;
}
