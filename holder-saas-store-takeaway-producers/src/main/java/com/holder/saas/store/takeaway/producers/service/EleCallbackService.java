package com.holder.saas.store.takeaway.producers.service;

import eleme.openapi.sdk.api.entity.other.OMessage;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderRocketListener
 * @date 2018/11/15 12:00
 * @description 饿了么外卖类服务
 * @program holder-saas-store-takeaway
 */
public interface EleCallbackService {

    /**
     * 目前订阅消息如下
     * <p>
     * 订单生效("订单生效", "订单状态扭转", 10, "订单生效，店铺可以看到新订单", "订单消息"),
     * 商户接单("商户接单", "订单状态扭转", 12, "商户已经接单", "订单状态变更消息"),
     * 订单被取消("订单被取消", "订单状态扭转", 14, "订单被取消（接单前）", "订单状态变更消息"),
     * 订单置为无效("订单置为无效", "订单状态扭转", 15, "订单置为无效（接单后）", "订单状态变更消息"),
     * 订单强制无效("订单强制无效", "订单状态扭转", 17, "订单强制无效（商家主动取消已接受订单、用户1分钟内取消）", "订单状态变更消息"),
     * 订单完结("订单完结", "订单状态扭转", 18, "订单完结", "订单状态变更消息"),
     * 用户申请取消单("用户申请取消单", "取消单流程", 20, "下单用户申请取消", "取消单消息"),
     * 用户取消取消单申请("用户取消取消单申请", "取消单流程", 21, "用户取消取消订单", "取消单消息"),
     * 商户拒绝取消单("商户拒绝取消单", "取消单流程", 22, "商户拒绝取消订单", "取消单消息"),
     * 商户同意取消单("商户同意取消单", "取消单流程", 23, "商户同意取消订单", "取消单消息"),
     * 客服仲裁取消单申请有效("客服仲裁取消单申请有效", "取消单流程", 25, "客服仲裁取消单申请有效", "取消单消息"),
     * 用户申请退单("用户申请退单", "退单流程", 30, "用户申请退单", "退单消息"),
     * 用户取消退单("用户取消退单", "退单流程", 31, "用户取消退单", "退单消息"),
     * 商户拒绝退单("商户拒绝退单", "退单流程", 32, "商户拒绝退单", "退单消息"),
     * 商户同意退单("商户同意退单", "退单流程", 33, "商户同意退单", "退单消息"),
     * 客服仲裁退单有效("客服仲裁退单有效", "退单流程", 35, "客服仲裁退单有效", "退单消息"),
     * 用户催单("用户催单", "催单流程", 45, "用户催单", "催单消息"),
     * 配送员配送中("配送员配送中", "运单追踪", 55, "配送员已取餐，配送中", "运单状态变更消息"),
     * 配送成功("配送成功", "运单追踪", 56, "配送成功", "运单状态变更消息"),
     * 应用授权解除通知("应用授权解除通知", "授权管理", 100, "授权状态变更消息", "授权状态变更消息"),
     *
     * @param oMessage
     * @see com.holderzone.saas.store.dto.takeaway.EleCbMsgTypeEnum 回调消息类型枚举
     */
    void orderCallback(OMessage oMessage);
}
