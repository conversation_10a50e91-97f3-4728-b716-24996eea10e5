package com.holder.saas.store.takeaway.producers.utils.sdk.jd.dto;

import lombok.Data;

import java.util.List;

@Data
public class SpuSkuDTO {

    /**
     * 外部商家spu编码
     */
    private String outSpuId;

    /**
     * SPU名称
     */
    private String spuName;

    /**
     * 店内分类ID
     */
    private Long[] shopCategories;

    /**
     * 类目ID
     */
    private Long categoryId;

    /**
     * 品牌ID
     */
    private Long brandId;

    /**
     * 上下架状态，1上架、2下架、4删除
     */
    private Integer fixedStatus;

    /**
     * SPU商品图片
     */
    private String[] images;

    /**
     * 商品描述
     */
    private String productDesc;

    /**
     * 商品描述是否在app端展示（0展示 1不展示）
     */
    private Integer ifViewDesc;

    /**
     * 单点不送
     */
    private Integer singleNoDelivery;

    /**
     * 销售属性集合
     */
    private List<SaleAttrRelationInfo> saleAttrRelationInfoList;

    /**
     * sku
     */
    private List<SkuMainNewDTO> skuMainInfo;

    private Long spuId;
}
