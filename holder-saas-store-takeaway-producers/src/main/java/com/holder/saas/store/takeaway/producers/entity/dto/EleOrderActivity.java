package com.holder.saas.store.takeaway.producers.entity.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 饿了么回调的OActivity对象字段缺失
 */
@Data
@NoArgsConstructor
public class EleOrderActivity implements Serializable {

    private static final long serialVersionUID = -7426360570449348240L;

    private long id;

    private String name;

    private int categoryId;

    private double amount;

    private Long foodId;

    /**
     * 1	单品折扣
     * 2	单品立减
     * 4	单品特价
     * 5	单品赠品
     * 102	店铺满减
     * 103	新用户立减
     * 106	满赠
     * 107	蜂鸟立减
     * 108	门店新客立减
     * 109	部分满减
     * 803	M件优惠
     * 901	配送费减免
     * 902	餐盒费减免
     * 912	用户标签
     * 711	限量抢购
     * 10048	津贴优惠
     * 10049	店铺新客津贴优惠
     * 10050	百亿补贴
     */
    private String type;
}
