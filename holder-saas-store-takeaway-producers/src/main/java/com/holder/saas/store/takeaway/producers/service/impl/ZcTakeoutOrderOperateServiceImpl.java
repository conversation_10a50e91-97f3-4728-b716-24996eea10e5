package com.holder.saas.store.takeaway.producers.service.impl;

import com.holder.saas.store.takeaway.producers.service.TakeoutOrderOperateService;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.takeaway.TakeoutOrderOperateDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service("zcTakeoutOrderOperateServiceImpl")
@Slf4j
public class ZcTakeoutOrderOperateServiceImpl implements TakeoutOrderOperateService {
    @Override
    public void orderPrepared(TakeoutOrderOperateDTO orderOperateDTO) {
        throw new BusinessException("暂不支持！");
    }
}
