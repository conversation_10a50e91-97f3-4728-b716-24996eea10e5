package com.holder.saas.store.takeaway.producers.service;

import com.holderzone.saas.store.dto.takeaway.UnOrder;
import com.holderzone.saas.store.dto.takeaway.request.SalesOrderDTO;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderRocketListener
 * @date 2018/11/15 12:00
 * @description 饿了么UnOrder解析器
 * @program holder-saas-store-takeaway
 */
public interface OwnUnOrderParser {


    /**
     * 订单生效
     *
     * @param salesOrderDTO
     * @return
     */
    UnOrder fromOrderCreated(SalesOrderDTO salesOrderDTO);


}
