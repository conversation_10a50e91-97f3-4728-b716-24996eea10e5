package com.holder.saas.store.takeaway.producers.entity;

import com.holderzone.saas.store.dto.takeaway.OrderStatus;
import eleme.openapi.sdk.api.enumeration.order.OOrderStatus;

public enum EleOrderStatusMapping {

    /**
     * 未生效订单
     */
    pending(OOrderStatus.pending.name(), OrderStatus.TO_DO),

    /**
     * 未处理订单
     */
    unprocessed(OOrderStatus.unprocessed.name(), OrderStatus.TO_DO),

    /**
     * 退单处理中
     */
    refunding(OOrderStatus.refunding.name(), OrderStatus.TO_SHIP),

    /**
     * 已处理的有效订单
     */
    valid(OOrderStatus.valid.name(), OrderStatus.TO_SHIP),

    /**
     * 无效订单
     */
    invalid(OOrderStatus.invalid.name(), OrderStatus.CANCELED),

    /**
     * 已完成订单
     */
    settled(OOrderStatus.settled.name(), OrderStatus.FINISHED);

    private String status;

    private int mappingStatus;

    EleOrderStatusMapping(String status, int mappingStatus) {
        this.status = status;
        this.mappingStatus = mappingStatus;
    }

    public String getStatus() {
        return status;
    }

    public int getMappingStatus() {
        return mappingStatus;
    }

    public static EleOrderStatusMapping ofStatus(String status) {
        for (EleOrderStatusMapping value : values()) {
            if (value.status.equalsIgnoreCase(status)) {
                return value;
            }
        }
        throw new IllegalArgumentException("无效的饿了么orderStatus[" + status + "]");
    }
}
