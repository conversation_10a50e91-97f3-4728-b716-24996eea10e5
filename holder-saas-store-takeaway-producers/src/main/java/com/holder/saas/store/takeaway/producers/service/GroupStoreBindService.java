package com.holder.saas.store.takeaway.producers.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.holder.saas.store.takeaway.producers.entity.domain.GroupStoreBindDO;
import com.holderzone.saas.store.dto.business.group.StoreBindDTO;
import com.holderzone.saas.store.dto.takeaway.response.StoreAuthDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-06-19
 * @description
 */
public interface GroupStoreBindService extends IService<GroupStoreBindDO> {
    void unbindStore(StoreBindDTO storeBind);

    List<StoreAuthDTO> listAllByStoreGuid(String storeGuid);
}
