package com.holder.saas.store.takeaway.producers.entity.dto.group;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.annotation.JSONField;
import com.holderzone.saas.store.dto.exception.GroupBuyException;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-06-19
 * @description
 */
@Data
public class DouYinPoi {

    private Poi poi;

    private String account;

    @Data
    public static
    class Poi{
        @JSONField(name = "poi_id")
        private String poiId;

        private String address;

        private BigDecimal latitude;

        private BigDecimal longitude;

        @JSONField(name = "poi_name")
        private String poiName;
    }

    public static Poi parseJsonAndFilterPoi(String rsp) {
        DouYinRspDTO<DouYinStoreQueryRspDTO> douYinStoreQueryRsp = JSONObject.parseObject(rsp,new TypeReference<DouYinRspDTO<DouYinStoreQueryRspDTO>>(){}.getType());
        if (douYinStoreQueryRsp == null || douYinStoreQueryRsp.getData() == null){
            throw new GroupBuyException("查询抖音门店为空");
        }
        if(douYinStoreQueryRsp.getData().getErrorCode() != 0){
            throw new GroupBuyException("请输入正确的抖音门店ID");
        }
        List<DouYinPoi> pois = douYinStoreQueryRsp.getData().getPois();
        if(CollectionUtil.isEmpty(pois)){
            throw new GroupBuyException("抖音门店为空");
        }
        return pois.get(0).getPoi();
    }
}
