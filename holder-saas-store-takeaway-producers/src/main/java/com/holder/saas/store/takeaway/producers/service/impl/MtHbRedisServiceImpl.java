package com.holder.saas.store.takeaway.producers.service.impl;

import com.holder.saas.store.takeaway.producers.service.MtHbRedisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

import static com.holderzone.framework.util.StringUtils.getStr;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MtHbRedisServiceImpl
 * @date 2018/09/28 15:44
 * @description
 * @program holder-saas-store-takeaway
 */
@Service
public class MtHbRedisServiceImpl implements MtHbRedisService {

    private final RedisTemplate<String, Object> redisTemplate;

    private static final String MODULE_NAME = "takeaway";

    private static final String REGEX = ":";

    private static final String MEMBER_INFO = "heartbeat";

    private static final int POLLING_TIME = 3;

    private static final TimeUnit POLLING_TIME_UNIT = TimeUnit.HOURS;

    @Autowired
    public MtHbRedisServiceImpl(RedisTemplate<String, Object> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    @Override
    public void saveHeartbeat(String heartbeatParameter) {
        redisTemplate.opsForValue().set(getStr(REGEX,MODULE_NAME,MEMBER_INFO),heartbeatParameter);
        redisTemplate.expire(getStr(REGEX,MODULE_NAME,MEMBER_INFO),POLLING_TIME, POLLING_TIME_UNIT);
    }

    @Override
    public String getHeartbeat(){
        return (String) redisTemplate.opsForValue().get(getStr(REGEX,MODULE_NAME,MEMBER_INFO));
    }
}
