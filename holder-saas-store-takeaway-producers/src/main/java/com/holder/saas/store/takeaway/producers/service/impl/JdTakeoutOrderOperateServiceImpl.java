package com.holder.saas.store.takeaway.producers.service.impl;

import com.holder.saas.store.takeaway.producers.config.MeiTuanConfig;
import com.holder.saas.store.takeaway.producers.constant.TakeoutConstant;
import com.holder.saas.store.takeaway.producers.entity.domain.JdAuthDO;
import com.holder.saas.store.takeaway.producers.entity.domain.MtAuthDO;
import com.holder.saas.store.takeaway.producers.service.JdAuthService;
import com.holder.saas.store.takeaway.producers.service.MtAuthService;
import com.holder.saas.store.takeaway.producers.service.TakeoutOrderOperateService;
import com.holder.saas.store.takeaway.producers.utils.sdk.jd.OrderDeliveryOperateRequest;
import com.holder.saas.store.takeaway.producers.utils.sdk.jd.dto.OrderDeliveryOperateDTO;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.takeaway.MtBusinessIdEnum;
import com.holderzone.saas.store.dto.takeaway.TakeoutOrderOperateDTO;
import com.meituan.sdk.DefaultMeituanClient;
import com.meituan.sdk.MeituanClient;
import com.meituan.sdk.MeituanResponse;
import com.meituan.sdk.internal.exceptions.MtSdkException;
import com.meituan.sdk.model.waimaiNg.order.orderDelivered.OrderDeliveredRequest;
import com.meituan.sdk.model.waimaiNg.order.preparationMealComplete.PreparationMealCompleteRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service("jdTakeoutOrderOperateServiceImpl")
@Slf4j
@RequiredArgsConstructor
public class JdTakeoutOrderOperateServiceImpl implements TakeoutOrderOperateService {

    private final JdAuthService jdAuthService;

    @Override
    public void orderPrepared(TakeoutOrderOperateDTO orderOperateDTO) {
        JdAuthDO auth = jdAuthService.getJdAuthByBrand(orderOperateDTO.getBrandGuid());
        if(auth == null) {
            throw new BusinessException(TakeoutConstant.JD_NOT_AUTH);
        }
        OrderDeliveryOperateDTO operateDTO = new OrderDeliveryOperateDTO();
        operateDTO.setOrderId(orderOperateDTO.getOrderId());
        operateDTO.setOperator(StringUtils.isEmpty(orderOperateDTO.getOperator()) ? "商家操作" : orderOperateDTO.getOperator());
        OrderDeliveryOperateRequest orderDeliveryOperateRequest = new OrderDeliveryOperateRequest(auth.getAppKey(),auth.getAppSecret(),auth.getToken());
        if(!orderDeliveryOperateRequest.execute(operateDTO)){
            throw new BusinessException("京东外卖订单出餐失败");
        }
    }
}
