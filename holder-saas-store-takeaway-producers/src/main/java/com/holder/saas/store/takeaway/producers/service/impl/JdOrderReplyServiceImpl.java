package com.holder.saas.store.takeaway.producers.service.impl;

import com.holder.saas.store.takeaway.producers.constant.TakeoutConstant;
import com.holder.saas.store.takeaway.producers.entity.domain.JdAuthDO;
import com.holder.saas.store.takeaway.producers.service.JdAuthService;
import com.holder.saas.store.takeaway.producers.service.UnOrderReplyService;
import com.holder.saas.store.takeaway.producers.utils.sdk.jd.AfsOpenApproveRequest;
import com.holder.saas.store.takeaway.producers.utils.sdk.jd.OrderAcceptOperateRequest;
import com.holder.saas.store.takeaway.producers.utils.sdk.jd.OrderCancelOperateRequest;
import com.holder.saas.store.takeaway.producers.utils.sdk.jd.dto.AfsOpenApproveDTO;
import com.holder.saas.store.takeaway.producers.utils.sdk.jd.dto.OrderAcceptOperateDTO;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.takeaway.OwnApiResult;
import com.holderzone.saas.store.dto.takeaway.UnOrder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service("jdOrderReplyServiceImpl")
@Slf4j
@RequiredArgsConstructor
public class JdOrderReplyServiceImpl implements UnOrderReplyService {

    private final JdAuthService jdAuthService;

    @Override
    public void replyCancelOrder(UnOrder unOrder) {
        log.info("京东外卖一体机拒单，订单编号：{}", unOrder.getOrderId());
        if (orderAcceptOperate(unOrder, false)){
            throw new BusinessException("京东外卖拒单失败");
        }
    }

    @Override
    public void replyConfirmOrder(UnOrder unOrder) {
        log.info("京东外卖一体机接单，订单编号：{}", unOrder.getOrderId());
        if (orderAcceptOperate(unOrder, true)){
            throw new BusinessException("京东外卖接单失败");
        }
    }


    private boolean orderAcceptOperate(UnOrder unOrder,boolean isAgreed){
        JdAuthDO auth = jdAuthService.getJdAuthByBrand(unOrder.getBrandGuid());
        if(auth == null) {
            throw new BusinessException(TakeoutConstant.JD_NOT_AUTH);
        }
        OrderAcceptOperateRequest request = new OrderAcceptOperateRequest(auth.getAppKey(), auth.getAppSecret(), auth.getToken());
        OrderAcceptOperateDTO orderAcceptOperate = new OrderAcceptOperateDTO();
        orderAcceptOperate.setOrderId(unOrder.getOrderId());
        orderAcceptOperate.setIsAgreed(isAgreed);
        orderAcceptOperate.setOperator(unOrder.getOperateStaffName());
        return !request.execute(orderAcceptOperate);
    }

    private boolean orderCancelOperate(UnOrder unOrder,boolean isAgreed){
        JdAuthDO auth = jdAuthService.getJdAuthByBrand(unOrder.getBrandGuid());
        if(auth == null) {
            throw new BusinessException(TakeoutConstant.JD_NOT_AUTH);
        }
        OrderCancelOperateRequest request = new OrderCancelOperateRequest(auth.getAppKey(), auth.getAppSecret(), auth.getToken());
        OrderAcceptOperateDTO orderAcceptOperate = new OrderAcceptOperateDTO();
        orderAcceptOperate.setOrderId(unOrder.getOrderId());
        orderAcceptOperate.setIsAgreed(isAgreed);
        orderAcceptOperate.setOperator(unOrder.getOperateStaffName());
        if(!isAgreed){
            orderAcceptOperate.setRemark(StringUtils.isEmpty(unOrder.getCancelReason()) ? "商家操作" : unOrder.getCancelReason());
        }
        return !request.execute(orderAcceptOperate);
    }


    @Override
    public void replyAgreeCancelOrder(UnOrder unOrder) {
        log.info("京东外卖商家同意取消订单，订单编号：{}", unOrder.getOrderId());
        if (orderCancelOperate(unOrder, true)){
            throw new BusinessException("京东商家同意取消订单失败");
        }
    }

    @Override
    public void replyDisagreeCancelOrder(UnOrder unOrder) {
        log.info("京东外卖商家不同意取消订单，订单编号：{}", unOrder.getOrderId());
        if (orderCancelOperate(unOrder, false)){
            throw new BusinessException("京东外卖商家不同意取消订单失败");
        }
    }

    @Override
    public void replyAgreeRefundOrder(UnOrder unOrder) {
        log.info("京东外卖商家同意退款，订单编号：{}", unOrder.getOrderId());

        if(afsOpenApprove(unOrder, 1)){
            throw new BusinessException("京东外卖商家同意退款订单失败");
        }
    }

    private boolean afsOpenApprove(UnOrder unOrder,Integer type){
        JdAuthDO auth = jdAuthService.getJdAuthByBrand(unOrder.getBrandGuid());
        if(auth == null) {
            throw new BusinessException(TakeoutConstant.JD_NOT_AUTH);
        }

        AfsOpenApproveRequest request = new AfsOpenApproveRequest(auth.getAppKey(), auth.getAppSecret(), auth.getToken());
        AfsOpenApproveDTO afsOpenApproveDTO = new AfsOpenApproveDTO();
        afsOpenApproveDTO.setServiceOrder(unOrder.getOrderId());
        afsOpenApproveDTO.setApproveType(type);
        afsOpenApproveDTO.setOptPin(unOrder.getOperateStaffName());
        if(type == 3){
            afsOpenApproveDTO.setRejectReason(StringUtils.isEmpty(unOrder.getRefundReplyMessage()) ? "商家驳回" : unOrder.getRefundReplyMessage());
        }
        return !request.execute(afsOpenApproveDTO);

    }

    @Override
    public void replyDisagreeRefundOrder(UnOrder unOrder) {
        log.info("京东外卖商家不同意退款，订单编号：{}", unOrder.getOrderId());

        if(afsOpenApprove(unOrder, 3)){
            throw new BusinessException("京东外卖商家不同意退款失败");
        }
    }

    @Override
    public void replyUrgeOrder(UnOrder unOrder) {
        throw new UnsupportedOperationException();
    }

    @Override
    public void startDelivery(UnOrder unOrder) {
        throw new UnsupportedOperationException();
    }

    @Override
    public OwnApiResult startDeliveryMQ(UnOrder unOrder) {
        return null;
    }

    @Override
    public void cancelDelivery(UnOrder unOrder) {
        throw new UnsupportedOperationException();
    }

    @Override
    public void replyDeliveryAccept(UnOrder unOrder) {
        throw new UnsupportedOperationException();
    }

    @Override
    public void replyDeliveryStart(UnOrder unOrder) {
        throw new UnsupportedOperationException();
    }

    @Override
    public void replyDeliveryCancel(UnOrder unOrder) {
        throw new UnsupportedOperationException();
    }

    @Override
    public void replyDeliveryComplete(UnOrder unOrder) {
        throw new UnsupportedOperationException();
    }

    @Override
    public void replyRiderPosition(UnOrder unOrder) {
        throw new UnsupportedOperationException();
    }
}
