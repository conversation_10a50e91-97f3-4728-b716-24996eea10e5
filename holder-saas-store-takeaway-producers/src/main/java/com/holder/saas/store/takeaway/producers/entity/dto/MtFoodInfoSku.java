package com.holder.saas.store.takeaway.producers.entity.dto;

import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@RequiredArgsConstructor
public class MtFoodInfoSku {

    @SerializedName("box_num")
    private BigDecimal boxNum;
    @SerializedName("box_price")
    private BigDecimal boxPrice;
    @SerializedName("ladder_num")
    private Integer ladderNum;
    @SerializedName("ladder_price")
    private BigDecimal ladderPrice;
    @SerializedName("mt_sku_id")
    private Long mtSkuId;
    @SerializedName("origin_sku_id")
    private Long originSkuId;
    @SerializedName("sku_id")
    private String skuId;
    @SerializedName("price")
    private BigDecimal price;
    @SerializedName("spec")
    private String spec;
    @SerializedName("stock")
    private String stock;
    @SerializedName("weight")
    private Integer weight;
    @SerializedName("weight_unit")
    private String weightUnit;

}
