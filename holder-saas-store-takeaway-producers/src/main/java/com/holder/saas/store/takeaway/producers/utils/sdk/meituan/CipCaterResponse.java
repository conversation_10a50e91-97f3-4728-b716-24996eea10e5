package com.holder.saas.store.takeaway.producers.utils.sdk.meituan;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/9/2
 **/
@Data
public class CipCaterResponse implements Serializable {


    /**
     * code : OP_SYSTEM_PARAM_ERROR
     * msg : 缺少系统参数
     * traceId : 123
     */

    private String code;
    private String msg;
    private String traceId;
    private String data;

    private static final String STR_OK = "OP_SUCCESS";

    public boolean isOK() {
        return STR_OK.equalsIgnoreCase(code);
    }
}
