package com.holder.saas.store.takeaway.producers.entity.dto;

import com.google.gson.annotations.SerializedName;
import com.meituan.sdk.model.waimaiNg.dish.dishFoodListAll.OpenToppingGroup;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@AllArgsConstructor
@RequiredArgsConstructor
public class MtFoodInfo {
    @SerializedName("epoiId")
    private String epoiId;
    @SerializedName("app_food_code")
    private String appFoodCode;
    @SerializedName("name")
    private String name;
    @SerializedName("description")
    private String description;
    @SerializedName("price")
    private @NotNull(
            message = "price不能为空"
    ) Double price;
    @SerializedName("min_order_count")
    private Integer minOrderCount;
    @SerializedName("max_order_count")
    private Integer maxOrderCount;
    @SerializedName("unit")
    private String unit;
    @SerializedName("box_num")
    private Float boxNum;
    @SerializedName("box_price")
    private Float boxPrice;
    @SerializedName("is_sold_out")
    private Integer isSoldOut;
    @SerializedName("sequence")
    private Integer sequence;
    @SerializedName("picture")
    private String picture;
    @SerializedName("skus")
    private List<MtFoodInfoSku> skus;
    @SerializedName("ctime")
    private Integer ctime;
    @SerializedName("utime")
    private Integer utime;
    @SerializedName("spuAttr")
    private String spuAttr;
    @SerializedName("mt_spu_id")
    private Long mtSpuId;
    @SerializedName("mt_tag_id")
    private Long mtTagId;
    @SerializedName("tag_name")
    private String tagName;
    @SerializedName("origin_spu_id")
    private Long originSpuId;
    @SerializedName("pictures")
    private String pictures;
    @SerializedName("high_light")
    private @NotBlank(
            message = "highLight不能为空"
    ) String highLight;
    @SerializedName("speciality")
    private Integer speciality;
    @SerializedName("is_not_single")
    private Integer isNotSingle;
    @SerializedName("monthSaled")
    private @NotNull(
            message = "monthSaled不能为空"
    ) Long monthSaled;
    @SerializedName("onlySellInCombo")
    private Boolean onlySellInCombo;
    @SerializedName("longPictures")
    private String longPictures;
    @SerializedName("toppingGroups")
    private List<OpenToppingGroup> toppingGroups;
    @SerializedName("isCombo")
    private Integer isCombo;

    /**
     * 如果是套餐
     */
    public boolean isComboGroup(){
        return this.isCombo != null && this.isCombo == 1;
    }
}
