package com.holder.saas.store.takeaway.producers.entity.dto;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class MtAuthDTO {

    /**
     * 美团门店GUID或者会员主体guid
     */
    private String mtStoreGuid;

    /**
     * 美团门店名字或者会员主体名字
     */
    private String mtStoreName;

    /**
     * erp门店ID，即storeGuid或者授权实体的唯一标识
     */
    private String ePoiId;

    /**
     * Erp商户GUID
     */
    private String enterpriseGuid;

    /***
     * 美团配送方式
     */
    private Integer deliveryType;

    /**
     * 访问token
     */
    private String accessToken;

    /**
     * 更新令牌
     */
    private String refreshToken;

    /**
     * erp门店开通的业务类型
     */
    private Byte businessId;

    /**
     * 记录创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 记录修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * token生效时间
     */
    private LocalDateTime activeTime;

    /**
     * token失效时间
     */
    private LocalDateTime expireTime;

    /**
     * 更新令牌失效时间
     */
    private LocalDateTime refreshTokenExpire;

    private Boolean deleted;
}