package com.holder.saas.store.takeaway.producers.entity.dto.group;

import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023-12-06
 * @description 农行聚卡慧优惠券返回
 */
@Data
public class AbcCouponRspDTO {

    /**
     * 优惠券名称
     */
    private String couponName;

    /**
     * 优惠券面值
     */
    private String value;

    /**
     * 用户实际购买优惠券金额
     */
    private Double payAmount;

    /**
     * 优惠券id
     */
    private Long couponId;

    /**
     * expired:已过期 used：已使用init：待使用 invalid：已作废  用户优惠券状态
     */
    private String status;

    /**
     * reduce：商户立减金 券码类型
     */
    private String merchantCouponType;

    /**
     * 用户优惠券使用开始时间
     */
    private Long beginTime;

    /**
     * 用户优惠券使用截止时间
     */
    private Long expiredTime;

    public boolean isAvailable() {
        return "init".equalsIgnoreCase(status);
    }
}
