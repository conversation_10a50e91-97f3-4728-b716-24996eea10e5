package com.holder.saas.store.takeaway.producers.controller;

import com.holder.saas.store.takeaway.producers.service.JdItemMappingService;
import com.holder.saas.store.takeaway.producers.service.UnItemMappingServiceFactory;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.organization.QueryBrandDTO;
import com.holderzone.saas.store.dto.takeaway.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 菜品映射相关处理（查询，设置（新增修改），删除，批量删除）
 */
@Slf4j
@RestController
@RequestMapping("/item_mapping")
public class MappingController {

    private final UnItemMappingServiceFactory unItemMappingServiceFactory;

    private final JdItemMappingService jdItemMappingService;

    @Autowired
    public MappingController(UnItemMappingServiceFactory unItemMappingServiceFactory, JdItemMappingService jdItemMappingService) {
        this.unItemMappingServiceFactory = unItemMappingServiceFactory;
        this.jdItemMappingService = jdItemMappingService;
    }

    @GetMapping("/query_type/{storeGuid}/{type}")
    public List<UnMappedType> getType(@PathVariable("storeGuid") String storeGuid, @PathVariable("type") int type) {
        if (type == 0) {
            log.info("收到ERP发送的查询菜品分类消息，storeGuid: {}，type: {}", storeGuid, "美团");
        } else if (type == 1) {
            log.info("收到ERP发送的查询菜品分类消息，storeGuid: {}，type: {}", storeGuid, "饿了么");
        } else if (type == 2) {
            log.info("收到ERP发送的查询菜品分类消息，storeGuid: {}，type: {}", storeGuid, "自营");
        } else if (type == 3) {
            log.info("收到ERP发送的查询菜品分类消息，storeGuid: {}，type: {}", storeGuid, "赚餐");
        }
        return unItemMappingServiceFactory.create(type).getType(storeGuid);
    }

    @GetMapping("/query_item/{storeGuid}/{type}")
    public List<UnMappedItem> getItem(@PathVariable("storeGuid") String storeGuid, @PathVariable("type") int type) {
        if (type == 0) {
            log.info("收到ERP发送的查询菜品消息，storeGuid: {}，type: {}", storeGuid, "美团");
        } else if (type == 1) {
            log.info("收到ERP发送的查询菜品消息，storeGuid: {}，type: {}", storeGuid, "饿了么");
        } else if (type == 2) {
            log.info("收到ERP发送的查询菜品消息，storeGuid: {}，type: {}", storeGuid, "自营");
        } else if (type == 3) {
            log.info("收到ERP发送的查询菜品消息，storeGuid: {}, type: {}", storeGuid, "赚餐");
        }
        return unItemMappingServiceFactory.create(type).getItem(storeGuid);
    }

    @PostMapping("/query_jd_item")
    public List<UnMappedItem> getJdItem(@RequestBody QueryBrandDTO queryBrandDTO) {
        return jdItemMappingService.getItem(queryBrandDTO.getStoreGuid(), queryBrandDTO.getBrandGuid());
    }

    /**
     * 批量获取菜品信息
     */
    @PostMapping("/query_items")
    public List<UnMappedItem> getItems(@RequestBody UnItemQueryReq unItemQueryReq) {
        log.info("收到ERP发送的批量查询菜品消息，入参:{}", JacksonUtils.writeValueAsString(unItemQueryReq));
        return unItemMappingServiceFactory.create(unItemQueryReq.getTakeoutType()).getItems(unItemQueryReq);
    }


    @PostMapping("/bind")
    public void bindItem(@RequestBody @Validated UnItemBindUnbindReq unItemBindUnbindReq) {
        if (log.isInfoEnabled()) {
            log.info("收到ERP发送的设置菜品映射消息，unItemBindUnbindReq: {}", JacksonUtils.writeValueAsString(unItemBindUnbindReq));
        }
        unItemMappingServiceFactory.create(unItemBindUnbindReq.getTakeoutType()).bindMapping(unItemBindUnbindReq);
    }

    @PostMapping("/unbind")
    public void unbindItem(@RequestBody @Validated UnItemBindUnbindReq unItemBindUnbindReq) {
        if (log.isInfoEnabled()) {
            log.info("收到ERP发送的删除菜品映射消息，unItemBindUnbindReq: {}", JacksonUtils.writeValueAsString(unItemBindUnbindReq));
        }
        unItemMappingServiceFactory.create(unItemBindUnbindReq.getTakeoutType()).unbindMapping(unItemBindUnbindReq);
    }

    @PostMapping("/batch_unbind")
    public void batchUnbindItem(@RequestBody @Validated UnItemBatchUnbindReq unItemBatchUnbindReq) {
        if (log.isInfoEnabled()) {
            log.info("收到ERP发送的删除菜品映射消息，unItemBatchUnbindReq: {}", JacksonUtils.writeValueAsString(unItemBatchUnbindReq));
        }
        // 解绑
        unItemBatchUnbindReq.setBindFlag(false);
        unItemMappingServiceFactory.create(unItemBatchUnbindReq.getTakeoutType()).batchUnbindMapping(unItemBatchUnbindReq);
    }

    @PostMapping("/batch_bind")
    public void batchBindItem(@RequestBody @Validated UnItemBatchUnbindReq unItemBatchUnbindReq) {
        if (log.isInfoEnabled()) {
            log.info("批量绑定菜品映射，unItemBindReq: {}", JacksonUtils.writeValueAsString(unItemBatchUnbindReq));
        }
        // 绑定
        unItemBatchUnbindReq.setBindFlag(true);
        unItemMappingServiceFactory.create(unItemBatchUnbindReq.getTakeoutType()).batchUnbindMapping(unItemBatchUnbindReq);
    }
}
