package com.holder.saas.store.takeaway.producers.service;

import com.holderzone.saas.store.dto.takeaway.UnOrder;
import eleme.openapi.sdk.api.entity.order.OOrder;
import eleme.openapi.sdk.api.entity.other.OMessage;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderRocketListener
 * @date 2018/11/15 12:00
 * @description 饿了么UnOrder解析器
 * @program holder-saas-store-takeaway
 */
public interface EleUnOrderParser {

    /**
     * @param oOrder
     * @return
     */
    UnOrder fromOOrder(OOrder oOrder);

    /**
     * 订单生效
     *
     * @param oMessage
     * @return
     */
    UnOrder fromOrderCreated(OMessage oMessage);

    /**
     * 订单已确认
     *
     * @param oMessage
     * @return
     */
    UnOrder fromOrderConfirmed(OMessage oMessage);

    /**
     * 已分配给配送员，取餐中
     */
    UnOrder fromOrderShippingDistribute(OMessage oMessage);

    /**
     * 订单配送中
     *
     * @param oMessage
     * @return
     */
    UnOrder fromOrderShipping(OMessage oMessage);

    /**
     * 订单配送完成
     *
     * @param oMessage
     * @return
     */
    UnOrder fromOrderShipSuccessful(OMessage oMessage);

    /**
     * 订单已完成
     *
     * @param oMessage
     * @return
     */
    UnOrder fromOrderFinished(OMessage oMessage);

    /**
     * 订单已取消
     *
     * @param oMessage
     * @return
     */
    UnOrder fromOrderCanceled(OMessage oMessage);

    /**
     * 订单有催单
     *
     * @param oMessage
     * @return
     */
    UnOrder fromOrderReminded(OMessage oMessage);

    /**
     * 客户取消订单
     *
     * @param oMessage
     * @return
     */
    UnOrder fromOrderCancelReq(OMessage oMessage);

    /**
     * 客户取消取消订单
     *
     * @param oMessage
     * @return
     */
    UnOrder fromOrderCancelCancelReq(OMessage oMessage);

    /**
     * 商家同意取消订单
     *
     * @param oMessage
     * @return
     */
    UnOrder fromOrderCancelAgreed(OMessage oMessage);

    /**
     * 商家不同意取消订单
     *
     * @param oMessage
     * @return
     */
    UnOrder fromOrderCancelDisagreed(OMessage oMessage);

    /**
     * 客服仲裁取消单申请有效
     *
     * @param oMessage
     * @return
     */
    UnOrder fromOrderCancelArbitrationEffective(OMessage oMessage);

    /**
     * 客户退单
     *
     * @param oMessage
     * @return
     */
    UnOrder fromOrderRefundReq(OMessage oMessage);

    /**
     * 客户取消退单
     *
     * @param oMessage
     * @return
     */
    UnOrder fromOrderCancelRefundReq(OMessage oMessage);

    /**
     * 商家同意退单
     *
     * @param oMessage
     * @return
     */
    UnOrder fromOrderRefundAgreed(OMessage oMessage);

    /**
     * 商家不同意退单
     *
     * @param oMessage
     * @return
     */
    UnOrder fromOrderRefundDisagreed(OMessage oMessage);

    /**
     * 客服仲裁退单有效
     *
     * @param oMessage
     * @return
     */
    UnOrder fromOrderRefundArbitrationEffective(OMessage oMessage);
}
