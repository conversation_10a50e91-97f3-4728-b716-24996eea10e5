package com.holder.saas.store.takeaway.producers;

import com.holderzone.sdk.util.SpringContextUtils;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import springfox.documentation.swagger2.annotations.EnableSwagger2;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;



@EnableAsync
@EnableSwagger2
@EnableEurekaClient
@EnableFeignClients
@SpringBootApplication
@EnableApolloConfig
@EnableScheduling
public class HolderSaasStoreTakeawayProducersApplication {

    public static void main(String[] args) {
        ConfigurableApplicationContext app = SpringApplication.run(HolderSaasStoreTakeawayProducersApplication.class, args);

        // 设置Spring容器上下文
        SpringContextUtils.getInstance().setCfgContext(app);
    }
}



