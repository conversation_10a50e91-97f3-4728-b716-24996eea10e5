package com.holder.saas.store.takeaway.producers.entity.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023年09月08日 17:15
 * @description 更新授权令牌返回
 */
@Data
public class MtMemberRefreshTokenRespDTO {

    private String code;

    private MemberRefreshTokenResp data;

    @Data
    public static class MemberRefreshTokenResp {

        private String accessToken;

        /**
         * accessToken的访问过期时间【单位秒】，还有多少秒后过期
         */
        private Long expireIn;

        private String opBizCode;

        private String refreshToken;

        private String scope;

    }
}
