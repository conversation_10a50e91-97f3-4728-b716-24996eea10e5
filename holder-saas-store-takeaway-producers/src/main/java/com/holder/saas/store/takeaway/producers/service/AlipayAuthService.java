package com.holder.saas.store.takeaway.producers.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holder.saas.store.takeaway.producers.entity.domain.AlipayAuthDO;
import com.holderzone.saas.store.dto.takeaway.request.AlipayAuthQO;
import com.holderzone.saas.store.dto.takeaway.request.NotifyAliPayAuthReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.AlipayAuthRespDTO;

/**
 * <p>
 * 支付宝授权 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-15
 */
public interface AlipayAuthService extends IService<AlipayAuthDO> {

    AlipayAuthRespDTO queryAuthInfo(AlipayAuthQO authQO);

    void notifyAliPayAuth(NotifyAliPayAuthReqDTO notifyDTO);
}
