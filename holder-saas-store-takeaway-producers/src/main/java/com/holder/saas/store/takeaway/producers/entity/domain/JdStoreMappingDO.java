package com.holder.saas.store.takeaway.producers.entity.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@TableName("hst_jd_store_mapping")
public class JdStoreMappingDO {

    @TableId
    private Integer id;

    /**
     * 企业GUID
     */
    private String enterpriseGuid;

    /**
     * 门店GUID
     */
    private String storeGuid;

    /**
     * 京东商户门店id
     */
    private String venderStoreId;

    /**
     * 京东商户门店名称
     */
    private String venderStoreName;

    /**
     * 逻辑删除：0=未删除，1=已删除
     */
    @TableLogic
    @TableField("is_deleted")
    private Boolean deleted;

    /**
     * 记录创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 记录修改时间
     */
    private LocalDateTime gmtModified;


}