package com.holder.saas.store.takeaway.producers.mapstruct;

import com.holderzone.saas.store.dto.takeaway.MtCbOrderDetail;
import com.holderzone.saas.store.dto.takeaway.MtQueryOrderDetail;
import org.mapstruct.Mapper;
import org.springframework.stereotype.Component;

@Component
@Mapper(componentModel = "spring")
public interface MtOrderMapstruct {

    MtCbOrderDetail fromMtQueryOrderDetail(MtQueryOrderDetail mtQueryOrderDetail);
}
