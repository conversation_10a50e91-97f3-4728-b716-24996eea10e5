package com.holder.saas.store.takeaway.producers.service.job;

import cn.hutool.core.collection.CollUtil;
import com.holder.saas.store.takeaway.producers.constant.TakeoutConstant;
import com.holder.saas.store.takeaway.producers.entity.domain.EleAuthDO;
import com.holder.saas.store.takeaway.producers.mapstruct.EleMessageMapstruct;
import com.holder.saas.store.takeaway.producers.service.EleAuthService;
import com.holder.saas.store.takeaway.producers.service.EleUnOrderParser;
import com.holder.saas.store.takeaway.producers.service.UnOrderMqService;
import com.holder.saas.store.takeaway.producers.utils.ThrowableExtUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.takeaway.OrderType;
import com.holderzone.saas.store.dto.takeaway.UnOrder;
import eleme.openapi.sdk.api.entity.message.OMessage;
import eleme.openapi.sdk.api.entity.order.OOrder;
import eleme.openapi.sdk.api.exception.ServiceException;
import eleme.openapi.sdk.api.service.MessageService;
import eleme.openapi.sdk.api.service.OrderService;
import eleme.openapi.sdk.config.Config;
import eleme.openapi.sdk.oauth.response.Token;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.stream.Collectors;

/**
 * https://open.shop.ele.me/openapi/documents/callback
 * 最下方“消息补偿”
 * <p>
 * https://open.shop.ele.me/openapi/apilist/eleme-order/eleme-order-mgetOrders
 * 批量获取订单
 * <p>
 * https://open.shop.ele.me/openapi/search?keyword=getNonReachedOMessages&type=WORK_ORDER&_t=1544530939609
 * “未到达消息”搜索结果
 * <p>
 * https://open.shop.ele.me/openapi/documents/ts
 * FAQ 第5点
 * <p>
 * https://open.shop.ele.me/openapi/documents/faq
 * 搜索getNonReachedOMessages
 */
@Slf4j
@Component
public class EleOrderJob {

    @Value("${ele.APP_ID}")
    private Integer appId;

    private final Config config;

    private final EleAuthService eleAuthService;

    private final EleUnOrderParser eleUnOrderParser;

    private final UnOrderMqService unOrderMqService;

    private final EleMessageMapstruct eleMessageMapstruct;

    private final StringRedisTemplate redisTemplate;

    @Autowired
    public EleOrderJob(Config config, EleAuthService eleAuthService, EleUnOrderParser eleUnOrderParser,
                       UnOrderMqService unOrderMqService, EleMessageMapstruct eleMessageMapstruct, StringRedisTemplate redisTemplate) {
        this.config = config;
        this.eleAuthService = eleAuthService;
        this.eleUnOrderParser = eleUnOrderParser;
        this.unOrderMqService = unOrderMqService;
        this.eleMessageMapstruct = eleMessageMapstruct;
        this.redisTemplate = redisTemplate;
    }

    @Async
    public void queryUnProcessOrders() {
        log.info("<==========(饿了么)订单轮询，最近10分钟内未被商家确认的饿了么订单列表刷新开始==========>");
        handleUnProcessOrders();
        log.info("<==========(饿了么)订单轮询，最近10分钟内未被商家确认的饿了么订单列表刷新结束==========>");
    }

    public void handleUnProcessOrders() {
        try {
            List<EleAuthDO> arrayOfEleAuthDO = eleAuthService.list(null);
            for (EleAuthDO eleAuthDO : arrayOfEleAuthDO) {
                Token token = eleAuthService.getToken(eleAuthDO);
                queryUnProcessOrders(eleAuthDO, token);
            }
        } catch (Exception e) {
            log.error("(饿了么)订单轮询，发生异常：{}", ThrowableExtUtils.asStringIfAbsent(e));
        }
    }

    private void queryUnProcessOrders(EleAuthDO eleAuthDO, Token token) {
        if (Objects.isNull(token)) {
            return;
        }
        try {
            //移除十分钟之前的数据
            redisTemplate.opsForZSet().removeRangeByScore(TakeoutConstant.ELE_ME_COMPENSATE,0,LocalDateTime.now().toEpochSecond(ZoneOffset.ofHours(8)));
            //查询出所有时间的订单
            Set<String> orderIdOverSet = redisTemplate.opsForZSet().range(
                    TakeoutConstant.ELE_ME_COMPENSATE, 0, -1);
            OrderService orderService = new OrderService(config, token);
            List<String> unProcessOrders = orderService.getUnprocessOrders(eleAuthDO.getShopId());
            if (CollectionUtils.isEmpty(unProcessOrders)) {
                return;
            }
            if(CollUtil.isNotEmpty(orderIdOverSet) && orderIdOverSet != null){
                //过滤出商家未处理的订单
                unProcessOrders.removeIf(orderIdOverSet::contains);
            }

            if(CollUtil.isEmpty(unProcessOrders)){
                return;
            }
            log.info("10分钟内未被商家确认的饿了么订单列表：{}", JacksonUtils.writeValueAsString(unProcessOrders));
            Map<String, OOrder> oOrderMap = orderService.mgetOrders(unProcessOrders);
            if (null == oOrderMap) {
                log.info("没有查询出饿了么订单 ...");
                return;
            }
            Collection<OOrder> oOrders = oOrderMap.values();
            for (OOrder oOrder : oOrders) {
                UnOrder unOrder = eleUnOrderParser.fromOOrder(oOrder);
                unOrder.setStoreGuid(eleAuthDO.getStoreGuid());
                unOrder.setEnterpriseGuid(eleAuthDO.getEnterpriseGuid());
                unOrder.setOrderType(OrderType.TAKEOUT_ORDER.getType());
                unOrder.setOrderSubType(OrderType.TakeoutSubType.ELE_TAKEOUT.getType());
                if (log.isInfoEnabled()) {
                    log.info("(饿了么)订单轮询，向ERP发送(饿了么)新订单，unOrder：{}",
                            JacksonUtils.writeValueAsString(unOrder));
                }
                unOrderMqService.sendUnOrder(unOrder);
            }
        } catch (ServiceException e) {
            log.error("enterpriseGuid: {}，storeGuid: {}，(饿了么)订单轮询，获取未处理订单失败，失败原因：{}",
                    eleAuthDO.getEnterpriseGuid(), eleAuthDO.getStoreGuid(), ThrowableExtUtils.asStringIfAbsent(e));
            eleAuthService.correctAuth(e, eleAuthDO);
        }
    }


    public void handleUnProcessOrdersByNonReachedOMessages() {
        List<EleAuthDO> arrayOfEleAuthDO = eleAuthService.list(null);
        for (EleAuthDO eleAuthDO : arrayOfEleAuthDO) {
            Token token = eleAuthService.getToken(eleAuthDO);
            if (token != null) {
                MessageService messageService = new MessageService(config, token);
                try {
                    List<OMessage> nonReachedOMessages = messageService.getNonReachedOMessages(appId);
                    for (OMessage nonReachedOMessage : nonReachedOMessages) {
                        eleme.openapi.sdk.api.entity.other.OMessage oMessage = eleMessageMapstruct.fromOMessage(nonReachedOMessage);
                        UnOrder unOrder = eleUnOrderParser.fromOrderCreated(oMessage);
                        unOrder.setStoreGuid(eleAuthDO.getStoreGuid());
                        unOrder.setEnterpriseGuid(eleAuthDO.getEnterpriseGuid());
                        unOrder.setOrderType(OrderType.TAKEOUT_ORDER.getType());
                        unOrder.setOrderSubType(OrderType.TakeoutSubType.ELE_TAKEOUT.getType());
                        if (log.isInfoEnabled()) {
                            log.info("(饿了么)订单轮询，向ERP发送(饿了么)新订单，unOrder：{}",
                                    JacksonUtils.writeValueAsString(unOrder));
                        }
                        unOrderMqService.sendUnOrder(unOrder);
                    }
                } catch (ServiceException e) {
                    log.error("enterpriseGuid: {}，storeGuid: {}，(饿了么)订单轮询，获取未到达的推送消息实体失败",
                            eleAuthDO.getEnterpriseGuid(), eleAuthDO.getStoreGuid());
                }
            }
        }
    }
}
