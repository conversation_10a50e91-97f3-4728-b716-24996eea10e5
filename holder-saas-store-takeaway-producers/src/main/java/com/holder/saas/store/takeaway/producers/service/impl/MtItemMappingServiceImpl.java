package com.holder.saas.store.takeaway.producers.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.holder.saas.store.takeaway.producers.constant.TakeoutConstant;
import com.holder.saas.store.takeaway.producers.entity.domain.MtAuthDO;
import com.holder.saas.store.takeaway.producers.entity.dto.*;
import com.holder.saas.store.takeaway.producers.entity.enums.MtItemBusinessEnum;
import com.holder.saas.store.takeaway.producers.service.MtAuthService;
import com.holder.saas.store.takeaway.producers.service.UnItemMappingService;
import com.holder.saas.store.takeaway.producers.utils.ThrowableExtUtils;
import com.holder.saas.store.takeaway.producers.utils.sdk.meituan.CipCaterTakeoutSpecialFoodBatchQueryRequest;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.constant.Constant;
import com.holderzone.saas.store.constant.takeaway.TakeawayConstants;
import com.holderzone.saas.store.dto.takeaway.*;
import com.meituan.sdk.DefaultMeituanClient;
import com.meituan.sdk.MeituanClient;
import com.meituan.sdk.MeituanResponse;
import com.meituan.sdk.model.waimaiNg.dish.dishFoodListAll.DishFoodListAllRequest;
import com.meituan.sdk.model.waimaiNg.dish.dishFoodListAll.FoodInfo;
import com.meituan.sdk.model.waimaiNg.special.specialFoodBindSpuAndSkuCode.SpecialFoodBindSpuAndSkuCodeRequest;
import com.sankuai.sjst.platform.developer.domain.RequestSysParams;
import com.sankuai.sjst.platform.developer.request.CipCaterRequest;
import com.sankuai.sjst.platform.developer.request.CipCaterTakeoutDishBaseQueryByEPoiIdRequest;
import com.sankuai.sjst.platform.developer.request.CipCaterTakeoutDishMapRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import java.io.IOException;
import java.net.URISyntaxException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Slf4j
@SuppressWarnings("Duplicates")
@Service("mtItemMappingServiceImpl")
public class MtItemMappingServiceImpl implements UnItemMappingService {

    private final MtAuthService authService;

    @Value("${mt.SIGN_KEY}")
    private String mtSignKey;

    @Value("${mt.DEVELOPER_ID}")
    private int mtDeveloperId;

    @Qualifier("mtProductQueryThreadPool")
    @Autowired
    private ExecutorService mtProductQueryThreadPool;

    @Autowired
    public MtItemMappingServiceImpl(MtAuthService authService) {
        this.authService = authService;
    }

    @Override
    public List<UnMappedType> getType(String storeGuid) {
        String msg = "查询菜品分类";
        logRequestProcessing(msg, storeGuid);

        // 根据门店ID查询外卖平台对应的门店token (首先去缓存找，缓存没有去数据库找)
        MtAuthDO mtAuthDO = authService.getAuth(storeGuid, MtBusinessIdEnum.TAKEOUT.getType());
        if (mtAuthDO == null) {
            logAuthFailureThenThrow(msg, storeGuid);
            return null;
        }

        String authToken = mtAuthDO.getAccessToken();
        CipCaterTakeoutDishBaseQueryByEPoiIdRequest request = new CipCaterTakeoutDishBaseQueryByEPoiIdRequest();
        RequestSysParams requestSysParams = new RequestSysParams(mtSignKey, authToken, TakeoutConstant.CHARSET_UTF_8);
        request.setRequestSysParams(requestSysParams);
        request.setePoiId(storeGuid);

        try {
            String result = request.doRequest();
            log.info("查询美团商品源数据，result={}", result);
            MtRespItemDTO mtRespItemDTO = JacksonUtils.toObject(MtRespItemDTO.class, result);
            MtResponseDTO.ErrorDetail error = mtRespItemDTO.getError();
            if (error != null) {
                logReplyFailedThenThrow(msg, storeGuid, error);
            }
            List<MtRespItem> mtRespItemList = mtRespItemDTO.getData();
            log.info("查询到菜品：{}", JacksonUtils.writeValueAsString(mtRespItemList));
            return mtItem2UnTypeName(mtRespItemList);
        } catch (IOException | URISyntaxException e) {
            logExceptionThenThrow(msg, storeGuid, e);
        }
        return null;
    }

    @Override
    public List<UnMappedItem> getItem(String storeGuid) {
        String msg = "查询菜品";
        logRequestProcessing(msg, storeGuid);

        // 根据门店ID查询外卖平台对应的门店token (首先去缓存找，缓存没有去数据库找)
        MtAuthDO mtAuthDO = authService.getAuth(storeGuid, MtBusinessIdEnum.TAKEOUT.getType());
        if (mtAuthDO == null) {
            logAuthFailureThenThrow(msg, storeGuid);
            return null;
        }
        //查询拼好饭以及普通商品合集
        return listNormalAndSpecialItem(mtAuthDO.getAccessToken(),storeGuid);
    }

    private List<UnMappedItem> listNormalAndSpecialItemSync(String accessToken, String storeGuid) {
        List<UnMappedItem> mergeList = Lists.newArrayList();
        List<UnMappedItem> narmalItemList = queryMergeItem(accessToken, storeGuid);
        List<UnMappedItem> specialItemList = querySpecialFood(accessToken, storeGuid);
        if(CollUtil.isNotEmpty(specialItemList)){
            mergeList.addAll(specialItemList);
        }
        if(CollUtil.isNotEmpty(narmalItemList)){
            mergeList.addAll(narmalItemList);
        }
        return mergeList;
    }

    private List<UnMappedItem> listNormalAndSpecialItem(String accessToken, String storeGuid) {
        //查询普通商品
        CompletableFuture<List<UnMappedItem>> normalItemsFuture = CompletableFuture.supplyAsync(() -> queryMergeItem(accessToken, storeGuid), mtProductQueryThreadPool);
        //查询拼好饭商品
        CompletableFuture<List<UnMappedItem>> specialItemsFuture = CompletableFuture.supplyAsync(() -> querySpecialFood(accessToken, storeGuid), mtProductQueryThreadPool);
        CompletableFuture<Void> all = CompletableFuture.allOf(normalItemsFuture, specialItemsFuture);
        try {
            all.get();
            List<UnMappedItem> normalItems = normalItemsFuture.get();
            List<UnMappedItem> specialItems = specialItemsFuture.get();
            List<UnMappedItem> items = Lists.newArrayList();
            if(CollUtil.isNotEmpty(specialItems)){
                items.addAll(specialItems);
            }
            if(CollUtil.isNotEmpty(normalItems)){
                items.addAll(normalItems);
            }
            return items;
        }catch (InterruptedException e){
            log.error("Interrupted!",e);
            Thread.currentThread().interrupt();
        }catch (Exception e){
            log.error("查询美团商品失败",e);
        }
        return null;
    }

    /**
     * 查询拼好饭商品
     * @param accessToken token
     * @param storeGuid 美团门店id
     * @return 商品列表
     */
    private List<UnMappedItem> querySpecialFood(String accessToken,String storeGuid){
        CipCaterTakeoutSpecialFoodBatchQueryRequest request = new CipCaterTakeoutSpecialFoodBatchQueryRequest(mtSignKey,accessToken,mtDeveloperId);
        //默认查询第一页20条数据
        request.setEPoiId(storeGuid);
        try {
            String rsp = request.doRequest();
            log.info("批量查询拼好饭商品列表：{}",rsp);
            MtRspDTO<MtSpecialFoodPageDTO> mtSpecialFoodPage = JSONObject.parseObject(rsp,new TypeReference<MtRspDTO<MtSpecialFoodPageDTO>>(){}.getType());
            if(mtSpecialFoodPage == null || mtSpecialFoodPage.getData() == null){
                log.error("批量查询拼好饭商品列表返回为空");
                return Collections.emptyList();
            }
            if(!mtSpecialFoodPage.success()){
                log.error("批量查询拼好饭商品列表失败，{}",mtSpecialFoodPage.getMsg());
                return Collections.emptyList();
            }
            //数据转换
            List<UnMappedItem> itemList = specialTransferUnMappedItem(mtSpecialFoodPage.getData().getWmOpenProductSpus());
            if(CollUtil.isNotEmpty(itemList)){
                itemList.forEach(e -> e.setErpStoreGuid(storeGuid));
            }
            return itemList;
        }catch (Exception error){
            log.error("批量查询拼好饭商品列表异常", error);
        }
        return Collections.emptyList();
    }

    private List<UnMappedItem> specialTransferUnMappedItem(List<MtSpecialFoodPageDTO.WmOpenProductSpu> wmOpenProductSpus) {
        if(CollUtil.isEmpty(wmOpenProductSpus)){
            return Collections.emptyList();
        }
        List<UnMappedItem> itemList = Lists.newArrayList();
        wmOpenProductSpus.forEach(spu -> spu.getSkus().forEach(sku -> {
            UnMappedItem unMappedItem = new UnMappedItem();
            unMappedItem.setUnItemId(String.valueOf(spu.getMtSpuId()));
            unMappedItem.setUnItemName(spu.getName());
            unMappedItem.setUnItemSkuId(String.valueOf(sku.getMtSkuId()));
            unMappedItem.setMtSkuId(String.valueOf(sku.getMtSkuId()));
            if(StringUtils.isEmpty(sku.getSpec())){
                unMappedItem.setUnItemNameWithSku(spu.getName());
            }else {
                unMappedItem.setUnItemNameWithSku(String.format(TakeoutConstant.BRACKET,spu.getName(),sku.getSpec()));
            }
            unMappedItem.setUnItemTypeId(String.valueOf(spu.getMtTagId()));
            unMappedItem.setUnItemTypeName(spu.getTagName());
            if(!StringUtils.isEmpty(sku.getSkuId())){
                unMappedItem.setErpItemSkuId(sku.getSkuId());
            }
            if(!StringUtils.isEmpty(spu.getAppFoodCode())){
                unMappedItem.setErpItemId(spu.getAppFoodCode());
            }
            unMappedItem.setExtendValue(JacksonUtils.writeValueAsString(spu.getSkus()));
            //设置业务类型为拼好饭
            unMappedItem.setBusinessIdentify(MtItemBusinessEnum.SPECIAL.ordinal());
            itemList.add(unMappedItem);
        }));

        return itemList;
    }

    @Override
    public List<UnMappedItem> getItems(UnItemQueryReq unItemQueryReq) {
        List<String> storeGuids = unItemQueryReq.getStoreGuids();
        String storeGuidStr = String.join(",", storeGuids);
        logRequestProcessing(TakeawayConstants.BATCH_QUERY_ITEM_OPERATE + "(美团)", storeGuidStr);
        // 查询各门店的token
        List<MtAuthDO> auths = authService.getAuths(storeGuids, MtBusinessIdEnum.TAKEOUT.getType());
        if (CollectionUtils.isEmpty(auths)) {
            log.warn("不存在token, storeGuids:{}", JacksonUtils.writeValueAsString(storeGuids));
            return Collections.emptyList();
        }
        if (storeGuids.size() != auths.size()) {
            // 部分门店没有绑定
            List<String> authStoreGuids = auths.stream().map(MtAuthDO::getEPoiId).collect(Collectors.toList());
            storeGuids.removeAll(authStoreGuids);
            log.warn("部分门店没有token, storeGuids:{}", JacksonUtils.writeValueAsString(storeGuids));
        }
        List<UnMappedItem> unMappedItemList = asyncGroupQuery(auths);
        if (!StringUtils.isEmpty(unItemQueryReq.getKeywords())) {
            unMappedItemList.removeIf(e -> !e.getUnItemNameWithSku().contains(unItemQueryReq.getKeywords()));
        }
        log.info("批量查询门店商品列表返回:{}", JacksonUtils.writeValueAsString(unMappedItemList));
        return unMappedItemList;
    }

    private static final int QUERY_TIMEOUT_SECONDS = 30;
    private static final int GROUP_DELAY_MS = 100;

    private List<UnMappedItem> asyncGroupQuery(List<MtAuthDO> auths) {
        // 分组处理认证信息
        List<List<MtAuthDO>> groupByMtAuthList = Lists.partition(auths, TakeoutConstant.MT_BATCH_QUERY_ITEM_PARTITION);
        List<UnMappedItem> unMappedItemList = Lists.newCopyOnWriteArrayList();

        // 处理每一组
        for (List<MtAuthDO> authGroup : groupByMtAuthList) {
            try {
                List<UnMappedItem> groupResults = processAuthGroup(authGroup);
                if (CollUtil.isNotEmpty(groupResults)) {
                    unMappedItemList.addAll(groupResults);
                }
                // 组间延迟
                Thread.sleep(GROUP_DELAY_MS);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("商品查询被中断", e);
                break;
            } catch (Exception e) {
                log.error("处理美团商品分组查询失败, groupSize: {}", authGroup.size(), e);
            }
        }

        return unMappedItemList;
    }

    private List<UnMappedItem> processAuthGroup(List<MtAuthDO> authGroup) throws InterruptedException {
        // 创建组内的异步任务
        List<CompletableFuture<List<UnMappedItem>>> groupFutures = authGroup.stream()
                .map(auth -> CompletableFuture.supplyAsync(
                        () -> queryItemsWithRetry(auth),
                        mtProductQueryThreadPool
                ))
                .collect(Collectors.toList());

        try {
            // 等待所有任务完成或超时
            CompletableFuture<Void> allOf = CompletableFuture.allOf(groupFutures.toArray(new CompletableFuture[0]));
            allOf.get(QUERY_TIMEOUT_SECONDS, TimeUnit.SECONDS);

            // 收集结果
            return groupFutures.stream()
                    .map(this::getResultSafely)
                    .filter(CollUtil::isNotEmpty)
                    .flatMap(List::stream)
                    .collect(Collectors.toList());
        } catch (TimeoutException e) {
            log.error("商品查询超时", e);
            cancelFutures(groupFutures);
            return Collections.emptyList();
        }catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("商品查询被中断", e);
            return Collections.emptyList();
        } catch (Exception e) {
            log.error("商品查询异常", e);
            return Collections.emptyList();
        }
    }

    private List<UnMappedItem> queryItemsWithRetry(MtAuthDO auth) {
        try {
            return listNormalAndSpecialItemSync(auth.getAccessToken(), auth.getEPoiId());
        } catch (Exception e) {
            log.error("查询美团商品失败, ePoiId: {}, error: {}",
                    auth.getEPoiId(), e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    private List<UnMappedItem> getResultSafely(CompletableFuture<List<UnMappedItem>> future) {
        try {
            return future.get();
        } catch (Exception e) {
            log.error("获取查询结果失败", e);
            return Collections.emptyList();
        }
    }

    private void cancelFutures(List<CompletableFuture<List<UnMappedItem>>> futures) {
        futures.forEach(f -> f.cancel(true));
    }


    private static final int MAX_LIMIT = 199;


    /**
     * 查询美团方 商品分类 + 商品列表
     * 美团只需要查询商品列表， 商品列表上有商品分类字段
     *
     * @param authToken 门店授权token
     * @return 商品列表
     */
    private List<UnMappedItem> queryMergeItem(String authToken, String storeGuid) {
        //调用新套餐查询商品接口
        MeituanClient  meituanClient = DefaultMeituanClient.builder((long) mtDeveloperId, mtSignKey).build();
        MtFoodListAllRequest dishFoodListAllRequest = new MtFoodListAllRequest();
        dishFoodListAllRequest.setOffset(1);
        dishFoodListAllRequest.setLimit(MAX_LIMIT);
        try {
            MeituanResponse<List<MtFoodInfo>> response = meituanClient.invokeApi(dishFoodListAllRequest, authToken);

            if (!response.isSuccess()) {
                logReplyFailedThenThrow(TakeawayConstants.BATCH_QUERY_ITEM_OPERATE, storeGuid, response.getMsg());
                return Collections.emptyList();
            }
            List<MtFoodInfo> resp = response.getData();
            log.info("查询美团菜品, 门店guid:{}, 商品返回：{}", storeGuid, JacksonUtils.writeValueAsString(resp));
            List<UnMappedItem> unMappedItemList = transferMtFoodInfo(resp);
            unMappedItemList.forEach(e -> e.setErpStoreGuid(storeGuid));
            return unMappedItemList;
        }catch (Exception e) {
            logExceptionThenThrow(TakeawayConstants.BATCH_QUERY_ITEM_OPERATE, storeGuid, e);
            return Collections.emptyList();
        }

    }

    private List<UnMappedItem> transferMtFoodInfo(List<MtFoodInfo> foodInfoList) {
        if (CollectionUtils.isEmpty(foodInfoList)) {
            return Collections.emptyList();
        }

        return foodInfoList.stream()
            .flatMap(mtFoodInfo -> {
                if (mtFoodInfo.isComboGroup()) {
                    return Stream.of(createUnMappedItem(mtFoodInfo, mtFoodInfo.getSkus().get(0)));
                } else {
                    return mtFoodInfo.getSkus().stream()
                        .map(sku -> createUnMappedItem(mtFoodInfo, sku));
                }
            })
            .collect(Collectors.toList());
    }

    private UnMappedItem createUnMappedItem(MtFoodInfo mtFoodInfo, MtFoodInfoSku sku) {
        UnMappedItem unMappedItem = new UnMappedItem();
        unMappedItem.setUnItemId(String.valueOf(mtFoodInfo.getOriginSpuId()));
        unMappedItem.setUnItemName(mtFoodInfo.getName());
        unMappedItem.setUnItemTypeId(mtFoodInfo.getTagName());
        unMappedItem.setUnItemTypeName(mtFoodInfo.getTagName());
        unMappedItem.setExtendValue(JacksonUtils.writeValueAsString(transferMtReqSkuMapping(mtFoodInfo.getSkus())));
        unMappedItem.setUnItemSkuId(String.valueOf(sku.getOriginSkuId()));
        unMappedItem.setMtSkuId(String.valueOf(sku.getMtSkuId()));
        unMappedItem.setUnItemSkuName(sku.getSpec());
        unMappedItem.setErpItemSkuId(sku.getSkuId());
        unMappedItem.setUnItemNameWithSku(sku.getSpec() != null ?
            String.format(TakeoutConstant.BRACKET, mtFoodInfo.getName(), sku.getSpec()) :
            mtFoodInfo.getName());
        return unMappedItem;
    }

    private  List<MtReqSkuMapping> transferMtReqSkuMapping(List<MtFoodInfoSku> skus) {
        if (CollectionUtils.isEmpty(skus)) {
            return Collections.emptyList();
        }
        return skus.stream()
                .map(mtFoodInfoSku -> {
                    MtReqSkuMapping mtReqSkuMapping = new MtReqSkuMapping();
                    mtReqSkuMapping.setDishSkuId(String.valueOf(mtFoodInfoSku.getOriginSkuId()));
                    mtReqSkuMapping.setEDishSkuCode(mtFoodInfoSku.getSkuId());
                    return mtReqSkuMapping;
                })
                .collect(Collectors.toList());
    }

    @Override
    public void bindMapping(UnItemBindUnbindReq unItemBindUnbindReq) {
        String storeGuid = unItemBindUnbindReq.getStoreGuid();
        logRequestProcessing(TakeoutConstant.MSG_EDIT_ITEM_MAPPING, storeGuid);

        MtAuthDO mtAuthDO = authService.getAuth(storeGuid, MtBusinessIdEnum.TAKEOUT.getType());
        if (mtAuthDO == null) {
            logAuthFailureThenThrow(TakeoutConstant.MSG_EDIT_ITEM_MAPPING, storeGuid);
            return;
        }
        if(unItemBindUnbindReq.getBusinessIdentify() == MtItemBusinessEnum.NORMAL.ordinal()){
            bindNormalItem(mtAuthDO.getAccessToken(),unItemBindUnbindReq);
            return;
        }
        //拼好饭
        if(unItemBindUnbindReq.getBusinessIdentify() == MtItemBusinessEnum.SPECIAL.ordinal()){
            bindSpecialItem(mtAuthDO.getAccessToken(),unItemBindUnbindReq);
            return;
        }
        throw new BusinessException("商品业务类型有误");
    }

    /**
     * 绑定拼好饭商品
     * @param accessToken token
     * @param unItemBindUnbindReq 映射信息
     */
    private void bindSpecialItem(String accessToken, UnItemBindUnbindReq unItemBindUnbindReq) {
        try {
            MeituanClient meituanClient = DefaultMeituanClient.builder((long) mtDeveloperId, mtSignKey).build();
            SpecialFoodBindSpuAndSkuCodeRequest specialFoodBindSpuAndSkuCodeRequest = new SpecialFoodBindSpuAndSkuCodeRequest();
            specialFoodBindSpuAndSkuCodeRequest.setEpoiId(unItemBindUnbindReq.getStoreGuid());
            specialFoodBindSpuAndSkuCodeRequest.setBusinessIdentify(1);
            specialFoodBindSpuAndSkuCodeRequest.setEDishCode(unItemBindUnbindReq.getErpItemGuid());
            specialFoodBindSpuAndSkuCodeRequest.setMtSpuId(Long.valueOf(unItemBindUnbindReq.getUnItemId()));
            specialFoodBindSpuAndSkuCodeRequest.setSkuId(unItemBindUnbindReq.getErpItemSkuId());
            specialFoodBindSpuAndSkuCodeRequest.setMtSkuId(Long.valueOf(unItemBindUnbindReq.getUnItemSkuId()));
            MeituanResponse<String> response = meituanClient.invokeApi(specialFoodBindSpuAndSkuCodeRequest, accessToken);
            log.info("绑定拼好饭商品：{}",response);
            if (!response.isSuccess()) {
                throw new BusinessException("美团返回失败：" + response.getMsg());
            }

        }catch (BusinessException e){
            log.error("绑定拼好饭商品失败",e);
            throw e;
        }catch (Exception e){
            log.error("绑定拼好饭商品失败",e);
            throw new BusinessException("绑定拼好饭商品失败，联系管理员！");
        }

    }

    /**
     * 绑定普通商品
     * @param accessToken token
     * @param unItemBindUnbindReq 映射信息
     */
    private void bindNormalItem(String accessToken, UnItemBindUnbindReq unItemBindUnbindReq) {
        CipCaterTakeoutDishMapRequest request = new CipCaterTakeoutDishMapRequest();
        RequestSysParams requestSysParams = new RequestSysParams(mtSignKey, accessToken, TakeoutConstant.CHARSET_UTF_8);
        request.setRequestSysParams(requestSysParams);
        request.setePoiId(unItemBindUnbindReq.getStoreGuid());
        MtReqItemMapping mtReqItemMapping = new MtReqItemMapping();
        mtReqItemMapping.setEDishCode(unItemBindUnbindReq.getErpItemSkuId());
        mtReqItemMapping.setDishId(unItemBindUnbindReq.getUnItemId());
        List<MtReqSkuMapping> waiMaiDishSkuMappings = JacksonUtils.toObjectList(
                MtReqSkuMapping.class, unItemBindUnbindReq.getExtendValue());
        Optional<MtReqSkuMapping> skuMappingOptional = waiMaiDishSkuMappings.stream()
                .filter(mtReqSkuMapping -> mtReqSkuMapping.getDishSkuId()
                        .equalsIgnoreCase(unItemBindUnbindReq.getUnItemSkuId()))
                .findFirst();
        if (skuMappingOptional.isPresent()) {
            skuMappingOptional.get().setEDishSkuCode(unItemBindUnbindReq.getErpItemSkuId());
        } else {
            MtReqSkuMapping mtReqSkuMapping = new MtReqSkuMapping();
            mtReqSkuMapping.setDishSkuId(unItemBindUnbindReq.getUnItemSkuId());
            mtReqSkuMapping.setEDishSkuCode(unItemBindUnbindReq.getErpItemSkuId());
            waiMaiDishSkuMappings.add(mtReqSkuMapping);
        }
        mtReqItemMapping.setWaiMaiDishSkuMappings(waiMaiDishSkuMappings);
        List<MtReqItemMapping> mtReqItemMappings = Collections.singletonList(mtReqItemMapping);
        request.setDishMappings(JacksonUtils.writeValueAsString(mtReqItemMappings));
        doReply(request, unItemBindUnbindReq.getStoreGuid(), TakeoutConstant.MSG_EDIT_ITEM_MAPPING, null);
    }

    @Override
    public void unbindMapping(UnItemBindUnbindReq unItemBindUnbindReq) {
        logRequestProcessing(TakeoutConstant.MSG_DELETE_ITEM_MAPPING, unItemBindUnbindReq.getStoreGuid());

        MtAuthDO mtAuthDO = authService.getAuth(unItemBindUnbindReq.getStoreGuid(), MtBusinessIdEnum.TAKEOUT.getType());
        if (mtAuthDO == null) {
            logAuthFailureThenThrow(TakeoutConstant.MSG_DELETE_ITEM_MAPPING, unItemBindUnbindReq.getStoreGuid());
            return;
        }
        if(unItemBindUnbindReq.getBusinessIdentify() == MtItemBusinessEnum.NORMAL.ordinal()){
            unbindNormal(mtAuthDO.getAccessToken(),unItemBindUnbindReq);
            return;
        }
        //拼好饭
        if(unItemBindUnbindReq.getBusinessIdentify() == MtItemBusinessEnum.SPECIAL.ordinal()){
            //当sku_id为default时，表示清空该值
            unItemBindUnbindReq.setErpItemSkuId("default");
            bindSpecialItem(mtAuthDO.getAccessToken(),unItemBindUnbindReq);
            return;
        }
        throw new BusinessException("商品业务类型有误");
    }

    /**
     * 解绑普通商品
     * @param authToken token
     * @param unItemBindUnbindReq 映射信息
     */
    private void unbindNormal(String authToken,UnItemBindUnbindReq unItemBindUnbindReq){
        CipCaterTakeoutDishMapRequest request = new CipCaterTakeoutDishMapRequest();
        RequestSysParams requestSysParams = new RequestSysParams(mtSignKey, authToken, TakeoutConstant.CHARSET_UTF_8);
        request.setRequestSysParams(requestSysParams);
        request.setePoiId(unItemBindUnbindReq.getStoreGuid());
        MtReqItemMapping mtReqItemMapping = new MtReqItemMapping();
        mtReqItemMapping.setDishId(unItemBindUnbindReq.getUnItemId());
        mtReqItemMapping.setEDishCode(unItemBindUnbindReq.getErpItemGuid());
        List<MtReqSkuMapping> waiMaiDishSkuMappings = JacksonUtils.toObjectList(
                MtReqSkuMapping.class, unItemBindUnbindReq.getExtendValue());
        waiMaiDishSkuMappings.stream()
                .filter(mtReqSkuMapping -> mtReqSkuMapping.getDishSkuId()
                        .equalsIgnoreCase(unItemBindUnbindReq.getUnItemSkuId()))
                .findFirst().ifPresent(mtReqSkuMapping -> mtReqSkuMapping.setEDishSkuCode(mtReqSkuMapping.getDishSkuId()));
        if (waiMaiDishSkuMappings.stream()
                .allMatch(mtReqSkuMapping -> mtReqSkuMapping.getDishSkuId()
                        .equalsIgnoreCase(mtReqSkuMapping.getEDishSkuCode()))) {
            mtReqItemMapping.setEDishCode(unItemBindUnbindReq.getUnItemId());
        }
        mtReqItemMapping.setWaiMaiDishSkuMappings(waiMaiDishSkuMappings);
        List<MtReqItemMapping> mtReqItemMappings = Collections.singletonList(mtReqItemMapping);
        request.setDishMappings(JacksonUtils.writeValueAsString(mtReqItemMappings));
        doReply(request, unItemBindUnbindReq.getStoreGuid(), TakeoutConstant.MSG_DELETE_ITEM_MAPPING, null);
    }

    @Override
    public void batchUnbindMapping(UnItemBatchUnbindReq unItemBatchUnbindReq) {
        String msg = unItemBatchUnbindReq.getBindFlag() ? "批量绑定商品映射" : "批量删除商品映射";
        String storeGuid = unItemBatchUnbindReq.getStoreGuid();
        logRequestProcessing(msg, storeGuid);

        MtAuthDO mtAuthDO = authService.getAuth(storeGuid, MtBusinessIdEnum.TAKEOUT.getType());
        if (mtAuthDO == null) {
            logAuthFailureThenThrow(msg, storeGuid);
            return;
        }
        String authToken = mtAuthDO.getAccessToken();
        //若是绑定
        if (unItemBatchUnbindReq.getBindFlag()) {
            // 绑定
            Map<String, MtReqSkuMapping> skuHandleMap = new HashMap<>();
            for (UnItemBaseMapReq unItemBaseMapReq : unItemBatchUnbindReq.getUnItemUnbindList()) {
                // 处理多规格平台商品
                handleUnSkuInfo(unItemBaseMapReq, skuHandleMap);
                //单独去进行绑定
                UnItemBindUnbindReq unItemBindUnbindReq = transferBindUnbind(unItemBaseMapReq);
                unItemBindUnbindReq.setStoreGuid(storeGuid);
                if(unItemBaseMapReq.getBusinessIdentify() == MtItemBusinessEnum.NORMAL.ordinal()){
                    bindNormalItem(authToken,unItemBindUnbindReq);
                    continue;
                }
                if(unItemBaseMapReq.getBusinessIdentify() == MtItemBusinessEnum.SPECIAL.ordinal()){
                    bindSpecialItem(authToken,unItemBindUnbindReq);
                }
            }
            return;
        }
        //解绑
        batchUnbind(mtAuthDO.getAccessToken(),unItemBatchUnbindReq);
    }

    private void batchUnbind(String authToken,UnItemBatchUnbindReq unItemBatchUnbindReq) {
        Map<String, String> extendValueMap = new HashMap<>(unItemBatchUnbindReq.getUnItemUnbindList().size());
        // 解绑
        for (UnItemBaseMapReq unItemBaseMapReq : unItemBatchUnbindReq.getUnItemUnbindList()) {
            //解绑拼好饭
            if(unItemBaseMapReq.getBusinessIdentify() == MtItemBusinessEnum.SPECIAL.ordinal()){
                UnItemBindUnbindReq unItemBindUnbindReq = transferBindUnbind(unItemBaseMapReq);
                unItemBindUnbindReq.setErpItemSkuId("default");
                unItemBindUnbindReq.setStoreGuid(unItemBatchUnbindReq.getStoreGuid());
                bindSpecialItem(authToken,unItemBindUnbindReq);
                continue;
            }
            CipCaterTakeoutDishMapRequest request = new CipCaterTakeoutDishMapRequest();
            RequestSysParams requestSysParams = new RequestSysParams(mtSignKey, authToken, TakeoutConstant.CHARSET_UTF_8);
            request.setRequestSysParams(requestSysParams);
            request.setePoiId(unItemBatchUnbindReq.getStoreGuid());
            MtReqItemMapping mtReqItemMapping = new MtReqItemMapping();
            mtReqItemMapping.setDishId(unItemBaseMapReq.getUnItemId());
            mtReqItemMapping.setEDishCode(unItemBaseMapReq.getErpItemGuid());
            String extendValue = extendValueMap.get(unItemBaseMapReq.getUnItemId());
            if (!StringUtils.hasText(extendValue)) {
                extendValue = unItemBaseMapReq.getExtendValue();
            }
            List<MtReqSkuMapping> waiMaiDishSkuMappings = JacksonUtils.toObjectList(MtReqSkuMapping.class, extendValue);
            waiMaiDishSkuMappings.stream()
                    .filter(mtReqSkuMapping -> mtReqSkuMapping.getDishSkuId()
                            .equalsIgnoreCase(unItemBaseMapReq.getUnItemSkuId()))
                    .findFirst().ifPresent(mtReqSkuMapping ->
                            mtReqSkuMapping.setEDishSkuCode(mtReqSkuMapping.getDishSkuId()));
            if (waiMaiDishSkuMappings.stream()
                    .allMatch(mtReqSkuMapping -> mtReqSkuMapping.getDishSkuId()
                            .equalsIgnoreCase(mtReqSkuMapping.getEDishSkuCode()))) {
                mtReqItemMapping.setEDishCode(unItemBaseMapReq.getUnItemId());
            }
            mtReqItemMapping.setWaiMaiDishSkuMappings(waiMaiDishSkuMappings);
            List<MtReqItemMapping> mtReqItemMappings = Collections.singletonList(mtReqItemMapping);
            request.setDishMappings(JacksonUtils.writeValueAsString(mtReqItemMappings));
            extendValueMap.put(unItemBaseMapReq.getUnItemId(), JacksonUtils.writeValueAsString(waiMaiDishSkuMappings));

            doReply(request, unItemBatchUnbindReq.getStoreGuid(),  TakeoutConstant.MSG_DELETE_ITEM_MAPPING, unItemBaseMapReq.getErpItemSkuId());
        }
    }

    private UnItemBindUnbindReq transferBindUnbind(UnItemBaseMapReq unItemBaseMapReq) {
        UnItemBindUnbindReq unItemBindUnbindReq = new UnItemBindUnbindReq();
        unItemBindUnbindReq.setErpItemSkuId(unItemBaseMapReq.getErpItemSkuId());
        unItemBindUnbindReq.setErpItemGuid(unItemBaseMapReq.getErpItemGuid());
        unItemBindUnbindReq.setUnItemId(unItemBaseMapReq.getUnItemId());
        unItemBindUnbindReq.setUnItemSkuId(unItemBaseMapReq.getUnItemSkuId());
        unItemBindUnbindReq.setExtendValue(unItemBaseMapReq.getExtendValue());
        return unItemBindUnbindReq;
    }

    private void handleUnSkuInfo(UnItemBaseMapReq unItemBaseMapReq, Map<String, MtReqSkuMapping> skuHandleMap) {
        List<MtReqSkuMapping> waiMaiDishSkuMappings = JacksonUtils.toObjectList(
                MtReqSkuMapping.class, unItemBaseMapReq.getExtendValue());
        if (waiMaiDishSkuMappings.size() > Constant.NUMBER_ONE) {
            Map<String, MtReqSkuMapping> skuMappingMap = waiMaiDishSkuMappings.stream()
                    .collect(Collectors.toMap(MtReqSkuMapping::getDishSkuId, Function.identity(), (v1, v2) -> v1));
            skuMappingMap.forEach((dishSkuId, other) -> {
                MtReqSkuMapping skuHandle = skuHandleMap.get(dishSkuId);
                if (!ObjectUtils.isEmpty(skuHandle)) {
                    other.setEDishSkuCode(skuHandle.getEDishSkuCode());
                }
            });
            unItemBaseMapReq.setExtendValue(JacksonUtils.writeValueAsString(waiMaiDishSkuMappings));

            MtReqSkuMapping thisBind = skuMappingMap.get(unItemBaseMapReq.getUnItemSkuId());
            if (!ObjectUtils.isEmpty(thisBind)) {
                thisBind.setEDishSkuCode(unItemBaseMapReq.getErpItemSkuId());
                skuHandleMap.put(thisBind.getDishSkuId(), thisBind);
            } else {
                MtReqSkuMapping mtReqSkuMapping = new MtReqSkuMapping();
                mtReqSkuMapping.setDishSkuId(unItemBaseMapReq.getUnItemSkuId());
                mtReqSkuMapping.setEDishSkuCode(unItemBaseMapReq.getErpItemSkuId());
                skuHandleMap.put(mtReqSkuMapping.getDishSkuId(), mtReqSkuMapping);
            }
        }
    }

    private List<UnMappedType> mtItemType2UnItemType(List<MtRespItemType> mtRespItemTypeList) {
        if (CollectionUtils.isEmpty(mtRespItemTypeList)) {
            return Collections.emptyList();
        }
        return mtRespItemTypeList.stream()
                .map(mtRespItemType -> {
                    UnMappedType unMappedType = new UnMappedType();
                    unMappedType.setUnItemTypeId(mtRespItemType.getName());
                    unMappedType.setUnItemTypeName(mtRespItemType.getName());
                    return unMappedType;
                })
                .collect(Collectors.toList());
    }

    private List<UnMappedItem> mtItem2UnItem(List<MtRespItem> mtRespItemList) {
        if (CollectionUtils.isEmpty(mtRespItemList)) {
            return Collections.emptyList();
        }
        return mtRespItemList.stream()
                .flatMap(mtRespItem -> {
                    List<UnMappedItem> unMappedItems = new ArrayList<>();
                    List<MtRespItemSku> mtRespItemSkus = mtRespItem.getWaiMaiDishSkuBases();
                    for (MtRespItemSku mtRespItemSku : mtRespItemSkus) {
                        UnMappedItem unMappedItem = new UnMappedItem();
                        unMappedItem.setUnItemId(mtRespItem.getDishId());
                        unMappedItem.setUnItemName(mtRespItem.getDishName());
                        unMappedItem.setUnItemSkuId(mtRespItemSku.getDishSkuId());
                        unMappedItem.setMtSkuId(mtRespItemSku.getMtSkuId());
                        unMappedItem.setUnItemSkuName(mtRespItemSku.getSpec());
                        if (StringUtils.hasText(mtRespItemSku.getSpec())) {
                            unMappedItem.setUnItemNameWithSku(String.format(TakeoutConstant.BRACKET,mtRespItemSku.getDishSkuName(),mtRespItemSku.getSpec()));
                        } else {
                            unMappedItem.setUnItemNameWithSku(mtRespItemSku.getDishSkuName());
                        }
                        unMappedItem.setUnItemTypeId(mtRespItem.getCategoryName());
                        unMappedItem.setUnItemTypeName(mtRespItem.getCategoryName());
                        unMappedItem.setErpItemSkuId(mtRespItemSku.getEDishSkuCode());
                        unMappedItem.setExtendValue(JacksonUtils.writeValueAsString(mtRespItemSkus));
                        unMappedItems.add(unMappedItem);
                    }
                    return unMappedItems.stream();
                })
                .collect(Collectors.toList());
    }


    private List<UnMappedType> mtItem2UnTypeName(List<MtRespItem> mtRespItemList) {
        if (CollectionUtils.isEmpty(mtRespItemList)) {
            return Collections.emptyList();
        }
        List<String> unMappedTypeList = mtRespItemList
                .stream()
                .map(MtRespItem::getCategoryName).distinct().collect(Collectors.toList());
        return unMappedTypeList.stream().map(e -> {
            UnMappedType unMappedType = new UnMappedType();
            unMappedType.setUnItemTypeId(e);
            unMappedType.setUnItemTypeName(e);
            return unMappedType;
        }).collect(Collectors.toList());
    }


    private void doReply(CipCaterRequest request, String storeGuid, String msg, String erpItemSkuId) {
        try {
            log.info("request={}", JacksonUtils.writeValueAsString(request));
            String result = request.doRequest();
            MtResponseDTO mtResponseDTO = JacksonUtils.toObject(MtResponseDTO.class, result);
            MtResponseDTO.ErrorDetail error = mtResponseDTO.getError();
            if (error != null) {
                logReplyFailedThenThrow(msg, storeGuid, error);
            }
            if (mtResponseDTO.isOK()) {
                if (!StringUtils.hasText(erpItemSkuId)) {
                    logRequestSucceed(msg, storeGuid);
                } else {
                    logRequestBatchSucceed(msg, storeGuid, erpItemSkuId);
                }
            } else {
                logReplyFailedThenThrow(msg, storeGuid, mtResponseDTO.getError());
            }
        } catch (IOException | URISyntaxException e) {
            logExceptionThenThrow(msg, storeGuid, e);
        }
    }

    private void logRequestProcessing(String msg, String storeGuid) {
        log.info("Request(美团){}，storeGuid: {}，处理中", msg, storeGuid);
    }

    private void logRequestSucceed(String msg, String storeGuid) {
        log.info("Request(美团){}，storeGuid: {}，处理成功", msg, storeGuid);
    }

    private void logRequestBatchSucceed(String msg, String storeGuid, String erpItemSkuId) {
        log.info("Request(美团){}，storeGuid: {}，erpItemGuid: {}，处理成功", msg, storeGuid, erpItemSkuId);
    }

    private void logAuthFailureThenThrow(String msg, String storeGuid) {
        log.error("Request(美团){}，处理失败: 根据storeGuid: {} 未查询到Token", msg, storeGuid);
        throw new BusinessException("业务失败：" + "门店未绑定");
    }

    private void logReplyFailedThenThrow(String msg, String storeGuid, MtResponseDTO.ErrorDetail errorDetail) {
        log.error("Request(美团){}，storeGuid: {}，处理失败，code: {}, message: {}",
                msg, storeGuid, errorDetail.getCode(), errorDetail.getMessage());
        authService.correctAuth(storeGuid, MtBusinessIdEnum.TAKEOUT.getType(), errorDetail.getError_type());
        throw new BusinessException("业务失败：" + errorDetail.getMessage());
    }

    private void logReplyFailedThenThrow(String msg, String storeGuid,String errorMsg) {
        log.error("Request(美团){}，storeGuid: {}，message: {}", msg,storeGuid,  errorMsg);
        throw new BusinessException("业务失败：" + (StringUtils.isEmpty(msg) ? "第三方平台返回错误": errorMsg));
    }

    private void logExceptionThenThrow(String msg, String storeGuid, Exception e) {
        if (log.isErrorEnabled()) {
            log.error("Request(美团){}，storeGuid: {}，处理失败: {}", msg, storeGuid,
                    ThrowableExtUtils.asStringIfAbsent(e));
        }
        throw new BusinessException("业务失败：" + ThrowableExtUtils.asStringIfAbsent(e));
    }

}
