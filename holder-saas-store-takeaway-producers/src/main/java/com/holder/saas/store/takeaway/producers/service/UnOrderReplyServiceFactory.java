package com.holder.saas.store.takeaway.producers.service;

import com.holderzone.saas.store.dto.takeaway.OrderType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderRocketListener
 * @date 2018/11/15 12:00
 * @description UnOrder回复平台服务工厂
 * @program holder-saas-store-takeaway
 */
@Component
public class UnOrderReplyServiceFactory {

    private final UnOrderReplyService mtOrderReplyServiceImpl;

    private final UnOrderReplyService eleOrderReplyServiceImpl;

    private final UnOrderReplyService OwnOrderReplyServiceImpl;

    private final UnOrderReplyService TcdOrderReplyServiceImpl;

    private final UnOrderReplyService jdOrderReplyServiceImpl;

    @Autowired
    public UnOrderReplyServiceFactory(@Qualifier("mtOrderReplyServiceImpl") UnOrderReplyService mtOrderReplyServiceImpl,
                                      @Qualifier("eleOrderReplyServiceImpl") UnOrderReplyService eleOrderReplyServiceImpl,
                                      @Qualifier("ownOrderReplyServiceImpl") UnOrderReplyService OwnOrderReplyServiceImpl,
                                      @Qualifier("tcdOrderReplyServiceImpl") UnOrderReplyService TcdOrderReplyServiceImpl,
                                      @Qualifier("jdOrderReplyServiceImpl") UnOrderReplyService jdOrderReplyServiceImpl) {
        this.mtOrderReplyServiceImpl = mtOrderReplyServiceImpl;
        this.eleOrderReplyServiceImpl = eleOrderReplyServiceImpl;
        this.OwnOrderReplyServiceImpl = OwnOrderReplyServiceImpl;
        this.TcdOrderReplyServiceImpl=TcdOrderReplyServiceImpl;
        this.jdOrderReplyServiceImpl = jdOrderReplyServiceImpl;
    }

    /**
     * 根据平台的类型选择对应的实现类(0, "美团外卖"  1, "饿了么外卖"  ...)
     *
     * @param takeoutSubType
     * @return
     */
    public UnOrderReplyService create(OrderType.TakeoutSubType takeoutSubType) {
        switch (takeoutSubType) {
            case MT_TAKEOUT:
                return mtOrderReplyServiceImpl;
            case ELE_TAKEOUT:
                return eleOrderReplyServiceImpl;
            case OWN_TAKEOUT:
                return OwnOrderReplyServiceImpl;
            case TCD_TAKEOUT:
                return TcdOrderReplyServiceImpl;
            case JD_TAKEOUT:
                return jdOrderReplyServiceImpl;
            default:
                throw new UnsupportedOperationException("外卖类型不支持[" + takeoutSubType.getType() + "]");
        }
    }
}
