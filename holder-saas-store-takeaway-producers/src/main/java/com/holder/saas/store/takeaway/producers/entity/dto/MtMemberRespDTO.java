package com.holder.saas.store.takeaway.producers.entity.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023年09月08日 14:59
 * @description 美团会员信息
 */
@Data
public class MtMemberRespDTO implements Serializable{

    private static final long serialVersionUID = 1474081472327658640L;

    private String code;

    private String msg;

    private String traceId;

    private MemberResp data;

    public boolean checkSuccess() {
        return "OP_SUCCESS".equals(this.code);
    }

    public boolean checkDataSuccess() {
        return "SUCCESS".equals(this.data.status);
    }

    @Data
    public static class MemberResp implements Serializable {

        private static final long serialVersionUID = -126945318953750220L;

        private String status;

        private MemberResult result;

        private String errMsg;

    }

    @Data
    public static class MemberResult implements Serializable {

        private static final long serialVersionUID = -6781220103939943763L;

        private List<FormItem> formItemList;

        private Long receiveTime;

        private Long expireTime;

        private String cardNumber;

    }

    @Data
    public static class FormItem implements Serializable {

        private static final long serialVersionUID = -8206751928612709262L;

        private String itemEnum;

        private String value;

    }
}
