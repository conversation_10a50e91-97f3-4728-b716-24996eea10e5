package com.holder.saas.store.takeaway.producers.utils.sdk.jd.dto;

import lombok.Data;

@Data
public class ShopCategory {

    /**
     * 商家店内分类编号
     */
    private Long id;

    /**
     * 父店内分类编号
     */
    private Long pid;

    /**
     * 店内分类名称
     */
    private String shopCategoryName;

    /**
     * 店内分类级别
     */
    private Integer shopCategoryLevel;

    /**
     * 类型,0和null:代表普通分类,1:代表必选分类
     */
    private Integer categoryType;

    /**
     * 店内分类排序
     */
    private Long sort;

}
