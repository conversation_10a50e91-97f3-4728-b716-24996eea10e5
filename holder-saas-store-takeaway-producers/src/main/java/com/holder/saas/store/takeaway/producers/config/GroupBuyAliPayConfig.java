package com.holder.saas.store.takeaway.producers.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/11/13
 * @description 支付宝团购配置类
 */
@Data
@Component
@ConfigurationProperties(prefix = "group.alipay")
public class GroupBuyAliPayConfig {

    /**
     * 应用私钥
     */
    private String privateKey;

    /**
     * 支付宝公钥
     */
    private String alipayPublicKey;

    private String appId;

}
