package com.holder.saas.store.takeaway.producers.controller;

import com.holder.saas.store.takeaway.producers.service.UnOrderReplyServiceFactory;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.takeaway.OrderType.TakeoutSubType;
import com.holderzone.saas.store.dto.takeaway.UnOrder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
public class ReplyController {

    private final UnOrderReplyServiceFactory unOrderReplyServiceFactory;

    @Autowired
    public ReplyController(UnOrderReplyServiceFactory unOrderReplyServiceFactory) {
        this.unOrderReplyServiceFactory = unOrderReplyServiceFactory;
    }

    @PostMapping("/reply")
    public void reply(@RequestBody UnOrder unOrder) {
        if (log.isInfoEnabled()) {
            log.info("收到ERP发送的Reply消息，unOrder: {}", JacksonUtils.writeValueAsString(unOrder));
        }
        //得到平台的类型(0, "美团外卖"  1, "饿了么外卖"  ...)
        TakeoutSubType takeoutSubType = TakeoutSubType.ofType(unOrder.getOrderSubType());
        //对订单请求进行回复
        unOrderReplyServiceFactory.create(takeoutSubType).doReply(unOrder);
    }
}
