package com.holder.saas.store.takeaway.producers.entity.dto.group;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023-06-19
 * @description
 */
@Data
public class DouYinCouponCancelReqDTO {

    /**
     * 代表一张券码的标识
     */
    @JSONField(name = "certificate_id")
    private String certificateId;

    /**
     * 代表券码一次核销的唯一标识
     */
    @JSONField(name = "verify_id")
    private String verifyId;

    public static String buildJsonString(String certificateId, String verifyId) {
        DouYinCouponCancelReqDTO cancelReq = new DouYinCouponCancelReqDTO();
        cancelReq.setCertificateId(certificateId);
        cancelReq.setVerifyId(verifyId);
        return JSON.toJSONString(cancelReq);
    }
}
