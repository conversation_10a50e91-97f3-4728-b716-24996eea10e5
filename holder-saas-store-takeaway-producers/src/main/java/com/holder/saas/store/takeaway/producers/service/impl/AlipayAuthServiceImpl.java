package com.holder.saas.store.takeaway.producers.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holder.saas.store.takeaway.producers.entity.domain.AlipayAuthDO;
import com.holder.saas.store.takeaway.producers.mapper.AlipayAuthMapper;
import com.holder.saas.store.takeaway.producers.service.AlipayAuthService;
import com.holder.saas.store.takeaway.producers.service.DistributedService;
import com.holder.saas.store.takeaway.producers.service.converter.AuthConverter;
import com.holder.saas.store.takeaway.producers.service.rpc.CloudEnterpriseService;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.resource.common.dto.enterprise.MultiMemberDTO;
import com.holderzone.saas.store.dto.takeaway.request.AlipayAuthQO;
import com.holderzone.saas.store.dto.takeaway.request.NotifyAliPayAuthReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.AlipayAuthRespDTO;
import com.holderzone.saas.store.enums.common.BooleanEnum;
import com.holderzone.saas.store.enums.takeaway.NotifyTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

/**
 * <p>
 * 支付宝授权 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-15
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AlipayAuthServiceImpl extends ServiceImpl<AlipayAuthMapper, AlipayAuthDO> implements AlipayAuthService {

    private final AlipayAuthMapper alipayAuthMapper;

    private final DistributedService distributedService;

    private final CloudEnterpriseService cloudService;

    @Override
    public AlipayAuthRespDTO queryAuthInfo(AlipayAuthQO authQO) {
        MultiMemberDTO multiMemberDTO = cloudService.findMemberInfoByOrganizationGuid(authQO.getStoreGuid());
        if (ObjectUtils.isEmpty(multiMemberDTO)) {
            log.error("门店[{}]查询云平台运营主体失败", authQO.getStoreGuid());
            throw new BusinessException("云平台服务异常");
        }
        AlipayAuthDO alipayAuthDO = alipayAuthMapper.selectOne(new LambdaQueryWrapper<AlipayAuthDO>()
                .eq(AlipayAuthDO::getOperSubjectGuid, multiMemberDTO.getMultiMemberGuid())
                .eq(AlipayAuthDO::getEnterpriseGuid, authQO.getEnterpriseGuid())
                .eq(AlipayAuthDO::getIsDeleted, BooleanEnum.FALSE)
        );
        return AuthConverter.alipayAuthDO2AlipayAuthDTO(alipayAuthDO);
    }

    @Override
    public void notifyAliPayAuth(NotifyAliPayAuthReqDTO notifyDTO) {
        if (NotifyTypeEnum.AUTH.getCode() == notifyDTO.getNotifyType()) {
            AlipayAuthDO authDO = this.getOne(new LambdaQueryWrapper<AlipayAuthDO>()
                    .eq(AlipayAuthDO::getOperSubjectGuid, notifyDTO.getOperSubjectGuid())
                    .eq(AlipayAuthDO::getEnterpriseGuid, notifyDTO.getEnterpriseGuid())
            );
            if (ObjectUtils.isEmpty(authDO)) {
                AlipayAuthDO alipayAuthDO = AuthConverter.notifyDTO2AlipayAuthDO(notifyDTO);
                alipayAuthDO.setGuid(distributedService.nextAliPayGuid());
                alipayAuthMapper.insert(alipayAuthDO);
                return;
            }
            authDO.setAppAuthToken(notifyDTO.getAppAuthToken());
            authDO.setAppId(notifyDTO.getAppId());
            alipayAuthMapper.updateById(authDO);
            return;
        }
        this.remove(new LambdaQueryWrapper<AlipayAuthDO>()
                .eq(AlipayAuthDO::getOperSubjectGuid, notifyDTO.getOperSubjectGuid()));
    }
}
