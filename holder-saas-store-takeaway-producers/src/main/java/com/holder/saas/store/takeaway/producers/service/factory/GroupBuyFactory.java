package com.holder.saas.store.takeaway.producers.service.factory;

import com.holder.saas.store.takeaway.producers.service.GroupBuyService;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.enums.GroupBuyTypeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @create 2023-06-19
 * @description 团购服务工厂类
 */
@Component
public class GroupBuyFactory {

    private final GroupBuyService douYinGroupBuyServiceImpl;

    private final GroupBuyService daZhongGroupBuyServiceImpl;

    private final GroupBuyService meiTuanGroupBuyServiceImpl;

    private final GroupBuyService aliPayGroupBuyServiceImpl;

    private final GroupBuyService abcGroupBuyServiceImpl;

    @Autowired
    public GroupBuyFactory(@Qualifier("douYinGroupBuyServiceImpl") GroupBuyService douYinGroupBuyServiceImpl,
                           @Qualifier("daZhongGroupBuyServiceImpl") GroupBuyService daZhongGroupBuyServiceImpl,
                           @Qualifier("aliPayGroupBuyServiceImpl") GroupBuyService aliPayGroupBuyServiceImpl,
                           @Qualifier("abcGroupBuyServiceImpl") GroupBuyService abcGroupBuyServiceImpl,
                           GroupBuyService meiTuanGroupBuyServiceImpl) {
        this.douYinGroupBuyServiceImpl = douYinGroupBuyServiceImpl;
        this.daZhongGroupBuyServiceImpl = daZhongGroupBuyServiceImpl;
        this.meiTuanGroupBuyServiceImpl = meiTuanGroupBuyServiceImpl;
        this.aliPayGroupBuyServiceImpl = aliPayGroupBuyServiceImpl;
        this.abcGroupBuyServiceImpl = abcGroupBuyServiceImpl;
    }

    public GroupBuyService build(Integer groupBuyType) {
        switch (GroupBuyTypeEnum.groupBuyType(groupBuyType)) {
            case DOU_YIN:
                return douYinGroupBuyServiceImpl;
            case DA_ZHONG_DIAN_PIN:
                return daZhongGroupBuyServiceImpl;
            case MEI_TUAN:
                return meiTuanGroupBuyServiceImpl;
            case ALIPAY:
                return aliPayGroupBuyServiceImpl;
            case ABC:
                return abcGroupBuyServiceImpl;
            default:
                throw new BusinessException("不支持的团购类型");
        }

    }
}
