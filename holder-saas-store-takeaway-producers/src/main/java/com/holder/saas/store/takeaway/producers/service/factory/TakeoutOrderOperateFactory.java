package com.holder.saas.store.takeaway.producers.service.factory;

import com.holder.saas.store.takeaway.producers.service.TakeoutOrderOperateService;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.takeaway.OrderType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description 外卖订单操作工厂
 */
@Component
public class TakeoutOrderOperateFactory {

    private final TakeoutOrderOperateService eleMeTakeoutOrderOperateServiceImpl;

    private final TakeoutOrderOperateService zcTakeoutOrderOperateServiceImpl;

    private final TakeoutOrderOperateService mtTakeoutOrderOperateServiceImpl;

    private final TakeoutOrderOperateService jdTakeoutOrderOperateServiceImpl;

    @Autowired
    public TakeoutOrderOperateFactory(@Qualifier("eleMeTakeoutOrderOperateServiceImpl") TakeoutOrderOperateService eleMeTakeoutOrderOperateServiceImpl,
                                      @Qualifier("zcTakeoutOrderOperateServiceImpl") TakeoutOrderOperateService zcTakeoutOrderOperateServiceImpl,
                                      @Qualifier("mtTakeoutOrderOperateServiceImpl") TakeoutOrderOperateService mtTakeoutOrderOperateServiceImpl,
                                      @Qualifier("jdTakeoutOrderOperateServiceImpl") TakeoutOrderOperateService jdTakeoutOrderOperateServiceImpl) {
        this.eleMeTakeoutOrderOperateServiceImpl = eleMeTakeoutOrderOperateServiceImpl;
        this.zcTakeoutOrderOperateServiceImpl = zcTakeoutOrderOperateServiceImpl;
        this.mtTakeoutOrderOperateServiceImpl = mtTakeoutOrderOperateServiceImpl;
        this.jdTakeoutOrderOperateServiceImpl = jdTakeoutOrderOperateServiceImpl;
    }

    public TakeoutOrderOperateService type(Integer type) {
        switch (OrderType.TakeoutSubType.ofType(type)){
            case MT_TAKEOUT:
                return mtTakeoutOrderOperateServiceImpl;
            case ELE_TAKEOUT:
                return eleMeTakeoutOrderOperateServiceImpl;
            case TCD_TAKEOUT:
                return zcTakeoutOrderOperateServiceImpl;
            case JD_TAKEOUT:
                return jdTakeoutOrderOperateServiceImpl;
        }
        throw new BusinessException("不支持的外卖类型");
    }
}
