package com.holder.saas.store.takeaway.producers.entity.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.holderzone.framework.util.DateTimeUtils;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("hst_holder_auth")
public class HolderAuthDO implements Serializable {

    private static final long serialVersionUID = 5466838236697202547L;

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 唯一标识
     */
    private String guid;

    /**
     * 商户GUID
     */
    private String enterpriseGuid;

    /**
     * 门店GUID，即ePoiId
     */
    private String storeGuid;

    /**
     * 饿了么商户ID
     */
    private Long userId;

    /**
     * 饿了么商户名称
     */
    private String userName;

    /**
     * 商户编码
     */
    private String code;

    /**
     * 商户编码
     */
    private String shopName;

    /**
     * 访问token
     */
    private String accessToken;

    /**
     * 刷新token
     */
    private String refreshToken;


    /**
     * token生效时间
     */
    private LocalDateTime activeTime;

    /**
     *fixme 若未来需要开通自营外卖的配送，那么解除该条注释即可
     *       自营外卖平台配送方式
     *
     *      private Integer deliveryType;
     */

    /**
     * token有效时间，单位秒
     */
    private Long expires;

    /**
     * token过期时间
     */
    private LocalDateTime expireTime;


    /**
     * token生效时间
     */
    private LocalDateTime refreshActiveTime;

    /**
     * token有效时间，单位秒
     */
    private Long refreshExpires;

    /**
     * token过期时间
     */
    private LocalDateTime refreshExpireTime;


    /**
     * 逻辑删除：0=未删除，1=已删除
     */
    @TableLogic
    @TableField("is_deleted")
    private Boolean deleted;

    /**
     * 记录创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 记录修改时间
     */
    private LocalDateTime gmtModified;

    public static HolderAuthDO ofToken(String accessToken, String refreshToken, Integer refreshExpires) {
        HolderAuthDO holderAuthDO = new HolderAuthDO();
        holderAuthDO.setAccessToken(accessToken);
        holderAuthDO.setRefreshToken(refreshToken);
        holderAuthDO.setActiveTime(DateTimeUtils.now());
        holderAuthDO.setExpires((long) refreshExpires * 60 * 60);
        holderAuthDO.setExpireTime(DateTimeUtils.now().plusHours(refreshExpires));
        holderAuthDO.setRefreshActiveTime(DateTimeUtils.now());
        holderAuthDO.setRefreshExpires((long) refreshExpires * 60 * 60);
        holderAuthDO.setRefreshExpireTime(DateTimeUtils.now().plusHours(refreshExpires));
        return holderAuthDO;
    }

}