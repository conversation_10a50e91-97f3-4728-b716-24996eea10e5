package com.holder.saas.store.takeaway.producers.utils.sdk.jd;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.holder.saas.store.takeaway.producers.utils.sdk.jd.dto.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @description 新查询商家已上传商品信息列表接口
 */
@Slf4j
public class QuerySkuInfoRequest extends AbstractJdRequest{


    public QuerySkuInfoRequest() {
    }

    public QuerySkuInfoRequest(String appKey, String appSecret, String token) {
       super.appKey = appKey;
       super.appSecret = appSecret;
       super.token = token;
    }

    public List<SkuMain> execute(QuerySkuInfoReqDTO skuInfoReq){
        try {
            String rsp = super.execute(JSON.toJSONString(skuInfoReq), "/pms/querySkuInfoList");
            CommonRspDTO<SkuQueryResponse> querySkuInfoRspDTO = JSON.parseObject(rsp, new TypeReference<CommonRspDTO<SkuQueryResponse>>(){});
            if(!querySkuInfoRspDTO.isSuccess()){
                return Collections.emptyList();
            }
            String skuString = querySkuInfoRspDTO.getResult().getResult();
            if(StringUtils.isEmpty(skuString)){
                return Collections.emptyList();
            }
            return JSON.parseArray(skuString, SkuMain.class);
        }catch (Exception e){
            log.error("查询商家商品列表失败",e);
        }
        return Collections.emptyList();
    }

    /**
     * 查询所有商品列表
     * @return 商品列表
     */
    public List<SkuMain> execute(String skuName){
        List<SkuMain> all = Lists.newArrayList();
        int page = 1;
        int pageSize = 20;
        QuerySkuInfoReqDTO reqDTO = QuerySkuInfoReqDTO.builder().pageNo(page).pageSize(pageSize).isFilterDel("0").build();
        if(StringUtils.isNotEmpty(skuName)){
            reqDTO.setSkuName(skuName);
        }
        // 递归查询
        while (true){
            SkuQueryResponse skuQueryResponse = recursionQuery(reqDTO);
            if(skuQueryResponse == null || StringUtils.isEmpty(skuQueryResponse.getResult())){
                break;
            }
            all.addAll(JSON.parseArray(skuQueryResponse.getResult(), SkuMain.class));
            if (skuQueryResponse.getSearchAfterSkuId() == null) {
                break;
            }
            page++;
            reqDTO.setPageNo(page);
            reqDTO.setSearchAfterSkuId(skuQueryResponse.getSearchAfterSkuId());
        }
                
        return all;
    }

    private SkuQueryResponse recursionQuery(QuerySkuInfoReqDTO reqDTO){
        try {
            String rsp = super.execute(JSON.toJSONString(reqDTO), "/pms/querySkuInfoList");
            CommonRspDTO<SkuQueryResponse> querySkuInfoRspDTO = JSON.parseObject(rsp, new TypeReference<CommonRspDTO<SkuQueryResponse>>(){});
            if(!querySkuInfoRspDTO.isSuccess()){
                return null;
            }
            return querySkuInfoRspDTO.getResult();
        }catch (Exception e){
            log.error("查询商家商品列表失败",e);
        }
        return null;
    }
}
