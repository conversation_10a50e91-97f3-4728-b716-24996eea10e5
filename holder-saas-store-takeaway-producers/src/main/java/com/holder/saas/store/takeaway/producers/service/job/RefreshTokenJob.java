package com.holder.saas.store.takeaway.producers.service.job;

import com.holder.saas.store.takeaway.producers.service.EleAuthService;
import com.holder.saas.store.takeaway.producers.service.HolderAuthService;
import com.holder.saas.store.takeaway.producers.service.MtHeartbeatService;
import eleme.openapi.sdk.api.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderRocketListener
 * @date 2018/11/15 12:00
 * @description 定时任务，包含token刷新、心跳上报
 * @program holder-saas-store-takeaway
 */
@Slf4j
@Component
public class RefreshTokenJob {

    @Value("${ele.TOKEN_REFRESH_AHEAD_HOURS}")
    private Integer tokenRefreshAheadHours;

    @Value("${own.TOKEN_REFRESH_AHEAD_HOURS}")
    private Integer ownTokenRefreshAheadHours;

    private final EleAuthService eleAuthService;

    private final HolderAuthService holderAuthService;

    private final MtHeartbeatService mtHeartbeatService;

    @Autowired
    public RefreshTokenJob(EleAuthService eleAuthService, MtHeartbeatService mtHeartbeatService,
                           HolderAuthService holderAuthService) {
        this.eleAuthService = eleAuthService;
        this.mtHeartbeatService = mtHeartbeatService;
        this.holderAuthService = holderAuthService;
    }

    /**
     * 每天凌晨4点执行
     *
     * @throws Exception
     * @throws ServiceException
     */
    @Async
    public void refreshEleToken() {
        log.info("=========(饿了么)定时刷新token开始=============");
        // 调用eleAutoHandle刷新token
        eleAuthService.refreshToken(tokenRefreshAheadHours * 60 * 60);
        log.info("=========(饿了么)定时刷新token结束=============");
    }


    /**
     * 每天凌晨4点执行
     *
     * @throws Exception
     * @throws ServiceException
     */
    @Async
    public void refreshOwnToken() {
        log.info("=========(自营外卖平台)定时刷新token开始=============");
        // 调用eleAutoHandle刷新token
        holderAuthService.refreshToken(ownTokenRefreshAheadHours * 60 * 60);
        log.info("=========(自营外卖平台)定时刷新token结束=============");
    }


    /**
     * 每隔30秒调用一次
     *
     * @throws ServiceException
     * @throws Exception
     */
//    @Scheduled(cron = "0/30 * *  * * ? ")
    public void heartbeatReport() throws ServiceException, Exception {
        log.info("=========心跳上报开始=============");
        mtHeartbeatService.heartbeatReport();
        log.info("=========心跳上报结束=============");
    }
}
