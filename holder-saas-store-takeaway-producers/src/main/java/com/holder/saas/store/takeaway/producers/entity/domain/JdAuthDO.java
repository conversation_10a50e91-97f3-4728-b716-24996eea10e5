package com.holder.saas.store.takeaway.producers.entity.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@TableName("hst_jd_auth")
public class JdAuthDO {

    @TableId
    private Integer id;

    /**
     * 企业品牌guid
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String brandGuid;

    /**
     * 企业GUID
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String enterpriseGuid;

    /**
     * 京东商户id
     */
    private String venderId;

    /**
     * 京东商户昵称
     */
    private String userNick;

    /***
     * 京东token
     */
    private String token;

    /**
     * token有效时长
     */
    private String expiresIn;


    /**
     * token生效时间
     */
    private LocalDateTime activeTime;

    /**
     * token失效时间
     */
    private LocalDateTime expireTime;

    /**
     * 逻辑删除：0=未删除，1=已删除
     */
    @TableLogic
    @TableField("is_deleted")
    private Boolean deleted;

    /**
     * 记录创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 记录修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 应用key
     */
    private String appKey;

    /**
     * 应用secret
     */
    private String appSecret;

}