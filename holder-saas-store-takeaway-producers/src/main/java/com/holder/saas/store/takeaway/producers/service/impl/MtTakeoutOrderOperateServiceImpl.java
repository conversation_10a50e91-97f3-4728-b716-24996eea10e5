package com.holder.saas.store.takeaway.producers.service.impl;

import com.holder.saas.store.takeaway.producers.config.MeiTuanConfig;
import com.holder.saas.store.takeaway.producers.entity.domain.MtAuthDO;
import com.holder.saas.store.takeaway.producers.service.MtAuthService;
import com.holder.saas.store.takeaway.producers.service.TakeoutOrderOperateService;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.takeaway.MtBusinessIdEnum;
import com.holderzone.saas.store.dto.takeaway.TakeoutOrderOperateDTO;
import com.meituan.sdk.DefaultMeituanClient;
import com.meituan.sdk.MeituanClient;
import com.meituan.sdk.MeituanResponse;
import com.meituan.sdk.internal.exceptions.MtSdkException;
import com.meituan.sdk.model.waimaiNg.order.preparationMealComplete.PreparationMealCompleteRequest;
import lombok.AllArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service("mtTakeoutOrderOperateServiceImpl")
@Slf4j
@RequiredArgsConstructor
public class MtTakeoutOrderOperateServiceImpl implements TakeoutOrderOperateService {

    private final MeiTuanConfig meiTuanConfig;

    private final MtAuthService authService;

    @Override
    public void orderPrepared(TakeoutOrderOperateDTO orderOperateDTO) {
        MtAuthDO mtAuthDO = authService.getAuth(orderOperateDTO.getStoreGuid(), MtBusinessIdEnum.TAKEOUT.getType());
        if(mtAuthDO == null){
            throw new BusinessException("获取美团授权信息失败");
        }
        MeituanClient meituanClient = DefaultMeituanClient.builder(Long.valueOf(meiTuanConfig.getDeveloperId()), meiTuanConfig.getSignKey()).build();
        PreparationMealCompleteRequest preparationMealCompleteRequest = new PreparationMealCompleteRequest();

        preparationMealCompleteRequest.setOrderId(orderOperateDTO.getOrderId());

        try {
            MeituanResponse<String> response = meituanClient.invokeApi(preparationMealCompleteRequest, mtAuthDO.getAccessToken());
            if(!response.isSuccess()){
                throw new BusinessException("美团外卖出餐失败！");
            }
        } catch (MtSdkException e) {
            log.error("请求美团外卖出餐异常",e);
            throw new BusinessException("美团外卖出餐失败！");
        }
    }
}
