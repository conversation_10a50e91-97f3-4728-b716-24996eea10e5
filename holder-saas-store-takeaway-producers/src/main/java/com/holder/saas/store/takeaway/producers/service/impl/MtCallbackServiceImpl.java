package com.holder.saas.store.takeaway.producers.service.impl;

import com.alibaba.fastjson.JSON;
import com.holder.saas.store.takeaway.producers.config.MeiTuanConfig;
import com.holder.saas.store.takeaway.producers.constant.TakeoutConstant;
import com.holder.saas.store.takeaway.producers.entity.domain.MtAuthDO;
import com.holder.saas.store.takeaway.producers.entity.domain.MtPrivacyDO;
import com.holder.saas.store.takeaway.producers.entity.dto.*;
import com.holder.saas.store.takeaway.producers.entity.enums.MtPathEnum;
import com.holder.saas.store.takeaway.producers.mapstruct.MtPrivacyMapstruct;
import com.holder.saas.store.takeaway.producers.service.*;
import com.holder.saas.store.takeaway.producers.service.converter.MtCallbackConverter;
import com.holder.saas.store.takeaway.producers.service.rpc.ErpFeignService;
import com.holder.saas.store.takeaway.producers.utils.sdk.meituan.CipCaterAuthorizationRequest;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.resource.common.dto.enterprise.MultiMemberDTO;
import com.holderzone.saas.store.dto.takeaway.*;
import com.sankuai.sjst.platform.developer.utils.WebUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.NameValuePair;
import org.apache.http.client.utils.URIBuilder;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutorService;

@Slf4j
@Service
@AllArgsConstructor
public class MtCallbackServiceImpl implements MtCallbackService {

    private final MtUnOrderParser mtUnOrderParser;

    private final UnOrderMqService unOrderMqService;

    private final ExecutorService executorService;

    private final MtPrivacyService mtPrivacyService;

    private final MtPrivacyMapstruct mtPrivacyMapstruct;

    private final DistributedService distributedService;

    private final OrderTradeDetailMqService orderTradeDetailMqService;

    private final MtAuthService mtAuthService;

    private final ErpFeignService erpFeignService;

    private final MeiTuanConfig meiTuanConfig;

    @Override
    public void orderCallback(MtCallbackDTO mtCallbackDTO, String path) {
        UnOrder unorder = parseToUnOrder(mtCallbackDTO, path);
        if (unorder != null) {
            unOrderMqService.sendUnOrder(unorder);
        }
    }

    /**
     * 订单隐私降级
     *
     * @param mtCallbackDTO
     */
    @Override
    public void orderPrivacyDegrade(MtCallbackDTO mtCallbackDTO) {
        executorService.execute(() -> {
            boolean isDone = Boolean.FALSE;
            int initDegradOffset = 0;
            while (!isDone) {
                String result = null;
                try {
                    MtPrivacyReqDTO mtPrivacyReqDTO = new MtPrivacyReqDTO();
                    mtPrivacyReqDTO.setCharset(TakeoutConstant.CHARSET_UTF_8);
                    mtPrivacyReqDTO.setTimestamp(System.currentTimeMillis() / 1000);
                    mtPrivacyReqDTO.setDeveloperId(meiTuanConfig.getDeveloperId());
                    mtPrivacyReqDTO.setDegradOffset(initDegradOffset);
                    mtPrivacyReqDTO.setDegradLimit(1000);
                    List<NameValuePair> paramsInUrl = mtPrivacyReqDTO.nameValuePairs(meiTuanConfig.getSignKey());
                    String url = (new URIBuilder())
                            .setParameters(paramsInUrl)
                            .setPath(TakeoutConstant.REAL_PHONE_NUMBER_URL)
                            .build().toString();
                    result = WebUtils.post(url, null);
                } catch (Exception e) {
                    if (log.isErrorEnabled()) {
                        log.error(e.getMessage());
                    }
                }

                MtCbPrivacyDTO mtCbPrivacyDTO = JacksonUtils.toObject(MtCbPrivacyDTO.class, result);
                String error = mtCbPrivacyDTO.getError();
                if (error != null) {
                    MtCbPrivacyDTO.MtCbErrorDetail mtCbErrorDetail = JacksonUtils.toObject(MtCbPrivacyDTO.MtCbErrorDetail.class, error);
                    if (log.isErrorEnabled()) {
                        log.error("批量拉取隐私号失败，错误代码：{}，错误原因：{}",
                                mtCbErrorDetail.getCode(), mtCbErrorDetail.getMessage());
                    }
                    break;
                }

                String data = mtCbPrivacyDTO.getData();
                List<MtCbPrivacyDTO.MtCbPrivacyDetail> mtCbPrivacyDetails = JacksonUtils.toObjectList(
                        MtCbPrivacyDTO.MtCbPrivacyDetail.class, data);
                List<MtPrivacyDO> mtPrivacyDOList = mtPrivacyMapstruct.fromMtCbPrivacyDetail(mtCbPrivacyDetails);
                if (mtPrivacyDOList.size() > 0 && mtPrivacyDOList.size() <= 1000) {
                    try {
                        mtPrivacyDOList.forEach(mtPrivacyDO -> mtPrivacyDO.setGuid(distributedService.nextMtGuid()));
                        mtPrivacyService.saveBatch(mtPrivacyDOList);
                    } catch (Exception e) {
                        if (log.isErrorEnabled()) {
                            log.error(e.getMessage());
                        }
                    }
                    initDegradOffset += 1000;
                }
                if (mtPrivacyDOList.size() < 1000) isDone = true;
            }
        });
    }

    @Override
    public void orderTradeDetailCallback(MtCallbackSettlementDTO mtCallbackSettlementDTO) {
        TakeoutOrderTradeDetailDTO takeoutOrderTradeDetailDTO = parseToOrderTradeDetail(mtCallbackSettlementDTO);
        orderTradeDetailMqService.sendOrderTradeDetail(takeoutOrderTradeDetailDTO);
    }

    private TakeoutOrderTradeDetailDTO parseToOrderTradeDetail(MtCallbackSettlementDTO mtCallbackSettlementDTO) {
        TakeoutOrderTradeDetailDTO mtTakeoutTradeDetail = JacksonUtils.toObject(TakeoutOrderTradeDetailDTO.class, mtCallbackSettlementDTO.getTradeDetail());
        if (Objects.isNull(mtTakeoutTradeDetail)) {
            log.error("订单结算信息为空");
            return null;
        }
        MtAuthDO auth = mtAuthService.getAuth(mtCallbackSettlementDTO.getEpoiId(), MtBusinessIdEnum.TAKEOUT.getType());
        if (Objects.isNull(auth)) {
            log.error("门店授权信息为空,回调信息:{}", JacksonUtils.writeValueAsString(mtCallbackSettlementDTO));
            return null;
        }
        mtTakeoutTradeDetail.setEnterpriseGuid(auth.getEnterpriseGuid());
        return mtTakeoutTradeDetail;
    }

    /**
     * 解析订单
     *
     * @param mtCallbackDTO
     * @param path
     * @return
     */
    private UnOrder parseToUnOrder(MtCallbackDTO mtCallbackDTO, String path) {
        UnOrder unOrder = null;
        switch (MtPathEnum.ofPath(path)) {
            case FIRST:
                unOrder = mtUnOrderParser.fromMtCbOrderCreated(mtCallbackDTO);
                if (log.isInfoEnabled()) {
                    log.info("(美团)订单回调，向ERP发送(美团)新订单，unOrder: {}", JacksonUtils.writeValueAsString(unOrder));
                }
                break;
            case CANCEL:
                unOrder = mtUnOrderParser.fromMtCbOrderCanceled(mtCallbackDTO);
                if (log.isInfoEnabled()) {
                    log.info("(美团)订单回调，向ERP发送(美团)取消订单，unOrder: {}", JacksonUtils.writeValueAsString(unOrder));
                }
                break;
            case REFUND:
                unOrder = mtUnOrderParser.fromMtCbOrderRefund(mtCallbackDTO);
                if (log.isInfoEnabled()) {
                    log.info("(美团)订单回调，向ERP发送(美团)退单/退款，unOrder: {}", JacksonUtils.writeValueAsString(unOrder));
                }
                break;
            case PART_REFUND:
                unOrder = mtUnOrderParser.fromMtCbOrderPartRefund(mtCallbackDTO);
                if (log.isInfoEnabled()) {
                    log.info("(美团)订单回调，向ERP发送(美团)部分退款，unOrder: {}", JacksonUtils.writeValueAsString(unOrder));
                }
                break;
            case CONFIRM:
                unOrder = mtUnOrderParser.fromMtCbOrderConfirmed(mtCallbackDTO);
                if (log.isInfoEnabled()) {
                    log.info("(美团)订单回调，向ERP发送(美团)接单，unOrder: {}", JacksonUtils.writeValueAsString(unOrder));
                }
                break;
            case FINISH:
                unOrder = mtUnOrderParser.fromMtCbOrderFinished(mtCallbackDTO);
                if (log.isInfoEnabled()) {
                    log.info("(美团)订单回调，向ERP发送(美团)订单完成，unOrder: {}", JacksonUtils.writeValueAsString(unOrder));
                }
                break;
            case SHIPPING:
                unOrder = mtUnOrderParser.fromMtCbShippingStatus(mtCallbackDTO);
                if (log.isInfoEnabled()) {
                    log.info("(美团)订单回调，向ERP发送(美团)订单配送，unOrder: {}", JacksonUtils.writeValueAsString(unOrder));
                }
                break;
            default:
                break;
        }

        return unOrder;
    }

    @Override
    public void authorizationCallback(MtCallbackAuthorizationDTO mtCallbackDTO) {
        if(StringUtils.isEmpty(mtCallbackDTO.getState())){
            throw new BusinessException("透传字段不能为空");
        }
        CipCaterAuthorizationRequest cipCaterAuthorizationRequest = new CipCaterAuthorizationRequest(mtCallbackDTO.getBusinessId(), mtCallbackDTO.getDeveloperId(), mtCallbackDTO.getCode(), meiTuanConfig.getSignKey());
        String response = cipCaterAuthorizationRequest.doPost();

        Integer developerId = meiTuanConfig.getDeveloperId();
        log.info(String.valueOf(developerId));
        if(response == null){
            throw new BusinessException("获取access_token失败");
        }
        MtOauthRspDTO mtOauthRsp = JSON.parseObject(response, MtOauthRspDTO.class);
        //请求返回失败
        if(!mtOauthRsp.isSuccess()){
            throw new BusinessException(mtOauthRsp.getMessage());
        }
        //绑定会员主体
        if(mtCallbackDTO.getBusinessId() == MtBusinessIdEnum.BRAND_MEMBER.getType()){
            MultiMemberDTO multiMember = erpFeignService.getMultiMemberByGuid(mtCallbackDTO.getState());
            if(multiMember == null){
                throw new BusinessException("会员主体不存在");
            }
            MtAuthDTO mtAuthDTO = MtCallbackConverter.fromMultiMemberAndRsp(multiMember,mtOauthRsp);
            mtAuthService.saveCallbackAuth(mtAuthDTO);
        }
    }

    @Override
    public void unAuthorizationCallback(MtCallbackUnbindAuthorizationDTO mtCallbackDTO) {
        //解析消息体
        MtUnOauthMessageDTO mtUnOauthMessage = JSON.parseObject(mtCallbackDTO.getMessage(), MtUnOauthMessageDTO.class);
        //判断消息体内容
        if(mtUnOauthMessage.getOpBizCode() == null || mtUnOauthMessage.getBusinessId() == null){
            throw new BusinessException("解绑回调参数有误");
        }
        mtAuthService.deleteAuthByMt(mtUnOauthMessage.getOpBizCode(), mtUnOauthMessage.getBusinessId());
    }
}
