package com.holder.saas.store.takeaway.producers.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.holder.saas.store.takeaway.producers.constant.TakeoutConstant;
import com.holder.saas.store.takeaway.producers.entity.domain.MtAuthDO;
import com.holder.saas.store.takeaway.producers.entity.dto.MtAuthDTO;
import com.holder.saas.store.takeaway.producers.mapper.MtAuthMapper;
import com.holder.saas.store.takeaway.producers.mapstruct.MtAuthMapstruct;
import com.holder.saas.store.takeaway.producers.service.DistributedService;
import com.holder.saas.store.takeaway.producers.service.ErpGuidCacheService;
import com.holder.saas.store.takeaway.producers.service.MtAuthService;
import com.holder.saas.store.takeaway.producers.utils.ThrowableExtUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.saas.store.dto.takeaway.MtAuthBindUrlDTO;
import com.holderzone.saas.store.dto.takeaway.MtBusinessIdEnum;
import com.holderzone.saas.store.dto.takeaway.MtCallbackDTO;
import com.holderzone.saas.store.dto.takeaway.TokenDTO;
import com.holderzone.saas.store.dto.takeaway.request.GroupBuyShopBindReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutItemBindReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutShopBindReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.GroupBuyShopBindRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.StoreAuthDTO;
import com.holderzone.saas.store.dto.takeaway.response.TakeoutItemBindRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.TakeoutShopBindRespDTO;
import com.holderzone.saas.store.enums.common.BooleanEnum;
import com.sankuai.sjst.platform.developer.utils.SignUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 外卖平台和erp门店身份认证信息接口实现
 * @time 2017年7月26日 下午3:35:17
 */
@Slf4j
@Service("takeawayInfoAuth")
public class MtAuthServiceImpl extends ServiceImpl<MtAuthMapper, MtAuthDO> implements MtAuthService {

    @Value("${mt.DEVELOPER_ID}")
    private int mtDeveloperId;

    @Value("${mt.SIGN_KEY}")
    private String mtSignKey;

    @Resource
    private MtAuthMapper mtAuthMapper;

    private final ErpGuidCacheService erpGuidCacheService;

    private final DistributedService distributedService;

    private final MtAuthMapstruct mtAuthMapstruct;

    @Autowired
    public MtAuthServiceImpl(ErpGuidCacheService erpGuidCacheService, DistributedService distributedService, MtAuthMapstruct mtAuthMapstruct) {
        this.erpGuidCacheService = erpGuidCacheService;
        this.distributedService = distributedService;
        this.mtAuthMapstruct = mtAuthMapstruct;
    }

    @Override
    public TakeoutShopBindRespDTO takeOutBindingUrl(TakeoutShopBindReqDTO takeoutShopBindReqDTO) {
        Assert.hasText(takeoutShopBindReqDTO.getStoreGuid(), "门店Guid不得为空");
        String url;
        Map<String, String> params = new HashMap<>();
        if (takeoutShopBindReqDTO.getBindingStatus() == TakeoutConstant.BINDING) {
            //绑定

            url = TakeoutConstant.MEI_TUAN_BINDING_URL + "?";
            params.put(TakeoutConstant.PARAM_DEVELOPER_ID, String.valueOf(mtDeveloperId));
            params.put("businessId", String.valueOf(MtBusinessIdEnum.TAKEOUT.getType()));
            params.put("ePoiId", takeoutShopBindReqDTO.getStoreGuid());
            params.put("timestamp", String.valueOf(System.currentTimeMillis()));
            params.put("waimaiDishMap", "1");
            params.put("sign", SignUtils.createSign(mtSignKey, params));
        } else {
            // 解绑

            url = TakeoutConstant.MEI_TUAN_UN_BINDING_URL + "?";
            TokenDTO tokenRespDTO = getToken(takeoutShopBindReqDTO);
            params.put("businessId", String.valueOf(MtBusinessIdEnum.TAKEOUT.getType()));
            params.put("appAuthToken", tokenRespDTO.getToken());
            params.put("timestamp", String.valueOf(System.currentTimeMillis()));
            params.put("sign", SignUtils.createSign(mtSignKey, params));
        }
        String requestParams = params.entrySet().stream()
                .map(entry -> entry.getKey() + "=" + entry.getValue())
                .collect(Collectors.joining("&"));

        url = url + requestParams;

        return new TakeoutShopBindRespDTO().setUrl(url);
    }

    @Qualifier
    @Override
    public GroupBuyShopBindRespDTO groupBuyBindingUrl(GroupBuyShopBindReqDTO groupBuyShopBindReqDTO) {
        String url;
        Map<String, String> params = new HashMap<>();
        if (groupBuyShopBindReqDTO.getBindingStatus() == TakeoutConstant.BINDING) {
            url = TakeoutConstant.MEI_TUAN_LOGIN_BINDING_URL + "?";
            params.put(TakeoutConstant.PARAM_DEVELOPER_ID, String.valueOf(mtDeveloperId));
            params.put("businessId", String.valueOf(MtBusinessIdEnum.TUAN_GOU.getType()));
            params.put("ePoiId", groupBuyShopBindReqDTO.getStoreGuid());
            log.info("ePoiId=" + groupBuyShopBindReqDTO.getStoreGuid());

            params.put("timestamp", String.valueOf(System.currentTimeMillis()));
            // fixme 此处应该无需该字段，可能造成美团方验签失败
            //params.put("waimaiDishMap", "1");
            params.put("sign", SignUtils.createSign(mtSignKey, params));
        } else {
            // 解绑
            url = TakeoutConstant.MEI_TUAN_UN_BINDING_URL + "?";
            TokenDTO tokenRespDTO = getToken(groupBuyShopBindReqDTO);
            params.put("businessId", String.valueOf(MtBusinessIdEnum.TUAN_GOU.getType()));
            params.put("appAuthToken", tokenRespDTO.getToken());
            params.put("timestamp", String.valueOf(System.currentTimeMillis()));
            params.put("sign", SignUtils.createSign(mtSignKey, params));
        }
        String requestParams = params.entrySet().stream()
                .map(entry -> entry.getKey() + "=" + entry.getValue())
                .collect(Collectors.joining("&"));

        url = url + requestParams;

        return new GroupBuyShopBindRespDTO().setUrl(url);
    }

    @Override
    public TakeoutItemBindRespDTO itemBindingUrl(TakeoutItemBindReqDTO takeoutItemBindReqDTO) {
        TokenDTO tokenDTO = getToken(takeoutItemBindReqDTO.getStoreGuid(), MtBusinessIdEnum.TAKEOUT.getType());

        Map<String, String> params = new HashMap<>();
        params.put("ePoiId", takeoutItemBindReqDTO.getStoreGuid());
        log.info("ePoiId=" + takeoutItemBindReqDTO.getStoreGuid());
        params.put("appAuthToken", tokenDTO.getToken());
        params.put("timestamp", String.valueOf(System.currentTimeMillis()));
        params.put("sign", SignUtils.createSign(mtSignKey, params));
        String requestParams = params.entrySet().stream()
                .map(entry -> entry.getKey() + "=" + entry.getValue())
                .collect(Collectors.joining("&"));

        String url = TakeoutConstant.MEI_TUAN_ITEM_BINDING_URL + "?" + requestParams;

        return new TakeoutItemBindRespDTO().setUrl(url);
    }

    @Override
    public void bind(MtCallbackDTO mtCallbackDTO) {
        String appAuthToken = mtCallbackDTO.getAppAuthToken();
        String ePoiId = mtCallbackDTO.getEPoiId();
        String businessId = mtCallbackDTO.getBusinessId();

        // 手动切换数据源
        String enterpriseGuid = erpGuidCacheService.getEnterpriseGuid(ePoiId);

        // 删除原有token（防止美团未推送）
        remove(wrapperByMtStoreGuidBizId(mtCallbackDTO.getPoiId(), Byte.valueOf(businessId)));

        // 插入或更新token
        MtAuthDO mtAuthInDb = getAuth(ePoiId, Integer.parseInt(businessId));
        MtAuthDO mtAuthNew = new MtAuthDO();
        mtAuthNew.setGuid(distributedService.nextMtGuid());
        mtAuthNew.setEPoiId(ePoiId);
        mtAuthNew.setEnterpriseGuid(enterpriseGuid);
        mtAuthNew.setMtStoreGuid(mtCallbackDTO.getPoiId());
        mtAuthNew.setMtStoreName(mtCallbackDTO.getPoiName());
        mtAuthNew.setAccessToken(appAuthToken);
        mtAuthNew.setBusinessId(Byte.valueOf(businessId));
        mtAuthNew.setActiveTime(DateTimeUtils.mills2LocalDateTime(mtCallbackDTO.getTimestamp()));
        mtAuthNew.setExpireTime(DateTimeUtils.mills2LocalDateTime(mtCallbackDTO.getTimestamp()).plusYears(100));
        if (null == mtAuthInDb) {
            save(mtAuthNew);
        } else {
            updateById(mtAuthNew);
        }
    }

    @Override
    public void unbind(MtCallbackDTO mtCallbackDTO) {
        String ePoiId = mtCallbackDTO.getEPoiId();
        String businessId = mtCallbackDTO.getBusinessId();

        // 删除token
        deleteAuth(ePoiId, Byte.valueOf(businessId));
    }

    @Override
    public MtAuthDO getAuth(String ePoiId, int businessId) {
        return getOne(wrapperByPoiIdBizId(ePoiId, businessId));
    }

    @Override
    public List<MtAuthDO> getAuths(List<String> ePoiIds, int businessId) {
        return list(wrapperByPoiIdsBizId(ePoiIds, businessId));
    }

    @Override
    public MtAuthDO getAuthByBizCode(String opBizCode, int businessId) {
        return getOne(wrapperByMtStoreGuidBizId(opBizCode, businessId));
    }

    @Override
    public void deleteAuth(String ePoiId, int businessId) {
        remove(wrapperByPoiIdBizId(ePoiId, businessId));
    }

    @Override
    public void deleteAuthByMt(String bizCode, int businessId) {
        remove(wrapperByMtStoreGuidBizId(bizCode, businessId));
    }

    @Override
    public void correctAuth(String storeGuid, int businessId, String errorCode) {
        // https://developer.meituan.com/openapi#3.3
        // 4 authority_error 权限验证失败，请检查外卖或者团购门店是否绑定
        // 5 app_auth_token_error 令牌错误
        if ("authority_error".equalsIgnoreCase(errorCode) || "app_auth_token_error".equalsIgnoreCase(errorCode)) {
            try {
                this.deleteAuth(storeGuid, businessId);
                log.info("storeGuid: {}，(美团)移除失效Token成功", storeGuid);
            } catch (Throwable throwable) {
                log.error("storeGuid: {}，(美团)移除失效Token失败，失败原因：{}",
                        storeGuid, ThrowableExtUtils.asStringIfAbsent(throwable));
            }
        }
    }

    @Override
    public List<StoreAuthDTO> listAuth(List<StoreAuthDTO> storeAuthorizationDTOList) {
        if (CollectionUtils.isEmpty(storeAuthorizationDTOList)) return Collections.emptyList();
        List<String> arrayOfStoreId = storeAuthorizationDTOList.stream()
                .map(StoreAuthDTO::getStoreGuid)
                .collect(Collectors.toList());
        Map<String, StoreAuthDTO> storeAuthMap = storeAuthorizationDTOList.stream()
                .collect(Collectors.toMap(StoreAuthDTO::getStoreGuid, storeAuth -> storeAuth));
        List<MtAuthDO> arrayOfMtAuthDO = list(new LambdaQueryWrapper<MtAuthDO>()
                .in(MtAuthDO::getEPoiId, arrayOfStoreId)
                .eq(MtAuthDO::getBusinessId, MtBusinessIdEnum.TAKEOUT.getType()));
        Map<String, MtAuthDO> mtAuthMap = arrayOfMtAuthDO.stream()
                .collect(Collectors.toMap(MtAuthDO::getEPoiId, mtAuthDO -> mtAuthDO));
        return arrayOfStoreId.stream()
                .map(storeId -> {
                    MtAuthDO mtAuthDO = mtAuthMap.get(storeId);
                    StoreAuthDTO storeAuthDTO = storeAuthMap.get(storeId);
                    if (mtAuthDO == null) {
                        return storeAuthDTO.setBindingStatus(0);
                    }
                    return storeAuthDTO.setBindingStatus(1)
                            .setShopId(mtAuthDO.getMtStoreGuid())
                            .setShopName(mtAuthDO.getMtStoreName());
                })
                .collect(Collectors.toList());
    }

    @Override
    public StoreAuthDTO getTakeoutAuth(StoreAuthDTO storeAuthDTO) {
        MtAuthDO mtAuthDO = getAuth(storeAuthDTO.getStoreGuid(), MtBusinessIdEnum.TAKEOUT.getType());
        if (mtAuthDO == null) {
            return storeAuthDTO.setBindingStatus(0);
        }
        return storeAuthDTO.setBindingStatus(1)
                .setShopId(mtAuthDO.getMtStoreGuid())
                .setShopName(mtAuthDO.getMtStoreName())
                .setDeliveryType(mtAuthDO.getDeliveryType());

    }

    @Override
    public Boolean updateDelivery(StoreAuthDTO storeAuthDTO) {
        MtAuthDO mtAuthDO = getAuth(storeAuthDTO.getStoreGuid(), MtBusinessIdEnum.TAKEOUT.getType());
        mtAuthDO.setDeliveryType(storeAuthDTO.getDeliveryType());
        return updateById(mtAuthDO);
    }

    @Override
    public StoreAuthDTO getTuanGouAuth(StoreAuthDTO storeAuthDTO) {
        MtAuthDO mtAuthDO = getAuth(storeAuthDTO.getStoreGuid(), MtBusinessIdEnum.TUAN_GOU.getType());
        if (mtAuthDO == null) {
            return storeAuthDTO.setBindingStatus(0);
        }
        return storeAuthDTO.setBindingStatus(1)
                .setShopId(mtAuthDO.getMtStoreGuid())
                .setShopName(mtAuthDO.getMtStoreName());
    }

    @Override
    public TokenDTO getToken(String ePoiId, int businessId) {
        MtAuthDO mtAuthDO = getAuth(ePoiId, businessId);
        if (null == mtAuthDO) {
            throw new BusinessException("根据门店id：" + ePoiId + "查询美团token为空");
        }
        return new TokenDTO().setToken(mtAuthDO.getAccessToken());
    }

    @Override
    public TokenDTO getToken(GroupBuyShopBindReqDTO groupBuyShopBindReqDTO) {
        return getToken(groupBuyShopBindReqDTO.getStoreGuid(), MtBusinessIdEnum.TUAN_GOU.getType());
    }

    @Override
    public TokenDTO getToken(TakeoutShopBindReqDTO takeoutShopBindReqDTO) {
        return getToken(takeoutShopBindReqDTO.getStoreGuid(), MtBusinessIdEnum.TAKEOUT.getType());
    }

    private Wrapper<MtAuthDO> wrapperByMtStoreGuidBizId(String mtStoreGuid, int businessId) {
        return new LambdaQueryWrapper<MtAuthDO>()
                .eq(MtAuthDO::getMtStoreGuid, mtStoreGuid)
                .eq(MtAuthDO::getBusinessId, businessId);
    }

    private Wrapper<MtAuthDO> wrapperByPoiIdBizId(String ePoiId, int businessId) {
        return new LambdaQueryWrapper<MtAuthDO>()
                .eq(MtAuthDO::getEPoiId, ePoiId)
                .eq(MtAuthDO::getBusinessId, businessId);
    }

    private Wrapper<MtAuthDO> wrapperByPoiIdsBizId(List<String> ePoiIds, int businessId) {
        return new LambdaQueryWrapper<MtAuthDO>()
                .in(MtAuthDO::getEPoiId, ePoiIds)
                .eq(MtAuthDO::getBusinessId, businessId);
    }

    private Wrapper<MtAuthDO> wrapperByPoiIdOfTakeout(String ePoiId) {
        return new LambdaQueryWrapper<MtAuthDO>()
                .eq(MtAuthDO::getEPoiId, ePoiId)
                .eq(MtAuthDO::getBusinessId, MtBusinessIdEnum.TAKEOUT.getType());
    }

    private Wrapper<MtAuthDO> wrapperByPoiIdOfCoupon(String ePoiId) {
        return new LambdaQueryWrapper<MtAuthDO>()
                .eq(MtAuthDO::getEPoiId, ePoiId)
                .eq(MtAuthDO::getBusinessId, MtBusinessIdEnum.TUAN_GOU.getType());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveCallbackAuth(MtAuthDTO mtAuthDTO) {
        MtAuthDO mtAuthDO = mtAuthMapstruct.fromMtAuth(mtAuthDTO);
        //先解绑所有在新增
        deleteAuthByMt(mtAuthDO.getMtStoreGuid(), mtAuthDO.getBusinessId());
        MtAuthDO auth = getAuth(mtAuthDO.getEPoiId(), mtAuthDO.getBusinessId());
        if(auth == null){
            mtAuthDO.setGuid(distributedService.nextMtGuid());
            save(mtAuthDO);
            return;
        }
        mtAuthDO.setGuid(auth.getGuid());
        mtAuthDO.setId(auth.getId());
        updateById(mtAuthDO);
    }

    @Override
    public String getMtAuthBindUrl(MtAuthBindUrlDTO authBindUrl) {
        if(!authBindUrl.isUnBind() && !authBindUrl.isBind()){
            throw new BusinessException("授权状态有误");
        }
        //如果是授权到店品牌会员卡业务
        if(authBindUrl.getBusinessId() == MtBusinessIdEnum.BRAND_MEMBER.getType()){
            return getBrandMemberUrl(authBindUrl);
        }
        throw new BusinessException("授权业务不支持");
    }

    @Override
    public List<MtAuthBindUrlDTO> getMtBindAuth(String data) {
        MtAuthBindUrlDTO mtAuthBindUrl = new MtAuthBindUrlDTO();
        mtAuthBindUrl.setOperSubjectGuid(data);
        mtAuthBindUrl.setBusinessId(MtBusinessIdEnum.BRAND_MEMBER.getType());
        mtAuthBindUrl.setBusinessName(MtBusinessIdEnum.BRAND_MEMBER.getDesc());
        mtAuthBindUrl.setStatus(BooleanEnum.FALSE.getCode());
        MtAuthDO auth = getAuth(data, MtBusinessIdEnum.BRAND_MEMBER.getType());
        if(auth != null){
            mtAuthBindUrl.setStatus(BooleanEnum.TRUE.getCode());
        }
        return Lists.newArrayList(mtAuthBindUrl);
    }

    private String getBrandMemberUrl(MtAuthBindUrlDTO authBindUrl) {
        if(StringUtils.isEmpty(authBindUrl.getOperSubjectGuid())){
            throw new BusinessException("获取美团到店授权，会员主体不能为空");
        }
        Map<String, String> map = Maps.newHashMap();
        map.put("state",authBindUrl.getOperSubjectGuid());
        Map<String, String> params = buildAuthUrlParamsAndSign(String.valueOf(authBindUrl.getBusinessId()), map);
        return buildAuthUrl(params,authBindUrl.isBind() ? TakeoutConstant.MEI_TUAN_AUTH_BINDING_URL : TakeoutConstant.MEI_TUAN_AUTH_UN_BINDING_URL);
    }

    private Map<String,String> buildAuthUrlParamsAndSign(String businessId,Map<String,String> extralMap){
        Map<String, String> paramMap = Maps.newHashMap();
        paramMap.put(TakeoutConstant.PARAM_DEVELOPER_ID, String.valueOf(mtDeveloperId));
        paramMap.put("businessId",businessId);
        paramMap.put("timestamp", String.valueOf(System.currentTimeMillis()));
        paramMap.put("charset","UTF-8");
        if(CollectionUtil.isNotEmpty(extralMap)){
            paramMap.putAll(extralMap);
        }
        String sign = SignUtils.createSign(mtSignKey, paramMap);
        paramMap.put("sign",sign);
        return paramMap;
    }

    private String buildAuthUrl(Map<String,String> paramsMap,String url){
        String requestParams = paramsMap.entrySet().stream()
                .map(entry -> entry.getKey() + "=" + entry.getValue())
                .collect(Collectors.joining("&"));

        return url + "?" + requestParams;
    }
}
