package com.holder.saas.store.takeaway.producers.service.impl;

import com.holder.saas.store.takeaway.producers.service.EleAuthService;
import com.holder.saas.store.takeaway.producers.service.TakeoutOrderOperateService;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.takeaway.TakeoutOrderOperateDTO;
import eleme.openapi.sdk.api.exception.ServiceException;
import eleme.openapi.sdk.api.service.OrderService;
import eleme.openapi.sdk.config.Config;
import eleme.openapi.sdk.oauth.response.Token;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service("eleMeTakeoutOrderOperateServiceImpl")
@Slf4j
@RequiredArgsConstructor
public class EleMeTakeoutOrderOperateServiceImpl implements TakeoutOrderOperateService {

    private final EleAuthService eleAuthService;

    private final Config config;

    @Override
    public void orderPrepared(TakeoutOrderOperateDTO orderOperateDTO) {
        Token token = eleAuthService.getToken(orderOperateDTO.getStoreGuid());
        if(token == null) {
            throw new BusinessException("饿了么获取token失败");
        }
        OrderService orderService = new OrderService(config, token);
        try {
            orderService.setOrderPrepared(orderOperateDTO.getOrderId());
        } catch (ServiceException e) {
            log.error("饿了么出餐失败！",e);
            throw new BusinessException(e.getMessage());
        }
    }
}
