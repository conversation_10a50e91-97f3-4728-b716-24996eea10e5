package com.holder.saas.store.takeaway.producers.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.holder.saas.store.takeaway.producers.entity.domain.MtAuthDO;
import com.holder.saas.store.takeaway.producers.service.MtAuthService;
import com.holder.saas.store.takeaway.producers.service.MtOrderService;
import com.holder.saas.store.takeaway.producers.utils.HttpsClientUtils;
import com.holder.saas.store.takeaway.producers.utils.SignUtil;
import com.holder.saas.store.takeaway.producers.utils.ThrowableExtUtils;
import com.holder.saas.store.takeaway.producers.utils.sdk.meituan.CipCaterResponse;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.takeaway.MtBusinessIdEnum;
import com.holderzone.saas.store.dto.takeaway.UnOrder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 美团订单服务
 */
@Service
@Slf4j
public class MtOrderServiceImpl implements MtOrderService {

    @Autowired
    private MtAuthService authService;

    @Value("${mt.DEVELOPER_ID}")
    private Long mtDeveloperId;

    @Value("${mt.SIGN_KEY}")
    private String mtSignKey;

    private static final String MT_GET_REAL_ADDRESS_URL = "https://api-open-cater.meituan.com/waimai/ng/order/getRealRecipientAddress";

    @Override
    public String getRealRecipientAddress(UnOrder unOrder) {
        String msgType = "查询收货人真实地址";
        MtAuthDO mtAuthDO = authService.getAuth(unOrder.getStoreGuid(), MtBusinessIdEnum.TAKEOUT.getType());
        if (mtAuthDO == null) {
            logTokenUnavailableThenThrow(unOrder, msgType);
        } else {
            try {
                String authToken = mtAuthDO.getAccessToken();
                Map<String, String> params = Maps.newHashMap();
                params.put("appAuthToken", authToken);
                params.put("charset", "UTF-8");
                params.put("timestamp", String.valueOf(System.currentTimeMillis() / 1000));
                params.put("developerId", String.valueOf(mtDeveloperId));
                params.put("biz", "{\"orderId\":" + unOrder.getOrderId() + ",\"queryReasonType\":1}");
                params.put("version", "2");
                params.put("businessId", "2");
                String sign = SignUtil.getSign(mtSignKey, params);
                params.put("sign", sign);
                log.info("查询收货人真实地址发送请求:{}", JacksonUtils.writeValueAsString(params));
                String result = HttpsClientUtils.doPost(MT_GET_REAL_ADDRESS_URL, params);
                log.info("查询收货人真实地址返回结果:{}", result);
                CipCaterResponse cipCaterResponse = JSONObject.parseObject(result, CipCaterResponse.class);
                if (cipCaterResponse.isOK()) {
                    String data = cipCaterResponse.getData();
                    if (StringUtils.isEmpty(data)) {
                        return null;
                    }
                    return JacksonUtils.toJSONObject(data).getString("recipient_address");
                } else {
                    logReplyFailedThenThrow(unOrder, cipCaterResponse, msgType);
                }
            } catch (Exception e) {
                logExceptionThenThrow(unOrder, e, msgType);
            }
        }
        return null;
    }

    private void logTokenUnavailableThenThrow(UnOrder unOrder, String msgType) {
        log.error("GET(美团){}，orderId: {}，处理失败: 根据storeGuid: {} 未查询到Token",
                msgType, unOrder.getOrderId(), unOrder.getStoreGuid());
        throw new BusinessException("业务失败：" + "门店未绑定");
    }

    private void logReplyFailedThenThrow(UnOrder unOrder, CipCaterResponse errorDetail, String msgType) {
        log.error("GET(美团){}，orderId: {}，处理失败，code: {}, message: {}",
                msgType, unOrder.getOrderId(), errorDetail.getCode(), errorDetail.getMsg());
        throw new BusinessException("业务失败：" + errorDetail.getMsg());
    }

    private void logExceptionThenThrow(UnOrder unOrder, Exception e, String msgType) {
        if (log.isErrorEnabled()) {
            log.error("GET(美团){}，orderId: {}，处理失败: {}", msgType, unOrder.getOrderId(),
                    ThrowableExtUtils.asStringIfAbsent(e));
        }
        throw new BusinessException("业务失败：" + ThrowableExtUtils.asStringIfAbsent(e));
    }

}
