package com.holder.saas.store.takeaway.producers.utils.sdk.jd;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.holder.saas.store.takeaway.producers.utils.sdk.jd.dto.CategoriesQueryResponse;
import com.holder.saas.store.takeaway.producers.utils.sdk.jd.dto.CommonRspDTO;
import com.holder.saas.store.takeaway.producers.utils.sdk.jd.dto.QueryCategoriesReqDTO;
import com.holder.saas.store.takeaway.producers.utils.sdk.jd.dto.ShopCategory;
import lombok.extern.slf4j.Slf4j;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @description 新查询商家店内分类信息接口
 */
@Slf4j
public class QueryCategoriesRequest extends AbstractJdRequest {


    public QueryCategoriesRequest() {
    }

    public QueryCategoriesRequest(String appKey, String appSecret, String token) {
       super.appKey = appKey;
       super.appSecret = appSecret;
       super.token = token;
    }

    public List<ShopCategory> execute(){
        try {
            QueryCategoriesReqDTO categoriesReq = QueryCategoriesReqDTO.builder()
                    .fields(Lists.newArrayList("ID", "PID", "SHOP_CATEGORY_NAME"))
                    .optType(0)
                    .build();
            String rsp = super.execute(JSON.toJSONString(categoriesReq), "/pms/queryCategoriesByOrgCode");
            CategoriesQueryResponse queryCategoriesRspDTO = JSON.parseObject(rsp,CategoriesQueryResponse.class);
            if(!queryCategoriesRspDTO.isSuccess()){
                return Collections.emptyList();
            }
            List<ShopCategory> categoryList = queryCategoriesRspDTO.getResult();
            if(CollUtil.isEmpty(categoryList)){
                return Collections.emptyList();
            }
            return categoryList;
        }catch (Exception e){
            log.error("查询商家店内分类信息失败",e);
        }
        return Collections.emptyList();
    }
}
