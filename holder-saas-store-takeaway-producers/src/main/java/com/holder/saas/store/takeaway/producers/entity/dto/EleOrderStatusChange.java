package com.holder.saas.store.takeaway.producers.entity.dto;

import eleme.openapi.sdk.api.enumeration.order.OOrderStatus;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 订单状态变更消息
 * <p>
 * https://nest-fe.faas.ele.me/openapi/documents/callback
 */
@Data
@NoArgsConstructor
public class EleOrderStatusChange implements Serializable {

    private static final long serialVersionUID = -7121324363617996584L;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 订单状态，参见OOrder结构体的OOrderStatus枚举定义
     */
    private OOrderStatus state;

    /**
     * 店铺id
     */
    private long shopId;

    /**
     * 状态变更的时间戳，单位秒
     */
    private long updateTime;

    /**
     * 驱动状态发生变更的操作者角色
     *
     * @see com.holder.saas.store.takeaway.producers.entity.enums.EleRoleEnum
     */
    private long role;
}
