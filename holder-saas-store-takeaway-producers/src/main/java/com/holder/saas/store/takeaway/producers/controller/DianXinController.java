package com.holder.saas.store.takeaway.producers.controller;

import com.holder.saas.store.takeaway.producers.service.DianXinService;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.takeaway.SendOrderCallReq;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/dx")
public class DianXinController {


    private final DianXinService dianXinService;

    public DianXinController(DianXinService dianXinService) {
        this.dianXinService = dianXinService;
    }


    @PostMapping(value = "/send_call")
    @ApiOperation(value = "推送电信呼叫", notes = "自营外卖平台订单接收")
    public void sendCall(@RequestBody SendOrderCallReq sendOrderCallReq) {
        log.info("推送电信呼叫入参：{}" + JacksonUtils.writeValueAsString(sendOrderCallReq));
        dianXinService.outbound(sendOrderCallReq);
    }
}
