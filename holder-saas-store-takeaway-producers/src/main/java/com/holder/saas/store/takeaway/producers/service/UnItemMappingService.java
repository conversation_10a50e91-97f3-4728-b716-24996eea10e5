package com.holder.saas.store.takeaway.producers.service;

import com.holderzone.saas.store.dto.takeaway.*;

import java.util.List;

public interface UnItemMappingService {

    List<UnMappedType> getType(String storeGuid);

    List<UnMappedItem> getItem(String storeGuid);

    /**
     * 批量获取商品
     *
     * @param unItemQueryReq 查询入参
     * @return 商品分类 - 商品列表
     */
    List<UnMappedItem> getItems(UnItemQueryReq unItemQueryReq);

    void bindMapping(UnItemBindUnbindReq unItemBindUnbindReq);

    void unbindMapping(UnItemBindUnbindReq unItemBindUnbindReq);

    void batchUnbindMapping(UnItemBatchUnbindReq unItemBatchUnbindReq);
}
