package com.holder.saas.store.takeaway.producers.utils.sdk.jd.dto;

import lombok.Data;

import java.util.Map;

@Data
public class OrderProductDTO {

    /**
     * 商品规格，多规格之间用英文分号;分隔
     */
    private String skuCostumeProperty;

    /**
     * 调整单记录id
     */
    private Long adjustId;

    /**
     * 到家商品编码
     */
    private Long skuId;

    /**
     * 商品的名称（150字符）
     */
    private String skuName;

    /**
     * 到家商品销售价
     */
    private Long skuJdPrice;

    /**
     * 采购价(仅京东天选有)
     */
    private Long skuPurchasePrice;

    /**
     * 商家商品编码（100字符）
     */
    private String skuIdIsv;

    /**
     * 下单数量
     */
    private Integer skuCount;

    /**
     * 到家商品门店价
     */
    private Long skuStorePrice;

    /**
     * 到家商品成本价
     */
    private Long skuCostPrice;

    /**
     * 餐盒费（商品的总餐盒费）
     */
    private Long canteenMoney;

    /**
     * 商品扩展信息(到家spu编码spuId,商家spu编码outSpuId)
     */
    private Map<String,String> productExtendInfoMap;
}
