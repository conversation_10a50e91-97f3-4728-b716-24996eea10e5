package com.holder.saas.store.takeaway.producers.entity.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 催单消息
 * <p>
 * https://nest-fe.faas.ele.me/openapi/documents/callback
 */
@Data
@NoArgsConstructor
public class EleOrderUrge implements Serializable {

    private static final long serialVersionUID = -4794176645007604800L;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 店铺id
     */
    private long shopId;

    /**
     * 催单id
     */
    private long remindId;

    /**
     * 发起催单的用户id
     */
    private long userId;

    /**
     * 用户发起催单的时间戳，单位秒
     */
    private long updateTime;
}
