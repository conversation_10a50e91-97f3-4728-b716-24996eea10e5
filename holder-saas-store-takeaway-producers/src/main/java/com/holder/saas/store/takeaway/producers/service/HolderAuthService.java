package com.holder.saas.store.takeaway.producers.service;


import brave.internal.Nullable;
import com.baomidou.mybatisplus.extension.service.IService;
import com.holder.saas.store.takeaway.producers.entity.domain.HolderAuthDO;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.takeaway.OwnCallbackResponse;
import com.holderzone.saas.store.dto.takeaway.TakeoutOrderDTO;
import com.holderzone.saas.store.dto.takeaway.request.*;
import com.holderzone.saas.store.dto.takeaway.response.OwnDistributionDTO;
import com.holderzone.saas.store.dto.takeaway.response.StoreAuthDTO;
import com.holderzone.saas.store.dto.takeaway.response.TakeoutOwnItemMappingRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.TakeoutOwnRespDTO;

import java.util.List;

public interface HolderAuthService extends IService<HolderAuthDO> {

    TakeoutOwnRespDTO doTakeOutBind(TakeoutShopOwnBindReqDTO takeoutShopOwnBindReqDTO);

    TakeoutOwnRespDTO doTakeOutUnBind(TakeoutShopOwnUnBindReqDTO takeoutShopOwnUnBindReqDTO);

    List<TakeoutOwnItemMappingRespDTO> getItem(String storeGuid);

    String itemBind(TakeoutShopOwnItemBindReqDTO takeoutShopOwnItemBindReqDTO, String code, String token);

    String itemUnBind(TakeoutShopOwnItemBindReqDTO takeoutShopOwnItemBindReqDTO, String code, String token);

    String getCode(String storeGuid);

    HolderAuthDO getHolder(String storeGuid);

    List<OwnDistributionDTO> getDistribution(BaseDTO baseDTO);

    OwnCallbackResponse goShipping(TakeoutOrderDTO takeoutOrderDTO);

    OwnCallbackResponse doneShipping(TakeoutOrderDTO takeoutOrderDTO);

    OwnCallbackResponse cancelShipping(TakeoutOrderDTO takeoutOrderDTO);

    @Nullable
    String getToken(HolderAuthDO holderAuthDO);

    void refreshToken(long beforeTime);

    String refreshToken(HolderAuthDO holderAuthDO);

    StoreAuthDTO getTakeoutAuth(StoreAuthDTO storeAuthDTO);

    HolderAuthDO getHolderAuth(String storeGuid);

    void deleteAuth(String storeGuid);

}
