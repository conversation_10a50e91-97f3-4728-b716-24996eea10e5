package com.holder.saas.store.takeaway.producers.entity.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023-08-25
 * @description 美团获取access_token返回
 */
@Data
public class MtOauthRspDTO {

    private Integer code;

    private String message;

    private Data data;

    @lombok.Data
    public static class Data{
        private String accessToken;

        private Long expireIn;

        private String refreshToken;

        private String scope;

        private String opBizCode;
    }

    public boolean isSuccess(){
        return this.code != null && this.code == 0;
    }
}
