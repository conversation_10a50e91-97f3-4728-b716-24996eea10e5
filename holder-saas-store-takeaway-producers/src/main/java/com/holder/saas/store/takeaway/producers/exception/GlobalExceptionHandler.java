package com.holder.saas.store.takeaway.producers.exception;

import com.holder.saas.store.takeaway.producers.utils.ThrowableExtUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.NotFoundException;
import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.FeignMsgUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.exception.GroupBuyException;
import com.netflix.hystrix.exception.HystrixBadRequestException;
import com.netflix.hystrix.exception.HystrixRuntimeException;
import feign.FeignException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderRocketListener
 * @date 2018/11/15 12:00
 * @description
 * @program holder-saas-store-takeaway
 */
@Slf4j
@RestControllerAdvice
@SuppressWarnings("Duplicates")
public class GlobalExceptionHandler {

    @ExceptionHandler(value = MethodArgumentNotValidException.class)
    public ResponseEntity<String> requestValueError(HttpServletRequest request, MethodArgumentNotValidException e) {
        String message = e.getBindingResult().getFieldErrors().stream()
                .map(fieldError -> String.format("%s:%s", fieldError.getField(), fieldError.getDefaultMessage()))
                .collect(Collectors.joining(","));
        return new ResponseEntity<>(message, HttpStatus.PRECONDITION_REQUIRED);
    }

    @ExceptionHandler(value = IllegalArgumentException.class)
    public ResponseEntity<String> illegalArgumentException(HttpServletRequest request, IllegalArgumentException e) {
        String message = e.getMessage();
        if (log.isErrorEnabled()) {
            log.error("外卖Producer服务参数错误：{}", message);
        }
        if (StringUtils.isEmpty(message)) {
            return new ResponseEntity<>("外卖Producer服务参数错误", HttpStatus.PRECONDITION_REQUIRED);
        }
        return new ResponseEntity<>(message, HttpStatus.PRECONDITION_REQUIRED);
    }

    @ExceptionHandler(value = NotFoundException.class)
    public ResponseEntity<String> exception(NotFoundException e) {
        String message = e.getMessage();
        if (log.isErrorEnabled()) {
            log.error("外卖Producer服务未找到：{}", message);
        }
        return new ResponseEntity<>(message, HttpStatus.NOT_FOUND);
    }

    @ExceptionHandler(value = BusinessException.class)
    public ResponseEntity<String> exception(BusinessException e) {
        String message = e.getMessage();
        if (log.isErrorEnabled()) {
            log.error("外卖Producer服务业务异常：{}", message);
        }
        return new ResponseEntity<>(message, HttpStatus.CONFLICT);
    }

    @ExceptionHandler(value = HystrixBadRequestException.class)
    public ResponseEntity<String> hystrixBadRequestException(HttpServletRequest request, HystrixBadRequestException e) {
        String message = e.getMessage();
        if (log.isErrorEnabled()) {
            log.error("服务方业务异常：{}", message);
        }
        if (StringUtils.isEmpty(message)) {
            return new ResponseEntity<>("服务方业务异常", HttpStatus.CONFLICT);
        }
        return new ResponseEntity<>(message, HttpStatus.CONFLICT);
    }

    /**
     * e.getFallbackException() -> Exception
     * e.getFallbackException().getCause() -> AssertionError
     * e.getFallbackException().getCause().getCause() -> ServerException
     * e.getFallbackException().getCause().getCause().getMessage() -> status 500 ...
     * <p>
     * e.getCause() -> FeignException
     * e.getCause().getMessage() -> status 500 ...
     *
     * @param request
     * @param e
     * @return
     */
    @ExceptionHandler(value = HystrixRuntimeException.class)
    public ResponseEntity<String> hystrixRuntimeException(HttpServletRequest request, HystrixRuntimeException e) {
        String message = e.getCause().getMessage();
        if (log.isErrorEnabled()) {
            log.error("服务方系统异常：{}", message);
        }
        message = FeignMsgUtils.parseFeignMsgDetailed(message);
        if (StringUtils.isEmpty(message)) {
            return new ResponseEntity<>("服务方系统异常", HttpStatus.INTERNAL_SERVER_ERROR);
        }
        return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @ExceptionHandler(value = FeignException.class)
    public ResponseEntity<String> feignException(HttpServletRequest request, FeignException e) {
        String message = e.getMessage();
        if (log.isErrorEnabled()) {
            log.error("服务方系统异常：{}", message);
        }
        message = FeignMsgUtils.parseFeignMsgDetailed(message);
        if (StringUtils.isEmpty(message)) {
            return new ResponseEntity<>("服务方系统异常", HttpStatus.INTERNAL_SERVER_ERROR);
        }
        return new ResponseEntity<>(message, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @ExceptionHandler(value = ServerException.class)
    public ResponseEntity<String> exception(ServerException e) {
        String message = e.getMessage();
        if (log.isErrorEnabled()) {
            log.error("外卖Producer本地系统异常：{}", ThrowableExtUtils.asStringIfAbsent(e));
        }
        return new ResponseEntity<>(message, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @ExceptionHandler(value = GroupBuyException.class)
    public void exception(GroupBuyException e) {
        if (log.isErrorEnabled()) {
            log.error("团购业务异常：{}", ThrowableExtUtils.asStringIfAbsent(e));
        }
        throw e;
    }

    @ExceptionHandler(value = Exception.class)
    public ResponseEntity<String> exception(Exception e) {
        String message = e.getMessage();
        if (log.isErrorEnabled()) {
            log.error("外卖Producer本地系统异常：{}", ThrowableExtUtils.asStringIfAbsent(e));
        }
        return new ResponseEntity<>(message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
}
