package com.holder.saas.store.takeaway.producers.entity.bo;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SinModul
 * @date 2018/09/30 15:40
 * @description
 * @program holder-saas-store-takeaway
 */
@Data
@NoArgsConstructor
public class SinModul implements Serializable {

    private Long time;

    private int developerId;

    private List<Pos> list;

    @Data
    @NoArgsConstructor
    public static class Pos implements Serializable {

        private String ePoiId;

        private String posId;
    }
}
