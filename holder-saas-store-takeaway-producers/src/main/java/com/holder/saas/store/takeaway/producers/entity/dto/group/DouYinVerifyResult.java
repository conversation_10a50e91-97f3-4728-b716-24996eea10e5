package com.holder.saas.store.takeaway.producers.entity.dto.group;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023-06-21
 * @description
 */
@Data
public class DouYinVerifyResult {

    /**
     * 验券结果码，0表示成功，非0表示失败
     */
    private Integer result;

    /**
     * 验券结果说明
     */
    private String msg;

    /**
     * 代表验券传入的code或encrypted_code
     */
    private String code;

    /**
     * 代表券码一次核销的标识（撤销时需要）
     */
    @JSONField(name = "verify_id")
    private String verifyId;

    /**
     * 代表一张订单的标识
     */
    @JSONField(name = "order_id")
    private String orderId;

    /**
     * 代表一张券码的标识（撤销时需要）
     */
    @JSONField(name = "certificate_id")
    private String certificateId;

    /**
     * 代表验券传入的certificate_no_content
     */
    @JSONField(name = "certificate_no")
    private String certificateNo;

    /**
     * 代表抖音团购券的12位原始券码（抖音加密券码核销时）
     */
    @JSONField(name = "origin_code")
    private String originCode;

    /**
     * 代表企业号商家总店id（查询验券历史时需要）
     */
    @JSONField(name = "account_id")
    private String accountId;
}
