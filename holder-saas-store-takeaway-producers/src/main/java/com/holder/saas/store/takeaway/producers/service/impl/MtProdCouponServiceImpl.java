package com.holder.saas.store.takeaway.producers.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.holder.saas.store.takeaway.producers.constant.TakeoutConstant;
import com.holder.saas.store.takeaway.producers.entity.domain.MtAuthDO;
import com.holder.saas.store.takeaway.producers.mapstruct.MtCouponMapstruct;
import com.holder.saas.store.takeaway.producers.service.MtAuthService;
import com.holder.saas.store.takeaway.producers.service.MtCouponService;
import com.holder.saas.store.takeaway.producers.utils.MtCouponUtils;
import com.holder.saas.store.takeaway.producers.utils.sdk.meituan.CipCaterCouponQueryTradeDetailRequest;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.IDUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.constant.Constant;
import com.holderzone.saas.store.dto.takeaway.MtBusinessIdEnum;
import com.holderzone.saas.store.dto.takeaway.request.CouponDelReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.MtCouponReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.*;
import com.holderzone.saas.store.enums.order.GrouponReceiptChannelEnum;
import com.meituan.sdk.DefaultMeituanClient;
import com.meituan.sdk.MeituanClient;
import com.meituan.sdk.MeituanResponse;
import com.meituan.sdk.model.tuangouNg.coupon.msSuperConsume.MsSuperConsumeRequest;
import com.meituan.sdk.model.tuangouNg.coupon.msSuperConsume.MsSuperConsumeResponse;
import com.meituan.sdk.model.tuangouNg.coupon.msSuperPrepare.MsSuperPrepareRequest;
import com.meituan.sdk.model.tuangouNg.coupon.msSuperPrepare.MsSuperPrepareResponse;
import com.meituan.sdk.model.tuangouNg.groupVoucher.couponQueryProfitByCodeMaidan.CouponQueryProfitByCodeMaidanRequest;
import com.meituan.sdk.model.tuangouNg.groupVoucher.couponQueryProfitByCodeMaidan.DataSub;
import com.meituan.sdk.model.tuangouNg.groupVoucher.groupvoucherOrderRefund.GroupvoucherOrderRefundRequest;
import com.meituan.sdk.model.tuangouNg.groupVoucher.groupvoucherOrderRefund.GroupvoucherOrderRefundResponse;
import com.sankuai.sjst.platform.developer.domain.RequestSysParams;
import com.sankuai.sjst.platform.developer.request.CipCaterCouponConsumptionCancelRequest;
import com.sankuai.sjst.platform.developer.request.CipCaterCouponQueryRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.net.URISyntaxException;
import java.util.Collections;
import java.util.List;
import java.util.Objects;


@Slf4j
@Service
@Qualifier("mtDevCouponServiceImpl")
public class MtProdCouponServiceImpl implements MtCouponService {

    private final MtAuthService authService;

    private final MtCouponMapstruct mtCouponMapstruct;

    @Value("${mt.SIGN_KEY}")
    private String mtSignKey;

    @Value("${mt.DEVELOPER_ID}")
    private String developerId;

    public static final String STORE_UN_BIND = "门店未绑定";


    public MtProdCouponServiceImpl(MtAuthService authService, MtCouponMapstruct mtCouponMapstruct) {
        log.info("注入开发环境MtDevCouponServiceImpl");
        this.authService = authService;
        this.mtCouponMapstruct = mtCouponMapstruct;
    }


    @Override
    public MtCouponDetailRespDTO queryById(MtCouponReqDTO mtCouponReqDTO) {
        log.info("进入queryById方法,developerId:{},mtSignKey:{}", developerId, mtSignKey);
        String authToken = mtCouponReqDTO.getAppAuthToken();
        if (StringUtils.isEmpty(authToken)) {
            MtAuthDO mtAuthDO = authService.getAuth(mtCouponReqDTO.getStoreGuid(), MtBusinessIdEnum.TUAN_GOU.getType());
            if (mtAuthDO == null) {
                log.info("已验券码查询失败,门店未绑定.storeGuid:{}", mtCouponReqDTO.getStoreGuid());
                throw new BusinessException(STORE_UN_BIND);
            }
            authToken = mtAuthDO.getAccessToken();
        }
        try {
            // 已验券码查询
            CipCaterCouponQueryRequest request = new CipCaterCouponQueryRequest();
            RequestSysParams requestSysParams = new RequestSysParams(mtSignKey, authToken, TakeoutConstant.CHARSET_UTF_8);
            request.setRequestSysParams(requestSysParams);
            request.setCouponCode(mtCouponReqDTO.getCouponCode());
            log.info("已验券码查询前参数打印：{}", request);
            String result = request.doRequest();
            log.info("已验券码查询返回：{}", result);
            MtCouponDetailRespDTO mtCouponDetailRespDTO = JSON.parseObject(result).getObject("data", MtCouponDetailRespDTO.class);
            log.info("已验券码查询成功,结果为:{}", mtCouponDetailRespDTO);
            return mtCouponDetailRespDTO;
        } catch (Exception e) {
            log.error("已验券码查询异常:", e);
            throw new BusinessException("已验券码查询异常");
        }
    }

    @Override
    public MtCouponDoCheckRespDTO checkTicket(MtCouponReqDTO mtCouponReqDTO) {
        return MtCouponUtils.checkTicket(mtCouponReqDTO, authService, mtSignKey, developerId);
    }


    @Override
    public MtCouponDoCheckRespDTO doCheck(MtCouponReqDTO mtCouponReqDTO) {
        log.info("进入doCheck方法,developerId:{},mtSignKey:{}", developerId, mtSignKey);
        MtCouponDoCheckRespDTO mtCouponDoCheckRespDTO;
        MtAuthDO mtAuthDO = authService.getAuth(mtCouponReqDTO.getStoreGuid(), MtBusinessIdEnum.TUAN_GOU.getType());
        if (mtAuthDO == null) {
            log.info("执行验券失败,门店未绑定.storeGuid:{}", mtCouponReqDTO.getStoreGuid());
            throw new BusinessException(Constant.COUPON_UN_BIND_STORE);
        }
        MeituanClient meituanClient = DefaultMeituanClient.builder(Long.valueOf(developerId), mtSignKey).build();
        MsSuperConsumeRequest msSuperConsumeRequest = new MsSuperConsumeRequest();
        msSuperConsumeRequest.setNum(mtCouponReqDTO.getCount());
        msSuperConsumeRequest.setCode(mtCouponReqDTO.getCouponCode());
        // 扩展用核销渠道，如需兼容一键买单券码，固定入参1004即可
        msSuperConsumeRequest.setExtendChannel(1004);
        msSuperConsumeRequest.setIdempotent(IDUtils.nextId());
        try {
            MeituanResponse<MsSuperConsumeResponse> response = meituanClient.invokeApi(msSuperConsumeRequest, mtAuthDO.getAccessToken());
            log.info("执行验券返回：{}", JacksonUtils.writeValueAsString(response));
            if (!response.isSuccess()) {
                throw new BusinessException("执行验券异常");
            }
            mtCouponDoCheckRespDTO = JacksonUtils.toObject(MtCouponDoCheckRespDTO.class, JacksonUtils.writeValueAsString(response.getData()));
            mtCouponDoCheckRespDTO.setOrderId(mtCouponReqDTO.getErpOrderId());
            log.info("执行验券成功,erp交易号:{}", mtCouponReqDTO.getErpOrderId());
            return mtCouponDoCheckRespDTO;
        } catch (Exception e) {
            log.error("执行验券异常:", e);
            throw new BusinessException("执行验券失败");
        }
    }

    @Override
    public MtCouponPreRespDTO preCheck(MtCouponReqDTO mtCouponReqDTO) {
        log.info("进入preCheck方法,developerId:{},mtSignKey:{}", developerId, mtSignKey);
        MtCouponPreRespDTO mtCouponPreRespDTO;
        MtAuthDO mtAuthDO = authService.getAuth(mtCouponReqDTO.getStoreGuid(), MtBusinessIdEnum.TUAN_GOU.getType());
        if (mtAuthDO == null) {
            log.info("预验券失败,门店未绑定.storeGuid:{}", mtCouponReqDTO.getStoreGuid());
            throw new BusinessException(Constant.COUPON_UN_BIND_STORE);
        }
        MeituanClient meituanClient = DefaultMeituanClient.builder(Long.valueOf(developerId), mtSignKey).build();
        MsSuperPrepareRequest msSuperPrepareRequest = new MsSuperPrepareRequest();
        msSuperPrepareRequest.setCode(mtCouponReqDTO.getCouponCode());
        msSuperPrepareRequest.setVersion(1);
        try {
            MeituanResponse<MsSuperPrepareResponse> response = meituanClient.invokeApi(msSuperPrepareRequest, mtAuthDO.getAccessToken());
            log.info("预验券返回：{}", JacksonUtils.writeValueAsString(response));
            if (!response.isSuccess()) {
                throw new BusinessException("预验券异常:" + response.getMsg());
            }
            mtCouponPreRespDTO = mtCouponMapstruct.msSuperPrepareResponsetoMtCouponPreRespDTO(response.getData());
            log.info("预验券成功,券码:{},最大可验券数量:{}", mtCouponPreRespDTO.getCouponCode(), mtCouponPreRespDTO.getCount());
            return mtCouponPreRespDTO;
        } catch (BusinessException e) {
            log.error("预验券业务异常:", e);
            throw new BusinessException(e.getMessage());
        } catch (Exception e) {
            log.error("预验券异常:", e);
            throw new BusinessException("预验券异常");
        }
    }

    @Override
    public MtDelCouponRespDTO cancelTicket(CouponDelReqDTO couponDelReqDTO) {
        log.info("进入cancelTicket方法,developerId:{},mtSignKey:{}", developerId, mtSignKey);
        String result;
        MtDelCouponRespDTO mtDelCouponRespDTO = null;
        MtDoDelRespDTO mtDoDelRespDTO;
        int resp;
        MtAuthDO mtAuthDO = authService.getAuth(couponDelReqDTO.getStoreGuid(), MtBusinessIdEnum.TUAN_GOU.getType());
        if (mtAuthDO == null) {
            log.info("撤销验券失败,门店未绑定.storeGuid:{}", couponDelReqDTO.getStoreGuid());
            throw new BusinessException(Constant.COUPON_UN_BIND_STORE);
        }
        if (Objects.equals(GrouponReceiptChannelEnum.MAITON.getCode(), couponDelReqDTO.getReceiptChannel())) {
            // 买单退款
            return cancelMaitonCoupon(couponDelReqDTO, mtAuthDO);
        }
        try {
            CipCaterCouponConsumptionCancelRequest request = new CipCaterCouponConsumptionCancelRequest();
            RequestSysParams requestSysParams = new RequestSysParams(mtSignKey, mtAuthDO.getAccessToken(), TakeoutConstant.CHARSET_UTF_8);
            request.setRequestSysParams(requestSysParams);
            request.setCouponCode(couponDelReqDTO.getCouponCode());
            request.setType(1);
            if (couponDelReqDTO.getErpId().length() >= 32) {
                // only he's
                request.seteId(1000000000000000000L);
            } else {
                request.seteId(Long.parseLong(couponDelReqDTO.getErpId()));
            }
            if (couponDelReqDTO.getErpName().length() > 32) {
                log.info("门店名大于32位，erpName：{}", couponDelReqDTO.getErpName().substring(0, 32));
                request.seteName(couponDelReqDTO.getErpName().substring(0, 32));
            } else {
                log.info("正常门店名，erpName：{}", couponDelReqDTO.getErpName());
                request.seteName(couponDelReqDTO.getErpName());
            }
            result = request.doRequest();
            log.info("撤销券返回：{}", result);
            mtDoDelRespDTO = JacksonUtils.toObject(MtDoDelRespDTO.class, result);
            if (mtDoDelRespDTO.getData() != null) {
                mtDelCouponRespDTO = mtDoDelRespDTO.getData();
            } else {
                mtDelCouponRespDTO = JacksonUtils.toObject(MtDelCouponRespDTO.class, result);
            }
            resp = mtDelCouponRespDTO.getResult();
        } catch (Exception e) {
            log.error("撤销券异常,e:", e);
            if (mtDelCouponRespDTO != null) {
                log.error("撤销券失败:" + mtDelCouponRespDTO.getMessage());
                throw new BusinessException("撤销券失败:" + mtDelCouponRespDTO.getMessage());
            } else {
                log.error("撤销券异常：" + e.getMessage());
                throw new BusinessException("撤销券异常");
            }
        }
        if (resp != 0) {
            log.error("撤销验券失败，券码：{}，错误原因：{}",
                    couponDelReqDTO.getCouponCode(),
                    mtDelCouponRespDTO.getMessage());
            throw new BusinessException("撤销验券失败:" + mtDelCouponRespDTO.getMessage());
        }
        return mtDelCouponRespDTO;
    }

    private MtDelCouponRespDTO cancelMaitonCoupon(CouponDelReqDTO couponDelReqDTO, MtAuthDO mtAuthDO) {
        MeituanClient meituanClient = DefaultMeituanClient.builder(Long.valueOf(developerId), mtSignKey).build();
        GroupvoucherOrderRefundRequest groupvoucherOrderRefundRequest = new GroupvoucherOrderRefundRequest();
        groupvoucherOrderRefundRequest.setRefundReason("商家退款");
        MtMaitonConsumeDTO maitonConsumeDTO = couponDelReqDTO.getMaitonConsumeDTO();
        if (Objects.isNull(maitonConsumeDTO)) {
            log.error("代金券买单订单信息缺失, couponDelReqDTO:{}", JacksonUtils.writeValueAsString(couponDelReqDTO));
            throw new BusinessException("代金券买单订单信息缺失");
        }
        groupvoucherOrderRefundRequest.setOrderId(Long.valueOf(maitonConsumeDTO.getMaitonOrderId()));
        try {
            MeituanResponse<GroupvoucherOrderRefundResponse> response = meituanClient.invokeApi(groupvoucherOrderRefundRequest, mtAuthDO.getAccessToken());
            log.info("代金券买单申请退款返回：{}", JacksonUtils.writeValueAsString(response));
            if (!response.isSuccess()) {
                throw new BusinessException("代金券买单退款异常");
            }
            return new MtDelCouponRespDTO();
        } catch (Exception e) {
            log.error("代金券买单退款异常:", e);
            throw new BusinessException("代金券买单退款异常");
        }
    }


    @Override
    public MtCouponTradeDetailRespDTO queryGroupTradeDetail(MtCouponReqDTO mtCouponReqDTO) {
        log.info("进入queryGroupTradeDetail方法,developerId:{},mtSignKey:{}", developerId, mtSignKey);
        MtAuthDO mtAuthDO = authService.getAuth(mtCouponReqDTO.getStoreGuid(), MtBusinessIdEnum.TUAN_GOU.getType());
        if (mtAuthDO == null) {
            log.info("查询团购订单结算明细失败,门店未绑定.storeGuid:{}", mtCouponReqDTO.getStoreGuid());
            throw new BusinessException(STORE_UN_BIND);
        }
        String authToken = mtAuthDO.getAccessToken();
        try {
            mtCouponReqDTO.setAppAuthToken(authToken);
            MtCouponDetailRespDTO mtCouponDetailRespDTO = queryById(mtCouponReqDTO);
            log.info("查询券详情：{}", JacksonUtils.writeValueAsString(mtCouponDetailRespDTO));
            // 查询团购订单结算明细
            CipCaterCouponQueryTradeDetailRequest request = new CipCaterCouponQueryTradeDetailRequest();
            RequestSysParams requestSysParams = new RequestSysParams(mtSignKey, authToken, TakeoutConstant.CHARSET_UTF_8);
            request.setRequestSysParams(requestSysParams);
            request.setCouponCode(mtCouponReqDTO.getCouponCode());
            log.info("查询团购订单结算明细前参数打印：{}", JacksonUtils.writeValueAsString(request));
            String result = request.doRequest();
            log.info("查询团购订单结算明细返回：{}", result);
            String data = JSON.parseObject(result).getString("data");
            List<MtCouponTradeDetailRespDTO> couponTradeDetailList = Lists.newArrayList();
            if (!StringUtils.isEmpty(data)) {
                log.info("查询团购订单结算明细成功,结果为:{}", data);
                couponTradeDetailList = JacksonUtils.toObjectList(MtCouponTradeDetailRespDTO.class, data);
            }
            if (CollectionUtils.isEmpty(couponTradeDetailList)) {
                // 重新查询团购订单结算明细
                couponTradeDetailList = reQueryGroupTradeDetail(request);
            }
            MtCouponTradeDetailRespDTO mtCouponTradeDetailRespDTO = buildMtCouponTradeDetailRespDTO(couponTradeDetailList, mtCouponDetailRespDTO, mtAuthDO);
            if (Objects.nonNull(mtCouponTradeDetailRespDTO)
                    && Objects.equals(GrouponReceiptChannelEnum.MAITON.getCode(), mtCouponReqDTO.getReceiptChannel())) {
                // 查询买单信息
                List<DataSub> maitonDataSubs = queryMaitonGroupTradeDetail(mtAuthDO, mtCouponTradeDetailRespDTO);
                log.info("查询买单信息：{}", JacksonUtils.writeValueAsString(maitonDataSubs));
            }
            return mtCouponTradeDetailRespDTO;
        } catch (Exception e) {
            log.error("查询团购订单结算明细异常:" + e.getMessage());
            throw new BusinessException("查询团购订单结算明细异常");
        }
    }


    /**
     * 查询买单订单结算扩展明细
     */
    public List<DataSub> queryMaitonGroupTradeDetail(MtAuthDO mtAuthDO,
                                                     MtCouponTradeDetailRespDTO mtCouponTradeDetailRespDTO) {
        MeituanClient meituanClient = DefaultMeituanClient.builder(Long.valueOf(developerId), mtSignKey).build();
        CouponQueryProfitByCodeMaidanRequest couponQueryProfitByCodeMaidanRequest = new CouponQueryProfitByCodeMaidanRequest();
        couponQueryProfitByCodeMaidanRequest.setOrderId(mtCouponTradeDetailRespDTO.getOrderId());
        couponQueryProfitByCodeMaidanRequest.setDealId(Long.valueOf(mtCouponTradeDetailRespDTO.getDealId()));
        try {
            MeituanResponse<List<DataSub>> response = meituanClient.invokeApi(couponQueryProfitByCodeMaidanRequest, mtAuthDO.getAccessToken());
            log.info("查询买单订单结算扩展明细返回：{}", JacksonUtils.writeValueAsString(response));
            if (!response.isSuccess()) {
                throw new BusinessException("查询买单订单结算扩展明细异常");
            }
            return response.getData();
        } catch (Exception e) {
            log.error("查询买单订单结算扩展明细异常:", e);
            throw new BusinessException("查询买单订单结算扩展明细异常");
        }
    }


    /**
     * 再次查询团购结算数据
     */
    private List<MtCouponTradeDetailRespDTO> reQueryGroupTradeDetail(CipCaterCouponQueryTradeDetailRequest request) throws IOException, URISyntaxException {
        // 查询团购订单结算明细
        log.info("重新查询团购订单结算明细前参数打印：{}", JacksonUtils.writeValueAsString(request));
        if (request.getCouponCode().length() >= 14) {
            request.setCouponCode(request.getCouponCode().substring(2));
        }
        String result = request.doRequest();
        log.info("重新查询团购订单结算明细返回：{}", result);
        String data = JSON.parseObject(result).getString("data");
        if (StringUtils.isEmpty(data)) {
            return Collections.emptyList();
        }
        List<MtCouponTradeDetailRespDTO> couponTradeDetailList = JacksonUtils.toObjectList(MtCouponTradeDetailRespDTO.class, data);
        log.info("重新查询团购订单结算明细成功,结果为:{}", JacksonUtils.writeValueAsString(couponTradeDetailList));
        return couponTradeDetailList;
    }


    private MtCouponTradeDetailRespDTO buildMtCouponTradeDetailRespDTO(List<MtCouponTradeDetailRespDTO> reCouponTradeDetailList,
                                                                       MtCouponDetailRespDTO mtCouponDetailRespDTO, MtAuthDO mtAuthDO) {
        if (CollectionUtils.isEmpty(reCouponTradeDetailList)) {
            return null;
        }
        MtCouponTradeDetailRespDTO mtCouponTradeDetailRespDTO = reCouponTradeDetailList.get(0);
        mtCouponTradeDetailRespDTO.setCouponDetail(mtCouponDetailRespDTO);
        mtCouponTradeDetailRespDTO.setStoreGuid(mtAuthDO.getEPoiId());
        mtCouponTradeDetailRespDTO.setMtStoreGuid(mtAuthDO.getMtStoreGuid());
        mtCouponTradeDetailRespDTO.setMtStoreName(mtAuthDO.getMtStoreName());
        return mtCouponTradeDetailRespDTO;
    }
}
