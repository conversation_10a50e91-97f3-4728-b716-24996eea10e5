package com.holder.saas.store.takeaway.producers.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holder.saas.store.takeaway.producers.config.MeiTuanConfig;
import com.holder.saas.store.takeaway.producers.config.RocketMqConfig;
import com.holder.saas.store.takeaway.producers.entity.domain.MtAuthDO;
import com.holder.saas.store.takeaway.producers.entity.dto.MtMemberParamsDTO;
import com.holder.saas.store.takeaway.producers.entity.dto.MtMemberRefreshTokenRespDTO;
import com.holder.saas.store.takeaway.producers.entity.dto.MtMemberRespDTO;
import com.holder.saas.store.takeaway.producers.entity.enums.AuthErrorCodeEnum;
import com.holder.saas.store.takeaway.producers.entity.enums.MtItemEnum;
import com.holder.saas.store.takeaway.producers.service.MtAuthService;
import com.holder.saas.store.takeaway.producers.service.MtBrandMemberService;
import com.holder.saas.store.takeaway.producers.service.converter.PlatformMemberConverter;
import com.holder.saas.store.takeaway.producers.service.rpc.CloudEnterpriseService;
import com.holder.saas.store.takeaway.producers.service.rpc.MemberFeignService;
import com.holder.saas.store.takeaway.producers.utils.sdk.meituan.CipCaterMemberRequest;
import com.holder.saas.store.takeaway.producers.utils.sdk.meituan.CipCaterRefreshTokenRequest;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.rocketmq.common.DefaultRocketMqProducer;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.member.PlatformMemberConsumeRecordDTO;
import com.holderzone.saas.store.dto.member.PlatformMemberDTO;
import com.holderzone.saas.store.dto.takeaway.*;
import com.holderzone.saas.store.enums.common.BooleanEnum;
import com.holderzone.saas.store.enums.takeaway.MtBusinessTypeEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.Message;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-08-29
 * @description 美团到餐品牌会员对接服务对接
 */
@Service
@Slf4j
@AllArgsConstructor
public class MtBrandMemberServiceImpl implements MtBrandMemberService {

    private final MtAuthService mtAuthService;

    private final MemberFeignService memberFeignService;

    private final CloudEnterpriseService cloudEnterpriseService;

    private final MeiTuanConfig meiTuanConfig;

    private final DefaultRocketMqProducer defaultRocketMqProducer;

    /**
     * 过期时间
     */
    private static final Integer EXPIRE_DAY = 1;

    /**
     * 更新令牌有效期为35天，更新令牌过期需要重新授权
     */
    private static final Integer UPDATE_EXPIRE_DAY = 35;

    @Override
    public boolean judgeAndMember(MtCallbackMemberDTO mtCallbackDTO) {
        //先查询是否绑定
        if (StringUtils.isEmpty(mtCallbackDTO.getOpBizCode())) {
            throw new BusinessException("业务实体id为空");
        }
        if (StringUtils.isEmpty(mtCallbackDTO.getBusinessId())) {
            throw new BusinessException("业务id错误");
        }
        MtAuthDO auth = mtAuthService.getAuthByBizCode(mtCallbackDTO.getOpBizCode(), mtCallbackDTO.getBusinessId());
        if (auth == null) {
            throw new BusinessException("业务未授权");
        }
        MtMemberParamsDTO mtMemberParams = JSON.parseObject(mtCallbackDTO.getParam(), MtMemberParamsDTO.class);
        PlatformMemberDTO platformMember = PlatformMemberConverter.fromMtMemberParamsAndAuth(mtMemberParams, auth);
        MtMemberMqReq mqReq = new MtMemberMqReq();
        mqReq.setBusinessType(MtBusinessTypeEnum.NEW_MEMBER.getCode());
        mqReq.setBody(JSON.toJSONString(platformMember));
        Message message = new Message(
                RocketMqConfig.MT_CARD_MEMBER_TOPIC,
                RocketMqConfig.MT_CARD_MEMBER_TAG,
                JacksonUtils.toJsonByte(mqReq)
        );
        defaultRocketMqProducer.sendMessage(message);
        return true;
    }

    @Override
    public void saveConsumeRecord(MtCallbackConsumeDTO mtCallbackDTO) {
        checkParam(mtCallbackDTO);
        MtAuthDO auth = mtAuthService.getAuthByBizCode(mtCallbackDTO.getOpBizCode(), mtCallbackDTO.getBusinessId());
        if (ObjectUtils.isEmpty(auth)) {
            throw new BusinessException("业务未授权");
        }

        PlatformMemberConsumeRecordDTO recordDTO = JacksonUtils.toObject(PlatformMemberConsumeRecordDTO.class, mtCallbackDTO.getMessage());
        PlatformMemberConsumeRecordDTO.EventDetail eventDetail = recordDTO.getEventDetail();
        eventDetail.setEnterpriseGuid(auth.getEnterpriseGuid());
        eventDetail.setOperSubjectGuid(auth.getEPoiId());
        eventDetail.setPhone(getPhone(mtCallbackDTO, eventDetail, auth));
        MtMemberMqReq mqReq = new MtMemberMqReq();
        mqReq.setBusinessType(MtBusinessTypeEnum.INTEGRAL_CALLBACK.getCode());
        mqReq.setBody(JSON.toJSONString(recordDTO));
        Message message = new Message(
                RocketMqConfig.MT_CARD_MEMBER_TOPIC,
                RocketMqConfig.MT_CARD_MEMBER_TAG,
                JacksonUtils.toJsonByte(mqReq)
        );
        defaultRocketMqProducer.sendMessage(message);
    }

    private String getPhone(MtCallbackConsumeDTO mtCallbackDTO, PlatformMemberConsumeRecordDTO.EventDetail eventDetail, MtAuthDO auth) {
        CipCaterMemberRequest memberRequest = new CipCaterMemberRequest(mtCallbackDTO.getBusinessId(),
                mtCallbackDTO.getDeveloperId(), meiTuanConfig.getSignKey(), eventDetail.getUserOpenId(), auth.getAccessToken());
        String response = memberRequest.doPost();
        log.info("[getPhone]response={}", response);
        if (StringUtils.isEmpty(response)) {
            throw new BusinessException("查询用户会员卡信息失败");
        }
        MtMemberRespDTO memberRespDTO = JacksonUtils.toObject(MtMemberRespDTO.class, response);
        log.info("[排查日志]memberRespDTO={}", JacksonUtils.writeValueAsString(memberRespDTO));
        // 请求返回失败
        if (!memberRespDTO.checkSuccess()) {
            log.error("[查询会员]失败,ErrMsgResp={}", JacksonUtils.writeValueAsString(memberRespDTO));
            throw new BusinessException(memberRespDTO.getMsg());
        }
        if (!memberRespDTO.checkDataSuccess()) {
            log.error("[查询会员]返回异常,ErrMsgResp={}", JacksonUtils.writeValueAsString(memberRespDTO));
            throw new BusinessException(memberRespDTO.getData().getErrMsg());
        }
        MtMemberRespDTO.FormItem phoneItem = memberRespDTO.getData().getResult().getFormItemList().stream()
                .filter(f -> MtItemEnum.PHONE.name().equals(f.getItemEnum())).findFirst().orElse(null);
        if (ObjectUtils.isEmpty(phoneItem)) {
            throw new BusinessException("手机号查询异常");
        }
        return phoneItem.getValue();
    }

    private static void checkParam(MtCallbackConsumeDTO mtCallbackDTO) {
        if (StringUtils.isEmpty(mtCallbackDTO.getBusinessId())) {
            throw new BusinessException("业务id错误");
        }
        if (StringUtils.isEmpty(mtCallbackDTO.getOpBizCode())) {
            throw new BusinessException("业务实体id为空");
        }
        if (StringUtils.isEmpty(mtCallbackDTO.getMessage())) {
            throw new BusinessException("消息体为空");
        }
    }

    @Override
    public void memberRefreshToken() {
        List<MtAuthDO> list = mtAuthService.list(new LambdaQueryWrapper<MtAuthDO>()
                .eq(MtAuthDO::getBusinessId, MtBusinessIdEnum.BRAND_MEMBER.getType())
                .eq(MtAuthDO::getDeleted, BooleanEnum.FALSE.getCode())
                .isNotNull(MtAuthDO::getAccessToken)
                .isNotNull(MtAuthDO::getRefreshToken)
                .le(MtAuthDO::getExpireTime, LocalDateTime.now().plusDays(EXPIRE_DAY))
        );
        if (CollectionUtils.isEmpty(list)) {
            log.info("今日无需更新token");
            return;
        }
        List<MtAuthDO> updateList = new ArrayList<>();
        list.forEach(auth -> {
            RefreshTokenMemberDTO memberDTO = new RefreshTokenMemberDTO();
            memberDTO.setBusinessId(String.valueOf(MtBusinessIdEnum.BRAND_MEMBER.getType()));
            memberDTO.setRefreshToken(auth.getRefreshToken());
            MtMemberRefreshTokenRespDTO.MemberRefreshTokenResp memberRefreshTokenResp = requestRefreshToken(memberDTO);
            if (ObjectUtils.isEmpty(memberRefreshTokenResp)) {
                log.warn("[更新授权令牌失败，请重新授权]auth={},Resp={}", JacksonUtils.writeValueAsString(auth),
                        JacksonUtils.writeValueAsString(memberRefreshTokenResp));
                return;
            }
            auth.setRefreshToken(memberRefreshTokenResp.getRefreshToken());
            auth.setAccessToken(memberRefreshTokenResp.getAccessToken());
            LocalDateTime now = LocalDateTime.now();
            auth.setActiveTime(now);
            auth.setGmtModified(now);
            auth.setExpireTime(now.plusSeconds(memberRefreshTokenResp.getExpireIn()));
            auth.setRefreshTokenExpire(LocalDateTime.now().plusDays(UPDATE_EXPIRE_DAY));
            updateList.add(auth);
        });
        if (!CollectionUtils.isEmpty(updateList)) {
            mtAuthService.updateBatchById(updateList);
        }
    }

    public MtMemberRefreshTokenRespDTO.MemberRefreshTokenResp requestRefreshToken(RefreshTokenMemberDTO refreshTokenMemberDTO) {
        CipCaterRefreshTokenRequest refreshTokenRequest = new CipCaterRefreshTokenRequest(
                refreshTokenMemberDTO.getBusinessId(),
                String.valueOf(meiTuanConfig.getDeveloperId()),
                meiTuanConfig.getSignKey(),
                refreshTokenMemberDTO.getRefreshToken());
        String response = refreshTokenRequest.doPost();
        log.info("[requestRefreshToken]response={}", response);
        if (StringUtils.isEmpty(response)) {
            log.error("[更新授权令牌]请求失败");
            return null;
        }

        MtMemberRefreshTokenRespDTO refreshTokenRespDTO = JSON.parseObject(response, MtMemberRefreshTokenRespDTO.class);
        if (ObjectUtils.isEmpty(refreshTokenRespDTO) || AuthErrorCodeEnum.allError().contains(refreshTokenRespDTO.getCode())) {
            log.error("[更新授权令牌]失败,ErrMsgResp={}", JacksonUtils.writeValueAsString(refreshTokenRespDTO));
            return null;
        }
        return refreshTokenRespDTO.getData();
    }
}
