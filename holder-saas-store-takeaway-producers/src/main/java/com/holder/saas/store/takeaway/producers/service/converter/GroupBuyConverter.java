package com.holder.saas.store.takeaway.producers.service.converter;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alipay.api.domain.*;
import com.alipay.api.response.AlipayMarketingCertificateCertificationPrepareuseResponse;
import com.alipay.api.response.AntMerchantExpandShopQueryResponse;
import com.google.common.collect.Lists;
import com.holder.saas.store.takeaway.producers.entity.domain.GroupStoreBindDO;
import com.holder.saas.store.takeaway.producers.entity.domain.MtAuthDO;
import com.holder.saas.store.takeaway.producers.entity.dto.group.AbcCouponRspDTO;
import com.holder.saas.store.takeaway.producers.entity.dto.group.DouYinCertificate;
import com.holder.saas.store.takeaway.producers.entity.dto.group.DouYinStoreBindRspDTO;
import com.holder.saas.store.takeaway.producers.entity.dto.group.DouYinVerifyResult;
import com.holder.saas.store.takeaway.producers.utils.TimeUtil;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.constant.Constant;
import com.holderzone.saas.store.dto.business.group.StoreBindDTO;
import com.holderzone.saas.store.dto.order.response.groupon.GroupVerifyDTO;
import com.holderzone.saas.store.dto.takeaway.request.CouPonPreReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.CouPonReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.CouponDelReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.MtCouponReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtCouponDoCheckRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtCouponPreRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtMaitonConsumeDTO;
import com.holderzone.saas.store.dto.takeaway.response.StoreAuthDTO;
import com.holderzone.saas.store.enums.GroupBuyTypeEnum;
import com.holderzone.saas.store.enums.order.GrouponReceiptChannelEnum;
import com.holderzone.saas.store.enums.trade.CouponTypeEnum;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @create 2023-06-19
 * @description
 */
public class GroupBuyConverter {

    public static GroupStoreBindDO fromNameAndStoreBind(DouYinStoreBindRspDTO poiRsp, StoreBindDTO storeBindDTO, Integer type) {
        GroupStoreBindDO groupStoreBindDO = new GroupStoreBindDO();
        groupStoreBindDO.setStoreGuid(storeBindDTO.getStoreGuid());
        groupStoreBindDO.setPoiId(storeBindDTO.getPoiId());
        groupStoreBindDO.setPoiName(poiRsp.getPoiName());
        groupStoreBindDO.setTaskId(poiRsp.getTaskId());
        groupStoreBindDO.setType(type);
        return groupStoreBindDO;
    }

    public static List<StoreAuthDTO> toStoreAuthList(List<GroupStoreBindDO> bindList, MtAuthDO auth) {
        List<StoreAuthDTO> list = Lists.newArrayList();
        if (CollectionUtil.isNotEmpty(bindList)) {
            bindList.forEach(e -> list.add(toStoreAuth(e)));
        }
        //如果是美团绑定
        if (auth != null) {
            list.add(mtToStoreAuth(auth));
        }
        return list;
    }

    private static StoreAuthDTO mtToStoreAuth(MtAuthDO auth) {
        StoreAuthDTO storeAuthDTO = new StoreAuthDTO();
        storeAuthDTO.setTakeoutType(1);
        storeAuthDTO.setPlatformName(GroupBuyTypeEnum.MEI_TUAN.getDesc());
        storeAuthDTO.setShopName(auth.getMtStoreName());
        storeAuthDTO.setBindingStatus(1);
        storeAuthDTO.setStoreGuid(auth.getMtStoreGuid());
        return storeAuthDTO;
    }

    private static StoreAuthDTO toStoreAuth(GroupStoreBindDO groupStoreBindDO) {
        StoreAuthDTO storeAuthDTO = new StoreAuthDTO();
        storeAuthDTO.setTakeoutType(groupStoreBindDO.getType());
        storeAuthDTO.setPlatformName(GroupBuyTypeEnum.getDesc(groupStoreBindDO.getType()));
        storeAuthDTO.setShopName(groupStoreBindDO.getPoiName());
        storeAuthDTO.setBindingStatus(1);
        storeAuthDTO.setStoreGuid(groupStoreBindDO.getStoreGuid());
        return storeAuthDTO;
    }

    public static List<MtCouponPreRespDTO> toDouYinCouponPreRespList(List<DouYinCertificate> certificateList) {
        List<MtCouponPreRespDTO> list = Lists.newArrayList();
        certificateList.forEach(c -> list.add(toDouYinCouponPreResp(c)));
        return list;
    }

    private static MtCouponPreRespDTO toDouYinCouponPreResp(DouYinCertificate douYinCertificate) {
        MtCouponPreRespDTO mtCouponPreRespDTO = new MtCouponPreRespDTO();
        //如果是抖音次卡需重新设置对应购买金额和抵扣金额
        if (douYinCertificate.getSku().getGrouponType() == 3) {
            if (douYinCertificate.getTimeCard() == null || CollUtil.isEmpty(douYinCertificate.getTimeCard().getSerialAmountList())) {
                throw new BusinessException("抖音次卡团购券次数已用完");
            }
            if (Objects.equals(douYinCertificate.getTimeCard().getTimesCount(), douYinCertificate.getTimeCard().getTimesUsed())) {
                throw new BusinessException("抖音次卡团购券次数已用完");
            }
            List<DouYinCertificate.SerialAmount> serialAmountList = douYinCertificate.getTimeCard().getSerialAmountList();
            //根据次数设置每次验券金额和购买金额
            DouYinCertificate.SerialAmount serialAmount = serialAmountList.get(douYinCertificate.getTimeCard().getTimesUsed());
            mtCouponPreRespDTO.setCouponBuyPrice((double) serialAmount.getAmount().getCouponPayAmount() / 100);
            mtCouponPreRespDTO.setDealValue((double) serialAmount.getAmount().getListMarketAmount() / 100);
        } else {
            mtCouponPreRespDTO.setCouponBuyPrice((double) douYinCertificate.getAmount().getCouponPayAmount() / 100);
            mtCouponPreRespDTO.setDealValue((double) douYinCertificate.getSku().getMarketPrice() / 100);
        }
        mtCouponPreRespDTO.setCouponEndTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(douYinCertificate.getExpireTime() * 1000L));
        mtCouponPreRespDTO.setCouponCode(douYinCertificate.getEncryptedCode());
        mtCouponPreRespDTO.setDealTitle(douYinCertificate.getSku().getTitle());
        mtCouponPreRespDTO.setSkuId(douYinCertificate.getSku().getSkuId());
        mtCouponPreRespDTO.setCouponType(douYinCertificate.getSku().getGrouponType());
        mtCouponPreRespDTO.setCount(1);
        return mtCouponPreRespDTO;
    }

    public static List<GroupVerifyDTO> toDouYinCouponVerifyRespList(List<DouYinVerifyResult> verifyResultList) {
        List<GroupVerifyDTO> list = Lists.newArrayList();
        verifyResultList.forEach(v -> list.add(toDouYinCouponVerifyResp(v)));
        return list;
    }

    private static GroupVerifyDTO toDouYinCouponVerifyResp(DouYinVerifyResult douYinVerifyResult) {
        GroupVerifyDTO groupVerifyRsp = new GroupVerifyDTO();
        groupVerifyRsp.setVerifyId(douYinVerifyResult.getVerifyId());
        groupVerifyRsp.setCertificateId(douYinVerifyResult.getCertificateId());
        groupVerifyRsp.setGroupBuyType(GroupBuyTypeEnum.DOU_YIN.getCode());
        //设置为原始code
        groupVerifyRsp.setCode(douYinVerifyResult.getOriginCode());
        //设置初始code
        groupVerifyRsp.setVerifyCode(douYinVerifyResult.getCode());
        return groupVerifyRsp;
    }

    public static MtCouponReqDTO mtPreReqFromPreReq(CouPonPreReqDTO couPonPreReqDTO) {
        MtCouponReqDTO mtCouponReqDTO = new MtCouponReqDTO();
        mtCouponReqDTO.setStoreGuid(couPonPreReqDTO.getStoreGuid());
        mtCouponReqDTO.setCouponCode(couPonPreReqDTO.getCouponCode());
        return mtCouponReqDTO;
    }

    public static MtCouponReqDTO mtCouPonReqFromCouPonReq(CouPonReqDTO couPonReq) {
        MtCouponReqDTO mtCouponReqDTO = new MtCouponReqDTO();
        mtCouponReqDTO.setStoreGuid(couPonReq.getErpId());
        mtCouponReqDTO.setCouponCode(couPonReq.getCouponCode());
        mtCouponReqDTO.setCount(couPonReq.getCount());
        mtCouponReqDTO.setErpId(couPonReq.getErpId());
        mtCouponReqDTO.setErpName(couPonReq.getErpName());
        mtCouponReqDTO.setErpOrderId(couPonReq.getErpOrderId());
        if (CollectionUtils.isNotEmpty(couPonReq.getCouponCodeList())) {
            mtCouponReqDTO.setCouponCode(couPonReq.getCouponCodeList().get(0));
            mtCouponReqDTO.setCount(couPonReq.getCouponCodeList().size());
        }
        return mtCouponReqDTO;
    }

    public static List<MtCouponPreRespDTO> toMtCouponPreRespList(CouPonPreReqDTO couPonPreReqDTO, MtCouponPreRespDTO mtCouponPreRespDTO) {
        if (mtCouponPreRespDTO == null) {
            return Collections.emptyList();
        }
        // 美团买单
        if (Objects.equals(GrouponReceiptChannelEnum.MAITON.getCode(), mtCouponPreRespDTO.getReceiptChannel())) {
            return toMtMaitonCouponPreRespList(couPonPreReqDTO, mtCouponPreRespDTO);
        }
        List<MtCouponPreRespDTO> list = Lists.newArrayList();
        for (int i = 0; i < mtCouponPreRespDTO.getCount(); i++) {
            MtCouponPreRespDTO mtCouponPreResp = new MtCouponPreRespDTO();
            mtCouponPreResp.setCount(1);
            mtCouponPreResp.setCouponCode(mtCouponPreRespDTO.getCouponCode());
            mtCouponPreResp.setCouponBuyPrice(mtCouponPreRespDTO.getCouponBuyPrice());
            mtCouponPreResp.setCouponEndTime(mtCouponPreRespDTO.getCouponEndTime());
            mtCouponPreResp.setDealId(mtCouponPreRespDTO.getDealId());
            mtCouponPreResp.setDealTitle(Boolean.TRUE.equals(mtCouponPreRespDTO.getIsVoucher())
                    ? mtCouponPreRespDTO.getDealTitle() : mtCouponPreRespDTO.getRawTitle());
            mtCouponPreResp.setDealValue(mtCouponPreRespDTO.getDealValue());
            mtCouponPreResp.setDealPrice(mtCouponPreRespDTO.getDealPrice());
            //判断券类型
            mtCouponPreResp.setCouponType(Boolean.TRUE.equals(mtCouponPreRespDTO.getIsVoucher())
                    ? CouponTypeEnum.VOUCHER.getCode() : CouponTypeEnum.ITEM.getCode());
            list.add(mtCouponPreResp);
        }
        return list;
    }


    public static List<MtCouponPreRespDTO> toMtMaitonCouponPreRespList(CouPonPreReqDTO couPonPreReqDTO, MtCouponPreRespDTO mtCouponPreRespDTO) {
        MtCouponPreRespDTO mtCouponPreResp = new MtCouponPreRespDTO();
        mtCouponPreResp.setCount(1);
        // 使用验券的券码，美团方没有在外层返回券码
        mtCouponPreResp.setCouponCode(couPonPreReqDTO.getCouponCode());

        MtMaitonConsumeDTO maitonConsumeDTO = mtCouponPreRespDTO.getMaitonConsumeDTO();
        mtCouponPreResp.setCouponBuyPrice(Double.parseDouble(maitonConsumeDTO.getTotalBuyPrice()));
        mtCouponPreResp.setCouponEndTime(mtCouponPreRespDTO.getCouponEndTime());
        mtCouponPreResp.setDealId(-1);
        mtCouponPreResp.setDealTitle("美团买单");
        mtCouponPreResp.setDealValue(Double.parseDouble(maitonConsumeDTO.getTotalOriginalPrice()));
        mtCouponPreResp.setDealPrice(Double.parseDouble(maitonConsumeDTO.getTotalVoucherBuyPrice()));
        //判断券类型
        mtCouponPreResp.setCouponType(Boolean.TRUE.equals(mtCouponPreRespDTO.getIsVoucher())
                ? CouponTypeEnum.VOUCHER.getCode() : CouponTypeEnum.ITEM.getCode());

        mtCouponPreResp.setReceiptChannel(mtCouponPreRespDTO.getReceiptChannel());
        mtCouponPreResp.setMaitonConsumeDTO(mtCouponPreRespDTO.getMaitonConsumeDTO());

        return Lists.newArrayList(mtCouponPreResp);
    }


    public static List<GroupVerifyDTO> toMtCouponVerifyRespList(CouPonReqDTO couPonReq, MtCouponDoCheckRespDTO mtCouponDoCheckResp) {
        if (mtCouponDoCheckResp == null) {
            return Collections.emptyList();
        }
        // 美团买单
        if (Objects.equals(GrouponReceiptChannelEnum.MAITON.getCode(), mtCouponDoCheckResp.getReceiptChannel())) {
            return toMtMaitonCouponVerifyRespList(couPonReq, mtCouponDoCheckResp);
        }
        if (CollectionUtils.isEmpty(mtCouponDoCheckResp.getCouponCodes())) {
            return Collections.emptyList();
        }
        List<GroupVerifyDTO> list = Lists.newArrayList();
        mtCouponDoCheckResp.getCouponCodes().forEach(e -> {
            GroupVerifyDTO groupVerifyDTO = new GroupVerifyDTO();
            groupVerifyDTO.setCode(e);
            groupVerifyDTO.setGroupBuyType(GroupBuyTypeEnum.MEI_TUAN.getCode());
            list.add(groupVerifyDTO);
        });
        return list;
    }

    public static List<GroupVerifyDTO> toMtMaitonCouponVerifyRespList(CouPonReqDTO couPonReq, MtCouponDoCheckRespDTO mtCouponDoCheckResp) {
        GroupVerifyDTO groupVerifyDTO = new GroupVerifyDTO();
        groupVerifyDTO.setCode(couPonReq.getCouponCodeList().get(0));
        groupVerifyDTO.setGroupBuyType(GroupBuyTypeEnum.MEI_TUAN.getCode());
        groupVerifyDTO.setReceiptChannel(mtCouponDoCheckResp.getReceiptChannel());
        groupVerifyDTO.setMaitonConsumeDTO(mtCouponDoCheckResp.getMaitonConsumeDTO());
        return Lists.newArrayList(groupVerifyDTO);
    }

    public static CouponDelReqDTO mtRevokeFromRevokeReq(GroupVerifyDTO revokeReq) {
        CouponDelReqDTO couponDelReqDTO = new CouponDelReqDTO();
        couponDelReqDTO.setCouponCode(revokeReq.getCode());
        couponDelReqDTO.setErpId(revokeReq.getErpId());
        couponDelReqDTO.setErpName(revokeReq.getErpName());
        couponDelReqDTO.setStoreGuid(revokeReq.getStoreGuid());
        couponDelReqDTO.setReceiptChannel(revokeReq.getReceiptChannel());
        couponDelReqDTO.setMaitonConsumeDTO(revokeReq.getMaitonConsumeDTO());
        return couponDelReqDTO;
    }

    public static GroupStoreBindDO fromAliPayStoreBind(AntMerchantExpandShopQueryResponse response, StoreBindDTO storeBind) {
        GroupStoreBindDO bindDO = new GroupStoreBindDO();
        bindDO.setStoreGuid(storeBind.getStoreGuid());
        bindDO.setPoiId(storeBind.getPoiId());
        bindDO.setPoiName(response.getShopName());
        bindDO.setType(GroupBuyTypeEnum.ALIPAY.getCode());
        return bindDO;
    }

    public static List<MtCouponPreRespDTO> toAliPayCouponPreRespList(AlipayMarketingCertificateCertificationPrepareuseResponse prepareUseResponse) {
        List<MtCouponPreRespDTO> couponPreRespDTOList = new ArrayList<>();
        List<CertificatePrepareInfo> prepareInfoList = prepareUseResponse.getCertificatePrepareInfoList();
        prepareInfoList.forEach(prepareInfo -> {
            MtCouponPreRespDTO respDTO = new MtCouponPreRespDTO();
            respDTO.setCount(Constant.NUMBER_ONE);
            respDTO.setIsVoucher(false);
            respDTO.setDealBeginTime(TimeUtil.getDateStr(prepareInfo.getValidBeginTime()));
            respDTO.setCouponEndTime(TimeUtil.getDateStr(prepareInfo.getValidEndTime()));

            // 凭证实例的金额信息，在异常场景下可能为空，需要判断是否有值才可进行使用
            CertificateInstanceAmountInfo amountInfo = prepareInfo.getAmountInfo();
            if (!ObjectUtils.isEmpty(amountInfo)) {
                respDTO.setDealValue(Double.parseDouble(amountInfo.getOriginalPrice()));
                respDTO.setCouponBuyPrice(Double.parseDouble(amountInfo.getSalePrice()));
            }

            respDTO.setCouponCode(prepareInfo.getEncryptedCode());
            CertificateSkuInfo skuInfo = prepareInfo.getSkuInfo();
            if (!ObjectUtils.isEmpty(skuInfo)) {
                respDTO.setDealTitle(skuInfo.getTitle());
                respDTO.setItemId(skuInfo.getItemId());
                respDTO.setSkuId(skuInfo.getSkuId());
                respDTO.setCouponType(Integer.valueOf(skuInfo.getItemType()));
                if (respDTO.getCouponType() == 1) {
                    respDTO.setIsVoucher(true);
                }
            }
            respDTO.setUserId(prepareUseResponse.getUserId());
            respDTO.setOrderId(prepareUseResponse.getOrderId());
            couponPreRespDTOList.add(respDTO);
        });
        return couponPreRespDTOList;
    }

    public static List<GroupVerifyDTO> toAliPayCouponVerifyRespList(List<CertificateUseResult> useResultList, String userId) {
        /*List<CertificateQueryInfo> certificateInfoList = queryResponse.getCertificateInfoList();
        Map<String, String> certificateCodeMap = certificateInfoList.stream()
                .collect(Collectors.toMap(CertificateQueryInfo::getCertificateId, CertificateQueryInfo::getCode, (v1, v2) -> v1));*/
        List<GroupVerifyDTO> verifyDTOList = new ArrayList<>();
        useResultList.forEach(useResult -> {
            GroupVerifyDTO verifyDTO = new GroupVerifyDTO();
            verifyDTO.setGroupBuyType(GroupBuyTypeEnum.ALIPAY.getCode());
//            String code = certificateCodeMap.get(useResult.getCertificateId());
            verifyDTO.setCode(useResult.getCertificateId());
            verifyDTO.setVerifyId(useResult.getUseOrderNo());
            verifyDTO.setCertificateId(useResult.getCertificateId());
            verifyDTO.setUserId(userId);

            verifyDTOList.add(verifyDTO);
        });
        return verifyDTOList;
    }

    public static List<MtCouponPreRespDTO> toAbcCouponPreRespList(AbcCouponRspDTO couponRsp, String couponCode) {
        List<MtCouponPreRespDTO> rspCouponList = Lists.newArrayList();
        MtCouponPreRespDTO mtCouponPreResp = new MtCouponPreRespDTO();
        mtCouponPreResp.setCouponBuyPrice(couponRsp.getPayAmount());
        mtCouponPreResp.setDealValue(Double.parseDouble(couponRsp.getValue()));
        mtCouponPreResp.setCouponEndTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(couponRsp.getExpiredTime()));
        mtCouponPreResp.setCouponCode(couponCode);
        mtCouponPreResp.setDealTitle(couponRsp.getCouponName());
        mtCouponPreResp.setSkuId(String.valueOf(couponRsp.getCouponId()));
        mtCouponPreResp.setCouponType("reduce".equalsIgnoreCase(couponRsp.getMerchantCouponType()) ? 2 : 1);
        mtCouponPreResp.setIsVoucher("reduce".equalsIgnoreCase(couponRsp.getMerchantCouponType()));
        mtCouponPreResp.setCount(1);
        rspCouponList.add(mtCouponPreResp);
        return rspCouponList;
    }
}
