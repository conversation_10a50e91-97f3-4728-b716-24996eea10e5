package com.holder.saas.store.takeaway.producers.utils;

import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutTcdOrderReqDTO;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.ValidatorFactory;
import java.util.Set;

public class ManualValidUtils {

    public static void validate(TakeoutTcdOrderReqDTO req, Class<?>... var2) {
        ValidatorFactory vf = Validation.buildDefaultValidatorFactory();
        javax.validation.Validator validator = vf.getValidator();
        Set<ConstraintViolation<TakeoutTcdOrderReqDTO>> set = validator.validate(req, var2);
        StringBuilder msg = new StringBuilder();
        for (ConstraintViolation<TakeoutTcdOrderReqDTO> constraintViolation : set) {
            msg.append(constraintViolation.getMessage()).append(";");
        }
        if (!set.isEmpty()) {
            //抛出参数校验异常
            String message = msg.toString();
            throw new ParameterException(message.endsWith(";") ? message.substring(0, message.length() - 1) : message);
        }
    }
}
