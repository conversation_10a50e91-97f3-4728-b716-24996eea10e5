package com.holder.saas.store.takeaway.producers.service.impl;

import com.holder.saas.store.takeaway.producers.service.EleAuthService;
import com.holder.saas.store.takeaway.producers.service.EleOrderService;
import com.holder.saas.store.takeaway.producers.utils.ThrowableExtUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import eleme.openapi.sdk.api.entity.order.*;
import eleme.openapi.sdk.api.exception.ServiceException;
import eleme.openapi.sdk.api.exception.UnauthorizedException;
import eleme.openapi.sdk.api.service.OrderService;
import eleme.openapi.sdk.config.Config;
import eleme.openapi.sdk.oauth.response.Token;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * 饿了吗订单解析(各种订单状态的处理)
 */
@Slf4j
@Service
public class EleOrderServiceImpl implements EleOrderService {

    @Autowired
    private EleAuthService eleAuthService;

    @Autowired
    private Config config;

    @Override
    public OOrder getOrder(String storeGuid, String orderId) {
        String requestType = "订单查询";
        logRequestProcessing(requestType, orderId);

        Token token = eleAuthService.getToken(storeGuid);
        if (token != null) {
            OrderService orderService = new OrderService(config, token);
            try {
                OOrder order = orderService.getOrder(orderId);
                logRequestDataSucceed(requestType, orderId, order);
                return order;
            } catch (ServiceException e) {
                logExceptionThenThrow(requestType, orderId, e);
            }
        } else {
            logAuthFailureThenThrow(requestType, storeGuid);
        }
        return null;
    }


    private void logRequestProcessing(String msg, String orderId) {
        log.info("Request(饿了么){}，orderId: {}，处理中", msg, orderId);
    }

    private void logRequestDataSucceed(String msg, String orderId, Object obj) {
        log.info("Request(饿了么){}，orderId: {}，查询成功：{}",
                msg, orderId, JacksonUtils.writeValueAsString(obj));
    }

    private void logAuthFailureThenThrow(String msg, String storeGuid) {
        log.error("Request(饿了么){}，处理失败，通过storeGuid[{}]未查询到token", msg, storeGuid);
        throw new BusinessException("操作失败，您的饿了么外卖已与门店解绑，请重新登录商户后台绑定");
    }

    private void logExceptionThenThrow(String msg, String orderId, ServiceException e) {
        if (log.isErrorEnabled()) {
            log.error("Request(饿了么){}，orderId: {}，处理失败：{}",
                    msg, orderId, ThrowableExtUtils.asStringIfAbsent(e));
        }
        if (e instanceof UnauthorizedException) {
            throw new BusinessException("操作失败，您的饿了么外卖已与门店解绑，请重新登录商户后台绑定");
        }
        throw new BusinessException(ThrowableExtUtils.asStringIfAbsent(e));
    }

}
