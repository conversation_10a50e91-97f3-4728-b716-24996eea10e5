package com.holder.saas.store.takeaway.producers.entity;

import eleme.openapi.sdk.api.enumeration.order.InvoiceType;

public enum EleInvoiceTypeMapping {

    /**
     * 个人
     */
    PERSONAL(InvoiceType.personal.name(), "个人", 0),

    /**
     * 企业
     */
    COMPANY(InvoiceType.company.name(), "企业", 1),

    /**
     * 未知
     */
    UNKNOWN("unknown", "未知", -1);

    private String type;

    private String desc;

    private int mapValue;

    EleInvoiceTypeMapping(String type, String desc, int mapValue) {
        this.type = type;
        this.desc = desc;
        this.mapValue = mapValue;
    }

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public int getMapValue() {
        return mapValue;
    }

    public static EleInvoiceTypeMapping ofType(String type) {
        for (EleInvoiceTypeMapping value : values()) {
            if (value.type.equalsIgnoreCase(type)) {
                return value;
            }
        }
        return UNKNOWN;
    }
}
