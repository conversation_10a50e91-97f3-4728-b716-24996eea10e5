package com.holder.saas.store.takeaway.producers.utils;

import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

public class TimeUtil {

    public static final String DEFAULT_FORMAT = "yyyy-MM-dd HH:mm:ss";


    public static long getTimestampOfLocalDateTime(LocalDateTime localDateTime) {
        ZoneId zone = ZoneId.systemDefault();
        Instant instant = localDateTime.atZone(zone).toInstant();
        return instant.toEpochMilli();
    }

    /**
     * int类型时间戳
     *
     * @return
     */
    public static Integer getIntTimestamp(LocalDateTime localDateTime) {
        return Math.toIntExact(localDateTime.atZone(ZoneId.systemDefault()).toEpochSecond());
    }

    /**
     * int类型时间戳
     *
     * @return
     */
    public static Integer getIntTimestamp() {
        return Math.toIntExact(LocalDateTime.now().atZone(ZoneId.systemDefault()).toEpochSecond());
    }

    /**
     * long类型时间戳
     *
     * @return
     */
    public static Long getLongTimestamp() {
        return LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    /**
     * Date类型 转 字符串
     */
    public static String getDateStr(Date date) {
        SimpleDateFormat dateFormat = new SimpleDateFormat(DEFAULT_FORMAT);
        return dateFormat.format(date);
    }
}
