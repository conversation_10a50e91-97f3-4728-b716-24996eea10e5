package com.holder.saas.store.takeaway.producers.entity.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

@Data
public class MtSpecialFoodPageDTO {

    /**
     * 商品总数
     */
    private Integer total;

    /**
     * 拼好饭商品列表
     */
    private List<WmOpenProductSpu> wmOpenProductSpus;

    @Data
    public static
    class WmOpenProductSpu{

        /**
         * 美团SPUID
         */
        @JSONField(name = "mt_spu_id")
        private Long mtSpuId;

        /**
         * 美团商品名
         */
        private String name;

        /**
         * 三方菜品id
         */
        @JSONField(name = "app_food_code")
        private String appFoodCode;

        /**
         * 商品图片
         */
        private String picture;

        /**
         * 菜品分类名
         */
        @JSONField(name = "tag_name")
        private String tagName;

        /**
         * 美团菜品分类ID
         */
        @JSONField(name = "mt_tag_id")
        private Long mtTagId;

        /**
         * 商品描述
         */
        private String description;

        /**
         * 商品标签
         */
        private String tag;

        /**
         * 规格信息
         */
        private List<Sku> skus;

        /**
         * 商品属性
         */
        private String propertys;

        /**
         * 套餐详情
         */
        private String setmeals;

        /**
         * 商品类型。0是单品，2是套餐，10是自选套餐
         */
        @JSONField(name = "spu_type")
        private Long spuType;

        /**
         * 当前分类下的排序序号
         */
        private Long sequence;

        /**
         * 商品创建时间戳
         */
        private Long ctime;

        /**
         * 商品修改时间戳
         */
        private Long utime;

        @JSONField(name = "sell_status")
        private Long sellStatus;

        @JSONField(name = "max_num")
        private Long maxNum;

    }

    @Data
    public static
    class Sku{

        /**
         * 美团SKUID
         */
        @JSONField(name = "mt_sku_id")
        private Long mtSkuId;

        /**
         * 三方商品规格ID
         */
        @JSONField(name = "sku_id")
        private String skuId;

        /**
         * 规格名称
         */
        private String spec;

        /**
         * sku价格
         */
        private Double price;

        /**
         * sku库存数量，不能为负数或小数，传'*'表示库存无限
         */
        private String stock;

        @JSONField(name = "max_stock")
        private Long maxStock;
    }
}
