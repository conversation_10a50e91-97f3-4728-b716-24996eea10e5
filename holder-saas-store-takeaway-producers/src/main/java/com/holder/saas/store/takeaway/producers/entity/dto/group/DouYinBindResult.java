package com.holder.saas.store.takeaway.producers.entity.dto.group;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.annotation.JSONField;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.exception.GroupBuyException;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-06-19
 * @description
 */
@Data
public class DouYinBindResult {

    @JSONField(name = "ext_id")
    private String extId;

    /**
     * 1=等待执行 2=匹配进行中 5=匹配完成
     */
    @JSONField(name = "match_status")
    private Integer matchStatus;

    /**
     * 任务结果
     * 1=匹配成功
     * 2=匹配失败
     * 4=无效数据，请检查传参
     * 5=重复提交
     * 6=抖音poiid已被其他三方id匹配上
     * 7=三方id已匹配上其他抖音poiid
     * 8=超过每日匹配数量限制
     * 9=经纬度填写错误
     */
    @JSONField(name = "match_result")
    private Integer matchResult;

    @JSONField(name = "match_message")
    private String matchMessage;

    public static DouYinBindResult parseJson(String rsp) {
        DouYinRspDTO<DouYinBindQueryRspDTO> douYinBindQueryRsp = JSONObject.parseObject(rsp,new TypeReference<DouYinRspDTO<DouYinBindQueryRspDTO>>(){}.getType());
        if (douYinBindQueryRsp == null || douYinBindQueryRsp.getData() == null){
            throw new GroupBuyException("查询门店绑定任务为空");
        }
        if(douYinBindQueryRsp.getData().getErrorCode() != 0){
            throw new GroupBuyException(douYinBindQueryRsp.getData().getDescription());
        }
        List<DouYinBindResult> results = douYinBindQueryRsp.getData().getResults();
        if(CollectionUtil.isEmpty(results)){
            throw new GroupBuyException("查询门店绑定任务为空");
        }
        return results.get(0);
    }

    public boolean isProcess(){
        return matchStatus == 1 || matchStatus == 2;
    }

    public boolean isSuccess() {
        return matchStatus == 5 && matchResult == 1;
    }
}
