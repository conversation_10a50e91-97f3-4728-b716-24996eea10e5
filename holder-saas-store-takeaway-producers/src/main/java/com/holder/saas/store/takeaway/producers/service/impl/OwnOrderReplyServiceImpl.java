package com.holder.saas.store.takeaway.producers.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holder.saas.store.takeaway.producers.constant.TakeoutConstant;
import com.holder.saas.store.takeaway.producers.entity.domain.HolderAuthDO;
import com.holder.saas.store.takeaway.producers.entity.domain.MtAuthDO;
import com.holder.saas.store.takeaway.producers.mapper.HolderAuthMapper;
import com.holder.saas.store.takeaway.producers.service.HolderAuthService;
import com.holder.saas.store.takeaway.producers.service.MtAuthService;
import com.holder.saas.store.takeaway.producers.service.UnOrderMqService;
import com.holder.saas.store.takeaway.producers.service.UnOrderReplyService;
import com.holder.saas.store.takeaway.producers.service.rpc.ConsumersFeignService;
import com.holder.saas.store.takeaway.producers.utils.HttpRequestUtils;
import com.holder.saas.store.takeaway.producers.utils.ThrowableExtUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.takeaway.*;
import com.holderzone.saas.store.dto.takeaway.request.SalesUpdateDTO;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutOwnAcceptReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.TakeoutOwnAcceptedRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.TakeoutOwnRejectedRespDTO;
import com.sankuai.sjst.platform.developer.domain.RequestSysParams;
import com.sankuai.sjst.platform.developer.request.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.net.URISyntaxException;


/**
 * <AUTHOR>
 * @Description 接收erp推送的的消息处理后发送给平台处理
 * @time 2017年7月25日 下午6:30:32
 */
@Slf4j
@Service("ownOrderReplyServiceImpl")
public class OwnOrderReplyServiceImpl extends ServiceImpl<HolderAuthMapper, HolderAuthDO> implements UnOrderReplyService {

    private final HolderAuthService holderAuthService;

    private final UnOrderMqService unOrderMqService;

    private final ConsumersFeignService consumersFeignService;

    private final String success = "10000";

    @Value("${own.ORDER_UPDATE}")
    private String orderUpdate;

    @Value("${own.URL}")
    private String url;

    @Value("${own.ITEM_URL}")
    private String itemurl;

    @Value("${own.APP_ID}")
    private String appId;

    @Value("${mt.SIGN_KEY}")
    private String mtSignKey;

    @Autowired
    public OwnOrderReplyServiceImpl(HolderAuthService holderAuthService, UnOrderMqService unOrderMqService,
                                    ConsumersFeignService consumersFeignService) {
        this.holderAuthService = holderAuthService;
        this.unOrderMqService = unOrderMqService;
        this.consumersFeignService = consumersFeignService;
    }

    @Override
    public void replyCancelOrder(UnOrder unOrder) {
        String msgType = "商家拒单";
        logReplyProcessing(unOrder, msgType);

        HolderAuthDO holderAuthDO = getOne(new LambdaQueryWrapper<HolderAuthDO>()
                .eq(HolderAuthDO::getStoreGuid, unOrder.getStoreGuid()));
        if (holderAuthDO == null) {
            throw new BusinessException("门店未绑定!请绑定门店后重试!");
        }
        String code = holderAuthDO.getCode();
        String token = holderAuthDO.getAccessToken();
        //调用状态变更接口
        TakeoutOwnAcceptReqDTO takeoutOwnAcceptReqDTO = new TakeoutOwnAcceptReqDTO();
        takeoutOwnAcceptReqDTO.setAppId(appId);
        takeoutOwnAcceptReqDTO.setSign("all");
        takeoutOwnAcceptReqDTO.setOrderID(Long.parseLong(unOrder.getOrderId()));
        takeoutOwnAcceptReqDTO.setStatus(OwnTypeEnum.已拒单.getType());

        String jsonParam = JacksonUtils.writeValueAsString(takeoutOwnAcceptReqDTO);
        log.info("【自营外卖平台】商家拒单参数：url={},param={},code={}", itemurl + orderUpdate, jsonParam, code);
        String result = HttpRequestUtils.sendHttpRequest(itemurl + orderUpdate, jsonParam, token, code);
        log.info("【自营外卖平台】商家拒单结果：result={}", result);
        if (result == null) {
            log.error("【自营外卖平台】商家拒单失败，storeGuid={},orderGuid={}", unOrder.getStoreGuid(), unOrder.getOrderId());
        }
        TakeoutOwnAcceptedRespDTO resp = JacksonUtils.toObject(TakeoutOwnAcceptedRespDTO.class, result);
        if (!success.equals(resp.getCode())) {
            log.error("【自营外卖平台】商家拒单失败，message={}", resp.getMessage());
        } else {
            SalesUpdateDTO sale = new SalesUpdateDTO();
            sale.setOrderID(Long.parseLong(unOrder.getOrderId()));
            sale.setDistributionType(unOrder.getDistributionType());
            sale.setOrderStatus(OwnTypeEnum.已拒单.getType());
            sale.setStoreGuid(unOrder.getStoreGuid());
            OwnCallbackResponse own = consumersFeignService.orderUpdate(sale);
            if (success.equals(String.valueOf(own.getCode()))) {
                log.info("【自营外卖平台】拒单更改订单状态，message={}", own.getMessage());
                //发送消息
                unOrder.setEnterpriseGuid(holderAuthDO.getEnterpriseGuid());
                unOrder.setOrderType(OrderType.TAKEOUT_ORDER.getType());
                unOrder.setOrderSubType(OrderType.TakeoutSubType.OWN_TAKEOUT.getType());
                unOrder.setCbMsgType(UnOrderCbMsgType.ORDER_CANCELED);
                unOrderMqService.sendUnOrder(unOrder);
            } else {
                log.error("【自营外卖平台】拒单更改订单状态失败，message={}", own.getMessage());
            }
        }
    }


    @Override
    public void replyConfirmOrder(UnOrder unOrder) {
        String msgType = "商家接单";

        HolderAuthDO holderAuthDO = getOne(new LambdaQueryWrapper<HolderAuthDO>()
                .eq(HolderAuthDO::getStoreGuid, unOrder.getStoreGuid()));
        if (holderAuthDO == null) {
            throw new BusinessException("门店未绑定!请绑定门店后重试!");
        }
        String code = holderAuthDO.getCode();
        String token = holderAuthDO.getAccessToken();

        //调用状态变更接口
        TakeoutOwnAcceptReqDTO takeoutOwnAcceptReqDTO = new TakeoutOwnAcceptReqDTO();
        takeoutOwnAcceptReqDTO.setAppId(appId);
        takeoutOwnAcceptReqDTO.setSign("all");
        takeoutOwnAcceptReqDTO.setOrderID(Long.parseLong(unOrder.getOrderId()));
        takeoutOwnAcceptReqDTO.setStatus(OwnTypeEnum.待配送.getType());
        takeoutOwnAcceptReqDTO.setDistributionType(unOrder.getDistributionType());
        String jsonParam = JacksonUtils.writeValueAsString(takeoutOwnAcceptReqDTO);
        log.info("【自营外卖平台】商家接单参数：url={},param={},code={}", itemurl + orderUpdate, jsonParam, code);
        String result = HttpRequestUtils.sendHttpRequest(itemurl + orderUpdate, jsonParam, token, code);
        log.info("【自营外卖平台】商家接单结果：result={}", result);
        if (result == null) {
            log.error("【自营外卖平台】商家接单失败，storeGuid={},orderGuid={}", unOrder.getStoreGuid(), unOrder.getOrderId());
        }
        TakeoutOwnRejectedRespDTO resp = JacksonUtils.toObject(TakeoutOwnRejectedRespDTO.class, result);
        if (!success.equals(resp.getCode())) {
            log.error("【自营外卖平台】商家接单失败，message={}", resp.getMessage());
        } else {
            SalesUpdateDTO sale = new SalesUpdateDTO();
            sale.setOrderID(Long.parseLong(unOrder.getOrderId()));
            sale.setDistributionType(unOrder.getDistributionType());
            sale.setOrderStatus(OwnTypeEnum.待配送.getType());
            sale.setStoreGuid(unOrder.getStoreGuid());
            OwnCallbackResponse own = consumersFeignService.orderUpdate(sale);
            if (success.equals(String.valueOf(own.getCode()))) {
                log.info("【自营外卖平台】接单更改订单状态，message={}", own.getMessage());
                //发送消息，打印单据
                unOrder.setEnterpriseGuid(holderAuthDO.getEnterpriseGuid());
                unOrder.setOrderType(OrderType.TAKEOUT_ORDER.getType());
                unOrder.setOrderSubType(OrderType.TakeoutSubType.OWN_TAKEOUT.getType());
                unOrder.setCbMsgType(UnOrderCbMsgType.ORDER_CONFIRMED);
                unOrder.setAcceptTime(DateTimeUtils.now());
                unOrderMqService.sendUnOrder(unOrder);
            } else {
                log.error("【自营外卖平台】接单更改订单状态失败，message={}", own.getMessage());
            }
        }
    }

    @Override
    public void replyAgreeCancelOrder(UnOrder unOrder) {

    }

    @Override
    public void replyDisagreeCancelOrder(UnOrder unOrder) {
    }

    @Override
    public void replyAgreeRefundOrder(UnOrder unOrder) {

    }

    @Override
    public void replyDisagreeRefundOrder(UnOrder unOrder) {

    }

    @Override
    public void startDelivery(UnOrder unOrder) {
        log.info("自营外卖平台暂未实现一城飞客配送");
    }

    @Override
    public OwnApiResult startDeliveryMQ(UnOrder unOrder) {
        log.info("自营外卖平台暂未实现一城飞客配送");
        OwnApiResult ownApiResult = new OwnApiResult();
        ownApiResult.setCode(0);
        ownApiResult.setMsg("自营外卖平台暂未实现一城飞客配送");
        return ownApiResult;
    }

    @Override
    public void cancelDelivery(UnOrder unOrder) {
        throw new BusinessException("自营外卖平台暂未实现一城飞客取消配送");
    }

    @Override
    public void replyUrgeOrder(UnOrder unOrder) {
        throw new BusinessException("美团暂未实现催单");
    }

    private void logReplyProcessing(UnOrder unOrder, String msgType) {
        log.info("Reply(自营外卖平台){}，orderId: {}，处理中", msgType, unOrder.getOrderId());
    }

    private void logReplySucceed(UnOrder unOrder, String msgType) {
        log.info("Reply(自营外卖平台){}，orderId: {}，处理成功", msgType, unOrder.getOrderId());
    }

    private void logTokenUnavailableThenThrow(UnOrder unOrder, String msgType) {
        log.error("Reply(自营外卖平台){}，orderId: {}，处理失败: 根据storeGuid: {} 未查询到Token",
                msgType, unOrder.getOrderId(), unOrder.getStoreGuid());
        throw new BusinessException("业务失败：" + "门店未绑定");
    }

    private void logReplyFailedThenThrow(UnOrder unOrder, MtResponseDTO.ErrorDetail errorDetail, String msgType) {
        log.error("Reply(自营外卖平台){}，orderId: {}，处理失败，code: {}, message: {}",
                msgType, unOrder.getOrderId(), errorDetail.getCode(), errorDetail.getMessage());
        if ("808".equals(errorDetail.getCode())) {
            throw new BusinessException("用户申诉退款只能由客服进行操作");
        }
        // https://developer.meituan.com/openapi#3.3
        // 4 authority_error 权限验证失败，请检查外卖或者团购门店是否绑定
        // 5 app_auth_token_error 令牌错误
        if ("4".equals(errorDetail.getCode())
                || "5".equals(errorDetail.getCode())) {
            try {
                holderAuthService.deleteAuth(unOrder.getStoreGuid());
                log.info("storeGuid: {}，(自营外卖平台)移除失效Token成功", unOrder.getStoreGuid());
            } catch (Throwable throwable) {
                log.error("storeGuid: {}，(自营外卖平台)移除失效Token失败，失败原因：{}",
                        unOrder.getStoreGuid(), ThrowableExtUtils.asStringIfAbsent(throwable));
            }
        }
        throw new BusinessException("业务失败：" + errorDetail.getMessage());
    }

    private void logExceptionThenThrow(UnOrder unOrder, Exception e, String msgType) {
        if (log.isErrorEnabled()) {
            log.error("Reply(自营外卖平台){}，orderId: {}，处理失败: {}", msgType, unOrder.getOrderId(),
                    ThrowableExtUtils.asStringIfAbsent(e));
        }
        throw new BusinessException("业务失败：" + ThrowableExtUtils.asStringIfAbsent(e));
    }

    private void doReply(CipCaterRequest request, UnOrder unOrder, String msgType) {
        try {
            String result = request.doRequest();
            MtResponseDTO mtResponseDTO = JacksonUtils.toObject(MtResponseDTO.class, result);
            if (mtResponseDTO.isOK()) {
                logReplySucceed(unOrder, msgType);
            } else {
                logReplyFailedThenThrow(unOrder, mtResponseDTO.getError(), msgType);
            }
        } catch (IOException | URISyntaxException e) {
            logExceptionThenThrow(unOrder, e, msgType);
        }
    }

    @Override
    public void replyDeliveryAccept(UnOrder unOrder) {
        throw new BusinessException("自营外卖平台暂未实现一城飞客骑手到达");

    }

    @Override
    public void replyDeliveryStart(UnOrder unOrder) {
        throw new BusinessException("自营外卖平台暂未实现一城飞客商家已送出");

    }

    @Override
    public void replyDeliveryCancel(UnOrder unOrder) {
        throw new BusinessException("自营外卖平台暂未实现一城飞客取消配送");

    }

    @Override
    public void replyDeliveryComplete(UnOrder unOrder) {
        throw new BusinessException("自营外卖平台暂未实现一城飞客商家已送达");

    }

    @Override
    public void replyRiderPosition(UnOrder unOrder) {
        throw new BusinessException("自营外卖平台暂未实现一城飞客骑手定位展示");
    }
}
