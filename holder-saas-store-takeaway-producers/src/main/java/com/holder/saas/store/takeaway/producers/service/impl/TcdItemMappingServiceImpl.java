package com.holder.saas.store.takeaway.producers.service.impl;

import com.google.common.collect.Lists;
import com.holder.saas.store.takeaway.producers.entity.domain.TcdAuthDO;
import com.holder.saas.store.takeaway.producers.service.UnItemMappingService;
import com.holder.saas.store.takeaway.producers.service.TcdAuthService;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.constant.takeaway.TakeawayConstants;
import com.holderzone.saas.store.dto.takeaway.*;
import com.holderzone.saas.store.dto.takeaway.request.TCDItemBindingReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.TcdDish;
import com.holderzone.saas.store.dto.takeaway.response.TcdDishSku;
import com.holderzone.saas.store.dto.takeaway.response.TcdItemMappingRespDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

@Slf4j
@Service("tcdItemMappingServiceImpl")
public class TcdItemMappingServiceImpl implements UnItemMappingService {

    @Resource
    private TcdAuthService tcdAuthService;

    @Qualifier("tcdProductQueryThreadPool")
    @Autowired
    private ExecutorService tcdProductQueryThreadPool;

    @Override
    public List<UnMappedType> getType(String storeGuid) {
        String token = tcdAuthService.checkToken(storeGuid);
        List<TcdItemMappingRespDTO> list = tcdAuthService.getType(token);
        return mtItemType2UnItemType(list);
    }


    private List<UnMappedType> mtItemType2UnItemType(List<TcdItemMappingRespDTO> tcdItemMappingRespDTOS) {
        if (CollectionUtils.isEmpty(tcdItemMappingRespDTOS)) {
            return Collections.emptyList();
        }
        return tcdItemMappingRespDTOS.stream()
                .map(tcdItemMappingRespDTO -> {
                    UnMappedType unMappedType = new UnMappedType();
                    unMappedType.setUnItemTypeId(String.valueOf(tcdItemMappingRespDTO.getId()));
                    unMappedType.setUnItemTypeName(tcdItemMappingRespDTO.getName());
                    return unMappedType;
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<UnMappedItem> getItem(String storeGuid) {
        String replyType = "查询菜品";
        logRequestProcessing(replyType, storeGuid);
        List<UnMappedItem> data;
        // 查询赚餐控者下所有菜品信息
        String token = tcdAuthService.checkToken(storeGuid);
        List<TcdItemMappingRespDTO> list = tcdAuthService.getItem(token);
        data = changeData(list);
        log.info("查询菜品返回：data={}", data);
        return data;
    }

    @Override
    public List<UnMappedItem> getItems(UnItemQueryReq unItemQueryReq) {
        List<String> storeGuids = unItemQueryReq.getStoreGuids();
        String storeGuidStr = String.join(",", storeGuids);
        logRequestProcessing(TakeawayConstants.BATCH_QUERY_ITEM_OPERATE + "(赚餐)", storeGuidStr);
        // 查询各门店的token
        List<TcdAuthDO> tokens = tcdAuthService.getTokens(storeGuids);
        if (CollectionUtils.isEmpty(tokens)) {
            log.warn("不存在token, storeGuids:{}", JacksonUtils.writeValueAsString(storeGuids));
            return Collections.emptyList();
        }
        if (storeGuids.size() != tokens.size()) {
            // 部分门店没有绑定
            List<String> authStoreGuids = tokens.stream().map(TcdAuthDO::getStoreGuid).collect(Collectors.toList());
            storeGuids.removeAll(authStoreGuids);
            log.warn("部分门店没有token, storeGuids:{}", JacksonUtils.writeValueAsString(storeGuids));
        }
        List<List<TcdAuthDO>> groupByTcdAuthList = Lists.partition(tokens, TakeawayConstants.BATCH_QUERY_ITEM_PARTITION);
        List<List<UnMappedItem>> tcdResultList = Collections.synchronizedList(new ArrayList<>());
        CompletableFuture.allOf(groupByTcdAuthList.stream()
                .map(queryAuths ->
                        CompletableFuture.supplyAsync(() -> queryAuths.stream()
                                .map(queryAuth -> queryMergeItem(queryAuth.getAccessToken(), queryAuth.getStoreGuid()))
                                .collect(Collectors.toList()), tcdProductQueryThreadPool)
                                .whenComplete((result, throwable) -> {
                                    if (!CollectionUtils.isEmpty(result)) {
                                        tcdResultList.addAll(result);
                                    }
                                    if (throwable != null) {
                                        log.error("completableFuture error:{}", throwable.getMessage());
                                    }
                                })).toArray(CompletableFuture[]::new)).whenComplete((v, th) -> {
        }).join();
        List<UnMappedItem> unMappedItemList = tcdResultList.stream().flatMap(Collection::stream).collect(Collectors.toList());
        if (!StringUtils.isEmpty(unItemQueryReq.getKeywords())) {
            unMappedItemList.removeIf(e -> !e.getUnItemNameWithSku().contains(unItemQueryReq.getKeywords()));
        }
        log.info("批量查询门店商品列表返回:{}", JacksonUtils.writeValueAsString(unMappedItemList));
        return unMappedItemList;
    }

    /**
     * 查询赚餐 商品分类 + 商品列表
     * 赚餐在商品列表上直接返回了商品分类字段
     *
     * @param authToken 门店授权token
     * @param storeGuid 门店guid
     * @return 商品列表
     */
    private List<UnMappedItem> queryMergeItem(String authToken, String storeGuid) {
        // 再查询商品列表
        try {
            List<TcdItemMappingRespDTO> tcdItemList = tcdAuthService.getItem(authToken);
            log.info("查询赚餐菜品返回,storeGuid:{}, tcdItemList:{}", storeGuid, JacksonUtils.writeValueAsString(tcdItemList));
            List<UnMappedItem> unMappedItemList = changeData(tcdItemList);
            unMappedItemList.forEach(e -> e.setErpStoreGuid(storeGuid));
            return unMappedItemList;
        } catch (Exception e) {
            log.error("查询赚餐菜品失败：e:{}", e.getMessage());
            return Collections.emptyList();
        }
    }


    public List<UnMappedItem> changeData(List<TcdItemMappingRespDTO> list) {
        List<UnMappedItem> data = new ArrayList<>();
        if (ObjectUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        for (TcdItemMappingRespDTO tcdItemMappingRespDTO : list) {
            List<TcdDish> dishes = tcdItemMappingRespDTO.getDishes();
            for (TcdDish tcdDish : dishes) {
                List<TcdDishSku> skus = tcdDish.getSkus();
                for (TcdDishSku tcdDishSku : skus) {
                    UnMappedItem unItem = new UnMappedItem();
                    //规格Id
                    unItem.setUnItemSkuId(String.valueOf(tcdDishSku.getId()));
                    //规格名称
                    unItem.setUnItemSkuName(tcdDishSku.getSpec());
                    //价格：放入透传字段
                    unItem.setExtendValue(String.valueOf(tcdDishSku.getPrice()));
                    //菜品ID
                    unItem.setUnItemId(String.valueOf(tcdDish.getId()));
                    //菜品名称
                    unItem.setUnItemName(tcdDish.getName());
                    //菜品类型ID
                    unItem.setUnItemTypeId(String.valueOf(tcdItemMappingRespDTO.getId()));
                    //菜品类型名称
                    unItem.setUnItemTypeName(tcdItemMappingRespDTO.getName());
                    //已绑定菜品sku
                    unItem.setErpItemSkuId(tcdDishSku.getHolderDishSkuId());
                    //已绑定菜品id
                    unItem.setErpItemId("");
                    //平台方单位名称
                    unItem.setUnItemSkuName("");
                    //平台方单位名称
                    if (StringUtils.hasText(tcdDishSku.getSpec())) {
                        unItem.setUnItemNameWithSku(tcdDish.getName() + "(" + tcdDishSku.getSpec() + ")");
                    } else {
                        unItem.setUnItemNameWithSku(tcdDish.getName());
                    }
                    data.add(unItem);
                }
            }
        }
        return data;
    }

    private void logRequestProcessing(String msg, String storeGuid) {
        log.info("Request(赚餐外卖平台){}，storeGuid: {}，处理中", msg, storeGuid);
    }

    private void logRequestDataSucceed(String msg, String storeGuid, Object obj) {
        log.info("Request(赚餐外卖平台){}，storeGuid: {}，查询成功：{}",
                msg, storeGuid, JacksonUtils.writeValueAsString(obj));
    }

    private void logReplyDataSucceed(String msg, String storeGuid, Object obj) {
        log.info("Request(赚餐外卖平台){}，storeGuid: {}，处理成功，处理结果：{}"
                , msg, storeGuid, JacksonUtils.writeValueAsString(obj));
    }

    @Override
    public void bindMapping(UnItemBindUnbindReq unItemBindUnbindReq) {
        handleMapping(unItemBindUnbindReq, true);
    }

    @Override
    public void unbindMapping(UnItemBindUnbindReq unItemBindUnbindReq) {
        handleMapping(unItemBindUnbindReq, false);
    }

    @SuppressWarnings("unchecked")
    private void handleMapping(UnItemBindUnbindReq unItemBindUnbindReq, boolean isBinding) {
        String storeGuid = unItemBindUnbindReq.getStoreGuid();
        String token = tcdAuthService.getToken(storeGuid);
        if ("-1".equals(token)) {
            throw new BusinessException("门店未绑定!请绑定门店后重试!");
        }
        String replyType = isBinding ? "设置商品映射" : "删除商品映射";
        logRequestProcessing(replyType, storeGuid);
        //实现菜品绑定逻辑
        TCDItemBindingReqDTO tcdItemBindingReqDTO = new TCDItemBindingReqDTO();
        List<TcdDishSku> dishes = new ArrayList<>();
        TcdDishSku tcdDishSku = new TcdDishSku();
        tcdDishSku.setId(Long.parseLong(unItemBindUnbindReq.getUnItemSkuId()));
        tcdDishSku.setDishId(unItemBindUnbindReq.getUnItemId());
        tcdDishSku.setHolderDishSkuId(unItemBindUnbindReq.getErpItemSkuId());
        tcdDishSku.setHolderDishId(unItemBindUnbindReq.getErpItemGuid());
        tcdDishSku.setHolderActualDishSkuId(unItemBindUnbindReq.getActualErpItemSkuId());
        dishes.add(tcdDishSku);
        tcdItemBindingReqDTO.setToken(token);
        tcdItemBindingReqDTO.setDishSkus(dishes);
        if (isBinding) {
            tcdItemBindingReqDTO.setOperateType(0);
        } else {
            tcdItemBindingReqDTO.setOperateType(1);
        }
        String result = tcdAuthService.doTcdItemBinding(tcdItemBindingReqDTO);
        if (isBinding) {
            //菜品绑定
            log.info("菜品绑定,result={}", result);
        } else {
            //菜品解绑
            log.info("菜品解绑,result={}", result);
        }
    }

    private void handleMapping(UnItemBatchUnbindReq unItemBatchUnbindReq) {
        String storeGuid = unItemBatchUnbindReq.getStoreGuid();
        String token = tcdAuthService.getToken(storeGuid);
        if ("-1".equals(token)) {
            throw new BusinessException("门店未绑定!请绑定门店后重试!");
        }
        String replyType = unItemBatchUnbindReq.getBindFlag() ? "设置商品映射" : "删除商品映射";
        logRequestProcessing(replyType, storeGuid);
        //实现菜品绑定逻辑
        TCDItemBindingReqDTO tcdItemBindingReqDTO = new TCDItemBindingReqDTO();
        List<TcdDishSku> dishes = new ArrayList<>();
        for (UnItemBaseMapReq unItemBindUnbindReq : unItemBatchUnbindReq.getUnItemUnbindList()) {
            TcdDishSku tcdDishSku = new TcdDishSku();
            tcdDishSku.setId(Long.parseLong(unItemBindUnbindReq.getUnItemSkuId()));
            tcdDishSku.setDishId(unItemBindUnbindReq.getUnItemId());
            tcdDishSku.setHolderDishSkuId(unItemBindUnbindReq.getErpItemSkuId());
            tcdDishSku.setHolderDishId(unItemBindUnbindReq.getErpItemGuid());
            tcdDishSku.setHolderActualDishSkuId(unItemBindUnbindReq.getActualErpItemSkuId());
            dishes.add(tcdDishSku);
        }
        tcdItemBindingReqDTO.setToken(token);
        tcdItemBindingReqDTO.setDishSkus(dishes);
        if (unItemBatchUnbindReq.getBindFlag()) {
            tcdItemBindingReqDTO.setOperateType(0);
        } else {
            tcdItemBindingReqDTO.setOperateType(1);
        }
        String result = tcdAuthService.doTcdItemBinding(tcdItemBindingReqDTO);
        if (unItemBatchUnbindReq.getBindFlag()) {
            //菜品绑定
            log.info("菜品绑定,result={}", result);
        } else {
            //菜品解绑
            log.info("菜品解绑,result={}", result);
        }
    }

    @Override
    public void batchUnbindMapping(UnItemBatchUnbindReq unItemBatchUnbindReq) {
        handleMapping(unItemBatchUnbindReq);
    }
}
