package com.holder.saas.store.takeaway.producers.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holder.saas.store.takeaway.producers.entity.domain.JdItemMappingDO;
import com.holderzone.saas.store.dto.takeaway.UnItemBindUnbindReq;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@Mapper
public interface JdItemMappingMapper extends BaseMapper<JdItemMappingDO> {
    
    @Update("UPDATE hst_jd_item_mapping SET is_deleted = 1 WHERE store_guid = #{req.storeGuid} AND spu_id = #{req.unItemId} AND sku_id = #{req.unItemSkuId} AND item_guid = #{req.erpItemGuid}")
    void unbind(@Param("req") UnItemBindUnbindReq unItemBindUnbindReq);
    
    /**
     * 批量插入商品映射
     * 
     * @param mappingList 要插入的商品映射列表
     * @return 影响的行数
     */
    @Insert("<script>" +
            "INSERT INTO hst_jd_item_mapping " +
            "(store_guid, item_guid, sku_guid, sku_id, spu_id, is_deleted) VALUES " +
            "<foreach collection='mappingList' item='mapping' separator=','>" +
            "(#{mapping.storeGuid}, #{mapping.itemGuid}, #{mapping.skuGuid}, #{mapping.skuId}, #{mapping.spuId}, #{mapping.deleted})" +
            "</foreach>" +
            "</script>")
    int batchInsert(@Param("mappingList") List<JdItemMappingDO> mappingList);
    
    /**
     * 批量更新商品映射
     * 
     * @param mappingList 要更新的商品映射列表
     * @return 影响的行数
     */
    @Update("<script>" +
            "<foreach collection='mappingList' item='mapping' separator=';'>" +
            "UPDATE hst_jd_item_mapping SET " +
            "item_guid = #{mapping.itemGuid}, " +
            "sku_guid = #{mapping.skuGuid} " +
            "WHERE id = #{mapping.id}" +
            "</foreach>" +
            "</script>")
    int batchUpdate(@Param("mappingList") List<JdItemMappingDO> mappingList);
    
    /**
     * 批量解绑商品映射（逻辑删除）
     * 
     * @param storeGuid 门店GUID
     * @param skuIds 商品SKU ID列表
     * @param spuIds 商品SPU ID列表
     * @return 影响的行数
     */
    @Update("<script>" +
            "UPDATE hst_jd_item_mapping SET is_deleted = 1 " +
            "WHERE store_guid = #{storeGuid} " +
            "AND sku_id IN " +
            "<foreach collection='skuIds' item='skuId' open='(' separator=',' close=')'>" +
            "#{skuId}" +
            "</foreach>" +
            "AND spu_id IN " +
            "<foreach collection='spuIds' item='spuId' open='(' separator=',' close=')'>" +
            "#{spuId}" +
            "</foreach>" +
            "AND is_deleted = 0" +
            "</script>")
    int batchUnbind(@Param("storeGuid") String storeGuid, 
                   @Param("skuIds") List<String> skuIds, 
                   @Param("spuIds") List<String> spuIds);
}
