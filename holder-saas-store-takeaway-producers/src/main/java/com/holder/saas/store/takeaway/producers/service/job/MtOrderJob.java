package com.holder.saas.store.takeaway.producers.service.job;

import com.holder.saas.store.takeaway.producers.constant.TakeoutConstant;
import com.holder.saas.store.takeaway.producers.entity.domain.MtAuthDO;
import com.holder.saas.store.takeaway.producers.service.MtAuthService;
import com.holder.saas.store.takeaway.producers.service.MtUnOrderParser;
import com.holder.saas.store.takeaway.producers.service.UnOrderMqService;
import com.holder.saas.store.takeaway.producers.utils.ThrowableExtUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.takeaway.*;
import com.sankuai.sjst.platform.developer.domain.RequestSysParams;
import com.sankuai.sjst.platform.developer.request.CipCaterTakeoutOrderQueryByIdRequest;
import com.sankuai.sjst.platform.developer.request.CipCaterTakeoutOrderQueryNewByDevIdRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.net.URISyntaxException;
import java.util.List;

@Slf4j
@Component
public class MtOrderJob{

    private static final int FETCH_SIZE_PER_BATCH = 100;

    private static final int MAX_NUMBER_OF_RECURSIONS = 10;

    @Value("${mt.DEVELOPER_ID}")
    private Integer developerId;

    @Value("${mt.SIGN_KEY}")
    private String mtSignKey;

    private final MtAuthService mtAuthService;

    private final MtUnOrderParser mtUnOrderParser;

    private final UnOrderMqService unOrderMqService;

    @Autowired
    public MtOrderJob(MtAuthService mtAuthService, MtUnOrderParser mtUnOrderParser, UnOrderMqService unOrderMqService){
        this.mtAuthService = mtAuthService;
        this.mtUnOrderParser = mtUnOrderParser;
        this.unOrderMqService = unOrderMqService;
    }

    @Async
    public void queryNewOrdersByDevId(){
        log.info("<==========(美团)订单轮询，最近5分钟内未被商家确认的美团订单号列表刷新开始==========");
        recursion(0L, 0);
        log.info("<==========(美团)订单轮询，最近5分钟内未被商家确认的美团订单号列表刷新结束==========>");
    }

    public void recursion(long maxOffsetId, int numberOfRecursions){
        CipCaterTakeoutOrderQueryNewByDevIdRequest cipCaterRequest = new CipCaterTakeoutOrderQueryNewByDevIdRequest();
        RequestSysParams requestSysParams = new RequestSysParams(mtSignKey, null, TakeoutConstant.CHARSET_UTF_8);
        cipCaterRequest.setRequestSysParams(requestSysParams);
        cipCaterRequest.setDeveloperId(developerId);
        cipCaterRequest.setMaxOffsetId(maxOffsetId);
        cipCaterRequest.setSize(FETCH_SIZE_PER_BATCH);
        try {
            String requestResult = cipCaterRequest.doRequest();
            log.info("刷新美团订单号列表返回:{}", requestResult);
            MtQueryOrderIdDTO mtQueryOrderIdDTO = JacksonUtils.toObject(MtQueryOrderIdDTO.class, requestResult);
            if (!StringUtils.isEmpty(mtQueryOrderIdDTO.getError_type())) {
                log.error("刷新美团订单号列表失败：" + mtQueryOrderIdDTO.getMessage());
            } else {
                List<MtQueryOrderIdDetail> mtQueryOrderIdDetails = mtQueryOrderIdDTO.getData();
                if (mtQueryOrderIdDetails != null && !mtQueryOrderIdDetails.isEmpty()) {
                    for (MtQueryOrderIdDetail mtQueryOrderIdDetail : mtQueryOrderIdDetails) {
                        Long orderId = mtQueryOrderIdDetail.getOrderId();
                        String storeGuid = mtQueryOrderIdDetail.getEpoiId();
                        try {
                            MtAuthDO mtAuthDO = mtAuthService.getAuth(storeGuid, MtBusinessIdEnum.TAKEOUT.getType());

                            if (null == mtAuthDO) {
                                log.warn("(美团)订单轮询，根据storeGuid: {} 未查询到授权记录", storeGuid);
                                continue;
                            }
                            String authToken = mtAuthDO.getAccessToken();
                            CipCaterTakeoutOrderQueryByIdRequest request = new CipCaterTakeoutOrderQueryByIdRequest();
                            requestSysParams = new RequestSysParams(mtSignKey, authToken,
                                    TakeoutConstant.CHARSET_UTF_8);
                            request.setRequestSysParams(requestSysParams);
                            request.setOrderId(orderId);
                            requestResult = request.doRequest();
                            MtQueryOrderDTO mtQueryOrderDTO = JacksonUtils.toObject(MtQueryOrderDTO.class,
                                    requestResult);
                            if (!StringUtils.isEmpty(mtQueryOrderDTO.getError_type()) || mtQueryOrderDTO.getData() == null) {
                                log.error("(美团)订单轮询，根据orderId: {} 获取美团订单失败： {}", orderId, mtQueryOrderDTO.getMessage());
                                mtAuthService.correctAuth(storeGuid, MtBusinessIdEnum.TAKEOUT.getType(),
                                        String.valueOf(mtQueryOrderDTO.getError_type()));
                            } else {
                                mtQueryOrderDTO.getData().setEPoiId(storeGuid);
                                UnOrder unOrder = mtUnOrderParser.fromMtQueryOrderDetail(mtQueryOrderDTO.getData());
                                if (log.isInfoEnabled()) {
                                    log.info("(美团)订单轮询，向ERP发送(美团)新订单，unOrder：{}",
                                            JacksonUtils.writeValueAsString(unOrder));
                                }
                                unOrderMqService.sendUnOrder(unOrder);
                            }
                        } catch (IOException | URISyntaxException e) {
                            log.error("(美团)订单轮询，根据orderId: {} 获取美团订单失败： {}", orderId,
                                    ThrowableExtUtils.asStringIfAbsent(e));
                        }
                    }
                    int fetchedSizeThisBatch = mtQueryOrderIdDetails.size();
                    if (FETCH_SIZE_PER_BATCH == fetchedSizeThisBatch && ++numberOfRecursions < MAX_NUMBER_OF_RECURSIONS) {
                        recursion(mtQueryOrderIdDetails.get(fetchedSizeThisBatch - 1).getOffsetId(),
                                numberOfRecursions);
                    }
                }
            }
        } catch (IOException | URISyntaxException e) {
            log.error("(美团)订单轮询，刷新美团订单号列表失败：" + ThrowableExtUtils.asStringIfAbsent(e));
        }
    }
}
