package com.holder.saas.store.takeaway.producers.service.impl;

import com.holder.saas.store.takeaway.producers.service.OrderTradeDetailMqService;
import com.holderzone.framework.rocketmq.common.DefaultRocketMqProducer;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.takeaway.TakeoutOrderTradeDetailDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.Message;
import org.springframework.stereotype.Service;

import static com.holder.saas.store.takeaway.producers.config.RocketMqConfig.TAKEAWAY_CONSUMERS_ORDER_TRADE_DETAIL_TOPIC;
import static com.holder.saas.store.takeaway.producers.config.RocketMqConfig.TAKEAWAY_CONSUMERS_ORDER_TRADE_DETAIL_TAG;

@Slf4j
@Service
@RequiredArgsConstructor
public class OrderTradeDetailMqServiceImpl implements OrderTradeDetailMqService {

    private final DefaultRocketMqProducer defaultRocketMqProducer;

    @Override
    public void sendOrderTradeDetail(TakeoutOrderTradeDetailDTO tradeDetailDTO) {
        Message message = new Message(TAKEAWAY_CONSUMERS_ORDER_TRADE_DETAIL_TOPIC,
                TAKEAWAY_CONSUMERS_ORDER_TRADE_DETAIL_TAG, JacksonUtils.toJsonByte(tradeDetailDTO));

        defaultRocketMqProducer.sendMessage(message);
    }
}
