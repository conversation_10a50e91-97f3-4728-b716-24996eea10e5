package com.holder.saas.store.takeaway.producers.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = "jd")
@Data
public class JdConfig {

    public String appKey;

    public String appSecret;

    public String authUrl = "https://store-hub.jddj.com/store/login?encryptAppId=H7JPnIRuukdIVdK7jehUOQ%3D%3D";

    /**
     * 跳转地址
     */
    public String returnUrl;
}
