package com.holder.saas.store.takeaway.producers.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 支付宝授权
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-15
 */
@Data
@TableName("hst_alipay_auth")
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="HstAlipayAuth对象", description="支付宝授权")
public class AlipayAuthDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "唯一标识")
    private String guid;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime gmtModified;

    @TableLogic
    @ApiModelProperty(value = "是否已删除：0=未删除，1=已删除")
    private Boolean isDeleted;

    @ApiModelProperty(value = "企业GUID")
    private String enterpriseGuid;

    @ApiModelProperty(value = "运营主体GUID")
    private String operSubjectGuid;

    @ApiModelProperty(value = "应用授权Token")
    private String appAuthToken;

    @ApiModelProperty(value = "支付宝商户appid")
    private String appId;

    @ApiModelProperty(value = "'应用公钥'")
    private String applyPublicKey;

    @ApiModelProperty(value = "'应用私钥'")
    private String applyPrivateKey;

    @ApiModelProperty(value = "'支付宝公钥'")
    private String aliPublicKey;

    @ApiModelProperty(value = "支付宝接口加密方式")
    private String aes;

}
