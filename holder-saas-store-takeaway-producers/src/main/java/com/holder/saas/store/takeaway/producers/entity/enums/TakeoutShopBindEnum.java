package com.holder.saas.store.takeaway.producers.entity.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@AllArgsConstructor
@Getter
public enum TakeoutShopBindEnum {

    MT_TAKEOUT(1, "美团外卖"),

    ELE_TAKEOUT(2, "饿了么外卖"),

    OWN_TAKEOUT(3, "自营外卖"),

    TIKTOK_TAKEOUT(4, "抖音外卖"),

    JD_TAKEOUT(5, "京东外卖");

    private int type;

    /**
     * 描述
     */
    private String desc;

}
