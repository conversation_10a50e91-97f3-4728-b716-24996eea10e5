package com.holder.saas.store.takeaway.producers.utils.sdk.jd;

import com.alibaba.fastjson.JSON;
import com.holder.saas.store.takeaway.producers.utils.sdk.jd.dto.RefreshTokenDTO;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @description 更新授权令牌
 */
@Slf4j
public class RefreshTokenRequest extends AbstractJdRequest{


    public RefreshTokenRequest() {
    }

    public RefreshTokenRequest(String appKey, String appSecret, String token) {
       super.appKey = appKey;
       super.appSecret = appSecret;
       super.token = token;
    }

    public boolean execute(RefreshTokenDTO refreshToken){
        try {
            super.execute(JSON.toJSONString(refreshToken), "/ApplicationService/verificationUpdateToken");
            return true;
        }catch (Exception e){
            log.error("更新token失败",e);
        }
        return false;
    }
}
