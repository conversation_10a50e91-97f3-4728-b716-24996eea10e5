package com.holder.saas.store.takeaway.producers.utils.sdk.jd;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.holder.saas.store.takeaway.producers.utils.sdk.jd.dto.*;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @description 商家确认接单接口
 */
@Slf4j
public class OrderAcceptOperateRequest extends AbstractJdRequest {


    public OrderAcceptOperateRequest() {
    }

    public OrderAcceptOperateRequest(String appKey, String appSecret, String token) {
       super.appKey = appKey;
       super.appSecret = appSecret;
       super.token = token;
    }

    public boolean execute(OrderAcceptOperateDTO orderAcceptOperate){
        try {
            String response = super.execute(JSON.toJSONString(orderAcceptOperate), "/ocs/orderAcceptOperate");

            CommonRspDTO<String> orderAcceptOperateRsp = JSON.parseObject(response, new TypeReference<CommonRspDTO<String>>(){});
            return orderAcceptOperateRsp.isSuccess();
        }catch (Exception e){
            log.error("商家确认接单失败",e);
        }
        return false;
    }
}
