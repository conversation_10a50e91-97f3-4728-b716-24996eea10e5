package com.holder.saas.store.takeaway.producers.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holder.saas.store.takeaway.producers.entity.domain.TcdAuthDO;
import com.holderzone.saas.store.dto.takeaway.request.*;
import com.holderzone.saas.store.dto.takeaway.response.StoreAuthDTO;
import com.holderzone.saas.store.dto.takeaway.response.TcdCommonRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.TcdItemMappingRespDTO;

import java.util.List;

public interface TcdAuthService extends IService<TcdAuthDO> {

    StoreAuthDTO getTakeoutAuth(StoreAuthDTO storeAuthDTO);

    String getToken(String storeGuid);

    Boolean updateDelivery(StoreAuthDTO storeAuthDTO);

    TcdCommonRespDTO doShopBindingTcd(TCDBindReqDTO TCDBindReqDTO);

    TcdCommonRespDTO doShopUnBindingTcd(TCDBindReqDTO TCDBindReqDTO);

    String doTcdItemBinding(TCDItemBindingReqDTO TCDItemBindingReqDTO);

    List<TcdItemMappingRespDTO> getItem(String token);

    List<TcdItemMappingRespDTO> getType(String token);

    TcdCommonRespDTO diningOutTcd(TakeoutTCDDiningOutDTO takeoutTCDDiningOutDTO);

    TcdCommonRespDTO confirmTheMealTcd(TakeoutTCDConfirmTheMealDTO takeoutTCDConfirmTheMealDTO);

    TcdCommonRespDTO pickUpTcd(TakeoutTCDPickUpDTO takeoutTCDPickUpDTO);

    void removeByZcStoreId(String zcStoreId);

    String checkToken(String storeGuid);

    List<TcdAuthDO> getTokens(List<String> storeGuids);
}
