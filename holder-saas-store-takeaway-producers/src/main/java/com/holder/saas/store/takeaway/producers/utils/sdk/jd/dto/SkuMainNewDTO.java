package com.holder.saas.store.takeaway.producers.utils.sdk.jd.dto;

import lombok.Data;

import java.util.List;

@Data
public class SkuMainNewDTO {

    /**
     * 商品名称
     */
    private String skuName;

    /**
     * 外部商品编码
     */
    private String outSkuId;

    /**
     * 上下架状态1上架、2下架、4删除
     */
    private Integer fixedStatus;

    /**
     * SPU商品图片
     */
    private String[] images;

    /**
     * 商品描述
     */
    private String productDesc;

    /**
     * 需要和SPU的销售属性的顺序保持一致
     */
    private List<SaleAttrValueRelationInfo> saleAttrValues;

    /**
     * 卖点词，每个SKU可填写2-3个卖点，每个卖点3-7个字（至少2个卖点词）
     */
    private List<String> salePointList;

    /**
     * 商品初始化价格（单位：分）
     */
    private Long skuPrice;

    /**
     * 重量（单位：千克）
     */
    private Float weight;

    /**
     * UPC编码
     */
    private String upcCode;

    /**
     *餐盒费 仅当分类下支持设置餐盒费时候，才可设置(可通过【获取京东到家类目信息接口】查询) 餐盒费需大于等于0元，小于等于5元，且最多保留两位小数
     */
    private Float packageFee;

    /**
     * 商品初始化价格（单位：分）
     */
    private Long spuId;
}
