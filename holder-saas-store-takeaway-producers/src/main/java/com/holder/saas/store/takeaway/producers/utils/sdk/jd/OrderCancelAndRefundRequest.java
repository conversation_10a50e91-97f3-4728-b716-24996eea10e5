package com.holder.saas.store.takeaway.producers.utils.sdk.jd;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.holder.saas.store.takeaway.producers.utils.sdk.jd.dto.CommonRspDTO;
import com.holder.saas.store.takeaway.producers.utils.sdk.jd.dto.OrderCancelAndRefundDTO;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @description 单取消且退款接口
 * 应用场景：
 * 1、商家自送订单在配送流程中，若用户拒收，商家可调用接口进行取消；
 * 2、非商家自送订单，调用接口取消失败，仅可用户进行取消；
 * 3、达达配送转商家自送的订单在“配送中”，若用户拒收，商家可调用接口进行取消；
 */
@Slf4j
public class OrderCancelAndRefundRequest extends AbstractJdRequest {


    public OrderCancelAndRefundRequest() {
    }

    public OrderCancelAndRefundRequest(String appKey, String appSecret, String token) {
       super.appKey = appKey;
       super.appSecret = appSecret;
       super.token = token;
    }

    public boolean execute(OrderCancelAndRefundDTO orderCancelAndRefund){
        try {
            String response = super.execute(JSON.toJSONString(orderCancelAndRefund), "/orderStatus/cancelAndRefund");

            CommonRspDTO<String> orderAcceptOperateRsp = JSON.parseObject(response, new TypeReference<CommonRspDTO<String>>(){});
            return orderAcceptOperateRsp.isSuccess();
        }catch (Exception e){
            log.error("订单取消且退款失败",e);
        }
        return false;
    }
}
