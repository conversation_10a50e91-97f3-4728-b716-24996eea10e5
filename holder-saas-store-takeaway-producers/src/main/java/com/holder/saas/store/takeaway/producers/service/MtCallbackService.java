package com.holder.saas.store.takeaway.producers.service;

import com.holderzone.saas.store.dto.takeaway.MtCallbackAuthorizationDTO;
import com.holderzone.saas.store.dto.takeaway.MtCallbackDTO;
import com.holderzone.saas.store.dto.takeaway.MtCallbackSettlementDTO;
import com.holderzone.saas.store.dto.takeaway.MtCallbackUnbindAuthorizationDTO;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderRocketListener
 * @date 2018/11/15 12:00
 * @description 美团外卖类服务
 * @program holder-saas-store-takeaway
 */
public interface MtCallbackService {

    /**
     * 订单回调处理
     * <p>
     * 新的美团订单
     * <p>
     * 美团订单已取消
     * 1001 系统取消，超时未确认
     * 1002 系统取消，在线支付订单30分钟未支付
     * 1101 用户取消，在线支付中取消
     * 1102 用户取消，商家确认前取消
     * 1103 用户取消，用户退款取消
     * 1201 客服取消，用户下错单
     * 1202 客服取消，用户测试
     * 1203 客服取消，重复订单
     * 1204 客服取消，其他原因
     * 1301 其他原因
     * <p>
     * 美团订单退款
     * notifyType
     * apply 发起退款
     * agree 确认退款
     * reject 驳回退款
     * cancelRefund 用户取消退款申请
     * cancelRefundComplaint 取消退款申诉
     * <p>
     * 美团订单部分退款
     * notifyType
     * part 商家/用户发起部分退款
     * agree 商家同意部分退款
     * reject 商家拒绝部分退款
     * <p>
     * 美团订单已接单
     * <p>
     * 美团订单已完成
     * <p>
     * 美团订单配送中
     * 配送状态
     * 0 配送单发往配送
     * 5 已经分配骑手，等待骑手接单
     * 10 配送单已确认(骑手接单)
     * 15 骑手已到店
     * 20 骑手已取餐
     * 40 骑手已送达
     * 100 配送单已取消
     *
     * @param mtCallbackDTO
     * @param path
     * @see com.holderzone.saas.store.dto.takeaway.MtCbOcReasonCodeEnum 订单取消原因枚举
     * @see com.holderzone.saas.store.dto.takeaway.MtCbOrNotifyTypeEnum 退款操作类型枚举
     * @see com.holderzone.saas.store.dto.takeaway.MtCbOprNotifyTypeEnum 部分退款操作类型枚举
     * @see com.holderzone.saas.store.dto.takeaway.MtCbShippingStatusEnum 配送状态枚举
     */
    void orderCallback(MtCallbackDTO mtCallbackDTO, String path);

    /**
     * 美团隐私号降级
     *
     * @param mtCallbackDTO
     */
    void orderPrivacyDegrade(MtCallbackDTO mtCallbackDTO);

    /**
     * 接收订单结算明细回调接口
     */
    void orderTradeDetailCallback(MtCallbackSettlementDTO mtCallbackSettlementDTO);

    void authorizationCallback(MtCallbackAuthorizationDTO mtCallbackDTO);

    void unAuthorizationCallback(MtCallbackUnbindAuthorizationDTO mtCallbackDTO);
}
