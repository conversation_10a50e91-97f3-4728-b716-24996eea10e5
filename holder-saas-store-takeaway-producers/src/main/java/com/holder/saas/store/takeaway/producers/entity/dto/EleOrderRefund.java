package com.holder.saas.store.takeaway.producers.entity.dto;

import eleme.openapi.sdk.api.entity.order.ORefundOrder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 取消单退单消息
 * <p>
 * https://nest-fe.faas.ele.me/openapi/documents/callback
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class EleOrderRefund extends ORefundOrder implements Serializable {

    private static final long serialVersionUID = -7228813175502100274L;

    /**
     * 退单操作原因描述
     */
    private String reason;

    /**
     * 消息发送时间戳，单位秒
     */
    private long updateTime;
}
