package com.holder.saas.store.takeaway.producers.utils;

import com.holder.saas.store.takeaway.producers.constant.TakeoutConstant;
import com.holder.saas.store.takeaway.producers.entity.domain.MtAuthDO;
import com.holder.saas.store.takeaway.producers.entity.dto.MtCbPrivacyDTO;
import com.holder.saas.store.takeaway.producers.service.MtAuthService;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.takeaway.MtBusinessIdEnum;
import com.holderzone.saas.store.dto.takeaway.request.MtCouponReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtCouponDoCheckRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtCouponPreRespDTO;
import com.sankuai.sjst.platform.developer.domain.RequestSysParams;
import com.sankuai.sjst.platform.developer.request.CipCaterCouponConsumeRequest;
import com.sankuai.sjst.platform.developer.request.CipCaterCouponConsumptionPrepareRequest;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
public class MtCouponUtils {
    public static MtCouponDoCheckRespDTO checkTicket(MtCouponReqDTO mtCouponReqDTO,MtAuthService authService, String mtSignKey, String developerId){
        log.info("进入checkTicket方法,developerId:{},mtSignKey:{}",developerId,mtSignKey);
        String resultPre;
        String resultDo;
        String maxCheckedNum;
        String minCheckedNum;
        String errorPre;
        MtAuthDO mtAuthDO = authService.getAuth(mtCouponReqDTO.getStoreGuid(), MtBusinessIdEnum.TUAN_GOU.getType());
        String authToken = mtAuthDO.getAccessToken();
        MtCouponDoCheckRespDTO mtCouponDoCheckRespDTO;
        try {
            //1.预验券，查询出最大可验条数
            CipCaterCouponConsumptionPrepareRequest requestPre = new CipCaterCouponConsumptionPrepareRequest();
            RequestSysParams requestSysParams = new RequestSysParams(mtSignKey, authToken, TakeoutConstant.CHARSET_UTF_8);
            requestPre.setRequestSysParams(requestSysParams);
            requestPre.setCouponCode(mtCouponReqDTO.getCouponCode());
            resultPre = requestPre.doRequest();
            log.info("预验券返回：{}", resultPre);
            MtCbPrivacyDTO mtCbPrivacyDTO = JacksonUtils.toObject(MtCbPrivacyDTO.class, resultPre);
            errorPre = mtCbPrivacyDTO.getError();
            if (errorPre != null) {
                MtCbPrivacyDTO.MtCbErrorDetail mtCbErrorDetail = JacksonUtils.toObject(MtCbPrivacyDTO.MtCbErrorDetail.class, errorPre);
                log.error("预验券失败，erp订单号：{}，错误代码: {} ,错误消息：{}，",
                        mtCouponReqDTO.getErpOrderId(),
                        mtCbErrorDetail.getCode(),
                        mtCbErrorDetail.getMessage());
                throw new BusinessException("预验券失败");
            }
            String dataPre = mtCbPrivacyDTO.getData();
            MtCouponPreRespDTO mtCouponPreRespDTO = JacksonUtils.toObject(MtCouponPreRespDTO.class, dataPre);
            maxCheckedNum = mtCouponPreRespDTO.getCount() + "";
            minCheckedNum = mtCouponPreRespDTO.getMinConsume() + "";
            int currentNum = mtCouponReqDTO.getCount();
            //当前验券数量，小于等于最大验券数量，且大于等于最小验券数量时，可以正常处理：
            if (null != maxCheckedNum && null != minCheckedNum && currentNum >= Integer.parseInt(minCheckedNum) && currentNum <= Integer.parseInt(maxCheckedNum)) {
                //2.执行验券
                CipCaterCouponConsumeRequest requestDo = buildCipCaterCouponConsumeRequest(requestSysParams,mtCouponReqDTO);
                resultDo = requestDo.doRequest();
                log.info("验券返回：{}", resultDo);
                mtCouponDoCheckRespDTO = JacksonUtils.toObject(MtCouponDoCheckRespDTO.class, resultDo);
                int errorDo = mtCouponDoCheckRespDTO.getResult();
                if (errorDo != 0) {
                    log.error("验证券失败，错误代码：{}，错误原因：{}", mtCouponDoCheckRespDTO.getResult(), mtCouponDoCheckRespDTO.getMessage());
                    throw new BusinessException("验证券失败");
                }
            } else {
                log.info("超过可验券数量范围。实际可验券：{}-{}个，申请验券：{}个", minCheckedNum, maxCheckedNum, currentNum);
                throw new BusinessException("超过可验券数量范围");
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new BusinessException("验券异常");
        }
        log.info("结束checkTicket方法");
        return mtCouponDoCheckRespDTO;
    }

    public static CipCaterCouponConsumeRequest buildCipCaterCouponConsumeRequest(  RequestSysParams requestSysParams,MtCouponReqDTO mtCouponReqDTO){
        CipCaterCouponConsumeRequest requestDo = new CipCaterCouponConsumeRequest();
        requestDo.setRequestSysParams(requestSysParams);
        requestDo.setCouponCode(mtCouponReqDTO.getCouponCode());
        requestDo.setCount(mtCouponReqDTO.getCount());
        if (mtCouponReqDTO.getErpId().length() >= 32) {
            // only he's
            requestDo.seteId(1000000000000000000L);
        } else {
            requestDo.seteId(Long.parseLong(mtCouponReqDTO.getErpId()));
        }
        if(mtCouponReqDTO.getErpName().length()>32){
            log.info("门店名大于32位，erpName：{}",mtCouponReqDTO.getErpName().substring(0,32));
            requestDo.seteName(mtCouponReqDTO.getErpName().substring(0,32));
        }else{
            log.info("正常门店名，erpName：{}",mtCouponReqDTO.getErpName());
            requestDo.seteName(mtCouponReqDTO.getErpName());
        }
        requestDo.seteOrderId(Long.parseLong(mtCouponReqDTO.getErpOrderId()));
        return requestDo;
    }
}
