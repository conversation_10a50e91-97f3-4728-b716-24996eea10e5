package com.holder.saas.store.takeaway.producers.service.impl;

import com.holder.saas.store.takeaway.producers.service.OwnUnOrderParser;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.takeaway.*;
import com.holderzone.saas.store.dto.takeaway.request.CustomersDTO;
import com.holderzone.saas.store.dto.takeaway.request.SalesOrderConstituteDTO;
import com.holderzone.saas.store.dto.takeaway.request.SalesOrderDTO;
import com.holderzone.saas.store.dto.takeaway.request.SalesOrderGoodsDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 饿了吗订单解析(各种订单状态的处理)
 */
@Slf4j
@Service
public class OwnUnOrderParseImpl implements OwnUnOrderParser {


    /**
     * 订单生效
     *
     * @param salesOrderDTO
     * @return
     */
    @Override
    public UnOrder fromOrderCreated(SalesOrderDTO salesOrderDTO) {
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        CustomersDTO customersDTO = salesOrderDTO.getCustomersDTO();
        UnOrder unOrder = new UnOrder();
        // 标识、其他
        unOrder.setOrderId(String.valueOf(salesOrderDTO.getOrderId()));
        unOrder.setOrderViewId(salesOrderDTO.getSerialNumber());
        unOrder.setOrderDaySn(salesOrderDTO.getTakeAwayNum());
        unOrder.setOrderRemark(salesOrderDTO.getRemark());
        unOrder.setOnlinePay(Boolean.TRUE);
        unOrder.setFirstOrder(false);

        // 预订
        unOrder.setReserve(Boolean.FALSE);

        // 时间
        Optional.ofNullable(salesOrderDTO.getCreateTime())
                .ifPresent(date -> unOrder.setCreateTime(LocalDateTime.parse(date, df)));
        Optional.ofNullable(salesOrderDTO.getCreateTime())
                .ifPresent(date -> unOrder.setActiveTime(LocalDateTime.parse(date, df)));


        //将就餐人数打印出来
        if (salesOrderDTO.getGuestCount() != null) {
            unOrder.setCustomerNumber(salesOrderDTO.getGuestCount());
        } else {
            unOrder.setCustomerNumber(1);
        }

        if (customersDTO != null) {
            unOrder.setCustomerName(customersDTO.getName());
            unOrder.setCustomerAddress(customersDTO.getAddress());

            //顾客真实手机号
            if (!ObjectUtils.isEmpty(customersDTO.getPhone())) {
                List<String> customerPhones = new ArrayList<>();
                customerPhones.add(customersDTO.getPhone());
                unOrder.setCustomerPhone(JacksonUtils.writeValueAsString(customerPhones));
            }

            // 配送
            unOrder.setShipLatitude(String.valueOf(customersDTO.getLatitude()));
            unOrder.setShipLongitude(String.valueOf(customersDTO.getLongitude()));

            //期望送达时间
            //BugFixed:18791 和小程序端确认：原本立即送达应该是传null，但是小程序端业务耦合了。修改代价较高。
            //现在的处理方式：TakeMealTime-createTime的时间间隔小于15分钟，就设置为立即送达。且，小程序端保证TakeMealTime、createTime都不为空
            Optional.ofNullable(customersDTO.getTakeMealTime())
                    .ifPresent(date -> unOrder.setEstimateDeliveredTime(LocalDateTime.parse(date, df)));
            if (!ObjectUtils.isEmpty(customersDTO.getTakeMealTime()) && !ObjectUtils.isEmpty(salesOrderDTO.getCreateTime())) {
                LocalDateTime takeMealTime = LocalDateTime.parse(customersDTO.getTakeMealTime(), df);
                LocalDateTime createTime = LocalDateTime.parse(salesOrderDTO.getCreateTime(), df);
                Duration duration = Duration.between(createTime, takeMealTime);
                long minutes = duration.toMinutes();//相差的分钟数
                if (minutes >= 0 && minutes < 15) {
                    unOrder.setEstimateDeliveredTime(null);
                }
            }
        }
        unOrder.setShipTotal(BigDecimal.ZERO);
        unOrder.setPackageTotal(BigDecimal.ZERO);
        List<SalesOrderConstituteDTO> saleLists = salesOrderDTO.getSalesOrderConstituteDTOs();
        for (SalesOrderConstituteDTO sale : saleLists) {
            //4:为掌控者外卖的配送费
            if (sale.getDiscountType() == 4) {
                //设置配送费
                unOrder.setShipTotal(sale.getDiscountAmount().setScale(2, BigDecimal.ROUND_HALF_UP));
            }
            if (sale.getDiscountType() == 3) {
                //设置餐盒费
                unOrder.setPackageTotal(sale.getDiscountAmount().setScale(2, BigDecimal.ROUND_HALF_UP));
            }
        }

        // 订单金额
        unOrder.setTotal(salesOrderDTO.getGoodsAmount().add(salesOrderDTO.getDeliverAmount()).setScale(2, BigDecimal.ROUND_HALF_UP));
        unOrder.setItemTotal(salesOrderDTO.getGoodsAmount().setScale(2, BigDecimal.ROUND_HALF_UP));
        unOrder.setDiscountTotal(salesOrderDTO.getDiscountAmount().setScale(2, BigDecimal.ROUND_HALF_UP));


        // 店铺实收
        unOrder.setCustomerActualPay(salesOrderDTO.getPayAmount().setScale(2, BigDecimal.ROUND_HALF_UP));
        unOrder.setShopTotal(salesOrderDTO.getPayAmount().setScale(2, BigDecimal.ROUND_HALF_UP));

        // 填充UnOrderDetail
        unOrder.setArrayOfUnItem(parseUnOrderItems(salesOrderDTO));

        // 填充UnOrderDiscount
        unOrder.setArrayOfUnDiscount(parseUnOrderDiscount(salesOrderDTO));

        unOrder.setPlatformDiscount(BigDecimal.ZERO);
        unOrder.setEnterpriseDiscount(BigDecimal.ZERO);

        // todo 订单生效时该OOrderStatus只可能为unprocessed，确认是这个后可以取消以下文字
        log.debug("------------------> orderStatus: {}", salesOrderDTO.getOrderStatus());

        return unOrder;
    }


    private List<UnItem> parseUnOrderItems(SalesOrderDTO salesOrderDTO) {
        List<UnItem> arrayOfUnOrderDetail = new ArrayList<>();
        if (salesOrderDTO == null) {
            return Collections.emptyList();
        }
        if (ObjectUtils.isEmpty(salesOrderDTO.getSalesOrderGoods())) {
            return Collections.emptyList();
        }
        for (SalesOrderGoodsDTO dish : salesOrderDTO.getSalesOrderGoods()) {
            UnItem unOrderDetail = new UnItem();
            unOrderDetail.setItemName(dish.getGoodsName());

            unOrderDetail.setItemSku(dish.getThirdSkuId());
            unOrderDetail.setItemCount(BigDecimal.valueOf(dish.getNumber()).setScale(2, BigDecimal.ROUND_HALF_UP));
            unOrderDetail.setItemPrice(dish.getPrice());
            unOrderDetail.setItemTotal(dish.getAmount());
            unOrderDetail.setItemProperty(dish.getRemark());
            arrayOfUnOrderDetail.add(unOrderDetail);
        }
        return arrayOfUnOrderDetail;
    }


    /**
     * 解析为unOrderDiscount
     *
     * @param salesOrderDTO
     */
    private List<UnDiscount> parseUnOrderDiscount(SalesOrderDTO salesOrderDTO) {
        List<UnDiscount> arrayOfUnDiscount = new ArrayList<>();

        if (salesOrderDTO == null) {
            return Collections.emptyList();
        }

        if (ObjectUtils.isEmpty(salesOrderDTO.getSalesOrderConstituteDTOs())) {
            return Collections.emptyList();
        }


        for (SalesOrderConstituteDTO sale : salesOrderDTO.getSalesOrderConstituteDTOs()) {
            if (sale != null) {
                UnDiscount unDiscount = new UnDiscount();
                unDiscount.setDiscountName(sale.getDiscountName());
                unDiscount.setTotalDiscount(sale.getDiscountAmount());
                arrayOfUnDiscount.add(unDiscount);
            }
        }
        return arrayOfUnDiscount;
    }
}
