/*
 * Copyright (c) 2018-2028 成都掌控者科技有限公司 All Rights Reserved.
 * ProjectName:saas-platform
 * FileName:TcdUnOrderParse.java
 * Date:2020-3-2
 * Author:terry
 */

package com.holder.saas.store.takeaway.producers.service;

import com.holderzone.saas.store.dto.takeaway.UnOrder;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutTcdOrderReqDTO;

/**
 * <AUTHOR>
 * @date 2020-03-02 下午2:28
 */
public interface TcdUnOrderParser {

    UnOrder fromOrderCreate(TakeoutTcdOrderReqDTO req);

    UnOrder fromOrderCreatePersonPending(TakeoutTcdOrderReqDTO req);

    UnOrder fromOrderConfirmed(TakeoutTcdOrderReqDTO req);

    UnOrder fromOrderCanceled(TakeoutTcdOrderReqDTO req);

    UnOrder fromOrderRefunded(TakeoutTcdOrderReqDTO req);

    UnOrder fromOrderShipping(TakeoutTcdOrderReqDTO req);

    UnOrder fromOrderFinished(TakeoutTcdOrderReqDTO req);
}
