package com.holder.saas.store.takeaway.producers.utils.sdk.meituan;

import com.google.common.collect.Maps;
import com.sankuai.sjst.platform.developer.domain.RequestDomain;
import com.sankuai.sjst.platform.developer.domain.RequestMethod;
import com.sankuai.sjst.platform.developer.request.CipCaterStringPairRequest;
import java.util.Map;

/**
 * 拼好饭菜品映射
 */
public class CipCaterTakeoutSpecialFoodBindRequest extends CipCaterStringPairRequest {

    private String ePoiId;

    private String eDishCode;

    private String mtSpuId;

    private String skuId;

    private String mtSkuId;

    public CipCaterTakeoutSpecialFoodBindRequest() {
        this.url = RequestDomain.preUrl.getValue() + "/waimai/ng/special/food/bindSpuAndSkuCode";
        this.requestMethod = RequestMethod.POST;
    }

    public String getePoiId() {
        return ePoiId;
    }

    public void setePoiId(String ePoiId) {
        this.ePoiId = ePoiId;
    }

    public String geteDishCode() {

        return eDishCode;
    }

    public void seteDishCode(String eDishCode) {
        this.eDishCode = eDishCode;
    }

    public String getMtSpuId() {
        return mtSpuId;
    }

    public void setMtSpuId(String mtSpuId) {
        this.mtSpuId = mtSpuId;
    }

    public String getSkuId() {
        return skuId;
    }

    public void setSkuId(String skuId) {
        this.skuId = skuId;
    }

    public String getMtSkuId() {
        return mtSkuId;
    }

    public void setMtSkuId(String mtSkuId) {
        this.mtSkuId = mtSkuId;
    }

    @Override
    public Map<String, String> getParams() {
        Map<String, String> map = Maps.newHashMap();
        map.put("ePoiId", CipCaterTakeoutSpecialFoodBindRequest.this.ePoiId);
        map.put("businessIdentify", "1");
        map.put("eDishCode", CipCaterTakeoutSpecialFoodBindRequest.this.eDishCode);
        map.put("mtSpuId", CipCaterTakeoutSpecialFoodBindRequest.this.mtSpuId);
        map.put("skuId", CipCaterTakeoutSpecialFoodBindRequest.this.skuId);
        map.put("mtSkuId", CipCaterTakeoutSpecialFoodBindRequest.this.mtSkuId);
        return map;
    }
}
