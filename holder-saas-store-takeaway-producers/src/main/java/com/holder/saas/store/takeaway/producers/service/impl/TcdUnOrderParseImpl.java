/*
 * Copyright (c) 2018-2028 成都掌控者科技有限公司 All Rights Reserved.
 * ProjectName:saas-platform
 * FileName:TcdUnOrderParseImpl.java
 * Date:2020-3-2
 * Author:terry
 */

package com.holder.saas.store.takeaway.producers.service.impl;

import com.holder.saas.store.takeaway.producers.service.TcdUnOrderParser;
import com.holder.saas.store.takeaway.producers.utils.BigDecimalUtil;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.takeaway.*;
import com.holderzone.saas.store.dto.takeaway.request.SelfSupportDishPropertyTemp;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutTcdOrderReqDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020-03-02 下午2:29
 */
@Slf4j
@Service
public class TcdUnOrderParseImpl implements TcdUnOrderParser {

    private static final String DELIVERY_NOW = "立即送餐";

    private static final String PREPARE_NOW = "立即备餐";


    private static void parseDeliveredAndRemark(String orderSend, UnOrder unOrder) {
        try {
            if (!StringUtils.hasText(orderSend) || orderSend.contains(DELIVERY_NOW)) {
                // 今天、立即送餐：立即送餐
                unOrder.setReserve(false);
                unOrder.setEstimateDeliveredTime(DateTimeUtils.mills2LocalDateTime(0L));
            } else if (orderSend.contains(PREPARE_NOW)) {
                // 今天、预订：今天（3月-3日）立即备餐
                unOrder.setReserve(true);
                if (!StringUtils.hasText(unOrder.getOrderRemark())) {
                    unOrder.setOrderRemark("到店取餐");
                } else {
                    unOrder.setOrderRemark(unOrder.getOrderRemark() + "，到店取餐");
                }
                LocalDate date = LocalDate.now();
                if (orderSend.contains("月") && orderSend.contains("日")) {
                    String m = orderSend.substring(Math.max(0, orderSend.indexOf("（") + 1), orderSend.indexOf("月"));
                    String d = orderSend.substring(Math.max(0, orderSend.indexOf("-") + 1), orderSend.indexOf("日"));
                    date = LocalDate.of(date.getYear(), Integer.parseInt(m.trim()), Integer.parseInt(d.trim()));
                }
                String h = orderSend.substring(Math.max(0, orderSend.indexOf(":") - 2), orderSend.length());
                try {
                    LocalTime hour = LocalTime.parse(h.trim(), DateTimeFormatter.ofPattern("HH:mm"));
                    unOrder.setEstimateDeliveredTime(LocalDateTime.of(date, hour));
                    return;
                } catch (Exception e) {
                    log.warn("解析 {} 出现异常：", "HH:mm", e);
                }
                try {
                    DateTimeFormatter pattern = DateTimeFormatter.ofPattern(DateTimeUtils.PATTERN_SECONDS);
                    unOrder.setEstimateDeliveredTime(LocalDateTime.parse(orderSend, pattern));
                    return;
                } catch (Exception e) {
                    log.warn("解析 {} 出现异常：", DateTimeUtils.PATTERN_SECONDS, e);
                }
                unOrder.setEstimateDeliveredTime(DateTimeUtils.mills2LocalDateTime(0L));
            } else {
                unOrder.setReserve(true);
                LocalDate date = LocalDate.now();
                if (orderSend.contains("月") && orderSend.contains("日")) {
                    String m = orderSend.substring(Math.max(0, orderSend.indexOf("（") + 1), orderSend.indexOf("月"));
                    String d = orderSend.substring(Math.max(0, orderSend.indexOf("-") + 1), orderSend.indexOf("日"));
                    date = LocalDate.of(date.getYear(), Integer.parseInt(m.trim()), Integer.parseInt(d.trim()));
                }
                String h = orderSend.substring(Math.max(0, orderSend.indexOf("）") + 1), orderSend.lastIndexOf("-"));
                try {
                    LocalTime hour = LocalTime.parse(h.trim(), DateTimeFormatter.ofPattern("HH:mm"));
                    unOrder.setEstimateDeliveredTime(LocalDateTime.of(date, hour));
                    return;
                } catch (Exception e) {
                    log.warn("解析 {} 出现异常：", "HH:mm", e);
                }
                try {
                    DateTimeFormatter pattern = DateTimeFormatter.ofPattern(DateTimeUtils.PATTERN_SECONDS);
                    unOrder.setEstimateDeliveredTime(LocalDateTime.parse(orderSend, pattern));
                    return;
                } catch (Exception e) {
                    log.warn("解析 {} 出现异常：", DateTimeUtils.PATTERN_SECONDS, e);
                }
                unOrder.setEstimateDeliveredTime(DateTimeUtils.mills2LocalDateTime(0L));
            }
        } catch (Exception e) {
            log.error("解析orderSend发生错误：", e);
            unOrder.setReserve(false);
            unOrder.setEstimateDeliveredTime(DateTimeUtils.mills2LocalDateTime(0L));
        }
    }

    @Override
    public UnOrder fromOrderCreate(TakeoutTcdOrderReqDTO req) {
        log.info("(赚餐)新订单，orderId: {}", req.getOrderSn());
        UnOrder unOrder = parseToUnOrder(req);
        unOrder.setCreateTime(LocalDateTime.now());
        unOrder.setActiveTime(LocalDateTime.now());
        unOrder.setCbMsgType(UnOrderCbMsgType.ORDER_ACTIVATED);
        unOrder.setOrderStatusTcd("PENDING");
        if (!StringUtils.isEmpty(req.getOrderType())) {
            unOrder.setOrderTypeTcd(req.getOrderType());
        }
        if (!StringUtils.isEmpty(req.getWriteOffCode())) {
            unOrder.setWriteOffCode(req.getWriteOffCode());
        }
        unOrder.setDiscountTotal(req.getDiscountFee());
        unOrder.setEnterpriseDiscount(req.getDiscountFee());
        return unOrder;
    }

    @Override
    public UnOrder fromOrderCreatePersonPending(TakeoutTcdOrderReqDTO req) {
        log.info("(赚餐)出餐订单，orderId: {}", req.getOrderSn());
        UnOrder unOrder = parseToUnOrder(req);
        unOrder.setCreateTime(LocalDateTime.now());
        unOrder.setActiveTime(LocalDateTime.now());
        unOrder.setCbMsgType(UnOrderCbMsgType.ORDER_DINING_OUT_TCD);
        unOrder.setOrderStatusTcd("PERSON_PENDING");
        if (!StringUtils.isEmpty(req.getOrderType()) && StringUtils.isEmpty(unOrder.getOrderTypeTcd())) {
            unOrder.setOrderTypeTcd(req.getOrderType());
        }
        unOrder.setDiscountTotal(req.getDiscountFee());
        unOrder.setEnterpriseDiscount(req.getDiscountFee());
        return unOrder;
    }

    @Override
    public UnOrder fromOrderConfirmed(TakeoutTcdOrderReqDTO req) {
        log.info("(赚餐)订单已确认，orderId: {}", req.getOrderSn());
//        UnOrder unOrder = parseErpInfo(req);
        UnOrder unOrder = parseToUnOrder(req);
        if (!StringUtils.isEmpty(req.getWriteOffCode())) {
            unOrder.setWriteOffCode(req.getWriteOffCode());
        }
        unOrder.setOrderStatusTcd("COOKING");
        if (!StringUtils.isEmpty(req.getOrderType()) && StringUtils.isEmpty(unOrder.getOrderTypeTcd())) {
            unOrder.setOrderTypeTcd(req.getOrderType());
        }
        unOrder.setCreateTime(LocalDateTime.now());
        unOrder.setActiveTime(LocalDateTime.now());
        unOrder.setAcceptTime(DateTimeUtils.now());
        unOrder.setCbMsgType(UnOrderCbMsgType.ORDER_CONFIRMED);
        return unOrder;
    }

    @Override
    public UnOrder fromOrderCanceled(TakeoutTcdOrderReqDTO takeoutTcdOrderReqDTO) {
        log.info("(赚餐)订单已取消，orderId: {}", takeoutTcdOrderReqDTO.getOrderSn());
        UnOrder unOrder = parseErpInfo(takeoutTcdOrderReqDTO);
        unOrder.setCancelTime(DateTimeUtils.now());
        if (Integer.valueOf(1).equals(takeoutTcdOrderReqDTO.getWhoRefunds())) {
            unOrder.setCancelRoleName("用户");
            unOrder.setCancelReason(Optional.ofNullable(takeoutTcdOrderReqDTO.getRequestRefundsReason()).orElse("用户取消订单"));
        } else {
            unOrder.setCancelRoleName("商户");
            unOrder.setCancelReason("商户取消订单");
        }
        if (!StringUtils.isEmpty(takeoutTcdOrderReqDTO.getOrderType()) && StringUtils.isEmpty(unOrder.getOrderTypeTcd())) {
            unOrder.setOrderTypeTcd(takeoutTcdOrderReqDTO.getOrderType());
        }
        unOrder.setCbMsgType(UnOrderCbMsgType.ORDER_CANCELED);
        unOrder.setOrderStatusTcd("CANCELLED");
        return unOrder;
    }


    @Override
    public UnOrder fromOrderRefunded(TakeoutTcdOrderReqDTO req) {
        UnOrder unOrder = parseErpInfo(req);
        unOrder.setRefundReqTime(DateTimeUtils.now());
        unOrder.setRefundReqReason(req.getRequestRefundsReason());
        log.info("(赚餐)退款，orderId: {}", req.getOrderSn());
        unOrder.setPart(false);
        if (!StringUtils.isEmpty(req.getOrderType()) && StringUtils.isEmpty(unOrder.getOrderTypeTcd())) {
            unOrder.setOrderTypeTcd(req.getOrderType());
        }
        unOrder.setOrderStatusTcd("REFUND");
        unOrder.setCustomerRefund(req.getAmount());
        unOrder.setCbMsgType(UnOrderCbMsgType.ORDER_REFUND_REQ);
        return unOrder;
    }

    @Override
    public UnOrder fromOrderShipping(TakeoutTcdOrderReqDTO req) {
        log.info("(赚餐)订单配送中，orderId: {}", req.getOrderSn());
        UnOrder unOrder = parseErpInfo(req);
        unOrder.setOrderStatusTcd("DISTRIBUTION");
        if (!StringUtils.isEmpty(req.getOrderType()) && StringUtils.isEmpty(unOrder.getOrderTypeTcd())) {
            unOrder.setOrderTypeTcd(req.getOrderType());
        }
        unOrder.setDeliveryTime(DateTimeUtils.now());
        unOrder.setShipperName(req.getPersonName());
        unOrder.setShipperPhone(req.getPersonPhone());
        unOrder.setCbMsgType(UnOrderCbMsgType.ORDER_SHIPPING);
        return unOrder;
    }

    @Override
    public UnOrder fromOrderFinished(TakeoutTcdOrderReqDTO req) {
        log.info("(赚餐)订单已完成，orderId: {}", req.getOrderSn());
        UnOrder unOrder = parseErpInfo(req);
        unOrder.setOrderStatusTcd("FINISH");
        if (!StringUtils.isEmpty(req.getOrderType()) && StringUtils.isEmpty(unOrder.getOrderTypeTcd())) {
            unOrder.setOrderTypeTcd(req.getOrderType());
        }
        unOrder.setCompleteTime(DateTimeUtils.now());
        unOrder.setCbMsgType(UnOrderCbMsgType.ORDER_FINISHED);
        return unOrder;
    }

    private UnOrder parseErpInfo(TakeoutTcdOrderReqDTO req) {
        UnOrder unOrder = new UnOrder();
        unOrder.setShopId(req.getMerchantId());
        unOrder.setShopName(req.getStoreName());
        unOrder.setOrderId(req.getOrderSn());
        unOrder.setOrderViewId(req.getOrderSn());
        unOrder.setOrderDaySn(String.valueOf(req.getDayCount()));
        unOrder.setEnterpriseGuid(req.getEnterpriseGuid());
        unOrder.setStoreGuid(req.getStoreGuid());
        unOrder.setStoreName(req.getStoreName());
        unOrder.setPrint(req.getPrint());
        unOrder.setOrderType(OrderType.TAKEOUT_ORDER.getType());
        unOrder.setOrderSubType(OrderType.TakeoutSubType.TCD_TAKEOUT.getType());
        return unOrder;
    }

    private UnOrder parseToUnOrder(TakeoutTcdOrderReqDTO req) {
        UnOrder unOrder = parseErpInfo(req);

        TakeoutTcdOrderReqDTO.OrderExtended orderExtended = req.getOrderExtended();

        // 标识、其他
        unOrder.setOrderRemark(orderExtended.getOrderMark());
        unOrder.setOnlinePay(true);
        unOrder.setFirstOrder(true);

        // 顾客，默认人数为1
        unOrder.setCustomerName(orderExtended.getCustomerName());
        unOrder.setCustomerAddress(orderExtended.getCustomerAddress());
        // 顾客真实手机号
        unOrder.setCustomerPhone(JacksonUtils.writeValueAsString(Collections.singleton(orderExtended.getCustomerPhone())));
        // 顾客隐私号
        unOrder.setPrivacyPhone(null);
        unOrder.setCustomerNumber(1);

        // 配送
        String coordinate = orderExtended.getCustomerCoordinate();
        if (coordinate != null) {
            String[] split = coordinate.split(",");
            if (split.length >= 2) {
                unOrder.setShipLatitude(split[1]);
                unOrder.setShipLongitude(split[0]);
            }
        }
        // 解析配送时间、预订单和自提备注
        parseDeliveredAndRemark(orderExtended.getOrderSend(), unOrder);

        // 发票
        unOrder.setInvoiced(false);
        unOrder.setInvoiceTitle(null);
        unOrder.setTaxpayerId(null);
        unOrder.setInvoiceType(0);

        // 订单原始价格=餐盒费+配送费+菜品费用
        unOrder.setTotal(req.getTotalAmount());
        unOrder.setPackageTotal(req.getPackFee());
        unOrder.setShipTotal(req.getShippingFee());
        unOrder.setItemTotal(req.getProductFee());

        // 折扣合计、商家承担的折扣部分、外卖平台承担的折扣部分
        BigDecimal reductionFee = req.getFullReduction() != null
                ? req.getFullReduction() : BigDecimal.ZERO;
        BigDecimal couponFee = req.getCouponFee() != null
                ? req.getCouponFee() : BigDecimal.ZERO;
        unOrder.setDiscountTotal(reductionFee.add(couponFee));
        unOrder.setEnterpriseDiscount(unOrder.getDiscountTotal());
        unOrder.setPlatformDiscount(BigDecimal.ZERO);
        unOrder.setOtherDiscount(req.getOtherDiscount());

        // 服务费(平台抽成费用)、服务费率(平台抽成比例)
        unOrder.setServiceFeeRate(BigDecimal.ZERO);
        unOrder.setServiceFee(BigDecimal.ZERO);

        // 店铺实收
        unOrder.setShopTotal(req.getAmount());

        // 第三方配送
        unOrder.setCustomerActualPay(req.getAmount());
        unOrder.setThirdShipper(false);

        // 填充UnOrderDetail
        unOrder.setArrayOfUnItem(parseUnOrderItems(req));

        // 填充UnOrderDiscount
        unOrder.setArrayOfUnDiscount(parseUnOrderDiscount(req));

        //填充是否打印print
        unOrder.setPrint(req.getPrint());

        return unOrder;
    }

    private List<UnDiscount> parseUnOrderDiscount(TakeoutTcdOrderReqDTO req) {
        TakeoutTcdOrderReqDTO.ReduceActivity reduceActivity = req.getReduceActivity();
        if (reduceActivity == null) {
            return null;
        }
        List<TakeoutTcdOrderReqDTO.Reduction> reductionList = reduceActivity.getReductionList();
        if (reductionList == null) {
            return null;
        }
        List<UnDiscount> arrayOfUnDiscount = new ArrayList<>();
        for (int i = 0; i < reductionList.size(); i++) {
            TakeoutTcdOrderReqDTO.Reduction reduction = reductionList.get(i);
            UnDiscount unDiscount = new UnDiscount();
            unDiscount.setDiscountName("赚餐满减" + (i + 1));
            unDiscount.setDiscountRemark("赚餐满减" + (i + 1));
            unDiscount.setTotalDiscount(reduction.getCutMoney());
            unDiscount.setEnterpriseDiscount(reduction.getCutMoney());
            unDiscount.setPlatformDiscount(BigDecimal.ZERO);
            unDiscount.setOtherDiscount(BigDecimal.ZERO);
            arrayOfUnDiscount.add(unDiscount);
        }
        return arrayOfUnDiscount;
    }

    private List<UnItem> parseUnOrderItems(TakeoutTcdOrderReqDTO req) {
        if (req.getOrderProductList() == null) {
            return null;
        }
        return req.getOrderProductList().stream()
                .map(item -> {
                    TakeoutTcdOrderReqDTO.ProductSpec spec = item.getSpec();
                    UnItem unItem = new UnItem();
                    unItem.setItemSku(spec.getSkuGuid());
                    unItem.setItemCode(null);
                    if (StringUtils.hasText(spec.getSkuName())) {
                        unItem.setItemName(item.getItemName() + "(" + spec.getSkuName() + ")");
                    } else if (StringUtils.hasText(spec.getName())) {
                        unItem.setItemName(item.getItemName() + "(" + spec.getName() + ")");
                    } else {
                        unItem.setItemName(item.getItemName());
                    }
                    unItem.setItemUnit(spec.getUnit());
                    unItem.setItemPrice(spec.getPrice());
                    unItem.setItemCount(BigDecimal.valueOf(item.getProductNum()));
                    unItem.setItemTotal(BigDecimalUtil.nonNullValue(item.getProductPrice()).multiply(BigDecimal.valueOf(item.getProductNum())));
                    unItem.setItemSpec(spec.getName());
                    unItem.setItemProperty(null);
                    if (!ObjectUtils.isEmpty(item.getSelfSupportDishPropertyTempList())) {
                        List<String> itemPropertys = item.getSelfSupportDishPropertyTempList()
                                .stream().map(SelfSupportDishPropertyTemp::getDetails).collect(Collectors.toList());
                        if (!ObjectUtils.isEmpty(itemPropertys)) {
                            unItem.setItemProperty(org.apache.commons.lang3.StringUtils.join(itemPropertys, ","));
                        }
                    }
                    unItem.setBoxPrice(spec.getBoxPrice());
                    if (spec.getPackNum() != null) {
                        unItem.setBoxCount(BigDecimal.valueOf(spec.getPackNum()));
                    }
                    unItem.setBoxTotal(spec.getPackPrice());
                    unItem.setCartId(0);
                    unItem.setDiscountPrice(BigDecimal.ZERO);
                    unItem.setDiscountRatio(BigDecimal.ZERO);
                    unItem.setDiscountTotal(BigDecimal.ZERO);
                    unItem.setSettleType(0);
                    unItem.setUnItemSkuId(item.getSkuId());
                    unItem.setThirdSkuId(item.getSkuId());
                    unItem.setActualPrice(Objects.nonNull(item.getMemberPrice()) ? item.getMemberPrice().multiply(new BigDecimal(item.getProductNum())) : null);
                    return unItem;
                })
                .collect(Collectors.toList());
    }
}
