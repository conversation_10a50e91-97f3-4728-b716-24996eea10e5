package com.holder.saas.store.takeaway.producers.service.impl;

import com.holder.saas.store.takeaway.producers.config.RocketMqConfig;
import com.holder.saas.store.takeaway.producers.service.StarDeliveryService;
import com.holder.saas.store.takeaway.producers.service.UnOrderDeliveryService;
import com.holder.saas.store.takeaway.producers.service.UnOrderMqService;
import com.holderzone.framework.rocketmq.common.DefaultRocketMqProducer;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.takeaway.OwnApiResult;
import com.holderzone.saas.store.dto.takeaway.UnOrder;
import com.holderzone.saas.store.dto.takeaway.UnOrderCbMsgType;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class StarDeliveryServiceImpl implements StarDeliveryService {

    private final DefaultRocketMqProducer defaultRocketMqProducer;

    private final UnOrderMqService unOrderMqService;


    @Autowired
    public StarDeliveryServiceImpl(DefaultRocketMqProducer defaultRocketMqProducer, UnOrderMqService unOrderMqService) {
        this.defaultRocketMqProducer = defaultRocketMqProducer;
        this.unOrderMqService = unOrderMqService;

    }

    @Override
    public void starDelivery(UnOrderDeliveryService unOrderDeliveryService, UnOrder unOrder) {
        OwnApiResult ownApiResult = unOrderDeliveryService.startDeliveryMQ(unOrder);
        if (ownApiResult.getCode() == 0) {
            return;
        }
        if (this.judgeDeliveryRetry(unOrderDeliveryService, unOrder, true)) {
            return;
        }

        //修改订单状态为异常单
        unOrder.setCbMsgType(UnOrderCbMsgType.ORDER_DELIVERY_ERROR);
        unOrder.setCancelReason("发起一城飞客配送异常，请主动联系一城飞客人员发配送单");
        unOrder.setCancelTime(DateTimeUtils.now());
        unOrder.setCancelReqTime(DateTimeUtils.now());
        unOrder.setRefundReqTime(DateTimeUtils.now());
        unOrderMqService.sendUnOrder(unOrder);

    }

    @Override
    public boolean judgeDeliveryRetry(UnOrderDeliveryService unOrderDeliveryService, UnOrder unOrder, Boolean isRetry) {
        if (unOrder.getCurRetryTime() <= unOrder.getMaxRetryTimes() && isRetry) {
            unOrder.setCurRetryTime(unOrder.getCurRetryTime() + 1);
            int delayTimeLevel = !unOrder.getIsEnableBackOff()
                    ? unOrder.getDelayTimeLevel()
                    : unOrder.getDelayTimeLevel() + 2;
            unOrder.setDelayTimeLevel(delayTimeLevel);
            Message message = new Message(
                    RocketMqConfig.DISTRIBUTION_MESSAGE_TOPIC,
                    RocketMqConfig.DISTRIBUTION_START_TAG,
                    JacksonUtils.toJsonByte(unOrder)
            );
            message.setDelayTimeLevel(delayTimeLevel);
            defaultRocketMqProducer.sendMessage(message);
            return true;
        }
        return false;
    }
}


