package com.holder.saas.store.takeaway.producers.event;


import com.holder.saas.store.takeaway.producers.config.RocketMqConfig;
import com.holder.saas.store.takeaway.producers.service.StarDeliveryService;
import com.holder.saas.store.takeaway.producers.service.UnOrderDeliveryService;
import com.holder.saas.store.takeaway.producers.utils.ThrowableExtUtils;
import com.holderzone.framework.rocketmq.anno.RocketListenerHandler;
import com.holderzone.framework.rocketmq.common.AbstractRocketMqConsumer;
import com.holderzone.framework.rocketmq.constants.RocketMqTopic;
import com.holderzone.saas.store.dto.takeaway.UnOrder;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RocketListenerHandler(
        topic = RocketMqConfig.DISTRIBUTION_MESSAGE_TOPIC,
        tags = RocketMqConfig.DISTRIBUTION_START_TAG,
        consumerGroup = RocketMqConfig.DISTRIBUTION_MESSAGE_GROUP)
public class DistributionListener extends AbstractRocketMqConsumer<RocketMqTopic, UnOrder> {

    private final UnOrderDeliveryService mtOrderReplyService;

    private final UnOrderDeliveryService eleOrderReplyService;

    private final UnOrderDeliveryService ownOrderReplyService;

    private final UnOrderDeliveryService tcdOrderReplyServiceImpl;

    private final StarDeliveryService starDeliveryService;

    @Autowired
    public DistributionListener(@Qualifier("mtOrderReplyServiceImpl") UnOrderDeliveryService mtOrderReplyService,
                                @Qualifier("eleOrderReplyServiceImpl") UnOrderDeliveryService eleOrderReplyService,
                                @Qualifier("ownOrderReplyServiceImpl") UnOrderDeliveryService ownOrderReplyService,
                                @Qualifier("tcdOrderReplyServiceImpl") UnOrderDeliveryService tcdOrderReplyServiceImpl,
                                StarDeliveryService starDeliveryService) {

        this.mtOrderReplyService = mtOrderReplyService;
        this.eleOrderReplyService = eleOrderReplyService;
        this.ownOrderReplyService = ownOrderReplyService;
        this.tcdOrderReplyServiceImpl = tcdOrderReplyServiceImpl;
        this.starDeliveryService = starDeliveryService;
    }


    @Override
    public boolean consumeMsg(UnOrder unOrder, MessageExt messageExt) {
        String orderId = unOrder.getOrderId();
        try {
            UnOrderDeliveryService unOrderDeliveryService = create(unOrder);
            starDeliveryService.starDelivery(unOrderDeliveryService, unOrder);
        } catch (Exception e) {
            log.error("订单号[{}]打印任务重试消息消费异常，throwable={}", orderId, ThrowableExtUtils.asStringIfAbsent(e));
        } finally {

        }
        return true;
    }

    /**
     * 根据平台的类型选择对应的实现类(0, "美团外卖"  1, "饿了么外卖"  ...)
     *
     * @param
     * @return
     */
    public UnOrderDeliveryService create(UnOrder unOrder) {
        switch (unOrder.getOrderSubType()) {
            case 0:
                return mtOrderReplyService;
            case 1:
                return eleOrderReplyService;
            case 5:
                return ownOrderReplyService;
            case 6:
                return tcdOrderReplyServiceImpl;
            default:
                throw new UnsupportedOperationException("暂不支持除美团、饿了么以外的外卖业务[]");
        }
    }

}
