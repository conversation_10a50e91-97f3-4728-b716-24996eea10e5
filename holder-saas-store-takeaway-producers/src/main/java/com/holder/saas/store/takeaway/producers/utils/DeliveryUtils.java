package com.holder.saas.store.takeaway.producers.utils;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.takeaway.OrderType;
import com.holderzone.saas.store.dto.takeaway.OwnApiResult;
import com.holderzone.saas.store.dto.takeaway.UnOrder;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutDistributionRequest;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutOwnOrderCancelDTO;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutOwnOrderDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Slf4j
public class DeliveryUtils {



    public static OwnApiResult startDelivery(UnOrder unOrder,String platformName,String deliveryUrl){
        String orderId = unOrder.getOrderId();
        try {
            log.info("({})订单[{}]进入发起一城飞客配送方法：orderId={},storeGuid={},shopName={},reserve={}," +
                            "estimateDeliveredTime={},orderDaySn={}," +
                            "packageTotal={},shipTotal={},invoiced={}," +
                            "invoiceType={},taxpayerId={},shipLatitude={}," +
                            "shipLongitude={},customerPhone={},privacyPhone={}",
                    platformName, orderId, orderId, unOrder.getStoreGuid(), unOrder.getShopName(), unOrder.getReserve(),
                    unOrder.getEstimateDeliveredTime(), unOrder.getOrderDaySn(),
                    unOrder.getPackageTotal(), unOrder.getShipTotal(), unOrder.getInvoiced(),
                    unOrder.getInvoiceType(), unOrder.getTaxpayerId(), unOrder.getShipLatitude(),
                    unOrder.getShipLongitude(), unOrder.getCustomerPhone(), unOrder.getPrivacyPhone()
            );
            TakeoutDistributionRequest takeoutDistributionRequest = new TakeoutDistributionRequest();
            takeoutDistributionRequest.setPlatform(3);
            TakeoutOwnOrderDTO takeoutOwnOrderDTO = new TakeoutOwnOrderDTO();
            takeoutOwnOrderDTO.setShopNo(unOrder.getStoreGuid());
            takeoutOwnOrderDTO.setShopName(unOrder.getShopName());

            if (unOrder.getReserve()) {
                DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                String reachTime = df.format(unOrder.getEstimateDeliveredTime());
                takeoutOwnOrderDTO.setReachTime(reachTime);
            } else {
                takeoutOwnOrderDTO.setReachTime("");
            }

            takeoutOwnOrderDTO.setDayIndex(Integer.valueOf(unOrder.getOrderDaySn()));
            takeoutOwnOrderDTO.setPickType(0);
            takeoutOwnOrderDTO.setBoxFee(unOrder.getPackageTotal().doubleValue());
            if (unOrder.getDiscountTotal() != null && BigDecimalUtil.greaterThanZero(unOrder.getDiscountTotal())) {
                takeoutOwnOrderDTO.setActivityMoney(unOrder.getDiscountTotal().doubleValue());
            } else {
                takeoutOwnOrderDTO.setActivityMoney(0);
            }
            if (unOrder.getEnterpriseDiscount() != null && BigDecimalUtil.greaterThanZero(unOrder.getEnterpriseDiscount())) {
                takeoutOwnOrderDTO.setShopActivityMoney(unOrder.getEnterpriseDiscount().doubleValue());
            } else {
                takeoutOwnOrderDTO.setShopActivityMoney(0);
            }
            if (unOrder.getInvoiced()) {
                if (unOrder.getInvoiceType() == null) {
                    takeoutOwnOrderDTO.setInvoiceType(0);
                } else if (unOrder.getInvoiceType() == -1) {
                    takeoutOwnOrderDTO.setInvoiceType(0);
                } else {
                    takeoutOwnOrderDTO.setInvoiceType(unOrder.getInvoiceType() + 1);
                    takeoutOwnOrderDTO.setInvoiceTitle(unOrder.getInvoiceTitle());
                    takeoutOwnOrderDTO.setTaxpayerId(unOrder.getTaxpayerId());
                }
            } else {
                takeoutOwnOrderDTO.setInvoiceType(0);
            }

            takeoutOwnOrderDTO.setOriginId(unOrder.getOrderId());
            if (!ObjectUtils.isEmpty(unOrder.getCreateTime())) {
                takeoutOwnOrderDTO.setOrderTime(TimeUtil.getTimestampOfLocalDateTime(unOrder.getCreateTime()));
            }
            if (!ObjectUtils.isEmpty(unOrder.getEstimateDeliveredTime())) {
                takeoutOwnOrderDTO.setExpectDeliveryTime(TimeUtil.getTimestampOfLocalDateTime(unOrder.getEstimateDeliveredTime()));
            }
            takeoutOwnOrderDTO.setCargoPrice(unOrder.getTotal());
            takeoutOwnOrderDTO.setOrderWeight(0);
            takeoutOwnOrderDTO.setOrderRemark(unOrder.getOrderRemark());
            takeoutOwnOrderDTO.setIsPrepay(0);
            takeoutOwnOrderDTO.setRequirePayment(0);
            takeoutOwnOrderDTO.setReceiverName(unOrder.getCustomerName());
            String customerAddress = unOrder.getCustomerAddress();
            if(unOrder.getOrderSubType()==OrderType.TakeoutSubType.MT_TAKEOUT.getType()) {
                //氧气公寓 (309)@#四川省成都市武侯区紫荆北路紫荆北路12号汉庭酒店(紫荆店)
                //中国石油棕树加油站 (长寿路1号)@#四川省成都市武侯区科华中路科华中路186号中华保险(四川分公司)

                /**
                 * BugFixed：美团的地址是 XXX@#YYY
                 * XXX：真实地址
                 * YYY：根据经纬度反查的地址
                 * 根据一城飞客的工作人员反馈，以氧气公寓为例：
                 * 氧气公寓 (309)@#四川省成都市武侯区紫荆北路紫荆北路12号汉庭酒店(紫荆店)
                 * 骑士在以【四川省成都市武侯区紫荆北路紫荆北路12号汉庭酒店(紫荆店)】为配送地址的时候，会送错，真实的送货地址应该是【氧气公寓 (309)】
                 * 并且在询问了美团的工作人员之后，可以用XXX也就是【氧气公寓 (309)】作为真实的配送地址。
                 * 故而做了以下修改：判断在包含@#的情况下，用XXX作为一城飞客骑手的真实送货地址。
                 */

                if (!StringUtils.isEmpty(unOrder.getCustomerAddress())) {
                    log.info("地址：{}", unOrder.getCustomerAddress());
                    if (unOrder.getCustomerAddress().contains("@#")) {
                        log.info("地址截取前：{}", unOrder.getCustomerAddress());
                        customerAddress = unOrder.getCustomerAddress().substring(0, unOrder.getCustomerAddress().indexOf("@#"));
                        log.info("地址截取后：{}", customerAddress);
                    } else {
                        log.info("地址未截取：{}", customerAddress);
                    }
                }
            }
            takeoutOwnOrderDTO.setReceiverAddress(customerAddress);
            takeoutOwnOrderDTO.setReceiverLat(unOrder.getShipLatitude());
            takeoutOwnOrderDTO.setReceiverLng(unOrder.getShipLongitude());
            if(unOrder.getOrderSubType()==OrderType.TakeoutSubType.TCD_TAKEOUT.getType()){
                takeoutOwnOrderDTO.setReceiverPhone(unOrder.getCustomerPhone());
            }else {
                String json = unOrder.getCustomerPhone();
                if(unOrder.getOrderSubType()==OrderType.TakeoutSubType.ELE_TAKEOUT.getType()){
                    if(!StringUtils.isEmpty(unOrder.getPrivacyPhone())){
                        json=unOrder.getPrivacyPhone();
                    }
                }
                if (!StringUtils.isEmpty(json)) {
                    List<String> privacyLists = JacksonUtils.toObjectList(String.class, json);
                    if (!ObjectUtils.isEmpty(privacyLists) && !StringUtils.isEmpty(privacyLists.get(0))) {
                        String privacyPhone = privacyLists.get(0);
                        privacyPhone = privacyPhone.replaceAll("&", ",");
                        takeoutOwnOrderDTO.setReceiverPhone(privacyPhone);
                    }
                }
            }

            if (unOrder.getTotal() == null) {
                unOrder.setTotal(BigDecimal.ZERO);
            }
            if (ObjectUtils.isEmpty(unOrder.getFoodLists())) {
                log.info("({})订单[{}]发起一城飞客配送,菜品为空", platformName, orderId);
            }
            takeoutOwnOrderDTO.setGoodsPrice(unOrder.getTotal().doubleValue());
            takeoutOwnOrderDTO.setStoreCode(unOrder.getStoreGuid());
            takeoutOwnOrderDTO.setFoodList(unOrder.getFoodLists());
            if (unOrder.getShipTotal() == null) {
                takeoutOwnOrderDTO.setFreight(0);
            } else {
                takeoutOwnOrderDTO.setFreight(unOrder.getShipTotal().doubleValue());
            }

            takeoutOwnOrderDTO.setFlag(platformName);
            takeoutDistributionRequest.setRequestJsonData(takeoutOwnOrderDTO);
            String jsonParam = JacksonUtils.writeValueAsString(takeoutDistributionRequest);
            log.info("({})订单[{}]发起一城飞客配送入参：url={},param={}", platformName, orderId, deliveryUrl, jsonParam);
            String result = HttpRequestUtils.sendHttpRequest2(deliveryUrl, takeoutDistributionRequest, "-1");
            log.info("({})订单[{}]发起一城飞客配送返回,result={}", platformName, orderId, result);
            if (result == null) {
                log.info("({})订单[{}]发起一城飞客配送失败", platformName, orderId);
                //fixme 重试以及异常单
            }
            OwnApiResult ownApiResult = JacksonUtils.toObject(OwnApiResult.class, result);
            if (ownApiResult.getCode() != 0) {
                log.info("({})订单[{}]发起一城飞客配送异常,错误信息:{}", platformName, orderId, ownApiResult.getMsg());
            }
            return ownApiResult;
        } catch (Exception e) {
            log.error("({})订单[{}]发起一城飞客配送方法，发生异常:{}", platformName, orderId, e);
            OwnApiResult ownApiResult = new OwnApiResult();
            ownApiResult.setCode(-1);
            ownApiResult.setMsg(e.getMessage());
            return ownApiResult;
        }
    }
    public static void cancelDelivery(UnOrder unOrder,String platformName,String cancelDeliveryUrl){
        String orderId = unOrder.getOrderId();
        try {
            log.info("({})订单[{}]取消一城飞客配送,处理中", platformName, orderId);
            TakeoutDistributionRequest takeoutDistributionRequest = new TakeoutDistributionRequest();
            takeoutDistributionRequest.setPlatform(3);
            TakeoutOwnOrderCancelDTO takeoutOwnOrderCancelDTO = new TakeoutOwnOrderCancelDTO();
            takeoutOwnOrderCancelDTO.setOrderId(unOrder.getOrderId());
            takeoutOwnOrderCancelDTO.setCancelReasonId(0);
            takeoutOwnOrderCancelDTO.setCancelReason(unOrder.getCancelReason());
            takeoutDistributionRequest.setRequestJsonData(takeoutOwnOrderCancelDTO);

            String jsonParam = JacksonUtils.writeValueAsString(takeoutDistributionRequest);
            log.info("({})订单[{}]取消一城飞客配送入参：url={},param={}", platformName, orderId, cancelDeliveryUrl, jsonParam);
            String result = HttpRequestUtils.sendHttpRequest(cancelDeliveryUrl, jsonParam, "-1");
            log.info("({})订单[{}]取消一城飞客配送返回：result={}", platformName, orderId, result);
            if (result == null) {
                log.info("({})订单[{}]取消一城飞客配送失败", platformName, orderId);
            }
            OwnApiResult ownApiResult = JacksonUtils.toObject(OwnApiResult.class, result);
            if (ownApiResult.getCode() != 0) {
                log.info("({})订单[{}]取消一城飞客配送异常,错误信息:{}", platformName, orderId, ownApiResult.getMsg());
            }
        } catch (Exception e) {
            log.info("({})订单[{}]取消一城飞客配送方法，发生异常", platformName, orderId, e);
        }
    }
}
