package com.holder.saas.store.takeaway.producers.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import com.holder.saas.store.takeaway.producers.constant.TakeoutConstant;
import com.holder.saas.store.takeaway.producers.entity.domain.EleAuthDO;
import com.holder.saas.store.takeaway.producers.mapper.EleAuthMapper;
import com.holder.saas.store.takeaway.producers.service.DistributedService;
import com.holder.saas.store.takeaway.producers.service.EleAuthService;
import com.holder.saas.store.takeaway.producers.utils.ThrowableExtUtils;
import com.holder.saas.store.takeaway.producers.utils.UrlCodecUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.takeaway.EleCallbackBindDTO;
import com.holderzone.saas.store.dto.takeaway.EleCbUnbindDetail;
import com.holderzone.saas.store.dto.takeaway.UnOrder;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutShopBindReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.StoreAuthDTO;
import com.holderzone.saas.store.dto.takeaway.response.TakeoutShopBindRespDTO;
import eleme.openapi.sdk.api.entity.other.OMessage;
import eleme.openapi.sdk.api.entity.packs.ShopContract;
import eleme.openapi.sdk.api.entity.user.OAuthorizedShop;
import eleme.openapi.sdk.api.entity.user.OUser;
import eleme.openapi.sdk.api.enumeration.shop.OShopProperty;
import eleme.openapi.sdk.api.exception.ServiceException;
import eleme.openapi.sdk.api.exception.UnauthorizedException;
import eleme.openapi.sdk.api.service.PacksService;
import eleme.openapi.sdk.api.service.ShopService;
import eleme.openapi.sdk.api.service.UserService;
import eleme.openapi.sdk.config.Config;
import eleme.openapi.sdk.oauth.OAuthClient;
import eleme.openapi.sdk.oauth.response.Token;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 饿了吗认证授权相关
 */
@Slf4j
@Service("eleAutoHandle")
//@ConfigurationProperties(prefix="ele")
public class EleAuthServiceImpl extends ServiceImpl<EleAuthMapper, EleAuthDO> implements EleAuthService {
    /**
     * 授权URL
     */
    @Value("${ele.BINDING_URL}")
    private String bindingUrl;

    /**
     * 回调地址URL
     */
    @Value("${ele.REDIRECT_URL}")
    private String redirectUrl;

    /**
     * key
     */
    @Value("${ele.CLIENT_KEY}")
    private String eleClientKey;

    @Value("${ele.TOKEN_REFRESH_AHEAD_HOURS}")
    private Integer tokenRefreshAheadHours;

    @Value("${ele.REFRESH_TOKEN_VALIDITY_PERIOD_DAYS}")
    private Integer refreshTokenValidityPeriodDays;

    private final Config config;

    private final OAuthClient oAuthClient;

    private final DistributedService distributedService;

    @Resource
    private EleAuthMapper eleAuthMapper;

    @Autowired
    public EleAuthServiceImpl(Config config, OAuthClient oAuthClient, DistributedService distributedService) {
        this.config = config;
        this.oAuthClient = oAuthClient;
        this.distributedService = distributedService;
    }

    /**
     * 拼接授权地址
     *
     * @param takeoutShopBindReqDTO
     * @return
     */
    @Override
    public TakeoutShopBindRespDTO takeOutBindingUrl(TakeoutShopBindReqDTO takeoutShopBindReqDTO) {
        TakeoutShopBindRespDTO takeoutShopBindRespDTO = new TakeoutShopBindRespDTO();
        if (takeoutShopBindReqDTO.getBindingStatus() == TakeoutConstant.BINDING) {
            String state = EleCallbackBindDTO.getStateUsingGuid(takeoutShopBindReqDTO);
            String url = bindingUrl +
                    "?response_type=code" +
                    "&scope=all" +
                    "&client_id=" + eleClientKey +
                    "&state=" + state +
                    "&redirect_uri=" + UrlCodecUtils.getURLEncoderString(redirectUrl);
            takeoutShopBindRespDTO.setUrl(url);
        }
        return takeoutShopBindRespDTO;
    }

    /**
     * 保存token
     *
     * @param eleCallbackBindDTO
     */
    @Override
    public void bindCallback(EleCallbackBindDTO eleCallbackBindDTO) {
        try {
            saveToken(eleCallbackBindDTO.getCode(),
                    eleCallbackBindDTO.getEnterpriseGuid(), eleCallbackBindDTO.getStoreGuid());
        } catch (Exception e) {
            if (log.isErrorEnabled()) {
                log.error("(饿了么)绑定回调，处理失败，异常：{}", ThrowableExtUtils.asStringIfAbsent(e));
            }
        }
    }

    /**
     * 移除token
     *
     * @param oMessage
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void unbindCallback(OMessage oMessage) {
        long shopId = oMessage.getShopId();
        EleCbUnbindDetail eleCbUnbindDetail = JSON.parseObject(oMessage.getMessage(), EleCbUnbindDetail.class);
        LocalDateTime relieveOAuthTime = eleCbUnbindDetail.getRelieveOAuthTime();
        LambdaQueryWrapper<EleAuthDO> wrapper = new LambdaQueryWrapper<EleAuthDO>().eq(EleAuthDO::getShopId, shopId);
        EleAuthDO eleAuthInDb = getOne(wrapper);
        if (eleAuthInDb != null && relieveOAuthTime.compareTo(eleAuthInDb.getGmtModified()) > 0) {
            remove(wrapper);
        }
    }

    /**
     * 授权码(企业)模式获取Token
     *
     * @throws Exception
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void saveToken(String autoCode, String enterpriseGuid, String storeGuid) throws Exception {
        Token token = oAuthClient.getTokenByCode(autoCode, redirectUrl);
        if (token == null) {
            throw new BusinessException("授权失败： token is null");
        }
        if (!token.isSuccess()) {
            throw new BusinessException("授权失败"
                    + "error_code:" + token.getError()
                    + " error_desc: " + token.getError_description());
        }
        log.info("token: {}", token.toString());

        // 新Token
        EleAuthDO eleAuthNew = EleAuthDO.ofToken(token, refreshTokenValidityPeriodDays);

        // 设置userId,useName
        OUser oUser = new UserService(config, token).getUser();
        if (oUser == null) {
            throw new BusinessException("oUser为空");
        }
        long userId = oUser.getUserId();
        eleAuthNew.setGuid(distributedService.nextEleGuid());
        eleAuthNew.setEnterpriseGuid(enterpriseGuid);
        eleAuthNew.setStoreGuid(storeGuid);
        eleAuthNew.setUserId(userId);
        eleAuthNew.setUserName(oUser.getUserName());

        // fixme 目前这里不考虑连锁的情况
        // 绑定openId
        List<OAuthorizedShop> authorizedShops = oUser.getAuthorizedShops();
        if (authorizedShops.isEmpty()) {
            throw new BusinessException("店铺为空");
        }
        OAuthorizedShop oAuthorizedShop = authorizedShops.get(0);
        Map<OShopProperty, Object> map = new HashMap<>();
        map.put(OShopProperty.openId, storeGuid);
        new ShopService(config, token).updateShop(oAuthorizedShop.getId(), map);
        eleAuthNew.setShopId(oAuthorizedShop.getId());
        eleAuthNew.setShopName(oAuthorizedShop.getName());

        // 保存或更新token
        EleAuthDO eleAuthInDb = getOne(new LambdaQueryWrapper<EleAuthDO>().eq(EleAuthDO::getUserId, userId));
        log.info("商家重新查询EleAuthDO认证数据：{}", JacksonUtils.writeValueAsString(eleAuthInDb));
        if (eleAuthInDb == null) {
            log.info("商家重新认证创建的EleAuthDO认证数据：{}", JacksonUtils.writeValueAsString(eleAuthNew));
            List<EleAuthDO> eleAuthDOS = eleAuthMapper.eleAuthList(userId);
            if (!CollectionUtils.isEmpty(eleAuthDOS)) {
                EleAuthDO eleAuthDO = eleAuthDOS.get(0);
                Integer deliveryType = eleAuthDO.getDeliveryType();
                if (!Objects.isNull(deliveryType)) {
                    eleAuthNew.setDeliveryType(deliveryType);
                    log.info("商家重新认证创建的EleAuthDO认证数据，进行了deliveryType补偿，原：{}", JacksonUtils.writeValueAsString(eleAuthDO));
                }
            }
            save(eleAuthNew);
        } else {
            EleAuthDO eleAuthDO = eleAuthNew.setId(eleAuthInDb.getId());
            log.info("商家重新认证更新的EleAuthDO认证数据：{}", JacksonUtils.writeValueAsString(eleAuthDO));
            updateById(eleAuthDO);
        }
    }

    @Override
    public Token getTokenByShopId(Long shopId) {
        // 避免有多条
        List<EleAuthDO> eleAuthList = list(new LambdaQueryWrapper<EleAuthDO>()
                .eq(EleAuthDO::getShopId, shopId)
                .orderByDesc(EleAuthDO::getGmtCreate)
                .last("limit 1"));
        if (CollectionUtils.isEmpty(eleAuthList)) return null;
        return getToken(eleAuthList.get(0));
    }

    /**
     * 授权码(企业)模式刷新Token
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void refreshToken(long beforeTime) {
        List<EleAuthDO> expireTokenList = list(new LambdaQueryWrapper<EleAuthDO>()
                .le(EleAuthDO::getExpireTime, DateTimeUtils.now().plusSeconds(beforeTime)));
        expireTokenList.forEach(this::refreshToken);
    }

    /**
     * 刷新数据库中指定的token，并更新到库中
     *
     * @param eleAuthDO
     * @return
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Token refreshToken(EleAuthDO eleAuthDO) {
        log.info("enterpriseGuid: {}，storeGuid: {}，开始刷新饿了么TOKEN！",
                eleAuthDO.getEnterpriseGuid(), eleAuthDO.getStoreGuid());
        String refreshToken = eleAuthDO.getRefreshToken();
        Token token = oAuthClient.getTokenByRefreshToken(refreshToken);
        if (token == null) {
            log.error("storeGuid: {}，error_token: the token is null", eleAuthDO.getStoreGuid());
            return null;
        }
        if (!token.isSuccess()) {
            if ("Unauthorized refresh token.".equalsIgnoreCase(token.getError_description())) {
                if (remove(new LambdaQueryWrapper<EleAuthDO>()
                        .eq(EleAuthDO::getId, eleAuthDO.getId())
                        .eq(EleAuthDO::getRefreshToken, refreshToken))) {
                    log.error("storeGuid: {}，token is remove,error_token: {}", eleAuthDO.getStoreGuid(),
                            JacksonUtils.writeValueAsString(token));
                }
            } else {
                log.error("storeGuid: {}，error_token: {}", eleAuthDO.getStoreGuid(),
                        JacksonUtils.writeValueAsString(token));
            }
            return null;
        }
        if (!updateById(EleAuthDO.ofToken(token, refreshTokenValidityPeriodDays).setId(eleAuthDO.getId()))) {
            log.info("更新旧的饿了么token失败--eleAuthId:{}", eleAuthDO.getId());
            EleAuthDO entity = EleAuthDO.ofToken(token, refreshTokenValidityPeriodDays);
            entity.setEnterpriseGuid(eleAuthDO.getEnterpriseGuid());
            entity.setStoreGuid(eleAuthDO.getStoreGuid());
            entity.setUserId(eleAuthDO.getUserId());
            entity.setUserName(eleAuthDO.getUserName());
            entity.setShopId(eleAuthDO.getShopId());
            entity.setShopName(eleAuthDO.getShopName());
            entity.setDeliveryType(eleAuthDO.getDeliveryType());
            save(entity.setGuid(distributedService.nextEleGuid()));
        }
        log.info("enterpriseGuid: {}，storeGuid: {}，TOKEN刷新成功！",
                eleAuthDO.getEnterpriseGuid(), eleAuthDO.getStoreGuid());
        return token;
    }


    /**
     * 根据userID获取token
     *
     * @param userId
     * @return
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Token getToken(long userId) {
        EleAuthDO eleAuthDO = getOne(new LambdaQueryWrapper<EleAuthDO>().eq(EleAuthDO::getUserId, userId));
        if (eleAuthDO == null) return null;
        return getToken(eleAuthDO);
    }


    /**
     * 根据storeGuid获取token
     *
     * @param storeGuid
     * @return
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Token getToken(String storeGuid) {
        EleAuthDO eleAuthDO = getOne(new LambdaQueryWrapper<EleAuthDO>().eq(EleAuthDO::getStoreGuid, storeGuid));
        if (eleAuthDO == null) return null;
        return getToken(eleAuthDO);
    }

    /**
     * 根据eleAuthDO获取token
     *
     * @param eleAuthDO
     * @return
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Token getToken(EleAuthDO eleAuthDO) {
        LocalDateTime expireTime = eleAuthDO.getExpireTime();
        LocalDateTime refreshTime = expireTime.minusHours(tokenRefreshAheadHours);
        LocalDateTime nowTime = DateTimeUtils.now();
        LocalDateTime refreshExpireTime = eleAuthDO.getRefreshExpireTime();
        if (nowTime.compareTo(refreshExpireTime) >= 0) {
            log.info("RefreshToken，过期时间：{}，现在时间：{}，删除Token", refreshExpireTime, nowTime);
            removeById(eleAuthDO.getId());
            return null;
        }
        if (nowTime.compareTo(refreshTime) >= 0) {
            log.info("Token，过期时间：{}，预设刷新时间：{}，现在时间：{}，刷新Token", expireTime, refreshTime, nowTime);
            return refreshToken(eleAuthDO);
        }
        return EleAuthDO.toToken(eleAuthDO);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Map<String, Pair<EleAuthDO, Token>> getTokens(List<String> storeGuids) {
        List<EleAuthDO> eleAuthList = list(new LambdaQueryWrapper<EleAuthDO>().in(EleAuthDO::getStoreGuid, storeGuids));
        if (CollectionUtils.isEmpty(eleAuthList)) {
            return Maps.newHashMap();
        }
        Map<String, Pair<EleAuthDO, Token>> storeTokenMap = Maps.newHashMap();
        for (EleAuthDO authDO : eleAuthList) {
            Token token = getToken(authDO);
            if (Objects.nonNull(token)) {
                storeTokenMap.put(authDO.getStoreGuid(), Pair.of(authDO, token));
            }
        }
        return storeTokenMap;
    }


    /**
     * 根据erp门店id查询饿了么外卖授权表，是否已经授权
     *
     * @param storeAuthorizationDTOList
     * @return
     */
    @Override
    public List<StoreAuthDTO> listAuth(List<StoreAuthDTO> storeAuthorizationDTOList) {
        if (CollectionUtils.isEmpty(storeAuthorizationDTOList)) return Collections.emptyList();
        List<String> arrayOfStoreId = storeAuthorizationDTOList.stream()
                .map(StoreAuthDTO::getStoreGuid)
                .collect(Collectors.toList());
        Map<String, StoreAuthDTO> storeAuthMap = storeAuthorizationDTOList.stream()
                .collect(Collectors.toMap(StoreAuthDTO::getStoreGuid, storeAuth -> storeAuth));

        List<EleAuthDO> arrayOfEleAuthDO = list(new LambdaQueryWrapper<EleAuthDO>()
                .in(EleAuthDO::getStoreGuid, arrayOfStoreId));
        Map<String, EleAuthDO> eleAuthMap = arrayOfEleAuthDO.stream()
                .collect(Collectors.toMap(EleAuthDO::getStoreGuid, eleShopDO -> eleShopDO));
        return arrayOfStoreId.stream()
                .map(storeId -> {
                    EleAuthDO eleAuthDO = eleAuthMap.get(storeId);
                    StoreAuthDTO storeAuthDTO = storeAuthMap.get(storeId);
                    if (eleAuthDO == null) {
                        return storeAuthDTO.setBindingStatus(0);
                    }
                    return storeAuthDTO.setBindingStatus(1)
                            .setShopId(Long.toString(eleAuthDO.getShopId()))
                            .setShopName(eleAuthDO.getShopName());
                })
                .collect(Collectors.toList());
    }

    /**
     * 根据erp门店id查询饿了么外卖授权表，是否已经授权
     *
     * @param storeAuthDTO
     * @return
     */
    @Override
    public StoreAuthDTO getTakeoutAuth(StoreAuthDTO storeAuthDTO) {
        EleAuthDO eleAuthDO = getOne(new LambdaQueryWrapper<EleAuthDO>()
                .eq(EleAuthDO::getStoreGuid, storeAuthDTO.getStoreGuid()));
        if (eleAuthDO == null) {
            return storeAuthDTO.setBindingStatus(0);
        }
        return storeAuthDTO.setBindingStatus(1)
                .setShopId(Long.toString(eleAuthDO.getShopId()))
                .setShopName(eleAuthDO.getShopName())
                .setDeliveryType(eleAuthDO.getDeliveryType());
    }

    @Override
    public Boolean updateDelivery(StoreAuthDTO storeAuthDTO) {
        EleAuthDO eleAuthDO = getOne(new LambdaQueryWrapper<EleAuthDO>()
                .eq(EleAuthDO::getStoreGuid, storeAuthDTO.getStoreGuid()));
        eleAuthDO.setDeliveryType(storeAuthDTO.getDeliveryType());
        return updateById(eleAuthDO);
    }

    /**
     * (饿了么)移除失效Token
     *
     * @param e
     * @param eleAuthDO
     */
    @Override
    public void correctAuth(ServiceException e, EleAuthDO eleAuthDO) {
        if (e instanceof UnauthorizedException) {
            try {
                refreshToken(eleAuthDO);
            } catch (Throwable throwable) {
                log.error("enterpriseGuid: {}，storeGuid: {}，(饿了么)刷新Token失败，失败原因：{}",
                        eleAuthDO.getEnterpriseGuid(), eleAuthDO.getStoreGuid(), ThrowableExtUtils.asStringIfAbsent(throwable));
            }
        }
    }

    private Long getUserIdByStoreGuid(String storeGuid) {
        return this.getOne(new LambdaQueryWrapper<EleAuthDO>().eq(EleAuthDO::getStoreGuid, storeGuid)).getUserId();
    }

    @Override
    public ShopContract getEffectServicePackContract(UnOrder unOrder) {
        ShopContract shopContract = new ShopContract();
        EleAuthDO eleAuthDO = getOne(new LambdaQueryWrapper<EleAuthDO>()
                .eq(EleAuthDO::getStoreGuid, unOrder.getStoreGuid()));
        Long userId = getUserIdByStoreGuid(unOrder.getStoreGuid());
        if (userId != null) {
            Token token = this.getToken(userId);
            PacksService packsService = new PacksService(config, token);
            try {
                shopContract = packsService.getEffectServicePackContract(eleAuthDO.getShopId());
            } catch (ServiceException e) {
                e.printStackTrace();
                log.info("查询门店配送生效方式异常:{}", e.getMessage());
            }
        } else {
            log.info("查询门店配送生效方式错误，userId为空:{}", userId);
        }
        log.info("shopContract={}", JacksonUtils.writeValueAsString(shopContract));
        return shopContract;
    }


    @Override
    public void correctAuthThenThrow(ServiceException e, UnOrder unOrder) {
        if (e instanceof UnauthorizedException) {
            try {
                EleAuthDO eleAuthDO = getOne(new LambdaQueryWrapper<EleAuthDO>().eq(EleAuthDO::getShopId, unOrder.getShopId()));
                refreshToken(eleAuthDO);
            } catch (Throwable throwable) {
                log.error("enterpriseGuid: {}，storeGuid: {}，(饿了么)刷新Token失败，失败原因：{}",
                        unOrder.getEnterpriseGuid(), unOrder.getStoreGuid(), ThrowableExtUtils.asStringIfAbsent(throwable));
            }
        }
    }
}