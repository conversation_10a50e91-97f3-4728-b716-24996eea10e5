package com.holder.saas.store.takeaway.producers.utils.sdk.meituan;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.holder.saas.store.takeaway.producers.constant.TakeoutConstant;
import com.sankuai.sjst.platform.developer.domain.RequestDomain;
import com.sankuai.sjst.platform.developer.utils.SignUtils;
import com.sankuai.sjst.platform.developer.utils.WebUtils;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.NameValuePair;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.message.BasicNameValuePair;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @date 2023/9/8 11:29
 * @description 查询用户会员卡信息
 */
@Data
@Slf4j
public class CipCaterMemberRequest {

    /**
     * 门店令牌，接口需要授权时必填
     */
    private String appAuthToken;

    private String timestamp;

    private String url;

    private Integer businessId;

    private String signKey;

    private String developerId;

//    private String userOpenId;

    private BIZ biz;

    /**
     * 版本号，固定传2
     */
    private static final String VERSION = "2";

    public CipCaterMemberRequest() {

    }

    public CipCaterMemberRequest(Integer businessId, Long developerId, String signKey, String userOpenId, String appAuthToken) {
        this.url = RequestDomain.preUrl.getValue() + "/jmcard/members/query";
        this.timestamp = (new Timestamp((new Date()).getTime())).toString();
        this.signKey = signKey;
        this.businessId = businessId;
        this.developerId = String.valueOf(developerId);
        this.biz = new BIZ().setUserOpenId(userOpenId);
        this.appAuthToken = appAuthToken;
    }

    public String doPost() {
        List<NameValuePair> paramsInUrl = Lists.newArrayList();
        paramsInUrl.add(new BasicNameValuePair("businessId", String.valueOf(this.businessId)));
        paramsInUrl.add(new BasicNameValuePair("timestamp", this.timestamp));
        paramsInUrl.add(new BasicNameValuePair("charset", TakeoutConstant.CHARSET_UTF_8));
        paramsInUrl.add(new BasicNameValuePair("developerId", this.developerId));
        paramsInUrl.add(new BasicNameValuePair("biz", JSON.toJSONString(this.biz)));
        paramsInUrl.add(new BasicNameValuePair("version", VERSION));
        paramsInUrl.add(new BasicNameValuePair("appAuthToken", this.appAuthToken));
        paramsInUrl.add(new BasicNameValuePair("sign", getSign()));
        try {
            String finalUrl = (new URIBuilder()).setParameters(paramsInUrl).setPath(this.url).build().toString();
            log.info("[CipCaterMemberRequest]finalUrl={}",finalUrl);
            return WebUtils.post(finalUrl, null);
        } catch (Exception e) {
            log.error("[查询用户会员卡信息]失败", e);
        }
        return null;
    }

    public String getSign() {
        Map<String, String> params = Maps.newHashMap();
        params.put("businessId", String.valueOf(this.businessId));
        params.put("charset", TakeoutConstant.CHARSET_UTF_8);
        params.put("developerId", this.developerId);
        params.put("timestamp", this.timestamp);
        params.put("biz", JSON.toJSONString(this.biz));
        params.put("version", VERSION);
        params.put("appAuthToken", this.appAuthToken);
        return SignUtils.createSign(this.signKey, params);
    }

    @Data
    @Accessors(chain = true)
    public static class BIZ implements Serializable {

        private static final long serialVersionUID = -7092192923448592584L;

        private String userOpenId;
    }

}
