package com.holder.saas.store.takeaway.producers.entity.dto;

import com.sankuai.sjst.platform.developer.utils.SignUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@ApiModel
@NoArgsConstructor
public class MtPrivacyReqDTO {

    @ApiModelProperty(value = "认领门店返回的token【一店一token】，非必须")
    private String appAuthToken;

    @ApiModelProperty(value = "交互数据的编码【建议UTF-8】，必须")
    private String charset;

    @ApiModelProperty(value = "当前请求的时间戳【单位是秒】，必须")
    private long timestamp;

    @ApiModelProperty(value = "接口版本【默认是1】，非必须")
    private String version;

    @ApiModelProperty(value = "请求的数字签名，必须")
    private String sign;

    @ApiModelProperty(value = "开发者id，必须")
    private int developerId;

    @ApiModelProperty(value = "分页查询的偏移量，必须")
    private int degradOffset;

    @ApiModelProperty(value = "每页条数，需小于等于1000，必须")
    private int degradLimit;

    public List<NameValuePair> nameValuePairs(String mtSignKey) {

        String charsetStr = charset;
        String timestampStr = String.valueOf(timestamp);
        String developerIdStr = String.valueOf(developerId);
        String degradOffsetStr = String.valueOf(degradOffset);
        String degradLimitStr = String.valueOf(degradLimit);

        Map<String, String> params = new HashMap<>();
        params.put("charset", charsetStr);
        params.put("timestamp", timestampStr);
        params.put("developerId", developerIdStr);
        params.put("degradOffset", degradOffsetStr);
        params.put("degradLimit", degradLimitStr);

        String signStr = SignUtils.createSign(mtSignKey, params);

        List<NameValuePair> paramsInUrl = new ArrayList<>();
        paramsInUrl.add(new BasicNameValuePair("charset", charsetStr));
        paramsInUrl.add(new BasicNameValuePair("timestamp", timestampStr));
        paramsInUrl.add(new BasicNameValuePair("sign", signStr));
        paramsInUrl.add(new BasicNameValuePair("developerId", developerIdStr));
        paramsInUrl.add(new BasicNameValuePair("degradOffset", degradOffsetStr));
        paramsInUrl.add(new BasicNameValuePair("degradLimit", degradLimitStr));

        return paramsInUrl;
    }
}
