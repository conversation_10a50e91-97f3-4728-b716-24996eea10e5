package com.holder.saas.store.takeaway.producers.service.impl;


import com.holder.saas.store.takeaway.producers.service.GroupBuyService;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.business.group.StoreBindDTO;
import com.holderzone.saas.store.dto.order.response.groupon.GroupVerifyDTO;
import com.holderzone.saas.store.dto.takeaway.request.CouPonPreReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.CouPonReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtCouponPreRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtDelCouponRespDTO;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-06-19
 * @description
 */
@Service("daZhongGroupBuyServiceImpl")
public class DaZhongGroupBuyServiceImpl implements GroupBuyService {
    @Override
    public void bindStore(StoreBindDTO storeBind) {
        throw new BusinessException("服务不存在");
    }

    @Override
    public String getToken() {
        return null;
    }

    @Override
    public List<MtCouponPreRespDTO> couponPrepare(CouPonPreReqDTO couPonPreReqDTO) {
        return Collections.emptyList();
    }

    @Override
    public List<GroupVerifyDTO> verifyCoupon(CouPonReqDTO couPonReq) {
        return Collections.emptyList();
    }

    @Override
    public MtDelCouponRespDTO revokeCoupon(GroupVerifyDTO revokeReq) {
        return null;
    }
}
