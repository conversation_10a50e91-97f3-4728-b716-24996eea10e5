package com.holder.saas.store.takeaway.producers.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @create 2023-12-06
 * @description 农行团购配置信息
 */
@Component
@ConfigurationProperties(prefix = "group.abc")
@Data
public class GroupBuyAbcConfig {

    public String appId;

    public String appSecret;

    public String host;

    public String shopQuery;

    public String couponQuery;

    public String couponVerify;

    public String couponCancel;

}
