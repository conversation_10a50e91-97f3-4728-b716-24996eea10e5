package com.holder.saas.store.takeaway.producers.utils.sdk.jd;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.holder.saas.store.takeaway.producers.utils.sdk.jd.dto.CommonRspDTO;
import com.holder.saas.store.takeaway.producers.utils.sdk.jd.dto.StoreInfo;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @description 根据到家门店编码查询门店基本信息接
 */
@Slf4j
public class GetStoreInfoByStationNoRequest extends AbstractJdRequest{


    public GetStoreInfoByStationNoRequest() {
    }

    public GetStoreInfoByStationNoRequest(String appKey, String appSecret, String token) {
       super.appKey = appKey;
       super.appSecret = appSecret;
       super.token = token;
    }

    public StoreInfo execute(String storeNo){
        try {
            String response = super.execute("{\"StoreNo\":\"" + storeNo + "\"}", "/storeapi/getStoreInfoByStationNo");

            CommonRspDTO<StoreInfo> storeInfoRsp = JSON.parseObject(response,new TypeReference<CommonRspDTO<StoreInfo>>(){});
            if(!storeInfoRsp.isSuccess()){
                return null;
            }
            return storeInfoRsp.getResult();
        }catch (Exception e){
            log.error("查询商家商品列表失败",e);
        }
        return null;
    }
}
