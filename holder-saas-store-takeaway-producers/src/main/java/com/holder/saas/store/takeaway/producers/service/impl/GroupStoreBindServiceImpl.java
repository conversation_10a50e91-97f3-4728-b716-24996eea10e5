package com.holder.saas.store.takeaway.producers.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.holder.saas.store.takeaway.producers.entity.domain.GroupStoreBindDO;
import com.holder.saas.store.takeaway.producers.entity.domain.MtAuthDO;
import com.holder.saas.store.takeaway.producers.mapper.GroupStoreBindMapper;
import com.holder.saas.store.takeaway.producers.service.GroupStoreBindService;
import com.holder.saas.store.takeaway.producers.service.MtAuthService;
import com.holder.saas.store.takeaway.producers.service.converter.GroupBuyConverter;
import com.holderzone.saas.store.dto.business.group.StoreBindDTO;
import com.holderzone.saas.store.dto.takeaway.MtBusinessIdEnum;
import com.holderzone.saas.store.dto.takeaway.response.StoreAuthDTO;
import com.holderzone.saas.store.enums.GroupBuyTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2023-06-19
 * @description
 */
@Service
@Slf4j
public class GroupStoreBindServiceImpl extends ServiceImpl<GroupStoreBindMapper, GroupStoreBindDO> implements GroupStoreBindService {

    private static final List<StoreAuthDTO> DEFAULT_GROUP_STORE_LIST;

    private final MtAuthService takeawayInfoAuth;

    static {
        DEFAULT_GROUP_STORE_LIST = Lists.newArrayList();
        GroupBuyTypeEnum[] values = GroupBuyTypeEnum.values();
        for (GroupBuyTypeEnum groupBuyTypeEnum : values) {

            if (groupBuyTypeEnum == GroupBuyTypeEnum.DA_ZHONG_DIAN_PIN
                    || groupBuyTypeEnum == GroupBuyTypeEnum.ZHUAN_CAN
                    || groupBuyTypeEnum == GroupBuyTypeEnum.OTHER
                    || groupBuyTypeEnum == GroupBuyTypeEnum.MAITON) {
                continue;
            }
            StoreAuthDTO storeAuthDTO = new StoreAuthDTO();
            storeAuthDTO.setBindingStatus(0);
            storeAuthDTO.setTakeoutType(groupBuyTypeEnum.getCode() == GroupBuyTypeEnum.MEI_TUAN.getCode() ? 1 : groupBuyTypeEnum.getCode());
            storeAuthDTO.setPlatformName(groupBuyTypeEnum.getDesc());
            DEFAULT_GROUP_STORE_LIST.add(storeAuthDTO);
        }
    }

    @Autowired
    public GroupStoreBindServiceImpl(@Qualifier("takeawayInfoAuth")MtAuthService takeawayInfoAuth) {
        this.takeawayInfoAuth = takeawayInfoAuth;
    }

    @Override
    public void unbindStore(StoreBindDTO storeBind) {
        //根据团购类型删除
        remove(new LambdaQueryWrapper<GroupStoreBindDO>().eq(GroupStoreBindDO::getStoreGuid,storeBind.getStoreGuid())
                .eq(GroupStoreBindDO::getType,storeBind.getGroupBuyType()));
    }

    @Override
    public List<StoreAuthDTO> listAllByStoreGuid(String storeGuid) {
        //已绑定的门店信息
        List<GroupStoreBindDO> bindList = list(new LambdaQueryWrapper<GroupStoreBindDO>().eq(GroupStoreBindDO::getStoreGuid, storeGuid));
        //美团绑定信息
        MtAuthDO auth = takeawayInfoAuth.getAuth(storeGuid, MtBusinessIdEnum.TUAN_GOU.getType());
        if(CollectionUtil.isEmpty(bindList) && auth == null){
            return DEFAULT_GROUP_STORE_LIST;
        }
        List<StoreAuthDTO> storeAuthList = GroupBuyConverter.toStoreAuthList(bindList, auth);
        if(storeAuthList.size() < DEFAULT_GROUP_STORE_LIST.size()){
            Set<Integer> collect = storeAuthList.stream().map(StoreAuthDTO::getTakeoutType).collect(Collectors.toSet());
            storeAuthList.addAll(DEFAULT_GROUP_STORE_LIST.stream().filter(e -> !collect.contains(e.getTakeoutType())).collect(Collectors.toList()));
        }
        storeAuthList.sort(Comparator.comparing(StoreAuthDTO::getTakeoutType).reversed());
        return storeAuthList;
    }
}
