package com.holder.saas.store.takeaway.producers.service.rpc;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.resource.common.dto.enterprise.MultiMemberDTO;
import com.holderzone.resource.common.dto.enterprise.MultiMemberQueryDTO;
import com.holderzone.resource.common.dto.enterprise.OrganizationDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@Component
@FeignClient(name = "holder-saas-cloud-enterprise", fallbackFactory = CloudEnterpriseService.ServiceFallBack.class)
public interface CloudEnterpriseService {

    @GetMapping("/enterprise/member/info/{organizationGuid}")
    MultiMemberDTO findMemberInfoByOrganizationGuid(@PathVariable("organizationGuid") String organizationGuid);

    /**
     * 查询运营主体下的门店列表
     *
     * @param queryDTO 关联企业guid，运营主体guid
     * @return 门店列表
     */
    @ApiOperation(value = "查询运营主体下的门店列表", notes = "必传 关联企业guid，运营主体guid")
    @PostMapping("/organization/store_list_multiMemberGuid")
    List<OrganizationDTO> getStoreByMultiMemberGuid(@RequestBody MultiMemberQueryDTO queryDTO);

    /**
     * 根据企业guid查询企业经营模式
     *
     * @param enterpriseGuid 企业guid
     * @return SINGLE, 单店 CHAIN,连锁 PLATFORM平台
     */
    @GetMapping("/enterprise/management_model/{enterpriseGuid}")
    String queryManagementModel(@PathVariable(value = "enterpriseGuid") String enterpriseGuid);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<CloudEnterpriseService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public CloudEnterpriseService create(Throwable throwable) {
            return new CloudEnterpriseService() {

                @Override
                public MultiMemberDTO findMemberInfoByOrganizationGuid(String organizationGuid) {
                    log.error(HYSTRIX_PATTERN, "findMemberInfoByOrganizationGuid", "门店guid：" + organizationGuid,
                            ThrowableUtils.asString(throwable));
                    throw new BusinessException("查询接口熔断");
                }

                @Override
                public List<OrganizationDTO> getStoreByMultiMemberGuid(MultiMemberQueryDTO queryDTO) {
                    log.error(HYSTRIX_PATTERN, "getStoreByMultiMemberGuid", JacksonUtils.writeValueAsString(queryDTO),
                            ThrowableUtils.asString(throwable));
                    throw new BusinessException("查询运营主体下的门店列表 熔断");
                }

                @Override
                public String queryManagementModel(String enterpriseGuid) {
                    log.error(HYSTRIX_PATTERN, "queryManagementModel", enterpriseGuid, ThrowableUtils.asString(throwable));
                    throw new BusinessException("根据企业guid查询企业经营模式 熔断");
                }

            };
        }
    }
}