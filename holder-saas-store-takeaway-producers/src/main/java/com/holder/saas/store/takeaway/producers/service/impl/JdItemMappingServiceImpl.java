package com.holder.saas.store.takeaway.producers.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Maps;
import com.holder.saas.store.takeaway.producers.constant.TakeoutConstant;
import com.holder.saas.store.takeaway.producers.entity.domain.JdAuthDO;
import com.holder.saas.store.takeaway.producers.entity.domain.JdItemMappingDO;
import com.holder.saas.store.takeaway.producers.entity.domain.JdStoreMappingDO;
import com.holder.saas.store.takeaway.producers.mapper.JdItemMappingMapper;
import com.holder.saas.store.takeaway.producers.service.JdAuthService;
import com.holder.saas.store.takeaway.producers.service.JdItemMappingService;
import com.holder.saas.store.takeaway.producers.service.JdStoreMappingService;
import com.holder.saas.store.takeaway.producers.service.UnItemMappingService;
import com.holder.saas.store.takeaway.producers.utils.sdk.jd.QueryCategoriesRequest;
import com.holder.saas.store.takeaway.producers.utils.sdk.jd.QuerySkuInfoRequest;
import com.holder.saas.store.takeaway.producers.utils.sdk.jd.dto.ShopCategory;
import com.holder.saas.store.takeaway.producers.utils.sdk.jd.dto.SkuMain;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.takeaway.*;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 京东商品映射服务实现类
 */
@Service("jdItemMappingServiceImpl")
@Slf4j
@RequiredArgsConstructor
public class JdItemMappingServiceImpl implements UnItemMappingService , JdItemMappingService {

    private final JdAuthService jdAuthService;

    private final JdStoreMappingService jdStoreMappingService;

    private final JdItemMappingMapper jdItemMappingMapper;

    @Override
    public List<UnMappedType> getType(String storeGuid) {
        return Collections.emptyList();
    }

    @Override
    public List<UnMappedItem> getItem(String storeGuid){
        return Collections.emptyList();
    }

    @Override
    public List<UnMappedItem> getItem(String storeGuid,String brandGuid) {
        JdAuthDO jdAuthByBrand = jdAuthService.getJdAuthByBrand(brandGuid);
        if (jdAuthByBrand == null) {
            throw new BusinessException("京东商户未授权");
        }
        JdStoreMappingDO jdStoreMappingDO = jdStoreMappingService.getByStoreGuid(storeGuid);
        if(jdStoreMappingDO == null) {
            throw new BusinessException("门店未绑定");
        }
        QuerySkuInfoRequest querySkuInfoRequest = new QuerySkuInfoRequest(jdAuthByBrand.getAppKey(), jdAuthByBrand.getAppSecret(), jdAuthByBrand.getToken());
        //查询所有商品列表
        List<SkuMain> skuMainList = querySkuInfoRequest.execute("");
        List<UnMappedItem> unMappedItems = transferUnMappedItem(skuMainList);
        //根据商品映射情况查询映射关系
        if(CollUtil.isNotEmpty(unMappedItems)) {
            //需查询所有分类
            QueryCategoriesRequest queryCategoriesRequest = new QueryCategoriesRequest(jdAuthByBrand.getAppKey(), jdAuthByBrand.getAppSecret(), jdAuthByBrand.getToken());
            List<ShopCategory> categories = queryCategoriesRequest.execute();

            List<JdItemMappingDO> itemMappingDOS = jdItemMappingMapper.selectList(new LambdaQueryWrapper<JdItemMappingDO>()
                    .eq(JdItemMappingDO::getStoreGuid, storeGuid)
                    .eq(JdItemMappingDO::getDeleted, false));
            //装配分类信息以及绑定信息
            assembleUnMappedItem(unMappedItems,categories,itemMappingDOS);
        }
        return unMappedItems;
    }

    private void assembleUnMappedItem(List<UnMappedItem> unMappedItems, List<ShopCategory> categories
            , List<JdItemMappingDO> itemMappingDOS) {
        Map<String, JdItemMappingDO> mappingMap = Maps.newHashMap();
        Map<Long, ShopCategory> categoryMap = Maps.newHashMap();
        //设置映射关系
        if(CollUtil.isNotEmpty(itemMappingDOS)) {
            mappingMap = itemMappingDOS.stream().collect(Collectors.toMap(JdItemMappingDO::getSkuId, Function.identity(), (key1, key2) -> key2));
        }
        if(CollUtil.isNotEmpty(categories)) {
            categoryMap = categories.stream().collect(Collectors.toMap(ShopCategory::getId, Function.identity(), (key1, key2) -> key2));
        }
        for (UnMappedItem unMappedItem : unMappedItems) {
            ShopCategory shopCategory = categoryMap.get(Long.parseLong(unMappedItem.getUnItemTypeId()));
            if(shopCategory != null) {
                unMappedItem.setUnItemTypeName(shopCategory.getShopCategoryName());
            }
            JdItemMappingDO jdItemMapping = mappingMap.get(unMappedItem.getUnItemSkuId());
            if(jdItemMapping != null) {
                unMappedItem.setErpItemId(jdItemMapping.getItemGuid());
                unMappedItem.setErpItemSkuId(jdItemMapping.getSkuGuid());
            }
        }

    }

    private List<UnMappedItem> transferUnMappedItem(List<SkuMain> skuMainList) {
        if (CollectionUtils.isEmpty(skuMainList)) {
            return Collections.emptyList();
        }
        return skuMainList.stream().flatMap(sku ->
                        Stream.of(createUnMappedItem(sku)))
                .collect(Collectors.toList());
    }

    private UnMappedItem createUnMappedItem(SkuMain sku) {
        UnMappedItem unMappedItem = new UnMappedItem();
        unMappedItem.setUnItemId(String.valueOf(sku.getSuperId() == null ? sku.getSkuId() : sku.getSuperId()));
        unMappedItem.setUnItemName(sku.getSkuName());
        unMappedItem.setUnItemTypeId(String.valueOf(sku.getShopCategories() == null || sku.getShopCategories().length == 0 ? 0 : sku.getShopCategories()[0]));
        unMappedItem.setUnItemSkuId(String.valueOf(sku.getSkuId()));
        unMappedItem.setUnItemSkuName(sku.getSkuName());
        unMappedItem.setUnItemNameWithSku(StringUtils.isNotEmpty(sku.getSaleAttrValueName()) ? String.format(TakeoutConstant.BRACKET,sku.getSkuName(),sku.getSaleAttrValueName().replaceAll(";","")) : sku.getSkuName());
        unMappedItem.setExtendValue(JacksonUtils.writeValueAsString(sku));
        return unMappedItem;
    }

    /**
     * 批量查询商品数据
     */
    @Override
    public List<UnMappedItem> getItems(UnItemQueryReq unItemQueryReq) {
        log.info("批量查询京东商品数据，请求参数：{}", JacksonUtils.writeValueAsString(unItemQueryReq));
        
        // 参数校验
        if (CollUtil.isEmpty(unItemQueryReq.getStoreGuids())) {
            log.warn("批量查询京东商品数据，门店ID列表为空");
            return Collections.emptyList();
        }
        
        try {
            // 批量获取有效门店
            List<String> validStoreGuids = getValidStoreGuids(unItemQueryReq.getStoreGuids());
            if (CollUtil.isEmpty(validStoreGuids)) {
                log.warn("批量查询京东商品数据，所有门店未绑定京东");
                return Collections.emptyList();
            }
            
            // 获取授权信息 - 直接使用getBrandGuid方法
            String brandGuid = unItemQueryReq.getBrandGuid();
            if (StringUtils.isEmpty(brandGuid)) {
                log.warn("未提供brandGuid，无法获取授权信息");
                return Collections.emptyList();
            }
            
            // 根据brandGuid获取授权信息
            JdAuthDO jdAuthDO = jdAuthService.getJdAuthByBrand(brandGuid);
            if (jdAuthDO == null) {
                log.error("未找到与brandGuid: {}对应的授权信息", brandGuid);
                return Collections.emptyList();
            }
            
            // 获取商品和分类数据
            List<UnMappedItem> commonItems = getCommonItemsWithCategories(jdAuthDO,unItemQueryReq.getKeywords());
            if (CollUtil.isEmpty(commonItems)) {
                return Collections.emptyList();
            }

            // 获取商品映射关系
            Map<String, JdItemMappingDO> mappingMap = getItemMappings(validStoreGuids, commonItems);

            // 组装返回结果
            return buildResultItems(commonItems, validStoreGuids, mappingMap, unItemQueryReq.getBindingFlag());

        } catch (Exception e) {
            log.error("批量查询京东商品数据异常", e);
            return Collections.emptyList();
        }
    }

    /**
     * 批量获取有效门店ID列表
     */
    private List<String> getValidStoreGuids(List<String> storeGuids) {
        // 使用批量查询方法获取门店映射关系
        List<JdStoreMappingDO> storeMappings = jdStoreMappingService.listByStoreGuids(storeGuids);

        if (CollUtil.isEmpty(storeMappings)) {
            return Collections.emptyList();
        }

        // 提取有效门店ID
        List<String> validStoreGuids = storeMappings.stream()
                .map(JdStoreMappingDO::getStoreGuid)
                .collect(Collectors.toList());

        // 使用Set提高查找效率
        Set<String> validStoreGuidSet = new HashSet<>(validStoreGuids);
        List<String> invalidStoreGuids = storeGuids.stream()
                .filter(storeGuid -> !validStoreGuidSet.contains(storeGuid))
                .collect(Collectors.toList());

        if (CollUtil.isNotEmpty(invalidStoreGuids)) {
            log.warn("以下门店未绑定京东：{}", invalidStoreGuids);
        }

        return validStoreGuids;
    }

    /**
     * 获取通用商品列表和分类信息
     */
    private List<UnMappedItem> getCommonItemsWithCategories(JdAuthDO jdAuthDO,String keywords) {
        // 查询商品列表
        QuerySkuInfoRequest querySkuInfoRequest = new QuerySkuInfoRequest(
                jdAuthDO.getAppKey(), 
                jdAuthDO.getAppSecret(), 
                jdAuthDO.getToken()
        );
        
        List<SkuMain> skuMainList = querySkuInfoRequest.execute(keywords);
        if (CollUtil.isEmpty(skuMainList)) {
            log.info("未查询到京东商品数据");
            return Collections.emptyList();
        }
        
        // 转换商品数据
        List<UnMappedItem> unMappedItems = transferUnMappedItem(skuMainList);
        
        // 查询分类信息
        QueryCategoriesRequest queryCategoriesRequest = new QueryCategoriesRequest(
                jdAuthDO.getAppKey(),
                jdAuthDO.getAppSecret(),
                jdAuthDO.getToken()
        );
        List<ShopCategory> categories = queryCategoriesRequest.execute();
        
        // 填充分类信息
        fillCategoryInfo(unMappedItems, categories);
        
        return unMappedItems;
    }
    
    /**
     * 填充商品分类信息
     */
    private void fillCategoryInfo(List<UnMappedItem> unMappedItems, List<ShopCategory> categories) {
        if (CollUtil.isEmpty(categories)) {
            return;
        }
        
        Map<Long, ShopCategory> categoryMap = categories.stream()
                .collect(Collectors.toMap(ShopCategory::getId, Function.identity(), (k1, k2) -> k1));
                
        // 使用并行流处理大量商品数据
        unMappedItems.parallelStream().forEach(item -> {
            if (StringUtils.isNotEmpty(item.getUnItemTypeId())) {
                try {
                    ShopCategory category = categoryMap.get(Long.parseLong(item.getUnItemTypeId()));
                    if (category != null) {
                        item.setUnItemTypeName(category.getShopCategoryName());
                    }
                } catch (NumberFormatException e) {
                    log.warn("分类ID格式错误：{}", item.getUnItemTypeId());
                }
            }
        });
    }
    
    /**
     * 获取商品映射关系
     * 优化：使用并行流处理大量数据，提前过滤空值
     */
    private Map<String, JdItemMappingDO> getItemMappings(List<String> storeGuids, List<UnMappedItem> items) {
        // 获取商品ID列表，过滤空值并去重
        List<String> skuIds = items.parallelStream()
                .map(UnMappedItem::getUnItemSkuId)
                .filter(StringUtils::isNotEmpty)
                .distinct()
                .collect(Collectors.toList());
        
        if (CollUtil.isEmpty(skuIds)) {
            return Collections.emptyMap();
        }
        
        // 批量查询映射关系
        List<JdItemMappingDO> mappings = jdItemMappingMapper.selectList(
                new LambdaQueryWrapper<JdItemMappingDO>()
                .in(JdItemMappingDO::getSkuId, skuIds)
                .in(JdItemMappingDO::getStoreGuid, storeGuids)
                .eq(JdItemMappingDO::getDeleted, false)
        );
        
        if (CollUtil.isEmpty(mappings)) {
            return Collections.emptyMap();
        }
        
        // 构建映射关系map，key为storeGuid_skuId，预分配容量
        Map<String, JdItemMappingDO> resultMap = new HashMap<>(mappings.size() * 4 / 3 + 1);
        mappings.parallelStream().forEach(mapping -> {
            String key = mapping.getStoreGuid() + "_" + mapping.getSkuId();
            resultMap.put(key, mapping);
        });
        
        return resultMap;
    }
    
    /**
     * 构建最终返回结果
     * 
     * @param commonItems 通用商品列表
     * @param storeGuids 门店GUID列表
     * @param mappingMap 映射关系Map
     * @param bindingFlag 绑定标志：null-查询所有，true-已绑定，false-未绑定
     * @return 过滤后的商品列表
     */
    private List<UnMappedItem> buildResultItems(
            List<UnMappedItem> commonItems, 
            List<String> storeGuids, 
            Map<String, JdItemMappingDO> mappingMap,
            Integer bindingFlag) {
        
        // 计算预期结果大小，避免List频繁扩容
        int expectedSize = commonItems.size() * storeGuids.size();
        List<UnMappedItem> resultList = new ArrayList<>(expectedSize);
        
        // 分批处理，避免大数据量时内存溢出
        int batchSize = 1000; // 每批处理1000个商品
        
        for (int i = 0; i < commonItems.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, commonItems.size());
            List<UnMappedItem> batchItems = commonItems.subList(i, endIndex);
            
            // 使用并行流处理当前批次
            List<UnMappedItem> batchResult = storeGuids.parallelStream()
                    .flatMap(storeGuid -> 
                        batchItems.stream().map(item -> {
                            // 为每个门店创建商品副本
                            UnMappedItem storeItem = copyUnMappedItem(item);
                            storeItem.setErpStoreGuid(storeGuid);
                            
                            // 设置映射关系
                            String mappingKey = storeGuid + "_" + storeItem.getUnItemSkuId();
                            JdItemMappingDO mapping = mappingMap.get(mappingKey);
                            
                            if (mapping != null) {
                                storeItem.setErpItemId(mapping.getItemGuid());
                                storeItem.setErpItemSkuId(mapping.getSkuGuid());
                            }
                            
                            return storeItem;
                        })
                    )
                    .filter(item -> shouldIncludeItem(item, bindingFlag)) // 统一过滤逻辑
                    .collect(Collectors.toList());
            
            resultList.addAll(batchResult);
        }
        
        log.info("批量查询京东商品数据完成，返回商品数量：{}", resultList.size());
        return resultList;
    }
    
    /**
     * 判断是否应该包含该商品项
     */
    private boolean shouldIncludeItem(UnMappedItem item, Integer bindingFlag) {
        if (bindingFlag == null || bindingFlag == 0) {
            return true; // 不过滤
        }
        
        // 判断是否已绑定：如果有erp商品ID则认为已绑定
        boolean isBound = StringUtils.isNotEmpty(item.getErpItemId());
        return (bindingFlag == 1 && isBound) || (bindingFlag == -1 && !isBound);
    }
    
    /**
     * 复制UnMappedItem对象
     * 使用BeanUtils提高复制性能
     */
    private UnMappedItem copyUnMappedItem(UnMappedItem source) {
        UnMappedItem target = new UnMappedItem();
        BeanUtils.copyProperties(source, target);
        return target;
    }

    @Override
    public void bindMapping(UnItemBindUnbindReq unItemBindUnbindReq) {
        //查询是否存在绑定，根据门店、外卖平台spu、sku查询
        JdItemMappingDO jdItemMappingDO = jdItemMappingMapper.selectOne(new LambdaQueryWrapper<JdItemMappingDO>()
                .eq(JdItemMappingDO::getStoreGuid, unItemBindUnbindReq.getStoreGuid())
                .eq(JdItemMappingDO::getSpuId, unItemBindUnbindReq.getUnItemId())
                .eq(JdItemMappingDO::getSkuId, unItemBindUnbindReq.getUnItemSkuId())
                .eq(JdItemMappingDO::getDeleted, false));
        //若存在则更新
        if(jdItemMappingDO != null) {
            jdItemMappingDO.setItemGuid(unItemBindUnbindReq.getErpItemGuid());
            jdItemMappingDO.setSkuGuid(unItemBindUnbindReq.getErpItemSkuId());
            jdItemMappingMapper.updateById(jdItemMappingDO);
            return;
        }
        //若不存在则新增
        JdItemMappingDO insertMapping = new JdItemMappingDO();
        insertMapping.setStoreGuid(unItemBindUnbindReq.getStoreGuid());
        insertMapping.setItemGuid(unItemBindUnbindReq.getErpItemGuid());
        insertMapping.setSkuGuid(unItemBindUnbindReq.getErpItemSkuId());
        insertMapping.setSkuId(unItemBindUnbindReq.getUnItemSkuId());
        insertMapping.setSpuId(unItemBindUnbindReq.getUnItemId());
        jdItemMappingMapper.insert(insertMapping);
    }

    @Override
    public void unbindMapping(UnItemBindUnbindReq unItemBindUnbindReq) {
        jdItemMappingMapper.unbind(unItemBindUnbindReq);
    }

    @Override
    public void batchUnbindMapping(UnItemBatchUnbindReq unItemBatchUnbindReq) {
        String replyType = unItemBatchUnbindReq.getBindFlag() ? "批量绑定商品映射" : "批量解绑商品映射";
        String storeGuid = unItemBatchUnbindReq.getStoreGuid();
        log.info("京东外卖{}开始，storeGuid: {}", replyType, storeGuid);
        
        // 校验参数
        if (CollUtil.isEmpty(unItemBatchUnbindReq.getUnItemUnbindList())) {
            log.warn("京东外卖{}，商品列表为空，storeGuid: {}", replyType, storeGuid);
            return;
        }
        
        try {
            // 构建单个操作的请求对象列表
            List<UnItemBindUnbindReq> bindUnbindReqList = convertToBindUnbindReqList(unItemBatchUnbindReq);
            
            // 根据bindFlag决定执行批量绑定还是批量解绑
            if (unItemBatchUnbindReq.getBindFlag()) {
                // 批量绑定
                batchBindMappings(bindUnbindReqList);
            } else {
                // 批量解绑
                batchUnbindMappings(bindUnbindReqList);
            }
            
            log.info("京东外卖{}完成，storeGuid: {}", replyType, storeGuid);
        } catch (Exception e) {
            log.error("京东外卖{}异常, storeGuid: {}, error: {}", replyType, storeGuid, e.getMessage(), e);
            throw new BusinessException("京东外卖" + replyType + "失败: " + e.getMessage());
        }
    }
    
    /**
     * 将UnItemBatchUnbindReq转换为UnItemBindUnbindReq列表
     */
    private List<UnItemBindUnbindReq> convertToBindUnbindReqList(UnItemBatchUnbindReq batchReq) {
        String storeGuid = batchReq.getStoreGuid();
        return batchReq.getUnItemUnbindList().stream()
                .map(item -> {
                    UnItemBindUnbindReq req = new UnItemBindUnbindReq();
                    req.setStoreGuid(storeGuid);
                    req.setUnItemId(item.getUnItemId());
                    req.setUnItemSkuId(item.getUnItemSkuId());
                    req.setUnItemTypeId(item.getUnItemTypeId());
                    req.setExtendValue(item.getExtendValue());
                    req.setErpItemGuid(item.getErpItemGuid());
                    req.setErpItemSkuId(item.getErpItemSkuId());
                    return req;
                })
                .collect(Collectors.toList());
    }
    
    /**
     * 从请求列表中提取基本信息
     */
    private RequestInfo extractRequestInfo(List<UnItemBindUnbindReq> reqList) {
        if (CollUtil.isEmpty(reqList)) {
            return null;
        }
        
        String storeGuid = reqList.get(0).getStoreGuid();
        List<String> spuIds = reqList.stream()
                .map(UnItemBindUnbindReq::getUnItemId)
                .collect(Collectors.toList());
        List<String> skuIds = reqList.stream()
                .map(UnItemBindUnbindReq::getUnItemSkuId)
                .collect(Collectors.toList());
                
        return new RequestInfo(storeGuid, spuIds, skuIds);
    }
    
    /**
     * 请求信息封装类
     */
    @Getter
    private static class RequestInfo {
        private final String storeGuid;
        private final List<String> spuIds;
        private final List<String> skuIds;
        
        public RequestInfo(String storeGuid, List<String> spuIds, List<String> skuIds) {
            this.storeGuid = storeGuid;
            this.spuIds = spuIds;
            this.skuIds = skuIds;
        }

    }
    
    /**
     * 批量绑定商品映射
     */
    private void batchBindMappings(List<UnItemBindUnbindReq> bindReqList) {
        if (CollUtil.isEmpty(bindReqList)) {
            return;
        }
        
        log.info("京东外卖批量绑定商品映射，数量: {}", bindReqList.size());
        
        // 提取请求信息
        RequestInfo reqInfo = extractRequestInfo(bindReqList);
        if (reqInfo == null) {
            log.warn("京东外卖批量绑定商品映射，请求信息提取失败");
            return;
        }
        
        // 查询已存在的映射关系
        List<JdItemMappingDO> existingMappings = jdItemMappingMapper.selectList(
                new LambdaQueryWrapper<JdItemMappingDO>()
                        .eq(JdItemMappingDO::getStoreGuid, reqInfo.getStoreGuid())
                        .in(JdItemMappingDO::getSkuId, reqInfo.getSkuIds())
                        .in(JdItemMappingDO::getSpuId, reqInfo.getSpuIds())
                        .eq(JdItemMappingDO::getDeleted, false)
        );
        
        // 将已存在的映射按SKU ID和SPU ID组织成Map
        Map<String, JdItemMappingDO> existingMappingMap = new HashMap<>();
        if (CollUtil.isNotEmpty(existingMappings)) {
            existingMappingMap = existingMappings.stream()
                    .collect(Collectors.toMap(
                            mapping -> mapping.getSkuId() + "_" + mapping.getSpuId(),
                            Function.identity(),
                            (v1, v2) -> v1
                    ));
        }
        
        // 收集需要更新的记录
        List<JdItemMappingDO> toUpdateList = new ArrayList<>();
        // 收集需要新增的记录
        List<JdItemMappingDO> toInsertList = new ArrayList<>();
        
        for (UnItemBindUnbindReq req : bindReqList) {
            String key = req.getUnItemSkuId() + "_" + req.getUnItemId();
            if (existingMappingMap.containsKey(key)) {
                // 更新已存在的映射
                JdItemMappingDO existingMapping = existingMappingMap.get(key);
                existingMapping.setItemGuid(req.getErpItemGuid());
                existingMapping.setSkuGuid(req.getErpItemSkuId());
                toUpdateList.add(existingMapping);
            } else {
                // 新增映射
                JdItemMappingDO newMapping = new JdItemMappingDO();
                newMapping.setStoreGuid(req.getStoreGuid());
                newMapping.setItemGuid(req.getErpItemGuid());
                newMapping.setSkuGuid(req.getErpItemSkuId());
                newMapping.setSkuId(req.getUnItemSkuId());
                newMapping.setSpuId(req.getUnItemId());
                newMapping.setDeleted(false);  // 确保设置为未删除
                toInsertList.add(newMapping);
            }
        }
        
        try {
            // 批量执行更新操作
            if (CollUtil.isNotEmpty(toUpdateList)) {
                log.info("京东外卖批量更新商品映射，数量: {}", toUpdateList.size());
                // 使用新的批量更新方法
                int updatedCount = jdItemMappingMapper.batchUpdate(toUpdateList);
                log.info("成功更新商品映射记录数量: {}", updatedCount);
            }
            
            // 批量执行插入操作
            if (CollUtil.isNotEmpty(toInsertList)) {
                log.info("京东外卖批量新增商品映射，数量: {}", toInsertList.size());
                // 使用新的批量插入方法
                int insertedCount = jdItemMappingMapper.batchInsert(toInsertList);
                log.info("成功新增商品映射记录数量: {}", insertedCount);
            }
            
            log.info("京东外卖批量绑定商品映射完成");
        } catch (Exception e) {
            log.error("京东外卖批量绑定商品映射异常: {}", e.getMessage(), e);
            throw new BusinessException("批量绑定失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量解绑商品映射
     */
    private void batchUnbindMappings(List<UnItemBindUnbindReq> unbindReqList) {
        if (CollUtil.isEmpty(unbindReqList)) {
            return;
        }
        
        log.info("京东外卖批量解绑商品映射，数量: {}", unbindReqList.size());
        
        // 提取请求信息
        RequestInfo reqInfo = extractRequestInfo(unbindReqList);
        if (reqInfo == null) {
            log.warn("京东外卖批量解绑商品映射，请求信息提取失败");
            return;
        }
        
        try {
            // 使用新的批量解绑方法，直接在数据库层面实现批量更新
            int unbindCount = jdItemMappingMapper.batchUnbind(
                    reqInfo.getStoreGuid(), 
                    reqInfo.getSkuIds(), 
                    reqInfo.getSpuIds()
            );
            
            log.info("成功解绑商品映射记录数量: {}", unbindCount);
        } catch (Exception e) {
            log.error("京东外卖批量解绑商品映射异常: {}", e.getMessage(), e);
            throw new BusinessException("批量解绑失败: " + e.getMessage());
        }
    }

    @Override
    public List<JdItemMappingDO> listItemMappingByStore(String storeGuid, List<String> skuIdList) {
        return jdItemMappingMapper.selectList(new LambdaQueryWrapper<JdItemMappingDO>().eq(JdItemMappingDO::getStoreGuid, storeGuid)
                .in(JdItemMappingDO::getSkuId, skuIdList).orderByDesc(JdItemMappingDO::getGmtModified));
    }
}
