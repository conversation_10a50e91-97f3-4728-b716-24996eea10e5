package com.holder.saas.store.takeaway.producers.interceptor;

import com.holder.saas.store.takeaway.producers.utils.ThreadCacheUserInfo;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

import static com.holderzone.saas.store.dto.common.CommonConstant.USER_INFO;

/**
 * <AUTHOR>
 * @version 1.0
 * @className FeignInterceptor
 * @date 2018/09/13 16:19
 * @description
 * @program holder-saas-store--store-info
 */
@Slf4j
@Deprecated
public class FeignInterceptor implements RequestInterceptor {

    @Override
    public void apply(RequestTemplate template) {
        try {
            if (StringUtils.hasText(ThreadCacheUserInfo.getJsonStr())) {
                template.header(USER_INFO, URLEncoder.encode(ThreadCacheUserInfo.getJsonStr(), "utf-8"));
            }
        } catch (UnsupportedEncodingException e) {
            log.error("添加userInfo头出错", e);
        }
    }
}
