package com.holder.saas.store.takeaway.producers.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.net.URLEncodeUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HtmlUtil;
import cn.hutool.http.HttpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holder.saas.store.takeaway.producers.config.JdConfig;
import com.holder.saas.store.takeaway.producers.constant.TakeoutConstant;
import com.holder.saas.store.takeaway.producers.entity.domain.JdAuthDO;
import com.holder.saas.store.takeaway.producers.entity.domain.JdStoreMappingDO;
import com.holder.saas.store.takeaway.producers.mapper.JdAuthMapper;
import com.holder.saas.store.takeaway.producers.mapper.JdStoreMappingMapper;
import com.holder.saas.store.takeaway.producers.service.JdAuthService;
import com.holder.saas.store.takeaway.producers.utils.UrlCodecUtils;
import com.holder.saas.store.takeaway.producers.utils.sdk.jd.GetStoreInfoByStationNoRequest;
import com.holder.saas.store.takeaway.producers.utils.sdk.jd.dto.RefreshTokenDTO;
import com.holder.saas.store.takeaway.producers.utils.sdk.jd.RefreshTokenRequest;
import com.holder.saas.store.takeaway.producers.utils.sdk.jd.dto.StoreInfo;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.takeaway.jd.CallBackDTO;
import com.holderzone.saas.store.dto.takeaway.request.StoreAuthByStoreReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutShopBindReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.StoreAuthDTO;
import com.holderzone.saas.store.dto.takeaway.response.TakeoutShopBindRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class JdAuthServiceImpl extends ServiceImpl<JdAuthMapper, JdAuthDO> implements JdAuthService {

    private final JdConfig jdConfig;

    private final JdStoreMappingMapper jdStoreMappingMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void tokenSetting(CallBackDTO callBackDTO) {
        //设置生效时间
        Instant instant = Instant.ofEpochMilli(Long.parseLong(callBackDTO.getTime()));
        LocalDateTime activeTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
        //设置过期时间
        Instant expireInstant = Instant.ofEpochMilli(Long.parseLong(callBackDTO.getTime()) + Long.parseLong(callBackDTO.getExpiresIn()) * 1000);
        LocalDateTime expireTime = LocalDateTime.ofInstant(expireInstant, ZoneId.systemDefault());
        JdAuthDO jdAuthDO = getOne(new LambdaQueryWrapper<JdAuthDO>().eq(JdAuthDO::getVenderId, callBackDTO.getVenderId()).last(" limit 1"));
        //透传字段
        String brandGuid = callBackDTO.getBrandGuid();
        String enterpriseGuid = callBackDTO.getEnterpriseGuid();
        if (jdAuthDO == null) {
            //新增授权
            JdAuthDO newJdAuthDO = new JdAuthDO();
            newJdAuthDO.setToken(callBackDTO.getToken());
            newJdAuthDO.setExpiresIn(callBackDTO.getExpiresIn());
            newJdAuthDO.setUserNick(callBackDTO.getUserNick());
            newJdAuthDO.setVenderId(callBackDTO.getVenderId());
            newJdAuthDO.setExpireTime(expireTime);
            newJdAuthDO.setActiveTime(activeTime);
            newJdAuthDO.setEnterpriseGuid(enterpriseGuid);
            newJdAuthDO.setBrandGuid(brandGuid);
            newJdAuthDO.setAppKey(callBackDTO.getAppKey());
            newJdAuthDO.setAppSecret(callBackDTO.getAppSecret());
            save(newJdAuthDO);
            return;
        }
        //若不是空则进行更新token，需确认是否是token续期回调(目前根据到期时间前30内为更新token)
        if (!callBackDTO.getToken().equals(jdAuthDO.getToken()) && expireTime.minusDays(30L).isBefore(LocalDateTime.now())) {
            new RefreshTokenRequest(callBackDTO.getAppKey(), callBackDTO.getAppSecret(), jdAuthDO.getToken())
                    .execute(RefreshTokenDTO.builder().newToken(callBackDTO.getToken())
                            .oldToken(jdAuthDO.getToken())
                            .appKey(callBackDTO.getAppKey()).build());
        }

    }

    @Override
    public void doBindStore(StoreAuthByStoreReqDTO storeAuthByStoreReqDTO) {
        if (StringUtils.isEmpty(storeAuthByStoreReqDTO.getBrandGuid())) {
            throw new BusinessException("参数错误，品牌guid不能为空");
        }
        //先根据商户id查询是否已经在开放平台授权
        JdAuthDO existAuth = getOne(new LambdaQueryWrapper<JdAuthDO>().eq(JdAuthDO::getBrandGuid, storeAuthByStoreReqDTO.getBrandGuid()).last(" limit 1"));
        if (existAuth == null) {
            throw new BusinessException("请先在商家平台授权应用");
        }
        //查询京东商家门店信息
        GetStoreInfoByStationNoRequest request = new GetStoreInfoByStationNoRequest(existAuth.getAppKey(), existAuth.getAppSecret(), existAuth.getToken());
        StoreInfo storeInfo = request.execute(storeAuthByStoreReqDTO.getPoiId());
        if (storeInfo == null) {
            throw new BusinessException("京东商家不存在此门店");
        }
        //查询门店是否绑定
        Integer count = jdStoreMappingMapper.selectCount(new LambdaQueryWrapper<JdStoreMappingDO>()
                .eq(JdStoreMappingDO::getStoreGuid, storeAuthByStoreReqDTO.getStoreGuid())
                .eq(JdStoreMappingDO::getDeleted, 0));
        if (count > 0) {
            throw new BusinessException("此门店已绑定");
        }
        JdStoreMappingDO jdStoreMappingDO = new JdStoreMappingDO();
        jdStoreMappingDO.setStoreGuid(storeAuthByStoreReqDTO.getStoreGuid());
        jdStoreMappingDO.setVenderStoreName(storeInfo.getStationName());
        jdStoreMappingDO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        jdStoreMappingDO.setVenderStoreId(storeAuthByStoreReqDTO.getPoiId());
        jdStoreMappingMapper.insert(jdStoreMappingDO);
    }

    @Override
    public void doUnbindStore(StoreAuthByStoreReqDTO storeAuthByStoreReqDTO) {
        //先根据商户id查询是否已经在开放平台授权
        JdAuthDO existAuth = getOne(new LambdaQueryWrapper<JdAuthDO>().eq(JdAuthDO::getBrandGuid, storeAuthByStoreReqDTO.getBrandGuid()));
        if (existAuth == null) {
            return;
        }
        jdStoreMappingMapper.delete(new LambdaQueryWrapper<JdStoreMappingDO>().eq(JdStoreMappingDO::getStoreGuid, storeAuthByStoreReqDTO.getStoreGuid()));
    }

    @Override
    public StoreAuthDTO getTakeoutAuth(StoreAuthDTO storeAuthDTO) {
        JdStoreMappingDO jdStoreMappingDO = jdStoreMappingMapper.selectOne(new LambdaQueryWrapper<JdStoreMappingDO>()
                .eq(JdStoreMappingDO::getStoreGuid, storeAuthDTO.getStoreGuid()));
        if (jdStoreMappingDO == null) {
            return storeAuthDTO.setBindingStatus(0);
        }
        return storeAuthDTO.setBindingStatus(1)
                .setShopId(jdStoreMappingDO.getVenderStoreId())
                .setShopName(jdStoreMappingDO.getVenderStoreName());
    }

    @Override
    public TakeoutShopBindRespDTO takeOutBindingUrl(TakeoutShopBindReqDTO takeoutShopBindReqDTO) {
        TakeoutShopBindRespDTO takeoutShopBindRespDTO = new TakeoutShopBindRespDTO();
        if (takeoutShopBindReqDTO.getBindingStatus() != TakeoutConstant.BINDING) {
            throw new BusinessException("京东外卖暂不支持在服务商解绑");
        }
        String url = String.format("%s&returnUrl=%s&invokeId=%s", jdConfig.getAuthUrl(),
                UrlCodecUtils.getURLEncoderString(jdConfig.getReturnUrl()),
                CallBackDTO.getStateUsingGuid(takeoutShopBindReqDTO));
        takeoutShopBindRespDTO.setUrl(url);
        return takeoutShopBindRespDTO;
    }

    @Override
    public JdAuthDO getJdAuthByToken(String token) {
        return getOne(new LambdaQueryWrapper<JdAuthDO>().eq(JdAuthDO::getToken, token).eq(JdAuthDO::getDeleted, 0).last("limit 1"));
    }

    @Override
    public JdAuthDO getJdAuthByBrand(String brandGuid) {
        return getOne(new LambdaQueryWrapper<JdAuthDO>().eq(JdAuthDO::getBrandGuid, brandGuid)
                .eq(JdAuthDO::getDeleted, 0).last("limit 1"));
    }

    @Override
    public List<JdAuthDO> listJdAuthByBrand(List<String> brandGuidList) {
        return list(new LambdaQueryWrapper<JdAuthDO>().eq(JdAuthDO::getDeleted, 0).in(JdAuthDO::getBrandGuid, brandGuidList));
    }

    @Override
    public List<String> authBrand(List<String> brandGuidList) {
        List<JdAuthDO> jdAuthDOS = listJdAuthByBrand(brandGuidList);
        if (CollUtil.isEmpty(jdAuthDOS)) {
            return Collections.emptyList();
        }
        return jdAuthDOS.stream().map(JdAuthDO::getBrandGuid).collect(Collectors.toList());
    }
}
