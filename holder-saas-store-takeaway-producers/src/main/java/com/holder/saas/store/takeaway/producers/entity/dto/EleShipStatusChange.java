package com.holder.saas.store.takeaway.producers.entity.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
public class EleShipStatusChange implements Serializable {

    private static final long serialVersionUID = 3728858581285924686L;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 店铺id
     */
    private long shopId;

    /**
     * 运单主状态，参阅运单主状态枚举定义
     */
    private String state;

    /**
     * 运单子状态，参阅运单子状态枚举定义
     */
    private String subState;

    /**
     * 配送员姓名
     */
    private String name;

    /**
     * 配送员联系方式
     */
    private String phone;

    /**
     * 状态变更的时间戳，单位毫秒
     */
    private long updateAt;
}
