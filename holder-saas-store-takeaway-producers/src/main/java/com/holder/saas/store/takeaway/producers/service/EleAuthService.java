package com.holder.saas.store.takeaway.producers.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.holder.saas.store.takeaway.producers.entity.domain.EleAuthDO;
import com.holderzone.saas.store.dto.takeaway.EleCallbackBindDTO;
import com.holderzone.saas.store.dto.takeaway.UnOrder;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutShopBindReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.StoreAuthDTO;
import com.holderzone.saas.store.dto.takeaway.response.TakeoutShopBindRespDTO;
import eleme.openapi.sdk.api.entity.other.OMessage;
import eleme.openapi.sdk.api.entity.packs.ShopContract;
import eleme.openapi.sdk.api.exception.ServiceException;
import eleme.openapi.sdk.oauth.response.Token;
import org.springframework.data.util.Pair;
import org.springframework.lang.Nullable;

import java.util.List;
import java.util.Map;

public interface EleAuthService extends IService<EleAuthDO> {

    TakeoutShopBindRespDTO takeOutBindingUrl(TakeoutShopBindReqDTO takeoutShopBindReqDTO);

    void bindCallback(EleCallbackBindDTO eleCallbackBindDTO);

    void unbindCallback(OMessage oMessage);

    void saveToken(String autoCode, String enterpriseGuid, String storeGuid) throws Exception;

    Token getTokenByShopId(Long shopId);

    @Nullable
    Token getToken(long userId);

    @Nullable
    Token getToken(String storeGuid);

    @Nullable
    Token getToken(EleAuthDO eleAuthDO);

    Map<String, Pair<EleAuthDO, Token>> getTokens(List<String> storeGuids);

    void refreshToken(long beforeTime);

    Token refreshToken(EleAuthDO eleAuthDO);

    List<StoreAuthDTO> listAuth(List<StoreAuthDTO> storeAuthorizationDTOList);

    StoreAuthDTO getTakeoutAuth(StoreAuthDTO storeAuthDTO);

    Boolean updateDelivery(StoreAuthDTO storeAuthDTO);

    void correctAuth(ServiceException e, EleAuthDO eleAuthDO);

    void correctAuthThenThrow(ServiceException e, UnOrder unOrder);

    ShopContract getEffectServicePackContract(UnOrder unOrder);
}
