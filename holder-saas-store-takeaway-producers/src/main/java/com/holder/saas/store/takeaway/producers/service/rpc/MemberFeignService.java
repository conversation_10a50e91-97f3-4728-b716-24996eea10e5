package com.holder.saas.store.takeaway.producers.service.rpc;

import com.holder.saas.store.takeaway.producers.entity.dto.MemberResultDTO;
import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.member.PlatformMemberConsumeRecordDTO;
import com.holderzone.saas.store.dto.member.PlatformMemberDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 */
@Component
@FeignClient(value = "holder-saas-member-merchant", fallbackFactory = MemberFeignService.ServiceFallBack.class)
public interface MemberFeignService {

    @PostMapping("/platform_member/judge_add")
    MemberResultDTO<Boolean> judgeAndMember(PlatformMemberDTO platformMember);

    @PostMapping("/platform_member/consume_record_add")
    MemberResultDTO<Void> createConsumeRecord(@RequestBody PlatformMemberConsumeRecordDTO platformMemberConsumeRecordDTO);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<MemberFeignService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public MemberFeignService create(Throwable cause) {
            return new MemberFeignService() {
                @Override
                public MemberResultDTO<Boolean> judgeAndMember(PlatformMemberDTO platformMember) {
                    log.error(HYSTRIX_PATTERN, "judgeAndMember",
                            JacksonUtils.writeValueAsString(platformMember),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public MemberResultDTO<Void> createConsumeRecord(PlatformMemberConsumeRecordDTO platformMemberConsumeRecordDTO) {
                    log.error(HYSTRIX_PATTERN, "createConsumeRecord",
                            JacksonUtils.writeValueAsString(platformMemberConsumeRecordDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }
            };
        }

    }
}
