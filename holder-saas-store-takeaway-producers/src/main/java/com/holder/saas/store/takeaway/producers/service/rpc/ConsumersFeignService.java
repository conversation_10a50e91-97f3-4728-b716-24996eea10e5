package com.holder.saas.store.takeaway.producers.service.rpc;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.takeaway.OwnCallbackResponse;
import com.holderzone.saas.store.dto.takeaway.TakeoutOrderDTO;
import com.holderzone.saas.store.dto.takeaway.ThirdCarrierUpdateDTO;
import com.holderzone.saas.store.dto.takeaway.request.OrderCallDTO;
import com.holderzone.saas.store.dto.takeaway.request.SalesUpdateDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderRocketListener
 * @date 2018/11/15 12:00
 * @description 组织RPC服务
 * @program holder-saas-store-takeaway
 */
@Component
@FeignClient(value = "holder-saas-takeaway-consumer", fallbackFactory = ConsumersFeignService.ServiceFallBack.class)
public interface ConsumersFeignService {

    /**
     * 根据门店guid查询企业guid
     *
     * @param salesUpdateDTO
     * @return
     */
    @Deprecated
    @PostMapping("/takeout/order_update")
    OwnCallbackResponse orderUpdate(@RequestBody SalesUpdateDTO salesUpdateDTO);

    @PostMapping("/takeout/update_third_carrier_id")
    void updateThirdCarrierId(@RequestBody ThirdCarrierUpdateDTO carrierUpdate);

    @ApiOperation(value = "修改更新电信订单记录")
    @PostMapping("/takeout/updateCallOrder")
    void updateCallOrder(@RequestBody OrderCallDTO orderCallDTO);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<ConsumersFeignService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public ConsumersFeignService create(Throwable cause) {
            return new ConsumersFeignService() {

                @Override
                public OwnCallbackResponse orderUpdate(SalesUpdateDTO salesUpdateDTO) {
                    log.error(HYSTRIX_PATTERN, "orderUpdate", JacksonUtils.writeValueAsString(salesUpdateDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public void updateThirdCarrierId(ThirdCarrierUpdateDTO carrierUpdate) {
                    log.error(HYSTRIX_PATTERN, "updateThirdCarrierId", JacksonUtils.writeValueAsString(carrierUpdate),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public void updateCallOrder(OrderCallDTO orderCallDTO) {
                    log.error(HYSTRIX_PATTERN, "updateCallOrder", JacksonUtils.writeValueAsString(orderCallDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

            };
        }

    }
}
