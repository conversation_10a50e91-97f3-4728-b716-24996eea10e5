package com.holder.saas.store.takeaway.producers.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @className UnItemMappingServiceFactory
 * @date 2018/11/15 12:00
 * @description UnItem映射服务工厂
 * @program holder-saas-store-takeaway
 */
@Component
public class UnItemMappingServiceFactory {

    private final UnItemMappingService mtItemMappingService;

    private final UnItemMappingService eleItemMappingService;

    private final UnItemMappingService ownItemMappingService;

    private final UnItemMappingService zcItemMappingService;

    private final UnItemMappingService jdItemMappingService;

    @Autowired
    public UnItemMappingServiceFactory(@Qualifier("mtItemMappingServiceImpl") UnItemMappingService mtItemMappingService,
                                       @Qualifier("eleItemMappingServiceImpl") UnItemMappingService eleItemMappingService,
                                       @Qualifier("ownItemMappingServiceImpl") UnItemMappingService ownItemMappingService,
                                       @Qualifier("tcdItemMappingServiceImpl") UnItemMappingService zcItemMappingService,
                                       @Qualifier("jdItemMappingServiceImpl") UnItemMappingService jdItemMappingService) {
        this.mtItemMappingService = mtItemMappingService;
        this.eleItemMappingService = eleItemMappingService;
        this.ownItemMappingService = ownItemMappingService;
        this.zcItemMappingService = zcItemMappingService;
        this.jdItemMappingService = jdItemMappingService;
    }

    public UnItemMappingService create(int type) {
        switch (type) {
            case 0:
                return mtItemMappingService;
            case 1:
                return eleItemMappingService;
            case 2:
                return ownItemMappingService;
            case 3:
                return zcItemMappingService;
            case 5:
                return jdItemMappingService;
            default:
                throw new UnsupportedOperationException("暂不支持除美团、饿了么以外的外卖业务[" + type + "]");
        }
    }
}
