package com.holder.saas.store.takeaway.producers.utils.sdk.jd.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

@Data
public class OrderQueryRspDTO {

    /**
     * 当前页数
     */
    private Integer pageNo;

    /**
     * 每页条数
     */
    private Integer pageSize;

    /**
     * 订每页最大条数
     */
    private Integer maxPageSize;

    /**
     * 数据总条数
     */
    private Integer totalCount;

    /**
     * 包含需要查询订单的List列表
     */
    private List<OrderInfoDTO> resultList;
}
