package com.holder.saas.store.takeaway.producers.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holder.saas.store.takeaway.producers.entity.domain.TcdAuthDO;
import com.holder.saas.store.takeaway.producers.mapper.TcdAuthMapper;
import com.holder.saas.store.takeaway.producers.service.MtAuthService;
import com.holder.saas.store.takeaway.producers.service.UnOrderMqService;
import com.holder.saas.store.takeaway.producers.service.UnOrderReplyService;
import com.holder.saas.store.takeaway.producers.utils.BigDecimalUtil;
import com.holder.saas.store.takeaway.producers.utils.DeliveryUtils;
import com.holder.saas.store.takeaway.producers.utils.HttpRequestUtils;
import com.holder.saas.store.takeaway.producers.utils.TimeUtil;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.takeaway.*;
import com.holderzone.saas.store.dto.takeaway.request.*;
import com.holderzone.saas.store.dto.takeaway.response.TcdCommonRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;

@Slf4j
@Service("tcdOrderReplyServiceImpl")
public class TcdOrderReplyServiceImpl extends ServiceImpl<TcdAuthMapper, TcdAuthDO> implements UnOrderReplyService {

    private final String platformName = "赚餐自营";

    @Value("${zc.URL}")
    private String zcUrl;

    @Value("${zc.ORDER_ACCEPT_OR_REFUSE_URL}")
    private String orderAcceptOrRefuseUrl;

    @Value("${zc.ORDER_AGREE_OR_DISAGREE_URL}")
    private String orderAgreeOrDisagreeUrl;

    @Value("${small.ORDER_DELIVERY}")
    private String deliveryUrl;

    @Value("${zc.ORDER_DELIVERY_ACCEPT_URL}")
    private String deliveryAcceptUrl;

    @Value("${zc.ORDER_UPDATE_PERSON_URL}")
    private String updatePersonUrl;

    @Value("${zc.ORDER_DELIVERY_FINISH_URL}")
    private String deliveryFinishUrl;

    @Value("${zc.ORDER_KNIGHT_ACCEPT}")
    private String knightAcceptUrl;

    @Value("${small.ORDER_CANCEL_DELIVERY}")
    private String cancelDeliveryUrl;


    @Override
    public void replyCancelOrder(UnOrder unOrder) {
        String msgType = "商家拒单";
        logReplyProcessing(unOrder, msgType);
        String token = checkToken(unOrder.getStoreGuid());
        //调用赚餐拒单接口
        TakeoutTCDAcceptOrRefuseReqDTO takeoutTCDAcceptOrRefuseReqDTO = new TakeoutTCDAcceptOrRefuseReqDTO();
        takeoutTCDAcceptOrRefuseReqDTO.setOrderSn(unOrder.getOrderId());
        takeoutTCDAcceptOrRefuseReqDTO.setOrderState(TCDOrderStatus.CANCELLED);
        takeoutTCDAcceptOrRefuseReqDTO.setToken(token);
        takeoutTCDAcceptOrRefuseReqDTO.setRefuseReason(unOrder.getCancelReason() != null ? unOrder.getCancelReason() : "其他原因");
        takeoutTCDAcceptOrRefuseReqDTO.setRefuseTime(unOrder.getRefundReqTime());

        String jsonParam = JacksonUtils.writeValueAsString(takeoutTCDAcceptOrRefuseReqDTO);
        log.info("【赚餐外卖平台】商家拒单参数：url={},param={}", zcUrl + orderAcceptOrRefuseUrl, jsonParam);
        String result = HttpRequestUtils.sendHttpRequestPost(zcUrl + orderAcceptOrRefuseUrl, jsonParam);
        log.info("【赚餐外卖平台】商家拒单结果：result={}", result);
        if (result == null) {
            log.error("【赚餐外卖平台】商家拒单失败，赚餐平台无响应，storeGuid={},orderId={}", unOrder.getStoreGuid(), unOrder.getOrderId());
            return;
        }
        TcdCommonRespDTO tcdCommonRespDTO = JacksonUtils.toObject(TcdCommonRespDTO.class, result);
        if (tcdCommonRespDTO.getCode() != 200) {
            log.error("【赚餐外卖平台】商家拒单失败，message={}", tcdCommonRespDTO.getMessage());
        } else {
            logReplySucceed(unOrder, msgType);
        }
    }

    @Override
    public void replyConfirmOrder(UnOrder unOrder) {
        String msgType = "商家接单";
        logReplyProcessing(unOrder, msgType);
        String token = checkToken(unOrder.getStoreGuid());
        //调用赚餐接单接口
        TakeoutTCDAcceptOrRefuseReqDTO takeoutTCDAcceptOrRefuseReqDTO = new TakeoutTCDAcceptOrRefuseReqDTO();
        takeoutTCDAcceptOrRefuseReqDTO.setOrderSn(unOrder.getOrderId());
        takeoutTCDAcceptOrRefuseReqDTO.setOrderState(TCDOrderStatus.COOKING);
        takeoutTCDAcceptOrRefuseReqDTO.setToken(token);

        String jsonParam = JacksonUtils.writeValueAsString(takeoutTCDAcceptOrRefuseReqDTO);
        log.info("【赚餐外卖平台】商家接单参数：url={},param={}", zcUrl + orderAcceptOrRefuseUrl, jsonParam);
        String result = HttpRequestUtils.sendHttpRequestPost(zcUrl + orderAcceptOrRefuseUrl, jsonParam);
        log.info("【赚餐外卖平台】商家接单结果：result={}", result);
        if (result == null) {
            log.error("【赚餐外卖平台】商家接单失败，storeGuid={},orderId={}", unOrder.getStoreGuid(), unOrder.getOrderId());
            return;
        }
        TcdCommonRespDTO tcdCommonRespDTO = JacksonUtils.toObject(TcdCommonRespDTO.class, result);
        if (tcdCommonRespDTO.getCode() != 200) {
            log.error("【赚餐外卖平台】商家接单失败，message={}", tcdCommonRespDTO.getMessage());
        } else {
            logReplySucceed(unOrder, msgType);
        }
    }

    @Override
    public void replyAgreeCancelOrder(UnOrder unOrder) {

    }

    @Override
    public void replyDisagreeCancelOrder(UnOrder unOrder) {

    }

    @Override
    public void replyAgreeRefundOrder(UnOrder unOrder) {
        String msgType = "商家同意取消订单";
        logReplyProcessing(unOrder, msgType);
        String token = checkToken(unOrder.getStoreGuid());
        //调用赚餐同意取消接口
        TakeoutTCDAcceptOrRefuseReqDTO takeoutTCDAcceptOrRefuseReqDTO = new TakeoutTCDAcceptOrRefuseReqDTO();
        takeoutTCDAcceptOrRefuseReqDTO.setOrderSn(unOrder.getOrderId());
        takeoutTCDAcceptOrRefuseReqDTO.setOrderState(TCDOrderStatus.CANCELLED);
        takeoutTCDAcceptOrRefuseReqDTO.setToken(token);
        takeoutTCDAcceptOrRefuseReqDTO.setRefuseReason(unOrder.getCancelReason() != null ? unOrder.getCancelReason() : "其他原因");

        String jsonParam = JacksonUtils.writeValueAsString(takeoutTCDAcceptOrRefuseReqDTO);
        log.info("【赚餐外卖平台】商家同意取消订单参数：url={},param={}", zcUrl + orderAgreeOrDisagreeUrl, jsonParam);
        String result = HttpRequestUtils.sendHttpRequestPost(zcUrl + orderAgreeOrDisagreeUrl, jsonParam);
        log.info("【赚餐外卖平台】商家同意取消订单结果：result={}", result);
        if (result == null) {
            log.error("【赚餐外卖平台】商家同意取消订单失败，storeGuid={},orderGuid={}", unOrder.getStoreGuid(), unOrder.getOrderId());
            return;
        }
        TcdCommonRespDTO tcdCommonRespDTO = JacksonUtils.toObject(TcdCommonRespDTO.class, result);
        if (tcdCommonRespDTO.getCode() != 200) {
            log.error("【赚餐外卖平台】商家同意取消订单失败，message={}", tcdCommonRespDTO.getMessage());
        } else {
            logReplySucceed(unOrder, msgType);
        }
    }

    @Override
    public void replyDisagreeRefundOrder(UnOrder unOrder) {
        String msgType = "商家不同意取消订单";
        logReplyProcessing(unOrder, msgType);
        String token = checkToken(unOrder.getStoreGuid());
        //调用赚餐同意取消接口
        TakeoutTCDAcceptOrRefuseReqDTO takeoutTCDAcceptOrRefuseReqDTO = new TakeoutTCDAcceptOrRefuseReqDTO();
        takeoutTCDAcceptOrRefuseReqDTO.setOrderSn(unOrder.getOrderId());
        takeoutTCDAcceptOrRefuseReqDTO.setOrderState(TCDOrderStatus.COOKING);
        takeoutTCDAcceptOrRefuseReqDTO.setToken(token);
        takeoutTCDAcceptOrRefuseReqDTO.setRefuseReason(unOrder.getCancelReason() != null ? unOrder.getCancelReason() : "其他原因");

        String jsonParam = JacksonUtils.writeValueAsString(takeoutTCDAcceptOrRefuseReqDTO);
        log.info("【赚餐外卖平台】商家不同意取消订单参数：url={},param={}", zcUrl + orderAgreeOrDisagreeUrl, jsonParam);
        String result = HttpRequestUtils.sendHttpRequestPost(zcUrl + orderAgreeOrDisagreeUrl, jsonParam);
        log.info("【赚餐外卖平台】商家不同意取消订单结果：result={}", result);
        if (result == null) {
            log.error("【赚餐外卖平台】商家不同意取消订单失败，storeGuid={},orderGuid={}", unOrder.getStoreGuid(), unOrder.getOrderId());
            return;
        }
        TcdCommonRespDTO tcdCommonRespDTO = JacksonUtils.toObject(TcdCommonRespDTO.class, result);
        if (tcdCommonRespDTO.getCode() != 200) {
            log.error("【赚餐外卖平台】商家不同意取消订单失败，message={}", tcdCommonRespDTO.getMessage());
        } else {
            logReplySucceed(unOrder, msgType);
        }
    }

    @Override
    public void replyUrgeOrder(UnOrder unOrder) {

    }

    @Override
    public void startDelivery(UnOrder unOrder) {

    }

    @Override
    public OwnApiResult startDeliveryMQ(UnOrder unOrder) {
        return  DeliveryUtils.startDelivery(unOrder,platformName,deliveryUrl);
    }

    @Override
    public void cancelDelivery(UnOrder unOrder) {
        DeliveryUtils.cancelDelivery(unOrder,platformName,cancelDeliveryUrl);
    }

    @Override
    public void replyDeliveryAccept(UnOrder unOrder) {
        //fixme 骑手接单后，将骑手信息上报赚餐平台
        String msgType = "上报骑手信息";
        String storeGuid = unOrder.getStoreGuid();
        String orderId = unOrder.getOrderId();
        log.info("(赚餐外卖平台){}，orderId: {}，处理中", msgType, orderId);
        String token = checkToken(storeGuid);
        TakeoutTCDAcceptOrRefuseReqDTO takeoutTCDAcceptOrRefuseReqDTO = new TakeoutTCDAcceptOrRefuseReqDTO();
        takeoutTCDAcceptOrRefuseReqDTO.setOrderSn(orderId);
        takeoutTCDAcceptOrRefuseReqDTO.setOrderState(TCDOrderStatus.COOKING);
        takeoutTCDAcceptOrRefuseReqDTO.setDeliveryPersonName(unOrder.getShipperName());
        takeoutTCDAcceptOrRefuseReqDTO.setDeliveryPersonPhone(unOrder.getShipperPhone());
        takeoutTCDAcceptOrRefuseReqDTO.setToken(token);
        //调用赚餐出餐接口
        String jsonParam = JacksonUtils.writeValueAsString(takeoutTCDAcceptOrRefuseReqDTO);
        log.info("【赚餐外卖平台】上报骑手信息参数：url={},param={}", zcUrl + deliveryAcceptUrl, jsonParam);
        String result = HttpRequestUtils.sendHttpRequestPost(zcUrl + deliveryAcceptUrl, jsonParam);
        log.info("【赚餐外卖平台】上报骑手信息结果：result={}", result);
        if (result == null) {
            log.error("【赚餐外卖平台】上报骑手信息失败，storeGuid={},orderId={}", storeGuid, orderId);
        }
        TcdCommonRespDTO tcdCommonRespDTO = JacksonUtils.toObject(TcdCommonRespDTO.class, result);
        if (tcdCommonRespDTO.getCode() != 200) {
            log.error("【赚餐外卖平台】商家出餐失败，message={}", tcdCommonRespDTO.getMessage());
        } else {
            log.error("【赚餐外卖平台】商家出餐成功，orderId={}", takeoutTCDAcceptOrRefuseReqDTO.getOrderSn());
        }
    }

    @Override
    public void replyDeliveryStart(UnOrder unOrder) {
        String msgType = "骑手已分配";
        logReplyProcessing(unOrder, msgType);
        String token = checkToken(unOrder.getStoreGuid());
        //调用赚餐同意取消接口
        TakeoutTCDAcceptOrRefuseReqDTO takeoutTCDAcceptOrRefuseReqDTO = new TakeoutTCDAcceptOrRefuseReqDTO();
        takeoutTCDAcceptOrRefuseReqDTO.setOrderSn(unOrder.getOrderId());
        takeoutTCDAcceptOrRefuseReqDTO.setToken(token);
        takeoutTCDAcceptOrRefuseReqDTO.setOrderState(TCDOrderStatus.COOKING);
        takeoutTCDAcceptOrRefuseReqDTO.setRefuseReason(unOrder.getCancelReason() != null ? unOrder.getCancelReason() : "其他原因");

        String jsonParam = JacksonUtils.writeValueAsString(takeoutTCDAcceptOrRefuseReqDTO);
        log.info("【赚餐外卖平台】骑手已分配参数：url={},param={}", zcUrl + deliveryAcceptUrl, jsonParam);
        String result = HttpRequestUtils.sendHttpRequestPost(zcUrl + deliveryAcceptUrl, jsonParam);
        log.info("【赚餐外卖平台】骑手已分配结果：result={}", result);
        if (result == null) {
            log.error("【赚餐外卖平台】骑手已分配失败，storeGuid={},orderGuid={}", unOrder.getStoreGuid(), unOrder.getOrderId());
        }
        TcdCommonRespDTO tcdCommonRespDTO = JacksonUtils.toObject(TcdCommonRespDTO.class, result);
        if (tcdCommonRespDTO.getCode() != 200) {
            log.error("【赚餐外卖平台】骑手已分配失败，message={}", tcdCommonRespDTO.getMessage());
        } else {
            logReplySucceed(unOrder, msgType);
        }
    }

    @Override
    public void replyDeliveryCancel(UnOrder unOrder) {

    }

    @Override
    public void replyDeliveryComplete(UnOrder unOrder) {
        String msgType = "骑手已送达";
        logReplyProcessing(unOrder, msgType);
        String token = checkToken(unOrder.getStoreGuid());
        //调用赚餐同意取消接口
        TakeoutTCDAcceptOrRefuseReqDTO takeoutTCDAcceptOrRefuseReqDTO = new TakeoutTCDAcceptOrRefuseReqDTO();
        takeoutTCDAcceptOrRefuseReqDTO.setOrderSn(unOrder.getOrderId());
        takeoutTCDAcceptOrRefuseReqDTO.setToken(token);
        takeoutTCDAcceptOrRefuseReqDTO.setOrderState(TCDOrderStatus.FINISH);
        takeoutTCDAcceptOrRefuseReqDTO.setRefuseReason(unOrder.getCancelReason() != null ? unOrder.getCancelReason() : "其他原因");

        String jsonParam = JacksonUtils.writeValueAsString(takeoutTCDAcceptOrRefuseReqDTO);
        log.info("【赚餐外卖平台】骑手已送达参数：url={},param={}", zcUrl + deliveryFinishUrl, jsonParam);
        String result = HttpRequestUtils.sendHttpRequestPost(zcUrl + deliveryFinishUrl, jsonParam);
        log.info("【赚餐外卖平台】骑手已送达结果：result={}", result);
        if (result == null) {
            log.error("【赚餐外卖平台】骑手已送达失败，storeGuid={},orderGuid={}", unOrder.getStoreGuid(), unOrder.getOrderId());
        }
        TcdCommonRespDTO tcdCommonRespDTO = JacksonUtils.toObject(TcdCommonRespDTO.class, result);
        if (tcdCommonRespDTO.getCode() != 200) {
            log.error("【赚餐外卖平台】骑手已送达失败，message={}", tcdCommonRespDTO.getMessage());
        } else {
            logReplySucceed(unOrder, msgType);
        }
    }

    @Override
    public void replyRiderPosition(UnOrder unOrder) {
        String msgType = "更新骑手信息";
        String orderId = unOrder.getOrderId();
        String shipperName = unOrder.getShipperName();
        String shipperPhone = unOrder.getShipperPhone();
        if(null == orderId){
            log.error("【赚餐外卖平台】更新骑手信息失败,orderId为空");
            return ;
        }
        if(StringUtils.isEmpty(shipperName) && StringUtils.isEmpty(shipperPhone)){
            log.info("骑手信息为空");
            return ;
        }
        log.info("(赚餐外卖平台){}，orderId: {}，处理中", msgType, orderId);
        TakeoutTCDAcceptOrRefuseReqDTO takeoutTCDAcceptOrRefuseReqDTO = new TakeoutTCDAcceptOrRefuseReqDTO();
        takeoutTCDAcceptOrRefuseReqDTO.setOrderSn(orderId);
        takeoutTCDAcceptOrRefuseReqDTO.setDeliveryPersonName(shipperName);
        takeoutTCDAcceptOrRefuseReqDTO.setDeliveryPersonPhone(shipperPhone);
        //调用更新赚餐订单接口
        String jsonParam = JacksonUtils.writeValueAsString(takeoutTCDAcceptOrRefuseReqDTO);
        log.info("【赚餐外卖平台】更新骑手信息参数：url={},param={}", zcUrl + updatePersonUrl, jsonParam);
        String result = HttpRequestUtils.sendHttpRequestPost(zcUrl + updatePersonUrl, jsonParam);
        log.info("【赚餐外卖平台】更新骑手信息结果：result={}", result);
        if (result == null) {
            log.error("【赚餐外卖平台】更新骑手信息失败,orderId={}", orderId);
        }
    }

    private void logReplyProcessing(UnOrder unOrder, String msgType) {
        log.info("Reply(赚餐外卖平台){}，orderId: {}，处理中", msgType, unOrder.getOrderId());
    }

    private void logReplySucceed(UnOrder unOrder, String msgType) {
        log.info("Reply(赚餐外卖平台){}，orderId: {}，处理成功", msgType, unOrder.getOrderId());
    }

    private void logTokenUnavailableThenThrow(UnOrder unOrder, String msgType) {
        log.error("Reply(赚餐外卖平台){}，orderId: {}，处理失败: 根据storeGuid: {} 未查询到Token",
                msgType, unOrder.getOrderId(), unOrder.getStoreGuid());
        throw new BusinessException("业务失败：" + "门店未绑定");
    }


    private String checkToken(String storeGuid) {
        TcdAuthDO tcdAuthDO = null;
        if (!StringUtils.isEmpty(storeGuid)) {
            tcdAuthDO = getOne(new LambdaQueryWrapper<TcdAuthDO>()
                    .eq(TcdAuthDO::getStoreGuid, storeGuid));
            if (tcdAuthDO == null) {
                throw new BusinessException("门店未绑定!请绑定门店后重试!");
            } else {
                return tcdAuthDO.getAccessToken();
            }
        } else {
            throw new BusinessException("storeGuid为空！");
        }
    }
}
