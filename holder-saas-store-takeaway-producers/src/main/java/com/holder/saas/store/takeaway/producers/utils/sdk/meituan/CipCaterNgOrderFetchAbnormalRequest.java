package com.holder.saas.store.takeaway.producers.utils.sdk.meituan;


import com.holder.saas.store.takeaway.producers.utils.TimeUtil;
import com.sankuai.sjst.platform.developer.domain.RequestDomain;
import com.sankuai.sjst.platform.developer.domain.RequestMethod;
import com.sankuai.sjst.platform.developer.request.CipCaterStringPairRequest;

import java.util.HashMap;
import java.util.Map;

/**
 * 批量拉取异常订单
 */
public class CipCaterNgOrderFetchAbnormalRequest extends CipCaterStringPairRequest {
    private Integer developerId;
    private Integer version = 2;

    /**
     * 异常订单类型 1: 推单失败 2: 超时取消
     */
    private Integer type;
    /**
     * 开始时间点时间戳，不得早于当前时间7天前
     */
    private Integer startTime;
    /**
     * 结束时间点时间戳，不得晚于当前时间
     */
    private Integer endTime;
    /**
     * 分页拉取偏移量
     */
    private Integer offset;
    /**
     * 每页拉取数量，不得超过200
     */
    private Integer limit;

    public CipCaterNgOrderFetchAbnormalRequest() {
        this.url = RequestDomain.preUrl.getValue() + "/waimai/ng/order/batchFetchAbnormalOrder";
        this.requestMethod = RequestMethod.POST;
        this.timestamp = String.valueOf(TimeUtil.getIntTimestamp());
    }

    @Override
    public boolean sharedParamsAbsent() {
        if (this.requestSysParams == null) {
            return true;
        } else {
            boolean charsetAbsent = this.requestSysParams.getCharset() == null || this.requestSysParams.getCharset().trim().isEmpty();
            boolean secretAbsent = this.requestSysParams.getSecret() == null || this.requestSysParams.getSecret().trim().isEmpty();
            return charsetAbsent || secretAbsent;
        }
    }

    @Override
    public Map<String, String> getSharedParams() {
        Map<String, String> sysParams = new HashMap();
        sysParams.put("timestamp", this.timestamp);
        sysParams.put("charset", this.requestSysParams.getCharset());
        return sysParams;
    }

    @Override
    public Map<String, String> getParams() {
        Map<String, String>  param = new HashMap<>(8);
        param.put("developerId", CipCaterNgOrderFetchAbnormalRequest.this.developerId.toString());
        param.put("version", CipCaterNgOrderFetchAbnormalRequest.this.version.toString());
        param.put("type", CipCaterNgOrderFetchAbnormalRequest.this.type.toString());
        param.put("startTime", CipCaterNgOrderFetchAbnormalRequest.this.startTime.toString());
        param.put("endTime", CipCaterNgOrderFetchAbnormalRequest.this.endTime.toString());
        param.put("offset", CipCaterNgOrderFetchAbnormalRequest.this.offset.toString());
        param.put("limit", CipCaterNgOrderFetchAbnormalRequest.this.limit.toString());
        return param;
    }

        public Integer getDeveloperId(){
        return developerId;
    }

    public void setDeveloperId(Integer developerId){
        this.developerId = developerId;
    }

    public Integer getVersion(){
        return version;
    }

    public void setVersion(Integer version){
        this.version = version;
    }

    public Integer getType(){
        return type;
    }

    public void setType(Integer type){
        this.type = type;
    }

    public Integer getStartTime(){
        return startTime;
    }

    public void setStartTime(Integer startTime){
        this.startTime = startTime;
    }

    public Integer getEndTime(){
        return endTime;
    }

    public void setEndTime(Integer endTime){
        this.endTime = endTime;
    }

    public Integer getOffset(){
        return offset;
    }

    public void setOffset(Integer offset){
        this.offset = offset;
    }

    public Integer getLimit(){
        return limit;
    }

    public void setLimit(Integer limit){
        this.limit = limit;
    }
}

