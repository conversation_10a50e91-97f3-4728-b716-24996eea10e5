package com.holder.saas.store.takeaway.producers.utils.sdk.jd.dto;

import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class QueryCategoriesReqDTO {

    /**
     * 定义需要返回的字段列表；所有字段列表：ID--商家店内分类编号,PID--父级店内分类编号
     * ,SHOP_CATEGORY_NAME--店内分类名称,SHOP_CATEGORY_LEVEL--店内分类级别,SORT--店内分类排序,
     * CATEGORY_TYPE--类型（普通分类or必选分类）
     */
    private List<String> fields;

    /**
     * 店内分类操作类型，0-商家店内分类；1-门店店内分类
     */
    private Integer optType;

    /**
     * 外部门店编号（optType传1时必传）
     */
    private String outStationNo;
}
