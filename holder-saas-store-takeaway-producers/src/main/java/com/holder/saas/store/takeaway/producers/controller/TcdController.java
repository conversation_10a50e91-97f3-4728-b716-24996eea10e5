package com.holder.saas.store.takeaway.producers.controller;

import com.holder.saas.store.takeaway.producers.service.TcdAuthService;
import com.holder.saas.store.takeaway.producers.service.TcdCallbackService;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.takeaway.TcdCallbackResponse;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutTCDConfirmTheMealDTO;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutTCDDiningOutDTO;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutTCDPickUpDTO;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutTcdOrderReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.StoreAuthDTO;
import com.holderzone.saas.store.dto.takeaway.response.TcdCommonRespDTO;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/tcd")
public class TcdController {

    private final TcdAuthService tcdAuthService;

    private final TcdCallbackService tcdCallbackService;

    @Autowired
    public TcdController(TcdAuthService tcdAuthService, TcdCallbackService tcdCallbackService) {
        this.tcdAuthService = tcdAuthService;
        this.tcdCallbackService = tcdCallbackService;
    }

    @PostMapping(value = "/callback/order")
    @ApiOperation(value = "赚餐外卖平台订单接收", notes = "赚餐平台订单接收")
    public TcdCallbackResponse orderCallback(@RequestBody TakeoutTcdOrderReqDTO takeoutTcdOrderReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("(赚餐外卖平台)订单接收：{}", JacksonUtils.writeValueAsString(takeoutTcdOrderReqDTO));
        }
        try {
            tcdCallbackService.orderCallback(takeoutTcdOrderReqDTO);
        } catch (Exception e) {
            if (log.isErrorEnabled()) {
                log.error("(赚餐外卖平台)订单接收异常：", e);
            }
            return new TcdCallbackResponse(299, e.getMessage());
        }
        return TcdCallbackResponse.SUCCESS;
    }

    @PostMapping("/get_takeout_auth")
    @ApiOperation(value = "根据erp门店id查询赚餐外卖授权表，是否已经授权")
    public StoreAuthDTO getTakeoutAuth(@RequestBody StoreAuthDTO storeAuthDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询赚餐外卖授权列表入参：{}", JacksonUtils.writeValueAsString(storeAuthDTO));
        }
        return tcdAuthService.getTakeoutAuth(storeAuthDTO);
    }

    @PostMapping("/update_delivery")
    @ApiOperation(value = "修改门店配送方式")
    public Boolean updateDelivery(@RequestBody StoreAuthDTO storeAuthDTO) {
        return tcdAuthService.updateDelivery(storeAuthDTO);
    }

    @PostMapping("/dining_out_tcd")
    @ApiOperation(value = "赚餐外卖出餐")
    public TcdCommonRespDTO diningOutTcd(@RequestBody TakeoutTCDDiningOutDTO takeoutTCDDiningOutDTO) {
        return tcdAuthService.diningOutTcd(takeoutTCDDiningOutDTO);
    }

    @PostMapping("/confirm_the_meal_tcd")
    @ApiOperation(value = "赚餐外卖确认取餐")
    public TcdCommonRespDTO confirmTheMealTcd(@RequestBody TakeoutTCDConfirmTheMealDTO takeoutTCDConfirmTheMealDTO) {
        return tcdAuthService.confirmTheMealTcd(takeoutTCDConfirmTheMealDTO);
    }

    @PostMapping("/pick_up_tcd")
    @ApiOperation(value = "赚餐外卖到店自提扫码")
    public TcdCommonRespDTO pickUpTcd(@RequestBody TakeoutTCDPickUpDTO takeoutTCDPickUpDTO) {
        return tcdAuthService.pickUpTcd(takeoutTCDPickUpDTO);
    }
}
