package com.holder.saas.store.takeaway.producers.service;

import com.holderzone.saas.store.dto.takeaway.MtQueryOrderDetail;
import com.holderzone.saas.store.dto.takeaway.MtCallbackDTO;
import com.holderzone.saas.store.dto.takeaway.UnOrder;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderRocketListener
 * @date 2018/11/15 12:00
 * @description 美团UnOrder解析器
 * @program holder-saas-store-takeaway
 */
public interface MtUnOrderParser {

    /**
     * 美团新订单 转换为 UnOrder
     *
     * @param mtQueryOrderDetail
     * @return
     */
    UnOrder fromMtQueryOrderDetail(MtQueryOrderDetail mtQueryOrderDetail);

    /**
     * 美团新订单 转换为 UnOrder
     *
     * @param mtCallbackDTO
     * @return
     */
    UnOrder fromMtCbOrderCreated(MtCallbackDTO mtCallbackDTO);

    /**
     * 美团取消订单 转换为 UnOrder
     *
     * @param mtCallbackDTO
     * @return
     */
    UnOrder fromMtCbOrderCanceled(MtCallbackDTO mtCallbackDTO);

    /**
     * 美团退单 转换为 UnOrder
     *
     * @param mtCallbackDTO
     * @return
     */
    UnOrder fromMtCbOrderRefund(MtCallbackDTO mtCallbackDTO);

    /**
     * 美团部分退款 转换为 UnOrder
     *
     * @param mtCallbackDTO
     * @return
     */
    UnOrder fromMtCbOrderPartRefund(MtCallbackDTO mtCallbackDTO);

    /**
     * 美团接单 转换为 UnOrder
     *
     * @param mtCallbackDTO
     * @return
     */
    UnOrder fromMtCbOrderConfirmed(MtCallbackDTO mtCallbackDTO);

    /**
     * 美团订单完成 转换为 UnOrder
     *
     * @param mtCallbackDTO
     * @return
     */
    UnOrder fromMtCbOrderFinished(MtCallbackDTO mtCallbackDTO);

    /**
     * 美团订单配送 转换为 UnOrder
     *
     * @param mtCallbackDTO
     * @return
     */
    UnOrder fromMtCbShippingStatus(MtCallbackDTO mtCallbackDTO);
}
