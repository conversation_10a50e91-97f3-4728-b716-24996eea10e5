package com.holder.saas.store.takeaway.producers.entity.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.holderzone.framework.util.DateTimeUtils;
import eleme.openapi.sdk.oauth.response.Token;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("hst_ele_auth")
public class EleAuthDO implements Serializable {

    private static final long serialVersionUID = 5466838236697202547L;

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 唯一标识
     */
    private String guid;

    /**
     * 商户GUID
     */
    private String enterpriseGuid;

    /**
     * 门店GUID，即ePoiId
     */
    private String storeGuid;

    /**
     * 饿了么商户ID
     */
    private Long userId;

    /**
     * 饿了么商户名称
     */
    private String userName;

    /**
     * 饿了么店铺ID
     */
    private Long shopId;

    /**
     * 饿了么店铺名称
     */
    private String shopName;

    /**
     * 饿了么配送方式
     */
    private Integer deliveryType;

    /**
     * 访问token
     */
    private String accessToken;

    /**
     * 刷新token
     */
    private String refreshToken;

    /**
     * token类型
     */
    private String tokenType;

    /**
     * token生效时间
     */
    private LocalDateTime activeTime;

    /**
     * token有效时间，单位秒
     */
    private Long expires;

    /**
     * token过期时间
     */
    private LocalDateTime expireTime;

    /**
     * token生效时间
     */
    private LocalDateTime refreshActiveTime;

    /**
     * token有效时间，单位秒
     */
    private Long refreshExpires;

    /**
     * token过期时间
     */
    private LocalDateTime refreshExpireTime;

    /**
     * 逻辑删除：0=未删除，1=已删除
     */
    @TableLogic
    @TableField("is_deleted")
    private Boolean deleted;

    /**
     * 记录创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 记录修改时间
     */
    private LocalDateTime gmtModified;

    public static Token toToken(EleAuthDO eleAuthDO) {
        Token token = new Token();
        token.setAccessToken(eleAuthDO.getAccessToken());
        token.setRefreshToken(eleAuthDO.getRefreshToken());
        token.setExpires(eleAuthDO.getExpires());
        token.setTokenType(eleAuthDO.getTokenType());
        return token;
    }

    public Token toToken() {
        Token token = new Token();
        token.setAccessToken(accessToken);
        token.setRefreshToken(refreshToken);
        token.setExpires(expires);
        token.setTokenType(tokenType);
        return token;
    }

    public static EleAuthDO ofToken(Token token, Integer refreshExpires) {
        EleAuthDO eleAuthDO = new EleAuthDO();
        eleAuthDO.setAccessToken(token.getAccessToken());
        eleAuthDO.setRefreshToken(token.getRefreshToken());
        eleAuthDO.setTokenType(token.getTokenType());
        eleAuthDO.setActiveTime(DateTimeUtils.now());
        eleAuthDO.setExpires(token.getExpires());
        eleAuthDO.setExpireTime(DateTimeUtils.now().plusSeconds(token.getExpires()));
        eleAuthDO.setRefreshActiveTime(DateTimeUtils.now());
        eleAuthDO.setRefreshExpires((long) (refreshExpires * 24 * 60 * 60));
        eleAuthDO.setRefreshExpireTime(DateTimeUtils.now().plusDays(refreshExpires));
        return eleAuthDO;
    }
}