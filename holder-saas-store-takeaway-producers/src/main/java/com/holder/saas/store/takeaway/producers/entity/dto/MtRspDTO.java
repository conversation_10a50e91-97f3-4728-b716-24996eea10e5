package com.holder.saas.store.takeaway.producers.entity.dto;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @create 2024-11-05
 * @description 美团公共返回
 */
@Data
public class MtRspDTO<T> {

    private String code;

    private String msg;

    private String traceId;

    private T data;

    public boolean success(){
        return  StringUtils.isNotEmpty(this.code) && "OP_SUCCESS".equals(this.code);
    }
}
