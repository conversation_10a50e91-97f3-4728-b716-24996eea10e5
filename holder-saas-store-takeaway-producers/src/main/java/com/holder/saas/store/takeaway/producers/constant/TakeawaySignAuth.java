package com.holder.saas.store.takeaway.producers.constant;

import com.alibaba.fastjson.JSONObject;
import com.holder.saas.store.takeaway.producers.entity.bo.SinModul;
import com.holderzone.framework.util.Bean2Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeSet;

/**
 * <AUTHOR>
 * @Description 美团外卖签名认证
 * @time 2017年7月14日 下午2:12:33
 */
public class TakeawaySignAuth {

    private static final Logger log = LoggerFactory.getLogger(TakeawaySignAuth.class);

    /**
     * 美团 SignKey
     */
    private static final String MT_SIGN_KEY = "9cs57yydie0wsfj2";

    /**
     * ERP厂商入驻新美大餐饮平台得到的唯一身份表示
     */
    private static final String MT_DEVELOPERID = "104664";

    /**
     * erp方门店id(测试)
     */
    private static final String MT_EPOILID = "kfpttest_zl8_5";

    /**
     * 美团数字签名认证(根据请求参数做一个sign)
     *
     * @param request
     * @param response
     * @return
     */
    public String MeiTuan(HttpServletRequest request, HttpServletResponse response) {
        String sign = "";
        // 获取参数
        String requestParam = request.getQueryString();
        String encryptStr = "{" + MT_SIGN_KEY + "}";
        Set<String> naturalSort = new TreeSet<String>();
        StringBuffer sb = new StringBuffer();

        try {
            if (!StringUtils.isEmpty(requestParam)) {
                String[] aa = requestParam.split("&");

                // 除去sign本身以及值为空的参数
                for (String singleParam : aa) {
                    String[] bb = singleParam.trim().split("=");
                    if (bb.length > 1 && !"sign".equals(bb[0])) {
                        naturalSort.add(bb[0]);
                    }
                }
                // 除去=号和&号，按参数自然排序
                for (String str : naturalSort) {
                    for (String resultStr : aa) {
                        String[] bb = resultStr.trim().split("=");
                        if (str.equals(bb[0])) {
                            sb.append(bb[0]).append(bb[1]);
                        }
                    }
                }
                encryptStr += sb;
                // 对加密字符串做 sha1 散列
                encryptStr = sha1(encryptStr);
                sign = encryptStr.toLowerCase();

            }
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        return sign;
    }

    /**
     * sha1 散列
     *
     * @param data
     * @return
     * @throws NoSuchAlgorithmException
     */
    private static String sha1(String data) throws NoSuchAlgorithmException {
        MessageDigest md = MessageDigest.getInstance("SHA1");
        md.update(data.getBytes());
        StringBuffer buf = new StringBuffer();
        byte[] bits = md.digest();
        for (int i = 0; i < bits.length; i++) {
            int a = bits[i];
            if (a < 0)
                a += 256;
            if (a < 16)
                buf.append("0");
            buf.append(Integer.toHexString(a));
        }
        return buf.toString();
    }


    public static Map<String, Object> getSihnModul(long time, int developerId, List<SinModul.Pos> list) {
        SinModul sinModul = new SinModul();
        sinModul.setDeveloperId(developerId);
        sinModul.setTime(time);
        sinModul.setList(list);
        Map<String, Object> stringObjectMap = Bean2Map.bean2map(sinModul);
        return stringObjectMap;
    }


//    public static String heartBeatBak(Map<String, Object> stringObjectMap) {
//        System.out.println(stringObjectMap.toString());
//        StringBuilder sb = new StringBuilder();
//        stringObjectMap.entrySet()
//                .stream()
//                .forEach(item -> {
//                    if (StringUtils.isEmpty(item.getValue())) {
//                        return;
//                    }
//                    if (item.getValue() instanceof List) {
//                        sb.append(",\"").append(item.getKey()).append("\"").append(":").append(JacksonUtils.writeValueAsString(item.getValue())).append(",");
//                    } else {
//                        sb.append("\"").append(item.getKey()).append("\"").append(":").append(item.getValue());
//                    }
//                });
//        String signKey = MT_SIGN_KEY;
//        String result = signKey +"data{"+sb.toString()+"}";
//        System.out.println("sha1加密前的字符串：" + result);
//        try {
//            return sha1(result);
//        } catch (NoSuchAlgorithmException e) {
//            e.printStackTrace();
//            log.error("sha1加密错误:{}",e.getMessage());
//            return null;
//        }
//    }

    public static String heartBeatBak(JSONObject jsonObject) {
        System.out.println(jsonObject.toJSONString());
        String signKey = MT_SIGN_KEY;
        String result = signKey +"data"+jsonObject.toString();
        System.out.println("sha1加密前的字符串：" + result);
        try {
            return sha1(result);
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
            log.error("sha1加密错误:{}",e.getMessage());
            return null;
        }
    }
}
