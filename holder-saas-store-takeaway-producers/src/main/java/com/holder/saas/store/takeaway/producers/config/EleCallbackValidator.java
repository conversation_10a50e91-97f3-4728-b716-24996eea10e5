package com.holder.saas.store.takeaway.producers.config;

import com.holderzone.framework.util.Bean2Map;
import com.holderzone.framework.util.StringUtils;
import eleme.openapi.sdk.api.entity.other.OMessage;
import eleme.openapi.sdk.utils.SignatureUtil;
import javafx.util.Pair;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.TreeMap;
import java.util.stream.Collectors;

@Component
public class EleCallbackValidator {

    private static final String KEY_SIGNATURE = "signature";

    @Value("${ele.SECRET}")
    private String secret;

    /**
     * 检查参数
     * @param oMessage
     * @return
     */
    public Pair<Boolean, String> checkParameter(OMessage oMessage) {
        if (!StringUtils.hasText(oMessage.getRequestId())) {
            return new Pair<>(false, "饿了么回调消息requestId为空");
        }
        if (!StringUtils.hasText(oMessage.getMessage())) {
            return new Pair<>(false, "饿了么回调消息message为空");
        }
        if (!StringUtils.hasText(oMessage.getSignature())) {
            return new Pair<>(false, "饿了么回调消息signature为空");
        }
        return new Pair<>(true, "饿了么回调消息参数正确");
    }

    /**
     * 检查签名
     * @param oMessage
     * @return
     */
    public boolean checkSignature(OMessage oMessage) {
        // map字典序升序
        Map<String, Object> filteredSortedMap = Bean2Map.bean2map(oMessage).entrySet().stream()
                .filter(stringObjectEntry -> validate(stringObjectEntry.getValue()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue,
                        (u, v) -> {
                            throw new IllegalStateException(String.format("Duplicate key %s", u));
                        }, TreeMap::new)
                );

        // 移除signature
        filteredSortedMap.remove(KEY_SIGNATURE);

        // 拼接
        String joinedParam = filteredSortedMap.entrySet().stream()
                .map(stringObjectEntry -> String.format("%s=%s", stringObjectEntry.getKey(), stringObjectEntry.getValue()))
                .collect(Collectors.joining(""));
        String joinedParamWithKey = String.format("%s%s", joinedParam, secret);

        return SignatureUtil.md5(joinedParamWithKey).equalsIgnoreCase(oMessage.getSignature());
    }

    /**
     * 校验可拼接的属性
     *
     * @param object 属性
     * @return 是否可用
     */
    private boolean validate(Object object) {
        return object != null && (!(object instanceof String) || StringUtils.hasText((String) object));
    }
}
