package com.holder.saas.store.takeaway.producers.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.holder.saas.store.takeaway.producers.constant.TakeawaySignAuth;
import com.holder.saas.store.takeaway.producers.entity.bo.SinModul;
import com.holder.saas.store.takeaway.producers.entity.domain.MtAuthDO;
import com.holder.saas.store.takeaway.producers.mapper.MtAuthMapper;
import com.holder.saas.store.takeaway.producers.service.MtHbRedisService;
import com.holder.saas.store.takeaway.producers.service.MtHeartbeatService;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.terminal.StoreDeviceDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceQueryDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.holder.saas.store.takeaway.producers.constant.TakeawaySignAuth.getSihnModul;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MtHeartbeatServiceImpl
 * @date 2018/09/14 15:49
 * @description
 * @program holder-saas-store-takeaway
 */
@Service
@Slf4j
public class MtHeartbeatServiceImpl implements MtHeartbeatService {

    @Resource
    private MtAuthMapper mtAuthMapper;

    @Autowired
    private MtHbRedisService mtHbRedisService;

    @Value("${mt.DEVELOPER_ID}")
    private String mtDeveloperId;

    @Value("${mt.SIGN_KEY}")
    private String mtSignKey;

    @Override
    public void heartbeatReport() {
        String str = mtHbRedisService.getHeartbeat();
        if ("".equals(str) || str == null) {
            int developerId = Integer.parseInt(mtDeveloperId);
            long time = System.currentTimeMillis();
            List<SinModul.Pos> list = new ArrayList<>();

//            UserInfoDTO userInfoDTO = new UserInfoDTO();
//            userInfoDTO.setEnterpriseGuid(enterpriseDTO.getEnterpriseGuid());
//            ThreadCacheUserInfo.put(JacksonUtils.writeValueAsString(userInfoDTO));
            List<MtAuthDO> mtAuthDOList = mtAuthMapper.selectList(null);
            if (null == mtAuthDOList || mtAuthDOList.size() == 0) {
                log.info("当前企业没有门店信息(开通外卖业务的门店)");
                return;
            }

            List<StoreDeviceQueryDTO> storeDeviceQueryDTOList = new ArrayList<>();
            for (MtAuthDO mtAuthDO : mtAuthDOList) {
                StoreDeviceQueryDTO storeDeviceQueryDTO = new StoreDeviceQueryDTO();
                storeDeviceQueryDTO.setDeviceType(3);       //一体机
                storeDeviceQueryDTO.setStoreGuid(mtAuthDO.getEPoiId());
                storeDeviceQueryDTOList.add(storeDeviceQueryDTO);
            }

            //查询所有的（绑定过美团外卖）门店终端（一体机）信息
            List<StoreDeviceDTO> storeDeviceDTOList = Collections.emptyList();

            if (null == storeDeviceDTOList || storeDeviceDTOList.size() == 0) {
                // fixme 这儿到底在干嘛???
                List<String> list1 = mtAuthDOList.stream().map(MtAuthDO::getEPoiId).collect(Collectors.toList());
                return;
//                    throw new BusinessException("心跳上报，根据门店List：{" + list1 +"}，查询出一体机的信息为空");
            }


            storeDeviceDTOList.forEach(item -> {
                SinModul.Pos pos = new SinModul.Pos();
                pos.setEPoiId(item.getStoreGuid());
                pos.setPosId(item.getDeviceGuid());
                list.add(pos);
            });

            Map<String, Object> sihnModul = getSihnModul(time, developerId, list);

            JSONObject jsonObject = new JSONObject(true);
            jsonObject.put("developerId", developerId);
            jsonObject.put("list", list);
            jsonObject.put("time", time);

            String sign = TakeawaySignAuth.heartBeatBak(jsonObject);

            String data = JacksonUtils.writeValueAsString(sihnModul);
            data = "data=" + data + "&sign=" + sign;

            String result1 = detectionHeartbeat("http://heartbeat.meituan.com/pos/heartbeat", data);

            JSONObject jsonObject1 = JSONObject.parseObject(result1);
            String data1 = jsonObject1.getString("data");
            JSONObject jsonObject2 = JSONObject.parseObject(data1);
            if (jsonObject2 != null) {
                String message = jsonObject2.getString("message");
                if ("成功".equals(message)) {
                    mtHbRedisService.saveHeartbeat(data);
                } else {
                    String error = jsonObject1.getString("error");
//                        throw new BusinessException("心跳上报错误:"+error);
                    return;
                }
            } else {
                String error = jsonObject1.getString("error");
//                    throw new BusinessException("心跳上报错误:"+error);
                return;
            }

        } else {
            String result1 = detectionHeartbeat("http://heartbeat.meituan.com/pos/heartbeat", str);

            JSONObject jsonObject1 = JSONObject.parseObject(result1);
            String data1 = jsonObject1.getString("data");
            JSONObject jsonObject2 = JSONObject.parseObject(data1);
//            String message = jsonObject2.getString("message");

            if (jsonObject2 == null) {
                String error = jsonObject1.getString("error");
//                    throw new BusinessException("心跳上报错误:"+error);
                return;
            }

        }
    }

    public String detectionHeartbeat(String url, String params) {
        OutputStreamWriter out = null;
        BufferedReader in = null;
        StringBuilder result = new StringBuilder();
        try {
            URL realUrl = new URL(url);
            HttpURLConnection conn = (HttpURLConnection) realUrl.openConnection();
            // 发送POST请求必须设置如下两行
            conn.setDoOutput(true);
            conn.setDoInput(true);
            // POST方法
            conn.setRequestMethod("POST");
            // 设置通用的请求属性
            conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
            conn.connect();
            // 获取URLConnection对象对应的输出流
            out = new OutputStreamWriter(conn.getOutputStream(), "UTF-8");
            out.write(params);
            // flush输出流的缓冲
            out.flush();
            // 定义BufferedReader输入流来读取URL的响应
            in = new BufferedReader(
                    new InputStreamReader(conn.getInputStream(), "UTF-8"));
            String line;
            while ((line = in.readLine()) != null) {
                result.append(line);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        //使用finally块来关闭输出流、输入流
        finally {
            try {
                if (in != null) {
                    in.close();
                }
                if (out != null) {
                    out.close();
                }
            } catch (IOException ex) {
                ex.printStackTrace();
            }
        }
        return result.toString();
    }


}
