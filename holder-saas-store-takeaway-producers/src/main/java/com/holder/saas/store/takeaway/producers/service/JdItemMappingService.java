package com.holder.saas.store.takeaway.producers.service;

import com.holder.saas.store.takeaway.producers.entity.domain.JdItemMappingDO;
import com.holderzone.saas.store.dto.takeaway.UnMappedItem;

import java.util.List;

public interface JdItemMappingService {

   List<UnMappedItem> getItem(String storeGuid, String brandGuid);

   List<JdItemMappingDO> listItemMappingByStore(String storeGuid, List<String> skuIdList);
}
