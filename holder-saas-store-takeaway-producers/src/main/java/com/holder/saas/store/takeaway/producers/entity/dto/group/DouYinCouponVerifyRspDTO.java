package com.holder.saas.store.takeaway.producers.entity.dto.group;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.annotation.JSONField;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.exception.GroupBuyException;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-06-19
 * @description
 */
@Data
public class DouYinCouponVerifyRspDTO extends DouYinCommonDTO{

    /**
     * 验券结果
     */
    @JSONField(name = "verify_results")
    private List<DouYinVerifyResult> verifyResults;

    public static List<DouYinVerifyResult> parseJsonAndCheck(String rsp) {
        DouYinRspDTO<DouYinCouponVerifyRspDTO> couponVerifyRsp = JSONObject.parseObject(rsp,new TypeReference<DouYinRspDTO<DouYinCouponVerifyRspDTO>>(){}.getType());
        if (couponVerifyRsp == null || couponVerifyRsp.getData() == null){
            throw new BusinessException("抖音验券返回为空");
        }
        if(couponVerifyRsp.getData().getErrorCode() != 0){
            throw new BusinessException(couponVerifyRsp.getData().getDescription());
        }
        List<DouYinVerifyResult> verifyResultsRsp = couponVerifyRsp.getData().getVerifyResults();
        if(CollectionUtil.isEmpty(verifyResultsRsp)){
            throw new BusinessException("抖音验券返回为空");
        }
        //移除失败得券码
        verifyResultsRsp.removeIf(v -> v.getResult() != 0);
        if (CollectionUtil.isEmpty(verifyResultsRsp)){
            throw new BusinessException("抖音验券失败");
        }
        return verifyResultsRsp;
    }
}
