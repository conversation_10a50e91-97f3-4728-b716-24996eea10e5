package com.holder.saas.store.takeaway.producers.service.impl;

import com.holder.saas.store.takeaway.producers.service.MtAuthService;
import com.holder.saas.store.takeaway.producers.service.MtCouponService;
import com.holder.saas.store.takeaway.producers.utils.MtCouponUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.takeaway.MtResponseDTO;
import com.holderzone.saas.store.dto.takeaway.request.CouponDelReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.MtCouponReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 优惠券
 */
@Slf4j
@Service
@Qualifier("mtDevCouponServiceImpl")
public class MtDevCouponServiceImpl implements MtCouponService {


    private final MtAuthService authService;

    @Value("${mt.SIGN_KEY}")
    private String mtSignKey;


    public MtDevCouponServiceImpl(MtAuthService authService) {
        log.info("注入线上环境MtProdCouponServiceImpl");
        this.authService = authService;
    }


    @Override
    public MtCouponDetailRespDTO queryById(MtCouponReqDTO mtCouponReqDTO) {
        return null;
    }

    @Override
    public MtCouponDoCheckRespDTO checkTicket(MtCouponReqDTO mtCouponReqDTO) {
        return MtCouponUtils.checkTicket(mtCouponReqDTO,authService,mtSignKey,null);
    }

    @Override
    public MtCouponDoCheckRespDTO doCheck(MtCouponReqDTO mtCouponReqDTO) {
        MtCouponDoCheckRespDTO mtCouponDoCheckRespDTO = new MtCouponDoCheckRespDTO();
        List<String> couList = new ArrayList<>();
        couList.add("080576250516");
        couList.add("181268150407");
        couList.add("222539050279");
        couList.add("232496250741");
        mtCouponDoCheckRespDTO.setCouponCodes(couList);
        mtCouponDoCheckRespDTO.setResult(0);
        mtCouponDoCheckRespDTO.setDealId(41777850);
        mtCouponDoCheckRespDTO.setDealValue(0.11);
        mtCouponDoCheckRespDTO.setDealTitle("kfpttest_zl5_02人餐");
        mtCouponDoCheckRespDTO.setPoiid("159869278");
        mtCouponDoCheckRespDTO.setMessage("");
        return mtCouponDoCheckRespDTO;
    }

    @Override
    public MtCouponPreRespDTO  preCheck(MtCouponReqDTO mtCouponReqDTO) {
        //String success1="{\"data\":{\"dealBeginTime\":\"2018-03-18\",\"dealId\":41777850,\"dealValue\":0.11,\"dealTitle\":\"kfpttest_zl5_02人餐\",\"count\":1,\"dealPrice\":0.1,\"dealMenu\":[[{\"type\":\"0\",\"content\":\"开放平台测试小吃\",\"notDishes\":\"false\"},{\"total\":\"0.1\",\"images\":\"[]\",\"price\":\"0.1\",\"specification\":\"1 份\",\"type\":\"128\",\"content\":\"小吃1\",\"desc\":\"\"},{\"total\":\"0.01\",\"images\":\"[]\",\"price\":\"0.01\",\"specification\":\"1 份\",\"type\":\"128\",\"content\":\"小吃2\",\"desc\":\"\"}]],\"message\":\"\",\"minConsume\":1,\"couponEndTime\":\"2019-05-19\",\"result\":0,\"couponBuyPrice\":0.1,\"couponCode\":\"013793750568\",\"isVoucher\":false}}";
        String success2="{\"data\":{\"dealBeginTime\":\"2018-03-18\",\"dealId\":41777850,\"dealValue\":0.11,\"dealTitle\":\"kfpttest_zl5_02人餐\",\"count\":4,\"dealPrice\":0.1,\"dealMenu\":[[{\"type\":\"0\",\"content\":\"开放平台测试小吃\",\"notDishes\":\"false\"},{\"total\":\"0.1\",\"images\":\"[]\",\"price\":\"0.1\",\"specification\":\"1 份\",\"type\":\"128\",\"content\":\"小吃1\",\"desc\":\"\"},{\"total\":\"0.01\",\"images\":\"[]\",\"price\":\"0.01\",\"specification\":\"1 份\",\"type\":\"128\",\"content\":\"小吃2\",\"desc\":\"\"}]],\"message\":\"\",\"minConsume\":1,\"couponEndTime\":\"2019-05-19\",\"result\":0,\"couponBuyPrice\":0.1,\"couponCode\":\"308156150633\",\"isVoucher\":false}}";
        //String failure1 = "{\"error\":{\"code\":4,\"error_type\":\"authority_error\",\"message\":\"券码错误\"}}";
        //String failure2="{\"error\":{\"code\":2,\"error_type\":\"param_error\",\"message\":\"参数校验失败,couponCode[013793750568,181268150407,080576250516,308156150633,232496250741]格式错误\"}}";

        MtResponseDTO.ErrorDetail error;
        MtPreCheckRespDTO mtPreCheckRespDTO = JacksonUtils.toObject(MtPreCheckRespDTO.class, success2);
        MtCouponPreRespDTO MtCouponPreRespDTO=new MtCouponPreRespDTO();
        if(mtPreCheckRespDTO.getError() != null){
            error=mtPreCheckRespDTO.getError();
            log.error("预验券失败，错误代码：{}，错误信息：{}，错误类型：{}",
                    error.getCode(),
                    error.getMessage(),
                    error.getError_type());
            throw new BusinessException("预验券失败:"+error.getMessage());

        }else{
            MtCouponPreRespDTO=mtPreCheckRespDTO.getData();
        }
        System.out.println("MtCouponPreRespDTO="+MtCouponPreRespDTO);

        List<MtDealRespMenu> menu = new ArrayList<>();
        List<List<MtDealRespMenu>> menuList = new ArrayList<>();

        MtCouponPreRespDTO mtCouponPreRespDTO = new MtCouponPreRespDTO();
        mtCouponPreRespDTO.setCount(4);
        mtCouponPreRespDTO.setCouponBuyPrice(0.1);
        mtCouponPreRespDTO.setCouponCode("131489950071");
        mtCouponPreRespDTO.setIsVoucher(true);
        mtCouponPreRespDTO.setCouponEndTime("2019-12-31");
        mtCouponPreRespDTO.setDealBeginTime("2019-03-18");
        mtCouponPreRespDTO.setDealId(41777850);

        MtDealRespMenu menu1 = new MtDealRespMenu();
        menu1.setType("0");
        menu1.setContent("开放平台测试小吃");
        menu1.setNotDishes("false");

        MtDealRespMenu menu2 = new MtDealRespMenu();
        menu2.setTotal("0.1");
        menu2.setImages("[]");
        menu2.setPrice("0.1");
        menu2.setSpecification("1 份");
        menu2.setType("128");
        menu2.setContent("小吃1");
        menu2.setDesc("");

        MtDealRespMenu menu3 = new MtDealRespMenu();
        menu3.setTotal("0.01");
        menu3.setImages("[]");
        menu3.setPrice("0.01");
        menu3.setSpecification("1 份");
        menu3.setType("128");
        menu3.setContent("小吃2");
        menu3.setDesc("");

        menu.add(menu1);
        menu.add(menu2);
        menu.add(menu3);
        menuList.add(menu);

        mtCouponPreRespDTO.setDealPrice(0.1);
        mtCouponPreRespDTO.setDealTitle("kfpttest_zl5_02人餐");
        mtCouponPreRespDTO.setDealValue(0.11);
        mtCouponPreRespDTO.setMessage("");
        mtCouponPreRespDTO.setMinConsume(1);
        mtCouponPreRespDTO.setResult(0);
        return mtCouponPreRespDTO;
    }

    @Override
    public MtDelCouponRespDTO cancelTicket(CouponDelReqDTO couponDelReqDTO) {
        String success1="{\"result\":0,\"message\":\"成功\"}";
        //String failure1="{\"data\":{\"result\":2003,\"message\":\"券码错误\"}}";
        //String failure2="{\"data\":{\"result\":2003,\"message\":\"此券状态已改变，不能接待。请重试\"}}";

        MtDoDelRespDTO mtDoDelRespDTO= JacksonUtils.toObject(MtDoDelRespDTO.class, success1);
        if(mtDoDelRespDTO.getData()!=null){
            System.out.println(mtDoDelRespDTO.getData().getResult()+":"+mtDoDelRespDTO.getData().getMessage());
        }else{
            MtDelCouponRespDTO mtDelCouponRespDTO= JacksonUtils.toObject(MtDelCouponRespDTO.class, success1);
            System.out.println(mtDelCouponRespDTO.getResult()+":"+mtDelCouponRespDTO.getMessage());
        }



        MtDelCouponRespDTO mtDelCouponRespDTO = new MtDelCouponRespDTO();
        mtDelCouponRespDTO.setResult(0);
        mtDelCouponRespDTO.setMessage("成功");
        return mtDelCouponRespDTO;
    }

    @Override
    public MtCouponTradeDetailRespDTO queryGroupTradeDetail(MtCouponReqDTO mtCouponReqDTO) {
        return null;
    }
}
