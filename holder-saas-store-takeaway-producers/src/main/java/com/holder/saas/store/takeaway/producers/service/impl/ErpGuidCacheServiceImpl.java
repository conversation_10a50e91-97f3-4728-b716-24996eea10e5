package com.holder.saas.store.takeaway.producers.service.impl;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.holder.saas.store.takeaway.producers.service.ErpGuidCacheService;
import com.holder.saas.store.takeaway.producers.service.rpc.ErpFeignService;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.framework.util.ThrowableUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @className cacheServiceImpl
 * @date 2018/10/11 17:15
 * @description
 * @program holder-saas-store-takeaway
 */
@Slf4j
@Service
public class ErpGuidCacheServiceImpl implements ErpGuidCacheService {

    private final ErpFeignService erpFeignService;

    private LoadingCache<String, String> cache = CacheBuilder.newBuilder()
            .maximumSize(20)
            .expireAfterWrite(1, TimeUnit.HOURS)
            .build(new CacheLoader<String, String>() {
                @Override
                public String load(String storeGuid) {
                    String enterpriseGuid = erpFeignService.getEnterpriseGuidByStoreGuid(storeGuid);
                    if (StringUtils.isEmpty(enterpriseGuid)) {
                        throw new BusinessException("门店storeGuid[" + storeGuid + "]未找到enterpriseGuid");
                    } else if (log.isInfoEnabled()) {
                        log.info("根据门店storeGuid[{}]找到enterpriseGuid[{}]", storeGuid, enterpriseGuid);
                    }
                    return enterpriseGuid;
                }
            });

    @Autowired
    public ErpGuidCacheServiceImpl(ErpFeignService erpFeignService) {
        this.erpFeignService = erpFeignService;
    }

    @Override
    public String getEnterpriseGuid(String storeGuid) {
        try {
            return cache.get(storeGuid);
        } catch (ExecutionException e) {
            throw new BusinessException("根据门店storeGuid[" + storeGuid + "]查找enterpriseGuid发生异常：" + ThrowableUtils.asString(e));
        }
    }
}
