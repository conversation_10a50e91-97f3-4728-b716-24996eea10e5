package com.holder.saas.store.takeaway.producers.entity.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("hst_mt_auth")
public class MtAuthDO {

    @TableId
    private Integer id;

    /**
     * 唯一标识
     */
    private String guid;

    /**
     * erp门店ID，即storeGuid
     */
    private String ePoiId;

    /**
     * Erp商户GUID
     */
    private String enterpriseGuid;

    /**
     * 美团门店GUID
     */
    private String mtStoreGuid;

    /**
     * 美团门店名字
     */
    private String mtStoreName;

    /***
     * 美团配送方式
     */
    private Integer deliveryType;

    /**
     * 访问token
     */
    private String accessToken;

    /**
     * erp门店开通的业务类型
     */
    private Byte businessId;

    /**
     * token生效时间
     */
    private LocalDateTime activeTime;

    /**
     * token失效时间
     */
    private LocalDateTime expireTime;

    /**
     * 逻辑删除：0=未删除，1=已删除
     */
    @TableLogic
    @TableField("is_deleted")
    private Boolean deleted;

    /**
     * 记录创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 记录修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 更新令牌
     */
    private String refreshToken;

    /**
     * 更新令牌失效时间
     */
    private LocalDateTime refreshTokenExpire;
}