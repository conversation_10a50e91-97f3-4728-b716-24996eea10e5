package com.holder.saas.store.takeaway.producers.utils.sdk.jd.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class OrderQueryReqDTO {

    /**
     * 当前页数
     */
    private Long pageNo;

    /**
     * 每页条数
     */
    private Integer pageSize;

    /**
     * 订单号（如果传了订单号，其他条件不生效）
     */
    private Long orderId;

    /**
     * 订单状态（20010:锁定，20020:订单取消，20030:订单取消申请，20040:超时未支付系统取消，31000:等待付款，31020:已付款，41000:待处理，32000:等待出库，33040:配送中，33060:已妥投，90000:订单完成）
     */
    private Integer orderStatus;

    /**
     * 业务类型（1:京东到家商超,2:京东到家美食,3:京东到家精品有约,4:京东到家开放仓,5:哥伦布店内订单,6:货柜项目订单,7:智能货柜项目订单,
     * 8:轻松购订单,9:自助收银订单,10:超级会员码，15：券码核销订单），当多个业务类型时，是以逗号分隔的数值串。
     */
    @JSONField(name = "businessType_list")
    private Integer[] businessTypeList;
}
