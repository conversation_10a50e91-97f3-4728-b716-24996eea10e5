package com.holder.saas.store.takeaway.producers.interceptor;

import com.holder.saas.store.takeaway.producers.utils.ThreadCacheUserInfo;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLDecoder;

import static com.holderzone.saas.store.dto.common.CommonConstant.USER_INFO;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WebInterceptor
 * @date 2018/09/13 16:16
 * @description
 * @program holder-saas-store--store-info
 */
@Configuration
@Deprecated
public class WebInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (StringUtils.hasText(request.getHeader(USER_INFO))) {
            String userInfo = URLDecoder.decode(request.getHeader(USER_INFO), "utf-8");
            ThreadCacheUserInfo.put(userInfo);
        }
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {

    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        ThreadCacheUserInfo.remove();
    }
}

