package com.holder.saas.store.takeaway.producers.mapstruct;

import com.holder.saas.store.takeaway.producers.entity.domain.MtAuthDO;
import com.holder.saas.store.takeaway.producers.entity.dto.MtAuthDTO;
import org.mapstruct.Mapper;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 */
@Component
@Mapper(componentModel = "spring")
public interface MtAuthMapstruct {

    MtAuthDO fromMtAuth(MtAuthDTO mtAuthDTO);

}
