package com.holder.saas.store.takeaway.producers.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holder.saas.store.takeaway.producers.entity.domain.OrderCallDO;
import com.holder.saas.store.takeaway.producers.mapper.OrderCallMapper;
import com.holder.saas.store.takeaway.producers.service.DianXinService;
import com.holder.saas.store.takeaway.producers.service.rpc.ConsumersFeignService;
import com.holder.saas.store.takeaway.producers.utils.HttpsClientUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.takeaway.OrderCallEnum;
import com.holderzone.saas.store.dto.takeaway.SendOrderCallReq;
import com.holderzone.saas.store.dto.takeaway.request.OrderCallDTO;
import com.holderzone.saas.store.dto.takeaway.response.SendCallRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.SendOutboundRespDTO;
import com.holderzone.saas.store.enums.common.BooleanEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class DianXinServiceImpl implements DianXinService {

    private static final ScheduledExecutorService ORDER_CALL_EXECUTOR_SERVICE = Executors.newScheduledThreadPool(20);

    @Value("${dx.DIAN_XIN_TOKEN_URL}")
    private String DIAN_XIN_TOKEN_URL;

    @Value("${dx.DIAN_XIN_CALL_URL}")
    private String DIAN_XIN_CALL_URL;

    @Value("${dx.APPID}")
    private String APPID;

    @Value("${dx.SECRET}")
    private String SECRET;

    @Value("${dx.TASK_ID}")
    private String TASK_ID;

    @Value("${dx.PROJECT_ID}")
    private String PROJECT_ID;

    private final ConsumersFeignService consumersFeignService;

    public DianXinServiceImpl(ConsumersFeignService consumersFeignService) {
        this.consumersFeignService = consumersFeignService;
    }

    @Override
    public void outbound(SendOrderCallReq sendOrderCallReq) {
        log.info("模板TASK_ID：{}", TASK_ID);
        log.info("DIAN_XIN_TOKEN_URL：{}", DIAN_XIN_TOKEN_URL);
        if (Objects.isNull(TASK_ID) || StringUtils.isEmpty(TASK_ID)) {
            log.info("未配置模板ID");
            return;
        }
        LocalDateTime now = LocalDateTime.now();
        long date = Duration.between(LocalDateTime.now(), sendOrderCallReq.getLongTime()).getSeconds();
        log.info("当前时间，{}", JacksonUtils.writeValueAsString(now));
        log.info("订单延迟时间，{}秒", JacksonUtils.writeValueAsString(date));
        sendCall(sendOrderCallReq, date);
    }

    private void sendCall(SendOrderCallReq sendOrderCallReq, long date) {
        ORDER_CALL_EXECUTOR_SERVICE.schedule(() -> {
            try {
                OrderCallDTO dto = new OrderCallDTO();
                BeanUtils.copyProperties(sendOrderCallReq, dto);
                //电信推送
                log.info("开始执行当前订单号外呼{}：" + sendOrderCallReq.getOrderGuid());
                log.info("开始执行电信呼叫，当前时间：{}" + JacksonUtils.writeValueAsString(LocalDateTime.now()));
                String url = DIAN_XIN_TOKEN_URL + "appid=" + APPID +
                        "secret=" + SECRET;
                log.info("获取密钥URL{}：" + url);
                String result = HttpsClientUtils.HttpGet(url);
                SendCallRespDTO sendCallRespDTO = JSONObject.parseObject(result, SendCallRespDTO.class);
                log.info("电信获取token返参，sendCallRespDTO:{}" + JacksonUtils.writeValueAsString(sendCallRespDTO));
                if (Objects.nonNull(sendCallRespDTO) && sendCallRespDTO.getStatusCode() == 200) {
                    String token = sendCallRespDTO.getData().getAccess_token();
                    Map<String, String> params = new HashMap<>();
                    params.put("task_id", TASK_ID);
                    params.put("project_id", PROJECT_ID);
                    params.put("token", token);
                    params.put("phone_number", sendOrderCallReq.getPhone());
                    log.info("电信呼叫入参，params:{}" + JacksonUtils.writeValueAsString(params));
                    String resultPost = HttpsClientUtils.doPost(DIAN_XIN_CALL_URL, params);
                    dto.setCallStatus(OrderCallEnum.ALREADY_CALL.getType());
                    SendOutboundRespDTO sendOutboundRespDTO = JSONObject.parseObject(resultPost, SendOutboundRespDTO.class);
                    log.info("电信呼叫返参，sendOutboundRespDTO:{}" + JacksonUtils.writeValueAsString(sendOutboundRespDTO));
                    if (Objects.nonNull(sendOutboundRespDTO) && sendOutboundRespDTO.getStatusCode() == 200
                            && sendOutboundRespDTO.getMessage().getInfo().equals("Success")) {
                        dto.setCallStatus(OrderCallEnum.ALREADY_CONNECT.getType());
                        dto.setMessage("Success");
                    } else {
                        log.info("电信呼叫失败");
                        dto.setCallStatus(OrderCallEnum.NOT_CONNECT.getType());
                        dto.setMessage("电信呼叫失败");
                    }
                } else {
                    log.info("获取电信token失败，url:{}" + url);
                    dto.setCallStatus(OrderCallEnum.NOT_CONNECT.getType());
                    dto.setMessage("电信获取token失败");
                }
                //状态更新
                log.info("电信呼叫成功开始更新状态，批次号：" + sendOrderCallReq.getBatch());
                dto.setEnterpriseGuid(sendOrderCallReq.getEnterpriseGuid());
                consumersFeignService.updateCallOrder(dto);
            } catch (Exception e) {
                log.error("推送电信失败", e);
            }
        }, date, TimeUnit.SECONDS);

    }
}
