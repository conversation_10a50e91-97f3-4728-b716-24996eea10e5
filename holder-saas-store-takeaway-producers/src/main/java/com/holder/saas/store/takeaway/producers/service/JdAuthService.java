package com.holder.saas.store.takeaway.producers.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.holder.saas.store.takeaway.producers.entity.domain.JdAuthDO;
import com.holderzone.saas.store.dto.takeaway.jd.CallBackDTO;
import com.holderzone.saas.store.dto.takeaway.request.StoreAuthByStoreReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutShopBindReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.StoreAuthDTO;
import com.holderzone.saas.store.dto.takeaway.response.TakeoutShopBindRespDTO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 京东授权服务
 */
public interface JdAuthService extends IService<JdAuthDO> {

    /**
     * token设置
     * @param callBackDTO 回调参数
     */
    void tokenSetting(CallBackDTO callBackDTO);

    /**
     * 门店绑定
     * @param storeAuthByStoreReqDTO 请求绑定参数
     */
    void doBindStore(StoreAuthByStoreReqDTO storeAuthByStoreReqDTO);

    /**
     * 门店解绑
     * @param storeAuthByStoreReqDTO 请求解绑参数
     */
    void doUnbindStore(StoreAuthByStoreReqDTO storeAuthByStoreReqDTO);

    /**
     * 门店授权信息
     * @param storeAuthDTO 授权请求参数
     */
    StoreAuthDTO getTakeoutAuth(StoreAuthDTO storeAuthDTO);


    /**
     * 获取京东门店绑定授权链接
     * @param takeoutShopBindReqDTO 请求参数
     * @return 返回链接
     */
    TakeoutShopBindRespDTO takeOutBindingUrl(TakeoutShopBindReqDTO takeoutShopBindReqDTO);

    /**
     * 根据token查询授权信息
     * @param token token
     * @return 授权信息
     */
    JdAuthDO getJdAuthByToken(String token);

    /**
     * 根据brandGuid查询授权信息
     * @param brandGuid brandGuid
     * @return 授权信息
     */
    JdAuthDO getJdAuthByBrand(String brandGuid);

    /**
     * 根据品牌列表查询是否授权京东店铺
     * @param brandGuidList 品牌guid列表
     * @return 授权列表
     */
    List<JdAuthDO> listJdAuthByBrand(List<String> brandGuidList);

    /**
     * 查询品牌是否授权
     * @param brandGuidList 品牌列表
     * @return 授权的品牌guid
     */
    List<String> authBrand(List<String> brandGuidList);
}
