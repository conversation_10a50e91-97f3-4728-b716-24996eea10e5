package com.holder.saas.store.takeaway.producers.utils.sdk.jd.dto;

import lombok.Data;

import java.util.List;

@Data
public class CategoriesQueryResponse {

    /**
     * 状态码，0：获取商家类目结果成功! -1：获取商家类目结果失败，接口异常! 20000：入参为空
     */
    private String code;

    /**
     * 返回描述信息
     */
    private String msg;

    /**
     * 返回数据
     */
    private List<ShopCategory> result;

    public boolean isSuccess() {
        return "0".equals(this.code);
    }
}
