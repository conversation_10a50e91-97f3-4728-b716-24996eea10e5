package com.holder.saas.store.takeaway.producers.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holder.saas.store.takeaway.producers.config.GroupBuyDouYinConfig;
import com.holder.saas.store.takeaway.producers.entity.domain.GroupStoreBindDO;
import com.holder.saas.store.takeaway.producers.entity.dto.group.*;
import com.holder.saas.store.takeaway.producers.service.GroupBuyService;
import com.holder.saas.store.takeaway.producers.service.GroupStoreBindService;
import com.holder.saas.store.takeaway.producers.service.converter.GroupBuyConverter;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.constant.Constant;
import com.holderzone.saas.store.dto.business.group.StoreBindDTO;
import com.holderzone.saas.store.dto.exception.GroupBuyException;
import com.holderzone.saas.store.dto.order.response.groupon.GroupVerifyDTO;
import com.holderzone.saas.store.dto.takeaway.request.CouPonPreReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.CouPonReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtCouponPreRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtDelCouponRespDTO;
import com.holderzone.saas.store.enums.GroupBuyTypeEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;

import java.net.URI;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @create 2023-06-19
 * @description 抖音团购服务类
 * 商户账号：6900751593394800648
 * 沙湾店ID：6601165426317592590
 */
@Service("douYinGroupBuyServiceImpl")
@Slf4j
@AllArgsConstructor
public class DouYinGroupBuyServiceImpl implements GroupBuyService {

    private final GroupBuyDouYinConfig groupBuyDouYinConfig;

    private final StringRedisTemplate redisTemplate;

    private final RestTemplate groupBuyRestTemplate;

    private final GroupStoreBindService groupStoreBindService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void bindStore(StoreBindDTO storeBind) {
        storeBind.verify();
        //查询门店是否绑定
        int count = groupStoreBindService.count(new LambdaQueryWrapper<GroupStoreBindDO>().eq(GroupStoreBindDO::getStoreGuid, storeBind.getStoreGuid())
                .eq(GroupStoreBindDO::getType, storeBind.getGroupBuyType()));
        if(count > 0){
            throw new GroupBuyException("此门店已绑定抖音门店");
        }
        //清除绑定此抖音门店的信息
        groupStoreBindService.remove(new LambdaQueryWrapper<GroupStoreBindDO>().eq(GroupStoreBindDO::getPoiId, storeBind.getPoiId())
                .eq(GroupStoreBindDO::getType, storeBind.getGroupBuyType()));
        //远程请求绑定门店
        DouYinStoreBindRspDTO poiRsp = remoteBindStore(storeBind);
        //保存绑定关系
        groupStoreBindService.save(GroupBuyConverter.fromNameAndStoreBind(poiRsp,storeBind,GroupBuyTypeEnum.DOU_YIN.getCode()));
    }

    @Override
    public List<MtCouponPreRespDTO> couponPrepare(CouPonPreReqDTO couPonPreReqDTO) {
        //抖音验券准备需要encrypted_data/code必须二选一
        if(StringUtils.isEmpty(couPonPreReqDTO.getCouponCode())){
           throw new BusinessException("验券入参为空");
        }
        if(couPonPreReqDTO.getCouponCode().contains("https://")){
            couPonPreReqDTO.setEncryptedData(resolveUrl(couPonPreReqDTO.getCouponCode()));
            couPonPreReqDTO.setCouponCode(null);
        }

        //查询门店是否绑定
        int count = groupStoreBindService.count(new LambdaQueryWrapper<GroupStoreBindDO>().eq(GroupStoreBindDO::getStoreGuid, couPonPreReqDTO.getStoreGuid())
                .eq(GroupStoreBindDO::getType, couPonPreReqDTO.getGroupBuyType()));
        if(count == 0){
            throw new BusinessException(Constant.COUPON_UN_BIND_STORE);
        }
        String urlParams;
        if(StringUtils.isNotEmpty(couPonPreReqDTO.getCouponCode())){
            urlParams = "?code=" + couPonPreReqDTO.getCouponCode();
        }else {
            urlParams = "?encrypted_data=" + couPonPreReqDTO.getEncryptedData();
        }
        String rsp = getRequest(urlParams,getToken(),groupBuyDouYinConfig.host + groupBuyDouYinConfig.certificatePrepare);
        List<DouYinCertificate> certificateList = DouYinCouPonPreRspDTO.parseJsonAndCheck(rsp);
        if(CollectionUtil.isEmpty(certificateList)){
            return Collections.emptyList();
        }
        //转化抖音优惠券
        return GroupBuyConverter.toDouYinCouponPreRespList(certificateList);
    }
    private static final String PREPARE_QR_ERROR = "抖音团购券码有误";

    private static final String REMOTE_ERROR = "请求抖音服务异常";

    private String resolveUrl(String encryptedData) {
        HttpHeaders httpHeaders = groupBuyRestTemplate.headForHeaders(encryptedData);
        URI location = httpHeaders.getLocation();
        if(location == null){
            throw new BusinessException(PREPARE_QR_ERROR);
        }
        String query = location.getQuery();
        if(StringUtils.isEmpty(query)){
            throw new BusinessException(PREPARE_QR_ERROR);
        }
        String[] split = query.split("&");
        for(String s : split){
            if(s.contains("object_id")){
                return s.replace("object_id=","");
            }
        }
        throw new BusinessException(PREPARE_QR_ERROR);
    }

    @Override
    public List<GroupVerifyDTO> verifyCoupon(CouPonReqDTO couPonReq) {
        //需要判断验券信息是否为空
        if(CollectionUtil.isEmpty(couPonReq.getCouponCodeList())){
            throw new BusinessException("券码为空");
        }
        if(StringUtils.isEmpty(couPonReq.getErpId())){
            throw new BusinessException("验券门店不能为空");
        }
        //根据业务门店查询抖音门店信息
        GroupStoreBindDO bindStore = groupStoreBindService.getOne(new LambdaQueryWrapper<GroupStoreBindDO>().eq(GroupStoreBindDO::getStoreGuid, couPonReq.getErpId())
                .eq(GroupStoreBindDO::getType, couPonReq.getGroupBuyType()));
        if(bindStore == null){
            throw new BusinessException(Constant.COUPON_UN_BIND_STORE);
        }
        String request = DouYinCouponVerifyReqDTO.buildJsonString(couPonReq.getCouponCodeList(), bindStore.getPoiId(),couPonReq.getErpOrderId());
        String rsp = postRequest(request, getToken(), groupBuyDouYinConfig.host + groupBuyDouYinConfig.certificateVerify);
        log.info("抖音验券返回信息：{}",rsp);
        //验券返回处理
        List<DouYinVerifyResult> verifyResultList = DouYinCouponVerifyRspDTO.parseJsonAndCheck(rsp);
        return GroupBuyConverter.toDouYinCouponVerifyRespList(verifyResultList);
    }

    @Override
    public MtDelCouponRespDTO revokeCoupon(GroupVerifyDTO revokeReq) {
        log.info("抖音撤销验券入参,revokeReq:{}", JacksonUtils.writeValueAsString(revokeReq));
        if(StringUtils.isEmpty(revokeReq.getCertificateId()) || StringUtils.isEmpty(revokeReq.getVerifyId())){
            throw new BusinessException("撤销验券信息有误");
        }
        String rsp = postRequest(DouYinCouponCancelReqDTO.buildJsonString(revokeReq.getCertificateId(), revokeReq.getVerifyId()),
                getToken(), groupBuyDouYinConfig.host + groupBuyDouYinConfig.certificateCancel);
        DouYinRspDTO<DouYinCommonDTO> couponCancelRsp = JSONObject.parseObject(rsp,new TypeReference<DouYinRspDTO<DouYinCommonDTO>>(){}.getType());
        if (couponCancelRsp == null || couponCancelRsp.getData() == null){
            throw new BusinessException("抖音撤销验券返回为空");
        }
        if(couponCancelRsp.getData().getErrorCode() != 0){
            return MtDelCouponRespDTO.buildError(couponCancelRsp.getData().getDescription());
        }
        return MtDelCouponRespDTO.buildSuccess();
    }

    private DouYinStoreBindRspDTO remoteBindStore(StoreBindDTO storeBind) {
        String storeQueryUrlParams = DouYinStoreQueryReqDTO.buildUrlParams(storeBind.getPoiId());
        //先查询绑定门店
        String queryStoreRsp = getRequest(storeQueryUrlParams, getToken(), groupBuyDouYinConfig.host + groupBuyDouYinConfig.shopQuery);
        //抖音门店信息
        DouYinPoi.Poi poi = DouYinPoi.parseJsonAndFilterPoi(queryStoreRsp);

        //提交门店绑定
        String jsonStoreBindRequest = DouYinStoreBindReqDTO.buildJsonString(poi,storeBind.getStoreGuid());
        String storeBindRsp = postRequest(jsonStoreBindRequest, getToken(), groupBuyDouYinConfig.host + groupBuyDouYinConfig.storeMatchSubmitUrl);
        //绑定抖音门店返回数据
        DouYinStoreBindRspDTO douYinStoreBindRsp = DouYinStoreBindRspDTO.parseJsonAndCheck(storeBindRsp);
        douYinStoreBindRsp.setPoiName(poi.getPoiName());
        return douYinStoreBindRsp;
    }

    public void queryTask(String taskId){
        //需要通过任务id去查询绑定状态
        String bindQueryRsp = getRequest("?task_id=" + taskId, getToken(), groupBuyDouYinConfig.host + groupBuyDouYinConfig.storeMatchQueryUrl);
        log.info("抖音门店绑定返回信息：{}",bindQueryRsp);
        DouYinBindResult douYinBindResult = DouYinBindResult.parseJson(bindQueryRsp);
        //如果任务正在进行中 是否需要重试
        if(douYinBindResult.isProcess()){
            throw new GroupBuyException("绑定抖音门店失败");
        };
        if(douYinBindResult.isSuccess()){
            return;
        }
        throw new GroupBuyException(douYinBindResult.getMatchMessage());

    }

    @Override
    public String getToken() {
        if(groupBuyDouYinConfig.isSandbox()){
            return groupBuyDouYinConfig.sandboxToken;
        }
        //先从redis取token 如果不存在则请求外部
        String cacheToken = redisTemplate.opsForValue().get(GROUP_BUY_TOKEN + GroupBuyTypeEnum.DOU_YIN);
        if(StringUtils.isNotEmpty(cacheToken)){
            return cacheToken;
        }
        //远程获取token
        String rsp = postRequest(commonBody(),null,groupBuyDouYinConfig.tokenUrl);
        DouYinTokenDTO douYinToken = DouYinTokenDTO.parseJson(rsp);

        redisTemplate.opsForValue().set(GROUP_BUY_TOKEN + GroupBuyTypeEnum.DOU_YIN,douYinToken.getAccessToken(),douYinToken.getExpiresIn(), TimeUnit.SECONDS);
        return douYinToken.getAccessToken();
    }

    private String commonBody(){
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("client_key",groupBuyDouYinConfig.getAppId());
        jsonObject.put("client_secret",groupBuyDouYinConfig.getAppSecret());
        jsonObject.put("grant_type","client_credential");
        return jsonObject.toJSONString();
    }

    private String postRequest(String body,String token,String url){
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        if(StringUtils.isNotEmpty(token)){
            headers.add("access-token",token);
        }
        if(groupBuyDouYinConfig.isSandbox()){
            headers.add("X-Sandbox-Token", String.valueOf(1));
        }
        HttpEntity<String> entity = new HttpEntity<>(body, headers);
        ResponseEntity<String> stringResponseEntity;
        try {
            stringResponseEntity = groupBuyRestTemplate.postForEntity(url, entity, String.class);
        }catch (HttpClientErrorException e){
            log.info("请求抖音异常：{}",e.getResponseBodyAsString());
            throw new GroupBuyException(REMOTE_ERROR);
        }
        return getRspBody(stringResponseEntity);

    }

    private String getRequest(String urlParams, String token, String url){
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.parseMediaType("application/json;"));
        headers.add("access-token",token);
        if(groupBuyDouYinConfig.isSandbox()){
            headers.add("X-Sandbox-Token", String.valueOf(1));
        }
        HttpEntity<JSONObject> httpEntity = new HttpEntity<>(null, headers);
        ResponseEntity<String> stringResponseEntity;
        try {
            stringResponseEntity = groupBuyRestTemplate.exchange(url + urlParams, HttpMethod.GET, httpEntity, String.class);
        }catch (HttpClientErrorException e){
            log.info("请求抖音服务异常：{}",e.getResponseBodyAsString());
            throw new GroupBuyException(REMOTE_ERROR);
        }
        return getRspBody(stringResponseEntity);
    }

    private String getRspBody(ResponseEntity<String> stringResponseEntity) {
        log.info("请求抖音服务返回参数信息：{}",stringResponseEntity);
        if(ObjectUtil.isNull(stringResponseEntity)){
            throw new GroupBuyException(REMOTE_ERROR);
        }
        //响应状态
        HttpStatus statusCode = stringResponseEntity.getStatusCode();
        if(statusCode != HttpStatus.OK){
            throw new GroupBuyException(REMOTE_ERROR);
        }
        return stringResponseEntity.getBody();
    }


}
