package com.holder.saas.store.takeaway.producers.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holder.saas.store.takeaway.producers.entity.domain.JdStoreMappingDO;

import java.util.List;

public interface JdStoreMappingService extends IService<JdStoreMappingDO> {

    /**
     * 根据门店查询授权信息
     * @param storeId 门店id
     * @return 授权信息
     */
    JdStoreMappingDO getByVenderStoreAndToken(String storeId);

    /**
     * 根据系统门店guid查询授权信息
     * @param storeGuid 门店guid
     * @return 授权信息
     */
    JdStoreMappingDO getByStoreGuid(String storeGuid);

    List<JdStoreMappingDO> listByStoreGuids(List<String> storeGuids);
}
