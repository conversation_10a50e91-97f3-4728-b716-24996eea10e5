package com.holder.saas.store.takeaway.producers.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holder.saas.store.takeaway.producers.entity.domain.EleAuthDO;
import com.holder.saas.store.takeaway.producers.entity.domain.HolderAuthDO;
import com.holder.saas.store.takeaway.producers.mapper.HolderAuthMapper;
import com.holder.saas.store.takeaway.producers.service.*;
import com.holder.saas.store.takeaway.producers.utils.ThrowableExtUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.takeaway.OrderType;
import com.holderzone.saas.store.dto.takeaway.OwnTypeEnum;
import com.holderzone.saas.store.dto.takeaway.UnOrder;
import com.holderzone.saas.store.dto.takeaway.UnOrderCbMsgType;
import com.holderzone.saas.store.dto.takeaway.request.SalesOrderDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 解析饿了吗订单类型并发送消息
 */
@Slf4j
@Service
public class OwnCallbackServiceImpl extends ServiceImpl<HolderAuthMapper, HolderAuthDO> implements OwnCallbackService {

    private final OwnUnOrderParser ownUnOrderParse;

    private final UnOrderMqService unOrderMqService;

    @Autowired
    public OwnCallbackServiceImpl(UnOrderMqService unOrderMqService, OwnUnOrderParser ownUnOrderParse) {
        this.unOrderMqService = unOrderMqService;
        this.ownUnOrderParse = ownUnOrderParse;
    }

    /**
     * 应用授权解除或发送mq消息
     *
     * @param
     */
    @Override
    public void orderCallback(SalesOrderDTO salesOrderDTO) {
        HolderAuthDO holderAuthDO = getOne(new LambdaQueryWrapper<HolderAuthDO>()
                .eq(HolderAuthDO::getStoreGuid, salesOrderDTO.getStoreGuid()));
        String storeGuid = holderAuthDO.getStoreGuid();
        String enterpriseGuid = holderAuthDO.getEnterpriseGuid();

        try {
            UnOrder unOrder = parseToUnOrder(salesOrderDTO);
            if (unOrder == null) {
                log.error("(自营外卖平台)接收数据错误");
            } else {
                unOrder.setCbMsgType(UnOrderCbMsgType.ORDER_OWN_SAVE);
                unOrder.setStoreGuid(storeGuid);
                unOrder.setEnterpriseGuid(enterpriseGuid);
                unOrder.setOrderType(OrderType.TAKEOUT_ORDER.getType());
                unOrder.setOrderSubType(OrderType.TakeoutSubType.OWN_TAKEOUT.getType());
                unOrderMqService.sendUnOrder(unOrder);
            }
        } catch (Exception e) {
            if (log.isErrorEnabled()) {
                log.error("(自营外卖平台)订单回调，处理失败，异常：{}", ThrowableExtUtils.asStringIfAbsent(e));
            }
        }
    }

    /**
     * 判定饿了吗订单回调类型
     *
     * @param salesOrderDTO
     * @return
     */
    private UnOrder parseToUnOrder(SalesOrderDTO salesOrderDTO) {
        UnOrder unOrder = null;
        //待支付;1支付完成;2订单完成）"
        if (salesOrderDTO.getOrderStatus() == OwnTypeEnum.支付完成.getType()) {
            unOrder = ownUnOrderParse.fromOrderCreated(salesOrderDTO);
            if (log.isInfoEnabled()) {
                log.info("(自营外卖平台)订单回调，向ERP发送(自营外卖平台)新订单，unOrder：{}", JacksonUtils.writeValueAsString(unOrder));
            }
        }
        return unOrder;
    }

    private Wrapper<EleAuthDO> wrapperByUserId(long userId) {
        return new LambdaQueryWrapper<EleAuthDO>().eq(EleAuthDO::getUserId, userId);
    }
}
