package com.holder.saas.store.takeaway.producers.utils.sdk.jd.dto;

import lombok.Data;

import java.util.List;

/**
 * 京东售后申请信息响应类
 */
@Data
public class OrderAfsApplyInfoResponse {
    
    // 订单基本信息
    private String orderId;               // 订单号
    private Long orderTotalMoney;         // 订单总金额
    private String orderPayType;          // 订单支付类型
    private String orderType;             // 订单类型
    private String customerName;          // 用户名
    private String customerMobilePhone;   // 用户手机号
    private String address;               // 取件地址
    
    // 售后申请详情列表
    private List<AfsApplyDetailVender> afsDetailList;  // 售后申请详情
    
    // 售后问题列表
    private List<AfsServiceQuestionVender> afsServiceQuestionVenderList; // 售后原因列表
    
    // 退货相关信息
    private String dutyAssume;            // 责任方
    private String dealType;              // 处理方式
    private String returnDetailAddress;   // 用户自寄退货收件人详细地址
    private String returnAddresseeName;   // 用户自寄退货收件人名称
    private String returnAddresseeMobilePhone; // 用户自寄退货收件人电话
    
    /**
     * 售后申请详情项
     */
    @Data
    public static class AfsApplyDetailVender {
        private Long skuId;               // 商品id
        private String skuName;           // 商品名称
        private Long skuPrice;            // 商品价格
        private Integer promotionType;    // 商品促销价
        private Integer mealboxMoney;     // 商品餐盒费
        private Integer skuCount;         // 订单中商品数量
        private Integer skuApplyNum;      // 商品可申请数量
        private String skuSpecification;  // 商品规格
        private Boolean canPartialRefundFlag; // 售后商品是否可以部分退款标识
    }
    
    /**
     * 售后问题
     */
    @Data
    public static class AfsServiceQuestionVender {
        private Integer questionTypeCode;  // 售后原因编码
        private String questionTypeName;   // 售后原因名称
    }
}
