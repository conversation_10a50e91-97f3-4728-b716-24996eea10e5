package com.holder.saas.store.takeaway.producers.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holder.saas.store.takeaway.producers.entity.domain.HolderAuthDO;
import com.holder.saas.store.takeaway.producers.entity.domain.MtAuthDO;
import com.holder.saas.store.takeaway.producers.mapper.HolderAuthMapper;
import com.holder.saas.store.takeaway.producers.service.DistributedService;
import com.holder.saas.store.takeaway.producers.service.HolderAuthService;
import com.holder.saas.store.takeaway.producers.service.UnOrderMqService;
import com.holder.saas.store.takeaway.producers.service.rpc.ConsumersFeignService;
import com.holder.saas.store.takeaway.producers.utils.HttpRequestUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.takeaway.*;
import com.holderzone.saas.store.dto.takeaway.request.*;
import com.holderzone.saas.store.dto.takeaway.response.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

/**
 * 饿了吗认证授权相关
 */
@Slf4j
@Service("holderAutoHandle")
public class HolderAuthServiceImpl extends ServiceImpl<HolderAuthMapper, HolderAuthDO> implements HolderAuthService {

    private String success = "10000";

    private String failure = "-1";

    /**
     * 绑定URL
     */
    @Value("${own.URL}")
    private String url;

    /**
     * 菜品相关、订单相关URL
     */
    @Value("${own.ITEM_URL}")
    private String itemUrl;

    /**
     * 菜品查询
     */
    @Value("${own.ITEM_QUERY}")
    private String itemQuery;


    /**
     * 菜品绑定
     */
    @Value("${own.ITEM_BINGING}")
    private String itemBinding;

    /**
     * 菜品解绑
     */
    @Value("${own.ITEM_UNBINGING}")
    private String itemUnBinding;

    /**
     * 查询配送方式
     */
    @Value("${own.DISTRIBUTION_QUERY}")
    private String distributionQuery;


    /**
     * 更改订单状态
     */
    @Value("${own.ORDER_UPDATE}")
    private String orderUpdate;


    /**
     * 门店绑定
     */
    @Value("${own.BINDING_URL}")
    private String bindingUrl;

    /**
     * 门店解绑
     */
    @Value("${own.UNBINDING_URL}")
    private String unBindingUrl;

    /**
     * 刷新token
     */
    @Value("${own.REFRESH_URL}")
    private String refreshUrl;

    /**
     * appId
     */
    @Value("${own.APP_ID}")
    private String appId;

    /**
     * appSecret
     */
    @Value("${own.APP_SECRET}")
    private String appSecret;

    /**
     * 预设刷新时间 tokenRefreshAheadHours
     */
    @Value("${own.TOKEN_REFRESH_AHEAD_HOURS}")
    private Integer tokenRefreshAheadHours;

    /**
     * tocken有效时间 refreshTokenValidityPeriodDays
     */
    @Value("${own.REFRESH_TOKEN_VALIDITY_PERIOD_DAYS}")
    private Integer refreshTokenValidityPeriodDays;

    private final DistributedService distributedService;

    private final ConsumersFeignService consumersFeignService;

    private final UnOrderMqService unOrderMqService;

    @Autowired
    public HolderAuthServiceImpl(DistributedService distributedService, ConsumersFeignService consumersFeignService,
                                 UnOrderMqService unOrderMqService) {
        this.distributedService = distributedService;
        this.consumersFeignService = consumersFeignService;
        this.unOrderMqService = unOrderMqService;
    }

    /**
     * 绑定自营外卖门店
     *
     * @param
     * @return
     */
    @Override
    public TakeoutOwnRespDTO doTakeOutBind(TakeoutShopOwnBindReqDTO takeoutShopOwnBindReqDTO) {
        TakeoutOwnShopBindReqDTO own = new TakeoutOwnShopBindReqDTO();
        TakeoutOwnRespDTO takeoutOwnRespDTO = new TakeoutOwnRespDTO();


        own.setStoreCode(takeoutShopOwnBindReqDTO.getStoreCode());
        own.setTel(takeoutShopOwnBindReqDTO.getTel());
        own.setPassWord(takeoutShopOwnBindReqDTO.getPassWord());
        own.setAppId(appId);
        own.setAppSecret(appSecret);
        own.setThirdStoreCode(takeoutShopOwnBindReqDTO.getStoreGuid());
        //fixme sign暂时先写all，后续再做签名的生成
        own.setSign("all");

        String jsonParam = JacksonUtils.writeValueAsString(own);
        log.info("【自营外卖平台】绑定参数：url={},param={}", url + bindingUrl, jsonParam);
        String result = HttpRequestUtils.sendHttpRequest(url + bindingUrl, jsonParam, "-1");
        log.info("【自营外卖平台】绑定结果：result={}", result);
        if (result == null) {
            return null;
        }

        try {
            takeoutOwnRespDTO = JacksonUtils.toObject(TakeoutOwnRespDTO.class, result);
            log.info("takeoutOwnRespDTO={}", takeoutOwnRespDTO);
            if (!success.equals(takeoutOwnRespDTO.getCode())) {
                return takeoutOwnRespDTO;
            } else {
                TakeoutOwnBindRespDTO takeoutOwnBindRespDTO = takeoutOwnRespDTO.getData();
                log.info("takeoutOwnBindRespDTO={}", takeoutOwnBindRespDTO);
                String accessToken = takeoutOwnBindRespDTO.getToken();
                HolderAuthDO holderAuthDO = HolderAuthDO.ofToken(accessToken, accessToken, refreshTokenValidityPeriodDays);
                holderAuthDO.setGuid(distributedService.nextOwnGuid());
                holderAuthDO.setCode(takeoutOwnBindRespDTO.getEnterpriseCode());
                holderAuthDO.setShopName(takeoutOwnBindRespDTO.getEnterpriseName());
                holderAuthDO.setEnterpriseGuid(takeoutShopOwnBindReqDTO.getEnterpriseGuid());
                holderAuthDO.setUserId(Long.parseLong(takeoutShopOwnBindReqDTO.getUserGuid()));
                holderAuthDO.setUserName(takeoutShopOwnBindReqDTO.getUserName());
                holderAuthDO.setStoreGuid(takeoutShopOwnBindReqDTO.getStoreGuid());
                this.save(holderAuthDO);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.info("message={}", e.getMessage());
        }
        return takeoutOwnRespDTO;
    }


    /**
     * 解绑自营外卖门店
     *
     * @param
     * @return
     */
    @Override
    public TakeoutOwnRespDTO doTakeOutUnBind(TakeoutShopOwnUnBindReqDTO takeoutShopOwnUnBindReqDTO) {
        TakeoutOwnShopBindReqDTO own = new TakeoutOwnShopBindReqDTO();
        String token = this.getToken(this.getHolder(takeoutShopOwnUnBindReqDTO.getStoreGuid()));
        HolderAuthDO holderAuthDO = this.getHolder(takeoutShopOwnUnBindReqDTO.getStoreGuid());
        if ("-1".equals(token) || token == null || holderAuthDO == null) {
            throw new BusinessException("门店未绑定!请绑定门店后重试!");
        }
        TakeoutOwnRespDTO takeoutOwnRespDTO;
        own.setStoreCode(takeoutShopOwnUnBindReqDTO.getStoreCode());
        own.setTel(takeoutShopOwnUnBindReqDTO.getTel());
        own.setPassWord(takeoutShopOwnUnBindReqDTO.getPassWord());
        own.setAppId(appId);
        //fixme sign暂时先写all，后续再做签名的生成
        own.setSign("all");

        String jsonParam = JacksonUtils.writeValueAsString(own);
        log.info("【自营外卖平台】解绑参数：url={},param={}", url + unBindingUrl, jsonParam);
        String result = HttpRequestUtils.sendHttpRequest(url + unBindingUrl, jsonParam, token);
        log.info("【自营外卖平台】解绑结果：result={}", result);
        if (result == null) {
            return null;
        }

        takeoutOwnRespDTO = JacksonUtils.toObject(TakeoutOwnRespDTO.class, result);
        if (!success.equals(takeoutOwnRespDTO.getCode())) {
            return takeoutOwnRespDTO;
        } else {
            update(new HolderAuthDO(), new UpdateWrapper<HolderAuthDO>().lambda()
                    .eq(HolderAuthDO::getId, holderAuthDO.getId())
                    .set(HolderAuthDO::getDeleted, Boolean.TRUE));
            return takeoutOwnRespDTO;
        }
    }


    /**
     * 根据门店查询所有菜品
     *
     * @param storeGuid
     * @return
     */
    @Override
    public List<TakeoutOwnItemMappingRespDTO> getItem(String storeGuid) {
        String code = this.getCode(storeGuid);
        String token = this.getToken(this.getHolder(storeGuid));
        if ("-1".equals(token) || "-1".equals(code)) {
            throw new BusinessException("门店未绑定!请绑定门店后重试!");
        }
        TakeoutOwnItemReqDTO takeoutOwnItemReqDTO = new TakeoutOwnItemReqDTO();
        takeoutOwnItemReqDTO.setAppId(appId);
        takeoutOwnItemReqDTO.setSign("all");
        takeoutOwnItemReqDTO.setStoreCode(code);

        String jsonParam = JacksonUtils.writeValueAsString(takeoutOwnItemReqDTO);
        String httpUrl = itemUrl + itemQuery;
        log.info("【自营外卖平台】查询菜品入参：url={},param={}", httpUrl, jsonParam);
        String result = HttpRequestUtils.sendHttpRequest(httpUrl, jsonParam, token, code);
        log.info("【自营外卖平台】查询菜品结果：result={}", result);
        if (result == null) {
            return null;
        }
        TakeoutOwnItemQueryNewRespDTO take = JacksonUtils.toObject(TakeoutOwnItemQueryNewRespDTO.class, result);
        List<TakeoutOwnItemMappingRespDTO> data = take.getData().getData();
        log.info("【自营外卖平台】查询菜品返回：data={}", data);
        return data;
    }

    @Override
    public String itemBind(TakeoutShopOwnItemBindReqDTO takeoutShopOwnItemBindReqDTO, String code, String token) {
        takeoutShopOwnItemBindReqDTO.setStoreCode(code);
        String jsonParam = JacksonUtils.writeValueAsString(takeoutShopOwnItemBindReqDTO);
        String httpUrl = itemUrl + itemBinding;
        log.info("【自营外卖平台】绑定菜品入参：url={},param={}", httpUrl, jsonParam);
        String result = HttpRequestUtils.sendHttpRequest(httpUrl, jsonParam, token, code);
        log.info("【自营外卖平台】绑定菜品结果：result={}", result);
        if (result == null) {
            return null;
        }
        TakeoutOwnRespDTO takeoutOwnRespDTO = JacksonUtils.toObject(TakeoutOwnRespDTO.class, result);
        if (success.equals(takeoutOwnRespDTO.getCode())) {
            return "SUCCESS";
        } else {
            return "FAILURE";
        }
    }

    @Override
    public HolderAuthDO getHolder(String storeGuid) {
        //查询Holder
        HolderAuthDO holderAuthDO = getOne(new LambdaQueryWrapper<HolderAuthDO>()
                .eq(HolderAuthDO::getStoreGuid, storeGuid));
        return holderAuthDO;
    }

    @Override
    public String itemUnBind(TakeoutShopOwnItemBindReqDTO takeoutShopOwnItemBindReqDTO, String code, String token) {
        takeoutShopOwnItemBindReqDTO.setStoreCode(code);
        String jsonParam = JacksonUtils.writeValueAsString(takeoutShopOwnItemBindReqDTO);
        String httpUrl = itemUrl + itemUnBinding;
        log.info("【自营外卖平台】解绑菜品入参：url={},param={}", httpUrl, jsonParam);
        String result = HttpRequestUtils.sendHttpRequest(httpUrl, jsonParam, token, code);
        log.info("【自营外卖平台】解绑菜品结果：result={}", result);
        if (result == null) {
            return null;
        }
        TakeoutOwnRespDTO takeoutOwnRespDTO = JacksonUtils.toObject(TakeoutOwnRespDTO.class, result);
        if (success.equals(takeoutOwnRespDTO.getCode())) {
            return "SUCCESS";
        } else {
            return "FAILURE";
        }
    }

    @Override
    public String getCode(String storeGuid) {
        //查询code
        String code = "";
        HolderAuthDO holderAuthDO = getOne(new LambdaQueryWrapper<HolderAuthDO>()
                .eq(HolderAuthDO::getStoreGuid, storeGuid));
        if (holderAuthDO == null) {
            code = "-1";
        } else {
            code = holderAuthDO.getCode();
        }
        return code;
    }

    @Override
    public List<OwnDistributionDTO> getDistribution(BaseDTO baseDTO) {
        String storeGuid = baseDTO.getStoreGuid();
        String token = this.getToken(this.getHolder(storeGuid));
        String code = this.getCode(storeGuid);
        if ("-1".equals(token) || "-1".equals(code)) {
            throw new BusinessException("门店未绑定!请绑定门店后重试!");
        }
        TakeoutOwnItemReqDTO takeoutOwnItemReqDTO = new TakeoutOwnItemReqDTO();
        takeoutOwnItemReqDTO.setAppId(appId);
        takeoutOwnItemReqDTO.setSign("all");

        String jsonParam = JacksonUtils.writeValueAsString(takeoutOwnItemReqDTO);
        String httpUrl = itemUrl + distributionQuery;
        log.info("【自营外卖平台】查询配送方式入参：url={},param={},code={}", httpUrl, jsonParam, code);
        String result = HttpRequestUtils.sendHttpRequest(httpUrl, jsonParam, token, code);
        log.info("【自营外卖平台】查询配送方式结果：result={}", result);
        if (result == null) {
            return null;
        }
        TakeoutOwnResponseDTO resp = JacksonUtils.toObject(TakeoutOwnResponseDTO.class, result);

        if (!success.equals(resp.getCode())) {
            return Collections.emptyList();
        } else {
            return resp.getData();
        }
    }

    @Override
    public OwnCallbackResponse goShipping(TakeoutOrderDTO takeoutOrderDTO) {
        HolderAuthDO holderAuthDO = getOne(new LambdaQueryWrapper<HolderAuthDO>()
                .eq(HolderAuthDO::getStoreGuid, takeoutOrderDTO.getStoreGuid()));
        if (holderAuthDO == null) {
            throw new BusinessException("门店未绑定!请绑定门店后重试!");
        }
        OwnCallbackResponse response = new OwnCallbackResponse();
        String code = holderAuthDO.getCode();
        String token = holderAuthDO.getAccessToken();
        //调用状态变更接口
        TakeoutOwnAcceptReqDTO takeoutOwnAcceptReqDTO = new TakeoutOwnAcceptReqDTO();
        takeoutOwnAcceptReqDTO.setAppId(appId);
        takeoutOwnAcceptReqDTO.setSign("all");
        takeoutOwnAcceptReqDTO.setOrderID(Long.parseLong(takeoutOrderDTO.getOrderId()));
        takeoutOwnAcceptReqDTO.setStatus(OwnTypeEnum.配送中.getType());

        String jsonParam = JacksonUtils.writeValueAsString(takeoutOwnAcceptReqDTO);
        log.info("【自营外卖平台】商家发起配送参数：url={},param={},code={}", itemUrl + orderUpdate, jsonParam, code);
        String result = HttpRequestUtils.sendHttpRequest(itemUrl + orderUpdate, jsonParam, token, code);
        log.info("【自营外卖平台】商家发起配送结果：result={}", result);
        if (result == null) {
            log.error("【自营外卖平台】商家发起配送失败，storeGuid={},orderGuid={}", takeoutOrderDTO.getStoreGuid(), takeoutOrderDTO.getOrderId());
            response.setCode(10001);
            response.setMessage("商家发起配送失败，返回结果为空");
            return response;
        }
        TakeoutOwnAcceptedRespDTO resp = JacksonUtils.toObject(TakeoutOwnAcceptedRespDTO.class, result);
        if (!success.equals(resp.getCode())) {
            log.error("【自营外卖平台】商家发起配送失败，message={}", resp.getMessage());
            response.setCode(Integer.parseInt(resp.getCode()));
            response.setMessage(resp.getMessage());
            return response;
        } else {
            SalesUpdateDTO sale = new SalesUpdateDTO();
            sale.setOrderID(Long.parseLong(takeoutOrderDTO.getOrderId()));
            sale.setDistributionType(takeoutOrderDTO.getDistributionType());
            sale.setOrderStatus(OwnTypeEnum.配送中.getType());
            sale.setStoreGuid(takeoutOrderDTO.getStoreGuid());
            //fixme 去掉下面这句，后续走统一的订单状态变更流程。
            OwnCallbackResponse own = consumersFeignService.orderUpdate(sale);
            if (success.equals(String.valueOf(own.getCode()))) {
                log.info("【自营外卖平台】发起配送成功，message={}", own.getMessage());
                response.setCode(own.getCode());
                response.setMessage("订单发起配送成功");

                //fixme 后续优化：方法返回值void，发送消息给consumer消费，走统一的订单配送状态变更流程。
/*                UnOrder unOrder = new UnOrder();
                unOrder.setOrderId(takeoutOrderDTO.getOrderId());
                unOrder.setOrderSubType(OrderType.TakeoutSubType.OWN_TAKEOUT.getType());
                unOrder.setDeliveryTime(DateTimeUtils.now());
                unOrder.setCbMsgType(UnOrderCbMsgType.ORDER_SHIPPING);
                unOrderMqService.sendUnOrder(unOrder);*/
                //fixme

                return response;
            } else {
                log.error("【自营外卖平台】发起配送失败，message={}", own.getMessage());
                response.setCode(own.getCode());
                response.setMessage(own.getMessage());
                return response;
            }
        }
    }

    @Override
    public OwnCallbackResponse doneShipping(TakeoutOrderDTO takeoutOrderDTO) {
        HolderAuthDO holderAuthDO = getOne(new LambdaQueryWrapper<HolderAuthDO>()
                .eq(HolderAuthDO::getStoreGuid, takeoutOrderDTO.getStoreGuid()));
        if (holderAuthDO == null) {
            throw new BusinessException("门店未绑定!请绑定门店后重试!");
        }
        OwnCallbackResponse response = new OwnCallbackResponse();
        String code = holderAuthDO.getCode();
        String token = holderAuthDO.getAccessToken();
        //调用状态变更接口
        TakeoutOwnAcceptReqDTO takeoutOwnAcceptReqDTO = new TakeoutOwnAcceptReqDTO();
        takeoutOwnAcceptReqDTO.setAppId(appId);
        takeoutOwnAcceptReqDTO.setSign("all");
        takeoutOwnAcceptReqDTO.setOrderID(Long.parseLong(takeoutOrderDTO.getOrderId()));
        takeoutOwnAcceptReqDTO.setStatus(OwnTypeEnum.订单完成.getType());

        String jsonParam = JacksonUtils.writeValueAsString(takeoutOwnAcceptReqDTO);
        log.info("【自营外卖平台】商家完成配送参数：url={},param={},code={}", itemUrl + orderUpdate, jsonParam, code);
        String result = HttpRequestUtils.sendHttpRequest(itemUrl + orderUpdate, jsonParam, token, code);
        log.info("【自营外卖平台】商家完成配送结果：result={}", result);
        if (result == null) {
            log.error("【自营外卖平台】商家完成配送失败，storeGuid={},orderGuid={}", takeoutOrderDTO.getStoreGuid(), takeoutOrderDTO.getOrderId());
            response.setCode(10001);
            response.setMessage("商家完成配送失败，返回结果为空");
            return response;
        }
        TakeoutOwnAcceptedRespDTO resp = JacksonUtils.toObject(TakeoutOwnAcceptedRespDTO.class, result);
        if (!success.equals(resp.getCode())) {
            log.error("【自营外卖平台】商家完成配送失败，message={}", resp.getMessage());
            response.setCode(Integer.parseInt(resp.getCode()));
            response.setMessage(resp.getMessage());
            return response;
        } else {
            SalesUpdateDTO sale = new SalesUpdateDTO();
            sale.setOrderID(Long.parseLong(takeoutOrderDTO.getOrderId()));
            sale.setDistributionType(takeoutOrderDTO.getDistributionType());
            sale.setOrderStatus(OwnTypeEnum.订单完成.getType());
            sale.setStoreGuid(takeoutOrderDTO.getStoreGuid());
            //fixme 去掉下面这句，后续走统一的订单状态变更流程。
            OwnCallbackResponse own = consumersFeignService.orderUpdate(sale);
            if (success.equals(String.valueOf(own.getCode()))) {
                log.info("【自营外卖平台】完成配送成功，message={}", own.getMessage());
                response.setCode(own.getCode());
                response.setMessage("订单完成配送");

                //fixme 后续优化：方法返回值void，发送消息给consumer消费，走统一的订单配送状态变更流程。
/*                UnOrder unOrder = new UnOrder();
                unOrder.setOrderId(takeoutOrderDTO.getOrderId());
                unOrder.setOrderSubType(OrderType.TakeoutSubType.OWN_TAKEOUT.getType());
                unOrder.setCbMsgType(UnOrderCbMsgType.ORDER_FINISHED);
                unOrder.setDeliveredTime(DateTimeUtils.now());
                unOrderMqService.sendUnOrder(unOrder);*/
                //fixme

                return response;
            } else {
                log.error("【自营外卖平台】完成配送失败，message={}", own.getMessage());
                response.setCode(own.getCode());
                response.setMessage(own.getMessage());
                return response;
            }
        }
    }

    @Override
    public OwnCallbackResponse cancelShipping(TakeoutOrderDTO takeoutOrderDTO) {
        HolderAuthDO holderAuthDO = getOne(new LambdaQueryWrapper<HolderAuthDO>()
                .eq(HolderAuthDO::getStoreGuid, takeoutOrderDTO.getStoreGuid()));
        if (holderAuthDO == null) {
            throw new BusinessException("门店未绑定!请绑定门店后重试!");
        }
        OwnCallbackResponse response = new OwnCallbackResponse();
        String code = holderAuthDO.getCode();
        String token = holderAuthDO.getAccessToken();
        //调用状态变更接口
        TakeoutOwnAcceptReqDTO takeoutOwnAcceptReqDTO = new TakeoutOwnAcceptReqDTO();
        takeoutOwnAcceptReqDTO.setAppId(appId);
        takeoutOwnAcceptReqDTO.setSign("all");
        takeoutOwnAcceptReqDTO.setOrderID(Long.parseLong(takeoutOrderDTO.getOrderId()));
        takeoutOwnAcceptReqDTO.setStatus(OwnTypeEnum.已取消.getType());

        String jsonParam = JacksonUtils.writeValueAsString(takeoutOwnAcceptReqDTO);
        log.info("【自营外卖平台】商家取消配送参数：url={},param={},code={}", itemUrl + orderUpdate, jsonParam, code);
        String result = HttpRequestUtils.sendHttpRequest(itemUrl + orderUpdate, jsonParam, token, code);
        log.info("【自营外卖平台】商家取消配送结果：result={}", result);
        if (result == null) {
            log.error("【自营外卖平台】商家取消配送失败，storeGuid={},orderGuid={}", takeoutOrderDTO.getStoreGuid(), takeoutOrderDTO.getOrderId());
            response.setCode(10001);
            response.setMessage("商家取消配送失败，返回结果为空");
            return response;
        }
        TakeoutOwnAcceptedRespDTO resp = JacksonUtils.toObject(TakeoutOwnAcceptedRespDTO.class, result);
        if (!success.equals(resp.getCode())) {
            log.error("【自营外卖平台】商家取消配送失败，message={}", resp.getMessage());
            response.setCode(Integer.parseInt(resp.getCode()));
            response.setMessage(resp.getMessage());
            return response;
        } else {
            SalesUpdateDTO sale = new SalesUpdateDTO();
            sale.setOrderID(Long.parseLong(takeoutOrderDTO.getOrderId()));
            sale.setDistributionType(takeoutOrderDTO.getDistributionType());
            sale.setOrderStatus(OwnTypeEnum.已取消.getType());
            sale.setStoreGuid(takeoutOrderDTO.getStoreGuid());
            //fixme 去掉下面这句，后续走统一的订单状态变更流程。
            OwnCallbackResponse own = consumersFeignService.orderUpdate(sale);
            if (success.equals(String.valueOf(own.getCode()))) {
                log.info("【自营外卖平台】取消配送成功，message={}", own.getMessage());
                response.setCode((own.getCode()));
                response.setMessage("订单取消成功");


                //fixme 后续优化：方法返回值void，发送消息给consumer消费，走统一的订单配送状态变更流程。
                /*UnOrder unOrder = new UnOrder();
                unOrder.setOrderId(takeoutOrderDTO.getOrderId());
                unOrder.setOrderSubType(OrderType.TakeoutSubType.OWN_TAKEOUT.getType());
                unOrder.setCancelReason("");
                unOrder.setCancelTime(DateTimeUtils.now());
                unOrder.setCbMsgType(UnOrderCbMsgType.ORDER_CANCELED);
                unOrderMqService.sendUnOrder(unOrder);*/
                //fixme

                return response;
            } else {
                log.error("【自营外卖平台】取消配送失败，message={}", own.getMessage());
                response.setCode((own.getCode()));
                response.setMessage(own.getMessage());
                return response;
            }
        }
    }


    /**
     * 授权码(企业)模式刷新Token
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void refreshToken(long beforeTime) {
        List<HolderAuthDO> expireTokenList = list(new LambdaQueryWrapper<HolderAuthDO>()
                .le(HolderAuthDO::getExpireTime, DateTimeUtils.now().plusSeconds(beforeTime)));
        expireTokenList.forEach(this::refreshToken);
    }

    /**
     * 刷新数据库中指定的token，并更新到库中
     *
     * @param
     * @return
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public String refreshToken(HolderAuthDO holderAuthDO) {
        String token = holderAuthDO.getAccessToken();
        String shopName = holderAuthDO.getShopName();
        String httpUrl = url + refreshUrl;
        log.info("【自营外卖平台】刷新token：url={},shopName={},token={}", httpUrl, shopName, token);
        String result = HttpRequestUtils.sendHttpRequest(httpUrl, "", token);
        log.info("【自营外卖平台】刷新token：result={}", result);
        if (result == null) {
            log.error("error_token: 自营平台刷新token返回为空");
            return null;
        }
        TakeoutOwnTokenDTO resp = JacksonUtils.toObject(TakeoutOwnTokenDTO.class, result);

        if (resp == null) {
            log.error("error_token: 转换token为空");
            return null;
        }
        if (!success.equals(resp.getCode())) {
            if ("Unauthorized refresh token.".equalsIgnoreCase(resp.getMessage())) {
                if (remove(new LambdaQueryWrapper<HolderAuthDO>()
                        .eq(HolderAuthDO::getId, holderAuthDO.getId()))) {
                    log.error("error_token: {}", JacksonUtils.writeValueAsString(token));
                }
            } else {
                log.error("error_token: {}", JacksonUtils.writeValueAsString(token));
            }
            return null;
        }
        String refreshToken = resp.getData();
        if (!updateById(HolderAuthDO.ofToken(refreshToken, refreshToken, refreshTokenValidityPeriodDays).setId(holderAuthDO.getId()))) {
            HolderAuthDO entity = HolderAuthDO.ofToken(refreshToken, refreshToken, refreshTokenValidityPeriodDays);
            entity.setEnterpriseGuid(holderAuthDO.getEnterpriseGuid());
            entity.setStoreGuid(holderAuthDO.getStoreGuid());
            entity.setUserId(holderAuthDO.getUserId());
            save(entity.setGuid(distributedService.nextEleGuid()));
        }
        return holderAuthDO.getCode();
    }


    /**
     * 根据eleAuthDO获取token
     *
     * @param
     * @return
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public String getToken(HolderAuthDO holderAuthDO) {
        if (holderAuthDO == null) {
            return null;
        }
        //获得门店token过期时间
        LocalDateTime expireTime = holderAuthDO.getExpireTime();
        //token过期时间-1小时（token到期前几小时刷新）
        LocalDateTime refreshTime = expireTime.minusHours(tokenRefreshAheadHours);
        LocalDateTime nowTime = DateTimeUtils.now();
        //刷新Token过期时间
        LocalDateTime refreshExpireTime = holderAuthDO.getRefreshExpireTime();

        //fixme 因为小程序端没做tocken刷新功能，目前暂时取消所有关于tocken的校验
        //如果当前时间大于刷新Token过期时间，则过期，删除token;
        //目前这里做了定时刷新，程序不出问题的情况下，不会进入该判断
    /*    if (nowTime.compareTo(refreshExpireTime) >= 0) {
            log.info("RefreshToken，过期时间：{}，现在时间：{}，删除Token", refreshExpireTime, nowTime);
            removeById(holderAuthDO.getId());
            return null;
        }*/

        //fixme 因为小程序端没做tocken刷新功能，目前暂时取消所有关于tocken的校验
        //如果当前时间大于预设刷新时间，则刷新token
        /*if (nowTime.compareTo(refreshTime) >= 0) {
            log.info("Token，过期时间：{}，预设刷新时间：{}，现在时间：{}，刷新Token", expireTime, refreshTime, nowTime);
            return refreshToken(holderAuthDO);
        }*/

        //如果当前时间小于预设刷新时间，则处于有效期，拿到数据，返回。
        return holderAuthDO.getAccessToken();
    }

    /**
     * 根据erp门店id查询饿了么外卖授权表，是否已经授权
     *
     * @param storeAuthDTO
     * @return
     */
    @Override
    public StoreAuthDTO getTakeoutAuth(StoreAuthDTO storeAuthDTO) {
        HolderAuthDO holderAuthDO = getOne(new LambdaQueryWrapper<HolderAuthDO>()
                .eq(HolderAuthDO::getStoreGuid, storeAuthDTO.getStoreGuid()));
        if (holderAuthDO == null) {
            return storeAuthDTO.setBindingStatus(0);
        }
        return storeAuthDTO.setBindingStatus(1).setShopName(holderAuthDO.getShopName());
    }


    @Override
    public HolderAuthDO getHolderAuth(String storeGuid) {
        HolderAuthDO holderAuthDO = getOne(new LambdaQueryWrapper<HolderAuthDO>()
                .eq(HolderAuthDO::getStoreGuid, storeGuid));
        return holderAuthDO;
    }

    @Override
    public void deleteAuth(String storeGuid) {
        remove(wrapperByPoiIdBizId(storeGuid));
    }

    private Wrapper<HolderAuthDO> wrapperByPoiIdBizId(String storeGuid) {
        return new LambdaQueryWrapper<HolderAuthDO>()
                .eq(HolderAuthDO::getStoreGuid, storeGuid);
    }
}