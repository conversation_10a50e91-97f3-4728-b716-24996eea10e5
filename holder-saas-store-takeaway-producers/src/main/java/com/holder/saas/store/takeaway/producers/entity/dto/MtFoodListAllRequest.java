package com.holder.saas.store.takeaway.producers.entity.dto;

import com.google.gson.annotations.SerializedName;
import com.google.gson.reflect.TypeToken;
import com.meituan.sdk.MeituanRequest;
import com.meituan.sdk.MeituanResponse;
import com.meituan.sdk.annotations.ApiMeta;
import com.meituan.sdk.internal.utils.JsonUtil;

import javax.validation.constraints.NotNull;
import java.lang.reflect.Type;
import java.util.List;

@ApiMeta(
        path = "/waimai/ng/dish/food/listAll",
        businessId = 2,
        apiVersion = "10115",
        apiName = "dish_food_list_all",
        needAuth = true
)
public class MtFoodListAllRequest implements MeituanRequest<List<MtFoodInfo>> {

    @SerializedName("offset")
    private @NotNull(
            message = "offset不能为空"
    ) Integer offset;
    @SerializedName("limit")
    private @NotNull(
            message = "limit不能为空"
    ) Integer limit;
    @SerializedName("needTopping")
    private Boolean needTopping;

    public Integer getOffset() {
        return this.offset;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getLimit() {
        return this.limit;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Boolean getNeedTopping() {
        return this.needTopping;
    }

    public void setNeedTopping(Boolean needTopping) {
        this.needTopping = needTopping;
    }

    public MeituanResponse<List<MtFoodInfo>> deserializeResponse(String response) {
        Type type = (new TypeToken<MeituanResponse<List<MtFoodInfo>>>() {
        }).getType();
        return JsonUtil.fromJson(response, type);
    }

    public String serializeToJson() {
        return JsonUtil.toJson(this);
    }

    public String toString() {
        return "MtFoodListAllRequest{offset=" + this.offset + ",limit=" + this.limit + ",needTopping=" + this.needTopping + "}";
    }
}
