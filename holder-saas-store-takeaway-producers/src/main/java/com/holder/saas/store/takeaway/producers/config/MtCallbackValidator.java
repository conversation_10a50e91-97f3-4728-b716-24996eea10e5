package com.holder.saas.store.takeaway.producers.config;

import com.holderzone.framework.util.Bean2Map;
import com.holderzone.saas.store.dto.takeaway.*;
import com.sankuai.sjst.platform.developer.utils.SignUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class MtCallbackValidator {

    @Value("${mt.SIGN_KEY}")
    private String signKey;

    public boolean checkSignature(MtCallbackDTO mtCallbackDTO) {
        return SignUtils.checkSign(signKey, params(mtCallbackDTO, 1));
    }

    public boolean checkSignature(MtCallbackSettlementDTO mtCallbackDTO) {
        return SignUtils.checkSign(signKey, params(mtCallbackDTO, 2));
    }

    public boolean checkSignature(MtCallbackAuthorizationDTO mtCallbackDTO) {
        return SignUtils.checkSign(signKey, params(mtCallbackDTO, 2));
    }

    public boolean checkSignature(MtCallbackUnbindAuthorizationDTO mtCallbackDTO) {
        return SignUtils.checkSign(signKey, params(mtCallbackDTO, 2));
    }

    public boolean checkSignature(MtCallbackMemberDTO mtCallbackDTO) {
        return SignUtils.checkSign(signKey, params(mtCallbackDTO, 2));
    }

    public boolean checkSignature(MtCallbackConsumeDTO mtCallbackDTO) {
        return SignUtils.checkSign(signKey, params(mtCallbackDTO, 2));
    }


    /**
     * 去掉对象中的null值
     */
    private static Map<String, String> params(Object mtCallbackDTO, Integer version) {
        Map<String, String> params = new HashMap<>();
        Map<String, Object> filedMap = Bean2Map.bean2map(mtCallbackDTO);
        for (Map.Entry<String, Object> entry : filedMap.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            if (key != null && value != null) {
                if ("ePoiId".equalsIgnoreCase(entry.getKey()) && version == 1) {
                    key = "ePoiId";
                }
                params.put(key, value.toString());
            }
        }
        return params;
    }
}
