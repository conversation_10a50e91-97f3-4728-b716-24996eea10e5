package com.holder.saas.store.takeaway.producers.utils.sdk.jd;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.holder.saas.store.takeaway.producers.utils.sdk.jd.dto.*;
import lombok.extern.slf4j.Slf4j;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @description 查询商家下spu列表
 */
@Slf4j
public class QuerySpuInfoRequest extends AbstractJdRequest{


    public QuerySpuInfoRequest() {
    }

    public QuerySpuInfoRequest(String appKey, String appSecret, String token) {
       super.appKey = appKey;
       super.appSecret = appSecret;
       super.token = token;
    }

    public List<SpuSkuDTO> execute(QuerySpuInfoReqDTO spuInfoReq){
        try {
            String rsp = super.execute(JSON.toJSONString(spuInfoReq), "/pms/OpenSpuService/querySpuInfoList");
            CommonRspDTO<SpuQueryResponse> querySpuInfoRspDTO = JSON.parseObject(rsp, new TypeReference<CommonRspDTO<SpuQueryResponse>>(){});
            if(!querySpuInfoRspDTO.isSuccess()){
                return Collections.emptyList();
            }
            List<SpuSkuDTO> spuSkuDTOList = querySpuInfoRspDTO.getResult().getResult();
            if(CollUtil.isEmpty(spuSkuDTOList)){
                return Collections.emptyList();
            }
            return spuSkuDTOList;
        }catch (Exception e){
            log.error("查询商家商品列表失败",e);
        }
        return Collections.emptyList();
    }

    /**
     * 查询所有商品列表
     * @return 商品列表
     */
    public List<SpuSkuDTO> execute(){
        List<SpuSkuDTO> all = Lists.newArrayList();
        int page = 1;
        int pageSize = 20;
        QuerySpuInfoReqDTO reqDTO = QuerySpuInfoReqDTO.builder().pageNo(page).pageSize(pageSize).build();
        // 递归查询
        while (true){
            SpuQueryResponse spuQueryResponse = recursionQuery(reqDTO);
            if(spuQueryResponse == null || CollUtil.isEmpty(spuQueryResponse.getResult())){
                break;
            }
            all.addAll(spuQueryResponse.getResult());
            if (spuQueryResponse.getSearchAfterSpuId() == null) {
                break;
            }
            page++;
            reqDTO.setPageNo(page);
            reqDTO.setSearchAfterSpuId(spuQueryResponse.getSearchAfterSpuId());
        }
                
        return all;
    }

    private SpuQueryResponse recursionQuery(QuerySpuInfoReqDTO reqDTO){
        try {
            String rsp = super.execute(JSON.toJSONString(reqDTO), "/pms/OpenSpuService/querySpuInfoList");
            CommonRspDTO<SpuQueryResponse> querySpuInfoRspDTO = JSON.parseObject(rsp, new TypeReference<CommonRspDTO<SpuQueryResponse>>(){});
            if(!querySpuInfoRspDTO.isSuccess()){
                return null;
            }
            return querySpuInfoRspDTO.getResult();
        }catch (Exception e){
            log.error("查询商家商品列表失败",e);
        }
        return null;
    }
}
