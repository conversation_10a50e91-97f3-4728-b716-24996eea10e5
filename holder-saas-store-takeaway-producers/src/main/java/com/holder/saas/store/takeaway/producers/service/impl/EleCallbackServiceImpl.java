package com.holder.saas.store.takeaway.producers.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holder.saas.store.takeaway.producers.entity.domain.EleAuthDO;
import com.holder.saas.store.takeaway.producers.service.EleAuthService;
import com.holder.saas.store.takeaway.producers.service.EleCallbackService;
import com.holder.saas.store.takeaway.producers.service.EleUnOrderParser;
import com.holder.saas.store.takeaway.producers.service.UnOrderMqService;
import com.holder.saas.store.takeaway.producers.service.rpc.OrganizationService;
import com.holder.saas.store.takeaway.producers.utils.ThrowableExtUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.takeaway.EleCbMsgTypeEnum;
import com.holderzone.saas.store.dto.takeaway.OrderType;
import com.holderzone.saas.store.dto.takeaway.UnOrder;
import eleme.openapi.sdk.api.entity.other.OMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 解析饿了吗订单类型并发送消息
 */
@Slf4j
@Service
public class EleCallbackServiceImpl implements EleCallbackService {

    private final EleAuthService eleAuthService;

    private final EleUnOrderParser eleUnOrderParse;

    private final UnOrderMqService unOrderMqService;

    @Autowired
    public EleCallbackServiceImpl(EleAuthService eleAuthService,
                                  EleUnOrderParser eleUnOrderParse, UnOrderMqService unOrderMqService, OrganizationService organizationService) {
        this.eleAuthService = eleAuthService;
        this.eleUnOrderParse = eleUnOrderParse;
        this.unOrderMqService = unOrderMqService;
    }

    /**
     * 应用授权解除或发送mq消息
     *
     * @param oMessage
     */
    @Override
    public void orderCallback(OMessage oMessage) {
        EleAuthDO eleAuthDO = eleAuthService.getOne(wrapperByUserId(oMessage.getUserId()));
        String enterpriseGuid = eleAuthDO.getEnterpriseGuid();
        String storeGuid = eleAuthDO.getStoreGuid();

        try {
            if (EleCbMsgTypeEnum.应用授权解除通知.equals(EleCbMsgTypeEnum.ofType(oMessage.getType()))) {
                log.error("(饿了么)解绑回调，shopId: {}", oMessage.getShopId());
                eleAuthService.unbindCallback(oMessage);
            } else {
                UnOrder unOrder = parseToUnOrder(oMessage);
                if (unOrder == null) {
                    log.error("(饿了么)回调类型({})未定义具体逻辑", oMessage.getType());
                } else {
                    unOrder.setStoreGuid(storeGuid);
                    unOrder.setEnterpriseGuid(enterpriseGuid);
                    unOrder.setOrderType(OrderType.TAKEOUT_ORDER.getType());
                    unOrder.setOrderSubType(OrderType.TakeoutSubType.ELE_TAKEOUT.getType());
                    unOrderMqService.sendUnOrder(unOrder);
                }
            }
        } catch (Exception e) {
            if (log.isErrorEnabled()) {
                if (EleCbMsgTypeEnum.应用授权解除通知.equals(EleCbMsgTypeEnum.ofType(oMessage.getType()))) {
                    log.error("(饿了么)解绑回调，处理失败，异常：{}", ThrowableExtUtils.asStringIfAbsent(e));
                } else {
                    log.error("(饿了么)订单回调，处理失败，异常：{}", ThrowableExtUtils.asStringIfAbsent(e));
                }
            }
        }
    }

    /**
     * 判定饿了吗订单回调类型
     *
     * @param oMessage
     * @return
     */
    private UnOrder parseToUnOrder(OMessage oMessage) {
        UnOrder unOrder = null;
        switch (EleCbMsgTypeEnum.ofType(oMessage.getType())) {
            case 订单生效:
                unOrder = eleUnOrderParse.fromOrderCreated(oMessage);
                if (log.isInfoEnabled()) {
                    log.info("(饿了么)订单回调，向ERP发送(饿了么)新订单，unOrder：{}", JacksonUtils.writeValueAsString(unOrder));
                }
                break;
            case 商户接单:
                unOrder = eleUnOrderParse.fromOrderConfirmed(oMessage);
                if (log.isInfoEnabled()) {
                    log.info("(饿了么)订单回调，向ERP发送(饿了么)接单，unOrder：{}", JacksonUtils.writeValueAsString(unOrder));
                }
                break;
            case 配送员取餐中:
                unOrder = eleUnOrderParse.fromOrderShippingDistribute(oMessage);
                if (log.isInfoEnabled()) {
                    log.info("(饿了么)订单回调，向ERP发送(饿了么)配送员接单，unOrder：{}", JacksonUtils.writeValueAsString(unOrder));
                }
                break;
            case 配送员配送中:
                unOrder = eleUnOrderParse.fromOrderShipping(oMessage);
                if (log.isInfoEnabled()) {
                    log.info("(饿了么)订单回调，向ERP发送(饿了么)配送中，unOrder：{}", JacksonUtils.writeValueAsString(unOrder));
                }
                break;
            case 配送成功:
                unOrder = eleUnOrderParse.fromOrderShipSuccessful(oMessage);
                if (log.isInfoEnabled()) {
                    log.info("(饿了么)订单回调，向ERP发送(饿了么)配送成功，unOrder：{}", JacksonUtils.writeValueAsString(unOrder));
                }
                break;
            case 订单完结:
                unOrder = eleUnOrderParse.fromOrderFinished(oMessage);
                if (log.isInfoEnabled()) {
                    log.info("(饿了么)订单回调，向ERP发送(饿了么)订单完成，unOrder：{}", JacksonUtils.writeValueAsString(unOrder));
                }
                break;
            case 订单被取消:
            case 订单置为无效:
            case 订单强制无效:
                unOrder = eleUnOrderParse.fromOrderCanceled(oMessage);
                if (log.isInfoEnabled()) {
                    log.info("(饿了么)订单回调，向ERP发送(饿了么)取消订单，unOrder：{}", JacksonUtils.writeValueAsString(unOrder));
                }
                break;
            case 用户催单:
                unOrder = eleUnOrderParse.fromOrderReminded(oMessage);
                if (log.isInfoEnabled()) {
                    log.info("(饿了么)订单回调，向ERP发送(饿了么)用户催单，unOrder：{}", JacksonUtils.writeValueAsString(unOrder));
                }
                break;
            case 用户申请取消单:
                unOrder = eleUnOrderParse.fromOrderCancelReq(oMessage);
                if (log.isInfoEnabled()) {
                    log.info("(饿了么)订单回调，向ERP发送(饿了么)用户申请取消单，unOrder：{}", JacksonUtils.writeValueAsString(unOrder));
                }
                break;
            case 用户取消取消单申请:
                unOrder = eleUnOrderParse.fromOrderCancelCancelReq(oMessage);
                if (log.isInfoEnabled()) {
                    log.info("(饿了么)订单回调，向ERP发送(饿了么)用户取消取消单申请，unOrder：{}", JacksonUtils.writeValueAsString(unOrder));
                }
                break;
            case 商户拒绝取消单:
                unOrder = eleUnOrderParse.fromOrderCancelDisagreed(oMessage);
                if (log.isInfoEnabled()) {
                    log.info("(饿了么)订单回调，向ERP发送(饿了么)商户拒绝取消单，unOrder：{}", JacksonUtils.writeValueAsString(unOrder));
                }
                break;
            case 客服仲裁取消单申请有效:
                unOrder = eleUnOrderParse.fromOrderCancelArbitrationEffective(oMessage);
                if (log.isInfoEnabled()) {
                    log.info("(饿了么)订单回调，向ERP发送(饿了么)客服仲裁取消单申请有效，unOrder：{}", JacksonUtils.writeValueAsString(unOrder));
                }
                break;
            case 商户同意取消单:
                unOrder = eleUnOrderParse.fromOrderCancelAgreed(oMessage);
                if (log.isInfoEnabled()) {
                    log.info("(饿了么)订单回调，向ERP发送(饿了么)商户同意取消单，unOrder：{}", JacksonUtils.writeValueAsString(unOrder));
                }
                break;
            case 用户申请退单:
                unOrder = eleUnOrderParse.fromOrderRefundReq(oMessage);
                if (log.isInfoEnabled()) {
                    log.info("(饿了么)订单回调，向ERP发送(饿了么)用户申请退单，unOrder：{}", JacksonUtils.writeValueAsString(unOrder));
                }
                break;
            case 用户取消退单:
                unOrder = eleUnOrderParse.fromOrderCancelRefundReq(oMessage);
                if (log.isInfoEnabled()) {
                    log.info("(饿了么)订单回调，向ERP发送(饿了么)用户取消退单，unOrder：{}", JacksonUtils.writeValueAsString(unOrder));
                }
                break;
            case 商户拒绝退单:
                unOrder = eleUnOrderParse.fromOrderRefundDisagreed(oMessage);
                if (log.isInfoEnabled()) {
                    log.info("(饿了么)订单回调，向ERP发送(饿了么)商户拒绝退单，unOrder：{}", JacksonUtils.writeValueAsString(unOrder));
                }
                break;
            case 客服仲裁退单有效:
                unOrder = eleUnOrderParse.fromOrderRefundArbitrationEffective(oMessage);
                if (log.isInfoEnabled()) {
                    log.info("(饿了么)订单回调，向ERP发送(饿了么)客服仲裁退单有效，unOrder：{}", JacksonUtils.writeValueAsString(unOrder));
                }
                break;
            case 商户同意退单:
                unOrder = eleUnOrderParse.fromOrderRefundAgreed(oMessage);
                if (log.isInfoEnabled()) {
                    log.info("(饿了么)订单回调，向ERP发送(饿了么)商户同意退单，unOrder：{}", JacksonUtils.writeValueAsString(unOrder));
                }
                break;
            default:
                break;
        }
        return unOrder;
    }

    private Wrapper<EleAuthDO> wrapperByUserId(long userId) {
        return new LambdaQueryWrapper<EleAuthDO>().eq(EleAuthDO::getUserId, userId);
    }
}
