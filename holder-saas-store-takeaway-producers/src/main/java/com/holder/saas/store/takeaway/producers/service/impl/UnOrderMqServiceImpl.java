package com.holder.saas.store.takeaway.producers.service.impl;

import com.holder.saas.store.takeaway.producers.service.UnOrderMqService;
import com.holderzone.framework.rocketmq.common.DefaultRocketMqProducer;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.takeaway.OrderType;
import com.holderzone.saas.store.dto.takeaway.UnOrder;
import com.holderzone.saas.store.dto.takeaway.UnOrderCbMsgType;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.MessageQueueSelector;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.common.message.MessageQueue;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import static com.holder.saas.store.takeaway.producers.config.RocketMqConfig.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @className UnOrderMqServiceImpl
 * @date 2018/09/18 18:08
 * @description
 * @program holder-saas-store-takeaway
 */
@Slf4j
@Service
public class UnOrderMqServiceImpl implements UnOrderMqService {

    private final DefaultRocketMqProducer defaultRocketMqProducer;

    private static final ScheduledExecutorService EXECUTOR_SERVICE = Executors.newScheduledThreadPool(10);


    @Autowired
    public UnOrderMqServiceImpl(DefaultRocketMqProducer defaultRocketMqProducer) {
        this.defaultRocketMqProducer = defaultRocketMqProducer;
    }

    @Override
    public void sendUnOrder(UnOrder unOrder) {
        if(UnOrderCbMsgType.ORDER_FINISHED != unOrder.getCbMsgType()){
            doSend(unOrder);
            return;
        }
        //若是已完成状态延迟3分钟发送
        EXECUTOR_SERVICE.schedule(() ->{
            doSend(unOrder);
        },3, TimeUnit.MINUTES);

    }

    private void doSend(UnOrder unOrder){
        //根据不同的业务来源区分消息队列
        Message message;
        switch (OrderType.TakeoutSubType.ofType(unOrder.getOrderSubType())){
            case ELE_TAKEOUT:
                message = new Message(TAKEAWAY_CONSUMERS_ELEM_ORDER_TOPIC,
                        TAKEAWAY_CONSUMERS_ELEM_ORDER_TAG, JacksonUtils.toJsonByte(unOrder));
                break;
            case MT_TAKEOUT:
                message = new Message(TAKEAWAY_CONSUMERS_MT_ORDER_TOPIC,
                        TAKEAWAY_CONSUMERS_MT_ORDER_TAG, JacksonUtils.toJsonByte(unOrder));
                break;
            case JD_TAKEOUT:
                message = new Message(TAKEAWAY_CONSUMERS_JD_ORDER_TOPIC,
                        TAKEAWAY_CONSUMERS_JD_ORDER_TAG, JacksonUtils.toJsonByte(unOrder));
                break;
            default:
                message = new Message(TAKEAWAY_CONSUMERS_ORDER_TOPIC,
                        TAKEAWAY_CONSUMERS_ORDER_TAG, JacksonUtils.toJsonByte(unOrder));
        }
        //根据订单id选择发送的队列 并在消费端设置为顺序消费
        defaultRocketMqProducer.sendMessage(message, (list, mg, o) -> {
            int orderIdHashCode = Math.abs(o.hashCode());
            int index = orderIdHashCode % list.size();
            return list.get(index);
        },unOrder.getOrderId());
    }
}
