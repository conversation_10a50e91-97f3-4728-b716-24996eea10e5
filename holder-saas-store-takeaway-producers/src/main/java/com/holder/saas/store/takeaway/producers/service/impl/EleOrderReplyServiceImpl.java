package com.holder.saas.store.takeaway.producers.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holder.saas.store.takeaway.producers.entity.domain.EleAuthDO;
import com.holder.saas.store.takeaway.producers.service.EleAuthService;
import com.holder.saas.store.takeaway.producers.service.UnOrderMqService;
import com.holder.saas.store.takeaway.producers.service.UnOrderReplyService;
import com.holder.saas.store.takeaway.producers.utils.DeliveryUtils;
import com.holder.saas.store.takeaway.producers.utils.ThrowableExtUtils;
import com.holder.saas.store.takeaway.producers.utils.TimeUtil;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.takeaway.OwnApiResult;
import com.holderzone.saas.store.dto.takeaway.UnOrder;
import com.holderzone.saas.store.dto.takeaway.UnOrderCbMsgType;
import com.holderzone.saas.store.dto.takeaway.UnRemind;
import eleme.openapi.sdk.api.entity.order.OLocationInfo;
import eleme.openapi.sdk.api.entity.order.OSelfDeliveryKnight;
import eleme.openapi.sdk.api.entity.order.OSelfDeliveryStateInfo;
import eleme.openapi.sdk.api.enumeration.order.OInvalidateType;
import eleme.openapi.sdk.api.enumeration.order.OSelfDeliveryStateEnum;
import eleme.openapi.sdk.api.enumeration.order.ReplyReminderType;
import eleme.openapi.sdk.api.exception.ServiceException;
import eleme.openapi.sdk.api.service.OrderService;
import eleme.openapi.sdk.config.Config;
import eleme.openapi.sdk.oauth.response.Token;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * 回复平台信息
 */
@Slf4j
@SuppressWarnings("Duplicates")
@Service("eleOrderReplyServiceImpl")
public class EleOrderReplyServiceImpl implements UnOrderReplyService {

    private final EleAuthService eleAuthService;

    private final UnOrderMqService unOrderMqService;

    private final Config config;

    private final String platformName = "饿了么";

    @Value("${small.ORDER_DELIVERY}")
    private String deliveryUrl;

    @Value("${small.ORDER_CANCEL_DELIVERY}")
    private String cancelDeliveryUrl;

    @Autowired
    public EleOrderReplyServiceImpl(EleAuthService eleAuthService, Config config,
                                    UnOrderMqService unOrderMqService) {
        this.eleAuthService = eleAuthService;
        this.config = config;
        this.unOrderMqService = unOrderMqService;
    }

    @Override
    public void replyCancelOrder(UnOrder unOrder) {
        String replyType = "商家取消订单";
        logReplyProcessing(unOrder, replyType);

        Token token = eleAuthService.getToken(unOrder.getStoreGuid());
        if (token != null) {
            OrderService orderService = new OrderService(config, token);
            try {
                orderService.cancelOrderLite(unOrder.getOrderId(), OInvalidateType.others, "");
                logReplySucceed(unOrder, replyType);
            } catch (ServiceException e) {
                logExceptionThenThrow(unOrder, e, replyType);
            }
        } else {
            logBizFailureThenThrow(unOrder, replyType);
        }
    }

    @Override
    public void replyConfirmOrder(UnOrder unOrder) {
        String replyType = "商家接单";
        logReplyProcessing(unOrder, replyType);

        Long userId = getUserIdByStoreGuid(unOrder.getStoreGuid());
        if (userId != null) {
            Token token = eleAuthService.getToken(userId);
            OrderService orderService = new OrderService(config, token);
            try {
                orderService.confirmOrderLite(unOrder.getOrderId());
                logReplySucceed(unOrder, replyType);
            } catch (ServiceException e) {
                logExceptionThenThrow(unOrder, e, replyType);
            }
        } else {
            logBizFailureThenThrow(unOrder, replyType);
        }
    }

    @Override
    public void replyAgreeCancelOrder(UnOrder unOrder) {
        String replyType = "同意取消订单消息";
        logReplyProcessing(unOrder, replyType);

        Long userId = getUserIdByStoreGuid(unOrder.getStoreGuid());
        if (userId != null) {
            Token token = eleAuthService.getToken(userId);
            OrderService orderService = new OrderService(config, token);
            try {
                orderService.agreeRefundLite(unOrder.getOrderId());
                logReplySucceed(unOrder, replyType);
            } catch (ServiceException e) {
                logExceptionThenThrow(unOrder, e, replyType);
            }
        } else {
            logBizFailureThenThrow(unOrder, replyType);
        }
    }

    @Override
    public void replyDisagreeCancelOrder(UnOrder unOrder) {
        String replyType = "不同意取消订单";
        logReplyProcessing(unOrder, replyType);

        Long userId = getUserIdByStoreGuid(unOrder.getStoreGuid());
        if (userId != null) {
            Token token = eleAuthService.getToken(userId);
            OrderService orderService = new OrderService(config, token);
            try {
                String disagreeReason = unOrder.getCancelReplyMessage() != null
                        ? unOrder.getCancelReplyMessage() : "不同意取消订单";
                orderService.disagreeRefundLite(unOrder.getOrderId(), disagreeReason);
                logReplySucceed(unOrder, replyType);
            } catch (ServiceException e) {
                logExceptionThenThrow(unOrder, e, replyType);
            }
        } else {
            logBizFailureThenThrow(unOrder, replyType);
        }
    }

    @Override
    public void replyAgreeRefundOrder(UnOrder unOrder) {
        String replyType = "同意退单";
        logReplyProcessing(unOrder, replyType);

        Long userId = getUserIdByStoreGuid(unOrder.getStoreGuid());
        if (userId != null) {
            Token token = eleAuthService.getToken(userId);
            OrderService orderService = new OrderService(config, token);
            try {
                orderService.agreeRefundLite(unOrder.getOrderId());
                logReplySucceed(unOrder, replyType);
            } catch (ServiceException e) {
                logExceptionThenThrow(unOrder, e, replyType);
            }
        } else {
            logBizFailureThenThrow(unOrder, replyType);
        }
    }

    @Override
    public void replyDisagreeRefundOrder(UnOrder unOrder) {
        String replyType = "不同意退单消息";
        logReplyProcessing(unOrder, replyType);

        Long userId = getUserIdByStoreGuid(unOrder.getStoreGuid());
        if (userId != null) {
            Token token = eleAuthService.getToken(userId);
            OrderService orderService = new OrderService(config, token);
            try {
                String disagreeReason = unOrder.getRefundReplyMessage() != null
                        ? unOrder.getRefundReplyMessage() : "不同意退单";
                orderService.disagreeRefundLite(unOrder.getOrderId(), disagreeReason);
                logReplySucceed(unOrder, replyType);
            } catch (ServiceException e) {
                logExceptionThenThrow(unOrder, e, replyType);
            }
        } else {
            logBizFailureThenThrow(unOrder, replyType);
        }
    }

    @Override
    public void replyUrgeOrder(UnOrder unOrder) {
        String replyType = "催单回复";
        logReplyProcessing(unOrder, replyType);

        Long userId = getUserIdByStoreGuid(unOrder.getStoreGuid());
        if (userId != null) {
            Token token = eleAuthService.getToken(userId);
            OrderService orderService = new OrderService(config, token);
            UnRemind unRemind = unOrder.getArrayOfUnRemind().get(0);
            String remindId = unRemind.getRemindId();
            String replyContent = unRemind.getReplyContent() != null ? unRemind.getReplyContent() : "";
            try {
                orderService.replyReminder(remindId, ReplyReminderType.custom, replyContent);
                logReplySucceed(unOrder, replyType);
            } catch (ServiceException e) {
                logExceptionThenThrow(unOrder, e, replyType);
            }
        } else {
            logBizFailureThenThrow(unOrder, replyType);
        }
    }

    @Override
    public void replyDeliveryAccept(UnOrder unOrder) {
        try {
            log.info("(饿了么)订单[{}]骑手接单,unOrder={}", unOrder.getOrderId(), JacksonUtils.writeValueAsString(unOrder));
            replyDeliveryState(unOrder, "骑手接单", OSelfDeliveryStateEnum.DELIVERY_KNIGHT_ACCEPT);
        } catch (Exception e) {
            e.printStackTrace();
            log.info("(饿了么)订单[{}]骑手接单异常,异常消息：{}", unOrder.getOrderId(), e.getMessage());
        }
    }

    @Override
    public void replyDeliveryStart(UnOrder unOrder) {
        try {
            log.info("(饿了么)订单[{}]商家已送出,unOrder={}", unOrder.getOrderId(), JacksonUtils.writeValueAsString(unOrder));
            replyDeliveryState(unOrder, "商家已送出", OSelfDeliveryStateEnum.DELIVERY_START);
            unOrder.setCbMsgType(UnOrderCbMsgType.ORDER_SHIPPING);
            unOrderMqService.sendUnOrder(unOrder);
        } catch (Exception e) {
            e.printStackTrace();
            log.info("(饿了么)订单[{}]商家已送出异常,异常消息:{}", unOrder.getOrderId(), e.getMessage());
        }

    }

    @Override
    public void replyDeliveryCancel(UnOrder unOrder) {
        try {
            log.info("(饿了么)订单[{}]商家已取消,unOrder={}", unOrder.getOrderId(), JacksonUtils.writeValueAsString(unOrder));
            replyDeliveryState(unOrder, "商家已取消", OSelfDeliveryStateEnum.DELIVERY_CANCEL);
            unOrder.setCbMsgType(UnOrderCbMsgType.ORDER_CANCEL_AGREED);
            unOrderMqService.sendUnOrder(unOrder);
        } catch (Exception e) {
            e.printStackTrace();
            log.info("(饿了么)订单[{}]商家配送已取消异常,异常消息:{}", unOrder.getOrderId(), e.getMessage());
        }
    }

    @Override
    public void replyDeliveryComplete(UnOrder unOrder) {
        try {
            log.info("(饿了么)订单[{}]商家已送达,unOrder={}", unOrder.getOrderId(), JacksonUtils.writeValueAsString(unOrder));
            replyDeliveryState(unOrder, "商家已送达", OSelfDeliveryStateEnum.DELIVERY_COMPLETE);
            unOrder.setCbMsgType(UnOrderCbMsgType.ORDER_SHIP_SUCCEED);
            unOrderMqService.sendUnOrder(unOrder);
        } catch (Exception e) {
            e.printStackTrace();
            log.info("(饿了么)订单[{}]商家配送已取消异常,异常消息:{}", unOrder.getOrderId(), e.getMessage());
        }
    }

    private void replyDeliveryState(UnOrder unOrder, String replyType, OSelfDeliveryStateEnum deliveryKnightAccept) {
        logReplyProcessing(unOrder, replyType);

        EleAuthDO eleAuthDO = getByStoreGuid(unOrder.getStoreGuid());
        if (eleAuthDO.getUserId() != null) {
            Token token = eleAuthService.getToken(eleAuthDO.getUserId());
            replyRiderUserInfo(unOrder, replyType, deliveryKnightAccept, eleAuthDO, token);
        } else {
            logBizFailureThenThrow(unOrder, replyType);
        }
    }

    private void replyRiderUserInfo(UnOrder unOrder, String replyType, OSelfDeliveryStateEnum deliveryKnightAccept,
                           EleAuthDO eleAuthDO, Token token) {
        OrderService orderService = new OrderService(config, token);
        OSelfDeliveryStateInfo oSelfDeliveryStateInfo = new OSelfDeliveryStateInfo();
        oSelfDeliveryStateInfo.setOrderId(unOrder.getOrderId());
        oSelfDeliveryStateInfo.setDistributorId(201L);
        OSelfDeliveryKnight oSelfDeliveryKnight = new OSelfDeliveryKnight();
        oSelfDeliveryKnight.setId(unOrder.getShipperId());
        oSelfDeliveryKnight.setPhone(unOrder.getShipperPhone());
        oSelfDeliveryKnight.setName(unOrder.getShipperName());
        oSelfDeliveryStateInfo.setKnight(oSelfDeliveryKnight);
        oSelfDeliveryStateInfo.setState(deliveryKnightAccept);
        log.info("饿了吗：推送骑手信息：{}",JacksonUtils.writeValueAsString(oSelfDeliveryStateInfo));
        try {
            //自配送同步运单信息（配送员信息）到饿了吗
            orderService.selfDeliveryStateSync(eleAuthDO.getShopId(), oSelfDeliveryStateInfo);
            logReplySucceed(unOrder, replyType);
        } catch (ServiceException e) {
            logExceptionThenThrow(unOrder, e, replyType);
        }
    }

    @Override
    public void replyRiderPosition(UnOrder unOrder) {
        final String replyType = "推送骑手定位";
        logReplyProcessing(unOrder, replyType);
        EleAuthDO eleAuthDO = getByStoreGuid(unOrder.getStoreGuid());
        if (eleAuthDO.getUserId() != null) {
            Token token = eleAuthService.getToken(eleAuthDO.getUserId());
            if(!StringUtils.isEmpty(unOrder.getShipperName()) && !StringUtils.isEmpty(unOrder.getShipperPhone())) {
                //推送骑手用户名和手机号
                replyRiderUserInfo(unOrder, "骑手信息", OSelfDeliveryStateEnum.DELIVERY_KNIGHT_ACCEPT, eleAuthDO, token);
            }
            if(!StringUtils.isEmpty(unOrder.getShipLatitude()) && !StringUtils.isEmpty(unOrder.getShipLongitude())){
                //推送骑手经纬度
                replyRiderLocation(unOrder, replyType, eleAuthDO, token);
            }
        } else {
            logBizFailureThenThrow(unOrder, replyType);
        }
    }

    private void replyRiderLocation(UnOrder unOrder, String replyType, EleAuthDO eleAuthDO, Token token) {
        OrderService orderService = new OrderService(config, token);
        OLocationInfo oLocationInfo = new OLocationInfo();
        oLocationInfo.setLatitude(Double.parseDouble(unOrder.getShipLatitude()));
        oLocationInfo.setLongitude(Double.parseDouble(unOrder.getShipLongitude()));
        //高度
        oLocationInfo.setAltitude((double) 0);
        oLocationInfo.setUtc(TimeUtil.getLongTimestamp());
        log.info("饿了吗：推送骑手定位：{}",JacksonUtils.writeValueAsString(oLocationInfo));
        try {
            //自配送同步骑手定位
            orderService.selfDeliveryLocationSync(eleAuthDO.getShopId(), unOrder.getOrderId(),oLocationInfo);
            logReplySucceed(unOrder, replyType);
        } catch (ServiceException e) {
            logExceptionThenThrow(unOrder, e, replyType);
        }
    }

    private EleAuthDO getByStoreGuid(String storeGuid) {
        return eleAuthService.getOne(new LambdaQueryWrapper<EleAuthDO>().eq(EleAuthDO::getStoreGuid, storeGuid));
    }

    private Long getUserIdByStoreGuid(String storeGuid) {
        return eleAuthService.getOne(new LambdaQueryWrapper<EleAuthDO>().eq(EleAuthDO::getStoreGuid, storeGuid)).getUserId();
    }

    private String getTokenByStoreGuid(String storeGuid) {
        return eleAuthService.getOne(new LambdaQueryWrapper<EleAuthDO>().eq(EleAuthDO::getStoreGuid, storeGuid)).getAccessToken();
    }

    private void logReplyProcessing(UnOrder unOrder, String msg) {
        log.info("Reply(饿了么){}，orderId: {}，处理中", msg, unOrder.getOrderId());
    }

    private void logReplySucceed(UnOrder unOrder, String msg) {
        log.info("Reply(饿了么){}，orderId: {}，处理成功", msg, unOrder.getOrderId());
    }

    private void logExceptionThenThrow(UnOrder unOrder, ServiceException e, String msg) {
        if (log.isErrorEnabled()) {
            log.error("Reply(饿了么){}，orderId: {}，处理失败：{}",
                    msg, unOrder.getOrderId(), ThrowableExtUtils.asStringIfAbsent(e));
        }
        eleAuthService.correctAuthThenThrow(e, unOrder);
        throw new BusinessException(ThrowableExtUtils.asStringIfAbsent(e));
    }

    private void logBizFailureThenThrow(UnOrder unOrder, String msg) {
        log.error("Reply(饿了么){}，orderId: {}，处理失败，通过storeGuid[{}]未查询到userId",
                msg, unOrder.getOrderId(), unOrder.getStoreGuid());
        throw new BusinessException("操作失败，您的饿了么外卖已与门店解绑，请重新登录商户后台绑定");
    }

    @Override
    public void startDelivery(UnOrder unOrder) {
        DeliveryUtils.startDelivery(unOrder,platformName,deliveryUrl);
    }


    @Override
    public OwnApiResult startDeliveryMQ(UnOrder unOrder) {
       return  DeliveryUtils.startDelivery(unOrder,platformName,deliveryUrl);
    }

    @Override
    public void cancelDelivery(UnOrder unOrder) {
        DeliveryUtils.cancelDelivery(unOrder,platformName,cancelDeliveryUrl);
    }
}
