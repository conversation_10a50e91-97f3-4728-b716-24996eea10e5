package com.holder.saas.store.takeaway.producers.utils.sdk.jd;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.holder.saas.store.takeaway.producers.utils.sdk.jd.dto.CommonRspDTO;
import com.holder.saas.store.takeaway.producers.utils.sdk.jd.dto.OrderAfsApplyInfoResponse;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @description 查询订单申请售后详情接口
 */
@Slf4j
public class OrderAfsApplyInfoRequest extends AbstractJdRequest{


    public OrderAfsApplyInfoRequest() {
    }

    public OrderAfsApplyInfoRequest(String appKey, String appSecret, String token) {
       super.appKey = appKey;
       super.appSecret = appSecret;
       super.token = token;
    }

    public OrderAfsApplyInfoResponse execute(String orderId){
        try {
            String response = super.execute("{\"orderId\":\"" + orderId + "\"}", "/djapi/afsInner/getOrderAfsApplyInfo");

            CommonRspDTO<OrderAfsApplyInfoResponse> afsApplyInfoRsp = JSON.parseObject(response,new TypeReference<CommonRspDTO<OrderAfsApplyInfoResponse>>(){});
            if(!afsApplyInfoRsp.isSuccess()){
                return null;
            }
            return afsApplyInfoRsp.getResult();
        }catch (Exception e){
            log.error("查询订单申请售后详情接口失败",e);
        }
        return null;
    }
}
