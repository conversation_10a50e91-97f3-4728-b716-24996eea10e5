package com.holder.saas.store.takeaway.producers.controller;

import com.holder.saas.store.takeaway.producers.entity.domain.HolderAuthDO;
import com.holder.saas.store.takeaway.producers.mapstruct.HolderMapstruct;
import com.holder.saas.store.takeaway.producers.service.HolderAuthService;
import com.holder.saas.store.takeaway.producers.service.OwnCallbackService;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.takeaway.OwnCallbackResponse;
import com.holderzone.saas.store.dto.takeaway.TakeoutOrderDTO;
import com.holderzone.saas.store.dto.takeaway.request.SalesOrderDTO;
import com.holderzone.saas.store.dto.takeaway.response.HolderAuthDTO;
import com.holderzone.saas.store.dto.takeaway.response.OwnDistributionDTO;
import com.holderzone.saas.store.dto.takeaway.response.StoreAuthDTO;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;

/**
 * 自营外卖平台
 * 绑定回调（保存token）
 * 订单回调（订单处理包括解绑）
 * 查询授权列表
 */
@Slf4j
@RestController
@RequestMapping("/own")
public class OwnController {


    private final OwnCallbackService ownCallbackService;

    private final HolderAuthService holderAuthService;

    private final HolderMapstruct holderMapstruct;

    @Autowired
    public OwnController(OwnCallbackService ownCallbackService, HolderAuthService holderAuthService,
                         HolderMapstruct holderMapstruct) {
        this.ownCallbackService = ownCallbackService;
        this.holderAuthService = holderAuthService;
        this.holderMapstruct = holderMapstruct;
    }


    /**
     * 解除授权或向mq发送unorder消息
     *
     * @param
     * @return
     */
    @PostMapping(value = "/callback/order")
    @ApiOperation(value = "自营外卖平台订单接收", notes = "自营外卖平台订单接收")
    public OwnCallbackResponse orderCallback(@RequestBody SalesOrderDTO salesOrderDTO) {
        if (log.isInfoEnabled()) {
            log.info("(自营外卖平台)订单接收：{}", JacksonUtils.writeValueAsString(salesOrderDTO));
        }

        try {
            //解除授权或向mq发送unorder消息
            ownCallbackService.orderCallback(salesOrderDTO);
        } catch (Exception e) {
            if (log.isErrorEnabled()) {
                log.error("(自营外卖平台)订单接收异常：{}", ThrowableUtils.asString(e));
            }
            return OwnCallbackResponse.DO_ERROR;
        }
        return OwnCallbackResponse.SUCCESS;
    }

    /**
     * 自营外卖平台获取配送方式
     *
     * @param
     * @return
     */
    @PostMapping(value = "/get_distribution")
    @ApiOperation(value = "自营外卖平台获取配送方式", notes = "自营外卖获取配送方式")
    public List<OwnDistributionDTO> getDistribution(@RequestBody BaseDTO baseDTO) {
        if (log.isInfoEnabled()) {
            log.info("(自营外卖平台)获取配送方式入参：{}", baseDTO);
        }
        List<OwnDistributionDTO> distributionLists = holderAuthService.getDistribution(baseDTO);
        log.info("(自营外卖平台)获取配送方式返回：{}", distributionLists);
        return distributionLists;
    }


    @PostMapping("/list_auth")
    @ApiOperation(value = "根据erp门店id查询自营外卖授权表，是否已经授权")
    public List<StoreAuthDTO> listAuth(
            @RequestBody List<StoreAuthDTO> storeAuthorizationDTOList) {
        return Collections.emptyList();
    }

    @PostMapping("/get_takeout_auth")
    @ApiOperation(value = "根据erp门店id查询自营外卖授权表，是否已经授权")
    public StoreAuthDTO getTakeoutAuth(@RequestBody StoreAuthDTO storeAuthDTO) {
        return holderAuthService.getTakeoutAuth(storeAuthDTO);
    }

    @PostMapping("/get_holder_auth")
    @ApiOperation(value = "根据erp门店id查询自营外卖授权表，是否已经授权")
    public HolderAuthDTO getHolder(@RequestBody String storeGuid) {
        HolderAuthDO holderAuthDO = holderAuthService.getHolderAuth(storeGuid);
        HolderAuthDTO holderAuthDTO = holderMapstruct.toHolderAuthDTO(holderAuthDO);
        return holderAuthDTO;
    }

    @PostMapping("/get_token")
    @ApiOperation(value = "根据erp门店id查询自营外卖授权表，是否已经授权")
    public String getToken(@RequestBody String storeGuid) {
        HolderAuthDO holderAuthDO = holderAuthService.getHolderAuth(storeGuid);
        String token = holderAuthService.getToken(holderAuthDO);
        return token;
    }

    @PostMapping("/go_shipping")
    @ApiOperation(value = "发起配送")
    public OwnCallbackResponse goShipping(@RequestBody TakeoutOrderDTO takeoutOrderDTO) {
        if (log.isInfoEnabled()) {
            log.info("(自营外卖平台)商家发起配送：{}", JacksonUtils.writeValueAsString(takeoutOrderDTO));
        }
        return holderAuthService.goShipping(takeoutOrderDTO);
    }

    @PostMapping("/done_shipping")
    @ApiOperation(value = "完成配送")
    public OwnCallbackResponse doneShipping(@RequestBody TakeoutOrderDTO takeoutOrderDTO) {
        if (log.isInfoEnabled()) {
            log.info("(自营外卖平台)商家完成配送：{}", JacksonUtils.writeValueAsString(takeoutOrderDTO));
        }
        return holderAuthService.doneShipping(takeoutOrderDTO);
    }

    @PostMapping("/cancel_shipping")
    @ApiOperation(value = "取消配送")
    public OwnCallbackResponse cancelShipping(@RequestBody TakeoutOrderDTO takeoutOrderDTO) {
        if (log.isInfoEnabled()) {
            log.info("(自营外卖平台)商家取消配送：{}", JacksonUtils.writeValueAsString(takeoutOrderDTO));
        }
        return holderAuthService.cancelShipping(takeoutOrderDTO);
    }

}
