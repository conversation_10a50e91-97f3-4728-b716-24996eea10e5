package com.holder.saas.store.takeaway.producers.controller;

import com.google.common.collect.Lists;
import com.holder.saas.store.takeaway.producers.config.EleCallbackValidator;
import com.holder.saas.store.takeaway.producers.mapstruct.EleOrderMapstruct;
import com.holder.saas.store.takeaway.producers.service.EleAuthService;
import com.holder.saas.store.takeaway.producers.service.EleCallbackService;
import com.holder.saas.store.takeaway.producers.service.EleOrderService;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.takeaway.*;
import com.holderzone.saas.store.dto.takeaway.response.StoreAuthDTO;
import eleme.openapi.sdk.api.entity.order.OGoodsItem;
import eleme.openapi.sdk.api.entity.order.OOrder;
import eleme.openapi.sdk.api.entity.other.OMessage;
import eleme.openapi.sdk.api.entity.packs.ShopContract;
import io.swagger.annotations.ApiOperation;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 饿了么
 * 绑定回调（保存token）
 * 订单回调（订单处理包括解绑）
 * 查询授权列表
 */
@Slf4j
@RestController
@RequestMapping("/ele")
public class EleController {

    private final EleCallbackValidator eleCallbackValidator;

    private final EleCallbackService eleCallbackService;

    private final EleAuthService eleAuthService;

    private final EleOrderService eleOrderService;

    private final EleOrderMapstruct eleOrderMapstruct;

    @Autowired
    public EleController(EleCallbackValidator eleCallbackValidator, EleCallbackService eleCallbackService,
                         EleAuthService eleAuthService, EleOrderService eleOrderService, EleOrderMapstruct eleOrderMapstruct) {
        this.eleCallbackValidator = eleCallbackValidator;
        this.eleCallbackService = eleCallbackService;
        this.eleAuthService = eleAuthService;
        this.eleOrderService = eleOrderService;
        this.eleOrderMapstruct = eleOrderMapstruct;
    }

    /**
     * 商户授权后回调该地址保存token
     *
     * @param eleCallbackBindDTO
     * @return
     */
    @GetMapping(value = "/callback/bind")
    @ApiOperation(value = "饿了么绑定回调", notes = "饿了么绑定回调")
    public EleCallbackResponse bindCallback(@RequestBody @Validated EleCallbackBindDTO eleCallbackBindDTO) {
        if (log.isInfoEnabled()) {
            log.info("(饿了么)绑定回调，入参：{}", JacksonUtils.writeValueAsString(eleCallbackBindDTO));
        }
        try {
            eleAuthService.bindCallback(eleCallbackBindDTO);
        } catch (Exception e) {
            if (log.isErrorEnabled()) {
                log.error("(饿了么)绑定回调，处理异常：{}", ThrowableUtils.asString(e));
            }
            return EleCallbackResponse.UNKNOWN_ERROR;
        }

        return EleCallbackResponse.SUCCESS;
    }

    /**
     * 解除授权或向mq发送unorder消息
     *
     * @param omessage
     * @return
     */
    @PostMapping(value = "/callback/order")
    @ApiOperation(value = "饿了么订单回调", notes = "饿了么订单回调")
    public EleCallbackResponse orderCallback(@RequestBody OMessage omessage) {
        if (log.isInfoEnabled()) {
            log.info("(饿了么)订单回调：{}", JacksonUtils.writeValueAsString(omessage));
        }

        //检查参数
        Pair<Boolean, String> pair = eleCallbackValidator.checkParameter(omessage);
        if (!pair.getKey()) {
            log.info("(饿了么)订单回调，参数错误：{}", pair.getValue());
            return EleCallbackResponse.PARAMETER_ERROR;
        }

        //检查签名
        if (!eleCallbackValidator.checkSignature(omessage)) {
            log.info("(饿了么)订单回调，签名不匹配");
            return EleCallbackResponse.SIGNATURE_ERROR;
        }

        try {
            //解除授权或向mq发送unorder消息
            eleCallbackService.orderCallback(omessage);
        } catch (Exception e) {
            if (log.isErrorEnabled()) {
                log.error("(饿了么)订单回调，处理异常：{}", ThrowableUtils.asString(e));
            }
            return EleCallbackResponse.UNKNOWN_ERROR;
        }

        return EleCallbackResponse.SUCCESS;
    }

    @PostMapping("/list_auth")
    @ApiOperation(value = "根据erp门店id查询饿了么外卖授权表，是否已经授权")
    public List<StoreAuthDTO> listAuth(
            @RequestBody List<StoreAuthDTO> storeAuthorizationDTOList) {
        return eleAuthService.listAuth(storeAuthorizationDTOList);
    }

    @PostMapping("/get_takeout_auth")
    @ApiOperation(value = "根据erp门店id查询饿了么外卖授权表，是否已经授权")
    public StoreAuthDTO getTakeoutAuth(@RequestBody StoreAuthDTO storeAuthDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询饿了么外卖授权列表入参：{}", JacksonUtils.writeValueAsString(storeAuthDTO));
        }
        return eleAuthService.getTakeoutAuth(storeAuthDTO);
    }

    @PostMapping("/update_delivery")
    @ApiOperation(value = "修改门店配送方式")
    public Boolean updateDelivery(@RequestBody StoreAuthDTO storeAuthDTO) {
        return eleAuthService.updateDelivery(storeAuthDTO);
    }

    @PostMapping("/get_effect_service_packContract")
    @ApiOperation(value = "查询门店配送合同生效方式")
    public String getEffectServicePackContract(@RequestBody UnOrder unOrder) {
        log.info("查询门店配送合同生效方式入参：{}" + JacksonUtils.writeValueAsString(unOrder));
        ShopContract shopContract = eleAuthService.getEffectServicePackContract(unOrder);
        String contractTypeName = shopContract.getContractTypeName();
        log.info("查询门店配送合同生效方式返回：{}" + contractTypeName);
        if (StringUtils.isEmpty(contractTypeName)) {
            return "-1";
        } else {
            return contractTypeName;
        }
    }

    @GetMapping("/get_order_item_price/{storeGuid}/{orderId}")
    @ApiOperation(value = "查询饿了么订单信息")
    public List<EleOrderItemPriceDTO> getOrderItemPrice(@PathVariable("storeGuid") String storeGuid, @PathVariable("orderId") String orderId) {
        List<EleOrderItemPriceDTO> itemPriceList = Lists.newArrayList();
        log.info("查询饿了么订单信息：storeGuid：{}，orderId：{}", storeGuid, orderId);
        OOrder order = eleOrderService.getOrder(storeGuid, orderId);
        log.info("查询饿了么订单信息结果：{}", JacksonUtils.writeValueAsString(order));
        if (Objects.nonNull(order)) {
            List<OGoodsItem> items = order.getGroups().stream().flatMap(e -> e.getItems().stream()).collect(Collectors.toList());
            itemPriceList = eleOrderMapstruct.toEleOrderItemPriceDTOS(items);
        }
        return itemPriceList;
    }

    @GetMapping("/get_order/{storeGuid}/{orderId}")
    @ApiOperation(value = "查询饿了么订单信息")
    public OleOrder getOrderItemOOrder(@PathVariable("storeGuid") String storeGuid, @PathVariable("orderId") String orderId) {
        log.info("查询饿了么订单信息：storeGuid：{}，orderId：{}", storeGuid, orderId);
        OOrder order = eleOrderService.getOrder(storeGuid, orderId);
        log.info("查询饿了么订单信息结果：{}", JacksonUtils.writeValueAsString(order));
        OleOrder older = new OleOrder();
        BeanUtils.copyProperties(order, older);
        return older;
    }


}
