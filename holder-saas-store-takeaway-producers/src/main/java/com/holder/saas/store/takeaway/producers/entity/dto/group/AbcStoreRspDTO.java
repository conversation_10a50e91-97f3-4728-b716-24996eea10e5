package com.holder.saas.store.takeaway.producers.entity.dto.group;

import com.holder.saas.store.takeaway.producers.entity.domain.GroupStoreBindDO;
import com.holderzone.saas.store.dto.business.group.StoreBindDTO;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023-12-06
 * @description 农行聚卡慧门店返回
 */
@Data
public class AbcStoreRspDTO {

    /**
     * 店铺id
     */
    private Long storeId;

    /**
     * 名称
     */
    private String name;

    /**
     * 状态
     */
    private Boolean enabled;

}
