package com.holder.saas.store.takeaway.producers.config;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "mt")
@Data
public class MeiTuanConfig {

    @JSONField(name = "DEVELOPER_ID")
    public Integer developerId;

    @JSONField(name = "SIGN_KEY")
    public String signKey;

}
