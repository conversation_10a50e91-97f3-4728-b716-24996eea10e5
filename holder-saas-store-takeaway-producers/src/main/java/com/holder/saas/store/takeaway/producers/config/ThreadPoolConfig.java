package com.holder.saas.store.takeaway.producers.config;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.*;

@Configuration
public class ThreadPoolConfig {

    @Bean
    public ExecutorService executorService() {
        return Executors.newFixedThreadPool(2);
    }

    @Bean(value = "mtProductQueryThreadPool")
    public ExecutorService mtProductQueryThreadPool() {
        return new ThreadPoolExecutor(5, 10, 5L, TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(50), new ThreadFactoryBuilder().setNameFormat("takeout-mt-query-product-pool-%d").build());
    }

    @Bean(value = "eleProductQueryThreadPool")
    public ExecutorService eleProductQueryThreadPool() {
        return new ThreadPoolExecutor(5, 10, 5L, TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(50), new ThreadFactoryBuilder().setNameFormat("takeout-ele-query-product-pool-%d").build());
    }

    @Bean(value = "tcdProductQueryThreadPool")
    public ExecutorService tcdProductQueryThreadPool() {
        return new ThreadPoolExecutor(5, 10, 5L, TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(50), new ThreadFactoryBuilder().setNameFormat("takeout-tcd-query-product-pool-%d").build());
    }

    @Bean(value = "batchBindProductThreadPool")
    public ExecutorService batchBindProductThreadPool() {
        return new ThreadPoolExecutor(5, 10, 5L, TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(50), new ThreadFactoryBuilder().setNameFormat("takeout-batch-bind-product-pool-%d").build());
    }

}
