package com.holder.saas.store.takeaway.producers.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.holder.saas.store.takeaway.producers.constant.TakeoutConstant;
import com.holder.saas.store.takeaway.producers.entity.EleInvoiceTypeMapping;
import com.holder.saas.store.takeaway.producers.entity.dto.*;
import com.holder.saas.store.takeaway.producers.entity.enums.EleRoleEnum;
import com.holder.saas.store.takeaway.producers.service.EleAuthService;
import com.holder.saas.store.takeaway.producers.service.EleUnOrderParser;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.Assert;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.takeaway.*;
import eleme.openapi.sdk.api.entity.order.*;
import eleme.openapi.sdk.api.entity.other.OMessage;
import eleme.openapi.sdk.api.enumeration.order.InvoiceType;
import eleme.openapi.sdk.api.enumeration.order.OOrderStatus;
import eleme.openapi.sdk.api.enumeration.order.ORefundType;
import eleme.openapi.sdk.api.service.OrderService;
import eleme.openapi.sdk.config.Config;
import eleme.openapi.sdk.oauth.response.Token;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 饿了吗订单解析(各种订单状态的处理)
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EleUnOrderParseImpl implements EleUnOrderParser {

    private final EleAuthService eleAuthService;

    private final Config config;

    private final StringRedisTemplate redisTemplate;

    @Override
    public UnOrder fromOOrder(OOrder oOrder) {
        // 查询订单
        try {
            Token token = eleAuthService.getTokenByShopId(oOrder.getShopId());
            OrderService orderService = new OrderService(config, token);
            oOrder = orderService.getOrder(oOrder.getId());
        } catch (Exception e) {
            log.error("推单异常，查询订单异常, orderId:{}", oOrder.getId());
            throw new BusinessException("推单异常，查询订单异常");
        }
        UnOrder unOrder = new UnOrder();

        // 标识、其他
        unOrder.setShopId(oOrder.getShopId());
        unOrder.setShopName(oOrder.getShopName());
        unOrder.setOrderId(oOrder.getId());
        unOrder.setOrderViewId(oOrder.getId());
        unOrder.setOrderDaySn(Integer.toString(oOrder.getDaySn()));
        unOrder.setOrderRemark(oOrder.getDescription());
        unOrder.setOnlinePay(oOrder.getOnlinePaid());
        unOrder.setFirstOrder(false);


        // 时间
        Optional.ofNullable(oOrder.getCreatedAt()).ifPresent(date -> unOrder.setCreateTime(DateTimeUtils.mills2LocalDateTime(date.getTime())));
        Optional.ofNullable(oOrder.getActiveAt()).ifPresent(date -> unOrder.setActiveTime(DateTimeUtils.mills2LocalDateTime(date.getTime())));

        // 顾客，默认人数为1
        unOrder.setCustomerName(oOrder.getConsignee());
        unOrder.setCustomerAddress(oOrder.getDeliveryPoiAddress());

        //顾客真实手机号
        if (!ObjectUtils.isEmpty(oOrder.getConsigneePhones())) {
            unOrder.setCustomerPhone(JacksonUtils.writeValueAsString(oOrder.getConsigneePhones()));
        }

        //顾客隐私号
        if (!ObjectUtils.isEmpty(oOrder.getPhoneList())) {
            List<String> phoneList = oOrder.getPhoneList();
            List<String> data = new ArrayList<>();
            for (String phone : phoneList) {
                phone = phone.replaceAll(",", "&");
                data.add(phone);
            }
            unOrder.setPrivacyPhone(JacksonUtils.writeValueAsString(data));
        }
        unOrder.setCustomerNumber(1);

        // 配送
        String deliveryGeo = oOrder.getDeliveryGeo();
        if (StringUtils.isNotBlank(deliveryGeo)) {
            String[] split = deliveryGeo.split(",");
            Assert.valid(2 == split.length, "饿了么deliveryGeo参数错误");
            log.info("shipLongitude={},shipLatitude={}", split[0], split[1]);
            unOrder.setShipLongitude(split[0]);
            unOrder.setShipLatitude(split[1]);
        }
        long deliverTime = oOrder.getDeliverTime() != null ? oOrder.getDeliverTime().getTime() : 0;
        unOrder.setEstimateDeliveredTime(DateTimeUtils.mills2LocalDateTime(deliverTime));

        //是否是预订单：美团外卖：立即送达delivery=0;    饿了么外卖：立即送达delivery=null
        log.info("(饿了么)订单[{}]book={},deliverTime={}", oOrder.getId(), oOrder.getBook(), oOrder.getDeliverTime());
        if (oOrder.getBook()) {
            if (oOrder.getDeliverTime() != null) {
                log.info("(饿了么)订单[{}]是预定单", oOrder.getId());
                unOrder.setReserve(Boolean.TRUE);
            } else {
                log.info("(饿了么)订单[{}]非预定单(deliverTime为null)", oOrder.getId());
                unOrder.setReserve(Boolean.FALSE);
            }
        } else {
            log.info("(饿了么)订单[{}]非预定单(book为false)", oOrder.getId());
            unOrder.setReserve(Boolean.FALSE);
        }


        // 发票
        unOrder.setInvoiced(oOrder.getInvoiced());
        unOrder.setInvoiceTitle(oOrder.getInvoice());
        unOrder.setTaxpayerId(oOrder.getTaxpayerId());
        InvoiceType invoiceType = oOrder.getInvoiceType();
        if (invoiceType != null) {
            unOrder.setInvoiceType(EleInvoiceTypeMapping.ofType(invoiceType.name()).getMapValue());
        }

        // 订单原始价格=餐盒费+配送费+菜品费用
        BigDecimal originalPrice = BigDecimal.valueOf(oOrder.getOriginalPrice());
        BigDecimal deliverFee = BigDecimal.valueOf(oOrder.getDeliverFee());
        BigDecimal packageFee = BigDecimal.valueOf(oOrder.getPackageFee());
        unOrder.setTotal(originalPrice.setScale(2, BigDecimal.ROUND_HALF_UP));
        unOrder.setPackageTotal(packageFee.setScale(2, BigDecimal.ROUND_HALF_UP));
        unOrder.setShipTotal(deliverFee.setScale(2, BigDecimal.ROUND_HALF_UP));
        unOrder.setItemTotal(originalPrice.subtract(deliverFee).subtract(packageFee).setScale(2, BigDecimal.ROUND_HALF_UP));

        // 折扣合计、商家承担的折扣部分、外卖平台承担的折扣部分
        BigDecimal shopPart = BigDecimal.valueOf(oOrder.getShopPart());
        BigDecimal elemePart = BigDecimal.valueOf(oOrder.getElemePart());
        BigDecimal activityAmountSum = BigDecimal.ZERO;
        for (OActivity oActivity : oOrder.getOrderActivities()) {
            activityAmountSum = activityAmountSum.add(BigDecimal.valueOf(oActivity.getAmount()));
        }
        unOrder.setDiscountTotal(activityAmountSum.setScale(2, BigDecimal.ROUND_UP).abs());
        unOrder.setEnterpriseDiscount(shopPart.setScale(2, BigDecimal.ROUND_HALF_UP).abs());
        unOrder.setPlatformDiscount(elemePart.setScale(2, BigDecimal.ROUND_HALF_UP).abs());

        // 服务费(平台抽成费用)、服务费率(平台抽成比例)
        BigDecimal serviceFee = BigDecimal.valueOf(oOrder.getServiceFee());
        unOrder.setServiceFee(serviceFee.setScale(2, BigDecimal.ROUND_HALF_UP).abs());
        unOrder.setServiceFeeRate(BigDecimal.valueOf(oOrder.getServiceRate()).setScale(2, BigDecimal.ROUND_HALF_UP));

        // 店铺实收
        BigDecimal income = BigDecimal.valueOf(oOrder.getIncome()).setScale(2, BigDecimal.ROUND_HALF_UP);
        unOrder.setShopTotal(income);

        // 第三方配送：如果 门店收入=菜品费用+餐盒费+配送费+商家承担费用+服务费，则认为第三方配送，否则为平台配送
        BigDecimal calculateIncome = originalPrice.add(shopPart).add(serviceFee)
                .setScale(2, BigDecimal.ROUND_HALF_UP);
        unOrder.setCustomerActualPay(BigDecimal.valueOf(oOrder.getTotalPrice())
                .setScale(2, BigDecimal.ROUND_HALF_UP).abs());
        unOrder.setThirdShipper(calculateIncome.equals(income));

        // 填充UnOrderDetail
        unOrder.setArrayOfUnItem(parseUnOrderItems(oOrder));

        // 填充UnOrderDiscount
        unOrder.setArrayOfUnDiscount(parseUnOrderDiscount(oOrder));

        // 回调消息类型
        unOrder.setCbMsgType(UnOrderCbMsgType.ORDER_ACTIVATED);

        // todo 订单生效时该OOrderStatus只可能为unprocessed，确认是这个后可以取消以下文字
        OOrderStatus oOrderStatus = oOrder.getStatus();
        log.debug("------------------> orderStatus: {}", oOrderStatus.name());

        return unOrder;
    }


    /**
     * 订单生效
     *
     * @param oMessage
     * @return
     */
    @Override
    public UnOrder fromOrderCreated(OMessage oMessage) {
        // JacksonUtils的时间格式与OMessage中不一致，所以使用Fastjson来处理
        JSONObject jsonObject = JSON.parseObject(oMessage.getMessage(), JSONObject.class);
        //防止时间解析报错先转换成jsonobject对象在转换成业务对象
        OOrder oOrder = jsonObject.toJavaObject(OOrder.class);
        log.info("(饿了么)新订单，orderId: {}，requestId: {}", oOrder.getId(), oMessage.getRequestId());
        //保存新订单id到redis补偿机制使用
        saveOrderIdWithCompensate(oOrder.getId());
        return fromOOrder(oOrder);
    }

    private void saveOrderIdWithCompensate(String id) {
        try {
            //将当前时间10分钟后的时间戳作为score加入到redis
            redisTemplate.opsForZSet().add(TakeoutConstant.ELE_ME_COMPENSATE, id, LocalDateTime.now().plusMinutes(10L).toEpochSecond(ZoneOffset.ofHours(8)));
        } catch (Exception e) {
            log.info("(饿了么)新订单保存到补偿队列失败，orderId: {}", id);
        }
    }

    /**
     * 订单已确认
     *
     * @param oMessage
     * @return
     */
    @Override
    public UnOrder fromOrderConfirmed(OMessage oMessage) {
        EleOrderStatusChange eleOrderStatusChange = JSON.parseObject(oMessage.getMessage(), EleOrderStatusChange.class);
        log.info("(饿了么)订单已确认，orderId: {}，requestId: {}", eleOrderStatusChange.getOrderId(), oMessage.getRequestId());
        UnOrder unOrder = new UnOrder();
        unOrder.setOrderId(eleOrderStatusChange.getOrderId());
        unOrder.setAcceptTime(DateTimeUtils.mills2LocalDateTime(eleOrderStatusChange.getUpdateTime() * 1000));
        unOrder.setCbMsgType(UnOrderCbMsgType.ORDER_CONFIRMED);
        return unOrder;
    }

    @Override
    public UnOrder fromOrderShippingDistribute(OMessage oMessage) {
        EleShipStatusChange eleShipStatusChange = JSON.parseObject(oMessage.getMessage(), EleShipStatusChange.class);
        log.info("(饿了么)订单配送中，orderId: {}，requestId: {}", eleShipStatusChange.getOrderId(), oMessage.getRequestId());
        UnOrder unOrder = new UnOrder();
        unOrder.setOrderId(eleShipStatusChange.getOrderId());
        unOrder.setShipperName(eleShipStatusChange.getName());
        unOrder.setShipperPhone(eleShipStatusChange.getPhone());
        unOrder.setCbMsgType(UnOrderCbMsgType.ORDER_SHIPPING_DISTRIBUTE);
        return unOrder;
    }

    /**
     * 订单配送中
     *
     * @param oMessage
     * @return
     */
    @Override
    public UnOrder fromOrderShipping(OMessage oMessage) {
        EleShipStatusChange eleShipStatusChange = JSON.parseObject(oMessage.getMessage(), EleShipStatusChange.class);
        log.info("(饿了么)订单配送中，orderId: {}，requestId: {}", eleShipStatusChange.getOrderId(), oMessage.getRequestId());
        UnOrder unOrder = new UnOrder();
        unOrder.setOrderId(eleShipStatusChange.getOrderId());
        unOrder.setShipperName(eleShipStatusChange.getName());
        unOrder.setShipperPhone(eleShipStatusChange.getPhone());
        unOrder.setDeliveryTime(DateTimeUtils.mills2LocalDateTime(eleShipStatusChange.getUpdateAt()));
        unOrder.setCbMsgType(UnOrderCbMsgType.ORDER_SHIPPING);
        return unOrder;
    }

    /**
     * 订单配送完成
     *
     * @param oMessage
     * @return
     */
    @Override
    public UnOrder fromOrderShipSuccessful(OMessage oMessage) {
        EleShipStatusChange eleShipStatusChange = JSON.parseObject(oMessage.getMessage(), EleShipStatusChange.class);
        log.info("(饿了么)订单配送完成，orderId: {}，requestId: {}", eleShipStatusChange.getOrderId(), oMessage.getRequestId());
        UnOrder unOrder = new UnOrder();
        unOrder.setOrderId(eleShipStatusChange.getOrderId());
        unOrder.setShipperName(eleShipStatusChange.getName());
        unOrder.setShipperPhone(eleShipStatusChange.getPhone());
        unOrder.setDeliveredTime(DateTimeUtils.mills2LocalDateTime(eleShipStatusChange.getUpdateAt()));
        unOrder.setCbMsgType(UnOrderCbMsgType.ORDER_SHIP_SUCCEED);
        return unOrder;
    }

    /**
     * 订单已完成
     *
     * @param oMessage
     * @return
     */
    @Override
    public UnOrder fromOrderFinished(OMessage oMessage) {
        EleOrderStatusChange eleOrderStatusChange = JSON.parseObject(oMessage.getMessage(), EleOrderStatusChange.class);
        log.info("(饿了么)订单已完成，orderId: {}，requestId: {}", eleOrderStatusChange.getOrderId(), oMessage.getRequestId());
        UnOrder unOrder = new UnOrder();
        unOrder.setOrderId(eleOrderStatusChange.getOrderId());
        unOrder.setCompleteTime(DateTimeUtils.mills2LocalDateTime(eleOrderStatusChange.getUpdateTime() * 1000));
        unOrder.setCbMsgType(UnOrderCbMsgType.ORDER_FINISHED);
        return unOrder;
    }

    /**
     * 订单已取消
     *
     * @param oMessage
     * @return
     */
    @Override
    public UnOrder fromOrderCanceled(OMessage oMessage) {
        EleOrderStatusChange eleOrderStatusChange = JSON.parseObject(oMessage.getMessage(), EleOrderStatusChange.class);
        log.info("(饿了么)订单已取消，orderId: {}，requestId: {}", eleOrderStatusChange.getOrderId(), oMessage.getRequestId());
        UnOrder unOrder = new UnOrder();
        unOrder.setOrderId(eleOrderStatusChange.getOrderId());
        unOrder.setCancelTime(DateTimeUtils.mills2LocalDateTime(eleOrderStatusChange.getUpdateTime() * 1000));

        switch (EleCbMsgTypeEnum.ofType(oMessage.getType())) {
            case 订单被取消:
                if (EleRoleEnum.饿了么商户.equals(EleRoleEnum.byRole((int) eleOrderStatusChange.getRole()))) {
                    unOrder.setCbMsgType(UnOrderCbMsgType.ORDER_CANCELED_BY_MCHNT_BEFORE_CONFIRMATION);
                    unOrder.setCancelRoleName(EleRoleEnum.byRole((int) eleOrderStatusChange.getRole()).getDesc());
                    unOrder.setCancelReason("饿了么商家版取消订单");
                } else {
                    EleRoleEnum eleRoleEnum = EleRoleEnum.byRole((int) eleOrderStatusChange.getRole());
                    unOrder.setCancelRoleName(eleRoleEnum.getDesc());
                    if (EleRoleEnum.饿了么系统.equals(eleRoleEnum)) {
                        unOrder.setCancelReason("下单超时未确认，系统自动取消");
                    } else if (EleRoleEnum.下单用户.equals(eleRoleEnum)) {
                        unOrder.setCancelReason("用户取消，商家确认前取消");
                    } else {
                        unOrder.setCancelReason("已取消，详情请查看饿了么商家版");
                    }
                    unOrder.setCbMsgType(UnOrderCbMsgType.ORDER_CANCELED);
                }
                break;
            case 订单强制无效:
                if (EleRoleEnum.下单用户.equals(EleRoleEnum.byRole((int) eleOrderStatusChange.getRole()))) {
                    unOrder.setCancelReason("用户1分钟内取消");
                    unOrder.setCancelRoleName(EleRoleEnum.下单用户.getDesc());
                } else {
                    unOrder.setCancelReason("商家主动取消已接受订单");
                    unOrder.setCancelRoleName("饿了么商家版");
                }
                unOrder.setCbMsgType(UnOrderCbMsgType.ORDER_CANCELED);
                break;
            case 订单置为无效:
                // 不处理，避免覆盖cancelReq或refundReq时copy进cancelReason,cancelStaffName的值
                unOrder.setCbMsgType(UnOrderCbMsgType.ORDER_CANCELED);
                break;
            default:
        }

        return unOrder;
    }

    /**
     * 订单有催单
     *
     * @param oMessage
     * @return
     */
    @Override
    public UnOrder fromOrderReminded(OMessage oMessage) {
        EleOrderUrge eleOrderUrge = JSON.parseObject(oMessage.getMessage(), EleOrderUrge.class);
        log.info("(饿了么)催单，orderId: {}，requestId: {}", eleOrderUrge.getOrderId(), oMessage.getRequestId());
        UnOrder unOrder = new UnOrder();
        unOrder.setOrderId(eleOrderUrge.getOrderId());
        unOrder.setCbMsgType(UnOrderCbMsgType.ORDER_URGED);
        UnRemind unRemind = new UnRemind().setOrderId(eleOrderUrge.getOrderId())
                .setRemindId(String.valueOf(eleOrderUrge.getRemindId()))
                .setRemindTime(DateTimeUtils.mills2LocalDateTime(eleOrderUrge.getUpdateTime()));
        unOrder.setArrayOfUnRemind(Collections.singletonList(unRemind));
        return unOrder;
    }

    /**
     * 客户取消订单
     *
     * @param oMessage
     * @return
     */
    @Override
    public UnOrder fromOrderCancelReq(OMessage oMessage) {
        EleOrderRefund eleOrderRefund = JSON.parseObject(oMessage.getMessage(), EleOrderRefund.class);
        log.info("(饿了么)取消订单请求，orderId: {}，requestId: {}", eleOrderRefund.getOrderId(), oMessage.getRequestId());
        UnOrder unOrder = new UnOrder();
        unOrder.setOrderId(eleOrderRefund.getOrderId());
        unOrder.setPart(ORefundType.part.equals(eleOrderRefund.getRefundType()));
        unOrder.setCancelReqTime(DateTimeUtils.mills2LocalDateTime(eleOrderRefund.getUpdateTime() * 1000));
        unOrder.setCancelReqReason(eleOrderRefund.getReason());
        unOrder.setCbMsgType(UnOrderCbMsgType.ORDER_CANCEL_REQ);
        return unOrder;
    }

    /**
     * 客户取消取消订单
     *
     * @param oMessage
     * @return
     */
    @Override
    public UnOrder fromOrderCancelCancelReq(OMessage oMessage) {
        EleOrderRefund eleOrderRefund = JSON.parseObject(oMessage.getMessage(), EleOrderRefund.class);
        log.info("(饿了么)取消取消订单请求，orderId: {}，requestId: {}", eleOrderRefund.getOrderId(), oMessage.getRequestId());
        UnOrder unOrder = new UnOrder();
        unOrder.setOrderId(eleOrderRefund.getOrderId());
        unOrder.setPart(ORefundType.part.equals(eleOrderRefund.getRefundType()));
        unOrder.setCbMsgType(UnOrderCbMsgType.ORDER_CANCEL_CANCEL_REQ);
        return unOrder;
    }

    /**
     * 商家同意取消订单
     *
     * @param oMessage
     * @return
     */
    @Override
    public UnOrder fromOrderCancelAgreed(OMessage oMessage) {
        EleOrderRefund eleOrderRefund = JSON.parseObject(oMessage.getMessage(), EleOrderRefund.class);
        log.info("(饿了么)商户同意取消订单，orderId: {}，requestId: {}", eleOrderRefund.getOrderId(), oMessage.getRequestId());
        UnOrder unOrder = new UnOrder();
        unOrder.setOrderId(eleOrderRefund.getOrderId());
        unOrder.setPart(ORefundType.part.equals(eleOrderRefund.getRefundType()));
        unOrder.setCancelTime(DateTimeUtils.mills2LocalDateTime(eleOrderRefund.getUpdateTime() * 1000));
        unOrder.setCbMsgType(UnOrderCbMsgType.ORDER_CANCEL_AGREED);
        return unOrder;
    }

    /**
     * 商家不同意取消订单
     *
     * @param oMessage
     * @return
     */
    @Override
    public UnOrder fromOrderCancelDisagreed(OMessage oMessage) {
        EleOrderRefund eleOrderRefund = JSON.parseObject(oMessage.getMessage(), EleOrderRefund.class);
        log.info("(饿了么)商户不同意取消订单，orderId: {}，requestId: {}", eleOrderRefund.getOrderId(), oMessage.getRequestId());
        UnOrder unOrder = new UnOrder();
        unOrder.setOrderId(eleOrderRefund.getOrderId());
        unOrder.setPart(ORefundType.part.equals(eleOrderRefund.getRefundType()));
        unOrder.setCbMsgType(UnOrderCbMsgType.ORDER_CANCEL_DISAGREED);
        return unOrder;
    }

    /**
     * 客服仲裁取消单申请有效
     *
     * @param oMessage
     * @return
     */
    @Override
    public UnOrder fromOrderCancelArbitrationEffective(OMessage oMessage) {
        EleOrderRefund eleOrderRefund = JSON.parseObject(oMessage.getMessage(), EleOrderRefund.class);
        log.info("(饿了么)客服仲裁取消单申请有效，orderId: {}，requestId: {}", eleOrderRefund.getOrderId(), oMessage.getRequestId());
        UnOrder unOrder = new UnOrder();
        unOrder.setOrderId(eleOrderRefund.getOrderId());
        unOrder.setPart(ORefundType.part.equals(eleOrderRefund.getRefundType()));
        unOrder.setCbMsgType(UnOrderCbMsgType.ORDER_CANCEL_ARBITRATION_EFFECTIVE);
        return unOrder;
    }

    /**
     * 客户退单
     *
     * @param oMessage
     * @return
     */
    @Override
    public UnOrder fromOrderRefundReq(OMessage oMessage) {
        EleOrderRefund eleOrderRefund = JSON.parseObject(oMessage.getMessage(), EleOrderRefund.class);
        log.info("(饿了么)用户申请退单，orderId: {}，requestId: {}", eleOrderRefund.getOrderId(), oMessage.getRequestId());
        UnOrder unOrder = new UnOrder();
        unOrder.setOrderId(eleOrderRefund.getOrderId());
        unOrder.setRefundReqTime(DateTimeUtils.mills2LocalDateTime(eleOrderRefund.getUpdateTime() * 1000));
        unOrder.setRefundReqReason(eleOrderRefund.getReason());
        unOrder.setPart(ORefundType.part.equals(eleOrderRefund.getRefundType()));
        unOrder.setCustomerRefund(BigDecimal.valueOf(eleOrderRefund.getTotalPrice()));
        unOrder.setCustomerRefundItem(eleOrderRefund.getGoodsList().stream()
                .map(item -> item.getName() + "*" + item.getQuantity())
                .collect(Collectors.joining("，")));
        unOrder.setArrayOfUnRefundItem(parseUnRefundItems(eleOrderRefund.getGoodsList()));
        unOrder.setCbMsgType(UnOrderCbMsgType.ORDER_REFUND_REQ);
        return unOrder;
    }


    /**
     * 客户取消退单
     *
     * @param oMessage
     * @return
     */
    @Override
    public UnOrder fromOrderCancelRefundReq(OMessage oMessage) {
        EleOrderRefund eleOrderRefund = JSON.parseObject(oMessage.getMessage(), EleOrderRefund.class);
        log.info("(饿了么)取消退单请求，orderId: {}，requestId: {}", eleOrderRefund.getOrderId(), oMessage.getRequestId());
        UnOrder unOrder = new UnOrder();
        unOrder.setOrderId(eleOrderRefund.getOrderId());
        unOrder.setPart(ORefundType.part.equals(eleOrderRefund.getRefundType()));
        unOrder.setCbMsgType(UnOrderCbMsgType.ORDER_CANCEL_REFUND_REQ);
        return unOrder;
    }

    /**
     * 商户同意退单
     *
     * @param oMessage
     * @return
     */
    @Override
    public UnOrder fromOrderRefundAgreed(OMessage oMessage) {
        EleOrderRefund eleOrderRefund = JSON.parseObject(oMessage.getMessage(), EleOrderRefund.class);
        log.info("(饿了么)商户同意退单，orderId: {}，requestId: {}", eleOrderRefund.getOrderId(), oMessage.getRequestId());
        UnOrder unOrder = new UnOrder();
        unOrder.setOrderId(eleOrderRefund.getOrderId());
        unOrder.setPart(ORefundType.part.equals(eleOrderRefund.getRefundType()));
        unOrder.setCancelTime(DateTimeUtils.mills2LocalDateTime(eleOrderRefund.getUpdateTime() * 1000));
        unOrder.setCbMsgType(UnOrderCbMsgType.ORDER_REFUND_AGREED);
        unOrder.setCustomerRefund(BigDecimal.valueOf(eleOrderRefund.getTotalPrice()));
        unOrder.setCustomerRefundItem(eleOrderRefund.getGoodsList().stream()
                .map(item -> item.getName() + "*" + item.getQuantity())
                .collect(Collectors.joining("，")));
        unOrder.setArrayOfUnRefundItem(parseUnRefundItems(eleOrderRefund.getGoodsList()));
        return unOrder;
    }

    /**
     * 商家不同意退单
     *
     * @param oMessage
     * @return
     */
    @Override
    public UnOrder fromOrderRefundDisagreed(OMessage oMessage) {
        EleOrderRefund eleOrderRefund = JSON.parseObject(oMessage.getMessage(), EleOrderRefund.class);
        log.info("(饿了么)商户不同意退单，orderId: {}，requestId: {}", eleOrderRefund.getOrderId(), oMessage.getRequestId());
        UnOrder unOrder = new UnOrder();
        unOrder.setOrderId(eleOrderRefund.getOrderId());
        unOrder.setPart(ORefundType.part.equals(eleOrderRefund.getRefundType()));
        unOrder.setCbMsgType(UnOrderCbMsgType.ORDER_REFUND_DISAGREED);
        return unOrder;
    }

    /**
     * 客服仲裁退单有效
     *
     * @param oMessage
     * @return
     */
    @Override
    public UnOrder fromOrderRefundArbitrationEffective(OMessage oMessage) {
        EleOrderRefund eleOrderRefund = JSON.parseObject(oMessage.getMessage(), EleOrderRefund.class);
        log.info("(饿了么)客服仲裁退单申请有效，orderId: {}，requestId: {}", eleOrderRefund.getOrderId(), oMessage.getRequestId());
        UnOrder unOrder = new UnOrder();
        unOrder.setOrderId(eleOrderRefund.getOrderId());
        unOrder.setPart(ORefundType.part.equals(eleOrderRefund.getRefundType()));
        unOrder.setCbMsgType(UnOrderCbMsgType.ORDER_REFUND_ARBITRATION_EFFECTIVE);
        unOrder.setCustomerRefund(BigDecimal.valueOf(eleOrderRefund.getTotalPrice()));
        unOrder.setCustomerRefundItem(eleOrderRefund.getGoodsList().stream()
                .map(item -> item.getName() + "*" + item.getQuantity())
                .collect(Collectors.joining("，")));
        unOrder.setArrayOfUnRefundItem(parseUnRefundItems(eleOrderRefund.getGoodsList()));
        return unOrder;
    }

    /**
     * 解析为unOrderItems
     *
     * @param oOrder
     * @return
     */
    private List<UnItem> parseUnOrderItems(OOrder oOrder) {
        List<UnItem> arrayOfUnOrderDetail = new ArrayList<>();
        int cartIndex = 0;
        for (OGoodsGroup ogg : oOrder.getGroups()) {
            boolean isExistItem = false;
            for (OGoodsItem oGoodsItem : ogg.getItems()) {
                UnItem unOrderDetail = new UnItem();
                // 只统计非餐盒费(除102)
                if (oGoodsItem != null && oGoodsItem.getCategoryId() != 102) {
                    // 至少存在一个菜品item为真
                    isExistItem = true;
                    // 多规格，NewSpecs列表理论上应该只有一个值
                    List<String> specValues = new ArrayList<>();
                    List<OGroupItemSpec> newSpecs = oGoodsItem.getNewSpecs();
                    if (newSpecs != null) {
                        for (OGroupItemSpec oGroupItemSpec : newSpecs) {
                            if (oGroupItemSpec != null) {
                                specValues.add(oGroupItemSpec.getValue());
                            }
                        }
                    }
                    if (specValues.size() > 0) {
                        unOrderDetail.setItemSpec(StringUtils.join(specValues, ","));
                    }
                    // 格式化菜品名字
                    // 饿了么单品无属性       特色烧鸡公[少冰+重辣]      null              null
                    // 饿了么单品有属性       特色烧鸡公[少冰+重辣]      特色烧鸡公        少冰,重辣
                    // 饿了么多规格无属性     红烧狮子头-规格1           规格1             nul
                    // 饿了么多规格有属性     红烧狮子头-规格2[无糖]     规格2             无糖
                    // 这里需要把规格append到菜品名称里
                    String oGoodsItemName = StringUtils.isNotEmpty(oGoodsItem.getOriginalName()) ? oGoodsItem.getOriginalName() : oGoodsItem.getName();
                    String oGoodsItemSpec = unOrderDetail.getItemSpec();
                    try {
                        if (oGoodsItemSpec == null) {
                            unOrderDetail.setItemName(oGoodsItemName);
                        } else {
                            int indexOfSpec = oGoodsItemName.lastIndexOf(oGoodsItemSpec);
                            if (indexOfSpec < 0) {
                                unOrderDetail.setItemName(oGoodsItemName);
                            } else if (indexOfSpec == 0) {
                                int nextIndexOfSpec = oGoodsItemName.indexOf(oGoodsItemSpec, indexOfSpec + 1);
                                if (nextIndexOfSpec < 0) {
                                    unOrderDetail.setItemName(oGoodsItemSpec);
                                } else {
                                    unOrderDetail.setItemName(oGoodsItemSpec + "(" + oGoodsItemSpec + ")");
                                }
                            } else {
                                unOrderDetail.setItemName(oGoodsItemName.substring(0, indexOfSpec - 1) + "(" + oGoodsItemSpec + ")");
                            }
                        }
                    } catch (Exception e) {
                        log.error("饿了么菜品名称解析出错，将使用原菜品名: {}", oGoodsItemName);
                        unOrderDetail.setItemName(oGoodsItemName);
                    }
                    unOrderDetail.setUnItemSkuId(String.valueOf(oGoodsItem.getId()));
                    unOrderDetail.setThirdSkuId(String.valueOf(oGoodsItem.getId()));
                    unOrderDetail.setItemSku(String.valueOf(oGoodsItem.getExtendCode()));
                    unOrderDetail.setItemCount(BigDecimal.valueOf(oGoodsItem.getQuantity()).setScale(2, BigDecimal.ROUND_HALF_UP));
                    unOrderDetail.setItemPrice(BigDecimal.valueOf(oGoodsItem.getPrice()).setScale(2, BigDecimal.ROUND_HALF_UP));
                    unOrderDetail.setItemTotal(BigDecimal.valueOf(oGoodsItem.getTotal()).setScale(2, BigDecimal.ROUND_HALF_UP));

                    // 多属性
                    String itemProperty = buildProperty(oGoodsItem.getAttributes());

                    if (itemProperty != null) {
                        unOrderDetail.setItemProperty(itemProperty);
                    }

                    // 口袋
                    unOrderDetail.setCartId(cartIndex);

                    // 分组类型 正常的菜品、赠品、配送费等
                    unOrderDetail.setSettleType(ogg.getType().name().equalsIgnoreCase("normal") ? 0 : 1);
                    if (oGoodsItem.getFoodGroup() == null) {
                        arrayOfUnOrderDetail.add(unOrderDetail);
                        continue;
                    }
                    // 套餐
                    List<OFoodItem> foodGroup = oGoodsItem.getFoodGroup().stream()
                            .flatMap(Collection::stream)
                            .collect(Collectors.toList());
                    if (!org.springframework.util.CollectionUtils.isEmpty(foodGroup)) {
                        List<UnItem> subItemList = new ArrayList<>();
                        foodGroup.forEach(food -> {
                            UnItem unItem = new UnItem();
                            unItem.setItemSku(food.getSkuId());
                            unItem.setItemName(food.getName());
                            unItem.setItemCount(BigDecimal.valueOf(food.getQuantity()));
                            unItem.setParentItemSku(String.valueOf(oGoodsItem.getExtendCode()));
                            //套餐子属性
                            String subItemProperty = buildProperty(food.getAttrs());
                            if (subItemProperty != null) {
                                unItem.setItemProperty(subItemProperty);
                            }
                            subItemList.add(unItem);
                        });
                        unOrderDetail.setSubItemList(subItemList);
                    }

                    arrayOfUnOrderDetail.add(unOrderDetail);
                }
            }
            // 该分组下至少存在一个菜品则统计为一个口袋
            if (isExistItem) cartIndex++;
        }
        return arrayOfUnOrderDetail;
    }


    private String buildProperty(List<OGroupItemAttribute> attributes) {
        List<String> attrValues = new ArrayList<>();
        if (CollUtil.isEmpty(attributes)) {
            return null;
        }

        for (OGroupItemAttribute oGroupItemAttribute : attributes) {
            if (oGroupItemAttribute != null) {
                attrValues.add(oGroupItemAttribute.getValue());
            }
        }
        if (CollUtil.isEmpty(attributes)) {
            return null;
        }
        return StringUtils.join(attrValues, ",");
    }

    /**
     * 解析为unOrderDiscount
     *
     * @param oOrder
     */
    private List<UnDiscount> parseUnOrderDiscount(OOrder oOrder) {
        List<UnDiscount> arrayOfUnDiscount = new ArrayList<>();

        for (OActivity oActivity : oOrder.getOrderActivities()) {
            if (oActivity != null) {
                UnDiscount unDiscount = new UnDiscount();
                unDiscount.setDiscountName(oActivity.getName());
                unDiscount.setTotalDiscount(BigDecimal.valueOf(oActivity.getAmount()));
                unDiscount.setEnterpriseDiscount(BigDecimal.valueOf(oOrder.getShopPart()));
                unDiscount.setPlatformDiscount(BigDecimal.valueOf(oOrder.getElemePart()));
                arrayOfUnDiscount.add(unDiscount);
            }
        }

        return arrayOfUnDiscount;
    }

    private void parseItemActualPrice(UnOrder unOrder, OMessage oMessage) {
        // 特殊处理活动商品,设置活动后购买价格
        try {
            JSONArray orderActivities = JSON.parseObject(oMessage.getMessage()).getJSONArray("orderActivities");
            if (CollectionUtils.isEmpty(orderActivities)) {
                return;
            }
            List<EleOrderActivity> eleOrderActivityList = JSONArray.parseArray(orderActivities.toJSONString(), EleOrderActivity.class);
            if (CollectionUtils.isNotEmpty(eleOrderActivityList)) {
                Map<Long, Double> eleOrderActivityMap = eleOrderActivityList.stream()
                        .filter(e -> Objects.nonNull(e.getFoodId()) && Objects.nonNull(e.getType()))
                        .filter(e -> "1".equals(e.getType()) || "4".equals(e.getType()))
                        .collect(Collectors.toMap(EleOrderActivity::getFoodId, EleOrderActivity::getAmount, (key1, key2) -> key1));
                unOrder.getArrayOfUnItem().forEach(e -> {
                    Double activityReducePrice = eleOrderActivityMap.get(Long.valueOf(e.getUnItemSkuId()));
                    if (Objects.nonNull(activityReducePrice)) {
                        e.setActualPrice(e.getItemTotal().add(BigDecimal.valueOf(activityReducePrice)));
                    }
                });
            }
        } catch (Exception e) {
            e.printStackTrace();
            // 不影响主流程
            log.error("饿了么商品折扣价解析错误:{}", e.getMessage());
        }
    }


    private List<UnRefundItem> parseUnRefundItems(List<Item> goodsList) {
        if (CollectionUtils.isEmpty(goodsList)) {
            return null;
        }
        return goodsList.stream()
                .map(item -> {
                    BigDecimal quantity = BigDecimal.valueOf(item.getQuantity());
                    BigDecimal price = BigDecimal.valueOf(item.getPrice());
                    BigDecimal total = price.multiply(quantity);
                    BigDecimal refundPrice = BigDecimal.valueOf(item.getPrice());

                    UnRefundItem refundItem = new UnRefundItem();

                    refundItem.setItemName(item.getName());
                    refundItem.setItemCount(quantity);
                    refundItem.setItemPrice(price);
                    refundItem.setItemTotal(total);
                    refundItem.setRefundPrice(refundPrice);
                    refundItem.setThirdSkuId(String.valueOf(item.getVfoodId()));
                    return refundItem;
                })
                .collect(Collectors.toList());
    }

}
