package com.holder.saas.store.takeaway.producers.config;

import eleme.openapi.sdk.config.Config;
import eleme.openapi.sdk.oauth.OAuthClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class EleConfig {

    @Value("${ele.IS_SAND_BOX}")
    private Boolean isSandBox;

    @Value("${ele.CLIENT_KEY}")
    private String clientKey;

    @Value("${ele.SECRET}")
    private String secret;

    /**
     * 配置类Config
     * @return
     */
    @Bean
    public Config config() {
        return new Config(isSandBox, clientKey, secret);
    }

    /**
     * 授权类OAuthClient
     * @param config
     * @return
     */
    @Bean
    public OAuthClient OAuthClient(eleme.openapi.sdk.config.Config config) {
        return new OAuthClient(config);
    }
}
