package com.holder.saas.store.takeaway.producers.utils.sdk.meituan;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.holder.saas.store.takeaway.producers.constant.TakeoutConstant;
import com.sankuai.sjst.platform.developer.domain.RequestDomain;
import com.sankuai.sjst.platform.developer.utils.SignUtils;
import com.sankuai.sjst.platform.developer.utils.WebUtils;
import eleme.openapi.sdk.api.exception.BusinessException;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.NameValuePair;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.message.BasicNameValuePair;
import java.io.IOException;
import java.net.URISyntaxException;
import java.sql.Timestamp;
import java.util.*;

/**
 * <AUTHOR>
 */
@Data
@Slf4j
public class CipCaterAuthorizationRequest{

    private String code;

    private String url;

    private String timestamp;

    private Integer businessId;

    private Long developerId;

    private String signKey;

    private static final String GRANT_TYPE = "authorization_code";

    public CipCaterAuthorizationRequest() {

    }

    public CipCaterAuthorizationRequest(Integer businessId,Long developerId,String code,String signKey) {
        this.url = RequestDomain.preUrl.getValue() + "/oauth/token";
        this.timestamp = (new Timestamp((new Date()).getTime())).toString();
        this.businessId = businessId;
        this.signKey = signKey;
        this.developerId = developerId;
        this.code = code;
    }

    public String doPost()  {
        List<NameValuePair> paramsInUrl = Lists.newArrayList();
        paramsInUrl.add(new BasicNameValuePair("businessId", String.valueOf(this.businessId)));
        paramsInUrl.add(new BasicNameValuePair("code", this.code));
        paramsInUrl.add(new BasicNameValuePair("charset", TakeoutConstant.CHARSET_UTF_8));
        paramsInUrl.add(new BasicNameValuePair("timestamp", this.timestamp));
        paramsInUrl.add(new BasicNameValuePair("developerId", String.valueOf(this.developerId)));
        paramsInUrl.add(new BasicNameValuePair("grantType", GRANT_TYPE));
        paramsInUrl.add(new BasicNameValuePair("sign", getSign()));
        try {
            return WebUtils.post((new URIBuilder()).setParameters(paramsInUrl).setPath(this.url).build().toString(), null);
        } catch (Exception e) {
            log.error("获取access_token失败",e);
        }
        return null;
    }

    public String getSign() {
        Map<String, String> params = Maps.newHashMap();
        params.put("businessId",String.valueOf(this.businessId));
        params.put("charset",TakeoutConstant.CHARSET_UTF_8);
        params.put("code",this.code);
        params.put("developerId",String.valueOf(this.developerId));
        params.put("grantType",GRANT_TYPE);
        params.put("timestamp",this.timestamp);
        return SignUtils.createSign(this.signKey, params);
    }

}
