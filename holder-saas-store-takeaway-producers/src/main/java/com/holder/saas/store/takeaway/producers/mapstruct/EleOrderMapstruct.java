package com.holder.saas.store.takeaway.producers.mapstruct;

import com.holderzone.saas.store.dto.takeaway.EleOrderItemPriceDTO;
import eleme.openapi.sdk.api.entity.order.OGoodsItem;
import org.mapstruct.Mapper;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Mapper(componentModel = "spring")
public interface EleOrderMapstruct {

    EleOrderItemPriceDTO toEleOrderItemPriceDTO(OGoodsItem item);

    List<EleOrderItemPriceDTO> toEleOrderItemPriceDTOS(List<OGoodsItem> items);
}
