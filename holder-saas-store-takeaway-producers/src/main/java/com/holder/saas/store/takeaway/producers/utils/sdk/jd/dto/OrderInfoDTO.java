package com.holder.saas.store.takeaway.producers.utils.sdk.jd.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class OrderInfoDTO {

    /**
     * 订单号
     */
    private Long orderId;

    /**
     * 订单来源类型(0:原订单，50:上门店换新)（老数据：10:退款单，20:补货单，30:直赔商品 ，40:退货 ，）
     */
    private Integer srcInnerType;

    /**
     * 订单类型（10000：从门店出的订单）
     */
    private Integer orderType;

    /**
     * 订单状态（20010:锁定，20020:订单取消，20030:订单取消申请，20040:超时未支付系统取消，31000:等待付款，
     * 31020:已付款，41000:待处理，32000:等待出库，33040:配送中，33060:已妥投，90000:订单完成）
     */
    private Integer orderStatus;

    /**
     * 订单状态最新更改时间
     */
    private LocalDateTime orderStatusTime;

    /**
     * 订单商家备注（500字符）
     */
    private String orderVenderRemark;

    /**
     * 下单时间
     */
    private LocalDateTime orderStartTime;

    /**
     * 订单成交时间(在线支付类型订单的付款完成时间)
     */
    private LocalDateTime orderPurchaseTime;

    /**
     * 订单时效类型
     */
    private Integer orderAgingType;

    /**
     * 预计送达开始时间（算法计算，用户下单时承诺的预计最早送达时间）
     */
    private LocalDateTime orderPreStartDeliveryTime;

    /**
     * 预计送达结束时间（算法计算，用户下单时承诺的预计最晚送达时间）
     */
    private LocalDateTime orderPreEndDeliveryTime;

    /**
     * 商家最晚拣货完成时间
     */
    private LocalDateTime pickDeadline;

    /**
     * 订单取消时间
     */
    private LocalDateTime orderCancelTime;

    /**
     * 订单取消备注（200字符）
     */
    private String orderCancelRemark;

    /**
     * 商家编码
     */
    private String orgCode;

    /**
     * 买家账号
     */
    private String buyerPin;

    /**
     * 收货人名称
     */
    private String buyerFullName;

    /**
     * 收货人地址
     */
    private String buyerFullAddress;

    /**
     * 收货人电话
     */
    private String buyerTelephone;

    /**
     * 收货人手机号
     */
    private String buyerMobile;

    /**
     * 收货人真实手机号后四位
     */
    private String lastFourDigitsOfBuyerMobile;

    /**
     * 到家配送门店编码
     */
    private String deliveryStationNo;

    /**
     * 商家门店编码
     */
    private String deliveryStationNoIsv;

    /**
     * 配送门店名称
     */
    private String deliveryStationName;

    /**
     * 承运商编号(9966:达达专送;2938:商家自送;3587:同城快递;9999:到店自提)
     */
    private String deliveryCarrierNo;

    /**
     * 承运商名称
     */
    private String deliveryCarrierName;

    /**
     * 承运单号，通常情况下和订单号一致
     */
    private String deliveryBillNo;

    /**
     * 订单支付类型(1：货到付款，4:在线支付;)
     */
    private Integer orderPayType;

    /**
     * 订单支付渠道，8001：微信支付；8002：微信免密代扣；8003：微信找人代付；9000：京东支付（无法判断具体京东支付子类型）；
     * 9002：京东银行卡支付；9004：京东收银台-京东白条；9012：京东余额支付；9014：京东白条；9022：京东小金库支付；
     * 4001： apple pay ；4002：云闪付【秒送、天选业务此字段无数据】
     */
    private Integer payChannel;

    /**
     * 订单商品销售价总金额，等于sum（京东到家销售价skuJdPrice*商品下单数量skuCount）
     */
    private Long orderTotalMoney;

    /**
     * 订单级别优惠商品金额：(不含单品促销类优惠金额及运费相关优惠金额)，等于OrderDiscountlist表中，
     * 除优惠类型7，8，12，15，16，18，外的优惠金额discountPrice累加和
     */
    private Long orderDiscountMoney;

    /**
     * 用户支付的实际订单运费：订单应收运费（orderReceivableFreight）-运费优惠（OrderDiscountlist表中，
     * 优惠类型7，8，12，15，16，18，的优惠金额。运费优惠大于应收运费时，实际支付为0
     */
    private Long orderFreightMoney;

    /**
     * 达达同城送运费(单位：分)
     */
    private Integer localDeliveryMoney;

    /**
     * 订单应收运费：用户应该支付的订单运费，即未优惠前应付运费
     */
    private Long orderReceivableFreight;

    /**
     * 用户应付金额（单位为分）=商品销售价总金额orderTotalMoney -订单优惠总金额 orderDiscountMoney+实际订单运费orderFreightMoney +包装金额packagingMoney -用户积分抵扣金额platformPointsDeductionMoney-京豆抵扣包装费（discountType=19）
     */
    private Long orderBuyerPayableMoney;

    /**
     * 包装金额
     */
    private Long packagingMoney;

    /**
     * 是否拼团订单(false:否;true:是)
     */
    private Boolean isGroupon;

    /**
     * 收货人地址腾讯坐标纬度
     */
    private Double buyerLng;

    /**
     * 收货人地址腾讯坐标经度
     */
    private Double buyerLat;

    /**
     * 收货人市ID
     */
    private String buyerCity;

    /**
     * 收货人市名称
     */
    private String buyerCityName;

    /**
     * 收货人县(区)ID
     */
    private String buyerCountry;

    /**
     * 收货人县(区)名称
     */
    private String buyerCountryName;

    /**
     * 订单买家备注（500字符）
     */
    private String orderBuyerRemark;

    /**
     * 收货人POI信息
     */
    private String buyerPoi;

    /**
     * 当天门店订单序号
     */
    private Long orderNum;

    /**
     * 订单业务类型(1：京东到家商超，2：京东到家美食，4：京东到家开放仓，5：哥伦布店内订单，
     * 6：货柜订单，8：轻松购订单，9：是自助收银，10：超级会员码，15： 券码核销订单)
     */
    private Integer businessType;

    /**
     * 当天门店订单序号
     */
    private List<OrderProductDTO> product;

    /**
     * 包含需要查询订单的优惠List列表
     */
    private List<OrderDiscountDTO> discount;

    /**
     * 业务标识，用英文分号分隔
     */
    private String businessTag;

    /*
     * 发票具体信息
     */
    private OrderInvoice orderInvoice;

    /*
     * 订单开发票标识（1.开发票；2.不开发票）
     */
    private Integer orderInvoiceOpenMark;

    /*
     * 用户积分抵扣金额
     */
    private Long platformPointsDeductionMoney;

    /**
     * 妥投时间
     */
    private LocalDateTime deliveryConfirmTime;
}
