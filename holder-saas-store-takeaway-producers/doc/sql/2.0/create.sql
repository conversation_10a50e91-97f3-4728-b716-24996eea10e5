-- My<PERSON><PERSON> Script generated by <PERSON><PERSON><PERSON> Workbench
-- 2019年03月04日 星期一 18时09分44秒
-- Model: New Model    Version: 1.0
-- MySQL Workbench Forward Engineering

SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0;
SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0;
SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='TRADITIONAL,ALLOW_INVALID_DATES';

-- -----------------------------------------------------
-- Schema hst_takeout_db
-- -----------------------------------------------------
--   外卖服务
DROP SCHEMA IF EXISTS `hst_takeaway_db` ;

-- -----------------------------------------------------
-- Schema hst_takeout_db
--
--   外卖服务
-- -----------------------------------------------------
CREATE SCHEMA IF NOT EXISTS `hst_takeaway_db` DEFAULT CHARACTER SET utf8mb4 ;
USE `hst_takeaway_db` ;

-- -----------------------------------------------------
-- Table `hst_takeout_db`.`hst_ele_auth`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `hst_takeaway_db`.`hst_ele_auth` ;

CREATE TABLE IF NOT EXISTS `hst_takeaway_db`.`hst_ele_auth` (
  `id` BIGINT(64) UNSIGNED NOT NULL AUTO_INCREMENT,
  `guid` VARCHAR(50) NOT NULL,
  `store_guid` VARCHAR(50) NOT NULL COMMENT '门店guid',
  `enterprise_guid` VARCHAR(50) NOT NULL,
  `user_id` BIGINT(64) UNSIGNED NOT NULL COMMENT '商户id',
  `user_name` VARCHAR(50) NOT NULL COMMENT '商户名',
  `shop_id` BIGINT(64) NOT NULL COMMENT '店铺ID',
  `shop_name` VARCHAR(50) NOT NULL COMMENT '店铺名称',
  `access_token` VARCHAR(50) NOT NULL COMMENT '访问token',
  `refresh_token` VARCHAR(50) NOT NULL COMMENT '刷新token',
  `token_type` VARCHAR(50) NOT NULL COMMENT 'token类型',
  `active_time` DATETIME NOT NULL COMMENT 'Token生效时间',
  `expires` BIGINT(64) UNSIGNED NOT NULL COMMENT 'Token有效时长，秒',
  `expire_time` DATETIME NOT NULL COMMENT 'Token过期时间',
  `refresh_active_time` DATETIME NOT NULL COMMENT '刷新Token生效时间',
  `refresh_expires` BIGINT(64) NOT NULL COMMENT '刷新Token有效时长，秒',
  `refresh_expire_time` DATETIME NOT NULL COMMENT '刷新Token过期时间',
  `is_deleted` TINYINT(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否已删除：0 未删除 1 已删除',
  `gmt_create` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  INDEX `idx_store_guid` (`store_guid` ASC),
  INDEX `idx_user_id` (`user_id` ASC),
  INDEX `idx_expire_time` (`expire_time` ASC),
  INDEX `idx_is_deleted` (`is_deleted` ASC),
  UNIQUE INDEX `uk_guid` (`guid` ASC))
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8mb4
COMMENT = '饿了么授权';


-- -----------------------------------------------------
-- Table `hst_takeout_db`.`hst_mt_auth`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `hst_takeaway_db`.`hst_mt_auth` ;

CREATE TABLE IF NOT EXISTS `hst_takeaway_db`.`hst_mt_auth` (
  `id` BIGINT(64) UNSIGNED NOT NULL AUTO_INCREMENT,
  `guid` VARCHAR(50) NOT NULL,
  `e_poi_id` VARCHAR(50) NOT NULL COMMENT 'Erp方门店GUID，即storeGuid',
  `enterprise_guid` VARCHAR(50) NOT NULL COMMENT 'Erp方企业GUID',
  `mt_store_guid` VARCHAR(50) NOT NULL COMMENT '美团门店GUID',
  `mt_store_name` VARCHAR(50) NOT NULL COMMENT '美团门店名字',
  `access_token` VARCHAR(200) NOT NULL COMMENT '授权Token',
  `business_id` TINYINT(5) UNSIGNED NOT NULL COMMENT '业务ID',
  `active_time` DATETIME NOT NULL COMMENT 'token激活时间',
  `expire_time` DATETIME NOT NULL COMMENT 'token过期时间',
  `is_deleted` TINYINT(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否已删除：0=未删除，1=已删除',
  `gmt_create` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  INDEX `idx_e_poi_id` (`e_poi_id` ASC),
  INDEX `idx_mt_store_guid` (`mt_store_guid` ASC),
  INDEX `idx_expire_time` (`expire_time` ASC),
  INDEX `idx_is_deleted` (`is_deleted` ASC),
  UNIQUE INDEX `uk_guid` (`guid` ASC))
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8mb4
COMMENT = '美团授权';


-- -----------------------------------------------------
-- Table `hst_takeout_db`.`hst_mt_privacy`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `hst_takeaway_db`.`hst_mt_privacy` ;

CREATE TABLE IF NOT EXISTS `hst_takeaway_db`.`hst_mt_privacy` (
  `id` BIGINT(64) UNSIGNED NOT NULL AUTO_INCREMENT,
  `guid` VARCHAR(50) NULL,
  `e_poi_id` VARCHAR(50) NULL COMMENT 'Erp方门店GUID，即storeGuid',
  `order_id` VARCHAR(50) NULL COMMENT '操作结果？',
  `order_id_view` VARCHAR(50) NULL COMMENT '订单展示号？',
  `day_seq` VARCHAR(50) NULL COMMENT '订单流水号？',
  `real_phone_number` VARCHAR(50) NULL COMMENT '真实手机号',
  `gmt_create` DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  INDEX `idx_e_poi_id` (`e_poi_id` ASC),
  UNIQUE INDEX `uk_guid` (`guid` ASC))
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8mb4
COMMENT = '美团隐私号';


SET SQL_MODE=@OLD_SQL_MODE;
SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS;
SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS;
