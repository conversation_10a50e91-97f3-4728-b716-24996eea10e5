<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.0.3.RELEASE</version>
    </parent>
    <groupId>com.holderzone</groupId>
    <artifactId>holder-saas-store-media-dto</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>holder-saas-store-media-dto</name>
    <description>holder-saas-store-media-dto</description>
    <packaging>jar</packaging>

    <properties>
        <java.version>1.8</java.version>
        <custom_maven_url>http://nexus.holderzone.cn/nexus</custom_maven_url>
    </properties>

    <dependencies>

        <!--actuator监控-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.0</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
            <version>2.8.0</version>
            <exclusions>
                <exclusion>
                    <artifactId>mapstruct</artifactId>
                    <groupId>org.mapstruct</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
            <version>2.8.0</version>
        </dependency>


    </dependencies>


    <pluginRepositories>
        <pluginRepository>
            <id>snapshots</id>
            <name>Nexus Snapshots</name>
            <url>${custom_maven_url}/content/repositories/snapshots/</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </pluginRepository>
        <pluginRepository>
            <id>thirdparty</id>
            <name>Nexus ThirdParty</name>
            <url>${custom_maven_url}/content/repositories/thirdparty/</url>
        </pluginRepository>
        <pluginRepository>
            <id>releases</id>
            <name>Nexus Releases</name>
            <url>${custom_maven_url}/content/repositories/releases/</url>
        </pluginRepository>
        <!--<pluginRepository> <id>spring-snapshots</id> <name>Spring Snapshots</name>
            <url>https://repo.spring.io/snapshot</url> <snapshots> <enabled>true</enabled>
            </snapshots> </pluginRepository> <pluginRepository> <id>spring-milestones</id>
            <name>Spring Milestones</name> <url>https://repo.spring.io/milestone</url>
            <snapshots> <enabled>false</enabled> </snapshots> </pluginRepository> -->
    </pluginRepositories>

    <distributionManagement>
        <snapshotRepository>
            <uniqueVersion>false</uniqueVersion>
            <id>snapshots</id>
            <url>http://nexus.holderzone.cn/nexus/content/repositories/snapshots/</url>
            <layout>legacy</layout>
        </snapshotRepository>
    </distributionManagement>


</project>
