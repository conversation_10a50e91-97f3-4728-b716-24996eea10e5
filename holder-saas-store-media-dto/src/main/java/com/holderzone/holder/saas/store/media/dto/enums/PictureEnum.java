package com.holderzone.holder.saas.store.media.dto.enums;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PictureEnum
 * @date 2018/09/14 13:42
 * @description
 * @program holder-saas-aggregation-merchant
 */
public enum PictureEnum {

    TYPE_1080_1920(1, 1080, 1920, 20480 * 1024L),

    TYPE_1024_600(3, 1024, 600, 20480 * 1024L),

    TYPE_635_600(4, 635, 600, 20480 * 1024L),

    DISH_PIC(2, 800, 800, 1024 * 512L),

    TYPE_1920_1080(5, 1920, 1080, 20480 * 1024L),

    TYPE_650_690(6, 650, 690, 20480 * 1024L)
    ;

    private Integer type;

    private Integer weight;

    private Integer height;

    private Long size;

    PictureEnum(Integer type, Integer weight, Integer height, Long size) {
        this.type = type;
        this.weight = weight;
        this.height = height;
        this.size = size;
    }

    public static PictureEnum getByType(Integer type) {
        return Arrays.stream(PictureEnum.values())
                .filter(pictureEnum -> Objects.equals(pictureEnum.type, type))
                .findFirst()
                .orElse(null);
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getHeight() {
        return height;
    }

    public void setHeight(Integer height) {
        this.height = height;
    }

    public Integer getWeight() {
        return weight;
    }

    public void setWeight(Integer weight) {
        this.weight = weight;
    }

    public Long getSize() {
        return size;
    }

    public void setSize(Long size) {
        this.size = size;
    }
}
