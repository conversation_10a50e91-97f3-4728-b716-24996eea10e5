package com.holderzone.holder.saas.store.media.dto.entity;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class PageMedia{

    @ApiModelProperty("每页数据大小")
    private Integer pageSize = 10;

    @ApiModelProperty("当前页数")
    private Integer currentPage = 1;

    @ApiModelProperty(value = "多媒体Guid")
    private String guid;

    @ApiModelProperty(value = "品牌GUID")
    private String brandGuid;

    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "文件路径，文件夹为空")
    private String fileUrl;

    @ApiModelProperty(value = "父目录guid，根目录下的文件或文件夹为空")
    private String parentGuid;

    @ApiModelProperty(value = "'是否文件夹")
    private Boolean isFolder;

    @ApiModelProperty(value = "'是否删除")
    private Boolean isDelete;

    @ApiModelProperty(value = "'是否品牌图库")
    private Boolean isBrand;

    @ApiModelProperty(value = "文件大小（单位：KB）")
    private Long fileSize;

    @ApiModelProperty(value = "文件类型：1-图片 2-音乐 3-视频 4-其它格式文件")
    private Integer fileType;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建人guid")
    private String createUserGuid;

    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    @ApiModelProperty(value = "修改人guid")
    private String modifyUserGuid;

    @ApiModelProperty(value = "修改人姓名")
    private String modifyUserName;

    public int getIndex() {
        return (this.currentPage < 1 ? 0 : this.currentPage - 1) * this.pageSize;
    }
}
