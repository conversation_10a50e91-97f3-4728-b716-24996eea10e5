package com.holderzone.holder.saas.store.media.dto.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(value = "FileRenameDTO", description = "文件重命名请求参数对象")
@Data
public class FileRenameDTO {

    @ApiModelProperty(value = "要重命名的文件或目录guid")
    private String fileGuid;

    @ApiModelProperty(value = "新名称")
    private String newName;

    @ApiModelProperty(value = "父目录guid，根目录下为空")
    private String parentGuid;

    @ApiModelProperty(value = "是否文件夹")
    private Boolean isFolder;

    @ApiModelProperty(value = "品牌guid，品牌图库必传")
    private String brandGuid;

    @ApiModelProperty(value = "门店guid，门店图库必传")
    private String storeGuid;
}
