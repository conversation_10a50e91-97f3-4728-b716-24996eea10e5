package com.holderzone.holder.saas.store.media.dto.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "FileMoveDTO", description = "文件移动请求参数对象")
public class FileMoveDTO {

    @ApiModelProperty(value = "需要移动的文件guid")
    private List<String> moveFileGuidList;

    @ApiModelProperty(value = "目标文件夹的guid，根目录为空")
    private String targetFolderGuid;

    @ApiModelProperty(value = "品牌guid，品牌图库必传")
    private String brandGuid;

    @ApiModelProperty(value = "门店guid，门店图库必传")
    private String storeGuid;

    @ApiModelProperty(value = "修改人guid", example = "后台获取，前端不传")
    private String modifyUserGuid;

    @ApiModelProperty(value = "修改人名字", example = "后台获取，前端不传")
    private String modifyUserName;
}
