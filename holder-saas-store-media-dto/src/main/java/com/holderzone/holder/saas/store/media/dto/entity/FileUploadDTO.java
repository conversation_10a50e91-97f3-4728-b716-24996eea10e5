package com.holderzone.holder.saas.store.media.dto.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className FileUploadDTO
 * @date 2018/09/13 10:47
 * @description
 * @program holder-saas-aggregation-merchant
 */
@Data
@ApiModel
public class FileUploadDTO implements Serializable {

    @ApiModelProperty(value = "像素高度要求")
    private Integer height;

    @ApiModelProperty(value = "像素宽度要求")
    private Integer weight;

    @ApiModelProperty(value = "文件大小要求。超过自动压缩")
    private Long size;

}
