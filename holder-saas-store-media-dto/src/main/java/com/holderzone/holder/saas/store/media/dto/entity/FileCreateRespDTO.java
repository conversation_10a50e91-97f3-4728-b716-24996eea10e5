package com.holderzone.holder.saas.store.media.dto.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "FileCreateRespDTO", description = "创建文件返回对象")
public class FileCreateRespDTO {

    @ApiModelProperty(value = "新文件guid")
    private String guid;

    @ApiModelProperty(value = "新文件名称")
    private String name;

    @ApiModelProperty(value = "url")
    private String url;


}
